set ::msgcat::header "Project-Id-Version: git-gui\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2016-05-06 13:09+0000\nLast-Translator: Vasco Almeida <<EMAIL>>\nLanguage-Team: Portuguese\nLanguage: pt\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Virtaal 0.7.1\n"
::msgcat::mcset pt_pt "Invalid font specified in %s:" "Tipo de letra inv\u00e1lido especificado em %s:"
::msgcat::mcset pt_pt "Main Font" "Tipo de letra principal"
::msgcat::mcset pt_pt "Diff/Console Font" "Tipo de letra Diferen\u00e7as/Consola"
::msgcat::mcset pt_pt "git-gui: fatal error" "git-gui: erro fatal"
::msgcat::mcset pt_pt "Cannot find git in PATH." "N\u00e3o \u00e9 poss\u00edvel encontrar o git em PATH."
::msgcat::mcset pt_pt "Cannot parse Git version string:" "N\u00e3o \u00e9 poss\u00edvel analisar a vers\u00e3o do Git:"
::msgcat::mcset pt_pt "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "A vers\u00e3o do Git n\u00e3o p\u00f4de ser determinada.\n\n%s alega que est\u00e1 na vers\u00e3o '%s'.\n\n%s requer pelo menos Git 1.5.0 ou mais recente.\n\nAssumir que '%s' est\u00e1 na vers\u00e3o 1.5.0?\n"
::msgcat::mcset pt_pt "Git directory not found:" "Diret\u00f3rio Git n\u00e3o encontrado:"
::msgcat::mcset pt_pt "Cannot move to top of working directory:" "N\u00e3o \u00e9 poss\u00edvel mover para o topo do diret\u00f3rio de trabalho:"
::msgcat::mcset pt_pt "Cannot use bare repository:" "N\u00e3o \u00e9 poss\u00edvel usar reposit\u00f3rio nu:"
::msgcat::mcset pt_pt "No working directory" "Nenhum diret\u00f3rio de trabalho"
::msgcat::mcset pt_pt "Refreshing file status..." "A atualizar estado do ficheiro..."
::msgcat::mcset pt_pt "Scanning for modified files ..." "A procurar por ficheiros modificados..."
::msgcat::mcset pt_pt "Calling prepare-commit-msg hook..." "A invocar gancho preparar-mensagem-de-commit (prepare-commit-msg hook)..."
::msgcat::mcset pt_pt "Commit declined by prepare-commit-msg hook." "Commit recusado pelo gancho preparar-mensagem-de-commit (prepare-commit-msg hook)."
::msgcat::mcset pt_pt "Ready." "Pronto."
::msgcat::mcset pt_pt "Display limit (gui.maxfilesdisplayed = %s) reached, not showing all %s files." "Limite de visualiza\u00e7\u00e3o (gui.maxfilesdisplayed = %s) atingido, n\u00e3o s\u00e3o mostrados todos os %s ficheiros."
::msgcat::mcset pt_pt "Unmodified" "N\u00e3o modificado"
::msgcat::mcset pt_pt "Modified, not staged" "Modificado, n\u00e3o preparado"
::msgcat::mcset pt_pt "Staged for commit" "Preparado para commit"
::msgcat::mcset pt_pt "Portions staged for commit" "Por\u00e7\u00f5es preparadas para commit"
::msgcat::mcset pt_pt "Staged for commit, missing" "Preparado para commit, em falta"
::msgcat::mcset pt_pt "File type changed, not staged" "Tipo de ficheiro modificado, n\u00e3o preparado"
::msgcat::mcset pt_pt "File type changed, old type staged for commit" "Tipo de ficheiro modificado, tipo antigo preparado para commit"
::msgcat::mcset pt_pt "File type changed, staged" "Tipo de ficheiro modificado, preparado"
::msgcat::mcset pt_pt "File type change staged, modification not staged" "Tipo de ficheiro modificado, modifica\u00e7\u00e3o n\u00e3o preparada"
::msgcat::mcset pt_pt "File type change staged, file missing" "Tipo de ficheiro modificado, ficheiro em falta"
::msgcat::mcset pt_pt "Untracked, not staged" "N\u00e3o controlado, n\u00e3o preparado"
::msgcat::mcset pt_pt "Missing" "Em falta"
::msgcat::mcset pt_pt "Staged for removal" "Preparado para remo\u00e7\u00e3o"
::msgcat::mcset pt_pt "Staged for removal, still present" "Preparado para remo\u00e7\u00e3o, ainda presente"
::msgcat::mcset pt_pt "Requires merge resolution" "Requer resolu\u00e7\u00e3o de integra\u00e7\u00e3o"
::msgcat::mcset pt_pt "Starting gitk... please wait..." "A iniciar gitk... aguarde..."
::msgcat::mcset pt_pt "Couldn't find gitk in PATH" "N\u00e3o foi poss\u00edvel encontrar gitk em PATH"
::msgcat::mcset pt_pt "Couldn't find git gui in PATH" "N\u00e3o foi poss\u00edvel encontrar git gui em PATH"
::msgcat::mcset pt_pt "Repository" "Reposit\u00f3rio"
::msgcat::mcset pt_pt "Edit" "Editar"
::msgcat::mcset pt_pt "Branch" "Ramo"
::msgcat::mcset pt_pt "Commit@@noun" "Commit"
::msgcat::mcset pt_pt "Merge" "Integrar"
::msgcat::mcset pt_pt "Remote" "Remoto"
::msgcat::mcset pt_pt "Tools" "Ferramentas"
::msgcat::mcset pt_pt "Explore Working Copy" "Explorar c\u00f3pia de trabalho"
::msgcat::mcset pt_pt "Git Bash" "Git Bash"
::msgcat::mcset pt_pt "Browse Current Branch's Files" "Navegar pelos ficheiro do ramo atual"
::msgcat::mcset pt_pt "Browse Branch Files..." "Navegar pelos ficheiros do ramo..."
::msgcat::mcset pt_pt "Visualize Current Branch's History" "Visualizar hist\u00f3rico do ramo atual"
::msgcat::mcset pt_pt "Visualize All Branch History" "Visualizar hist\u00f3rico de todos os ramos"
::msgcat::mcset pt_pt "Browse %s's Files" "Navegar pelos ficheiro de %s"
::msgcat::mcset pt_pt "Visualize %s's History" "Visualizar hist\u00f3rico de %s"
::msgcat::mcset pt_pt "Database Statistics" "Estat\u00edsticas da base de dados"
::msgcat::mcset pt_pt "Compress Database" "Comprimir base de dados"
::msgcat::mcset pt_pt "Verify Database" "Verificar base de dados"
::msgcat::mcset pt_pt "Create Desktop Icon" "Criar \u00edcone no ambiente de trabalho"
::msgcat::mcset pt_pt "Quit" "Sair"
::msgcat::mcset pt_pt "Undo" "Desfazer"
::msgcat::mcset pt_pt "Redo" "Refazer"
::msgcat::mcset pt_pt "Cut" "Cortar"
::msgcat::mcset pt_pt "Copy" "Copiar"
::msgcat::mcset pt_pt "Paste" "Colar"
::msgcat::mcset pt_pt "Delete" "Eliminar"
::msgcat::mcset pt_pt "Select All" "Selecionar tudo"
::msgcat::mcset pt_pt "Create..." "Criar..."
::msgcat::mcset pt_pt "Checkout..." "Extrair..."
::msgcat::mcset pt_pt "Rename..." "Mudar nome..."
::msgcat::mcset pt_pt "Delete..." "Eliminar..."
::msgcat::mcset pt_pt "Reset..." "Repor..."
::msgcat::mcset pt_pt "Done" "Conclu\u00eddo"
::msgcat::mcset pt_pt "Commit@@verb" "Submeter"
::msgcat::mcset pt_pt "New Commit" "Novo commit"
::msgcat::mcset pt_pt "Amend Last Commit" "Emendar \u00faltimo commit"
::msgcat::mcset pt_pt "Rescan" "Reanalisar"
::msgcat::mcset pt_pt "Stage To Commit" "Preparar para commit"
::msgcat::mcset pt_pt "Stage Changed Files To Commit" "Preparar ficheiros modificados para commit"
::msgcat::mcset pt_pt "Unstage From Commit" "Retirar do commit"
::msgcat::mcset pt_pt "Revert Changes" "Reverter altera\u00e7\u00f5es"
::msgcat::mcset pt_pt "Show Less Context" "Mostrar menos contexto"
::msgcat::mcset pt_pt "Show More Context" "Mostrar mais contexto"
::msgcat::mcset pt_pt "Sign Off" "Assinar por baixo"
::msgcat::mcset pt_pt "Local Merge..." "Integra\u00e7\u00e3o local..."
::msgcat::mcset pt_pt "Abort Merge..." "Abortar integra\u00e7\u00e3o..."
::msgcat::mcset pt_pt "Add..." "Adicionar..."
::msgcat::mcset pt_pt "Push..." "Publicar..."
::msgcat::mcset pt_pt "Delete Branch..." "Eliminar ramo..."
::msgcat::mcset pt_pt "Options..." "Op\u00e7\u00f5es..."
::msgcat::mcset pt_pt "Remove..." "Remover..."
::msgcat::mcset pt_pt "Help" "Ajuda"
::msgcat::mcset pt_pt "About %s" "Sobre %s"
::msgcat::mcset pt_pt "Online Documentation" "Documenta\u00e7\u00e3o online"
::msgcat::mcset pt_pt "Show SSH Key" "Mostrar chave SSH"
::msgcat::mcset pt_pt "Usage" "Utiliza\u00e7\u00e3o"
::msgcat::mcset pt_pt "Error" "Erro"
::msgcat::mcset pt_pt "fatal: cannot stat path %s: No such file or directory" "fatal: n\u00e3o \u00e9 poss\u00edvel obter estado do caminho %s: Ficheiro ou diret\u00f3rio inexistente"
::msgcat::mcset pt_pt "Current Branch:" "Ramo atual:"
::msgcat::mcset pt_pt "Staged Changes (Will Commit)" "Altera\u00e7\u00f5es preparadas (para commit)"
::msgcat::mcset pt_pt "Unstaged Changes" "Altera\u00e7\u00f5es n\u00e3o preparadas"
::msgcat::mcset pt_pt "Stage Changed" "Preparar modificados"
::msgcat::mcset pt_pt "Push" "Publicar"
::msgcat::mcset pt_pt "Initial Commit Message:" "Mensagem de commit inicial:"
::msgcat::mcset pt_pt "Amended Commit Message:" "Mensagem de commit emendada:"
::msgcat::mcset pt_pt "Amended Initial Commit Message:" "Mensagem de commit inicial emendada:"
::msgcat::mcset pt_pt "Amended Merge Commit Message:" "Mensagem de commit de integra\u00e7\u00e3o emendada:"
::msgcat::mcset pt_pt "Merge Commit Message:" "Mensagem de commit de integra\u00e7\u00e3o:"
::msgcat::mcset pt_pt "Commit Message:" "Mensagem de commit:"
::msgcat::mcset pt_pt "Copy All" "Copiar tudo"
::msgcat::mcset pt_pt "File:" "Ficheiro:"
::msgcat::mcset pt_pt "Refresh" "Atualizar"
::msgcat::mcset pt_pt "Decrease Font Size" "Diminuir tamanho de letra"
::msgcat::mcset pt_pt "Increase Font Size" "Aumentar tamanho de letra"
::msgcat::mcset pt_pt "Encoding" "Codifica\u00e7\u00e3o"
::msgcat::mcset pt_pt "Apply/Reverse Hunk" "Aplicar/Reverter excerto"
::msgcat::mcset pt_pt "Apply/Reverse Line" "Aplicar/Reverter linha"
::msgcat::mcset pt_pt "Run Merge Tool" "Executar ferramenta de integra\u00e7\u00e3o"
::msgcat::mcset pt_pt "Use Remote Version" "Usar a vers\u00e3o remota"
::msgcat::mcset pt_pt "Use Local Version" "Usar a vers\u00e3o local"
::msgcat::mcset pt_pt "Revert To Base" "Reverter para a base"
::msgcat::mcset pt_pt "Visualize These Changes In The Submodule" "Visualizar estas altera\u00e7\u00f5es no subm\u00f3dulo"
::msgcat::mcset pt_pt "Visualize Current Branch History In The Submodule" "Visualizar hist\u00f3rico do ramo atual no subm\u00f3dulo"
::msgcat::mcset pt_pt "Visualize All Branch History In The Submodule" "Visualizar hist\u00f3rico de todos os ramos no subm\u00f3dulo"
::msgcat::mcset pt_pt "Start git gui In The Submodule" "Iniciar git gui no subm\u00f3dulo"
::msgcat::mcset pt_pt "Unstage Hunk From Commit" "Retirar excerto do commit"
::msgcat::mcset pt_pt "Unstage Lines From Commit" "Retirar linhas do commit"
::msgcat::mcset pt_pt "Unstage Line From Commit" "Retirar linha do commit"
::msgcat::mcset pt_pt "Stage Hunk For Commit" "Preparar excerto para commit"
::msgcat::mcset pt_pt "Stage Lines For Commit" "Preparar linhas para commit"
::msgcat::mcset pt_pt "Stage Line For Commit" "Preparar linha para commit"
::msgcat::mcset pt_pt "Initializing..." "A inicializar..."
::msgcat::mcset pt_pt "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "Existem poss\u00edveis erros de ambiente.\n\nAs seguintes vari\u00e1veis de ambiente ser\u00e3o provavelmente\nignoradas pelos subprocessos do Git executados\npor %s:\n\n"
::msgcat::mcset pt_pt "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\nDevido a um problema conhecido com o\nbin\u00e1rio Tcl distribu\u00eddo pelo Cygwin."
::msgcat::mcset pt_pt "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\nUm bom substituto para %s\n\u00e9 colocar valores das defini\u00e7\u00f5es user.name e\nuser.email no ficheiro pessoal ~/.gitconfig.\n"
::msgcat::mcset pt_pt "Goto Line:" "Ir para a linha:"
::msgcat::mcset pt_pt "Go" "Ir"
::msgcat::mcset pt_pt "Working... please wait..." "A processar... aguarde..."
::msgcat::mcset pt_pt "Close" "Fechar"
::msgcat::mcset pt_pt "Success" "Sucesso"
::msgcat::mcset pt_pt "Error: Command Failed" "Erro: falha ao executar comando"
::msgcat::mcset pt_pt "Fetching %s from %s" "A obter %s de %s"
::msgcat::mcset pt_pt "fatal: Cannot resolve %s" "fatal: N\u00e3o \u00e9 poss\u00edvel resolver %s"
::msgcat::mcset pt_pt "Branch '%s' does not exist." "O ramo '%s' n\u00e3o existe."
::msgcat::mcset pt_pt "Failed to configure simplified git-pull for '%s'." "Falha ao configurar git-pull simplificado de '%s'."
::msgcat::mcset pt_pt "Branch '%s' already exists." "O ramo '%s' j\u00e1 existe."
::msgcat::mcset pt_pt "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "O ramo '%s' j\u00e1 existe.\n\nN\u00e3o pode ser avan\u00e7ado rapidamente para %s.\nIntegra\u00e7\u00e3o necess\u00e1ria."
::msgcat::mcset pt_pt "Merge strategy '%s' not supported." "A estrat\u00e9gia de integra\u00e7\u00e3o '%s' n\u00e3o \u00e9 suportada."
::msgcat::mcset pt_pt "Failed to update '%s'." "Falha ao atualizar '%s'."
::msgcat::mcset pt_pt "Staging area (index) is already locked." "A \u00e1rea de est\u00e1gio (\u00edndice) j\u00e1 est\u00e1 bloqueada."
::msgcat::mcset pt_pt "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "O \u00faltimo estado analisado n\u00e3o corresponde ao estado do reposit\u00f3rio.\n\nOutro programa Git modificou este reposit\u00f3rio deste a \u00faltima an\u00e1lise. Deve-se reanalisar antes do ramo atual poder ser alterado.\n\nIr\u00e1-se reanalisar automaticamente agora.\n"
::msgcat::mcset pt_pt "Updating working directory to '%s'..." "A atualizar o diret\u00f3rio de trabalho para '%s'..."
::msgcat::mcset pt_pt "files checked out" "ficheiros extra\u00eddos"
::msgcat::mcset pt_pt "Aborted checkout of '%s' (file level merging is required)." "Extra\u00e7\u00e3o de '%s' abortada (\u00e9 necess\u00e1rio integrar ao n\u00edvel de ficheiros)."
::msgcat::mcset pt_pt "File level merge required." "Integra\u00e7\u00e3o ao n\u00edvel de ficheiros necess\u00e1ria."
::msgcat::mcset pt_pt "Staying on branch '%s'." "Permanecer no ramo '%s'."
::msgcat::mcset pt_pt "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "J\u00e1 n\u00e3o se encontra num ramo local.\n\nSe queria estar sobre um ramo, crie um a partir de 'Esta extra\u00e7\u00e3o destacada'."
::msgcat::mcset pt_pt "Checked out '%s'." "'%s' extra\u00eddo."
::msgcat::mcset pt_pt "Resetting '%s' to '%s' will lose the following commits:" "Ao repor '%s' para '%s' perder\u00e1 os seguintes commits:"
::msgcat::mcset pt_pt "Recovering lost commits may not be easy." "Recuperar commits perdidos pode n\u00e3o ser f\u00e1cil."
::msgcat::mcset pt_pt "Reset '%s'?" "Repor '%s'?"
::msgcat::mcset pt_pt "Visualize" "Visualizar"
::msgcat::mcset pt_pt "Reset" "Repor"
::msgcat::mcset pt_pt "Cancel" "Cancelar"
::msgcat::mcset pt_pt "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "Falha ao definir ramo atual.\n\nApenas se mudou o diret\u00f3rio de trabalho parcialmente. Os ficheiros foram atualizados com sucesso, mas n\u00e3o foi poss\u00edvel atualizar o ficheiro Git interno.\n\nN\u00e3o devia ter ocorrido. %s ir\u00e1 terminar e desistir."
::msgcat::mcset pt_pt "fetch %s" "obter %s"
::msgcat::mcset pt_pt "Fetching new changes from %s" "Obter novas altera\u00e7\u00f5es de %s"
::msgcat::mcset pt_pt "remote prune %s" "poda remota de %s"
::msgcat::mcset pt_pt "Pruning tracking branches deleted from %s" "A podar ramos de monitoriza\u00e7\u00e3o eliminados de %s"
::msgcat::mcset pt_pt "fetch all remotes" "obter de todos os remotos"
::msgcat::mcset pt_pt "Fetching new changes from all remotes" "A obter novas altera\u00e7\u00f5es de todos os remotos"
::msgcat::mcset pt_pt "remote prune all remotes" "poda remota de todos os remotos"
::msgcat::mcset pt_pt "Pruning tracking branches deleted from all remotes" "A podar ramos de monitoriza\u00e7\u00e3o eliminados de todos os remotos"
::msgcat::mcset pt_pt "push %s" "publicar %s"
::msgcat::mcset pt_pt "Pushing changes to %s" "A publicar altera\u00e7\u00f5es em %s"
::msgcat::mcset pt_pt "Mirroring to %s" "A espelhar em %s"
::msgcat::mcset pt_pt "Pushing %s %s to %s" "A publicar %s %s em %s"
::msgcat::mcset pt_pt "Push Branches" "Publicar ramos"
::msgcat::mcset pt_pt "Source Branches" "Ramos de origem"
::msgcat::mcset pt_pt "Destination Repository" "Reposit\u00f3rio de destino"
::msgcat::mcset pt_pt "Remote:" "Remoto:"
::msgcat::mcset pt_pt "Arbitrary Location:" "Localiza\u00e7\u00e3o arbitr\u00e1ria:"
::msgcat::mcset pt_pt "Transfer Options" "Op\u00e7\u00f5es de transfer\u00eancia"
::msgcat::mcset pt_pt "Force overwrite existing branch (may discard changes)" "For\u00e7ar substitui\u00e7\u00e3o de ramos existente (pode descartar altera\u00e7\u00f5es)"
::msgcat::mcset pt_pt "Use thin pack (for slow network connections)" "Usar pacote fino (para conex\u00f5es de rede lentas)"
::msgcat::mcset pt_pt "Include tags" "Incluir tags"
::msgcat::mcset pt_pt "Add Remote" "Adicionar remoto"
::msgcat::mcset pt_pt "Add New Remote" "Adicionar novo remoto"
::msgcat::mcset pt_pt "Add" "Adicionar"
::msgcat::mcset pt_pt "Remote Details" "Detalhes do remoto"
::msgcat::mcset pt_pt "Name:" "Nome:"
::msgcat::mcset pt_pt "Location:" "Localiza\u00e7\u00e3o:"
::msgcat::mcset pt_pt "Further Action" "A\u00e7\u00e3o adicional"
::msgcat::mcset pt_pt "Fetch Immediately" "Obter imediatamente"
::msgcat::mcset pt_pt "Initialize Remote Repository and Push" "Inicializar reposit\u00f3rio remoto e publicar"
::msgcat::mcset pt_pt "Do Nothing Else Now" "N\u00e3o fazer mais nada agora"
::msgcat::mcset pt_pt "Please supply a remote name." "Forne\u00e7a um nome para o remoto."
::msgcat::mcset pt_pt "'%s' is not an acceptable remote name." "'%s' n\u00e3o pode ser aceite como nome de remoto."
::msgcat::mcset pt_pt "Failed to add remote '%s' of location '%s'." "Falha ao adicionar remoto '%s' localizado em '%s'."
::msgcat::mcset pt_pt "Fetching the %s" "A obter de %s"
::msgcat::mcset pt_pt "Do not know how to initialize repository at location '%s'." "N\u00e3o se sabe como inicializar o reposit\u00f3rio localizado em '%s'."
::msgcat::mcset pt_pt "Setting up the %s (at %s)" "A configurar %s (em %s)"
::msgcat::mcset pt_pt "Starting..." "A iniciar..."
::msgcat::mcset pt_pt "File Browser" "Navegador de ficheiros"
::msgcat::mcset pt_pt "Loading %s..." "A carregar %s..."
::msgcat::mcset pt_pt "\[Up To Parent\]" "\[Subir\]"
::msgcat::mcset pt_pt "Browse Branch Files" "Navegar pelos ficheiros do ramo"
::msgcat::mcset pt_pt "Browse" "Navegar"
::msgcat::mcset pt_pt "Revision" "Revis\u00e3o"
::msgcat::mcset pt_pt "Running %s requires a selected file." "Deve selecionar um ficheiro para executar %s."
::msgcat::mcset pt_pt "Are you sure you want to run %1\$s on file \"%2\$s\"?" "Tem a certeza que pretende executar %1\$s sobre o ficheiro \"%2\$s\"?"
::msgcat::mcset pt_pt "Are you sure you want to run %s?" "Tem a certeza que pretende executar %s?"
::msgcat::mcset pt_pt "Tool: %s" "Ferramenta: %s"
::msgcat::mcset pt_pt "Running: %s" "A executar: %s"
::msgcat::mcset pt_pt "Tool completed successfully: %s" "A ferramenta conclu\u00ed com sucesso: %s"
::msgcat::mcset pt_pt "Tool failed: %s" "A ferramenta falhou: %s"
::msgcat::mcset pt_pt "Checkout Branch" "Extrair ramo"
::msgcat::mcset pt_pt "Checkout" "Extrair"
::msgcat::mcset pt_pt "Options" "Op\u00e7\u00f5es"
::msgcat::mcset pt_pt "Fetch Tracking Branch" "Obter ramo de monitoriza\u00e7\u00e3o"
::msgcat::mcset pt_pt "Detach From Local Branch" "Destacar do ramo local"
::msgcat::mcset pt_pt "Unsupported spell checker" "Corretor ortogr\u00e1fico n\u00e3o suportado"
::msgcat::mcset pt_pt "Spell checking is unavailable" "Corre\u00e7\u00e3o ortogr\u00e1fica indispon\u00edvel"
::msgcat::mcset pt_pt "Invalid spell checking configuration" "Configura\u00e7\u00e3o inv\u00e1lida do corretor ortogr\u00e1fico"
::msgcat::mcset pt_pt "Reverting dictionary to %s." "A reverter dicion\u00e1rio para %s."
::msgcat::mcset pt_pt "Spell checker silently failed on startup" "O corretor ortogr\u00e1fico falhou silenciosamente ao iniciar"
::msgcat::mcset pt_pt "Unrecognized spell checker" "Corretor ortogr\u00e1fico n\u00e3o reconhecido"
::msgcat::mcset pt_pt "No Suggestions" "Sem sugest\u00f5es"
::msgcat::mcset pt_pt "Unexpected EOF from spell checker" "EOF (fim de ficheiro) inesperado do corretor ortogr\u00e1fico"
::msgcat::mcset pt_pt "Spell Checker Failed" "Corretor ortogr\u00e1fico falhou"
::msgcat::mcset pt_pt "%s ... %*i of %*i %s (%3i%%)" "%s ... %*i de %*i %s (%3i%%)"
::msgcat::mcset pt_pt "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "Nenhum diferen\u00e7a detetada.\n\n%s n\u00e3o tem altera\u00e7\u00f5es.\n\nA data de modifica\u00e7\u00e3o deste ficheiro foi atualizada por outra aplica\u00e7\u00e3o, mas o conte\u00fado no interior do ficheiro n\u00e3o foi alterado.\n\nIr\u00e1-se reanalisar automaticamente para encontrar outros ficheiros que estejam no mesmo estado."
::msgcat::mcset pt_pt "Loading diff of %s..." "A carregar diferen\u00e7as de %s..."
::msgcat::mcset pt_pt "LOCAL: deleted\nREMOTE:\n" "LOCAL: eliminado\nREMOTO:\n"
::msgcat::mcset pt_pt "REMOTE: deleted\nLOCAL:\n" "REMOTO: eliminado\nLOCAL:\n"
::msgcat::mcset pt_pt "LOCAL:\n" "LOCAL:\n"
::msgcat::mcset pt_pt "REMOTE:\n" "REMOTO:\n"
::msgcat::mcset pt_pt "Unable to display %s" "N\u00e3o \u00e9 poss\u00edvel mostrar %s"
::msgcat::mcset pt_pt "Error loading file:" "Erro ao carregar ficheiro:"
::msgcat::mcset pt_pt "Git Repository (subproject)" "Reposit\u00f3rio Git (subprojeto)"
::msgcat::mcset pt_pt "* Binary file (not showing content)." "* Ficheiro bin\u00e1rio (conte\u00fado n\u00e3o exibido)."
::msgcat::mcset pt_pt "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* O ficheiro n\u00e3o controlado tem %d bytes.\n* Exibido apenas os primeiros %d bytes.\n"
::msgcat::mcset pt_pt "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* Ficheiro n\u00e3o controlado recortado aqui por %s.\n* Para ver o ficheiro inteiro, use um editor externo.\n"
::msgcat::mcset pt_pt "Error loading diff:" "Erro ao carregar diferen\u00e7as:"
::msgcat::mcset pt_pt "Failed to unstage selected hunk." "Falha ao retirar excerto selecionado do \u00edndice."
::msgcat::mcset pt_pt "Failed to stage selected hunk." "Falha ao preparar excerto selecionado."
::msgcat::mcset pt_pt "Failed to unstage selected line." "Falha ao retirar linha selecionada do \u00edndice."
::msgcat::mcset pt_pt "Failed to stage selected line." "Falha ao preparar linha selecionada."
::msgcat::mcset pt_pt "Push to" "Publicar em"
::msgcat::mcset pt_pt "Remove Remote" "Remover remoto"
::msgcat::mcset pt_pt "Prune from" "Podar de"
::msgcat::mcset pt_pt "Fetch from" "Obter de"
::msgcat::mcset pt_pt "Select" "Selecionar"
::msgcat::mcset pt_pt "Font Family" "Fam\u00edlia de tipo de letra"
::msgcat::mcset pt_pt "Font Size" "Tamanho de letra"
::msgcat::mcset pt_pt "Font Example" "Exemplo do tipo de letra"
::msgcat::mcset pt_pt "This is example text.\nIf you like this text, it can be your font." "Este texto \u00e9 um exemplo.\nSe gostar deste texto, pode defini-lo como tipo de letra."
::msgcat::mcset pt_pt "Invalid global encoding '%s'" "Codifica\u00e7\u00e3o global '%s' inv\u00e1lida"
::msgcat::mcset pt_pt "Invalid repo encoding '%s'" "Codifica\u00e7\u00e3o do reposit\u00f3rio '%s' inv\u00e1lida"
::msgcat::mcset pt_pt "Restore Defaults" "Restaurar predefini\u00e7\u00f5es"
::msgcat::mcset pt_pt "Save" "Guardar"
::msgcat::mcset pt_pt "%s Repository" "Reposit\u00f3rio %s"
::msgcat::mcset pt_pt "Global (All Repositories)" "Global (todos os reposit\u00f3rios)"
::msgcat::mcset pt_pt "User Name" "Nome de utilizador"
::msgcat::mcset pt_pt "Email Address" "Endere\u00e7o de e-mail"
::msgcat::mcset pt_pt "Summarize Merge Commits" "Resumir commits de integra\u00e7\u00e3o"
::msgcat::mcset pt_pt "Merge Verbosity" "Verbosidade de integra\u00e7\u00e3o"
::msgcat::mcset pt_pt "Show Diffstat After Merge" "Mostrar estat\u00edsticas de diferen\u00e7as depois de integrar"
::msgcat::mcset pt_pt "Use Merge Tool" "Usar ferramenta de integra\u00e7\u00e3o"
::msgcat::mcset pt_pt "Trust File Modification Timestamps" "Confiar na data de modifica\u00e7\u00e3o dos ficheiros"
::msgcat::mcset pt_pt "Prune Tracking Branches During Fetch" "Podar ramos de monitoriza\u00e7\u00e3o ao obter"
::msgcat::mcset pt_pt "Match Tracking Branches" "Corresponder ramos de monitoriza\u00e7\u00e3o"
::msgcat::mcset pt_pt "Use Textconv For Diffs and Blames" "Usar textconv para mostrar diferen\u00e7as e culpar"
::msgcat::mcset pt_pt "Blame Copy Only On Changed Files" "Detetar c\u00f3pia apenas em ficheiros modificados"
::msgcat::mcset pt_pt "Maximum Length of Recent Repositories List" "Comprimento m\u00e1ximo da lista de reposit\u00f3rios recentes"
::msgcat::mcset pt_pt "Minimum Letters To Blame Copy On" "N\u00famero m\u00ednimo de letras para detetar c\u00f3pia"
::msgcat::mcset pt_pt "Blame History Context Radius (days)" "Raio de contexto hist\u00f3rico para culpar (dias)"
::msgcat::mcset pt_pt "Number of Diff Context Lines" "N\u00famero de linhas de contexto ao mostrar diferen\u00e7as"
::msgcat::mcset pt_pt "Additional Diff Parameters" "Par\u00e2metros de diff adicionais"
::msgcat::mcset pt_pt "Commit Message Text Width" "Largura do texto da mensagem de commit"
::msgcat::mcset pt_pt "New Branch Name Template" "Modelo para nome de novo ramo"
::msgcat::mcset pt_pt "Default File Contents Encoding" "Codifica\u00e7\u00e3o predefinida dos conte\u00fados de ficheiros"
::msgcat::mcset pt_pt "Warn before committing to a detached head" "Avisar antes de submeter numa cabe\u00e7a destacada"
::msgcat::mcset pt_pt "Staging of untracked files" "Preparar ficheiros n\u00e3o controlados"
::msgcat::mcset pt_pt "Show untracked files" "Mostrar ficheiros n\u00e3o controlados"
::msgcat::mcset pt_pt "Tab spacing" "Espa\u00e7amento da tabula\u00e7\u00e3o"
::msgcat::mcset pt_pt "Change" "Alterar"
::msgcat::mcset pt_pt "Spelling Dictionary:" "Dicion\u00e1rio ortogr\u00e1fico:"
::msgcat::mcset pt_pt "Change Font" "Alterar tipo de letra"
::msgcat::mcset pt_pt "Choose %s" "Escolher %s"
::msgcat::mcset pt_pt "pt." "pt."
::msgcat::mcset pt_pt "Preferences" "Prefer\u00eancias"
::msgcat::mcset pt_pt "Failed to completely save options:" "Falha ao guardar todas as op\u00e7\u00f5es:"
::msgcat::mcset pt_pt "Force resolution to the base version?" "For\u00e7ar resolu\u00e7\u00e3o para a vers\u00e3o base?"
::msgcat::mcset pt_pt "Force resolution to this branch?" "For\u00e7ar resolu\u00e7\u00e3o para este ramo?"
::msgcat::mcset pt_pt "Force resolution to the other branch?" "For\u00e7ar resolu\u00e7\u00e3o para o outro ramo?"
::msgcat::mcset pt_pt "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "Note que as diferen\u00e7as mostram apenas altera\u00e7\u00f5es em conflito.\n\n%s ser\u00e1 substitu\u00eddo.\n\nEsta opera\u00e7\u00e3o s\u00f3 pode ser anulada reiniciando a integra\u00e7\u00e3o."
::msgcat::mcset pt_pt "File %s seems to have unresolved conflicts, still stage?" "O ficheiro %s parece ter conflitos n\u00e3o resolvidos, prepar\u00e1-lo mesmo assim?"
::msgcat::mcset pt_pt "Adding resolution for %s" "A adicionar resolu\u00e7\u00e3o de %s"
::msgcat::mcset pt_pt "Cannot resolve deletion or link conflicts using a tool" "N\u00e3o \u00e9 poss\u00edvel resolver conflitos de exclus\u00e3o ou liga\u00e7\u00e3o usando uma ferramenta"
::msgcat::mcset pt_pt "Conflict file does not exist" "O ficheiro em conflito n\u00e3o existe"
::msgcat::mcset pt_pt "Not a GUI merge tool: '%s'" "N\u00e3o \u00e9 uma ferramenta GUI de integra\u00e7\u00e3o: '%s'"
::msgcat::mcset pt_pt "Unsupported merge tool '%s'" "Ferramenta de integra\u00e7\u00e3o '%s' n\u00e3o suportada"
::msgcat::mcset pt_pt "Merge tool is already running, terminate it?" "A ferramenta de integra\u00e7\u00e3o j\u00e1 est\u00e1 a executar, termin\u00e1-la?"
::msgcat::mcset pt_pt "Error retrieving versions:\n%s" "Erro ao obter vers\u00f5es:\n%s"
::msgcat::mcset pt_pt "Could not start the merge tool:\n\n%s" "N\u00e3o foi poss\u00edvel iniciar a ferramenta de integra\u00e7\u00e3o:\n\n%s"
::msgcat::mcset pt_pt "Running merge tool..." "A executar a ferramenta de integra\u00e7\u00e3o..."
::msgcat::mcset pt_pt "Merge tool failed." "A ferramenta de integra\u00e7\u00e3o falhou."
::msgcat::mcset pt_pt "Add Tool" "Adicionar ferramenta"
::msgcat::mcset pt_pt "Add New Tool Command" "Adicionar novo comando de ferramenta"
::msgcat::mcset pt_pt "Add globally" "Adicionar globalmente"
::msgcat::mcset pt_pt "Tool Details" "Detalhes da ferramenta"
::msgcat::mcset pt_pt "Use '/' separators to create a submenu tree:" "Use separadores '/' para criar uma \u00e1rvore de submenus:"
::msgcat::mcset pt_pt "Command:" "Comando:"
::msgcat::mcset pt_pt "Show a dialog before running" "Mostrar um di\u00e1logo antes de executar"
::msgcat::mcset pt_pt "Ask the user to select a revision (sets \$REVISION)" "Pedir ao utilizador para selecionar uma revis\u00e3o (define \$REVISION)"
::msgcat::mcset pt_pt "Ask the user for additional arguments (sets \$ARGS)" "Pedir ao utilizador argumentos adicionais (define \$ARGS)"
::msgcat::mcset pt_pt "Don't show the command output window" "N\u00e3o mostrar a janela com a sa\u00edda do comando"
::msgcat::mcset pt_pt "Run only if a diff is selected (\$FILENAME not empty)" "Executar s\u00f3 se for selecionada um diferen\u00e7a (\$FILENAME n\u00e3o vazio)"
::msgcat::mcset pt_pt "Please supply a name for the tool." "Forne\u00e7a um nome para a ferramenta."
::msgcat::mcset pt_pt "Tool '%s' already exists." "A ferramenta '%s' j\u00e1 existe."
::msgcat::mcset pt_pt "Could not add tool:\n%s" "N\u00e3o foi poss\u00edvel adicionar ferramenta:\n%s"
::msgcat::mcset pt_pt "Remove Tool" "Remover ferramenta"
::msgcat::mcset pt_pt "Remove Tool Commands" "Remover comandos de ferramenta"
::msgcat::mcset pt_pt "Remove" "Remover"
::msgcat::mcset pt_pt "(Blue denotes repository-local tools)" "(Azul denota ferramentas locais do reposit\u00f3rio)"
::msgcat::mcset pt_pt "Run Command: %s" "Executar comando: %s"
::msgcat::mcset pt_pt "Arguments" "Argumentos"
::msgcat::mcset pt_pt "OK" "OK"
::msgcat::mcset pt_pt "Find:" "Procurar:"
::msgcat::mcset pt_pt "Next" "Seguinte"
::msgcat::mcset pt_pt "Prev" "Anterior"
::msgcat::mcset pt_pt "RegExp" "ExpReg"
::msgcat::mcset pt_pt "Case" "Mai\u00fasculas"
::msgcat::mcset pt_pt "Cannot write shortcut:" "N\u00e3o \u00e9 poss\u00edvel escrever atalho:"
::msgcat::mcset pt_pt "Cannot write icon:" "N\u00e3o \u00e9 poss\u00edvel escrever \u00edcone:"
::msgcat::mcset pt_pt "Rename Branch" "Mudar nome de ramo"
::msgcat::mcset pt_pt "Rename" "Mudar nome"
::msgcat::mcset pt_pt "Branch:" "Ramo:"
::msgcat::mcset pt_pt "New Name:" "Novo nome:"
::msgcat::mcset pt_pt "Please select a branch to rename." "Selecione um ramo para mudar de nome."
::msgcat::mcset pt_pt "Please supply a branch name." "Indique um nome para o ramo."
::msgcat::mcset pt_pt "'%s' is not an acceptable branch name." "'%s' n\u00e3o pode ser aceite como nome de ramo."
::msgcat::mcset pt_pt "Failed to rename '%s'." "Falha ao mudar o nome de '%s'."
::msgcat::mcset pt_pt "Delete Branch Remotely" "Remover ramo remotamente"
::msgcat::mcset pt_pt "From Repository" "Do reposit\u00f3rio"
::msgcat::mcset pt_pt "Branches" "Ramos"
::msgcat::mcset pt_pt "Delete Only If" "Eliminar s\u00f3 se"
::msgcat::mcset pt_pt "Merged Into:" "Integrar em:"
::msgcat::mcset pt_pt "Always (Do not perform merge checks)" "Sempre (n\u00e3o realizar verifica\u00e7\u00e3o de integra\u00e7\u00e3o)"
::msgcat::mcset pt_pt "A branch is required for 'Merged Into'." "\u00c9 necess\u00e1rio um ramo em 'Integrar em'."
::msgcat::mcset pt_pt "The following branches are not completely merged into %s:\n\n - %s" "Os seguintes ramos n\u00e3o foram completamente integrados em %s:\n\n - %s"
::msgcat::mcset pt_pt "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "Um ou mais testes de integra\u00e7\u00e3o falharam porque n\u00e3o obteve os commits necess\u00e1rios. Tente primeiro obter de %s."
::msgcat::mcset pt_pt "Please select one or more branches to delete." "Selecione um ou mais ramos para eliminar."
::msgcat::mcset pt_pt "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "Recuperar ramos eliminados \u00e9 dif\u00edcil.\n\nEliminar os ramos selecionado?"
::msgcat::mcset pt_pt "Deleting branches from %s" "A eliminar ramos de %s"
::msgcat::mcset pt_pt "No repository selected." "Nenhum reposit\u00f3rio selecionado."
::msgcat::mcset pt_pt "Scanning %s..." "A analisar %s..."
::msgcat::mcset pt_pt "Git Gui" "Git Gui"
::msgcat::mcset pt_pt "Create New Repository" "Criar novo reposit\u00f3rio"
::msgcat::mcset pt_pt "New..." "Novo..."
::msgcat::mcset pt_pt "Clone Existing Repository" "Clonar reposit\u00f3rio existente"
::msgcat::mcset pt_pt "Clone..." "Clonar..."
::msgcat::mcset pt_pt "Open Existing Repository" "Abrir reposit\u00f3rio existente"
::msgcat::mcset pt_pt "Open..." "Abrir..."
::msgcat::mcset pt_pt "Recent Repositories" "Reposit\u00f3rios recentes"
::msgcat::mcset pt_pt "Open Recent Repository:" "Abrir reposit\u00f3rio recente:"
::msgcat::mcset pt_pt "Failed to create repository %s:" "Falha ao criar o reposit\u00f3rio %s:"
::msgcat::mcset pt_pt "Create" "Criar"
::msgcat::mcset pt_pt "Directory:" "Diret\u00f3rio:"
::msgcat::mcset pt_pt "Git Repository" "Reposit\u00f3rio Git"
::msgcat::mcset pt_pt "Directory %s already exists." "O diret\u00f3rio %s j\u00e1 existe."
::msgcat::mcset pt_pt "File %s already exists." "O ficheiro %s j\u00e1 existe."
::msgcat::mcset pt_pt "Clone" "Clonar"
::msgcat::mcset pt_pt "Source Location:" "Localiza\u00e7\u00e3o de origem:"
::msgcat::mcset pt_pt "Target Directory:" "Diret\u00f3rio de destino:"
::msgcat::mcset pt_pt "Clone Type:" "Tipo de clone:"
::msgcat::mcset pt_pt "Standard (Fast, Semi-Redundant, Hardlinks)" "Padr\u00e3o (r\u00e1pido, semi-redundante, liga\u00e7\u00f5es fixas)"
::msgcat::mcset pt_pt "Full Copy (Slower, Redundant Backup)" "C\u00f3pia Total (lento, c\u00f3pia de seguran\u00e7a redundante)"
::msgcat::mcset pt_pt "Shared (Fastest, Not Recommended, No Backup)" "Partilhado (mais r\u00e1pido, n\u00e3o recomendado, sem c\u00f3pia)"
::msgcat::mcset pt_pt "Recursively clone submodules too" "Clonar recursivamente subm\u00f3dulos tamb\u00e9m"
::msgcat::mcset pt_pt "Not a Git repository: %s" "N\u00e3o \u00e9 um reposit\u00f3rio Git: %s"
::msgcat::mcset pt_pt "Standard only available for local repository." "Padr\u00e3o s\u00f3 dispon\u00edvel em reposit\u00f3rios locais."
::msgcat::mcset pt_pt "Shared only available for local repository." "Partilhado s\u00f3 dispon\u00edvel em reposit\u00f3rios locais."
::msgcat::mcset pt_pt "Location %s already exists." "A localiza\u00e7\u00e3o %s j\u00e1 existe."
::msgcat::mcset pt_pt "Failed to configure origin" "Falha ao configurar origem"
::msgcat::mcset pt_pt "Counting objects" "A contar objetos"
::msgcat::mcset pt_pt "buckets" "baldes"
::msgcat::mcset pt_pt "Unable to copy objects/info/alternates: %s" "N\u00e3o \u00e9 poss\u00edvel copiar objects/info/alternates: %s"
::msgcat::mcset pt_pt "Nothing to clone from %s." "Nada para clonar de %s."
::msgcat::mcset pt_pt "The 'master' branch has not been initialized." "O ramo 'master' n\u00e3o foi inicializado."
::msgcat::mcset pt_pt "Hardlinks are unavailable.  Falling back to copying." "Liga\u00e7\u00f5es fixas indispon\u00edveis. A recorrer a c\u00f3pia."
::msgcat::mcset pt_pt "Cloning from %s" "A clonar de %s"
::msgcat::mcset pt_pt "Copying objects" "A copiar objetos"
::msgcat::mcset pt_pt "KiB" "KiB"
::msgcat::mcset pt_pt "Unable to copy object: %s" "N\u00e3o \u00e9 poss\u00edvel copiar objeto: %s"
::msgcat::mcset pt_pt "Linking objects" "A ligar objetos"
::msgcat::mcset pt_pt "objects" "objetos"
::msgcat::mcset pt_pt "Unable to hardlink object: %s" "N\u00e3o \u00e9 poss\u00edvel criar liga\u00e7\u00e3o fixa de objeto: %s"
::msgcat::mcset pt_pt "Cannot fetch branches and objects.  See console output for details." "N\u00e3o \u00e9 poss\u00edvel obter ramos e objetos. Ver sa\u00edda na consola para detalhes."
::msgcat::mcset pt_pt "Cannot fetch tags.  See console output for details." "N\u00e3o \u00e9 poss\u00edvel obter tags. Ver sa\u00edda na consola para detalhes."
::msgcat::mcset pt_pt "Cannot determine HEAD.  See console output for details." "N\u00e3o \u00e9 poss\u00edvel determinar HEAD. Ver sa\u00edda na consola para detalhes."
::msgcat::mcset pt_pt "Unable to cleanup %s" "N\u00e3o foi poss\u00edvel limpar %s"
::msgcat::mcset pt_pt "Clone failed." "Falha ao clonar."
::msgcat::mcset pt_pt "No default branch obtained." "N\u00e3o foi obtido nenhum ramo predefinido."
::msgcat::mcset pt_pt "Cannot resolve %s as a commit." "N\u00e3o \u00e9 poss\u00edvel resolver %s como um commit."
::msgcat::mcset pt_pt "Creating working directory" "A criar diret\u00f3rio de trabalho"
::msgcat::mcset pt_pt "files" "ficheiros"
::msgcat::mcset pt_pt "Cannot clone submodules." "N\u00e3o \u00e9 poss\u00edvel clonar subm\u00f3dulos."
::msgcat::mcset pt_pt "Cloning submodules" "A clonar subm\u00f3dulos"
::msgcat::mcset pt_pt "Initial file checkout failed." "Falha de extra\u00e7\u00e3o inicial de ficheiro."
::msgcat::mcset pt_pt "Open" "Abrir"
::msgcat::mcset pt_pt "Repository:" "Reposit\u00f3rio:"
::msgcat::mcset pt_pt "Failed to open repository %s:" "Falha ao abrir o reposit\u00f3rio %s:"
::msgcat::mcset pt_pt "git-gui - a graphical user interface for Git." "git-gui - uma interface gr\u00e1fica do Git."
::msgcat::mcset pt_pt "File Viewer" "Visualizador de ficheiros"
::msgcat::mcset pt_pt "Commit:" "Commit:"
::msgcat::mcset pt_pt "Copy Commit" "Copiar commit"
::msgcat::mcset pt_pt "Find Text..." "Procurar texto..."
::msgcat::mcset pt_pt "Goto Line..." "Ir para a linha..."
::msgcat::mcset pt_pt "Do Full Copy Detection" "Efetuar dete\u00e7\u00e3o de c\u00f3pia integral"
::msgcat::mcset pt_pt "Show History Context" "Mostrar contexto hist\u00f3rico"
::msgcat::mcset pt_pt "Blame Parent Commit" "Culpar commit pai"
::msgcat::mcset pt_pt "Reading %s..." "A ler %s..."
::msgcat::mcset pt_pt "Loading copy/move tracking annotations..." "A carregar anota\u00e7\u00f5es de c\u00f3pia/movimento..."
::msgcat::mcset pt_pt "lines annotated" "linhas anotadas"
::msgcat::mcset pt_pt "Loading original location annotations..." "A carregar anota\u00e7\u00f5es da localiza\u00e7\u00e3o original..."
::msgcat::mcset pt_pt "Annotation complete." "Anota\u00e7\u00e3o conclu\u00edda."
::msgcat::mcset pt_pt "Busy" "A processar"
::msgcat::mcset pt_pt "Annotation process is already running." "O processo de anota\u00e7\u00e3o j\u00e1 est\u00e1 em execu\u00e7\u00e3o."
::msgcat::mcset pt_pt "Running thorough copy detection..." "A executar dete\u00e7\u00e3o de c\u00f3pia integral..."
::msgcat::mcset pt_pt "Loading annotation..." "A carregar anota\u00e7\u00e3o..."
::msgcat::mcset pt_pt "Author:" "Autor:"
::msgcat::mcset pt_pt "Committer:" "Committer:"
::msgcat::mcset pt_pt "Original File:" "Ficheiro original:"
::msgcat::mcset pt_pt "Cannot find HEAD commit:" "N\u00e3o \u00e9 poss\u00edvel encontrar commit HEAD:"
::msgcat::mcset pt_pt "Cannot find parent commit:" "N\u00e3o \u00e9 poss\u00edvel encontrar commit pai:"
::msgcat::mcset pt_pt "Unable to display parent" "N\u00e3o \u00e9 poss\u00edvel mostrar pai"
::msgcat::mcset pt_pt "Originally By:" "Originalmente por:"
::msgcat::mcset pt_pt "In File:" "No ficheiro:"
::msgcat::mcset pt_pt "Copied Or Moved Here By:" "Copiado ou Movido para aqui por:"
::msgcat::mcset pt_pt "No keys found." "Nenhum chave encontrada."
::msgcat::mcset pt_pt "Found a public key in: %s" "Chave p\u00fablica encontrada em: %s"
::msgcat::mcset pt_pt "Generate Key" "Gerar chave"
::msgcat::mcset pt_pt "Copy To Clipboard" "Copiar para a \u00e1rea de transfer\u00eancia"
::msgcat::mcset pt_pt "Your OpenSSH Public Key" "A sua chave OpenSSH p\u00fablica"
::msgcat::mcset pt_pt "Generating..." "A gerar..."
::msgcat::mcset pt_pt "Could not start ssh-keygen:\n\n%s" "N\u00e3o foi poss\u00edvel iniciar ssh-keygen:\n\n%s"
::msgcat::mcset pt_pt "Generation failed." "Falha ao gerar."
::msgcat::mcset pt_pt "Generation succeeded, but no keys found." "Gerada com sucesso, mas n\u00e3o foi encontrada nenhum chave."
::msgcat::mcset pt_pt "Your key is in: %s" "A sua chave encontra-se em: %s"
::msgcat::mcset pt_pt "Create Branch" "Criar ramo"
::msgcat::mcset pt_pt "Create New Branch" "Cria novo ramo"
::msgcat::mcset pt_pt "Branch Name" "Nome do ramo"
::msgcat::mcset pt_pt "Match Tracking Branch Name" "Corresponder ao nome do ramo de monitoriza\u00e7\u00e3o"
::msgcat::mcset pt_pt "Starting Revision" "Revis\u00e3o inicial"
::msgcat::mcset pt_pt "Update Existing Branch:" "Atualizar ramo existente:"
::msgcat::mcset pt_pt "No" "N\u00e3o"
::msgcat::mcset pt_pt "Fast Forward Only" "Apenas avan\u00e7o r\u00e1pido (fast-forward)"
::msgcat::mcset pt_pt "Checkout After Creation" "Extrair depois de criar"
::msgcat::mcset pt_pt "Please select a tracking branch." "Selecione um ramo de monitoriza\u00e7\u00e3o."
::msgcat::mcset pt_pt "Tracking branch %s is not a branch in the remote repository." "O ramo de monitoriza\u00e7\u00e3o %s n\u00e3o \u00e9 um ramo no reposit\u00f3rio remoto."
::msgcat::mcset pt_pt "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "N\u00e3o h\u00e1 nada para emendar.\n\nEst\u00e1 prestes a criar o commit inicial. N\u00e3o h\u00e1 nenhum commit antes deste para emendar.\n"
::msgcat::mcset pt_pt "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "N\u00e3o \u00e9 poss\u00edvel emendar ao mesmo tempo que se integra.\n\nH\u00e1 uma integra\u00e7\u00e3o em curso que n\u00e3o foi conclu\u00edda. N\u00e3o pode emendar o commit anterior a n\u00e3o ser que primeiro aborte a atividade da integra\u00e7\u00e3o atual.\n"
::msgcat::mcset pt_pt "Error loading commit data for amend:" "Erro ao carregar dados do commit para emendar:"
::msgcat::mcset pt_pt "Unable to obtain your identity:" "N\u00e3o \u00e9 poss\u00edvel obter a sua identidade:"
::msgcat::mcset pt_pt "Invalid GIT_COMMITTER_IDENT:" "GIT_COMMITTER_IDENT inv\u00e1lido:"
::msgcat::mcset pt_pt "warning: Tcl does not support encoding '%s'." "aviso: Tcl n\u00e3o suporta a codifica\u00e7\u00e3o '%s'."
::msgcat::mcset pt_pt "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "O \u00faltimo estado analisado n\u00e3o corresponde ao estado do reposit\u00f3rio.\n\nOutro programa Git modificou este reposit\u00f3rio deste a \u00faltima an\u00e1lise. Deve-se reanalisar antes que se possa criar outro commit.\n\nIr\u00e1-se reanalisar automaticamente agora.\n"
::msgcat::mcset pt_pt "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "N\u00e3o pode fazer commit de ficheiros n\u00e3o integrados.\n\nO ficheiro %s tem conflitos de integra\u00e7\u00e3o. Deve resolv\u00ea-los e preparar o ficheiro antes de submeter.\n"
::msgcat::mcset pt_pt "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "Detetado estado de ficheiro %s desconhecido.\n\nEste programa n\u00e3o pode submeter o ficheiro %s.\n"
::msgcat::mcset pt_pt "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "Nenhum altera\u00e7\u00e3o para submeter.\n\nDeve preparar pelo menos 1 ficheiro antes de submeter.\n"
::msgcat::mcset pt_pt "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "Forne\u00e7a uma mensagem de commit.\n\nUm boa mensagem de commit tem o seguinte formato:\n\n- Primeira linha: descreve numa frase o que fez.\n- Segunda linha: em branco.\n- Linhas restantes: descreve porque esta altera\u00e7\u00e3o \u00e9 vantajosa.\n"
::msgcat::mcset pt_pt "Calling pre-commit hook..." "A invocar gancho de pr\u00e9-commit (pre-commit hook)..."
::msgcat::mcset pt_pt "Commit declined by pre-commit hook." "Commit recusado pela retina de pr\u00e9-commit (pre-commit hook)."
::msgcat::mcset pt_pt "You are about to commit on a detached head. This is a potentially dangerous thing to do because if you switch to another branch you will lose your changes and it can be difficult to retrieve them later from the reflog. You should probably cancel this commit and create a new branch to continue.\n \n Do you really want to proceed with your Commit?" "Est\u00e1 prestes a submeter numa cabe\u00e7a destacada. Faz\u00ea-lo \u00e9 potencialmente perigoso, porque, se mudar para outro ramo, perder\u00e1 as suas altera\u00e7\u00f5es e pode ser dif\u00edcil recuper\u00e1-las do reflog posteriormente. Provavelmente deve cancelar este commit e criar um novo ramo para continuar.\n\nPretende mesmo continuar com o commit?"
::msgcat::mcset pt_pt "Calling commit-msg hook..." "A invocar gancho de mensagem-de-commit (commit-msg hook)..."
::msgcat::mcset pt_pt "Commit declined by commit-msg hook." "Commit recusado pelo gancho de mensagem-de-commit (commit-msg hook)."
::msgcat::mcset pt_pt "Committing changes..." "A submeter altera\u00e7\u00f5es..."
::msgcat::mcset pt_pt "write-tree failed:" "write-tree falhou:"
::msgcat::mcset pt_pt "Commit failed." "Falha ao submeter."
::msgcat::mcset pt_pt "Commit %s appears to be corrupt" "O commit %s parece estar corrompido"
::msgcat::mcset pt_pt "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "N\u00e3o h\u00e1 altera\u00e7\u00f5es para submeter.\n\nNenhum ficheiro foi modificado por este commit e n\u00e3o era um commit de integra\u00e7\u00e3o.\n\nIr\u00e1-se reanalisar agora automaticamente.\n"
::msgcat::mcset pt_pt "No changes to commit." "N\u00e3o h\u00e1 altera\u00e7\u00f5es para submeter."
::msgcat::mcset pt_pt "commit-tree failed:" "commit-tree falhou:"
::msgcat::mcset pt_pt "update-ref failed:" "update-ref falhou:"
::msgcat::mcset pt_pt "Created commit %s: %s" "Commit %s criado: %s"
::msgcat::mcset pt_pt "Delete Branch" "Eliminar ramo"
::msgcat::mcset pt_pt "Delete Local Branch" "Eliminar ramo local"
::msgcat::mcset pt_pt "Local Branches" "Ramos locais"
::msgcat::mcset pt_pt "Delete Only If Merged Into" "Eliminar s\u00f3 se foi integrado"
::msgcat::mcset pt_pt "The following branches are not completely merged into %s:" "Os seguintes ramos n\u00e3o foram completamente integrados em %s:"
::msgcat::mcset pt_pt "Failed to delete branches:\n%s" "Falha ao eliminar ramos:\n%s"
::msgcat::mcset pt_pt "Unable to unlock the index." "N\u00e3o \u00e9 poss\u00edvel desbloquear o \u00edndice."
::msgcat::mcset pt_pt "Index Error" "Erro de \u00cdndice"
::msgcat::mcset pt_pt "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "Falha ao atualizar o \u00edndice do Git. Ir\u00e1-se reanalisar automaticamente para ressincronizar o git-gui."
::msgcat::mcset pt_pt "Continue" "Continuar"
::msgcat::mcset pt_pt "Unlock Index" "Desbloquear \u00edndice"
::msgcat::mcset pt_pt "Unstaging selected files from commit" "A retirar ficheiros selecionados do commit"
::msgcat::mcset pt_pt "Unstaging %s from commit" "A retirar %s do commit"
::msgcat::mcset pt_pt "Ready to commit." "Pronto para submeter."
::msgcat::mcset pt_pt "Adding selected files" "A adicionar ficheiros selecionados"
::msgcat::mcset pt_pt "Adding %s" "A adicionar %s"
::msgcat::mcset pt_pt "Stage %d untracked files?" "Preparar %d ficheiros n\u00e3o controlados?"
::msgcat::mcset pt_pt "Adding all changed files" "A adicionar todos os ficheiros controlados"
::msgcat::mcset pt_pt "Revert changes in file %s?" "Reverter altera\u00e7\u00f5es no ficheiro %s?"
::msgcat::mcset pt_pt "Revert changes in these %i files?" "Reverter altera\u00e7\u00f5es nestes %i ficheiros?"
::msgcat::mcset pt_pt "Any unstaged changes will be permanently lost by the revert." "Qualquer altera\u00e7\u00e3o n\u00e3o preparada ser\u00e1 permanentemente perdida ao reverter."
::msgcat::mcset pt_pt "Do Nothing" "N\u00e3o fazer nada"
::msgcat::mcset pt_pt "Reverting selected files" "A reverter ficheiros selecionados"
::msgcat::mcset pt_pt "Reverting %s" "A reverter %s"
::msgcat::mcset pt_pt "Default" "Predefini\u00e7\u00e3o"
::msgcat::mcset pt_pt "System (%s)" "Sistema (%s)"
::msgcat::mcset pt_pt "Other" "Outro"
::msgcat::mcset pt_pt "Invalid date from Git: %s" "Data do Git inv\u00e1lida: %s"
::msgcat::mcset pt_pt "This Detached Checkout" "Esta extra\u00e7\u00e3o destacada"
::msgcat::mcset pt_pt "Revision Expression:" "Express\u00e3o de revis\u00e3o:"
::msgcat::mcset pt_pt "Local Branch" "Ramo local"
::msgcat::mcset pt_pt "Tracking Branch" "Ramo de monitoriza\u00e7\u00e3o"
::msgcat::mcset pt_pt "Tag" "Tag"
::msgcat::mcset pt_pt "Invalid revision: %s" "Revis\u00e3o inv\u00e1lida: %s"
::msgcat::mcset pt_pt "No revision selected." "Nenhum revis\u00e3o selecionada."
::msgcat::mcset pt_pt "Revision expression is empty." "A express\u00e3o de revis\u00e3o est\u00e1 vazia."
::msgcat::mcset pt_pt "Updated" "Atualizado"
::msgcat::mcset pt_pt "URL" "URL"
::msgcat::mcset pt_pt "Number of loose objects" "N\u00famero de objetos soltos"
::msgcat::mcset pt_pt "Disk space used by loose objects" "Espa\u00e7o em disco usados por objetos soltos"
::msgcat::mcset pt_pt "Number of packed objects" "N\u00famero de objetos compactados"
::msgcat::mcset pt_pt "Number of packs" "N\u00fameros de pacotes"
::msgcat::mcset pt_pt "Disk space used by packed objects" "Espa\u00e7o em disco usado por objetos compactados"
::msgcat::mcset pt_pt "Packed objects waiting for pruning" "Objetos compactados \u00e0 espera de poda"
::msgcat::mcset pt_pt "Garbage files" "Ficheiros de lixo"
::msgcat::mcset pt_pt "Compressing the object database" "A comprimir a base de dados de objetos"
::msgcat::mcset pt_pt "Verifying the object database with fsck-objects" "A verificar a base de dados de objetos com fsck-objects"
::msgcat::mcset pt_pt "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "Este reposit\u00f3rio tem aproximadamente %i objetos soltos.\n\nPara manter o desempenho \u00f3timo \u00e9 veemente recomendado que comprima a base de dados.\n\nComprimir a base de dados agora?"
::msgcat::mcset pt_pt "error" "erro"
::msgcat::mcset pt_pt "warning" "aviso"
::msgcat::mcset pt_pt "You must correct the above errors before committing." "Deve corrigir os erros acima antes de submeter."
::msgcat::mcset pt_pt "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "N\u00e3o poss\u00edvel integrar ao mesmo tempo que se emenda.\n\nDeve acabar de emendar este commit antes de iniciar qualquer tipo de integra\u00e7\u00e3o.\n"
::msgcat::mcset pt_pt "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "O \u00faltimo estado analisado n\u00e3o corresponde ao estado do reposit\u00f3rio.\n\nOutro programa Git modificou este reposit\u00f3rio deste a \u00faltima an\u00e1lise. Deve-se reanalisar antes de se poder integrar.\n\nIr\u00e1-se reanalisar agora automaticamente.\n"
::msgcat::mcset pt_pt "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "Integra\u00e7\u00e3o com conflitos em curso.\n\nO ficheiro %s tem conflitos de integra\u00e7\u00e3o.\n\nDeve resolv\u00ea-los, preparar o ficheiro e submeter para concluir a integra\u00e7\u00e3o atual. S\u00f3 ent\u00e3o pode iniciar outra integra\u00e7\u00e3o.\n"
::msgcat::mcset pt_pt "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "Tem altera\u00e7\u00f5es presentes.\n\nO ficheiro %s foi modificado.\n\nDeve concluir o commit atual antes de iniciar uma integra\u00e7\u00e3o. Assim, ajuda-o a abortar uma integra\u00e7\u00e3o falhada, caso necess\u00e1rio.\n"
::msgcat::mcset pt_pt "%s of %s" "%s de %s"
::msgcat::mcset pt_pt "Merging %s and %s..." "A integrar %s e %s..."
::msgcat::mcset pt_pt "Merge completed successfully." "Integra\u00e7\u00e3o conclu\u00edda com sucesso."
::msgcat::mcset pt_pt "Merge failed.  Conflict resolution is required." "Integra\u00e7\u00e3o falhada. \u00c9 necess\u00e1rio resolver conflitos."
::msgcat::mcset pt_pt "Merge Into %s" "Integrar em %s"
::msgcat::mcset pt_pt "Revision To Merge" "Revis\u00e3o a integrar"
::msgcat::mcset pt_pt "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "N\u00e3o \u00e9 poss\u00edvel abortar enquanto se emenda.\n\nDeve acabar de emendar este commit.\n"
::msgcat::mcset pt_pt "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "Abortar integra\u00e7\u00e3o?\n\nAo abortar a integra\u00e7\u00e3o atual perder\u00e1 *TODAS* as altera\u00e7\u00e3o que n\u00e3o foram submetidas.\n\nContinuar a abortar a integra\u00e7\u00e3o atual?"
::msgcat::mcset pt_pt "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "Repor altera\u00e7\u00f5es?\n\nAo repor as altera\u00e7\u00f5es perder\u00e1 *TODAS* as altera\u00e7\u00f5es n\u00e3o submetidas.\n\nContinuar a repor as altera\u00e7\u00f5es atuais?"
::msgcat::mcset pt_pt "Aborting" "A abortar"
::msgcat::mcset pt_pt "files reset" "ficheiros repostos"
::msgcat::mcset pt_pt "Abort failed." "Falha ao abortar."
::msgcat::mcset pt_pt "Abort completed.  Ready." "Aborto conclu\u00eddo. Pronto."
