<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_handle_events</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_handle_events - advance asynchronous state machine and perform network I/O</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

int SSL_handle_events(SSL *ssl);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_handle_events() performs any internal processing which is due on an SSL object. The exact operations performed by SSL_handle_events() vary depending on what kind of protocol is being used with the given SSL object. For example, SSL_handle_events() may handle timeout events which have become due, or may attempt, to the extent currently possible, to perform network I/O operations on one of the BIOs underlying the SSL object.</p>

<p>The primary use case for SSL_handle_events() is to allow an application which uses OpenSSL in nonblocking mode to give OpenSSL an opportunity to handle timer events, or to respond to the availability of new data to be read from an underlying BIO, or to respond to the opportunity to write pending data to an underlying BIO.</p>

<p>SSL_handle_events() can be used only with the following types of SSL object:</p>

<dl>

<dt id="DTLS-SSL-objects">DTLS SSL objects</dt>
<dd>

<p>Using SSL_handle_events() on an SSL object being used with a DTLS method allows timeout events to be handled properly. This is equivalent to a call to <a href="../man3/DTLSv1_handle_timeout.html">DTLSv1_handle_timeout(3)</a>. Since SSL_handle_events() handles a superset of the use cases of <a href="../man3/DTLSv1_handle_timeout.html">DTLSv1_handle_timeout(3)</a>, it should be preferred for new applications which do not require support for OpenSSL 3.1 or older.</p>

<p>When using DTLS, an application must call SSL_handle_events() as indicated by calls to <a href="../man3/SSL_get_event_timeout.html">SSL_get_event_timeout(3)</a>; event handling is not performed automatically by calls to other SSL functions such as <a href="../man3/SSL_read.html">SSL_read(3)</a> or <a href="../man3/SSL_write.html">SSL_write(3)</a>. Note that this is different to QUIC which also performs event handling implicitly; see below.</p>

</dd>
<dt id="QUIC-connection-SSL-objects">QUIC connection SSL objects</dt>
<dd>

<p>Using SSL_handle_events() on an SSL object which represents a QUIC connection allows timeout events to be handled properly, as well as incoming network data to be processed, and queued outgoing network data to be written, if the underlying BIO has the capacity to accept it.</p>

<p>Ordinarily, when an application uses an SSL object in blocking mode, it does not need to call SSL_handle_events() because OpenSSL performs ticking internally on an automatic basis. However, if an application uses a QUIC connection in nonblocking mode, it must at a minimum ensure that SSL_handle_events() is called periodically to allow timeout events to be handled. An application can find out when it next needs to call SSL_handle_events() for this purpose (if at all) by calling <a href="../man3/SSL_get_event_timeout.html">SSL_get_event_timeout(3)</a>.</p>

<p>Calling SSL_handle_events() on a QUIC connection SSL object being used in blocking mode is not necessary unless no I/O calls (such as <a href="../man3/SSL_read.html">SSL_read(3)</a> or <a href="../man3/SSL_write.html">SSL_write(3)</a>) will be made to the object for a substantial period of time. So long as at least one call to the SSL object is blocking, no such call is needed. However, SSL_handle_events() may optionally be used on a QUIC connection object if desired.</p>

<p>With the thread-assisted mode of operation <a href="../man3/OSSL_QUIC_client_thread_method.html">OSSL_QUIC_client_thread_method(3)</a> it is unnecessary to call SSL_handle_events() as the assist thread handles the QUIC connection events.</p>

</dd>
</dl>

<p>Calling SSL_handle_events() on any other kind of SSL object is a no-op. This is considered a success case.</p>

<p>Note that SSL_handle_events() supersedes the older <a href="../man3/DTLSv1_handle_timeout.html">DTLSv1_handle_timeout(3)</a> function for all use cases.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>Returns 1 on success and 0 on failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/SSL_get_event_timeout.html">SSL_get_event_timeout(3)</a>, <a href="../man3/DTLSv1_handle_timeout.html">DTLSv1_handle_timeout(3)</a>, <a href="../man7/ssl.html">ssl(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The SSL_handle_events() function was added in OpenSSL 3.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


