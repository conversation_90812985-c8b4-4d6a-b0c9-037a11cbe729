<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OPENSSL_load_u16_le</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OPENSSL_load_u16_le, OPENSSL_load_u16_be, OPENSSL_load_u32_le, OPENSSL_load_u32_be, OPENSSL_load_u64_le, OPENSSL_load_u64_be, OPENSSL_store_u16_le, OPENSSL_store_u16_be, OPENSSL_store_u32_le, OPENSSL_store_u32_be, OPENSSL_store_u64_le, OPENSSL_store_u64_be - Read and write unsigned 16, 32 and 64-bit integers in a specific byte order</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/byteorder.h&gt;

static ossl_inline unsigned char *OPENSSL_store_u16_le(
    unsigned char *out, uint16_t val);
static ossl_inline unsigned char *OPENSSL_store_u16_be(
    unsigned char *out, uint16_t val);
static ossl_inline unsigned char *OPENSSL_store_u32_le(
    unsigned char *out, uint32_t val);
static ossl_inline unsigned char *OPENSSL_store_u32_be(
    unsigned char *out, uint32_t val);
static ossl_inline unsigned char *OPENSSL_store_u64_le(
    unsigned char *out, uint64_t val);
static ossl_inline unsigned char *OPENSSL_store_u64_be(
    unsigned char *out, uint64_t val);
static ossl_inline const unsigned char *OPENSSL_load_u16_le(
    uint16_t *val, const unsigned char *in);
static ossl_inline const unsigned char *OPENSSL_load_u16_be(
    uint16_t *val, const unsigned char *in);
static ossl_inline const unsigned char *OPENSSL_load_u32_le(
    uint32_t *val, const unsigned char *in);
static ossl_inline const unsigned char *OPENSSL_load_u32_be(
    uint32_t *val, const unsigned char *in);
static ossl_inline const unsigned char *OPENSSL_load_u64_le(
    uint64_t *val, const unsigned char *in);
static ossl_inline const unsigned char *OPENSSL_load_u64_be(
    uint64_t *val, const unsigned char *in);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions read and write 16, 32 and 64 bit unsigned integers in a specified byte order. The <code>_be</code> functions use big-endian byte order, while the <code>_le</code> functions use little-endian byte order. They&#39;re implemented directly in the header file, and declared static. When the compiler supports inline functions, they&#39;re also declared inline. An optimising compiler will often convert these to just one or two machine instructions: a load or store with a possible byte swap.</p>

<p>The <code>load</code> functions write the decoded integer value at the address pointed to by <i>val</i>, which must be a valid (possibly suitably aligned) address of an object of the appropriate type. The <code>store</code> functions write the encoding of <i>val</i> at the address pointed to by <i>out</i>.</p>

<p>For convenience, these functions return the updated input or output pointer, making it easy to continue reading or writing more data at the next memory location.</p>

<p>No bounds checks are performed, the caller is responsible for making sure that the input or output buffers are sufficiently large for the requested read or write.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>All these functions return the next memory address following the last byte written or read.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 3.5.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2025 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


