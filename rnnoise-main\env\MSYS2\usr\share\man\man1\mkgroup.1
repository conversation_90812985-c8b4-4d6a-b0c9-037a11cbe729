'\" t
.\"     Title: mkgroup
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/18/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "MKGROUP" "1" "06/18/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
mkgroup \- Write /etc/group\-like output to stdout
.SH "SYNOPSIS"
.HP \w'\fBmkgroup\fR\ 'u
\fBmkgroup\fR [\-l\ |\ \-L\ [\fIMACHINE\fR]] [\-d\ [\fIDOMAIN\fR]] [\-c] [\-S\ \fICHAR\fR] [\-o\ \fIOFFSET\fR] [\-g\ \fIGROUPNAME\fR] [\-b] [\-U\ \fIGROUPLIST\fR]
.HP \w'\fBmkgroup\fR\ 'u
\fBmkgroup\fR \-h | \-V 
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
   \-l,\-\-local [machine]    Print local group accounts of \e"machine\e",
                           from local machine if no machine specified\&.
                           Automatically adding machine prefix for local
                           machine depends on settings in /etc/nsswitch\&.conf\&.
   \-L,\-\-Local [machine]    Ditto, but generate groupname with machine prefix\&.
   \-d,\-\-domain [domain]    Print domain groups,
                           from current domain if no domain specified\&.
   \-c,\-\-current            Print current group\&.
   \-S,\-\-separator char     For \-L use character char as domain\e\egroup
                           separator in groupname instead of default \*(Aq+\*(Aq\&.
   \-o,\-\-id\-offset offset   Change the default offset (0x10000) added to gids
   \-g,\-\-group groupname    Only return information for the specified group\&.
                           One of \-l, \-d must be specified, too\&.
   \-b,\-\-no\-builtin         Don\*(Aqt print BUILTIN groups\&.
   \-U,\-\-unix grouplist     Print UNIX groups when using \-l on a UNIX Samba
                           server\&.  Grouplist is a comma\-separated list of
                           groupnames or gid ranges (root,\-25,50\-100)\&.
                           Enumerating large ranges can take a long time!
   \-h,\-\-help               Print this message\&.
   \-v,\-\-version            Print version information and exit\&.

Default is to print local groups on stand\-alone machines, plus domain
groups on domain controllers and domain member machines\&.
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
Don\*(Aqt use this command to generate a local /etc/group file, unless you really need one\&. See the Cygwin User\*(Aqs Guide for more information\&.
.PP
The
\fBmkgroup\fR
program can be used to create a local
/etc/group
file\&. Cygwin doesn\*(Aqt need this file, because it reads group information from the Windows account databases, but you can add an
/etc/group
file for instance, if your machine is often disconnected from its domain controller\&.
.PP
Note that this information is static, in contrast to the information automatically gathered by Cygwin from the Windows account databases\&. If you change the group information on your system, you\*(Aqll need to regenerate the group file for it to have the new information\&.
.PP
By default, the information generated by
\fBmkgroup\fR
is equivalent to the information generated by Cygwin itself\&. The
\-d
and
\-l/\-L
options allow you to specify where the information comes from, some domain, or the local SAM of a machine\&. Note that you can only enumerate accounts from trusted domains\&. Any non\-trusted domain will be ignored\&. Access\-restrictions of your current account apply\&. The
\-l/\-L
when used with a machine name, tries to contact that machine to enumerate local groups of other machines, typically outside of domains\&. This scenario cannot be covered by Cygwin\*(Aqs account automatism\&. If you want to use the
\-L
option, but you don\*(Aqt like the default domain/group separator from
/etc/nsswitch\&.conf, you can specify another separator using the
\-S
option, for instance:
.PP
\fBExample\ \&3.9.\ \&Setting up group entry for current user with different domain/group separator\fR
.sp
.if n \{\
.RS 4
.\}
.nf
$ \fBmkgroup \-L server1 \-S= > /etc/group\fR
.fi
.if n \{\
.RE
.\}
.PP
For very simple needs, an entry for the current user\*(Aqs group can be created by using the option
\-c\&.
.PP
The
\-o
option allows for (unlikely) special cases with multiple machines where the GIDs might match otherwise\&. The
\-g
option only prints the information for one group\&. The
\-U
option allows you to enumerate the standard UNIX groups on a Samba machine\&. It\*(Aqs used together with
\-l samba\-server
or
\-L samba\-server\&. The normal UNIX groups are usually not enumerated, but they can show up as a group in
\fBls \-l\fR
output\&.
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
