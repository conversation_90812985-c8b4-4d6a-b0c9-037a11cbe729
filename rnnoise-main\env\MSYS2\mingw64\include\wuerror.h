/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _WUERROR_
#define _WUERROR_

#ifdef RC_INVOKED
#define _HRESULT_TYPEDEF_(_sc) _sc
#else
#define _HRESULT_TYPEDEF_(_sc) ((HRESULT)_sc)
#endif

#define WU_S_SERVICE_STOP _HRESULT_TYPEDEF_(0x00240001)
#define WU_S_SELFUPDATE _HRESULT_TYPEDEF_(0x00240002)
#define WU_S_UPDATE_ERROR _HRESULT_TYPEDEF_(0x00240003)
#define WU_S_MARKED_FOR_DISCONNECT _HRESULT_TYPEDEF_(0x00240004)
#define WU_S_REBOOT_REQUIRED _HRESULT_TYPEDEF_(0x00240005)
#define WU_S_ALREADY_INSTALLED _HRESULT_TYPEDEF_(0x00240006)
#define WU_S_ALREADY_UNINSTALLED _HRESULT_TYPEDEF_(0x00240007)
#define WU_S_ALREADY_DOWNLOADED _HRESULT_TYPEDEF_(0x00240008)
#define WU_S_SOME_UPDATES_SKIPPED_ON_BATTERY _HRESULT_TYPEDEF_(0x00240009)
#define WU_S_ALREADY_REVERTED _HRESULT_TYPEDEF_(0x0024000A)
#define WU_S_SEARCH_CRITERIA_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x00240010)
#define WU_S_UH_INSTALLSTILLPENDING _HRESULT_TYPEDEF_(0x00242015)
#define WU_S_UH_DOWNLOAD_SIZE_CALCULATED _HRESULT_TYPEDEF_(0x00242016)
#define WU_S_SIH_NOOP _HRESULT_TYPEDEF_(0x00245001)
#define WU_S_DM_ALREADYDOWNLOADING _HRESULT_TYPEDEF_(0x00246001)
#define WU_S_METADATA_SKIPPED_BY_ENFORCEMENTMODE _HRESULT_TYPEDEF_(0x00247101)
#define WU_S_METADATA_IGNORED_SIGNATURE_VERIFICATION _HRESULT_TYPEDEF_(0x00247102)
#define WU_S_SEARCH_LOAD_SHEDDING _HRESULT_TYPEDEF_(0x00248001)
#define WU_E_NO_SERVICE _HRESULT_TYPEDEF_(0x80240001)
#define WU_E_MAX_CAPACITY_REACHED _HRESULT_TYPEDEF_(0x80240002)
#define WU_E_UNKNOWN_ID _HRESULT_TYPEDEF_(0x80240003)
#define WU_E_NOT_INITIALIZED _HRESULT_TYPEDEF_(0x80240004)
#define WU_E_RANGEOVERLAP _HRESULT_TYPEDEF_(0x80240005)
#define WU_E_TOOMANYRANGES _HRESULT_TYPEDEF_(0x80240006)
#define WU_E_INVALIDINDEX _HRESULT_TYPEDEF_(0x80240007)
#define WU_E_ITEMNOTFOUND _HRESULT_TYPEDEF_(0x80240008)
#define WU_E_OPERATIONINPROGRESS _HRESULT_TYPEDEF_(0x80240009)
#define WU_E_COULDNOTCANCEL _HRESULT_TYPEDEF_(0x8024000A)
#define WU_E_CALL_CANCELLED _HRESULT_TYPEDEF_(0x8024000B)
#define WU_E_NOOP _HRESULT_TYPEDEF_(0x8024000C)
#define WU_E_XML_MISSINGDATA _HRESULT_TYPEDEF_(0x8024000D)
#define WU_E_XML_INVALID _HRESULT_TYPEDEF_(0x8024000E)
#define WU_E_CYCLE_DETECTED _HRESULT_TYPEDEF_(0x8024000F)
#define WU_E_TOO_DEEP_RELATION _HRESULT_TYPEDEF_(0x80240010)
#define WU_E_INVALID_RELATIONSHIP _HRESULT_TYPEDEF_(0x80240011)
#define WU_E_REG_VALUE_INVALID _HRESULT_TYPEDEF_(0x80240012)
#define WU_E_DUPLICATE_ITEM _HRESULT_TYPEDEF_(0x80240013)
#define WU_E_INVALID_INSTALL_REQUESTED _HRESULT_TYPEDEF_(0x80240014)
#define WU_E_INSTALL_NOT_ALLOWED _HRESULT_TYPEDEF_(0x80240016)
#define WU_E_NOT_APPLICABLE _HRESULT_TYPEDEF_(0x80240017)
#define WU_E_NO_USERTOKEN _HRESULT_TYPEDEF_(0x80240018)
#define WU_E_EXCLUSIVE_INSTALL_CONFLICT _HRESULT_TYPEDEF_(0x80240019)
#define WU_E_POLICY_NOT_SET _HRESULT_TYPEDEF_(0x8024001A)
#define WU_E_SELFUPDATE_IN_PROGRESS _HRESULT_TYPEDEF_(0x8024001B)
#define WU_E_INVALID_UPDATE _HRESULT_TYPEDEF_(0x8024001D)
#define WU_E_SERVICE_STOP _HRESULT_TYPEDEF_(0x8024001E)
#define WU_E_NO_CONNECTION _HRESULT_TYPEDEF_(0x8024001F)
#define WU_E_NO_INTERACTIVE_USER _HRESULT_TYPEDEF_(0x80240020)
#define WU_E_TIME_OUT _HRESULT_TYPEDEF_(0x80240021)
#define WU_E_ALL_UPDATES_FAILED _HRESULT_TYPEDEF_(0x80240022)
#define WU_E_EULAS_DECLINED _HRESULT_TYPEDEF_(0x80240023)
#define WU_E_NO_UPDATE _HRESULT_TYPEDEF_(0x80240024)
#define WU_E_USER_ACCESS_DISABLED _HRESULT_TYPEDEF_(0x80240025)
#define WU_E_INVALID_UPDATE_TYPE _HRESULT_TYPEDEF_(0x80240026)
#define WU_E_URL_TOO_LONG _HRESULT_TYPEDEF_(0x80240027)
#define WU_E_UNINSTALL_NOT_ALLOWED _HRESULT_TYPEDEF_(0x80240028)
#define WU_E_INVALID_PRODUCT_LICENSE _HRESULT_TYPEDEF_(0x80240029)
#define WU_E_MISSING_HANDLER _HRESULT_TYPEDEF_(0x8024002A)
#define WU_E_LEGACYSERVER _HRESULT_TYPEDEF_(0x8024002B)
#define WU_E_BIN_SOURCE_ABSENT _HRESULT_TYPEDEF_(0x8024002C)
#define WU_E_SOURCE_ABSENT _HRESULT_TYPEDEF_(0x8024002D)
#define WU_E_WU_DISABLED _HRESULT_TYPEDEF_(0x8024002E)
#define WU_E_CALL_CANCELLED_BY_POLICY _HRESULT_TYPEDEF_(0x8024002F)
#define WU_E_INVALID_PROXY_SERVER _HRESULT_TYPEDEF_(0x80240030)
#define WU_E_INVALID_FILE _HRESULT_TYPEDEF_(0x80240031)
#define WU_E_INVALID_CRITERIA _HRESULT_TYPEDEF_(0x80240032)
#define WU_E_EULA_UNAVAILABLE _HRESULT_TYPEDEF_(0x80240033)
#define WU_E_DOWNLOAD_FAILED _HRESULT_TYPEDEF_(0x80240034)
#define WU_E_UPDATE_NOT_PROCESSED _HRESULT_TYPEDEF_(0x80240035)
#define WU_E_INVALID_OPERATION _HRESULT_TYPEDEF_(0x80240036)
#define WU_E_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x80240037)
#define WU_E_WINHTTP_INVALID_FILE _HRESULT_TYPEDEF_(0x80240038)
#define WU_E_TOO_MANY_RESYNC _HRESULT_TYPEDEF_(0x80240039)
#define WU_E_NO_SERVER_CORE_SUPPORT _HRESULT_TYPEDEF_(0x80240040)
#define WU_E_SYSPREP_IN_PROGRESS _HRESULT_TYPEDEF_(0x80240041)
#define WU_E_UNKNOWN_SERVICE _HRESULT_TYPEDEF_(0x80240042)
#define WU_E_NO_UI_SUPPORT _HRESULT_TYPEDEF_(0x80240043)
#define WU_E_PER_MACHINE_UPDATE_ACCESS_DENIED _HRESULT_TYPEDEF_(0x80240044)
#define WU_E_UNSUPPORTED_SEARCHSCOPE _HRESULT_TYPEDEF_(0x80240045)
#define WU_E_BAD_FILE_URL _HRESULT_TYPEDEF_(0x80240046)
#define WU_E_REVERT_NOT_ALLOWED _HRESULT_TYPEDEF_(0x80240047)
#define WU_E_INVALID_NOTIFICATION_INFO _HRESULT_TYPEDEF_(0x80240048)
#define WU_E_OUTOFRANGE _HRESULT_TYPEDEF_(0x80240049)
#define WU_E_SETUP_IN_PROGRESS _HRESULT_TYPEDEF_(0x8024004A)
#define WU_E_ORPHANED_DOWNLOAD_JOB _HRESULT_TYPEDEF_(0x8024004B)
#define WU_E_LOW_BATTERY _HRESULT_TYPEDEF_(0x8024004C)
#define WU_E_INFRASTRUCTUREFILE_INVALID_FORMAT _HRESULT_TYPEDEF_(0x8024004D)
#define WU_E_INFRASTRUCTUREFILE_REQUIRES_SSL _HRESULT_TYPEDEF_(0x8024004E)
#define WU_E_IDLESHUTDOWN_OPCOUNT_DISCOVERY _HRESULT_TYPEDEF_(0x8024004F)
#define WU_E_IDLESHUTDOWN_OPCOUNT_SEARCH _HRESULT_TYPEDEF_(0x80240050)
#define WU_E_IDLESHUTDOWN_OPCOUNT_DOWNLOAD _HRESULT_TYPEDEF_(0x80240051)
#define WU_E_IDLESHUTDOWN_OPCOUNT_INSTALL _HRESULT_TYPEDEF_(0x80240052)
#define WU_E_IDLESHUTDOWN_OPCOUNT_OTHER  _HRESULT_TYPEDEF_(0x80240053)
#define WU_E_INTERACTIVE_CALL_CANCELLED  _HRESULT_TYPEDEF_(0x80240054)
#define WU_E_AU_CALL_CANCELLED _HRESULT_TYPEDEF_(0x80240055)
#define WU_E_SYSTEM_UNSUPPORTED _HRESULT_TYPEDEF_(0x80240056)
#define WU_E_NO_SUCH_HANDLER_PLUGIN _HRESULT_TYPEDEF_(0x80240057)
#define WU_E_INVALID_SERIALIZATION_VERSION _HRESULT_TYPEDEF_(0x80240058)
#define WU_E_NETWORK_COST_EXCEEDS_POLICY _HRESULT_TYPEDEF_(0x80240059)
#define WU_E_CALL_CANCELLED_BY_HIDE _HRESULT_TYPEDEF_(0x8024005A)
#define WU_E_CALL_CANCELLED_BY_INVALID _HRESULT_TYPEDEF_(0x8024005B)
#define WU_E_INVALID_VOLUMEID _HRESULT_TYPEDEF_(0x8024005C)
#define WU_E_UNRECOGNIZED_VOLUMEID _HRESULT_TYPEDEF_(0x8024005D)
#define WU_E_EXTENDEDERROR_NOTSET _HRESULT_TYPEDEF_(0x8024005E)
#define WU_E_EXTENDEDERROR_FAILED _HRESULT_TYPEDEF_(0x8024005F)
#define WU_E_IDLESHUTDOWN_OPCOUNT_SERVICEREGISTRATION _HRESULT_TYPEDEF_(0x80240060)
#define WU_E_FILETRUST_SHA2SIGNATURE_MISSING _HRESULT_TYPEDEF_(0x80240061)
#define WU_E_UPDATE_NOT_APPROVED _HRESULT_TYPEDEF_(0x80240062)
#define WU_E_CALL_CANCELLED_BY_INTERACTIVE_SEARCH _HRESULT_TYPEDEF_(0x80240063)
#define WU_E_INSTALL_JOB_RESUME_NOT_ALLOWED _HRESULT_TYPEDEF_(0x80240064)
#define WU_E_INSTALL_JOB_NOT_SUSPENDED _HRESULT_TYPEDEF_(0x80240065)
#define WU_E_INSTALL_USERCONTEXT_ACCESSDENIED _HRESULT_TYPEDEF_(0x80240066)
#define WU_E_UNEXPECTED _HRESULT_TYPEDEF_(0x80240FFF)
#define WU_E_MSI_WRONG_VERSION _HRESULT_TYPEDEF_(0x80241001)
#define WU_E_MSI_NOT_CONFIGURED _HRESULT_TYPEDEF_(0x80241002)
#define WU_E_MSP_DISABLED _HRESULT_TYPEDEF_(0x80241003)
#define WU_E_MSI_WRONG_APP_CONTEXT _HRESULT_TYPEDEF_(0x80241004)
#define WU_E_MSI_NOT_PRESENT _HRESULT_TYPEDEF_(0x80241005)
#define WU_E_MSP_UNEXPECTED _HRESULT_TYPEDEF_(0x80241FFF)
#define WU_E_PT_SOAPCLIENT_BASE _HRESULT_TYPEDEF_(0x80244000)
#define WU_E_PT_SOAPCLIENT_INITIALIZE _HRESULT_TYPEDEF_(0x80244001)
#define WU_E_PT_SOAPCLIENT_OUTOFMEMORY _HRESULT_TYPEDEF_(0x80244002)
#define WU_E_PT_SOAPCLIENT_GENERATE _HRESULT_TYPEDEF_(0x80244003)
#define WU_E_PT_SOAPCLIENT_CONNECT _HRESULT_TYPEDEF_(0x80244004)
#define WU_E_PT_SOAPCLIENT_SEND _HRESULT_TYPEDEF_(0x80244005)
#define WU_E_PT_SOAPCLIENT_SERVER _HRESULT_TYPEDEF_(0x80244006)
#define WU_E_PT_SOAPCLIENT_SOAPFAULT _HRESULT_TYPEDEF_(0x80244007)
#define WU_E_PT_SOAPCLIENT_PARSEFAULT _HRESULT_TYPEDEF_(0x80244008)
#define WU_E_PT_SOAPCLIENT_READ _HRESULT_TYPEDEF_(0x80244009)
#define WU_E_PT_SOAPCLIENT_PARSE _HRESULT_TYPEDEF_(0x8024400A)
#define WU_E_PT_SOAP_VERSION _HRESULT_TYPEDEF_(0x8024400B)
#define WU_E_PT_SOAP_MUST_UNDERSTAND _HRESULT_TYPEDEF_(0x8024400C)
#define WU_E_PT_SOAP_CLIENT _HRESULT_TYPEDEF_(0x8024400D)
#define WU_E_PT_SOAP_SERVER _HRESULT_TYPEDEF_(0x8024400E)
#define WU_E_PT_WMI_ERROR _HRESULT_TYPEDEF_(0x8024400F)
#define WU_E_PT_EXCEEDED_MAX_SERVER_TRIPS _HRESULT_TYPEDEF_(0x80244010)
#define WU_E_PT_SUS_SERVER_NOT_SET _HRESULT_TYPEDEF_(0x80244011)
#define WU_E_PT_DOUBLE_INITIALIZATION _HRESULT_TYPEDEF_(0x80244012)
#define WU_E_PT_INVALID_COMPUTER_NAME _HRESULT_TYPEDEF_(0x80244013)
#define WU_E_PT_REFRESH_CACHE_REQUIRED _HRESULT_TYPEDEF_(0x80244015)
#define WU_E_PT_HTTP_STATUS_BAD_REQUEST _HRESULT_TYPEDEF_(0x80244016)
#define WU_E_PT_HTTP_STATUS_DENIED _HRESULT_TYPEDEF_(0x80244017)
#define WU_E_PT_HTTP_STATUS_FORBIDDEN _HRESULT_TYPEDEF_(0x80244018)
#define WU_E_PT_HTTP_STATUS_NOT_FOUND _HRESULT_TYPEDEF_(0x80244019)
#define WU_E_PT_HTTP_STATUS_BAD_METHOD _HRESULT_TYPEDEF_(0x8024401A)
#define WU_E_PT_HTTP_STATUS_PROXY_AUTH_REQ _HRESULT_TYPEDEF_(0x8024401B)
#define WU_E_PT_HTTP_STATUS_REQUEST_TIMEOUT _HRESULT_TYPEDEF_(0x8024401C)
#define WU_E_PT_HTTP_STATUS_CONFLICT _HRESULT_TYPEDEF_(0x8024401D)
#define WU_E_PT_HTTP_STATUS_GONE _HRESULT_TYPEDEF_(0x8024401E)
#define WU_E_PT_HTTP_STATUS_SERVER_ERROR _HRESULT_TYPEDEF_(0x8024401F)
#define WU_E_PT_HTTP_STATUS_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x80244020)
#define WU_E_PT_HTTP_STATUS_BAD_GATEWAY _HRESULT_TYPEDEF_(0x80244021)
#define WU_E_PT_HTTP_STATUS_SERVICE_UNAVAIL _HRESULT_TYPEDEF_(0x80244022)
#define WU_E_PT_HTTP_STATUS_GATEWAY_TIMEOUT _HRESULT_TYPEDEF_(0x80244023)
#define WU_E_PT_HTTP_STATUS_VERSION_NOT_SUP _HRESULT_TYPEDEF_(0x80244024)
#define WU_E_PT_FILE_LOCATIONS_CHANGED _HRESULT_TYPEDEF_(0x80244025)
#define WU_E_PT_REGISTRATION_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x80244026)
#define WU_E_PT_NO_AUTH_PLUGINS_REQUESTED _HRESULT_TYPEDEF_(0x80244027)
#define WU_E_PT_NO_AUTH_COOKIES_CREATED _HRESULT_TYPEDEF_(0x80244028)
#define WU_E_PT_INVALID_CONFIG_PROP _HRESULT_TYPEDEF_(0x80244029)
#define WU_E_PT_CONFIG_PROP_MISSING _HRESULT_TYPEDEF_(0x8024402A)
#define WU_E_PT_HTTP_STATUS_NOT_MAPPED _HRESULT_TYPEDEF_(0x8024402B)
#define WU_E_PT_WINHTTP_NAME_NOT_RESOLVED _HRESULT_TYPEDEF_(0x8024402C)
#define WU_E_PT_LOAD_SHEDDING _HRESULT_TYPEDEF_(0x8024402D)
#define WU_E_PT_SAME_REDIR_ID _HRESULT_TYPEDEF_(0x8024502D)
#define WU_E_PT_NO_MANAGED_RECOVER _HRESULT_TYPEDEF_(0x8024502E)
#define WU_E_PT_ECP_SUCCEEDED_WITH_ERRORS _HRESULT_TYPEDEF_(0x8024402F)
#define WU_E_PT_ECP_INIT_FAILED _HRESULT_TYPEDEF_(0x80244030)
#define WU_E_PT_ECP_INVALID_FILE_FORMAT _HRESULT_TYPEDEF_(0x80244031)
#define WU_E_PT_ECP_INVALID_METADATA _HRESULT_TYPEDEF_(0x80244032)
#define WU_E_PT_ECP_FAILURE_TO_EXTRACT_DIGEST _HRESULT_TYPEDEF_(0x80244033)
#define WU_E_PT_ECP_FAILURE_TO_DECOMPRESS_CAB_FILE _HRESULT_TYPEDEF_(0x80244034)
#define WU_E_PT_ECP_FILE_LOCATION_ERROR _HRESULT_TYPEDEF_(0x80244035)
#define WU_E_PT_CATALOG_SYNC_REQUIRED _HRESULT_TYPEDEF_(0x80240436)
#define WU_E_PT_SECURITY_VERIFICATION_FAILURE _HRESULT_TYPEDEF_(0x80240437)
#define WU_E_PT_ENDPOINT_UNREACHABLE _HRESULT_TYPEDEF_(0x80240438)
#define WU_E_PT_INVALID_FORMAT _HRESULT_TYPEDEF_(0x80240439)
#define WU_E_PT_INVALID_URL _HRESULT_TYPEDEF_(0x8024043A)
#define WU_E_PT_NWS_NOT_LOADED _HRESULT_TYPEDEF_(0x8024043B)
#define WU_E_PT_PROXY_AUTH_SCHEME_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x8024043C)
#define WU_E_SERVICEPROP_NOTAVAIL _HRESULT_TYPEDEF_(0x8024043D)
#define WU_E_PT_ENDPOINT_REFRESH_REQUIRED _HRESULT_TYPEDEF_(0x8024043E)
#define WU_E_PT_ENDPOINTURL_NOTAVAIL _HRESULT_TYPEDEF_(0x8024043F)
#define WU_E_PT_ENDPOINT_DISCONNECTED _HRESULT_TYPEDEF_(0x80240440)
#define WU_E_PT_INVALID_OPERATION _HRESULT_TYPEDEF_(0x80240441)
#define WU_E_PT_OBJECT_FAULTED _HRESULT_TYPEDEF_(0x80240442)
#define WU_E_PT_NUMERIC_OVERFLOW _HRESULT_TYPEDEF_(0x80240443)
#define WU_E_PT_OPERATION_ABORTED _HRESULT_TYPEDEF_(0x80240444)
#define WU_E_PT_OPERATION_ABANDONED _HRESULT_TYPEDEF_(0x80240445)
#define WU_E_PT_QUOTA_EXCEEDED _HRESULT_TYPEDEF_(0x80240446)
#define WU_E_PT_NO_TRANSLATION_AVAILABLE _HRESULT_TYPEDEF_(0x80240447)
#define WU_E_PT_ADDRESS_IN_USE _HRESULT_TYPEDEF_(0x80240448)
#define WU_E_PT_ADDRESS_NOT_AVAILABLE _HRESULT_TYPEDEF_(0x80240449)
#define WU_E_PT_OTHER _HRESULT_TYPEDEF_(0x8024044A)
#define WU_E_PT_SECURITY_SYSTEM_FAILURE _HRESULT_TYPEDEF_(0x8024044B)
#define WU_E_PT_UNEXPECTED _HRESULT_TYPEDEF_(0x80244FFF)
#define WU_E_REDIRECTOR_LOAD_XML _HRESULT_TYPEDEF_(0x80245001)
#define WU_E_REDIRECTOR_S_FALSE _HRESULT_TYPEDEF_(0x80245002)
#define WU_E_REDIRECTOR_ID_SMALLER _HRESULT_TYPEDEF_(0x80245003)
#define WU_E_REDIRECTOR_UNKNOWN_SERVICE _HRESULT_TYPEDEF_(0x80245004)
#define WU_E_REDIRECTOR_UNSUPPORTED_CONTENTTYPE _HRESULT_TYPEDEF_(0x80245005)
#define WU_E_REDIRECTOR_INVALID_RESPONSE _HRESULT_TYPEDEF_(0x80245006)
#define WU_E_REDIRECTOR_ATTRPROVIDER_EXCEEDED_MAX_NAMEVALUE _HRESULT_TYPEDEF_(0x80245008)
#define WU_E_REDIRECTOR_ATTRPROVIDER_INVALID_NAME _HRESULT_TYPEDEF_(0x80245009)
#define WU_E_REDIRECTOR_ATTRPROVIDER_INVALID_VALUE _HRESULT_TYPEDEF_(0x8024500A)
#define WU_E_REDIRECTOR_SLS_GENERIC_ERROR _HRESULT_TYPEDEF_(0x8024500B)
#define WU_E_REDIRECTOR_CONNECT_POLICY _HRESULT_TYPEDEF_(0x8024500C)
#define WU_E_REDIRECTOR_ONLINE_DISALLOWED _HRESULT_TYPEDEF_(0x8024500D)
#define WU_E_REDIRECTOR_UNEXPECTED _HRESULT_TYPEDEF_(0x802450FF)
#define WU_E_SIH_VERIFY_DOWNLOAD_ENGINE _HRESULT_TYPEDEF_(0x80245101)
#define WU_E_SIH_VERIFY_DOWNLOAD_PAYLOAD _HRESULT_TYPEDEF_(0x80245102)
#define WU_E_SIH_VERIFY_STAGE_ENGINE _HRESULT_TYPEDEF_(0x80245103)
#define WU_E_SIH_VERIFY_STAGE_PAYLOAD _HRESULT_TYPEDEF_(0x80245104)
#define WU_E_SIH_ACTION_NOT_FOUND _HRESULT_TYPEDEF_(0x80245105)
#define WU_E_SIH_SLS_PARSE _HRESULT_TYPEDEF_(0x80245106)
#define WU_E_SIH_INVALIDHASH _HRESULT_TYPEDEF_(0x80245107)
#define WU_E_SIH_NO_ENGINE _HRESULT_TYPEDEF_(0x80245108)
#define WU_E_SIH_POST_REBOOT_INSTALL_FAILED _HRESULT_TYPEDEF_(0x80245109)
#define WU_E_SIH_POST_REBOOT_NO_CACHED_SLS_RESPONSE _HRESULT_TYPEDEF_(0x8024510A)
#define WU_E_SIH_PARSE _HRESULT_TYPEDEF_(0x8024510B)
#define WU_E_SIH_SECURITY _HRESULT_TYPEDEF_(0x8024510C)
#define WU_E_SIH_PPL _HRESULT_TYPEDEF_(0x8024510D)
#define WU_E_SIH_POLICY _HRESULT_TYPEDEF_(0x8024510E)
#define WU_E_SIH_STDEXCEPTION _HRESULT_TYPEDEF_(0x8024510F)
#define WU_E_SIH_NONSTDEXCEPTION _HRESULT_TYPEDEF_(0x80245110)
#define WU_E_SIH_ENGINE_EXCEPTION _HRESULT_TYPEDEF_(0x80245111)
#define WU_E_SIH_BLOCKED_FOR_PLATFORM _HRESULT_TYPEDEF_(0x80245112)
#define WU_E_SIH_ANOTHER_INSTANCE_RUNNING _HRESULT_TYPEDEF_(0x80245113)
#define WU_E_SIH_DNSRESILIENCY_OFF _HRESULT_TYPEDEF_(0x80245114)
#define WU_E_SIH_UNEXPECTED _HRESULT_TYPEDEF_(0x802451FF)
#define WU_E_DRV_PRUNED _HRESULT_TYPEDEF_(0x8024C001)
#define WU_E_DRV_NOPROP_OR_LEGACY _HRESULT_TYPEDEF_(0x8024C002)
#define WU_E_DRV_REG_MISMATCH _HRESULT_TYPEDEF_(0x8024C003)
#define WU_E_DRV_NO_METADATA _HRESULT_TYPEDEF_(0x8024C004)
#define WU_E_DRV_MISSING_ATTRIBUTE _HRESULT_TYPEDEF_(0x8024C005)
#define WU_E_DRV_SYNC_FAILED _HRESULT_TYPEDEF_(0x8024C006)
#define WU_E_DRV_NO_PRINTER_CONTENT _HRESULT_TYPEDEF_(0x8024C007)
#define WU_E_DRV_DEVICE_PROBLEM _HRESULT_TYPEDEF_(0x8024C008)
#define WU_E_DRV_UNEXPECTED _HRESULT_TYPEDEF_(0x8024CFFF)
#define WU_E_DS_SHUTDOWN _HRESULT_TYPEDEF_(0x80248000)
#define WU_E_DS_INUSE _HRESULT_TYPEDEF_(0x80248001)
#define WU_E_DS_INVALID _HRESULT_TYPEDEF_(0x80248002)
#define WU_E_DS_TABLEMISSING _HRESULT_TYPEDEF_(0x80248003)
#define WU_E_DS_TABLEINCORRECT _HRESULT_TYPEDEF_(0x80248004)
#define WU_E_DS_INVALIDTABLENAME _HRESULT_TYPEDEF_(0x80248005)
#define WU_E_DS_BADVERSION _HRESULT_TYPEDEF_(0x80248006)
#define WU_E_DS_NODATA _HRESULT_TYPEDEF_(0x80248007)
#define WU_E_DS_MISSINGDATA _HRESULT_TYPEDEF_(0x80248008)
#define WU_E_DS_MISSINGREF _HRESULT_TYPEDEF_(0x80248009)
#define WU_E_DS_UNKNOWNHANDLER _HRESULT_TYPEDEF_(0x8024800A)
#define WU_E_DS_CANTDELETE _HRESULT_TYPEDEF_(0x8024800B)
#define WU_E_DS_LOCKTIMEOUTEXPIRED _HRESULT_TYPEDEF_(0x8024800C)
#define WU_E_DS_NOCATEGORIES _HRESULT_TYPEDEF_(0x8024800D)
#define WU_E_DS_ROWEXISTS _HRESULT_TYPEDEF_(0x8024800E)
#define WU_E_DS_STOREFILELOCKED _HRESULT_TYPEDEF_(0x8024800F)
#define WU_E_DS_CANNOTREGISTER _HRESULT_TYPEDEF_(0x80248010)
#define WU_E_DS_UNABLETOSTART _HRESULT_TYPEDEF_(0x80248011)
#define WU_E_DS_DUPLICATEUPDATEID _HRESULT_TYPEDEF_(0x80248013)
#define WU_E_DS_UNKNOWNSERVICE _HRESULT_TYPEDEF_(0x80248014)
#define WU_E_DS_SERVICEEXPIRED _HRESULT_TYPEDEF_(0x80248015)
#define WU_E_DS_DECLINENOTALLOWED _HRESULT_TYPEDEF_(0x80248016)
#define WU_E_DS_TABLESESSIONMISMATCH _HRESULT_TYPEDEF_(0x80248017)
#define WU_E_DS_SESSIONLOCKMISMATCH _HRESULT_TYPEDEF_(0x80248018)
#define WU_E_DS_NEEDWINDOWSSERVICE _HRESULT_TYPEDEF_(0x80248019)
#define WU_E_DS_INVALIDOPERATION _HRESULT_TYPEDEF_(0x8024801A)
#define WU_E_DS_SCHEMAMISMATCH _HRESULT_TYPEDEF_(0x8024801B)
#define WU_E_DS_RESETREQUIRED _HRESULT_TYPEDEF_(0x8024801C)
#define WU_E_DS_IMPERSONATED _HRESULT_TYPEDEF_(0x8024801D)
#define WU_E_DS_DATANOTAVAILABLE _HRESULT_TYPEDEF_(0x8024801E)
#define WU_E_DS_DATANOTLOADED _HRESULT_TYPEDEF_(0x8024801F)
#define WU_E_DS_NODATA_NOSUCHREVISION _HRESULT_TYPEDEF_(0x80248020)
#define WU_E_DS_NODATA_NOSUCHUPDATE _HRESULT_TYPEDEF_(0x80248021)
#define WU_E_DS_NODATA_EULA _HRESULT_TYPEDEF_(0x80248022)
#define WU_E_DS_NODATA_SERVICE _HRESULT_TYPEDEF_(0x80248023)
#define WU_E_DS_NODATA_COOKIE _HRESULT_TYPEDEF_(0x80248024)
#define WU_E_DS_NODATA_TIMER _HRESULT_TYPEDEF_(0x80248025)
#define WU_E_DS_NODATA_CCR _HRESULT_TYPEDEF_(0x80248026)
#define WU_E_DS_NODATA_FILE _HRESULT_TYPEDEF_(0x80248027)
#define WU_E_DS_NODATA_DOWNLOADJOB _HRESULT_TYPEDEF_(0x80248028)
#define WU_E_DS_NODATA_TMI _HRESULT_TYPEDEF_(0x80248029)
#define WU_E_DS_UNEXPECTED _HRESULT_TYPEDEF_(0x80248FFF)
#define WU_E_INVENTORY_PARSEFAILED _HRESULT_TYPEDEF_(0x80249001)
#define WU_E_INVENTORY_GET_INVENTORY_TYPE_FAILED _HRESULT_TYPEDEF_(0x80249002)
#define WU_E_INVENTORY_RESULT_UPLOAD_FAILED _HRESULT_TYPEDEF_(0x80249003)
#define WU_E_INVENTORY_UNEXPECTED _HRESULT_TYPEDEF_(0x80249004)
#define WU_E_INVENTORY_WMI_ERROR _HRESULT_TYPEDEF_(0x80249005)
#define WU_E_AU_NOSERVICE _HRESULT_TYPEDEF_(0x8024A000)
#define WU_E_AU_NONLEGACYSERVER _HRESULT_TYPEDEF_(0x8024A002)
#define WU_E_AU_LEGACYCLIENTDISABLED _HRESULT_TYPEDEF_(0x8024A003)
#define WU_E_AU_PAUSED _HRESULT_TYPEDEF_(0x8024A004)
#define WU_E_AU_NO_REGISTERED_SERVICE _HRESULT_TYPEDEF_(0x8024A005)
#define WU_E_AU_DETECT_SVCID_MISMATCH _HRESULT_TYPEDEF_(0x8024A006)
#define WU_E_REBOOT_IN_PROGRESS _HRESULT_TYPEDEF_(0x8024A007)
#define WU_E_AU_OOBE_IN_PROGRESS _HRESULT_TYPEDEF_(0x8024A008)
#define WU_E_AU_UNEXPECTED _HRESULT_TYPEDEF_(0x8024AFFF)
#define WU_E_UH_REMOTEUNAVAILABLE _HRESULT_TYPEDEF_(0x80242000)
#define WU_E_UH_LOCALONLY _HRESULT_TYPEDEF_(0x80242001)
#define WU_E_UH_UNKNOWNHANDLER _HRESULT_TYPEDEF_(0x80242002)
#define WU_E_UH_REMOTEALREADYACTIVE _HRESULT_TYPEDEF_(0x80242003)
#define WU_E_UH_DOESNOTSUPPORTACTION _HRESULT_TYPEDEF_(0x80242004)
#define WU_E_UH_WRONGHANDLER _HRESULT_TYPEDEF_(0x80242005)
#define WU_E_UH_INVALIDMETADATA _HRESULT_TYPEDEF_(0x80242006)
#define WU_E_UH_INSTALLERHUNG _HRESULT_TYPEDEF_(0x80242007)
#define WU_E_UH_OPERATIONCANCELLED _HRESULT_TYPEDEF_(0x80242008)
#define WU_E_UH_BADHANDLERXML _HRESULT_TYPEDEF_(0x80242009)
#define WU_E_UH_CANREQUIREINPUT _HRESULT_TYPEDEF_(0x8024200A)
#define WU_E_UH_INSTALLERFAILURE _HRESULT_TYPEDEF_(0x8024200B)
#define WU_E_UH_FALLBACKTOSELFCONTAINED _HRESULT_TYPEDEF_(0x8024200C)
#define WU_E_UH_NEEDANOTHERDOWNLOAD _HRESULT_TYPEDEF_(0x8024200D)
#define WU_E_UH_NOTIFYFAILURE _HRESULT_TYPEDEF_(0x8024200E)
#define WU_E_UH_INCONSISTENT_FILE_NAMES _HRESULT_TYPEDEF_(0x8024200F)
#define WU_E_UH_FALLBACKERROR _HRESULT_TYPEDEF_(0x80242010)
#define WU_E_UH_TOOMANYDOWNLOADREQUESTS _HRESULT_TYPEDEF_(0x80242011)
#define WU_E_UH_UNEXPECTEDCBSRESPONSE _HRESULT_TYPEDEF_(0x80242012)
#define WU_E_UH_BADCBSPACKAGEID _HRESULT_TYPEDEF_(0x80242013)
#define WU_E_UH_POSTREBOOTSTILLPENDING _HRESULT_TYPEDEF_(0x80242014)
#define WU_E_UH_POSTREBOOTRESULTUNKNOWN _HRESULT_TYPEDEF_(0x80242015)
#define WU_E_UH_POSTREBOOTUNEXPECTEDSTATE _HRESULT_TYPEDEF_(0x80242016)
#define WU_E_UH_NEW_SERVICING_STACK_REQUIRED _HRESULT_TYPEDEF_(0x80242017)
#define WU_E_UH_CALLED_BACK_FAILURE _HRESULT_TYPEDEF_(0x80242018)
#define WU_E_UH_CUSTOMINSTALLER_INVALID_SIGNATURE _HRESULT_TYPEDEF_(0x80242019)
#define WU_E_UH_UNSUPPORTED_INSTALLCONTEXT _HRESULT_TYPEDEF_(0x8024201A)
#define WU_E_UH_INVALID_TARGETSESSION _HRESULT_TYPEDEF_(0x8024201B)
#define WU_E_UH_DECRYPTFAILURE _HRESULT_TYPEDEF_(0x8024201C)
#define WU_E_UH_HANDLER_DISABLEDUNTILREBOOT _HRESULT_TYPEDEF_(0x8024201D)
#define WU_E_UH_APPX_NOT_PRESENT _HRESULT_TYPEDEF_(0x8024201E)
#define WU_E_UH_NOTREADYTOCOMMIT _HRESULT_TYPEDEF_(0x8024201F)
#define WU_E_UH_APPX_INVALID_PACKAGE_VOLUME _HRESULT_TYPEDEF_(0x80242020)
#define WU_E_UH_APPX_DEFAULT_PACKAGE_VOLUME_UNAVAILABLE _HRESULT_TYPEDEF_(0x80242021)
#define WU_E_UH_APPX_INSTALLED_PACKAGE_VOLUME_UNAVAILABLE _HRESULT_TYPEDEF_(0x80242022)
#define WU_E_UH_APPX_PACKAGE_FAMILY_NOT_FOUND _HRESULT_TYPEDEF_(0x80242023)
#define WU_E_UH_APPX_SYSTEM_VOLUME_NOT_FOUND _HRESULT_TYPEDEF_(0x80242024)
#define WU_E_UH_UNEXPECTED _HRESULT_TYPEDEF_(0x80242FFF)
#define WU_E_DM_URLNOTAVAILABLE _HRESULT_TYPEDEF_(0x80246001)
#define WU_E_DM_INCORRECTFILEHASH _HRESULT_TYPEDEF_(0x80246002)
#define WU_E_DM_UNKNOWNALGORITHM _HRESULT_TYPEDEF_(0x80246003)
#define WU_E_DM_NEEDDOWNLOADREQUEST _HRESULT_TYPEDEF_(0x80246004)
#define WU_E_DM_NONETWORK _HRESULT_TYPEDEF_(0x80246005)
#define WU_E_DM_WRONGBITSVERSION _HRESULT_TYPEDEF_(0x80246006)
#define WU_E_DM_NOTDOWNLOADED _HRESULT_TYPEDEF_(0x80246007)
#define WU_E_DM_FAILTOCONNECTTOBITS _HRESULT_TYPEDEF_(0x80246008)
#define WU_E_DM_BITSTRANSFERERROR _HRESULT_TYPEDEF_(0x80246009)
#define WU_E_DM_DOWNLOADLOCATIONCHANGED _HRESULT_TYPEDEF_(0x8024600A)
#define WU_E_DM_CONTENTCHANGED _HRESULT_TYPEDEF_(0x8024600B)
#define WU_E_DM_DOWNLOADLIMITEDBYUPDATESIZE _HRESULT_TYPEDEF_(0x8024600C)
#define WU_E_DM_UNAUTHORIZED _HRESULT_TYPEDEF_(0x8024600E)
#define WU_E_DM_BG_ERROR_TOKEN_REQUIRED _HRESULT_TYPEDEF_(0x8024600F)
#define WU_E_DM_DOWNLOADSANDBOXNOTFOUND _HRESULT_TYPEDEF_(0x80246010)
#define WU_E_DM_DOWNLOADFILEPATHUNKNOWN _HRESULT_TYPEDEF_(0x80246011)
#define WU_E_DM_DOWNLOADFILEMISSING _HRESULT_TYPEDEF_(0x80246012)
#define WU_E_DM_UPDATEREMOVED _HRESULT_TYPEDEF_(0x80246013)
#define WU_E_DM_READRANGEFAILED _HRESULT_TYPEDEF_(0x80246014)
#define WU_E_DM_UNAUTHORIZED_NO_USER _HRESULT_TYPEDEF_(0x80246016)
#define WU_E_DM_UNAUTHORIZED_LOCAL_USER _HRESULT_TYPEDEF_(0x80246017)
#define WU_E_DM_UNAUTHORIZED_DOMAIN_USER _HRESULT_TYPEDEF_(0x80246018)
#define WU_E_DM_UNAUTHORIZED_MSA_USER _HRESULT_TYPEDEF_(0x80246019)
#define WU_E_DM_FALLINGBACKTOBITS _HRESULT_TYPEDEF_(0x8024601A)
#define WU_E_DM_DOWNLOAD_VOLUME_CONFLICT _HRESULT_TYPEDEF_(0x8024601B)
#define WU_E_DM_SANDBOX_HASH_MISMATCH _HRESULT_TYPEDEF_(0x8024601C)
#define WU_E_DM_HARDRESERVEID_CONFLICT _HRESULT_TYPEDEF_(0x8024601D)
#define WU_E_DM_DOSVC_REQUIRED _HRESULT_TYPEDEF_(0x8024601E)
#define WU_E_DM_UNEXPECTED _HRESULT_TYPEDEF_(0x80246FFF)
#define WU_E_SETUP_INVALID_INFDATA _HRESULT_TYPEDEF_(0x8024D001)
#define WU_E_SETUP_INVALID_IDENTDATA _HRESULT_TYPEDEF_(0x8024D002)
#define WU_E_SETUP_ALREADY_INITIALIZED _HRESULT_TYPEDEF_(0x8024D003)
#define WU_E_SETUP_NOT_INITIALIZED _HRESULT_TYPEDEF_(0x8024D004)
#define WU_E_SETUP_SOURCE_VERSION_MISMATCH _HRESULT_TYPEDEF_(0x8024D005)
#define WU_E_SETUP_TARGET_VERSION_GREATER _HRESULT_TYPEDEF_(0x8024D006)
#define WU_E_SETUP_REGISTRATION_FAILED _HRESULT_TYPEDEF_(0x8024D007)
#define WU_E_SELFUPDATE_SKIP_ON_FAILURE _HRESULT_TYPEDEF_(0x8024D008)
#define WU_E_SETUP_SKIP_UPDATE _HRESULT_TYPEDEF_(0x8024D009)
#define WU_E_SETUP_UNSUPPORTED_CONFIGURATION _HRESULT_TYPEDEF_(0x8024D00A)
#define WU_E_SETUP_BLOCKED_CONFIGURATION _HRESULT_TYPEDEF_(0x8024D00B)
#define WU_E_SETUP_REBOOT_TO_FIX _HRESULT_TYPEDEF_(0x8024D00C)
#define WU_E_SETUP_ALREADYRUNNING _HRESULT_TYPEDEF_(0x8024D00D)
#define WU_E_SETUP_REBOOTREQUIRED _HRESULT_TYPEDEF_(0x8024D00E)
#define WU_E_SETUP_HANDLER_EXEC_FAILURE _HRESULT_TYPEDEF_(0x8024D00F)
#define WU_E_SETUP_INVALID_REGISTRY_DATA _HRESULT_TYPEDEF_(0x8024D010)
#define WU_E_SELFUPDATE_REQUIRED _HRESULT_TYPEDEF_(0x8024D011)
#define WU_E_SELFUPDATE_REQUIRED_ADMIN _HRESULT_TYPEDEF_(0x8024D012)
#define WU_E_SETUP_WRONG_SERVER_VERSION _HRESULT_TYPEDEF_(0x8024D013)
#define WU_E_SETUP_DEFERRABLE_REBOOT_PENDING _HRESULT_TYPEDEF_(0x8024D014)
#define WU_E_SETUP_NON_DEFERRABLE_REBOOT_PENDING _HRESULT_TYPEDEF_(0x8024D015)
#define WU_E_SETUP_FAIL _HRESULT_TYPEDEF_(0x8024D016)
#define WU_E_SETUP_UNEXPECTED _HRESULT_TYPEDEF_(0x8024DFFF)
#define WU_E_EE_UNKNOWN_EXPRESSION _HRESULT_TYPEDEF_(0x8024E001)
#define WU_E_EE_INVALID_EXPRESSION _HRESULT_TYPEDEF_(0x8024E002)
#define WU_E_EE_MISSING_METADATA _HRESULT_TYPEDEF_(0x8024E003)
#define WU_E_EE_INVALID_VERSION _HRESULT_TYPEDEF_(0x8024E004)
#define WU_E_EE_NOT_INITIALIZED _HRESULT_TYPEDEF_(0x8024E005)
#define WU_E_EE_INVALID_ATTRIBUTEDATA _HRESULT_TYPEDEF_(0x8024E006)
#define WU_E_EE_CLUSTER_ERROR _HRESULT_TYPEDEF_(0x8024E007)
#define WU_E_EE_UNEXPECTED _HRESULT_TYPEDEF_(0x8024EFFF)
#define WU_E_INSTALLATION_RESULTS_UNKNOWN_VERSION _HRESULT_TYPEDEF_(0x80243001)
#define WU_E_INSTALLATION_RESULTS_INVALID_DATA _HRESULT_TYPEDEF_(0x80243002)
#define WU_E_INSTALLATION_RESULTS_NOT_FOUND _HRESULT_TYPEDEF_(0x80243003)
#define WU_E_TRAYICON_FAILURE _HRESULT_TYPEDEF_(0x80243004)
#define WU_E_NON_UI_MODE _HRESULT_TYPEDEF_(0x80243FFD)
#define WU_E_WUCLTUI_UNSUPPORTED_VERSION _HRESULT_TYPEDEF_(0x80243FFE)
#define WU_E_AUCLIENT_UNEXPECTED _HRESULT_TYPEDEF_(0x80243FFF)
#define WU_E_REPORTER_EVENTCACHECORRUPT _HRESULT_TYPEDEF_(0x8024F001)
#define WU_E_REPORTER_EVENTNAMESPACEPARSEFAILED _HRESULT_TYPEDEF_(0x8024F002)
#define WU_E_INVALID_EVENT _HRESULT_TYPEDEF_(0x8024F003)
#define WU_E_SERVER_BUSY _HRESULT_TYPEDEF_(0x8024F004)
#define WU_E_CALLBACK_COOKIE_NOT_FOUND _HRESULT_TYPEDEF_(0x8024F005)
#define WU_E_REPORTER_UNEXPECTED _HRESULT_TYPEDEF_(0x8024FFFF)
#define WU_E_OL_INVALID_SCANFILE _HRESULT_TYPEDEF_(0x80247001)
#define WU_E_OL_NEWCLIENT_REQUIRED _HRESULT_TYPEDEF_(0x80247002)
#define WU_E_INVALID_EVENT_PAYLOAD _HRESULT_TYPEDEF_(0x80247003)
#define WU_E_INVALID_EVENT_PAYLOADSIZE _HRESULT_TYPEDEF_(0x80247004)
#define WU_E_SERVICE_NOT_REGISTERED _HRESULT_TYPEDEF_(0x80247005)
#define WU_E_OL_UNEXPECTED _HRESULT_TYPEDEF_(0x80247FFF)
#define WU_E_METADATA_NOOP _HRESULT_TYPEDEF_(0x80247100)
#define WU_E_METADATA_CONFIG_INVALID_BINARY_ENCODING _HRESULT_TYPEDEF_(0x80247101)
#define WU_E_METADATA_FETCH_CONFIG _HRESULT_TYPEDEF_(0x80247102)
#define WU_E_METADATA_INVALID_PARAMETER _HRESULT_TYPEDEF_(0x80247104)
#define WU_E_METADATA_UNEXPECTED _HRESULT_TYPEDEF_(0x80247105)
#define WU_E_METADATA_NO_VERIFICATION_DATA _HRESULT_TYPEDEF_(0x80247106)
#define WU_E_METADATA_BAD_FRAGMENTSIGNING_CONFIG _HRESULT_TYPEDEF_(0x80247107)
#define WU_E_METADATA_FAILURE_PROCESSING_FRAGMENTSIGNING_CONFIG _HRESULT_TYPEDEF_(0x80247108)
#define WU_E_METADATA_XML_MISSING _HRESULT_TYPEDEF_(0x80247120)
#define WU_E_METADATA_XML_FRAGMENTSIGNING_MISSING _HRESULT_TYPEDEF_(0x80247121)
#define WU_E_METADATA_XML_MODE_MISSING _HRESULT_TYPEDEF_(0x80247122)
#define WU_E_METADATA_XML_MODE_INVALID _HRESULT_TYPEDEF_(0x80247123)
#define WU_E_METADATA_XML_VALIDITY_INVALID _HRESULT_TYPEDEF_(0x80247124)
#define WU_E_METADATA_XML_LEAFCERT_MISSING _HRESULT_TYPEDEF_(0x80247125)
#define WU_E_METADATA_XML_INTERMEDIATECERT_MISSING _HRESULT_TYPEDEF_(0x80247126)
#define WU_E_METADATA_XML_LEAFCERT_ID_MISSING _HRESULT_TYPEDEF_(0x80247127)
#define WU_E_METADATA_XML_BASE64CERDATA_MISSING _HRESULT_TYPEDEF_(0x80247128)
#define WU_E_METADATA_BAD_SIGNATURE _HRESULT_TYPEDEF_(0x80247140)
#define WU_E_METADATA_UNSUPPORTED_HASH_ALG _HRESULT_TYPEDEF_(0x80247141)
#define WU_E_METADATA_SIGNATURE_VERIFY_FAILED _HRESULT_TYPEDEF_(0x80247142)
#define WU_E_METADATATRUST_CERTIFICATECHAIN_VERIFICATION _HRESULT_TYPEDEF_(0x80247150)
#define WU_E_METADATATRUST_UNTRUSTED_CERTIFICATECHAIN _HRESULT_TYPEDEF_(0x80247151)
#define WU_E_METADATA_TIMESTAMP_TOKEN_MISSING _HRESULT_TYPEDEF_(0x80247160)
#define WU_E_METADATA_TIMESTAMP_TOKEN_VERIFICATION_FAILED _HRESULT_TYPEDEF_(0x80247161)
#define WU_E_METADATA_TIMESTAMP_TOKEN_UNTRUSTED _HRESULT_TYPEDEF_(0x80247162)
#define WU_E_METADATA_TIMESTAMP_TOKEN_VALIDITY_WINDOW _HRESULT_TYPEDEF_(0x80247163)
#define WU_E_METADATA_TIMESTAMP_TOKEN_SIGNATURE _HRESULT_TYPEDEF_(0x80247164)
#define WU_E_METADATA_TIMESTAMP_TOKEN_CERTCHAIN _HRESULT_TYPEDEF_(0x80247165)
#define WU_E_METADATA_TIMESTAMP_TOKEN_REFRESHONLINE _HRESULT_TYPEDEF_(0x80247166)
#define WU_E_METADATA_TIMESTAMP_TOKEN_ALL_BAD _HRESULT_TYPEDEF_(0x80247167)
#define WU_E_METADATA_TIMESTAMP_TOKEN_NODATA _HRESULT_TYPEDEF_(0x80247168)
#define WU_E_METADATA_TIMESTAMP_TOKEN_CACHELOOKUP _HRESULT_TYPEDEF_(0x80247169)
#define WU_E_METADATA_TIMESTAMP_TOKEN_VALIDITYWINDOW_UNEXPECTED _HRESULT_TYPEDEF_(0x8024717E)
#define WU_E_METADATA_TIMESTAMP_TOKEN_UNEXPECTED _HRESULT_TYPEDEF_(0x8024717F)
#define WU_E_METADATA_CERT_MISSING _HRESULT_TYPEDEF_(0x80247180)
#define WU_E_METADATA_LEAFCERT_BAD_TRANSPORT_ENCODING _HRESULT_TYPEDEF_(0x80247181)
#define WU_E_METADATA_INTCERT_BAD_TRANSPORT_ENCODING _HRESULT_TYPEDEF_(0x80247182)
#define WU_E_METADATA_CERT_UNTRUSTED _HRESULT_TYPEDEF_(0x80247183)
#define WU_E_WUTASK_INPROGRESS _HRESULT_TYPEDEF_(0x8024B001)
#define WU_E_WUTASK_STATUS_DISABLED _HRESULT_TYPEDEF_(0x8024B002)
#define WU_E_WUTASK_NOT_STARTED _HRESULT_TYPEDEF_(0x8024B003)
#define WU_E_WUTASK_RETRY _HRESULT_TYPEDEF_(0x8024B004)
#define WU_E_WUTASK_CANCELINSTALL_DISALLOWED _HRESULT_TYPEDEF_(0x8024B005)
#define WU_E_UNKNOWN_HARDWARECAPABILITY _HRESULT_TYPEDEF_(0x8024B101)
#define WU_E_BAD_XML_HARDWARECAPABILITY _HRESULT_TYPEDEF_(0x8024B102)
#define WU_E_WMI_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x8024B103)
#define WU_E_UPDATE_MERGE_NOT_ALLOWED _HRESULT_TYPEDEF_(0x8024B104)
#define WU_E_SKIPPED_UPDATE_INSTALLATION _HRESULT_TYPEDEF_(0x8024B105)
#define WU_E_SLS_INVALID_REVISION _HRESULT_TYPEDEF_(0x8024B201)
#define WU_E_FILETRUST_DUALSIGNATURE_RSA _HRESULT_TYPEDEF_(0x8024B301)
#define WU_E_FILETRUST_DUALSIGNATURE_ECC _HRESULT_TYPEDEF_(0x8024B302)
#define WU_E_TRUST_SUBJECT_NOT_TRUSTED _HRESULT_TYPEDEF_(0x8024B303)
#define WU_E_TRUST_PROVIDER_UNKNOWN _HRESULT_TYPEDEF_(0x8024B304)

#endif /* _WUERROR_ */
