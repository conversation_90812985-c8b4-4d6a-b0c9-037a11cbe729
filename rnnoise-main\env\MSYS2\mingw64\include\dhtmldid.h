/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef __DHTMLDID_H_
#define __DHTMLDID_H_

#define DISPID_LOADDOCUMENT 1
#define DISPID_EXECCOMMAND 2
#define DISPID_QUERYSTATUS 3
#define DISPID_SAVEDOCUMENT 4
#define DISPID_SETCONTEXTMENU 5
#define DISPID_DOCUMENT 6
#define DISPID_ACTIVATEAPPLETS 7
#define DISPID_ACTIVATEACTIVEXCONTROLS 8
#define DISPID_ACTIVATEDTCS 9
#define DISPID_SHOWDETAILS 11
#define DISPID_SHOWBORDERS 12
#define DISPID_DHTMLEDITAPPEARANCE 13
#define DISPID_DHTMLEDITSCROLLBARS 14
#define DISPID_SCROLLBARAPPEARANCE 15
#define DISPID_SOURCECODEPRESERVATION 16
#define DISPID_DOCUMENTHTML 17
#define DISPID_ABSOLUTEDROPMODE 18
#define DISPID_SNAPTOGRIDX 19
#define DISPID_SNAPTOGRIDY 20
#define DISPID_SNAPTOGRID 21
#define DISPID_ISDIRTY 22
#define DISPID_CURRENTDOCUMENTPATH 23
#define DISPID_BASEURL 24
#define DISPID_DOCUMENTTITLE 25
#define DISPID_BROWSEMODE 26
#define DISPID_NEWDOCUMENT 27
#define DISPID_PRINT 28
#define DISPID_LOADURL 29
#define DISPID_USEDIVONCR 30
#define DISPID_FILTERSRCCODE 31
#define DISPID_REFRESHDOC 32
#define DISPID_BUSY 33

#define DISPID_DOCUMENTCOMPLETE 1
#define DISPID_DISPLAYCHANGED 2
#define DISPID_SHOWCONTEXTMENU 3
#define DISPID_CONTEXTMENUACTION 4
#define DISPID_ONMOUSEDOWN 5
#define DISPID_ONMOUSEMOVE 6
#define DISPID_ONMOUSEUP 7
#define DISPID_ONMOUSEOUT 8
#define DISPID_ONMOUSEOVER 9
#define DISPID_ONCLICK 10
#define DISPID_ONDBLCLICK 11
#define DISPID_ONKEYDOWN 12
#define DISPID_ONKEYPRESS 13
#define DISPID_ONKEYUP 14
#define DISPID_ONBLUR 15
#define DISPID_ONREADYSTATECHANGE 16

#define DISPID_TABLEPARAMS_NUMROWS 1
#define DISPID_TABLEPARAMS_NUMCOLS 2
#define DISPID_TABLEPARAMS_TABLEATTRS 3
#define DISPID_TABLEPARAMS_CELLATTRS 4
#define DISPID_TABLEPARAMS_CAPTION 5

#define DISPID_NAMESPARAM_NAMES 1

#endif
