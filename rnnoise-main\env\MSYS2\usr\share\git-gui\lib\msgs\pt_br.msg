set ::msgcat::header "Project-Id-Version: git-gui\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2010-09-18 11:09-0300\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Brazilian Portuguese <>\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset pt_br "git-gui: fatal error" "git-gui: erro fatal"
::msgcat::mcset pt_br "Invalid font specified in %s:" "Fonte inv\u00e1lida indicada em %s:"
::msgcat::mcset pt_br "Main Font" "Fonte principal"
::msgcat::mcset pt_br "Diff/Console Font" "Fonte para o diff/console"
::msgcat::mcset pt_br "Cannot find git in PATH." "Imposs\u00edvel encontrar o git no \"PATH\""
::msgcat::mcset pt_br "Cannot parse Git version string:" "Imposs\u00edvel interpretar a vers\u00e3o do git:"
::msgcat::mcset pt_br "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "N\u00e3o foi poss\u00edvel determinar a vers\u00e3o do git:\n\n%s afirmar que sua vers\u00e3o \u00e9 \"%s\".\n\n%s exige o Git 1.5.0 ou posterior.\n\nAssumir que '%s' \u00e9 a vers\u00e3o 1.5.0?\n"
::msgcat::mcset pt_br "Git directory not found:" "Diret\u00f3rio do Git n\u00e3o encontrado:"
::msgcat::mcset pt_br "Cannot move to top of working directory:" "Imposs\u00edvel mover para o in\u00edcio do diret\u00f3rio de trabalho:"
::msgcat::mcset pt_br "Cannot use bare repository:" "Imposs\u00edvel usar reposit\u00f3rio puro:"
::msgcat::mcset pt_br "No working directory" "Sem diret\u00f3rio de trabalho"
::msgcat::mcset pt_br "Refreshing file status..." "Atualizando estado dos arquivos..."
::msgcat::mcset pt_br "Scanning for modified files ..." "Procurando por arquivos modificados ..."
::msgcat::mcset pt_br "Calling prepare-commit-msg hook..." "Executando hook \"prepare-commit-msg\"..."
::msgcat::mcset pt_br "Commit declined by prepare-commit-msg hook." "O script \"prepare-commit-msg\" negou a cria\u00e7\u00e3o de uma nova revis\u00e3o"
::msgcat::mcset pt_br "Ready." "Pronto."
::msgcat::mcset pt_br "Displaying only %s of %s files." "Exibindo apenas %s de %s arquivos."
::msgcat::mcset pt_br "Unmodified" "N\u00e3o modificado"
::msgcat::mcset pt_br "Modified, not staged" "Modificado, n\u00e3o marcado"
::msgcat::mcset pt_br "Staged for commit" "Marcado para uma nova revis\u00e3o"
::msgcat::mcset pt_br "Portions staged for commit" "Trechos marcados para revis\u00e3o"
::msgcat::mcset pt_br "Staged for commit, missing" "Marcado para revis\u00e3o, faltando"
::msgcat::mcset pt_br "File type changed, not staged" "Tipo do arquivo modificado, n\u00e3o marcado"
::msgcat::mcset pt_br "File type changed, staged" "Tipo do arquivo modificado, marcado"
::msgcat::mcset pt_br "Untracked, not staged" "N\u00e3o monitorado, n\u00e3o marcado"
::msgcat::mcset pt_br "Missing" "Faltando"
::msgcat::mcset pt_br "Staged for removal" "Marcado para remo\u00e7\u00e3o"
::msgcat::mcset pt_br "Staged for removal, still present" "Marcado para remo\u00e7\u00e3o, ainda presente"
::msgcat::mcset pt_br "Requires merge resolution" "Requer resolu\u00e7\u00e3o de conflitos"
::msgcat::mcset pt_br "Starting gitk... please wait..." "Iniciando gitk... Aguarde..."
::msgcat::mcset pt_br "Couldn't find gitk in PATH" "Imposs\u00edvel encontrar o gitk no PATH"
::msgcat::mcset pt_br "Couldn't find git gui in PATH" "Imposs\u00edvel encontrar o \"git gui\" no PATH"
::msgcat::mcset pt_br "Repository" "Reposit\u00f3rio"
::msgcat::mcset pt_br "Edit" "Editar"
::msgcat::mcset pt_br "Branch" "Ramo"
::msgcat::mcset pt_br "Commit@@noun" "Revis\u00e3o"
::msgcat::mcset pt_br "Merge" "Mesclar"
::msgcat::mcset pt_br "Remote" "Remoto"
::msgcat::mcset pt_br "Tools" "Ferramentas"
::msgcat::mcset pt_br "Explore Working Copy" "Explorar c\u00f3pia de trabalho"
::msgcat::mcset pt_br "Browse Current Branch's Files" "Explorar arquivos do ramo atual"
::msgcat::mcset pt_br "Browse Branch Files..." "Explorar arquivos do ramo..."
::msgcat::mcset pt_br "Visualize Current Branch's History" "Visualizar hist\u00f3rico do ramo atual"
::msgcat::mcset pt_br "Visualize All Branch History" "Visualizar hist\u00f3rico de todos os ramos"
::msgcat::mcset pt_br "Browse %s's Files" "Explorar arquivos de %s"
::msgcat::mcset pt_br "Visualize %s's History" "Visualizar hist\u00f3rico de %s"
::msgcat::mcset pt_br "Database Statistics" "Estat\u00edsticas do banco de dados"
::msgcat::mcset pt_br "Compress Database" "Compactar banco de dados"
::msgcat::mcset pt_br "Verify Database" "Verificar banco de dados"
::msgcat::mcset pt_br "Create Desktop Icon" "Criar \u00edcone na \u00e1rea de trabalho"
::msgcat::mcset pt_br "Quit" "Sair"
::msgcat::mcset pt_br "Undo" "Desfazer"
::msgcat::mcset pt_br "Redo" "Refazer"
::msgcat::mcset pt_br "Cut" "Recortar"
::msgcat::mcset pt_br "Copy" "Copiar"
::msgcat::mcset pt_br "Paste" "Colar"
::msgcat::mcset pt_br "Delete" "Apagar"
::msgcat::mcset pt_br "Select All" "Selecionar tudo"
::msgcat::mcset pt_br "Create..." "Criar..."
::msgcat::mcset pt_br "Checkout..." "Checkout..."
::msgcat::mcset pt_br "Rename..." "Renomear..."
::msgcat::mcset pt_br "Delete..." "Apagar..."
::msgcat::mcset pt_br "Reset..." "Redefinir..."
::msgcat::mcset pt_br "Done" "Pronto"
::msgcat::mcset pt_br "Commit@@verb" "Salvar revis\u00e3o"
::msgcat::mcset pt_br "New Commit" "Nova revis\u00e3o"
::msgcat::mcset pt_br "Amend Last Commit" "Corrigir \u00faltima revis\u00e3o"
::msgcat::mcset pt_br "Rescan" "Atualizar"
::msgcat::mcset pt_br "Stage To Commit" "Marcar para revis\u00e3o"
::msgcat::mcset pt_br "Stage Changed Files To Commit" "Marcar arquivos modificados"
::msgcat::mcset pt_br "Unstage From Commit" "Desmarcar"
::msgcat::mcset pt_br "Revert Changes" "Reverter mudan\u00e7as"
::msgcat::mcset pt_br "Show Less Context" "Mostrar menos contexto"
::msgcat::mcset pt_br "Show More Context" "Mostrar mais contexto"
::msgcat::mcset pt_br "Sign Off" "Assinar embaixo"
::msgcat::mcset pt_br "Local Merge..." "Mesclar localmente..."
::msgcat::mcset pt_br "Abort Merge..." "Abortar mesclagem..."
::msgcat::mcset pt_br "Add..." "Adicionar..."
::msgcat::mcset pt_br "Push..." "Enviar..."
::msgcat::mcset pt_br "Delete Branch..." "Apagar ramo..."
::msgcat::mcset pt_br "Options..." "Op\u00e7\u00f5es..."
::msgcat::mcset pt_br "Remove..." "Remover..."
::msgcat::mcset pt_br "Help" "Ajuda"
::msgcat::mcset pt_br "About %s" "Sobre o %s"
::msgcat::mcset pt_br "Online Documentation" "Ajuda online"
::msgcat::mcset pt_br "Show SSH Key" "Mostrar chave SSH"
::msgcat::mcset pt_br "fatal: cannot stat path %s: No such file or directory" "erro fatal: imposs\u00edvel executar \"stat\" em  %s: Arquivo ou diret\u00f3rio n\u00e3o encontrado"
::msgcat::mcset pt_br "Current Branch:" "Ramo atual:"
::msgcat::mcset pt_br "Staged Changes (Will Commit)" "Mudan\u00e7as marcadas"
::msgcat::mcset pt_br "Unstaged Changes" "Mudan\u00e7as n\u00e3o marcadas"
::msgcat::mcset pt_br "Stage Changed" "Marcar alterados"
::msgcat::mcset pt_br "Push" "Enviar"
::msgcat::mcset pt_br "Initial Commit Message:" "Descri\u00e7\u00e3o da revis\u00e3o inicial:"
::msgcat::mcset pt_br "Amended Commit Message:" "Descri\u00e7\u00e3o da revis\u00e3o corrigida:"
::msgcat::mcset pt_br "Amended Initial Commit Message:" "Descri\u00e7\u00e3o da revis\u00e3o inicial corrigida:"
::msgcat::mcset pt_br "Amended Merge Commit Message:" "Descri\u00e7\u00e3o da revis\u00e3o de mescla corrigida:"
::msgcat::mcset pt_br "Merge Commit Message:" "Descri\u00e7\u00e3o da revis\u00e3o de mescla:"
::msgcat::mcset pt_br "Commit Message:" "Descri\u00e7\u00e3o da revis\u00e3o:"
::msgcat::mcset pt_br "Copy All" "Copiar todos"
::msgcat::mcset pt_br "File:" "Arquivo:"
::msgcat::mcset pt_br "Refresh" "Atualizar"
::msgcat::mcset pt_br "Decrease Font Size" "Reduzir tamanho da fonte"
::msgcat::mcset pt_br "Increase Font Size" "Aumentar tamanho da fonte"
::msgcat::mcset pt_br "Encoding" "Codifica\u00e7\u00e3o"
::msgcat::mcset pt_br "Apply/Reverse Hunk" "Aplicar/reverter trecho"
::msgcat::mcset pt_br "Apply/Reverse Line" "Aplicar/reverter linha"
::msgcat::mcset pt_br "Run Merge Tool" "Executar ferramenta de mescla"
::msgcat::mcset pt_br "Use Remote Version" "Usar vers\u00e3o remota"
::msgcat::mcset pt_br "Use Local Version" "Usar vers\u00e3o local"
::msgcat::mcset pt_br "Revert To Base" "Reverter para a vers\u00e3o-base"
::msgcat::mcset pt_br "Visualize These Changes In The Submodule" "Visualizar estas mudan\u00e7as no sub-m\u00f3dulo"
::msgcat::mcset pt_br "Visualize Current Branch History In The Submodule" "Visualizar hist\u00f3rico do ramo atual no sub-m\u00f3dulo"
::msgcat::mcset pt_br "Visualize All Branch History In The Submodule" "Visualizar hist\u00f3rico de todos os camos no sub-m\u00f3dulo"
::msgcat::mcset pt_br "Start git gui In The Submodule" "Iniciar \"git gui\" no sub-m\u00f3dulo"
::msgcat::mcset pt_br "Unstage Hunk From Commit" "Desmarcar trecho para revis\u00e3o"
::msgcat::mcset pt_br "Unstage Lines From Commit" "Desmarcar linhas para revis\u00e3o"
::msgcat::mcset pt_br "Unstage Line From Commit" "Desmarcar linha para revis\u00e3o"
::msgcat::mcset pt_br "Stage Hunk For Commit" "Marcar trecho para revis\u00e3o"
::msgcat::mcset pt_br "Stage Lines For Commit" "Marcar linhas para revis\u00e3o"
::msgcat::mcset pt_br "Stage Line For Commit" "Marcar linha para revis\u00e3o"
::msgcat::mcset pt_br "Initializing..." "Iniciando..."
::msgcat::mcset pt_br "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "Poss\u00edveis problemas com as vari\u00e1veis de ambiente.\n\nAs seguintes vari\u00e1veis de ambiente provavelmente ser\u00e3o\nignoradas por qualquer sub-processo do Git executado por\n%s:\n"
::msgcat::mcset pt_br "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\nIsto se deve a um problema conhecido com os bin\u00e1rios da Tcl \ndistribu\u00eddos com o Cygwin"
::msgcat::mcset pt_br "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\nUma boa alternativa para %s\n\u00e9 colocar os valores para o nome de usu\u00e1rio e e-mail\nno seu arquivo \"~/.gitconfig\"\n"
::msgcat::mcset pt_br "git-gui - a graphical user interface for Git." "git-gui - uma interface gr\u00e1fica para o Git"
::msgcat::mcset pt_br "File Viewer" "Visualizador de arquivos"
::msgcat::mcset pt_br "Commit:" "Revis\u00e3o:"
::msgcat::mcset pt_br "Copy Commit" "Copiar revis\u00e3o"
::msgcat::mcset pt_br "Find Text..." "Procurar texto..."
::msgcat::mcset pt_br "Do Full Copy Detection" "Executar detec\u00e7\u00e3o completa de c\u00f3pias"
::msgcat::mcset pt_br "Show History Context" "Mostrar contexto do hist\u00f3rico"
::msgcat::mcset pt_br "Blame Parent Commit" "Anotar revis\u00e3o anterior"
::msgcat::mcset pt_br "Reading %s..." "Lendo %s..."
::msgcat::mcset pt_br "Loading copy/move tracking annotations..." "Carregando anota\u00e7\u00f5es de c\u00f3pia/movimenta\u00e7\u00e3o..."
::msgcat::mcset pt_br "lines annotated" "linhas anotadas"
::msgcat::mcset pt_br "Loading original location annotations..." "Carregando anota\u00e7\u00f5es originais..."
::msgcat::mcset pt_br "Annotation complete." "Anota\u00e7\u00e3o completa."
::msgcat::mcset pt_br "Busy" "Ocupado"
::msgcat::mcset pt_br "Annotation process is already running." "O processo de anota\u00e7\u00e3o j\u00e1 est\u00e1 em execu\u00e7\u00e3o"
::msgcat::mcset pt_br "Running thorough copy detection..." "Executando detec\u00e7\u00e3o de c\u00f3pia..."
::msgcat::mcset pt_br "Loading annotation..." "Carregando anota\u00e7\u00f5es..."
::msgcat::mcset pt_br "Author:" "Autor:"
::msgcat::mcset pt_br "Committer:" "Revisor:"
::msgcat::mcset pt_br "Original File:" "Arquivo original:"
::msgcat::mcset pt_br "Cannot find HEAD commit:" "Imposs\u00edvel encontrar revis\u00e3o HEAD:"
::msgcat::mcset pt_br "Cannot find parent commit:" "Imposs\u00edvel encontrar revis\u00e3o anterior:"
::msgcat::mcset pt_br "Unable to display parent" "Imposs\u00edvel exibir revis\u00e3o anterior"
::msgcat::mcset pt_br "Error loading diff:" "Erro ao carregar as diferen\u00e7as:"
::msgcat::mcset pt_br "Originally By:" "Originalmente por:"
::msgcat::mcset pt_br "In File:" "No arquivo:"
::msgcat::mcset pt_br "Copied Or Moved Here By:" "Copiado ou movido para c\u00e1 por:"
::msgcat::mcset pt_br "Checkout Branch" "Efetuar checkout do ramo"
::msgcat::mcset pt_br "Checkout" "Checkout"
::msgcat::mcset pt_br "Cancel" "Cancelar"
::msgcat::mcset pt_br "Revision" "Revis\u00e3o"
::msgcat::mcset pt_br "Options" "Op\u00e7\u00f5es"
::msgcat::mcset pt_br "Fetch Tracking Branch" "Obter ramo de rastreamento"
::msgcat::mcset pt_br "Detach From Local Branch" "Separar do ramo local"
::msgcat::mcset pt_br "Create Branch" "Criar ramo"
::msgcat::mcset pt_br "Create New Branch" "Criar novo ramo"
::msgcat::mcset pt_br "Create" "Criar"
::msgcat::mcset pt_br "Branch Name" "Nome do ramo"
::msgcat::mcset pt_br "Name:" "Nome:"
::msgcat::mcset pt_br "Match Tracking Branch Name" "Coincidir nome do ramo de rastreamento"
::msgcat::mcset pt_br "Starting Revision" "Revis\u00e3o inicial"
::msgcat::mcset pt_br "Update Existing Branch:" "Atualizar ramo existente:"
::msgcat::mcset pt_br "No" "N\u00e3o"
::msgcat::mcset pt_br "Fast Forward Only" "Somente se for um avan\u00e7o r\u00e1pido"
::msgcat::mcset pt_br "Reset" "Redefinir"
::msgcat::mcset pt_br "Checkout After Creation" "Efetuar checkout ap\u00f3s a cria\u00e7\u00e3o"
::msgcat::mcset pt_br "Please select a tracking branch." "Selecione um ramo de rastreamento."
::msgcat::mcset pt_br "Tracking branch %s is not a branch in the remote repository." "O ramo de rastreamento %s n\u00e3o \u00e9 um ramo do reposit\u00f3rio remoto."
::msgcat::mcset pt_br "Please supply a branch name." "Indique um nome para o ramo."
::msgcat::mcset pt_br "'%s' is not an acceptable branch name." "\"%s\" n\u00e3o \u00e9 um nome de ramo v\u00e1lido"
::msgcat::mcset pt_br "Delete Branch" "Apagar ramo"
::msgcat::mcset pt_br "Delete Local Branch" "Apagar ramo local"
::msgcat::mcset pt_br "Local Branches" "Ramos locais"
::msgcat::mcset pt_br "Delete Only If Merged Into" "Apagar somente se mesclado em"
::msgcat::mcset pt_br "Always (Do not perform merge checks)" "For\u00e7ar exclus\u00e3o (n\u00e3o verificar se o ramo foi mesclado)"
::msgcat::mcset pt_br "The following branches are not completely merged into %s:" "Os ramos seguintes n\u00e3o foram completamente mesclados em %s:"
::msgcat::mcset pt_br "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "Recuperar ramos apagados \u00e9 dif\u00edcil.\n\nApagar os ramos selecionados?"
::msgcat::mcset pt_br "Failed to delete branches:\n%s" "Erro ao apagar ramos:\n%s"
::msgcat::mcset pt_br "Rename Branch" "Renomear ramo"
::msgcat::mcset pt_br "Rename" "Renomear"
::msgcat::mcset pt_br "Branch:" "Ramo:"
::msgcat::mcset pt_br "New Name:" "Novo nome:"
::msgcat::mcset pt_br "Please select a branch to rename." "Selecione um ramo para renomear."
::msgcat::mcset pt_br "Branch '%s' already exists." "O ramo \"%s\" j\u00e1 existe."
::msgcat::mcset pt_br "Failed to rename '%s'." "Erro ao renomear \"%s\"."
::msgcat::mcset pt_br "Starting..." "Inciando..."
::msgcat::mcset pt_br "File Browser" "Navegador de arquivos"
::msgcat::mcset pt_br "Loading %s..." "Carregando %s..."
::msgcat::mcset pt_br "\[Up To Parent\]" "\[Subir\]"
::msgcat::mcset pt_br "Browse Branch Files" "Explorar arquivos do ramo"
::msgcat::mcset pt_br "Browse" "Explorar"
::msgcat::mcset pt_br "Fetching %s from %s" "Obtendo %s de %s"
::msgcat::mcset pt_br "fatal: Cannot resolve %s" "Erro fatal: imposs\u00edvel resolver %s"
::msgcat::mcset pt_br "Close" "Fechar"
::msgcat::mcset pt_br "Branch '%s' does not exist." "O ramo \"%s\" n\u00e3o existe."
::msgcat::mcset pt_br "Failed to configure simplified git-pull for '%s'." "Erro ao configurar git-pull simplificado para \"%s\"."
::msgcat::mcset pt_br "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "O ramo \"%s\" j\u00e1 existe.\n\nN\u00e3o \u00e9 poss\u00edvel avan\u00e7\u00e1-lo para %s.\n\u00c9 preciso mesclar."
::msgcat::mcset pt_br "Merge strategy '%s' not supported." "Estrat\u00e9gia de mesclagem \"%s\" n\u00e3o suportada."
::msgcat::mcset pt_br "Failed to update '%s'." "Erro ao atualizar \"%s\"."
::msgcat::mcset pt_br "Staging area (index) is already locked." "A \u00e1rea de marca\u00e7\u00e3o (staging area, index) j\u00e1 est\u00e1 bloqueada."
::msgcat::mcset pt_br "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "O \u00faltimo estado lido n\u00e3o confere com o estado atual.\n\nOutro programa do Git modificou o reposit\u00f3rio desde a \u00faltima leitura. Uma atualiza\u00e7\u00e3o deve ser executada antes de alterar o ramo atual.\n\nA atualiza\u00e7\u00e3o come\u00e7ar\u00e1 automaticamente agora.\n"
::msgcat::mcset pt_br "Updating working directory to '%s'..." "Atualizando diret\u00f3rio de trabalho para \"%s\"..."
::msgcat::mcset pt_br "files checked out" "arquivos retirados"
::msgcat::mcset pt_br "Aborted checkout of '%s' (file level merging is required)." "Checkout de \"%s\" abortado (\u00e9 preciso mesclar alguns arquivos)"
::msgcat::mcset pt_br "File level merge required." "Mesclagem de arquivos necess\u00e1ria."
::msgcat::mcset pt_br "Staying on branch '%s'." "Permanecendo no ramo \"%s\"."
::msgcat::mcset pt_br "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "Voc\u00ea n\u00e3o est\u00e1 mais em um ramo local\n\nSe voc\u00ea deseja um ramo, crie um agora a partir deste checkout."
::msgcat::mcset pt_br "Checked out '%s'." "Checkout de \"%s\" conclu\u00eddo."
::msgcat::mcset pt_br "Resetting '%s' to '%s' will lose the following commits:" "Redefinir \"%s\" para \"%s\" provocar\u00e1 a perda das seguintes revis\u00f5es:"
::msgcat::mcset pt_br "Recovering lost commits may not be easy." "Recuperar revis\u00f5es perdidas pode n\u00e3o ser f\u00e1cil."
::msgcat::mcset pt_br "Reset '%s'?" "Redefinir \"%s\"?"
::msgcat::mcset pt_br "Visualize" "Visualizar"
::msgcat::mcset pt_br "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "Erro ao definir o ramo atual.\n\nEste diret\u00f3rio de trabalho est\u00e1 incompleto. Foi poss\u00edvel atualizar seus arquivos, mas houve uma falha ao atualizar os arquivos internos do Git.\n\nIsto n\u00e3o deveria ter acontecido, %s terminar\u00e1 agora."
::msgcat::mcset pt_br "Select" "Selecionar"
::msgcat::mcset pt_br "Font Family" "Tipo da fonte"
::msgcat::mcset pt_br "Font Size" "Tamanho da fonte"
::msgcat::mcset pt_br "Font Example" "Exemplo"
::msgcat::mcset pt_br "This is example text.\nIf you like this text, it can be your font." "Este \u00e9 um texto de exemplo.\nSe voc\u00ea gostar deste texto, esta pode ser sua fonte."
::msgcat::mcset pt_br "Git Gui" "Git Gui"
::msgcat::mcset pt_br "Create New Repository" "Criar novo reposit\u00f3rio"
::msgcat::mcset pt_br "New..." "Novo..."
::msgcat::mcset pt_br "Clone Existing Repository" "Clonar reposit\u00f3rio existente"
::msgcat::mcset pt_br "Clone..." "Clonar..."
::msgcat::mcset pt_br "Open Existing Repository" "Abrir reposit\u00f3rio existente"
::msgcat::mcset pt_br "Open..." "Abrir..."
::msgcat::mcset pt_br "Recent Repositories" "Reposit\u00f3rios recentes"
::msgcat::mcset pt_br "Open Recent Repository:" "Abrir reposit\u00f3rio recente:"
::msgcat::mcset pt_br "Failed to create repository %s:" "Erro ao criar reposit\u00f3rio %s:"
::msgcat::mcset pt_br "Directory:" "Diret\u00f3rio:"
::msgcat::mcset pt_br "Git Repository" "Reposit\u00f3rio Git"
::msgcat::mcset pt_br "Directory %s already exists." "O diret\u00f3rio %s j\u00e1 existe."
::msgcat::mcset pt_br "File %s already exists." "O arquivo %s j\u00e1 existe."
::msgcat::mcset pt_br "Clone" "Clonar"
::msgcat::mcset pt_br "Source Location:" "Origem:"
::msgcat::mcset pt_br "Target Directory:" "Diret\u00f3rio de destino:"
::msgcat::mcset pt_br "Clone Type:" "Tipo de clonagem:"
::msgcat::mcset pt_br "Standard (Fast, Semi-Redundant, Hardlinks)" "Padr\u00e3o (r\u00e1pida, semi-redundante, com hardlinks)"
::msgcat::mcset pt_br "Full Copy (Slower, Redundant Backup)" "C\u00f3pia completa (mais lenta, backup redundante)"
::msgcat::mcset pt_br "Shared (Fastest, Not Recommended, No Backup)" "Compartilhada (A mais r\u00e1pida, n\u00e3o recomendada, sem backup)"
::msgcat::mcset pt_br "Not a Git repository: %s" "Este n\u00e3o \u00e9 um reposit\u00f3rio do Git: %s"
::msgcat::mcset pt_br "Standard only available for local repository." "Clonagens padr\u00f5es s\u00f3 s\u00e3o poss\u00edveis em reposit\u00f3rios locais."
::msgcat::mcset pt_br "Shared only available for local repository." "Clonagens parciais s\u00f3 s\u00e3o poss\u00edveis em reposit\u00f3rios locais."
::msgcat::mcset pt_br "Location %s already exists." "O local %s j\u00e1 existe."
::msgcat::mcset pt_br "Failed to configure origin" "Erro ao configurar origem"
::msgcat::mcset pt_br "Counting objects" "Contando objetos"
::msgcat::mcset pt_br "buckets" "buckets"
::msgcat::mcset pt_br "Unable to copy objects/info/alternates: %s" "Erro ao copiar objetos ou informa\u00e7\u00f5es adicionais: %s"
::msgcat::mcset pt_br "Nothing to clone from %s." "N\u00e3o h\u00e1 nada para clonar em %s."
::msgcat::mcset pt_br "The 'master' branch has not been initialized." "O ramo \"master\" n\u00e3o foi inicializado."
::msgcat::mcset pt_br "Hardlinks are unavailable.  Falling back to copying." "N\u00e3o foi poss\u00edvel criar hardlinks, usando c\u00f3pias convencionais."
::msgcat::mcset pt_br "Cloning from %s" "Clonando de %s"
::msgcat::mcset pt_br "Copying objects" "Copiando objetos"
::msgcat::mcset pt_br "KiB" "KiB"
::msgcat::mcset pt_br "Unable to copy object: %s" "N\u00e3o foi poss\u00edvel copiar o objeto: %s"
::msgcat::mcset pt_br "Linking objects" "Ligando objetos"
::msgcat::mcset pt_br "objects" "objetos"
::msgcat::mcset pt_br "Unable to hardlink object: %s" "N\u00e3o foi poss\u00edvel ligar o objeto: %s"
::msgcat::mcset pt_br "Cannot fetch branches and objects.  See console output for details." "N\u00e3o foi poss\u00edvel receber ramos ou objetos. Veja a sa\u00edda do console para detalhes."
::msgcat::mcset pt_br "Cannot fetch tags.  See console output for details." "N\u00e3o foi poss\u00edvel receber as etiquetas. Veja a sa\u00edda do console para detalhes."
::msgcat::mcset pt_br "Cannot determine HEAD.  See console output for details." "N\u00e3o foi poss\u00edvel determinar a etiqueta HEAD. Veja a sa\u00edda do console para detalhes."
::msgcat::mcset pt_br "Unable to cleanup %s" "N\u00e3o foi poss\u00edvel limpar %s"
::msgcat::mcset pt_br "Clone failed." "A clonagem falhou."
::msgcat::mcset pt_br "No default branch obtained." "O ramo padr\u00e3o n\u00e3o foi recebido."
::msgcat::mcset pt_br "Cannot resolve %s as a commit." "N\u00e3o foi poss\u00edvel resolver %s como uma revis\u00e3o."
::msgcat::mcset pt_br "Creating working directory" "Criando diret\u00f3rio de trabalho."
::msgcat::mcset pt_br "files" "arquivos"
::msgcat::mcset pt_br "Initial file checkout failed." "Erro ao efetuar checkout inicial."
::msgcat::mcset pt_br "Open" "Abrir"
::msgcat::mcset pt_br "Repository:" "Reposit\u00f3rio:"
::msgcat::mcset pt_br "Failed to open repository %s:" "Erro ao abrir o reposit\u00f3rio %s:"
::msgcat::mcset pt_br "This Detached Checkout" "Este checkout"
::msgcat::mcset pt_br "Revision Expression:" "Express\u00e3o de revis\u00e3o:"
::msgcat::mcset pt_br "Local Branch" "Ramo local"
::msgcat::mcset pt_br "Tracking Branch" "Ramo de rastreamento"
::msgcat::mcset pt_br "Tag" "Etiqueta"
::msgcat::mcset pt_br "Invalid revision: %s" "Revis\u00e3o inv\u00e1lida: %s"
::msgcat::mcset pt_br "No revision selected." "Nenhuma revis\u00e3o selecionada."
::msgcat::mcset pt_br "Revision expression is empty." "A express\u00e3o de revis\u00e3o est\u00e1 vazia."
::msgcat::mcset pt_br "Updated" "Atualizado"
::msgcat::mcset pt_br "URL" "URL"
::msgcat::mcset pt_br "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "N\u00e3o h\u00e1 nada para corrigir.\n\nVoc\u00ea est\u00e1 prestes a criar uma revis\u00e3o inicial. N\u00e3o h\u00e1 revis\u00e3o anterior para corrigir.\n"
::msgcat::mcset pt_br "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "N\u00e3o \u00e9 poss\u00edvel corrigir durante uma mesclagem.\n\nVoc\u00ea est\u00e1 em meio a uma opera\u00e7\u00e3o de mesclagem que n\u00e3o foi completada. N\u00e3o \u00e9 poss\u00edvel corrigir a revis\u00e3o anterior a menos que voc\u00ea aborte a mescla atual antes.\n"
::msgcat::mcset pt_br "Error loading commit data for amend:" "Erro ao carregar dados da revis\u00e3o para corrigir:"
::msgcat::mcset pt_br "Unable to obtain your identity:" "N\u00e3o foi poss\u00edvel obter a sua identidade:"
::msgcat::mcset pt_br "Invalid GIT_COMMITTER_IDENT:" "Vari\u00e1vel \"GIT_COMMITTER_IDENT\" inv\u00e1lida:"
::msgcat::mcset pt_br "warning: Tcl does not support encoding '%s'." "aviso: O Tcl n\u00e3o suporta a codifica\u00e7\u00e3o \"%s\"."
::msgcat::mcset pt_br "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "O \u00faltimo estado lido n\u00e3o confere com o estado atual.\n\nOutro programa do Git modificou o reposit\u00f3rio desde a \u00faltima leitura. Uma atualiza\u00e7\u00e3o deve ser executada antes de criar outra revis\u00e3o.\n\nA atualiza\u00e7\u00e3o come\u00e7ar\u00e1 automaticamente agora.\n"
::msgcat::mcset pt_br "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "N\u00e3o \u00e9 poss\u00edvel salvar revis\u00f5es para arquivos n\u00e3o mesclados.\n\nO arquivo %s possui conflitos de mesclagem. Voc\u00ea deve resolv\u00ea-los e marcar o arquivo antes de salvar a revis\u00e3o.\n"
::msgcat::mcset pt_br "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "Estado desconhecido detectado para o arquivo %s.\n\nEste programa n\u00e3o pode salvar uma revis\u00e3o para o arquivo %s.\n"
::msgcat::mcset pt_br "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "N\u00e3o h\u00e1 mudan\u00e7as para salvar.\n\nVoc\u00ea deve marcar ao menos um arquivo antes de salvar a revis\u00e3o.\n"
::msgcat::mcset pt_br "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "Por favor, indique uma descri\u00e7\u00e3o para a revis\u00e3o.\n\nUma boa descri\u00e7\u00e3o tem o seguinte formato:\n\n- Primeira linha: descreve, em uma \u00fanica frase, o que voc\u00ea fez.\n- Segunda linha: em branco.\n- Demais linhas: Descreve detalhadamente a revis\u00e3o.\n"
::msgcat::mcset pt_br "Calling pre-commit hook..." "Executando script \"pre-commit\"..."
::msgcat::mcset pt_br "Commit declined by pre-commit hook." "A revis\u00e3o foi bloqueada pelo script \"pre-commit\"."
::msgcat::mcset pt_br "Calling commit-msg hook..." "Executando script \"commit-msg\"..."
::msgcat::mcset pt_br "Commit declined by commit-msg hook." "Revis\u00e3o bloqueada pelo script \"commit-msg\"."
::msgcat::mcset pt_br "Committing changes..." "Salvando revis\u00e3o..."
::msgcat::mcset pt_br "write-tree failed:" "write-tree falhou:"
::msgcat::mcset pt_br "Commit failed." "A revis\u00e3o falhou."
::msgcat::mcset pt_br "Commit %s appears to be corrupt" "A revis\u00e3o %s parece estar corrompida."
::msgcat::mcset pt_br "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "N\u00e3o h\u00e1 altera\u00e7\u00f5es para salvar.\n\nNenhum arquivo foi modificado e esta n\u00e3o \u00e9 uma revis\u00e3o de mesclagem.\n\nUma atualiza\u00e7\u00e3o ser\u00e1 executada automaticamente agora.\n"
::msgcat::mcset pt_br "No changes to commit." "N\u00e3o h\u00e1 altera\u00e7\u00f5es para salvar."
::msgcat::mcset pt_br "commit-tree failed:" "commit-tree falhou:"
::msgcat::mcset pt_br "update-ref failed:" "update-ref falhou:"
::msgcat::mcset pt_br "Created commit %s: %s" "Revis\u00e3o %s criada: %s"
::msgcat::mcset pt_br "Working... please wait..." "Trabalhando... aguarde..."
::msgcat::mcset pt_br "Success" "Sucesso"
::msgcat::mcset pt_br "Error: Command Failed" "Erro: o comando falhou"
::msgcat::mcset pt_br "Number of loose objects" "N\u00famero de objetos soltos"
::msgcat::mcset pt_br "Disk space used by loose objects" "Espa\u00e7o ocupado pelos objetos soltos"
::msgcat::mcset pt_br "Number of packed objects" "N\u00famero de objetos compactados"
::msgcat::mcset pt_br "Number of packs" "N\u00famero de pacotes"
::msgcat::mcset pt_br "Disk space used by packed objects" "Espa\u00e7o ocupado pelos objetos compactados"
::msgcat::mcset pt_br "Packed objects waiting for pruning" "Objetos compactados aguardando elimina\u00e7\u00e3o"
::msgcat::mcset pt_br "Garbage files" "Arquivos de lixo"
::msgcat::mcset pt_br "Compressing the object database" "Compactando banco de dados de objetos"
::msgcat::mcset pt_br "Verifying the object database with fsck-objects" "Verificando banco de dados de objetos com fsck-objects"
::msgcat::mcset pt_br "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "Este reposit\u00f3rio possui aproximadamente %i objetos soltos.\n\nPara manter o desempenho \u00f3timo \u00e9 altamente recomendado que voc\u00ea compacte o banco de dados.\n\nCompactar o banco de dados agora?"
::msgcat::mcset pt_br "Invalid date from Git: %s" "Data inv\u00e1lida recebida do Git: %s"
::msgcat::mcset pt_br "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "Nenhuma diferen\u00e7a foi detectada.\n\n%s n\u00e3o possui mudan\u00e7as.\n\nA data de modifica\u00e7\u00e3o deste arquivo foi atualizada por outro aplicativo, mas o conte\u00fado do arquivo n\u00e3o foi alterado.\n\nUma atualiza\u00e7\u00e3o ser executada para encontrar outros arquivos que possam ter o mesmo estado."
::msgcat::mcset pt_br "Loading diff of %s..." "Carregando diferen\u00e7as de %s..."
::msgcat::mcset pt_br "LOCAL: deleted\nREMOTE:\n" "Local: apagado\nRemoto:\n"
::msgcat::mcset pt_br "REMOTE: deleted\nLOCAL:\n" "Remoto: apagado\nLocal:\n"
::msgcat::mcset pt_br "LOCAL:\n" "Local:\n"
::msgcat::mcset pt_br "REMOTE:\n" "Remoto:\n"
::msgcat::mcset pt_br "Unable to display %s" "Imposs\u00edvel exibir %s"
::msgcat::mcset pt_br "Error loading file:" "Erro ao carregar o arquivo:"
::msgcat::mcset pt_br "Git Repository (subproject)" "Reposit\u00f3rio Git (sub-projeto)"
::msgcat::mcset pt_br "* Binary file (not showing content)." "* Arquivo bin\u00e1rio (conte\u00fado n\u00e3o exibido)."
::msgcat::mcset pt_br "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* O arquivo n\u00e3o rastreado possui %d bytes.\n* Exibindo apenas os primeiros %d bytes.\n"
::msgcat::mcset pt_br "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* O arquivo n\u00e3o rastreado foi cortado aqui por %s.\n* Para ver o arquivo completo, use um editor externo.\n"
::msgcat::mcset pt_br "Failed to unstage selected hunk." "Erro ao desmarcar o trecho selecionado."
::msgcat::mcset pt_br "Failed to stage selected hunk." "Erro ao marcar o trecho selecionado."
::msgcat::mcset pt_br "Failed to unstage selected line." "Erro ao desmarcar a linha selecionada."
::msgcat::mcset pt_br "Failed to stage selected line." "Erro ao marcar a linha selecionada."
::msgcat::mcset pt_br "Default" "Padr\u00e3o"
::msgcat::mcset pt_br "System (%s)" "Sistema (%s)"
::msgcat::mcset pt_br "Other" "Outro"
::msgcat::mcset pt_br "error" "Erro"
::msgcat::mcset pt_br "warning" "aviso"
::msgcat::mcset pt_br "You must correct the above errors before committing." "Voc\u00ea precisa corrigir os erros acima antes de salvar a revis\u00e3o."
::msgcat::mcset pt_br "Unable to unlock the index." "Imposs\u00edvel desbloquear o \u00edndice."
::msgcat::mcset pt_br "Index Error" "Erro no \u00edndice"
::msgcat::mcset pt_br "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "A atualiza\u00e7\u00e3o do \u00edndice do Git falhou. Uma atualiza\u00e7\u00e3o ser\u00e1 executada automaticamente para ressincronizar o Git GUI"
::msgcat::mcset pt_br "Continue" "Continuar"
::msgcat::mcset pt_br "Unlock Index" "Desbloquear \u00edndice"
::msgcat::mcset pt_br "Unstaging %s from commit" "Desmarcando %s para revis\u00e3o"
::msgcat::mcset pt_br "Ready to commit." "Pronto para salvar a revis\u00e3o."
::msgcat::mcset pt_br "Adding %s" "Adicionando %s"
::msgcat::mcset pt_br "Revert changes in file %s?" "Reverter as altera\u00e7\u00f5es no arquivo %s?"
::msgcat::mcset pt_br "Revert changes in these %i files?" "Reverter as altera\u00e7\u00f5es nestes %i arquivos?"
::msgcat::mcset pt_br "Any unstaged changes will be permanently lost by the revert." "Qualquer altera\u00e7\u00e3o n\u00e3o marcada ser\u00e1 permanentemente perdida na revers\u00e3o."
::msgcat::mcset pt_br "Do Nothing" "N\u00e3o fazer nada"
::msgcat::mcset pt_br "Reverting selected files" "Revertendo os arquivos selecionados"
::msgcat::mcset pt_br "Reverting %s" "Revertendo %s"
::msgcat::mcset pt_br "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "N\u00e3o \u00e9 poss\u00edvel mesclar durante uma corre\u00e7\u00e3o.\n\nVoc\u00ea deve concluir a corre\u00e7\u00e3o antes de come\u00e7ar qualquer mesclagem.\n"
::msgcat::mcset pt_br "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "O \u00faltimo estado lido n\u00e3o confere com o estado atual.\n\nOutro programa do Git modificou o reposit\u00f3rio desde a \u00faltima leitura. Uma atualiza\u00e7\u00e3o deve ser executada antes de efetuar uma mesclagem.\n\nA atualiza\u00e7\u00e3o come\u00e7ar\u00e1 automaticamente agora.\n"
::msgcat::mcset pt_br "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "H\u00e1 uma mesclagem com conflitos em progresso.\n\nO arquivo %s possui conflitos de mesclagem.\n\nVoc\u00ea deve resolv\u00ea-los, marcar o arquivo e salvar a revis\u00e3o para completar a mesclagem atual. S\u00f3 ent\u00e3o voc\u00ea poder\u00e1 come\u00e7ar outra.\n"
::msgcat::mcset pt_br "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "Voc\u00ea est\u00e1 em meio a uma mudan\u00e7a.\n\nO arquivo %s foi modificado.\n\nVoc\u00ea deve completar e salvar a revis\u00e3o atual antes de come\u00e7ar uma mesclagem. Ao faz\u00ea-lo, voc\u00ea poder\u00e1 abortar a mesclagem caso haja algum erro.\n"
::msgcat::mcset pt_br "%s of %s" "%s de %s"
::msgcat::mcset pt_br "Merging %s and %s..." "Mesclando %s e %s..."
::msgcat::mcset pt_br "Merge completed successfully." "Mesclagem completada com sucesso."
::msgcat::mcset pt_br "Merge failed.  Conflict resolution is required." "A mesclagem falhou. \u00c9 necess\u00e1rio resolver conflitos."
::msgcat::mcset pt_br "Merge Into %s" "Mesclar em %s"
::msgcat::mcset pt_br "Revision To Merge" "Revis\u00e3o para mesclar"
::msgcat::mcset pt_br "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "N\u00e3o \u00e9 poss\u00edvel abortar durante uma corre\u00e7\u00e3o.\n\nVoc\u00ea precisa finalizar a corre\u00e7\u00e3o desta revis\u00e3o.\n"
::msgcat::mcset pt_br "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "Abortar mesclagem?\n\nAbortar a mesclagem atual implicar\u00e1 na perda de *TODAS* as mudan\u00e7as n\u00e3o salvas.\n\nAbortar a mesclagem atual?"
::msgcat::mcset pt_br "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "Descartar as mudan\u00e7as?\n\nAo faz\u00ea-lo, *TODAS* as altera\u00e7\u00f5es n\u00e3o salvas ser\u00e3o perdidas.\n\nContinuar e descartar as mudan\u00e7as atuais?"
::msgcat::mcset pt_br "Aborting" "Abortando"
::msgcat::mcset pt_br "files reset" "arquivos redefindos"
::msgcat::mcset pt_br "Abort failed." "A tentativa de abortar a opera\u00e7\u00e3o falhou"
::msgcat::mcset pt_br "Abort completed.  Ready." "Opera\u00e7\u00e3o abortada com sucesso. Pronto."
::msgcat::mcset pt_br "Force resolution to the base version?" "For\u00e7ar a resolu\u00e7\u00e3o para a vers\u00e3o base?"
::msgcat::mcset pt_br "Force resolution to this branch?" "For\u00e7ar resolu\u00e7\u00e3o para este ramo?"
::msgcat::mcset pt_br "Force resolution to the other branch?" "For\u00e7ar resolu\u00e7\u00e3o para o outro ramo?"
::msgcat::mcset pt_br "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "Note que o diff mostra apenas as mudan\u00e7as conflitantes.\n\n%s ser\u00e1 sobrescrito.\n\nCaso necess\u00e1rio, ser\u00e1 preciso reiniciar a mesclagem para desfazer esta opera\u00e7\u00e3o."
::msgcat::mcset pt_br "File %s seems to have unresolved conflicts, still stage?" "O arquivo %s parece ter conflitos n\u00e3o resolvidos. Marcar mesmo assim?"
::msgcat::mcset pt_br "Adding resolution for %s" "Adicionando resolu\u00e7\u00e3o para %s"
::msgcat::mcset pt_br "Cannot resolve deletion or link conflicts using a tool" "Imposs\u00edvel resolver conflitos envolvendo exclus\u00e3o ou links de arquivos com esta ferramenta."
::msgcat::mcset pt_br "Conflict file does not exist" "O arquivo conflitante n\u00e3o existe"
::msgcat::mcset pt_br "Not a GUI merge tool: '%s'" "N\u00e3o \u00e9 uma ferramenta de mesclagem gr\u00e1fica: \"%s\""
::msgcat::mcset pt_br "Unsupported merge tool '%s'" "Ferramenta de mesclagem n\u00e3o suportada \"%s\""
::msgcat::mcset pt_br "Merge tool is already running, terminate it?" "A ferramenta de mesclagem j\u00e1 est\u00e1 em execu\u00e7\u00e3o. Finalizar?"
::msgcat::mcset pt_br "Error retrieving versions:\n%s" "Erro ao obter as vers\u00f5es:\n%s"
::msgcat::mcset pt_br "Could not start the merge tool:\n\n%s" "N\u00e3o foi poss\u00edvel iniciar a ferramenta de mesclagem:\n\n%s"
::msgcat::mcset pt_br "Running merge tool..." "Executando ferramenta de mesclagem..."
::msgcat::mcset pt_br "Merge tool failed." "Ferramenta de mesclagem falhou."
::msgcat::mcset pt_br "Invalid global encoding '%s'" "Codifica\u00e7\u00e3o global inv\u00e1lida \"%s\""
::msgcat::mcset pt_br "Invalid repo encoding '%s'" "Codifica\u00e7\u00e3o do reposit\u00f3rio inv\u00e1lida \"%s\""
::msgcat::mcset pt_br "Restore Defaults" "Restaurar padr\u00f5es"
::msgcat::mcset pt_br "Save" "Salvar"
::msgcat::mcset pt_br "%s Repository" "Reposit\u00f3rio %s"
::msgcat::mcset pt_br "Global (All Repositories)" "Global (todos os reposit\u00f3rios)"
::msgcat::mcset pt_br "User Name" "Nome do usu\u00e1rio"
::msgcat::mcset pt_br "Email Address" "Endere\u00e7o de e-mail"
::msgcat::mcset pt_br "Summarize Merge Commits" "Exibir sum\u00e1rio das revis\u00f5es de mesclagem"
::msgcat::mcset pt_br "Merge Verbosity" "N\u00edvel de detalhamento da mesclagem"
::msgcat::mcset pt_br "Show Diffstat After Merge" "Exibir estat\u00edsticas ap\u00f3s mesclagens"
::msgcat::mcset pt_br "Use Merge Tool" "Usar ferramenta de mesclagem"
::msgcat::mcset pt_br "Trust File Modification Timestamps" "Confiar nas datas de modifica\u00e7\u00e3o dos arquivos"
::msgcat::mcset pt_br "Prune Tracking Branches During Fetch" "Eliminar ramos de rastreamento ao receber"
::msgcat::mcset pt_br "Match Tracking Branches" "Coincidir ramos de rastreamento"
::msgcat::mcset pt_br "Blame Copy Only On Changed Files" "Detectar c\u00f3pias somente em arquivos modificados"
::msgcat::mcset pt_br "Minimum Letters To Blame Copy On" "N\u00famero m\u00ednimo de letras para detectar c\u00f3pias"
::msgcat::mcset pt_br "Blame History Context Radius (days)" "Extens\u00e3o do contexto de detec\u00e7\u00e3o (em dias)"
::msgcat::mcset pt_br "Number of Diff Context Lines" "N\u00famero de linhas para o diff contextual"
::msgcat::mcset pt_br "Commit Message Text Width" "Largura do texto da descri\u00e7\u00e3o da revis\u00e3o"
::msgcat::mcset pt_br "New Branch Name Template" "Modelo de nome para novos ramos"
::msgcat::mcset pt_br "Default File Contents Encoding" "Codifica\u00e7\u00e3o padr\u00e3o dos arquivos"
::msgcat::mcset pt_br "Change" "Alterar"
::msgcat::mcset pt_br "Spelling Dictionary:" "Dicion\u00e1rio para o verificador ortogr\u00e1fico:"
::msgcat::mcset pt_br "Change Font" "Mudar fonte"
::msgcat::mcset pt_br "Choose %s" "Escolher %s"
::msgcat::mcset pt_br "pt." "pt."
::msgcat::mcset pt_br "Preferences" "Prefer\u00eancias"
::msgcat::mcset pt_br "Failed to completely save options:" "Houve um erro ao salvar as op\u00e7\u00f5es:"
::msgcat::mcset pt_br "Remove Remote" "Excluir"
::msgcat::mcset pt_br "Prune from" "Limpar de"
::msgcat::mcset pt_br "Fetch from" "Receber de"
::msgcat::mcset pt_br "Push to" "Enviar para"
::msgcat::mcset pt_br "Add Remote" "Adicionar reposit\u00f3rio remoto"
::msgcat::mcset pt_br "Add New Remote" "Adicionar novo reposit\u00f3rio remoto"
::msgcat::mcset pt_br "Add" "Adicionar"
::msgcat::mcset pt_br "Remote Details" "Detalhes do reposit\u00f3rio remoto"
::msgcat::mcset pt_br "Location:" "Local:"
::msgcat::mcset pt_br "Further Action" "A\u00e7\u00f5es adicionais"
::msgcat::mcset pt_br "Fetch Immediately" "Receber imediatamente"
::msgcat::mcset pt_br "Initialize Remote Repository and Push" "Inicializar reposit\u00f3rio remoto e enviar"
::msgcat::mcset pt_br "Do Nothing Else Now" "N\u00e3o fazer nada agora"
::msgcat::mcset pt_br "Please supply a remote name." "Por favor, indique um nome para o reposit\u00f3rio remoto."
::msgcat::mcset pt_br "'%s' is not an acceptable remote name." "\"%s\" n\u00e3o \u00e9 um nome v\u00e1lido para um reposit\u00f3rio remoto."
::msgcat::mcset pt_br "Failed to add remote '%s' of location '%s'." "Erro ao adicionar reposit\u00f3rio remoto \"%s\" do local \"%s\":"
::msgcat::mcset pt_br "fetch %s" "receber %s"
::msgcat::mcset pt_br "Fetching the %s" "Recebendo o %s"
::msgcat::mcset pt_br "Do not know how to initialize repository at location '%s'." "N\u00e3o sabe como inicializar o reposit\u00f3rio remoto em \"%s\"."
::msgcat::mcset pt_br "push %s" "enviar %s"
::msgcat::mcset pt_br "Setting up the %s (at %s)" "Configurando %s (em %s)"
::msgcat::mcset pt_br "Delete Branch Remotely" "Apagar ramo remoto"
::msgcat::mcset pt_br "From Repository" "Do reposit\u00f3rio"
::msgcat::mcset pt_br "Remote:" "Remoto:"
::msgcat::mcset pt_br "Arbitrary Location:" "Outro local:"
::msgcat::mcset pt_br "Branches" "Ramos"
::msgcat::mcset pt_br "Delete Only If" "Apagar somente se"
::msgcat::mcset pt_br "Merged Into:" "Mesclado em:"
::msgcat::mcset pt_br "A branch is required for 'Merged Into'." "\u00c9 preciso indicar um ramo para \"Mesclado em\"."
::msgcat::mcset pt_br "The following branches are not completely merged into %s:\n\n - %s" "Os seguintes ramos n\u00e3o est\u00e3o inteiramente mesclados em %s:\n\n - %s"
::msgcat::mcset pt_br "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "Um ou mais testes de mesclagem falharam porque voc\u00ea n\u00e3o possui as revis\u00f5es necess\u00e1rias. Tente receber revis\u00f5es de %s primeiro."
::msgcat::mcset pt_br "Please select one or more branches to delete." "Por favor selecione um ou mais ramos para apagar."
::msgcat::mcset pt_br "Deleting branches from %s" "Apagar ramos de %s"
::msgcat::mcset pt_br "No repository selected." "Nenhum reposit\u00f3rio foi selecionado."
::msgcat::mcset pt_br "Scanning %s..." "Atualizando %s..."
::msgcat::mcset pt_br "Find:" "Encontrar:"
::msgcat::mcset pt_br "Next" "Pr\u00f3ximo"
::msgcat::mcset pt_br "Prev" "Anterior"
::msgcat::mcset pt_br "Case-Sensitive" "Sens\u00edvel a mai\u00fasculas/min\u00fasculas"
::msgcat::mcset pt_br "Cannot write shortcut:" "N\u00e3o foi poss\u00edvel gravar o atalho:"
::msgcat::mcset pt_br "Cannot write icon:" "N\u00e3o foi poss\u00edvel gravar o \u00edcone:"
::msgcat::mcset pt_br "Unsupported spell checker" "Verificador ortogr\u00e1fico n\u00e3o suportado"
::msgcat::mcset pt_br "Spell checking is unavailable" "Verifica\u00e7\u00e3o ortogr\u00e1fica indispon\u00edvel"
::msgcat::mcset pt_br "Invalid spell checking configuration" "Configura\u00e7\u00e3o do verificador ortogr\u00e1fico inv\u00e1lida"
::msgcat::mcset pt_br "Reverting dictionary to %s." "Revertendo dicion\u00e1rio para %s."
::msgcat::mcset pt_br "Spell checker silently failed on startup" "O verificador ortogr\u00e1fico falhou sem relatar nenhum erro"
::msgcat::mcset pt_br "Unrecognized spell checker" "Verificador ortogr\u00e1fico n\u00e3o reconhecido"
::msgcat::mcset pt_br "No Suggestions" "Sem sugest\u00f5es"
::msgcat::mcset pt_br "Unexpected EOF from spell checker" "Final de arquivo inesperado recebido do verificador ortogr\u00e1fico"
::msgcat::mcset pt_br "Spell Checker Failed" "A verifica\u00e7\u00e3o ortogr\u00e1fica falhou"
::msgcat::mcset pt_br "No keys found." "Nenhuma chave encontrada"
::msgcat::mcset pt_br "Found a public key in: %s" "Chave p\u00fablica encontrada em: %s"
::msgcat::mcset pt_br "Generate Key" "Gerar chave"
::msgcat::mcset pt_br "Copy To Clipboard" "Copiar para a \u00e1rea de transfer\u00eancia"
::msgcat::mcset pt_br "Your OpenSSH Public Key" "Sua chave p\u00fablica OpenSSH"
::msgcat::mcset pt_br "Generating..." "Gerando..."
::msgcat::mcset pt_br "Could not start ssh-keygen:\n\n%s" "Imposs\u00edvel iniciar ssh-keygen:\n\n%s"
::msgcat::mcset pt_br "Generation failed." "A gera\u00e7\u00e3o da chave falhou."
::msgcat::mcset pt_br "Generation succeeded, but no keys found." "A gera\u00e7\u00e3o da chave foi bem-sucedida, mas nenhuma chave foi encontrada."
::msgcat::mcset pt_br "Your key is in: %s" "Sua chave em: %s"
::msgcat::mcset pt_br "%s ... %*i of %*i %s (%3i%%)" "%s ... %*i de %*i %s (%3i%%)"
::msgcat::mcset pt_br "Running %s requires a selected file." "\u00c9 preciso selecionar um arquivo para executar %s."
::msgcat::mcset pt_br "Are you sure you want to run %s?" "Voc\u00ea tem certeza que deseja executar %s?"
::msgcat::mcset pt_br "Tool: %s" "Ferramenta: %s"
::msgcat::mcset pt_br "Running: %s" "Executando: %s"
::msgcat::mcset pt_br "Tool completed successfully: %s" "Execu\u00e7\u00e3o completada com sucesso: %s"
::msgcat::mcset pt_br "Tool failed: %s" "Ferramenta falhou: %s"
::msgcat::mcset pt_br "Add Tool" "Adicionar ferramenta"
::msgcat::mcset pt_br "Add New Tool Command" "Adicionar novo comando de ferramenta"
::msgcat::mcset pt_br "Add globally" "Adicionar globalmente"
::msgcat::mcset pt_br "Tool Details" "Detalhes da ferramenta"
::msgcat::mcset pt_br "Use '/' separators to create a submenu tree:" "Use o separador \"/\" para criar uma \u00e1rvore de sub-menus:"
::msgcat::mcset pt_br "Command:" "Comando:"
::msgcat::mcset pt_br "Show a dialog before running" "Exibir uma caixa de di\u00e1logo antes de executar"
::msgcat::mcset pt_br "Ask the user to select a revision (sets \$REVISION)" "Solicitar a sele\u00e7\u00e3o de uma revis\u00e3o (a vari\u00e1vel \$REVISION)"
::msgcat::mcset pt_br "Ask the user for additional arguments (sets \$ARGS)" "Solicitar argumentos adicionais (define a vari\u00e1vel \$ARGS)"
::msgcat::mcset pt_br "Don't show the command output window" "N\u00e3o exibir a janela de sa\u00edda do comando"
::msgcat::mcset pt_br "Run only if a diff is selected (\$FILENAME not empty)" "Executar apenas se houver um diff selecionado (\$FILENAME n\u00e3o-vazio)"
::msgcat::mcset pt_br "Please supply a name for the tool." "Por favor, indique um nome para a ferramenta."
::msgcat::mcset pt_br "Tool '%s' already exists." "A ferramenta \"%s\" j\u00e1 existe."
::msgcat::mcset pt_br "Could not add tool:\n%s" "N\u00e3o foi poss\u00edvel adicionar a ferramenta:\n%s"
::msgcat::mcset pt_br "Remove Tool" "Excluir ferramenta"
::msgcat::mcset pt_br "Remove Tool Commands" "Excluir comando de ferramenta"
::msgcat::mcset pt_br "Remove" "Excluir"
::msgcat::mcset pt_br "(Blue denotes repository-local tools)" "(Azul indica ferramentas do reposit\u00f3rio local)"
::msgcat::mcset pt_br "Run Command: %s" "Executar comando: %s"
::msgcat::mcset pt_br "Arguments" "Argumentos"
::msgcat::mcset pt_br "OK" "OK"
::msgcat::mcset pt_br "Fetching new changes from %s" "Recebendo novas mudan\u00e7as de %s"
::msgcat::mcset pt_br "remote prune %s" "Limpar %s"
::msgcat::mcset pt_br "Pruning tracking branches deleted from %s" "Limpando ramos exclu\u00eddos de %s"
::msgcat::mcset pt_br "Pushing changes to %s" "Enviando mudan\u00e7as para %s"
::msgcat::mcset pt_br "Mirroring to %s" "Duplicando para %s"
::msgcat::mcset pt_br "Pushing %s %s to %s" "Enviando %s %s para %s"
::msgcat::mcset pt_br "Push Branches" "Enviar ramos"
::msgcat::mcset pt_br "Source Branches" "Ramos de origem"
::msgcat::mcset pt_br "Destination Repository" "Reposit\u00f3rio de destino"
::msgcat::mcset pt_br "Transfer Options" "Op\u00e7\u00f5es de transfer\u00eancia"
::msgcat::mcset pt_br "Force overwrite existing branch (may discard changes)" "Sobrescrever ramos existentes (pode descartar mudan\u00e7as)"
::msgcat::mcset pt_br "Use thin pack (for slow network connections)" "Usar compacta\u00e7\u00e3o minimalista (para redes lentas)"
::msgcat::mcset pt_br "Include tags" "Incluir etiquetas"
