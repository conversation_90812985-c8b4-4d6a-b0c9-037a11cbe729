## Syntax highlighting for JSON files.

## Original author:  <PERSON><PERSON><PERSON>
## License:  GPL version 3 or newer

syntax json "\.json$"
# No comments are permitted in JSON.
comment ""

# Numbers (used as value).
color green ":[[:blank:]]*\-?(0|[1-9][0-9]*)(\.[0-9]+)?([Ee]?[-+]?[0-9]+)?"
# Values (well, any string).
color brightmagenta "".+""
# Hex numbers (used as value).
color green ":[[:blank:]]*"#[[:xdigit:]]+""
# Escapes.
color green "\\(["\/bfnrt]|u[[:xdigit:]]{4})"
# Special words.
color green "\<(true|false|null)\>"

# Names (very unlikely to contain a quote).
color brightblue ""[^"]+"[[:blank:]]*:"

# Brackets, braces, and separators.
color brightblue "[][]"
color brightred "[{},:]"

# Comments.
color cyan "(^|[[:blank:]]+)(//|#).*"

# Trailing whitespace.
color ,green "[[:space:]]+$"
