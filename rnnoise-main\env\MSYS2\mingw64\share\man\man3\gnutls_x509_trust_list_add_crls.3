.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_add_crls" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_add_crls \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_add_crls(gnutls_x509_trust_list_t " list ", const gnutls_x509_crl_t * " crl_list ", unsigned " crl_size ", unsigned int " flags ", unsigned int " verification_flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "const gnutls_x509_crl_t * crl_list" 12
A list of CRLs
.IP "unsigned crl_size" 12
The length of the CRL list
.IP "unsigned int flags" 12
flags from \fBgnutls_trust_list_flags_t\fP
.IP "unsigned int verification_flags" 12
gnutls_certificate_verify_flags if flags specifies GNUTLS_TL_VERIFY_CRL
.SH "DESCRIPTION"
This function will add the given certificate revocation lists
to the trusted list. The CRLs in  \fIcrl_list\fP must not be deinitialized
during the lifetime of  \fIlist\fP .

This function must be called after \fBgnutls_x509_trust_list_add_cas()\fP
to allow verifying the CRLs for validity. If the flag \fBGNUTLS_TL_NO_DUPLICATES\fP
is given, then the final CRL list will not contain duplicate entries.

If the flag \fBGNUTLS_TL_NO_DUPLICATES\fP is given, \fBgnutls_x509_trust_list_deinit()\fP must be
called with parameter  \fIall\fP being 1.

If flag \fBGNUTLS_TL_VERIFY_CRL\fP is given the CRLs will be verified before being added,
and if verification fails, they will be skipped.
.SH "RETURNS"
The number of added elements is returned; that includes
duplicate entries.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
