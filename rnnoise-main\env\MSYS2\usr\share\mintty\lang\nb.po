# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the mintty package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2025-03-22 07:33+0100\n"
"PO-Revision-Date: 2019-09-06 18:08+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: nb\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.3\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: charset.c:228 charset.c:239 winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "(Default)"
msgstr "(Standard)"

#: charset.c:250
msgid "(OEM codepage)"
msgstr "(OEM-kodeside)"

#: charset.c:254
msgid "(ANSI codepage)"
msgstr "(ANSI-kodeside)"

#: child.c:96
msgid "There are no available terminals"
msgstr "Det er ingen tilgjengelige terminaler"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "Feil: Klarte ikke å åpne loggfilen"

#: child.c:334
msgid "Error: Could not fork child process"
msgstr "Feil: Klarte ikke å adskille underprosessen"

#: child.c:336
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr "DLL-rebasing kan være nødvendig; se 'rebaseall / rebase --help'"

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:426
msgid "Failed to run '%s': %s"
msgstr "Klarte ikke å kjøre '%s': %s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:578
msgid "%s: Exit %i"
msgstr "%s: Avslutt %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:585
msgid "TERMINATED"
msgstr "AVSLUTTET"

#: child.c:1232
msgid "Error: Could not fork child daemon"
msgstr "Feil: Klarte ikke å adskille underdaemonen"

#. __ Setting false for Boolean options (localization optional)
#: config.c:655
msgid "no"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:657
msgid "yes"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:659
msgid "false"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:661
msgid "true"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:663
msgid "off"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:665
msgid "on"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:694
msgid "stretch"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:696
msgid "align"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:698
msgid "middle"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:700
msgid "full"
msgstr ""

#. __ %s: unknown option name
#: config.c:866
msgid "Ignoring unknown option '%s'"
msgstr "Ignorerer det ukjente alternativet '%s'"

#: config.c:914 config.c:943
msgid "Internal error: too many options"
msgstr "Intern feil: For mange innstillinger"

#: config.c:930
msgid "Internal error: too many options/comments"
msgstr "Intern feil: For mange innstillinger/kommentarer"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1100
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "Ignorerer den ukjente verdien '%s' for '%s'-innstillingen"

#. __ %s: option name
#: config.c:1112
msgid "Ignoring option '%s' with missing value"
msgstr "Ignorerer '%s'-innstillingen som mangler en verdi"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1791
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"Klarte ikke å lagre innstillingene til '%s':\n"
"%s."

#: config.c:2172
msgid "◇ None (printing disabled) ◇"
msgstr "◇ Ingen (utskriving er skrudd av) ◇"

#: config.c:2174
msgid "◆ Default printer ◆"
msgstr "◆ Standardskriver ◆"

#. __ UI localization disabled
#: config.c:2283
msgid "– None –"
msgstr "– Ingen –"

#. __ UI localization: use Windows desktop setting
#: config.c:2285
msgid "@ Windows language @"
msgstr "@ Windows-språk @"

#. __ UI localization: use environment variable setting (LANGUAGE, LC_*)
#: config.c:2287
msgid "* Locale environm. *"
msgstr "* Regionsområde *"

#. __ UI localization: use mintty configuration setting (Text - Locale)
#: config.c:2289
msgid "= cfg. Text Locale ="
msgstr "= Oppsatt tekstregion ="

#: config.c:2394
msgid "simple beep"
msgstr "enkelt bipp"

#: config.c:2395
msgid "no beep"
msgstr "ingen bipp"

#: config.c:2396
msgid "Default Beep"
msgstr "Standardbipp"

#: config.c:2397
msgid "Critical Stop"
msgstr "Kritisk stopp"

#: config.c:2398
msgid "Question"
msgstr "Spørsmål"

#: config.c:2399
msgid "Exclamation"
msgstr "Utropstegn"

#: config.c:2400
msgid "Asterisk"
msgstr "Asterisk"

#: config.c:2443
msgid "◇ None (system sound) ◇"
msgstr "◇ Ingen (systemlyd) ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2874 config.c:3425
msgid "◇ None ◇"
msgstr "◇ Ingen ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2877
msgid "downloaded / give me a name!"
msgstr "lastet ned / gi meg et navn!"

#: config.c:2983
msgid "Could not load web theme"
msgstr "Klarte ikke å laste ned nett-temaet"

#: config.c:3040
msgid "Cannot write theme file"
msgstr "Klarte ikke å skrive til temafilen"

#: config.c:3045
msgid "Cannot store theme file"
msgstr "Klarte ikke å lagre temafilen"

#. __ Options - Text:
#: config.c:3502 config.c:3840 config.c:3939
msgid "as font"
msgstr "som skrifttype"

#. __ Options - Text:
#: config.c:3503 config.c:3845 config.c:3944
msgid "as colour"
msgstr "som farge"

#: config.c:3504
msgid "as font & as colour"
msgstr "skrifttype & farge"

#. __ Options - Text:
#: config.c:3505 config.c:3850 config.c:3949
msgid "xterm"
msgstr ""

#. __ Dialog button - show About text
#: config.c:3653
msgid "About..."
msgstr "Om …"

#. __ Dialog button - save changes
#: config.c:3656
msgid "Save"
msgstr "Lagre"

#. __ Dialog button - cancel
#: config.c:3660 winctrls.c:1277 windialog.c:895
msgid "Cancel"
msgstr "Avbrytt"

#. __ Dialog button - apply changes
#: config.c:3664
msgid "Apply"
msgstr "Bruk"

#. __ Dialog button - take notice
#: config.c:3668 windialog.c:892
msgid "I see"
msgstr "Jeg skjønner"

#. __ Dialog button - confirm action
#: config.c:3670 winctrls.c:1276 windialog.c:894
msgid "OK"
msgstr "OK"

#. __ Options - Looks: treeview label
#: config.c:3677 config.c:3708 config.c:3767
msgid "Looks"
msgstr "Utseende"

#. __ Options - Looks: panel title
#: config.c:3679
msgid "Looks in Terminal"
msgstr "Utseende i terminalen"

#. __ Options - Looks: section title
#: config.c:3681
msgid "Colours"
msgstr "Farger"

#. __ Options - Looks:
#: config.c:3685
msgid "&Foreground..."
msgstr "&Forgrunn …"

#. __ Options - Looks:
#: config.c:3689
msgid "&Background..."
msgstr "&Bakgrunn …"

#. __ Options - Looks:
#: config.c:3693
msgid "&Cursor..."
msgstr "&Musepeker …"

#. __ Options - Looks:
#: config.c:3697
msgid "&Theme"
msgstr "&Tema"

#. __ Options - Looks: name of web service
#: config.c:3702
msgid "Color Scheme Designer"
msgstr "Fargepalettutformer"

#. __ Options - Looks: store colour scheme
#: config.c:3705 winctrls.c:484
msgid "Store"
msgstr "Lagre"

#. __ Options - Looks: section title
#: config.c:3710
msgid "Transparency"
msgstr "Gjennomsiktighet"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3716 config.c:4098 config.c:4261 config.c:4459
msgid "&Off"
msgstr "&Av"

#. __ Options - Looks: transparency
#: config.c:3718
msgid "&Low"
msgstr "&Lav"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3720
msgid "&Med."
msgstr "&Mid."

#. __ Options - Looks: transparency
#: config.c:3722
msgid "&Medium"
msgstr "&Middels"

#. __ Options - Looks: transparency
#: config.c:3724
msgid "&High"
msgstr "&Høy"

#. __ Options - Looks: transparency
#: config.c:3726
msgid "Gla&ss"
msgstr "Gla&ss"

#. __ Options - Looks: transparency
#: config.c:3733 config.c:3745 config.c:3752
msgid "Opa&que when focused"
msgstr "Fast&het ved fokus"

#. __ Options - Looks: transparency
#: config.c:3738
msgid "Blu&r"
msgstr "Ukla&rhet"

#: config.c:3759
msgid "◄"
msgstr ""

#: config.c:3762
msgid "►"
msgstr ""

#. __ Options - Looks: section title
#: config.c:3769
msgid "Cursor"
msgstr "Musepeker"

#. __ Options - Looks: cursor type
#: config.c:3774
msgid "Li&ne"
msgstr "Li&nje"

#. __ Options - Looks: cursor type
#: config.c:3776
msgid "Bloc&k"
msgstr "Blok&kmerking"

#. __ Options - Looks: cursor type
#: config.c:3779
msgid "Bo&x"
msgstr ""

#. __ Options - Looks: cursor type
#: config.c:3782
msgid "&Underscore"
msgstr "&Understreking"

#. __ Options - Looks: cursor feature
#: config.c:3787
msgid "Blinkin&g"
msgstr "Blinkin&g"

#. __ Options - Text: treeview label
#: config.c:3794 config.c:3819 config.c:3834 config.c:3883 config.c:3933
#: config.c:3958 config.c:3980 config.c:3993 config.c:4001
msgid "Text"
msgstr "Tekst"

#. __ Options - Text: panel title
#: config.c:3796
msgid "Text and Font properties"
msgstr "Tekst- og skrifttypeegenskaper"

#. __ Options - Text: section title
#: config.c:3798
msgid "Font"
msgstr "Skrift"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3806 winctrls.c:1287
msgid "Font st&yle:"
msgstr "Skrifttypest&il:"

#. __ Font chooser:
#: config.c:3811 winctrls.c:1289
msgid "&Size:"
msgstr "&Størrelse:"

#. __ Options - Text:
#: config.c:3823 config.c:3902
msgid "Sho&w bold as font"
msgstr "Vi&s fet tekst som skrifttype"

#. __ Options - Text:
#: config.c:3828 config.c:3907
msgid "Show &bold as colour"
msgstr "Vis &fet tekst som farge"

#. __ Options - Text:
#: config.c:3836 config.c:3857 config.c:3935 config.c:3962
msgid "Show bold"
msgstr "Vis fet tekst"

#. __ Options - Text:
#: config.c:3864 config.c:3912 config.c:3968
msgid "&Allow blinking"
msgstr "&Tillat blinking"

#. __ Options - Text:
#: config.c:3869 config.c:3973
msgid "Show dim as font"
msgstr "Vi&s blek tekst som skrifttype"

#. __ Options - Text:
#: config.c:3887 config.c:3920 config.c:3955
msgid "Font smoothing"
msgstr "Skrifttypeutjevning"

#. __ Options - Text:
#: config.c:3890 config.c:3923 config.c:4149 config.c:4188 config.c:4342
#: config.c:4355
msgid "&Default"
msgstr "&Standard"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3892 config.c:3925 config.c:4147 config.c:4186 config.c:4340
#: config.c:4353 config.c:4440
msgid "&None"
msgstr "&Ingen"

#. __ Options - Text:
#: config.c:3894 config.c:3927 config.c:4148 config.c:4187 config.c:4341
#: config.c:4354
msgid "&Partial"
msgstr "&Delvis"

#. __ Options - Text:
#: config.c:3896 config.c:3929 config.c:4150 config.c:4189 config.c:4343
#: config.c:4356
msgid "&Full"
msgstr "&Full"

#: config.c:3983
msgid "&Locale"
msgstr "&Region"

#: config.c:3986
msgid "&Character set"
msgstr "&Tegnsett"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3997 config.c:4003
msgid "Emojis"
msgstr "Emojier"

#. __ Options - Text - Emojis:
#: config.c:4007
msgid "Style"
msgstr "Stil"

#. __ Options - Text - Emojis:
#: config.c:4012
msgid "Placement"
msgstr "Plassering"

#. __ Options - Keys: treeview label
#: config.c:4020 config.c:4050 config.c:4085 config.c:4103
msgid "Keys"
msgstr "Nøkler"

#. __ Options - Keys: panel title
#: config.c:4022
msgid "Keyboard features"
msgstr "Tastaturegenskaper"

#. __ Options - Keys:
#: config.c:4026
msgid "&Backarrow sends ^H"
msgstr "&Bakoverpil sender ^H"

#. __ Options - Keys:
#: config.c:4031
msgid "&Delete sends DEL"
msgstr "&Delete sender DEL"

#. __ Options - Keys:
#: config.c:4036
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "Ctrl+LeftAlt er Alt&Gr"

#. __ Options - Keys:
#: config.c:4041
msgid "AltGr is also Alt"
msgstr "AltGr er også Alt"

#. __ Options - Keys:
#: config.c:4046
msgid "&Esc/Enter reset IME to alphanumeric"
msgstr ""

#. __ Options - Keys: section title
#: config.c:4052
msgid "Shortcuts"
msgstr "Snarveier"

#. __ Options - Keys:
#: config.c:4055
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "Kop&ier og lim inn (Ctrl/Shift+Ins)"

#. __ Options - Keys:
#: config.c:4060
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "&Meny og fullskjerm (Alt+Space/Enter)"

#. __ Options - Keys:
#: config.c:4065
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "&Bytt vindu (Ctrl+[Shift+]Tab)"

#. __ Options - Keys:
#: config.c:4070
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "&Forstørr (Ctrl+pluss/minus/null)"

#. __ Options - Keys:
#: config.c:4075
msgid "&Alt+Fn shortcuts"
msgstr "&Alt+Fn-snarveier"

#. __ Options - Keys:
#: config.c:4080
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "&Ctrl+Shift+bokstav-snarveier"

#. __ Options - Keys: section title
#: config.c:4087 config.c:4105
msgid "Compose key"
msgstr "Compose-tast"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:4092 config.c:4253 config.c:4272 config.c:4451 config.c:4470
msgid "&Shift"
msgstr "&Shift"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:4094 config.c:4255 config.c:4280 config.c:4453 config.c:4478
msgid "&Ctrl"
msgstr "&Ctrl"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4096 config.c:4257 config.c:4276 config.c:4455 config.c:4474
msgid "&Alt"
msgstr "&Alt"

#. __ Options - Mouse: treeview label
#: config.c:4112 config.c:4201 config.c:4233
msgid "Mouse"
msgstr "Mus"

#. __ Options - Mouse: panel title
#: config.c:4114
msgid "Mouse functions"
msgstr "Musefunksjoner"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4122 config.c:4155 config.c:4171 config.c:4319
msgid "Cop&y on select"
msgstr "Kop&ier ved velging"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4127 config.c:4160 config.c:4324 wininput.c:685
msgid "Copy with TABs"
msgstr "Kopier som &TABs"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4132 config.c:4165 config.c:4177 config.c:4329
msgid "Copy as &rich text"
msgstr "Kopier som &rik tekst"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4138 config.c:4145 config.c:4184 config.c:4338 config.c:4351
msgid "Copy as &HTML"
msgstr "Kopier som &HTML"

#. __ Options - Mouse:
#: config.c:4197
msgid "Clic&ks place command line cursor"
msgstr "Kli&kk plasserer ledetekstmusepekeren"

#. __ Options - Mouse: section title
#: config.c:4203
msgid "Click actions"
msgstr "Klikkhandlinger"

#. __ Options - Mouse:
#: config.c:4206
msgid "Right mouse button"
msgstr "Høyre museknapp"

#. __ Options - Mouse:
#: config.c:4209 config.c:4223
msgid "&Paste"
msgstr "&Lim inn"

#. __ Options - Mouse:
#: config.c:4211 config.c:4225
msgid "E&xtend"
msgstr "U&tvid"

#. __ Options - Mouse:
#: config.c:4213
msgid "&Menu"
msgstr "&Meny"

#. __ Options - Mouse:
#: config.c:4215 config.c:4229
msgid "Ente&r"
msgstr "Ente&r"

#. __ Options - Mouse:
#: config.c:4220
msgid "Middle mouse button"
msgstr "Midtre museknapp"

#. __ Options - Mouse:
#: config.c:4227
msgid "&Nothing"
msgstr "&Ingenting"

#. __ Options - Mouse: section title
#: config.c:4235
msgid "Application mouse mode"
msgstr "Programmusemodus"

#. __ Options - Mouse:
#: config.c:4238
msgid "Default click target"
msgstr "Standard klikkmålpunkt"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4241
msgid "&Window"
msgstr "&Vindu"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4243
msgid "&Application"
msgstr "&Program"

#. __ Options - Mouse:
#: config.c:4250 config.c:4268
msgid "Modifier for overriding default"
msgstr "Modifikator for å overstyre standardene"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4259 config.c:4284 config.c:4457 config.c:4482
msgid "&Win"
msgstr "&Win"

#. __ Options - Modifier - Super:
#: config.c:4288 config.c:4486
msgid "&Sup"
msgstr "&Sup"

#. __ Options - Modifier - Hyper:
#: config.c:4292 config.c:4490
msgid "&Hyp"
msgstr "&Hyp"

#. __ Options - Selection: treeview label
#: config.c:4302 config.c:4313 config.c:4377
msgid "Selection"
msgstr "Markering"

#. __ Options - Selection: panel title
#: config.c:4304
msgid "Selection and clipboard"
msgstr "Utvalg og utklippstavle"

#. __ Options - Selection:
#: config.c:4308
msgid "Clear selection on input"
msgstr "Tøm utvalget ved inndata"

#. __ Options - Selection: section title
#: config.c:4315
msgid "Clipboard"
msgstr "Utklippstavle"

#. __ Options - Selection:
#: config.c:4365
msgid "Trim space from selection"
msgstr "Trim vekk mellomrom fra utvalg"

#. __ Options - Selection:
#: config.c:4371
msgid "Allow setting selection"
msgstr "Tillat å fastsette utvalg"

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4379 config.c:4401 config.c:4426 config.c:4499
msgid "Window"
msgstr "Vindu"

#. __ Options - Selection: clock position of info popup for text size
#: config.c:4384
msgid "Show size while selecting (0..12)"
msgstr "Vis størrelsen under utvelging (0..12)"

#. __ Options - Selection:
#: config.c:4391
msgid "Suspend output while selecting"
msgstr "Suspender utdata ved utvelging"

#. __ Options - Window: panel title
#: config.c:4403
msgid "Window properties"
msgstr "Vindusegenskaper"

#. __ Options - Window: section title
#: config.c:4405
msgid "Default size"
msgstr "Standardstørrelse"

#. __ Options - Window:
#: config.c:4409
msgid "Colu&mns"
msgstr "Kolo&nner"

#. __ Options - Window:
#: config.c:4413
msgid "Ro&ws"
msgstr "Ra&der"

#. __ Options - Window:
#: config.c:4417
msgid "C&urrent size"
msgstr "Nå&værende størrelse"

#. __ Options - Window:
#: config.c:4422
msgid "Re&wrap on resize"
msgstr "Bryt på nytt ved endring av størrelse"

#. __ Options - Window:
#: config.c:4430
msgid "Scroll&back lines"
msgstr "Antall skrolle&historikklinjer"

#. __ Options - Window:
#: config.c:4435
msgid "Scrollbar"
msgstr "Rullefelt"

#. __ Options - Window: scrollbar
#: config.c:4438
msgid "&Left"
msgstr "&Venstre"

#. __ Options - Window: scrollbar
#: config.c:4442
msgid "&Right"
msgstr "&Høyre"

#. __ Options - Window:
#: config.c:4448 config.c:4466
msgid "Modifier for scrolling"
msgstr "Modifikator for skrolling"

#. __ Options - Window:
#: config.c:4495
msgid "&PgUp and PgDn scroll without modifier"
msgstr "&PgUp- og PgDn-skrolling uten modifikator"

#. __ Options - Window: section title
#: config.c:4501
msgid "UI language"
msgstr "Grensesnittsspråk"

#. __ Options - Terminal: treeview label
#: config.c:4511 config.c:4524 config.c:4585 config.c:4599
msgid "Terminal"
msgstr "Terminal"

#. __ Options - Terminal: panel title
#: config.c:4513
msgid "Terminal features"
msgstr "Terminalegenskaper"

#. __ Options - Terminal:
#: config.c:4517
msgid "&Type"
msgstr "&Type"

#. __ Options - Terminal: answerback string for ^E request
#: config.c:4521
msgid "&Answerback"
msgstr "&Tilbakemelding"

#. __ Options - Terminal: section title
#: config.c:4526
msgid "Bell"
msgstr "Bjelle"

#. __ Options - Terminal: bell
#: config.c:4533
msgid "► &Play"
msgstr "► &Ring"

#. __ Options - Terminal: bell
#: config.c:4539
msgid "&Wave"
msgstr "&Vink"

#. __ Options - Terminal: bell
#: config.c:4561 config.c:4574
msgid "&Flash"
msgstr "&Blink"

#. __ Options - Terminal: bell
#: config.c:4563 config.c:4578
msgid "&Highlight in taskbar"
msgstr "&Fremhev i verktøylinjen"

#. __ Options - Terminal: bell
#: config.c:4565 config.c:4582
msgid "&Popup"
msgstr "&Oppsprett"

#. __ Options - Terminal: section title
#: config.c:4587
msgid "Printer"
msgstr "Skriver"

#. __ Options - Terminal:
#: config.c:4602
msgid "Prompt about running processes on &close"
msgstr "Spør om pågående prosesser ved &avslutning"

#. __ Options - Terminal:
#. __ Context menu:
#: config.c:4607 wininput.c:581
msgid "Status Line"
msgstr ""

#: textprint.c:44 textprint.c:127
msgid "[Printing...] "
msgstr "[Skriver …] "

#. __ Options - Text: font chooser activation button
#: winctrls.c:935
msgid "&Select..."
msgstr "&Velg …"

#. __ Font chooser: title bar label
#: winctrls.c:1281
msgid "Font "
msgstr "Skrifttype "

#. __ Font chooser: button
#: winctrls.c:1283
msgid "&Apply"
msgstr "&Anvend"

#. __ Font chooser:
#: winctrls.c:1285
msgid "&Font:"
msgstr "&Skrifttype:"

#. __ Font chooser:
#: winctrls.c:1291
msgid "Sample"
msgstr "Prøveeksemplar"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1295 winctrls.c:1554 winctrls.c:1719
msgid "Ferqœm’4€"
msgstr "AaBbZzØø"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1312
msgid "Sc&ript:"
msgstr "Sk&ript:"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1314
msgid "<A>Show more fonts</A>"
msgstr "<A>Vis flere skrifttyper</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1319
msgid "Colour "
msgstr "Farge "

#. __ Colour chooser:
#: winctrls.c:1332 winctrls.c:1344
msgid "B&asic colours:"
msgstr "G&runnleggende farger:"

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Custom colours:"
msgstr "&Tilpassede farger:"

#. __ Colour chooser:
#: winctrls.c:1360
msgid "De&fine Custom Colours >>"
msgstr "De&finer tilpassede farger >>"

#. __ Colour chooser:
#: winctrls.c:1363
msgid "Colour"
msgstr "Farge"

#. __ Colour chooser:
#: winctrls.c:1365
msgid "|S&olid"
msgstr "|S&olid"

#. __ Colour chooser:
#: winctrls.c:1367
msgid "&Hue:"
msgstr "&Sky:"

#. __ Colour chooser:
#: winctrls.c:1370
msgid "&Sat:"
msgstr "&Met:"

#. __ Colour chooser:
#: winctrls.c:1372
msgid "&Lum:"
msgstr "&Lys:"

#. __ Colour chooser:
#: winctrls.c:1374
msgid "&Red:"
msgstr "&Rød:"

#. __ Colour chooser:
#: winctrls.c:1376
msgid "&Green:"
msgstr "&Grønn:"

#. __ Colour chooser:
#: winctrls.c:1378
msgid "&Blue:"
msgstr "&Blå:"

#. __ Colour chooser:
#: winctrls.c:1381
msgid "A&dd to Custom Colours"
msgstr "L&egg til i de tilpassede fargene"

#. __ Options: dialog title
#: windialog.c:266 windialog.c:839
msgid "Options"
msgstr "Innstillinger"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:268
msgid "available"
msgstr "tilgjengelig"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:783
msgid "100"
msgstr "100"

#: windialog.c:924 windialog.c:951
msgid "Error"
msgstr "Feil"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:300
msgid "Session switcher"
msgstr "Øktbytter"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:320
msgid "Session launcher"
msgstr "Øktstarter"

#: wininput.c:429 wininput.c:435
msgid "Ctrl+"
msgstr "Ctrl+"

#: wininput.c:430 wininput.c:436
msgid "Alt+"
msgstr "Alt+"

#: wininput.c:431 wininput.c:437
msgid "Shift+"
msgstr "Skift+"

#. __ System menu:
#: wininput.c:462
msgid "&Restore"
msgstr "&Gjenoppretting"

#. __ System menu:
#: wininput.c:464
msgid "&Move"
msgstr "&Flytt"

#. __ System menu:
#: wininput.c:466
msgid "&Size"
msgstr "&Størrelse"

#. __ System menu:
#: wininput.c:468
msgid "Mi&nimize"
msgstr "Mi&nimer"

#. __ System menu:
#: wininput.c:470
msgid "Ma&ximize"
msgstr "Ut&vid"

#. __ System menu:
#: wininput.c:472
msgid "&Close"
msgstr "&Lukk"

#. __ System menu:
#: wininput.c:477
msgid "New &Window"
msgstr "Nytt &Vindu"

#. __ System menu:
#: wininput.c:483
msgid "New &Tab"
msgstr "Ny &Fane"

#. __ Context menu:
#: wininput.c:490
msgid "&Copy"
msgstr "&Kopier"

#. __ Context menu:
#: wininput.c:509
msgid "&Paste "
msgstr "&Lim inn "

#. __ Context menu:
#: wininput.c:514
msgid "Copy → Paste"
msgstr "Kopier → Lim inn"

#. __ Context menu:
#: wininput.c:519
msgid "S&earch"
msgstr "S&øk"

#. __ Context menu:
#: wininput.c:526
msgid "&Log to File"
msgstr "&Loggfør til fil"

#. __ Context menu:
#: wininput.c:532
msgid "Character &Info"
msgstr "Tegn&info"

#. __ Context menu:
#: wininput.c:538
msgid "VT220 Keyboard"
msgstr "VT220-tastatur"

#. __ Context menu:
#: wininput.c:543
msgid "&Reset"
msgstr "&Tilbakestill"

#. __ Context menu:
#: wininput.c:551
msgid "&Default Size"
msgstr "&Standardstørrelse"

#. __ Context menu:
#: wininput.c:561
msgid "Scroll&bar"
msgstr "Skrolle&linje"

#. __ Context menu:
#: wininput.c:567
msgid "&Full Screen"
msgstr "&Fullskjerm"

#. __ Context menu:
#: wininput.c:573
msgid "Flip &Screen"
msgstr "Speilvend &skjermen"

#. __ System menu:
#: wininput.c:591 wininput.c:763
msgid "Copy &Title"
msgstr "Kopier &tittelen"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:593 wininput.c:747 wininput.c:765
msgid "&Options..."
msgstr "&Innstillinger …"

#. __ Context menu:
#: wininput.c:678
msgid "Ope&n"
msgstr "Åp&ne"

#. __ Context menu:
#: wininput.c:683
msgid "Copy as text"
msgstr "Kopier som tekst"

#. __ Context menu:
#: wininput.c:687
msgid "Copy as RTF"
msgstr "Kopier som RTF"

#. __ Context menu:
#: wininput.c:689
msgid "Copy as HTML text"
msgstr "Kopier som HTML-tekst"

#. __ Context menu:
#: wininput.c:691
msgid "Copy as HTML"
msgstr "Kopier som HTML"

#. __ Context menu:
#: wininput.c:693
msgid "Copy as HTML full"
msgstr "Kopier som full HTML"

#. __ Context menu:
#: wininput.c:700
msgid "Select &All"
msgstr "Velg &alle"

#. __ Context menu:
#: wininput.c:702
msgid "Save as &Image"
msgstr "Lagre som &bilde"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:714
msgid "HTML Screen Dump"
msgstr "HTML-skjermdump"

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:722
msgid "Clear Scrollback"
msgstr "Tøm skrollehistorikkbufferen"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:733
msgid "Send Break"
msgstr "Send «Break»"

#. __ Context menu, user commands
#: wininput.c:835
msgid "User commands"
msgstr "Brukerkommandoer"

#: wininput.c:1503 wininput.c:1524 wininput.c:1526 wininput.c:1528
#: wininput.c:1565
msgid "[NO SCROLL] "
msgstr ""

#: wininput.c:1516 wininput.c:1525 wininput.c:1530 wininput.c:1586
msgid "[SCROLL MODE] "
msgstr ""

#: winmain.c:3845
msgid "Processes are running in session:"
msgstr "Prosesser som pågår i denne økten:"

#: winmain.c:3846
msgid "Close anyway?"
msgstr "Vil du lukke det likevel?"

#: winmain.c:3870
msgid "Reset terminal?"
msgstr "Tilbakestill terminal?"

#: winmain.c:4100
msgid "Try '--help' for more information"
msgstr "Prøv med '--help' for mere informasjon"

#: winmain.c:4108
msgid "Could not load icon"
msgstr "Klarte ikke å laste inn ikonet"

#: winmain.c:6402
msgid "Usage:"
msgstr "Bruk:"

#: winmain.c:6403
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[OPTION]... [ PROGRAM [ARG]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:6406
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"Start en ny terminaløkt som kjører det valgte programmet eller brukerens "
"skall.\n"
"Dersom en strek blir brukt i stedet for et problem, start opp skallet som et "
"påloggingsskall.\n"
"\n"
"Innstillinger.\n"
"  -c, --config FIL   Last inn den spesifiserte oppsettsfilen (sammenlign "
"med. -C eller -p TemaFil)\n"
"  -e, --exec …     Behandle gjenværende argumenter som kommandoer som skal "
"kjøres\n"
"  -h, --hold never|start|error|always  Behold vinduet åpent etter at "
"kommandoen har kjørt seg ferdig\n"
"  -p, --position X,Y   Åpne et vindu ved de spesifiserte koordinatene\n"
"  -p, --position center|left|right|top|bottom  Åpne et vindu ved den "
"spesifiserte posisjonen\n"
"  -p, position --position @N    Åpne vinduet på skjerm N\n"
"  -s, --size KOLONNER,REKKER  Bestem skjermstørrelsen i tegn (også "
"KOLONNERxREKKER)\n"
"  -s, --size maxwidth|maxheight  Bestem maks-skjermstørrelsen i det gitte "
"skjermforholdet\n"
"  -t, --title TITTEL    Bestem vindustittelen (standard: den benyttede "
"kommandoen) (sammenlign med -T)\n"
"  -w, --window normal|min|max|full|hide  Bestem vinduets starttilstand\n"
"  -i, --icon FIL[,IX]  Last inn vindusikon fra en fil, om ønskelig med "
"indeks\n"
"  -l, --log FIL|-    Loggfør utdataen til en fil eller stdout\n"
"     --nobidi|--nortl  Skru av bidi (høyre-til-venstre-støtte)\n"
"  -o, --option INNST=VERDI  Bestem/Overstyr oppsettsfilinnstillingen med den "
"oppgitte verdien\n"
"  -B, --Border frame|void  Bruk en tynn eller ingen vinduskant\n"
"  -R, --Report s|o    Rapporter vindusposisjonen (kort/lang) etter "
"avslutning\n"
"     --nopin      Gjør at denne økten ikke kan festes fast til "
"verktøylinjen\n"
"  -D, --daemon     Start en ny økt med Windows-snarveistasten\n"
"  -H, --help        Vis hjelp og avslutt\n"
"  -V, --version     Skriv versjonsinformasjon og avslutt\n"
"Se bruksanvisningssiden for ytterligere ledetekstinnstillinger og oppsett.\n"

#: winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "WSL distribution '%s' not found"
msgstr "WSL-distribusjonen '%s' ble ikke funnet"

#: winmain.c:6731
msgid "Duplicate option '%s'"
msgstr "Duplikatinnstilling '%s'"

#: winmain.c:6739 winmain.c:6830
msgid "Unknown option '%s'"
msgstr "Ukjent alternativ '%s'"

#: winmain.c:6741
msgid "Option '%s' requires an argument"
msgstr "'%s'-innstillingen krever et argument"

#: winmain.c:6768
msgid "Syntax error in position argument '%s'"
msgstr "Syntaksfeil i posisjonsargumentet '%s'"

#: winmain.c:6779
msgid "Syntax error in size argument '%s'"
msgstr "Syntaksfeil i størrelsesargumentet '%s'"

#: winmain.c:6939
msgid "Syntax error in geometry argument '%s'"
msgstr "Syntaksfeil i geometriargumentet '%s'"

#: winmain.c:7038
msgid "Mintty could not detach from caller, starting anyway"
msgstr ""
"Mintty klarte ikke å løsrive seg fra påkalleren, men starter opp likevel"

#: winmain.c:7387
msgid "Using default title due to invalid characters in program name"
msgstr "Benytter standardtittelen på grunn av ugyldige tegn i programnavnet"

#: winsearch.c:232
msgid "◀"
msgstr "◀"

#: winsearch.c:233
msgid "▶"
msgstr "▶"

#: winsearch.c:234
msgid "X"
msgstr "X"

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:165
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "Linjeskille: %d, Fet: %s, Understreket: %s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:167
msgid "font"
msgstr "skrifttype"

#. __ Options - Text: font properties: value affected by option
#: wintext.c:169
msgid "manual"
msgstr "manual"

#: wintext.c:544
msgid "Font not found, using system substitute"
msgstr "Skrifttypen ble ikke funnet, benytter systemerstatningen"

#: wintext.c:559
msgid "Font has limited support for character ranges"
msgstr "Skrifttypen har begrenset støtte for tegnseksjoner"

#: wintext.c:702
msgid "Font installation corrupt, using system substitute"
msgstr "Skrifttypeinstallasjonen er korrumpert, benytter systemerstatningen"

#: wintext.c:756
msgid "Font does not support system locale"
msgstr "Skrifttypen støtter ikke systemets regioninnstilling"

#: appinfo.h:64
msgid "There is no warranty, to the extent permitted by law."
msgstr "Det gis ingen forsikringer, i den grad loven tillater det."

#. __ %s: WEBSITE (URL)
#: appinfo.h:69
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"Vennligst meld inn bugger eller forespør forbedringer gjennom rapportlisten "
"på mintty sin prosjektside, som er hos\n"
"%s.\n"
"Se også wikien der for flere tips, takk, og krediteringer."
