/* Definition for processor table alias flags.
   Copyright (C) 2001-2025 Free Software Foundation, Inc.

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

DEF_PTA(3DNOW)
DEF_PTA(3DNOW_A)
DEF_PTA(64BIT)
DEF_PTA(ABM)
DEF_PTA(AES)
DEF_PTA(AVX)
DEF_PTA(BMI)
DEF_PTA(CX16)
DEF_PTA(F16C)
DEF_PTA(FMA)
DEF_PTA(FMA4)
DEF_PTA(FSGSBASE)
DEF_PTA(LWP)
DEF_PTA(LZCNT)
DEF_PTA(MMX)
DEF_PTA(MOVBE)
DEF_PTA(NO_SAHF)
DEF_PTA(PCLMUL)
DEF_PTA(POPCNT)
DEF_PTA(PREFETCH_SSE)
DEF_PTA(RDRND)
DEF_PTA(SSE)
DEF_PTA(SSE2)
DEF_PTA(SSE3)
DEF_PTA(SSE4_1)
DEF_PTA(SSE4_2)
DEF_PTA(SSE4A)
DEF_PTA(SSSE3)
DEF_PTA(TBM)
DEF_PTA(XOP)
DEF_PTA(AVX2)
DEF_PTA(BMI2)
DEF_PTA(RTM)
DEF_PTA(HLE)
DEF_PTA(PRFCHW)
DEF_PTA(RDSEED)
DEF_PTA(ADX)
DEF_PTA(FXSR)
DEF_PTA(XSAVE)
DEF_PTA(XSAVEOPT)
DEF_PTA(AVX512F)
DEF_PTA(AVX512CD)
DEF_PTA(NO_TUNE)
DEF_PTA(SHA)
DEF_PTA(CLFLUSHOPT)
DEF_PTA(XSAVEC)
DEF_PTA(XSAVES)
DEF_PTA(AVX512DQ)
DEF_PTA(AVX512BW)
DEF_PTA(AVX512VL)
DEF_PTA(AVX512IFMA)
DEF_PTA(AVX512VBMI)
DEF_PTA(CLWB)
DEF_PTA(MWAITX)
DEF_PTA(CLZERO)
DEF_PTA(NO_80387)
DEF_PTA(PKU)
DEF_PTA(AVX512VPOPCNTDQ)
DEF_PTA(SGX)
DEF_PTA(AVX512VNNI)
DEF_PTA(GFNI)
DEF_PTA(VAES)
DEF_PTA(AVX512VBMI2)
DEF_PTA(VPCLMULQDQ)
DEF_PTA(AVX512BITALG)
DEF_PTA(RDPID)
DEF_PTA(PCONFIG)
DEF_PTA(WBNOINVD)
DEF_PTA(AVX512VP2INTERSECT)
DEF_PTA(PTWRITE)
DEF_PTA(AVX512BF16)
DEF_PTA(WAITPKG)
DEF_PTA(MOVDIRI)
DEF_PTA(MOVDIR64B)
DEF_PTA(ENQCMD)
DEF_PTA(CLDEMOTE)
DEF_PTA(SERIALIZE)
DEF_PTA(TSXLDTRK)
DEF_PTA(AMX_TILE)
DEF_PTA(AMX_INT8)
DEF_PTA(AMX_BF16)
DEF_PTA(UINTR)
DEF_PTA(HRESET)
DEF_PTA(KL)
DEF_PTA(WIDEKL)
DEF_PTA(AVXVNNI)
DEF_PTA(AVX512FP16)
DEF_PTA(AVXIFMA)
DEF_PTA(AVXVNNIINT8)
DEF_PTA(AVXNECONVERT)
DEF_PTA(CMPCCXADD)
DEF_PTA(AMX_FP16)
DEF_PTA(PREFETCHI)
DEF_PTA(RAOINT)
DEF_PTA(AMX_COMPLEX)
DEF_PTA(AVXVNNIINT16)
DEF_PTA(SM3)
DEF_PTA(SHA512)
DEF_PTA(SM4)
DEF_PTA(APX_F)
DEF_PTA(USER_MSR)
DEF_PTA(EVEX512)
DEF_PTA(AVX10_1_256)
DEF_PTA(AVX10_1)
DEF_PTA(AVX10_2)
DEF_PTA(AMX_AVX512)
DEF_PTA(AMX_TF32)
DEF_PTA(AMX_TRANSPOSE)
DEF_PTA(AMX_FP8)
DEF_PTA(MOVRS)
DEF_PTA(AMX_MOVRS)
