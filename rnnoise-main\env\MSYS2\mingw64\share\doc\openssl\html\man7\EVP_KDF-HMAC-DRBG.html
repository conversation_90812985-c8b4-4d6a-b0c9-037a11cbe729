<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_KDF-HMAC-DRBG</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
      <li><a href="#Supported-parameters">Supported parameters</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_KDF-HMAC-DRBG - The HMAC DRBG DETERMINISTIC EVP_KDF implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for a deterministic HMAC DRBG using the <b>EVP_KDF</b> API. This is similar to <a href="../man7/EVP_RAND-HMAC-DRBG.html">EVP_RAND-HMAC-DRBG(7)</a>, but uses fixed values for its entropy and nonce values. This is used to generate deterministic nonce value required by ECDSA and DSA (as defined in RFC 6979).</p>

<h2 id="Identity">Identity</h2>

<p>&quot;HMAC-DRBG-KDF&quot; is the name for this implementation; it can be used with the EVP_KDF_fetch() function.</p>

<h2 id="Supported-parameters">Supported parameters</h2>

<p>The supported parameters are:</p>

<dl>

<dt id="digest-OSSL_DRBG_PARAM_DIGEST-UTF8-string">&quot;digest&quot; (<b>OSSL_DRBG_PARAM_DIGEST</b>) &lt;UTF8 string&gt;</dt>
<dd>

</dd>
<dt id="properties-OSSL_DRBG_PARAM_PROPERTIES-UTF8-string">&quot;properties&quot; (<b>OSSL_DRBG_PARAM_PROPERTIES</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>These parameters work as described in <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a>.</p>

</dd>
<dt id="entropy-OSSL_KDF_PARAM_HMACDRBG_ENTROPY-octet-string">&quot;entropy&quot; (<b>OSSL_KDF_PARAM_HMACDRBG_ENTROPY</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the entropy bytes supplied to the HMAC-DRBG.</p>

</dd>
<dt id="nonce-OSSL_KDF_PARAM_HMACDRBG_NONCE-octet-string">&quot;nonce&quot; (<b>OSSL_KDF_PARAM_HMACDRBG_NONCE</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the nonce bytes supplied to the HMAC-DRBG.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>A context for KDF HMAC DRBG can be obtained by calling:</p>

<pre><code>EVP_KDF *kdf = EVP_KDF_fetch(NULL, &quot;HMAC-DRBG-KDF&quot;, NULL);
EVP_KDF_CTX *kdf_ctx = EVP_KDF_CTX_new(kdf, NULL);</code></pre>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>RFC 6979</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_KDF.html">EVP_KDF(3)</a>, <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The EVP_KDF-HMAC-DRBG functionality was added in OpenSSL 3.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2022-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


