/*
 * Copyright 2009 <PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "d3d10.idl";
import "d3d10shader.idl";

interface ID3D10EffectConstantBuffer;
interface ID3D10EffectBlendVariable;
interface ID3D10EffectDepthStencilVariable;
interface ID3D10EffectDepthStencilViewVariable;
interface ID3D10EffectMatrixVariable;
interface ID3D10EffectPass;
interface ID3D10EffectRasterizerVariable;
interface ID3D10EffectRenderTargetViewVariable;
interface ID3D10EffectSamplerVariable;
interface ID3D10EffectScalarVariable;
interface ID3D10EffectShaderVariable;
interface ID3D10EffectShaderResourceVariable;
interface ID3D10EffectStringVariable;
interface ID3D10EffectVectorVariable;

#ifndef D3D10_BYTES_FROM_BITS
#define D3D10_BYTES_FROM_BITS(x) (((x) + 7) >> 3)
#endif
cpp_quote("#ifndef D3D10_BYTES_FROM_BITS")
cpp_quote("#define D3D10_BYTES_FROM_BITS(x) (((x) + 7) >> 3)")
cpp_quote("#endif")

const unsigned int D3D10_EFFECT_VARIABLE_POOLED                 = 0x1;
const unsigned int D3D10_EFFECT_VARIABLE_ANNOTATION             = 0x2;
const unsigned int D3D10_EFFECT_VARIABLE_EXPLICIT_BIND_POINT    = 0x4;

typedef enum _D3D10_DEVICE_STATE_TYPES
{
    D3D10_DST_SO_BUFFERS = 1,
    D3D10_DST_OM_RENDER_TARGETS,
    D3D10_DST_OM_DEPTH_STENCIL_STATE,
    D3D10_DST_OM_BLEND_STATE,
    D3D10_DST_VS,
    D3D10_DST_VS_SAMPLERS,
    D3D10_DST_VS_SHADER_RESOURCES,
    D3D10_DST_VS_CONSTANT_BUFFERS,
    D3D10_DST_GS,
    D3D10_DST_GS_SAMPLERS,
    D3D10_DST_GS_SHADER_RESOURCES,
    D3D10_DST_GS_CONSTANT_BUFFERS,
    D3D10_DST_PS,
    D3D10_DST_PS_SAMPLERS,
    D3D10_DST_PS_SHADER_RESOURCES,
    D3D10_DST_PS_CONSTANT_BUFFERS,
    D3D10_DST_IA_VERTEX_BUFFERS,
    D3D10_DST_IA_INDEX_BUFFER,
    D3D10_DST_IA_INPUT_LAYOUT,
    D3D10_DST_IA_PRIMITIVE_TOPOLOGY,
    D3D10_DST_RS_VIEWPORTS,
    D3D10_DST_RS_SCISSOR_RECTS,
    D3D10_DST_RS_RASTERIZER_STATE,
    D3D10_DST_PREDICATION,
} D3D10_DEVICE_STATE_TYPES;

typedef struct _D3D10_EFFECT_TYPE_DESC
{
    const char *TypeName;
    D3D10_SHADER_VARIABLE_CLASS Class;
    D3D10_SHADER_VARIABLE_TYPE Type;
    UINT Elements;
    UINT Members;
    UINT Rows;
    UINT Columns;
    UINT PackedSize;
    UINT UnpackedSize;
    UINT Stride;
} D3D10_EFFECT_TYPE_DESC;

typedef struct _D3D10_EFFECT_VARIABLE_DESC
{
    const char *Name;
    const char *Semantic;
    UINT Flags;
    UINT Annotations;
    UINT BufferOffset;
    UINT ExplicitBindPoint;
} D3D10_EFFECT_VARIABLE_DESC;

typedef struct _D3D10_TECHNIQUE_DESC
{
    const char *Name;
    UINT Passes;
    UINT Annotations;
} D3D10_TECHNIQUE_DESC;

typedef struct _D3D10_STATE_BLOCK_MASK
{
    BYTE VS;
    BYTE VSSamplers[D3D10_BYTES_FROM_BITS(D3D10_COMMONSHADER_SAMPLER_SLOT_COUNT)];
    BYTE VSShaderResources[D3D10_BYTES_FROM_BITS(D3D10_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT)];
    BYTE VSConstantBuffers[D3D10_BYTES_FROM_BITS(D3D10_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT)];
    BYTE GS;
    BYTE GSSamplers[D3D10_BYTES_FROM_BITS(D3D10_COMMONSHADER_SAMPLER_SLOT_COUNT)];
    BYTE GSShaderResources[D3D10_BYTES_FROM_BITS(D3D10_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT)];
    BYTE GSConstantBuffers[D3D10_BYTES_FROM_BITS(D3D10_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT)];
    BYTE PS;
    BYTE PSSamplers[D3D10_BYTES_FROM_BITS(D3D10_COMMONSHADER_SAMPLER_SLOT_COUNT)];
    BYTE PSShaderResources[D3D10_BYTES_FROM_BITS(D3D10_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT)];
    BYTE PSConstantBuffers[D3D10_BYTES_FROM_BITS(D3D10_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT)];
    BYTE IAVertexBuffers[D3D10_BYTES_FROM_BITS(D3D10_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT)];
    BYTE IAIndexBuffer;
    BYTE IAInputLayout;
    BYTE IAPrimitiveTopology;
    BYTE OMRenderTargets;
    BYTE OMDepthStencilState;
    BYTE OMBlendState;
    BYTE RSViewports;
    BYTE RSScissorRects;
    BYTE RSRasterizerState;
    BYTE SOBuffers;
    BYTE Predication;
} D3D10_STATE_BLOCK_MASK;

typedef struct _D3D10_EFFECT_DESC
{
    BOOL IsChildEffect;
    UINT ConstantBuffers;
    UINT SharedConstantBuffers;
    UINT GlobalVariables;
    UINT SharedGlobalVariables;
    UINT Techniques;
} D3D10_EFFECT_DESC;

typedef struct _D3D10_EFFECT_SHADER_DESC
{
    const BYTE *pInputSignature;
    BOOL IsInline;
    const BYTE *pBytecode;
    UINT BytecodeLength;
    const char *SODecl;
    UINT NumInputSignatureEntries;
    UINT NumOutputSignatureEntries;
} D3D10_EFFECT_SHADER_DESC;

typedef struct _D3D10_PASS_DESC
{
    const char *Name;
    UINT Annotations;
    BYTE *pIAInputSignature;
    SIZE_T IAInputSignatureSize;
    UINT StencilRef;
    UINT SampleMask;
    FLOAT BlendFactor[4];
} D3D10_PASS_DESC;

typedef struct _D3D10_PASS_SHADER_DESC
{
    ID3D10EffectShaderVariable *pShaderVariable;
    UINT ShaderIndex;
} D3D10_PASS_SHADER_DESC;

const unsigned int D3D10_EFFECT_COMPILE_CHILD_EFFECT    = 0x0001;
const unsigned int D3D10_EFFECT_COMPILE_ALLOW_SLOW_OPS  = 0x0002;
const unsigned int D3D10_EFFECT_SINGLE_THREADED         = 0x0008;

[
    local,
    object,
    uuid(4e9e1ddc-cd9d-4772-a837-00180b9b88fd),
]
interface ID3D10EffectType
{
    BOOL IsValid();
    HRESULT GetDesc(D3D10_EFFECT_TYPE_DESC *desc);
    ID3D10EffectType *GetMemberTypeByIndex(UINT index);
    ID3D10EffectType *GetMemberTypeByName(const char *name);
    ID3D10EffectType *GetMemberTypeBySemantic(const char *semantic);
    const char *GetMemberName(UINT index);
    const char *GetMemberSemantic(UINT index);
}

[
    local,
    object,
    uuid(ae897105-00e6-45bf-bb8e-281dd6db8e1b),
]
interface ID3D10EffectVariable
{
    BOOL IsValid();
    ID3D10EffectType *GetType();
    HRESULT GetDesc(D3D10_EFFECT_VARIABLE_DESC *desc);
    ID3D10EffectVariable *GetAnnotationByIndex(UINT index);
    ID3D10EffectVariable *GetAnnotationByName(const char *name);
    ID3D10EffectVariable *GetMemberByIndex(UINT index);
    ID3D10EffectVariable *GetMemberByName(const char *name);
    ID3D10EffectVariable *GetMemberBySemantic(const char *semantic);
    ID3D10EffectVariable *GetElement(UINT index);
    ID3D10EffectConstantBuffer *GetParentConstantBuffer();
    ID3D10EffectScalarVariable *AsScalar();
    ID3D10EffectVectorVariable *AsVector();
    ID3D10EffectMatrixVariable *AsMatrix();
    ID3D10EffectStringVariable *AsString();
    ID3D10EffectShaderResourceVariable *AsShaderResource();
    ID3D10EffectRenderTargetViewVariable *AsRenderTargetView();
    ID3D10EffectDepthStencilViewVariable *AsDepthStencilView();
    ID3D10EffectConstantBuffer *AsConstantBuffer();
    ID3D10EffectShaderVariable *AsShader();
    ID3D10EffectBlendVariable *AsBlend();
    ID3D10EffectDepthStencilVariable *AsDepthStencil();
    ID3D10EffectRasterizerVariable *AsRasterizer();
    ID3D10EffectSamplerVariable *AsSampler();
    HRESULT SetRawValue(void *data, UINT offset, UINT count);
    HRESULT GetRawValue(void *data, UINT offset, UINT count);
}

[
    local,
    object,
    uuid(56648f4d-cc8b-4444-a5ad-b5a3d76e91b3),
]
interface ID3D10EffectConstantBuffer : ID3D10EffectVariable
{
    HRESULT SetConstantBuffer(ID3D10Buffer *buffer);
    HRESULT GetConstantBuffer(ID3D10Buffer **buffer);
    HRESULT SetTextureBuffer(ID3D10ShaderResourceView *view);
    HRESULT GetTextureBuffer(ID3D10ShaderResourceView **view);
}

[
    local,
    object,
    uuid(00e48f7b-d2c8-49e8-a86c-022dee53431f),
]
interface ID3D10EffectScalarVariable : ID3D10EffectVariable
{
    HRESULT SetFloat(float value);
    HRESULT GetFloat(float *value);
    HRESULT SetFloatArray(float *values, UINT offset, UINT count);
    HRESULT GetFloatArray(float *values, UINT offset, UINT count);
    HRESULT SetInt(int value);
    HRESULT GetInt(int *value);
    HRESULT SetIntArray(int *values, UINT offset, UINT count);
    HRESULT GetIntArray(int *values, UINT offset, UINT count);
    HRESULT SetBool(BOOL value);
    HRESULT GetBool(BOOL *value);
    HRESULT SetBoolArray(BOOL *values, UINT offset, UINT count);
    HRESULT GetBoolArray(BOOL *values, UINT offset, UINT count);
}

[
    local,
    object,
    uuid(62b98c44-1f82-4c67-bcd0-72cf8f217e81),
]
interface ID3D10EffectVectorVariable : ID3D10EffectVariable
{
    HRESULT SetBoolVector(BOOL *value);
    HRESULT SetIntVector(int *value);
    HRESULT SetFloatVector(float *value);
    HRESULT GetBoolVector(BOOL *value);
    HRESULT GetIntVector(int *value);
    HRESULT GetFloatVector(float *value);
    HRESULT SetBoolVectorArray(BOOL *values, UINT offset, UINT count);
    HRESULT SetIntVectorArray(int *values, UINT offset, UINT count);
    HRESULT SetFloatVectorArray(float *values, UINT offset, UINT count);
    HRESULT GetBoolVectorArray(BOOL *values, UINT offset, UINT count);
    HRESULT GetIntVectorArray(int *values, UINT offset, UINT count);
    HRESULT GetFloatVectorArray(float *values, UINT offset, UINT count);
}

[
    local,
    object,
    uuid(50666c24-b82f-4eed-a172-5b6e7e8522e0),
]
interface ID3D10EffectMatrixVariable : ID3D10EffectVariable
{
    HRESULT SetMatrix(float *data);
    HRESULT GetMatrix(float *data);
    HRESULT SetMatrixArray(float *data, UINT offset, UINT count);
    HRESULT GetMatrixArray(float *data, UINT offset, UINT count);
    HRESULT SetMatrixTranspose(float *data);
    HRESULT GetMatrixTranspose(float *data);
    HRESULT SetMatrixTransposeArray(float *data, UINT offset, UINT count);
    HRESULT GetMatrixTransposeArray(float *data, UINT offset, UINT count);
}

[
    local,
    object,
    uuid(71417501-8df9-4e0a-a78a-255f9756baff),
]
interface ID3D10EffectStringVariable : ID3D10EffectVariable
{
    HRESULT GetString(const char **str);
    HRESULT GetStringArray(const char **strs, UINT offset, UINT count);
}

[
    local,
    object,
    uuid(c0a7157b-d872-4b1d-8073-efc2acd4b1fc),
]
interface ID3D10EffectShaderResourceVariable : ID3D10EffectVariable
{
    HRESULT SetResource(ID3D10ShaderResourceView *resource);
    HRESULT GetResource(ID3D10ShaderResourceView **resource);
    HRESULT SetResourceArray(ID3D10ShaderResourceView **resources, UINT offset, UINT count);
    HRESULT GetResourceArray(ID3D10ShaderResourceView **resources, UINT offset, UINT count);
}

[
    local,
    object,
    uuid(28ca0cc3-c2c9-40bb-b57f-67b737122b17),
]
interface ID3D10EffectRenderTargetViewVariable : ID3D10EffectVariable
{
    HRESULT SetRenderTarget(ID3D10RenderTargetView *view);
    HRESULT GetRenderTarget(ID3D10RenderTargetView **view);
    HRESULT SetRenderTargetArray(ID3D10RenderTargetView **views, UINT offset, UINT count);
    HRESULT GetRenderTargetArray(ID3D10RenderTargetView **views, UINT offset, UINT count);
}

[
    local,
    object,
    uuid(3e02c918-cc79-4985-b622-2d92ad701623),
]
interface ID3D10EffectDepthStencilViewVariable : ID3D10EffectVariable
{
    HRESULT SetDepthStencil(ID3D10DepthStencilView *view);
    HRESULT GetDepthStencil(ID3D10DepthStencilView **view);
    HRESULT SetDepthStencilArray(ID3D10DepthStencilView **views, UINT offset, UINT count);
    HRESULT GetDepthStencilArray(ID3D10DepthStencilView **views, UINT offset, UINT count);
}

[
    local,
    object,
    uuid(80849279-c799-4797-8c33-0407a07d9e06),
]
interface ID3D10EffectShaderVariable : ID3D10EffectVariable
{
    HRESULT GetShaderDesc(UINT index, D3D10_EFFECT_SHADER_DESC *desc);
    HRESULT GetVertexShader(UINT index, ID3D10VertexShader **shader);
    HRESULT GetGeometryShader(UINT index, ID3D10GeometryShader **shader);
    HRESULT GetPixelShader(UINT index, ID3D10PixelShader **shader);
    HRESULT GetInputSignatureElementDesc(UINT shader_index, UINT element_index,
            D3D10_SIGNATURE_PARAMETER_DESC *desc);
    HRESULT GetOutputSignatureElementDesc(UINT shader_index, UINT element_index,
            D3D10_SIGNATURE_PARAMETER_DESC *desc);
}

[
    local,
    object,
    uuid(1fcd2294-df6d-4eae-86b3-0e9160cfb07b),
]
interface ID3D10EffectBlendVariable : ID3D10EffectVariable
{
    HRESULT GetBlendState(UINT index, ID3D10BlendState **blend_state);
    HRESULT GetBackingStore(UINT index, D3D10_BLEND_DESC *desc);
}

[
    local,
    object,
    uuid(af482368-330a-46a5-9a5c-01c71af24c8d),
]
interface ID3D10EffectDepthStencilVariable : ID3D10EffectVariable
{
    HRESULT GetDepthStencilState(UINT index, ID3D10DepthStencilState **depth_stencil_state);
    HRESULT GetBackingStore(UINT index, D3D10_DEPTH_STENCIL_DESC *desc);
}

[
    local,
    object,
    uuid(21af9f0e-4d94-4ea9-9785-2cb76b8c0b34),
]
interface ID3D10EffectRasterizerVariable : ID3D10EffectVariable
{
    HRESULT GetRasterizerState(UINT index, ID3D10RasterizerState **rasterizer_state);
    HRESULT GetBackingStore(UINT index, D3D10_RASTERIZER_DESC *desc);
}

[
    local,
    object,
    uuid(6530d5c7-07e9-4271-a418-e7ce4bd1e480),
]
interface ID3D10EffectSamplerVariable : ID3D10EffectVariable
{
    HRESULT GetSampler(UINT index, ID3D10SamplerState **sampler);
    HRESULT GetBackingStore(UINT index, D3D10_SAMPLER_DESC *desc);
}

[
    local,
    object,
    uuid(db122ce8-d1c9-4292-b237-24ed3de8b175),
]
interface ID3D10EffectTechnique
{
    BOOL IsValid();
    HRESULT GetDesc(D3D10_TECHNIQUE_DESC *desc);
    ID3D10EffectVariable *GetAnnotationByIndex(UINT index);
    ID3D10EffectVariable *GetAnnotationByName(const char *name);
    ID3D10EffectPass *GetPassByIndex(UINT index);
    ID3D10EffectPass *GetPassByName(const char *name);
    HRESULT ComputeStateBlockMask(D3D10_STATE_BLOCK_MASK *mask);
}

[
    local,
    object,
    uuid(51b0ca8b-ec0b-4519-870d-8ee1cb5017c7),
]
interface ID3D10Effect : IUnknown
{
    BOOL IsValid();
    BOOL IsPool();
    HRESULT GetDevice(ID3D10Device **device);
    HRESULT GetDesc(D3D10_EFFECT_DESC *desc);
    ID3D10EffectConstantBuffer *GetConstantBufferByIndex(UINT index);
    ID3D10EffectConstantBuffer *GetConstantBufferByName(const char *name);
    ID3D10EffectVariable *GetVariableByIndex(UINT index);
    ID3D10EffectVariable *GetVariableByName(const char *name);
    ID3D10EffectVariable *GetVariableBySemantic(const char *semantic);
    ID3D10EffectTechnique *GetTechniqueByIndex(UINT index);
    ID3D10EffectTechnique *GetTechniqueByName(const char *name);
    HRESULT Optimize();
    BOOL IsOptimized();
}

[
    local,
    object,
    uuid(9537ab04-3250-412e-8213-fcd2f8677933),
]
interface ID3D10EffectPool : IUnknown
{
    ID3D10Effect *AsEffect();
}

[
    local,
    object,
    uuid(5cfbeb89-1a06-46e0-b282-e3f9bfa36a54),
]
interface ID3D10EffectPass
{
    BOOL IsValid();
    HRESULT GetDesc(D3D10_PASS_DESC *desc);
    HRESULT GetVertexShaderDesc(D3D10_PASS_SHADER_DESC *desc);
    HRESULT GetGeometryShaderDesc(D3D10_PASS_SHADER_DESC *desc);
    HRESULT GetPixelShaderDesc(D3D10_PASS_SHADER_DESC *desc);
    ID3D10EffectVariable *GetAnnotationByIndex(UINT index);
    ID3D10EffectVariable *GetAnnotationByName(const char *name);
    HRESULT Apply(UINT flags);
    HRESULT ComputeStateBlockMask(D3D10_STATE_BLOCK_MASK *mask);
}

[
    local,
    object,
    uuid(0803425a-57f5-4dd6-9465-a87570834a08),
]
interface ID3D10StateBlock : IUnknown
{
    HRESULT Capture();
    HRESULT Apply();
    HRESULT ReleaseAllDeviceObjects();
    HRESULT GetDevice(ID3D10Device **device);
}

HRESULT __stdcall D3D10CompileEffectFromMemory(void *data, SIZE_T data_size, const char *filename,
        const D3D10_SHADER_MACRO *defines, ID3D10Include *include, UINT hlsl_flags, UINT fx_flags,
        ID3D10Blob **effect, ID3D10Blob **errors);
HRESULT __stdcall D3D10CreateEffectFromMemory(void *data, SIZE_T data_size, UINT flags,
        ID3D10Device *device, ID3D10EffectPool *effect_pool, ID3D10Effect **effect);
HRESULT __stdcall D3D10CreateEffectPoolFromMemory(void *data, SIZE_T data_size, UINT fx_flags,
        ID3D10Device *device, ID3D10EffectPool **effect_pool);
HRESULT __stdcall D3D10CreateStateBlock(ID3D10Device *device,
        D3D10_STATE_BLOCK_MASK *mask, ID3D10StateBlock **stateblock);

HRESULT __stdcall D3D10StateBlockMaskDifference(D3D10_STATE_BLOCK_MASK *mask_x,
        D3D10_STATE_BLOCK_MASK *mask_y, D3D10_STATE_BLOCK_MASK *result);
HRESULT __stdcall D3D10StateBlockMaskDisableAll(D3D10_STATE_BLOCK_MASK *mask);
HRESULT __stdcall D3D10StateBlockMaskDisableCapture(D3D10_STATE_BLOCK_MASK *mask,
        D3D10_DEVICE_STATE_TYPES state_type, UINT start_idx, UINT count);
HRESULT __stdcall D3D10StateBlockMaskEnableAll(D3D10_STATE_BLOCK_MASK *mask);
HRESULT __stdcall D3D10StateBlockMaskEnableCapture(D3D10_STATE_BLOCK_MASK *mask,
        D3D10_DEVICE_STATE_TYPES state_type, UINT start_idx, UINT count);
BOOL __stdcall D3D10StateBlockMaskGetSetting(D3D10_STATE_BLOCK_MASK *mask,
        D3D10_DEVICE_STATE_TYPES state_type, UINT idx);
HRESULT __stdcall D3D10StateBlockMaskIntersect(D3D10_STATE_BLOCK_MASK *mask_x,
        D3D10_STATE_BLOCK_MASK *mask_y, D3D10_STATE_BLOCK_MASK *result);
HRESULT __stdcall D3D10StateBlockMaskUnion(D3D10_STATE_BLOCK_MASK *mask_x,
        D3D10_STATE_BLOCK_MASK *mask_y, D3D10_STATE_BLOCK_MASK *result);
