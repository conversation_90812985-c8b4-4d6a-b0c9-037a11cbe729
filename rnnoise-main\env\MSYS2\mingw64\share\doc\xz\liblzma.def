EXPORTS
    lzma_alone_decoder
    lzma_alone_encoder
    lzma_auto_decoder
    lzma_bcj_arm64_decode
    lzma_bcj_arm64_encode
    lzma_bcj_riscv_decode
    lzma_bcj_riscv_encode
    lzma_bcj_x86_decode
    lzma_bcj_x86_encode
    lzma_block_buffer_bound
    lzma_block_buffer_decode
    lzma_block_buffer_encode
    lzma_block_compressed_size
    lzma_block_decoder
    lzma_block_encoder
    lzma_block_header_decode
    lzma_block_header_encode
    lzma_block_header_size
    lzma_block_total_size
    lzma_block_uncomp_encode
    lzma_block_unpadded_size
    lzma_check_is_supported
    lzma_check_size
    lzma_code
    lzma_cputhreads
    lzma_crc32
    lzma_crc64
    lzma_easy_buffer_encode
    lzma_easy_decoder_memusage
    lzma_easy_encoder
    lzma_easy_encoder_memusage
    lzma_end
    lzma_file_info_decoder
    lzma_filter_decoder_is_supported
    lzma_filter_encoder_is_supported
    lzma_filter_flags_decode
    lzma_filter_flags_encode
    lzma_filter_flags_size
    lzma_filters_copy
    lzma_filters_free
    lzma_filters_update
    lzma_get_check
    lzma_get_progress
    lzma_index_append
    lzma_index_block_count
    lzma_index_buffer_decode
    lzma_index_buffer_encode
    lzma_index_cat
    lzma_index_checks
    lzma_index_decoder
    lzma_index_dup
    lzma_index_encoder
    lzma_index_end
    lzma_index_file_size
    lzma_index_hash_append
    lzma_index_hash_decode
    lzma_index_hash_end
    lzma_index_hash_init
    lzma_index_hash_size
    lzma_index_init
    lzma_index_iter_init
    lzma_index_iter_locate
    lzma_index_iter_next
    lzma_index_iter_rewind
    lzma_index_memusage
    lzma_index_memused
    lzma_index_size
    lzma_index_stream_count
    lzma_index_stream_flags
    lzma_index_stream_padding
    lzma_index_stream_size
    lzma_index_total_size
    lzma_index_uncompressed_size
    lzma_lzip_decoder
    lzma_lzma_preset
    lzma_memlimit_get
    lzma_memlimit_set
    lzma_memusage
    lzma_mf_is_supported
    lzma_microlzma_decoder
    lzma_microlzma_encoder
    lzma_mode_is_supported
    lzma_mt_block_size
    lzma_physmem
    lzma_properties_decode
    lzma_properties_encode
    lzma_properties_size
    lzma_raw_buffer_decode
    lzma_raw_buffer_encode
    lzma_raw_decoder
    lzma_raw_decoder_memusage
    lzma_raw_encoder
    lzma_raw_encoder_memusage
    lzma_str_from_filters
    lzma_str_list_filters
    lzma_str_to_filters
    lzma_stream_buffer_bound
    lzma_stream_buffer_decode
    lzma_stream_buffer_encode
    lzma_stream_decoder
    lzma_stream_decoder_mt
    lzma_stream_encoder
    lzma_stream_encoder_mt
    lzma_stream_encoder_mt_memusage
    lzma_stream_flags_compare
    lzma_stream_footer_decode
    lzma_stream_footer_encode
    lzma_stream_header_decode
    lzma_stream_header_encode
    lzma_version_number
    lzma_version_string
    lzma_vli_decode
    lzma_vli_encode
    lzma_vli_size
