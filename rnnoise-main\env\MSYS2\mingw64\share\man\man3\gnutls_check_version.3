.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_check_version" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_check_version \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_check_version(const char * " req_version ");"
.SH ARGUMENTS
.IP "const char * req_version" 12
version string to compare with, or \fBNULL\fP.
.SH "DESCRIPTION"
Check the GnuTLS Library version against the provided string.
See \fBGNUTLS_VERSION\fP for a suitable  \fIreq_version\fP string.

See also \fBgnutls_check_version_numeric()\fP, which provides this
functionality as a macro.
.SH "RETURNS"
Check that the version of the library is at
minimum the one given as a string in  \fIreq_version\fP and return the
actual version string of the library; return \fBNULL\fP if the
condition is not met.  If \fBNULL\fP is passed to this function no
check is done and only the version string is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
