<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DH_size</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>DH_size, DH_bits, DH_security_bits - get Diffie-Hellman prime size and security bits</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/dh.h&gt;</code></pre>

<p>The following functions have been deprecated since OpenSSL 3.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>int DH_bits(const DH *dh);

int DH_size(const DH *dh);

int DH_security_bits(const DH *dh);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The functions described on this page are deprecated. Applications should instead use <a href="../man3/EVP_PKEY_get_bits.html">EVP_PKEY_get_bits(3)</a>, <a href="../man3/EVP_PKEY_get_security_bits.html">EVP_PKEY_get_security_bits(3)</a> and <a href="../man3/EVP_PKEY_get_size.html">EVP_PKEY_get_size(3)</a>.</p>

<p>DH_bits() returns the number of significant bits.</p>

<p><b>dh</b> and <b>dh-&gt;p</b> must not be <b>NULL</b>.</p>

<p>DH_size() returns the Diffie-Hellman prime size in bytes. It can be used to determine how much memory must be allocated for the shared secret computed by <a href="../man3/DH_compute_key.html">DH_compute_key(3)</a>.</p>

<p>DH_security_bits() returns the number of security bits of the given <b>dh</b> key. See <a href="../man3/BN_security_bits.html">BN_security_bits(3)</a>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>DH_bits() returns the number of bits in the key, or -1 if <b>dh</b> doesn&#39;t hold any key parameters.</p>

<p>DH_size() returns the prime size of Diffie-Hellman in bytes, or -1 if <b>dh</b> doesn&#39;t hold any key parameters.</p>

<p>DH_security_bits() returns the number of security bits, or -1 if <b>dh</b> doesn&#39;t hold any key parameters.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_get_bits.html">EVP_PKEY_get_bits(3)</a>, <a href="../man3/DH_new.html">DH_new(3)</a>, <a href="../man3/DH_generate_key.html">DH_generate_key(3)</a>, <a href="../man3/BN_num_bits.html">BN_num_bits(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>All functions were deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


