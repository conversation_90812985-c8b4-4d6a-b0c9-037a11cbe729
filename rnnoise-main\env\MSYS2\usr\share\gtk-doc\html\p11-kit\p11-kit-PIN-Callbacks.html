<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>PIN Callbacks: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="reference.html" title="API Reference">
<link rel="prev" href="p11-kit-URIs.html" title="URIs">
<link rel="next" href="p11-kit-Utilities.html" title="Utilities">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts">
<a href="#" class="shortcut">Top</a><span id="nav_description">  <span class="dim">|</span> 
                  <a href="#p11-kit-PIN-Callbacks.description" class="shortcut">Description</a></span>
</td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="reference.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="p11-kit-URIs.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="p11-kit-Utilities.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="refentry">
<a name="p11-kit-PIN-Callbacks"></a><div class="titlepage"></div>
<div class="refnamediv"><table width="100%"><tr>
<td valign="top">
<h2><span class="refentrytitle"><a name="p11-kit-PIN-Callbacks.top_of_page"></a>PIN Callbacks</span></h2>
<p>PIN Callbacks — PIN Callbacks</p>
</td>
<td class="gallery_image" valign="top" align="right"></td>
</tr></table></div>
<div class="refsect1">
<a name="p11-kit-PIN-Callbacks.functions"></a><h2>Functions</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="functions_proto_type">
<col class="functions_proto_name">
</colgroup>
<tbody>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-new" title="p11_kit_pin_new ()">p11_kit_pin_new</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-new-for-buffer" title="p11_kit_pin_new_for_buffer ()">p11_kit_pin_new_for_buffer</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-new-for-string" title="p11_kit_pin_new_for_string ()">p11_kit_pin_new_for_string</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">const unsigned <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-get-value" title="p11_kit_pin_get_value ()">p11_kit_pin_get_value</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">size_t</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-get-length" title="p11_kit_pin_get_length ()">p11_kit_pin_get_length</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-ref" title="p11_kit_pin_ref ()">p11_kit_pin_ref</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unref" title="p11_kit_pin_unref ()">p11_kit_pin_unref</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-register-callback" title="p11_kit_pin_register_callback ()">p11_kit_pin_register_callback</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unregister-callback" title="p11_kit_pin_unregister_callback ()">p11_kit_pin_unregister_callback</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
</td>
<td class="function_name">
<span class="c_punctuation">(</span><a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-callback" title="p11_kit_pin_callback ()">*p11_kit_pin_callback</a><span class="c_punctuation">)</span> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-request" title="p11_kit_pin_request ()">p11_kit_pin_request</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<span class="c_punctuation">(</span><a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-destroy-func" title="p11_kit_pin_destroy_func ()">*p11_kit_pin_destroy_func</a><span class="c_punctuation">)</span> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-file-callback" title="p11_kit_pin_file_callback ()">p11_kit_pin_file_callback</a> <span class="c_punctuation">()</span>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-PIN-Callbacks.other"></a><h2>Types and Values</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="other_proto_type">
<col class="other_proto_name">
</colgroup>
<tbody>
<tr>
<td class="typedef_keyword">typedef</td>
<td class="function_name"><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin">P11KitPin</a></td>
</tr>
<tr>
<td class="datatype_keyword">enum</td>
<td class="function_name"><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPinFlags" title="enum P11KitPinFlags">P11KitPinFlags</a></td>
</tr>
<tr>
<td class="define_keyword">#define</td>
<td class="function_name"><a class="link" href="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FALLBACK:CAPS" title="P11_KIT_PIN_FALLBACK">P11_KIT_PIN_FALLBACK</a></td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-PIN-Callbacks.description"></a><h2>Description</h2>
<p>Applications can register a callback which will be called to provide a
password associated with a given pin source.</p>
<p>PKCS#11 URIs can contain a 'pin-source' attribute. The value of this attribute
is application dependent, but often references a file containing a PIN to
use.</p>
<p>Using these functions, an applications or libraries can register a
callback with <a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-register-callback" title="p11_kit_pin_register_callback ()"><code class="function">p11_kit_pin_register_callback()</code></a> to be called when a given
'pin-source' attribute value is requested. The application can then prompt
the user or retrieve a PIN for the given context. These registered
callbacks are only relevant and valid within the current process.</p>
<p>A fallback callback can be registered by passing the <a class="link" href="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FALLBACK:CAPS" title="P11_KIT_PIN_FALLBACK"><code class="literal">P11_KIT_PIN_FALLBACK</code></a>
value to <a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-register-callback" title="p11_kit_pin_register_callback ()"><code class="function">p11_kit_pin_register_callback()</code></a>. This fallback callback will be
called for every 'pin-source' attribute request for which no callback has been
directly registered.</p>
<p>To request a PIN for a given 'pin-source' attribute, use the
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-request" title="p11_kit_pin_request ()"><code class="function">p11_kit_pin_request()</code></a> function. If this function returns <code class="literal">NULL</code> then either
no callbacks were registered or none of them could handle the request.</p>
<p>If multiple callbacks are registered for the same PIN source, then they are
called in last-registered-first-called order. They are called in turn until
one of them can handle the request. Fallback callbacks are not called if
a callback was registered specifically for a requested 'pin-source' attribute.</p>
<p>PINs themselves are handled inside of P11KitPin structures. These are thread
safe and allow the callback to specify how the PIN is stored in memory
and freed. A callback can use <a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-new-for-string" title="p11_kit_pin_new_for_string ()"><code class="function">p11_kit_pin_new_for_string()</code></a> or related
functions to create a PIN to be returned.</p>
<p>For example in order to handle the following PKCS#11 URI with a 'pin-source'
attribute</p>
<code class="code"><div class="literallayout"><p><br>
     pkcs11:id=\%69\%95\%3e\%5c\%f4\%bd\%ec\%91;pin-source=my-application<br>
</p></div></code><p>an application could register a callback like this:</p>
<div class="informalexample">
  <table class="listing_frame" border="0" cellpadding="0" cellspacing="0">
    <tbody>
      <tr>
        <td class="listing_lines" align="right"><pre>1
2
3
4
5
6
7
8
9
10</pre></td>
        <td class="listing_code"><pre class="programlisting"><span class="k">static</span><span class="w"> </span><span class="n">P11KitPin</span><span class="o">*</span>
<span class="nf">my_application_pin_callback</span><span class="w"> </span><span class="p">(</span><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">pin_source</span><span class="p">,</span><span class="w"> </span><span class="n">P11KitUri</span><span class="w"> </span><span class="o">*</span><span class="n">pin_uri</span><span class="p">,</span>
<span class="w">                             </span><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">pin_description</span><span class="p">,</span><span class="w"> </span><span class="n">P11KitPinFlags</span><span class="w"> </span><span class="n">pin_flags</span><span class="p">,</span>
<span class="w">                             </span><span class="kt">void</span><span class="w"> </span><span class="o">*</span><span class="n">callback_data</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">p11_kit_pin_new_for_string</span><span class="w"> </span><span class="p">(</span><span class="s">&quot;pin-value&quot;</span><span class="p">);</span>
<span class="p">}</span>

<span class="n">p11_kit_pin_register_callback</span><span class="w"> </span><span class="p">(</span><span class="s">&quot;my-application&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">my_application_pin_callback</span><span class="p">,</span>
<span class="w">                               </span><span class="nb">NULL</span><span class="p">,</span><span class="w"> </span><span class="nb">NULL</span><span class="p">);</span></pre></td>
      </tr>
    </tbody>
  </table>
</div>

</div>
<div class="refsect1">
<a name="p11-kit-PIN-Callbacks.functions_details"></a><h2>Functions</h2>
<div class="refsect2">
<a name="p11-kit-pin-new"></a><h3>p11_kit_pin_new ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
p11_kit_pin_new (<em class="parameter"><code>const unsigned <span class="type">char</span> *value</code></em>,
                 <em class="parameter"><code><span class="type">size_t</span> length</code></em>);</pre>
<p>Create a new P11KitPin with the given PIN value. This function is
usually used from within registered PIN callbacks.</p>
<p>Exactly <em class="parameter"><code>length</code></em>
 bytes from <em class="parameter"><code>value</code></em>
 are used. Null terminated strings,
or encodings are not considered. A copy of the <em class="parameter"><code>value</code></em>
 will be made.</p>
<div class="refsect3">
<a name="p11-kit-pin-new.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>value</p></td>
<td class="parameter_description"><p>the value of the PIN</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>length</p></td>
<td class="parameter_description"><p>the length of <em class="parameter"><code>value</code></em>
</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-new.returns"></a><h4>Returns</h4>
<p> The newly allocated P11KitPin, which should be freed with
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unref" title="p11_kit_pin_unref ()"><code class="function">p11_kit_pin_unref()</code></a> when no longer needed.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-new-for-buffer"></a><h3>p11_kit_pin_new_for_buffer ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
p11_kit_pin_new_for_buffer (<em class="parameter"><code>unsigned <span class="type">char</span> *buffer</code></em>,
                            <em class="parameter"><code><span class="type">size_t</span> length</code></em>,
                            <em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-destroy-func" title="p11_kit_pin_destroy_func ()"><span class="type">p11_kit_pin_destroy_func</span></a> destroy</code></em>);</pre>
<p>Create a new P11KitPin which will use <em class="parameter"><code>buffer</code></em>
 for the PIN value.
This function is usually used from within registered PIN callbacks.</p>
<p>The buffer will not be copied. String encodings and null characters
are not considered.</p>
<p>When the last reference to this PIN is lost, then the <em class="parameter"><code>destroy</code></em>
 callback
function will be called passing <em class="parameter"><code>buffer</code></em>
 as an argument. This allows the
caller to use a buffer as a PIN without copying it.</p>
<div class="informalexample">
  <table class="listing_frame" border="0" cellpadding="0" cellspacing="0">
    <tbody>
      <tr>
        <td class="listing_lines" align="right"><pre>1
2
3
4</pre></td>
        <td class="listing_code"><pre class="programlisting"><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">buffer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">malloc</span><span class="w"> </span><span class="p">(</span><span class="mi">128</span><span class="p">);</span>
<span class="n">P11KitPin</span><span class="w"> </span><span class="o">*</span><span class="n">pin</span><span class="p">;</span>
<span class="w"> </span><span class="p">....</span>
<span class="n">pin</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">p11_kit_pin_new_for_buffer</span><span class="w"> </span><span class="p">(</span><span class="n">buffer</span><span class="p">,</span><span class="w"> </span><span class="mi">128</span><span class="p">,</span><span class="w"> </span><span class="n">free</span><span class="p">);</span></pre></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="refsect3">
<a name="p11-kit-pin-new-for-buffer.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>buffer</p></td>
<td class="parameter_description"><p>the value of the PIN</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>length</p></td>
<td class="parameter_description"><p>the length of <em class="parameter"><code>buffer</code></em>
</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>destroy</p></td>
<td class="parameter_description"><p>if not <code class="literal">NULL</code>, then called when PIN is destroyed.</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-new-for-buffer.returns"></a><h4>Returns</h4>
<p> The newly allocated P11KitPin, which should be freed with
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unref" title="p11_kit_pin_unref ()"><code class="function">p11_kit_pin_unref()</code></a> when no longer needed.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-new-for-string"></a><h3>p11_kit_pin_new_for_string ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
p11_kit_pin_new_for_string (<em class="parameter"><code>const <span class="type">char</span> *value</code></em>);</pre>
<p>Create a new P11KitPin for the given null-terminated string, such as a
password. This function is usually used from within registered
PIN callbacks.</p>
<p>The PIN will consist of the string not including the null terminator.
String encoding is not considered. A copy of the <em class="parameter"><code>value</code></em>
 will be made.</p>
<div class="refsect3">
<a name="p11-kit-pin-new-for-string.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>value</p></td>
<td class="parameter_description"><p>the value of the PIN</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-new-for-string.returns"></a><h4>Returns</h4>
<p> The newly allocated P11KitPin, which should be freed with
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unref" title="p11_kit_pin_unref ()"><code class="function">p11_kit_pin_unref()</code></a> when no longer needed.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-get-value"></a><h3>p11_kit_pin_get_value ()</h3>
<pre class="programlisting">const unsigned <span class="returnvalue">char</span> *
p11_kit_pin_get_value (<em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="type">P11KitPin</span></a> *pin</code></em>,
                       <em class="parameter"><code><span class="type">size_t</span> *length</code></em>);</pre>
<p>Get the PIN value from a P11KitPin. <em class="parameter"><code>length</code></em>
 will be set to the
length of the value.</p>
<p>The value returned is owned by the P11KitPin and should not be modified.
It remains valid as long as a reference to the PIN is held. The PIN value
will not contain an extra null-terminator character.</p>
<div class="refsect3">
<a name="p11-kit-pin-get-value.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>pin</p></td>
<td class="parameter_description"><p>the P11KitPin</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>length</p></td>
<td class="parameter_description"><p>a location to return the value length</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-get-value.returns"></a><h4>Returns</h4>
<p> the value for the PIN.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-get-length"></a><h3>p11_kit_pin_get_length ()</h3>
<pre class="programlisting"><span class="returnvalue">size_t</span>
p11_kit_pin_get_length (<em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="type">P11KitPin</span></a> *pin</code></em>);</pre>
<p>Get the length of the PIN value from a P11KitPin.</p>
<div class="refsect3">
<a name="p11-kit-pin-get-length.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>pin</p></td>
<td class="parameter_description"><p>the P11KitPin</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-get-length.returns"></a><h4>Returns</h4>
<p> the length of the PIN value.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-ref"></a><h3>p11_kit_pin_ref ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
p11_kit_pin_ref (<em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="type">P11KitPin</span></a> *pin</code></em>);</pre>
<p>Add a reference to a P11KitPin. This should be matched with a later call
to <a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unref" title="p11_kit_pin_unref ()"><code class="function">p11_kit_pin_unref()</code></a>. As long as at least one reference is held, the PIN
will remain valid and in memory.</p>
<div class="refsect3">
<a name="p11-kit-pin-ref.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>pin</p></td>
<td class="parameter_description"><p>the P11KitPin</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-ref.returns"></a><h4>Returns</h4>
<p> the <em class="parameter"><code>pin</code></em>
pointer, for convenience sake.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-unref"></a><h3>p11_kit_pin_unref ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_pin_unref (<em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="type">P11KitPin</span></a> *pin</code></em>);</pre>
<p>Remove a reference from a P11KitPin. When all references have been removed
then the PIN will be freed and will no longer be in memory.</p>
<div class="refsect3">
<a name="p11-kit-pin-unref.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>pin</p></td>
<td class="parameter_description"><p>the P11KitPin</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-register-callback"></a><h3>p11_kit_pin_register_callback ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_pin_register_callback (<em class="parameter"><code>const <span class="type">char</span> *pin_source</code></em>,
                               <em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-callback" title="p11_kit_pin_callback ()"><span class="type">p11_kit_pin_callback</span></a> callback</code></em>,
                               <em class="parameter"><code><span class="type">void</span> *callback_data</code></em>,
                               <em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-destroy-func" title="p11_kit_pin_destroy_func ()"><span class="type">p11_kit_pin_destroy_func</span></a> callback_destroy</code></em>);</pre>
<p>Register a callback to handle PIN requests for a given 'pin-source' attribute.
If <em class="parameter"><code>pin_source</code></em>
 is set to P11_KIT_PIN_FALLBACK then this will be a fallback
callback and will be called for requests for which no other callback has
been specifically registered.</p>
<p>If multiple callbacks are registered for the same <em class="parameter"><code>pin_source</code></em>
 value, then
the last registered callback will be the first to be called.</p>
<div class="refsect3">
<a name="p11-kit-pin-register-callback.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>pin_source</p></td>
<td class="parameter_description"><p>the 'pin-source' attribute this this callback is for</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>callback</p></td>
<td class="parameter_description"><p>the callback function</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>callback_data</p></td>
<td class="parameter_description"><p>data that will be passed to the callback</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>callback_destroy</p></td>
<td class="parameter_description"><p>a function that will be called with <em class="parameter"><code>callback_data</code></em>
when
the callback is unregistered.</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-register-callback.returns"></a><h4>Returns</h4>
<p> Returns negative if registering fails.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-unregister-callback"></a><h3>p11_kit_pin_unregister_callback ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_pin_unregister_callback (<em class="parameter"><code>const <span class="type">char</span> *pin_source</code></em>,
                                 <em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-callback" title="p11_kit_pin_callback ()"><span class="type">p11_kit_pin_callback</span></a> callback</code></em>,
                                 <em class="parameter"><code><span class="type">void</span> *callback_data</code></em>);</pre>
<p>Unregister a callback that was previously registered with the
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-register-callback" title="p11_kit_pin_register_callback ()"><code class="function">p11_kit_pin_register_callback()</code></a> function. If more than one registered
callback matches the given arguments, then only one of those will be
removed.</p>
<div class="refsect3">
<a name="p11-kit-pin-unregister-callback.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>pin_source</p></td>
<td class="parameter_description"><p>the 'pin-source' attribute the callback was registered for</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>callback</p></td>
<td class="parameter_description"><p>the callback function that was registered</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>callback_data</p></td>
<td class="parameter_description"><p>data that was registered for the callback</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-callback"></a><h3>p11_kit_pin_callback ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
<span class="c_punctuation">(</span>*p11_kit_pin_callback<span class="c_punctuation">)</span> (<em class="parameter"><code>const <span class="type">char</span> *pin_source</code></em>,
                         <em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *pin_uri</code></em>,
                         <em class="parameter"><code>const <span class="type">char</span> *pin_description</code></em>,
                         <em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPinFlags" title="enum P11KitPinFlags"><span class="type">P11KitPinFlags</span></a> pin_flags</code></em>,
                         <em class="parameter"><code><span class="type">void</span> *callback_data</code></em>);</pre>
<p>Represents a PIN callback function.</p>
<p>The various arguments are the same as the ones passed to
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-request" title="p11_kit_pin_request ()"><code class="function">p11_kit_pin_request()</code></a>. The <em class="parameter"><code>callback_data</code></em>
 argument was the one passed to
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-register-callback" title="p11_kit_pin_register_callback ()"><code class="function">p11_kit_pin_register_callback()</code></a> when registering this callback.</p>
<p>The function should return <code class="literal">NULL</code> if it could not provide a PIN, either
because of an error or a user cancellation.</p>
<p>If a PIN is returned, it will be unreferenced by the caller. So it should be
either newly allocated, or referenced before returning.</p>
<div class="refsect3">
<a name="p11-kit-pin-callback.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>pin_source</p></td>
<td class="parameter_description"><p>a 'pin-source' attribute string</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_uri</p></td>
<td class="parameter_description"><p>a PKCS#11 URI that the PIN is for, or <code class="literal">NULL</code></p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_description</p></td>
<td class="parameter_description"><p>a descrption of what the PIN is for</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_flags</p></td>
<td class="parameter_description"><p>flags describing the PIN request</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>callback_data</p></td>
<td class="parameter_description"><p>data that was provided when registering this callback</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-callback.returns"></a><h4>Returns</h4>
<p> A PIN or <code class="literal">NULL</code></p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-request"></a><h3>p11_kit_pin_request ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
p11_kit_pin_request (<em class="parameter"><code>const <span class="type">char</span> *pin_source</code></em>,
                     <em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *pin_uri</code></em>,
                     <em class="parameter"><code>const <span class="type">char</span> *pin_description</code></em>,
                     <em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPinFlags" title="enum P11KitPinFlags"><span class="type">P11KitPinFlags</span></a> pin_flags</code></em>);</pre>
<p>Request a PIN for a given 'pin-source' attribute. The result depends on the
registered callbacks.</p>
<p>If not <code class="literal">NULL</code>, then the <em class="parameter"><code>pin_uri</code></em>
 attribute should point to the thing that the
PIN is being requested for. In most use cases this should be a PKCS#11 URI
pointing to a token.</p>
<p>The <em class="parameter"><code>pin_description</code></em>
 should always be specified. It is a string describing
what the PIN is for. For example this would be the token label, if the PIN
is for a token.</p>
<p>If more than one callback is registered for the <em class="parameter"><code>pin_source</code></em>
, then the latest
registered one will be called first. If that callback does not return a
PIN, then the next will be called in turn.</p>
<p>If no callback is registered for <em class="parameter"><code>pin_source</code></em>
, then the fallback callbacks will
be invoked in the same way. The fallback callbacks will not be called if any
callback has been registered specifically for <em class="parameter"><code>pin_source</code></em>
.</p>
<p>The PIN returned should be released with <a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unref" title="p11_kit_pin_unref ()"><code class="function">p11_kit_pin_unref()</code></a>.</p>
<div class="refsect3">
<a name="p11-kit-pin-request.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>pin_source</p></td>
<td class="parameter_description"><p>the 'pin-source' attribute that is being requested</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_uri</p></td>
<td class="parameter_description"><p>a PKCS#11 URI that the PIN is being requested for, optionally <code class="literal">NULL</code>.</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_description</p></td>
<td class="parameter_description"><p>a description of what the PIN is for, must not be <code class="literal">NULL</code>.</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_flags</p></td>
<td class="parameter_description"><p>various flags for this request</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-request.returns"></a><h4>Returns</h4>
<p> the PIN which should be released with <a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unref" title="p11_kit_pin_unref ()"><code class="function">p11_kit_pin_unref()</code></a>, or <code class="literal">NULL</code>
if no callback was registered or could proivde a PIN</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-destroy-func"></a><h3>p11_kit_pin_destroy_func ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
<span class="c_punctuation">(</span>*p11_kit_pin_destroy_func<span class="c_punctuation">)</span> (<em class="parameter"><code><span class="type">void</span> *data</code></em>);</pre>
<p>A function called to free or cleanup <em class="parameter"><code>data</code></em>
.</p>
<div class="refsect3">
<a name="p11-kit-pin-destroy-func.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>data</p></td>
<td class="parameter_description"><p>the data to destroy</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-pin-file-callback"></a><h3>p11_kit_pin_file_callback ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin"><span class="returnvalue">P11KitPin</span></a> *
p11_kit_pin_file_callback (<em class="parameter"><code>const <span class="type">char</span> *pin_source</code></em>,
                           <em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *pin_uri</code></em>,
                           <em class="parameter"><code>const <span class="type">char</span> *pin_description</code></em>,
                           <em class="parameter"><code><a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPinFlags" title="enum P11KitPinFlags"><span class="type">P11KitPinFlags</span></a> pin_flags</code></em>,
                           <em class="parameter"><code><span class="type">void</span> *callback_data</code></em>);</pre>
<p>This is a PIN callback function that looks up the 'pin-source' attribute in
a file with that name. This can be used to enable the normal PKCS#11 URI
behavior described in the RFC.</p>
<p>If <em class="parameter"><code>pin_flags</code></em>
 contains the <a class="link" href="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FLAGS-RETRY:CAPS"><code class="literal">P11_KIT_PIN_FLAGS_RETRY</code></a> flag, then this
callback will always return <code class="literal">NULL</code>. This is to prevent endless loops
where an application is expecting to interact with a prompter, but
instead is interacting with this callback reading a file over and over.</p>
<p>This callback fails on files larger than 4 Kilobytes.</p>
<p>This callback is not registered by default. It may have security
implications depending on the source of the PKCS#11 URI and the PKCS#11
in use. To register it, use code like the following:</p>
<div class="informalexample">
  <table class="listing_frame" border="0" cellpadding="0" cellspacing="0">
    <tbody>
      <tr>
        <td class="listing_lines" align="right"><pre>1
2</pre></td>
        <td class="listing_code"><pre class="programlisting"><span class="n">p11_kit_pin_register_callback</span><span class="w"> </span><span class="p">(</span><span class="n">P11_KIT_PIN_FALLBACK</span><span class="p">,</span><span class="w"> </span><span class="n">p11_kit_pin_file_callback</span><span class="p">,</span>
<span class="w">                               </span><span class="nb">NULL</span><span class="p">,</span><span class="w"> </span><span class="nb">NULL</span><span class="p">);</span></pre></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="refsect3">
<a name="p11-kit-pin-file-callback.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>pin_source</p></td>
<td class="parameter_description"><p>a 'pin-source' attribute string</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_uri</p></td>
<td class="parameter_description"><p>a PKCS#11 URI that the PIN is for, or <code class="literal">NULL</code></p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_description</p></td>
<td class="parameter_description"><p>a descrption of what the PIN is for</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_flags</p></td>
<td class="parameter_description"><p>flags describing the PIN request</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>callback_data</p></td>
<td class="parameter_description"><p>unused, should be <code class="literal">NULL</code></p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-pin-file-callback.returns"></a><h4>Returns</h4>
<p> a referenced PIN with the file contents, or <code class="literal">NULL</code> if the file
could not be read</p>
</div>
</div>
</div>
<div class="refsect1">
<a name="p11-kit-PIN-Callbacks.other_details"></a><h2>Types and Values</h2>
<div class="refsect2">
<a name="P11KitPin"></a><h3>P11KitPin</h3>
<p>A structure representing a PKCS#11 PIN. There are no public fields
visible in this structure. Use the various accessor functions.</p>
</div>
<hr>
<div class="refsect2">
<a name="P11KitPinFlags"></a><h3>enum P11KitPinFlags</h3>
<p>Flags that are passed to <a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-request" title="p11_kit_pin_request ()"><code class="function">p11_kit_pin_request()</code></a> and registered callbacks.</p>
<div class="refsect3">
<a name="P11KitPinFlags.members"></a><h4>Members</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="300px" class="enum_members_name">
<col class="enum_members_description">
<col width="200px" class="enum_members_annotations">
</colgroup>
<tbody>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-PIN-FLAGS-USER-LOGIN:CAPS"></a>P11_KIT_PIN_FLAGS_USER_LOGIN</p></td>
<td class="enum_member_description">
<p>The PIN is for a PKCS#11 user type login.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-PIN-FLAGS-SO-LOGIN:CAPS"></a>P11_KIT_PIN_FLAGS_SO_LOGIN</p></td>
<td class="enum_member_description">
<p>The PIN is for a PKCS#11 security officer type login.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-PIN-FLAGS-CONTEXT-LOGIN:CAPS"></a>P11_KIT_PIN_FLAGS_CONTEXT_LOGIN</p></td>
<td class="enum_member_description">
<p>The PIN is for a PKCS#11 contect specific type login.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-PIN-FLAGS-RETRY:CAPS"></a>P11_KIT_PIN_FLAGS_RETRY</p></td>
<td class="enum_member_description">
<p>The PIN is being requested again, due to an invalid previous PIN.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-PIN-FLAGS-MANY-TRIES:CAPS"></a>P11_KIT_PIN_FLAGS_MANY_TRIES</p></td>
<td class="enum_member_description">
<p>The PIN has failed too many times, and few tries are left.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-PIN-FLAGS-FINAL-TRY:CAPS"></a>P11_KIT_PIN_FLAGS_FINAL_TRY</p></td>
<td class="enum_member_description">
<p>The PIN has failed too many times, and this is the last try.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="P11-KIT-PIN-FALLBACK:CAPS"></a><h3>P11_KIT_PIN_FALLBACK</h3>
<pre class="programlisting">#define             P11_KIT_PIN_FALLBACK</pre>
<p>Used with <a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-register-callback" title="p11_kit_pin_register_callback ()"><code class="function">p11_kit_pin_register_callback()</code></a> to register a fallback callback.
This callback will be called if no other callback is registered for a 'pin-source'.</p>
</div>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>