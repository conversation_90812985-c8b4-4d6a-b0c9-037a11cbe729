# RNN噪声抑制与人声增强系统 - 快速开始指南

## 🎯 项目目标

基于您现有的RNNoise系统，扩展实现：
- **噪声抑制** (保持原有功能)
- **人声增强** (新增功能)
- **实时处理** (Makefile编译)
- **双重优化** (联合训练)

## 📋 准备工作

### 1. 环境检查
```bash
# 检查必要工具
gcc --version          # 需要GCC编译器
python3 --version      # 需要Python 3.7+
pip3 list | grep tensorflow  # 需要TensorFlow
```

### 2. 项目初始化
```bash
# 运行自动化设置脚本
python3 setup_enhanced_rnnoise.py enhanced-rnnoise

# 进入项目目录
cd enhanced-rnnoise

# 检查项目结构
tree .
```

## 🚀 实施步骤

### 第一阶段：基础框架搭建 (1-2天)

#### 1.1 扩展现有特征提取
```bash
# 基于您现有的 src/denoise.c，添加人声特征提取
# 需要实现的函数：
# - extract_voice_enhancement_features()
# - extract_pitch_features()
# - extract_formant_features()
# - extract_harmonic_features()
# - extract_clarity_features()
```

#### 1.2 修改训练数据生成
```bash
# 扩展您的 denoise_training_gao.exe
# 从 75维 -> 103维 特征向量
# (68输入 + 18噪声目标 + 18人声目标 + 1VAD)

# 使用您现有的数据
./enhanced_training_generator data/clean_voice data/wind_noise_voice mixed.wav > enhanced_training.f32
```

### 第二阶段：模型训练 (2-3天)

#### 2.1 数据转换
```bash
# 转换为HDF5格式
python3 training/bin2hdf5.py enhanced_training.f32 auto 103 enhanced_training.h5
```

#### 2.2 模型训练
```bash
# 运行增强训练脚本
cd training
python3 enhanced_rnn_train.py
```

#### 2.3 模型导出
```bash
# 导出训练好的模型到C代码
python3 dump_enhanced_rnn.py enhanced_model.keras ../src/enhanced_rnn_data.c ../include/enhanced_rnn_data.h
```

### 第三阶段：C代码集成 (2-3天)

#### 3.1 编译增强系统
```bash
# 编译最终的可执行文件
make all

# 测试系统
./bin/rnnoise_enhanced test_input.wav test_output.wav
```

#### 3.2 性能优化
- 实时性能测试
- 内存使用优化
- 音频质量评估

## 📊 技术架构概览

### 特征扩展方案
```
原始系统: 38维输入 -> 18维输出 + 1维VAD
增强系统: 68维输入 -> 36维输出 + 1维VAD

新增30维人声特征:
├── 基频特征 (8维)
├── 共振峰特征 (6维)  
├── 谐波结构特征 (8维)
└── 语音清晰度特征 (8维)
```

### 双重损失函数
```python
总损失 = 10.0 × 噪声抑制损失 + 8.0 × 人声增强损失 + 0.5 × VAD损失
```

### 处理流程
```
音频输入 -> 特征提取(68维) -> RNN处理 -> 双重增益输出 -> 增益融合 -> 音频输出
```

## 🛠️ 开发工具

### 1. 自动化脚本
- `setup_enhanced_rnnoise.py` - 项目初始化
- `enhanced_rnn_train.py` - 模型训练
- `dump_enhanced_rnn.py` - 模型导出

### 2. 编译系统
- `Makefile` - 完整编译流程
- 支持增量编译
- 自动依赖管理

### 3. 测试工具
```bash
# 功能测试
make test

# 性能测试
make benchmark

# 质量评估
make evaluate
```

## 📈 预期效果

### 性能指标
- **噪声抑制**: 保持原有水平 (PESQ > 2.5)
- **人声增强**: 提升15-25% (主观评分)
- **实时性**: <10ms延迟 (16kHz采样率)
- **兼容性**: 完全兼容原有接口

### 应用场景
- 语音通话降噪
- 会议录音增强
- 语音识别预处理
- 音频内容制作

## 🔧 故障排除

### 常见问题

1. **编译错误**
```bash
# 检查依赖
sudo apt-get install build-essential
make clean && make all
```

2. **训练失败**
```bash
# 检查数据格式
python3 -c "import h5py; print(h5py.File('enhanced_training.h5', 'r')['data'].shape)"
```

3. **性能问题**
```bash
# 启用优化编译
export CFLAGS="-O3 -ffast-math -march=native"
make clean && make all
```

## 📚 参考资源

### 技术文档
- `RNN噪声抑制与人声增强系统改进技术文档.md` - 完整技术方案
- `README.md` - 项目说明
- 源码注释 - 实现细节

### 相关论文
- RNNoise: Learning Noise Suppression
- Deep Learning for Audio Enhancement
- Real-time Speech Enhancement

## 🎯 里程碑计划

### Week 1: 基础实现
- [x] 项目框架搭建
- [ ] 特征提取扩展
- [ ] 训练数据生成

### Week 2: 模型训练
- [ ] 数据预处理
- [ ] 模型训练调优
- [ ] 性能验证

### Week 3: 系统集成
- [ ] C代码集成
- [ ] 编译优化
- [ ] 功能测试

### Week 4: 优化部署
- [ ] 性能优化
- [ ] 质量评估
- [ ] 文档完善

## 💡 开发建议

1. **渐进式开发**: 先实现基础功能，再逐步优化
2. **数据驱动**: 充分利用您现有的400对训练数据
3. **性能优先**: 确保实时性能不受影响
4. **兼容性**: 保持与现有系统的接口兼容
5. **测试驱动**: 每个阶段都要有充分的测试

## 🤝 技术支持

如果在实施过程中遇到问题，可以：
1. 查看技术文档的详细实现
2. 检查示例代码和注释
3. 运行测试脚本验证环境
4. 逐步调试各个模块

---

**准备好开始了吗？运行 `python3 setup_enhanced_rnnoise.py` 开始您的增强RNN之旅！** 🚀
