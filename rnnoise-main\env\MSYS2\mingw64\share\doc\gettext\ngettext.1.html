<!-- Creator     : groff version 1.22.3 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>NGETTEXT</title>

</head>
<body>

<h1 align="center">NGETTEXT</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#AUTHOR">AUTHOR</a><br>
<a href="#REPORTING BUGS">REPORTING BUGS</a><br>
<a href="#COPYRIGHT">COPYRIGHT</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">ngettext
- translate message and choose plural form</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><b>ngettext</b>
[<i>OPTION</i>] [<i>TEXTDOMAIN</i>] <i>MSGID MSGID-PLURAL
COUNT</i></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The
<b>ngettext</b> program translates a natural language
message into the user&rsquo;s language, by looking up the
translation in a message catalog, and chooses the
appropriate plural form, which depends on the number
<i>COUNT</i> and the language of the message catalog where
the translation was found.</p>

<p style="margin-left:11%; margin-top: 1em">Display native
language translation of a textual message whose grammatical
form depends on a number. <b><br>
-d</b>,
<b>--domain</b>=<i>TEXTDOMAIN</i></p>

<p style="margin-left:22%;">retrieve translated message
from TEXTDOMAIN</p>

<p style="margin-left:11%;"><b>-c</b>,
<b>--context</b>=<i>CONTEXT</i></p>

<p style="margin-left:22%;">specify context for MSGID</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>-e</b></p></td>
<td width="8%"></td>
<td width="63%">


<p>enable expansion of some escape sequences</p></td>
<td width="15%">
</td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>-E</b></p></td>
<td width="8%"></td>
<td width="63%">


<p>(ignored for compatibility)</p></td>
<td width="15%">
</td></tr>
</table>

<p style="margin-left:11%;">[TEXTDOMAIN]</p>

<p style="margin-left:22%;">retrieve translated message
from TEXTDOMAIN</p>

<p style="margin-left:11%;">MSGID MSGID-PLURAL</p>

<p style="margin-left:22%;">translate MSGID (singular) /
MSGID-PLURAL (plural)</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">


<p>COUNT</p></td>
<td width="4%"></td>
<td width="72%">


<p>choose singular/plural form based on this value</p></td>
<td width="6%">
</td></tr>
</table>

<p style="margin-left:11%; margin-top: 1em"><b>Informative
output: <br>
-h</b>, <b>--help</b></p>

<p style="margin-left:22%;">display this help and exit</p>

<p style="margin-left:11%;"><b>-V</b>,
<b>--version</b></p>

<p style="margin-left:22%;">display version information and
exit</p>

<p style="margin-left:11%; margin-top: 1em">If the
TEXTDOMAIN parameter is not given, the domain is determined
from the environment variable TEXTDOMAIN. If the message
catalog is not found in the regular directory, another
location can be specified with the environment variable
TEXTDOMAINDIR. Standard search directory: /mingw64/share/locale</p>

<h2>AUTHOR
<a name="AUTHOR"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Written by
Ulrich Drepper.</p>

<h2>REPORTING BUGS
<a name="REPORTING BUGS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Report bugs in
the bug tracker at
&lt;https://savannah.gnu.org/projects/gettext&gt; or by
email to &lt;<EMAIL>&gt;.</p>

<h2>COPYRIGHT
<a name="COPYRIGHT"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Copyright
&copy; 1995-2025 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later
&lt;https://gnu.org/licenses/gpl.html&gt; <br>
This is free software: you are free to change and
redistribute it. There is NO WARRANTY, to the extent
permitted by law.</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The full
documentation for <b>ngettext</b> is maintained as a Texinfo
manual. If the <b>info</b> and <b>ngettext</b> programs are
properly installed at your site, the command</p>

<p style="margin-left:22%; margin-top: 1em"><b>info
ngettext</b></p>

<p style="margin-left:11%; margin-top: 1em">should give you
access to the complete manual.</p>
<hr>
</body>
</html>
