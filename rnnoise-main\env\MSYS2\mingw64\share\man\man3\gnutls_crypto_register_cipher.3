.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_crypto_register_cipher" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_crypto_register_cipher \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_crypto_register_cipher(gnutls_cipher_algorithm_t " algorithm ", int " priority ", gnutls_cipher_init_func " init ", gnutls_cipher_setkey_func " setkey ", gnutls_cipher_setiv_func " setiv ", gnutls_cipher_encrypt_func " encrypt ", gnutls_cipher_decrypt_func " decrypt ", gnutls_cipher_deinit_func " deinit ");"
.SH ARGUMENTS
.IP "gnutls_cipher_algorithm_t algorithm" 12
is the gnutls algorithm identifier
.IP "int priority" 12
is the priority of the algorithm
.IP "gnutls_cipher_init_func init" 12
A function which initializes the cipher
.IP "gnutls_cipher_setkey_func setkey" 12
A function which sets the key of the cipher
.IP "gnutls_cipher_setiv_func setiv" 12
A function which sets the nonce/IV of the cipher (non\-AEAD)
.IP "gnutls_cipher_encrypt_func encrypt" 12
A function which performs encryption (non\-AEAD)
.IP "gnutls_cipher_decrypt_func decrypt" 12
A function which performs decryption (non\-AEAD)
.IP "gnutls_cipher_deinit_func deinit" 12
A function which deinitializes the cipher
.SH "DESCRIPTION"
This function will register a cipher algorithm to be used by
gnutls.  Any algorithm registered will override the included
algorithms and by convention kernel implemented algorithms have
priority of 90 and CPU\-assisted of 80.  The algorithm with the lowest priority will be
used by gnutls.

In the case the registered init or setkey functions return \fBGNUTLS_E_NEED_FALLBACK\fP,
GnuTLS will attempt to use the next in priority registered cipher.

The functions which are marked as non\-AEAD they are not required when
registering a cipher to be used with the new AEAD API introduced in
GnuTLS 3.4.0. Internally GnuTLS uses the new AEAD API.
.SH "DEPRECATED"
since 3.7.0 it is no longer possible to override cipher implementation
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
