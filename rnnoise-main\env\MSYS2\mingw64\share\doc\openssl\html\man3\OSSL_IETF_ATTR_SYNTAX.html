<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_IETF_ATTR_SYNTAX</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_IETF_ATTR_SYNTAX, OSSL_IETF_ATTR_SYNTAX_get0_policyAuthority, OSSL_IETF_ATTR_SYNTAX_set0_policyAuthority, OSSL_IETF_ATTR_SYNTAX_get_value_num, OSSL_IETF_ATTR_SYNTAX_get0_value, OSSL_IETF_ATTR_SYNTAX_add1_value - Accessors and setters for OSSL_IETF_ATTR_SYNTAX</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509_acert.h&gt;

typedef struct OSSL_IETF_ATTR_SYNTAX_st OSSL_IETF_ATTR_SYNTAX;

const GENERAL_NAMES *
OSSL_IETF_ATTR_SYNTAX_get0_policyAuthority(const OSSL_IETF_ATTR_SYNTAX *a);
void OSSL_IETF_ATTR_SYNTAX_set0_policyAuthority(OSSL_IETF_ATTR_SYNTAX *a,
                                                GENERAL_NAMES *names);

int OSSL_IETF_ATTR_SYNTAX_get_value_num(const OSSL_IETF_ATTR_SYNTAX *a);
void *OSSL_IETF_ATTR_SYNTAX_get0_value(const OSSL_IETF_ATTR_SYNTAX *a,
                                       int ind, int *type);
int OSSL_IETF_ATTR_SYNTAX_add1_value(OSSL_IETF_ATTR_SYNTAX *a, int type,
                                     void *data);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p><b>OSSL_IETF_ATTR_SYNTAX</b> is an opaque structure that represents the IetfAttrSyntax type defined in RFC 5755 (Section 4.4) for use as an AttributeValue.</p>

<p>OSSL_IETF_ATTR_SYNTAX_get0_policyAuthority() and OSSL_IETF_ATTR_SYNTAX_set0_policyAuthority() get and set the policyAuthority field of the structure. Both routines act on internal pointers of the structure and must not be freed by the application.</p>

<p>An <b>OSSL_IETF_ATTR_SYNTAX</b> object also holds a sequence of values. OSSL_IETF_ATTR_SYNTAX_get_value_num() returns the number of values in the sequence. OSSL_IETF_ATTR_SYNTAX_add1_value(), adds a copy of <i>data</i> of a specified <i>type</i> to the sequence. The caller should free the <i>data</i> after use.</p>

<p>OSSL_IETF_ATTR_SYNTAX_get0_value() will return the value and a specific index <i>ind</i> in the sequence or NULL on error. If <i>type</i> is not NULL, the type of the value will be written to this location.</p>

<p>The <i>type</i> of the values stored in the <b>OSSL_IETF_ATTR_SYNTAX</b> value sequence is one of the following:</p>

<dl>

<dt id="OSSL_IETFAS_OCTETS">OSSL_IETFAS_OCTETS</dt>
<dd>

<p>A pointer to an ASN1_OCTET_STRING</p>

</dd>
<dt id="OSSL_IETFAS_OID">OSSL_IETFAS_OID</dt>
<dd>

<p>A pointer to an ASN1_OBJECT</p>

</dd>
<dt id="OSSL_IETFAS_STRING">OSSL_IETFAS_STRING</dt>
<dd>

<p>A pointer to an ASN1_UTF8STRING</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_IETF_ATTR_SYNTAX_get0_policyAuthority() returns an pointer to a <b>GENERAL_NAMES</b> structure or <b>NULL</b> if the policy authority has not been set.</p>

<p>OSSL_IETF_ATTR_SYNTAX_get_value_num() returns the number of entries in the value sequence or -1 on error.</p>

<p>OSSL_IETF_ATTR_SYNTAX_get0_value() returns a pointer to the value at the given index or NULL if the index is out of range.</p>

<p>OSSL_IETF_ATTR_SYNTAX_add1_value() returns 1 on success and 0 on failure.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>OSSL_IETF_ATTR_SYNTAX_get0_policyAuthority(), OSSL_IETF_ATTR_SYNTAX_set0_policyAuthority(), OSSL_IETF_ATTR_SYNTAX_get_value_num(), OSSL_IETF_ATTR_SYNTAX_get0_value(), and OSSL_IETF_ATTR_SYNTAX_add1_value() were added in OpenSSL 3.4.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


