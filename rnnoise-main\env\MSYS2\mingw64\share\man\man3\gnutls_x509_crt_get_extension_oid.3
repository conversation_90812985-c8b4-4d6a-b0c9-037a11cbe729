.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_extension_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_extension_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_extension_oid(gnutls_x509_crt_t " cert ", unsigned " indx ", void * " oid ", size_t * " oid_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "unsigned indx" 12
Specifies which extension OID to send. Use (0) to get the first one.
.IP "void * oid" 12
a pointer to a structure to hold the OID (may be null)
.IP "size_t * oid_size" 12
initially holds the size of  \fIoid\fP 
.SH "DESCRIPTION"
This function will return the requested extension OID in the certificate.
The extension OID will be stored as a string in the provided buffer.

The  \fIoid\fP returned will be null terminated, although  \fIoid_size\fP will not
account for the trailing null.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.  If you have reached the
last extension available \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
will be returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
