.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake_set_timeout" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake_set_timeout \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_handshake_set_timeout(gnutls_session_t " session ", unsigned int " ms ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int ms" 12
is a timeout value in milliseconds
.SH "DESCRIPTION"
This function sets the timeout for the TLS handshake process
to the provided value. Use an  \fIms\fP value of zero to disable
timeout, or \fBGNUTLS_DEFAULT_HANDSHAKE_TIMEOUT\fP for a reasonable
default value. For the DTLS protocol, the more detailed
\fBgnutls_dtls_set_timeouts()\fP is provided.

This function requires to set a pull timeout callback. See
\fBgnutls_transport_set_pull_timeout_function()\fP.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
