.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_x509_key_file" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_x509_key_file \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_x509_key_file(gnutls_certificate_credentials_t " res ", const char * " certfile ", const char * " keyfile ", gnutls_x509_crt_fmt_t " type ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "const char * certfile" 12
is a file that containing the certificate list (path) for
the specified private key, in PKCS7 format, or a list of certificates
.IP "const char * keyfile" 12
is a file that contains the private key
.IP "gnutls_x509_crt_fmt_t type" 12
is PEM or DER
.SH "DESCRIPTION"
This function sets a certificate/private key pair in the
gnutls_certificate_credentials_t type.  This function may be
called more than once, in case multiple keys/certificates exist for
the server.  For clients that need to send more than its own end
entity certificate, e.g., also an intermediate CA cert, then the
 \fIcertfile\fP must contain the ordered certificate chain.

Note that the names in the certificate provided will be considered
when selecting the appropriate certificate to use (in case of multiple
certificate/key pairs).

This function can also accept URLs at  \fIkeyfile\fP and  \fIcertfile\fP . In that case it
will use the private key and certificate indicated by the URLs. Note
that the supported URLs are the ones indicated by \fBgnutls_url_is_supported()\fP.

In case the  \fIcertfile\fP is provided as a PKCS \fB11\fP URL, then the certificate, and its
present issuers in the token are imported (i.e., forming the required trust chain).

If that function fails to load the  \fIres\fP structure is at an undefined state, it must
not be reused to load other keys or certificates.

Note that, this function by default returns zero on success and a negative value on error.
Since 3.5.6, when the flag \fBGNUTLS_CERTIFICATE_API_V2\fP is set using \fBgnutls_certificate_set_flags()\fP
it returns an index (greater or equal to zero). That index can be used to other functions to refer to the added key\-pair.
.SH "RETURNS"
On success this functions returns zero, and otherwise a negative value on error (see above for modifying that behavior).
.SH "SINCE"
3.1.11
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
