// Variable Templates For chrono -*- C++ -*-

// Copyright (C) 2014-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file experimental/chrono
 *  This is a TS C++ Library header.
 *  @ingroup libfund-ts
 */

//
// N3932 Variable Templates For Type Traits (Revision 1)
//

#ifndef _GLIBCXX_EXPERIMENTAL_CHRONO
#define _GLIBCXX_EXPERIMENTAL_CHRONO 1

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#include <bits/requires_hosted.h> // experimental is currently omitted

#if __cplusplus >= 201402L

#include <bits/chrono.h> // Only the C++14 parts of <chrono>
#include <experimental/bits/lfts_config.h>

namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

namespace chrono {
namespace experimental
{
inline namespace fundamentals_v1
{
// See C++14 20.12.4, customization traits
template <typename _Rep>
   constexpr bool treat_as_floating_point_v =
     treat_as_floating_point<_Rep>::value;
} // namespace fundamentals_v1
} // namespace experimental
} // namespace chrono
_GLIBCXX_END_NAMESPACE_VERSION
} // namespace std

#endif // __cplusplus <= 201103L

#endif // _GLIBCXX_EXPERIMENTAL_CHRONO
