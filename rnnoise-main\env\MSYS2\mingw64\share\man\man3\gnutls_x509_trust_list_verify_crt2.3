.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_verify_crt2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_verify_crt2 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_verify_crt2(gnutls_x509_trust_list_t " list ", gnutls_x509_crt_t * " cert_list ", unsigned int " cert_list_size ", gnutls_typed_vdata_st * " data ", unsigned int " elements ", unsigned int " flags ", unsigned int * " voutput ", gnutls_verify_output_function " func ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "gnutls_x509_crt_t * cert_list" 12
is the certificate list to be verified
.IP "unsigned int cert_list_size" 12
is the certificate list size
.IP "gnutls_typed_vdata_st * data" 12
an array of typed data
.IP "unsigned int elements" 12
the number of data elements
.IP "unsigned int flags" 12
Flags that may be used to change the verification algorithm. Use OR of the gnutls_certificate_verify_flags enumerations.
.IP "unsigned int * voutput" 12
will hold the certificate verification output.
.IP "gnutls_verify_output_function func" 12
If non\-null will be called on each chain element verification with the output.
.SH "DESCRIPTION"
This function will attempt to verify the given certificate chain and return
its status. The  \fIvoutput\fP parameter will hold an OR'ed sequence of
\fBgnutls_certificate_status_t\fP flags.

When a certificate chain of  \fIcert_list_size\fP with more than one certificates is
provided, the verification status will apply to the first certificate in the chain
that failed verification. The verification process starts from the end of the chain
(from CA to end certificate). The first certificate in the chain must be the end\-certificate
while the rest of the members may be sorted or not.

Additionally a certificate verification profile can be specified
from the ones in \fBgnutls_certificate_verification_profiles_t\fP by
ORing the result of \fBGNUTLS_PROFILE_TO_VFLAGS()\fP to the verification
flags.

Additional verification parameters are possible via the  \fIdata\fP types; the
acceptable types are \fBGNUTLS_DT_DNS_HOSTNAME\fP, \fBGNUTLS_DT_IP_ADDRESS\fP and \fBGNUTLS_DT_KEY_PURPOSE_OID\fP.
The former accepts as data a null\-terminated hostname, and the latter a null\-terminated
object identifier (e.g., \fBGNUTLS_KP_TLS_WWW_SERVER\fP).
If a DNS hostname is provided then this function will compare
the hostname in the end certificate against the given. If names do not match the
\fBGNUTLS_CERT_UNEXPECTED_OWNER\fP status flag will be set. In addition it
will consider certificates provided with \fBgnutls_x509_trust_list_add_named_crt()\fP.

If a key purpose OID is provided and the end\-certificate contains the extended key
usage PKIX extension, it will be required to match the provided OID
or be marked for any purpose, otherwise verification will fail with 
\fBGNUTLS_CERT_PURPOSE_MISMATCH\fP status.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value. Note that verification failure will not result to an
error code, only  \fIvoutput\fP will be updated.
.SH "SINCE"
3.3.8
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
