// Exception Handling support header for -*- C++ -*-

// Copyright (C) 1995-2025 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file exception
 *  This is a Standard C++ Library header.
 */

#ifndef __EXCEPTION__
#define __EXCEPTION__

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#include <bits/c++config.h>
#include <bits/exception.h>

#define __glibcxx_want_uncaught_exceptions
#include <bits/version.h>

extern "C++" {

namespace std _GLIBCXX_VISIBILITY(default)
{
  /** @addtogroup exceptions
   *  @{
   */

  /** If an %exception is thrown which is not listed in a function's
   *  %exception specification, one of these may be thrown.
   *
   *  @ingroup exceptions
   */
  class bad_exception : public exception
  {
  public:
    bad_exception() _GLIBCXX_USE_NOEXCEPT { }

    // This declaration is not useless:
    // http://gcc.gnu.org/onlinedocs/gcc-3.0.2/gcc_6.html#SEC118
    virtual ~bad_exception() _GLIBCXX_TXN_SAFE_DYN _GLIBCXX_USE_NOEXCEPT;

    // See comment in eh_exception.cc.
    virtual const char*
    what() const _GLIBCXX_TXN_SAFE_DYN _GLIBCXX_USE_NOEXCEPT;
  };

  /// If you write a replacement %terminate handler, it must be of this type.
  typedef void (*terminate_handler) ();

  /// Takes a new handler function as an argument, returns the old function.
  terminate_handler set_terminate(terminate_handler) _GLIBCXX_USE_NOEXCEPT;

#if __cplusplus >= 201103L
  /// Return the current terminate handler.
  terminate_handler get_terminate() noexcept;
#endif

  /** The runtime will call this function if %exception handling must be
   *  abandoned for any reason.  It can also be called by the user.  */
  void terminate() _GLIBCXX_USE_NOEXCEPT __attribute__ ((__noreturn__,__cold__));

#if __cplusplus < 201703L || (__cplusplus <= 202002L && _GLIBCXX_USE_DEPRECATED)
  /// If you write a replacement %unexpected handler, it must be of this type.
  typedef void (*_GLIBCXX11_DEPRECATED unexpected_handler) ();

  /** Takes a new handler function as an argument, returns the old function.
   *
   * @deprecated Removed from the C++ standard in C++17
   */
  _GLIBCXX11_DEPRECATED
  unexpected_handler set_unexpected(unexpected_handler) _GLIBCXX_USE_NOEXCEPT;

#if __cplusplus >= 201103L
  /** Return the current unexpected handler.
   *
   * @since C++11
   * @deprecated Removed from the C++ standard in C++17
   */
  _GLIBCXX11_DEPRECATED
  unexpected_handler get_unexpected() noexcept;
#endif

  /** The runtime will call this function if an %exception is thrown which
   *  violates the function's %exception specification.
   *
   * @deprecated Removed from the C++ standard in C++17
   */
  _GLIBCXX11_DEPRECATED
  void unexpected() __attribute__ ((__noreturn__,__cold__));
#endif

  /** [18.6.4]/1:  'Returns true after completing evaluation of a
   *  throw-expression until either completing initialization of the
   *  exception-declaration in the matching handler or entering `unexpected()`
   *  due to the throw; or after entering `terminate()` for any reason
   *  other than an explicit call to `terminate()`.  [Note: This includes
   *  stack unwinding [15.2].  end note]'
   *
   *  2: 'When `uncaught_exception()` is true, throwing an
   *  %exception can result in a call of 1terminate()`
   *  (15.5.1).'
   */
  _GLIBCXX17_DEPRECATED_SUGGEST("std::uncaught_exceptions()")
  bool uncaught_exception() _GLIBCXX_USE_NOEXCEPT __attribute__ ((__pure__));

#ifdef __cpp_lib_uncaught_exceptions // C++ >= 17 || GNU++ >= 03
  /** The number of uncaught exceptions.
   *  @since C++17, or any non-strict mode, e.g. `-std=gnu++98`
   *  @see uncaught_exception()
   */
  int uncaught_exceptions() _GLIBCXX_USE_NOEXCEPT __attribute__ ((__pure__));
#endif

  /// @} group exceptions
} // namespace std

namespace __gnu_cxx _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  /**
   *  @brief A replacement for the standard terminate_handler which
   *  prints more information about the terminating exception (if any)
   *  on stderr.
   *
   *  @ingroup exceptions
   *
   *  Call
   *   @code
   *     std::set_terminate(__gnu_cxx::__verbose_terminate_handler)
   *   @endcode
   *  to use.  For more info, see
   *  http://gcc.gnu.org/onlinedocs/libstdc++/manual/bk01pt02ch06s02.html
   *
   *  In 3.4 and later, this is on by default.
   */
  void __verbose_terminate_handler();

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace

} // extern "C++"

#if (__cplusplus >= 201103L)
#include <bits/exception_ptr.h>
#include <bits/nested_exception.h>
#endif

#endif
