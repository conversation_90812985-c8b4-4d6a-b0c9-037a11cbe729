#!/bin/sh

cat >&2 <<'EOT'
WARNING: git-remote-bzr is now maintained independently.
WARNING: For more information visit https://github.com/felipec/git-remote-bzr
WARNING:
WARNING: You can pick a directory on your $PATH and download it, e.g.:
WARNING:   $ wget -O $HOME/bin/git-remote-bzr \
WARNING:     https://raw.github.com/felipec/git-remote-bzr/master/git-remote-bzr
WARNING:   $ chmod +x $HOME/bin/git-remote-bzr
EOT
