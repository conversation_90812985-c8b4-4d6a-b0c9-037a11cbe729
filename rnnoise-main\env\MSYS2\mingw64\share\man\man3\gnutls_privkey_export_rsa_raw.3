.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_export_rsa_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_export_rsa_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_export_rsa_raw(gnutls_privkey_t " key ", gnutls_datum_t * " m ", gnutls_datum_t * " e ", gnutls_datum_t * " d ", gnutls_datum_t * " p ", gnutls_datum_t * " q ", gnutls_datum_t * " u ", gnutls_datum_t * " e1 ", gnutls_datum_t * " e2 ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
Holds the certificate
.IP "gnutls_datum_t * m" 12
will hold the modulus
.IP "gnutls_datum_t * e" 12
will hold the public exponent
.IP "gnutls_datum_t * d" 12
will hold the private exponent
.IP "gnutls_datum_t * p" 12
will hold the first prime (p)
.IP "gnutls_datum_t * q" 12
will hold the second prime (q)
.IP "gnutls_datum_t * u" 12
will hold the coefficient
.IP "gnutls_datum_t * e1" 12
will hold e1 = d mod (p\-1)
.IP "gnutls_datum_t * e2" 12
will hold e2 = d mod (q\-1)
.SH "DESCRIPTION"
This function will export the RSA private key's parameters found
in the given structure. The new parameters will be allocated using
\fBgnutls_malloc()\fP and will be stored in the appropriate datum. For
EdDSA keys, the  \fIy\fP value should be \fBNULL\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
