RULE_LAUNCH_LINK
----------------

Specify a launcher for link rules.

.. note::
  This property is intended for internal use by :manual:`ctest(1)`.  Projects
  and developers should use the :prop_tgt:`<LANG>_LINKER_LAUNCHER` target
  properties or the associated :variable:`CMAKE_<LANG>_LINKER_LAUNCHER`
  variables instead.

See the :prop_gbl:`global property <RULE_LAUNCH_LINK>` of the same name for
details.  This overrides the global and directory property for a target.
