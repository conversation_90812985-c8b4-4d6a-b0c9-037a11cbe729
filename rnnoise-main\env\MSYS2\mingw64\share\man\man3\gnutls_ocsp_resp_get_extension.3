.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_get_extension" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_get_extension \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_get_extension(gnutls_ocsp_resp_const_t " resp ", unsigned " indx ", gnutls_datum_t * " oid ", unsigned int * " critical ", gnutls_datum_t * " data ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
should contain a \fBgnutls_ocsp_resp_t\fP type
.IP "unsigned indx" 12
Specifies which extension OID to get. Use (0) to get the first one.
.IP "gnutls_datum_t * oid" 12
will hold newly allocated buffer with OID of extension, may be NULL
.IP "unsigned int * critical" 12
output variable with critical flag, may be NULL.
.IP "gnutls_datum_t * data" 12
will hold newly allocated buffer with extension data, may be NULL
.SH "DESCRIPTION"
This function will return all information about the requested
extension in the OCSP response.  The information returned is the
OID, the critical flag, and the data itself.  The extension OID
will be stored as a string.  Any of  \fIoid\fP ,  \fIcritical\fP , and  \fIdata\fP may
be NULL which means that the caller is not interested in getting
that information back.

The caller needs to deallocate memory by calling \fBgnutls_free()\fP on
 \fIoid\fP \->data and  \fIdata\fP \->data.

Since 3.7.0  \fIoid\fP \->size does not account for the terminating null byte.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code is returned.  If you have reached the last
extension available \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will
be returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
