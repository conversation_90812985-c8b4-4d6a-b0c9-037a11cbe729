.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_remove_cas" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_remove_cas \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_remove_cas(gnutls_x509_trust_list_t " list ", const gnutls_x509_crt_t * " clist ", unsigned " clist_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "const gnutls_x509_crt_t * clist" 12
A list of CAs
.IP "unsigned clist_size" 12
The length of the CA list
.SH "DESCRIPTION"
This function will remove the given certificate authorities
from the trusted list.

Note that this function can accept certificates and authorities
not yet known. In that case they will be kept in a separate
block list that will be used during certificate verification.
Unlike \fBgnutls_x509_trust_list_add_cas()\fP there is no deinitialization
restriction for  certificate list provided in this function.
.SH "RETURNS"
The number of removed elements is returned.
.SH "SINCE"
3.1.10
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
