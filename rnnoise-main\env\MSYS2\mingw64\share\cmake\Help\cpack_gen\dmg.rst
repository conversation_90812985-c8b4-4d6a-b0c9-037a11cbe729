CPack DragNDrop Generator
-------------------------

The DragNDrop CPack generator (macOS) creates a DMG image.

Variables specific to CPack DragNDrop generator
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

The following variables are specific to the DragNDrop installers built
on macOS:

.. variable:: CPACK_DMG_VOLUME_NAME

 The volume name of the generated disk image.

 :Default: :variable:`CPACK_PACKAGE_FILE_NAME`

.. variable:: CPACK_DMG_FORMAT

 The disk image format.

 :Default: ``UDZO``

 Common values are ``UDRO`` (UDIF read-only), ``UDZO`` (UDIF
 zlib-compressed) or ``UDBZ`` (UDIF bzip2-compressed). Refer to ``hdiutil(1)`` for
 more information on other available formats.

.. variable:: CPACK_DMG_DS_STORE

 Path to a custom ``.DS_Store`` file. This ``.DS_Store`` file can be used to
 specify the Finder window position/geometry and layout (such as hidden
 toolbars, placement of the icons etc.). This file has to be generated by
 the Finder (either manually or through AppleScript) using a normal folder
 from which the ``.DS_Store`` file can then be extracted.

.. variable:: CPACK_DMG_DS_STORE_SETUP_SCRIPT

 .. versionadded:: 3.5

 Path to a custom AppleScript file.  This AppleScript is used to generate
 a ``.DS_Store`` file which specifies the Finder window position/geometry and
 layout (such as hidden toolbars, placement of the icons etc.).
 By specifying a custom AppleScript there is no need to use
 ``CPACK_DMG_DS_STORE``, as the ``.DS_Store`` that is generated by the AppleScript
 will be packaged.

.. variable:: CPACK_DMG_BACKGROUND_IMAGE

 :Default:

 Path to an image file to be used as the background.  This file will be
 copied to ``.background``/``background.<ext>``, where ``<ext>`` is the original image file
 extension.  The background image is installed into the image before
 ``CPACK_DMG_DS_STORE_SETUP_SCRIPT`` is executed or ``CPACK_DMG_DS_STORE`` is
 installed.  By default no background image is set.

.. variable:: CPACK_DMG_DISABLE_APPLICATIONS_SYMLINK

 .. versionadded:: 3.6

 Default behavior is to include a symlink to ``/Applications`` in the DMG.
 Set this option to ``ON`` to avoid adding the symlink.

.. variable:: CPACK_DMG_SLA_USE_RESOURCE_FILE_LICENSE

 .. versionadded:: 3.23

 :Default: ``OFF``

 Control whether :variable:`CPACK_RESOURCE_FILE_LICENSE`, if set to a
 non-default value, is used as the license agreement provided when
 mounting the DMG.  If :variable:`!CPACK_DMG_SLA_USE_RESOURCE_FILE_LICENSE` is
 not set, :manual:`cpack(1)` defaults to off.

 In a CMake project that uses the :module:`CPack` module to generate
 ``CPackConfig.cmake``, :variable:`!CPACK_DMG_SLA_USE_RESOURCE_FILE_LICENSE`
 must be explicitly enabled by the project to activate the SLA.
 See policy :policy:`CMP0133`.

 .. note::

  This option was added in response to macOS 12.0's deprecation of
  the ``hdiutil udifrez`` command to make its use optional.
  CPack 3.22 and below always use :variable:`CPACK_RESOURCE_FILE_LICENSE`,
  if set to a non-default value, as the DMG license.

.. variable:: CPACK_DMG_SLA_DIR

  .. versionadded:: 3.5

  Directory where license and menu files for different languages are stored.
  Setting this causes CPack to look for a ``<language>.menu.txt`` and
  ``<language>.license.txt`` or ``<language>.license.rtf`` file for every
  language defined in :variable:`CPACK_DMG_SLA_LANGUAGES`.  If both this variable and
  :variable:`CPACK_RESOURCE_FILE_LICENSE` are set, CPack will only look for the menu
  files and use the same license file for all languages.  If both
  ``<language>.license.txt`` and ``<language>.license.rtf`` exist, the ``.txt``
  file will be used.

  .. versionadded:: 3.17
    RTF support.

.. variable:: CPACK_DMG_SLA_LANGUAGES

  .. versionadded:: 3.5

  Languages for which a license agreement is provided when mounting the
  generated DMG. A menu file consists of 9 lines of text. The first line is
  is the name of the language itself, uppercase, in English (e.g. German).
  The other lines are translations of the following strings:

  - Agree
  - Disagree
  - Print
  - Save...
  - You agree to the terms of the License Agreement when you click the
    "Agree" button.
  - Software License Agreement
  - This text cannot be saved. The disk may be full or locked, or the file
    may be locked.
  - Unable to print. Make sure you have selected a printer.

  For every language in this list, CPack will try to find files
  ``<language>.menu.txt`` and ``<language>.license.txt`` in the directory
  specified by the :variable:`CPACK_DMG_SLA_DIR` variable.

.. variable:: CPACK_DMG_<component>_FILE_NAME

 .. versionadded:: 3.17

 File name when packaging ``<component>`` as its own DMG
 (:variable:`CPACK_COMPONENTS_GROUPING` set to ``IGNORE``).

 :Default: ``CPACK_PACKAGE_FILE_NAME-<component>``

.. variable:: CPACK_DMG_FILESYSTEM

 .. versionadded:: 3.21

 :Default: ``HFS+``

 The filesystem format. Common values are ``APFS`` and ``HFS+``.
 See ``man hdiutil`` for a full list of supported formats.

.. variable:: CPACK_COMMAND_HDIUTIL

 Path to the ``hdiutil(1)`` command used to operate on disk image files on
 macOS. This variable can be used to override the automatically detected
 command (or specify its location if the auto-detection fails to find it).

.. variable:: CPACK_COMMAND_SETFILE

 Path to the ``SetFile(1)`` command used to set extended attributes on files and
 directories on macOS. This variable can be used to override the
 automatically detected command (or specify its location if the
 auto-detection fails to find it).

.. variable:: CPACK_COMMAND_REZ

 Path to the ``Rez(1)`` command used to compile resources on macOS. This
 variable can be used to override the automatically detected command (or
 specify its location if the auto-detection fails to find it).
