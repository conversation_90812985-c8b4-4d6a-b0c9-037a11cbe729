.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_decode_ber_digest_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_decode_ber_digest_info \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_decode_ber_digest_info(const gnutls_datum_t * " info ", gnutls_digest_algorithm_t * " hash ", unsigned char * " digest ", unsigned int * " digest_size ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * info" 12
an RSA BER encoded DigestInfo structure
.IP "gnutls_digest_algorithm_t * hash" 12
will contain the hash algorithm of the structure
.IP "unsigned char * digest" 12
will contain the hash output of the structure
.IP "unsigned int * digest_size" 12
will contain the hash size of the structure; initially must hold the maximum size of  \fIdigest\fP 
.SH "DESCRIPTION"
This function will parse an RSA PKCS\fB1\fP 1.5 DigestInfo structure
and report the hash algorithm used as well as the digest data.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
