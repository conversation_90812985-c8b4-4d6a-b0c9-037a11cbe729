.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dtls_set_mtu" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dtls_set_mtu \- API function
.SH SYNOPSIS
.B #include <gnutls/dtls.h>
.sp
.BI "void gnutls_dtls_set_mtu(gnutls_session_t " session ", unsigned int " mtu ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int mtu" 12
The maximum transfer unit of the transport
.SH "DESCRIPTION"
This function will set the maximum transfer unit of the transport
that DTLS packets are sent over. Note that this should exclude
the IP (or IPv6) and UDP headers. So for DTLS over IPv6 on an
Ethernet device with MTU 1500, the DTLS MTU set with this function
would be 1500 \- 40 (IPV6 header) \- 8 (UDP header) = 1452.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
