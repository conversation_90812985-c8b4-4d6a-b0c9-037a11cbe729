set ::msgcat::header "Project-Id-Version: gitk\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2008-03-25 11:20+0100\nLast-Translator: Santiago Gala <<EMAIL>>\nLanguage-Team: Spanish\nLanguage: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset es "Couldn't get list of unmerged files:" "Imposible obtener la lista de archivos pendientes de fusi\u00f3n:"
::msgcat::mcset es "No files selected: --merge specified but no files are unmerged." "No hay archivos seleccionados: se seleccion\u00f3 la opci\u00f3n --merge pero no hay archivos pendientes de fusi\u00f3n."
::msgcat::mcset es "No files selected: --merge specified but no unmerged files are within file limit." "No hay archivos seleccionados: se seleccion\u00f3 la opci\u00f3n --merge pero los archivos especificados no necesitan fusi\u00f3n."
::msgcat::mcset es "Reading" "Leyendo"
::msgcat::mcset es "Reading commits..." "Leyendo revisiones..."
::msgcat::mcset es "No commits selected" "No se seleccionaron revisiones"
::msgcat::mcset es "Command line" "L\u00ednea de comandos"
::msgcat::mcset es "Can't parse git log output:" "Error analizando la salida de git log:"
::msgcat::mcset es "No commit information available" "Falta informaci\u00f3n sobre las revisiones"
::msgcat::mcset es "OK" "Aceptar"
::msgcat::mcset es "Cancel" "Cancelar"
::msgcat::mcset es "&Update" "Actualizar"
::msgcat::mcset es "Reread re&ferences" "Releer referencias"
::msgcat::mcset es "&List references" "Lista de referencias"
::msgcat::mcset es "&Quit" "Salir"
::msgcat::mcset es "&File" "Archivo"
::msgcat::mcset es "&Preferences" "Preferencias"
::msgcat::mcset es "&Edit" "Editar"
::msgcat::mcset es "&New view..." "Nueva vista..."
::msgcat::mcset es "&Edit view..." "Modificar vista..."
::msgcat::mcset es "&Delete view" "Eliminar vista"
::msgcat::mcset es "&All files" "Todos los archivos"
::msgcat::mcset es "&View" "Vista"
::msgcat::mcset es "&About gitk" "Acerca de gitk"
::msgcat::mcset es "&Key bindings" "Combinaciones de teclas"
::msgcat::mcset es "&Help" "Ayuda"
::msgcat::mcset es "SHA1 ID:" "SHA1 ID:"
::msgcat::mcset es "Find" "Buscar"
::msgcat::mcset es "commit" "revisi\u00f3n"
::msgcat::mcset es "containing:" "que contiene:"
::msgcat::mcset es "touching paths:" "que modifica la ruta:"
::msgcat::mcset es "adding/removing string:" "que a\u00f1ade/elimina cadena:"
::msgcat::mcset es "Exact" "Exacto"
::msgcat::mcset es "IgnCase" "NoMay\u00fas"
::msgcat::mcset es "Regexp" "Regex"
::msgcat::mcset es "All fields" "Todos los campos"
::msgcat::mcset es "Headline" "T\u00edtulo"
::msgcat::mcset es "Comments" "Comentarios"
::msgcat::mcset es "Author" "Autor"
::msgcat::mcset es "Search" "Buscar"
::msgcat::mcset es "Diff" "Diferencia"
::msgcat::mcset es "Old version" "Versi\u00f3n antigua"
::msgcat::mcset es "New version" "Versi\u00f3n nueva"
::msgcat::mcset es "Lines of context" "L\u00edneas de contexto"
::msgcat::mcset es "Ignore space change" "Ignora cambios de espaciado"
::msgcat::mcset es "Patch" "Parche"
::msgcat::mcset es "Tree" "\u00c1rbol"
::msgcat::mcset es "Diff this -> selected" "Diferencia de esta -> seleccionada"
::msgcat::mcset es "Diff selected -> this" "Diferencia de seleccionada -> esta"
::msgcat::mcset es "Make patch" "Crear patch"
::msgcat::mcset es "Create tag" "Crear etiqueta"
::msgcat::mcset es "Write commit to file" "Escribir revisiones a archivo"
::msgcat::mcset es "Create new branch" "Crear nueva rama"
::msgcat::mcset es "Cherry-pick this commit" "A\u00f1adir esta revisi\u00f3n a la rama actual (cherry-pick)"
::msgcat::mcset es "Reset HEAD branch to here" "Traer la rama HEAD aqu\u00ed"
::msgcat::mcset es "Check out this branch" "Cambiar a esta rama"
::msgcat::mcset es "Remove this branch" "Eliminar esta rama"
::msgcat::mcset es "Highlight this too" "Seleccionar tambi\u00e9n"
::msgcat::mcset es "Highlight this only" "Seleccionar s\u00f3lo"
::msgcat::mcset es "Close" "Cerrar"
::msgcat::mcset es "Gitk key bindings" "Combinaciones de tecla de Gitk"
::msgcat::mcset es "Gitk key bindings:" "Combinaciones de tecla de Gitk:"
::msgcat::mcset es "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Salir"
::msgcat::mcset es "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009Ir a la primera revisi\u00f3n"
::msgcat::mcset es "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009Ir a la \u00faltima revisi\u00f3n"
::msgcat::mcset es "<Right>, x, l\u0009Go forward in history list" "<Right>, x, l\u0009Avanzar en la historia"
::msgcat::mcset es "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009Subir una p\u00e1gina en la lista de revisiones"
::msgcat::mcset es "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009Bajar una p\u00e1gina en la lista de revisiones"
::msgcat::mcset es "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009Desplazarse al inicio de la lista de revisiones"
::msgcat::mcset es "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009Desplazarse al final de la lista de revisiones"
::msgcat::mcset es "<%s-Up>\u0009Scroll commit list up one line" "<%s-Up>\u0009Desplazar una l\u00ednea hacia arriba la lista de revisiones"
::msgcat::mcset es "<%s-Down>\u0009Scroll commit list down one line" "<%s-Down>\u0009Desplazar una l\u00ednea hacia abajo la lista de revisiones"
::msgcat::mcset es "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009Desplazar una p\u00e1gina hacia arriba la lista de revisiones"
::msgcat::mcset es "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009Desplazar una p\u00e1gina hacia abajo la lista de revisiones"
::msgcat::mcset es "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Up>\u0009Buscar hacia atr\u00e1s (arriba, revisiones siguientes)"
::msgcat::mcset es "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Down>\u0009Buscar hacia adelante (abajo, revisiones anteriores)"
::msgcat::mcset es "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009Desplaza hacia arriba una p\u00e1gina la vista de diferencias"
::msgcat::mcset es "<Backspace>\u0009Scroll diff view up one page" "<Backspace>\u0009Desplaza hacia arriba una p\u00e1gina la vista de diferencias"
::msgcat::mcset es "<Space>\u0009\u0009Scroll diff view down one page" "<Space>\u0009\u0009Desplaza hacia abajo una p\u00e1gina la vista de diferencias"
::msgcat::mcset es "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009Desplaza hacia arriba 18 l\u00edneas la vista de diferencias"
::msgcat::mcset es "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009Desplaza hacia abajo 18 l\u00edneas la vista de diferencias"
::msgcat::mcset es "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009Buscar"
::msgcat::mcset es "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009Buscar el siguiente"
::msgcat::mcset es "<Return>\u0009Move to next find hit" "<Return>\u0009Buscar el siguiente"
::msgcat::mcset es "?\u0009\u0009Move to previous find hit" "?\u0009\u0009Buscar el anterior"
::msgcat::mcset es "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009Desplazar la vista de diferencias al archivo siguiente"
::msgcat::mcset es "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009Buscar siguiente en la vista de diferencias"
::msgcat::mcset es "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009Buscar anterior en la vista de diferencias"
::msgcat::mcset es "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009Aumentar tama\u00f1o del texto"
::msgcat::mcset es "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009Aumentar tama\u00f1o del texto"
::msgcat::mcset es "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009Disminuir tama\u00f1o del texto"
::msgcat::mcset es "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009Disminuir tama\u00f1o del texto"
::msgcat::mcset es "<F5>\u0009\u0009Update" "<F5>\u0009\u0009Actualizar"
::msgcat::mcset es "Searching" "Buscando"
::msgcat::mcset es "Gitk view definition" "Definici\u00f3n de vistas de Gitk"
::msgcat::mcset es "Remember this view" "Recordar esta vista"
::msgcat::mcset es "Enter files and directories to include, one per line:" "Introducir archivos y directorios a incluir, uno por l\u00ednea:"
::msgcat::mcset es "Command to generate more commits to include:" "Comando que genera m\u00e1s revisiones a incluir:"
::msgcat::mcset es "Error in commit selection arguments:" "Error en los argumentos de selecci\u00f3n de las revisiones:"
::msgcat::mcset es "None" "Ninguno"
::msgcat::mcset es "Descendant" "Descendiente"
::msgcat::mcset es "Not descendant" "No descendiente"
::msgcat::mcset es "Ancestor" "Antepasado"
::msgcat::mcset es "Not ancestor" "No antepasado"
::msgcat::mcset es "Local changes checked in to index but not committed" "Cambios locales a\u00f1adidos al \u00edndice pero sin completar revisi\u00f3n"
::msgcat::mcset es "Local uncommitted changes, not checked in to index" "Cambios locales sin a\u00f1adir al \u00edndice"
::msgcat::mcset es "Tags:" "Etiquetas:"
::msgcat::mcset es "Parent" "Padre"
::msgcat::mcset es "Child" "Hija"
::msgcat::mcset es "Branch" "Rama"
::msgcat::mcset es "Follows" "Sigue-a"
::msgcat::mcset es "Precedes" "Precede-a"
::msgcat::mcset es "Goto:" "Ir a:"
::msgcat::mcset es "Short SHA1 id %s is ambiguous" "La id SHA1 abreviada %s es ambigua"
::msgcat::mcset es "SHA1 id %s is not known" "La id SHA1 %s es desconocida"
::msgcat::mcset es "Date" "Fecha"
::msgcat::mcset es "Children" "Hijas"
::msgcat::mcset es "Reset %s branch to here" "Poner la rama %s en esta revisi\u00f3n"
::msgcat::mcset es "Top" "Origen"
::msgcat::mcset es "From" "De"
::msgcat::mcset es "To" "A"
::msgcat::mcset es "Generate patch" "Generar parche"
::msgcat::mcset es "From:" "De:"
::msgcat::mcset es "To:" "Para:"
::msgcat::mcset es "Reverse" "Invertir"
::msgcat::mcset es "Output file:" "Escribir a archivo:"
::msgcat::mcset es "Generate" "Generar"
::msgcat::mcset es "Error creating patch:" "Error en la creaci\u00f3n del parche:"
::msgcat::mcset es "ID:" "ID:"
::msgcat::mcset es "Tag name:" "Nombre de etiqueta:"
::msgcat::mcset es "Create" "Crear"
::msgcat::mcset es "No tag name specified" "No se ha especificado etiqueta"
::msgcat::mcset es "Tag \"%s\" already exists" "La etiqueta \"%s\" ya existe"
::msgcat::mcset es "Error creating tag:" "Error al crear la etiqueta:"
::msgcat::mcset es "Command:" "Comando:"
::msgcat::mcset es "Write" "Escribir"
::msgcat::mcset es "Error writing commit:" "Error al escribir revisi\u00f3n:"
::msgcat::mcset es "Name:" "Nombre:"
::msgcat::mcset es "Please specify a name for the new branch" "Especifique un nombre para la nueva rama"
::msgcat::mcset es "Commit %s is already included in branch %s -- really re-apply it?" "La revisi\u00f3n %s ya est\u00e1 incluida en la rama %s -- \u00bfVolver a aplicarla?"
::msgcat::mcset es "Cherry-picking" "Eligiendo revisiones (cherry-picking)"
::msgcat::mcset es "No changes committed" "No se han guardado cambios"
::msgcat::mcset es "Confirm reset" "Confirmar git reset"
::msgcat::mcset es "Reset branch %s to %s?" "\u00bfReponer la rama %s a %s?"
::msgcat::mcset es "Reset type:" "Tipo de reposici\u00f3n:"
::msgcat::mcset es "Soft: Leave working tree and index untouched" "Suave: No altera la copia de trabajo ni el \u00edndice"
::msgcat::mcset es "Mixed: Leave working tree untouched, reset index" "Mixta: Actualiza el \u00edndice, no altera la copia de trabajo"
::msgcat::mcset es "Hard: Reset working tree and index\n(discard ALL local changes)" "Dura: Actualiza el \u00edndice y la copia de trabajo\n(abandona TODAS las modificaciones locales)"
::msgcat::mcset es "Resetting" "Reponiendo"
::msgcat::mcset es "Checking out" "Creando copia de trabajo"
::msgcat::mcset es "Cannot delete the currently checked-out branch" "No se puede borrar la rama actual"
::msgcat::mcset es "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "Las revisiones de la rama %s no est\u00e1n presentes en otras ramas.\n\u00bfBorrar la rama %s?"
::msgcat::mcset es "Tags and heads: %s" "Etiquetas y ramas: %s"
::msgcat::mcset es "Filter" "Filtro"
::msgcat::mcset es "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "Error al leer la topolog\u00eda de revisiones: la informaci\u00f3n sobre las ramas y etiquetas precedentes y siguientes ser\u00e1 incompleta."
::msgcat::mcset es "Tag" "Etiqueta"
::msgcat::mcset es "Id" "Id"
::msgcat::mcset es "Gitk font chooser" "Selector de tipograf\u00edas gitk"
::msgcat::mcset es "B" "B"
::msgcat::mcset es "I" "I"
::msgcat::mcset es "Commit list display options" "Opciones de visualizaci\u00f3n de la lista de revisiones"
::msgcat::mcset es "Maximum graph width (lines)" "Ancho m\u00e1ximo del gr\u00e1fico (en l\u00edneas)"
::msgcat::mcset es "Maximum graph width (% of pane)" "Ancho m\u00e1ximo del gr\u00e1fico (en % del panel)"
::msgcat::mcset es "Show local changes" "Mostrar cambios locales"
::msgcat::mcset es "Diff display options" "Opciones de visualizaci\u00f3n de diferencias"
::msgcat::mcset es "Tab spacing" "Espaciado de tabulador"
::msgcat::mcset es "Limit diffs to listed paths" "Limitar las diferencias a las rutas seleccionadas"
::msgcat::mcset es "Colors: press to choose" "Colores: pulse para seleccionar"
::msgcat::mcset es "Background" "Fondo"
::msgcat::mcset es "Foreground" "Primer plano"
::msgcat::mcset es "Diff: old lines" "Diff: l\u00edneas viejas"
::msgcat::mcset es "Diff: new lines" "Diff: l\u00edneas nuevas"
::msgcat::mcset es "Diff: hunk header" "Diff: cabecera de fragmento"
::msgcat::mcset es "Select bg" "Color de fondo de la selecci\u00f3n"
::msgcat::mcset es "Fonts: press to choose" "Tipograf\u00edas: pulse para elegir"
::msgcat::mcset es "Main font" "Tipograf\u00eda principal"
::msgcat::mcset es "Diff display font" "Tipograf\u00eda para diferencias"
::msgcat::mcset es "User interface font" "Tipograf\u00eda para interfaz de usuario"
::msgcat::mcset es "Gitk preferences" "Preferencias de gitk"
::msgcat::mcset es "Gitk: choose color for %s" "Gitk: elegir color para %s"
::msgcat::mcset es "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "Esta versi\u00f3n de Tcl/Tk es demasiado antigua.\n Gitk requiere Tcl/Tk versi\u00f3n 8.4 o superior."
::msgcat::mcset es "Cannot find a git repository here." "No hay un repositorio git aqu\u00ed."
::msgcat::mcset es "Ambiguous argument '%s': both revision and filename" "Argumento ambiguo: '%s' es tanto una revisi\u00f3n como un nombre de archivo"
::msgcat::mcset es "Bad arguments to gitk:" "Argumentos incorrectos a Gitk:"
