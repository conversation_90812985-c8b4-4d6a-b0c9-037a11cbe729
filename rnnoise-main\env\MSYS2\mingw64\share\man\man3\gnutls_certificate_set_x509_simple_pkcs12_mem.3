.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_x509_simple_pkcs12_mem" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_x509_simple_pkcs12_mem \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_x509_simple_pkcs12_mem(gnutls_certificate_credentials_t " res ", const gnutls_datum_t * " p12blob ", gnutls_x509_crt_fmt_t " type ", const char * " password ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "const gnutls_datum_t * p12blob" 12
the PKCS\fB12\fP blob.
.IP "gnutls_x509_crt_fmt_t type" 12
is PEM or DER of the  \fIpkcs12file\fP .
.IP "const char * password" 12
optional password used to decrypt PKCS\fB12\fP file, bags and keys.
.SH "DESCRIPTION"
This function sets a certificate/private key pair and/or a CRL in
the gnutls_certificate_credentials_t type.  This function may
be called more than once (in case multiple keys/certificates exist
for the server).

Encrypted PKCS\fB12\fP bags and PKCS\fB8\fP private keys are supported.  However,
only password based security, and the same password for all
operations, are supported.

PKCS\fB12\fP file may contain many keys and/or certificates, and this
function will try to auto\-detect based on the key ID the certificate
and key pair to use. If the PKCS\fB12\fP file contain the issuer of
the selected certificate, it will be appended to the certificate
to form a chain.

If more than one private keys are stored in the PKCS\fB12\fP file,
then only one key will be read (and it is undefined which one).

It is believed that the limitations of this function is acceptable
for most usage, and that any more flexibility would introduce
complexity that would make it harder to use this functionality at
all.

Note that, this function by default returns zero on success and a negative value on error.
Since 3.5.6, when the flag \fBGNUTLS_CERTIFICATE_API_V2\fP is set using \fBgnutls_certificate_set_flags()\fP
it returns an index (greater or equal to zero). That index can be used to other functions to refer to the added key\-pair.
.SH "RETURNS"
On success this functions returns zero, and otherwise a negative value on error (see above for modifying that behavior).
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
