.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_import_x509_crq" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_import_x509_crq \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_import_x509_crq(gnutls_pubkey_t " key ", gnutls_x509_crq_t " crq ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
The public key
.IP "gnutls_x509_crq_t crq" 12
The certificate to be imported
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will import the given public key to the abstract
\fBgnutls_pubkey_t\fP type.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
