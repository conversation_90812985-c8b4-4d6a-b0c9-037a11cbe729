.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_sign_hash2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_sign_hash2 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_sign_hash2(gnutls_privkey_t " signer ", gnutls_sign_algorithm_t " algo ", unsigned int " flags ", const gnutls_datum_t * " hash_data ", gnutls_datum_t * " signature ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t signer" 12
Holds the signer's key
.IP "gnutls_sign_algorithm_t algo" 12
The signature algorithm used
.IP "unsigned int flags" 12
Zero or one of \fBgnutls_privkey_flags_t\fP
.IP "const gnutls_datum_t * hash_data" 12
holds the data to be signed
.IP "gnutls_datum_t * signature" 12
will contain newly allocated signature
.SH "DESCRIPTION"
This function will sign the given hashed data using the specified signature
algorithm. This function is an enhancement of \fBgnutls_privkey_sign_hash()\fP,
as it allows utilizing a alternative signature algorithm where possible
(e.g, use an RSA key with RSA\-PSS).

The flags may be \fBGNUTLS_PRIVKEY_SIGN_FLAG_TLS1_RSA\fP.
In that case this function will ignore  \fIhash_algo\fP and perform a raw PKCS1 signature.
Note that this flag is supported since 3.6.9.

Note also that, not all algorithm support signing already hashed data. When
signing with Ed25519, \fBgnutls_privkey_sign_data2()\fP should be used instead.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
