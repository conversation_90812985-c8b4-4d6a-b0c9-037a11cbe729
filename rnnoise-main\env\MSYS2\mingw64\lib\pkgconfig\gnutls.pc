# Process this file with autoconf to produce a pkg-config metadata file.

# Copyright (C) 2004-2012 Free Software Foundation, Inc.

# Copying and distribution of this file, with or without modification,
# are permitted in any medium without royalty provided the copyright
# notice and this notice are preserved.  This file is offered as-is,
# without any warranty.

# Author: <PERSON>

prefix=/mingw64
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include

Name: GnuTLS
Description: Transport Security Layer implementation for the GNU system
URL: https://www.gnutls.org/
Version: 3.8.9
Libs: -L${libdir} -lgnutls
Libs.private: -lintl -lws2_32 -lws2_32    -lgmp -lunistring -latomic  -ladvapi32 -lcrypt32 -lncrypt -lbcrypt
Requires.private: nettle, hogweed, libtasn1, libidn2, zlib, libbrotlienc, libbrotlidec, libzstd
Cflags: -I${includedir}
