.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_set_client_credentials2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_set_client_credentials2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_psk_set_client_credentials2(gnutls_psk_client_credentials_t " res ", const gnutls_datum_t * " username ", const gnutls_datum_t * " key ", gnutls_psk_key_flags " flags ");"
.SH ARGUMENTS
.IP "gnutls_psk_client_credentials_t res" 12
is a \fBgnutls_psk_client_credentials_t\fP type.
.IP "const gnutls_datum_t * username" 12
is the userid
.IP "const gnutls_datum_t * key" 12
is the user's key
.IP "gnutls_psk_key_flags flags" 12
indicate the format of the key, either
\fBGNUTLS_PSK_KEY_RAW\fP or \fBGNUTLS_PSK_KEY_HEX\fP.
.SH "DESCRIPTION"
This function is identical to \fBgnutls_psk_set_client_credentials()\fP,
except that it allows a non\-null\-terminated username to be introduced.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
