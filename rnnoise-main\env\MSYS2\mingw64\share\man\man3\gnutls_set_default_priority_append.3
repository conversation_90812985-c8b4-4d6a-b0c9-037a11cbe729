.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_set_default_priority_append" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_set_default_priority_append \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_set_default_priority_append(gnutls_session_t " session ", const char * " add_prio ", const char ** " err_pos ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const char * add_prio" 12
is a string describing priorities to be appended to default
.IP "const char ** err_pos" 12
In case of an error this will have the position in the string the error occurred
.IP "unsigned flags" 12
must be zero
.SH "DESCRIPTION"
Sets the default priority on the ciphers, key exchange methods,
and macs with the additional options in  \fIadd_prio\fP . This is the recommended method of
setting the defaults when only few additional options are to be added. This promotes
consistency between applications using GnuTLS, and allows GnuTLS using applications
to update settings in par with the library.

The  \fIadd_prio\fP string should start as a normal priority string, e.g.,
'\-VERS\-TLS\-ALL:+VERS\-TLS1.3:%COMPAT' or '%FORCE_ETM'. That is, it must not start
with ':'.

To allow a user to override the defaults (e.g., when a user interface
or configuration file is available), the functions
\fBgnutls_priority_set_direct()\fP or \fBgnutls_priority_set()\fP can
be used.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
