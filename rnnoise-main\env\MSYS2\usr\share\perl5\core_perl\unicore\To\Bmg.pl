# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   IT IS DEPRECATED TO USE THIS FILE   !!!!!!!

# This file is for internal use by core Perl only.  It is retained for
# backwards compatibility with applications that may have come to rely on it,
# but its format and even its name or existence are subject to change without
# notice in a future Perl version.  Don't use it directly.  Instead, its
# contents are now retrievable through a stable API in the Unicode::UCD
# module: Unicode::UCD::prop_invmap('Bidi_Mirroring_Glyph') (Values for individual
# code points can be retrieved via Unicode::UCD::charprop());



# The name this table is to be known by, with the format of the mappings in
# the main body of the table, and what all code points missing from this file
# map to.
$Unicode::UCD::SwashInfo{'ToBmg'}{'format'} = 'x'; # non-negative hex whole number; a code point
$Unicode::UCD::SwashInfo{'ToBmg'}{'missing'} = ''; # code point maps to the empty string

return <<'END';
0028		0029
0029		0028
003C		003E
003E		003C
005B		005D
005D		005B
007B		007D
007D		007B
00AB		00BB
00BB		00AB
0F3A		0F3B
0F3B		0F3A
0F3C		0F3D
0F3D		0F3C
169B		169C
169C		169B
2039		203A
203A		2039
2045		2046
2046		2045
207D		207E
207E		207D
208D		208E
208E		208D
2208		220B
2209		220C
220A		220D
220B		2208
220C		2209
220D		220A
2215		29F5
221F		2BFE
2220		29A3
2221		299B
2222		29A0
2224		2AEE
223C		223D
223D		223C
2243		22CD
2245		224C
224C		2245
2252		2253
2253		2252
2254		2255
2255		2254
2264		2265
2265		2264
2266		2267
2267		2266
2268		2269
2269		2268
226A		226B
226B		226A
226E		226F
226F		226E
2270		2271
2271		2270
2272		2273
2273		2272
2274		2275
2275		2274
2276		2277
2277		2276
2278		2279
2279		2278
227A		227B
227B		227A
227C		227D
227D		227C
227E		227F
227F		227E
2280		2281
2281		2280
2282		2283
2283		2282
2284		2285
2285		2284
2286		2287
2287		2286
2288		2289
2289		2288
228A		228B
228B		228A
228F		2290
2290		228F
2291		2292
2292		2291
2298		29B8
22A2		22A3
22A3		22A2
22A6		2ADE
22A8		2AE4
22A9		2AE3
22AB		2AE5
22B0		22B1
22B1		22B0
22B2		22B3
22B3		22B2
22B4		22B5
22B5		22B4
22B6		22B7
22B7		22B6
22B8		27DC
22C9		22CA
22CA		22C9
22CB		22CC
22CC		22CB
22CD		2243
22D0		22D1
22D1		22D0
22D6		22D7
22D7		22D6
22D8		22D9
22D9		22D8
22DA		22DB
22DB		22DA
22DC		22DD
22DD		22DC
22DE		22DF
22DF		22DE
22E0		22E1
22E1		22E0
22E2		22E3
22E3		22E2
22E4		22E5
22E5		22E4
22E6		22E7
22E7		22E6
22E8		22E9
22E9		22E8
22EA		22EB
22EB		22EA
22EC		22ED
22ED		22EC
22F0		22F1
22F1		22F0
22F2		22FA
22F3		22FB
22F4		22FC
22F6		22FD
22F7		22FE
22FA		22F2
22FB		22F3
22FC		22F4
22FD		22F6
22FE		22F7
2308		2309
2309		2308
230A		230B
230B		230A
2329		232A
232A		2329
2768		2769
2769		2768
276A		276B
276B		276A
276C		276D
276D		276C
276E		276F
276F		276E
2770		2771
2771		2770
2772		2773
2773		2772
2774		2775
2775		2774
27C3		27C4
27C4		27C3
27C5		27C6
27C6		27C5
27C8		27C9
27C9		27C8
27CB		27CD
27CD		27CB
27D5		27D6
27D6		27D5
27DC		22B8
27DD		27DE
27DE		27DD
27E2		27E3
27E3		27E2
27E4		27E5
27E5		27E4
27E6		27E7
27E7		27E6
27E8		27E9
27E9		27E8
27EA		27EB
27EB		27EA
27EC		27ED
27ED		27EC
27EE		27EF
27EF		27EE
2983		2984
2984		2983
2985		2986
2986		2985
2987		2988
2988		2987
2989		298A
298A		2989
298B		298C
298C		298B
298D		2990
298E		298F
298F		298E
2990		298D
2991		2992
2992		2991
2993		2994
2994		2993
2995		2996
2996		2995
2997		2998
2998		2997
299B		2221
29A0		2222
29A3		2220
29A4		29A5
29A5		29A4
29A8		29A9
29A9		29A8
29AA		29AB
29AB		29AA
29AC		29AD
29AD		29AC
29AE		29AF
29AF		29AE
29B8		2298
29C0		29C1
29C1		29C0
29C4		29C5
29C5		29C4
29CF		29D0
29D0		29CF
29D1		29D2
29D2		29D1
29D4		29D5
29D5		29D4
29D8		29D9
29D9		29D8
29DA		29DB
29DB		29DA
29E8		29E9
29E9		29E8
29F5		2215
29F8		29F9
29F9		29F8
29FC		29FD
29FD		29FC
2A2B		2A2C
2A2C		2A2B
2A2D		2A2E
2A2E		2A2D
2A34		2A35
2A35		2A34
2A3C		2A3D
2A3D		2A3C
2A64		2A65
2A65		2A64
2A79		2A7A
2A7A		2A79
2A7B		2A7C
2A7C		2A7B
2A7D		2A7E
2A7E		2A7D
2A7F		2A80
2A80		2A7F
2A81		2A82
2A82		2A81
2A83		2A84
2A84		2A83
2A85		2A86
2A86		2A85
2A87		2A88
2A88		2A87
2A89		2A8A
2A8A		2A89
2A8B		2A8C
2A8C		2A8B
2A8D		2A8E
2A8E		2A8D
2A8F		2A90
2A90		2A8F
2A91		2A92
2A92		2A91
2A93		2A94
2A94		2A93
2A95		2A96
2A96		2A95
2A97		2A98
2A98		2A97
2A99		2A9A
2A9A		2A99
2A9B		2A9C
2A9C		2A9B
2A9D		2A9E
2A9E		2A9D
2A9F		2AA0
2AA0		2A9F
2AA1		2AA2
2AA2		2AA1
2AA6		2AA7
2AA7		2AA6
2AA8		2AA9
2AA9		2AA8
2AAA		2AAB
2AAB		2AAA
2AAC		2AAD
2AAD		2AAC
2AAF		2AB0
2AB0		2AAF
2AB1		2AB2
2AB2		2AB1
2AB3		2AB4
2AB4		2AB3
2AB5		2AB6
2AB6		2AB5
2AB7		2AB8
2AB8		2AB7
2AB9		2ABA
2ABA		2AB9
2ABB		2ABC
2ABC		2ABB
2ABD		2ABE
2ABE		2ABD
2ABF		2AC0
2AC0		2ABF
2AC1		2AC2
2AC2		2AC1
2AC3		2AC4
2AC4		2AC3
2AC5		2AC6
2AC6		2AC5
2AC7		2AC8
2AC8		2AC7
2AC9		2ACA
2ACA		2AC9
2ACB		2ACC
2ACC		2ACB
2ACD		2ACE
2ACE		2ACD
2ACF		2AD0
2AD0		2ACF
2AD1		2AD2
2AD2		2AD1
2AD3		2AD4
2AD4		2AD3
2AD5		2AD6
2AD6		2AD5
2ADE		22A6
2AE3		22A9
2AE4		22A8
2AE5		22AB
2AEC		2AED
2AED		2AEC
2AEE		2224
2AF7		2AF8
2AF8		2AF7
2AF9		2AFA
2AFA		2AF9
2BFE		221F
2E02		2E03
2E03		2E02
2E04		2E05
2E05		2E04
2E09		2E0A
2E0A		2E09
2E0C		2E0D
2E0D		2E0C
2E1C		2E1D
2E1D		2E1C
2E20		2E21
2E21		2E20
2E22		2E23
2E23		2E22
2E24		2E25
2E25		2E24
2E26		2E27
2E27		2E26
2E28		2E29
2E29		2E28
2E55		2E56
2E56		2E55
2E57		2E58
2E58		2E57
2E59		2E5A
2E5A		2E59
2E5B		2E5C
2E5C		2E5B
3008		3009
3009		3008
300A		300B
300B		300A
300C		300D
300D		300C
300E		300F
300F		300E
3010		3011
3011		3010
3014		3015
3015		3014
3016		3017
3017		3016
3018		3019
3019		3018
301A		301B
301B		301A
FE59		FE5A
FE5A		FE59
FE5B		FE5C
FE5C		FE5B
FE5D		FE5E
FE5E		FE5D
FE64		FE65
FE65		FE64
FF08		FF09
FF09		FF08
FF1C		FF1E
FF1E		FF1C
FF3B		FF3D
FF3D		FF3B
FF5B		FF5D
FF5D		FF5B
FF5F		FF60
FF60		FF5F
FF62		FF63
FF63		FF62
END
