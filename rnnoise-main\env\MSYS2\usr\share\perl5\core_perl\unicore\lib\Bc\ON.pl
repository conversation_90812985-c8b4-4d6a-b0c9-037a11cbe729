# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V378
33
35
38
43
59
65
91
97
123
127
161
162
166
170
171
173
174
176
180
181
182
185
187
192
215
216
247
248
697
699
706
720
722
736
741
750
751
768
884
886
894
895
900
902
903
904
1014
1015
1418
1419
1421
1423
1542
1544
1550
1552
1758
1759
1769
1770
2038
2042
3059
3065
3066
3067
3192
3199
3898
3902
5008
5018
5120
5121
5787
5789
6128
6138
6144
6155
6464
6465
6468
6470
6622
6656
8125
8126
8127
8130
8141
8144
8157
8160
8173
8176
8189
8191
8208
8232
8245
8260
8261
8287
8316
8319
8332
8335
8448
8450
8451
8455
8456
8458
8468
8469
8470
8473
8478
8484
8485
8486
8487
8488
8489
8490
8506
8508
8512
8517
8522
8526
8528
8544
8585
8588
8592
8722
8724
9014
9083
9109
9110
9255
9280
9291
9312
9352
9450
9900
9901
10240
10496
11124
11126
11158
11159
11264
11493
11499
11513
11520
11776
11870
11904
11930
11931
12020
12032
12246
12272
12284
12289
12293
12296
12321
12336
12337
12342
12344
12349
12352
12443
12445
12448
12449
12539
12540
12736
12772
12829
12831
12880
12896
12924
12927
12977
12992
13004
13008
13175
13179
13278
13280
13311
13312
19904
19968
42128
42183
42509
42512
42611
42612
42622
42624
42752
42786
42888
42889
43048
43052
43124
43128
43882
43884
64830
64848
64975
64976
65021
65024
65040
65050
65072
65104
65105
65106
65108
65109
65110
65119
65120
65122
65124
65127
65128
65129
65131
65132
65281
65283
65286
65291
65307
65313
65339
65345
65371
65382
65506
65509
65512
65519
65529
65534
65793
65794
65856
65933
65936
65949
65952
65953
67871
67872
68409
68416
69714
69734
71264
71277
73685
73693
73697
73714
94178
94179
119273
119275
119296
119362
119365
119366
119552
119639
120539
120540
120597
120598
120655
120656
120713
120714
120771
120772
126704
126706
126976
127020
127024
127124
127136
127151
127153
127168
127169
127184
127185
127222
127243
127248
127279
127280
127338
127344
127405
127406
127584
127590
127744
128728
128732
128749
128752
128765
128768
128887
128891
128986
128992
129004
129008
129009
129024
129036
129040
129096
129104
129114
129120
129160
129168
129198
129200
129202
129280
129620
129632
129646
129648
129661
129664
129673
129680
129726
129727
129734
129742
129756
129760
129769
129776
129785
129792
129939
129940
129995
END
