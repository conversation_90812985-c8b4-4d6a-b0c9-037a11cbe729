.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_error_to_alert" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_error_to_alert \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_error_to_alert(int " err ", int * " level ");"
.SH ARGUMENTS
.IP "int err" 12
is a negative integer
.IP "int * level" 12
the alert level will be stored there
.SH "DESCRIPTION"
Get an alert depending on the error code returned by a gnutls
function.  All alerts sent by this function should be considered
fatal.  The only exception is when  \fIerr\fP is \fBGNUTLS_E_REHANDSHAKE\fP,
where a warning alert should be sent to the peer indicating that no
renegotiation will be performed.

If there is no mapping to a valid alert the alert to indicate
internal error (\fBGNUTLS_A_INTERNAL_ERROR\fP) is returned.
.SH "RETURNS"
the alert code to use for a particular error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
