#!/bin/sh
# Prints the user's email address, with confirmation from the user.
#
# Copyright (C) 2001-2003, 2005, 2013 Free Software Foundation, Inc.
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

# Prerequisites for using ${exec_prefix}/lib and ${datarootdir}/locale.
prefix="/usr"
exec_prefix="${prefix}"
datarootdir="${prefix}/share"
datadir="${datarootdir}"
# Set variables libdir, localedir.
libdir="${exec_prefix}/lib"
localedir="${datarootdir}/locale"

# Support for relocatability.
if test "no" = yes; then
  orig_installdir="$libdir"/gettext # see Makefile.am's install rule
  # Determine curr_installdir without caring for symlinked callers.
  curr_installdir=`echo "$0" | sed -e 's,/[^/]*$,,'`
  curr_installdir=`cd "$curr_installdir" && pwd`
  # Compute the original/current installation prefixes by stripping the
  # trailing directories off the original/current installation directories.
  while true; do
    orig_last=`echo "$orig_installdir" | sed -n -e 's,^.*/\([^/]*\)$,\1,p'`
    curr_last=`echo "$curr_installdir" | sed -n -e 's,^.*/\([^/]*\)$,\1,p'`
    if test -z "$orig_last" || test -z "$curr_last"; then
      break
    fi
    if test "$orig_last" != "$curr_last"; then
      break
    fi
    orig_installdir=`echo "$orig_installdir" | sed -e 's,/[^/]*$,,'`
    curr_installdir=`echo "$curr_installdir" | sed -e 's,/[^/]*$,,'`
  done
  # Now relocate the directory variables that we use.
  libdir=`echo "$libdir/" | sed -e "s%^${orig_installdir}/%${curr_installdir}/%" | sed -e 's,/$,,'`
  localedir=`echo "$localedir/" | sed -e "s%^${orig_installdir}/%${curr_installdir}/%" | sed -e 's,/$,,'`
fi

# Internationalization.
. gettext.sh
TEXTDOMAIN=gettext-tools
export TEXTDOMAIN
TEXTDOMAINDIR="$localedir"
export TEXTDOMAINDIR

# Redirect fileno 3 to interactive I/O.
exec 3>/dev/tty

# Output a prompt.
if test $# != 0; then
  echo "$1" 1>&3
fi

# Find the user name on the local machine.
user=`id -u -n 2>/dev/null`
if test -z "$user"; then
  user="$USER"
  if test -z "$user"; then
    user="$LOGNAME"
    if test -z "$user"; then
      user=unknown
    fi
  fi
fi

# Find the hostname.
# hostname on some systems (SVR3.2, old Linux) returns a bogus exit status,
# so uname gets run too, so we keep only the first line of output.
#host=`(hostname || uname -n) 2>/dev/null | sed 1q`
host=`"$libdir"/gettext/hostname --short 2>/dev/null | sed 1q`

# Find the hostname.
hostfqdn=`"$libdir"/gettext/hostname --fqdn 2>/dev/null | sed 1q`

# Find a list of email addresses from various mailer configuration files.
# All mailers use configuration files under $HOME. We handle them in a
# last-modified - first-priority order.
cd $HOME
files=""

# ----------------------- BEGIN MAILER SPECIFIC CODE -----------------------

# Mozilla Thunderbird addresses
files="$files .thunderbird/*/prefs.js"

# Mozilla addresses
files="$files .mozilla/*/prefs.js"

# Netscape 4 addresses
files="$files .netscape/liprefs.js .netscape/preferences.js"

# Netscape 3 addresses
files="$files .netscape/preferences"

# Emacs/XEmacs rmail, Emacs/XEmacs gnus, XEmacs vm addresses
# XEmacs mew addresses
files="$files .emacs .emacs.el"

# KDE2 addresses
files="$files .kde2/share/config/emaildefaults"

# KDE kmail addresses
files="$files .kde2/share/config/kmailrc"

# GNOME evolution 2 addresses
files="$files .gconf/apps/evolution/mail/%gconf.xml"

# GNOME evolution 1 addresses
files="$files evolution/config.xmldb"

# GNOME balsa addresses
files="$files .gnome/balsa"

# StarOffice and OpenOffice addresses
sed_dos2unix='s/\r$//'
sed_soffice51='s,StarOffice 5\.1=\(.*\)$,\1/sofficerc,p'
sed_soffice52='s,StarOffice 5\.2=\(.*\)$,\1/user/sofficerc,p'
sed_ooffice='s,^OpenOffice[^=]*=\(.*\)$,\1/user/config/registry/instance/org/openoffice/UserProfile.xml,p'
files="$files Office51/sofficerc Office52/user/sofficerc "`sed -n -e "$sed_dos2unix" -e "$sed_soffice51" -e "$sed_soffice52" -e "$sed_ooffice" .sversionrc 2>/dev/null | sed -e 's,^file://*,/,'`

# mutt addresses
files="$files .muttrc"

# pine addresses
files="$files .pinerc"

# xfmail addresses
files="$files .xfmail/.xfmailrc"

# tkrat addresses
files="$files .ratatosk/ratatoskrc"

# ----------------------- END MAILER SPECIFIC CODE -----------------------

# Expand wildcards and remove nonexistent files from the list.
nfiles=""
for file in $files; do
  if test -r "$file" && test ! -d "$file"; then
    nfiles="$nfiles $file"
  fi
done
files="$nfiles"

addresses=""

if test -n "$files"; then

  for file in `ls -t $files`; do

    case "$file" in

# ----------------------- BEGIN MAILER SPECIFIC CODE -----------------------

      # Mozilla and Mozilla Thunderbird addresses
      .mozilla/*/prefs.js | .thunderbird/*/prefs.js)
        addresses="$addresses "`grep -h '^user_pref("mail\.identity\..*\.useremail", ".*");$' $file 2>/dev/null | sed -e 's/^user_pref("mail\.identity\..*\.useremail", "\(.*\)");$/\1/'`
        ;;

      # Netscape 4 addresses
      .netscape/liprefs.js | .netscape/preferences.js)
        addresses="$addresses "`grep -h '^user_pref("mail\.identity\.useremail", ".*");$' $file 2>/dev/null | sed -e 's/^user_pref("mail\.identity\.useremail", "\(.*\)");$/\1/'`
        ;;

      # Netscape 3 addresses
      .netscape/preferences)
        addresses="$addresses "`grep -h '^EMAIL_ADDRESS:' $file 2>/dev/null | sed -e 's/^EMAIL_ADDRESS:[ 	]*//'`
        ;;

      .emacs | .emacs.el)
        # Emacs/XEmacs rmail, Emacs/XEmacs gnus, XEmacs vm addresses
        addresses="$addresses "`grep -h '[ (]user-mail-address "[^"]*"' $file 2>/dev/null | sed -e 's/^.*[ (]user-mail-address "\([^"]*\)".*$/\1/'`
        # XEmacs mew addresses
        domains=`grep -h '[ (]mew-mail-domain "[^"]*"' $file 2>/dev/null | sed -e 's/^.*[ (]mew-mail-domain "\([^"]*\)".*$/\1/'`
        if test -n "$domains"; then
          for domain in $domains; do
            addresses="$addresses ${user}@$domain"
          done
        fi
        ;;

      # KDE2 addresses
      .kde2/share/config/emaildefaults)
        addresses="$addresses "`grep -h '^EmailAddress=' $file 2>/dev/null | sed -e 's/^EmailAddress=//'`
        ;;

      # KDE kmail addresses
      .kde2/share/config/kmailrc)
        addresses="$addresses "`grep -h '^Email Address=' $file 2>/dev/null | sed -e 's/^Email Address=//'`
        ;;

      # GNOME evolution 2 addresses
      .gconf/apps/evolution/mail/%gconf.xml)
        sedexpr0='s,^.*&lt;addr-spec&gt;\(.*\)&lt;/addr-spec&gt;.*$,\1,p'
        addresses="$addresses "`sed -n -e "$sedexpr0" < $file`
        ;;

      # GNOME evolution 1 addresses
      evolution/config.xmldb)
        sedexpr0='s/^.*<entry name="identity_address_[0-9]*" type="string" value="\([^"]*\)".*$/\1/p'
        sedexpr1='s/\(..\)/\\x\1/g'
        sedexpr2='s,$,\\n,'
        addresses="$addresses "`sed -n -e "$sedexpr0" < $file | while read hexstring; do printf \`echo "$hexstring" | sed -e "$sedexpr1" -e "$sedexpr2"\`; done`
        ;;

      # GNOME balsa addresses
      .gnome/balsa)
        addresses="$addresses "`grep -h '^Address=' $file 2>/dev/null | sed -e 's/^Address=//'`
        ;;

      # OpenOffice addresses
      */UserProfile.xml)
        addresses="$addresses "`sed -n -e 's,^.*<mail cfg:type="string">\(.*\)</mail>.*$,\1,p' $file 2>/dev/null`
        ;;

      # StarOffice addresses
      # Not a typo. They really write "Adress" with a single d.
      # German orthography...
      */sofficerc)
        addresses="$addresses "`grep -h '^User-Adress=' $file 2>/dev/null | sed -e 's/#[^#]*$//' -e 's/^.*#//'`
        ;;

      # mutt addresses
      .muttrc)
        mutt_addresses=`grep -h '^set from="[^"]*"[ 	]*$' $file 2>/dev/null | sed -e 's/^set from="\([^"]*\)"[ 	]*$/\1/'`
        if test -n "$mutt_addresses"; then
          addresses="$addresses $mutt_addresses"
        else
          # mutt uses $EMAIL as fallback.
          if test -n "$EMAIL"; then
            addresses="$addresses $EMAIL"
          fi
        fi
        ;;

      # pine addresses
      .pinerc)
        domains=`grep -h '^user-domain=' $file 2>/dev/null | sed -e 's/^user-domain=//'`
        if test -n "$domains"; then
          for domain in $domains; do
            addresses="$addresses ${user}@$domain"
          done
        else
          # The use-only-domain-name option is only used if the user-domain option is not present.
          domains=`grep -h '^use-only-domain-name=' $file 2>/dev/null | sed -e 's/^use-only-domain-name=//'`
          if test "Yes" = "$domains"; then
            addresses="$addresses ${user}@"`echo "$hostfqdn" | sed -e 's/^[^.]*\.//'`
          fi
        fi
        ;;

      # xfmail addresses
      .xfmail/.xfmailrc)
        addresses="$addresses "`grep -h '^from=.*<.*>' $file 2>/dev/null | sed -e 's/^.*<\([^<>]*\)>.*$/\1/'`
        ;;

      # tkrat addresses
      .ratatosk/ratatoskrc)
        domains=`grep -h '^set option(masquerade_as) ' $file 2>/dev/null | sed -e 's/^set option(masquerade_as) //'`
        if test -n "$domains"; then
          for domain in $domains; do
            addresses="$addresses ${user}@$domain"
          done
        else
          # The domain option is used only if the masquerade_as option is not present.
          domains=`grep -h '^set option(domain) ' $file 2>/dev/null | sed -e 's/^set option(domain) //'`
          if test -n "$domains"; then
            for domain in $domains; do
              addresses="$addresses ${user}@${host}.$domain"
            done
          fi
        fi
        ;;

# ----------------------- END MAILER SPECIFIC CODE -----------------------

    esac

  done

fi

# Some Debian systems have a file /etc/mailname.
if test -r /etc/mailname; then
  hostmailname=`cat /etc/mailname`
  if test -n "$hostmailname"; then
    addresses="$addresses ${user}@$hostmailname"
  fi
fi

# SuSE Linux >= 8.0 systems have a file /etc/sysconfig/mail.
if test -r /etc/sysconfig/mail; then
  hostmailname=`. /etc/sysconfig/mail && echo "$FROM_HEADER"`
  if test -n "$hostmailname"; then
    addresses="$addresses ${user}@$hostmailname"
  fi
fi

# elm has no user-defined addresses.
# mailx has no user-defined addresses.
# mh has no user-defined addresses.
# They use the system default.
addresses="$addresses ${user}@$hostfqdn"

# Normalize addresses: remove addresses without @, lowercase the part after @,
# and remove duplicates.
lowercase_sed='{
h
s/^[^@]*@\(.*\)$/\1/
y/ABCDEFGHIJKLMNOPQRSTUVWXYZ/abcdefghijklmnopqrstuvwxyz/
x
s/^\([^@]*\)@.*/\1@/
G
s/\n//
p
}'
naddresses=""
for addr in $addresses; do
  case "$addr" in
    "<"*">") addr=`echo "$addr" | sed -e 's/^<//' -e 's/>$//'` ;;
  esac
  case "$addr" in
    *@*)
      addr=`echo "$addr" | sed -n -e "$lowercase_sed"`
      case " $naddresses " in
        *" $addr "*) ;;
        *) naddresses="$naddresses $addr" ;;
      esac
      ;;
  esac
done
addresses="$naddresses"

# Now it's time to ask the user.
case "$addresses" in
  " "*" "*)
    # At least two addresses.
    lines=""
    i=0
    for addr in $addresses; do
      i=`expr $i + 1`
      lines="${lines}${i} ${addr}
"
    done
    while true; do
      { gettext "Which is your email address?"; echo; } 1>&3
      echo "$lines" 1>&3
      { gettext "Please choose the number, or enter your email address."; echo; } 1>&3
      read answer < /dev/tty
      case "$answer" in
        *@*) ;;
        [0-9]*)
          i=0
          for addr in $addresses; do
            i=`expr $i + 1`
            if test "$i" = "$answer"; then
              break 2
            fi
          done
          ;;
      esac
      case "$answer" in
        "<"*">") answer=`echo "$answer" | sed -e 's/^<//' -e 's/>$//'` ;;
      esac
      case "$answer" in
        *" "*) { gettext "Invalid email address: invalid character."; echo; echo; } 1>&3 ; continue ;;
        *@*.*) ;;
        *@*) { gettext "Invalid email address: need a fully qualified host name or domain name."; echo; echo; } 1>&3 ; continue ;;
        *) { gettext "Invalid email address: missing @"; echo; echo; } 1>&3 ; continue ;;
      esac
      addr=`echo "$answer" | sed -n -e "$lowercase_sed"`
      break
    done
    ;;
  " "*)
    # One address.
    while true; do
      { gettext "Is the following your email address?"; echo; } 1>&3
      echo " $addresses" 1>&3
      { gettext "Please confirm by pressing Return, or enter your email address."; echo; } 1>&3
      read answer < /dev/tty
      if test -z "$answer"; then
        addr=`echo "$addresses" | sed -e 's/^ //'`
        break
      fi
      case "$answer" in
        "<"*">") answer=`echo "$answer" | sed -e 's/^<//' -e 's/>$//'` ;;
      esac
      case "$answer" in
        *" "*) { gettext "Invalid email address: invalid character."; echo; echo; } 1>&3 ; continue ;;
        *@*.*) ;;
        *@*) { gettext "Invalid email address: need a fully qualified host name or domain name."; echo; echo; } 1>&3 ; continue ;;
        *) { gettext "Invalid email address: missing @"; echo; echo; } 1>&3 ; continue ;;
      esac
      addr=`echo "$answer" | sed -n -e "$lowercase_sed"`
      break
    done
    ;;
  "")
    # No address.
    { gettext "Couldn't find out about your email address."; echo; } 1>&3
    while true; do
      { gettext "Please enter your email address."; echo; } 1>&3
      read answer < /dev/tty
      case "$answer" in
        "<"*">") answer=`echo "$answer" | sed -e 's/^<//' -e 's/>$//'` ;;
      esac
      case "$answer" in
        *" "*) { gettext "Invalid email address: invalid character."; echo; echo; } 1>&3 ; continue ;;
        *@*.*) ;;
        *@*) { gettext "Invalid email address: need a fully qualified host name or domain name."; echo; echo; } 1>&3 ; continue ;;
        *) { gettext "Invalid email address: missing @"; echo; echo; } 1>&3 ; continue ;;
      esac
      addr=`echo "$answer" | sed -n -e "$lowercase_sed"`
      break
    done
    ;;
  *) echo "internal error" 1>&3 ; exit 1 ;;
esac

# Print to standard output.
echo "$addr"
