.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_prf_early" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_prf_early \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_prf_early(gnutls_session_t " session ", size_t " label_size ", const char * " label ", size_t " context_size ", const char * " context ", size_t " outsize ", char * " out ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "size_t label_size" 12
length of the  \fIlabel\fP variable.
.IP "const char * label" 12
label used in PRF computation, typically a short string.
.IP "size_t context_size" 12
length of the  \fIextra\fP variable.
.IP "const char * context" 12
optional extra data to seed the PRF with.
.IP "size_t outsize" 12
size of pre\-allocated output buffer to hold the output.
.IP "char * out" 12
pre\-allocated buffer to hold the generated data.
.SH "DESCRIPTION"
This function is similar to \fBgnutls_prf_rfc5705()\fP, but only works in
TLS 1.3 or later to export early keying material.

Note that the keying material is only available after the
ClientHello message is processed and before the application traffic
keys are established.  Therefore this function shall be called in a
handshake hook function for \fBGNUTLS_HANDSHAKE_CLIENT_HELLO\fP.

The  \fIlabel\fP variable usually contains a string denoting the purpose
for the generated data.

The  \fIcontext\fP variable can be used to add more data to the seed, after
the random variables.  It can be used to make sure the
generated output is strongly connected to some additional data
(e.g., a string used in user authentication).

The output is placed in  \fIout\fP , which must be pre\-allocated.

Note that, to provide the RFC5705 context, the  \fIcontext\fP variable
must be non\-null.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.
.SH "SINCE"
3.6.8
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
