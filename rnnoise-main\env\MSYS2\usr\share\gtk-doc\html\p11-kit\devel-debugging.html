<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Debugging Tips: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="devel.html" title="Building, Packaging, and Contributing to p11-kit">
<link rel="prev" href="devel-testing.html" title="Testing and Code Coverage">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="devel.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="devel-testing.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><img src="right-insensitive.png" width="16" height="16" border="0"></td>
</tr></table>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="devel-debugging"></a>Debugging Tips</h2></div></div></div>
<p>Unexpected conditions will produce critical warnings by p11-kit.
		These are often failed internal preconditions, and usually indicate a
		bug either in p11-kit or the software calling it.</p>
<p>You can use the environment variable <code class="literal">P11_KIT_STRICT=yes</code>
		to make p11-kit do an <code class="literal">abort()</code> (and core dump depending on
		your configuration) when a critical warning occurs.</p>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>