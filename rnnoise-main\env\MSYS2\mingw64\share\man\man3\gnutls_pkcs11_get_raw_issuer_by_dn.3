.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_get_raw_issuer_by_dn" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_get_raw_issuer_by_dn \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_get_raw_issuer_by_dn(const char * " url ", const gnutls_datum_t * " dn ", gnutls_datum_t * " issuer ", gnutls_x509_crt_fmt_t " fmt ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * url" 12
A PKCS 11 url identifying a token
.IP "const gnutls_datum_t * dn" 12
is the DN to search for
.IP "gnutls_datum_t * issuer" 12
Will hold the issuer if any in an allocated buffer.
.IP "gnutls_x509_crt_fmt_t fmt" 12
The format of the exported issuer.
.IP "unsigned int flags" 12
Use zero or flags from \fBGNUTLS_PKCS11_OBJ_FLAG\fP.
.SH "DESCRIPTION"
This function will return the certificate with the given DN, if it
is stored in the token. By default only marked as trusted issuers
are returned. If any issuer should be returned specify
\fBGNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_ANY\fP in  \fIflags\fP .

The name of the function includes issuer because it can
be used to discover issuers of certificates.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
