/*
 * Copyright 2004 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "unknwn.idl";
import "mmstream.idl";

cpp_quote("#ifndef __DDRAW_INCLUDED__")
typedef void * LPDDSURFACEDESC;
typedef struct tDDSURFACEDESC DDSURFACEDESC;
interface IDirectDraw;
interface IDirectDrawSurface;
interface IDirectDrawPalette;
cpp_quote("#endif")
cpp_quote("#include <ddraw.h>")

enum {
	DDSFF_PROGRESSIVERENDER = 0x00000001
};

interface IDirectDrawMediaStream;
interface IDirectDrawStreamSample;

[
object,
local,
uuid(F4104FCE-9A70-11d0-8FDE-00C04FD9189D),
pointer_default(unique)
]
interface IDirectDrawMediaStream : IMediaStream
{
	HRESULT GetFormat(
		[out] DDSURFACEDESC *pDDSDCurrent,
		[out] IDirectDrawPalette **ppDirectDrawPalette,
		[out] DDSURFACEDESC *pDDSDDesired,
		[out] DWORD *pdwFlags);

	HRESULT SetFormat(
		[in] const DDSURFACEDESC *pDDSurfaceDesc,
		[in] IDirectDrawPalette *pDirectDrawPalette);

	HRESULT GetDirectDraw(
		[out] IDirectDraw **ppDirectDraw);

	HRESULT SetDirectDraw(
		[in] IDirectDraw *pDirectDraw);

	HRESULT CreateSample(
		[in] IDirectDrawSurface *pSurface,
		[in] const RECT *pRect,
		[in] DWORD dwFlags,
		[out] IDirectDrawStreamSample **ppSample);

	HRESULT GetTimePerFrame(
		[out] STREAM_TIME *pFrameTime);
}


[
object,
local,
uuid(F4104FCF-9A70-11d0-8FDE-00C04FD9189D),
pointer_default(unique)
]
interface IDirectDrawStreamSample : IStreamSample
{
	HRESULT GetSurface(
		[out] IDirectDrawSurface ** ppDirectDrawSurface,
		[out] RECT * pRect);

	HRESULT SetRect(
		[in] const RECT * pRect);

}
