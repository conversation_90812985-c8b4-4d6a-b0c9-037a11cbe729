ctest_start
-----------

Starts the testing for a given model

.. code-block:: cmake

  ctest_start(<model> [<source> [<binary>]] [GROUP <group>] [QUIET])

  ctest_start([<model> [<source> [<binary>]]] [GROUP <group>] APPEND [QUIET])

Starts the testing for a given model.  The command should be called
after the binary directory is initialized.

The parameters are as follows:

``<model>``
  Set the dashboard model. Must be one of ``Experimental``, ``Continuous``, or
  ``Nightly``. This parameter is required unless ``APPEND`` is specified.

``<source>``
  Set the source directory. If not specified, the value of
  :variable:`CTEST_SOURCE_DIRECTORY` is used instead.

``<binary>``
  Set the binary directory. If not specified, the value of
  :variable:`CTEST_BINARY_DIRECTORY` is used instead.

``GROUP <group>``
  If ``GROUP`` is used, the submissions will go to the specified group on the
  CDash server. If no ``GROUP`` is specified, the name of the model is used by
  default.

  .. versionchanged:: 3.16
    This replaces the deprecated option ``TRACK``. Despite the name
    change its behavior is unchanged.

``APPEND``
  If ``APPEND`` is used, the existing ``TAG`` is used rather than creating a new
  one based on the current time stamp. If you use ``APPEND``, you can omit the
  ``<model>`` and ``GROUP <group>`` parameters, because they will be read from
  the generated ``TAG`` file. For example:

  .. code-block:: cmake

    ctest_start(Experimental GROUP GroupExperimental)

  Later, in another :option:`ctest -S` script:

  .. code-block:: cmake

    ctest_start(APPEND)

  When the second script runs ``ctest_start(APPEND)``, it will read the
  ``Experimental`` model and ``GroupExperimental`` group from the ``TAG`` file
  generated by the first ``ctest_start()`` command. Please note that if you
  call ``ctest_start(APPEND)`` and specify a different model or group than
  in the first ``ctest_start()`` command, a warning will be issued, and the
  new model and group will be used.

``QUIET``
  .. versionadded:: 3.3

  If ``QUIET`` is used, CTest will suppress any non-error messages that it
  otherwise would have printed to the console.

The parameters for ``ctest_start()`` can be issued in any order, with the
exception that ``<model>``, ``<source>``, and ``<binary>`` have to appear
in that order with respect to each other. The following are all valid and
equivalent:

.. code-block:: cmake

  ctest_start(Experimental path/to/source path/to/binary GROUP SomeGroup QUIET APPEND)

  ctest_start(GROUP SomeGroup Experimental QUIET path/to/source APPEND path/to/binary)

  ctest_start(APPEND QUIET Experimental path/to/source GROUP SomeGroup path/to/binary)

However, for the sake of readability, it is recommended that you order your
parameters in the order listed at the top of this page.

If the :variable:`CTEST_CHECKOUT_COMMAND` variable (or the
:variable:`CTEST_CVS_CHECKOUT` variable) is set, its content is treated as
command-line.  The command is invoked with the current working directory set
to the parent of the source directory, even if the source directory already
exists.  This can be used to create the source tree from a version control
repository.
