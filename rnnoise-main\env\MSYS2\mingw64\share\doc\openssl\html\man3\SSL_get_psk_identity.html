<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_get_psk_identity</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_get_psk_identity, SSL_get_psk_identity_hint - get PSK client identity and hint</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

const char *SSL_get_psk_identity_hint(const SSL *ssl);
const char *SSL_get_psk_identity(const SSL *ssl);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_get_psk_identity_hint() is used to retrieve the PSK identity hint used during the connection setup related to SSL object <b>ssl</b>. Similarly, SSL_get_psk_identity() is used to retrieve the PSK identity used during the connection setup.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>If non-<b>NULL</b>, SSL_get_psk_identity_hint() returns the PSK identity hint and SSL_get_psk_identity() returns the PSK identity. Both are <b>NULL</b>-terminated. SSL_get_psk_identity_hint() may return <b>NULL</b> if no PSK identity hint was used during the connection setup.</p>

<p>Note that the return value is valid only during the lifetime of the SSL object <b>ssl</b>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2006-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


