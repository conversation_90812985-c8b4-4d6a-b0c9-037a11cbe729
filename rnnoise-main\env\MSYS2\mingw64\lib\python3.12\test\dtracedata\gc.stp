global tracing

probe process.mark("function__entry")
{
    funcname = user_string($arg2);

    if (funcname == "start") {
        tracing = 1;
    }
}

probe process.mark("gc__start"), process.mark("gc__done")
{
    if (tracing) {
        printf("%d\t%s:%ld\n", gettimeofday_us(), $$name, $arg1);
    }
}

probe process.mark("function__return")
{
    funcname = user_string($arg2);

    if (funcname == "start") {
        tracing = 0;
    }
}
