.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_import2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_import2 \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_import2(gnutls_ocsp_resp_t " resp ", const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " fmt ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_t resp" 12
The data to store the parsed response.
.IP "const gnutls_datum_t * data" 12
DER or PEM encoded OCSP response.
.IP "gnutls_x509_crt_fmt_t fmt" 12
DER or PEM
.SH "DESCRIPTION"
This function will convert the given OCSP response to
the native \fBgnutls_ocsp_resp_t\fP format.  It also decodes the Basic
OCSP Response part, if any.  The output will be stored in  \fIresp\fP .
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
