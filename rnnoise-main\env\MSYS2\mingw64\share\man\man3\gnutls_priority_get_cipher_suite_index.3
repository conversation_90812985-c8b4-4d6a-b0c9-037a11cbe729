.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_priority_get_cipher_suite_index" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_priority_get_cipher_suite_index \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_priority_get_cipher_suite_index(gnutls_priority_t " pcache ", unsigned int " idx ", unsigned int * " sidx ");"
.SH ARGUMENTS
.IP "gnutls_priority_t pcache" 12
is a \fBgnutls_priority_t\fP type.
.IP "unsigned int idx" 12
is an index number.
.IP "unsigned int * sidx" 12
internal index of cipher suite to get information about.
.SH "DESCRIPTION"
Provides the internal ciphersuite index to be used with
\fBgnutls_cipher_suite_info()\fP. The index  \fIidx\fP provided is an
index kept at the priorities structure. It might be that a valid
priorities index does not correspond to a ciphersuite and in
that case \fBGNUTLS_E_UNKNOWN_CIPHER_SUITE\fP will be returned.
Once the last available index is crossed then
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be returned.
.SH "RETURNS"
On success it returns \fBGNUTLS_E_SUCCESS\fP (0), or a negative error value otherwise.
.SH "SINCE"
3.0.9
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
