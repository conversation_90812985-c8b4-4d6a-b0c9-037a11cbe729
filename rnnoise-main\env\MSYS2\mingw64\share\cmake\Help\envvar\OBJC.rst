OBJC
----

.. versionadded:: 3.16.7

.. include:: ENV_VAR.txt

Preferred executable for compiling ``OBJC`` language files. Will only be used
by <PERSON><PERSON><PERSON> on the first configuration to determine ``<PERSON>BJC`` compiler, after
which the value for ``<PERSON><PERSON><PERSON><PERSON>`` is stored in the cache as
:variable:`CMAKE_OBJC_COMPILER <CMAKE_<LANG>_COMPILER>`. For any configuration
run (including the first), the environment variable will be ignored if the
:variable:`CMAKE_OBJC_COMPILER <CMAKE_<LANG>_COMPILER>` variable is defined.

If ``OBJC`` is not defined, the :envvar:`CC` environment variable will
be checked instead.
