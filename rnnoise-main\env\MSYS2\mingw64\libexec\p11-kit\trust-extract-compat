#!/bin/sh

# This script is a placeholder designed to be replaced when this software
# has been customized for distribution. It should be symlinked linked to the
# distribution's update-ca-certificates or update-ca-trust command as
# appropriate. In the future this script will be called when the PKCS#11
# trust module is used to modify trust anchors and related data.

if [ $# -ne 0 ]; then
	echo "usage: trust extract-compat" >&2
	exit 2
fi

uid=$(id -u)
if [ "$uid" != 0 ]; then
        echo "trust: running as non-root user: skip extracting compat bundles" >&2
        exit 0
fi

echo "trust: the placeholder extract-compat command has not been customized by your distribution." >&2

# You can use commands like this to extract data from trust modules
# into appropriate locations for your distribution.
#
# trust extract --format=openssl-bundle --filter=ca-anchors \
# 	--overwrite /tmp/openssl-bundle.pem
# trust extract --format=pem-bundle --filter=ca-anchors --overwrite \
# 	--purpose server-auth /tmp/server-auth-bundle.pem
# trust extract --format=java-cacerts --filter=ca-anchors --overwrite \
# 	--purpose server-auth /tmp/cacerts

exit 1
