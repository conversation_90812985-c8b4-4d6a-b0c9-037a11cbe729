CMAKE_FRAMEWORK_PATH
--------------------

:ref:`Semicolon-separated list <CMake Language Lists>` of directories specifying a search path
for macOS frameworks used by the :command:`find_library`,
:command:`find_package`, :command:`find_path`, and :command:`find_file`
commands.

There is also an environment variable :envvar:`CMAKE_FRAMEWORK_PATH`, which is used
as an additional list of search directories.
