.de1 NOP
.  it 1 an-trap
.  if \\n[.$] \,\\$*\/
..
.ie t \
.ds B-Font [CB]
.ds I-Font [CI]
.ds R-Font [CR]
.el \
.ds B-Font B
.ds I-Font I
.ds R-Font R
.TH gnutls-cli 1 "08 Feb 2025" "3.8.9" "User Commands"
.SH NAME
\f\*[B-Font]gnutls-cli\fP
\- GnuTLS client
.SH SYNOPSIS
\f\*[B-Font]gnutls-cli\fP
.\" Mixture of short (flag) options and long options
[\f\*[B-Font]\-flags\f[]]
[\f\*[B-Font]\-flag\f[] [\f\*[I-Font]value\f[]]]
[\f\*[B-Font]\-\-option-name\f[][[=| ]\f\*[I-Font]value\f[]]]
[hostname]
.sp \n(Ppu
.ne 2

Operands and options may be intermixed.  They will be reordered.
.sp \n(Ppu
.ne 2
.SH "DESCRIPTION"
Simple client program to set up a TLS connection to some other computer. 
It sets up a TLS connection and forwards data from the standard input to the secured socket and vice versa.
.sp
.SH "OPTIONS"
.TP
.NOP \f\*[B-Font]\-d\f[] \f\*[I-Font]num\f[], \f\*[B-Font]\-\-debug\f[]=\f\*[I-Font]num\f[]
Enable debugging.
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 9999
.fi
.in -4
.sp
Specifies the debug level.
.TP
.NOP \f\*[B-Font]\-V\f[], \f\*[B-Font]\-\-verbose\f[]
More verbose output.
.sp
.TP
.NOP \f\*[B-Font]\-\-tofu\f[], \f\*[B-Font]\-\-no\-tofu\f[]
Enable trust on first use authentication.
The \fIno\-tofu\fP form will disable the option.
.sp
This option will, in addition to certificate authentication, perform authentication
based on previously seen public keys, a model similar to SSH authentication. Note that when tofu 
is specified (PKI) and DANE authentication will become advisory to assist the public key acceptance
process.
.TP
.NOP \f\*[B-Font]\-\-strict\-tofu\f[], \f\*[B-Font]\-\-no\-strict\-tofu\f[]
Fail to connect if a certificate is unknown or a known certificate has changed.
The \fIno\-strict\-tofu\fP form will disable the option.
.sp
This option will perform authentication as with option \-\-tofu; however, no questions shall be asked whatsoever, neither to accept an unknown certificate nor a changed one.
.TP
.NOP \f\*[B-Font]\-\-dane\f[], \f\*[B-Font]\-\-no\-dane\f[]
Enable DANE certificate verification (DNSSEC).
The \fIno\-dane\fP form will disable the option.
.sp
This option will, in addition to certificate authentication using 
the trusted CAs, verify the server certificates using on the DANE information
available via DNSSEC.
.TP
.NOP \f\*[B-Font]\-\-local\-dns\f[], \f\*[B-Font]\-\-no\-local\-dns\f[]
Use the local DNS server for DNSSEC resolving.
The \fIno\-local\-dns\fP form will disable the option.
.sp
This option will use the local DNS server for DNSSEC.
This is disabled by default due to many servers not allowing DNSSEC.
.TP
.NOP \f\*[B-Font]\-\-ca\-verification\f[], \f\*[B-Font]\-\-no\-ca\-verification\f[]
Enable CA certificate verification.
The \fIno\-ca\-verification\fP form will disable the option.
This option is enabled by default.
.sp
This option can be used to enable or disable CA certificate verification. It is to be used with the \-\-dane or \-\-tofu options.
.TP
.NOP \f\*[B-Font]\-\-ocsp\f[], \f\*[B-Font]\-\-no\-ocsp\f[]
Enable OCSP certificate verification.
The \fIno\-ocsp\fP form will disable the option.
.sp
This option will enable verification of the peer's certificate using ocsp
.TP
.NOP \f\*[B-Font]\-r\f[], \f\*[B-Font]\-\-resume\f[]
Establish a session and resume.
.sp
Connect, establish a session, reconnect and resume.
.TP
.NOP \f\*[B-Font]\-\-earlydata\f[]=\f\*[I-Font]str\f[]
Send early data on resumption from the specified file.
.sp
.TP
.NOP \f\*[B-Font]\-e\f[], \f\*[B-Font]\-\-rehandshake\f[]
Establish a session and rehandshake.
.sp
Connect, establish a session and rehandshake immediately.
.TP
.NOP \f\*[B-Font]\-\-sni\-hostname\f[]=\f\*[I-Font]str\f[]
Server's hostname for server name indication extension.
.sp
Set explicitly the server name used in the TLS server name indication extension. That is useful when testing with servers setup on different DNS name than the intended. If not specified, the provided hostname is used. Even with this option server certificate verification still uses the hostname passed on the main commandline. Use \-\-verify\-hostname to change this.
.TP
.NOP \f\*[B-Font]\-\-verify\-hostname\f[]=\f\*[I-Font]str\f[]
Server's hostname to use for validation.
.sp
Set explicitly the server name to be used when validating the server's certificate.
.TP
.NOP \f\*[B-Font]\-s\f[], \f\*[B-Font]\-\-starttls\f[]
Connect, establish a plain session and start TLS.
.sp
The TLS session will be initiated when EOF or a SIGALRM is received.
.TP
.NOP \f\*[B-Font]\-\-app\-proto\f[]
This is an alias for the \fI--starttls-proto\fR option.
.TP
.NOP \f\*[B-Font]\-\-starttls\-proto\f[]=\f\*[I-Font]str\f[]
The application protocol to be used to obtain the server's certificate (https, ftp, smtp, imap, ldap, xmpp, lmtp, pop3, nntp, sieve, postgres).
This option must not appear in combination with any of the following options:
starttls.
.sp
Specify the application layer protocol for STARTTLS. If the protocol is supported, gnutls\-cli will proceed to the TLS negotiation.
.TP
.NOP \f\*[B-Font]\-\-starttls\-name\f[]=\f\*[I-Font]str\f[]
The hostname presented to the application protocol for STARTTLS (for smtp, xmpp, lmtp).
This option must not appear in combination with any of the following options:
starttls.
This option must appear in combination with the following options:
starttls-proto.
.sp
Specify the hostname presented to the application protocol for STARTTLS.
.TP
.NOP \f\*[B-Font]\-u\f[], \f\*[B-Font]\-\-udp\f[]
Use DTLS (datagram TLS) over UDP.
.sp
.TP
.NOP \f\*[B-Font]\-\-mtu\f[]=\f\*[I-Font]num\f[]
Set MTU for datagram TLS.
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 17000
.fi
.in -4
.sp
.TP
.NOP \f\*[B-Font]\-\-crlf\f[]
Send CR LF instead of LF.
.sp
.TP
.NOP \f\*[B-Font]\-\-fastopen\f[]
Enable TCP Fast Open.
.sp
.TP
.NOP \f\*[B-Font]\-\-x509fmtder\f[]
Use DER format for certificates to read from.
.sp
.TP
.NOP \f\*[B-Font]\-\-print\-cert\f[]
Print peer's certificate in PEM format.
.sp
.TP
.NOP \f\*[B-Font]\-\-save\-cert\f[]=\f\*[I-Font]str\f[]
Save the peer's certificate chain in the specified file in PEM format.
.sp
.TP
.NOP \f\*[B-Font]\-\-save\-ocsp\f[]=\f\*[I-Font]str\f[]
Save the peer's OCSP status response in the provided file.
This option must not appear in combination with any of the following options:
save-ocsp-multi.
.sp
.TP
.NOP \f\*[B-Font]\-\-save\-ocsp\-multi\f[]=\f\*[I-Font]str\f[]
Save all OCSP responses provided by the peer in this file.
This option must not appear in combination with any of the following options:
save-ocsp.
.sp
The file will contain a list of PEM encoded OCSP status responses if any were provided by the peer, starting with the one for the peer's server certificate.
.TP
.NOP \f\*[B-Font]\-\-save\-server\-trace\f[]=\f\*[I-Font]str\f[]
Save the server-side TLS message trace in the provided file.
.sp
.TP
.NOP \f\*[B-Font]\-\-save\-client\-trace\f[]=\f\*[I-Font]str\f[]
Save the client-side TLS message trace in the provided file.
.sp
.TP
.NOP \f\*[B-Font]\-\-dh\-bits\f[]=\f\*[I-Font]num\f[]
The minimum number of bits allowed for DH.
This option takes an integer number as its argument.
.sp
This option sets the minimum number of bits allowed for a Diffie\-Hellman key exchange. You may want to lower the default value if the peer sends a weak prime and you get an connection error with unacceptable prime.
.TP
.NOP \f\*[B-Font]\-\-priority\f[]=\f\*[I-Font]str\f[]
Priorities string.
.sp
TLS algorithms and protocols to enable. You can
use predefined sets of ciphersuites such as PERFORMANCE,
NORMAL, PFS, SECURE128, SECURE256. The default is NORMAL.
.sp
Check  the  GnuTLS  manual  on  section  \(lqPriority strings\(rq for more
information on the allowed keywords
.TP
.NOP \f\*[B-Font]\-\-x509cafile\f[]=\f\*[I-Font]str\f[]
Certificate file or PKCS #11 URL to use.
.sp
.TP
.NOP \f\*[B-Font]\-\-x509crlfile\f[]=\f\*[I-Font]file\f[]
CRL file to use.
.sp
.TP
.NOP \f\*[B-Font]\-\-x509keyfile\f[]=\f\*[I-Font]str\f[]
X.509 key file or PKCS #11 URL to use.
.sp
.TP
.NOP \f\*[B-Font]\-\-x509certfile\f[]=\f\*[I-Font]str\f[]
X.509 Certificate file or PKCS #11 URL to use.
This option must appear in combination with the following options:
x509keyfile.
.sp
.TP
.NOP \f\*[B-Font]\-\-rawpkkeyfile\f[]=\f\*[I-Font]str\f[]
Private key file (PKCS #8 or PKCS #12) or PKCS #11 URL to use.
.sp
In order to instruct the application to negotiate raw public keys one
must enable the respective certificate types via the priority strings (i.e. CTYPE\-CLI\-*
and CTYPE\-SRV\-* flags).
.sp
Check  the  GnuTLS  manual  on  section  \(lqPriority strings\(rq for more
information on how to set certificate types.
.TP
.NOP \f\*[B-Font]\-\-rawpkfile\f[]=\f\*[I-Font]str\f[]
Raw public-key file to use.
This option must appear in combination with the following options:
rawpkkeyfile.
.sp
In order to instruct the application to negotiate raw public keys one
must enable the respective certificate types via the priority strings (i.e. CTYPE\-CLI\-*
and CTYPE\-SRV\-* flags).
.sp
Check  the  GnuTLS  manual  on  section  \(lqPriority strings\(rq for more
information on how to set certificate types.
.TP
.NOP \f\*[B-Font]\-\-srpusername\f[]=\f\*[I-Font]str\f[]
SRP username to use.
.sp
.TP
.NOP \f\*[B-Font]\-\-srppasswd\f[]=\f\*[I-Font]str\f[]
SRP password to use.
.sp
.TP
.NOP \f\*[B-Font]\-\-pskusername\f[]=\f\*[I-Font]str\f[]
PSK username to use.
.sp
.TP
.NOP \f\*[B-Font]\-\-pskkey\f[]=\f\*[I-Font]str\f[]
PSK key (in hex) to use.
.sp
.TP
.NOP \f\*[B-Font]\-p\f[] \f\*[I-Font]str\f[], \f\*[B-Font]\-\-port\f[]=\f\*[I-Font]str\f[]
The port or service to connect to.
.sp
.TP
.NOP \f\*[B-Font]\-\-insecure\f[]
Don't abort program if server certificate can't be validated.
.sp
.TP
.NOP \f\*[B-Font]\-\-verify\-allow\-broken\f[]
Allow broken algorithms, such as MD5 for certificate verification.
.sp
.TP
.NOP \f\*[B-Font]\-\-ranges\f[]
Use length-hiding padding to prevent traffic analysis.
.sp
When possible (e.g., when using CBC ciphersuites), use length\-hiding padding to prevent traffic analysis.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-benchmark\-ciphers\f[]
Benchmark individual ciphers.
.sp
By default the benchmarked ciphers will utilize any capabilities of the local CPU to improve performance. To test against the raw software implementation set the environment variable GNUTLS_CPUID_OVERRIDE to 0x1.
.TP
.NOP \f\*[B-Font]\-\-benchmark\-tls\-kx\f[]
Benchmark TLS key exchange methods.
.sp
.TP
.NOP \f\*[B-Font]\-\-benchmark\-tls\-ciphers\f[]
Benchmark TLS ciphers.
.sp
By default the benchmarked ciphers will utilize any capabilities of the local CPU to improve performance. To test against the raw software implementation set the environment variable GNUTLS_CPUID_OVERRIDE to 0x1.
.TP
.NOP \f\*[B-Font]\-l\f[], \f\*[B-Font]\-\-list\f[]
Print a list of the supported algorithms and modes.
This option must not appear in combination with any of the following options:
port.
.sp
Print a list of the supported algorithms and modes. If a priority string is given then only the enabled ciphersuites are shown.
.TP
.NOP \f\*[B-Font]\-\-priority\-list\f[]
Print a list of the supported priority strings.
.sp
Print a list of the supported priority strings. The ciphersuites corresponding to each priority string can be examined using \-l \-p.
.TP
.NOP \f\*[B-Font]\-\-noticket\f[]
Don't allow session tickets.
.sp
Disable the request of receiving of session tickets under TLS1.2 or earlier
.TP
.NOP \f\*[B-Font]\-\-srtp\-profiles\f[]=\f\*[I-Font]str\f[]
Offer SRTP profiles.
.sp
.TP
.NOP \f\*[B-Font]\-\-alpn\f[]=\f\*[I-Font]str\f[]
Application layer protocol.
This option may appear an unlimited number of times.
.sp
This option will set and enable the Application Layer Protocol Negotiation  (ALPN) in the TLS protocol.
.TP
.NOP \f\*[B-Font]\-\-compress\-cert\f[]=\f\*[I-Font]str\f[]
Compress certificate.
This option may appear an unlimited number of times.
.sp
This option sets a supported compression method for certificate compression.
.TP
.NOP \f\*[B-Font]\-b\f[], \f\*[B-Font]\-\-heartbeat\f[]
Activate heartbeat support.
.sp
.TP
.NOP \f\*[B-Font]\-\-recordsize\f[]=\f\*[I-Font]num\f[]
The maximum record size to advertise.
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 4096
.fi
.in -4
.sp
.TP
.NOP \f\*[B-Font]\-\-disable\-sni\f[]
Do not send a Server Name Indication (SNI).
.sp
.TP
.NOP \f\*[B-Font]\-\-disable\-extensions\f[]
Disable all the TLS extensions.
.sp
This option disables all TLS extensions. Deprecated option. Use the priority string.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-single\-key\-share\f[]
Send a single key share under TLS1.3.
.sp
This option switches the default mode of sending multiple
key shares, to send a single one (the top one).
.TP
.NOP \f\*[B-Font]\-\-post\-handshake\-auth\f[]
Enable post-handshake authentication under TLS1.3.
.sp
This option enables post\-handshake authentication when under TLS1.3.
.TP
.NOP \f\*[B-Font]\-\-inline\-commands\f[]
Inline commands of the form ^<cmd>^.
.sp
Enable inline commands of the form ^<cmd>^. The inline commands are expected to be in a line by themselves. The available commands are: resume, rekey1 (local rekey), rekey (rekey on both peers) and renegotiate.
.TP
.NOP \f\*[B-Font]\-\-inline\-commands\-prefix\f[]=\f\*[I-Font]str\f[]
Change the default delimiter for inline commands.
.sp
Change the default delimiter (^) used for inline commands. The delimiter is expected to be a single US\-ASCII character (octets 0 \- 127). This option is only relevant if inline commands are enabled via the inline\-commands option
.TP
.NOP \f\*[B-Font]\-\-provider\f[]=\f\*[I-Font]file\f[]
Specify the PKCS #11 provider library.
.sp
This will override the default options in /etc/gnutls/pkcs11.conf
.TP
.NOP \f\*[B-Font]\-\-fips140\-mode\f[]
Reports the status of the FIPS140-2 mode in gnutls library.
.sp
.TP
.NOP \f\*[B-Font]\-\-list\-config\f[]
Reports the configuration of the library.
.sp
.TP
.NOP \f\*[B-Font]\-\-logfile\f[]=\f\*[I-Font]str\f[]
Redirect informational messages to a specific file.
.sp
Redirect informational messages to a specific file. The file may be /dev/null also to make the gnutls client quiet to use it in piped server connections where only the server communication may appear on stdout.
.TP
.NOP \f\*[B-Font]\-\-keymatexport\f[]=\f\*[I-Font]str\f[]
Label used for exporting keying material.
.sp
.TP
.NOP \f\*[B-Font]\-\-keymatexportsize\f[]=\f\*[I-Font]num\f[]
Size of the exported keying material.
This option takes an integer number as its argument.
.sp
.TP
.NOP \f\*[B-Font]\-\-waitresumption\f[]
Block waiting for the resumption data under TLS1.3.
.sp
This option makes the client to block waiting for the resumption data under TLS1.3. The option has effect only when \-\-resume is provided.
.TP
.NOP \f\*[B-Font]\-\-ca\-auto\-retrieve\f[], \f\*[B-Font]\-\-no\-ca\-auto\-retrieve\f[]
Enable automatic retrieval of missing CA certificates.
The \fIno\-ca\-auto\-retrieve\fP form will disable the option.
.sp
This option enables the client to automatically retrieve the missing intermediate CA certificates in the certificate chain, based on the Authority Information Access (AIA) extension.
.TP
.NOP \f\*[B-Font]\-\-attime\f[]=\f\*[I-Font]timestamp\f[]
Perform validation at the timestamp instead of the system time.
.sp
timestamp is an instance in time encoded as Unix time or in a human
 readable timestring such as "29 Feb 2004", "2004\-02\-29".
Full documentation available at 
<https://www.gnu.org/software/coreutils/manual/html_node/Date\-input\-formats.html>
or locally via info '(coreutils) date invocation'.
.TP
.NOP \f\*[B-Font]\-v\f[] \f\*[I-Font]arg\f[], \f\*[B-Font]\-\-version\f[]=\f\*[I-Font]arg\f[]
Output version of program and exit.  The default mode is `v', a simple
version.  The `c' mode will print copyright information and `n' will
print the full copyright notice.
.TP
.NOP \f\*[B-Font]\-h\f[], \f\*[B-Font]\-\-help\f[]
Display usage information and exit.
.TP
.NOP \f\*[B-Font]\-!\f[], \f\*[B-Font]\-\-more\-help\f[]
Pass the extended usage information through a pager.

.sp
.SH EXAMPLES
.br
\fBConnecting using PSK authentication\fP
.br
To connect to a server using PSK authentication, you need to enable the choice of PSK by using a cipher priority parameter such as in the example below. 
.br
.in +4
.nf
$ ./gnutls\-cli \-p 5556 localhost \-\-pskusername psk_identity \
    \-\-pskkey 88f3824b3e5659f52d00e959bacab954b6540344 \
    \-\-priority NORMAL:\-KX\-ALL:+ECDHE\-PSK:+DHE\-PSK:+PSK
Resolving 'localhost'...
Connecting to '127.0.0.1:5556'...
\- PSK authentication.
\- Version: TLS1.1
\- Key Exchange: PSK
\- Cipher: AES\-128\-CBC
\- MAC: SHA1
\- Compression: NULL
\- Handshake was completed
    
\- Simple Client Mode:
.in -4
.fi
By keeping the \-\-pskusername parameter and removing the \-\-pskkey parameter, it will query only for the password during the handshake.
.sp
.br
\fBConnecting using raw public\-key authentication\fP
.br
To connect to a server using raw public\-key authentication, you need to enable the option to negotiate raw public\-keys via the priority strings such as in the example below. 
.br
.in +4
.nf
$ ./gnutls\-cli \-p 5556 localhost \-\-priority NORMAL:\-CTYPE\-CLI\-ALL:+CTYPE\-CLI\-RAWPK \
    \-\-rawpkkeyfile cli.key.pem \
    \-\-rawpkfile cli.rawpk.pem
Processed 1 client raw public key pair...
Resolving 'localhost'...
Connecting to '127.0.0.1:5556'...
\- Successfully sent 1 certificate(s) to server.
\- Server has requested a certificate.
\- Certificate type: X.509
\- Got a certificate list of 1 certificates.
\- Certificate[0] info:
 \- skipped
\- Description: (TLS1.3\-Raw Public Key\-X.509)\-(ECDHE\-SECP256R1)\-(RSA\-PSS\-RSAE\-SHA256)\-(AES\-256\-GCM)
\- Options:
\- Handshake was completed
    
\- Simple Client Mode:
.in -4
.fi
.sp
.br
\fBConnecting to STARTTLS services\fP
.br
.sp
You could also use the client to connect to services with starttls capability.
.br
.in +4
.nf
$ gnutls\-cli \-\-starttls\-proto smtp \-\-port 25 localhost
.in -4
.fi
.sp
.br
\fBListing ciphersuites in a priority string\fP
.br
To list the ciphersuites in a priority string:
.br
.in +4
.nf
$ ./gnutls\-cli \-\-priority SECURE192 \-l
Cipher suites for SECURE192
TLS_ECDHE_ECDSA_AES_256_CBC_SHA384         0xc0, 0x24	TLS1.2
TLS_ECDHE_ECDSA_AES_256_GCM_SHA384         0xc0, 0x2e	TLS1.2
TLS_ECDHE_RSA_AES_256_GCM_SHA384           0xc0, 0x30	TLS1.2
TLS_DHE_RSA_AES_256_CBC_SHA256             0x00, 0x6b	TLS1.2
TLS_DHE_DSS_AES_256_CBC_SHA256             0x00, 0x6a	TLS1.2
TLS_RSA_AES_256_CBC_SHA256                 0x00, 0x3d	TLS1.2
.sp
Certificate types: CTYPE\-X.509
Protocols: VERS\-TLS1.2, VERS\-TLS1.1, VERS\-TLS1.0, VERS\-SSL3.0, VERS\-DTLS1.0
Compression: COMP\-NULL
Elliptic curves: CURVE\-SECP384R1, CURVE\-SECP521R1
PK\-signatures: SIGN\-RSA\-SHA384, SIGN\-ECDSA\-SHA384, SIGN\-RSA\-SHA512, SIGN\-ECDSA\-SHA512
.in -4
.fi
.sp
.br
\fBConnecting using a PKCS #11 token\fP
.br
To connect to a server using a certificate and a private key present in a PKCS #11 token you 
need to substitute the PKCS 11 URLs in the x509certfile and x509keyfile parameters.
.sp
Those can be found using "p11tool \-\-list\-tokens" and then listing all the objects in the
needed token, and using the appropriate.
.br
.in +4
.nf
$ p11tool \-\-list\-tokens
.sp
Token 0:
	URL: pkcs11:model=PKCS15;manufacturer=MyMan;serial=1234;token=Test
	Label: Test
	Manufacturer: EnterSafe
	Model: PKCS15
	Serial: 1234
.sp
$ p11tool \-\-login \-\-list\-certs "pkcs11:model=PKCS15;manufacturer=MyMan;serial=1234;token=Test"
.sp
Object 0:
	URL: pkcs11:model=PKCS15;manufacturer=MyMan;serial=1234;token=Test;object=client;type=cert
	Type: X.509 Certificate
	Label: client
	ID: 2a:97:0d:58:d1:51:3c:23:07:ae:4e:0d:72:26:03:7d:99:06:02:6a
.sp
$ MYCERT="pkcs11:model=PKCS15;manufacturer=MyMan;serial=1234;token=Test;object=client;type=cert"
$ MYKEY="pkcs11:model=PKCS15;manufacturer=MyMan;serial=1234;token=Test;object=client;type=private"
$ export MYCERT MYKEY
.sp
$ gnutls\-cli www.example.com \-\-x509keyfile $MYKEY \-\-x509certfile $MYCERT
.in -4
.fi
Notice that the private key only differs from the certificate in the type.
.SH "EXIT STATUS"
One of the following exit values will be returned:
.TP
.NOP 0 " (EXIT_SUCCESS)"
Successful program execution.
.TP
.NOP 1 " (EXIT_FAILURE)"
The operation failed or the command syntax was not valid.
.PP
.SH "SEE ALSO"
gnutls\-cli\-debug(1), gnutls\-serv(1)
.SH "AUTHORS"

.SH "COPYRIGHT"
Copyright (C) 2020-2023 Free Software Foundation, and others all rights reserved.
This program is released under the terms of
the GNU General Public License, version 3 or later
.
.SH "BUGS"
Please send bug reports to: <EMAIL>
