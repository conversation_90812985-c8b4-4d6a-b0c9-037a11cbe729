## Syntax highlighting for Bourne shell scripts.

## Original author:  <PERSON>
## License:  GPL version 3 or newer

syntax sh "(\.sh|(^|/|\.)(a|ba|c|da|k|mk|pdk|tc|z)sh(rc|_profile)?|(/etc/|(^|/)\.)profile)$"
header "^#!.*/(((env|busybox)[[:blank:]]+)?(a|ba|c|da|k|mk|pdk|tc|z)?sh|openrc-run|runscript)\>"
header "-\*-.*shell-script.*-\*-"
magic "(POSIX|Bourne-Again) shell script.*text"
comment "#"

linter dash -n

# Function declarations.
color brightgreen "^[[:alnum:]_-]+\(\)"

# Keywords, symbols, and comparisons.
color green "\<(break|case|continue|do|done|elif|else|esac|exit|fi|for|function|if|in|read|return|select|shift|then|time|until|while)\>"
color green "\<(declare|eval|exec|export|let|local)\>"
color green "[][{}():;|`$<>!=&\]"
color green "-(eq|ne|gt|lt|ge|le|ef|ot|nt)\>"

# Short and long options.
color brightmagenta "[[:blank:]](-[[:alpha:]]|--\<[[:alpha:]-]+)\>"

# Common commands.
color brightblue "\<(awk|cat|cd|ch(grp|mod|own)|cp|cut|echo|env|grep|head|install|ln|make|mkdir|mv|popd|printf|pushd|rm|rmdir|sed|set|sort|tail|tar|touch|umask|unset)\>"
color normal "[.-]tar\>"

# Basic variable names (no braces).
color brightred "\$([-@*#?$![:digit:]]|[[:alpha:]_][[:alnum:]_]*)"
# More complicated variable names; handles braces and replacements and arrays.
color brightred "\$\{[#!]?([-@*#?$!]|[[:digit:]]+|[[:alpha:]_][[:alnum:]_]*)(\[([[:blank:]]*[[:alnum:]_]+[[:blank:]]*|@)\])?(([#%/]|:?[-=?+])[^}]*\}|\[|\})"

# Comments.
color cyan "(^|[[:blank:]])#.*"

# Strings.
color brightyellow ""([^"\]|\\.)*"|'([^'\]|\\.)*'"

# Trailing whitespace.
color ,green "[[:space:]]+$"
