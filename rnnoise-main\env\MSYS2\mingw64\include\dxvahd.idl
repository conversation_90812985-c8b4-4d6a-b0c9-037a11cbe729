/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("")
cpp_quote("#if (_WIN32_WINNT >= 0x0601)")

import "unknwn.idl";

cpp_quote("#if 0")
interface IDirect3DSurface9;
interface IDirect3DDevice9Ex;
typedef DWORD D3DFORMAT;
typedef DWORD D3DPOOL;
typedef struct { char dummy; } D3DCOLOR;
cpp_quote("#endif")

cpp_quote("#ifndef CALLBACK")
cpp_quote("#if defined(_ARM_)")
cpp_quote("#define CALLBACK")
cpp_quote("#else")
cpp_quote("#define CALLBACK __stdcall")
cpp_quote("#endif")
cpp_quote("#endif")

interface IDXVAHD_Device;
interface IDXVAHD_VideoProcessor;

typedef enum _DXVAHD_ALPHA_FILL_MODE {
    DXVAHD_ALPHA_FILL_MODE_OPAQUE          = 0,
    DXVAHD_ALPHA_FILL_MODE_BACKGROUND      = 1,
    DXVAHD_ALPHA_FILL_MODE_DESTINATION     = 2,
    DXVAHD_ALPHA_FILL_MODE_SOURCE_STREAM   = 3 
} DXVAHD_ALPHA_FILL_MODE;

typedef struct _DXVAHD_COLOR_YCbCrA {
    FLOAT Y;
    FLOAT Cb;
    FLOAT Cr;
    FLOAT A;
} DXVAHD_COLOR_YCbCrA;

typedef struct _DXVAHD_COLOR_RGBA {
    FLOAT R;
    FLOAT G;
    FLOAT B;
    FLOAT A;
} DXVAHD_COLOR_RGBA;

typedef union _DXVAHD_COLOR {
    DXVAHD_COLOR_RGBA   RGB;
    DXVAHD_COLOR_YCbCrA YCbCr;
} DXVAHD_COLOR;

typedef struct _DXVAHD_BLT_STATE_BACKGROUND_COLOR_DATA {
    WINBOOL         YCbCr;
    DXVAHD_COLOR BackgroundColor;
} DXVAHD_BLT_STATE_BACKGROUND_COLOR_DATA;

typedef enum _DXVAHD_BLT_STATE {
  DXVAHD_BLT_STATE_TARGET_RECT          = 0,
  DXVAHD_BLT_STATE_BACKGROUND_COLOR     = 1,
  DXVAHD_BLT_STATE_OUTPUT_COLOR_SPACE   = 2,
  DXVAHD_BLT_STATE_ALPHA_FILL           = 3,
  DXVAHD_BLT_STATE_CONSTRICTION         = 4,
  DXVAHD_BLT_STATE_PRIVATE              = 1000 
} DXVAHD_BLT_STATE;

typedef enum _DXVAHD_DEVICE_CAPS {
  DXVAHD_DEVICE_CAPS_LINEAR_SPACE              = 0x1,
  DXVAHD_DEVICE_CAPS_xvYCC                     = 0x2,
  DXVAHD_DEVICE_CAPS_RGB_RANGE_CONVERSION      = 0x4,
  DXVAHD_DEVICE_CAPS_YCbCr_MATRIX_CONVERSION   = 0x8 
} DXVAHD_DEVICE_CAPS;

typedef enum _DXVAHD_DEVICE_TYPE {
  DXVAHD_DEVICE_TYPE_HARDWARE    = 0,
  DXVAHD_DEVICE_TYPE_SOFTWARE    = 1,
  DXVAHD_DEVICE_TYPE_REFERENCE   = 2,
  DXVAHD_DEVICE_TYPE_OTHER       = 3 
} DXVAHD_DEVICE_TYPE;

typedef enum _DXVAHD_DEVICE_USAGE {
  DXVAHD_DEVICE_USAGE_PLAYBACK_NORMAL   = 0,
  DXVAHD_DEVICE_USAGE_OPTIMAL_SPEED     = 1,
  DXVAHD_DEVICE_USAGE_OPTIMAL_QUALITY   = 2 
} DXVAHD_DEVICE_USAGE;


typedef enum _DXVAHD_FEATURE_CAPS {
  DXVAHD_FEATURE_CAPS_ALPHA_FILL      = 0x1,
  DXVAHD_FEATURE_CAPS_CONSTRICTION    = 0x2,
  DXVAHD_FEATURE_CAPS_LUMA_KEY        = 0x4,
  DXVAHD_FEATURE_CAPS_ALPHA_PALETTE   = 0x8 
} DXVAHD_FEATURE_CAPS;

typedef enum _DXVAHD_FILTER {
  DXVAHD_FILTER_BRIGHTNESS           = 0,
  DXVAHD_FILTER_CONTRAST             = 1,
  DXVAHD_FILTER_HUE                  = 2,
  DXVAHD_FILTER_SATURATION           = 3,
  DXVAHD_FILTER_NOISE_REDUCTION      = 4,
  DXVAHD_FILTER_EDGE_ENHANCEMENT     = 5,
  DXVAHD_FILTER_ANAMORPHIC_SCALING   = 6 
} DXVAHD_FILTER;

typedef enum _DXVAHD_FILTER_CAPS {
  DXVAHD_FILTER_CAPS_BRIGHTNESS           = 0x1,
  DXVAHD_FILTER_CAPS_CONTRAST             = 0x2,
  DXVAHD_FILTER_CAPS_HUE                  = 0x4,
  DXVAHD_FILTER_CAPS_SATURATION           = 0x8,
  DXVAHD_FILTER_CAPS_NOISE_REDUCTION      = 0x10,
  DXVAHD_FILTER_CAPS_EDGE_ENHANCEMENT     = 0x20,
  DXVAHD_FILTER_CAPS_ANAMORPHIC_SCALING   = 0x40 
} DXVAHD_FILTER_CAPS;

typedef enum _DXVAHD_FRAME_FORMAT {
  DXVAHD_FRAME_FORMAT_PROGRESSIVE                     = 0,
  DXVAHD_FRAME_FORMAT_INTERLACED_TOP_FIELD_FIRST      = 1,
  DXVAHD_FRAME_FORMAT_INTERLACED_BOTTOM_FIELD_FIRST   = 2 
} DXVAHD_FRAME_FORMAT;

typedef enum _DXVAHD_INPUT_FORMAT_CAPS {
  DXVAHD_INPUT_FORMAT_CAPS_RGB_INTERLACED       = 0x1,
  DXVAHD_INPUT_FORMAT_CAPS_RGB_PROCAMP          = 0x2,
  DXVAHD_INPUT_FORMAT_CAPS_RGB_LUMA_KEY         = 0x4,
  DXVAHD_INPUT_FORMAT_CAPS_PALETTE_INTERLACED   = 0x8 
} DXVAHD_INPUT_FORMAT_CAPS;

typedef enum _DXVAHD_ITELECINE_CAPS {
  DXVAHD_ITELECINE_CAPS_32             = 0x1,
  DXVAHD_ITELECINE_CAPS_22             = 0x2,
  DXVAHD_ITELECINE_CAPS_2224           = 0x4,
  DXVAHD_ITELECINE_CAPS_2332           = 0x8,
  DXVAHD_ITELECINE_CAPS_32322          = 0x10,
  DXVAHD_ITELECINE_CAPS_55             = 0x20,
  DXVAHD_ITELECINE_CAPS_64             = 0x40,
  DXVAHD_ITELECINE_CAPS_87             = 0x80,
  DXVAHD_ITELECINE_CAPS_222222222223   = 0x100,
  DXVAHD_ITELECINE_CAPS_OTHER          = 0x80000000 
} DXVAHD_ITELECINE_CAPS;

typedef enum _DXVAHD_OUTPUT_RATE {
  DXVAHD_OUTPUT_RATE_NORMAL   = 0,
  DXVAHD_OUTPUT_RATE_HALF     = 1,
  DXVAHD_OUTPUT_RATE_CUSTOM   = 2 
} DXVAHD_OUTPUT_RATE;

typedef enum _DXVAHD_PROCESSOR_CAPS {
  DXVAHD_PROCESSOR_CAPS_DEINTERLACE_BLEND                 = 0x1,
  DXVAHD_PROCESSOR_CAPS_DEINTERLACE_BOB                   = 0x2,
  DXVAHD_PROCESSOR_CAPS_DEINTERLACE_ADAPTIVE              = 0x4,
  DXVAHD_PROCESSOR_CAPS_DEINTERLACE_MOTION_COMPENSATION   = 0x8,
  DXVAHD_PROCESSOR_CAPS_INVERSE_TELECINE                  = 0x10,
  DXVAHD_PROCESSOR_CAPS_FRAME_RATE_CONVERSION             = 0x20 
} DXVAHD_PROCESSOR_CAPS;

typedef enum _DXVAHD_STREAM_STATE {
  DXVAHD_STREAM_STATE_D3DFORMAT                   = 0,
  DXVAHD_STREAM_STATE_FRAME_FORMAT                = 1,
  DXVAHD_STREAM_STATE_INPUT_COLOR_SPACE           = 2,
  DXVAHD_STREAM_STATE_OUTPUT_RATE                 = 3,
  DXVAHD_STREAM_STATE_SOURCE_RECT                 = 4,
  DXVAHD_STREAM_STATE_DESTINATION_RECT            = 5,
  DXVAHD_STREAM_STATE_ALPHA                       = 6,
  DXVAHD_STREAM_STATE_PALETTE                     = 7,
  DXVAHD_STREAM_STATE_LUMA_KEY                    = 8,
  DXVAHD_STREAM_STATE_ASPECT_RATIO                = 9,
  DXVAHD_STREAM_STATE_FILTER_BRIGHTNESS           = 100,
  DXVAHD_STREAM_STATE_FILTER_CONTRAST             = 101,
  DXVAHD_STREAM_STATE_FILTER_HUE                  = 102,
  DXVAHD_STREAM_STATE_FILTER_SATURATION           = 103,
  DXVAHD_STREAM_STATE_FILTER_NOISE_REDUCTION      = 104,
  DXVAHD_STREAM_STATE_FILTER_EDGE_ENHANCEMENT     = 105,
  DXVAHD_STREAM_STATE_FILTER_ANAMORPHIC_SCALING   = 106,
  DXVAHD_STREAM_STATE_PRIVATE                     = 1000 
} DXVAHD_STREAM_STATE;

typedef enum _DXVAHD_SURFACE_TYPE {
  DXVAHD_SURFACE_TYPE_VIDEO_INPUT           = 0,
  DXVAHD_SURFACE_TYPE_VIDEO_INPUT_PRIVATE   = 1,
  DXVAHD_SURFACE_TYPE_VIDEO_OUTPUT          = 2 
} DXVAHD_SURFACE_TYPE;

typedef struct _DXVAHD_VPDEVCAPS {
  DXVAHD_DEVICE_TYPE DeviceType;
  UINT               DeviceCaps;
  UINT               FeatureCaps;
  UINT               FilterCaps;
  UINT               InputFormatCaps;
  D3DPOOL            InputPool;
  UINT               OutputFormatCount;
  UINT               InputFormatCount;
  UINT               VideoProcessorCount;
  UINT               MaxInputStreams;
  UINT               MaxStreamStates;
} DXVAHD_VPDEVCAPS;

typedef struct _DXVAHD_BLT_STATE_ALPHA_FILL_DATA {
  DXVAHD_ALPHA_FILL_MODE Mode;
  UINT                   StreamNumber;
} DXVAHD_BLT_STATE_ALPHA_FILL_DATA;

typedef struct _DXVAHD_BLT_STATE_CONSTRICTION_DATA {
  WINBOOL Enable;
  SIZE Size;
} DXVAHD_BLT_STATE_CONSTRICTION_DATA;

typedef struct _DXVAHD_BLT_STATE_OUTPUT_COLOR_SPACE_DATA {
  union {
    struct {
      UINT Usage  :1;
      UINT RGB_Range  :1;
      UINT YCbCr_Matrix  :1;
      UINT YCbCr_xvYCC  :1;
      UINT Reserved : 28;
    };
    UINT Value;
  };
} DXVAHD_BLT_STATE_OUTPUT_COLOR_SPACE_DATA;

typedef struct _DXVAHD_BLT_STATE_PRIVATE_DATA {
  GUID Guid;
  UINT DataSize;
  void *pData;
} DXVAHD_BLT_STATE_PRIVATE_DATA;

typedef struct _DXVAHD_BLT_STATE_TARGET_RECT_DATA {
  WINBOOL Enable;
  RECT TargetRect;
} DXVAHD_BLT_STATE_TARGET_RECT_DATA;

typedef struct _DXVAHD_RATIONAL {
  UINT Numerator;
  UINT Denominator;
} DXVAHD_RATIONAL;

typedef struct _DXVAHD_CONTENT_DESC {
  DXVAHD_FRAME_FORMAT InputFrameFormat;
  DXVAHD_RATIONAL     InputFrameRate;
  UINT                InputWidth;
  UINT                InputHeight;
  DXVAHD_RATIONAL     OutputFrameRate;
  UINT                OutputWidth;
  UINT                OutputHeight;
} DXVAHD_CONTENT_DESC;

typedef struct _DXVAHD_CUSTOM_RATE_DATA {
  DXVAHD_RATIONAL CustomRate;
  UINT            OutputFrames;
  WINBOOL         InputInterlaced;
  UINT            InputFramesOrFields;
} DXVAHD_CUSTOM_RATE_DATA;

typedef struct _DXVAHD_FILTER_RANGE_DATA {
  INT   Minimum;
  INT   Maximum;
  INT   Default;
  FLOAT Multiplier;
} DXVAHD_FILTER_RANGE_DATA;

typedef struct _DXVAHD_STREAM_DATA {
  WINBOOL           Enable;
  UINT              OutputIndex;
  UINT              InputFrameOrField;
  UINT              PastFrames;
  UINT              FutureFrames;
  IDirect3DSurface9 **ppPastSurfaces;
  IDirect3DSurface9 *pInputSurface;
  IDirect3DSurface9 **ppFutureSurfaces;
} DXVAHD_STREAM_DATA;

typedef struct _DXVAHD_VPCAPS {
  GUID VPGuid;
  UINT PastFrames;
  UINT FutureFrames;
  UINT ProcessorCaps;
  UINT ITelecineCaps;
  UINT CustomRateCount;
} DXVAHD_VPCAPS;

typedef struct _DXVAHD_STREAM_STATE_ALPHA_DATA {
  WINBOOL Enable;
  FLOAT   Alpha;
} DXVAHD_STREAM_STATE_ALPHA_DATA;

typedef struct _DXVAHD_STREAM_STATE_ASPECT_RATIO_DATA {
  WINBOOL         Enable;
  DXVAHD_RATIONAL SourceAspectRatio;
  DXVAHD_RATIONAL DestinationAspectRatio;
} DXVAHD_STREAM_STATE_ASPECT_RATIO_DATA;

typedef struct _DXVAHD_STREAM_STATE_D3DFORMAT_DATA {
  D3DFORMAT Format;
} DXVAHD_STREAM_STATE_D3DFORMAT_DATA;

typedef struct _DXVAHD_STREAM_STATE_DESTINATION_RECT_DATA {
  WINBOOL Enable;
  RECT    DestinationRect;
} DXVAHD_STREAM_STATE_DESTINATION_RECT_DATA;

typedef struct _DXVAHD_STREAM_STATE_FILTER_DATA {
  WINBOOL Enable;
  INT     Level;
} DXVAHD_STREAM_STATE_FILTER_DATA;

typedef struct _DXVAHD_STREAM_STATE_FRAME_FORMAT_DATA {
  DXVAHD_FRAME_FORMAT FrameFormat;
} DXVAHD_STREAM_STATE_FRAME_FORMAT_DATA;

typedef struct _DXVAHD_STREAM_STATE_INPUT_COLOR_SPACE_DATA {
  union {
    struct {
      UINT Type  :1;
      UINT RGB_Range  :1;
      UINT YCbCr_Matrix  :1;
      UINT YCbCr_xvYCC  :1;
      UINT Reserved  :28;
    };
    UINT Value;
  };
} DXVAHD_STREAM_STATE_INPUT_COLOR_SPACE_DATA;

typedef struct _DXVAHD_STREAM_STATE_LUMA_KEY_DATA {
  WINBOOL Enable;
  FLOAT   Lower;
  FLOAT   Upper;
} DXVAHD_STREAM_STATE_LUMA_KEY_DATA;

typedef struct _DXVAHD_STREAM_STATE_OUTPUT_RATE_DATA {
  WINBOOL            RepeatFrame;
  DXVAHD_OUTPUT_RATE OutputRate;
  DXVAHD_RATIONAL    CustomRate;
} DXVAHD_STREAM_STATE_OUTPUT_RATE_DATA;

typedef struct _DXVAHD_STREAM_STATE_SOURCE_RECT_DATA {
  WINBOOL Enable;
  RECT    SourceRect;
} DXVAHD_STREAM_STATE_SOURCE_RECT_DATA;

typedef struct _DXVAHD_STREAM_STATE_PRIVATE_IVTC_DATA {
  WINBOOL Enable;
  UINT    ITelecineFlags;
  UINT    Frames;
  UINT    InputField;
} DXVAHD_STREAM_STATE_PRIVATE_IVTC_DATA;

typedef struct _DXVAHD_STREAM_STATE_PRIVATE_DATA {
  GUID Guid;
  UINT DataSize;
  void *pData;
} DXVAHD_STREAM_STATE_PRIVATE_DATA;

typedef struct _DXVAHD_STREAM_STATE_PALETTE_DATA {
  UINT     Count;
  D3DCOLOR *pEntries;
} DXVAHD_STREAM_STATE_PALETTE_DATA;

cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_CreateDevice )(IDirect3DDevice9Ex *pD3DDevice,HANDLE *phDevice);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_ProposeVideoPrivateFormat )(HANDLE hDevice,D3DFORMAT *pFormat);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_GetVideoProcessorDeviceCaps )(HANDLE hDevice,const DXVAHD_CONTENT_DESC *pContentDesc,DXVAHD_DEVICE_USAGE Usage,DXVAHD_VPDEVCAPS *pCaps);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_GetVideoProcessorOutputFormats )(HANDLE hDevice,const DXVAHD_CONTENT_DESC *pContentDesc,DXVAHD_DEVICE_USAGE Usage,UINT Count,D3DFORMAT *pFormats);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_GetVideoProcessorInputFormats )(HANDLE hDevice,const DXVAHD_CONTENT_DESC *pContentDesc,DXVAHD_DEVICE_USAGE Usage,UINT Count,D3DFORMAT *pFormats);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_GetVideoProcessorCaps )(HANDLE hDevice,const DXVAHD_CONTENT_DESC *pContentDesc,DXVAHD_DEVICE_USAGE Usage,UINT Count,DXVAHD_VPCAPS *pCaps);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_GetVideoProcessorCustomRates )(HANDLE hDevice,const GUID *pVPGuid,UINT Count,DXVAHD_CUSTOM_RATE_DATA *pRates);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_SetVideoProcessBltState )(HANDLE hVideoProcessor,DXVAHD_BLT_STATE State,UINT DataSize,const void *pData);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_CreateVideoProcessor )(HANDLE hDevice,const GUID *pVPGuid,HANDLE *phVideoProcessor);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_DestroyDevice )(HANDLE hDevice);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_GetVideoProcessorFilterRange )(HANDLE hDevice,DXVAHD_FILTER Filter,DXVAHD_FILTER_RANGE_DATA *pRange);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_DestroyVideoProcessor )(HANDLE hVideoProcessor);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_VideoProcessBltHD )(HANDLE hVideoProcessor,IDirect3DSurface9 *pOutputSurface,UINT OutputFrame,UINT StreamCount,const DXVAHD_STREAM_DATA *pStreams);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_GetVideoProcessStreamStatePrivate )(HANDLE hVideoProcessor,UINT StreamNumber,DXVAHD_STREAM_STATE_PRIVATE_DATA *pData);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_SetVideoProcessStreamState )(HANDLE hVideoProcessor,UINT StreamNumber,DXVAHD_STREAM_STATE State,UINT DataSize,const void *pData);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_GetVideoProcessBltStatePrivate )(HANDLE hVideoProcessor,DXVAHD_BLT_STATE_PRIVATE_DATA *pData);")
cpp_quote("typedef HRESULT ( CALLBACK *PDXVAHDSW_Plugin )(UINT Size,void *pCallbacks);")
cpp_quote("")
cpp_quote("typedef struct _DXVAHDSW_CALLBACKS {")
cpp_quote("  PDXVAHDSW_CreateDevice                      CreateDevice;")
cpp_quote("  PDXVAHDSW_ProposeVideoPrivateFormat         ProposeVideoPrivateFormat;")
cpp_quote("  PDXVAHDSW_GetVideoProcessorDeviceCaps       GetVideoProcessorDeviceCaps;")
cpp_quote("  PDXVAHDSW_GetVideoProcessorOutputFormats    GetVideoProcessorOutputFormats;")
cpp_quote("  PDXVAHDSW_GetVideoProcessorInputFormats     GetVideoProcessorInputFormats;")
cpp_quote("  PDXVAHDSW_GetVideoProcessorCaps             GetVideoProcessorCaps;")
cpp_quote("  PDXVAHDSW_GetVideoProcessorCustomRates      GetVideoProcessorCustomRates;")
cpp_quote("  PDXVAHDSW_GetVideoProcessorFilterRange      GetVideoProcessorFilterRange;")
cpp_quote("  PDXVAHDSW_DestroyDevice                     DestroyDevice;")
cpp_quote("  PDXVAHDSW_CreateVideoProcessor              CreateVideoProcessor;")
cpp_quote("  PDXVAHDSW_SetVideoProcessBltState           SetVideoProcessBltState;")
cpp_quote("  PDXVAHDSW_GetVideoProcessBltStatePrivate    GetVideoProcessBltStatePrivate;")
cpp_quote("  PDXVAHDSW_SetVideoProcessStreamState        SetVideoProcessStreamState;")
cpp_quote("  PDXVAHDSW_GetVideoProcessStreamStatePrivate GetVideoProcessStreamStatePrivate;")
cpp_quote("  PDXVAHDSW_VideoProcessBltHD                 VideoProcessBltHD;")
cpp_quote("  PDXVAHDSW_DestroyVideoProcessor             DestroyVideoProcessor;")
cpp_quote("} DXVAHDSW_CALLBACKS;")
cpp_quote("")
cpp_quote("HRESULT WINAPI DXVAHD_CreateDevice(IDirect3DDevice9Ex *pD3DDevice,const DXVAHD_CONTENT_DESC *pContentDesc,DXVAHD_DEVICE_USAGE Usage,PDXVAHDSW_Plugin pPlugin,IDXVAHD_Device **ppDevice);")
cpp_quote("")
[
    object,
    uuid(95f12dfd-d77e-49be-815f-57d579634d6d),
    local
]
interface IDXVAHD_Device : IUnknown
{
    HRESULT CreateVideoSurface(UINT Width,UINT Height,D3DFORMAT Format,D3DPOOL Pool,DWORD Usage,DXVAHD_SURFACE_TYPE Type,UINT NumSurfaces,IDirect3DSurface9 **ppSurfaces,HANDLE *pSharedHandle);
    HRESULT GetVideoProcessorDeviceCaps(DXVAHD_VPDEVCAPS *pCaps);
    HRESULT GetVideoProcessorOutputFormats(UINT Count,D3DFORMAT *pFormats);
    HRESULT GetVideoProcessorInputFormats(UINT Count,D3DFORMAT *pFormats);
    HRESULT GetVideoProcessorCaps(UINT Count,DXVAHD_VPCAPS *pCaps);
    HRESULT GetVideoProcessorCustomRates(const GUID *pVPGuid,UINT Count,DXVAHD_CUSTOM_RATE_DATA *pRates);
    HRESULT GetVideoProcessorFilterRange(DXVAHD_FILTER Filter,DXVAHD_FILTER_RANGE_DATA *pRange);
    HRESULT CreateVideoProcessor(const GUID *pVPGuid,IDXVAHD_VideoProcessor **ppVideoProcessor);
};

[
    object,
    uuid(95f4edf4-6e03-4cd7-be1b-3075d665aa52),
    local
]
interface IDXVAHD_VideoProcessor : IUnknown
{
    HRESULT SetVideoProcessBltState(DXVAHD_BLT_STATE State,UINT DataSize,const void *pData);
    HRESULT GetVideoProcessBltState(DXVAHD_BLT_STATE State,UINT DataSize,void *pData);
    HRESULT SetVideoProcessStreamState(UINT StreamNumber,DXVAHD_STREAM_STATE State,UINT DataSize,const void *pData);
    HRESULT GetVideoProcessStreamState(UINT StreamNumber,DXVAHD_STREAM_STATE State,UINT DataSize,void *pData);
    HRESULT VideoProcessBltHD(IDirect3DSurface9 *pOutputSurface,UINT OutputFrame,UINT StreamCount,const DXVAHD_STREAM_DATA *pStreams);
};

cpp_quote("#endif /*(_WIN32_WINNT >= 0x0601)*/")
cpp_quote("#endif /*WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)*/")
