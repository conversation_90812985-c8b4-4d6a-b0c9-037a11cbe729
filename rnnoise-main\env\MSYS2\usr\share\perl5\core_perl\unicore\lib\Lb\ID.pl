# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V336
8986
8988
9200
9204
9728
9732
9748
9750
9752
9753
9754
9757
9758
9760
9785
9788
9832
9833
9855
9856
9917
9929
9933
9934
9935
9938
9939
9941
9944
9946
9948
9949
9951
9954
9962
9963
9969
9974
9975
9977
9978
9979
9981
9989
9992
9994
10084
10085
11904
11930
11931
12020
12032
12246
12272
12284
12291
12293
12294
12296
12306
12308
12320
12330
12336
12341
12342
12347
12349
12352
12354
12355
12356
12357
12358
12359
12360
12361
12362
12387
12388
12419
12420
12421
12422
12423
12424
12430
12431
12437
12447
12448
12450
12451
12452
12453
12454
12455
12456
12457
12458
12483
12484
12515
12516
12517
12518
12519
12520
12526
12527
12533
12535
12539
12543
12544
12549
12592
12593
12687
12688
12772
12800
12831
12832
12872
12880
19904
19968
40981
40982
42125
42128
42183
63744
64256
65072
65077
65093
65095
65097
65104
65105
65106
65112
65113
65119
65127
65128
65129
65131
65132
65282
65284
65286
65288
65290
65292
65293
65294
65295
65306
65308
65311
65312
65339
65340
65341
65342
65371
65372
65373
65374
65375
65382
65383
65393
65438
65440
65471
65474
65480
65482
65488
65490
65496
65498
65501
65506
65509
73541
73552
94208
100344
100352
101120
101632
101641
110592
110883
110960
111356
126976
127232
127245
127248
127341
127344
127405
127462
127488
127877
127878
127900
127902
127925
127927
127932
127933
127938
127941
127943
127944
127946
127949
127995
128000
128066
128068
128070
128081
128102
128121
128124
128125
128129
128132
128133
128136
128143
128144
128145
128146
128160
128161
128162
128163
128164
128165
128170
128171
128175
128176
128177
128179
128256
128263
128279
128293
128306
128330
128372
128374
128378
128379
128400
128401
128405
128407
128468
128476
128500
128506
128581
128584
128587
128640
128675
128676
128692
128695
128704
128705
128716
128717
128768
128884
128896
128981
129024
129036
129040
129096
129104
129114
129120
129160
129168
129198
129280
129293
129295
129296
129304
129312
129318
129319
129328
129338
129340
129343
129399
129400
129461
129463
129464
129466
129467
129468
129485
129488
129489
129502
129536
129620
129731
129734
129776
129785
129792
130048
131070
131072
196606
196608
262142
END
