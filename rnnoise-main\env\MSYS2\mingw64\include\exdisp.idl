/*
 * Defines the COM interfaces and APIs related to the IE Web browser
 *
 * Copyright (C) 2001 John R. Sheets (for CodeWeavers)
 * Copyright (C) 2003 Alexandre Julliard
 * Copyright (C) 2004 <PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "ocidl.idl";
import "docobj.idl";

#include <olectl.h>
#include <exdispid.h>

#ifndef __WIDL__
#define threading(model)
#define progid(str)
#define vi_progid(str)
#endif

cpp_quote("#ifdef WINE_NO_UNICODE_MACROS")
cpp_quote("#undef FindText")
cpp_quote("#endif")

/*****************************************************************************
 * SHDocVw library
 */
[
    uuid(EAB22AC0-30C1-11CF-A7EB-0000C05BAE0B),
    version(1.1),
    helpstring("Microsoft Internet Controls")
]
library SHDocVw
{

importlib("stdole2.tlb");


/*****************************************************************************
 * IWebBrowser interface
 */
[
    object,
    oleautomation,
    uuid(eab22ac1-30c1-11cf-a7eb-0000c05bae0b),
    hidden,
    dual
]
interface IWebBrowser : IDispatch
{
    typedef enum BrowserNavConstants
    {
        navOpenInNewWindow          = 0x00000001,
        navNoHistory                = 0x00000002,
        navNoReadFromCache          = 0x00000004,
        navNoWriteToCache           = 0x00000008,
        navAllowAutosearch          = 0x00000010,
        navBrowserBar               = 0x00000020,
        navHyperlink                = 0x00000040,
        navEnforceRestricted        = 0x00000080,
        navNewWindowsManaged        = 0x00000100,
        navUntrustedForDownload     = 0x00000200,
        navTrustedForActiveX        = 0x00000400,
        navOpenInNewTab             = 0x00000800,
        navOpenInBackgroundTab      = 0x00001000,
        navKeepWordWheelText        = 0x00002000,
        navVirtualTab               = 0x00004000,
        navBlockRedirectsXDomain    = 0x00008000,
        navOpenNewForegroundTab     = 0x00010000,
        navTravelLogScreenshot      = 0x00020000,
        navDeferUnload              = 0x00040000,
        navSpeculative              = 0x00080000,
        navSuggestNewWindow         = 0x00100000,
        navSuggestNewTab            = 0x00200000,
        navReserved1                = 0x00400000,
        navHomepageNavigate         = 0x00800000,
        navRefresh                  = 0x01000000,
        navHostNavigation           = 0x02000000,
        navReserved2                = 0x04000000,
        navReserved3                = 0x08000000,
        navReserved4                = 0x10000000,
    } BrowserNavConstants;

    typedef enum RefreshConstants
    {
        REFRESH_NORMAL     = 0,
        REFRESH_IFEXPIRED  = 1,
        REFRESH_COMPLETELY = 3
    } RefreshConstants;

    [id(100)] HRESULT GoBack();
    [id(101)] HRESULT GoForward();
    [id(102)] HRESULT GoHome();
    [id(103)] HRESULT GoSearch();

    [id(104)]
    HRESULT Navigate(
        [in] BSTR URL,
        [in, optional] VARIANT *Flags,
        [in, optional] VARIANT *TargetFrameName,
        [in, optional] VARIANT *PostData,
        [in, optional] VARIANT *Headers);

    [id(DISPID_REFRESH)] HRESULT Refresh();
    [id(105)] HRESULT Refresh2([in, optional] VARIANT *Level);
    [id(106)] HRESULT Stop();
    [id(200), propget] HRESULT Application([out, retval] IDispatch** ppDisp);
    [id(201), propget] HRESULT Parent([out, retval] IDispatch** ppDisp);
    [id(202), propget] HRESULT Container([out, retval] IDispatch** ppDisp);
    [id(203), propget] HRESULT Document([out, retval] IDispatch** ppDisp);
    [id(204), propget] HRESULT TopLevelContainer([out, retval] VARIANT_BOOL* pBool);
    [id(205), propget] HRESULT Type([out, retval] BSTR* Type);
    [id(206), propget] HRESULT Left([out, retval] long *pl);
    [id(206), propput] HRESULT Left([in] long Left);
    [id(207), propget] HRESULT Top([out, retval] long *pl);
    [id(207), propput] HRESULT Top([in] long Top);
    [id(208), propget] HRESULT Width([out, retval] long *pl);
    [id(208), propput] HRESULT Width([in] long Width);
    [id(209), propget] HRESULT Height([out, retval] long *pl);
    [id(209), propput] HRESULT Height([in] long Height);
    [id(210), propget] HRESULT LocationName([out, retval] BSTR *LocationName);
    [id(211), propget] HRESULT LocationURL([out, retval] BSTR *LocationURL);
    [id(212), propget] HRESULT Busy([out, retval] VARIANT_BOOL *pBool);
}

/*****************************************************************************
 * DWebBrowserEvents dispinterface
 */
[
    uuid(eab22ac2-30c1-11CF-a7eb-0000C05bae0b),
    hidden
]
dispinterface DWebBrowserEvents
{
    properties:
    methods:
    [id(DISPID_BEFORENAVIGATE)]
    void BeforeNavigate(
        [in] BSTR URL,
        long Flags,
        BSTR TargetFrameName,
        VARIANT *PostData,
        BSTR Headers,
        [in, out] VARIANT_BOOL *Cancel);

    [id(DISPID_NAVIGATECOMPLETE)]
    void NavigateComplete([in] BSTR URL);

    [id(DISPID_STATUSTEXTCHANGE)]
    void StatusTextChange([in] BSTR Text);

    [id(DISPID_PROGRESSCHANGE)]
    void ProgressChange([in] long Progress, [in] long ProgressMax);

    [id(DISPID_DOWNLOADCOMPLETE)]
    void DownloadComplete();

    [id(DISPID_COMMANDSTATECHANGE)]
    void CommandStateChange([in] long Command, [in]VARIANT_BOOL Enable);

    [id(DISPID_DOWNLOADBEGIN)]
    void DownloadBegin();

    [id(DISPID_NEWWINDOW)]
    void NewWindow(
        [in] BSTR URL,
        [in] long Flags,
        [in] BSTR TargetFrameName,
        [in] VARIANT *PostData,
        [in] BSTR Headers,
        [in,out] VARIANT_BOOL *Processed);

    [id(DISPID_TITLECHANGE)]
    void TitleChange([in] BSTR Text);

    [id(DISPID_FRAMEBEFORENAVIGATE)]
    void FrameBeforeNavigate(
        [in] BSTR URL,
        long Flags,
        BSTR TargetFrameName,
        VARIANT *PostData,
        BSTR Headers,
        [in, out]VARIANT_BOOL *Cancel);

    [id(DISPID_FRAMENAVIGATECOMPLETE)]
    void FrameNavigateComplete([in] BSTR URL);

    [id(DISPID_FRAMENEWWINDOW)]
    void FrameNewWindow(
        [in] BSTR URL,
        [in] long Flags,
        [in] BSTR TargetFrameName,
        [in] VARIANT *PostData,
        [in] BSTR Headers,
        [in,out] VARIANT_BOOL *Processed);

    [id(DISPID_QUIT)]
    void Quit([in, out] VARIANT_BOOL *Cancel);

    [id(DISPID_WINDOWMOVE)]
    void WindowMove();

    [id(DISPID_WINDOWRESIZE)]
    void WindowResize();

    [id(DISPID_WINDOWACTIVATE)]
    void WindowActivate();

    [id(DISPID_PROPERTYCHANGE)]
    void PropertyChange([in] BSTR Property);
}

typedef
[
    uuid(34a226e0-df30-11cf-89a9-00a0c9054129)
]
enum CommandStateChangeConstants {
    CSC_UPDATECOMMANDS  = -1,
    CSC_NAVIGATEFORWARD = 1,
    CSC_NAVIGATEBACK    = 2
} CommandStateChangeConstants;

/*****************************************************************************
 * IWebBrowserApp interface
 */
[
    object,
    oleautomation,
    uuid(0002df05-0000-0000-c000-000000000046),
    hidden,
    dual
]
interface IWebBrowserApp : IWebBrowser
{
    [id(300)] HRESULT Quit();
    [id(301)] HRESULT ClientToWindow([in,out] int* pcx, [in,out] int* pcy);
    [id(302)] HRESULT PutProperty([in] BSTR Property, [in] VARIANT vtValue);
    [id(303)] HRESULT GetProperty([in] BSTR Property, [out, retval] VARIANT *pvtValue);
    [id(DISPID_VALUE), propget] HRESULT Name([out, retval] BSTR* Name);
    [id(DISPID_HWND), propget] HRESULT HWND([out, retval] SHANDLE_PTR *pHWND);
    [id(400), propget] HRESULT FullName([out, retval] BSTR* FullName);
    [id(401), propget] HRESULT Path([out, retval] BSTR* Path);
    [id(402), propget] HRESULT Visible([out, retval] VARIANT_BOOL* pBool);
    [id(402), propput] HRESULT Visible([in] VARIANT_BOOL Value);
    [id(403), propget] HRESULT StatusBar([out, retval] VARIANT_BOOL* pBool);
    [id(403), propput] HRESULT StatusBar([in] VARIANT_BOOL Value);
    [id(404), propget] HRESULT StatusText([out, retval] BSTR *StatusText);
    [id(404), propput] HRESULT StatusText([in] BSTR StatusText);
    [id(405), propget] HRESULT ToolBar([out, retval] int * Value);
    [id(405), propput] HRESULT ToolBar([in] int Value);
    [id(406), propget] HRESULT MenuBar([out, retval] VARIANT_BOOL *Value);
    [id(406), propput] HRESULT MenuBar([in] VARIANT_BOOL Value);
    [id(407), propget] HRESULT FullScreen([out, retval] VARIANT_BOOL *pbFullScreen);
    [id(407), propput] HRESULT FullScreen([in] VARIANT_BOOL bFullScreen);
}

/*****************************************************************************
 * IWebBrowser2 interface
 */
[
    object,
    oleautomation,
    uuid(d30c1661-cdaf-11d0-8a3e-00c04fc9e26e),
    hidden,
    dual
]
interface IWebBrowser2 : IWebBrowserApp
{
    [id(500)] HRESULT Navigate2(
        [in] VARIANT *URL,
        [in, optional] VARIANT *Flags,
        [in, optional] VARIANT *TargetFrameName,
        [in, optional] VARIANT *PostData,
        [in, optional] VARIANT *Headers);

    [id(501)] HRESULT QueryStatusWB(
        [in] OLECMDID cmdID,
        [out, retval] OLECMDF *pcmdf);

    [id(502)] HRESULT ExecWB(
        [in] OLECMDID cmdID,
        [in] OLECMDEXECOPT cmdexecopt,
        [in, optional] VARIANT *pvaIn,
        [out, in, optional] VARIANT *pvaOut);

    [id(503)] HRESULT ShowBrowserBar(
        [in] VARIANT *pvaClsid,
        [in, optional] VARIANT *pvarShow,
        [in, optional] VARIANT *pvarSize);

    [id(DISPID_READYSTATE), propget, bindable]
    HRESULT ReadyState([out, retval] READYSTATE *plReadyState);

    [id(550), propget] HRESULT Offline([out, retval] VARIANT_BOOL *pbOffline);
    [id(550), propput] HRESULT Offline([in] VARIANT_BOOL bOffline);
    [id(551), propget] HRESULT Silent([out, retval] VARIANT_BOOL *pbSilent);
    [id(551), propput] HRESULT Silent([in] VARIANT_BOOL bSilent);
    [id(552), propget] HRESULT RegisterAsBrowser([out, retval] VARIANT_BOOL *pbRegister);
    [id(552), propput] HRESULT RegisterAsBrowser([in] VARIANT_BOOL bRegister);
    [id(553), propget] HRESULT RegisterAsDropTarget([out, retval] VARIANT_BOOL *pbRegister);
    [id(553), propput] HRESULT RegisterAsDropTarget([in] VARIANT_BOOL bRegister);
    [id(554), propget] HRESULT TheaterMode([out, retval] VARIANT_BOOL *pbRegister);
    [id(554), propput] HRESULT TheaterMode([in] VARIANT_BOOL bRegister);
    [id(555), propget] HRESULT AddressBar([out, retval] VARIANT_BOOL *Value);
    [id(555), propput] HRESULT AddressBar([in] VARIANT_BOOL Value);
    [id(556), propget] HRESULT Resizable([out, retval] VARIANT_BOOL *Value);
    [id(556), propput] HRESULT Resizable([in] VARIANT_BOOL Value);
}

typedef
[
    uuid(65507be0-91a8-11d3-a845-009027220e6d)
]
enum SecureLockIconConstants {
    secureLockIconUnsecure          = 0,
    secureLockIconMixed             = 1,
    secureLockIconSecureUnknownBits = 2,
    secureLockIconSecure40Bit       = 3,
    secureLockIconSecure56Bit       = 4,
    secureLockIconSecureFortezza    = 5,
    secureLockIconSecure128Bit      = 6
} SecureLockIconConstants;

/*****************************************************************************
 * DWebBrowserEvents2 dispinterface
 */
[
    uuid(34a715a0-6587-11d0-924a-0020afc7ac4d),
    hidden
]
dispinterface DWebBrowserEvents2
{
    properties:
    methods:
    [id(DISPID_STATUSTEXTCHANGE)]
    void StatusTextChange([in] BSTR Text);

    [id(DISPID_PROGRESSCHANGE)]
    void ProgressChange([in] long Progress, [in] long ProgressMax);

    [id(DISPID_COMMANDSTATECHANGE)]
    void CommandStateChange([in] long Command, [in] VARIANT_BOOL Enable);

    [id(DISPID_DOWNLOADBEGIN)]
    void DownloadBegin();

    [id(DISPID_DOWNLOADCOMPLETE)]
    void DownloadComplete();

    [id(DISPID_TITLECHANGE)]
    void TitleChange([in] BSTR Text);

    [id(DISPID_PROPERTYCHANGE)]
    void PropertyChange([in] BSTR szProperty);

    [id(DISPID_BEFORENAVIGATE2)]
    void BeforeNavigate2(
        [in] IDispatch *pDisp,
        [in] VARIANT *URL,
        [in] VARIANT *Flags,
        [in] VARIANT *TargetFrameName,
        [in] VARIANT *PostData,
        [in] VARIANT *Headers,
        [in, out] VARIANT_BOOL *Cancel);

    [id(DISPID_NEWWINDOW2)]
    void NewWindow2([in, out] IDispatch **ppDisp, [in, out] VARIANT_BOOL *Cancel);

    [id(DISPID_NAVIGATECOMPLETE2)]
    void NavigateComplete2([in] IDispatch *pDisp, [in] VARIANT *URL);

    [id(DISPID_DOCUMENTCOMPLETE)]
    void DocumentComplete([in] IDispatch *pDisp, [in] VARIANT *URL);

    [id(DISPID_ONQUIT)]
    void OnQuit();

    [id(DISPID_ONVISIBLE)]
    void OnVisible([in] VARIANT_BOOL Visible);

    [id(DISPID_ONTOOLBAR)]
    void OnToolBar([in] VARIANT_BOOL ToolBar);

    [id(DISPID_ONMENUBAR)]
    void OnMenuBar([in] VARIANT_BOOL MenuBar);

    [id(DISPID_ONSTATUSBAR)]
    void OnStatusBar([in] VARIANT_BOOL StatusBar);

    [id(DISPID_ONFULLSCREEN)]
    void OnFullScreen([in] VARIANT_BOOL FullScreen);

    [id(DISPID_ONTHEATERMODE)]
    void OnTheaterMode([in] VARIANT_BOOL TheaterMode);

    [id(DISPID_WINDOWSETRESIZABLE)]
    void WindowSetResizable([in] VARIANT_BOOL Resizable);

    [id(DISPID_WINDOWSETLEFT)]
    void WindowSetLeft([in] long Left);

    [id(DISPID_WINDOWSETTOP)]
    void WindowSetTop([in] long Top);

    [id(DISPID_WINDOWSETWIDTH)]
    void WindowSetWidth([in] long Width);

    [id(DISPID_WINDOWSETHEIGHT)]
    void WindowSetHeight([in] long Height);

    [id(DISPID_WINDOWCLOSING)]
    void WindowClosing(
            [in] VARIANT_BOOL IsChildWindow,
            [in, out] VARIANT_BOOL *Cancel);

    [id(DISPID_CLIENTTOHOSTWINDOW)]
    void ClientToHostWindow(
            [in, out] long *CX,
            [in, out] long *CY);

    [id(DISPID_SETSECURELOCKICON)]
    void SetSecureLockIcon([in] long SecureLockIcon);

    [id(DISPID_FILEDOWNLOAD)]
    void FileDownload(
            [in] VARIANT_BOOL ActiveDocument,
            [in, out] VARIANT_BOOL *Cancel);

    [id(DISPID_NAVIGATEERROR)]
    void NavigateError(
            [in] IDispatch *pDisp,
            [in] VARIANT *URL,
            [in] VARIANT *Frame,
            [in] VARIANT *StatusCode,
            [in, out] VARIANT_BOOL *Cancel);

    [id(DISPID_PRINTTEMPLATEINSTANTIATION)]
    void PrintTemplateInstantiation([in] IDispatch *pDisp);

    [id(DISPID_PRINTTEMPLATETEARDOWN)]
    void PrintTemplateTeardown([in] IDispatch *pDisp);

    [id(DISPID_UPDATEPAGESTATUS)]
    void UpdatePageStatus(
            [in] IDispatch *pDisp,
            [in] VARIANT *nPage,
            [in] VARIANT *fDone);

    [id(DISPID_PRIVACYIMPACTEDSTATECHANGE)]
    void PrivacyImpactedStateChange([in] VARIANT_BOOL bImpacted);

    [id(DISPID_NEWWINDOW3)]
    void NewWindow3(
            [in, out] IDispatch **ppDisp,
            [in, out] VARIANT_BOOL *Cancel,
            [in] DWORD dwFlags,
            [in] BSTR bstrUrlContext,
            [in] BSTR bstrUrl);

    [id(DISPID_SETPHISHINGFILTERSTATUS)]
    void SetPhishingFilterStatus([in] long PhishingFilterStatus);

    [id(DISPID_WINDOWSTATECHANGED)]
    void WindowStateChanged(
            [in] DWORD dwWindowStateFlags,
            [in] DWORD dwValidFlagsMask);

    [id(DISPID_NEWPROCESS)]
    void NewProcess(
            [in] long lCauseFlag,
            [in] IDispatch *pWB2,
            [in, out] VARIANT_BOOL *Cancel);

    [id(DISPID_THIRDPARTYURLBLOCKED)]
    void ThirdPartyUrlBlocked(
            [in] VARIANT *URL,
            [in] DWORD dwCount);

    [id(DISPID_REDIRECTXDOMAINBLOCKED)]
    void RedirectXDomainBlocked(
            [in] IDispatch *pDisp,
            [in] VARIANT *StartURL,
            [in] VARIANT *RedirectURL,
            [in] VARIANT *Frame,
            [in] VARIANT *StatusCode);

    [id(DISPID_BEFORESCRIPTEXECUTE)]
    void BeforeScriptExecute([in] IDispatch *pDispWindow);

    [id(DISPID_WEBWORKERSTARTED)]
    void WebWorkerStarted(
            [in] DWORD dwUniqueID,
            [in] BSTR bstrWorkerLabel);

    [id(DISPID_WEBWORKERFINISHED)]
    void WebWorkerFinished([in] DWORD dwUniqueID);
}

[
    helpstring("Microsoft Web Browser Version 1"),
    threading(apartment),
    progid("Shell.Explorer.1"),
    vi_progid("Shell.Explorer"),
    uuid(eab22ac3-30c1-11cf-a7eb-0000c05bae0b),
    control
]
coclass WebBrowser_V1
{
    interface IWebBrowser2;
    [default] interface IWebBrowser;
    [source] dispinterface DWebBrowserEvents2;
    [default, source] dispinterface DWebBrowserEvents;
}

[
    helpstring("Microsoft Web Browser"),
    threading(apartment),
    progid("Shell.Explorer.2"),
    vi_progid("Shell.Explorer"),
    uuid(8856f961-340a-11d0-a96b-00c04fd705a2),
    control
]
coclass WebBrowser
{
    [default] interface IWebBrowser2;
    interface IWebBrowser;
    [default, source] dispinterface DWebBrowserEvents2;
    [source] dispinterface DWebBrowserEvents;
}

[
    helpstring("Internet Explorer(Ver 1.0)"),
    progid("InternetExplorer.Application.1"),
    vi_progid("InternetExplorer.Application"),
    uuid(0002df01-0000-0000-c000-000000000046)
]
coclass InternetExplorer
{
    [default] interface IWebBrowser2;
              interface IWebBrowserApp;
    [default, source] dispinterface DWebBrowserEvents2;
    [source]          dispinterface DWebBrowserEvents;
}

[
    uuid(C08AFD90-F2A1-11D1-8455-00A0C91F3880),
    hidden
]
coclass ShellBrowserWindow
{
    [default] interface IWebBrowser2;
    interface IWebBrowserApp;
    [default, source] dispinterface DWebBrowserEvents2;
    [source] dispinterface DWebBrowserEvents;
}

typedef
[
    uuid(f41e6981-28e5-11d0-82b4-00a0c90c29c5)
]
enum ShellWindowTypeConstants {
    SWC_EXPLORER = 0,
    SWC_BROWSER  = 1,
    SWC_3RDPARTY = 2,
    SWC_CALLBACK = 4,
    SWC_DESKTOP  = 8
} ShellWindowTypeConstants;

typedef
[
    uuid(7716a370-38Ca-11d0-a48B-00a0c90a8f39)
]
enum ShellWindowFindWindowOptions {
    SWFO_NEEDDISPATCH   = 1,
    SWFO_INCLUDEPENDING = 2,
    SWFO_COOKIEPASSED   = 4
} ShellWindowFindWindowOptions;

[
    uuid(FE4106E0-399A-11D0-A48C-00A0C90A8F39)
]
dispinterface DShellWindowsEvents {
    properties:
    methods:
    [id(DISPID_WINDOWREGISTERED)]
    void WindowRegistered([in] long lCookie);

    [id(DISPID_WINDOWREVOKED)]
    void WindowRevoked([in] long lCookie);
}

[
    object,
    oleautomation,
    uuid(85cb6900-4d95-11cf-960c-0080c7f4ee85),
    dual
]
interface IShellWindows : IDispatch
{
    [propget] HRESULT Count([out, retval] long *Count);

    [id(DISPID_VALUE)]
    HRESULT Item(
            [in, optional] VARIANT index,
            [out, retval] IDispatch **Folder);

    [id(DISPID_NEWENUM)]
    HRESULT _NewEnum([out, retval] IUnknown **ppunk);

    [hidden] HRESULT Register(
            [in] IDispatch *pid,
            [in] long hWnd,
            [in] int swClass,
            [out] long *plCookie);

    [hidden] HRESULT RegisterPending(
            [in] long lThreadId,
            [in] VARIANT *pvarloc,
            [in] VARIANT *pvarlocRoot,
            [in] int swClass,
            [out] long *plCookie);

    [hidden] HRESULT Revoke([in] long lCookie);
    [hidden] HRESULT OnNavigate([in] long lCookie, [in] VARIANT *pvarLoc);
    [hidden] HRESULT OnActivated([in] long lCookie, [in] VARIANT_BOOL fActive);
    [hidden] HRESULT FindWindowSW(
            [in] VARIANT *pvarLoc,
            [in] VARIANT *pvarLocRoot,
            [in] int swClass,
            [out] long *phwnd,
            [in] int swfwOptions,
            [out, retval] IDispatch **ppdispOut);

    [hidden] HRESULT OnCreated([in] long lCookie, [in] IUnknown *punk);
    [hidden] HRESULT ProcessAttachDetach([in] VARIANT_BOOL fAttach);
}

[
    threading(apartment),
    uuid(9ba05972-f6a8-11cf-a442-00a0c90a8f39)
]
coclass ShellWindows
{
    [default] interface IShellWindows;
    [default, source] dispinterface DShellWindowsEvents;
}

[
    odl,
    uuid(729fe2f8-1ea8-11d1-8f85-00C04fc2fbe1),
    dual,
    oleautomation
]
interface IShellUIHelper : IDispatch {
    [id(1), hidden] HRESULT ResetFirstBootMode();
    [id(2), hidden] HRESULT ResetSafeMode();
    [id(3), hidden] HRESULT RefreshOfflineDesktop();

    [id(4)] HRESULT AddFavorite(
            [in] BSTR URL,
            [in, optional] VARIANT* Title);

    [id(5)] HRESULT AddChannel([in] BSTR URL);

    [id(6)] HRESULT AddDesktopComponent(
            [in] BSTR URL,
            [in] BSTR Type,
            [in, optional] VARIANT *Left,
            [in, optional] VARIANT *Top,
            [in, optional] VARIANT *Width,
            [in, optional] VARIANT *Height);

    [id(7)] HRESULT IsSubscribed(
            [in] BSTR URL,
            [out, retval] VARIANT_BOOL *pBool);

    [id(8)] HRESULT NavigateAndFind(
            [in] BSTR URL,
            [in] BSTR strQuery,
            [in] VARIANT *varTargetFrame);

    [id(9)] HRESULT ImportExportFavorites(
            [in] VARIANT_BOOL fImport,
            [in] BSTR strImpExpPath);

    [id(10)] HRESULT AutoCompleteSaveForm([in, optional] VARIANT *Form);

    [id(11)] HRESULT AutoScan(
            [in] BSTR strSearch,
            [in] BSTR strFailureUrl,
            [in, optional] VARIANT *pvarTargetFrame);

    [id(12), hidden] HRESULT AutoCompleteAttach([in, optional] VARIANT *Reserved);

    [id(13)] HRESULT ShowBrowserUI(
            [in] BSTR bstrName,
            [in] VARIANT *pvarIn,
            [out, retval] VARIANT *pvarOut);
}

[
    uuid(a7fe6eda-1932-4281-b881-87b31b8bc52c),
    oleautomation,
    dual
]
interface IShellUIHelper2 : IShellUIHelper {
    [id(DISPID_ADDSEARCHPROVIDER)]
    HRESULT AddSearchProvider([in] BSTR URL);

    [id(DISPID_RUNONCESHOWN)]
    HRESULT RunOnceShown();

    [id(DISPID_SKIPRUNONCE)]
    HRESULT SkipRunOnce();

    [id(DISPID_CUSTOMIZESETTINGS)] HRESULT CustomizeSettings(
        [in] VARIANT_BOOL fSQM,
        [in] VARIANT_BOOL fPhishing,
        [in] BSTR bstrLocale);

    [id(DISPID_SQMENABLED)]
    HRESULT SqmEnabled([out, retval] VARIANT_BOOL *pfEnabled);

    [id(DISPID_PHISHINGENABLED)]
    HRESULT PhishingEnabled([out, retval] VARIANT_BOOL *pfEnabled);

    [id(DISPID_BRANDIMAGEURI)]
    HRESULT BrandImageUri([out, retval] BSTR *pbstrUri);

    [id(DISPID_SKIPTABSWELCOME)]
    HRESULT SkipTabsWelcome();

    [id(DISPID_DIAGNOSECONNECTION)]
    HRESULT DiagnoseConnection();

    [id(DISPID_CUSTOMIZECLEARTYPE)]
    HRESULT CustomizeClearType([in] VARIANT_BOOL fSet);

    [id(DISPID_ISSEARCHPROVIDERINSTALLED)]
    HRESULT IsSearchProviderInstalled(
        [in] BSTR URL,
        [out, retval] DWORD *pdwResult);

    [id(DISPID_ISSEARCHMIGRATED)]
    HRESULT IsSearchMigrated([out, retval] VARIANT_BOOL *pfMigrated);

    [id(DISPID_DEFAULTSEARCHPROVIDER)]
    HRESULT DefaultSearchProvider([out, retval] BSTR *pbstrName);

    [id(DISPID_RUNONCEREQUIREDSETTINGSCOMPLETE)]
    HRESULT RunOnceRequiredSettingsComplete([in] VARIANT_BOOL fComplete);

    [id(DISPID_RUNONCEHASSHOWN)]
    HRESULT RunOnceHasShown([out, retval] VARIANT_BOOL *pfShown);

    [id(DISPID_SEARCHGUIDEURL)]
    HRESULT SearchGuideUrl([out, retval] BSTR *pbstrUrl);
}

[
    helpstring("Microsoft Shell UI Helper"),
    threading(apartment),
    progid("Shell.UIHelper.1"),
    vi_progid("Shell.UIHelper"),
    uuid(64ab4bb7-111e-11d1-8f79-00c04fc2fbe1)
]
coclass ShellUIHelper {
    [default] interface IShellUIHelper2;
}

[
    uuid(55136806-b2de-11d1-b9f2-00a0c98bc547)
]
dispinterface DShellNameSpaceEvents {
    properties:
    methods:
    [id(1)] void FavoritesSelectionChange(
            [in] long cItems,
            [in] long hItem,
            [in] BSTR strName,
            [in] BSTR strUrl,
            [in] long cVisits,
            [in] BSTR strDate,
            [in] long fAvailableOffline);

    [id(2)] void SelectionChange();
    [id(3)] void DoubleClick();
    [id(4)] void Initialized();
}

[
    odl,
    uuid(55136804-b2de-11d1-b9f2-00a0c98bc547),
    hidden,
    dual,
    oleautomation
]
interface IShellFavoritesNameSpace : IDispatch {
    [id(1)] HRESULT MoveSelectionUp();
    [id(2)] HRESULT MoveSelectionDown();
    [id(3)] HRESULT ResetSort();
    [id(4)] HRESULT NewFolder();
    [id(5)] HRESULT Synchronize();
    [id(6)] HRESULT Import();
    [id(7)] HRESULT Export();
    [id(8)] HRESULT InvokeContextMenuCommand([in] BSTR strCommand);
    [id(9)] HRESULT MoveSelectionTo();
    [id(10), propget] HRESULT SubscriptionsEnabled([out, retval] VARIANT_BOOL *pBool);
    [id(11)] HRESULT CreateSubscriptionForSelection([out, retval] VARIANT_BOOL *pBool);
    [id(12)] HRESULT DeleteSubscriptionForSelection([out, retval] VARIANT_BOOL *pBool);
    [id(13)] HRESULT SetRoot([in] BSTR bstrFullPath);
}

[
    odl,
    uuid(e572d3c9-37be-4ae2-825d-d521763e3108),
    hidden,
    dual,
    oleautomation
]
interface IShellNameSpace : IShellFavoritesNameSpace {
    [id(14), propget] HRESULT EnumOptions([out, retval] long* pgrfEnumFlags);
    [id(14), propput] HRESULT EnumOptions([in] long pgrfEnumFlags);

    [id(15), propget] HRESULT SelectedItem([out, retval] IDispatch **pItem);
    [id(15), propput] HRESULT SelectedItem([in] IDispatch *pItem);

    [id(16), propget] HRESULT Root([out, retval] VARIANT *pvar);
    [id(16), propput] HRESULT Root([in] VARIANT pvar);

    [id(17), propget] HRESULT Depth([out, retval] int *piDepth);
    [id(17), propput] HRESULT Depth([in] int piDepth);

    [id(18), propget] HRESULT Mode([out, retval] unsigned int *puMode);
    [id(18), propput] HRESULT Mode([in] unsigned int puMode);

    [id(19), propget] HRESULT Flags([out, retval] unsigned long *pdwFlags);
    [id(19), propput] HRESULT Flags([in] unsigned long pdwFlags);

    [id(20), propput] HRESULT TVFlags([in] unsigned long dwFlags);
    [id(20), propget] HRESULT TVFlags([out, retval] unsigned long *dwFlags);

    [id(21), propget] HRESULT Columns([out, retval] BSTR *bstrColumns);
    [id(21), propput] HRESULT Columns([in] BSTR bstrColumns);

    [id(22), propget] HRESULT CountViewTypes([out, retval] int *piTypes);

    [id(23)] HRESULT SetViewType([in] int iType);
    [id(24)] HRESULT SelectedItems([out, retval] IDispatch **ppid);
    [id(25)] HRESULT Expand([in] VARIANT var,  int iDepth);
    [id(26)] HRESULT UnselectAll();
}

[
    helpstring("Shell Name Space"),
    threading(apartment),
    progid("ShellNameSpace.ShellNameSpace.1"),
    vi_progid("ShellNameSpace.ShellNameSpace"),
    uuid(2f2f1f96-2bc1-4b1c-be28-ea3774f4676a)
]
coclass ShellShellNameSpace {
    [default] interface IShellNameSpace;
    [default, source] dispinterface DShellNameSpaceEvents;
}

[
    helpstring("Shell Name Space"),
    threading(apartment),
    progid("ShellNameSpace.ShellNameSpace.1"),
    vi_progid("ShellNameSpace.ShellNameSpace"),
    uuid(55136805-b2de-11d1-b9f2-00a0c98bc547)
]
coclass ShellNameSpace {
    [default] interface IShellNameSpace;
    [default, source] dispinterface DShellNameSpaceEvents;
}

[
    odl,
    uuid(f3470f24-15fd-11d2-bb2e-00805ff7efca),
    hidden,
    dual,
    oleautomation
]
interface IScriptErrorList : IDispatch {
    [id(10)] HRESULT advanceError();
    [id(11)] HRESULT retreatError();
    [id(12)] HRESULT canAdvanceError([out, retval] long *pfCanAdvance);
    [id(13)] HRESULT canRetreatError([out, retval] long *pfCanRetreat);
    [id(14)] HRESULT getErrorLine([out, retval] long *plLine);
    [id(15)] HRESULT getErrorChar([out, retval] long *plChar);
    [id(16)] HRESULT getErrorCode([out, retval] long *plCode);
    [id(17)] HRESULT getErrorMsg([out, retval] BSTR *pstr);
    [id(18)] HRESULT getErrorUrl([out, retval] BSTR *pstr);
    [id(23)] HRESULT getAlwaysShowLockState([out, retval] long *pfAlwaysShowLocked);
    [id(19)] HRESULT getDetailsPaneOpen([out, retval] long *pfDetailsPaneOpen);
    [id(20)] HRESULT setDetailsPaneOpen(long fDetailsPaneOpen);
    [id(21)] HRESULT getPerErrorDisplay([out, retval] long *pfPerErrorDisplay);
    [id(22)] HRESULT setPerErrorDisplay(long fPerErrorDisplay);
}

[
    uuid(efd01300-160f-11d2-bb2e-00805ff7efca),
    hidden,
    noncreatable
]
coclass CScriptErrorList {
    [default] interface IScriptErrorList;
}

[
    odl,
    uuid(ba9239a4-3dd5-11d2-bf8b-00c04fb93661),
    hidden,
    dual,
    oleautomation
]
interface ISearch : IDispatch {
    [propget] HRESULT Title([out, retval] BSTR *pbstrTitle);
    [propget] HRESULT Id([out, retval] BSTR *pbstrId);
    [propget] HRESULT URL([out, retval] BSTR *pbstrUrl);
}

[
    odl,
    uuid(47c922a2-3dd5-11d2-bf8b-00c04fb93661),
    hidden,
    dual,
    oleautomation
]
interface ISearches : IDispatch {
    [propget] HRESULT Count([out, retval] long *plCount);
    [propget] HRESULT Default([out, retval] BSTR *pbstrDefault);

    HRESULT Item(
            [in, optional] VARIANT index,
            [out, retval] ISearch **ppid);

    [id(DISPID_NEWENUM)]
    HRESULT _NewEnum([out, retval] IUnknown **ppunk);
}

[
    odl,
    uuid(72423e8f-8011-11d2-be79-00a0c9a83da1),
    hidden,
    dual,
    oleautomation
]
interface ISearchAssistantOC : IDispatch {
    [id(1)] HRESULT AddNextMenuItem([in] BSTR bstrText, [in] long idItem);
    [id(2)] HRESULT SetDefaultSearchUrl([in] BSTR bstrUrl);
    [id(3)] HRESULT NavigateToDefaultSearch();

    [id(4)] HRESULT IsRestricted(
            [in] BSTR bstrGuid,
            [out, retval] VARIANT_BOOL *pVal);

    [id(5), propget] HRESULT ShellFeaturesEnabled([out, retval] VARIANT_BOOL *pVal);
    [id(6), propget] HRESULT SearchAssistantDefault([out, retval] VARIANT_BOOL *pVal);
    [id(7), propget] HRESULT Searches([out, retval] ISearches **ppid);
    [id(8), propget] HRESULT InWebFolder([out, retval] VARIANT_BOOL *pVal);

    [id(9)] HRESULT PutProperty(
            [in] VARIANT_BOOL bPerLocale,
            [in] BSTR bstrName,
            [in] BSTR bstrValue);

    [id(10)] HRESULT GetProperty(
            [in] VARIANT_BOOL bPerLocale,
            [in] BSTR bstrName,
            [out, retval] BSTR *pbstrValue);

    [id(11), propput] HRESULT EventHandled([in] VARIANT_BOOL rhs);
    [id(12)] HRESULT ResetNextMenu();
    [id(13)] HRESULT FindOnWeb();
    [id(14)] HRESULT FindFilesOrFolders();
    [id(15)] HRESULT FindComputer();
    [id(16)] HRESULT FindPrinter();
    [id(17)] HRESULT FindPeople();

    [id(18)] HRESULT GetSearchAssistantURL(
            [in] VARIANT_BOOL bSubstitute,
            [in] VARIANT_BOOL bCustomize,
            [out, retval] BSTR *pbstrValue);

    [id(19)] HRESULT NotifySearchSettingsChanged();

    [id(20), propput] HRESULT ASProvider([in] BSTR pProvider);
    [id(20), propget] HRESULT ASProvider([out, retval] BSTR *pProvider);

    [id(21), propput] HRESULT ASSetting([in] int pSetting);
    [id(21), propget] HRESULT ASSetting([out, retval] int *pSetting);

    [id(22)] HRESULT NETDetectNextNavigate();
    [id(23)] HRESULT PutFindText([in] BSTR FindText);
    [id(24), propget] HRESULT Version([out, retval] int *pVersion);

    [id(25)] HRESULT EncodeString(
            [in] BSTR bstrValue,
            [in] BSTR bstrCharSet,
            [in] VARIANT_BOOL bUseUTF8,
            [out, retval] BSTR* pbstrResult);
}

[
    odl,
    uuid(72423e8f-8011-11d2-be79-00a0c9a83da2),
    hidden,
    dual,
    oleautomation
]
interface ISearchAssistantOC2 : ISearchAssistantOC {
    [id(26), propget] HRESULT ShowFindPrinter([out, retval] VARIANT_BOOL *pbShowFindPrinter);
}

[
    odl,
    uuid(72423e8f-8011-11d2-be79-00a0c9a83da3),
    hidden,
    dual,
    oleautomation
]
interface ISearchAssistantOC3 : ISearchAssistantOC2 {
    [id(27), propget] HRESULT SearchCompanionAvailable([out, retval] VARIANT_BOOL *pbAvailable);

    [id(28), propput] HRESULT UseSearchCompanion([in] VARIANT_BOOL pbUseSC);
    [id(28), propget] HRESULT UseSearchCompanion([out, retval] VARIANT_BOOL *pbUseSC);
}

[
    uuid(1611fdda-445b-11d2-85de-00C04fa35c89),
    hidden
]
dispinterface _SearchAssistantEvents {
    properties:
    methods:
    [id(1)] void OnNextMenuSelect([in] long idItem);
    [id(2)] void OnNewSearch();
}

[
    helpstring("SearchAssistantOC"),
    threading(apartment),
    progid("SearchAssistantOC.SearchAssistantOC.1"),
    vi_progid("SearchAssistantOC.SearchAssistantOC"),
    uuid(2e71fd0f-aab1-42c0-9146-6d2c4edcf07d),
    hidden
]
coclass ShellSearchAssistantOC {
    [default] interface ISearchAssistantOC3;
    [default, source] dispinterface _SearchAssistantEvents;
}

[
    threading(apartment),
    progid("SearchAssistantOC.SearchAssistantOC.1"),
    vi_progid("SearchAssistantOC.SearchAssistantOC"),
    uuid(b45ff030-4447-11d2-85de-00C04fa35c89),
    hidden
]
coclass SearchAssistantOC {
    [default] interface ISearchAssistantOC3;
    [default, source] dispinterface _SearchAssistantEvents;
}

} /* library */
