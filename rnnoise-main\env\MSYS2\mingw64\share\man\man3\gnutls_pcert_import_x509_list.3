.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pcert_import_x509_list" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pcert_import_x509_list \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pcert_import_x509_list(gnutls_pcert_st * " pcert_list ", gnutls_x509_crt_t * " crt ", unsigned * " ncrt ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pcert_st * pcert_list" 12
The structures to store the certificates; must not contain initialized \fBgnutls_pcert_st\fP structures.
.IP "gnutls_x509_crt_t * crt" 12
The certificates to be imported
.IP "unsigned * ncrt" 12
The number of certificates in  \fIcrt\fP ; will be updated if necessary
.IP "unsigned int flags" 12
zero or \fBGNUTLS_X509_CRT_LIST_SORT\fP
.SH "DESCRIPTION"
This convenience function will import the given certificates to an
already allocated set of \fBgnutls_pcert_st\fP structures. The structures must
be deinitialized afterwards using \fBgnutls_pcert_deinit()\fP.  \fIpcert_list\fP should contain space for at least  \fIncrt\fP elements.

In the case \fBGNUTLS_X509_CRT_LIST_SORT\fP is specified and that
function cannot sort the list, \fBGNUTLS_E_CERTIFICATE_LIST_UNSORTED\fP
will be returned. Currently sorting can fail if the list size
exceeds an internal constraint (16).
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
