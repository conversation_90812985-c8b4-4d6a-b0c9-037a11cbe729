CMAKE_<LANG>_EXTENSIONS
-----------------------

The variations are:

* :variable:`CMAKE_C_EXTENSIONS`
* :variable:`CMAKE_CXX_EXTENSIONS`
* :variable:`<PERSON>MAKE_CUDA_EXTENSIONS`
* :variable:`CMAKE_HIP_EXTENSIONS`
* :variable:`CMAKE_OBJC_EXTENSIONS`
* :variable:`CMAKE_OBJCXX_EXTENSIONS`

Default values for :prop_tgt:`<LANG>_EXTENSIONS` target properties if set when
a target is created.  For the compiler's default setting see
:variable:`CMAKE_<LANG>_EXTENSIONS_DEFAULT`.

For supported CMake versions see the respective pages.

See the :manual:`cmake-compile-features(7)` manual for information on
compile features and a list of supported compilers.
