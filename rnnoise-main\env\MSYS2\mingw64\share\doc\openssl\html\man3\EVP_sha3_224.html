<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_sha3_224</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_sha3_224, EVP_sha3_256, EVP_sha3_384, EVP_sha3_512, EVP_shake128, EVP_shake256 - SHA-3 For EVP</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const EVP_MD *EVP_sha3_224(void);
const EVP_MD *EVP_sha3_256(void);
const EVP_MD *EVP_sha3_384(void);
const EVP_MD *EVP_sha3_512(void);

const EVP_MD *EVP_shake128(void);
const EVP_MD *EVP_shake256(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SHA-3 (Secure Hash Algorithm 3) is a family of cryptographic hash functions standardized in NIST FIPS 202, first published in 2015. It is based on the Keccak algorithm.</p>

<dl>

<dt id="EVP_sha3_224-EVP_sha3_256-EVP_sha3_384-EVP_sha3_512">EVP_sha3_224(), EVP_sha3_256(), EVP_sha3_384(), EVP_sha3_512()</dt>
<dd>

<p>The SHA-3 SHA-3-224, SHA-3-256, SHA-3-384, and SHA-3-512 algorithms respectively. They produce 224, 256, 384 and 512 bits of output from a given input.</p>

</dd>
<dt id="EVP_shake128-EVP_shake256">EVP_shake128(), EVP_shake256()</dt>
<dd>

<p>The SHAKE-128 and SHAKE-256 Extendable Output Functions (XOF) that can generate a variable hash length.</p>

<p>Specifically, <b>EVP_shake128</b> provides an overall security of 128 bits, while <b>EVP_shake256</b> provides that of 256 bits.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>Developers should be aware of the negative performance implications of calling these functions multiple times and should consider using <a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a> with <a href="../man7/EVP_MD-SHA3.html">EVP_MD-SHA3(7)</a> or <a href="../man7/EVP_MD-SHAKE.html">EVP_MD-SHAKE(7)</a> instead. See <a href="../man7/crypto.html">&quot;Performance&quot; in crypto(7)</a> for further information.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return a <b>EVP_MD</b> structure that contains the implementation of the message digest. See <a href="../man3/EVP_MD_meth_new.html">EVP_MD_meth_new(3)</a> for details of the <b>EVP_MD</b> structure.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>NIST FIPS 202.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


