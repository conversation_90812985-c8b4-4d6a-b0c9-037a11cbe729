<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CTLOG_STORE_get0_log_by_id</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>CTLOG_STORE_get0_log_by_id - Get a Certificate Transparency log from a CTLOG_STORE</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ct.h&gt;

const CTLOG *CTLOG_STORE_get0_log_by_id(const CTLOG_STORE *store,
                                        const uint8_t *log_id,
                                        size_t log_id_len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>A Signed Certificate Timestamp (SCT) identifies the Certificate Transparency (CT) log that issued it using the log&#39;s LogID (see RFC 6962, Section 3.2). Therefore, it is useful to be able to look up more information about a log (e.g. its public key) using this LogID.</p>

<p>CTLOG_STORE_get0_log_by_id() provides a way to do this. It will find a CTLOG in a CTLOG_STORE that has a given LogID.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p><b>CTLOG_STORE_get0_log_by_id</b> returns a CTLOG with the given LogID, if it exists in the given CTLOG_STORE, otherwise it returns NULL.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ct.html">ct(7)</a>, <a href="../man3/CTLOG_STORE_new.html">CTLOG_STORE_new(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The CTLOG_STORE_get0_log_by_id() function was added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


