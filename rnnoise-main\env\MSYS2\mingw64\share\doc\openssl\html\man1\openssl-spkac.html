<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-spkac</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-spkac - SPKAC printing and generating command</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>spkac</b> [<b>-help</b>] [<b>-in</b> <i>filename</i>] [<b>-out</b> <i>filename</i>] [<b>-digest</b> <i>digest</i>] [<b>-key</b> <i>filename</i>|<i>uri</i>] [<b>-keyform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b>] [<b>-passin</b> <i>arg</i>] [<b>-challenge</b> <i>string</i>] [<b>-pubkey</b>] [<b>-spkac</b> <i>spkacname</i>] [<b>-spksect</b> <i>section</i>] [<b>-noout</b>] [<b>-verify</b>] [<b>-engine</b> <i>id</i>] [<b>-provider</b> <i>name</i>] [<b>-provider-path</b> <i>path</i>] [<b>-provparam</b> <i>[name:]key=value</i>] [<b>-propquery</b> <i>propq</i>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command processes Netscape signed public key and challenge (SPKAC) files. It can print out their contents, verify the signature and produce its own SPKACs from a supplied private key.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="in-filename"><b>-in</b> <i>filename</i></dt>
<dd>

<p>This specifies the input filename to read from or standard input if this option is not specified. Ignored if the <b>-key</b> option is used.</p>

</dd>
<dt id="out-filename"><b>-out</b> <i>filename</i></dt>
<dd>

<p>Specifies the output filename to write to or standard output by default.</p>

</dd>
<dt id="digest-digest"><b>-digest</b> <i>digest</i></dt>
<dd>

<p>Use the specified <i>digest</i> to sign a created SPKAC file. The default digest algorithm is MD5.</p>

</dd>
<dt id="key-filename-uri"><b>-key</b> <i>filename</i>|<i>uri</i></dt>
<dd>

<p>Create an SPKAC file using the private key specified by <i>filename</i> or <i>uri</i>. The <b>-in</b>, <b>-noout</b>, <b>-spksect</b> and <b>-verify</b> options are ignored if present.</p>

</dd>
<dt id="keyform-DER-PEM-P12-ENGINE"><b>-keyform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b></dt>
<dd>

<p>The key format; unspecified by default. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="passin-arg"><b>-passin</b> <i>arg</i></dt>
<dd>

<p>The input file password source. For more information about the format of <i>arg</i> see <a href="../man1/openssl-passphrase-options.html">openssl-passphrase-options(1)</a>.</p>

</dd>
<dt id="challenge-string"><b>-challenge</b> <i>string</i></dt>
<dd>

<p>Specifies the challenge string if an SPKAC is being created.</p>

</dd>
<dt id="spkac-spkacname"><b>-spkac</b> <i>spkacname</i></dt>
<dd>

<p>Allows an alternative name form the variable containing the SPKAC. The default is &quot;SPKAC&quot;. This option affects both generated and input SPKAC files.</p>

</dd>
<dt id="spksect-section"><b>-spksect</b> <i>section</i></dt>
<dd>

<p>Allows an alternative name form the section containing the SPKAC. The default is the default section.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>Don&#39;t output the text version of the SPKAC (not used if an SPKAC is being created).</p>

</dd>
<dt id="pubkey"><b>-pubkey</b></dt>
<dd>

<p>Output the public key of an SPKAC (not used if an SPKAC is being created).</p>

</dd>
<dt id="verify"><b>-verify</b></dt>
<dd>

<p>Verifies the digital signature on the supplied SPKAC.</p>

</dd>
<dt id="engine-id"><b>-engine</b> <i>id</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Engine Options&quot; in openssl(1)</a>. This option is deprecated.</p>

</dd>
<dt id="provider-name"><b>-provider</b> <i>name</i></dt>
<dd>

</dd>
<dt id="provider-path-path"><b>-provider-path</b> <i>path</i></dt>
<dd>

</dd>
<dt id="provparam-name:-key-value"><b>-provparam</b> <i>[name:]key=value</i></dt>
<dd>

</dd>
<dt id="propquery-propq"><b>-propquery</b> <i>propq</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Provider Options&quot; in openssl(1)</a>, <a href="../man7/provider.html">provider(7)</a>, and <a href="../man7/property.html">property(7)</a>.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Print out the contents of an SPKAC:</p>

<pre><code>openssl spkac -in spkac.cnf</code></pre>

<p>Verify the signature of an SPKAC:</p>

<pre><code>openssl spkac -in spkac.cnf -noout -verify</code></pre>

<p>Create an SPKAC using the challenge string &quot;hello&quot;:</p>

<pre><code>openssl spkac -key key.pem -challenge hello -out spkac.cnf</code></pre>

<p>Example of an SPKAC, (long lines split up for clarity):</p>

<pre><code>SPKAC=MIG5MGUwXDANBgkqhkiG9w0BAQEFAANLADBIAkEA\
1cCoq2Wa3Ixs47uI7FPVwHVIPDx5yso105Y6zpozam135a\
8R0CpoRvkkigIyXfcCjiVi5oWk+6FfPaD03uPFoQIDAQAB\
FgVoZWxsbzANBgkqhkiG9w0BAQQFAANBAFpQtY/FojdwkJ\
h1bEIYuc2EeM2KHTWPEepWYeawvHD0gQ3DngSC75YCWnnD\
dq+NQ3F+X4deMx9AaEglZtULwV4=</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>A created SPKAC with suitable DN components appended can be fed to <a href="../man1/openssl-ca.html">openssl-ca(1)</a>.</p>

<p>SPKACs are typically generated by Netscape when a form is submitted containing the <b>KEYGEN</b> tag as part of the certificate enrollment process.</p>

<p>The challenge string permits a primitive form of proof of possession of private key. By checking the SPKAC signature and a random challenge string some guarantee is given that the user knows the private key corresponding to the public key being certified. This is important in some applications. Without this it is possible for a previous SPKAC to be used in a &quot;replay attack&quot;.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man1/openssl-ca.html">openssl-ca(1)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>-engine</b> option was deprecated in OpenSSL 3.0.</p>

<p>The <b>-digest</b> option was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


