/* Copyright (C) 2001-2025 Free Software Foundation, Inc.
   Contributed by <PERSON> <<EMAIL>>.

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

/* This header provides a declarative way of describing the attributes
   that are applied to some built-in functions by default.  Attributes
   that are meant to be used by user-defined functions but aren't used
   by any built-ins, or attributes that apply to types or variables
   but not to functions need not and should not be defined here.

   Before including this header, you must define the following macros.
   In each case where there is an ENUM, it is an identifier used to
   reference the tree in subsequent definitions.

   DEF_ATTR_NULL_TREE (ENUM)

     Constructs a NULL_TREE.

   DEF_ATTR_INT (ENUM, VALUE)

     Constructs an INTEGER_CST with value VALUE (an integer representable
     in HOST_WIDE_INT).

   DEF_ATTR_IDENT (ENUM, STRING)

     Constructs an IDENTIFIER_NODE for STRING.

   DEF_ATTR_TREE_LIST (ENUM, PURPOSE, VALUE, CHAIN)

     Constructs a TREE_LIST with given PURPOSE, VALUE and CHAIN (given
     as previous ENUM names).  */

DEF_ATTR_NULL_TREE (ATTR_NULL)

/* Construct a tree for a given integer and a list containing it.  */
#define DEF_ATTR_FOR_INT(VALUE)					\
  DEF_ATTR_INT (ATTR_##VALUE, VALUE)			\
  DEF_ATTR_TREE_LIST (ATTR_LIST_##VALUE, ATTR_NULL,	\
		      ATTR_##VALUE, ATTR_NULL)
DEF_ATTR_FOR_INT (0)
DEF_ATTR_FOR_INT (1)
DEF_ATTR_FOR_INT (2)
DEF_ATTR_FOR_INT (3)
DEF_ATTR_FOR_INT (4)
DEF_ATTR_FOR_INT (5)
DEF_ATTR_FOR_INT (6)
#undef DEF_ATTR_FOR_INT

/* Construct a tree for a given string and a list containing it.  */
#define DEF_ATTR_FOR_STRING(ENUM, VALUE)					\
  DEF_ATTR_STRING (ATTR_##ENUM, VALUE)			\
  DEF_ATTR_TREE_LIST (ATTR_LIST_##ENUM, ATTR_NULL,	\
		      ATTR_##ENUM, ATTR_NULL)
DEF_ATTR_FOR_STRING (STR1, "1 ")
DEF_ATTR_FOR_STRING (STRERRNOC, ".C")
DEF_ATTR_FOR_STRING (STRERRNOP, ".P")
#undef DEF_ATTR_FOR_STRING

/* Construct a tree for a list of two integers.  */
#define DEF_LIST_INT_INT(VALUE1, VALUE2)				 \
  DEF_ATTR_TREE_LIST (ATTR_LIST_##VALUE1##_##VALUE2, ATTR_NULL,		 \
		      ATTR_##VALUE1, ATTR_LIST_##VALUE2)
DEF_LIST_INT_INT (1,0)
DEF_LIST_INT_INT (1,2)
DEF_LIST_INT_INT (1,3)
DEF_LIST_INT_INT (1,4)
DEF_LIST_INT_INT (1,5)
DEF_LIST_INT_INT (2,0)
DEF_LIST_INT_INT (2,3)
DEF_LIST_INT_INT (3,0)
DEF_LIST_INT_INT (3,4)
DEF_LIST_INT_INT (4,0)
DEF_LIST_INT_INT (4,5)
DEF_LIST_INT_INT (5,0)
DEF_LIST_INT_INT (5,6)
#undef DEF_LIST_INT_INT

/* Construct trees for identifiers used in built-in function attributes.
   The construction contributes to startup costs so only attributes that
   are used to define built-ins should be defined here.  */
DEF_ATTR_IDENT (ATTR_ALLOC_SIZE, "alloc_size")
DEF_ATTR_IDENT (ATTR_COLD, "cold")
DEF_ATTR_IDENT (ATTR_CONST, "const")
DEF_ATTR_IDENT (ATTR_FORMAT, "format")
DEF_ATTR_IDENT (ATTR_FORMAT_ARG, "format_arg")
DEF_ATTR_IDENT (ATTR_MALLOC, "malloc")
DEF_ATTR_IDENT (ATTR_NONNULL, "nonnull")
DEF_ATTR_IDENT (ATTR_NONNULL_IF_NONZERO, "nonnull_if_nonzero")
DEF_ATTR_IDENT (ATTR_NORETURN, "noreturn")
DEF_ATTR_IDENT (ATTR_NOTHROW, "nothrow")
DEF_ATTR_IDENT (ATTR_LEAF, "leaf")
DEF_ATTR_IDENT (ATTR_FNSPEC, "fn spec")
DEF_ATTR_IDENT (ATTR_PRINTF, "printf")
DEF_ATTR_IDENT (ATTR_ASM_FPRINTF, "asm_fprintf")
DEF_ATTR_IDENT (ATTR_GCC_DIAG, "gcc_diag")
DEF_ATTR_IDENT (ATTR_GCC_CDIAG, "gcc_cdiag")
DEF_ATTR_IDENT (ATTR_GCC_CXXDIAG, "gcc_cxxdiag")
DEF_ATTR_IDENT (ATTR_PURE, "pure")
DEF_ATTR_IDENT (ATTR_NOVOPS, "no vops")
DEF_ATTR_IDENT (ATTR_SCANF, "scanf")
DEF_ATTR_IDENT (ATTR_SENTINEL, "sentinel")
DEF_ATTR_IDENT (ATTR_STRFMON, "strfmon")
DEF_ATTR_IDENT (ATTR_STRFTIME, "strftime")
DEF_ATTR_IDENT (ATTR_TYPEGENERIC, "type generic")
DEF_ATTR_IDENT (ATTR_TM_REGPARM, "*tm regparm")
DEF_ATTR_IDENT (ATTR_TM_TMPURE, "transaction_pure")
DEF_ATTR_IDENT (ATTR_RETURNS_TWICE, "returns_twice")
DEF_ATTR_IDENT (ATTR_RETURNS_NONNULL, "returns_nonnull")
DEF_ATTR_IDENT (ATTR_WARN_UNUSED_RESULT, "warn_unused_result")

DEF_ATTR_TREE_LIST (ATTR_NOVOPS_LIST, ATTR_NOVOPS, ATTR_NULL, ATTR_NULL)

DEF_ATTR_TREE_LIST (ATTR_NOVOPS_LEAF_LIST, ATTR_LEAF, ATTR_NULL, ATTR_NOVOPS_LIST)

DEF_ATTR_TREE_LIST (ATTR_LEAF_LIST, ATTR_LEAF, ATTR_NULL, ATTR_NULL)

DEF_ATTR_TREE_LIST (ATTR_NOTHROW_LIST, ATTR_NOTHROW, ATTR_NULL, ATTR_NULL)

DEF_ATTR_TREE_LIST (ATTR_NOTHROW_LEAF_LIST, ATTR_LEAF, ATTR_NULL, ATTR_NOTHROW_LIST)

DEF_ATTR_TREE_LIST (ATTR_NOVOPS_NOTHROW_LEAF_LIST, ATTR_NOVOPS, \
		        ATTR_NULL, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_CONST_NOTHROW_LIST, ATTR_CONST,	\
			ATTR_NULL, ATTR_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_CONST_NOTHROW_LEAF_LIST, ATTR_CONST,	\
			ATTR_NULL, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_ERRNOCONST_NOTHROW_LEAF_LIST, ATTR_FNSPEC,\
			ATTR_LIST_STRERRNOC, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_ERRNOPURE_NOTHROW_LEAF_LIST, ATTR_FNSPEC,\
			ATTR_LIST_STRERRNOP, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_PURE_NOTHROW_LIST, ATTR_PURE,		\
			ATTR_NULL, ATTR_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_PURE_NOTHROW_LEAF_LIST, ATTR_PURE,	\
			ATTR_NULL, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_NORETURN_NOTHROW_LIST, ATTR_NORETURN,	\
			ATTR_NULL, ATTR_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_NORETURN_NOTHROW_LEAF_LIST, ATTR_NORETURN,\
			ATTR_NULL, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_NORETURN_NOTHROW_LEAF_COLD_LIST, ATTR_COLD,\
			ATTR_NULL, ATTR_NORETURN_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_RT_NOTHROW_LEAF_LIST, ATTR_RETURNS_TWICE,\
			ATTR_NULL, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_COLD_NOTHROW_LEAF_LIST, ATTR_COLD,\
			ATTR_NULL, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_COLD_NORETURN_NOTHROW_LEAF_LIST, ATTR_COLD,\
			ATTR_NULL, ATTR_NORETURN_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_CONST_NORETURN_NOTHROW_LEAF_LIST, ATTR_CONST,\
			ATTR_NULL, ATTR_NORETURN_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_CONST_NORETURN_NOTHROW_LEAF_COLD_LIST, ATTR_COLD,\
			ATTR_NULL, ATTR_CONST_NORETURN_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_MALLOC_NOTHROW_LIST, ATTR_MALLOC,	\
			ATTR_NULL, ATTR_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_WARN_UNUSED_RESULT_NOTHROW_LEAF_LIST, ATTR_WARN_UNUSED_RESULT,	\
			ATTR_NULL, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_MALLOC_WARN_UNUSED_RESULT_NOTHROW_LEAF_LIST, ATTR_MALLOC,	\
			ATTR_NULL, ATTR_WARN_UNUSED_RESULT_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_SENTINEL_NOTHROW_LIST, ATTR_SENTINEL,	\
			ATTR_NULL, ATTR_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_SENTINEL_NOTHROW_LEAF_LIST, ATTR_SENTINEL,	\
			ATTR_NULL, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_COLD_CONST_NORETURN_NOTHROW_LEAF_LIST, ATTR_CONST,\
			ATTR_NULL, ATTR_COLD_NORETURN_NOTHROW_LEAF_LIST)

/* Allocation functions like malloc and realloc whose first argument
   with _SIZE_1, or second argument with _SIZE_2, specifies the size
   of the allocated object.  */
DEF_ATTR_TREE_LIST (ATTR_MALLOC_SIZE_1_NOTHROW_LIST, ATTR_ALLOC_SIZE,	\
		    ATTR_LIST_1, ATTR_MALLOC_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_MALLOC_WARN_UNUSED_RESULT_NOTHROW_LIST, ATTR_WARN_UNUSED_RESULT,	\
		    ATTR_NULL, ATTR_MALLOC_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_ALLOC_WARN_UNUSED_RESULT_SIZE_2_NOTHROW_LIST, ATTR_ALLOC_SIZE,	\
		    ATTR_LIST_2, ATTR_MALLOC_WARN_UNUSED_RESULT_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_MALLOC_WARN_UNUSED_RESULT_SIZE_1_NOTHROW_LEAF_LIST, ATTR_ALLOC_SIZE, \
		    ATTR_LIST_1, ATTR_MALLOC_WARN_UNUSED_RESULT_NOTHROW_LEAF_LIST)
/* Alloca is just like malloc except that it never returns null.  */
DEF_ATTR_TREE_LIST (ATTR_ALLOCA_WARN_UNUSED_RESULT_SIZE_1_NOTHROW_LEAF_LIST, ATTR_RETURNS_NONNULL,
		    ATTR_NULL, ATTR_MALLOC_WARN_UNUSED_RESULT_SIZE_1_NOTHROW_LEAF_LIST)

/* Allocation functions like calloc the product of whose first two arguments
   specifies the size of the allocated object.  */
DEF_ATTR_TREE_LIST (ATTR_MALLOC_WARN_UNUSED_RESULT_SIZE_1_2_NOTHROW_LEAF_LIST, ATTR_ALLOC_SIZE, \
		    ATTR_LIST_1_2, ATTR_MALLOC_WARN_UNUSED_RESULT_NOTHROW_LEAF_LIST)

/* Allocation functions like realloc whose second argument specifies
   the size of the allocated object.  */
DEF_ATTR_TREE_LIST (ATTR_ALLOC_WARN_UNUSED_RESULT_SIZE_2_NOTHROW_LEAF_LIST, ATTR_ALLOC_SIZE, \
		    ATTR_LIST_2, ATTR_WARN_UNUSED_RESULT_NOTHROW_LEAF_LIST)

/* Functions whose pointer parameter(s) are all nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_NONNULL_LIST, ATTR_NONNULL, ATTR_NULL, ATTR_NULL)
/* Functions whose first parameter is a nonnull pointer.  */
DEF_ATTR_TREE_LIST (ATTR_NONNULL_1, ATTR_NONNULL, ATTR_LIST_1, ATTR_NULL)
/* Functions whose second parameter is a nonnull pointer.  */
DEF_ATTR_TREE_LIST (ATTR_NONNULL_2, ATTR_NONNULL, ATTR_LIST_2, ATTR_NULL)
/* Functions whose third parameter is a nonnull pointer.  */
DEF_ATTR_TREE_LIST (ATTR_NONNULL_3, ATTR_NONNULL, ATTR_LIST_3, ATTR_NULL)
/* Nothrow functions with the sentinel(1) attribute. */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_SENTINEL_1, ATTR_SENTINEL, ATTR_LIST_1, \
			ATTR_NOTHROW_LIST)
/* Nothrow functions whose pointer parameter(s) are all nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL, ATTR_NONNULL, ATTR_NULL, \
			ATTR_NOTHROW_LIST)
/* Nothrow leaf functions whose pointer parameter(s) are all nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_LEAF, ATTR_NONNULL, ATTR_NULL, \
			ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_LEAF_LIST, ATTR_LEAF, ATTR_NULL, ATTR_NOTHROW_NONNULL_LEAF)
/* Nothrow functions whose first parameter is a nonnull pointer.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_1, ATTR_NONNULL, ATTR_LIST_1, \
			ATTR_NOTHROW_LIST)
/* Nothrow functions whose second parameter is a nonnull pointer.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_2, ATTR_NONNULL, ATTR_LIST_2, \
			ATTR_NOTHROW_LIST)
/* Nothrow functions whose third parameter is a nonnull pointer.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_3, ATTR_NONNULL, ATTR_LIST_3, \
			ATTR_NOTHROW_LIST)
/* Nothrow functions whose fourth parameter is a nonnull pointer.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_4, ATTR_NONNULL, ATTR_LIST_4, \
			ATTR_NOTHROW_LIST)
/* Nothrow functions whose fifth parameter is a nonnull pointer.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_5, ATTR_NONNULL, ATTR_LIST_5, \
			ATTR_NOTHROW_LIST)
/* Nothrow leaf functions whose selected pointer parameter(s) are conditionally
   nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_IF12_LEAF, ATTR_NONNULL_IF_NONZERO, \
			ATTR_LIST_1_2, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_IF13_LEAF, ATTR_NONNULL_IF_NONZERO, \
			ATTR_LIST_1_3, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_IF123_LEAF, ATTR_NONNULL_IF_NONZERO, \
			ATTR_LIST_2_3, ATTR_NOTHROW_NONNULL_IF13_LEAF)
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_IF23_LEAF, ATTR_NONNULL_IF_NONZERO, \
			ATTR_LIST_2_3, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_1_IF23_LEAF, ATTR_NONNULL, \
			ATTR_LIST_1, ATTR_NOTHROW_NONNULL_IF23_LEAF)

/* Same as ATTR_NONNULL_1.  */
DEF_ATTR_TREE_LIST (ATTR_NONNULL_1_1, ATTR_NONNULL, ATTR_LIST_1, ATTR_NULL)
/* Functions like {v,}fprintf whose first and second parameters are
   nonnull pointers.  As cancellation points the functions are not
   nothrow.  */
DEF_ATTR_TREE_LIST (ATTR_NONNULL_1_2, ATTR_NONNULL, ATTR_LIST_1_2, ATTR_NULL)
/* The following don't have {v,}fprintf forms.  They exist only to
   make it possible to declare {v,}{f,s}printf attributes using
   the same macro.  */
DEF_ATTR_TREE_LIST (ATTR_NONNULL_1_3, ATTR_NONNULL, ATTR_LIST_1_3, ATTR_NULL)
DEF_ATTR_TREE_LIST (ATTR_NONNULL_1_4, ATTR_NONNULL, ATTR_LIST_1_4, ATTR_NULL)
DEF_ATTR_TREE_LIST (ATTR_NONNULL_1_5, ATTR_NONNULL, ATTR_LIST_1_5, ATTR_NULL)

/* Same as ATTR_NOTHROW_NONNULL_1.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_1_1, ATTR_NONNULL, ATTR_LIST_1,
		    ATTR_NOTHROW_LIST)
/* Nothrow functions like {v,}sprintf whose first and second parameters
   are nonnull pointers.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_1_2, ATTR_NONNULL, ATTR_LIST_1_2, \
		    ATTR_NOTHROW_LIST)
/* Nothrow functions like {v,}snprintf whose first and third parameters
   are nonnull pointers.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_1_3, ATTR_NONNULL, ATTR_LIST_1_3, \
		    ATTR_NOTHROW_LIST)
/* Nothrow functions like {v,}sprintf_chk whose first and fourth parameters
   are nonnull pointers.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_1_4, ATTR_NONNULL, ATTR_LIST_1_4, \
		    ATTR_NOTHROW_LIST)
/* Nothrow functions like {v,}snprintf_chk whose first and fifth parameters
   are nonnull pointers.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_1_5, ATTR_NONNULL, ATTR_LIST_1_5, \
		    ATTR_NOTHROW_LIST)

/* Nothrow leaf functions which are type-generic.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_TYPEGENERIC_LEAF, ATTR_TYPEGENERIC, ATTR_NULL, \
			ATTR_NOTHROW_LEAF_LIST)
/* Nothrow nonnull leaf functions that are type-generic.  */
DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_TYPEGENERIC_LEAF,
		    ATTR_TYPEGENERIC, ATTR_NULL,
		    ATTR_NOTHROW_NONNULL_LEAF)
/* Nothrow const functions whose pointer parameter(s) are all nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_CONST_NOTHROW_NONNULL, ATTR_CONST, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL)
/* Nothrow leaf functions whose pointer parameter(s) are all nonnull,
   and which return their first argument.  */
DEF_ATTR_TREE_LIST (ATTR_RET1_NOTHROW_NONNULL_LEAF, ATTR_FNSPEC, ATTR_LIST_STR1, \
			ATTR_NOTHROW_NONNULL_LEAF)
/* Nothrow leaf functions whose pointer parameter(s) are all nonnull,
   and return value is also nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_RETNONNULL_NOTHROW_LEAF, ATTR_RETURNS_NONNULL, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL_LEAF)
/* Nothrow const leaf functions whose pointer parameter(s) are all nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_CONST_NOTHROW_NONNULL_LEAF, ATTR_CONST, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL_LEAF)
/* Nothrow const functions which are type-generic.  */
DEF_ATTR_TREE_LIST (ATTR_CONST_NOTHROW_TYPEGENERIC, ATTR_TYPEGENERIC, ATTR_NULL, \
			ATTR_CONST_NOTHROW_LIST)
/* Nothrow const leaf functions which are type-generic.  */
DEF_ATTR_TREE_LIST (ATTR_CONST_NOTHROW_TYPEGENERIC_LEAF, ATTR_TYPEGENERIC, ATTR_NULL, \
			ATTR_CONST_NOTHROW_LEAF_LIST)
/* Nothrow pure functions whose pointer parameter(s) are all nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_PURE_NOTHROW_NONNULL, ATTR_PURE, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL)
/* Nothrow pure leaf functions whose pointer parameter(s) are all nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_PURE_NOTHROW_NONNULL_LEAF, ATTR_PURE, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL_LEAF)
/* Nothrow pure leaf functions whose selected pointer parameter(s) are
   conditionally nonnull. */
DEF_ATTR_TREE_LIST (ATTR_PURE_NOTHROW_NONNULL_IF12_LEAF, ATTR_PURE, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL_IF12_LEAF)
DEF_ATTR_TREE_LIST (ATTR_PURE_NOTHROW_NONNULL_IF13_LEAF, ATTR_PURE, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL_IF13_LEAF)
DEF_ATTR_TREE_LIST (ATTR_PURE_NOTHROW_NONNULL_IF123_LEAF, ATTR_PURE, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL_IF123_LEAF)
/* Nothrow malloc functions whose pointer parameter(s) are all nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_WARN_UNUSED_RESULT_NOTHROW_NONNULL, ATTR_WARN_UNUSED_RESULT, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL)
DEF_ATTR_TREE_LIST (ATTR_MALLOC_WARN_UNUSED_RESULT_NOTHROW_NONNULL, ATTR_MALLOC, ATTR_NULL, \
			ATTR_WARN_UNUSED_RESULT_NOTHROW_NONNULL)
/* Nothrow malloc leaf functions whose pointer parameter(s) are all nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_WARN_UNUSED_RESULT_NOTHROW_NONNULL_LEAF, ATTR_WARN_UNUSED_RESULT, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL_LEAF)
DEF_ATTR_TREE_LIST (ATTR_MALLOC_WARN_UNUSED_RESULT_NOTHROW_NONNULL_LEAF, ATTR_MALLOC, ATTR_NULL, \
			ATTR_WARN_UNUSED_RESULT_NOTHROW_NONNULL_LEAF)
/* Nothrow malloc leaf functions whose selected pointer parameter(s) are
   conditionally nonnull.  */
DEF_ATTR_TREE_LIST (ATTR_WARN_UNUSED_RESULT_NOTHROW_NONNULL_IF12_LEAF, \
			ATTR_WARN_UNUSED_RESULT, ATTR_NULL, \
			ATTR_NOTHROW_NONNULL_IF12_LEAF)
DEF_ATTR_TREE_LIST (ATTR_MALLOC_WARN_UNUSED_RESULT_NOTHROW_NONNULL_IF12_LEAF, \
			ATTR_MALLOC, ATTR_NULL, \
			ATTR_WARN_UNUSED_RESULT_NOTHROW_NONNULL_IF12_LEAF)

/* Construct a tree for the format attribute (and implicitly nonnull).  */
#define DEF_FORMAT_ATTRIBUTE(TYPE, FA, VALUES)				 \
  DEF_ATTR_TREE_LIST (ATTR_##TYPE##_##VALUES, ATTR_NULL,		 \
		      ATTR_##TYPE, ATTR_LIST_##VALUES)			 \
  DEF_ATTR_TREE_LIST (ATTR_FORMAT_##TYPE##_##VALUES, ATTR_FORMAT,	 \
		      ATTR_##TYPE##_##VALUES, ATTR_NONNULL_##FA)

/* Construct a tree for the format and nothrow attributes (format
   implies nonnull).  */
#define DEF_FORMAT_ATTRIBUTE_NOTHROW(TYPE, FA, VALUES)			 \
  DEF_ATTR_TREE_LIST (ATTR_##TYPE##_##VALUES, ATTR_NULL,		 \
		      ATTR_##TYPE, ATTR_LIST_##VALUES)			 \
  DEF_ATTR_TREE_LIST (ATTR_FORMAT_##TYPE##_NOTHROW_##VALUES, ATTR_FORMAT,\
		      ATTR_##TYPE##_##VALUES, ATTR_NOTHROW_NONNULL_##FA)

/* Construct one tree for the format attribute and another for the format
   and nothrow attributes (in both cases format implies nonnull).  */
#define DEF_FORMAT_ATTRIBUTE_BOTH(TYPE, FA, VALUES)			 \
  DEF_ATTR_TREE_LIST (ATTR_##TYPE##_##VALUES, ATTR_NULL,		 \
		      ATTR_##TYPE, ATTR_LIST_##VALUES)			 \
  DEF_ATTR_TREE_LIST (ATTR_FORMAT_##TYPE##_##VALUES, ATTR_FORMAT,	 \
		      ATTR_##TYPE##_##VALUES, ATTR_NONNULL_##FA)	 \
  DEF_ATTR_TREE_LIST (ATTR_FORMAT_##TYPE##_NOTHROW_##VALUES, ATTR_FORMAT,\
		      ATTR_##TYPE##_##VALUES, ATTR_NOTHROW_NONNULL_##FA)

/* Construct a pair of trees for the nonnull attribute for the first
   argument, plus format printf attribute (format implies nonnull):
   the first ordinary and the second nothrow.  */
#define DEF_FORMAT_ATTRIBUTE_NONNULL(TYPE, FA, VALUES)			 \
  DEF_ATTR_TREE_LIST (ATTR_NONNULL_1_FORMAT_##TYPE##_##VALUES,   	 \
  		      ATTR_FORMAT, ATTR_##TYPE##_##VALUES, 	 	 \
		      ATTR_NONNULL_1_##FA)				 \
  DEF_ATTR_TREE_LIST (ATTR_NOTHROW_NONNULL_1_FORMAT_##TYPE##_##VALUES,   \
  		      ATTR_FORMAT, ATTR_##TYPE##_##VALUES, 	 	 \
		      ATTR_NOTHROW_NONNULL_1_##FA)

DEF_FORMAT_ATTRIBUTE(PRINTF,1,1_0)
DEF_FORMAT_ATTRIBUTE(PRINTF,1,1_2)
DEF_FORMAT_ATTRIBUTE_BOTH(PRINTF,2,2_0)
DEF_FORMAT_ATTRIBUTE_BOTH(PRINTF,2,2_3)
DEF_FORMAT_ATTRIBUTE_BOTH(PRINTF,3,3_0)
DEF_FORMAT_ATTRIBUTE_BOTH(PRINTF,3,3_4)
DEF_FORMAT_ATTRIBUTE_NOTHROW(PRINTF,4,4_0)
DEF_FORMAT_ATTRIBUTE_NOTHROW(PRINTF,4,4_5)
DEF_FORMAT_ATTRIBUTE_NOTHROW(PRINTF,5,5_0)
DEF_FORMAT_ATTRIBUTE_NOTHROW(PRINTF,5,5_6)

/* Attributes for fprintf(f, f, va).  */
DEF_FORMAT_ATTRIBUTE_NONNULL(PRINTF,1,1_2)
/* Attributes for v{f,s}printf(d, f, va).  vsprintf is nothrow, vfprintf
   is not.  */
DEF_FORMAT_ATTRIBUTE_NONNULL(PRINTF,2,2_0)
/* Attributes for {f,s}printf(d, f, ...).  sprintf is nothrow, fprintf
   is not.  */
DEF_FORMAT_ATTRIBUTE_NONNULL(PRINTF,2,2_3)
/* Attributes for vprintf_chk.  */
DEF_FORMAT_ATTRIBUTE_NONNULL(PRINTF,3,3_0)
/* Attributes for printf_chk.  */
DEF_FORMAT_ATTRIBUTE_NONNULL(PRINTF,3,3_4)
/* Attributes for v{f,s}printf_chk(d, t, bos, f, va).  vsprintf_chk is
   nothrow, vfprintf_chk is not.  */
DEF_FORMAT_ATTRIBUTE_NONNULL(PRINTF,4,4_0)
/* Attributes for {f,s}printf_chk(d, t, bos, f, ...).  sprintf_chk is
   nothrow, fprintf_chk is not.  */
DEF_FORMAT_ATTRIBUTE_NONNULL(PRINTF,4,4_5)

DEF_FORMAT_ATTRIBUTE(SCANF,1,1_0)
DEF_FORMAT_ATTRIBUTE(SCANF,1,1_2)
DEF_FORMAT_ATTRIBUTE_BOTH(SCANF,2,2_0)
DEF_FORMAT_ATTRIBUTE_BOTH(SCANF,2,2_3)
DEF_FORMAT_ATTRIBUTE_NOTHROW(STRFTIME,3,3_0)
DEF_FORMAT_ATTRIBUTE_NOTHROW(STRFMON,3,3_4)
#undef DEF_FORMAT_ATTRIBUTE
#undef DEF_FORMAT_ATTRIBUTE_NOTHROW
#undef DEF_FORMAT_ATTRIBUTE_BOTH

/* Transactional memory variants of the above.  */

DEF_ATTR_TREE_LIST (ATTR_TM_NOTHROW_LIST,
		    ATTR_TM_REGPARM, ATTR_NULL, ATTR_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_TM_TMPURE_NOTHROW_LIST,
		    ATTR_TM_TMPURE, ATTR_NULL, ATTR_TM_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_TM_PURE_TMPURE_NOTHROW_LIST,
		    ATTR_PURE, ATTR_NULL, ATTR_TM_TMPURE_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_TM_NORETURN_NOTHROW_LIST,
		    ATTR_TM_REGPARM, ATTR_NULL, ATTR_NORETURN_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_TM_CONST_NOTHROW_LIST,
		    ATTR_TM_REGPARM, ATTR_NULL, ATTR_CONST_NOTHROW_LIST)
DEF_ATTR_TREE_LIST (ATTR_TM_NOTHROW_RT_LIST,
		    ATTR_RETURNS_TWICE, ATTR_NULL, ATTR_TM_NOTHROW_LIST)

/* Same attributes used for BUILT_IN_MALLOC except with TM_PURE thrown in.  */
DEF_ATTR_TREE_LIST (ATTR_TMPURE_MALLOC_NOTHROW_LIST,
		   ATTR_TM_TMPURE, ATTR_NULL, ATTR_MALLOC_NOTHROW_LIST)
/* Same attributes used for BUILT_IN_FREE except with TM_PURE thrown in.  */
DEF_ATTR_TREE_LIST (ATTR_TMPURE_NOTHROW_LIST,
		   ATTR_TM_TMPURE, ATTR_NULL, ATTR_NOTHROW_LIST)

DEF_ATTR_TREE_LIST (ATTR_TMPURE_NOTHROW_LEAF_LIST,
		    ATTR_TM_TMPURE, ATTR_NULL, ATTR_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_TMPURE_NORETURN_NOTHROW_LEAF_LIST,
		    ATTR_TM_TMPURE, ATTR_NULL, ATTR_NORETURN_NOTHROW_LEAF_LIST)
DEF_ATTR_TREE_LIST (ATTR_TMPURE_NORETURN_NOTHROW_LEAF_COLD_LIST,
		    ATTR_COLD, ATTR_NULL,
		    ATTR_TMPURE_NORETURN_NOTHROW_LEAF_LIST)

/* Construct a tree for a format_arg attribute.  */
#define DEF_FORMAT_ARG_ATTRIBUTE(FA)					\
  DEF_ATTR_TREE_LIST (ATTR_FORMAT_ARG_##FA, ATTR_FORMAT_ARG,		\
		      ATTR_LIST_##FA, ATTR_NOTHROW_NONNULL_##FA)
DEF_FORMAT_ARG_ATTRIBUTE(1)
DEF_FORMAT_ARG_ATTRIBUTE(2)
#undef DEF_FORMAT_ARG_ATTRIBUTE

