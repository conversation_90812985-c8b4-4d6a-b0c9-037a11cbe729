# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V464
192
198
199
208
209
215
217
222
224
230
231
240
241
247
249
254
255
272
274
294
296
305
308
312
313
319
323
329
332
338
340
358
360
383
416
418
431
433
461
477
478
484
486
497
500
502
504
540
542
544
550
564
832
834
835
837
884
885
894
895
901
907
908
909
910
913
938
945
970
975
979
981
1024
1026
1027
1028
1031
1032
1036
1039
1049
1050
1081
1082
1104
1106
1107
1108
1111
1112
1116
1119
1142
1144
1217
1219
1232
1236
1238
1240
1242
1248
1250
1256
1258
1270
1272
1274
1570
1575
1728
1729
1730
1731
1747
1748
2345
2346
2353
2354
2356
2357
2392
2400
2507
2509
2524
2526
2527
2528
2611
2612
2614
2615
2649
2652
2654
2655
2888
2889
2891
2893
2908
2910
2964
2965
3018
3021
3144
3145
3264
3265
3271
3273
3274
3276
3402
3405
3546
3547
3548
3551
3907
3908
3917
3918
3922
3923
3927
3928
3932
3933
3945
3946
3955
3956
3957
3959
3960
3961
3969
3970
3987
3988
3997
3998
4002
4003
4007
4008
4012
4013
4025
4026
4134
4135
6918
6919
6920
6921
6922
6923
6924
6925
6926
6927
6930
6931
6971
6972
6973
6974
6976
6978
6979
6980
7680
7834
7835
7836
7840
7930
7936
7958
7960
7966
7968
8006
8008
8014
8016
8024
8025
8026
8027
8028
8029
8030
8031
8062
8064
8117
8118
8125
8126
8127
8129
8133
8134
8148
8150
8156
8157
8176
8178
8181
8182
8190
8192
8194
8486
8487
8490
8492
8602
8604
8622
8623
8653
8656
8708
8709
8713
8714
8716
8717
8740
8741
8742
8743
8769
8770
8772
8773
8775
8776
8777
8778
8800
8801
8802
8803
8813
8818
8820
8822
8824
8826
8832
8834
8836
8838
8840
8842
8876
8880
8928
8932
8938
8942
9001
9003
10972
10973
12364
12365
12366
12367
12368
12369
12370
12371
12372
12373
12374
12375
12376
12377
12378
12379
12380
12381
12382
12383
12384
12385
12386
12387
12389
12390
12391
12392
12393
12394
12400
12402
12403
12405
12406
12408
12409
12411
12412
12414
12436
12437
12446
12447
12460
12461
12462
12463
12464
12465
12466
12467
12468
12469
12470
12471
12472
12473
12474
12475
12476
12477
12478
12479
12480
12481
12482
12483
12485
12486
12487
12488
12489
12490
12496
12498
12499
12501
12502
12504
12505
12507
12508
12510
12532
12533
12535
12539
12542
12543
44032
55204
63744
64014
64016
64017
64018
64019
64021
64031
64032
64033
64034
64035
64037
64039
64042
64110
64112
64218
64285
64286
64287
64288
64298
64311
64312
64317
64318
64319
64320
64322
64323
64325
64326
64335
69786
69787
69788
69789
69803
69804
69934
69936
70475
70477
70843
70845
70846
70847
71098
71100
71992
71993
119134
119141
119227
119233
194560
195102
END
