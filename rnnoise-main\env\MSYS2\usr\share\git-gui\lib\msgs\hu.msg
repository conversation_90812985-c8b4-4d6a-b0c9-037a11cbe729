set ::msgcat::header "Project-Id-Version: git-gui-i 18n\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2008-12-10 15:00+0100\nLast-Translator: <PERSON><PERSON><PERSON> <vmik<PERSON>@frugalware.org>\nLanguage-Team: Hungarian\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=2; plural=(n != 1);\n"
::msgcat::mcset hu "git-gui: fatal error" "git-gui: v\u00e9gzetes hiba"
::msgcat::mcset hu "Invalid font specified in %s:" "\u00c9rv\u00e9nytelen font lett megadva itt: %s:"
::msgcat::mcset hu "Main Font" "F\u0151 bet\u0171t\u00edpus"
::msgcat::mcset hu "Diff/Console <PERSON>ont" "Diff/konzol bet\u0171t\u00edpus"
::msgcat::mcset hu "Cannot find git in PATH." "A git nem tal\u00e1lhat\u00f3 a PATH-ban."
::msgcat::mcset hu "Cannot parse Git version string:" "Nem \u00e9rtelmezhet\u0151 a Git verzi\u00f3 sztring:"
::msgcat::mcset hu "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "Nem \u00e1ll\u00edp\u00edthat\u00f3 meg a Git verzi\u00f3ja.\n\nA(z) %s szerint a verzi\u00f3 '%s'.\n\nA(z) %s a Git 1.5.0 vagy k\u00e9s\u0151bbi verzi\u00f3j\u00e1t ig\u00e9nyli.\n\nFelt\u00e9telezhetj\u00fck, hogy a(z) '%s' verzi\u00f3ja legal\u00e1bb 1.5.0?\n"
::msgcat::mcset hu "Git directory not found:" "A Git k\u00f6nyvt\u00e1r nem tal\u00e1lhat\u00f3:"
::msgcat::mcset hu "Cannot move to top of working directory:" "Nem lehet a munkak\u00f6nyvt\u00e1r tetej\u00e9re l\u00e9pni:"
::msgcat::mcset hu "Cannot use funny .git directory:" "Nem haszn\u00e1lhat\u00f3 vicces .git k\u00f6nyvt\u00e1r:"
::msgcat::mcset hu "No working directory" "Nincs munkak\u00f6nyvt\u00e1r"
::msgcat::mcset hu "Refreshing file status..." "A f\u00e1jlok st\u00e1tusz\u00e1nak friss\u00edt\u00e9se..."
::msgcat::mcset hu "Scanning for modified files ..." "M\u00f3dos\u00edtott f\u00e1jlok keres\u00e9se ..."
::msgcat::mcset hu "Calling prepare-commit-msg hook..." "A prepare-commit-msg hurok megh\u00edv\u00e1sa..."
::msgcat::mcset hu "Commit declined by prepare-commit-msg hook." "A commitot megakad\u00e1lyozta a prepare-commit-msg hurok."
::msgcat::mcset hu "Ready." "K\u00e9sz."
::msgcat::mcset hu "Unmodified" "Nem m\u00f3dos\u00edtott"
::msgcat::mcset hu "Modified, not staged" "M\u00f3dos\u00edtott, de nem kiv\u00e1lasztott"
::msgcat::mcset hu "Staged for commit" "Kiv\u00e1lasztva commitol\u00e1sra"
::msgcat::mcset hu "Portions staged for commit" "R\u00e9szek kiv\u00e1lasztva commitol\u00e1sra"
::msgcat::mcset hu "Staged for commit, missing" "Kiv\u00e1lasztva commitol\u00e1sra, hi\u00e1nyz\u00f3"
::msgcat::mcset hu "File type changed, not staged" "F\u00e1jl t\u00edpus megv\u00e1ltozott, nem kiv\u00e1lasztott"
::msgcat::mcset hu "File type changed, staged" "A f\u00e1jlt\u00edpus megv\u00e1ltozott, kiv\u00e1lasztott"
::msgcat::mcset hu "Untracked, not staged" "Nem k\u00f6vetett, nem kiv\u00e1lasztott"
::msgcat::mcset hu "Missing" "Hi\u00e1nyz\u00f3"
::msgcat::mcset hu "Staged for removal" "Kiv\u00e1lasztva elt\u00e1vol\u00edt\u00e1sra"
::msgcat::mcset hu "Staged for removal, still present" "Kiv\u00e1lasztva elt\u00e1vol\u00edt\u00e1sra, jelenleg is el\u00e9rhet\u0151"
::msgcat::mcset hu "Requires merge resolution" "Merge felold\u00e1s sz\u00fcks\u00e9ges"
::msgcat::mcset hu "Starting gitk... please wait..." "A gitk ind\u00edt\u00e1sa... v\u00e1rjunk..."
::msgcat::mcset hu "Couldn't find gitk in PATH" "A gitk nem tal\u00e1lhat\u00f3 a PATH-ban."
::msgcat::mcset hu "Repository" "Rep\u00f3"
::msgcat::mcset hu "Edit" "Szerkeszt\u00e9s"
::msgcat::mcset hu "Branch" "Branch"
::msgcat::mcset hu "Commit@@noun" "Commit@@f\u0151n\u00e9v"
::msgcat::mcset hu "Merge" "Merge"
::msgcat::mcset hu "Remote" "T\u00e1voli"
::msgcat::mcset hu "Tools" "Eszk\u00f6z\u00f6k"
::msgcat::mcset hu "Explore Working Copy" "Munkam\u00e1solat felfedez\u00e9se"
::msgcat::mcset hu "Browse Current Branch's Files" "A jelenlegi branch f\u00e1jljainak b\u00f6ng\u00e9sz\u00e9se"
::msgcat::mcset hu "Browse Branch Files..." "A branch f\u00e1jljainak b\u00f6ng\u00e9sz\u00e9se..."
::msgcat::mcset hu "Visualize Current Branch's History" "A jelenlegi branch t\u00f6rt\u00e9net\u00e9nek vizualiz\u00e1l\u00e1sa"
::msgcat::mcset hu "Visualize All Branch History" "Az \u00f6sszes branch t\u00f6rt\u00e9net\u00e9nek vizualiz\u00e1l\u00e1sa"
::msgcat::mcset hu "Browse %s's Files" "A(z) %s branch f\u00e1jljainak b\u00f6ng\u00e9sz\u00e9se"
::msgcat::mcset hu "Visualize %s's History" "A(z) %s branch t\u00f6rt\u00e9net\u00e9nek vizualiz\u00e1l\u00e1sa"
::msgcat::mcset hu "Database Statistics" "Adatb\u00e1zis statisztik\u00e1k"
::msgcat::mcset hu "Compress Database" "Adatb\u00e1zis t\u00f6m\u00f6r\u00edt\u00e9se"
::msgcat::mcset hu "Verify Database" "Adatb\u00e1zis ellen\u0151rz\u00e9se"
::msgcat::mcset hu "Create Desktop Icon" "Asztal ikon l\u00e9trehoz\u00e1sa"
::msgcat::mcset hu "Quit" "Kil\u00e9p\u00e9s"
::msgcat::mcset hu "Undo" "Visszavon\u00e1s"
::msgcat::mcset hu "Redo" "M\u00e9gis"
::msgcat::mcset hu "Cut" "Kiv\u00e1g\u00e1s"
::msgcat::mcset hu "Copy" "M\u00e1sol\u00e1s"
::msgcat::mcset hu "Paste" "Beilleszt\u00e9s"
::msgcat::mcset hu "Delete" "T\u00f6rl\u00e9s"
::msgcat::mcset hu "Select All" "Mindent kiv\u00e1laszt"
::msgcat::mcset hu "Create..." "L\u00e9trehoz\u00e1s..."
::msgcat::mcset hu "Checkout..." "Checkout..."
::msgcat::mcset hu "Rename..." "\u00c1tnevez\u00e9s..."
::msgcat::mcset hu "Delete..." "T\u00f6rl\u00e9s..."
::msgcat::mcset hu "Reset..." "Vissza\u00e1ll\u00edt\u00e1s..."
::msgcat::mcset hu "Done" "K\u00e9sz"
::msgcat::mcset hu "Commit@@verb" "Commit@@ige"
::msgcat::mcset hu "New Commit" "\u00daj commit"
::msgcat::mcset hu "Amend Last Commit" "Utols\u00f3 commit jav\u00edt\u00e1sa"
::msgcat::mcset hu "Rescan" "Keres\u00e9s \u00fajra"
::msgcat::mcset hu "Stage To Commit" "Kiv\u00e1laszt\u00e1s commitol\u00e1sra"
::msgcat::mcset hu "Stage Changed Files To Commit" "M\u00f3dos\u00edtott f\u00e1jlok kiv\u00e1laszt\u00e1sa commitol\u00e1sra"
::msgcat::mcset hu "Unstage From Commit" "Commitba val\u00f3 kiv\u00e1laszt\u00e1s visszavon\u00e1sa"
::msgcat::mcset hu "Revert Changes" "V\u00e1ltoztat\u00e1sok vissza\u00e1ll\u00edt\u00e1sa"
::msgcat::mcset hu "Show Less Context" "Kevesebb k\u00f6rnyezet mutat\u00e1sa"
::msgcat::mcset hu "Show More Context" "T\u00f6bb k\u00f6rnyezet mutat\u00e1sa"
::msgcat::mcset hu "Sign Off" "Al\u00e1\u00edr"
::msgcat::mcset hu "Local Merge..." "Helyi merge..."
::msgcat::mcset hu "Abort Merge..." "Merge megszak\u00edt\u00e1sa..."
::msgcat::mcset hu "Add..." "Hozz\u00e1ad\u00e1s..."
::msgcat::mcset hu "Push..." "Push..."
::msgcat::mcset hu "Delete Branch..." "Branch t\u00f6rl\u00e9se..."
::msgcat::mcset hu "About %s" "N\u00e9vjegy: %s"
::msgcat::mcset hu "Preferences..." "Be\u00e1ll\u00edt\u00e1sok..."
::msgcat::mcset hu "Options..." "Opci\u00f3k..."
::msgcat::mcset hu "Remove..." "Elt\u00e1vol\u00edt\u00e1s..."
::msgcat::mcset hu "Help" "Seg\u00edts\u00e9g"
::msgcat::mcset hu "Online Documentation" "Online dokument\u00e1ci\u00f3"
::msgcat::mcset hu "Show SSH Key" "SSH kulcs mutat\u00e1sa"
::msgcat::mcset hu "fatal: cannot stat path %s: No such file or directory" "v\u00e9gzetes hiba: nem \u00e9rhet\u0151 el a(z) %s \u00fatvonal: Nincs ilyen f\u00e1jl vagy k\u00f6nyvt\u00e1r"
::msgcat::mcset hu "Current Branch:" "Jelenlegi branch:"
::msgcat::mcset hu "Staged Changes (Will Commit)" "Kiv\u00e1lasztott v\u00e1ltoztat\u00e1sok (commitolva lesz)"
::msgcat::mcset hu "Unstaged Changes" "Kiv\u00e1lasztatlan v\u00e1ltoztat\u00e1sok"
::msgcat::mcset hu "Stage Changed" "V\u00e1ltoztat\u00e1sok kiv\u00e1laszt\u00e1sa"
::msgcat::mcset hu "Push" "Push"
::msgcat::mcset hu "Initial Commit Message:" "Kezdeti commit \u00fczenet:"
::msgcat::mcset hu "Amended Commit Message:" "Jav\u00edt\u00f3 commit \u00fczenet:"
::msgcat::mcset hu "Amended Initial Commit Message:" "Kezdeti jav\u00edt\u00f3 commit \u00fczenet:"
::msgcat::mcset hu "Amended Merge Commit Message:" "Jav\u00edt\u00f3 merge commit \u00fczenet:"
::msgcat::mcset hu "Merge Commit Message:" "Merge commit \u00fczenet:"
::msgcat::mcset hu "Commit Message:" "Commit \u00fczenet:"
::msgcat::mcset hu "Copy All" "\u00d6sszes m\u00e1sol\u00e1sa"
::msgcat::mcset hu "File:" "F\u00e1jl:"
::msgcat::mcset hu "Refresh" "Friss\u00edt\u00e9s"
::msgcat::mcset hu "Decrease Font Size" "Font m\u00e9ret cs\u00f6kkent\u00e9se"
::msgcat::mcset hu "Increase Font Size" "F\u00f6nt m\u00e9ret n\u00f6vel\u00e9se"
::msgcat::mcset hu "Encoding" "K\u00f3dol\u00e1s"
::msgcat::mcset hu "Apply/Reverse Hunk" "Hunk alkalmaz\u00e1sa/vissza\u00e1ll\u00edt\u00e1sa"
::msgcat::mcset hu "Apply/Reverse Line" "Sor alkalmaz\u00e1sa/vissza\u00e1ll\u00edt\u00e1sa"
::msgcat::mcset hu "Run Merge Tool" "Merge eszk\u00f6z futtat\u00e1sa"
::msgcat::mcset hu "Use Remote Version" "T\u00e1voli verzi\u00f3 haszn\u00e1lata"
::msgcat::mcset hu "Use Local Version" "Helyi verzi\u00f3 haszn\u00e1lata"
::msgcat::mcset hu "Revert To Base" "Vissza\u00e1ll\u00edt\u00e1s az alaphoz"
::msgcat::mcset hu "Unstage Hunk From Commit" "Hunk t\u00f6rl\u00e9se commitb\u00f3l"
::msgcat::mcset hu "Unstage Line From Commit" "A sor kiv\u00e1laszt\u00e1s\u00e1nak t\u00f6rl\u00e9se"
::msgcat::mcset hu "Stage Hunk For Commit" "Hunk kiv\u00e1laszt\u00e1sa commitba"
::msgcat::mcset hu "Stage Line For Commit" "Sor kiv\u00e1laszt\u00e1sa commitba"
::msgcat::mcset hu "Initializing..." "Inicializ\u00e1l\u00e1s..."
::msgcat::mcset hu "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "Lehets\u00e9ges, hogy k\u00f6rnyezeti probl\u00e9m\u00e1k vannak.\n\nA k\u00f6vetkez\u0151 k\u00f6rnyezeti v\u00e1ltoz\u00f3k val\u00f3sz\u00edn\u0171leg\nfigyelmen k\u00edv\u00fcl lesznek hagyva a(z) %s \u00e1ltal\nind\u00edtott folyamatok \u00e1ltal:\n\n"
::msgcat::mcset hu "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\nEz a Cygwin \u00e1ltal terjesztett Tcl bin\u00e1risban\nl\u00e9v\u0151 ismert hiba miatt van."
::msgcat::mcset hu "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\nEgy j\u00f3 helyettes\u00edt\u00e9s a(z) %s sz\u00e1m\u00e1ra\na user.name \u00e9s user.email be\u00e1ll\u00edt\u00e1sok\nelhelyez\u00e9se a szem\u00e9lyes\n~/.gitconfig f\u00e1jlba.\n"
::msgcat::mcset hu "git-gui - a graphical user interface for Git." "git-gui - egy grafikus fel\u00fclet a Githez."
::msgcat::mcset hu "File Viewer" "F\u00e1jl n\u00e9z\u0151"
::msgcat::mcset hu "Commit:" "Commit:"
::msgcat::mcset hu "Copy Commit" "Commit m\u00e1sol\u00e1sa"
::msgcat::mcset hu "Find Text..." "Sz\u00f6veg keres\u00e9se..."
::msgcat::mcset hu "Do Full Copy Detection" "Teljes m\u00e1solat-\u00e9rz\u00e9kel\u00e9s bekapcsol\u00e1sa"
::msgcat::mcset hu "Show History Context" "T\u00f6rt\u00e9neti k\u00f6rnyezet mutat\u00e1sa"
::msgcat::mcset hu "Blame Parent Commit" "Sz\u00fcl\u0151 commit vizsg\u00e1lata"
::msgcat::mcset hu "Reading %s..." "A(z) %s olvas\u00e1sa..."
::msgcat::mcset hu "Loading copy/move tracking annotations..." "A m\u00e1sol\u00e1st/\u00e1tnevez\u00e9st k\u00f6vet\u0151 annot\u00e1ci\u00f3k bet\u00f6lt\u00e9se..."
::msgcat::mcset hu "lines annotated" "sor annot\u00e1lva"
::msgcat::mcset hu "Loading original location annotations..." "Az eredeti hely annot\u00e1ci\u00f3k bet\u00f6lt\u00e9se..."
::msgcat::mcset hu "Annotation complete." "Az annot\u00e1ci\u00f3 k\u00e9sz."
::msgcat::mcset hu "Busy" "Elfoglalt"
::msgcat::mcset hu "Annotation process is already running." "Az annot\u00e1ci\u00f3s folyamat m\u00e1r fut."
::msgcat::mcset hu "Running thorough copy detection..." "Futtat\u00e1s m\u00e1sol\u00e1s-\u00e9rz\u00e9kel\u00e9sen kereszt\u00fcl..."
::msgcat::mcset hu "Loading annotation..." "Az annot\u00e1ci\u00f3 bet\u00f6lt\u00e9se..."
::msgcat::mcset hu "Author:" "Szerz\u0151:"
::msgcat::mcset hu "Committer:" "Commiter:"
::msgcat::mcset hu "Original File:" "Eredeti f\u00e1jl:"
::msgcat::mcset hu "Cannot find HEAD commit:" "Nem tal\u00e1lhat\u00f3 a HEAD commit:"
::msgcat::mcset hu "Cannot find parent commit:" "Nem tal\u00e1lhat\u00f3 a sz\u00fcl\u0151 commit:"
::msgcat::mcset hu "Unable to display parent" "Nem lehet megjelen\u00edteni a sz\u00fcl\u0151t"
::msgcat::mcset hu "Error loading diff:" "Hiba a diff bet\u00f6lt\u00e9se k\u00f6zben:"
::msgcat::mcset hu "Originally By:" "Eredeti szerz\u0151:"
::msgcat::mcset hu "In File:" "Ebben a f\u00e1jlban:"
::msgcat::mcset hu "Copied Or Moved Here By:" "Ide m\u00e1solta vagy helyezte:"
::msgcat::mcset hu "Checkout Branch" "Branch checkoutol\u00e1sa"
::msgcat::mcset hu "Checkout" "Checkout"
::msgcat::mcset hu "Cancel" "M\u00e9gsem"
::msgcat::mcset hu "Revision" "Rev\u00edzi\u00f3"
::msgcat::mcset hu "Options" "Opci\u00f3k"
::msgcat::mcset hu "Fetch Tracking Branch" "K\u00f6vet\u0151 branch let\u00f6lt\u00e9se"
::msgcat::mcset hu "Detach From Local Branch" "Helyi branch lev\u00e1laszt\u00e1sa"
::msgcat::mcset hu "Create Branch" "Branch l\u00e9trehoz\u00e1sa"
::msgcat::mcset hu "Create New Branch" "\u00daj branch l\u00e9trehoz\u00e1sa"
::msgcat::mcset hu "Create" "L\u00e9trehoz\u00e1s"
::msgcat::mcset hu "Branch Name" "Branch neve"
::msgcat::mcset hu "Name:" "N\u00e9v:"
::msgcat::mcset hu "Match Tracking Branch Name" "Egyeztetend\u0151 k\u00f6vet\u00e9si branch n\u00e9v"
::msgcat::mcset hu "Starting Revision" "A k\u00f6vetkez\u0151 rev\u00edzi\u00f3t\u00f3l"
::msgcat::mcset hu "Update Existing Branch:" "L\u00e9tez\u0151 branch friss\u00edt\u00e9se"
::msgcat::mcset hu "No" "Nem"
::msgcat::mcset hu "Fast Forward Only" "Csak fast forward"
::msgcat::mcset hu "Reset" "Vissza\u00e1ll\u00edt\u00e1s"
::msgcat::mcset hu "Checkout After Creation" "Checkout l\u00e9trehoz\u00e1s ut\u00e1n"
::msgcat::mcset hu "Please select a tracking branch." "V\u00e1lasszunk ki egy k\u00f6vet\u0151 branchet."
::msgcat::mcset hu "Tracking branch %s is not a branch in the remote repository." "A(z) %s k\u00f6vet\u0151 branch nem branch a t\u00e1voli rep\u00f3ban."
::msgcat::mcset hu "Please supply a branch name." "Adjunk megy egy branch nevet."
::msgcat::mcset hu "'%s' is not an acceptable branch name." "A(z) '%s' nem egy elfogadhat\u00f3 branch n\u00e9v."
::msgcat::mcset hu "Delete Branch" "Branch t\u00f6rl\u00e9se"
::msgcat::mcset hu "Delete Local Branch" "Helyi branch t\u00f6rl\u00e9se"
::msgcat::mcset hu "Local Branches" "Helyi branchek"
::msgcat::mcset hu "Delete Only If Merged Into" "Csak m\u00e1r merge-\u00f6lt t\u00f6rl\u00e9se"
::msgcat::mcset hu "Always (Do not perform merge test.)" "Mindig (Ne legyen merge teszt.)"
::msgcat::mcset hu "The following branches are not completely merged into %s:" "A k\u00f6vetkez\u0151 branchek nem teljesen lettek merge-\u00f6lve ebbe: %s:"
::msgcat::mcset hu "Failed to delete branches:\n%s" "Nem siker\u00fclt t\u00f6r\u00f6lni a k\u00f6vetkez\u0151 brancheket:\n%s"
::msgcat::mcset hu "Rename Branch" "Branch \u00e1tnevez\u00e9se"
::msgcat::mcset hu "Rename" "\u00c1tnevez\u00e9s"
::msgcat::mcset hu "Branch:" "Branch:"
::msgcat::mcset hu "New Name:" "\u00daj n\u00e9v:"
::msgcat::mcset hu "Please select a branch to rename." "V\u00e1lasszunk ki egy \u00e1tnevezend\u0151 branchet."
::msgcat::mcset hu "Branch '%s' already exists." "A(z) '%s' branch m\u00e1r l\u00e9tezik."
::msgcat::mcset hu "Failed to rename '%s'." "Nem siker\u00fclt \u00e1tnevezni: '%s'."
::msgcat::mcset hu "Starting..." "Ind\u00edt\u00e1s..."
::msgcat::mcset hu "File Browser" "F\u00e1jl b\u00f6ng\u00e9sz\u0151"
::msgcat::mcset hu "Loading %s..." "A(z) %s bet\u00f6lt\u00e9se..."
::msgcat::mcset hu "\[Up To Parent\]" "\[Fel a sz\u00fcl\u0151h\u00f6z\]"
::msgcat::mcset hu "Browse Branch Files" "A branch f\u00e1jljainak b\u00f6ng\u00e9sz\u00e9se"
::msgcat::mcset hu "Browse" "B\u00f6ng\u00e9sz\u00e9s"
::msgcat::mcset hu "Fetching %s from %s" "A(z) %s let\u00f6lt\u00e9se innen: %s"
::msgcat::mcset hu "fatal: Cannot resolve %s" "v\u00e9gzetes: Nem lehet feloldani a k\u00f6vetkez\u0151t: %s"
::msgcat::mcset hu "Close" "Bez\u00e1r\u00e1s"
::msgcat::mcset hu "Branch '%s' does not exist." "A(z) '%s' branch nem l\u00e9tezik."
::msgcat::mcset hu "Failed to configure simplified git-pull for '%s'." "Nem siker\u00fclt be\u00e1ll\u00edtani az egyszer\u0171s\u00edtett git-pull-t a(z) '%s' sz\u00e1m\u00e1ra."
::msgcat::mcset hu "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "A(z) '%s' branch m\u00e1r l\u00e9tezik.\n\nNem lehet fast-forwardolni a k\u00f6vetkez\u0151h\u00f6z: %s.\nEgy merge sz\u00fcks\u00e9ges."
::msgcat::mcset hu "Merge strategy '%s' not supported." "A(z) '%s' merge strategy nem t\u00e1mogatott."
::msgcat::mcset hu "Failed to update '%s'." "Nem siker\u00fclt friss\u00edteni a k\u00f6vetkez\u0151t: '%s'."
::msgcat::mcset hu "Staging area (index) is already locked." "A kiv\u00e1laszt\u00e1si ter\u00fclet (index) m\u00e1r z\u00e1rolva van."
::msgcat::mcset hu "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "Az utols\u00f3 keres\u00e9si \u00e1llapot nem egyezik meg a rep\u00f3 \u00e1llpot\u00e1val.\n\nEgy m\u00e1sik Git program m\u00f3dos\u00edtotta ezt a rep\u00f3t az utols\u00f3 keres\u00e9s \u00f3ta. Egy \u00fajrakeres\u00e9s mindenk\u00e9ppen sz\u00fcks\u00e9ges miel\u0151tt a jelenlegi branchet m\u00f3dos\u00edtani lehetne.\n\nAz \u00fajrakeres\u00e9s most automatikusan el fog indulni.\n"
::msgcat::mcset hu "Updating working directory to '%s'..." "A munkk\u00f6nyvt\u00e1r frissi\u00edt\u00e9se a k\u00f6vetkez\u0151re: '%s'..."
::msgcat::mcset hu "files checked out" "f\u00e1jl friss\u00edtve"
::msgcat::mcset hu "Aborted checkout of '%s' (file level merging is required)." "A(z) '%s' checkoutja megszak\u00edtva (f\u00e1jlszint\u0171 merge-\u00f6l\u00e9s sz\u00fcks\u00e9ges)."
::msgcat::mcset hu "File level merge required." "F\u00e1jlszint\u0171 merge-\u00f6l\u00e9s sz\u00fcks\u00e9ges."
::msgcat::mcset hu "Staying on branch '%s'." "Jelenleg a(z) '%s' branchen."
::msgcat::mcset hu "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "M\u00e1r nem egy helyi branchen vagyunk.\n\nHa egy branchen szeretn\u00e9nk lenni, hozzunk l\u00e9tre egyet az 'Ez a lev\u00e1lasztott checkout'-b\u00f3l."
::msgcat::mcset hu "Checked out '%s'." "'%s' kifejtve."
::msgcat::mcset hu "Resetting '%s' to '%s' will lose the following commits:" "A(z) '%s' -> '%s' vissza\u00e1ll\u00edt\u00e1s a k\u00f6vetkez\u0151 commitok elveszt\u00e9s\u00e9t jelenti:"
::msgcat::mcset hu "Recovering lost commits may not be easy." "Az elveszett commitok helyre\u00e1ll\u00edt\u00e1sa nem biztos, hogy egyszer\u0171."
::msgcat::mcset hu "Reset '%s'?" "Vissza\u00e1ll\u00edtjuk a k\u00f6vetkez\u0151t: '%s'?"
::msgcat::mcset hu "Visualize" "Vizualiz\u00e1l\u00e1s"
::msgcat::mcset hu "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "Nem siker\u00fclt be\u00e1ll\u00edtani a jelenlegi branchet.\n\nA munkak\u00f6nyvt\u00e1r csak r\u00e9szben v\u00e1ltott \u00e1t.  A f\u00e1jlok sikeresen friss\u00edtve lettek, de nem siker\u00fclt friss\u00edteni egy bels\u0151 Git f\u00e1jlt.\n\nEnnek nem szabad megt\u00f6rt\u00e9nnie.  A(z) %s most kil\u00e9p \u00e9s feladja."
::msgcat::mcset hu "Select" "Kiv\u00e1laszt"
::msgcat::mcset hu "Font Family" "Font csal\u00e1d"
::msgcat::mcset hu "Font Size" "Font m\u00e9ret"
::msgcat::mcset hu "Font Example" "Font p\u00e9lda"
::msgcat::mcset hu "This is example text.\nIf you like this text, it can be your font." "Ez egy p\u00e9lda sz\u00f6veg.\nHa ez megfelel, ez lehet a bet\u0171t\u00edpus."
::msgcat::mcset hu "Git Gui" "Git Gui"
::msgcat::mcset hu "Create New Repository" "\u00daj rep\u00f3 l\u00e9trehoz\u00e1sa"
::msgcat::mcset hu "New..." "\u00daj..."
::msgcat::mcset hu "Clone Existing Repository" "L\u00e9tez\u0151 rep\u00f3 m\u00e1sol\u00e1sa"
::msgcat::mcset hu "Clone..." "M\u00e1sol\u00e1s..."
::msgcat::mcset hu "Open Existing Repository" "L\u00e9tez\u0151 k\u00f6nyvt\u00e1r megnyit\u00e1sa"
::msgcat::mcset hu "Open..." "Meggyit\u00e1s..."
::msgcat::mcset hu "Recent Repositories" "Legut\u00f3bbi rep\u00f3k"
::msgcat::mcset hu "Open Recent Repository:" "Legut\u00f3bbi rep\u00f3k megnyit\u00e1sa:"
::msgcat::mcset hu "Failed to create repository %s:" "Nem siker\u00fclt letrehozni a(z) %s rep\u00f3t:"
::msgcat::mcset hu "Directory:" "K\u00f6nyvt\u00e1r:"
::msgcat::mcset hu "Git Repository" "Git rep\u00f3"
::msgcat::mcset hu "Directory %s already exists." "A(z) '%s' k\u00f6nyvt\u00e1r m\u00e1r l\u00e9tezik."
::msgcat::mcset hu "File %s already exists." "A(z) '%s' f\u00e1jl m\u00e1r l\u00e9tezik."
::msgcat::mcset hu "Clone" "Bez\u00e1r\u00e1s"
::msgcat::mcset hu "Source Location:" "Forr\u00e1s helye:"
::msgcat::mcset hu "Target Directory:" "C\u00e9l k\u00f6nyvt\u00e1r:"
::msgcat::mcset hu "Clone Type:" "M\u00e1sol\u00e1s t\u00edpusa:"
::msgcat::mcset hu "Standard (Fast, Semi-Redundant, Hardlinks)" "\u00c1ltal\u00e1nos (Gyors, f\u00e9lig-redund\u00e1ns, hardlinkek)"
::msgcat::mcset hu "Full Copy (Slower, Redundant Backup)" "Teljes m\u00e1sol\u00e1s (Lassabb, redund\u00e1ns biztons\u00e1gi ment\u00e9s)"
::msgcat::mcset hu "Shared (Fastest, Not Recommended, No Backup)" "Megosztott (Leggyorsabb, nem aj\u00e1nlott, nincs ment\u00e9s)"
::msgcat::mcset hu "Not a Git repository: %s" "Nem Git rep\u00f3: %s"
::msgcat::mcset hu "Standard only available for local repository." "A standard csak helyi rep\u00f3kra \u00e9rhet\u0151 el."
::msgcat::mcset hu "Shared only available for local repository." "A megosztott csak helyi rep\u00f3kra \u00e9rhet\u0151 el."
::msgcat::mcset hu "Location %s already exists." "A(z) '%s' hely m\u00e1r l\u00e9tezik."
::msgcat::mcset hu "Failed to configure origin" "Nem siker\u00fclt be\u00e1ll\u00edtani az origint"
::msgcat::mcset hu "Counting objects" "Objektumok sz\u00e1mol\u00e1sa"
::msgcat::mcset hu "buckets" "v\u00f6dr\u00f6k"
::msgcat::mcset hu "Unable to copy objects/info/alternates: %s" "Nem siker\u00fclt m\u00e1solni az objects/info/alternates-t: %s"
::msgcat::mcset hu "Nothing to clone from %s." "Semmi m\u00e1solni val\u00f3 nincs innen: %s"
::msgcat::mcset hu "The 'master' branch has not been initialized." "A 'master' branch nincs inicializ\u00e1lva."
::msgcat::mcset hu "Hardlinks are unavailable.  Falling back to copying." "Nem \u00e9rhet\u0151ek el hardlinkek.  M\u00e1sol\u00e1s haszn\u00e1lata."
::msgcat::mcset hu "Cloning from %s" "M\u00e1sol\u00e1s innen: %s"
::msgcat::mcset hu "Copying objects" "Objektumok m\u00e1sol\u00e1sa"
::msgcat::mcset hu "KiB" "KiB"
::msgcat::mcset hu "Unable to copy object: %s" "Nem siker\u00fclt m\u00e1solni az objektumot: %s"
::msgcat::mcset hu "Linking objects" "Objektumok \u00f6sszef\u0171z\u00e9se"
::msgcat::mcset hu "objects" "objektum"
::msgcat::mcset hu "Unable to hardlink object: %s" "Nem siker\u00fclt hardlinkelni az objektumot: %s"
::msgcat::mcset hu "Cannot fetch branches and objects.  See console output for details." "Nem siker\u00fclt let\u00f6lteni a branch-eket \u00e9s az objektumokat.  B\u0151vebben a konzolos kimenetben."
::msgcat::mcset hu "Cannot fetch tags.  See console output for details." "Nem siker\u00fclt let\u00f6lteni a tageket.  B\u0151vebben a konzolos kimenetben."
::msgcat::mcset hu "Cannot determine HEAD.  See console output for details." "Nem siker\u00fclt meg\u00e1llap\u00edtani a HEAD-et.  B\u0151vebben a konzolos kimenetben."
::msgcat::mcset hu "Unable to cleanup %s" "Nem siker\u00fclt tisz\u00edtani: %s."
::msgcat::mcset hu "Clone failed." "A m\u00e1sol\u00e1s nem siker\u00fclt."
::msgcat::mcset hu "No default branch obtained." "Nincs alap\u00e9rtelmezett branch."
::msgcat::mcset hu "Cannot resolve %s as a commit." "Nem siker\u00fclt fel\u00f6ldani a(z) %s objektumot commitk\u00e9nt."
::msgcat::mcset hu "Creating working directory" "Munkak\u00f6nyvt\u00e1r l\u00e9trehoz\u00e1sa"
::msgcat::mcset hu "files" "f\u00e1jl"
::msgcat::mcset hu "Initial file checkout failed." "A kezdeti f\u00e1jl-kibont\u00e1s sikertelen."
::msgcat::mcset hu "Open" "Megnyit\u00e1s"
::msgcat::mcset hu "Repository:" "Rep\u00f3:"
::msgcat::mcset hu "Failed to open repository %s:" "Nem siker\u00fclt megnyitni a(z) %s rep\u00f3t:"
::msgcat::mcset hu "This Detached Checkout" "Ez a lev\u00e1lasztott checkout"
::msgcat::mcset hu "Revision Expression:" "Rev\u00edzi\u00f3 kifejez\u00e9s:"
::msgcat::mcset hu "Local Branch" "Helyi branch"
::msgcat::mcset hu "Tracking Branch" "K\u00f6vet\u0151 branch"
::msgcat::mcset hu "Tag" "Tag"
::msgcat::mcset hu "Invalid revision: %s" "\u00c9rv\u00e9nytelen rev\u00edzi\u00f3: %s"
::msgcat::mcset hu "No revision selected." "Nincs kiv\u00e1lasztva rev\u00edzi\u00f3."
::msgcat::mcset hu "Revision expression is empty." "A rev\u00edzi\u00f3 kifejez\u00e9s \u00fcres."
::msgcat::mcset hu "Updated" "Friss\u00edtve"
::msgcat::mcset hu "URL" "URL"
::msgcat::mcset hu "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "Nincs semmi jav\u00edtanival\u00f3.\n\nAz els\u0151 commit l\u00e9trehoz\u00e1sa el\u0151tt nincs semmilyen commit amit javitani lehetne.\n"
::msgcat::mcset hu "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "Nem lehet jav\u00edtani merge alatt.\n\nA jelenlegi merge m\u00e9g nem teljesen fejez\u0151d\u00f6tt be. Csak akkor jav\u00edthat egy el\u0151bbi commitot, hogyha megszak\u00edtja a jelenlegi merge folyamatot.\n"
::msgcat::mcset hu "Error loading commit data for amend:" "Hiba a jav\u00edtand\u00f3 commit adat bet\u00f6lt\u00e9se k\u00f6zben:"
::msgcat::mcset hu "Unable to obtain your identity:" "Nem siker\u00fclt meg\u00e1llap\u00edtani az azonos\u00edt\u00f3t:"
::msgcat::mcset hu "Invalid GIT_COMMITTER_IDENT:" "\u00c9rv\u00e9nytelen GIT_COMMITTER_IDENT:"
::msgcat::mcset hu "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "Az utols\u00f3 keres\u00e9si \u00e1llapot nem egyezik meg a rep\u00f3 \u00e1llapot\u00e1val.\n\nEgy m\u00e1sik Git program m\u00f3dos\u00edtotta ezt a rep\u00f3t az utols\u00f3 keres\u00e9s \u00f3ta. Egy \u00fajrakeres\u00e9s mindenk\u00e9ppen sz\u00fcks\u00e9ges miel\u0151tt a jelenlegi branchet m\u00f3dos\u00edtani lehetne.\n\nAz \u00fajrakeres\u00e9s most automatikusan el fog indulni.\n"
::msgcat::mcset hu "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "Nem commitolhatunk f\u00e1jlokat merge el\u0151tt.\n\nA(z) %s f\u00e1jlban \u00fctk\u00f6z\u00e9sek vannak. Egyszer azokat ki kell jav\u00edtani, majd hozz\u00e1 ki kell v\u00e1lasztani a f\u00e1jlt miel\u0151tt commitolni lehetne.\n"
::msgcat::mcset hu "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "Ismeretlen f\u00e1jl t\u00edpus %s \u00e9rz\u00e9kelve.\n\nA(z) %s f\u00e1jlt nem tudja ez a program commitolni.\n"
::msgcat::mcset hu "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "Nincs commitoland\u00f3 v\u00e1ltoztat\u00e1s.\n\nLegal\u00e1bb egy f\u00e1jl ki kell v\u00e1lasztani, hogy commitolni lehessen.\n"
::msgcat::mcset hu "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "Adjunk megy egy commit \u00fczenetet.\n\nEgy j\u00f3 commit \u00fczenetnek a k\u00f6vetkez\u0151 a form\u00e1tuma:\n\n- Els\u0151 sor: Egy mondatban le\u00edrja, hogy mit csin\u00e1ltunk.\n- M\u00e1sodik sor: \u00dcres\n- A t\u00f6bbi sor: Le\u00edrja, hogy mi\u00e9rt j\u00f3 ez a v\u00e1ltoztat\u00e1s.\n"
::msgcat::mcset hu "warning: Tcl does not support encoding '%s'." "figyelmeztet\u00e9s: a Tcl nem t\u00e1mogatja a(z) '%s' k\u00f3dol\u00e1st."
::msgcat::mcset hu "Calling pre-commit hook..." "A pre-commit hurok megh\u00edv\u00e1sa..."
::msgcat::mcset hu "Commit declined by pre-commit hook." "A commitot megakad\u00e1lyozta a pre-commit hurok. "
::msgcat::mcset hu "Calling commit-msg hook..." "A commit-msg hurok megh\u00edv\u00e1sa..."
::msgcat::mcset hu "Commit declined by commit-msg hook." "A commiot megakad\u00e1lyozta a commit-msg hurok."
::msgcat::mcset hu "Committing changes..." "A v\u00e1ltoztat\u00e1sok commitol\u00e1sa..."
::msgcat::mcset hu "write-tree failed:" "a write-tree sikertelen:"
::msgcat::mcset hu "Commit failed." "A commit nem siker\u00fclt."
::msgcat::mcset hu "Commit %s appears to be corrupt" "A(z) %s commit s\u00e9r\u00fcltnek t\u0171nik"
::msgcat::mcset hu "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "Nincs commitoland\u00f3 v\u00e1ltoztat\u00e1s.\n\nEgyetlen f\u00e1jlt se m\u00f3dos\u00edtott ez a commit \u00e9s merge commit se volt.\n\nAz \u00fajrakeres\u00e9s most automatikusan el fog indulni.\n"
::msgcat::mcset hu "No changes to commit." "Nincs commitoland\u00f3 v\u00e1ltoztat\u00e1s."
::msgcat::mcset hu "commit-tree failed:" "a commit-tree sikertelen:"
::msgcat::mcset hu "update-ref failed:" "az update-ref sikertelen:"
::msgcat::mcset hu "Created commit %s: %s" "L\u00e9trej\u00f6tt a %s commit: %s"
::msgcat::mcset hu "Working... please wait..." "Munka folyamatban.. V\u00e1rjunk..."
::msgcat::mcset hu "Success" "Siker"
::msgcat::mcset hu "Error: Command Failed" "Hiba: a parancs sikertelen"
::msgcat::mcset hu "Number of loose objects" "Elvesztett objektumok sz\u00e1ma"
::msgcat::mcset hu "Disk space used by loose objects" "Elveszett objektumok \u00e1ltal elfoglalt lemezter\u00fclet"
::msgcat::mcset hu "Number of packed objects" "Csomagolt objektumok sz\u00e1mra"
::msgcat::mcset hu "Number of packs" "Csomagok sz\u00e1ma"
::msgcat::mcset hu "Disk space used by packed objects" "A csomagolt objektumok \u00e1ltal haszn\u00e1lt lemezter\u00fclet"
::msgcat::mcset hu "Packed objects waiting for pruning" "Elt\u00e1vol\u00edt\u00e1sra v\u00e1r\u00f3 csomagolt objektumok sz\u00e1mra"
::msgcat::mcset hu "Garbage files" "Hullad\u00e9k f\u00e1jlok"
::msgcat::mcset hu "Compressing the object database" "Az objektum adatb\u00e1zis t\u00f6m\u00f6r\u00edt\u00e9se"
::msgcat::mcset hu "Verifying the object database with fsck-objects" "Az objektum adatb\u00e1zis ellen\u0151rz\u00e9se az fsck-objects haszn\u00e1lat\u00e1val"
::msgcat::mcset hu "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database when more than %i loose objects exist.\n\nCompress the database now?" "Ennek a rep\u00f3nak jelenleg %i k\u00fcl\u00f6n\u00e1ll\u00f3 objektuma van.\n\nAz optim\u00e1lis teljes\u00edtm\u00e9nyhez er\u0151sen aj\u00e1nlott az adatb\u00e1zis t\u00f6m\u00f6r\u00edt\u00e9se, ha t\u00f6bb mint %i objektum l\u00e9tezik.\n\nLehet most t\u00f6m\u00f6r\u00edteni az adatb\u00e1zist?"
::msgcat::mcset hu "Invalid date from Git: %s" "\u00c9rv\u00e9nytelen d\u00e1tum a Git-t\u0151l: %s"
::msgcat::mcset hu "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "Nincsenek v\u00e1ltoz\u00e1sok.\n\nA(z) %s m\u00f3dos\u00edtatlan.\n\nA f\u00e1jl m\u00f3dos\u00edt\u00e1si d\u00e1tum\u00e1t friss\u00edtette egy m\u00e1sik alkalmaz\u00e1s, de a f\u00e1jl tartalma v\u00e1ltozatlan.\n\nEgy \u00fajrakeres\u00e9s fog indulni a hasonl\u00f3 \u00e1llapot\u00fa f\u00e1jlok megtal\u00e1l\u00e1sa \u00e9rdek\u00e9ben."
::msgcat::mcset hu "Loading diff of %s..." "A(z) %s diff-j\u00e9nek bet\u00f6lt\u00e9se..."
::msgcat::mcset hu "LOCAL: deleted\nREMOTE:\n" "HELYI: t\u00f6r\u00f6lve\nT\u00c1VOLI:\n"
::msgcat::mcset hu "REMOTE: deleted\nLOCAL:\n" "T\u00c1VOLI: t\u00f6r\u00f6lve\nHELYI:\n"
::msgcat::mcset hu "LOCAL:\n" "HELYI:\n"
::msgcat::mcset hu "REMOTE:\n" "T\u00c1VOLI:\n"
::msgcat::mcset hu "Unable to display %s" "Nem lehet megjelen\u00edteni a k\u00f6vetkez\u0151t: %s"
::msgcat::mcset hu "Error loading file:" "Hiba a f\u00e1jl bet\u00f6lt\u00e9se k\u00f6zben:"
::msgcat::mcset hu "Git Repository (subproject)" "Git rep\u00f3 (alprojekt)"
::msgcat::mcset hu "* Binary file (not showing content)." "* Bin\u00e1ris f\u00e1jl (tartalom elrejt\u00e9se)."
::msgcat::mcset hu "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* Nem k\u00f6vetett f\u00e1jl %d b\u00e1jttal.\n* Csak az els\u0151 %d b\u00e1jt mutat\u00e1sa.\n"
::msgcat::mcset hu "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* Nem k\u00f6vetett f\u00e1jlt lev\u00e1gta a(z) %s.\n* A teljes tartalom megjelen\u00edt\u00e9s\u00e9hez haszn\u00e1ljunk k\u00fcls\u0151 sz\u00f6vegszerkeszt\u0151t.\n"
::msgcat::mcset hu "Failed to unstage selected hunk." "Nem visszavonni a hunk kiv\u00e1laszt\u00e1s\u00e1t."
::msgcat::mcset hu "Failed to stage selected hunk." "Nem siker\u00fclt kiv\u00e1lasztani a hunkot."
::msgcat::mcset hu "Failed to unstage selected line." "Nem siker\u00fclt visszavonni a sor kiv\u00e1laszt\u00e1s\u00e1t."
::msgcat::mcset hu "Failed to stage selected line." "Nem siker\u00fclt kiv\u00e1lasztani a sort."
::msgcat::mcset hu "Default" "Alap\u00e9rtelmez\u00e9s"
::msgcat::mcset hu "System (%s)" "Rendszer (%s)"
::msgcat::mcset hu "Other" "M\u00e1s"
::msgcat::mcset hu "error" "hiba"
::msgcat::mcset hu "warning" "figyelmeztet\u00e9s"
::msgcat::mcset hu "You must correct the above errors before committing." "Ki kell jav\u00edtanunk a fenti hib\u00e1kat commit el\u0151tt."
::msgcat::mcset hu "Unable to unlock the index." "Nem siker\u00fclt az index z\u00e1rol\u00e1s\u00e1nak felold\u00e1sa."
::msgcat::mcset hu "Index Error" "Index hiba"
::msgcat::mcset hu "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "A Git index friss\u00edt\u00e9se sikertelen volt.  Egy \u00fajraolvas\u00e1s automatikusan elindult, hogy a git-gui \u00fajra szinkonban legyen."
::msgcat::mcset hu "Continue" "Folytat\u00e1s"
::msgcat::mcset hu "Unlock Index" "Index z\u00e1rol\u00e1s\u00e1nak felold\u00e1sa"
::msgcat::mcset hu "Unstaging %s from commit" "A(z) %s commitba val\u00f3 kiv\u00e1laszt\u00e1s\u00e1nak visszavon\u00e1sa"
::msgcat::mcset hu "Ready to commit." "Commitol\u00e1sra k\u00e9sz."
::msgcat::mcset hu "Adding %s" "A(z) %s hozz\u00e1ad\u00e1sa..."
::msgcat::mcset hu "Revert changes in file %s?" "Vissza\u00e1ll\u00edtja a v\u00e1ltoztat\u00e1sokat a(z) %s f\u00e1jlban?"
::msgcat::mcset hu "Revert changes in these %i files?" "Vissza\u00e1ll\u00edtja a v\u00e1ltoztat\u00e1sokat ebben e %i f\u00e1jlban?"
::msgcat::mcset hu "Any unstaged changes will be permanently lost by the revert." "Minden nem kiv\u00e1lasztott v\u00e1ltoztat\u00e1s el fog veszni ez\u00e1ltal a vissza\u00e1ll\u00edt\u00e1s \u00e1ltal."
::msgcat::mcset hu "Do Nothing" "Ne csin\u00e1ljunk semmit"
::msgcat::mcset hu "Reverting selected files" "A kiv\u00e1lasztott f\u00e1jlok vissza\u00e1ll\u00edt\u00e1sa"
::msgcat::mcset hu "Reverting %s" "%s vissza\u00e1ll\u00edt\u00e1sa"
::msgcat::mcset hu "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "Jav\u00edt\u00e1s k\u00f6zben nem lehets\u00e9ges a merge.\n\nEgyszer be kell fejezni ennek a commitnak a jav\u00edt\u00e1s\u00e1t, majd kezd\u0151dhet egy merge.\n"
::msgcat::mcset hu "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "Az utols\u00f3 keres\u00e9si \u00e1llapot nem egyezik meg a rep\u00f3 \u00e1llapot\u00e1val.\n\nEgy m\u00e1sik Git program m\u00f3dos\u00edtotta ezt a rep\u00f3t az utols\u00f3 keres\u00e9s \u00f3ta. Egy \u00fajrakeres\u00e9s mindenk\u00e9ppen sz\u00fcks\u00e9ges miel\u0151tt a jelenlegi branchet m\u00f3dos\u00edtani lehetne.\n\nAz \u00fajrakeres\u00e9s most automatikusan el fog indulni.\n"
::msgcat::mcset hu "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "Jelenleg egy \u00fctk\u00f6z\u00e9s felold\u00e1sa k\u00f6zben vagyunk.\n\nA(z) %s f\u00e1jlban \u00fctk\u00f6z\u00e9sek vannak.\n\nFel kell oldanunk \u0151ket, kiv\u00e1lasztani a f\u00e1jlt, \u00e9s commitolni hogy befejezz\u00fck a jelenlegi merge-t. Csak ezut\u00e1n kezdhet\u00fcnk el egy \u00fajabbat.\n"
::msgcat::mcset hu "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "Jelenleg egy v\u00e1ltoztat\u00e1s k\u00f6zben vagyunk.\n\nA(z) %s f\u00e1jl megv\u00e1ltozott.\n\nEl\u0151sz\u00f6r be kell fejezn\u00fcnk a jelenlegi commitot, hogy elkezdhess\u00fcnk egy merge-t. Ez seg\u00edteni fog, hogy f\u00e9lbeszak\u00edthassunk egy merge-t.\n"
::msgcat::mcset hu "%s of %s" "%s / %s"
::msgcat::mcset hu "Merging %s and %s..." "A(z) %s \u00e9s a(z) %s merge-\u00f6l\u00e9se..."
::msgcat::mcset hu "Merge completed successfully." "A merge sikeresen befejez\u0151d\u00f6tt."
::msgcat::mcset hu "Merge failed.  Conflict resolution is required." "A merge sikertelen. Fel kell oldanunk az \u00fctk\u00f6z\u00e9seket."
::msgcat::mcset hu "Merge Into %s" "Merge-\u00f6l\u00e9s a k\u00f6vetkez\u0151be: %s"
::msgcat::mcset hu "Revision To Merge" "Merge-\u00f6lni sz\u00e1nd\u00e9kozott rev\u00edzi\u00f3"
::msgcat::mcset hu "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "A commit jav\u00edt\u00e1s k\u00f6zben megszak\u00edtva.\n\nBe kell fejezn\u00fcnk ennek a commitnak a jav\u00edt\u00e1s\u00e1t.\n"
::msgcat::mcset hu "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "Megszak\u00edtjuk a merge-t?\n\nA jelenlegi merge megszak\u00edt\u00e1sa *MINDEN* nem commitolt v\u00e1ltoztat\u00e1s elveszt\u00e9s\u00e9t jelenti.\n\nFolytatjuk a jelenlegi merge megszak\u00edt\u00e1s\u00e1t?"
::msgcat::mcset hu "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "Visszavonjuk a m\u00f3dos\u00edt\u00e1sokat?\n\nA m\u00f3dos\u00edt\u00e1sok visszavon\u00e1sa *MINDEN* nem commitolt v\u00e1ltoztat\u00e1s elveszt\u00e9s\u00e9t jelenti.\n\nFolytatjuk a jelenlegi m\u00f3dos\u00edt\u00e1sok visszavon\u00e1s\u00e1t?"
::msgcat::mcset hu "Aborting" "F\u00e9lbeszak\u00edt\u00e1s"
::msgcat::mcset hu "files reset" "f\u00e1jl vissza\u00e1ll\u00edtva"
::msgcat::mcset hu "Abort failed." "A f\u00e9lbeszak\u00edt\u00e1s nem siker\u00fclt."
::msgcat::mcset hu "Abort completed.  Ready." "A megkeszak\u00edt\u00e1s befejez\u0151d\u00f6tt. K\u00e9sz."
::msgcat::mcset hu "Force resolution to the base version?" "Felold\u00e1s er\u0151ltet\u00e9se az alap verzi\u00f3hoz?"
::msgcat::mcset hu "Force resolution to this branch?" "Felold\u00e1s er\u0151ltet\u00e9se ehhez a branch-hez?"
::msgcat::mcset hu "Force resolution to the other branch?" "Felold\u00e1s er\u0151ltet\u00e9se a m\u00e1sik branch-hez?"
::msgcat::mcset hu "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "Megjegyz\u00e9s: csak az \u00fctk\u00f6z\u0151 k\u00fcl\u00f6nbs\u00e9gek l\u00e1tszanak.\n\nA(z) %s fel\u00fcl lesz \u00edrva.\n\nEz a m\u0171velet csak a merge \u00fajraind\u00edt\u00e1s\u00e1val lesz visszavonhat\u00f3."
::msgcat::mcset hu "File %s seems to have unresolved conflicts, still stage?" "A(z) %s f\u00e1jl nem feloldott \u00fctk\u00f6z\u00e9seket tartalmaz, m\u00e9gis legyen kiv\u00e1lasztva?"
::msgcat::mcset hu "Adding resolution for %s" "Felold\u00e1s hozz\u00e1ad\u00e1sa a(z) %s sz\u00e1m\u00e1ra"
::msgcat::mcset hu "Cannot resolve deletion or link conflicts using a tool" "Nem lehet feloldani t\u00f6rl\u00e9si vagy link \u00fctk\u00f6z\u00e9st egy eszk\u00f6zzel"
::msgcat::mcset hu "Conflict file does not exist" "A konfiklus-f\u00e1jl nem l\u00e9tezik."
::msgcat::mcset hu "Not a GUI merge tool: '%s'" "Nem GUI merge eszk\u00f6z: %s"
::msgcat::mcset hu "Unsupported merge tool '%s'" "A(z) '%s' merge eszk\u00f6z nem t\u00e1mogatott"
::msgcat::mcset hu "Merge tool is already running, terminate it?" "A merge eszk\u00f6z m\u00e1r fut, le legyen \u00e1ll\u00edtva?"
::msgcat::mcset hu "Error retrieving versions:\n%s" "Hiba a verzi\u00f3k kinyer\u00e9se k\u00f6zben:\n%s"
::msgcat::mcset hu "Could not start the merge tool:\n\n%s" "A merge eszk\u00f6z ind\u00edt\u00e1sa sikertelen:\n\n%s"
::msgcat::mcset hu "Running merge tool..." "A merge eszk\u00f6z futtat\u00e1sa..."
::msgcat::mcset hu "Merge tool failed." "A merge eszk\u00f6z nem siker\u00fclt."
::msgcat::mcset hu "Invalid global encoding '%s'" "\u00c9rv\u00e9nytelen glob\u00e1lis k\u00f3dol\u00e1s '%s'"
::msgcat::mcset hu "Invalid repo encoding '%s'" "\u00c9rv\u00e9nytelen rep\u00f3 k\u00f3dol\u00e1s '%s'"
::msgcat::mcset hu "Restore Defaults" "Alap\u00e9rtelmez\u00e9s vissza\u00e1ll\u00edt\u00e1sa"
::msgcat::mcset hu "Save" "Ment\u00e9s"
::msgcat::mcset hu "%s Repository" "%s Rep\u00f3"
::msgcat::mcset hu "Global (All Repositories)" "Glob\u00e1lis (minden rep\u00f3)"
::msgcat::mcset hu "User Name" "Felhaszn\u00e1l\u00f3n\u00e9v"
::msgcat::mcset hu "Email Address" "Email c\u00edm"
::msgcat::mcset hu "Summarize Merge Commits" "A merge commitok \u00f6sszegz\u00e9se"
::msgcat::mcset hu "Merge Verbosity" "Merge besz\u00e9dess\u00e9g"
::msgcat::mcset hu "Show Diffstat After Merge" "Diffstat mutat\u00e1sa merge ut\u00e1n"
::msgcat::mcset hu "Use Merge Tool" "Merge eszk\u00f6z haszn\u00e1lata"
::msgcat::mcset hu "Trust File Modification Timestamps" "A f\u00e1jl m\u00f3dos\u00edt\u00e1si d\u00e1tumok megb\u00edzhat\u00f3ak"
::msgcat::mcset hu "Prune Tracking Branches During Fetch" "A k\u00f6vet\u0151 branchek elt\u00e1vol\u00edt\u00e1sa let\u00f6lt\u00e9s alatt"
::msgcat::mcset hu "Match Tracking Branches" "A k\u00f6vet\u0151 branchek egyeztet\u00e9se"
::msgcat::mcset hu "Blame Copy Only On Changed Files" "A blame m\u00e1sol\u00e1s bekapcsol\u00e1sa csak megv\u00e1ltozott f\u00e1jlokra"
::msgcat::mcset hu "Minimum Letters To Blame Copy On" "Minimum bet\u0171sz\u00e1m blame m\u00e1sol\u00e1s-\u00e9rz\u00e9kel\u00e9shez"
::msgcat::mcset hu "Blame History Context Radius (days)" "Blame t\u00f6rt\u00e9net k\u00f6rnyezet sug\u00e1r (napokban)"
::msgcat::mcset hu "Number of Diff Context Lines" "A diff k\u00f6rnyezeti sorok sz\u00e1ma"
::msgcat::mcset hu "Commit Message Text Width" "Commit \u00fczenet sz\u00f6veg\u00e9nek sz\u00e9less\u00e9ge"
::msgcat::mcset hu "New Branch Name Template" "\u00daj branch n\u00e9v sablon"
::msgcat::mcset hu "Default File Contents Encoding" "Alap\u00e9rtelmezett f\u00e1jltartalom-k\u00f3dol\u00e1s"
::msgcat::mcset hu "Change" "Megv\u00e1ltoztat\u00e1s"
::msgcat::mcset hu "Spelling Dictionary:" "Helyes\u00edr\u00e1s-ellen\u0151rz\u0151 sz\u00f3t\u00e1r:"
::msgcat::mcset hu "Change Font" "Bet\u0171t\u00edpus megv\u00e1ltoztat\u00e1sa"
::msgcat::mcset hu "Choose %s" "%s v\u00e1laszt\u00e1sa"
::msgcat::mcset hu "pt." "pt."
::msgcat::mcset hu "Preferences" "Be\u00e1ll\u00edt\u00e1sok"
::msgcat::mcset hu "Failed to completely save options:" "Nem siker\u00fclt teljesen elmenteni a be\u00e1ll\u00edt\u00e1sokat:"
::msgcat::mcset hu "Remove Remote" "Remote elt\u00e1vol\u00edt\u00e1sa"
::msgcat::mcset hu "Prune from" "T\u00f6rl\u00e9s innen"
::msgcat::mcset hu "Fetch from" "Let\u00f6lt\u00e9s innen"
::msgcat::mcset hu "Push to" "Push ide"
::msgcat::mcset hu "Add Remote" "Remote hozz\u00e1ad\u00e1sa"
::msgcat::mcset hu "Add New Remote" "\u00daj remote hozz\u00e1ad\u00e1sa"
::msgcat::mcset hu "Add" "Hozz\u00e1ad\u00e1s"
::msgcat::mcset hu "Remote Details" "Remote r\u00e9szletei"
::msgcat::mcset hu "Location:" "Hely:"
::msgcat::mcset hu "Further Action" "K\u00f6vetkez\u0151 m\u0171velet"
::msgcat::mcset hu "Fetch Immediately" "Let\u00f6lt\u00e9s most"
::msgcat::mcset hu "Initialize Remote Repository and Push" "T\u00e1voli rep\u00f3 inicializ\u00e1l\u00e1sa \u00e9s push"
::msgcat::mcset hu "Do Nothing Else Now" "Ne csin\u00e1ljunk semmit"
::msgcat::mcset hu "Please supply a remote name." "Adjunk megy egy remote nevet."
::msgcat::mcset hu "'%s' is not an acceptable remote name." "A(z) '%s' nem egy elfogadhat\u00f3 remote n\u00e9v."
::msgcat::mcset hu "Failed to add remote '%s' of location '%s'." "Nem siker\u00fclt a(t) '%s' remote hozz\u00e1ad\u00e1sa innen: '%s'."
::msgcat::mcset hu "fetch %s" "a(z) %s let\u00f6lt\u00e9se"
::msgcat::mcset hu "Fetching the %s" "A(z) %s let\u00f6lt\u00e9se"
::msgcat::mcset hu "Do not know how to initialize repository at location '%s'." "Nem tudni, hogy hogy kell a(z) '%s' helyen rep\u00f3t inicializ\u00e1lni."
::msgcat::mcset hu "push %s" "%s push-ol\u00e1sa"
::msgcat::mcset hu "Setting up the %s (at %s)" "A(z) %s be\u00e1ll\u00edt\u00e1sa itt: %s"
::msgcat::mcset hu "Delete Branch Remotely" "T\u00e1voli Branch t\u00f6rl\u00e9se"
::msgcat::mcset hu "From Repository" "Forr\u00e1s rep\u00f3"
::msgcat::mcset hu "Remote:" "T\u00e1voli:"
::msgcat::mcset hu "Arbitrary Location:" "\u00d6nk\u00e9nyes hely:"
::msgcat::mcset hu "Branches" "Branchek"
::msgcat::mcset hu "Delete Only If" "T\u00f6rl\u00e9s csak akkor ha"
::msgcat::mcset hu "Merged Into:" "Merge-\u00f6lt a k\u00f6vetkez\u0151be:"
::msgcat::mcset hu "Always (Do not perform merge checks)" "Mindig (Ne v\u00e9gezzen merge vizsg\u00e1latokat)"
::msgcat::mcset hu "A branch is required for 'Merged Into'." "Egy branch sz\u00fcks\u00e9ges a 'Merge-\u00f6lt a k\u00f6vetkez\u0151be'-hez."
::msgcat::mcset hu "The following branches are not completely merged into %s:\n\n - %s" "A k\u00f6vetkez\u0151 branchek nem teljesen lettek merge-\u00f6lve ebbe: %s:\n - %s"
::msgcat::mcset hu "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "Egy vagy t\u00f6bb merge teszt hib\u00e1t jelzett, mivel nem t\u00f6lt\u00f6tt\u00fck le a megfelel\u0151 commitokat. Pr\u00f3b\u00e1ljunk meg let\u00f6lteni a k\u00f6vetkez\u0151b\u0151l: %s el\u0151sz\u00f6r."
::msgcat::mcset hu "Please select one or more branches to delete." "V\u00e1lasszunk ki egy vagy t\u00f6bb branchet t\u00f6rl\u00e9sre."
::msgcat::mcset hu "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "A t\u00f6r\u00f6lt branchek vissza\u00e1ll\u00edt\u00e1sa neh\u00e9z.\n\nT\u00f6r\u00f6lj\u00fck a kiv\u00e1lasztott brancheket?"
::msgcat::mcset hu "Deleting branches from %s" "Brancek t\u00f6rl\u00e9se innen: %s"
::msgcat::mcset hu "No repository selected." "Nincs kiv\u00e1lasztott rep\u00f3."
::msgcat::mcset hu "Scanning %s..." "Keres\u00e9s itt: %s..."
::msgcat::mcset hu "Find:" "Keres\u00e9s:"
::msgcat::mcset hu "Next" "K\u00f6vetkez\u0151"
::msgcat::mcset hu "Prev" "El\u0151z\u0151"
::msgcat::mcset hu "Case-Sensitive" "Kisbet\u0171-nagybet\u0171 sz\u00e1m\u00edt"
::msgcat::mcset hu "Cannot write shortcut:" "Nem siker\u00fclt \u00edrni a gyorsbillenty\u0171t:"
::msgcat::mcset hu "Cannot write icon:" "Nem siker\u00fclt \u00edrni az ikont:"
::msgcat::mcset hu "Unsupported spell checker" "Nem t\u00e1mogatott helyes\u00edr\u00e1s-ellen\u0151rz\u0151"
::msgcat::mcset hu "Spell checking is unavailable" "A helyes\u00edr\u00e1s-ellen\u0151rz\u00e9s nem el\u00e9rhet\u0151"
::msgcat::mcset hu "Invalid spell checking configuration" "\u00c9rv\u00e9nytelen a helyes\u00edr\u00e1s-ellen\u0151rz\u0151 be\u00e1ll\u00edt\u00e1sa"
::msgcat::mcset hu "Reverting dictionary to %s." "Sz\u00f3t\u00e1r vissza\u00e1ll\u00edt\u00e1sa a k\u00f6vetkez\u0151re: %s."
::msgcat::mcset hu "Spell checker silently failed on startup" "A helyes\u00edr\u00e1s-ellen\u0151r\u0151 ind\u00edt\u00e1sa sikertelen"
::msgcat::mcset hu "Unrecognized spell checker" "Ismeretlen helyes\u00edr\u00e1s-ellen\u0151rz\u0151"
::msgcat::mcset hu "No Suggestions" "Nincs javaslat"
::msgcat::mcset hu "Unexpected EOF from spell checker" "Nem v\u00e1rt EOF a helyes\u00edr\u00e1s-ellen\u0151rz\u0151t\u0151l"
::msgcat::mcset hu "Spell Checker Failed" "A helyes\u00edr\u00e1s-ellen\u0151rz\u00e9s sikertelen"
::msgcat::mcset hu "No keys found." "Nincsenek kulcsok."
::msgcat::mcset hu "Found a public key in: %s" "Nyilv\u00e1nos kulcs tal\u00e1lhat\u00f3 ebben: %s"
::msgcat::mcset hu "Generate Key" "Kulcs gener\u00e1l\u00e1sa"
::msgcat::mcset hu "Copy To Clipboard" "M\u00e1sol\u00e1s v\u00e1g\u00f3lapra"
::msgcat::mcset hu "Your OpenSSH Public Key" "Az OpenSSH publikus kulcsunk"
::msgcat::mcset hu "Generating..." "Gener\u00e1l\u00e1s..."
::msgcat::mcset hu "Could not start ssh-keygen:\n\n%s" "Az ssh-keygen ind\u00edt\u00e1sa sikertelen:\n\n%s"
::msgcat::mcset hu "Generation failed." "A gener\u00e1l\u00e1s nem siker\u00fclt."
::msgcat::mcset hu "Generation succeeded, but no keys found." "A gener\u00e1l\u00e1s sikeres, de egy kulcs se tal\u00e1lhat\u00f3."
::msgcat::mcset hu "Your key is in: %s" "A kulcsunk itt van: %s"
::msgcat::mcset hu "%s ... %*i of %*i %s (%3i%%)" "%s ... %*i / %*i %s (%3i%%)"
::msgcat::mcset hu "Running %s requires a selected file." "A(z) %s futtat\u00e1sa egy kiv\u00e1lasztott f\u00e1jlt ig\u00e9nyel."
::msgcat::mcset hu "Are you sure you want to run %s?" "Biztos benne, hogy futtatni k\u00edv\u00e1nja: %s?"
::msgcat::mcset hu "Tool: %s" "Eszk\u00f6z: %s"
::msgcat::mcset hu "Running: %s" "Futtat\u00e1s: %s..."
::msgcat::mcset hu "Tool completed successfully: %s" "Az eszk\u00f6z sikeresen befejez\u0151d\u00f6tt: %s"
::msgcat::mcset hu "Tool failed: %s" "Az eszk\u00f6z sikertelen: %s"
::msgcat::mcset hu "Add Tool" "Eszk\u00f6z hozz\u00e1ad\u00e1sa"
::msgcat::mcset hu "Add New Tool Command" "\u00daj eszk\u00f6z-parancs hozz\u00e1ad\u00e1sa"
::msgcat::mcset hu "Add globally" "Glob\u00e1lis hozz\u00e1ad\u00e1s"
::msgcat::mcset hu "Tool Details" "Eszk\u00f6z r\u00e9szletei"
::msgcat::mcset hu "Use '/' separators to create a submenu tree:" "Haszn\u00e1ljunk '/' szepar\u00e1torokat almen\u00fc-fa l\u00e9trehoz\u00e1s\u00e1hoz:"
::msgcat::mcset hu "Command:" "Parancs:"
::msgcat::mcset hu "Show a dialog before running" "Parancsablak mutat\u00e1sa futtat\u00e1s el\u0151tt"
::msgcat::mcset hu "Ask the user to select a revision (sets \$REVISION)" "Megk\u00e9ri a felhaszn\u00e1l\u00f3t, hogy v\u00e1lasszon ki egy rev\u00edzi\u00f3t (a \$REVISION-t \u00e1ll\u00edtja)"
::msgcat::mcset hu "Ask the user for additional arguments (sets \$ARGS)" "Megk\u00e9rdezi a felhaszn\u00e1l\u00f3t tov\u00e1bbi argumentumok\u00e9rt (a \$ARGS-ot \u00e1ll\u00edtja)"
::msgcat::mcset hu "Don't show the command output window" "Ne mutassa a parancs kimeneti ablak\u00e1t"
::msgcat::mcset hu "Run only if a diff is selected (\$FILENAME not empty)" "Futtat\u00e1s csak ha egy diff ki van v\u00e1lasztva (a \$FILENAME nem \u00fcres)"
::msgcat::mcset hu "Please supply a name for the tool." "Adjunk meg egy eszk\u00f6z nevet."
::msgcat::mcset hu "Tool '%s' already exists." "A(z) '%s' eszk\u00f6z m\u00e1r l\u00e9tezik."
::msgcat::mcset hu "Could not add tool:\n%s" "Az eszk\u00f6z nem hozz\u00e1adhat\u00f3:\n%s"
::msgcat::mcset hu "Remove Tool" "Eszk\u00f6z elt\u00e1vol\u00edt\u00e1sa"
::msgcat::mcset hu "Remove Tool Commands" "Eszk\u00f6z parancsok elt\u00e1vol\u00edt\u00e1sa"
::msgcat::mcset hu "Remove" "Elt\u00e1vol\u00edt\u00e1s"
::msgcat::mcset hu "(Blue denotes repository-local tools)" "(K\u00e9k jelzi a rep\u00f3-specifikus eszk\u00f6z\u00f6ket)"
::msgcat::mcset hu "Run Command: %s" "Parancs futtat\u00e1sa: %s"
::msgcat::mcset hu "Arguments" "Argumentumok"
::msgcat::mcset hu "OK" "OK"
::msgcat::mcset hu "Fetching new changes from %s" "\u00daj v\u00e1ltoz\u00e1sok let\u00f6lt\u00e9se innen: %s"
::msgcat::mcset hu "remote prune %s" "a(z) %s t\u00e1voli t\u00f6rl\u00e9se"
::msgcat::mcset hu "Pruning tracking branches deleted from %s" "A %s rep\u00f3b\u00f3l t\u00f6r\u00f6lt k\u00f6vet\u0151 branchek t\u00f6rl\u00e9se"
::msgcat::mcset hu "Pushing changes to %s" "V\u00e1ltoz\u00e1sok pushol\u00e1sa ide: %s"
::msgcat::mcset hu "Mirroring to %s" "T\u00fckr\u00f6z\u00e9s a k\u00f6vetkez\u0151 helyre: %s"
::msgcat::mcset hu "Pushing %s %s to %s" "Pushol\u00e1s: %s %s, ide: %s"
::msgcat::mcset hu "Push Branches" "Branchek pushol\u00e1sa"
::msgcat::mcset hu "Source Branches" "Forr\u00e1s branchek"
::msgcat::mcset hu "Destination Repository" "C\u00e9l rep\u00f3"
::msgcat::mcset hu "Transfer Options" "\u00c1tviteli opci\u00f3k"
::msgcat::mcset hu "Force overwrite existing branch (may discard changes)" "L\u00e9tez\u0151 branch fel\u00fcl\u00edr\u00e1s\u00e1nak er\u0151ltet\u00e9se (lehet, hogy el fog dobni v\u00e1ltoztat\u00e1sokat)"
::msgcat::mcset hu "Use thin pack (for slow network connections)" "V\u00e9kony csomagok haszn\u00e1lata (lass\u00fa h\u00e1l\u00f3zati kapcsolatok sz\u00e1m\u00e1ra)"
::msgcat::mcset hu "Include tags" "Tageket is"
