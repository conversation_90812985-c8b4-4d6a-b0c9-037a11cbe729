<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>getfacl</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="getconf.html" title="getconf"><link rel="next" href="gmondump.html" title="gmondump"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">getfacl</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="getconf.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="gmondump.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="getfacl"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>getfacl &#8212; Display file and directory access control lists (ACLs)</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">getfacl</code>  [-adceEn]  <em class="replaceable"><code>FILE</code></em>... </p></div><div class="cmdsynopsis"><p><code class="command">getfacl</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="getfacl-options"></a><h2>Options</h2><pre class="screen">
  -a, --access        display the file access control list only
  -d, --default       display the default access control list only
  -c, --omit-header   do not display the comment header
  -e, --all-effective print all effective rights
  -E, --no-effective  print no effective rights
  -n, --numeric       print numeric user/group identifiers
  -V, --version       print version and exit
  -h, --help          this help text

When multiple files are specified on the command line, a blank
line separates the ACLs for each file.
</pre></div><div class="refsect1"><a name="getfacl-desc"></a><h2>Description</h2><p> For each argument that is a regular file, special file or directory,
      <span class="command"><strong>getfacl</strong></span> displays the owner, the group, and the ACL.
      For directories <span class="command"><strong>getfacl</strong></span> displays additionally the
      default ACL. With no options specified, <span class="command"><strong>getfacl</strong></span>
      displays the filename, the owner, the group, the setuid (s), setgid (s),
      and sticky (t) bits if available, and both the ACL and the default ACL,
      if it exists. For more information on Cygwin and Windows ACLs, see
      <a class="xref" href="ntsec.html" title="POSIX accounts, permission, and security">the section called &#8220;POSIX accounts, permission, and security&#8221;</a> in the Cygwin User's Guide. The format
      for ACL output is as follows:
      </p><pre class="screen">
     # file: filename
     # owner: name or uid
     # group: name or uid
     # flags: sst
     user::perm
     user:name or uid:perm
     group::perm
     group:name or gid:perm
     mask:perm
     other:perm
     default:user::perm
     default:user:name or uid:perm
     default:group::perm
     default:group:name or gid:perm
     default:mask:perm
     default:other:perm
</pre><p>
    </p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="getconf.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="gmondump.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">getconf&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;gmondump</td></tr></table></div></body></html>
