CMAKE_CURRENT_FUNCTION_LIST_LINE
--------------------------------

.. versionadded:: 3.17

When executing code inside a :command:`function`, this variable
contains the line number in the listfile where the current function
was defined.

See also :variable:`CMAKE_CURRENT_FUNCTION`,
:variable:`CMAKE_CURRENT_FUNCTION_LIST_DIR`,
:variable:`CMAKE_CURRENT_FUNCTION_LIST_FILE` and
:variable:`CMAKE_CURRENT_LIST_LINE`.
