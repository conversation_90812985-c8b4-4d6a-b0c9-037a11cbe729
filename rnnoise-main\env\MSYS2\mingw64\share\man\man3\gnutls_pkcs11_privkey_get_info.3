.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_privkey_get_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_privkey_get_info \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_privkey_get_info(gnutls_pkcs11_privkey_t " pkey ", gnutls_pkcs11_obj_info_t " itype ", void * " output ", size_t * " output_size ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_privkey_t pkey" 12
should contain a \fBgnutls_pkcs11_privkey_t\fP type
.IP "gnutls_pkcs11_obj_info_t itype" 12
Denotes the type of information requested
.IP "void * output" 12
where output will be stored
.IP "size_t * output_size" 12
contains the maximum size of the output and will be overwritten with actual
.SH "DESCRIPTION"
This function will return information about the PKCS 11 private key such
as the label, id as well as token information where the key is stored. When
output is text it returns null terminated string although \fBoutput_size\fP contains
the size of the actual data only.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success or a negative error code on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
