.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_get_authority_key_gn_serial" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_get_authority_key_gn_serial \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_get_authority_key_gn_serial(gnutls_x509_crl_t " crl ", unsigned int " seq ", void * " alt ", size_t * " alt_size ", unsigned int * " alt_type ", void * " serial ", size_t * " serial_size ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
should contain a \fBgnutls_x509_crl_t\fP type
.IP "unsigned int seq" 12
specifies the sequence number of the alt name (0 for the first one, 1 for the second etc.)
.IP "void * alt" 12
is the place where the alternative name will be copied to
.IP "size_t * alt_size" 12
holds the size of alt.
.IP "unsigned int * alt_type" 12
holds the type of the alternative name (one of gnutls_x509_subject_alt_name_t).
.IP "void * serial" 12
buffer to store the serial number (may be null)
.IP "size_t * serial_size" 12
Holds the size of the serial field (may be null)
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical (may be null)
.SH "DESCRIPTION"
This function will return the X.509 authority key
identifier when stored as a general name (authorityCertIssuer) 
and serial number.

Because more than one general names might be stored
 \fIseq\fP can be used as a counter to request them all until 
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned.
.SH "RETURNS"
Returns 0 on success, or an error code.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
