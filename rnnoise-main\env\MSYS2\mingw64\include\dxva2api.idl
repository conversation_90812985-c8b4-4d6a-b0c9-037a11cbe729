/*
 * Copyright 2014 <PERSON> for Pi<PERSON>ight
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "unknwn.idl";

cpp_quote("#if 0")
typedef DWORD IDirect3DDevice9;
typedef DWORD IDirect3DSurface9;

typedef DWORD D3DFORMAT;
typedef DWORD D3DPOOL;
cpp_quote("#endif")

/* MPEG2 */
cpp_quote("DEFINE_GUID(DXVA2_ModeMPEG2_MoComp, 0xe6a9f44b, 0x61b0,0x4563, 0x9e,0xa4,0x63,0xd2,0xa3,0xc6,0xfe,0x66);")
cpp_quote("#define DXVA2_ModeMPEG2_MOCOMP      DXVA2_ModeMPEG2_MoComp")

cpp_quote("DEFINE_GUID(DXVA2_ModeMPEG2_IDCT,   0xbf22ad00, 0x03ea,0x4690, 0x80,0x77,0x47,0x33,0x46,0x20,0x9b,0x7e);")

cpp_quote("DEFINE_GUID(DXVA2_ModeMPEG2_VLD,    0xee27417f, 0x5e28,0x4e65, 0xbe,0xea,0x1d,0x26,0xb5,0x08,0xad,0xc9);")

cpp_quote("DEFINE_GUID(DXVA2_ModeMPEG1_VLD,    0x6f3ec719, 0x3735,0x42cc, 0x80,0x63,0x65,0xcc,0x3c,0xb3,0x66,0x16);")

cpp_quote("DEFINE_GUID(DXVA2_ModeMPEG2and1_VLD, 0x86695f12, 0x340e,0x4f04, 0x9f,0xd3,0x92,0x53,0xdd,0x32,0x74,0x60);")

/* H264 */
cpp_quote("DEFINE_GUID(DXVA2_ModeH264_A,       0x1b81be64, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeH264_MoComp_NoFGT DXVA2_ModeH264_A")

cpp_quote("DEFINE_GUID(DXVA2_ModeH264_B,       0x1b81be65, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeH264_MoComp_FGT   DXVA2_ModeH264_B")

cpp_quote("DEFINE_GUID(DXVA2_ModeH264_C,       0x1b81be66, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeH264_IDCT_NoFGT   DXVA2_ModeH264_C")

cpp_quote("DEFINE_GUID(DXVA2_ModeH264_D,       0x1b81be67, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeH264_IDCT_FGT     DXVA2_ModeH264_D")

cpp_quote("DEFINE_GUID(DXVA2_ModeH264_E,       0x1b81be68, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeH264_VLD_NoFGT    DXVA2_ModeH264_E")

cpp_quote("DEFINE_GUID(DXVA2_ModeH264_F,       0x1b81be69, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeH264_VLD_FGT      DXVA2_ModeH264_F")

cpp_quote("DEFINE_GUID(DXVA2_ModeH264_VLD_WithFMOASO_NoFGT,         0xd5f04ff9, 0x3418,0x45d8, 0x95,0x61,0x32,0xa7,0x6a,0xae,0x2d,0xdd);")

cpp_quote("DEFINE_GUID(DXVA2_ModeH264_VLD_Stereo_Progressive_NoFGT, 0xd79be8da, 0x0cf1,0x4c81, 0xb8,0x2a,0x69,0xa4,0xe2,0x36,0xf4,0x3d);")
cpp_quote("DEFINE_GUID(DXVA2_ModeH264_VLD_Stereo_NoFGT,             0xf9aaccbb, 0xc2b6,0x4cfc, 0x87,0x79,0x57,0x07,0xb1,0x76,0x05,0x52);")
cpp_quote("DEFINE_GUID(DXVA2_ModeH264_VLD_Multiview_NoFGT,          0x705b9d82, 0x76cf,0x49d6, 0xb7,0xe6,0xac,0x88,0x72,0xdb,0x01,0x3c);")

/* WMV8 */
cpp_quote("DEFINE_GUID(DXVA2_ModeWMV8_A,       0x1b81be80, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeWMV8_PostProc     DXVA2_ModeWMV8_A")

cpp_quote("DEFINE_GUID(DXVA2_ModeWMV8_B,       0x1b81be81, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeWMV8_MoComp       DXVA2_ModeWMV8_B")

/* WMV9 */
cpp_quote("DEFINE_GUID(DXVA2_ModeWMV9_A,       0x1b81be90, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeWMV9_PostProc     DXVA2_ModeWMV9_A")

cpp_quote("DEFINE_GUID(DXVA2_ModeWMV9_B,       0x1b81be91, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeWMV9_MoComp       DXVA2_ModeWMV9_B")

cpp_quote("DEFINE_GUID(DXVA2_ModeWMV9_C,       0x1b81be94, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeWMV9_IDCT         DXVA2_ModeWMV9_C")

/* VC1 */
cpp_quote("DEFINE_GUID(DXVA2_ModeVC1_A,        0x1b81beA0, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeVC1_PostProc      DXVA2_ModeVC1_A")

cpp_quote("DEFINE_GUID(DXVA2_ModeVC1_B,        0x1b81beA1, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeVC1_MoComp        DXVA2_ModeVC1_B")

cpp_quote("DEFINE_GUID(DXVA2_ModeVC1_C,        0x1b81beA2, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeVC1_IDCT          DXVA2_ModeVC1_C")

cpp_quote("DEFINE_GUID(DXVA2_ModeVC1_D,        0x1b81beA3, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("#define DXVA2_ModeVC1_VLD           DXVA2_ModeVC1_D")

cpp_quote("DEFINE_GUID(DXVA2_ModeVC1_D2010,    0x1b81bea4, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")

/* Encryption */
cpp_quote("DEFINE_GUID(DXVA2_NoEncrypt,        0x1b81bed0, 0xa0c7,0x11d3, 0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")

cpp_quote("DEFINE_GUID(DXVA2_VideoProcProgressiveDevice, 0x5a54a0c9, 0xc7ec,0x4bd9, 0x8e,0xde,0xf3,0xc7,0x5d,0xc4,0x39,0x3b);")
cpp_quote("DEFINE_GUID(DXVA2_VideoProcBobDevice,         0x335aa36e, 0x7884,0x43a4, 0x9c,0x91,0x7f,0x87,0xfa,0xf3,0xe3,0x7e);")
cpp_quote("DEFINE_GUID(DXVA2_VideoProcSoftwareDevice,    0x4553d47f, 0xee7e,0x4e3f, 0x94,0x75,0xdb,0xf1,0x37,0x6c,0x48,0x10);")

cpp_quote("DEFINE_GUID(DXVA2_ModeMPEG4pt2_VLD_Simple,           0xefd64d74, 0xc9e8,0x41d7, 0xa5,0xe9,0xe9,0xb0,0xe3,0x9f,0xa3,0x19);")
cpp_quote("DEFINE_GUID(DXVA2_ModeMPEG4pt2_VLD_AdvSimple_NoGMC,  0xed418a9f, 0x010d,0x4eda, 0x9a,0xe3,0x9a,0x65,0x35,0x8d,0x8d,0x2e);")
cpp_quote("DEFINE_GUID(DXVA2_ModeMPEG4pt2_VLD_AdvSimple_GMC,    0xab998b5b, 0x4258,0x44a9, 0x9f,0xeb,0x94,0xe5,0x97,0xa6,0xba,0xae);")

cpp_quote("DEFINE_GUID(DXVA2_ModeHEVC_VLD_Main,    0x5b11d51b, 0x2f4c,0x4452, 0xbc,0xc3,0x09,0xf2,0xa1,0x16,0x0c,0xc0);")
cpp_quote("DEFINE_GUID(DXVA2_ModeHEVC_VLD_Main10,  0x107af0e0, 0xef1a,0x4d19, 0xab,0xa8,0x67,0xa1,0x63,0x07,0x3d,0x13);")

cpp_quote("DEFINE_GUID(DXVA2_ModeVP9_VLD_Profile0,       0x463707f8, 0xa1d0,0x4585, 0x87,0x6d,0x83,0xaa,0x6d,0x60,0xb8,0x9e);")
cpp_quote("DEFINE_GUID(DXVA2_ModeVP9_VLD_10bit_Profile2, 0xa4c749ef, 0x6ecf,0x48aa, 0x84,0x48,0x50,0xa7,0xa1,0x16,0x5f,0xf7);")
cpp_quote("DEFINE_GUID(DXVA2_ModeVP8_VLD,                0x90b899ea, 0x3a62,0x4705, 0x88,0xb3,0x8d,0xf0,0x4b,0x27,0x44,0xe7);")

cpp_quote("#ifndef REFERENCE_TIME_DEFINED")
cpp_quote("#define REFERENCE_TIME_DEFINED")
typedef LONGLONG REFERENCE_TIME;
cpp_quote("#endif")

/* DXVA2-specific error codes */
cpp_quote("#define DXVA2_E_NOT_INITIALIZED     ((HRESULT)0x80041000)")
cpp_quote("#define DXVA2_E_NEW_VIDEO_DEVICE    ((HRESULT)0x80041001)")
cpp_quote("#define DXVA2_E_VIDEO_DEVICE_LOCKED ((HRESULT)0x80041002)")
cpp_quote("#define DXVA2_E_NOT_AVAILABLE       ((HRESULT)0x80041003)")

enum
{
    DXVA2_PictureParametersBufferType,
    DXVA2_MacroBlockControlBufferType,
    DXVA2_ResidualDifferenceBufferType,
    DXVA2_DeblockingControlBufferType,
    DXVA2_InverseQuantizationMatrixBufferType,
    DXVA2_SliceControlBufferType,
    DXVA2_BitStreamDateBufferType,
    DXVA2_MotionVectorBuffer,
    DXVA2_FilmGrainBuffer
};

enum
{
    DXVA2_VideoDecoderRenderTarget,
    DXVA2_VideoProcessorRenderTarget,
    DXVA2_VideoSoftwareRenderTarget
};

typedef struct _DXVA2_ExtendedFormat
{
    union
    {
        struct
        {
            UINT SampleFormat           :8;
            UINT VideoChromaSubsampling :4;
            UINT NominalRange           :3;
            UINT VideoTransferMatrix    :3;
            UINT VideoLighting          :4;
            UINT VideoPrimaries         :5;
            UINT VideoTransferFunction  :5;
        };
        UINT value;
    };
} DXVA2_ExtendedFormat;

typedef enum _DXVA2_SampleFormat
{
    DXVA2_SampleFormatMask                = 0x00FF,
    DXVA2_SampleUnknown                   = 0,
    DXVA2_SampleProgressiveFrame          = 2,
    DXVA2_SampleFieldInterleavedEvenFirst = 3,
    DXVA2_SampleFieldInterleavedOddFirst  = 4,
    DXVA2_SampleFieldSingleEven           = 5,
    DXVA2_SampleFieldSingleOdd            = 6,
    DXVA2_SampleSubStream                 = 7
} DXVA2_SampleFormat;

typedef enum _DXVA2_VideoChromaSubSampling
{
    DXVA2_VideoChromaSubsamplingMask                            = 0x0F,
    DXVA2_VideoChromaSubsampling_Unknown                        = 0,
    DXVA2_VideoChromaSubsampling_ProgressiveChroma              = 0x8,
    DXVA2_VideoChromaSubsampling_Horizontally_Cosited           = 0x4,
    DXVA2_VideoChromaSubsampling_Vertically_Cosited             = 0x2,
    DXVA2_VideoChromaSubsampling_Vertically_AlignedChromaPlanes = 0x1,
    DXVA2_VideoChromaSubsampling_MPEG2   = DXVA2_VideoChromaSubsampling_Horizontally_Cosited |
                                           DXVA2_VideoChromaSubsampling_Vertically_AlignedChromaPlanes,
    DXVA2_VideoChromaSubsampling_MPEG1   = DXVA2_VideoChromaSubsampling_Vertically_AlignedChromaPlanes,
    DXVA2_VideoChromaSubsampling_DV_PAL  = DXVA2_VideoChromaSubsampling_Horizontally_Cosited |
                                           DXVA2_VideoChromaSubsampling_Vertically_Cosited,
    DXVA2_VideoChromaSubsampling_Cosited = DXVA2_VideoChromaSubsampling_Horizontally_Cosited |
                                           DXVA2_VideoChromaSubsampling_Vertically_Cosited |
                                           DXVA2_VideoChromaSubsampling_Vertically_AlignedChromaPlanes
} DXVA2_VideoChromaSubSampling;

typedef enum _DXVA2_NominalRange
{
    DXVA2_NominalRangeMask      = 0x07,
    DXVA2_NominalRange_Unknown  = 0,
    DXVA2_NominalRange_Normal   = 1,
    DXVA2_NominalRange_Wide     = 2,
    DXVA2_NominalRange_0_255    = 1,
    DXVA2_NominalRange_16_235   = 2,
    DXVA2_NominalRange_48_208   = 3
} DXVA2_NominalRange;

typedef enum _DXVA2_VideoTransferMatrix
{
    DXVA2_VideoTransferMatrixMask       = 0x07,
    DXVA2_VideoTransferMatrix_Unknown   = 0,
    DXVA2_VideoTransferMatrix_BT709     = 1,
    DXVA2_VideoTransferMatrix_BT601     = 2,
    DXVA2_VideoTransferMatrix_SMPTE240M = 3
} DXVA2_VideoTransferMatrix;

typedef enum _DXVA2_VideoLighting
{
    DXVA2_VideoLightingMask     = 0x0F,
    DXVA2_VideoLighting_Unknown = 0,
    DXVA2_VideoLighting_bright  = 1,
    DXVA2_VideoLighting_office  = 2,
    DXVA2_VideoLighting_dim     = 3,
    DXVA2_VideoLighting_dark    = 4
} DXVA2_VideoLighting;

typedef enum _DXVA2_VideoPrimaries
{
    DXVA2_VideoPrimariesMask            = 0x001f,
    DXVA2_VideoPrimaries_Unknown        = 0,
    DXVA2_VideoPrimaries_reserved       = 1,
    DXVA2_VideoPrimaries_BT709          = 2,
    DXVA2_VideoPrimaries_BT470_2_SysM   = 3,
    DXVA2_VideoPrimaries_BT470_2_SysBG  = 4,
    DXVA2_VideoPrimaries_SMPTE170M      = 5,
    DXVA2_VideoPrimaries_SMPTE240M      = 6,
    DXVA2_VideoPrimaries_EBU3213        = 7,
    DXVA2_VideoPrimaries_SMPTE_C        = 8
} DXVA2_VideoPrimaries;

typedef enum _DXVA2_VideoTransferFunction
{
    DXVA2_VideoTransFuncMask     = 0x001f,
    DXVA2_VideoTransFunc_Unknown = 0,
    DXVA2_VideoTransFunc_10      = 1,
    DXVA2_VideoTransFunc_18      = 2,
    DXVA2_VideoTransFunc_20      = 3,
    DXVA2_VideoTransFunc_22      = 4,
    DXVA2_VideoTransFunc_709     = 5,
    DXVA2_VideoTransFunc_240M    = 6,
    DXVA2_VideoTransFunc_sRGB    = 7,
    DXVA2_VideoTransFunc_28      = 8
} DXVA2_VideoTransferFunction;

cpp_quote("#define DXVA2_VideoTransFunc_22_709         DXVA2_VideoTransFunc_709")
cpp_quote("#define DXVA2_VideoTransFunc_22_240M        DXVA2_VideoTransFunc_240M")
cpp_quote("#define DXVA2_VideoTransFunc_22_8bit_sRGB   DXVA2_VideoTransFunc_sRGB")

typedef struct _DXVA2_Frequency
{
    UINT Numerator;
    UINT Denominator;
} DXVA2_Frequency;

typedef struct _DXVA2_ConfigPictureDecode
{
    GUID guidConfigBitstreamEncryption;
    GUID guidConfigMBcontrolEncryption;
    GUID guidConfigResidDiffEncryption;
    UINT ConfigBitstreamRaw;
    UINT ConfigMBcontrolRasterOrder;
    UINT ConfigResidDiffHost;
    UINT ConfigSpatialResid8;
    UINT ConfigResid8Subtraction;
    UINT ConfigSpatialHost8or9Clipping;
    UINT ConfigSpatialResidInterleaved;
    UINT ConfigIntraResidUnsigned;
    UINT ConfigResidDiffAccelerator;
    UINT ConfigHostInverseScan;
    UINT ConfigSpecificIDCT;
    UINT Config4GroupedCoefs;
    UINT ConfigMinRenderTargetBuffCount;
    USHORT ConfigDecoderSpecific;
} DXVA2_ConfigPictureDecode;

typedef struct _DXVA2_VideoDesc
{
    UINT SampleWidth;
    UINT SampleHeight;
    DXVA2_ExtendedFormat SampleFormat;
    D3DFORMAT Format;
    DXVA2_Frequency InputSampleFreq;
    DXVA2_Frequency OutputFrameFreq;
    UINT UABProtectionLevel;
    UINT Reserved;
} DXVA2_VideoDesc;

enum
{
    DXVA2_DeinterlaceTech_Unknown                = 0x0000,
    DXVA2_DeinterlaceTech_BOBLineReplicate       = 0x0001,
    DXVA2_DeinterlaceTech_BOBVerticalStretch     = 0x0002,
    DXVA2_DeinterlaceTech_BOBVerticalStretch4Tap = 0x0004,
    DXVA2_DeinterlaceTech_MedianFiltering        = 0x0008,
    DXVA2_DeinterlaceTech_EdgeFiltering          = 0x0010,
    DXVA2_DeinterlaceTech_FieldAdaptive          = 0x0020,
    DXVA2_DeinterlaceTech_PixelAdaptive          = 0x0040,
    DXVA2_DeinterlaceTech_MotionVectorSteered    = 0x0080,
    DXVA2_DeinterlaceTech_InverseTelecine        = 0x0100,
    DXVA2_DeinterlaceTech_Mask                   = 0x01ff
};

enum
{
    DXVA2_NoiseFilterLumaLevel          = 1,
    DXVA2_NoiseFilterLumaThreshold      = 2,
    DXVA2_NoiseFilterLumaRadius         = 3,
    DXVA2_NoiseFilterChromaLevel        = 4,
    DXVA2_NoiseFilterChromaThreshold    = 5,
    DXVA2_NoiseFilterChromaRadius       = 6,
    DXVA2_DetailFilterLumaLevel         = 7,
    DXVA2_DetailFilterLumaThreshold     = 8,
    DXVA2_DetailFilterLumaRadius        = 9,
    DXVA2_DetailFilterChromaLevel       = 10,
    DXVA2_DetailFilterChromaThreshold   = 11,
    DXVA2_DetailFilterChromaRadius      = 12
};

enum
{
    DXVA2_NoiseFilterTech_Unsupported   = 0,
    DXVA2_NoiseFilterTech_Unknown       = 0x0001,
    DXVA2_NoiseFilterTech_Median        = 0x0002,
    DXVA2_NoiseFilterTech_Temporal      = 0x0004,
    DXVA2_NoiseFilterTech_BlockNoise    = 0x0008,
    DXVA2_NoiseFilterTech_MosquitoNoise = 0x0010,
    DXVA2_NoiseFilterTech_Mask          = 0x001f
};

enum
{
    DXVA2_DetailFilterTech_Unsupported  = 0,
    DXVA2_DetailFilterTech_Unknown      = 0x0001,
    DXVA2_DetailFilterTech_Edge         = 0x0002,
    DXVA2_DetailFilterTech_Sharpening   = 0x0004,
    DXVA2_DetailFilterTech_Mask         = 0x0007
};

enum
{
    DXVA2_ProcAmp_None          = 0x0000,
    DXVA2_ProcAmp_Brightness    = 0x0001,
    DXVA2_ProcAmp_Contrast      = 0x0002,
    DXVA2_ProcAmp_Hue           = 0x0004,
    DXVA2_ProcAmp_Saturation    = 0x0008,
    DXVA2_ProcAmp_Mask          = 0x000f
};

enum
{
    DXVA2_VideoProcess_None                         = 0x0000,
    DXVA2_VideoProcess_YUV2RGB                      = 0x0001,
    DXVA2_VideoProcess_StretchX                     = 0x0002,
    DXVA2_VideoProcess_StretchY                     = 0x0004,
    DXVA2_VideoProcess_AlphaBlend                   = 0x0008,
    DXVA2_VideoProcess_SubRects                     = 0x0010,
    DXVA2_VideoProcess_SubStreams                   = 0x0020,
    DXVA2_VideoProcess_SubStreamsExtended           = 0x0040,
    DXVA2_VideoProcess_YUV2RGBExtended              = 0x0080,
    DXVA2_VideoProcess_AlphaBlendExtended           = 0x0100,
    DXVA2_VideoProcess_Constriction                 = 0x0200,
    DXVA2_VideoProcess_NoiseFilter                  = 0x0400,
    DXVA2_VideoProcess_DetailFilter                 = 0x0800,
    DXVA2_VideoProcess_PlanarAlpha                  = 0x1000,
    DXVA2_VideoProcess_LinearScaling                = 0x2000,
    DXVA2_VideoProcess_GammaCompensated             = 0x4000,
    DXVA2_VideoProcess_MaintainsOriginalFieldData   = 0x8000,
    DXVA2_VideoProcess_Mask                         = 0xffff
};

enum
{
    DXVA2_VPDev_HardwareDevice  = 0x0001,
    DXVA2_VPDev_EmulatedDXVA1   = 0x0002,
    DXVA2_VPDev_SoftwareDevice  = 0x0004,
    DXVA2_VPDev_Mask            = 0x0007
};

enum
{
    DXVA2_SampleData_RFF             = 0x0001,
    DXVA2_SampleData_TFF             = 0x0002,
    DXVA2_SampleData_RFF_TFF_Present = 0x0004,
    DXVA2_SampleData_Mask            = 0xffff
};

enum
{
    DXVA2_DestData_RFF               = 0x0001,
    DXVA2_DestData_TFF               = 0x0002,
    DXVA2_DestData_RFF_TFF_Present   = 0x0004,
    DXVA2_DestData_Mask              = 0xffff
};

typedef struct _DXVA2_DecodeBufferDesc
{
    DWORD CompressedBufferType;
    UINT BufferIndex;
    UINT DataOffset;
    UINT DataSize;
    UINT FirstMBaddress;
    UINT NumMBsInBuffer;
    UINT Width;
    UINT Height;
    UINT Stride;
    UINT ReservedBits;
    PVOID pvPVPState;
} DXVA2_DecodeBufferDesc;

typedef struct _DXVA2_AES_CTR_IV
{
    UINT64 IV;
    UINT64 Count;
} DXVA2_AES_CTR_IV;

typedef struct _DXVA2_DecodeExtensionData
{
    UINT Function;
    PVOID pPrivateInputData;
    UINT PrivateInputDataSize;
    PVOID pPrivateOutputData;
    UINT PrivateOutputDataSize;
} DXVA2_DecodeExtensionData;

typedef struct _DXVA2_DecodeExecuteParams
{
    UINT NumCompBuffers;
    DXVA2_DecodeBufferDesc* pCompressedBuffers;
    DXVA2_DecodeExtensionData* pExtensionData;
} DXVA2_DecodeExecuteParams;

typedef struct _DXVA2_VideoProcessorCaps
{
    UINT DeviceCaps;
    D3DPOOL InputPool;
    UINT NumForwardRefSamples;
    UINT NumBackwardRefSamples;
    UINT Reserved;
    UINT DeinterlaceTechnology;
    UINT ProcAmpControlCaps;
    UINT VideoProcessorOperations;
    UINT NoiseFilterTechnology;
    UINT DetailFilterTechnology;
} DXVA2_VideoProcessorCaps;

typedef struct _DXVA2_Fixed32
{
    union
    {
        struct
        {
            USHORT Fraction;
            SHORT Value;
        };
        LONG ll;
    };
} DXVA2_Fixed32;

typedef struct _DXVA2_ValueRange
{
    DXVA2_Fixed32 MinValue;
    DXVA2_Fixed32 MaxValue;
    DXVA2_Fixed32 DefaultValue;
    DXVA2_Fixed32 StepSize;
} DXVA2_ValueRange;

typedef struct _DXVA2_AYUVSample8
{
    UCHAR Cr;
    UCHAR Cb;
    UCHAR Y;
    UCHAR Alpha;
} DXVA2_AYUVSample8;

typedef struct _DXVA2_AYUVSample16
{
    USHORT Cr;
    USHORT Cb;
    USHORT Y;
    USHORT Alpha;
} DXVA2_AYUVSample16;

typedef struct _DXVA2_ProcAmpValues
{
    DXVA2_Fixed32 Brightness;
    DXVA2_Fixed32 Contrast;
    DXVA2_Fixed32 Hue;
    DXVA2_Fixed32 Saturation;
} DXVA2_ProcAmpValues;

typedef struct _DXVA2_FilterValues
{
    DXVA2_Fixed32 Level;
    DXVA2_Fixed32 Threshold;
    DXVA2_Fixed32 Radius;
} DXVA2_FilterValues;

typedef struct _DXVA2_VideoProcessBltParams
{
    REFERENCE_TIME TargetFrame;
    RECT TargetRect;
    SIZE ConstrictionSize;
    UINT StreamingFlags;
    DXVA2_AYUVSample16 BackgroundColor;
    DXVA2_ExtendedFormat DestFormat;
    DXVA2_ProcAmpValues ProcAmpValues;
    DXVA2_Fixed32 Alpha;
    DXVA2_FilterValues NoiseFilterLuma;
    DXVA2_FilterValues NoiseFilterChroma;
    DXVA2_FilterValues DetailFilterLuma;
    DXVA2_FilterValues DetailFilterChroma;
    DWORD DestData;
} DXVA2_VideoProcessBltParams;

cpp_quote("#ifdef _D3D9_H_")

typedef struct _DXVA2_VideoSample
{
    REFERENCE_TIME Start;
    REFERENCE_TIME End;
    DXVA2_ExtendedFormat SampleFormat;
    IDirect3DSurface9* SrcSurface;
    RECT SrcRect;
    RECT DstRect;
    DXVA2_AYUVSample8 Pal[16];
    DXVA2_Fixed32 PlanarAlpha;
    DWORD SampleData;
} DXVA2_VideoSample;

typedef enum
{
    DXVA2_SurfaceType_DecoderRenderTarget,
    DXVA2_SurfaceType_ProcessorRenderTarget,
    DXVA2_SurfaceType_D3DRenderTargetTexture,
} DXVA2_SurfaceType;

interface IDirect3DDeviceManager9;
interface IDirectXVideoAccelerationService;
interface IDirectXVideoDecoderService;
interface IDirectXVideoProcessorService;
interface IDirectXVideoDecoder;
interface IDirectXVideoProcessor;

/*****************************************************************************
 * IDirect3DDeviceManager9 interface
 */
[
    object,
    uuid(a0cade0f-06d5-4cf4-a1c7-f3cdd725aa75),
    local
]
interface IDirect3DDeviceManager9 : IUnknown
{
    HRESULT ResetDevice(
        [in] IDirect3DDevice9* pDevice,
        [in] UINT resetToken);

    HRESULT OpenDeviceHandle(
        [out] HANDLE* phDevice);

    HRESULT CloseDeviceHandle(
        [in] HANDLE hDevice);

    HRESULT TestDevice(
        [in] HANDLE hDevice);

    HRESULT LockDevice(
        [in] HANDLE hDevice,
        [out] IDirect3DDevice9** ppDevice,
        [in] BOOL fBlock);

    HRESULT UnlockDevice(
        [in] HANDLE hDevice,
        [in] BOOL fSaveState);

    HRESULT GetVideoService(
        [in] HANDLE hDevice,
        [in] REFIID riid,
        [out] void** ppService);
}

/*****************************************************************************
 * IDirectXVideoAccelerationService interface
 */
[
    object,
    uuid(fc51a550-d5e7-11d9-af55-00054e43ff02),
    local
]
interface IDirectXVideoAccelerationService : IUnknown
{
    HRESULT CreateSurface(
        [in] UINT width,
        [in] UINT height,
        [in] UINT backBuffers,
        [in] D3DFORMAT format,
        [in] D3DPOOL pool,
        [in] DWORD usage,
        [in] DWORD dxvaType,
        [out] IDirect3DSurface9 **ppSurface,
        [in, out] HANDLE *pSharedHandle);
}

/*****************************************************************************
 * IDirectXVideoDecoderService interface
 */
[
    object,
    uuid(fc51a551-d5e7-11d9-af55-00054e43ff02),
    local
]
interface IDirectXVideoDecoderService : IDirectXVideoAccelerationService
{
    HRESULT GetDecoderDeviceGuids(
        [out] UINT *count,
        [out] GUID **pGuids);

    HRESULT GetDecoderRenderTargets(
        [in] REFGUID guid,
        [out] UINT *pCount,
        [out] D3DFORMAT **pFormats);

    HRESULT GetDecoderConfigurations(
        [in] REFGUID guid,
        [in] const DXVA2_VideoDesc *pVideoDesc,
        [in] void *pReserved,
        [out] UINT *pCount,
        [out] DXVA2_ConfigPictureDecode **ppConfigs);

    HRESULT CreateVideoDecoder(
        [in] REFGUID guid,
        [in] const DXVA2_VideoDesc *pVideoDesc,
        [in] const DXVA2_ConfigPictureDecode *pConfig,
        [in] IDirect3DSurface9 **ppDecoderRenderTargets,
        [in] UINT NumSurfaces,
        [out] IDirectXVideoDecoder **ppDecode);
}

/*****************************************************************************
 * IDirectXVideoDecoder interface
 */
[
    object,
    uuid(f2b0810a-fd00-43c9-918c-df94e2d8ef7d),
    local
]
interface IDirectXVideoDecoder : IUnknown
{
    HRESULT GetVideoDecoderService(
        [out] IDirectXVideoDecoderService** ppService);

    HRESULT GetCreationParameters(
        [out] GUID* pDeviceGuid,
        [out] DXVA2_VideoDesc* pVideoDesc,
        [out] DXVA2_ConfigPictureDecode* pConfig,
        [out] IDirect3DSurface9*** pDecoderRenderTargets,
        [out] UINT* pNumSurfaces);

    HRESULT GetBuffer(
        [in] UINT BufferType,
        [out] void** ppBuffer,
        [out] UINT* pBufferSize);

    HRESULT ReleaseBuffer(
        [in] UINT BufferType);

    HRESULT BeginFrame(
        [in] IDirect3DSurface9* pRenderTarget,
        [in] void* pvPVPData);

    HRESULT EndFrame(
        [out] HANDLE* pHandleComplete);

    HRESULT Execute(
        [in] const DXVA2_DecodeExecuteParams* pExecuteParams);
}

/*****************************************************************************
 * IDirectXVideoProcessorService interface
 */
[
    object,
    uuid(fc51a552-d5e7-11d9-af55-00054e43ff02),
    local
]
interface IDirectXVideoProcessorService : IDirectXVideoAccelerationService
{
    HRESULT RegisterVideoProcessorSoftwareDevice(
        [in] void* pCallbacks);

    HRESULT GetVideoProcessorDeviceGuids(
        [in] const DXVA2_VideoDesc* pVideoDesc,
        [out] UINT* pCount,
        [out] GUID** pGuids);

    HRESULT GetVideoProcessorRenderTargets(
        [in] REFGUID VideoProcDeviceGuid,
        [in] const DXVA2_VideoDesc* pVideoDesc,
        [out] UINT* pCount,
        [out] D3DFORMAT** pFormats);

    HRESULT GetVideoProcessorSubStreamFormats(
        [in] REFGUID VideoProcDeviceGuid,
        [in] const DXVA2_VideoDesc* pVideoDesc,
        [in] D3DFORMAT RenderTargetFormat,
        [out] UINT* pCount,
        [out] D3DFORMAT** pFormats);

    HRESULT GetVideoProcessorCaps(
        [in] REFGUID VideoProcDeviceGuid,
        [in] const DXVA2_VideoDesc* pVideoDesc,
        [in] D3DFORMAT RenderTargetFormat,
        [out] DXVA2_VideoProcessorCaps* pCaps);

    HRESULT GetProcAmpRange(
        [in] REFGUID VideoProcDeviceGuid,
        [in] const DXVA2_VideoDesc* pVideoDesc,
        [in] D3DFORMAT RenderTargetFormat,
        [in] UINT ProcAmpCap,
        [out] DXVA2_ValueRange* pRange);

    HRESULT GetFilterPropertyRange(
        [in] REFGUID VideoProcDeviceGuid,
        [in] const DXVA2_VideoDesc* pVideoDesc,
        [in] D3DFORMAT renderTargetFormat,
        [in] UINT FilterSetting,
        [out] DXVA2_ValueRange* pRange);

    HRESULT CreateVideoProcessor(
        [in] REFGUID VideoProcDeviceGuid,
        [in] const DXVA2_VideoDesc* pVideoDesc,
        [in] D3DFORMAT RenderTargetFormat,
        [in] UINT MaxNumSubStreams,
        [out] IDirectXVideoProcessor** ppVidProcess);
}

/*****************************************************************************
 * IDirectXVideoProcessor interface
 */
[
    object,
    uuid(8c3a39f0-916e-4690-804f-4c8001355d25),
    local
]
interface IDirectXVideoProcessor : IUnknown
{
    HRESULT GetVideoProcessorService(
        [out] IDirectXVideoProcessorService** ppService);

    HRESULT GetCreationParameters(
        [out] GUID* pDeviceGuid,
        [out] DXVA2_VideoDesc* pVideoDesc,
        [out] D3DFORMAT* pRenderTargetFormat,
        [out] UINT* pMaxNumSubStreams);

    HRESULT GetVideoProcessorCaps(
        [out] DXVA2_VideoProcessorCaps* pCaps);

    HRESULT GetProcAmpRange(
        [in] UINT ProcAmpCap,
        [out] DXVA2_ValueRange* pRange);

    HRESULT GetFilterPropertyRange(
        [in] UINT FilterSetting,
        [out] DXVA2_ValueRange* pRange);

    HRESULT VideoProcessBlt(
        [in] IDirect3DSurface9* pRenderTarget,
        [in] const DXVA2_VideoProcessBltParams* pBltParams,
        [in] const DXVA2_VideoSample* pSamples,
        [in] UINT NumSamples,
        [out] HANDLE* pHandleComplete);
}

/*****************************************************************************
 * IDirectXVideoMemoryConfiguration interface
 */
[
    object,
    uuid(b7f916dd-db3b-49c1-84d7-e45ef99ec726),
    local
]
interface IDirectXVideoMemoryConfiguration : IUnknown
{
    HRESULT GetAvailableSurfaceTypeByIndex(
        [in] DWORD wTypeIndex,
        [out] DXVA2_SurfaceType *pdwType);

    HRESULT SetSurfaceType(
        [in] DXVA2_SurfaceType dwType);
}

cpp_quote("HRESULT WINAPI DXVA2CreateDirect3DDeviceManager9(UINT *pResetToken,IDirect3DDeviceManager9 **ppDXVAManager);")
cpp_quote("HRESULT WINAPI DXVA2CreateVideoService(IDirect3DDevice9 *pDD,REFIID riid,void **ppService);")

cpp_quote("#endif")

cpp_quote("static inline DXVA2_Fixed32 DXVA2_Fixed32OpaqueAlpha(void) {")
cpp_quote("  DXVA2_Fixed32 f32;")
cpp_quote("  f32.ll = 0 + (1 << 16);")
cpp_quote("  return f32;")
cpp_quote("}")
cpp_quote("")
cpp_quote("static inline DXVA2_Fixed32 DXVA2_Fixed32TransparentAlpha(void) {")
cpp_quote("  DXVA2_Fixed32 f32;")
cpp_quote("  f32.ll = 0;")
cpp_quote("  return f32;")
cpp_quote("}")
cpp_quote("")
cpp_quote("static inline float DXVA2FixedToFloat(DXVA2_Fixed32 f32) {")
cpp_quote("  return (float)f32.Value + (float)f32.Fraction / (1 << 16);")
cpp_quote("}")
cpp_quote("")
cpp_quote("static inline DXVA2_Fixed32 DXVA2FloatToFixed(float f) {")
cpp_quote("  DXVA2_Fixed32 f32;")
cpp_quote("  f32.Value    = ((ULONG) (f * (1 << 16))) >> 16;")
cpp_quote("  f32.Fraction = ((ULONG) (f * (1 << 16))) & 0xFFFF;")
cpp_quote("  return f32;")
cpp_quote("}")
