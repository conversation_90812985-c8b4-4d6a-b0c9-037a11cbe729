<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_get0_peer_rpk</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_add_expected_rpk, SSL_get_negotiated_client_cert_type, SSL_get_negotiated_server_cert_type, SSL_get0_peer_rpk, SSL_SESSION_get0_peer_rpk - raw public key (RFC7250) support</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

int SSL_add_expected_rpk(SSL *s, EVP_PKEY *rpk);
int SSL_get_negotiated_client_cert_type(const SSL *s);
int SSL_get_negotiated_server_cert_type(const SSL *s);
EVP_PKEY *SSL_get0_peer_rpk(const SSL *s);
EVP_PKEY *SSL_SESSION_get0_peer_rpk(const SSL_SESSION *ss);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_add_expected_rpk() adds a DANE TLSA record matching public key <b>rpk</b> to SSL <b>s</b>&#39;s DANE validation policy.</p>

<p>SSL_get_negotiated_client_cert_type() returns the connection&#39;s negotiated client certificate type.</p>

<p>SSL_get_negotiated_server_cert_type() returns the connection&#39;s negotiated server certificate type.</p>

<p>SSL_get0_peer_rpk() returns the peer&#39;s raw public key from SSL <b>s</b>.</p>

<p>SSL_SESSION_get0_peer_rpk() returns the peer&#39;s raw public key from SSL_SESSION <b>ss</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>Raw public keys are used in place of certificates when the option is negotiated. <b>SSL_add_expected_rpk()</b> may be called multiple times to configure multiple trusted keys, this makes it possible to allow for key rotation, where a peer might be expected to offer an &quot;old&quot; or &quot;new&quot; key and the endpoint must be able to accept either one.</p>

<p>When raw public keys are used, the certificate verify callback is called, and may be used to inspect the public key via X509_STORE_CTX_get0_rpk(3). Raw public keys have no subject, issuer, validity dates nor digital signature to verify. They can, however, be matched verbatim or by their digest value, this is done by specifying one or more TLSA records, see <a href="../man3/SSL_CTX_dane_enable.html">SSL_CTX_dane_enable(3)</a>.</p>

<p>The raw public key is typically taken from the certificate assigned to the connection (e.g. via <a href="../man3/SSL_use_certificate.html">SSL_use_certificate(3)</a>), but if a certificate is not configured, then the public key will be extracted from the assigned private key.</p>

<p>The SSL_add_expected_rpk() function is a wrapper around <a href="../man3/SSL_dane_tlsa_add.html">SSL_dane_tlsa_add(3)</a>. When DANE is enabled via <a href="../man3/SSL_dane_enable.html">SSL_dane_enable(3)</a>, the configured TLSA records will be used to validate the peer&#39;s public key or certificate. If DANE is not enabled, then no validation will occur.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_add_expected_rpk() returns 1 on success and 0 on failure.</p>

<p>SSL_get0_peer_rpk() and SSL_SESSION_get0_peer_rpk() return the peer&#39;s raw public key as an EVP_PKEY or NULL when the raw public key is not available.</p>

<p>SSL_get_negotiated_client_cert_type() and SSL_get_negotiated_server_cert_type() return one of the following values:</p>

<dl>

<dt id="TLSEXT_cert_type_x509">TLSEXT_cert_type_x509</dt>
<dd>

</dd>
<dt id="TLSEXT_cert_type_rpk">TLSEXT_cert_type_rpk</dt>
<dd>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/SSL_CTX_dane_enable.html">SSL_CTX_dane_enable(3)</a>, <a href="../man3/SSL_CTX_set_options.html">SSL_CTX_set_options(3)</a>, <a href="../man3/SSL_dane_enable.html">SSL_dane_enable(3)</a>, <a href="../man3/SSL_get_verify_result.html">SSL_get_verify_result(3)</a>, <a href="../man3/SSL_set_verify.html">SSL_set_verify(3)</a>, <a href="../man3/SSL_use_certificate.html">SSL_use_certificate(3)</a>, <a href="../man3/X509_STORE_CTX_get0_rpk.html">X509_STORE_CTX_get0_rpk(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 3.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.</p>


</body>

</html>


