<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_set_incoming_stream_policy</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_set_incoming_stream_policy, SSL_INCOMING_STREAM_POLICY_AUTO, SSL_INCOMING_STREAM_POLICY_ACCEPT, SSL_INCOMING_STREAM_POLICY_REJECT - manage the QUIC incoming stream policy</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

#define SSL_INCOMING_STREAM_POLICY_AUTO
#define SSL_INCOMING_STREAM_POLICY_ACCEPT
#define SSL_INCOMING_STREAM_POLICY_REJECT

int SSL_set_incoming_stream_policy(SSL *conn, int policy,
                                          uint64_t app_error_code);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_set_incoming_stream_policy() policy changes the incoming stream policy for a QUIC connection. Depending on the policy configured, OpenSSL QUIC may automatically reject incoming streams initiated by the peer. This is intended to ensure that legacy applications using single-stream operation with a default stream on a QUIC connection SSL object are not passed remotely-initiated streams by a peer which those applications are not prepared to handle.</p>

<p><i>app_error_code</i> is an application error code which will be used in any QUIC <b>STOP_SENDING</b> or <b>RESET_STREAM</b> frames generated to implement the policy. The default application error code is 0.</p>

<p>The valid values for <i>policy</i> are:</p>

<dl>

<dt id="SSL_INCOMING_STREAM_POLICY_AUTO">SSL_INCOMING_STREAM_POLICY_AUTO</dt>
<dd>

<p>This is the default setting. Incoming streams are accepted according to the following rules:</p>

<ul>

<li><p>If the default stream mode (configured using <a href="../man3/SSL_set_default_stream_mode.html">SSL_set_default_stream_mode(3)</a>) is set to <b>SSL_DEFAULT_STREAM_MODE_AUTO_BIDI</b> (the default) or <b>SSL_DEFAULT_STREAM_MODE_AUTO_UNI</b>, the incoming stream is rejected.</p>

</li>
<li><p>Otherwise (where the default stream mode is <b>SSL_DEFAULT_STREAM_MODE_NONE</b>), the application is assumed to be stream aware, and the incoming stream is accepted.</p>

</li>
</ul>

</dd>
<dt id="SSL_INCOMING_STREAM_POLICY_ACCEPT">SSL_INCOMING_STREAM_POLICY_ACCEPT</dt>
<dd>

<p>Always accept incoming streams, allowing them to be dequeued using <a href="../man3/SSL_accept_stream.html">SSL_accept_stream(3)</a>.</p>

</dd>
<dt id="SSL_INCOMING_STREAM_POLICY_REJECT">SSL_INCOMING_STREAM_POLICY_REJECT</dt>
<dd>

<p>Always reject incoming streams.</p>

</dd>
</dl>

<p>Where an incoming stream is rejected, it is rejected immediately and it is not possible to gain access to the stream using <a href="../man3/SSL_accept_stream.html">SSL_accept_stream(3)</a>. The stream is rejected using QUIC <b>STOP_SENDING</b> and <b>RESET_STREAM</b> frames as appropriate.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>Returns 1 on success and 0 on failure.</p>

<p>This function fails if called on a QUIC stream SSL object, or on a non-QUIC SSL object.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/SSL_set_default_stream_mode.html">SSL_set_default_stream_mode(3)</a>, <a href="../man3/SSL_accept_stream.html">SSL_accept_stream(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>SSL_set_incoming_stream_policy() was added in OpenSSL 3.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2002-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


