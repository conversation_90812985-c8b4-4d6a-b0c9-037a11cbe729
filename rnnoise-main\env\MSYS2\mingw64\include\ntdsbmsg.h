/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _NTDSBMSG_
#define _NTDSBMSG_

#define FACILITY_SYSTEM 0x0
#define FACILITY_NTDSB 0x800
#define FACILITY_BACKUP 0x7FF

#define STATUS_SEVERITY_WARNING 0x2
#define STATUS_SEVERITY_SUCCESS 0x0
#define STATUS_SEVERITY_INFORMATIONAL 0x1
#define STATUS_SEVERITY_ERROR 0x3

#define hrNone ((HRESULT)0x00000000)
#define hrNyi ((HRESULT)0xC0000001)
#define hrInvalidParam ((HRESULT)0xC7FF0001)
#define hrError ((HRESULT)0xC7FF0002)
#define hrInvalidHandle ((HRESULT)0xC7FF0003)
#define hrRestoreInProgress ((HRESULT)0xC7FF0004)
#define hrAlreadyOpen ((HRESULT)0xC7FF0005)
#define hrInvalidRecips ((HRESULT)0xC7FF0006)
#define hrCouldNotConnect ((HRESULT)0xC7FF0007)
#define hrRestoreMapExists ((HRESULT)0xC7FF0008)
#define hrIncrementalBackupDisabled ((HRESULT)0xC7FF0009)
#define hrLogFileNotFound ((HRESULT)0xC7FF000A)
#define hrCircularLogging ((HRESULT)0xC7FF000B)
#define hrNoFullRestore ((HRESULT)0xC7FF000C)
#define hrCommunicationError ((HRESULT)0xC7FF000D)
#define hrFullBackupNotTaken ((HRESULT)0xC7FF000E)
#define hrMissingExpiryToken ((HRESULT)0xC7FF000F)
#define hrUnknownExpiryTokenFormat ((HRESULT)0xC7FF0010)
#define hrContentsExpired ((HRESULT)0xC7FF0011)
#define hrAlreadyListening ((HRESULT)RPC_S_ALREADY_LISTENING)
#define hrFileClose ((HRESULT)0xC8000066)
#define hrOutOfThreads ((HRESULT)0xC8000067)
#define hrTooManyIO ((HRESULT)0xC8000069)
#define hrBFNotSynchronous ((HRESULT)0x880000C8)
#define hrBFPageNotFound ((HRESULT)0x880000C9)
#define hrBFInUse ((HRESULT)0xC80000CA)
#define hrPMRecDeleted ((HRESULT)0xC800012E)
#define hrRemainingVersions ((HRESULT)0x88000141)
#define hrFLDKeyTooBig ((HRESULT)0x88000190)
#define hrFLDTooManySegments ((HRESULT)0xC8000191)
#define hrFLDNullKey ((HRESULT)0x88000192)
#define hrLogFileCorrupt ((HRESULT)0xC80001F5)
#define hrNoBackupDirectory ((HRESULT)0xC80001F7)
#define hrBackupDirectoryNotEmpty ((HRESULT)0xC80001F8)
#define hrBackupInProgress ((HRESULT)0xC80001F9)
#define hrMissingPreviousLogFile ((HRESULT)0xC80001FD)
#define hrLogWriteFail ((HRESULT)0xC80001FE)
#define hrBadLogVersion ((HRESULT)0xC8000202)
#define hrInvalidLogSequence ((HRESULT)0xC8000203)
#define hrLoggingDisabled ((HRESULT)0xC8000204)
#define hrLogBufferTooSmall ((HRESULT)0xC8000205)
#define hrLogSequenceEnd ((HRESULT)0xC8000207)
#define hrNoBackup ((HRESULT)0xC8000208)
#define hrInvalidBackupSequence ((HRESULT)0xC8000209)
#define hrBackupNotAllowedYet ((HRESULT)0xC800020B)
#define hrDeleteBackupFileFail ((HRESULT)0xC800020C)
#define hrMakeBackupDirectoryFail ((HRESULT)0xC800020D)
#define hrInvalidBackup ((HRESULT)0xC800020E)
#define hrRecoveredWithErrors ((HRESULT)0xC800020F)
#define hrMissingLogFile ((HRESULT)0xC8000210)
#define hrLogDiskFull ((HRESULT)0xC8000211)
#define hrBadLogSignature ((HRESULT)0xC8000212)
#define hrBadDbSignature ((HRESULT)0xC8000213)
#define hrBadCheckpointSignature ((HRESULT)0xC8000214)
#define hrCheckpointCorrupt ((HRESULT)0xC8000215)
#define hrDatabaseInconsistent ((HRESULT)0xC8000226)
#define hrConsistentTimeMismatch ((HRESULT)0xC8000227)
#define hrPatchFileMismatch ((HRESULT)0xC8000228)
#define hrRestoreLogTooLow ((HRESULT)0xC8000229)
#define hrRestoreLogTooHigh ((HRESULT)0xC800022A)
#define hrGivenLogFileHasBadSignature ((HRESULT)0xC800022B)
#define hrGivenLogFileIsNotContiguous ((HRESULT)0xC800022C)
#define hrMissingRestoreLogFiles ((HRESULT)0xC800022D)
#define hrExistingLogFileHasBadSignature ((HRESULT)0x8800022E)
#define hrExistingLogFileIsNotContiguous ((HRESULT)0x8800022F)
#define hrMissingFullBackup ((HRESULT)0xC8000230)
#define hrBadBackupDatabaseSize ((HRESULT)0xC8000231)
#define hrTermInProgress ((HRESULT)0xC80003E8)
#define hrFeatureNotAvailable ((HRESULT)0xC80003E9)
#define hrInvalidName ((HRESULT)0xC80003EA)
#define hrInvalidParameter ((HRESULT)0xC80003EB)
#define hrColumnNull ((HRESULT)0x880003EC)
#define hrBufferTruncated ((HRESULT)0x880003EE)
#define hrDatabaseAttached ((HRESULT)0x880003EF)
#define hrInvalidDatabaseId ((HRESULT)0xC80003F2)
#define hrOutOfMemory ((HRESULT)0xC80003F3)
#define hrOutOfDatabaseSpace ((HRESULT)0xC80003F4)
#define hrOutOfCursors ((HRESULT)0xC80003F5)
#define hrOutOfBuffers ((HRESULT)0xC80003F6)
#define hrTooManyIndexes ((HRESULT)0xC80003F7)
#define hrTooManyKeys ((HRESULT)0xC80003F8)
#define hrRecordDeleted ((HRESULT)0xC80003F9)
#define hrReadVerifyFailure ((HRESULT)0xC80003FA)
#define hrOutOfFileHandles ((HRESULT)0xC80003FC)
#define hrDiskIO ((HRESULT)0xC80003FE)
#define hrInvalidPath ((HRESULT)0xC80003FF)
#define hrRecordTooBig ((HRESULT)0xC8000402)
#define hrTooManyOpenDatabases ((HRESULT)0xC8000403)
#define hrInvalidDatabase ((HRESULT)0xC8000404)
#define hrNotInitialized ((HRESULT)0xC8000405)
#define hrAlreadyInitialized ((HRESULT)0xC8000406)
#define hrFileAccessDenied ((HRESULT)0xC8000408)
#define hrBufferTooSmall ((HRESULT)0xC800040E)
#define hrSeekNotEqual ((HRESULT)0x8800040F)
#define hrTooManyColumns ((HRESULT)0xC8000410)
#define hrContainerNotEmpty ((HRESULT)0xC8000413)
#define hrInvalidFilename ((HRESULT)0xC8000414)
#define hrInvalidBookmark ((HRESULT)0xC8000415)
#define hrColumnInUse ((HRESULT)0xC8000416)
#define hrInvalidBufferSize ((HRESULT)0xC8000417)
#define hrColumnNotUpdatable ((HRESULT)0xC8000418)
#define hrIndexInUse ((HRESULT)0xC800041B)
#define hrNullKeyDisallowed ((HRESULT)0xC800041D)
#define hrNotInTransaction ((HRESULT)0xC800041E)
#define hrNoIdleActivity ((HRESULT)0x88000422)
#define hrTooManyActiveUsers ((HRESULT)0xC8000423)
#define hrInvalidCountry ((HRESULT)0xC8000425)
#define hrInvalidLanguageId ((HRESULT)0xC8000426)
#define hrInvalidCodePage ((HRESULT)0xC8000427)
#define hrNoWriteLock ((HRESULT)0x8800042B)
#define hrColumnSetNull ((HRESULT)0x8800042C)
#define hrVersionStoreOutOfMemory ((HRESULT)0xC800042D)
#define hrCurrencyStackOutOfMemory ((HRESULT)0xC800042E)
#define hrOutOfSessions ((HRESULT)0xC800044D)
#define hrWriteConflict ((HRESULT)0xC800044E)
#define hrTransTooDeep ((HRESULT)0xC800044F)
#define hrInvalidSesid ((HRESULT)0xC8000450)
#define hrSessionWriteConflict ((HRESULT)0xC8000453)
#define hrInTransaction ((HRESULT)0xC8000454)
#define hrDatabaseDuplicate ((HRESULT)0xC80004B1)
#define hrDatabaseInUse ((HRESULT)0xC80004B2)
#define hrDatabaseNotFound ((HRESULT)0xC80004B3)
#define hrDatabaseInvalidName ((HRESULT)0xC80004B4)
#define hrDatabaseInvalidPages ((HRESULT)0xC80004B5)
#define hrDatabaseCorrupted ((HRESULT)0xC80004B6)
#define hrDatabaseLocked ((HRESULT)0xC80004B7)
#define hrTableEmpty ((HRESULT)0x88000515)
#define hrTableLocked ((HRESULT)0xC8000516)
#define hrTableDuplicate ((HRESULT)0xC8000517)
#define hrTableInUse ((HRESULT)0xC8000518)
#define hrObjectNotFound ((HRESULT)0xC8000519)
#define hrCannotRename ((HRESULT)0xC800051A)
#define hrDensityInvalid ((HRESULT)0xC800051B)
#define hrTableNotEmpty ((HRESULT)0xC800051C)
#define hrInvalidTableId ((HRESULT)0xC800051E)
#define hrTooManyOpenTables ((HRESULT)0xC800051F)
#define hrIllegalOperation ((HRESULT)0xC8000520)
#define hrObjectDuplicate ((HRESULT)0xC8000522)
#define hrInvalidObject ((HRESULT)0xC8000524)
#define hrIndexCantBuild ((HRESULT)0xC8000579)
#define hrIndexHasPrimary ((HRESULT)0xC800057A)
#define hrIndexDuplicate ((HRESULT)0xC800057B)
#define hrIndexNotFound ((HRESULT)0xC800057C)
#define hrIndexMustStay ((HRESULT)0xC800057D)
#define hrIndexInvalidDef ((HRESULT)0xC800057E)
#define hrIndexHasClustered ((HRESULT)0xC8000580)
#define hrCreateIndexFailed ((HRESULT)0x88000581)
#define hrTooManyOpenIndexes ((HRESULT)0xC8000582)
#define hrColumnLong ((HRESULT)0xC80005DD)
#define hrColumnDoesNotFit ((HRESULT)0xC80005DF)
#define hrNullInvalid ((HRESULT)0xC80005E0)
#define hrColumnIndexed ((HRESULT)0xC80005E1)
#define hrColumnTooBig ((HRESULT)0xC80005E2)
#define hrColumnNotFound ((HRESULT)0xC80005E3)
#define hrColumnDuplicate ((HRESULT)0xC80005E4)
#define hrColumn2ndSysMaint ((HRESULT)0xC80005E6)
#define hrInvalidColumnType ((HRESULT)0xC80005E7)
#define hrColumnMaxTruncated ((HRESULT)0x880005E8)
#define hrColumnCannotIndex ((HRESULT)0xC80005E9)
#define hrTaggedNotNULL ((HRESULT)0xC80005EA)
#define hrNoCurrentIndex ((HRESULT)0xC80005EB)
#define hrKeyIsMade ((HRESULT)0xC80005EC)
#define hrBadColumnId ((HRESULT)0xC80005ED)
#define hrBadItagSequence ((HRESULT)0xC80005EE)
#define hrCannotBeTagged ((HRESULT)0xC80005F1)
#define hrRecordNotFound ((HRESULT)0xC8000641)
#define hrNoCurrentRecord ((HRESULT)0xC8000643)
#define hrRecordClusteredChanged ((HRESULT)0xC8000644)
#define hrKeyDuplicate ((HRESULT)0xC8000645)
#define hrAlreadyPrepared ((HRESULT)0xC8000647)
#define hrKeyNotMade ((HRESULT)0xC8000648)
#define hrUpdateNotPrepared ((HRESULT)0xC8000649)
#define hrwrnDataHasChanged ((HRESULT)0x8800064A)
#define hrerrDataHasChanged ((HRESULT)0xC800064B)
#define hrKeyChanged ((HRESULT)0x88000652)
#define hrTooManySorts ((HRESULT)0xC80006A5)
#define hrInvalidOnSort ((HRESULT)0xC80006A6)
#define hrTempFileOpenError ((HRESULT)0xC800070B)
#define hrTooManyAttachedDatabases ((HRESULT)0xC800070D)
#define hrDiskFull ((HRESULT)0xC8000710)
#define hrPermissionDenied ((HRESULT)0xC8000711)
#define hrFileNotFound ((HRESULT)0xC8000713)
#define hrFileOpenReadOnly ((HRESULT)0x88000715)
#define hrAfterInitialization ((HRESULT)0xC800073A)
#define hrLogCorrupted ((HRESULT)0xC800073C)
#define hrInvalidOperation ((HRESULT)0xC8000772)
#define hrAccessDenied ((HRESULT)0xC8000773)

#endif
