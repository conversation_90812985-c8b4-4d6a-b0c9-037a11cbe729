.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_recv_seq" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_recv_seq \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_recv_seq(gnutls_session_t " session ", void * " data ", size_t " data_size ", unsigned char * " seq ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "void * data" 12
the buffer that the data will be read into
.IP "size_t data_size" 12
the number of requested bytes
.IP "unsigned char * seq" 12
is the packet's 64\-bit sequence number. Should have space for 8 bytes.
.SH "DESCRIPTION"
This function is the same as \fBgnutls_record_recv()\fP, except that
it returns in addition to data, the sequence number of the data.
This is useful in DTLS where record packets might be received
out\-of\-order. The returned 8\-byte sequence number is an
integer in big\-endian format and should be
treated as a unique message identification.
.SH "RETURNS"
The number of bytes received and zero on EOF.  A negative
error code is returned in case of an error.  The number of bytes
received might be less than  \fIdata_size\fP .
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
