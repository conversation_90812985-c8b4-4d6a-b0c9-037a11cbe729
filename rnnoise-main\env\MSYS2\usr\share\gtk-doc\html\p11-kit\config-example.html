<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Example: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="config.html" title="PKCS#11 Configuration">
<link rel="prev" href="config.html" title="PKCS#11 Configuration">
<link rel="next" href="config-files.html" title="Configuration Files">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="config.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="config.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="config-files.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="config-example"></a>Example</h2></div></div></div>
<p>The following sections describe the config format in detail. But first
		an example which shows the various features. The configuration below, loads
		two modules called 'my-module' and 'nss'. The user settings override some
		aspects of the system settings.</p>
<p>Global configuration file: <code class="literal">/etc/pkcs11/pkcs11.conf</code></p>
<pre class="programlisting">
# This setting controls whether to load user configuration from the
# ~/.config/pkcs11 directory. Possible values:
#    none: No user configuration
#    merge: Merge the user config over the system configuration (default)
#    only: Only user configuration, ignore system configuration
user-config: merge
</pre>
<p>One module configuration file per module: <code class="literal">/etc/pkcs11/modules/my-module</code></p>
<pre class="programlisting">
# This setting controls the actual module library to load. This config file
# might be installed by the package that installs this module library. This
# is not an absolute path name. Relative path names are loaded from the
# $(libdir)/pkcs11 directory by default.
module: my-pkcs11-module.so

# This controls whether the module is required to successfully initialize. If 'yes', then
# a failure to load or initialize this module will result in a p11-kit system failure.
critical: no
</pre>
<p>User configuration file: <code class="literal">~/.config/pkcs11/pkcs11.conf</code></p>
<pre class="programlisting">
# This is an empty file. Files that do not exist are treated as empty.
</pre>
<p>User configuration file: <code class="literal">~/.config/pkcs11/modules/my-module</code></p>
<pre class="programlisting">
# Merge with the settings in the system my-module config file. In this case
# a developer has overridden to load a different module for my-module instead.
module: /home/<USER>/src/custom-module/my-module.so
</pre>
<p>User configuration file: <code class="literal">~/.config/pkcs11/modules/nss</code></p>
<pre class="programlisting">
# Load the NSS libsoftokn.so.3 PKCS#11 library as a module. Note that we pass
# some custom non-standard initialization arguments, as NSS expects.
module: /usr/lib/libsoftokn3.so
x-init-reserved: configdir='sql:/home/<USER>/.pki/nssdb' certPrefix='' keyPrefix='' secmod='socmod.db'
critical: yes
</pre>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>