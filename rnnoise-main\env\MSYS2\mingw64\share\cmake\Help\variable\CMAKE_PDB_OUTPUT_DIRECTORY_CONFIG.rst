CMAKE_PDB_OUTPUT_DIRECTORY_<CONFIG>
-----------------------------------

Per-configuration output directory for MS debug symbol ``.pdb`` files
generated by the linker for executable and shared library targets.

This is a per-configuration version of :variable:`CMAKE_PDB_OUTPUT_DIRECTORY`.
This variable is used to initialize the
:prop_tgt:`PDB_OUTPUT_DIRECTORY_<CONFIG>`
property on all the targets.  See that target property for additional
information.
