<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_inject_net_dgram</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_inject_net_dgram - inject a datagram as though received from the network</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

int SSL_inject_net_dgram(SSL *s, const unsigned char *buf,
                         size_t buf_len,
                         const BIO_ADDR *peer,
                         const BIO_ADDR *local);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This function can be used to inject a datagram payload to a QUIC connection SSL object. The payload is processed as though it was received from the network. This function can be used for debugging purposes or to allow datagrams to be fed to QUIC from alternative sources.</p>

<p><i>buf</i> is required and must point to a datagram payload to inject. <i>buf_len</i> is the length of the buffer in bytes. The buffer is copied and need not remain valid after this function returns.</p>

<p><i>peer</i> and <i>local</i> are optional values pointing to <b>BIO_ADDR</b> structures describing the remote and local UDP endpoint addresses for the packet. Though the injected packet was not actually received from the network directly by OpenSSL, the packet will be processed as though the received datagram had the given addresses.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>Returns 1 on success or 0 on failure. This function always fails if called on an SSL object which is not a QUIC connection SSL object.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/OSSL_QUIC_client_method.html">OSSL_QUIC_client_method(3)</a>, <a href="../man3/SSL_handle_events.html">SSL_handle_events(3)</a>, <a href="../man3/SSL_set_blocking_mode.html">SSL_set_blocking_mode(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The function SSL_inject_net_dgram() was added in OpenSSL 3.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


