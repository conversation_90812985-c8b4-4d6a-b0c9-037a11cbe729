<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('globals_eval.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="contents">
<div class="textblock">Here is a list of all documented enum values with links to the documentation:</div>

<h3><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>LZMA_BUF_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea9ca0ecb62459bdc84d6af47d16b23ae5">base.h</a></li>
<li>LZMA_CHECK_CRC32&#160;:&#160;<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa0be65014a40b5cb4ab32252b3709bef7">check.h</a></li>
<li>LZMA_CHECK_CRC64&#160;:&#160;<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa87b4b0697a1e1ccb6766dd5c2fa24afc">check.h</a></li>
<li>LZMA_CHECK_NONE&#160;:&#160;<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa7b9851d75abfabc08d7fc5b4aaeb6f20">check.h</a></li>
<li>LZMA_CHECK_SHA256&#160;:&#160;<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3faf26a55ddd204a50ae87ec3432e7bc309">check.h</a></li>
<li>LZMA_DATA_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea3aa72729a844790e39b4e1101a731dfb">base.h</a></li>
<li>LZMA_FINISH&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77ea7d24fb3c6c144d13bcb091195b8ebec1">base.h</a></li>
<li>LZMA_FORMAT_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea63b7a58949854eb9307f8e351358d56c">base.h</a></li>
<li>LZMA_FULL_BARRIER&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77eaf7bf60e3555a4d10ffad3ecc3d2e01f1">base.h</a></li>
<li>LZMA_FULL_FLUSH&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77eaab46f0d7c721f1ec377e9575eab2586f">base.h</a></li>
<li>LZMA_GET_CHECK&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eaa5b648c18da0f584f621cfdf7fef1bdb">base.h</a></li>
<li>LZMA_INDEX_ITER_ANY&#160;:&#160;<a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8af46f6e5c414471c7c96586f380e48315">index.h</a></li>
<li>LZMA_INDEX_ITER_BLOCK&#160;:&#160;<a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8a2702617d60d6fc15138a749e06ef3414">index.h</a></li>
<li>LZMA_INDEX_ITER_NONEMPTY_BLOCK&#160;:&#160;<a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8aa49bf4d561d8f2c61d300edbb6c282c7">index.h</a></li>
<li>LZMA_INDEX_ITER_STREAM&#160;:&#160;<a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8a5b31d985de1c823151acdd7e4a966fc9">index.h</a></li>
<li>LZMA_MEM_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea567e1464feca03900a5425fb45b2f5b6">base.h</a></li>
<li>LZMA_MEMLIMIT_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eaa1d705effe6026f32c0fe9756b6326bc">base.h</a></li>
<li>LZMA_MF_BT2&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a7ab212446c3f6520f5c33ccfa4b3386a">lzma12.h</a></li>
<li>LZMA_MF_BT3&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a983ecc59bf3e07a7c43fea551ea11865">lzma12.h</a></li>
<li>LZMA_MF_BT4&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a468c32cdea9861d1ff98478364e6c547">lzma12.h</a></li>
<li>LZMA_MF_HC3&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a6eb38f634021a192cada8a978b5de93b">lzma12.h</a></li>
<li>LZMA_MF_HC4&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a0944620f4949289c2ebde613cae12b04">lzma12.h</a></li>
<li>LZMA_MODE_FAST&#160;:&#160;<a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866ac8c0926a91b4f756e11121efd30648cc">lzma12.h</a></li>
<li>LZMA_MODE_NORMAL&#160;:&#160;<a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866ad37225f30d5cd21fc8bb3eaba283bbf9">lzma12.h</a></li>
<li>LZMA_NO_CHECK&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eaa720d30092d504d7d138a320db1905ef">base.h</a></li>
<li>LZMA_OK&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eac003781ccb81bbd5578e29abed8a8cfe">base.h</a></li>
<li>LZMA_OPTIONS_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eaa9ff6dfee36b7aba4fae60706d37425f">base.h</a></li>
<li>LZMA_PROG_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea2dac8d451cb38da8550653d0d7be4ec2">base.h</a></li>
<li>LZMA_RUN&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77ea868472b76492afcaef54020a481890b1">base.h</a></li>
<li>LZMA_SEEK_NEEDED&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea6cf28e5345851f13bd798a4eab8cc939">base.h</a></li>
<li>LZMA_STREAM_END&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea91ecc6fab14c13ad36224afbcb4e55c4">base.h</a></li>
<li>LZMA_SYNC_FLUSH&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77ea14d75152afcda85d215e877fdd9c4170">base.h</a></li>
<li>LZMA_UNSUPPORTED_CHECK&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea989f393a1772d85bf545a9da48fc7ac2">base.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
