.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pcert_export_x509" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pcert_export_x509 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pcert_export_x509(gnutls_pcert_st * " pcert ", gnutls_x509_crt_t * " crt ");"
.SH ARGUMENTS
.IP "gnutls_pcert_st * pcert" 12
The pcert structure.
.IP "gnutls_x509_crt_t * crt" 12
An initialized \fBgnutls_x509_crt_t\fP.
.SH "DESCRIPTION"
Converts the given \fBgnutls_pcert_t\fP type into a \fBgnutls_x509_crt_t\fP.
This function only works if the type of  \fIpcert\fP is \fBGNUTLS_CRT_X509\fP.
When successful, the value written to  \fIcrt\fP must be freed with
\fBgnutls_x509_crt_deinit()\fP when no longer needed.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
