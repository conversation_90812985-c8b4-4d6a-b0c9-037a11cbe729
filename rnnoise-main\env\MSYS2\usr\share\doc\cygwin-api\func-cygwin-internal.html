<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_internal</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="func-cygwin-misc.html" title="Miscellaneous functions"><link rel="prev" href="func-cygwin-attach-handle-to-fd.html" title="cygwin_attach_handle_to_fd"><link rel="next" href="func-cygwin-stackdump.html" title="cygwin_stackdump"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_internal</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="func-cygwin-attach-handle-to-fd.html">Prev</a>&#160;</td><th width="60%" align="center">Miscellaneous functions</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-stackdump.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-internal"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_internal</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">uintptr_t
<b class="fsfunc">cygwin_internal</b>(</code>cygwin_getinfo_types <var class="pdparam">t</var>, <var class="pdparam">...</var><code>)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-internal-desc"></a><h2>Description</h2><p>This function gives you access to various internal data and functions.
It takes two arguments.  The first argument is a type from the 'cygwin_getinfo_types'
enum.  The second is an optional pointer.</p><p>Stay away unless you know what you're doing.</p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="func-cygwin-attach-handle-to-fd.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="func-cygwin-misc.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-stackdump.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">cygwin_attach_handle_to_fd&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;cygwin_stackdump</td></tr></table></div></body></html>
