# Automatically generated from regenerate_perl_options_info.pl

package Texinfo::Options;

our %array_cmdline_options = (
  'CSS_FILES'                        => undef,
  'CSS_REFS'                         => undef,
  'EXPANDED_FORMATS'                 => undef,
  'INCLUDE_DIRECTORIES'              => undef,
  'TEXINFO_LANGUAGE_DIRECTORIES'     => undef,
);

our %converter_cmdline_options = (
  'SPLIT_SIZE'                       => 300000,
  'FILLCOLUMN'                       => 72,
  'NUMBER_SECTIONS'                  => 1,
  'NUMBER_FOOTNOTES'                 => 1,
  'TRANSLITERATE_FILE_NAMES'         => 1,
  'SPLIT'                            => undef,
  'HEADERS'                          => 1,
  'NODE_FILES'                       => undef,
  'VERBOSE'                          => undef,
  'OUTFILE'                          => undef,
  'SUBDIR'                           => undef,
  'ENABLE_ENCODING'                  => 1,
);

our %converter_customization_options = (
  'TOP_NODE_UP'                      => '(dir)',
  'BASEFILENAME_LENGTH'              => 255-10,
  'DOC_ENCODING_FOR_INPUT_FILE_NAME' => 1,
  'DOC_ENCODING_FOR_OUTPUT_FILE_NAME' => 0,
  'IMAGE_LINK_PREFIX'                => undef,
  'CASE_INSENSITIVE_FILENAMES'       => 0,
  'DEBUG'                            => 0,
  'HANDLER_FATAL_ERROR_LEVEL'        => 100,
  'TEST'                             => 0,
  'TEXTCONTENT_COMMENT'              => undef,
  'TEXINFO_DTD_VERSION'              => '7.1',
  'USE_UNICODE_COLLATION'            => 1,
  'AFTER_BODY_OPEN'                  => undef,
  'AFTER_SHORT_TOC_LINES'            => undef,
  'AFTER_TOC_LINES'                  => undef,
  'ASCII_DASHES_AND_QUOTES'          => undef,
  'ASCII_GLYPH'                      => undef,
  'ASCII_PUNCTUATION'                => undef,
  'AUTO_MENU_DESCRIPTION_ALIGN_COLUMN' => undef,
  'AUTO_MENU_MAX_WIDTH'              => undef,
  'BEFORE_SHORT_TOC_LINES'           => undef,
  'BEFORE_TOC_LINES'                 => undef,
  'BIG_RULE'                         => undef,
  'BODY_ELEMENT_ATTRIBUTES'          => undef,
  'CLASS_BEGIN_USEPACKAGE'           => undef,
  'COPIABLE_LINKS'                   => undef,
  'CHAPTER_HEADER_LEVEL'             => undef,
  'CHECK_HTMLXREF'                   => undef,
  'CLOSE_DOUBLE_QUOTE_SYMBOL'        => undef,
  'CLOSE_QUOTE_SYMBOL'               => undef,
  'COLLATION_LANGUAGE'               => undef,
  'COMMAND_LINE_ENCODING'            => undef,
  'INDENTED_BLOCK_COMMANDS_IN_TABLE' => undef,
  'CONTENTS_OUTPUT_LOCATION'         => undef,
  'CONVERT_TO_LATEX_IN_MATH'         => undef,
  'DATE_IN_HEADER'                   => undef,
  'DEFAULT_RULE'                     => undef,
  'DEF_TABLE'                        => undef,
  'DO_ABOUT'                         => undef,
  'DOCTYPE'                          => undef,
  'DOCUMENTLANGUAGE_COLLATION'       => undef,
  'END_USEPACKAGE'                   => undef,
  'EPUB_CREATE_CONTAINER_FILE'       => undef,
  'EPUB_KEEP_CONTAINER_FOLDER'       => undef,
  'EXTENSION'                        => undef,
  'EXTERNAL_CROSSREF_EXTENSION'      => undef,
  'EXTERNAL_CROSSREF_SPLIT'          => undef,
  'EXTERNAL_DIR'                     => undef,
  'EXTRA_HEAD'                       => undef,
  'FOOTNOTE_END_HEADER_LEVEL'        => undef,
  'FOOTNOTE_SEPARATE_HEADER_LEVEL'   => undef,
  'HEADER_IN_TABLE'                  => undef,
  'HIGHLIGHT_SYNTAX'                 => undef,
  'HIGHLIGHT_SYNTAX_DEFAULT_LANGUAGE' => undef,
  'HTML_MATH'                        => undef,
  'HTML_ROOT_ELEMENT_ATTRIBUTES'     => undef,
  'HTMLXREF_FILE'                    => undef,
  'HTMLXREF_MODE'                    => undef,
  'ICONS'                            => undef,
  'INDEX_ENTRY_COLON'                => undef,
  'INDEX_SPECIAL_CHARS_WARNING'      => undef,
  'INFO_JS_DIR'                      => undef,
  'INFO_SPECIAL_CHARS_QUOTE'         => undef,
  'INFO_SPECIAL_CHARS_WARNING'       => undef,
  'IGNORE_REF_TO_TOP_NODE_UP'        => undef,
  'INLINE_CSS_STYLE'                 => undef,
  'INPUT_FILE_NAME_ENCODING'         => undef,
  'JS_WEBLABELS'                     => undef,
  'JS_WEBLABELS_FILE'                => undef,
  'LATEX_FLOATS_FILE_EXTENSION'      => 'tfl',
  'LOCALE_ENCODING'                  => undef,
  'L2H_CLEAN'                        => undef,
  'L2H_FILE'                         => undef,
  'L2H_HTML_VERSION'                 => undef,
  'L2H_L2H'                          => undef,
  'L2H_SKIP'                         => undef,
  'L2H_TMP'                          => undef,
  'MATHJAX_CONFIGURATION'            => undef,
  'MATHJAX_SCRIPT'                   => undef,
  'MATHJAX_SOURCE'                   => undef,
  'MAX_HEADER_LEVEL'                 => undef,
  'MENU_ENTRY_COLON'                 => undef,
  'MENU_SYMBOL'                      => undef,
  'MESSAGE_ENCODING'                 => undef,
  'MONOLITHIC'                       => undef,
  'NO_CSS'                           => undef,
  'NO_NUMBER_FOOTNOTE_SYMBOL'        => undef,
  'NO_CUSTOM_HTML_ATTRIBUTE'         => undef,
  'NODE_NAME_IN_INDEX'               => undef,
  'NODE_NAME_IN_MENU'                => undef,
  'NO_TOP_NODE_OUTPUT'               => undef,
  'OPEN_DOUBLE_QUOTE_SYMBOL'         => undef,
  'OPEN_QUOTE_SYMBOL'                => undef,
  'OUTPUT_CHARACTERS'                => undef,
  'OUTPUT_ENCODING_NAME'             => undef,
  'OUTPUT_FILE_NAME_ENCODING'        => undef,
  'OUTPUT_PERL_ENCODING'             => undef,
  'PACKAGE'                          => undef,
  'PACKAGE_AND_VERSION'              => undef,
  'PACKAGE_NAME'                     => undef,
  'PACKAGE_URL'                      => undef,
  'PACKAGE_VERSION'                  => undef,
  'PRE_BODY_CLOSE'                   => undef,
  'PREFIX'                           => undef,
  'PROGRAM'                          => undef,
  'PROGRAM_NAME_IN_ABOUT'            => undef,
  'PROGRAM_NAME_IN_FOOTER'           => undef,
  'SECTION_NAME_IN_TITLE'            => undef,
  'SHORT_TOC_LINK_TO_TOC'            => undef,
  'SHOW_TITLE'                       => undef,
  'T4H_LATEX_CONVERSION'             => undef,
  'T4H_MATH_CONVERSION'              => undef,
  'T4H_TEX_CONVERSION'               => undef,
  'TEXI2HTML'                        => undef,
  'TEXINFO_OUTPUT_FORMAT'            => undef,
  'TXI_MARKUP_NO_SECTION_EXTENT'     => undef,
  'TOC_LINKS'                        => undef,
  'TOP_FILE'                         => undef,
  'TOP_NODE_FILE_TARGET'             => undef,
  'TOP_NODE_UP_URL'                  => undef,
  'USE_ACCESSKEY'                    => undef,
  'USE_ISO'                          => undef,
  'USE_LINKS'                        => undef,
  'USE_NEXT_HEADING_FOR_LONE_NODE'   => undef,
  'USE_NODES'                        => undef,
  'USE_NODE_DIRECTIONS'              => undef,
  'USE_NUMERIC_ENTITY'               => undef,
  'USE_REL_REV'                      => undef,
  'USE_SETFILENAME_EXTENSION'        => undef,
  'USE_TITLEPAGE_FOR_TITLE'          => undef,
  'USE_UNIDECODE'                    => undef,
  'USE_XML_SYNTAX'                   => undef,
  'VERTICAL_HEAD_NAVIGATION'         => undef,
  'WORDS_IN_PAGE'                    => undef,
  'XREF_USE_FLOAT_LABEL'             => undef,
  'XREF_USE_NODE_NAME_ARG'           => undef,
  'XS_EXTERNAL_CONVERSION'           => undef,
  'XS_EXTERNAL_FORMATTING'           => undef,
  'XS_STRXFRM_COLLATION_LOCALE'      => undef,
  '_INLINE_STYLE_WIDTH'              => undef,
);

our %converter_other_options = (
  'LINKS_BUTTONS'                    => undef,
  'TOP_BUTTONS'                      => undef,
  'TOP_FOOTER_BUTTONS'               => undef,
  'SECTION_BUTTONS'                  => undef,
  'CHAPTER_FOOTER_BUTTONS'           => undef,
  'SECTION_FOOTER_BUTTONS'           => undef,
  'NODE_FOOTER_BUTTONS'              => undef,
  'MISC_BUTTONS'                     => undef,
  'CHAPTER_BUTTONS'                  => undef,
  'ACTIVE_ICONS'                     => undef,
  'PASSIVE_ICONS'                    => undef,
);

our %multiple_at_command_options = (
  'allowcodebreaks'                  => 'true',
  'clickstyle'                       => '@arrow',
  'codequotebacktick'                => 'off',
  'codequoteundirected'              => 'off',
  'contents'                         => 0,
  'deftypefnnewline'                 => 'off',
  'documentencoding'                 => 'utf-8',
  'documentlanguage'                 => undef,
  'evenfooting'                      => undef,
  'evenheading'                      => undef,
  'everyfooting'                     => undef,
  'everyheading'                     => undef,
  'exampleindent'                    => '5',
  'firstparagraphindent'             => 'none',
  'frenchspacing'                    => 'off',
  'headings'                         => 'on',
  'kbdinputstyle'                    => 'distinct',
  'microtype'                        => undef,
  'oddheading'                       => undef,
  'oddfooting'                       => undef,
  'paragraphindent'                  => '3',
  'shortcontents'                    => 0,
  'summarycontents'                  => 0,
  'urefbreakstyle'                   => 'after',
  'xrefautomaticsectiontitle'        => 'off',
);

our %program_cmdline_options = (
  'MACRO_EXPAND'                     => undef,
  'INTERNAL_LINKS'                   => undef,
  'ERROR_LIMIT'                      => 100,
  'FORCE'                            => undef,
  'NO_WARN'                          => undef,
  'TRACE_INCLUDES'                   => 0,
  'FORMAT_MENU'                      => 'menu',
);

our %program_customization_options = (
  'CHECK_NORMAL_MENU_STRUCTURE'      => 1,
  'CHECK_MISSING_MENU_ENTRY'         => 1,
  'DUMP_TREE'                        => undef,
  'DUMP_TEXI'                        => undef,
  'SHOW_BUILTIN_CSS_RULES'           => 0,
  'SORT_ELEMENT_COUNT'               => undef,
  'SORT_ELEMENT_COUNT_WORDS'         => undef,
  'TEXI2DVI'                         => 'texi2dvi',
  'TREE_TRANSFORMATIONS'             => undef,
);

our %unique_at_command_options = (
  'afivepaper'                       => undef,
  'afourpaper'                       => undef,
  'afourlatex'                       => undef,
  'afourwide'                        => undef,
  'bsixpaper'                        => undef,
  'documentdescription'              => undef,
  'evenfootingmarks'                 => undef,
  'evenheadingmarks'                 => undef,
  'everyfootingmarks'                => 'bottom',
  'everyheadingmarks'                => 'bottom',
  'fonttextsize'                     => 11,
  'footnotestyle'                    => 'end',
  'novalidate'                       => 0,
  'oddfootingmarks'                  => undef,
  'oddheadingmarks'                  => undef,
  'pagesizes'                        => undef,
  'setchapternewpage'                => 'on',
  'setfilename'                      => undef,
  'smallbook'                        => undef,
);



#################################################

my %regular_options_types;

my %converter_common_regular_options_defaults = (
  'PACKAGE'                          => 'texinfo',
  'PACKAGE_NAME'                     => 'GNU Texinfo',
  'PACKAGE_AND_VERSION'              => 'texinfo',
  'PACKAGE_VERSION'                  => '',
  'PACKAGE_URL'                      => 'https://www.gnu.org/software/texinfo/',
  'PROGRAM'                          => '',
);

$regular_options_types{'common'} = \%converter_common_regular_options_defaults;

my %converter_converter_regular_options_defaults = (
  'documentlanguage'                 => undef,
);

$regular_options_types{'converter'} = \%converter_converter_regular_options_defaults;

my %converter_html_regular_options_defaults = (
  'BIG_RULE'                         => '<hr>',
  'BODY_ELEMENT_ATTRIBUTES'          => undef,
  'CHAPTER_HEADER_LEVEL'             => 2,
  'CLOSE_QUOTE_SYMBOL'               => undef,
  'CONTENTS_OUTPUT_LOCATION'         => 'after_top',
  'CONVERT_TO_LATEX_IN_MATH'         => undef,
  'INDENTED_BLOCK_COMMANDS_IN_TABLE' => 0,
  'CHECK_HTMLXREF'                   => 1,
  'COPIABLE_LINKS'                   => 1,
  'DATE_IN_HEADER'                   => 0,
  'DEFAULT_RULE'                     => '<hr>',
  'documentlanguage'                 => 'en',
  'DOCTYPE'                          => '<!DOCTYPE html>',
  'DO_ABOUT'                         => 0,
  'OUTPUT_CHARACTERS'                => 0,
  'EXTENSION'                        => 'html',
  'EXTERNAL_CROSSREF_EXTENSION'      => undef,
  'FOOTNOTE_END_HEADER_LEVEL'        => 4,
  'FOOTNOTE_SEPARATE_HEADER_LEVEL'   => 4,
  'FORMAT_MENU'                      => 'sectiontoc',
  'HEADERS'                          => 1,
  'INDEX_ENTRY_COLON'                => '',
  'INLINE_CSS_STYLE'                 => 0,
  'JS_WEBLABELS'                     => 'generate',
  'JS_WEBLABELS_FILE'                => 'js_licenses.html',
  'MAX_HEADER_LEVEL'                 => 4,
  'MENU_ENTRY_COLON'                 => ':',
  'MENU_SYMBOL'                      => undef,
  'MONOLITHIC'                       => 1,
  'NO_CUSTOM_HTML_ATTRIBUTE'         => 0,
  'NO_CSS'                           => 0,
  'NO_NUMBER_FOOTNOTE_SYMBOL'        => '*',
  'NODE_NAME_IN_MENU'                => 1,
  'OPEN_QUOTE_SYMBOL'                => undef,
  'OUTPUT_ENCODING_NAME'             => 'utf-8',
  'SECTION_NAME_IN_TITLE'            => 0,
  'SHORT_TOC_LINK_TO_TOC'            => 1,
  'SHOW_TITLE'                       => undef,
  'SPLIT'                            => 'node',
  'TOP_FILE'                         => 'index.html',
  'TOP_NODE_FILE_TARGET'             => 'index.html',
  'USE_ACCESSKEY'                    => 1,
  'USE_NEXT_HEADING_FOR_LONE_NODE'   => 1,
  'USE_ISO'                          => 1,
  'USE_LINKS'                        => 1,
  'USE_NODES'                        => 1,
  'USE_NODE_DIRECTIONS'              => undef,
  'USE_REL_REV'                      => 1,
  'USE_TITLEPAGE_FOR_TITLE'          => 1,
  'WORDS_IN_PAGE'                    => 300,
  'XREF_USE_NODE_NAME_ARG'           => undef,
  'XREF_USE_FLOAT_LABEL'             => 0,
  'xrefautomaticsectiontitle'        => 'on',
);

$regular_options_types{'html'} = \%converter_html_regular_options_defaults;

my %converter_texi2html_regular_options_defaults = (
  'FORMAT_MENU'                      => 'menu',
  'USE_SETFILENAME_EXTENSION'        => 0,
  'footnotestyle'                    => 'separate',
  'CONTENTS_OUTPUT_LOCATION'         => 'separate_element',
  'FORCE'                            => 1,
  'USE_ACCESSKEY'                    => 0,
  'NODE_NAME_IN_MENU'                => 0,
  'SHORT_TOC_LINK_TO_TOC'            => 0,
  'SHOW_TITLE'                       => 1,
  'USE_REL_REV'                      => 0,
  'USE_LINKS'                        => 0,
  'USE_NODES'                        => 0,
  'SPLIT'                            => '',
  'PROGRAM_NAME_IN_FOOTER'           => 1,
  'PROGRAM_NAME_IN_ABOUT'            => 1,
  'HEADER_IN_TABLE'                  => 1,
  'MENU_ENTRY_COLON'                 => '',
  'INDEX_ENTRY_COLON'                => '',
  'DO_ABOUT'                         => undef,
  'CHAPTER_HEADER_LEVEL'             => 1,
  'BIG_RULE'                         => '<hr style="height: 6px;">',
  'FOOTNOTE_END_HEADER_LEVEL'        => 3,
  'FOOTNOTE_SEPARATE_HEADER_LEVEL'   => 1,
);

$regular_options_types{'texi2html'} = \%converter_texi2html_regular_options_defaults;

sub get_converter_regular_options {
  my $input = shift;
  return $regular_options_types{$input}
}

1;
