.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_set_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_set_info \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_obj_set_info(gnutls_pkcs11_obj_t " obj ", gnutls_pkcs11_obj_info_t " itype ", const void * " data ", size_t " data_size ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_t obj" 12
should contain a \fBgnutls_pkcs11_obj_t\fP type
.IP "gnutls_pkcs11_obj_info_t itype" 12
Denotes the type of information to be set
.IP "const void * data" 12
the data to set
.IP "size_t data_size" 12
the size of data
.IP "unsigned flags" 12
Or sequence of GNUTLS_PKCS11_OBJ_* flags
.SH "DESCRIPTION"
This function will set attributes on the provided object.
Available options for  \fIitype\fP are \fBGNUTLS_PKCS11_OBJ_LABEL\fP,
\fBGNUTLS_PKCS11_OBJ_ID_HEX\fP, and \fBGNUTLS_PKCS11_OBJ_ID\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success or a negative error code on error.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
