+{
   locale_version => 1.31,
# in cldr test/lt.xml why I-dot-acute > I-dot though i-dot-acute < i-dot ?
   entry => <<'ENTRY', # for DUCET v13.0.0
0049 0307 ; [.2090.0020.0008][.0000.002E.0002] # <LATIN CAPITAL LETTER I, COMBINING DOT ABOVE>
0307 0300 ; [.0000.0025.0002] # <COMBINING DOT ABOVE, COMBINING GRAVE ACCENT>
0307 0301 ; [.0000.0024.0002] # <COMBINING DOT ABOVE, COMBINING ACUTE ACCENT>
0307 0303 ; [.0000.002D.0002] # <COMBINING DOT ABOVE, COMBINING TILDE>
0105      ; [.1FA2.0021.0002] # LATIN SMALL LETTER A WITH OGONEK
0061 0328 ; [.1FA2.0021.0002] # LATIN SMALL LETTER A WITH OGONEK
0104      ; [.1FA2.0021.0008] # LATIN CAPITAL LETTER A WITH OGONEK
0041 0328 ; [.1FA2.0021.0008] # LATIN CAPITAL LETTER A WITH OGONEK
010D      ; [.1FD7.0020.0002] # LATIN SMALL LETTER C WITH CARON
0063 030C ; [.1FD7.0020.0002] # LATIN SMALL LETTER C WITH CARON
010C      ; [.1FD7.0020.0008] # LATIN CAPITAL LETTER C WITH CARON
0043 030C ; [.1FD7.0020.0008] # LATIN CAPITAL LETTER C WITH CARON
0119      ; [.2007.0021.0002] # LATIN SMALL LETTER E WITH OGONEK
0065 0328 ; [.2007.0021.0002] # LATIN SMALL LETTER E WITH OGONEK
0118      ; [.2007.0021.0008] # LATIN CAPITAL LETTER E WITH OGONEK
0045 0328 ; [.2007.0021.0008] # LATIN CAPITAL LETTER E WITH OGONEK
0117      ; [.2007.0022.0002] # LATIN SMALL LETTER E WITH DOT ABOVE
0065 0307 ; [.2007.0022.0002] # LATIN SMALL LETTER E WITH DOT ABOVE
0116      ; [.2007.0022.0008] # LATIN CAPITAL LETTER E WITH DOT ABOVE
0045 0307 ; [.2007.0022.0008] # LATIN CAPITAL LETTER E WITH DOT ABOVE
012F      ; [.2090.0021.0002] # LATIN SMALL LETTER I WITH OGONEK
0069 0328 ; [.2090.0021.0002] # LATIN SMALL LETTER I WITH OGONEK
012E      ; [.2090.0021.0008] # LATIN CAPITAL LETTER I WITH OGONEK
0049 0328 ; [.2090.0021.0008] # LATIN CAPITAL LETTER I WITH OGONEK
0079      ; [.2090.0022.0002] # LATIN SMALL LETTER Y
0059      ; [.2090.0022.0008] # LATIN CAPITAL LETTER Y
0161      ; [.21D3.0020.0002] # LATIN SMALL LETTER S WITH CARON
0073 030C ; [.21D3.0020.0002] # LATIN SMALL LETTER S WITH CARON
0160      ; [.21D3.0020.0008] # LATIN CAPITAL LETTER S WITH CARON
0053 030C ; [.21D3.0020.0008] # LATIN CAPITAL LETTER S WITH CARON
0173      ; [.2217.0021.0002] # LATIN SMALL LETTER U WITH OGONEK
0075 0328 ; [.2217.0021.0002] # LATIN SMALL LETTER U WITH OGONEK
0172      ; [.2217.0021.0008] # LATIN CAPITAL LETTER U WITH OGONEK
0055 0328 ; [.2217.0021.0008] # LATIN CAPITAL LETTER U WITH OGONEK
016B      ; [.2217.0022.0002] # LATIN SMALL LETTER U WITH MACRON
0075 0304 ; [.2217.0022.0002] # LATIN SMALL LETTER U WITH MACRON
016A      ; [.2217.0022.0008] # LATIN CAPITAL LETTER U WITH MACRON
0055 0304 ; [.2217.0022.0008] # LATIN CAPITAL LETTER U WITH MACRON
017E      ; [.2287.0020.0002] # LATIN SMALL LETTER Z WITH CARON
007A 030C ; [.2287.0020.0002] # LATIN SMALL LETTER Z WITH CARON
017D      ; [.2287.0020.0008] # LATIN CAPITAL LETTER Z WITH CARON
005A 030C ; [.2287.0020.0008] # LATIN CAPITAL LETTER Z WITH CARON
ENTRY
};
