.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_get_exts" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_get_exts \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_obj_get_exts(gnutls_pkcs11_obj_t " obj ", gnutls_x509_ext_st ** " exts ", unsigned int * " exts_size ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_t obj" 12
should contain a \fBgnutls_pkcs11_obj_t\fP type
.IP "gnutls_x509_ext_st ** exts" 12
a pointer to a \fBgnutls_x509_ext_st\fP pointer
.IP "unsigned int * exts_size" 12
will be updated with the number of  \fIexts\fP 
.IP "unsigned int flags" 12
Or sequence of \fBGNUTLS_PKCS11_OBJ_\fP* flags 
.SH "DESCRIPTION"
This function will return information about attached extensions
that associate to the provided object (which should be a certificate).
The extensions are the attached p11\-kit trust module extensions.

Each element of  \fIexts\fP must be deinitialized using \fBgnutls_x509_ext_deinit()\fP
while  \fIexts\fP should be deallocated using \fBgnutls_free()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success or a negative error code on error.
.SH "SINCE"
3.3.8
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
