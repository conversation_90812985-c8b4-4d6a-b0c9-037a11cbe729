.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_peers_subkey_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_peers_subkey_id \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_get_peers_subkey_id(gnutls_session_t " session ", gnutls_datum_t * " id ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.IP "gnutls_datum_t * id" 12
will contain the ID
.SH "DESCRIPTION"
This function is no\-op.
.SH "RETURNS"
\fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP.
.SH "SINCE"
3.1.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
