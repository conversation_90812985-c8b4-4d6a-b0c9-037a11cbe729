.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_export2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_export2 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_export2(gnutls_pubkey_t " key ", gnutls_x509_crt_fmt_t " format ", gnutls_datum_t * " out ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
Holds the certificate
.IP "gnutls_x509_crt_fmt_t format" 12
the format of output params. One of PEM or DER.
.IP "gnutls_datum_t * out" 12
will contain a certificate PEM or DER encoded
.SH "DESCRIPTION"
This function will export the public key to DER or PEM format.
The contents of the exported data is the SubjectPublicKeyInfo
X.509 structure.

The output buffer will be allocated using \fBgnutls_malloc()\fP.

If the structure is PEM encoded, it will have a header
of "BEGIN CERTIFICATE".
.SH "RETURNS"
In case of failure a negative error code will be
returned, and 0 on success.
.SH "SINCE"
3.1.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
