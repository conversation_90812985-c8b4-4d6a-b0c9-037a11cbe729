/* This file contains the definitions and documentation for the
   additional tree codes used in the GNU C compiler (see tree.def
   for the standard codes).
   Copyright (C) 2023-2025 Free Software Foundation, Inc.

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

/* Tree nodes used in the C frontend only, not shared with C++ frontend.  */

/* Used to represent a vector of tokens for deferred parsing.  */
DEFTREECODE (C_TOKEN_VEC, "c_token_vec", tcc_exceptional, 0)

/*
Local variables:
mode:c
End:
*/
