diff_cmd () {
	"$merge_tool_path" "$LOCAL" "$REMOTE" >/dev/null 2>&1
}

diff_cmd_help () {
	echo "Use DiffMerge (requires a graphical session)"
}

merge_cmd () {
	if $base_present
	then
		"$merge_tool_path" --merge --result="$MERGED" \
			"$LOCAL" "$BASE" "$REMOTE"
	else
		"$merge_tool_path" --merge \
			--result="$MERGED" "$LOCAL" "$REMOTE"
	fi
}

merge_cmd_help () {
	echo "Use DiffMerge (requires a graphical session)"
}

exit_code_trustable () {
	true
}
