<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OPENSSL_strcasecmp</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OPENSSL_strcasecmp, OPENSSL_strncasecmp - compare two strings ignoring case</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/crypto.h&gt;

int OPENSSL_strcasecmp(const char *s1, const char *s2);
int OPENSSL_strncasecmp(const char *s1, const char *s2, size_t n);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The OPENSSL_strcasecmp function performs a byte-by-byte comparison of the strings <b>s1</b> and <b>s2</b>, ignoring the case of the characters.</p>

<p>The OPENSSL_strncasecmp function is similar, except that it compares no more than <b>n</b> bytes of <b>s1</b> and <b>s2</b>.</p>

<p>In POSIX-compatible system and on Windows these functions use &quot;C&quot; locale for case insensitive. Otherwise the comparison is done in current locale.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>Both functions return an integer less than, equal to, or greater than zero if s1 is found, respectively, to be less than, to match, or be greater than s2.</p>

<h1 id="NOTES">NOTES</h1>

<p>OpenSSL extensively uses case insensitive comparison of ASCII strings. Though OpenSSL itself is locale-agnostic, the applications using OpenSSL libraries may unpredictably suffer when they use localization (e.g. Turkish locale is well-known with a specific I/i cases). These functions use C locale for string comparison.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 3.0.3.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2022-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


