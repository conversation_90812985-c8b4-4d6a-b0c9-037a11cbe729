/*
 * Copyright (C) 2022 Mohamad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";

namespace Windows.System.Profile.SystemManufacturers
{
    apicontract SystemManufacturersContract;
    interface ISmbiosInformationStatics;
    runtimeclass SmbiosInformation;

    [
        contractversion(3.0)
    ]
    apicontract SystemManufacturersContract
    {
    }

    [
        contract(Windows.System.Profile.SystemManufacturers.SystemManufacturersContract, 1.0),
        exclusiveto(Windows.System.Profile.SystemManufacturers.SmbiosInformation),
        uuid(080cca7c-637c-48c4-b728-f9273812db8e)
    ]
    interface ISmbiosInformationStatics : IInspectable
    {
        [propget] HRESULT SerialNumber([out, retval] HSTRING *value);
    }

    [
        contract(Windows.System.Profile.SystemManufacturers.SystemManufacturersContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.System.Profile.SystemManufacturers.ISmbiosInformationStatics, Windows.System.Profile.SystemManufacturers.SystemManufacturersContract, 1.0)
    ]
    runtimeclass SmbiosInformation
    {
    }
}
