<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>umount</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="tzset.html" title="tzset"><link rel="next" href="using-effectively.html" title="Using Cygwin effectively with Windows"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">umount</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="tzset.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="using-effectively.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="umount"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>umount &#8212; Unmount filesystems</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">umount</code>   <em class="replaceable"><code>POSIXPATH</code></em> </p></div><div class="cmdsynopsis"><p><code class="command">umount</code>   -U </p></div><div class="cmdsynopsis"><p><code class="command">umount</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="umount-options"></a><h2>Options</h2><pre class="screen">
  -h, --help                    output usage information and exit
  -U, --remove-user-mounts      remove all user mounts
  -V, --version                 output version information and exit
</pre></div><div class="refsect1"><a name="umount-desc"></a><h2>Description</h2><p>The <span class="command"><strong>umount</strong></span> program removes mounts from the mount
      table in the current session. If you specify a POSIX path that
      corresponds to a current mount point, <span class="command"><strong>umount</strong></span> will
      remove it from the current mount table. Note that you can only remove
      user mount points. The <code class="literal">-U</code> flag may be used to specify
      removing all user mount points from the current user session.</p><p>See <a class="xref" href="using.html#mount-table" title="The Cygwin Mount Table">the section called &#8220;The Cygwin Mount Table&#8221;</a> for more information on the mount
      table.</p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="tzset.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="using-effectively.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">tzset&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Using Cygwin effectively with Windows</td></tr></table></div></body></html>
