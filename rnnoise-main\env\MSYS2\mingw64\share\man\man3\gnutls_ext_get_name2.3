.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ext_get_name2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ext_get_name2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_ext_get_name2(gnutls_session_t " session ", unsigned int " tls_id ", gnutls_ext_parse_type_t " parse_point ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
a \fBgnutls_session_t\fP opaque pointer
.IP "unsigned int tls_id" 12
is a TLS extension numeric ID
.IP "gnutls_ext_parse_type_t parse_point" 12
the parse type of the extension
.SH "DESCRIPTION"
Convert a TLS extension numeric ID to a printable string.
.SH "RETURNS"
a pointer to a string that contains the name of the
specified cipher, or \fBNULL\fP.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
