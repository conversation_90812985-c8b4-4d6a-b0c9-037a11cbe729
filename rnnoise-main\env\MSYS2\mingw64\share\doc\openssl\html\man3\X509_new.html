<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_new, X509_new_ex, X509_free, X509_up_ref, X509_chain_up_ref, OSSL_STACK_OF_X509_free - X509 certificate ASN1 allocation and deallocation functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

X509 *X509_new(void);
X509 *X509_new_ex(OSSL_LIB_CTX *libctx, const char *propq);
void X509_free(X509 *a);
int X509_up_ref(X509 *a);
STACK_OF(X509) *X509_chain_up_ref(STACK_OF(X509) *x);
void OSSL_STACK_OF_X509_free(STACK_OF(X509) *certs);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The X509 ASN1 allocation routines allocate and free an X509 structure, which represents an X509 certificate.</p>

<p>X509_new_ex() allocates and initializes a X509 structure with a library context of <i>libctx</i>, property query of <i>propq</i> and a reference count of <b>1</b>. Many X509 functions such as X509_check_purpose(), and X509_verify() use this library context to select which providers supply the fetched algorithms (SHA1 is used internally). This created X509 object can then be used when loading binary data using d2i_X509().</p>

<p>X509_new() is similar to X509_new_ex() but sets the library context and property query to NULL. This results in the default (NULL) library context being used for any X509 operations requiring algorithm fetches.</p>

<p>X509_free() decrements the reference count of <b>X509</b> structure <b>a</b> and frees it up if the reference count is zero. If the argument is NULL, nothing is done.</p>

<p>X509_up_ref() increments the reference count of <b>a</b>.</p>

<p>X509_chain_up_ref() increases the reference count of all certificates in chain <b>x</b> and returns a copy of the stack, or an empty stack if <b>a</b> is NULL.</p>

<p>OSSL_STACK_OF_X509_free() deallocates the given list of pointers to certificates after calling X509_free() on all its elements. If the argument is NULL, nothing is done.</p>

<h1 id="NOTES">NOTES</h1>

<p>The function X509_up_ref() if useful if a certificate structure is being used by several different operations each of which will free it up after use: this avoids the need to duplicate the entire certificate structure.</p>

<p>The function X509_chain_up_ref() doesn&#39;t just up the reference count of each certificate. It also returns a copy of the stack, using sk_X509_dup(), but it serves a similar purpose: the returned chain persists after the original has been freed.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>If the allocation fails, X509_new() returns NULL and sets an error code that can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>. Otherwise it returns a pointer to the newly allocated structure.</p>

<p>X509_up_ref() returns 1 for success and 0 for failure.</p>

<p>X509_chain_up_ref() returns a copy of the stack or NULL if an error occurred.</p>

<p>OSSL_STACK_OF_X509_free() has no return value.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/d2i_X509.html">d2i_X509(3)</a>, <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/X509_CRL_get0_by_serial.html">X509_CRL_get0_by_serial(3)</a>, <a href="../man3/X509_get0_signature.html">X509_get0_signature(3)</a>, <a href="../man3/X509_get_ext_d2i.html">X509_get_ext_d2i(3)</a>, <a href="../man3/X509_get_extension_flags.html">X509_get_extension_flags(3)</a>, <a href="../man3/X509_get_pubkey.html">X509_get_pubkey(3)</a>, <a href="../man3/X509_get_subject_name.html">X509_get_subject_name(3)</a>, <a href="../man3/X509_get_version.html">X509_get_version(3)</a>, <a href="../man3/X509_NAME_add_entry_by_txt.html">X509_NAME_add_entry_by_txt(3)</a>, <a href="../man3/X509_NAME_ENTRY_get_object.html">X509_NAME_ENTRY_get_object(3)</a>, <a href="../man3/X509_NAME_get_index_by_NID.html">X509_NAME_get_index_by_NID(3)</a>, <a href="../man3/X509_NAME_print_ex.html">X509_NAME_print_ex(3)</a>, <a href="../man3/X509_sign.html">X509_sign(3)</a>, <a href="../man3/X509V3_get_d2i.html">X509V3_get_d2i(3)</a>, <a href="../man3/X509_verify_cert.html">X509_verify_cert(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>X509_new_ex() was added in OpenSSL 3.0.</p>

<p>OSSL_STACK_OF_X509_free() was added in OpenSSL 3.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2002-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


