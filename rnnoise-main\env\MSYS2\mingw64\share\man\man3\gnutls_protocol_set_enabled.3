.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_protocol_set_enabled" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_protocol_set_enabled \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_protocol_set_enabled(gnutls_protocol_t " version ", unsigned int " enabled ");"
.SH ARGUMENTS
.IP "gnutls_protocol_t version" 12
is a (gnutls) version number
.IP "unsigned int enabled" 12
whether to enable the protocol
.SH "DESCRIPTION"
Control the previous system\-wide setting that marked  \fIversion\fP as
enabled or disabled.  Calling this function is allowed
only if allowlisting mode is set in the configuration file,
and only if the system\-wide TLS priority string
has not been initialized yet.
The intended usage is to provide applications with a way
to expressly deviate from the distribution or site defaults
inherited from the configuration file.
The modification is composable with further modifications
performed through the priority string mechanism.

This function is not thread\-safe and is intended to be called
in the main thread at the beginning of the process execution.
.SH "RETURNS"
0 on success or negative error code otherwise.
.SH "SINCE"
3.7.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
