.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_privkey_sign" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_privkey_sign \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_x509_crq_privkey_sign(gnutls_x509_crq_t " crq ", gnutls_privkey_t " key ", gnutls_digest_algorithm_t " dig ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "gnutls_privkey_t key" 12
holds a private key
.IP "gnutls_digest_algorithm_t dig" 12
The message digest to use, i.e., \fBGNUTLS_DIG_SHA1\fP
.IP "unsigned int flags" 12
must be 0
.SH "DESCRIPTION"
This function will sign the certificate request with a private key.
This must be the same key as the one used in
\fBgnutls_x509_crt_set_key()\fP since a certificate request is self
signed.

This must be the last step in a certificate request generation
since all the previously set parameters are now signed.

A known limitation of this function is, that a newly\-signed request will not
be fully functional (e.g., for signature verification), until it
is exported an re\-imported.

After GnuTLS 3.6.1 the value of  \fIdig\fP may be \fBGNUTLS_DIG_UNKNOWN\fP,
and in that case, a suitable but reasonable for the key algorithm will be selected.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
\fBGNUTLS_E_ASN1_VALUE_NOT_FOUND\fP is returned if you didn't set all
information in the certificate request (e.g., the version using
\fBgnutls_x509_crq_set_version()\fP).
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
