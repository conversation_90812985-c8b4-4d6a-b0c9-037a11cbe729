# help.hu.txt - hu GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
Az Ön döntésén múlik, hogy milyen értéket ad meg itt. Ezt az értéket soha
nem exportáljuk mások részére. Ez a bizalmak hálózatához (web-of-trust)
s<PERSON><PERSON><PERSON><PERSON><PERSON>, semmi köze az igazolások hálózatához (web-of-certificates).
.

.gpg.edit_ownertrust.set_ultimate.okay
Hogy a bizalmak hálózatát felépítsük, a GnuPG-nek tudnia kell, hogy
mely kulcsok alapvetően megbízhatóak - általában ezek azok a kulcsok,
melyek titkos kulcsához hozzáfér. Válaszoljon "igen"-nel, ha kulcsot
alapvetően megbízhatónak jelöli!

.

.gpg.untrusted_key.override
Ha mégis használni akarja ezt a kulcsot, melyben nem bízunk,
válaszoljon "igen"-nel!
.

.gpg.pklist.user_id.enter
Adja meg a címzett felhasználói azonosítóját!
.

.#gpg.keygen.algo
# fixme: Please translate and remove the hash mark from the key line.
Select the algorithm to use.

DSA (aka DSS) is the Digital Signature Algorithm and can only be used
for signatures.

Elgamal is an encrypt-only algorithm.

RSA may be used for signatures or encryption.

The first (primary) key must always be a key which is capable of signing.
.

.gpg.keygen.algo.rsa_se
Általában nem jó ötlet ugyanazt a kulcsot használni aláíráshoz és
titkosításhoz. Ezt az algoritmust csak bizonyos területeken ajánlatos
használni. Kérem, először konzultáljon a biztonsági szakértőjével!
.

.gpg.keygen.size
Adja meg a kulcs méretét!
.

.gpg.keygen.size.huge.okay
Kérem, adjon "igen" vagy "nem" választ!
.

.gpg.keygen.size.large.okay
Kérem, adjon "igen" vagy "nem" választ!
.

.gpg.keygen.valid
Adja meg a szükséges értéket, ahogy a prompt mutatja!
Lehetséges ISO dátumot is beírni (ÉÉÉÉ-HH-NN), de nem fog rendes
hibaüzenetet kapni, hanem a rendszer megpróbálja az értéket
intervallumként értelmezni.
.

.gpg.keygen.valid.okay
Kérem, adjon "igen" vagy "nem" választ!
.

.gpg.keygen.name
Adja meg a kulcs tulajdonosának a nevét!
.

.gpg.keygen.email
Kérem, adjon meg egy opcionális, de nagyon ajánlott e-mail címet!
.

.gpg.keygen.comment
Kérem, adjon meg egy opcionális megjegyzést!
.

.gpg.keygen.userid.cmd
N  név változtatása
M  megjegyzés változtatása
E  e-mail változtatása
R  kulcsgenerálás folytatása
Q  kilépés a kulcsgenerálásból
.

.gpg.keygen.sub.okay
Válaszoljon "igen"-nel (vagy csak "i"-vel), ha kezdhetjük az alkulcs
létrehozását!
.

.gpg.sign_uid.okay
Kérem, adjon "igen" vagy "nem" választ!
.

.gpg.sign_uid.class
Mielőtt aláír egy felhasználói azonosítót egy kulcson, ellenőriznie kell,
hogy a kulcs a felhasználói azonosítóban megnevezett személyhez tartozik.
Mások számára hasznos lehet, ha tudják, hogy milyen gondosan ellenőrizte
Ön ezt.

"0" azt jelenti, hogy nem tesz az ellenőrzés gondosságára vonatkozó
    kijelentést.

"1" azt jelenti, hogy Ön hiszi, hogy a kulcs annak a személynek a
    tulajdona, aki azt állítja, hogy az övé, de Ön nem tudta ezt
    ellenőrizni, vagy egyszerűen nem ellenőrizte ezt. Ez hasznos egy
    "persona" típusú ellenőrzéshez, mikor Ön egy pszeudonim felhasználó
    kulcsát írja alá.

"2" azt jelenti, hogy Ön a kulcsot hétköznapi alapossággal ellenőrizte.
    Például ez azt jelentheti, hogy ellenőrizte a kulcs ujjlenyomatát, és
    összevetette a kulcson szereplő felhasználóazonosítót egy fényképes
    igazolvánnyal.

"3" azt jelenti, hogy alaposan ellenőrizte a kulcsot. Például ez azt
    jelentheti, hogy a kulcs ujjlenyomatát a tulajdonossal személyesen
    találkozva ellenőrizte, egy nehezen hamisítható, fényképes igazolvánnyal
    (mint az útlevél) meggyőződött arról, hogy a személy neve egyezik a
    kulcson levővel, és végül (e-mail váltással) ellenőrizte, hogy a kulcson
    szereplő e-mail cím a kulcs tulajdonosához tartozik.

A 2-es és 3-as szintekhez adott példák *csak* példák. Végső soron Ön dönti
el, hogy mit jelentenek Önnek a "hétköznapi" és "alapos" kifejezések,
amikor mások kulcsát aláírja.

Ha nem tudja, hogy mit válaszoljon, írjon "0"-t!
.

.gpg.change_passwd.empty.okay
Kérem, adjon "igen" vagy "nem" választ!
.

.gpg.keyedit.save.okay
Kérem, adjon "igen" vagy "nem" választ!
.

.gpg.keyedit.cancel.okay
Kérem, adjon "igen" vagy "nem" választ!
.

.#gpg.keyedit.sign_all.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if you want to sign ALL the user IDs
.

.gpg.keyedit.remove.uid.okay
Válaszoljon "igen"-nel, ha valóban törölni akarja ezt a felhasználóazonosítót!
Minden igazolás törlődik vele együtt!
.

.gpg.keyedit.remove.subkey.okay
Válaszoljon "igen"-nel, ha az alkulcs törölhető.
.

.gpg.keyedit.delsig.valid
Ez egy érvényes aláírás a kulcson. Normál esetben nincs értelme
törölni, mert fontos lehet ahhoz, hogy érvényesítse ezt a kulcsot,
vagy egy másikat, melyet ezzel a kulccsal igazolnak.
.

.gpg.keyedit.delsig.unknown
Ezt az aláírást nem tudom ellenőrizni, mert nincs meg a hozzá tartozó
kulcs. Ajánlatos lenne elhalasztani a törlést addig, amíg meg nem tudja,
hogy melyik kulcsot használták, mert ez az aláíró kulcs bizalmi
kapcsolatot hozhat létre egy már hitelesített kulcson keresztül.
.

.gpg.keyedit.delsig.invalid
Ez az aláírás nem érvényes. Értelmetlen eltávolítani a kulcskarikáról.
.

.gpg.keyedit.delsig.selfsig
Ez egy olyan aláírás, amely összeköti a felhasználóazonosítót
a kulccsal. Általában nem jó ötlet egy ilyen aláírást eltávolítani.
Az is lehetséges, hogy a GnuPG többé nem tudja használni ezt
a kulcsot. Csak akkor tegye ezt, ha valami okból ez az önaláírás nem
érvényes, és rendelkezésre áll egy másik!
.

.gpg.keyedit.updpref.okay
Lecseréli az összes felhasználóazonosítóhoz (vagy csak a kijelöltekhez)
tartozó preferenciákat az aktuális preferenciákra. Minden érintett
önaláírás időpontját egy másodperccel növeli.

.

.gpg.passphrase.enter
Kérem, adja meg a jelszót! Ezt egy titkos mondat. 

.

.gpg.passphrase.repeat
Kérem, ismételje meg az előző jelszót ellenőrzésképpen!
.

.gpg.detached_signature.filename
Adja meg az állomány nevét, melyhez az aláírás tartozik!
.

.gpg.openfile.overwrite.okay
Válaszoljon "igen"-nel, ha felülírható az állomány!
.

.gpg.openfile.askoutname
Kérem, adjon meg egy új fájlnevet! Ha RETURN-t/ENTER-t nyom, akkor
a szögletes zárójelben levő alapértelmezett nevet használom.
.

.gpg.ask_revocation_reason.code
Ajánlatos megadni a visszavonás okát. A helyzettől függően válasszon
a következő listából:
  "A kulcs kompromittálódott."
      Használja ezt akkor, ha oka van azt hinni, hogy titkos kulcsa
      illetéktelen kezekbe került!
  "A kulcsot lecserélték."
      Használja ezt akkor, ha a kulcsot lecserélte egy újabbra!
  "A kulcs már nem használatos."
      Használja ezt akkor, ha már nem használja a kulcsot!
  "A felhasználóazonosító már nem érvényes."
      Használja ezt akkor, ha azt állítja, hogy a felhasználóazonosító
      már nem használatos! Általában érvénytelen e-mail címet jelent.

.

.gpg.ask_revocation_reason.text
Ha akarja, megadhat egy szöveget, melyben megindokolja, hogy miért
adta ki ezt a visszavonó igazolást. Kérem, fogalmazzon tömören!
Egy üres sor jelzi a szöveg végét.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
