/**
  *This file has no copyright assigned and is placed in the Public Domain.
  *This file is part of the mingw-w64 runtime package.
  *No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

import "propidl.idl";
import "wtypes.idl";
import "portabledevicetypes.idl";

interface IPortableDeviceManager;
interface IPortableDevice;
interface IPortableDeviceContent;
interface IPortableDeviceContent2;
interface IEnumPortableDeviceObjectIDs;
interface IPortableDeviceProperties;
interface IPortableDeviceResources;
interface IPortableDeviceCapabilities;
interface IPortableDeviceEventCallback;
interface IPortableDeviceDataStream;
interface IPortableDevicePropertiesBulk;
interface IPortableDevicePropertiesBulkCallback;
interface IPortableDeviceServiceManager;
interface IPortableDeviceService;
interface IPortableDeviceServiceCapabilities;
interface IPortableDeviceServiceMethods;
interface IPortableDeviceServiceMethodCallback;
interface IPortableDeviceDispatchFactory;
interface IPortableDeviceServiceActivation;
interface IPortableDeviceServiceOpenCallback;

[
    object,
    uuid(a1567595-4c2f-4574-a6fa-ecef917b9a40),
    pointer_default(unique)
]
interface IPortableDeviceManager : IUnknown
{
    HRESULT GetDevices(
        [in, out, unique] LPWSTR *device_ids,
        [in, out] DWORD *device_ids_count);

    HRESULT RefreshDeviceList();

    HRESULT GetDeviceFriendlyName(
        [in] LPCWSTR device_id,
        [in, out, unique] WCHAR *device_friendly_name,
        [in, out] DWORD *device_friendly_name_size);

    HRESULT GetDeviceDescription(
        [in] LPCWSTR device_id,
        [in, out, unique] WCHAR *device_description,
        [in, out] DWORD *device_description_size);

    HRESULT GetDeviceManufacturer(
        [in] LPCWSTR device_id,
        [in, out, unique] WCHAR *device_manufacturer,
        [in, out] DWORD *device_manufacturer_size);

    HRESULT GetDeviceProperty(
        [in] LPCWSTR device_id,
        [in] LPCWSTR device_property_name,
        [in, out, unique] BYTE *data,
        [in, out, unique] DWORD *data_size,
        [in, out, unique] DWORD *type);

    HRESULT GetPrivateDevices(
        [in, out, unique] LPWSTR *device_ids,
        [in, out] DWORD *device_ids_count);
}

[
    object,
    uuid(625e2df8-6392-4cf0-9ad1-3cfa5f17775c),
    pointer_default(unique)
]
interface IPortableDevice : IUnknown
{
    HRESULT Open(
        [in] LPCWSTR device_id,
        [in] IPortableDeviceValues *client_info);

    HRESULT SendCommand(
        [in] const DWORD flags,
        [in] IPortableDeviceValues *parameters,
        [out] IPortableDeviceValues **results);

    HRESULT Content(
       [out] IPortableDeviceContent **content);

    HRESULT Capabilities(
       [out] IPortableDeviceCapabilities **capabilities);

    HRESULT Cancel();

    HRESULT Close();

    HRESULT Advise(
        [in] const DWORD flags,
        [in] IPortableDeviceEventCallback *callback,
        [in, unique] IPortableDeviceValues *parameters,
        [out] LPWSTR *cookie);

    HRESULT Unadvise(
        [in] LPCWSTR cookie);

    HRESULT GetPnPDeviceID(
        [out] LPWSTR *device_id);
}

[
    object,
    uuid(6a96ed84-7c73-4480-9938-bf5af477d426),
    pointer_default(unique)
]
interface IPortableDeviceContent : IUnknown
{
    HRESULT EnumObjects(
        [in] const DWORD flags,
        [in] LPCWSTR parent_object_id,
        [in, unique] IPortableDeviceValues *filter,
        [out] IEnumPortableDeviceObjectIDs **enum_device_object);

    HRESULT Properties(
        [out] IPortableDeviceProperties **device_properties);

    HRESULT Transfer(
        [out] IPortableDeviceResources **resources);

    HRESULT CreateObjectWithPropertiesOnly(
       [in] IPortableDeviceValues *values,
       [in, out, unique] LPWSTR *object_id);

    HRESULT CreateObjectWithPropertiesAndData(
       [in] IPortableDeviceValues *values,
       [out] IStream **data,
       [in, out, unique] DWORD *optimal_write_buffer_size,
       [in, out, unique] LPWSTR *cookie);

    HRESULT Delete(
       [in] const DWORD options,
       [in] IPortableDevicePropVariantCollection *object_ids,
       [in, out, unique] IPortableDevicePropVariantCollection **results);

    HRESULT GetObjectIDsFromPersistentUniqueIDs(
        [in] IPortableDevicePropVariantCollection *persistent_unique_ids,
        [out] IPortableDevicePropVariantCollection **object_ids);

    HRESULT Cancel();

    HRESULT Move(
        [in] IPortableDevicePropVariantCollection *object_ids,
        [in] LPCWSTR destination_folder_object_id,
        [in, out, unique] IPortableDevicePropVariantCollection **results);

    HRESULT Copy(
        [in] IPortableDevicePropVariantCollection *object_ids,
        [in] LPCWSTR destination_folder_object_id,
        [in, out, unique] IPortableDevicePropVariantCollection **results);
}

[
    object,
    uuid(9b4add96-f6bf-4034-8708-eca72bf10554),
    pointer_default(unique)
]
interface IPortableDeviceContent2 : IPortableDeviceContent
{
    HRESULT UpdateObjectWithPropertiesAndData(
        [in] LPCWSTR object_id,
        [in] IPortableDeviceValues *device_properties,
        [out] IStream **data,
        [in, out, unique] DWORD *optimal_write_buffer_size);
}

[
    object,
    uuid(10ece955-cf41-4728-bfa0-41eedf1bbf19),
    pointer_default(unique)
]
interface IEnumPortableDeviceObjectIDs : IUnknown
{
    HRESULT Next(
        [in] ULONG objects,
        [out, size_is(objects), length_is(*fetched)] LPWSTR *obj_ids,
        [in, out, unique] ULONG *fetched);

    HRESULT Skip(
        [in] ULONG objects);

    HRESULT Reset();

    HRESULT Clone(
        [out] IEnumPortableDeviceObjectIDs **enum_device_object);

    HRESULT Cancel();
}

[
    object,
    uuid(7f6d695c-03df-4439-a809-59266beee3a6),
    pointer_default(unique)
]
interface IPortableDeviceProperties : IUnknown
{
    HRESULT GetSupportedProperties(
        [in] LPCWSTR object_id,
        [out] IPortableDeviceKeyCollection **keys);

    HRESULT GetPropertyAttributes(
        [in] LPCWSTR object_id,
        [in] REFPROPERTYKEY key,
        [out] IPortableDeviceValues **attributes);

    HRESULT GetValues(
        [in] LPCWSTR object_id,
        [in, unique] IPortableDeviceKeyCollection *keys,
        [out] IPortableDeviceValues **values);

    HRESULT SetValues(
        [in] LPCWSTR object_id,
        [in] IPortableDeviceValues *values,
        [out] IPortableDeviceValues **results);

    HRESULT Delete(
        [in] LPCWSTR object_id,
        [in] IPortableDeviceKeyCollection *keys);

    HRESULT Cancel();
}

[
    object,
    uuid(fd8878ac-d841-4d17-891c-e6829cdb6934),
    pointer_default(unique)
]
interface IPortableDeviceResources : IUnknown
{
    HRESULT GetSupportedResources(
        [in] LPCWSTR object_id,
        [out] IPortableDeviceKeyCollection **keys);

    HRESULT GetResourceAttributes(
        [in] LPCWSTR object_id,
        [in] REFPROPERTYKEY key,
        [out] IPortableDeviceValues **resource_attributes);

    HRESULT GetStream(
        [in] LPCWSTR object_id,
        [in] REFPROPERTYKEY key,
        [in] const DWORD mode,
        [in, out, unique] DWORD *optimal_buffer_size,
        [out] IStream **stream);

    HRESULT Delete(
        [in] LPCWSTR object_id,
        [in] IPortableDeviceKeyCollection *keys);

    HRESULT Cancel();

    HRESULT CreateResource(
       [in] IPortableDeviceValues *resource_attributes,
       [out] IStream **data,
       [in, out, unique] DWORD *optimal_write_buffer_size,
       [in, out, unique] LPWSTR *cookie);
}

[
    object,
    uuid(2c8c6dbf-e3dc-4061-becc-8542e810d126),
    pointer_default(unique)
]
interface IPortableDeviceCapabilities : IUnknown
{
    HRESULT GetSupportedCommands(
        [out] IPortableDeviceKeyCollection **commands);

    HRESULT GetCommandOptions(
        [in] REFPROPERTYKEY command,
        [out] IPortableDeviceValues **options);

    HRESULT GetFunctionalCategories(
        [out] IPortableDevicePropVariantCollection **categories);

    HRESULT GetFunctionalObjects(
        [in] REFGUID category,
        [out] IPortableDevicePropVariantCollection **object_ids);

    HRESULT GetSupportedContentTypes(
        [in] REFGUID category,
        [out] IPortableDevicePropVariantCollection **content_types);

    HRESULT GetSupportedFormats(
        [in] REFGUID content_type,
        [out] IPortableDevicePropVariantCollection **formats);

    HRESULT GetSupportedFormatProperties(
        [in] REFGUID format,
        [out] IPortableDeviceKeyCollection **keys);

    HRESULT GetFixedPropertyAttributes(
        [in] REFGUID format,
        [in] REFPROPERTYKEY key,
        [out] IPortableDeviceValues **attributes);

    HRESULT Cancel();

    HRESULT GetSupportedEvents(
        [out] IPortableDevicePropVariantCollection **events_collection);

    HRESULT GetEventOptions(
        [in] REFGUID event_id,
        [out] IPortableDeviceValues **options);
}

[
    object,
    uuid(a8792a31-f385-493c-a893-40f64eb45f6e),
    pointer_default(unique)
]
interface IPortableDeviceEventCallback : IUnknown
{
    HRESULT OnEvent(
        [in] IPortableDeviceValues *event_parameters);
}

[
    object,
    uuid(88e04db3-1012-4d64-9996-f703a950d3f4),
    pointer_default(unique)
]
interface IPortableDeviceDataStream : IStream
{
    HRESULT GetObjectID(
        [out] LPWSTR *object_id);

    HRESULT Cancel();
}

[
    object,
    uuid(5e98025f-bfc4-47a2-9a5f-bc900a507c67),
    pointer_default(unique)
]
interface IPortableDeviceUnitsStream : IUnknown
{
    [local]
    HRESULT SeekInUnits(
        [in] LARGE_INTEGER dlib_move,
        [in] WPD_STREAM_UNITS units,
        [in] DWORD origin,
        [out, optional] ULARGE_INTEGER *lib_new_position);

    [call_as(SeekInUnits)]
    HRESULT RemoteSeekInUnits(
        [in] LARGE_INTEGER dlib_move,
        [in] WPD_STREAM_UNITS units,
        [in] DWORD origin,
        [out] ULARGE_INTEGER *lib_new_position);

    HRESULT Cancel();
}

[
    object,
    uuid(482b05c0-4056-44ed-9e0f-5e23b009da93),
    pointer_default(unique)
]
interface IPortableDevicePropertiesBulk : IUnknown
{
    HRESULT QueueGetValuesByObjectList(
        [in] IPortableDevicePropVariantCollection *object_ids,
        [in] IPortableDeviceKeyCollection *keys,
        [in] IPortableDevicePropertiesBulkCallback *callback,
        [out] GUID *context);

    HRESULT QueueGetValuesByObjectFormat(
        [in] REFGUID guid_object_format,
        [in] LPCWSTR parent_object_id,
        [in] const DWORD depth,
        [in] IPortableDeviceKeyCollection *keys,
        [in] IPortableDevicePropertiesBulkCallback *callback,
        [out] GUID *context);

    HRESULT QueueSetValuesByObjectList(
        [in] IPortableDeviceValuesCollection *object_values,
        [in] IPortableDevicePropertiesBulkCallback *callback,
        [out] GUID *context);

    HRESULT Start([in] REFGUID context);

    HRESULT Cancel([in] REFGUID context);
}

[
    object,
    uuid(9deacb80-11e8-40e3-a9f3-f557986a7845),
    pointer_default(unique)
]
interface IPortableDevicePropertiesBulkCallback : IUnknown
{
    HRESULT OnStart(
        [in] REFGUID context);

    HRESULT OnProgress(
        [in] REFGUID context,
        [in] IPortableDeviceValuesCollection *results);

    HRESULT OnEnd(
        [in] REFGUID context,
        [in] HRESULT hr_status);
}

[
    object,
    uuid(a8abc4e9-a84a-47a9-80b3-c5d9b172a961),
    pointer_default(unique)
]
interface IPortableDeviceServiceManager : IUnknown
{
    HRESULT GetDeviceServices(
        [in] LPCWSTR device_id,
        [in] REFGUID guid_service_category,
        [in, out, unique] LPWSTR *services,
        [in, out] DWORD *services_count);

    HRESULT GetDeviceForService(
        [in] LPCWSTR service_id,
        [out] LPWSTR *device_id);
}

[
    object,
    uuid(d3bd3a44-d7b5-40a9-98b7-2fa4d01dec08),
    pointer_default(unique)
]
interface IPortableDeviceService : IUnknown
{
    HRESULT Open(
        [in] LPCWSTR service_id,
        [in] IPortableDeviceValues *client_info);

    HRESULT Capabilities(
        [out] IPortableDeviceServiceCapabilities **capabilities);

    HRESULT Content(
        [out] IPortableDeviceContent2 **content);

    HRESULT Methods(
        [out] IPortableDeviceServiceMethods **service_methods);

    HRESULT Cancel();

    HRESULT Close();

    HRESULT GetServiceObjectID(
        [out] LPWSTR *service_object_id);

    HRESULT GetPnPServiceID(
        [out] LPWSTR *service_id);

    HRESULT Advise(
        [in] const DWORD flags,
        [in] IPortableDeviceEventCallback *callback,
        [in, unique] IPortableDeviceValues *parameters,
        [out] LPWSTR *cookie);

    HRESULT Unadvise(
        [in] LPCWSTR cookie);

    HRESULT SendCommand(
        [in] const DWORD flags,
        [in] IPortableDeviceValues *parameters,
        [out] IPortableDeviceValues **results);
}

[
    object,
    uuid(24dbd89d-413e-43e0-bd5b-197f3c56c886),
    pointer_default(unique)
]
interface IPortableDeviceServiceCapabilities : IUnknown
{
    HRESULT GetSupportedMethods(
        [out] IPortableDevicePropVariantCollection **supported_methods);

    HRESULT GetSupportedMethodsByFormat(
        [in] REFGUID format,
        [out] IPortableDevicePropVariantCollection **supported_methods);

    HRESULT GetMethodAttributes (
        [in] REFGUID method,
        [out] IPortableDeviceValues **attributes);

    HRESULT GetMethodParameterAttributes(
        [in] REFGUID method,
        [in] REFPROPERTYKEY parameter,
        [out] IPortableDeviceValues **attributes);

    HRESULT GetSupportedFormats(
        [out] IPortableDevicePropVariantCollection **formats);

    HRESULT GetFormatAttributes(
        [in] REFGUID format,
        [out] IPortableDeviceValues **attributes);

    HRESULT GetSupportedFormatProperties(
        [in] REFGUID format,
        [out] IPortableDeviceKeyCollection **keys);

    HRESULT GetFormatPropertyAttributes(
        [in] REFGUID format,
        [in] REFPROPERTYKEY property,
        [out] IPortableDeviceValues **attributes);

    HRESULT GetSupportedEvents(
        [out] IPortableDevicePropVariantCollection **events);

    HRESULT GetEventAttributes(
        [in] REFGUID event_id,
        [out] IPortableDeviceValues **attributes);

    HRESULT GetEventParameterAttributes(
        [in] REFGUID event_id,
        [in] REFPROPERTYKEY parameter,
        [out] IPortableDeviceValues **attributes);

    HRESULT GetInheritedServices(
        [in] const DWORD inheritance_type,
        [out] IPortableDevicePropVariantCollection **services);

    HRESULT GetFormatRenderingProfiles(
        [in] REFGUID format,
        [out] IPortableDeviceValuesCollection **rendering_profiles);

    HRESULT GetSupportedCommands(
        [out] IPortableDeviceKeyCollection **commands);

    HRESULT GetCommandOptions(
        [in] REFPROPERTYKEY command,
        [out] IPortableDeviceValues **options);

    HRESULT Cancel();
}

[
    object,
    uuid(e20333c9-fd34-412d-a381-cc6f2d820df7),
    pointer_default(unique)
]
interface IPortableDeviceServiceMethods : IUnknown
{
    HRESULT Invoke(
       [in] REFGUID method_id,
       [in, unique] IPortableDeviceValues *parameters,
       [in, out, unique] IPortableDeviceValues **results);

    HRESULT InvokeAsync(
       [in] REFGUID method_id,
       [in, unique] IPortableDeviceValues *parameters,
       [in, unique] IPortableDeviceServiceMethodCallback *callback);

    HRESULT Cancel(
       [in, unique] IPortableDeviceServiceMethodCallback *callback);
}

[
    object,
    uuid(c424233c-afce-4828-a756-7ed7a2350083),
    pointer_default(unique)
]
interface IPortableDeviceServiceMethodCallback : IUnknown
{
    HRESULT OnComplete(
       [in] HRESULT hr_status,
       [in] IPortableDeviceValues *results);
}

[
    object,
    uuid(e56b0534-d9b9-425c-9b99-75f97cb3d7c8),
    pointer_default(unique)
]
interface IPortableDeviceServiceActivation : IUnknown
{
    HRESULT OpenAsync(
        [in] LPCWSTR service_id,
        [in] IPortableDeviceValues *client_info,
        [in] IPortableDeviceServiceOpenCallback *callback);

    HRESULT CancelOpenAsync();
}

[
    object,
    uuid(bced49c8-8efe-41ed-960b-61313abd47a9),
    pointer_default(unique)
]
interface IPortableDeviceServiceOpenCallback : IUnknown
{
    HRESULT OnComplete(
       [in] HRESULT hr_status);
}

[
    object,
    local,
    uuid(5e1eafc3-e3d7-4132-96fa-759c0f9d1e0f)
]
interface IPortableDeviceDispatchFactory : IUnknown
{
    HRESULT GetDeviceDispatch(
       [in] LPCWSTR device_id,
       [out] IDispatch **device_dispatch);
}

[
    object,
    dual,
    local,
    nonextensible,
    uuid(94fc7953-5ca1-483a-8aee-df52e7747d00)
]
interface IPortableDeviceWebControl: IDispatch
{
    HRESULT GetDeviceFromId(
       [in] BSTR device_id,
       [out, retval] IDispatch **device);

    HRESULT GetDeviceFromIdAsync(
       [in] BSTR device_id,
       [in] IDispatch *completion_handler,
       [in, unique] IDispatch *error_handler);
}

[
    uuid(1f001332-1a57-4934-be31-affc99f4ee0a),
    version(1.0)
]
library PortableDeviceApiLib
{
    [
        uuid(728a21c5-3d9e-48d7-9810-864848f0f404)
    ]
    coclass PortableDevice
    {
        [default] interface IPortableDevice;
    }

    [
        uuid(0af10cec-2ecd-4b92-9581-34f6ae0637f3)
    ]
    coclass PortableDeviceManager
    {
        [default] interface IPortableDeviceManager;
    }

    [
        uuid(ef5db4c2-9312-422c-9152-411cd9c4dd84)
    ]
    coclass PortableDeviceService
    {
        [default] interface IPortableDeviceService;
    }

    [
        uuid(*************-4658-ae01-0b4ae830b6b0)
    ]
    coclass PortableDeviceDispatchFactory
    {
        [default] interface IPortableDeviceDispatchFactory;
    }

    [
        uuid(f7c0039a-4762-488a-b4b3-760ef9a1ba9b)
    ]
    coclass PortableDeviceFTM
    {
        [default] interface IPortableDevice;
    }

    [
        uuid(1649b154-c794-497a-9b03-f3f0121302f3)
    ]
    coclass PortableDeviceServiceFTM
    {
        [default] interface IPortableDeviceService;
    }

    [
        uuid(186dd02c-2dec-41b5-a7d4-b59056fade51)
    ]
    coclass PortableDeviceWebControl
    {
        [default] interface IPortableDeviceWebControl;
    }
}
