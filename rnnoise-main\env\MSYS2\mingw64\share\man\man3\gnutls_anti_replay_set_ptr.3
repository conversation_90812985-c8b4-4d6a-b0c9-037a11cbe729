.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_anti_replay_set_ptr" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_anti_replay_set_ptr \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_anti_replay_set_ptr(gnutls_anti_replay_t " anti_replay ", void * " ptr ");"
.SH ARGUMENTS
.IP "gnutls_anti_replay_t anti_replay" 12
is a \fBgnutls_anti_replay_t\fP type.
.IP "void * ptr" 12
is the pointer
.SH "DESCRIPTION"
Sets the pointer that will be provided to db add function
as the first argument.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
