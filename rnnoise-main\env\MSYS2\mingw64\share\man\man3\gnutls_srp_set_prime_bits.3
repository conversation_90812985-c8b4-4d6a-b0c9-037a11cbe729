.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srp_set_prime_bits" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srp_set_prime_bits \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_srp_set_prime_bits(gnutls_session_t " session ", unsigned int " bits ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int bits" 12
is the number of bits
.SH "DESCRIPTION"
This function sets the minimum accepted number of bits, for use in
an SRP key exchange.  If zero, the default 2048 bits will be used.

In the client side it sets the minimum accepted number of bits.  If
a server sends a prime with less bits than that
\fBGNUTLS_E_RECEIVED_ILLEGAL_PARAMETER\fP will be returned by the
handshake.

This function has no effect in server side.
.SH "SINCE"
2.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
