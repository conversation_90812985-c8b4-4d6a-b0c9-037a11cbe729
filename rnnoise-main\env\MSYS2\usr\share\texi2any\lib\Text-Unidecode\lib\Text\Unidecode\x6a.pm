# Time-stamp: "Sat Jul 14 00:27:32 2001 by Automatic Bizooty (__blocks2pm.plx)"
$Text::Unidecode::Char[0x6a] = [
'<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ',
'<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON><PERSON> ', '<PERSON>g ', '<PERSON> ', '<PERSON>ha ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', 'Cong ', qq{[?] }, '<PERSON><PERSON><PERSON> ', '<PERSON><PERSON> ',
'<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ',
'<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>han ',
'<PERSON>o ', '<PERSON> ', '<PERSON> ', '<PERSON>n ', '<PERSON>i ', '<PERSON> ', '<PERSON> ', '<PERSON>g ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>e ', '<PERSON>e ', '<PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON>uo ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON>ng ', 'Rui ', 'Zhu ', 'Chuang ', 'Zeng ', 'Fen ', 'Qiong ', 'Ran ', 'Heng ', 'Cen ', 'Gu ', 'Liu ', 'Lao ',
'Gao ', 'Chu ', 'Zusa ', 'Nude ', 'Ca ', 'San ', 'Ji ', 'Dou ', 'Shou ', 'Lu ', qq{[?] }, qq{[?] }, 'Yuan ', 'Ta ', 'Shu ', 'Jiang ',
'Tan ', 'Lin ', 'Nong ', 'Yin ', 'Xi ', 'Sui ', 'Shan ', 'Zui ', 'Xuan ', 'Cheng ', 'Gan ', 'Ju ', 'Zui ', 'Yi ', 'Qin ', 'Pu ',
'Yan ', 'Lei ', 'Feng ', 'Hui ', 'Dang ', 'Ji ', 'Sui ', 'Bo ', 'Bi ', 'Ding ', 'Chu ', 'Zhua ', 'Kuai ', 'Ji ', 'Jie ', 'Jia ',
'Qing ', 'Zhe ', 'Jian ', 'Qiang ', 'Dao ', 'Yi ', 'Biao ', 'Song ', 'She ', 'Lin ', 'Kunugi ', 'Cha ', 'Meng ', 'Yin ', 'Tao ', 'Tai ',
'Mian ', 'Qi ', 'Toan ', 'Bin ', 'Huo ', 'Ji ', 'Qian ', 'Mi ', 'Ning ', 'Yi ', 'Gao ', 'Jian ', 'Yin ', 'Er ', 'Qing ', 'Yan ',
'Qi ', 'Mi ', 'Zhao ', 'Gui ', 'Chun ', 'Ji ', 'Kui ', 'Po ', 'Deng ', 'Chu ', qq{[?] }, 'Mian ', 'You ', 'Zhi ', 'Guang ', 'Qian ',
'Lei ', 'Lei ', 'Sa ', 'Lu ', 'Li ', 'Cuan ', 'Lu ', 'Mie ', 'Hui ', 'Ou ', 'Lu ', 'Jie ', 'Gao ', 'Du ', 'Yuan ', 'Li ',
'Fei ', 'Zhuo ', 'Sou ', 'Lian ', 'Tamo ', 'Chu ', qq{[?] }, 'Zhu ', 'Lu ', 'Yan ', 'Li ', 'Zhu ', 'Chen ', 'Jie ', 'E ', 'Su ',
'Huai ', 'Nie ', 'Yu ', 'Long ', 'Lai ', qq{[?] }, 'Xian ', 'Kwi ', 'Ju ', 'Xiao ', 'Ling ', 'Ying ', 'Jian ', 'Yin ', 'You ', 'Ying ',
];
1;
