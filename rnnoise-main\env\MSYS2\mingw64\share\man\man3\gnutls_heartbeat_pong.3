.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_heartbeat_pong" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_heartbeat_pong \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_heartbeat_pong(gnutls_session_t " session ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function replies to a ping by sending a pong to the peer.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.1.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
