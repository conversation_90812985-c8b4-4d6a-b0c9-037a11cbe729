#!/bin/sh

cat >&2 <<'EOT'
WARNING: git-remote-hg is now maintained independently.
WARNING: For more information visit https://github.com/felipec/git-remote-hg
WARNING:
WARNING: You can pick a directory on your $PATH and download it, e.g.:
WARNING:   $ wget -O $HOME/bin/git-remote-hg \
WARNING:     https://raw.github.com/felipec/git-remote-hg/master/git-remote-hg
WARNING:   $ chmod +x $HOME/bin/git-remote-hg
EOT
