# help.id.txt - id GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
Terserah anda untuk memberi nilai baru di sini; nilai ini tidak akan diekspor
ke pihak ketiga. Kami perlu untuk mengimplementasikan web-of-trust; tidak ada
kaitan dengan (membuat secara implisit) web-of-certificates.
.

.gpg.edit_ownertrust.set_ultimate.okay
Untuk membuat Web-of-Trust, GnuPG perlu tahu kunci mana yang
sangat dipercaya - mereka biasanya adalah kunci yang anda punya
akses ke kunci rahasia.  Jawab "yes" untuk menset kunci ini ke
sangat dipercaya

.

.gpg.untrusted_key.override
Jika anda ingin menggunakan kunci tidak terpercaya ini, jawab "ya".
.

.gpg.pklist.user_id.enter
Masukkan ID user penerima pesan.
.

.#gpg.keygen.algo
# fixme: Please translate and remove the hash mark from the key line.
Select the algorithm to use.

DSA (aka DSS) is the Digital Signature Algorithm and can only be used
for signatures.

Elgamal is an encrypt-only algorithm.

RSA may be used for signatures or encryption.

The first (primary) key must always be a key which is capable of signing.
.

.gpg.keygen.algo.rsa_se
Secara umum bukan ide baik untuk menggunakan kunci yang sama untuk menandai dan
mengenkripsi.  Algoritma ini seharusnya digunakan dalam domain tertentu.
Silakan berkonsultasi dulu dengan ahli keamanan anda.
.

.gpg.keygen.size
Masukkan ukuran kunci
.

.gpg.keygen.size.huge.okay
Jawab "ya" atau "tidak"
.

.gpg.keygen.size.large.okay
Jawab "ya" atau "tidak"
.

.gpg.keygen.valid
Masukkan nilai yang diperlukan seperti pada prompt.
Dapat digunakan format (YYYY-MM-DD) untuk mengisi tanggal ISO tetapi anda
tidak akan mendapat respon kesalahan yang baik - sebaiknya sistem akan
berusaha menginterprestasi nilai yang diberikan sebagai sebuah interval.
.

.gpg.keygen.valid.okay
Jawab "ya" atau "tidak"
.

.gpg.keygen.name
Masukkan nama pemegang kunci
.

.gpg.keygen.email
silakan masukkan alamat email (pilihan namun sangat dianjurkan)
.

.gpg.keygen.comment
Silakan masukkan komentar tambahan
.

.gpg.keygen.userid.cmd
N  untuk merubah nama.
K  untuk merubah komentar.
E  untuk merubah alamat email.
O  untuk melanjutkan dengan pembuatan kunci.
K  untuk menghentikan pembuatan kunci.
.

.gpg.keygen.sub.okay
Jawab "ya" (atau "y") jika telah siap membuat subkey.
.

.gpg.sign_uid.okay
Jawab "ya" atau "tidak"
.

.gpg.sign_uid.class
Ketika anda menandai user ID pada kunci, anda perlu memverifikasi bahwa kunci
milik orang yang disebut dalam user ID.  Ini penting bagi orang lain untuk tahu
seberapa cermat anda memverifikasi ini.

"0" berarti anda tidak melakukan klaim tentang betapa cermat anda memverifikasi    kunci.

"1" berarti anda percaya bahwa kunci dimiliki oleh orang yang mengklaim memilikinya
    namun anda tidak dapat, atau tidak memverifikasi kunci sama sekali.  Hal ini bergunabagi
    verifikasi "persona", yaitu anda menandai kunci user pseudonymous

"2" berarti anda melakukan verifikasi kasual atas kunci.  Sebagai contoh, halini dapat
    berarti bahwa anda memverifikasi fingerprint kunci dan memeriksa user ID pada kunci
    dengan photo ID.

"3" berarti anda melakukan verifikasi ekstensif atas kunci.  Sebagai contoh, hal ini
    dapat berarti anda memverifikasi fingerprint kunci dengan pemilik kunci
    secara personal, dan anda memeriksa, dengan menggunakan dokumen yang sulit dipalsukan yang memiliki
    photo ID (seperti paspor) bahwa nama pemilik kunci cocok dengan
    nama user ID kunci, dan bahwa anda telah memverifikasi (dengan pertukaran
    email) bahwa alamat email pada kunci milik pemilik kunci.

Contoh-contoh pada level 2 dan 3 hanyalah contoh.
Pada akhirnya, terserah anda untuk memutuskan apa arti  "kasual" dan "ekstensif"
bagi anda ketika menandai kunci lain.

Jika anda tidak tahu jawaban yang tepat, jawab "0".
.

.gpg.change_passwd.empty.okay
Jawab "ya" atau "tidak"
.

.gpg.keyedit.save.okay
Jawab "ya" atau "tidak"
.

.gpg.keyedit.cancel.okay
Jawab "ya" atau "tidak"
.

.#gpg.keyedit.sign_all.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if you want to sign ALL the user IDs
.

.gpg.keyedit.remove.uid.okay
Jawab "ya" jika anda benar-benar ingin menghapus ID user ini.
Seluruh sertifikat juga akan hilang!
.

.gpg.keyedit.remove.subkey.okay
Jawab "ya" jika ingin menghapus subkey
.

.gpg.keyedit.delsig.valid
Ini adalah signature valid untuk kunci; anda normalnya tdk ingin menghapus
signature ini karena mungkin penting membangun koneksi trust ke kunci atau
ke kunci tersertifikasi lain dengan kunci ini.
.

.gpg.keyedit.delsig.unknown
Signature ini tidak dapat diperiksa karena anda tidak memiliki kunci
korespondennya. Anda perlu menunda penghapusannya hingga anda tahu
kunci yang digunakan karena kunci penanda ini mungkin membangun suatu
koneksi trust melalui kunci yang telah tersertifikasi lain.
.

.gpg.keyedit.delsig.invalid
Signature tidak valid.  Adalah hal yang masuk akal untuk menghapusnya dari
keyring anda
.

.gpg.keyedit.delsig.selfsig
Ini adalah signature yang menghubungkan ID pemakai ke kunci. Biasanya
bukan ide yang baik untuk menghapus signature semacam itu. Umumnya
GnuPG tidak akan dapat menggunakan kunci ini lagi. Sehingga lakukan hal
ini bila self-signature untuk beberapa alasan tidak valid dan
tersedia yang kedua.
.

.gpg.keyedit.updpref.okay
Rubah preferensi seluruh user ID (atau hanya yang terpilih)
ke daftar preferensi saat ini.  Timestamp seluruh self-signature
yang terpengaruh akan bertambah satu detik.

.

.gpg.passphrase.enter
Silakan masukkan passphrase; ini kalimat rahasia

.

.gpg.passphrase.repeat
Silakan ulangi passphrase terakhir, sehingga anda yakin yang anda ketikkan.
.

.gpg.detached_signature.filename
Beri nama file tempat berlakunya signature
.

.gpg.openfile.overwrite.okay
Jawab "ya" jika tidak apa-apa menimpa file
.

.gpg.openfile.askoutname
Silakan masukan nama file baru. Jika anda hanya menekan RETURN nama
file baku (yang diapit tanda kurung) akan dipakai.
.

.gpg.ask_revocation_reason.code
Anda harus menspesifikasikan alasan pembatalan. Semua ini tergantung
konteks, anda dapat memilih dari daftar berikut:
 "Key has been compromised"
      Gunakan ini jika anda punya alasan untuk percaya bahwa orang yang tidak berhak
      memiliki akses ke kunci pribadi anda.
  "Key is superseded"
      Gunakan ini bila anda mengganti kunci anda dengan yang baru.
  "Key is no longer used"
      Gunakan ini bila anda telah mempensiunkan kunci ini.
  "User ID is no longer valid"
      Gunakan ini untuk menyatakan user ID tidak boleh digunakan lagi;
      normalnya digunakan untuk menandai bahwa alamat email tidak valid lagi.

.

.gpg.ask_revocation_reason.text
Jika anda suka, anda dapat memasukkan teks menjelaskan mengapa anda
mengeluarkan sertifikat pembatalan ini. Buatlah ringkas.
Baris kosong mengakhiri teks.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
