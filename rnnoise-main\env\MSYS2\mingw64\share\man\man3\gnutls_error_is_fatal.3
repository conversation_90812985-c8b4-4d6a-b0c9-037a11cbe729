.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_error_is_fatal" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_error_is_fatal \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_error_is_fatal(int " error ");"
.SH ARGUMENTS
.IP "int error" 12
is a GnuTLS error code, a negative error code
.SH "DESCRIPTION"
If a GnuTLS function returns a negative error code you may feed that
value to this function to see if the error condition is fatal to
a TLS session (i.e., must be terminated).

Note that you may also want to check the error code manually, since some
non\-fatal errors to the protocol (such as a warning alert or
a rehandshake request) may be fatal for your program.

This function is only useful if you are dealing with errors from
functions that relate to a TLS session (e.g., record layer or handshake
layer handling functions).
.SH "RETURNS"
Non\-zero value on fatal errors or zero on non\-fatal.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
