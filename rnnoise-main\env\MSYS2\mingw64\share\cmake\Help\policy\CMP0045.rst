CMP0045
-------

.. |REMOVED_IN_CMAKE_VERSION| replace:: 4.0
.. include:: REMOVED_PROLOGUE.txt

Error on non-existent target in get_target_property.

In CMake 2.8.12 and lower, the :command:`get_target_property` command accepted
a non-existent target argument without issuing any error or warning.  The
result variable is set to a ``-NOTFOUND`` value.

The ``OLD`` behavior for this policy is to issue no warning and set the result
variable to a ``-NOTFOUND`` value.  The ``NEW`` behavior
for this policy is to issue a ``FATAL_ERROR`` if the command is called with a
non-existent target.

.. |INTRODUCED_IN_CMAKE_VERSION| replace:: 3.0
.. |WARNED_OR_DID_NOT_WARN| replace:: warned
.. include:: REMOVED_EPILOGUE.txt
