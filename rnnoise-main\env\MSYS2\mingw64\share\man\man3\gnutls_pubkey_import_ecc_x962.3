.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_import_ecc_x962" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_import_ecc_x962 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_import_ecc_x962(gnutls_pubkey_t " key ", const gnutls_datum_t * " parameters ", const gnutls_datum_t * " ecpoint ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
The structure to store the parsed key
.IP "const gnutls_datum_t * parameters" 12
DER encoding of an ANSI X9.62 parameters
.IP "const gnutls_datum_t * ecpoint" 12
DER encoding of ANSI X9.62 ECPoint
.SH "DESCRIPTION"
This function will convert the given elliptic curve parameters to a
\fBgnutls_pubkey_t\fP.  The output will be stored in  \fIkey\fP .
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
