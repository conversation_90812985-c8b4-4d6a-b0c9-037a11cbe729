// -*- C++ -*- C forwarding header.

// Copyright (C) 1997-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/cmath
 *  This is a Standard C++ Library file.  You should @c \#include this file
 *  in your programs, rather than any of the @a *.h implementation files.
 *
 *  This is the C++ version of the Standard C Library header @c math.h,
 *  and its contents are (mostly) the same as that header, but are all
 *  contained in the namespace @c std (except for names which are defined
 *  as macros in C).
 */

//
// ISO C++ 14882: 26.5  C library
//

#ifndef _GLIBCXX_CMATH
#define _GLIBCXX_CMATH 1

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#include <bits/requires_hosted.h>

#include <bits/c++config.h>
#include <bits/cpp_type_traits.h>
#include <ext/type_traits.h>

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wpedantic" // include_next
#define _GLIBCXX_INCLUDE_NEXT_C_HEADERS
#include_next <math.h>
#undef _GLIBCXX_INCLUDE_NEXT_C_HEADERS
#pragma GCC diagnostic pop

#include <bits/std_abs.h>

#define __glibcxx_want_hypot
#define __glibcxx_want_interpolate
#include <bits/version.h>

// Get rid of those macros defined in <math.h> in lieu of real functions.
#undef div
#undef acos
#undef asin
#undef atan
#undef atan2
#undef ceil
#undef cos
#undef cosh
#undef exp
#undef fabs
#undef floor
#undef fmod
#undef frexp
#undef ldexp
#undef log
#undef log10
#undef modf
#undef pow
#undef sin
#undef sinh
#undef sqrt
#undef tan
#undef tanh

extern "C++"
{
namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  using ::acos;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  acos(float __x)
  { return __builtin_acosf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  acos(long double __x)
  { return __builtin_acosl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    acos(_Tp __x)
    { return __builtin_acos(__x); }

  using ::asin;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  asin(float __x)
  { return __builtin_asinf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  asin(long double __x)
  { return __builtin_asinl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    asin(_Tp __x)
    { return __builtin_asin(__x); }

  using ::atan;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  atan(float __x)
  { return __builtin_atanf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  atan(long double __x)
  { return __builtin_atanl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    atan(_Tp __x)
    { return __builtin_atan(__x); }

  using ::atan2;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  atan2(float __y, float __x)
  { return __builtin_atan2f(__y, __x); }

  inline _GLIBCXX_CONSTEXPR long double
  atan2(long double __y, long double __x)
  { return __builtin_atan2l(__y, __x); }
#endif

  using ::ceil;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  ceil(float __x)
  { return __builtin_ceilf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  ceil(long double __x)
  { return __builtin_ceill(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    ceil(_Tp __x)
    { return __builtin_ceil(__x); }

  using ::cos;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  cos(float __x)
  { return __builtin_cosf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  cos(long double __x)
  { return __builtin_cosl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    cos(_Tp __x)
    { return __builtin_cos(__x); }

  using ::cosh;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  cosh(float __x)
  { return __builtin_coshf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  cosh(long double __x)
  { return __builtin_coshl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    cosh(_Tp __x)
    { return __builtin_cosh(__x); }

  using ::exp;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  exp(float __x)
  { return __builtin_expf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  exp(long double __x)
  { return __builtin_expl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    exp(_Tp __x)
    { return __builtin_exp(__x); }

  using ::fabs;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  fabs(float __x)
  { return __builtin_fabsf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  fabs(long double __x)
  { return __builtin_fabsl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    fabs(_Tp __x)
    { return __builtin_fabs(__x); }

  using ::floor;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  floor(float __x)
  { return __builtin_floorf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  floor(long double __x)
  { return __builtin_floorl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    floor(_Tp __x)
    { return __builtin_floor(__x); }

  using ::fmod;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  fmod(float __x, float __y)
  { return __builtin_fmodf(__x, __y); }

  inline _GLIBCXX_CONSTEXPR long double
  fmod(long double __x, long double __y)
  { return __builtin_fmodl(__x, __y); }
#endif

  using ::frexp;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline float
  frexp(float __x, int* __exp)
  { return __builtin_frexpf(__x, __exp); }

  inline long double
  frexp(long double __x, int* __exp)
  { return __builtin_frexpl(__x, __exp); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    frexp(_Tp __x, int* __exp)
    { return __builtin_frexp(__x, __exp); }

  using ::ldexp;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  ldexp(float __x, int __exp)
  { return __builtin_ldexpf(__x, __exp); }

  inline _GLIBCXX_CONSTEXPR long double
  ldexp(long double __x, int __exp)
  { return __builtin_ldexpl(__x, __exp); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    ldexp(_Tp __x, int __exp)
    { return __builtin_ldexp(__x, __exp); }

  using ::log;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  log(float __x)
  { return __builtin_logf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  log(long double __x)
  { return __builtin_logl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    log(_Tp __x)
    { return __builtin_log(__x); }

  using ::log10;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  log10(float __x)
  { return __builtin_log10f(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  log10(long double __x)
  { return __builtin_log10l(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    log10(_Tp __x)
    { return __builtin_log10(__x); }

  using ::modf;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline float
  modf(float __x, float* __iptr)
  { return __builtin_modff(__x, __iptr); }

  inline long double
  modf(long double __x, long double* __iptr)
  { return __builtin_modfl(__x, __iptr); }
#endif

  using ::pow;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  pow(float __x, float __y)
  { return __builtin_powf(__x, __y); }

  inline _GLIBCXX_CONSTEXPR long double
  pow(long double __x, long double __y)
  { return __builtin_powl(__x, __y); }

#if __cplusplus < 201103L
  // _GLIBCXX_RESOLVE_LIB_DEFECTS
  // DR 550. What should the return type of pow(float,int) be?
  inline double
  pow(double __x, int __i)
  { return __builtin_powi(__x, __i); }

  inline float
  pow(float __x, int __n)
  { return __builtin_powif(__x, __n); }

  inline long double
  pow(long double __x, int __n)
  { return __builtin_powil(__x, __n); }
#endif
#endif

  using ::sin;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  sin(float __x)
  { return __builtin_sinf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  sin(long double __x)
  { return __builtin_sinl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    sin(_Tp __x)
    { return __builtin_sin(__x); }

  using ::sinh;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  sinh(float __x)
  { return __builtin_sinhf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  sinh(long double __x)
  { return __builtin_sinhl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    sinh(_Tp __x)
    { return __builtin_sinh(__x); }

  using ::sqrt;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  sqrt(float __x)
  { return __builtin_sqrtf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  sqrt(long double __x)
  { return __builtin_sqrtl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    sqrt(_Tp __x)
    { return __builtin_sqrt(__x); }

  using ::tan;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  tan(float __x)
  { return __builtin_tanf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  tan(long double __x)
  { return __builtin_tanl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    tan(_Tp __x)
    { return __builtin_tan(__x); }

  using ::tanh;

#ifndef __CORRECT_ISO_CPP_MATH_H_PROTO
  inline _GLIBCXX_CONSTEXPR float
  tanh(float __x)
  { return __builtin_tanhf(__x); }

  inline _GLIBCXX_CONSTEXPR long double
  tanh(long double __x)
  { return __builtin_tanhl(__x); }
#endif

  template<typename _Tp>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    double>::__type
    tanh(_Tp __x)
    { return __builtin_tanh(__x); }

#if defined(__STDCPP_FLOAT16_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  constexpr _Float16
  acos(_Float16 __x)
  { return _Float16(__builtin_acosf(__x)); }

  constexpr _Float16
  asin(_Float16 __x)
  { return _Float16(__builtin_asinf(__x)); }

  constexpr _Float16
  atan(_Float16 __x)
  { return _Float16(__builtin_atanf(__x)); }

  constexpr _Float16
  atan2(_Float16 __y, _Float16 __x)
  { return _Float16(__builtin_atan2f(__y, __x)); }

  constexpr _Float16
  ceil(_Float16 __x)
  { return _Float16(__builtin_ceilf(__x)); }

  constexpr _Float16
  cos(_Float16 __x)
  { return _Float16(__builtin_cosf(__x)); }

  constexpr _Float16
  cosh(_Float16 __x)
  { return _Float16(__builtin_coshf(__x)); }

  constexpr _Float16
  exp(_Float16 __x)
  { return _Float16(__builtin_expf(__x)); }

  constexpr _Float16
  fabs(_Float16 __x)
  { return _Float16(__builtin_fabsf(__x)); }

  constexpr _Float16
  floor(_Float16 __x)
  { return _Float16(__builtin_floorf(__x)); }

  constexpr _Float16
  fmod(_Float16 __x, _Float16 __y)
  { return _Float16(__builtin_fmodf(__x, __y)); }

  inline _Float16
  frexp(_Float16 __x, int* __exp)
  { return _Float16(__builtin_frexpf(__x, __exp)); }

  constexpr _Float16
  ldexp(_Float16 __x, int __exp)
  { return _Float16(__builtin_ldexpf(__x, __exp)); }

  constexpr _Float16
  log(_Float16 __x)
  { return _Float16(__builtin_logf(__x)); }

  constexpr _Float16
  log10(_Float16 __x)
  { return _Float16(__builtin_log10f(__x)); }

  inline _Float16
  modf(_Float16 __x, _Float16* __iptr)
  {
    float __i, __ret = __builtin_modff(__x, &__i);
    *__iptr = _Float16(__i);
    return _Float16(__ret);
  }

  constexpr _Float16
  pow(_Float16 __x, _Float16 __y)
  { return _Float16(__builtin_powf(__x, __y)); }

  constexpr _Float16
  sin(_Float16 __x)
  { return _Float16(__builtin_sinf(__x)); }

  constexpr _Float16
  sinh(_Float16 __x)
  { return _Float16(__builtin_sinhf(__x)); }

  constexpr _Float16
  sqrt(_Float16 __x)
  { return _Float16(__builtin_sqrtf(__x)); }

  constexpr _Float16
  tan(_Float16 __x)
  { return _Float16(__builtin_tanf(__x)); }

  constexpr _Float16
  tanh(_Float16 __x)
  { return _Float16(__builtin_tanhf(__x)); }
#endif

#if defined(__STDCPP_FLOAT32_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  constexpr _Float32
  acos(_Float32 __x)
  { return __builtin_acosf(__x); }

  constexpr _Float32
  asin(_Float32 __x)
  { return __builtin_asinf(__x); }

  constexpr _Float32
  atan(_Float32 __x)
  { return __builtin_atanf(__x); }

  constexpr _Float32
  atan2(_Float32 __y, _Float32 __x)
  { return __builtin_atan2f(__y, __x); }

  constexpr _Float32
  ceil(_Float32 __x)
  { return __builtin_ceilf(__x); }

  constexpr _Float32
  cos(_Float32 __x)
  { return __builtin_cosf(__x); }

  constexpr _Float32
  cosh(_Float32 __x)
  { return __builtin_coshf(__x); }

  constexpr _Float32
  exp(_Float32 __x)
  { return __builtin_expf(__x); }

  constexpr _Float32
  fabs(_Float32 __x)
  { return __builtin_fabsf(__x); }

  constexpr _Float32
  floor(_Float32 __x)
  { return __builtin_floorf(__x); }

  constexpr _Float32
  fmod(_Float32 __x, _Float32 __y)
  { return __builtin_fmodf(__x, __y); }

  inline _Float32
  frexp(_Float32 __x, int* __exp)
  { return __builtin_frexpf(__x, __exp); }

  constexpr _Float32
  ldexp(_Float32 __x, int __exp)
  { return __builtin_ldexpf(__x, __exp); }

  constexpr _Float32
  log(_Float32 __x)
  { return __builtin_logf(__x); }

  constexpr _Float32
  log10(_Float32 __x)
  { return __builtin_log10f(__x); }

  inline _Float32
  modf(_Float32 __x, _Float32* __iptr)
  {
    float __i, __ret = __builtin_modff(__x, &__i);
    *__iptr = __i;
    return __ret;
  }

  constexpr _Float32
  pow(_Float32 __x, _Float32 __y)
  { return __builtin_powf(__x, __y); }

  constexpr _Float32
  sin(_Float32 __x)
  { return __builtin_sinf(__x); }

  constexpr _Float32
  sinh(_Float32 __x)
  { return __builtin_sinhf(__x); }

  constexpr _Float32
  sqrt(_Float32 __x)
  { return __builtin_sqrtf(__x); }

  constexpr _Float32
  tan(_Float32 __x)
  { return __builtin_tanf(__x); }

  constexpr _Float32
  tanh(_Float32 __x)
  { return __builtin_tanhf(__x); }
#endif

#if defined(__STDCPP_FLOAT64_T__) && defined(_GLIBCXX_DOUBLE_IS_IEEE_BINARY64)
  constexpr _Float64
  acos(_Float64 __x)
  { return __builtin_acos(__x); }

  constexpr _Float64
  asin(_Float64 __x)
  { return __builtin_asin(__x); }

  constexpr _Float64
  atan(_Float64 __x)
  { return __builtin_atan(__x); }

  constexpr _Float64
  atan2(_Float64 __y, _Float64 __x)
  { return __builtin_atan2(__y, __x); }

  constexpr _Float64
  ceil(_Float64 __x)
  { return __builtin_ceil(__x); }

  constexpr _Float64
  cos(_Float64 __x)
  { return __builtin_cos(__x); }

  constexpr _Float64
  cosh(_Float64 __x)
  { return __builtin_cosh(__x); }

  constexpr _Float64
  exp(_Float64 __x)
  { return __builtin_exp(__x); }

  constexpr _Float64
  fabs(_Float64 __x)
  { return __builtin_fabs(__x); }

  constexpr _Float64
  floor(_Float64 __x)
  { return __builtin_floor(__x); }

  constexpr _Float64
  fmod(_Float64 __x, _Float64 __y)
  { return __builtin_fmod(__x, __y); }

  inline _Float64
  frexp(_Float64 __x, int* __exp)
  { return __builtin_frexp(__x, __exp); }

  constexpr _Float64
  ldexp(_Float64 __x, int __exp)
  { return __builtin_ldexp(__x, __exp); }

  constexpr _Float64
  log(_Float64 __x)
  { return __builtin_log(__x); }

  constexpr _Float64
  log10(_Float64 __x)
  { return __builtin_log10(__x); }

  inline _Float64
  modf(_Float64 __x, _Float64* __iptr)
  {
    double __i, __ret = __builtin_modf(__x, &__i);
    *__iptr = __i;
    return __ret;
  }

  constexpr _Float64
  pow(_Float64 __x, _Float64 __y)
  { return __builtin_pow(__x, __y); }

  constexpr _Float64
  sin(_Float64 __x)
  { return __builtin_sin(__x); }

  constexpr _Float64
  sinh(_Float64 __x)
  { return __builtin_sinh(__x); }

  constexpr _Float64
  sqrt(_Float64 __x)
  { return __builtin_sqrt(__x); }

  constexpr _Float64
  tan(_Float64 __x)
  { return __builtin_tan(__x); }

  constexpr _Float64
  tanh(_Float64 __x)
  { return __builtin_tanh(__x); }
#endif

#if defined(__STDCPP_FLOAT128_T__) && defined(_GLIBCXX_LDOUBLE_IS_IEEE_BINARY128)
  constexpr _Float128
  acos(_Float128 __x)
  { return __builtin_acosl(__x); }

  constexpr _Float128
  asin(_Float128 __x)
  { return __builtin_asinl(__x); }

  constexpr _Float128
  atan(_Float128 __x)
  { return __builtin_atanl(__x); }

  constexpr _Float128
  atan2(_Float128 __y, _Float128 __x)
  { return __builtin_atan2l(__y, __x); }

  constexpr _Float128
  ceil(_Float128 __x)
  { return __builtin_ceill(__x); }

  constexpr _Float128
  cos(_Float128 __x)
  { return __builtin_cosl(__x); }

  constexpr _Float128
  cosh(_Float128 __x)
  { return __builtin_coshl(__x); }

  constexpr _Float128
  exp(_Float128 __x)
  { return __builtin_expl(__x); }

  constexpr _Float128
  fabs(_Float128 __x)
  { return __builtin_fabsl(__x); }

  constexpr _Float128
  floor(_Float128 __x)
  { return __builtin_floorl(__x); }

  constexpr _Float128
  fmod(_Float128 __x, _Float128 __y)
  { return __builtin_fmodl(__x, __y); }

  inline _Float128
  frexp(_Float128 __x, int* __exp)
  { return __builtin_frexpl(__x, __exp); }

  constexpr _Float128
  ldexp(_Float128 __x, int __exp)
  { return __builtin_ldexpl(__x, __exp); }

  constexpr _Float128
  log(_Float128 __x)
  { return __builtin_logl(__x); }

  constexpr _Float128
  log10(_Float128 __x)
  { return __builtin_log10l(__x); }

  inline _Float128
  modf(_Float128 __x, _Float128* __iptr)
  {
    long double __i, __ret = __builtin_modfl(__x, &__i);
    *__iptr = __i;
    return __ret;
  }

  constexpr _Float128
  pow(_Float128 __x, _Float128 __y)
  { return __builtin_powl(__x, __y); }

  constexpr _Float128
  sin(_Float128 __x)
  { return __builtin_sinl(__x); }

  constexpr _Float128
  sinh(_Float128 __x)
  { return __builtin_sinhl(__x); }

  constexpr _Float128
  sqrt(_Float128 __x)
  { return __builtin_sqrtl(__x); }

  constexpr _Float128
  tan(_Float128 __x)
  { return __builtin_tanl(__x); }

  constexpr _Float128
  tanh(_Float128 __x)
  { return __builtin_tanhl(__x); }
#elif defined(__STDCPP_FLOAT128_T__) && defined(_GLIBCXX_HAVE_FLOAT128_MATH)
  constexpr _Float128
  acos(_Float128 __x)
  { return __builtin_acosf128(__x); }

  constexpr _Float128
  asin(_Float128 __x)
  { return __builtin_asinf128(__x); }

  constexpr _Float128
  atan(_Float128 __x)
  { return __builtin_atanf128(__x); }

  constexpr _Float128
  atan2(_Float128 __y, _Float128 __x)
  { return __builtin_atan2f128(__y, __x); }

  constexpr _Float128
  ceil(_Float128 __x)
  { return __builtin_ceilf128(__x); }

  constexpr _Float128
  cos(_Float128 __x)
  { return __builtin_cosf128(__x); }

  constexpr _Float128
  cosh(_Float128 __x)
  { return __builtin_coshf128(__x); }

  constexpr _Float128
  exp(_Float128 __x)
  { return __builtin_expf128(__x); }

  constexpr _Float128
  fabs(_Float128 __x)
  { return __builtin_fabsf128(__x); }

  constexpr _Float128
  floor(_Float128 __x)
  { return __builtin_floorf128(__x); }

  constexpr _Float128
  fmod(_Float128 __x, _Float128 __y)
  { return __builtin_fmodf128(__x, __y); }

  inline _Float128
  frexp(_Float128 __x, int* __exp)
  { return __builtin_frexpf128(__x, __exp); }

  constexpr _Float128
  ldexp(_Float128 __x, int __exp)
  { return __builtin_ldexpf128(__x, __exp); }

  constexpr _Float128
  log(_Float128 __x)
  { return __builtin_logf128(__x); }

  constexpr _Float128
  log10(_Float128 __x)
  { return __builtin_log10f128(__x); }

  inline _Float128
  modf(_Float128 __x, _Float128* __iptr)
  { return __builtin_modff128(__x, __iptr); }

  constexpr _Float128
  pow(_Float128 __x, _Float128 __y)
  { return __builtin_powf128(__x, __y); }

  constexpr _Float128
  sin(_Float128 __x)
  { return __builtin_sinf128(__x); }

  constexpr _Float128
  sinh(_Float128 __x)
  { return __builtin_sinhf128(__x); }

  constexpr _Float128
  sqrt(_Float128 __x)
  { return __builtin_sqrtf128(__x); }

  constexpr _Float128
  tan(_Float128 __x)
  { return __builtin_tanf128(__x); }

  constexpr _Float128
  tanh(_Float128 __x)
  { return __builtin_tanhf128(__x); }
#endif

#if defined(__STDCPP_BFLOAT16_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  constexpr __gnu_cxx::__bfloat16_t
  acos(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_acosf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  asin(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_asinf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  atan(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_atanf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  atan2(__gnu_cxx::__bfloat16_t __y, __gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_atan2f(__y, __x)); }

  constexpr __gnu_cxx::__bfloat16_t
  ceil(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_ceilf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  cos(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_cosf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  cosh(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_coshf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  exp(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_expf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  fabs(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_fabsf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  floor(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_floorf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  fmod(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __gnu_cxx::__bfloat16_t(__builtin_fmodf(__x, __y)); }

  inline __gnu_cxx::__bfloat16_t
  frexp(__gnu_cxx::__bfloat16_t __x, int* __exp)
  { return __gnu_cxx::__bfloat16_t(__builtin_frexpf(__x, __exp)); }

  constexpr __gnu_cxx::__bfloat16_t
  ldexp(__gnu_cxx::__bfloat16_t __x, int __exp)
  { return __gnu_cxx::__bfloat16_t(__builtin_ldexpf(__x, __exp)); }

  constexpr __gnu_cxx::__bfloat16_t
  log(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_logf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  log10(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_log10f(__x)); }

  inline __gnu_cxx::__bfloat16_t
  modf(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t* __iptr)
  {
    float __i, __ret = __builtin_modff(__x, &__i);
    *__iptr = __gnu_cxx::__bfloat16_t(__i);
    return __gnu_cxx::__bfloat16_t(__ret);
  }

  constexpr __gnu_cxx::__bfloat16_t
  pow(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __gnu_cxx::__bfloat16_t(__builtin_powf(__x, __y)); }

  constexpr __gnu_cxx::__bfloat16_t
  sin(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_sinf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  sinh(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_sinhf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  sqrt(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_sqrtf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  tan(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_tanf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  tanh(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_tanhf(__x)); }
#endif

  template<typename _Tp, typename _Up>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    atan2(_Tp __y, _Up __x)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return atan2(__type(__y), __type(__x));
    }

  template<typename _Tp, typename _Up>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    fmod(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return fmod(__type(__x), __type(__y));
    }

  template<typename _Tp, typename _Up>
    inline _GLIBCXX_CONSTEXPR
    typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    pow(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return pow(__type(__x), __type(__y));
    }

#if _GLIBCXX_USE_C99_MATH
#if !_GLIBCXX_USE_C99_FP_MACROS_DYNAMIC

// These are possible macros imported from C99-land.
#undef fpclassify
#undef isfinite
#undef isinf
#undef isnan
#undef isnormal
#undef signbit
#undef isgreater
#undef isgreaterequal
#undef isless
#undef islessequal
#undef islessgreater
#undef isunordered

#if __cplusplus >= 201103L

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr int
  fpclassify(float __x)
  { return __builtin_fpclassify(FP_NAN, FP_INFINITE, FP_NORMAL,
				FP_SUBNORMAL, FP_ZERO, __x); }

  constexpr int
  fpclassify(double __x)
  { return __builtin_fpclassify(FP_NAN, FP_INFINITE, FP_NORMAL,
				FP_SUBNORMAL, FP_ZERO, __x); }

  constexpr int
  fpclassify(long double __x)
  { return __builtin_fpclassify(FP_NAN, FP_INFINITE, FP_NORMAL,
				FP_SUBNORMAL, FP_ZERO, __x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                              int>::__type
    fpclassify(_Tp __x)
    { return __x != 0 ? FP_NORMAL : FP_ZERO; }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  isfinite(float __x)
  { return __builtin_isfinite(__x); }

  constexpr bool
  isfinite(double __x)
  { return __builtin_isfinite(__x); }

  constexpr bool
  isfinite(long double __x)
  { return __builtin_isfinite(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                              bool>::__type
    isfinite(_Tp)
    { return true; }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  isinf(float __x)
  { return __builtin_isinf(__x); }

#if _GLIBCXX_HAVE_OBSOLETE_ISINF \
  && !_GLIBCXX_NO_OBSOLETE_ISINF_ISNAN_DYNAMIC
  using ::isinf;
#else
  constexpr bool
  isinf(double __x)
  { return __builtin_isinf(__x); }
#endif

  constexpr bool
  isinf(long double __x)
  { return __builtin_isinf(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                              bool>::__type
    isinf(_Tp)
    { return false; }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  isnan(float __x)
  { return __builtin_isnan(__x); }

#if _GLIBCXX_HAVE_OBSOLETE_ISNAN \
  && !_GLIBCXX_NO_OBSOLETE_ISINF_ISNAN_DYNAMIC
  using ::isnan;
#else
  constexpr bool
  isnan(double __x)
  { return __builtin_isnan(__x); }
#endif

  constexpr bool
  isnan(long double __x)
  { return __builtin_isnan(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                              bool>::__type
    isnan(_Tp)
    { return false; }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  isnormal(float __x)
  { return __builtin_isnormal(__x); }

  constexpr bool
  isnormal(double __x)
  { return __builtin_isnormal(__x); }

  constexpr bool
  isnormal(long double __x)
  { return __builtin_isnormal(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                              bool>::__type
    isnormal(_Tp __x)
    { return __x != 0 ? true : false; }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  // Note: middle-end/36757 is fixed, __builtin_signbit is type-generic.
  constexpr bool
  signbit(float __x)
  { return __builtin_signbit(__x); }

  constexpr bool
  signbit(double __x)
  { return __builtin_signbit(__x); }

  constexpr bool
  signbit(long double __x)
  { return __builtin_signbit(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                              bool>::__type
    signbit(_Tp __x)
    { return __x < 0 ? true : false; }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  isgreater(float __x, float __y)
  { return __builtin_isgreater(__x, __y); }

  constexpr bool
  isgreater(double __x, double __y)
  { return __builtin_isgreater(__x, __y); }

  constexpr bool
  isgreater(long double __x, long double __y)
  { return __builtin_isgreater(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp, typename _Up>
    constexpr typename
    __gnu_cxx::__enable_if<(__is_arithmetic<_Tp>::__value
			    && __is_arithmetic<_Up>::__value), bool>::__type
    isgreater(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return __builtin_isgreater(__type(__x), __type(__y));
    }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  isgreaterequal(float __x, float __y)
  { return __builtin_isgreaterequal(__x, __y); }

  constexpr bool
  isgreaterequal(double __x, double __y)
  { return __builtin_isgreaterequal(__x, __y); }

  constexpr bool
  isgreaterequal(long double __x, long double __y)
  { return __builtin_isgreaterequal(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp, typename _Up>
    constexpr typename
    __gnu_cxx::__enable_if<(__is_arithmetic<_Tp>::__value
			    && __is_arithmetic<_Up>::__value), bool>::__type
    isgreaterequal(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return __builtin_isgreaterequal(__type(__x), __type(__y));
    }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  isless(float __x, float __y)
  { return __builtin_isless(__x, __y); }

  constexpr bool
  isless(double __x, double __y)
  { return __builtin_isless(__x, __y); }

  constexpr bool
  isless(long double __x, long double __y)
  { return __builtin_isless(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp, typename _Up>
    constexpr typename
    __gnu_cxx::__enable_if<(__is_arithmetic<_Tp>::__value
			    && __is_arithmetic<_Up>::__value), bool>::__type
    isless(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return __builtin_isless(__type(__x), __type(__y));
    }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  islessequal(float __x, float __y)
  { return __builtin_islessequal(__x, __y); }

  constexpr bool
  islessequal(double __x, double __y)
  { return __builtin_islessequal(__x, __y); }

  constexpr bool
  islessequal(long double __x, long double __y)
  { return __builtin_islessequal(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp, typename _Up>
    constexpr typename
    __gnu_cxx::__enable_if<(__is_arithmetic<_Tp>::__value
			    && __is_arithmetic<_Up>::__value), bool>::__type
    islessequal(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return __builtin_islessequal(__type(__x), __type(__y));
    }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  islessgreater(float __x, float __y)
  { return __builtin_islessgreater(__x, __y); }

  constexpr bool
  islessgreater(double __x, double __y)
  { return __builtin_islessgreater(__x, __y); }

  constexpr bool
  islessgreater(long double __x, long double __y)
  { return __builtin_islessgreater(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp, typename _Up>
    constexpr typename
    __gnu_cxx::__enable_if<(__is_arithmetic<_Tp>::__value
			    && __is_arithmetic<_Up>::__value), bool>::__type
    islessgreater(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return __builtin_islessgreater(__type(__x), __type(__y));
    }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr bool
  isunordered(float __x, float __y)
  { return __builtin_isunordered(__x, __y); }

  constexpr bool
  isunordered(double __x, double __y)
  { return __builtin_isunordered(__x, __y); }

  constexpr bool
  isunordered(long double __x, long double __y)
  { return __builtin_isunordered(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp, typename _Up>
    constexpr typename
    __gnu_cxx::__enable_if<(__is_arithmetic<_Tp>::__value
			    && __is_arithmetic<_Up>::__value), bool>::__type
    isunordered(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return __builtin_isunordered(__type(__x), __type(__y));
    }
#endif

#else

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    fpclassify(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_fpclassify(FP_NAN, FP_INFINITE, FP_NORMAL,
				  FP_SUBNORMAL, FP_ZERO, __type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    isfinite(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isfinite(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    isinf(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isinf(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    isnan(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isnan(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    isnormal(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isnormal(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    signbit(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_signbit(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    isgreater(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isgreater(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    isgreaterequal(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isgreaterequal(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    isless(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isless(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    islessequal(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_islessequal(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    islessgreater(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_islessgreater(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
					   int>::__type
    isunordered(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isunordered(__type(__f1), __type(__f2));
    }

#endif // C++11

#ifdef __STDCPP_FLOAT16_T__
  constexpr int
  fpclassify(_Float16 __x)
  { return __builtin_fpclassify(FP_NAN, FP_INFINITE, FP_NORMAL,
				FP_SUBNORMAL, FP_ZERO, __x); }

  constexpr bool
  isfinite(_Float16 __x)
  { return __builtin_isfinite(__x); }

  constexpr bool
  isinf(_Float16 __x)
  { return __builtin_isinf(__x); }

  constexpr bool
  isnan(_Float16 __x)
  { return __builtin_isnan(__x); }

  constexpr bool
  isnormal(_Float16 __x)
  { return __builtin_isnormal(__x); }

  constexpr bool
  signbit(_Float16 __x)
  { return __builtin_signbit(__x); }

  constexpr bool
  isgreater(_Float16 __x, _Float16 __y)
  { return __builtin_isgreater(__x, __y); }

  constexpr bool
  isgreaterequal(_Float16 __x, _Float16 __y)
  { return __builtin_isgreaterequal(__x, __y); }

  constexpr bool
  isless(_Float16 __x, _Float16 __y)
  { return __builtin_isless(__x, __y); }

  constexpr bool
  islessequal(_Float16 __x, _Float16 __y)
  { return __builtin_islessequal(__x, __y); }

  constexpr bool
  islessgreater(_Float16 __x, _Float16 __y)
  { return __builtin_islessgreater(__x, __y); }

  constexpr bool
  isunordered(_Float16 __x, _Float16 __y)
  { return __builtin_isunordered(__x, __y); }
#endif

#ifdef __STDCPP_FLOAT32_T__
  constexpr int
  fpclassify(_Float32 __x)
  { return __builtin_fpclassify(FP_NAN, FP_INFINITE, FP_NORMAL,
				FP_SUBNORMAL, FP_ZERO, __x); }

  constexpr bool
  isfinite(_Float32 __x)
  { return __builtin_isfinite(__x); }

  constexpr bool
  isinf(_Float32 __x)
  { return __builtin_isinf(__x); }

  constexpr bool
  isnan(_Float32 __x)
  { return __builtin_isnan(__x); }

  constexpr bool
  isnormal(_Float32 __x)
  { return __builtin_isnormal(__x); }

  constexpr bool
  signbit(_Float32 __x)
  { return __builtin_signbit(__x); }

  constexpr bool
  isgreater(_Float32 __x, _Float32 __y)
  { return __builtin_isgreater(__x, __y); }

  constexpr bool
  isgreaterequal(_Float32 __x, _Float32 __y)
  { return __builtin_isgreaterequal(__x, __y); }

  constexpr bool
  isless(_Float32 __x, _Float32 __y)
  { return __builtin_isless(__x, __y); }

  constexpr bool
  islessequal(_Float32 __x, _Float32 __y)
  { return __builtin_islessequal(__x, __y); }

  constexpr bool
  islessgreater(_Float32 __x, _Float32 __y)
  { return __builtin_islessgreater(__x, __y); }

  constexpr bool
  isunordered(_Float32 __x, _Float32 __y)
  { return __builtin_isunordered(__x, __y); }
#endif

#ifdef __STDCPP_FLOAT64_T__
  constexpr int
  fpclassify(_Float64 __x)
  { return __builtin_fpclassify(FP_NAN, FP_INFINITE, FP_NORMAL,
				FP_SUBNORMAL, FP_ZERO, __x); }

  constexpr bool
  isfinite(_Float64 __x)
  { return __builtin_isfinite(__x); }

  constexpr bool
  isinf(_Float64 __x)
  { return __builtin_isinf(__x); }

  constexpr bool
  isnan(_Float64 __x)
  { return __builtin_isnan(__x); }

  constexpr bool
  isnormal(_Float64 __x)
  { return __builtin_isnormal(__x); }

  constexpr bool
  signbit(_Float64 __x)
  { return __builtin_signbit(__x); }

  constexpr bool
  isgreater(_Float64 __x, _Float64 __y)
  { return __builtin_isgreater(__x, __y); }

  constexpr bool
  isgreaterequal(_Float64 __x, _Float64 __y)
  { return __builtin_isgreaterequal(__x, __y); }

  constexpr bool
  isless(_Float64 __x, _Float64 __y)
  { return __builtin_isless(__x, __y); }

  constexpr bool
  islessequal(_Float64 __x, _Float64 __y)
  { return __builtin_islessequal(__x, __y); }

  constexpr bool
  islessgreater(_Float64 __x, _Float64 __y)
  { return __builtin_islessgreater(__x, __y); }

  constexpr bool
  isunordered(_Float64 __x, _Float64 __y)
  { return __builtin_isunordered(__x, __y); }
#endif

#ifdef __STDCPP_FLOAT128_T__
  constexpr int
  fpclassify(_Float128 __x)
  { return __builtin_fpclassify(FP_NAN, FP_INFINITE, FP_NORMAL,
				FP_SUBNORMAL, FP_ZERO, __x); }

  constexpr bool
  isfinite(_Float128 __x)
  { return __builtin_isfinite(__x); }

  constexpr bool
  isinf(_Float128 __x)
  { return __builtin_isinf(__x); }

  constexpr bool
  isnan(_Float128 __x)
  { return __builtin_isnan(__x); }

  constexpr bool
  isnormal(_Float128 __x)
  { return __builtin_isnormal(__x); }

  constexpr bool
  signbit(_Float128 __x)
  { return __builtin_signbit(__x); }

  constexpr bool
  isgreater(_Float128 __x, _Float128 __y)
  { return __builtin_isgreater(__x, __y); }

  constexpr bool
  isgreaterequal(_Float128 __x, _Float128 __y)
  { return __builtin_isgreaterequal(__x, __y); }

  constexpr bool
  isless(_Float128 __x, _Float128 __y)
  { return __builtin_isless(__x, __y); }

  constexpr bool
  islessequal(_Float128 __x, _Float128 __y)
  { return __builtin_islessequal(__x, __y); }

  constexpr bool
  islessgreater(_Float128 __x, _Float128 __y)
  { return __builtin_islessgreater(__x, __y); }

  constexpr bool
  isunordered(_Float128 __x, _Float128 __y)
  { return __builtin_isunordered(__x, __y); }
#endif

#ifdef __STDCPP_BFLOAT16_T__
  constexpr int
  fpclassify(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_fpclassify(FP_NAN, FP_INFINITE, FP_NORMAL,
				FP_SUBNORMAL, FP_ZERO, __x); }

  constexpr bool
  isfinite(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_isfinite(__x); }

  constexpr bool
  isinf(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_isinf(__x); }

  constexpr bool
  isnan(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_isnan(__x); }

  constexpr bool
  isnormal(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_isnormal(__x); }

  constexpr bool
  signbit(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_signbit(__x); }

  constexpr bool
  isgreater(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __builtin_isgreater(__x, __y); }

  constexpr bool
  isgreaterequal(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __builtin_isgreaterequal(__x, __y); }

  constexpr bool
  isless(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __builtin_isless(__x, __y); }

  constexpr bool
  islessequal(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __builtin_islessequal(__x, __y); }

  constexpr bool
  islessgreater(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __builtin_islessgreater(__x, __y); }

  constexpr bool
  isunordered(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __builtin_isunordered(__x, __y); }
#endif

#endif /* _GLIBCXX_USE_C99_FP_MACROS_DYNAMIC */
#endif /* _GLIBCXX_USE_C99_MATH */

#if __cplusplus >= 201103L

#undef acosf
#undef acosl
#undef asinf
#undef asinl
#undef atanf
#undef atanl
#undef atan2f
#undef atan2l
#undef ceilf
#undef ceill
#undef cosf
#undef cosl
#undef coshf
#undef coshl
#undef expf
#undef expl
#undef fabsf
#undef fabsl
#undef floorf
#undef floorl
#undef fmodf
#undef fmodl
#undef frexpf
#undef frexpl
#undef ldexpf
#undef ldexpl
#undef logf
#undef logl
#undef log10f
#undef log10l
#undef modff
#undef modfl
#undef powf
#undef powl
#undef sinf
#undef sinl
#undef sinhf
#undef sinhl
#undef sqrtf
#undef sqrtl
#undef tanf
#undef tanl
#undef tanhf
#undef tanhl

#ifdef _GLIBCXX_HAVE_ACOSF
  using ::acosf;
#endif
#ifdef _GLIBCXX_HAVE_ACOSL
  using ::acosl;
#endif

#ifdef _GLIBCXX_HAVE_ASINF
  using ::asinf;
#endif
#ifdef _GLIBCXX_HAVE_ASINL
  using ::asinl;
#endif

#ifdef _GLIBCXX_HAVE_ATANF
  using ::atanf;
#endif
#ifdef _GLIBCXX_HAVE_ATANL
  using ::atanl;
#endif

#ifdef _GLIBCXX_HAVE_ATAN2F
  using ::atan2f;
#endif
#ifdef _GLIBCXX_HAVE_ATAN2L
  using ::atan2l;
#endif

#ifdef _GLIBCXX_HAVE_CEILF
  using ::ceilf;
#endif
#ifdef _GLIBCXX_HAVE_CEILL
  using ::ceill;
#endif

#ifdef _GLIBCXX_HAVE_COSF
  using ::cosf;
#endif
#ifdef _GLIBCXX_HAVE_COSL
  using ::cosl;
#endif

#ifdef _GLIBCXX_HAVE_COSHF
  using ::coshf;
#endif
#ifdef _GLIBCXX_HAVE_COSHL
  using ::coshl;
#endif

#ifdef _GLIBCXX_HAVE_EXPF
  using ::expf;
#endif
#ifdef _GLIBCXX_HAVE_EXPL
  using ::expl;
#endif

#ifdef _GLIBCXX_HAVE_FABSF
  using ::fabsf;
#endif
#ifdef _GLIBCXX_HAVE_FABSL
  using ::fabsl;
#endif

#ifdef _GLIBCXX_HAVE_FLOORF
  using ::floorf;
#endif
#ifdef _GLIBCXX_HAVE_FLOORL
  using ::floorl;
#endif

#ifdef _GLIBCXX_HAVE_FMODF
  using ::fmodf;
#endif
#ifdef _GLIBCXX_HAVE_FMODL
  using ::fmodl;
#endif

#ifdef _GLIBCXX_HAVE_FREXPF
  using ::frexpf;
#endif
#ifdef _GLIBCXX_HAVE_FREXPL
  using ::frexpl;
#endif

#ifdef _GLIBCXX_HAVE_LDEXPF
  using ::ldexpf;
#endif
#ifdef _GLIBCXX_HAVE_LDEXPL
  using ::ldexpl;
#endif

#ifdef _GLIBCXX_HAVE_LOGF
  using ::logf;
#endif
#ifdef _GLIBCXX_HAVE_LOGL
  using ::logl;
#endif

#ifdef _GLIBCXX_HAVE_LOG10F
  using ::log10f;
#endif
#ifdef _GLIBCXX_HAVE_LOG10L
  using ::log10l;
#endif

#ifdef _GLIBCXX_HAVE_MODFF
  using ::modff;
#endif
#ifdef _GLIBCXX_HAVE_MODFL
  using ::modfl;
#endif

#ifdef _GLIBCXX_HAVE_POWF
  using ::powf;
#endif
#ifdef _GLIBCXX_HAVE_POWL
  using ::powl;
#endif

#ifdef _GLIBCXX_HAVE_SINF
  using ::sinf;
#endif
#ifdef _GLIBCXX_HAVE_SINL
  using ::sinl;
#endif

#ifdef _GLIBCXX_HAVE_SINHF
  using ::sinhf;
#endif
#ifdef _GLIBCXX_HAVE_SINHL
  using ::sinhl;
#endif

#ifdef _GLIBCXX_HAVE_SQRTF
  using ::sqrtf;
#endif
#ifdef _GLIBCXX_HAVE_SQRTL
  using ::sqrtl;
#endif

#ifdef _GLIBCXX_HAVE_TANF
  using ::tanf;
#endif
#ifdef _GLIBCXX_HAVE_TANL
  using ::tanl;
#endif

#ifdef _GLIBCXX_HAVE_TANHF
  using ::tanhf;
#endif
#ifdef _GLIBCXX_HAVE_TANHL
  using ::tanhl;
#endif

#ifdef _GLIBCXX_USE_C99_MATH_FUNCS

#undef acosh
#undef acoshf
#undef acoshl
#undef asinh
#undef asinhf
#undef asinhl
#undef atanh
#undef atanhf
#undef atanhl
#undef cbrt
#undef cbrtf
#undef cbrtl
#undef copysign
#undef copysignf
#undef copysignl
#undef erf
#undef erff
#undef erfl
#undef erfc
#undef erfcf
#undef erfcl
#undef exp2
#undef exp2f
#undef exp2l
#undef expm1
#undef expm1f
#undef expm1l
#undef fdim
#undef fdimf
#undef fdiml
#undef fma
#undef fmaf
#undef fmal
#undef fmax
#undef fmaxf
#undef fmaxl
#undef fmin
#undef fminf
#undef fminl
#undef hypot
#undef hypotf
#undef hypotl
#undef ilogb
#undef ilogbf
#undef ilogbl
#undef lgamma
#undef lgammaf
#undef lgammal
#ifndef _GLIBCXX_NO_C99_ROUNDING_FUNCS
#undef llrint
#undef llrintf
#undef llrintl
#undef llround
#undef llroundf
#undef llroundl
#endif
#undef log1p
#undef log1pf
#undef log1pl
#undef log2
#undef log2f
#undef log2l
#undef logb
#undef logbf
#undef logbl
#undef lrint
#undef lrintf
#undef lrintl
#undef lround
#undef lroundf
#undef lroundl
#undef nan
#undef nanf
#undef nanl
#undef nearbyint
#undef nearbyintf
#undef nearbyintl
#undef nextafter
#undef nextafterf
#undef nextafterl
#undef nexttoward
#undef nexttowardf
#undef nexttowardl
#undef remainder
#undef remainderf
#undef remainderl
#undef remquo
#undef remquof
#undef remquol
#undef rint
#undef rintf
#undef rintl
#undef round
#undef roundf
#undef roundl
#undef scalbln
#undef scalblnf
#undef scalblnl
#undef scalbn
#undef scalbnf
#undef scalbnl
#undef tgamma
#undef tgammaf
#undef tgammal
#undef trunc
#undef truncf
#undef truncl

#ifdef _GLIBCXX_HAVE_C99_FLT_EVAL_TYPES
  // types
  using ::double_t;
  using ::float_t;
#endif

  // functions
  using ::acosh;
  using ::acoshf;
  using ::acoshl;

  using ::asinh;
  using ::asinhf;
  using ::asinhl;

  using ::atanh;
  using ::atanhf;
  using ::atanhl;

  using ::cbrt;
  using ::cbrtf;
  using ::cbrtl;

  using ::copysign;
  using ::copysignf;
  using ::copysignl;

  using ::erf;
  using ::erff;
  using ::erfl;

  using ::erfc;
  using ::erfcf;
  using ::erfcl;

  using ::exp2;
  using ::exp2f;
  using ::exp2l;

  using ::expm1;
  using ::expm1f;
  using ::expm1l;

  using ::fdim;
  using ::fdimf;
  using ::fdiml;

  using ::fma;
  using ::fmaf;
  using ::fmal;

  using ::fmax;
  using ::fmaxf;
  using ::fmaxl;

  using ::fmin;
  using ::fminf;
  using ::fminl;

  using ::hypot;
  using ::hypotf;
  using ::hypotl;

  using ::ilogb;
  using ::ilogbf;
  using ::ilogbl;

  using ::lgamma;
  using ::lgammaf;
  using ::lgammal;

#ifndef _GLIBCXX_NO_C99_ROUNDING_FUNCS
  using ::llrint;
  using ::llrintf;
  using ::llrintl;

  using ::llround;
  using ::llroundf;
  using ::llroundl;
#endif

  using ::log1p;
  using ::log1pf;
  using ::log1pl;

  using ::log2;
  using ::log2f;
  using ::log2l;

  using ::logb;
  using ::logbf;
  using ::logbl;

  using ::lrint;
  using ::lrintf;
  using ::lrintl;

  using ::lround;
  using ::lroundf;
  using ::lroundl;

  using ::nan;
  using ::nanf;
  using ::nanl;

  using ::nearbyint;
  using ::nearbyintf;
  using ::nearbyintl;

  using ::nextafter;
  using ::nextafterf;
  using ::nextafterl;

  using ::nexttoward;
  using ::nexttowardf;
  using ::nexttowardl;

  using ::remainder;
  using ::remainderf;
  using ::remainderl;

  using ::remquo;
  using ::remquof;
  using ::remquol;

  using ::rint;
  using ::rintf;
  using ::rintl;

  using ::round;
  using ::roundf;
  using ::roundl;

  using ::scalbln;
  using ::scalblnf;
  using ::scalblnl;

  using ::scalbn;
  using ::scalbnf;
  using ::scalbnl;

  using ::tgamma;
  using ::tgammaf;
  using ::tgammal;

  using ::trunc;
  using ::truncf;
  using ::truncl;

  /// Additional overloads.
#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  acosh(float __x)
  { return __builtin_acoshf(__x); }

  constexpr long double
  acosh(long double __x)
  { return __builtin_acoshl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    acosh(_Tp __x)
    { return __builtin_acosh(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  asinh(float __x)
  { return __builtin_asinhf(__x); }

  constexpr long double
  asinh(long double __x)
  { return __builtin_asinhl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    asinh(_Tp __x)
    { return __builtin_asinh(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  atanh(float __x)
  { return __builtin_atanhf(__x); }

  constexpr long double
  atanh(long double __x)
  { return __builtin_atanhl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    atanh(_Tp __x)
    { return __builtin_atanh(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  cbrt(float __x)
  { return __builtin_cbrtf(__x); }

  constexpr long double
  cbrt(long double __x)
  { return __builtin_cbrtl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    cbrt(_Tp __x)
    { return __builtin_cbrt(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  copysign(float __x, float __y)
  { return __builtin_copysignf(__x, __y); }

  constexpr long double
  copysign(long double __x, long double __y)
  { return __builtin_copysignl(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  erf(float __x)
  { return __builtin_erff(__x); }

  constexpr long double
  erf(long double __x)
  { return __builtin_erfl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    erf(_Tp __x)
    { return __builtin_erf(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  erfc(float __x)
  { return __builtin_erfcf(__x); }

  constexpr long double
  erfc(long double __x)
  { return __builtin_erfcl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    erfc(_Tp __x)
    { return __builtin_erfc(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  exp2(float __x)
  { return __builtin_exp2f(__x); }

  constexpr long double
  exp2(long double __x)
  { return __builtin_exp2l(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    exp2(_Tp __x)
    { return __builtin_exp2(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  expm1(float __x)
  { return __builtin_expm1f(__x); }

  constexpr long double
  expm1(long double __x)
  { return __builtin_expm1l(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    expm1(_Tp __x)
    { return __builtin_expm1(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  fdim(float __x, float __y)
  { return __builtin_fdimf(__x, __y); }

  constexpr long double
  fdim(long double __x, long double __y)
  { return __builtin_fdiml(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  fma(float __x, float __y, float __z)
  { return __builtin_fmaf(__x, __y, __z); }

  constexpr long double
  fma(long double __x, long double __y, long double __z)
  { return __builtin_fmal(__x, __y, __z); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  fmax(float __x, float __y)
  { return __builtin_fmaxf(__x, __y); }

  constexpr long double
  fmax(long double __x, long double __y)
  { return __builtin_fmaxl(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  fmin(float __x, float __y)
  { return __builtin_fminf(__x, __y); }

  constexpr long double
  fmin(long double __x, long double __y)
  { return __builtin_fminl(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  hypot(float __x, float __y)
  { return __builtin_hypotf(__x, __y); }

  constexpr long double
  hypot(long double __x, long double __y)
  { return __builtin_hypotl(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr int
  ilogb(float __x)
  { return __builtin_ilogbf(__x); }

  constexpr int
  ilogb(long double __x)
  { return __builtin_ilogbl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                    int>::__type
    ilogb(_Tp __x)
    { return __builtin_ilogb(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  lgamma(float __x)
  { return __builtin_lgammaf(__x); }

  constexpr long double
  lgamma(long double __x)
  { return __builtin_lgammal(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    lgamma(_Tp __x)
    { return __builtin_lgamma(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr long long
  llrint(float __x)
  { return __builtin_llrintf(__x); }

  constexpr long long
  llrint(long double __x)
  { return __builtin_llrintl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              long long>::__type
    llrint(_Tp __x)
    { return __builtin_llrint(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr long long
  llround(float __x)
  { return __builtin_llroundf(__x); }

  constexpr long long
  llround(long double __x)
  { return __builtin_llroundl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              long long>::__type
    llround(_Tp __x)
    { return __builtin_llround(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  log1p(float __x)
  { return __builtin_log1pf(__x); }

  constexpr long double
  log1p(long double __x)
  { return __builtin_log1pl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    log1p(_Tp __x)
    { return __builtin_log1p(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  // DR 568.
  constexpr float
  log2(float __x)
  { return __builtin_log2f(__x); }

  constexpr long double
  log2(long double __x)
  { return __builtin_log2l(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    log2(_Tp __x)
    { return __builtin_log2(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  logb(float __x)
  { return __builtin_logbf(__x); }

  constexpr long double
  logb(long double __x)
  { return __builtin_logbl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    logb(_Tp __x)
    { return __builtin_logb(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr long
  lrint(float __x)
  { return __builtin_lrintf(__x); }

  constexpr long
  lrint(long double __x)
  { return __builtin_lrintl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              long>::__type
    lrint(_Tp __x)
    { return __builtin_lrint(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr long
  lround(float __x)
  { return __builtin_lroundf(__x); }

  constexpr long
  lround(long double __x)
  { return __builtin_lroundl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              long>::__type
    lround(_Tp __x)
    { return __builtin_lround(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  nearbyint(float __x)
  { return __builtin_nearbyintf(__x); }

  constexpr long double
  nearbyint(long double __x)
  { return __builtin_nearbyintl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    nearbyint(_Tp __x)
    { return __builtin_nearbyint(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  nextafter(float __x, float __y)
  { return __builtin_nextafterf(__x, __y); }

  constexpr long double
  nextafter(long double __x, long double __y)
  { return __builtin_nextafterl(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  nexttoward(float __x, long double __y)
  { return __builtin_nexttowardf(__x, __y); }

  constexpr long double
  nexttoward(long double __x, long double __y)
  { return __builtin_nexttowardl(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    nexttoward(_Tp __x, long double __y)
    { return __builtin_nexttoward(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  remainder(float __x, float __y)
  { return __builtin_remainderf(__x, __y); }

  constexpr long double
  remainder(long double __x, long double __y)
  { return __builtin_remainderl(__x, __y); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  inline float
  remquo(float __x, float __y, int* __pquo)
  { return __builtin_remquof(__x, __y, __pquo); }

  inline long double
  remquo(long double __x, long double __y, int* __pquo)
  { return __builtin_remquol(__x, __y, __pquo); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  rint(float __x)
  { return __builtin_rintf(__x); }

  constexpr long double
  rint(long double __x)
  { return __builtin_rintl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    rint(_Tp __x)
    { return __builtin_rint(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  round(float __x)
  { return __builtin_roundf(__x); }

  constexpr long double
  round(long double __x)
  { return __builtin_roundl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    round(_Tp __x)
    { return __builtin_round(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  scalbln(float __x, long __ex)
  { return __builtin_scalblnf(__x, __ex); }

  constexpr long double
  scalbln(long double __x, long __ex)
  { return __builtin_scalblnl(__x, __ex); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    scalbln(_Tp __x, long __ex)
    { return __builtin_scalbln(__x, __ex); }
#endif
 
#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  scalbn(float __x, int __ex)
  { return __builtin_scalbnf(__x, __ex); }

  constexpr long double
  scalbn(long double __x, int __ex)
  { return __builtin_scalbnl(__x, __ex); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    scalbn(_Tp __x, int __ex)
    { return __builtin_scalbn(__x, __ex); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  tgamma(float __x)
  { return __builtin_tgammaf(__x); }

  constexpr long double
  tgamma(long double __x)
  { return __builtin_tgammal(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    tgamma(_Tp __x)
    { return __builtin_tgamma(__x); }
#endif
 
#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_FP
  constexpr float
  trunc(float __x)
  { return __builtin_truncf(__x); }

  constexpr long double
  trunc(long double __x)
  { return __builtin_truncl(__x); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp>
    constexpr typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value, 
                                              double>::__type
    trunc(_Tp __x)
    { return __builtin_trunc(__x); }
#endif

#if defined(__STDCPP_FLOAT16_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  constexpr _Float16
  acosh(_Float16 __x)
  { return _Float16(__builtin_acoshf(__x)); }

  constexpr _Float16
  asinh(_Float16 __x)
  { return _Float16(__builtin_asinhf(__x)); }

  constexpr _Float16
  atanh(_Float16 __x)
  { return _Float16(__builtin_atanhf(__x)); }

  constexpr _Float16
  cbrt(_Float16 __x)
  { return _Float16(__builtin_cbrtf(__x)); }

  constexpr _Float16
  copysign(_Float16 __x, _Float16 __y)
  { return __builtin_copysignf16(__x, __y); }

  constexpr _Float16
  erf(_Float16 __x)
  { return _Float16(__builtin_erff(__x)); }

  constexpr _Float16
  erfc(_Float16 __x)
  { return _Float16(__builtin_erfcf(__x)); }

  constexpr _Float16
  exp2(_Float16 __x)
  { return _Float16(__builtin_exp2f(__x)); }

  constexpr _Float16
  expm1(_Float16 __x)
  { return _Float16(__builtin_expm1f(__x)); }

  constexpr _Float16
  fdim(_Float16 __x, _Float16 __y)
  { return _Float16(__builtin_fdimf(__x, __y)); }

  constexpr _Float16
  fma(_Float16 __x, _Float16 __y, _Float16 __z)
  { return _Float16(__builtin_fmaf(__x, __y, __z)); }

  constexpr _Float16
  fmax(_Float16 __x, _Float16 __y)
  { return _Float16(__builtin_fmaxf(__x, __y)); }

  constexpr _Float16
  fmin(_Float16 __x, _Float16 __y)
  { return _Float16(__builtin_fminf(__x, __y)); }

  constexpr _Float16
  hypot(_Float16 __x, _Float16 __y)
  { return _Float16(__builtin_hypotf(__x, __y)); }

  constexpr int
  ilogb(_Float16 __x)
  { return __builtin_ilogbf(__x); }

  constexpr _Float16
  lgamma(_Float16 __x)
  { return _Float16(__builtin_lgammaf(__x)); }

  constexpr long long
  llrint(_Float16 __x)
  { return __builtin_llrintf(__x); }

  constexpr long long
  llround(_Float16 __x)
  { return __builtin_llroundf(__x); }

  constexpr _Float16
  log1p(_Float16 __x)
  { return _Float16(__builtin_log1pf(__x)); }

  // DR 568.
  constexpr _Float16
  log2(_Float16 __x)
  { return _Float16(__builtin_log2f(__x)); }

  constexpr _Float16
  logb(_Float16 __x)
  { return _Float16(__builtin_logbf(__x)); }

  constexpr long
  lrint(_Float16 __x)
  { return __builtin_lrintf(__x); }

  constexpr long
  lround(_Float16 __x)
  { return __builtin_lroundf(__x); }

  constexpr _Float16
  nearbyint(_Float16 __x)
  { return _Float16(__builtin_nearbyintf(__x)); }

  constexpr _Float16
  nextafter(_Float16 __x, _Float16 __y)
  {
#if __cpp_if_consteval >= 202106L
    // Can't use if (std::__is_constant_evaluated()) here, as it
    // doesn't guarantee optimizing the body away at -O0 and
    // nothing defines nextafterf16.
    if consteval { return __builtin_nextafterf16(__x, __y); }
#endif
#ifdef __INT16_TYPE__
    using __float16_int_type = __INT16_TYPE__;
#else
    using __float16_int_type = short int;
#endif
    __float16_int_type __hx, __hy, __ix, __iy;
    __builtin_memcpy(&__hx, &__x, sizeof(__x));
    __builtin_memcpy(&__hy, &__y, sizeof(__x));
    __ix = __hx & 0x7fff;	// |x|
    __iy = __hy & 0x7fff;	// |y|
    if (__ix > 0x7c00 || __iy > 0x7c00) // x or y is NaN
      return __x + __y;
    if (__x == __y)
      return __y;		// x == y, return y
    if (__ix == 0)		// x == 0
      {
	__hy = (__hy & 0x8000) | 1;	// return +-__FLT16_DENORM_MIN__
	__builtin_memcpy(&__x, &__hy, sizeof(__x));
	__builtin_nextafterf(0.0f, 1.0f);	// raise underflow
	return __x;
      }
    if (__hx >= 0)		// x > 0
      {
	if (__hx > __hy)	// x > y, x -= ulp
	  --__hx;
	else			// x < y, x += ulp
	  ++__hx;
      }
    else			// x < 0
      {
	if (__hy >= 0 || __hx > __hy)	// x < y, x -= ulp
	  --__hx;
	else			// x > y, x += ulp
	  ++__hx;
      }
    __hy = __hx & 0x7c00;
    if (__hy >= 0x7c00)
      __builtin_nextafterf(__FLT_MAX__, __builtin_inff());	// overflow
    else if (__hy < 0x0400)
      __builtin_nextafterf(__FLT_MIN__, 0.0f);	// underflow
    __builtin_memcpy(&__x, &__hx, sizeof(__x));
    return __x;
  }

  constexpr _Float16
  remainder(_Float16 __x, _Float16 __y)
  { return _Float16(__builtin_remainderf(__x, __y)); }

  inline _Float16
  remquo(_Float16 __x, _Float16 __y, int* __pquo)
  { return _Float16(__builtin_remquof(__x, __y, __pquo)); }

  constexpr _Float16
  rint(_Float16 __x)
  { return _Float16(__builtin_rintf(__x)); }

  constexpr _Float16
  round(_Float16 __x)
  { return _Float16(__builtin_roundf(__x)); }

  constexpr _Float16
  scalbln(_Float16 __x, long __ex)
  { return _Float16(__builtin_scalblnf(__x, __ex)); }

  constexpr _Float16
  scalbn(_Float16 __x, int __ex)
  { return _Float16(__builtin_scalbnf(__x, __ex)); }

  constexpr _Float16
  tgamma(_Float16 __x)
  { return _Float16(__builtin_tgammaf(__x)); }

  constexpr _Float16
  trunc(_Float16 __x)
  { return _Float16(__builtin_truncf(__x)); }
#endif

#if defined(__STDCPP_FLOAT32_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  constexpr _Float32
  acosh(_Float32 __x)
  { return __builtin_acoshf(__x); }

  constexpr _Float32
  asinh(_Float32 __x)
  { return __builtin_asinhf(__x); }

  constexpr _Float32
  atanh(_Float32 __x)
  { return __builtin_atanhf(__x); }

  constexpr _Float32
  cbrt(_Float32 __x)
  { return __builtin_cbrtf(__x); }

  constexpr _Float32
  copysign(_Float32 __x, _Float32 __y)
  { return __builtin_copysignf(__x, __y); }

  constexpr _Float32
  erf(_Float32 __x)
  { return __builtin_erff(__x); }

  constexpr _Float32
  erfc(_Float32 __x)
  { return __builtin_erfcf(__x); }

  constexpr _Float32
  exp2(_Float32 __x)
  { return __builtin_exp2f(__x); }

  constexpr _Float32
  expm1(_Float32 __x)
  { return __builtin_expm1f(__x); }

  constexpr _Float32
  fdim(_Float32 __x, _Float32 __y)
  { return __builtin_fdimf(__x, __y); }

  constexpr _Float32
  fma(_Float32 __x, _Float32 __y, _Float32 __z)
  { return __builtin_fmaf(__x, __y, __z); }

  constexpr _Float32
  fmax(_Float32 __x, _Float32 __y)
  { return __builtin_fmaxf(__x, __y); }

  constexpr _Float32
  fmin(_Float32 __x, _Float32 __y)
  { return __builtin_fminf(__x, __y); }

  constexpr _Float32
  hypot(_Float32 __x, _Float32 __y)
  { return __builtin_hypotf(__x, __y); }

  constexpr int
  ilogb(_Float32 __x)
  { return __builtin_ilogbf(__x); }

  constexpr _Float32
  lgamma(_Float32 __x)
  { return __builtin_lgammaf(__x); }

  constexpr long long
  llrint(_Float32 __x)
  { return __builtin_llrintf(__x); }

  constexpr long long
  llround(_Float32 __x)
  { return __builtin_llroundf(__x); }

  constexpr _Float32
  log1p(_Float32 __x)
  { return __builtin_log1pf(__x); }

  // DR 568.
  constexpr _Float32
  log2(_Float32 __x)
  { return __builtin_log2f(__x); }

  constexpr _Float32
  logb(_Float32 __x)
  { return __builtin_logbf(__x); }

  constexpr long
  lrint(_Float32 __x)
  { return __builtin_lrintf(__x); }

  constexpr long
  lround(_Float32 __x)
  { return __builtin_lroundf(__x); }

  constexpr _Float32
  nearbyint(_Float32 __x)
  { return __builtin_nearbyintf(__x); }

  constexpr _Float32
  nextafter(_Float32 __x, _Float32 __y)
  { return __builtin_nextafterf(__x, __y); }

  constexpr _Float32
  remainder(_Float32 __x, _Float32 __y)
  { return __builtin_remainderf(__x, __y); }

  inline _Float32
  remquo(_Float32 __x, _Float32 __y, int* __pquo)
  { return __builtin_remquof(__x, __y, __pquo); }

  constexpr _Float32
  rint(_Float32 __x)
  { return __builtin_rintf(__x); }

  constexpr _Float32
  round(_Float32 __x)
  { return __builtin_roundf(__x); }

  constexpr _Float32
  scalbln(_Float32 __x, long __ex)
  { return __builtin_scalblnf(__x, __ex); }

  constexpr _Float32
  scalbn(_Float32 __x, int __ex)
  { return __builtin_scalbnf(__x, __ex); }

  constexpr _Float32
  tgamma(_Float32 __x)
  { return __builtin_tgammaf(__x); }

  constexpr _Float32
  trunc(_Float32 __x)
  { return __builtin_truncf(__x); }
#endif

#if defined(__STDCPP_FLOAT64_T__) && defined(_GLIBCXX_DOUBLE_IS_IEEE_BINARY64)
  constexpr _Float64
  acosh(_Float64 __x)
  { return __builtin_acosh(__x); }

  constexpr _Float64
  asinh(_Float64 __x)
  { return __builtin_asinh(__x); }

  constexpr _Float64
  atanh(_Float64 __x)
  { return __builtin_atanh(__x); }

  constexpr _Float64
  cbrt(_Float64 __x)
  { return __builtin_cbrt(__x); }

  constexpr _Float64
  copysign(_Float64 __x, _Float64 __y)
  { return __builtin_copysign(__x, __y); }

  constexpr _Float64
  erf(_Float64 __x)
  { return __builtin_erf(__x); }

  constexpr _Float64
  erfc(_Float64 __x)
  { return __builtin_erfc(__x); }

  constexpr _Float64
  exp2(_Float64 __x)
  { return __builtin_exp2(__x); }

  constexpr _Float64
  expm1(_Float64 __x)
  { return __builtin_expm1(__x); }

  constexpr _Float64
  fdim(_Float64 __x, _Float64 __y)
  { return __builtin_fdim(__x, __y); }

  constexpr _Float64
  fma(_Float64 __x, _Float64 __y, _Float64 __z)
  { return __builtin_fma(__x, __y, __z); }

  constexpr _Float64
  fmax(_Float64 __x, _Float64 __y)
  { return __builtin_fmax(__x, __y); }

  constexpr _Float64
  fmin(_Float64 __x, _Float64 __y)
  { return __builtin_fmin(__x, __y); }

  constexpr _Float64
  hypot(_Float64 __x, _Float64 __y)
  { return __builtin_hypot(__x, __y); }

  constexpr int
  ilogb(_Float64 __x)
  { return __builtin_ilogb(__x); }

  constexpr _Float64
  lgamma(_Float64 __x)
  { return __builtin_lgamma(__x); }

  constexpr long long
  llrint(_Float64 __x)
  { return __builtin_llrint(__x); }

  constexpr long long
  llround(_Float64 __x)
  { return __builtin_llround(__x); }

  constexpr _Float64
  log1p(_Float64 __x)
  { return __builtin_log1p(__x); }

  // DR 568.
  constexpr _Float64
  log2(_Float64 __x)
  { return __builtin_log2(__x); }

  constexpr _Float64
  logb(_Float64 __x)
  { return __builtin_logb(__x); }

  constexpr long
  lrint(_Float64 __x)
  { return __builtin_lrint(__x); }

  constexpr long
  lround(_Float64 __x)
  { return __builtin_lround(__x); }

  constexpr _Float64
  nearbyint(_Float64 __x)
  { return __builtin_nearbyint(__x); }

  constexpr _Float64
  nextafter(_Float64 __x, _Float64 __y)
  { return __builtin_nextafter(__x, __y); }

  constexpr _Float64
  remainder(_Float64 __x, _Float64 __y)
  { return __builtin_remainder(__x, __y); }

  inline _Float64
  remquo(_Float64 __x, _Float64 __y, int* __pquo)
  { return __builtin_remquo(__x, __y, __pquo); }

  constexpr _Float64
  rint(_Float64 __x)
  { return __builtin_rint(__x); }

  constexpr _Float64
  round(_Float64 __x)
  { return __builtin_round(__x); }

  constexpr _Float64
  scalbln(_Float64 __x, long __ex)
  { return __builtin_scalbln(__x, __ex); }

  constexpr _Float64
  scalbn(_Float64 __x, int __ex)
  { return __builtin_scalbn(__x, __ex); }

  constexpr _Float64
  tgamma(_Float64 __x)
  { return __builtin_tgamma(__x); }

  constexpr _Float64
  trunc(_Float64 __x)
  { return __builtin_trunc(__x); }
#endif

#if defined(__STDCPP_FLOAT128_T__) && defined(_GLIBCXX_LDOUBLE_IS_IEEE_BINARY128)
  constexpr _Float128
  acosh(_Float128 __x)
  { return __builtin_acoshl(__x); }

  constexpr _Float128
  asinh(_Float128 __x)
  { return __builtin_asinhl(__x); }

  constexpr _Float128
  atanh(_Float128 __x)
  { return __builtin_atanhl(__x); }

  constexpr _Float128
  cbrt(_Float128 __x)
  { return __builtin_cbrtl(__x); }

  constexpr _Float128
  copysign(_Float128 __x, _Float128 __y)
  { return __builtin_copysignl(__x, __y); }

  constexpr _Float128
  erf(_Float128 __x)
  { return __builtin_erfl(__x); }

  constexpr _Float128
  erfc(_Float128 __x)
  { return __builtin_erfcl(__x); }

  constexpr _Float128
  exp2(_Float128 __x)
  { return __builtin_exp2l(__x); }

  constexpr _Float128
  expm1(_Float128 __x)
  { return __builtin_expm1l(__x); }

  constexpr _Float128
  fdim(_Float128 __x, _Float128 __y)
  { return __builtin_fdiml(__x, __y); }

  constexpr _Float128
  fma(_Float128 __x, _Float128 __y, _Float128 __z)
  { return __builtin_fmal(__x, __y, __z); }

  constexpr _Float128
  fmax(_Float128 __x, _Float128 __y)
  { return __builtin_fmaxl(__x, __y); }

  constexpr _Float128
  fmin(_Float128 __x, _Float128 __y)
  { return __builtin_fminl(__x, __y); }

  constexpr _Float128
  hypot(_Float128 __x, _Float128 __y)
  { return __builtin_hypotl(__x, __y); }

  constexpr int
  ilogb(_Float128 __x)
  { return __builtin_ilogbl(__x); }

  constexpr _Float128
  lgamma(_Float128 __x)
  { return __builtin_lgammal(__x); }

  constexpr long long
  llrint(_Float128 __x)
  { return __builtin_llrintl(__x); }

  constexpr long long
  llround(_Float128 __x)
  { return __builtin_llroundl(__x); }

  constexpr _Float128
  log1p(_Float128 __x)
  { return __builtin_log1pl(__x); }

  // DR 568.
  constexpr _Float128
  log2(_Float128 __x)
  { return __builtin_log2l(__x); }

  constexpr _Float128
  logb(_Float128 __x)
  { return __builtin_logbl(__x); }

  constexpr long
  lrint(_Float128 __x)
  { return __builtin_lrintl(__x); }

  constexpr long
  lround(_Float128 __x)
  { return __builtin_lroundl(__x); }

  constexpr _Float128
  nearbyint(_Float128 __x)
  { return __builtin_nearbyintl(__x); }

  constexpr _Float128
  nextafter(_Float128 __x, _Float128 __y)
  { return __builtin_nextafterl(__x, __y); }

  constexpr _Float128
  remainder(_Float128 __x, _Float128 __y)
  { return __builtin_remainderl(__x, __y); }

  inline _Float128
  remquo(_Float128 __x, _Float128 __y, int* __pquo)
  { return __builtin_remquol(__x, __y, __pquo); }

  constexpr _Float128
  rint(_Float128 __x)
  { return __builtin_rintl(__x); }

  constexpr _Float128
  round(_Float128 __x)
  { return __builtin_roundl(__x); }

  constexpr _Float128
  scalbln(_Float128 __x, long __ex)
  { return __builtin_scalblnl(__x, __ex); }

  constexpr _Float128
  scalbn(_Float128 __x, int __ex)
  { return __builtin_scalbnl(__x, __ex); }

  constexpr _Float128
  tgamma(_Float128 __x)
  { return __builtin_tgammal(__x); }

  constexpr _Float128
  trunc(_Float128 __x)
  { return __builtin_truncl(__x); }
#elif defined(__STDCPP_FLOAT128_T__) && defined(_GLIBCXX_HAVE_FLOAT128_MATH)
  constexpr _Float128
  acosh(_Float128 __x)
  { return __builtin_acoshf128(__x); }

  constexpr _Float128
  asinh(_Float128 __x)
  { return __builtin_asinhf128(__x); }

  constexpr _Float128
  atanh(_Float128 __x)
  { return __builtin_atanhf128(__x); }

  constexpr _Float128
  cbrt(_Float128 __x)
  { return __builtin_cbrtf128(__x); }

  constexpr _Float128
  copysign(_Float128 __x, _Float128 __y)
  { return __builtin_copysignf128(__x, __y); }

  constexpr _Float128
  erf(_Float128 __x)
  { return __builtin_erff128(__x); }

  constexpr _Float128
  erfc(_Float128 __x)
  { return __builtin_erfcf128(__x); }

  constexpr _Float128
  exp2(_Float128 __x)
  { return __builtin_exp2f128(__x); }

  constexpr _Float128
  expm1(_Float128 __x)
  { return __builtin_expm1f128(__x); }

  constexpr _Float128
  fdim(_Float128 __x, _Float128 __y)
  { return __builtin_fdimf128(__x, __y); }

  constexpr _Float128
  fma(_Float128 __x, _Float128 __y, _Float128 __z)
  { return __builtin_fmaf128(__x, __y, __z); }

  constexpr _Float128
  fmax(_Float128 __x, _Float128 __y)
  { return __builtin_fmaxf128(__x, __y); }

  constexpr _Float128
  fmin(_Float128 __x, _Float128 __y)
  { return __builtin_fminf128(__x, __y); }

  constexpr _Float128
  hypot(_Float128 __x, _Float128 __y)
  { return __builtin_hypotf128(__x, __y); }

  constexpr int
  ilogb(_Float128 __x)
  { return __builtin_ilogbf128(__x); }

  constexpr _Float128
  lgamma(_Float128 __x)
  { return __builtin_lgammaf128(__x); }

  constexpr long long
  llrint(_Float128 __x)
  { return __builtin_llrintf128(__x); }

  constexpr long long
  llround(_Float128 __x)
  { return __builtin_llroundf128(__x); }

  constexpr _Float128
  log1p(_Float128 __x)
  { return __builtin_log1pf128(__x); }

  // DR 568.
  constexpr _Float128
  log2(_Float128 __x)
  { return __builtin_log2f128(__x); }

  constexpr _Float128
  logb(_Float128 __x)
  { return __builtin_logbf128(__x); }

  constexpr long
  lrint(_Float128 __x)
  { return __builtin_lrintf128(__x); }

  constexpr long
  lround(_Float128 __x)
  { return __builtin_lroundf128(__x); }

  constexpr _Float128
  nearbyint(_Float128 __x)
  { return __builtin_nearbyintf128(__x); }

  constexpr _Float128
  nextafter(_Float128 __x, _Float128 __y)
  { return __builtin_nextafterf128(__x, __y); }

  constexpr _Float128
  remainder(_Float128 __x, _Float128 __y)
  { return __builtin_remainderf128(__x, __y); }

  inline _Float128
  remquo(_Float128 __x, _Float128 __y, int* __pquo)
  { return __builtin_remquof128(__x, __y, __pquo); }

  constexpr _Float128
  rint(_Float128 __x)
  { return __builtin_rintf128(__x); }

  constexpr _Float128
  round(_Float128 __x)
  { return __builtin_roundf128(__x); }

  constexpr _Float128
  scalbln(_Float128 __x, long __ex)
  { return __builtin_scalblnf128(__x, __ex); }

  constexpr _Float128
  scalbn(_Float128 __x, int __ex)
  { return __builtin_scalbnf128(__x, __ex); }

  constexpr _Float128
  tgamma(_Float128 __x)
  { return __builtin_tgammaf128(__x); }

  constexpr _Float128
  trunc(_Float128 __x)
  { return __builtin_truncf128(__x); }
#endif

#if defined(__STDCPP_BFLOAT16_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  constexpr __gnu_cxx::__bfloat16_t
  acosh(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_acoshf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  asinh(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_asinhf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  atanh(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_atanhf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  cbrt(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_cbrtf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  copysign(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __gnu_cxx::__bfloat16_t(__builtin_copysignf(__x, __y)); }

  constexpr __gnu_cxx::__bfloat16_t
  erf(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_erff(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  erfc(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_erfcf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  exp2(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_exp2f(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  expm1(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_expm1f(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  fdim(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __gnu_cxx::__bfloat16_t(__builtin_fdimf(__x, __y)); }

  constexpr __gnu_cxx::__bfloat16_t
  fma(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y, __gnu_cxx::__bfloat16_t __z)
  { return __gnu_cxx::__bfloat16_t(__builtin_fmaf(__x, __y, __z)); }

  constexpr __gnu_cxx::__bfloat16_t
  fmax(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __gnu_cxx::__bfloat16_t(__builtin_fmaxf(__x, __y)); }

  constexpr __gnu_cxx::__bfloat16_t
  fmin(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __gnu_cxx::__bfloat16_t(__builtin_fminf(__x, __y)); }

  constexpr __gnu_cxx::__bfloat16_t
  hypot(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __gnu_cxx::__bfloat16_t(__builtin_hypotf(__x, __y)); }

  constexpr int
  ilogb(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_ilogbf(__x); }

  constexpr __gnu_cxx::__bfloat16_t
  lgamma(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_lgammaf(__x)); }

  constexpr long long
  llrint(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_llrintf(__x); }

  constexpr long long
  llround(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_llroundf(__x); }

  constexpr __gnu_cxx::__bfloat16_t
  log1p(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_log1pf(__x)); }

  // DR 568.
  constexpr __gnu_cxx::__bfloat16_t
  log2(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_log2f(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  logb(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_logbf(__x)); }

  constexpr long
  lrint(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_lrintf(__x); }

  constexpr long
  lround(__gnu_cxx::__bfloat16_t __x)
  { return __builtin_lroundf(__x); }

  constexpr __gnu_cxx::__bfloat16_t
  nearbyint(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_nearbyintf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  nextafter(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  {
#if __cpp_if_consteval >= 202106L
    // Can't use if (std::__is_constant_evaluated()) here, as it
    // doesn't guarantee optimizing the body away at -O0 and
    // nothing defines nextafterf16b.
    if consteval { return __builtin_nextafterf16b(__x, __y); }
#endif
#ifdef __INT16_TYPE__
    using __bfloat16_int_type = __INT16_TYPE__;
#else
    using __bfloat16_int_type = short int;
#endif
    __bfloat16_int_type __hx, __hy, __ix, __iy;
    __builtin_memcpy(&__hx, &__x, sizeof(__x));
    __builtin_memcpy(&__hy, &__y, sizeof(__x));
    __ix = __hx & 0x7fff;	// |x|
    __iy = __hy & 0x7fff;	// |y|
    if (__ix > 0x7f80 || __iy > 0x7f80) // x or y is NaN
      return __x + __y;
    if (__x == __y)
      return __y;		// x == y, return y
    if (__ix == 0)		// x == 0
      {
	__hy = (__hy & 0x8000) | 1;	// return +-__BFLT16_DENORM_MIN__
	__builtin_memcpy(&__x, &__hy, sizeof(__x));
	__builtin_nextafterf(0.0f, 1.0f);	// raise underflow
	return __x;
      }
    if (__hx >= 0)		// x > 0
      {
	if (__hx > __hy)	// x > y, x -= ulp
	  --__hx;
	else			// x < y, x += ulp
	  ++__hx;
      }
    else			// x < 0
      {
	if (__hy >= 0 || __hx > __hy)	// x < y, x -= ulp
	  --__hx;
	else			// x > y, x += ulp
	  ++__hx;
      }
    __hy = __hx & 0x7f80;
    if (__hy >= 0x7f80)
      __builtin_nextafterf(__FLT_MAX__, __builtin_inff());	// overflow
    else if (__hy < 0x0080)
      __builtin_nextafterf(__FLT_MIN__, 0.0f);	// underflow
    __builtin_memcpy(&__x, &__hx, sizeof(__x));
    return __x;
  }

  constexpr __gnu_cxx::__bfloat16_t
  remainder(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y)
  { return __gnu_cxx::__bfloat16_t(__builtin_remainderf(__x, __y)); }

  inline __gnu_cxx::__bfloat16_t
  remquo(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y, int* __pquo)
  { return __gnu_cxx::__bfloat16_t(__builtin_remquof(__x, __y, __pquo)); }

  constexpr __gnu_cxx::__bfloat16_t
  rint(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_rintf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  round(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_roundf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  scalbln(__gnu_cxx::__bfloat16_t __x, long __ex)
  { return __gnu_cxx::__bfloat16_t(__builtin_scalblnf(__x, __ex)); }

  constexpr __gnu_cxx::__bfloat16_t
  scalbn(__gnu_cxx::__bfloat16_t __x, int __ex)
  { return __gnu_cxx::__bfloat16_t(__builtin_scalbnf(__x, __ex)); }

  constexpr __gnu_cxx::__bfloat16_t
  tgamma(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_tgammaf(__x)); }

  constexpr __gnu_cxx::__bfloat16_t
  trunc(__gnu_cxx::__bfloat16_t __x)
  { return __gnu_cxx::__bfloat16_t(__builtin_truncf(__x)); }
#endif

#ifndef __CORRECT_ISO_CPP11_MATH_H_PROTO_INT
  template<typename _Tp, typename _Up>
    constexpr typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    copysign(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return copysign(__type(__x), __type(__y));
    }

  template<typename _Tp, typename _Up>
    constexpr typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    fdim(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return fdim(__type(__x), __type(__y));
    }

  template<typename _Tp, typename _Up>
    constexpr typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    fmax(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return fmax(__type(__x), __type(__y));
    }

  template<typename _Tp, typename _Up>
    constexpr typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    fmin(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return fmin(__type(__x), __type(__y));
    }

  template<typename _Tp, typename _Up>
    constexpr typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    hypot(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return hypot(__type(__x), __type(__y));
    }

  template<typename _Tp, typename _Up>
    constexpr typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    nextafter(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return nextafter(__type(__x), __type(__y));
    }

  template<typename _Tp, typename _Up>
    constexpr typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    remainder(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return remainder(__type(__x), __type(__y));
    }

  template<typename _Tp, typename _Up>
    inline typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    remquo(_Tp __x, _Up __y, int* __pquo)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return remquo(__type(__x), __type(__y), __pquo);
    }

  template<typename _Tp, typename _Up, typename _Vp>
    constexpr typename __gnu_cxx::__promote_3<_Tp, _Up, _Vp>::__type
    fma(_Tp __x, _Up __y, _Vp __z)
    {
      typedef typename __gnu_cxx::__promote_3<_Tp, _Up, _Vp>::__type __type;
      return fma(__type(__x), __type(__y), __type(__z));
    }
#endif

#endif // _GLIBCXX_USE_C99_MATH_FUNCS
#endif // C++11

#ifdef __cpp_lib_hypot // C++ >= 17 && HOSTED
  // [c.math.hypot3], three-dimensional hypotenuse
  template<typename _Tp>
    inline _Tp
    __hypot3(_Tp __x, _Tp __y, _Tp __z)
    {
      __x = std::abs(__x);
      __y = std::abs(__y);
      __z = std::abs(__z);
      if (_Tp __a = __x < __y ? __y < __z ? __z : __y : __x < __z ? __z : __x)
	return __a * std::sqrt((__x / __a) * (__x / __a)
			       + (__y / __a) * (__y / __a)
			       + (__z / __a) * (__z / __a));
      else
	return {};
    }

  inline float
  hypot(float __x, float __y, float __z)
  { return std::__hypot3<float>(__x, __y, __z); }

  inline double
  hypot(double __x, double __y, double __z)
  { return std::__hypot3<double>(__x, __y, __z); }

  inline long double
  hypot(long double __x, long double __y, long double __z)
  { return std::__hypot3<long double>(__x, __y, __z); }

  template<typename _Tp, typename _Up, typename _Vp>
    __gnu_cxx::__promoted_t<_Tp, _Up, _Vp>
    hypot(_Tp __x, _Up __y, _Vp __z)
    {
      using __type = __gnu_cxx::__promoted_t<_Tp, _Up, _Vp>;
      return std::__hypot3<__type>(__x, __y, __z);
    }

#if defined(__STDCPP_FLOAT16_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  inline _Float16
  hypot(_Float16 __x, _Float16 __y, _Float16 __z)
  { return std::__hypot3<_Float16>(__x, __y, __z); }
#endif

#if defined(__STDCPP_FLOAT32_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  inline _Float32
  hypot(_Float32 __x, _Float32 __y, _Float32 __z)
  { return std::__hypot3<_Float32>(__x, __y, __z); }
#endif

#if defined(__STDCPP_FLOAT64_T__) && defined(_GLIBCXX_DOUBLE_IS_IEEE_BINARY64)
  inline _Float64
  hypot(_Float64 __x, _Float64 __y, _Float64 __z)
  { return std::__hypot3<_Float64>(__x, __y, __z); }
#endif

#if defined(__STDCPP_FLOAT128_T__) \
    && (defined(_GLIBCXX_LDOUBLE_IS_IEEE_BINARY128) \
	|| defined(_GLIBCXX_HAVE_FLOAT128_MATH))
  inline _Float128
  hypot(_Float128 __x, _Float128 __y, _Float128 __z)
  { return std::__hypot3<_Float128>(__x, __y, __z); }
#endif

#if defined(__STDCPP_BFLOAT16_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  inline __gnu_cxx::__bfloat16_t
  hypot(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y, __gnu_cxx::__bfloat16_t __z)
  { return std::__hypot3<__gnu_cxx::__bfloat16_t>(__x, __y, __z); }
#endif

#endif // __cpp_lib_hypot

#ifdef __cpp_lib_interpolate // C++ >= 20
  // linear interpolation
  template<typename _Fp>
    constexpr _Fp
    __lerp(_Fp __a, _Fp __b, _Fp __t) noexcept
    {
      if ((__a <= 0 && __b >= 0) || (__a >= 0 && __b <= 0))
	return __t * __b + (1 - __t) * __a;

      if (__t == 1)
	return __b;                        // exact

      // Exact at __t=0, monotonic except near __t=1,
      // bounded, determinate, and consistent:
      const _Fp __x = __a + __t * (__b - __a);
      return (__t > 1) == (__b > __a)
	? (__b < __x ? __x : __b)
	: (__b > __x ? __x : __b);  // monotonic near __t=1
    }

  constexpr float
  lerp(float __a, float __b, float __t) noexcept
  { return std::__lerp(__a, __b, __t); }

  constexpr double
  lerp(double __a, double __b, double __t) noexcept
  { return std::__lerp(__a, __b, __t); }

  constexpr long double
  lerp(long double __a, long double __b, long double __t) noexcept
  { return std::__lerp(__a, __b, __t); }

  template<typename _Tp, typename _Up, typename _Vp>
    constexpr __gnu_cxx::__promoted_t<_Tp, _Up, _Vp>
    lerp(_Tp __x, _Up __y, _Vp __z) noexcept
    {
      using __type = __gnu_cxx::__promoted_t<_Tp, _Up, _Vp>;
      return std::__lerp<__type>(__x, __y, __z);
    }

#if defined(__STDCPP_FLOAT16_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  inline _Float16
  lerp(_Float16 __x, _Float16 __y, _Float16 __z) noexcept
  { return std::__lerp<_Float16>(__x, __y, __z); }
#endif

#if defined(__STDCPP_FLOAT32_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  inline _Float32
  lerp(_Float32 __x, _Float32 __y, _Float32 __z) noexcept
  { return std::__lerp<_Float32>(__x, __y, __z); }
#endif

#if defined(__STDCPP_FLOAT64_T__) && defined(_GLIBCXX_DOUBLE_IS_IEEE_BINARY64)
  inline _Float64
  lerp(_Float64 __x, _Float64 __y, _Float64 __z) noexcept
  { return std::__lerp<_Float64>(__x, __y, __z); }
#endif

#if defined(__STDCPP_FLOAT128_T__) \
    && (defined(_GLIBCXX_LDOUBLE_IS_IEEE_BINARY128) \
	|| defined(_GLIBCXX_HAVE_FLOAT128_MATH))
  inline _Float128
  lerp(_Float128 __x, _Float128 __y, _Float128 __z) noexcept
  { return std::__lerp<_Float128>(__x, __y, __z); }
#endif

#if defined(__STDCPP_BFLOAT16_T__) && defined(_GLIBCXX_FLOAT_IS_IEEE_BINARY32)
  inline __gnu_cxx::__bfloat16_t
  lerp(__gnu_cxx::__bfloat16_t __x, __gnu_cxx::__bfloat16_t __y, __gnu_cxx::__bfloat16_t __z) noexcept
  { return std::__lerp<__gnu_cxx::__bfloat16_t>(__x, __y, __z); }
#endif
#endif // __cpp_lib_interpolate

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace

#if _GLIBCXX_USE_STD_SPEC_FUNCS
#  include <bits/specfun.h>
#endif

} // extern "C++"

#endif
