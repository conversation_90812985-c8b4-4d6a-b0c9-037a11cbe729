.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_client_get_request_status" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_client_get_request_status \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_certificate_client_get_request_status(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.SH "DESCRIPTION"
Get whether client certificate was requested on the last
handshake or not.
.SH "RETURNS"
0 if the peer (server) did not request client
authentication or 1 otherwise.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
