/* Generated automatically by the program `gencodes'
   from the machine description file `md'.  */

#ifndef GCC_INSN_CODES_H
#define GCC_INSN_CODES_H

enum insn_code {
  CODE_FOR_nothing = 0,
  CODE_FOR_ccmpqi = 1,
  CODE_FOR_ccmphi = 2,
  CODE_FOR_ccmpsi = 3,
  CODE_FOR_ccmpdi = 4,
  CODE_FOR_x86_sahf_1 = 47,
  CODE_FOR_x86_stc = 63,
  CODE_FOR_pushflsi2 = 80,
  CODE_FOR_pushfldi2 = 81,
  CODE_FOR_popflsi1 = 82,
  CODE_FOR_popfldi1 = 83,
  CODE_FOR_swapsi = 107,
  CODE_FOR_swapdi = 108,
  CODE_FOR_insvhi_1 = 128,
  CODE_FOR_insvsi_1 = 129,
  CODE_FOR_insvdi_1 = 130,
  CODE_FOR_push2_di = 159,
  CODE_FOR_pop2_di = 160,
  CODE_FOR_pushp_di = 161,
  CODE_FOR_popp_di = 162,
  CODE_FOR_push2p_di = 163,
  CODE_FOR_pop2p_di = 164,
  CODE_FOR_zero_extendditi2 = 174,
  CODE_FOR_zero_extendqidi2 = 176,
  CODE_FOR_zero_extendhidi2 = 177,
  CODE_FOR_zero_extendqisi2_and = 178,
  CODE_FOR_zero_extendhisi2_and = 179,
  CODE_FOR_zero_extendqihi2_and = 182,
  CODE_FOR_extendsidi2_1 = 185,
  CODE_FOR_extendditi2 = 186,
  CODE_FOR_extendqidi2 = 187,
  CODE_FOR_extendhidi2 = 188,
  CODE_FOR_extendhisi2 = 189,
  CODE_FOR_extendqisi2 = 191,
  CODE_FOR_extendqihi2 = 193,
  CODE_FOR_extendbfsf2_1 = 203,
  CODE_FOR_truncdfsf2 = 206,
  CODE_FOR_truncxfsf2 = 207,
  CODE_FOR_truncxfdf2 = 208,
  CODE_FOR_truncsfbf2 = 211,
  CODE_FOR_fix_trunchfsi2 = 212,
  CODE_FOR_fixuns_trunchfsi2 = 213,
  CODE_FOR_fix_trunchfdi2 = 214,
  CODE_FOR_fixuns_trunchfdi2 = 215,
  CODE_FOR_fixuns_truncsfdi2 = 216,
  CODE_FOR_fixuns_truncdfdi2 = 217,
  CODE_FOR_fixuns_truncsfsi2_avx512f = 218,
  CODE_FOR_fixuns_truncdfsi2_avx512f = 219,
  CODE_FOR_fix_truncsfsi_sse = 225,
  CODE_FOR_fix_truncsfdi_sse = 226,
  CODE_FOR_fix_truncdfsi_sse = 227,
  CODE_FOR_fix_truncdfdi_sse = 228,
  CODE_FOR_fix_trunchi_i387_fisttp = 229,
  CODE_FOR_fix_truncsi_i387_fisttp = 230,
  CODE_FOR_fix_truncdi_i387_fisttp = 231,
  CODE_FOR_fix_truncdi_i387 = 235,
  CODE_FOR_fix_trunchi_i387 = 236,
  CODE_FOR_fix_truncsi_i387 = 237,
  CODE_FOR_x86_fnstcw_1 = 238,
  CODE_FOR_floathisf2 = 239,
  CODE_FOR_floathidf2 = 240,
  CODE_FOR_floathixf2 = 241,
  CODE_FOR_floatsixf2 = 242,
  CODE_FOR_floatdixf2 = 243,
  CODE_FOR_floatsihf2 = 248,
  CODE_FOR_floatunssihf2 = 249,
  CODE_FOR_floatdihf2 = 250,
  CODE_FOR_floatunsdihf2 = 251,
  CODE_FOR_floatdisf2_i387_with_xmm = 254,
  CODE_FOR_floatdidf2_i387_with_xmm = 255,
  CODE_FOR_floatdixf2_i387_with_xmm = 256,
  CODE_FOR_floatunssisf2_i387_with_xmm = 261,
  CODE_FOR_floatunssidf2_i387_with_xmm = 262,
  CODE_FOR_floatunssixf2_i387_with_xmm = 263,
  CODE_FOR_addsi_1_zext = 296,
  CODE_FOR_addvqi4_1 = 346,
  CODE_FOR_addvhi4_1 = 347,
  CODE_FOR_addvsi4_1 = 348,
  CODE_FOR_addvdi4_1 = 349,
  CODE_FOR_subvqi4_1 = 429,
  CODE_FOR_subvhi4_1 = 430,
  CODE_FOR_subvsi4_1 = 431,
  CODE_FOR_subvdi4_1 = 432,
  CODE_FOR_addqi3_carry = 453,
  CODE_FOR_addhi3_carry = 454,
  CODE_FOR_addsi3_carry = 455,
  CODE_FOR_adddi3_carry = 456,
  CODE_FOR_addcarrysi = 483,
  CODE_FOR_addcarrydi = 484,
  CODE_FOR_subqi3_carry = 487,
  CODE_FOR_subhi3_carry = 488,
  CODE_FOR_subsi3_carry = 489,
  CODE_FOR_subdi3_carry = 490,
  CODE_FOR_subsi3_carry_ccc = 517,
  CODE_FOR_subdi3_carry_ccc = 518,
  CODE_FOR_subsi3_carry_ccgz = 521,
  CODE_FOR_subdi3_carry_ccgz = 522,
  CODE_FOR_subborrowsi = 523,
  CODE_FOR_subborrowdi = 524,
  CODE_FOR_addqi3_cc_overflow_1 = 538,
  CODE_FOR_addhi3_cc_overflow_1 = 539,
  CODE_FOR_addsi3_cc_overflow_1 = 540,
  CODE_FOR_adddi3_cc_overflow_1 = 541,
  CODE_FOR_smulsi3_highpart = 627,
  CODE_FOR_umulsi3_highpart = 628,
  CODE_FOR_smuldi3_highpart = 629,
  CODE_FOR_umuldi3_highpart = 630,
  CODE_FOR_divmodsi4_1 = 639,
  CODE_FOR_divmoddi4_1 = 640,
  CODE_FOR_udivmodsi4_1 = 641,
  CODE_FOR_udivmoddi4_1 = 642,
  CODE_FOR_divmodsi4_zext_1 = 643,
  CODE_FOR_udivmodsi4_zext_1 = 644,
  CODE_FOR_divmodsi4_zext_2 = 645,
  CODE_FOR_udivmodsi4_zext_2 = 646,
  CODE_FOR_divmodhiqi3_nf = 678,
  CODE_FOR_divmodhiqi3 = 679,
  CODE_FOR_udivmodhiqi3_nf = 680,
  CODE_FOR_udivmodhiqi3 = 681,
  CODE_FOR_ashldi3_doubleword = 1056,
  CODE_FOR_ashlti3_doubleword = 1057,
  CODE_FOR_x86_64_shld_nf = 1062,
  CODE_FOR_x86_64_shld = 1063,
  CODE_FOR_x86_64_shld_ndd_nf = 1064,
  CODE_FOR_x86_64_shld_ndd = 1065,
  CODE_FOR_x86_64_shld_1_nf = 1066,
  CODE_FOR_x86_64_shld_1 = 1067,
  CODE_FOR_x86_64_shld_ndd_1_nf = 1068,
  CODE_FOR_x86_64_shld_ndd_1 = 1069,
  CODE_FOR_x86_shld_nf = 1074,
  CODE_FOR_x86_shld = 1075,
  CODE_FOR_x86_shld_ndd_nf = 1076,
  CODE_FOR_x86_shld_ndd = 1077,
  CODE_FOR_x86_shld_1_nf = 1078,
  CODE_FOR_x86_shld_1 = 1079,
  CODE_FOR_x86_shld_ndd_1_nf = 1080,
  CODE_FOR_x86_shld_ndd_1 = 1081,
  CODE_FOR_lshrdi3_doubleword = 1130,
  CODE_FOR_ashrdi3_doubleword = 1131,
  CODE_FOR_lshrti3_doubleword = 1132,
  CODE_FOR_ashrti3_doubleword = 1133,
  CODE_FOR_lshrdi3_doubleword_lowpart_nf = 1134,
  CODE_FOR_ashrdi3_doubleword_lowpart_nf = 1135,
  CODE_FOR_lshrti3_doubleword_lowpart_nf = 1136,
  CODE_FOR_ashrti3_doubleword_lowpart_nf = 1137,
  CODE_FOR_lshrdi3_doubleword_lowpart = 1138,
  CODE_FOR_ashrdi3_doubleword_lowpart = 1139,
  CODE_FOR_lshrti3_doubleword_lowpart = 1140,
  CODE_FOR_ashrti3_doubleword_lowpart = 1141,
  CODE_FOR_x86_64_shrd_nf = 1142,
  CODE_FOR_x86_64_shrd = 1143,
  CODE_FOR_x86_64_shrd_ndd_nf = 1144,
  CODE_FOR_x86_64_shrd_ndd = 1145,
  CODE_FOR_x86_64_shrd_1_nf = 1146,
  CODE_FOR_x86_64_shrd_1 = 1147,
  CODE_FOR_x86_64_shrd_ndd_1_nf = 1148,
  CODE_FOR_x86_64_shrd_ndd_1 = 1149,
  CODE_FOR_x86_shrd_nf = 1154,
  CODE_FOR_x86_shrd = 1155,
  CODE_FOR_x86_shrd_ndd_nf = 1156,
  CODE_FOR_x86_shrd_ndd = 1157,
  CODE_FOR_x86_shrd_1_nf = 1158,
  CODE_FOR_x86_shrd_1 = 1159,
  CODE_FOR_x86_shrd_ndd_1_nf = 1160,
  CODE_FOR_x86_shrd_ndd_1 = 1161,
  CODE_FOR_ashrsi3_cvt_nf = 1166,
  CODE_FOR_ashrsi3_cvt = 1167,
  CODE_FOR_ashrdi3_cvt_nf = 1168,
  CODE_FOR_ashrdi3_cvt = 1169,
  CODE_FOR_ix86_rotldi3_doubleword = 1332,
  CODE_FOR_ix86_rotlti3_doubleword = 1333,
  CODE_FOR_ix86_rotrdi3_doubleword = 1334,
  CODE_FOR_ix86_rotrti3_doubleword = 1335,
  CODE_FOR_rotl32di2_doubleword = 1336,
  CODE_FOR_rotr32di2_doubleword = 1337,
  CODE_FOR_rotl64ti2_doubleword = 1338,
  CODE_FOR_rotr64ti2_doubleword = 1339,
  CODE_FOR_rcrsi2 = 1385,
  CODE_FOR_rcrdi2 = 1386,
  CODE_FOR_lshrsi3_carry = 1387,
  CODE_FOR_ashrsi3_carry = 1388,
  CODE_FOR_lshrdi3_carry = 1389,
  CODE_FOR_ashrdi3_carry = 1390,
  CODE_FOR_setcc_sf_sse = 1455,
  CODE_FOR_setcc_df_sse = 1456,
  CODE_FOR_setcc_hf_mask = 1457,
  CODE_FOR_jump = 1459,
  CODE_FOR_blockage = 1486,
  CODE_FOR_prologue_use = 1488,
  CODE_FOR_simple_return_internal = 1489,
  CODE_FOR_interrupt_return = 1490,
  CODE_FOR_simple_return_internal_long = 1491,
  CODE_FOR_simple_return_pop_internal = 1492,
  CODE_FOR_nop = 1495,
  CODE_FOR_nops = 1496,
  CODE_FOR_max_skip_align = 1497,
  CODE_FOR_set_got_rex64 = 1500,
  CODE_FOR_set_rip_rex64 = 1501,
  CODE_FOR_set_got_offset_rex64 = 1502,
  CODE_FOR_eh_return_internal = 1503,
  CODE_FOR_split_stack_return = 1506,
  CODE_FOR_ffssi2_no_cmove = 1507,
  CODE_FOR_ctzsi2 = 1514,
  CODE_FOR_ctzdi2 = 1515,
  CODE_FOR_bsr_rex64 = 1524,
  CODE_FOR_bsr_rex64_1 = 1525,
  CODE_FOR_bsr_rex64_1_zext = 1526,
  CODE_FOR_bsr = 1527,
  CODE_FOR_bsr_1 = 1528,
  CODE_FOR_bsr_zext_1 = 1529,
  CODE_FOR_clzsi2_lzcnt_nf = 1532,
  CODE_FOR_clzdi2_lzcnt_nf = 1533,
  CODE_FOR_clzsi2_lzcnt = 1534,
  CODE_FOR_clzdi2_lzcnt = 1535,
  CODE_FOR_tzcnt_si_nf = 1546,
  CODE_FOR_lzcnt_si_nf = 1547,
  CODE_FOR_tzcnt_di_nf = 1548,
  CODE_FOR_lzcnt_di_nf = 1549,
  CODE_FOR_tzcnt_si = 1550,
  CODE_FOR_lzcnt_si = 1551,
  CODE_FOR_tzcnt_di = 1552,
  CODE_FOR_lzcnt_di = 1553,
  CODE_FOR_tzcnt_hi_nf = 1562,
  CODE_FOR_tzcnt_hi = 1563,
  CODE_FOR_lzcnt_hi_nf = 1564,
  CODE_FOR_lzcnt_hi = 1565,
  CODE_FOR_bmi_bextr_si = 1566,
  CODE_FOR_bmi_bextr_di = 1567,
  CODE_FOR_bmi2_pdep_si3 = 1600,
  CODE_FOR_bmi2_pdep_di3 = 1601,
  CODE_FOR_bmi2_pext_si3 = 1602,
  CODE_FOR_bmi2_pext_di3 = 1603,
  CODE_FOR_tbm_bextri_si = 1604,
  CODE_FOR_tbm_bextri_di = 1605,
  CODE_FOR_popcountsi2_nf = 1624,
  CODE_FOR_popcountdi2_nf = 1625,
  CODE_FOR_popcountsi2 = 1626,
  CODE_FOR_popcountdi2 = 1627,
  CODE_FOR_popcounthi2_nf = 1638,
  CODE_FOR_popcounthi2 = 1639,
  CODE_FOR_bswaphisi2_lowpart = 1646,
  CODE_FOR_parityhi2_cmp = 1647,
  CODE_FOR_parityqi2_cmp = 1648,
  CODE_FOR_rcphf2 = 1679,
  CODE_FOR_truncxfsf2_i387_noop_unspec = 1704,
  CODE_FOR_truncxfdf2_i387_noop_unspec = 1705,
  CODE_FOR_sqrtxf2 = 1706,
  CODE_FOR_rsqrthf2 = 1708,
  CODE_FOR_sqrthf2 = 1709,
  CODE_FOR_x86_fnstsw_1 = 1712,
  CODE_FOR_fpremxf4_i387 = 1713,
  CODE_FOR_fprem1xf4_i387 = 1714,
  CODE_FOR_sinxf2 = 1715,
  CODE_FOR_cosxf2 = 1716,
  CODE_FOR_sincosxf3 = 1717,
  CODE_FOR_fptanxf4_i387 = 1718,
  CODE_FOR_atan2xf3 = 1719,
  CODE_FOR_fyl2xxf3_i387 = 1720,
  CODE_FOR_fyl2xp1xf3_i387 = 1721,
  CODE_FOR_fxtractxf3_i387 = 1722,
  CODE_FOR_fscalexf4_i387 = 1724,
  CODE_FOR_avx512f_scalefsf2 = 1725,
  CODE_FOR_avx512f_scalefdf2 = 1726,
  CODE_FOR_sse4_1_roundhf2 = 1727,
  CODE_FOR_sse4_1_roundsf2 = 1728,
  CODE_FOR_sse4_1_rounddf2 = 1729,
  CODE_FOR_rintxf2 = 1730,
  CODE_FOR_lrintxfdi2 = 1731,
  CODE_FOR_lrintxfhi2 = 1732,
  CODE_FOR_lrintxfsi2 = 1733,
  CODE_FOR_frndintxf2_roundeven = 1734,
  CODE_FOR_frndintxf2_floor = 1735,
  CODE_FOR_frndintxf2_ceil = 1736,
  CODE_FOR_frndintxf2_trunc = 1737,
  CODE_FOR_frndintxf2_roundeven_i387 = 1738,
  CODE_FOR_frndintxf2_floor_i387 = 1739,
  CODE_FOR_frndintxf2_ceil_i387 = 1740,
  CODE_FOR_frndintxf2_trunc_i387 = 1741,
  CODE_FOR_fistdi2_floor = 1748,
  CODE_FOR_fistdi2_ceil = 1749,
  CODE_FOR_fisthi2_floor = 1750,
  CODE_FOR_fisthi2_ceil = 1751,
  CODE_FOR_fistsi2_floor = 1752,
  CODE_FOR_fistsi2_ceil = 1753,
  CODE_FOR_fxamsf2_i387 = 1754,
  CODE_FOR_fxamdf2_i387 = 1755,
  CODE_FOR_fxamxf2_i387 = 1756,
  CODE_FOR_movmsk_df = 1757,
  CODE_FOR_cld = 1758,
  CODE_FOR_movhf_mask = 1807,
  CODE_FOR_smaxsf3 = 1813,
  CODE_FOR_sminsf3 = 1814,
  CODE_FOR_smaxdf3 = 1815,
  CODE_FOR_smindf3 = 1816,
  CODE_FOR_smaxhf3 = 1817,
  CODE_FOR_sminhf3 = 1818,
  CODE_FOR_pro_epilogue_adjust_stack_add_si = 1853,
  CODE_FOR_pro_epilogue_adjust_stack_add_di = 1854,
  CODE_FOR_pro_epilogue_adjust_stack_sub_si = 1855,
  CODE_FOR_pro_epilogue_adjust_stack_sub_di = 1856,
  CODE_FOR_allocate_stack_worker_probe_si = 1857,
  CODE_FOR_allocate_stack_worker_probe_di = 1858,
  CODE_FOR_probe_stack_1_si = 1859,
  CODE_FOR_probe_stack_1_di = 1860,
  CODE_FOR_adjust_stack_and_probe_si = 1861,
  CODE_FOR_adjust_stack_and_probe_di = 1862,
  CODE_FOR_probe_stack_range_si = 1863,
  CODE_FOR_probe_stack_range_di = 1864,
  CODE_FOR_stack_protect_set_1_si_si = 1865,
  CODE_FOR_stack_protect_set_1_di_si = 1866,
  CODE_FOR_stack_protect_set_1_si_di = 1867,
  CODE_FOR_stack_protect_set_1_di_di = 1868,
  CODE_FOR_stack_protect_test_1_si = 1881,
  CODE_FOR_stack_protect_test_1_di = 1882,
  CODE_FOR_trap = 1883,
  CODE_FOR_ud2 = 1884,
  CODE_FOR_prefetchi = 1887,
  CODE_FOR_sse4_2_crc32qi = 1889,
  CODE_FOR_sse4_2_crc32hi = 1890,
  CODE_FOR_sse4_2_crc32si = 1891,
  CODE_FOR_sse4_2_crc32di = 1892,
  CODE_FOR_rdpmc = 1893,
  CODE_FOR_rdpmc_rex64 = 1894,
  CODE_FOR_rdtsc = 1895,
  CODE_FOR_rdtsc_rex64 = 1896,
  CODE_FOR_rdtscp = 1897,
  CODE_FOR_rdtscp_rex64 = 1898,
  CODE_FOR_fxsave = 1899,
  CODE_FOR_fxsave64 = 1900,
  CODE_FOR_fxrstor = 1901,
  CODE_FOR_fxrstor64 = 1902,
  CODE_FOR_xsave = 1903,
  CODE_FOR_xsaveopt = 1904,
  CODE_FOR_xsavec = 1905,
  CODE_FOR_xsaves = 1906,
  CODE_FOR_xsave_rex64 = 1907,
  CODE_FOR_xsaveopt_rex64 = 1908,
  CODE_FOR_xsavec_rex64 = 1909,
  CODE_FOR_xsaves_rex64 = 1910,
  CODE_FOR_xsave64 = 1911,
  CODE_FOR_xsaveopt64 = 1912,
  CODE_FOR_xsavec64 = 1913,
  CODE_FOR_xsaves64 = 1914,
  CODE_FOR_xrstor = 1915,
  CODE_FOR_xrstors = 1916,
  CODE_FOR_xrstor_rex64 = 1917,
  CODE_FOR_xrstors_rex64 = 1918,
  CODE_FOR_xrstor64 = 1919,
  CODE_FOR_xrstors64 = 1920,
  CODE_FOR_xsetbv = 1921,
  CODE_FOR_xsetbv_rex64 = 1922,
  CODE_FOR_xgetbv = 1923,
  CODE_FOR_xgetbv_rex64 = 1924,
  CODE_FOR_fnstenv = 1925,
  CODE_FOR_fldenv = 1926,
  CODE_FOR_fnstsw = 1927,
  CODE_FOR_fnclex = 1928,
  CODE_FOR_lwp_llwpcbsi = 1929,
  CODE_FOR_lwp_llwpcbdi = 1930,
  CODE_FOR_lwp_slwpcbsi = 1931,
  CODE_FOR_lwp_slwpcbdi = 1932,
  CODE_FOR_lwp_lwpvalsi = 1933,
  CODE_FOR_lwp_lwpvaldi = 1934,
  CODE_FOR_lwp_lwpinssi = 1935,
  CODE_FOR_lwp_lwpinsdi = 1936,
  CODE_FOR_rdfsbasesi = 1937,
  CODE_FOR_rdgsbasesi = 1938,
  CODE_FOR_rdfsbasedi = 1939,
  CODE_FOR_rdgsbasedi = 1940,
  CODE_FOR_wrfsbasesi = 1941,
  CODE_FOR_wrgsbasesi = 1942,
  CODE_FOR_wrfsbasedi = 1943,
  CODE_FOR_wrgsbasedi = 1944,
  CODE_FOR_ptwritesi = 1945,
  CODE_FOR_ptwritedi = 1946,
  CODE_FOR_rdrandhi = 1947,
  CODE_FOR_rdrandsi = 1948,
  CODE_FOR_rdranddi = 1949,
  CODE_FOR_rdseedhi = 1950,
  CODE_FOR_rdseedsi = 1951,
  CODE_FOR_rdseeddi = 1952,
  CODE_FOR_rdsspsi = 1954,
  CODE_FOR_rdsspdi = 1955,
  CODE_FOR_incsspsi = 1956,
  CODE_FOR_incsspdi = 1957,
  CODE_FOR_saveprevssp = 1958,
  CODE_FOR_rstorssp = 1959,
  CODE_FOR_wrsssi = 1960,
  CODE_FOR_wrssdi = 1961,
  CODE_FOR_wrusssi = 1962,
  CODE_FOR_wrussdi = 1963,
  CODE_FOR_setssbsy = 1964,
  CODE_FOR_clrssbsy = 1965,
  CODE_FOR_nop_endbr = 1966,
  CODE_FOR_xbegin_1 = 1967,
  CODE_FOR_xend = 1968,
  CODE_FOR_xabort = 1969,
  CODE_FOR_xtest_1 = 1970,
  CODE_FOR_clwb = 1971,
  CODE_FOR_clflushopt = 1972,
  CODE_FOR_mwaitx = 1973,
  CODE_FOR_monitorx_si = 1974,
  CODE_FOR_monitorx_di = 1975,
  CODE_FOR_clzero_si = 1976,
  CODE_FOR_clzero_di = 1977,
  CODE_FOR_rdpid = 1980,
  CODE_FOR_rdpid_rex64 = 1981,
  CODE_FOR_wbinvd = 1982,
  CODE_FOR_wbnoinvd = 1983,
  CODE_FOR_movdirisi = 1984,
  CODE_FOR_movdiridi = 1985,
  CODE_FOR_movdir64b_si = 1986,
  CODE_FOR_movdir64b_di = 1987,
  CODE_FOR_xsusldtrk = 1988,
  CODE_FOR_xresldtrk = 1989,
  CODE_FOR_enqcmd_si = 1990,
  CODE_FOR_enqcmds_si = 1991,
  CODE_FOR_enqcmd_di = 1992,
  CODE_FOR_enqcmds_di = 1993,
  CODE_FOR_clui = 1994,
  CODE_FOR_stui = 1995,
  CODE_FOR_testui = 1996,
  CODE_FOR_senduipi = 1997,
  CODE_FOR_umwait = 1998,
  CODE_FOR_umwait_rex64 = 1999,
  CODE_FOR_umonitor_si = 2000,
  CODE_FOR_umonitor_di = 2001,
  CODE_FOR_tpause = 2002,
  CODE_FOR_tpause_rex64 = 2003,
  CODE_FOR_cldemote = 2004,
  CODE_FOR_speculation_barrier = 2005,
  CODE_FOR_serialize = 2006,
  CODE_FOR_patchable_area = 2007,
  CODE_FOR_hreset = 2008,
  CODE_FOR_urdmsr = 2009,
  CODE_FOR_uwrmsr = 2010,
  CODE_FOR_ldtilecfg = 2011,
  CODE_FOR_sttilecfg = 2012,
  CODE_FOR_movrsqi = 2013,
  CODE_FOR_movrshi = 2014,
  CODE_FOR_movrssi = 2015,
  CODE_FOR_movrsdi = 2016,
  CODE_FOR_sse_movntq = 2046,
  CODE_FOR_mmx_ieee_maxv2sf3 = 2055,
  CODE_FOR_mmx_ieee_minv2sf3 = 2056,
  CODE_FOR_mmx_rcpv2sf2 = 2057,
  CODE_FOR_mmx_rcpit1v2sf3 = 2058,
  CODE_FOR_mmx_rcpit2v2sf3 = 2059,
  CODE_FOR_mmx_rsqrtv2sf2 = 2060,
  CODE_FOR_mmx_rsqit1v2sf3 = 2061,
  CODE_FOR_mmx_hsubv2sf3 = 2064,
  CODE_FOR_mmx_gtv2sf3 = 2068,
  CODE_FOR_mmx_gev2sf3 = 2069,
  CODE_FOR_sse4_1_insertps_v2sf = 2070,
  CODE_FOR_sse4_1_insertps_v2si = 2071,
  CODE_FOR_mmx_blendvps = 2073,
  CODE_FOR_andv2sf3 = 2075,
  CODE_FOR_iorv2sf3 = 2076,
  CODE_FOR_xorv2sf3 = 2077,
  CODE_FOR_mmx_fix_truncv2sfv2si2 = 2078,
  CODE_FOR_mmx_floatv2siv2sf2 = 2079,
  CODE_FOR_mmx_pf2iw = 2080,
  CODE_FOR_mmx_pi2fw = 2081,
  CODE_FOR_mmx_pswapdv2sf2 = 2082,
  CODE_FOR_andv2bf3 = 2109,
  CODE_FOR_iorv2bf3 = 2110,
  CODE_FOR_xorv2bf3 = 2111,
  CODE_FOR_andv4bf3 = 2112,
  CODE_FOR_iorv4bf3 = 2113,
  CODE_FOR_xorv4bf3 = 2114,
  CODE_FOR_andv2hf3 = 2115,
  CODE_FOR_iorv2hf3 = 2116,
  CODE_FOR_xorv2hf3 = 2117,
  CODE_FOR_andv4hf3 = 2118,
  CODE_FOR_iorv4hf3 = 2119,
  CODE_FOR_xorv4hf3 = 2120,
  CODE_FOR_negv2qi2 = 2121,
  CODE_FOR_addv4qi3 = 2130,
  CODE_FOR_subv4qi3 = 2131,
  CODE_FOR_addv2hi3 = 2132,
  CODE_FOR_subv2hi3 = 2133,
  CODE_FOR_addv2qi3 = 2134,
  CODE_FOR_subv2qi3 = 2135,
  CODE_FOR_ssaddv4qi3 = 2144,
  CODE_FOR_usaddv4qi3 = 2145,
  CODE_FOR_sssubv4qi3 = 2146,
  CODE_FOR_ussubv4qi3 = 2147,
  CODE_FOR_ssaddv2qi3 = 2148,
  CODE_FOR_usaddv2qi3 = 2149,
  CODE_FOR_sssubv2qi3 = 2150,
  CODE_FOR_ussubv2qi3 = 2151,
  CODE_FOR_ssaddv2hi3 = 2152,
  CODE_FOR_usaddv2hi3 = 2153,
  CODE_FOR_sssubv2hi3 = 2154,
  CODE_FOR_ussubv2hi3 = 2155,
  CODE_FOR_mulv2si3 = 2156,
  CODE_FOR_mulv2hi3 = 2158,
  CODE_FOR_smulv2hi3_highpart = 2161,
  CODE_FOR_umulv2hi3_highpart = 2162,
  CODE_FOR_smaxv8qi3 = 2166,
  CODE_FOR_sminv8qi3 = 2167,
  CODE_FOR_smaxv2si3 = 2168,
  CODE_FOR_sminv2si3 = 2169,
  CODE_FOR_smaxv4qi3 = 2172,
  CODE_FOR_sminv4qi3 = 2173,
  CODE_FOR_smaxv2qi3 = 2174,
  CODE_FOR_sminv2qi3 = 2175,
  CODE_FOR_smaxv2hi3 = 2176,
  CODE_FOR_sminv2hi3 = 2177,
  CODE_FOR_umaxv4hi3 = 2178,
  CODE_FOR_uminv4hi3 = 2179,
  CODE_FOR_umaxv2si3 = 2180,
  CODE_FOR_uminv2si3 = 2181,
  CODE_FOR_umaxv4qi3 = 2184,
  CODE_FOR_uminv4qi3 = 2185,
  CODE_FOR_umaxv2qi3 = 2186,
  CODE_FOR_uminv2qi3 = 2187,
  CODE_FOR_umaxv2hi3 = 2188,
  CODE_FOR_uminv2hi3 = 2189,
  CODE_FOR_ssse3_absv8qi2 = 2190,
  CODE_FOR_ssse3_absv4hi2 = 2191,
  CODE_FOR_ssse3_absv2si2 = 2192,
  CODE_FOR_absv4qi2 = 2193,
  CODE_FOR_absv2qi2 = 2194,
  CODE_FOR_absv2hi2 = 2195,
  CODE_FOR_mmx_ashrv4hi3 = 2196,
  CODE_FOR_mmx_ashrv2si3 = 2197,
  CODE_FOR_mmx_ashlv4hi3 = 2200,
  CODE_FOR_mmx_lshrv4hi3 = 2201,
  CODE_FOR_mmx_ashlv2si3 = 2202,
  CODE_FOR_mmx_lshrv2si3 = 2203,
  CODE_FOR_mmx_ashlv1di3 = 2204,
  CODE_FOR_mmx_lshrv1di3 = 2205,
  CODE_FOR_mmx_ashlv1si3 = 2206,
  CODE_FOR_mmx_lshrv1si3 = 2207,
  CODE_FOR_ashlv2hi3 = 2208,
  CODE_FOR_lshrv2hi3 = 2209,
  CODE_FOR_ashrv2hi3 = 2210,
  CODE_FOR_ashlv2qi3 = 2212,
  CODE_FOR_lshrv2qi3 = 2213,
  CODE_FOR_ashrv2qi3 = 2214,
  CODE_FOR_mmx_gtv8qi3 = 2221,
  CODE_FOR_mmx_gtv4hi3 = 2222,
  CODE_FOR_mmx_gtv2si3 = 2223,
  CODE_FOR_mmx_pblendvb_v8qi = 2239,
  CODE_FOR_mmx_pblendvb_v4qi = 2243,
  CODE_FOR_mmx_pblendvb_v2qi = 2244,
  CODE_FOR_mmx_pblendvb_v2hi = 2245,
  CODE_FOR_mmx_ppermv64 = 2261,
  CODE_FOR_mmx_ppermv32 = 2262,
  CODE_FOR_one_cmplv4qi2 = 2263,
  CODE_FOR_one_cmplv2qi2 = 2264,
  CODE_FOR_one_cmplv2hi2 = 2265,
  CODE_FOR_mmx_andnotv8qi3 = 2266,
  CODE_FOR_mmx_andnotv4hi3 = 2267,
  CODE_FOR_mmx_andnotv2si3 = 2268,
  CODE_FOR_mmx_packsswb = 2290,
  CODE_FOR_mmx_packuswb = 2291,
  CODE_FOR_mmx_packssdw = 2292,
  CODE_FOR_mmx_packusdw = 2293,
  CODE_FOR_mmx_punpckhbw = 2294,
  CODE_FOR_mmx_punpckhbw_low = 2295,
  CODE_FOR_mmx_punpcklbw = 2296,
  CODE_FOR_mmx_punpcklbw_low = 2297,
  CODE_FOR_mmx_punpckhwd = 2298,
  CODE_FOR_mmx_punpcklwd = 2299,
  CODE_FOR_mmx_punpckhdq = 2300,
  CODE_FOR_mmx_punpckldq = 2301,
  CODE_FOR_sse4_1_sign_extendv4qiv4hi2 = 2302,
  CODE_FOR_sse4_1_zero_extendv4qiv4hi2 = 2303,
  CODE_FOR_sse4_1_sign_extendv2hiv2si2 = 2304,
  CODE_FOR_sse4_1_zero_extendv2hiv2si2 = 2305,
  CODE_FOR_sse4_1_sign_extendv2qiv2si2 = 2306,
  CODE_FOR_sse4_1_zero_extendv2qiv2si2 = 2307,
  CODE_FOR_sse4_1_sign_extendv2qiv2hi2 = 2308,
  CODE_FOR_sse4_1_zero_extendv2qiv2hi2 = 2309,
  CODE_FOR_avx512vl_truncv4hiv4qi2 = 2310,
  CODE_FOR_avx512vl_truncv2hiv2qi2 = 2311,
  CODE_FOR_avx512vl_truncv2siv2qi2 = 2312,
  CODE_FOR_avx512vl_truncv2siv2hi2 = 2313,
  CODE_FOR_mmx_pshufbv8qi3 = 2328,
  CODE_FOR_mmx_pshufbv4qi3 = 2329,
  CODE_FOR_mmx_pshufwv4hf_1 = 2330,
  CODE_FOR_mmx_pshufwv4bf_1 = 2331,
  CODE_FOR_mmx_pshufwv4hi_1 = 2332,
  CODE_FOR_mmx_pswapdv2si2 = 2340,
  CODE_FOR_uavgv4qi3_ceil = 2378,
  CODE_FOR_uavgv2qi3_ceil = 2379,
  CODE_FOR_uavgv2hi3_ceil = 2380,
  CODE_FOR_mmx_pmovmskb = 2382,
  CODE_FOR_popcountv8qi2 = 2387,
  CODE_FOR_popcountv4qi2 = 2388,
  CODE_FOR_popcountv2qi2 = 2389,
  CODE_FOR_popcountv4hi2 = 2390,
  CODE_FOR_popcountv2hi2 = 2391,
  CODE_FOR_popcountv2si2 = 2392,
  CODE_FOR_movv64qi_internal = 2393,
  CODE_FOR_movv32qi_internal = 2394,
  CODE_FOR_movv16qi_internal = 2395,
  CODE_FOR_movv32hi_internal = 2396,
  CODE_FOR_movv16hi_internal = 2397,
  CODE_FOR_movv8hi_internal = 2398,
  CODE_FOR_movv16si_internal = 2399,
  CODE_FOR_movv8si_internal = 2400,
  CODE_FOR_movv4si_internal = 2401,
  CODE_FOR_movv8di_internal = 2402,
  CODE_FOR_movv4di_internal = 2403,
  CODE_FOR_movv2di_internal = 2404,
  CODE_FOR_movv4ti_internal = 2405,
  CODE_FOR_movv2ti_internal = 2406,
  CODE_FOR_movv1ti_internal = 2407,
  CODE_FOR_movv32hf_internal = 2408,
  CODE_FOR_movv16hf_internal = 2409,
  CODE_FOR_movv8hf_internal = 2410,
  CODE_FOR_movv32bf_internal = 2411,
  CODE_FOR_movv16bf_internal = 2412,
  CODE_FOR_movv8bf_internal = 2413,
  CODE_FOR_movv16sf_internal = 2414,
  CODE_FOR_movv8sf_internal = 2415,
  CODE_FOR_movv4sf_internal = 2416,
  CODE_FOR_movv8df_internal = 2417,
  CODE_FOR_movv4df_internal = 2418,
  CODE_FOR_movv2df_internal = 2419,
  CODE_FOR_avx512f_movhf_mask = 2495,
  CODE_FOR_avx512f_movsf_mask = 2496,
  CODE_FOR_avx512f_movdf_mask = 2497,
  CODE_FOR_avx512f_storehf_mask = 2501,
  CODE_FOR_avx512f_storesf_mask = 2502,
  CODE_FOR_avx512f_storedf_mask = 2503,
  CODE_FOR_avx512f_blendmv16si = 2504,
  CODE_FOR_avx512vl_blendmv8si = 2505,
  CODE_FOR_avx512vl_blendmv4si = 2506,
  CODE_FOR_avx512f_blendmv8di = 2507,
  CODE_FOR_avx512vl_blendmv4di = 2508,
  CODE_FOR_avx512vl_blendmv2di = 2509,
  CODE_FOR_avx512f_blendmv16sf = 2510,
  CODE_FOR_avx512vl_blendmv8sf = 2511,
  CODE_FOR_avx512vl_blendmv4sf = 2512,
  CODE_FOR_avx512f_blendmv8df = 2513,
  CODE_FOR_avx512vl_blendmv4df = 2514,
  CODE_FOR_avx512vl_blendmv2df = 2515,
  CODE_FOR_avx512bw_blendmv64qi = 2516,
  CODE_FOR_avx512vl_blendmv16qi = 2517,
  CODE_FOR_avx512vl_blendmv32qi = 2518,
  CODE_FOR_avx512bw_blendmv32hi = 2519,
  CODE_FOR_avx512vl_blendmv16hi = 2520,
  CODE_FOR_avx512vl_blendmv8hi = 2521,
  CODE_FOR_avx512bw_blendmv32hf = 2522,
  CODE_FOR_avx512vl_blendmv16hf = 2523,
  CODE_FOR_avx512fp16_blendmv8hf = 2524,
  CODE_FOR_avx512bw_blendmv32bf = 2525,
  CODE_FOR_avx512vl_blendmv16bf = 2526,
  CODE_FOR_avx512vl_blendmv8bf = 2527,
  CODE_FOR_movdi_to_sse = 2556,
  CODE_FOR_avx_lddqu256 = 2557,
  CODE_FOR_sse3_lddqu = 2558,
  CODE_FOR_sse2_movntisi = 2559,
  CODE_FOR_sse2_movntidi = 2560,
  CODE_FOR_avx512f_movntv16sf = 2561,
  CODE_FOR_avx_movntv8sf = 2562,
  CODE_FOR_sse_movntv4sf = 2563,
  CODE_FOR_avx512f_movntv8df = 2564,
  CODE_FOR_avx_movntv4df = 2565,
  CODE_FOR_sse2_movntv2df = 2566,
  CODE_FOR_avx512f_movntv8di = 2567,
  CODE_FOR_avx_movntv4di = 2568,
  CODE_FOR_sse2_movntv2di = 2569,
  CODE_FOR_kandqi = 2570,
  CODE_FOR_kiorqi = 2571,
  CODE_FOR_kxorqi = 2572,
  CODE_FOR_kandhi = 2573,
  CODE_FOR_kiorhi = 2574,
  CODE_FOR_kxorhi = 2575,
  CODE_FOR_kandsi = 2576,
  CODE_FOR_kiorsi = 2577,
  CODE_FOR_kxorsi = 2578,
  CODE_FOR_kanddi = 2579,
  CODE_FOR_kiordi = 2580,
  CODE_FOR_kxordi = 2581,
  CODE_FOR_kandnqi = 2582,
  CODE_FOR_kandnhi = 2583,
  CODE_FOR_kandnsi = 2584,
  CODE_FOR_kandndi = 2585,
  CODE_FOR_kxnorqi = 2586,
  CODE_FOR_kxnorhi = 2587,
  CODE_FOR_kxnorsi = 2588,
  CODE_FOR_kxnordi = 2589,
  CODE_FOR_knotqi = 2590,
  CODE_FOR_knothi = 2591,
  CODE_FOR_knotsi = 2592,
  CODE_FOR_knotdi = 2593,
  CODE_FOR_kaddqi = 2595,
  CODE_FOR_kaddhi = 2596,
  CODE_FOR_kaddsi = 2597,
  CODE_FOR_kadddi = 2598,
  CODE_FOR_kashiftqi = 2599,
  CODE_FOR_klshiftrtqi = 2600,
  CODE_FOR_kashifthi = 2601,
  CODE_FOR_klshiftrthi = 2602,
  CODE_FOR_kashiftsi = 2603,
  CODE_FOR_klshiftrtsi = 2604,
  CODE_FOR_kashiftdi = 2605,
  CODE_FOR_klshiftrtdi = 2606,
  CODE_FOR_ktestqi = 2608,
  CODE_FOR_ktesthi = 2609,
  CODE_FOR_ktestsi = 2610,
  CODE_FOR_ktestdi = 2611,
  CODE_FOR_kortestqi_ccc = 2616,
  CODE_FOR_kortesthi_ccc = 2617,
  CODE_FOR_kortestsi_ccc = 2618,
  CODE_FOR_kortestdi_ccc = 2619,
  CODE_FOR_kortestqi_ccz = 2620,
  CODE_FOR_kortesthi_ccz = 2621,
  CODE_FOR_kortestsi_ccz = 2622,
  CODE_FOR_kortestdi_ccz = 2623,
  CODE_FOR_kunpckhi = 2648,
  CODE_FOR_kunpcksi = 2649,
  CODE_FOR_kunpckdi = 2650,
  CODE_FOR_avx512fp16_vmaddv8hf3 = 2759,
  CODE_FOR_avx512fp16_vmaddv8hf3_round = 2760,
  CODE_FOR_avx512fp16_vmaddv8hf3_mask = 2761,
  CODE_FOR_avx512fp16_vmaddv8hf3_mask_round = 2762,
  CODE_FOR_avx512fp16_vmsubv8hf3 = 2763,
  CODE_FOR_avx512fp16_vmsubv8hf3_round = 2764,
  CODE_FOR_avx512fp16_vmsubv8hf3_mask = 2765,
  CODE_FOR_avx512fp16_vmsubv8hf3_mask_round = 2766,
  CODE_FOR_sse_vmaddv4sf3 = 2767,
  CODE_FOR_sse_vmaddv4sf3_round = 2768,
  CODE_FOR_sse_vmaddv4sf3_mask = 2769,
  CODE_FOR_sse_vmaddv4sf3_mask_round = 2770,
  CODE_FOR_sse_vmsubv4sf3 = 2771,
  CODE_FOR_sse_vmsubv4sf3_round = 2772,
  CODE_FOR_sse_vmsubv4sf3_mask = 2773,
  CODE_FOR_sse_vmsubv4sf3_mask_round = 2774,
  CODE_FOR_sse2_vmaddv2df3 = 2775,
  CODE_FOR_sse2_vmaddv2df3_round = 2776,
  CODE_FOR_sse2_vmaddv2df3_mask = 2777,
  CODE_FOR_sse2_vmaddv2df3_mask_round = 2778,
  CODE_FOR_sse2_vmsubv2df3 = 2779,
  CODE_FOR_sse2_vmsubv2df3_round = 2780,
  CODE_FOR_sse2_vmsubv2df3_mask = 2781,
  CODE_FOR_sse2_vmsubv2df3_mask_round = 2782,
  CODE_FOR_avx512fp16_vmmulv8hf3 = 2825,
  CODE_FOR_avx512fp16_vmmulv8hf3_round = 2826,
  CODE_FOR_avx512fp16_vmmulv8hf3_mask = 2827,
  CODE_FOR_avx512fp16_vmmulv8hf3_mask_round = 2828,
  CODE_FOR_avx512fp16_vmdivv8hf3 = 2829,
  CODE_FOR_avx512fp16_vmdivv8hf3_round = 2830,
  CODE_FOR_avx512fp16_vmdivv8hf3_mask = 2831,
  CODE_FOR_avx512fp16_vmdivv8hf3_mask_round = 2832,
  CODE_FOR_sse_vmmulv4sf3 = 2833,
  CODE_FOR_sse_vmmulv4sf3_round = 2834,
  CODE_FOR_sse_vmmulv4sf3_mask = 2835,
  CODE_FOR_sse_vmmulv4sf3_mask_round = 2836,
  CODE_FOR_sse_vmdivv4sf3 = 2837,
  CODE_FOR_sse_vmdivv4sf3_round = 2838,
  CODE_FOR_sse_vmdivv4sf3_mask = 2839,
  CODE_FOR_sse_vmdivv4sf3_mask_round = 2840,
  CODE_FOR_sse2_vmmulv2df3 = 2841,
  CODE_FOR_sse2_vmmulv2df3_round = 2842,
  CODE_FOR_sse2_vmmulv2df3_mask = 2843,
  CODE_FOR_sse2_vmmulv2df3_mask_round = 2844,
  CODE_FOR_sse2_vmdivv2df3 = 2845,
  CODE_FOR_sse2_vmdivv2df3_round = 2846,
  CODE_FOR_sse2_vmdivv2df3_mask = 2847,
  CODE_FOR_sse2_vmdivv2df3_mask_round = 2848,
  CODE_FOR_avx512fp16_divv32hf3 = 2849,
  CODE_FOR_avx512fp16_divv32hf3_round = 2850,
  CODE_FOR_avx512fp16_divv32hf3_mask = 2851,
  CODE_FOR_avx512fp16_divv32hf3_mask_round = 2852,
  CODE_FOR_avx512fp16_divv16hf3 = 2853,
   CODE_FOR_avx512fp16_divv16hf3_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_divv16hf3_mask = 2854,
   CODE_FOR_avx512fp16_divv16hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_divv8hf3 = 2855,
   CODE_FOR_avx512fp16_divv8hf3_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_divv8hf3_mask = 2856,
   CODE_FOR_avx512fp16_divv8hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_divv16sf3 = 2857,
  CODE_FOR_avx512f_divv16sf3_round = 2858,
  CODE_FOR_avx512f_divv16sf3_mask = 2859,
  CODE_FOR_avx512f_divv16sf3_mask_round = 2860,
  CODE_FOR_avx_divv8sf3 = 2861,
   CODE_FOR_avx_divv8sf3_round = CODE_FOR_nothing,
  CODE_FOR_avx_divv8sf3_mask = 2862,
   CODE_FOR_avx_divv8sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_sse_divv4sf3 = 2863,
   CODE_FOR_sse_divv4sf3_round = CODE_FOR_nothing,
  CODE_FOR_sse_divv4sf3_mask = 2864,
   CODE_FOR_sse_divv4sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_divv8df3 = 2865,
  CODE_FOR_avx512f_divv8df3_round = 2866,
  CODE_FOR_avx512f_divv8df3_mask = 2867,
  CODE_FOR_avx512f_divv8df3_mask_round = 2868,
  CODE_FOR_avx_divv4df3 = 2869,
   CODE_FOR_avx_divv4df3_round = CODE_FOR_nothing,
  CODE_FOR_avx_divv4df3_mask = 2870,
   CODE_FOR_avx_divv4df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_sse2_divv2df3 = 2871,
   CODE_FOR_sse2_divv2df3_round = CODE_FOR_nothing,
  CODE_FOR_sse2_divv2df3_mask = 2872,
   CODE_FOR_sse2_divv2df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx_rcpv8sf2 = 2873,
  CODE_FOR_sse_rcpv4sf2 = 2874,
  CODE_FOR_sse_vmrcpv4sf2 = 2875,
  CODE_FOR_avx512fp16_rcpv32hf2 = 2877,
  CODE_FOR_avx512fp16_rcpv32hf2_mask = 2878,
  CODE_FOR_avx512fp16_rcpv16hf2 = 2879,
  CODE_FOR_avx512fp16_rcpv16hf2_mask = 2880,
  CODE_FOR_avx512fp16_rcpv8hf2 = 2881,
  CODE_FOR_avx512fp16_rcpv8hf2_mask = 2882,
  CODE_FOR_avx512fp16_vmrcpv8hf2 = 2883,
  CODE_FOR_avx512fp16_vmrcpv8hf2_mask = 2884,
  CODE_FOR_rcp14v16sf_mask = 2887,
  CODE_FOR_rcp14v8sf_mask = 2889,
  CODE_FOR_rcp14v4sf_mask = 2891,
  CODE_FOR_rcp14v8df_mask = 2893,
  CODE_FOR_rcp14v4df_mask = 2895,
  CODE_FOR_rcp14v2df_mask = 2897,
  CODE_FOR_srcp14v4sf = 2898,
  CODE_FOR_srcp14v2df = 2899,
  CODE_FOR_srcp14v4sf_mask = 2900,
  CODE_FOR_srcp14v2df_mask = 2901,
  CODE_FOR_avx512fp16_sqrtv32hf2 = 2902,
  CODE_FOR_avx512fp16_sqrtv32hf2_round = 2903,
  CODE_FOR_avx512fp16_sqrtv32hf2_mask = 2904,
  CODE_FOR_avx512fp16_sqrtv32hf2_mask_round = 2905,
  CODE_FOR_avx512fp16_sqrtv16hf2 = 2906,
   CODE_FOR_avx512fp16_sqrtv16hf2_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_sqrtv16hf2_mask = 2907,
   CODE_FOR_avx512fp16_sqrtv16hf2_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_sqrtv8hf2 = 2908,
   CODE_FOR_avx512fp16_sqrtv8hf2_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_sqrtv8hf2_mask = 2909,
   CODE_FOR_avx512fp16_sqrtv8hf2_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_sqrtv16sf2 = 2910,
  CODE_FOR_avx512f_sqrtv16sf2_round = 2911,
  CODE_FOR_avx512f_sqrtv16sf2_mask = 2912,
  CODE_FOR_avx512f_sqrtv16sf2_mask_round = 2913,
  CODE_FOR_avx_sqrtv8sf2 = 2914,
   CODE_FOR_avx_sqrtv8sf2_round = CODE_FOR_nothing,
  CODE_FOR_avx_sqrtv8sf2_mask = 2915,
   CODE_FOR_avx_sqrtv8sf2_mask_round = CODE_FOR_nothing,
  CODE_FOR_sse_sqrtv4sf2 = 2916,
   CODE_FOR_sse_sqrtv4sf2_round = CODE_FOR_nothing,
  CODE_FOR_sse_sqrtv4sf2_mask = 2917,
   CODE_FOR_sse_sqrtv4sf2_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_sqrtv8df2 = 2918,
  CODE_FOR_avx512f_sqrtv8df2_round = 2919,
  CODE_FOR_avx512f_sqrtv8df2_mask = 2920,
  CODE_FOR_avx512f_sqrtv8df2_mask_round = 2921,
  CODE_FOR_avx_sqrtv4df2 = 2922,
   CODE_FOR_avx_sqrtv4df2_round = CODE_FOR_nothing,
  CODE_FOR_avx_sqrtv4df2_mask = 2923,
   CODE_FOR_avx_sqrtv4df2_mask_round = CODE_FOR_nothing,
  CODE_FOR_sse2_sqrtv2df2 = 2924,
   CODE_FOR_sse2_sqrtv2df2_round = CODE_FOR_nothing,
  CODE_FOR_sse2_sqrtv2df2_mask = 2925,
   CODE_FOR_sse2_sqrtv2df2_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_vmsqrtv8hf2 = 2926,
  CODE_FOR_avx512fp16_vmsqrtv8hf2_round = 2927,
  CODE_FOR_avx512fp16_vmsqrtv8hf2_mask = 2928,
  CODE_FOR_avx512fp16_vmsqrtv8hf2_mask_round = 2929,
  CODE_FOR_sse_vmsqrtv4sf2 = 2930,
  CODE_FOR_sse_vmsqrtv4sf2_round = 2931,
  CODE_FOR_sse_vmsqrtv4sf2_mask = 2932,
  CODE_FOR_sse_vmsqrtv4sf2_mask_round = 2933,
  CODE_FOR_sse2_vmsqrtv2df2 = 2934,
  CODE_FOR_sse2_vmsqrtv2df2_round = 2935,
  CODE_FOR_sse2_vmsqrtv2df2_mask = 2936,
  CODE_FOR_sse2_vmsqrtv2df2_mask_round = 2937,
  CODE_FOR_avx_rsqrtv8sf2 = 2950,
  CODE_FOR_sse_rsqrtv4sf2 = 2951,
  CODE_FOR_avx512fp16_rsqrtv32hf2 = 2952,
  CODE_FOR_avx512fp16_rsqrtv32hf2_mask = 2953,
  CODE_FOR_avx512fp16_rsqrtv16hf2 = 2954,
  CODE_FOR_avx512fp16_rsqrtv16hf2_mask = 2955,
  CODE_FOR_avx512fp16_rsqrtv8hf2 = 2956,
  CODE_FOR_avx512fp16_rsqrtv8hf2_mask = 2957,
  CODE_FOR_rsqrt14v16sf_mask = 2959,
  CODE_FOR_rsqrt14v8sf_mask = 2961,
  CODE_FOR_rsqrt14v4sf_mask = 2963,
  CODE_FOR_rsqrt14v8df_mask = 2965,
  CODE_FOR_rsqrt14v4df_mask = 2967,
  CODE_FOR_rsqrt14v2df_mask = 2969,
  CODE_FOR_rsqrt14v4sf = 2970,
  CODE_FOR_rsqrt14v2df = 2971,
  CODE_FOR_rsqrt14_v4sf_mask = 2972,
  CODE_FOR_rsqrt14_v2df_mask = 2973,
  CODE_FOR_sse_vmrsqrtv4sf2 = 2974,
  CODE_FOR_avx512fp16_vmrsqrtv8hf2 = 2976,
  CODE_FOR_avx512fp16_vmrsqrtv8hf2_mask = 2977,
  CODE_FOR_ieee_maxv32hf3 = 3041,
  CODE_FOR_ieee_maxv32hf3_round = 3042,
  CODE_FOR_ieee_maxv32hf3_mask = 3043,
  CODE_FOR_ieee_maxv32hf3_mask_round = 3044,
  CODE_FOR_ieee_minv32hf3 = 3045,
  CODE_FOR_ieee_minv32hf3_round = 3046,
  CODE_FOR_ieee_minv32hf3_mask = 3047,
  CODE_FOR_ieee_minv32hf3_mask_round = 3048,
  CODE_FOR_ieee_maxv16hf3 = 3049,
   CODE_FOR_ieee_maxv16hf3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv16hf3_mask = 3050,
   CODE_FOR_ieee_maxv16hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv16hf3 = 3051,
   CODE_FOR_ieee_minv16hf3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv16hf3_mask = 3052,
   CODE_FOR_ieee_minv16hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv8hf3 = 3053,
   CODE_FOR_ieee_maxv8hf3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv8hf3_mask = 3054,
   CODE_FOR_ieee_maxv8hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv8hf3 = 3055,
   CODE_FOR_ieee_minv8hf3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv8hf3_mask = 3056,
   CODE_FOR_ieee_minv8hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv16sf3 = 3057,
  CODE_FOR_ieee_maxv16sf3_round = 3058,
  CODE_FOR_ieee_maxv16sf3_mask = 3059,
  CODE_FOR_ieee_maxv16sf3_mask_round = 3060,
  CODE_FOR_ieee_minv16sf3 = 3061,
  CODE_FOR_ieee_minv16sf3_round = 3062,
  CODE_FOR_ieee_minv16sf3_mask = 3063,
  CODE_FOR_ieee_minv16sf3_mask_round = 3064,
  CODE_FOR_ieee_maxv8sf3 = 3065,
   CODE_FOR_ieee_maxv8sf3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv8sf3_mask = 3066,
   CODE_FOR_ieee_maxv8sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv8sf3 = 3067,
   CODE_FOR_ieee_minv8sf3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv8sf3_mask = 3068,
   CODE_FOR_ieee_minv8sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv4sf3 = 3069,
   CODE_FOR_ieee_maxv4sf3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv4sf3_mask = 3070,
   CODE_FOR_ieee_maxv4sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv4sf3 = 3071,
   CODE_FOR_ieee_minv4sf3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv4sf3_mask = 3072,
   CODE_FOR_ieee_minv4sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv8df3 = 3073,
  CODE_FOR_ieee_maxv8df3_round = 3074,
  CODE_FOR_ieee_maxv8df3_mask = 3075,
  CODE_FOR_ieee_maxv8df3_mask_round = 3076,
  CODE_FOR_ieee_minv8df3 = 3077,
  CODE_FOR_ieee_minv8df3_round = 3078,
  CODE_FOR_ieee_minv8df3_mask = 3079,
  CODE_FOR_ieee_minv8df3_mask_round = 3080,
  CODE_FOR_ieee_maxv4df3 = 3081,
   CODE_FOR_ieee_maxv4df3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv4df3_mask = 3082,
   CODE_FOR_ieee_maxv4df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv4df3 = 3083,
   CODE_FOR_ieee_minv4df3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv4df3_mask = 3084,
   CODE_FOR_ieee_minv4df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv2df3 = 3085,
   CODE_FOR_ieee_maxv2df3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_maxv2df3_mask = 3086,
   CODE_FOR_ieee_maxv2df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv2df3 = 3087,
   CODE_FOR_ieee_minv2df3_round = CODE_FOR_nothing,
  CODE_FOR_ieee_minv2df3_mask = 3088,
   CODE_FOR_ieee_minv2df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_ieee_vmmaxv8hf3 = 3119,
  CODE_FOR_avx512fp16_ieee_vmmaxv8hf3_mask = 3120,
  CODE_FOR_avx512fp16_ieee_vmmaxv8hf3_round = 3121,
  CODE_FOR_avx512fp16_ieee_vmmaxv8hf3_mask_round = 3122,
  CODE_FOR_avx512fp16_ieee_vmminv8hf3 = 3123,
  CODE_FOR_avx512fp16_ieee_vmminv8hf3_mask = 3124,
  CODE_FOR_avx512fp16_ieee_vmminv8hf3_round = 3125,
  CODE_FOR_avx512fp16_ieee_vmminv8hf3_mask_round = 3126,
  CODE_FOR_sse_ieee_vmmaxv4sf3 = 3127,
  CODE_FOR_sse_ieee_vmmaxv4sf3_mask = 3128,
  CODE_FOR_sse_ieee_vmmaxv4sf3_round = 3129,
  CODE_FOR_sse_ieee_vmmaxv4sf3_mask_round = 3130,
  CODE_FOR_sse_ieee_vmminv4sf3 = 3131,
  CODE_FOR_sse_ieee_vmminv4sf3_mask = 3132,
  CODE_FOR_sse_ieee_vmminv4sf3_round = 3133,
  CODE_FOR_sse_ieee_vmminv4sf3_mask_round = 3134,
  CODE_FOR_sse2_ieee_vmmaxv2df3 = 3135,
  CODE_FOR_sse2_ieee_vmmaxv2df3_mask = 3136,
  CODE_FOR_sse2_ieee_vmmaxv2df3_round = 3137,
  CODE_FOR_sse2_ieee_vmmaxv2df3_mask_round = 3138,
  CODE_FOR_sse2_ieee_vmminv2df3 = 3139,
  CODE_FOR_sse2_ieee_vmminv2df3_mask = 3140,
  CODE_FOR_sse2_ieee_vmminv2df3_round = 3141,
  CODE_FOR_sse2_ieee_vmminv2df3_mask_round = 3142,
  CODE_FOR_vec_addsubv8sf3 = 3143,
  CODE_FOR_vec_addsubv4sf3 = 3144,
  CODE_FOR_vec_addsubv4df3 = 3145,
  CODE_FOR_vec_addsubv2df3 = 3146,
  CODE_FOR_avx_haddv4df3 = 3147,
  CODE_FOR_avx_hsubv4df3 = 3148,
  CODE_FOR_sse3_hsubv2df3 = 3150,
  CODE_FOR_avx_haddv8sf3 = 3153,
  CODE_FOR_avx_hsubv8sf3 = 3154,
  CODE_FOR_sse3_haddv4sf3 = 3155,
  CODE_FOR_sse3_hsubv4sf3 = 3156,
  CODE_FOR_reducepv32hf_mask = 3159,
  CODE_FOR_reducepv32hf_mask_round = 3160,
  CODE_FOR_reducepv16hf_mask = 3163,
  CODE_FOR_reducepv16hf_mask_round = 3164,
  CODE_FOR_reducepv8hf_mask = 3167,
  CODE_FOR_reducepv8hf_mask_round = 3168,
  CODE_FOR_reducepv16sf_mask = 3171,
  CODE_FOR_reducepv16sf_mask_round = 3172,
  CODE_FOR_reducepv8sf_mask = 3175,
  CODE_FOR_reducepv8sf_mask_round = 3176,
  CODE_FOR_reducepv4sf_mask = 3179,
  CODE_FOR_reducepv4sf_mask_round = 3180,
  CODE_FOR_reducepv8df_mask = 3183,
  CODE_FOR_reducepv8df_mask_round = 3184,
  CODE_FOR_reducepv4df_mask = 3187,
  CODE_FOR_reducepv4df_mask_round = 3188,
  CODE_FOR_reducepv2df_mask = 3191,
  CODE_FOR_reducepv2df_mask_round = 3192,
  CODE_FOR_reducesv8hf = 3193,
  CODE_FOR_reducesv8hf_mask = 3194,
  CODE_FOR_reducesv8hf_round = 3195,
  CODE_FOR_reducesv8hf_mask_round = 3196,
  CODE_FOR_reducesv4sf = 3197,
  CODE_FOR_reducesv4sf_mask = 3198,
  CODE_FOR_reducesv4sf_round = 3199,
  CODE_FOR_reducesv4sf_mask_round = 3200,
  CODE_FOR_reducesv2df = 3201,
  CODE_FOR_reducesv2df_mask = 3202,
  CODE_FOR_reducesv2df_round = 3203,
  CODE_FOR_reducesv2df_mask_round = 3204,
  CODE_FOR_avx_cmpv8sf3 = 3205,
  CODE_FOR_avx_cmpv4sf3 = 3206,
  CODE_FOR_avx_cmpv4df3 = 3207,
  CODE_FOR_avx_cmpv2df3 = 3208,
  CODE_FOR_avx_vmcmpv4sf3 = 3237,
  CODE_FOR_avx_vmcmpv2df3 = 3238,
  CODE_FOR_avx_maskcmpv8sf3 = 3243,
  CODE_FOR_sse_maskcmpv4sf3 = 3244,
  CODE_FOR_avx_maskcmpv4df3 = 3245,
  CODE_FOR_sse2_maskcmpv2df3 = 3246,
  CODE_FOR_sse_vmmaskcmpv4sf3 = 3247,
  CODE_FOR_sse2_vmmaskcmpv2df3 = 3248,
  CODE_FOR_avx512f_cmpv16si3 = 3249,
  CODE_FOR_avx512f_cmpv16si3_round = 3250,
  CODE_FOR_avx512f_cmpv16si3_mask = 3251,
  CODE_FOR_avx512f_cmpv16si3_mask_round = 3252,
  CODE_FOR_avx512vl_cmpv8si3 = 3253,
   CODE_FOR_avx512vl_cmpv8si3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv8si3_mask = 3254,
   CODE_FOR_avx512vl_cmpv8si3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv4si3 = 3255,
   CODE_FOR_avx512vl_cmpv4si3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv4si3_mask = 3256,
   CODE_FOR_avx512vl_cmpv4si3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_cmpv8di3 = 3257,
  CODE_FOR_avx512f_cmpv8di3_round = 3258,
  CODE_FOR_avx512f_cmpv8di3_mask = 3259,
  CODE_FOR_avx512f_cmpv8di3_mask_round = 3260,
  CODE_FOR_avx512vl_cmpv4di3 = 3261,
   CODE_FOR_avx512vl_cmpv4di3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv4di3_mask = 3262,
   CODE_FOR_avx512vl_cmpv4di3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv2di3 = 3263,
   CODE_FOR_avx512vl_cmpv2di3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv2di3_mask = 3264,
   CODE_FOR_avx512vl_cmpv2di3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_cmpv32hf3 = 3265,
  CODE_FOR_avx512bw_cmpv32hf3_round = 3266,
  CODE_FOR_avx512bw_cmpv32hf3_mask = 3267,
  CODE_FOR_avx512bw_cmpv32hf3_mask_round = 3268,
  CODE_FOR_avx512vl_cmpv16hf3 = 3269,
   CODE_FOR_avx512vl_cmpv16hf3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv16hf3_mask = 3270,
   CODE_FOR_avx512vl_cmpv16hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_cmpv8hf3 = 3271,
   CODE_FOR_avx512fp16_cmpv8hf3_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_cmpv8hf3_mask = 3272,
   CODE_FOR_avx512fp16_cmpv8hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_cmpv16sf3 = 3273,
  CODE_FOR_avx512f_cmpv16sf3_round = 3274,
  CODE_FOR_avx512f_cmpv16sf3_mask = 3275,
  CODE_FOR_avx512f_cmpv16sf3_mask_round = 3276,
  CODE_FOR_avx512vl_cmpv8sf3 = 3277,
   CODE_FOR_avx512vl_cmpv8sf3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv8sf3_mask = 3278,
   CODE_FOR_avx512vl_cmpv8sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv4sf3 = 3279,
   CODE_FOR_avx512vl_cmpv4sf3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv4sf3_mask = 3280,
   CODE_FOR_avx512vl_cmpv4sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_cmpv8df3 = 3281,
  CODE_FOR_avx512f_cmpv8df3_round = 3282,
  CODE_FOR_avx512f_cmpv8df3_mask = 3283,
  CODE_FOR_avx512f_cmpv8df3_mask_round = 3284,
  CODE_FOR_avx512vl_cmpv4df3 = 3285,
   CODE_FOR_avx512vl_cmpv4df3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv4df3_mask = 3286,
   CODE_FOR_avx512vl_cmpv4df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv2df3 = 3287,
   CODE_FOR_avx512vl_cmpv2df3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_cmpv2df3_mask = 3288,
   CODE_FOR_avx512vl_cmpv2df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_cmpv64qi3 = 3394,
  CODE_FOR_avx512bw_cmpv64qi3_mask = 3395,
  CODE_FOR_avx512vl_cmpv16qi3 = 3396,
  CODE_FOR_avx512vl_cmpv16qi3_mask = 3397,
  CODE_FOR_avx512vl_cmpv32qi3 = 3398,
  CODE_FOR_avx512vl_cmpv32qi3_mask = 3399,
  CODE_FOR_avx512bw_cmpv32hi3 = 3400,
  CODE_FOR_avx512bw_cmpv32hi3_mask = 3401,
  CODE_FOR_avx512vl_cmpv16hi3 = 3402,
  CODE_FOR_avx512vl_cmpv16hi3_mask = 3403,
  CODE_FOR_avx512vl_cmpv8hi3 = 3404,
  CODE_FOR_avx512vl_cmpv8hi3_mask = 3405,
  CODE_FOR_avx512bw_ucmpv64qi3 = 3478,
  CODE_FOR_avx512bw_ucmpv64qi3_mask = 3479,
  CODE_FOR_avx512vl_ucmpv16qi3 = 3480,
  CODE_FOR_avx512vl_ucmpv16qi3_mask = 3481,
  CODE_FOR_avx512vl_ucmpv32qi3 = 3482,
  CODE_FOR_avx512vl_ucmpv32qi3_mask = 3483,
  CODE_FOR_avx512bw_ucmpv32hi3 = 3484,
  CODE_FOR_avx512bw_ucmpv32hi3_mask = 3485,
  CODE_FOR_avx512vl_ucmpv16hi3 = 3486,
  CODE_FOR_avx512vl_ucmpv16hi3_mask = 3487,
  CODE_FOR_avx512vl_ucmpv8hi3 = 3488,
  CODE_FOR_avx512vl_ucmpv8hi3_mask = 3489,
  CODE_FOR_avx512f_ucmpv16si3 = 3550,
  CODE_FOR_avx512f_ucmpv16si3_mask = 3551,
  CODE_FOR_avx512vl_ucmpv8si3 = 3552,
  CODE_FOR_avx512vl_ucmpv8si3_mask = 3553,
  CODE_FOR_avx512vl_ucmpv4si3 = 3554,
  CODE_FOR_avx512vl_ucmpv4si3_mask = 3555,
  CODE_FOR_avx512f_ucmpv8di3 = 3556,
  CODE_FOR_avx512f_ucmpv8di3_mask = 3557,
  CODE_FOR_avx512vl_ucmpv4di3 = 3558,
  CODE_FOR_avx512vl_ucmpv4di3_mask = 3559,
  CODE_FOR_avx512vl_ucmpv2di3 = 3560,
  CODE_FOR_avx512vl_ucmpv2di3_mask = 3561,
  CODE_FOR_avx512f_vmcmpv8hf3 = 3616,
  CODE_FOR_avx512f_vmcmpv8hf3_round = 3617,
  CODE_FOR_avx512f_vmcmpv4sf3 = 3618,
  CODE_FOR_avx512f_vmcmpv4sf3_round = 3619,
  CODE_FOR_avx512f_vmcmpv2df3 = 3620,
  CODE_FOR_avx512f_vmcmpv2df3_round = 3621,
  CODE_FOR_avx512f_vmcmpv8hf3_mask = 3622,
  CODE_FOR_avx512f_vmcmpv8hf3_mask_round = 3623,
  CODE_FOR_avx512f_vmcmpv4sf3_mask = 3624,
  CODE_FOR_avx512f_vmcmpv4sf3_mask_round = 3625,
  CODE_FOR_avx512f_vmcmpv2df3_mask = 3626,
  CODE_FOR_avx512f_vmcmpv2df3_mask_round = 3627,
  CODE_FOR_avx10_2_comxhf = 3628,
  CODE_FOR_avx10_2_comxhf_round = 3629,
  CODE_FOR_avx10_2_ucomxhf = 3630,
  CODE_FOR_avx10_2_ucomxhf_round = 3631,
  CODE_FOR_avx10_2_comxsf = 3632,
  CODE_FOR_avx10_2_comxsf_round = 3633,
  CODE_FOR_avx10_2_ucomxsf = 3634,
  CODE_FOR_avx10_2_ucomxsf_round = 3635,
  CODE_FOR_avx10_2_comxdf = 3636,
  CODE_FOR_avx10_2_comxdf_round = 3637,
  CODE_FOR_avx10_2_ucomxdf = 3638,
  CODE_FOR_avx10_2_ucomxdf_round = 3639,
  CODE_FOR_avx512fp16_comi = 3640,
  CODE_FOR_avx512fp16_comi_round = 3641,
  CODE_FOR_avx512fp16_ucomi = 3642,
  CODE_FOR_avx512fp16_ucomi_round = 3643,
  CODE_FOR_sse_comi = 3644,
  CODE_FOR_sse_comi_round = 3645,
  CODE_FOR_sse_ucomi = 3646,
  CODE_FOR_sse_ucomi_round = 3647,
  CODE_FOR_sse2_comi = 3648,
  CODE_FOR_sse2_comi_round = 3649,
  CODE_FOR_sse2_ucomi = 3650,
  CODE_FOR_sse2_ucomi_round = 3651,
  CODE_FOR_avx10_2_comisbf16_v8bf = 3652,
  CODE_FOR_avx512bf16_andnotv16bf3 = 3653,
   CODE_FOR_avx512bf16_andnotv16bf3_mask = CODE_FOR_nothing,
  CODE_FOR_avx512bf16_andnotv8bf3 = 3654,
   CODE_FOR_avx512bf16_andnotv8bf3_mask = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_andnotv16hf3 = 3655,
   CODE_FOR_avx512fp16_andnotv16hf3_mask = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_andnotv8hf3 = 3656,
   CODE_FOR_avx512fp16_andnotv8hf3_mask = CODE_FOR_nothing,
  CODE_FOR_avx_andnotv8sf3 = 3657,
  CODE_FOR_avx_andnotv8sf3_mask = 3658,
  CODE_FOR_sse_andnotv4sf3 = 3659,
  CODE_FOR_sse_andnotv4sf3_mask = 3660,
  CODE_FOR_avx_andnotv4df3 = 3661,
  CODE_FOR_avx_andnotv4df3_mask = 3662,
  CODE_FOR_sse2_andnotv2df3 = 3663,
  CODE_FOR_sse2_andnotv2df3_mask = 3664,
  CODE_FOR_avx512bf16_andnotv32bf3 = 3665,
   CODE_FOR_avx512bf16_andnotv32bf3_mask = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_andnotv32hf3 = 3666,
   CODE_FOR_avx512fp16_andnotv32hf3_mask = CODE_FOR_nothing,
  CODE_FOR_avx512f_andnotv16sf3 = 3667,
  CODE_FOR_avx512f_andnotv16sf3_mask = 3668,
  CODE_FOR_avx512f_andnotv8df3 = 3669,
  CODE_FOR_avx512f_andnotv8df3_mask = 3670,
  CODE_FOR_andbf3 = 3743,
  CODE_FOR_iorbf3 = 3744,
  CODE_FOR_xorbf3 = 3745,
  CODE_FOR_andhf3 = 3746,
  CODE_FOR_iorhf3 = 3747,
  CODE_FOR_xorhf3 = 3748,
  CODE_FOR_andsf3 = 3749,
  CODE_FOR_iorsf3 = 3750,
  CODE_FOR_xorsf3 = 3751,
  CODE_FOR_anddf3 = 3752,
  CODE_FOR_iordf3 = 3753,
  CODE_FOR_xordf3 = 3754,
  CODE_FOR_fma_fmadd_v32hf_maskz_1 = 3765,
  CODE_FOR_fma_fmadd_v32hf_maskz_1_round = 3767,
  CODE_FOR_fma_fmadd_v16hf_maskz_1 = 3769,
   CODE_FOR_fma_fmadd_v16hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmadd_v8hf_maskz_1 = 3771,
   CODE_FOR_fma_fmadd_v8hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmadd_v16sf_maskz_1 = 3775,
  CODE_FOR_fma_fmadd_v16sf_maskz_1_round = 3777,
  CODE_FOR_fma_fmadd_v8sf_maskz_1 = 3779,
   CODE_FOR_fma_fmadd_v8sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmadd_v4sf_maskz_1 = 3781,
   CODE_FOR_fma_fmadd_v4sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmadd_v8df_maskz_1 = 3784,
  CODE_FOR_fma_fmadd_v8df_maskz_1_round = 3786,
  CODE_FOR_fma_fmadd_v4df_maskz_1 = 3788,
   CODE_FOR_fma_fmadd_v4df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmadd_v2df_maskz_1 = 3790,
   CODE_FOR_fma_fmadd_v2df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fmadd_v32hf_mask = 3791,
  CODE_FOR_avx512bw_fmadd_v32hf_mask_round = 3792,
  CODE_FOR_avx512vl_fmadd_v16hf_mask = 3793,
   CODE_FOR_avx512vl_fmadd_v16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fmadd_v8hf_mask = 3794,
   CODE_FOR_avx512fp16_fmadd_v8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fmadd_v16sf_mask = 3795,
  CODE_FOR_avx512f_fmadd_v16sf_mask_round = 3796,
  CODE_FOR_avx512vl_fmadd_v8sf_mask = 3797,
   CODE_FOR_avx512vl_fmadd_v8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fmadd_v4sf_mask = 3798,
   CODE_FOR_avx512vl_fmadd_v4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fmadd_v8df_mask = 3799,
  CODE_FOR_avx512f_fmadd_v8df_mask_round = 3800,
  CODE_FOR_avx512vl_fmadd_v4df_mask = 3801,
   CODE_FOR_avx512vl_fmadd_v4df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fmadd_v2df_mask = 3802,
   CODE_FOR_avx512vl_fmadd_v2df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fmadd_v32hf_mask3 = 3803,
  CODE_FOR_avx512bw_fmadd_v32hf_mask3_round = 3804,
  CODE_FOR_avx512vl_fmadd_v16hf_mask3 = 3805,
  CODE_FOR_avx512vl_fmadd_v16hf_mask3_round = 3806,
  CODE_FOR_avx512fp16_fmadd_v8hf_mask3 = 3807,
  CODE_FOR_avx512fp16_fmadd_v8hf_mask3_round = 3808,
  CODE_FOR_avx512f_fmadd_v16sf_mask3 = 3809,
  CODE_FOR_avx512f_fmadd_v16sf_mask3_round = 3810,
  CODE_FOR_avx512vl_fmadd_v8sf_mask3 = 3811,
  CODE_FOR_avx512vl_fmadd_v8sf_mask3_round = 3812,
  CODE_FOR_avx512vl_fmadd_v4sf_mask3 = 3813,
  CODE_FOR_avx512vl_fmadd_v4sf_mask3_round = 3814,
  CODE_FOR_avx512f_fmadd_v8df_mask3 = 3815,
  CODE_FOR_avx512f_fmadd_v8df_mask3_round = 3816,
  CODE_FOR_avx512vl_fmadd_v4df_mask3 = 3817,
  CODE_FOR_avx512vl_fmadd_v4df_mask3_round = 3818,
  CODE_FOR_avx512vl_fmadd_v2df_mask3 = 3819,
  CODE_FOR_avx512vl_fmadd_v2df_mask3_round = 3820,
  CODE_FOR_fma_fmsub_v32hf_maskz_1 = 3828,
  CODE_FOR_fma_fmsub_v32hf_maskz_1_round = 3830,
  CODE_FOR_fma_fmsub_v16hf_maskz_1 = 3832,
   CODE_FOR_fma_fmsub_v16hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsub_v8hf_maskz_1 = 3834,
   CODE_FOR_fma_fmsub_v8hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsub_v16sf_maskz_1 = 3838,
  CODE_FOR_fma_fmsub_v16sf_maskz_1_round = 3840,
  CODE_FOR_fma_fmsub_v8sf_maskz_1 = 3842,
   CODE_FOR_fma_fmsub_v8sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsub_v4sf_maskz_1 = 3844,
   CODE_FOR_fma_fmsub_v4sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsub_v8df_maskz_1 = 3847,
  CODE_FOR_fma_fmsub_v8df_maskz_1_round = 3849,
  CODE_FOR_fma_fmsub_v4df_maskz_1 = 3851,
   CODE_FOR_fma_fmsub_v4df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsub_v2df_maskz_1 = 3853,
   CODE_FOR_fma_fmsub_v2df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fmsub_v32hf_mask = 3854,
  CODE_FOR_avx512bw_fmsub_v32hf_mask_round = 3855,
  CODE_FOR_avx512vl_fmsub_v16hf_mask = 3856,
  CODE_FOR_avx512vl_fmsub_v16hf_mask_round = 3857,
  CODE_FOR_avx512fp16_fmsub_v8hf_mask = 3858,
  CODE_FOR_avx512fp16_fmsub_v8hf_mask_round = 3859,
  CODE_FOR_avx512f_fmsub_v16sf_mask = 3860,
  CODE_FOR_avx512f_fmsub_v16sf_mask_round = 3861,
  CODE_FOR_avx512vl_fmsub_v8sf_mask = 3862,
  CODE_FOR_avx512vl_fmsub_v8sf_mask_round = 3863,
  CODE_FOR_avx512vl_fmsub_v4sf_mask = 3864,
  CODE_FOR_avx512vl_fmsub_v4sf_mask_round = 3865,
  CODE_FOR_avx512f_fmsub_v8df_mask = 3866,
  CODE_FOR_avx512f_fmsub_v8df_mask_round = 3867,
  CODE_FOR_avx512vl_fmsub_v4df_mask = 3868,
  CODE_FOR_avx512vl_fmsub_v4df_mask_round = 3869,
  CODE_FOR_avx512vl_fmsub_v2df_mask = 3870,
  CODE_FOR_avx512vl_fmsub_v2df_mask_round = 3871,
  CODE_FOR_avx512bw_fmsub_v32hf_mask3 = 3872,
  CODE_FOR_avx512bw_fmsub_v32hf_mask3_round = 3873,
  CODE_FOR_avx512vl_fmsub_v16hf_mask3 = 3874,
   CODE_FOR_avx512vl_fmsub_v16hf_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fmsub_v8hf_mask3 = 3875,
   CODE_FOR_avx512fp16_fmsub_v8hf_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fmsub_v16sf_mask3 = 3876,
  CODE_FOR_avx512f_fmsub_v16sf_mask3_round = 3877,
  CODE_FOR_avx512vl_fmsub_v8sf_mask3 = 3878,
   CODE_FOR_avx512vl_fmsub_v8sf_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fmsub_v4sf_mask3 = 3879,
   CODE_FOR_avx512vl_fmsub_v4sf_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fmsub_v8df_mask3 = 3880,
  CODE_FOR_avx512f_fmsub_v8df_mask3_round = 3881,
  CODE_FOR_avx512vl_fmsub_v4df_mask3 = 3882,
   CODE_FOR_avx512vl_fmsub_v4df_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fmsub_v2df_mask3 = 3883,
   CODE_FOR_avx512vl_fmsub_v2df_mask3_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmadd_v32hf_maskz_1 = 3891,
  CODE_FOR_fma_fnmadd_v32hf_maskz_1_round = 3893,
  CODE_FOR_fma_fnmadd_v16hf_maskz_1 = 3895,
   CODE_FOR_fma_fnmadd_v16hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmadd_v8hf_maskz_1 = 3897,
   CODE_FOR_fma_fnmadd_v8hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmadd_v16sf_maskz_1 = 3901,
  CODE_FOR_fma_fnmadd_v16sf_maskz_1_round = 3903,
  CODE_FOR_fma_fnmadd_v8sf_maskz_1 = 3905,
   CODE_FOR_fma_fnmadd_v8sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmadd_v4sf_maskz_1 = 3907,
   CODE_FOR_fma_fnmadd_v4sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmadd_v8df_maskz_1 = 3910,
  CODE_FOR_fma_fnmadd_v8df_maskz_1_round = 3912,
  CODE_FOR_fma_fnmadd_v4df_maskz_1 = 3914,
   CODE_FOR_fma_fnmadd_v4df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmadd_v2df_maskz_1 = 3916,
   CODE_FOR_fma_fnmadd_v2df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fnmadd_v32hf_mask = 3917,
  CODE_FOR_avx512bw_fnmadd_v32hf_mask_round = 3918,
  CODE_FOR_avx512vl_fnmadd_v16hf_mask = 3919,
   CODE_FOR_avx512vl_fnmadd_v16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fnmadd_v8hf_mask = 3920,
   CODE_FOR_avx512fp16_fnmadd_v8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fnmadd_v16sf_mask = 3921,
  CODE_FOR_avx512f_fnmadd_v16sf_mask_round = 3922,
  CODE_FOR_avx512vl_fnmadd_v8sf_mask = 3923,
   CODE_FOR_avx512vl_fnmadd_v8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fnmadd_v4sf_mask = 3924,
   CODE_FOR_avx512vl_fnmadd_v4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fnmadd_v8df_mask = 3925,
  CODE_FOR_avx512f_fnmadd_v8df_mask_round = 3926,
  CODE_FOR_avx512vl_fnmadd_v4df_mask = 3927,
   CODE_FOR_avx512vl_fnmadd_v4df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fnmadd_v2df_mask = 3928,
   CODE_FOR_avx512vl_fnmadd_v2df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fnmadd_v32hf_mask3 = 3929,
  CODE_FOR_avx512bw_fnmadd_v32hf_mask3_round = 3930,
  CODE_FOR_avx512vl_fnmadd_v16hf_mask3 = 3931,
   CODE_FOR_avx512vl_fnmadd_v16hf_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fnmadd_v8hf_mask3 = 3932,
   CODE_FOR_avx512fp16_fnmadd_v8hf_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fnmadd_v16sf_mask3 = 3933,
  CODE_FOR_avx512f_fnmadd_v16sf_mask3_round = 3934,
  CODE_FOR_avx512vl_fnmadd_v8sf_mask3 = 3935,
   CODE_FOR_avx512vl_fnmadd_v8sf_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fnmadd_v4sf_mask3 = 3936,
   CODE_FOR_avx512vl_fnmadd_v4sf_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fnmadd_v8df_mask3 = 3937,
  CODE_FOR_avx512f_fnmadd_v8df_mask3_round = 3938,
  CODE_FOR_avx512vl_fnmadd_v4df_mask3 = 3939,
   CODE_FOR_avx512vl_fnmadd_v4df_mask3_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fnmadd_v2df_mask3 = 3940,
   CODE_FOR_avx512vl_fnmadd_v2df_mask3_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmsub_v32hf_maskz_1 = 3962,
  CODE_FOR_fma_fnmsub_v32hf_maskz_1_round = 3964,
  CODE_FOR_fma_fnmsub_v16hf_maskz_1 = 3966,
   CODE_FOR_fma_fnmsub_v16hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmsub_v8hf_maskz_1 = 3968,
   CODE_FOR_fma_fnmsub_v8hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmsub_v16sf_maskz_1 = 3972,
  CODE_FOR_fma_fnmsub_v16sf_maskz_1_round = 3974,
  CODE_FOR_fma_fnmsub_v8sf_maskz_1 = 3976,
   CODE_FOR_fma_fnmsub_v8sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmsub_v4sf_maskz_1 = 3978,
   CODE_FOR_fma_fnmsub_v4sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmsub_v8df_maskz_1 = 3981,
  CODE_FOR_fma_fnmsub_v8df_maskz_1_round = 3983,
  CODE_FOR_fma_fnmsub_v4df_maskz_1 = 3985,
   CODE_FOR_fma_fnmsub_v4df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fnmsub_v2df_maskz_1 = 3987,
   CODE_FOR_fma_fnmsub_v2df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fnmsub_v32hf_mask = 3988,
  CODE_FOR_avx512bw_fnmsub_v32hf_mask_round = 3989,
  CODE_FOR_avx512vl_fnmsub_v16hf_mask = 3990,
   CODE_FOR_avx512vl_fnmsub_v16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fnmsub_v8hf_mask = 3991,
   CODE_FOR_avx512fp16_fnmsub_v8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fnmsub_v16sf_mask = 3992,
  CODE_FOR_avx512f_fnmsub_v16sf_mask_round = 3993,
  CODE_FOR_avx512vl_fnmsub_v8sf_mask = 3994,
   CODE_FOR_avx512vl_fnmsub_v8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fnmsub_v4sf_mask = 3995,
   CODE_FOR_avx512vl_fnmsub_v4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512f_fnmsub_v8df_mask = 3996,
  CODE_FOR_avx512f_fnmsub_v8df_mask_round = 3997,
  CODE_FOR_avx512vl_fnmsub_v4df_mask = 3998,
   CODE_FOR_avx512vl_fnmsub_v4df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fnmsub_v2df_mask = 3999,
   CODE_FOR_avx512vl_fnmsub_v2df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fnmsub_v32hf_mask3 = 4000,
  CODE_FOR_avx512bw_fnmsub_v32hf_mask3_round = 4001,
  CODE_FOR_avx512vl_fnmsub_v16hf_mask3 = 4002,
  CODE_FOR_avx512vl_fnmsub_v16hf_mask3_round = 4003,
  CODE_FOR_avx512fp16_fnmsub_v8hf_mask3 = 4004,
  CODE_FOR_avx512fp16_fnmsub_v8hf_mask3_round = 4005,
  CODE_FOR_avx512f_fnmsub_v16sf_mask3 = 4006,
  CODE_FOR_avx512f_fnmsub_v16sf_mask3_round = 4007,
  CODE_FOR_avx512vl_fnmsub_v8sf_mask3 = 4008,
  CODE_FOR_avx512vl_fnmsub_v8sf_mask3_round = 4009,
  CODE_FOR_avx512vl_fnmsub_v4sf_mask3 = 4010,
  CODE_FOR_avx512vl_fnmsub_v4sf_mask3_round = 4011,
  CODE_FOR_avx512f_fnmsub_v8df_mask3 = 4012,
  CODE_FOR_avx512f_fnmsub_v8df_mask3_round = 4013,
  CODE_FOR_avx512vl_fnmsub_v4df_mask3 = 4014,
  CODE_FOR_avx512vl_fnmsub_v4df_mask3_round = 4015,
  CODE_FOR_avx512vl_fnmsub_v2df_mask3 = 4016,
  CODE_FOR_avx512vl_fnmsub_v2df_mask3_round = 4017,
  CODE_FOR_fma_fmaddsub_v32hf_maskz_1 = 4023,
  CODE_FOR_fma_fmaddsub_v32hf_maskz_1_round = 4025,
  CODE_FOR_fma_fmaddsub_v16hf_maskz_1 = 4027,
   CODE_FOR_fma_fmaddsub_v16hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmaddsub_v8hf_maskz_1 = 4029,
   CODE_FOR_fma_fmaddsub_v8hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmaddsub_v16sf_maskz_1 = 4031,
  CODE_FOR_fma_fmaddsub_v16sf_maskz_1_round = 4033,
  CODE_FOR_fma_fmaddsub_v8sf_maskz_1 = 4035,
   CODE_FOR_fma_fmaddsub_v8sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmaddsub_v4sf_maskz_1 = 4037,
   CODE_FOR_fma_fmaddsub_v4sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmaddsub_v8df_maskz_1 = 4039,
  CODE_FOR_fma_fmaddsub_v8df_maskz_1_round = 4041,
  CODE_FOR_fma_fmaddsub_v4df_maskz_1 = 4043,
   CODE_FOR_fma_fmaddsub_v4df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmaddsub_v2df_maskz_1 = 4045,
   CODE_FOR_fma_fmaddsub_v2df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fmaddsub_v32hf_mask = 4046,
  CODE_FOR_avx512bw_fmaddsub_v32hf_mask_round = 4047,
  CODE_FOR_avx512vl_fmaddsub_v16hf_mask = 4048,
  CODE_FOR_avx512vl_fmaddsub_v16hf_mask_round = 4049,
  CODE_FOR_avx512fp16_fmaddsub_v8hf_mask = 4050,
  CODE_FOR_avx512fp16_fmaddsub_v8hf_mask_round = 4051,
  CODE_FOR_avx512f_fmaddsub_v16sf_mask = 4052,
  CODE_FOR_avx512f_fmaddsub_v16sf_mask_round = 4053,
  CODE_FOR_avx512vl_fmaddsub_v8sf_mask = 4054,
  CODE_FOR_avx512vl_fmaddsub_v8sf_mask_round = 4055,
  CODE_FOR_avx512vl_fmaddsub_v4sf_mask = 4056,
  CODE_FOR_avx512vl_fmaddsub_v4sf_mask_round = 4057,
  CODE_FOR_avx512f_fmaddsub_v8df_mask = 4058,
  CODE_FOR_avx512f_fmaddsub_v8df_mask_round = 4059,
  CODE_FOR_avx512vl_fmaddsub_v4df_mask = 4060,
  CODE_FOR_avx512vl_fmaddsub_v4df_mask_round = 4061,
  CODE_FOR_avx512vl_fmaddsub_v2df_mask = 4062,
  CODE_FOR_avx512vl_fmaddsub_v2df_mask_round = 4063,
  CODE_FOR_avx512bw_fmaddsub_v32hf_mask3 = 4064,
  CODE_FOR_avx512bw_fmaddsub_v32hf_mask3_round = 4065,
  CODE_FOR_avx512vl_fmaddsub_v16hf_mask3 = 4066,
  CODE_FOR_avx512vl_fmaddsub_v16hf_mask3_round = 4067,
  CODE_FOR_avx512fp16_fmaddsub_v8hf_mask3 = 4068,
  CODE_FOR_avx512fp16_fmaddsub_v8hf_mask3_round = 4069,
  CODE_FOR_avx512f_fmaddsub_v16sf_mask3 = 4070,
  CODE_FOR_avx512f_fmaddsub_v16sf_mask3_round = 4071,
  CODE_FOR_avx512vl_fmaddsub_v8sf_mask3 = 4072,
  CODE_FOR_avx512vl_fmaddsub_v8sf_mask3_round = 4073,
  CODE_FOR_avx512vl_fmaddsub_v4sf_mask3 = 4074,
  CODE_FOR_avx512vl_fmaddsub_v4sf_mask3_round = 4075,
  CODE_FOR_avx512f_fmaddsub_v8df_mask3 = 4076,
  CODE_FOR_avx512f_fmaddsub_v8df_mask3_round = 4077,
  CODE_FOR_avx512vl_fmaddsub_v4df_mask3 = 4078,
  CODE_FOR_avx512vl_fmaddsub_v4df_mask3_round = 4079,
  CODE_FOR_avx512vl_fmaddsub_v2df_mask3 = 4080,
  CODE_FOR_avx512vl_fmaddsub_v2df_mask3_round = 4081,
  CODE_FOR_fma_fmsubadd_v32hf_maskz_1 = 4087,
  CODE_FOR_fma_fmsubadd_v32hf_maskz_1_round = 4089,
  CODE_FOR_fma_fmsubadd_v16hf_maskz_1 = 4091,
   CODE_FOR_fma_fmsubadd_v16hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsubadd_v8hf_maskz_1 = 4093,
   CODE_FOR_fma_fmsubadd_v8hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsubadd_v16sf_maskz_1 = 4095,
  CODE_FOR_fma_fmsubadd_v16sf_maskz_1_round = 4097,
  CODE_FOR_fma_fmsubadd_v8sf_maskz_1 = 4099,
   CODE_FOR_fma_fmsubadd_v8sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsubadd_v4sf_maskz_1 = 4101,
   CODE_FOR_fma_fmsubadd_v4sf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsubadd_v8df_maskz_1 = 4103,
  CODE_FOR_fma_fmsubadd_v8df_maskz_1_round = 4105,
  CODE_FOR_fma_fmsubadd_v4df_maskz_1 = 4107,
   CODE_FOR_fma_fmsubadd_v4df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmsubadd_v2df_maskz_1 = 4109,
   CODE_FOR_fma_fmsubadd_v2df_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fmsubadd_v32hf_mask = 4110,
  CODE_FOR_avx512bw_fmsubadd_v32hf_mask_round = 4111,
  CODE_FOR_avx512vl_fmsubadd_v16hf_mask = 4112,
  CODE_FOR_avx512vl_fmsubadd_v16hf_mask_round = 4113,
  CODE_FOR_avx512fp16_fmsubadd_v8hf_mask = 4114,
  CODE_FOR_avx512fp16_fmsubadd_v8hf_mask_round = 4115,
  CODE_FOR_avx512f_fmsubadd_v16sf_mask = 4116,
  CODE_FOR_avx512f_fmsubadd_v16sf_mask_round = 4117,
  CODE_FOR_avx512vl_fmsubadd_v8sf_mask = 4118,
  CODE_FOR_avx512vl_fmsubadd_v8sf_mask_round = 4119,
  CODE_FOR_avx512vl_fmsubadd_v4sf_mask = 4120,
  CODE_FOR_avx512vl_fmsubadd_v4sf_mask_round = 4121,
  CODE_FOR_avx512f_fmsubadd_v8df_mask = 4122,
  CODE_FOR_avx512f_fmsubadd_v8df_mask_round = 4123,
  CODE_FOR_avx512vl_fmsubadd_v4df_mask = 4124,
  CODE_FOR_avx512vl_fmsubadd_v4df_mask_round = 4125,
  CODE_FOR_avx512vl_fmsubadd_v2df_mask = 4126,
  CODE_FOR_avx512vl_fmsubadd_v2df_mask_round = 4127,
  CODE_FOR_avx512bw_fmsubadd_v32hf_mask3 = 4128,
  CODE_FOR_avx512bw_fmsubadd_v32hf_mask3_round = 4129,
  CODE_FOR_avx512vl_fmsubadd_v16hf_mask3 = 4130,
  CODE_FOR_avx512vl_fmsubadd_v16hf_mask3_round = 4131,
  CODE_FOR_avx512fp16_fmsubadd_v8hf_mask3 = 4132,
  CODE_FOR_avx512fp16_fmsubadd_v8hf_mask3_round = 4133,
  CODE_FOR_avx512f_fmsubadd_v16sf_mask3 = 4134,
  CODE_FOR_avx512f_fmsubadd_v16sf_mask3_round = 4135,
  CODE_FOR_avx512vl_fmsubadd_v8sf_mask3 = 4136,
  CODE_FOR_avx512vl_fmsubadd_v8sf_mask3_round = 4137,
  CODE_FOR_avx512vl_fmsubadd_v4sf_mask3 = 4138,
  CODE_FOR_avx512vl_fmsubadd_v4sf_mask3_round = 4139,
  CODE_FOR_avx512f_fmsubadd_v8df_mask3 = 4140,
  CODE_FOR_avx512f_fmsubadd_v8df_mask3_round = 4141,
  CODE_FOR_avx512vl_fmsubadd_v4df_mask3 = 4142,
  CODE_FOR_avx512vl_fmsubadd_v4df_mask3_round = 4143,
  CODE_FOR_avx512vl_fmsubadd_v2df_mask3 = 4144,
  CODE_FOR_avx512vl_fmsubadd_v2df_mask3_round = 4145,
  CODE_FOR_avx512f_vmfmadd_v8hf_mask = 4170,
  CODE_FOR_avx512f_vmfmadd_v8hf_mask_round = 4171,
  CODE_FOR_avx512f_vmfmadd_v4sf_mask = 4172,
  CODE_FOR_avx512f_vmfmadd_v4sf_mask_round = 4173,
  CODE_FOR_avx512f_vmfmadd_v2df_mask = 4174,
  CODE_FOR_avx512f_vmfmadd_v2df_mask_round = 4175,
  CODE_FOR_avx512f_vmfmadd_v8hf_mask3 = 4176,
  CODE_FOR_avx512f_vmfmadd_v8hf_mask3_round = 4177,
  CODE_FOR_avx512f_vmfmadd_v4sf_mask3 = 4178,
  CODE_FOR_avx512f_vmfmadd_v4sf_mask3_round = 4179,
  CODE_FOR_avx512f_vmfmadd_v2df_mask3 = 4180,
  CODE_FOR_avx512f_vmfmadd_v2df_mask3_round = 4181,
  CODE_FOR_avx512f_vmfmadd_v8hf_maskz_1 = 4182,
  CODE_FOR_avx512f_vmfmadd_v8hf_maskz_1_round = 4183,
  CODE_FOR_avx512f_vmfmadd_v4sf_maskz_1 = 4184,
  CODE_FOR_avx512f_vmfmadd_v4sf_maskz_1_round = 4185,
  CODE_FOR_avx512f_vmfmadd_v2df_maskz_1 = 4186,
  CODE_FOR_avx512f_vmfmadd_v2df_maskz_1_round = 4187,
  CODE_FOR_avx512f_vmfmsub_v8hf_mask3 = 4194,
  CODE_FOR_avx512f_vmfmsub_v8hf_mask3_round = 4195,
  CODE_FOR_avx512f_vmfmsub_v4sf_mask3 = 4196,
  CODE_FOR_avx512f_vmfmsub_v4sf_mask3_round = 4197,
  CODE_FOR_avx512f_vmfmsub_v2df_mask3 = 4198,
  CODE_FOR_avx512f_vmfmsub_v2df_mask3_round = 4199,
  CODE_FOR_avx512f_vmfnmadd_v8hf_mask = 4206,
  CODE_FOR_avx512f_vmfnmadd_v8hf_mask_round = 4207,
  CODE_FOR_avx512f_vmfnmadd_v4sf_mask = 4208,
  CODE_FOR_avx512f_vmfnmadd_v4sf_mask_round = 4209,
  CODE_FOR_avx512f_vmfnmadd_v2df_mask = 4210,
  CODE_FOR_avx512f_vmfnmadd_v2df_mask_round = 4211,
  CODE_FOR_avx512f_vmfnmadd_v8hf_mask3 = 4212,
  CODE_FOR_avx512f_vmfnmadd_v8hf_mask3_round = 4213,
  CODE_FOR_avx512f_vmfnmadd_v4sf_mask3 = 4214,
  CODE_FOR_avx512f_vmfnmadd_v4sf_mask3_round = 4215,
  CODE_FOR_avx512f_vmfnmadd_v2df_mask3 = 4216,
  CODE_FOR_avx512f_vmfnmadd_v2df_mask3_round = 4217,
  CODE_FOR_avx512f_vmfnmadd_v8hf_maskz_1 = 4218,
  CODE_FOR_avx512f_vmfnmadd_v8hf_maskz_1_round = 4219,
  CODE_FOR_avx512f_vmfnmadd_v4sf_maskz_1 = 4220,
  CODE_FOR_avx512f_vmfnmadd_v4sf_maskz_1_round = 4221,
  CODE_FOR_avx512f_vmfnmadd_v2df_maskz_1 = 4222,
  CODE_FOR_avx512f_vmfnmadd_v2df_maskz_1_round = 4223,
  CODE_FOR_fma_fmaddc_v32hf = 4250,
  CODE_FOR_fma_fmaddc_v32hf_round = 4251,
  CODE_FOR_fma_fmaddc_v32hf_maskz_1 = 4252,
  CODE_FOR_fma_fmaddc_v32hf_maskz_1_round = 4253,
  CODE_FOR_fma_fcmaddc_v32hf = 4254,
  CODE_FOR_fma_fcmaddc_v32hf_round = 4255,
  CODE_FOR_fma_fcmaddc_v32hf_maskz_1 = 4256,
  CODE_FOR_fma_fcmaddc_v32hf_maskz_1_round = 4257,
  CODE_FOR_fma_fmaddc_v16hf = 4258,
   CODE_FOR_fma_fmaddc_v16hf_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmaddc_v16hf_maskz_1 = 4259,
   CODE_FOR_fma_fmaddc_v16hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fcmaddc_v16hf = 4260,
   CODE_FOR_fma_fcmaddc_v16hf_round = CODE_FOR_nothing,
  CODE_FOR_fma_fcmaddc_v16hf_maskz_1 = 4261,
   CODE_FOR_fma_fcmaddc_v16hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmaddc_v8hf = 4262,
   CODE_FOR_fma_fmaddc_v8hf_round = CODE_FOR_nothing,
  CODE_FOR_fma_fmaddc_v8hf_maskz_1 = 4263,
   CODE_FOR_fma_fmaddc_v8hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_fcmaddc_v8hf = 4264,
   CODE_FOR_fma_fcmaddc_v8hf_round = CODE_FOR_nothing,
  CODE_FOR_fma_fcmaddc_v8hf_maskz_1 = 4265,
   CODE_FOR_fma_fcmaddc_v8hf_maskz_1_round = CODE_FOR_nothing,
  CODE_FOR_fma_v32hf_fadd_fmul = 4266,
  CODE_FOR_fma_v16hf_fadd_fmul = 4267,
  CODE_FOR_fma_v8hf_fadd_fmul = 4268,
  CODE_FOR_fma_v32hf_fadd_fcmul = 4269,
  CODE_FOR_fma_v16hf_fadd_fcmul = 4270,
  CODE_FOR_fma_v8hf_fadd_fcmul = 4271,
  CODE_FOR_fma_fmaddc_v32hf_fma_zero = 4272,
  CODE_FOR_fma_fcmaddc_v32hf_fma_zero = 4273,
  CODE_FOR_fma_fmaddc_v16hf_fma_zero = 4274,
  CODE_FOR_fma_fcmaddc_v16hf_fma_zero = 4275,
  CODE_FOR_fma_fmaddc_v8hf_fma_zero = 4276,
  CODE_FOR_fma_fcmaddc_v8hf_fma_zero = 4277,
  CODE_FOR_fma_fmaddc_v16sf_pair = 4278,
  CODE_FOR_fma_fcmaddc_v16sf_pair = 4279,
  CODE_FOR_fma_fmaddc_v8sf_pair = 4280,
  CODE_FOR_fma_fcmaddc_v8sf_pair = 4281,
  CODE_FOR_fma_fmaddc_v4sf_pair = 4282,
  CODE_FOR_fma_fcmaddc_v4sf_pair = 4283,
  CODE_FOR_fma_v32hf_fmaddc_bcst = 4284,
  CODE_FOR_fma_v16hf_fmaddc_bcst = 4285,
  CODE_FOR_fma_v8hf_fmaddc_bcst = 4286,
  CODE_FOR_fma_v32hf_fcmaddc_bcst = 4287,
  CODE_FOR_fma_v16hf_fcmaddc_bcst = 4288,
  CODE_FOR_fma_v8hf_fcmaddc_bcst = 4289,
  CODE_FOR_avx512bw_fmaddc_v32hf_mask = 4290,
  CODE_FOR_avx512bw_fmaddc_v32hf_mask_round = 4291,
  CODE_FOR_avx512bw_fcmaddc_v32hf_mask = 4292,
  CODE_FOR_avx512bw_fcmaddc_v32hf_mask_round = 4293,
  CODE_FOR_avx512vl_fmaddc_v16hf_mask = 4294,
   CODE_FOR_avx512vl_fmaddc_v16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fcmaddc_v16hf_mask = 4295,
   CODE_FOR_avx512vl_fcmaddc_v16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fmaddc_v8hf_mask = 4296,
   CODE_FOR_avx512fp16_fmaddc_v8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fcmaddc_v8hf_mask = 4297,
   CODE_FOR_avx512fp16_fcmaddc_v8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512bw_fmulc_v32hf = 4298,
  CODE_FOR_avx512bw_fmulc_v32hf_mask = 4299,
  CODE_FOR_avx512bw_fmulc_v32hf_round = 4300,
  CODE_FOR_avx512bw_fmulc_v32hf_mask_round = 4301,
  CODE_FOR_avx512bw_fcmulc_v32hf = 4302,
  CODE_FOR_avx512bw_fcmulc_v32hf_mask = 4303,
  CODE_FOR_avx512bw_fcmulc_v32hf_round = 4304,
  CODE_FOR_avx512bw_fcmulc_v32hf_mask_round = 4305,
  CODE_FOR_avx512vl_fmulc_v16hf = 4306,
  CODE_FOR_avx512vl_fmulc_v16hf_mask = 4307,
   CODE_FOR_avx512vl_fmulc_v16hf_round = CODE_FOR_nothing,
   CODE_FOR_avx512vl_fmulc_v16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512vl_fcmulc_v16hf = 4308,
  CODE_FOR_avx512vl_fcmulc_v16hf_mask = 4309,
   CODE_FOR_avx512vl_fcmulc_v16hf_round = CODE_FOR_nothing,
   CODE_FOR_avx512vl_fcmulc_v16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fmulc_v8hf = 4310,
  CODE_FOR_avx512fp16_fmulc_v8hf_mask = 4311,
   CODE_FOR_avx512fp16_fmulc_v8hf_round = CODE_FOR_nothing,
   CODE_FOR_avx512fp16_fmulc_v8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fcmulc_v8hf = 4312,
  CODE_FOR_avx512fp16_fcmulc_v8hf_mask = 4313,
   CODE_FOR_avx512fp16_fcmulc_v8hf_round = CODE_FOR_nothing,
   CODE_FOR_avx512fp16_fcmulc_v8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_fma_fmaddcsh_v8hf = 4314,
  CODE_FOR_avx512fp16_fma_fmaddcsh_v8hf_maskz = 4315,
  CODE_FOR_avx512fp16_fma_fmaddcsh_v8hf_round = 4316,
  CODE_FOR_avx512fp16_fma_fmaddcsh_v8hf_maskz_round = 4317,
  CODE_FOR_avx512fp16_fma_fcmaddcsh_v8hf = 4318,
  CODE_FOR_avx512fp16_fma_fcmaddcsh_v8hf_maskz = 4319,
  CODE_FOR_avx512fp16_fma_fcmaddcsh_v8hf_round = 4320,
  CODE_FOR_avx512fp16_fma_fcmaddcsh_v8hf_maskz_round = 4321,
  CODE_FOR_avx512fp16_fmaddcsh_v8hf_mask = 4322,
  CODE_FOR_avx512fp16_fmaddcsh_v8hf_mask_round = 4323,
  CODE_FOR_avx512fp16_fcmaddcsh_v8hf_mask = 4324,
  CODE_FOR_avx512fp16_fcmaddcsh_v8hf_mask_round = 4325,
  CODE_FOR_avx512fp16_fmulcsh_v8hf = 4326,
  CODE_FOR_avx512fp16_fmulcsh_v8hf_round = 4327,
  CODE_FOR_avx512fp16_fmulcsh_v8hf_mask = 4328,
  CODE_FOR_avx512fp16_fmulcsh_v8hf_mask_round = 4329,
  CODE_FOR_avx512fp16_fcmulcsh_v8hf = 4330,
  CODE_FOR_avx512fp16_fcmulcsh_v8hf_round = 4331,
  CODE_FOR_avx512fp16_fcmulcsh_v8hf_mask = 4332,
  CODE_FOR_avx512fp16_fcmulcsh_v8hf_mask_round = 4333,
  CODE_FOR_avx512fp16_vcvtph2uw_v32hi = 4334,
  CODE_FOR_avx512fp16_vcvtph2uw_v32hi_round = 4335,
  CODE_FOR_avx512fp16_vcvtph2uw_v32hi_mask = 4336,
  CODE_FOR_avx512fp16_vcvtph2uw_v32hi_mask_round = 4337,
  CODE_FOR_avx512fp16_vcvtph2w_v32hi = 4338,
  CODE_FOR_avx512fp16_vcvtph2w_v32hi_round = 4339,
  CODE_FOR_avx512fp16_vcvtph2w_v32hi_mask = 4340,
  CODE_FOR_avx512fp16_vcvtph2w_v32hi_mask_round = 4341,
  CODE_FOR_avx512fp16_vcvtph2udq_v16si = 4342,
  CODE_FOR_avx512fp16_vcvtph2udq_v16si_round = 4343,
  CODE_FOR_avx512fp16_vcvtph2udq_v16si_mask = 4344,
  CODE_FOR_avx512fp16_vcvtph2udq_v16si_mask_round = 4345,
  CODE_FOR_avx512fp16_vcvtph2dq_v16si = 4346,
  CODE_FOR_avx512fp16_vcvtph2dq_v16si_round = 4347,
  CODE_FOR_avx512fp16_vcvtph2dq_v16si_mask = 4348,
  CODE_FOR_avx512fp16_vcvtph2dq_v16si_mask_round = 4349,
  CODE_FOR_avx512fp16_vcvtph2uqq_v8di = 4350,
  CODE_FOR_avx512fp16_vcvtph2uqq_v8di_round = 4351,
  CODE_FOR_avx512fp16_vcvtph2uqq_v8di_mask = 4352,
  CODE_FOR_avx512fp16_vcvtph2uqq_v8di_mask_round = 4353,
  CODE_FOR_avx512fp16_vcvtph2qq_v8di = 4354,
  CODE_FOR_avx512fp16_vcvtph2qq_v8di_round = 4355,
  CODE_FOR_avx512fp16_vcvtph2qq_v8di_mask = 4356,
  CODE_FOR_avx512fp16_vcvtph2qq_v8di_mask_round = 4357,
  CODE_FOR_avx512fp16_vcvtph2uw_v16hi = 4358,
  CODE_FOR_avx512fp16_vcvtph2uw_v16hi_round = 4359,
  CODE_FOR_avx512fp16_vcvtph2uw_v16hi_mask = 4360,
  CODE_FOR_avx512fp16_vcvtph2uw_v16hi_mask_round = 4361,
  CODE_FOR_avx512fp16_vcvtph2w_v16hi = 4362,
  CODE_FOR_avx512fp16_vcvtph2w_v16hi_round = 4363,
  CODE_FOR_avx512fp16_vcvtph2w_v16hi_mask = 4364,
  CODE_FOR_avx512fp16_vcvtph2w_v16hi_mask_round = 4365,
  CODE_FOR_avx512fp16_vcvtph2udq_v8si = 4366,
  CODE_FOR_avx512fp16_vcvtph2udq_v8si_round = 4367,
  CODE_FOR_avx512fp16_vcvtph2udq_v8si_mask = 4368,
  CODE_FOR_avx512fp16_vcvtph2udq_v8si_mask_round = 4369,
  CODE_FOR_avx512fp16_vcvtph2dq_v8si = 4370,
  CODE_FOR_avx512fp16_vcvtph2dq_v8si_round = 4371,
  CODE_FOR_avx512fp16_vcvtph2dq_v8si_mask = 4372,
  CODE_FOR_avx512fp16_vcvtph2dq_v8si_mask_round = 4373,
  CODE_FOR_avx512fp16_vcvtph2uqq_v4di = 4374,
  CODE_FOR_avx512fp16_vcvtph2uqq_v4di_round = 4375,
  CODE_FOR_avx512fp16_vcvtph2uqq_v4di_mask = 4376,
  CODE_FOR_avx512fp16_vcvtph2uqq_v4di_mask_round = 4377,
  CODE_FOR_avx512fp16_vcvtph2qq_v4di = 4378,
  CODE_FOR_avx512fp16_vcvtph2qq_v4di_round = 4379,
  CODE_FOR_avx512fp16_vcvtph2qq_v4di_mask = 4380,
  CODE_FOR_avx512fp16_vcvtph2qq_v4di_mask_round = 4381,
  CODE_FOR_avx512fp16_vcvtph2uw_v8hi = 4382,
  CODE_FOR_avx512fp16_vcvtph2uw_v8hi_round = 4383,
  CODE_FOR_avx512fp16_vcvtph2uw_v8hi_mask = 4384,
  CODE_FOR_avx512fp16_vcvtph2uw_v8hi_mask_round = 4385,
  CODE_FOR_avx512fp16_vcvtph2w_v8hi = 4386,
  CODE_FOR_avx512fp16_vcvtph2w_v8hi_round = 4387,
  CODE_FOR_avx512fp16_vcvtph2w_v8hi_mask = 4388,
  CODE_FOR_avx512fp16_vcvtph2w_v8hi_mask_round = 4389,
  CODE_FOR_avx512fp16_vcvtph2udq_v4si = 4390,
  CODE_FOR_avx512fp16_vcvtph2udq_v4si_round = 4391,
  CODE_FOR_avx512fp16_vcvtph2udq_v4si_mask = 4392,
  CODE_FOR_avx512fp16_vcvtph2udq_v4si_mask_round = 4393,
  CODE_FOR_avx512fp16_vcvtph2dq_v4si = 4394,
  CODE_FOR_avx512fp16_vcvtph2dq_v4si_round = 4395,
  CODE_FOR_avx512fp16_vcvtph2dq_v4si_mask = 4396,
  CODE_FOR_avx512fp16_vcvtph2dq_v4si_mask_round = 4397,
  CODE_FOR_avx512fp16_vcvtph2uqq_v2di = 4398,
  CODE_FOR_avx512fp16_vcvtph2uqq_v2di_round = 4399,
  CODE_FOR_avx512fp16_vcvtph2uqq_v2di_mask = 4400,
  CODE_FOR_avx512fp16_vcvtph2uqq_v2di_mask_round = 4401,
  CODE_FOR_avx512fp16_vcvtph2qq_v2di = 4402,
  CODE_FOR_avx512fp16_vcvtph2qq_v2di_round = 4403,
  CODE_FOR_avx512fp16_vcvtph2qq_v2di_mask = 4404,
  CODE_FOR_avx512fp16_vcvtph2qq_v2di_mask_round = 4405,
  CODE_FOR_avx512fp16_vcvtw2ph_v8hi = 4406,
  CODE_FOR_avx512fp16_vcvtw2ph_v8hi_round = 4407,
  CODE_FOR_avx512fp16_vcvtw2ph_v8hi_mask = 4408,
  CODE_FOR_avx512fp16_vcvtw2ph_v8hi_mask_round = 4409,
  CODE_FOR_avx512fp16_vcvtuw2ph_v8hi = 4410,
  CODE_FOR_avx512fp16_vcvtuw2ph_v8hi_round = 4411,
  CODE_FOR_avx512fp16_vcvtuw2ph_v8hi_mask = 4412,
  CODE_FOR_avx512fp16_vcvtuw2ph_v8hi_mask_round = 4413,
  CODE_FOR_avx512fp16_vcvtw2ph_v16hi = 4414,
  CODE_FOR_avx512fp16_vcvtw2ph_v16hi_round = 4415,
  CODE_FOR_avx512fp16_vcvtw2ph_v16hi_mask = 4416,
  CODE_FOR_avx512fp16_vcvtw2ph_v16hi_mask_round = 4417,
  CODE_FOR_avx512fp16_vcvtuw2ph_v16hi = 4418,
  CODE_FOR_avx512fp16_vcvtuw2ph_v16hi_round = 4419,
  CODE_FOR_avx512fp16_vcvtuw2ph_v16hi_mask = 4420,
  CODE_FOR_avx512fp16_vcvtuw2ph_v16hi_mask_round = 4421,
  CODE_FOR_avx512fp16_vcvtw2ph_v32hi = 4422,
  CODE_FOR_avx512fp16_vcvtw2ph_v32hi_round = 4423,
  CODE_FOR_avx512fp16_vcvtw2ph_v32hi_mask = 4424,
  CODE_FOR_avx512fp16_vcvtw2ph_v32hi_mask_round = 4425,
  CODE_FOR_avx512fp16_vcvtuw2ph_v32hi = 4426,
  CODE_FOR_avx512fp16_vcvtuw2ph_v32hi_round = 4427,
  CODE_FOR_avx512fp16_vcvtuw2ph_v32hi_mask = 4428,
  CODE_FOR_avx512fp16_vcvtuw2ph_v32hi_mask_round = 4429,
  CODE_FOR_avx512fp16_vcvtdq2ph_v8si = 4430,
  CODE_FOR_avx512fp16_vcvtdq2ph_v8si_round = 4431,
  CODE_FOR_avx512fp16_vcvtdq2ph_v8si_mask = 4432,
  CODE_FOR_avx512fp16_vcvtdq2ph_v8si_mask_round = 4433,
  CODE_FOR_avx512fp16_vcvtudq2ph_v8si = 4434,
  CODE_FOR_avx512fp16_vcvtudq2ph_v8si_round = 4435,
  CODE_FOR_avx512fp16_vcvtudq2ph_v8si_mask = 4436,
  CODE_FOR_avx512fp16_vcvtudq2ph_v8si_mask_round = 4437,
  CODE_FOR_avx512fp16_vcvtdq2ph_v16si = 4438,
  CODE_FOR_avx512fp16_vcvtdq2ph_v16si_round = 4439,
  CODE_FOR_avx512fp16_vcvtdq2ph_v16si_mask = 4440,
  CODE_FOR_avx512fp16_vcvtdq2ph_v16si_mask_round = 4441,
  CODE_FOR_avx512fp16_vcvtudq2ph_v16si = 4442,
  CODE_FOR_avx512fp16_vcvtudq2ph_v16si_round = 4443,
  CODE_FOR_avx512fp16_vcvtudq2ph_v16si_mask = 4444,
  CODE_FOR_avx512fp16_vcvtudq2ph_v16si_mask_round = 4445,
  CODE_FOR_avx512fp16_vcvtqq2ph_v8di = 4446,
  CODE_FOR_avx512fp16_vcvtqq2ph_v8di_round = 4447,
  CODE_FOR_avx512fp16_vcvtqq2ph_v8di_mask = 4448,
  CODE_FOR_avx512fp16_vcvtqq2ph_v8di_mask_round = 4449,
  CODE_FOR_avx512fp16_vcvtuqq2ph_v8di = 4450,
  CODE_FOR_avx512fp16_vcvtuqq2ph_v8di_round = 4451,
  CODE_FOR_avx512fp16_vcvtuqq2ph_v8di_mask = 4452,
  CODE_FOR_avx512fp16_vcvtuqq2ph_v8di_mask_round = 4453,
  CODE_FOR_avx512fp16_vcvtsh2usi = 4472,
  CODE_FOR_avx512fp16_vcvtsh2usi_round = 4473,
  CODE_FOR_avx512fp16_vcvtsh2si = 4474,
  CODE_FOR_avx512fp16_vcvtsh2si_round = 4475,
  CODE_FOR_avx512fp16_vcvtsh2usiq = 4476,
  CODE_FOR_avx512fp16_vcvtsh2usiq_round = 4477,
  CODE_FOR_avx512fp16_vcvtsh2siq = 4478,
  CODE_FOR_avx512fp16_vcvtsh2siq_round = 4479,
  CODE_FOR_avx512fp16_vcvtsh2usi_2 = 4480,
  CODE_FOR_avx512fp16_vcvtsh2si_2 = 4481,
  CODE_FOR_avx512fp16_vcvtsh2usiq_2 = 4482,
  CODE_FOR_avx512fp16_vcvtsh2siq_2 = 4483,
  CODE_FOR_avx512fp16_vcvtsi2sh = 4484,
  CODE_FOR_avx512fp16_vcvtsi2sh_round = 4485,
  CODE_FOR_avx512fp16_vcvtusi2sh = 4486,
  CODE_FOR_avx512fp16_vcvtusi2sh_round = 4487,
  CODE_FOR_avx512fp16_vcvtsi2shq = 4488,
  CODE_FOR_avx512fp16_vcvtsi2shq_round = 4489,
  CODE_FOR_avx512fp16_vcvtusi2shq = 4490,
  CODE_FOR_avx512fp16_vcvtusi2shq_round = 4491,
  CODE_FOR_unspec_avx512fp16_fix_truncv8hi2 = 4492,
  CODE_FOR_unspec_avx512fp16_fix_truncv8hi2_round = 4493,
  CODE_FOR_unspec_avx512fp16_fix_truncv8hi2_mask = 4494,
  CODE_FOR_unspec_avx512fp16_fix_truncv8hi2_mask_round = 4495,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8hi2 = 4496,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8hi2_round = 4497,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8hi2_mask = 4498,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8hi2_mask_round = 4499,
  CODE_FOR_unspec_avx512fp16_fix_truncv16hi2 = 4500,
  CODE_FOR_unspec_avx512fp16_fix_truncv16hi2_round = 4501,
  CODE_FOR_unspec_avx512fp16_fix_truncv16hi2_mask = 4502,
  CODE_FOR_unspec_avx512fp16_fix_truncv16hi2_mask_round = 4503,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv16hi2 = 4504,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv16hi2_round = 4505,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv16hi2_mask = 4506,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv16hi2_mask_round = 4507,
  CODE_FOR_unspec_avx512fp16_fix_truncv32hi2 = 4508,
  CODE_FOR_unspec_avx512fp16_fix_truncv32hi2_round = 4509,
  CODE_FOR_unspec_avx512fp16_fix_truncv32hi2_mask = 4510,
  CODE_FOR_unspec_avx512fp16_fix_truncv32hi2_mask_round = 4511,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv32hi2 = 4512,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv32hi2_round = 4513,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv32hi2_mask = 4514,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv32hi2_mask_round = 4515,
  CODE_FOR_unspec_avx512fp16_fix_truncv8si2 = 4516,
  CODE_FOR_unspec_avx512fp16_fix_truncv8si2_round = 4517,
  CODE_FOR_unspec_avx512fp16_fix_truncv8si2_mask = 4518,
  CODE_FOR_unspec_avx512fp16_fix_truncv8si2_mask_round = 4519,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8si2 = 4520,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8si2_round = 4521,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8si2_mask = 4522,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8si2_mask_round = 4523,
  CODE_FOR_unspec_avx512fp16_fix_truncv16si2 = 4524,
  CODE_FOR_unspec_avx512fp16_fix_truncv16si2_round = 4525,
  CODE_FOR_unspec_avx512fp16_fix_truncv16si2_mask = 4526,
  CODE_FOR_unspec_avx512fp16_fix_truncv16si2_mask_round = 4527,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv16si2 = 4528,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv16si2_round = 4529,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv16si2_mask = 4530,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv16si2_mask_round = 4531,
  CODE_FOR_unspec_avx512fp16_fix_truncv8di2 = 4532,
  CODE_FOR_unspec_avx512fp16_fix_truncv8di2_round = 4533,
  CODE_FOR_unspec_avx512fp16_fix_truncv8di2_mask = 4534,
  CODE_FOR_unspec_avx512fp16_fix_truncv8di2_mask_round = 4535,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8di2 = 4536,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8di2_round = 4537,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8di2_mask = 4538,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv8di2_mask_round = 4539,
  CODE_FOR_avx512fp16_fix_truncv8hi2 = 4540,
  CODE_FOR_avx512fp16_fix_truncv8hi2_round = 4541,
  CODE_FOR_avx512fp16_fix_truncv8hi2_mask = 4542,
  CODE_FOR_avx512fp16_fix_truncv8hi2_mask_round = 4543,
  CODE_FOR_avx512fp16_fixuns_truncv8hi2 = 4544,
  CODE_FOR_avx512fp16_fixuns_truncv8hi2_round = 4545,
  CODE_FOR_avx512fp16_fixuns_truncv8hi2_mask = 4546,
  CODE_FOR_avx512fp16_fixuns_truncv8hi2_mask_round = 4547,
  CODE_FOR_avx512fp16_fix_truncv16hi2 = 4548,
  CODE_FOR_avx512fp16_fix_truncv16hi2_round = 4549,
  CODE_FOR_avx512fp16_fix_truncv16hi2_mask = 4550,
  CODE_FOR_avx512fp16_fix_truncv16hi2_mask_round = 4551,
  CODE_FOR_avx512fp16_fixuns_truncv16hi2 = 4552,
  CODE_FOR_avx512fp16_fixuns_truncv16hi2_round = 4553,
  CODE_FOR_avx512fp16_fixuns_truncv16hi2_mask = 4554,
  CODE_FOR_avx512fp16_fixuns_truncv16hi2_mask_round = 4555,
  CODE_FOR_avx512fp16_fix_truncv32hi2 = 4556,
  CODE_FOR_avx512fp16_fix_truncv32hi2_round = 4557,
  CODE_FOR_avx512fp16_fix_truncv32hi2_mask = 4558,
  CODE_FOR_avx512fp16_fix_truncv32hi2_mask_round = 4559,
  CODE_FOR_avx512fp16_fixuns_truncv32hi2 = 4560,
  CODE_FOR_avx512fp16_fixuns_truncv32hi2_round = 4561,
  CODE_FOR_avx512fp16_fixuns_truncv32hi2_mask = 4562,
  CODE_FOR_avx512fp16_fixuns_truncv32hi2_mask_round = 4563,
  CODE_FOR_avx512fp16_fix_truncv8si2 = 4564,
  CODE_FOR_avx512fp16_fix_truncv8si2_round = 4565,
  CODE_FOR_avx512fp16_fix_truncv8si2_mask = 4566,
  CODE_FOR_avx512fp16_fix_truncv8si2_mask_round = 4567,
  CODE_FOR_avx512fp16_fixuns_truncv8si2 = 4568,
  CODE_FOR_avx512fp16_fixuns_truncv8si2_round = 4569,
  CODE_FOR_avx512fp16_fixuns_truncv8si2_mask = 4570,
  CODE_FOR_avx512fp16_fixuns_truncv8si2_mask_round = 4571,
  CODE_FOR_avx512fp16_fix_truncv16si2 = 4572,
  CODE_FOR_avx512fp16_fix_truncv16si2_round = 4573,
  CODE_FOR_avx512fp16_fix_truncv16si2_mask = 4574,
  CODE_FOR_avx512fp16_fix_truncv16si2_mask_round = 4575,
  CODE_FOR_avx512fp16_fixuns_truncv16si2 = 4576,
  CODE_FOR_avx512fp16_fixuns_truncv16si2_round = 4577,
  CODE_FOR_avx512fp16_fixuns_truncv16si2_mask = 4578,
  CODE_FOR_avx512fp16_fixuns_truncv16si2_mask_round = 4579,
  CODE_FOR_avx512fp16_fix_truncv8di2 = 4580,
  CODE_FOR_avx512fp16_fix_truncv8di2_round = 4581,
  CODE_FOR_avx512fp16_fix_truncv8di2_mask = 4582,
  CODE_FOR_avx512fp16_fix_truncv8di2_mask_round = 4583,
  CODE_FOR_avx512fp16_fixuns_truncv8di2 = 4584,
  CODE_FOR_avx512fp16_fixuns_truncv8di2_round = 4585,
  CODE_FOR_avx512fp16_fixuns_truncv8di2_mask = 4586,
  CODE_FOR_avx512fp16_fixuns_truncv8di2_mask_round = 4587,
  CODE_FOR_unspec_avx512fp16_fix_truncv4si2 = 4588,
  CODE_FOR_unspec_avx512fp16_fix_truncv4si2_mask = 4589,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv4si2 = 4590,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv4si2_mask = 4591,
  CODE_FOR_unspec_avx512fp16_fix_truncv4di2 = 4592,
  CODE_FOR_unspec_avx512fp16_fix_truncv4di2_mask = 4593,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv4di2 = 4594,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv4di2_mask = 4595,
  CODE_FOR_avx512fp16_fix_truncv4si2 = 4596,
  CODE_FOR_avx512fp16_fix_truncv4si2_mask = 4597,
  CODE_FOR_avx512fp16_fixuns_truncv4si2 = 4598,
  CODE_FOR_avx512fp16_fixuns_truncv4si2_mask = 4599,
  CODE_FOR_avx512fp16_fix_truncv4di2 = 4600,
  CODE_FOR_avx512fp16_fix_truncv4di2_mask = 4601,
  CODE_FOR_avx512fp16_fixuns_truncv4di2 = 4602,
  CODE_FOR_avx512fp16_fixuns_truncv4di2_mask = 4603,
  CODE_FOR_unspec_avx512fp16_fix_truncv2di2 = 4612,
  CODE_FOR_unspec_avx512fp16_fix_truncv2di2_mask = 4613,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv2di2 = 4614,
  CODE_FOR_unspec_avx512fp16_fixuns_truncv2di2_mask = 4615,
  CODE_FOR_avx512fp16_fix_truncv2di2 = 4616,
  CODE_FOR_avx512fp16_fix_truncv2di2_mask = 4617,
  CODE_FOR_avx512fp16_fixuns_truncv2di2 = 4618,
  CODE_FOR_avx512fp16_fixuns_truncv2di2_mask = 4619,
  CODE_FOR_unspec_avx512fp16_fix_truncsi2 = 4624,
  CODE_FOR_unspec_avx512fp16_fix_truncsi2_round = 4625,
  CODE_FOR_unspec_avx512fp16_fixuns_truncsi2 = 4626,
  CODE_FOR_unspec_avx512fp16_fixuns_truncsi2_round = 4627,
  CODE_FOR_unspec_avx512fp16_fix_truncdi2 = 4628,
  CODE_FOR_unspec_avx512fp16_fix_truncdi2_round = 4629,
  CODE_FOR_unspec_avx512fp16_fixuns_truncdi2 = 4630,
  CODE_FOR_unspec_avx512fp16_fixuns_truncdi2_round = 4631,
  CODE_FOR_avx512fp16_fix_truncsi2 = 4632,
  CODE_FOR_avx512fp16_fix_truncsi2_round = 4633,
  CODE_FOR_avx512fp16_fixuns_truncsi2 = 4634,
  CODE_FOR_avx512fp16_fixuns_truncsi2_round = 4635,
  CODE_FOR_avx512fp16_fix_truncdi2 = 4636,
  CODE_FOR_avx512fp16_fix_truncdi2_round = 4637,
  CODE_FOR_avx512fp16_fixuns_truncdi2 = 4638,
  CODE_FOR_avx512fp16_fixuns_truncdi2_round = 4639,
  CODE_FOR_avx512fp16_fix_truncsi2_mem = 4640,
  CODE_FOR_avx512fp16_fixuns_truncsi2_mem = 4641,
  CODE_FOR_avx512fp16_fix_truncdi2_mem = 4642,
  CODE_FOR_avx512fp16_fixuns_truncdi2_mem = 4643,
  CODE_FOR_avx512fp16_float_extend_phv8df2 = 4644,
  CODE_FOR_avx512fp16_float_extend_phv8df2_round = 4645,
  CODE_FOR_avx512fp16_float_extend_phv8df2_mask = 4646,
  CODE_FOR_avx512fp16_float_extend_phv8df2_mask_round = 4647,
  CODE_FOR_avx512fp16_float_extend_phv16sf2 = 4648,
  CODE_FOR_avx512fp16_float_extend_phv16sf2_round = 4649,
  CODE_FOR_avx512fp16_float_extend_phv16sf2_mask = 4650,
  CODE_FOR_avx512fp16_float_extend_phv16sf2_mask_round = 4651,
  CODE_FOR_avx512fp16_float_extend_phv8sf2 = 4652,
  CODE_FOR_avx512fp16_float_extend_phv8sf2_round = 4653,
  CODE_FOR_avx512fp16_float_extend_phv8sf2_mask = 4654,
  CODE_FOR_avx512fp16_float_extend_phv8sf2_mask_round = 4655,
  CODE_FOR_avx512fp16_float_extend_phv4df2 = 4656,
  CODE_FOR_avx512fp16_float_extend_phv4df2_mask = 4657,
  CODE_FOR_avx512fp16_float_extend_phv4sf2 = 4658,
  CODE_FOR_avx512fp16_float_extend_phv4sf2_mask = 4659,
  CODE_FOR_avx512fp16_float_extend_phv2df2 = 4664,
  CODE_FOR_avx512fp16_float_extend_phv2df2_mask = 4665,
  CODE_FOR_avx512fp16_vcvtpd2ph_v8df = 4668,
  CODE_FOR_avx512fp16_vcvtpd2ph_v8df_round = 4669,
  CODE_FOR_avx512fp16_vcvtpd2ph_v8df_mask = 4670,
  CODE_FOR_avx512fp16_vcvtpd2ph_v8df_mask_round = 4671,
  CODE_FOR_avx512fp16_vcvtps2ph_v16sf = 4672,
  CODE_FOR_avx512fp16_vcvtps2ph_v16sf_round = 4673,
  CODE_FOR_avx512fp16_vcvtps2ph_v16sf_mask = 4674,
  CODE_FOR_avx512fp16_vcvtps2ph_v16sf_mask_round = 4675,
  CODE_FOR_avx512fp16_vcvtps2ph_v8sf = 4676,
  CODE_FOR_avx512fp16_vcvtps2ph_v8sf_round = 4677,
  CODE_FOR_avx512fp16_vcvtps2ph_v8sf_mask = 4678,
  CODE_FOR_avx512fp16_vcvtps2ph_v8sf_mask_round = 4679,
  CODE_FOR_avx512fp16_vcvtsh2sd = 4689,
  CODE_FOR_avx512fp16_vcvtsh2sd_mask = 4690,
  CODE_FOR_avx512fp16_vcvtsh2sd_round = 4691,
  CODE_FOR_avx512fp16_vcvtsh2sd_mask_round = 4692,
  CODE_FOR_avx512fp16_vcvtsh2ss = 4693,
  CODE_FOR_avx512fp16_vcvtsh2ss_mask = 4694,
  CODE_FOR_avx512fp16_vcvtsh2ss_round = 4695,
  CODE_FOR_avx512fp16_vcvtsh2ss_mask_round = 4696,
  CODE_FOR_avx512fp16_vcvtsh2sd_mem = 4697,
  CODE_FOR_avx512fp16_vcvtsh2sd_mask_mem = 4698,
  CODE_FOR_avx512fp16_vcvtsh2ss_mem = 4699,
  CODE_FOR_avx512fp16_vcvtsh2ss_mask_mem = 4700,
  CODE_FOR_avx512fp16_vcvtsd2sh = 4701,
  CODE_FOR_avx512fp16_vcvtsd2sh_round = 4702,
  CODE_FOR_avx512fp16_vcvtsd2sh_mask = 4703,
  CODE_FOR_avx512fp16_vcvtsd2sh_mask_round = 4704,
  CODE_FOR_avx512fp16_vcvtss2sh = 4705,
  CODE_FOR_avx512fp16_vcvtss2sh_round = 4706,
  CODE_FOR_avx512fp16_vcvtss2sh_mask = 4707,
  CODE_FOR_avx512fp16_vcvtss2sh_mask_round = 4708,
  CODE_FOR_avx512fp16_vcvtss2sh_mem = 4709,
  CODE_FOR_avx512fp16_vcvtss2sh_mask_mem = 4710,
  CODE_FOR_avx512fp16_vcvtsd2sh_mem = 4711,
  CODE_FOR_avx512fp16_vcvtsd2sh_mask_mem = 4712,
  CODE_FOR_sse_cvtpi2ps = 4713,
  CODE_FOR_sse_cvtps2pi = 4714,
  CODE_FOR_unspec_sse_cvttps2pi = 4715,
  CODE_FOR_sse_cvttps2pi = 4716,
  CODE_FOR_sse_cvtsi2ss = 4717,
  CODE_FOR_sse_cvtsi2ss_round = 4718,
  CODE_FOR_sse_cvtsi2ssq = 4719,
  CODE_FOR_sse_cvtsi2ssq_round = 4720,
  CODE_FOR_sse_cvtss2si = 4721,
  CODE_FOR_sse_cvtss2si_round = 4722,
  CODE_FOR_sse_cvtss2siq = 4723,
  CODE_FOR_sse_cvtss2siq_round = 4724,
  CODE_FOR_sse_cvtss2si_2 = 4725,
  CODE_FOR_sse_cvtss2siq_2 = 4726,
  CODE_FOR_unspec_sse_cvttss2si = 4727,
  CODE_FOR_unspec_sse_cvttss2si_round = 4728,
  CODE_FOR_unspec_sse_cvttss2siq = 4729,
  CODE_FOR_unspec_sse_cvttss2siq_round = 4730,
  CODE_FOR_sse_cvttss2si = 4731,
  CODE_FOR_sse_cvttss2si_round = 4732,
  CODE_FOR_sse_cvttss2siq = 4733,
  CODE_FOR_sse_cvttss2siq_round = 4734,
  CODE_FOR_cvtusi2ss32 = 4735,
  CODE_FOR_cvtusi2ss32_round = 4736,
  CODE_FOR_cvtusi2sd32 = 4737,
   CODE_FOR_cvtusi2sd32_round = CODE_FOR_nothing,
  CODE_FOR_cvtusi2ss64 = 4738,
  CODE_FOR_cvtusi2ss64_round = 4739,
  CODE_FOR_cvtusi2sd64 = 4740,
  CODE_FOR_cvtusi2sd64_round = 4741,
  CODE_FOR_floatv16siv16sf2 = 4742,
  CODE_FOR_floatv16siv16sf2_round = 4743,
  CODE_FOR_floatv16siv16sf2_mask = 4744,
  CODE_FOR_floatv16siv16sf2_mask_round = 4745,
  CODE_FOR_floatv8siv8sf2 = 4746,
   CODE_FOR_floatv8siv8sf2_round = CODE_FOR_nothing,
  CODE_FOR_floatv8siv8sf2_mask = 4747,
   CODE_FOR_floatv8siv8sf2_mask_round = CODE_FOR_nothing,
  CODE_FOR_floatv4siv4sf2 = 4748,
   CODE_FOR_floatv4siv4sf2_round = CODE_FOR_nothing,
  CODE_FOR_floatv4siv4sf2_mask = 4749,
   CODE_FOR_floatv4siv4sf2_mask_round = CODE_FOR_nothing,
  CODE_FOR_floatunsv16siv16sf2_mask = 4752,
  CODE_FOR_floatunsv16siv16sf2_mask_round = 4753,
  CODE_FOR_floatunsv8siv8sf2_mask = 4756,
  CODE_FOR_floatunsv8siv8sf2_mask_round = 4757,
  CODE_FOR_floatunsv4siv4sf2_mask = 4760,
  CODE_FOR_floatunsv4siv4sf2_mask_round = 4761,
  CODE_FOR_avx_fix_notruncv8sfv8si = 4762,
  CODE_FOR_avx_fix_notruncv8sfv8si_mask = 4763,
  CODE_FOR_sse2_fix_notruncv4sfv4si = 4764,
  CODE_FOR_sse2_fix_notruncv4sfv4si_mask = 4765,
  CODE_FOR_avx512f_fix_notruncv16sfv16si = 4766,
  CODE_FOR_avx512f_fix_notruncv16sfv16si_round = 4767,
  CODE_FOR_avx512f_fix_notruncv16sfv16si_mask = 4768,
  CODE_FOR_avx512f_fix_notruncv16sfv16si_mask_round = 4769,
  CODE_FOR_avx512f_fixuns_notruncv16sfv16si_mask = 4772,
  CODE_FOR_avx512f_fixuns_notruncv16sfv16si_mask_round = 4773,
  CODE_FOR_avx512vl_fixuns_notruncv8sfv8si_mask = 4776,
  CODE_FOR_avx512vl_fixuns_notruncv8sfv8si_mask_round = 4777,
  CODE_FOR_avx512vl_fixuns_notruncv4sfv4si_mask = 4780,
  CODE_FOR_avx512vl_fixuns_notruncv4sfv4si_mask_round = 4781,
  CODE_FOR_avx512dq_cvtps2qqv8di_mask = 4784,
  CODE_FOR_avx512dq_cvtps2qqv8di_mask_round = 4785,
  CODE_FOR_avx512dq_cvtps2qqv4di_mask = 4787,
   CODE_FOR_avx512dq_cvtps2qqv4di_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_cvtps2qqv2di_mask = 4789,
  CODE_FOR_avx512dq_cvtps2uqqv8di_mask = 4792,
  CODE_FOR_avx512dq_cvtps2uqqv8di_mask_round = 4793,
  CODE_FOR_avx512dq_cvtps2uqqv4di_mask = 4795,
   CODE_FOR_avx512dq_cvtps2uqqv4di_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_cvtps2uqqv2di_mask = 4797,
  CODE_FOR_unspec_fix_truncv16sfv16si2 = 4798,
  CODE_FOR_unspec_fix_truncv16sfv16si2_round = 4799,
  CODE_FOR_unspec_fix_truncv16sfv16si2_mask = 4800,
  CODE_FOR_unspec_fix_truncv16sfv16si2_mask_round = 4801,
  CODE_FOR_unspec_fixuns_truncv16sfv16si2 = 4802,
  CODE_FOR_unspec_fixuns_truncv16sfv16si2_round = 4803,
  CODE_FOR_unspec_fixuns_truncv16sfv16si2_mask = 4804,
  CODE_FOR_unspec_fixuns_truncv16sfv16si2_mask_round = 4805,
  CODE_FOR_fix_truncv16sfv16si2 = 4806,
  CODE_FOR_fix_truncv16sfv16si2_round = 4807,
  CODE_FOR_fix_truncv16sfv16si2_mask = 4808,
  CODE_FOR_fix_truncv16sfv16si2_mask_round = 4809,
  CODE_FOR_fixuns_truncv16sfv16si2 = 4810,
  CODE_FOR_fixuns_truncv16sfv16si2_round = 4811,
  CODE_FOR_fixuns_truncv16sfv16si2_mask = 4812,
  CODE_FOR_fixuns_truncv16sfv16si2_mask_round = 4813,
  CODE_FOR_unspec_fix_truncv8sfv8si2 = 4814,
  CODE_FOR_unspec_fix_truncv8sfv8si2_mask = 4815,
  CODE_FOR_fix_truncv8sfv8si2 = 4816,
  CODE_FOR_fix_truncv8sfv8si2_mask = 4817,
  CODE_FOR_unspec_fix_truncv4sfv4si2 = 4818,
  CODE_FOR_unspec_fix_truncv4sfv4si2_mask = 4819,
  CODE_FOR_fix_truncv4sfv4si2 = 4820,
  CODE_FOR_fix_truncv4sfv4si2_mask = 4821,
  CODE_FOR_sse2_cvtpi2pd = 4822,
  CODE_FOR_floatunsv2siv2df2 = 4823,
  CODE_FOR_sse2_cvtpd2pi = 4824,
  CODE_FOR_unspec_sse2_cvttpd2pi = 4825,
  CODE_FOR_sse2_cvttpd2pi = 4826,
  CODE_FOR_unspec_fixuns_truncv2dfv2si2 = 4827,
  CODE_FOR_fixuns_truncv2dfv2si2 = 4828,
  CODE_FOR_sse2_cvtsi2sd = 4829,
  CODE_FOR_sse2_cvtsi2sdq = 4830,
  CODE_FOR_sse2_cvtsi2sdq_round = 4831,
  CODE_FOR_avx512f_vcvtss2usi = 4832,
  CODE_FOR_avx512f_vcvtss2usi_round = 4833,
  CODE_FOR_avx512f_vcvtss2usiq = 4834,
  CODE_FOR_avx512f_vcvtss2usiq_round = 4835,
  CODE_FOR_unspec_avx512f_vcvttss2usi = 4836,
  CODE_FOR_unspec_avx512f_vcvttss2usi_round = 4837,
  CODE_FOR_unspec_avx512f_vcvttss2usiq = 4838,
  CODE_FOR_unspec_avx512f_vcvttss2usiq_round = 4839,
  CODE_FOR_avx512f_vcvttss2usi = 4840,
  CODE_FOR_avx512f_vcvttss2usi_round = 4841,
  CODE_FOR_avx512f_vcvttss2usiq = 4842,
  CODE_FOR_avx512f_vcvttss2usiq_round = 4843,
  CODE_FOR_avx512f_vcvtsd2usi = 4844,
  CODE_FOR_avx512f_vcvtsd2usi_round = 4845,
  CODE_FOR_avx512f_vcvtsd2usiq = 4846,
  CODE_FOR_avx512f_vcvtsd2usiq_round = 4847,
  CODE_FOR_unspec_avx512f_vcvttsd2usi = 4848,
  CODE_FOR_unspec_avx512f_vcvttsd2usi_round = 4849,
  CODE_FOR_unspec_avx512f_vcvttsd2usiq = 4850,
  CODE_FOR_unspec_avx512f_vcvttsd2usiq_round = 4851,
  CODE_FOR_avx512f_vcvttsd2usi = 4852,
  CODE_FOR_avx512f_vcvttsd2usi_round = 4853,
  CODE_FOR_avx512f_vcvttsd2usiq = 4854,
  CODE_FOR_avx512f_vcvttsd2usiq_round = 4855,
  CODE_FOR_sse2_cvtsd2si = 4856,
  CODE_FOR_sse2_cvtsd2si_round = 4857,
  CODE_FOR_sse2_cvtsd2siq = 4858,
  CODE_FOR_sse2_cvtsd2siq_round = 4859,
  CODE_FOR_sse2_cvtsd2si_2 = 4860,
  CODE_FOR_sse2_cvtsd2siq_2 = 4861,
  CODE_FOR_unspec_sse2_cvttsd2si = 4862,
  CODE_FOR_unspec_sse2_cvttsd2si_round = 4863,
  CODE_FOR_unspec_sse2_cvttsd2siq = 4864,
  CODE_FOR_unspec_sse2_cvttsd2siq_round = 4865,
  CODE_FOR_sse2_cvttsd2si = 4866,
  CODE_FOR_sse2_cvttsd2si_round = 4867,
  CODE_FOR_sse2_cvttsd2siq = 4868,
  CODE_FOR_sse2_cvttsd2siq_round = 4869,
  CODE_FOR_floatv8siv8df2 = 4870,
  CODE_FOR_floatv8siv8df2_mask = 4871,
  CODE_FOR_floatv4siv4df2 = 4872,
  CODE_FOR_floatv4siv4df2_mask = 4873,
  CODE_FOR_floatv8div8df2 = 4874,
  CODE_FOR_floatv8div8df2_round = 4875,
  CODE_FOR_floatv8div8df2_mask = 4876,
  CODE_FOR_floatv8div8df2_mask_round = 4877,
  CODE_FOR_floatunsv8div8df2 = 4878,
  CODE_FOR_floatunsv8div8df2_round = 4879,
  CODE_FOR_floatunsv8div8df2_mask = 4880,
  CODE_FOR_floatunsv8div8df2_mask_round = 4881,
  CODE_FOR_floatv4div4df2 = 4882,
  CODE_FOR_floatv4div4df2_round = 4883,
  CODE_FOR_floatv4div4df2_mask = 4884,
  CODE_FOR_floatv4div4df2_mask_round = 4885,
  CODE_FOR_floatunsv4div4df2 = 4886,
  CODE_FOR_floatunsv4div4df2_round = 4887,
  CODE_FOR_floatunsv4div4df2_mask = 4888,
  CODE_FOR_floatunsv4div4df2_mask_round = 4889,
  CODE_FOR_floatv2div2df2 = 4890,
  CODE_FOR_floatv2div2df2_round = 4891,
  CODE_FOR_floatv2div2df2_mask = 4892,
  CODE_FOR_floatv2div2df2_mask_round = 4893,
  CODE_FOR_floatunsv2div2df2 = 4894,
  CODE_FOR_floatunsv2div2df2_round = 4895,
  CODE_FOR_floatunsv2div2df2_mask = 4896,
  CODE_FOR_floatunsv2div2df2_mask_round = 4897,
  CODE_FOR_floatv8div8sf2 = 4898,
  CODE_FOR_floatv8div8sf2_round = 4899,
  CODE_FOR_floatv8div8sf2_mask = 4900,
  CODE_FOR_floatv8div8sf2_mask_round = 4901,
  CODE_FOR_floatunsv8div8sf2 = 4902,
  CODE_FOR_floatunsv8div8sf2_round = 4903,
  CODE_FOR_floatunsv8div8sf2_mask = 4904,
  CODE_FOR_floatunsv8div8sf2_mask_round = 4905,
  CODE_FOR_floatv4div4sf2 = 4906,
   CODE_FOR_floatv4div4sf2_round = CODE_FOR_nothing,
  CODE_FOR_floatv4div4sf2_mask = 4907,
   CODE_FOR_floatv4div4sf2_mask_round = CODE_FOR_nothing,
  CODE_FOR_floatunsv4div4sf2 = 4908,
   CODE_FOR_floatunsv4div4sf2_round = CODE_FOR_nothing,
  CODE_FOR_floatunsv4div4sf2_mask = 4909,
   CODE_FOR_floatunsv4div4sf2_mask_round = CODE_FOR_nothing,
  CODE_FOR_floatunsv8siv8df2 = 4916,
  CODE_FOR_floatunsv8siv8df2_mask = 4917,
  CODE_FOR_floatunsv4siv4df2 = 4918,
  CODE_FOR_floatunsv4siv4df2_mask = 4919,
  CODE_FOR_floatunsv2siv2df2_mask = 4921,
  CODE_FOR_avx512f_cvtdq2pd512_2 = 4922,
  CODE_FOR_avx_cvtdq2pd256_2 = 4923,
  CODE_FOR_sse2_cvtdq2pd = 4924,
  CODE_FOR_sse2_cvtdq2pd_mask = 4925,
  CODE_FOR_avx512f_cvtpd2dq512 = 4926,
  CODE_FOR_avx512f_cvtpd2dq512_round = 4927,
  CODE_FOR_avx512f_cvtpd2dq512_mask = 4928,
  CODE_FOR_avx512f_cvtpd2dq512_mask_round = 4929,
  CODE_FOR_avx_cvtpd2dq256 = 4930,
  CODE_FOR_avx_cvtpd2dq256_mask = 4931,
  CODE_FOR_sse2_cvtpd2dq = 4933,
  CODE_FOR_sse2_cvtpd2dq_mask = 4934,
  CODE_FOR_fixuns_notruncv8dfv8si2 = 4936,
  CODE_FOR_fixuns_notruncv8dfv8si2_round = 4937,
  CODE_FOR_fixuns_notruncv8dfv8si2_mask = 4938,
  CODE_FOR_fixuns_notruncv8dfv8si2_mask_round = 4939,
  CODE_FOR_fixuns_notruncv4dfv4si2 = 4940,
  CODE_FOR_fixuns_notruncv4dfv4si2_round = 4941,
  CODE_FOR_fixuns_notruncv4dfv4si2_mask = 4942,
  CODE_FOR_fixuns_notruncv4dfv4si2_mask_round = 4943,
  CODE_FOR_fixuns_notruncv2dfv2si2 = 4944,
  CODE_FOR_fixuns_notruncv2dfv2si2_mask = 4945,
  CODE_FOR_unspec_fix_truncv8dfv8si2 = 4947,
  CODE_FOR_unspec_fix_truncv8dfv8si2_round = 4948,
  CODE_FOR_unspec_fix_truncv8dfv8si2_mask = 4949,
  CODE_FOR_unspec_fix_truncv8dfv8si2_mask_round = 4950,
  CODE_FOR_unspec_fixuns_truncv8dfv8si2 = 4951,
  CODE_FOR_unspec_fixuns_truncv8dfv8si2_round = 4952,
  CODE_FOR_unspec_fixuns_truncv8dfv8si2_mask = 4953,
  CODE_FOR_unspec_fixuns_truncv8dfv8si2_mask_round = 4954,
  CODE_FOR_fix_truncv8dfv8si2 = 4955,
  CODE_FOR_fix_truncv8dfv8si2_round = 4956,
  CODE_FOR_fix_truncv8dfv8si2_mask = 4957,
  CODE_FOR_fix_truncv8dfv8si2_mask_round = 4958,
  CODE_FOR_fixuns_truncv8dfv8si2 = 4959,
  CODE_FOR_fixuns_truncv8dfv8si2_round = 4960,
  CODE_FOR_fixuns_truncv8dfv8si2_mask = 4961,
  CODE_FOR_fixuns_truncv8dfv8si2_mask_round = 4962,
  CODE_FOR_unspec_fixuns_truncv2dfv2si2_mask = 4965,
  CODE_FOR_fixuns_truncv2dfv2si2_mask = 4966,
  CODE_FOR_unspec_fix_truncv4dfv4si2 = 4968,
  CODE_FOR_unspec_fix_truncv4dfv4si2_mask = 4969,
  CODE_FOR_fix_truncv4dfv4si2 = 4970,
  CODE_FOR_fix_truncv4dfv4si2_mask = 4971,
  CODE_FOR_unspec_fixuns_truncv4dfv4si2 = 4972,
  CODE_FOR_unspec_fixuns_truncv4dfv4si2_mask = 4973,
  CODE_FOR_fixuns_truncv4dfv4si2 = 4974,
  CODE_FOR_fixuns_truncv4dfv4si2_mask = 4975,
  CODE_FOR_unspec_fix_truncv8dfv8di2 = 4976,
  CODE_FOR_unspec_fix_truncv8dfv8di2_round = 4977,
  CODE_FOR_unspec_fix_truncv8dfv8di2_mask = 4978,
  CODE_FOR_unspec_fix_truncv8dfv8di2_mask_round = 4979,
  CODE_FOR_unspec_fixuns_truncv8dfv8di2 = 4980,
  CODE_FOR_unspec_fixuns_truncv8dfv8di2_round = 4981,
  CODE_FOR_unspec_fixuns_truncv8dfv8di2_mask = 4982,
  CODE_FOR_unspec_fixuns_truncv8dfv8di2_mask_round = 4983,
  CODE_FOR_unspec_fix_truncv4dfv4di2 = 4984,
   CODE_FOR_unspec_fix_truncv4dfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fix_truncv4dfv4di2_mask = 4985,
   CODE_FOR_unspec_fix_truncv4dfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fixuns_truncv4dfv4di2 = 4986,
   CODE_FOR_unspec_fixuns_truncv4dfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fixuns_truncv4dfv4di2_mask = 4987,
   CODE_FOR_unspec_fixuns_truncv4dfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fix_truncv2dfv2di2 = 4988,
   CODE_FOR_unspec_fix_truncv2dfv2di2_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fix_truncv2dfv2di2_mask = 4989,
   CODE_FOR_unspec_fix_truncv2dfv2di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fixuns_truncv2dfv2di2 = 4990,
   CODE_FOR_unspec_fixuns_truncv2dfv2di2_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fixuns_truncv2dfv2di2_mask = 4991,
   CODE_FOR_unspec_fixuns_truncv2dfv2di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fix_truncv8dfv8di2 = 4992,
  CODE_FOR_fix_truncv8dfv8di2_round = 4993,
  CODE_FOR_fix_truncv8dfv8di2_mask = 4994,
  CODE_FOR_fix_truncv8dfv8di2_mask_round = 4995,
  CODE_FOR_fixuns_truncv8dfv8di2 = 4996,
  CODE_FOR_fixuns_truncv8dfv8di2_round = 4997,
  CODE_FOR_fixuns_truncv8dfv8di2_mask = 4998,
  CODE_FOR_fixuns_truncv8dfv8di2_mask_round = 4999,
  CODE_FOR_fix_truncv4dfv4di2 = 5000,
   CODE_FOR_fix_truncv4dfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_fix_truncv4dfv4di2_mask = 5001,
   CODE_FOR_fix_truncv4dfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_truncv4dfv4di2 = 5002,
   CODE_FOR_fixuns_truncv4dfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_truncv4dfv4di2_mask = 5003,
   CODE_FOR_fixuns_truncv4dfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fix_truncv2dfv2di2 = 5004,
   CODE_FOR_fix_truncv2dfv2di2_round = CODE_FOR_nothing,
  CODE_FOR_fix_truncv2dfv2di2_mask = 5005,
   CODE_FOR_fix_truncv2dfv2di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_truncv2dfv2di2 = 5006,
   CODE_FOR_fixuns_truncv2dfv2di2_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_truncv2dfv2di2_mask = 5007,
   CODE_FOR_fixuns_truncv2dfv2di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fix_notruncv8dfv8di2 = 5008,
  CODE_FOR_fix_notruncv8dfv8di2_round = 5009,
  CODE_FOR_fix_notruncv8dfv8di2_mask = 5010,
  CODE_FOR_fix_notruncv8dfv8di2_mask_round = 5011,
  CODE_FOR_fix_notruncv4dfv4di2 = 5012,
   CODE_FOR_fix_notruncv4dfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_fix_notruncv4dfv4di2_mask = 5013,
   CODE_FOR_fix_notruncv4dfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fix_notruncv2dfv2di2 = 5014,
   CODE_FOR_fix_notruncv2dfv2di2_round = CODE_FOR_nothing,
  CODE_FOR_fix_notruncv2dfv2di2_mask = 5015,
   CODE_FOR_fix_notruncv2dfv2di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_notruncv8dfv8di2 = 5016,
  CODE_FOR_fixuns_notruncv8dfv8di2_round = 5017,
  CODE_FOR_fixuns_notruncv8dfv8di2_mask = 5018,
  CODE_FOR_fixuns_notruncv8dfv8di2_mask_round = 5019,
  CODE_FOR_fixuns_notruncv4dfv4di2 = 5020,
   CODE_FOR_fixuns_notruncv4dfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_notruncv4dfv4di2_mask = 5021,
   CODE_FOR_fixuns_notruncv4dfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_notruncv2dfv2di2 = 5022,
   CODE_FOR_fixuns_notruncv2dfv2di2_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_notruncv2dfv2di2_mask = 5023,
   CODE_FOR_fixuns_notruncv2dfv2di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fix_truncv8sfv8di2 = 5024,
  CODE_FOR_unspec_fix_truncv8sfv8di2_round = 5025,
  CODE_FOR_unspec_fix_truncv8sfv8di2_mask = 5026,
  CODE_FOR_unspec_fix_truncv8sfv8di2_mask_round = 5027,
  CODE_FOR_unspec_fixuns_truncv8sfv8di2 = 5028,
  CODE_FOR_unspec_fixuns_truncv8sfv8di2_round = 5029,
  CODE_FOR_unspec_fixuns_truncv8sfv8di2_mask = 5030,
  CODE_FOR_unspec_fixuns_truncv8sfv8di2_mask_round = 5031,
  CODE_FOR_unspec_fix_truncv4sfv4di2 = 5032,
   CODE_FOR_unspec_fix_truncv4sfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fix_truncv4sfv4di2_mask = 5033,
   CODE_FOR_unspec_fix_truncv4sfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fixuns_truncv4sfv4di2 = 5034,
   CODE_FOR_unspec_fixuns_truncv4sfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_unspec_fixuns_truncv4sfv4di2_mask = 5035,
   CODE_FOR_unspec_fixuns_truncv4sfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fix_truncv8sfv8di2 = 5036,
  CODE_FOR_fix_truncv8sfv8di2_round = 5037,
  CODE_FOR_fix_truncv8sfv8di2_mask = 5038,
  CODE_FOR_fix_truncv8sfv8di2_mask_round = 5039,
  CODE_FOR_fixuns_truncv8sfv8di2 = 5040,
  CODE_FOR_fixuns_truncv8sfv8di2_round = 5041,
  CODE_FOR_fixuns_truncv8sfv8di2_mask = 5042,
  CODE_FOR_fixuns_truncv8sfv8di2_mask_round = 5043,
  CODE_FOR_fix_truncv4sfv4di2 = 5044,
   CODE_FOR_fix_truncv4sfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_fix_truncv4sfv4di2_mask = 5045,
   CODE_FOR_fix_truncv4sfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_truncv4sfv4di2 = 5046,
   CODE_FOR_fixuns_truncv4sfv4di2_round = CODE_FOR_nothing,
  CODE_FOR_fixuns_truncv4sfv4di2_mask = 5047,
   CODE_FOR_fixuns_truncv4sfv4di2_mask_round = CODE_FOR_nothing,
  CODE_FOR_unspec_avx512dq_fix_truncv2sfv2di2 = 5048,
  CODE_FOR_unspec_avx512dq_fix_truncv2sfv2di2_mask = 5049,
  CODE_FOR_unspec_avx512dq_fixuns_truncv2sfv2di2 = 5050,
  CODE_FOR_unspec_avx512dq_fixuns_truncv2sfv2di2_mask = 5051,
  CODE_FOR_avx512dq_fix_truncv2sfv2di2 = 5052,
  CODE_FOR_avx512dq_fix_truncv2sfv2di2_mask = 5053,
  CODE_FOR_avx512dq_fixuns_truncv2sfv2di2 = 5054,
  CODE_FOR_avx512dq_fixuns_truncv2sfv2di2_mask = 5055,
  CODE_FOR_unspec_fixuns_truncv8sfv8si2_mask = 5057,
  CODE_FOR_unspec_fixuns_truncv4sfv4si2_mask = 5059,
  CODE_FOR_fixuns_truncv8sfv8si2_mask = 5061,
  CODE_FOR_fixuns_truncv4sfv4si2_mask = 5063,
  CODE_FOR_unspec_sse2_cvttpd2dq = 5064,
  CODE_FOR_unspec_sse2_cvttpd2dq_mask = 5065,
  CODE_FOR_sse2_cvttpd2dq = 5066,
  CODE_FOR_sse2_cvttpd2dq_mask = 5067,
  CODE_FOR_sse2_cvtsd2ss = 5069,
  CODE_FOR_sse2_cvtsd2ss_round = 5070,
  CODE_FOR_sse2_cvtsd2ss_mask = 5071,
  CODE_FOR_sse2_cvtsd2ss_mask_round = 5072,
  CODE_FOR_sse2_cvtss2sd = 5074,
  CODE_FOR_sse2_cvtss2sd_round = 5075,
  CODE_FOR_sse2_cvtss2sd_mask = 5076,
  CODE_FOR_sse2_cvtss2sd_mask_round = 5077,
  CODE_FOR_avx512f_cvtpd2ps512_mask = 5081,
  CODE_FOR_avx512f_cvtpd2ps512_mask_round = 5082,
  CODE_FOR_avx_cvtpd2ps256 = 5083,
  CODE_FOR_avx_cvtpd2ps256_mask = 5084,
  CODE_FOR_truncv2dfv2sf2 = 5086,
  CODE_FOR_avx512f_cvtps2pd512 = 5089,
  CODE_FOR_avx512f_cvtps2pd512_round = 5090,
  CODE_FOR_avx512f_cvtps2pd512_mask = 5091,
  CODE_FOR_avx512f_cvtps2pd512_mask_round = 5092,
  CODE_FOR_avx_cvtps2pd256 = 5093,
   CODE_FOR_avx_cvtps2pd256_round = CODE_FOR_nothing,
  CODE_FOR_avx_cvtps2pd256_mask = 5094,
   CODE_FOR_avx_cvtps2pd256_mask_round = CODE_FOR_nothing,
  CODE_FOR_vec_unpacks_lo_v16sf = 5096,
  CODE_FOR_avx512bw_cvtb2maskv64qi = 5097,
  CODE_FOR_avx512vl_cvtb2maskv16qi = 5098,
  CODE_FOR_avx512vl_cvtb2maskv32qi = 5099,
  CODE_FOR_avx512bw_cvtw2maskv32hi = 5100,
  CODE_FOR_avx512vl_cvtw2maskv16hi = 5101,
  CODE_FOR_avx512vl_cvtw2maskv8hi = 5102,
  CODE_FOR_avx512f_cvtd2maskv16si = 5103,
  CODE_FOR_avx512vl_cvtd2maskv8si = 5104,
  CODE_FOR_avx512vl_cvtd2maskv4si = 5105,
  CODE_FOR_avx512f_cvtq2maskv8di = 5106,
  CODE_FOR_avx512vl_cvtq2maskv4di = 5107,
  CODE_FOR_avx512vl_cvtq2maskv2di = 5108,
  CODE_FOR_sse2_cvtps2pd = 5139,
  CODE_FOR_sse2_cvtps2pd_mask = 5140,
  CODE_FOR_sse2_cvtps2pd_1 = 5141,
  CODE_FOR_sse2_cvtps2pd_mask_1 = 5142,
  CODE_FOR_sse_movhlps = 5143,
  CODE_FOR_sse_movlhps = 5144,
  CODE_FOR_sse_movlhps_v8hi = 5145,
  CODE_FOR_sse_movlhps_v8hf = 5146,
  CODE_FOR_sse_movlhps_v8bf = 5147,
  CODE_FOR_avx512f_unpckhps512_mask = 5149,
  CODE_FOR_avx_unpckhps256 = 5150,
  CODE_FOR_avx_unpckhps256_mask = 5151,
  CODE_FOR_vec_interleave_highv4sf = 5152,
  CODE_FOR_vec_interleave_highv4sf_mask = 5153,
  CODE_FOR_avx512f_unpcklps512_mask = 5155,
  CODE_FOR_avx_unpcklps256 = 5156,
  CODE_FOR_avx_unpcklps256_mask = 5157,
  CODE_FOR_unpcklps128_mask = 5158,
  CODE_FOR_vec_interleave_lowv4sf = 5159,
  CODE_FOR_avx_movshdup256 = 5160,
  CODE_FOR_avx_movshdup256_mask = 5161,
  CODE_FOR_sse3_movshdup = 5162,
  CODE_FOR_sse3_movshdup_mask = 5163,
  CODE_FOR_avx512f_movshdup512_mask = 5165,
  CODE_FOR_avx_movsldup256 = 5166,
  CODE_FOR_avx_movsldup256_mask = 5167,
  CODE_FOR_sse3_movsldup = 5168,
  CODE_FOR_sse3_movsldup_mask = 5169,
  CODE_FOR_avx512f_movsldup512_mask = 5171,
  CODE_FOR_avx_shufps256_1 = 5172,
  CODE_FOR_avx_shufps256_1_mask = 5173,
  CODE_FOR_sse_shufps_v4sf_mask = 5174,
  CODE_FOR_sse_shufps_v4si = 5175,
  CODE_FOR_sse_shufps_v4sf = 5176,
  CODE_FOR_sse_storehps = 5177,
  CODE_FOR_sse_loadhps = 5178,
  CODE_FOR_sse_storelps = 5179,
  CODE_FOR_sse_storelps_unalign = 5180,
  CODE_FOR_sse_loadlps = 5181,
  CODE_FOR_sse_movss_v4si = 5182,
  CODE_FOR_sse_movss_v4sf = 5183,
  CODE_FOR_avx2_vec_dupv8sf = 5184,
  CODE_FOR_avx2_vec_dupv4sf = 5185,
  CODE_FOR_avx2_vec_dupv8sf_1 = 5186,
  CODE_FOR_avx512f_vec_dupv16sf_1 = 5187,
  CODE_FOR_avx512f_vec_dupv8df_1 = 5188,
  CODE_FOR_vec_setv4si_0 = 5200,
  CODE_FOR_vec_setv4sf_0 = 5201,
  CODE_FOR_vec_setv8hi_0 = 5202,
  CODE_FOR_vec_setv8hf_0 = 5203,
  CODE_FOR_vec_setv8bf_0 = 5204,
  CODE_FOR_vec_setv16hi_0 = 5208,
  CODE_FOR_vec_setv32hi_0 = 5209,
  CODE_FOR_vec_setv16hf_0 = 5210,
  CODE_FOR_vec_setv32hf_0 = 5211,
  CODE_FOR_vec_setv16bf_0 = 5212,
  CODE_FOR_vec_setv32bf_0 = 5213,
  CODE_FOR_avx512fp16_movv8hi = 5221,
  CODE_FOR_avx512fp16_movv8hf = 5222,
  CODE_FOR_avx512fp16_movv8bf = 5223,
  CODE_FOR_vec_setv8si_0 = 5225,
  CODE_FOR_vec_setv8sf_0 = 5226,
  CODE_FOR_vec_setv16si_0 = 5227,
  CODE_FOR_vec_setv16sf_0 = 5228,
  CODE_FOR_sse4_1_insertps_v4si = 5233,
  CODE_FOR_sse4_1_insertps_v4sf = 5234,
  CODE_FOR_vec_setv2df_0 = 5235,
  CODE_FOR_avx512dq_vextractf64x2_1_mask = 5239,
  CODE_FOR_avx512dq_vextracti64x2_1_mask = 5240,
  CODE_FOR_avx512f_vextractf32x4_1_mask = 5243,
  CODE_FOR_avx512f_vextracti32x4_1_mask = 5244,
  CODE_FOR_vec_extract_lo_v8df_mask = 5247,
  CODE_FOR_vec_extract_lo_v8di_mask = 5248,
  CODE_FOR_vec_extract_lo_v8df = 5249,
  CODE_FOR_vec_extract_lo_v8di = 5250,
  CODE_FOR_vec_extract_hi_v8df_mask = 5251,
  CODE_FOR_vec_extract_hi_v8di_mask = 5252,
  CODE_FOR_vec_extract_hi_v8df = 5253,
  CODE_FOR_vec_extract_hi_v8di = 5254,
  CODE_FOR_vec_extract_hi_v16sf_mask = 5255,
  CODE_FOR_vec_extract_hi_v16si_mask = 5256,
  CODE_FOR_vec_extract_hi_v16sf = 5257,
  CODE_FOR_vec_extract_hi_v16si = 5258,
  CODE_FOR_vec_extract_lo_v16sf_mask = 5259,
  CODE_FOR_vec_extract_lo_v16si_mask = 5260,
  CODE_FOR_vec_extract_lo_v16sf = 5261,
  CODE_FOR_vec_extract_lo_v16si = 5262,
  CODE_FOR_vec_extract_lo_v4di_mask = 5263,
  CODE_FOR_vec_extract_lo_v4df_mask = 5264,
  CODE_FOR_vec_extract_lo_v4di = 5265,
  CODE_FOR_vec_extract_lo_v4df = 5266,
  CODE_FOR_vec_extract_hi_v4di_mask = 5267,
  CODE_FOR_vec_extract_hi_v4df_mask = 5268,
  CODE_FOR_vec_extract_hi_v4di = 5269,
  CODE_FOR_vec_extract_hi_v4df = 5270,
  CODE_FOR_vec_extract_lo_v8si_mask = 5271,
  CODE_FOR_vec_extract_lo_v8sf_mask = 5272,
  CODE_FOR_vec_extract_lo_v8si = 5273,
  CODE_FOR_vec_extract_lo_v8sf = 5274,
  CODE_FOR_vec_extract_hi_v8si_mask = 5275,
  CODE_FOR_vec_extract_hi_v8sf_mask = 5276,
  CODE_FOR_vec_extract_hi_v8si = 5277,
  CODE_FOR_vec_extract_hi_v8sf = 5278,
  CODE_FOR_vec_extract_lo_v32hi = 5279,
  CODE_FOR_vec_extract_lo_v32hf = 5280,
  CODE_FOR_vec_extract_lo_v32bf = 5281,
  CODE_FOR_vec_extract_hi_v32hi = 5282,
  CODE_FOR_vec_extract_hi_v32hf = 5283,
  CODE_FOR_vec_extract_hi_v32bf = 5284,
  CODE_FOR_vec_extract_lo_v16hi = 5285,
  CODE_FOR_vec_extract_lo_v16hf = 5286,
  CODE_FOR_vec_extract_lo_v16bf = 5287,
  CODE_FOR_vec_extract_hi_v16hi = 5288,
  CODE_FOR_vec_extract_hi_v16hf = 5289,
  CODE_FOR_vec_extract_hi_v16bf = 5290,
  CODE_FOR_vec_extract_lo_v64qi = 5291,
  CODE_FOR_vec_extract_hi_v64qi = 5292,
  CODE_FOR_vec_extract_lo_v32qi = 5293,
  CODE_FOR_vec_extract_hi_v32qi = 5294,
  CODE_FOR_avx512f_unpckhpd512_mask = 5304,
  CODE_FOR_avx_unpckhpd256 = 5305,
  CODE_FOR_avx_unpckhpd256_mask = 5306,
  CODE_FOR_avx512vl_unpckhpd128_mask = 5307,
  CODE_FOR_avx512f_movddup512 = 5309,
  CODE_FOR_avx512f_movddup512_mask = 5310,
  CODE_FOR_avx512f_unpcklpd512 = 5311,
  CODE_FOR_avx512f_unpcklpd512_mask = 5312,
  CODE_FOR_avx_movddup256 = 5313,
  CODE_FOR_avx_movddup256_mask = 5314,
  CODE_FOR_avx_unpcklpd256 = 5315,
  CODE_FOR_avx_unpcklpd256_mask = 5316,
  CODE_FOR_avx512vl_unpcklpd128_mask = 5317,
  CODE_FOR_avx512f_vmscalefv8hf = 5319,
  CODE_FOR_avx512f_vmscalefv8hf_round = 5320,
  CODE_FOR_avx512f_vmscalefv8hf_mask = 5321,
  CODE_FOR_avx512f_vmscalefv8hf_mask_round = 5322,
  CODE_FOR_avx512f_vmscalefv4sf = 5323,
  CODE_FOR_avx512f_vmscalefv4sf_round = 5324,
  CODE_FOR_avx512f_vmscalefv4sf_mask = 5325,
  CODE_FOR_avx512f_vmscalefv4sf_mask_round = 5326,
  CODE_FOR_avx512f_vmscalefv2df = 5327,
  CODE_FOR_avx512f_vmscalefv2df_round = 5328,
  CODE_FOR_avx512f_vmscalefv2df_mask = 5329,
  CODE_FOR_avx512f_vmscalefv2df_mask_round = 5330,
  CODE_FOR_avx512bw_scalefv32hf = 5331,
  CODE_FOR_avx512bw_scalefv32hf_round = 5332,
  CODE_FOR_avx512bw_scalefv32hf_mask = 5333,
  CODE_FOR_avx512bw_scalefv32hf_mask_round = 5334,
  CODE_FOR_avx512vl_scalefv16hf = 5335,
  CODE_FOR_avx512vl_scalefv16hf_round = 5336,
  CODE_FOR_avx512vl_scalefv16hf_mask = 5337,
  CODE_FOR_avx512vl_scalefv16hf_mask_round = 5338,
  CODE_FOR_avx512fp16_scalefv8hf = 5339,
  CODE_FOR_avx512fp16_scalefv8hf_round = 5340,
  CODE_FOR_avx512fp16_scalefv8hf_mask = 5341,
  CODE_FOR_avx512fp16_scalefv8hf_mask_round = 5342,
  CODE_FOR_avx512f_scalefv16sf = 5343,
  CODE_FOR_avx512f_scalefv16sf_round = 5344,
  CODE_FOR_avx512f_scalefv16sf_mask = 5345,
  CODE_FOR_avx512f_scalefv16sf_mask_round = 5346,
  CODE_FOR_avx512vl_scalefv8sf = 5347,
  CODE_FOR_avx512vl_scalefv8sf_round = 5348,
  CODE_FOR_avx512vl_scalefv8sf_mask = 5349,
  CODE_FOR_avx512vl_scalefv8sf_mask_round = 5350,
  CODE_FOR_avx512vl_scalefv4sf = 5351,
  CODE_FOR_avx512vl_scalefv4sf_round = 5352,
  CODE_FOR_avx512vl_scalefv4sf_mask = 5353,
  CODE_FOR_avx512vl_scalefv4sf_mask_round = 5354,
  CODE_FOR_avx512f_scalefv8df = 5355,
  CODE_FOR_avx512f_scalefv8df_round = 5356,
  CODE_FOR_avx512f_scalefv8df_mask = 5357,
  CODE_FOR_avx512f_scalefv8df_mask_round = 5358,
  CODE_FOR_avx512vl_scalefv4df = 5359,
  CODE_FOR_avx512vl_scalefv4df_round = 5360,
  CODE_FOR_avx512vl_scalefv4df_mask = 5361,
  CODE_FOR_avx512vl_scalefv4df_mask_round = 5362,
  CODE_FOR_avx512vl_scalefv2df = 5363,
  CODE_FOR_avx512vl_scalefv2df_round = 5364,
  CODE_FOR_avx512vl_scalefv2df_mask = 5365,
  CODE_FOR_avx512vl_scalefv2df_mask_round = 5366,
  CODE_FOR_avx512f_vternlogv16si = 5367,
  CODE_FOR_avx512f_vternlogv16si_maskz_1 = 5368,
  CODE_FOR_avx512vl_vternlogv8si = 5369,
  CODE_FOR_avx512vl_vternlogv8si_maskz_1 = 5370,
  CODE_FOR_avx512vl_vternlogv4si = 5371,
  CODE_FOR_avx512vl_vternlogv4si_maskz_1 = 5372,
  CODE_FOR_avx512f_vternlogv8di = 5373,
  CODE_FOR_avx512f_vternlogv8di_maskz_1 = 5374,
  CODE_FOR_avx512vl_vternlogv4di = 5375,
  CODE_FOR_avx512vl_vternlogv4di_maskz_1 = 5376,
  CODE_FOR_avx512vl_vternlogv2di = 5377,
  CODE_FOR_avx512vl_vternlogv2di_maskz_1 = 5378,
  CODE_FOR_avx512bw_getexpv32hf = 6945,
  CODE_FOR_avx512bw_getexpv32hf_round = 6946,
  CODE_FOR_avx512bw_getexpv32hf_mask = 6947,
  CODE_FOR_avx512bw_getexpv32hf_mask_round = 6948,
  CODE_FOR_avx512vl_getexpv16hf = 6949,
  CODE_FOR_avx512vl_getexpv16hf_round = 6950,
  CODE_FOR_avx512vl_getexpv16hf_mask = 6951,
  CODE_FOR_avx512vl_getexpv16hf_mask_round = 6952,
  CODE_FOR_avx512fp16_getexpv8hf = 6953,
  CODE_FOR_avx512fp16_getexpv8hf_round = 6954,
  CODE_FOR_avx512fp16_getexpv8hf_mask = 6955,
  CODE_FOR_avx512fp16_getexpv8hf_mask_round = 6956,
  CODE_FOR_avx512f_getexpv16sf = 6957,
  CODE_FOR_avx512f_getexpv16sf_round = 6958,
  CODE_FOR_avx512f_getexpv16sf_mask = 6959,
  CODE_FOR_avx512f_getexpv16sf_mask_round = 6960,
  CODE_FOR_avx512vl_getexpv8sf = 6961,
  CODE_FOR_avx512vl_getexpv8sf_round = 6962,
  CODE_FOR_avx512vl_getexpv8sf_mask = 6963,
  CODE_FOR_avx512vl_getexpv8sf_mask_round = 6964,
  CODE_FOR_avx512vl_getexpv4sf = 6965,
  CODE_FOR_avx512vl_getexpv4sf_round = 6966,
  CODE_FOR_avx512vl_getexpv4sf_mask = 6967,
  CODE_FOR_avx512vl_getexpv4sf_mask_round = 6968,
  CODE_FOR_avx512f_getexpv8df = 6969,
  CODE_FOR_avx512f_getexpv8df_round = 6970,
  CODE_FOR_avx512f_getexpv8df_mask = 6971,
  CODE_FOR_avx512f_getexpv8df_mask_round = 6972,
  CODE_FOR_avx512vl_getexpv4df = 6973,
  CODE_FOR_avx512vl_getexpv4df_round = 6974,
  CODE_FOR_avx512vl_getexpv4df_mask = 6975,
  CODE_FOR_avx512vl_getexpv4df_mask_round = 6976,
  CODE_FOR_avx512vl_getexpv2df = 6977,
  CODE_FOR_avx512vl_getexpv2df_round = 6978,
  CODE_FOR_avx512vl_getexpv2df_mask = 6979,
  CODE_FOR_avx512vl_getexpv2df_mask_round = 6980,
  CODE_FOR_avx512f_sgetexpv8hf = 6981,
  CODE_FOR_avx512f_sgetexpv8hf_mask = 6982,
  CODE_FOR_avx512f_sgetexpv8hf_round = 6983,
  CODE_FOR_avx512f_sgetexpv8hf_mask_round = 6984,
  CODE_FOR_avx512f_sgetexpv4sf = 6985,
  CODE_FOR_avx512f_sgetexpv4sf_mask = 6986,
  CODE_FOR_avx512f_sgetexpv4sf_round = 6987,
  CODE_FOR_avx512f_sgetexpv4sf_mask_round = 6988,
  CODE_FOR_avx512f_sgetexpv2df = 6989,
  CODE_FOR_avx512f_sgetexpv2df_mask = 6990,
  CODE_FOR_avx512f_sgetexpv2df_round = 6991,
  CODE_FOR_avx512f_sgetexpv2df_mask_round = 6992,
  CODE_FOR_avx512f_alignv16si_mask = 6994,
  CODE_FOR_avx512vl_alignv8si_mask = 6996,
  CODE_FOR_avx512vl_alignv4si_mask = 6998,
  CODE_FOR_avx512f_alignv8di_mask = 7000,
  CODE_FOR_avx512vl_alignv4di_mask = 7002,
  CODE_FOR_avx512vl_alignv2di_mask = 7004,
  CODE_FOR_avx512f_fixupimmv16sf = 7013,
  CODE_FOR_avx512f_fixupimmv16sf_round = 7014,
  CODE_FOR_avx512f_fixupimmv16sf_maskz_1 = 7015,
  CODE_FOR_avx512f_fixupimmv16sf_maskz_1_round = 7016,
  CODE_FOR_avx512vl_fixupimmv8sf = 7017,
  CODE_FOR_avx512vl_fixupimmv8sf_round = 7018,
  CODE_FOR_avx512vl_fixupimmv8sf_maskz_1 = 7019,
  CODE_FOR_avx512vl_fixupimmv8sf_maskz_1_round = 7020,
  CODE_FOR_avx512vl_fixupimmv4sf = 7021,
  CODE_FOR_avx512vl_fixupimmv4sf_round = 7022,
  CODE_FOR_avx512vl_fixupimmv4sf_maskz_1 = 7023,
  CODE_FOR_avx512vl_fixupimmv4sf_maskz_1_round = 7024,
  CODE_FOR_avx512f_fixupimmv8df = 7025,
  CODE_FOR_avx512f_fixupimmv8df_round = 7026,
  CODE_FOR_avx512f_fixupimmv8df_maskz_1 = 7027,
  CODE_FOR_avx512f_fixupimmv8df_maskz_1_round = 7028,
  CODE_FOR_avx512vl_fixupimmv4df = 7029,
  CODE_FOR_avx512vl_fixupimmv4df_round = 7030,
  CODE_FOR_avx512vl_fixupimmv4df_maskz_1 = 7031,
  CODE_FOR_avx512vl_fixupimmv4df_maskz_1_round = 7032,
  CODE_FOR_avx512vl_fixupimmv2df = 7033,
  CODE_FOR_avx512vl_fixupimmv2df_round = 7034,
  CODE_FOR_avx512vl_fixupimmv2df_maskz_1 = 7035,
  CODE_FOR_avx512vl_fixupimmv2df_maskz_1_round = 7036,
  CODE_FOR_avx512f_fixupimmv16sf_mask = 7037,
  CODE_FOR_avx512f_fixupimmv16sf_mask_round = 7038,
  CODE_FOR_avx512vl_fixupimmv8sf_mask = 7039,
  CODE_FOR_avx512vl_fixupimmv8sf_mask_round = 7040,
  CODE_FOR_avx512vl_fixupimmv4sf_mask = 7041,
  CODE_FOR_avx512vl_fixupimmv4sf_mask_round = 7042,
  CODE_FOR_avx512f_fixupimmv8df_mask = 7043,
  CODE_FOR_avx512f_fixupimmv8df_mask_round = 7044,
  CODE_FOR_avx512vl_fixupimmv4df_mask = 7045,
  CODE_FOR_avx512vl_fixupimmv4df_mask_round = 7046,
  CODE_FOR_avx512vl_fixupimmv2df_mask = 7047,
  CODE_FOR_avx512vl_fixupimmv2df_mask_round = 7048,
  CODE_FOR_avx512f_sfixupimmv4sf = 7049,
  CODE_FOR_avx512f_sfixupimmv4sf_round = 7050,
  CODE_FOR_avx512f_sfixupimmv4sf_maskz_1 = 7051,
  CODE_FOR_avx512f_sfixupimmv4sf_maskz_1_round = 7052,
  CODE_FOR_avx512f_sfixupimmv2df = 7053,
  CODE_FOR_avx512f_sfixupimmv2df_round = 7054,
  CODE_FOR_avx512f_sfixupimmv2df_maskz_1 = 7055,
  CODE_FOR_avx512f_sfixupimmv2df_maskz_1_round = 7056,
  CODE_FOR_avx512f_sfixupimmv4sf_mask = 7057,
  CODE_FOR_avx512f_sfixupimmv4sf_mask_round = 7058,
  CODE_FOR_avx512f_sfixupimmv2df_mask = 7059,
  CODE_FOR_avx512f_sfixupimmv2df_mask_round = 7060,
  CODE_FOR_avx512bw_rndscalev32hf = 7061,
  CODE_FOR_avx512bw_rndscalev32hf_round = 7062,
  CODE_FOR_avx512bw_rndscalev32hf_mask = 7063,
  CODE_FOR_avx512bw_rndscalev32hf_mask_round = 7064,
  CODE_FOR_avx512vl_rndscalev16hf = 7065,
  CODE_FOR_avx512vl_rndscalev16hf_round = 7066,
  CODE_FOR_avx512vl_rndscalev16hf_mask = 7067,
  CODE_FOR_avx512vl_rndscalev16hf_mask_round = 7068,
  CODE_FOR_avx512fp16_rndscalev8hf = 7069,
  CODE_FOR_avx512fp16_rndscalev8hf_round = 7070,
  CODE_FOR_avx512fp16_rndscalev8hf_mask = 7071,
  CODE_FOR_avx512fp16_rndscalev8hf_mask_round = 7072,
  CODE_FOR_avx512f_rndscalev16sf = 7073,
  CODE_FOR_avx512f_rndscalev16sf_round = 7074,
  CODE_FOR_avx512f_rndscalev16sf_mask = 7075,
  CODE_FOR_avx512f_rndscalev16sf_mask_round = 7076,
  CODE_FOR_avx512vl_rndscalev8sf = 7077,
  CODE_FOR_avx512vl_rndscalev8sf_round = 7078,
  CODE_FOR_avx512vl_rndscalev8sf_mask = 7079,
  CODE_FOR_avx512vl_rndscalev8sf_mask_round = 7080,
  CODE_FOR_avx512vl_rndscalev4sf = 7081,
  CODE_FOR_avx512vl_rndscalev4sf_round = 7082,
  CODE_FOR_avx512vl_rndscalev4sf_mask = 7083,
  CODE_FOR_avx512vl_rndscalev4sf_mask_round = 7084,
  CODE_FOR_avx512f_rndscalev8df = 7085,
  CODE_FOR_avx512f_rndscalev8df_round = 7086,
  CODE_FOR_avx512f_rndscalev8df_mask = 7087,
  CODE_FOR_avx512f_rndscalev8df_mask_round = 7088,
  CODE_FOR_avx512vl_rndscalev4df = 7089,
  CODE_FOR_avx512vl_rndscalev4df_round = 7090,
  CODE_FOR_avx512vl_rndscalev4df_mask = 7091,
  CODE_FOR_avx512vl_rndscalev4df_mask_round = 7092,
  CODE_FOR_avx512vl_rndscalev2df = 7093,
  CODE_FOR_avx512vl_rndscalev2df_round = 7094,
  CODE_FOR_avx512vl_rndscalev2df_mask = 7095,
  CODE_FOR_avx512vl_rndscalev2df_mask_round = 7096,
  CODE_FOR_avx512f_rndscalev8hf = 7097,
  CODE_FOR_avx512f_rndscalev8hf_mask = 7098,
  CODE_FOR_avx512f_rndscalev8hf_round = 7099,
  CODE_FOR_avx512f_rndscalev8hf_mask_round = 7100,
  CODE_FOR_avx512f_rndscalev4sf = 7101,
  CODE_FOR_avx512f_rndscalev4sf_mask = 7102,
  CODE_FOR_avx512f_rndscalev4sf_round = 7103,
  CODE_FOR_avx512f_rndscalev4sf_mask_round = 7104,
  CODE_FOR_avx512f_rndscalev2df = 7105,
  CODE_FOR_avx512f_rndscalev2df_mask = 7106,
  CODE_FOR_avx512f_rndscalev2df_round = 7107,
  CODE_FOR_avx512f_rndscalev2df_mask_round = 7108,
  CODE_FOR_avx512f_shufps512_1 = 7115,
  CODE_FOR_avx512f_shufps512_1_mask = 7116,
  CODE_FOR_avx512f_shufpd512_1 = 7117,
  CODE_FOR_avx512f_shufpd512_1_mask = 7118,
  CODE_FOR_avx_shufpd256_1 = 7119,
  CODE_FOR_avx_shufpd256_1_mask = 7120,
  CODE_FOR_sse2_shufpd_v2df_mask = 7121,
  CODE_FOR_avx2_interleave_highv4di = 7122,
  CODE_FOR_avx2_interleave_highv4di_mask = 7123,
  CODE_FOR_avx512f_interleave_highv8di_mask = 7125,
  CODE_FOR_vec_interleave_highv2di = 7126,
  CODE_FOR_vec_interleave_highv2di_mask = 7127,
  CODE_FOR_avx2_interleave_lowv4di = 7128,
  CODE_FOR_avx2_interleave_lowv4di_mask = 7129,
  CODE_FOR_avx512f_interleave_lowv8di_mask = 7131,
  CODE_FOR_vec_interleave_lowv2di = 7132,
  CODE_FOR_vec_interleave_lowv2di_mask = 7133,
  CODE_FOR_sse2_shufpd_v2di = 7134,
  CODE_FOR_sse2_shufpd_v2df = 7135,
  CODE_FOR_sse2_storehpd = 7136,
  CODE_FOR_sse2_storelpd = 7138,
  CODE_FOR_sse2_loadhpd = 7141,
  CODE_FOR_sse2_loadlpd = 7142,
  CODE_FOR_sse2_movsd_v2di = 7143,
  CODE_FOR_sse2_movsd_v2df = 7144,
  CODE_FOR_vec_dupv2df = 7145,
  CODE_FOR_vec_dupv2df_mask = 7146,
  CODE_FOR_vec_concatv2df = 7147,
  CODE_FOR_vec_setv8df_0 = 7148,
  CODE_FOR_vec_setv4df_0 = 7149,
  CODE_FOR_avx512f_ss_truncatev16siv16qi2_mask = 7167,
  CODE_FOR_avx512f_truncatev16siv16qi2_mask = 7168,
  CODE_FOR_avx512f_us_truncatev16siv16qi2_mask = 7169,
  CODE_FOR_avx512f_ss_truncatev16siv16hi2_mask = 7170,
  CODE_FOR_avx512f_truncatev16siv16hi2_mask = 7171,
  CODE_FOR_avx512f_us_truncatev16siv16hi2_mask = 7172,
  CODE_FOR_avx512f_ss_truncatev8div8si2_mask = 7173,
  CODE_FOR_avx512f_truncatev8div8si2_mask = 7174,
  CODE_FOR_avx512f_us_truncatev8div8si2_mask = 7175,
  CODE_FOR_avx512f_ss_truncatev8div8hi2_mask = 7176,
  CODE_FOR_avx512f_truncatev8div8hi2_mask = 7177,
  CODE_FOR_avx512f_us_truncatev8div8hi2_mask = 7178,
  CODE_FOR_avx512bw_ss_truncatev32hiv32qi2 = 7179,
  CODE_FOR_avx512bw_truncatev32hiv32qi2 = 7180,
  CODE_FOR_avx512bw_us_truncatev32hiv32qi2 = 7181,
  CODE_FOR_avx512bw_ss_truncatev32hiv32qi2_mask = 7183,
  CODE_FOR_avx512bw_truncatev32hiv32qi2_mask = 7184,
  CODE_FOR_avx512bw_us_truncatev32hiv32qi2_mask = 7185,
  CODE_FOR_avx512vl_ss_truncatev4div4si2_mask = 7197,
  CODE_FOR_avx512vl_truncatev4div4si2_mask = 7198,
  CODE_FOR_avx512vl_us_truncatev4div4si2_mask = 7199,
  CODE_FOR_avx512vl_ss_truncatev8siv8hi2_mask = 7200,
  CODE_FOR_avx512vl_truncatev8siv8hi2_mask = 7201,
  CODE_FOR_avx512vl_us_truncatev8siv8hi2_mask = 7202,
  CODE_FOR_avx512vl_ss_truncatev16hiv16qi2_mask = 7203,
  CODE_FOR_avx512vl_truncatev16hiv16qi2_mask = 7204,
  CODE_FOR_avx512vl_us_truncatev16hiv16qi2_mask = 7205,
  CODE_FOR_avx512vl_ss_truncatev4div4qi2 = 7206,
  CODE_FOR_avx512vl_truncatev4div4qi2 = 7207,
  CODE_FOR_avx512vl_us_truncatev4div4qi2 = 7208,
  CODE_FOR_avx512vl_ss_truncatev2div2qi2 = 7209,
  CODE_FOR_avx512vl_truncatev2div2qi2 = 7210,
  CODE_FOR_avx512vl_us_truncatev2div2qi2 = 7211,
  CODE_FOR_avx512vl_ss_truncatev8siv8qi2 = 7212,
  CODE_FOR_avx512vl_truncatev8siv8qi2 = 7213,
  CODE_FOR_avx512vl_us_truncatev8siv8qi2 = 7214,
  CODE_FOR_avx512vl_ss_truncatev4siv4qi2 = 7215,
  CODE_FOR_avx512vl_truncatev4siv4qi2 = 7216,
  CODE_FOR_avx512vl_us_truncatev4siv4qi2 = 7217,
  CODE_FOR_avx512vl_ss_truncatev8hiv8qi2 = 7218,
  CODE_FOR_avx512vl_truncatev8hiv8qi2 = 7219,
  CODE_FOR_avx512vl_us_truncatev8hiv8qi2 = 7220,
  CODE_FOR_avx512vl_ss_truncatev2div2qi2_mask = 7228,
  CODE_FOR_avx512vl_truncatev2div2qi2_mask = 7229,
  CODE_FOR_avx512vl_us_truncatev2div2qi2_mask = 7230,
  CODE_FOR_avx512vl_ss_truncatev2div2qi2_mask_store_1 = 7234,
  CODE_FOR_avx512vl_truncatev2div2qi2_mask_store_1 = 7235,
  CODE_FOR_avx512vl_us_truncatev2div2qi2_mask_store_1 = 7236,
  CODE_FOR_avx512vl_ss_truncatev4siv4qi2_mask = 7249,
  CODE_FOR_avx512vl_truncatev4siv4qi2_mask = 7250,
  CODE_FOR_avx512vl_us_truncatev4siv4qi2_mask = 7251,
  CODE_FOR_avx512vl_ss_truncatev4div4qi2_mask = 7252,
  CODE_FOR_avx512vl_truncatev4div4qi2_mask = 7253,
  CODE_FOR_avx512vl_us_truncatev4div4qi2_mask = 7254,
  CODE_FOR_avx512vl_ss_truncatev4siv4qi2_mask_store_1 = 7261,
  CODE_FOR_avx512vl_truncatev4siv4qi2_mask_store_1 = 7262,
  CODE_FOR_avx512vl_us_truncatev4siv4qi2_mask_store_1 = 7263,
  CODE_FOR_avx512vl_ss_truncatev4div4qi2_mask_store_1 = 7264,
  CODE_FOR_avx512vl_truncatev4div4qi2_mask_store_1 = 7265,
  CODE_FOR_avx512vl_us_truncatev4div4qi2_mask_store_1 = 7266,
  CODE_FOR_avx512vl_ss_truncatev8hiv8qi2_mask = 7279,
  CODE_FOR_avx512vl_truncatev8hiv8qi2_mask = 7280,
  CODE_FOR_avx512vl_us_truncatev8hiv8qi2_mask = 7281,
  CODE_FOR_avx512vl_ss_truncatev8siv8qi2_mask = 7282,
  CODE_FOR_avx512vl_truncatev8siv8qi2_mask = 7283,
  CODE_FOR_avx512vl_us_truncatev8siv8qi2_mask = 7284,
  CODE_FOR_avx512vl_ss_truncatev8hiv8qi2_mask_store_1 = 7291,
  CODE_FOR_avx512vl_truncatev8hiv8qi2_mask_store_1 = 7292,
  CODE_FOR_avx512vl_us_truncatev8hiv8qi2_mask_store_1 = 7293,
  CODE_FOR_avx512vl_ss_truncatev8siv8qi2_mask_store_1 = 7294,
  CODE_FOR_avx512vl_truncatev8siv8qi2_mask_store_1 = 7295,
  CODE_FOR_avx512vl_us_truncatev8siv8qi2_mask_store_1 = 7296,
  CODE_FOR_avx512vl_ss_truncatev4div4hi2 = 7297,
  CODE_FOR_avx512vl_truncatev4div4hi2 = 7298,
  CODE_FOR_avx512vl_us_truncatev4div4hi2 = 7299,
  CODE_FOR_avx512vl_ss_truncatev2div2hi2 = 7300,
  CODE_FOR_avx512vl_truncatev2div2hi2 = 7301,
  CODE_FOR_avx512vl_us_truncatev2div2hi2 = 7302,
  CODE_FOR_avx512vl_ss_truncatev4siv4hi2 = 7303,
  CODE_FOR_avx512vl_truncatev4siv4hi2 = 7304,
  CODE_FOR_avx512vl_us_truncatev4siv4hi2 = 7305,
  CODE_FOR_avx512vl_ss_truncatev4siv4hi2_mask = 7319,
  CODE_FOR_avx512vl_truncatev4siv4hi2_mask = 7320,
  CODE_FOR_avx512vl_us_truncatev4siv4hi2_mask = 7321,
  CODE_FOR_avx512vl_ss_truncatev4div4hi2_mask = 7322,
  CODE_FOR_avx512vl_truncatev4div4hi2_mask = 7323,
  CODE_FOR_avx512vl_us_truncatev4div4hi2_mask = 7324,
  CODE_FOR_avx512vl_ss_truncatev4siv4hi2_mask_store_1 = 7331,
  CODE_FOR_avx512vl_truncatev4siv4hi2_mask_store_1 = 7332,
  CODE_FOR_avx512vl_us_truncatev4siv4hi2_mask_store_1 = 7333,
  CODE_FOR_avx512vl_ss_truncatev4div4hi2_mask_store_1 = 7334,
  CODE_FOR_avx512vl_truncatev4div4hi2_mask_store_1 = 7335,
  CODE_FOR_avx512vl_us_truncatev4div4hi2_mask_store_1 = 7336,
  CODE_FOR_avx512vl_ss_truncatev2div2hi2_mask = 7343,
  CODE_FOR_avx512vl_truncatev2div2hi2_mask = 7344,
  CODE_FOR_avx512vl_us_truncatev2div2hi2_mask = 7345,
  CODE_FOR_avx512vl_ss_truncatev2div2hi2_mask_store_1 = 7349,
  CODE_FOR_avx512vl_truncatev2div2hi2_mask_store_1 = 7350,
  CODE_FOR_avx512vl_us_truncatev2div2hi2_mask_store_1 = 7351,
  CODE_FOR_avx512vl_ss_truncatev2div2si2 = 7352,
  CODE_FOR_avx512vl_truncatev2div2si2 = 7353,
  CODE_FOR_avx512vl_us_truncatev2div2si2 = 7354,
  CODE_FOR_avx512vl_ss_truncatev2div2si2_mask = 7362,
  CODE_FOR_avx512vl_truncatev2div2si2_mask = 7363,
  CODE_FOR_avx512vl_us_truncatev2div2si2_mask = 7364,
  CODE_FOR_avx512vl_ss_truncatev2div2si2_mask_store_1 = 7368,
  CODE_FOR_avx512vl_truncatev2div2si2_mask_store_1 = 7369,
  CODE_FOR_avx512vl_us_truncatev2div2si2_mask_store_1 = 7370,
  CODE_FOR_avx512f_ss_truncatev8div16qi2 = 7371,
  CODE_FOR_avx512f_truncatev8div16qi2 = 7372,
  CODE_FOR_avx512f_us_truncatev8div16qi2 = 7373,
  CODE_FOR_avx512f_ss_truncatev8div16qi2_mask = 7380,
  CODE_FOR_avx512f_truncatev8div16qi2_mask = 7381,
  CODE_FOR_avx512f_us_truncatev8div16qi2_mask = 7382,
  CODE_FOR_avx512f_ss_truncatev8div16qi2_mask_store_1 = 7386,
  CODE_FOR_avx512f_truncatev8div16qi2_mask_store_1 = 7387,
  CODE_FOR_avx512f_us_truncatev8div16qi2_mask_store_1 = 7388,
  CODE_FOR_avx512bw_pmaddwd512v32hi = 7515,
  CODE_FOR_avx512bw_pmaddwd512v32hi_mask = 7516,
  CODE_FOR_avx512bw_pmaddwd512v16hi = 7517,
  CODE_FOR_avx512bw_pmaddwd512v16hi_mask = 7518,
  CODE_FOR_avx512bw_pmaddwd512v8hi = 7519,
  CODE_FOR_avx512bw_pmaddwd512v8hi_mask = 7520,
  CODE_FOR_ashrv16hi3_mask = 7536,
  CODE_FOR_ashrv8hi3_mask = 7538,
  CODE_FOR_ashrv8si3_mask = 7540,
  CODE_FOR_ashrv4si3_mask = 7542,
  CODE_FOR_ashrv2di3_mask = 7544,
  CODE_FOR_ashrv16hi3 = 7545,
  CODE_FOR_ashrv8hi3 = 7546,
  CODE_FOR_ashrv8si3 = 7547,
  CODE_FOR_ashrv4si3 = 7548,
  CODE_FOR_ashrv32hi3_mask = 7554,
  CODE_FOR_ashrv4di3_mask = 7556,
  CODE_FOR_ashrv16si3_mask = 7558,
  CODE_FOR_ashrv8di3_mask = 7560,
  CODE_FOR_ashlv16hi3_mask = 7571,
  CODE_FOR_lshrv16hi3_mask = 7573,
  CODE_FOR_ashlv8hi3_mask = 7575,
  CODE_FOR_lshrv8hi3_mask = 7577,
  CODE_FOR_ashlv8si3_mask = 7579,
  CODE_FOR_lshrv8si3_mask = 7581,
  CODE_FOR_ashlv4si3_mask = 7583,
  CODE_FOR_lshrv4si3_mask = 7585,
  CODE_FOR_ashlv4di3_mask = 7587,
  CODE_FOR_lshrv4di3_mask = 7589,
  CODE_FOR_ashlv2di3_mask = 7591,
  CODE_FOR_lshrv2di3_mask = 7593,
  CODE_FOR_ashlv16hi3 = 7594,
  CODE_FOR_lshrv16hi3 = 7595,
  CODE_FOR_ashlv8hi3 = 7596,
  CODE_FOR_lshrv8hi3 = 7597,
  CODE_FOR_ashlv8si3 = 7598,
  CODE_FOR_lshrv8si3 = 7599,
  CODE_FOR_ashlv4si3 = 7600,
  CODE_FOR_lshrv4si3 = 7601,
  CODE_FOR_ashlv4di3 = 7602,
  CODE_FOR_lshrv4di3 = 7603,
  CODE_FOR_ashlv2di3 = 7604,
  CODE_FOR_lshrv2di3 = 7605,
  CODE_FOR_ashlv32hi3 = 7610,
  CODE_FOR_ashlv32hi3_mask = 7611,
  CODE_FOR_lshrv32hi3 = 7612,
  CODE_FOR_lshrv32hi3_mask = 7613,
  CODE_FOR_ashlv16si3 = 7614,
  CODE_FOR_ashlv16si3_mask = 7615,
  CODE_FOR_lshrv16si3 = 7616,
  CODE_FOR_lshrv16si3_mask = 7617,
  CODE_FOR_ashlv8di3 = 7618,
  CODE_FOR_ashlv8di3_mask = 7619,
  CODE_FOR_lshrv8di3 = 7620,
  CODE_FOR_lshrv8di3_mask = 7621,
  CODE_FOR_avx512bw_ashlv4ti3 = 7627,
  CODE_FOR_avx512bw_lshrv4ti3 = 7628,
  CODE_FOR_avx512bw_ashlv2ti3 = 7629,
  CODE_FOR_avx512bw_lshrv2ti3 = 7630,
  CODE_FOR_avx512bw_ashlv1ti3 = 7631,
  CODE_FOR_avx512bw_lshrv1ti3 = 7632,
  CODE_FOR_avx2_ashlv2ti3 = 7633,
  CODE_FOR_avx2_lshrv2ti3 = 7634,
  CODE_FOR_sse2_ashlv1ti3 = 7635,
  CODE_FOR_sse2_lshrv1ti3 = 7636,
  CODE_FOR_avx512f_rolvv16si = 7637,
  CODE_FOR_avx512f_rolvv16si_mask = 7638,
  CODE_FOR_avx512f_rorvv16si = 7639,
  CODE_FOR_avx512f_rorvv16si_mask = 7640,
  CODE_FOR_avx512vl_rolvv8si = 7641,
  CODE_FOR_avx512vl_rolvv8si_mask = 7642,
  CODE_FOR_avx512vl_rorvv8si = 7643,
  CODE_FOR_avx512vl_rorvv8si_mask = 7644,
  CODE_FOR_avx512vl_rolvv4si = 7645,
  CODE_FOR_avx512vl_rolvv4si_mask = 7646,
  CODE_FOR_avx512vl_rorvv4si = 7647,
  CODE_FOR_avx512vl_rorvv4si_mask = 7648,
  CODE_FOR_avx512f_rolvv8di = 7649,
  CODE_FOR_avx512f_rolvv8di_mask = 7650,
  CODE_FOR_avx512f_rorvv8di = 7651,
  CODE_FOR_avx512f_rorvv8di_mask = 7652,
  CODE_FOR_avx512vl_rolvv4di = 7653,
  CODE_FOR_avx512vl_rolvv4di_mask = 7654,
  CODE_FOR_avx512vl_rorvv4di = 7655,
  CODE_FOR_avx512vl_rorvv4di_mask = 7656,
  CODE_FOR_avx512vl_rolvv2di = 7657,
  CODE_FOR_avx512vl_rolvv2di_mask = 7658,
  CODE_FOR_avx512vl_rorvv2di = 7659,
  CODE_FOR_avx512vl_rorvv2di_mask = 7660,
  CODE_FOR_avx512f_rolv16si = 7661,
  CODE_FOR_avx512f_rolv16si_mask = 7662,
  CODE_FOR_avx512f_rorv16si = 7663,
  CODE_FOR_avx512f_rorv16si_mask = 7664,
  CODE_FOR_avx512vl_rolv8si = 7665,
  CODE_FOR_avx512vl_rolv8si_mask = 7666,
  CODE_FOR_avx512vl_rorv8si = 7667,
  CODE_FOR_avx512vl_rorv8si_mask = 7668,
  CODE_FOR_avx512vl_rolv4si = 7669,
  CODE_FOR_avx512vl_rolv4si_mask = 7670,
  CODE_FOR_avx512vl_rorv4si = 7671,
  CODE_FOR_avx512vl_rorv4si_mask = 7672,
  CODE_FOR_avx512f_rolv8di = 7673,
  CODE_FOR_avx512f_rolv8di_mask = 7674,
  CODE_FOR_avx512f_rorv8di = 7675,
  CODE_FOR_avx512f_rorv8di_mask = 7676,
  CODE_FOR_avx512vl_rolv4di = 7677,
  CODE_FOR_avx512vl_rolv4di_mask = 7678,
  CODE_FOR_avx512vl_rorv4di = 7679,
  CODE_FOR_avx512vl_rorv4di_mask = 7680,
  CODE_FOR_avx512vl_rolv2di = 7681,
  CODE_FOR_avx512vl_rolv2di_mask = 7682,
  CODE_FOR_avx512vl_rorv2di = 7683,
  CODE_FOR_avx512vl_rorv2di_mask = 7684,
  CODE_FOR_sse4_2_gtv2di3 = 7875,
  CODE_FOR_avx2_gtv32qi3 = 7876,
  CODE_FOR_avx2_gtv16hi3 = 7877,
  CODE_FOR_avx2_gtv8si3 = 7878,
  CODE_FOR_avx2_gtv4di3 = 7879,
  CODE_FOR_one_cmplv16si2_mask = 7884,
  CODE_FOR_one_cmplv8di2_mask = 7886,
   CODE_FOR_one_cmplv64qi2_mask = CODE_FOR_nothing,
   CODE_FOR_one_cmplv32qi2_mask = CODE_FOR_nothing,
   CODE_FOR_one_cmplv16qi2_mask = CODE_FOR_nothing,
   CODE_FOR_one_cmplv32hi2_mask = CODE_FOR_nothing,
   CODE_FOR_one_cmplv16hi2_mask = CODE_FOR_nothing,
   CODE_FOR_one_cmplv8hi2_mask = CODE_FOR_nothing,
  CODE_FOR_one_cmplv8si2_mask = 7894,
  CODE_FOR_one_cmplv4si2_mask = 7896,
  CODE_FOR_one_cmplv4di2_mask = 7898,
  CODE_FOR_one_cmplv2di2_mask = 7900,
  CODE_FOR_andv1ti3 = 7985,
  CODE_FOR_iorv1ti3 = 7986,
  CODE_FOR_xorv1ti3 = 7987,
  CODE_FOR_avx512bw_testmv64qi3 = 8036,
  CODE_FOR_avx512bw_testmv64qi3_mask = 8037,
  CODE_FOR_avx512vl_testmv32qi3 = 8038,
  CODE_FOR_avx512vl_testmv32qi3_mask = 8039,
  CODE_FOR_avx512vl_testmv16qi3 = 8040,
  CODE_FOR_avx512vl_testmv16qi3_mask = 8041,
  CODE_FOR_avx512bw_testmv32hi3 = 8042,
  CODE_FOR_avx512bw_testmv32hi3_mask = 8043,
  CODE_FOR_avx512vl_testmv16hi3 = 8044,
  CODE_FOR_avx512vl_testmv16hi3_mask = 8045,
  CODE_FOR_avx512vl_testmv8hi3 = 8046,
  CODE_FOR_avx512vl_testmv8hi3_mask = 8047,
  CODE_FOR_avx512f_testmv16si3 = 8048,
  CODE_FOR_avx512f_testmv16si3_mask = 8049,
  CODE_FOR_avx512vl_testmv8si3 = 8050,
  CODE_FOR_avx512vl_testmv8si3_mask = 8051,
  CODE_FOR_avx512vl_testmv4si3 = 8052,
  CODE_FOR_avx512vl_testmv4si3_mask = 8053,
  CODE_FOR_avx512f_testmv8di3 = 8054,
  CODE_FOR_avx512f_testmv8di3_mask = 8055,
  CODE_FOR_avx512vl_testmv4di3 = 8056,
  CODE_FOR_avx512vl_testmv4di3_mask = 8057,
  CODE_FOR_avx512vl_testmv2di3 = 8058,
  CODE_FOR_avx512vl_testmv2di3_mask = 8059,
  CODE_FOR_avx512bw_testnmv64qi3 = 8060,
  CODE_FOR_avx512bw_testnmv64qi3_mask = 8061,
  CODE_FOR_avx512vl_testnmv32qi3 = 8062,
  CODE_FOR_avx512vl_testnmv32qi3_mask = 8063,
  CODE_FOR_avx512vl_testnmv16qi3 = 8064,
  CODE_FOR_avx512vl_testnmv16qi3_mask = 8065,
  CODE_FOR_avx512bw_testnmv32hi3 = 8066,
  CODE_FOR_avx512bw_testnmv32hi3_mask = 8067,
  CODE_FOR_avx512vl_testnmv16hi3 = 8068,
  CODE_FOR_avx512vl_testnmv16hi3_mask = 8069,
  CODE_FOR_avx512vl_testnmv8hi3 = 8070,
  CODE_FOR_avx512vl_testnmv8hi3_mask = 8071,
  CODE_FOR_avx512f_testnmv16si3 = 8072,
  CODE_FOR_avx512f_testnmv16si3_mask = 8073,
  CODE_FOR_avx512vl_testnmv8si3 = 8074,
  CODE_FOR_avx512vl_testnmv8si3_mask = 8075,
  CODE_FOR_avx512vl_testnmv4si3 = 8076,
  CODE_FOR_avx512vl_testnmv4si3_mask = 8077,
  CODE_FOR_avx512f_testnmv8di3 = 8078,
  CODE_FOR_avx512f_testnmv8di3_mask = 8079,
  CODE_FOR_avx512vl_testnmv4di3 = 8080,
  CODE_FOR_avx512vl_testnmv4di3_mask = 8081,
  CODE_FOR_avx512vl_testnmv2di3 = 8082,
  CODE_FOR_avx512vl_testnmv2di3_mask = 8083,
  CODE_FOR_sse2_packsswb = 8228,
  CODE_FOR_sse2_packsswb_mask = 8229,
  CODE_FOR_avx2_packsswb = 8230,
  CODE_FOR_avx2_packsswb_mask = 8231,
  CODE_FOR_avx512bw_packsswb = 8232,
  CODE_FOR_avx512bw_packsswb_mask = 8233,
  CODE_FOR_sse2_packssdw = 8234,
  CODE_FOR_sse2_packssdw_mask = 8235,
  CODE_FOR_avx2_packssdw = 8236,
  CODE_FOR_avx2_packssdw_mask = 8237,
  CODE_FOR_avx512bw_packssdw = 8238,
  CODE_FOR_avx512bw_packssdw_mask = 8239,
  CODE_FOR_avx512bw_packuswb = 8240,
  CODE_FOR_avx512bw_packuswb_mask = 8241,
  CODE_FOR_avx2_packuswb = 8242,
  CODE_FOR_avx2_packuswb_mask = 8243,
  CODE_FOR_sse2_packuswb = 8244,
  CODE_FOR_sse2_packuswb_mask = 8245,
  CODE_FOR_avx512bw_interleave_highv64qi = 8246,
  CODE_FOR_avx512bw_interleave_highv64qi_mask = 8247,
  CODE_FOR_avx2_interleave_highv32qi = 8248,
  CODE_FOR_avx2_interleave_highv32qi_mask = 8249,
  CODE_FOR_vec_interleave_highv16qi = 8250,
  CODE_FOR_vec_interleave_highv16qi_mask = 8251,
  CODE_FOR_avx512bw_interleave_lowv64qi = 8252,
  CODE_FOR_avx512bw_interleave_lowv64qi_mask = 8253,
  CODE_FOR_avx2_interleave_lowv32qi = 8254,
  CODE_FOR_avx2_interleave_lowv32qi_mask = 8255,
  CODE_FOR_vec_interleave_lowv16qi = 8256,
  CODE_FOR_vec_interleave_lowv16qi_mask = 8257,
  CODE_FOR_avx512bw_interleave_highv32hi = 8258,
  CODE_FOR_avx512bw_interleave_highv32hi_mask = 8259,
  CODE_FOR_avx512bw_interleave_highv32hf = 8260,
  CODE_FOR_avx512bw_interleave_highv32hf_mask = 8261,
  CODE_FOR_avx512bw_interleave_highv32bf = 8262,
  CODE_FOR_avx512bw_interleave_highv32bf_mask = 8263,
  CODE_FOR_avx2_interleave_highv16hi = 8264,
  CODE_FOR_avx2_interleave_highv16hi_mask = 8265,
  CODE_FOR_avx2_interleave_highv16hf = 8266,
  CODE_FOR_avx2_interleave_highv16hf_mask = 8267,
  CODE_FOR_avx2_interleave_highv16bf = 8268,
  CODE_FOR_avx2_interleave_highv16bf_mask = 8269,
  CODE_FOR_vec_interleave_highv8hi = 8270,
  CODE_FOR_vec_interleave_highv8hi_mask = 8271,
  CODE_FOR_vec_interleave_highv8hf = 8272,
  CODE_FOR_vec_interleave_highv8hf_mask = 8273,
  CODE_FOR_vec_interleave_highv8bf = 8274,
  CODE_FOR_vec_interleave_highv8bf_mask = 8275,
  CODE_FOR_avx512bw_interleave_lowv32hi_mask = 8277,
  CODE_FOR_avx512bw_interleave_lowv32hf_mask = 8279,
  CODE_FOR_avx512bw_interleave_lowv32bf_mask = 8281,
  CODE_FOR_avx2_interleave_lowv16hi = 8282,
  CODE_FOR_avx2_interleave_lowv16hi_mask = 8283,
  CODE_FOR_avx2_interleave_lowv16hf = 8284,
  CODE_FOR_avx2_interleave_lowv16hf_mask = 8285,
  CODE_FOR_avx2_interleave_lowv16bf = 8286,
  CODE_FOR_avx2_interleave_lowv16bf_mask = 8287,
  CODE_FOR_vec_interleave_lowv8hi = 8288,
  CODE_FOR_vec_interleave_lowv8hi_mask = 8289,
  CODE_FOR_vec_interleave_lowv8hf = 8290,
  CODE_FOR_vec_interleave_lowv8hf_mask = 8291,
  CODE_FOR_vec_interleave_lowv8bf = 8292,
  CODE_FOR_vec_interleave_lowv8bf_mask = 8293,
  CODE_FOR_avx2_interleave_highv8si = 8294,
  CODE_FOR_avx2_interleave_highv8si_mask = 8295,
  CODE_FOR_avx512f_interleave_highv16si_mask = 8297,
  CODE_FOR_vec_interleave_highv4si = 8298,
  CODE_FOR_vec_interleave_highv4si_mask = 8299,
  CODE_FOR_avx2_interleave_lowv8si = 8300,
  CODE_FOR_avx2_interleave_lowv8si_mask = 8301,
  CODE_FOR_avx512f_interleave_lowv16si_mask = 8303,
  CODE_FOR_vec_interleave_lowv4si = 8304,
  CODE_FOR_vec_interleave_lowv4si_mask = 8305,
  CODE_FOR_sse4_1_pinsrb = 8306,
  CODE_FOR_sse2_pinsrw = 8307,
  CODE_FOR_sse2_pinsrph = 8308,
  CODE_FOR_sse2_pinsrbf = 8309,
  CODE_FOR_sse4_1_pinsrd = 8310,
  CODE_FOR_sse4_1_pinsrq = 8311,
  CODE_FOR_avx512dq_vinsertf64x2_1_mask = 8317,
  CODE_FOR_avx512dq_vinserti64x2_1_mask = 8319,
  CODE_FOR_avx512f_vinsertf32x4_1_mask = 8321,
  CODE_FOR_avx512f_vinserti32x4_1_mask = 8323,
  CODE_FOR_vec_set_lo_v16sf = 8324,
  CODE_FOR_vec_set_lo_v16sf_mask = 8325,
  CODE_FOR_vec_set_lo_v16si = 8326,
  CODE_FOR_vec_set_lo_v16si_mask = 8327,
  CODE_FOR_vec_set_hi_v16sf = 8328,
  CODE_FOR_vec_set_hi_v16sf_mask = 8329,
  CODE_FOR_vec_set_hi_v16si = 8330,
  CODE_FOR_vec_set_hi_v16si_mask = 8331,
  CODE_FOR_vec_set_lo_v8df = 8332,
  CODE_FOR_vec_set_lo_v8df_mask = 8333,
  CODE_FOR_vec_set_lo_v8di = 8334,
  CODE_FOR_vec_set_lo_v8di_mask = 8335,
  CODE_FOR_vec_set_hi_v8df = 8336,
  CODE_FOR_vec_set_hi_v8df_mask = 8337,
  CODE_FOR_vec_set_hi_v8di = 8338,
  CODE_FOR_vec_set_hi_v8di_mask = 8339,
  CODE_FOR_avx512dq_shuf_i64x2_1_mask = 8341,
  CODE_FOR_avx512dq_shuf_f64x2_1_mask = 8343,
  CODE_FOR_avx512f_shuf_f64x2_1 = 8344,
  CODE_FOR_avx512f_shuf_f64x2_1_mask = 8345,
  CODE_FOR_avx512f_shuf_i64x2_1 = 8346,
  CODE_FOR_avx512f_shuf_i64x2_1_mask = 8347,
  CODE_FOR_avx512vl_shuf_i32x4_1 = 8352,
  CODE_FOR_avx512vl_shuf_i32x4_1_mask = 8353,
  CODE_FOR_avx512vl_shuf_f32x4_1 = 8354,
  CODE_FOR_avx512vl_shuf_f32x4_1_mask = 8355,
  CODE_FOR_avx512f_shuf_f32x4_1 = 8356,
  CODE_FOR_avx512f_shuf_f32x4_1_mask = 8357,
  CODE_FOR_avx512f_shuf_i32x4_1 = 8358,
  CODE_FOR_avx512f_shuf_i32x4_1_mask = 8359,
  CODE_FOR_avx512f_pshufd_1 = 8364,
  CODE_FOR_avx512f_pshufd_1_mask = 8365,
  CODE_FOR_avx2_pshufd_1 = 8366,
  CODE_FOR_avx2_pshufd_1_mask = 8367,
  CODE_FOR_sse2_pshufd_1 = 8368,
  CODE_FOR_sse2_pshufd_1_mask = 8369,
  CODE_FOR_avx512bw_pshuflwv32hi_mask = 8371,
  CODE_FOR_avx2_pshuflw_1 = 8372,
  CODE_FOR_avx2_pshuflw_1_mask = 8373,
  CODE_FOR_sse2_pshuflw_1 = 8374,
  CODE_FOR_sse2_pshuflw_1_mask = 8375,
  CODE_FOR_avx512bw_pshufhwv32hi_mask = 8377,
  CODE_FOR_avx2_pshufhw_1 = 8378,
  CODE_FOR_avx2_pshufhw_1_mask = 8379,
  CODE_FOR_sse2_pshufhw_1 = 8380,
  CODE_FOR_sse2_pshufhw_1_mask = 8381,
  CODE_FOR_sse2_loadld = 8382,
  CODE_FOR_vec_concatv2di = 8416,
  CODE_FOR_vec_setv8di_0 = 8418,
  CODE_FOR_vec_setv4di_0 = 8419,
  CODE_FOR_avx_movmskps256 = 8435,
  CODE_FOR_sse_movmskps = 8436,
  CODE_FOR_avx_movmskpd256 = 8437,
  CODE_FOR_sse2_movmskpd = 8438,
  CODE_FOR_avx2_pmovmskb = 8483,
  CODE_FOR_sse2_pmovmskb = 8484,
  CODE_FOR_sse_ldmxcsr = 8508,
  CODE_FOR_sse_stmxcsr = 8509,
  CODE_FOR_sse2_clflush = 8510,
  CODE_FOR_sse3_mwait = 8511,
  CODE_FOR_sse3_monitor_si = 8512,
  CODE_FOR_sse3_monitor_di = 8513,
  CODE_FOR_avx2_phaddwv16hi3 = 8514,
  CODE_FOR_avx2_phaddswv16hi3 = 8515,
  CODE_FOR_avx2_phsubwv16hi3 = 8516,
  CODE_FOR_avx2_phsubswv16hi3 = 8517,
  CODE_FOR_ssse3_phaddwv8hi3 = 8518,
  CODE_FOR_ssse3_phaddswv8hi3 = 8519,
  CODE_FOR_ssse3_phsubwv8hi3 = 8520,
  CODE_FOR_ssse3_phsubswv8hi3 = 8521,
  CODE_FOR_ssse3_phaddwv4hi3 = 8522,
  CODE_FOR_ssse3_phaddswv4hi3 = 8523,
  CODE_FOR_ssse3_phsubwv4hi3 = 8524,
  CODE_FOR_ssse3_phsubswv4hi3 = 8525,
  CODE_FOR_avx2_phadddv8si3 = 8526,
  CODE_FOR_avx2_phsubdv8si3 = 8527,
  CODE_FOR_ssse3_phadddv4si3 = 8528,
  CODE_FOR_ssse3_phsubdv4si3 = 8529,
  CODE_FOR_ssse3_phadddv2si3 = 8530,
  CODE_FOR_ssse3_phsubdv2si3 = 8531,
  CODE_FOR_avx2_pmaddubsw256 = 8532,
  CODE_FOR_avx512bw_pmaddubsw512v8hi = 8533,
  CODE_FOR_avx512bw_pmaddubsw512v8hi_mask = 8534,
  CODE_FOR_avx512bw_pmaddubsw512v16hi = 8535,
  CODE_FOR_avx512bw_pmaddubsw512v16hi_mask = 8536,
  CODE_FOR_avx512bw_pmaddubsw512v32hi = 8537,
  CODE_FOR_avx512bw_pmaddubsw512v32hi_mask = 8538,
  CODE_FOR_avx512bw_umulhrswv32hi3 = 8539,
  CODE_FOR_avx512bw_umulhrswv32hi3_mask = 8540,
  CODE_FOR_ssse3_pmaddubsw128 = 8541,
  CODE_FOR_ssse3_pmaddubsw = 8542,
  CODE_FOR_avx512bw_pshufbv64qi3 = 8551,
  CODE_FOR_avx512bw_pshufbv64qi3_mask = 8552,
  CODE_FOR_avx2_pshufbv32qi3 = 8553,
  CODE_FOR_avx2_pshufbv32qi3_mask = 8554,
  CODE_FOR_ssse3_pshufbv16qi3 = 8555,
  CODE_FOR_ssse3_pshufbv16qi3_mask = 8556,
  CODE_FOR_avx2_psignv32qi3 = 8558,
  CODE_FOR_ssse3_psignv16qi3 = 8559,
  CODE_FOR_avx2_psignv16hi3 = 8560,
  CODE_FOR_ssse3_psignv8hi3 = 8561,
  CODE_FOR_avx2_psignv8si3 = 8562,
  CODE_FOR_ssse3_psignv4si3 = 8563,
  CODE_FOR_ssse3_psignv8qi3 = 8564,
  CODE_FOR_ssse3_psignv4hi3 = 8565,
  CODE_FOR_ssse3_psignv2si3 = 8566,
  CODE_FOR_avx512bw_palignrv64qi_mask = 8567,
  CODE_FOR_avx2_palignrv32qi_mask = 8568,
  CODE_FOR_ssse3_palignrv16qi_mask = 8569,
  CODE_FOR_avx512bw_palignrv4ti = 8570,
  CODE_FOR_avx2_palignrv2ti = 8571,
  CODE_FOR_ssse3_palignrv1ti = 8572,
  CODE_FOR_ssse3_palignrdi = 8573,
  CODE_FOR_absv16si2_mask = 8586,
  CODE_FOR_absv8si2_mask = 8587,
  CODE_FOR_absv4si2_mask = 8588,
  CODE_FOR_absv8di2_mask = 8589,
  CODE_FOR_absv4di2_mask = 8590,
  CODE_FOR_absv2di2_mask = 8591,
  CODE_FOR_absv64qi2_mask = 8592,
  CODE_FOR_absv16qi2_mask = 8593,
  CODE_FOR_absv32qi2_mask = 8594,
  CODE_FOR_absv32hi2_mask = 8595,
  CODE_FOR_absv16hi2_mask = 8596,
  CODE_FOR_absv8hi2_mask = 8597,
  CODE_FOR_sse4a_movntsf = 8598,
  CODE_FOR_sse4a_movntdf = 8599,
  CODE_FOR_sse4a_vmmovntv4sf = 8600,
  CODE_FOR_sse4a_vmmovntv2df = 8601,
  CODE_FOR_sse4a_extrqi = 8602,
  CODE_FOR_sse4a_extrq = 8603,
  CODE_FOR_sse4a_insertqi = 8604,
  CODE_FOR_sse4a_insertq = 8605,
  CODE_FOR_avx_blendps256 = 8606,
  CODE_FOR_sse4_1_blendps = 8607,
  CODE_FOR_avx_blendpd256 = 8608,
  CODE_FOR_sse4_1_blendpd = 8609,
  CODE_FOR_avx_blendvps256 = 8610,
  CODE_FOR_sse4_1_blendvps = 8611,
  CODE_FOR_avx_blendvpd256 = 8612,
  CODE_FOR_sse4_1_blendvpd = 8613,
  CODE_FOR_sse4_1_blendvss = 8614,
  CODE_FOR_sse4_1_blendvsd = 8615,
  CODE_FOR_avx_dpps256 = 8640,
  CODE_FOR_sse4_1_dpps = 8641,
  CODE_FOR_avx_dppd256 = 8642,
  CODE_FOR_sse4_1_dppd = 8643,
  CODE_FOR_avx512f_movntdqa = 8644,
  CODE_FOR_avx2_movntdqa = 8645,
  CODE_FOR_sse4_1_movntdqa = 8646,
  CODE_FOR_avx2_mpsadbw = 8647,
  CODE_FOR_sse4_1_mpsadbw = 8648,
  CODE_FOR_avx10_2_mpsadbw = 8649,
  CODE_FOR_avx10_2_mpsadbw_mask = 8650,
  CODE_FOR_avx2_mpsadbw_mask = 8652,
  CODE_FOR_sse4_1_mpsadbw_mask = 8654,
  CODE_FOR_avx512bw_packusdw = 8655,
  CODE_FOR_avx512bw_packusdw_mask = 8656,
  CODE_FOR_avx2_packusdw = 8657,
  CODE_FOR_avx2_packusdw_mask = 8658,
  CODE_FOR_sse4_1_packusdw = 8659,
  CODE_FOR_sse4_1_packusdw_mask = 8660,
  CODE_FOR_avx2_pblendvb = 8661,
  CODE_FOR_sse4_1_pblendvb = 8662,
  CODE_FOR_sse4_1_pblendw = 8671,
  CODE_FOR_sse4_1_pblendph = 8672,
  CODE_FOR_sse4_1_pblendbf = 8673,
  CODE_FOR_avx2_pblenddv8si = 8677,
  CODE_FOR_avx2_pblenddv4si = 8678,
  CODE_FOR_sse4_1_phminposuw = 8679,
  CODE_FOR_avx2_sign_extendv16qiv16hi2 = 8680,
  CODE_FOR_avx2_sign_extendv16qiv16hi2_mask = 8681,
  CODE_FOR_avx2_zero_extendv16qiv16hi2 = 8682,
  CODE_FOR_avx2_zero_extendv16qiv16hi2_mask = 8683,
  CODE_FOR_avx512bw_sign_extendv32qiv32hi2 = 8688,
  CODE_FOR_avx512bw_sign_extendv32qiv32hi2_mask = 8689,
  CODE_FOR_avx512bw_zero_extendv32qiv32hi2 = 8690,
  CODE_FOR_avx512bw_zero_extendv32qiv32hi2_mask = 8691,
  CODE_FOR_sse4_1_sign_extendv8qiv8hi2 = 8696,
  CODE_FOR_sse4_1_sign_extendv8qiv8hi2_mask = 8697,
  CODE_FOR_sse4_1_zero_extendv8qiv8hi2 = 8698,
  CODE_FOR_sse4_1_zero_extendv8qiv8hi2_mask = 8699,
  CODE_FOR_avx512f_sign_extendv16qiv16si2_mask = 8713,
  CODE_FOR_avx512f_zero_extendv16qiv16si2_mask = 8715,
  CODE_FOR_avx2_sign_extendv8qiv8si2 = 8716,
  CODE_FOR_avx2_sign_extendv8qiv8si2_mask = 8717,
  CODE_FOR_avx2_zero_extendv8qiv8si2 = 8718,
  CODE_FOR_avx2_zero_extendv8qiv8si2_mask = 8719,
  CODE_FOR_sse4_1_sign_extendv4qiv4si2 = 8728,
  CODE_FOR_sse4_1_sign_extendv4qiv4si2_mask = 8729,
  CODE_FOR_sse4_1_zero_extendv4qiv4si2 = 8730,
  CODE_FOR_sse4_1_zero_extendv4qiv4si2_mask = 8731,
  CODE_FOR_avx512f_sign_extendv16hiv16si2 = 8740,
  CODE_FOR_avx512f_sign_extendv16hiv16si2_mask = 8741,
  CODE_FOR_avx512f_zero_extendv16hiv16si2 = 8742,
  CODE_FOR_avx512f_zero_extendv16hiv16si2_mask = 8743,
  CODE_FOR_avx512f_zero_extendv16hiv16si2_1 = 8744,
  CODE_FOR_avx2_sign_extendv8hiv8si2 = 8748,
  CODE_FOR_avx2_sign_extendv8hiv8si2_mask = 8749,
  CODE_FOR_avx2_zero_extendv8hiv8si2 = 8750,
  CODE_FOR_avx2_zero_extendv8hiv8si2_mask = 8751,
  CODE_FOR_avx2_zero_extendv8hiv8si2_1 = 8752,
  CODE_FOR_sse4_1_sign_extendv4hiv4si2 = 8756,
  CODE_FOR_sse4_1_sign_extendv4hiv4si2_mask = 8757,
  CODE_FOR_sse4_1_zero_extendv4hiv4si2 = 8758,
  CODE_FOR_sse4_1_zero_extendv4hiv4si2_mask = 8759,
  CODE_FOR_avx512f_sign_extendv8qiv8di2 = 8772,
  CODE_FOR_avx512f_sign_extendv8qiv8di2_mask = 8773,
  CODE_FOR_avx512f_zero_extendv8qiv8di2 = 8774,
  CODE_FOR_avx512f_zero_extendv8qiv8di2_mask = 8775,
  CODE_FOR_avx2_sign_extendv4qiv4di2 = 8784,
  CODE_FOR_avx2_sign_extendv4qiv4di2_mask = 8785,
  CODE_FOR_avx2_zero_extendv4qiv4di2 = 8786,
  CODE_FOR_avx2_zero_extendv4qiv4di2_mask = 8787,
  CODE_FOR_sse4_1_sign_extendv2qiv2di2 = 8796,
  CODE_FOR_sse4_1_sign_extendv2qiv2di2_mask = 8797,
  CODE_FOR_sse4_1_zero_extendv2qiv2di2 = 8798,
  CODE_FOR_sse4_1_zero_extendv2qiv2di2_mask = 8799,
  CODE_FOR_avx512f_sign_extendv8hiv8di2 = 8807,
  CODE_FOR_avx512f_sign_extendv8hiv8di2_mask = 8808,
  CODE_FOR_avx512f_zero_extendv8hiv8di2 = 8809,
  CODE_FOR_avx512f_zero_extendv8hiv8di2_mask = 8810,
  CODE_FOR_avx2_sign_extendv4hiv4di2 = 8811,
  CODE_FOR_avx2_sign_extendv4hiv4di2_mask = 8812,
  CODE_FOR_avx2_zero_extendv4hiv4di2 = 8813,
  CODE_FOR_avx2_zero_extendv4hiv4di2_mask = 8814,
  CODE_FOR_sse4_1_sign_extendv2hiv2di2 = 8823,
  CODE_FOR_sse4_1_sign_extendv2hiv2di2_mask = 8824,
  CODE_FOR_sse4_1_zero_extendv2hiv2di2 = 8825,
  CODE_FOR_sse4_1_zero_extendv2hiv2di2_mask = 8826,
  CODE_FOR_avx512f_sign_extendv8siv8di2 = 8835,
  CODE_FOR_avx512f_sign_extendv8siv8di2_mask = 8836,
  CODE_FOR_avx512f_zero_extendv8siv8di2 = 8837,
  CODE_FOR_avx512f_zero_extendv8siv8di2_mask = 8838,
  CODE_FOR_avx2_sign_extendv4siv4di2 = 8841,
  CODE_FOR_avx2_sign_extendv4siv4di2_mask = 8842,
  CODE_FOR_avx2_zero_extendv4siv4di2 = 8843,
  CODE_FOR_avx2_zero_extendv4siv4di2_mask = 8844,
  CODE_FOR_sse4_1_sign_extendv2siv2di2 = 8847,
  CODE_FOR_sse4_1_sign_extendv2siv2di2_mask = 8848,
  CODE_FOR_sse4_1_zero_extendv2siv2di2 = 8849,
  CODE_FOR_sse4_1_zero_extendv2siv2di2_mask = 8850,
  CODE_FOR_avx_vtestps256 = 8861,
  CODE_FOR_avx_vtestps = 8862,
  CODE_FOR_avx_vtestpd256 = 8863,
  CODE_FOR_avx_vtestpd = 8864,
  CODE_FOR_ptesttf2 = 8879,
  CODE_FOR_avx_roundps256 = 8894,
  CODE_FOR_sse4_1_roundps = 8895,
  CODE_FOR_avx_roundpd256 = 8896,
  CODE_FOR_sse4_1_roundpd = 8897,
  CODE_FOR_sse4_1_roundsh = 8898,
  CODE_FOR_sse4_1_roundss = 8899,
  CODE_FOR_sse4_1_roundsd = 8900,
  CODE_FOR_sse4_2_pcmpestr = 8904,
  CODE_FOR_sse4_2_pcmpestri = 8905,
  CODE_FOR_sse4_2_pcmpestrm = 8906,
  CODE_FOR_sse4_2_pcmpestr_cconly = 8907,
  CODE_FOR_sse4_2_pcmpistr = 8908,
  CODE_FOR_sse4_2_pcmpistri = 8909,
  CODE_FOR_sse4_2_pcmpistrm = 8910,
  CODE_FOR_sse4_2_pcmpistr_cconly = 8911,
  CODE_FOR_xop_pmacsww = 8912,
  CODE_FOR_xop_pmacssww = 8913,
  CODE_FOR_xop_pmacsdd = 8914,
  CODE_FOR_xop_pmacssdd = 8915,
  CODE_FOR_xop_pmacsdql = 8916,
  CODE_FOR_xop_pmacssdql = 8917,
  CODE_FOR_xop_pmacsdqh = 8918,
  CODE_FOR_xop_pmacssdqh = 8919,
  CODE_FOR_xop_pmacswd = 8920,
  CODE_FOR_xop_pmacsswd = 8921,
  CODE_FOR_xop_pmadcswd = 8922,
  CODE_FOR_xop_pmadcsswd = 8923,
  CODE_FOR_xop_pcmov_v32qi256 = 8924,
  CODE_FOR_xop_pcmov_v16qi = 8925,
  CODE_FOR_xop_pcmov_v16hi256 = 8926,
  CODE_FOR_xop_pcmov_v8hi = 8927,
  CODE_FOR_xop_pcmov_v8si256 = 8928,
  CODE_FOR_xop_pcmov_v4si = 8929,
  CODE_FOR_xop_pcmov_v4di256 = 8930,
  CODE_FOR_xop_pcmov_v2di = 8931,
  CODE_FOR_xop_pcmov_v2ti256 = 8932,
  CODE_FOR_xop_pcmov_v1ti = 8933,
  CODE_FOR_xop_pcmov_v16hf256 = 8934,
  CODE_FOR_xop_pcmov_v8hf = 8935,
  CODE_FOR_xop_pcmov_v8sf256 = 8936,
  CODE_FOR_xop_pcmov_v4sf = 8937,
  CODE_FOR_xop_pcmov_v4df256 = 8938,
  CODE_FOR_xop_pcmov_v2df = 8939,
  CODE_FOR_xop_phaddbw = 8940,
  CODE_FOR_xop_phaddubw = 8941,
  CODE_FOR_xop_phaddbd = 8942,
  CODE_FOR_xop_phaddubd = 8943,
  CODE_FOR_xop_phaddbq = 8944,
  CODE_FOR_xop_phaddubq = 8945,
  CODE_FOR_xop_phaddwd = 8946,
  CODE_FOR_xop_phadduwd = 8947,
  CODE_FOR_xop_phaddwq = 8948,
  CODE_FOR_xop_phadduwq = 8949,
  CODE_FOR_xop_phadddq = 8950,
  CODE_FOR_xop_phaddudq = 8951,
  CODE_FOR_xop_phsubbw = 8952,
  CODE_FOR_xop_phsubwd = 8953,
  CODE_FOR_xop_phsubdq = 8954,
  CODE_FOR_xop_pperm = 8955,
  CODE_FOR_xop_pperm_pack_v2di_v4si = 8956,
  CODE_FOR_xop_pperm_pack_v4si_v8hi = 8957,
  CODE_FOR_xop_pperm_pack_v8hi_v16qi = 8958,
  CODE_FOR_xop_rotlv16qi3 = 8959,
  CODE_FOR_xop_rotlv8hi3 = 8960,
  CODE_FOR_xop_rotlv4si3 = 8961,
  CODE_FOR_xop_rotlv2di3 = 8962,
  CODE_FOR_xop_rotrv16qi3 = 8963,
  CODE_FOR_xop_rotrv8hi3 = 8964,
  CODE_FOR_xop_rotrv4si3 = 8965,
  CODE_FOR_xop_rotrv2di3 = 8966,
  CODE_FOR_xop_vrotlv16qi3 = 8967,
  CODE_FOR_xop_vrotlv8hi3 = 8968,
  CODE_FOR_xop_vrotlv4si3 = 8969,
  CODE_FOR_xop_vrotlv2di3 = 8970,
  CODE_FOR_xop_shav16qi3 = 8971,
  CODE_FOR_xop_shav8hi3 = 8972,
  CODE_FOR_xop_shav4si3 = 8973,
  CODE_FOR_xop_shav2di3 = 8974,
  CODE_FOR_xop_shlv16qi3 = 8975,
  CODE_FOR_xop_shlv8hi3 = 8976,
  CODE_FOR_xop_shlv4si3 = 8977,
  CODE_FOR_xop_shlv2di3 = 8978,
  CODE_FOR_xop_frczsf2 = 8980,
  CODE_FOR_xop_frczdf2 = 8981,
  CODE_FOR_xop_frczv4sf2 = 8982,
  CODE_FOR_xop_frczv2df2 = 8983,
  CODE_FOR_xop_frczv8sf2 = 8984,
  CODE_FOR_xop_frczv4df2 = 8985,
  CODE_FOR_xop_maskcmpv16qi3 = 8988,
  CODE_FOR_xop_maskcmpv8hi3 = 8989,
  CODE_FOR_xop_maskcmpv4si3 = 8990,
  CODE_FOR_xop_maskcmpv2di3 = 8991,
  CODE_FOR_xop_maskcmp_unsv16qi3 = 8992,
  CODE_FOR_xop_maskcmp_unsv8hi3 = 8993,
  CODE_FOR_xop_maskcmp_unsv4si3 = 8994,
  CODE_FOR_xop_maskcmp_unsv2di3 = 8995,
  CODE_FOR_xop_maskcmp_uns2v16qi3 = 8996,
  CODE_FOR_xop_maskcmp_uns2v8hi3 = 8997,
  CODE_FOR_xop_maskcmp_uns2v4si3 = 8998,
  CODE_FOR_xop_maskcmp_uns2v2di3 = 8999,
  CODE_FOR_xop_pcom_tfv16qi3 = 9000,
  CODE_FOR_xop_pcom_tfv8hi3 = 9001,
  CODE_FOR_xop_pcom_tfv4si3 = 9002,
  CODE_FOR_xop_pcom_tfv2di3 = 9003,
  CODE_FOR_xop_vpermil2v8sf3 = 9004,
  CODE_FOR_xop_vpermil2v4sf3 = 9005,
  CODE_FOR_xop_vpermil2v4df3 = 9006,
  CODE_FOR_xop_vpermil2v2df3 = 9007,
  CODE_FOR_aesenc = 9008,
  CODE_FOR_aesenclast = 9009,
  CODE_FOR_aesdec = 9010,
  CODE_FOR_aesdeclast = 9011,
  CODE_FOR_aesimc = 9012,
  CODE_FOR_aeskeygenassist = 9013,
  CODE_FOR_pclmulqdq = 9014,
  CODE_FOR_avx_vzeroupper_callee_abi = 9016,
  CODE_FOR_avx2_pbroadcastv16si = 9017,
  CODE_FOR_avx2_pbroadcastv8di = 9018,
  CODE_FOR_avx2_pbroadcastv64qi = 9019,
  CODE_FOR_avx2_pbroadcastv32qi = 9020,
  CODE_FOR_avx2_pbroadcastv16qi = 9021,
  CODE_FOR_avx2_pbroadcastv32hi = 9022,
  CODE_FOR_avx2_pbroadcastv16hi = 9023,
  CODE_FOR_avx2_pbroadcastv8hi = 9024,
  CODE_FOR_avx2_pbroadcastv8si = 9025,
  CODE_FOR_avx2_pbroadcastv4si = 9026,
  CODE_FOR_avx2_pbroadcastv4di = 9027,
  CODE_FOR_avx2_pbroadcastv2di = 9028,
  CODE_FOR_avx2_pbroadcastv32hf = 9029,
  CODE_FOR_avx2_pbroadcastv16hf = 9030,
  CODE_FOR_avx2_pbroadcastv8hf = 9031,
  CODE_FOR_avx2_pbroadcastv32bf = 9032,
  CODE_FOR_avx2_pbroadcastv16bf = 9033,
  CODE_FOR_avx2_pbroadcastv8bf = 9034,
  CODE_FOR_avx2_pbroadcastv32qi_1 = 9035,
  CODE_FOR_avx2_pbroadcastv16hi_1 = 9036,
  CODE_FOR_avx2_pbroadcastv8si_1 = 9037,
  CODE_FOR_avx2_pbroadcastv4di_1 = 9038,
  CODE_FOR_avx2_pbroadcastv16hf_1 = 9039,
  CODE_FOR_avx2_pbroadcastv16bf_1 = 9040,
  CODE_FOR_avx2_permvarv8si = 9041,
  CODE_FOR_avx2_permvarv8si_mask = 9042,
  CODE_FOR_avx2_permvarv8sf = 9043,
  CODE_FOR_avx2_permvarv8sf_mask = 9044,
  CODE_FOR_avx512f_permvarv16si = 9045,
  CODE_FOR_avx512f_permvarv16si_mask = 9046,
  CODE_FOR_avx512f_permvarv16sf = 9047,
  CODE_FOR_avx512f_permvarv16sf_mask = 9048,
  CODE_FOR_avx512f_permvarv8di = 9049,
  CODE_FOR_avx512f_permvarv8di_mask = 9050,
  CODE_FOR_avx512f_permvarv8df = 9051,
  CODE_FOR_avx512f_permvarv8df_mask = 9052,
  CODE_FOR_avx2_permvarv4di = 9053,
  CODE_FOR_avx2_permvarv4di_mask = 9054,
  CODE_FOR_avx2_permvarv4df = 9055,
  CODE_FOR_avx2_permvarv4df_mask = 9056,
  CODE_FOR_avx512bw_permvarv64qi = 9057,
  CODE_FOR_avx512bw_permvarv64qi_mask = 9058,
  CODE_FOR_avx512vl_permvarv16qi = 9059,
  CODE_FOR_avx512vl_permvarv16qi_mask = 9060,
  CODE_FOR_avx512vl_permvarv32qi = 9061,
  CODE_FOR_avx512vl_permvarv32qi_mask = 9062,
  CODE_FOR_avx512vl_permvarv8hi = 9063,
  CODE_FOR_avx512vl_permvarv8hi_mask = 9064,
  CODE_FOR_avx512vl_permvarv16hi = 9065,
  CODE_FOR_avx512vl_permvarv16hi_mask = 9066,
  CODE_FOR_avx512bw_permvarv32hi = 9067,
  CODE_FOR_avx512bw_permvarv32hi_mask = 9068,
  CODE_FOR_avx512fp16_permvarv8hf = 9069,
  CODE_FOR_avx512fp16_permvarv8hf_mask = 9070,
  CODE_FOR_avx512vl_permvarv16hf = 9071,
  CODE_FOR_avx512vl_permvarv16hf_mask = 9072,
  CODE_FOR_avx512bw_permvarv32hf = 9073,
  CODE_FOR_avx512bw_permvarv32hf_mask = 9074,
  CODE_FOR_avx512vl_permvarv8bf = 9075,
  CODE_FOR_avx512vl_permvarv8bf_mask = 9076,
  CODE_FOR_avx512vl_permvarv16bf = 9077,
  CODE_FOR_avx512vl_permvarv16bf_mask = 9078,
  CODE_FOR_avx512bw_permvarv32bf = 9079,
  CODE_FOR_avx512bw_permvarv32bf_mask = 9080,
  CODE_FOR_avx2_permv4di_1 = 9096,
  CODE_FOR_avx2_permv4di_1_mask = 9097,
  CODE_FOR_avx2_permv4df_1 = 9098,
  CODE_FOR_avx2_permv4df_1_mask = 9099,
  CODE_FOR_avx512f_permv8df_1 = 9100,
  CODE_FOR_avx512f_permv8df_1_mask = 9101,
  CODE_FOR_avx512f_permv8di_1 = 9102,
  CODE_FOR_avx512f_permv8di_1_mask = 9103,
  CODE_FOR_avx2_permv2ti = 9104,
  CODE_FOR_avx2_vec_dupv4df = 9105,
  CODE_FOR_avx512f_vec_dupv16si_1 = 9106,
  CODE_FOR_avx512f_vec_dupv8di_1 = 9107,
  CODE_FOR_avx512bw_vec_dupv32hi_1 = 9108,
  CODE_FOR_avx512bw_vec_dupv64qi_1 = 9109,
  CODE_FOR_avx512bw_vec_dupv32hf_1 = 9110,
  CODE_FOR_avx512bw_vec_dupv32bf_1 = 9111,
  CODE_FOR_avx512f_vec_dupv16si = 9112,
  CODE_FOR_avx512f_vec_dupv16si_mask = 9113,
  CODE_FOR_avx512vl_vec_dupv8si = 9114,
  CODE_FOR_avx512vl_vec_dupv8si_mask = 9115,
  CODE_FOR_avx512vl_vec_dupv4si = 9116,
  CODE_FOR_avx512vl_vec_dupv4si_mask = 9117,
  CODE_FOR_avx512f_vec_dupv8di = 9118,
  CODE_FOR_avx512f_vec_dupv8di_mask = 9119,
  CODE_FOR_avx512vl_vec_dupv4di = 9120,
  CODE_FOR_avx512vl_vec_dupv4di_mask = 9121,
  CODE_FOR_avx512vl_vec_dupv2di = 9122,
  CODE_FOR_avx512vl_vec_dupv2di_mask = 9123,
  CODE_FOR_avx512f_vec_dupv16sf = 9124,
  CODE_FOR_avx512f_vec_dupv16sf_mask = 9125,
  CODE_FOR_avx512vl_vec_dupv8sf = 9126,
  CODE_FOR_avx512vl_vec_dupv8sf_mask = 9127,
  CODE_FOR_avx512vl_vec_dupv4sf = 9128,
  CODE_FOR_avx512vl_vec_dupv4sf_mask = 9129,
  CODE_FOR_avx512f_vec_dupv8df = 9130,
  CODE_FOR_avx512f_vec_dupv8df_mask = 9131,
  CODE_FOR_avx512vl_vec_dupv4df = 9132,
  CODE_FOR_avx512vl_vec_dupv4df_mask = 9133,
  CODE_FOR_avx512vl_vec_dupv2df = 9134,
  CODE_FOR_avx512vl_vec_dupv2df_mask = 9135,
  CODE_FOR_avx512bw_vec_dupv64qi = 9136,
  CODE_FOR_avx512bw_vec_dupv64qi_mask = 9137,
  CODE_FOR_avx512vl_vec_dupv16qi = 9138,
  CODE_FOR_avx512vl_vec_dupv16qi_mask = 9139,
  CODE_FOR_avx512vl_vec_dupv32qi = 9140,
  CODE_FOR_avx512vl_vec_dupv32qi_mask = 9141,
  CODE_FOR_avx512bw_vec_dupv32hi = 9142,
  CODE_FOR_avx512bw_vec_dupv32hi_mask = 9143,
  CODE_FOR_avx512vl_vec_dupv16hi = 9144,
  CODE_FOR_avx512vl_vec_dupv16hi_mask = 9145,
  CODE_FOR_avx512vl_vec_dupv8hi = 9146,
  CODE_FOR_avx512vl_vec_dupv8hi_mask = 9147,
  CODE_FOR_avx512bw_vec_dupv32hf = 9148,
  CODE_FOR_avx512bw_vec_dupv32hf_mask = 9149,
  CODE_FOR_avx512vl_vec_dupv16hf = 9150,
  CODE_FOR_avx512vl_vec_dupv16hf_mask = 9151,
  CODE_FOR_avx512fp16_vec_dupv8hf = 9152,
  CODE_FOR_avx512fp16_vec_dupv8hf_mask = 9153,
  CODE_FOR_avx512bw_vec_dupv32bf = 9154,
  CODE_FOR_avx512bw_vec_dupv32bf_mask = 9155,
  CODE_FOR_avx512vl_vec_dupv16bf = 9156,
  CODE_FOR_avx512vl_vec_dupv16bf_mask = 9157,
  CODE_FOR_avx512vl_vec_dupv8bf = 9158,
  CODE_FOR_avx512vl_vec_dupv8bf_mask = 9159,
  CODE_FOR_avx512f_broadcastv16sf_mask = 9161,
  CODE_FOR_avx512f_broadcastv16si_mask = 9163,
  CODE_FOR_avx512f_broadcastv8df_mask = 9165,
  CODE_FOR_avx512f_broadcastv8di_mask = 9167,
  CODE_FOR_avx512bw_vec_dup_gprv64qi_mask = 9169,
  CODE_FOR_avx512vl_vec_dup_gprv16qi_mask = 9171,
  CODE_FOR_avx512vl_vec_dup_gprv32qi_mask = 9173,
  CODE_FOR_avx512bw_vec_dup_gprv32hi_mask = 9175,
  CODE_FOR_avx512vl_vec_dup_gprv16hi_mask = 9177,
  CODE_FOR_avx512vl_vec_dup_gprv8hi_mask = 9179,
  CODE_FOR_avx512bw_vec_dup_gprv32hf_mask = 9181,
  CODE_FOR_avx512vl_vec_dup_gprv16hf_mask = 9183,
  CODE_FOR_avx512fp16_vec_dup_gprv8hf_mask = 9185,
  CODE_FOR_avx512bw_vec_dup_gprv32bf_mask = 9187,
  CODE_FOR_avx512vl_vec_dup_gprv16bf_mask = 9189,
  CODE_FOR_avx512vl_vec_dup_gprv8bf_mask = 9191,
  CODE_FOR_avx512f_vec_dup_gprv16si_mask = 9193,
  CODE_FOR_avx512vl_vec_dup_gprv8si_mask = 9195,
  CODE_FOR_avx512vl_vec_dup_gprv4si_mask = 9197,
  CODE_FOR_avx512f_vec_dup_gprv8di_mask = 9199,
  CODE_FOR_avx512vl_vec_dup_gprv4di_mask = 9201,
  CODE_FOR_avx512vl_vec_dup_gprv2di_mask = 9203,
  CODE_FOR_avx512f_vec_dup_gprv16sf_mask = 9205,
  CODE_FOR_avx512vl_vec_dup_gprv8sf_mask = 9207,
  CODE_FOR_avx512vl_vec_dup_gprv4sf_mask = 9209,
  CODE_FOR_avx512f_vec_dup_gprv8df_mask = 9211,
  CODE_FOR_avx512vl_vec_dup_gprv4df_mask = 9213,
  CODE_FOR_avx512vl_vec_dup_gprv2df_mask = 9215,
  CODE_FOR_vec_dupv4sf = 9216,
  CODE_FOR_avx2_vbroadcasti128_v32qi = 9219,
  CODE_FOR_avx2_vbroadcasti128_v16hi = 9220,
  CODE_FOR_avx2_vbroadcasti128_v8si = 9221,
  CODE_FOR_avx2_vbroadcasti128_v4di = 9222,
  CODE_FOR_avx2_lddqu_inserti_to_bcasti = 9223,
  CODE_FOR_vec_dupv8si = 9234,
  CODE_FOR_vec_dupv8sf = 9235,
  CODE_FOR_vec_dupv4di = 9236,
  CODE_FOR_vec_dupv4df = 9237,
  CODE_FOR_avx_vbroadcastf128_v32qi = 9238,
  CODE_FOR_avx_vbroadcastf128_v16hi = 9239,
  CODE_FOR_avx_vbroadcastf128_v8si = 9240,
  CODE_FOR_avx_vbroadcastf128_v4di = 9241,
  CODE_FOR_avx_vbroadcastf128_v8sf = 9242,
  CODE_FOR_avx_vbroadcastf128_v4df = 9243,
  CODE_FOR_avx_vbroadcastf128_v16hf = 9244,
  CODE_FOR_avx_vbroadcastf128_v16bf = 9245,
  CODE_FOR_avx512dq_broadcastv16si_mask = 9247,
  CODE_FOR_avx512dq_broadcastv8si_mask = 9249,
  CODE_FOR_avx512dq_broadcastv4si_mask = 9251,
  CODE_FOR_avx512dq_broadcastv16sf_mask = 9253,
  CODE_FOR_avx512dq_broadcastv8sf_mask = 9255,
  CODE_FOR_avx512vl_broadcastv8si_mask_1 = 9257,
  CODE_FOR_avx512vl_broadcastv8sf_mask_1 = 9259,
  CODE_FOR_avx512dq_broadcastv16sf_mask_1 = 9261,
  CODE_FOR_avx512dq_broadcastv16si_mask_1 = 9263,
  CODE_FOR_avx512dq_broadcastv8di_mask_1 = 9265,
  CODE_FOR_avx512dq_broadcastv8df_mask_1 = 9267,
  CODE_FOR_avx512dq_broadcastv4di_mask_1 = 9269,
  CODE_FOR_avx512dq_broadcastv4df_mask_1 = 9271,
  CODE_FOR_avx512cd_maskb_vec_dupv8di = 9272,
  CODE_FOR_avx512cd_maskb_vec_dupv4di = 9273,
  CODE_FOR_avx512cd_maskb_vec_dupv2di = 9274,
  CODE_FOR_avx512cd_maskw_vec_dupv16si = 9275,
  CODE_FOR_avx512cd_maskw_vec_dupv8si = 9276,
  CODE_FOR_avx512cd_maskw_vec_dupv4si = 9277,
  CODE_FOR_avx512f_vpermilvarv16sf3 = 9278,
  CODE_FOR_avx512f_vpermilvarv16sf3_mask = 9279,
  CODE_FOR_avx_vpermilvarv8sf3 = 9280,
  CODE_FOR_avx_vpermilvarv8sf3_mask = 9281,
  CODE_FOR_avx_vpermilvarv4sf3 = 9282,
  CODE_FOR_avx_vpermilvarv4sf3_mask = 9283,
  CODE_FOR_avx512f_vpermilvarv8df3 = 9284,
  CODE_FOR_avx512f_vpermilvarv8df3_mask = 9285,
  CODE_FOR_avx_vpermilvarv4df3 = 9286,
  CODE_FOR_avx_vpermilvarv4df3_mask = 9287,
  CODE_FOR_avx_vpermilvarv2df3 = 9288,
  CODE_FOR_avx_vpermilvarv2df3_mask = 9289,
  CODE_FOR_avx512f_vpermt2varv16si3 = 9308,
  CODE_FOR_avx512f_vpermt2varv16si3_maskz_1 = 9309,
  CODE_FOR_avx512f_vpermt2varv16sf3 = 9310,
  CODE_FOR_avx512f_vpermt2varv16sf3_maskz_1 = 9311,
  CODE_FOR_avx512f_vpermt2varv8di3 = 9312,
  CODE_FOR_avx512f_vpermt2varv8di3_maskz_1 = 9313,
  CODE_FOR_avx512f_vpermt2varv8df3 = 9314,
  CODE_FOR_avx512f_vpermt2varv8df3_maskz_1 = 9315,
  CODE_FOR_avx512vl_vpermt2varv8si3 = 9316,
  CODE_FOR_avx512vl_vpermt2varv8si3_maskz_1 = 9317,
  CODE_FOR_avx512vl_vpermt2varv8sf3 = 9318,
  CODE_FOR_avx512vl_vpermt2varv8sf3_maskz_1 = 9319,
  CODE_FOR_avx512vl_vpermt2varv4di3 = 9320,
  CODE_FOR_avx512vl_vpermt2varv4di3_maskz_1 = 9321,
  CODE_FOR_avx512vl_vpermt2varv4df3 = 9322,
  CODE_FOR_avx512vl_vpermt2varv4df3_maskz_1 = 9323,
  CODE_FOR_avx512vl_vpermt2varv4si3 = 9324,
  CODE_FOR_avx512vl_vpermt2varv4si3_maskz_1 = 9325,
  CODE_FOR_avx512vl_vpermt2varv4sf3 = 9326,
  CODE_FOR_avx512vl_vpermt2varv4sf3_maskz_1 = 9327,
  CODE_FOR_avx512vl_vpermt2varv2di3 = 9328,
  CODE_FOR_avx512vl_vpermt2varv2di3_maskz_1 = 9329,
  CODE_FOR_avx512vl_vpermt2varv2df3 = 9330,
  CODE_FOR_avx512vl_vpermt2varv2df3_maskz_1 = 9331,
  CODE_FOR_avx512bw_vpermt2varv32hi3 = 9332,
  CODE_FOR_avx512bw_vpermt2varv32hi3_maskz_1 = 9333,
  CODE_FOR_avx512vl_vpermt2varv16hi3 = 9334,
  CODE_FOR_avx512vl_vpermt2varv16hi3_maskz_1 = 9335,
  CODE_FOR_avx512vl_vpermt2varv8hi3 = 9336,
  CODE_FOR_avx512vl_vpermt2varv8hi3_maskz_1 = 9337,
  CODE_FOR_avx512bw_vpermt2varv64qi3 = 9338,
  CODE_FOR_avx512bw_vpermt2varv64qi3_maskz_1 = 9339,
  CODE_FOR_avx512vl_vpermt2varv32qi3 = 9340,
  CODE_FOR_avx512vl_vpermt2varv32qi3_maskz_1 = 9341,
  CODE_FOR_avx512vl_vpermt2varv16qi3 = 9342,
  CODE_FOR_avx512vl_vpermt2varv16qi3_maskz_1 = 9343,
  CODE_FOR_avx512bw_vpermt2varv32hf3 = 9344,
  CODE_FOR_avx512bw_vpermt2varv32hf3_maskz_1 = 9345,
  CODE_FOR_avx512vl_vpermt2varv16hf3 = 9346,
  CODE_FOR_avx512vl_vpermt2varv16hf3_maskz_1 = 9347,
  CODE_FOR_avx512fp16_vpermt2varv8hf3 = 9348,
  CODE_FOR_avx512fp16_vpermt2varv8hf3_maskz_1 = 9349,
  CODE_FOR_avx512bw_vpermt2varv32bf3 = 9350,
  CODE_FOR_avx512bw_vpermt2varv32bf3_maskz_1 = 9351,
  CODE_FOR_avx512vl_vpermt2varv16bf3 = 9352,
  CODE_FOR_avx512vl_vpermt2varv16bf3_maskz_1 = 9353,
  CODE_FOR_avx512vl_vpermt2varv8bf3 = 9354,
  CODE_FOR_avx512vl_vpermt2varv8bf3_maskz_1 = 9355,
  CODE_FOR_avx512f_vpermt2varv16si3_mask = 9356,
  CODE_FOR_avx512f_vpermt2varv16sf3_mask = 9357,
  CODE_FOR_avx512f_vpermt2varv8di3_mask = 9358,
  CODE_FOR_avx512f_vpermt2varv8df3_mask = 9359,
  CODE_FOR_avx512vl_vpermt2varv8si3_mask = 9360,
  CODE_FOR_avx512vl_vpermt2varv8sf3_mask = 9361,
  CODE_FOR_avx512vl_vpermt2varv4di3_mask = 9362,
  CODE_FOR_avx512vl_vpermt2varv4df3_mask = 9363,
  CODE_FOR_avx512vl_vpermt2varv4si3_mask = 9364,
  CODE_FOR_avx512vl_vpermt2varv4sf3_mask = 9365,
  CODE_FOR_avx512vl_vpermt2varv2di3_mask = 9366,
  CODE_FOR_avx512vl_vpermt2varv2df3_mask = 9367,
  CODE_FOR_avx512bw_vpermt2varv32hi3_mask = 9368,
  CODE_FOR_avx512vl_vpermt2varv16hi3_mask = 9369,
  CODE_FOR_avx512vl_vpermt2varv8hi3_mask = 9370,
  CODE_FOR_avx512bw_vpermt2varv64qi3_mask = 9371,
  CODE_FOR_avx512vl_vpermt2varv32qi3_mask = 9372,
  CODE_FOR_avx512vl_vpermt2varv16qi3_mask = 9373,
  CODE_FOR_vec_set_lo_v4di = 9388,
  CODE_FOR_vec_set_lo_v4di_mask = 9389,
  CODE_FOR_vec_set_lo_v4df = 9390,
  CODE_FOR_vec_set_lo_v4df_mask = 9391,
  CODE_FOR_vec_set_hi_v4di = 9392,
  CODE_FOR_vec_set_hi_v4di_mask = 9393,
  CODE_FOR_vec_set_hi_v4df = 9394,
  CODE_FOR_vec_set_hi_v4df_mask = 9395,
  CODE_FOR_vec_set_lo_v8si = 9396,
  CODE_FOR_vec_set_lo_v8si_mask = 9397,
  CODE_FOR_vec_set_lo_v8sf = 9398,
  CODE_FOR_vec_set_lo_v8sf_mask = 9399,
  CODE_FOR_vec_set_hi_v8si = 9400,
  CODE_FOR_vec_set_hi_v8si_mask = 9401,
  CODE_FOR_vec_set_hi_v8sf = 9402,
  CODE_FOR_vec_set_hi_v8sf_mask = 9403,
  CODE_FOR_vec_set_lo_v16hi = 9404,
  CODE_FOR_vec_set_lo_v16hf = 9405,
  CODE_FOR_vec_set_lo_v16bf = 9406,
  CODE_FOR_vec_set_hi_v16hi = 9407,
  CODE_FOR_vec_set_hi_v16hf = 9408,
  CODE_FOR_vec_set_hi_v16bf = 9409,
  CODE_FOR_vec_set_lo_v32qi = 9410,
  CODE_FOR_vec_set_hi_v32qi = 9411,
  CODE_FOR_avx_maskloadps = 9412,
  CODE_FOR_avx_maskloadpd = 9413,
  CODE_FOR_avx2_maskloadq256 = 9414,
  CODE_FOR_avx2_maskloadq = 9415,
  CODE_FOR_avx_maskloadps256 = 9416,
  CODE_FOR_avx_maskloadpd256 = 9417,
  CODE_FOR_avx2_maskloadd256 = 9418,
  CODE_FOR_avx2_maskloadd = 9419,
  CODE_FOR_avx_maskstoreps = 9420,
  CODE_FOR_avx_maskstorepd = 9421,
  CODE_FOR_avx2_maskstoreq256 = 9422,
  CODE_FOR_avx2_maskstoreq = 9423,
  CODE_FOR_avx_maskstoreps256 = 9424,
  CODE_FOR_avx_maskstorepd256 = 9425,
  CODE_FOR_avx2_maskstored256 = 9426,
  CODE_FOR_avx2_maskstored = 9427,
  CODE_FOR_avx512f_storev16si_mask = 9428,
  CODE_FOR_avx512vl_storev8si_mask = 9429,
  CODE_FOR_avx512vl_storev4si_mask = 9430,
  CODE_FOR_avx512f_storev8di_mask = 9431,
  CODE_FOR_avx512vl_storev4di_mask = 9432,
  CODE_FOR_avx512vl_storev2di_mask = 9433,
  CODE_FOR_avx512f_storev16sf_mask = 9434,
  CODE_FOR_avx512vl_storev8sf_mask = 9435,
  CODE_FOR_avx512vl_storev4sf_mask = 9436,
  CODE_FOR_avx512f_storev8df_mask = 9437,
  CODE_FOR_avx512vl_storev4df_mask = 9438,
  CODE_FOR_avx512vl_storev2df_mask = 9439,
  CODE_FOR_avx512bw_storev64qi_mask = 9440,
  CODE_FOR_avx512vl_storev16qi_mask = 9441,
  CODE_FOR_avx512vl_storev32qi_mask = 9442,
  CODE_FOR_avx512bw_storev32hi_mask = 9443,
  CODE_FOR_avx512vl_storev16hi_mask = 9444,
  CODE_FOR_avx512vl_storev8hi_mask = 9445,
  CODE_FOR_avx512bw_storev32hf_mask = 9446,
  CODE_FOR_avx512vl_storev16hf_mask = 9447,
  CODE_FOR_avx512fp16_storev8hf_mask = 9448,
  CODE_FOR_avx512bw_storev32bf_mask = 9449,
  CODE_FOR_avx512vl_storev16bf_mask = 9450,
  CODE_FOR_avx512vl_storev8bf_mask = 9451,
  CODE_FOR_avx_si256_si = 9476,
  CODE_FOR_avx_ps256_ps = 9477,
  CODE_FOR_avx_pd256_pd = 9478,
  CODE_FOR_avx2_ashrvv4si = 9479,
  CODE_FOR_avx2_ashrvv4si_mask = 9480,
  CODE_FOR_avx2_ashrvv8si = 9481,
  CODE_FOR_avx2_ashrvv8si_mask = 9482,
  CODE_FOR_avx512f_ashrvv16si = 9483,
  CODE_FOR_avx512f_ashrvv16si_mask = 9484,
  CODE_FOR_avx2_ashrvv2di = 9485,
  CODE_FOR_avx2_ashrvv2di_mask = 9486,
  CODE_FOR_avx2_ashrvv4di = 9487,
  CODE_FOR_avx2_ashrvv4di_mask = 9488,
  CODE_FOR_avx512f_ashrvv8di = 9489,
  CODE_FOR_avx512f_ashrvv8di_mask = 9490,
  CODE_FOR_avx512vl_ashrvv8hi = 9491,
  CODE_FOR_avx512vl_ashrvv8hi_mask = 9492,
  CODE_FOR_avx512vl_ashrvv16hi = 9493,
  CODE_FOR_avx512vl_ashrvv16hi_mask = 9494,
  CODE_FOR_avx512bw_ashrvv32hi = 9495,
  CODE_FOR_avx512bw_ashrvv32hi_mask = 9496,
  CODE_FOR_avx512f_ashlvv16si = 9497,
  CODE_FOR_avx512f_ashlvv16si_mask = 9498,
  CODE_FOR_avx512f_lshrvv16si = 9499,
  CODE_FOR_avx512f_lshrvv16si_mask = 9500,
  CODE_FOR_avx2_ashlvv8si = 9501,
  CODE_FOR_avx2_ashlvv8si_mask = 9502,
  CODE_FOR_avx2_lshrvv8si = 9503,
  CODE_FOR_avx2_lshrvv8si_mask = 9504,
  CODE_FOR_avx2_ashlvv4si = 9505,
  CODE_FOR_avx2_ashlvv4si_mask = 9506,
  CODE_FOR_avx2_lshrvv4si = 9507,
  CODE_FOR_avx2_lshrvv4si_mask = 9508,
  CODE_FOR_avx512f_ashlvv8di = 9509,
  CODE_FOR_avx512f_ashlvv8di_mask = 9510,
  CODE_FOR_avx512f_lshrvv8di = 9511,
  CODE_FOR_avx512f_lshrvv8di_mask = 9512,
  CODE_FOR_avx2_ashlvv4di = 9513,
  CODE_FOR_avx2_ashlvv4di_mask = 9514,
  CODE_FOR_avx2_lshrvv4di = 9515,
  CODE_FOR_avx2_lshrvv4di_mask = 9516,
  CODE_FOR_avx2_ashlvv2di = 9517,
  CODE_FOR_avx2_ashlvv2di_mask = 9518,
  CODE_FOR_avx2_lshrvv2di = 9519,
  CODE_FOR_avx2_lshrvv2di_mask = 9520,
  CODE_FOR_avx512vl_ashlvv8hi = 9521,
  CODE_FOR_avx512vl_ashlvv8hi_mask = 9522,
  CODE_FOR_avx512vl_lshrvv8hi = 9523,
  CODE_FOR_avx512vl_lshrvv8hi_mask = 9524,
  CODE_FOR_avx512vl_ashlvv16hi = 9525,
  CODE_FOR_avx512vl_ashlvv16hi_mask = 9526,
  CODE_FOR_avx512vl_lshrvv16hi = 9527,
  CODE_FOR_avx512vl_lshrvv16hi_mask = 9528,
  CODE_FOR_avx512bw_ashlvv32hi = 9529,
  CODE_FOR_avx512bw_ashlvv32hi_mask = 9530,
  CODE_FOR_avx512bw_lshrvv32hi = 9531,
  CODE_FOR_avx512bw_lshrvv32hi_mask = 9532,
  CODE_FOR_avx_vec_concatv32qi = 9533,
  CODE_FOR_avx_vec_concatv16hi = 9534,
  CODE_FOR_avx_vec_concatv16hf = 9535,
  CODE_FOR_avx_vec_concatv16bf = 9536,
  CODE_FOR_avx_vec_concatv8si = 9537,
  CODE_FOR_avx_vec_concatv4di = 9538,
  CODE_FOR_avx_vec_concatv8sf = 9539,
  CODE_FOR_avx_vec_concatv4df = 9540,
  CODE_FOR_avx_vec_concatv64qi = 9541,
  CODE_FOR_avx_vec_concatv32hi = 9542,
  CODE_FOR_avx_vec_concatv32hf = 9543,
  CODE_FOR_avx_vec_concatv32bf = 9544,
  CODE_FOR_avx_vec_concatv16si = 9545,
  CODE_FOR_avx_vec_concatv8di = 9546,
  CODE_FOR_avx_vec_concatv16sf = 9547,
  CODE_FOR_avx_vec_concatv8df = 9548,
  CODE_FOR_vcvtph2ps = 9573,
  CODE_FOR_vcvtph2ps_mask = 9574,
  CODE_FOR_vcvtph2ps256 = 9577,
  CODE_FOR_vcvtph2ps256_mask = 9578,
  CODE_FOR_avx512f_vcvtph2ps512_mask = 9581,
  CODE_FOR_avx512f_vcvtph2ps512_mask_round = 9582,
  CODE_FOR_vcvtps2ph256 = 9586,
  CODE_FOR_vcvtps2ph256_mask = 9587,
  CODE_FOR_avx512f_vcvtps2ph512_mask = 9592,
  CODE_FOR_avx512f_vcvtps2ph512_mask_round = 9593,
  CODE_FOR_avx512f_compressv16si_mask = 9812,
  CODE_FOR_avx512f_compressv16sf_mask = 9813,
  CODE_FOR_avx512f_compressv8di_mask = 9814,
  CODE_FOR_avx512f_compressv8df_mask = 9815,
  CODE_FOR_avx512vl_compressv8si_mask = 9816,
  CODE_FOR_avx512vl_compressv8sf_mask = 9817,
  CODE_FOR_avx512vl_compressv4di_mask = 9818,
  CODE_FOR_avx512vl_compressv4df_mask = 9819,
  CODE_FOR_avx512vl_compressv4si_mask = 9820,
  CODE_FOR_avx512vl_compressv4sf_mask = 9821,
  CODE_FOR_avx512vl_compressv2di_mask = 9822,
  CODE_FOR_avx512vl_compressv2df_mask = 9823,
  CODE_FOR_compressv64qi_mask = 9824,
  CODE_FOR_compressv16qi_mask = 9825,
  CODE_FOR_compressv32qi_mask = 9826,
  CODE_FOR_compressv32hi_mask = 9827,
  CODE_FOR_compressv16hi_mask = 9828,
  CODE_FOR_compressv8hi_mask = 9829,
  CODE_FOR_avx512f_compressstorev16si_mask = 9830,
  CODE_FOR_avx512f_compressstorev16sf_mask = 9831,
  CODE_FOR_avx512f_compressstorev8di_mask = 9832,
  CODE_FOR_avx512f_compressstorev8df_mask = 9833,
  CODE_FOR_avx512vl_compressstorev8si_mask = 9834,
  CODE_FOR_avx512vl_compressstorev8sf_mask = 9835,
  CODE_FOR_avx512vl_compressstorev4di_mask = 9836,
  CODE_FOR_avx512vl_compressstorev4df_mask = 9837,
  CODE_FOR_avx512vl_compressstorev4si_mask = 9838,
  CODE_FOR_avx512vl_compressstorev4sf_mask = 9839,
  CODE_FOR_avx512vl_compressstorev2di_mask = 9840,
  CODE_FOR_avx512vl_compressstorev2df_mask = 9841,
  CODE_FOR_compressstorev64qi_mask = 9842,
  CODE_FOR_compressstorev16qi_mask = 9843,
  CODE_FOR_compressstorev32qi_mask = 9844,
  CODE_FOR_compressstorev32hi_mask = 9845,
  CODE_FOR_compressstorev16hi_mask = 9846,
  CODE_FOR_compressstorev8hi_mask = 9847,
  CODE_FOR_expandv16si_mask = 9848,
  CODE_FOR_expandv16sf_mask = 9849,
  CODE_FOR_expandv8di_mask = 9850,
  CODE_FOR_expandv8df_mask = 9851,
  CODE_FOR_expandv8si_mask = 9852,
  CODE_FOR_expandv8sf_mask = 9853,
  CODE_FOR_expandv4di_mask = 9854,
  CODE_FOR_expandv4df_mask = 9855,
  CODE_FOR_expandv4si_mask = 9856,
  CODE_FOR_expandv4sf_mask = 9857,
  CODE_FOR_expandv2di_mask = 9858,
  CODE_FOR_expandv2df_mask = 9859,
  CODE_FOR_expandv64qi_mask = 9860,
  CODE_FOR_expandv16qi_mask = 9861,
  CODE_FOR_expandv32qi_mask = 9862,
  CODE_FOR_expandv32hi_mask = 9863,
  CODE_FOR_expandv16hi_mask = 9864,
  CODE_FOR_expandv8hi_mask = 9865,
  CODE_FOR_avx512dq_rangepv16sf = 9884,
  CODE_FOR_avx512dq_rangepv16sf_round = 9885,
  CODE_FOR_avx512dq_rangepv16sf_mask = 9886,
  CODE_FOR_avx512dq_rangepv16sf_mask_round = 9887,
  CODE_FOR_avx512dq_rangepv8sf = 9888,
   CODE_FOR_avx512dq_rangepv8sf_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_rangepv8sf_mask = 9889,
   CODE_FOR_avx512dq_rangepv8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_rangepv4sf = 9890,
   CODE_FOR_avx512dq_rangepv4sf_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_rangepv4sf_mask = 9891,
   CODE_FOR_avx512dq_rangepv4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_rangepv8df = 9892,
  CODE_FOR_avx512dq_rangepv8df_round = 9893,
  CODE_FOR_avx512dq_rangepv8df_mask = 9894,
  CODE_FOR_avx512dq_rangepv8df_mask_round = 9895,
  CODE_FOR_avx512dq_rangepv4df = 9896,
   CODE_FOR_avx512dq_rangepv4df_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_rangepv4df_mask = 9897,
   CODE_FOR_avx512dq_rangepv4df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_rangepv2df = 9898,
   CODE_FOR_avx512dq_rangepv2df_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_rangepv2df_mask = 9899,
   CODE_FOR_avx512dq_rangepv2df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512dq_rangesv4sf = 9900,
  CODE_FOR_avx512dq_rangesv4sf_mask = 9901,
  CODE_FOR_avx512dq_rangesv4sf_round = 9902,
  CODE_FOR_avx512dq_rangesv4sf_mask_round = 9903,
  CODE_FOR_avx512dq_rangesv2df = 9904,
  CODE_FOR_avx512dq_rangesv2df_mask = 9905,
  CODE_FOR_avx512dq_rangesv2df_round = 9906,
  CODE_FOR_avx512dq_rangesv2df_mask_round = 9907,
  CODE_FOR_avx512dq_fpclassv32hf = 9908,
  CODE_FOR_avx512dq_fpclassv32hf_mask = 9909,
  CODE_FOR_avx512dq_fpclassv16hf = 9910,
  CODE_FOR_avx512dq_fpclassv16hf_mask = 9911,
  CODE_FOR_avx512dq_fpclassv8hf = 9912,
  CODE_FOR_avx512dq_fpclassv8hf_mask = 9913,
  CODE_FOR_avx512dq_fpclassv16sf = 9914,
  CODE_FOR_avx512dq_fpclassv16sf_mask = 9915,
  CODE_FOR_avx512dq_fpclassv8sf = 9916,
  CODE_FOR_avx512dq_fpclassv8sf_mask = 9917,
  CODE_FOR_avx512dq_fpclassv4sf = 9918,
  CODE_FOR_avx512dq_fpclassv4sf_mask = 9919,
  CODE_FOR_avx512dq_fpclassv8df = 9920,
  CODE_FOR_avx512dq_fpclassv8df_mask = 9921,
  CODE_FOR_avx512dq_fpclassv4df = 9922,
  CODE_FOR_avx512dq_fpclassv4df_mask = 9923,
  CODE_FOR_avx512dq_fpclassv2df = 9924,
  CODE_FOR_avx512dq_fpclassv2df_mask = 9925,
  CODE_FOR_avx512dq_vmfpclassv8hf = 9926,
  CODE_FOR_avx512dq_vmfpclassv8hf_mask = 9927,
  CODE_FOR_avx512dq_vmfpclassv4sf = 9928,
  CODE_FOR_avx512dq_vmfpclassv4sf_mask = 9929,
  CODE_FOR_avx512dq_vmfpclassv2df = 9930,
  CODE_FOR_avx512dq_vmfpclassv2df_mask = 9931,
  CODE_FOR_avx512bw_getmantv32hf = 9932,
  CODE_FOR_avx512bw_getmantv32hf_round = 9933,
  CODE_FOR_avx512bw_getmantv32hf_mask = 9934,
  CODE_FOR_avx512bw_getmantv32hf_mask_round = 9935,
  CODE_FOR_avx512vl_getmantv16hf = 9936,
  CODE_FOR_avx512vl_getmantv16hf_round = 9937,
  CODE_FOR_avx512vl_getmantv16hf_mask = 9938,
  CODE_FOR_avx512vl_getmantv16hf_mask_round = 9939,
  CODE_FOR_avx512fp16_getmantv8hf = 9940,
  CODE_FOR_avx512fp16_getmantv8hf_round = 9941,
  CODE_FOR_avx512fp16_getmantv8hf_mask = 9942,
  CODE_FOR_avx512fp16_getmantv8hf_mask_round = 9943,
  CODE_FOR_avx512f_getmantv16sf = 9944,
  CODE_FOR_avx512f_getmantv16sf_round = 9945,
  CODE_FOR_avx512f_getmantv16sf_mask = 9946,
  CODE_FOR_avx512f_getmantv16sf_mask_round = 9947,
  CODE_FOR_avx512vl_getmantv8sf = 9948,
  CODE_FOR_avx512vl_getmantv8sf_round = 9949,
  CODE_FOR_avx512vl_getmantv8sf_mask = 9950,
  CODE_FOR_avx512vl_getmantv8sf_mask_round = 9951,
  CODE_FOR_avx512vl_getmantv4sf = 9952,
  CODE_FOR_avx512vl_getmantv4sf_round = 9953,
  CODE_FOR_avx512vl_getmantv4sf_mask = 9954,
  CODE_FOR_avx512vl_getmantv4sf_mask_round = 9955,
  CODE_FOR_avx512f_getmantv8df = 9956,
  CODE_FOR_avx512f_getmantv8df_round = 9957,
  CODE_FOR_avx512f_getmantv8df_mask = 9958,
  CODE_FOR_avx512f_getmantv8df_mask_round = 9959,
  CODE_FOR_avx512vl_getmantv4df = 9960,
  CODE_FOR_avx512vl_getmantv4df_round = 9961,
  CODE_FOR_avx512vl_getmantv4df_mask = 9962,
  CODE_FOR_avx512vl_getmantv4df_mask_round = 9963,
  CODE_FOR_avx512vl_getmantv2df = 9964,
  CODE_FOR_avx512vl_getmantv2df_round = 9965,
  CODE_FOR_avx512vl_getmantv2df_mask = 9966,
  CODE_FOR_avx512vl_getmantv2df_mask_round = 9967,
  CODE_FOR_avx512f_vgetmantv8hf = 9968,
  CODE_FOR_avx512f_vgetmantv8hf_mask = 9969,
  CODE_FOR_avx512f_vgetmantv8hf_round = 9970,
  CODE_FOR_avx512f_vgetmantv8hf_mask_round = 9971,
  CODE_FOR_avx512f_vgetmantv4sf = 9972,
  CODE_FOR_avx512f_vgetmantv4sf_mask = 9973,
  CODE_FOR_avx512f_vgetmantv4sf_round = 9974,
  CODE_FOR_avx512f_vgetmantv4sf_mask_round = 9975,
  CODE_FOR_avx512f_vgetmantv2df = 9976,
  CODE_FOR_avx512f_vgetmantv2df_mask = 9977,
  CODE_FOR_avx512f_vgetmantv2df_round = 9978,
  CODE_FOR_avx512f_vgetmantv2df_mask_round = 9979,
  CODE_FOR_avx512bw_dbpsadbwv8hi_mask = 9981,
  CODE_FOR_avx512bw_dbpsadbwv16hi_mask = 9983,
  CODE_FOR_avx512bw_dbpsadbwv32hi_mask = 9985,
  CODE_FOR_clzv16si2 = 9986,
  CODE_FOR_clzv16si2_mask = 9987,
  CODE_FOR_clzv8si2 = 9988,
  CODE_FOR_clzv8si2_mask = 9989,
  CODE_FOR_clzv4si2 = 9990,
  CODE_FOR_clzv4si2_mask = 9991,
  CODE_FOR_clzv8di2 = 9992,
  CODE_FOR_clzv8di2_mask = 9993,
  CODE_FOR_clzv4di2 = 9994,
  CODE_FOR_clzv4di2_mask = 9995,
  CODE_FOR_clzv2di2 = 9996,
  CODE_FOR_clzv2di2_mask = 9997,
  CODE_FOR_conflictv16si_mask = 9999,
  CODE_FOR_conflictv8si_mask = 10001,
  CODE_FOR_conflictv4si_mask = 10003,
  CODE_FOR_conflictv8di_mask = 10005,
  CODE_FOR_conflictv4di_mask = 10007,
  CODE_FOR_conflictv2di_mask = 10009,
  CODE_FOR_sha1msg1 = 10010,
  CODE_FOR_sha1msg2 = 10011,
  CODE_FOR_sha1nexte = 10012,
  CODE_FOR_sha1rnds4 = 10013,
  CODE_FOR_sha256msg1 = 10014,
  CODE_FOR_sha256msg2 = 10015,
  CODE_FOR_sha256rnds2 = 10016,
  CODE_FOR_vsm3msg1 = 10017,
  CODE_FOR_vsm3msg2 = 10018,
  CODE_FOR_vsm3rnds2 = 10019,
  CODE_FOR_vsha512msg1 = 10020,
  CODE_FOR_vsha512msg2 = 10021,
  CODE_FOR_vsha512rnds2 = 10022,
  CODE_FOR_vsm4key4_v16si = 10023,
  CODE_FOR_vsm4key4_v8si = 10024,
  CODE_FOR_vsm4key4_v4si = 10025,
  CODE_FOR_vsm4rnds4_v16si = 10026,
  CODE_FOR_vsm4rnds4_v8si = 10027,
  CODE_FOR_vsm4rnds4_v4si = 10028,
  CODE_FOR_avx512f_si512_si = 10029,
  CODE_FOR_avx512f_ps512_ps = 10030,
  CODE_FOR_avx512f_pd512_pd = 10031,
  CODE_FOR_avx512f_si512_256si = 10032,
  CODE_FOR_avx512f_ps512_256ps = 10033,
  CODE_FOR_avx512f_pd512_256pd = 10034,
  CODE_FOR_vpmadd52luqv8di = 10035,
  CODE_FOR_vpmadd52huqv8di = 10036,
  CODE_FOR_vpmadd52luqv4di = 10037,
  CODE_FOR_vpmadd52huqv4di = 10038,
  CODE_FOR_vpmadd52luqv2di = 10039,
  CODE_FOR_vpmadd52huqv2di = 10040,
  CODE_FOR_vpmadd52luqv8di_maskz_1 = 10041,
  CODE_FOR_vpmadd52huqv8di_maskz_1 = 10042,
  CODE_FOR_vpmadd52luqv4di_maskz_1 = 10043,
  CODE_FOR_vpmadd52huqv4di_maskz_1 = 10044,
  CODE_FOR_vpmadd52luqv2di_maskz_1 = 10045,
  CODE_FOR_vpmadd52huqv2di_maskz_1 = 10046,
  CODE_FOR_vpmadd52luqv8di_mask = 10047,
  CODE_FOR_vpmadd52huqv8di_mask = 10048,
  CODE_FOR_vpmadd52luqv4di_mask = 10049,
  CODE_FOR_vpmadd52huqv4di_mask = 10050,
  CODE_FOR_vpmadd52luqv2di_mask = 10051,
  CODE_FOR_vpmadd52huqv2di_mask = 10052,
  CODE_FOR_vpmultishiftqbv64qi = 10053,
  CODE_FOR_vpmultishiftqbv64qi_mask = 10054,
  CODE_FOR_vpmultishiftqbv16qi = 10055,
  CODE_FOR_vpmultishiftqbv16qi_mask = 10056,
  CODE_FOR_vpmultishiftqbv32qi = 10057,
  CODE_FOR_vpmultishiftqbv32qi_mask = 10058,
  CODE_FOR_vpopcountv16si = 10059,
  CODE_FOR_vpopcountv16si_mask = 10060,
  CODE_FOR_vpopcountv8si = 10061,
  CODE_FOR_vpopcountv8si_mask = 10062,
  CODE_FOR_vpopcountv4si = 10063,
  CODE_FOR_vpopcountv4si_mask = 10064,
  CODE_FOR_vpopcountv8di = 10065,
  CODE_FOR_vpopcountv8di_mask = 10066,
  CODE_FOR_vpopcountv4di = 10067,
  CODE_FOR_vpopcountv4di_mask = 10068,
  CODE_FOR_vpopcountv2di = 10069,
  CODE_FOR_vpopcountv2di_mask = 10070,
  CODE_FOR_vpopcountv64qi = 10079,
  CODE_FOR_vpopcountv64qi_mask = 10080,
  CODE_FOR_vpopcountv16qi = 10081,
  CODE_FOR_vpopcountv16qi_mask = 10082,
  CODE_FOR_vpopcountv32qi = 10083,
  CODE_FOR_vpopcountv32qi_mask = 10084,
  CODE_FOR_vpopcountv32hi = 10085,
  CODE_FOR_vpopcountv32hi_mask = 10086,
  CODE_FOR_vpopcountv16hi = 10087,
  CODE_FOR_vpopcountv16hi_mask = 10088,
  CODE_FOR_vpopcountv8hi = 10089,
  CODE_FOR_vpopcountv8hi_mask = 10090,
  CODE_FOR_vgf2p8affineinvqb_v64qi = 10091,
  CODE_FOR_vgf2p8affineinvqb_v64qi_mask = 10092,
  CODE_FOR_vgf2p8affineinvqb_v32qi = 10093,
  CODE_FOR_vgf2p8affineinvqb_v32qi_mask = 10094,
  CODE_FOR_vgf2p8affineinvqb_v16qi = 10095,
  CODE_FOR_vgf2p8affineinvqb_v16qi_mask = 10096,
  CODE_FOR_vgf2p8affineqb_v64qi = 10097,
  CODE_FOR_vgf2p8affineqb_v64qi_mask = 10098,
  CODE_FOR_vgf2p8affineqb_v32qi = 10099,
  CODE_FOR_vgf2p8affineqb_v32qi_mask = 10100,
  CODE_FOR_vgf2p8affineqb_v16qi = 10101,
  CODE_FOR_vgf2p8affineqb_v16qi_mask = 10102,
  CODE_FOR_vgf2p8mulb_v64qi = 10103,
  CODE_FOR_vgf2p8mulb_v64qi_mask = 10104,
  CODE_FOR_vgf2p8mulb_v32qi = 10105,
  CODE_FOR_vgf2p8mulb_v32qi_mask = 10106,
  CODE_FOR_vgf2p8mulb_v16qi = 10107,
  CODE_FOR_vgf2p8mulb_v16qi_mask = 10108,
  CODE_FOR_vpshrd_v32hi = 10109,
  CODE_FOR_vpshrd_v32hi_mask = 10110,
  CODE_FOR_vpshrd_v16si = 10111,
  CODE_FOR_vpshrd_v16si_mask = 10112,
  CODE_FOR_vpshrd_v8di = 10113,
  CODE_FOR_vpshrd_v8di_mask = 10114,
  CODE_FOR_vpshrd_v16hi = 10115,
  CODE_FOR_vpshrd_v16hi_mask = 10116,
  CODE_FOR_vpshrd_v8si = 10117,
  CODE_FOR_vpshrd_v8si_mask = 10118,
  CODE_FOR_vpshrd_v4di = 10119,
  CODE_FOR_vpshrd_v4di_mask = 10120,
  CODE_FOR_vpshrd_v8hi = 10121,
  CODE_FOR_vpshrd_v8hi_mask = 10122,
  CODE_FOR_vpshrd_v4si = 10123,
  CODE_FOR_vpshrd_v4si_mask = 10124,
  CODE_FOR_vpshrd_v2di = 10125,
  CODE_FOR_vpshrd_v2di_mask = 10126,
  CODE_FOR_vpshld_v32hi = 10127,
  CODE_FOR_vpshld_v32hi_mask = 10128,
  CODE_FOR_vpshld_v16si = 10129,
  CODE_FOR_vpshld_v16si_mask = 10130,
  CODE_FOR_vpshld_v8di = 10131,
  CODE_FOR_vpshld_v8di_mask = 10132,
  CODE_FOR_vpshld_v16hi = 10133,
  CODE_FOR_vpshld_v16hi_mask = 10134,
  CODE_FOR_vpshld_v8si = 10135,
  CODE_FOR_vpshld_v8si_mask = 10136,
  CODE_FOR_vpshld_v4di = 10137,
  CODE_FOR_vpshld_v4di_mask = 10138,
  CODE_FOR_vpshld_v8hi = 10139,
  CODE_FOR_vpshld_v8hi_mask = 10140,
  CODE_FOR_vpshld_v4si = 10141,
  CODE_FOR_vpshld_v4si_mask = 10142,
  CODE_FOR_vpshld_v2di = 10143,
  CODE_FOR_vpshld_v2di_mask = 10144,
  CODE_FOR_vpshrdv_v32hi = 10145,
  CODE_FOR_vpshrdv_v16si = 10146,
  CODE_FOR_vpshrdv_v8di = 10147,
  CODE_FOR_vpshrdv_v16hi = 10148,
  CODE_FOR_vpshrdv_v8si = 10149,
  CODE_FOR_vpshrdv_v4di = 10150,
  CODE_FOR_vpshrdv_v8hi = 10151,
  CODE_FOR_vpshrdv_v4si = 10152,
  CODE_FOR_vpshrdv_v2di = 10153,
  CODE_FOR_vpshrdv_v32hi_mask = 10154,
  CODE_FOR_vpshrdv_v16si_mask = 10155,
  CODE_FOR_vpshrdv_v8di_mask = 10156,
  CODE_FOR_vpshrdv_v16hi_mask = 10157,
  CODE_FOR_vpshrdv_v8si_mask = 10158,
  CODE_FOR_vpshrdv_v4di_mask = 10159,
  CODE_FOR_vpshrdv_v8hi_mask = 10160,
  CODE_FOR_vpshrdv_v4si_mask = 10161,
  CODE_FOR_vpshrdv_v2di_mask = 10162,
  CODE_FOR_vpshrdv_v32hi_maskz_1 = 10163,
  CODE_FOR_vpshrdv_v16si_maskz_1 = 10164,
  CODE_FOR_vpshrdv_v8di_maskz_1 = 10165,
  CODE_FOR_vpshrdv_v16hi_maskz_1 = 10166,
  CODE_FOR_vpshrdv_v8si_maskz_1 = 10167,
  CODE_FOR_vpshrdv_v4di_maskz_1 = 10168,
  CODE_FOR_vpshrdv_v8hi_maskz_1 = 10169,
  CODE_FOR_vpshrdv_v4si_maskz_1 = 10170,
  CODE_FOR_vpshrdv_v2di_maskz_1 = 10171,
  CODE_FOR_vpshldv_v32hi = 10172,
  CODE_FOR_vpshldv_v16si = 10173,
  CODE_FOR_vpshldv_v8di = 10174,
  CODE_FOR_vpshldv_v16hi = 10175,
  CODE_FOR_vpshldv_v8si = 10176,
  CODE_FOR_vpshldv_v4di = 10177,
  CODE_FOR_vpshldv_v8hi = 10178,
  CODE_FOR_vpshldv_v4si = 10179,
  CODE_FOR_vpshldv_v2di = 10180,
  CODE_FOR_vpshldv_v32hi_mask = 10181,
  CODE_FOR_vpshldv_v16si_mask = 10182,
  CODE_FOR_vpshldv_v8di_mask = 10183,
  CODE_FOR_vpshldv_v16hi_mask = 10184,
  CODE_FOR_vpshldv_v8si_mask = 10185,
  CODE_FOR_vpshldv_v4di_mask = 10186,
  CODE_FOR_vpshldv_v8hi_mask = 10187,
  CODE_FOR_vpshldv_v4si_mask = 10188,
  CODE_FOR_vpshldv_v2di_mask = 10189,
  CODE_FOR_vpshldv_v32hi_maskz_1 = 10190,
  CODE_FOR_vpshldv_v16si_maskz_1 = 10191,
  CODE_FOR_vpshldv_v8di_maskz_1 = 10192,
  CODE_FOR_vpshldv_v16hi_maskz_1 = 10193,
  CODE_FOR_vpshldv_v8si_maskz_1 = 10194,
  CODE_FOR_vpshldv_v4di_maskz_1 = 10195,
  CODE_FOR_vpshldv_v8hi_maskz_1 = 10196,
  CODE_FOR_vpshldv_v4si_maskz_1 = 10197,
  CODE_FOR_vpshldv_v2di_maskz_1 = 10198,
  CODE_FOR_vpdpbusd_v16si = 10199,
  CODE_FOR_vpdpbusd_v8si = 10200,
  CODE_FOR_vpdpbusd_v4si = 10201,
  CODE_FOR_vpdpbusd_v16si_mask = 10202,
  CODE_FOR_vpdpbusd_v8si_mask = 10203,
  CODE_FOR_vpdpbusd_v4si_mask = 10204,
  CODE_FOR_vpdpbusd_v16si_maskz_1 = 10205,
  CODE_FOR_vpdpbusd_v8si_maskz_1 = 10206,
  CODE_FOR_vpdpbusd_v4si_maskz_1 = 10207,
  CODE_FOR_vpdpbusds_v16si = 10208,
  CODE_FOR_vpdpbusds_v8si = 10209,
  CODE_FOR_vpdpbusds_v4si = 10210,
  CODE_FOR_vpdpbusds_v16si_mask = 10211,
  CODE_FOR_vpdpbusds_v8si_mask = 10212,
  CODE_FOR_vpdpbusds_v4si_mask = 10213,
  CODE_FOR_vpdpbusds_v16si_maskz_1 = 10214,
  CODE_FOR_vpdpbusds_v8si_maskz_1 = 10215,
  CODE_FOR_vpdpbusds_v4si_maskz_1 = 10216,
  CODE_FOR_vpdpwssd_v16si = 10217,
  CODE_FOR_vpdpwssd_v8si = 10218,
  CODE_FOR_vpdpwssd_v4si = 10219,
  CODE_FOR_vpdpwssd_v16si_mask = 10220,
  CODE_FOR_vpdpwssd_v8si_mask = 10221,
  CODE_FOR_vpdpwssd_v4si_mask = 10222,
  CODE_FOR_vpdpwssd_v16si_maskz_1 = 10223,
  CODE_FOR_vpdpwssd_v8si_maskz_1 = 10224,
  CODE_FOR_vpdpwssd_v4si_maskz_1 = 10225,
  CODE_FOR_vpdpwssds_v16si = 10226,
  CODE_FOR_vpdpwssds_v8si = 10227,
  CODE_FOR_vpdpwssds_v4si = 10228,
  CODE_FOR_vpdpwssds_v16si_mask = 10229,
  CODE_FOR_vpdpwssds_v8si_mask = 10230,
  CODE_FOR_vpdpwssds_v4si_mask = 10231,
  CODE_FOR_vpdpwssds_v16si_maskz_1 = 10232,
  CODE_FOR_vpdpwssds_v8si_maskz_1 = 10233,
  CODE_FOR_vpdpwssds_v4si_maskz_1 = 10234,
  CODE_FOR_vaesdec_v32qi = 10235,
  CODE_FOR_vaesdec_v16qi = 10236,
  CODE_FOR_vaesdec_v64qi = 10237,
  CODE_FOR_vaesdeclast_v32qi = 10238,
  CODE_FOR_vaesdeclast_v16qi = 10239,
  CODE_FOR_vaesdeclast_v64qi = 10240,
  CODE_FOR_vaesenc_v32qi = 10241,
  CODE_FOR_vaesenc_v16qi = 10242,
  CODE_FOR_vaesenc_v64qi = 10243,
  CODE_FOR_vaesenclast_v32qi = 10244,
  CODE_FOR_vaesenclast_v16qi = 10245,
  CODE_FOR_vaesenclast_v64qi = 10246,
  CODE_FOR_vpclmulqdq_v8di = 10247,
  CODE_FOR_vpclmulqdq_v4di = 10248,
  CODE_FOR_vpclmulqdq_v2di = 10249,
  CODE_FOR_avx512vl_vpshufbitqmbv64qi = 10250,
  CODE_FOR_avx512vl_vpshufbitqmbv64qi_mask = 10251,
  CODE_FOR_avx512vl_vpshufbitqmbv16qi = 10252,
  CODE_FOR_avx512vl_vpshufbitqmbv16qi_mask = 10253,
  CODE_FOR_avx512vl_vpshufbitqmbv32qi = 10254,
  CODE_FOR_avx512vl_vpshufbitqmbv32qi_mask = 10255,
  CODE_FOR_avx512vp2intersect_2intersectv8di = 10258,
  CODE_FOR_avx512vp2intersect_2intersectv4di = 10259,
  CODE_FOR_avx512vp2intersect_2intersectv2di = 10260,
  CODE_FOR_avx512vp2intersect_2intersectv8si = 10261,
  CODE_FOR_avx512vp2intersect_2intersectv4si = 10262,
  CODE_FOR_avx512vp2intersect_2intersectv16si = 10263,
  CODE_FOR_avx512f_cvtne2ps2bf16_v32bf = 10264,
  CODE_FOR_avx512f_cvtne2ps2bf16_v32bf_mask = 10265,
  CODE_FOR_avx512f_cvtne2ps2bf16_v16bf = 10266,
  CODE_FOR_avx512f_cvtne2ps2bf16_v16bf_mask = 10267,
  CODE_FOR_avx512f_cvtne2ps2bf16_v8bf = 10268,
  CODE_FOR_avx512f_cvtne2ps2bf16_v8bf_mask = 10269,
  CODE_FOR_avx512f_cvtneps2bf16_v4sf_mask_1 = 10271,
  CODE_FOR_vcvtneps2bf16_v8sf = 10272,
  CODE_FOR_avx512f_cvtneps2bf16_v16sf = 10273,
  CODE_FOR_avx512f_cvtneps2bf16_v16sf_mask = 10274,
  CODE_FOR_avx512f_cvtneps2bf16_v8sf = 10275,
  CODE_FOR_avx512f_cvtneps2bf16_v8sf_mask = 10276,
  CODE_FOR_avx512f_dpbf16ps_v16sf = 10277,
  CODE_FOR_avx512f_dpbf16ps_v16sf_maskz_1 = 10278,
  CODE_FOR_avx512f_dpbf16ps_v8sf = 10279,
  CODE_FOR_avx512f_dpbf16ps_v8sf_maskz_1 = 10280,
  CODE_FOR_avx512f_dpbf16ps_v4sf = 10281,
  CODE_FOR_avx512f_dpbf16ps_v4sf_maskz_1 = 10282,
  CODE_FOR_avx512f_dpbf16ps_v16sf_mask = 10283,
  CODE_FOR_avx512f_dpbf16ps_v8sf_mask = 10284,
  CODE_FOR_avx512f_dpbf16ps_v4sf_mask = 10285,
  CODE_FOR_loadiwkey = 10286,
  CODE_FOR_aesdec128klu8 = 10289,
  CODE_FOR_aesdec256klu8 = 10290,
  CODE_FOR_aesenc128klu8 = 10291,
  CODE_FOR_aesenc256klu8 = 10292,
  CODE_FOR_vpdpbssd_v8si = 10297,
  CODE_FOR_vpdpbssds_v8si = 10298,
  CODE_FOR_vpdpbsud_v8si = 10299,
  CODE_FOR_vpdpbsuds_v8si = 10300,
  CODE_FOR_vpdpbuud_v8si = 10301,
  CODE_FOR_vpdpbuuds_v8si = 10302,
  CODE_FOR_vpdpbssd_v4si = 10303,
  CODE_FOR_vpdpbssds_v4si = 10304,
  CODE_FOR_vpdpbsud_v4si = 10305,
  CODE_FOR_vpdpbsuds_v4si = 10306,
  CODE_FOR_vpdpbuud_v4si = 10307,
  CODE_FOR_vpdpbuuds_v4si = 10308,
  CODE_FOR_vpdpbssd_v16si = 10309,
  CODE_FOR_vpdpbssds_v16si = 10310,
  CODE_FOR_vpdpbsud_v16si = 10311,
  CODE_FOR_vpdpbsuds_v16si = 10312,
  CODE_FOR_vpdpbuud_v16si = 10313,
  CODE_FOR_vpdpbuuds_v16si = 10314,
  CODE_FOR_vpdpbssd_v16si_mask = 10315,
  CODE_FOR_vpdpbssds_v16si_mask = 10316,
  CODE_FOR_vpdpbsud_v16si_mask = 10317,
  CODE_FOR_vpdpbsuds_v16si_mask = 10318,
  CODE_FOR_vpdpbuud_v16si_mask = 10319,
  CODE_FOR_vpdpbuuds_v16si_mask = 10320,
  CODE_FOR_vpdpbssd_v8si_mask = 10321,
  CODE_FOR_vpdpbssds_v8si_mask = 10322,
  CODE_FOR_vpdpbsud_v8si_mask = 10323,
  CODE_FOR_vpdpbsuds_v8si_mask = 10324,
  CODE_FOR_vpdpbuud_v8si_mask = 10325,
  CODE_FOR_vpdpbuuds_v8si_mask = 10326,
  CODE_FOR_vpdpbssd_v4si_mask = 10327,
  CODE_FOR_vpdpbssds_v4si_mask = 10328,
  CODE_FOR_vpdpbsud_v4si_mask = 10329,
  CODE_FOR_vpdpbsuds_v4si_mask = 10330,
  CODE_FOR_vpdpbuud_v4si_mask = 10331,
  CODE_FOR_vpdpbuuds_v4si_mask = 10332,
  CODE_FOR_vbcstnebf162ps_v8sf = 10351,
  CODE_FOR_vbcstnebf162ps_v4sf = 10352,
  CODE_FOR_vbcstnesh2ps_v8sf = 10353,
  CODE_FOR_vbcstnesh2ps_v4sf = 10354,
  CODE_FOR_vcvtneeph2ps_v8hf = 10355,
  CODE_FOR_vcvtneebf162ps_v8bf = 10356,
  CODE_FOR_vcvtneeph2ps_v16hf = 10357,
  CODE_FOR_vcvtneebf162ps_v16bf = 10358,
  CODE_FOR_vcvtneoph2ps_v8hf = 10359,
  CODE_FOR_vcvtneobf162ps_v8bf = 10360,
  CODE_FOR_vcvtneoph2ps_v16hf = 10361,
  CODE_FOR_vcvtneobf162ps_v16bf = 10362,
  CODE_FOR_avx10_2_cvt2ps2phx_v32hf = 10363,
  CODE_FOR_avx10_2_cvt2ps2phx_v32hf_round = 10364,
  CODE_FOR_avx10_2_cvt2ps2phx_v32hf_mask = 10365,
  CODE_FOR_avx10_2_cvt2ps2phx_v32hf_mask_round = 10366,
  CODE_FOR_avx10_2_cvt2ps2phx_v16hf = 10367,
   CODE_FOR_avx10_2_cvt2ps2phx_v16hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvt2ps2phx_v16hf_mask = 10368,
   CODE_FOR_avx10_2_cvt2ps2phx_v16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvt2ps2phx_v8hf = 10369,
   CODE_FOR_avx10_2_cvt2ps2phx_v8hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvt2ps2phx_v8hf_mask = 10370,
   CODE_FOR_avx10_2_cvt2ps2phx_v8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_vcvt2ph2bf8v32hf = 10371,
  CODE_FOR_vcvt2ph2bf8v32hf_mask = 10372,
  CODE_FOR_vcvt2ph2bf8sv32hf = 10373,
  CODE_FOR_vcvt2ph2bf8sv32hf_mask = 10374,
  CODE_FOR_vcvt2ph2hf8v32hf = 10375,
  CODE_FOR_vcvt2ph2hf8v32hf_mask = 10376,
  CODE_FOR_vcvt2ph2hf8sv32hf = 10377,
  CODE_FOR_vcvt2ph2hf8sv32hf_mask = 10378,
  CODE_FOR_vcvt2ph2bf8v16hf = 10379,
  CODE_FOR_vcvt2ph2bf8v16hf_mask = 10380,
  CODE_FOR_vcvt2ph2bf8sv16hf = 10381,
  CODE_FOR_vcvt2ph2bf8sv16hf_mask = 10382,
  CODE_FOR_vcvt2ph2hf8v16hf = 10383,
  CODE_FOR_vcvt2ph2hf8v16hf_mask = 10384,
  CODE_FOR_vcvt2ph2hf8sv16hf = 10385,
  CODE_FOR_vcvt2ph2hf8sv16hf_mask = 10386,
  CODE_FOR_vcvt2ph2bf8v8hf = 10387,
  CODE_FOR_vcvt2ph2bf8v8hf_mask = 10388,
  CODE_FOR_vcvt2ph2bf8sv8hf = 10389,
  CODE_FOR_vcvt2ph2bf8sv8hf_mask = 10390,
  CODE_FOR_vcvt2ph2hf8v8hf = 10391,
  CODE_FOR_vcvt2ph2hf8v8hf_mask = 10392,
  CODE_FOR_vcvt2ph2hf8sv8hf = 10393,
  CODE_FOR_vcvt2ph2hf8sv8hf_mask = 10394,
  CODE_FOR_vcvtbiasph2bf8v32hf = 10403,
  CODE_FOR_vcvtbiasph2bf8v32hf_mask = 10404,
  CODE_FOR_vcvtbiasph2bf8sv32hf = 10405,
  CODE_FOR_vcvtbiasph2bf8sv32hf_mask = 10406,
  CODE_FOR_vcvtbiasph2hf8v32hf = 10407,
  CODE_FOR_vcvtbiasph2hf8v32hf_mask = 10408,
  CODE_FOR_vcvtbiasph2hf8sv32hf = 10409,
  CODE_FOR_vcvtbiasph2hf8sv32hf_mask = 10410,
  CODE_FOR_vcvtbiasph2bf8v16hf = 10411,
  CODE_FOR_vcvtbiasph2bf8v16hf_mask = 10412,
  CODE_FOR_vcvtbiasph2bf8sv16hf = 10413,
  CODE_FOR_vcvtbiasph2bf8sv16hf_mask = 10414,
  CODE_FOR_vcvtbiasph2hf8v16hf = 10415,
  CODE_FOR_vcvtbiasph2hf8v16hf_mask = 10416,
  CODE_FOR_vcvtbiasph2hf8sv16hf = 10417,
  CODE_FOR_vcvtbiasph2hf8sv16hf_mask = 10418,
  CODE_FOR_vcvtph2bf8v16hf = 10427,
  CODE_FOR_vcvtph2bf8v16hf_mask = 10428,
  CODE_FOR_vcvtph2bf8sv16hf = 10429,
  CODE_FOR_vcvtph2bf8sv16hf_mask = 10430,
  CODE_FOR_vcvtph2hf8v16hf = 10431,
  CODE_FOR_vcvtph2hf8v16hf_mask = 10432,
  CODE_FOR_vcvtph2hf8sv16hf = 10433,
  CODE_FOR_vcvtph2hf8sv16hf_mask = 10434,
  CODE_FOR_vcvtph2bf8v32hf = 10435,
  CODE_FOR_vcvtph2bf8v32hf_mask = 10436,
  CODE_FOR_vcvtph2bf8sv32hf = 10437,
  CODE_FOR_vcvtph2bf8sv32hf_mask = 10438,
  CODE_FOR_vcvtph2hf8v32hf = 10439,
  CODE_FOR_vcvtph2hf8v32hf_mask = 10440,
  CODE_FOR_vcvtph2hf8sv32hf = 10441,
  CODE_FOR_vcvtph2hf8sv32hf_mask = 10442,
  CODE_FOR_vcvthf82phv32hf = 10443,
  CODE_FOR_vcvthf82phv32hf_mask = 10444,
  CODE_FOR_vcvthf82phv16hf = 10445,
  CODE_FOR_vcvthf82phv16hf_mask = 10446,
  CODE_FOR_vcvthf82phv8hf = 10447,
  CODE_FOR_vcvthf82phv8hf_mask = 10448,
  CODE_FOR_vpdpwusd_v8si = 10449,
  CODE_FOR_vpdpwusds_v8si = 10450,
  CODE_FOR_vpdpwsud_v8si = 10451,
  CODE_FOR_vpdpwsuds_v8si = 10452,
  CODE_FOR_vpdpwuud_v8si = 10453,
  CODE_FOR_vpdpwuuds_v8si = 10454,
  CODE_FOR_vpdpwusd_v4si = 10455,
  CODE_FOR_vpdpwusds_v4si = 10456,
  CODE_FOR_vpdpwsud_v4si = 10457,
  CODE_FOR_vpdpwsuds_v4si = 10458,
  CODE_FOR_vpdpwuud_v4si = 10459,
  CODE_FOR_vpdpwuuds_v4si = 10460,
  CODE_FOR_vpdpwusd_v16si = 10461,
  CODE_FOR_vpdpwusds_v16si = 10462,
  CODE_FOR_vpdpwsud_v16si = 10463,
  CODE_FOR_vpdpwsuds_v16si = 10464,
  CODE_FOR_vpdpwuud_v16si = 10465,
  CODE_FOR_vpdpwuuds_v16si = 10466,
  CODE_FOR_vpdpwusd_v16si_mask = 10467,
  CODE_FOR_vpdpwusds_v16si_mask = 10468,
  CODE_FOR_vpdpwsud_v16si_mask = 10469,
  CODE_FOR_vpdpwsuds_v16si_mask = 10470,
  CODE_FOR_vpdpwuud_v16si_mask = 10471,
  CODE_FOR_vpdpwuuds_v16si_mask = 10472,
  CODE_FOR_vpdpwusd_v8si_mask = 10473,
  CODE_FOR_vpdpwusds_v8si_mask = 10474,
  CODE_FOR_vpdpwsud_v8si_mask = 10475,
  CODE_FOR_vpdpwsuds_v8si_mask = 10476,
  CODE_FOR_vpdpwuud_v8si_mask = 10477,
  CODE_FOR_vpdpwuuds_v8si_mask = 10478,
  CODE_FOR_vpdpwusd_v4si_mask = 10479,
  CODE_FOR_vpdpwusds_v4si_mask = 10480,
  CODE_FOR_vpdpwsud_v4si_mask = 10481,
  CODE_FOR_vpdpwsuds_v4si_mask = 10482,
  CODE_FOR_vpdpwuud_v4si_mask = 10483,
  CODE_FOR_vpdpwuuds_v4si_mask = 10484,
  CODE_FOR_vdpphps_v16sf = 10503,
  CODE_FOR_vdpphps_v8sf = 10504,
  CODE_FOR_vdpphps_v4sf = 10505,
  CODE_FOR_vdpphps_v16sf_mask = 10506,
  CODE_FOR_vdpphps_v8sf_mask = 10507,
  CODE_FOR_vdpphps_v4sf_mask = 10508,
  CODE_FOR_vdpphps_v16sf_maskz_1 = 10509,
  CODE_FOR_vdpphps_v8sf_maskz_1 = 10510,
  CODE_FOR_vdpphps_v4sf_maskz_1 = 10511,
  CODE_FOR_avx10_2_scalefbf16_v32bf = 10512,
  CODE_FOR_avx10_2_scalefbf16_v32bf_mask = 10513,
  CODE_FOR_avx10_2_scalefbf16_v16bf = 10514,
  CODE_FOR_avx10_2_scalefbf16_v16bf_mask = 10515,
  CODE_FOR_avx10_2_scalefbf16_v8bf = 10516,
  CODE_FOR_avx10_2_scalefbf16_v8bf_mask = 10517,
  CODE_FOR_avx10_2_smaxbf16_v32bf = 10518,
  CODE_FOR_avx10_2_smaxbf16_v32bf_mask = 10519,
  CODE_FOR_avx10_2_sminbf16_v32bf = 10520,
  CODE_FOR_avx10_2_sminbf16_v32bf_mask = 10521,
  CODE_FOR_avx10_2_smaxbf16_v16bf = 10522,
  CODE_FOR_avx10_2_smaxbf16_v16bf_mask = 10523,
  CODE_FOR_avx10_2_sminbf16_v16bf = 10524,
  CODE_FOR_avx10_2_sminbf16_v16bf_mask = 10525,
  CODE_FOR_avx10_2_smaxbf16_v8bf = 10526,
  CODE_FOR_avx10_2_smaxbf16_v8bf_mask = 10527,
  CODE_FOR_avx10_2_sminbf16_v8bf = 10528,
  CODE_FOR_avx10_2_sminbf16_v8bf_mask = 10529,
  CODE_FOR_avx10_2_addbf16_v32bf = 10530,
  CODE_FOR_avx10_2_addbf16_v32bf_mask = 10531,
  CODE_FOR_avx10_2_subbf16_v32bf = 10532,
  CODE_FOR_avx10_2_subbf16_v32bf_mask = 10533,
  CODE_FOR_avx10_2_mulbf16_v32bf = 10534,
  CODE_FOR_avx10_2_mulbf16_v32bf_mask = 10535,
  CODE_FOR_avx10_2_divbf16_v32bf = 10536,
  CODE_FOR_avx10_2_divbf16_v32bf_mask = 10537,
  CODE_FOR_avx10_2_addbf16_v16bf = 10538,
  CODE_FOR_avx10_2_addbf16_v16bf_mask = 10539,
  CODE_FOR_avx10_2_subbf16_v16bf = 10540,
  CODE_FOR_avx10_2_subbf16_v16bf_mask = 10541,
  CODE_FOR_avx10_2_mulbf16_v16bf = 10542,
  CODE_FOR_avx10_2_mulbf16_v16bf_mask = 10543,
  CODE_FOR_avx10_2_divbf16_v16bf = 10544,
  CODE_FOR_avx10_2_divbf16_v16bf_mask = 10545,
  CODE_FOR_avx10_2_addbf16_v8bf = 10546,
  CODE_FOR_avx10_2_addbf16_v8bf_mask = 10547,
  CODE_FOR_avx10_2_subbf16_v8bf = 10548,
  CODE_FOR_avx10_2_subbf16_v8bf_mask = 10549,
  CODE_FOR_avx10_2_mulbf16_v8bf = 10550,
  CODE_FOR_avx10_2_mulbf16_v8bf_mask = 10551,
  CODE_FOR_avx10_2_divbf16_v8bf = 10552,
  CODE_FOR_avx10_2_divbf16_v8bf_mask = 10553,
  CODE_FOR_avx10_2_fmaddbf16_v32bf = 10554,
  CODE_FOR_avx10_2_fmaddbf16_v32bf_maskz_1 = 10555,
  CODE_FOR_avx10_2_fmaddbf16_v16bf = 10556,
  CODE_FOR_avx10_2_fmaddbf16_v16bf_maskz_1 = 10557,
  CODE_FOR_avx10_2_fmaddbf16_v8bf = 10558,
  CODE_FOR_avx10_2_fmaddbf16_v8bf_maskz_1 = 10559,
  CODE_FOR_avx10_2_fmaddbf16_v32bf_mask = 10560,
  CODE_FOR_avx10_2_fmaddbf16_v16bf_mask = 10561,
  CODE_FOR_avx10_2_fmaddbf16_v8bf_mask = 10562,
  CODE_FOR_avx10_2_fmaddbf16_v32bf_mask3 = 10563,
  CODE_FOR_avx10_2_fmaddbf16_v16bf_mask3 = 10564,
  CODE_FOR_avx10_2_fmaddbf16_v8bf_mask3 = 10565,
  CODE_FOR_avx10_2_fnmaddbf16_v32bf = 10566,
  CODE_FOR_avx10_2_fnmaddbf16_v32bf_maskz_1 = 10567,
  CODE_FOR_avx10_2_fnmaddbf16_v16bf = 10568,
  CODE_FOR_avx10_2_fnmaddbf16_v16bf_maskz_1 = 10569,
  CODE_FOR_avx10_2_fnmaddbf16_v8bf = 10570,
  CODE_FOR_avx10_2_fnmaddbf16_v8bf_maskz_1 = 10571,
  CODE_FOR_avx10_2_fnmaddbf16_v32bf_mask = 10572,
  CODE_FOR_avx10_2_fnmaddbf16_v16bf_mask = 10573,
  CODE_FOR_avx10_2_fnmaddbf16_v8bf_mask = 10574,
  CODE_FOR_avx10_2_fnmaddbf16_v32bf_mask3 = 10575,
  CODE_FOR_avx10_2_fnmaddbf16_v16bf_mask3 = 10576,
  CODE_FOR_avx10_2_fnmaddbf16_v8bf_mask3 = 10577,
  CODE_FOR_avx10_2_fmsubbf16_v32bf = 10578,
  CODE_FOR_avx10_2_fmsubbf16_v32bf_maskz_1 = 10579,
  CODE_FOR_avx10_2_fmsubbf16_v16bf = 10580,
  CODE_FOR_avx10_2_fmsubbf16_v16bf_maskz_1 = 10581,
  CODE_FOR_avx10_2_fmsubbf16_v8bf = 10582,
  CODE_FOR_avx10_2_fmsubbf16_v8bf_maskz_1 = 10583,
  CODE_FOR_avx10_2_fmsubbf16_v32bf_mask = 10584,
  CODE_FOR_avx10_2_fmsubbf16_v16bf_mask = 10585,
  CODE_FOR_avx10_2_fmsubbf16_v8bf_mask = 10586,
  CODE_FOR_avx10_2_fmsubbf16_v32bf_mask3 = 10587,
  CODE_FOR_avx10_2_fmsubbf16_v16bf_mask3 = 10588,
  CODE_FOR_avx10_2_fmsubbf16_v8bf_mask3 = 10589,
  CODE_FOR_avx10_2_fnmsubbf16_v32bf = 10590,
  CODE_FOR_avx10_2_fnmsubbf16_v32bf_maskz_1 = 10591,
  CODE_FOR_avx10_2_fnmsubbf16_v16bf = 10592,
  CODE_FOR_avx10_2_fnmsubbf16_v16bf_maskz_1 = 10593,
  CODE_FOR_avx10_2_fnmsubbf16_v8bf = 10594,
  CODE_FOR_avx10_2_fnmsubbf16_v8bf_maskz_1 = 10595,
  CODE_FOR_avx10_2_fnmsubbf16_v32bf_mask = 10596,
  CODE_FOR_avx10_2_fnmsubbf16_v16bf_mask = 10597,
  CODE_FOR_avx10_2_fnmsubbf16_v8bf_mask = 10598,
  CODE_FOR_avx10_2_fnmsubbf16_v32bf_mask3 = 10599,
  CODE_FOR_avx10_2_fnmsubbf16_v16bf_mask3 = 10600,
  CODE_FOR_avx10_2_fnmsubbf16_v8bf_mask3 = 10601,
  CODE_FOR_avx10_2_rsqrtbf16_v32bf = 10602,
  CODE_FOR_avx10_2_rsqrtbf16_v32bf_mask = 10603,
  CODE_FOR_avx10_2_rsqrtbf16_v16bf = 10604,
  CODE_FOR_avx10_2_rsqrtbf16_v16bf_mask = 10605,
  CODE_FOR_avx10_2_rsqrtbf16_v8bf = 10606,
  CODE_FOR_avx10_2_rsqrtbf16_v8bf_mask = 10607,
  CODE_FOR_avx10_2_sqrtbf16_v32bf = 10608,
  CODE_FOR_avx10_2_sqrtbf16_v32bf_mask = 10609,
  CODE_FOR_avx10_2_sqrtbf16_v16bf = 10610,
  CODE_FOR_avx10_2_sqrtbf16_v16bf_mask = 10611,
  CODE_FOR_avx10_2_sqrtbf16_v8bf = 10612,
  CODE_FOR_avx10_2_sqrtbf16_v8bf_mask = 10613,
  CODE_FOR_avx10_2_rcpbf16_v32bf = 10614,
  CODE_FOR_avx10_2_rcpbf16_v32bf_mask = 10615,
  CODE_FOR_avx10_2_rcpbf16_v16bf = 10616,
  CODE_FOR_avx10_2_rcpbf16_v16bf_mask = 10617,
  CODE_FOR_avx10_2_rcpbf16_v8bf = 10618,
  CODE_FOR_avx10_2_rcpbf16_v8bf_mask = 10619,
  CODE_FOR_avx10_2_getexpbf16_v32bf = 10620,
  CODE_FOR_avx10_2_getexpbf16_v32bf_mask = 10621,
  CODE_FOR_avx10_2_getexpbf16_v16bf = 10622,
  CODE_FOR_avx10_2_getexpbf16_v16bf_mask = 10623,
  CODE_FOR_avx10_2_getexpbf16_v8bf = 10624,
  CODE_FOR_avx10_2_getexpbf16_v8bf_mask = 10625,
  CODE_FOR_avx10_2_rndscalebf16_v32bf = 10626,
  CODE_FOR_avx10_2_rndscalebf16_v32bf_mask = 10627,
  CODE_FOR_avx10_2_reducebf16_v32bf = 10628,
  CODE_FOR_avx10_2_reducebf16_v32bf_mask = 10629,
  CODE_FOR_avx10_2_getmantbf16_v32bf = 10630,
  CODE_FOR_avx10_2_getmantbf16_v32bf_mask = 10631,
  CODE_FOR_avx10_2_rndscalebf16_v16bf = 10632,
  CODE_FOR_avx10_2_rndscalebf16_v16bf_mask = 10633,
  CODE_FOR_avx10_2_reducebf16_v16bf = 10634,
  CODE_FOR_avx10_2_reducebf16_v16bf_mask = 10635,
  CODE_FOR_avx10_2_getmantbf16_v16bf = 10636,
  CODE_FOR_avx10_2_getmantbf16_v16bf_mask = 10637,
  CODE_FOR_avx10_2_rndscalebf16_v8bf = 10638,
  CODE_FOR_avx10_2_rndscalebf16_v8bf_mask = 10639,
  CODE_FOR_avx10_2_reducebf16_v8bf = 10640,
  CODE_FOR_avx10_2_reducebf16_v8bf_mask = 10641,
  CODE_FOR_avx10_2_getmantbf16_v8bf = 10642,
  CODE_FOR_avx10_2_getmantbf16_v8bf_mask = 10643,
  CODE_FOR_avx10_2_fpclassbf16_v32bf = 10644,
  CODE_FOR_avx10_2_fpclassbf16_v32bf_mask = 10645,
  CODE_FOR_avx10_2_fpclassbf16_v16bf = 10646,
  CODE_FOR_avx10_2_fpclassbf16_v16bf_mask = 10647,
  CODE_FOR_avx10_2_fpclassbf16_v8bf = 10648,
  CODE_FOR_avx10_2_fpclassbf16_v8bf_mask = 10649,
  CODE_FOR_avx10_2_cmpbf16_v32bf = 10650,
  CODE_FOR_avx10_2_cmpbf16_v32bf_mask = 10651,
  CODE_FOR_avx10_2_cmpbf16_v16bf = 10652,
  CODE_FOR_avx10_2_cmpbf16_v16bf_mask = 10653,
  CODE_FOR_avx10_2_cmpbf16_v8bf = 10654,
  CODE_FOR_avx10_2_cmpbf16_v8bf_mask = 10655,
  CODE_FOR_avx10_2_cvtbf162ibsv32bf = 10656,
  CODE_FOR_avx10_2_cvtbf162ibsv32bf_mask = 10657,
  CODE_FOR_avx10_2_cvtbf162iubsv32bf = 10658,
  CODE_FOR_avx10_2_cvtbf162iubsv32bf_mask = 10659,
  CODE_FOR_avx10_2_cvttbf162ibsv32bf = 10660,
  CODE_FOR_avx10_2_cvttbf162ibsv32bf_mask = 10661,
  CODE_FOR_avx10_2_cvttbf162iubsv32bf = 10662,
  CODE_FOR_avx10_2_cvttbf162iubsv32bf_mask = 10663,
  CODE_FOR_avx10_2_cvtbf162ibsv16bf = 10664,
  CODE_FOR_avx10_2_cvtbf162ibsv16bf_mask = 10665,
  CODE_FOR_avx10_2_cvtbf162iubsv16bf = 10666,
  CODE_FOR_avx10_2_cvtbf162iubsv16bf_mask = 10667,
  CODE_FOR_avx10_2_cvttbf162ibsv16bf = 10668,
  CODE_FOR_avx10_2_cvttbf162ibsv16bf_mask = 10669,
  CODE_FOR_avx10_2_cvttbf162iubsv16bf = 10670,
  CODE_FOR_avx10_2_cvttbf162iubsv16bf_mask = 10671,
  CODE_FOR_avx10_2_cvtbf162ibsv8bf = 10672,
  CODE_FOR_avx10_2_cvtbf162ibsv8bf_mask = 10673,
  CODE_FOR_avx10_2_cvtbf162iubsv8bf = 10674,
  CODE_FOR_avx10_2_cvtbf162iubsv8bf_mask = 10675,
  CODE_FOR_avx10_2_cvttbf162ibsv8bf = 10676,
  CODE_FOR_avx10_2_cvttbf162ibsv8bf_mask = 10677,
  CODE_FOR_avx10_2_cvttbf162iubsv8bf = 10678,
  CODE_FOR_avx10_2_cvttbf162iubsv8bf_mask = 10679,
  CODE_FOR_avx10_2_cvtph2ibsv32hf = 10680,
  CODE_FOR_avx10_2_cvtph2ibsv32hf_round = 10681,
  CODE_FOR_avx10_2_cvtph2ibsv32hf_mask = 10682,
  CODE_FOR_avx10_2_cvtph2ibsv32hf_mask_round = 10683,
  CODE_FOR_avx10_2_cvtph2iubsv32hf = 10684,
  CODE_FOR_avx10_2_cvtph2iubsv32hf_round = 10685,
  CODE_FOR_avx10_2_cvtph2iubsv32hf_mask = 10686,
  CODE_FOR_avx10_2_cvtph2iubsv32hf_mask_round = 10687,
  CODE_FOR_avx10_2_cvtph2ibsv16hf = 10688,
   CODE_FOR_avx10_2_cvtph2ibsv16hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtph2ibsv16hf_mask = 10689,
   CODE_FOR_avx10_2_cvtph2ibsv16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtph2iubsv16hf = 10690,
   CODE_FOR_avx10_2_cvtph2iubsv16hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtph2iubsv16hf_mask = 10691,
   CODE_FOR_avx10_2_cvtph2iubsv16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtph2ibsv8hf = 10692,
   CODE_FOR_avx10_2_cvtph2ibsv8hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtph2ibsv8hf_mask = 10693,
   CODE_FOR_avx10_2_cvtph2ibsv8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtph2iubsv8hf = 10694,
   CODE_FOR_avx10_2_cvtph2iubsv8hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtph2iubsv8hf_mask = 10695,
   CODE_FOR_avx10_2_cvtph2iubsv8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttph2ibsv32hf = 10696,
  CODE_FOR_avx10_2_cvttph2ibsv32hf_round = 10697,
  CODE_FOR_avx10_2_cvttph2ibsv32hf_mask = 10698,
  CODE_FOR_avx10_2_cvttph2ibsv32hf_mask_round = 10699,
  CODE_FOR_avx10_2_cvttph2iubsv32hf = 10700,
  CODE_FOR_avx10_2_cvttph2iubsv32hf_round = 10701,
  CODE_FOR_avx10_2_cvttph2iubsv32hf_mask = 10702,
  CODE_FOR_avx10_2_cvttph2iubsv32hf_mask_round = 10703,
  CODE_FOR_avx10_2_cvttph2ibsv16hf = 10704,
   CODE_FOR_avx10_2_cvttph2ibsv16hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttph2ibsv16hf_mask = 10705,
   CODE_FOR_avx10_2_cvttph2ibsv16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttph2iubsv16hf = 10706,
   CODE_FOR_avx10_2_cvttph2iubsv16hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttph2iubsv16hf_mask = 10707,
   CODE_FOR_avx10_2_cvttph2iubsv16hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttph2ibsv8hf = 10708,
   CODE_FOR_avx10_2_cvttph2ibsv8hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttph2ibsv8hf_mask = 10709,
   CODE_FOR_avx10_2_cvttph2ibsv8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttph2iubsv8hf = 10710,
   CODE_FOR_avx10_2_cvttph2iubsv8hf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttph2iubsv8hf_mask = 10711,
   CODE_FOR_avx10_2_cvttph2iubsv8hf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtps2ibsv16sf = 10712,
  CODE_FOR_avx10_2_cvtps2ibsv16sf_round = 10713,
  CODE_FOR_avx10_2_cvtps2ibsv16sf_mask = 10714,
  CODE_FOR_avx10_2_cvtps2ibsv16sf_mask_round = 10715,
  CODE_FOR_avx10_2_cvtps2iubsv16sf = 10716,
  CODE_FOR_avx10_2_cvtps2iubsv16sf_round = 10717,
  CODE_FOR_avx10_2_cvtps2iubsv16sf_mask = 10718,
  CODE_FOR_avx10_2_cvtps2iubsv16sf_mask_round = 10719,
  CODE_FOR_avx10_2_cvtps2ibsv8sf = 10720,
   CODE_FOR_avx10_2_cvtps2ibsv8sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtps2ibsv8sf_mask = 10721,
   CODE_FOR_avx10_2_cvtps2ibsv8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtps2iubsv8sf = 10722,
   CODE_FOR_avx10_2_cvtps2iubsv8sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtps2iubsv8sf_mask = 10723,
   CODE_FOR_avx10_2_cvtps2iubsv8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtps2ibsv4sf = 10724,
   CODE_FOR_avx10_2_cvtps2ibsv4sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtps2ibsv4sf_mask = 10725,
   CODE_FOR_avx10_2_cvtps2ibsv4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtps2iubsv4sf = 10726,
   CODE_FOR_avx10_2_cvtps2iubsv4sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvtps2iubsv4sf_mask = 10727,
   CODE_FOR_avx10_2_cvtps2iubsv4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttps2ibsv16sf = 10728,
  CODE_FOR_avx10_2_cvttps2ibsv16sf_round = 10729,
  CODE_FOR_avx10_2_cvttps2ibsv16sf_mask = 10730,
  CODE_FOR_avx10_2_cvttps2ibsv16sf_mask_round = 10731,
  CODE_FOR_avx10_2_cvttps2iubsv16sf = 10732,
  CODE_FOR_avx10_2_cvttps2iubsv16sf_round = 10733,
  CODE_FOR_avx10_2_cvttps2iubsv16sf_mask = 10734,
  CODE_FOR_avx10_2_cvttps2iubsv16sf_mask_round = 10735,
  CODE_FOR_avx10_2_cvttps2ibsv8sf = 10736,
   CODE_FOR_avx10_2_cvttps2ibsv8sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttps2ibsv8sf_mask = 10737,
   CODE_FOR_avx10_2_cvttps2ibsv8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttps2iubsv8sf = 10738,
   CODE_FOR_avx10_2_cvttps2iubsv8sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttps2iubsv8sf_mask = 10739,
   CODE_FOR_avx10_2_cvttps2iubsv8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttps2ibsv4sf = 10740,
   CODE_FOR_avx10_2_cvttps2ibsv4sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttps2ibsv4sf_mask = 10741,
   CODE_FOR_avx10_2_cvttps2ibsv4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttps2iubsv4sf = 10742,
   CODE_FOR_avx10_2_cvttps2iubsv4sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_cvttps2iubsv4sf_mask = 10743,
   CODE_FOR_avx10_2_cvttps2iubsv4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2dqsv16sf = 10744,
  CODE_FOR_avx10_2_vcvttps2dqsv16sf_round = 10745,
  CODE_FOR_avx10_2_vcvttps2dqsv16sf_mask = 10746,
  CODE_FOR_avx10_2_vcvttps2dqsv16sf_mask_round = 10747,
  CODE_FOR_avx10_2_vcvttps2udqsv16sf = 10748,
  CODE_FOR_avx10_2_vcvttps2udqsv16sf_round = 10749,
  CODE_FOR_avx10_2_vcvttps2udqsv16sf_mask = 10750,
  CODE_FOR_avx10_2_vcvttps2udqsv16sf_mask_round = 10751,
  CODE_FOR_avx10_2_vcvttps2dqsv8sf = 10752,
   CODE_FOR_avx10_2_vcvttps2dqsv8sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2dqsv8sf_mask = 10753,
   CODE_FOR_avx10_2_vcvttps2dqsv8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2udqsv8sf = 10754,
   CODE_FOR_avx10_2_vcvttps2udqsv8sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2udqsv8sf_mask = 10755,
   CODE_FOR_avx10_2_vcvttps2udqsv8sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2dqsv4sf = 10756,
   CODE_FOR_avx10_2_vcvttps2dqsv4sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2dqsv4sf_mask = 10757,
   CODE_FOR_avx10_2_vcvttps2dqsv4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2udqsv4sf = 10758,
   CODE_FOR_avx10_2_vcvttps2udqsv4sf_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2udqsv4sf_mask = 10759,
   CODE_FOR_avx10_2_vcvttps2udqsv4sf_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2dqsv8df = 10760,
  CODE_FOR_avx10_2_vcvttpd2dqsv8df_round = 10761,
  CODE_FOR_avx10_2_vcvttpd2dqsv8df_mask = 10762,
  CODE_FOR_avx10_2_vcvttpd2dqsv8df_mask_round = 10763,
  CODE_FOR_avx10_2_vcvttpd2udqsv8df = 10764,
  CODE_FOR_avx10_2_vcvttpd2udqsv8df_round = 10765,
  CODE_FOR_avx10_2_vcvttpd2udqsv8df_mask = 10766,
  CODE_FOR_avx10_2_vcvttpd2udqsv8df_mask_round = 10767,
  CODE_FOR_avx10_2_vcvttpd2dqsv4df = 10768,
   CODE_FOR_avx10_2_vcvttpd2dqsv4df_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2dqsv4df_mask = 10769,
   CODE_FOR_avx10_2_vcvttpd2dqsv4df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2udqsv4df = 10770,
   CODE_FOR_avx10_2_vcvttpd2udqsv4df_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2udqsv4df_mask = 10771,
   CODE_FOR_avx10_2_vcvttpd2udqsv4df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2dqsv2df = 10772,
   CODE_FOR_avx10_2_vcvttpd2dqsv2df_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2dqsv2df_mask = 10773,
   CODE_FOR_avx10_2_vcvttpd2dqsv2df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2udqsv2df = 10774,
   CODE_FOR_avx10_2_vcvttpd2udqsv2df_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2udqsv2df_mask = 10775,
   CODE_FOR_avx10_2_vcvttpd2udqsv2df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2qqsv8df = 10776,
  CODE_FOR_avx10_2_vcvttpd2qqsv8df_round = 10777,
  CODE_FOR_avx10_2_vcvttpd2qqsv8df_mask = 10778,
  CODE_FOR_avx10_2_vcvttpd2qqsv8df_mask_round = 10779,
  CODE_FOR_avx10_2_vcvttpd2uqqsv8df = 10780,
  CODE_FOR_avx10_2_vcvttpd2uqqsv8df_round = 10781,
  CODE_FOR_avx10_2_vcvttpd2uqqsv8df_mask = 10782,
  CODE_FOR_avx10_2_vcvttpd2uqqsv8df_mask_round = 10783,
  CODE_FOR_avx10_2_vcvttpd2qqsv4df = 10784,
   CODE_FOR_avx10_2_vcvttpd2qqsv4df_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2qqsv4df_mask = 10785,
   CODE_FOR_avx10_2_vcvttpd2qqsv4df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2uqqsv4df = 10786,
   CODE_FOR_avx10_2_vcvttpd2uqqsv4df_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2uqqsv4df_mask = 10787,
   CODE_FOR_avx10_2_vcvttpd2uqqsv4df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2qqsv2df = 10788,
   CODE_FOR_avx10_2_vcvttpd2qqsv2df_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2qqsv2df_mask = 10789,
   CODE_FOR_avx10_2_vcvttpd2qqsv2df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2uqqsv2df = 10790,
   CODE_FOR_avx10_2_vcvttpd2uqqsv2df_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttpd2uqqsv2df_mask = 10791,
   CODE_FOR_avx10_2_vcvttpd2uqqsv2df_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2qqsv8di = 10792,
  CODE_FOR_avx10_2_vcvttps2qqsv8di_round = 10793,
  CODE_FOR_avx10_2_vcvttps2qqsv8di_mask = 10794,
  CODE_FOR_avx10_2_vcvttps2qqsv8di_mask_round = 10795,
  CODE_FOR_avx10_2_vcvttps2uqqsv8di = 10796,
  CODE_FOR_avx10_2_vcvttps2uqqsv8di_round = 10797,
  CODE_FOR_avx10_2_vcvttps2uqqsv8di_mask = 10798,
  CODE_FOR_avx10_2_vcvttps2uqqsv8di_mask_round = 10799,
  CODE_FOR_avx10_2_vcvttps2qqsv4di = 10800,
   CODE_FOR_avx10_2_vcvttps2qqsv4di_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2qqsv4di_mask = 10801,
   CODE_FOR_avx10_2_vcvttps2qqsv4di_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2uqqsv4di = 10802,
   CODE_FOR_avx10_2_vcvttps2uqqsv4di_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2uqqsv4di_mask = 10803,
   CODE_FOR_avx10_2_vcvttps2uqqsv4di_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2qqsv2di = 10804,
   CODE_FOR_avx10_2_vcvttps2qqsv2di_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2qqsv2di_mask = 10805,
   CODE_FOR_avx10_2_vcvttps2qqsv2di_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2uqqsv2di = 10806,
   CODE_FOR_avx10_2_vcvttps2uqqsv2di_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttps2uqqsv2di_mask = 10807,
   CODE_FOR_avx10_2_vcvttps2uqqsv2di_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx10_2_vcvttsd2sissi = 10808,
  CODE_FOR_avx10_2_vcvttsd2sissi_round = 10809,
  CODE_FOR_avx10_2_vcvttsd2usissi = 10810,
  CODE_FOR_avx10_2_vcvttsd2usissi_round = 10811,
  CODE_FOR_avx10_2_vcvttsd2sisdi = 10812,
  CODE_FOR_avx10_2_vcvttsd2sisdi_round = 10813,
  CODE_FOR_avx10_2_vcvttsd2usisdi = 10814,
  CODE_FOR_avx10_2_vcvttsd2usisdi_round = 10815,
  CODE_FOR_avx10_2_vcvttss2sissi = 10816,
  CODE_FOR_avx10_2_vcvttss2sissi_round = 10817,
  CODE_FOR_avx10_2_vcvttss2usissi = 10818,
  CODE_FOR_avx10_2_vcvttss2usissi_round = 10819,
  CODE_FOR_avx10_2_vcvttss2sisdi = 10820,
  CODE_FOR_avx10_2_vcvttss2sisdi_round = 10821,
  CODE_FOR_avx10_2_vcvttss2usisdi = 10822,
  CODE_FOR_avx10_2_vcvttss2usisdi_round = 10823,
  CODE_FOR_avx10_2_minmaxbf16_v32bf = 10824,
  CODE_FOR_avx10_2_minmaxbf16_v32bf_mask = 10825,
  CODE_FOR_avx10_2_minmaxbf16_v16bf = 10826,
  CODE_FOR_avx10_2_minmaxbf16_v16bf_mask = 10827,
  CODE_FOR_avx10_2_minmaxbf16_v8bf = 10828,
  CODE_FOR_avx10_2_minmaxbf16_v8bf_mask = 10829,
  CODE_FOR_avx10_2_minmaxpv32hf = 10830,
  CODE_FOR_avx10_2_minmaxpv32hf_round = 10831,
  CODE_FOR_avx10_2_minmaxpv32hf_mask = 10832,
  CODE_FOR_avx10_2_minmaxpv32hf_mask_round = 10833,
  CODE_FOR_avx10_2_minmaxpv16hf = 10834,
  CODE_FOR_avx10_2_minmaxpv16hf_round = 10835,
  CODE_FOR_avx10_2_minmaxpv16hf_mask = 10836,
  CODE_FOR_avx10_2_minmaxpv16hf_mask_round = 10837,
  CODE_FOR_avx10_2_minmaxpv8hf = 10838,
  CODE_FOR_avx10_2_minmaxpv8hf_round = 10839,
  CODE_FOR_avx10_2_minmaxpv8hf_mask = 10840,
  CODE_FOR_avx10_2_minmaxpv8hf_mask_round = 10841,
  CODE_FOR_avx10_2_minmaxpv16sf = 10842,
  CODE_FOR_avx10_2_minmaxpv16sf_round = 10843,
  CODE_FOR_avx10_2_minmaxpv16sf_mask = 10844,
  CODE_FOR_avx10_2_minmaxpv16sf_mask_round = 10845,
  CODE_FOR_avx10_2_minmaxpv8sf = 10846,
  CODE_FOR_avx10_2_minmaxpv8sf_round = 10847,
  CODE_FOR_avx10_2_minmaxpv8sf_mask = 10848,
  CODE_FOR_avx10_2_minmaxpv8sf_mask_round = 10849,
  CODE_FOR_avx10_2_minmaxpv4sf = 10850,
  CODE_FOR_avx10_2_minmaxpv4sf_round = 10851,
  CODE_FOR_avx10_2_minmaxpv4sf_mask = 10852,
  CODE_FOR_avx10_2_minmaxpv4sf_mask_round = 10853,
  CODE_FOR_avx10_2_minmaxpv8df = 10854,
  CODE_FOR_avx10_2_minmaxpv8df_round = 10855,
  CODE_FOR_avx10_2_minmaxpv8df_mask = 10856,
  CODE_FOR_avx10_2_minmaxpv8df_mask_round = 10857,
  CODE_FOR_avx10_2_minmaxpv4df = 10858,
  CODE_FOR_avx10_2_minmaxpv4df_round = 10859,
  CODE_FOR_avx10_2_minmaxpv4df_mask = 10860,
  CODE_FOR_avx10_2_minmaxpv4df_mask_round = 10861,
  CODE_FOR_avx10_2_minmaxpv2df = 10862,
  CODE_FOR_avx10_2_minmaxpv2df_round = 10863,
  CODE_FOR_avx10_2_minmaxpv2df_mask = 10864,
  CODE_FOR_avx10_2_minmaxpv2df_mask_round = 10865,
  CODE_FOR_avx10_2_minmaxsv8hf = 10866,
  CODE_FOR_avx10_2_minmaxsv8hf_mask = 10867,
  CODE_FOR_avx10_2_minmaxsv8hf_round = 10868,
  CODE_FOR_avx10_2_minmaxsv8hf_mask_round = 10869,
  CODE_FOR_avx10_2_minmaxsv4sf = 10870,
  CODE_FOR_avx10_2_minmaxsv4sf_mask = 10871,
  CODE_FOR_avx10_2_minmaxsv4sf_round = 10872,
  CODE_FOR_avx10_2_minmaxsv4sf_mask_round = 10873,
  CODE_FOR_avx10_2_minmaxsv2df = 10874,
  CODE_FOR_avx10_2_minmaxsv2df_mask = 10875,
  CODE_FOR_avx10_2_minmaxsv2df_round = 10876,
  CODE_FOR_avx10_2_minmaxsv2df_mask_round = 10877,
  CODE_FOR_avx10_2_vmovrsbv64qi = 10878,
  CODE_FOR_avx10_2_vmovrsbv64qi_mask = 10879,
  CODE_FOR_avx10_2_vmovrsbv32qi = 10880,
  CODE_FOR_avx10_2_vmovrsbv32qi_mask = 10881,
  CODE_FOR_avx10_2_vmovrsbv16qi = 10882,
  CODE_FOR_avx10_2_vmovrsbv16qi_mask = 10883,
  CODE_FOR_avx10_2_vmovrswv32hi = 10884,
  CODE_FOR_avx10_2_vmovrswv32hi_mask = 10885,
  CODE_FOR_avx10_2_vmovrswv16hi = 10886,
  CODE_FOR_avx10_2_vmovrswv16hi_mask = 10887,
  CODE_FOR_avx10_2_vmovrswv8hi = 10888,
  CODE_FOR_avx10_2_vmovrswv8hi_mask = 10889,
  CODE_FOR_avx10_2_vmovrsdv16si = 10890,
  CODE_FOR_avx10_2_vmovrsdv16si_mask = 10891,
  CODE_FOR_avx10_2_vmovrsdv8si = 10892,
  CODE_FOR_avx10_2_vmovrsdv8si_mask = 10893,
  CODE_FOR_avx10_2_vmovrsdv4si = 10894,
  CODE_FOR_avx10_2_vmovrsdv4si_mask = 10895,
  CODE_FOR_avx10_2_vmovrsqv8di = 10896,
  CODE_FOR_avx10_2_vmovrsqv8di_mask = 10897,
  CODE_FOR_avx10_2_vmovrsqv4di = 10898,
  CODE_FOR_avx10_2_vmovrsqv4di_mask = 10899,
  CODE_FOR_avx10_2_vmovrsqv2di = 10900,
  CODE_FOR_avx10_2_vmovrsqv2di_mask = 10901,
  CODE_FOR_mfence_sse2 = 10904,
  CODE_FOR_mfence_nosse = 10905,
  CODE_FOR_atomic_loaddi_fpu = 10906,
  CODE_FOR_atomic_storeqi_1 = 10907,
  CODE_FOR_atomic_storehi_1 = 10908,
  CODE_FOR_atomic_storesi_1 = 10909,
  CODE_FOR_atomic_storedi_1 = 10910,
  CODE_FOR_atomic_storedi_fpu = 10911,
  CODE_FOR_loaddi_via_fpu = 10912,
  CODE_FOR_storedi_via_fpu = 10913,
  CODE_FOR_loaddi_via_sse = 10914,
  CODE_FOR_storedi_via_sse = 10915,
  CODE_FOR_atomic_compare_and_swapdi_doubleword = 10916,
  CODE_FOR_atomic_compare_and_swapti_doubleword = 10917,
  CODE_FOR_atomic_compare_and_swapqi_1 = 10918,
  CODE_FOR_atomic_compare_and_swaphi_1 = 10919,
  CODE_FOR_atomic_compare_and_swapsi_1 = 10920,
  CODE_FOR_atomic_compare_and_swapdi_1 = 10921,
  CODE_FOR_atomic_fetch_addqi = 10922,
  CODE_FOR_atomic_fetch_addhi = 10923,
  CODE_FOR_atomic_fetch_addsi = 10924,
  CODE_FOR_atomic_fetch_adddi = 10925,
  CODE_FOR_atomic_exchangeqi = 10930,
  CODE_FOR_atomic_exchangehi = 10931,
  CODE_FOR_atomic_exchangesi = 10932,
  CODE_FOR_atomic_exchangedi = 10933,
  CODE_FOR_rao_aandsi = 10934,
  CODE_FOR_rao_aorsi = 10935,
  CODE_FOR_rao_axorsi = 10936,
  CODE_FOR_rao_aaddsi = 10937,
  CODE_FOR_rao_aanddi = 10938,
  CODE_FOR_rao_aordi = 10939,
  CODE_FOR_rao_axordi = 10940,
  CODE_FOR_rao_aadddi = 10941,
  CODE_FOR_atomic_addqi = 10942,
  CODE_FOR_atomic_addhi = 10943,
  CODE_FOR_atomic_addsi = 10944,
  CODE_FOR_atomic_adddi = 10945,
  CODE_FOR_atomic_subqi = 10946,
  CODE_FOR_atomic_subhi = 10947,
  CODE_FOR_atomic_subsi = 10948,
  CODE_FOR_atomic_subdi = 10949,
  CODE_FOR_atomic_andqi = 10950,
  CODE_FOR_atomic_orqi = 10951,
  CODE_FOR_atomic_xorqi = 10952,
  CODE_FOR_atomic_andhi = 10953,
  CODE_FOR_atomic_orhi = 10954,
  CODE_FOR_atomic_xorhi = 10955,
  CODE_FOR_atomic_andsi = 10956,
  CODE_FOR_atomic_orsi = 10957,
  CODE_FOR_atomic_xorsi = 10958,
  CODE_FOR_atomic_anddi = 10959,
  CODE_FOR_atomic_ordi = 10960,
  CODE_FOR_atomic_xordi = 10961,
  CODE_FOR_atomic_bit_test_and_sethi_1 = 10962,
  CODE_FOR_atomic_bit_test_and_setsi_1 = 10963,
  CODE_FOR_atomic_bit_test_and_setdi_1 = 10964,
  CODE_FOR_atomic_bit_test_and_complementhi_1 = 10965,
  CODE_FOR_atomic_bit_test_and_complementsi_1 = 10966,
  CODE_FOR_atomic_bit_test_and_complementdi_1 = 10967,
  CODE_FOR_atomic_bit_test_and_resethi_1 = 10968,
  CODE_FOR_atomic_bit_test_and_resetsi_1 = 10969,
  CODE_FOR_atomic_bit_test_and_resetdi_1 = 10970,
  CODE_FOR_atomic_add_fetch_cmp_0qi_1 = 10971,
  CODE_FOR_atomic_add_fetch_cmp_0hi_1 = 10972,
  CODE_FOR_atomic_add_fetch_cmp_0si_1 = 10973,
  CODE_FOR_atomic_add_fetch_cmp_0di_1 = 10974,
  CODE_FOR_atomic_sub_fetch_cmp_0qi_1 = 10975,
  CODE_FOR_atomic_sub_fetch_cmp_0hi_1 = 10976,
  CODE_FOR_atomic_sub_fetch_cmp_0si_1 = 10977,
  CODE_FOR_atomic_sub_fetch_cmp_0di_1 = 10978,
  CODE_FOR_atomic_and_fetch_cmp_0qi_1 = 10979,
  CODE_FOR_atomic_or_fetch_cmp_0qi_1 = 10980,
  CODE_FOR_atomic_xor_fetch_cmp_0qi_1 = 10981,
  CODE_FOR_atomic_and_fetch_cmp_0hi_1 = 10982,
  CODE_FOR_atomic_or_fetch_cmp_0hi_1 = 10983,
  CODE_FOR_atomic_xor_fetch_cmp_0hi_1 = 10984,
  CODE_FOR_atomic_and_fetch_cmp_0si_1 = 10985,
  CODE_FOR_atomic_or_fetch_cmp_0si_1 = 10986,
  CODE_FOR_atomic_xor_fetch_cmp_0si_1 = 10987,
  CODE_FOR_atomic_and_fetch_cmp_0di_1 = 10988,
  CODE_FOR_atomic_or_fetch_cmp_0di_1 = 10989,
  CODE_FOR_atomic_xor_fetch_cmp_0di_1 = 10990,
  CODE_FOR_cmpccxadd_si = 10991,
  CODE_FOR_cmpccxadd_di = 10992,
  CODE_FOR_cbranchqi4 = 10993,
  CODE_FOR_cbranchhi4 = 10994,
  CODE_FOR_cbranchsi4 = 10995,
  CODE_FOR_cbranchdi4 = 10996,
  CODE_FOR_cbranchti4 = 10997,
  CODE_FOR_cbranchoi4 = 10998,
  CODE_FOR_cbranchxi4 = 10999,
  CODE_FOR_cstoreqi4 = 11000,
  CODE_FOR_cstorehi4 = 11001,
  CODE_FOR_cstoresi4 = 11002,
  CODE_FOR_cstoredi4 = 11003,
  CODE_FOR_cstoreti4 = 11004,
  CODE_FOR_cmpqi_1 = 11005,
  CODE_FOR_cmphi_1 = 11006,
  CODE_FOR_cmpsi_1 = 11007,
  CODE_FOR_cmpdi_1 = 11008,
  CODE_FOR_cmpqi_ext_3 = 11009,
  CODE_FOR_cbranchxf4 = 11010,
  CODE_FOR_cstorexf4 = 11011,
  CODE_FOR_cbranchhf4 = 11012,
  CODE_FOR_cbranchsf4 = 11013,
  CODE_FOR_cbranchdf4 = 11014,
  CODE_FOR_cbranchbf4 = 11015,
  CODE_FOR_cstorehf4 = 11016,
  CODE_FOR_cstorebf4 = 11017,
  CODE_FOR_cstoresf4 = 11018,
  CODE_FOR_cstoredf4 = 11019,
  CODE_FOR_cbranchcc4 = 11020,
  CODE_FOR_cstoreccgc4 = 11021,
  CODE_FOR_cstoreccgoc4 = 11022,
  CODE_FOR_cstoreccno4 = 11023,
  CODE_FOR_cstoreccgz4 = 11024,
  CODE_FOR_cstorecca4 = 11025,
  CODE_FOR_cstoreccc4 = 11026,
  CODE_FOR_cstorecco4 = 11027,
  CODE_FOR_cstoreccp4 = 11028,
  CODE_FOR_cstoreccs4 = 11029,
  CODE_FOR_cstoreccz4 = 11030,
  CODE_FOR_cstorecc4 = 11031,
  CODE_FOR_reload_noff_store = 11032,
  CODE_FOR_reload_noff_load = 11033,
  CODE_FOR_movxi = 11034,
  CODE_FOR_movoi = 11035,
  CODE_FOR_movti = 11036,
  CODE_FOR_movcdi = 11037,
  CODE_FOR_movqi = 11038,
  CODE_FOR_movhi = 11039,
  CODE_FOR_movsi = 11040,
  CODE_FOR_movdi = 11041,
  CODE_FOR_movstrictqi = 11042,
  CODE_FOR_movstricthi = 11043,
  CODE_FOR_extvhi = 11044,
  CODE_FOR_extvsi = 11045,
  CODE_FOR_extzvhi = 11046,
  CODE_FOR_extzvsi = 11047,
  CODE_FOR_extzvdi = 11048,
  CODE_FOR_insvhi = 11049,
  CODE_FOR_insvsi = 11050,
  CODE_FOR_insvdi = 11051,
  CODE_FOR_movtf = 11052,
  CODE_FOR_movhf = 11053,
  CODE_FOR_movsf = 11054,
  CODE_FOR_movdf = 11055,
  CODE_FOR_movxf = 11056,
  CODE_FOR_movbf = 11057,
  CODE_FOR_zero_extendsidi2 = 11058,
  CODE_FOR_zero_extendqisi2 = 11059,
  CODE_FOR_zero_extendhisi2 = 11060,
  CODE_FOR_zero_extendqihi2 = 11061,
  CODE_FOR_extendsidi2 = 11062,
  CODE_FOR_extendsfdf2 = 11063,
  CODE_FOR_extendhfsf2 = 11064,
  CODE_FOR_extendhfdf2 = 11065,
  CODE_FOR_extendbfsf2 = 11066,
  CODE_FOR_extendsfxf2 = 11067,
  CODE_FOR_extenddfxf2 = 11068,
  CODE_FOR_truncsfhf2 = 11069,
  CODE_FOR_truncdfhf2 = 11070,
  CODE_FOR_fix_truncxfdi2 = 11071,
  CODE_FOR_fix_truncsfdi2 = 11072,
  CODE_FOR_fix_truncdfdi2 = 11073,
  CODE_FOR_fix_truncxfsi2 = 11074,
  CODE_FOR_fix_truncsfsi2 = 11075,
  CODE_FOR_fix_truncdfsi2 = 11076,
  CODE_FOR_fix_truncsfhi2 = 11077,
  CODE_FOR_fix_truncdfhi2 = 11078,
  CODE_FOR_fix_truncxfhi2 = 11079,
  CODE_FOR_fixuns_truncsfsi2 = 11080,
  CODE_FOR_fixuns_truncdfsi2 = 11081,
  CODE_FOR_fixuns_trunchfhi2 = 11082,
  CODE_FOR_fixuns_truncsfhi2 = 11083,
  CODE_FOR_fixuns_truncdfhi2 = 11084,
  CODE_FOR_floatsisf2 = 11085,
  CODE_FOR_floatdisf2 = 11086,
  CODE_FOR_floatsidf2 = 11087,
  CODE_FOR_floatdidf2 = 11088,
  CODE_FOR_floatunsqisf2 = 11089,
  CODE_FOR_floatunshisf2 = 11090,
  CODE_FOR_floatunsqidf2 = 11091,
  CODE_FOR_floatunshidf2 = 11092,
  CODE_FOR_floatunssisf2 = 11093,
  CODE_FOR_floatunssidf2 = 11094,
  CODE_FOR_floatunssixf2 = 11095,
  CODE_FOR_floatunsdisf2 = 11096,
  CODE_FOR_floatunsdidf2 = 11097,
  CODE_FOR_addqi3 = 11098,
  CODE_FOR_addhi3 = 11099,
  CODE_FOR_addsi3 = 11100,
  CODE_FOR_adddi3 = 11101,
  CODE_FOR_addti3 = 11102,
  CODE_FOR_addqi_ext_1 = 11103,
  CODE_FOR_addvqi4 = 11104,
  CODE_FOR_addvhi4 = 11105,
  CODE_FOR_addvsi4 = 11106,
  CODE_FOR_addvdi4 = 11107,
  CODE_FOR_addvti4 = 11108,
  CODE_FOR_uaddvqi4 = 11109,
  CODE_FOR_uaddvhi4 = 11110,
  CODE_FOR_uaddvsi4 = 11111,
  CODE_FOR_uaddvdi4 = 11112,
  CODE_FOR_uaddvti4 = 11113,
  CODE_FOR_subqi3 = 11114,
  CODE_FOR_subhi3 = 11115,
  CODE_FOR_subsi3 = 11116,
  CODE_FOR_subdi3 = 11117,
  CODE_FOR_subti3 = 11118,
  CODE_FOR_subvqi4 = 11119,
  CODE_FOR_subvhi4 = 11120,
  CODE_FOR_subvsi4 = 11121,
  CODE_FOR_subvdi4 = 11122,
  CODE_FOR_subvti4 = 11123,
  CODE_FOR_usubvqi4 = 11124,
  CODE_FOR_usubvhi4 = 11125,
  CODE_FOR_usubvsi4 = 11126,
  CODE_FOR_usubvdi4 = 11127,
  CODE_FOR_subqi_3 = 11128,
  CODE_FOR_subhi_3 = 11129,
  CODE_FOR_subsi_3 = 11130,
  CODE_FOR_subdi_3 = 11131,
  CODE_FOR_addcarrysi_0 = 11132,
  CODE_FOR_addcarrydi_0 = 11133,
  CODE_FOR_subborrowsi_0 = 11134,
  CODE_FOR_subborrowdi_0 = 11135,
  CODE_FOR_uaddcsi5 = 11136,
  CODE_FOR_uaddcdi5 = 11137,
  CODE_FOR_usubcsi5 = 11138,
  CODE_FOR_usubcdi5 = 11139,
  CODE_FOR_addqi3_cconly_overflow = 11140,
  CODE_FOR_usaddqi3 = 11141,
  CODE_FOR_usaddhi3 = 11142,
  CODE_FOR_usaddsi3 = 11143,
  CODE_FOR_usadddi3 = 11144,
  CODE_FOR_ussubqi3 = 11145,
  CODE_FOR_ussubhi3 = 11146,
  CODE_FOR_ussubsi3 = 11147,
  CODE_FOR_ussubdi3 = 11148,
  CODE_FOR_ustruncdiqi2 = 11149,
  CODE_FOR_ustruncdihi2 = 11150,
  CODE_FOR_ustruncdisi2 = 11151,
  CODE_FOR_ustruncsiqi2 = 11152,
  CODE_FOR_ustruncsihi2 = 11153,
  CODE_FOR_ustrunchiqi2 = 11154,
  CODE_FOR_addxf3 = 11155,
  CODE_FOR_subxf3 = 11156,
  CODE_FOR_addhf3 = 11157,
  CODE_FOR_subhf3 = 11158,
  CODE_FOR_addsf3 = 11159,
  CODE_FOR_subsf3 = 11160,
  CODE_FOR_adddf3 = 11161,
  CODE_FOR_subdf3 = 11162,
  CODE_FOR_mulhi3 = 11163,
  CODE_FOR_mulsi3 = 11164,
  CODE_FOR_muldi3 = 11165,
  CODE_FOR_mulqi3 = 11166,
  CODE_FOR_mulvhi4 = 11167,
  CODE_FOR_mulvsi4 = 11168,
  CODE_FOR_mulvdi4 = 11169,
  CODE_FOR_umulvhi4 = 11170,
  CODE_FOR_umulvsi4 = 11171,
  CODE_FOR_umulvdi4 = 11172,
  CODE_FOR_mulvqi4 = 11173,
  CODE_FOR_umulvqi4 = 11174,
  CODE_FOR_mulsidi3 = 11175,
  CODE_FOR_umulsidi3 = 11176,
  CODE_FOR_mulditi3 = 11177,
  CODE_FOR_umulditi3 = 11178,
  CODE_FOR_mulqihi3 = 11179,
  CODE_FOR_umulqihi3 = 11180,
  CODE_FOR_mulxf3 = 11181,
  CODE_FOR_mulhf3 = 11182,
  CODE_FOR_mulsf3 = 11183,
  CODE_FOR_muldf3 = 11184,
  CODE_FOR_divxf3 = 11185,
  CODE_FOR_divhf3 = 11186,
  CODE_FOR_divsf3 = 11187,
  CODE_FOR_divdf3 = 11188,
  CODE_FOR_divmodhi4 = 11189,
  CODE_FOR_udivmodhi4 = 11190,
  CODE_FOR_divmodsi4 = 11191,
  CODE_FOR_udivmodsi4 = 11192,
  CODE_FOR_divmoddi4 = 11193,
  CODE_FOR_udivmoddi4 = 11194,
  CODE_FOR_divmodqi4 = 11195,
  CODE_FOR_udivmodqi4 = 11196,
  CODE_FOR_testsi_ccno_1 = 11197,
  CODE_FOR_testdi_ccno_1 = 11198,
  CODE_FOR_testqi_ccz_1 = 11199,
  CODE_FOR_testqi_ext_1_ccno = 11200,
  CODE_FOR_andqi3 = 11201,
  CODE_FOR_andhi3 = 11202,
  CODE_FOR_andsi3 = 11203,
  CODE_FOR_anddi3 = 11204,
  CODE_FOR_andti3 = 11205,
  CODE_FOR_andqi_ext_1 = 11206,
  CODE_FOR_iorqi3 = 11207,
  CODE_FOR_xorqi3 = 11208,
  CODE_FOR_iorhi3 = 11209,
  CODE_FOR_xorhi3 = 11210,
  CODE_FOR_iorsi3 = 11211,
  CODE_FOR_xorsi3 = 11212,
  CODE_FOR_iordi3 = 11213,
  CODE_FOR_xordi3 = 11214,
  CODE_FOR_iorti3 = 11215,
  CODE_FOR_xorti3 = 11216,
  CODE_FOR_xorqi_ext_1_cc = 11217,
  CODE_FOR_negqi2 = 11218,
  CODE_FOR_neghi2 = 11219,
  CODE_FOR_negsi2 = 11220,
  CODE_FOR_negdi2 = 11221,
  CODE_FOR_negti2 = 11222,
  CODE_FOR_x86_negsi_ccc = 11223,
  CODE_FOR_x86_negdi_ccc = 11224,
  CODE_FOR_negvqi3 = 11225,
  CODE_FOR_negvhi3 = 11226,
  CODE_FOR_negvsi3 = 11227,
  CODE_FOR_negvdi3 = 11228,
  CODE_FOR_absqi2 = 11229,
  CODE_FOR_abshi2 = 11230,
  CODE_FOR_abssi2 = 11231,
  CODE_FOR_absdi2 = 11232,
  CODE_FOR_absti2 = 11233,
  CODE_FOR_abstf2 = 11234,
  CODE_FOR_negtf2 = 11235,
  CODE_FOR_abshf2 = 11236,
  CODE_FOR_neghf2 = 11237,
  CODE_FOR_abssf2 = 11238,
  CODE_FOR_negsf2 = 11239,
  CODE_FOR_absdf2 = 11240,
  CODE_FOR_negdf2 = 11241,
  CODE_FOR_absxf2 = 11242,
  CODE_FOR_negxf2 = 11243,
  CODE_FOR_copysignhf3 = 11244,
  CODE_FOR_copysignsf3 = 11245,
  CODE_FOR_copysigndf3 = 11246,
  CODE_FOR_copysigntf3 = 11247,
  CODE_FOR_xorsignhf3 = 11248,
  CODE_FOR_xorsignsf3 = 11249,
  CODE_FOR_xorsigndf3 = 11250,
  CODE_FOR_one_cmplqi2 = 11251,
  CODE_FOR_one_cmplhi2 = 11252,
  CODE_FOR_one_cmplsi2 = 11253,
  CODE_FOR_one_cmpldi2 = 11254,
  CODE_FOR_one_cmplti2 = 11255,
  CODE_FOR_ashlqi3 = 11256,
  CODE_FOR_ashlhi3 = 11257,
  CODE_FOR_ashlsi3 = 11258,
  CODE_FOR_ashldi3 = 11259,
  CODE_FOR_ashlti3 = 11260,
  CODE_FOR_x86_shiftsi_adj_1 = 11261,
  CODE_FOR_x86_shiftdi_adj_1 = 11262,
  CODE_FOR_x86_shiftsi_adj_2 = 11263,
  CODE_FOR_x86_shiftdi_adj_2 = 11264,
  CODE_FOR_lshrqi3 = 11265,
  CODE_FOR_ashrqi3 = 11266,
  CODE_FOR_lshrhi3 = 11267,
  CODE_FOR_ashrhi3 = 11268,
  CODE_FOR_lshrsi3 = 11269,
  CODE_FOR_ashrsi3 = 11270,
  CODE_FOR_lshrdi3 = 11271,
  CODE_FOR_ashrdi3 = 11272,
  CODE_FOR_lshrti3 = 11273,
  CODE_FOR_ashrti3 = 11274,
  CODE_FOR_x86_shiftsi_adj_3 = 11275,
  CODE_FOR_x86_shiftdi_adj_3 = 11276,
  CODE_FOR_rotlti3 = 11277,
  CODE_FOR_rotrti3 = 11278,
  CODE_FOR_rotldi3 = 11279,
  CODE_FOR_rotrdi3 = 11280,
  CODE_FOR_rotlqi3 = 11281,
  CODE_FOR_rotrqi3 = 11282,
  CODE_FOR_rotlhi3 = 11283,
  CODE_FOR_rotrhi3 = 11284,
  CODE_FOR_rotlsi3 = 11285,
  CODE_FOR_rotrsi3 = 11286,
  CODE_FOR_setcc_si_slp = 11287,
  CODE_FOR_indirect_jump = 11288,
  CODE_FOR_tablejump = 11289,
  CODE_FOR_call = 11290,
  CODE_FOR_sibcall = 11291,
  CODE_FOR_call_pop = 11292,
  CODE_FOR_call_value = 11293,
  CODE_FOR_sibcall_value = 11294,
  CODE_FOR_call_value_pop = 11295,
  CODE_FOR_untyped_call = 11296,
  CODE_FOR_memory_blockage = 11297,
  CODE_FOR_return = 11298,
  CODE_FOR_simple_return = 11299,
  CODE_FOR_simple_return_indirect_internal = 11300,
  CODE_FOR_prologue = 11301,
  CODE_FOR_set_got = 11302,
  CODE_FOR_set_got_labelled = 11303,
  CODE_FOR_epilogue = 11304,
  CODE_FOR_sibcall_epilogue = 11305,
  CODE_FOR_eh_return = 11306,
  CODE_FOR_leave_si = 11307,
  CODE_FOR_leave_di = 11308,
  CODE_FOR_split_stack_prologue = 11309,
  CODE_FOR_split_stack_space_check = 11310,
  CODE_FOR_ffssi2 = 11311,
  CODE_FOR_ffsdi2 = 11312,
  CODE_FOR_clzsi2 = 11313,
  CODE_FOR_clzdi2 = 11314,
  CODE_FOR_bmi2_bzhi_si3 = 11315,
  CODE_FOR_bmi2_bzhi_di3 = 11316,
  CODE_FOR_bswapdi2 = 11317,
  CODE_FOR_bswapsi2 = 11318,
  CODE_FOR_bswaphi2 = 11319,
  CODE_FOR_paritydi2 = 11320,
  CODE_FOR_paritysi2 = 11321,
  CODE_FOR_parityhi2 = 11322,
  CODE_FOR_parityqi2 = 11323,
  CODE_FOR_tls_global_dynamic_32 = 11324,
  CODE_FOR_tls_global_dynamic_64_si = 11325,
  CODE_FOR_tls_global_dynamic_64_di = 11326,
  CODE_FOR_tls_local_dynamic_base_32 = 11327,
  CODE_FOR_tls_local_dynamic_base_64_si = 11328,
  CODE_FOR_tls_local_dynamic_base_64_di = 11329,
  CODE_FOR_get_thread_pointersi = 11330,
  CODE_FOR_get_thread_pointerdi = 11331,
  CODE_FOR_tls_dynamic_gnu2_32 = 11332,
  CODE_FOR_tls_dynamic_gnu2_64_si = 11333,
  CODE_FOR_tls_dynamic_gnu2_64_di = 11334,
  CODE_FOR_rsqrtsf2 = 11335,
  CODE_FOR_sqrtsf2 = 11336,
  CODE_FOR_sqrtdf2 = 11337,
  CODE_FOR_hypotsf3 = 11338,
  CODE_FOR_hypotdf3 = 11339,
  CODE_FOR_fmodxf3 = 11340,
  CODE_FOR_fmodsf3 = 11341,
  CODE_FOR_fmoddf3 = 11342,
  CODE_FOR_remainderxf3 = 11343,
  CODE_FOR_remaindersf3 = 11344,
  CODE_FOR_remainderdf3 = 11345,
  CODE_FOR_sinsf2 = 11346,
  CODE_FOR_cossf2 = 11347,
  CODE_FOR_sindf2 = 11348,
  CODE_FOR_cosdf2 = 11349,
  CODE_FOR_sincossf3 = 11350,
  CODE_FOR_sincosdf3 = 11351,
  CODE_FOR_tanxf2 = 11352,
  CODE_FOR_tansf2 = 11353,
  CODE_FOR_tandf2 = 11354,
  CODE_FOR_atan2sf3 = 11355,
  CODE_FOR_atan2df3 = 11356,
  CODE_FOR_atanxf2 = 11357,
  CODE_FOR_atansf2 = 11358,
  CODE_FOR_atandf2 = 11359,
  CODE_FOR_asinxf2 = 11360,
  CODE_FOR_asinsf2 = 11361,
  CODE_FOR_asindf2 = 11362,
  CODE_FOR_acosxf2 = 11363,
  CODE_FOR_acossf2 = 11364,
  CODE_FOR_acosdf2 = 11365,
  CODE_FOR_sinhxf2 = 11366,
  CODE_FOR_sinhsf2 = 11367,
  CODE_FOR_sinhdf2 = 11368,
  CODE_FOR_coshxf2 = 11369,
  CODE_FOR_coshsf2 = 11370,
  CODE_FOR_coshdf2 = 11371,
  CODE_FOR_tanhxf2 = 11372,
  CODE_FOR_tanhsf2 = 11373,
  CODE_FOR_tanhdf2 = 11374,
  CODE_FOR_asinhxf2 = 11375,
  CODE_FOR_asinhsf2 = 11376,
  CODE_FOR_asinhdf2 = 11377,
  CODE_FOR_acoshxf2 = 11378,
  CODE_FOR_acoshsf2 = 11379,
  CODE_FOR_acoshdf2 = 11380,
  CODE_FOR_atanhxf2 = 11381,
  CODE_FOR_atanhsf2 = 11382,
  CODE_FOR_atanhdf2 = 11383,
  CODE_FOR_logxf2 = 11384,
  CODE_FOR_logsf2 = 11385,
  CODE_FOR_logdf2 = 11386,
  CODE_FOR_log10xf2 = 11387,
  CODE_FOR_log10sf2 = 11388,
  CODE_FOR_log10df2 = 11389,
  CODE_FOR_log2xf2 = 11390,
  CODE_FOR_log2sf2 = 11391,
  CODE_FOR_log2df2 = 11392,
  CODE_FOR_log1pxf2 = 11393,
  CODE_FOR_log1psf2 = 11394,
  CODE_FOR_log1pdf2 = 11395,
  CODE_FOR_logbxf2 = 11396,
  CODE_FOR_logbsf2 = 11397,
  CODE_FOR_logbdf2 = 11398,
  CODE_FOR_ilogbxf2 = 11399,
  CODE_FOR_ilogbsf2 = 11400,
  CODE_FOR_ilogbdf2 = 11401,
  CODE_FOR_expNcorexf3 = 11402,
  CODE_FOR_expxf2 = 11403,
  CODE_FOR_expsf2 = 11404,
  CODE_FOR_expdf2 = 11405,
  CODE_FOR_exp10xf2 = 11406,
  CODE_FOR_exp10sf2 = 11407,
  CODE_FOR_exp10df2 = 11408,
  CODE_FOR_exp2xf2 = 11409,
  CODE_FOR_exp2sf2 = 11410,
  CODE_FOR_exp2df2 = 11411,
  CODE_FOR_expm1xf2 = 11412,
  CODE_FOR_expm1sf2 = 11413,
  CODE_FOR_expm1df2 = 11414,
  CODE_FOR_ldexpxf3 = 11415,
  CODE_FOR_ldexpsf3 = 11416,
  CODE_FOR_ldexpdf3 = 11417,
  CODE_FOR_scalbxf3 = 11418,
  CODE_FOR_scalbsf3 = 11419,
  CODE_FOR_scalbdf3 = 11420,
  CODE_FOR_significandxf2 = 11421,
  CODE_FOR_significandsf2 = 11422,
  CODE_FOR_significanddf2 = 11423,
  CODE_FOR_rinthf2 = 11424,
  CODE_FOR_rintsf2 = 11425,
  CODE_FOR_rintdf2 = 11426,
  CODE_FOR_nearbyintxf2 = 11427,
  CODE_FOR_nearbyinthf2 = 11428,
  CODE_FOR_nearbyintsf2 = 11429,
  CODE_FOR_nearbyintdf2 = 11430,
  CODE_FOR_roundhf2 = 11431,
  CODE_FOR_roundsf2 = 11432,
  CODE_FOR_rounddf2 = 11433,
  CODE_FOR_roundxf2 = 11434,
  CODE_FOR_lroundhfhi2 = 11435,
  CODE_FOR_lroundhfsi2 = 11436,
  CODE_FOR_lroundhfdi2 = 11437,
  CODE_FOR_lrinthfsi2 = 11438,
  CODE_FOR_lrinthfdi2 = 11439,
  CODE_FOR_lrintsfsi2 = 11440,
  CODE_FOR_lrintsfdi2 = 11441,
  CODE_FOR_lrintdfsi2 = 11442,
  CODE_FOR_lrintdfdi2 = 11443,
  CODE_FOR_lroundsfhi2 = 11444,
  CODE_FOR_lrounddfhi2 = 11445,
  CODE_FOR_lroundxfhi2 = 11446,
  CODE_FOR_lroundsfsi2 = 11447,
  CODE_FOR_lrounddfsi2 = 11448,
  CODE_FOR_lroundxfsi2 = 11449,
  CODE_FOR_lroundsfdi2 = 11450,
  CODE_FOR_lrounddfdi2 = 11451,
  CODE_FOR_lroundxfdi2 = 11452,
  CODE_FOR_roundevenxf2 = 11453,
  CODE_FOR_floorxf2 = 11454,
  CODE_FOR_ceilxf2 = 11455,
  CODE_FOR_btruncxf2 = 11456,
  CODE_FOR_roundevenhf2 = 11457,
  CODE_FOR_floorhf2 = 11458,
  CODE_FOR_ceilhf2 = 11459,
  CODE_FOR_btrunchf2 = 11460,
  CODE_FOR_roundevensf2 = 11461,
  CODE_FOR_floorsf2 = 11462,
  CODE_FOR_ceilsf2 = 11463,
  CODE_FOR_btruncsf2 = 11464,
  CODE_FOR_roundevendf2 = 11465,
  CODE_FOR_floordf2 = 11466,
  CODE_FOR_ceildf2 = 11467,
  CODE_FOR_btruncdf2 = 11468,
  CODE_FOR_lfloorxfhi2 = 11469,
  CODE_FOR_lceilxfhi2 = 11470,
  CODE_FOR_lfloorxfsi2 = 11471,
  CODE_FOR_lceilxfsi2 = 11472,
  CODE_FOR_lfloorxfdi2 = 11473,
  CODE_FOR_lceilxfdi2 = 11474,
  CODE_FOR_lfloorhfsi2 = 11475,
  CODE_FOR_lceilhfsi2 = 11476,
  CODE_FOR_lfloorhfdi2 = 11477,
  CODE_FOR_lceilhfdi2 = 11478,
  CODE_FOR_lfloorsfsi2 = 11479,
  CODE_FOR_lceilsfsi2 = 11480,
  CODE_FOR_lfloorsfdi2 = 11481,
  CODE_FOR_lceilsfdi2 = 11482,
  CODE_FOR_lfloordfsi2 = 11483,
  CODE_FOR_lceildfsi2 = 11484,
  CODE_FOR_lfloordfdi2 = 11485,
  CODE_FOR_lceildfdi2 = 11486,
  CODE_FOR_signbittf2 = 11487,
  CODE_FOR_signbitxf2 = 11488,
  CODE_FOR_signbitdf2 = 11489,
  CODE_FOR_signbitsf2 = 11490,
  CODE_FOR_cpymemsi = 11491,
  CODE_FOR_cpymemdi = 11492,
  CODE_FOR_strmov = 11493,
  CODE_FOR_strmov_singleop = 11494,
  CODE_FOR_rep_mov = 11495,
  CODE_FOR_setmemsi = 11496,
  CODE_FOR_setmemdi = 11497,
  CODE_FOR_strset = 11498,
  CODE_FOR_strset_singleop = 11499,
  CODE_FOR_rep_stos = 11500,
  CODE_FOR_cmpmemsi = 11501,
  CODE_FOR_cmpstrnsi = 11502,
  CODE_FOR_cmpintqi = 11503,
  CODE_FOR_cmpstrnqi_nz_1 = 11504,
  CODE_FOR_cmpstrnqi_1 = 11505,
  CODE_FOR_strlensi = 11506,
  CODE_FOR_strlendi = 11507,
  CODE_FOR_strlenqi_1 = 11508,
  CODE_FOR_movqicc = 11509,
  CODE_FOR_movhicc = 11510,
  CODE_FOR_movsicc = 11511,
  CODE_FOR_movdicc = 11512,
  CODE_FOR_x86_movsicc_0_m1 = 11513,
  CODE_FOR_x86_movdicc_0_m1 = 11514,
  CODE_FOR_x86_movqicc_0_m1_neg = 11515,
  CODE_FOR_x86_movhicc_0_m1_neg = 11516,
  CODE_FOR_x86_movsicc_0_m1_neg = 11517,
  CODE_FOR_x86_movdicc_0_m1_neg = 11518,
  CODE_FOR_movhfcc = 11519,
  CODE_FOR_movsfcc = 11520,
  CODE_FOR_movdfcc = 11521,
  CODE_FOR_movxfcc = 11522,
  CODE_FOR_addqicc = 11523,
  CODE_FOR_addhicc = 11524,
  CODE_FOR_addsicc = 11525,
  CODE_FOR_adddicc = 11526,
  CODE_FOR_smaxqi3 = 11527,
  CODE_FOR_sminqi3 = 11528,
  CODE_FOR_umaxqi3 = 11529,
  CODE_FOR_uminqi3 = 11530,
  CODE_FOR_smaxhi3 = 11531,
  CODE_FOR_sminhi3 = 11532,
  CODE_FOR_umaxhi3 = 11533,
  CODE_FOR_uminhi3 = 11534,
  CODE_FOR_smaxsi3 = 11535,
  CODE_FOR_sminsi3 = 11536,
  CODE_FOR_umaxsi3 = 11537,
  CODE_FOR_uminsi3 = 11538,
  CODE_FOR_smaxdi3 = 11539,
  CODE_FOR_smindi3 = 11540,
  CODE_FOR_umaxdi3 = 11541,
  CODE_FOR_umindi3 = 11542,
  CODE_FOR_smaxti3 = 11543,
  CODE_FOR_sminti3 = 11544,
  CODE_FOR_umaxti3 = 11545,
  CODE_FOR_uminti3 = 11546,
  CODE_FOR_allocate_stack = 11547,
  CODE_FOR_probe_stack = 11548,
  CODE_FOR_builtin_setjmp_receiver = 11549,
  CODE_FOR_save_stack_nonlocal = 11550,
  CODE_FOR_restore_stack_nonlocal = 11551,
  CODE_FOR_stack_protect_set = 11552,
  CODE_FOR_stack_protect_test = 11553,
  CODE_FOR_prefetch = 11554,
  CODE_FOR_pause = 11555,
  CODE_FOR_xbegin = 11556,
  CODE_FOR_xtest = 11557,
  CODE_FOR_rdpkru = 11558,
  CODE_FOR_wrpkru = 11559,
  CODE_FOR_spaceshipsf4 = 11560,
  CODE_FOR_spaceshipdf4 = 11561,
  CODE_FOR_spaceshipxf4 = 11562,
  CODE_FOR_spaceshipqi4 = 11563,
  CODE_FOR_spaceshiphi4 = 11564,
  CODE_FOR_spaceshipsi4 = 11565,
  CODE_FOR_spaceshipdi4 = 11566,
  CODE_FOR_issignalingxf2 = 11567,
  CODE_FOR_movv8qi = 11568,
  CODE_FOR_movv4hi = 11569,
  CODE_FOR_movv2si = 11570,
  CODE_FOR_movv1di = 11571,
  CODE_FOR_movv2sf = 11572,
  CODE_FOR_movv4hf = 11573,
  CODE_FOR_movv4bf = 11574,
  CODE_FOR_movmisalignv8qi = 11575,
  CODE_FOR_movmisalignv4hi = 11576,
  CODE_FOR_movmisalignv2si = 11577,
  CODE_FOR_movmisalignv1di = 11578,
  CODE_FOR_movmisalignv2sf = 11579,
  CODE_FOR_movmisalignv4hf = 11580,
  CODE_FOR_movmisalignv4bf = 11581,
  CODE_FOR_movv4qi = 11582,
  CODE_FOR_movv2hi = 11583,
  CODE_FOR_movv1si = 11584,
  CODE_FOR_movv2hf = 11585,
  CODE_FOR_movv2bf = 11586,
  CODE_FOR_movmisalignv4qi = 11587,
  CODE_FOR_movmisalignv2hi = 11588,
  CODE_FOR_movmisalignv1si = 11589,
  CODE_FOR_movmisalignv2hf = 11590,
  CODE_FOR_movmisalignv2bf = 11591,
  CODE_FOR_movv2qi = 11592,
  CODE_FOR_movmisalignv2qi = 11593,
  CODE_FOR_movq_v2sf_to_sse = 11594,
  CODE_FOR_movq_v2si_to_sse = 11595,
  CODE_FOR_movq_v4hf_to_sse = 11596,
  CODE_FOR_movq_v4hi_to_sse = 11597,
  CODE_FOR_absv2sf2 = 11598,
  CODE_FOR_negv2sf2 = 11599,
  CODE_FOR_addv2sf3 = 11600,
  CODE_FOR_subv2sf3 = 11601,
  CODE_FOR_mulv2sf3 = 11602,
  CODE_FOR_mmx_addv2sf3 = 11603,
  CODE_FOR_mmx_subv2sf3 = 11604,
  CODE_FOR_mmx_subrv2sf3 = 11605,
  CODE_FOR_mmx_mulv2sf3 = 11606,
  CODE_FOR_divv2sf3 = 11607,
  CODE_FOR_smaxv2sf3 = 11608,
  CODE_FOR_sminv2sf3 = 11609,
  CODE_FOR_mmx_smaxv2sf3 = 11610,
  CODE_FOR_mmx_sminv2sf3 = 11611,
  CODE_FOR_sqrtv2sf2 = 11612,
  CODE_FOR_mmx_haddv2sf3 = 11613,
  CODE_FOR_mmx_haddsubv2sf3 = 11614,
  CODE_FOR_vec_addsubv2sf3 = 11615,
  CODE_FOR_vec_fmaddsubv2sf4 = 11616,
  CODE_FOR_vec_fmsubaddv2sf4 = 11617,
  CODE_FOR_mmx_eqv2sf3 = 11618,
  CODE_FOR_vec_cmpv2sfv2si = 11619,
  CODE_FOR_copysignv2sf3 = 11620,
  CODE_FOR_xorsignv2sf3 = 11621,
  CODE_FOR_signbitv2sf2 = 11622,
  CODE_FOR_fmav2sf4 = 11623,
  CODE_FOR_fmsv2sf4 = 11624,
  CODE_FOR_fnmav2sf4 = 11625,
  CODE_FOR_fnmsv2sf4 = 11626,
  CODE_FOR_fix_truncv2sfv2si2 = 11627,
  CODE_FOR_fixuns_truncv2sfv2si2 = 11628,
  CODE_FOR_floatv2siv2sf2 = 11629,
  CODE_FOR_floatunsv2siv2sf2 = 11630,
  CODE_FOR_vec_setv2sf = 11631,
  CODE_FOR_vec_extractv2sfsf = 11632,
  CODE_FOR_vec_initv2sfsf = 11633,
  CODE_FOR_nearbyintv2sf2 = 11634,
  CODE_FOR_rintv2sf2 = 11635,
  CODE_FOR_lrintv2sfv2si2 = 11636,
  CODE_FOR_ceilv2sf2 = 11637,
  CODE_FOR_lceilv2sfv2si2 = 11638,
  CODE_FOR_floorv2sf2 = 11639,
  CODE_FOR_lfloorv2sfv2si2 = 11640,
  CODE_FOR_btruncv2sf2 = 11641,
  CODE_FOR_roundv2sf2 = 11642,
  CODE_FOR_lroundv2sfv2si2 = 11643,
  CODE_FOR_divv4hf3 = 11644,
  CODE_FOR_movd_v2hf_to_sse = 11645,
  CODE_FOR_movd_v2bf_to_sse = 11646,
  CODE_FOR_movd_v2hi_to_sse = 11647,
  CODE_FOR_movd_v2hf_to_sse_reg = 11648,
  CODE_FOR_movd_v2bf_to_sse_reg = 11649,
  CODE_FOR_movd_v2hi_to_sse_reg = 11650,
  CODE_FOR_addv2hf3 = 11651,
  CODE_FOR_subv2hf3 = 11652,
  CODE_FOR_mulv2hf3 = 11653,
  CODE_FOR_addv4hf3 = 11654,
  CODE_FOR_subv4hf3 = 11655,
  CODE_FOR_mulv4hf3 = 11656,
  CODE_FOR_addv2bf3 = 11657,
  CODE_FOR_subv2bf3 = 11658,
  CODE_FOR_mulv2bf3 = 11659,
  CODE_FOR_divv2bf3 = 11660,
  CODE_FOR_addv4bf3 = 11661,
  CODE_FOR_subv4bf3 = 11662,
  CODE_FOR_mulv4bf3 = 11663,
  CODE_FOR_divv4bf3 = 11664,
  CODE_FOR_divv2hf3 = 11665,
  CODE_FOR_smaxv2hf3 = 11666,
  CODE_FOR_sminv2hf3 = 11667,
  CODE_FOR_smaxv4hf3 = 11668,
  CODE_FOR_sminv4hf3 = 11669,
  CODE_FOR_smaxv2bf3 = 11670,
  CODE_FOR_sminv2bf3 = 11671,
  CODE_FOR_smaxv4bf3 = 11672,
  CODE_FOR_sminv4bf3 = 11673,
  CODE_FOR_sqrtv2hf2 = 11674,
  CODE_FOR_sqrtv4hf2 = 11675,
  CODE_FOR_sqrtv2bf2 = 11676,
  CODE_FOR_sqrtv4bf2 = 11677,
  CODE_FOR_absv2bf2 = 11678,
  CODE_FOR_negv2bf2 = 11679,
  CODE_FOR_absv4bf2 = 11680,
  CODE_FOR_negv4bf2 = 11681,
  CODE_FOR_absv2hf2 = 11682,
  CODE_FOR_negv2hf2 = 11683,
  CODE_FOR_absv4hf2 = 11684,
  CODE_FOR_negv4hf2 = 11685,
  CODE_FOR_vec_cmpv4hfqi = 11686,
  CODE_FOR_vcond_mask_v4hfv4hi = 11687,
  CODE_FOR_vcond_mask_v4bfv4hi = 11688,
  CODE_FOR_vcond_mask_v4hfqi = 11689,
  CODE_FOR_vcond_mask_v4bfqi = 11690,
  CODE_FOR_vcond_mask_v4hiqi = 11691,
  CODE_FOR_vec_cmpv2hfqi = 11692,
  CODE_FOR_vcond_mask_v2hfv2hi = 11693,
  CODE_FOR_vcond_mask_v2bfv2hi = 11694,
  CODE_FOR_vcond_mask_v2hfqi = 11695,
  CODE_FOR_vcond_mask_v2bfqi = 11696,
  CODE_FOR_vcond_mask_v2hiqi = 11697,
  CODE_FOR_vec_cmpv2bfqi = 11698,
  CODE_FOR_vec_cmpv4bfqi = 11699,
  CODE_FOR_btruncv2hf2 = 11700,
  CODE_FOR_btruncv4hf2 = 11701,
  CODE_FOR_nearbyintv2hf2 = 11702,
  CODE_FOR_nearbyintv4hf2 = 11703,
  CODE_FOR_rintv2hf2 = 11704,
  CODE_FOR_rintv4hf2 = 11705,
  CODE_FOR_lrintv2hfv2hi2 = 11706,
  CODE_FOR_lrintv4hfv4hi2 = 11707,
  CODE_FOR_floorv2hf2 = 11708,
  CODE_FOR_floorv4hf2 = 11709,
  CODE_FOR_lfloorv2hfv2hi2 = 11710,
  CODE_FOR_lfloorv4hfv4hi2 = 11711,
  CODE_FOR_ceilv2hf2 = 11712,
  CODE_FOR_ceilv4hf2 = 11713,
  CODE_FOR_lceilv2hfv2hi2 = 11714,
  CODE_FOR_lceilv4hfv4hi2 = 11715,
  CODE_FOR_roundv2hf2 = 11716,
  CODE_FOR_roundv4hf2 = 11717,
  CODE_FOR_lroundv2hfv2hi2 = 11718,
  CODE_FOR_lroundv4hfv4hi2 = 11719,
  CODE_FOR_copysignv2bf3 = 11720,
  CODE_FOR_copysignv4bf3 = 11721,
  CODE_FOR_copysignv2hf3 = 11722,
  CODE_FOR_copysignv4hf3 = 11723,
  CODE_FOR_xorsignv2bf3 = 11724,
  CODE_FOR_xorsignv4bf3 = 11725,
  CODE_FOR_xorsignv2hf3 = 11726,
  CODE_FOR_xorsignv4hf3 = 11727,
  CODE_FOR_signbitv2bf2 = 11728,
  CODE_FOR_signbitv4bf2 = 11729,
  CODE_FOR_signbitv2hf2 = 11730,
  CODE_FOR_signbitv4hf2 = 11731,
  CODE_FOR_fmav2hf4 = 11732,
  CODE_FOR_fmav4hf4 = 11733,
  CODE_FOR_fmsv2hf4 = 11734,
  CODE_FOR_fmsv4hf4 = 11735,
  CODE_FOR_fnmav2hf4 = 11736,
  CODE_FOR_fnmav4hf4 = 11737,
  CODE_FOR_fnmsv2hf4 = 11738,
  CODE_FOR_fnmsv4hf4 = 11739,
  CODE_FOR_vec_fmaddsubv4hf4 = 11740,
  CODE_FOR_vec_fmsubaddv4hf4 = 11741,
  CODE_FOR_fmav2bf4 = 11742,
  CODE_FOR_fmav4bf4 = 11743,
  CODE_FOR_fmsv2bf4 = 11744,
  CODE_FOR_fmsv4bf4 = 11745,
  CODE_FOR_fnmav2bf4 = 11746,
  CODE_FOR_fnmav4bf4 = 11747,
  CODE_FOR_fnmsv2bf4 = 11748,
  CODE_FOR_fnmsv4bf4 = 11749,
  CODE_FOR_cmlav4hf4 = 11750,
  CODE_FOR_cmla_conjv4hf4 = 11751,
  CODE_FOR_cmulv4hf3 = 11752,
  CODE_FOR_cmul_conjv4hf3 = 11753,
  CODE_FOR_fix_truncv2hfv2hi2 = 11754,
  CODE_FOR_fixuns_truncv2hfv2hi2 = 11755,
  CODE_FOR_fix_truncv4hfv4hi2 = 11756,
  CODE_FOR_fixuns_truncv4hfv4hi2 = 11757,
  CODE_FOR_fix_truncv2hfv2si2 = 11758,
  CODE_FOR_fixuns_truncv2hfv2si2 = 11759,
  CODE_FOR_floatv2hiv2hf2 = 11760,
  CODE_FOR_floatunsv2hiv2hf2 = 11761,
  CODE_FOR_floatv4hiv4hf2 = 11762,
  CODE_FOR_floatunsv4hiv4hf2 = 11763,
  CODE_FOR_floatv2siv2hf2 = 11764,
  CODE_FOR_floatunsv2siv2hf2 = 11765,
  CODE_FOR_extendv2hfv2sf2 = 11766,
  CODE_FOR_truncv2sfv2hf2 = 11767,
  CODE_FOR_truncv2sfv2bf2 = 11768,
  CODE_FOR_extendv2bfv2sf2 = 11769,
  CODE_FOR_negv8qi2 = 11770,
  CODE_FOR_negv4hi2 = 11771,
  CODE_FOR_negv2si2 = 11772,
  CODE_FOR_negv4qi2 = 11773,
  CODE_FOR_negv2hi2 = 11774,
  CODE_FOR_mmx_addv8qi3 = 11775,
  CODE_FOR_mmx_subv8qi3 = 11776,
  CODE_FOR_mmx_addv4hi3 = 11777,
  CODE_FOR_mmx_subv4hi3 = 11778,
  CODE_FOR_mmx_addv2si3 = 11779,
  CODE_FOR_mmx_subv2si3 = 11780,
  CODE_FOR_mmx_addv1di3 = 11781,
  CODE_FOR_mmx_subv1di3 = 11782,
  CODE_FOR_addv8qi3 = 11783,
  CODE_FOR_subv8qi3 = 11784,
  CODE_FOR_addv4hi3 = 11785,
  CODE_FOR_subv4hi3 = 11786,
  CODE_FOR_addv2si3 = 11787,
  CODE_FOR_subv2si3 = 11788,
  CODE_FOR_mmx_ssaddv8qi3 = 11789,
  CODE_FOR_mmx_usaddv8qi3 = 11790,
  CODE_FOR_mmx_sssubv8qi3 = 11791,
  CODE_FOR_mmx_ussubv8qi3 = 11792,
  CODE_FOR_mmx_ssaddv4hi3 = 11793,
  CODE_FOR_mmx_usaddv4hi3 = 11794,
  CODE_FOR_mmx_sssubv4hi3 = 11795,
  CODE_FOR_mmx_ussubv4hi3 = 11796,
  CODE_FOR_ssaddv8qi3 = 11797,
  CODE_FOR_usaddv8qi3 = 11798,
  CODE_FOR_sssubv8qi3 = 11799,
  CODE_FOR_ussubv8qi3 = 11800,
  CODE_FOR_ssaddv4hi3 = 11801,
  CODE_FOR_usaddv4hi3 = 11802,
  CODE_FOR_sssubv4hi3 = 11803,
  CODE_FOR_ussubv4hi3 = 11804,
  CODE_FOR_mmx_mulv4hi3 = 11805,
  CODE_FOR_mulv4hi3 = 11806,
  CODE_FOR_mulv8qi3 = 11807,
  CODE_FOR_mulv4qi3 = 11808,
  CODE_FOR_mmx_smulv4hi3_highpart = 11809,
  CODE_FOR_mmx_umulv4hi3_highpart = 11810,
  CODE_FOR_smulv4hi3_highpart = 11811,
  CODE_FOR_umulv4hi3_highpart = 11812,
  CODE_FOR_mmx_pmaddwd = 11813,
  CODE_FOR_mmx_pmulhrwv4hi3 = 11814,
  CODE_FOR_sse2_umulv1siv1di3 = 11815,
  CODE_FOR_mmx_smaxv4hi3 = 11816,
  CODE_FOR_mmx_sminv4hi3 = 11817,
  CODE_FOR_smaxv4hi3 = 11818,
  CODE_FOR_sminv4hi3 = 11819,
  CODE_FOR_mmx_umaxv8qi3 = 11820,
  CODE_FOR_mmx_uminv8qi3 = 11821,
  CODE_FOR_umaxv8qi3 = 11822,
  CODE_FOR_uminv8qi3 = 11823,
  CODE_FOR_absv8qi2 = 11824,
  CODE_FOR_absv4hi2 = 11825,
  CODE_FOR_absv2si2 = 11826,
  CODE_FOR_ashrv4hi3 = 11827,
  CODE_FOR_ashrv2si3 = 11828,
  CODE_FOR_ashlv4hi3 = 11829,
  CODE_FOR_lshrv4hi3 = 11830,
  CODE_FOR_ashlv2si3 = 11831,
  CODE_FOR_lshrv2si3 = 11832,
  CODE_FOR_ashlv8qi3 = 11833,
  CODE_FOR_lshrv8qi3 = 11834,
  CODE_FOR_ashrv8qi3 = 11835,
  CODE_FOR_ashlv4qi3 = 11836,
  CODE_FOR_lshrv4qi3 = 11837,
  CODE_FOR_ashrv4qi3 = 11838,
  CODE_FOR_vashlv8qi3 = 11839,
  CODE_FOR_vlshrv8qi3 = 11840,
  CODE_FOR_vashrv8qi3 = 11841,
  CODE_FOR_vashlv4qi3 = 11842,
  CODE_FOR_vlshrv4qi3 = 11843,
  CODE_FOR_vashrv4qi3 = 11844,
  CODE_FOR_vec_shl_v2sf = 11845,
  CODE_FOR_vec_shl_v2si = 11846,
  CODE_FOR_vec_shl_v4hf = 11847,
  CODE_FOR_vec_shl_v4bf = 11848,
  CODE_FOR_vec_shl_v4hi = 11849,
  CODE_FOR_vec_shl_v8qi = 11850,
  CODE_FOR_vec_shl_v2hf = 11851,
  CODE_FOR_vec_shl_v2bf = 11852,
  CODE_FOR_vec_shl_v2hi = 11853,
  CODE_FOR_vec_shl_v4qi = 11854,
  CODE_FOR_vec_shr_v2sf = 11855,
  CODE_FOR_vec_shr_v2si = 11856,
  CODE_FOR_vec_shr_v4hf = 11857,
  CODE_FOR_vec_shr_v4bf = 11858,
  CODE_FOR_vec_shr_v4hi = 11859,
  CODE_FOR_vec_shr_v8qi = 11860,
  CODE_FOR_vec_shr_v2hf = 11861,
  CODE_FOR_vec_shr_v2bf = 11862,
  CODE_FOR_vec_shr_v2hi = 11863,
  CODE_FOR_vec_shr_v4qi = 11864,
  CODE_FOR_mmx_eqv8qi3 = 11865,
  CODE_FOR_mmx_eqv4hi3 = 11866,
  CODE_FOR_mmx_eqv2si3 = 11867,
  CODE_FOR_vec_cmpv8qiv8qi = 11868,
  CODE_FOR_vec_cmpv4hiv4hi = 11869,
  CODE_FOR_vec_cmpv2siv2si = 11870,
  CODE_FOR_vec_cmpv4qiv4qi = 11871,
  CODE_FOR_vec_cmpv2qiv2qi = 11872,
  CODE_FOR_vec_cmpv2hiv2hi = 11873,
  CODE_FOR_vec_cmpuv8qiv8qi = 11874,
  CODE_FOR_vec_cmpuv4hiv4hi = 11875,
  CODE_FOR_vec_cmpuv2siv2si = 11876,
  CODE_FOR_vec_cmpuv4qiv4qi = 11877,
  CODE_FOR_vec_cmpuv2qiv2qi = 11878,
  CODE_FOR_vec_cmpuv2hiv2hi = 11879,
  CODE_FOR_vcond_mask_v8qiv8qi = 11880,
  CODE_FOR_vcond_mask_v4hiv4hi = 11881,
  CODE_FOR_vcond_mask_v2siv2si = 11882,
  CODE_FOR_vcond_mask_v2sfv2si = 11883,
  CODE_FOR_vcond_mask_v4qiv4qi = 11884,
  CODE_FOR_vcond_mask_v2qiv2qi = 11885,
  CODE_FOR_vcond_mask_v2hiv2hi = 11886,
  CODE_FOR_one_cmplv8qi2 = 11887,
  CODE_FOR_one_cmplv4hi2 = 11888,
  CODE_FOR_one_cmplv2si2 = 11889,
  CODE_FOR_andnv8qi3 = 11890,
  CODE_FOR_andnv4hi3 = 11891,
  CODE_FOR_andnv2si3 = 11892,
  CODE_FOR_mmx_andv8qi3 = 11893,
  CODE_FOR_mmx_iorv8qi3 = 11894,
  CODE_FOR_mmx_xorv8qi3 = 11895,
  CODE_FOR_mmx_andv4hi3 = 11896,
  CODE_FOR_mmx_iorv4hi3 = 11897,
  CODE_FOR_mmx_xorv4hi3 = 11898,
  CODE_FOR_mmx_andv2si3 = 11899,
  CODE_FOR_mmx_iorv2si3 = 11900,
  CODE_FOR_mmx_xorv2si3 = 11901,
  CODE_FOR_andv8qi3 = 11902,
  CODE_FOR_iorv8qi3 = 11903,
  CODE_FOR_xorv8qi3 = 11904,
  CODE_FOR_andv4hi3 = 11905,
  CODE_FOR_iorv4hi3 = 11906,
  CODE_FOR_xorv4hi3 = 11907,
  CODE_FOR_andv2si3 = 11908,
  CODE_FOR_iorv2si3 = 11909,
  CODE_FOR_xorv2si3 = 11910,
  CODE_FOR_andv4qi3 = 11911,
  CODE_FOR_iorv4qi3 = 11912,
  CODE_FOR_xorv4qi3 = 11913,
  CODE_FOR_andv2qi3 = 11914,
  CODE_FOR_iorv2qi3 = 11915,
  CODE_FOR_xorv2qi3 = 11916,
  CODE_FOR_andv2hi3 = 11917,
  CODE_FOR_iorv2hi3 = 11918,
  CODE_FOR_xorv2hi3 = 11919,
  CODE_FOR_extendv4qiv4hi2 = 11920,
  CODE_FOR_zero_extendv4qiv4hi2 = 11921,
  CODE_FOR_extendv2hiv2si2 = 11922,
  CODE_FOR_zero_extendv2hiv2si2 = 11923,
  CODE_FOR_extendv2qiv2si2 = 11924,
  CODE_FOR_zero_extendv2qiv2si2 = 11925,
  CODE_FOR_extendv2qiv2hi2 = 11926,
  CODE_FOR_zero_extendv2qiv2hi2 = 11927,
  CODE_FOR_truncv4hiv4qi2 = 11928,
  CODE_FOR_truncv2hiv2qi2 = 11929,
  CODE_FOR_truncv2siv2qi2 = 11930,
  CODE_FOR_truncv2siv2hi2 = 11931,
  CODE_FOR_vec_pack_trunc_v4hi = 11932,
  CODE_FOR_vec_pack_trunc_v2si = 11933,
  CODE_FOR_vec_pack_trunc_v2hi = 11934,
  CODE_FOR_vec_unpacks_lo_v8qi = 11935,
  CODE_FOR_vec_unpacks_lo_v4hi = 11936,
  CODE_FOR_vec_unpacks_hi_v8qi = 11937,
  CODE_FOR_vec_unpacks_hi_v4hi = 11938,
  CODE_FOR_vec_unpacku_lo_v8qi = 11939,
  CODE_FOR_vec_unpacku_lo_v4hi = 11940,
  CODE_FOR_vec_unpacku_hi_v8qi = 11941,
  CODE_FOR_vec_unpacku_hi_v4hi = 11942,
  CODE_FOR_vec_unpacks_lo_v4qi = 11943,
  CODE_FOR_vec_unpacks_hi_v4qi = 11944,
  CODE_FOR_vec_unpacku_lo_v4qi = 11945,
  CODE_FOR_vec_unpacku_hi_v4qi = 11946,
  CODE_FOR_mmx_pshufw = 11947,
  CODE_FOR_vec_setv2si = 11948,
  CODE_FOR_vec_extractv2sisi = 11949,
  CODE_FOR_vec_initv2sisi = 11950,
  CODE_FOR_vec_setv4hf = 11951,
  CODE_FOR_vec_setv4bf = 11952,
  CODE_FOR_vec_setv4hi = 11953,
  CODE_FOR_vec_extractv4hfhf = 11954,
  CODE_FOR_vec_extractv4bfbf = 11955,
  CODE_FOR_vec_extractv4hihi = 11956,
  CODE_FOR_vec_initv4hihi = 11957,
  CODE_FOR_vec_initv4hfhf = 11958,
  CODE_FOR_vec_initv4bfbf = 11959,
  CODE_FOR_vec_setv8qi = 11960,
  CODE_FOR_vec_extractv8qiqi = 11961,
  CODE_FOR_vec_initv8qiqi = 11962,
  CODE_FOR_vec_setv2hf = 11963,
  CODE_FOR_vec_setv2bf = 11964,
  CODE_FOR_vec_setv2hi = 11965,
  CODE_FOR_vec_extractv2hfhf = 11966,
  CODE_FOR_vec_extractv2bfbf = 11967,
  CODE_FOR_vec_extractv2hihi = 11968,
  CODE_FOR_vec_setv4qi = 11969,
  CODE_FOR_vec_extractv4qiqi = 11970,
  CODE_FOR_vec_initv2hfhf = 11971,
  CODE_FOR_vec_initv2bfbf = 11972,
  CODE_FOR_vec_initv2hihi = 11973,
  CODE_FOR_vec_initv4qiqi = 11974,
  CODE_FOR_mmx_uavgv8qi3 = 11975,
  CODE_FOR_mmx_uavgv4hi3 = 11976,
  CODE_FOR_uavgv8qi3_ceil = 11977,
  CODE_FOR_uavgv4hi3_ceil = 11978,
  CODE_FOR_mmx_psadbw = 11979,
  CODE_FOR_reduc_and_scal_v8qi = 11980,
  CODE_FOR_reduc_ior_scal_v8qi = 11981,
  CODE_FOR_reduc_xor_scal_v8qi = 11982,
  CODE_FOR_reduc_and_scal_v4hi = 11983,
  CODE_FOR_reduc_ior_scal_v4hi = 11984,
  CODE_FOR_reduc_xor_scal_v4hi = 11985,
  CODE_FOR_reduc_and_scal_v4qi = 11986,
  CODE_FOR_reduc_ior_scal_v4qi = 11987,
  CODE_FOR_reduc_xor_scal_v4qi = 11988,
  CODE_FOR_reduc_plus_scal_v8qi = 11989,
  CODE_FOR_reduc_plus_scal_v4hi = 11990,
  CODE_FOR_reduc_smax_scal_v4hi = 11991,
  CODE_FOR_reduc_smin_scal_v4hi = 11992,
  CODE_FOR_reduc_smax_scal_v4qi = 11993,
  CODE_FOR_reduc_smin_scal_v4qi = 11994,
  CODE_FOR_reduc_umax_scal_v4hi = 11995,
  CODE_FOR_reduc_umin_scal_v4hi = 11996,
  CODE_FOR_reduc_umax_scal_v4qi = 11997,
  CODE_FOR_reduc_umin_scal_v4qi = 11998,
  CODE_FOR_reduc_plus_scal_v4qi = 11999,
  CODE_FOR_usadv8qi = 12000,
  CODE_FOR_usdot_prodv2siv8qi = 12001,
  CODE_FOR_sdot_prodv2siv8qi = 12002,
  CODE_FOR_udot_prodv2siv8qi = 12003,
  CODE_FOR_usdot_prodv2siv4hi = 12004,
  CODE_FOR_udot_prodv2siv4hi = 12005,
  CODE_FOR_sdot_prodv2siv4hi = 12006,
  CODE_FOR_mmx_maskmovq = 12007,
  CODE_FOR_mmx_emms = 12008,
  CODE_FOR_mmx_femms = 12009,
  CODE_FOR_movv64qi = 12010,
  CODE_FOR_movv32qi = 12011,
  CODE_FOR_movv16qi = 12012,
  CODE_FOR_movv32hi = 12013,
  CODE_FOR_movv16hi = 12014,
  CODE_FOR_movv8hi = 12015,
  CODE_FOR_movv16si = 12016,
  CODE_FOR_movv8si = 12017,
  CODE_FOR_movv4si = 12018,
  CODE_FOR_movv8di = 12019,
  CODE_FOR_movv4di = 12020,
  CODE_FOR_movv2di = 12021,
  CODE_FOR_movv4ti = 12022,
  CODE_FOR_movv2ti = 12023,
  CODE_FOR_movv1ti = 12024,
  CODE_FOR_movv32hf = 12025,
  CODE_FOR_movv16hf = 12026,
  CODE_FOR_movv8hf = 12027,
  CODE_FOR_movv32bf = 12028,
  CODE_FOR_movv16bf = 12029,
  CODE_FOR_movv8bf = 12030,
  CODE_FOR_movv16sf = 12031,
  CODE_FOR_movv8sf = 12032,
  CODE_FOR_movv4sf = 12033,
  CODE_FOR_movv8df = 12034,
  CODE_FOR_movv4df = 12035,
  CODE_FOR_movv2df = 12036,
  CODE_FOR_avx512f_loadv16si_mask = 12037,
  CODE_FOR_avx512vl_loadv8si_mask = 12038,
  CODE_FOR_avx512vl_loadv4si_mask = 12039,
  CODE_FOR_avx512f_loadv8di_mask = 12040,
  CODE_FOR_avx512vl_loadv4di_mask = 12041,
  CODE_FOR_avx512vl_loadv2di_mask = 12042,
  CODE_FOR_avx512f_loadv16sf_mask = 12043,
  CODE_FOR_avx512vl_loadv8sf_mask = 12044,
  CODE_FOR_avx512vl_loadv4sf_mask = 12045,
  CODE_FOR_avx512f_loadv8df_mask = 12046,
  CODE_FOR_avx512vl_loadv4df_mask = 12047,
  CODE_FOR_avx512vl_loadv2df_mask = 12048,
  CODE_FOR_avx512bw_loadv64qi_mask = 12049,
  CODE_FOR_avx512vl_loadv16qi_mask = 12050,
  CODE_FOR_avx512vl_loadv32qi_mask = 12051,
  CODE_FOR_avx512bw_loadv32hi_mask = 12052,
  CODE_FOR_avx512vl_loadv16hi_mask = 12053,
  CODE_FOR_avx512vl_loadv8hi_mask = 12054,
  CODE_FOR_avx512f_loadhf_mask = 12055,
  CODE_FOR_avx512f_loadsf_mask = 12056,
  CODE_FOR_avx512f_loaddf_mask = 12057,
  CODE_FOR_sse2_movq128 = 12058,
  CODE_FOR_movmisalignv64qi = 12059,
  CODE_FOR_movmisalignv32qi = 12060,
  CODE_FOR_movmisalignv16qi = 12061,
  CODE_FOR_movmisalignv32hi = 12062,
  CODE_FOR_movmisalignv16hi = 12063,
  CODE_FOR_movmisalignv8hi = 12064,
  CODE_FOR_movmisalignv16si = 12065,
  CODE_FOR_movmisalignv8si = 12066,
  CODE_FOR_movmisalignv4si = 12067,
  CODE_FOR_movmisalignv8di = 12068,
  CODE_FOR_movmisalignv4di = 12069,
  CODE_FOR_movmisalignv2di = 12070,
  CODE_FOR_movmisalignv4ti = 12071,
  CODE_FOR_movmisalignv2ti = 12072,
  CODE_FOR_movmisalignv1ti = 12073,
  CODE_FOR_movmisalignv32hf = 12074,
  CODE_FOR_movmisalignv16hf = 12075,
  CODE_FOR_movmisalignv8hf = 12076,
  CODE_FOR_movmisalignv32bf = 12077,
  CODE_FOR_movmisalignv16bf = 12078,
  CODE_FOR_movmisalignv8bf = 12079,
  CODE_FOR_movmisalignv16sf = 12080,
  CODE_FOR_movmisalignv8sf = 12081,
  CODE_FOR_movmisalignv4sf = 12082,
  CODE_FOR_movmisalignv8df = 12083,
  CODE_FOR_movmisalignv4df = 12084,
  CODE_FOR_movmisalignv2df = 12085,
  CODE_FOR_storentdi = 12086,
  CODE_FOR_storentsi = 12087,
  CODE_FOR_storentsf = 12088,
  CODE_FOR_storentdf = 12089,
  CODE_FOR_storentv8di = 12090,
  CODE_FOR_storentv4di = 12091,
  CODE_FOR_storentv2di = 12092,
  CODE_FOR_storentv16sf = 12093,
  CODE_FOR_storentv8sf = 12094,
  CODE_FOR_storentv4sf = 12095,
  CODE_FOR_storentv8df = 12096,
  CODE_FOR_storentv4df = 12097,
  CODE_FOR_storentv2df = 12098,
  CODE_FOR_kmovb = 12099,
  CODE_FOR_kmovw = 12100,
  CODE_FOR_kmovd = 12101,
  CODE_FOR_kmovq = 12102,
  CODE_FOR_kortestqi = 12103,
  CODE_FOR_kortesthi = 12104,
  CODE_FOR_kortestsi = 12105,
  CODE_FOR_kortestdi = 12106,
  CODE_FOR_absv32bf2 = 12107,
  CODE_FOR_negv32bf2 = 12108,
  CODE_FOR_absv16bf2 = 12109,
  CODE_FOR_negv16bf2 = 12110,
  CODE_FOR_absv8bf2 = 12111,
  CODE_FOR_negv8bf2 = 12112,
  CODE_FOR_absv32hf2 = 12113,
  CODE_FOR_negv32hf2 = 12114,
  CODE_FOR_absv16hf2 = 12115,
  CODE_FOR_negv16hf2 = 12116,
  CODE_FOR_absv8hf2 = 12117,
  CODE_FOR_negv8hf2 = 12118,
  CODE_FOR_absv16sf2 = 12119,
  CODE_FOR_negv16sf2 = 12120,
  CODE_FOR_absv8sf2 = 12121,
  CODE_FOR_negv8sf2 = 12122,
  CODE_FOR_absv4sf2 = 12123,
  CODE_FOR_negv4sf2 = 12124,
  CODE_FOR_absv8df2 = 12125,
  CODE_FOR_negv8df2 = 12126,
  CODE_FOR_absv4df2 = 12127,
  CODE_FOR_negv4df2 = 12128,
  CODE_FOR_absv2df2 = 12129,
  CODE_FOR_negv2df2 = 12130,
  CODE_FOR_cond_addv32hf = 12131,
  CODE_FOR_cond_subv32hf = 12132,
  CODE_FOR_cond_addv16hf = 12133,
  CODE_FOR_cond_subv16hf = 12134,
  CODE_FOR_cond_addv8hf = 12135,
  CODE_FOR_cond_subv8hf = 12136,
  CODE_FOR_cond_addv16sf = 12137,
  CODE_FOR_cond_subv16sf = 12138,
  CODE_FOR_cond_addv8sf = 12139,
  CODE_FOR_cond_subv8sf = 12140,
  CODE_FOR_cond_addv4sf = 12141,
  CODE_FOR_cond_subv4sf = 12142,
  CODE_FOR_cond_addv8df = 12143,
  CODE_FOR_cond_subv8df = 12144,
  CODE_FOR_cond_addv4df = 12145,
  CODE_FOR_cond_subv4df = 12146,
  CODE_FOR_cond_addv2df = 12147,
  CODE_FOR_cond_subv2df = 12148,
  CODE_FOR_addv32hf3 = 12149,
  CODE_FOR_addv32hf3_round = 12150,
  CODE_FOR_addv32hf3_mask = 12151,
  CODE_FOR_addv32hf3_mask_round = 12152,
  CODE_FOR_subv32hf3 = 12153,
  CODE_FOR_subv32hf3_round = 12154,
  CODE_FOR_subv32hf3_mask = 12155,
  CODE_FOR_subv32hf3_mask_round = 12156,
  CODE_FOR_addv16hf3 = 12157,
   CODE_FOR_addv16hf3_round = CODE_FOR_nothing,
  CODE_FOR_addv16hf3_mask = 12158,
   CODE_FOR_addv16hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_subv16hf3 = 12159,
   CODE_FOR_subv16hf3_round = CODE_FOR_nothing,
  CODE_FOR_subv16hf3_mask = 12160,
   CODE_FOR_subv16hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_addv8hf3 = 12161,
   CODE_FOR_addv8hf3_round = CODE_FOR_nothing,
  CODE_FOR_addv8hf3_mask = 12162,
   CODE_FOR_addv8hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_subv8hf3 = 12163,
   CODE_FOR_subv8hf3_round = CODE_FOR_nothing,
  CODE_FOR_subv8hf3_mask = 12164,
   CODE_FOR_subv8hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_addv16sf3 = 12165,
  CODE_FOR_addv16sf3_round = 12166,
  CODE_FOR_addv16sf3_mask = 12167,
  CODE_FOR_addv16sf3_mask_round = 12168,
  CODE_FOR_subv16sf3 = 12169,
  CODE_FOR_subv16sf3_round = 12170,
  CODE_FOR_subv16sf3_mask = 12171,
  CODE_FOR_subv16sf3_mask_round = 12172,
  CODE_FOR_addv8sf3 = 12173,
   CODE_FOR_addv8sf3_round = CODE_FOR_nothing,
  CODE_FOR_addv8sf3_mask = 12174,
   CODE_FOR_addv8sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_subv8sf3 = 12175,
   CODE_FOR_subv8sf3_round = CODE_FOR_nothing,
  CODE_FOR_subv8sf3_mask = 12176,
   CODE_FOR_subv8sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_addv4sf3 = 12177,
   CODE_FOR_addv4sf3_round = CODE_FOR_nothing,
  CODE_FOR_addv4sf3_mask = 12178,
   CODE_FOR_addv4sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_subv4sf3 = 12179,
   CODE_FOR_subv4sf3_round = CODE_FOR_nothing,
  CODE_FOR_subv4sf3_mask = 12180,
   CODE_FOR_subv4sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_addv8df3 = 12181,
  CODE_FOR_addv8df3_round = 12182,
  CODE_FOR_addv8df3_mask = 12183,
  CODE_FOR_addv8df3_mask_round = 12184,
  CODE_FOR_subv8df3 = 12185,
  CODE_FOR_subv8df3_round = 12186,
  CODE_FOR_subv8df3_mask = 12187,
  CODE_FOR_subv8df3_mask_round = 12188,
  CODE_FOR_addv4df3 = 12189,
   CODE_FOR_addv4df3_round = CODE_FOR_nothing,
  CODE_FOR_addv4df3_mask = 12190,
   CODE_FOR_addv4df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_subv4df3 = 12191,
   CODE_FOR_subv4df3_round = CODE_FOR_nothing,
  CODE_FOR_subv4df3_mask = 12192,
   CODE_FOR_subv4df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_addv2df3 = 12193,
   CODE_FOR_addv2df3_round = CODE_FOR_nothing,
  CODE_FOR_addv2df3_mask = 12194,
   CODE_FOR_addv2df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_subv2df3 = 12195,
   CODE_FOR_subv2df3_round = CODE_FOR_nothing,
  CODE_FOR_subv2df3_mask = 12196,
   CODE_FOR_subv2df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_addv32bf3 = 12197,
  CODE_FOR_addv32bf3_mask = 12198,
  CODE_FOR_subv32bf3 = 12199,
  CODE_FOR_subv32bf3_mask = 12200,
  CODE_FOR_addv16bf3 = 12201,
  CODE_FOR_addv16bf3_mask = 12202,
  CODE_FOR_subv16bf3 = 12203,
  CODE_FOR_subv16bf3_mask = 12204,
  CODE_FOR_addv8bf3 = 12205,
  CODE_FOR_addv8bf3_mask = 12206,
  CODE_FOR_subv8bf3 = 12207,
  CODE_FOR_subv8bf3_mask = 12208,
  CODE_FOR_cond_mulv32hf = 12209,
  CODE_FOR_cond_mulv16hf = 12210,
  CODE_FOR_cond_mulv8hf = 12211,
  CODE_FOR_cond_mulv16sf = 12212,
  CODE_FOR_cond_mulv8sf = 12213,
  CODE_FOR_cond_mulv4sf = 12214,
  CODE_FOR_cond_mulv8df = 12215,
  CODE_FOR_cond_mulv4df = 12216,
  CODE_FOR_cond_mulv2df = 12217,
  CODE_FOR_mulv32hf3 = 12218,
  CODE_FOR_mulv32hf3_round = 12219,
  CODE_FOR_mulv32hf3_mask = 12220,
  CODE_FOR_mulv32hf3_mask_round = 12221,
  CODE_FOR_mulv16hf3 = 12222,
   CODE_FOR_mulv16hf3_round = CODE_FOR_nothing,
  CODE_FOR_mulv16hf3_mask = 12223,
   CODE_FOR_mulv16hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_mulv8hf3 = 12224,
   CODE_FOR_mulv8hf3_round = CODE_FOR_nothing,
  CODE_FOR_mulv8hf3_mask = 12225,
   CODE_FOR_mulv8hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_mulv16sf3 = 12226,
  CODE_FOR_mulv16sf3_round = 12227,
  CODE_FOR_mulv16sf3_mask = 12228,
  CODE_FOR_mulv16sf3_mask_round = 12229,
  CODE_FOR_mulv8sf3 = 12230,
   CODE_FOR_mulv8sf3_round = CODE_FOR_nothing,
  CODE_FOR_mulv8sf3_mask = 12231,
   CODE_FOR_mulv8sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_mulv4sf3 = 12232,
   CODE_FOR_mulv4sf3_round = CODE_FOR_nothing,
  CODE_FOR_mulv4sf3_mask = 12233,
   CODE_FOR_mulv4sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_mulv8df3 = 12234,
  CODE_FOR_mulv8df3_round = 12235,
  CODE_FOR_mulv8df3_mask = 12236,
  CODE_FOR_mulv8df3_mask_round = 12237,
  CODE_FOR_mulv4df3 = 12238,
   CODE_FOR_mulv4df3_round = CODE_FOR_nothing,
  CODE_FOR_mulv4df3_mask = 12239,
   CODE_FOR_mulv4df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_mulv2df3 = 12240,
   CODE_FOR_mulv2df3_round = CODE_FOR_nothing,
  CODE_FOR_mulv2df3_mask = 12241,
   CODE_FOR_mulv2df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_mulv32bf3 = 12242,
  CODE_FOR_mulv32bf3_mask = 12243,
  CODE_FOR_mulv16bf3 = 12244,
  CODE_FOR_mulv16bf3_mask = 12245,
  CODE_FOR_mulv8bf3 = 12246,
  CODE_FOR_mulv8bf3_mask = 12247,
  CODE_FOR_divv8df3 = 12248,
  CODE_FOR_divv4df3 = 12249,
  CODE_FOR_divv2df3 = 12250,
  CODE_FOR_divv32hf3 = 12251,
  CODE_FOR_divv16hf3 = 12252,
  CODE_FOR_divv8hf3 = 12253,
  CODE_FOR_divv16sf3 = 12254,
  CODE_FOR_divv8sf3 = 12255,
  CODE_FOR_divv4sf3 = 12256,
  CODE_FOR_divv32bf3 = 12257,
  CODE_FOR_divv16bf3 = 12258,
  CODE_FOR_divv8bf3 = 12259,
  CODE_FOR_cond_divv32hf = 12260,
  CODE_FOR_cond_divv16hf = 12261,
  CODE_FOR_cond_divv8hf = 12262,
  CODE_FOR_cond_divv16sf = 12263,
  CODE_FOR_cond_divv8sf = 12264,
  CODE_FOR_cond_divv4sf = 12265,
  CODE_FOR_cond_divv8df = 12266,
  CODE_FOR_cond_divv4df = 12267,
  CODE_FOR_cond_divv2df = 12268,
  CODE_FOR_sqrtv32bf2 = 12269,
  CODE_FOR_sqrtv16bf2 = 12270,
  CODE_FOR_sqrtv8bf2 = 12271,
  CODE_FOR_sqrtv32hf2 = 12272,
  CODE_FOR_sqrtv16hf2 = 12273,
  CODE_FOR_sqrtv8hf2 = 12274,
  CODE_FOR_sqrtv8df2 = 12275,
  CODE_FOR_sqrtv4df2 = 12276,
  CODE_FOR_sqrtv2df2 = 12277,
  CODE_FOR_sqrtv16sf2 = 12278,
  CODE_FOR_sqrtv8sf2 = 12279,
  CODE_FOR_sqrtv4sf2 = 12280,
  CODE_FOR_rsqrtv8sf2 = 12281,
  CODE_FOR_rsqrtv4sf2 = 12282,
  CODE_FOR_rsqrtv32hf2 = 12283,
  CODE_FOR_rsqrtv16hf2 = 12284,
  CODE_FOR_rsqrtv8hf2 = 12285,
  CODE_FOR_cond_smaxv32hf = 12286,
  CODE_FOR_cond_sminv32hf = 12287,
  CODE_FOR_cond_smaxv16hf = 12288,
  CODE_FOR_cond_sminv16hf = 12289,
  CODE_FOR_cond_smaxv8hf = 12290,
  CODE_FOR_cond_sminv8hf = 12291,
  CODE_FOR_cond_smaxv16sf = 12292,
  CODE_FOR_cond_sminv16sf = 12293,
  CODE_FOR_cond_smaxv8sf = 12294,
  CODE_FOR_cond_sminv8sf = 12295,
  CODE_FOR_cond_smaxv4sf = 12296,
  CODE_FOR_cond_sminv4sf = 12297,
  CODE_FOR_cond_smaxv8df = 12298,
  CODE_FOR_cond_sminv8df = 12299,
  CODE_FOR_cond_smaxv4df = 12300,
  CODE_FOR_cond_sminv4df = 12301,
  CODE_FOR_cond_smaxv2df = 12302,
  CODE_FOR_cond_sminv2df = 12303,
  CODE_FOR_smaxv32hf3 = 12304,
  CODE_FOR_smaxv32hf3_round = 12305,
  CODE_FOR_smaxv32hf3_mask = 12306,
  CODE_FOR_smaxv32hf3_mask_round = 12307,
  CODE_FOR_sminv32hf3 = 12308,
  CODE_FOR_sminv32hf3_round = 12309,
  CODE_FOR_sminv32hf3_mask = 12310,
  CODE_FOR_sminv32hf3_mask_round = 12311,
  CODE_FOR_smaxv16hf3 = 12312,
   CODE_FOR_smaxv16hf3_round = CODE_FOR_nothing,
  CODE_FOR_smaxv16hf3_mask = 12313,
   CODE_FOR_smaxv16hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_sminv16hf3 = 12314,
   CODE_FOR_sminv16hf3_round = CODE_FOR_nothing,
  CODE_FOR_sminv16hf3_mask = 12315,
   CODE_FOR_sminv16hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_smaxv8hf3 = 12316,
   CODE_FOR_smaxv8hf3_round = CODE_FOR_nothing,
  CODE_FOR_smaxv8hf3_mask = 12317,
   CODE_FOR_smaxv8hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_sminv8hf3 = 12318,
   CODE_FOR_sminv8hf3_round = CODE_FOR_nothing,
  CODE_FOR_sminv8hf3_mask = 12319,
   CODE_FOR_sminv8hf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_smaxv16sf3 = 12320,
  CODE_FOR_smaxv16sf3_round = 12321,
  CODE_FOR_smaxv16sf3_mask = 12322,
  CODE_FOR_smaxv16sf3_mask_round = 12323,
  CODE_FOR_sminv16sf3 = 12324,
  CODE_FOR_sminv16sf3_round = 12325,
  CODE_FOR_sminv16sf3_mask = 12326,
  CODE_FOR_sminv16sf3_mask_round = 12327,
  CODE_FOR_smaxv8sf3 = 12328,
   CODE_FOR_smaxv8sf3_round = CODE_FOR_nothing,
  CODE_FOR_smaxv8sf3_mask = 12329,
   CODE_FOR_smaxv8sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_sminv8sf3 = 12330,
   CODE_FOR_sminv8sf3_round = CODE_FOR_nothing,
  CODE_FOR_sminv8sf3_mask = 12331,
   CODE_FOR_sminv8sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_smaxv4sf3 = 12332,
   CODE_FOR_smaxv4sf3_round = CODE_FOR_nothing,
  CODE_FOR_smaxv4sf3_mask = 12333,
   CODE_FOR_smaxv4sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_sminv4sf3 = 12334,
   CODE_FOR_sminv4sf3_round = CODE_FOR_nothing,
  CODE_FOR_sminv4sf3_mask = 12335,
   CODE_FOR_sminv4sf3_mask_round = CODE_FOR_nothing,
  CODE_FOR_smaxv8df3 = 12336,
  CODE_FOR_smaxv8df3_round = 12337,
  CODE_FOR_smaxv8df3_mask = 12338,
  CODE_FOR_smaxv8df3_mask_round = 12339,
  CODE_FOR_sminv8df3 = 12340,
  CODE_FOR_sminv8df3_round = 12341,
  CODE_FOR_sminv8df3_mask = 12342,
  CODE_FOR_sminv8df3_mask_round = 12343,
  CODE_FOR_smaxv4df3 = 12344,
   CODE_FOR_smaxv4df3_round = CODE_FOR_nothing,
  CODE_FOR_smaxv4df3_mask = 12345,
   CODE_FOR_smaxv4df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_sminv4df3 = 12346,
   CODE_FOR_sminv4df3_round = CODE_FOR_nothing,
  CODE_FOR_sminv4df3_mask = 12347,
   CODE_FOR_sminv4df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_smaxv2df3 = 12348,
   CODE_FOR_smaxv2df3_round = CODE_FOR_nothing,
  CODE_FOR_smaxv2df3_mask = 12349,
   CODE_FOR_smaxv2df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_sminv2df3 = 12350,
   CODE_FOR_sminv2df3_round = CODE_FOR_nothing,
  CODE_FOR_sminv2df3_mask = 12351,
   CODE_FOR_sminv2df3_mask_round = CODE_FOR_nothing,
  CODE_FOR_avx512fp16_vmsmaxv8hf3 = 12352,
  CODE_FOR_avx512fp16_vmsmaxv8hf3_mask = 12353,
  CODE_FOR_avx512fp16_vmsmaxv8hf3_round = 12354,
  CODE_FOR_avx512fp16_vmsmaxv8hf3_mask_round = 12355,
  CODE_FOR_avx512fp16_vmsminv8hf3 = 12356,
  CODE_FOR_avx512fp16_vmsminv8hf3_mask = 12357,
  CODE_FOR_avx512fp16_vmsminv8hf3_round = 12358,
  CODE_FOR_avx512fp16_vmsminv8hf3_mask_round = 12359,
  CODE_FOR_sse_vmsmaxv4sf3 = 12360,
  CODE_FOR_sse_vmsmaxv4sf3_mask = 12361,
  CODE_FOR_sse_vmsmaxv4sf3_round = 12362,
  CODE_FOR_sse_vmsmaxv4sf3_mask_round = 12363,
  CODE_FOR_sse_vmsminv4sf3 = 12364,
  CODE_FOR_sse_vmsminv4sf3_mask = 12365,
  CODE_FOR_sse_vmsminv4sf3_round = 12366,
  CODE_FOR_sse_vmsminv4sf3_mask_round = 12367,
  CODE_FOR_sse2_vmsmaxv2df3 = 12368,
  CODE_FOR_sse2_vmsmaxv2df3_mask = 12369,
  CODE_FOR_sse2_vmsmaxv2df3_round = 12370,
  CODE_FOR_sse2_vmsmaxv2df3_mask_round = 12371,
  CODE_FOR_sse2_vmsminv2df3 = 12372,
  CODE_FOR_sse2_vmsminv2df3_mask = 12373,
  CODE_FOR_sse2_vmsminv2df3_round = 12374,
  CODE_FOR_sse2_vmsminv2df3_mask_round = 12375,
  CODE_FOR_sse3_haddv2df3 = 12376,
  CODE_FOR_reduc_plus_scal_v2df = 12377,
  CODE_FOR_reduc_plus_scal_v4sf = 12378,
  CODE_FOR_reduc_plus_scal_v8hf = 12379,
  CODE_FOR_reduc_plus_scal_v8hi = 12380,
  CODE_FOR_reduc_plus_scal_v4si = 12381,
  CODE_FOR_reduc_plus_scal_v2di = 12382,
  CODE_FOR_reduc_plus_scal_v16qi = 12383,
  CODE_FOR_reduc_plus_scal_v4df = 12384,
  CODE_FOR_reduc_plus_scal_v8sf = 12385,
  CODE_FOR_reduc_plus_scal_v16hf = 12386,
  CODE_FOR_reduc_plus_scal_v8df = 12387,
  CODE_FOR_reduc_plus_scal_v16sf = 12388,
  CODE_FOR_reduc_plus_scal_v32hf = 12389,
  CODE_FOR_reduc_plus_scal_v32qi = 12390,
  CODE_FOR_reduc_plus_scal_v16hi = 12391,
  CODE_FOR_reduc_plus_scal_v8si = 12392,
  CODE_FOR_reduc_plus_scal_v4di = 12393,
  CODE_FOR_reduc_plus_scal_v64qi = 12394,
  CODE_FOR_reduc_plus_scal_v32hi = 12395,
  CODE_FOR_reduc_plus_scal_v16si = 12396,
  CODE_FOR_reduc_plus_scal_v8di = 12397,
  CODE_FOR_reduc_smax_scal_v8hf = 12398,
  CODE_FOR_reduc_smin_scal_v8hf = 12399,
  CODE_FOR_reduc_smax_scal_v4sf = 12400,
  CODE_FOR_reduc_smin_scal_v4sf = 12401,
  CODE_FOR_reduc_smax_scal_v2df = 12402,
  CODE_FOR_reduc_smin_scal_v2df = 12403,
  CODE_FOR_reduc_smax_scal_v4si = 12404,
  CODE_FOR_reduc_smin_scal_v4si = 12405,
  CODE_FOR_reduc_smax_scal_v8hi = 12406,
  CODE_FOR_reduc_smin_scal_v8hi = 12407,
  CODE_FOR_reduc_smax_scal_v16qi = 12408,
  CODE_FOR_reduc_smin_scal_v16qi = 12409,
  CODE_FOR_reduc_smax_scal_v2di = 12410,
  CODE_FOR_reduc_smin_scal_v2di = 12411,
  CODE_FOR_reduc_smax_scal_v32qi = 12412,
  CODE_FOR_reduc_smin_scal_v32qi = 12413,
  CODE_FOR_reduc_smax_scal_v16hi = 12414,
  CODE_FOR_reduc_smin_scal_v16hi = 12415,
  CODE_FOR_reduc_smax_scal_v16hf = 12416,
  CODE_FOR_reduc_smin_scal_v16hf = 12417,
  CODE_FOR_reduc_smax_scal_v8si = 12418,
  CODE_FOR_reduc_smin_scal_v8si = 12419,
  CODE_FOR_reduc_smax_scal_v4di = 12420,
  CODE_FOR_reduc_smin_scal_v4di = 12421,
  CODE_FOR_reduc_smax_scal_v8sf = 12422,
  CODE_FOR_reduc_smin_scal_v8sf = 12423,
  CODE_FOR_reduc_smax_scal_v4df = 12424,
  CODE_FOR_reduc_smin_scal_v4df = 12425,
  CODE_FOR_reduc_smax_scal_v64qi = 12426,
  CODE_FOR_reduc_smin_scal_v64qi = 12427,
  CODE_FOR_reduc_smax_scal_v32hf = 12428,
  CODE_FOR_reduc_smin_scal_v32hf = 12429,
  CODE_FOR_reduc_smax_scal_v32hi = 12430,
  CODE_FOR_reduc_smin_scal_v32hi = 12431,
  CODE_FOR_reduc_smax_scal_v16si = 12432,
  CODE_FOR_reduc_smin_scal_v16si = 12433,
  CODE_FOR_reduc_smax_scal_v8di = 12434,
  CODE_FOR_reduc_smin_scal_v8di = 12435,
  CODE_FOR_reduc_smax_scal_v16sf = 12436,
  CODE_FOR_reduc_smin_scal_v16sf = 12437,
  CODE_FOR_reduc_smax_scal_v8df = 12438,
  CODE_FOR_reduc_smin_scal_v8df = 12439,
  CODE_FOR_reduc_umax_scal_v16si = 12440,
  CODE_FOR_reduc_umin_scal_v16si = 12441,
  CODE_FOR_reduc_umax_scal_v8di = 12442,
  CODE_FOR_reduc_umin_scal_v8di = 12443,
  CODE_FOR_reduc_umax_scal_v32hi = 12444,
  CODE_FOR_reduc_umin_scal_v32hi = 12445,
  CODE_FOR_reduc_umax_scal_v64qi = 12446,
  CODE_FOR_reduc_umin_scal_v64qi = 12447,
  CODE_FOR_reduc_umax_scal_v32qi = 12448,
  CODE_FOR_reduc_umin_scal_v32qi = 12449,
  CODE_FOR_reduc_umax_scal_v16hi = 12450,
  CODE_FOR_reduc_umin_scal_v16hi = 12451,
  CODE_FOR_reduc_umax_scal_v8si = 12452,
  CODE_FOR_reduc_umin_scal_v8si = 12453,
  CODE_FOR_reduc_umax_scal_v4di = 12454,
  CODE_FOR_reduc_umin_scal_v4di = 12455,
  CODE_FOR_reduc_umin_scal_v8hi = 12456,
  CODE_FOR_reduc_and_scal_v16qi = 12457,
  CODE_FOR_reduc_ior_scal_v16qi = 12458,
  CODE_FOR_reduc_xor_scal_v16qi = 12459,
  CODE_FOR_reduc_and_scal_v8hi = 12460,
  CODE_FOR_reduc_ior_scal_v8hi = 12461,
  CODE_FOR_reduc_xor_scal_v8hi = 12462,
  CODE_FOR_reduc_and_scal_v4si = 12463,
  CODE_FOR_reduc_ior_scal_v4si = 12464,
  CODE_FOR_reduc_xor_scal_v4si = 12465,
  CODE_FOR_reduc_and_scal_v2di = 12466,
  CODE_FOR_reduc_ior_scal_v2di = 12467,
  CODE_FOR_reduc_xor_scal_v2di = 12468,
  CODE_FOR_reduc_and_scal_v32qi = 12469,
  CODE_FOR_reduc_ior_scal_v32qi = 12470,
  CODE_FOR_reduc_xor_scal_v32qi = 12471,
  CODE_FOR_reduc_and_scal_v16hi = 12472,
  CODE_FOR_reduc_ior_scal_v16hi = 12473,
  CODE_FOR_reduc_xor_scal_v16hi = 12474,
  CODE_FOR_reduc_and_scal_v8si = 12475,
  CODE_FOR_reduc_ior_scal_v8si = 12476,
  CODE_FOR_reduc_xor_scal_v8si = 12477,
  CODE_FOR_reduc_and_scal_v4di = 12478,
  CODE_FOR_reduc_ior_scal_v4di = 12479,
  CODE_FOR_reduc_xor_scal_v4di = 12480,
  CODE_FOR_reduc_and_scal_v64qi = 12481,
  CODE_FOR_reduc_ior_scal_v64qi = 12482,
  CODE_FOR_reduc_xor_scal_v64qi = 12483,
  CODE_FOR_reduc_and_scal_v32hi = 12484,
  CODE_FOR_reduc_ior_scal_v32hi = 12485,
  CODE_FOR_reduc_xor_scal_v32hi = 12486,
  CODE_FOR_reduc_and_scal_v16si = 12487,
  CODE_FOR_reduc_ior_scal_v16si = 12488,
  CODE_FOR_reduc_xor_scal_v16si = 12489,
  CODE_FOR_reduc_and_scal_v8di = 12490,
  CODE_FOR_reduc_ior_scal_v8di = 12491,
  CODE_FOR_reduc_xor_scal_v8di = 12492,
  CODE_FOR_vec_cmpv16sihi = 12493,
  CODE_FOR_vec_cmpv8siqi = 12494,
  CODE_FOR_vec_cmpv4siqi = 12495,
  CODE_FOR_vec_cmpv8diqi = 12496,
  CODE_FOR_vec_cmpv4diqi = 12497,
  CODE_FOR_vec_cmpv2diqi = 12498,
  CODE_FOR_vec_cmpv32hfsi = 12499,
  CODE_FOR_vec_cmpv16hfhi = 12500,
  CODE_FOR_vec_cmpv8hfqi = 12501,
  CODE_FOR_vec_cmpv16sfhi = 12502,
  CODE_FOR_vec_cmpv8sfqi = 12503,
  CODE_FOR_vec_cmpv4sfqi = 12504,
  CODE_FOR_vec_cmpv8dfqi = 12505,
  CODE_FOR_vec_cmpv4dfqi = 12506,
  CODE_FOR_vec_cmpv2dfqi = 12507,
  CODE_FOR_vec_cmpv64qidi = 12508,
  CODE_FOR_vec_cmpv16qihi = 12509,
  CODE_FOR_vec_cmpv32qisi = 12510,
  CODE_FOR_vec_cmpv32hisi = 12511,
  CODE_FOR_vec_cmpv16hihi = 12512,
  CODE_FOR_vec_cmpv8hiqi = 12513,
  CODE_FOR_vec_cmpv32bfsi = 12514,
  CODE_FOR_vec_cmpv16bfhi = 12515,
  CODE_FOR_vec_cmpv8bfqi = 12516,
  CODE_FOR_vec_cmpv32qiv32qi = 12517,
  CODE_FOR_vec_cmpv16hiv16hi = 12518,
  CODE_FOR_vec_cmpv8siv8si = 12519,
  CODE_FOR_vec_cmpv4div4di = 12520,
  CODE_FOR_vec_cmpv16qiv16qi = 12521,
  CODE_FOR_vec_cmpv8hiv8hi = 12522,
  CODE_FOR_vec_cmpv4siv4si = 12523,
  CODE_FOR_vec_cmpv2div2di = 12524,
  CODE_FOR_vec_cmpv8sfv8si = 12525,
  CODE_FOR_vec_cmpv4dfv4di = 12526,
  CODE_FOR_vec_cmpv4sfv4si = 12527,
  CODE_FOR_vec_cmpv2dfv2di = 12528,
  CODE_FOR_vec_cmpuv16sihi = 12529,
  CODE_FOR_vec_cmpuv8siqi = 12530,
  CODE_FOR_vec_cmpuv4siqi = 12531,
  CODE_FOR_vec_cmpuv8diqi = 12532,
  CODE_FOR_vec_cmpuv4diqi = 12533,
  CODE_FOR_vec_cmpuv2diqi = 12534,
  CODE_FOR_vec_cmpuv64qidi = 12535,
  CODE_FOR_vec_cmpuv16qihi = 12536,
  CODE_FOR_vec_cmpuv32qisi = 12537,
  CODE_FOR_vec_cmpuv32hisi = 12538,
  CODE_FOR_vec_cmpuv16hihi = 12539,
  CODE_FOR_vec_cmpuv8hiqi = 12540,
  CODE_FOR_vec_cmpuv32qiv32qi = 12541,
  CODE_FOR_vec_cmpuv16hiv16hi = 12542,
  CODE_FOR_vec_cmpuv8siv8si = 12543,
  CODE_FOR_vec_cmpuv4div4di = 12544,
  CODE_FOR_vec_cmpuv16qiv16qi = 12545,
  CODE_FOR_vec_cmpuv8hiv8hi = 12546,
  CODE_FOR_vec_cmpuv4siv4si = 12547,
  CODE_FOR_vec_cmpuv2div2di = 12548,
  CODE_FOR_vec_cmpeqv2div2di = 12549,
  CODE_FOR_vec_cmpeqv1tiv1ti = 12550,
  CODE_FOR_vcond_mask_v16sihi = 12551,
  CODE_FOR_vcond_mask_v8siqi = 12552,
  CODE_FOR_vcond_mask_v4siqi = 12553,
  CODE_FOR_vcond_mask_v8diqi = 12554,
  CODE_FOR_vcond_mask_v4diqi = 12555,
  CODE_FOR_vcond_mask_v2diqi = 12556,
  CODE_FOR_vcond_mask_v16sfhi = 12557,
  CODE_FOR_vcond_mask_v8sfqi = 12558,
  CODE_FOR_vcond_mask_v4sfqi = 12559,
  CODE_FOR_vcond_mask_v8dfqi = 12560,
  CODE_FOR_vcond_mask_v4dfqi = 12561,
  CODE_FOR_vcond_mask_v2dfqi = 12562,
  CODE_FOR_vcond_mask_v64qidi = 12563,
  CODE_FOR_vcond_mask_v16qihi = 12564,
  CODE_FOR_vcond_mask_v32qisi = 12565,
  CODE_FOR_vcond_mask_v32hisi = 12566,
  CODE_FOR_vcond_mask_v16hihi = 12567,
  CODE_FOR_vcond_mask_v8hiqi = 12568,
  CODE_FOR_vcond_mask_v32hfsi = 12569,
  CODE_FOR_vcond_mask_v16hfhi = 12570,
  CODE_FOR_vcond_mask_v8hfqi = 12571,
  CODE_FOR_vcond_mask_v32bfsi = 12572,
  CODE_FOR_vcond_mask_v16bfhi = 12573,
  CODE_FOR_vcond_mask_v8bfqi = 12574,
  CODE_FOR_vcond_mask_v32qiv32qi = 12575,
  CODE_FOR_vcond_mask_v16hiv16hi = 12576,
  CODE_FOR_vcond_mask_v8siv8si = 12577,
  CODE_FOR_vcond_mask_v4div4di = 12578,
  CODE_FOR_vcond_mask_v16qiv16qi = 12579,
  CODE_FOR_vcond_mask_v8hiv8hi = 12580,
  CODE_FOR_vcond_mask_v4siv4si = 12581,
  CODE_FOR_vcond_mask_v2div2di = 12582,
  CODE_FOR_vcond_mask_v1tiv1ti = 12583,
  CODE_FOR_vcond_mask_v8sfv8si = 12584,
  CODE_FOR_vcond_mask_v4dfv4di = 12585,
  CODE_FOR_vcond_mask_v4sfv4si = 12586,
  CODE_FOR_vcond_mask_v2dfv2di = 12587,
  CODE_FOR_vcond_mask_qiqi = 12588,
  CODE_FOR_vcond_mask_hihi = 12589,
  CODE_FOR_vcond_mask_sisi = 12590,
  CODE_FOR_vcond_mask_didi = 12591,
  CODE_FOR_andv16bf3 = 12592,
   CODE_FOR_andv16bf3_mask = CODE_FOR_nothing,
  CODE_FOR_iorv16bf3 = 12593,
   CODE_FOR_iorv16bf3_mask = CODE_FOR_nothing,
  CODE_FOR_xorv16bf3 = 12594,
   CODE_FOR_xorv16bf3_mask = CODE_FOR_nothing,
  CODE_FOR_andv8bf3 = 12595,
   CODE_FOR_andv8bf3_mask = CODE_FOR_nothing,
  CODE_FOR_iorv8bf3 = 12596,
   CODE_FOR_iorv8bf3_mask = CODE_FOR_nothing,
  CODE_FOR_xorv8bf3 = 12597,
   CODE_FOR_xorv8bf3_mask = CODE_FOR_nothing,
  CODE_FOR_andv16hf3 = 12598,
   CODE_FOR_andv16hf3_mask = CODE_FOR_nothing,
  CODE_FOR_iorv16hf3 = 12599,
   CODE_FOR_iorv16hf3_mask = CODE_FOR_nothing,
  CODE_FOR_xorv16hf3 = 12600,
   CODE_FOR_xorv16hf3_mask = CODE_FOR_nothing,
  CODE_FOR_andv8hf3 = 12601,
   CODE_FOR_andv8hf3_mask = CODE_FOR_nothing,
  CODE_FOR_iorv8hf3 = 12602,
   CODE_FOR_iorv8hf3_mask = CODE_FOR_nothing,
  CODE_FOR_xorv8hf3 = 12603,
   CODE_FOR_xorv8hf3_mask = CODE_FOR_nothing,
  CODE_FOR_andv8sf3 = 12604,
  CODE_FOR_andv8sf3_mask = 12605,
  CODE_FOR_iorv8sf3 = 12606,
  CODE_FOR_iorv8sf3_mask = 12607,
  CODE_FOR_xorv8sf3 = 12608,
  CODE_FOR_xorv8sf3_mask = 12609,
  CODE_FOR_andv4sf3 = 12610,
  CODE_FOR_andv4sf3_mask = 12611,
  CODE_FOR_iorv4sf3 = 12612,
  CODE_FOR_iorv4sf3_mask = 12613,
  CODE_FOR_xorv4sf3 = 12614,
  CODE_FOR_xorv4sf3_mask = 12615,
  CODE_FOR_andv4df3 = 12616,
  CODE_FOR_andv4df3_mask = 12617,
  CODE_FOR_iorv4df3 = 12618,
  CODE_FOR_iorv4df3_mask = 12619,
  CODE_FOR_xorv4df3 = 12620,
  CODE_FOR_xorv4df3_mask = 12621,
  CODE_FOR_andv2df3 = 12622,
  CODE_FOR_andv2df3_mask = 12623,
  CODE_FOR_iorv2df3 = 12624,
  CODE_FOR_iorv2df3_mask = 12625,
  CODE_FOR_xorv2df3 = 12626,
  CODE_FOR_xorv2df3_mask = 12627,
  CODE_FOR_andv32bf3 = 12628,
   CODE_FOR_andv32bf3_mask = CODE_FOR_nothing,
  CODE_FOR_iorv32bf3 = 12629,
   CODE_FOR_iorv32bf3_mask = CODE_FOR_nothing,
  CODE_FOR_xorv32bf3 = 12630,
   CODE_FOR_xorv32bf3_mask = CODE_FOR_nothing,
  CODE_FOR_andv32hf3 = 12631,
   CODE_FOR_andv32hf3_mask = CODE_FOR_nothing,
  CODE_FOR_iorv32hf3 = 12632,
   CODE_FOR_iorv32hf3_mask = CODE_FOR_nothing,
  CODE_FOR_xorv32hf3 = 12633,
   CODE_FOR_xorv32hf3_mask = CODE_FOR_nothing,
  CODE_FOR_andv16sf3 = 12634,
  CODE_FOR_andv16sf3_mask = 12635,
  CODE_FOR_iorv16sf3 = 12636,
  CODE_FOR_iorv16sf3_mask = 12637,
  CODE_FOR_xorv16sf3 = 12638,
  CODE_FOR_xorv16sf3_mask = 12639,
  CODE_FOR_andv8df3 = 12640,
  CODE_FOR_andv8df3_mask = 12641,
  CODE_FOR_iorv8df3 = 12642,
  CODE_FOR_iorv8df3_mask = 12643,
  CODE_FOR_xorv8df3 = 12644,
  CODE_FOR_xorv8df3_mask = 12645,
  CODE_FOR_copysignv32bf3 = 12646,
  CODE_FOR_copysignv16bf3 = 12647,
  CODE_FOR_copysignv8bf3 = 12648,
  CODE_FOR_copysignv32hf3 = 12649,
  CODE_FOR_copysignv16hf3 = 12650,
  CODE_FOR_copysignv8hf3 = 12651,
  CODE_FOR_copysignv16sf3 = 12652,
  CODE_FOR_copysignv8sf3 = 12653,
  CODE_FOR_copysignv4sf3 = 12654,
  CODE_FOR_copysignv8df3 = 12655,
  CODE_FOR_copysignv4df3 = 12656,
  CODE_FOR_copysignv2df3 = 12657,
  CODE_FOR_xorsignv32bf3 = 12658,
  CODE_FOR_xorsignv16bf3 = 12659,
  CODE_FOR_xorsignv8bf3 = 12660,
  CODE_FOR_xorsignv32hf3 = 12661,
  CODE_FOR_xorsignv16hf3 = 12662,
  CODE_FOR_xorsignv8hf3 = 12663,
  CODE_FOR_xorsignv16sf3 = 12664,
  CODE_FOR_xorsignv8sf3 = 12665,
  CODE_FOR_xorsignv4sf3 = 12666,
  CODE_FOR_xorsignv8df3 = 12667,
  CODE_FOR_xorsignv4df3 = 12668,
  CODE_FOR_xorsignv2df3 = 12669,
  CODE_FOR_signbitv16sf2 = 12670,
  CODE_FOR_signbitv8sf2 = 12671,
  CODE_FOR_signbitv4sf2 = 12672,
  CODE_FOR_andtf3 = 12673,
  CODE_FOR_iortf3 = 12674,
  CODE_FOR_xortf3 = 12675,
  CODE_FOR_fmasf4 = 12676,
  CODE_FOR_fmadf4 = 12677,
  CODE_FOR_fmav4sf4 = 12678,
  CODE_FOR_fmav2df4 = 12679,
  CODE_FOR_fmav8sf4 = 12680,
  CODE_FOR_fmav4df4 = 12681,
  CODE_FOR_fmav16sf4 = 12682,
  CODE_FOR_fmav8df4 = 12683,
  CODE_FOR_fmahf4 = 12684,
  CODE_FOR_fmav8hf4 = 12685,
  CODE_FOR_fmav16hf4 = 12686,
  CODE_FOR_fmav32hf4 = 12687,
  CODE_FOR_fmav8bf4 = 12688,
  CODE_FOR_fmav16bf4 = 12689,
  CODE_FOR_fmav32bf4 = 12690,
  CODE_FOR_fmssf4 = 12691,
  CODE_FOR_fmsdf4 = 12692,
  CODE_FOR_fmsv4sf4 = 12693,
  CODE_FOR_fmsv2df4 = 12694,
  CODE_FOR_fmsv8sf4 = 12695,
  CODE_FOR_fmsv4df4 = 12696,
  CODE_FOR_fmsv16sf4 = 12697,
  CODE_FOR_fmsv8df4 = 12698,
  CODE_FOR_fmshf4 = 12699,
  CODE_FOR_fmsv8hf4 = 12700,
  CODE_FOR_fmsv16hf4 = 12701,
  CODE_FOR_fmsv32hf4 = 12702,
  CODE_FOR_fmsv8bf4 = 12703,
  CODE_FOR_fmsv16bf4 = 12704,
  CODE_FOR_fmsv32bf4 = 12705,
  CODE_FOR_fnmasf4 = 12706,
  CODE_FOR_fnmadf4 = 12707,
  CODE_FOR_fnmav4sf4 = 12708,
  CODE_FOR_fnmav2df4 = 12709,
  CODE_FOR_fnmav8sf4 = 12710,
  CODE_FOR_fnmav4df4 = 12711,
  CODE_FOR_fnmav16sf4 = 12712,
  CODE_FOR_fnmav8df4 = 12713,
  CODE_FOR_fnmahf4 = 12714,
  CODE_FOR_fnmav8hf4 = 12715,
  CODE_FOR_fnmav16hf4 = 12716,
  CODE_FOR_fnmav32hf4 = 12717,
  CODE_FOR_fnmav8bf4 = 12718,
  CODE_FOR_fnmav16bf4 = 12719,
  CODE_FOR_fnmav32bf4 = 12720,
  CODE_FOR_fnmssf4 = 12721,
  CODE_FOR_fnmsdf4 = 12722,
  CODE_FOR_fnmsv4sf4 = 12723,
  CODE_FOR_fnmsv2df4 = 12724,
  CODE_FOR_fnmsv8sf4 = 12725,
  CODE_FOR_fnmsv4df4 = 12726,
  CODE_FOR_fnmsv16sf4 = 12727,
  CODE_FOR_fnmsv8df4 = 12728,
  CODE_FOR_fnmshf4 = 12729,
  CODE_FOR_fnmsv8hf4 = 12730,
  CODE_FOR_fnmsv16hf4 = 12731,
  CODE_FOR_fnmsv32hf4 = 12732,
  CODE_FOR_fnmsv8bf4 = 12733,
  CODE_FOR_fnmsv16bf4 = 12734,
  CODE_FOR_fnmsv32bf4 = 12735,
  CODE_FOR_fma4i_fmadd_sf = 12736,
  CODE_FOR_fma4i_fmadd_df = 12737,
  CODE_FOR_fma4i_fmadd_v4sf = 12738,
  CODE_FOR_fma4i_fmadd_v2df = 12739,
  CODE_FOR_fma4i_fmadd_v8sf = 12740,
  CODE_FOR_fma4i_fmadd_v4df = 12741,
  CODE_FOR_fma4i_fmadd_v16sf = 12742,
  CODE_FOR_fma4i_fmadd_v8df = 12743,
  CODE_FOR_fma4i_fmsub_sf = 12744,
  CODE_FOR_fma4i_fmsub_df = 12745,
  CODE_FOR_fma4i_fmsub_v4sf = 12746,
  CODE_FOR_fma4i_fmsub_v2df = 12747,
  CODE_FOR_fma4i_fmsub_v8sf = 12748,
  CODE_FOR_fma4i_fmsub_v4df = 12749,
  CODE_FOR_fma4i_fmsub_v16sf = 12750,
  CODE_FOR_fma4i_fmsub_v8df = 12751,
  CODE_FOR_fma4i_fnmadd_sf = 12752,
  CODE_FOR_fma4i_fnmadd_df = 12753,
  CODE_FOR_fma4i_fnmadd_v4sf = 12754,
  CODE_FOR_fma4i_fnmadd_v2df = 12755,
  CODE_FOR_fma4i_fnmadd_v8sf = 12756,
  CODE_FOR_fma4i_fnmadd_v4df = 12757,
  CODE_FOR_fma4i_fnmadd_v16sf = 12758,
  CODE_FOR_fma4i_fnmadd_v8df = 12759,
  CODE_FOR_fma4i_fnmsub_sf = 12760,
  CODE_FOR_fma4i_fnmsub_df = 12761,
  CODE_FOR_fma4i_fnmsub_v4sf = 12762,
  CODE_FOR_fma4i_fnmsub_v2df = 12763,
  CODE_FOR_fma4i_fnmsub_v8sf = 12764,
  CODE_FOR_fma4i_fnmsub_v4df = 12765,
  CODE_FOR_fma4i_fnmsub_v16sf = 12766,
  CODE_FOR_fma4i_fnmsub_v8df = 12767,
  CODE_FOR_avx512bw_fmadd_v32hf_maskz = 12768,
  CODE_FOR_avx512bw_fmadd_v32hf_maskz_round = 12769,
  CODE_FOR_avx512vl_fmadd_v16hf_maskz = 12770,
  CODE_FOR_avx512vl_fmadd_v16hf_maskz_round = 12771,
  CODE_FOR_avx512fp16_fmadd_v8hf_maskz = 12772,
  CODE_FOR_avx512fp16_fmadd_v8hf_maskz_round = 12773,
  CODE_FOR_avx512f_fmadd_v16sf_maskz = 12774,
  CODE_FOR_avx512f_fmadd_v16sf_maskz_round = 12775,
  CODE_FOR_avx512vl_fmadd_v8sf_maskz = 12776,
  CODE_FOR_avx512vl_fmadd_v8sf_maskz_round = 12777,
  CODE_FOR_avx512vl_fmadd_v4sf_maskz = 12778,
  CODE_FOR_avx512vl_fmadd_v4sf_maskz_round = 12779,
  CODE_FOR_avx512f_fmadd_v8df_maskz = 12780,
  CODE_FOR_avx512f_fmadd_v8df_maskz_round = 12781,
  CODE_FOR_avx512vl_fmadd_v4df_maskz = 12782,
  CODE_FOR_avx512vl_fmadd_v4df_maskz_round = 12783,
  CODE_FOR_avx512vl_fmadd_v2df_maskz = 12784,
  CODE_FOR_avx512vl_fmadd_v2df_maskz_round = 12785,
  CODE_FOR_cond_fmav32hf = 12786,
  CODE_FOR_cond_fmav16hf = 12787,
  CODE_FOR_cond_fmav8hf = 12788,
  CODE_FOR_cond_fmav16sf = 12789,
  CODE_FOR_cond_fmav8sf = 12790,
  CODE_FOR_cond_fmav4sf = 12791,
  CODE_FOR_cond_fmav8df = 12792,
  CODE_FOR_cond_fmav4df = 12793,
  CODE_FOR_cond_fmav2df = 12794,
  CODE_FOR_avx512bw_fmsub_v32hf_maskz = 12795,
  CODE_FOR_avx512bw_fmsub_v32hf_maskz_round = 12796,
  CODE_FOR_avx512vl_fmsub_v16hf_maskz = 12797,
  CODE_FOR_avx512vl_fmsub_v16hf_maskz_round = 12798,
  CODE_FOR_avx512fp16_fmsub_v8hf_maskz = 12799,
  CODE_FOR_avx512fp16_fmsub_v8hf_maskz_round = 12800,
  CODE_FOR_avx512f_fmsub_v16sf_maskz = 12801,
  CODE_FOR_avx512f_fmsub_v16sf_maskz_round = 12802,
  CODE_FOR_avx512vl_fmsub_v8sf_maskz = 12803,
  CODE_FOR_avx512vl_fmsub_v8sf_maskz_round = 12804,
  CODE_FOR_avx512vl_fmsub_v4sf_maskz = 12805,
  CODE_FOR_avx512vl_fmsub_v4sf_maskz_round = 12806,
  CODE_FOR_avx512f_fmsub_v8df_maskz = 12807,
  CODE_FOR_avx512f_fmsub_v8df_maskz_round = 12808,
  CODE_FOR_avx512vl_fmsub_v4df_maskz = 12809,
  CODE_FOR_avx512vl_fmsub_v4df_maskz_round = 12810,
  CODE_FOR_avx512vl_fmsub_v2df_maskz = 12811,
  CODE_FOR_avx512vl_fmsub_v2df_maskz_round = 12812,
  CODE_FOR_cond_fmsv32hf = 12813,
  CODE_FOR_cond_fmsv16hf = 12814,
  CODE_FOR_cond_fmsv8hf = 12815,
  CODE_FOR_cond_fmsv16sf = 12816,
  CODE_FOR_cond_fmsv8sf = 12817,
  CODE_FOR_cond_fmsv4sf = 12818,
  CODE_FOR_cond_fmsv8df = 12819,
  CODE_FOR_cond_fmsv4df = 12820,
  CODE_FOR_cond_fmsv2df = 12821,
  CODE_FOR_avx512bw_fnmadd_v32hf_maskz = 12822,
  CODE_FOR_avx512bw_fnmadd_v32hf_maskz_round = 12823,
  CODE_FOR_avx512vl_fnmadd_v16hf_maskz = 12824,
  CODE_FOR_avx512vl_fnmadd_v16hf_maskz_round = 12825,
  CODE_FOR_avx512fp16_fnmadd_v8hf_maskz = 12826,
  CODE_FOR_avx512fp16_fnmadd_v8hf_maskz_round = 12827,
  CODE_FOR_avx512f_fnmadd_v16sf_maskz = 12828,
  CODE_FOR_avx512f_fnmadd_v16sf_maskz_round = 12829,
  CODE_FOR_avx512vl_fnmadd_v8sf_maskz = 12830,
  CODE_FOR_avx512vl_fnmadd_v8sf_maskz_round = 12831,
  CODE_FOR_avx512vl_fnmadd_v4sf_maskz = 12832,
  CODE_FOR_avx512vl_fnmadd_v4sf_maskz_round = 12833,
  CODE_FOR_avx512f_fnmadd_v8df_maskz = 12834,
  CODE_FOR_avx512f_fnmadd_v8df_maskz_round = 12835,
  CODE_FOR_avx512vl_fnmadd_v4df_maskz = 12836,
  CODE_FOR_avx512vl_fnmadd_v4df_maskz_round = 12837,
  CODE_FOR_avx512vl_fnmadd_v2df_maskz = 12838,
  CODE_FOR_avx512vl_fnmadd_v2df_maskz_round = 12839,
  CODE_FOR_cond_fnmav32hf = 12840,
  CODE_FOR_cond_fnmav16hf = 12841,
  CODE_FOR_cond_fnmav8hf = 12842,
  CODE_FOR_cond_fnmav16sf = 12843,
  CODE_FOR_cond_fnmav8sf = 12844,
  CODE_FOR_cond_fnmav4sf = 12845,
  CODE_FOR_cond_fnmav8df = 12846,
  CODE_FOR_cond_fnmav4df = 12847,
  CODE_FOR_cond_fnmav2df = 12848,
  CODE_FOR_avx512bw_fnmsub_v32hf_maskz = 12849,
  CODE_FOR_avx512bw_fnmsub_v32hf_maskz_round = 12850,
  CODE_FOR_avx512vl_fnmsub_v16hf_maskz = 12851,
  CODE_FOR_avx512vl_fnmsub_v16hf_maskz_round = 12852,
  CODE_FOR_avx512fp16_fnmsub_v8hf_maskz = 12853,
  CODE_FOR_avx512fp16_fnmsub_v8hf_maskz_round = 12854,
  CODE_FOR_avx512f_fnmsub_v16sf_maskz = 12855,
  CODE_FOR_avx512f_fnmsub_v16sf_maskz_round = 12856,
  CODE_FOR_avx512vl_fnmsub_v8sf_maskz = 12857,
  CODE_FOR_avx512vl_fnmsub_v8sf_maskz_round = 12858,
  CODE_FOR_avx512vl_fnmsub_v4sf_maskz = 12859,
  CODE_FOR_avx512vl_fnmsub_v4sf_maskz_round = 12860,
  CODE_FOR_avx512f_fnmsub_v8df_maskz = 12861,
  CODE_FOR_avx512f_fnmsub_v8df_maskz_round = 12862,
  CODE_FOR_avx512vl_fnmsub_v4df_maskz = 12863,
  CODE_FOR_avx512vl_fnmsub_v4df_maskz_round = 12864,
  CODE_FOR_avx512vl_fnmsub_v2df_maskz = 12865,
  CODE_FOR_avx512vl_fnmsub_v2df_maskz_round = 12866,
  CODE_FOR_cond_fnmsv32hf = 12867,
  CODE_FOR_cond_fnmsv16hf = 12868,
  CODE_FOR_cond_fnmsv8hf = 12869,
  CODE_FOR_cond_fnmsv16sf = 12870,
  CODE_FOR_cond_fnmsv8sf = 12871,
  CODE_FOR_cond_fnmsv4sf = 12872,
  CODE_FOR_cond_fnmsv8df = 12873,
  CODE_FOR_cond_fnmsv4df = 12874,
  CODE_FOR_cond_fnmsv2df = 12875,
  CODE_FOR_vec_fmaddsubv32hf4 = 12876,
  CODE_FOR_vec_fmaddsubv16hf4 = 12877,
  CODE_FOR_vec_fmaddsubv8hf4 = 12878,
  CODE_FOR_vec_fmaddsubv16sf4 = 12879,
  CODE_FOR_vec_fmaddsubv8sf4 = 12880,
  CODE_FOR_vec_fmaddsubv4sf4 = 12881,
  CODE_FOR_vec_fmaddsubv8df4 = 12882,
  CODE_FOR_vec_fmaddsubv4df4 = 12883,
  CODE_FOR_vec_fmaddsubv2df4 = 12884,
  CODE_FOR_vec_fmsubaddv32hf4 = 12885,
  CODE_FOR_vec_fmsubaddv16hf4 = 12886,
  CODE_FOR_vec_fmsubaddv8hf4 = 12887,
  CODE_FOR_vec_fmsubaddv16sf4 = 12888,
  CODE_FOR_vec_fmsubaddv8sf4 = 12889,
  CODE_FOR_vec_fmsubaddv4sf4 = 12890,
  CODE_FOR_vec_fmsubaddv8df4 = 12891,
  CODE_FOR_vec_fmsubaddv4df4 = 12892,
  CODE_FOR_vec_fmsubaddv2df4 = 12893,
  CODE_FOR_fmaddsub_v16sf = 12894,
  CODE_FOR_fmaddsub_v8sf = 12895,
  CODE_FOR_fmaddsub_v4sf = 12896,
  CODE_FOR_fmaddsub_v8df = 12897,
  CODE_FOR_fmaddsub_v4df = 12898,
  CODE_FOR_fmaddsub_v2df = 12899,
  CODE_FOR_avx512bw_fmaddsub_v32hf_maskz = 12900,
  CODE_FOR_avx512bw_fmaddsub_v32hf_maskz_round = 12901,
  CODE_FOR_avx512vl_fmaddsub_v16hf_maskz = 12902,
  CODE_FOR_avx512vl_fmaddsub_v16hf_maskz_round = 12903,
  CODE_FOR_avx512fp16_fmaddsub_v8hf_maskz = 12904,
  CODE_FOR_avx512fp16_fmaddsub_v8hf_maskz_round = 12905,
  CODE_FOR_avx512f_fmaddsub_v16sf_maskz = 12906,
  CODE_FOR_avx512f_fmaddsub_v16sf_maskz_round = 12907,
  CODE_FOR_avx512vl_fmaddsub_v8sf_maskz = 12908,
  CODE_FOR_avx512vl_fmaddsub_v8sf_maskz_round = 12909,
  CODE_FOR_avx512vl_fmaddsub_v4sf_maskz = 12910,
  CODE_FOR_avx512vl_fmaddsub_v4sf_maskz_round = 12911,
  CODE_FOR_avx512f_fmaddsub_v8df_maskz = 12912,
  CODE_FOR_avx512f_fmaddsub_v8df_maskz_round = 12913,
  CODE_FOR_avx512vl_fmaddsub_v4df_maskz = 12914,
  CODE_FOR_avx512vl_fmaddsub_v4df_maskz_round = 12915,
  CODE_FOR_avx512vl_fmaddsub_v2df_maskz = 12916,
  CODE_FOR_avx512vl_fmaddsub_v2df_maskz_round = 12917,
  CODE_FOR_avx512bw_fmsubadd_v32hf_maskz = 12918,
  CODE_FOR_avx512bw_fmsubadd_v32hf_maskz_round = 12919,
  CODE_FOR_avx512vl_fmsubadd_v16hf_maskz = 12920,
  CODE_FOR_avx512vl_fmsubadd_v16hf_maskz_round = 12921,
  CODE_FOR_avx512fp16_fmsubadd_v8hf_maskz = 12922,
  CODE_FOR_avx512fp16_fmsubadd_v8hf_maskz_round = 12923,
  CODE_FOR_avx512f_fmsubadd_v16sf_maskz = 12924,
  CODE_FOR_avx512f_fmsubadd_v16sf_maskz_round = 12925,
  CODE_FOR_avx512vl_fmsubadd_v8sf_maskz = 12926,
  CODE_FOR_avx512vl_fmsubadd_v8sf_maskz_round = 12927,
  CODE_FOR_avx512vl_fmsubadd_v4sf_maskz = 12928,
  CODE_FOR_avx512vl_fmsubadd_v4sf_maskz_round = 12929,
  CODE_FOR_avx512f_fmsubadd_v8df_maskz = 12930,
  CODE_FOR_avx512f_fmsubadd_v8df_maskz_round = 12931,
  CODE_FOR_avx512vl_fmsubadd_v4df_maskz = 12932,
  CODE_FOR_avx512vl_fmsubadd_v4df_maskz_round = 12933,
  CODE_FOR_avx512vl_fmsubadd_v2df_maskz = 12934,
  CODE_FOR_avx512vl_fmsubadd_v2df_maskz_round = 12935,
  CODE_FOR_fmai_vmfmadd_v8hf = 12936,
  CODE_FOR_fmai_vmfmadd_v8hf_round = 12937,
  CODE_FOR_fmai_vmfmadd_v4sf = 12938,
  CODE_FOR_fmai_vmfmadd_v4sf_round = 12939,
  CODE_FOR_fmai_vmfmadd_v2df = 12940,
  CODE_FOR_fmai_vmfmadd_v2df_round = 12941,
  CODE_FOR_fmai_vmfmsub_v8hf = 12942,
  CODE_FOR_fmai_vmfmsub_v8hf_round = 12943,
  CODE_FOR_fmai_vmfmsub_v4sf = 12944,
  CODE_FOR_fmai_vmfmsub_v4sf_round = 12945,
  CODE_FOR_fmai_vmfmsub_v2df = 12946,
  CODE_FOR_fmai_vmfmsub_v2df_round = 12947,
  CODE_FOR_fmai_vmfnmadd_v8hf = 12948,
  CODE_FOR_fmai_vmfnmadd_v8hf_round = 12949,
  CODE_FOR_fmai_vmfnmadd_v4sf = 12950,
  CODE_FOR_fmai_vmfnmadd_v4sf_round = 12951,
  CODE_FOR_fmai_vmfnmadd_v2df = 12952,
  CODE_FOR_fmai_vmfnmadd_v2df_round = 12953,
  CODE_FOR_fmai_vmfnmsub_v8hf = 12954,
  CODE_FOR_fmai_vmfnmsub_v8hf_round = 12955,
  CODE_FOR_fmai_vmfnmsub_v4sf = 12956,
  CODE_FOR_fmai_vmfnmsub_v4sf_round = 12957,
  CODE_FOR_fmai_vmfnmsub_v2df = 12958,
  CODE_FOR_fmai_vmfnmsub_v2df_round = 12959,
  CODE_FOR_avx512f_vmfmadd_v8hf_maskz = 12960,
  CODE_FOR_avx512f_vmfmadd_v8hf_maskz_round = 12961,
  CODE_FOR_avx512f_vmfmadd_v4sf_maskz = 12962,
  CODE_FOR_avx512f_vmfmadd_v4sf_maskz_round = 12963,
  CODE_FOR_avx512f_vmfmadd_v2df_maskz = 12964,
  CODE_FOR_avx512f_vmfmadd_v2df_maskz_round = 12965,
  CODE_FOR_avx512f_vmfnmadd_v8hf_maskz = 12966,
  CODE_FOR_avx512f_vmfnmadd_v8hf_maskz_round = 12967,
  CODE_FOR_avx512f_vmfnmadd_v4sf_maskz = 12968,
  CODE_FOR_avx512f_vmfnmadd_v4sf_maskz_round = 12969,
  CODE_FOR_avx512f_vmfnmadd_v2df_maskz = 12970,
  CODE_FOR_avx512f_vmfnmadd_v2df_maskz_round = 12971,
  CODE_FOR_fma4i_vmfmadd_v4sf = 12972,
  CODE_FOR_fma4i_vmfmadd_v2df = 12973,
  CODE_FOR_avx512bw_fmaddc_v32hf_mask1 = 12974,
  CODE_FOR_avx512bw_fmaddc_v32hf_mask1_round = 12975,
  CODE_FOR_avx512vl_fmaddc_v16hf_mask1 = 12976,
  CODE_FOR_avx512vl_fmaddc_v16hf_mask1_round = 12977,
  CODE_FOR_avx512fp16_fmaddc_v8hf_mask1 = 12978,
  CODE_FOR_avx512fp16_fmaddc_v8hf_mask1_round = 12979,
  CODE_FOR_avx512bw_fmaddc_v32hf_maskz = 12980,
  CODE_FOR_avx512bw_fmaddc_v32hf_maskz_round = 12981,
  CODE_FOR_avx512vl_fmaddc_v16hf_maskz = 12982,
  CODE_FOR_avx512vl_fmaddc_v16hf_maskz_round = 12983,
  CODE_FOR_avx512fp16_fmaddc_v8hf_maskz = 12984,
  CODE_FOR_avx512fp16_fmaddc_v8hf_maskz_round = 12985,
  CODE_FOR_avx512bw_fcmaddc_v32hf_mask1 = 12986,
  CODE_FOR_avx512bw_fcmaddc_v32hf_mask1_round = 12987,
  CODE_FOR_avx512vl_fcmaddc_v16hf_mask1 = 12988,
  CODE_FOR_avx512vl_fcmaddc_v16hf_mask1_round = 12989,
  CODE_FOR_avx512fp16_fcmaddc_v8hf_mask1 = 12990,
  CODE_FOR_avx512fp16_fcmaddc_v8hf_mask1_round = 12991,
  CODE_FOR_avx512bw_fcmaddc_v32hf_maskz = 12992,
  CODE_FOR_avx512bw_fcmaddc_v32hf_maskz_round = 12993,
  CODE_FOR_avx512vl_fcmaddc_v16hf_maskz = 12994,
  CODE_FOR_avx512vl_fcmaddc_v16hf_maskz_round = 12995,
  CODE_FOR_avx512fp16_fcmaddc_v8hf_maskz = 12996,
  CODE_FOR_avx512fp16_fcmaddc_v8hf_maskz_round = 12997,
  CODE_FOR_cmlav32hf4 = 12998,
  CODE_FOR_cmla_conjv32hf4 = 12999,
  CODE_FOR_cmlav16hf4 = 13000,
  CODE_FOR_cmla_conjv16hf4 = 13001,
  CODE_FOR_cmlav8hf4 = 13002,
  CODE_FOR_cmla_conjv8hf4 = 13003,
  CODE_FOR_cmulv32hf3 = 13004,
  CODE_FOR_cmul_conjv32hf3 = 13005,
  CODE_FOR_cmulv16hf3 = 13006,
  CODE_FOR_cmul_conjv16hf3 = 13007,
  CODE_FOR_cmulv8hf3 = 13008,
  CODE_FOR_cmul_conjv8hf3 = 13009,
  CODE_FOR_avx512fp16_fmaddcsh_v8hf_maskz = 13010,
  CODE_FOR_avx512fp16_fmaddcsh_v8hf_maskz_round = 13011,
  CODE_FOR_avx512fp16_fmaddcsh_v8hf_mask1 = 13012,
  CODE_FOR_avx512fp16_fmaddcsh_v8hf_mask1_round = 13013,
  CODE_FOR_avx512fp16_fcmaddcsh_v8hf_maskz = 13014,
  CODE_FOR_avx512fp16_fcmaddcsh_v8hf_maskz_round = 13015,
  CODE_FOR_avx512fp16_fcmaddcsh_v8hf_mask1 = 13016,
  CODE_FOR_avx512fp16_fcmaddcsh_v8hf_mask1_round = 13017,
  CODE_FOR_avx512fp16_fcmaddcsh_v8hf_mask3 = 13018,
  CODE_FOR_avx512fp16_fcmaddcsh_v8hf_mask3_round = 13019,
  CODE_FOR_avx512fp16_fmaddcsh_v8hf_mask3 = 13020,
  CODE_FOR_avx512fp16_fmaddcsh_v8hf_mask3_round = 13021,
  CODE_FOR_vec_unpacks_lo_v32hf = 13022,
  CODE_FOR_vec_unpacks_lo_v16hf = 13023,
  CODE_FOR_vec_unpacks_lo_v8hf = 13024,
  CODE_FOR_vec_unpacks_hi_v32hf = 13025,
  CODE_FOR_vec_unpacks_hi_v16hf = 13026,
  CODE_FOR_vec_unpacks_hi_v8hf = 13027,
  CODE_FOR_lrintv32hfv32hi2 = 13028,
  CODE_FOR_lrintv16hfv16hi2 = 13029,
  CODE_FOR_lrintv8hfv8hi2 = 13030,
  CODE_FOR_floatv8hiv8hf2 = 13031,
  CODE_FOR_floatunsv8hiv8hf2 = 13032,
  CODE_FOR_floatv16hiv16hf2 = 13033,
  CODE_FOR_floatunsv16hiv16hf2 = 13034,
  CODE_FOR_floatv32hiv32hf2 = 13035,
  CODE_FOR_floatunsv32hiv32hf2 = 13036,
  CODE_FOR_floatv8siv8hf2 = 13037,
  CODE_FOR_floatunsv8siv8hf2 = 13038,
  CODE_FOR_floatv16siv16hf2 = 13039,
  CODE_FOR_floatunsv16siv16hf2 = 13040,
  CODE_FOR_floatv8div8hf2 = 13041,
  CODE_FOR_floatunsv8div8hf2 = 13042,
  CODE_FOR_floatv4siv4hf2 = 13043,
  CODE_FOR_floatunsv4siv4hf2 = 13044,
  CODE_FOR_floatv4div4hf2 = 13045,
  CODE_FOR_floatunsv4div4hf2 = 13046,
  CODE_FOR_avx512fp16_floatv4siv4hf2 = 13047,
  CODE_FOR_avx512fp16_floatunsv4siv4hf2 = 13048,
  CODE_FOR_avx512fp16_floatv4div4hf2 = 13049,
  CODE_FOR_avx512fp16_floatunsv4div4hf2 = 13050,
  CODE_FOR_avx512fp16_vcvtdq2ph_v4si_mask = 13051,
  CODE_FOR_avx512fp16_vcvtudq2ph_v4si_mask = 13052,
  CODE_FOR_avx512fp16_vcvtqq2ph_v4di_mask = 13053,
  CODE_FOR_avx512fp16_vcvtuqq2ph_v4di_mask = 13054,
  CODE_FOR_floatv2div2hf2 = 13055,
  CODE_FOR_floatunsv2div2hf2 = 13056,
  CODE_FOR_avx512fp16_floatv2div2hf2 = 13057,
  CODE_FOR_avx512fp16_floatunsv2div2hf2 = 13058,
  CODE_FOR_avx512fp16_vcvtqq2ph_v2di_mask = 13059,
  CODE_FOR_avx512fp16_vcvtuqq2ph_v2di_mask = 13060,
  CODE_FOR_fix_truncv8hfv8hi2 = 13061,
  CODE_FOR_fixuns_truncv8hfv8hi2 = 13062,
  CODE_FOR_fix_truncv16hfv16hi2 = 13063,
  CODE_FOR_fixuns_truncv16hfv16hi2 = 13064,
  CODE_FOR_fix_truncv32hfv32hi2 = 13065,
  CODE_FOR_fixuns_truncv32hfv32hi2 = 13066,
  CODE_FOR_fix_truncv8hfv8si2 = 13067,
  CODE_FOR_fixuns_truncv8hfv8si2 = 13068,
  CODE_FOR_fix_truncv16hfv16si2 = 13069,
  CODE_FOR_fixuns_truncv16hfv16si2 = 13070,
  CODE_FOR_fix_truncv8hfv8di2 = 13071,
  CODE_FOR_fixuns_truncv8hfv8di2 = 13072,
  CODE_FOR_fix_truncv4hfv4si2 = 13073,
  CODE_FOR_fixuns_truncv4hfv4si2 = 13074,
  CODE_FOR_fix_truncv4hfv4di2 = 13075,
  CODE_FOR_fixuns_truncv4hfv4di2 = 13076,
  CODE_FOR_fix_truncv2hfv2di2 = 13077,
  CODE_FOR_fixuns_truncv2hfv2di2 = 13078,
  CODE_FOR_extendv8hfv8df2 = 13079,
  CODE_FOR_extendv16hfv16sf2 = 13080,
  CODE_FOR_extendv8hfv8sf2 = 13081,
  CODE_FOR_extendv4hfv4df2 = 13082,
  CODE_FOR_extendv4hfv4sf2 = 13083,
  CODE_FOR_extendv2hfv2df2 = 13084,
  CODE_FOR_truncv8dfv8hf2 = 13085,
  CODE_FOR_truncv16sfv16hf2 = 13086,
  CODE_FOR_truncv8sfv8hf2 = 13087,
  CODE_FOR_truncv4dfv4hf2 = 13088,
  CODE_FOR_truncv4sfv4hf2 = 13089,
  CODE_FOR_avx512fp16_truncv4dfv4hf2 = 13090,
  CODE_FOR_avx512fp16_truncv4sfv4hf2 = 13091,
  CODE_FOR_avx512fp16_vcvtpd2ph_v4df_mask = 13092,
  CODE_FOR_avx512fp16_vcvtps2ph_v4sf_mask = 13093,
  CODE_FOR_truncv2dfv2hf2 = 13094,
  CODE_FOR_avx512fp16_truncv2dfv2hf2 = 13095,
  CODE_FOR_avx512fp16_vcvtpd2ph_v2df_mask = 13096,
  CODE_FOR_floatunsv16siv16sf2 = 13097,
  CODE_FOR_floatunsv8siv8sf2 = 13098,
  CODE_FOR_floatunsv4siv4sf2 = 13099,
  CODE_FOR_fixuns_truncv8sfv8si2 = 13100,
  CODE_FOR_fixuns_truncv4sfv4si2 = 13101,
  CODE_FOR_floatv2siv2df2 = 13102,
  CODE_FOR_fix_truncv2dfv2si2 = 13103,
  CODE_FOR_avx512dq_floatv2div2sf2 = 13104,
  CODE_FOR_avx512dq_floatunsv2div2sf2 = 13105,
  CODE_FOR_floatv2div2sf2 = 13106,
  CODE_FOR_floatunsv2div2sf2 = 13107,
  CODE_FOR_vec_packs_float_v8di = 13108,
  CODE_FOR_vec_packu_float_v8di = 13109,
  CODE_FOR_vec_packs_float_v4di = 13110,
  CODE_FOR_vec_packu_float_v4di = 13111,
  CODE_FOR_vec_packs_float_v2di = 13112,
  CODE_FOR_vec_packu_float_v2di = 13113,
  CODE_FOR_vec_packs_float_v16si = 13114,
  CODE_FOR_vec_packu_float_v16si = 13115,
  CODE_FOR_vec_packs_float_v8si = 13116,
  CODE_FOR_vec_packu_float_v8si = 13117,
  CODE_FOR_vec_packs_float_v4si = 13118,
  CODE_FOR_vec_packu_float_v4si = 13119,
  CODE_FOR_floatv2div2sf2_mask = 13120,
  CODE_FOR_floatunsv2div2sf2_mask = 13121,
  CODE_FOR_avx_cvtpd2dq256_2 = 13122,
  CODE_FOR_fix_truncv2sfv2di2 = 13123,
  CODE_FOR_fixuns_truncv2sfv2di2 = 13124,
  CODE_FOR_vec_unpack_sfix_trunc_lo_v16sf = 13125,
  CODE_FOR_vec_unpack_ufix_trunc_lo_v16sf = 13126,
  CODE_FOR_vec_unpack_sfix_trunc_lo_v8sf = 13127,
  CODE_FOR_vec_unpack_ufix_trunc_lo_v8sf = 13128,
  CODE_FOR_vec_unpack_sfix_trunc_lo_v4sf = 13129,
  CODE_FOR_vec_unpack_ufix_trunc_lo_v4sf = 13130,
  CODE_FOR_vec_unpack_sfix_trunc_hi_v16sf = 13131,
  CODE_FOR_vec_unpack_ufix_trunc_hi_v16sf = 13132,
  CODE_FOR_vec_unpack_sfix_trunc_hi_v8sf = 13133,
  CODE_FOR_vec_unpack_ufix_trunc_hi_v8sf = 13134,
  CODE_FOR_vec_unpack_sfix_trunc_hi_v4sf = 13135,
  CODE_FOR_vec_unpack_ufix_trunc_hi_v4sf = 13136,
  CODE_FOR_vec_unpack_sfix_trunc_lo_v32hf = 13137,
  CODE_FOR_vec_unpack_ufix_trunc_lo_v32hf = 13138,
  CODE_FOR_vec_unpack_sfix_trunc_lo_v16hf = 13139,
  CODE_FOR_vec_unpack_ufix_trunc_lo_v16hf = 13140,
  CODE_FOR_vec_unpack_sfix_trunc_lo_v8hf = 13141,
  CODE_FOR_vec_unpack_ufix_trunc_lo_v8hf = 13142,
  CODE_FOR_vec_unpack_sfix_trunc_hi_v32hf = 13143,
  CODE_FOR_vec_unpack_ufix_trunc_hi_v32hf = 13144,
  CODE_FOR_vec_unpack_sfix_trunc_hi_v16hf = 13145,
  CODE_FOR_vec_unpack_ufix_trunc_hi_v16hf = 13146,
  CODE_FOR_vec_unpack_sfix_trunc_hi_v8hf = 13147,
  CODE_FOR_vec_unpack_ufix_trunc_hi_v8hf = 13148,
  CODE_FOR_avx_cvttpd2dq256_2 = 13149,
  CODE_FOR_sse2_cvtpd2ps = 13150,
  CODE_FOR_sse2_cvtpd2ps_mask = 13151,
  CODE_FOR_truncv8dfv8sf2 = 13152,
  CODE_FOR_truncv4dfv4sf2 = 13153,
  CODE_FOR_extendv8sfv8df2 = 13154,
  CODE_FOR_extendv4sfv4df2 = 13155,
  CODE_FOR_avx512bw_cvtmask2bv64qi = 13156,
  CODE_FOR_avx512vl_cvtmask2bv16qi = 13157,
  CODE_FOR_avx512vl_cvtmask2bv32qi = 13158,
  CODE_FOR_avx512bw_cvtmask2wv32hi = 13159,
  CODE_FOR_avx512vl_cvtmask2wv16hi = 13160,
  CODE_FOR_avx512vl_cvtmask2wv8hi = 13161,
  CODE_FOR_avx512f_cvtmask2dv16si = 13162,
  CODE_FOR_avx512vl_cvtmask2dv8si = 13163,
  CODE_FOR_avx512vl_cvtmask2dv4si = 13164,
  CODE_FOR_avx512f_cvtmask2qv8di = 13165,
  CODE_FOR_avx512vl_cvtmask2qv4di = 13166,
  CODE_FOR_avx512vl_cvtmask2qv2di = 13167,
  CODE_FOR_extendv2sfv2df2 = 13168,
  CODE_FOR_vec_unpacks_hi_v4sf = 13169,
  CODE_FOR_vec_unpacks_hi_v8sf = 13170,
  CODE_FOR_vec_unpacks_hi_v16sf = 13171,
  CODE_FOR_vec_unpacks_lo_v4sf = 13172,
  CODE_FOR_vec_unpacks_lo_v8sf = 13173,
  CODE_FOR_vec_unpacks_float_hi_v32hi = 13174,
  CODE_FOR_vec_unpacks_float_hi_v16hi = 13175,
  CODE_FOR_vec_unpacks_float_hi_v8hi = 13176,
  CODE_FOR_vec_unpacks_float_lo_v32hi = 13177,
  CODE_FOR_vec_unpacks_float_lo_v16hi = 13178,
  CODE_FOR_vec_unpacks_float_lo_v8hi = 13179,
  CODE_FOR_vec_unpacku_float_hi_v32hi = 13180,
  CODE_FOR_vec_unpacku_float_hi_v16hi = 13181,
  CODE_FOR_vec_unpacku_float_hi_v8hi = 13182,
  CODE_FOR_vec_unpacku_float_lo_v32hi = 13183,
  CODE_FOR_vec_unpacku_float_lo_v16hi = 13184,
  CODE_FOR_vec_unpacku_float_lo_v8hi = 13185,
  CODE_FOR_vec_unpacks_float_hi_v4si = 13186,
  CODE_FOR_vec_unpacks_float_lo_v4si = 13187,
  CODE_FOR_vec_unpacks_float_hi_v8si = 13188,
  CODE_FOR_vec_unpacks_float_lo_v8si = 13189,
  CODE_FOR_vec_unpacks_float_hi_v16si = 13190,
  CODE_FOR_vec_unpacks_float_lo_v16si = 13191,
  CODE_FOR_vec_unpacku_float_hi_v4si = 13192,
  CODE_FOR_vec_unpacku_float_lo_v4si = 13193,
  CODE_FOR_vec_unpacku_float_hi_v8si = 13194,
  CODE_FOR_vec_unpacku_float_hi_v16si = 13195,
  CODE_FOR_vec_unpacku_float_lo_v8si = 13196,
  CODE_FOR_vec_unpacku_float_lo_v16si = 13197,
  CODE_FOR_vec_pack_trunc_v8df = 13198,
  CODE_FOR_vec_pack_trunc_v4df = 13199,
  CODE_FOR_vec_pack_trunc_v16sf = 13200,
  CODE_FOR_vec_pack_trunc_v8sf = 13201,
  CODE_FOR_vec_pack_trunc_v4sf = 13202,
  CODE_FOR_vec_pack_trunc_v2df = 13203,
  CODE_FOR_vec_pack_sfix_trunc_v8df = 13204,
  CODE_FOR_vec_pack_sfix_trunc_v4df = 13205,
  CODE_FOR_vec_pack_sfix_trunc_v2df = 13206,
  CODE_FOR_vec_pack_ufix_trunc_v8df = 13207,
  CODE_FOR_vec_pack_ufix_trunc_v4df = 13208,
  CODE_FOR_vec_pack_ufix_trunc_v2df = 13209,
  CODE_FOR_avx512f_vec_pack_sfix_v8df = 13210,
  CODE_FOR_vec_pack_sfix_v4df = 13211,
  CODE_FOR_vec_pack_sfix_v2df = 13212,
  CODE_FOR_sse_movhlps_exp = 13213,
  CODE_FOR_sse_movlhps_exp = 13214,
  CODE_FOR_vec_interleave_highv8sf = 13215,
  CODE_FOR_vec_interleave_lowv8sf = 13216,
  CODE_FOR_avx_shufps256 = 13217,
  CODE_FOR_avx_shufps256_mask = 13218,
  CODE_FOR_sse_shufps = 13219,
  CODE_FOR_sse_shufps_mask = 13220,
  CODE_FOR_sse_loadhps_exp = 13221,
  CODE_FOR_sse_loadlps_exp = 13222,
  CODE_FOR_vec_setv16qi = 13223,
  CODE_FOR_vec_setv8hi = 13224,
  CODE_FOR_vec_setv8hf = 13225,
  CODE_FOR_vec_setv8bf = 13226,
  CODE_FOR_vec_setv4si = 13227,
  CODE_FOR_vec_setv2di = 13228,
  CODE_FOR_vec_setv4sf = 13229,
  CODE_FOR_vec_setv2df = 13230,
  CODE_FOR_vec_setv32qi = 13231,
  CODE_FOR_vec_setv16hi = 13232,
  CODE_FOR_vec_setv16hf = 13233,
  CODE_FOR_vec_setv16bf = 13234,
  CODE_FOR_vec_setv8si = 13235,
  CODE_FOR_vec_setv4di = 13236,
  CODE_FOR_vec_setv8sf = 13237,
  CODE_FOR_vec_setv4df = 13238,
  CODE_FOR_vec_setv64qi = 13239,
  CODE_FOR_vec_setv32hi = 13240,
  CODE_FOR_vec_setv32hf = 13241,
  CODE_FOR_vec_setv32bf = 13242,
  CODE_FOR_vec_setv16si = 13243,
  CODE_FOR_vec_setv8di = 13244,
  CODE_FOR_vec_setv16sf = 13245,
  CODE_FOR_vec_setv8df = 13246,
  CODE_FOR_avx512dq_vextractf64x2_mask = 13247,
  CODE_FOR_avx512dq_vextracti64x2_mask = 13248,
  CODE_FOR_avx512f_vextractf32x4_mask = 13249,
  CODE_FOR_avx512f_vextracti32x4_mask = 13250,
  CODE_FOR_avx512dq_vextractf32x8_mask = 13251,
  CODE_FOR_avx512dq_vextracti32x8_mask = 13252,
  CODE_FOR_avx512f_vextractf64x4_mask = 13253,
  CODE_FOR_avx512f_vextracti64x4_mask = 13254,
  CODE_FOR_avx512vl_vextractf128v8si = 13255,
  CODE_FOR_avx512vl_vextractf128v8sf = 13256,
  CODE_FOR_avx512vl_vextractf128v4di = 13257,
  CODE_FOR_avx512vl_vextractf128v4df = 13258,
  CODE_FOR_avx_vextractf128v32qi = 13259,
  CODE_FOR_avx_vextractf128v16hi = 13260,
  CODE_FOR_avx_vextractf128v8si = 13261,
  CODE_FOR_avx_vextractf128v4di = 13262,
  CODE_FOR_avx_vextractf128v8sf = 13263,
  CODE_FOR_avx_vextractf128v4df = 13264,
  CODE_FOR_avx_vextractf128v16hf = 13265,
  CODE_FOR_avx_vextractf128v16bf = 13266,
  CODE_FOR_vec_extractv64qiqi = 13267,
  CODE_FOR_vec_extractv32qiqi = 13268,
  CODE_FOR_vec_extractv16qiqi = 13269,
  CODE_FOR_vec_extractv32hihi = 13270,
  CODE_FOR_vec_extractv16hihi = 13271,
  CODE_FOR_vec_extractv8hihi = 13272,
  CODE_FOR_vec_extractv16sisi = 13273,
  CODE_FOR_vec_extractv8sisi = 13274,
  CODE_FOR_vec_extractv4sisi = 13275,
  CODE_FOR_vec_extractv8didi = 13276,
  CODE_FOR_vec_extractv4didi = 13277,
  CODE_FOR_vec_extractv2didi = 13278,
  CODE_FOR_vec_extractv32hfhf = 13279,
  CODE_FOR_vec_extractv16hfhf = 13280,
  CODE_FOR_vec_extractv8hfhf = 13281,
  CODE_FOR_vec_extractv32bfbf = 13282,
  CODE_FOR_vec_extractv16bfbf = 13283,
  CODE_FOR_vec_extractv8bfbf = 13284,
  CODE_FOR_vec_extractv16sfsf = 13285,
  CODE_FOR_vec_extractv8sfsf = 13286,
  CODE_FOR_vec_extractv4sfsf = 13287,
  CODE_FOR_vec_extractv8dfdf = 13288,
  CODE_FOR_vec_extractv4dfdf = 13289,
  CODE_FOR_vec_extractv2dfdf = 13290,
  CODE_FOR_vec_extractv4titi = 13291,
  CODE_FOR_vec_extractv2titi = 13292,
  CODE_FOR_vec_extractv32qiv16qi = 13293,
  CODE_FOR_vec_extractv16hiv8hi = 13294,
  CODE_FOR_vec_extractv16hfv8hf = 13295,
  CODE_FOR_vec_extractv16bfv8bf = 13296,
  CODE_FOR_vec_extractv8siv4si = 13297,
  CODE_FOR_vec_extractv4div2di = 13298,
  CODE_FOR_vec_extractv8sfv4sf = 13299,
  CODE_FOR_vec_extractv4dfv2df = 13300,
  CODE_FOR_vec_extractv64qiv32qi = 13301,
  CODE_FOR_vec_extractv32hiv16hi = 13302,
  CODE_FOR_vec_extractv32hfv16hf = 13303,
  CODE_FOR_vec_extractv32bfv16bf = 13304,
  CODE_FOR_vec_extractv16siv8si = 13305,
  CODE_FOR_vec_extractv8div4di = 13306,
  CODE_FOR_vec_extractv16sfv8sf = 13307,
  CODE_FOR_vec_extractv8dfv4df = 13308,
  CODE_FOR_vec_interleave_highv4df = 13309,
  CODE_FOR_vec_interleave_highv2df = 13310,
  CODE_FOR_vec_interleave_lowv4df = 13311,
  CODE_FOR_vec_interleave_lowv2df = 13312,
  CODE_FOR_avx512f_vternlogv16si_maskz = 13313,
  CODE_FOR_avx512vl_vternlogv8si_maskz = 13314,
  CODE_FOR_avx512vl_vternlogv4si_maskz = 13315,
  CODE_FOR_avx512f_vternlogv8di_maskz = 13316,
  CODE_FOR_avx512vl_vternlogv4di_maskz = 13317,
  CODE_FOR_avx512vl_vternlogv2di_maskz = 13318,
  CODE_FOR_avx512f_vternlogv16si_mask = 13319,
  CODE_FOR_avx512vl_vternlogv8si_mask = 13320,
  CODE_FOR_avx512vl_vternlogv4si_mask = 13321,
  CODE_FOR_avx512f_vternlogv8di_mask = 13322,
  CODE_FOR_avx512vl_vternlogv4di_mask = 13323,
  CODE_FOR_avx512vl_vternlogv2di_mask = 13324,
  CODE_FOR_avx512f_shufps512_mask = 13325,
  CODE_FOR_avx512f_fixupimmv16sf_maskz = 13326,
  CODE_FOR_avx512f_fixupimmv16sf_maskz_round = 13327,
  CODE_FOR_avx512vl_fixupimmv8sf_maskz = 13328,
  CODE_FOR_avx512vl_fixupimmv8sf_maskz_round = 13329,
  CODE_FOR_avx512vl_fixupimmv4sf_maskz = 13330,
  CODE_FOR_avx512vl_fixupimmv4sf_maskz_round = 13331,
  CODE_FOR_avx512f_fixupimmv8df_maskz = 13332,
  CODE_FOR_avx512f_fixupimmv8df_maskz_round = 13333,
  CODE_FOR_avx512vl_fixupimmv4df_maskz = 13334,
  CODE_FOR_avx512vl_fixupimmv4df_maskz_round = 13335,
  CODE_FOR_avx512vl_fixupimmv2df_maskz = 13336,
  CODE_FOR_avx512vl_fixupimmv2df_maskz_round = 13337,
  CODE_FOR_avx512f_sfixupimmv4sf_maskz = 13338,
  CODE_FOR_avx512f_sfixupimmv4sf_maskz_round = 13339,
  CODE_FOR_avx512f_sfixupimmv2df_maskz = 13340,
  CODE_FOR_avx512f_sfixupimmv2df_maskz_round = 13341,
  CODE_FOR_avx512f_shufpd512_mask = 13342,
  CODE_FOR_avx_shufpd256 = 13343,
  CODE_FOR_avx_shufpd256_mask = 13344,
  CODE_FOR_sse2_shufpd = 13345,
  CODE_FOR_sse2_shufpd_mask = 13346,
  CODE_FOR_sse2_loadhpd_exp = 13347,
  CODE_FOR_sse2_loadlpd_exp = 13348,
  CODE_FOR_truncv16siv16qi2 = 13349,
  CODE_FOR_truncv16siv16hi2 = 13350,
  CODE_FOR_truncv8div8si2 = 13351,
  CODE_FOR_truncv8div8hi2 = 13352,
  CODE_FOR_avx512f_ss_truncatev16siv16qi2_mask_store = 13353,
  CODE_FOR_avx512f_truncatev16siv16qi2_mask_store = 13354,
  CODE_FOR_avx512f_us_truncatev16siv16qi2_mask_store = 13355,
  CODE_FOR_avx512f_ss_truncatev16siv16hi2_mask_store = 13356,
  CODE_FOR_avx512f_truncatev16siv16hi2_mask_store = 13357,
  CODE_FOR_avx512f_us_truncatev16siv16hi2_mask_store = 13358,
  CODE_FOR_avx512f_ss_truncatev8div8si2_mask_store = 13359,
  CODE_FOR_avx512f_truncatev8div8si2_mask_store = 13360,
  CODE_FOR_avx512f_us_truncatev8div8si2_mask_store = 13361,
  CODE_FOR_avx512f_ss_truncatev8div8hi2_mask_store = 13362,
  CODE_FOR_avx512f_truncatev8div8hi2_mask_store = 13363,
  CODE_FOR_avx512f_us_truncatev8div8hi2_mask_store = 13364,
  CODE_FOR_truncv32hiv32qi2 = 13365,
  CODE_FOR_avx512bw_ss_truncatev32hiv32qi2_mask_store = 13366,
  CODE_FOR_avx512bw_truncatev32hiv32qi2_mask_store = 13367,
  CODE_FOR_avx512bw_us_truncatev32hiv32qi2_mask_store = 13368,
  CODE_FOR_truncv4div4si2 = 13369,
  CODE_FOR_truncv8siv8hi2 = 13370,
  CODE_FOR_truncv16hiv16qi2 = 13371,
  CODE_FOR_avx512vl_ss_truncatev4div4si2_mask_store = 13372,
  CODE_FOR_avx512vl_truncatev4div4si2_mask_store = 13373,
  CODE_FOR_avx512vl_us_truncatev4div4si2_mask_store = 13374,
  CODE_FOR_avx512vl_ss_truncatev8siv8hi2_mask_store = 13375,
  CODE_FOR_avx512vl_truncatev8siv8hi2_mask_store = 13376,
  CODE_FOR_avx512vl_us_truncatev8siv8hi2_mask_store = 13377,
  CODE_FOR_avx512vl_ss_truncatev16hiv16qi2_mask_store = 13378,
  CODE_FOR_avx512vl_truncatev16hiv16qi2_mask_store = 13379,
  CODE_FOR_avx512vl_us_truncatev16hiv16qi2_mask_store = 13380,
  CODE_FOR_truncv4div4qi2 = 13381,
  CODE_FOR_truncv2div2qi2 = 13382,
  CODE_FOR_truncv8siv8qi2 = 13383,
  CODE_FOR_truncv4siv4qi2 = 13384,
  CODE_FOR_truncv8hiv8qi2 = 13385,
  CODE_FOR_avx512vl_ss_truncatev2div2qi2_mask_store_2 = 13386,
  CODE_FOR_avx512vl_truncatev2div2qi2_mask_store_2 = 13387,
  CODE_FOR_avx512vl_us_truncatev2div2qi2_mask_store_2 = 13388,
  CODE_FOR_avx512vl_ss_truncatev4siv4qi2_mask_store_2 = 13389,
  CODE_FOR_avx512vl_truncatev4siv4qi2_mask_store_2 = 13390,
  CODE_FOR_avx512vl_us_truncatev4siv4qi2_mask_store_2 = 13391,
  CODE_FOR_avx512vl_ss_truncatev4div4qi2_mask_store_2 = 13392,
  CODE_FOR_avx512vl_truncatev4div4qi2_mask_store_2 = 13393,
  CODE_FOR_avx512vl_us_truncatev4div4qi2_mask_store_2 = 13394,
  CODE_FOR_avx512vl_ss_truncatev8hiv8qi2_mask_store_2 = 13395,
  CODE_FOR_avx512vl_truncatev8hiv8qi2_mask_store_2 = 13396,
  CODE_FOR_avx512vl_us_truncatev8hiv8qi2_mask_store_2 = 13397,
  CODE_FOR_avx512vl_ss_truncatev8siv8qi2_mask_store_2 = 13398,
  CODE_FOR_avx512vl_truncatev8siv8qi2_mask_store_2 = 13399,
  CODE_FOR_avx512vl_us_truncatev8siv8qi2_mask_store_2 = 13400,
  CODE_FOR_truncv4div4hi2 = 13401,
  CODE_FOR_truncv2div2hi2 = 13402,
  CODE_FOR_truncv4siv4hi2 = 13403,
  CODE_FOR_avx512vl_ss_truncatev4siv4hi2_mask_store_2 = 13404,
  CODE_FOR_avx512vl_truncatev4siv4hi2_mask_store_2 = 13405,
  CODE_FOR_avx512vl_us_truncatev4siv4hi2_mask_store_2 = 13406,
  CODE_FOR_avx512vl_ss_truncatev4div4hi2_mask_store_2 = 13407,
  CODE_FOR_avx512vl_truncatev4div4hi2_mask_store_2 = 13408,
  CODE_FOR_avx512vl_us_truncatev4div4hi2_mask_store_2 = 13409,
  CODE_FOR_avx512vl_ss_truncatev2div2hi2_mask_store_2 = 13410,
  CODE_FOR_avx512vl_truncatev2div2hi2_mask_store_2 = 13411,
  CODE_FOR_avx512vl_us_truncatev2div2hi2_mask_store_2 = 13412,
  CODE_FOR_truncv2div2si2 = 13413,
  CODE_FOR_avx512vl_ss_truncatev2div2si2_mask_store_2 = 13414,
  CODE_FOR_avx512vl_truncatev2div2si2_mask_store_2 = 13415,
  CODE_FOR_avx512vl_us_truncatev2div2si2_mask_store_2 = 13416,
  CODE_FOR_truncv8div8qi2 = 13417,
  CODE_FOR_avx512f_ss_truncatev8div16qi2_mask_store_2 = 13418,
  CODE_FOR_avx512f_truncatev8div16qi2_mask_store_2 = 13419,
  CODE_FOR_avx512f_us_truncatev8div16qi2_mask_store_2 = 13420,
  CODE_FOR_negv64qi2 = 13421,
  CODE_FOR_negv32qi2 = 13422,
  CODE_FOR_negv16qi2 = 13423,
  CODE_FOR_negv32hi2 = 13424,
  CODE_FOR_negv16hi2 = 13425,
  CODE_FOR_negv8hi2 = 13426,
  CODE_FOR_negv16si2 = 13427,
  CODE_FOR_negv8si2 = 13428,
  CODE_FOR_negv4si2 = 13429,
  CODE_FOR_negv8di2 = 13430,
  CODE_FOR_negv4di2 = 13431,
  CODE_FOR_negv2di2 = 13432,
  CODE_FOR_addv64qi3 = 13433,
  CODE_FOR_subv64qi3 = 13434,
  CODE_FOR_addv32qi3 = 13435,
  CODE_FOR_subv32qi3 = 13436,
  CODE_FOR_addv16qi3 = 13437,
  CODE_FOR_subv16qi3 = 13438,
  CODE_FOR_addv32hi3 = 13439,
  CODE_FOR_subv32hi3 = 13440,
  CODE_FOR_addv16hi3 = 13441,
  CODE_FOR_subv16hi3 = 13442,
  CODE_FOR_addv8hi3 = 13443,
  CODE_FOR_subv8hi3 = 13444,
  CODE_FOR_addv16si3 = 13445,
  CODE_FOR_subv16si3 = 13446,
  CODE_FOR_addv8si3 = 13447,
  CODE_FOR_subv8si3 = 13448,
  CODE_FOR_addv4si3 = 13449,
  CODE_FOR_subv4si3 = 13450,
  CODE_FOR_addv8di3 = 13451,
  CODE_FOR_subv8di3 = 13452,
  CODE_FOR_addv4di3 = 13453,
  CODE_FOR_subv4di3 = 13454,
  CODE_FOR_addv2di3 = 13455,
  CODE_FOR_subv2di3 = 13456,
  CODE_FOR_cond_addv64qi = 13457,
  CODE_FOR_cond_subv64qi = 13458,
  CODE_FOR_cond_addv32qi = 13459,
  CODE_FOR_cond_subv32qi = 13460,
  CODE_FOR_cond_addv16qi = 13461,
  CODE_FOR_cond_subv16qi = 13462,
  CODE_FOR_cond_addv32hi = 13463,
  CODE_FOR_cond_subv32hi = 13464,
  CODE_FOR_cond_addv16hi = 13465,
  CODE_FOR_cond_subv16hi = 13466,
  CODE_FOR_cond_addv8hi = 13467,
  CODE_FOR_cond_subv8hi = 13468,
  CODE_FOR_cond_addv16si = 13469,
  CODE_FOR_cond_subv16si = 13470,
  CODE_FOR_cond_addv8si = 13471,
  CODE_FOR_cond_subv8si = 13472,
  CODE_FOR_cond_addv4si = 13473,
  CODE_FOR_cond_subv4si = 13474,
  CODE_FOR_cond_addv8di = 13475,
  CODE_FOR_cond_subv8di = 13476,
  CODE_FOR_cond_addv4di = 13477,
  CODE_FOR_cond_subv4di = 13478,
  CODE_FOR_cond_addv2di = 13479,
  CODE_FOR_cond_subv2di = 13480,
  CODE_FOR_addv16si3_mask = 13481,
  CODE_FOR_subv16si3_mask = 13482,
  CODE_FOR_addv8si3_mask = 13483,
  CODE_FOR_subv8si3_mask = 13484,
  CODE_FOR_addv4si3_mask = 13485,
  CODE_FOR_subv4si3_mask = 13486,
  CODE_FOR_addv8di3_mask = 13487,
  CODE_FOR_subv8di3_mask = 13488,
  CODE_FOR_addv4di3_mask = 13489,
  CODE_FOR_subv4di3_mask = 13490,
  CODE_FOR_addv2di3_mask = 13491,
  CODE_FOR_subv2di3_mask = 13492,
  CODE_FOR_addv64qi3_mask = 13493,
  CODE_FOR_subv64qi3_mask = 13494,
  CODE_FOR_addv16qi3_mask = 13495,
  CODE_FOR_subv16qi3_mask = 13496,
  CODE_FOR_addv32qi3_mask = 13497,
  CODE_FOR_subv32qi3_mask = 13498,
  CODE_FOR_addv32hi3_mask = 13499,
  CODE_FOR_subv32hi3_mask = 13500,
  CODE_FOR_addv16hi3_mask = 13501,
  CODE_FOR_subv16hi3_mask = 13502,
  CODE_FOR_addv8hi3_mask = 13503,
  CODE_FOR_subv8hi3_mask = 13504,
  CODE_FOR_ssaddv64qi3 = 13505,
  CODE_FOR_ssaddv64qi3_mask = 13506,
  CODE_FOR_usaddv64qi3 = 13507,
  CODE_FOR_usaddv64qi3_mask = 13508,
  CODE_FOR_sssubv64qi3 = 13509,
  CODE_FOR_sssubv64qi3_mask = 13510,
  CODE_FOR_ussubv64qi3 = 13511,
  CODE_FOR_ussubv64qi3_mask = 13512,
  CODE_FOR_ssaddv32qi3 = 13513,
  CODE_FOR_ssaddv32qi3_mask = 13514,
  CODE_FOR_usaddv32qi3 = 13515,
  CODE_FOR_usaddv32qi3_mask = 13516,
  CODE_FOR_sssubv32qi3 = 13517,
  CODE_FOR_sssubv32qi3_mask = 13518,
  CODE_FOR_ussubv32qi3 = 13519,
  CODE_FOR_ussubv32qi3_mask = 13520,
  CODE_FOR_ssaddv16qi3 = 13521,
  CODE_FOR_ssaddv16qi3_mask = 13522,
  CODE_FOR_usaddv16qi3 = 13523,
  CODE_FOR_usaddv16qi3_mask = 13524,
  CODE_FOR_sssubv16qi3 = 13525,
  CODE_FOR_sssubv16qi3_mask = 13526,
  CODE_FOR_ussubv16qi3 = 13527,
  CODE_FOR_ussubv16qi3_mask = 13528,
  CODE_FOR_ssaddv32hi3 = 13529,
  CODE_FOR_ssaddv32hi3_mask = 13530,
  CODE_FOR_usaddv32hi3 = 13531,
  CODE_FOR_usaddv32hi3_mask = 13532,
  CODE_FOR_sssubv32hi3 = 13533,
  CODE_FOR_sssubv32hi3_mask = 13534,
  CODE_FOR_ussubv32hi3 = 13535,
  CODE_FOR_ussubv32hi3_mask = 13536,
  CODE_FOR_ssaddv16hi3 = 13537,
  CODE_FOR_ssaddv16hi3_mask = 13538,
  CODE_FOR_usaddv16hi3 = 13539,
  CODE_FOR_usaddv16hi3_mask = 13540,
  CODE_FOR_sssubv16hi3 = 13541,
  CODE_FOR_sssubv16hi3_mask = 13542,
  CODE_FOR_ussubv16hi3 = 13543,
  CODE_FOR_ussubv16hi3_mask = 13544,
  CODE_FOR_ssaddv8hi3 = 13545,
  CODE_FOR_ssaddv8hi3_mask = 13546,
  CODE_FOR_usaddv8hi3 = 13547,
  CODE_FOR_usaddv8hi3_mask = 13548,
  CODE_FOR_sssubv8hi3 = 13549,
  CODE_FOR_sssubv8hi3_mask = 13550,
  CODE_FOR_ussubv8hi3 = 13551,
  CODE_FOR_ussubv8hi3_mask = 13552,
  CODE_FOR_mulv64qi3 = 13553,
  CODE_FOR_mulv32qi3 = 13554,
  CODE_FOR_mulv16qi3 = 13555,
  CODE_FOR_cond_mulv8hi = 13556,
  CODE_FOR_cond_mulv16hi = 13557,
  CODE_FOR_cond_mulv32hi = 13558,
  CODE_FOR_mulv32hi3 = 13559,
  CODE_FOR_mulv32hi3_mask = 13560,
  CODE_FOR_mulv16hi3 = 13561,
  CODE_FOR_mulv16hi3_mask = 13562,
  CODE_FOR_mulv8hi3 = 13563,
  CODE_FOR_mulv8hi3_mask = 13564,
  CODE_FOR_smulv32hi3_highpart = 13565,
  CODE_FOR_smulv32hi3_highpart_mask = 13566,
  CODE_FOR_umulv32hi3_highpart = 13567,
  CODE_FOR_umulv32hi3_highpart_mask = 13568,
  CODE_FOR_smulv16hi3_highpart = 13569,
  CODE_FOR_smulv16hi3_highpart_mask = 13570,
  CODE_FOR_umulv16hi3_highpart = 13571,
  CODE_FOR_umulv16hi3_highpart_mask = 13572,
  CODE_FOR_smulv8hi3_highpart = 13573,
  CODE_FOR_smulv8hi3_highpart_mask = 13574,
  CODE_FOR_umulv8hi3_highpart = 13575,
  CODE_FOR_umulv8hi3_highpart_mask = 13576,
  CODE_FOR_vec_widen_umult_even_v16si = 13577,
  CODE_FOR_vec_widen_umult_even_v16si_mask = 13578,
  CODE_FOR_vec_widen_umult_even_v8si = 13579,
  CODE_FOR_vec_widen_umult_even_v8si_mask = 13580,
  CODE_FOR_vec_widen_umult_even_v4si = 13581,
  CODE_FOR_vec_widen_umult_even_v4si_mask = 13582,
  CODE_FOR_vec_widen_smult_even_v16si = 13583,
  CODE_FOR_vec_widen_smult_even_v16si_mask = 13584,
  CODE_FOR_vec_widen_smult_even_v8si = 13585,
  CODE_FOR_vec_widen_smult_even_v8si_mask = 13586,
  CODE_FOR_sse4_1_mulv2siv2di3 = 13587,
  CODE_FOR_sse4_1_mulv2siv2di3_mask = 13588,
  CODE_FOR_avx2_pmaddwd = 13589,
  CODE_FOR_sse2_pmaddwd = 13590,
  CODE_FOR_cond_mulv8di = 13591,
  CODE_FOR_cond_mulv4di = 13592,
  CODE_FOR_cond_mulv2di = 13593,
  CODE_FOR_avx512dq_mulv8di3 = 13594,
  CODE_FOR_avx512dq_mulv8di3_mask = 13595,
  CODE_FOR_avx512dq_mulv4di3 = 13596,
  CODE_FOR_avx512dq_mulv4di3_mask = 13597,
  CODE_FOR_avx512dq_mulv2di3 = 13598,
  CODE_FOR_avx512dq_mulv2di3_mask = 13599,
  CODE_FOR_cond_mulv16si = 13600,
  CODE_FOR_cond_mulv8si = 13601,
  CODE_FOR_cond_mulv4si = 13602,
  CODE_FOR_mulv16si3 = 13603,
  CODE_FOR_mulv16si3_mask = 13604,
  CODE_FOR_mulv8si3 = 13605,
  CODE_FOR_mulv8si3_mask = 13606,
  CODE_FOR_mulv4si3 = 13607,
  CODE_FOR_mulv4si3_mask = 13608,
  CODE_FOR_mulv8di3 = 13609,
  CODE_FOR_mulv4di3 = 13610,
  CODE_FOR_mulv2di3 = 13611,
  CODE_FOR_vec_widen_smult_hi_v32qi = 13612,
  CODE_FOR_vec_widen_umult_hi_v32qi = 13613,
  CODE_FOR_vec_widen_smult_hi_v16qi = 13614,
  CODE_FOR_vec_widen_umult_hi_v16qi = 13615,
  CODE_FOR_vec_widen_smult_hi_v16hi = 13616,
  CODE_FOR_vec_widen_umult_hi_v16hi = 13617,
  CODE_FOR_vec_widen_smult_hi_v8hi = 13618,
  CODE_FOR_vec_widen_umult_hi_v8hi = 13619,
  CODE_FOR_vec_widen_smult_hi_v8si = 13620,
  CODE_FOR_vec_widen_umult_hi_v8si = 13621,
  CODE_FOR_vec_widen_smult_hi_v4si = 13622,
  CODE_FOR_vec_widen_umult_hi_v4si = 13623,
  CODE_FOR_vec_widen_smult_lo_v32qi = 13624,
  CODE_FOR_vec_widen_umult_lo_v32qi = 13625,
  CODE_FOR_vec_widen_smult_lo_v16qi = 13626,
  CODE_FOR_vec_widen_umult_lo_v16qi = 13627,
  CODE_FOR_vec_widen_smult_lo_v16hi = 13628,
  CODE_FOR_vec_widen_umult_lo_v16hi = 13629,
  CODE_FOR_vec_widen_smult_lo_v8hi = 13630,
  CODE_FOR_vec_widen_umult_lo_v8hi = 13631,
  CODE_FOR_vec_widen_smult_lo_v8si = 13632,
  CODE_FOR_vec_widen_umult_lo_v8si = 13633,
  CODE_FOR_vec_widen_smult_lo_v4si = 13634,
  CODE_FOR_vec_widen_umult_lo_v4si = 13635,
  CODE_FOR_vec_widen_smult_even_v4si = 13636,
  CODE_FOR_vec_widen_smult_odd_v16si = 13637,
  CODE_FOR_vec_widen_umult_odd_v16si = 13638,
  CODE_FOR_vec_widen_smult_odd_v8si = 13639,
  CODE_FOR_vec_widen_umult_odd_v8si = 13640,
  CODE_FOR_vec_widen_smult_odd_v4si = 13641,
  CODE_FOR_vec_widen_umult_odd_v4si = 13642,
  CODE_FOR_sdot_prodv16siv32hi = 13643,
  CODE_FOR_sdot_prodv8siv16hi = 13644,
  CODE_FOR_sdot_prodv4siv8hi = 13645,
  CODE_FOR_sdot_prodv2div4si = 13646,
  CODE_FOR_uavgv64qi3_ceil = 13647,
  CODE_FOR_uavgv32qi3_ceil = 13648,
  CODE_FOR_uavgv16qi3_ceil = 13649,
  CODE_FOR_uavgv32hi3_ceil = 13650,
  CODE_FOR_uavgv16hi3_ceil = 13651,
  CODE_FOR_uavgv8hi3_ceil = 13652,
  CODE_FOR_usadv16qi = 13653,
  CODE_FOR_usadv32qi = 13654,
  CODE_FOR_usadv64qi = 13655,
  CODE_FOR_ashrv32hi3 = 13656,
  CODE_FOR_ashrv16si3 = 13657,
  CODE_FOR_ashrv8di3 = 13658,
  CODE_FOR_ashrv4di3 = 13659,
  CODE_FOR_vec_shl_v16qi = 13660,
  CODE_FOR_vec_shl_v8hi = 13661,
  CODE_FOR_vec_shl_v8hf = 13662,
  CODE_FOR_vec_shl_v8bf = 13663,
  CODE_FOR_vec_shl_v4si = 13664,
  CODE_FOR_vec_shl_v2di = 13665,
  CODE_FOR_vec_shl_v4sf = 13666,
  CODE_FOR_vec_shl_v2df = 13667,
  CODE_FOR_vec_shr_v16qi = 13668,
  CODE_FOR_vec_shr_v8hi = 13669,
  CODE_FOR_vec_shr_v8hf = 13670,
  CODE_FOR_vec_shr_v8bf = 13671,
  CODE_FOR_vec_shr_v4si = 13672,
  CODE_FOR_vec_shr_v2di = 13673,
  CODE_FOR_vec_shr_v4sf = 13674,
  CODE_FOR_vec_shr_v2df = 13675,
  CODE_FOR_ashlv1ti3 = 13676,
  CODE_FOR_lshrv1ti3 = 13677,
  CODE_FOR_ashrv1ti3 = 13678,
  CODE_FOR_rotlv1ti3 = 13679,
  CODE_FOR_rotrv1ti3 = 13680,
  CODE_FOR_smaxv32qi3 = 13681,
  CODE_FOR_sminv32qi3 = 13682,
  CODE_FOR_umaxv32qi3 = 13683,
  CODE_FOR_uminv32qi3 = 13684,
  CODE_FOR_smaxv16hi3 = 13685,
  CODE_FOR_sminv16hi3 = 13686,
  CODE_FOR_umaxv16hi3 = 13687,
  CODE_FOR_uminv16hi3 = 13688,
  CODE_FOR_smaxv8si3 = 13689,
  CODE_FOR_sminv8si3 = 13690,
  CODE_FOR_umaxv8si3 = 13691,
  CODE_FOR_uminv8si3 = 13692,
  CODE_FOR_smaxv64qi3 = 13693,
  CODE_FOR_sminv64qi3 = 13694,
  CODE_FOR_umaxv64qi3 = 13695,
  CODE_FOR_uminv64qi3 = 13696,
  CODE_FOR_smaxv32hi3 = 13697,
  CODE_FOR_sminv32hi3 = 13698,
  CODE_FOR_umaxv32hi3 = 13699,
  CODE_FOR_uminv32hi3 = 13700,
  CODE_FOR_smaxv16si3 = 13701,
  CODE_FOR_sminv16si3 = 13702,
  CODE_FOR_umaxv16si3 = 13703,
  CODE_FOR_uminv16si3 = 13704,
  CODE_FOR_cond_smaxv64qi = 13705,
  CODE_FOR_cond_sminv64qi = 13706,
  CODE_FOR_cond_umaxv64qi = 13707,
  CODE_FOR_cond_uminv64qi = 13708,
  CODE_FOR_cond_smaxv32qi = 13709,
  CODE_FOR_cond_sminv32qi = 13710,
  CODE_FOR_cond_umaxv32qi = 13711,
  CODE_FOR_cond_uminv32qi = 13712,
  CODE_FOR_cond_smaxv16qi = 13713,
  CODE_FOR_cond_sminv16qi = 13714,
  CODE_FOR_cond_umaxv16qi = 13715,
  CODE_FOR_cond_uminv16qi = 13716,
  CODE_FOR_cond_smaxv32hi = 13717,
  CODE_FOR_cond_sminv32hi = 13718,
  CODE_FOR_cond_umaxv32hi = 13719,
  CODE_FOR_cond_uminv32hi = 13720,
  CODE_FOR_cond_smaxv16hi = 13721,
  CODE_FOR_cond_sminv16hi = 13722,
  CODE_FOR_cond_umaxv16hi = 13723,
  CODE_FOR_cond_uminv16hi = 13724,
  CODE_FOR_cond_smaxv8hi = 13725,
  CODE_FOR_cond_sminv8hi = 13726,
  CODE_FOR_cond_umaxv8hi = 13727,
  CODE_FOR_cond_uminv8hi = 13728,
  CODE_FOR_cond_smaxv16si = 13729,
  CODE_FOR_cond_sminv16si = 13730,
  CODE_FOR_cond_umaxv16si = 13731,
  CODE_FOR_cond_uminv16si = 13732,
  CODE_FOR_cond_smaxv8si = 13733,
  CODE_FOR_cond_sminv8si = 13734,
  CODE_FOR_cond_umaxv8si = 13735,
  CODE_FOR_cond_uminv8si = 13736,
  CODE_FOR_cond_smaxv4si = 13737,
  CODE_FOR_cond_sminv4si = 13738,
  CODE_FOR_cond_umaxv4si = 13739,
  CODE_FOR_cond_uminv4si = 13740,
  CODE_FOR_cond_smaxv8di = 13741,
  CODE_FOR_cond_sminv8di = 13742,
  CODE_FOR_cond_umaxv8di = 13743,
  CODE_FOR_cond_uminv8di = 13744,
  CODE_FOR_cond_smaxv4di = 13745,
  CODE_FOR_cond_sminv4di = 13746,
  CODE_FOR_cond_umaxv4di = 13747,
  CODE_FOR_cond_uminv4di = 13748,
  CODE_FOR_cond_smaxv2di = 13749,
  CODE_FOR_cond_sminv2di = 13750,
  CODE_FOR_cond_umaxv2di = 13751,
  CODE_FOR_cond_uminv2di = 13752,
  CODE_FOR_smaxv64qi3_mask = 13753,
  CODE_FOR_sminv64qi3_mask = 13754,
  CODE_FOR_umaxv64qi3_mask = 13755,
  CODE_FOR_uminv64qi3_mask = 13756,
  CODE_FOR_smaxv32qi3_mask = 13757,
  CODE_FOR_sminv32qi3_mask = 13758,
  CODE_FOR_umaxv32qi3_mask = 13759,
  CODE_FOR_uminv32qi3_mask = 13760,
  CODE_FOR_smaxv16qi3_mask = 13761,
  CODE_FOR_sminv16qi3_mask = 13762,
  CODE_FOR_umaxv16qi3_mask = 13763,
  CODE_FOR_uminv16qi3_mask = 13764,
  CODE_FOR_smaxv32hi3_mask = 13765,
  CODE_FOR_sminv32hi3_mask = 13766,
  CODE_FOR_umaxv32hi3_mask = 13767,
  CODE_FOR_uminv32hi3_mask = 13768,
  CODE_FOR_smaxv16hi3_mask = 13769,
  CODE_FOR_sminv16hi3_mask = 13770,
  CODE_FOR_umaxv16hi3_mask = 13771,
  CODE_FOR_uminv16hi3_mask = 13772,
  CODE_FOR_smaxv8hi3_mask = 13773,
  CODE_FOR_sminv8hi3_mask = 13774,
  CODE_FOR_umaxv8hi3_mask = 13775,
  CODE_FOR_uminv8hi3_mask = 13776,
  CODE_FOR_smaxv16si3_mask = 13777,
  CODE_FOR_sminv16si3_mask = 13778,
  CODE_FOR_umaxv16si3_mask = 13779,
  CODE_FOR_uminv16si3_mask = 13780,
  CODE_FOR_smaxv8si3_mask = 13781,
  CODE_FOR_sminv8si3_mask = 13782,
  CODE_FOR_umaxv8si3_mask = 13783,
  CODE_FOR_uminv8si3_mask = 13784,
  CODE_FOR_smaxv4si3_mask = 13785,
  CODE_FOR_sminv4si3_mask = 13786,
  CODE_FOR_umaxv4si3_mask = 13787,
  CODE_FOR_uminv4si3_mask = 13788,
  CODE_FOR_smaxv8di3_mask = 13789,
  CODE_FOR_sminv8di3_mask = 13790,
  CODE_FOR_umaxv8di3_mask = 13791,
  CODE_FOR_uminv8di3_mask = 13792,
  CODE_FOR_smaxv4di3_mask = 13793,
  CODE_FOR_sminv4di3_mask = 13794,
  CODE_FOR_umaxv4di3_mask = 13795,
  CODE_FOR_uminv4di3_mask = 13796,
  CODE_FOR_smaxv2di3_mask = 13797,
  CODE_FOR_sminv2di3_mask = 13798,
  CODE_FOR_umaxv2di3_mask = 13799,
  CODE_FOR_uminv2di3_mask = 13800,
  CODE_FOR_smaxv8di3 = 13801,
  CODE_FOR_sminv8di3 = 13802,
  CODE_FOR_umaxv8di3 = 13803,
  CODE_FOR_uminv8di3 = 13804,
  CODE_FOR_smaxv4di3 = 13805,
  CODE_FOR_sminv4di3 = 13806,
  CODE_FOR_umaxv4di3 = 13807,
  CODE_FOR_uminv4di3 = 13808,
  CODE_FOR_smaxv2di3 = 13809,
  CODE_FOR_sminv2di3 = 13810,
  CODE_FOR_umaxv2di3 = 13811,
  CODE_FOR_uminv2di3 = 13812,
  CODE_FOR_smaxv16qi3 = 13813,
  CODE_FOR_sminv16qi3 = 13814,
  CODE_FOR_smaxv8hi3 = 13815,
  CODE_FOR_sminv8hi3 = 13816,
  CODE_FOR_smaxv4si3 = 13817,
  CODE_FOR_sminv4si3 = 13818,
  CODE_FOR_umaxv16qi3 = 13819,
  CODE_FOR_uminv16qi3 = 13820,
  CODE_FOR_umaxv8hi3 = 13821,
  CODE_FOR_uminv8hi3 = 13822,
  CODE_FOR_umaxv4si3 = 13823,
  CODE_FOR_uminv4si3 = 13824,
  CODE_FOR_avx512bw_eqv64qi3 = 13825,
  CODE_FOR_avx512bw_eqv64qi3_mask = 13826,
  CODE_FOR_avx512vl_eqv16qi3 = 13827,
  CODE_FOR_avx512vl_eqv16qi3_mask = 13828,
  CODE_FOR_avx512vl_eqv32qi3 = 13829,
  CODE_FOR_avx512vl_eqv32qi3_mask = 13830,
  CODE_FOR_avx512bw_eqv32hi3 = 13831,
  CODE_FOR_avx512bw_eqv32hi3_mask = 13832,
  CODE_FOR_avx512vl_eqv16hi3 = 13833,
  CODE_FOR_avx512vl_eqv16hi3_mask = 13834,
  CODE_FOR_avx512vl_eqv8hi3 = 13835,
  CODE_FOR_avx512vl_eqv8hi3_mask = 13836,
  CODE_FOR_avx512f_eqv16si3 = 13837,
  CODE_FOR_avx512f_eqv16si3_mask = 13838,
  CODE_FOR_avx512vl_eqv8si3 = 13839,
  CODE_FOR_avx512vl_eqv8si3_mask = 13840,
  CODE_FOR_avx512vl_eqv4si3 = 13841,
  CODE_FOR_avx512vl_eqv4si3_mask = 13842,
  CODE_FOR_avx512f_eqv8di3 = 13843,
  CODE_FOR_avx512f_eqv8di3_mask = 13844,
  CODE_FOR_avx512vl_eqv4di3 = 13845,
  CODE_FOR_avx512vl_eqv4di3_mask = 13846,
  CODE_FOR_avx512vl_eqv2di3 = 13847,
  CODE_FOR_avx512vl_eqv2di3_mask = 13848,
  CODE_FOR_avx512f_gtv16si3 = 13849,
  CODE_FOR_avx512f_gtv16si3_mask = 13850,
  CODE_FOR_avx512vl_gtv8si3 = 13851,
  CODE_FOR_avx512vl_gtv8si3_mask = 13852,
  CODE_FOR_avx512vl_gtv4si3 = 13853,
  CODE_FOR_avx512vl_gtv4si3_mask = 13854,
  CODE_FOR_avx512f_gtv8di3 = 13855,
  CODE_FOR_avx512f_gtv8di3_mask = 13856,
  CODE_FOR_avx512vl_gtv4di3 = 13857,
  CODE_FOR_avx512vl_gtv4di3_mask = 13858,
  CODE_FOR_avx512vl_gtv2di3 = 13859,
  CODE_FOR_avx512vl_gtv2di3_mask = 13860,
  CODE_FOR_avx512bw_gtv64qi3 = 13861,
  CODE_FOR_avx512bw_gtv64qi3_mask = 13862,
  CODE_FOR_avx512vl_gtv16qi3 = 13863,
  CODE_FOR_avx512vl_gtv16qi3_mask = 13864,
  CODE_FOR_avx512vl_gtv32qi3 = 13865,
  CODE_FOR_avx512vl_gtv32qi3_mask = 13866,
  CODE_FOR_avx512bw_gtv32hi3 = 13867,
  CODE_FOR_avx512bw_gtv32hi3_mask = 13868,
  CODE_FOR_avx512vl_gtv16hi3 = 13869,
  CODE_FOR_avx512vl_gtv16hi3_mask = 13870,
  CODE_FOR_avx512vl_gtv8hi3 = 13871,
  CODE_FOR_avx512vl_gtv8hi3_mask = 13872,
  CODE_FOR_vec_permv16qi = 13873,
  CODE_FOR_vec_permv8hi = 13874,
  CODE_FOR_vec_permv4si = 13875,
  CODE_FOR_vec_permv2di = 13876,
  CODE_FOR_vec_permv4sf = 13877,
  CODE_FOR_vec_permv2df = 13878,
  CODE_FOR_vec_permv8hf = 13879,
  CODE_FOR_vec_permv32qi = 13880,
  CODE_FOR_vec_permv16hi = 13881,
  CODE_FOR_vec_permv8si = 13882,
  CODE_FOR_vec_permv4di = 13883,
  CODE_FOR_vec_permv8sf = 13884,
  CODE_FOR_vec_permv4df = 13885,
  CODE_FOR_vec_permv16hf = 13886,
  CODE_FOR_vec_permv16sf = 13887,
  CODE_FOR_vec_permv8df = 13888,
  CODE_FOR_vec_permv16si = 13889,
  CODE_FOR_vec_permv8di = 13890,
  CODE_FOR_vec_permv32hi = 13891,
  CODE_FOR_vec_permv64qi = 13892,
  CODE_FOR_vec_permv32hf = 13893,
  CODE_FOR_one_cmplv16si2 = 13894,
  CODE_FOR_one_cmplv8di2 = 13895,
  CODE_FOR_one_cmplv64qi2 = 13896,
  CODE_FOR_one_cmplv32qi2 = 13897,
  CODE_FOR_one_cmplv16qi2 = 13898,
  CODE_FOR_one_cmplv32hi2 = 13899,
  CODE_FOR_one_cmplv16hi2 = 13900,
  CODE_FOR_one_cmplv8hi2 = 13901,
  CODE_FOR_one_cmplv8si2 = 13902,
  CODE_FOR_one_cmplv4si2 = 13903,
  CODE_FOR_one_cmplv4di2 = 13904,
  CODE_FOR_one_cmplv2di2 = 13905,
  CODE_FOR_avx512bw_andnotv64qi3 = 13906,
  CODE_FOR_avx2_andnotv32qi3 = 13907,
  CODE_FOR_sse2_andnotv16qi3 = 13908,
  CODE_FOR_avx512bw_andnotv32hi3 = 13909,
  CODE_FOR_avx2_andnotv16hi3 = 13910,
  CODE_FOR_sse2_andnotv8hi3 = 13911,
  CODE_FOR_avx512f_andnotv16si3 = 13912,
  CODE_FOR_avx2_andnotv8si3 = 13913,
  CODE_FOR_sse2_andnotv4si3 = 13914,
  CODE_FOR_avx512f_andnotv8di3 = 13915,
  CODE_FOR_avx2_andnotv4di3 = 13916,
  CODE_FOR_sse2_andnotv2di3 = 13917,
  CODE_FOR_andnv16si3 = 13918,
  CODE_FOR_andnv8di3 = 13919,
  CODE_FOR_andnv64qi3 = 13920,
  CODE_FOR_andnv32qi3 = 13921,
  CODE_FOR_andnv16qi3 = 13922,
  CODE_FOR_andnv32hi3 = 13923,
  CODE_FOR_andnv16hi3 = 13924,
  CODE_FOR_andnv8hi3 = 13925,
  CODE_FOR_andnv8si3 = 13926,
  CODE_FOR_andnv4si3 = 13927,
  CODE_FOR_andnv4di3 = 13928,
  CODE_FOR_andnv2di3 = 13929,
  CODE_FOR_avx512f_andnotv16si3_mask = 13930,
  CODE_FOR_avx2_andnotv8si3_mask = 13931,
  CODE_FOR_sse2_andnotv4si3_mask = 13932,
  CODE_FOR_avx512f_andnotv8di3_mask = 13933,
  CODE_FOR_avx2_andnotv4di3_mask = 13934,
  CODE_FOR_sse2_andnotv2di3_mask = 13935,
  CODE_FOR_andv16si3 = 13936,
  CODE_FOR_iorv16si3 = 13937,
  CODE_FOR_xorv16si3 = 13938,
  CODE_FOR_andv8di3 = 13939,
  CODE_FOR_iorv8di3 = 13940,
  CODE_FOR_xorv8di3 = 13941,
  CODE_FOR_andv64qi3 = 13942,
  CODE_FOR_iorv64qi3 = 13943,
  CODE_FOR_xorv64qi3 = 13944,
  CODE_FOR_andv32qi3 = 13945,
  CODE_FOR_iorv32qi3 = 13946,
  CODE_FOR_xorv32qi3 = 13947,
  CODE_FOR_andv16qi3 = 13948,
  CODE_FOR_iorv16qi3 = 13949,
  CODE_FOR_xorv16qi3 = 13950,
  CODE_FOR_andv32hi3 = 13951,
  CODE_FOR_iorv32hi3 = 13952,
  CODE_FOR_xorv32hi3 = 13953,
  CODE_FOR_andv16hi3 = 13954,
  CODE_FOR_iorv16hi3 = 13955,
  CODE_FOR_xorv16hi3 = 13956,
  CODE_FOR_andv8hi3 = 13957,
  CODE_FOR_iorv8hi3 = 13958,
  CODE_FOR_xorv8hi3 = 13959,
  CODE_FOR_andv8si3 = 13960,
  CODE_FOR_iorv8si3 = 13961,
  CODE_FOR_xorv8si3 = 13962,
  CODE_FOR_andv4si3 = 13963,
  CODE_FOR_iorv4si3 = 13964,
  CODE_FOR_xorv4si3 = 13965,
  CODE_FOR_andv4di3 = 13966,
  CODE_FOR_iorv4di3 = 13967,
  CODE_FOR_xorv4di3 = 13968,
  CODE_FOR_andv2di3 = 13969,
  CODE_FOR_iorv2di3 = 13970,
  CODE_FOR_xorv2di3 = 13971,
  CODE_FOR_cond_andv16si = 13972,
  CODE_FOR_cond_iorv16si = 13973,
  CODE_FOR_cond_xorv16si = 13974,
  CODE_FOR_cond_andv8si = 13975,
  CODE_FOR_cond_iorv8si = 13976,
  CODE_FOR_cond_xorv8si = 13977,
  CODE_FOR_cond_andv4si = 13978,
  CODE_FOR_cond_iorv4si = 13979,
  CODE_FOR_cond_xorv4si = 13980,
  CODE_FOR_cond_andv8di = 13981,
  CODE_FOR_cond_iorv8di = 13982,
  CODE_FOR_cond_xorv8di = 13983,
  CODE_FOR_cond_andv4di = 13984,
  CODE_FOR_cond_iorv4di = 13985,
  CODE_FOR_cond_xorv4di = 13986,
  CODE_FOR_cond_andv2di = 13987,
  CODE_FOR_cond_iorv2di = 13988,
  CODE_FOR_cond_xorv2di = 13989,
  CODE_FOR_andv16si3_mask = 13990,
  CODE_FOR_iorv16si3_mask = 13991,
  CODE_FOR_xorv16si3_mask = 13992,
  CODE_FOR_andv8si3_mask = 13993,
  CODE_FOR_iorv8si3_mask = 13994,
  CODE_FOR_xorv8si3_mask = 13995,
  CODE_FOR_andv4si3_mask = 13996,
  CODE_FOR_iorv4si3_mask = 13997,
  CODE_FOR_xorv4si3_mask = 13998,
  CODE_FOR_andv8di3_mask = 13999,
  CODE_FOR_iorv8di3_mask = 14000,
  CODE_FOR_xorv8di3_mask = 14001,
  CODE_FOR_andv4di3_mask = 14002,
  CODE_FOR_iorv4di3_mask = 14003,
  CODE_FOR_xorv4di3_mask = 14004,
  CODE_FOR_andv2di3_mask = 14005,
  CODE_FOR_iorv2di3_mask = 14006,
  CODE_FOR_xorv2di3_mask = 14007,
  CODE_FOR_one_cmplv1ti2 = 14008,
  CODE_FOR_vec_pack_trunc_v32hi = 14009,
  CODE_FOR_vec_pack_trunc_v16hi = 14010,
  CODE_FOR_vec_pack_trunc_v8hi = 14011,
  CODE_FOR_vec_pack_trunc_v16si = 14012,
  CODE_FOR_vec_pack_trunc_v8si = 14013,
  CODE_FOR_vec_pack_trunc_v4si = 14014,
  CODE_FOR_vec_pack_trunc_v8di = 14015,
  CODE_FOR_vec_pack_trunc_v4di = 14016,
  CODE_FOR_vec_pack_trunc_v2di = 14017,
  CODE_FOR_vec_pack_trunc_qi = 14018,
  CODE_FOR_vec_pack_trunc_hi = 14019,
  CODE_FOR_vec_pack_trunc_si = 14020,
  CODE_FOR_vec_pack_sbool_trunc_qi = 14021,
  CODE_FOR_vec_interleave_highv32qi = 14022,
  CODE_FOR_vec_interleave_highv16hi = 14023,
  CODE_FOR_vec_interleave_highv8si = 14024,
  CODE_FOR_vec_interleave_highv4di = 14025,
  CODE_FOR_vec_interleave_lowv32qi = 14026,
  CODE_FOR_vec_interleave_lowv16hi = 14027,
  CODE_FOR_vec_interleave_lowv8si = 14028,
  CODE_FOR_vec_interleave_lowv4di = 14029,
  CODE_FOR_avx512dq_vinsertf64x2_mask = 14030,
  CODE_FOR_avx512dq_vinserti64x2_mask = 14031,
  CODE_FOR_avx512f_vinsertf32x4_mask = 14032,
  CODE_FOR_avx512f_vinserti32x4_mask = 14033,
  CODE_FOR_avx512dq_vinsertf32x8_mask = 14034,
  CODE_FOR_avx512dq_vinserti32x8_mask = 14035,
  CODE_FOR_avx512f_vinsertf64x4_mask = 14036,
  CODE_FOR_avx512f_vinserti64x4_mask = 14037,
  CODE_FOR_avx512dq_shuf_i64x2_mask = 14038,
  CODE_FOR_avx512dq_shuf_f64x2_mask = 14039,
  CODE_FOR_avx512f_shuf_f64x2_mask = 14040,
  CODE_FOR_avx512f_shuf_i64x2_mask = 14041,
  CODE_FOR_avx512vl_shuf_i32x4_mask = 14042,
  CODE_FOR_avx512vl_shuf_f32x4_mask = 14043,
  CODE_FOR_avx512f_shuf_f32x4_mask = 14044,
  CODE_FOR_avx512f_shuf_i32x4_mask = 14045,
  CODE_FOR_avx512f_pshufdv3_mask = 14046,
  CODE_FOR_avx512vl_pshufdv3_mask = 14047,
  CODE_FOR_avx2_pshufdv3 = 14048,
  CODE_FOR_avx512vl_pshufd_mask = 14049,
  CODE_FOR_sse2_pshufd = 14050,
  CODE_FOR_avx512vl_pshuflwv3_mask = 14051,
  CODE_FOR_avx2_pshuflwv3 = 14052,
  CODE_FOR_avx512vl_pshuflw_mask = 14053,
  CODE_FOR_sse2_pshuflw = 14054,
  CODE_FOR_avx2_pshufhwv3 = 14055,
  CODE_FOR_avx512vl_pshufhwv3_mask = 14056,
  CODE_FOR_avx512vl_pshufhw_mask = 14057,
  CODE_FOR_sse2_pshufhw = 14058,
  CODE_FOR_sse2_loadd = 14059,
  CODE_FOR_vec_unpacks_lo_v64qi = 14060,
  CODE_FOR_vec_unpacks_lo_v32qi = 14061,
  CODE_FOR_vec_unpacks_lo_v16qi = 14062,
  CODE_FOR_vec_unpacks_lo_v32hi = 14063,
  CODE_FOR_vec_unpacks_lo_v16hi = 14064,
  CODE_FOR_vec_unpacks_lo_v8hi = 14065,
  CODE_FOR_vec_unpacks_lo_v16si = 14066,
  CODE_FOR_vec_unpacks_lo_v8si = 14067,
  CODE_FOR_vec_unpacks_lo_v4si = 14068,
  CODE_FOR_vec_unpacks_hi_v64qi = 14069,
  CODE_FOR_vec_unpacks_hi_v32qi = 14070,
  CODE_FOR_vec_unpacks_hi_v16qi = 14071,
  CODE_FOR_vec_unpacks_hi_v32hi = 14072,
  CODE_FOR_vec_unpacks_hi_v16hi = 14073,
  CODE_FOR_vec_unpacks_hi_v8hi = 14074,
  CODE_FOR_vec_unpacks_hi_v16si = 14075,
  CODE_FOR_vec_unpacks_hi_v8si = 14076,
  CODE_FOR_vec_unpacks_hi_v4si = 14077,
  CODE_FOR_vec_unpacku_lo_v64qi = 14078,
  CODE_FOR_vec_unpacku_lo_v32qi = 14079,
  CODE_FOR_vec_unpacku_lo_v16qi = 14080,
  CODE_FOR_vec_unpacku_lo_v32hi = 14081,
  CODE_FOR_vec_unpacku_lo_v16hi = 14082,
  CODE_FOR_vec_unpacku_lo_v8hi = 14083,
  CODE_FOR_vec_unpacku_lo_v16si = 14084,
  CODE_FOR_vec_unpacku_lo_v8si = 14085,
  CODE_FOR_vec_unpacku_lo_v4si = 14086,
  CODE_FOR_vec_unpacks_sbool_lo_qi = 14087,
  CODE_FOR_vec_unpacks_lo_hi = 14088,
  CODE_FOR_vec_unpacks_lo_si = 14089,
  CODE_FOR_vec_unpacks_lo_di = 14090,
  CODE_FOR_vec_unpacku_hi_v64qi = 14091,
  CODE_FOR_vec_unpacku_hi_v32qi = 14092,
  CODE_FOR_vec_unpacku_hi_v16qi = 14093,
  CODE_FOR_vec_unpacku_hi_v32hi = 14094,
  CODE_FOR_vec_unpacku_hi_v16hi = 14095,
  CODE_FOR_vec_unpacku_hi_v8hi = 14096,
  CODE_FOR_vec_unpacku_hi_v16si = 14097,
  CODE_FOR_vec_unpacku_hi_v8si = 14098,
  CODE_FOR_vec_unpacku_hi_v4si = 14099,
  CODE_FOR_vec_unpacks_sbool_hi_qi = 14100,
  CODE_FOR_vec_unpacks_hi_hi = 14101,
  CODE_FOR_vec_unpacks_hi_si = 14102,
  CODE_FOR_vec_unpacks_hi_di = 14103,
  CODE_FOR_avx512bw_uavgv64qi3 = 14104,
  CODE_FOR_avx512bw_uavgv64qi3_mask = 14105,
  CODE_FOR_avx2_uavgv32qi3 = 14106,
  CODE_FOR_avx2_uavgv32qi3_mask = 14107,
  CODE_FOR_sse2_uavgv16qi3 = 14108,
  CODE_FOR_sse2_uavgv16qi3_mask = 14109,
  CODE_FOR_avx512bw_uavgv32hi3 = 14110,
  CODE_FOR_avx512bw_uavgv32hi3_mask = 14111,
  CODE_FOR_avx2_uavgv16hi3 = 14112,
  CODE_FOR_avx2_uavgv16hi3_mask = 14113,
  CODE_FOR_sse2_uavgv8hi3 = 14114,
  CODE_FOR_sse2_uavgv8hi3_mask = 14115,
  CODE_FOR_avx512f_psadbw = 14116,
  CODE_FOR_avx2_psadbw = 14117,
  CODE_FOR_sse2_psadbw = 14118,
  CODE_FOR_sse2_maskmovdqu = 14119,
  CODE_FOR_ssse3_pmulhrswv8hi3_mask = 14120,
  CODE_FOR_avx2_pmulhrswv16hi3_mask = 14121,
  CODE_FOR_ssse3_pmulhrswv8hi3 = 14122,
  CODE_FOR_avx2_pmulhrswv16hi3 = 14123,
  CODE_FOR_smulhrsv32hi3 = 14124,
  CODE_FOR_smulhrsv16hi3 = 14125,
  CODE_FOR_smulhrsv8hi3 = 14126,
  CODE_FOR_smulhrsv4hi3 = 14127,
  CODE_FOR_ssse3_pmulhrswv4hi3 = 14128,
  CODE_FOR_smulhrsv2hi3 = 14129,
  CODE_FOR_ssse3_pshufbv8qi3 = 14130,
  CODE_FOR_absv64qi2 = 14131,
  CODE_FOR_absv32qi2 = 14132,
  CODE_FOR_absv16qi2 = 14133,
  CODE_FOR_absv32hi2 = 14134,
  CODE_FOR_absv16hi2 = 14135,
  CODE_FOR_absv8hi2 = 14136,
  CODE_FOR_absv16si2 = 14137,
  CODE_FOR_absv8si2 = 14138,
  CODE_FOR_absv4si2 = 14139,
  CODE_FOR_absv8di2 = 14140,
  CODE_FOR_absv4di2 = 14141,
  CODE_FOR_absv2di2 = 14142,
  CODE_FOR_avx2_pblendw = 14143,
  CODE_FOR_avx2_pblendph = 14144,
  CODE_FOR_avx2_pblendbf = 14145,
  CODE_FOR_avx2_pblendw_1 = 14146,
  CODE_FOR_avx2_pblendph_1 = 14147,
  CODE_FOR_avx2_pblendbf_1 = 14148,
  CODE_FOR_extendv16qiv16hi2 = 14149,
  CODE_FOR_zero_extendv16qiv16hi2 = 14150,
  CODE_FOR_extendv32qiv32hi2 = 14151,
  CODE_FOR_zero_extendv32qiv32hi2 = 14152,
  CODE_FOR_extendv8qiv8hi2 = 14153,
  CODE_FOR_zero_extendv8qiv8hi2 = 14154,
  CODE_FOR_extendv16qiv16si2 = 14155,
  CODE_FOR_zero_extendv16qiv16si2 = 14156,
  CODE_FOR_extendv8qiv8si2 = 14157,
  CODE_FOR_zero_extendv8qiv8si2 = 14158,
  CODE_FOR_extendv4qiv4si2 = 14159,
  CODE_FOR_zero_extendv4qiv4si2 = 14160,
  CODE_FOR_extendv16hiv16si2 = 14161,
  CODE_FOR_zero_extendv16hiv16si2 = 14162,
  CODE_FOR_extendv8hiv8si2 = 14163,
  CODE_FOR_zero_extendv8hiv8si2 = 14164,
  CODE_FOR_extendv4hiv4si2 = 14165,
  CODE_FOR_zero_extendv4hiv4si2 = 14166,
  CODE_FOR_extendv8qiv8di2 = 14167,
  CODE_FOR_zero_extendv8qiv8di2 = 14168,
  CODE_FOR_extendv4qiv4di2 = 14169,
  CODE_FOR_zero_extendv4qiv4di2 = 14170,
  CODE_FOR_extendv2qiv2di2 = 14171,
  CODE_FOR_zero_extendv2qiv2di2 = 14172,
  CODE_FOR_extendv8hiv8di2 = 14173,
  CODE_FOR_zero_extendv8hiv8di2 = 14174,
  CODE_FOR_extendv4hiv4di2 = 14175,
  CODE_FOR_zero_extendv4hiv4di2 = 14176,
  CODE_FOR_extendv2hiv2di2 = 14177,
  CODE_FOR_zero_extendv2hiv2di2 = 14178,
  CODE_FOR_extendv8siv8di2 = 14179,
  CODE_FOR_zero_extendv8siv8di2 = 14180,
  CODE_FOR_extendv4siv4di2 = 14181,
  CODE_FOR_zero_extendv4siv4di2 = 14182,
  CODE_FOR_extendv2siv2di2 = 14183,
  CODE_FOR_zero_extendv2siv2di2 = 14184,
  CODE_FOR_sse4_1_ptestzv16qi = 14185,
  CODE_FOR_sse4_1_ptestzv8hi = 14186,
  CODE_FOR_sse4_1_ptestzv4si = 14187,
  CODE_FOR_sse4_1_ptestzv2di = 14188,
  CODE_FOR_sse4_1_ptestzv1ti = 14189,
  CODE_FOR_sse4_1_ptestzv4sf = 14190,
  CODE_FOR_sse4_1_ptestzv2df = 14191,
  CODE_FOR_avx_ptestzv32qi = 14192,
  CODE_FOR_avx_ptestzv16hi = 14193,
  CODE_FOR_avx_ptestzv8si = 14194,
  CODE_FOR_avx_ptestzv4di = 14195,
  CODE_FOR_avx_ptestzv2ti = 14196,
  CODE_FOR_avx_ptestzv8sf = 14197,
  CODE_FOR_avx_ptestzv4df = 14198,
  CODE_FOR_sse4_1_ptestcv16qi = 14199,
  CODE_FOR_sse4_1_ptestcv8hi = 14200,
  CODE_FOR_sse4_1_ptestcv4si = 14201,
  CODE_FOR_sse4_1_ptestcv2di = 14202,
  CODE_FOR_sse4_1_ptestcv1ti = 14203,
  CODE_FOR_sse4_1_ptestcv4sf = 14204,
  CODE_FOR_sse4_1_ptestcv2df = 14205,
  CODE_FOR_avx_ptestcv32qi = 14206,
  CODE_FOR_avx_ptestcv16hi = 14207,
  CODE_FOR_avx_ptestcv8si = 14208,
  CODE_FOR_avx_ptestcv4di = 14209,
  CODE_FOR_avx_ptestcv2ti = 14210,
  CODE_FOR_avx_ptestcv8sf = 14211,
  CODE_FOR_avx_ptestcv4df = 14212,
  CODE_FOR_sse4_1_ptestv16qi = 14213,
  CODE_FOR_sse4_1_ptestv8hi = 14214,
  CODE_FOR_sse4_1_ptestv4si = 14215,
  CODE_FOR_sse4_1_ptestv2di = 14216,
  CODE_FOR_sse4_1_ptestv1ti = 14217,
  CODE_FOR_sse4_1_ptestv4sf = 14218,
  CODE_FOR_sse4_1_ptestv2df = 14219,
  CODE_FOR_avx_ptestv32qi = 14220,
  CODE_FOR_avx_ptestv16hi = 14221,
  CODE_FOR_avx_ptestv8si = 14222,
  CODE_FOR_avx_ptestv4di = 14223,
  CODE_FOR_avx_ptestv2ti = 14224,
  CODE_FOR_avx_ptestv8sf = 14225,
  CODE_FOR_avx_ptestv4df = 14226,
  CODE_FOR_nearbyintv32hf2 = 14227,
  CODE_FOR_nearbyintv16hf2 = 14228,
  CODE_FOR_nearbyintv8hf2 = 14229,
  CODE_FOR_nearbyintv16sf2 = 14230,
  CODE_FOR_nearbyintv8sf2 = 14231,
  CODE_FOR_nearbyintv4sf2 = 14232,
  CODE_FOR_nearbyintv8df2 = 14233,
  CODE_FOR_nearbyintv4df2 = 14234,
  CODE_FOR_nearbyintv2df2 = 14235,
  CODE_FOR_rintv32hf2 = 14236,
  CODE_FOR_rintv16hf2 = 14237,
  CODE_FOR_rintv8hf2 = 14238,
  CODE_FOR_rintv16sf2 = 14239,
  CODE_FOR_rintv8sf2 = 14240,
  CODE_FOR_rintv4sf2 = 14241,
  CODE_FOR_rintv8df2 = 14242,
  CODE_FOR_rintv4df2 = 14243,
  CODE_FOR_rintv2df2 = 14244,
  CODE_FOR_lrintv16sfv16si2 = 14245,
  CODE_FOR_lrintv8sfv8si2 = 14246,
  CODE_FOR_lrintv4sfv4si2 = 14247,
  CODE_FOR_lrintv8dfv8di2 = 14248,
  CODE_FOR_lrintv4dfv4di2 = 14249,
  CODE_FOR_lrintv2dfv2di2 = 14250,
  CODE_FOR_avx_roundps_sfix256 = 14251,
  CODE_FOR_sse4_1_roundps_sfix = 14252,
  CODE_FOR_avx512f_roundps512 = 14253,
  CODE_FOR_avx512f_roundpd512 = 14254,
  CODE_FOR_avx512f_roundps512_sfix = 14255,
  CODE_FOR_avx512f_roundpd_vec_pack_sfix512 = 14256,
  CODE_FOR_avx_roundpd_vec_pack_sfix256 = 14257,
  CODE_FOR_sse4_1_roundpd_vec_pack_sfix = 14258,
  CODE_FOR_floorv32hf2 = 14259,
  CODE_FOR_floorv16hf2 = 14260,
  CODE_FOR_floorv8hf2 = 14261,
  CODE_FOR_floorv16sf2 = 14262,
  CODE_FOR_floorv8sf2 = 14263,
  CODE_FOR_floorv4sf2 = 14264,
  CODE_FOR_floorv8df2 = 14265,
  CODE_FOR_floorv4df2 = 14266,
  CODE_FOR_floorv2df2 = 14267,
  CODE_FOR_lfloorv32hfv32hi2 = 14268,
  CODE_FOR_lfloorv16hfv16hi2 = 14269,
  CODE_FOR_lfloorv8hfv8hi2 = 14270,
  CODE_FOR_lfloorv16sfv16si2 = 14271,
  CODE_FOR_lfloorv8sfv8si2 = 14272,
  CODE_FOR_lfloorv4sfv4si2 = 14273,
  CODE_FOR_lfloorv8dfv8di2 = 14274,
  CODE_FOR_lfloorv4dfv4di2 = 14275,
  CODE_FOR_lfloorv2dfv2di2 = 14276,
  CODE_FOR_ceilv32hf2 = 14277,
  CODE_FOR_ceilv16hf2 = 14278,
  CODE_FOR_ceilv8hf2 = 14279,
  CODE_FOR_ceilv16sf2 = 14280,
  CODE_FOR_ceilv8sf2 = 14281,
  CODE_FOR_ceilv4sf2 = 14282,
  CODE_FOR_ceilv8df2 = 14283,
  CODE_FOR_ceilv4df2 = 14284,
  CODE_FOR_ceilv2df2 = 14285,
  CODE_FOR_lceilv32hfv32hi2 = 14286,
  CODE_FOR_lceilv16hfv16hi2 = 14287,
  CODE_FOR_lceilv8hfv8hi2 = 14288,
  CODE_FOR_lceilv16sfv16si2 = 14289,
  CODE_FOR_lceilv8sfv8si2 = 14290,
  CODE_FOR_lceilv4sfv4si2 = 14291,
  CODE_FOR_lceilv8dfv8di2 = 14292,
  CODE_FOR_lceilv4dfv4di2 = 14293,
  CODE_FOR_lceilv2dfv2di2 = 14294,
  CODE_FOR_btruncv32hf2 = 14295,
  CODE_FOR_btruncv16hf2 = 14296,
  CODE_FOR_btruncv8hf2 = 14297,
  CODE_FOR_btruncv16sf2 = 14298,
  CODE_FOR_btruncv8sf2 = 14299,
  CODE_FOR_btruncv4sf2 = 14300,
  CODE_FOR_btruncv8df2 = 14301,
  CODE_FOR_btruncv4df2 = 14302,
  CODE_FOR_btruncv2df2 = 14303,
  CODE_FOR_roundv32hf2 = 14304,
  CODE_FOR_roundv16hf2 = 14305,
  CODE_FOR_roundv8hf2 = 14306,
  CODE_FOR_roundv16sf2 = 14307,
  CODE_FOR_roundv8sf2 = 14308,
  CODE_FOR_roundv4sf2 = 14309,
  CODE_FOR_roundv8df2 = 14310,
  CODE_FOR_roundv4df2 = 14311,
  CODE_FOR_roundv2df2 = 14312,
  CODE_FOR_lroundv32hfv32hi2 = 14313,
  CODE_FOR_lroundv16hfv16hi2 = 14314,
  CODE_FOR_lroundv8hfv8hi2 = 14315,
  CODE_FOR_lroundv16sfv16si2 = 14316,
  CODE_FOR_lroundv8sfv8si2 = 14317,
  CODE_FOR_lroundv4sfv4si2 = 14318,
  CODE_FOR_lroundv8dfv8di2 = 14319,
  CODE_FOR_lroundv4dfv4di2 = 14320,
  CODE_FOR_lroundv2dfv2di2 = 14321,
  CODE_FOR_roundv16sf2_sfix = 14322,
  CODE_FOR_roundv8sf2_sfix = 14323,
  CODE_FOR_roundv4sf2_sfix = 14324,
  CODE_FOR_roundv8df2_vec_pack_sfix = 14325,
  CODE_FOR_roundv4df2_vec_pack_sfix = 14326,
  CODE_FOR_roundv2df2_vec_pack_sfix = 14327,
  CODE_FOR_rotlv16qi3 = 14328,
  CODE_FOR_rotlv8hi3 = 14329,
  CODE_FOR_rotlv4si3 = 14330,
  CODE_FOR_rotlv2di3 = 14331,
  CODE_FOR_rotrv16qi3 = 14332,
  CODE_FOR_rotrv8hi3 = 14333,
  CODE_FOR_rotrv4si3 = 14334,
  CODE_FOR_rotrv2di3 = 14335,
  CODE_FOR_vrotrv16qi3 = 14336,
  CODE_FOR_vrotrv8hi3 = 14337,
  CODE_FOR_vrotrv4si3 = 14338,
  CODE_FOR_vrotrv2di3 = 14339,
  CODE_FOR_vrotlv16qi3 = 14340,
  CODE_FOR_vrotlv8hi3 = 14341,
  CODE_FOR_vrotlv4si3 = 14342,
  CODE_FOR_vrotlv2di3 = 14343,
  CODE_FOR_vlshrv16qi3 = 14344,
  CODE_FOR_vlshrv8hi3 = 14345,
  CODE_FOR_vlshrv4si3 = 14346,
  CODE_FOR_vlshrv2di3 = 14347,
  CODE_FOR_vashlv64qi3 = 14348,
  CODE_FOR_vlshrv64qi3 = 14349,
  CODE_FOR_vashrv64qi3 = 14350,
  CODE_FOR_vashlv32qi3 = 14351,
  CODE_FOR_vlshrv32qi3 = 14352,
  CODE_FOR_vashrv32qi3 = 14353,
  CODE_FOR_vashlv32hi3 = 14354,
  CODE_FOR_vlshrv32hi3 = 14355,
  CODE_FOR_vashrv32hi3 = 14356,
  CODE_FOR_vashlv16hi3 = 14357,
  CODE_FOR_vlshrv16hi3 = 14358,
  CODE_FOR_vashrv16hi3 = 14359,
  CODE_FOR_vlshrv16si3 = 14360,
  CODE_FOR_vlshrv8di3 = 14361,
  CODE_FOR_vlshrv8si3 = 14362,
  CODE_FOR_vlshrv4di3 = 14363,
  CODE_FOR_vashrv8di3 = 14364,
  CODE_FOR_vashrv4di3 = 14365,
  CODE_FOR_vashrv16qi3 = 14366,
  CODE_FOR_vashrv8hi3 = 14367,
  CODE_FOR_vashrv2di3 = 14368,
  CODE_FOR_vashrv4si3 = 14369,
  CODE_FOR_vashrv16si3 = 14370,
  CODE_FOR_vashrv8si3 = 14371,
  CODE_FOR_vashlv16qi3 = 14372,
  CODE_FOR_vashlv8hi3 = 14373,
  CODE_FOR_vashlv4si3 = 14374,
  CODE_FOR_vashlv2di3 = 14375,
  CODE_FOR_vashlv16si3 = 14376,
  CODE_FOR_vashlv8di3 = 14377,
  CODE_FOR_vashlv8si3 = 14378,
  CODE_FOR_vashlv4di3 = 14379,
  CODE_FOR_ashlv64qi3 = 14380,
  CODE_FOR_lshrv64qi3 = 14381,
  CODE_FOR_ashrv64qi3 = 14382,
  CODE_FOR_ashlv32qi3 = 14383,
  CODE_FOR_lshrv32qi3 = 14384,
  CODE_FOR_ashrv32qi3 = 14385,
  CODE_FOR_ashlv16qi3 = 14386,
  CODE_FOR_lshrv16qi3 = 14387,
  CODE_FOR_ashrv16qi3 = 14388,
  CODE_FOR_ashrv2di3 = 14389,
  CODE_FOR_xop_vmfrczv4sf2 = 14390,
  CODE_FOR_xop_vmfrczv2df2 = 14391,
  CODE_FOR_avx_vzeroall = 14392,
  CODE_FOR_avx_vzeroupper = 14393,
  CODE_FOR_avx512f_vpermilv8df = 14394,
  CODE_FOR_avx512f_vpermilv8df_mask = 14395,
  CODE_FOR_avx_vpermilv4df = 14396,
  CODE_FOR_avx_vpermilv4df_mask = 14397,
  CODE_FOR_avx_vpermilv2df = 14398,
  CODE_FOR_avx_vpermilv2df_mask = 14399,
  CODE_FOR_avx512f_vpermilv16sf = 14400,
  CODE_FOR_avx512f_vpermilv16sf_mask = 14401,
  CODE_FOR_avx_vpermilv8sf = 14402,
  CODE_FOR_avx_vpermilv8sf_mask = 14403,
  CODE_FOR_avx_vpermilv4sf = 14404,
  CODE_FOR_avx_vpermilv4sf_mask = 14405,
  CODE_FOR_avx2_permv4di = 14406,
  CODE_FOR_avx2_permv4df = 14407,
  CODE_FOR_avx512vl_permv4di_mask = 14408,
  CODE_FOR_avx512vl_permv4df_mask = 14409,
  CODE_FOR_avx512f_permv8df = 14410,
  CODE_FOR_avx512f_permv8di = 14411,
  CODE_FOR_avx512f_permv8df_mask = 14412,
  CODE_FOR_avx512f_permv8di_mask = 14413,
  CODE_FOR_avx512f_vpermi2varv16si3_mask = 14414,
  CODE_FOR_avx512f_vpermi2varv16sf3_mask = 14415,
  CODE_FOR_avx512f_vpermi2varv8di3_mask = 14416,
  CODE_FOR_avx512f_vpermi2varv8df3_mask = 14417,
  CODE_FOR_avx512vl_vpermi2varv8si3_mask = 14418,
  CODE_FOR_avx512vl_vpermi2varv8sf3_mask = 14419,
  CODE_FOR_avx512vl_vpermi2varv4di3_mask = 14420,
  CODE_FOR_avx512vl_vpermi2varv4df3_mask = 14421,
  CODE_FOR_avx512vl_vpermi2varv4si3_mask = 14422,
  CODE_FOR_avx512vl_vpermi2varv4sf3_mask = 14423,
  CODE_FOR_avx512vl_vpermi2varv2di3_mask = 14424,
  CODE_FOR_avx512vl_vpermi2varv2df3_mask = 14425,
  CODE_FOR_avx512bw_vpermi2varv32hi3_mask = 14426,
  CODE_FOR_avx512vl_vpermi2varv16hi3_mask = 14427,
  CODE_FOR_avx512vl_vpermi2varv8hi3_mask = 14428,
  CODE_FOR_avx512bw_vpermi2varv64qi3_mask = 14429,
  CODE_FOR_avx512vl_vpermi2varv32qi3_mask = 14430,
  CODE_FOR_avx512vl_vpermi2varv16qi3_mask = 14431,
  CODE_FOR_avx512f_vpermt2varv16si3_maskz = 14432,
  CODE_FOR_avx512f_vpermt2varv16sf3_maskz = 14433,
  CODE_FOR_avx512f_vpermt2varv8di3_maskz = 14434,
  CODE_FOR_avx512f_vpermt2varv8df3_maskz = 14435,
  CODE_FOR_avx512vl_vpermt2varv8si3_maskz = 14436,
  CODE_FOR_avx512vl_vpermt2varv8sf3_maskz = 14437,
  CODE_FOR_avx512vl_vpermt2varv4di3_maskz = 14438,
  CODE_FOR_avx512vl_vpermt2varv4df3_maskz = 14439,
  CODE_FOR_avx512vl_vpermt2varv4si3_maskz = 14440,
  CODE_FOR_avx512vl_vpermt2varv4sf3_maskz = 14441,
  CODE_FOR_avx512vl_vpermt2varv2di3_maskz = 14442,
  CODE_FOR_avx512vl_vpermt2varv2df3_maskz = 14443,
  CODE_FOR_avx512bw_vpermt2varv32hi3_maskz = 14444,
  CODE_FOR_avx512vl_vpermt2varv16hi3_maskz = 14445,
  CODE_FOR_avx512vl_vpermt2varv8hi3_maskz = 14446,
  CODE_FOR_avx512bw_vpermt2varv64qi3_maskz = 14447,
  CODE_FOR_avx512vl_vpermt2varv32qi3_maskz = 14448,
  CODE_FOR_avx512vl_vpermt2varv16qi3_maskz = 14449,
  CODE_FOR_avx_vperm2f128v8si3 = 14450,
  CODE_FOR_avx_vperm2f128v8sf3 = 14451,
  CODE_FOR_avx_vperm2f128v4df3 = 14452,
  CODE_FOR_avx512vl_vinsertv8si = 14453,
  CODE_FOR_avx512vl_vinsertv8sf = 14454,
  CODE_FOR_avx512vl_vinsertv4di = 14455,
  CODE_FOR_avx512vl_vinsertv4df = 14456,
  CODE_FOR_avx_vinsertf128v32qi = 14457,
  CODE_FOR_avx_vinsertf128v16hi = 14458,
  CODE_FOR_avx_vinsertf128v8si = 14459,
  CODE_FOR_avx_vinsertf128v4di = 14460,
  CODE_FOR_avx_vinsertf128v8sf = 14461,
  CODE_FOR_avx_vinsertf128v4df = 14462,
  CODE_FOR_avx_vinsertf128v16hf = 14463,
  CODE_FOR_avx_vinsertf128v16bf = 14464,
  CODE_FOR_maskloadv4sfv4si_1 = 14465,
  CODE_FOR_maskloadv2dfv2di_1 = 14466,
  CODE_FOR_maskloadv4div4di_1 = 14467,
  CODE_FOR_maskloadv2div2di_1 = 14468,
  CODE_FOR_maskloadv8sfv8si_1 = 14469,
  CODE_FOR_maskloadv4dfv4di_1 = 14470,
  CODE_FOR_maskloadv8siv8si_1 = 14471,
  CODE_FOR_maskloadv4siv4si_1 = 14472,
  CODE_FOR_maskloadv4sfv4si = 14473,
  CODE_FOR_maskloadv2dfv2di = 14474,
  CODE_FOR_maskloadv4div4di = 14475,
  CODE_FOR_maskloadv2div2di = 14476,
  CODE_FOR_maskloadv8sfv8si = 14477,
  CODE_FOR_maskloadv4dfv4di = 14478,
  CODE_FOR_maskloadv8siv8si = 14479,
  CODE_FOR_maskloadv4siv4si = 14480,
  CODE_FOR_maskloadv16sihi = 14481,
  CODE_FOR_maskloadv8siqi = 14482,
  CODE_FOR_maskloadv4siqi = 14483,
  CODE_FOR_maskloadv8diqi = 14484,
  CODE_FOR_maskloadv4diqi = 14485,
  CODE_FOR_maskloadv2diqi = 14486,
  CODE_FOR_maskloadv16sfhi = 14487,
  CODE_FOR_maskloadv8sfqi = 14488,
  CODE_FOR_maskloadv4sfqi = 14489,
  CODE_FOR_maskloadv8dfqi = 14490,
  CODE_FOR_maskloadv4dfqi = 14491,
  CODE_FOR_maskloadv2dfqi = 14492,
  CODE_FOR_maskloadv64qidi = 14493,
  CODE_FOR_maskloadv16qihi = 14494,
  CODE_FOR_maskloadv32qisi = 14495,
  CODE_FOR_maskloadv32hisi = 14496,
  CODE_FOR_maskloadv16hihi = 14497,
  CODE_FOR_maskloadv8hiqi = 14498,
  CODE_FOR_maskloadv32hfsi = 14499,
  CODE_FOR_maskloadv16hfhi = 14500,
  CODE_FOR_maskloadv8hfqi = 14501,
  CODE_FOR_maskloadv32bfsi = 14502,
  CODE_FOR_maskloadv16bfhi = 14503,
  CODE_FOR_maskloadv8bfqi = 14504,
  CODE_FOR_maskstorev4sfv4si = 14505,
  CODE_FOR_maskstorev2dfv2di = 14506,
  CODE_FOR_maskstorev4div4di = 14507,
  CODE_FOR_maskstorev2div2di = 14508,
  CODE_FOR_maskstorev8sfv8si = 14509,
  CODE_FOR_maskstorev4dfv4di = 14510,
  CODE_FOR_maskstorev8siv8si = 14511,
  CODE_FOR_maskstorev4siv4si = 14512,
  CODE_FOR_maskstorev16sihi = 14513,
  CODE_FOR_maskstorev8siqi = 14514,
  CODE_FOR_maskstorev4siqi = 14515,
  CODE_FOR_maskstorev8diqi = 14516,
  CODE_FOR_maskstorev4diqi = 14517,
  CODE_FOR_maskstorev2diqi = 14518,
  CODE_FOR_maskstorev16sfhi = 14519,
  CODE_FOR_maskstorev8sfqi = 14520,
  CODE_FOR_maskstorev4sfqi = 14521,
  CODE_FOR_maskstorev8dfqi = 14522,
  CODE_FOR_maskstorev4dfqi = 14523,
  CODE_FOR_maskstorev2dfqi = 14524,
  CODE_FOR_maskstorev64qidi = 14525,
  CODE_FOR_maskstorev16qihi = 14526,
  CODE_FOR_maskstorev32qisi = 14527,
  CODE_FOR_maskstorev32hisi = 14528,
  CODE_FOR_maskstorev16hihi = 14529,
  CODE_FOR_maskstorev8hiqi = 14530,
  CODE_FOR_maskstorev32hfsi = 14531,
  CODE_FOR_maskstorev16hfhi = 14532,
  CODE_FOR_maskstorev8hfqi = 14533,
  CODE_FOR_maskstorev32bfsi = 14534,
  CODE_FOR_maskstorev16bfhi = 14535,
  CODE_FOR_maskstorev8bfqi = 14536,
  CODE_FOR_cbranchv64qi4 = 14537,
  CODE_FOR_cbranchv32qi4 = 14538,
  CODE_FOR_cbranchv16qi4 = 14539,
  CODE_FOR_cbranchv32hi4 = 14540,
  CODE_FOR_cbranchv16hi4 = 14541,
  CODE_FOR_cbranchv8hi4 = 14542,
  CODE_FOR_cbranchv16si4 = 14543,
  CODE_FOR_cbranchv8si4 = 14544,
  CODE_FOR_cbranchv4si4 = 14545,
  CODE_FOR_cbranchv8di4 = 14546,
  CODE_FOR_cbranchv4di4 = 14547,
  CODE_FOR_cbranchv2di4 = 14548,
  CODE_FOR_vec_initv64qiqi = 14549,
  CODE_FOR_vec_initv32qiqi = 14550,
  CODE_FOR_vec_initv16qiqi = 14551,
  CODE_FOR_vec_initv32hihi = 14552,
  CODE_FOR_vec_initv16hihi = 14553,
  CODE_FOR_vec_initv8hihi = 14554,
  CODE_FOR_vec_initv16sisi = 14555,
  CODE_FOR_vec_initv8sisi = 14556,
  CODE_FOR_vec_initv4sisi = 14557,
  CODE_FOR_vec_initv8didi = 14558,
  CODE_FOR_vec_initv4didi = 14559,
  CODE_FOR_vec_initv2didi = 14560,
  CODE_FOR_vec_initv32hfhf = 14561,
  CODE_FOR_vec_initv16hfhf = 14562,
  CODE_FOR_vec_initv8hfhf = 14563,
  CODE_FOR_vec_initv32bfbf = 14564,
  CODE_FOR_vec_initv16bfbf = 14565,
  CODE_FOR_vec_initv8bfbf = 14566,
  CODE_FOR_vec_initv16sfsf = 14567,
  CODE_FOR_vec_initv8sfsf = 14568,
  CODE_FOR_vec_initv4sfsf = 14569,
  CODE_FOR_vec_initv8dfdf = 14570,
  CODE_FOR_vec_initv4dfdf = 14571,
  CODE_FOR_vec_initv2dfdf = 14572,
  CODE_FOR_vec_initv4titi = 14573,
  CODE_FOR_vec_initv2titi = 14574,
  CODE_FOR_vec_initv64qiv32qi = 14575,
  CODE_FOR_vec_initv32qiv16qi = 14576,
  CODE_FOR_vec_initv16qiv8qi = 14577,
  CODE_FOR_vec_initv32hiv16hi = 14578,
  CODE_FOR_vec_initv16hiv8hi = 14579,
  CODE_FOR_vec_initv8hiv4hi = 14580,
  CODE_FOR_vec_initv16siv8si = 14581,
  CODE_FOR_vec_initv8siv4si = 14582,
  CODE_FOR_vec_initv4siv2si = 14583,
  CODE_FOR_vec_initv8div4di = 14584,
  CODE_FOR_vec_initv4div2di = 14585,
  CODE_FOR_vec_initv32hfv16hf = 14586,
  CODE_FOR_vec_initv16hfv8hf = 14587,
  CODE_FOR_vec_initv8hfv4hf = 14588,
  CODE_FOR_vec_initv32bfv16bf = 14589,
  CODE_FOR_vec_initv16bfv8bf = 14590,
  CODE_FOR_vec_initv8bfv4bf = 14591,
  CODE_FOR_vec_initv16sfv8sf = 14592,
  CODE_FOR_vec_initv8sfv4sf = 14593,
  CODE_FOR_vec_initv4sfv2sf = 14594,
  CODE_FOR_vec_initv8dfv4df = 14595,
  CODE_FOR_vec_initv4dfv2df = 14596,
  CODE_FOR_vec_initv4tiv2ti = 14597,
  CODE_FOR_cond_ashlv32hi = 14598,
  CODE_FOR_cond_lshrv32hi = 14599,
  CODE_FOR_cond_ashrv32hi = 14600,
  CODE_FOR_cond_ashlv16hi = 14601,
  CODE_FOR_cond_lshrv16hi = 14602,
  CODE_FOR_cond_ashrv16hi = 14603,
  CODE_FOR_cond_ashlv8hi = 14604,
  CODE_FOR_cond_lshrv8hi = 14605,
  CODE_FOR_cond_ashrv8hi = 14606,
  CODE_FOR_cond_ashlv16si = 14607,
  CODE_FOR_cond_lshrv16si = 14608,
  CODE_FOR_cond_ashrv16si = 14609,
  CODE_FOR_cond_ashlv8si = 14610,
  CODE_FOR_cond_lshrv8si = 14611,
  CODE_FOR_cond_ashrv8si = 14612,
  CODE_FOR_cond_ashlv4si = 14613,
  CODE_FOR_cond_lshrv4si = 14614,
  CODE_FOR_cond_ashrv4si = 14615,
  CODE_FOR_cond_ashlv8di = 14616,
  CODE_FOR_cond_lshrv8di = 14617,
  CODE_FOR_cond_ashrv8di = 14618,
  CODE_FOR_cond_ashlv4di = 14619,
  CODE_FOR_cond_lshrv4di = 14620,
  CODE_FOR_cond_ashrv4di = 14621,
  CODE_FOR_cond_ashlv2di = 14622,
  CODE_FOR_cond_lshrv2di = 14623,
  CODE_FOR_cond_ashrv2di = 14624,
  CODE_FOR_vcvtps2ph_mask = 14625,
  CODE_FOR_vcvtps2ph = 14626,
  CODE_FOR_avx512f_vcvtps2ph512_mask_sae = 14627,
  CODE_FOR_avx2_gathersiv2di = 14628,
  CODE_FOR_avx2_gathersiv2df = 14629,
  CODE_FOR_avx2_gathersiv4di = 14630,
  CODE_FOR_avx2_gathersiv4df = 14631,
  CODE_FOR_avx2_gathersiv4si = 14632,
  CODE_FOR_avx2_gathersiv4sf = 14633,
  CODE_FOR_avx2_gathersiv8si = 14634,
  CODE_FOR_avx2_gathersiv8sf = 14635,
  CODE_FOR_avx2_gatherdiv2di = 14636,
  CODE_FOR_avx2_gatherdiv2df = 14637,
  CODE_FOR_avx2_gatherdiv4di = 14638,
  CODE_FOR_avx2_gatherdiv4df = 14639,
  CODE_FOR_avx2_gatherdiv4si = 14640,
  CODE_FOR_avx2_gatherdiv4sf = 14641,
  CODE_FOR_avx2_gatherdiv8si = 14642,
  CODE_FOR_avx2_gatherdiv8sf = 14643,
  CODE_FOR_avx512f_gathersiv16si = 14644,
  CODE_FOR_avx512f_gathersiv16sf = 14645,
  CODE_FOR_avx512f_gathersiv8di = 14646,
  CODE_FOR_avx512f_gathersiv8df = 14647,
  CODE_FOR_avx512vl_gathersiv8si = 14648,
  CODE_FOR_avx512vl_gathersiv8sf = 14649,
  CODE_FOR_avx512vl_gathersiv4di = 14650,
  CODE_FOR_avx512vl_gathersiv4df = 14651,
  CODE_FOR_avx512vl_gathersiv4si = 14652,
  CODE_FOR_avx512vl_gathersiv4sf = 14653,
  CODE_FOR_avx512vl_gathersiv2di = 14654,
  CODE_FOR_avx512vl_gathersiv2df = 14655,
  CODE_FOR_avx512f_gatherdiv16si = 14656,
  CODE_FOR_avx512f_gatherdiv16sf = 14657,
  CODE_FOR_avx512f_gatherdiv8di = 14658,
  CODE_FOR_avx512f_gatherdiv8df = 14659,
  CODE_FOR_avx512vl_gatherdiv8si = 14660,
  CODE_FOR_avx512vl_gatherdiv8sf = 14661,
  CODE_FOR_avx512vl_gatherdiv4di = 14662,
  CODE_FOR_avx512vl_gatherdiv4df = 14663,
  CODE_FOR_avx512vl_gatherdiv4si = 14664,
  CODE_FOR_avx512vl_gatherdiv4sf = 14665,
  CODE_FOR_avx512vl_gatherdiv2di = 14666,
  CODE_FOR_avx512vl_gatherdiv2df = 14667,
  CODE_FOR_avx512f_scattersiv16si = 14668,
  CODE_FOR_avx512f_scattersiv16sf = 14669,
  CODE_FOR_avx512f_scattersiv8di = 14670,
  CODE_FOR_avx512f_scattersiv8df = 14671,
  CODE_FOR_avx512vl_scattersiv8si = 14672,
  CODE_FOR_avx512vl_scattersiv8sf = 14673,
  CODE_FOR_avx512vl_scattersiv4di = 14674,
  CODE_FOR_avx512vl_scattersiv4df = 14675,
  CODE_FOR_avx512vl_scattersiv4si = 14676,
  CODE_FOR_avx512vl_scattersiv4sf = 14677,
  CODE_FOR_avx512vl_scattersiv2di = 14678,
  CODE_FOR_avx512vl_scattersiv2df = 14679,
  CODE_FOR_avx512f_scatterdiv16si = 14680,
  CODE_FOR_avx512f_scatterdiv16sf = 14681,
  CODE_FOR_avx512f_scatterdiv8di = 14682,
  CODE_FOR_avx512f_scatterdiv8df = 14683,
  CODE_FOR_avx512vl_scatterdiv8si = 14684,
  CODE_FOR_avx512vl_scatterdiv8sf = 14685,
  CODE_FOR_avx512vl_scatterdiv4di = 14686,
  CODE_FOR_avx512vl_scatterdiv4df = 14687,
  CODE_FOR_avx512vl_scatterdiv4si = 14688,
  CODE_FOR_avx512vl_scatterdiv4sf = 14689,
  CODE_FOR_avx512vl_scatterdiv2di = 14690,
  CODE_FOR_avx512vl_scatterdiv2df = 14691,
  CODE_FOR_avx512f_expandv16si_maskz = 14692,
  CODE_FOR_avx512f_expandv16sf_maskz = 14693,
  CODE_FOR_avx512f_expandv8di_maskz = 14694,
  CODE_FOR_avx512f_expandv8df_maskz = 14695,
  CODE_FOR_avx512vl_expandv8si_maskz = 14696,
  CODE_FOR_avx512vl_expandv8sf_maskz = 14697,
  CODE_FOR_avx512vl_expandv4di_maskz = 14698,
  CODE_FOR_avx512vl_expandv4df_maskz = 14699,
  CODE_FOR_avx512vl_expandv4si_maskz = 14700,
  CODE_FOR_avx512vl_expandv4sf_maskz = 14701,
  CODE_FOR_avx512vl_expandv2di_maskz = 14702,
  CODE_FOR_avx512vl_expandv2df_maskz = 14703,
  CODE_FOR_expandv64qi_maskz = 14704,
  CODE_FOR_expandv16qi_maskz = 14705,
  CODE_FOR_expandv32qi_maskz = 14706,
  CODE_FOR_expandv32hi_maskz = 14707,
  CODE_FOR_expandv16hi_maskz = 14708,
  CODE_FOR_expandv8hi_maskz = 14709,
  CODE_FOR_vpmadd52huqv8di_maskz = 14710,
  CODE_FOR_vpmadd52huqv4di_maskz = 14711,
  CODE_FOR_vpmadd52huqv2di_maskz = 14712,
  CODE_FOR_vpmadd52luqv8di_maskz = 14713,
  CODE_FOR_vpmadd52luqv4di_maskz = 14714,
  CODE_FOR_vpmadd52luqv2di_maskz = 14715,
  CODE_FOR_popcountv16si2 = 14716,
  CODE_FOR_popcountv8si2 = 14717,
  CODE_FOR_popcountv4si2 = 14718,
  CODE_FOR_popcountv8di2 = 14719,
  CODE_FOR_popcountv4di2 = 14720,
  CODE_FOR_popcountv2di2 = 14721,
  CODE_FOR_popcountv64qi2 = 14722,
  CODE_FOR_popcountv16qi2 = 14723,
  CODE_FOR_popcountv32qi2 = 14724,
  CODE_FOR_popcountv32hi2 = 14725,
  CODE_FOR_popcountv16hi2 = 14726,
  CODE_FOR_popcountv8hi2 = 14727,
  CODE_FOR_vpshrdv_v32hi_maskz = 14728,
  CODE_FOR_vpshrdv_v16si_maskz = 14729,
  CODE_FOR_vpshrdv_v8di_maskz = 14730,
  CODE_FOR_vpshrdv_v16hi_maskz = 14731,
  CODE_FOR_vpshrdv_v8si_maskz = 14732,
  CODE_FOR_vpshrdv_v4di_maskz = 14733,
  CODE_FOR_vpshrdv_v8hi_maskz = 14734,
  CODE_FOR_vpshrdv_v4si_maskz = 14735,
  CODE_FOR_vpshrdv_v2di_maskz = 14736,
  CODE_FOR_vpshldv_v32hi_maskz = 14737,
  CODE_FOR_vpshldv_v16si_maskz = 14738,
  CODE_FOR_vpshldv_v8di_maskz = 14739,
  CODE_FOR_vpshldv_v16hi_maskz = 14740,
  CODE_FOR_vpshldv_v8si_maskz = 14741,
  CODE_FOR_vpshldv_v4di_maskz = 14742,
  CODE_FOR_vpshldv_v8hi_maskz = 14743,
  CODE_FOR_vpshldv_v4si_maskz = 14744,
  CODE_FOR_vpshldv_v2di_maskz = 14745,
  CODE_FOR_usdot_prodv16siv64qi = 14746,
  CODE_FOR_usdot_prodv8siv32qi = 14747,
  CODE_FOR_usdot_prodv4siv16qi = 14748,
  CODE_FOR_vpdpbusd_v16si_maskz = 14749,
  CODE_FOR_vpdpbusd_v8si_maskz = 14750,
  CODE_FOR_vpdpbusd_v4si_maskz = 14751,
  CODE_FOR_vpdpbusds_v16si_maskz = 14752,
  CODE_FOR_vpdpbusds_v8si_maskz = 14753,
  CODE_FOR_vpdpbusds_v4si_maskz = 14754,
  CODE_FOR_vpdpwssd_v16si_maskz = 14755,
  CODE_FOR_vpdpwssd_v8si_maskz = 14756,
  CODE_FOR_vpdpwssd_v4si_maskz = 14757,
  CODE_FOR_vpdpwssds_v16si_maskz = 14758,
  CODE_FOR_vpdpwssds_v8si_maskz = 14759,
  CODE_FOR_vpdpwssds_v4si_maskz = 14760,
  CODE_FOR_movp2qi = 14761,
  CODE_FOR_movp2hi = 14762,
  CODE_FOR_avx512f_cvtne2ps2bf16_v32bf_maskz = 14763,
  CODE_FOR_avx512f_cvtne2ps2bf16_v16bf_maskz = 14764,
  CODE_FOR_avx512f_cvtne2ps2bf16_v8bf_maskz = 14765,
  CODE_FOR_truncv4sfv4bf2 = 14766,
  CODE_FOR_vcvtneps2bf16_v4sf = 14767,
  CODE_FOR_avx512f_cvtneps2bf16_v4sf_maskz = 14768,
  CODE_FOR_avx512f_cvtneps2bf16_v4sf_mask = 14769,
  CODE_FOR_avx512f_cvtneps2bf16_v16sf_maskz = 14770,
  CODE_FOR_avx512f_cvtneps2bf16_v8sf_maskz = 14771,
  CODE_FOR_truncv8sfv8bf2 = 14772,
  CODE_FOR_truncv16sfv16bf2 = 14773,
  CODE_FOR_extendv16bfv16sf2 = 14774,
  CODE_FOR_extendv8bfv8sf2 = 14775,
  CODE_FOR_extendv4bfv4sf2 = 14776,
  CODE_FOR_avx512f_dpbf16ps_v16sf_maskz = 14777,
  CODE_FOR_avx512f_dpbf16ps_v8sf_maskz = 14778,
  CODE_FOR_avx512f_dpbf16ps_v4sf_maskz = 14779,
  CODE_FOR_encodekey128u32 = 14780,
  CODE_FOR_encodekey256u32 = 14781,
  CODE_FOR_aesdecwide128klu8 = 14782,
  CODE_FOR_aesdecwide256klu8 = 14783,
  CODE_FOR_aesencwide128klu8 = 14784,
  CODE_FOR_aesencwide256klu8 = 14785,
  CODE_FOR_vec_duplicatev64qi = 14786,
  CODE_FOR_vec_duplicatev32qi = 14787,
  CODE_FOR_vec_duplicatev16qi = 14788,
  CODE_FOR_vec_duplicatev32hi = 14789,
  CODE_FOR_vec_duplicatev16hi = 14790,
  CODE_FOR_vec_duplicatev8hi = 14791,
  CODE_FOR_vec_duplicatev16si = 14792,
  CODE_FOR_vec_duplicatev8si = 14793,
  CODE_FOR_vec_duplicatev4si = 14794,
  CODE_FOR_vec_duplicatev8di = 14795,
  CODE_FOR_vec_duplicatev4di = 14796,
  CODE_FOR_vec_duplicatev2di = 14797,
  CODE_FOR_sdot_prodv16siv64qi = 14798,
  CODE_FOR_sdot_prodv8siv32qi = 14799,
  CODE_FOR_sdot_prodv4siv16qi = 14800,
  CODE_FOR_udot_prodv16siv64qi = 14801,
  CODE_FOR_udot_prodv8siv32qi = 14802,
  CODE_FOR_udot_prodv4siv16qi = 14803,
  CODE_FOR_vpdpbssd_v16si_maskz = 14804,
  CODE_FOR_vpdpbssds_v16si_maskz = 14805,
  CODE_FOR_vpdpbsud_v16si_maskz = 14806,
  CODE_FOR_vpdpbsuds_v16si_maskz = 14807,
  CODE_FOR_vpdpbuud_v16si_maskz = 14808,
  CODE_FOR_vpdpbuuds_v16si_maskz = 14809,
  CODE_FOR_vpdpbssd_v8si_maskz = 14810,
  CODE_FOR_vpdpbssds_v8si_maskz = 14811,
  CODE_FOR_vpdpbsud_v8si_maskz = 14812,
  CODE_FOR_vpdpbsuds_v8si_maskz = 14813,
  CODE_FOR_vpdpbuud_v8si_maskz = 14814,
  CODE_FOR_vpdpbuuds_v8si_maskz = 14815,
  CODE_FOR_vpdpbssd_v4si_maskz = 14816,
  CODE_FOR_vpdpbssds_v4si_maskz = 14817,
  CODE_FOR_vpdpbsud_v4si_maskz = 14818,
  CODE_FOR_vpdpbsuds_v4si_maskz = 14819,
  CODE_FOR_vpdpbuud_v4si_maskz = 14820,
  CODE_FOR_vpdpbuuds_v4si_maskz = 14821,
  CODE_FOR_vcvtbiasph2bf8v8hf = 14822,
  CODE_FOR_vcvtbiasph2bf8sv8hf = 14823,
  CODE_FOR_vcvtbiasph2hf8v8hf = 14824,
  CODE_FOR_vcvtbiasph2hf8sv8hf = 14825,
  CODE_FOR_vcvtbiasph2bf8v8hf_mask = 14826,
  CODE_FOR_vcvtbiasph2bf8sv8hf_mask = 14827,
  CODE_FOR_vcvtbiasph2hf8v8hf_mask = 14828,
  CODE_FOR_vcvtbiasph2hf8sv8hf_mask = 14829,
  CODE_FOR_vcvtph2bf8v8hf = 14830,
  CODE_FOR_vcvtph2bf8sv8hf = 14831,
  CODE_FOR_vcvtph2hf8v8hf = 14832,
  CODE_FOR_vcvtph2hf8sv8hf = 14833,
  CODE_FOR_vcvtph2bf8v8hf_mask = 14834,
  CODE_FOR_vcvtph2bf8sv8hf_mask = 14835,
  CODE_FOR_vcvtph2hf8v8hf_mask = 14836,
  CODE_FOR_vcvtph2hf8sv8hf_mask = 14837,
  CODE_FOR_usdot_prodv16siv32hi = 14838,
  CODE_FOR_usdot_prodv8siv16hi = 14839,
  CODE_FOR_usdot_prodv4siv8hi = 14840,
  CODE_FOR_udot_prodv16siv32hi = 14841,
  CODE_FOR_udot_prodv8siv16hi = 14842,
  CODE_FOR_udot_prodv4siv8hi = 14843,
  CODE_FOR_vpdpwusd_v16si_maskz = 14844,
  CODE_FOR_vpdpwusds_v16si_maskz = 14845,
  CODE_FOR_vpdpwsud_v16si_maskz = 14846,
  CODE_FOR_vpdpwsuds_v16si_maskz = 14847,
  CODE_FOR_vpdpwuud_v16si_maskz = 14848,
  CODE_FOR_vpdpwuuds_v16si_maskz = 14849,
  CODE_FOR_vpdpwusd_v8si_maskz = 14850,
  CODE_FOR_vpdpwusds_v8si_maskz = 14851,
  CODE_FOR_vpdpwsud_v8si_maskz = 14852,
  CODE_FOR_vpdpwsuds_v8si_maskz = 14853,
  CODE_FOR_vpdpwuud_v8si_maskz = 14854,
  CODE_FOR_vpdpwuuds_v8si_maskz = 14855,
  CODE_FOR_vpdpwusd_v4si_maskz = 14856,
  CODE_FOR_vpdpwusds_v4si_maskz = 14857,
  CODE_FOR_vpdpwsud_v4si_maskz = 14858,
  CODE_FOR_vpdpwsuds_v4si_maskz = 14859,
  CODE_FOR_vpdpwuud_v4si_maskz = 14860,
  CODE_FOR_vpdpwuuds_v4si_maskz = 14861,
  CODE_FOR_vdpphps_v16sf_maskz = 14862,
  CODE_FOR_vdpphps_v8sf_maskz = 14863,
  CODE_FOR_vdpphps_v4sf_maskz = 14864,
  CODE_FOR_smaxv32bf3 = 14865,
  CODE_FOR_sminv32bf3 = 14866,
  CODE_FOR_smaxv16bf3 = 14867,
  CODE_FOR_sminv16bf3 = 14868,
  CODE_FOR_smaxv8bf3 = 14869,
  CODE_FOR_sminv8bf3 = 14870,
  CODE_FOR_avx10_2_fmaddbf16_v32bf_maskz = 14871,
  CODE_FOR_avx10_2_fmaddbf16_v16bf_maskz = 14872,
  CODE_FOR_avx10_2_fmaddbf16_v8bf_maskz = 14873,
  CODE_FOR_avx10_2_fnmaddbf16_v32bf_maskz = 14874,
  CODE_FOR_avx10_2_fnmaddbf16_v16bf_maskz = 14875,
  CODE_FOR_avx10_2_fnmaddbf16_v8bf_maskz = 14876,
  CODE_FOR_avx10_2_fmsubbf16_v32bf_maskz = 14877,
  CODE_FOR_avx10_2_fmsubbf16_v16bf_maskz = 14878,
  CODE_FOR_avx10_2_fmsubbf16_v8bf_maskz = 14879,
  CODE_FOR_avx10_2_fnmsubbf16_v32bf_maskz = 14880,
  CODE_FOR_avx10_2_fnmsubbf16_v16bf_maskz = 14881,
  CODE_FOR_avx10_2_fnmsubbf16_v8bf_maskz = 14882,
  CODE_FOR_sse2_lfence = 14883,
  CODE_FOR_sse_sfence = 14884,
  CODE_FOR_sse2_mfence = 14885,
  CODE_FOR_mem_thread_fence = 14886,
  CODE_FOR_atomic_loadqi = 14887,
  CODE_FOR_atomic_loadhi = 14888,
  CODE_FOR_atomic_loadsi = 14889,
  CODE_FOR_atomic_loaddi = 14890,
  CODE_FOR_atomic_storeqi = 14891,
  CODE_FOR_atomic_storehi = 14892,
  CODE_FOR_atomic_storesi = 14893,
  CODE_FOR_atomic_storedi = 14894,
  CODE_FOR_atomic_compare_and_swapqi = 14895,
  CODE_FOR_atomic_compare_and_swaphi = 14896,
  CODE_FOR_atomic_compare_and_swapsi = 14897,
  CODE_FOR_atomic_compare_and_swapdi = 14898,
  CODE_FOR_atomic_compare_and_swapti = 14899,
  CODE_FOR_atomic_fetch_andqi = 14900,
  CODE_FOR_atomic_fetch_orqi = 14901,
  CODE_FOR_atomic_fetch_xorqi = 14902,
  CODE_FOR_atomic_fetch_andhi = 14903,
  CODE_FOR_atomic_fetch_orhi = 14904,
  CODE_FOR_atomic_fetch_xorhi = 14905,
  CODE_FOR_atomic_fetch_andsi = 14906,
  CODE_FOR_atomic_fetch_orsi = 14907,
  CODE_FOR_atomic_fetch_xorsi = 14908,
  CODE_FOR_atomic_and_fetchqi = 14909,
  CODE_FOR_atomic_or_fetchqi = 14910,
  CODE_FOR_atomic_xor_fetchqi = 14911,
  CODE_FOR_atomic_and_fetchhi = 14912,
  CODE_FOR_atomic_or_fetchhi = 14913,
  CODE_FOR_atomic_xor_fetchhi = 14914,
  CODE_FOR_atomic_and_fetchsi = 14915,
  CODE_FOR_atomic_or_fetchsi = 14916,
  CODE_FOR_atomic_xor_fetchsi = 14917,
  CODE_FOR_atomic_fetch_nandqi = 14918,
  CODE_FOR_atomic_fetch_nandhi = 14919,
  CODE_FOR_atomic_fetch_nandsi = 14920,
  CODE_FOR_atomic_nand_fetchqi = 14921,
  CODE_FOR_atomic_nand_fetchhi = 14922,
  CODE_FOR_atomic_nand_fetchsi = 14923,
  CODE_FOR_atomic_fetch_anddi = 14924,
  CODE_FOR_atomic_fetch_ordi = 14925,
  CODE_FOR_atomic_fetch_xordi = 14926,
  CODE_FOR_atomic_fetch_andti = 14927,
  CODE_FOR_atomic_fetch_orti = 14928,
  CODE_FOR_atomic_fetch_xorti = 14929,
  CODE_FOR_atomic_and_fetchdi = 14930,
  CODE_FOR_atomic_or_fetchdi = 14931,
  CODE_FOR_atomic_xor_fetchdi = 14932,
  CODE_FOR_atomic_and_fetchti = 14933,
  CODE_FOR_atomic_or_fetchti = 14934,
  CODE_FOR_atomic_xor_fetchti = 14935,
  CODE_FOR_atomic_fetch_nanddi = 14936,
  CODE_FOR_atomic_fetch_nandti = 14937,
  CODE_FOR_atomic_nand_fetchdi = 14938,
  CODE_FOR_atomic_nand_fetchti = 14939,
  CODE_FOR_atomic_bit_test_and_sethi = 14940,
  CODE_FOR_atomic_bit_test_and_setsi = 14941,
  CODE_FOR_atomic_bit_test_and_setdi = 14942,
  CODE_FOR_atomic_bit_test_and_complementhi = 14943,
  CODE_FOR_atomic_bit_test_and_complementsi = 14944,
  CODE_FOR_atomic_bit_test_and_complementdi = 14945,
  CODE_FOR_atomic_bit_test_and_resethi = 14946,
  CODE_FOR_atomic_bit_test_and_resetsi = 14947,
  CODE_FOR_atomic_bit_test_and_resetdi = 14948,
  CODE_FOR_atomic_add_fetch_cmp_0qi = 14949,
  CODE_FOR_atomic_sub_fetch_cmp_0qi = 14950,
  CODE_FOR_atomic_add_fetch_cmp_0hi = 14951,
  CODE_FOR_atomic_sub_fetch_cmp_0hi = 14952,
  CODE_FOR_atomic_add_fetch_cmp_0si = 14953,
  CODE_FOR_atomic_sub_fetch_cmp_0si = 14954,
  CODE_FOR_atomic_add_fetch_cmp_0di = 14955,
  CODE_FOR_atomic_sub_fetch_cmp_0di = 14956,
  CODE_FOR_atomic_and_fetch_cmp_0qi = 14957,
  CODE_FOR_atomic_or_fetch_cmp_0qi = 14958,
  CODE_FOR_atomic_xor_fetch_cmp_0qi = 14959,
  CODE_FOR_atomic_and_fetch_cmp_0hi = 14960,
  CODE_FOR_atomic_or_fetch_cmp_0hi = 14961,
  CODE_FOR_atomic_xor_fetch_cmp_0hi = 14962,
  CODE_FOR_atomic_and_fetch_cmp_0si = 14963,
  CODE_FOR_atomic_or_fetch_cmp_0si = 14964,
  CODE_FOR_atomic_xor_fetch_cmp_0si = 14965,
  CODE_FOR_atomic_and_fetch_cmp_0di = 14966,
  CODE_FOR_atomic_or_fetch_cmp_0di = 14967,
  CODE_FOR_atomic_xor_fetch_cmp_0di = 14968
};

const unsigned int NUM_INSN_CODES = 14969;
#endif /* GCC_INSN_CODES_H */
