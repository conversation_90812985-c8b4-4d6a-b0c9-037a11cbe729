.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_crl_dist_points" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_crl_dist_points \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_crl_dist_points(gnutls_x509_crt_t " crt ", gnutls_x509_subject_alt_name_t " type ", const void * " data_string ", unsigned int " reason_flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "gnutls_x509_subject_alt_name_t type" 12
is one of the gnutls_x509_subject_alt_name_t enumerations
.IP "const void * data_string" 12
The data to be set
.IP "unsigned int reason_flags" 12
revocation reasons
.SH "DESCRIPTION"
This function will set the CRL distribution points certificate extension.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
