# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


# The mappings in the non-hash portion of this file must be modified to get the
# correct values by adding the code point ordinal number to each one that is
# numeric.

# The name this table is to be known by, with the format of the mappings in
# the main body of the table, and what all code points missing from this file
# map to.
$Unicode::UCD::SwashInfo{'ToCf'}{'format'} = 'ax'; # mapped value in hex; some entries need adjustment
$Unicode::UCD::SwashInfo{'ToCf'}{'specials_name'} = 'Unicode::UCD::ToSpecCf'; # Name of hash of special mappings
$Unicode::UCD::SwashInfo{'ToCf'}{'missing'} = '0'; # code point maps to itself

# Some code points require special handling because their mappings are each to
# multiple code points.  These do not appear in the main body, but are defined
# in the hash below.

# Each key is the string of N bytes that together make up the UTF-8 encoding
# for the code point.  (i.e. the same as looking at the code point's UTF-8
# under "use bytes").  Each value is the UTF-8 of the translation, for speed.
%Unicode::UCD::ToSpecCf = (
"\xC3\x9F" => "\x{0073}\x{0073}",             # U+00DF => 0073 0073
"\xC4\xB0" => "\x{0069}\x{0307}",             # U+0130 => 0069 0307
"\xC5\x89" => "\x{02BC}\x{006E}",             # U+0149 => 02BC 006E
"\xC7\xB0" => "\x{006A}\x{030C}",             # U+01F0 => 006A 030C
"\xCE\x90" => "\x{03B9}\x{0308}\x{0301}",     # U+0390 => 03B9 0308 0301
"\xCE\xB0" => "\x{03C5}\x{0308}\x{0301}",     # U+03B0 => 03C5 0308 0301
"\xD6\x87" => "\x{0565}\x{0582}",             # U+0587 => 0565 0582
"\xE1\xBA\x96" => "\x{0068}\x{0331}",         # U+1E96 => 0068 0331
"\xE1\xBA\x97" => "\x{0074}\x{0308}",         # U+1E97 => 0074 0308
"\xE1\xBA\x98" => "\x{0077}\x{030A}",         # U+1E98 => 0077 030A
"\xE1\xBA\x99" => "\x{0079}\x{030A}",         # U+1E99 => 0079 030A
"\xE1\xBA\x9A" => "\x{0061}\x{02BE}",         # U+1E9A => 0061 02BE
"\xE1\xBA\x9E" => "\x{0073}\x{0073}",         # U+1E9E => 0073 0073
"\xE1\xBD\x90" => "\x{03C5}\x{0313}",         # U+1F50 => 03C5 0313
"\xE1\xBD\x92" => "\x{03C5}\x{0313}\x{0300}", # U+1F52 => 03C5 0313 0300
"\xE1\xBD\x94" => "\x{03C5}\x{0313}\x{0301}", # U+1F54 => 03C5 0313 0301
"\xE1\xBD\x96" => "\x{03C5}\x{0313}\x{0342}", # U+1F56 => 03C5 0313 0342
"\xE1\xBE\x80" => "\x{1F00}\x{03B9}",         # U+1F80 => 1F00 03B9
"\xE1\xBE\x81" => "\x{1F01}\x{03B9}",         # U+1F81 => 1F01 03B9
"\xE1\xBE\x82" => "\x{1F02}\x{03B9}",         # U+1F82 => 1F02 03B9
"\xE1\xBE\x83" => "\x{1F03}\x{03B9}",         # U+1F83 => 1F03 03B9
"\xE1\xBE\x84" => "\x{1F04}\x{03B9}",         # U+1F84 => 1F04 03B9
"\xE1\xBE\x85" => "\x{1F05}\x{03B9}",         # U+1F85 => 1F05 03B9
"\xE1\xBE\x86" => "\x{1F06}\x{03B9}",         # U+1F86 => 1F06 03B9
"\xE1\xBE\x87" => "\x{1F07}\x{03B9}",         # U+1F87 => 1F07 03B9
"\xE1\xBE\x88" => "\x{1F00}\x{03B9}",         # U+1F88 => 1F00 03B9
"\xE1\xBE\x89" => "\x{1F01}\x{03B9}",         # U+1F89 => 1F01 03B9
"\xE1\xBE\x8A" => "\x{1F02}\x{03B9}",         # U+1F8A => 1F02 03B9
"\xE1\xBE\x8B" => "\x{1F03}\x{03B9}",         # U+1F8B => 1F03 03B9
"\xE1\xBE\x8C" => "\x{1F04}\x{03B9}",         # U+1F8C => 1F04 03B9
"\xE1\xBE\x8D" => "\x{1F05}\x{03B9}",         # U+1F8D => 1F05 03B9
"\xE1\xBE\x8E" => "\x{1F06}\x{03B9}",         # U+1F8E => 1F06 03B9
"\xE1\xBE\x8F" => "\x{1F07}\x{03B9}",         # U+1F8F => 1F07 03B9
"\xE1\xBE\x90" => "\x{1F20}\x{03B9}",         # U+1F90 => 1F20 03B9
"\xE1\xBE\x91" => "\x{1F21}\x{03B9}",         # U+1F91 => 1F21 03B9
"\xE1\xBE\x92" => "\x{1F22}\x{03B9}",         # U+1F92 => 1F22 03B9
"\xE1\xBE\x93" => "\x{1F23}\x{03B9}",         # U+1F93 => 1F23 03B9
"\xE1\xBE\x94" => "\x{1F24}\x{03B9}",         # U+1F94 => 1F24 03B9
"\xE1\xBE\x95" => "\x{1F25}\x{03B9}",         # U+1F95 => 1F25 03B9
"\xE1\xBE\x96" => "\x{1F26}\x{03B9}",         # U+1F96 => 1F26 03B9
"\xE1\xBE\x97" => "\x{1F27}\x{03B9}",         # U+1F97 => 1F27 03B9
"\xE1\xBE\x98" => "\x{1F20}\x{03B9}",         # U+1F98 => 1F20 03B9
"\xE1\xBE\x99" => "\x{1F21}\x{03B9}",         # U+1F99 => 1F21 03B9
"\xE1\xBE\x9A" => "\x{1F22}\x{03B9}",         # U+1F9A => 1F22 03B9
"\xE1\xBE\x9B" => "\x{1F23}\x{03B9}",         # U+1F9B => 1F23 03B9
"\xE1\xBE\x9C" => "\x{1F24}\x{03B9}",         # U+1F9C => 1F24 03B9
"\xE1\xBE\x9D" => "\x{1F25}\x{03B9}",         # U+1F9D => 1F25 03B9
"\xE1\xBE\x9E" => "\x{1F26}\x{03B9}",         # U+1F9E => 1F26 03B9
"\xE1\xBE\x9F" => "\x{1F27}\x{03B9}",         # U+1F9F => 1F27 03B9
"\xE1\xBE\xA0" => "\x{1F60}\x{03B9}",         # U+1FA0 => 1F60 03B9
"\xE1\xBE\xA1" => "\x{1F61}\x{03B9}",         # U+1FA1 => 1F61 03B9
"\xE1\xBE\xA2" => "\x{1F62}\x{03B9}",         # U+1FA2 => 1F62 03B9
"\xE1\xBE\xA3" => "\x{1F63}\x{03B9}",         # U+1FA3 => 1F63 03B9
"\xE1\xBE\xA4" => "\x{1F64}\x{03B9}",         # U+1FA4 => 1F64 03B9
"\xE1\xBE\xA5" => "\x{1F65}\x{03B9}",         # U+1FA5 => 1F65 03B9
"\xE1\xBE\xA6" => "\x{1F66}\x{03B9}",         # U+1FA6 => 1F66 03B9
"\xE1\xBE\xA7" => "\x{1F67}\x{03B9}",         # U+1FA7 => 1F67 03B9
"\xE1\xBE\xA8" => "\x{1F60}\x{03B9}",         # U+1FA8 => 1F60 03B9
"\xE1\xBE\xA9" => "\x{1F61}\x{03B9}",         # U+1FA9 => 1F61 03B9
"\xE1\xBE\xAA" => "\x{1F62}\x{03B9}",         # U+1FAA => 1F62 03B9
"\xE1\xBE\xAB" => "\x{1F63}\x{03B9}",         # U+1FAB => 1F63 03B9
"\xE1\xBE\xAC" => "\x{1F64}\x{03B9}",         # U+1FAC => 1F64 03B9
"\xE1\xBE\xAD" => "\x{1F65}\x{03B9}",         # U+1FAD => 1F65 03B9
"\xE1\xBE\xAE" => "\x{1F66}\x{03B9}",         # U+1FAE => 1F66 03B9
"\xE1\xBE\xAF" => "\x{1F67}\x{03B9}",         # U+1FAF => 1F67 03B9
"\xE1\xBE\xB2" => "\x{1F70}\x{03B9}",         # U+1FB2 => 1F70 03B9
"\xE1\xBE\xB3" => "\x{03B1}\x{03B9}",         # U+1FB3 => 03B1 03B9
"\xE1\xBE\xB4" => "\x{03AC}\x{03B9}",         # U+1FB4 => 03AC 03B9
"\xE1\xBE\xB6" => "\x{03B1}\x{0342}",         # U+1FB6 => 03B1 0342
"\xE1\xBE\xB7" => "\x{03B1}\x{0342}\x{03B9}", # U+1FB7 => 03B1 0342 03B9
"\xE1\xBE\xBC" => "\x{03B1}\x{03B9}",         # U+1FBC => 03B1 03B9
"\xE1\xBF\x82" => "\x{1F74}\x{03B9}",         # U+1FC2 => 1F74 03B9
"\xE1\xBF\x83" => "\x{03B7}\x{03B9}",         # U+1FC3 => 03B7 03B9
"\xE1\xBF\x84" => "\x{03AE}\x{03B9}",         # U+1FC4 => 03AE 03B9
"\xE1\xBF\x86" => "\x{03B7}\x{0342}",         # U+1FC6 => 03B7 0342
"\xE1\xBF\x87" => "\x{03B7}\x{0342}\x{03B9}", # U+1FC7 => 03B7 0342 03B9
"\xE1\xBF\x8C" => "\x{03B7}\x{03B9}",         # U+1FCC => 03B7 03B9
"\xE1\xBF\x92" => "\x{03B9}\x{0308}\x{0300}", # U+1FD2 => 03B9 0308 0300
"\xE1\xBF\x93" => "\x{03B9}\x{0308}\x{0301}", # U+1FD3 => 03B9 0308 0301
"\xE1\xBF\x96" => "\x{03B9}\x{0342}",         # U+1FD6 => 03B9 0342
"\xE1\xBF\x97" => "\x{03B9}\x{0308}\x{0342}", # U+1FD7 => 03B9 0308 0342
"\xE1\xBF\xA2" => "\x{03C5}\x{0308}\x{0300}", # U+1FE2 => 03C5 0308 0300
"\xE1\xBF\xA3" => "\x{03C5}\x{0308}\x{0301}", # U+1FE3 => 03C5 0308 0301
"\xE1\xBF\xA4" => "\x{03C1}\x{0313}",         # U+1FE4 => 03C1 0313
"\xE1\xBF\xA6" => "\x{03C5}\x{0342}",         # U+1FE6 => 03C5 0342
"\xE1\xBF\xA7" => "\x{03C5}\x{0308}\x{0342}", # U+1FE7 => 03C5 0308 0342
"\xE1\xBF\xB2" => "\x{1F7C}\x{03B9}",         # U+1FF2 => 1F7C 03B9
"\xE1\xBF\xB3" => "\x{03C9}\x{03B9}",         # U+1FF3 => 03C9 03B9
"\xE1\xBF\xB4" => "\x{03CE}\x{03B9}",         # U+1FF4 => 03CE 03B9
"\xE1\xBF\xB6" => "\x{03C9}\x{0342}",         # U+1FF6 => 03C9 0342
"\xE1\xBF\xB7" => "\x{03C9}\x{0342}\x{03B9}", # U+1FF7 => 03C9 0342 03B9
"\xE1\xBF\xBC" => "\x{03C9}\x{03B9}",         # U+1FFC => 03C9 03B9
"\xEF\xAC\x80" => "\x{0066}\x{0066}",         # U+FB00 => 0066 0066
"\xEF\xAC\x81" => "\x{0066}\x{0069}",         # U+FB01 => 0066 0069
"\xEF\xAC\x82" => "\x{0066}\x{006C}",         # U+FB02 => 0066 006C
"\xEF\xAC\x83" => "\x{0066}\x{0066}\x{0069}", # U+FB03 => 0066 0066 0069
"\xEF\xAC\x84" => "\x{0066}\x{0066}\x{006C}", # U+FB04 => 0066 0066 006C
"\xEF\xAC\x85" => "\x{0073}\x{0074}",         # U+FB05 => 0073 0074
"\xEF\xAC\x86" => "\x{0073}\x{0074}",         # U+FB06 => 0073 0074
"\xEF\xAC\x93" => "\x{0574}\x{0576}",         # U+FB13 => 0574 0576
"\xEF\xAC\x94" => "\x{0574}\x{0565}",         # U+FB14 => 0574 0565
"\xEF\xAC\x95" => "\x{0574}\x{056B}",         # U+FB15 => 0574 056B
"\xEF\xAC\x96" => "\x{057E}\x{0576}",         # U+FB16 => 057E 0576
"\xEF\xAC\x97" => "\x{0574}\x{056D}",         # U+FB17 => 0574 056D
);

return <<'END';
41	5A	61
B5		3BC
C0	D6	E0
D8	DE	F8
100		101
102		103
104		105
106		107
108		109
10A		10B
10C		10D
10E		10F
110		111
112		113
114		115
116		117
118		119
11A		11B
11C		11D
11E		11F
120		121
122		123
124		125
126		127
128		129
12A		12B
12C		12D
12E		12F
132		133
134		135
136		137
139		13A
13B		13C
13D		13E
13F		140
141		142
143		144
145		146
147		148
14A		14B
14C		14D
14E		14F
150		151
152		153
154		155
156		157
158		159
15A		15B
15C		15D
15E		15F
160		161
162		163
164		165
166		167
168		169
16A		16B
16C		16D
16E		16F
170		171
172		173
174		175
176		177
178		FF
179		17A
17B		17C
17D		17E
17F		73
181		253
182		183
184		185
186		254
187		188
189	18A	256
18B		18C
18E		1DD
18F		259
190		25B
191		192
193		260
194		263
196		269
197		268
198		199
19C		26F
19D		272
19F		275
1A0		1A1
1A2		1A3
1A4		1A5
1A6		280
1A7		1A8
1A9		283
1AC		1AD
1AE		288
1AF		1B0
1B1	1B2	28A
1B3		1B4
1B5		1B6
1B7		292
1B8		1B9
1BC		1BD
1C4		1C6
1C5		1C6
1C7		1C9
1C8		1C9
1CA		1CC
1CB		1CC
1CD		1CE
1CF		1D0
1D1		1D2
1D3		1D4
1D5		1D6
1D7		1D8
1D9		1DA
1DB		1DC
1DE		1DF
1E0		1E1
1E2		1E3
1E4		1E5
1E6		1E7
1E8		1E9
1EA		1EB
1EC		1ED
1EE		1EF
1F1		1F3
1F2		1F3
1F4		1F5
1F6		195
1F7		1BF
1F8		1F9
1FA		1FB
1FC		1FD
1FE		1FF
200		201
202		203
204		205
206		207
208		209
20A		20B
20C		20D
20E		20F
210		211
212		213
214		215
216		217
218		219
21A		21B
21C		21D
21E		21F
220		19E
222		223
224		225
226		227
228		229
22A		22B
22C		22D
22E		22F
230		231
232		233
23A		2C65
23B		23C
23D		19A
23E		2C66
241		242
243		180
244		289
245		28C
246		247
248		249
24A		24B
24C		24D
24E		24F
345		3B9
370		371
372		373
376		377
37F		3F3
386		3AC
388	38A	3AD
38C		3CC
38E	38F	3CD
391	3A1	3B1
3A3	3AB	3C3
3C2		3C3
3CF		3D7
3D0		3B2
3D1		3B8
3D5		3C6
3D6		3C0
3D8		3D9
3DA		3DB
3DC		3DD
3DE		3DF
3E0		3E1
3E2		3E3
3E4		3E5
3E6		3E7
3E8		3E9
3EA		3EB
3EC		3ED
3EE		3EF
3F0		3BA
3F1		3C1
3F4		3B8
3F5		3B5
3F7		3F8
3F9		3F2
3FA		3FB
3FD	3FF	37B
400	40F	450
410	42F	430
460		461
462		463
464		465
466		467
468		469
46A		46B
46C		46D
46E		46F
470		471
472		473
474		475
476		477
478		479
47A		47B
47C		47D
47E		47F
480		481
48A		48B
48C		48D
48E		48F
490		491
492		493
494		495
496		497
498		499
49A		49B
49C		49D
49E		49F
4A0		4A1
4A2		4A3
4A4		4A5
4A6		4A7
4A8		4A9
4AA		4AB
4AC		4AD
4AE		4AF
4B0		4B1
4B2		4B3
4B4		4B5
4B6		4B7
4B8		4B9
4BA		4BB
4BC		4BD
4BE		4BF
4C0		4CF
4C1		4C2
4C3		4C4
4C5		4C6
4C7		4C8
4C9		4CA
4CB		4CC
4CD		4CE
4D0		4D1
4D2		4D3
4D4		4D5
4D6		4D7
4D8		4D9
4DA		4DB
4DC		4DD
4DE		4DF
4E0		4E1
4E2		4E3
4E4		4E5
4E6		4E7
4E8		4E9
4EA		4EB
4EC		4ED
4EE		4EF
4F0		4F1
4F2		4F3
4F4		4F5
4F6		4F7
4F8		4F9
4FA		4FB
4FC		4FD
4FE		4FF
500		501
502		503
504		505
506		507
508		509
50A		50B
50C		50D
50E		50F
510		511
512		513
514		515
516		517
518		519
51A		51B
51C		51D
51E		51F
520		521
522		523
524		525
526		527
528		529
52A		52B
52C		52D
52E		52F
531	556	561
10A0	10C5	2D00
10C7		2D27
10CD		2D2D
13F8	13FD	13F0
1C80		432
1C81		434
1C82		43E
1C83	1C84	441
1C85		442
1C86		44A
1C87		463
1C88		A64B
1C90	1CBA	10D0
1CBD	1CBF	10FD
1E00		1E01
1E02		1E03
1E04		1E05
1E06		1E07
1E08		1E09
1E0A		1E0B
1E0C		1E0D
1E0E		1E0F
1E10		1E11
1E12		1E13
1E14		1E15
1E16		1E17
1E18		1E19
1E1A		1E1B
1E1C		1E1D
1E1E		1E1F
1E20		1E21
1E22		1E23
1E24		1E25
1E26		1E27
1E28		1E29
1E2A		1E2B
1E2C		1E2D
1E2E		1E2F
1E30		1E31
1E32		1E33
1E34		1E35
1E36		1E37
1E38		1E39
1E3A		1E3B
1E3C		1E3D
1E3E		1E3F
1E40		1E41
1E42		1E43
1E44		1E45
1E46		1E47
1E48		1E49
1E4A		1E4B
1E4C		1E4D
1E4E		1E4F
1E50		1E51
1E52		1E53
1E54		1E55
1E56		1E57
1E58		1E59
1E5A		1E5B
1E5C		1E5D
1E5E		1E5F
1E60		1E61
1E62		1E63
1E64		1E65
1E66		1E67
1E68		1E69
1E6A		1E6B
1E6C		1E6D
1E6E		1E6F
1E70		1E71
1E72		1E73
1E74		1E75
1E76		1E77
1E78		1E79
1E7A		1E7B
1E7C		1E7D
1E7E		1E7F
1E80		1E81
1E82		1E83
1E84		1E85
1E86		1E87
1E88		1E89
1E8A		1E8B
1E8C		1E8D
1E8E		1E8F
1E90		1E91
1E92		1E93
1E94		1E95
1E9B		1E61
1E9E		DF
1EA0		1EA1
1EA2		1EA3
1EA4		1EA5
1EA6		1EA7
1EA8		1EA9
1EAA		1EAB
1EAC		1EAD
1EAE		1EAF
1EB0		1EB1
1EB2		1EB3
1EB4		1EB5
1EB6		1EB7
1EB8		1EB9
1EBA		1EBB
1EBC		1EBD
1EBE		1EBF
1EC0		1EC1
1EC2		1EC3
1EC4		1EC5
1EC6		1EC7
1EC8		1EC9
1ECA		1ECB
1ECC		1ECD
1ECE		1ECF
1ED0		1ED1
1ED2		1ED3
1ED4		1ED5
1ED6		1ED7
1ED8		1ED9
1EDA		1EDB
1EDC		1EDD
1EDE		1EDF
1EE0		1EE1
1EE2		1EE3
1EE4		1EE5
1EE6		1EE7
1EE8		1EE9
1EEA		1EEB
1EEC		1EED
1EEE		1EEF
1EF0		1EF1
1EF2		1EF3
1EF4		1EF5
1EF6		1EF7
1EF8		1EF9
1EFA		1EFB
1EFC		1EFD
1EFE		1EFF
1F08	1F0F	1F00
1F18	1F1D	1F10
1F28	1F2F	1F20
1F38	1F3F	1F30
1F48	1F4D	1F40
1F59		1F51
1F5B		1F53
1F5D		1F55
1F5F		1F57
1F68	1F6F	1F60
1F88	1F8F	1F80
1F98	1F9F	1F90
1FA8	1FAF	1FA0
1FB8	1FB9	1FB0
1FBA	1FBB	1F70
1FBC		1FB3
1FBE		3B9
1FC8	1FCB	1F72
1FCC		1FC3
1FD8	1FD9	1FD0
1FDA	1FDB	1F76
1FE8	1FE9	1FE0
1FEA	1FEB	1F7A
1FEC		1FE5
1FF8	1FF9	1F78
1FFA	1FFB	1F7C
1FFC		1FF3
2126		3C9
212A		6B
212B		E5
2132		214E
2160	216F	2170
2183		2184
24B6	24CF	24D0
2C00	2C2F	2C30
2C60		2C61
2C62		26B
2C63		1D7D
2C64		27D
2C67		2C68
2C69		2C6A
2C6B		2C6C
2C6D		251
2C6E		271
2C6F		250
2C70		252
2C72		2C73
2C75		2C76
2C7E	2C7F	23F
2C80		2C81
2C82		2C83
2C84		2C85
2C86		2C87
2C88		2C89
2C8A		2C8B
2C8C		2C8D
2C8E		2C8F
2C90		2C91
2C92		2C93
2C94		2C95
2C96		2C97
2C98		2C99
2C9A		2C9B
2C9C		2C9D
2C9E		2C9F
2CA0		2CA1
2CA2		2CA3
2CA4		2CA5
2CA6		2CA7
2CA8		2CA9
2CAA		2CAB
2CAC		2CAD
2CAE		2CAF
2CB0		2CB1
2CB2		2CB3
2CB4		2CB5
2CB6		2CB7
2CB8		2CB9
2CBA		2CBB
2CBC		2CBD
2CBE		2CBF
2CC0		2CC1
2CC2		2CC3
2CC4		2CC5
2CC6		2CC7
2CC8		2CC9
2CCA		2CCB
2CCC		2CCD
2CCE		2CCF
2CD0		2CD1
2CD2		2CD3
2CD4		2CD5
2CD6		2CD7
2CD8		2CD9
2CDA		2CDB
2CDC		2CDD
2CDE		2CDF
2CE0		2CE1
2CE2		2CE3
2CEB		2CEC
2CED		2CEE
2CF2		2CF3
A640		A641
A642		A643
A644		A645
A646		A647
A648		A649
A64A		A64B
A64C		A64D
A64E		A64F
A650		A651
A652		A653
A654		A655
A656		A657
A658		A659
A65A		A65B
A65C		A65D
A65E		A65F
A660		A661
A662		A663
A664		A665
A666		A667
A668		A669
A66A		A66B
A66C		A66D
A680		A681
A682		A683
A684		A685
A686		A687
A688		A689
A68A		A68B
A68C		A68D
A68E		A68F
A690		A691
A692		A693
A694		A695
A696		A697
A698		A699
A69A		A69B
A722		A723
A724		A725
A726		A727
A728		A729
A72A		A72B
A72C		A72D
A72E		A72F
A732		A733
A734		A735
A736		A737
A738		A739
A73A		A73B
A73C		A73D
A73E		A73F
A740		A741
A742		A743
A744		A745
A746		A747
A748		A749
A74A		A74B
A74C		A74D
A74E		A74F
A750		A751
A752		A753
A754		A755
A756		A757
A758		A759
A75A		A75B
A75C		A75D
A75E		A75F
A760		A761
A762		A763
A764		A765
A766		A767
A768		A769
A76A		A76B
A76C		A76D
A76E		A76F
A779		A77A
A77B		A77C
A77D		1D79
A77E		A77F
A780		A781
A782		A783
A784		A785
A786		A787
A78B		A78C
A78D		265
A790		A791
A792		A793
A796		A797
A798		A799
A79A		A79B
A79C		A79D
A79E		A79F
A7A0		A7A1
A7A2		A7A3
A7A4		A7A5
A7A6		A7A7
A7A8		A7A9
A7AA		266
A7AB		25C
A7AC		261
A7AD		26C
A7AE		26A
A7B0		29E
A7B1		287
A7B2		29D
A7B3		AB53
A7B4		A7B5
A7B6		A7B7
A7B8		A7B9
A7BA		A7BB
A7BC		A7BD
A7BE		A7BF
A7C0		A7C1
A7C2		A7C3
A7C4		A794
A7C5		282
A7C6		1D8E
A7C7		A7C8
A7C9		A7CA
A7D0		A7D1
A7D6		A7D7
A7D8		A7D9
A7F5		A7F6
AB70	ABBF	13A0
FF21	FF3A	FF41
10400	10427	10428
104B0	104D3	104D8
10570	1057A	10597
1057C	1058A	105A3
1058C	10592	105B3
10594	10595	105BB
10C80	10CB2	10CC0
118A0	118BF	118C0
16E40	16E5F	16E60
1E900	1E921	1E922
END
