.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_priority_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_priority_init \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_priority_init(gnutls_priority_t * " priority_cache ", const char * " priorities ", const char ** " err_pos ");"
.SH ARGUMENTS
.IP "gnutls_priority_t * priority_cache" 12
is a \fBgnutls_priority_t\fP type.
.IP "const char * priorities" 12
is a string describing priorities (may be \fBNULL\fP)
.IP "const char ** err_pos" 12
In case of an error this will have the position in the string the error occurred
.SH "DESCRIPTION"
For applications that do not modify their crypto settings per release, consider
using \fBgnutls_priority_init2()\fP with \fBGNUTLS_PRIORITY_INIT_DEF_APPEND\fP flag
instead. We suggest to use centralized crypto settings handled by the GnuTLS
library, and applications modifying the default settings to their needs.

This function is identical to \fBgnutls_priority_init2()\fP with zero
flags.

A \fBNULL\fP  \fIpriorities\fP string indicates the default priorities to be
used (this is available since GnuTLS 3.3.0).
.SH "RETURNS"
On syntax error \fBGNUTLS_E_INVALID_REQUEST\fP is returned,
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
