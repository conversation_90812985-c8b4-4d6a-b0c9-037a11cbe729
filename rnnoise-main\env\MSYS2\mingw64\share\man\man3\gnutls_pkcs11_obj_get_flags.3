.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_get_flags" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_get_flags \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_obj_get_flags(gnutls_pkcs11_obj_t " obj ", unsigned int * " oflags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_t obj" 12
The pkcs11 object
.IP "unsigned int * oflags" 12
Will hold the output flags
.SH "DESCRIPTION"
This function will return the flags of the object.
The  \fIoflags\fP will be flags from \fBgnutls_pkcs11_obj_flags\fP. That is,
the \fBGNUTLS_PKCS11_OBJ_FLAG_MARK_\fP* flags.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.3.7
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
