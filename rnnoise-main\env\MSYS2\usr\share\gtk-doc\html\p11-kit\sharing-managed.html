<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Managed modules: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="sharing.html" title="Sharing PKCS#11 modules">
<link rel="prev" href="sharing.html" title="Sharing PKCS#11 modules">
<link rel="next" href="sharing.html" title="Proxy Module">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="sharing.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="sharing.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="sharing.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="sharing-managed"></a>Managed modules</h2></div></div></div>
<p><code class="literal">p11-kit</code> wraps PKCS#11 modules to manage
		them and customize their functionality so that they are able
		to be shared between multiple callers in the same process.</p>
<p>Each caller that uses the
		<a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a>
		or <a class="link" href="p11-kit-Modules.html#p11-kit-module-load" title="p11_kit_module_load ()"><code class="function">p11_kit_module_load()</code></a>
		function gets independent wrapped PKCS#11 module(s). This is unless a caller
		or module configuration specifies that a module should be used in an
		unmanaged fashion.</p>
<p>When modules are managed, the following aspects are wrapped and
		coordinated:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
<p>Calls to <code class="literal">C_Initialize</code> and
			<code class="literal">C_Finalize</code> can be called by multiple
			callers.</p>
<p>The first time that the managed module
			<code class="literal">C_Initialize</code> is called, the PKCS#11 module's actual
			<code class="literal">C_Initialize</code> function is called. Subsequent calls by
			other callers will cause <code class="literal">p11-kit</code> to increment an
			internal initialization count, rather than calling
			<code class="literal">C_Initialize</code> again.</p>
<p>Multiple callers can call the managed
			<code class="literal">C_Initialize</code> function concurrently from different
			threads and <code class="literal">p11-kit</code> will guarantee that this managed
			in a thread-safe manner.</p>
</li>
<li class="listitem">
<p>When the managed module <code class="literal">C_Finalize</code> is used
			to finalize a module, each time it is called it decrements the internal
			initialization count for that module. When the internal initialization
			count reaches zero, the module's actual <code class="literal">C_Finalize</code>
			function is called.</p>
<p>Multiple callers can call the managed <code class="literal">C_Finalize</code>
			function concurrently from different threads and <code class="literal">p11-kit</code>
			will guarantee that this managed in a thread-safe manner.</p>
</li>
<li class="listitem"><p>Call to <code class="literal">C_CloseAllSessions</code> only close the
			sessions that the caller of the managed module has opened. This allows the
			<code class="literal">C_CloseAllSessions</code> function to be used without closing
			sessions for other callers of the same PKCS#11 module.</p></li>
<li class="listitem"><p>Managed modules have ability to log PKCS#11 method calls for debugging
			purposes. See the <a class="link" href="pkcs11-conf.html#option-log-calls"><code class="literal">log-calls = yes</code></a>
			module configuration option.</p></li>
<li class="listitem"><p>Managed modules have the ability to be remoted to another machine or
			isolated in their own process.
			See the <a class="link" href="pkcs11-conf.html#option-remote"><code class="literal">remote = ...</code></a>
			module configuration option.</p></li>
</ul></div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>