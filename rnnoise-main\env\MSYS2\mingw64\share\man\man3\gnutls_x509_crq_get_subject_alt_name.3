.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_subject_alt_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_subject_alt_name \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_subject_alt_name(gnutls_x509_crq_t " crq ", unsigned int " seq ", void * " ret ", size_t * " ret_size ", unsigned int * " ret_type ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "unsigned int seq" 12
specifies the sequence number of the alt name, 0 for the
first one, 1 for the second etc.
.IP "void * ret" 12
is the place where the alternative name will be copied to
.IP "size_t * ret_size" 12
holds the size of ret.
.IP "unsigned int * ret_type" 12
holds the \fBgnutls_x509_subject_alt_name_t\fP name type
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical
(may be null)
.SH "DESCRIPTION"
This function will return the alternative names, contained in the
given certificate.  It is the same as
\fBgnutls_x509_crq_get_subject_alt_name()\fP except for the fact that it
will return the type of the alternative name in  \fIret_type\fP even if
the function fails for some reason (i.e.  the buffer provided is
not enough).
.SH "RETURNS"
the alternative subject name type on success, one of the
enumerated \fBgnutls_x509_subject_alt_name_t\fP.  It will return
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if  \fIret_size\fP is not large enough to
hold the value.  In that case  \fIret_size\fP will be updated with the
required size.  If the certificate request does not have an
Alternative name with the specified sequence number then
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
