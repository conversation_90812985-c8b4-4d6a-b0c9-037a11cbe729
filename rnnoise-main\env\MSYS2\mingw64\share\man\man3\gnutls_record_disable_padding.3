.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_disable_padding" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_disable_padding \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_record_disable_padding(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
Used to disabled padding in TLS 1.0 and above.  Normally you do not
need to use this function, but there are buggy clients that
complain if a server pads the encrypted data.  This of course will
disable protection against statistical attacks on the data.

This function is defunct since 3.1.7. Random padding is disabled
by default unless requested using \fBgnutls_record_send_range()\fP.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
