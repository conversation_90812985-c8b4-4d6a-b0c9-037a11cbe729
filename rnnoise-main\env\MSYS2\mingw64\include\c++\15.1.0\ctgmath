// <ctgmath> -*- C++ -*-

// Copyright (C) 2007-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/ctgmath
 *  This is a Standard C++ Library header.
 */

#ifndef _GLIBCXX_CTGMATH
#define _GLIBCXX_CTGMATH 1

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#if __cplusplus < 201103L
#  include <bits/c++0x_warning.h>
#else
#  include <cmath>
extern "C++" {
#  include <complex>
}
#  if __cplusplus >= 202002L && ! _GLIBCXX_USE_DEPRECATED
#    error "<ctgmath> is not a standard header in C++20, use <complex> or <cmath> instead"
#  elif __cplusplus >= 201703L && defined __DEPRECATED
#    pragma GCC diagnostic push
#    pragma GCC diagnostic ignored "-Wc++23-extensions"
#    warning "<ctgmath> is deprecated in C++17, use <complex> or <cmath> instead"
#    pragma GCC diagnostic pop
#  endif
#endif

#endif
