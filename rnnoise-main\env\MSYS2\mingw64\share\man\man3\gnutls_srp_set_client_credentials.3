.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srp_set_client_credentials" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srp_set_client_credentials \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srp_set_client_credentials(gnutls_srp_client_credentials_t " res ", const char * " username ", const char * " password ");"
.SH ARGUMENTS
.IP "gnutls_srp_client_credentials_t res" 12
is a \fBgnutls_srp_client_credentials_t\fP type.
.IP "const char * username" 12
is the user's userid
.IP "const char * password" 12
is the user's password
.SH "DESCRIPTION"
This function sets the username and password, in a
\fBgnutls_srp_client_credentials_t\fP type.  Those will be used in
SRP authentication.   \fIusername\fP should be an ASCII string or UTF\-8
string. In case of a UTF\-8 string it is recommended to be following
the PRECIS framework for usernames (rfc8265). The password can
be in ASCII format, or normalized using \fBgnutls_utf8_password_normalize()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, or an
error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
