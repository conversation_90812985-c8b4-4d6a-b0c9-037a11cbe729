.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_set_extension_by_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_set_extension_by_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_set_extension_by_oid(gnutls_x509_crq_t " crq ", const char * " oid ", const void * " buf ", size_t " sizeof_buf ", unsigned int " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
a certificate of type \fBgnutls_x509_crq_t\fP
.IP "const char * oid" 12
holds an Object Identifier in null terminated string
.IP "const void * buf" 12
a pointer to a DER encoded data
.IP "size_t sizeof_buf" 12
holds the size of  \fIbuf\fP 
.IP "unsigned int critical" 12
should be non\-zero if the extension is to be marked as critical
.SH "DESCRIPTION"
This function will set an the extension, by the specified OID, in
the certificate request.  The extension data should be binary data DER
encoded.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
