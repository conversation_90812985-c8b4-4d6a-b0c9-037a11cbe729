.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_set_server_dh_params" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_set_server_dh_params \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_psk_set_server_dh_params(gnutls_psk_server_credentials_t " res ", gnutls_dh_params_t " dh_params ");"
.SH ARGUMENTS
.IP "gnutls_psk_server_credentials_t res" 12
is a gnutls_psk_server_credentials_t type
.IP "gnutls_dh_params_t dh_params" 12
is a structure that holds Diffie\-Hellman parameters.
.SH "DESCRIPTION"
This function will set the Diffie\-Hellman parameters for an
anonymous server to use. These parameters will be used in
Diffie\-Hellman exchange with PSK cipher suites.
.SH "DEPRECATED"
This function is unnecessary and discouraged on GnuTLS 3.6.0
or later. Since 3.6.0, DH parameters are negotiated
following RFC7919.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
