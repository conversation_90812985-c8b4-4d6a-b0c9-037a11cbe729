# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!


# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


# This file is for the use of Unicode::UCD

# Highest legal Unicode code point
$Unicode::UCD::MAX_UNICODE_CODEPOINT = 0x10FFFF;

# Hangul syllables
$Unicode::UCD::HANGUL_BEGIN = 0xAC00;
$Unicode::UCD::HANGUL_COUNT = 11172;

# Maps Unicode (not Perl single-form extensions) property names in loose
# standard form to their corresponding standard names
%Unicode::UCD::loose_property_name_of = (
'age' => 'age',
'ahex' => 'ahex',
'alpha' => 'alpha',
'alphabetic' => 'alpha',
'asciihexdigit' => 'ahex',
'bc' => 'bc',
'bidic' => 'bidic',
'bidiclass' => 'bc',
'bidicontrol' => 'bidic',
'bidim' => 'bidim',
'bidimirrored' => 'bidim',
'bidipairedbrackettype' => 'bpt',
'blk' => 'blk',
'block' => 'blk',
'bpt' => 'bpt',
'canonicalcombiningclass' => 'ccc',
'cased' => 'cased',
'caseignorable' => 'ci',
'category' => 'gc',
'ccc' => 'ccc',
'ce' => 'ce',
'changeswhencasefolded' => 'cwcf',
'changeswhencasemapped' => 'cwcm',
'changeswhenlowercased' => 'cwl',
'changeswhennfkccasefolded' => 'cwkcf',
'changeswhentitlecased' => 'cwt',
'changeswhenuppercased' => 'cwu',
'ci' => 'ci',
'compex' => 'compex',
'compositionexclusion' => 'ce',
'cwcf' => 'cwcf',
'cwcm' => 'cwcm',
'cwkcf' => 'cwkcf',
'cwl' => 'cwl',
'cwt' => 'cwt',
'cwu' => 'cwu',
'dash' => 'dash',
'decompositiontype' => 'dt',
'defaultignorablecodepoint' => 'di',
'dep' => 'dep',
'deprecated' => 'dep',
'di' => 'di',
'dia' => 'dia',
'diacritic' => 'dia',
'dt' => 'dt',
'ea' => 'ea',
'eastasianwidth' => 'ea',
'ebase' => 'ebase',
'ecomp' => 'ecomp',
'emod' => 'emod',
'emoji' => 'emoji',
'emojicomponent' => 'ecomp',
'emojimodifier' => 'emod',
'emojimodifierbase' => 'ebase',
'emojipresentation' => 'epres',
'epres' => 'epres',
'ext' => 'ext',
'extendedpictographic' => 'extpict',
'extender' => 'ext',
'extpict' => 'extpict',
'fullcompositionexclusion' => 'compex',
'gc' => 'gc',
'gcb' => 'gcb',
'generalcategory' => 'gc',
'graphemebase' => 'grbase',
'graphemeclusterbreak' => 'gcb',
'graphemeextend' => 'grext',
'grbase' => 'grbase',
'grext' => 'grext',
'hangulsyllabletype' => 'hst',
'hex' => 'hex',
'hexdigit' => 'hex',
'hst' => 'hst',
'hyphen' => 'hyphen',
'idc' => 'idc',
'idcontinue' => 'idc',
'identifierstatus' => 'identifierstatus',
'identifiertype' => 'identifiertype',
'ideo' => 'ideo',
'ideographic' => 'ideo',
'ids' => 'ids',
'idsb' => 'idsb',
'idsbinaryoperator' => 'idsb',
'idst' => 'idst',
'idstart' => 'ids',
'idstrinaryoperator' => 'idst',
'in' => 'in',
'indicpositionalcategory' => 'inpc',
'indicsyllabiccategory' => 'insc',
'inpc' => 'inpc',
'insc' => 'insc',
'jg' => 'jg',
'joinc' => 'joinc',
'joincontrol' => 'joinc',
'joininggroup' => 'jg',
'joiningtype' => 'jt',
'jt' => 'jt',
'lb' => 'lb',
'linebreak' => 'lb',
'loe' => 'loe',
'logicalorderexception' => 'loe',
'lower' => 'lower',
'lowercase' => 'lower',
'math' => 'math',
'nchar' => 'nchar',
'nfcqc' => 'nfcqc',
'nfcquickcheck' => 'nfcqc',
'nfdqc' => 'nfdqc',
'nfdquickcheck' => 'nfdqc',
'nfkcqc' => 'nfkcqc',
'nfkcquickcheck' => 'nfkcqc',
'nfkdqc' => 'nfkdqc',
'nfkdquickcheck' => 'nfkdqc',
'noncharactercodepoint' => 'nchar',
'nt' => 'nt',
'numerictype' => 'nt',
'numericvalue' => 'nv',
'nv' => 'nv',
'patsyn' => 'patsyn',
'patternsyntax' => 'patsyn',
'patternwhitespace' => 'patws',
'patws' => 'patws',
'pcm' => 'pcm',
'prependedconcatenationmark' => 'pcm',
'presentin' => 'in',
'qmark' => 'qmark',
'quotationmark' => 'qmark',
'radical' => 'radical',
'regionalindicator' => 'ri',
'ri' => 'ri',
'sb' => 'sb',
'sc' => 'sc',
'script' => 'sc',
'scriptextensions' => 'scx',
'scx' => 'scx',
'sd' => 'sd',
'sentencebreak' => 'sb',
'sentenceterminal' => 'sterm',
'softdotted' => 'sd',
'space' => 'wspace',
'sterm' => 'sterm',
'term' => 'term',
'terminalpunctuation' => 'term',
'uideo' => 'uideo',
'unifiedideograph' => 'uideo',
'upper' => 'upper',
'uppercase' => 'upper',
'variationselector' => 'vs',
'verticalorientation' => 'vo',
'vo' => 'vo',
'vs' => 'vs',
'wb' => 'wb',
'whitespace' => 'wspace',
'wordbreak' => 'wb',
'wspace' => 'wspace',
'xidc' => 'xidc',
'xidcontinue' => 'xidc',
'xids' => 'xids',
'xidstart' => 'xids',
);

# Same, but strict names
%Unicode::UCD::strict_property_name_of = (
'_perlgcb' => 'gcb',
'_perlsb' => 'sb',
'_perlscx' => '_perlscx',
'_perlwb' => 'wb',
);

# Gives the definitions (in the form of inversion lists) for those properties
# whose definitions aren't kept in files
@Unicode::UCD::inline_definitions = (

'V0',
'V1
0',
'V2
0
1114112',
'V4
9
14
32
33',
'V6
10
14
133
134
8232
8234',
'V6
48
58
65
91
97
123',
'V4
65
91
97
123',
'V4
9
10
32
33',
'V4
0
32
127
128',
'V2
48
58',
'V2
33
127',
'V2
97
123',
'V2
32
127',
'V2
65
91',
'V2
55296
57344',
'V2
12334
12336',
'V2
119149
119150',
'V4
7674
7675
12330
12331',
'V6
861
863
864
866
7629
7630',
'V2
837
838',
'V2
12441
12443',
'V2
7630
7631',
'V6
801
803
807
809
7632
7633',
'V2
94192
94194',
'V2
1456
1457',
'V2
1457
1458',
'V2
1458
1459',
'V2
1459
1460',
'V2
1460
1461',
'V2
1461
1462',
'V2
1462
1463',
'V2
1463
1464',
'V4
1464
1465
1479
1480',
'V2
1465
1467',
'V2
1467
1468',
'V2
1468
1469',
'V2
1469
1470',
'V2
1471
1472',
'V2
1473
1474',
'V2
1474
1475',
'V2
64286
64287',
'V4
1611
1612
2288
2289',
'V4
1612
1613
2289
2290',
'V4
1613
1614
2290
2291',
'V4
1560
1561
1614
1615',
'V4
1561
1562
1615
1616',
'V4
1562
1563
1616
1617',
'V2
1617
1618',
'V2
1618
1619',
'V2
1648
1649',
'V2
1809
1810',
'V2
3157
3158',
'V2
3158
3159',
'V2
3640
3642',
'V2
3656
3660',
'V2
3768
3770',
'V2
3784
3788',
'V2
3953
3954',
'V6
3954
3955
3962
3966
3968
3969',
'V2
3956
3957',
'V6
48
58
65
71
97
103',
'V2
127995
128000',
'V3
0
127995
128000',
'V4
4352
4448
43360
43389',
'V4
4520
4608
55243
55292',
'V4
4448
4520
55216
55239',
'V2
13
14',
'V2
10
11',
'V2
127462
127488',
'V2
8205
8206',
'V4
12272
12274
12276
12284',
'V5
0
12272
12274
12276
12284',
'V2
12274
12276',
'V3
0
12274
12276',
'V2
43455
43456',
'V4
4156
4157
71454
71455',
'V2
6973
6974',
'V2
8204
8205',
'V6
6512
6517
43712
43713
43714
43715',
'V2
69759
69760',
'V4
3660
3661
6093
6094',
'V2
2947
2948',
'V2
6089
6091',
'V2
69714
69734',
'V2
3976
3981',
'V2
6092
6093',
'V2
6746
6747',
'V2
8204
8206',
'V3
0
8204
8206',
'V3
0
127462
127488',
'V6
11904
11930
11931
12020
12032
12246',
'V4
133
134
8232
8234',
'V2
34
35',
'V6
11
13
133
134
8232
8234',
'V2
39
40',
'V4
8364
8365
65532
65533',
'V2
8378
8379',
'V4
1564
1565
8294
8298',
'V2
13055
13056',
'V6
9
10
11
12
31
32',
'V2
8296
8297',
'V2
8234
8235',
'V2
8294
8295',
'V2
8237
8238',
'V2
8236
8237',
'V2
8297
8298',
'V2
8235
8236',
'V2
8295
8296',
'V2
8238
8239',
'V2
65024
65040',
'V2
19968
40960',
'V2
12272
12288',
'V2
3712
3840',
'V2
92736
92784',
'V2
1984
2048',
'V2
9280
9312',
'V2
57344
63744',
'V2
42240
42560',
'V2
71424
71504',
'V2
43520
43616',
'V2
4352
4608',
'V2
73472
73568',
'V2
42192
42240',
'V2
93952
94112',
'V2
71168
71264',
'V2
70656
70784',
'V2
69216
69248',
'V2
917504
917632',
'V2
3584
3712',
'V2
123536
123584',
'V2
5120
5760',
'V2
0
128',
'V2
125184
125280',
'V2
42656
42752',
'V2
7104
7168',
'V2
5952
5984',
'V2
71680
71760',
'V2
880
1024',
'V2
6016
6144',
'V2
6400
6480',
'V2
119040
119296',
'V2
110960
111360',
'V2
5760
5792',
'V2
2816
2944',
'V2
66736
66816',
'V2
5792
5888',
'V2
6480
6528',
'V2
71296
71376',
'V2
2944
3072',
'V2
917760
918000',
'V2
1536
1792',
'V2
8592
8704',
'V2
69632
69760',
'V2
66208
66272',
'V2
69888
69968',
'V2
11392
11520',
'V2
127024
127136',
'V2
66352
66384',
'V2
44032
55216',
'V2
67808
67840',
'V2
1424
1536',
'V2
592
688',
'V2
69760
69840',
'V2
12688
12704',
'V2
12032
12256',
'V2
70144
70224',
'V2
128
256',
'V2
7168
7248',
'V2
66176
66208',
'V2
67872
67904',
'V2
43312
43360',
'V2
1792
1872',
'V2
92784
92880',
'V2
94208
100352',
'V2
3072
3200',
'V2
1920
1984',
'V2
123584
123648',
'V2
69248
69312',
'V2
19904
19968',
'V2
68352
68416',
'V2
2432
2560',
'V2
10240
10496',
'V2
13312
19904',
'V2
131072
173792',
'V2
173824
177984',
'V2
177984
178208',
'V2
178208
183984',
'V2
183984
191472',
'V2
196608
201552',
'V2
201552
205744',
'V2
66560
66640',
'V2
66816
66864',
'V2
69600
69632',
'V2
70400
70528',
'V2
5920
5952',
'V2
110592
110848',
'V2
3200
3328',
'V2
43264
43312',
'V2
67072
67456',
'V2
73648
73664',
'V2
126976
127024',
'V2
73440
73472',
'V2
2112
2144',
'V2
72816
72896',
'V2
70272
70320',
'V2
4096
4256',
'V2
7248
7296',
'V2
66688
66736',
'V2
43072
43136',
'V2
70016
70112',
'V2
66640
66688',
'V2
71040
71168',
'V2
3456
3584',
'V2
69424
69488',
'V2
72272
72368',
'V2
983040
1048576',
'V2
1048576
1114112',
'V2
5888
5920',
'V2
6688
6832',
'V2
43648
43744',
'V2
3840
4096',
'V2
70784
70880',
'V2
6320
6400',
'V2
1328
1424',
'V2
6912
7040',
'V2
92160
92736',
'V2
92880
92928',
'V2
12544
12592',
'V2
6656
6688',
'V2
5024
5120',
'V2
1024
1280',
'V2
9984
10176',
'V2
113664
113824',
'V2
4608
4992',
'V2
4256
4352',
'V2
7936
8192',
'V2
2688
2816',
'V2
2560
2688',
'V2
12352
12448',
'V2
43360
43392',
'V2
55216
55296',
'V2
43392
43488',
'V2
110848
110896',
'V2
110576
110592',
'V2
12448
12544',
'V2
69968
70016',
'V2
66000
66048',
'V2
65520
65536',
'V2
5984
6016',
'V2
73664
73728',
'V2
11568
11648',
'V2
72368
72384',
'V2
66432
66464',
'V2
7376
7424',
'V2
66928
67008',
'V2
64336
65024',
'V2
65136
65280',
'V2
1872
1920',
'V2
72704
72816',
'V2
13056
13312',
'V2
73728
74752',
'V2
128512
128592',
'V2
65056
65072',
'V2
70320
70400',
'V2
256
384',
'V2
384
592',
'V2
11360
11392',
'V2
42784
43008',
'V2
43824
43888',
'V2
67456
67520',
'V2
122624
122880',
'V2
3328
3456',
'V2
6144
6320',
'V2
67712
67760',
'V2
6528
6624',
'V2
66304
66352',
'V2
66384
66432',
'V2
68608
68688',
'V2
69488
69552',
'V2
67680
67712',
'V2
72384
72448',
'V2
2048
2112',
'V2
7040
7104',
'V2
2144
2160',
'V2
101632
101760',
'V2
128768
128896',
'V2
2208
2304',
'V2
2160
2208',
'V2
69312
69376',
'V2
126464
126720',
'V2
9472
9600',
'V2
12736
12784',
'V2
12288
12352',
'V2
69552
69600',
'V2
12592
12688',
'V2
2304
2432',
'V2
71936
72032',
'V2
11264
11360',
'V2
68096
68192',
'V2
68288
68352',
'V2
11008
11264',
'V2
124112
124160',
'V2
66464
66528',
'V2
69376
69424',
'V2
67840
67872',
'V2
43136
43232',
'V2
65104
65136',
'V2
10224
10240',
'V2
10496
10624',
'V2
129024
129280',
'V2
71840
71936',
'V2
42128
42192',
'V2
12704
12736',
'V2
43888
43968',
'V2
119648
119680',
'V2
77712
77824',
'V2
1280
1328',
'V2
12800
13056',
'V2
11648
11744',
'V2
4992
5024',
'V2
7312
7360',
'V2
11520
11568',
'V2
12784
12800',
'V2
93760
93856',
'V2
43968
44032',
'V2
9728
9984',
'V2
43616
43648',
'V2
43488
43520',
'V2
72096
72192',
'V2
8528
8592',
'V2
92928
93072',
'V2
7424
7552',
'V2
8192
8304',
'V2
69840
69888',
'V2
8304
8352',
'V2
43008
43056',
'V2
119552
119648',
'V2
40960
42128',
'V2
64256
64336',
'V2
129536
129648',
'V2
11744
11776',
'V2
42560
42656',
'V2
7296
7312',
'V2
122928
123024',
'V2
768
880',
'V2
43776
43824',
'V2
124896
124928',
'V2
73056
73136',
'V2
6624
6656',
'V2
72960
73056',
'V2
119808
120832',
'V2
124928
125152',
'V2
71264
71296',
'V2
68736
68864',
'V2
127136
127232',
'V2
110896
110960',
'V2
7360
7376',
'V2
65792
65856',
'V2
9600
9632',
'V2
43232
43264',
'V2
122880
122928',
'V2
56320
57344',
'V2
8704
8960',
'V2
119520
119552',
'V2
8960
9216',
'V2
65040
65056',
'V2
118528
118736',
'V2
65936
66000',
'V2
118784
119040',
'V2
65072
65104',
'V2
11904
12032',
'V2
72448
72544',
'V2
68864
68928',
'V2
55296
56192',
'V2
43744
43776',
'V2
7552
7616',
'V2
68480
68528',
'V2
11776
11904',
'V2
9216
9280',
'V2
8352
8400',
'V2
6832
6912',
'V2
7616
7680',
'V2
9632
9728',
'V2
67648
67680',
'V2
68000
68096',
'V2
127744
128512',
'V2
688
768',
'V2
68224
68256',
'V2
68192
68224',
'V2
128640
128768',
'V2
72192
72272',
'V2
74752
74880',
'V2
67584
67648',
'V2
9312
9472',
'V2
65280
65520',
'V2
56192
56320',
'V2
43056
43072',
'V2
119488
119520',
'V2
65664
65792',
'V2
65536
65664',
'V2
10176
10224',
'V2
10624
10752',
'V2
10752
11008',
'V2
100352
101120',
'V2
119296
119376',
'V2
66864
66928',
'V2
126064
126144',
'V2
101120
101632',
'V2
8448
8528',
'V2
120832
121520',
'V2
66272
66304',
'V2
128896
129024',
'V2
94176
94208',
'V2
7680
7936',
'V2
128592
128640',
'V2
65856
65936',
'V2
63744
64256',
'V2
77824
78896',
'V2
127232
127488',
'V2
67968
68000',
'V2
42752
42784',
'V2
126208
126288',
'V2
82944
83584',
'V2
68448
68480',
'V2
123136
123216',
'V2
68416
68448',
'V2
70112
70144',
'V2
194560
195104',
'V2
8400
8448',
'V2
74880
75088',
'V2
127488
127744',
'V2
113824
113840',
'V2
129280
129536',
'V2
129648
129792',
'V2
129792
130048',
'V2
78896
78944',
'V6
188
191
8528
8544
8585
8586',
'V6
65104
65107
65108
65127
65128
65132',
'V6
12288
12289
65281
65377
65504
65511',
'V4
0
32
127
160',
'V6
57344
63744
983040
1048574
1048576
1114110',
'V2
8232
8233',
'V2
8233
8234',
'V2
1829
1830',
'V2
1871
1872',
'V2
1815
1816',
'V2
1830
1831',
'V2
1607
1608',
'V2
1825
1826',
'V2
1826
1827',
'V2
1725
1726',
'V2
1836
1837',
'V4
1810
1811
1837
1838',
'V2
1818
1819',
'V2
1823
1824',
'V6
1605
1606
1893
1895
2215
2216',
'V2
1833
1834',
'V2
1835
1836',
'V2
1819
1821',
'V2
1821
1822',
'V2
1817
1818',
'V2
1808
1809',
'V4
1811
1813
1838
1839',
'V2
1870
1871',
'V2
1832
1833',
'V2
1869
1870',
'V2
1824
1825',
'V2
1822
1823',
'V2
1729
1731',
'V2
1827
1828',
'V2
2182
2183',
'V2
1706
1707',
'V2
1816
1817',
'V2
1746
1748',
'V2
2235
2236',
'V4
2236
2237
2244
2245',
'V6
1813
1815
1834
1835
1839
1840',
'V4
1726
1727
1791
1792',
'V2
1831
1832',
'V6
1577
1578
1728
1729
1749
1750',
'V2
2237
2238',
'V2
2145
2146',
'V2
2151
2152',
'V2
2220
2221',
'V2
2225
2226',
'V2
1741
1742',
'V2
1828
1829',
'V2
2150
2151',
'V2
2152
2153',
'V2
2144
2145',
'V2
2148
2149',
'V2
2146
2147',
'V2
2154
2155',
'V2
2147
2148',
'V2
68315
68317',
'V2
2190
2191',
'V2
2153
2154',
'V2
2149
2150',
'V2
68310
68311',
'V2
68311
68312',
'V2
68331
68332',
'V2
68324
68325',
'V2
68333
68334',
'V2
68295
68296',
'V2
1731
1732',
'V2
68313
68315',
'V2
68289
68291',
'V2
68332
68333',
'V2
68301
68302',
'V2
68304
68307',
'V2
68318
68321',
'V2
68321
68322',
'V2
68302
68303',
'V2
68303
68304',
'V2
68288
68289',
'V2
68291
68293',
'V2
68317
68318',
'V2
68297
68299',
'V6
68866
68867
68873
68874
68892
68893',
'V2
68293
68294',
'V2
68307
68308',
'V2
68312
68313',
'V2
68334
68335',
'V2
68308
68309',
'V2
68335
68336',
'V2
68309
68310',
'V2
1914
1916',
'V4
8212
8213
11834
11836',
'V4
11
13
8232
8234',
'V2
65532
65533',
'V4
41
42
93
94',
'V2
45
46',
'V2
133
134',
'V2
55296
57344',
'V2
32
33',
'V2
47
48',
'V4
8288
8289
65279
65280',
'V2
8203
8204',
'V2
12881
12882',
'V2
12882
12883',
'V2
12883
12884',
'V2
12884
12885',
'V2
12885
12886',
'V2
12886
12887',
'V2
12887
12888',
'V2
12888
12889',
'V2
12889
12890',
'V2
12891
12892',
'V2
12892
12893',
'V2
12893
12894',
'V2
12894
12895',
'V2
12895
12896',
'V2
12977
12978',
'V2
12978
12979',
'V2
12979
12980',
'V2
12980
12981',
'V2
12982
12983',
'V2
12983
12984',
'V2
12984
12985',
'V2
12985
12986',
'V2
12986
12987',
'V2
12987
12988',
'V2
12988
12989',
'V2
12989
12990',
'V2
12990
12991',
'V6
3422
3423
8533
8534
73679
73680',
'V2
8528
8529',
'V2
8529
8530',
'V2
8534
8535',
'V2
3883
3884',
'V2
8535
8536',
'V2
8540
8541',
'V2
8536
8537',
'V2
3884
3885',
'V6
8538
8539
68095
68096
74844
74845',
'V2
8541
8542',
'V2
3885
3886',
'V2
8542
8543',
'V2
3886
3887',
'V2
3891
3892',
'V6
3420
3421
8530
8531
73675
73676',
'V2
68086
68087',
'V4
3419
3420
73672
73673',
'V2
73669
73670',
'V4
3417
3418
73668
73669',
'V2
73667
73668',
'V2
73666
73667',
'V2
3887
3888',
'V2
3888
3889',
'V2
3889
3890',
'V2
3890
3891',
'V4
3421
3422
73677
73678',
'V2
73671
73672',
'V4
3418
3419
73670
73671',
'V2
68090
68091',
'V2
68092
68093',
'V4
3416
3417
73665
73666',
'V4
73664
73665
73684
73685',
'V2
68028
68029',
'V4
68078
68079
126111
126112',
'V2
74802
74803',
'V2
68079
68080',
'V2
68080
68081',
'V2
74803
74804',
'V2
68081
68082',
'V2
68082
68083',
'V2
68083
68084',
'V2
68084
68085',
'V2
68085
68086',
'V2
93022
93023',
'V2
126113
126114',
'V2
126114
126115',
'V6
20159
20160
20740
20741
93023
93024',
'V2
93024
93025',
'V4
20806
20807
93025
93026',
'V4
40960
42125
42128
42183',
'V4
1984
2043
2045
2048',
'V6
125184
125260
125264
125274
125278
125280',
'V6
746
748
12549
12592
12704
12736',
'V4
6656
6684
6686
6688',
'V2
5952
5972',
'V4
69888
69941
69942
69960',
'V6
994
1008
11392
11508
11513
11520',
'V2
77712
77811',
'V2
71680
71740',
'V2
5920
5941',
'V6
43392
43470
43472
43482
43486
43488',
'V4
43264
43310
43311
43312',
'V4
70144
70162
70163
70210',
'V4
69760
69827
69837
69838',
'V6
67072
67383
67392
67414
67424
67432',
'V2
69968
70007',
'V4
2112
2140
2142
2143',
'V4
68288
68327
68331
68343',
'V4
71168
71237
71248
71258',
'V6
4096
4256
43488
43519
43616
43648',
'V6
72096
72104
72106
72152
72154
72165',
'V2
69488
69514',
'V2
66384
66427',
'V2
43072
43128',
'V6
68480
68498
68505
68509
68521
68528',
'V4
68864
68904
68912
68922',
'V4
70320
70379
70384
70394',
'V2
69424
69466',
'V2
43008
43053',
'V6
5984
5997
5998
6001
6002
6004',
'V4
71296
71354
71360
71370',
'V4
6480
6510
6512
6517',
'V4
5888
5910
5919
5920',
'V2
1920
1970',
'V4
70784
70856
70864
70874',
'V6
69248
69290
69291
69294
69296
69298',
'V6
92736
92767
92768
92778
92782
92784',
'V2
42240
42540',
'V4
66864
66916
66927
66928',
'V6
71424
71451
71453
71468
71472
71495',
'V4
67648
67670
67671
67680',
'V4
68352
68406
68409
68416',
'V4
6912
6989
6992
7039',
'V4
42656
42744
92160
92729',
'V4
92880
92910
92912
92918',
'V4
7104
7156
7164
7168',
'V6
69632
69710
69714
69750
69759
69760',
'V6
6656
6684
6686
6688
43471
43472',
'V4
5941
5943
5952
5972',
'V6
5120
5760
6320
6390
72368
72384',
'V2
66208
66257',
'V6
5024
5110
5112
5118
43888
43968',
'V2
69552
69580',
'V4
65792
65794
77712
77811',
'V6
2404
2416
43056
43066
71680
71740',
'V2
77824
78934',
'V2
66816
66856',
'V2
69600
69623',
'V2
66352
66379',
'V2
5920
5943',
'V6
67808
67827
67828
67830
67835
67840',
'V2
82944
83527',
'V6
68736
68787
68800
68851
68858
68864',
'V4
66304
66340
66349
66352',
'V6
43392
43470
43471
43482
43486
43488',
'V6
73472
73489
73490
73531
73534
73562',
'V4
94180
94181
101120
101590',
'V6
7168
7224
7227
7242
7245
7248',
'V4
42192
42240
73648
73649',
'V2
66176
66205',
'V4
67872
67898
67903
67904',
'V6
2404
2416
43056
43066
69968
70007',
'V2
73440
73465',
'V6
1600
1601
2112
2140
2142
2143',
'V6
1600
1601
68288
68327
68331
68343',
'V6
72816
72848
72850
72872
72873
72887',
'V2
93760
93851',
'V4
124928
125125
125127
125143',
'V6
68000
68024
68028
68048
68050
68096',
'V6
93952
94027
94031
94088
94095
94112',
'V6
43056
43066
71168
71237
71248
71258',
'V6
43744
43767
43968
44014
44016
44026',
'V2
124112
124154',
'V4
67712
67743
67751
67760',
'V4
70656
70748
70749
70754',
'V4
94177
94178
110960
111356',
'V2
5760
5789',
'V2
68608
68681',
'V4
66736
66772
66776
66812',
'V4
66688
66718
66720
66730',
'V6
1600
1601
68338
68339
69488
69514',
'V2
72384
72441',
'V4
1155
1156
66384
66427',
'V6
6146
6148
6149
6150
43072
43128',
'V4
68448
68467
68472
68480',
'V4
67840
67868
67871
67872',
'V4
68416
68438
68440
68448',
'V4
43312
43348
43359
43360',
'V4
5792
5867
5870
5881',
'V4
2048
2094
2096
2111',
'V4
43136
43206
43214
43226',
'V6
120832
121484
121499
121504
121505
121520',
'V4
71040
71094
71096
71134',
'V4
1600
1601
69424
69466',
'V2
69376
69416',
'V4
69840
69865
69872
69882',
'V2
72272
72355',
'V4
7040
7104
7360
7368',
'V6
2404
2406
2534
2544
43008
43053',
'V6
4160
4170
6480
6510
6512
6517',
'V4
43648
43715
43739
43744',
'V6
11568
11624
11631
11633
11647
11648',
'V6
5888
5910
5919
5920
5941
5943',
'V4
3585
3643
3648
3676',
'V4
92784
92863
92864
92874',
'V2
123536
123567',
'V4
66432
66462
66463
66464',
'V4
71840
71923
71935
71936',
'V4
123584
123642
123647
123648',
'V4
66464
66500
66504
66518',
'V2
72192
72264',
,
);

# Maps property, table to file for those using stricter matching.  For paths
# whose directory is '#', the file is in the form of a numeric index into
# @inline_definitions
%Unicode::UCD::stricter_to_file_of = (
'_perl_any_folds' => 'Perl/_PerlAny',
'_perl_charname_begin' => 'Perl/_PerlCha',
'_perl_charname_continue' => 'Perl/_PerlCh2',
'_perl_folds_to_multi_char' => 'Perl/_PerlFol',
'_perl_idcont' => 'Perl/_PerlIDC',
'_perl_idstart' => 'Perl/_PerlIDS',
'_perl_is_in_multi_char_fold' => 'Perl/_PerlIsI',
'_perl_nchar' => 'Perl/_PerlNch',
'_perl_patws' => 'Perl/_PerlPat',
'_perl_problematic_locale_foldeds_start' => 'Perl/_PerlPr2',
'_perl_problematic_locale_folds' => 'Perl/_PerlPro',
'_perl_quotemeta' => 'Perl/_PerlQuo',
'_perl_surrogate' => '#/14',
'age=1.1' => 'Age/V11',
'age=10' => 'Age/V100',
'age=10.0' => 'Age/V100',
'age=11' => 'Age/V110',
'age=11.0' => 'Age/V110',
'age=12' => 'Age/V120',
'age=12.0' => 'Age/V120',
'age=12.1' => '#/98',
'age=13' => 'Age/V130',
'age=13.0' => 'Age/V130',
'age=14' => 'Age/V140',
'age=14.0' => 'Age/V140',
'age=15' => 'Age/V150',
'age=15.0' => 'Age/V150',
'age=2' => 'Age/V20',
'age=2.0' => 'Age/V20',
'age=2.1' => '#/95',
'age=3' => 'Age/V30',
'age=3.0' => 'Age/V30',
'age=3.1' => 'Age/V31',
'age=3.2' => 'Age/V32',
'age=4' => 'Age/V40',
'age=4.0' => 'Age/V40',
'age=4.1' => 'Age/V41',
'age=5' => 'Age/V50',
'age=5.0' => 'Age/V50',
'age=5.1' => 'Age/V51',
'age=5.2' => 'Age/V52',
'age=6' => 'Age/V60',
'age=6.0' => 'Age/V60',
'age=6.1' => 'Age/V61',
'age=6.2' => '#/96',
'age=6.3' => '#/97',
'age=7' => 'Age/V70',
'age=7.0' => 'Age/V70',
'age=8' => 'Age/V80',
'age=8.0' => 'Age/V80',
'age=9' => 'Age/V90',
'age=9.0' => 'Age/V90',
'ccc=0' => 'Ccc/NR',
'ccc=1' => 'Ccc/OV',
'ccc=10' => '#/24',
'ccc=103' => '#/53',
'ccc=107' => '#/54',
'ccc=11' => '#/25',
'ccc=118' => '#/55',
'ccc=12' => '#/26',
'ccc=122' => '#/56',
'ccc=129' => '#/57',
'ccc=13' => '#/27',
'ccc=130' => '#/58',
'ccc=132' => '#/59',
'ccc=133' => '#/0',
'ccc=14' => '#/28',
'ccc=15' => '#/29',
'ccc=16' => '#/30',
'ccc=17' => '#/31',
'ccc=18' => '#/32',
'ccc=19' => '#/33',
'ccc=20' => '#/34',
'ccc=200' => '#/0',
'ccc=202' => '#/22',
'ccc=21' => '#/35',
'ccc=214' => '#/21',
'ccc=216' => 'Ccc/ATAR',
'ccc=218' => '#/17',
'ccc=22' => '#/36',
'ccc=220' => 'Ccc/B',
'ccc=222' => 'Ccc/BR',
'ccc=224' => '#/15',
'ccc=226' => '#/16',
'ccc=228' => 'Ccc/AL',
'ccc=23' => '#/37',
'ccc=230' => 'Ccc/A',
'ccc=232' => 'Ccc/AR',
'ccc=233' => 'Ccc/DB',
'ccc=234' => '#/18',
'ccc=24' => '#/38',
'ccc=240' => '#/19',
'ccc=25' => '#/39',
'ccc=26' => '#/40',
'ccc=27' => '#/41',
'ccc=28' => '#/42',
'ccc=29' => '#/43',
'ccc=30' => '#/44',
'ccc=31' => '#/45',
'ccc=32' => '#/46',
'ccc=33' => '#/47',
'ccc=34' => '#/48',
'ccc=35' => '#/49',
'ccc=36' => '#/50',
'ccc=6' => '#/23',
'ccc=7' => 'Ccc/NK',
'ccc=8' => '#/20',
'ccc=84' => '#/51',
'ccc=9' => 'Ccc/VR',
'ccc=91' => '#/52',
'in=1.1' => 'Age/V11',
'in=10' => 'In/10_0',
'in=10.0' => 'In/10_0',
'in=11' => 'In/11_0',
'in=11.0' => 'In/11_0',
'in=12' => 'In/12_0',
'in=12.0' => 'In/12_0',
'in=12.1' => 'In/12_1',
'in=13' => 'In/13_0',
'in=13.0' => 'In/13_0',
'in=14' => 'In/14_0',
'in=14.0' => 'In/14_0',
'in=15' => 'In/15_0',
'in=15.0' => 'In/15_0',
'in=2' => 'In/2_0',
'in=2.0' => 'In/2_0',
'in=2.1' => 'In/2_1',
'in=3' => 'In/3_0',
'in=3.0' => 'In/3_0',
'in=3.1' => 'In/3_1',
'in=3.2' => 'In/3_2',
'in=4' => 'In/4_0',
'in=4.0' => 'In/4_0',
'in=4.1' => 'In/4_1',
'in=5' => 'In/5_0',
'in=5.0' => 'In/5_0',
'in=5.1' => 'In/5_1',
'in=5.2' => 'In/5_2',
'in=6' => 'In/6_0',
'in=6.0' => 'In/6_0',
'in=6.1' => 'In/6_1',
'in=6.2' => 'In/6_2',
'in=6.3' => 'In/6_3',
'in=7' => 'In/7_0',
'in=7.0' => 'In/7_0',
'in=8' => 'In/8_0',
'in=8.0' => 'In/8_0',
'in=9' => 'In/9_0',
'in=9.0' => 'In/9_0',
'nv=-1/2' => '#/579',
'nv=0' => 'Nv/0',
'nv=1' => 'Nv/1',
'nv=1/10' => '#/580',
'nv=1/12' => '#/581',
'nv=1/16' => 'Nv/1_16',
'nv=1/160' => '#/596',
'nv=1/2' => 'Nv/1_2',
'nv=1/20' => '#/582',
'nv=1/3' => 'Nv/1_3',
'nv=1/32' => '#/583',
'nv=1/320' => '#/597',
'nv=1/4' => 'Nv/1_4',
'nv=1/40' => '#/584',
'nv=1/5' => '#/565',
'nv=1/6' => 'Nv/1_6',
'nv=1/64' => '#/585',
'nv=1/7' => '#/566',
'nv=1/8' => 'Nv/1_8',
'nv=1/80' => '#/586',
'nv=1/9' => '#/567',
'nv=10' => 'Nv/10',
'nv=100' => 'Nv/100',
'nv=1000' => 'Nv/1000',
'nv=10000' => 'Nv/10000',
'nv=100000' => 'Nv/100000',
'nv=1000000' => '#/609',
'nv=10000000' => '#/610',
'nv=100000000' => '#/612',
'nv=10000000000' => '#/613',
'nv=1000000000000' => '#/614',
'nv=11' => 'Nv/11',
'nv=11/12' => '#/598',
'nv=11/2' => '#/587',
'nv=12' => 'Nv/12',
'nv=13' => 'Nv/13',
'nv=13/2' => '#/588',
'nv=14' => 'Nv/14',
'nv=15' => 'Nv/15',
'nv=15/2' => '#/589',
'nv=16' => 'Nv/16',
'nv=17' => 'Nv/17',
'nv=17/2' => '#/590',
'nv=18' => 'Nv/18',
'nv=19' => 'Nv/19',
'nv=2' => 'Nv/2',
'nv=2/3' => 'Nv/2_3',
'nv=2/5' => '#/568',
'nv=20' => 'Nv/20',
'nv=200' => 'Nv/200',
'nv=2000' => 'Nv/2000',
'nv=20000' => 'Nv/20000',
'nv=200000' => '#/599',
'nv=20000000' => '#/611',
'nv=21' => '#/538',
'nv=216000' => '#/600',
'nv=22' => '#/539',
'nv=23' => '#/540',
'nv=24' => '#/541',
'nv=25' => '#/542',
'nv=26' => '#/543',
'nv=27' => '#/544',
'nv=28' => '#/545',
'nv=29' => '#/546',
'nv=3' => 'Nv/3',
'nv=3/16' => 'Nv/3_16',
'nv=3/2' => '#/569',
'nv=3/20' => '#/591',
'nv=3/4' => 'Nv/3_4',
'nv=3/5' => '#/570',
'nv=3/64' => '#/592',
'nv=3/8' => '#/571',
'nv=3/80' => '#/593',
'nv=30' => 'Nv/30',
'nv=300' => 'Nv/300',
'nv=3000' => 'Nv/3000',
'nv=30000' => 'Nv/30000',
'nv=300000' => '#/601',
'nv=31' => '#/547',
'nv=32' => '#/548',
'nv=33' => '#/549',
'nv=34' => '#/550',
'nv=35' => '#/551',
'nv=36' => '#/552',
'nv=37' => '#/553',
'nv=38' => '#/554',
'nv=39' => '#/555',
'nv=4' => 'Nv/4',
'nv=4/5' => '#/572',
'nv=40' => 'Nv/40',
'nv=400' => 'Nv/400',
'nv=4000' => 'Nv/4000',
'nv=40000' => 'Nv/40000',
'nv=400000' => '#/602',
'nv=41' => '#/556',
'nv=42' => '#/557',
'nv=43' => '#/558',
'nv=432000' => '#/603',
'nv=44' => '#/559',
'nv=45' => '#/560',
'nv=46' => '#/561',
'nv=47' => '#/562',
'nv=48' => '#/563',
'nv=49' => '#/564',
'nv=5' => 'Nv/5',
'nv=5/12' => '#/594',
'nv=5/2' => '#/573',
'nv=5/6' => '#/574',
'nv=5/8' => '#/575',
'nv=50' => 'Nv/50',
'nv=500' => 'Nv/500',
'nv=5000' => 'Nv/5000',
'nv=50000' => 'Nv/50000',
'nv=500000' => '#/604',
'nv=6' => 'Nv/6',
'nv=60' => 'Nv/60',
'nv=600' => 'Nv/600',
'nv=6000' => 'Nv/6000',
'nv=60000' => 'Nv/60000',
'nv=600000' => '#/605',
'nv=7' => 'Nv/7',
'nv=7/12' => '#/595',
'nv=7/2' => '#/576',
'nv=7/8' => '#/577',
'nv=70' => 'Nv/70',
'nv=700' => 'Nv/700',
'nv=7000' => 'Nv/7000',
'nv=70000' => 'Nv/70000',
'nv=700000' => '#/606',
'nv=8' => 'Nv/8',
'nv=80' => 'Nv/80',
'nv=800' => 'Nv/800',
'nv=8000' => 'Nv/8000',
'nv=80000' => 'Nv/80000',
'nv=800000' => '#/607',
'nv=9' => 'Nv/9',
'nv=9/2' => '#/578',
'nv=90' => 'Nv/90',
'nv=900' => 'Nv/900',
'nv=9000' => 'Nv/9000',
'nv=90000' => 'Nv/90000',
'nv=900000' => '#/608',
);

# Maps property, table to file for those using loose matching.  For paths
# whose directory is '#', the file is in the form of a numeric index into
# @inline_definitions
%Unicode::UCD::loose_to_file_of = (
'adlam' => 'Scx/Adlm',
'adlm' => 'Scx/Adlm',
'aegeannumbers' => '#/357',
'age=na' => 'Age/NA',
'age=unassigned' => 'Age/NA',
'age=v100' => 'Age/V100',
'age=v11' => 'Age/V11',
'age=v110' => 'Age/V110',
'age=v120' => 'Age/V120',
'age=v121' => '#/98',
'age=v130' => 'Age/V130',
'age=v140' => 'Age/V140',
'age=v150' => 'Age/V150',
'age=v20' => 'Age/V20',
'age=v21' => '#/95',
'age=v30' => 'Age/V30',
'age=v31' => 'Age/V31',
'age=v32' => 'Age/V32',
'age=v40' => 'Age/V40',
'age=v41' => 'Age/V41',
'age=v50' => 'Age/V50',
'age=v51' => 'Age/V51',
'age=v52' => 'Age/V52',
'age=v60' => 'Age/V60',
'age=v61' => 'Age/V61',
'age=v62' => '#/96',
'age=v63' => '#/97',
'age=v70' => 'Age/V70',
'age=v80' => 'Age/V80',
'age=v90' => 'Age/V90',
'aghb' => '#/654',
'ahex' => '#/60',
'ahex=f' => '#/!60',
'ahex=false' => '#/!60',
'ahex=n' => '#/!60',
'ahex=no' => '#/!60',
'ahex=t' => '#/60',
'ahex=true' => '#/60',
'ahex=y' => '#/60',
'ahex=yes' => '#/60',
'ahom' => '#/655',
'alchemical' => '#/285',
'alchemicalsymbols' => '#/285',
'all' => '#/1',
'alnum' => 'Perl/Alnum',
'alpha' => 'Alpha/Y',
'alpha=f' => '!Alpha/Y',
'alpha=false' => '!Alpha/Y',
'alpha=n' => '!Alpha/Y',
'alpha=no' => '!Alpha/Y',
'alpha=t' => 'Alpha/Y',
'alpha=true' => 'Alpha/Y',
'alpha=y' => 'Alpha/Y',
'alpha=yes' => 'Alpha/Y',
'alphabetic' => 'Alpha/Y',
'alphabeticpf' => '#/338',
'alphabeticpresentationforms' => '#/338',
'anatolianhieroglyphs' => '#/677',
'ancientgreekmusic' => '#/404',
'ancientgreekmusicalnotation' => '#/404',
'ancientgreeknumbers' => '#/415',
'ancientsymbols' => '#/367',
'any' => '#/2',
'arab' => 'Scx/Arab',
'arabic' => 'Scx/Arab',
'arabicexta' => '#/286',
'arabicextb' => '#/287',
'arabicextc' => '#/288',
'arabicextendeda' => '#/286',
'arabicextendedb' => '#/287',
'arabicextendedc' => '#/288',
'arabicmath' => '#/289',
'arabicmathematicalalphabeticsymbols' => '#/289',
'arabicpfa' => '#/255',
'arabicpfb' => '#/256',
'arabicpresentationformsa' => '#/255',
'arabicpresentationformsb' => '#/256',
'arabicsup' => '#/257',
'arabicsupplement' => '#/257',
'armenian' => 'Scx/Armn',
'armi' => '#/656',
'armn' => 'Scx/Armn',
'arrows' => '#/151',
'ascii' => '#/131',
'asciihexdigit' => '#/60',
'assigned' => 'Perl/Assigned',
'avestan' => '#/657',
'avst' => '#/657',
'bali' => '#/658',
'balinese' => '#/658',
'bamu' => '#/659',
'bamum' => '#/659',
'bamumsup' => '#/225',
'bamumsupplement' => '#/225',
'basiclatin' => '#/131',
'bass' => '#/660',
'bassavah' => '#/660',
'batak' => '#/661',
'batk' => '#/661',
'bc=al' => 'Bc/AL',
'bc=an' => 'Bc/AN',
'bc=arabicletter' => 'Bc/AL',
'bc=arabicnumber' => 'Bc/AN',
'bc=b' => 'Bc/B',
'bc=bn' => 'Bc/BN',
'bc=boundaryneutral' => 'Bc/BN',
'bc=commonseparator' => 'Bc/CS',
'bc=cs' => 'Bc/CS',
'bc=en' => 'Bc/EN',
'bc=es' => 'Bc/ES',
'bc=et' => 'Bc/ET',
'bc=europeannumber' => 'Bc/EN',
'bc=europeanseparator' => 'Bc/ES',
'bc=europeanterminator' => 'Bc/ET',
'bc=firststrongisolate' => '#/100',
'bc=fsi' => '#/100',
'bc=l' => 'Bc/L',
'bc=lefttoright' => 'Bc/L',
'bc=lefttorightembedding' => '#/101',
'bc=lefttorightisolate' => '#/102',
'bc=lefttorightoverride' => '#/103',
'bc=lre' => '#/101',
'bc=lri' => '#/102',
'bc=lro' => '#/103',
'bc=nonspacingmark' => 'Bc/NSM',
'bc=nsm' => 'Bc/NSM',
'bc=on' => 'Bc/ON',
'bc=otherneutral' => 'Bc/ON',
'bc=paragraphseparator' => 'Bc/B',
'bc=pdf' => '#/104',
'bc=pdi' => '#/105',
'bc=popdirectionalformat' => '#/104',
'bc=popdirectionalisolate' => '#/105',
'bc=r' => 'Bc/R',
'bc=righttoleft' => 'Bc/R',
'bc=righttoleftembedding' => '#/106',
'bc=righttoleftisolate' => '#/107',
'bc=righttoleftoverride' => '#/108',
'bc=rle' => '#/106',
'bc=rli' => '#/107',
'bc=rlo' => '#/108',
'bc=s' => '#/99',
'bc=segmentseparator' => '#/99',
'bc=whitespace' => 'Bc/WS',
'bc=ws' => 'Bc/WS',
'beng' => 'Scx/Beng',
'bengali' => 'Scx/Beng',
'bhaiksuki' => 'Scx/Bhks',
'bhks' => 'Scx/Bhks',
'bidic' => 'BidiC/Y',
'bidic=f' => '!BidiC/Y',
'bidic=false' => '!BidiC/Y',
'bidic=n' => '!BidiC/Y',
'bidic=no' => '!BidiC/Y',
'bidic=t' => 'BidiC/Y',
'bidic=true' => 'BidiC/Y',
'bidic=y' => 'BidiC/Y',
'bidic=yes' => 'BidiC/Y',
'bidicontrol' => 'BidiC/Y',
'bidim' => 'BidiM/Y',
'bidim=f' => '!BidiM/Y',
'bidim=false' => '!BidiM/Y',
'bidim=n' => '!BidiM/Y',
'bidim=no' => '!BidiM/Y',
'bidim=t' => 'BidiM/Y',
'bidim=true' => 'BidiM/Y',
'bidim=y' => 'BidiM/Y',
'bidim=yes' => 'BidiM/Y',
'bidimirrored' => 'BidiM/Y',
'blank' => 'Perl/Blank',
'blk=adlam' => '#/132',
'blk=aegeannumbers' => '#/357',
'blk=ahom' => '#/118',
'blk=alchemical' => '#/285',
'blk=alchemicalsymbols' => '#/285',
'blk=alphabeticpf' => '#/338',
'blk=alphabeticpresentationforms' => '#/338',
'blk=anatolianhieroglyphs' => '#/422',
'blk=ancientgreekmusic' => '#/404',
'blk=ancientgreekmusicalnotation' => '#/404',
'blk=ancientgreeknumbers' => '#/415',
'blk=ancientsymbols' => '#/367',
'blk=arabic' => '#/150',
'blk=arabicexta' => '#/286',
'blk=arabicextb' => '#/287',
'blk=arabicextc' => '#/288',
'blk=arabicextendeda' => '#/286',
'blk=arabicextendedb' => '#/287',
'blk=arabicextendedc' => '#/288',
'blk=arabicmath' => '#/289',
'blk=arabicmathematicalalphabeticsymbols' => '#/289',
'blk=arabicpfa' => '#/255',
'blk=arabicpfb' => '#/256',
'blk=arabicpresentationformsa' => '#/255',
'blk=arabicpresentationformsb' => '#/256',
'blk=arabicsup' => '#/257',
'blk=arabicsupplement' => '#/257',
'blk=armenian' => '#/223',
'blk=arrows' => '#/151',
'blk=ascii' => '#/131',
'blk=avestan' => '#/179',
'blk=balinese' => '#/224',
'blk=bamum' => '#/133',
'blk=bamumsup' => '#/225',
'blk=bamumsupplement' => '#/225',
'blk=basiclatin' => '#/131',
'blk=bassavah' => '#/226',
'blk=batak' => '#/134',
'blk=bengali' => '#/180',
'blk=bhaiksuki' => '#/258',
'blk=blockelements' => '#/358',
'blk=bopomofo' => '#/227',
'blk=bopomofoext' => '#/312',
'blk=bopomofoextended' => '#/312',
'blk=boxdrawing' => '#/290',
'blk=brahmi' => '#/152',
'blk=braille' => '#/181',
'blk=braillepatterns' => '#/181',
'blk=buginese' => '#/228',
'blk=buhid' => '#/135',
'blk=byzantinemusic' => '#/368',
'blk=byzantinemusicalsymbols' => '#/368',
'blk=canadiansyllabics' => '#/130',
'blk=carian' => '#/153',
'blk=caucasianalbanian' => '#/405',
'blk=chakma' => '#/154',
'blk=cham' => '#/119',
'blk=cherokee' => '#/229',
'blk=cherokeesup' => '#/313',
'blk=cherokeesupplement' => '#/313',
'blk=chesssymbols' => '#/339',
'blk=chorasmian' => '#/293',
'blk=cjk' => '#/110',
'blk=cjkcompat' => '#/259',
'blk=cjkcompatforms' => '#/369',
'blk=cjkcompatibility' => '#/259',
'blk=cjkcompatibilityforms' => '#/369',
'blk=cjkcompatibilityideographs' => '#/416',
'blk=cjkcompatibilityideographssupplement' => '#/427',
'blk=cjkcompatideographs' => '#/416',
'blk=cjkcompatideographssup' => '#/427',
'blk=cjkexta' => '#/182',
'blk=cjkextb' => '#/183',
'blk=cjkextc' => '#/184',
'blk=cjkextd' => '#/185',
'blk=cjkexte' => '#/186',
'blk=cjkextf' => '#/187',
'blk=cjkextg' => '#/188',
'blk=cjkexth' => '#/189',
'blk=cjkradicalssup' => '#/370',
'blk=cjkradicalssupplement' => '#/370',
'blk=cjkstrokes' => '#/291',
'blk=cjksymbols' => '#/292',
'blk=cjksymbolsandpunctuation' => '#/292',
'blk=cjkunifiedideographs' => '#/110',
'blk=cjkunifiedideographsextensiona' => '#/182',
'blk=cjkunifiedideographsextensionb' => '#/183',
'blk=cjkunifiedideographsextensionc' => '#/184',
'blk=cjkunifiedideographsextensiond' => '#/185',
'blk=cjkunifiedideographsextensione' => '#/186',
'blk=cjkunifiedideographsextensionf' => '#/187',
'blk=cjkunifiedideographsextensiong' => '#/188',
'blk=cjkunifiedideographsextensionh' => '#/189',
'blk=combiningdiacriticalmarks' => '#/344',
'blk=combiningdiacriticalmarksextended' => '#/380',
'blk=combiningdiacriticalmarksforsymbols' => '#/428',
'blk=combiningdiacriticalmarkssupplement' => '#/381',
'blk=combininghalfmarks' => '#/262',
'blk=combiningmarksforsymbols' => '#/428',
'blk=commonindicnumberforms' => '#/396',
'blk=compatjamo' => '#/294',
'blk=controlpictures' => '#/378',
'blk=coptic' => '#/155',
'blk=copticepactnumbers' => '#/410',
'blk=countingrod' => '#/314',
'blk=countingrodnumerals' => '#/314',
'blk=cuneiform' => '#/260',
'blk=cuneiformnumbers' => '#/391',
'blk=cuneiformnumbersandpunctuation' => '#/391',
'blk=currencysymbols' => '#/379',
'blk=cypriotsyllabary' => '#/392',
'blk=cyprominoan' => '#/315',
'blk=cyrillic' => '#/230',
'blk=cyrillicexta' => '#/340',
'blk=cyrillicextb' => '#/341',
'blk=cyrillicextc' => '#/342',
'blk=cyrillicextd' => '#/343',
'blk=cyrillicextendeda' => '#/340',
'blk=cyrillicextendedb' => '#/341',
'blk=cyrillicextendedc' => '#/342',
'blk=cyrillicextendedd' => '#/343',
'blk=cyrillicsup' => '#/316',
'blk=cyrillicsupplement' => '#/316',
'blk=cyrillicsupplementary' => '#/316',
'blk=deseret' => '#/190',
'blk=devanagari' => '#/295',
'blk=devanagariext' => '#/359',
'blk=devanagariexta' => '#/371',
'blk=devanagariextended' => '#/359',
'blk=devanagariextendeda' => '#/371',
'blk=diacriticals' => '#/344',
'blk=diacriticalsext' => '#/380',
'blk=diacriticalsforsymbols' => '#/428',
'blk=diacriticalssup' => '#/381',
'blk=dingbats' => '#/231',
'blk=divesakuru' => '#/296',
'blk=dogra' => '#/136',
'blk=domino' => '#/156',
'blk=dominotiles' => '#/156',
'blk=duployan' => '#/232',
'blk=earlydynasticcuneiform' => '#/429',
'blk=egyptianhieroglyphformatcontrols' => '#/435',
'blk=egyptianhieroglyphs' => '#/417',
'blk=elbasan' => '#/191',
'blk=elymaic' => '#/192',
'blk=emoticons' => '#/261',
'blk=enclosedalphanum' => '#/393',
'blk=enclosedalphanumerics' => '#/393',
'blk=enclosedalphanumericsupplement' => '#/418',
'blk=enclosedalphanumsup' => '#/418',
'blk=enclosedcjk' => '#/317',
'blk=enclosedcjklettersandmonths' => '#/317',
'blk=enclosedideographicsup' => '#/430',
'blk=enclosedideographicsupplement' => '#/430',
'blk=ethiopic' => '#/233',
'blk=ethiopicext' => '#/318',
'blk=ethiopicexta' => '#/345',
'blk=ethiopicextb' => '#/346',
'blk=ethiopicextended' => '#/318',
'blk=ethiopicextendeda' => '#/345',
'blk=ethiopicextendedb' => '#/346',
'blk=ethiopicsup' => '#/319',
'blk=ethiopicsupplement' => '#/319',
'blk=generalpunctuation' => '#/332',
'blk=geometricshapes' => '#/382',
'blk=geometricshapesext' => '#/411',
'blk=geometricshapesextended' => '#/411',
'blk=georgian' => '#/234',
'blk=georgianext' => '#/320',
'blk=georgianextended' => '#/320',
'blk=georgiansup' => '#/321',
'blk=georgiansupplement' => '#/321',
'blk=glagolitic' => '#/297',
'blk=glagoliticsup' => '#/360',
'blk=glagoliticsupplement' => '#/360',
'blk=gothic' => '#/157',
'blk=grantha' => '#/193',
'blk=greek' => '#/137',
'blk=greekandcoptic' => '#/137',
'blk=greekext' => '#/235',
'blk=greekextended' => '#/235',
'blk=gujarati' => '#/236',
'blk=gunjalagondi' => '#/347',
'blk=gurmukhi' => '#/237',
'blk=halfandfullforms' => '#/394',
'blk=halfmarks' => '#/262',
'blk=halfwidthandfullwidthforms' => '#/394',
'blk=hangul' => '#/158',
'blk=hangulcompatibilityjamo' => '#/294',
'blk=hanguljamo' => '#/120',
'blk=hanguljamoextendeda' => '#/239',
'blk=hanguljamoextendedb' => '#/240',
'blk=hangulsyllables' => '#/158',
'blk=hanifirohingya' => '#/372',
'blk=hanunoo' => '#/194',
'blk=hatran' => '#/159',
'blk=hebrew' => '#/160',
'blk=highprivateusesurrogates' => '#/395',
'blk=highpusurrogates' => '#/395',
'blk=highsurrogates' => '#/373',
'blk=hiragana' => '#/238',
'blk=idc' => '#/111',
'blk=ideographicdescriptioncharacters' => '#/111',
'blk=ideographicsymbols' => '#/412',
'blk=ideographicsymbolsandpunctuation' => '#/412',
'blk=imperialaramaic' => '#/383',
'blk=indicnumberforms' => '#/396',
'blk=indicsiyaqnumbers' => '#/406',
'blk=inscriptionalpahlavi' => '#/423',
'blk=inscriptionalparthian' => '#/425',
'blk=ipaext' => '#/161',
'blk=ipaextensions' => '#/161',
'blk=jamo' => '#/120',
'blk=jamoexta' => '#/239',
'blk=jamoextb' => '#/240',
'blk=javanese' => '#/241',
'blk=kaithi' => '#/162',
'blk=kaktoviknumerals' => '#/397',
'blk=kanaexta' => '#/242',
'blk=kanaextb' => '#/243',
'blk=kanaextendeda' => '#/242',
'blk=kanaextendedb' => '#/243',
'blk=kanasup' => '#/195',
'blk=kanasupplement' => '#/195',
'blk=kanbun' => '#/163',
'blk=kangxi' => '#/164',
'blk=kangxiradicals' => '#/164',
'blk=kannada' => '#/196',
'blk=katakana' => '#/244',
'blk=katakanaext' => '#/322',
'blk=katakanaphoneticextensions' => '#/322',
'blk=kawi' => '#/121',
'blk=kayahli' => '#/197',
'blk=kharoshthi' => '#/298',
'blk=khitansmallscript' => '#/407',
'blk=khmer' => '#/138',
'blk=khmersymbols' => '#/348',
'blk=khojki' => '#/165',
'blk=khudawadi' => '#/263',
'blk=lao' => '#/112',
'blk=latin1' => '#/166',
'blk=latin1sup' => '#/166',
'blk=latin1supplement' => '#/166',
'blk=latinexta' => '#/264',
'blk=latinextadditional' => '#/413',
'blk=latinextb' => '#/265',
'blk=latinextc' => '#/266',
'blk=latinextd' => '#/267',
'blk=latinexte' => '#/268',
'blk=latinextendeda' => '#/264',
'blk=latinextendedadditional' => '#/413',
'blk=latinextendedb' => '#/265',
'blk=latinextendedc' => '#/266',
'blk=latinextendedd' => '#/267',
'blk=latinextendede' => '#/268',
'blk=latinextendedf' => '#/269',
'blk=latinextendedg' => '#/270',
'blk=latinextf' => '#/269',
'blk=latinextg' => '#/270',
'blk=lepcha' => '#/167',
'blk=letterlikesymbols' => '#/408',
'blk=limbu' => '#/139',
'blk=lineara' => '#/198',
'blk=linearbideograms' => '#/398',
'blk=linearbsyllabary' => '#/399',
'blk=lisu' => '#/122',
'blk=lisusup' => '#/199',
'blk=lisusupplement' => '#/199',
'blk=lowsurrogates' => '#/361',
'blk=lycian' => '#/168',
'blk=lydian' => '#/169',
'blk=mahajani' => '#/245',
'blk=mahjong' => '#/200',
'blk=mahjongtiles' => '#/200',
'blk=makasar' => '#/201',
'blk=malayalam' => '#/271',
'blk=mandaic' => '#/202',
'blk=manichaean' => '#/299',
'blk=marchen' => '#/203',
'blk=masaramgondi' => '#/349',
'blk=mathalphanum' => '#/350',
'blk=mathematicalalphanumericsymbols' => '#/350',
'blk=mathematicaloperators' => '#/362',
'blk=mathoperators' => '#/362',
'blk=mayannumerals' => '#/363',
'blk=medefaidrin' => '#/323',
'blk=meeteimayek' => '#/324',
'blk=meeteimayekext' => '#/374',
'blk=meeteimayekextensions' => '#/374',
'blk=mendekikakui' => '#/351',
'blk=meroiticcursive' => '#/384',
'blk=meroitichieroglyphs' => '#/419',
'blk=miao' => '#/123',
'blk=miscarrows' => '#/300',
'blk=miscellaneousmathematicalsymbolsa' => '#/400',
'blk=miscellaneousmathematicalsymbolsb' => '#/401',
'blk=miscellaneoussymbols' => '#/325',
'blk=miscellaneoussymbolsandarrows' => '#/300',
'blk=miscellaneoussymbolsandpictographs' => '#/385',
'blk=miscellaneoustechnical' => '#/364',
'blk=miscmathsymbolsa' => '#/400',
'blk=miscmathsymbolsb' => '#/401',
'blk=miscpictographs' => '#/385',
'blk=miscsymbols' => '#/325',
'blk=misctechnical' => '#/364',
'blk=modi' => '#/124',
'blk=modifierletters' => '#/386',
'blk=modifiertoneletters' => '#/420',
'blk=mongolian' => '#/272',
'blk=mongoliansup' => '#/352',
'blk=mongoliansupplement' => '#/352',
'blk=mro' => '#/113',
'blk=multani' => '#/204',
'blk=music' => '#/140',
'blk=musicalsymbols' => '#/140',
'blk=myanmar' => '#/205',
'blk=myanmarexta' => '#/326',
'blk=myanmarextb' => '#/327',
'blk=myanmarextendeda' => '#/326',
'blk=myanmarextendedb' => '#/327',
'blk=nabataean' => '#/273',
'blk=nagmundari' => '#/301',
'blk=nandinagari' => '#/328',
'blk=nb' => 'Blk/NB',
'blk=newa' => '#/125',
'blk=newtailue' => '#/274',
'blk=nko' => '#/114',
'blk=noblock' => 'Blk/NB',
'blk=numberforms' => '#/329',
'blk=nushu' => '#/141',
'blk=nyiakengpuachuehmong' => '#/424',
'blk=ocr' => '#/115',
'blk=ogham' => '#/142',
'blk=olchiki' => '#/206',
'blk=oldhungarian' => '#/353',
'blk=olditalic' => '#/275',
'blk=oldnortharabian' => '#/387',
'blk=oldpermic' => '#/276',
'blk=oldpersian' => '#/302',
'blk=oldsogdian' => '#/303',
'blk=oldsoutharabian' => '#/388',
'blk=oldturkic' => '#/277',
'blk=olduyghur' => '#/278',
'blk=opticalcharacterrecognition' => '#/115',
'blk=oriya' => '#/143',
'blk=ornamentaldingbats' => '#/414',
'blk=osage' => '#/144',
'blk=osmanya' => '#/207',
'blk=ottomansiyaqnumbers' => '#/421',
'blk=pahawhhmong' => '#/330',
'blk=palmyrene' => '#/279',
'blk=paucinhau' => '#/280',
'blk=phagspa' => '#/208',
'blk=phaistos' => '#/246',
'blk=phaistosdisc' => '#/246',
'blk=phoenician' => '#/304',
'blk=phoneticext' => '#/331',
'blk=phoneticextensions' => '#/331',
'blk=phoneticextensionssupplement' => '#/375',
'blk=phoneticextsup' => '#/375',
'blk=playingcards' => '#/354',
'blk=privateuse' => '#/116',
'blk=privateusearea' => '#/116',
'blk=psalterpahlavi' => '#/376',
'blk=pua' => '#/116',
'blk=punctuation' => '#/332',
'blk=rejang' => '#/170',
'blk=rumi' => '#/126',
'blk=ruminumeralsymbols' => '#/126',
'blk=runic' => '#/145',
'blk=samaritan' => '#/281',
'blk=saurashtra' => '#/305',
'blk=sharada' => '#/209',
'blk=shavian' => '#/210',
'blk=shorthandformatcontrols' => '#/431',
'blk=siddham' => '#/211',
'blk=sinhala' => '#/212',
'blk=sinhalaarchaicnumbers' => '#/426',
'blk=smallforms' => '#/306',
'blk=smallformvariants' => '#/306',
'blk=smallkanaext' => '#/355',
'blk=smallkanaextension' => '#/355',
'blk=sogdian' => '#/213',
'blk=sorasompeng' => '#/333',
'blk=soyombo' => '#/214',
'blk=spacingmodifierletters' => '#/386',
'blk=specials' => '#/247',
'blk=sundanese' => '#/282',
'blk=sundanesesup' => '#/356',
'blk=sundanesesupplement' => '#/356',
'blk=suparrowsa' => '#/307',
'blk=suparrowsb' => '#/308',
'blk=suparrowsc' => '#/309',
'blk=superandsub' => '#/334',
'blk=superscriptsandsubscripts' => '#/334',
'blk=supmathoperators' => '#/402',
'blk=supplementalarrowsa' => '#/307',
'blk=supplementalarrowsb' => '#/308',
'blk=supplementalarrowsc' => '#/309',
'blk=supplementalmathematicaloperators' => '#/402',
'blk=supplementalpunctuation' => '#/377',
'blk=supplementalsymbolsandpictographs' => '#/432',
'blk=supplementaryprivateuseareaa' => '#/215',
'blk=supplementaryprivateuseareab' => '#/216',
'blk=suppuaa' => '#/215',
'blk=suppuab' => '#/216',
'blk=suppunctuation' => '#/377',
'blk=supsymbolsandpictographs' => '#/432',
'blk=suttonsignwriting' => '#/409',
'blk=sylotinagri' => '#/335',
'blk=symbolsandpictographsexta' => '#/433',
'blk=symbolsandpictographsextendeda' => '#/433',
'blk=symbolsforlegacycomputing' => '#/434',
'blk=syriac' => '#/171',
'blk=syriacsup' => '#/283',
'blk=syriacsupplement' => '#/283',
'blk=tagalog' => '#/217',
'blk=tagbanwa' => '#/248',
'blk=tags' => '#/127',
'blk=taile' => '#/146',
'blk=taitham' => '#/218',
'blk=taiviet' => '#/219',
'blk=taixuanjing' => '#/336',
'blk=taixuanjingsymbols' => '#/336',
'blk=takri' => '#/147',
'blk=tamil' => '#/148',
'blk=tamilsup' => '#/249',
'blk=tamilsupplement' => '#/249',
'blk=tangsa' => '#/172',
'blk=tangut' => '#/173',
'blk=tangutcomponents' => '#/403',
'blk=tangutsup' => '#/284',
'blk=tangutsupplement' => '#/284',
'blk=telugu' => '#/174',
'blk=thaana' => '#/175',
'blk=thai' => '#/128',
'blk=tibetan' => '#/220',
'blk=tifinagh' => '#/250',
'blk=tirhuta' => '#/221',
'blk=toto' => '#/129',
'blk=transportandmap' => '#/389',
'blk=transportandmapsymbols' => '#/389',
'blk=ucas' => '#/130',
'blk=ucasext' => '#/222',
'blk=ucasexta' => '#/251',
'blk=ugaritic' => '#/252',
'blk=unifiedcanadianaboriginalsyllabics' => '#/130',
'blk=unifiedcanadianaboriginalsyllabicsextended' => '#/222',
'blk=unifiedcanadianaboriginalsyllabicsextendeda' => '#/251',
'blk=vai' => '#/117',
'blk=variationselectors' => '#/109',
'blk=variationselectorssupplement' => '#/149',
'blk=vedicext' => '#/253',
'blk=vedicextensions' => '#/253',
'blk=verticalforms' => '#/365',
'blk=vithkuqi' => '#/254',
'blk=vs' => '#/109',
'blk=vssup' => '#/149',
'blk=wancho' => '#/176',
'blk=warangciti' => '#/310',
'blk=yezidi' => '#/177',
'blk=yijing' => '#/178',
'blk=yijinghexagramsymbols' => '#/178',
'blk=yiradicals' => '#/311',
'blk=yisyllables' => '#/337',
'blk=zanabazarsquare' => '#/390',
'blk=znamennymusic' => '#/366',
'blk=znamennymusicalnotation' => '#/366',
'blockelements' => '#/358',
'bopo' => 'Scx/Bopo',
'bopomofo' => 'Scx/Bopo',
'bopomofoext' => '#/312',
'bopomofoextended' => '#/312',
'boxdrawing' => '#/290',
'bpt=c' => 'Bpt/C',
'bpt=close' => 'Bpt/C',
'bpt=n' => 'Bpt/N',
'bpt=none' => 'Bpt/N',
'bpt=o' => 'Bpt/O',
'bpt=open' => 'Bpt/O',
'brah' => '#/662',
'brahmi' => '#/662',
'brai' => '#/181',
'braille' => '#/181',
'braillepatterns' => '#/181',
'bugi' => '#/663',
'buginese' => '#/663',
'buhd' => '#/664',
'buhid' => '#/664',
'byzantinemusic' => '#/368',
'byzantinemusicalsymbols' => '#/368',
'c' => 'Gc/C',
'cakm' => 'Scx/Cakm',
'canadianaboriginal' => '#/665',
'canadiansyllabics' => '#/130',
'cans' => '#/665',
'cari' => '#/666',
'carian' => '#/666',
'cased' => 'Cased/Y',
'cased=f' => '!Cased/Y',
'cased=false' => '!Cased/Y',
'cased=n' => '!Cased/Y',
'cased=no' => '!Cased/Y',
'cased=t' => 'Cased/Y',
'cased=true' => 'Cased/Y',
'cased=y' => 'Cased/Y',
'cased=yes' => 'Cased/Y',
'casedletter' => 'Gc/LC',
'caseignorable' => 'CI/Y',
'caucasianalbanian' => '#/654',
'cc' => '#/439',
'ccc=a' => 'Ccc/A',
'ccc=above' => 'Ccc/A',
'ccc=aboveleft' => 'Ccc/AL',
'ccc=aboveright' => 'Ccc/AR',
'ccc=al' => 'Ccc/AL',
'ccc=ar' => 'Ccc/AR',
'ccc=ata' => '#/21',
'ccc=atar' => 'Ccc/ATAR',
'ccc=atb' => '#/22',
'ccc=atbl' => '#/0',
'ccc=attachedabove' => '#/21',
'ccc=attachedaboveright' => 'Ccc/ATAR',
'ccc=attachedbelow' => '#/22',
'ccc=attachedbelowleft' => '#/0',
'ccc=b' => 'Ccc/B',
'ccc=below' => 'Ccc/B',
'ccc=belowleft' => '#/17',
'ccc=belowright' => 'Ccc/BR',
'ccc=bl' => '#/17',
'ccc=br' => 'Ccc/BR',
'ccc=ccc10' => '#/24',
'ccc=ccc103' => '#/53',
'ccc=ccc107' => '#/54',
'ccc=ccc11' => '#/25',
'ccc=ccc118' => '#/55',
'ccc=ccc12' => '#/26',
'ccc=ccc122' => '#/56',
'ccc=ccc129' => '#/57',
'ccc=ccc13' => '#/27',
'ccc=ccc130' => '#/58',
'ccc=ccc132' => '#/59',
'ccc=ccc133' => '#/0',
'ccc=ccc14' => '#/28',
'ccc=ccc15' => '#/29',
'ccc=ccc16' => '#/30',
'ccc=ccc17' => '#/31',
'ccc=ccc18' => '#/32',
'ccc=ccc19' => '#/33',
'ccc=ccc20' => '#/34',
'ccc=ccc21' => '#/35',
'ccc=ccc22' => '#/36',
'ccc=ccc23' => '#/37',
'ccc=ccc24' => '#/38',
'ccc=ccc25' => '#/39',
'ccc=ccc26' => '#/40',
'ccc=ccc27' => '#/41',
'ccc=ccc28' => '#/42',
'ccc=ccc29' => '#/43',
'ccc=ccc30' => '#/44',
'ccc=ccc31' => '#/45',
'ccc=ccc32' => '#/46',
'ccc=ccc33' => '#/47',
'ccc=ccc34' => '#/48',
'ccc=ccc35' => '#/49',
'ccc=ccc36' => '#/50',
'ccc=ccc84' => '#/51',
'ccc=ccc91' => '#/52',
'ccc=da' => '#/18',
'ccc=db' => 'Ccc/DB',
'ccc=doubleabove' => '#/18',
'ccc=doublebelow' => 'Ccc/DB',
'ccc=hanr' => '#/23',
'ccc=hanreading' => '#/23',
'ccc=iotasubscript' => '#/19',
'ccc=is' => '#/19',
'ccc=kanavoicing' => '#/20',
'ccc=kv' => '#/20',
'ccc=l' => '#/15',
'ccc=left' => '#/15',
'ccc=nk' => 'Ccc/NK',
'ccc=notreordered' => 'Ccc/NR',
'ccc=nr' => 'Ccc/NR',
'ccc=nukta' => 'Ccc/NK',
'ccc=ov' => 'Ccc/OV',
'ccc=overlay' => 'Ccc/OV',
'ccc=r' => '#/16',
'ccc=right' => '#/16',
'ccc=virama' => 'Ccc/VR',
'ccc=vr' => 'Ccc/VR',
'ce' => 'CE/Y',
'ce=f' => '!CE/Y',
'ce=false' => '!CE/Y',
'ce=n' => '!CE/Y',
'ce=no' => '!CE/Y',
'ce=t' => 'CE/Y',
'ce=true' => 'CE/Y',
'ce=y' => 'CE/Y',
'ce=yes' => 'CE/Y',
'cf' => 'Gc/Cf',
'chakma' => 'Scx/Cakm',
'cham' => 'Scx/Cham',
'changeswhencasefolded' => 'CWCF/Y',
'changeswhencasemapped' => 'CWCM/Y',
'changeswhenlowercased' => 'CWL/Y',
'changeswhennfkccasefolded' => 'CWKCF/Y',
'changeswhentitlecased' => 'CWT/Y',
'changeswhenuppercased' => 'CWU/Y',
'cher' => '#/667',
'cherokee' => '#/667',
'cherokeesup' => '#/313',
'cherokeesupplement' => '#/313',
'chesssymbols' => '#/339',
'chorasmian' => '#/668',
'chrs' => '#/668',
'ci' => 'CI/Y',
'ci=f' => '!CI/Y',
'ci=false' => '!CI/Y',
'ci=n' => '!CI/Y',
'ci=no' => '!CI/Y',
'ci=t' => 'CI/Y',
'ci=true' => 'CI/Y',
'ci=y' => 'CI/Y',
'ci=yes' => 'CI/Y',
'cjk' => '#/110',
'cjkcompat' => '#/259',
'cjkcompatforms' => '#/369',
'cjkcompatibility' => '#/259',
'cjkcompatibilityforms' => '#/369',
'cjkcompatibilityideographs' => '#/416',
'cjkcompatibilityideographssupplement' => '#/427',
'cjkcompatideographs' => '#/416',
'cjkcompatideographssup' => '#/427',
'cjkexta' => '#/182',
'cjkextb' => '#/183',
'cjkextc' => '#/184',
'cjkextd' => '#/185',
'cjkexte' => '#/186',
'cjkextf' => '#/187',
'cjkextg' => '#/188',
'cjkexth' => '#/189',
'cjkradicalssup' => '#/370',
'cjkradicalssupplement' => '#/370',
'cjkstrokes' => '#/291',
'cjksymbols' => '#/292',
'cjksymbolsandpunctuation' => '#/292',
'cjkunifiedideographs' => '#/110',
'cjkunifiedideographsextensiona' => '#/182',
'cjkunifiedideographsextensionb' => '#/183',
'cjkunifiedideographsextensionc' => '#/184',
'cjkunifiedideographsextensiond' => '#/185',
'cjkunifiedideographsextensione' => '#/186',
'cjkunifiedideographsextensionf' => '#/187',
'cjkunifiedideographsextensiong' => '#/188',
'cjkunifiedideographsextensionh' => '#/189',
'closepunctuation' => 'Gc/Pe',
'cn' => 'Gc/Cn',
'cntrl' => '#/439',
'co' => '#/440',
'combiningdiacriticalmarks' => '#/344',
'combiningdiacriticalmarksextended' => '#/380',
'combiningdiacriticalmarksforsymbols' => '#/428',
'combiningdiacriticalmarkssupplement' => '#/381',
'combininghalfmarks' => '#/262',
'combiningmark' => 'Gc/M',
'combiningmarksforsymbols' => '#/428',
'common' => 'Scx/Zyyy',
'commonindicnumberforms' => '#/396',
'compatjamo' => '#/294',
'compex' => 'CompEx/Y',
'compex=f' => '!CompEx/Y',
'compex=false' => '!CompEx/Y',
'compex=n' => '!CompEx/Y',
'compex=no' => '!CompEx/Y',
'compex=t' => 'CompEx/Y',
'compex=true' => 'CompEx/Y',
'compex=y' => 'CompEx/Y',
'compex=yes' => 'CompEx/Y',
'compositionexclusion' => 'CE/Y',
'connectorpunctuation' => 'Gc/Pc',
'control' => '#/439',
'controlpictures' => '#/378',
'copt' => 'Scx/Copt',
'coptic' => 'Scx/Copt',
'copticepactnumbers' => '#/410',
'countingrod' => '#/314',
'countingrodnumerals' => '#/314',
'cpmn' => '#/669',
'cprt' => 'Scx/Cprt',
'cs' => '#/14',
'cuneiform' => 'Scx/Xsux',
'cuneiformnumbers' => '#/391',
'cuneiformnumbersandpunctuation' => '#/391',
'currencysymbol' => 'Gc/Sc',
'currencysymbols' => '#/379',
'cwcf' => 'CWCF/Y',
'cwcf=f' => '!CWCF/Y',
'cwcf=false' => '!CWCF/Y',
'cwcf=n' => '!CWCF/Y',
'cwcf=no' => '!CWCF/Y',
'cwcf=t' => 'CWCF/Y',
'cwcf=true' => 'CWCF/Y',
'cwcf=y' => 'CWCF/Y',
'cwcf=yes' => 'CWCF/Y',
'cwcm' => 'CWCM/Y',
'cwcm=f' => '!CWCM/Y',
'cwcm=false' => '!CWCM/Y',
'cwcm=n' => '!CWCM/Y',
'cwcm=no' => '!CWCM/Y',
'cwcm=t' => 'CWCM/Y',
'cwcm=true' => 'CWCM/Y',
'cwcm=y' => 'CWCM/Y',
'cwcm=yes' => 'CWCM/Y',
'cwkcf' => 'CWKCF/Y',
'cwkcf=f' => '!CWKCF/Y',
'cwkcf=false' => '!CWKCF/Y',
'cwkcf=n' => '!CWKCF/Y',
'cwkcf=no' => '!CWKCF/Y',
'cwkcf=t' => 'CWKCF/Y',
'cwkcf=true' => 'CWKCF/Y',
'cwkcf=y' => 'CWKCF/Y',
'cwkcf=yes' => 'CWKCF/Y',
'cwl' => 'CWL/Y',
'cwl=f' => '!CWL/Y',
'cwl=false' => '!CWL/Y',
'cwl=n' => '!CWL/Y',
'cwl=no' => '!CWL/Y',
'cwl=t' => 'CWL/Y',
'cwl=true' => 'CWL/Y',
'cwl=y' => 'CWL/Y',
'cwl=yes' => 'CWL/Y',
'cwt' => 'CWT/Y',
'cwt=f' => '!CWT/Y',
'cwt=false' => '!CWT/Y',
'cwt=n' => '!CWT/Y',
'cwt=no' => '!CWT/Y',
'cwt=t' => 'CWT/Y',
'cwt=true' => 'CWT/Y',
'cwt=y' => 'CWT/Y',
'cwt=yes' => 'CWT/Y',
'cwu' => 'CWU/Y',
'cwu=f' => '!CWU/Y',
'cwu=false' => '!CWU/Y',
'cwu=n' => '!CWU/Y',
'cwu=no' => '!CWU/Y',
'cwu=t' => 'CWU/Y',
'cwu=true' => 'CWU/Y',
'cwu=y' => 'CWU/Y',
'cwu=yes' => 'CWU/Y',
'cypriot' => 'Scx/Cprt',
'cypriotsyllabary' => '#/392',
'cyprominoan' => '#/669',
'cyrillic' => 'Scx/Cyrl',
'cyrillicexta' => '#/340',
'cyrillicextb' => '#/341',
'cyrillicextc' => '#/342',
'cyrillicextd' => '#/343',
'cyrillicextendeda' => '#/340',
'cyrillicextendedb' => '#/341',
'cyrillicextendedc' => '#/342',
'cyrillicextendedd' => '#/343',
'cyrillicsup' => '#/316',
'cyrillicsupplement' => '#/316',
'cyrillicsupplementary' => '#/316',
'cyrl' => 'Scx/Cyrl',
'dash' => 'Dash/Y',
'dash=f' => '!Dash/Y',
'dash=false' => '!Dash/Y',
'dash=n' => '!Dash/Y',
'dash=no' => '!Dash/Y',
'dash=t' => 'Dash/Y',
'dash=true' => 'Dash/Y',
'dash=y' => 'Dash/Y',
'dash=yes' => 'Dash/Y',
'dashpunctuation' => 'Gc/Pd',
'decimalnumber' => 'Gc/Nd',
'defaultignorablecodepoint' => 'DI/Y',
'dep' => 'Dep/Y',
'dep=f' => '!Dep/Y',
'dep=false' => '!Dep/Y',
'dep=n' => '!Dep/Y',
'dep=no' => '!Dep/Y',
'dep=t' => 'Dep/Y',
'dep=true' => 'Dep/Y',
'dep=y' => 'Dep/Y',
'dep=yes' => 'Dep/Y',
'deprecated' => 'Dep/Y',
'deseret' => '#/190',
'deva' => 'Scx/Deva',
'devanagari' => 'Scx/Deva',
'devanagariext' => '#/359',
'devanagariexta' => '#/371',
'devanagariextended' => '#/359',
'devanagariextendeda' => '#/371',
'di' => 'DI/Y',
'di=f' => '!DI/Y',
'di=false' => '!DI/Y',
'di=n' => '!DI/Y',
'di=no' => '!DI/Y',
'di=t' => 'DI/Y',
'di=true' => 'DI/Y',
'di=y' => 'DI/Y',
'di=yes' => 'DI/Y',
'dia' => 'Dia/Y',
'dia=f' => '!Dia/Y',
'dia=false' => '!Dia/Y',
'dia=n' => '!Dia/Y',
'dia=no' => '!Dia/Y',
'dia=t' => 'Dia/Y',
'dia=true' => 'Dia/Y',
'dia=y' => 'Dia/Y',
'dia=yes' => 'Dia/Y',
'diacritic' => 'Dia/Y',
'diacriticals' => '#/344',
'diacriticalsext' => '#/380',
'diacriticalsforsymbols' => '#/428',
'diacriticalssup' => '#/381',
'diak' => 'Scx/Diak',
'digit' => 'Gc/Nd',
'dingbats' => '#/231',
'divesakuru' => 'Scx/Diak',
'dogr' => '#/670',
'dogra' => '#/670',
'domino' => '#/156',
'dominotiles' => '#/156',
'dsrt' => '#/190',
'dt=can' => 'NFDQC/N',
'dt=canonical' => 'NFDQC/N',
'dt=circle' => 'Dt/Enc',
'dt=com' => 'Dt/Com',
'dt=compat' => 'Dt/Com',
'dt=enc' => 'Dt/Enc',
'dt=fin' => 'Dt/Fin',
'dt=final' => 'Dt/Fin',
'dt=font' => 'Dt/Font',
'dt=fra' => '#/436',
'dt=fraction' => '#/436',
'dt=init' => 'Dt/Init',
'dt=initial' => 'Dt/Init',
'dt=iso' => 'Dt/Iso',
'dt=isolated' => 'Dt/Iso',
'dt=med' => 'Dt/Med',
'dt=medial' => 'Dt/Med',
'dt=nar' => 'Dt/Nar',
'dt=narrow' => 'Dt/Nar',
'dt=nb' => 'Dt/Nb',
'dt=nobreak' => 'Dt/Nb',
'dt=noncanon' => 'Dt/NonCanon',
'dt=noncanonical' => 'Dt/NonCanon',
'dt=none' => 'NFKDQC/Y',
'dt=small' => '#/437',
'dt=sml' => '#/437',
'dt=sqr' => 'Dt/Sqr',
'dt=square' => 'Dt/Sqr',
'dt=sub' => 'Dt/Sub',
'dt=sup' => 'Dt/Sup',
'dt=super' => 'Dt/Sup',
'dt=vert' => 'Dt/Vert',
'dt=vertical' => 'Dt/Vert',
'dt=wide' => '#/438',
'dupl' => 'Scx/Dupl',
'duployan' => 'Scx/Dupl',
'ea=a' => 'Ea/A',
'ea=ambiguous' => 'Ea/A',
'ea=f' => '#/438',
'ea=fullwidth' => '#/438',
'ea=h' => 'Ea/H',
'ea=halfwidth' => 'Ea/H',
'ea=n' => 'Ea/N',
'ea=na' => 'Ea/Na',
'ea=narrow' => 'Ea/Na',
'ea=neutral' => 'Ea/N',
'ea=w' => 'Ea/W',
'ea=wide' => 'Ea/W',
'earlydynasticcuneiform' => '#/429',
'ebase' => 'EBase/Y',
'ebase=f' => '!EBase/Y',
'ebase=false' => '!EBase/Y',
'ebase=n' => '!EBase/Y',
'ebase=no' => '!EBase/Y',
'ebase=t' => 'EBase/Y',
'ebase=true' => 'EBase/Y',
'ebase=y' => 'EBase/Y',
'ebase=yes' => 'EBase/Y',
'ecomp' => 'EComp/Y',
'ecomp=f' => '!EComp/Y',
'ecomp=false' => '!EComp/Y',
'ecomp=n' => '!EComp/Y',
'ecomp=no' => '!EComp/Y',
'ecomp=t' => 'EComp/Y',
'ecomp=true' => 'EComp/Y',
'ecomp=y' => 'EComp/Y',
'ecomp=yes' => 'EComp/Y',
'egyp' => '#/671',
'egyptianhieroglyphformatcontrols' => '#/435',
'egyptianhieroglyphs' => '#/671',
'elba' => '#/672',
'elbasan' => '#/672',
'elym' => '#/673',
'elymaic' => '#/673',
'emod' => '#/61',
'emod=f' => '#/!61',
'emod=false' => '#/!61',
'emod=n' => '#/!61',
'emod=no' => '#/!61',
'emod=t' => '#/61',
'emod=true' => '#/61',
'emod=y' => '#/61',
'emod=yes' => '#/61',
'emoji' => 'Emoji/Y',
'emoji=f' => '!Emoji/Y',
'emoji=false' => '!Emoji/Y',
'emoji=n' => '!Emoji/Y',
'emoji=no' => '!Emoji/Y',
'emoji=t' => 'Emoji/Y',
'emoji=true' => 'Emoji/Y',
'emoji=y' => 'Emoji/Y',
'emoji=yes' => 'Emoji/Y',
'emojicomponent' => 'EComp/Y',
'emojimodifier' => '#/61',
'emojimodifierbase' => 'EBase/Y',
'emojipresentation' => 'EPres/Y',
'emoticons' => '#/261',
'enclosedalphanum' => '#/393',
'enclosedalphanumerics' => '#/393',
'enclosedalphanumericsupplement' => '#/418',
'enclosedalphanumsup' => '#/418',
'enclosedcjk' => '#/317',
'enclosedcjklettersandmonths' => '#/317',
'enclosedideographicsup' => '#/430',
'enclosedideographicsupplement' => '#/430',
'enclosingmark' => 'Gc/Me',
'epres' => 'EPres/Y',
'epres=f' => '!EPres/Y',
'epres=false' => '!EPres/Y',
'epres=n' => '!EPres/Y',
'epres=no' => '!EPres/Y',
'epres=t' => 'EPres/Y',
'epres=true' => 'EPres/Y',
'epres=y' => 'EPres/Y',
'epres=yes' => 'EPres/Y',
'ethi' => 'Scx/Ethi',
'ethiopic' => 'Scx/Ethi',
'ethiopicext' => '#/318',
'ethiopicexta' => '#/345',
'ethiopicextb' => '#/346',
'ethiopicextended' => '#/318',
'ethiopicextendeda' => '#/345',
'ethiopicextendedb' => '#/346',
'ethiopicsup' => '#/319',
'ethiopicsupplement' => '#/319',
'ext' => 'Ext/Y',
'ext=f' => '!Ext/Y',
'ext=false' => '!Ext/Y',
'ext=n' => '!Ext/Y',
'ext=no' => '!Ext/Y',
'ext=t' => 'Ext/Y',
'ext=true' => 'Ext/Y',
'ext=y' => 'Ext/Y',
'ext=yes' => 'Ext/Y',
'extendedpictographic' => 'ExtPict/Y',
'extender' => 'Ext/Y',
'extpict' => 'ExtPict/Y',
'extpict=f' => '!ExtPict/Y',
'extpict=false' => '!ExtPict/Y',
'extpict=n' => '!ExtPict/Y',
'extpict=no' => '!ExtPict/Y',
'extpict=t' => 'ExtPict/Y',
'extpict=true' => 'ExtPict/Y',
'extpict=y' => 'ExtPict/Y',
'extpict=yes' => 'ExtPict/Y',
'finalpunctuation' => 'Gc/Pf',
'format' => 'Gc/Cf',
'fullcompositionexclusion' => 'CompEx/Y',
'gc=c' => 'Gc/C',
'gc=casedletter' => 'Gc/LC',
'gc=cc' => '#/439',
'gc=cf' => 'Gc/Cf',
'gc=closepunctuation' => 'Gc/Pe',
'gc=cn' => 'Gc/Cn',
'gc=cntrl' => '#/439',
'gc=co' => '#/440',
'gc=combiningmark' => 'Gc/M',
'gc=connectorpunctuation' => 'Gc/Pc',
'gc=control' => '#/439',
'gc=cs' => '#/14',
'gc=currencysymbol' => 'Gc/Sc',
'gc=dashpunctuation' => 'Gc/Pd',
'gc=decimalnumber' => 'Gc/Nd',
'gc=digit' => 'Gc/Nd',
'gc=enclosingmark' => 'Gc/Me',
'gc=finalpunctuation' => 'Gc/Pf',
'gc=format' => 'Gc/Cf',
'gc=initialpunctuation' => 'Gc/Pi',
'gc=l' => 'Gc/L',
'gc=l&' => 'Gc/LC',
'gc=l_' => 'Gc/LC',
'gc=lc' => 'Gc/LC',
'gc=letter' => 'Gc/L',
'gc=letternumber' => 'Gc/Nl',
'gc=lineseparator' => '#/441',
'gc=ll' => 'Gc/Ll',
'gc=lm' => 'Gc/Lm',
'gc=lo' => 'Gc/Lo',
'gc=lowercaseletter' => 'Gc/Ll',
'gc=lt' => 'Perl/Title',
'gc=lu' => 'Gc/Lu',
'gc=m' => 'Gc/M',
'gc=mark' => 'Gc/M',
'gc=mathsymbol' => 'Gc/Sm',
'gc=mc' => 'Gc/Mc',
'gc=me' => 'Gc/Me',
'gc=mn' => 'Gc/Mn',
'gc=modifierletter' => 'Gc/Lm',
'gc=modifiersymbol' => 'Gc/Sk',
'gc=n' => 'Gc/N',
'gc=nd' => 'Gc/Nd',
'gc=nl' => 'Gc/Nl',
'gc=no' => 'Gc/No',
'gc=nonspacingmark' => 'Gc/Mn',
'gc=number' => 'Gc/N',
'gc=openpunctuation' => 'Gc/Ps',
'gc=other' => 'Gc/C',
'gc=otherletter' => 'Gc/Lo',
'gc=othernumber' => 'Gc/No',
'gc=otherpunctuation' => 'Gc/Po',
'gc=othersymbol' => 'Gc/So',
'gc=p' => 'Gc/P',
'gc=paragraphseparator' => '#/442',
'gc=pc' => 'Gc/Pc',
'gc=pd' => 'Gc/Pd',
'gc=pe' => 'Gc/Pe',
'gc=pf' => 'Gc/Pf',
'gc=pi' => 'Gc/Pi',
'gc=po' => 'Gc/Po',
'gc=privateuse' => '#/440',
'gc=ps' => 'Gc/Ps',
'gc=punct' => 'Gc/P',
'gc=punctuation' => 'Gc/P',
'gc=s' => 'Gc/S',
'gc=sc' => 'Gc/Sc',
'gc=separator' => 'Gc/Z',
'gc=sk' => 'Gc/Sk',
'gc=sm' => 'Gc/Sm',
'gc=so' => 'Gc/So',
'gc=spaceseparator' => 'Gc/Zs',
'gc=spacingmark' => 'Gc/Mc',
'gc=surrogate' => '#/14',
'gc=symbol' => 'Gc/S',
'gc=titlecaseletter' => 'Perl/Title',
'gc=unassigned' => 'Gc/Cn',
'gc=uppercaseletter' => 'Gc/Lu',
'gc=z' => 'Gc/Z',
'gc=zl' => '#/441',
'gc=zp' => '#/442',
'gc=zs' => 'Gc/Zs',
'gcb=cn' => 'GCB/CN',
'gcb=control' => 'GCB/CN',
'gcb=cr' => '#/66',
'gcb=eb' => '#/0',
'gcb=ebase' => '#/0',
'gcb=ebasegaz' => '#/0',
'gcb=ebg' => '#/0',
'gcb=em' => '#/0',
'gcb=emodifier' => '#/0',
'gcb=ex' => 'GCB/EX',
'gcb=extend' => 'GCB/EX',
'gcb=gaz' => '#/0',
'gcb=glueafterzwj' => '#/0',
'gcb=l' => '#/63',
'gcb=lf' => '#/67',
'gcb=lv' => 'GCB/LV',
'gcb=lvt' => 'GCB/LVT',
'gcb=other' => 'GCB/XX',
'gcb=pp' => 'GCB/PP',
'gcb=prepend' => 'GCB/PP',
'gcb=regionalindicator' => '#/68',
'gcb=ri' => '#/68',
'gcb=sm' => 'GCB/SM',
'gcb=spacingmark' => 'GCB/SM',
'gcb=t' => '#/64',
'gcb=v' => '#/65',
'gcb=xx' => 'GCB/XX',
'gcb=zwj' => '#/69',
'generalpunctuation' => '#/332',
'geometricshapes' => '#/382',
'geometricshapesext' => '#/411',
'geometricshapesextended' => '#/411',
'geor' => 'Scx/Geor',
'georgian' => 'Scx/Geor',
'georgianext' => '#/320',
'georgianextended' => '#/320',
'georgiansup' => '#/321',
'georgiansupplement' => '#/321',
'glag' => 'Scx/Glag',
'glagolitic' => 'Scx/Glag',
'glagoliticsup' => '#/360',
'glagoliticsupplement' => '#/360',
'gong' => 'Scx/Gong',
'gonm' => 'Scx/Gonm',
'goth' => '#/674',
'gothic' => '#/674',
'gran' => 'Scx/Gran',
'grantha' => 'Scx/Gran',
'graph' => 'Perl/Graph',
'graphemebase' => 'GrBase/Y',
'graphemeextend' => 'GrExt/Y',
'grbase' => 'GrBase/Y',
'grbase=f' => '!GrBase/Y',
'grbase=false' => '!GrBase/Y',
'grbase=n' => '!GrBase/Y',
'grbase=no' => '!GrBase/Y',
'grbase=t' => 'GrBase/Y',
'grbase=true' => 'GrBase/Y',
'grbase=y' => 'GrBase/Y',
'grbase=yes' => 'GrBase/Y',
'greek' => 'Scx/Grek',
'greekandcoptic' => '#/137',
'greekext' => '#/235',
'greekextended' => '#/235',
'grek' => 'Scx/Grek',
'grext' => 'GrExt/Y',
'grext=f' => '!GrExt/Y',
'grext=false' => '!GrExt/Y',
'grext=n' => '!GrExt/Y',
'grext=no' => '!GrExt/Y',
'grext=t' => 'GrExt/Y',
'grext=true' => 'GrExt/Y',
'grext=y' => 'GrExt/Y',
'grext=yes' => 'GrExt/Y',
'gujarati' => 'Scx/Gujr',
'gujr' => 'Scx/Gujr',
'gunjalagondi' => 'Scx/Gong',
'gurmukhi' => 'Scx/Guru',
'guru' => 'Scx/Guru',
'halfandfullforms' => '#/394',
'halfmarks' => '#/262',
'halfwidthandfullwidthforms' => '#/394',
'han' => 'Scx/Han',
'hang' => 'Scx/Hang',
'hangul' => 'Scx/Hang',
'hangulcompatibilityjamo' => '#/294',
'hanguljamo' => '#/120',
'hanguljamoextendeda' => '#/239',
'hanguljamoextendedb' => '#/240',
'hangulsyllables' => '#/158',
'hani' => 'Scx/Han',
'hanifirohingya' => 'Scx/Rohg',
'hano' => '#/675',
'hanunoo' => '#/675',
'hatr' => '#/676',
'hatran' => '#/676',
'hebr' => 'Scx/Hebr',
'hebrew' => 'Scx/Hebr',
'hex' => 'Hex/Y',
'hex=f' => '!Hex/Y',
'hex=false' => '!Hex/Y',
'hex=n' => '!Hex/Y',
'hex=no' => '!Hex/Y',
'hex=t' => 'Hex/Y',
'hex=true' => 'Hex/Y',
'hex=y' => 'Hex/Y',
'hex=yes' => 'Hex/Y',
'hexdigit' => 'Hex/Y',
'highprivateusesurrogates' => '#/395',
'highpusurrogates' => '#/395',
'highsurrogates' => '#/373',
'hira' => 'Scx/Hira',
'hiragana' => 'Scx/Hira',
'hluw' => '#/677',
'hmng' => 'Scx/Hmng',
'hmnp' => 'Scx/Hmnp',
'horizspace' => 'Perl/Blank',
'hst=l' => '#/63',
'hst=leadingjamo' => '#/63',
'hst=lv' => 'GCB/LV',
'hst=lvsyllable' => 'GCB/LV',
'hst=lvt' => 'GCB/LVT',
'hst=lvtsyllable' => 'GCB/LVT',
'hst=na' => 'Hst/NA',
'hst=notapplicable' => 'Hst/NA',
'hst=t' => '#/64',
'hst=trailingjamo' => '#/64',
'hst=v' => '#/65',
'hst=voweljamo' => '#/65',
'hung' => '#/678',
'hyphen' => 'Hyphen/T',
'hyphen=f' => '!Hyphen/T',
'hyphen=false' => '!Hyphen/T',
'hyphen=n' => '!Hyphen/T',
'hyphen=no' => '!Hyphen/T',
'hyphen=t' => 'Hyphen/T',
'hyphen=true' => 'Hyphen/T',
'hyphen=y' => 'Hyphen/T',
'hyphen=yes' => 'Hyphen/T',
'idc' => 'IDC/Y',
'idc=f' => '!IDC/Y',
'idc=false' => '!IDC/Y',
'idc=n' => '!IDC/Y',
'idc=no' => '!IDC/Y',
'idc=t' => 'IDC/Y',
'idc=true' => 'IDC/Y',
'idc=y' => 'IDC/Y',
'idc=yes' => 'IDC/Y',
'idcontinue' => 'IDC/Y',
'identifierstatus=allowed' => 'IdStatus/Allowed',
'identifierstatus=restricted' => 'IdStatus/Restrict',
'identifiertype=defaultignorable' => 'IdType/DefaultI',
'identifiertype=deprecated' => 'Dep/Y',
'identifiertype=exclusion' => 'IdType/Exclusio',
'identifiertype=inclusion' => 'IdType/Inclusio',
'identifiertype=limiteduse' => 'IdType/LimitedU',
'identifiertype=notcharacter' => 'IdType/NotChara',
'identifiertype=notnfkc' => 'IdType/NotNFKC',
'identifiertype=notxid' => 'IdType/NotXID',
'identifiertype=obsolete' => 'IdType/Obsolete',
'identifiertype=recommended' => 'IdType/Recommen',
'identifiertype=technical' => 'IdType/Technica',
'identifiertype=uncommonuse' => 'IdType/Uncommon',
'ideo' => 'Ideo/Y',
'ideo=f' => '!Ideo/Y',
'ideo=false' => '!Ideo/Y',
'ideo=n' => '!Ideo/Y',
'ideo=no' => '!Ideo/Y',
'ideo=t' => 'Ideo/Y',
'ideo=true' => 'Ideo/Y',
'ideo=y' => 'Ideo/Y',
'ideo=yes' => 'Ideo/Y',
'ideographic' => 'Ideo/Y',
'ideographicdescriptioncharacters' => '#/111',
'ideographicsymbols' => '#/412',
'ideographicsymbolsandpunctuation' => '#/412',
'ids' => 'IDS/Y',
'ids=f' => '!IDS/Y',
'ids=false' => '!IDS/Y',
'ids=n' => '!IDS/Y',
'ids=no' => '!IDS/Y',
'ids=t' => 'IDS/Y',
'ids=true' => 'IDS/Y',
'ids=y' => 'IDS/Y',
'ids=yes' => 'IDS/Y',
'idsb' => '#/70',
'idsb=f' => '#/!70',
'idsb=false' => '#/!70',
'idsb=n' => '#/!70',
'idsb=no' => '#/!70',
'idsb=t' => '#/70',
'idsb=true' => '#/70',
'idsb=y' => '#/70',
'idsb=yes' => '#/70',
'idsbinaryoperator' => '#/70',
'idst' => '#/72',
'idst=f' => '#/!72',
'idst=false' => '#/!72',
'idst=n' => '#/!72',
'idst=no' => '#/!72',
'idst=t' => '#/72',
'idst=true' => '#/72',
'idst=y' => '#/72',
'idst=yes' => '#/72',
'idstart' => 'IDS/Y',
'idstrinaryoperator' => '#/72',
'imperialaramaic' => '#/656',
'in=na' => 'Age/NA',
'in=unassigned' => 'Age/NA',
'in=v100' => 'In/10_0',
'in=v11' => 'Age/V11',
'in=v110' => 'In/11_0',
'in=v120' => 'In/12_0',
'in=v121' => 'In/12_1',
'in=v130' => 'In/13_0',
'in=v140' => 'In/14_0',
'in=v150' => 'In/15_0',
'in=v20' => 'In/2_0',
'in=v21' => 'In/2_1',
'in=v30' => 'In/3_0',
'in=v31' => 'In/3_1',
'in=v32' => 'In/3_2',
'in=v40' => 'In/4_0',
'in=v41' => 'In/4_1',
'in=v50' => 'In/5_0',
'in=v51' => 'In/5_1',
'in=v52' => 'In/5_2',
'in=v60' => 'In/6_0',
'in=v61' => 'In/6_1',
'in=v62' => 'In/6_2',
'in=v63' => 'In/6_3',
'in=v70' => 'In/7_0',
'in=v80' => 'In/8_0',
'in=v90' => 'In/9_0',
'inadlam' => '#/132',
'inaegeannumbers' => '#/357',
'inahom' => '#/118',
'inalchemical' => '#/285',
'inalchemicalsymbols' => '#/285',
'inalphabeticpf' => '#/338',
'inalphabeticpresentationforms' => '#/338',
'inanatolianhieroglyphs' => '#/422',
'inancientgreekmusic' => '#/404',
'inancientgreekmusicalnotation' => '#/404',
'inancientgreeknumbers' => '#/415',
'inancientsymbols' => '#/367',
'inarabic' => '#/150',
'inarabicexta' => '#/286',
'inarabicextb' => '#/287',
'inarabicextc' => '#/288',
'inarabicextendeda' => '#/286',
'inarabicextendedb' => '#/287',
'inarabicextendedc' => '#/288',
'inarabicmath' => '#/289',
'inarabicmathematicalalphabeticsymbols' => '#/289',
'inarabicpfa' => '#/255',
'inarabicpfb' => '#/256',
'inarabicpresentationformsa' => '#/255',
'inarabicpresentationformsb' => '#/256',
'inarabicsup' => '#/257',
'inarabicsupplement' => '#/257',
'inarmenian' => '#/223',
'inarrows' => '#/151',
'inascii' => '#/131',
'inavestan' => '#/179',
'inbalinese' => '#/224',
'inbamum' => '#/133',
'inbamumsup' => '#/225',
'inbamumsupplement' => '#/225',
'inbasiclatin' => '#/131',
'inbassavah' => '#/226',
'inbatak' => '#/134',
'inbengali' => '#/180',
'inbhaiksuki' => '#/258',
'inblockelements' => '#/358',
'inbopomofo' => '#/227',
'inbopomofoext' => '#/312',
'inbopomofoextended' => '#/312',
'inboxdrawing' => '#/290',
'inbrahmi' => '#/152',
'inbraille' => '#/181',
'inbraillepatterns' => '#/181',
'inbuginese' => '#/228',
'inbuhid' => '#/135',
'inbyzantinemusic' => '#/368',
'inbyzantinemusicalsymbols' => '#/368',
'incanadiansyllabics' => '#/130',
'incarian' => '#/153',
'incaucasianalbanian' => '#/405',
'inchakma' => '#/154',
'incham' => '#/119',
'incherokee' => '#/229',
'incherokeesup' => '#/313',
'incherokeesupplement' => '#/313',
'inchesssymbols' => '#/339',
'inchorasmian' => '#/293',
'incjk' => '#/110',
'incjkcompat' => '#/259',
'incjkcompatforms' => '#/369',
'incjkcompatibility' => '#/259',
'incjkcompatibilityforms' => '#/369',
'incjkcompatibilityideographs' => '#/416',
'incjkcompatibilityideographssupplement' => '#/427',
'incjkcompatideographs' => '#/416',
'incjkcompatideographssup' => '#/427',
'incjkexta' => '#/182',
'incjkextb' => '#/183',
'incjkextc' => '#/184',
'incjkextd' => '#/185',
'incjkexte' => '#/186',
'incjkextf' => '#/187',
'incjkextg' => '#/188',
'incjkexth' => '#/189',
'incjkradicalssup' => '#/370',
'incjkradicalssupplement' => '#/370',
'incjkstrokes' => '#/291',
'incjksymbols' => '#/292',
'incjksymbolsandpunctuation' => '#/292',
'incjkunifiedideographs' => '#/110',
'incjkunifiedideographsextensiona' => '#/182',
'incjkunifiedideographsextensionb' => '#/183',
'incjkunifiedideographsextensionc' => '#/184',
'incjkunifiedideographsextensiond' => '#/185',
'incjkunifiedideographsextensione' => '#/186',
'incjkunifiedideographsextensionf' => '#/187',
'incjkunifiedideographsextensiong' => '#/188',
'incjkunifiedideographsextensionh' => '#/189',
'incombiningdiacriticalmarks' => '#/344',
'incombiningdiacriticalmarksextended' => '#/380',
'incombiningdiacriticalmarksforsymbols' => '#/428',
'incombiningdiacriticalmarkssupplement' => '#/381',
'incombininghalfmarks' => '#/262',
'incombiningmarksforsymbols' => '#/428',
'incommonindicnumberforms' => '#/396',
'incompatjamo' => '#/294',
'incontrolpictures' => '#/378',
'incoptic' => '#/155',
'incopticepactnumbers' => '#/410',
'incountingrod' => '#/314',
'incountingrodnumerals' => '#/314',
'incuneiform' => '#/260',
'incuneiformnumbers' => '#/391',
'incuneiformnumbersandpunctuation' => '#/391',
'incurrencysymbols' => '#/379',
'incypriotsyllabary' => '#/392',
'incyprominoan' => '#/315',
'incyrillic' => '#/230',
'incyrillicexta' => '#/340',
'incyrillicextb' => '#/341',
'incyrillicextc' => '#/342',
'incyrillicextd' => '#/343',
'incyrillicextendeda' => '#/340',
'incyrillicextendedb' => '#/341',
'incyrillicextendedc' => '#/342',
'incyrillicextendedd' => '#/343',
'incyrillicsup' => '#/316',
'incyrillicsupplement' => '#/316',
'incyrillicsupplementary' => '#/316',
'indeseret' => '#/190',
'indevanagari' => '#/295',
'indevanagariext' => '#/359',
'indevanagariexta' => '#/371',
'indevanagariextended' => '#/359',
'indevanagariextendeda' => '#/371',
'indiacriticals' => '#/344',
'indiacriticalsext' => '#/380',
'indiacriticalsforsymbols' => '#/428',
'indiacriticalssup' => '#/381',
'indicnumberforms' => '#/396',
'indicsiyaqnumbers' => '#/406',
'indingbats' => '#/231',
'indivesakuru' => '#/296',
'indogra' => '#/136',
'indomino' => '#/156',
'indominotiles' => '#/156',
'induployan' => '#/232',
'inearlydynasticcuneiform' => '#/429',
'inegyptianhieroglyphformatcontrols' => '#/435',
'inegyptianhieroglyphs' => '#/417',
'inelbasan' => '#/191',
'inelymaic' => '#/192',
'inemoticons' => '#/261',
'inenclosedalphanum' => '#/393',
'inenclosedalphanumerics' => '#/393',
'inenclosedalphanumericsupplement' => '#/418',
'inenclosedalphanumsup' => '#/418',
'inenclosedcjk' => '#/317',
'inenclosedcjklettersandmonths' => '#/317',
'inenclosedideographicsup' => '#/430',
'inenclosedideographicsupplement' => '#/430',
'inethiopic' => '#/233',
'inethiopicext' => '#/318',
'inethiopicexta' => '#/345',
'inethiopicextb' => '#/346',
'inethiopicextended' => '#/318',
'inethiopicextendeda' => '#/345',
'inethiopicextendedb' => '#/346',
'inethiopicsup' => '#/319',
'inethiopicsupplement' => '#/319',
'ingeneralpunctuation' => '#/332',
'ingeometricshapes' => '#/382',
'ingeometricshapesext' => '#/411',
'ingeometricshapesextended' => '#/411',
'ingeorgian' => '#/234',
'ingeorgianext' => '#/320',
'ingeorgianextended' => '#/320',
'ingeorgiansup' => '#/321',
'ingeorgiansupplement' => '#/321',
'inglagolitic' => '#/297',
'inglagoliticsup' => '#/360',
'inglagoliticsupplement' => '#/360',
'ingothic' => '#/157',
'ingrantha' => '#/193',
'ingreek' => '#/137',
'ingreekandcoptic' => '#/137',
'ingreekext' => '#/235',
'ingreekextended' => '#/235',
'ingujarati' => '#/236',
'ingunjalagondi' => '#/347',
'ingurmukhi' => '#/237',
'inhalfandfullforms' => '#/394',
'inhalfmarks' => '#/262',
'inhalfwidthandfullwidthforms' => '#/394',
'inhangul' => '#/158',
'inhangulcompatibilityjamo' => '#/294',
'inhanguljamo' => '#/120',
'inhanguljamoextendeda' => '#/239',
'inhanguljamoextendedb' => '#/240',
'inhangulsyllables' => '#/158',
'inhanifirohingya' => '#/372',
'inhanunoo' => '#/194',
'inhatran' => '#/159',
'inhebrew' => '#/160',
'inherited' => 'Scx/Zinh',
'inhighprivateusesurrogates' => '#/395',
'inhighpusurrogates' => '#/395',
'inhighsurrogates' => '#/373',
'inhiragana' => '#/238',
'inidc' => '#/111',
'inideographicdescriptioncharacters' => '#/111',
'inideographicsymbols' => '#/412',
'inideographicsymbolsandpunctuation' => '#/412',
'inimperialaramaic' => '#/383',
'inindicnumberforms' => '#/396',
'inindicsiyaqnumbers' => '#/406',
'ininscriptionalpahlavi' => '#/423',
'ininscriptionalparthian' => '#/425',
'inipaext' => '#/161',
'inipaextensions' => '#/161',
'initialpunctuation' => 'Gc/Pi',
'injamo' => '#/120',
'injamoexta' => '#/239',
'injamoextb' => '#/240',
'injavanese' => '#/241',
'inkaithi' => '#/162',
'inkaktoviknumerals' => '#/397',
'inkanaexta' => '#/242',
'inkanaextb' => '#/243',
'inkanaextendeda' => '#/242',
'inkanaextendedb' => '#/243',
'inkanasup' => '#/195',
'inkanasupplement' => '#/195',
'inkanbun' => '#/163',
'inkangxi' => '#/164',
'inkangxiradicals' => '#/164',
'inkannada' => '#/196',
'inkatakana' => '#/244',
'inkatakanaext' => '#/322',
'inkatakanaphoneticextensions' => '#/322',
'inkawi' => '#/121',
'inkayahli' => '#/197',
'inkharoshthi' => '#/298',
'inkhitansmallscript' => '#/407',
'inkhmer' => '#/138',
'inkhmersymbols' => '#/348',
'inkhojki' => '#/165',
'inkhudawadi' => '#/263',
'inlao' => '#/112',
'inlatin1' => '#/166',
'inlatin1sup' => '#/166',
'inlatin1supplement' => '#/166',
'inlatinexta' => '#/264',
'inlatinextadditional' => '#/413',
'inlatinextb' => '#/265',
'inlatinextc' => '#/266',
'inlatinextd' => '#/267',
'inlatinexte' => '#/268',
'inlatinextendeda' => '#/264',
'inlatinextendedadditional' => '#/413',
'inlatinextendedb' => '#/265',
'inlatinextendedc' => '#/266',
'inlatinextendedd' => '#/267',
'inlatinextendede' => '#/268',
'inlatinextendedf' => '#/269',
'inlatinextendedg' => '#/270',
'inlatinextf' => '#/269',
'inlatinextg' => '#/270',
'inlepcha' => '#/167',
'inletterlikesymbols' => '#/408',
'inlimbu' => '#/139',
'inlineara' => '#/198',
'inlinearbideograms' => '#/398',
'inlinearbsyllabary' => '#/399',
'inlisu' => '#/122',
'inlisusup' => '#/199',
'inlisusupplement' => '#/199',
'inlowsurrogates' => '#/361',
'inlycian' => '#/168',
'inlydian' => '#/169',
'inmahajani' => '#/245',
'inmahjong' => '#/200',
'inmahjongtiles' => '#/200',
'inmakasar' => '#/201',
'inmalayalam' => '#/271',
'inmandaic' => '#/202',
'inmanichaean' => '#/299',
'inmarchen' => '#/203',
'inmasaramgondi' => '#/349',
'inmathalphanum' => '#/350',
'inmathematicalalphanumericsymbols' => '#/350',
'inmathematicaloperators' => '#/362',
'inmathoperators' => '#/362',
'inmayannumerals' => '#/363',
'inmedefaidrin' => '#/323',
'inmeeteimayek' => '#/324',
'inmeeteimayekext' => '#/374',
'inmeeteimayekextensions' => '#/374',
'inmendekikakui' => '#/351',
'inmeroiticcursive' => '#/384',
'inmeroitichieroglyphs' => '#/419',
'inmiao' => '#/123',
'inmiscarrows' => '#/300',
'inmiscellaneousmathematicalsymbolsa' => '#/400',
'inmiscellaneousmathematicalsymbolsb' => '#/401',
'inmiscellaneoussymbols' => '#/325',
'inmiscellaneoussymbolsandarrows' => '#/300',
'inmiscellaneoussymbolsandpictographs' => '#/385',
'inmiscellaneoustechnical' => '#/364',
'inmiscmathsymbolsa' => '#/400',
'inmiscmathsymbolsb' => '#/401',
'inmiscpictographs' => '#/385',
'inmiscsymbols' => '#/325',
'inmisctechnical' => '#/364',
'inmodi' => '#/124',
'inmodifierletters' => '#/386',
'inmodifiertoneletters' => '#/420',
'inmongolian' => '#/272',
'inmongoliansup' => '#/352',
'inmongoliansupplement' => '#/352',
'inmro' => '#/113',
'inmultani' => '#/204',
'inmusic' => '#/140',
'inmusicalsymbols' => '#/140',
'inmyanmar' => '#/205',
'inmyanmarexta' => '#/326',
'inmyanmarextb' => '#/327',
'inmyanmarextendeda' => '#/326',
'inmyanmarextendedb' => '#/327',
'innabataean' => '#/273',
'innagmundari' => '#/301',
'innandinagari' => '#/328',
'innb' => 'Blk/NB',
'innewa' => '#/125',
'innewtailue' => '#/274',
'innko' => '#/114',
'innoblock' => 'Blk/NB',
'innumberforms' => '#/329',
'innushu' => '#/141',
'innyiakengpuachuehmong' => '#/424',
'inocr' => '#/115',
'inogham' => '#/142',
'inolchiki' => '#/206',
'inoldhungarian' => '#/353',
'inolditalic' => '#/275',
'inoldnortharabian' => '#/387',
'inoldpermic' => '#/276',
'inoldpersian' => '#/302',
'inoldsogdian' => '#/303',
'inoldsoutharabian' => '#/388',
'inoldturkic' => '#/277',
'inolduyghur' => '#/278',
'inopticalcharacterrecognition' => '#/115',
'inoriya' => '#/143',
'inornamentaldingbats' => '#/414',
'inosage' => '#/144',
'inosmanya' => '#/207',
'inottomansiyaqnumbers' => '#/421',
'inpahawhhmong' => '#/330',
'inpalmyrene' => '#/279',
'inpaucinhau' => '#/280',
'inpc=bottom' => 'InPC/Bottom',
'inpc=bottomandleft' => '#/74',
'inpc=bottomandright' => 'InPC/BottomAn',
'inpc=left' => 'InPC/Left',
'inpc=leftandright' => 'InPC/LeftAndR',
'inpc=na' => 'InPC/NA',
'inpc=overstruck' => 'InPC/Overstru',
'inpc=right' => 'InPC/Right',
'inpc=top' => 'InPC/Top',
'inpc=topandbottom' => 'InPC/TopAndBo',
'inpc=topandbottomandleft' => '#/75',
'inpc=topandbottomandright' => '#/76',
'inpc=topandleft' => 'InPC/TopAndLe',
'inpc=topandleftandright' => 'InPC/TopAndL2',
'inpc=topandright' => 'InPC/TopAndRi',
'inpc=visualorderleft' => 'InPC/VisualOr',
'inphagspa' => '#/208',
'inphaistos' => '#/246',
'inphaistosdisc' => '#/246',
'inphoenician' => '#/304',
'inphoneticext' => '#/331',
'inphoneticextensions' => '#/331',
'inphoneticextensionssupplement' => '#/375',
'inphoneticextsup' => '#/375',
'inplayingcards' => '#/354',
'inprivateuse' => '#/116',
'inprivateusearea' => '#/116',
'inpsalterpahlavi' => '#/376',
'inpua' => '#/116',
'inpunctuation' => '#/332',
'inrejang' => '#/170',
'inrumi' => '#/126',
'inruminumeralsymbols' => '#/126',
'inrunic' => '#/145',
'insamaritan' => '#/281',
'insaurashtra' => '#/305',
'insc=avagraha' => 'InSC/Avagraha',
'insc=bindu' => 'InSC/Bindu',
'insc=brahmijoiningnumber' => '#/83',
'insc=cantillationmark' => 'InSC/Cantilla',
'insc=consonant' => 'InSC/Consonan',
'insc=consonantdead' => 'InSC/Consona2',
'insc=consonantfinal' => 'InSC/Consona3',
'insc=consonantheadletter' => '#/84',
'insc=consonantinitialpostfixed' => '#/86',
'insc=consonantkiller' => '#/80',
'insc=consonantmedial' => 'InSC/Consona4',
'insc=consonantplaceholder' => 'InSC/Consona7',
'insc=consonantprecedingrepha' => 'InSC/Consona9',
'insc=consonantprefixed' => 'InSC/Consona5',
'insc=consonantsubjoined' => 'InSC/Consona6',
'insc=consonantsucceedingrepha' => '#/85',
'insc=consonantwithstacker' => 'InSC/Consona8',
'insc=geminationmark' => 'InSC/Geminati',
'insc=invisiblestacker' => 'InSC/Invisibl',
'insc=joiner' => '#/69',
'insc=modifyingletter' => '#/81',
'insc=nonjoiner' => '#/77',
'insc=nukta' => 'InSC/Nukta',
'insc=number' => 'InSC/Number',
'insc=numberjoiner' => '#/79',
'insc=other' => 'InSC/Other',
'insc=purekiller' => 'InSC/PureKill',
'insc=registershifter' => '#/82',
'insc=syllablemodifier' => 'InSC/Syllable',
'insc=toneletter' => '#/78',
'insc=tonemark' => 'InSC/ToneMark',
'insc=virama' => 'InSC/Virama',
'insc=visarga' => 'InSC/Visarga',
'insc=vowel' => 'InSC/Vowel',
'insc=voweldependent' => 'InSC/VowelDep',
'insc=vowelindependent' => 'InSC/VowelInd',
'inscriptionalpahlavi' => '#/710',
'inscriptionalparthian' => '#/712',
'insharada' => '#/209',
'inshavian' => '#/210',
'inshorthandformatcontrols' => '#/431',
'insiddham' => '#/211',
'insinhala' => '#/212',
'insinhalaarchaicnumbers' => '#/426',
'insmallforms' => '#/306',
'insmallformvariants' => '#/306',
'insmallkanaext' => '#/355',
'insmallkanaextension' => '#/355',
'insogdian' => '#/213',
'insorasompeng' => '#/333',
'insoyombo' => '#/214',
'inspacingmodifierletters' => '#/386',
'inspecials' => '#/247',
'insundanese' => '#/282',
'insundanesesup' => '#/356',
'insundanesesupplement' => '#/356',
'insuparrowsa' => '#/307',
'insuparrowsb' => '#/308',
'insuparrowsc' => '#/309',
'insuperandsub' => '#/334',
'insuperscriptsandsubscripts' => '#/334',
'insupmathoperators' => '#/402',
'insupplementalarrowsa' => '#/307',
'insupplementalarrowsb' => '#/308',
'insupplementalarrowsc' => '#/309',
'insupplementalmathematicaloperators' => '#/402',
'insupplementalpunctuation' => '#/377',
'insupplementalsymbolsandpictographs' => '#/432',
'insupplementaryprivateuseareaa' => '#/215',
'insupplementaryprivateuseareab' => '#/216',
'insuppuaa' => '#/215',
'insuppuab' => '#/216',
'insuppunctuation' => '#/377',
'insupsymbolsandpictographs' => '#/432',
'insuttonsignwriting' => '#/409',
'insylotinagri' => '#/335',
'insymbolsandpictographsexta' => '#/433',
'insymbolsandpictographsextendeda' => '#/433',
'insymbolsforlegacycomputing' => '#/434',
'insyriac' => '#/171',
'insyriacsup' => '#/283',
'insyriacsupplement' => '#/283',
'intagalog' => '#/217',
'intagbanwa' => '#/248',
'intags' => '#/127',
'intaile' => '#/146',
'intaitham' => '#/218',
'intaiviet' => '#/219',
'intaixuanjing' => '#/336',
'intaixuanjingsymbols' => '#/336',
'intakri' => '#/147',
'intamil' => '#/148',
'intamilsup' => '#/249',
'intamilsupplement' => '#/249',
'intangsa' => '#/172',
'intangut' => '#/173',
'intangutcomponents' => '#/403',
'intangutsup' => '#/284',
'intangutsupplement' => '#/284',
'intelugu' => '#/174',
'inthaana' => '#/175',
'inthai' => '#/128',
'intibetan' => '#/220',
'intifinagh' => '#/250',
'intirhuta' => '#/221',
'intoto' => '#/129',
'intransportandmap' => '#/389',
'intransportandmapsymbols' => '#/389',
'inucas' => '#/130',
'inucasext' => '#/222',
'inucasexta' => '#/251',
'inugaritic' => '#/252',
'inunifiedcanadianaboriginalsyllabics' => '#/130',
'inunifiedcanadianaboriginalsyllabicsextended' => '#/222',
'inunifiedcanadianaboriginalsyllabicsextendeda' => '#/251',
'invai' => '#/117',
'invariationselectors' => '#/109',
'invariationselectorssupplement' => '#/149',
'invedicext' => '#/253',
'invedicextensions' => '#/253',
'inverticalforms' => '#/365',
'invithkuqi' => '#/254',
'invs' => '#/109',
'invssup' => '#/149',
'inwancho' => '#/176',
'inwarangciti' => '#/310',
'inyezidi' => '#/177',
'inyijing' => '#/178',
'inyijinghexagramsymbols' => '#/178',
'inyiradicals' => '#/311',
'inyisyllables' => '#/337',
'inzanabazarsquare' => '#/390',
'inznamennymusic' => '#/366',
'inznamennymusicalnotation' => '#/366',
'ipaext' => '#/161',
'ipaextensions' => '#/161',
'isadlam' => 'Scx/Adlm',
'isadlm' => 'Scx/Adlm',
'isaegeannumbers' => '#/357',
'isaghb' => '#/654',
'isahex' => '#/60',
'isahom' => '#/655',
'isalchemical' => '#/285',
'isalchemicalsymbols' => '#/285',
'isall' => '#/1',
'isalnum' => 'Perl/Alnum',
'isalpha' => 'Alpha/Y',
'isalphabetic' => 'Alpha/Y',
'isalphabeticpf' => '#/338',
'isalphabeticpresentationforms' => '#/338',
'isanatolianhieroglyphs' => '#/677',
'isancientgreekmusic' => '#/404',
'isancientgreekmusicalnotation' => '#/404',
'isancientgreeknumbers' => '#/415',
'isancientsymbols' => '#/367',
'isany' => '#/2',
'isarab' => 'Scx/Arab',
'isarabic' => 'Scx/Arab',
'isarabicexta' => '#/286',
'isarabicextb' => '#/287',
'isarabicextc' => '#/288',
'isarabicextendeda' => '#/286',
'isarabicextendedb' => '#/287',
'isarabicextendedc' => '#/288',
'isarabicmath' => '#/289',
'isarabicmathematicalalphabeticsymbols' => '#/289',
'isarabicpfa' => '#/255',
'isarabicpfb' => '#/256',
'isarabicpresentationformsa' => '#/255',
'isarabicpresentationformsb' => '#/256',
'isarabicsup' => '#/257',
'isarabicsupplement' => '#/257',
'isarmenian' => 'Scx/Armn',
'isarmi' => '#/656',
'isarmn' => 'Scx/Armn',
'isarrows' => '#/151',
'isascii' => '#/131',
'isasciihexdigit' => '#/60',
'isassigned' => 'Perl/Assigned',
'isavestan' => '#/657',
'isavst' => '#/657',
'isbali' => '#/658',
'isbalinese' => '#/658',
'isbamu' => '#/659',
'isbamum' => '#/659',
'isbamumsup' => '#/225',
'isbamumsupplement' => '#/225',
'isbasiclatin' => '#/131',
'isbass' => '#/660',
'isbassavah' => '#/660',
'isbatak' => '#/661',
'isbatk' => '#/661',
'isbeng' => 'Scx/Beng',
'isbengali' => 'Scx/Beng',
'isbhaiksuki' => 'Scx/Bhks',
'isbhks' => 'Scx/Bhks',
'isbidic' => 'BidiC/Y',
'isbidicontrol' => 'BidiC/Y',
'isbidim' => 'BidiM/Y',
'isbidimirrored' => 'BidiM/Y',
'isblank' => 'Perl/Blank',
'isblockelements' => '#/358',
'isbopo' => 'Scx/Bopo',
'isbopomofo' => 'Scx/Bopo',
'isbopomofoext' => '#/312',
'isbopomofoextended' => '#/312',
'isboxdrawing' => '#/290',
'isbrah' => '#/662',
'isbrahmi' => '#/662',
'isbrai' => '#/181',
'isbraille' => '#/181',
'isbraillepatterns' => '#/181',
'isbugi' => '#/663',
'isbuginese' => '#/663',
'isbuhd' => '#/664',
'isbuhid' => '#/664',
'isbyzantinemusic' => '#/368',
'isbyzantinemusicalsymbols' => '#/368',
'isc' => 'Gc/C',
'iscakm' => 'Scx/Cakm',
'iscanadianaboriginal' => '#/665',
'iscanadiansyllabics' => '#/130',
'iscans' => '#/665',
'iscari' => '#/666',
'iscarian' => '#/666',
'iscased' => 'Cased/Y',
'iscasedletter' => 'Gc/LC',
'iscaseignorable' => 'CI/Y',
'iscaucasianalbanian' => '#/654',
'iscc' => '#/439',
'isce' => 'CE/Y',
'iscf' => 'Gc/Cf',
'ischakma' => 'Scx/Cakm',
'ischam' => 'Scx/Cham',
'ischangeswhencasefolded' => 'CWCF/Y',
'ischangeswhencasemapped' => 'CWCM/Y',
'ischangeswhenlowercased' => 'CWL/Y',
'ischangeswhennfkccasefolded' => 'CWKCF/Y',
'ischangeswhentitlecased' => 'CWT/Y',
'ischangeswhenuppercased' => 'CWU/Y',
'ischer' => '#/667',
'ischerokee' => '#/667',
'ischerokeesup' => '#/313',
'ischerokeesupplement' => '#/313',
'ischesssymbols' => '#/339',
'ischorasmian' => '#/668',
'ischrs' => '#/668',
'isci' => 'CI/Y',
'iscjk' => '#/110',
'iscjkcompat' => '#/259',
'iscjkcompatforms' => '#/369',
'iscjkcompatibility' => '#/259',
'iscjkcompatibilityforms' => '#/369',
'iscjkcompatibilityideographs' => '#/416',
'iscjkcompatibilityideographssupplement' => '#/427',
'iscjkcompatideographs' => '#/416',
'iscjkcompatideographssup' => '#/427',
'iscjkexta' => '#/182',
'iscjkextb' => '#/183',
'iscjkextc' => '#/184',
'iscjkextd' => '#/185',
'iscjkexte' => '#/186',
'iscjkextf' => '#/187',
'iscjkextg' => '#/188',
'iscjkexth' => '#/189',
'iscjkradicalssup' => '#/370',
'iscjkradicalssupplement' => '#/370',
'iscjkstrokes' => '#/291',
'iscjksymbols' => '#/292',
'iscjksymbolsandpunctuation' => '#/292',
'iscjkunifiedideographs' => '#/110',
'iscjkunifiedideographsextensiona' => '#/182',
'iscjkunifiedideographsextensionb' => '#/183',
'iscjkunifiedideographsextensionc' => '#/184',
'iscjkunifiedideographsextensiond' => '#/185',
'iscjkunifiedideographsextensione' => '#/186',
'iscjkunifiedideographsextensionf' => '#/187',
'iscjkunifiedideographsextensiong' => '#/188',
'iscjkunifiedideographsextensionh' => '#/189',
'isclosepunctuation' => 'Gc/Pe',
'iscn' => 'Gc/Cn',
'iscntrl' => '#/439',
'isco' => '#/440',
'iscombiningdiacriticalmarks' => '#/344',
'iscombiningdiacriticalmarksextended' => '#/380',
'iscombiningdiacriticalmarksforsymbols' => '#/428',
'iscombiningdiacriticalmarkssupplement' => '#/381',
'iscombininghalfmarks' => '#/262',
'iscombiningmark' => 'Gc/M',
'iscombiningmarksforsymbols' => '#/428',
'iscommon' => 'Scx/Zyyy',
'iscommonindicnumberforms' => '#/396',
'iscompatjamo' => '#/294',
'iscompex' => 'CompEx/Y',
'iscompositionexclusion' => 'CE/Y',
'isconnectorpunctuation' => 'Gc/Pc',
'iscontrol' => '#/439',
'iscontrolpictures' => '#/378',
'iscopt' => 'Scx/Copt',
'iscoptic' => 'Scx/Copt',
'iscopticepactnumbers' => '#/410',
'iscountingrod' => '#/314',
'iscountingrodnumerals' => '#/314',
'iscpmn' => '#/669',
'iscprt' => 'Scx/Cprt',
'iscs' => '#/14',
'iscuneiform' => 'Scx/Xsux',
'iscuneiformnumbers' => '#/391',
'iscuneiformnumbersandpunctuation' => '#/391',
'iscurrencysymbol' => 'Gc/Sc',
'iscurrencysymbols' => '#/379',
'iscwcf' => 'CWCF/Y',
'iscwcm' => 'CWCM/Y',
'iscwkcf' => 'CWKCF/Y',
'iscwl' => 'CWL/Y',
'iscwt' => 'CWT/Y',
'iscwu' => 'CWU/Y',
'iscypriot' => 'Scx/Cprt',
'iscypriotsyllabary' => '#/392',
'iscyprominoan' => '#/669',
'iscyrillic' => 'Scx/Cyrl',
'iscyrillicexta' => '#/340',
'iscyrillicextb' => '#/341',
'iscyrillicextc' => '#/342',
'iscyrillicextd' => '#/343',
'iscyrillicextendeda' => '#/340',
'iscyrillicextendedb' => '#/341',
'iscyrillicextendedc' => '#/342',
'iscyrillicextendedd' => '#/343',
'iscyrillicsup' => '#/316',
'iscyrillicsupplement' => '#/316',
'iscyrillicsupplementary' => '#/316',
'iscyrl' => 'Scx/Cyrl',
'isdash' => 'Dash/Y',
'isdashpunctuation' => 'Gc/Pd',
'isdecimalnumber' => 'Gc/Nd',
'isdefaultignorablecodepoint' => 'DI/Y',
'isdep' => 'Dep/Y',
'isdeprecated' => 'Dep/Y',
'isdeseret' => '#/190',
'isdeva' => 'Scx/Deva',
'isdevanagari' => 'Scx/Deva',
'isdevanagariext' => '#/359',
'isdevanagariexta' => '#/371',
'isdevanagariextended' => '#/359',
'isdevanagariextendeda' => '#/371',
'isdi' => 'DI/Y',
'isdia' => 'Dia/Y',
'isdiacritic' => 'Dia/Y',
'isdiacriticals' => '#/344',
'isdiacriticalsext' => '#/380',
'isdiacriticalsforsymbols' => '#/428',
'isdiacriticalssup' => '#/381',
'isdiak' => 'Scx/Diak',
'isdigit' => 'Gc/Nd',
'isdingbats' => '#/231',
'isdivesakuru' => 'Scx/Diak',
'isdogr' => '#/670',
'isdogra' => '#/670',
'isdomino' => '#/156',
'isdominotiles' => '#/156',
'isdsrt' => '#/190',
'isdupl' => 'Scx/Dupl',
'isduployan' => 'Scx/Dupl',
'isearlydynasticcuneiform' => '#/429',
'isebase' => 'EBase/Y',
'isecomp' => 'EComp/Y',
'isegyp' => '#/671',
'isegyptianhieroglyphformatcontrols' => '#/435',
'isegyptianhieroglyphs' => '#/671',
'iselba' => '#/672',
'iselbasan' => '#/672',
'iselym' => '#/673',
'iselymaic' => '#/673',
'isemod' => '#/61',
'isemoji' => 'Emoji/Y',
'isemojicomponent' => 'EComp/Y',
'isemojimodifier' => '#/61',
'isemojimodifierbase' => 'EBase/Y',
'isemojipresentation' => 'EPres/Y',
'isemoticons' => '#/261',
'isenclosedalphanum' => '#/393',
'isenclosedalphanumerics' => '#/393',
'isenclosedalphanumericsupplement' => '#/418',
'isenclosedalphanumsup' => '#/418',
'isenclosedcjk' => '#/317',
'isenclosedcjklettersandmonths' => '#/317',
'isenclosedideographicsup' => '#/430',
'isenclosedideographicsupplement' => '#/430',
'isenclosingmark' => 'Gc/Me',
'isepres' => 'EPres/Y',
'isethi' => 'Scx/Ethi',
'isethiopic' => 'Scx/Ethi',
'isethiopicext' => '#/318',
'isethiopicexta' => '#/345',
'isethiopicextb' => '#/346',
'isethiopicextended' => '#/318',
'isethiopicextendeda' => '#/345',
'isethiopicextendedb' => '#/346',
'isethiopicsup' => '#/319',
'isethiopicsupplement' => '#/319',
'isext' => 'Ext/Y',
'isextendedpictographic' => 'ExtPict/Y',
'isextender' => 'Ext/Y',
'isextpict' => 'ExtPict/Y',
'isfinalpunctuation' => 'Gc/Pf',
'isformat' => 'Gc/Cf',
'isfullcompositionexclusion' => 'CompEx/Y',
'isgeneralpunctuation' => '#/332',
'isgeometricshapes' => '#/382',
'isgeometricshapesext' => '#/411',
'isgeometricshapesextended' => '#/411',
'isgeor' => 'Scx/Geor',
'isgeorgian' => 'Scx/Geor',
'isgeorgianext' => '#/320',
'isgeorgianextended' => '#/320',
'isgeorgiansup' => '#/321',
'isgeorgiansupplement' => '#/321',
'isglag' => 'Scx/Glag',
'isglagolitic' => 'Scx/Glag',
'isglagoliticsup' => '#/360',
'isglagoliticsupplement' => '#/360',
'isgong' => 'Scx/Gong',
'isgonm' => 'Scx/Gonm',
'isgoth' => '#/674',
'isgothic' => '#/674',
'isgran' => 'Scx/Gran',
'isgrantha' => 'Scx/Gran',
'isgraph' => 'Perl/Graph',
'isgraphemebase' => 'GrBase/Y',
'isgraphemeextend' => 'GrExt/Y',
'isgrbase' => 'GrBase/Y',
'isgreek' => 'Scx/Grek',
'isgreekandcoptic' => '#/137',
'isgreekext' => '#/235',
'isgreekextended' => '#/235',
'isgrek' => 'Scx/Grek',
'isgrext' => 'GrExt/Y',
'isgujarati' => 'Scx/Gujr',
'isgujr' => 'Scx/Gujr',
'isgunjalagondi' => 'Scx/Gong',
'isgurmukhi' => 'Scx/Guru',
'isguru' => 'Scx/Guru',
'ishalfandfullforms' => '#/394',
'ishalfmarks' => '#/262',
'ishalfwidthandfullwidthforms' => '#/394',
'ishan' => 'Scx/Han',
'ishang' => 'Scx/Hang',
'ishangul' => 'Scx/Hang',
'ishangulcompatibilityjamo' => '#/294',
'ishanguljamo' => '#/120',
'ishanguljamoextendeda' => '#/239',
'ishanguljamoextendedb' => '#/240',
'ishangulsyllables' => '#/158',
'ishani' => 'Scx/Han',
'ishanifirohingya' => 'Scx/Rohg',
'ishano' => '#/675',
'ishanunoo' => '#/675',
'ishatr' => '#/676',
'ishatran' => '#/676',
'ishebr' => 'Scx/Hebr',
'ishebrew' => 'Scx/Hebr',
'ishex' => 'Hex/Y',
'ishexdigit' => 'Hex/Y',
'ishighprivateusesurrogates' => '#/395',
'ishighpusurrogates' => '#/395',
'ishighsurrogates' => '#/373',
'ishira' => 'Scx/Hira',
'ishiragana' => 'Scx/Hira',
'ishluw' => '#/677',
'ishmng' => 'Scx/Hmng',
'ishmnp' => 'Scx/Hmnp',
'ishorizspace' => 'Perl/Blank',
'ishung' => '#/678',
'ishyphen' => 'Hyphen/T',
'isidc' => 'IDC/Y',
'isidcontinue' => 'IDC/Y',
'isideo' => 'Ideo/Y',
'isideographic' => 'Ideo/Y',
'isideographicdescriptioncharacters' => '#/111',
'isideographicsymbols' => '#/412',
'isideographicsymbolsandpunctuation' => '#/412',
'isids' => 'IDS/Y',
'isidsb' => '#/70',
'isidsbinaryoperator' => '#/70',
'isidst' => '#/72',
'isidstart' => 'IDS/Y',
'isidstrinaryoperator' => '#/72',
'isimperialaramaic' => '#/656',
'isindicnumberforms' => '#/396',
'isindicsiyaqnumbers' => '#/406',
'isinherited' => 'Scx/Zinh',
'isinitialpunctuation' => 'Gc/Pi',
'isinscriptionalpahlavi' => '#/710',
'isinscriptionalparthian' => '#/712',
'isipaext' => '#/161',
'isipaextensions' => '#/161',
'isital' => '#/679',
'isjamo' => '#/120',
'isjamoexta' => '#/239',
'isjamoextb' => '#/240',
'isjava' => '#/680',
'isjavanese' => '#/680',
'isjoinc' => '#/87',
'isjoincontrol' => '#/87',
'iskaithi' => 'Scx/Kthi',
'iskaktoviknumerals' => '#/397',
'iskali' => '#/197',
'iskana' => 'Scx/Kana',
'iskanaexta' => '#/242',
'iskanaextb' => '#/243',
'iskanaextendeda' => '#/242',
'iskanaextendedb' => '#/243',
'iskanasup' => '#/195',
'iskanasupplement' => '#/195',
'iskanbun' => '#/163',
'iskangxi' => '#/164',
'iskangxiradicals' => '#/164',
'iskannada' => 'Scx/Knda',
'iskatakana' => 'Scx/Kana',
'iskatakanaext' => '#/322',
'iskatakanaphoneticextensions' => '#/322',
'iskawi' => '#/681',
'iskayahli' => '#/197',
'iskhar' => 'Scx/Khar',
'iskharoshthi' => 'Scx/Khar',
'iskhitansmallscript' => '#/682',
'iskhmer' => 'Scx/Khmr',
'iskhmersymbols' => '#/348',
'iskhmr' => 'Scx/Khmr',
'iskhoj' => 'Scx/Khoj',
'iskhojki' => 'Scx/Khoj',
'iskhudawadi' => 'Scx/Sind',
'iskits' => '#/682',
'isknda' => 'Scx/Knda',
'iskthi' => 'Scx/Kthi',
'isl' => 'Gc/L',
'isl&' => 'Gc/LC',
'isl_' => 'Gc/LC',
'islana' => 'Scx/Lana',
'islao' => 'Scx/Lao',
'islaoo' => 'Scx/Lao',
'islatin' => 'Scx/Latn',
'islatin1' => '#/166',
'islatin1sup' => '#/166',
'islatin1supplement' => '#/166',
'islatinexta' => '#/264',
'islatinextadditional' => '#/413',
'islatinextb' => '#/265',
'islatinextc' => '#/266',
'islatinextd' => '#/267',
'islatinexte' => '#/268',
'islatinextendeda' => '#/264',
'islatinextendedadditional' => '#/413',
'islatinextendedb' => '#/265',
'islatinextendedc' => '#/266',
'islatinextendedd' => '#/267',
'islatinextendede' => '#/268',
'islatinextendedf' => '#/269',
'islatinextendedg' => '#/270',
'islatinextf' => '#/269',
'islatinextg' => '#/270',
'islatn' => 'Scx/Latn',
'islc' => 'Gc/LC',
'islepc' => '#/683',
'islepcha' => '#/683',
'isletter' => 'Gc/L',
'isletterlikesymbols' => '#/408',
'isletternumber' => 'Gc/Nl',
'islimb' => 'Scx/Limb',
'islimbu' => 'Scx/Limb',
'islina' => 'Scx/Lina',
'islinb' => 'Scx/Linb',
'islineara' => 'Scx/Lina',
'islinearb' => 'Scx/Linb',
'islinearbideograms' => '#/398',
'islinearbsyllabary' => '#/399',
'islineseparator' => '#/441',
'islisu' => '#/684',
'islisusup' => '#/199',
'islisusupplement' => '#/199',
'isll' => 'Gc/Ll',
'islm' => 'Gc/Lm',
'islo' => 'Gc/Lo',
'isloe' => 'InPC/VisualOr',
'islogicalorderexception' => 'InPC/VisualOr',
'islower' => 'Lower/Y',
'islowercase' => 'Lower/Y',
'islowercaseletter' => 'Gc/Ll',
'islowsurrogates' => '#/361',
'islt' => 'Perl/Title',
'islu' => 'Gc/Lu',
'islyci' => '#/685',
'islycian' => '#/685',
'islydi' => '#/686',
'islydian' => '#/686',
'ism' => 'Gc/M',
'ismahajani' => '#/687',
'ismahj' => '#/687',
'ismahjong' => '#/200',
'ismahjongtiles' => '#/200',
'ismaka' => '#/688',
'ismakasar' => '#/688',
'ismalayalam' => 'Scx/Mlym',
'ismand' => '#/689',
'ismandaic' => '#/689',
'ismani' => '#/690',
'ismanichaean' => '#/690',
'ismarc' => '#/691',
'ismarchen' => '#/691',
'ismark' => 'Gc/M',
'ismasaramgondi' => 'Scx/Gonm',
'ismath' => 'Math/Y',
'ismathalphanum' => '#/350',
'ismathematicalalphanumericsymbols' => '#/350',
'ismathematicaloperators' => '#/362',
'ismathoperators' => '#/362',
'ismathsymbol' => 'Gc/Sm',
'ismayannumerals' => '#/363',
'ismc' => 'Gc/Mc',
'isme' => 'Gc/Me',
'ismedefaidrin' => '#/692',
'ismedf' => '#/692',
'ismeeteimayek' => '#/697',
'ismeeteimayekext' => '#/374',
'ismeeteimayekextensions' => '#/374',
'ismend' => '#/693',
'ismendekikakui' => '#/693',
'ismerc' => '#/694',
'ismero' => '#/419',
'ismeroiticcursive' => '#/694',
'ismeroitichieroglyphs' => '#/419',
'ismiao' => '#/695',
'ismiscarrows' => '#/300',
'ismiscellaneousmathematicalsymbolsa' => '#/400',
'ismiscellaneousmathematicalsymbolsb' => '#/401',
'ismiscellaneoussymbols' => '#/325',
'ismiscellaneoussymbolsandarrows' => '#/300',
'ismiscellaneoussymbolsandpictographs' => '#/385',
'ismiscellaneoustechnical' => '#/364',
'ismiscmathsymbolsa' => '#/400',
'ismiscmathsymbolsb' => '#/401',
'ismiscpictographs' => '#/385',
'ismiscsymbols' => '#/325',
'ismisctechnical' => '#/364',
'ismlym' => 'Scx/Mlym',
'ismn' => 'Gc/Mn',
'ismodi' => '#/696',
'ismodifierletter' => 'Gc/Lm',
'ismodifierletters' => '#/386',
'ismodifiersymbol' => 'Gc/Sk',
'ismodifiertoneletters' => '#/420',
'ismong' => 'Scx/Mong',
'ismongolian' => 'Scx/Mong',
'ismongoliansup' => '#/352',
'ismongoliansupplement' => '#/352',
'ismro' => '#/652',
'ismroo' => '#/652',
'ismtei' => '#/697',
'ismult' => 'Scx/Mult',
'ismultani' => 'Scx/Mult',
'ismusic' => '#/140',
'ismusicalsymbols' => '#/140',
'ismyanmar' => 'Scx/Mymr',
'ismyanmarexta' => '#/326',
'ismyanmarextb' => '#/327',
'ismyanmarextendeda' => '#/326',
'ismyanmarextendedb' => '#/327',
'ismymr' => 'Scx/Mymr',
'isn' => 'Gc/N',
'isnabataean' => '#/699',
'isnagm' => '#/698',
'isnagmundari' => '#/698',
'isnand' => 'Scx/Nand',
'isnandinagari' => 'Scx/Nand',
'isnarb' => '#/387',
'isnb' => 'Blk/NB',
'isnbat' => '#/699',
'isnchar' => 'Perl/_PerlNch',
'isnd' => 'Gc/Nd',
'isnewa' => '#/700',
'isnewtailue' => 'Scx/Talu',
'isnko' => 'Scx/Nko',
'isnkoo' => 'Scx/Nko',
'isnl' => 'Gc/Nl',
'isno' => 'Gc/No',
'isnoblock' => 'Blk/NB',
'isnoncharactercodepoint' => 'Perl/_PerlNch',
'isnonspacingmark' => 'Gc/Mn',
'isnshu' => '#/701',
'isnumber' => 'Gc/N',
'isnumberforms' => '#/329',
'isnushu' => '#/701',
'isnyiakengpuachuehmong' => 'Scx/Hmnp',
'isocr' => '#/115',
'isogam' => '#/702',
'isogham' => '#/702',
'isolchiki' => '#/206',
'isolck' => '#/206',
'isoldhungarian' => '#/678',
'isolditalic' => '#/679',
'isoldnortharabian' => '#/387',
'isoldpermic' => '#/708',
'isoldpersian' => '#/735',
'isoldsogdian' => '#/720',
'isoldsoutharabian' => '#/388',
'isoldturkic' => '#/703',
'isolduyghur' => '#/706',
'isopenpunctuation' => 'Gc/Ps',
'isopticalcharacterrecognition' => '#/115',
'isoriya' => 'Scx/Orya',
'isorkh' => '#/703',
'isornamentaldingbats' => '#/414',
'isorya' => 'Scx/Orya',
'isosage' => '#/704',
'isosge' => '#/704',
'isosma' => '#/705',
'isosmanya' => '#/705',
'isother' => 'Gc/C',
'isotherletter' => 'Gc/Lo',
'isothernumber' => 'Gc/No',
'isotherpunctuation' => 'Gc/Po',
'isothersymbol' => 'Gc/So',
'isottomansiyaqnumbers' => '#/421',
'isougr' => '#/706',
'isp' => 'Gc/P',
'ispahawhhmong' => 'Scx/Hmng',
'ispalm' => '#/279',
'ispalmyrene' => '#/279',
'isparagraphseparator' => '#/442',
'ispatsyn' => 'PatSyn/Y',
'ispatternsyntax' => 'PatSyn/Y',
'ispatternwhitespace' => 'Perl/_PerlPat',
'ispatws' => 'Perl/_PerlPat',
'ispauc' => '#/707',
'ispaucinhau' => '#/707',
'ispc' => 'Gc/Pc',
'ispcm' => 'PCM/Y',
'ispd' => 'Gc/Pd',
'ispe' => 'Gc/Pe',
'isperlspace' => '#/3',
'isperlword' => 'Perl/PerlWord',
'isperm' => '#/708',
'ispf' => 'Gc/Pf',
'isphag' => '#/709',
'isphagspa' => '#/709',
'isphaistos' => '#/246',
'isphaistosdisc' => '#/246',
'isphli' => '#/710',
'isphlp' => 'Scx/Phlp',
'isphnx' => '#/711',
'isphoenician' => '#/711',
'isphoneticext' => '#/331',
'isphoneticextensions' => '#/331',
'isphoneticextensionssupplement' => '#/375',
'isphoneticextsup' => '#/375',
'ispi' => 'Gc/Pi',
'isplayingcards' => '#/354',
'isplrd' => '#/695',
'ispo' => 'Gc/Po',
'isposixalnum' => '#/5',
'isposixalpha' => '#/6',
'isposixblank' => '#/7',
'isposixcntrl' => '#/8',
'isposixdigit' => '#/9',
'isposixgraph' => '#/10',
'isposixlower' => '#/11',
'isposixprint' => '#/12',
'isposixpunct' => 'Perl/PosixPun',
'isposixspace' => '#/3',
'isposixupper' => '#/13',
'isposixword' => 'Perl/PerlWord',
'isposixxdigit' => '#/60',
'isprependedconcatenationmark' => 'PCM/Y',
'isprint' => 'Perl/Print',
'isprivateuse' => '#/440',
'isprivateusearea' => '#/116',
'isprti' => '#/712',
'isps' => 'Gc/Ps',
'ispsalterpahlavi' => 'Scx/Phlp',
'ispua' => '#/116',
'ispunct' => 'Gc/P',
'ispunctuation' => 'Gc/P',
'isqaac' => 'Scx/Copt',
'isqaai' => 'Scx/Zinh',
'isqmark' => 'QMark/Y',
'isquotationmark' => 'QMark/Y',
'isradical' => '#/90',
'isregionalindicator' => '#/68',
'isrejang' => '#/713',
'isri' => '#/68',
'isrjng' => '#/713',
'isrohg' => 'Scx/Rohg',
'isrumi' => '#/126',
'isruminumeralsymbols' => '#/126',
'isrunic' => '#/714',
'isrunr' => '#/714',
'iss' => 'Gc/S',
'issamaritan' => '#/715',
'issamr' => '#/715',
'issarb' => '#/388',
'issaur' => '#/716',
'issaurashtra' => '#/716',
'issc' => 'Gc/Sc',
'issd' => 'SD/Y',
'issentenceterminal' => 'STerm/Y',
'isseparator' => 'Gc/Z',
'issgnw' => '#/717',
'issharada' => 'Scx/Shrd',
'isshavian' => '#/210',
'isshaw' => '#/210',
'isshorthandformatcontrols' => '#/431',
'isshrd' => 'Scx/Shrd',
'issidd' => '#/718',
'issiddham' => '#/718',
'issignwriting' => '#/717',
'issind' => 'Scx/Sind',
'issinh' => 'Scx/Sinh',
'issinhala' => 'Scx/Sinh',
'issinhalaarchaicnumbers' => '#/426',
'issk' => 'Gc/Sk',
'issm' => 'Gc/Sm',
'issmallforms' => '#/306',
'issmallformvariants' => '#/306',
'issmallkanaext' => '#/355',
'issmallkanaextension' => '#/355',
'isso' => 'Gc/So',
'issoftdotted' => 'SD/Y',
'issogd' => '#/719',
'issogdian' => '#/719',
'issogo' => '#/720',
'issora' => '#/721',
'issorasompeng' => '#/721',
'issoyo' => '#/722',
'issoyombo' => '#/722',
'isspace' => 'Perl/SpacePer',
'isspaceperl' => 'Perl/SpacePer',
'isspaceseparator' => 'Gc/Zs',
'isspacingmark' => 'Gc/Mc',
'isspacingmodifierletters' => '#/386',
'isspecials' => '#/247',
'issterm' => 'STerm/Y',
'issund' => '#/723',
'issundanese' => '#/723',
'issundanesesup' => '#/356',
'issundanesesupplement' => '#/356',
'issuparrowsa' => '#/307',
'issuparrowsb' => '#/308',
'issuparrowsc' => '#/309',
'issuperandsub' => '#/334',
'issuperscriptsandsubscripts' => '#/334',
'issupmathoperators' => '#/402',
'issupplementalarrowsa' => '#/307',
'issupplementalarrowsb' => '#/308',
'issupplementalarrowsc' => '#/309',
'issupplementalmathematicaloperators' => '#/402',
'issupplementalpunctuation' => '#/377',
'issupplementalsymbolsandpictographs' => '#/432',
'issupplementaryprivateuseareaa' => '#/215',
'issupplementaryprivateuseareab' => '#/216',
'issuppuaa' => '#/215',
'issuppuab' => '#/216',
'issuppunctuation' => '#/377',
'issupsymbolsandpictographs' => '#/432',
'issurrogate' => '#/14',
'issuttonsignwriting' => '#/409',
'issylo' => '#/724',
'issylotinagri' => '#/724',
'issymbol' => 'Gc/S',
'issymbolsandpictographsexta' => '#/433',
'issymbolsandpictographsextendeda' => '#/433',
'issymbolsforlegacycomputing' => '#/434',
'issyrc' => 'Scx/Syrc',
'issyriac' => 'Scx/Syrc',
'issyriacsup' => '#/283',
'issyriacsupplement' => '#/283',
'istagalog' => '#/728',
'istagb' => 'Scx/Tagb',
'istagbanwa' => 'Scx/Tagb',
'istags' => '#/127',
'istaile' => '#/725',
'istaitham' => 'Scx/Lana',
'istaiviet' => '#/726',
'istaixuanjing' => '#/336',
'istaixuanjingsymbols' => '#/336',
'istakr' => 'Scx/Takr',
'istakri' => 'Scx/Takr',
'istale' => '#/725',
'istalu' => 'Scx/Talu',
'istamil' => 'Scx/Taml',
'istamilsup' => '#/249',
'istamilsupplement' => '#/249',
'istaml' => 'Scx/Taml',
'istang' => 'Scx/Tang',
'istangsa' => '#/730',
'istangut' => 'Scx/Tang',
'istangutcomponents' => '#/403',
'istangutsup' => '#/284',
'istangutsupplement' => '#/284',
'istavt' => '#/726',
'istelu' => 'Scx/Telu',
'istelugu' => 'Scx/Telu',
'isterm' => 'Term/Y',
'isterminalpunctuation' => 'Term/Y',
'istfng' => '#/727',
'istglg' => '#/728',
'isthaa' => 'Scx/Thaa',
'isthaana' => 'Scx/Thaa',
'isthai' => '#/729',
'istibetan' => 'Scx/Tibt',
'istibt' => 'Scx/Tibt',
'istifinagh' => '#/727',
'istirh' => 'Scx/Tirh',
'istirhuta' => 'Scx/Tirh',
'istitle' => 'Perl/Title',
'istitlecase' => 'Perl/Title',
'istitlecaseletter' => 'Perl/Title',
'istnsa' => '#/730',
'istoto' => '#/731',
'istransportandmap' => '#/389',
'istransportandmapsymbols' => '#/389',
'isucas' => '#/130',
'isucasext' => '#/222',
'isucasexta' => '#/251',
'isugar' => '#/732',
'isugaritic' => '#/732',
'isuideo' => 'UIdeo/Y',
'isunassigned' => 'Gc/Cn',
'isunicode' => '#/2',
'isunifiedcanadianaboriginalsyllabics' => '#/130',
'isunifiedcanadianaboriginalsyllabicsextended' => '#/222',
'isunifiedcanadianaboriginalsyllabicsextendeda' => '#/251',
'isunifiedideograph' => 'UIdeo/Y',
'isunknown' => 'Scx/Zzzz',
'isupper' => 'Upper/Y',
'isuppercase' => 'Upper/Y',
'isuppercaseletter' => 'Gc/Lu',
'isvai' => '#/653',
'isvaii' => '#/653',
'isvariationselector' => 'VS/Y',
'isvariationselectors' => '#/109',
'isvariationselectorssupplement' => '#/149',
'isvedicext' => '#/253',
'isvedicextensions' => '#/253',
'isverticalforms' => '#/365',
'isvertspace' => '#/4',
'isvith' => 'Scx/Vith',
'isvithkuqi' => 'Scx/Vith',
'isvs' => 'VS/Y',
'isvssup' => '#/149',
'iswancho' => '#/734',
'iswara' => '#/733',
'iswarangciti' => '#/733',
'iswcho' => '#/734',
'iswhitespace' => 'Perl/SpacePer',
'isword' => 'Perl/Word',
'iswspace' => 'Perl/SpacePer',
'isxdigit' => 'Hex/Y',
'isxidc' => 'XIDC/Y',
'isxidcontinue' => 'XIDC/Y',
'isxids' => 'XIDS/Y',
'isxidstart' => 'XIDS/Y',
'isxpeo' => '#/735',
'isxperlspace' => 'Perl/SpacePer',
'isxposixalnum' => 'Perl/Alnum',
'isxposixalpha' => 'Alpha/Y',
'isxposixblank' => 'Perl/Blank',
'isxposixcntrl' => '#/439',
'isxposixdigit' => 'Gc/Nd',
'isxposixgraph' => 'Perl/Graph',
'isxposixlower' => 'Lower/Y',
'isxposixprint' => 'Perl/Print',
'isxposixpunct' => 'Perl/XPosixPu',
'isxposixspace' => 'Perl/SpacePer',
'isxposixupper' => 'Upper/Y',
'isxposixword' => 'Perl/Word',
'isxposixxdigit' => 'Hex/Y',
'isxsux' => 'Scx/Xsux',
'isyezi' => 'Scx/Yezi',
'isyezidi' => 'Scx/Yezi',
'isyi' => 'Scx/Yi',
'isyiii' => 'Scx/Yi',
'isyijing' => '#/178',
'isyijinghexagramsymbols' => '#/178',
'isyiradicals' => '#/311',
'isyisyllables' => '#/337',
'isz' => 'Gc/Z',
'iszanabazarsquare' => '#/736',
'iszanb' => '#/736',
'iszinh' => 'Scx/Zinh',
'iszl' => '#/441',
'isznamennymusic' => '#/366',
'isznamennymusicalnotation' => '#/366',
'iszp' => '#/442',
'iszs' => 'Gc/Zs',
'iszyyy' => 'Scx/Zyyy',
'iszzzz' => 'Scx/Zzzz',
'ital' => '#/679',
'jamo' => '#/120',
'jamoexta' => '#/239',
'jamoextb' => '#/240',
'java' => '#/680',
'javanese' => '#/680',
'jg=africanfeh' => '#/474',
'jg=africannoon' => '#/480',
'jg=africanqaf' => '#/475',
'jg=ain' => 'Jg/Ain',
'jg=alaph' => '#/461',
'jg=alef' => 'Jg/Alef',
'jg=beh' => 'Jg/Beh',
'jg=beth' => '#/452',
'jg=burushaskiyehbarree' => '#/526',
'jg=dal' => 'Jg/Dal',
'jg=dalathrish' => '#/476',
'jg=e' => '#/443',
'jg=farsiyeh' => 'Jg/FarsiYeh',
'jg=fe' => '#/444',
'jg=feh' => 'Jg/Feh',
'jg=finalsemkath' => '#/486',
'jg=gaf' => 'Jg/Gaf',
'jg=gamal' => '#/462',
'jg=hah' => 'Jg/Hah',
'jg=hamzaonhehgoal' => '#/504',
'jg=hanifirohingyakinnaya' => 'Jg/HanifiRo',
'jg=hanifirohingyapa' => '#/518',
'jg=he' => '#/445',
'jg=heh' => '#/447',
'jg=hehgoal' => '#/468',
'jg=heth' => '#/453',
'jg=kaf' => 'Jg/Kaf',
'jg=kaph' => '#/454',
'jg=khaph' => '#/463',
'jg=knottedheh' => '#/477',
'jg=lam' => 'Jg/Lam',
'jg=lamadh' => '#/466',
'jg=malayalambha' => '#/487',
'jg=malayalamja' => '#/481',
'jg=malayalamlla' => '#/488',
'jg=malayalamllla' => '#/496',
'jg=malayalamnga' => '#/489',
'jg=malayalamnna' => '#/490',
'jg=malayalamnnna' => '#/497',
'jg=malayalamnya' => '#/491',
'jg=malayalamra' => '#/482',
'jg=malayalamssa' => '#/492',
'jg=malayalamtta' => '#/493',
'jg=manichaeanaleph' => '#/514',
'jg=manichaeanayin' => '#/505',
'jg=manichaeanbeth' => '#/506',
'jg=manichaeandaleth' => '#/519',
'jg=manichaeandhamedh' => '#/523',
'jg=manichaeanfive' => '#/507',
'jg=manichaeangimel' => '#/515',
'jg=manichaeanheth' => '#/508',
'jg=manichaeanhundred' => '#/524',
'jg=manichaeankaph' => '#/509',
'jg=manichaeanlamedh' => '#/520',
'jg=manichaeanmem' => '#/498',
'jg=manichaeannun' => '#/499',
'jg=manichaeanone' => '#/500',
'jg=manichaeanpe' => '#/494',
'jg=manichaeanqoph' => '#/510',
'jg=manichaeanresh' => '#/511',
'jg=manichaeansadhe' => '#/516',
'jg=manichaeansamekh' => '#/521',
'jg=manichaeantaw' => '#/501',
'jg=manichaeanten' => '#/502',
'jg=manichaeanteth' => '#/512',
'jg=manichaeanthamedh' => '#/525',
'jg=manichaeantwenty' => '#/522',
'jg=manichaeanwaw' => '#/503',
'jg=manichaeanyodh' => '#/513',
'jg=manichaeanzayin' => '#/517',
'jg=meem' => '#/455',
'jg=mim' => '#/448',
'jg=nojoininggroup' => 'Jg/NoJoinin',
'jg=noon' => 'Jg/Noon',
'jg=nun' => '#/449',
'jg=nya' => '#/450',
'jg=pe' => '#/446',
'jg=qaf' => 'Jg/Qaf',
'jg=qaph' => '#/456',
'jg=reh' => 'Jg/Reh',
'jg=reversedpe' => '#/478',
'jg=rohingyayeh' => '#/483',
'jg=sad' => 'Jg/Sad',
'jg=sadhe' => '#/464',
'jg=seen' => 'Jg/Seen',
'jg=semkath' => '#/469',
'jg=shin' => '#/457',
'jg=straightwaw' => '#/484',
'jg=swashkaf' => '#/471',
'jg=syriacwaw' => '#/472',
'jg=tah' => 'Jg/Tah',
'jg=taw' => '#/451',
'jg=tehmarbuta' => '#/479',
'jg=tehmarbutagoal' => '#/504',
'jg=teth' => '#/458',
'jg=thinyeh' => '#/470',
'jg=verticaltail' => '#/495',
'jg=waw' => 'Jg/Waw',
'jg=yeh' => 'Jg/Yeh',
'jg=yehbarree' => '#/473',
'jg=yehwithtail' => '#/485',
'jg=yudh' => '#/459',
'jg=yudhhe' => '#/467',
'jg=zain' => '#/460',
'jg=zhain' => '#/465',
'joinc' => '#/87',
'joinc=f' => '#/!87',
'joinc=false' => '#/!87',
'joinc=n' => '#/!87',
'joinc=no' => '#/!87',
'joinc=t' => '#/87',
'joinc=true' => '#/87',
'joinc=y' => '#/87',
'joinc=yes' => '#/87',
'joincontrol' => '#/87',
'jt=c' => 'Jt/C',
'jt=d' => 'Jt/D',
'jt=dualjoining' => 'Jt/D',
'jt=joincausing' => 'Jt/C',
'jt=l' => 'Jt/L',
'jt=leftjoining' => 'Jt/L',
'jt=nonjoining' => 'Jt/U',
'jt=r' => 'Jt/R',
'jt=rightjoining' => 'Jt/R',
'jt=t' => 'Jt/T',
'jt=transparent' => 'Jt/T',
'jt=u' => 'Jt/U',
'kaithi' => 'Scx/Kthi',
'kaktoviknumerals' => '#/397',
'kali' => '#/197',
'kana' => 'Scx/Kana',
'kanaexta' => '#/242',
'kanaextb' => '#/243',
'kanaextendeda' => '#/242',
'kanaextendedb' => '#/243',
'kanasup' => '#/195',
'kanasupplement' => '#/195',
'kanbun' => '#/163',
'kangxi' => '#/164',
'kangxiradicals' => '#/164',
'kannada' => 'Scx/Knda',
'katakana' => 'Scx/Kana',
'katakanaext' => '#/322',
'katakanaphoneticextensions' => '#/322',
'kawi' => '#/681',
'kayahli' => '#/197',
'khar' => 'Scx/Khar',
'kharoshthi' => 'Scx/Khar',
'khitansmallscript' => '#/682',
'khmer' => 'Scx/Khmr',
'khmersymbols' => '#/348',
'khmr' => 'Scx/Khmr',
'khoj' => 'Scx/Khoj',
'khojki' => 'Scx/Khoj',
'khudawadi' => 'Scx/Sind',
'kits' => '#/682',
'knda' => 'Scx/Knda',
'kthi' => 'Scx/Kthi',
'l' => 'Gc/L',
'l&' => 'Gc/LC',
'l_' => 'Gc/LC',
'lana' => 'Scx/Lana',
'lao' => 'Scx/Lao',
'laoo' => 'Scx/Lao',
'latin' => 'Scx/Latn',
'latin1' => '#/166',
'latin1sup' => '#/166',
'latin1supplement' => '#/166',
'latinexta' => '#/264',
'latinextadditional' => '#/413',
'latinextb' => '#/265',
'latinextc' => '#/266',
'latinextd' => '#/267',
'latinexte' => '#/268',
'latinextendeda' => '#/264',
'latinextendedadditional' => '#/413',
'latinextendedb' => '#/265',
'latinextendedc' => '#/266',
'latinextendedd' => '#/267',
'latinextendede' => '#/268',
'latinextendedf' => '#/269',
'latinextendedg' => '#/270',
'latinextf' => '#/269',
'latinextg' => '#/270',
'latn' => 'Scx/Latn',
'lb=ai' => 'Lb/AI',
'lb=al' => 'Lb/AL',
'lb=alphabetic' => 'Lb/AL',
'lb=ambiguous' => 'Lb/AI',
'lb=b2' => '#/527',
'lb=ba' => 'Lb/BA',
'lb=bb' => 'Lb/BB',
'lb=bk' => '#/528',
'lb=breakafter' => 'Lb/BA',
'lb=breakbefore' => 'Lb/BB',
'lb=breakboth' => '#/527',
'lb=breaksymbols' => '#/535',
'lb=carriagereturn' => '#/66',
'lb=cb' => '#/529',
'lb=cj' => 'Lb/CJ',
'lb=cl' => 'Lb/CL',
'lb=closeparenthesis' => '#/530',
'lb=closepunctuation' => 'Lb/CL',
'lb=cm' => 'Lb/CM',
'lb=combiningmark' => 'Lb/CM',
'lb=complexcontext' => 'Lb/SA',
'lb=conditionaljapanesestarter' => 'Lb/CJ',
'lb=contingentbreak' => '#/529',
'lb=cp' => '#/530',
'lb=cr' => '#/66',
'lb=eb' => 'EBase/Y',
'lb=ebase' => 'EBase/Y',
'lb=em' => '#/61',
'lb=emodifier' => '#/61',
'lb=ex' => 'Lb/EX',
'lb=exclamation' => 'Lb/EX',
'lb=gl' => 'Lb/GL',
'lb=glue' => 'Lb/GL',
'lb=h2' => 'GCB/LV',
'lb=h3' => 'GCB/LVT',
'lb=hebrewletter' => 'WB/HL',
'lb=hl' => 'WB/HL',
'lb=hy' => '#/531',
'lb=hyphen' => '#/531',
'lb=id' => 'Lb/ID',
'lb=ideographic' => 'Lb/ID',
'lb=in' => 'Lb/IN',
'lb=infixnumeric' => 'Lb/IS',
'lb=inseparable' => 'Lb/IN',
'lb=inseperable' => 'Lb/IN',
'lb=is' => 'Lb/IS',
'lb=jl' => '#/63',
'lb=jt' => '#/64',
'lb=jv' => '#/65',
'lb=lf' => '#/67',
'lb=linefeed' => '#/67',
'lb=mandatorybreak' => '#/528',
'lb=nextline' => '#/532',
'lb=nl' => '#/532',
'lb=nonstarter' => 'Lb/NS',
'lb=ns' => 'Lb/NS',
'lb=nu' => 'Lb/NU',
'lb=numeric' => 'Lb/NU',
'lb=op' => 'Lb/OP',
'lb=openpunctuation' => 'Lb/OP',
'lb=po' => 'Lb/PO',
'lb=postfixnumeric' => 'Lb/PO',
'lb=pr' => 'Lb/PR',
'lb=prefixnumeric' => 'Lb/PR',
'lb=qu' => 'Lb/QU',
'lb=quotation' => 'Lb/QU',
'lb=regionalindicator' => '#/68',
'lb=ri' => '#/68',
'lb=sa' => 'Lb/SA',
'lb=sg' => '#/533',
'lb=sp' => '#/534',
'lb=space' => '#/534',
'lb=surrogate' => '#/533',
'lb=sy' => '#/535',
'lb=unknown' => 'Lb/XX',
'lb=wj' => '#/536',
'lb=wordjoiner' => '#/536',
'lb=xx' => 'Lb/XX',
'lb=zw' => '#/537',
'lb=zwj' => '#/69',
'lb=zwspace' => '#/537',
'lc' => 'Gc/LC',
'lepc' => '#/683',
'lepcha' => '#/683',
'letter' => 'Gc/L',
'letterlikesymbols' => '#/408',
'letternumber' => 'Gc/Nl',
'limb' => 'Scx/Limb',
'limbu' => 'Scx/Limb',
'lina' => 'Scx/Lina',
'linb' => 'Scx/Linb',
'lineara' => 'Scx/Lina',
'linearb' => 'Scx/Linb',
'linearbideograms' => '#/398',
'linearbsyllabary' => '#/399',
'lineseparator' => '#/441',
'lisu' => '#/684',
'lisusup' => '#/199',
'lisusupplement' => '#/199',
'll' => 'Gc/Ll',
'lm' => 'Gc/Lm',
'lo' => 'Gc/Lo',
'loe' => 'InPC/VisualOr',
'loe=f' => '!InPC/VisualOr',
'loe=false' => '!InPC/VisualOr',
'loe=n' => '!InPC/VisualOr',
'loe=no' => '!InPC/VisualOr',
'loe=t' => 'InPC/VisualOr',
'loe=true' => 'InPC/VisualOr',
'loe=y' => 'InPC/VisualOr',
'loe=yes' => 'InPC/VisualOr',
'logicalorderexception' => 'InPC/VisualOr',
'lower' => 'Lower/Y',
'lower=f' => '!Lower/Y',
'lower=false' => '!Lower/Y',
'lower=n' => '!Lower/Y',
'lower=no' => '!Lower/Y',
'lower=t' => 'Lower/Y',
'lower=true' => 'Lower/Y',
'lower=y' => 'Lower/Y',
'lower=yes' => 'Lower/Y',
'lowercase' => 'Lower/Y',
'lowercaseletter' => 'Gc/Ll',
'lowsurrogates' => '#/361',
'lt' => 'Perl/Title',
'lu' => 'Gc/Lu',
'lyci' => '#/685',
'lycian' => '#/685',
'lydi' => '#/686',
'lydian' => '#/686',
'm' => 'Gc/M',
'mahajani' => '#/687',
'mahj' => '#/687',
'mahjong' => '#/200',
'mahjongtiles' => '#/200',
'maka' => '#/688',
'makasar' => '#/688',
'malayalam' => 'Scx/Mlym',
'mand' => '#/689',
'mandaic' => '#/689',
'mani' => '#/690',
'manichaean' => '#/690',
'marc' => '#/691',
'marchen' => '#/691',
'mark' => 'Gc/M',
'masaramgondi' => 'Scx/Gonm',
'math' => 'Math/Y',
'math=f' => '!Math/Y',
'math=false' => '!Math/Y',
'math=n' => '!Math/Y',
'math=no' => '!Math/Y',
'math=t' => 'Math/Y',
'math=true' => 'Math/Y',
'math=y' => 'Math/Y',
'math=yes' => 'Math/Y',
'mathalphanum' => '#/350',
'mathematicalalphanumericsymbols' => '#/350',
'mathematicaloperators' => '#/362',
'mathoperators' => '#/362',
'mathsymbol' => 'Gc/Sm',
'mayannumerals' => '#/363',
'mc' => 'Gc/Mc',
'me' => 'Gc/Me',
'medefaidrin' => '#/692',
'medf' => '#/692',
'meeteimayek' => '#/697',
'meeteimayekext' => '#/374',
'meeteimayekextensions' => '#/374',
'mend' => '#/693',
'mendekikakui' => '#/693',
'merc' => '#/694',
'mero' => '#/419',
'meroiticcursive' => '#/694',
'meroitichieroglyphs' => '#/419',
'miao' => '#/695',
'miscarrows' => '#/300',
'miscellaneousmathematicalsymbolsa' => '#/400',
'miscellaneousmathematicalsymbolsb' => '#/401',
'miscellaneoussymbols' => '#/325',
'miscellaneoussymbolsandarrows' => '#/300',
'miscellaneoussymbolsandpictographs' => '#/385',
'miscellaneoustechnical' => '#/364',
'miscmathsymbolsa' => '#/400',
'miscmathsymbolsb' => '#/401',
'miscpictographs' => '#/385',
'miscsymbols' => '#/325',
'misctechnical' => '#/364',
'mlym' => 'Scx/Mlym',
'mn' => 'Gc/Mn',
'modi' => '#/696',
'modifierletter' => 'Gc/Lm',
'modifierletters' => '#/386',
'modifiersymbol' => 'Gc/Sk',
'modifiertoneletters' => '#/420',
'mong' => 'Scx/Mong',
'mongolian' => 'Scx/Mong',
'mongoliansup' => '#/352',
'mongoliansupplement' => '#/352',
'mro' => '#/652',
'mroo' => '#/652',
'mtei' => '#/697',
'mult' => 'Scx/Mult',
'multani' => 'Scx/Mult',
'music' => '#/140',
'musicalsymbols' => '#/140',
'myanmar' => 'Scx/Mymr',
'myanmarexta' => '#/326',
'myanmarextb' => '#/327',
'myanmarextendeda' => '#/326',
'myanmarextendedb' => '#/327',
'mymr' => 'Scx/Mymr',
'n' => 'Gc/N',
'nabataean' => '#/699',
'nagm' => '#/698',
'nagmundari' => '#/698',
'nand' => 'Scx/Nand',
'nandinagari' => 'Scx/Nand',
'narb' => '#/387',
'nb' => 'Blk/NB',
'nbat' => '#/699',
'nchar' => 'Perl/_PerlNch',
'nchar=f' => '!Perl/_PerlNch',
'nchar=false' => '!Perl/_PerlNch',
'nchar=n' => '!Perl/_PerlNch',
'nchar=no' => '!Perl/_PerlNch',
'nchar=t' => 'Perl/_PerlNch',
'nchar=true' => 'Perl/_PerlNch',
'nchar=y' => 'Perl/_PerlNch',
'nchar=yes' => 'Perl/_PerlNch',
'nd' => 'Gc/Nd',
'newa' => '#/700',
'newtailue' => 'Scx/Talu',
'nfcqc=m' => 'NFCQC/M',
'nfcqc=maybe' => 'NFCQC/M',
'nfcqc=n' => 'CompEx/Y',
'nfcqc=no' => 'CompEx/Y',
'nfcqc=y' => 'NFCQC/Y',
'nfcqc=yes' => 'NFCQC/Y',
'nfdqc=n' => 'NFDQC/N',
'nfdqc=no' => 'NFDQC/N',
'nfdqc=y' => 'NFDQC/Y',
'nfdqc=yes' => 'NFDQC/Y',
'nfkcqc=m' => 'NFCQC/M',
'nfkcqc=maybe' => 'NFCQC/M',
'nfkcqc=n' => 'NFKCQC/N',
'nfkcqc=no' => 'NFKCQC/N',
'nfkcqc=y' => 'NFKCQC/Y',
'nfkcqc=yes' => 'NFKCQC/Y',
'nfkdqc=n' => 'NFKDQC/N',
'nfkdqc=no' => 'NFKDQC/N',
'nfkdqc=y' => 'NFKDQC/Y',
'nfkdqc=yes' => 'NFKDQC/Y',
'nko' => 'Scx/Nko',
'nkoo' => 'Scx/Nko',
'nl' => 'Gc/Nl',
'no' => 'Gc/No',
'noblock' => 'Blk/NB',
'noncharactercodepoint' => 'Perl/_PerlNch',
'nonspacingmark' => 'Gc/Mn',
'nshu' => '#/701',
'nt=de' => 'Gc/Nd',
'nt=decimal' => 'Gc/Nd',
'nt=di' => 'Nt/Di',
'nt=digit' => 'Nt/Di',
'nt=none' => 'Nt/None',
'nt=nu' => 'Nt/Nu',
'nt=numeric' => 'Nt/Nu',
'number' => 'Gc/N',
'numberforms' => '#/329',
'nushu' => '#/701',
'nv=nan' => 'Nt/None',
'nyiakengpuachuehmong' => 'Scx/Hmnp',
'ocr' => '#/115',
'ogam' => '#/702',
'ogham' => '#/702',
'olchiki' => '#/206',
'olck' => '#/206',
'oldhungarian' => '#/678',
'olditalic' => '#/679',
'oldnortharabian' => '#/387',
'oldpermic' => '#/708',
'oldpersian' => '#/735',
'oldsogdian' => '#/720',
'oldsoutharabian' => '#/388',
'oldturkic' => '#/703',
'olduyghur' => '#/706',
'openpunctuation' => 'Gc/Ps',
'opticalcharacterrecognition' => '#/115',
'oriya' => 'Scx/Orya',
'orkh' => '#/703',
'ornamentaldingbats' => '#/414',
'orya' => 'Scx/Orya',
'osage' => '#/704',
'osge' => '#/704',
'osma' => '#/705',
'osmanya' => '#/705',
'other' => 'Gc/C',
'otherletter' => 'Gc/Lo',
'othernumber' => 'Gc/No',
'otherpunctuation' => 'Gc/Po',
'othersymbol' => 'Gc/So',
'ottomansiyaqnumbers' => '#/421',
'ougr' => '#/706',
'p' => 'Gc/P',
'pahawhhmong' => 'Scx/Hmng',
'palm' => '#/279',
'palmyrene' => '#/279',
'paragraphseparator' => '#/442',
'patsyn' => 'PatSyn/Y',
'patsyn=f' => '!PatSyn/Y',
'patsyn=false' => '!PatSyn/Y',
'patsyn=n' => '!PatSyn/Y',
'patsyn=no' => '!PatSyn/Y',
'patsyn=t' => 'PatSyn/Y',
'patsyn=true' => 'PatSyn/Y',
'patsyn=y' => 'PatSyn/Y',
'patsyn=yes' => 'PatSyn/Y',
'patternsyntax' => 'PatSyn/Y',
'patternwhitespace' => 'Perl/_PerlPat',
'patws' => 'Perl/_PerlPat',
'patws=f' => '!Perl/_PerlPat',
'patws=false' => '!Perl/_PerlPat',
'patws=n' => '!Perl/_PerlPat',
'patws=no' => '!Perl/_PerlPat',
'patws=t' => 'Perl/_PerlPat',
'patws=true' => 'Perl/_PerlPat',
'patws=y' => 'Perl/_PerlPat',
'patws=yes' => 'Perl/_PerlPat',
'pauc' => '#/707',
'paucinhau' => '#/707',
'pc' => 'Gc/Pc',
'pcm' => 'PCM/Y',
'pcm=f' => '!PCM/Y',
'pcm=false' => '!PCM/Y',
'pcm=n' => '!PCM/Y',
'pcm=no' => '!PCM/Y',
'pcm=t' => 'PCM/Y',
'pcm=true' => 'PCM/Y',
'pcm=y' => 'PCM/Y',
'pcm=yes' => 'PCM/Y',
'pd' => 'Gc/Pd',
'pe' => 'Gc/Pe',
'perlspace' => '#/3',
'perlword' => 'Perl/PerlWord',
'perm' => '#/708',
'pf' => 'Gc/Pf',
'phag' => '#/709',
'phagspa' => '#/709',
'phaistos' => '#/246',
'phaistosdisc' => '#/246',
'phli' => '#/710',
'phlp' => 'Scx/Phlp',
'phnx' => '#/711',
'phoenician' => '#/711',
'phoneticext' => '#/331',
'phoneticextensions' => '#/331',
'phoneticextensionssupplement' => '#/375',
'phoneticextsup' => '#/375',
'pi' => 'Gc/Pi',
'playingcards' => '#/354',
'plrd' => '#/695',
'po' => 'Gc/Po',
'posixalnum' => '#/5',
'posixalpha' => '#/6',
'posixblank' => '#/7',
'posixcntrl' => '#/8',
'posixdigit' => '#/9',
'posixgraph' => '#/10',
'posixlower' => '#/11',
'posixprint' => '#/12',
'posixpunct' => 'Perl/PosixPun',
'posixspace' => '#/3',
'posixupper' => '#/13',
'posixword' => 'Perl/PerlWord',
'posixxdigit' => '#/60',
'prependedconcatenationmark' => 'PCM/Y',
'print' => 'Perl/Print',
'privateuse' => '#/440',
'privateusearea' => '#/116',
'prti' => '#/712',
'ps' => 'Gc/Ps',
'psalterpahlavi' => 'Scx/Phlp',
'pua' => '#/116',
'punct' => 'Gc/P',
'punctuation' => 'Gc/P',
'qaac' => 'Scx/Copt',
'qaai' => 'Scx/Zinh',
'qmark' => 'QMark/Y',
'qmark=f' => '!QMark/Y',
'qmark=false' => '!QMark/Y',
'qmark=n' => '!QMark/Y',
'qmark=no' => '!QMark/Y',
'qmark=t' => 'QMark/Y',
'qmark=true' => 'QMark/Y',
'qmark=y' => 'QMark/Y',
'qmark=yes' => 'QMark/Y',
'quotationmark' => 'QMark/Y',
'radical' => '#/90',
'radical=f' => '#/!90',
'radical=false' => '#/!90',
'radical=n' => '#/!90',
'radical=no' => '#/!90',
'radical=t' => '#/90',
'radical=true' => '#/90',
'radical=y' => '#/90',
'radical=yes' => '#/90',
'regionalindicator' => '#/68',
'rejang' => '#/713',
'ri' => '#/68',
'ri=f' => '#/!68',
'ri=false' => '#/!68',
'ri=n' => '#/!68',
'ri=no' => '#/!68',
'ri=t' => '#/68',
'ri=true' => '#/68',
'ri=y' => '#/68',
'ri=yes' => '#/68',
'rjng' => '#/713',
'rohg' => 'Scx/Rohg',
'rumi' => '#/126',
'ruminumeralsymbols' => '#/126',
'runic' => '#/714',
'runr' => '#/714',
's' => 'Gc/S',
'samaritan' => '#/715',
'samr' => '#/715',
'sarb' => '#/388',
'saur' => '#/716',
'saurashtra' => '#/716',
'sb=at' => 'SB/AT',
'sb=aterm' => 'SB/AT',
'sb=cl' => 'SB/CL',
'sb=close' => 'SB/CL',
'sb=cr' => '#/66',
'sb=ex' => 'SB/EX',
'sb=extend' => 'SB/EX',
'sb=fo' => 'SB/FO',
'sb=format' => 'SB/FO',
'sb=le' => 'SB/LE',
'sb=lf' => '#/67',
'sb=lo' => 'SB/LO',
'sb=lower' => 'SB/LO',
'sb=nu' => 'SB/NU',
'sb=numeric' => 'SB/NU',
'sb=oletter' => 'SB/LE',
'sb=other' => 'SB/XX',
'sb=sc' => 'SB/SC',
'sb=scontinue' => 'SB/SC',
'sb=se' => '#/91',
'sb=sep' => '#/91',
'sb=sp' => 'SB/Sp',
'sb=st' => 'SB/ST',
'sb=sterm' => 'SB/ST',
'sb=up' => 'SB/UP',
'sb=upper' => 'SB/UP',
'sb=xx' => 'SB/XX',
'sc' => 'Gc/Sc',
'sc=adlam' => '#/617',
'sc=adlm' => '#/617',
'sc=aghb' => '#/654',
'sc=ahom' => '#/655',
'sc=anatolianhieroglyphs' => '#/677',
'sc=arab' => 'Sc/Arab',
'sc=arabic' => 'Sc/Arab',
'sc=armenian' => 'Scx/Armn',
'sc=armi' => '#/656',
'sc=armn' => 'Scx/Armn',
'sc=avestan' => '#/657',
'sc=avst' => '#/657',
'sc=bali' => '#/658',
'sc=balinese' => '#/658',
'sc=bamu' => '#/659',
'sc=bamum' => '#/659',
'sc=bass' => '#/660',
'sc=bassavah' => '#/660',
'sc=batak' => '#/661',
'sc=batk' => '#/661',
'sc=beng' => 'Sc/Beng',
'sc=bengali' => 'Sc/Beng',
'sc=bhaiksuki' => 'Scx/Bhks',
'sc=bhks' => 'Scx/Bhks',
'sc=bopo' => '#/618',
'sc=bopomofo' => '#/618',
'sc=brah' => '#/662',
'sc=brahmi' => '#/662',
'sc=brai' => '#/181',
'sc=braille' => '#/181',
'sc=bugi' => '#/619',
'sc=buginese' => '#/619',
'sc=buhd' => '#/620',
'sc=buhid' => '#/620',
'sc=cakm' => '#/621',
'sc=canadianaboriginal' => '#/665',
'sc=cans' => '#/665',
'sc=cari' => '#/666',
'sc=carian' => '#/666',
'sc=caucasianalbanian' => '#/654',
'sc=chakma' => '#/621',
'sc=cham' => 'Scx/Cham',
'sc=cher' => '#/667',
'sc=cherokee' => '#/667',
'sc=chorasmian' => '#/668',
'sc=chrs' => '#/668',
'sc=common' => 'Sc/Zyyy',
'sc=copt' => '#/622',
'sc=coptic' => '#/622',
'sc=cpmn' => '#/623',
'sc=cprt' => 'Sc/Cprt',
'sc=cuneiform' => 'Scx/Xsux',
'sc=cypriot' => 'Sc/Cprt',
'sc=cyprominoan' => '#/623',
'sc=cyrillic' => 'Sc/Cyrl',
'sc=cyrl' => 'Sc/Cyrl',
'sc=deseret' => '#/190',
'sc=deva' => 'Sc/Deva',
'sc=devanagari' => 'Sc/Deva',
'sc=diak' => 'Scx/Diak',
'sc=divesakuru' => 'Scx/Diak',
'sc=dogr' => '#/624',
'sc=dogra' => '#/624',
'sc=dsrt' => '#/190',
'sc=dupl' => 'Sc/Dupl',
'sc=duployan' => 'Sc/Dupl',
'sc=egyp' => '#/671',
'sc=egyptianhieroglyphs' => '#/671',
'sc=elba' => '#/672',
'sc=elbasan' => '#/672',
'sc=elym' => '#/673',
'sc=elymaic' => '#/673',
'sc=ethi' => 'Scx/Ethi',
'sc=ethiopic' => 'Scx/Ethi',
'sc=geor' => 'Sc/Geor',
'sc=georgian' => 'Sc/Geor',
'sc=glag' => 'Sc/Glag',
'sc=glagolitic' => 'Sc/Glag',
'sc=gong' => 'Sc/Gong',
'sc=gonm' => 'Sc/Gonm',
'sc=goth' => '#/674',
'sc=gothic' => '#/674',
'sc=gran' => 'Sc/Gran',
'sc=grantha' => 'Sc/Gran',
'sc=greek' => 'Sc/Grek',
'sc=grek' => 'Sc/Grek',
'sc=gujarati' => 'Sc/Gujr',
'sc=gujr' => 'Sc/Gujr',
'sc=gunjalagondi' => 'Sc/Gong',
'sc=gurmukhi' => 'Sc/Guru',
'sc=guru' => 'Sc/Guru',
'sc=han' => 'Sc/Han',
'sc=hang' => 'Sc/Hang',
'sc=hangul' => 'Sc/Hang',
'sc=hani' => 'Sc/Han',
'sc=hanifirohingya' => '#/641',
'sc=hano' => '#/625',
'sc=hanunoo' => '#/625',
'sc=hatr' => '#/676',
'sc=hatran' => '#/676',
'sc=hebr' => 'Scx/Hebr',
'sc=hebrew' => 'Scx/Hebr',
'sc=hira' => 'Sc/Hira',
'sc=hiragana' => 'Sc/Hira',
'sc=hluw' => '#/677',
'sc=hmng' => 'Scx/Hmng',
'sc=hmnp' => 'Scx/Hmnp',
'sc=hung' => '#/678',
'sc=imperialaramaic' => '#/656',
'sc=inherited' => 'Sc/Zinh',
'sc=inscriptionalpahlavi' => '#/710',
'sc=inscriptionalparthian' => '#/712',
'sc=ital' => '#/679',
'sc=java' => '#/626',
'sc=javanese' => '#/626',
'sc=kaithi' => '#/629',
'sc=kali' => '#/627',
'sc=kana' => 'Sc/Kana',
'sc=kannada' => 'Sc/Knda',
'sc=katakana' => 'Sc/Kana',
'sc=kawi' => '#/681',
'sc=kayahli' => '#/627',
'sc=khar' => 'Scx/Khar',
'sc=kharoshthi' => 'Scx/Khar',
'sc=khitansmallscript' => '#/682',
'sc=khmer' => 'Scx/Khmr',
'sc=khmr' => 'Scx/Khmr',
'sc=khoj' => '#/628',
'sc=khojki' => '#/628',
'sc=khudawadi' => '#/642',
'sc=kits' => '#/682',
'sc=knda' => 'Sc/Knda',
'sc=kthi' => '#/629',
'sc=lana' => 'Scx/Lana',
'sc=lao' => 'Scx/Lao',
'sc=laoo' => 'Scx/Lao',
'sc=latin' => 'Sc/Latn',
'sc=latn' => 'Sc/Latn',
'sc=lepc' => '#/683',
'sc=lepcha' => '#/683',
'sc=limb' => 'Sc/Limb',
'sc=limbu' => 'Sc/Limb',
'sc=lina' => '#/630',
'sc=linb' => 'Sc/Linb',
'sc=lineara' => '#/630',
'sc=linearb' => 'Sc/Linb',
'sc=lisu' => '#/684',
'sc=lyci' => '#/685',
'sc=lycian' => '#/685',
'sc=lydi' => '#/686',
'sc=lydian' => '#/686',
'sc=mahajani' => '#/631',
'sc=mahj' => '#/631',
'sc=maka' => '#/688',
'sc=makasar' => '#/688',
'sc=malayalam' => 'Sc/Mlym',
'sc=mand' => '#/632',
'sc=mandaic' => '#/632',
'sc=mani' => '#/633',
'sc=manichaean' => '#/633',
'sc=marc' => '#/691',
'sc=marchen' => '#/691',
'sc=masaramgondi' => 'Sc/Gonm',
'sc=medefaidrin' => '#/692',
'sc=medf' => '#/692',
'sc=meeteimayek' => '#/697',
'sc=mend' => '#/693',
'sc=mendekikakui' => '#/693',
'sc=merc' => '#/694',
'sc=mero' => '#/419',
'sc=meroiticcursive' => '#/694',
'sc=meroitichieroglyphs' => '#/419',
'sc=miao' => '#/695',
'sc=mlym' => 'Sc/Mlym',
'sc=modi' => '#/634',
'sc=mong' => 'Sc/Mong',
'sc=mongolian' => 'Sc/Mong',
'sc=mro' => '#/652',
'sc=mroo' => '#/652',
'sc=mtei' => '#/697',
'sc=mult' => 'Sc/Mult',
'sc=multani' => 'Sc/Mult',
'sc=myanmar' => '#/635',
'sc=mymr' => '#/635',
'sc=nabataean' => '#/699',
'sc=nagm' => '#/698',
'sc=nagmundari' => '#/698',
'sc=nand' => '#/636',
'sc=nandinagari' => '#/636',
'sc=narb' => '#/387',
'sc=nbat' => '#/699',
'sc=newa' => '#/700',
'sc=newtailue' => 'Scx/Talu',
'sc=nko' => '#/616',
'sc=nkoo' => '#/616',
'sc=nshu' => '#/701',
'sc=nushu' => '#/701',
'sc=nyiakengpuachuehmong' => 'Scx/Hmnp',
'sc=ogam' => '#/702',
'sc=ogham' => '#/702',
'sc=olchiki' => '#/206',
'sc=olck' => '#/206',
'sc=oldhungarian' => '#/678',
'sc=olditalic' => '#/679',
'sc=oldnortharabian' => '#/387',
'sc=oldpermic' => '#/638',
'sc=oldpersian' => '#/735',
'sc=oldsogdian' => '#/720',
'sc=oldsoutharabian' => '#/388',
'sc=oldturkic' => '#/703',
'sc=olduyghur' => '#/637',
'sc=oriya' => 'Sc/Orya',
'sc=orkh' => '#/703',
'sc=orya' => 'Sc/Orya',
'sc=osage' => '#/704',
'sc=osge' => '#/704',
'sc=osma' => '#/705',
'sc=osmanya' => '#/705',
'sc=ougr' => '#/637',
'sc=pahawhhmong' => 'Scx/Hmng',
'sc=palm' => '#/279',
'sc=palmyrene' => '#/279',
'sc=pauc' => '#/707',
'sc=paucinhau' => '#/707',
'sc=perm' => '#/638',
'sc=phag' => '#/639',
'sc=phagspa' => '#/639',
'sc=phli' => '#/710',
'sc=phlp' => '#/640',
'sc=phnx' => '#/711',
'sc=phoenician' => '#/711',
'sc=plrd' => '#/695',
'sc=prti' => '#/712',
'sc=psalterpahlavi' => '#/640',
'sc=qaac' => '#/622',
'sc=qaai' => 'Sc/Zinh',
'sc=rejang' => '#/713',
'sc=rjng' => '#/713',
'sc=rohg' => '#/641',
'sc=runic' => '#/714',
'sc=runr' => '#/714',
'sc=samaritan' => '#/715',
'sc=samr' => '#/715',
'sc=sarb' => '#/388',
'sc=saur' => '#/716',
'sc=saurashtra' => '#/716',
'sc=sgnw' => '#/717',
'sc=sharada' => '#/209',
'sc=shavian' => '#/210',
'sc=shaw' => '#/210',
'sc=shrd' => '#/209',
'sc=sidd' => '#/718',
'sc=siddham' => '#/718',
'sc=signwriting' => '#/717',
'sc=sind' => '#/642',
'sc=sinh' => 'Sc/Sinh',
'sc=sinhala' => 'Sc/Sinh',
'sc=sogd' => '#/643',
'sc=sogdian' => '#/643',
'sc=sogo' => '#/720',
'sc=sora' => '#/721',
'sc=sorasompeng' => '#/721',
'sc=soyo' => '#/722',
'sc=soyombo' => '#/722',
'sc=sund' => '#/723',
'sc=sundanese' => '#/723',
'sc=sylo' => '#/644',
'sc=sylotinagri' => '#/644',
'sc=syrc' => 'Sc/Syrc',
'sc=syriac' => 'Sc/Syrc',
'sc=tagalog' => '#/648',
'sc=tagb' => '#/645',
'sc=tagbanwa' => '#/645',
'sc=taile' => '#/647',
'sc=taitham' => 'Scx/Lana',
'sc=taiviet' => '#/726',
'sc=takr' => '#/646',
'sc=takri' => '#/646',
'sc=tale' => '#/647',
'sc=talu' => 'Scx/Talu',
'sc=tamil' => 'Sc/Taml',
'sc=taml' => 'Sc/Taml',
'sc=tang' => 'Scx/Tang',
'sc=tangsa' => '#/730',
'sc=tangut' => 'Scx/Tang',
'sc=tavt' => '#/726',
'sc=telu' => 'Sc/Telu',
'sc=telugu' => 'Sc/Telu',
'sc=tfng' => '#/727',
'sc=tglg' => '#/648',
'sc=thaa' => '#/649',
'sc=thaana' => '#/649',
'sc=thai' => '#/729',
'sc=tibetan' => 'Scx/Tibt',
'sc=tibt' => 'Scx/Tibt',
'sc=tifinagh' => '#/727',
'sc=tirh' => '#/650',
'sc=tirhuta' => '#/650',
'sc=tnsa' => '#/730',
'sc=toto' => '#/731',
'sc=ugar' => '#/732',
'sc=ugaritic' => '#/732',
'sc=unknown' => 'Scx/Zzzz',
'sc=vai' => '#/653',
'sc=vaii' => '#/653',
'sc=vith' => 'Scx/Vith',
'sc=vithkuqi' => 'Scx/Vith',
'sc=wancho' => '#/734',
'sc=wara' => '#/733',
'sc=warangciti' => '#/733',
'sc=wcho' => '#/734',
'sc=xpeo' => '#/735',
'sc=xsux' => 'Scx/Xsux',
'sc=yezi' => '#/651',
'sc=yezidi' => '#/651',
'sc=yi' => '#/615',
'sc=yiii' => '#/615',
'sc=zanabazarsquare' => '#/736',
'sc=zanb' => '#/736',
'sc=zinh' => 'Sc/Zinh',
'sc=zyyy' => 'Sc/Zyyy',
'sc=zzzz' => 'Scx/Zzzz',
'scx=adlam' => 'Scx/Adlm',
'scx=adlm' => 'Scx/Adlm',
'scx=aghb' => '#/654',
'scx=ahom' => '#/655',
'scx=anatolianhieroglyphs' => '#/677',
'scx=arab' => 'Scx/Arab',
'scx=arabic' => 'Scx/Arab',
'scx=armenian' => 'Scx/Armn',
'scx=armi' => '#/656',
'scx=armn' => 'Scx/Armn',
'scx=avestan' => '#/657',
'scx=avst' => '#/657',
'scx=bali' => '#/658',
'scx=balinese' => '#/658',
'scx=bamu' => '#/659',
'scx=bamum' => '#/659',
'scx=bass' => '#/660',
'scx=bassavah' => '#/660',
'scx=batak' => '#/661',
'scx=batk' => '#/661',
'scx=beng' => 'Scx/Beng',
'scx=bengali' => 'Scx/Beng',
'scx=bhaiksuki' => 'Scx/Bhks',
'scx=bhks' => 'Scx/Bhks',
'scx=bopo' => 'Scx/Bopo',
'scx=bopomofo' => 'Scx/Bopo',
'scx=brah' => '#/662',
'scx=brahmi' => '#/662',
'scx=brai' => '#/181',
'scx=braille' => '#/181',
'scx=bugi' => '#/663',
'scx=buginese' => '#/663',
'scx=buhd' => '#/664',
'scx=buhid' => '#/664',
'scx=cakm' => 'Scx/Cakm',
'scx=canadianaboriginal' => '#/665',
'scx=cans' => '#/665',
'scx=cari' => '#/666',
'scx=carian' => '#/666',
'scx=caucasianalbanian' => '#/654',
'scx=chakma' => 'Scx/Cakm',
'scx=cham' => 'Scx/Cham',
'scx=cher' => '#/667',
'scx=cherokee' => '#/667',
'scx=chorasmian' => '#/668',
'scx=chrs' => '#/668',
'scx=common' => 'Scx/Zyyy',
'scx=copt' => 'Scx/Copt',
'scx=coptic' => 'Scx/Copt',
'scx=cpmn' => '#/669',
'scx=cprt' => 'Scx/Cprt',
'scx=cuneiform' => 'Scx/Xsux',
'scx=cypriot' => 'Scx/Cprt',
'scx=cyprominoan' => '#/669',
'scx=cyrillic' => 'Scx/Cyrl',
'scx=cyrl' => 'Scx/Cyrl',
'scx=deseret' => '#/190',
'scx=deva' => 'Scx/Deva',
'scx=devanagari' => 'Scx/Deva',
'scx=diak' => 'Scx/Diak',
'scx=divesakuru' => 'Scx/Diak',
'scx=dogr' => '#/670',
'scx=dogra' => '#/670',
'scx=dsrt' => '#/190',
'scx=dupl' => 'Scx/Dupl',
'scx=duployan' => 'Scx/Dupl',
'scx=egyp' => '#/671',
'scx=egyptianhieroglyphs' => '#/671',
'scx=elba' => '#/672',
'scx=elbasan' => '#/672',
'scx=elym' => '#/673',
'scx=elymaic' => '#/673',
'scx=ethi' => 'Scx/Ethi',
'scx=ethiopic' => 'Scx/Ethi',
'scx=geor' => 'Scx/Geor',
'scx=georgian' => 'Scx/Geor',
'scx=glag' => 'Scx/Glag',
'scx=glagolitic' => 'Scx/Glag',
'scx=gong' => 'Scx/Gong',
'scx=gonm' => 'Scx/Gonm',
'scx=goth' => '#/674',
'scx=gothic' => '#/674',
'scx=gran' => 'Scx/Gran',
'scx=grantha' => 'Scx/Gran',
'scx=greek' => 'Scx/Grek',
'scx=grek' => 'Scx/Grek',
'scx=gujarati' => 'Scx/Gujr',
'scx=gujr' => 'Scx/Gujr',
'scx=gunjalagondi' => 'Scx/Gong',
'scx=gurmukhi' => 'Scx/Guru',
'scx=guru' => 'Scx/Guru',
'scx=han' => 'Scx/Han',
'scx=hang' => 'Scx/Hang',
'scx=hangul' => 'Scx/Hang',
'scx=hani' => 'Scx/Han',
'scx=hanifirohingya' => 'Scx/Rohg',
'scx=hano' => '#/675',
'scx=hanunoo' => '#/675',
'scx=hatr' => '#/676',
'scx=hatran' => '#/676',
'scx=hebr' => 'Scx/Hebr',
'scx=hebrew' => 'Scx/Hebr',
'scx=hira' => 'Scx/Hira',
'scx=hiragana' => 'Scx/Hira',
'scx=hluw' => '#/677',
'scx=hmng' => 'Scx/Hmng',
'scx=hmnp' => 'Scx/Hmnp',
'scx=hung' => '#/678',
'scx=imperialaramaic' => '#/656',
'scx=inherited' => 'Scx/Zinh',
'scx=inscriptionalpahlavi' => '#/710',
'scx=inscriptionalparthian' => '#/712',
'scx=ital' => '#/679',
'scx=java' => '#/680',
'scx=javanese' => '#/680',
'scx=kaithi' => 'Scx/Kthi',
'scx=kali' => '#/197',
'scx=kana' => 'Scx/Kana',
'scx=kannada' => 'Scx/Knda',
'scx=katakana' => 'Scx/Kana',
'scx=kawi' => '#/681',
'scx=kayahli' => '#/197',
'scx=khar' => 'Scx/Khar',
'scx=kharoshthi' => 'Scx/Khar',
'scx=khitansmallscript' => '#/682',
'scx=khmer' => 'Scx/Khmr',
'scx=khmr' => 'Scx/Khmr',
'scx=khoj' => 'Scx/Khoj',
'scx=khojki' => 'Scx/Khoj',
'scx=khudawadi' => 'Scx/Sind',
'scx=kits' => '#/682',
'scx=knda' => 'Scx/Knda',
'scx=kthi' => 'Scx/Kthi',
'scx=lana' => 'Scx/Lana',
'scx=lao' => 'Scx/Lao',
'scx=laoo' => 'Scx/Lao',
'scx=latin' => 'Scx/Latn',
'scx=latn' => 'Scx/Latn',
'scx=lepc' => '#/683',
'scx=lepcha' => '#/683',
'scx=limb' => 'Scx/Limb',
'scx=limbu' => 'Scx/Limb',
'scx=lina' => 'Scx/Lina',
'scx=linb' => 'Scx/Linb',
'scx=lineara' => 'Scx/Lina',
'scx=linearb' => 'Scx/Linb',
'scx=lisu' => '#/684',
'scx=lyci' => '#/685',
'scx=lycian' => '#/685',
'scx=lydi' => '#/686',
'scx=lydian' => '#/686',
'scx=mahajani' => '#/687',
'scx=mahj' => '#/687',
'scx=maka' => '#/688',
'scx=makasar' => '#/688',
'scx=malayalam' => 'Scx/Mlym',
'scx=mand' => '#/689',
'scx=mandaic' => '#/689',
'scx=mani' => '#/690',
'scx=manichaean' => '#/690',
'scx=marc' => '#/691',
'scx=marchen' => '#/691',
'scx=masaramgondi' => 'Scx/Gonm',
'scx=medefaidrin' => '#/692',
'scx=medf' => '#/692',
'scx=meeteimayek' => '#/697',
'scx=mend' => '#/693',
'scx=mendekikakui' => '#/693',
'scx=merc' => '#/694',
'scx=mero' => '#/419',
'scx=meroiticcursive' => '#/694',
'scx=meroitichieroglyphs' => '#/419',
'scx=miao' => '#/695',
'scx=mlym' => 'Scx/Mlym',
'scx=modi' => '#/696',
'scx=mong' => 'Scx/Mong',
'scx=mongolian' => 'Scx/Mong',
'scx=mro' => '#/652',
'scx=mroo' => '#/652',
'scx=mtei' => '#/697',
'scx=mult' => 'Scx/Mult',
'scx=multani' => 'Scx/Mult',
'scx=myanmar' => 'Scx/Mymr',
'scx=mymr' => 'Scx/Mymr',
'scx=nabataean' => '#/699',
'scx=nagm' => '#/698',
'scx=nagmundari' => '#/698',
'scx=nand' => 'Scx/Nand',
'scx=nandinagari' => 'Scx/Nand',
'scx=narb' => '#/387',
'scx=nbat' => '#/699',
'scx=newa' => '#/700',
'scx=newtailue' => 'Scx/Talu',
'scx=nko' => 'Scx/Nko',
'scx=nkoo' => 'Scx/Nko',
'scx=nshu' => '#/701',
'scx=nushu' => '#/701',
'scx=nyiakengpuachuehmong' => 'Scx/Hmnp',
'scx=ogam' => '#/702',
'scx=ogham' => '#/702',
'scx=olchiki' => '#/206',
'scx=olck' => '#/206',
'scx=oldhungarian' => '#/678',
'scx=olditalic' => '#/679',
'scx=oldnortharabian' => '#/387',
'scx=oldpermic' => '#/708',
'scx=oldpersian' => '#/735',
'scx=oldsogdian' => '#/720',
'scx=oldsoutharabian' => '#/388',
'scx=oldturkic' => '#/703',
'scx=olduyghur' => '#/706',
'scx=oriya' => 'Scx/Orya',
'scx=orkh' => '#/703',
'scx=orya' => 'Scx/Orya',
'scx=osage' => '#/704',
'scx=osge' => '#/704',
'scx=osma' => '#/705',
'scx=osmanya' => '#/705',
'scx=ougr' => '#/706',
'scx=pahawhhmong' => 'Scx/Hmng',
'scx=palm' => '#/279',
'scx=palmyrene' => '#/279',
'scx=pauc' => '#/707',
'scx=paucinhau' => '#/707',
'scx=perm' => '#/708',
'scx=phag' => '#/709',
'scx=phagspa' => '#/709',
'scx=phli' => '#/710',
'scx=phlp' => 'Scx/Phlp',
'scx=phnx' => '#/711',
'scx=phoenician' => '#/711',
'scx=plrd' => '#/695',
'scx=prti' => '#/712',
'scx=psalterpahlavi' => 'Scx/Phlp',
'scx=qaac' => 'Scx/Copt',
'scx=qaai' => 'Scx/Zinh',
'scx=rejang' => '#/713',
'scx=rjng' => '#/713',
'scx=rohg' => 'Scx/Rohg',
'scx=runic' => '#/714',
'scx=runr' => '#/714',
'scx=samaritan' => '#/715',
'scx=samr' => '#/715',
'scx=sarb' => '#/388',
'scx=saur' => '#/716',
'scx=saurashtra' => '#/716',
'scx=sgnw' => '#/717',
'scx=sharada' => 'Scx/Shrd',
'scx=shavian' => '#/210',
'scx=shaw' => '#/210',
'scx=shrd' => 'Scx/Shrd',
'scx=sidd' => '#/718',
'scx=siddham' => '#/718',
'scx=signwriting' => '#/717',
'scx=sind' => 'Scx/Sind',
'scx=sinh' => 'Scx/Sinh',
'scx=sinhala' => 'Scx/Sinh',
'scx=sogd' => '#/719',
'scx=sogdian' => '#/719',
'scx=sogo' => '#/720',
'scx=sora' => '#/721',
'scx=sorasompeng' => '#/721',
'scx=soyo' => '#/722',
'scx=soyombo' => '#/722',
'scx=sund' => '#/723',
'scx=sundanese' => '#/723',
'scx=sylo' => '#/724',
'scx=sylotinagri' => '#/724',
'scx=syrc' => 'Scx/Syrc',
'scx=syriac' => 'Scx/Syrc',
'scx=tagalog' => '#/728',
'scx=tagb' => 'Scx/Tagb',
'scx=tagbanwa' => 'Scx/Tagb',
'scx=taile' => '#/725',
'scx=taitham' => 'Scx/Lana',
'scx=taiviet' => '#/726',
'scx=takr' => 'Scx/Takr',
'scx=takri' => 'Scx/Takr',
'scx=tale' => '#/725',
'scx=talu' => 'Scx/Talu',
'scx=tamil' => 'Scx/Taml',
'scx=taml' => 'Scx/Taml',
'scx=tang' => 'Scx/Tang',
'scx=tangsa' => '#/730',
'scx=tangut' => 'Scx/Tang',
'scx=tavt' => '#/726',
'scx=telu' => 'Scx/Telu',
'scx=telugu' => 'Scx/Telu',
'scx=tfng' => '#/727',
'scx=tglg' => '#/728',
'scx=thaa' => 'Scx/Thaa',
'scx=thaana' => 'Scx/Thaa',
'scx=thai' => '#/729',
'scx=tibetan' => 'Scx/Tibt',
'scx=tibt' => 'Scx/Tibt',
'scx=tifinagh' => '#/727',
'scx=tirh' => 'Scx/Tirh',
'scx=tirhuta' => 'Scx/Tirh',
'scx=tnsa' => '#/730',
'scx=toto' => '#/731',
'scx=ugar' => '#/732',
'scx=ugaritic' => '#/732',
'scx=unknown' => 'Scx/Zzzz',
'scx=vai' => '#/653',
'scx=vaii' => '#/653',
'scx=vith' => 'Scx/Vith',
'scx=vithkuqi' => 'Scx/Vith',
'scx=wancho' => '#/734',
'scx=wara' => '#/733',
'scx=warangciti' => '#/733',
'scx=wcho' => '#/734',
'scx=xpeo' => '#/735',
'scx=xsux' => 'Scx/Xsux',
'scx=yezi' => 'Scx/Yezi',
'scx=yezidi' => 'Scx/Yezi',
'scx=yi' => 'Scx/Yi',
'scx=yiii' => 'Scx/Yi',
'scx=zanabazarsquare' => '#/736',
'scx=zanb' => '#/736',
'scx=zinh' => 'Scx/Zinh',
'scx=zyyy' => 'Scx/Zyyy',
'scx=zzzz' => 'Scx/Zzzz',
'sd' => 'SD/Y',
'sd=f' => '!SD/Y',
'sd=false' => '!SD/Y',
'sd=n' => '!SD/Y',
'sd=no' => '!SD/Y',
'sd=t' => 'SD/Y',
'sd=true' => 'SD/Y',
'sd=y' => 'SD/Y',
'sd=yes' => 'SD/Y',
'sentenceterminal' => 'STerm/Y',
'separator' => 'Gc/Z',
'sgnw' => '#/717',
'sharada' => 'Scx/Shrd',
'shavian' => '#/210',
'shaw' => '#/210',
'shorthandformatcontrols' => '#/431',
'shrd' => 'Scx/Shrd',
'sidd' => '#/718',
'siddham' => '#/718',
'signwriting' => '#/717',
'sind' => 'Scx/Sind',
'sinh' => 'Scx/Sinh',
'sinhala' => 'Scx/Sinh',
'sinhalaarchaicnumbers' => '#/426',
'sk' => 'Gc/Sk',
'sm' => 'Gc/Sm',
'smallforms' => '#/306',
'smallformvariants' => '#/306',
'smallkanaext' => '#/355',
'smallkanaextension' => '#/355',
'so' => 'Gc/So',
'softdotted' => 'SD/Y',
'sogd' => '#/719',
'sogdian' => '#/719',
'sogo' => '#/720',
'sora' => '#/721',
'sorasompeng' => '#/721',
'soyo' => '#/722',
'soyombo' => '#/722',
'space' => 'Perl/SpacePer',
'spaceperl' => 'Perl/SpacePer',
'spaceseparator' => 'Gc/Zs',
'spacingmark' => 'Gc/Mc',
'spacingmodifierletters' => '#/386',
'specials' => '#/247',
'sterm' => 'STerm/Y',
'sterm=f' => '!STerm/Y',
'sterm=false' => '!STerm/Y',
'sterm=n' => '!STerm/Y',
'sterm=no' => '!STerm/Y',
'sterm=t' => 'STerm/Y',
'sterm=true' => 'STerm/Y',
'sterm=y' => 'STerm/Y',
'sterm=yes' => 'STerm/Y',
'sund' => '#/723',
'sundanese' => '#/723',
'sundanesesup' => '#/356',
'sundanesesupplement' => '#/356',
'suparrowsa' => '#/307',
'suparrowsb' => '#/308',
'suparrowsc' => '#/309',
'superandsub' => '#/334',
'superscriptsandsubscripts' => '#/334',
'supmathoperators' => '#/402',
'supplementalarrowsa' => '#/307',
'supplementalarrowsb' => '#/308',
'supplementalarrowsc' => '#/309',
'supplementalmathematicaloperators' => '#/402',
'supplementalpunctuation' => '#/377',
'supplementalsymbolsandpictographs' => '#/432',
'supplementaryprivateuseareaa' => '#/215',
'supplementaryprivateuseareab' => '#/216',
'suppuaa' => '#/215',
'suppuab' => '#/216',
'suppunctuation' => '#/377',
'supsymbolsandpictographs' => '#/432',
'surrogate' => '#/14',
'suttonsignwriting' => '#/409',
'sylo' => '#/724',
'sylotinagri' => '#/724',
'symbol' => 'Gc/S',
'symbolsandpictographsexta' => '#/433',
'symbolsandpictographsextendeda' => '#/433',
'symbolsforlegacycomputing' => '#/434',
'syrc' => 'Scx/Syrc',
'syriac' => 'Scx/Syrc',
'syriacsup' => '#/283',
'syriacsupplement' => '#/283',
'tagalog' => '#/728',
'tagb' => 'Scx/Tagb',
'tagbanwa' => 'Scx/Tagb',
'tags' => '#/127',
'taile' => '#/725',
'taitham' => 'Scx/Lana',
'taiviet' => '#/726',
'taixuanjing' => '#/336',
'taixuanjingsymbols' => '#/336',
'takr' => 'Scx/Takr',
'takri' => 'Scx/Takr',
'tale' => '#/725',
'talu' => 'Scx/Talu',
'tamil' => 'Scx/Taml',
'tamilsup' => '#/249',
'tamilsupplement' => '#/249',
'taml' => 'Scx/Taml',
'tang' => 'Scx/Tang',
'tangsa' => '#/730',
'tangut' => 'Scx/Tang',
'tangutcomponents' => '#/403',
'tangutsup' => '#/284',
'tangutsupplement' => '#/284',
'tavt' => '#/726',
'telu' => 'Scx/Telu',
'telugu' => 'Scx/Telu',
'term' => 'Term/Y',
'term=f' => '!Term/Y',
'term=false' => '!Term/Y',
'term=n' => '!Term/Y',
'term=no' => '!Term/Y',
'term=t' => 'Term/Y',
'term=true' => 'Term/Y',
'term=y' => 'Term/Y',
'term=yes' => 'Term/Y',
'terminalpunctuation' => 'Term/Y',
'tfng' => '#/727',
'tglg' => '#/728',
'thaa' => 'Scx/Thaa',
'thaana' => 'Scx/Thaa',
'thai' => '#/729',
'tibetan' => 'Scx/Tibt',
'tibt' => 'Scx/Tibt',
'tifinagh' => '#/727',
'tirh' => 'Scx/Tirh',
'tirhuta' => 'Scx/Tirh',
'title' => 'Perl/Title',
'titlecase' => 'Perl/Title',
'titlecaseletter' => 'Perl/Title',
'tnsa' => '#/730',
'toto' => '#/731',
'transportandmap' => '#/389',
'transportandmapsymbols' => '#/389',
'ucas' => '#/130',
'ucasext' => '#/222',
'ucasexta' => '#/251',
'ugar' => '#/732',
'ugaritic' => '#/732',
'uideo' => 'UIdeo/Y',
'uideo=f' => '!UIdeo/Y',
'uideo=false' => '!UIdeo/Y',
'uideo=n' => '!UIdeo/Y',
'uideo=no' => '!UIdeo/Y',
'uideo=t' => 'UIdeo/Y',
'uideo=true' => 'UIdeo/Y',
'uideo=y' => 'UIdeo/Y',
'uideo=yes' => 'UIdeo/Y',
'unassigned' => 'Gc/Cn',
'unicode' => '#/2',
'unifiedcanadianaboriginalsyllabics' => '#/130',
'unifiedcanadianaboriginalsyllabicsextended' => '#/222',
'unifiedcanadianaboriginalsyllabicsextendeda' => '#/251',
'unifiedideograph' => 'UIdeo/Y',
'unknown' => 'Scx/Zzzz',
'upper' => 'Upper/Y',
'upper=f' => '!Upper/Y',
'upper=false' => '!Upper/Y',
'upper=n' => '!Upper/Y',
'upper=no' => '!Upper/Y',
'upper=t' => 'Upper/Y',
'upper=true' => 'Upper/Y',
'upper=y' => 'Upper/Y',
'upper=yes' => 'Upper/Y',
'uppercase' => 'Upper/Y',
'uppercaseletter' => 'Gc/Lu',
'vai' => '#/653',
'vaii' => '#/653',
'variationselector' => 'VS/Y',
'variationselectors' => '#/109',
'variationselectorssupplement' => '#/149',
'vedicext' => '#/253',
'vedicextensions' => '#/253',
'verticalforms' => '#/365',
'vertspace' => '#/4',
'vith' => 'Scx/Vith',
'vithkuqi' => 'Scx/Vith',
'vo=r' => 'Vo/R',
'vo=rotated' => 'Vo/R',
'vo=tr' => 'Vo/Tr',
'vo=transformedrotated' => 'Vo/Tr',
'vo=transformedupright' => 'Vo/Tu',
'vo=tu' => 'Vo/Tu',
'vo=u' => 'Vo/U',
'vo=upright' => 'Vo/U',
'vs' => 'VS/Y',
'vs=f' => '!VS/Y',
'vs=false' => '!VS/Y',
'vs=n' => '!VS/Y',
'vs=no' => '!VS/Y',
'vs=t' => 'VS/Y',
'vs=true' => 'VS/Y',
'vs=y' => 'VS/Y',
'vs=yes' => 'VS/Y',
'vssup' => '#/149',
'wancho' => '#/734',
'wara' => '#/733',
'warangciti' => '#/733',
'wb=aletter' => 'WB/LE',
'wb=cr' => '#/66',
'wb=doublequote' => '#/92',
'wb=dq' => '#/92',
'wb=eb' => '#/0',
'wb=ebase' => '#/0',
'wb=ebasegaz' => '#/0',
'wb=ebg' => '#/0',
'wb=em' => '#/0',
'wb=emodifier' => '#/0',
'wb=ex' => 'WB/EX',
'wb=extend' => 'WB/Extend',
'wb=extendnumlet' => 'WB/EX',
'wb=fo' => 'WB/FO',
'wb=format' => 'WB/FO',
'wb=gaz' => '#/0',
'wb=glueafterzwj' => '#/0',
'wb=hebrewletter' => 'WB/HL',
'wb=hl' => 'WB/HL',
'wb=ka' => 'WB/KA',
'wb=katakana' => 'WB/KA',
'wb=le' => 'WB/LE',
'wb=lf' => '#/67',
'wb=mb' => 'WB/MB',
'wb=midletter' => 'WB/ML',
'wb=midnum' => 'WB/MN',
'wb=midnumlet' => 'WB/MB',
'wb=ml' => 'WB/ML',
'wb=mn' => 'WB/MN',
'wb=newline' => '#/93',
'wb=nl' => '#/93',
'wb=nu' => 'WB/NU',
'wb=numeric' => 'WB/NU',
'wb=other' => 'WB/XX',
'wb=regionalindicator' => '#/68',
'wb=ri' => '#/68',
'wb=singlequote' => '#/94',
'wb=sq' => '#/94',
'wb=wsegspace' => 'WB/WSegSpac',
'wb=xx' => 'WB/XX',
'wb=zwj' => '#/69',
'wcho' => '#/734',
'whitespace' => 'Perl/SpacePer',
'word' => 'Perl/Word',
'wspace' => 'Perl/SpacePer',
'wspace=f' => '!Perl/SpacePer',
'wspace=false' => '!Perl/SpacePer',
'wspace=n' => '!Perl/SpacePer',
'wspace=no' => '!Perl/SpacePer',
'wspace=t' => 'Perl/SpacePer',
'wspace=true' => 'Perl/SpacePer',
'wspace=y' => 'Perl/SpacePer',
'wspace=yes' => 'Perl/SpacePer',
'xdigit' => 'Hex/Y',
'xidc' => 'XIDC/Y',
'xidc=f' => '!XIDC/Y',
'xidc=false' => '!XIDC/Y',
'xidc=n' => '!XIDC/Y',
'xidc=no' => '!XIDC/Y',
'xidc=t' => 'XIDC/Y',
'xidc=true' => 'XIDC/Y',
'xidc=y' => 'XIDC/Y',
'xidc=yes' => 'XIDC/Y',
'xidcontinue' => 'XIDC/Y',
'xids' => 'XIDS/Y',
'xids=f' => '!XIDS/Y',
'xids=false' => '!XIDS/Y',
'xids=n' => '!XIDS/Y',
'xids=no' => '!XIDS/Y',
'xids=t' => 'XIDS/Y',
'xids=true' => 'XIDS/Y',
'xids=y' => 'XIDS/Y',
'xids=yes' => 'XIDS/Y',
'xidstart' => 'XIDS/Y',
'xpeo' => '#/735',
'xperlspace' => 'Perl/SpacePer',
'xposixalnum' => 'Perl/Alnum',
'xposixalpha' => 'Alpha/Y',
'xposixblank' => 'Perl/Blank',
'xposixcntrl' => '#/439',
'xposixdigit' => 'Gc/Nd',
'xposixgraph' => 'Perl/Graph',
'xposixlower' => 'Lower/Y',
'xposixprint' => 'Perl/Print',
'xposixpunct' => 'Perl/XPosixPu',
'xposixspace' => 'Perl/SpacePer',
'xposixupper' => 'Upper/Y',
'xposixword' => 'Perl/Word',
'xposixxdigit' => 'Hex/Y',
'xsux' => 'Scx/Xsux',
'yezi' => 'Scx/Yezi',
'yezidi' => 'Scx/Yezi',
'yi' => 'Scx/Yi',
'yiii' => 'Scx/Yi',
'yijing' => '#/178',
'yijinghexagramsymbols' => '#/178',
'yiradicals' => '#/311',
'yisyllables' => '#/337',
'z' => 'Gc/Z',
'zanabazarsquare' => '#/736',
'zanb' => '#/736',
'zinh' => 'Scx/Zinh',
'zl' => '#/441',
'znamennymusic' => '#/366',
'znamennymusicalnotation' => '#/366',
'zp' => '#/442',
'zs' => 'Gc/Zs',
'zyyy' => 'Scx/Zyyy',
'zzzz' => 'Scx/Zzzz',
);

# Maps floating point to fractional form
%Unicode::UCD::nv_floating_to_rational = (
'-5.000e-01' => '-1/2',
'1.000e-01' => '1/10',
'1.111e-01' => '1/9',
'1.250e-01' => '1/8',
'1.250e-02' => '1/80',
'1.429e-01' => '1/7',
'1.500e+00' => '3/2',
'1.500e-01' => '3/20',
'1.562e-02' => '1/64',
'1.563e-02' => '1/64',
'1.667e-01' => '1/6',
'1.875e-01' => '3/16',
'2.000e-01' => '1/5',
'2.500e+00' => '5/2',
'2.500e-01' => '1/4',
'2.500e-02' => '1/40',
'3.125e-02' => '1/32',
'3.125e-03' => '1/320',
'3.333e-01' => '1/3',
'3.500e+00' => '7/2',
'3.750e-01' => '3/8',
'3.750e-02' => '3/80',
'4.000e-01' => '2/5',
'4.167e-01' => '5/12',
'4.500e+00' => '9/2',
'4.688e-02' => '3/64',
'5.000e-01' => '1/2',
'5.000e-02' => '1/20',
'5.500e+00' => '11/2',
'5.833e-01' => '7/12',
'6.000e-01' => '3/5',
'6.250e-01' => '5/8',
'6.250e-02' => '1/16',
'6.250e-03' => '1/160',
'6.500e+00' => '13/2',
'6.667e-01' => '2/3',
'7.500e+00' => '15/2',
'7.500e-01' => '3/4',
'8.000e-01' => '4/5',
'8.333e-01' => '5/6',
'8.333e-02' => '1/12',
'8.500e+00' => '17/2',
'8.750e-01' => '7/8',
'9.167e-01' => '11/12',
);

# If a %e floating point number doesn't have this number of digits in it after
# the decimal point to get this close to a fraction, it isn't considered to be
# that fraction even if all the digits it does have match.
$Unicode::UCD::e_precision = 3;

# Deprecated tables to generate a warning for.  The key is the file containing
# the table, so as to avoid duplication, as many property names can map to the
# file, but we only need one entry for all of them.
%Unicode::UCD::why_deprecated = (
'#/533' => 'Surrogates should never appear in well-formed text, and therefore shouldn\'t be the basis for line breaking',
'Hyphen/T' => 'Supplanted by Line_Break property values; see www.unicode.org/reports/tr14',
);

# A few properties have different behavior under /i matching.  This maps
# those to substitute files to use under /i.
%Unicode::UCD::caseless_equivalent = (
'gc=ll' => 'Gc/LC',
'gc=lowercaseletter' => 'Gc/LC',
'gc=lt' => 'Gc/LC',
'gc=lu' => 'Gc/LC',
'gc=titlecaseletter' => 'Gc/LC',
'gc=uppercaseletter' => 'Gc/LC',
'isll' => 'Gc/LC',
'islower' => 'Cased/Y',
'islowercase' => 'Cased/Y',
'islowercaseletter' => 'Gc/LC',
'islt' => 'Gc/LC',
'islu' => 'Gc/LC',
'isposixlower' => '#/6',
'isposixupper' => '#/6',
'istitle' => 'Cased/Y',
'istitlecase' => 'Cased/Y',
'istitlecaseletter' => 'Gc/LC',
'isupper' => 'Cased/Y',
'isuppercase' => 'Cased/Y',
'isuppercaseletter' => 'Gc/LC',
'isxposixlower' => 'Cased/Y',
'isxposixupper' => 'Cased/Y',
'll' => 'Gc/LC',
'lower' => 'Cased/Y',
'lower=f' => '!Cased/Y',
'lower=false' => '!Cased/Y',
'lower=n' => '!Cased/Y',
'lower=no' => '!Cased/Y',
'lower=t' => 'Cased/Y',
'lower=true' => 'Cased/Y',
'lower=y' => 'Cased/Y',
'lower=yes' => 'Cased/Y',
'lowercase' => 'Cased/Y',
'lowercaseletter' => 'Gc/LC',
'lt' => 'Gc/LC',
'lu' => 'Gc/LC',
'posixlower' => '#/6',
'posixupper' => '#/6',
'title' => 'Cased/Y',
'titlecase' => 'Cased/Y',
'titlecaseletter' => 'Gc/LC',
'upper' => 'Cased/Y',
'upper=f' => '!Cased/Y',
'upper=false' => '!Cased/Y',
'upper=n' => '!Cased/Y',
'upper=no' => '!Cased/Y',
'upper=t' => 'Cased/Y',
'upper=true' => 'Cased/Y',
'upper=y' => 'Cased/Y',
'upper=yes' => 'Cased/Y',
'uppercase' => 'Cased/Y',
'uppercaseletter' => 'Gc/LC',
'xposixlower' => 'Cased/Y',
'xposixupper' => 'Cased/Y',
);

# Property names to mapping files
%Unicode::UCD::loose_property_to_file_of = (
'age' => 'To/Age',
'bc' => 'To/Bc',
'bidiclass' => 'To/Bc',
'bidimirroringglyph' => 'To/Bmg',
'bidipairedbracket' => 'To/Bpb',
'bidipairedbrackettype' => 'To/Bpt',
'bmg' => 'To/Bmg',
'bpb' => 'To/Bpb',
'bpt' => 'To/Bpt',
'canonicalcombiningclass' => 'CombiningClass',
'casefolding' => 'To/Cf',
'category' => 'To/Gc',
'ccc' => 'CombiningClass',
'cf' => 'To/Cf',
'ea' => 'To/Ea',
'eastasianwidth' => 'To/Ea',
'equideo' => 'To/EqUIdeo',
'equivalentunifiedideograph' => 'To/EqUIdeo',
'gc' => 'To/Gc',
'gcb' => 'To/GCB',
'generalcategory' => 'To/Gc',
'graphemeclusterbreak' => 'To/GCB',
'hangulsyllabletype' => 'To/Hst',
'hst' => 'To/Hst',
'identifierstatus' => 'To/Identifi',
'identifiertype' => 'To/Identif2',
'indicpositionalcategory' => 'To/InPC',
'indicsyllabiccategory' => 'To/InSC',
'inpc' => 'To/InPC',
'insc' => 'To/InSC',
'isc' => 'To/Isc',
'isocomment' => 'To/Isc',
'jg' => 'To/Jg',
'joininggroup' => 'To/Jg',
'joiningtype' => 'To/Jt',
'jt' => 'To/Jt',
'lb' => 'To/Lb',
'lc' => 'To/Lc',
'linebreak' => 'To/Lb',
'lowercasemapping' => 'To/Lc',
'na1' => 'To/Na1',
'namealias' => 'To/NameAlia',
'nfcqc' => 'To/NFCQC',
'nfcquickcheck' => 'To/NFCQC',
'nfdqc' => 'To/NFDQC',
'nfdquickcheck' => 'To/NFDQC',
'nfkccasefold' => 'To/NFKCCF',
'nfkccf' => 'To/NFKCCF',
'nfkcqc' => 'To/NFKCQC',
'nfkcquickcheck' => 'To/NFKCQC',
'nfkdqc' => 'To/NFKDQC',
'nfkdquickcheck' => 'To/NFKDQC',
'nt' => 'To/Nt',
'numerictype' => 'To/Nt',
'numericvalue' => 'To/Nv',
'nv' => 'To/Nv',
'perldecimaldigit' => 'To/PerlDeci',
'sb' => 'To/SB',
'sc' => 'To/Sc',
'script' => 'To/Sc',
'scriptextensions' => 'To/Scx',
'scx' => 'To/Scx',
'sentencebreak' => 'To/SB',
'tc' => 'To/Tc',
'titlecasemapping' => 'To/Tc',
'uc' => 'To/Uc',
'unicode1name' => 'To/Na1',
'uppercasemapping' => 'To/Uc',
'verticalorientation' => 'To/Vo',
'vo' => 'To/Vo',
'wb' => 'To/WB',
'wordbreak' => 'To/WB',
);

# Property names to mapping files
%Unicode::UCD::strict_property_to_file_of = (
'_perl_gcb' => 'To/GCB',
'_perl_lb' => 'To/_PerlLB',
'_perl_name_alias' => 'To/NameAlia',
'_perl_sb' => 'To/SB',
'_perl_scx' => 'To/_PerlSCX',
'_perl_wb' => 'To/WB',
);

# Files to the swash names within them.
%Unicode::UCD::file_to_swash_name = (
'CombiningClass' => 'ToCombiningClass',
'To/_PerlLB' => 'To_PerlLB',
'To/_PerlSCX' => 'To_PerlSCX',
'To/Age' => 'ToAge',
'To/Bc' => 'ToBc',
'To/Bmg' => 'ToBmg',
'To/Bpb' => 'ToBpb',
'To/Bpt' => 'ToBpt',
'To/Cf' => 'ToCf',
'To/Ea' => 'ToEa',
'To/EqUIdeo' => 'ToEqUIdeo',
'To/Gc' => 'ToGc',
'To/GCB' => 'ToGCB',
'To/Hst' => 'ToHst',
'To/Identif2' => 'ToIdentifierType',
'To/Identifi' => 'ToIdentifierStatus',
'To/InPC' => 'ToInPC',
'To/InSC' => 'ToInSC',
'To/Isc' => 'ToIsc',
'To/Jg' => 'ToJg',
'To/Jt' => 'ToJt',
'To/Lb' => 'ToLb',
'To/Lc' => 'ToLc',
'To/Na1' => 'ToNa1',
'To/NameAlia' => 'ToNameAlias',
'To/NFCQC' => 'ToNFCQC',
'To/NFDQC' => 'ToNFDQC',
'To/NFKCCF' => 'ToNFKCCF',
'To/NFKCQC' => 'ToNFKCQC',
'To/NFKDQC' => 'ToNFKDQC',
'To/Nt' => 'ToNt',
'To/Nv' => 'ToNv',
'To/PerlDeci' => 'ToPerlDecimalDigit',
'To/SB' => 'ToSB',
'To/Sc' => 'ToSc',
'To/Scx' => 'ToScx',
'To/Tc' => 'ToTc',
'To/Uc' => 'ToUc',
'To/Vo' => 'ToVo',
'To/WB' => 'ToWB',
);

# Keys are all the possible "prop=value" combinations, in loose form; values
# are the standard loose name for the 'value' part of the key
%Unicode::UCD::loose_to_standard_value = (
'age=1.1' => '1.1',
'age=10.0' => '10.0',
'age=11.0' => '11.0',
'age=12.0' => '12.0',
'age=12.1' => '12.1',
'age=13.0' => '13.0',
'age=14.0' => '14.0',
'age=15.0' => '15.0',
'age=2.0' => '2.0',
'age=2.1' => '2.1',
'age=3.0' => '3.0',
'age=3.1' => '3.1',
'age=3.2' => '3.2',
'age=4.0' => '4.0',
'age=4.1' => '4.1',
'age=5.0' => '5.0',
'age=5.1' => '5.1',
'age=5.2' => '5.2',
'age=6.0' => '6.0',
'age=6.1' => '6.1',
'age=6.2' => '6.2',
'age=6.3' => '6.3',
'age=7.0' => '7.0',
'age=8.0' => '8.0',
'age=9.0' => '9.0',
'age=na' => 'na',
'age=unassigned' => 'na',
'age=v100' => '10.0',
'age=v11' => '1.1',
'age=v110' => '11.0',
'age=v120' => '12.0',
'age=v121' => '12.1',
'age=v130' => '13.0',
'age=v140' => '14.0',
'age=v150' => '15.0',
'age=v20' => '2.0',
'age=v21' => '2.1',
'age=v30' => '3.0',
'age=v31' => '3.1',
'age=v32' => '3.2',
'age=v40' => '4.0',
'age=v41' => '4.1',
'age=v50' => '5.0',
'age=v51' => '5.1',
'age=v52' => '5.2',
'age=v60' => '6.0',
'age=v61' => '6.1',
'age=v62' => '6.2',
'age=v63' => '6.3',
'age=v70' => '7.0',
'age=v80' => '8.0',
'age=v90' => '9.0',
'ahex=f' => 'n',
'ahex=false' => 'n',
'ahex=n' => 'n',
'ahex=no' => 'n',
'ahex=t' => 'y',
'ahex=true' => 'y',
'ahex=y' => 'y',
'ahex=yes' => 'y',
'alpha=f' => 'n',
'alpha=false' => 'n',
'alpha=n' => 'n',
'alpha=no' => 'n',
'alpha=t' => 'y',
'alpha=true' => 'y',
'alpha=y' => 'y',
'alpha=yes' => 'y',
'bc=al' => 'al',
'bc=an' => 'an',
'bc=arabicletter' => 'al',
'bc=arabicnumber' => 'an',
'bc=b' => 'b',
'bc=bn' => 'bn',
'bc=boundaryneutral' => 'bn',
'bc=commonseparator' => 'cs',
'bc=cs' => 'cs',
'bc=en' => 'en',
'bc=es' => 'es',
'bc=et' => 'et',
'bc=europeannumber' => 'en',
'bc=europeanseparator' => 'es',
'bc=europeanterminator' => 'et',
'bc=firststrongisolate' => 'fsi',
'bc=fsi' => 'fsi',
'bc=l' => 'l',
'bc=lefttoright' => 'l',
'bc=lefttorightembedding' => 'lre',
'bc=lefttorightisolate' => 'lri',
'bc=lefttorightoverride' => 'lro',
'bc=lre' => 'lre',
'bc=lri' => 'lri',
'bc=lro' => 'lro',
'bc=nonspacingmark' => 'nsm',
'bc=nsm' => 'nsm',
'bc=on' => 'on',
'bc=otherneutral' => 'on',
'bc=paragraphseparator' => 'b',
'bc=pdf' => 'pdf',
'bc=pdi' => 'pdi',
'bc=popdirectionalformat' => 'pdf',
'bc=popdirectionalisolate' => 'pdi',
'bc=r' => 'r',
'bc=righttoleft' => 'r',
'bc=righttoleftembedding' => 'rle',
'bc=righttoleftisolate' => 'rli',
'bc=righttoleftoverride' => 'rlo',
'bc=rle' => 'rle',
'bc=rli' => 'rli',
'bc=rlo' => 'rlo',
'bc=s' => 's',
'bc=segmentseparator' => 's',
'bc=whitespace' => 'ws',
'bc=ws' => 'ws',
'bidic=f' => 'n',
'bidic=false' => 'n',
'bidic=n' => 'n',
'bidic=no' => 'n',
'bidic=t' => 'y',
'bidic=true' => 'y',
'bidic=y' => 'y',
'bidic=yes' => 'y',
'bidim=f' => 'n',
'bidim=false' => 'n',
'bidim=n' => 'n',
'bidim=no' => 'n',
'bidim=t' => 'y',
'bidim=true' => 'y',
'bidim=y' => 'y',
'bidim=yes' => 'y',
'blk=adlam' => 'adlam',
'blk=aegeannumbers' => 'aegeannumbers',
'blk=ahom' => 'ahom',
'blk=alchemical' => 'alchemical',
'blk=alchemicalsymbols' => 'alchemical',
'blk=alphabeticpf' => 'alphabeticpf',
'blk=alphabeticpresentationforms' => 'alphabeticpf',
'blk=anatolianhieroglyphs' => 'anatolianhieroglyphs',
'blk=ancientgreekmusic' => 'ancientgreekmusic',
'blk=ancientgreekmusicalnotation' => 'ancientgreekmusic',
'blk=ancientgreeknumbers' => 'ancientgreeknumbers',
'blk=ancientsymbols' => 'ancientsymbols',
'blk=arabic' => 'arabic',
'blk=arabicexta' => 'arabicexta',
'blk=arabicextb' => 'arabicextb',
'blk=arabicextc' => 'arabicextc',
'blk=arabicextendeda' => 'arabicexta',
'blk=arabicextendedb' => 'arabicextb',
'blk=arabicextendedc' => 'arabicextc',
'blk=arabicmath' => 'arabicmath',
'blk=arabicmathematicalalphabeticsymbols' => 'arabicmath',
'blk=arabicpfa' => 'arabicpfa',
'blk=arabicpfb' => 'arabicpfb',
'blk=arabicpresentationformsa' => 'arabicpfa',
'blk=arabicpresentationformsb' => 'arabicpfb',
'blk=arabicsup' => 'arabicsup',
'blk=arabicsupplement' => 'arabicsup',
'blk=armenian' => 'armenian',
'blk=arrows' => 'arrows',
'blk=ascii' => 'ascii',
'blk=avestan' => 'avestan',
'blk=balinese' => 'balinese',
'blk=bamum' => 'bamum',
'blk=bamumsup' => 'bamumsup',
'blk=bamumsupplement' => 'bamumsup',
'blk=basiclatin' => 'ascii',
'blk=bassavah' => 'bassavah',
'blk=batak' => 'batak',
'blk=bengali' => 'bengali',
'blk=bhaiksuki' => 'bhaiksuki',
'blk=blockelements' => 'blockelements',
'blk=bopomofo' => 'bopomofo',
'blk=bopomofoext' => 'bopomofoext',
'blk=bopomofoextended' => 'bopomofoext',
'blk=boxdrawing' => 'boxdrawing',
'blk=brahmi' => 'brahmi',
'blk=braille' => 'braille',
'blk=braillepatterns' => 'braille',
'blk=buginese' => 'buginese',
'blk=buhid' => 'buhid',
'blk=byzantinemusic' => 'byzantinemusic',
'blk=byzantinemusicalsymbols' => 'byzantinemusic',
'blk=canadiansyllabics' => 'ucas',
'blk=carian' => 'carian',
'blk=caucasianalbanian' => 'caucasianalbanian',
'blk=chakma' => 'chakma',
'blk=cham' => 'cham',
'blk=cherokee' => 'cherokee',
'blk=cherokeesup' => 'cherokeesup',
'blk=cherokeesupplement' => 'cherokeesup',
'blk=chesssymbols' => 'chesssymbols',
'blk=chorasmian' => 'chorasmian',
'blk=cjk' => 'cjk',
'blk=cjkcompat' => 'cjkcompat',
'blk=cjkcompatforms' => 'cjkcompatforms',
'blk=cjkcompatibility' => 'cjkcompat',
'blk=cjkcompatibilityforms' => 'cjkcompatforms',
'blk=cjkcompatibilityideographs' => 'cjkcompatideographs',
'blk=cjkcompatibilityideographssupplement' => 'cjkcompatideographssup',
'blk=cjkcompatideographs' => 'cjkcompatideographs',
'blk=cjkcompatideographssup' => 'cjkcompatideographssup',
'blk=cjkexta' => 'cjkexta',
'blk=cjkextb' => 'cjkextb',
'blk=cjkextc' => 'cjkextc',
'blk=cjkextd' => 'cjkextd',
'blk=cjkexte' => 'cjkexte',
'blk=cjkextf' => 'cjkextf',
'blk=cjkextg' => 'cjkextg',
'blk=cjkexth' => 'cjkexth',
'blk=cjkradicalssup' => 'cjkradicalssup',
'blk=cjkradicalssupplement' => 'cjkradicalssup',
'blk=cjkstrokes' => 'cjkstrokes',
'blk=cjksymbols' => 'cjksymbols',
'blk=cjksymbolsandpunctuation' => 'cjksymbols',
'blk=cjkunifiedideographs' => 'cjk',
'blk=cjkunifiedideographsextensiona' => 'cjkexta',
'blk=cjkunifiedideographsextensionb' => 'cjkextb',
'blk=cjkunifiedideographsextensionc' => 'cjkextc',
'blk=cjkunifiedideographsextensiond' => 'cjkextd',
'blk=cjkunifiedideographsextensione' => 'cjkexte',
'blk=cjkunifiedideographsextensionf' => 'cjkextf',
'blk=cjkunifiedideographsextensiong' => 'cjkextg',
'blk=cjkunifiedideographsextensionh' => 'cjkexth',
'blk=combiningdiacriticalmarks' => 'diacriticals',
'blk=combiningdiacriticalmarksextended' => 'diacriticalsext',
'blk=combiningdiacriticalmarksforsymbols' => 'diacriticalsforsymbols',
'blk=combiningdiacriticalmarkssupplement' => 'diacriticalssup',
'blk=combininghalfmarks' => 'halfmarks',
'blk=combiningmarksforsymbols' => 'diacriticalsforsymbols',
'blk=commonindicnumberforms' => 'indicnumberforms',
'blk=compatjamo' => 'compatjamo',
'blk=controlpictures' => 'controlpictures',
'blk=coptic' => 'coptic',
'blk=copticepactnumbers' => 'copticepactnumbers',
'blk=countingrod' => 'countingrod',
'blk=countingrodnumerals' => 'countingrod',
'blk=cuneiform' => 'cuneiform',
'blk=cuneiformnumbers' => 'cuneiformnumbers',
'blk=cuneiformnumbersandpunctuation' => 'cuneiformnumbers',
'blk=currencysymbols' => 'currencysymbols',
'blk=cypriotsyllabary' => 'cypriotsyllabary',
'blk=cyprominoan' => 'cyprominoan',
'blk=cyrillic' => 'cyrillic',
'blk=cyrillicexta' => 'cyrillicexta',
'blk=cyrillicextb' => 'cyrillicextb',
'blk=cyrillicextc' => 'cyrillicextc',
'blk=cyrillicextd' => 'cyrillicextd',
'blk=cyrillicextendeda' => 'cyrillicexta',
'blk=cyrillicextendedb' => 'cyrillicextb',
'blk=cyrillicextendedc' => 'cyrillicextc',
'blk=cyrillicextendedd' => 'cyrillicextd',
'blk=cyrillicsup' => 'cyrillicsup',
'blk=cyrillicsupplement' => 'cyrillicsup',
'blk=cyrillicsupplementary' => 'cyrillicsup',
'blk=deseret' => 'deseret',
'blk=devanagari' => 'devanagari',
'blk=devanagariext' => 'devanagariext',
'blk=devanagariexta' => 'devanagariexta',
'blk=devanagariextended' => 'devanagariext',
'blk=devanagariextendeda' => 'devanagariexta',
'blk=diacriticals' => 'diacriticals',
'blk=diacriticalsext' => 'diacriticalsext',
'blk=diacriticalsforsymbols' => 'diacriticalsforsymbols',
'blk=diacriticalssup' => 'diacriticalssup',
'blk=dingbats' => 'dingbats',
'blk=divesakuru' => 'divesakuru',
'blk=dogra' => 'dogra',
'blk=domino' => 'domino',
'blk=dominotiles' => 'domino',
'blk=duployan' => 'duployan',
'blk=earlydynasticcuneiform' => 'earlydynasticcuneiform',
'blk=egyptianhieroglyphformatcontrols' => 'egyptianhieroglyphformatcontrols',
'blk=egyptianhieroglyphs' => 'egyptianhieroglyphs',
'blk=elbasan' => 'elbasan',
'blk=elymaic' => 'elymaic',
'blk=emoticons' => 'emoticons',
'blk=enclosedalphanum' => 'enclosedalphanum',
'blk=enclosedalphanumerics' => 'enclosedalphanum',
'blk=enclosedalphanumericsupplement' => 'enclosedalphanumsup',
'blk=enclosedalphanumsup' => 'enclosedalphanumsup',
'blk=enclosedcjk' => 'enclosedcjk',
'blk=enclosedcjklettersandmonths' => 'enclosedcjk',
'blk=enclosedideographicsup' => 'enclosedideographicsup',
'blk=enclosedideographicsupplement' => 'enclosedideographicsup',
'blk=ethiopic' => 'ethiopic',
'blk=ethiopicext' => 'ethiopicext',
'blk=ethiopicexta' => 'ethiopicexta',
'blk=ethiopicextb' => 'ethiopicextb',
'blk=ethiopicextended' => 'ethiopicext',
'blk=ethiopicextendeda' => 'ethiopicexta',
'blk=ethiopicextendedb' => 'ethiopicextb',
'blk=ethiopicsup' => 'ethiopicsup',
'blk=ethiopicsupplement' => 'ethiopicsup',
'blk=generalpunctuation' => 'punctuation',
'blk=geometricshapes' => 'geometricshapes',
'blk=geometricshapesext' => 'geometricshapesext',
'blk=geometricshapesextended' => 'geometricshapesext',
'blk=georgian' => 'georgian',
'blk=georgianext' => 'georgianext',
'blk=georgianextended' => 'georgianext',
'blk=georgiansup' => 'georgiansup',
'blk=georgiansupplement' => 'georgiansup',
'blk=glagolitic' => 'glagolitic',
'blk=glagoliticsup' => 'glagoliticsup',
'blk=glagoliticsupplement' => 'glagoliticsup',
'blk=gothic' => 'gothic',
'blk=grantha' => 'grantha',
'blk=greek' => 'greek',
'blk=greekandcoptic' => 'greek',
'blk=greekext' => 'greekext',
'blk=greekextended' => 'greekext',
'blk=gujarati' => 'gujarati',
'blk=gunjalagondi' => 'gunjalagondi',
'blk=gurmukhi' => 'gurmukhi',
'blk=halfandfullforms' => 'halfandfullforms',
'blk=halfmarks' => 'halfmarks',
'blk=halfwidthandfullwidthforms' => 'halfandfullforms',
'blk=hangul' => 'hangul',
'blk=hangulcompatibilityjamo' => 'compatjamo',
'blk=hanguljamo' => 'jamo',
'blk=hanguljamoextendeda' => 'jamoexta',
'blk=hanguljamoextendedb' => 'jamoextb',
'blk=hangulsyllables' => 'hangul',
'blk=hanifirohingya' => 'hanifirohingya',
'blk=hanunoo' => 'hanunoo',
'blk=hatran' => 'hatran',
'blk=hebrew' => 'hebrew',
'blk=highprivateusesurrogates' => 'highpusurrogates',
'blk=highpusurrogates' => 'highpusurrogates',
'blk=highsurrogates' => 'highsurrogates',
'blk=hiragana' => 'hiragana',
'blk=idc' => 'idc',
'blk=ideographicdescriptioncharacters' => 'idc',
'blk=ideographicsymbols' => 'ideographicsymbols',
'blk=ideographicsymbolsandpunctuation' => 'ideographicsymbols',
'blk=imperialaramaic' => 'imperialaramaic',
'blk=indicnumberforms' => 'indicnumberforms',
'blk=indicsiyaqnumbers' => 'indicsiyaqnumbers',
'blk=inscriptionalpahlavi' => 'inscriptionalpahlavi',
'blk=inscriptionalparthian' => 'inscriptionalparthian',
'blk=ipaext' => 'ipaext',
'blk=ipaextensions' => 'ipaext',
'blk=jamo' => 'jamo',
'blk=jamoexta' => 'jamoexta',
'blk=jamoextb' => 'jamoextb',
'blk=javanese' => 'javanese',
'blk=kaithi' => 'kaithi',
'blk=kaktoviknumerals' => 'kaktoviknumerals',
'blk=kanaexta' => 'kanaexta',
'blk=kanaextb' => 'kanaextb',
'blk=kanaextendeda' => 'kanaexta',
'blk=kanaextendedb' => 'kanaextb',
'blk=kanasup' => 'kanasup',
'blk=kanasupplement' => 'kanasup',
'blk=kanbun' => 'kanbun',
'blk=kangxi' => 'kangxi',
'blk=kangxiradicals' => 'kangxi',
'blk=kannada' => 'kannada',
'blk=katakana' => 'katakana',
'blk=katakanaext' => 'katakanaext',
'blk=katakanaphoneticextensions' => 'katakanaext',
'blk=kawi' => 'kawi',
'blk=kayahli' => 'kayahli',
'blk=kharoshthi' => 'kharoshthi',
'blk=khitansmallscript' => 'khitansmallscript',
'blk=khmer' => 'khmer',
'blk=khmersymbols' => 'khmersymbols',
'blk=khojki' => 'khojki',
'blk=khudawadi' => 'khudawadi',
'blk=lao' => 'lao',
'blk=latin1' => 'latin1sup',
'blk=latin1sup' => 'latin1sup',
'blk=latin1supplement' => 'latin1sup',
'blk=latinexta' => 'latinexta',
'blk=latinextadditional' => 'latinextadditional',
'blk=latinextb' => 'latinextb',
'blk=latinextc' => 'latinextc',
'blk=latinextd' => 'latinextd',
'blk=latinexte' => 'latinexte',
'blk=latinextendeda' => 'latinexta',
'blk=latinextendedadditional' => 'latinextadditional',
'blk=latinextendedb' => 'latinextb',
'blk=latinextendedc' => 'latinextc',
'blk=latinextendedd' => 'latinextd',
'blk=latinextendede' => 'latinexte',
'blk=latinextendedf' => 'latinextf',
'blk=latinextendedg' => 'latinextg',
'blk=latinextf' => 'latinextf',
'blk=latinextg' => 'latinextg',
'blk=lepcha' => 'lepcha',
'blk=letterlikesymbols' => 'letterlikesymbols',
'blk=limbu' => 'limbu',
'blk=lineara' => 'lineara',
'blk=linearbideograms' => 'linearbideograms',
'blk=linearbsyllabary' => 'linearbsyllabary',
'blk=lisu' => 'lisu',
'blk=lisusup' => 'lisusup',
'blk=lisusupplement' => 'lisusup',
'blk=lowsurrogates' => 'lowsurrogates',
'blk=lycian' => 'lycian',
'blk=lydian' => 'lydian',
'blk=mahajani' => 'mahajani',
'blk=mahjong' => 'mahjong',
'blk=mahjongtiles' => 'mahjong',
'blk=makasar' => 'makasar',
'blk=malayalam' => 'malayalam',
'blk=mandaic' => 'mandaic',
'blk=manichaean' => 'manichaean',
'blk=marchen' => 'marchen',
'blk=masaramgondi' => 'masaramgondi',
'blk=mathalphanum' => 'mathalphanum',
'blk=mathematicalalphanumericsymbols' => 'mathalphanum',
'blk=mathematicaloperators' => 'mathoperators',
'blk=mathoperators' => 'mathoperators',
'blk=mayannumerals' => 'mayannumerals',
'blk=medefaidrin' => 'medefaidrin',
'blk=meeteimayek' => 'meeteimayek',
'blk=meeteimayekext' => 'meeteimayekext',
'blk=meeteimayekextensions' => 'meeteimayekext',
'blk=mendekikakui' => 'mendekikakui',
'blk=meroiticcursive' => 'meroiticcursive',
'blk=meroitichieroglyphs' => 'meroitichieroglyphs',
'blk=miao' => 'miao',
'blk=miscarrows' => 'miscarrows',
'blk=miscellaneousmathematicalsymbolsa' => 'miscmathsymbolsa',
'blk=miscellaneousmathematicalsymbolsb' => 'miscmathsymbolsb',
'blk=miscellaneoussymbols' => 'miscsymbols',
'blk=miscellaneoussymbolsandarrows' => 'miscarrows',
'blk=miscellaneoussymbolsandpictographs' => 'miscpictographs',
'blk=miscellaneoustechnical' => 'misctechnical',
'blk=miscmathsymbolsa' => 'miscmathsymbolsa',
'blk=miscmathsymbolsb' => 'miscmathsymbolsb',
'blk=miscpictographs' => 'miscpictographs',
'blk=miscsymbols' => 'miscsymbols',
'blk=misctechnical' => 'misctechnical',
'blk=modi' => 'modi',
'blk=modifierletters' => 'modifierletters',
'blk=modifiertoneletters' => 'modifiertoneletters',
'blk=mongolian' => 'mongolian',
'blk=mongoliansup' => 'mongoliansup',
'blk=mongoliansupplement' => 'mongoliansup',
'blk=mro' => 'mro',
'blk=multani' => 'multani',
'blk=music' => 'music',
'blk=musicalsymbols' => 'music',
'blk=myanmar' => 'myanmar',
'blk=myanmarexta' => 'myanmarexta',
'blk=myanmarextb' => 'myanmarextb',
'blk=myanmarextendeda' => 'myanmarexta',
'blk=myanmarextendedb' => 'myanmarextb',
'blk=nabataean' => 'nabataean',
'blk=nagmundari' => 'nagmundari',
'blk=nandinagari' => 'nandinagari',
'blk=nb' => 'nb',
'blk=newa' => 'newa',
'blk=newtailue' => 'newtailue',
'blk=nko' => 'nko',
'blk=noblock' => 'nb',
'blk=numberforms' => 'numberforms',
'blk=nushu' => 'nushu',
'blk=nyiakengpuachuehmong' => 'nyiakengpuachuehmong',
'blk=ocr' => 'ocr',
'blk=ogham' => 'ogham',
'blk=olchiki' => 'olchiki',
'blk=oldhungarian' => 'oldhungarian',
'blk=olditalic' => 'olditalic',
'blk=oldnortharabian' => 'oldnortharabian',
'blk=oldpermic' => 'oldpermic',
'blk=oldpersian' => 'oldpersian',
'blk=oldsogdian' => 'oldsogdian',
'blk=oldsoutharabian' => 'oldsoutharabian',
'blk=oldturkic' => 'oldturkic',
'blk=olduyghur' => 'olduyghur',
'blk=opticalcharacterrecognition' => 'ocr',
'blk=oriya' => 'oriya',
'blk=ornamentaldingbats' => 'ornamentaldingbats',
'blk=osage' => 'osage',
'blk=osmanya' => 'osmanya',
'blk=ottomansiyaqnumbers' => 'ottomansiyaqnumbers',
'blk=pahawhhmong' => 'pahawhhmong',
'blk=palmyrene' => 'palmyrene',
'blk=paucinhau' => 'paucinhau',
'blk=phagspa' => 'phagspa',
'blk=phaistos' => 'phaistos',
'blk=phaistosdisc' => 'phaistos',
'blk=phoenician' => 'phoenician',
'blk=phoneticext' => 'phoneticext',
'blk=phoneticextensions' => 'phoneticext',
'blk=phoneticextensionssupplement' => 'phoneticextsup',
'blk=phoneticextsup' => 'phoneticextsup',
'blk=playingcards' => 'playingcards',
'blk=privateuse' => 'pua',
'blk=privateusearea' => 'pua',
'blk=psalterpahlavi' => 'psalterpahlavi',
'blk=pua' => 'pua',
'blk=punctuation' => 'punctuation',
'blk=rejang' => 'rejang',
'blk=rumi' => 'rumi',
'blk=ruminumeralsymbols' => 'rumi',
'blk=runic' => 'runic',
'blk=samaritan' => 'samaritan',
'blk=saurashtra' => 'saurashtra',
'blk=sharada' => 'sharada',
'blk=shavian' => 'shavian',
'blk=shorthandformatcontrols' => 'shorthandformatcontrols',
'blk=siddham' => 'siddham',
'blk=sinhala' => 'sinhala',
'blk=sinhalaarchaicnumbers' => 'sinhalaarchaicnumbers',
'blk=smallforms' => 'smallforms',
'blk=smallformvariants' => 'smallforms',
'blk=smallkanaext' => 'smallkanaext',
'blk=smallkanaextension' => 'smallkanaext',
'blk=sogdian' => 'sogdian',
'blk=sorasompeng' => 'sorasompeng',
'blk=soyombo' => 'soyombo',
'blk=spacingmodifierletters' => 'modifierletters',
'blk=specials' => 'specials',
'blk=sundanese' => 'sundanese',
'blk=sundanesesup' => 'sundanesesup',
'blk=sundanesesupplement' => 'sundanesesup',
'blk=suparrowsa' => 'suparrowsa',
'blk=suparrowsb' => 'suparrowsb',
'blk=suparrowsc' => 'suparrowsc',
'blk=superandsub' => 'superandsub',
'blk=superscriptsandsubscripts' => 'superandsub',
'blk=supmathoperators' => 'supmathoperators',
'blk=supplementalarrowsa' => 'suparrowsa',
'blk=supplementalarrowsb' => 'suparrowsb',
'blk=supplementalarrowsc' => 'suparrowsc',
'blk=supplementalmathematicaloperators' => 'supmathoperators',
'blk=supplementalpunctuation' => 'suppunctuation',
'blk=supplementalsymbolsandpictographs' => 'supsymbolsandpictographs',
'blk=supplementaryprivateuseareaa' => 'suppuaa',
'blk=supplementaryprivateuseareab' => 'suppuab',
'blk=suppuaa' => 'suppuaa',
'blk=suppuab' => 'suppuab',
'blk=suppunctuation' => 'suppunctuation',
'blk=supsymbolsandpictographs' => 'supsymbolsandpictographs',
'blk=suttonsignwriting' => 'suttonsignwriting',
'blk=sylotinagri' => 'sylotinagri',
'blk=symbolsandpictographsexta' => 'symbolsandpictographsexta',
'blk=symbolsandpictographsextendeda' => 'symbolsandpictographsexta',
'blk=symbolsforlegacycomputing' => 'symbolsforlegacycomputing',
'blk=syriac' => 'syriac',
'blk=syriacsup' => 'syriacsup',
'blk=syriacsupplement' => 'syriacsup',
'blk=tagalog' => 'tagalog',
'blk=tagbanwa' => 'tagbanwa',
'blk=tags' => 'tags',
'blk=taile' => 'taile',
'blk=taitham' => 'taitham',
'blk=taiviet' => 'taiviet',
'blk=taixuanjing' => 'taixuanjing',
'blk=taixuanjingsymbols' => 'taixuanjing',
'blk=takri' => 'takri',
'blk=tamil' => 'tamil',
'blk=tamilsup' => 'tamilsup',
'blk=tamilsupplement' => 'tamilsup',
'blk=tangsa' => 'tangsa',
'blk=tangut' => 'tangut',
'blk=tangutcomponents' => 'tangutcomponents',
'blk=tangutsup' => 'tangutsup',
'blk=tangutsupplement' => 'tangutsup',
'blk=telugu' => 'telugu',
'blk=thaana' => 'thaana',
'blk=thai' => 'thai',
'blk=tibetan' => 'tibetan',
'blk=tifinagh' => 'tifinagh',
'blk=tirhuta' => 'tirhuta',
'blk=toto' => 'toto',
'blk=transportandmap' => 'transportandmap',
'blk=transportandmapsymbols' => 'transportandmap',
'blk=ucas' => 'ucas',
'blk=ucasext' => 'ucasext',
'blk=ucasexta' => 'ucasexta',
'blk=ugaritic' => 'ugaritic',
'blk=unifiedcanadianaboriginalsyllabics' => 'ucas',
'blk=unifiedcanadianaboriginalsyllabicsextended' => 'ucasext',
'blk=unifiedcanadianaboriginalsyllabicsextendeda' => 'ucasexta',
'blk=vai' => 'vai',
'blk=variationselectors' => 'vs',
'blk=variationselectorssupplement' => 'vssup',
'blk=vedicext' => 'vedicext',
'blk=vedicextensions' => 'vedicext',
'blk=verticalforms' => 'verticalforms',
'blk=vithkuqi' => 'vithkuqi',
'blk=vs' => 'vs',
'blk=vssup' => 'vssup',
'blk=wancho' => 'wancho',
'blk=warangciti' => 'warangciti',
'blk=yezidi' => 'yezidi',
'blk=yijing' => 'yijing',
'blk=yijinghexagramsymbols' => 'yijing',
'blk=yiradicals' => 'yiradicals',
'blk=yisyllables' => 'yisyllables',
'blk=zanabazarsquare' => 'zanabazarsquare',
'blk=znamennymusic' => 'znamennymusic',
'blk=znamennymusicalnotation' => 'znamennymusic',
'bpt=c' => 'c',
'bpt=close' => 'c',
'bpt=n' => 'n',
'bpt=none' => 'n',
'bpt=o' => 'o',
'bpt=open' => 'o',
'cased=f' => 'n',
'cased=false' => 'n',
'cased=n' => 'n',
'cased=no' => 'n',
'cased=t' => 'y',
'cased=true' => 'y',
'cased=y' => 'y',
'cased=yes' => 'y',
'ccc=0' => 'nr',
'ccc=1' => 'ov',
'ccc=10' => 'ccc10',
'ccc=103' => 'ccc103',
'ccc=107' => 'ccc107',
'ccc=11' => 'ccc11',
'ccc=118' => 'ccc118',
'ccc=12' => 'ccc12',
'ccc=122' => 'ccc122',
'ccc=129' => 'ccc129',
'ccc=13' => 'ccc13',
'ccc=130' => 'ccc130',
'ccc=132' => 'ccc132',
'ccc=133' => 'ccc133',
'ccc=14' => 'ccc14',
'ccc=15' => 'ccc15',
'ccc=16' => 'ccc16',
'ccc=17' => 'ccc17',
'ccc=18' => 'ccc18',
'ccc=19' => 'ccc19',
'ccc=20' => 'ccc20',
'ccc=200' => 'atbl',
'ccc=202' => 'atb',
'ccc=21' => 'ccc21',
'ccc=214' => 'ata',
'ccc=216' => 'atar',
'ccc=218' => 'bl',
'ccc=22' => 'ccc22',
'ccc=220' => 'b',
'ccc=222' => 'br',
'ccc=224' => 'l',
'ccc=226' => 'r',
'ccc=228' => 'al',
'ccc=23' => 'ccc23',
'ccc=230' => 'a',
'ccc=232' => 'ar',
'ccc=233' => 'db',
'ccc=234' => 'da',
'ccc=24' => 'ccc24',
'ccc=240' => 'is',
'ccc=25' => 'ccc25',
'ccc=26' => 'ccc26',
'ccc=27' => 'ccc27',
'ccc=28' => 'ccc28',
'ccc=29' => 'ccc29',
'ccc=30' => 'ccc30',
'ccc=31' => 'ccc31',
'ccc=32' => 'ccc32',
'ccc=33' => 'ccc33',
'ccc=34' => 'ccc34',
'ccc=35' => 'ccc35',
'ccc=36' => 'ccc36',
'ccc=6' => 'hanr',
'ccc=7' => 'nk',
'ccc=8' => 'kv',
'ccc=84' => 'ccc84',
'ccc=9' => 'vr',
'ccc=91' => 'ccc91',
'ccc=a' => 'a',
'ccc=above' => 'a',
'ccc=aboveleft' => 'al',
'ccc=aboveright' => 'ar',
'ccc=al' => 'al',
'ccc=ar' => 'ar',
'ccc=ata' => 'ata',
'ccc=atar' => 'atar',
'ccc=atb' => 'atb',
'ccc=atbl' => 'atbl',
'ccc=attachedabove' => 'ata',
'ccc=attachedaboveright' => 'atar',
'ccc=attachedbelow' => 'atb',
'ccc=attachedbelowleft' => 'atbl',
'ccc=b' => 'b',
'ccc=below' => 'b',
'ccc=belowleft' => 'bl',
'ccc=belowright' => 'br',
'ccc=bl' => 'bl',
'ccc=br' => 'br',
'ccc=ccc10' => 'ccc10',
'ccc=ccc103' => 'ccc103',
'ccc=ccc107' => 'ccc107',
'ccc=ccc11' => 'ccc11',
'ccc=ccc118' => 'ccc118',
'ccc=ccc12' => 'ccc12',
'ccc=ccc122' => 'ccc122',
'ccc=ccc129' => 'ccc129',
'ccc=ccc13' => 'ccc13',
'ccc=ccc130' => 'ccc130',
'ccc=ccc132' => 'ccc132',
'ccc=ccc133' => 'ccc133',
'ccc=ccc14' => 'ccc14',
'ccc=ccc15' => 'ccc15',
'ccc=ccc16' => 'ccc16',
'ccc=ccc17' => 'ccc17',
'ccc=ccc18' => 'ccc18',
'ccc=ccc19' => 'ccc19',
'ccc=ccc20' => 'ccc20',
'ccc=ccc21' => 'ccc21',
'ccc=ccc22' => 'ccc22',
'ccc=ccc23' => 'ccc23',
'ccc=ccc24' => 'ccc24',
'ccc=ccc25' => 'ccc25',
'ccc=ccc26' => 'ccc26',
'ccc=ccc27' => 'ccc27',
'ccc=ccc28' => 'ccc28',
'ccc=ccc29' => 'ccc29',
'ccc=ccc30' => 'ccc30',
'ccc=ccc31' => 'ccc31',
'ccc=ccc32' => 'ccc32',
'ccc=ccc33' => 'ccc33',
'ccc=ccc34' => 'ccc34',
'ccc=ccc35' => 'ccc35',
'ccc=ccc36' => 'ccc36',
'ccc=ccc84' => 'ccc84',
'ccc=ccc91' => 'ccc91',
'ccc=da' => 'da',
'ccc=db' => 'db',
'ccc=doubleabove' => 'da',
'ccc=doublebelow' => 'db',
'ccc=hanr' => 'hanr',
'ccc=hanreading' => 'hanr',
'ccc=iotasubscript' => 'is',
'ccc=is' => 'is',
'ccc=kanavoicing' => 'kv',
'ccc=kv' => 'kv',
'ccc=l' => 'l',
'ccc=left' => 'l',
'ccc=nk' => 'nk',
'ccc=notreordered' => 'nr',
'ccc=nr' => 'nr',
'ccc=nukta' => 'nk',
'ccc=ov' => 'ov',
'ccc=overlay' => 'ov',
'ccc=r' => 'r',
'ccc=right' => 'r',
'ccc=virama' => 'vr',
'ccc=vr' => 'vr',
'ce=f' => 'n',
'ce=false' => 'n',
'ce=n' => 'n',
'ce=no' => 'n',
'ce=t' => 'y',
'ce=true' => 'y',
'ce=y' => 'y',
'ce=yes' => 'y',
'ci=f' => 'n',
'ci=false' => 'n',
'ci=n' => 'n',
'ci=no' => 'n',
'ci=t' => 'y',
'ci=true' => 'y',
'ci=y' => 'y',
'ci=yes' => 'y',
'compex=f' => 'n',
'compex=false' => 'n',
'compex=n' => 'n',
'compex=no' => 'n',
'compex=t' => 'y',
'compex=true' => 'y',
'compex=y' => 'y',
'compex=yes' => 'y',
'cwcf=f' => 'n',
'cwcf=false' => 'n',
'cwcf=n' => 'n',
'cwcf=no' => 'n',
'cwcf=t' => 'y',
'cwcf=true' => 'y',
'cwcf=y' => 'y',
'cwcf=yes' => 'y',
'cwcm=f' => 'n',
'cwcm=false' => 'n',
'cwcm=n' => 'n',
'cwcm=no' => 'n',
'cwcm=t' => 'y',
'cwcm=true' => 'y',
'cwcm=y' => 'y',
'cwcm=yes' => 'y',
'cwkcf=f' => 'n',
'cwkcf=false' => 'n',
'cwkcf=n' => 'n',
'cwkcf=no' => 'n',
'cwkcf=t' => 'y',
'cwkcf=true' => 'y',
'cwkcf=y' => 'y',
'cwkcf=yes' => 'y',
'cwl=f' => 'n',
'cwl=false' => 'n',
'cwl=n' => 'n',
'cwl=no' => 'n',
'cwl=t' => 'y',
'cwl=true' => 'y',
'cwl=y' => 'y',
'cwl=yes' => 'y',
'cwt=f' => 'n',
'cwt=false' => 'n',
'cwt=n' => 'n',
'cwt=no' => 'n',
'cwt=t' => 'y',
'cwt=true' => 'y',
'cwt=y' => 'y',
'cwt=yes' => 'y',
'cwu=f' => 'n',
'cwu=false' => 'n',
'cwu=n' => 'n',
'cwu=no' => 'n',
'cwu=t' => 'y',
'cwu=true' => 'y',
'cwu=y' => 'y',
'cwu=yes' => 'y',
'dash=f' => 'n',
'dash=false' => 'n',
'dash=n' => 'n',
'dash=no' => 'n',
'dash=t' => 'y',
'dash=true' => 'y',
'dash=y' => 'y',
'dash=yes' => 'y',
'dep=f' => 'n',
'dep=false' => 'n',
'dep=n' => 'n',
'dep=no' => 'n',
'dep=t' => 'y',
'dep=true' => 'y',
'dep=y' => 'y',
'dep=yes' => 'y',
'di=f' => 'n',
'di=false' => 'n',
'di=n' => 'n',
'di=no' => 'n',
'di=t' => 'y',
'di=true' => 'y',
'di=y' => 'y',
'di=yes' => 'y',
'dia=f' => 'n',
'dia=false' => 'n',
'dia=n' => 'n',
'dia=no' => 'n',
'dia=t' => 'y',
'dia=true' => 'y',
'dia=y' => 'y',
'dia=yes' => 'y',
'dt=can' => 'can',
'dt=canonical' => 'can',
'dt=circle' => 'enc',
'dt=com' => 'com',
'dt=compat' => 'com',
'dt=enc' => 'enc',
'dt=fin' => 'fin',
'dt=final' => 'fin',
'dt=font' => 'font',
'dt=fra' => 'fra',
'dt=fraction' => 'fra',
'dt=init' => 'init',
'dt=initial' => 'init',
'dt=iso' => 'iso',
'dt=isolated' => 'iso',
'dt=med' => 'med',
'dt=medial' => 'med',
'dt=nar' => 'nar',
'dt=narrow' => 'nar',
'dt=nb' => 'nb',
'dt=nobreak' => 'nb',
'dt=noncanon' => 'noncanon',
'dt=noncanonical' => 'noncanon',
'dt=none' => 'none',
'dt=small' => 'sml',
'dt=sml' => 'sml',
'dt=sqr' => 'sqr',
'dt=square' => 'sqr',
'dt=sub' => 'sub',
'dt=sup' => 'sup',
'dt=super' => 'sup',
'dt=vert' => 'vert',
'dt=vertical' => 'vert',
'dt=wide' => 'wide',
'ea=a' => 'a',
'ea=ambiguous' => 'a',
'ea=f' => 'f',
'ea=fullwidth' => 'f',
'ea=h' => 'h',
'ea=halfwidth' => 'h',
'ea=n' => 'n',
'ea=na' => 'na',
'ea=narrow' => 'na',
'ea=neutral' => 'n',
'ea=w' => 'w',
'ea=wide' => 'w',
'ebase=f' => 'n',
'ebase=false' => 'n',
'ebase=n' => 'n',
'ebase=no' => 'n',
'ebase=t' => 'y',
'ebase=true' => 'y',
'ebase=y' => 'y',
'ebase=yes' => 'y',
'ecomp=f' => 'n',
'ecomp=false' => 'n',
'ecomp=n' => 'n',
'ecomp=no' => 'n',
'ecomp=t' => 'y',
'ecomp=true' => 'y',
'ecomp=y' => 'y',
'ecomp=yes' => 'y',
'emod=f' => 'n',
'emod=false' => 'n',
'emod=n' => 'n',
'emod=no' => 'n',
'emod=t' => 'y',
'emod=true' => 'y',
'emod=y' => 'y',
'emod=yes' => 'y',
'emoji=f' => 'n',
'emoji=false' => 'n',
'emoji=n' => 'n',
'emoji=no' => 'n',
'emoji=t' => 'y',
'emoji=true' => 'y',
'emoji=y' => 'y',
'emoji=yes' => 'y',
'epres=f' => 'n',
'epres=false' => 'n',
'epres=n' => 'n',
'epres=no' => 'n',
'epres=t' => 'y',
'epres=true' => 'y',
'epres=y' => 'y',
'epres=yes' => 'y',
'ext=f' => 'n',
'ext=false' => 'n',
'ext=n' => 'n',
'ext=no' => 'n',
'ext=t' => 'y',
'ext=true' => 'y',
'ext=y' => 'y',
'ext=yes' => 'y',
'extpict=f' => 'n',
'extpict=false' => 'n',
'extpict=n' => 'n',
'extpict=no' => 'n',
'extpict=t' => 'y',
'extpict=true' => 'y',
'extpict=y' => 'y',
'extpict=yes' => 'y',
'gc=c' => 'c',
'gc=casedletter' => 'lc',
'gc=cc' => 'cc',
'gc=cf' => 'cf',
'gc=closepunctuation' => 'pe',
'gc=cn' => 'cn',
'gc=cntrl' => 'cc',
'gc=co' => 'co',
'gc=combiningmark' => 'm',
'gc=connectorpunctuation' => 'pc',
'gc=control' => 'cc',
'gc=cs' => 'cs',
'gc=currencysymbol' => 'sc',
'gc=dashpunctuation' => 'pd',
'gc=decimalnumber' => 'nd',
'gc=digit' => 'nd',
'gc=enclosingmark' => 'me',
'gc=finalpunctuation' => 'pf',
'gc=format' => 'cf',
'gc=initialpunctuation' => 'pi',
'gc=l' => 'l',
'gc=l&' => 'lc',
'gc=l_' => 'lc',
'gc=lc' => 'lc',
'gc=letter' => 'l',
'gc=letternumber' => 'nl',
'gc=lineseparator' => 'zl',
'gc=ll' => 'll',
'gc=lm' => 'lm',
'gc=lo' => 'lo',
'gc=lowercaseletter' => 'll',
'gc=lt' => 'lt',
'gc=lu' => 'lu',
'gc=m' => 'm',
'gc=mark' => 'm',
'gc=mathsymbol' => 'sm',
'gc=mc' => 'mc',
'gc=me' => 'me',
'gc=mn' => 'mn',
'gc=modifierletter' => 'lm',
'gc=modifiersymbol' => 'sk',
'gc=n' => 'n',
'gc=nd' => 'nd',
'gc=nl' => 'nl',
'gc=no' => 'no',
'gc=nonspacingmark' => 'mn',
'gc=number' => 'n',
'gc=openpunctuation' => 'ps',
'gc=other' => 'c',
'gc=otherletter' => 'lo',
'gc=othernumber' => 'no',
'gc=otherpunctuation' => 'po',
'gc=othersymbol' => 'so',
'gc=p' => 'p',
'gc=paragraphseparator' => 'zp',
'gc=pc' => 'pc',
'gc=pd' => 'pd',
'gc=pe' => 'pe',
'gc=pf' => 'pf',
'gc=pi' => 'pi',
'gc=po' => 'po',
'gc=privateuse' => 'co',
'gc=ps' => 'ps',
'gc=punct' => 'p',
'gc=punctuation' => 'p',
'gc=s' => 's',
'gc=sc' => 'sc',
'gc=separator' => 'z',
'gc=sk' => 'sk',
'gc=sm' => 'sm',
'gc=so' => 'so',
'gc=spaceseparator' => 'zs',
'gc=spacingmark' => 'mc',
'gc=surrogate' => 'cs',
'gc=symbol' => 's',
'gc=titlecaseletter' => 'lt',
'gc=unassigned' => 'cn',
'gc=uppercaseletter' => 'lu',
'gc=z' => 'z',
'gc=zl' => 'zl',
'gc=zp' => 'zp',
'gc=zs' => 'zs',
'gcb=cn' => 'cn',
'gcb=control' => 'cn',
'gcb=cr' => 'cr',
'gcb=eb' => 'eb',
'gcb=ebase' => 'eb',
'gcb=ebasegaz' => 'ebg',
'gcb=ebg' => 'ebg',
'gcb=em' => 'em',
'gcb=emodifier' => 'em',
'gcb=ex' => 'ex',
'gcb=extend' => 'ex',
'gcb=gaz' => 'gaz',
'gcb=glueafterzwj' => 'gaz',
'gcb=l' => 'l',
'gcb=lf' => 'lf',
'gcb=lv' => 'lv',
'gcb=lvt' => 'lvt',
'gcb=other' => 'xx',
'gcb=pp' => 'pp',
'gcb=prepend' => 'pp',
'gcb=regionalindicator' => 'ri',
'gcb=ri' => 'ri',
'gcb=sm' => 'sm',
'gcb=spacingmark' => 'sm',
'gcb=t' => 't',
'gcb=v' => 'v',
'gcb=xx' => 'xx',
'gcb=zwj' => 'zwj',
'grbase=f' => 'n',
'grbase=false' => 'n',
'grbase=n' => 'n',
'grbase=no' => 'n',
'grbase=t' => 'y',
'grbase=true' => 'y',
'grbase=y' => 'y',
'grbase=yes' => 'y',
'grext=f' => 'n',
'grext=false' => 'n',
'grext=n' => 'n',
'grext=no' => 'n',
'grext=t' => 'y',
'grext=true' => 'y',
'grext=y' => 'y',
'grext=yes' => 'y',
'hex=f' => 'n',
'hex=false' => 'n',
'hex=n' => 'n',
'hex=no' => 'n',
'hex=t' => 'y',
'hex=true' => 'y',
'hex=y' => 'y',
'hex=yes' => 'y',
'hst=l' => 'l',
'hst=leadingjamo' => 'l',
'hst=lv' => 'lv',
'hst=lvsyllable' => 'lv',
'hst=lvt' => 'lvt',
'hst=lvtsyllable' => 'lvt',
'hst=na' => 'na',
'hst=notapplicable' => 'na',
'hst=t' => 't',
'hst=trailingjamo' => 't',
'hst=v' => 'v',
'hst=voweljamo' => 'v',
'hyphen=f' => 'n',
'hyphen=false' => 'n',
'hyphen=n' => 'n',
'hyphen=no' => 'n',
'hyphen=t' => 'y',
'hyphen=true' => 'y',
'hyphen=y' => 'y',
'hyphen=yes' => 'y',
'idc=f' => 'n',
'idc=false' => 'n',
'idc=n' => 'n',
'idc=no' => 'n',
'idc=t' => 'y',
'idc=true' => 'y',
'idc=y' => 'y',
'idc=yes' => 'y',
'identifierstatus=allowed' => 'allowed',
'identifierstatus=restricted' => 'restricted',
'identifiertype=defaultignorable' => 'defaultignorable',
'identifiertype=deprecated' => 'deprecated',
'identifiertype=exclusion' => 'exclusion',
'identifiertype=inclusion' => 'inclusion',
'identifiertype=limiteduse' => 'limiteduse',
'identifiertype=notcharacter' => 'notcharacter',
'identifiertype=notnfkc' => 'notnfkc',
'identifiertype=notxid' => 'notxid',
'identifiertype=obsolete' => 'obsolete',
'identifiertype=recommended' => 'recommended',
'identifiertype=technical' => 'technical',
'identifiertype=uncommonuse' => 'uncommonuse',
'ideo=f' => 'n',
'ideo=false' => 'n',
'ideo=n' => 'n',
'ideo=no' => 'n',
'ideo=t' => 'y',
'ideo=true' => 'y',
'ideo=y' => 'y',
'ideo=yes' => 'y',
'ids=f' => 'n',
'ids=false' => 'n',
'ids=n' => 'n',
'ids=no' => 'n',
'ids=t' => 'y',
'ids=true' => 'y',
'ids=y' => 'y',
'ids=yes' => 'y',
'idsb=f' => 'n',
'idsb=false' => 'n',
'idsb=n' => 'n',
'idsb=no' => 'n',
'idsb=t' => 'y',
'idsb=true' => 'y',
'idsb=y' => 'y',
'idsb=yes' => 'y',
'idst=f' => 'n',
'idst=false' => 'n',
'idst=n' => 'n',
'idst=no' => 'n',
'idst=t' => 'y',
'idst=true' => 'y',
'idst=y' => 'y',
'idst=yes' => 'y',
'in=1.1' => '1.1',
'in=10.0' => '10.0',
'in=11.0' => '11.0',
'in=12.0' => '12.0',
'in=12.1' => '12.1',
'in=13.0' => '13.0',
'in=14.0' => '14.0',
'in=15.0' => '15.0',
'in=2.0' => '2.0',
'in=2.1' => '2.1',
'in=3.0' => '3.0',
'in=3.1' => '3.1',
'in=3.2' => '3.2',
'in=4.0' => '4.0',
'in=4.1' => '4.1',
'in=5.0' => '5.0',
'in=5.1' => '5.1',
'in=5.2' => '5.2',
'in=6.0' => '6.0',
'in=6.1' => '6.1',
'in=6.2' => '6.2',
'in=6.3' => '6.3',
'in=7.0' => '7.0',
'in=8.0' => '8.0',
'in=9.0' => '9.0',
'in=na' => 'unassigned',
'in=unassigned' => 'unassigned',
'in=v100' => '10.0',
'in=v11' => '1.1',
'in=v110' => '11.0',
'in=v120' => '12.0',
'in=v121' => '12.1',
'in=v130' => '13.0',
'in=v140' => '14.0',
'in=v150' => '15.0',
'in=v20' => '2.0',
'in=v21' => '2.1',
'in=v30' => '3.0',
'in=v31' => '3.1',
'in=v32' => '3.2',
'in=v40' => '4.0',
'in=v41' => '4.1',
'in=v50' => '5.0',
'in=v51' => '5.1',
'in=v52' => '5.2',
'in=v60' => '6.0',
'in=v61' => '6.1',
'in=v62' => '6.2',
'in=v63' => '6.3',
'in=v70' => '7.0',
'in=v80' => '8.0',
'in=v90' => '9.0',
'inpc=bottom' => 'bottom',
'inpc=bottomandleft' => 'bottomandleft',
'inpc=bottomandright' => 'bottomandright',
'inpc=left' => 'left',
'inpc=leftandright' => 'leftandright',
'inpc=na' => 'na',
'inpc=overstruck' => 'overstruck',
'inpc=right' => 'right',
'inpc=top' => 'top',
'inpc=topandbottom' => 'topandbottom',
'inpc=topandbottomandleft' => 'topandbottomandleft',
'inpc=topandbottomandright' => 'topandbottomandright',
'inpc=topandleft' => 'topandleft',
'inpc=topandleftandright' => 'topandleftandright',
'inpc=topandright' => 'topandright',
'inpc=visualorderleft' => 'visualorderleft',
'insc=avagraha' => 'avagraha',
'insc=bindu' => 'bindu',
'insc=brahmijoiningnumber' => 'brahmijoiningnumber',
'insc=cantillationmark' => 'cantillationmark',
'insc=consonant' => 'consonant',
'insc=consonantdead' => 'consonantdead',
'insc=consonantfinal' => 'consonantfinal',
'insc=consonantheadletter' => 'consonantheadletter',
'insc=consonantinitialpostfixed' => 'consonantinitialpostfixed',
'insc=consonantkiller' => 'consonantkiller',
'insc=consonantmedial' => 'consonantmedial',
'insc=consonantplaceholder' => 'consonantplaceholder',
'insc=consonantprecedingrepha' => 'consonantprecedingrepha',
'insc=consonantprefixed' => 'consonantprefixed',
'insc=consonantsubjoined' => 'consonantsubjoined',
'insc=consonantsucceedingrepha' => 'consonantsucceedingrepha',
'insc=consonantwithstacker' => 'consonantwithstacker',
'insc=geminationmark' => 'geminationmark',
'insc=invisiblestacker' => 'invisiblestacker',
'insc=joiner' => 'joiner',
'insc=modifyingletter' => 'modifyingletter',
'insc=nonjoiner' => 'nonjoiner',
'insc=nukta' => 'nukta',
'insc=number' => 'number',
'insc=numberjoiner' => 'numberjoiner',
'insc=other' => 'other',
'insc=purekiller' => 'purekiller',
'insc=registershifter' => 'registershifter',
'insc=syllablemodifier' => 'syllablemodifier',
'insc=toneletter' => 'toneletter',
'insc=tonemark' => 'tonemark',
'insc=virama' => 'virama',
'insc=visarga' => 'visarga',
'insc=vowel' => 'vowel',
'insc=voweldependent' => 'voweldependent',
'insc=vowelindependent' => 'vowelindependent',
'jg=africanfeh' => 'africanfeh',
'jg=africannoon' => 'africannoon',
'jg=africanqaf' => 'africanqaf',
'jg=ain' => 'ain',
'jg=alaph' => 'alaph',
'jg=alef' => 'alef',
'jg=beh' => 'beh',
'jg=beth' => 'beth',
'jg=burushaskiyehbarree' => 'burushaskiyehbarree',
'jg=dal' => 'dal',
'jg=dalathrish' => 'dalathrish',
'jg=e' => 'e',
'jg=farsiyeh' => 'farsiyeh',
'jg=fe' => 'fe',
'jg=feh' => 'feh',
'jg=finalsemkath' => 'finalsemkath',
'jg=gaf' => 'gaf',
'jg=gamal' => 'gamal',
'jg=hah' => 'hah',
'jg=hamzaonhehgoal' => 'tehmarbutagoal',
'jg=hanifirohingyakinnaya' => 'hanifirohingyakinnaya',
'jg=hanifirohingyapa' => 'hanifirohingyapa',
'jg=he' => 'he',
'jg=heh' => 'heh',
'jg=hehgoal' => 'hehgoal',
'jg=heth' => 'heth',
'jg=kaf' => 'kaf',
'jg=kaph' => 'kaph',
'jg=khaph' => 'khaph',
'jg=knottedheh' => 'knottedheh',
'jg=lam' => 'lam',
'jg=lamadh' => 'lamadh',
'jg=malayalambha' => 'malayalambha',
'jg=malayalamja' => 'malayalamja',
'jg=malayalamlla' => 'malayalamlla',
'jg=malayalamllla' => 'malayalamllla',
'jg=malayalamnga' => 'malayalamnga',
'jg=malayalamnna' => 'malayalamnna',
'jg=malayalamnnna' => 'malayalamnnna',
'jg=malayalamnya' => 'malayalamnya',
'jg=malayalamra' => 'malayalamra',
'jg=malayalamssa' => 'malayalamssa',
'jg=malayalamtta' => 'malayalamtta',
'jg=manichaeanaleph' => 'manichaeanaleph',
'jg=manichaeanayin' => 'manichaeanayin',
'jg=manichaeanbeth' => 'manichaeanbeth',
'jg=manichaeandaleth' => 'manichaeandaleth',
'jg=manichaeandhamedh' => 'manichaeandhamedh',
'jg=manichaeanfive' => 'manichaeanfive',
'jg=manichaeangimel' => 'manichaeangimel',
'jg=manichaeanheth' => 'manichaeanheth',
'jg=manichaeanhundred' => 'manichaeanhundred',
'jg=manichaeankaph' => 'manichaeankaph',
'jg=manichaeanlamedh' => 'manichaeanlamedh',
'jg=manichaeanmem' => 'manichaeanmem',
'jg=manichaeannun' => 'manichaeannun',
'jg=manichaeanone' => 'manichaeanone',
'jg=manichaeanpe' => 'manichaeanpe',
'jg=manichaeanqoph' => 'manichaeanqoph',
'jg=manichaeanresh' => 'manichaeanresh',
'jg=manichaeansadhe' => 'manichaeansadhe',
'jg=manichaeansamekh' => 'manichaeansamekh',
'jg=manichaeantaw' => 'manichaeantaw',
'jg=manichaeanten' => 'manichaeanten',
'jg=manichaeanteth' => 'manichaeanteth',
'jg=manichaeanthamedh' => 'manichaeanthamedh',
'jg=manichaeantwenty' => 'manichaeantwenty',
'jg=manichaeanwaw' => 'manichaeanwaw',
'jg=manichaeanyodh' => 'manichaeanyodh',
'jg=manichaeanzayin' => 'manichaeanzayin',
'jg=meem' => 'meem',
'jg=mim' => 'mim',
'jg=nojoininggroup' => 'nojoininggroup',
'jg=noon' => 'noon',
'jg=nun' => 'nun',
'jg=nya' => 'nya',
'jg=pe' => 'pe',
'jg=qaf' => 'qaf',
'jg=qaph' => 'qaph',
'jg=reh' => 'reh',
'jg=reversedpe' => 'reversedpe',
'jg=rohingyayeh' => 'rohingyayeh',
'jg=sad' => 'sad',
'jg=sadhe' => 'sadhe',
'jg=seen' => 'seen',
'jg=semkath' => 'semkath',
'jg=shin' => 'shin',
'jg=straightwaw' => 'straightwaw',
'jg=swashkaf' => 'swashkaf',
'jg=syriacwaw' => 'syriacwaw',
'jg=tah' => 'tah',
'jg=taw' => 'taw',
'jg=tehmarbuta' => 'tehmarbuta',
'jg=tehmarbutagoal' => 'tehmarbutagoal',
'jg=teth' => 'teth',
'jg=thinyeh' => 'thinyeh',
'jg=verticaltail' => 'verticaltail',
'jg=waw' => 'waw',
'jg=yeh' => 'yeh',
'jg=yehbarree' => 'yehbarree',
'jg=yehwithtail' => 'yehwithtail',
'jg=yudh' => 'yudh',
'jg=yudhhe' => 'yudhhe',
'jg=zain' => 'zain',
'jg=zhain' => 'zhain',
'joinc=f' => 'n',
'joinc=false' => 'n',
'joinc=n' => 'n',
'joinc=no' => 'n',
'joinc=t' => 'y',
'joinc=true' => 'y',
'joinc=y' => 'y',
'joinc=yes' => 'y',
'jt=c' => 'c',
'jt=d' => 'd',
'jt=dualjoining' => 'd',
'jt=joincausing' => 'c',
'jt=l' => 'l',
'jt=leftjoining' => 'l',
'jt=nonjoining' => 'u',
'jt=r' => 'r',
'jt=rightjoining' => 'r',
'jt=t' => 't',
'jt=transparent' => 't',
'jt=u' => 'u',
'lb=ai' => 'ai',
'lb=al' => 'al',
'lb=alphabetic' => 'al',
'lb=ambiguous' => 'ai',
'lb=b2' => 'b2',
'lb=ba' => 'ba',
'lb=bb' => 'bb',
'lb=bk' => 'bk',
'lb=breakafter' => 'ba',
'lb=breakbefore' => 'bb',
'lb=breakboth' => 'b2',
'lb=breaksymbols' => 'sy',
'lb=carriagereturn' => 'cr',
'lb=cb' => 'cb',
'lb=cj' => 'cj',
'lb=cl' => 'cl',
'lb=closeparenthesis' => 'cp',
'lb=closepunctuation' => 'cl',
'lb=cm' => 'cm',
'lb=combiningmark' => 'cm',
'lb=complexcontext' => 'sa',
'lb=conditionaljapanesestarter' => 'cj',
'lb=contingentbreak' => 'cb',
'lb=cp' => 'cp',
'lb=cr' => 'cr',
'lb=eb' => 'eb',
'lb=ebase' => 'eb',
'lb=em' => 'em',
'lb=emodifier' => 'em',
'lb=ex' => 'ex',
'lb=exclamation' => 'ex',
'lb=gl' => 'gl',
'lb=glue' => 'gl',
'lb=h2' => 'h2',
'lb=h3' => 'h3',
'lb=hebrewletter' => 'hl',
'lb=hl' => 'hl',
'lb=hy' => 'hy',
'lb=hyphen' => 'hy',
'lb=id' => 'id',
'lb=ideographic' => 'id',
'lb=in' => 'in',
'lb=infixnumeric' => 'is',
'lb=inseparable' => 'in',
'lb=inseperable' => 'in',
'lb=is' => 'is',
'lb=jl' => 'jl',
'lb=jt' => 'jt',
'lb=jv' => 'jv',
'lb=lf' => 'lf',
'lb=linefeed' => 'lf',
'lb=mandatorybreak' => 'bk',
'lb=nextline' => 'nl',
'lb=nl' => 'nl',
'lb=nonstarter' => 'ns',
'lb=ns' => 'ns',
'lb=nu' => 'nu',
'lb=numeric' => 'nu',
'lb=op' => 'op',
'lb=openpunctuation' => 'op',
'lb=po' => 'po',
'lb=postfixnumeric' => 'po',
'lb=pr' => 'pr',
'lb=prefixnumeric' => 'pr',
'lb=qu' => 'qu',
'lb=quotation' => 'qu',
'lb=regionalindicator' => 'ri',
'lb=ri' => 'ri',
'lb=sa' => 'sa',
'lb=sg' => 'sg',
'lb=sp' => 'sp',
'lb=space' => 'sp',
'lb=surrogate' => 'sg',
'lb=sy' => 'sy',
'lb=unknown' => 'xx',
'lb=wj' => 'wj',
'lb=wordjoiner' => 'wj',
'lb=xx' => 'xx',
'lb=zw' => 'zw',
'lb=zwj' => 'zwj',
'lb=zwspace' => 'zw',
'loe=f' => 'n',
'loe=false' => 'n',
'loe=n' => 'n',
'loe=no' => 'n',
'loe=t' => 'y',
'loe=true' => 'y',
'loe=y' => 'y',
'loe=yes' => 'y',
'lower=f' => 'n',
'lower=false' => 'n',
'lower=n' => 'n',
'lower=no' => 'n',
'lower=t' => 'y',
'lower=true' => 'y',
'lower=y' => 'y',
'lower=yes' => 'y',
'math=f' => 'n',
'math=false' => 'n',
'math=n' => 'n',
'math=no' => 'n',
'math=t' => 'y',
'math=true' => 'y',
'math=y' => 'y',
'math=yes' => 'y',
'nchar=f' => 'n',
'nchar=false' => 'n',
'nchar=n' => 'n',
'nchar=no' => 'n',
'nchar=t' => 'y',
'nchar=true' => 'y',
'nchar=y' => 'y',
'nchar=yes' => 'y',
'nfcqc=m' => 'm',
'nfcqc=maybe' => 'm',
'nfcqc=n' => 'n',
'nfcqc=no' => 'n',
'nfcqc=y' => 'y',
'nfcqc=yes' => 'y',
'nfdqc=n' => 'n',
'nfdqc=no' => 'n',
'nfdqc=y' => 'y',
'nfdqc=yes' => 'y',
'nfkcqc=m' => 'm',
'nfkcqc=maybe' => 'm',
'nfkcqc=n' => 'n',
'nfkcqc=no' => 'n',
'nfkcqc=y' => 'y',
'nfkcqc=yes' => 'y',
'nfkdqc=n' => 'n',
'nfkdqc=no' => 'n',
'nfkdqc=y' => 'y',
'nfkdqc=yes' => 'y',
'nt=de' => 'de',
'nt=decimal' => 'de',
'nt=di' => 'di',
'nt=digit' => 'di',
'nt=none' => 'none',
'nt=nu' => 'nu',
'nt=numeric' => 'nu',
'nv=-1/2' => '-1/2',
'nv=0' => 0,
'nv=1' => 1,
'nv=1/10' => '1/10',
'nv=1/12' => '1/12',
'nv=1/16' => '1/16',
'nv=1/160' => '1/160',
'nv=1/2' => '1/2',
'nv=1/20' => '1/20',
'nv=1/3' => '1/3',
'nv=1/32' => '1/32',
'nv=1/320' => '1/320',
'nv=1/4' => '1/4',
'nv=1/40' => '1/40',
'nv=1/5' => '1/5',
'nv=1/6' => '1/6',
'nv=1/64' => '1/64',
'nv=1/7' => '1/7',
'nv=1/8' => '1/8',
'nv=1/80' => '1/80',
'nv=1/9' => '1/9',
'nv=10' => 10,
'nv=100' => 100,
'nv=1000' => 1000,
'nv=10000' => 10000,
'nv=100000' => 100000,
'nv=1000000' => 1000000,
'nv=10000000' => 10000000,
'nv=100000000' => 100000000,
'nv=10000000000' => 10000000000,
'nv=1000000000000' => 1000000000000,
'nv=11' => 11,
'nv=11/12' => '11/12',
'nv=11/2' => '11/2',
'nv=12' => 12,
'nv=13' => 13,
'nv=13/2' => '13/2',
'nv=14' => 14,
'nv=15' => 15,
'nv=15/2' => '15/2',
'nv=16' => 16,
'nv=17' => 17,
'nv=17/2' => '17/2',
'nv=18' => 18,
'nv=19' => 19,
'nv=2' => 2,
'nv=2/3' => '2/3',
'nv=2/5' => '2/5',
'nv=20' => 20,
'nv=200' => 200,
'nv=2000' => 2000,
'nv=20000' => 20000,
'nv=200000' => 200000,
'nv=20000000' => 20000000,
'nv=21' => 21,
'nv=216000' => 216000,
'nv=22' => 22,
'nv=23' => 23,
'nv=24' => 24,
'nv=25' => 25,
'nv=26' => 26,
'nv=27' => 27,
'nv=28' => 28,
'nv=29' => 29,
'nv=3' => 3,
'nv=3/16' => '3/16',
'nv=3/2' => '3/2',
'nv=3/20' => '3/20',
'nv=3/4' => '3/4',
'nv=3/5' => '3/5',
'nv=3/64' => '3/64',
'nv=3/8' => '3/8',
'nv=3/80' => '3/80',
'nv=30' => 30,
'nv=300' => 300,
'nv=3000' => 3000,
'nv=30000' => 30000,
'nv=300000' => 300000,
'nv=31' => 31,
'nv=32' => 32,
'nv=33' => 33,
'nv=34' => 34,
'nv=35' => 35,
'nv=36' => 36,
'nv=37' => 37,
'nv=38' => 38,
'nv=39' => 39,
'nv=4' => 4,
'nv=4/5' => '4/5',
'nv=40' => 40,
'nv=400' => 400,
'nv=4000' => 4000,
'nv=40000' => 40000,
'nv=400000' => 400000,
'nv=41' => 41,
'nv=42' => 42,
'nv=43' => 43,
'nv=432000' => 432000,
'nv=44' => 44,
'nv=45' => 45,
'nv=46' => 46,
'nv=47' => 47,
'nv=48' => 48,
'nv=49' => 49,
'nv=5' => 5,
'nv=5/12' => '5/12',
'nv=5/2' => '5/2',
'nv=5/6' => '5/6',
'nv=5/8' => '5/8',
'nv=50' => 50,
'nv=500' => 500,
'nv=5000' => 5000,
'nv=50000' => 50000,
'nv=500000' => 500000,
'nv=6' => 6,
'nv=60' => 60,
'nv=600' => 600,
'nv=6000' => 6000,
'nv=60000' => 60000,
'nv=600000' => 600000,
'nv=7' => 7,
'nv=7/12' => '7/12',
'nv=7/2' => '7/2',
'nv=7/8' => '7/8',
'nv=70' => 70,
'nv=700' => 700,
'nv=7000' => 7000,
'nv=70000' => 70000,
'nv=700000' => 700000,
'nv=8' => 8,
'nv=80' => 80,
'nv=800' => 800,
'nv=8000' => 8000,
'nv=80000' => 80000,
'nv=800000' => 800000,
'nv=9' => 9,
'nv=9/2' => '9/2',
'nv=90' => 90,
'nv=900' => 900,
'nv=9000' => 9000,
'nv=90000' => 90000,
'nv=900000' => 900000,
'nv=nan' => 'nan',
'patsyn=f' => 'n',
'patsyn=false' => 'n',
'patsyn=n' => 'n',
'patsyn=no' => 'n',
'patsyn=t' => 'y',
'patsyn=true' => 'y',
'patsyn=y' => 'y',
'patsyn=yes' => 'y',
'patws=f' => 'n',
'patws=false' => 'n',
'patws=n' => 'n',
'patws=no' => 'n',
'patws=t' => 'y',
'patws=true' => 'y',
'patws=y' => 'y',
'patws=yes' => 'y',
'pcm=f' => 'n',
'pcm=false' => 'n',
'pcm=n' => 'n',
'pcm=no' => 'n',
'pcm=t' => 'y',
'pcm=true' => 'y',
'pcm=y' => 'y',
'pcm=yes' => 'y',
'qmark=f' => 'n',
'qmark=false' => 'n',
'qmark=n' => 'n',
'qmark=no' => 'n',
'qmark=t' => 'y',
'qmark=true' => 'y',
'qmark=y' => 'y',
'qmark=yes' => 'y',
'radical=f' => 'n',
'radical=false' => 'n',
'radical=n' => 'n',
'radical=no' => 'n',
'radical=t' => 'y',
'radical=true' => 'y',
'radical=y' => 'y',
'radical=yes' => 'y',
'ri=f' => 'n',
'ri=false' => 'n',
'ri=n' => 'n',
'ri=no' => 'n',
'ri=t' => 'y',
'ri=true' => 'y',
'ri=y' => 'y',
'ri=yes' => 'y',
'sb=at' => 'at',
'sb=aterm' => 'at',
'sb=cl' => 'cl',
'sb=close' => 'cl',
'sb=cr' => 'cr',
'sb=ex' => 'ex',
'sb=extend' => 'ex',
'sb=fo' => 'fo',
'sb=format' => 'fo',
'sb=le' => 'le',
'sb=lf' => 'lf',
'sb=lo' => 'lo',
'sb=lower' => 'lo',
'sb=nu' => 'nu',
'sb=numeric' => 'nu',
'sb=oletter' => 'le',
'sb=other' => 'xx',
'sb=sc' => 'sc',
'sb=scontinue' => 'sc',
'sb=se' => 'se',
'sb=sep' => 'se',
'sb=sp' => 'sp',
'sb=st' => 'st',
'sb=sterm' => 'st',
'sb=up' => 'up',
'sb=upper' => 'up',
'sb=xx' => 'xx',
'sc=adlam' => 'adlm',
'sc=adlm' => 'adlm',
'sc=aghb' => 'aghb',
'sc=ahom' => 'ahom',
'sc=anatolianhieroglyphs' => 'hluw',
'sc=arab' => 'arab',
'sc=arabic' => 'arab',
'sc=armenian' => 'armn',
'sc=armi' => 'armi',
'sc=armn' => 'armn',
'sc=avestan' => 'avst',
'sc=avst' => 'avst',
'sc=bali' => 'bali',
'sc=balinese' => 'bali',
'sc=bamu' => 'bamu',
'sc=bamum' => 'bamu',
'sc=bass' => 'bass',
'sc=bassavah' => 'bass',
'sc=batak' => 'batk',
'sc=batk' => 'batk',
'sc=beng' => 'beng',
'sc=bengali' => 'beng',
'sc=bhaiksuki' => 'bhks',
'sc=bhks' => 'bhks',
'sc=bopo' => 'bopo',
'sc=bopomofo' => 'bopo',
'sc=brah' => 'brah',
'sc=brahmi' => 'brah',
'sc=brai' => 'brai',
'sc=braille' => 'brai',
'sc=bugi' => 'bugi',
'sc=buginese' => 'bugi',
'sc=buhd' => 'buhd',
'sc=buhid' => 'buhd',
'sc=cakm' => 'cakm',
'sc=canadianaboriginal' => 'cans',
'sc=cans' => 'cans',
'sc=cari' => 'cari',
'sc=carian' => 'cari',
'sc=caucasianalbanian' => 'aghb',
'sc=chakma' => 'cakm',
'sc=cham' => 'cham',
'sc=cher' => 'cher',
'sc=cherokee' => 'cher',
'sc=chorasmian' => 'chrs',
'sc=chrs' => 'chrs',
'sc=common' => 'zyyy',
'sc=copt' => 'copt',
'sc=coptic' => 'copt',
'sc=cpmn' => 'cpmn',
'sc=cprt' => 'cprt',
'sc=cuneiform' => 'xsux',
'sc=cypriot' => 'cprt',
'sc=cyprominoan' => 'cpmn',
'sc=cyrillic' => 'cyrl',
'sc=cyrl' => 'cyrl',
'sc=deseret' => 'dsrt',
'sc=deva' => 'deva',
'sc=devanagari' => 'deva',
'sc=diak' => 'diak',
'sc=divesakuru' => 'diak',
'sc=dogr' => 'dogr',
'sc=dogra' => 'dogr',
'sc=dsrt' => 'dsrt',
'sc=dupl' => 'dupl',
'sc=duployan' => 'dupl',
'sc=egyp' => 'egyp',
'sc=egyptianhieroglyphs' => 'egyp',
'sc=elba' => 'elba',
'sc=elbasan' => 'elba',
'sc=elym' => 'elym',
'sc=elymaic' => 'elym',
'sc=ethi' => 'ethi',
'sc=ethiopic' => 'ethi',
'sc=geor' => 'geor',
'sc=georgian' => 'geor',
'sc=glag' => 'glag',
'sc=glagolitic' => 'glag',
'sc=gong' => 'gong',
'sc=gonm' => 'gonm',
'sc=goth' => 'goth',
'sc=gothic' => 'goth',
'sc=gran' => 'gran',
'sc=grantha' => 'gran',
'sc=greek' => 'grek',
'sc=grek' => 'grek',
'sc=gujarati' => 'gujr',
'sc=gujr' => 'gujr',
'sc=gunjalagondi' => 'gong',
'sc=gurmukhi' => 'guru',
'sc=guru' => 'guru',
'sc=han' => 'hani',
'sc=hang' => 'hang',
'sc=hangul' => 'hang',
'sc=hani' => 'hani',
'sc=hanifirohingya' => 'rohg',
'sc=hano' => 'hano',
'sc=hanunoo' => 'hano',
'sc=hatr' => 'hatr',
'sc=hatran' => 'hatr',
'sc=hebr' => 'hebr',
'sc=hebrew' => 'hebr',
'sc=hira' => 'hira',
'sc=hiragana' => 'hira',
'sc=hluw' => 'hluw',
'sc=hmng' => 'hmng',
'sc=hmnp' => 'hmnp',
'sc=hung' => 'hung',
'sc=imperialaramaic' => 'armi',
'sc=inherited' => 'zinh',
'sc=inscriptionalpahlavi' => 'phli',
'sc=inscriptionalparthian' => 'prti',
'sc=ital' => 'ital',
'sc=java' => 'java',
'sc=javanese' => 'java',
'sc=kaithi' => 'kthi',
'sc=kali' => 'kali',
'sc=kana' => 'kana',
'sc=kannada' => 'knda',
'sc=katakana' => 'kana',
'sc=kawi' => 'kawi',
'sc=kayahli' => 'kali',
'sc=khar' => 'khar',
'sc=kharoshthi' => 'khar',
'sc=khitansmallscript' => 'kits',
'sc=khmer' => 'khmr',
'sc=khmr' => 'khmr',
'sc=khoj' => 'khoj',
'sc=khojki' => 'khoj',
'sc=khudawadi' => 'sind',
'sc=kits' => 'kits',
'sc=knda' => 'knda',
'sc=kthi' => 'kthi',
'sc=lana' => 'lana',
'sc=lao' => 'laoo',
'sc=laoo' => 'laoo',
'sc=latin' => 'latn',
'sc=latn' => 'latn',
'sc=lepc' => 'lepc',
'sc=lepcha' => 'lepc',
'sc=limb' => 'limb',
'sc=limbu' => 'limb',
'sc=lina' => 'lina',
'sc=linb' => 'linb',
'sc=lineara' => 'lina',
'sc=linearb' => 'linb',
'sc=lisu' => 'lisu',
'sc=lyci' => 'lyci',
'sc=lycian' => 'lyci',
'sc=lydi' => 'lydi',
'sc=lydian' => 'lydi',
'sc=mahajani' => 'mahj',
'sc=mahj' => 'mahj',
'sc=maka' => 'maka',
'sc=makasar' => 'maka',
'sc=malayalam' => 'mlym',
'sc=mand' => 'mand',
'sc=mandaic' => 'mand',
'sc=mani' => 'mani',
'sc=manichaean' => 'mani',
'sc=marc' => 'marc',
'sc=marchen' => 'marc',
'sc=masaramgondi' => 'gonm',
'sc=medefaidrin' => 'medf',
'sc=medf' => 'medf',
'sc=meeteimayek' => 'mtei',
'sc=mend' => 'mend',
'sc=mendekikakui' => 'mend',
'sc=merc' => 'merc',
'sc=mero' => 'mero',
'sc=meroiticcursive' => 'merc',
'sc=meroitichieroglyphs' => 'mero',
'sc=miao' => 'plrd',
'sc=mlym' => 'mlym',
'sc=modi' => 'modi',
'sc=mong' => 'mong',
'sc=mongolian' => 'mong',
'sc=mro' => 'mroo',
'sc=mroo' => 'mroo',
'sc=mtei' => 'mtei',
'sc=mult' => 'mult',
'sc=multani' => 'mult',
'sc=myanmar' => 'mymr',
'sc=mymr' => 'mymr',
'sc=nabataean' => 'nbat',
'sc=nagm' => 'nagm',
'sc=nagmundari' => 'nagm',
'sc=nand' => 'nand',
'sc=nandinagari' => 'nand',
'sc=narb' => 'narb',
'sc=nbat' => 'nbat',
'sc=newa' => 'newa',
'sc=newtailue' => 'talu',
'sc=nko' => 'nkoo',
'sc=nkoo' => 'nkoo',
'sc=nshu' => 'nshu',
'sc=nushu' => 'nshu',
'sc=nyiakengpuachuehmong' => 'hmnp',
'sc=ogam' => 'ogam',
'sc=ogham' => 'ogam',
'sc=olchiki' => 'olck',
'sc=olck' => 'olck',
'sc=oldhungarian' => 'hung',
'sc=olditalic' => 'ital',
'sc=oldnortharabian' => 'narb',
'sc=oldpermic' => 'perm',
'sc=oldpersian' => 'xpeo',
'sc=oldsogdian' => 'sogo',
'sc=oldsoutharabian' => 'sarb',
'sc=oldturkic' => 'orkh',
'sc=olduyghur' => 'ougr',
'sc=oriya' => 'orya',
'sc=orkh' => 'orkh',
'sc=orya' => 'orya',
'sc=osage' => 'osge',
'sc=osge' => 'osge',
'sc=osma' => 'osma',
'sc=osmanya' => 'osma',
'sc=ougr' => 'ougr',
'sc=pahawhhmong' => 'hmng',
'sc=palm' => 'palm',
'sc=palmyrene' => 'palm',
'sc=pauc' => 'pauc',
'sc=paucinhau' => 'pauc',
'sc=perm' => 'perm',
'sc=phag' => 'phag',
'sc=phagspa' => 'phag',
'sc=phli' => 'phli',
'sc=phlp' => 'phlp',
'sc=phnx' => 'phnx',
'sc=phoenician' => 'phnx',
'sc=plrd' => 'plrd',
'sc=prti' => 'prti',
'sc=psalterpahlavi' => 'phlp',
'sc=qaac' => 'copt',
'sc=qaai' => 'zinh',
'sc=rejang' => 'rjng',
'sc=rjng' => 'rjng',
'sc=rohg' => 'rohg',
'sc=runic' => 'runr',
'sc=runr' => 'runr',
'sc=samaritan' => 'samr',
'sc=samr' => 'samr',
'sc=sarb' => 'sarb',
'sc=saur' => 'saur',
'sc=saurashtra' => 'saur',
'sc=sgnw' => 'sgnw',
'sc=sharada' => 'shrd',
'sc=shavian' => 'shaw',
'sc=shaw' => 'shaw',
'sc=shrd' => 'shrd',
'sc=sidd' => 'sidd',
'sc=siddham' => 'sidd',
'sc=signwriting' => 'sgnw',
'sc=sind' => 'sind',
'sc=sinh' => 'sinh',
'sc=sinhala' => 'sinh',
'sc=sogd' => 'sogd',
'sc=sogdian' => 'sogd',
'sc=sogo' => 'sogo',
'sc=sora' => 'sora',
'sc=sorasompeng' => 'sora',
'sc=soyo' => 'soyo',
'sc=soyombo' => 'soyo',
'sc=sund' => 'sund',
'sc=sundanese' => 'sund',
'sc=sylo' => 'sylo',
'sc=sylotinagri' => 'sylo',
'sc=syrc' => 'syrc',
'sc=syriac' => 'syrc',
'sc=tagalog' => 'tglg',
'sc=tagb' => 'tagb',
'sc=tagbanwa' => 'tagb',
'sc=taile' => 'tale',
'sc=taitham' => 'lana',
'sc=taiviet' => 'tavt',
'sc=takr' => 'takr',
'sc=takri' => 'takr',
'sc=tale' => 'tale',
'sc=talu' => 'talu',
'sc=tamil' => 'taml',
'sc=taml' => 'taml',
'sc=tang' => 'tang',
'sc=tangsa' => 'tnsa',
'sc=tangut' => 'tang',
'sc=tavt' => 'tavt',
'sc=telu' => 'telu',
'sc=telugu' => 'telu',
'sc=tfng' => 'tfng',
'sc=tglg' => 'tglg',
'sc=thaa' => 'thaa',
'sc=thaana' => 'thaa',
'sc=thai' => 'thai',
'sc=tibetan' => 'tibt',
'sc=tibt' => 'tibt',
'sc=tifinagh' => 'tfng',
'sc=tirh' => 'tirh',
'sc=tirhuta' => 'tirh',
'sc=tnsa' => 'tnsa',
'sc=toto' => 'toto',
'sc=ugar' => 'ugar',
'sc=ugaritic' => 'ugar',
'sc=unknown' => 'zzzz',
'sc=vai' => 'vaii',
'sc=vaii' => 'vaii',
'sc=vith' => 'vith',
'sc=vithkuqi' => 'vith',
'sc=wancho' => 'wcho',
'sc=wara' => 'wara',
'sc=warangciti' => 'wara',
'sc=wcho' => 'wcho',
'sc=xpeo' => 'xpeo',
'sc=xsux' => 'xsux',
'sc=yezi' => 'yezi',
'sc=yezidi' => 'yezi',
'sc=yi' => 'yiii',
'sc=yiii' => 'yiii',
'sc=zanabazarsquare' => 'zanb',
'sc=zanb' => 'zanb',
'sc=zinh' => 'zinh',
'sc=zyyy' => 'zyyy',
'sc=zzzz' => 'zzzz',
'scx=adlam' => 'adlm',
'scx=adlm' => 'adlm',
'scx=aghb' => 'aghb',
'scx=ahom' => 'ahom',
'scx=anatolianhieroglyphs' => 'hluw',
'scx=arab' => 'arab',
'scx=arabic' => 'arab',
'scx=armenian' => 'armn',
'scx=armi' => 'armi',
'scx=armn' => 'armn',
'scx=avestan' => 'avst',
'scx=avst' => 'avst',
'scx=bali' => 'bali',
'scx=balinese' => 'bali',
'scx=bamu' => 'bamu',
'scx=bamum' => 'bamu',
'scx=bass' => 'bass',
'scx=bassavah' => 'bass',
'scx=batak' => 'batk',
'scx=batk' => 'batk',
'scx=beng' => 'beng',
'scx=bengali' => 'beng',
'scx=bhaiksuki' => 'bhks',
'scx=bhks' => 'bhks',
'scx=bopo' => 'bopo',
'scx=bopomofo' => 'bopo',
'scx=brah' => 'brah',
'scx=brahmi' => 'brah',
'scx=brai' => 'brai',
'scx=braille' => 'brai',
'scx=bugi' => 'bugi',
'scx=buginese' => 'bugi',
'scx=buhd' => 'buhd',
'scx=buhid' => 'buhd',
'scx=cakm' => 'cakm',
'scx=canadianaboriginal' => 'cans',
'scx=cans' => 'cans',
'scx=cari' => 'cari',
'scx=carian' => 'cari',
'scx=caucasianalbanian' => 'aghb',
'scx=chakma' => 'cakm',
'scx=cham' => 'cham',
'scx=cher' => 'cher',
'scx=cherokee' => 'cher',
'scx=chorasmian' => 'chrs',
'scx=chrs' => 'chrs',
'scx=common' => 'zyyy',
'scx=copt' => 'copt',
'scx=coptic' => 'copt',
'scx=cpmn' => 'cpmn',
'scx=cprt' => 'cprt',
'scx=cuneiform' => 'xsux',
'scx=cypriot' => 'cprt',
'scx=cyprominoan' => 'cpmn',
'scx=cyrillic' => 'cyrl',
'scx=cyrl' => 'cyrl',
'scx=deseret' => 'dsrt',
'scx=deva' => 'deva',
'scx=devanagari' => 'deva',
'scx=diak' => 'diak',
'scx=divesakuru' => 'diak',
'scx=dogr' => 'dogr',
'scx=dogra' => 'dogr',
'scx=dsrt' => 'dsrt',
'scx=dupl' => 'dupl',
'scx=duployan' => 'dupl',
'scx=egyp' => 'egyp',
'scx=egyptianhieroglyphs' => 'egyp',
'scx=elba' => 'elba',
'scx=elbasan' => 'elba',
'scx=elym' => 'elym',
'scx=elymaic' => 'elym',
'scx=ethi' => 'ethi',
'scx=ethiopic' => 'ethi',
'scx=geor' => 'geor',
'scx=georgian' => 'geor',
'scx=glag' => 'glag',
'scx=glagolitic' => 'glag',
'scx=gong' => 'gong',
'scx=gonm' => 'gonm',
'scx=goth' => 'goth',
'scx=gothic' => 'goth',
'scx=gran' => 'gran',
'scx=grantha' => 'gran',
'scx=greek' => 'grek',
'scx=grek' => 'grek',
'scx=gujarati' => 'gujr',
'scx=gujr' => 'gujr',
'scx=gunjalagondi' => 'gong',
'scx=gurmukhi' => 'guru',
'scx=guru' => 'guru',
'scx=han' => 'hani',
'scx=hang' => 'hang',
'scx=hangul' => 'hang',
'scx=hani' => 'hani',
'scx=hanifirohingya' => 'rohg',
'scx=hano' => 'hano',
'scx=hanunoo' => 'hano',
'scx=hatr' => 'hatr',
'scx=hatran' => 'hatr',
'scx=hebr' => 'hebr',
'scx=hebrew' => 'hebr',
'scx=hira' => 'hira',
'scx=hiragana' => 'hira',
'scx=hluw' => 'hluw',
'scx=hmng' => 'hmng',
'scx=hmnp' => 'hmnp',
'scx=hung' => 'hung',
'scx=imperialaramaic' => 'armi',
'scx=inherited' => 'zinh',
'scx=inscriptionalpahlavi' => 'phli',
'scx=inscriptionalparthian' => 'prti',
'scx=ital' => 'ital',
'scx=java' => 'java',
'scx=javanese' => 'java',
'scx=kaithi' => 'kthi',
'scx=kali' => 'kali',
'scx=kana' => 'kana',
'scx=kannada' => 'knda',
'scx=katakana' => 'kana',
'scx=kawi' => 'kawi',
'scx=kayahli' => 'kali',
'scx=khar' => 'khar',
'scx=kharoshthi' => 'khar',
'scx=khitansmallscript' => 'kits',
'scx=khmer' => 'khmr',
'scx=khmr' => 'khmr',
'scx=khoj' => 'khoj',
'scx=khojki' => 'khoj',
'scx=khudawadi' => 'sind',
'scx=kits' => 'kits',
'scx=knda' => 'knda',
'scx=kthi' => 'kthi',
'scx=lana' => 'lana',
'scx=lao' => 'laoo',
'scx=laoo' => 'laoo',
'scx=latin' => 'latn',
'scx=latn' => 'latn',
'scx=lepc' => 'lepc',
'scx=lepcha' => 'lepc',
'scx=limb' => 'limb',
'scx=limbu' => 'limb',
'scx=lina' => 'lina',
'scx=linb' => 'linb',
'scx=lineara' => 'lina',
'scx=linearb' => 'linb',
'scx=lisu' => 'lisu',
'scx=lyci' => 'lyci',
'scx=lycian' => 'lyci',
'scx=lydi' => 'lydi',
'scx=lydian' => 'lydi',
'scx=mahajani' => 'mahj',
'scx=mahj' => 'mahj',
'scx=maka' => 'maka',
'scx=makasar' => 'maka',
'scx=malayalam' => 'mlym',
'scx=mand' => 'mand',
'scx=mandaic' => 'mand',
'scx=mani' => 'mani',
'scx=manichaean' => 'mani',
'scx=marc' => 'marc',
'scx=marchen' => 'marc',
'scx=masaramgondi' => 'gonm',
'scx=medefaidrin' => 'medf',
'scx=medf' => 'medf',
'scx=meeteimayek' => 'mtei',
'scx=mend' => 'mend',
'scx=mendekikakui' => 'mend',
'scx=merc' => 'merc',
'scx=mero' => 'mero',
'scx=meroiticcursive' => 'merc',
'scx=meroitichieroglyphs' => 'mero',
'scx=miao' => 'plrd',
'scx=mlym' => 'mlym',
'scx=modi' => 'modi',
'scx=mong' => 'mong',
'scx=mongolian' => 'mong',
'scx=mro' => 'mroo',
'scx=mroo' => 'mroo',
'scx=mtei' => 'mtei',
'scx=mult' => 'mult',
'scx=multani' => 'mult',
'scx=myanmar' => 'mymr',
'scx=mymr' => 'mymr',
'scx=nabataean' => 'nbat',
'scx=nagm' => 'nagm',
'scx=nagmundari' => 'nagm',
'scx=nand' => 'nand',
'scx=nandinagari' => 'nand',
'scx=narb' => 'narb',
'scx=nbat' => 'nbat',
'scx=newa' => 'newa',
'scx=newtailue' => 'talu',
'scx=nko' => 'nkoo',
'scx=nkoo' => 'nkoo',
'scx=nshu' => 'nshu',
'scx=nushu' => 'nshu',
'scx=nyiakengpuachuehmong' => 'hmnp',
'scx=ogam' => 'ogam',
'scx=ogham' => 'ogam',
'scx=olchiki' => 'olck',
'scx=olck' => 'olck',
'scx=oldhungarian' => 'hung',
'scx=olditalic' => 'ital',
'scx=oldnortharabian' => 'narb',
'scx=oldpermic' => 'perm',
'scx=oldpersian' => 'xpeo',
'scx=oldsogdian' => 'sogo',
'scx=oldsoutharabian' => 'sarb',
'scx=oldturkic' => 'orkh',
'scx=olduyghur' => 'ougr',
'scx=oriya' => 'orya',
'scx=orkh' => 'orkh',
'scx=orya' => 'orya',
'scx=osage' => 'osge',
'scx=osge' => 'osge',
'scx=osma' => 'osma',
'scx=osmanya' => 'osma',
'scx=ougr' => 'ougr',
'scx=pahawhhmong' => 'hmng',
'scx=palm' => 'palm',
'scx=palmyrene' => 'palm',
'scx=pauc' => 'pauc',
'scx=paucinhau' => 'pauc',
'scx=perm' => 'perm',
'scx=phag' => 'phag',
'scx=phagspa' => 'phag',
'scx=phli' => 'phli',
'scx=phlp' => 'phlp',
'scx=phnx' => 'phnx',
'scx=phoenician' => 'phnx',
'scx=plrd' => 'plrd',
'scx=prti' => 'prti',
'scx=psalterpahlavi' => 'phlp',
'scx=qaac' => 'copt',
'scx=qaai' => 'zinh',
'scx=rejang' => 'rjng',
'scx=rjng' => 'rjng',
'scx=rohg' => 'rohg',
'scx=runic' => 'runr',
'scx=runr' => 'runr',
'scx=samaritan' => 'samr',
'scx=samr' => 'samr',
'scx=sarb' => 'sarb',
'scx=saur' => 'saur',
'scx=saurashtra' => 'saur',
'scx=sgnw' => 'sgnw',
'scx=sharada' => 'shrd',
'scx=shavian' => 'shaw',
'scx=shaw' => 'shaw',
'scx=shrd' => 'shrd',
'scx=sidd' => 'sidd',
'scx=siddham' => 'sidd',
'scx=signwriting' => 'sgnw',
'scx=sind' => 'sind',
'scx=sinh' => 'sinh',
'scx=sinhala' => 'sinh',
'scx=sogd' => 'sogd',
'scx=sogdian' => 'sogd',
'scx=sogo' => 'sogo',
'scx=sora' => 'sora',
'scx=sorasompeng' => 'sora',
'scx=soyo' => 'soyo',
'scx=soyombo' => 'soyo',
'scx=sund' => 'sund',
'scx=sundanese' => 'sund',
'scx=sylo' => 'sylo',
'scx=sylotinagri' => 'sylo',
'scx=syrc' => 'syrc',
'scx=syriac' => 'syrc',
'scx=tagalog' => 'tglg',
'scx=tagb' => 'tagb',
'scx=tagbanwa' => 'tagb',
'scx=taile' => 'tale',
'scx=taitham' => 'lana',
'scx=taiviet' => 'tavt',
'scx=takr' => 'takr',
'scx=takri' => 'takr',
'scx=tale' => 'tale',
'scx=talu' => 'talu',
'scx=tamil' => 'taml',
'scx=taml' => 'taml',
'scx=tang' => 'tang',
'scx=tangsa' => 'tnsa',
'scx=tangut' => 'tang',
'scx=tavt' => 'tavt',
'scx=telu' => 'telu',
'scx=telugu' => 'telu',
'scx=tfng' => 'tfng',
'scx=tglg' => 'tglg',
'scx=thaa' => 'thaa',
'scx=thaana' => 'thaa',
'scx=thai' => 'thai',
'scx=tibetan' => 'tibt',
'scx=tibt' => 'tibt',
'scx=tifinagh' => 'tfng',
'scx=tirh' => 'tirh',
'scx=tirhuta' => 'tirh',
'scx=tnsa' => 'tnsa',
'scx=toto' => 'toto',
'scx=ugar' => 'ugar',
'scx=ugaritic' => 'ugar',
'scx=unknown' => 'zzzz',
'scx=vai' => 'vaii',
'scx=vaii' => 'vaii',
'scx=vith' => 'vith',
'scx=vithkuqi' => 'vith',
'scx=wancho' => 'wcho',
'scx=wara' => 'wara',
'scx=warangciti' => 'wara',
'scx=wcho' => 'wcho',
'scx=xpeo' => 'xpeo',
'scx=xsux' => 'xsux',
'scx=yezi' => 'yezi',
'scx=yezidi' => 'yezi',
'scx=yi' => 'yiii',
'scx=yiii' => 'yiii',
'scx=zanabazarsquare' => 'zanb',
'scx=zanb' => 'zanb',
'scx=zinh' => 'zinh',
'scx=zyyy' => 'zyyy',
'scx=zzzz' => 'zzzz',
'sd=f' => 'n',
'sd=false' => 'n',
'sd=n' => 'n',
'sd=no' => 'n',
'sd=t' => 'y',
'sd=true' => 'y',
'sd=y' => 'y',
'sd=yes' => 'y',
'sterm=f' => 'n',
'sterm=false' => 'n',
'sterm=n' => 'n',
'sterm=no' => 'n',
'sterm=t' => 'y',
'sterm=true' => 'y',
'sterm=y' => 'y',
'sterm=yes' => 'y',
'term=f' => 'n',
'term=false' => 'n',
'term=n' => 'n',
'term=no' => 'n',
'term=t' => 'y',
'term=true' => 'y',
'term=y' => 'y',
'term=yes' => 'y',
'uideo=f' => 'n',
'uideo=false' => 'n',
'uideo=n' => 'n',
'uideo=no' => 'n',
'uideo=t' => 'y',
'uideo=true' => 'y',
'uideo=y' => 'y',
'uideo=yes' => 'y',
'upper=f' => 'n',
'upper=false' => 'n',
'upper=n' => 'n',
'upper=no' => 'n',
'upper=t' => 'y',
'upper=true' => 'y',
'upper=y' => 'y',
'upper=yes' => 'y',
'vo=r' => 'r',
'vo=rotated' => 'r',
'vo=tr' => 'tr',
'vo=transformedrotated' => 'tr',
'vo=transformedupright' => 'tu',
'vo=tu' => 'tu',
'vo=u' => 'u',
'vo=upright' => 'u',
'vs=f' => 'n',
'vs=false' => 'n',
'vs=n' => 'n',
'vs=no' => 'n',
'vs=t' => 'y',
'vs=true' => 'y',
'vs=y' => 'y',
'vs=yes' => 'y',
'wb=aletter' => 'le',
'wb=cr' => 'cr',
'wb=doublequote' => 'dq',
'wb=dq' => 'dq',
'wb=eb' => 'eb',
'wb=ebase' => 'eb',
'wb=ebasegaz' => 'ebg',
'wb=ebg' => 'ebg',
'wb=em' => 'em',
'wb=emodifier' => 'em',
'wb=ex' => 'ex',
'wb=extend' => 'extend',
'wb=extendnumlet' => 'ex',
'wb=fo' => 'fo',
'wb=format' => 'fo',
'wb=gaz' => 'gaz',
'wb=glueafterzwj' => 'gaz',
'wb=hebrewletter' => 'hl',
'wb=hl' => 'hl',
'wb=ka' => 'ka',
'wb=katakana' => 'ka',
'wb=le' => 'le',
'wb=lf' => 'lf',
'wb=mb' => 'mb',
'wb=midletter' => 'ml',
'wb=midnum' => 'mn',
'wb=midnumlet' => 'mb',
'wb=ml' => 'ml',
'wb=mn' => 'mn',
'wb=newline' => 'nl',
'wb=nl' => 'nl',
'wb=nu' => 'nu',
'wb=numeric' => 'nu',
'wb=other' => 'xx',
'wb=regionalindicator' => 'ri',
'wb=ri' => 'ri',
'wb=singlequote' => 'sq',
'wb=sq' => 'sq',
'wb=wsegspace' => 'wsegspace',
'wb=xx' => 'xx',
'wb=zwj' => 'zwj',
'wspace=f' => 'n',
'wspace=false' => 'n',
'wspace=n' => 'n',
'wspace=no' => 'n',
'wspace=t' => 'y',
'wspace=true' => 'y',
'wspace=y' => 'y',
'wspace=yes' => 'y',
'xidc=f' => 'n',
'xidc=false' => 'n',
'xidc=n' => 'n',
'xidc=no' => 'n',
'xidc=t' => 'y',
'xidc=true' => 'y',
'xidc=y' => 'y',
'xidc=yes' => 'y',
'xids=f' => 'n',
'xids=false' => 'n',
'xids=n' => 'n',
'xids=no' => 'n',
'xids=t' => 'y',
'xids=true' => 'y',
'xids=y' => 'y',
'xids=yes' => 'y',
);

# String property loose names to standard loose name
%Unicode::UCD::string_property_loose_to_name = (
'_perlnamealias' => 'namealias',
'bidimirroringglyph' => 'bmg',
'bidipairedbracket' => 'bpb',
'bmg' => 'bmg',
'bpb' => 'bpb',
'casefolding' => 'cf',
'cf' => 'cf',
'decompositionmapping' => 'dm',
'dm' => 'dm',
'equideo' => 'equideo',
'equivalentunifiedideograph' => 'equideo',
'isc' => 'isc',
'isocomment' => 'isc',
'lc' => 'lc',
'lowercasemapping' => 'lc',
'na' => 'na',
'na1' => 'na1',
'name' => 'na',
'namealias' => 'namealias',
'nfkccasefold' => 'nfkccf',
'nfkccf' => 'nfkccf',
'perldecimaldigit' => 'perldecimaldigit',
'scf' => 'scf',
'sfc' => 'scf',
'simplecasefolding' => 'scf',
'simplelowercasemapping' => 'slc',
'simpletitlecasemapping' => 'stc',
'simpleuppercasemapping' => 'suc',
'slc' => 'slc',
'stc' => 'stc',
'suc' => 'suc',
'tc' => 'tc',
'titlecasemapping' => 'tc',
'uc' => 'uc',
'unicode1name' => 'na1',
'uppercasemapping' => 'uc',
);

# Keys are Perl extensions in loose form; values are each one's list of
# aliases
%Unicode::UCD::loose_perlprop_to_name = (
'all' => 
[
'All',
],
'alnum' => 
[
'Alnum',
'XPosixAlnum',
],
'any' => 
[
'Any',
'Unicode',
],
'ascii' => 
[
'ASCII',
],
'assigned' => 
[
'Assigned',
],
'blank' => 
[
'Blank',
'XPosixBlank',
'HorizSpace',
],
'cntrl' => 
[
'Cntrl',
'XPosixCntrl',
],
'digit' => 
[
'Digit',
'XPosixDigit',
],
'graph' => 
[
'Graph',
'XPosixGraph',
],
'horizspace' => 
[
'Blank',
'XPosixBlank',
'HorizSpace',
],
'perlspace' => 
[
'PosixSpace',
'PerlSpace',
],
'perlword' => 
[
'PosixWord',
'PerlWord',
],
'posixalnum' => 
[
'PosixAlnum',
],
'posixalpha' => 
[
'PosixAlpha',
],
'posixblank' => 
[
'PosixBlank',
],
'posixcntrl' => 
[
'PosixCntrl',
],
'posixdigit' => 
[
'PosixDigit',
],
'posixgraph' => 
[
'PosixGraph',
],
'posixlower' => 
[
'PosixLower',
],
'posixprint' => 
[
'PosixPrint',
],
'posixpunct' => 
[
'PosixPunct',
],
'posixspace' => 
[
'PosixSpace',
'PerlSpace',
],
'posixupper' => 
[
'PosixUpper',
],
'posixword' => 
[
'PosixWord',
'PerlWord',
],
'posixxdigit' => 
[
'PosixXDigit',
],
'print' => 
[
'Print',
'XPosixPrint',
],
'punct' => 
[
'Punct',
],
'spaceperl' => 
[
'XPosixSpace',
'XPerlSpace',
'SpacePerl',
],
'title' => 
[
'Title',
'Titlecase',
],
'titlecase' => 
[
'Title',
'Titlecase',
],
'unicode' => 
[
'Any',
'Unicode',
],
'vertspace' => 
[
'VertSpace',
],
'word' => 
[
'Word',
'XPosixWord',
],
'xdigit' => 
[
'XDigit',
'XPosixXDigit',
],
'xperlspace' => 
[
'XPosixSpace',
'XPerlSpace',
'SpacePerl',
],
'xposixalnum' => 
[
'Alnum',
'XPosixAlnum',
],
'xposixalpha' => 
[
'XPosixAlpha',
],
'xposixblank' => 
[
'Blank',
'XPosixBlank',
'HorizSpace',
],
'xposixcntrl' => 
[
'Cntrl',
'XPosixCntrl',
],
'xposixdigit' => 
[
'Digit',
'XPosixDigit',
],
'xposixgraph' => 
[
'Graph',
'XPosixGraph',
],
'xposixlower' => 
[
'XPosixLower',
],
'xposixprint' => 
[
'Print',
'XPosixPrint',
],
'xposixpunct' => 
[
'XPosixPunct',
],
'xposixspace' => 
[
'XPosixSpace',
'XPerlSpace',
'SpacePerl',
],
'xposixupper' => 
[
'XPosixUpper',
],
'xposixword' => 
[
'Word',
'XPosixWord',
],
'xposixxdigit' => 
[
'XDigit',
'XPosixXDigit',
],
);

# Keys are standard property name; values are each one's aliases
%Unicode::UCD::prop_aliases = (
'age' => 
[
'age',
'Age',
],
'ahex' => 
[
'AHex',
'ASCII_Hex_Digit',
],
'alpha' => 
[
'Alpha',
'Alphabetic',
],
'bc' => 
[
'bc',
'Bidi_Class',
],
'bidic' => 
[
'Bidi_C',
'Bidi_Control',
],
'bidim' => 
[
'Bidi_M',
'Bidi_Mirrored',
],
'blk' => 
[
'blk',
'Block',
],
'bmg' => 
[
'bmg',
'Bidi_Mirroring_Glyph',
],
'bpb' => 
[
'bpb',
'Bidi_Paired_Bracket',
],
'bpt' => 
[
'bpt',
'Bidi_Paired_Bracket_Type',
],
'cased' => 
[
'Cased',
'Cased',
],
'ccc' => 
[
'ccc',
'Canonical_Combining_Class',
],
'ce' => 
[
'CE',
'Composition_Exclusion',
],
'cf' => 
[
'cf',
'Case_Folding',
],
'ci' => 
[
'CI',
'Case_Ignorable',
],
'compex' => 
[
'Comp_Ex',
'Full_Composition_Exclusion',
],
'cwcf' => 
[
'CWCF',
'Changes_When_Casefolded',
],
'cwcm' => 
[
'CWCM',
'Changes_When_Casemapped',
],
'cwkcf' => 
[
'CWKCF',
'Changes_When_NFKC_Casefolded',
],
'cwl' => 
[
'CWL',
'Changes_When_Lowercased',
],
'cwt' => 
[
'CWT',
'Changes_When_Titlecased',
],
'cwu' => 
[
'CWU',
'Changes_When_Uppercased',
],
'dash' => 
[
'Dash',
'Dash',
],
'dep' => 
[
'Dep',
'Deprecated',
],
'di' => 
[
'DI',
'Default_Ignorable_Code_Point',
],
'dia' => 
[
'Dia',
'Diacritic',
],
'dm' => 
[
'dm',
'Decomposition_Mapping',
],
'dt' => 
[
'dt',
'Decomposition_Type',
],
'ea' => 
[
'ea',
'East_Asian_Width',
],
'ebase' => 
[
'EBase',
'Emoji_Modifier_Base',
],
'ecomp' => 
[
'EComp',
'Emoji_Component',
],
'emod' => 
[
'EMod',
'Emoji_Modifier',
],
'emoji' => 
[
'Emoji',
'Emoji',
],
'epres' => 
[
'EPres',
'Emoji_Presentation',
],
'equideo' => 
[
'EqUIdeo',
'Equivalent_Unified_Ideograph',
],
'ext' => 
[
'Ext',
'Extender',
],
'extpict' => 
[
'ExtPict',
'Extended_Pictographic',
],
'gc' => 
[
'gc',
'General_Category',
'Category',
],
'gcb' => 
[
'GCB',
'Grapheme_Cluster_Break',
'_Perl_GCB',
],
'grbase' => 
[
'Gr_Base',
'Grapheme_Base',
],
'grext' => 
[
'Gr_Ext',
'Grapheme_Extend',
],
'hex' => 
[
'Hex',
'Hex_Digit',
],
'hst' => 
[
'hst',
'Hangul_Syllable_Type',
],
'hyphen' => 
[
'Hyphen',
'Hyphen',
],
'idc' => 
[
'IDC',
'ID_Continue',
],
'identifierstatus' => 
[
'Identifier_Status',
'Identifier_Status',
],
'identifiertype' => 
[
'Identifier_Type',
'Identifier_Type',
],
'ideo' => 
[
'Ideo',
'Ideographic',
],
'ids' => 
[
'IDS',
'ID_Start',
],
'idsb' => 
[
'IDSB',
'IDS_Binary_Operator',
],
'idst' => 
[
'IDST',
'IDS_Trinary_Operator',
],
'in' => 
[
'In',
'Present_In',
],
'inpc' => 
[
'InPC',
'Indic_Positional_Category',
],
'insc' => 
[
'InSC',
'Indic_Syllabic_Category',
],
'isc' => 
[
'isc',
'ISO_Comment',
],
'jg' => 
[
'jg',
'Joining_Group',
],
'joinc' => 
[
'Join_C',
'Join_Control',
],
'jt' => 
[
'jt',
'Joining_Type',
],
'lb' => 
[
'lb',
'Line_Break',
],
'lc' => 
[
'lc',
'Lowercase_Mapping',
],
'loe' => 
[
'LOE',
'Logical_Order_Exception',
],
'lower' => 
[
'Lower',
'Lowercase',
],
'math' => 
[
'Math',
'Math',
],
'na' => 
[
'na',
'Name',
],
'na1' => 
[
'na1',
'Unicode_1_Name',
],
'namealias' => 
[
'Name_Alias',
'Name_Alias',
'_Perl_Name_Alias',
],
'nchar' => 
[
'NChar',
'Noncharacter_Code_Point',
],
'nfcqc' => 
[
'NFC_QC',
'NFC_Quick_Check',
],
'nfdqc' => 
[
'NFD_QC',
'NFD_Quick_Check',
],
'nfkccf' => 
[
'NFKC_CF',
'NFKC_Casefold',
],
'nfkcqc' => 
[
'NFKC_QC',
'NFKC_Quick_Check',
],
'nfkdqc' => 
[
'NFKD_QC',
'NFKD_Quick_Check',
],
'nt' => 
[
'nt',
'Numeric_Type',
],
'nv' => 
[
'nv',
'Numeric_Value',
],
'patsyn' => 
[
'Pat_Syn',
'Pattern_Syntax',
],
'patws' => 
[
'Pat_WS',
'Pattern_White_Space',
],
'pcm' => 
[
'PCM',
'Prepended_Concatenation_Mark',
],
'perldecimaldigit' => 
[
'Perl_Decimal_Digit',
'Perl_Decimal_Digit',
],
'qmark' => 
[
'QMark',
'Quotation_Mark',
],
'radical' => 
[
'Radical',
'Radical',
],
'ri' => 
[
'RI',
'Regional_Indicator',
],
'sb' => 
[
'SB',
'Sentence_Break',
'_Perl_SB',
],
'sc' => 
[
'sc',
'Script',
],
'scf' => 
[
'scf',
'Simple_Case_Folding',
'Sfc',
],
'scx' => 
[
'scx',
'Script_Extensions',
],
'sd' => 
[
'SD',
'Soft_Dotted',
],
'slc' => 
[
'slc',
'Simple_Lowercase_Mapping',
],
'stc' => 
[
'stc',
'Simple_Titlecase_Mapping',
],
'sterm' => 
[
'STerm',
'Sentence_Terminal',
],
'suc' => 
[
'suc',
'Simple_Uppercase_Mapping',
],
'tc' => 
[
'tc',
'Titlecase_Mapping',
],
'term' => 
[
'Term',
'Terminal_Punctuation',
],
'uc' => 
[
'uc',
'Uppercase_Mapping',
],
'uideo' => 
[
'UIdeo',
'Unified_Ideograph',
],
'upper' => 
[
'Upper',
'Uppercase',
],
'vo' => 
[
'vo',
'Vertical_Orientation',
],
'vs' => 
[
'VS',
'Variation_Selector',
],
'wb' => 
[
'WB',
'Word_Break',
'_Perl_WB',
],
'wspace' => 
[
'WSpace',
'White_Space',
'Space',
],
'xidc' => 
[
'XIDC',
'XID_Continue',
],
'xids' => 
[
'XIDS',
'XID_Start',
],
);

# Keys of top level are standard property name; values are keys to another
# hash,  Each one is one of the property's values, in standard form.  The
# values are that prop-val's aliases.  If only one specified, the short and
# long alias are identical.
%Unicode::UCD::prop_value_aliases = (
'age' => 
{
'1.1' => 
[
'1.1',
'V1_1',
],
'10.0' => 
[
'10.0',
'V10_0',
],
'11.0' => 
[
'11.0',
'V11_0',
],
'12.0' => 
[
'12.0',
'V12_0',
],
'12.1' => 
[
'12.1',
'V12_1',
],
'13.0' => 
[
'13.0',
'V13_0',
],
'14.0' => 
[
'14.0',
'V14_0',
],
'15.0' => 
[
'15.0',
'V15_0',
],
'2.0' => 
[
'2.0',
'V2_0',
],
'2.1' => 
[
'2.1',
'V2_1',
],
'3.0' => 
[
'3.0',
'V3_0',
],
'3.1' => 
[
'3.1',
'V3_1',
],
'3.2' => 
[
'3.2',
'V3_2',
],
'4.0' => 
[
'4.0',
'V4_0',
],
'4.1' => 
[
'4.1',
'V4_1',
],
'5.0' => 
[
'5.0',
'V5_0',
],
'5.1' => 
[
'5.1',
'V5_1',
],
'5.2' => 
[
'5.2',
'V5_2',
],
'6.0' => 
[
'6.0',
'V6_0',
],
'6.1' => 
[
'6.1',
'V6_1',
],
'6.2' => 
[
'6.2',
'V6_2',
],
'6.3' => 
[
'6.3',
'V6_3',
],
'7.0' => 
[
'7.0',
'V7_0',
],
'8.0' => 
[
'8.0',
'V8_0',
],
'9.0' => 
[
'9.0',
'V9_0',
],
'na' => 
[
'NA',
'Unassigned',
],
},
'ahex' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'alpha' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'bc' => 
{
'al' => 
[
'AL',
'Arabic_Letter',
],
'an' => 
[
'AN',
'Arabic_Number',
],
'b' => 
[
'B',
'Paragraph_Separator',
],
'bn' => 
[
'BN',
'Boundary_Neutral',
],
'cs' => 
[
'CS',
'Common_Separator',
],
'en' => 
[
'EN',
'European_Number',
],
'es' => 
[
'ES',
'European_Separator',
],
'et' => 
[
'ET',
'European_Terminator',
],
'fsi' => 
[
'FSI',
'First_Strong_Isolate',
],
'l' => 
[
'L',
'Left_To_Right',
],
'lre' => 
[
'LRE',
'Left_To_Right_Embedding',
],
'lri' => 
[
'LRI',
'Left_To_Right_Isolate',
],
'lro' => 
[
'LRO',
'Left_To_Right_Override',
],
'nsm' => 
[
'NSM',
'Nonspacing_Mark',
],
'on' => 
[
'ON',
'Other_Neutral',
],
'pdf' => 
[
'PDF',
'Pop_Directional_Format',
],
'pdi' => 
[
'PDI',
'Pop_Directional_Isolate',
],
'r' => 
[
'R',
'Right_To_Left',
],
'rle' => 
[
'RLE',
'Right_To_Left_Embedding',
],
'rli' => 
[
'RLI',
'Right_To_Left_Isolate',
],
'rlo' => 
[
'RLO',
'Right_To_Left_Override',
],
's' => 
[
'S',
'Segment_Separator',
],
'ws' => 
[
'WS',
'White_Space',
],
},
'bidic' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'bidim' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'blk' => 
{
'adlam' => 
[
'Adlam',
],
'aegeannumbers' => 
[
'Aegean_Numbers',
],
'ahom' => 
[
'Ahom',
],
'alchemical' => 
[
'Alchemical',
'Alchemical_Symbols',
],
'alphabeticpf' => 
[
'Alphabetic_PF',
'Alphabetic_Presentation_Forms',
],
'anatolianhieroglyphs' => 
[
'Anatolian_Hieroglyphs',
],
'ancientgreekmusic' => 
[
'Ancient_Greek_Music',
'Ancient_Greek_Musical_Notation',
],
'ancientgreeknumbers' => 
[
'Ancient_Greek_Numbers',
],
'ancientsymbols' => 
[
'Ancient_Symbols',
],
'arabic' => 
[
'Arabic',
],
'arabicexta' => 
[
'Arabic_Ext_A',
'Arabic_Extended_A',
],
'arabicextb' => 
[
'Arabic_Ext_B',
'Arabic_Extended_B',
],
'arabicextc' => 
[
'Arabic_Ext_C',
'Arabic_Extended_C',
],
'arabicmath' => 
[
'Arabic_Math',
'Arabic_Mathematical_Alphabetic_Symbols',
],
'arabicpfa' => 
[
'Arabic_PF_A',
'Arabic_Presentation_Forms_A',
],
'arabicpfb' => 
[
'Arabic_PF_B',
'Arabic_Presentation_Forms_B',
],
'arabicsup' => 
[
'Arabic_Sup',
'Arabic_Supplement',
],
'armenian' => 
[
'Armenian',
],
'arrows' => 
[
'Arrows',
],
'ascii' => 
[
'ASCII',
'Basic_Latin',
],
'avestan' => 
[
'Avestan',
],
'balinese' => 
[
'Balinese',
],
'bamum' => 
[
'Bamum',
],
'bamumsup' => 
[
'Bamum_Sup',
'Bamum_Supplement',
],
'bassavah' => 
[
'Bassa_Vah',
],
'batak' => 
[
'Batak',
],
'bengali' => 
[
'Bengali',
],
'bhaiksuki' => 
[
'Bhaiksuki',
],
'blockelements' => 
[
'Block_Elements',
],
'bopomofo' => 
[
'Bopomofo',
],
'bopomofoext' => 
[
'Bopomofo_Ext',
'Bopomofo_Extended',
],
'boxdrawing' => 
[
'Box_Drawing',
],
'brahmi' => 
[
'Brahmi',
],
'braille' => 
[
'Braille',
'Braille_Patterns',
],
'buginese' => 
[
'Buginese',
],
'buhid' => 
[
'Buhid',
],
'byzantinemusic' => 
[
'Byzantine_Music',
'Byzantine_Musical_Symbols',
],
'carian' => 
[
'Carian',
],
'caucasianalbanian' => 
[
'Caucasian_Albanian',
],
'chakma' => 
[
'Chakma',
],
'cham' => 
[
'Cham',
],
'cherokee' => 
[
'Cherokee',
],
'cherokeesup' => 
[
'Cherokee_Sup',
'Cherokee_Supplement',
],
'chesssymbols' => 
[
'Chess_Symbols',
],
'chorasmian' => 
[
'Chorasmian',
],
'cjk' => 
[
'CJK',
'CJK_Unified_Ideographs',
],
'cjkcompat' => 
[
'CJK_Compat',
'CJK_Compatibility',
],
'cjkcompatforms' => 
[
'CJK_Compat_Forms',
'CJK_Compatibility_Forms',
],
'cjkcompatideographs' => 
[
'CJK_Compat_Ideographs',
'CJK_Compatibility_Ideographs',
],
'cjkcompatideographssup' => 
[
'CJK_Compat_Ideographs_Sup',
'CJK_Compatibility_Ideographs_Supplement',
],
'cjkexta' => 
[
'CJK_Ext_A',
'CJK_Unified_Ideographs_Extension_A',
],
'cjkextb' => 
[
'CJK_Ext_B',
'CJK_Unified_Ideographs_Extension_B',
],
'cjkextc' => 
[
'CJK_Ext_C',
'CJK_Unified_Ideographs_Extension_C',
],
'cjkextd' => 
[
'CJK_Ext_D',
'CJK_Unified_Ideographs_Extension_D',
],
'cjkexte' => 
[
'CJK_Ext_E',
'CJK_Unified_Ideographs_Extension_E',
],
'cjkextf' => 
[
'CJK_Ext_F',
'CJK_Unified_Ideographs_Extension_F',
],
'cjkextg' => 
[
'CJK_Ext_G',
'CJK_Unified_Ideographs_Extension_G',
],
'cjkexth' => 
[
'CJK_Ext_H',
'CJK_Unified_Ideographs_Extension_H',
],
'cjkradicalssup' => 
[
'CJK_Radicals_Sup',
'CJK_Radicals_Supplement',
],
'cjkstrokes' => 
[
'CJK_Strokes',
],
'cjksymbols' => 
[
'CJK_Symbols',
'CJK_Symbols_And_Punctuation',
],
'compatjamo' => 
[
'Compat_Jamo',
'Hangul_Compatibility_Jamo',
],
'controlpictures' => 
[
'Control_Pictures',
],
'coptic' => 
[
'Coptic',
],
'copticepactnumbers' => 
[
'Coptic_Epact_Numbers',
],
'countingrod' => 
[
'Counting_Rod',
'Counting_Rod_Numerals',
],
'cuneiform' => 
[
'Cuneiform',
],
'cuneiformnumbers' => 
[
'Cuneiform_Numbers',
'Cuneiform_Numbers_And_Punctuation',
],
'currencysymbols' => 
[
'Currency_Symbols',
],
'cypriotsyllabary' => 
[
'Cypriot_Syllabary',
],
'cyprominoan' => 
[
'Cypro_Minoan',
],
'cyrillic' => 
[
'Cyrillic',
],
'cyrillicexta' => 
[
'Cyrillic_Ext_A',
'Cyrillic_Extended_A',
],
'cyrillicextb' => 
[
'Cyrillic_Ext_B',
'Cyrillic_Extended_B',
],
'cyrillicextc' => 
[
'Cyrillic_Ext_C',
'Cyrillic_Extended_C',
],
'cyrillicextd' => 
[
'Cyrillic_Ext_D',
'Cyrillic_Extended_D',
],
'cyrillicsup' => 
[
'Cyrillic_Sup',
'Cyrillic_Supplement',
'Cyrillic_Supplementary',
],
'deseret' => 
[
'Deseret',
],
'devanagari' => 
[
'Devanagari',
],
'devanagariext' => 
[
'Devanagari_Ext',
'Devanagari_Extended',
],
'devanagariexta' => 
[
'Devanagari_Ext_A',
'Devanagari_Extended_A',
],
'diacriticals' => 
[
'Diacriticals',
'Combining_Diacritical_Marks',
],
'diacriticalsext' => 
[
'Diacriticals_Ext',
'Combining_Diacritical_Marks_Extended',
],
'diacriticalsforsymbols' => 
[
'Diacriticals_For_Symbols',
'Combining_Diacritical_Marks_For_Symbols',
'Combining_Marks_For_Symbols',
],
'diacriticalssup' => 
[
'Diacriticals_Sup',
'Combining_Diacritical_Marks_Supplement',
],
'dingbats' => 
[
'Dingbats',
],
'divesakuru' => 
[
'Dives_Akuru',
],
'dogra' => 
[
'Dogra',
],
'domino' => 
[
'Domino',
'Domino_Tiles',
],
'duployan' => 
[
'Duployan',
],
'earlydynasticcuneiform' => 
[
'Early_Dynastic_Cuneiform',
],
'egyptianhieroglyphformatcontrols' => 
[
'Egyptian_Hieroglyph_Format_Controls',
],
'egyptianhieroglyphs' => 
[
'Egyptian_Hieroglyphs',
],
'elbasan' => 
[
'Elbasan',
],
'elymaic' => 
[
'Elymaic',
],
'emoticons' => 
[
'Emoticons',
],
'enclosedalphanum' => 
[
'Enclosed_Alphanum',
'Enclosed_Alphanumerics',
],
'enclosedalphanumsup' => 
[
'Enclosed_Alphanum_Sup',
'Enclosed_Alphanumeric_Supplement',
],
'enclosedcjk' => 
[
'Enclosed_CJK',
'Enclosed_CJK_Letters_And_Months',
],
'enclosedideographicsup' => 
[
'Enclosed_Ideographic_Sup',
'Enclosed_Ideographic_Supplement',
],
'ethiopic' => 
[
'Ethiopic',
],
'ethiopicext' => 
[
'Ethiopic_Ext',
'Ethiopic_Extended',
],
'ethiopicexta' => 
[
'Ethiopic_Ext_A',
'Ethiopic_Extended_A',
],
'ethiopicextb' => 
[
'Ethiopic_Ext_B',
'Ethiopic_Extended_B',
],
'ethiopicsup' => 
[
'Ethiopic_Sup',
'Ethiopic_Supplement',
],
'geometricshapes' => 
[
'Geometric_Shapes',
],
'geometricshapesext' => 
[
'Geometric_Shapes_Ext',
'Geometric_Shapes_Extended',
],
'georgian' => 
[
'Georgian',
],
'georgianext' => 
[
'Georgian_Ext',
'Georgian_Extended',
],
'georgiansup' => 
[
'Georgian_Sup',
'Georgian_Supplement',
],
'glagolitic' => 
[
'Glagolitic',
],
'glagoliticsup' => 
[
'Glagolitic_Sup',
'Glagolitic_Supplement',
],
'gothic' => 
[
'Gothic',
],
'grantha' => 
[
'Grantha',
],
'greek' => 
[
'Greek',
'Greek_And_Coptic',
],
'greekext' => 
[
'Greek_Ext',
'Greek_Extended',
],
'gujarati' => 
[
'Gujarati',
],
'gunjalagondi' => 
[
'Gunjala_Gondi',
],
'gurmukhi' => 
[
'Gurmukhi',
],
'halfandfullforms' => 
[
'Half_And_Full_Forms',
'Halfwidth_And_Fullwidth_Forms',
],
'halfmarks' => 
[
'Half_Marks',
'Combining_Half_Marks',
],
'hangul' => 
[
'Hangul',
'Hangul_Syllables',
],
'hanifirohingya' => 
[
'Hanifi_Rohingya',
],
'hanunoo' => 
[
'Hanunoo',
],
'hatran' => 
[
'Hatran',
],
'hebrew' => 
[
'Hebrew',
],
'highpusurrogates' => 
[
'High_PU_Surrogates',
'High_Private_Use_Surrogates',
],
'highsurrogates' => 
[
'High_Surrogates',
],
'hiragana' => 
[
'Hiragana',
],
'idc' => 
[
'IDC',
'Ideographic_Description_Characters',
],
'ideographicsymbols' => 
[
'Ideographic_Symbols',
'Ideographic_Symbols_And_Punctuation',
],
'imperialaramaic' => 
[
'Imperial_Aramaic',
],
'indicnumberforms' => 
[
'Indic_Number_Forms',
'Common_Indic_Number_Forms',
],
'indicsiyaqnumbers' => 
[
'Indic_Siyaq_Numbers',
],
'inscriptionalpahlavi' => 
[
'Inscriptional_Pahlavi',
],
'inscriptionalparthian' => 
[
'Inscriptional_Parthian',
],
'ipaext' => 
[
'IPA_Ext',
'IPA_Extensions',
],
'jamo' => 
[
'Jamo',
'Hangul_Jamo',
],
'jamoexta' => 
[
'Jamo_Ext_A',
'Hangul_Jamo_Extended_A',
],
'jamoextb' => 
[
'Jamo_Ext_B',
'Hangul_Jamo_Extended_B',
],
'javanese' => 
[
'Javanese',
],
'kaithi' => 
[
'Kaithi',
],
'kaktoviknumerals' => 
[
'Kaktovik_Numerals',
],
'kanaexta' => 
[
'Kana_Ext_A',
'Kana_Extended_A',
],
'kanaextb' => 
[
'Kana_Ext_B',
'Kana_Extended_B',
],
'kanasup' => 
[
'Kana_Sup',
'Kana_Supplement',
],
'kanbun' => 
[
'Kanbun',
],
'kangxi' => 
[
'Kangxi',
'Kangxi_Radicals',
],
'kannada' => 
[
'Kannada',
],
'katakana' => 
[
'Katakana',
],
'katakanaext' => 
[
'Katakana_Ext',
'Katakana_Phonetic_Extensions',
],
'kawi' => 
[
'Kawi',
],
'kayahli' => 
[
'Kayah_Li',
],
'kharoshthi' => 
[
'Kharoshthi',
],
'khitansmallscript' => 
[
'Khitan_Small_Script',
],
'khmer' => 
[
'Khmer',
],
'khmersymbols' => 
[
'Khmer_Symbols',
],
'khojki' => 
[
'Khojki',
],
'khudawadi' => 
[
'Khudawadi',
],
'lao' => 
[
'Lao',
],
'latin1sup' => 
[
'Latin_1_Sup',
'Latin_1_Supplement',
'Latin_1',
],
'latinexta' => 
[
'Latin_Ext_A',
'Latin_Extended_A',
],
'latinextadditional' => 
[
'Latin_Ext_Additional',
'Latin_Extended_Additional',
],
'latinextb' => 
[
'Latin_Ext_B',
'Latin_Extended_B',
],
'latinextc' => 
[
'Latin_Ext_C',
'Latin_Extended_C',
],
'latinextd' => 
[
'Latin_Ext_D',
'Latin_Extended_D',
],
'latinexte' => 
[
'Latin_Ext_E',
'Latin_Extended_E',
],
'latinextf' => 
[
'Latin_Ext_F',
'Latin_Extended_F',
],
'latinextg' => 
[
'Latin_Ext_G',
'Latin_Extended_G',
],
'lepcha' => 
[
'Lepcha',
],
'letterlikesymbols' => 
[
'Letterlike_Symbols',
],
'limbu' => 
[
'Limbu',
],
'lineara' => 
[
'Linear_A',
],
'linearbideograms' => 
[
'Linear_B_Ideograms',
],
'linearbsyllabary' => 
[
'Linear_B_Syllabary',
],
'lisu' => 
[
'Lisu',
],
'lisusup' => 
[
'Lisu_Sup',
'Lisu_Supplement',
],
'lowsurrogates' => 
[
'Low_Surrogates',
],
'lycian' => 
[
'Lycian',
],
'lydian' => 
[
'Lydian',
],
'mahajani' => 
[
'Mahajani',
],
'mahjong' => 
[
'Mahjong',
'Mahjong_Tiles',
],
'makasar' => 
[
'Makasar',
],
'malayalam' => 
[
'Malayalam',
],
'mandaic' => 
[
'Mandaic',
],
'manichaean' => 
[
'Manichaean',
],
'marchen' => 
[
'Marchen',
],
'masaramgondi' => 
[
'Masaram_Gondi',
],
'mathalphanum' => 
[
'Math_Alphanum',
'Mathematical_Alphanumeric_Symbols',
],
'mathoperators' => 
[
'Math_Operators',
'Mathematical_Operators',
],
'mayannumerals' => 
[
'Mayan_Numerals',
],
'medefaidrin' => 
[
'Medefaidrin',
],
'meeteimayek' => 
[
'Meetei_Mayek',
],
'meeteimayekext' => 
[
'Meetei_Mayek_Ext',
'Meetei_Mayek_Extensions',
],
'mendekikakui' => 
[
'Mende_Kikakui',
],
'meroiticcursive' => 
[
'Meroitic_Cursive',
],
'meroitichieroglyphs' => 
[
'Meroitic_Hieroglyphs',
],
'miao' => 
[
'Miao',
],
'miscarrows' => 
[
'Misc_Arrows',
'Miscellaneous_Symbols_And_Arrows',
],
'miscmathsymbolsa' => 
[
'Misc_Math_Symbols_A',
'Miscellaneous_Mathematical_Symbols_A',
],
'miscmathsymbolsb' => 
[
'Misc_Math_Symbols_B',
'Miscellaneous_Mathematical_Symbols_B',
],
'miscpictographs' => 
[
'Misc_Pictographs',
'Miscellaneous_Symbols_And_Pictographs',
],
'miscsymbols' => 
[
'Misc_Symbols',
'Miscellaneous_Symbols',
],
'misctechnical' => 
[
'Misc_Technical',
'Miscellaneous_Technical',
],
'modi' => 
[
'Modi',
],
'modifierletters' => 
[
'Modifier_Letters',
'Spacing_Modifier_Letters',
],
'modifiertoneletters' => 
[
'Modifier_Tone_Letters',
],
'mongolian' => 
[
'Mongolian',
],
'mongoliansup' => 
[
'Mongolian_Sup',
'Mongolian_Supplement',
],
'mro' => 
[
'Mro',
],
'multani' => 
[
'Multani',
],
'music' => 
[
'Music',
'Musical_Symbols',
],
'myanmar' => 
[
'Myanmar',
],
'myanmarexta' => 
[
'Myanmar_Ext_A',
'Myanmar_Extended_A',
],
'myanmarextb' => 
[
'Myanmar_Ext_B',
'Myanmar_Extended_B',
],
'nabataean' => 
[
'Nabataean',
],
'nagmundari' => 
[
'Nag_Mundari',
],
'nandinagari' => 
[
'Nandinagari',
],
'nb' => 
[
'NB',
'No_Block',
],
'newa' => 
[
'Newa',
],
'newtailue' => 
[
'New_Tai_Lue',
],
'nko' => 
[
'NKo',
],
'numberforms' => 
[
'Number_Forms',
],
'nushu' => 
[
'Nushu',
],
'nyiakengpuachuehmong' => 
[
'Nyiakeng_Puachue_Hmong',
],
'ocr' => 
[
'OCR',
'Optical_Character_Recognition',
],
'ogham' => 
[
'Ogham',
],
'olchiki' => 
[
'Ol_Chiki',
],
'oldhungarian' => 
[
'Old_Hungarian',
],
'olditalic' => 
[
'Old_Italic',
],
'oldnortharabian' => 
[
'Old_North_Arabian',
],
'oldpermic' => 
[
'Old_Permic',
],
'oldpersian' => 
[
'Old_Persian',
],
'oldsogdian' => 
[
'Old_Sogdian',
],
'oldsoutharabian' => 
[
'Old_South_Arabian',
],
'oldturkic' => 
[
'Old_Turkic',
],
'olduyghur' => 
[
'Old_Uyghur',
],
'oriya' => 
[
'Oriya',
],
'ornamentaldingbats' => 
[
'Ornamental_Dingbats',
],
'osage' => 
[
'Osage',
],
'osmanya' => 
[
'Osmanya',
],
'ottomansiyaqnumbers' => 
[
'Ottoman_Siyaq_Numbers',
],
'pahawhhmong' => 
[
'Pahawh_Hmong',
],
'palmyrene' => 
[
'Palmyrene',
],
'paucinhau' => 
[
'Pau_Cin_Hau',
],
'phagspa' => 
[
'Phags_Pa',
],
'phaistos' => 
[
'Phaistos',
'Phaistos_Disc',
],
'phoenician' => 
[
'Phoenician',
],
'phoneticext' => 
[
'Phonetic_Ext',
'Phonetic_Extensions',
],
'phoneticextsup' => 
[
'Phonetic_Ext_Sup',
'Phonetic_Extensions_Supplement',
],
'playingcards' => 
[
'Playing_Cards',
],
'psalterpahlavi' => 
[
'Psalter_Pahlavi',
],
'pua' => 
[
'PUA',
'Private_Use_Area',
'Private_Use',
],
'punctuation' => 
[
'Punctuation',
'General_Punctuation',
],
'rejang' => 
[
'Rejang',
],
'rumi' => 
[
'Rumi',
'Rumi_Numeral_Symbols',
],
'runic' => 
[
'Runic',
],
'samaritan' => 
[
'Samaritan',
],
'saurashtra' => 
[
'Saurashtra',
],
'sharada' => 
[
'Sharada',
],
'shavian' => 
[
'Shavian',
],
'shorthandformatcontrols' => 
[
'Shorthand_Format_Controls',
],
'siddham' => 
[
'Siddham',
],
'sinhala' => 
[
'Sinhala',
],
'sinhalaarchaicnumbers' => 
[
'Sinhala_Archaic_Numbers',
],
'smallforms' => 
[
'Small_Forms',
'Small_Form_Variants',
],
'smallkanaext' => 
[
'Small_Kana_Ext',
'Small_Kana_Extension',
],
'sogdian' => 
[
'Sogdian',
],
'sorasompeng' => 
[
'Sora_Sompeng',
],
'soyombo' => 
[
'Soyombo',
],
'specials' => 
[
'Specials',
],
'sundanese' => 
[
'Sundanese',
],
'sundanesesup' => 
[
'Sundanese_Sup',
'Sundanese_Supplement',
],
'suparrowsa' => 
[
'Sup_Arrows_A',
'Supplemental_Arrows_A',
],
'suparrowsb' => 
[
'Sup_Arrows_B',
'Supplemental_Arrows_B',
],
'suparrowsc' => 
[
'Sup_Arrows_C',
'Supplemental_Arrows_C',
],
'superandsub' => 
[
'Super_And_Sub',
'Superscripts_And_Subscripts',
],
'supmathoperators' => 
[
'Sup_Math_Operators',
'Supplemental_Mathematical_Operators',
],
'suppuaa' => 
[
'Sup_PUA_A',
'Supplementary_Private_Use_Area_A',
],
'suppuab' => 
[
'Sup_PUA_B',
'Supplementary_Private_Use_Area_B',
],
'suppunctuation' => 
[
'Sup_Punctuation',
'Supplemental_Punctuation',
],
'supsymbolsandpictographs' => 
[
'Sup_Symbols_And_Pictographs',
'Supplemental_Symbols_And_Pictographs',
],
'suttonsignwriting' => 
[
'Sutton_SignWriting',
],
'sylotinagri' => 
[
'Syloti_Nagri',
],
'symbolsandpictographsexta' => 
[
'Symbols_And_Pictographs_Ext_A',
'Symbols_And_Pictographs_Extended_A',
],
'symbolsforlegacycomputing' => 
[
'Symbols_For_Legacy_Computing',
],
'syriac' => 
[
'Syriac',
],
'syriacsup' => 
[
'Syriac_Sup',
'Syriac_Supplement',
],
'tagalog' => 
[
'Tagalog',
],
'tagbanwa' => 
[
'Tagbanwa',
],
'tags' => 
[
'Tags',
],
'taile' => 
[
'Tai_Le',
],
'taitham' => 
[
'Tai_Tham',
],
'taiviet' => 
[
'Tai_Viet',
],
'taixuanjing' => 
[
'Tai_Xuan_Jing',
'Tai_Xuan_Jing_Symbols',
],
'takri' => 
[
'Takri',
],
'tamil' => 
[
'Tamil',
],
'tamilsup' => 
[
'Tamil_Sup',
'Tamil_Supplement',
],
'tangsa' => 
[
'Tangsa',
],
'tangut' => 
[
'Tangut',
],
'tangutcomponents' => 
[
'Tangut_Components',
],
'tangutsup' => 
[
'Tangut_Sup',
'Tangut_Supplement',
],
'telugu' => 
[
'Telugu',
],
'thaana' => 
[
'Thaana',
],
'thai' => 
[
'Thai',
],
'tibetan' => 
[
'Tibetan',
],
'tifinagh' => 
[
'Tifinagh',
],
'tirhuta' => 
[
'Tirhuta',
],
'toto' => 
[
'Toto',
],
'transportandmap' => 
[
'Transport_And_Map',
'Transport_And_Map_Symbols',
],
'ucas' => 
[
'UCAS',
'Unified_Canadian_Aboriginal_Syllabics',
'Canadian_Syllabics',
],
'ucasext' => 
[
'UCAS_Ext',
'Unified_Canadian_Aboriginal_Syllabics_Extended',
],
'ucasexta' => 
[
'UCAS_Ext_A',
'Unified_Canadian_Aboriginal_Syllabics_Extended_A',
],
'ugaritic' => 
[
'Ugaritic',
],
'vai' => 
[
'Vai',
],
'vedicext' => 
[
'Vedic_Ext',
'Vedic_Extensions',
],
'verticalforms' => 
[
'Vertical_Forms',
],
'vithkuqi' => 
[
'Vithkuqi',
],
'vs' => 
[
'VS',
'Variation_Selectors',
],
'vssup' => 
[
'VS_Sup',
'Variation_Selectors_Supplement',
],
'wancho' => 
[
'Wancho',
],
'warangciti' => 
[
'Warang_Citi',
],
'yezidi' => 
[
'Yezidi',
],
'yijing' => 
[
'Yijing',
'Yijing_Hexagram_Symbols',
],
'yiradicals' => 
[
'Yi_Radicals',
],
'yisyllables' => 
[
'Yi_Syllables',
],
'zanabazarsquare' => 
[
'Zanabazar_Square',
],
'znamennymusic' => 
[
'Znamenny_Music',
'Znamenny_Musical_Notation',
],
},
'bpt' => 
{
'c' => 
[
'c',
'Close',
],
'n' => 
[
'n',
'None',
],
'o' => 
[
'o',
'Open',
],
},
'cased' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'ccc' => 
{
'a' => 
[
'A',
'Above',
230,
],
'al' => 
[
'AL',
'Above_Left',
228,
],
'ar' => 
[
'AR',
'Above_Right',
232,
],
'ata' => 
[
'ATA',
'Attached_Above',
214,
],
'atar' => 
[
'ATAR',
'Attached_Above_Right',
216,
],
'atb' => 
[
'ATB',
'Attached_Below',
202,
],
'atbl' => 
[
'ATBL',
'Attached_Below_Left',
200,
],
'b' => 
[
'B',
'Below',
220,
],
'bl' => 
[
'BL',
'Below_Left',
218,
],
'br' => 
[
'BR',
'Below_Right',
222,
],
'ccc10' => 
[
'CCC10',
'CCC10',
10,
],
'ccc103' => 
[
'CCC103',
'CCC103',
103,
],
'ccc107' => 
[
'CCC107',
'CCC107',
107,
],
'ccc11' => 
[
'CCC11',
'CCC11',
11,
],
'ccc118' => 
[
'CCC118',
'CCC118',
118,
],
'ccc12' => 
[
'CCC12',
'CCC12',
12,
],
'ccc122' => 
[
'CCC122',
'CCC122',
122,
],
'ccc129' => 
[
'CCC129',
'CCC129',
129,
],
'ccc13' => 
[
'CCC13',
'CCC13',
13,
],
'ccc130' => 
[
'CCC130',
'CCC130',
130,
],
'ccc132' => 
[
'CCC132',
'CCC132',
132,
],
'ccc133' => 
[
'CCC133',
'CCC133',
133,
],
'ccc14' => 
[
'CCC14',
'CCC14',
14,
],
'ccc15' => 
[
'CCC15',
'CCC15',
15,
],
'ccc16' => 
[
'CCC16',
'CCC16',
16,
],
'ccc17' => 
[
'CCC17',
'CCC17',
17,
],
'ccc18' => 
[
'CCC18',
'CCC18',
18,
],
'ccc19' => 
[
'CCC19',
'CCC19',
19,
],
'ccc20' => 
[
'CCC20',
'CCC20',
20,
],
'ccc21' => 
[
'CCC21',
'CCC21',
21,
],
'ccc22' => 
[
'CCC22',
'CCC22',
22,
],
'ccc23' => 
[
'CCC23',
'CCC23',
23,
],
'ccc24' => 
[
'CCC24',
'CCC24',
24,
],
'ccc25' => 
[
'CCC25',
'CCC25',
25,
],
'ccc26' => 
[
'CCC26',
'CCC26',
26,
],
'ccc27' => 
[
'CCC27',
'CCC27',
27,
],
'ccc28' => 
[
'CCC28',
'CCC28',
28,
],
'ccc29' => 
[
'CCC29',
'CCC29',
29,
],
'ccc30' => 
[
'CCC30',
'CCC30',
30,
],
'ccc31' => 
[
'CCC31',
'CCC31',
31,
],
'ccc32' => 
[
'CCC32',
'CCC32',
32,
],
'ccc33' => 
[
'CCC33',
'CCC33',
33,
],
'ccc34' => 
[
'CCC34',
'CCC34',
34,
],
'ccc35' => 
[
'CCC35',
'CCC35',
35,
],
'ccc36' => 
[
'CCC36',
'CCC36',
36,
],
'ccc84' => 
[
'CCC84',
'CCC84',
84,
],
'ccc91' => 
[
'CCC91',
'CCC91',
91,
],
'da' => 
[
'DA',
'Double_Above',
234,
],
'db' => 
[
'DB',
'Double_Below',
233,
],
'hanr' => 
[
'HANR',
'Han_Reading',
6,
],
'is' => 
[
'IS',
'Iota_Subscript',
240,
],
'kv' => 
[
'KV',
'Kana_Voicing',
8,
],
'l' => 
[
'L',
'Left',
224,
],
'nk' => 
[
'NK',
'Nukta',
7,
],
'nr' => 
[
'NR',
'Not_Reordered',
0,
],
'ov' => 
[
'OV',
'Overlay',
1,
],
'r' => 
[
'R',
'Right',
226,
],
'vr' => 
[
'VR',
'Virama',
9,
],
},
'ce' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'ci' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'compex' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'cwcf' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'cwcm' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'cwkcf' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'cwl' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'cwt' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'cwu' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'dash' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'dep' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'di' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'dia' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'dt' => 
{
'can' => 
[
'Can',
'Canonical',
],
'com' => 
[
'Com',
'Compat',
],
'enc' => 
[
'Enc',
'Circle',
],
'fin' => 
[
'Fin',
'Final',
],
'font' => 
[
'Font',
],
'fra' => 
[
'Fra',
'Fraction',
],
'init' => 
[
'Init',
'Initial',
],
'iso' => 
[
'Iso',
'Isolated',
],
'med' => 
[
'Med',
'Medial',
],
'nar' => 
[
'Nar',
'Narrow',
],
'nb' => 
[
'Nb',
'Nobreak',
],
'noncanon' => 
[
'Non_Canon',
'Non_Canonical',
],
'none' => 
[
'None',
],
'sml' => 
[
'Sml',
'Small',
],
'sqr' => 
[
'Sqr',
'Square',
],
'sub' => 
[
'Sub',
],
'sup' => 
[
'Sup',
'Super',
],
'vert' => 
[
'Vert',
'Vertical',
],
'wide' => 
[
'Wide',
],
},
'ea' => 
{
'a' => 
[
'A',
'Ambiguous',
],
'f' => 
[
'F',
'Fullwidth',
],
'h' => 
[
'H',
'Halfwidth',
],
'n' => 
[
'N',
'Neutral',
],
'na' => 
[
'Na',
'Narrow',
],
'w' => 
[
'W',
'Wide',
],
},
'ebase' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'ecomp' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'emod' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'emoji' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'epres' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'ext' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'extpict' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'gc' => 
{
'c' => 
[
'C',
'Other',
],
'cc' => 
[
'Cc',
'Control',
'Cntrl',
],
'cf' => 
[
'Cf',
'Format',
],
'cn' => 
[
'Cn',
'Unassigned',
],
'co' => 
[
'Co',
'Private_Use',
],
'cs' => 
[
'Cs',
'Surrogate',
],
'l' => 
[
'L',
'Letter',
],
'lc' => 
[
'LC',
'Cased_Letter',
],
'll' => 
[
'Ll',
'Lowercase_Letter',
],
'lm' => 
[
'Lm',
'Modifier_Letter',
],
'lo' => 
[
'Lo',
'Other_Letter',
],
'lt' => 
[
'Lt',
'Titlecase_Letter',
],
'lu' => 
[
'Lu',
'Uppercase_Letter',
],
'm' => 
[
'M',
'Mark',
'Combining_Mark',
],
'mc' => 
[
'Mc',
'Spacing_Mark',
],
'me' => 
[
'Me',
'Enclosing_Mark',
],
'mn' => 
[
'Mn',
'Nonspacing_Mark',
],
'n' => 
[
'N',
'Number',
],
'nd' => 
[
'Nd',
'Decimal_Number',
'Digit',
],
'nl' => 
[
'Nl',
'Letter_Number',
],
'no' => 
[
'No',
'Other_Number',
],
'p' => 
[
'P',
'Punctuation',
'Punct',
],
'pc' => 
[
'Pc',
'Connector_Punctuation',
],
'pd' => 
[
'Pd',
'Dash_Punctuation',
],
'pe' => 
[
'Pe',
'Close_Punctuation',
],
'pf' => 
[
'Pf',
'Final_Punctuation',
],
'pi' => 
[
'Pi',
'Initial_Punctuation',
],
'po' => 
[
'Po',
'Other_Punctuation',
],
'ps' => 
[
'Ps',
'Open_Punctuation',
],
's' => 
[
'S',
'Symbol',
],
'sc' => 
[
'Sc',
'Currency_Symbol',
],
'sk' => 
[
'Sk',
'Modifier_Symbol',
],
'sm' => 
[
'Sm',
'Math_Symbol',
],
'so' => 
[
'So',
'Other_Symbol',
],
'z' => 
[
'Z',
'Separator',
],
'zl' => 
[
'Zl',
'Line_Separator',
],
'zp' => 
[
'Zp',
'Paragraph_Separator',
],
'zs' => 
[
'Zs',
'Space_Separator',
],
},
'gcb' => 
{
'cn' => 
[
'CN',
'Control',
],
'cr' => 
[
'CR',
],
'eb' => 
[
'EB',
'E_Base',
],
'ebg' => 
[
'EBG',
'E_Base_GAZ',
],
'em' => 
[
'EM',
'E_Modifier',
],
'ex' => 
[
'EX',
'Extend',
],
'gaz' => 
[
'GAZ',
'Glue_After_Zwj',
],
'l' => 
[
'L',
],
'lf' => 
[
'LF',
],
'lv' => 
[
'LV',
],
'lvt' => 
[
'LVT',
],
'pp' => 
[
'PP',
'Prepend',
],
'ri' => 
[
'RI',
'Regional_Indicator',
],
'sm' => 
[
'SM',
'SpacingMark',
],
't' => 
[
'T',
],
'v' => 
[
'V',
],
'xx' => 
[
'XX',
'Other',
],
'zwj' => 
[
'ZWJ',
],
},
'grbase' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'grext' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'hex' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'hst' => 
{
'l' => 
[
'L',
'Leading_Jamo',
],
'lv' => 
[
'LV',
'LV_Syllable',
],
'lvt' => 
[
'LVT',
'LVT_Syllable',
],
'na' => 
[
'NA',
'Not_Applicable',
],
't' => 
[
'T',
'Trailing_Jamo',
],
'v' => 
[
'V',
'Vowel_Jamo',
],
},
'hyphen' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'idc' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'identifierstatus' => 
{
'allowed' => 
[
'Allowed',
],
'restricted' => 
[
'Restricted',
],
},
'identifiertype' => 
{
'defaultignorable' => 
[
'Default_Ignorable',
],
'deprecated' => 
[
'Deprecated',
],
'exclusion' => 
[
'Exclusion',
],
'inclusion' => 
[
'Inclusion',
],
'limiteduse' => 
[
'Limited_Use',
],
'notcharacter' => 
[
'Not_Character',
],
'notnfkc' => 
[
'Not_NFKC',
],
'notxid' => 
[
'Not_XID',
],
'obsolete' => 
[
'Obsolete',
],
'recommended' => 
[
'Recommended',
],
'technical' => 
[
'Technical',
],
'uncommonuse' => 
[
'Uncommon_Use',
],
},
'ideo' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'ids' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'idsb' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'idst' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'in' => 
{
'1.1' => 
[
'1.1',
'1.1',
'V1_1',
],
'10.0' => 
[
'10.0',
'10.0',
'V10_0',
],
'11.0' => 
[
'11.0',
'11.0',
'V11_0',
],
'12.0' => 
[
'12.0',
'12.0',
'V12_0',
],
'12.1' => 
[
'12.1',
'12.1',
'V12_1',
],
'13.0' => 
[
'13.0',
'13.0',
'V13_0',
],
'14.0' => 
[
'14.0',
'14.0',
'V14_0',
],
'15.0' => 
[
'15.0',
'15.0',
'V15_0',
],
'2.0' => 
[
'2.0',
'2.0',
'V2_0',
],
'2.1' => 
[
'2.1',
'2.1',
'V2_1',
],
'3.0' => 
[
'3.0',
'3.0',
'V3_0',
],
'3.1' => 
[
'3.1',
'3.1',
'V3_1',
],
'3.2' => 
[
'3.2',
'3.2',
'V3_2',
],
'4.0' => 
[
'4.0',
'4.0',
'V4_0',
],
'4.1' => 
[
'4.1',
'4.1',
'V4_1',
],
'5.0' => 
[
'5.0',
'5.0',
'V5_0',
],
'5.1' => 
[
'5.1',
'5.1',
'V5_1',
],
'5.2' => 
[
'5.2',
'5.2',
'V5_2',
],
'6.0' => 
[
'6.0',
'6.0',
'V6_0',
],
'6.1' => 
[
'6.1',
'6.1',
'V6_1',
],
'6.2' => 
[
'6.2',
'6.2',
'V6_2',
],
'6.3' => 
[
'6.3',
'6.3',
'V6_3',
],
'7.0' => 
[
'7.0',
'7.0',
'V7_0',
],
'8.0' => 
[
'8.0',
'8.0',
'V8_0',
],
'9.0' => 
[
'9.0',
'9.0',
'V9_0',
],
'unassigned' => 
[
'Unassigned',
'Unassigned',
'NA',
],
},
'inpc' => 
{
'bottom' => 
[
'Bottom',
],
'bottomandleft' => 
[
'Bottom_And_Left',
],
'bottomandright' => 
[
'Bottom_And_Right',
],
'left' => 
[
'Left',
],
'leftandright' => 
[
'Left_And_Right',
],
'na' => 
[
'NA',
],
'overstruck' => 
[
'Overstruck',
],
'right' => 
[
'Right',
],
'top' => 
[
'Top',
],
'topandbottom' => 
[
'Top_And_Bottom',
],
'topandbottomandleft' => 
[
'Top_And_Bottom_And_Left',
],
'topandbottomandright' => 
[
'Top_And_Bottom_And_Right',
],
'topandleft' => 
[
'Top_And_Left',
],
'topandleftandright' => 
[
'Top_And_Left_And_Right',
],
'topandright' => 
[
'Top_And_Right',
],
'visualorderleft' => 
[
'Visual_Order_Left',
],
},
'insc' => 
{
'avagraha' => 
[
'Avagraha',
],
'bindu' => 
[
'Bindu',
],
'brahmijoiningnumber' => 
[
'Brahmi_Joining_Number',
],
'cantillationmark' => 
[
'Cantillation_Mark',
],
'consonant' => 
[
'Consonant',
],
'consonantdead' => 
[
'Consonant_Dead',
],
'consonantfinal' => 
[
'Consonant_Final',
],
'consonantheadletter' => 
[
'Consonant_Head_Letter',
],
'consonantinitialpostfixed' => 
[
'Consonant_Initial_Postfixed',
],
'consonantkiller' => 
[
'Consonant_Killer',
],
'consonantmedial' => 
[
'Consonant_Medial',
],
'consonantplaceholder' => 
[
'Consonant_Placeholder',
],
'consonantprecedingrepha' => 
[
'Consonant_Preceding_Repha',
],
'consonantprefixed' => 
[
'Consonant_Prefixed',
],
'consonantsubjoined' => 
[
'Consonant_Subjoined',
],
'consonantsucceedingrepha' => 
[
'Consonant_Succeeding_Repha',
],
'consonantwithstacker' => 
[
'Consonant_With_Stacker',
],
'geminationmark' => 
[
'Gemination_Mark',
],
'invisiblestacker' => 
[
'Invisible_Stacker',
],
'joiner' => 
[
'Joiner',
],
'modifyingletter' => 
[
'Modifying_Letter',
],
'nonjoiner' => 
[
'Non_Joiner',
],
'nukta' => 
[
'Nukta',
],
'number' => 
[
'Number',
],
'numberjoiner' => 
[
'Number_Joiner',
],
'other' => 
[
'Other',
],
'purekiller' => 
[
'Pure_Killer',
],
'registershifter' => 
[
'Register_Shifter',
],
'syllablemodifier' => 
[
'Syllable_Modifier',
],
'toneletter' => 
[
'Tone_Letter',
],
'tonemark' => 
[
'Tone_Mark',
],
'virama' => 
[
'Virama',
],
'visarga' => 
[
'Visarga',
],
'vowel' => 
[
'Vowel',
],
'voweldependent' => 
[
'Vowel_Dependent',
],
'vowelindependent' => 
[
'Vowel_Independent',
],
},
'jg' => 
{
'africanfeh' => 
[
'African_Feh',
],
'africannoon' => 
[
'African_Noon',
],
'africanqaf' => 
[
'African_Qaf',
],
'ain' => 
[
'Ain',
],
'alaph' => 
[
'Alaph',
],
'alef' => 
[
'Alef',
],
'beh' => 
[
'Beh',
],
'beth' => 
[
'Beth',
],
'burushaskiyehbarree' => 
[
'Burushaski_Yeh_Barree',
],
'dal' => 
[
'Dal',
],
'dalathrish' => 
[
'Dalath_Rish',
],
'e' => 
[
'E',
],
'farsiyeh' => 
[
'Farsi_Yeh',
],
'fe' => 
[
'Fe',
],
'feh' => 
[
'Feh',
],
'finalsemkath' => 
[
'Final_Semkath',
],
'gaf' => 
[
'Gaf',
],
'gamal' => 
[
'Gamal',
],
'hah' => 
[
'Hah',
],
'hanifirohingyakinnaya' => 
[
'Hanifi_Rohingya_Kinna_Ya',
],
'hanifirohingyapa' => 
[
'Hanifi_Rohingya_Pa',
],
'he' => 
[
'He',
],
'heh' => 
[
'Heh',
],
'hehgoal' => 
[
'Heh_Goal',
],
'heth' => 
[
'Heth',
],
'kaf' => 
[
'Kaf',
],
'kaph' => 
[
'Kaph',
],
'khaph' => 
[
'Khaph',
],
'knottedheh' => 
[
'Knotted_Heh',
],
'lam' => 
[
'Lam',
],
'lamadh' => 
[
'Lamadh',
],
'malayalambha' => 
[
'Malayalam_Bha',
],
'malayalamja' => 
[
'Malayalam_Ja',
],
'malayalamlla' => 
[
'Malayalam_Lla',
],
'malayalamllla' => 
[
'Malayalam_Llla',
],
'malayalamnga' => 
[
'Malayalam_Nga',
],
'malayalamnna' => 
[
'Malayalam_Nna',
],
'malayalamnnna' => 
[
'Malayalam_Nnna',
],
'malayalamnya' => 
[
'Malayalam_Nya',
],
'malayalamra' => 
[
'Malayalam_Ra',
],
'malayalamssa' => 
[
'Malayalam_Ssa',
],
'malayalamtta' => 
[
'Malayalam_Tta',
],
'manichaeanaleph' => 
[
'Manichaean_Aleph',
],
'manichaeanayin' => 
[
'Manichaean_Ayin',
],
'manichaeanbeth' => 
[
'Manichaean_Beth',
],
'manichaeandaleth' => 
[
'Manichaean_Daleth',
],
'manichaeandhamedh' => 
[
'Manichaean_Dhamedh',
],
'manichaeanfive' => 
[
'Manichaean_Five',
],
'manichaeangimel' => 
[
'Manichaean_Gimel',
],
'manichaeanheth' => 
[
'Manichaean_Heth',
],
'manichaeanhundred' => 
[
'Manichaean_Hundred',
],
'manichaeankaph' => 
[
'Manichaean_Kaph',
],
'manichaeanlamedh' => 
[
'Manichaean_Lamedh',
],
'manichaeanmem' => 
[
'Manichaean_Mem',
],
'manichaeannun' => 
[
'Manichaean_Nun',
],
'manichaeanone' => 
[
'Manichaean_One',
],
'manichaeanpe' => 
[
'Manichaean_Pe',
],
'manichaeanqoph' => 
[
'Manichaean_Qoph',
],
'manichaeanresh' => 
[
'Manichaean_Resh',
],
'manichaeansadhe' => 
[
'Manichaean_Sadhe',
],
'manichaeansamekh' => 
[
'Manichaean_Samekh',
],
'manichaeantaw' => 
[
'Manichaean_Taw',
],
'manichaeanten' => 
[
'Manichaean_Ten',
],
'manichaeanteth' => 
[
'Manichaean_Teth',
],
'manichaeanthamedh' => 
[
'Manichaean_Thamedh',
],
'manichaeantwenty' => 
[
'Manichaean_Twenty',
],
'manichaeanwaw' => 
[
'Manichaean_Waw',
],
'manichaeanyodh' => 
[
'Manichaean_Yodh',
],
'manichaeanzayin' => 
[
'Manichaean_Zayin',
],
'meem' => 
[
'Meem',
],
'mim' => 
[
'Mim',
],
'nojoininggroup' => 
[
'No_Joining_Group',
],
'noon' => 
[
'Noon',
],
'nun' => 
[
'Nun',
],
'nya' => 
[
'Nya',
],
'pe' => 
[
'Pe',
],
'qaf' => 
[
'Qaf',
],
'qaph' => 
[
'Qaph',
],
'reh' => 
[
'Reh',
],
'reversedpe' => 
[
'Reversed_Pe',
],
'rohingyayeh' => 
[
'Rohingya_Yeh',
],
'sad' => 
[
'Sad',
],
'sadhe' => 
[
'Sadhe',
],
'seen' => 
[
'Seen',
],
'semkath' => 
[
'Semkath',
],
'shin' => 
[
'Shin',
],
'straightwaw' => 
[
'Straight_Waw',
],
'swashkaf' => 
[
'Swash_Kaf',
],
'syriacwaw' => 
[
'Syriac_Waw',
],
'tah' => 
[
'Tah',
],
'taw' => 
[
'Taw',
],
'tehmarbuta' => 
[
'Teh_Marbuta',
],
'tehmarbutagoal' => 
[
'Teh_Marbuta_Goal',
'Hamza_On_Heh_Goal',
],
'teth' => 
[
'Teth',
],
'thinyeh' => 
[
'Thin_Yeh',
],
'verticaltail' => 
[
'Vertical_Tail',
],
'waw' => 
[
'Waw',
],
'yeh' => 
[
'Yeh',
],
'yehbarree' => 
[
'Yeh_Barree',
],
'yehwithtail' => 
[
'Yeh_With_Tail',
],
'yudh' => 
[
'Yudh',
],
'yudhhe' => 
[
'Yudh_He',
],
'zain' => 
[
'Zain',
],
'zhain' => 
[
'Zhain',
],
},
'joinc' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'jt' => 
{
'c' => 
[
'C',
'Join_Causing',
],
'd' => 
[
'D',
'Dual_Joining',
],
'l' => 
[
'L',
'Left_Joining',
],
'r' => 
[
'R',
'Right_Joining',
],
't' => 
[
'T',
'Transparent',
],
'u' => 
[
'U',
'Non_Joining',
],
},
'lb' => 
{
'ai' => 
[
'AI',
'Ambiguous',
],
'al' => 
[
'AL',
'Alphabetic',
],
'b2' => 
[
'B2',
'Break_Both',
],
'ba' => 
[
'BA',
'Break_After',
],
'bb' => 
[
'BB',
'Break_Before',
],
'bk' => 
[
'BK',
'Mandatory_Break',
],
'cb' => 
[
'CB',
'Contingent_Break',
],
'cj' => 
[
'CJ',
'Conditional_Japanese_Starter',
],
'cl' => 
[
'CL',
'Close_Punctuation',
],
'cm' => 
[
'CM',
'Combining_Mark',
],
'cp' => 
[
'CP',
'Close_Parenthesis',
],
'cr' => 
[
'CR',
'Carriage_Return',
],
'eb' => 
[
'EB',
'E_Base',
],
'em' => 
[
'EM',
'E_Modifier',
],
'ex' => 
[
'EX',
'Exclamation',
],
'gl' => 
[
'GL',
'Glue',
],
'h2' => 
[
'H2',
],
'h3' => 
[
'H3',
],
'hl' => 
[
'HL',
'Hebrew_Letter',
],
'hy' => 
[
'HY',
'Hyphen',
],
'id' => 
[
'ID',
'Ideographic',
],
'in' => 
[
'IN',
'Inseparable',
'Inseperable',
],
'is' => 
[
'IS',
'Infix_Numeric',
],
'jl' => 
[
'JL',
],
'jt' => 
[
'JT',
],
'jv' => 
[
'JV',
],
'lf' => 
[
'LF',
'Line_Feed',
],
'nl' => 
[
'NL',
'Next_Line',
],
'ns' => 
[
'NS',
'Nonstarter',
],
'nu' => 
[
'NU',
'Numeric',
],
'op' => 
[
'OP',
'Open_Punctuation',
],
'po' => 
[
'PO',
'Postfix_Numeric',
],
'pr' => 
[
'PR',
'Prefix_Numeric',
],
'qu' => 
[
'QU',
'Quotation',
],
'ri' => 
[
'RI',
'Regional_Indicator',
],
'sa' => 
[
'SA',
'Complex_Context',
],
'sg' => 
[
'SG',
'Surrogate',
],
'sp' => 
[
'SP',
'Space',
],
'sy' => 
[
'SY',
'Break_Symbols',
],
'wj' => 
[
'WJ',
'Word_Joiner',
],
'xx' => 
[
'XX',
'Unknown',
],
'zw' => 
[
'ZW',
'ZWSpace',
],
'zwj' => 
[
'ZWJ',
],
},
'loe' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'lower' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'math' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'nchar' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'nfcqc' => 
{
'm' => 
[
'M',
'Maybe',
],
'n' => 
[
'N',
'No',
],
'y' => 
[
'Y',
'Yes',
],
},
'nfdqc' => 
{
'n' => 
[
'N',
'No',
],
'y' => 
[
'Y',
'Yes',
],
},
'nfkcqc' => 
{
'm' => 
[
'M',
'Maybe',
],
'n' => 
[
'N',
'No',
],
'y' => 
[
'Y',
'Yes',
],
},
'nfkdqc' => 
{
'n' => 
[
'N',
'No',
],
'y' => 
[
'Y',
'Yes',
],
},
'nt' => 
{
'de' => 
[
'De',
'Decimal',
],
'di' => 
[
'Di',
'Digit',
],
'none' => 
[
'None',
],
'nu' => 
[
'Nu',
'Numeric',
],
},
'nv' => 
{
'-1/2' => 
[
'-1/2',
],
0 => 
[
0,
],
1 => 
[
1,
],
'1/10' => 
[
'1/10',
],
'1/12' => 
[
'1/12',
],
'1/16' => 
[
'1/16',
],
'1/160' => 
[
'1/160',
],
'1/2' => 
[
'1/2',
],
'1/20' => 
[
'1/20',
],
'1/3' => 
[
'1/3',
],
'1/32' => 
[
'1/32',
],
'1/320' => 
[
'1/320',
],
'1/4' => 
[
'1/4',
],
'1/40' => 
[
'1/40',
],
'1/5' => 
[
'1/5',
],
'1/6' => 
[
'1/6',
],
'1/64' => 
[
'1/64',
],
'1/7' => 
[
'1/7',
],
'1/8' => 
[
'1/8',
],
'1/80' => 
[
'1/80',
],
'1/9' => 
[
'1/9',
],
10 => 
[
10,
],
100 => 
[
100,
],
1000 => 
[
1000,
],
10000 => 
[
10000,
],
100000 => 
[
100000,
],
1000000 => 
[
1000000,
],
10000000 => 
[
10000000,
],
100000000 => 
[
100000000,
],
10000000000 => 
[
10000000000,
],
1000000000000 => 
[
1000000000000,
],
11 => 
[
11,
],
'11/12' => 
[
'11/12',
],
'11/2' => 
[
'11/2',
],
12 => 
[
12,
],
13 => 
[
13,
],
'13/2' => 
[
'13/2',
],
14 => 
[
14,
],
15 => 
[
15,
],
'15/2' => 
[
'15/2',
],
16 => 
[
16,
],
17 => 
[
17,
],
'17/2' => 
[
'17/2',
],
18 => 
[
18,
],
19 => 
[
19,
],
2 => 
[
2,
],
'2/3' => 
[
'2/3',
],
'2/5' => 
[
'2/5',
],
20 => 
[
20,
],
200 => 
[
200,
],
2000 => 
[
2000,
],
20000 => 
[
20000,
],
200000 => 
[
200000,
],
20000000 => 
[
20000000,
],
21 => 
[
21,
],
216000 => 
[
216000,
],
22 => 
[
22,
],
23 => 
[
23,
],
24 => 
[
24,
],
25 => 
[
25,
],
26 => 
[
26,
],
27 => 
[
27,
],
28 => 
[
28,
],
29 => 
[
29,
],
3 => 
[
3,
],
'3/16' => 
[
'3/16',
],
'3/2' => 
[
'3/2',
],
'3/20' => 
[
'3/20',
],
'3/4' => 
[
'3/4',
],
'3/5' => 
[
'3/5',
],
'3/64' => 
[
'3/64',
],
'3/8' => 
[
'3/8',
],
'3/80' => 
[
'3/80',
],
30 => 
[
30,
],
300 => 
[
300,
],
3000 => 
[
3000,
],
30000 => 
[
30000,
],
300000 => 
[
300000,
],
31 => 
[
31,
],
32 => 
[
32,
],
33 => 
[
33,
],
34 => 
[
34,
],
35 => 
[
35,
],
36 => 
[
36,
],
37 => 
[
37,
],
38 => 
[
38,
],
39 => 
[
39,
],
4 => 
[
4,
],
'4/5' => 
[
'4/5',
],
40 => 
[
40,
],
400 => 
[
400,
],
4000 => 
[
4000,
],
40000 => 
[
40000,
],
400000 => 
[
400000,
],
41 => 
[
41,
],
42 => 
[
42,
],
43 => 
[
43,
],
432000 => 
[
432000,
],
44 => 
[
44,
],
45 => 
[
45,
],
46 => 
[
46,
],
47 => 
[
47,
],
48 => 
[
48,
],
49 => 
[
49,
],
5 => 
[
5,
],
'5/12' => 
[
'5/12',
],
'5/2' => 
[
'5/2',
],
'5/6' => 
[
'5/6',
],
'5/8' => 
[
'5/8',
],
50 => 
[
50,
],
500 => 
[
500,
],
5000 => 
[
5000,
],
50000 => 
[
50000,
],
500000 => 
[
500000,
],
6 => 
[
6,
],
60 => 
[
60,
],
600 => 
[
600,
],
6000 => 
[
6000,
],
60000 => 
[
60000,
],
600000 => 
[
600000,
],
7 => 
[
7,
],
'7/12' => 
[
'7/12',
],
'7/2' => 
[
'7/2',
],
'7/8' => 
[
'7/8',
],
70 => 
[
70,
],
700 => 
[
700,
],
7000 => 
[
7000,
],
70000 => 
[
70000,
],
700000 => 
[
700000,
],
8 => 
[
8,
],
80 => 
[
80,
],
800 => 
[
800,
],
8000 => 
[
8000,
],
80000 => 
[
80000,
],
800000 => 
[
800000,
],
9 => 
[
9,
],
'9/2' => 
[
'9/2',
],
90 => 
[
90,
],
900 => 
[
900,
],
9000 => 
[
9000,
],
90000 => 
[
90000,
],
900000 => 
[
900000,
],
'nan' => 
[
'NaN',
],
},
'patsyn' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'patws' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'pcm' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'qmark' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'radical' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'ri' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'sb' => 
{
'at' => 
[
'AT',
'ATerm',
],
'cl' => 
[
'CL',
'Close',
],
'cr' => 
[
'CR',
],
'ex' => 
[
'EX',
'Extend',
],
'fo' => 
[
'FO',
'Format',
],
'le' => 
[
'LE',
'OLetter',
],
'lf' => 
[
'LF',
],
'lo' => 
[
'LO',
'Lower',
],
'nu' => 
[
'NU',
'Numeric',
],
'sc' => 
[
'SC',
'SContinue',
],
'se' => 
[
'SE',
'Sep',
],
'sp' => 
[
'SP',
'Sp',
],
'st' => 
[
'ST',
'STerm',
],
'up' => 
[
'UP',
'Upper',
],
'xx' => 
[
'XX',
'Other',
],
},
'sc' => 
{
'adlm' => 
[
'Adlm',
'Adlam',
],
'aghb' => 
[
'Aghb',
'Caucasian_Albanian',
],
'ahom' => 
[
'Ahom',
],
'arab' => 
[
'Arab',
'Arabic',
],
'armi' => 
[
'Armi',
'Imperial_Aramaic',
],
'armn' => 
[
'Armn',
'Armenian',
],
'avst' => 
[
'Avst',
'Avestan',
],
'bali' => 
[
'Bali',
'Balinese',
],
'bamu' => 
[
'Bamu',
'Bamum',
],
'bass' => 
[
'Bass',
'Bassa_Vah',
],
'batk' => 
[
'Batk',
'Batak',
],
'beng' => 
[
'Beng',
'Bengali',
],
'bhks' => 
[
'Bhks',
'Bhaiksuki',
],
'bopo' => 
[
'Bopo',
'Bopomofo',
],
'brah' => 
[
'Brah',
'Brahmi',
],
'brai' => 
[
'Brai',
'Braille',
],
'bugi' => 
[
'Bugi',
'Buginese',
],
'buhd' => 
[
'Buhd',
'Buhid',
],
'cakm' => 
[
'Cakm',
'Chakma',
],
'cans' => 
[
'Cans',
'Canadian_Aboriginal',
],
'cari' => 
[
'Cari',
'Carian',
],
'cham' => 
[
'Cham',
],
'cher' => 
[
'Cher',
'Cherokee',
],
'chrs' => 
[
'Chrs',
'Chorasmian',
],
'copt' => 
[
'Copt',
'Coptic',
'Qaac',
],
'cpmn' => 
[
'Cpmn',
'Cypro_Minoan',
],
'cprt' => 
[
'Cprt',
'Cypriot',
],
'cyrl' => 
[
'Cyrl',
'Cyrillic',
],
'deva' => 
[
'Deva',
'Devanagari',
],
'diak' => 
[
'Diak',
'Dives_Akuru',
],
'dogr' => 
[
'Dogr',
'Dogra',
],
'dsrt' => 
[
'Dsrt',
'Deseret',
],
'dupl' => 
[
'Dupl',
'Duployan',
],
'egyp' => 
[
'Egyp',
'Egyptian_Hieroglyphs',
],
'elba' => 
[
'Elba',
'Elbasan',
],
'elym' => 
[
'Elym',
'Elymaic',
],
'ethi' => 
[
'Ethi',
'Ethiopic',
],
'geor' => 
[
'Geor',
'Georgian',
],
'glag' => 
[
'Glag',
'Glagolitic',
],
'gong' => 
[
'Gong',
'Gunjala_Gondi',
],
'gonm' => 
[
'Gonm',
'Masaram_Gondi',
],
'goth' => 
[
'Goth',
'Gothic',
],
'gran' => 
[
'Gran',
'Grantha',
],
'grek' => 
[
'Grek',
'Greek',
],
'gujr' => 
[
'Gujr',
'Gujarati',
],
'guru' => 
[
'Guru',
'Gurmukhi',
],
'hang' => 
[
'Hang',
'Hangul',
],
'hani' => 
[
'Hani',
'Han',
],
'hano' => 
[
'Hano',
'Hanunoo',
],
'hatr' => 
[
'Hatr',
'Hatran',
],
'hebr' => 
[
'Hebr',
'Hebrew',
],
'hira' => 
[
'Hira',
'Hiragana',
],
'hluw' => 
[
'Hluw',
'Anatolian_Hieroglyphs',
],
'hmng' => 
[
'Hmng',
'Pahawh_Hmong',
],
'hmnp' => 
[
'Hmnp',
'Nyiakeng_Puachue_Hmong',
],
'hrkt' => 
[
'Hrkt',
'Katakana_Or_Hiragana',
],
'hung' => 
[
'Hung',
'Old_Hungarian',
],
'ital' => 
[
'Ital',
'Old_Italic',
],
'java' => 
[
'Java',
'Javanese',
],
'kali' => 
[
'Kali',
'Kayah_Li',
],
'kana' => 
[
'Kana',
'Katakana',
],
'kawi' => 
[
'Kawi',
],
'khar' => 
[
'Khar',
'Kharoshthi',
],
'khmr' => 
[
'Khmr',
'Khmer',
],
'khoj' => 
[
'Khoj',
'Khojki',
],
'kits' => 
[
'Kits',
'Khitan_Small_Script',
],
'knda' => 
[
'Knda',
'Kannada',
],
'kthi' => 
[
'Kthi',
'Kaithi',
],
'lana' => 
[
'Lana',
'Tai_Tham',
],
'laoo' => 
[
'Laoo',
'Lao',
],
'latn' => 
[
'Latn',
'Latin',
],
'lepc' => 
[
'Lepc',
'Lepcha',
],
'limb' => 
[
'Limb',
'Limbu',
],
'lina' => 
[
'Lina',
'Linear_A',
],
'linb' => 
[
'Linb',
'Linear_B',
],
'lisu' => 
[
'Lisu',
],
'lyci' => 
[
'Lyci',
'Lycian',
],
'lydi' => 
[
'Lydi',
'Lydian',
],
'mahj' => 
[
'Mahj',
'Mahajani',
],
'maka' => 
[
'Maka',
'Makasar',
],
'mand' => 
[
'Mand',
'Mandaic',
],
'mani' => 
[
'Mani',
'Manichaean',
],
'marc' => 
[
'Marc',
'Marchen',
],
'medf' => 
[
'Medf',
'Medefaidrin',
],
'mend' => 
[
'Mend',
'Mende_Kikakui',
],
'merc' => 
[
'Merc',
'Meroitic_Cursive',
],
'mero' => 
[
'Mero',
'Meroitic_Hieroglyphs',
],
'mlym' => 
[
'Mlym',
'Malayalam',
],
'modi' => 
[
'Modi',
],
'mong' => 
[
'Mong',
'Mongolian',
],
'mroo' => 
[
'Mroo',
'Mro',
],
'mtei' => 
[
'Mtei',
'Meetei_Mayek',
],
'mult' => 
[
'Mult',
'Multani',
],
'mymr' => 
[
'Mymr',
'Myanmar',
],
'nagm' => 
[
'Nagm',
'Nag_Mundari',
],
'nand' => 
[
'Nand',
'Nandinagari',
],
'narb' => 
[
'Narb',
'Old_North_Arabian',
],
'nbat' => 
[
'Nbat',
'Nabataean',
],
'newa' => 
[
'Newa',
],
'nkoo' => 
[
'Nkoo',
'Nko',
],
'nshu' => 
[
'Nshu',
'Nushu',
],
'ogam' => 
[
'Ogam',
'Ogham',
],
'olck' => 
[
'Olck',
'Ol_Chiki',
],
'orkh' => 
[
'Orkh',
'Old_Turkic',
],
'orya' => 
[
'Orya',
'Oriya',
],
'osge' => 
[
'Osge',
'Osage',
],
'osma' => 
[
'Osma',
'Osmanya',
],
'ougr' => 
[
'Ougr',
'Old_Uyghur',
],
'palm' => 
[
'Palm',
'Palmyrene',
],
'pauc' => 
[
'Pauc',
'Pau_Cin_Hau',
],
'perm' => 
[
'Perm',
'Old_Permic',
],
'phag' => 
[
'Phag',
'Phags_Pa',
],
'phli' => 
[
'Phli',
'Inscriptional_Pahlavi',
],
'phlp' => 
[
'Phlp',
'Psalter_Pahlavi',
],
'phnx' => 
[
'Phnx',
'Phoenician',
],
'plrd' => 
[
'Plrd',
'Miao',
],
'prti' => 
[
'Prti',
'Inscriptional_Parthian',
],
'rjng' => 
[
'Rjng',
'Rejang',
],
'rohg' => 
[
'Rohg',
'Hanifi_Rohingya',
],
'runr' => 
[
'Runr',
'Runic',
],
'samr' => 
[
'Samr',
'Samaritan',
],
'sarb' => 
[
'Sarb',
'Old_South_Arabian',
],
'saur' => 
[
'Saur',
'Saurashtra',
],
'sgnw' => 
[
'Sgnw',
'SignWriting',
],
'shaw' => 
[
'Shaw',
'Shavian',
],
'shrd' => 
[
'Shrd',
'Sharada',
],
'sidd' => 
[
'Sidd',
'Siddham',
],
'sind' => 
[
'Sind',
'Khudawadi',
],
'sinh' => 
[
'Sinh',
'Sinhala',
],
'sogd' => 
[
'Sogd',
'Sogdian',
],
'sogo' => 
[
'Sogo',
'Old_Sogdian',
],
'sora' => 
[
'Sora',
'Sora_Sompeng',
],
'soyo' => 
[
'Soyo',
'Soyombo',
],
'sund' => 
[
'Sund',
'Sundanese',
],
'sylo' => 
[
'Sylo',
'Syloti_Nagri',
],
'syrc' => 
[
'Syrc',
'Syriac',
],
'tagb' => 
[
'Tagb',
'Tagbanwa',
],
'takr' => 
[
'Takr',
'Takri',
],
'tale' => 
[
'Tale',
'Tai_Le',
],
'talu' => 
[
'Talu',
'New_Tai_Lue',
],
'taml' => 
[
'Taml',
'Tamil',
],
'tang' => 
[
'Tang',
'Tangut',
],
'tavt' => 
[
'Tavt',
'Tai_Viet',
],
'telu' => 
[
'Telu',
'Telugu',
],
'tfng' => 
[
'Tfng',
'Tifinagh',
],
'tglg' => 
[
'Tglg',
'Tagalog',
],
'thaa' => 
[
'Thaa',
'Thaana',
],
'thai' => 
[
'Thai',
],
'tibt' => 
[
'Tibt',
'Tibetan',
],
'tirh' => 
[
'Tirh',
'Tirhuta',
],
'tnsa' => 
[
'Tnsa',
'Tangsa',
],
'toto' => 
[
'Toto',
],
'ugar' => 
[
'Ugar',
'Ugaritic',
],
'vaii' => 
[
'Vaii',
'Vai',
],
'vith' => 
[
'Vith',
'Vithkuqi',
],
'wara' => 
[
'Wara',
'Warang_Citi',
],
'wcho' => 
[
'Wcho',
'Wancho',
],
'xpeo' => 
[
'Xpeo',
'Old_Persian',
],
'xsux' => 
[
'Xsux',
'Cuneiform',
],
'yezi' => 
[
'Yezi',
'Yezidi',
],
'yiii' => 
[
'Yiii',
'Yi',
],
'zanb' => 
[
'Zanb',
'Zanabazar_Square',
],
'zinh' => 
[
'Zinh',
'Inherited',
'Qaai',
],
'zyyy' => 
[
'Zyyy',
'Common',
],
'zzzz' => 
[
'Zzzz',
'Unknown',
],
},
'scx' => 
{
'adlm' => 
[
'Adlm',
'Adlam',
],
'aghb' => 
[
'Aghb',
'Caucasian_Albanian',
],
'ahom' => 
[
'Ahom',
],
'arab' => 
[
'Arab',
'Arabic',
],
'armi' => 
[
'Armi',
'Imperial_Aramaic',
],
'armn' => 
[
'Armn',
'Armenian',
],
'avst' => 
[
'Avst',
'Avestan',
],
'bali' => 
[
'Bali',
'Balinese',
],
'bamu' => 
[
'Bamu',
'Bamum',
],
'bass' => 
[
'Bass',
'Bassa_Vah',
],
'batk' => 
[
'Batk',
'Batak',
],
'beng' => 
[
'Beng',
'Bengali',
],
'bhks' => 
[
'Bhks',
'Bhaiksuki',
],
'bopo' => 
[
'Bopo',
'Bopomofo',
],
'brah' => 
[
'Brah',
'Brahmi',
],
'brai' => 
[
'Brai',
'Braille',
],
'bugi' => 
[
'Bugi',
'Buginese',
],
'buhd' => 
[
'Buhd',
'Buhid',
],
'cakm' => 
[
'Cakm',
'Chakma',
],
'cans' => 
[
'Cans',
'Canadian_Aboriginal',
],
'cari' => 
[
'Cari',
'Carian',
],
'cham' => 
[
'Cham',
],
'cher' => 
[
'Cher',
'Cherokee',
],
'chrs' => 
[
'Chrs',
'Chorasmian',
],
'copt' => 
[
'Copt',
'Coptic',
'Qaac',
],
'cpmn' => 
[
'Cpmn',
'Cypro_Minoan',
],
'cprt' => 
[
'Cprt',
'Cypriot',
],
'cyrl' => 
[
'Cyrl',
'Cyrillic',
],
'deva' => 
[
'Deva',
'Devanagari',
],
'diak' => 
[
'Diak',
'Dives_Akuru',
],
'dogr' => 
[
'Dogr',
'Dogra',
],
'dsrt' => 
[
'Dsrt',
'Deseret',
],
'dupl' => 
[
'Dupl',
'Duployan',
],
'egyp' => 
[
'Egyp',
'Egyptian_Hieroglyphs',
],
'elba' => 
[
'Elba',
'Elbasan',
],
'elym' => 
[
'Elym',
'Elymaic',
],
'ethi' => 
[
'Ethi',
'Ethiopic',
],
'geor' => 
[
'Geor',
'Georgian',
],
'glag' => 
[
'Glag',
'Glagolitic',
],
'gong' => 
[
'Gong',
'Gunjala_Gondi',
],
'gonm' => 
[
'Gonm',
'Masaram_Gondi',
],
'goth' => 
[
'Goth',
'Gothic',
],
'gran' => 
[
'Gran',
'Grantha',
],
'grek' => 
[
'Grek',
'Greek',
],
'gujr' => 
[
'Gujr',
'Gujarati',
],
'guru' => 
[
'Guru',
'Gurmukhi',
],
'hang' => 
[
'Hang',
'Hangul',
],
'hani' => 
[
'Hani',
'Han',
],
'hano' => 
[
'Hano',
'Hanunoo',
],
'hatr' => 
[
'Hatr',
'Hatran',
],
'hebr' => 
[
'Hebr',
'Hebrew',
],
'hira' => 
[
'Hira',
'Hiragana',
],
'hluw' => 
[
'Hluw',
'Anatolian_Hieroglyphs',
],
'hmng' => 
[
'Hmng',
'Pahawh_Hmong',
],
'hmnp' => 
[
'Hmnp',
'Nyiakeng_Puachue_Hmong',
],
'hrkt' => 
[
'Hrkt',
'Katakana_Or_Hiragana',
],
'hung' => 
[
'Hung',
'Old_Hungarian',
],
'ital' => 
[
'Ital',
'Old_Italic',
],
'java' => 
[
'Java',
'Javanese',
],
'kali' => 
[
'Kali',
'Kayah_Li',
],
'kana' => 
[
'Kana',
'Katakana',
],
'kawi' => 
[
'Kawi',
],
'khar' => 
[
'Khar',
'Kharoshthi',
],
'khmr' => 
[
'Khmr',
'Khmer',
],
'khoj' => 
[
'Khoj',
'Khojki',
],
'kits' => 
[
'Kits',
'Khitan_Small_Script',
],
'knda' => 
[
'Knda',
'Kannada',
],
'kthi' => 
[
'Kthi',
'Kaithi',
],
'lana' => 
[
'Lana',
'Tai_Tham',
],
'laoo' => 
[
'Laoo',
'Lao',
],
'latn' => 
[
'Latn',
'Latin',
],
'lepc' => 
[
'Lepc',
'Lepcha',
],
'limb' => 
[
'Limb',
'Limbu',
],
'lina' => 
[
'Lina',
'Linear_A',
],
'linb' => 
[
'Linb',
'Linear_B',
],
'lisu' => 
[
'Lisu',
],
'lyci' => 
[
'Lyci',
'Lycian',
],
'lydi' => 
[
'Lydi',
'Lydian',
],
'mahj' => 
[
'Mahj',
'Mahajani',
],
'maka' => 
[
'Maka',
'Makasar',
],
'mand' => 
[
'Mand',
'Mandaic',
],
'mani' => 
[
'Mani',
'Manichaean',
],
'marc' => 
[
'Marc',
'Marchen',
],
'medf' => 
[
'Medf',
'Medefaidrin',
],
'mend' => 
[
'Mend',
'Mende_Kikakui',
],
'merc' => 
[
'Merc',
'Meroitic_Cursive',
],
'mero' => 
[
'Mero',
'Meroitic_Hieroglyphs',
],
'mlym' => 
[
'Mlym',
'Malayalam',
],
'modi' => 
[
'Modi',
],
'mong' => 
[
'Mong',
'Mongolian',
],
'mroo' => 
[
'Mroo',
'Mro',
],
'mtei' => 
[
'Mtei',
'Meetei_Mayek',
],
'mult' => 
[
'Mult',
'Multani',
],
'mymr' => 
[
'Mymr',
'Myanmar',
],
'nagm' => 
[
'Nagm',
'Nag_Mundari',
],
'nand' => 
[
'Nand',
'Nandinagari',
],
'narb' => 
[
'Narb',
'Old_North_Arabian',
],
'nbat' => 
[
'Nbat',
'Nabataean',
],
'newa' => 
[
'Newa',
],
'nkoo' => 
[
'Nkoo',
'Nko',
],
'nshu' => 
[
'Nshu',
'Nushu',
],
'ogam' => 
[
'Ogam',
'Ogham',
],
'olck' => 
[
'Olck',
'Ol_Chiki',
],
'orkh' => 
[
'Orkh',
'Old_Turkic',
],
'orya' => 
[
'Orya',
'Oriya',
],
'osge' => 
[
'Osge',
'Osage',
],
'osma' => 
[
'Osma',
'Osmanya',
],
'ougr' => 
[
'Ougr',
'Old_Uyghur',
],
'palm' => 
[
'Palm',
'Palmyrene',
],
'pauc' => 
[
'Pauc',
'Pau_Cin_Hau',
],
'perm' => 
[
'Perm',
'Old_Permic',
],
'phag' => 
[
'Phag',
'Phags_Pa',
],
'phli' => 
[
'Phli',
'Inscriptional_Pahlavi',
],
'phlp' => 
[
'Phlp',
'Psalter_Pahlavi',
],
'phnx' => 
[
'Phnx',
'Phoenician',
],
'plrd' => 
[
'Plrd',
'Miao',
],
'prti' => 
[
'Prti',
'Inscriptional_Parthian',
],
'rjng' => 
[
'Rjng',
'Rejang',
],
'rohg' => 
[
'Rohg',
'Hanifi_Rohingya',
],
'runr' => 
[
'Runr',
'Runic',
],
'samr' => 
[
'Samr',
'Samaritan',
],
'sarb' => 
[
'Sarb',
'Old_South_Arabian',
],
'saur' => 
[
'Saur',
'Saurashtra',
],
'sgnw' => 
[
'Sgnw',
'SignWriting',
],
'shaw' => 
[
'Shaw',
'Shavian',
],
'shrd' => 
[
'Shrd',
'Sharada',
],
'sidd' => 
[
'Sidd',
'Siddham',
],
'sind' => 
[
'Sind',
'Khudawadi',
],
'sinh' => 
[
'Sinh',
'Sinhala',
],
'sogd' => 
[
'Sogd',
'Sogdian',
],
'sogo' => 
[
'Sogo',
'Old_Sogdian',
],
'sora' => 
[
'Sora',
'Sora_Sompeng',
],
'soyo' => 
[
'Soyo',
'Soyombo',
],
'sund' => 
[
'Sund',
'Sundanese',
],
'sylo' => 
[
'Sylo',
'Syloti_Nagri',
],
'syrc' => 
[
'Syrc',
'Syriac',
],
'tagb' => 
[
'Tagb',
'Tagbanwa',
],
'takr' => 
[
'Takr',
'Takri',
],
'tale' => 
[
'Tale',
'Tai_Le',
],
'talu' => 
[
'Talu',
'New_Tai_Lue',
],
'taml' => 
[
'Taml',
'Tamil',
],
'tang' => 
[
'Tang',
'Tangut',
],
'tavt' => 
[
'Tavt',
'Tai_Viet',
],
'telu' => 
[
'Telu',
'Telugu',
],
'tfng' => 
[
'Tfng',
'Tifinagh',
],
'tglg' => 
[
'Tglg',
'Tagalog',
],
'thaa' => 
[
'Thaa',
'Thaana',
],
'thai' => 
[
'Thai',
],
'tibt' => 
[
'Tibt',
'Tibetan',
],
'tirh' => 
[
'Tirh',
'Tirhuta',
],
'tnsa' => 
[
'Tnsa',
'Tangsa',
],
'toto' => 
[
'Toto',
],
'ugar' => 
[
'Ugar',
'Ugaritic',
],
'vaii' => 
[
'Vaii',
'Vai',
],
'vith' => 
[
'Vith',
'Vithkuqi',
],
'wara' => 
[
'Wara',
'Warang_Citi',
],
'wcho' => 
[
'Wcho',
'Wancho',
],
'xpeo' => 
[
'Xpeo',
'Old_Persian',
],
'xsux' => 
[
'Xsux',
'Cuneiform',
],
'yezi' => 
[
'Yezi',
'Yezidi',
],
'yiii' => 
[
'Yiii',
'Yi',
],
'zanb' => 
[
'Zanb',
'Zanabazar_Square',
],
'zinh' => 
[
'Zinh',
'Inherited',
'Qaai',
],
'zyyy' => 
[
'Zyyy',
'Common',
],
'zzzz' => 
[
'Zzzz',
'Unknown',
],
},
'sd' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'sterm' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'term' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'uideo' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'upper' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'vo' => 
{
'r' => 
[
'R',
'Rotated',
],
'tr' => 
[
'Tr',
'Transformed_Rotated',
],
'tu' => 
[
'Tu',
'Transformed_Upright',
],
'u' => 
[
'U',
'Upright',
],
},
'vs' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'wb' => 
{
'cr' => 
[
'CR',
],
'dq' => 
[
'DQ',
'Double_Quote',
],
'eb' => 
[
'EB',
'E_Base',
],
'ebg' => 
[
'EBG',
'E_Base_GAZ',
],
'em' => 
[
'EM',
'E_Modifier',
],
'ex' => 
[
'EX',
'ExtendNumLet',
],
'extend' => 
[
'Extend',
],
'fo' => 
[
'FO',
'Format',
],
'gaz' => 
[
'GAZ',
'Glue_After_Zwj',
],
'hl' => 
[
'HL',
'Hebrew_Letter',
],
'ka' => 
[
'KA',
'Katakana',
],
'le' => 
[
'LE',
'ALetter',
],
'lf' => 
[
'LF',
],
'mb' => 
[
'MB',
'MidNumLet',
],
'ml' => 
[
'ML',
'MidLetter',
],
'mn' => 
[
'MN',
'MidNum',
],
'nl' => 
[
'NL',
'Newline',
],
'nu' => 
[
'NU',
'Numeric',
],
'ri' => 
[
'RI',
'Regional_Indicator',
],
'sq' => 
[
'SQ',
'Single_Quote',
],
'wsegspace' => 
[
'WSegSpace',
],
'xx' => 
[
'XX',
'Other',
],
'zwj' => 
[
'ZWJ',
],
},
'wspace' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'xidc' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
'xids' => 
{
'n' => 
[
'N',
'No',
'F',
'False',
],
'y' => 
[
'Y',
'Yes',
'T',
'True',
],
},
);

# Ordered (by code point ordinal) list of the ranges of code points whose
# names are algorithmically determined.  Each range entry is an anonymous hash
# of the start and end points and a template for the names within it.
@Unicode::UCD::algorithmic_named_code_points = (

{
'high' => 19903,
'low' => 13312,
'name' => 'CJK UNIFIED IDEOGRAPH-<code point>',
},
{
'high' => 40959,
'low' => 19968,
'name' => 'CJK UNIFIED IDEOGRAPH-<code point>',
},
{
'high' => 55203,
'low' => 44032,
'name' => '<hangul syllable>',
},
{
'high' => 64109,
'low' => 63744,
'name' => 'CJK COMPATIBILITY IDEOGRAPH-<code point>',
},
{
'high' => 64217,
'low' => 64112,
'name' => 'CJK COMPATIBILITY IDEOGRAPH-<code point>',
},
{
'high' => 100343,
'low' => 94208,
'name' => 'TANGUT IDEOGRAPH-<code point>',
},
{
'high' => 101589,
'low' => 101120,
'name' => 'KHITAN SMALL SCRIPT CHARACTER-<code point>',
},
{
'high' => 101640,
'low' => 101632,
'name' => 'TANGUT IDEOGRAPH SUPPLEMENT-<code point>',
},
{
'high' => 111355,
'low' => 110960,
'name' => 'NUSHU CHARACTER-<code point>',
},
{
'high' => 173791,
'low' => 131072,
'name' => 'CJK UNIFIED IDEOGRAPH-<code point>',
},
{
'high' => 177977,
'low' => 173824,
'name' => 'CJK UNIFIED IDEOGRAPH-<code point>',
},
{
'high' => 178205,
'low' => 177984,
'name' => 'CJK UNIFIED IDEOGRAPH-<code point>',
},
{
'high' => 183969,
'low' => 178208,
'name' => 'CJK UNIFIED IDEOGRAPH-<code point>',
},
{
'high' => 191456,
'low' => 183984,
'name' => 'CJK UNIFIED IDEOGRAPH-<code point>',
},
{
'high' => 195101,
'low' => 194560,
'name' => 'CJK COMPATIBILITY IDEOGRAPH-<code point>',
},
{
'high' => 201546,
'low' => 196608,
'name' => 'CJK UNIFIED IDEOGRAPH-<code point>',
},
{
'high' => 205743,
'low' => 201552,
'name' => 'CJK UNIFIED IDEOGRAPH-<code point>',
},
,
);

# The properties that as-is have two meanings, and which must be disambiguated
%Unicode::UCD::ambiguous_names = (
'cf' => 1,
'isc' => 1,
'lc' => 1,
'sc' => 1,
);

# Keys are the prop-val combinations which are the default values for the
# given property, expressed in standard loose form
%Unicode::UCD::loose_defaults = (
'age=na' => 1,
'age=unassigned' => 1,
'ahex=f' => 1,
'ahex=false' => 1,
'ahex=n' => 1,
'ahex=no' => 1,
'alpha=f' => 1,
'alpha=false' => 1,
'alpha=n' => 1,
'alpha=no' => 1,
'bc=l' => 1,
'bc=lefttoright' => 1,
'bidic=f' => 1,
'bidic=false' => 1,
'bidic=n' => 1,
'bidic=no' => 1,
'bidim=f' => 1,
'bidim=false' => 1,
'bidim=n' => 1,
'bidim=no' => 1,
'blk=nb' => 1,
'blk=noblock' => 1,
'bpt=n' => 1,
'bpt=none' => 1,
'cased=f' => 1,
'cased=false' => 1,
'cased=n' => 1,
'cased=no' => 1,
'ccc=0' => 1,
'ccc=notreordered' => 1,
'ccc=nr' => 1,
'ce=f' => 1,
'ce=false' => 1,
'ce=n' => 1,
'ce=no' => 1,
'ci=f' => 1,
'ci=false' => 1,
'ci=n' => 1,
'ci=no' => 1,
'cn' => 1,
'compex=f' => 1,
'compex=false' => 1,
'compex=n' => 1,
'compex=no' => 1,
'cwcf=f' => 1,
'cwcf=false' => 1,
'cwcf=n' => 1,
'cwcf=no' => 1,
'cwcm=f' => 1,
'cwcm=false' => 1,
'cwcm=n' => 1,
'cwcm=no' => 1,
'cwkcf=f' => 1,
'cwkcf=false' => 1,
'cwkcf=n' => 1,
'cwkcf=no' => 1,
'cwl=f' => 1,
'cwl=false' => 1,
'cwl=n' => 1,
'cwl=no' => 1,
'cwt=f' => 1,
'cwt=false' => 1,
'cwt=n' => 1,
'cwt=no' => 1,
'cwu=f' => 1,
'cwu=false' => 1,
'cwu=n' => 1,
'cwu=no' => 1,
'dash=f' => 1,
'dash=false' => 1,
'dash=n' => 1,
'dash=no' => 1,
'dep=f' => 1,
'dep=false' => 1,
'dep=n' => 1,
'dep=no' => 1,
'di=f' => 1,
'di=false' => 1,
'di=n' => 1,
'di=no' => 1,
'dia=f' => 1,
'dia=false' => 1,
'dia=n' => 1,
'dia=no' => 1,
'dt=none' => 1,
'ea=n' => 1,
'ea=neutral' => 1,
'ebase=f' => 1,
'ebase=false' => 1,
'ebase=n' => 1,
'ebase=no' => 1,
'ecomp=f' => 1,
'ecomp=false' => 1,
'ecomp=n' => 1,
'ecomp=no' => 1,
'emod=f' => 1,
'emod=false' => 1,
'emod=n' => 1,
'emod=no' => 1,
'emoji=f' => 1,
'emoji=false' => 1,
'emoji=n' => 1,
'emoji=no' => 1,
'epres=f' => 1,
'epres=false' => 1,
'epres=n' => 1,
'epres=no' => 1,
'ext=f' => 1,
'ext=false' => 1,
'ext=n' => 1,
'ext=no' => 1,
'extpict=f' => 1,
'extpict=false' => 1,
'extpict=n' => 1,
'extpict=no' => 1,
'gc=cn' => 1,
'gc=unassigned' => 1,
'gcb=other' => 1,
'gcb=xx' => 1,
'grbase=f' => 1,
'grbase=false' => 1,
'grbase=n' => 1,
'grbase=no' => 1,
'grext=f' => 1,
'grext=false' => 1,
'grext=n' => 1,
'grext=no' => 1,
'hex=f' => 1,
'hex=false' => 1,
'hex=n' => 1,
'hex=no' => 1,
'hst=na' => 1,
'hst=notapplicable' => 1,
'hyphen=f' => 1,
'hyphen=false' => 1,
'hyphen=n' => 1,
'hyphen=no' => 1,
'idc=f' => 1,
'idc=false' => 1,
'idc=n' => 1,
'idc=no' => 1,
'identifierstatus=restricted' => 1,
'identifiertype=notcharacter' => 1,
'ideo=f' => 1,
'ideo=false' => 1,
'ideo=n' => 1,
'ideo=no' => 1,
'ids=f' => 1,
'ids=false' => 1,
'ids=n' => 1,
'ids=no' => 1,
'idsb=f' => 1,
'idsb=false' => 1,
'idsb=n' => 1,
'idsb=no' => 1,
'idst=f' => 1,
'idst=false' => 1,
'idst=n' => 1,
'idst=no' => 1,
'in=na' => 1,
'in=unassigned' => 1,
'innb' => 1,
'innoblock' => 1,
'inpc=na' => 1,
'insc=other' => 1,
'iscn' => 1,
'isnb' => 1,
'isnoblock' => 1,
'isunassigned' => 1,
'isunknown' => 1,
'iszzzz' => 1,
'jg=nojoininggroup' => 1,
'joinc=f' => 1,
'joinc=false' => 1,
'joinc=n' => 1,
'joinc=no' => 1,
'jt=nonjoining' => 1,
'jt=u' => 1,
'lb=unknown' => 1,
'lb=xx' => 1,
'loe=f' => 1,
'loe=false' => 1,
'loe=n' => 1,
'loe=no' => 1,
'lower=f' => 1,
'lower=false' => 1,
'lower=n' => 1,
'lower=no' => 1,
'math=f' => 1,
'math=false' => 1,
'math=n' => 1,
'math=no' => 1,
'nb' => 1,
'nchar=f' => 1,
'nchar=false' => 1,
'nchar=n' => 1,
'nchar=no' => 1,
'nfcqc=y' => 1,
'nfcqc=yes' => 1,
'nfdqc=y' => 1,
'nfdqc=yes' => 1,
'nfkcqc=y' => 1,
'nfkcqc=yes' => 1,
'nfkdqc=y' => 1,
'nfkdqc=yes' => 1,
'noblock' => 1,
'nt=none' => 1,
'nv=nan' => 1,
'patsyn=f' => 1,
'patsyn=false' => 1,
'patsyn=n' => 1,
'patsyn=no' => 1,
'patws=f' => 1,
'patws=false' => 1,
'patws=n' => 1,
'patws=no' => 1,
'pcm=f' => 1,
'pcm=false' => 1,
'pcm=n' => 1,
'pcm=no' => 1,
'qmark=f' => 1,
'qmark=false' => 1,
'qmark=n' => 1,
'qmark=no' => 1,
'radical=f' => 1,
'radical=false' => 1,
'radical=n' => 1,
'radical=no' => 1,
'ri=f' => 1,
'ri=false' => 1,
'ri=n' => 1,
'ri=no' => 1,
'sb=other' => 1,
'sb=xx' => 1,
'sc=unknown' => 1,
'sc=zzzz' => 1,
'scx=unknown' => 1,
'scx=zzzz' => 1,
'sd=f' => 1,
'sd=false' => 1,
'sd=n' => 1,
'sd=no' => 1,
'sterm=f' => 1,
'sterm=false' => 1,
'sterm=n' => 1,
'sterm=no' => 1,
'term=f' => 1,
'term=false' => 1,
'term=n' => 1,
'term=no' => 1,
'uideo=f' => 1,
'uideo=false' => 1,
'uideo=n' => 1,
'uideo=no' => 1,
'unassigned' => 1,
'unknown' => 1,
'upper=f' => 1,
'upper=false' => 1,
'upper=n' => 1,
'upper=no' => 1,
'vo=r' => 1,
'vo=rotated' => 1,
'vs=f' => 1,
'vs=false' => 1,
'vs=n' => 1,
'vs=no' => 1,
'wb=other' => 1,
'wb=xx' => 1,
'wspace=f' => 1,
'wspace=false' => 1,
'wspace=n' => 1,
'wspace=no' => 1,
'xidc=f' => 1,
'xidc=false' => 1,
'xidc=n' => 1,
'xidc=no' => 1,
'xids=f' => 1,
'xids=false' => 1,
'xids=n' => 1,
'xids=no' => 1,
'zzzz' => 1,
);

# The properties that are combinations, in that they have both a map table and
# a match table.  This is actually for UCD.t, so it knows how to test for
# these.
%Unicode::UCD::combination_property = (

);

# All combinations of names that are suppressed.
# This is actually for UCD.t, so it knows which properties shouldn't have
# entries.  If it got any bigger, would probably want to put it in its own
# file to use memory only when it was needed, in testing.
@Unicode::UCD::suppressed_properties = (

'cjkaccountingnumeric',
'cjkcompatibilityvariant',
'cjkiicore',
'cjkirggsource',
'cjkirghsource',
'cjkirgjsource',
'cjkirgkpsource',
'cjkirgksource',
'cjkirgmsource',
'cjkirgssource',
'cjkirgtsource',
'cjkirguksource',
'cjkirgusource',
'cjkirgvsource',
'cjkothernumeric',
'cjkprimarynumeric',
'cjkrsunicode',
'expandsonnfc',
'expandsonnfd',
'expandsonnfkc',
'expandsonnfkd',
'fcnfkc',
'fcnfkcclosure',
'graphemelink',
'grlink',
'jamoshortname',
'jsn',
'kaccountingnumeric',
'kcompatibilityvariant',
'kiicore',
'kirggsource',
'kirghsource',
'kirgjsource',
'kirgkpsource',
'kirgksource',
'kirgmsource',
'kirgssource',
'kirgtsource',
'kirguksource',
'kirgusource',
'kirgvsource',
'kothernumeric',
'kprimarynumeric',
'krsunicode',
'oalpha',
'odi',
'ogrext',
'oidc',
'oids',
'olower',
'omath',
'otheralphabetic',
'otherdefaultignorablecodepoint',
'othergraphemeextend',
'otheridcontinue',
'otheridstart',
'otherlowercase',
'othermath',
'otheruppercase',
'oupper',
'sc=hrkt',
'sc=katakanaorhiragana',
'script=hrkt',
'script=katakanaorhiragana',
'scriptextensions=hrkt',
'scriptextensions=katakanaorhiragana',
'scx=hrkt',
'scx=katakanaorhiragana',
'unicoderadicalstroke',
'urs',
'xonfc',
'xonfd',
'xonfkc',
'xonfkd',
,
);

1;
