------------------------------------------------------------------------
-- dqLogB.decTest -- integral 754r adjusted exponent, for decQuads    --
-- Copyright (c) IBM Corporation, 2005, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- basics
dqlogb000 logb  0                 -> -Infinity  Division_by_zero
dqlogb001 logb  1E-6176           -> -6176
dqlogb002 logb  1E-6143           -> -6143
dqlogb003 logb  0.001             -> -3
dqlogb004 logb  0.03              -> -2
dqlogb005 logb  1                 ->  0
dqlogb006 logb  2                 ->  0
dqlogb007 logb  2.5               ->  0
dqlogb008 logb  2.50              ->  0
dqlogb009 logb  2.500             ->  0
dqlogb010 logb  10                ->  1
dqlogb011 logb  70                ->  1
dqlogb012 logb  100               ->  2
dqlogb013 logb  250               ->  2
dqlogb014 logb  9E+6144           ->  6144
dqlogb015 logb +Infinity          ->  Infinity

-- negatives appear to be treated as positives
dqlogb021 logb -0                 -> -Infinity  Division_by_zero
dqlogb022 logb -1E-6176           -> -6176
dqlogb023 logb -9E-6143           -> -6143
dqlogb024 logb -0.001             -> -3
dqlogb025 logb -1                 ->  0
dqlogb026 logb -2                 ->  0
dqlogb027 logb -10                ->  1
dqlogb028 logb -70                ->  1
dqlogb029 logb -100               ->  2
dqlogb030 logb -9E+6144           ->  6144
dqlogb031 logb -Infinity          ->  Infinity

-- zeros
dqlogb111 logb          0   -> -Infinity  Division_by_zero
dqlogb112 logb         -0   -> -Infinity  Division_by_zero
dqlogb113 logb       0E+4   -> -Infinity  Division_by_zero
dqlogb114 logb      -0E+4   -> -Infinity  Division_by_zero
dqlogb115 logb     0.0000   -> -Infinity  Division_by_zero
dqlogb116 logb    -0.0000   -> -Infinity  Division_by_zero
dqlogb117 logb      0E-141  -> -Infinity  Division_by_zero
dqlogb118 logb     -0E-141  -> -Infinity  Division_by_zero

-- full coefficients, alternating bits
dqlogb121 logb   268268268        -> 8
dqlogb122 logb  -268268268        -> 8
dqlogb123 logb   134134134        -> 8
dqlogb124 logb  -134134134        -> 8

-- Nmax, Nmin, Ntiny
dqlogb131 logb  9.999999999999999999999999999999999E+6144   ->  6144
dqlogb132 logb  1E-6143                   -> -6143
dqlogb133 logb  1.000000000000000000000000000000000E-6143   -> -6143
dqlogb134 logb  1E-6176                   -> -6176

dqlogb135 logb  -1E-6176                  -> -6176
dqlogb136 logb  -1.000000000000000000000000000000000E-6143  -> -6143
dqlogb137 logb  -1E-6143                  -> -6143
dqlogb1614 logb  -9.999999999999999999999999999999999E+6144  ->  6144

-- ones
dqlogb0061 logb  1                 ->   0
dqlogb0062 logb  1.0               ->   0
dqlogb0063 logb  1.000000000000000 ->   0

-- notable cases -- exact powers of 10
dqlogb1100 logb 1             -> 0
dqlogb1101 logb 10            -> 1
dqlogb1102 logb 100           -> 2
dqlogb1103 logb 1000          -> 3
dqlogb1104 logb 10000         -> 4
dqlogb1105 logb 100000        -> 5
dqlogb1106 logb 1000000       -> 6
dqlogb1107 logb 10000000      -> 7
dqlogb1108 logb 100000000     -> 8
dqlogb1109 logb 1000000000    -> 9
dqlogb1110 logb 10000000000   -> 10
dqlogb1111 logb 100000000000  -> 11
dqlogb1112 logb 1000000000000 -> 12
dqlogb1113 logb 0.00000000001 -> -11
dqlogb1114 logb 0.0000000001 -> -10
dqlogb1115 logb 0.000000001 -> -9
dqlogb1116 logb 0.00000001 -> -8
dqlogb1117 logb 0.0000001 -> -7
dqlogb1118 logb 0.000001 -> -6
dqlogb1119 logb 0.00001 -> -5
dqlogb1120 logb 0.0001 -> -4
dqlogb1121 logb 0.001 -> -3
dqlogb1122 logb 0.01 -> -2
dqlogb1123 logb 0.1 -> -1
dqlogb1124 logb 1E-99  -> -99
dqlogb1125 logb 1E-100 -> -100
dqlogb1127 logb 1E-299 -> -299
dqlogb1126 logb 1E-6143 -> -6143

-- suggestions from Ilan Nehama
dqlogb1400 logb 10E-3    -> -2
dqlogb1401 logb 10E-2    -> -1
dqlogb1402 logb 100E-2   ->  0
dqlogb1403 logb 1000E-2  ->  1
dqlogb1404 logb 10000E-2 ->  2
dqlogb1405 logb 10E-1    ->  0
dqlogb1406 logb 100E-1   ->  1
dqlogb1407 logb 1000E-1  ->  2
dqlogb1408 logb 10000E-1 ->  3
dqlogb1409 logb 10E0     ->  1
dqlogb1410 logb 100E0    ->  2
dqlogb1411 logb 1000E0   ->  3
dqlogb1412 logb 10000E0  ->  4
dqlogb1413 logb 10E1     ->  2
dqlogb1414 logb 100E1    ->  3
dqlogb1415 logb 1000E1   ->  4
dqlogb1416 logb 10000E1  ->  5
dqlogb1417 logb 10E2     ->  3
dqlogb1418 logb 100E2    ->  4
dqlogb1419 logb 1000E2   ->  5
dqlogb1420 logb 10000E2  ->  6

-- special values
dqlogb820  logb   Infinity ->   Infinity
dqlogb821  logb   0        ->  -Infinity Division_by_zero
dqlogb822  logb   NaN      ->   NaN
dqlogb823  logb   sNaN     ->   NaN     Invalid_operation
-- propagating NaNs
dqlogb824  logb   sNaN123  ->   NaN123  Invalid_operation
dqlogb825  logb   -sNaN321 ->  -NaN321  Invalid_operation
dqlogb826  logb   NaN456   ->   NaN456
dqlogb827  logb   -NaN654  ->  -NaN654
dqlogb828  logb   NaN1     ->   NaN1

-- Null test
dqlogb900  logb #   -> NaN Invalid_operation


