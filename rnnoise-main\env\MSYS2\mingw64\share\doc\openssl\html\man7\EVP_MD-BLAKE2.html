<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_MD-BLAKE2</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identities">Identities</a></li>
      <li><a href="#Settable-Parameters">Settable Parameters</a></li>
      <li><a href="#Gettable-Parameters">Gettable Parameters</a></li>
      <li><a href="#Settable-Context-Parameters">Settable Context Parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_MD-BLAKE2 - The BLAKE2 EVP_MD implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing BLAKE2 digests through the <b>EVP_MD</b> API.</p>

<h2 id="Identities">Identities</h2>

<p>This implementation is only available with the default provider, and includes the following varieties:</p>

<dl>

<dt id="BLAKE2S-256">BLAKE2S-256</dt>
<dd>

<p>Known names are &quot;BLAKE2S-256&quot; and &quot;BLAKE2s256&quot;.</p>

</dd>
<dt id="BLAKE2B-512">BLAKE2B-512</dt>
<dd>

<p>Known names are &quot;BLAKE2B-512&quot; and &quot;BLAKE2b512&quot;.</p>

</dd>
</dl>

<h2 id="Settable-Parameters">Settable Parameters</h2>

<p>&quot;BLAKE2B-512&quot; supports the following EVP_MD_CTX_set_params() key described in <a href="../man3/EVP_DigestInit.html">&quot;PARAMETERS&quot; in EVP_DigestInit(3)</a>.</p>

<dl>

<dt id="size-OSSL_DIGEST_PARAM_SIZE-unsigned-integer">&quot;size&quot; (<b>OSSL_DIGEST_PARAM_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

</dd>
</dl>

<h2 id="Gettable-Parameters">Gettable Parameters</h2>

<p>This implementation supports the common gettable parameters described in <a href="../man7/EVP_MD-common.html">EVP_MD-common(7)</a>.</p>

<h2 id="Settable-Context-Parameters">Settable Context Parameters</h2>

<p>The implementation supports the following <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> entries which are settable for an <b>EVP_MD_CTX</b> with <a href="../man3/EVP_DigestInit_ex2.html">EVP_DigestInit_ex2(3)</a> or <a href="../man3/EVP_MD_CTX_set_params.html">EVP_MD_CTX_set_params(3)</a>:</p>

<dl>

<dt id="size-OSSL_DIGEST_PARAM_SIZE-unsigned-integer1">&quot;size&quot; (<b>OSSL_DIGEST_PARAM_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Sets a different digest length for the <a href="../man3/EVP_DigestFinal.html">EVP_DigestFinal(3)</a> output. The value of the &quot;size&quot; parameter must not exceed the default digest length of the respective BLAKE2 algorithm variants, 64 for BLAKE2B-512 and 32 for BLAKE2S-256. The parameter must be set with the <a href="../man3/EVP_DigestInit_ex2.html">EVP_DigestInit_ex2(3)</a> call to have an immediate effect. When set with <a href="../man3/EVP_MD_CTX_set_params.html">EVP_MD_CTX_set_params(3)</a> it will have an effect only if the <b>EVP_MD_CTX</b> context is reinitialized.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider-digest.html">provider-digest(7)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.0.</p>

<p>The variable size support was added in OpenSSL 3.2 for BLAKE2B-512 and in OpenSSL 3.3 for BLAKE2S-256.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


