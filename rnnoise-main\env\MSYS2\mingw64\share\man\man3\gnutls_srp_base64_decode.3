.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srp_base64_decode" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srp_base64_decode \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srp_base64_decode(const gnutls_datum_t * " b64_data ", char * " result ", size_t * " result_size ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * b64_data" 12
contain the encoded data
.IP "char * result" 12
the place where decoded data will be copied
.IP "size_t * result_size" 12
holds the size of the result
.SH "DESCRIPTION"
This function will decode the given encoded data, using the base64
encoding found in libsrp.

Note that  \fIb64_data\fP should be null terminated.

Warning!  This base64 encoding is not the "standard" encoding, so
do not use it for non\-SRP purposes.
.SH "RETURNS"
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if the buffer given is not
long enough, or 0 on success.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
