.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_subject_alt_names_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_subject_alt_names_get \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_subject_alt_names_get(gnutls_subject_alt_names_t " sans ", unsigned int " seq ", unsigned int * " san_type ", gnutls_datum_t * " san ", gnutls_datum_t * " othername_oid ");"
.SH ARGUMENTS
.IP "gnutls_subject_alt_names_t sans" 12
The alternative names
.IP "unsigned int seq" 12
The index of the name to get
.IP "unsigned int * san_type" 12
Will hold the type of the name (of \fBgnutls_subject_alt_names_t\fP)
.IP "gnutls_datum_t * san" 12
The alternative name data (should be treated as constant)
.IP "gnutls_datum_t * othername_oid" 12
The object identifier if  \fIsan_type\fP is \fBGNUTLS_SAN_OTHERNAME\fP (should be treated as constant)
.SH "DESCRIPTION"
This function will return a specific alternative name as stored in
the  \fIsans\fP type. The returned values should be treated as constant
and valid for the lifetime of  \fIsans\fP .
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
if the index is out of bounds, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
