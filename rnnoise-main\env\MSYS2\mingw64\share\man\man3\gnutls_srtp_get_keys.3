.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srtp_get_keys" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srtp_get_keys \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srtp_get_keys(gnutls_session_t " session ", void * " key_material ", unsigned int " key_material_size ", gnutls_datum_t * " client_key ", gnutls_datum_t * " client_salt ", gnutls_datum_t * " server_key ", gnutls_datum_t * " server_salt ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "void * key_material" 12
Space to hold the generated key material
.IP "unsigned int key_material_size" 12
The maximum size of the key material
.IP "gnutls_datum_t * client_key" 12
The master client write key, pointing inside the key material
.IP "gnutls_datum_t * client_salt" 12
The master client write salt, pointing inside the key material
.IP "gnutls_datum_t * server_key" 12
The master server write key, pointing inside the key material
.IP "gnutls_datum_t * server_salt" 12
The master server write salt, pointing inside the key material
.SH "DESCRIPTION"
This is a helper function to generate the keying material for SRTP.
It requires the space of the key material to be pre\-allocated (should be at least
2x the maximum key size and salt size). The  \fIclient_key\fP ,  \fIclient_salt\fP ,  \fIserver_key\fP and  \fIserver_salt\fP are convenience datums that point inside the key material. They may
be \fBNULL\fP.
.SH "RETURNS"
On success the size of the key material is returned,
otherwise, \fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if the buffer given is not 
sufficient, or a negative error code.

Since 3.1.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
