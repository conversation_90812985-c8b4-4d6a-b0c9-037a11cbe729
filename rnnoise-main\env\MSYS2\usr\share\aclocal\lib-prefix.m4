# lib-prefix.m4 serial 22
dnl Copyright (C) 2001-2005, 2008-2024 Free Software Foundation, Inc.
dnl This file is free software; the Free Software Foundation
dnl gives unlimited permission to copy and/or distribute it,
dnl with or without modifications, as long as this notice is preserved.

dnl From Bruno Haible.

dnl AC_LIB_PREFIX adds to the CPPFLAGS and LDFLAGS the flags that are needed
dnl to access previously installed libraries. The basic assumption is that
dnl a user will want packages to use other packages he previously installed
dnl with the same --prefix option.
dnl This macro is not needed if only AC_LIB_LINKFLAGS is used to locate
dnl libraries, but is otherwise very convenient.
AC_DEFUN([AC_LIB_PREFIX],
[
  AC_BEFORE([$0], [AC_LIB_LINKFLAGS])
  AC_REQUIRE([AC_PROG_CC])
  AC_REQUIRE([AC_CANONICAL_HOST])
  AC_REQUIRE([AC_LIB_PREPARE_MULTILIB])
  AC_REQUIRE([AC_LIB_PREPARE_PREFIX])
  dnl By default, look in $includedir and $libdir.
  use_additional=yes
  AC_LIB_WITH_FINAL_PREFIX([
    eval additional_includedir=\"$includedir\"
    eval additional_libdir=\"$libdir\"
  ])
  AC_ARG_WITH([lib-prefix],
[[  --with-lib-prefix[=DIR] search for libraries in DIR/include and DIR/lib
  --without-lib-prefix    don't search for libraries in includedir and libdir]],
[
    if test "X$withval" = "Xno"; then
      use_additional=no
    else
      if test "X$withval" = "X"; then
        AC_LIB_WITH_FINAL_PREFIX([
          eval additional_includedir=\"$includedir\"
          eval additional_libdir=\"$libdir\"
        ])
      else
        additional_includedir="$withval/include"
        additional_libdir="$withval/$acl_libdirstem"
      fi
    fi
])
  if test $use_additional = yes; then
    dnl Potentially add $additional_includedir to $CPPFLAGS.
    dnl But don't add it
    dnl   1. if it's the standard /usr/include,
    dnl   2. if it's already present in $CPPFLAGS,
    dnl   3. if it's /usr/local/include and we are using GCC on Linux,
    dnl   4. if it doesn't exist as a directory.
    if test "X$additional_includedir" != "X/usr/include"; then
      haveit=
      for x in $CPPFLAGS; do
        AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
        if test "X$x" = "X-I$additional_includedir"; then
          haveit=yes
          break
        fi
      done
      if test -z "$haveit"; then
        if test "X$additional_includedir" = "X/usr/local/include"; then
          if test -n "$GCC"; then
            case $host_os in
              linux* | gnu* | k*bsd*-gnu) haveit=yes;;
            esac
          fi
        fi
        if test -z "$haveit"; then
          if test -d "$additional_includedir"; then
            dnl Really add $additional_includedir to $CPPFLAGS.
            CPPFLAGS="${CPPFLAGS}${CPPFLAGS:+ }-I$additional_includedir"
          fi
        fi
      fi
    fi
    dnl Potentially add $additional_libdir to $LDFLAGS.
    dnl But don't add it
    dnl   1. if it's the standard /usr/lib,
    dnl   2. if it's already present in $LDFLAGS,
    dnl   3. if it's /usr/local/lib and we are using GCC on Linux,
    dnl   4. if it doesn't exist as a directory.
    if test "X$additional_libdir" != "X/usr/$acl_libdirstem"; then
      haveit=
      for x in $LDFLAGS; do
        AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
        if test "X$x" = "X-L$additional_libdir"; then
          haveit=yes
          break
        fi
      done
      if test -z "$haveit"; then
        if test "X$additional_libdir" = "X/usr/local/$acl_libdirstem"; then
          if test -n "$GCC"; then
            case $host_os in
              linux*) haveit=yes;;
            esac
          fi
        fi
        if test -z "$haveit"; then
          if test -d "$additional_libdir"; then
            dnl Really add $additional_libdir to $LDFLAGS.
            LDFLAGS="${LDFLAGS}${LDFLAGS:+ }-L$additional_libdir"
          fi
        fi
      fi
    fi
  fi
])

dnl AC_LIB_PREPARE_PREFIX creates variables acl_final_prefix,
dnl acl_final_exec_prefix, containing the values to which $prefix and
dnl $exec_prefix will expand at the end of the configure script.
AC_DEFUN([AC_LIB_PREPARE_PREFIX],
[
  dnl Unfortunately, prefix and exec_prefix get only finally determined
  dnl at the end of configure.
  if test "X$prefix" = "XNONE"; then
    acl_final_prefix="$ac_default_prefix"
  else
    acl_final_prefix="$prefix"
  fi
  if test "X$exec_prefix" = "XNONE"; then
    acl_final_exec_prefix='${prefix}'
  else
    acl_final_exec_prefix="$exec_prefix"
  fi
  acl_saved_prefix="$prefix"
  prefix="$acl_final_prefix"
  eval acl_final_exec_prefix=\"$acl_final_exec_prefix\"
  prefix="$acl_saved_prefix"
])

dnl AC_LIB_WITH_FINAL_PREFIX([statement]) evaluates statement, with the
dnl variables prefix and exec_prefix bound to the values they will have
dnl at the end of the configure script.
AC_DEFUN([AC_LIB_WITH_FINAL_PREFIX],
[
  acl_saved_prefix="$prefix"
  prefix="$acl_final_prefix"
  acl_saved_exec_prefix="$exec_prefix"
  exec_prefix="$acl_final_exec_prefix"
  $1
  exec_prefix="$acl_saved_exec_prefix"
  prefix="$acl_saved_prefix"
])

dnl AC_LIB_PREPARE_MULTILIB creates
dnl - a function acl_is_expected_elfclass, that tests whether standard input
dn;   has a 32-bit or 64-bit ELF header, depending on the host CPU ABI,
dnl - 3 variables acl_libdirstem, acl_libdirstem2, acl_libdirstem3, containing
dnl   the basename of the libdir to try in turn, either "lib" or "lib64" or
dnl   "lib/64" or "lib32" or "lib/sparcv9" or "lib/amd64" or similar.
AC_DEFUN([AC_LIB_PREPARE_MULTILIB],
[
  dnl There is no formal standard regarding lib, lib32, and lib64.
  dnl On most glibc systems, the current practice is that on a system supporting
  dnl 32-bit and 64-bit instruction sets or ABIs, 64-bit libraries go under
  dnl $prefix/lib64 and 32-bit libraries go under $prefix/lib. However, on
  dnl Arch Linux based distributions, it's the opposite: 32-bit libraries go
  dnl under $prefix/lib32 and 64-bit libraries go under $prefix/lib.
  dnl We determine the compiler's default mode by looking at the compiler's
  dnl library search path. If at least one of its elements ends in /lib64 or
  dnl points to a directory whose absolute pathname ends in /lib64, we use that
  dnl for 64-bit ABIs. Similarly for 32-bit ABIs. Otherwise we use the default,
  dnl namely "lib".
  dnl On Solaris systems, the current practice is that on a system supporting
  dnl 32-bit and 64-bit instruction sets or ABIs, 64-bit libraries go under
  dnl $prefix/lib/64 (which is a symlink to either $prefix/lib/sparcv9 or
  dnl $prefix/lib/amd64) and 32-bit libraries go under $prefix/lib.
  AC_REQUIRE([AC_CANONICAL_HOST])
  AC_REQUIRE([gl_HOST_CPU_C_ABI_32BIT])

  AC_CACHE_CHECK([for ELF binary format], [gl_cv_elf],
    [AC_EGREP_CPP([Extensible Linking Format],
       [#if defined __ELF__ || (defined __linux__ && defined __EDG__)
        Extensible Linking Format
        #endif
       ],
       [gl_cv_elf=yes],
       [gl_cv_elf=no])
    ])
  if test $gl_cv_elf = yes; then
    # Extract the ELF class of a file (5th byte) in decimal.
    # Cf. https://en.wikipedia.org/wiki/Executable_and_Linkable_Format#File_header
    if od -A x < /dev/null >/dev/null 2>/dev/null; then
      # Use POSIX od.
      func_elfclass ()
      {
        od -A n -t d1 -j 4 -N 1
      }
    else
      # Use BSD hexdump.
      func_elfclass ()
      {
        dd bs=1 count=1 skip=4 2>/dev/null | hexdump -e '1/1 "%3d "'
        echo
      }
    fi
    # Use 'expr', not 'test', to compare the values of func_elfclass, because on
    # Solaris 11 OpenIndiana and Solaris 11 OmniOS, the result is 001 or 002,
    # not 1 or 2.
changequote(,)dnl
    case $HOST_CPU_C_ABI_32BIT in
      yes)
        # 32-bit ABI.
        acl_is_expected_elfclass ()
        {
          expr "`func_elfclass | sed -e 's/[ 	]//g'`" = 1 > /dev/null
        }
        ;;
      no)
        # 64-bit ABI.
        acl_is_expected_elfclass ()
        {
          expr "`func_elfclass | sed -e 's/[ 	]//g'`" = 2 > /dev/null
        }
        ;;
      *)
        # Unknown.
        acl_is_expected_elfclass ()
        {
          :
        }
        ;;
    esac
changequote([,])dnl
  else
    acl_is_expected_elfclass ()
    {
      :
    }
  fi

  dnl Allow the user to override the result by setting acl_cv_libdirstems.
  AC_CACHE_CHECK([for the common suffixes of directories in the library search path],
    [acl_cv_libdirstems],
    [dnl Try 'lib' first, because that's the default for libdir in GNU, see
     dnl <https://www.gnu.org/prep/standards/html_node/Directory-Variables.html>.
     acl_libdirstem=lib
     acl_libdirstem2=
     acl_libdirstem3=
     case "$host_os" in
       solaris*)
         dnl See Solaris 10 Software Developer Collection > Solaris 64-bit Developer's Guide > The Development Environment
         dnl <https://docs.oracle.com/cd/E19253-01/816-5138/dev-env/index.html>.
         dnl "Portable Makefiles should refer to any library directories using the 64 symbolic link."
         dnl But we want to recognize the sparcv9 or amd64 subdirectory also if the
         dnl symlink is missing, so we set acl_libdirstem2 too.
         if test $HOST_CPU_C_ABI_32BIT = no; then
           acl_libdirstem2=lib/64
           case "$host_cpu" in
             sparc*)        acl_libdirstem3=lib/sparcv9 ;;
             i*86 | x86_64) acl_libdirstem3=lib/amd64 ;;
           esac
         fi
         ;;
       netbsd*)
         dnl On NetBSD/sparc64, there is a 'sparc' subdirectory that contains
         dnl 32-bit libraries.
         if test $HOST_CPU_C_ABI_32BIT != no; then
           case "$host_cpu" in
             sparc*) acl_libdirstem2=lib/sparc ;;
           esac
         fi
         ;;
       *)
         dnl If $CC generates code for a 32-bit ABI, the libraries are
         dnl surely under $prefix/lib or $prefix/lib32, not $prefix/lib64.
         dnl Similarly, if $CC generates code for a 64-bit ABI, the libraries
         dnl are surely under $prefix/lib or $prefix/lib64, not $prefix/lib32.
         dnl Find the compiler's search path. However, non-system compilers
         dnl sometimes have odd library search paths. But we can't simply invoke
         dnl '/usr/bin/gcc -print-search-dirs' because that would not take into
         dnl account the -m32/-m31 or -m64 options from the $CC or $CFLAGS.
         searchpath=`(LC_ALL=C $CC $CPPFLAGS $CFLAGS -print-search-dirs) 2>/dev/null \
                     | sed -n -e 's,^libraries: ,,p' | sed -e 's,^=,,'`
         if test $HOST_CPU_C_ABI_32BIT != no; then
           # 32-bit or unknown ABI.
           if test -d /usr/lib32; then
             acl_libdirstem2=lib32
           fi
         fi
         if test $HOST_CPU_C_ABI_32BIT != yes; then
           # 64-bit or unknown ABI.
           if test -d /usr/lib64; then
             acl_libdirstem3=lib64
           fi
         fi
         if test -n "$searchpath"; then
           acl_saved_IFS="${IFS= 	}"; IFS=":"
           for searchdir in $searchpath; do
             if test -d "$searchdir"; then
               case "$searchdir" in
                 */lib32/ | */lib32 ) acl_libdirstem2=lib32 ;;
                 */lib64/ | */lib64 ) acl_libdirstem3=lib64 ;;
                 */../ | */.. )
                   # Better ignore directories of this form. They are misleading.
                   ;;
                 *) searchdir=`cd "$searchdir" && pwd`
                    case "$searchdir" in
                      */lib32 ) acl_libdirstem2=lib32 ;;
                      */lib64 ) acl_libdirstem3=lib64 ;;
                    esac ;;
               esac
             fi
           done
           IFS="$acl_saved_IFS"
           if test $HOST_CPU_C_ABI_32BIT = yes; then
             # 32-bit ABI.
             acl_libdirstem3=
           fi
           if test $HOST_CPU_C_ABI_32BIT = no; then
             # 64-bit ABI.
             acl_libdirstem2=
           fi
         fi
         ;;
     esac
     test -n "$acl_libdirstem2" || acl_libdirstem2="$acl_libdirstem"
     test -n "$acl_libdirstem3" || acl_libdirstem3="$acl_libdirstem"
     acl_cv_libdirstems="$acl_libdirstem,$acl_libdirstem2,$acl_libdirstem3"
    ])
  dnl Decompose acl_cv_libdirstems into acl_libdirstem, acl_libdirstem2, and
  dnl acl_libdirstem3.
changequote(,)dnl
  acl_libdirstem=`echo "$acl_cv_libdirstems" | sed -e 's/,.*//'`
  acl_libdirstem2=`echo "$acl_cv_libdirstems" | sed -e 's/^[^,]*,//' -e 's/,.*//'`
  acl_libdirstem3=`echo "$acl_cv_libdirstems" | sed -e 's/^[^,]*,[^,]*,//' -e 's/,.*//'`
changequote([,])dnl
])
