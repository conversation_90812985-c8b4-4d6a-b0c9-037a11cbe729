.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_privkey_status" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_privkey_status \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "unsigned gnutls_pkcs11_privkey_status(gnutls_pkcs11_privkey_t " key ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_privkey_t key" 12
Holds the key
.SH "DESCRIPTION"
Checks the status of the private key token.
.SH "RETURNS"
this function will return non\-zero if the token
holding the private key is still available (inserted), and zero otherwise.
.SH "SINCE"
3.1.9
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
