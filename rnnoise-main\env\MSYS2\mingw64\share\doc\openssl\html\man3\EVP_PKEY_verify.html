<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_verify</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a>
    <ul>
      <li><a href="#General">General</a></li>
      <li><a href="#Performing-multiple-verifications">Performing multiple verifications</a></li>
      <li><a href="#On-EVP_PKEY_CTX_set_signature">On EVP_PKEY_CTX_set_signature()</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a>
    <ul>
      <li><a href="#RSA-with-PKCS-1-padding-for-SHA256">RSA with PKCS#1 padding for SHA256</a></li>
      <li><a href="#RSA-SHA256-with-a-pre-computed-digest">RSA-SHA256 with a pre-computed digest</a></li>
      <li><a href="#RSA-SHA256-one-shot">RSA-SHA256, one-shot</a></li>
      <li><a href="#RSA-SHA256-using-update-and-final">RSA-SHA256, using update and final</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_verify_init, EVP_PKEY_verify_init_ex, EVP_PKEY_verify_init_ex2, EVP_PKEY_verify, EVP_PKEY_verify_message_init, EVP_PKEY_verify_message_update, EVP_PKEY_verify_message_final, EVP_PKEY_CTX_set_signature - signature verification using a public key algorithm</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int EVP_PKEY_verify_init(EVP_PKEY_CTX *ctx);
int EVP_PKEY_verify_init_ex(EVP_PKEY_CTX *ctx, const OSSL_PARAM params[]);
int EVP_PKEY_verify_init_ex2(EVP_PKEY_CTX *ctx, EVP_SIGNATURE *algo,
                             const OSSL_PARAM params[]);
int EVP_PKEY_verify_message_init(EVP_PKEY_CTX *ctx, EVP_SIGNATURE *algo,
                                 const OSSL_PARAM params[]);
int EVP_PKEY_CTX_set_signature(EVP_PKEY_CTX *pctx,
                               const unsigned char *sig, size_t siglen);
int EVP_PKEY_verify_message_update(EVP_PKEY_CTX *ctx,
                                   unsigned char *in, size_t inlen);
int EVP_PKEY_verify_message_final(EVP_PKEY_CTX *ctx);
int EVP_PKEY_verify(EVP_PKEY_CTX *ctx,
                    const unsigned char *sig, size_t siglen,
                    const unsigned char *tbs, size_t tbslen);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>EVP_PKEY_verify_init() initializes a public key algorithm context <i>ctx</i> for verification using the algorithm given when the context was created using <a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a> or variants thereof. The algorithm is used to fetch a <b>EVP_SIGNATURE</b> method implicitly, see <a href="../man7/provider.html">&quot;Implicit fetch&quot; in provider(7)</a> for more information about implicit fetches.</p>

<p>EVP_PKEY_verify_init_ex() is the same as EVP_PKEY_verify_init() but additionally sets the passed parameters <i>params</i> on the context before returning.</p>

<p>EVP_PKEY_verify_init_ex2() is the same as EVP_PKEY_verify_init_ex(), but works with an explicitly fetched <b>EVP_SIGNATURE</b> <i>algo</i>. A context <i>ctx</i> without a pre-loaded key cannot be used with this function. Depending on what algorithm was fetched, certain details revolving around the treatment of the input to EVP_PKEY_verify() may be pre-determined, and in that case, those details may normally not be changed. See <a href="#NOTES">&quot;NOTES&quot;</a> below for a deeper explanation.</p>

<p>EVP_PKEY_verify_message_init() initializes a public key algorithm context <i>ctx</i> for verifying an unlimited size message using the algorithm given by <i>algo</i> and the key given through <a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a> or <a href="../man3/EVP_PKEY_CTX_new_from_pkey.html">EVP_PKEY_CTX_new_from_pkey(3)</a>. Passing the message is supported both in a one-shot fashion using EVP_PKEY_verify(), and through the combination of EVP_PKEY_verify_update() and EVP_PKEY_verify_final(). This function enables using algorithms that can process input of arbitrary length, such as ED25519, RSA-SHA256 and similar.</p>

<p>EVP_PKEY_CTX_set_signature() specifies the <i>siglen</i> bytes long signature <i>sig</i> to be verified against by EVP_PKEY_verify_final(). It <i>must</i> be used together with EVP_PKEY_verify_update() and EVP_PKEY_verify_final(). See <a href="#NOTES">&quot;NOTES&quot;</a> below for a deeper explanation.</p>

<p>EVP_PKEY_verify_update() adds <i>inlen</i> bytes from <i>in</i> to the data to be processed for verification. The signature algorithm specification and implementation determine how the input bytes are processed and if there&#39;s a limit on the total size of the input. See <a href="#NOTES">&quot;NOTES&quot;</a> below for a deeper explanation.</p>

<p>EVP_PKEY_verify_final() verifies the processed data, given only <i>ctx</i>. The signature to verify against must have been given with EVP_PKEY_CTX_set_signature().</p>

<p>EVP_PKEY_verify() is a one-shot function that performs the same thing as EVP_PKEY_CTX_set_signature() call with <i>sig</i> and <i>siglen</i> as parameters, followed by a single EVP_PKEY_verify_update() call with <i>tbs</i> and <i>tbslen</i>, followed by EVP_PKEY_verify_final() call.</p>

<h1 id="NOTES">NOTES</h1>

<h2 id="General">General</h2>

<p>Some signature implementations only accumulate the input data and do no further processing before verifying it (they expect the input to be a digest), while others compress the data, typically by internally producing a digest, and signing the result, which is then verified against a given signature. Some of them support both modes of operation at the same time. The caller is expected to know how the chosen algorithm is supposed to behave and under what conditions.</p>

<p>For example, an RSA implementation can be expected to only expect a digest as input, while ED25519 can be expected to process the input with a hash, i.e. to produce the digest internally, and while RSA-SHA256 can be expected to handle either mode of operation, depending on if the operation was initialized with EVP_PKEY_verify_init_ex2() or with EVP_PKEY_verify_message_init().</p>

<p>Similarly, an RSA implementation usually expects additional details to be set, like the message digest algorithm that the input is supposed to be digested with, as well as the padding mode (see <a href="../man3/EVP_PKEY_CTX_set_signature_md.html">EVP_PKEY_CTX_set_signature_md(3)</a> and <a href="../man3/EVP_PKEY_CTX_set_rsa_padding.html">EVP_PKEY_CTX_set_rsa_padding(3)</a> and similar others), while an RSA-SHA256 implementation usually has these details pre-set and immutable.</p>

<p>The functions described here can&#39;t be used to combine separate algorithms. In particular, neither <a href="../man3/EVP_PKEY_CTX_set_signature_md.html">EVP_PKEY_CTX_set_signature_md(3)</a> nor the <b>OSSL_PARAM</b> parameter &quot;digest&quot; (<b>OSSL_SIGNATURE_PARAM_DIGEST</b>) can be used to combine a signature algorithm with a hash algorithm to process the input. In other words, it&#39;s not possible to specify a <i>ctx</i> pre-loaded with an RSA pkey, or an <i>algo</i> that fetched <code>RSA</code> and try to specify SHA256 separately to get the functionality of RSA-SHA256. If combining algorithms in that manner is desired, please use <a href="../man3/EVP_DigestVerifyInit.html">EVP_DigestVerifyInit(3)</a> and associated functions, or <a href="../man3/EVP_VerifyInit.html">EVP_VerifyInit(3)</a> and associated functions.</p>

<h2 id="Performing-multiple-verifications">Performing multiple verifications</h2>

<p>When initialized using EVP_PKEY_verify_init_ex() or EVP_PKEY_verify_init_ex2(), EVP_PKEY_verify() can be called more than once on the same context to have several one-shot operations performed using the same parameters.</p>

<p>When initialized using EVP_PKEY_verify_message_init(), it&#39;s not possible to call EVP_PKEY_verify() multiple times.</p>

<h2 id="On-EVP_PKEY_CTX_set_signature">On EVP_PKEY_CTX_set_signature()</h2>

<p>Some signature algorithms (such as LMS) require the signature verification data be specified before verifying the message. Other algorithms allow the signature to be specified late. To allow either way (which may depend on the application&#39;s flow of input), the signature to be verified against <i>must</i> be specified using this function when using EVP_PKEY_verify_message_update() and EVP_PKEY_verify_message_final() to perform the verification.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>All functions return 1 for success and 0 or a negative value for failure. However, unlike other functions, the return value 0 from EVP_PKEY_verify(), EVP_PKEY_verify_recover() and EVP_PKEY_verify_message_final() only indicates that the signature did not verify successfully (that is tbs did not match the original data or the signature was of invalid form) it is not an indication of a more serious error.</p>

<p>A negative value indicates an error other that signature verification failure. In particular a return value of -2 indicates the operation is not supported by the public key algorithm.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<h2 id="RSA-with-PKCS-1-padding-for-SHA256">RSA with PKCS#1 padding for SHA256</h2>

<p>Verify signature using PKCS#1 padding and a SHA256 digest as input:</p>

<pre><code>#include &lt;openssl/evp.h&gt;
#include &lt;openssl/rsa.h&gt;

EVP_PKEY_CTX *ctx;
unsigned char *md, *sig;
size_t mdlen, siglen;
EVP_PKEY *verify_key;

/*
 * NB: assumes verify_key, sig, siglen md and mdlen are already set up
 * and that verify_key is an RSA public key
 */
ctx = EVP_PKEY_CTX_new(verify_key, NULL /* no engine */);
if (ctx == NULL)
    /* Error occurred */
if (EVP_PKEY_verify_init(ctx) &lt;= 0)
    /* Error */
if (EVP_PKEY_CTX_set_rsa_padding(ctx, RSA_PKCS1_PADDING) &lt;= 0)
    /* Error */
if (EVP_PKEY_CTX_set_signature_md(ctx, EVP_sha256()) &lt;= 0)
    /* Error */

/* Perform operation */
ret = EVP_PKEY_verify(ctx, sig, siglen, md, mdlen);

/*
 * ret == 1 indicates success, 0 verify failure and &lt; 0 for some
 * other error.
 */</code></pre>

<h2 id="RSA-SHA256-with-a-pre-computed-digest">RSA-SHA256 with a pre-computed digest</h2>

<p>Verify a digest with RSA-SHA256 using one-shot functions. To be noted is that RSA-SHA256 is assumed to be an implementation of <code>sha256WithRSAEncryption</code>, for which the padding is pre-determined to be <b>RSA_PKCS1_PADDING</b>, and the input digest is assumed to have been computed using SHA256.</p>

<pre><code>#include &lt;openssl/evp.h&gt;
#include &lt;openssl/rsa.h&gt;

EVP_PKEY_CTX *ctx;
/* md is a SHA-256 digest in this example. */
unsigned char *md, *sig;
size_t mdlen = 32, siglen;
EVP_PKEY *signing_key;

/*
 * NB: assumes verify_key, sig, siglen, md and mdlen are already set up
 * and that verify_key is an RSA public key
 */
ctx = EVP_PKEY_CTX_new(signing_key, NULL /* no engine */);
alg = EVP_SIGNATURE_fetch(NULL, &quot;RSA-SHA256&quot;, NULL);

if (ctx == NULL)
    /* Error occurred */
if (EVP_PKEY_verify_init_ex2(ctx, alg, NULL) &lt;= 0)
    /* Error */

/* Determine buffer length */
if (EVP_PKEY_verify(ctx, sig, siglen, md, mdlen) &lt;= 0)
    /* Error or signature doesn&#39;t verify */

/* Perform operation */
ret = EVP_PKEY_verify(ctx, sig, siglen, md, mdlen);

/*
 * ret == 1 indicates success, 0 verify failure and &lt; 0 for some
 * other error.
 */</code></pre>

<h2 id="RSA-SHA256-one-shot">RSA-SHA256, one-shot</h2>

<p>Verify a document with RSA-SHA256 using one-shot functions. To be noted is that RSA-SHA256 is assumed to be an implementation of <code>sha256WithRSAEncryption</code>, for which the padding is pre-determined to be <b>RSA_PKCS1_PADDING</b>.</p>

<pre><code>#include &lt;openssl/evp.h&gt;
#include &lt;openssl/rsa.h&gt;

EVP_PKEY_CTX *ctx;
/* in the input in this example. */
unsigned char *in, *sig;
/* inlen is the length of the input in this example. */
size_t inlen, siglen;
EVP_PKEY *signing_key;
EVP_SIGNATURE *alg;

/*
 * NB: assumes signing_key, in and inlen are set up before
 * the next step. signing_key must be an RSA private key,
 * in must point to data to be digested and signed, and
 * inlen must be the size of the data in bytes.
 */
ctx = EVP_PKEY_CTX_new(signing_key, NULL /* no engine */);
alg = EVP_SIGNATURE_fetch(NULL, &quot;RSA-SHA256&quot;, NULL);

if (ctx == NULL || alg == NULL)
    /* Error occurred */
if (EVP_PKEY_verify_message_init(ctx, alg, NULL) &lt;= 0)
    /* Error */

/* Perform operation */
ret = EVP_PKEY_verify(ctx, sig, siglen, in, inlen);

/*
 * ret == 1 indicates success, 0 verify failure and &lt; 0 for some
 * other error.
 */</code></pre>

<h2 id="RSA-SHA256-using-update-and-final">RSA-SHA256, using update and final</h2>

<p>This is the same as the previous example, but allowing stream-like functionality.</p>

<pre><code>#include &lt;openssl/evp.h&gt;
#include &lt;openssl/rsa.h&gt;

EVP_PKEY_CTX *ctx;
/* in is the input in this example. */
unsigned char *in, *sig;
/* inlen is the length of the input in this example. */
size_t inlen, siglen;
EVP_PKEY *signing_key;
EVP_SIGNATURE *alg;

/*
 * NB: assumes signing_key, in and inlen are set up before
 * the next step. signing_key must be an RSA private key,
 * in must point to data to be digested and signed, and
 * inlen must be the size of the data in bytes.
 */
ctx = EVP_PKEY_CTX_new(signing_key, NULL /* no engine */);
alg = EVP_SIGNATURE_fetch(NULL, &quot;RSA-SHA256&quot;, NULL);

if (ctx == NULL || alg == NULL)
    /* Error occurred */
if (EVP_PKEY_verify_message_init(ctx, alg, NULL) &lt;= 0)
    /* Error */

/* We have the signature, specify it early */
EVP_PKEY_CTX_set_signature(ctx, sig, siglen);

/* Perform operation */
while (inlen &gt; 0) {
    if (EVP_PKEY_verify_message_update(ctx, in, inlen)) &lt;= 0)
        /* Error */
    if (inlen &gt; 256) {
        inlen -= 256;
        in += 256;
    } else {
        inlen = 0;
    }
}
ret = EVP_PKEY_verify_message_final(ctx);

/*
 * ret == 1 indicates success, 0 verify failure and &lt; 0 for some
 * other error.
 */</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a>, <a href="../man3/EVP_PKEY_encrypt.html">EVP_PKEY_encrypt(3)</a>, <a href="../man3/EVP_PKEY_decrypt.html">EVP_PKEY_decrypt(3)</a>, <a href="../man3/EVP_PKEY_sign.html">EVP_PKEY_sign(3)</a>, <a href="../man3/EVP_PKEY_verify_recover.html">EVP_PKEY_verify_recover(3)</a>, <a href="../man3/EVP_PKEY_derive.html">EVP_PKEY_derive(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The EVP_PKEY_verify_init() and EVP_PKEY_verify() functions were added in OpenSSL 1.0.0.</p>

<p>The EVP_PKEY_verify_init_ex() function was added in OpenSSL 3.0.</p>

<p>The EVP_PKEY_verify_init_ex2(), EVP_PKEY_verify_message_init(), EVP_PKEY_verify_message_update(), EVP_PKEY_verify_message_final() and EVP_PKEY_CTX_set_signature() functions where added in OpenSSL 3.4.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2006-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


