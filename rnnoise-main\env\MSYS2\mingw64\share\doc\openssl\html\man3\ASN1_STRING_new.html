<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ASN1_STRING_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ASN1_STRING_new, ASN1_STRING_type_new, ASN1_STRING_free - ASN1_STRING allocation functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/asn1.h&gt;

ASN1_STRING *ASN1_STRING_new(void);
ASN1_STRING *ASN1_STRING_type_new(int type);
void ASN1_STRING_free(ASN1_STRING *a);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>ASN1_STRING_new() returns an allocated <b>ASN1_STRING</b> structure. Its type is undefined.</p>

<p>ASN1_STRING_type_new() returns an allocated <b>ASN1_STRING</b> structure of type <i>type</i>.</p>

<p>ASN1_STRING_free() frees up <i>a</i>. If <i>a</i> is NULL nothing is done.</p>

<h1 id="NOTES">NOTES</h1>

<p>Other string types call the <b>ASN1_STRING</b> functions. For example ASN1_OCTET_STRING_new() calls ASN1_STRING_type_new(V_ASN1_OCTET_STRING).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>ASN1_STRING_new() and ASN1_STRING_type_new() return a valid <b>ASN1_STRING</b> structure or NULL if an error occurred.</p>

<p>ASN1_STRING_free() does not return a value.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2002-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


