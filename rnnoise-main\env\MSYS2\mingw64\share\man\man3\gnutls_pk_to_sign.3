.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pk_to_sign" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pk_to_sign \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_sign_algorithm_t gnutls_pk_to_sign(gnutls_pk_algorithm_t " pk ", gnutls_digest_algorithm_t " hash ");"
.SH ARGUMENTS
.IP "gnutls_pk_algorithm_t pk" 12
is a public key algorithm
.IP "gnutls_digest_algorithm_t hash" 12
a hash algorithm
.SH "DESCRIPTION"
This function maps public key and hash algorithms combinations
to signature algorithms.
.SH "RETURNS"
return a \fBgnutls_sign_algorithm_t\fP value, or \fBGNUTLS_SIGN_UNKNOWN\fP on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
