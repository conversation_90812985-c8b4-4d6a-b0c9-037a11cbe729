/* Autogenerated by regenerate-attr-urls.py.  */

const attr_url_entry enumerator_attrs[] = {
 { "deprecated", "gcc/Enumerator-Attributes.html#index-deprecated-enumerator-attribute", "", 10},
 { "unavailable", "gcc/Enumerator-Attributes.html#index-unavailable-enumerator-attribute", "", 11},
};

const attr_url_entry function_attrs[] = {
 { "OS_main", "gcc/AVR-Function-Attributes.html#index-OS_005fmain-function-attribute_002c-AVR", "AVR", 7},
 { "OS_task", "gcc/AVR-Function-Attributes.html#index-OS_005ftask-function-attribute_002c-AVR", "AVR", 7},
 { "abi_tag", "gcc/C_002b_002b-Attributes.html#index-abi_005ftag-function-attribute", "", 7},
 { "access", "gcc/Common-Function-Attributes.html#index-access-function-attribute", "", 6},
 { "alias", "gcc/Common-Function-Attributes.html#index-alias-function-attribute", "", 5},
 { "aligned", "gcc/Common-Function-Attributes.html#index-aligned-function-attribute", "", 7},
 { "alloc_align", "gcc/Common-Function-Attributes.html#index-alloc_005falign-function-attribute", "", 11},
 { "alloc_size", "gcc/Common-Function-Attributes.html#index-alloc_005fsize-function-attribute", "", 10},
 { "always_inline", "gcc/Common-Function-Attributes.html#index-always_005finline-function-attribute", "", 13},
 { "amdgpu_hsa_kernel", "gcc/AMD-GCN-Function-Attributes.html#index-amdgpu_005fhsa_005fkernel-function-attribute_002c-AMD-GCN", "AMD GCN", 17},
 { "arch=", "gcc/AArch64-Function-Attributes.html#index-arch_003d-function-attribute_002c-AArch64", "AArch64", 5},
 { "arch=", "gcc/ARM-Function-Attributes.html#index-arch_003d-function-attribute_002c-ARM", "ARM", 5},
 { "arch=", "gcc/LoongArch-Function-Attributes.html#index-arch_003d-function-attribute_002c-LoongArch", "LoongArch", 5},
 { "arch=", "gcc/RISC-V-Function-Attributes.html#index-arch_003d-function-attribute_002c-RISC-V", "RISC-V", 5},
 { "artificial", "gcc/Common-Function-Attributes.html#index-artificial-function-attribute", "", 10},
 { "assume_aligned", "gcc/Common-Function-Attributes.html#index-assume_005faligned-function-attribute", "", 14},
 { "bank_switch", "gcc/M32C-Function-Attributes.html#index-bank_005fswitch-function-attribute_002c-M32C", "M32C", 11},
 { "branch-protection", "gcc/AArch64-Function-Attributes.html#index-branch-protection-function-attribute_002c-AArch64", "AArch64", 17},
 { "break_handler", "gcc/MicroBlaze-Function-Attributes.html#index-break_005fhandler-function-attribute_002c-MicroBlaze", "MicroBlaze", 13},
 { "brk_interrupt", "gcc/RL78-Function-Attributes.html#index-brk_005finterrupt-function-attribute_002c-RL78", "RL78", 13},
 { "callee_pop_aggregate_return", "gcc/x86-Function-Attributes.html#index-callee_005fpop_005faggregate_005freturn-function-attribute_002c-x86", "x86", 27},
 { "cdecl", "gcc/x86-Function-Attributes.html#index-cdecl-function-attribute_002c-x86-32", "x86-32", 5},
 { "cf_check", "gcc/x86-Function-Attributes.html#index-cf_005fcheck-function-attribute_002c-x86", "x86", 8},
 { "cmodel=", "gcc/AArch64-Function-Attributes.html#index-cmodel_003d-function-attribute_002c-AArch64", "AArch64", 7},
 { "cmodel=", "gcc/LoongArch-Function-Attributes.html#index-cmodel_003d-function-attribute_002c-LoongArch", "LoongArch", 7},
 { "code_readable", "gcc/MIPS-Function-Attributes.html#index-code_005freadable-function-attribute_002c-MIPS", "MIPS", 13},
 { "cold", "gcc/Common-Function-Attributes.html#index-cold-function-attribute", "", 4},
 { "const", "gcc/Common-Function-Attributes.html#index-const-function-attribute", "", 5},
 { "constructor", "gcc/Common-Function-Attributes.html#index-constructor-function-attribute", "", 11},
 { "copy", "gcc/Common-Function-Attributes.html#index-copy-function-attribute", "", 4},
 { "cpu=", "gcc/AArch64-Function-Attributes.html#index-cpu_003d-function-attribute_002c-AArch64", "AArch64", 4},
 { "cpu=", "gcc/RISC-V-Function-Attributes.html#index-cpu_003d-function-attribute_002c-RISC-V", "RISC-V", 4},
 { "critical", "gcc/MSP430-Function-Attributes.html#index-critical-function-attribute_002c-MSP430", "MSP430", 8},
 { "deprecated", "gcc/Common-Function-Attributes.html#index-deprecated-function-attribute", "", 10},
 { "destructor", "gcc/Common-Function-Attributes.html#index-destructor-function-attribute", "", 10},
 { "disinterrupt", "gcc/Epiphany-Function-Attributes.html#index-disinterrupt-function-attribute_002c-Epiphany", "Epiphany", 12},
 { "dllexport", "gcc/Microsoft-Windows-Function-Attributes.html#index-dllexport-function-attribute", "", 9},
 { "dllimport", "gcc/Microsoft-Windows-Function-Attributes.html#index-dllimport-function-attribute", "", 9},
 { "either", "gcc/MSP430-Function-Attributes.html#index-either-function-attribute_002c-MSP430", "MSP430", 6},
 { "error", "gcc/Common-Function-Attributes.html#index-error-function-attribute", "", 5},
 { "exception", "gcc/NDS32-Function-Attributes.html#index-exception-function-attribute", "", 9},
 { "exception_handler", "gcc/Blackfin-Function-Attributes.html#index-exception_005fhandler-function-attribute", "", 17},
 { "expected_throw", "gcc/Common-Function-Attributes.html#index-expected_005fthrow-function-attribute", "", 14},
 { "externally_visible", "gcc/Common-Function-Attributes.html#index-externally_005fvisible-function-attribute", "", 18},
 { "far", "gcc/MIPS-Function-Attributes.html#index-far-function-attribute_002c-MIPS", "MIPS", 3},
 { "fast_interrupt", "gcc/M32C-Function-Attributes.html#index-fast_005finterrupt-function-attribute_002c-M32C", "M32C", 14},
 { "fast_interrupt", "gcc/MicroBlaze-Function-Attributes.html#index-fast_005finterrupt-function-attribute_002c-MicroBlaze", "MicroBlaze", 14},
 { "fast_interrupt", "gcc/RX-Function-Attributes.html#index-fast_005finterrupt-function-attribute_002c-RX", "RX", 14},
 { "fastcall", "gcc/x86-Function-Attributes.html#index-fastcall-function-attribute_002c-x86-32", "x86-32", 8},
 { "fd_arg", "gcc/Common-Function-Attributes.html#index-fd_005farg-function-attribute", "", 6},
 { "fd_arg_read", "gcc/Common-Function-Attributes.html#index-fd_005farg_005fread-function-attribute", "", 11},
 { "fd_arg_write", "gcc/Common-Function-Attributes.html#index-fd_005farg_005fwrite-function-attribute", "", 12},
 { "fentry_name", "gcc/x86-Function-Attributes.html#index-fentry_005fname-function-attribute_002c-x86", "x86", 11},
 { "fentry_section", "gcc/x86-Function-Attributes.html#index-fentry_005fsection-function-attribute_002c-x86", "x86", 14},
 { "fix-cortex-a53-835769", "gcc/AArch64-Function-Attributes.html#index-fix-cortex-a53-835769-function-attribute_002c-AArch64", "AArch64", 21},
 { "flatten", "gcc/Common-Function-Attributes.html#index-flatten-function-attribute", "", 7},
 { "force_align_arg_pointer", "gcc/x86-Function-Attributes.html#index-force_005falign_005farg_005fpointer-function-attribute_002c-x86", "x86", 23},
 { "format", "gcc/Common-Function-Attributes.html#index-format-function-attribute", "", 6},
 { "format_arg", "gcc/Common-Function-Attributes.html#index-format_005farg-function-attribute", "", 10},
 { "forwarder_section", "gcc/Epiphany-Function-Attributes.html#index-forwarder_005fsection-function-attribute_002c-Epiphany", "Epiphany", 17},
 { "function_return", "gcc/x86-Function-Attributes.html#index-function_005freturn-function-attribute_002c-x86", "x86", 15},
 { "function_vector", "gcc/H8_002f300-Function-Attributes.html#index-function_005fvector-function-attribute_002c-H8_002f300", "H8/300", 15},
 { "function_vector", "gcc/M32C-Function-Attributes.html#index-function_005fvector-function-attribute_002c-M16C_002fM32C", "M16C/M32C", 15},
 { "function_vector", "gcc/SH-Function-Attributes.html#index-function_005fvector-function-attribute_002c-SH", "SH", 15},
 { "general-regs-only", "gcc/AArch64-Function-Attributes.html#index-general-regs-only-function-attribute_002c-AArch64", "AArch64", 17},
 { "general-regs-only", "gcc/ARM-Function-Attributes.html#index-general-regs-only-function-attribute_002c-ARM", "ARM", 17},
 { "gnu_inline", "gcc/Common-Function-Attributes.html#index-gnu_005finline-function-attribute", "", 10},
 { "hot", "gcc/Common-Function-Attributes.html#index-hot-function-attribute", "", 3},
 { "hotpatch", "gcc/S_002f390-Function-Attributes.html#index-hotpatch-function-attribute_002c-S_002f390", "S/390", 8},
 { "ifunc", "gcc/Common-Function-Attributes.html#index-ifunc-function-attribute", "", 5},
 { "indirect_branch", "gcc/x86-Function-Attributes.html#index-indirect_005fbranch-function-attribute_002c-x86", "x86", 15},
 { "indirect_return", "gcc/AArch64-Function-Attributes.html#index-indirect_005freturn-function-attribute_002c-AArch64", "AArch64", 15},
 { "indirect_return", "gcc/x86-Function-Attributes.html#index-indirect_005freturn-function-attribute_002c-x86", "x86", 15},
 { "interrupt", "gcc/ARC-Function-Attributes.html#index-interrupt-function-attribute_002c-ARC", "ARC", 9},
 { "interrupt", "gcc/ARM-Function-Attributes.html#index-interrupt-function-attribute_002c-ARM", "ARM", 9},
 { "interrupt", "gcc/AVR-Function-Attributes.html#index-interrupt-function-attribute_002c-AVR", "AVR", 9},
 { "interrupt", "gcc/C-SKY-Function-Attributes.html#index-interrupt-function-attribute_002c-C-SKY", "C-SKY", 9},
 { "interrupt", "gcc/Common-Function-Attributes.html#index-interrupt-function-attribute", "", 9},
 { "interrupt", "gcc/Epiphany-Function-Attributes.html#index-interrupt-function-attribute_002c-Epiphany", "Epiphany", 9},
 { "interrupt", "gcc/M32C-Function-Attributes.html#index-interrupt-function-attribute_002c-M32C", "M32C", 9},
 { "interrupt", "gcc/M32R_002fD-Function-Attributes.html#index-interrupt-function-attribute_002c-M32R_002fD", "M32R/D", 9},
 { "interrupt", "gcc/MIPS-Function-Attributes.html#index-interrupt-function-attribute_002c-MIPS", "MIPS", 9},
 { "interrupt", "gcc/MSP430-Function-Attributes.html#index-interrupt-function-attribute_002c-MSP430", "MSP430", 9},
 { "interrupt", "gcc/NDS32-Function-Attributes.html#index-interrupt-function-attribute_002c-NDS32", "NDS32", 9},
 { "interrupt", "gcc/RISC-V-Function-Attributes.html#index-interrupt-function-attribute_002c-RISC-V", "RISC-V", 9},
 { "interrupt", "gcc/RL78-Function-Attributes.html#index-interrupt-function-attribute_002c-RL78", "RL78", 9},
 { "interrupt", "gcc/RX-Function-Attributes.html#index-interrupt-function-attribute_002c-RX", "RX", 9},
 { "interrupt", "gcc/V850-Function-Attributes.html#index-interrupt-function-attribute_002c-V850", "V850", 9},
 { "interrupt", "gcc/Visium-Function-Attributes.html#index-interrupt-function-attribute_002c-Visium", "Visium", 9},
 { "interrupt", "gcc/Xstormy16-Function-Attributes.html#index-interrupt-function-attribute_002c-Xstormy16", "Xstormy16", 9},
 { "interrupt", "gcc/m68k-Function-Attributes.html#index-interrupt-function-attribute_002c-m68k", "m68k", 9},
 { "interrupt", "gcc/x86-Function-Attributes.html#index-interrupt-function-attribute_002c-x86", "x86", 9},
 { "interrupt_handler", "gcc/Blackfin-Function-Attributes.html#index-interrupt_005fhandler-function-attribute_002c-Blackfin", "Blackfin", 17},
 { "interrupt_handler", "gcc/Common-Function-Attributes.html#index-interrupt_005fhandler-function-attribute", "", 17},
 { "interrupt_handler", "gcc/H8_002f300-Function-Attributes.html#index-interrupt_005fhandler-function-attribute_002c-H8_002f300", "H8/300", 17},
 { "interrupt_handler", "gcc/MicroBlaze-Function-Attributes.html#index-interrupt_005fhandler-function-attribute_002c-MicroBlaze", "MicroBlaze", 17},
 { "interrupt_handler", "gcc/SH-Function-Attributes.html#index-interrupt_005fhandler-function-attribute_002c-SH", "SH", 17},
 { "interrupt_handler", "gcc/V850-Function-Attributes.html#index-interrupt_005fhandler-function-attribute_002c-V850", "V850", 17},
 { "interrupt_handler", "gcc/m68k-Function-Attributes.html#index-interrupt_005fhandler-function-attribute_002c-m68k", "m68k", 17},
 { "interrupt_thread", "gcc/m68k-Function-Attributes.html#index-interrupt_005fthread-function-attribute_002c-fido", "fido", 16},
 { "isr", "gcc/ARM-Function-Attributes.html#index-isr-function-attribute_002c-ARM", "ARM", 3},
 { "isr", "gcc/C-SKY-Function-Attributes.html#index-isr-function-attribute_002c-C-SKY", "C-SKY", 3},
 { "jli_always", "gcc/ARC-Function-Attributes.html#index-jli_005falways-function-attribute_002c-ARC", "ARC", 10},
 { "jli_fixed", "gcc/ARC-Function-Attributes.html#index-jli_005ffixed-function-attribute_002c-ARC", "ARC", 9},
 { "keep_interrupts_masked", "gcc/MIPS-Function-Attributes.html#index-keep_005finterrupts_005fmasked-function-attribute_002c-MIPS", "MIPS", 22},
 { "kernel", "gcc/Nvidia-PTX-Function-Attributes.html#index-kernel-function-attribute_002c-Nvidia-PTX", "Nvidia PTX", 6},
 { "kspisusp", "gcc/Blackfin-Function-Attributes.html#index-kspisusp-function-attribute_002c-Blackfin", "Blackfin", 8},
 { "l1_text", "gcc/Blackfin-Function-Attributes.html#index-l1_005ftext-function-attribute_002c-Blackfin", "Blackfin", 7},
 { "l2", "gcc/Blackfin-Function-Attributes.html#index-l2-function-attribute_002c-Blackfin", "Blackfin", 2},
 { "lasx", "gcc/LoongArch-Function-Attributes.html#index-lasx-function-attribute_002c-LoongArch", "LoongArch", 4},
 { "leaf", "gcc/Common-Function-Attributes.html#index-leaf-function-attribute", "", 4},
 { "long_call", "gcc/ARC-Function-Attributes.html#index-long_005fcall-function-attribute_002c-ARC", "ARC", 9},
 { "long_call", "gcc/ARM-Function-Attributes.html#index-long_005fcall-function-attribute_002c-ARM", "ARM", 9},
 { "long_call", "gcc/Epiphany-Function-Attributes.html#index-long_005fcall-function-attribute_002c-Epiphany", "Epiphany", 9},
 { "long_call", "gcc/MIPS-Function-Attributes.html#index-long_005fcall-function-attribute_002c-MIPS", "MIPS", 9},
 { "longcall", "gcc/Blackfin-Function-Attributes.html#index-longcall-function-attribute_002c-Blackfin", "Blackfin", 8},
 { "longcall", "gcc/PowerPC-Function-Attributes.html#index-longcall-function-attribute_002c-PowerPC", "PowerPC", 8},
 { "lower", "gcc/MSP430-Function-Attributes.html#index-lower-function-attribute_002c-MSP430", "MSP430", 5},
 { "lsx", "gcc/LoongArch-Function-Attributes.html#index-lsx-function-attribute_002c-LoongArch", "LoongArch", 3},
 { "malloc", "gcc/Common-Function-Attributes.html#index-malloc-function-attribute", "", 6},
 { "medium_call", "gcc/ARC-Function-Attributes.html#index-medium_005fcall-function-attribute_002c-ARC", "ARC", 11},
 { "micromips", "gcc/MIPS-Function-Attributes.html#index-micromips-function-attribute", "", 9},
 { "mips16", "gcc/MIPS-Function-Attributes.html#index-mips16-function-attribute_002c-MIPS", "MIPS", 6},
 { "model", "gcc/M32R_002fD-Function-Attributes.html#index-model-function-attribute_002c-M32R_002fD", "M32R/D", 5},
 { "ms_abi", "gcc/x86-Function-Attributes.html#index-ms_005fabi-function-attribute_002c-x86", "x86", 6},
 { "ms_hook_prologue", "gcc/x86-Function-Attributes.html#index-ms_005fhook_005fprologue-function-attribute_002c-x86", "x86", 16},
 { "naked", "gcc/ARC-Function-Attributes.html#index-naked-function-attribute_002c-ARC", "ARC", 5},
 { "naked", "gcc/ARM-Function-Attributes.html#index-naked-function-attribute_002c-ARM", "ARM", 5},
 { "naked", "gcc/AVR-Function-Attributes.html#index-naked-function-attribute_002c-AVR", "AVR", 5},
 { "naked", "gcc/BPF-Function-Attributes.html#index-naked-function-attribute_002c-BPF", "BPF", 5},
 { "naked", "gcc/C-SKY-Function-Attributes.html#index-naked-function-attribute_002c-C-SKY", "C-SKY", 5},
 { "naked", "gcc/MCORE-Function-Attributes.html#index-naked-function-attribute_002c-MCORE", "MCORE", 5},
 { "naked", "gcc/MSP430-Function-Attributes.html#index-naked-function-attribute_002c-MSP430", "MSP430", 5},
 { "naked", "gcc/NDS32-Function-Attributes.html#index-naked-function-attribute_002c-NDS32", "NDS32", 5},
 { "naked", "gcc/RISC-V-Function-Attributes.html#index-naked-function-attribute_002c-RISC-V", "RISC-V", 5},
 { "naked", "gcc/RL78-Function-Attributes.html#index-naked-function-attribute_002c-RL78", "RL78", 5},
 { "naked", "gcc/RX-Function-Attributes.html#index-naked-function-attribute_002c-RX", "RX", 5},
 { "naked", "gcc/x86-Function-Attributes.html#index-naked-function-attribute_002c-x86", "x86", 5},
 { "near", "gcc/MIPS-Function-Attributes.html#index-near-function-attribute_002c-MIPS", "MIPS", 4},
 { "nested", "gcc/NDS32-Function-Attributes.html#index-nested-function-attribute_002c-NDS32", "NDS32", 6},
 { "nested_ready", "gcc/NDS32-Function-Attributes.html#index-nested_005fready-function-attribute_002c-NDS32", "NDS32", 12},
 { "nesting", "gcc/Blackfin-Function-Attributes.html#index-nesting-function-attribute_002c-Blackfin", "Blackfin", 7},
 { "nmi", "gcc/NDS32-Function-Attributes.html#index-nmi-function-attribute_002c-NDS32", "NDS32", 3},
 { "nmi_handler", "gcc/Blackfin-Function-Attributes.html#index-nmi_005fhandler-function-attribute_002c-Blackfin", "Blackfin", 11},
 { "no_callee_saved_registers", "gcc/x86-Function-Attributes.html#index-no_005fcallee_005fsaved_005fregisters-function-attribute_002c-x86", "x86", 25},
 { "no_caller_saved_registers", "gcc/x86-Function-Attributes.html#index-no_005fcaller_005fsaved_005fregisters-function-attribute_002c-x86", "x86", 25},
 { "no_dangling", "gcc/C_002b_002b-Attributes.html#index-no_005fdangling-function-attribute", "", 11},
 { "no_gccisr", "gcc/AVR-Function-Attributes.html#index-no_005fgccisr-function-attribute_002c-AVR", "AVR", 9},
 { "no_icf", "gcc/Common-Function-Attributes.html#index-no_005ficf-function-attribute", "", 6},
 { "no_instrument_function", "gcc/Common-Function-Attributes.html#index-no_005finstrument_005ffunction-function-attribute", "", 22},
 { "no_profile_instrument_function", "gcc/Common-Function-Attributes.html#index-no_005fprofile_005finstrument_005ffunction-function-attribute", "", 30},
 { "no_reorder", "gcc/Common-Function-Attributes.html#index-no_005freorder-function-attribute", "", 10},
 { "no_sanitize", "gcc/Common-Function-Attributes.html#index-no_005fsanitize-function-attribute", "", 11},
 { "no_sanitize_address", "gcc/Common-Function-Attributes.html#index-no_005fsanitize_005faddress-function-attribute", "", 19},
 { "no_sanitize_coverage", "gcc/Common-Function-Attributes.html#index-no_005fsanitize_005fcoverage-function-attribute", "", 20},
 { "no_sanitize_thread", "gcc/Common-Function-Attributes.html#index-no_005fsanitize_005fthread-function-attribute", "", 18},
 { "no_sanitize_undefined", "gcc/Common-Function-Attributes.html#index-no_005fsanitize_005fundefined-function-attribute", "", 21},
 { "no_split_stack", "gcc/Common-Function-Attributes.html#index-no_005fsplit_005fstack-function-attribute", "", 14},
 { "no_stack_limit", "gcc/Common-Function-Attributes.html#index-no_005fstack_005flimit-function-attribute", "", 14},
 { "no_stack_protector", "gcc/Common-Function-Attributes.html#index-no_005fstack_005fprotector-function-attribute", "", 18},
 { "noblock", "gcc/AVR-Function-Attributes.html#index-noblock-function-attribute_002c-AVR", "AVR", 7},
 { "nocf_check", "gcc/x86-Function-Attributes.html#index-nocf_005fcheck-function-attribute", "", 10},
 { "noclone", "gcc/Common-Function-Attributes.html#index-noclone-function-attribute", "", 7},
 { "nocompression", "gcc/MIPS-Function-Attributes.html#index-nocompression-function-attribute_002c-MIPS", "MIPS", 13},
 { "nodirect_extern_access", "gcc/x86-Function-Attributes.html#index-nodirect_005fextern_005faccess-function-attribute", "", 22},
 { "noinline", "gcc/Common-Function-Attributes.html#index-noinline-function-attribute", "", 8},
 { "noipa", "gcc/Common-Function-Attributes.html#index-noipa-function-attribute", "", 5},
 { "nomicromips", "gcc/MIPS-Function-Attributes.html#index-nomicromips-function-attribute", "", 11},
 { "nomips16", "gcc/MIPS-Function-Attributes.html#index-nomips16-function-attribute_002c-MIPS", "MIPS", 8},
 { "nonnull", "gcc/Common-Function-Attributes.html#index-nonnull-function-attribute", "", 7},
 { "nonnull_if_nonzero", "gcc/Common-Function-Attributes.html#index-nonnull_005fif_005fnonzero-function-attribute", "", 18},
 { "noplt", "gcc/Common-Function-Attributes.html#index-noplt-function-attribute", "", 5},
 { "noreturn", "gcc/Common-Function-Attributes.html#index-noreturn-function-attribute", "", 8},
 { "nosave_low_regs", "gcc/SH-Function-Attributes.html#index-nosave_005flow_005fregs-function-attribute_002c-SH", "SH", 15},
 { "not_nested", "gcc/NDS32-Function-Attributes.html#index-not_005fnested-function-attribute_002c-NDS32", "NDS32", 10},
 { "nothrow", "gcc/Common-Function-Attributes.html#index-nothrow-function-attribute", "", 7},
 { "null_terminated_string_arg", "gcc/Common-Function-Attributes.html#index-null_005fterminated_005fstring_005farg-function-attribute", "", 26},
 { "omit-leaf-frame-pointer", "gcc/AArch64-Function-Attributes.html#index-omit-leaf-frame-pointer-function-attribute_002c-AArch64", "AArch64", 23},
 { "optimize", "gcc/Common-Function-Attributes.html#index-optimize-function-attribute", "", 8},
 { "outline-atomics", "gcc/AArch64-Function-Attributes.html#index-outline-atomics-function-attribute_002c-AArch64", "AArch64", 15},
 { "partial_save", "gcc/NDS32-Function-Attributes.html#index-partial_005fsave-function-attribute_002c-NDS32", "NDS32", 12},
 { "patchable_function_entry", "gcc/Common-Function-Attributes.html#index-patchable_005ffunction_005fentry-function-attribute", "", 24},
 { "pcs", "gcc/ARM-Function-Attributes.html#index-pcs-function-attribute_002c-ARM", "ARM", 3},
 { "prefer-vector-width", "gcc/x86-Function-Attributes.html#index-prefer-vector-width-function-attribute_002c-x86", "x86", 19},
 { "pure", "gcc/Common-Function-Attributes.html#index-pure-function-attribute", "", 4},
 { "reentrant", "gcc/MSP430-Function-Attributes.html#index-reentrant-function-attribute_002c-MSP430", "MSP430", 9},
 { "regparm", "gcc/x86-Function-Attributes.html#index-regparm-function-attribute_002c-x86", "x86", 7},
 { "renesas", "gcc/SH-Function-Attributes.html#index-renesas-function-attribute_002c-SH", "SH", 7},
 { "resbank", "gcc/SH-Function-Attributes.html#index-resbank-function-attribute_002c-SH", "SH", 7},
 { "reset", "gcc/NDS32-Function-Attributes.html#index-reset-function-attribute_002c-NDS32", "NDS32", 5},
 { "retain", "gcc/Common-Function-Attributes.html#index-retain-function-attribute", "", 6},
 { "returns_nonnull", "gcc/Common-Function-Attributes.html#index-returns_005fnonnull-function-attribute", "", 15},
 { "returns_twice", "gcc/Common-Function-Attributes.html#index-returns_005ftwice-function-attribute", "", 13},
 { "riscv_vector_cc", "gcc/RISC-V-Function-Attributes.html#index-riscv_005fvector_005fcc-function-attribute_002c-RISC-V", "RISC-V", 15},
 { "save_all", "gcc/NDS32-Function-Attributes.html#index-save_005fall-function-attribute_002c-NDS32", "NDS32", 8},
 { "save_volatiles", "gcc/MicroBlaze-Function-Attributes.html#index-save_005fvolatiles-function-attribute_002c-MicroBlaze", "MicroBlaze", 14},
 { "saveall", "gcc/Blackfin-Function-Attributes.html#index-saveall-function-attribute_002c-Blackfin", "Blackfin", 7},
 { "saveall", "gcc/H8_002f300-Function-Attributes.html#index-saveall-function-attribute_002c-H8_002f300", "H8/300", 7},
 { "section", "gcc/Common-Function-Attributes.html#index-section-function-attribute", "", 7},
 { "secure_call", "gcc/ARC-Function-Attributes.html#index-secure_005fcall-function-attribute_002c-ARC", "ARC", 11},
 { "sentinel", "gcc/Common-Function-Attributes.html#index-sentinel-function-attribute", "", 8},
 { "short_call", "gcc/ARC-Function-Attributes.html#index-short_005fcall-function-attribute_002c-ARC", "ARC", 10},
 { "short_call", "gcc/ARM-Function-Attributes.html#index-short_005fcall-function-attribute_002c-ARM", "ARM", 10},
 { "short_call", "gcc/Epiphany-Function-Attributes.html#index-short_005fcall-function-attribute_002c-Epiphany", "Epiphany", 10},
 { "short_call", "gcc/MIPS-Function-Attributes.html#index-short_005fcall-function-attribute_002c-MIPS", "MIPS", 10},
 { "shortcall", "gcc/Blackfin-Function-Attributes.html#index-shortcall-function-attribute_002c-Blackfin", "Blackfin", 9},
 { "shortcall", "gcc/PowerPC-Function-Attributes.html#index-shortcall-function-attribute_002c-PowerPC", "PowerPC", 9},
 { "sign-return-address", "gcc/AArch64-Function-Attributes.html#index-sign-return-address-function-attribute_002c-AArch64", "AArch64", 19},
 { "signal", "gcc/AVR-Function-Attributes.html#index-signal-function-attribute_002c-AVR", "AVR", 6},
 { "simd", "gcc/Common-Function-Attributes.html#index-simd-function-attribute", "", 4},
 { "sp_switch", "gcc/SH-Function-Attributes.html#index-sp_005fswitch-function-attribute_002c-SH", "SH", 9},
 { "sseregparm", "gcc/x86-Function-Attributes.html#index-sseregparm-function-attribute_002c-x86", "x86", 10},
 { "stack_protect", "gcc/Common-Function-Attributes.html#index-stack_005fprotect-function-attribute", "", 13},
 { "stdcall", "gcc/x86-Function-Attributes.html#index-stdcall-function-attribute_002c-x86-32", "x86-32", 7},
 { "strict-align", "gcc/AArch64-Function-Attributes.html#index-strict-align-function-attribute_002c-AArch64", "AArch64", 12},
 { "strict-align", "gcc/LoongArch-Function-Attributes.html#index-strict-align-function-attribute_002c-LoongArch", "LoongArch", 12},
 { "symver", "gcc/Common-Function-Attributes.html#index-symver-function-attribute", "", 6},
 { "syscall_linkage", "gcc/IA-64-Function-Attributes.html#index-syscall_005flinkage-function-attribute_002c-IA-64", "IA-64", 15},
 { "sysv_abi", "gcc/x86-Function-Attributes.html#index-sysv_005fabi-function-attribute_002c-x86", "x86", 8},
 { "tainted_args", "gcc/Common-Function-Attributes.html#index-tainted_005fargs-function-attribute", "", 12},
 { "target", "gcc/ARM-Function-Attributes.html#index-target-function-attribute-1", "", 6},
 { "target", "gcc/Common-Function-Attributes.html#index-target-function-attribute", "", 6},
 { "target", "gcc/PowerPC-Function-Attributes.html#index-target-function-attribute-2", "", 6},
 { "target", "gcc/S_002f390-Function-Attributes.html#index-target-function-attribute-3", "", 6},
 { "target", "gcc/x86-Function-Attributes.html#index-target-function-attribute-4", "", 6},
 { "target_clones", "gcc/Common-Function-Attributes.html#index-target_005fclones-function-attribute", "", 13},
 { "thiscall", "gcc/x86-Function-Attributes.html#index-thiscall-function-attribute_002c-x86-32", "x86-32", 8},
 { "tls-dialect=", "gcc/AArch64-Function-Attributes.html#index-tls-dialect_003d-function-attribute_002c-AArch64", "AArch64", 12},
 { "trap_exit", "gcc/SH-Function-Attributes.html#index-trap_005fexit-function-attribute_002c-SH", "SH", 9},
 { "trapa_handler", "gcc/SH-Function-Attributes.html#index-trapa_005fhandler-function-attribute_002c-SH", "SH", 13},
 { "tune=", "gcc/AArch64-Function-Attributes.html#index-tune_003d-function-attribute_002c-AArch64", "AArch64", 5},
 { "tune=", "gcc/LoongArch-Function-Attributes.html#index-tune_003d-function-attribute_002c-LoongArch", "LoongArch", 5},
 { "tune=", "gcc/RISC-V-Function-Attributes.html#index-tune_003d-function-attribute_002c-RISC-V", "RISC-V", 5},
 { "unavailable", "gcc/Common-Function-Attributes.html#index-unavailable-function-attribute", "", 11},
 { "unused", "gcc/Common-Function-Attributes.html#index-unused-function-attribute", "", 6},
 { "upper", "gcc/MSP430-Function-Attributes.html#index-upper-function-attribute_002c-MSP430", "MSP430", 5},
 { "use_debug_exception_return", "gcc/MIPS-Function-Attributes.html#index-use_005fdebug_005fexception_005freturn-function-attribute_002c-MIPS", "MIPS", 26},
 { "use_hazard_barrier_return", "gcc/MIPS-Function-Attributes.html#index-use_005fhazard_005fbarrier_005freturn-function-attribute_002c-MIPS", "MIPS", 25},
 { "use_shadow_register_set", "gcc/MIPS-Function-Attributes.html#index-use_005fshadow_005fregister_005fset-function-attribute_002c-MIPS", "MIPS", 23},
 { "used", "gcc/Common-Function-Attributes.html#index-used-function-attribute", "", 4},
 { "vector", "gcc/RX-Function-Attributes.html#index-vector-function-attribute_002c-RX", "RX", 6},
 { "version_id", "gcc/IA-64-Function-Attributes.html#index-version_005fid-function-attribute_002c-IA-64", "IA-64", 10},
 { "visibility", "gcc/Common-Function-Attributes.html#index-visibility-function-attribute", "", 10},
 { "wakeup", "gcc/MSP430-Function-Attributes.html#index-wakeup-function-attribute_002c-MSP430", "MSP430", 6},
 { "warm", "gcc/NDS32-Function-Attributes.html#index-warm-function-attribute_002c-NDS32", "NDS32", 4},
 { "warn_unused_result", "gcc/Common-Function-Attributes.html#index-warn_005funused_005fresult-function-attribute", "", 18},
 { "warning", "gcc/Common-Function-Attributes.html#index-warning-function-attribute", "", 7},
 { "weak", "gcc/Common-Function-Attributes.html#index-weak-function-attribute", "", 4},
 { "weakref", "gcc/Common-Function-Attributes.html#index-weakref-function-attribute", "", 7},
 { "zero_call_used_regs", "gcc/Common-Function-Attributes.html#index-zero_005fcall_005fused_005fregs-function-attribute", "", 19},
};

const attr_url_entry label_attrs[] = {
 { "cold", "gcc/Label-Attributes.html#index-cold-label-attribute", "", 4},
 { "hot", "gcc/Label-Attributes.html#index-hot-label-attribute", "", 3},
 { "unused", "gcc/Label-Attributes.html#index-unused-label-attribute", "", 6},
};

const attr_url_entry statement_attrs[] = {
 { "assume", "gcc/Statement-Attributes.html#index-assume-statement-attribute", "", 6},
 { "fallthrough", "gcc/Statement-Attributes.html#index-fallthrough-statement-attribute", "", 11},
 { "musttail", "gcc/Statement-Attributes.html#index-musttail-statement-attribute", "", 8},
};

const attr_url_entry type_attrs[] = {
 { "abi_tag", "gcc/C_002b_002b-Attributes.html#index-abi_005ftag-type-attribute", "", 7},
 { "aligned", "gcc/Common-Type-Attributes.html#index-aligned-type-attribute", "", 7},
 { "alloc_size", "gcc/Common-Type-Attributes.html#index-alloc_005fsize-type-attribute", "", 10},
 { "altivec", "gcc/PowerPC-Type-Attributes.html#index-altivec-type-attribute_002c-PowerPC", "PowerPC", 7},
 { "cold", "gcc/C_002b_002b-Attributes.html#index-cold-type-attribute", "", 4},
 { "copy", "gcc/Common-Type-Attributes.html#index-copy-type-attribute", "", 4},
 { "deprecated", "gcc/Common-Type-Attributes.html#index-deprecated-type-attribute", "", 10},
 { "designated_init", "gcc/Common-Type-Attributes.html#index-designated_005finit-type-attribute", "", 15},
 { "flag_enum", "gcc/Common-Type-Attributes.html#index-flag_005fenum-type-attribute", "", 9},
 { "gcc_struct", "gcc/PowerPC-Type-Attributes.html#index-gcc_005fstruct-type-attribute_002c-PowerPC", "PowerPC", 10},
 { "gcc_struct", "gcc/x86-Type-Attributes.html#index-gcc_005fstruct-type-attribute_002c-x86", "x86", 10},
 { "hardbool", "gcc/Common-Type-Attributes.html#index-hardbool-type-attribute", "", 8},
 { "hot", "gcc/C_002b_002b-Attributes.html#index-hot-type-attribute", "", 3},
 { "may_alias", "gcc/Common-Type-Attributes.html#index-may_005falias-type-attribute", "", 9},
 { "mode", "gcc/Common-Type-Attributes.html#index-mode-type-attribute", "", 4},
 { "ms_struct", "gcc/PowerPC-Type-Attributes.html#index-ms_005fstruct-type-attribute_002c-PowerPC", "PowerPC", 9},
 { "ms_struct", "gcc/x86-Type-Attributes.html#index-ms_005fstruct-type-attribute_002c-x86", "x86", 9},
 { "no_dangling", "gcc/C_002b_002b-Attributes.html#index-no_005fdangling-type-attribute", "", 11},
 { "notshared", "gcc/ARM-Type-Attributes.html#index-notshared-type-attribute_002c-ARM", "ARM", 9},
 { "objc_root_class", "gcc/Common-Type-Attributes.html#index-objc_005froot_005fclass-type-attribute", "", 15},
 { "packed", "gcc/Common-Type-Attributes.html#index-packed-type-attribute", "", 6},
 { "preserve_access_index", "gcc/BPF-Type-Attributes.html#index-preserve_005faccess_005findex-type-attribute_002c-BPF", "BPF", 21},
 { "scalar_storage_order", "gcc/Common-Type-Attributes.html#index-scalar_005fstorage_005forder-type-attribute", "", 20},
 { "strub", "gcc/Common-Type-Attributes.html#index-strub-type-attribute", "", 5},
 { "transparent_union", "gcc/Common-Type-Attributes.html#index-transparent_005funion-type-attribute", "", 17},
 { "unavailable", "gcc/Common-Type-Attributes.html#index-unavailable-type-attribute", "", 11},
 { "uncached", "gcc/ARC-Type-Attributes.html#index-uncached-type-attribute_002c-ARC", "ARC", 8},
 { "unused", "gcc/Common-Type-Attributes.html#index-unused-type-attribute", "", 6},
 { "vector_size", "gcc/Common-Type-Attributes.html#index-vector_005fsize-type-attribute", "", 11},
 { "visibility", "gcc/Common-Type-Attributes.html#index-visibility-type-attribute", "", 10},
 { "warn_if_not_aligned", "gcc/Common-Type-Attributes.html#index-warn_005fif_005fnot_005faligned-type-attribute", "", 19},
 { "warn_unused", "gcc/C_002b_002b-Attributes.html#index-warn_005funused-type-attribute", "", 11},
};

const attr_url_entry variable_attrs[] = {
 { "abi_tag", "gcc/C_002b_002b-Attributes.html#index-abi_005ftag-variable-attribute", "", 7},
 { "absdata", "gcc/AVR-Variable-Attributes.html#index-absdata-variable-attribute_002c-AVR", "AVR", 7},
 { "address", "gcc/AVR-Variable-Attributes.html#index-address-variable-attribute_002c-AVR", "AVR", 7},
 { "alias", "gcc/Common-Variable-Attributes.html#index-alias-variable-attribute", "", 5},
 { "aligned", "gcc/Common-Variable-Attributes.html#index-aligned-variable-attribute", "", 7},
 { "alloc_size", "gcc/Common-Variable-Attributes.html#index-alloc_005fsize-variable-attribute", "", 10},
 { "altivec", "gcc/PowerPC-Variable-Attributes.html#index-altivec-variable-attribute_002c-PowerPC", "PowerPC", 7},
 { "aux", "gcc/ARC-Variable-Attributes.html#index-aux-variable-attribute_002c-ARC", "ARC", 3},
 { "below100", "gcc/Xstormy16-Variable-Attributes.html#index-below100-variable-attribute_002c-Xstormy16", "Xstormy16", 8},
 { "cleanup", "gcc/Common-Variable-Attributes.html#index-cleanup-variable-attribute", "", 7},
 { "common", "gcc/Common-Variable-Attributes.html#index-common-variable-attribute", "", 6},
 { "copy", "gcc/Common-Variable-Attributes.html#index-copy-variable-attribute", "", 4},
 { "counted_by", "gcc/Common-Variable-Attributes.html#index-counted_005fby-variable-attribute", "", 10},
 { "deprecated", "gcc/Common-Variable-Attributes.html#index-deprecated-variable-attribute", "", 10},
 { "dllexport", "gcc/Microsoft-Windows-Variable-Attributes.html#index-dllexport-variable-attribute", "", 9},
 { "dllimport", "gcc/Microsoft-Windows-Variable-Attributes.html#index-dllimport-variable-attribute", "", 9},
 { "eightbit_data", "gcc/H8_002f300-Variable-Attributes.html#index-eightbit_005fdata-variable-attribute_002c-H8_002f300", "H8/300", 13},
 { "either", "gcc/MSP430-Variable-Attributes.html#index-either-variable-attribute_002c-MSP430", "MSP430", 6},
 { "gcc_struct", "gcc/PowerPC-Variable-Attributes.html#index-gcc_005fstruct-variable-attribute_002c-PowerPC", "PowerPC", 10},
 { "gcc_struct", "gcc/x86-Variable-Attributes.html#index-gcc_005fstruct-variable-attribute_002c-x86", "x86", 10},
 { "init_priority", "gcc/C_002b_002b-Attributes.html#index-init_005fpriority-variable-attribute", "", 13},
 { "io", "gcc/AVR-Variable-Attributes.html#index-io-variable-attribute_002c-AVR", "AVR", 2},
 { "io_low", "gcc/AVR-Variable-Attributes.html#index-io_005flow-variable-attribute_002c-AVR", "AVR", 6},
 { "l1_data", "gcc/Blackfin-Variable-Attributes.html#index-l1_005fdata-variable-attribute_002c-Blackfin", "Blackfin", 7},
 { "l1_data_A", "gcc/Blackfin-Variable-Attributes.html#index-l1_005fdata_005fA-variable-attribute_002c-Blackfin", "Blackfin", 9},
 { "l1_data_B", "gcc/Blackfin-Variable-Attributes.html#index-l1_005fdata_005fB-variable-attribute_002c-Blackfin", "Blackfin", 9},
 { "l2", "gcc/Blackfin-Variable-Attributes.html#index-l2-variable-attribute_002c-Blackfin", "Blackfin", 2},
 { "lower", "gcc/MSP430-Variable-Attributes.html#index-lower-variable-attribute_002c-MSP430", "MSP430", 5},
 { "mode", "gcc/Common-Variable-Attributes.html#index-mode-variable-attribute", "", 4},
 { "model", "gcc/IA-64-Variable-Attributes.html#index-model-variable-attribute_002c-IA-64", "IA-64", 5},
 { "model", "gcc/LoongArch-Variable-Attributes.html#index-model-variable-attribute_002c-LoongArch", "LoongArch", 5},
 { "model-name", "gcc/M32R_002fD-Variable-Attributes.html#index-model-name-variable-attribute_002c-M32R_002fD", "M32R/D", 10},
 { "ms_struct", "gcc/PowerPC-Variable-Attributes.html#index-ms_005fstruct-variable-attribute_002c-PowerPC", "PowerPC", 9},
 { "ms_struct", "gcc/x86-Variable-Attributes.html#index-ms_005fstruct-variable-attribute_002c-x86", "x86", 9},
 { "no_icf", "gcc/Common-Variable-Attributes.html#index-no_005ficf-variable-attribute", "", 6},
 { "nocommon", "gcc/Common-Variable-Attributes.html#index-nocommon-variable-attribute", "", 8},
 { "noinit", "gcc/Common-Variable-Attributes.html#index-noinit-variable-attribute", "", 6},
 { "nonstring", "gcc/Common-Variable-Attributes.html#index-nonstring-variable-attribute", "", 9},
 { "objc_nullability", "gcc/Common-Variable-Attributes.html#index-objc_005fnullability-variable-attribute", "", 16},
 { "packed", "gcc/Common-Variable-Attributes.html#index-packed-variable-attribute", "", 6},
 { "persistent", "gcc/Common-Variable-Attributes.html#index-persistent-variable-attribute", "", 10},
 { "progmem", "gcc/AVR-Variable-Attributes.html#index-progmem-variable-attribute_002c-AVR", "AVR", 7},
 { "retain", "gcc/Common-Variable-Attributes.html#index-retain-variable-attribute", "", 6},
 { "saddr", "gcc/RL78-Variable-Attributes.html#index-saddr-variable-attribute_002c-RL78", "RL78", 5},
 { "sda", "gcc/V850-Variable-Attributes.html#index-sda-variable-attribute_002c-V850", "V850", 3},
 { "section", "gcc/Common-Variable-Attributes.html#index-section-variable-attribute", "", 7},
 { "selectany", "gcc/Microsoft-Windows-Variable-Attributes.html#index-selectany-variable-attribute", "", 9},
 { "shared", "gcc/Microsoft-Windows-Variable-Attributes.html#index-shared-variable-attribute", "", 6},
 { "shared", "gcc/Nvidia-PTX-Variable-Attributes.html#index-shared-variable-attribute_002c-Nvidia-PTX", "Nvidia PTX", 6},
 { "strict_flex_array", "gcc/Common-Variable-Attributes.html#index-strict_005fflex_005farray-variable-attribute", "", 17},
 { "tda", "gcc/V850-Variable-Attributes.html#index-tda-variable-attribute_002c-V850", "V850", 3},
 { "tiny_data", "gcc/H8_002f300-Variable-Attributes.html#index-tiny_005fdata-variable-attribute_002c-H8_002f300", "H8/300", 9},
 { "tls_model", "gcc/Common-Variable-Attributes.html#index-tls_005fmodel-variable-attribute", "", 9},
 { "unavailable", "gcc/Common-Variable-Attributes.html#index-unavailable-variable-attribute", "", 11},
 { "uninitialized", "gcc/Common-Variable-Attributes.html#index-uninitialized-variable-attribute", "", 13},
 { "unused", "gcc/Common-Variable-Attributes.html#index-unused-variable-attribute", "", 6},
 { "upper", "gcc/MSP430-Variable-Attributes.html#index-upper-variable-attribute_002c-MSP430", "MSP430", 5},
 { "used", "gcc/Common-Variable-Attributes.html#index-used-variable-attribute", "", 4},
 { "vector_size", "gcc/Common-Variable-Attributes.html#index-vector_005fsize-variable-attribute", "", 11},
 { "visibility", "gcc/Common-Variable-Attributes.html#index-visibility-variable-attribute", "", 10},
 { "warn_if_not_aligned", "gcc/Common-Variable-Attributes.html#index-warn_005fif_005fnot_005faligned-variable-attribute", "", 19},
 { "weak", "gcc/Common-Variable-Attributes.html#index-weak-variable-attribute", "", 4},
 { "zda", "gcc/V850-Variable-Attributes.html#index-zda-variable-attribute_002c-V850", "V850", 3},
};

static const struct attr_url_table {
  const attr_url_entry *m_table;
  const size_t m_table_sz;
} attr_url_tables[] = {
  { enumerator_attrs, ARRAY_SIZE (enumerator_attrs) },
  { function_attrs, ARRAY_SIZE (function_attrs) },
  { label_attrs, ARRAY_SIZE (label_attrs) },
  { statement_attrs, ARRAY_SIZE (statement_attrs) },
  { type_attrs, ARRAY_SIZE (type_attrs) },
  { variable_attrs, ARRAY_SIZE (variable_attrs) },
};
