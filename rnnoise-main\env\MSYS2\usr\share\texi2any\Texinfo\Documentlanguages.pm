# This file was automatically generated from maintain/regenerate_documentlanguages-loc.pl

package Texinfo::Documentlanguages;

our %language_codes = (
'aa' => 1,
'ab' => 1,
'ae' => 1,
'af' => 1,
'ak' => 1,
'am' => 1,
'an' => 1,
'ar' => 1,
'as' => 1,
'av' => 1,
'ay' => 1,
'az' => 1,
'ba' => 1,
'be' => 1,
'bg' => 1,
'bh' => 1,
'bi' => 1,
'bm' => 1,
'bn' => 1,
'bo' => 1,
'br' => 1,
'bs' => 1,
'ca' => 1,
'ce' => 1,
'ch' => 1,
'co' => 1,
'cr' => 1,
'cs' => 1,
'cu' => 1,
'cv' => 1,
'cy' => 1,
'da' => 1,
'de' => 1,
'dv' => 1,
'dz' => 1,
'ee' => 1,
'el' => 1,
'en' => 1,
'eo' => 1,
'es' => 1,
'et' => 1,
'eu' => 1,
'fa' => 1,
'ff' => 1,
'fi' => 1,
'fj' => 1,
'fo' => 1,
'fr' => 1,
'fy' => 1,
'ga' => 1,
'gd' => 1,
'gl' => 1,
'gn' => 1,
'gu' => 1,
'gv' => 1,
'ha' => 1,
'he' => 1,
'hi' => 1,
'ho' => 1,
'hr' => 1,
'ht' => 1,
'hu' => 1,
'hy' => 1,
'hz' => 1,
'ia' => 1,
'id' => 1,
'ie' => 1,
'ig' => 1,
'ii' => 1,
'ik' => 1,
'io' => 1,
'is' => 1,
'it' => 1,
'iu' => 1,
'ja' => 1,
'jv' => 1,
'ka' => 1,
'kg' => 1,
'ki' => 1,
'kj' => 1,
'kk' => 1,
'kl' => 1,
'km' => 1,
'kn' => 1,
'ko' => 1,
'kr' => 1,
'ks' => 1,
'ku' => 1,
'kv' => 1,
'kw' => 1,
'ky' => 1,
'la' => 1,
'lb' => 1,
'lg' => 1,
'li' => 1,
'ln' => 1,
'lo' => 1,
'lt' => 1,
'lu' => 1,
'lv' => 1,
'mg' => 1,
'mh' => 1,
'mi' => 1,
'mk' => 1,
'ml' => 1,
'mn' => 1,
'mr' => 1,
'ms' => 1,
'mt' => 1,
'my' => 1,
'na' => 1,
'nb' => 1,
'nd' => 1,
'ne' => 1,
'ng' => 1,
'nl' => 1,
'nn' => 1,
'no' => 1,
'nr' => 1,
'nv' => 1,
'ny' => 1,
'oc' => 1,
'oj' => 1,
'om' => 1,
'or' => 1,
'os' => 1,
'pa' => 1,
'pi' => 1,
'pl' => 1,
'ps' => 1,
'pt' => 1,
'qu' => 1,
'rm' => 1,
'rn' => 1,
'ro' => 1,
'ru' => 1,
'rw' => 1,
'sa' => 1,
'sc' => 1,
'sd' => 1,
'se' => 1,
'sg' => 1,
'si' => 1,
'sk' => 1,
'sl' => 1,
'sm' => 1,
'sn' => 1,
'so' => 1,
'sq' => 1,
'sr' => 1,
'ss' => 1,
'st' => 1,
'su' => 1,
'sv' => 1,
'sw' => 1,
'ta' => 1,
'te' => 1,
'tg' => 1,
'th' => 1,
'ti' => 1,
'tk' => 1,
'tl' => 1,
'tn' => 1,
'to' => 1,
'tr' => 1,
'ts' => 1,
'tt' => 1,
'tw' => 1,
'ty' => 1,
'ug' => 1,
'uk' => 1,
'ur' => 1,
'uz' => 1,
've' => 1,
'vi' => 1,
'vo' => 1,
'wa' => 1,
'wo' => 1,
'xh' => 1,
'yi' => 1,
'yo' => 1,
'za' => 1,
'zh' => 1,
'zu' => 1,
);

our %region_codes = (
'AD' => 1,
'AE' => 1,
'AF' => 1,
'AG' => 1,
'AI' => 1,
'AL' => 1,
'AM' => 1,
'AO' => 1,
'AQ' => 1,
'AR' => 1,
'AS' => 1,
'AT' => 1,
'AU' => 1,
'AW' => 1,
'AX' => 1,
'AZ' => 1,
'BA' => 1,
'BB' => 1,
'BD' => 1,
'BE' => 1,
'BF' => 1,
'BG' => 1,
'BH' => 1,
'BI' => 1,
'BJ' => 1,
'BL' => 1,
'BM' => 1,
'BN' => 1,
'BO' => 1,
'BQ' => 1,
'BR' => 1,
'BS' => 1,
'BT' => 1,
'BV' => 1,
'BW' => 1,
'BY' => 1,
'BZ' => 1,
'CA' => 1,
'CC' => 1,
'CD' => 1,
'CF' => 1,
'CG' => 1,
'CH' => 1,
'CI' => 1,
'CK' => 1,
'CL' => 1,
'CM' => 1,
'CN' => 1,
'CO' => 1,
'CR' => 1,
'CU' => 1,
'CV' => 1,
'CW' => 1,
'CX' => 1,
'CY' => 1,
'CZ' => 1,
'DE' => 1,
'DJ' => 1,
'DK' => 1,
'DM' => 1,
'DO' => 1,
'DZ' => 1,
'EC' => 1,
'EE' => 1,
'EG' => 1,
'EH' => 1,
'ER' => 1,
'ES' => 1,
'ET' => 1,
'FI' => 1,
'FJ' => 1,
'FK' => 1,
'FM' => 1,
'FO' => 1,
'FR' => 1,
'GA' => 1,
'GB' => 1,
'GD' => 1,
'GE' => 1,
'GF' => 1,
'GG' => 1,
'GH' => 1,
'GI' => 1,
'GL' => 1,
'GM' => 1,
'GN' => 1,
'GP' => 1,
'GQ' => 1,
'GR' => 1,
'GS' => 1,
'GT' => 1,
'GU' => 1,
'GW' => 1,
'GY' => 1,
'HK' => 1,
'HM' => 1,
'HN' => 1,
'HR' => 1,
'HT' => 1,
'HU' => 1,
'ID' => 1,
'IE' => 1,
'IL' => 1,
'IM' => 1,
'IN' => 1,
'IO' => 1,
'IQ' => 1,
'IR' => 1,
'IS' => 1,
'IT' => 1,
'JE' => 1,
'JM' => 1,
'JO' => 1,
'JP' => 1,
'KE' => 1,
'KG' => 1,
'KH' => 1,
'KI' => 1,
'KM' => 1,
'KN' => 1,
'KP' => 1,
'KR' => 1,
'KW' => 1,
'KY' => 1,
'KZ' => 1,
'LA' => 1,
'LB' => 1,
'LC' => 1,
'LI' => 1,
'LK' => 1,
'LR' => 1,
'LS' => 1,
'LT' => 1,
'LU' => 1,
'LV' => 1,
'LY' => 1,
'MA' => 1,
'MC' => 1,
'MD' => 1,
'ME' => 1,
'MF' => 1,
'MG' => 1,
'MH' => 1,
'MK' => 1,
'ML' => 1,
'MM' => 1,
'MN' => 1,
'MO' => 1,
'MP' => 1,
'MQ' => 1,
'MR' => 1,
'MS' => 1,
'MT' => 1,
'MU' => 1,
'MV' => 1,
'MW' => 1,
'MX' => 1,
'MY' => 1,
'MZ' => 1,
'NA' => 1,
'NC' => 1,
'NE' => 1,
'NF' => 1,
'NG' => 1,
'NI' => 1,
'NL' => 1,
'NO' => 1,
'NP' => 1,
'NR' => 1,
'NU' => 1,
'NZ' => 1,
'OM' => 1,
'PA' => 1,
'PE' => 1,
'PF' => 1,
'PG' => 1,
'PH' => 1,
'PK' => 1,
'PL' => 1,
'PM' => 1,
'PN' => 1,
'PR' => 1,
'PS' => 1,
'PT' => 1,
'PW' => 1,
'PY' => 1,
'QA' => 1,
'RE' => 1,
'RO' => 1,
'RS' => 1,
'RU' => 1,
'RW' => 1,
'SA' => 1,
'SB' => 1,
'SC' => 1,
'SD' => 1,
'SE' => 1,
'SG' => 1,
'SH' => 1,
'SI' => 1,
'SJ' => 1,
'SK' => 1,
'SL' => 1,
'SM' => 1,
'SN' => 1,
'SO' => 1,
'SR' => 1,
'SS' => 1,
'ST' => 1,
'SV' => 1,
'SX' => 1,
'SY' => 1,
'SZ' => 1,
'TC' => 1,
'TD' => 1,
'TF' => 1,
'TG' => 1,
'TH' => 1,
'TJ' => 1,
'TK' => 1,
'TL' => 1,
'TM' => 1,
'TN' => 1,
'TO' => 1,
'TR' => 1,
'TT' => 1,
'TV' => 1,
'TW' => 1,
'TZ' => 1,
'UA' => 1,
'UG' => 1,
'UM' => 1,
'US' => 1,
'UY' => 1,
'UZ' => 1,
'VA' => 1,
'VC' => 1,
'VE' => 1,
'VG' => 1,
'VI' => 1,
'VN' => 1,
'VU' => 1,
'WF' => 1,
'WS' => 1,
'YE' => 1,
'YT' => 1,
'ZA' => 1,
'ZM' => 1,
'ZW' => 1,
);

1;
