# help.tr.txt - tr GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
Bir değeri buraya işaretlemek size kalmış; bu değer herhangi bir 3. şahsa
gönderilmeyecek. Bir güvence ağı sağlamak için bizim buna ihtiyacımız var;
bunun (a<PERSON><PERSON><PERSON><PERSON> belirtilmeden oluşturulmuş) sertifikalar ağıyla
hiçbir alakası yok.
.

.gpg.edit_ownertrust.set_ultimate.okay
Web-of-Trust oluşturulabilmesi için GnuPG'ye hangi anahtarların son derece
güvenli (bunlar gizli anahtarlarına erişiminiz olan anahtarlardır) olduğunun
bildirilmesi gerekir. "evet" yanıtı bu anahtarın son derece güvenli
olduğunun belirtilmesi için yeterlidir.

.

.gpg.untrusted_key.override
Bu güvencesiz anahtarı yine de kullanmak istiyorsanız cevap olarak
 "evet" yazın.
.

.gpg.pklist.user_id.enter
Bu iletiyi göndereceğiniz adresin kullanıcı kimliğini giriniz.
.

.gpg.keygen.algo
Kullanılacak algoritmayı seçiniz.

DSA (nam-ı diğer DSS) Sayısal İmza Algortimasıdır ve
sadece imzalar için kullanılabilir.

Elgamal sadece şifreleme amacıyla kullanılabilen bir algoritmadır.

RSA hem imzalamak hem de şifrelemek amacıyla kullanılabilir.

İlk (asıl) anahtar daima imzalama yeteneğine sahip bir anahtar olmalıdır.
.

.gpg.keygen.algo.rsa_se
Genelde imzalama ve şifreleme için aynı anahtarı kullanmak iyi bir fikir
değildir. Bu algoritma sadece belli alanlarda kullanılabilir.
Lütfen güvenlik uzmanınıza danışın.
.

.gpg.keygen.size
Anahtar uzunluğunu giriniz
.

.gpg.keygen.size.huge.okay
Cevap "evet" ya da "hayır"
.

.gpg.keygen.size.large.okay
Cevap "evet" ya da "hayır"
.

.gpg.keygen.valid
İstenen değeri girin. ISO tarihi (YYYY-AA-GG) girmeniz mümkündür fakat
iyi bir hata cevabı alamazsınız -- onun yerine sistem verilen değeri
bir zaman aralığı olarak çözümlemeyi dener.
.

.gpg.keygen.valid.okay
Cevap "evet" ya da "hayır"
.

.gpg.keygen.name
Anahtar tutucunun ismini giriniz
.

.gpg.keygen.email
lütfen bir E-posta adresi girin (isteğe bağlı ancak kuvvetle tavsiye edilir)
.

.gpg.keygen.comment
Lütfen önbilgi girin (isteğe bağlı)
.

.gpg.keygen.userid.cmd
S iSim değiştirmek için.
B önBilgiyi değiştirmek için.
P e-Posta adresini değiştirmek için.
D anahtar üretimine Devam etmek için.
K anahtar üretiminden çıKmak için.
.

.gpg.keygen.sub.okay
Yardımcı anahtarı üretmek istiyorsanız "evet" ya da "e" girin.
.

.gpg.sign_uid.okay
Cevap "evet" ya da "hayır"
.

.gpg.sign_uid.class
Bir anahtarı bir kullanıcı kimlikle imzalamadan önce kullanıcı kimliğin
içindeki ismin, anahtarın sahibine ait olup olmadığını kontrol etmelisiniz.

"0" bu kontrolu yapmadığınız ve yapmayı da bilmediğiniz anlamındadır.
"1" anahtar size sahibi tarafından gönderildi ama siz bu anahtarı başka
      kaynaklardan doğrulamadınız anlamındadır. Bu kişisel doğrulama için
      yeterlidir. En azında yarı anonim bir anahtar imzalaması yapmış
      olursunuz.
"2" ayrıntılı bir inceleme yapıldığı anlamındadır. Örneğin parmakizi ve
      bir anahtarın foto kimliğiyle kullanıcı kimliğini karşılaştırmak
      gibi denetimleri yapmışsınızdır.
"3" inceden inceye bir doğrulama anlatır. Örneğin, şahıstaki anahtarın
      sahibi ile anahtar parmak izini karşılaştırmışsınızdır ve anahtardaki
      kullanıcı kimlikte belirtilen isme ait bir basılı kimlik belgesindeki
      bir fotoğrafla şahsı karşılaştırmışsınızdır ve son olarak anahtar
      sahibinin e-posta adresini kendisinin kullanmakta olduğunu da
      denetlemişsinizdir.
Burada 2 ve 3 için verilen örnekler *sadece* örnektir.
Eninde sonunda bir anahtarı imzalarken "ayrıntılı" ve "inceden inceye" kontroller arasındaki ayrıma siz karar vereceksiniz.
Bu kararı verebilecek durumda değilseniz "0" cevabını verin.
.

.gpg.change_passwd.empty.okay
Cevap "evet" ya da "hayır"
.

.gpg.keyedit.save.okay
Cevap "evet" ya da "hayır"
.

.gpg.keyedit.cancel.okay
Cevap "evet" ya da "hayır"
.

.gpg.keyedit.sign_all.okay
Kullanıcı kimliklerinin TÜMünü imzalamak istiyorsanız "evet" ya da "yes" yazın
.

.gpg.keyedit.remove.uid.okay
Bu kullanıcı kimliğini gerçekten silmek istiyorsanız "evet" girin.
Böylece bütün sertifikaları kaybedeceksiniz!
.

.gpg.keyedit.remove.subkey.okay
Bu yardımcı anahtarı silme izni vermek istiyorsanız "evet" girin
.

.gpg.keyedit.delsig.valid
Bu, anahtar üzerinde geçerli bir imzadır; anahtara ya da bu anahtarla
sertifikalanmış bir diğer anahtara bir güvence bağlantısı sağlamakta
önemli olabileceğinden normalde bu imzayı silmek istemezsiniz.
.

.gpg.keyedit.delsig.unknown
Bu imza, anahtarına sahip olmadığınızdan, kontrol edilemez. Bu imzanın
silinmesini hangi anahtarın kullanıldığını bilene kadar
ertelemelisiniz çünkü bu imzalama anahtarı başka bir sertifikalı
anahtar vasıtası ile bir güvence bağlantısı sağlayabilir.
.

.gpg.keyedit.delsig.invalid
İmza geçersiz. Anahtarlıktan kaldırmak uygun olacak.
.

.gpg.keyedit.delsig.selfsig
Bu imza kullanıcı kimliğini anahtara bağlar. Öz-imzayı silmek hiç iyi
bir fikir değil. GnuPG bu anahtarı bir daha hiç kullanamayabilir.
Bunu sadece, eğer bu öz-imza bazı durumlarda geçerli değilse ya da
kullanılabilir bir ikincisi var ise yapın.
.

.gpg.keyedit.updpref.okay
Tüm kullanıcı kimlik tercihlerini (ya da seçilen birini) mevcut tercihler
listesine çevirir. Tüm etkilenen öz-imzaların zaman damgaları bir sonraki
tarafından öne alınacaktır.

.

.gpg.passphrase.enter
Lütfen bir anahtar parolası giriniz; yazdıklarınız görünmeyecek

.

.gpg.passphrase.repeat
Lütfen son parolayı tekrarlayarak ne yazdığınızdan emin olun.
.

.gpg.detached_signature.filename
İmzanın uygulanacağı dosyanın ismini verin
.

.gpg.openfile.overwrite.okay
Dosyanın üzerine yazılacaksa lütfen "evet" yazın
.

.gpg.openfile.askoutname
Lütfen yeni dosya ismini girin. Dosya ismini yazmadan RETURN tuşlarsanız
parantez içinde gösterilen öntanımlı dosya kullanılacak.
.

.gpg.ask_revocation_reason.code
Sertifikalama için bir sebep belirtmelisiniz. İçeriğine bağlı olarak
bu listeden seçebilirsiniz:
  "Anahtar tehlikede"
	Yetkisiz kişilerin gizli anahtarınıza erişebildiğine inanıyorsanız
	bunu seçin.
  "Anahtar geçici"
	Mevcut anahtarı daha yeni bir anahtar ile değiştirmişseniz bunu seçin.
  "Anahtar artık kullanılmayacak"
	Anahtarı emekliye ayıracaksanız bunu seçin.
  "Kullanıcı kimliği artık geçersiz"
	Kullanıcı kimliği artık kullanılamayacak durumdaysa bunu
	seçin; genelde Eposta adresi geçersiz olduğunda kullanılır.

.

.gpg.ask_revocation_reason.text
İsterseniz, neden bu yürürlükten kaldırma sertifikasını
verdiğinizi açıklayan bir metin girebilirsiniz.
Lütfen bu metin kısa olsun. Bir boş satır metni bitirir.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
