/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

cpp_quote("#include <winapifamily.h>")

import "mfidl.idl";
import "d3d12.idl";

cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")

[object, uuid(09d0f835-92ff-4e53-8efa-40faa551f233), local]
interface IMFD3D12SynchronizationObjectCommands : IUnknown
{
  HRESULT EnqueueResourceReady([in] ID3D12CommandQueue *pProducerCommandQueue);
  HRESULT EnqueueResourceReadyWait([in] ID3D12CommandQueue *pConsumerCommandQueue);
  HRESULT SignalEventOnResourceReady([in] HANDLE hEvent);
  HRESULT EnqueueResourceRelease([in] ID3D12CommandQueue *pConsumerCommandQueue);
}

[object, uuid(802302b0-82de-45e1-b421-f19ee5bdaf23), local]
interface IMFD3D12SynchronizationObject : IUnknown
{
  HRESULT SignalEventOnFinalResourceRelease([in] HANDLE hEvent);
  HRESULT Reset();
}

cpp_quote("#if NTDDI_VERSION >= NTDDI_WIN10_CO")
cpp_quote("HRESULT MFCreateD3D12SynchronizationObject(ID3D12Device *pDevice, REFIID riid, void **ppvSyncObject);")
cpp_quote("#endif /* NTDDI_WIN10_CO */")

typedef enum MF_MT_D3D_RESOURCE_VERSION_ENUM {
  MF_D3D11_RESOURCE,
  MF_D3D12_RESOURCE
} MF_MT_D3D_RESOURCE_VERSION_ENUM;

cpp_quote("EXTERN_GUID(MF_D3D12_SYNCHRONIZATION_OBJECT, 0x2a7c8d6a, 0x85a6, 0x494d, 0xa0, 0x46, 0x6, 0xea, 0x1a, 0x13, 0x8f, 0x4b);")
cpp_quote("EXTERN_GUID(MF_MT_D3D_RESOURCE_VERSION, 0x174f1e85, 0xfe26, 0x453d, 0xb5, 0x2e, 0x5b, 0xdd, 0x4e, 0x55, 0xb9, 0x44);")
cpp_quote("EXTERN_GUID(MF_MT_D3D12_CPU_READBACK, 0x28ee9fe3, 0xd481, 0x46a6, 0xb9, 0x8a, 0x7f, 0x69, 0xd5, 0x28, 0xe, 0x82);")
cpp_quote("EXTERN_GUID(MF_MT_D3D12_TEXTURE_LAYOUT, 0x97c85caa, 0xbeb, 0x4ee1, 0x97, 0x15, 0xf2, 0x2f, 0xad, 0x8c, 0x10, 0xf5);")
cpp_quote("EXTERN_GUID(MF_MT_D3D12_RESOURCE_FLAG_ALLOW_RENDER_TARGET, 0xeeac2585, 0x3430, 0x498c, 0x84, 0xa2, 0x77, 0xb1, 0xbb, 0xa5, 0x70, 0xf6);")
cpp_quote("EXTERN_GUID(MF_MT_D3D12_RESOURCE_FLAG_ALLOW_DEPTH_STENCIL, 0xb1138dc3, 0x1d5, 0x4c14, 0x9b, 0xdc, 0xcd, 0xc9, 0x33, 0x6f, 0x55, 0xb9);")
cpp_quote("EXTERN_GUID(MF_MT_D3D12_RESOURCE_FLAG_ALLOW_UNORDERED_ACCESS, 0x82c85647, 0x5057, 0x4960, 0x95, 0x59, 0xf4, 0x5b, 0x8e, 0x27, 0x14, 0x27);")
cpp_quote("EXTERN_GUID(MF_MT_D3D12_RESOURCE_FLAG_DENY_SHADER_RESOURCE, 0xba06bfac, 0xffe3, 0x474a, 0xab, 0x55, 0x16, 0x1e, 0xe4, 0x41, 0x7a, 0x2e);")
cpp_quote("EXTERN_GUID(MF_MT_D3D12_RESOURCE_FLAG_ALLOW_CROSS_ADAPTER, 0xa6a1e439, 0x2f96, 0x4ab5, 0x98, 0xdc, 0xad, 0xf7, 0x49, 0x73, 0x50, 0x5d);")
cpp_quote("EXTERN_GUID(MF_MT_D3D12_RESOURCE_FLAG_ALLOW_SIMULTANEOUS_ACCESS, 0xa4940b2, 0xcfd6, 0x4738, 0x9d, 0x2, 0x98, 0x11, 0x37, 0x34, 0x1, 0x5a);")
cpp_quote("EXTERN_GUID(MF_SA_D3D12_HEAP_FLAGS, 0x496b3266, 0xd28f, 0x4f8c, 0x93, 0xa7, 0x4a, 0x59, 0x6b, 0x1a, 0x31, 0xa1);")
cpp_quote("EXTERN_GUID(MF_SA_D3D12_HEAP_TYPE, 0x56f26a76, 0xbbc1, 0x4ce0, 0xbb, 0x11, 0xe2, 0x23, 0x68, 0xd8, 0x74, 0xed);")
cpp_quote("EXTERN_GUID(MF_SA_D3D12_CLEAR_VALUE, 0x86ba9a39, 0x526, 0x495d, 0x9a, 0xb5, 0x54, 0xec, 0x9f, 0xad, 0x6f, 0xc3);")

cpp_quote("#endif /* WINAPI_PARTITION_APP */")
