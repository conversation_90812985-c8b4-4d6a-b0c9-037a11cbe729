+{
   locale_version => 1.31,
   entry => <<'ENTRY', # for DUCET v13.0.0
0109      ; [.1FD7.0020.0002] # LATIN SMALL LETTER C WITH CIRCUMFLEX
0063 0302 ; [.1FD7.0020.0002] # LATIN SMALL LETTER C WITH CIRCUMFLEX
0108      ; [.1FD7.0020.0008] # LATIN CAPITAL LETTER C WITH CIRCUMFLEX
0043 0302 ; [.1FD7.0020.0008] # LATIN CAPITAL LETTER C WITH CIRCUMFLEX
011D      ; [.2052.0020.0002] # LATIN SMALL LETTER G WITH CIRCUMFLEX
0067 0302 ; [.2052.0020.0002] # LATIN SMALL LETTER G WITH CIRCUMFLEX
011C      ; [.2052.0020.0008] # LATIN CAPITAL LETTER G WITH CIRCUMFLEX
0047 0302 ; [.2052.0020.0008] # LATIN CAPITAL LETTER G WITH CIRCUMFLEX
0125      ; [.2076.0020.0002] # LATIN SMALL LETTER H WITH CIRCUMFLEX
0068 0302 ; [.2076.0020.0002] # LATIN SMALL LETTER H WITH CIRCUMFLEX
0124      ; [.2076.0020.0008] # LATIN CAPITAL LETTER H WITH CIRCUMFLEX
0048 0302 ; [.2076.0020.0008] # LATIN CAPITAL LETTER H WITH CIRCUMFLEX
0135      ; [.20AC.0020.0002] # LATIN SMALL LETTER J WITH CIRCUMFLEX
006A 0302 ; [.20AC.0020.0002] # LATIN SMALL LETTER J WITH CIRCUMFLEX
0134      ; [.20AC.0020.0008] # LATIN CAPITAL LETTER J WITH CIRCUMFLEX
004A 0302 ; [.20AC.0020.0008] # LATIN CAPITAL LETTER J WITH CIRCUMFLEX
015D      ; [.21D3.0020.0002] # LATIN SMALL LETTER S WITH CIRCUMFLEX
0073 0302 ; [.21D3.0020.0002] # LATIN SMALL LETTER S WITH CIRCUMFLEX
015C      ; [.21D3.0020.0008] # LATIN CAPITAL LETTER S WITH CIRCUMFLEX
0053 0302 ; [.21D3.0020.0008] # LATIN CAPITAL LETTER S WITH CIRCUMFLEX
016D      ; [.2218.0020.0002] # LATIN SMALL LETTER U WITH BREVE
0075 0306 ; [.2218.0020.0002] # LATIN SMALL LETTER U WITH BREVE
016C      ; [.2218.0020.0008] # LATIN CAPITAL LETTER U WITH BREVE
0055 0306 ; [.2218.0020.0008] # LATIN CAPITAL LETTER U WITH BREVE
ENTRY
};
