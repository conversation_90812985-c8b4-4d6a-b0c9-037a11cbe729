.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_get_signature_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_get_signature_info \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_get_signature_info(gnutls_pkcs7_t " pkcs7 ", unsigned " idx ", gnutls_pkcs7_signature_info_st * " info ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_t pkcs7" 12
should contain a \fBgnutls_pkcs7_t\fP type
.IP "unsigned idx" 12
the index of the signature info to check
.IP "gnutls_pkcs7_signature_info_st * info" 12
will contain the output signature
.SH "DESCRIPTION"
This function will return information about the signature identified
by idx in the provided PKCS \fB7\fP structure. The information should be
deinitialized using \fBgnutls_pkcs7_signature_info_deinit()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
