<?xml version="1.0"?>
<!DOCTYPE syscalls_info SYSTEM "gdb-syscalls.dtd">
<!-- Copyright (C) 2011-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->
<!-- This file was generated using the following file:

     arch/mips/kernel/syscalls/syscall_n32.tbl

     The file mentioned above belongs to the Linux Kernel.  -->
<syscalls_info>
  <syscall name="read" number="6000" groups="descriptor"/>
  <syscall name="write" number="6001" groups="descriptor"/>
  <syscall name="open" number="6002" groups="descriptor,file"/>
  <syscall name="close" number="6003" groups="descriptor"/>
  <syscall name="stat" number="6004" groups="file"/>
  <syscall name="fstat" number="6005" groups="descriptor"/>
  <syscall name="lstat" number="6006" groups="file"/>
  <syscall name="poll" number="6007" groups="descriptor"/>
  <syscall name="lseek" number="6008" groups="descriptor"/>
  <syscall name="mmap" number="6009" groups="descriptor,memory"/>
  <syscall name="mprotect" number="6010" groups="memory"/>
  <syscall name="munmap" number="6011" groups="memory"/>
  <syscall name="brk" number="6012" groups="memory"/>
  <syscall name="rt_sigaction" number="6013" groups="signal"/>
  <syscall name="rt_sigprocmask" number="6014" groups="signal"/>
  <syscall name="ioctl" number="6015" groups="descriptor"/>
  <syscall name="pread64" number="6016" groups="descriptor"/>
  <syscall name="pwrite64" number="6017" groups="descriptor"/>
  <syscall name="readv" number="6018" groups="descriptor"/>
  <syscall name="writev" number="6019" groups="descriptor"/>
  <syscall name="access" number="6020" groups="file"/>
  <syscall name="pipe" number="6021" groups="descriptor"/>
  <syscall name="_newselect" number="6022" groups="descriptor"/>
  <syscall name="sched_yield" number="6023"/>
  <syscall name="mremap" number="6024" groups="memory"/>
  <syscall name="msync" number="6025" groups="memory"/>
  <syscall name="mincore" number="6026" groups="memory"/>
  <syscall name="madvise" number="6027" groups="memory"/>
  <syscall name="shmget" number="6028" groups="ipc"/>
  <syscall name="shmat" number="6029" groups="ipc,memory"/>
  <syscall name="shmctl" number="6030" groups="ipc"/>
  <syscall name="dup" number="6031" groups="descriptor"/>
  <syscall name="dup2" number="6032" groups="descriptor"/>
  <syscall name="pause" number="6033" groups="signal"/>
  <syscall name="nanosleep" number="6034"/>
  <syscall name="getitimer" number="6035"/>
  <syscall name="setitimer" number="6036"/>
  <syscall name="alarm" number="6037"/>
  <syscall name="getpid" number="6038"/>
  <syscall name="sendfile" number="6039" groups="descriptor,network"/>
  <syscall name="socket" number="6040" groups="network"/>
  <syscall name="connect" number="6041" groups="network"/>
  <syscall name="accept" number="6042" groups="network"/>
  <syscall name="sendto" number="6043" groups="network"/>
  <syscall name="recvfrom" number="6044" groups="network"/>
  <syscall name="sendmsg" number="6045" groups="network"/>
  <syscall name="recvmsg" number="6046" groups="network"/>
  <syscall name="shutdown" number="6047" groups="network"/>
  <syscall name="bind" number="6048" groups="network"/>
  <syscall name="listen" number="6049" groups="network"/>
  <syscall name="getsockname" number="6050" groups="network"/>
  <syscall name="getpeername" number="6051" groups="network"/>
  <syscall name="socketpair" number="6052" groups="network"/>
  <syscall name="setsockopt" number="6053" groups="network"/>
  <syscall name="getsockopt" number="6054" groups="network"/>
  <syscall name="clone" number="6055" groups="process"/>
  <syscall name="fork" number="6056" groups="process"/>
  <syscall name="execve" number="6057" groups="file,process"/>
  <syscall name="exit" number="6058" groups="process"/>
  <syscall name="wait4" number="6059" groups="process"/>
  <syscall name="kill" number="6060" groups="signal,process"/>
  <syscall name="uname" number="6061"/>
  <syscall name="semget" number="6062" groups="ipc"/>
  <syscall name="semop" number="6063" groups="ipc"/>
  <syscall name="semctl" number="6064" groups="ipc"/>
  <syscall name="shmdt" number="6065" groups="ipc,memory"/>
  <syscall name="msgget" number="6066" groups="ipc"/>
  <syscall name="msgsnd" number="6067" groups="ipc"/>
  <syscall name="msgrcv" number="6068" groups="ipc"/>
  <syscall name="msgctl" number="6069" groups="ipc"/>
  <syscall name="fcntl" number="6070" groups="descriptor"/>
  <syscall name="flock" number="6071" groups="descriptor"/>
  <syscall name="fsync" number="6072" groups="descriptor"/>
  <syscall name="fdatasync" number="6073" groups="descriptor"/>
  <syscall name="truncate" number="6074" groups="file"/>
  <syscall name="ftruncate" number="6075" groups="descriptor"/>
  <syscall name="getdents" number="6076" groups="descriptor"/>
  <syscall name="getcwd" number="6077" groups="file"/>
  <syscall name="chdir" number="6078" groups="file"/>
  <syscall name="fchdir" number="6079" groups="descriptor"/>
  <syscall name="rename" number="6080" groups="file"/>
  <syscall name="mkdir" number="6081" groups="file"/>
  <syscall name="rmdir" number="6082" groups="file"/>
  <syscall name="creat" number="6083" groups="descriptor,file"/>
  <syscall name="link" number="6084" groups="file"/>
  <syscall name="unlink" number="6085" groups="file"/>
  <syscall name="symlink" number="6086" groups="file"/>
  <syscall name="readlink" number="6087" groups="file"/>
  <syscall name="chmod" number="6088" groups="file"/>
  <syscall name="fchmod" number="6089" groups="descriptor"/>
  <syscall name="chown" number="6090" groups="file"/>
  <syscall name="fchown" number="6091" groups="descriptor"/>
  <syscall name="lchown" number="6092" groups="file"/>
  <syscall name="umask" number="6093"/>
  <syscall name="gettimeofday" number="6094"/>
  <syscall name="getrlimit" number="6095"/>
  <syscall name="getrusage" number="6096"/>
  <syscall name="sysinfo" number="6097"/>
  <syscall name="times" number="6098"/>
  <syscall name="ptrace" number="6099"/>
  <syscall name="getuid" number="6100"/>
  <syscall name="syslog" number="6101"/>
  <syscall name="getgid" number="6102"/>
  <syscall name="setuid" number="6103"/>
  <syscall name="setgid" number="6104"/>
  <syscall name="geteuid" number="6105"/>
  <syscall name="getegid" number="6106"/>
  <syscall name="setpgid" number="6107"/>
  <syscall name="getppid" number="6108"/>
  <syscall name="getpgrp" number="6109"/>
  <syscall name="setsid" number="6110"/>
  <syscall name="setreuid" number="6111"/>
  <syscall name="setregid" number="6112"/>
  <syscall name="getgroups" number="6113"/>
  <syscall name="setgroups" number="6114"/>
  <syscall name="setresuid" number="6115"/>
  <syscall name="getresuid" number="6116"/>
  <syscall name="setresgid" number="6117"/>
  <syscall name="getresgid" number="6118"/>
  <syscall name="getpgid" number="6119"/>
  <syscall name="setfsuid" number="6120"/>
  <syscall name="setfsgid" number="6121"/>
  <syscall name="getsid" number="6122"/>
  <syscall name="capget" number="6123"/>
  <syscall name="capset" number="6124"/>
  <syscall name="rt_sigpending" number="6125" groups="signal"/>
  <syscall name="rt_sigtimedwait" number="6126" groups="signal"/>
  <syscall name="rt_sigqueueinfo" number="6127" groups="signal,process"/>
  <syscall name="rt_sigsuspend" number="6128" groups="signal"/>
  <syscall name="sigaltstack" number="6129" groups="signal"/>
  <syscall name="utime" number="6130" groups="file"/>
  <syscall name="mknod" number="6131" groups="file"/>
  <syscall name="personality" number="6132"/>
  <syscall name="ustat" number="6133"/>
  <syscall name="statfs" number="6134" groups="file"/>
  <syscall name="fstatfs" number="6135" groups="descriptor"/>
  <syscall name="sysfs" number="6136"/>
  <syscall name="getpriority" number="6137"/>
  <syscall name="setpriority" number="6138"/>
  <syscall name="sched_setparam" number="6139"/>
  <syscall name="sched_getparam" number="6140"/>
  <syscall name="sched_setscheduler" number="6141"/>
  <syscall name="sched_getscheduler" number="6142"/>
  <syscall name="sched_get_priority_max" number="6143"/>
  <syscall name="sched_get_priority_min" number="6144"/>
  <syscall name="sched_rr_get_interval" number="6145"/>
  <syscall name="mlock" number="6146" groups="memory"/>
  <syscall name="munlock" number="6147" groups="memory"/>
  <syscall name="mlockall" number="6148" groups="memory"/>
  <syscall name="munlockall" number="6149" groups="memory"/>
  <syscall name="vhangup" number="6150"/>
  <syscall name="pivot_root" number="6151" groups="file"/>
  <syscall name="_sysctl" number="6152"/>
  <syscall name="prctl" number="6153"/>
  <syscall name="adjtimex" number="6154"/>
  <syscall name="setrlimit" number="6155"/>
  <syscall name="chroot" number="6156" groups="file"/>
  <syscall name="sync" number="6157"/>
  <syscall name="acct" number="6158" groups="file"/>
  <syscall name="settimeofday" number="6159"/>
  <syscall name="mount" number="6160" groups="file"/>
  <syscall name="umount2" number="6161" groups="file"/>
  <syscall name="swapon" number="6162" groups="file"/>
  <syscall name="swapoff" number="6163" groups="file"/>
  <syscall name="reboot" number="6164"/>
  <syscall name="sethostname" number="6165"/>
  <syscall name="setdomainname" number="6166"/>
  <syscall name="create_module" number="6167"/>
  <syscall name="init_module" number="6168"/>
  <syscall name="delete_module" number="6169"/>
  <syscall name="get_kernel_syms" number="6170"/>
  <syscall name="query_module" number="6171"/>
  <syscall name="quotactl" number="6172" groups="file"/>
  <syscall name="nfsservctl" number="6173"/>
  <syscall name="getpmsg" number="6174" groups="network"/>
  <syscall name="putpmsg" number="6175" groups="network"/>
  <syscall name="afs_syscall" number="6176"/>
  <syscall name="gettid" number="6178"/>
  <syscall name="readahead" number="6179" groups="descriptor"/>
  <syscall name="setxattr" number="6180" groups="file"/>
  <syscall name="lsetxattr" number="6181" groups="file"/>
  <syscall name="fsetxattr" number="6182" groups="descriptor"/>
  <syscall name="getxattr" number="6183" groups="file"/>
  <syscall name="lgetxattr" number="6184" groups="file"/>
  <syscall name="fgetxattr" number="6185" groups="descriptor"/>
  <syscall name="listxattr" number="6186" groups="file"/>
  <syscall name="llistxattr" number="6187" groups="file"/>
  <syscall name="flistxattr" number="6188" groups="descriptor"/>
  <syscall name="removexattr" number="6189" groups="file"/>
  <syscall name="lremovexattr" number="6190" groups="file"/>
  <syscall name="fremovexattr" number="6191" groups="descriptor"/>
  <syscall name="tkill" number="6192" groups="signal,process"/>
  <syscall name="futex" number="6194"/>
  <syscall name="sched_setaffinity" number="6195"/>
  <syscall name="sched_getaffinity" number="6196"/>
  <syscall name="cacheflush" number="6197" groups="memory"/>
  <syscall name="cachectl" number="6198"/>
  <syscall name="sysmips" number="6199"/>
  <syscall name="io_setup" number="6200" groups="memory"/>
  <syscall name="io_destroy" number="6201" groups="memory"/>
  <syscall name="io_getevents" number="6202"/>
  <syscall name="io_submit" number="6203"/>
  <syscall name="io_cancel" number="6204"/>
  <syscall name="exit_group" number="6205" groups="process"/>
  <syscall name="lookup_dcookie" number="6206"/>
  <syscall name="epoll_create" number="6207" groups="descriptor"/>
  <syscall name="epoll_ctl" number="6208" groups="descriptor"/>
  <syscall name="epoll_wait" number="6209" groups="descriptor"/>
  <syscall name="remap_file_pages" number="6210" groups="memory"/>
  <syscall name="rt_sigreturn" number="6211" groups="signal"/>
  <syscall name="fcntl64" number="6212" groups="descriptor"/>
  <syscall name="set_tid_address" number="6213"/>
  <syscall name="restart_syscall" number="6214"/>
  <syscall name="semtimedop" number="6215" groups="ipc"/>
  <syscall name="fadvise64" number="6216" groups="descriptor"/>
  <syscall name="statfs64" number="6217" groups="file"/>
  <syscall name="fstatfs64" number="6218" groups="descriptor"/>
  <syscall name="sendfile64" number="6219" groups="descriptor,network"/>
  <syscall name="timer_create" number="6220"/>
  <syscall name="timer_settime" number="6221"/>
  <syscall name="timer_gettime" number="6222"/>
  <syscall name="timer_getoverrun" number="6223"/>
  <syscall name="timer_delete" number="6224"/>
  <syscall name="clock_settime" number="6225"/>
  <syscall name="clock_gettime" number="6226"/>
  <syscall name="clock_getres" number="6227"/>
  <syscall name="clock_nanosleep" number="6228"/>
  <syscall name="tgkill" number="6229" groups="signal,process"/>
  <syscall name="utimes" number="6230" groups="file"/>
  <syscall name="mbind" number="6231" groups="memory"/>
  <syscall name="get_mempolicy" number="6232" groups="memory"/>
  <syscall name="set_mempolicy" number="6233" groups="memory"/>
  <syscall name="mq_open" number="6234" groups="descriptor"/>
  <syscall name="mq_unlink" number="6235"/>
  <syscall name="mq_timedsend" number="6236" groups="descriptor"/>
  <syscall name="mq_timedreceive" number="6237" groups="descriptor"/>
  <syscall name="mq_notify" number="6238" groups="descriptor"/>
  <syscall name="mq_getsetattr" number="6239" groups="descriptor"/>
  <syscall name="vserver" number="6240"/>
  <syscall name="waitid" number="6241" groups="process"/>
  <syscall name="add_key" number="6243"/>
  <syscall name="request_key" number="6244"/>
  <syscall name="keyctl" number="6245"/>
  <syscall name="set_thread_area" number="6246"/>
  <syscall name="inotify_init" number="6247" groups="descriptor"/>
  <syscall name="inotify_add_watch" number="6248" groups="descriptor,file"/>
  <syscall name="inotify_rm_watch" number="6249" groups="descriptor"/>
  <syscall name="migrate_pages" number="6250" groups="memory"/>
  <syscall name="openat" number="6251" groups="descriptor,file"/>
  <syscall name="mkdirat" number="6252" groups="descriptor,file"/>
  <syscall name="mknodat" number="6253" groups="descriptor,file"/>
  <syscall name="fchownat" number="6254" groups="descriptor,file"/>
  <syscall name="futimesat" number="6255" groups="descriptor,file"/>
  <syscall name="newfstatat" number="6256" groups="descriptor,file"/>
  <syscall name="unlinkat" number="6257" groups="descriptor,file"/>
  <syscall name="renameat" number="6258" groups="descriptor,file"/>
  <syscall name="linkat" number="6259" groups="descriptor,file"/>
  <syscall name="symlinkat" number="6260" groups="descriptor,file"/>
  <syscall name="readlinkat" number="6261" groups="descriptor,file"/>
  <syscall name="fchmodat" number="6262" groups="descriptor,file"/>
  <syscall name="faccessat" number="6263" groups="descriptor,file"/>
  <syscall name="pselect6" number="6264" groups="descriptor"/>
  <syscall name="ppoll" number="6265" groups="descriptor"/>
  <syscall name="unshare" number="6266"/>
  <syscall name="splice" number="6267" groups="descriptor"/>
  <syscall name="sync_file_range" number="6268" groups="descriptor"/>
  <syscall name="tee" number="6269" groups="descriptor"/>
  <syscall name="vmsplice" number="6270" groups="descriptor"/>
  <syscall name="move_pages" number="6271" groups="memory"/>
  <syscall name="set_robust_list" number="6272"/>
  <syscall name="get_robust_list" number="6273"/>
  <syscall name="kexec_load" number="6274"/>
  <syscall name="getcpu" number="6275"/>
  <syscall name="epoll_pwait" number="6276" groups="descriptor"/>
  <syscall name="ioprio_set" number="6277"/>
  <syscall name="ioprio_get" number="6278"/>
  <syscall name="utimensat" number="6279" groups="descriptor,file"/>
  <syscall name="signalfd" number="6280" groups="descriptor,signal"/>
  <syscall name="timerfd" number="6281" groups="descriptor"/>
  <syscall name="eventfd" number="6282" groups="descriptor"/>
  <syscall name="fallocate" number="6283" groups="descriptor"/>
  <syscall name="timerfd_create" number="6284" groups="descriptor"/>
  <syscall name="timerfd_gettime" number="6285" groups="descriptor"/>
  <syscall name="timerfd_settime" number="6286" groups="descriptor"/>
  <syscall name="signalfd4" number="6287" groups="descriptor,signal"/>
  <syscall name="eventfd2" number="6288" groups="descriptor"/>
  <syscall name="epoll_create1" number="6289" groups="descriptor"/>
  <syscall name="dup3" number="6290" groups="descriptor"/>
  <syscall name="pipe2" number="6291" groups="descriptor"/>
  <syscall name="inotify_init1" number="6292" groups="descriptor"/>
  <syscall name="preadv" number="6293" groups="descriptor"/>
  <syscall name="pwritev" number="6294" groups="descriptor"/>
  <syscall name="rt_tgsigqueueinfo" number="6295" groups="process,signal"/>
  <syscall name="perf_event_open" number="6296" groups="descriptor"/>
  <syscall name="accept4" number="6297" groups="network"/>
  <syscall name="recvmmsg" number="6298" groups="network"/>
  <syscall name="getdents64" number="6299" groups="descriptor"/>
  <syscall name="fanotify_init" number="6300" groups="descriptor"/>
  <syscall name="fanotify_mark" number="6301" groups="descriptor,file"/>
  <syscall name="prlimit64" number="6302"/>
  <syscall name="name_to_handle_at" number="6303" groups="descriptor,file"/>
  <syscall name="open_by_handle_at" number="6304" groups="descriptor"/>
  <syscall name="clock_adjtime" number="6305"/>
  <syscall name="syncfs" number="6306" groups="descriptor"/>
  <syscall name="sendmmsg" number="6307" groups="network"/>
  <syscall name="setns" number="6308" groups="descriptor"/>
  <syscall name="process_vm_readv" number="6309"/>
  <syscall name="process_vm_writev" number="6310"/>
  <syscall name="kcmp" number="6311"/>
  <syscall name="finit_module" number="6312" groups="descriptor"/>
  <syscall name="sched_setattr" number="6313"/>
  <syscall name="sched_getattr" number="6314"/>
  <syscall name="renameat2" number="6315" groups="descriptor,file"/>
  <syscall name="seccomp" number="6316"/>
  <syscall name="getrandom" number="6317"/>
  <syscall name="memfd_create" number="6318" groups="descriptor"/>
  <syscall name="bpf" number="6319" groups="descriptor"/>
  <syscall name="execveat" number="6320" groups="descriptor,file,process"/>
  <syscall name="userfaultfd" number="6321" groups="descriptor"/>
  <syscall name="membarrier" number="6322"/>
  <syscall name="mlock2" number="6323" groups="memory"/>
  <syscall name="copy_file_range" number="6324" groups="descriptor"/>
  <syscall name="preadv2" number="6325" groups="descriptor"/>
  <syscall name="pwritev2" number="6326" groups="descriptor"/>
  <syscall name="pkey_mprotect" number="6327" groups="memory"/>
  <syscall name="pkey_alloc" number="6328"/>
  <syscall name="pkey_free" number="6329"/>
  <syscall name="statx" number="6330" groups="descriptor,file"/>
  <syscall name="rseq" number="6331"/>
  <syscall name="io_pgetevents" number="6332"/>
  <syscall name="clock_gettime64" number="6403"/>
  <syscall name="clock_settime64" number="6404"/>
  <syscall name="clock_adjtime64" number="6405"/>
  <syscall name="clock_getres_time64" number="6406"/>
  <syscall name="clock_nanosleep_time64" number="6407"/>
  <syscall name="timer_gettime64" number="6408"/>
  <syscall name="timer_settime64" number="6409"/>
  <syscall name="timerfd_gettime64" number="6410" groups="descriptor"/>
  <syscall name="timerfd_settime64" number="6411" groups="descriptor"/>
  <syscall name="utimensat_time64" number="6412" groups="descriptor,file"/>
  <syscall name="pselect6_time64" number="6413" groups="descriptor"/>
  <syscall name="ppoll_time64" number="6414" groups="descriptor"/>
  <syscall name="io_pgetevents_time64" number="6416"/>
  <syscall name="recvmmsg_time64" number="6417" groups="network"/>
  <syscall name="mq_timedsend_time64" number="6418" groups="descriptor"/>
  <syscall name="mq_timedreceive_time64" number="6419" groups="descriptor"/>
  <syscall name="semtimedop_time64" number="6420" groups="ipc"/>
  <syscall name="rt_sigtimedwait_time64" number="6421" groups="signal"/>
  <syscall name="futex_time64" number="6422"/>
  <syscall name="sched_rr_get_interval_time64" number="6423"/>
  <syscall name="pidfd_send_signal" number="6424" groups="descriptor,signal,process"/>
  <syscall name="io_uring_setup" number="6425" groups="descriptor"/>
  <syscall name="io_uring_enter" number="6426" groups="descriptor,signal"/>
  <syscall name="io_uring_register" number="6427" groups="descriptor,memory"/>
  <syscall name="open_tree" number="6428" groups="descriptor,file"/>
  <syscall name="move_mount" number="6429" groups="descriptor,file"/>
  <syscall name="fsopen" number="6430" groups="descriptor"/>
  <syscall name="fsconfig" number="6431" groups="descriptor,file"/>
  <syscall name="fsmount" number="6432" groups="descriptor"/>
  <syscall name="fspick" number="6433" groups="descriptor,file"/>
  <syscall name="pidfd_open" number="6434" groups="descriptor"/>
  <syscall name="clone3" number="6435" groups="process"/>
  <syscall name="close_range" number="6436"/>
  <syscall name="openat2" number="6437" groups="descriptor,file"/>
  <syscall name="pidfd_getfd" number="6438" groups="descriptor"/>
  <syscall name="faccessat2" number="6439" groups="descriptor,file"/>
  <syscall name="process_madvise" number="6440" groups="descriptor"/>
  <syscall name="epoll_pwait2" number="6441" groups="descriptor"/>
  <syscall name="mount_setattr" number="6442" groups="descriptor,file"/>
  <syscall name="quotactl_fd" number="6443" groups="descriptor"/>
  <syscall name="landlock_create_ruleset" number="6444" groups="descriptor"/>
  <syscall name="landlock_add_rule" number="6445" groups="descriptor"/>
  <syscall name="landlock_restrict_self" number="6446" groups="descriptor"/>
  <syscall name="process_mrelease" number="6448" groups="descriptor"/>
  <syscall name="futex_waitv" number="6449"/>
  <syscall name="set_mempolicy_home_node" number="6450" groups="memory"/>
  <syscall name="cachestat" number="6451" groups="descriptor"/>
  <syscall name="fchmodat2" number="6452" groups="descriptor,file"/>
  <syscall name="map_shadow_stack" number="6453" groups="memory"/>
  <syscall name="futex_wake" number="6454"/>
  <syscall name="futex_wait" number="6455"/>
  <syscall name="futex_requeue" number="6456"/>
  <syscall name="statmount" number="6457"/>
  <syscall name="listmount" number="6458"/>
  <syscall name="lsm_get_self_attr" number="6459"/>
  <syscall name="lsm_set_self_attr" number="6460"/>
  <syscall name="lsm_list_modules" number="6461"/>
  <syscall name="mseal" number="6462" groups="memory"/>
  <syscall name="setxattrat" number="6463"/>
  <syscall name="getxattrat" number="6464"/>
  <syscall name="listxattrat" number="6465"/>
  <syscall name="removexattrat" number="6466"/>
</syscalls_info>
