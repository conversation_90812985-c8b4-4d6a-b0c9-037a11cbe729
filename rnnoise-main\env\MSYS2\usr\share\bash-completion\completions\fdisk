_fdisk_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-s'|'--getsz')
			COMPREPLY=( $(compgen -W "$(lsblk -pnro name)" -- $cur) )
			return 0
			;;
		'-b'|'--sector-size')
			COMPREPLY=( $(compgen -W "512 1024 2048 4096" -- $cur) )
			return 0
			;;
		'-c'|'--compatibility')
			COMPREPLY=( $(compgen -W "dos nondos" -- $cur) )
			return 0
			;;
		'-L'|'--color')
			COMPREPLY=( $(compgen -W "auto never always" -- $cur) )
			return 0
			;;
		'--output')
			local prefix realcur OUTPUT_ALL OUTPUT
			realcur="${cur##*,}"
			prefix="${cur%$realcur}"
			OUTPUT_ALL="
				Attrs
				Boot
				Bsize
				Cpg
				Cylinders
				Device
				End
				End-C/H/S
				Flags
				Fsize
				Id
				Name
				Sectors
				Size
				Slice
				Start
				Start-C/H/S
				Type
				Type-UUID
				UUID
			"
			for WORD in $OUTPUT_ALL; do
				if ! [[ $prefix == *"$WORD"* ]]; then
					OUTPUT="$WORD ${OUTPUT:-""}"
				fi
			done
			compopt -o nospace
			COMPREPLY=( $(compgen -P "$prefix" -W "$OUTPUT" -S ',' -- "$realcur") )
			return 0
			;;
		'-u'|'--units')
			COMPREPLY=( $(compgen -W "cylinders sectors" -- $cur) )
			return 0
			;;
		'-C'|'--cylinders'|'-H'|'--heads'|'-S'|'--sectors')
			COMPREPLY=( $(compgen -W "number" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-v'|'--version'|'-V'|'--list')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--sector-size
				--protect-boot
				--compatibility
				--color
				--list
				--list-details
				--noauto-pt
				--lock
				--output
				--type
				--units
				--getsz
				--bytes
				--wipe
				--wipe-partitions
				--cylinders
				--heads
				--sectors
				--help
				--version"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	compopt -o bashdefault -o default
	COMPREPLY=( $(compgen -W "$(lsblk -pnro name)" -- $cur) )
	return 0
}
complete -F _fdisk_module fdisk
