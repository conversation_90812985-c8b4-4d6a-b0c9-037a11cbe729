.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_digest_set_secure" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_digest_set_secure \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_digest_set_secure(gnutls_digest_algorithm_t " dig ", unsigned int " secure ");"
.SH ARGUMENTS
.IP "gnutls_digest_algorithm_t dig" 12
is a digest algorithm
.IP "unsigned int secure" 12
whether to mark the digest algorithm secure
.SH "DESCRIPTION"
Modify the previous system wide setting that marked  \fIdig\fP as secure
or insecure. This only has effect when the algorithm is enabled
through the allowlisting mode in the configuration file, or when
the setting is modified with a prior call to this function.
.SH "SINCE"
3.7.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
