# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the mintty package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2025-03-22 07:33+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: charset.c:228 charset.c:239 winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "(Default)"
msgstr ""

#: charset.c:250
msgid "(OEM codepage)"
msgstr ""

#: charset.c:254
msgid "(ANSI codepage)"
msgstr ""

#: child.c:96
msgid "There are no available terminals"
msgstr "Keine verfügbaren Terminals"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "Fehler: Konnte Log-Datei nicht öffnen"

#: child.c:334
msgid "Error: Could not fork child process"
msgstr "Fehler: Konnte Prozess nicht erzeugen"

#: child.c:336
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr ""
"DLL rebasing vielleicht erforderlich; siehe 'rebaseall / rebase --help'"

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:426
msgid "Failed to run '%s': %s"
msgstr "Starten von '%s' fehlgeschlagen: %s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:578
msgid "%s: Exit %i"
msgstr "%s: Beendigung %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:585
msgid "TERMINATED"
msgstr "BEENDET"

#: child.c:1232
msgid "Error: Could not fork child daemon"
msgstr "Fehler: Konnte Daemon-Prozess nicht starten"

#. __ Setting false for Boolean options (localization optional)
#: config.c:655
msgid "no"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:657
msgid "yes"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:659
msgid "false"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:661
msgid "true"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:663
msgid "off"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:665
msgid "on"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:694
msgid "stretch"
msgstr "gedehnt"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:696
msgid "align"
msgstr "ausgerichtet"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:698
msgid "middle"
msgstr "zentriert"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:700
msgid "full"
msgstr "voll"

#. __ %s: unknown option name
#: config.c:866
msgid "Ignoring unknown option '%s'"
msgstr "Ignoriere unbekannte Option '%s'"

#: config.c:914 config.c:943
msgid "Internal error: too many options"
msgstr "Interner Fehler: zu viele Optionen"

#: config.c:930
msgid "Internal error: too many options/comments"
msgstr "Interner Fehler: zu viele Optionen/Kommentare"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1100
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "Ignoriere ungültigen Wert '%s' für Option '%s'"

#. __ %s: option name
#: config.c:1112
msgid "Ignoring option '%s' with missing value"
msgstr "Ignoriere Option '%s' – Wert fehlt"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1791
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"Konnte Option nicht in '%s' schreiben:\n"
"%s."

#: config.c:2172
msgid "◇ None (printing disabled) ◇"
msgstr "◇ Keiner (Drucken deaktiviert) ◇"

#: config.c:2174
msgid "◆ Default printer ◆"
msgstr "◆ Default-Drucker ◆"

#. __ UI localization disabled
#: config.c:2283
msgid "– None –"
msgstr "– Keine –"

#. __ UI localization: use Windows desktop setting
#: config.c:2285
msgid "@ Windows language @"
msgstr "@ Windows-Sprache @"

#. __ UI localization: use environment variable setting (LANGUAGE, LC_*)
#: config.c:2287
msgid "* Locale environm. *"
msgstr "* Locale-Umgebung *"

#. __ UI localization: use mintty configuration setting (Text - Locale)
#: config.c:2289
msgid "= cfg. Text Locale ="
msgstr "= kfg. Text-Locale ="

#: config.c:2394
msgid "simple beep"
msgstr "einfacher Piep"

#: config.c:2395
msgid "no beep"
msgstr "kein Piep"

#: config.c:2396
msgid "Default Beep"
msgstr "Default-Piep"

#: config.c:2397
msgid "Critical Stop"
msgstr "Fehler"

#: config.c:2398
msgid "Question"
msgstr "Frage"

#: config.c:2399
msgid "Exclamation"
msgstr "Warnung"

#: config.c:2400
msgid "Asterisk"
msgstr "Hinweis"

#: config.c:2443
msgid "◇ None (system sound) ◇"
msgstr "◇ Keine (Systemklang) ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2874 config.c:3425
msgid "◇ None ◇"
msgstr "◇ Keins ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2877
msgid "downloaded / give me a name!"
msgstr "runtergeladen / Namen geben!"

#: config.c:2983
msgid "Could not load web theme"
msgstr "Konnte Web-Schema nicht laden"

#: config.c:3040
msgid "Cannot write theme file"
msgstr "Kann Schema nicht schreiben"

#: config.c:3045
msgid "Cannot store theme file"
msgstr "Kann Schema nicht speichern"

#. __ Options - Text:
#: config.c:3502 config.c:3840 config.c:3939
msgid "as font"
msgstr "als Schrift"

#. __ Options - Text:
#: config.c:3503 config.c:3845 config.c:3944
msgid "as colour"
msgstr "als Farbe"

#: config.c:3504
msgid "as font & as colour"
msgstr "als Schrift & Farbe"

#. __ Options - Text:
#: config.c:3505 config.c:3850 config.c:3949
msgid "xterm"
msgstr ""

#. __ Dialog button - show About text
#: config.c:3653
msgid "About..."
msgstr "Über..."

#. __ Dialog button - save changes
#: config.c:3656
msgid "Save"
msgstr "Sichern"

#. __ Dialog button - cancel
#: config.c:3660 winctrls.c:1277 windialog.c:895
msgid "Cancel"
msgstr "Abbruch"

#. __ Dialog button - apply changes
#: config.c:3664
msgid "Apply"
msgstr "Anwenden"

#. __ Dialog button - take notice
#: config.c:3668 windialog.c:892
msgid "I see"
msgstr "Aha"

#. __ Dialog button - confirm action
#: config.c:3670 winctrls.c:1276 windialog.c:894
msgid "OK"
msgstr ""

#. __ Options - Looks: treeview label
#: config.c:3677 config.c:3708 config.c:3767
msgid "Looks"
msgstr "Aussehen"

#. __ Options - Looks: panel title
#: config.c:3679
msgid "Looks in Terminal"
msgstr "Aussehen im Terminal"

#. __ Options - Looks: section title
#: config.c:3681
msgid "Colours"
msgstr "Farben"

#. __ Options - Looks:
#: config.c:3685
msgid "&Foreground..."
msgstr "&Vordergrund.."

#. __ Options - Looks:
#: config.c:3689
msgid "&Background..."
msgstr "&Hintergrund.."

#. __ Options - Looks:
#: config.c:3693
msgid "&Cursor..."
msgstr "&Zeiger.."

#. __ Options - Looks:
#: config.c:3697
msgid "&Theme"
msgstr "&Schema"

#. __ Options - Looks: name of web service
#: config.c:3702
msgid "Color Scheme Designer"
msgstr ""

#. __ Options - Looks: store colour scheme
#: config.c:3705 winctrls.c:484
msgid "Store"
msgstr "Sichern"

#. __ Options - Looks: section title
#: config.c:3710
msgid "Transparency"
msgstr "Transparenz"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3716 config.c:4098 config.c:4261 config.c:4459
msgid "&Off"
msgstr "A&us"

#. __ Options - Looks: transparency
#: config.c:3718
msgid "&Low"
msgstr "&Klein"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3720
msgid "&Med."
msgstr "&Mittel"

#. __ Options - Looks: transparency
#: config.c:3722
msgid "&Medium"
msgstr "&Mittel"

#. __ Options - Looks: transparency
#: config.c:3724
msgid "&High"
msgstr "H&och"

#. __ Options - Looks: transparency
#: config.c:3726
msgid "Gla&ss"
msgstr "&Glas"

#. __ Options - Looks: transparency
#: config.c:3733 config.c:3745 config.c:3752
msgid "Opa&que when focused"
msgstr "Undurchsichtig wenn im &Fokus"

#. __ Options - Looks: transparency
#: config.c:3738
msgid "Blu&r"
msgstr "U&nklar"

#: config.c:3759
msgid "◄"
msgstr ""

#: config.c:3762
msgid "►"
msgstr ""

#. __ Options - Looks: section title
#: config.c:3769
msgid "Cursor"
msgstr "Zeiger"

#. __ Options - Looks: cursor type
#: config.c:3774
msgid "Li&ne"
msgstr "&Linie"

#. __ Options - Looks: cursor type
#: config.c:3776
msgid "Bloc&k"
msgstr "&Block"

#. __ Options - Looks: cursor type
#: config.c:3779
msgid "Bo&x"
msgstr ""

#. __ Options - Looks: cursor type
#: config.c:3782
msgid "&Underscore"
msgstr "Un&terstrich"

#. __ Options - Looks: cursor feature
#: config.c:3787
msgid "Blinkin&g"
msgstr "Blinken&d"

#. __ Options - Text: treeview label
#: config.c:3794 config.c:3819 config.c:3834 config.c:3883 config.c:3933
#: config.c:3958 config.c:3980 config.c:3993 config.c:4001
msgid "Text"
msgstr "Schrift"

#. __ Options - Text: panel title
#: config.c:3796
msgid "Text and Font properties"
msgstr "Text und Schrift – Eigenschaften"

#. __ Options - Text: section title
#: config.c:3798
msgid "Font"
msgstr "Schrift"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3806 winctrls.c:1287
msgid "Font st&yle:"
msgstr "Schriftsch&nitt:"

#. __ Font chooser:
#: config.c:3811 winctrls.c:1289
msgid "&Size:"
msgstr "Schr.&grad:"

#. __ Options - Text:
#: config.c:3823 config.c:3902
msgid "Sho&w bold as font"
msgstr "Zeige &fett als Schrift"

#. __ Options - Text:
#: config.c:3828 config.c:3907
msgid "Show &bold as colour"
msgstr "Zeige fett als F&arbe"

#. __ Options - Text:
#: config.c:3836 config.c:3857 config.c:3935 config.c:3962
msgid "Show bold"
msgstr "Zeige fett"

#. __ Options - Text:
#: config.c:3864 config.c:3912 config.c:3968
msgid "&Allow blinking"
msgstr "&Blinken zulassen"

#. __ Options - Text:
#: config.c:3869 config.c:3973
msgid "Show dim as font"
msgstr "Zeige blass als Schrift"

#. __ Options - Text:
#: config.c:3887 config.c:3920 config.c:3955
msgid "Font smoothing"
msgstr "Schriftglättung"

#. __ Options - Text:
#: config.c:3890 config.c:3923 config.c:4149 config.c:4188 config.c:4342
#: config.c:4355
msgid "&Default"
msgstr "&Default"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3892 config.c:3925 config.c:4147 config.c:4186 config.c:4340
#: config.c:4353 config.c:4440
msgid "&None"
msgstr "&Keine"

#. __ Options - Text:
#: config.c:3894 config.c:3927 config.c:4148 config.c:4187 config.c:4341
#: config.c:4354
msgid "&Partial"
msgstr "&Teils"

#. __ Options - Text:
#: config.c:3896 config.c:3929 config.c:4150 config.c:4189 config.c:4343
#: config.c:4356
msgid "&Full"
msgstr "&Voll"

#: config.c:3983
msgid "&Locale"
msgstr ""

#: config.c:3986
msgid "&Character set"
msgstr "&Zeichensatz"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3997 config.c:4003
msgid "Emojis"
msgstr ""

#. __ Options - Text - Emojis:
#: config.c:4007
msgid "Style"
msgstr "Stil"

#. __ Options - Text - Emojis:
#: config.c:4012
msgid "Placement"
msgstr "Platzierung"

#. __ Options - Keys: treeview label
#: config.c:4020 config.c:4050 config.c:4085 config.c:4103
msgid "Keys"
msgstr "Tastatur"

#. __ Options - Keys: panel title
#: config.c:4022
msgid "Keyboard features"
msgstr "Tastatur-Eigenschaften"

#. __ Options - Keys:
#: config.c:4026
msgid "&Backarrow sends ^H"
msgstr "&Rücktaste sendet ^H"

#. __ Options - Keys:
#: config.c:4031
msgid "&Delete sends DEL"
msgstr "&Entf sendet DEL"

#. __ Options - Keys:
#: config.c:4036
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "Strg+LeftAlt ist Alt&Gr"

#. __ Options - Keys:
#: config.c:4041
msgid "AltGr is also Alt"
msgstr "AltGr ist auch Alt"

#. __ Options - Keys:
#: config.c:4046
msgid "&Esc/Enter reset IME to alphanumeric"
msgstr "&Esc/Enter setzt IME zurück"

#. __ Options - Keys: section title
#: config.c:4052
msgid "Shortcuts"
msgstr "Kürzel"

#. __ Options - Keys:
#: config.c:4055
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "K&opieren/Einfügen (Strg/Shift+Ins)"

#. __ Options - Keys:
#: config.c:4060
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "&Menü und Vollbildschirm (Alt+Space/Enter)"

#. __ Options - Keys:
#: config.c:4065
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "&Fenster wechseln (Strg+[Shift+]Tab)"

#. __ Options - Keys:
#: config.c:4070
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "&Zoomen (Strg+plus/minus/zero)"

#. __ Options - Keys:
#: config.c:4075
msgid "&Alt+Fn shortcuts"
msgstr "&Alt+Fn Kürzel"

#. __ Options - Keys:
#: config.c:4080
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "&Strg+Shift+letter Kürzel"

#. __ Options - Keys: section title
#: config.c:4087 config.c:4105
msgid "Compose key"
msgstr "Compose-Taste"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:4092 config.c:4253 config.c:4272 config.c:4451 config.c:4470
msgid "&Shift"
msgstr ""

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:4094 config.c:4255 config.c:4280 config.c:4453 config.c:4478
msgid "&Ctrl"
msgstr "S&trg"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4096 config.c:4257 config.c:4276 config.c:4455 config.c:4474
msgid "&Alt"
msgstr ""

#. __ Options - Mouse: treeview label
#: config.c:4112 config.c:4201 config.c:4233
msgid "Mouse"
msgstr "Maus"

#. __ Options - Mouse: panel title
#: config.c:4114
msgid "Mouse functions"
msgstr "Maus-Funktionen"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4122 config.c:4155 config.c:4171 config.c:4319
msgid "Cop&y on select"
msgstr "K&opieren bei Auswahl"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4127 config.c:4160 config.c:4324 wininput.c:685
msgid "Copy with TABs"
msgstr "Kopieren mit &TABs"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4132 config.c:4165 config.c:4177 config.c:4329
msgid "Copy as &rich text"
msgstr "Kopieren als &Rich Text"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4138 config.c:4145 config.c:4184 config.c:4338 config.c:4351
msgid "Copy as &HTML"
msgstr "Kopieren als &HTML"

#. __ Options - Mouse:
#: config.c:4197
msgid "Clic&ks place command line cursor"
msgstr "&Klick platziert in Kommandozeile"

#. __ Options - Mouse: section title
#: config.c:4203
msgid "Click actions"
msgstr "Klick-Aktionen"

#. __ Options - Mouse:
#: config.c:4206
msgid "Right mouse button"
msgstr "Rechte Maustaste"

#. __ Options - Mouse:
#: config.c:4209 config.c:4223
msgid "&Paste"
msgstr "&Einfügen"

#. __ Options - Mouse:
#: config.c:4211 config.c:4225
msgid "E&xtend"
msgstr "&Dehnen"

#. __ Options - Mouse:
#: config.c:4213
msgid "&Menu"
msgstr "&Menü"

#. __ Options - Mouse:
#: config.c:4215 config.c:4229
msgid "Ente&r"
msgstr "Ein&gabe"

#. __ Options - Mouse:
#: config.c:4220
msgid "Middle mouse button"
msgstr "Mittlere Maustaste"

#. __ Options - Mouse:
#: config.c:4227
msgid "&Nothing"
msgstr "&Nichts"

#. __ Options - Mouse: section title
#: config.c:4235
msgid "Application mouse mode"
msgstr "Anwendungs-Maus-Modus"

#. __ Options - Mouse:
#: config.c:4238
msgid "Default click target"
msgstr "Default-Klickziel"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4241
msgid "&Window"
msgstr "&Fenster"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4243
msgid "&Application"
msgstr "&Anwendung"

#. __ Options - Mouse:
#: config.c:4250 config.c:4268
msgid "Modifier for overriding default"
msgstr "Modifizierer zum Ändern des Defaults"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4259 config.c:4284 config.c:4457 config.c:4482
msgid "&Win"
msgstr ""

#. __ Options - Modifier - Super:
#: config.c:4288 config.c:4486
msgid "&Sup"
msgstr ""

#. __ Options - Modifier - Hyper:
#: config.c:4292 config.c:4490
msgid "&Hyp"
msgstr ""

#. __ Options - Selection: treeview label
#: config.c:4302 config.c:4313 config.c:4377
msgid "Selection"
msgstr "Auswahl"

#. __ Options - Selection: panel title
#: config.c:4304
msgid "Selection and clipboard"
msgstr "Auswahl und Clipboard"

#. __ Options - Selection:
#: config.c:4308
msgid "Clear selection on input"
msgstr "Auswahl zurücksetzen bei Eingabe"

#. __ Options - Selection: section title
#: config.c:4315
msgid "Clipboard"
msgstr "Zwischenablage"

#. __ Options - Selection:
#: config.c:4365
msgid "Trim space from selection"
msgstr "Auswahl ohne Leerzeichen am Ende"

#. __ Options - Selection:
#: config.c:4371
msgid "Allow setting selection"
msgstr "Setzen der Auswahl zulassen"

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4379 config.c:4401 config.c:4426 config.c:4499
msgid "Window"
msgstr "Fenster"

#. __ Options - Selection: clock position of info popup for text size
#: config.c:4384
msgid "Show size while selecting (0..12)"
msgstr "Zeig Größe während Auswahl (0..12)"

#. __ Options - Selection:
#: config.c:4391
msgid "Suspend output while selecting"
msgstr "Ausgabe pausieren während Auswahl"

#. __ Options - Window: panel title
#: config.c:4403
msgid "Window properties"
msgstr "Fenster-Eigenschaften"

#. __ Options - Window: section title
#: config.c:4405
msgid "Default size"
msgstr "Default-Größe"

#. __ Options - Window:
#: config.c:4409
msgid "Colu&mns"
msgstr "&Spalten"

#. __ Options - Window:
#: config.c:4413
msgid "Ro&ws"
msgstr "&Zeilen"

#. __ Options - Window:
#: config.c:4417
msgid "C&urrent size"
msgstr "&Aktuelle"

#. __ Options - Window:
#: config.c:4422
msgid "Re&wrap on resize"
msgstr "Bei Größenänderung neu umbrechen"

#. __ Options - Window:
#: config.c:4430
msgid "Scroll&back lines"
msgstr "Zur&ückroll-Zeilen"

#. __ Options - Window:
#: config.c:4435
msgid "Scrollbar"
msgstr "Rollbalken"

#. __ Options - Window: scrollbar
#: config.c:4438
msgid "&Left"
msgstr "&Links"

#. __ Options - Window: scrollbar
#: config.c:4442
msgid "&Right"
msgstr "&Rechts"

#. __ Options - Window:
#: config.c:4448 config.c:4466
msgid "Modifier for scrolling"
msgstr "Modifizierer für Rollen"

#. __ Options - Window:
#: config.c:4495
msgid "&PgUp and PgDn scroll without modifier"
msgstr "&Bild↑ und Bild↓ rollen ohne Modifizierer"

#. __ Options - Window: section title
#: config.c:4501
msgid "UI language"
msgstr "Sprache"

#. __ Options - Terminal: treeview label
#: config.c:4511 config.c:4524 config.c:4585 config.c:4599
msgid "Terminal"
msgstr ""

#. __ Options - Terminal: panel title
#: config.c:4513
msgid "Terminal features"
msgstr "Terminal-Eigenschaften"

#. __ Options - Terminal:
#: config.c:4517
msgid "&Type"
msgstr "&Typ"

#. __ Options - Terminal: answerback string for ^E request
#: config.c:4521
msgid "&Answerback"
msgstr "Rück&antwort"

#. __ Options - Terminal: section title
#: config.c:4526
msgid "Bell"
msgstr "Glocke"

#. __ Options - Terminal: bell
#: config.c:4533
msgid "► &Play"
msgstr "► Abs&pielen"

#. __ Options - Terminal: bell
#: config.c:4539
msgid "&Wave"
msgstr "Klang"

#. __ Options - Terminal: bell
#: config.c:4561 config.c:4574
msgid "&Flash"
msgstr "Blit&z"

#. __ Options - Terminal: bell
#: config.c:4563 config.c:4578
msgid "&Highlight in taskbar"
msgstr "Blitz in Task&leiste"

#. __ Options - Terminal: bell
#: config.c:4565 config.c:4582
msgid "&Popup"
msgstr ""

#. __ Options - Terminal: section title
#: config.c:4587
msgid "Printer"
msgstr "Drucker"

#. __ Options - Terminal:
#: config.c:4602
msgid "Prompt about running processes on &close"
msgstr "Vor &Schließen nach laufenden Prozessen fragen"

#. __ Options - Terminal:
#. __ Context menu:
#: config.c:4607 wininput.c:581
msgid "Status Line"
msgstr "Status-Zeile"

#: textprint.c:44 textprint.c:127
msgid "[Printing...] "
msgstr "[Druckt...] "

#. __ Options - Text: font chooser activation button
#: winctrls.c:935
msgid "&Select..."
msgstr "&Wählen..."

#. __ Font chooser: title bar label
#: winctrls.c:1281
msgid "Font "
msgstr "Schriftart"

#. __ Font chooser: button
#: winctrls.c:1283
msgid "&Apply"
msgstr "&Übernehmen"

#. __ Font chooser:
#: winctrls.c:1285
msgid "&Font:"
msgstr "&Schriftart:"

#. __ Font chooser:
#: winctrls.c:1291
msgid "Sample"
msgstr "Beispiel"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1295 winctrls.c:1554 winctrls.c:1719
msgid "Ferqœm’4€"
msgstr ""

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1312
msgid "Sc&ript:"
msgstr "S&kript:"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1314
msgid "<A>Show more fonts</A>"
msgstr "<A>Weitere Schriftarten anzeigen</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1319
msgid "Colour "
msgstr "Farbe"

#. __ Colour chooser:
#: winctrls.c:1332 winctrls.c:1344
msgid "B&asic colours:"
msgstr "Gru&ndfarben:"

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Custom colours:"
msgstr "Ben&utzerdefinierte Farben:"

#. __ Colour chooser:
#: winctrls.c:1360
msgid "De&fine Custom Colours >>"
msgstr "Farben &definieren >>"

#. __ Colour chooser:
#: winctrls.c:1363
msgid "Colour"
msgstr "Farbe"

#. __ Colour chooser:
#: winctrls.c:1365
msgid "|S&olid"
msgstr "|B&asis"

#. __ Colour chooser:
#: winctrls.c:1367
msgid "&Hue:"
msgstr "Farb&t.:"

#. __ Colour chooser:
#: winctrls.c:1370
msgid "&Sat:"
msgstr "S&ätt.:"

#. __ Colour chooser:
#: winctrls.c:1372
msgid "&Lum:"
msgstr "&Hell.:"

#. __ Colour chooser:
#: winctrls.c:1374
msgid "&Red:"
msgstr "&Rot:"

#. __ Colour chooser:
#: winctrls.c:1376
msgid "&Green:"
msgstr "&Grün:"

#. __ Colour chooser:
#: winctrls.c:1378
msgid "&Blue:"
msgstr "&Blau:"

#. __ Colour chooser:
#: winctrls.c:1381
msgid "A&dd to Custom Colours"
msgstr "Farben hin&zufügen"

#. __ Options: dialog title
#: windialog.c:266 windialog.c:839
msgid "Options"
msgstr "Optionen"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:268
msgid "available"
msgstr "verfügbar"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:783
msgid "100"
msgstr ""

#: windialog.c:924 windialog.c:951
msgid "Error"
msgstr "Fehler"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:300
msgid "Session switcher"
msgstr "Sitzungen"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:320
msgid "Session launcher"
msgstr "Neue Sitzung"

#: wininput.c:429 wininput.c:435
msgid "Ctrl+"
msgstr "Strg+"

#: wininput.c:430 wininput.c:436
msgid "Alt+"
msgstr ""

#: wininput.c:431 wininput.c:437
msgid "Shift+"
msgstr ""

#. __ System menu:
#: wininput.c:462
msgid "&Restore"
msgstr "&Wiederherstellen"

#. __ System menu:
#: wininput.c:464
msgid "&Move"
msgstr "&Verschieben"

#. __ System menu:
#: wininput.c:466
msgid "&Size"
msgstr "&Größe ändern"

#. __ System menu:
#: wininput.c:468
msgid "Mi&nimize"
msgstr "M&inimieren"

#. __ System menu:
#: wininput.c:470
msgid "Ma&ximize"
msgstr "M&aximieren"

#. __ System menu:
#: wininput.c:472
msgid "&Close"
msgstr "&Schließen"

#. __ System menu:
#: wininput.c:477
msgid "New &Window"
msgstr "Neues &Fenster"

#. __ System menu:
#: wininput.c:483
msgid "New &Tab"
msgstr "Neuer &Tab"

#. __ Context menu:
#: wininput.c:490
msgid "&Copy"
msgstr "&Kopieren"

#. __ Context menu:
#: wininput.c:509
msgid "&Paste "
msgstr "&Einfügen"

#. __ Context menu:
#: wininput.c:514
msgid "Copy → Paste"
msgstr "Kopieren → Einfügen"

#. __ Context menu:
#: wininput.c:519
msgid "S&earch"
msgstr "S&uchen"

#. __ Context menu:
#: wininput.c:526
msgid "&Log to File"
msgstr "&Log in Datei"

#. __ Context menu:
#: wininput.c:532
msgid "Character &Info"
msgstr "Zeichen-&Info"

#. __ Context menu:
#: wininput.c:538
msgid "VT220 Keyboard"
msgstr "VT220-Tastatur"

#. __ Context menu:
#: wininput.c:543
msgid "&Reset"
msgstr "Zu&rücksetzen"

#. __ Context menu:
#: wininput.c:551
msgid "&Default Size"
msgstr "Default-Gr&öße"

#. __ Context menu:
#: wininput.c:561
msgid "Scroll&bar"
msgstr "Roll&balken"

#. __ Context menu:
#: wininput.c:567
msgid "&Full Screen"
msgstr "&Vollbildschirm"

#. __ Context menu:
#: wininput.c:573
msgid "Flip &Screen"
msgstr "Anzeige &wechseln"

#. __ System menu:
#: wininput.c:591 wininput.c:763
msgid "Copy &Title"
msgstr "&Titel kopieren"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:593 wininput.c:747 wininput.c:765
msgid "&Options..."
msgstr "&Optionen..."

#. __ Context menu:
#: wininput.c:678
msgid "Ope&n"
msgstr "Ö&ffnen"

#. __ Context menu:
#: wininput.c:683
msgid "Copy as text"
msgstr "Kopieren als Text"

#. __ Context menu:
#: wininput.c:687
msgid "Copy as RTF"
msgstr "Kopieren als RTF"

#. __ Context menu:
#: wininput.c:689
msgid "Copy as HTML text"
msgstr "Kopieren als HTML-Text"

#. __ Context menu:
#: wininput.c:691
msgid "Copy as HTML"
msgstr "Kopieren als HTML"

#. __ Context menu:
#: wininput.c:693
msgid "Copy as HTML full"
msgstr "Kopieren als HTML-Bild"

#. __ Context menu:
#: wininput.c:700
msgid "Select &All"
msgstr "Alles &auswählen"

#. __ Context menu:
#: wininput.c:702
msgid "Save as &Image"
msgstr "Speichern als &Bild"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:714
msgid "HTML Screen Dump"
msgstr "Speichern als HTML"

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:722
msgid "Clear Scrollback"
msgstr "Lösche Scrollback"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:733
msgid "Send Break"
msgstr "Sende Break"

#. __ Context menu, user commands
#: wininput.c:835
msgid "User commands"
msgstr "Benutzer-Kommandos"

#: wininput.c:1503 wininput.c:1524 wininput.c:1526 wininput.c:1528
#: wininput.c:1565
msgid "[NO SCROLL] "
msgstr "[KEIN SCROLLEN] "

#: wininput.c:1516 wininput.c:1525 wininput.c:1530 wininput.c:1586
msgid "[SCROLL MODE] "
msgstr "[SCROLL MODUS] "

#: winmain.c:3845
msgid "Processes are running in session:"
msgstr "Laufende Prozesse:"

#: winmain.c:3846
msgid "Close anyway?"
msgstr "Trotzdem schließen?"

#: winmain.c:3870
msgid "Reset terminal?"
msgstr "Terminal zurücksetzen?"

#: winmain.c:4100
msgid "Try '--help' for more information"
msgstr "Versuche '--help' für mehr Information"

#: winmain.c:4108
msgid "Could not load icon"
msgstr "Konnte Symbol nicht laden"

#: winmain.c:6402
msgid "Usage:"
msgstr "Aufruf:"

#: winmain.c:6403
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[OPTION]... [ PROGRAMM [ARG]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:6406
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"Startet eine neue Terminal-Sitzung mit dem angegebenen Programm oder der "
"Standard-Kommandozeile.\n"
"Mit Bindestrich/Minus statt einem Programm wird eine Anmelde-Kommandozeile "
"gestartet.\n"
"\n"
"Optionen:\n"
"  -c, --config FILE     Lädt angegebene Konfigurationsdatei (s.a. -C oder -o "
"ThemeFile)\n"
"  -e, --exec ...        Behandelt weitere Paramater als auszuführendes "
"Programm\n"
"  -h, --hold never|start|error|always  Halte Fenster nach Programmende "
"offen\n"
"  -p, --position X,Y    Öffne Fenster an gegebenen Koordinaten\n"
"  -p, --position center|left|right|top|bottom  Öffne Fenster an spezieller "
"Stelle\n"
"  -p, --position @N     Öffne Fenster auf Bildschirm N\n"
"  -s, --size COLS,ROWS  Setze Terminalgröße in Zeichen (auch COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Setze max. Größe in gegebener Richtung\n"
"  -t, --title TITLE     Setze Fenstertitel (Default: ausgeführtes Programm) "
"(s.. -T)\n"
"  -w, --window normal|min|max|full|hide  Setze anfänglichen Fenster-Modus\n"
"  -i, --icon FILE[,IX]  Lade Fenstersymbol aus Datei, optional mit Index\n"
"  -l, --log FILE|-      Protokolliere Ausgabe in Datei oder Standardausgabe\n"
"      --nobidi|--nortl  Deaktiviere bidirektionale Ausgabe (rechts-nach-"
"links)\n"
"  -o, --option OPT=VAL  Setze Konfigurationsoption mit gegebenem Wert\n"
"  -B, --Border frame|void  Lege dünnen bzw. keinen Fensterrand fest\n"
"  -R, --Report s|o      Berichte Fensterposition (kurz/lang) nach Ende\n"
"      --nopin           Dieses Fenster kann nicht an Taskleiste geheftet "
"werden\n"
"  -D, --daemon          Starte neues Fenster mit Windows Kurztaste\n"
"  -H, --help            Zeige nur Hilfe-Text\n"
"  -V, --version         Zeige nur Versions-Information\n"
"Siehe die Manual-Seite für weitere Optionen und Konfiguration.\n"

#: winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "WSL distribution '%s' not found"
msgstr "WSL-Distribution '%s' nicht gefunden"

#: winmain.c:6731
msgid "Duplicate option '%s'"
msgstr "Doppelte Option '%s'"

#: winmain.c:6739 winmain.c:6830
msgid "Unknown option '%s'"
msgstr "Unbekannte Option '%s'"

#: winmain.c:6741
msgid "Option '%s' requires an argument"
msgstr "Option '%s' benötigt einen Parameter"

#: winmain.c:6768
msgid "Syntax error in position argument '%s'"
msgstr "Formatfehler in Positionsparameter '%s'"

#: winmain.c:6779
msgid "Syntax error in size argument '%s'"
msgstr "Formatfehler in Größenparameter '%s'"

#: winmain.c:6939
msgid "Syntax error in geometry argument '%s'"
msgstr "Formatfehler in Größen-/Positionsparameter '%s'"

#: winmain.c:7038
msgid "Mintty could not detach from caller, starting anyway"
msgstr "Mintty konnte nicht vom Aufrufer abkoppeln, startet trotzdem"

#: winmain.c:7387
msgid "Using default title due to invalid characters in program name"
msgstr "Nehme Default-Titel wegen ungültiger Zeichen im Programmnamen"

#: winsearch.c:232
msgid "◀"
msgstr ""

#: winsearch.c:233
msgid "▶"
msgstr ""

#: winsearch.c:234
msgid "X"
msgstr ""

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:165
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "Durchschuss: %d, Fett: %s, Unterstrich: %s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:167
msgid "font"
msgstr ""

#. __ Options - Text: font properties: value affected by option
#: wintext.c:169
msgid "manual"
msgstr "manuell"

#: wintext.c:544
msgid "Font not found, using system substitute"
msgstr "Schrift nicht gefunden, nehme System-Ersatz"

#: wintext.c:559
msgid "Font has limited support for character ranges"
msgstr "Schrift hat beschränkte Unterstützung für Zeichensätze"

#: wintext.c:702
msgid "Font installation corrupt, using system substitute"
msgstr "Schriftinstallation defekt, nehme System-Ersatz"

#: wintext.c:756
msgid "Font does not support system locale"
msgstr "Schrift unterstützt System-Zeichensatz nicht"

#: appinfo.h:64
msgid "There is no warranty, to the extent permitted by law."
msgstr "Gewährleistung ist ausgeschlossen, soweit gesetzlich zulässig."

#. __ %s: WEBSITE (URL)
#: appinfo.h:69
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"Fehler oder Verbesserungswünsche bitte über das Verfolgungssystem auf der "
"Projektseite\n"
"%s\n"
"berichten. Siehe auch die Wiki-Seite dort für weitere Hinweise und "
"Danksagungen."
