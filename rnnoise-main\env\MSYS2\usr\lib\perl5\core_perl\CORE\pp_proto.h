/* -*- mode: C; buffer-read-only: t -*-
   !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
   This file is built by opcode.pl from its data.
   Any changes made here will be lost!
 */

PERL_CALLCONV PP(do_kv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_aassign) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_abs) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_accept) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_add) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_aeach) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_aelem) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_aelemfast) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_aelemfastlex_store) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_akeys) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_alarm) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_and) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_anoncode) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_anonconst) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_anonhash) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_anonlist) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_argcheck) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_argdefelem) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_argelem) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_aslice) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_atan2) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_av2arylen) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_avhvswitch) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_backtick) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_bind) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_binmode) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_bit_and) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_bit_or) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_bless) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_blessed) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_break) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_caller) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_catch) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ceil) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_chdir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_chop) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_chown) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_chr) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_chroot) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_clonecv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_close) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_closedir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_cmpchain_and) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_cmpchain_dup) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_complement) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_concat) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_cond_expr) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_const) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_continue) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_coreargs) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_crypt) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_dbmopen) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_dbstate) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_defined) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_delete) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_die) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_divide) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_each) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ehostent) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_emptyavhv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_enter) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_entereval) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_entergiven) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_enteriter) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_enterloop) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_entersub) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_entertry) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_entertrycatch) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_enterwhen) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_enterwrite) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_eof) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_eq) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_exec) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_exists) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_exit) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_fc) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_fileno) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_flip) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_flock) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_floor) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_flop) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_fork) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_formline) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ftis) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ftlink) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ftrowned) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ftrread) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_fttext) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_fttty) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ge) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_gelem) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_getc) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_getlogin) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_getpeername) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_getpgrp) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_getppid) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_getpriority) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ggrent) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ghostent) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_glob) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_gmtime) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_gnetent) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_goto) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_gprotoent) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_gpwent) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_grepstart) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_grepwhile) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_gservent) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_gt) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_gv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_gvsv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_helem) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_helemexistsor) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_hintseval) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_hslice) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_add) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_divide) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_eq) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_ge) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_gt) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_le) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_lt) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_modulo) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_multiply) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_ncmp) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_ne) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_negate) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_i_subtract) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_index) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_initfield) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_int) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_introcv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ioctl) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_is_bool) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_is_tainted) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_is_weak) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_isa) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_iter) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_join) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_kvaslice) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_kvhslice) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_last) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_lc) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_le) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leave) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leaveeval) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leavegiven) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leaveloop) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leavesub) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leavesublv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leavetry) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leavetrycatch) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leavewhen) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_leavewrite) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_left_shift) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_length) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_link) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_list) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_listen) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_lock) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_lslice) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_lt) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_lvavref) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_lvref) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_lvrefslice) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_mapwhile) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_match) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_method) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_method_named) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_method_redir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_method_redir_super) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_method_super) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_methstart) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_mkdir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_modulo) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_multiconcat) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_multideref) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_multiply) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_nbit_and) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_nbit_or) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ncmp) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ncomplement) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ne) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_negate) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_next) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_nextstate) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_not) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_null) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_oct) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_once) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_open) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_open_dir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_or) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ord) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_pack) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_padav) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_padcv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_padhv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_padrange) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_padsv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_padsv_store) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_pipe_op) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_poptry) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_pos) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_postdec) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_postinc) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_pow) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_predec) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_preinc) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_print) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_prototype) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_prtf) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_push) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_pushdefer) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_pushmark) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_qr) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_quotemeta) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_rand) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_range) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_rcatline) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_readdir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_readline) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_readlink) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_redo) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ref) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_refaddr) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_refassign) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_refgen) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_reftype) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_regcomp) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_regcreset) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_rename) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_repeat) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_require) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_reset) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_return) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_reverse) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_rewinddir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_right_shift) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_rmdir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_runcv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_rv2av) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_rv2cv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_rv2gv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_rv2sv) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sassign) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sbit_and) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sbit_or) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_schop) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_scmp) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_scomplement) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_seekdir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_select) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_semctl) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_semget) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_seq) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_setpgrp) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_setpriority) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_shift) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_shmwrite) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_shostent) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_shutdown) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sin) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sle) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sleep) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_smartmatch) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sne) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_socket) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sockpair) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sort) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_splice) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_split) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sprintf) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_srand) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_srefgen) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sselect) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ssockopt) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_stat) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_stringify) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_stub) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_study) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_subst) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_substcont) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_substr) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_subtract) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_syscall) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sysopen) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sysread) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_sysseek) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_system) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_syswrite) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_tell) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_telldir) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_tie) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_tied) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_time) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_tms) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_trans) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_truncate) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_uc) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_ucfirst) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_umask) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_undef) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_unpack) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_unshift) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_unstack) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_untie) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_unweaken) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_vec) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_wait) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_waitpid) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_wantarray) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_warn) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_weaken) __attribute__visibility__("hidden");
PERL_CALLCONV PP(pp_xor) __attribute__visibility__("hidden");
PERL_CALLCONV PP(unimplemented_op) __attribute__visibility__("hidden");

/* ex: set ro ft=c: */
