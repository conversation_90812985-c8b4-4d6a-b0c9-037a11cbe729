_getopt_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-l'|'--longoptions')
			COMPREPLY=( $(compgen -W "longopts" -- $cur) )
			return 0
			;;
		'-n'|'--name')
			COMPREPLY=( $(compgen -W "name" -- $cur) )
			return 0
			;;
		'-o'|'--options')
			COMPREPLY=( $(compgen -W "optstring" -- $cur) )
			return 0
			;;
		'-s'|'--shell')
			COMPREPLY=( $(compgen -W "sh bash csh tcsh" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--alternative --help --longoptions --name --options --quiet --quiet-output --shell --test --unquoted --version"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	return 0
}
complete -F _getopt_module getopt
