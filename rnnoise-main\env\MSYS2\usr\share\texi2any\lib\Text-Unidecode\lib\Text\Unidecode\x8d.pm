# Time-stamp: "Sat Jul 14 00:27:34 2001 by Automatic Bizooty (__blocks2pm.plx)"
$Text::Unidecode::Char[0x8d] = [
'<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', qq{[?] }, '<PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', qq{[?] }, '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ',
'<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ',
'<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ',
'<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON>u ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>g ',
'<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON>g ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON>e ', '<PERSON> ', '<PERSON>an ', '<PERSON> ',
'<PERSON>g ', '<PERSON> ', '<PERSON> ', '<PERSON>an ', 'Chi ', 'Xi ', 'She ', 'Nan ', 'Xiong ', 'Xi ', 'Cheng ', 'He ', 'Cheng ', 'Zhe ', 'Xia ', 'Tang ',
'Zou ', 'Zou ', 'Li ', 'Jiu ', 'Fu ', 'Zhao ', 'Gan ', 'Qi ', 'Shan ', 'Qiong ', 'Qin ', 'Xian ', 'Ci ', 'Jue ', 'Qin ', 'Chi ',
'Ci ', 'Chen ', 'Chen ', 'Die ', 'Ju ', 'Chao ', 'Di ', 'Se ', 'Zhan ', 'Zhu ', 'Yue ', 'Qu ', 'Jie ', 'Chi ', 'Chu ', 'Gua ',
'Xue ', 'Ci ', 'Tiao ', 'Duo ', 'Lie ', 'Gan ', 'Suo ', 'Cu ', 'Xi ', 'Zhao ', 'Su ', 'Yin ', 'Ju ', 'Jian ', 'Que ', 'Tang ',
'Chuo ', 'Cui ', 'Lu ', 'Qu ', 'Dang ', 'Qiu ', 'Zi ', 'Ti ', 'Qu ', 'Chi ', 'Huang ', 'Qiao ', 'Qiao ', 'Yao ', 'Zao ', 'Ti ',
qq{[?] }, 'Zan ', 'Zan ', 'Zu ', 'Pa ', 'Bao ', 'Ku ', 'Ke ', 'Dun ', 'Jue ', 'Fu ', 'Chen ', 'Jian ', 'Fang ', 'Zhi ', 'Sa ',
'Yue ', 'Pa ', 'Qi ', 'Yue ', 'Qiang ', 'Tuo ', 'Tai ', 'Yi ', 'Nian ', 'Ling ', 'Mei ', 'Ba ', 'Die ', 'Ku ', 'Tuo ', 'Jia ',
'Ci ', 'Pao ', 'Qia ', 'Zhu ', 'Ju ', 'Die ', 'Zhi ', 'Fu ', 'Pan ', 'Ju ', 'Shan ', 'Bo ', 'Ni ', 'Ju ', 'Li ', 'Gen ',
'Yi ', 'Ji ', 'Dai ', 'Xian ', 'Jiao ', 'Duo ', 'Zhu ', 'Zhuan ', 'Kua ', 'Zhuai ', 'Gui ', 'Qiong ', 'Kui ', 'Xiang ', 'Chi ', 'Lu ',
'Beng ', 'Zhi ', 'Jia ', 'Tiao ', 'Cai ', 'Jian ', 'Ta ', 'Qiao ', 'Bi ', 'Xian ', 'Duo ', 'Ji ', 'Ju ', 'Ji ', 'Shu ', 'Tu ',
];
1;
