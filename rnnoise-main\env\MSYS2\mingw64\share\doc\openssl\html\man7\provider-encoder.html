<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>provider-encoder</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Names-and-properties">Names and properties</a></li>
      <li><a href="#Subset-selections">Subset selections</a></li>
      <li><a href="#Context-functions">Context functions</a></li>
      <li><a href="#Import-functions">Import functions</a></li>
      <li><a href="#Encoding-functions">Encoding functions</a></li>
      <li><a href="#Encoder-operation-parameters">Encoder operation parameters</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>provider-encoder - The OSSL_ENCODER library &lt;-&gt; provider functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/core_dispatch.h&gt;

/*
 * None of these are actual functions, but are displayed like this for
 * the function signatures for functions that are offered as function
 * pointers in OSSL_DISPATCH arrays.
 */

/* Encoder parameter accessor and descriptor */
const OSSL_PARAM *OSSL_FUNC_encoder_gettable_params(void *provctx);
int OSSL_FUNC_encoder_get_params(OSSL_PARAM params[]);

/* Functions to construct / destruct / manipulate the encoder context */
void *OSSL_FUNC_encoder_newctx(void *provctx);
void OSSL_FUNC_encoder_freectx(void *ctx);
int OSSL_FUNC_encoder_set_ctx_params(void *ctx, const OSSL_PARAM params[]);
const OSSL_PARAM *OSSL_FUNC_encoder_settable_ctx_params(void *provctx);

/* Functions to check selection support */
int OSSL_FUNC_encoder_does_selection(void *provctx, int selection);

/* Functions to encode object data */
int OSSL_FUNC_encoder_encode(void *ctx, OSSL_CORE_BIO *out,
                             const void *obj_raw,
                             const OSSL_PARAM obj_abstract[],
                             int selection,
                             OSSL_PASSPHRASE_CALLBACK *cb,
                             void *cbarg);

/* Functions to import and free a temporary object to be encoded */
void *OSSL_FUNC_encoder_import_object(void *ctx, int selection,
                                      const OSSL_PARAM params[]);
void OSSL_FUNC_encoder_free_object(void *obj);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p><i>We use the wide term &quot;encode&quot; in this manual. This includes but is not limited to serialization.</i></p>

<p>The ENCODER operation is a generic method to encode a provider-native object (<i>obj_raw</i>) or an object abstraction (<i>object_abstract</i>, see <a href="../man7/provider-object.html">provider-object(7)</a>) into an encoded form, and write the result to the given OSSL_CORE_BIO. If the caller wants to get the encoded stream to memory, it should provide a <a href="../man3/BIO_s_mem.html">BIO_s_mem(3)</a> <b>BIO</b>.</p>

<p>The encoder doesn&#39;t need to know more about the <b>OSSL_CORE_BIO</b> pointer than being able to pass it to the appropriate BIO upcalls (see <a href="../man7/provider-base.html">&quot;Core functions&quot; in provider-base(7)</a>).</p>

<p>The ENCODER implementation may be part of a chain, where data is passed from one to the next. For example, there may be an implementation to encode an object to DER (that object is assumed to be provider-native and thereby passed via <i>obj_raw</i>), and another one that encodes DER to PEM (that one would receive the DER encoding via <i>obj_abstract</i>).</p>

<p>The encoding using the <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array form allows a encoder to be used for data that&#39;s been exported from another provider, and thereby allow them to exist independently of each other.</p>

<p>The encoding using a provider side object can only be safely used with provider data coming from the same provider, for example keys with the <a href="../man7/provider-keymgmt.html">KEYMGMT</a> provider.</p>

<p>All &quot;functions&quot; mentioned here are passed as function pointers between <i>libcrypto</i> and the provider in <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays via <a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a> arrays that are returned by the provider&#39;s provider_query_operation() function (see <a href="../man7/provider-base.html">&quot;Provider Functions&quot; in provider-base(7)</a>).</p>

<p>All these &quot;functions&quot; have a corresponding function type definition named <b>OSSL_FUNC_{name}_fn</b>, and a helper function to retrieve the function pointer from an <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> element named <b>OSSL_FUNC_{name}</b>. For example, the &quot;function&quot; OSSL_FUNC_encoder_encode() has these:</p>

<pre><code>typedef int
    (OSSL_FUNC_encoder_encode_fn)(void *ctx, OSSL_CORE_BIO *out,
                                  const void *obj_raw,
                                  const OSSL_PARAM obj_abstract[],
                                  int selection,
                                  OSSL_PASSPHRASE_CALLBACK *cb, void *cbarg);
static ossl_inline OSSL_FUNC_encoder_encode_fn
    OSSL_FUNC_encoder_encode(const OSSL_DISPATCH *opf);</code></pre>

<p><a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays are indexed by numbers that are provided as macros in <a href="../man7/openssl-core_dispatch.h.html">openssl-core_dispatch.h(7)</a>, as follows:</p>

<pre><code>OSSL_FUNC_encoder_get_params          OSSL_FUNC_ENCODER_GET_PARAMS
OSSL_FUNC_encoder_gettable_params     OSSL_FUNC_ENCODER_GETTABLE_PARAMS

OSSL_FUNC_encoder_newctx              OSSL_FUNC_ENCODER_NEWCTX
OSSL_FUNC_encoder_freectx             OSSL_FUNC_ENCODER_FREECTX
OSSL_FUNC_encoder_set_ctx_params      OSSL_FUNC_ENCODER_SET_CTX_PARAMS
OSSL_FUNC_encoder_settable_ctx_params OSSL_FUNC_ENCODER_SETTABLE_CTX_PARAMS

OSSL_FUNC_encoder_does_selection      OSSL_FUNC_ENCODER_DOES_SELECTION

OSSL_FUNC_encoder_encode              OSSL_FUNC_ENCODER_ENCODE

OSSL_FUNC_encoder_import_object       OSSL_FUNC_ENCODER_IMPORT_OBJECT
OSSL_FUNC_encoder_free_object         OSSL_FUNC_ENCODER_FREE_OBJECT</code></pre>

<h2 id="Names-and-properties">Names and properties</h2>

<p>The name of an implementation should match the type of object it handles. For example, an implementation that encodes an RSA key should be named &quot;RSA&quot;. Likewise, an implementation that further encodes DER should be named &quot;DER&quot;.</p>

<p>Properties, as defined in the <a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a> array element of each decoder implementation, can be used to further specify details about an implementation:</p>

<dl>

<dt id="output">output</dt>
<dd>

<p>This property is used to specify what type of output the implementation produces.</p>

<p>This property is <i>mandatory</i>.</p>

<p>OpenSSL providers recognize the following output types:</p>

<dl>

<dt id="text">text</dt>
<dd>

<p>An implementation with that output type outputs human readable text, making that implementation suitable for <code>-text</code> output in diverse <a href="../man1/openssl.html">openssl(1)</a> commands.</p>

</dd>
<dt id="pem">pem</dt>
<dd>

<p>An implementation with that output type outputs PEM formatted data.</p>

</dd>
<dt id="der">der</dt>
<dd>

<p>An implementation with that output type outputs DER formatted data.</p>

</dd>
<dt id="msblob">msblob</dt>
<dd>

<p>An implementation with that output type outputs MSBLOB formatted data.</p>

</dd>
<dt id="pvk">pvk</dt>
<dd>

<p>An implementation with that output type outputs PVK formatted data.</p>

</dd>
</dl>

</dd>
<dt id="structure">structure</dt>
<dd>

<p>This property is used to specify the structure that is used for the encoded object. An example could be <code>pkcs8</code>, to specify explicitly that an object (presumably an asymmetric key pair, in this case) will be wrapped in a PKCS#8 structure as part of the encoding.</p>

<p>This property is <i>optional</i>.</p>

</dd>
</dl>

<p>The possible values of both these properties is open ended. A provider may very well specify output types and structures that libcrypto doesn&#39;t know anything about.</p>

<h2 id="Subset-selections">Subset selections</h2>

<p>Sometimes, an object has more than one subset of data that is interesting to treat separately or together. It&#39;s possible to specify what subsets are to be encoded, with a set of bits <i>selection</i> that are passed in an <b>int</b>.</p>

<p>This set of bits depend entirely on what kind of provider-side object is passed. For example, those bits are assumed to be the same as those used with <a href="../man7/provider-keymgmt.html">provider-keymgmt(7)</a> (see <a href="../man7/provider-keymgmt.html">&quot;Key Objects&quot; in provider-keymgmt(7)</a>) when the object is an asymmetric keypair.</p>

<p>ENCODER implementations are free to regard the <i>selection</i> as a set of hints, but must do so with care. In the end, the output must make sense, and if there&#39;s a corresponding decoder, the resulting decoded object must match the original object that was encoded.</p>

<p>OSSL_FUNC_encoder_does_selection() should tell if a particular implementation supports any of the combinations given by <i>selection</i>.</p>

<h2 id="Context-functions">Context functions</h2>

<p>OSSL_FUNC_encoder_newctx() returns a context to be used with the rest of the functions.</p>

<p>OSSL_FUNC_encoder_freectx() frees the given <i>ctx</i>, if it was created by OSSL_FUNC_encoder_newctx().</p>

<p>OSSL_FUNC_encoder_set_ctx_params() sets context data according to parameters from <i>params</i> that it recognises. Unrecognised parameters should be ignored. Passing NULL for <i>params</i> should return true.</p>

<p>OSSL_FUNC_encoder_settable_ctx_params() returns a constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array describing the parameters that OSSL_FUNC_encoder_set_ctx_params() can handle.</p>

<p>See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for further details on the parameters structure used by OSSL_FUNC_encoder_set_ctx_params() and OSSL_FUNC_encoder_settable_ctx_params().</p>

<h2 id="Import-functions">Import functions</h2>

<p>A provider-native object may be associated with a foreign provider, and may therefore be unsuitable for direct use with a given ENCODER implementation. Provided that the foreign provider&#39;s implementation to handle the object has a function to export that object in <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array form, the ENCODER implementation should be able to import that array and create a suitable object to be passed to OSSL_FUNC_encoder_encode()&#39;s <i>obj_raw</i>.</p>

<p>OSSL_FUNC_encoder_import_object() should import the subset of <i>params</i> given with <i>selection</i> to create a provider-native object that can be passed as <i>obj_raw</i> to OSSL_FUNC_encoder_encode().</p>

<p>OSSL_FUNC_encoder_free_object() should free the object that was created with OSSL_FUNC_encoder_import_object().</p>

<h2 id="Encoding-functions">Encoding functions</h2>

<p>OSSL_FUNC_encoder_encode() should take a provider-native object (in <i>obj_raw</i>) or an object abstraction (in <i>obj_abstract</i>), and should output the object in encoded form to the <b>OSSL_CORE_BIO</b>. The <i>selection</i> bits, if relevant, should determine in greater detail what will be output. The encoding functions also take an <a href="../man3/OSSL_PASSPHRASE_CALLBACK.html">OSSL_PASSPHRASE_CALLBACK(3)</a> function pointer along with a pointer to application data <i>cbarg</i>, which should be used when a pass phrase prompt is needed.</p>

<h2 id="Encoder-operation-parameters">Encoder operation parameters</h2>

<p>Operation parameters currently recognised by built-in encoders are as follows:</p>

<dl>

<dt id="cipher-OSSL_ENCODER_PARAM_CIPHER-UTF8-string">&quot;cipher&quot; (<b>OSSL_ENCODER_PARAM_CIPHER</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>The name of the encryption cipher to be used when generating encrypted encoding. This is used when encoding private keys, as well as other objects that need protection.</p>

<p>If this name is invalid for the encoding implementation, the implementation should refuse to perform the encoding, i.e. OSSL_FUNC_encoder_encode_data() and OSSL_FUNC_encoder_encode_object() should return an error.</p>

</dd>
<dt id="properties-OSSL_ENCODER_PARAM_PROPERTIES-UTF8-string">&quot;properties&quot; (<b>OSSL_ENCODER_PARAM_PROPERTIES</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>The properties to be queried when trying to fetch the algorithm given with the &quot;cipher&quot; parameter. This must be given together with the &quot;cipher&quot; parameter to be considered valid.</p>

<p>The encoding implementation isn&#39;t obligated to use this value. However, it is recommended that implementations that do not handle property strings return an error on receiving this parameter unless its value NULL or the empty string.</p>

</dd>
<dt id="save-parameters-OSSL_ENCODER_PARAM_SAVE_PARAMETERS-integer">&quot;save-parameters&quot; (<b>OSSL_ENCODER_PARAM_SAVE_PARAMETERS</b>) &lt;integer&gt;</dt>
<dd>

<p>If set to 0 disables saving of key domain parameters. Default is 1. It currently has an effect only on DSA keys.</p>

</dd>
</dl>

<p>Parameters currently recognised by the built-in pass phrase callback:</p>

<dl>

<dt id="info-OSSL_PASSPHRASE_PARAM_INFO-UTF8-string">&quot;info&quot; (<b>OSSL_PASSPHRASE_PARAM_INFO</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>A string of information that will become part of the pass phrase prompt. This could be used to give the user information on what kind of object it&#39;s being prompted for.</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_FUNC_encoder_newctx() returns a pointer to a context, or NULL on failure.</p>

<p>OSSL_FUNC_encoder_set_ctx_params() returns 1, unless a recognised parameter was invalid or caused an error, for which 0 is returned.</p>

<p>OSSL_FUNC_encoder_settable_ctx_params() returns a pointer to an array of constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> elements.</p>

<p>OSSL_FUNC_encoder_does_selection() returns 1 if the encoder implementation supports any of the <i>selection</i> bits, otherwise 0.</p>

<p>OSSL_FUNC_encoder_encode() returns 1 on success, or 0 on failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider.html">provider(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The ENCODER interface was introduced in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2025 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


