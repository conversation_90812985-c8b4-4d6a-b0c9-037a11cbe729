.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_get_issuer" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_get_issuer \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_get_issuer(gnutls_x509_trust_list_t " list ", gnutls_x509_crt_t " cert ", gnutls_x509_crt_t * " issuer ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "gnutls_x509_crt_t cert" 12
is the certificate to find issuer for
.IP "gnutls_x509_crt_t * issuer" 12
Will hold the issuer if any. Should be treated as constant
unless \fBGNUTLS_TL_GET_COPY\fP is set in  \fIflags\fP .
.IP "unsigned int flags" 12
flags from \fBgnutls_trust_list_flags_t\fP (\fBGNUTLS_TL_GET_COPY\fP is applicable)
.SH "DESCRIPTION"
This function will find the issuer of the given certificate.
If the flag \fBGNUTLS_TL_GET_COPY\fP is specified a copy of the issuer
will be returned which must be freed using \fBgnutls_x509_crt_deinit()\fP.
In that case the provided  \fIissuer\fP must not be initialized.

Note that the flag \fBGNUTLS_TL_GET_COPY\fP is required for this function
to work with PKCS\fB11\fP trust lists in a thread\-safe way.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
