.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_set_verify_cert2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_set_verify_cert2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_session_set_verify_cert2(gnutls_session_t " session ", gnutls_typed_vdata_st * " data ", unsigned " elements ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.IP "gnutls_typed_vdata_st * data" 12
an array of typed data
.IP "unsigned elements" 12
the number of data elements
.IP "unsigned flags" 12
flags for certificate verification \-\- \fBgnutls_certificate_verify_flags\fP
.SH "DESCRIPTION"
This function instructs GnuTLS to verify the peer's certificate
using the provided typed data information. If the verification fails the handshake
will also fail with \fBGNUTLS_E_CERTIFICATE_VERIFICATION_ERROR\fP. In that
case the verification result can be obtained using \fBgnutls_session_get_verify_cert_status()\fP.

The acceptable typed data are the same as in \fBgnutls_certificate_verify_peers()\fP,
and once set must remain valid for the lifetime of the session. More precisely
they should be available during any subsequent handshakes.

If  \fIflags\fP is provided which contain a profile, this function should be
called after any session priority setting functions.
.SH "SINCE"
3.4.6
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
