<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>p11-kit: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="next" href="config.html" title="PKCS#11 Configuration">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<div class="book">
<div class="titlepage">
<div>
<div><table class="navigation" id="top" width="100%" cellpadding="2" cellspacing="0"><tr><th valign="middle"><p class="title">p11-kit</p></th></tr></table></div>
<div><p class="releaseinfo">for p11-kit 0.25.5</p></div>
</div>
<hr>
</div>
<div class="toc"><dl class="toc">
<dt><span class="chapter"><a href="config.html">PKCS#11 Configuration</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="config.html#config-introduction">Consistent configuration</a></span></dt>
<dt><span class="section"><a href="config-example.html">Example</a></span></dt>
<dt><span class="section"><a href="config-files.html">Configuration Files</a></span></dt>
</dl></dd>
<dt><span class="chapter"><a href="sharing.html">Sharing PKCS#11 modules</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="sharing.html#sharing-problem">Multiple consumers of PKCS#11 in a process</a></span></dt>
<dt><span class="section"><a href="sharing-managed.html">Managed modules</a></span></dt>
</dl></dd>
<dt><span class="chapter"><a href="sharing.html">Proxy Module</a></span></dt>
<dt><span class="chapter"><a href="remoting.html">Remoting / Forwarding</a></span></dt>
<dt><span class="chapter"><a href="trust-module.html">Trust Policy Module</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="trust-module.html#trust-files">Paths loaded by the Module</a></span></dt>
<dt><span class="section"><a href="trust-nss.html">Using the Trust Policy Module with NSS</a></span></dt>
<dt><span class="section"><a href="trust-glib-networking.html">Using the Trust Policy Module with glib-networking</a></span></dt>
<dt><span class="section"><a href="trust-disable.html">Disabling the Trust Policy Module</a></span></dt>
</dl></dd>
<dt><span class="chapter"><a href="tools.html">Manual Pages</a></span></dt>
<dd><dl>
<dt>
<span class="refentrytitle"><a href="p11-kit.html">p11-kit</a></span><span class="refpurpose"> — Tool for operating on configured PKCS#11 modules</span>
</dt>
<dt>
<span class="refentrytitle"><a href="pkcs11-conf.html">pkcs11.conf</a></span><span class="refpurpose"> — Configuration files for PKCS#11 modules</span>
</dt>
<dt>
<span class="refentrytitle"><a href="trust.html">trust</a></span><span class="refpurpose"> — Tool for operating on the trust policy store</span>
</dt>
</dl></dd>
<dt><span class="chapter"><a href="reference.html">API Reference</a></span></dt>
<dd><dl>
<dt>
<span class="refentrytitle"><a href="p11-kit-Modules.html">Modules</a></span><span class="refpurpose"> — Module loading and initializing</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-URIs.html">URIs</a></span><span class="refpurpose"> — Parsing and formatting PKCS#11 URIs</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-PIN-Callbacks.html">PIN Callbacks</a></span><span class="refpurpose"> — PIN Callbacks</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-Utilities.html">Utilities</a></span><span class="refpurpose"> — PKCS#11 utilities</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-Future.html">Future</a></span><span class="refpurpose"> — Future Unstable API</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-Deprecated.html">Deprecated</a></span><span class="refpurpose"> — Deprecated functions</span>
</dt>
<dt><span class="index"><a href="reference.html#api-index-full">API Index</a></span></dt>
<dt><span class="glossary"><a href="reference.html#annotation-glossary">Annotation Glossary</a></span></dt>
</dl></dd>
<dt><span class="chapter"><a href="devel.html">Building, Packaging, and Contributing to p11-kit</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="devel.html#devel-links">Helpful Resources</a></span></dt>
<dt><span class="section"><a href="devel-paths.html">Packaging PKCS#11 module configs</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="devel-paths.html#devel-paths-config">Path to place module configuration</a></span></dt>
<dt><span class="section"><a href="devel-paths.html#devel-paths-modules">Default path for modules with relative paths</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="devel-commands.html">Customizing installed commands</a></span></dt>
<dt><span class="section"><a href="devel-building.html">Compiling p11-kit from Source</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="devel-building.html#devel-building-unix">Building on UNIX</a></span></dt>
<dt><span class="section"><a href="devel-building.html#devel-building-dependencies">Optional Dependencies</a></span></dt>
<dt><span class="section"><a href="devel-building.html#devel-building-configure">Extra Configuration Options</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="devel-building-style.html">Coding Style</a></span></dt>
<dt><span class="section"><a href="devel-testing.html">Testing and Code Coverage</a></span></dt>
<dt><span class="section"><a href="devel-debugging.html">Debugging Tips</a></span></dt>
</dl></dd>
</dl></div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>