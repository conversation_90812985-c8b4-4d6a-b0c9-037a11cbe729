<PERSON>, as well as being the creator of the Python language, is the
original creator of IDLE.  Other contributors prior to Version 0.8 include
<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>.

Until Python 2.3, IDLE's development was carried out in the SF IDLEfork project. The
objective was to develop a version of IDLE which had an execution environment
which could be initialized prior to each run of user code.
IDLefork was merged into the Python code base in 2003.

The IDLEfork project was initiated by <PERSON>, with some help from <PERSON> and <PERSON>.  <PERSON> wrote the first version of the RPC
code and designed a fast turn-around environment for VPython.  <PERSON> developed
the RPC code and Remote Debugger currently integrated in IDLE.  <PERSON>
contributed considerable time testing and suggesting improvements.

Besides <PERSON> and <PERSON>, the main developers who were active on IDLEfork
are <PERSON>, who implemented the configuration GUI, the new
configuration system, and the About dialog, and <PERSON>, who completed
the integration of the RPC and remote debugger, implemented the threaded
subprocess, and made a number of usability enhancements.

Other contributors include <PERSON>, <PERSON> (Mac integration),
<PERSON> (code check and clean-up), <PERSON> (Mac integration),
<PERSON><PERSON> (Code Context, Call Tips, many other patches), and <PERSON><PERSON> (RPC
integration, debugger integration and persistent breakpoints).

<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,
<PERSON>, <PERSON> v<PERSON>, <PERSON>end<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> <PERSON>b,
<PERSON> <PERSON>, <PERSON>, <PERSON> <PERSON>e, and <PERSON><PERSON>le have submitted useful
patches.  Thanks, guys!

<PERSON> contributors since 2005:

- 2005: <PERSON>l <PERSON>at
- 2010: <PERSON> <PERSON> <PERSON><PERSON> (current maintainer)
- 2013: <PERSON> <PERSON>wy
- 2014: Sai<PERSON>hav Heblikar
- 2015: <PERSON><PERSON>
- 2017: <PERSON> <PERSON>, <PERSON> <PERSON><PERSON>, and <PERSON>hiy <PERSON>orcha<PERSON>

For additional details refer to NE<PERSON>.txt and Changelog.

If we missed you, feel free to submit a PR with a summary of
contributions (for instance, at least 5 merged PRs).



