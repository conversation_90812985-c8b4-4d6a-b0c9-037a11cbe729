.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_export_ecc_x962" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_export_ecc_x962 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_export_ecc_x962(gnutls_pubkey_t " key ", gnutls_datum_t * " parameters ", gnutls_datum_t * " ecpoint ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
Holds the public key
.IP "gnutls_datum_t * parameters" 12
DER encoding of an ANSI X9.62 parameters
.IP "gnutls_datum_t * ecpoint" 12
DER encoding of ANSI X9.62 ECPoint
.SH "DESCRIPTION"
This function will export the ECC public key's parameters found in
the given certificate.  The new parameters will be allocated using
\fBgnutls_malloc()\fP and will be stored in the appropriate datum.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
