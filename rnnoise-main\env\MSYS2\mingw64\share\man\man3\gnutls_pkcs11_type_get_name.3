.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_type_get_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_type_get_name \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "const char * gnutls_pkcs11_type_get_name(gnutls_pkcs11_obj_type_t " type ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_type_t type" 12
Holds the PKCS 11 object type, a \fBgnutls_pkcs11_obj_type_t\fP.
.SH "DESCRIPTION"
This function will return a human readable description of the
PKCS11 object type  \fIobj\fP .  It will return "Unknown" for unknown
types.
.SH "RETURNS"
human readable string labeling the PKCS11 object type
 \fItype\fP .
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
