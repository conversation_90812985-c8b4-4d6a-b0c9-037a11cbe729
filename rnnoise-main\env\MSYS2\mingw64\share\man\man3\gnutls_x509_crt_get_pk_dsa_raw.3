.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_pk_dsa_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_pk_dsa_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_pk_dsa_raw(gnutls_x509_crt_t " crt ", gnutls_datum_t * " p ", gnutls_datum_t * " q ", gnutls_datum_t * " g ", gnutls_datum_t * " y ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
Holds the certificate
.IP "gnutls_datum_t * p" 12
will hold the p
.IP "gnutls_datum_t * q" 12
will hold the q
.IP "gnutls_datum_t * g" 12
will hold the g
.IP "gnutls_datum_t * y" 12
will hold the y
.SH "DESCRIPTION"
This function will export the DSA public key's parameters found in
the given certificate.  The new parameters will be allocated using
\fBgnutls_malloc()\fP and will be stored in the appropriate datum.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
