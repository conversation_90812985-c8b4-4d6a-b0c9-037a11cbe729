global tracing

function basename:string(path:string)
{
    last_token = token = tokenize(path, "/");
    while (token != "") {
        last_token = token;
        token = tokenize("", "/");
    }
    return last_token;
}

probe process.mark("function__entry")
{
    funcname = user_string($arg2);

    if (funcname == "start") {
        tracing = 1;
    }
}

probe process.mark("function__entry"), process.mark("function__return")
{
    filename = user_string($arg1);
    funcname = user_string($arg2);
    lineno = $arg3;

    if (tracing) {
        printf("%d\t%s:%s:%s:%d\n", gettimeofday_us(), $$name,
               basename(filename), funcname, lineno);
    }
}

probe process.mark("function__return")
{
    funcname = user_string($arg2);

    if (funcname == "start") {
        tracing = 0;
    }
}
