.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_openpgp_send_cert" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_openpgp_send_cert \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_openpgp_send_cert(gnutls_session_t " session ", gnutls_openpgp_crt_status_t " status ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.IP "gnutls_openpgp_crt_status_t status" 12
is ignored
.SH "DESCRIPTION"
This function is no\-op.
.SH "RETURNS"
\fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
