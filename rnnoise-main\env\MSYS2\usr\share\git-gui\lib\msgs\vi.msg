set ::msgcat::header "Project-Id-Version: git-gui 0.19.0\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2014-06-27 07:48+0700\nLast-Translator: Tr\u1ea7n Ng\u1ecdc Qu\u00e2n <<EMAIL>>\nLanguage-Team: Vietnamese <<EMAIL>>\nLanguage: vi\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=1; plural=0;\nX-Poedit-Language: Vietnamese\nX-Poedit-Country: VIET NAM\nX-Poedit-SourceCharset: utf-8\nX-Poedit-Basepath: ../\n"
::msgcat::mcset vi "Invalid font specified in %s:" "Ph\u00f4ng ch\u1eef kh\u00f4ng h\u1ee3p l\u1ec7 \u0111\u01b0\u1ee3c \u0111\u1eb7c t\u1ea3 trong %s:"
::msgcat::mcset vi "Main Font" "Ph\u00f4ng ch\u1eef ch\u00ednh"
::msgcat::mcset vi "Diff/Console Font" "Ph\u00f4ng ch\u1eef cho B\u1ea3ng \u0111i\u1ec1u khi\u1ec3n hay Diff"
::msgcat::mcset vi "git-gui: fatal error" "git-gui: l\u1ed7i nghi\u00eam tr\u1ecdng"
::msgcat::mcset vi "Cannot find git in PATH." "Kh\u00f4ng t\u00ecm th\u1ea5y git trong bi\u1ebfn PATH."
::msgcat::mcset vi "Cannot parse Git version string:" "Kh\u00f4ng th\u1ec3 ph\u00e2n t\u00edch chu\u1ed7i phi\u00ean b\u1ea3n Git:"
::msgcat::mcset vi "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "Kh\u00f4ng th\u1ec3 nh\u1eadn ra phi\u00ean b\u1ea3n c\u1ee7a Git.\n\n%s n\u00f3i \u0111\u00e2y l\u00e0 phi\u00ean b\u1ea3n '%s'.\n\n%s y\u00eau c\u1ea7u Git phi\u00ean b\u1ea3n t\u1eeb 1.5.0 hay m\u1edbi h\u01a1n.\n\nC\u1ecdi '%s' c\u00f3 phi\u00ean b\u1ea3n l\u00e0 1.5.0?\n"
::msgcat::mcset vi "Git directory not found:" "Kh\u00f4ng t\u00ecm th\u1ea5y th\u01b0 m\u1ee5c git:"
::msgcat::mcset vi "Cannot move to top of working directory:" "Kh\u00f4ng th\u1ec3 di chuy\u1ec3n \u0111\u1ebfn \u0111\u1ec9nh c\u1ee7a th\u01b0 m\u1ee5c l\u00e0m vi\u1ec7c:"
::msgcat::mcset vi "Cannot use bare repository:" "Kh\u00f4ng th\u1ec3 d\u00f9ng kho tr\u1ea7n:"
::msgcat::mcset vi "No working directory" "Kh\u00f4ng c\u00f3 th\u01b0 m\u1ee5c l\u00e0m vi\u1ec7c"
::msgcat::mcset vi "Refreshing file status..." "C\u1eadp nh\u1eadt l\u1ea1i tr\u1ea1ng th\u00e1i t\u1eadp tin..."
::msgcat::mcset vi "Scanning for modified files ..." "\u0110ang qu\u00e9t \u0111\u0129a t\u00ecm t\u1eadp tin thay \u0111\u1ed5i..."
::msgcat::mcset vi "Calling prepare-commit-msg hook..." "\u0110ang g\u1ecdi m\u00f3c prepare-commit-msg..."
::msgcat::mcset vi "Commit declined by prepare-commit-msg hook." "L\u1ea7n chuy\u1ec3n giao b\u1ecb ch\u1ed1i t\u1eeb do m\u00f3c prepare-commit-msg."
::msgcat::mcset vi "Ready." "S\u1eb5n s\u00e0ng."
::msgcat::mcset vi "Displaying only %s of %s files." "Ch\u1ec9 hi\u1ec3n th\u1ecb %s trong s\u1ed1 %s t\u1eadp tin."
::msgcat::mcset vi "Unmodified" "Kh\u00f4ng thay \u0111\u1ed5i g\u00ec"
::msgcat::mcset vi "Modified, not staged" "\u0110\u00e3 s\u1eeda nh\u01b0ng ch\u01b0a \u0111\u00e1nh d\u1ea5u \u0111\u1ec3 chuy\u1ec3n giao"
::msgcat::mcset vi "Staged for commit" "\u0110\u00e1nh d\u1ea5u \u0111\u1ec3 chuy\u1ec3n giao"
::msgcat::mcset vi "Portions staged for commit" "C\u00e1c ph\u1ea7n \u0111\u01b0\u1ee3c \u0111\u00e1nh d\u1ea5u l\u00e0 c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Staged for commit, missing" "\u0110\u00e3 \u0111\u00e1nh d\u1ea5u l\u00e0 c\u1ea7n chuy\u1ec3n giao, thi\u1ebfu"
::msgcat::mcset vi "File type changed, not staged" "\u0110\u00e3 \u0111\u1ed5i ki\u1ec3u t\u1eadp tin nh\u01b0ng ch\u01b0a \u0111\u01b0\u1ee3c \u0111\u00e1nh d\u1ea5u c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "File type changed, old type staged for commit" "\u0110\u00e3 \u0111\u1ed5i ki\u1ec3u t\u1eadp tin, ki\u1ec3u c\u0169 \u0111\u00e3 \u0111\u01b0\u1ee3c \u0111\u00e1nh d\u1ea5u c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "File type changed, staged" "\u0110\u00e3 \u0111\u1ed5i ki\u1ec3u t\u1eadp tin, \u0111\u00e3 \u0111\u01b0\u1ee3c \u0111\u00e1nh d\u1ea5u c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "File type change staged, modification not staged" "Thay \u0111\u1ed5i ki\u1ec3u t\u1eadp tin \u0111\u00e3 \u0111\u01b0\u1ee3c \u0111\u00e1nh d\u1ea5u c\u1ea7n chuy\u1ec3n giao, nh\u01b0ng c\u00e1c thay \u0111\u1ed5i th\u00ec ch\u01b0a"
::msgcat::mcset vi "File type change staged, file missing" "Thay \u0111\u1ed5i ki\u1ec3u t\u1eadp tin \u0111\u00e3 \u0111\u01b0\u1ee3c \u0111\u00e1nh d\u1ea5u c\u1ea7n chuy\u1ec3n giao, t\u1eadp tin b\u1ecb thi\u1ebfu"
::msgcat::mcset vi "Untracked, not staged" "Ch\u01b0a \u0111\u01b0\u1ee3c theo d\u00f5i, ch\u01b0a \u0111\u00e1nh d\u1ea5u l\u00e0 c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Missing" "Thi\u1ebfu"
::msgcat::mcset vi "Staged for removal" "\u0110\u00e3 \u0111\u00e1nh d\u1ea5u l\u00e0 c\u1ea7n g\u1ee1 b\u1ecf"
::msgcat::mcset vi "Staged for removal, still present" "\u0110\u00e3 \u0111\u00e1nh d\u1ea5u l\u00e0 c\u1ea7n g\u1ee1 b\u1ecf, nh\u01b0ng v\u1eabn hi\u1ec7n di\u1ec7n"
::msgcat::mcset vi "Requires merge resolution" "C\u00e1c y\u00eau c\u1ea7u ph\u00e2n gi\u1ea3i h\u00f2a tr\u1ed9n"
::msgcat::mcset vi "Starting gitk... please wait..." "\u0110ang kh\u1edfi \u0111\u1ed9ng gitk... vui l\u00f2ng ch\u1edd..."
::msgcat::mcset vi "Couldn't find gitk in PATH" "Kh\u00f4ng th\u1ec3 t\u00ecm th\u1ea5y gitk trong PATH"
::msgcat::mcset vi "Couldn't find git gui in PATH" "Kh\u00f4ng th\u1ec3 t\u00ecm th\u1ea5y git gui trong PATH"
::msgcat::mcset vi "Repository" "Kho"
::msgcat::mcset vi "Edit" "Ch\u1ec9nh s\u1eeda"
::msgcat::mcset vi "Branch" "Nh\u00e1nh"
::msgcat::mcset vi "Commit@@noun" "Chuy\u1ec3n giao@@noun"
::msgcat::mcset vi "Merge" "Tr\u1ed9n"
::msgcat::mcset vi "Remote" "M\u00e1y ch\u1ee7"
::msgcat::mcset vi "Tools" "C\u00f4ng c\u1ee5"
::msgcat::mcset vi "Explore Working Copy" "Qu\u00e9t d\u00f2 th\u01b0 m\u1ee5c l\u00e0m vi\u1ec7c"
::msgcat::mcset vi "Git Bash" "Git Bash"
::msgcat::mcset vi "Browse Current Branch's Files" "Duy\u1ec7t c\u00e1c T\u1eadp tin \u1edf nh\u00e1nh hi\u1ec7n nay"
::msgcat::mcset vi "Browse Branch Files..." "Duy\u1ec7t c\u00e1c t\u1eadp tin nh\u00e1nh..."
::msgcat::mcset vi "Visualize Current Branch's History" "Hi\u1ec3n th\u1ecb tr\u1ef1c quan l\u1ecbch s\u1eed nh\u00e1nh hi\u1ec7n nay"
::msgcat::mcset vi "Visualize All Branch History" "Hi\u1ec3n th\u1ecb tr\u1ef1c quan l\u1ecbch s\u1eed m\u1ecdi nh\u00e1nh"
::msgcat::mcset vi "Browse %s's Files" "Duy\u1ec7t t\u1eadp tin c\u1ee7a %s..."
::msgcat::mcset vi "Visualize %s's History" "Duy\u1ec7t l\u1ecbch s\u1eed c\u1ee7a %s tr\u1ef1c quan"
::msgcat::mcset vi "Database Statistics" "Th\u1ed1ng k\u00ea c\u01a1 s\u1edf d\u1eef li\u1ec7u"
::msgcat::mcset vi "Compress Database" "N\u00e9n c\u01a1 s\u1edf d\u1eef li\u1ec7u"
::msgcat::mcset vi "Verify Database" "Th\u1ea9m tra c\u01a1 s\u1edf d\u1eef li\u1ec7u"
::msgcat::mcset vi "Create Desktop Icon" "T\u1ea1o l\u1ed1i t\u1eaft \u1edf m\u00e0n h\u00ecnh n\u1ec1n"
::msgcat::mcset vi "Quit" "Tho\u00e1t"
::msgcat::mcset vi "Undo" "H\u1ee7y l\u1ec7nh v\u1eeba r\u1ed3i"
::msgcat::mcset vi "Redo" "L\u00e0m l\u1ea1i"
::msgcat::mcset vi "Cut" "C\u1eaft"
::msgcat::mcset vi "Copy" "Ch\u00e9p"
::msgcat::mcset vi "Paste" "D\u00e1n"
::msgcat::mcset vi "Delete" "X\u00f3a b\u1ecf"
::msgcat::mcset vi "Select All" "Ch\u1ecdn t\u1ea5t c\u1ea3"
::msgcat::mcset vi "Create..." "T\u1ea1o..."
::msgcat::mcset vi "Checkout..." "L\u1ea5y ra..."
::msgcat::mcset vi "Rename..." "\u0110\u1ed5i t\u00ean..."
::msgcat::mcset vi "Delete..." "X\u00f3a..."
::msgcat::mcset vi "Reset..." "\u0110\u1eb7t l\u1ea1i.."
::msgcat::mcset vi "Done" "Xong"
::msgcat::mcset vi "Commit@@verb" "Chuy\u1ec3n giao@@verb"
::msgcat::mcset vi "New Commit" "L\u1ea7n chuy\u1ec3n giao m\u1edbi"
::msgcat::mcset vi "Amend Last Commit" "Tu b\u1ed5 l\u1ea7n chuy\u1ec3n giao cu\u1ed1i"
::msgcat::mcset vi "Rescan" "Qu\u00e9t l\u1ea1i"
::msgcat::mcset vi "Stage To Commit" "\u0110\u01b0a l\u00ean b\u1ec7 ph\u00f3ng \u0111\u1ec3 chuy\u1ec3n giao"
::msgcat::mcset vi "Stage Changed Files To Commit" "\u0110\u00e1nh d\u1ea5u c\u00e1c t\u1eadp tin \u0111\u00e3 thay \u0111\u1ed5i c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Unstage From Commit" "\u0110\u01b0a ra kh\u1ecfi b\u1ec7 ph\u00f3ng \u0111\u1ec3 kh\u00f4ng chuy\u1ec3n giao"
::msgcat::mcset vi "Revert Changes" "Ho\u00e0n nguy\u00ean c\u00e1c thay \u0111\u1ed5i"
::msgcat::mcset vi "Show Less Context" "Hi\u1ec7n \u00edt n\u1ed9i dung h\u01a1n"
::msgcat::mcset vi "Show More Context" "Hi\u1ec7n chi ti\u1ebft h\u01a1n"
::msgcat::mcset vi "Sign Off" "K\u00fd t\u00ean"
::msgcat::mcset vi "Local Merge..." "Tr\u1ed9n n\u1ed9i b\u1ed9..."
::msgcat::mcset vi "Abort Merge..." "H\u1ee7y b\u1ecf h\u00f2a tr\u1ed9n..."
::msgcat::mcset vi "Add..." "Th\u00eam..."
::msgcat::mcset vi "Push..." "\u0110\u1ea9y l\u00ean..."
::msgcat::mcset vi "Delete Branch..." "Xo\u00e1 nh\u00e1nh..."
::msgcat::mcset vi "Options..." "T\u00f9y ch\u1ecdn..."
::msgcat::mcset vi "Remove..." "G\u1ee1 b\u1ecf..."
::msgcat::mcset vi "Help" "Tr\u1ee3 gi\u00fap"
::msgcat::mcset vi "About %s" "Gi\u1edbi thi\u1ec7u v\u1ec1 %s"
::msgcat::mcset vi "Online Documentation" "\u0110\u1ecdc t\u00e0i li\u1ec7u tr\u1ef1c tuy\u1ebfn"
::msgcat::mcset vi "Show SSH Key" "Hi\u1ec7n kho\u00e1 SSH"
::msgcat::mcset vi "Usage" "C\u00e1ch d\u00f9ng"
::msgcat::mcset vi "Error" "L\u1ed7i"
::msgcat::mcset vi "fatal: cannot stat path %s: No such file or directory" "l\u1ed7i nghi\u00eam tr\u1ecdng: kh\u00f4ng th\u1ec3 l\u1ea5y th\u00f4ng tin v\u1ec1 \u0111\u01b0\u1eddng d\u1eabn %s: Kh\u00f4ng c\u00f3 t\u1eadp tin ho\u1eb7c th\u01b0 m\u1ee5c nh\u01b0 v\u1eady"
::msgcat::mcset vi "Current Branch:" "Nh\u00e1nh hi\u1ec7n h\u00e0nh:"
::msgcat::mcset vi "Staged Changes (Will Commit)" "\u0110\u00e1nh d\u1ea5u c\u00e1c thay \u0111\u1ed5i (S\u1ebd chuy\u1ec3n giao)"
::msgcat::mcset vi "Unstaged Changes" "B\u1ecf ra kh\u1ecfi b\u1ec7 ph\u00f3ng c\u00e1c thay \u0111\u1ed5i"
::msgcat::mcset vi "Stage Changed" "\u0110\u1eb7t l\u00ean b\u1ec7 ph\u00f3ng c\u00e1c thay \u0111\u1ed5i"
::msgcat::mcset vi "Push" "\u0110\u1ea9y l\u00ean"
::msgcat::mcset vi "Initial Commit Message:" "Ph\u1ea7n ch\u00fa th\u00edch cho l\u1ea7n chuy\u1ec3n giao kh\u1edfi t\u1ea1o:"
::msgcat::mcset vi "Amended Commit Message:" "Ph\u1ea7n ch\u00fa gi\u1ea3i cho l\u1ea7n chuy\u1ec3n giao tu b\u1ed5:"
::msgcat::mcset vi "Amended Initial Commit Message:" "Ph\u1ea7n ch\u00fa gi\u1ea3i cho l\u1ea7n chuy\u1ec3n giao tu b\u1ed5 l\u1ea7n kh\u1edfi t\u1ea1o:"
::msgcat::mcset vi "Amended Merge Commit Message:" "Ph\u1ea7n ch\u00fa gi\u1ea3i cho l\u1ea7n chuy\u1ec3n giao tu b\u1ed5 l\u1ea7n h\u00f2a tr\u1ed9n"
::msgcat::mcset vi "Merge Commit Message:" "Ghi ch\u00fa c\u1ee7a l\u1ea7n chuy\u1ec3n giao h\u00f2a tr\u1ed9n:"
::msgcat::mcset vi "Commit Message:" "Ch\u00fa th\u00edch c\u1ee7a l\u1ea7n chuy\u1ec3n giao:"
::msgcat::mcset vi "Copy All" "Ch\u00e9p t\u1ea5t c\u1ea3"
::msgcat::mcset vi "File:" "T\u1eadp tin:"
::msgcat::mcset vi "Refresh" "L\u00e0m t\u01b0\u01a1i l\u1ea1i"
::msgcat::mcset vi "Decrease Font Size" "Gi\u1ea3m k\u00edch c\u1ee1 ph\u00f4ng"
::msgcat::mcset vi "Increase Font Size" "T\u0103ng k\u00edch c\u1ee1 ph\u00f4ng"
::msgcat::mcset vi "Encoding" "B\u1ea3ng m\u00e3"
::msgcat::mcset vi "Apply/Reverse Hunk" "\u00c1p d\u1ee5ng hay \u0111\u1ea3o ng\u01b0\u1ee3c c\u1ea3 kh\u1ed1i"
::msgcat::mcset vi "Apply/Reverse Line" "\u00c1p d\u1ee5ng hay \u0111\u1ea3o ng\u01b0\u1ee3c d\u00f2ng"
::msgcat::mcset vi "Run Merge Tool" "Ch\u1ea1y c\u00f4ng c\u1ee5 h\u00f2a tr\u1ed9n"
::msgcat::mcset vi "Use Remote Version" "D\u00f9ng phi\u00ean b\u1ea3n \u1edf m\u00e1y ch\u1ee7"
::msgcat::mcset vi "Use Local Version" "D\u00f9ng phi\u00ean b\u1ea3n \u1edf m\u00e1y n\u1ed9i b\u1ed9"
::msgcat::mcset vi "Revert To Base" "Tr\u1edf l\u1ea1i c\u01a1 b\u1ea3n"
::msgcat::mcset vi "Visualize These Changes In The Submodule" "Hi\u1ec3n th\u1ecb tr\u1ef1c quan c\u00e1c thay \u0111\u1ed5i trong m\u00f4-\u0111un con"
::msgcat::mcset vi "Visualize Current Branch History In The Submodule" "Hi\u1ec3n th\u1ecb tr\u1ef1c quan l\u1ecbch s\u1eed nh\u00e1nh hi\u1ec7n t\u1ea1i trong m\u00f4-\u0111un con"
::msgcat::mcset vi "Visualize All Branch History In The Submodule" "Hi\u1ec3n th\u1ecb tr\u1ef1c quan l\u1ecbch s\u1eed m\u1ecdi nh\u00e1nh trong m\u00f4-\u0111un con"
::msgcat::mcset vi "Start git gui In The Submodule" "Kh\u1edfi ch\u1ea1y git gui trong m\u00f4-\u0111un-con"
::msgcat::mcset vi "Unstage Hunk From Commit" "B\u1ecf \u0111\u00e1nh d\u1ea5u \u0111o\u1ea1n c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Unstage Lines From Commit" "B\u1ecf \u0111\u00e1nh d\u1ea5u c\u00e1c d\u00f2ng c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Unstage Line From Commit" "B\u1ecf \u0111\u00e1nh d\u1ea5u d\u00f2ng c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Stage Hunk For Commit" "\u0110\u00e1nh d\u1ea5u \u0111o\u1ea1n c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Stage Lines For Commit" "\u0110\u00e1nh d\u1ea5u c\u00e1c d\u00f2ng c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Stage Line For Commit" "\u0110\u00e1nh d\u1ea5u d\u00f2ng c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Initializing..." "\u0110ang kh\u1edfi t\u1ea1o..."
::msgcat::mcset vi "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "G\u1ea7n nh\u01b0 ch\u1eafc ch\u1eafn l\u00e0 m\u00f4i tr\u01b0\u1eddng t\u1ed3n t\u1ea1i.\n\nC\u00e1c bi\u1ebfn m\u00f4i tr\u01b0\u1eddng sau \u0111\u00e2y c\u00f3 l\u1ebd s\u1ebd b\u1ecb b\u1ecf qua b\u1edfi c\u00e1c ti\u1ebfn tr\u00ecnh con git\nch\u1ea1y b\u1edfi %s:\n\n"
::msgcat::mcset vi "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\nC\u00e1i n\u00e0y c\u00f3 nguy\u00ean nh\u00e2n b\u1edfi m\u1ed9t l\u1ed7i ph\u00e1t ra t\u1eeb\nTcl ph\u00e2n ph\u1ed1i b\u1edfi Cygwin."
::msgcat::mcset vi "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\nThay th\u1ebf t\u1ed1t cho %s\nl\u00e0 thay th\u1ebf c\u00e1c gi\u00e1 tr\u1ecb c\u00e0i \u0111\u1eb7t cho user.name v\u00e0\nuser.email th\u00e0nh t\u1eadp tin c\u00e1 nh\u00e2n c\u1ee7a b\u1ea1n\n~/.gitconfig.\n"
::msgcat::mcset vi "git-gui - a graphical user interface for Git." "git-gui - c\u00f4ng c\u1ee5 \u0111\u1ed3 h\u1ecda d\u00e0nh cho Git."
::msgcat::mcset vi "File Viewer" "B\u1ed9 Xem T\u1eadp Tin"
::msgcat::mcset vi "Commit:" "L\u1ea7n chuy\u1ec3n giao:"
::msgcat::mcset vi "Copy Commit" "Ch\u00e9p l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Find Text..." "T\u00ecm ch\u1eef..."
::msgcat::mcset vi "Goto Line..." "Nh\u1ea3y \u0111\u1ebfn d\u00f2ng..."
::msgcat::mcset vi "Do Full Copy Detection" "Th\u1ef1c hi\u1ec7n d\u00f2 t\u00ecm ch\u00e9p to\u00e0n b\u1ed9"
::msgcat::mcset vi "Show History Context" "Hi\u1ec3n th\u1ecb n\u1ed9i dung c\u1ee7a l\u1ecbch s\u1eed"
::msgcat::mcset vi "Blame Parent Commit" "Xem c\u00f4ng tr\u1ea1ng c\u1ee7a l\u1ea7n chuy\u1ec3n giao cha m\u1eb9"
::msgcat::mcset vi "Reading %s..." "\u0110ang \u0111\u1ecdc %s..."
::msgcat::mcset vi "Loading copy/move tracking annotations..." "\u0110ang t\u1ea3i ph\u1ea7n ch\u00fa gi\u1ea3i theo d\u00f5i ch\u00e9p/chuy\u1ec3n..."
::msgcat::mcset vi "lines annotated" "d\u00f2ng ch\u00fa gi\u1ea3i"
::msgcat::mcset vi "Loading original location annotations..." "\u0110ang t\u1ea3i c\u00e1c ch\u00fa gi\u1ea3i v\u1ecb tr\u00ed nguy\u00ean g\u1ed1c..."
::msgcat::mcset vi "Annotation complete." "Ch\u00fa gi\u1ea3i ho\u00e0n t\u1ea5t."
::msgcat::mcset vi "Busy" "B\u1eadn"
::msgcat::mcset vi "Annotation process is already running." "Ti\u1ebfn tr\u00ecnh ch\u00fa gi\u1ea3i \u0111ang di\u1ec5n ra."
::msgcat::mcset vi "Running thorough copy detection..." "\u0110ang ch\u1ea1y d\u00f2 t\u00ecm sao ch\u00e9p to\u00e0n di\u1ec7n..."
::msgcat::mcset vi "Loading annotation..." "\u0110ang t\u1ea3i ph\u1ea7n ch\u00fa gi\u1ea3i..."
::msgcat::mcset vi "Author:" "T\u00e1c gi\u1ea3:"
::msgcat::mcset vi "Committer:" "Ng\u01b0\u1eddi chuy\u1ec3n giao:"
::msgcat::mcset vi "Original File:" "T\u1eadp tin g\u1ed1c:"
::msgcat::mcset vi "Cannot find HEAD commit:" "Kh\u00f4ng th\u1ec3 t\u00ecm th\u1ea5y HEAD c\u1ee7a l\u1ea7n chuy\u1ec3n giao:"
::msgcat::mcset vi "Cannot find parent commit:" "Kh\u00f4ng th\u1ec3 t\u00ecm th\u1ea5y l\u1ea7n chuy\u1ec3n giao m\u1eb9:"
::msgcat::mcset vi "Unable to display parent" "Kh\u00f4ng th\u1ec3 hi\u1ec3n th\u1ecb cha m\u1eb9"
::msgcat::mcset vi "Error loading diff:" "G\u1eb7p l\u1ed7i khi t\u1ea3i diff:"
::msgcat::mcset vi "Originally By:" "Nguy\u00ean g\u1ed1c b\u1edfi:"
::msgcat::mcset vi "In File:" "Trong t\u1eadp tin:"
::msgcat::mcset vi "Copied Or Moved Here By:" "\u0110\u00e3 ch\u00e9p ho\u1eb7c Di chuy\u1ec3n \u0111\u1ebfn \u0111\u00e2y b\u1edfi:"
::msgcat::mcset vi "Checkout Branch" "L\u1ea5y ra nh\u00e1nh"
::msgcat::mcset vi "Checkout" "L\u1ea5y ra"
::msgcat::mcset vi "Cancel" "Th\u00f4i"
::msgcat::mcset vi "Revision" "\u0110i\u1ec3m s\u1eeda \u0111\u1ed5i"
::msgcat::mcset vi "Options" "T\u00f9y ch\u1ecdn"
::msgcat::mcset vi "Fetch Tracking Branch" "L\u1ea5y v\u1ec1 nh\u00e1nh \u0111\u01b0\u1ee3c theo d\u00f5i"
::msgcat::mcset vi "Detach From Local Branch" "T\u00e1ch r\u1eddi t\u1eeb Nh\u00e1nh n\u1ed9i b\u1ed9"
::msgcat::mcset vi "Create Branch" "T\u1ea1o nh\u00e1nh"
::msgcat::mcset vi "Create New Branch" "T\u1ea1o nh\u00e1nh m\u1edbi"
::msgcat::mcset vi "Create" "T\u1ea1o"
::msgcat::mcset vi "Branch Name" "T\u00ean nh\u00e1nh"
::msgcat::mcset vi "Name:" "T\u00ean:"
::msgcat::mcset vi "Match Tracking Branch Name" "Kh\u1edbp v\u1edbi t\u00ean nh\u00e1nh \u0111\u01b0\u1ee3c theo d\u00f5i"
::msgcat::mcset vi "Starting Revision" "\u0110i\u1ec3m \u0111\u1ea7u"
::msgcat::mcset vi "Update Existing Branch:" "C\u1eadp nh\u1eadt nh\u00e1nh s\u1eb5n c\u00f3:"
::msgcat::mcset vi "No" "Kh\u00f4ng"
::msgcat::mcset vi "Fast Forward Only" "Ch\u1ec9 fast-forward"
::msgcat::mcset vi "Reset" "\u0110\u1eb7t l\u1ea1i"
::msgcat::mcset vi "Checkout After Creation" "L\u1ea5y ra sau khi t\u1ea1o"
::msgcat::mcset vi "Please select a tracking branch." "Vui l\u00f2ng ch\u1ecdn nh\u00e1nh theo d\u00f5i."
::msgcat::mcset vi "Tracking branch %s is not a branch in the remote repository." "Nh\u00e1nh theo d\u00f5i %s kh\u00f4ng ph\u1ea3i l\u00e0 m\u1ed9t nh\u00e1nh tr\u00ean kho ch\u1ee9a m\u00e1y ch\u1ee7."
::msgcat::mcset vi "Please supply a branch name." "H\u00e3y cung c\u1ea5p t\u00ean nh\u00e1nh."
::msgcat::mcset vi "'%s' is not an acceptable branch name." "'%s' kh\u00f4ng ph\u1ea3i l\u00e0 m\u1ed9t t\u00ean nh\u00e1nh \u0111\u01b0\u1ee3c ch\u1ea5p nh\u1eadn."
::msgcat::mcset vi "Delete Branch" "Xo\u00e1 nh\u00e1nh"
::msgcat::mcset vi "Delete Local Branch" "X\u00f3a nh\u00e1nh n\u1ed9i b\u1ed9"
::msgcat::mcset vi "Local Branches" "Nh\u00e1nh n\u1ed9i b\u1ed9"
::msgcat::mcset vi "Delete Only If Merged Into" "Ch\u1ec9 x\u00f3a n\u1ebfu \u0111\u00e3 h\u00f2a tr\u1ed9n v\u00e0o"
::msgcat::mcset vi "Always (Do not perform merge checks)" "Lu\u00f4n (Kh\u00f4ng th\u1ef1c hi\u1ec7n ki\u1ec3m tra h\u00f2a tr\u1ed9n)"
::msgcat::mcset vi "The following branches are not completely merged into %s:" "C\u00e1c nh\u00e1nh sau \u0111\u00e2y kh\u00f4ng \u0111\u01b0\u1ee3c h\u00f2a tr\u1ed9n ho\u00e0n to\u00e0n v\u00e0o %s:"
::msgcat::mcset vi "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "Kh\u00f4i ph\u1ee5c c\u00e1c nh\u00e1nh \u0111\u00e3 b\u1ecb x\u00f3a l\u00e0 vi\u1ec7c kh\u00f3 kh\u0103n.\n\nX\u00f3a nh\u00e1nh \u0111\u00e3 ch\u1ecdn ch\u1ee9?"
::msgcat::mcset vi "Failed to delete branches:\n%s" "G\u1eb7p l\u1ed7i khi x\u00f3a c\u00e1c nh\u00e1nh:\n%s"
::msgcat::mcset vi "Rename Branch" "\u0110\u1ed5i t\u00ean nh\u00e1nh"
::msgcat::mcset vi "Rename" "\u0110\u1ed5i t\u00ean"
::msgcat::mcset vi "Branch:" "Nh\u00e1nh:"
::msgcat::mcset vi "New Name:" "T\u00ean m\u1edbi:"
::msgcat::mcset vi "Please select a branch to rename." "H\u00e3y ch\u1ecdn nh\u00e1nh c\u1ea7n \u0111\u1ed5i t\u00ean."
::msgcat::mcset vi "Branch '%s' already exists." "Nh\u00e1nh '%s' \u0111\u00e3 c\u00f3 r\u1ed3i."
::msgcat::mcset vi "Failed to rename '%s'." "G\u1eb7p l\u1ed7i khi \u0111\u1ed5i t\u00ean '%s'."
::msgcat::mcset vi "Starting..." "\u0110ang kh\u1edfi \u0111\u1ed9ng..."
::msgcat::mcset vi "File Browser" "B\u1ed9 duy\u1ec7t t\u1eadp tin"
::msgcat::mcset vi "Loading %s..." "\u0110ang t\u1ea3i %s..."
::msgcat::mcset vi "\[Up To Parent\]" "\[T\u1edbi cha m\u1eb9\]"
::msgcat::mcset vi "Browse Branch Files" "Duy\u1ec7t c\u00e1c t\u1eadp tin nh\u00e1nh"
::msgcat::mcset vi "Browse" "T\u00ecm duy\u1ec7t"
::msgcat::mcset vi "Fetching %s from %s" "\u0110ang l\u1ea5y v\u1ec1 %s t\u1eeb %s"
::msgcat::mcset vi "fatal: Cannot resolve %s" "g\u1eb7p l\u1ed7i nghi\u00eam tr\u1ecdng: Kh\u00f4ng th\u1ec3 ph\u00e2n gi\u1ea3i %s"
::msgcat::mcset vi "Close" "\u0110\u00f3ng"
::msgcat::mcset vi "Branch '%s' does not exist." "Ch\u01b0a c\u00f3 nh\u00e1nh '%s'"
::msgcat::mcset vi "Failed to configure simplified git-pull for '%s'." "G\u1eb7p l\u1ed7i khi c\u1ea5u h\u00ecnh git-pull \u0111\u01a1n gi\u1ea3n d\u00e0nh cho '%s'."
::msgcat::mcset vi "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "Nh\u00e1nh '%s' \u0111\u00e3 s\u1eb5n c\u00f3.\n\nKh\u00f4ng th\u1ec3 fast-forward th\u00e0nh %s.\nB\u1ea1n c\u1ea7n ph\u1ea3i h\u00f2a tr\u1ed9n."
::msgcat::mcset vi "Merge strategy '%s' not supported." "Kh\u00f4ng h\u1ed7 tr\u1ee3 chi\u1ebfn l\u01b0\u1ee3c h\u00f2a tr\u1ed9n '%s'."
::msgcat::mcset vi "Failed to update '%s'." "G\u1eb7p l\u1ed7i khi c\u1eadp nh\u1eadt '%s'."
::msgcat::mcset vi "Staging area (index) is already locked." "V\u00f9ng b\u1ec7 ph\u00f3ng (ch\u1ec9 m\u1ee5c) \u0111\u00e3 b\u1ecb kh\u00f3a r\u1ed3i."
::msgcat::mcset vi "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "Tr\u1ea1ng th\u00e1i qu\u00e9t kh\u00f4ng kh\u1edbp v\u1edbi tr\u1ea1ng th\u00e1i kho.\n\nC\u00f3 Git kh\u00e1c \u0111\u00e3 s\u1eeda kho n\u00e0y k\u1ec3 t\u1eeb l\u1ea7n qu\u00e9t cu\u1ed1i. C\u1ea7n qu\u00e9t l\u1ea1i tr\u01b0\u1edbc khi th\u1ef1c hi\u1ec7n vi\u1ec7c chuy\u1ec3n nh\u00e1nh.\n\nS\u1ebd th\u1ef1c hi\u1ec7n vi\u1ec7c qu\u00e9t l\u1ea1i ngay b\u00e2y gi\u1eddi.\n"
::msgcat::mcset vi "Updating working directory to '%s'..." "C\u1eadp nh\u1eadt th\u01b0 m\u1ee5c l\u00e0m vi\u1ec7c th\u00e0nh '%s'..."
::msgcat::mcset vi "files checked out" "c\u00e1c t\u1eadp tin c\u1ea7n l\u1ea5y ra"
::msgcat::mcset vi "Aborted checkout of '%s' (file level merging is required)." "H\u1ee7y b\u1ecf l\u1ea5y ra '%s' (c\u1ea7n h\u00f2a tr\u1ed9n m\u1ee9c t\u1eadp tin)."
::msgcat::mcset vi "File level merge required." "C\u1ea7n m\u1ee9c h\u00f2a tr\u1ed9n t\u1eadp tin."
::msgcat::mcset vi "Staying on branch '%s'." "\u0110ang \u1edf tr\u00ean nh\u00e1nh '%s'."
::msgcat::mcset vi "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "B\u1ea1n hi\u1ec7n kh\u00f4ng c\u00f2n \u1edf nh\u00e1nh n\u1ed9i b\u1ed9.\n\nN\u1ebfu b\u1ea1n mu\u1ed1n tr\u00ean m\u1ed9t nh\u00e1nh, h\u00e3y t\u1ea1o m\u1ed9t c\u00e1i t\u1eeb '\u0110\u00e2y l\u00e0 l\u1ea5y ra t\u00e1ch r\u1eddi'."
::msgcat::mcset vi "Checked out '%s'." "\u0110\u00e3 l\u1ea5y ra '%s'."
::msgcat::mcset vi "Resetting '%s' to '%s' will lose the following commits:" "\u0110\u1eb7t l\u1ea1i '%s' th\u00e0nh '%s' s\u1ebd l\u00e0m m\u1ea5t nh\u1eefng l\u1ea7n chuy\u1ec3n giao sau \u0111\u00e2y:"
::msgcat::mcset vi "Recovering lost commits may not be easy." "L\u1ea5y l\u1ea1i nh\u1eefng l\u1ea7n chuy\u1ec3n giao \u0111\u00e3 m\u1ea5t l\u00e0 kh\u00f4ng d\u1ec5."
::msgcat::mcset vi "Reset '%s'?" "\u0110\u1eb7t l\u1ea1i '%s'?"
::msgcat::mcset vi "Visualize" "Tr\u1ef1c quan"
::msgcat::mcset vi "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "G\u1eb7p l\u1ed7i khi \u0111\u1eb7t nh\u00e1nh hi\u1ec7n h\u00e0nh.\n\nTh\u01b0 m\u1ee5c l\u00e0m vi\u1ec7c ch\u1ec9 chuy\u1ec3n kh\u00f4ng ho\u00e0n to\u00e0n.  Ch\u00fang t\u00f4i c\u1eadp nh\u1eadt th\u00e0nh c\u00f4ng c\u00e1c t\u1eadp tin c\u1ee7a b\u1ea1n, nh\u01b0ng l\u1ea1i g\u1eb7p l\u1ed7i khi c\u1eadp nh\u1eadt m\u1ed9t t\u1eadp tin c\u1ee7a Git.\n\n\u0110i\u1ec1u n\u00e0y \u0111\u00e1ng l\u1ebd kh\u00f4ng th\u1ec3 x\u1ea3y ra.  %s gi\u1edd s\u1ebd \u0111\u00f3ng l\u1ea1i v\u00e0 \u0111\u1ea7u h\u00e0ng."
::msgcat::mcset vi "Select" "Ch\u1ecdn"
::msgcat::mcset vi "Font Family" "H\u1ecd ph\u00f4ng ch\u1eef"
::msgcat::mcset vi "Font Size" "C\u1ee1 ph\u00f4ng ch\u1eef"
::msgcat::mcset vi "Font Example" "Ph\u00f4ng ch\u1eef v\u00ed d\u1ee5"
::msgcat::mcset vi "This is example text.\nIf you like this text, it can be your font." "\u0110\u00e2y l\u00e0 ch\u1eef m\u1eabu.\nN\u1ebfu b\u1ea1n th\u00edch ch\u1eef nh\u01b0 th\u1ebf n\u00e0y th\u00ec ch\u1ecdn ph\u00f4ng ch\u1eef n\u00e0y."
::msgcat::mcset vi "Git Gui" "Git Gui"
::msgcat::mcset vi "Create New Repository" "T\u1ea1o kho m\u1edbi"
::msgcat::mcset vi "New..." "M\u1edbi..."
::msgcat::mcset vi "Clone Existing Repository" "Nh\u00e2n b\u1ea3n m\u1ed9t kho s\u1eb5n c\u00f3"
::msgcat::mcset vi "Clone..." "Nh\u00e2n b\u1ea3n..."
::msgcat::mcset vi "Open Existing Repository" "M\u1edf m\u1ed9t kho \u0111\u00e3 c\u00f3."
::msgcat::mcset vi "Open..." "M\u1edf..."
::msgcat::mcset vi "Recent Repositories" "C\u00e1c kho m\u1edbi d\u00f9ng"
::msgcat::mcset vi "Open Recent Repository:" "M\u1edf kho m\u1edbi d\u00f9ng:"
::msgcat::mcset vi "Failed to create repository %s:" "G\u1eb7p l\u1ed7i khi t\u1ea1o kho %s:"
::msgcat::mcset vi "Directory:" "Th\u01b0 m\u1ee5c:"
::msgcat::mcset vi "Git Repository" "Kho Git"
::msgcat::mcset vi "Directory %s already exists." "Th\u01b0 m\u1ee5c %s \u0111\u00e3 s\u1eb5n c\u00f3."
::msgcat::mcset vi "File %s already exists." "T\u1eadp tin %s \u0111\u00e3 c\u00f3 s\u1eb5n."
::msgcat::mcset vi "Clone" "Nh\u00e2n b\u1ea3n"
::msgcat::mcset vi "Source Location:" "V\u1ecb tr\u00ed ngu\u1ed3n:"
::msgcat::mcset vi "Target Directory:" "Th\u01b0 m\u1ee5c \u0111\u00edch:"
::msgcat::mcset vi "Clone Type:" "Ki\u1ec3u nh\u00e2n b\u1ea3n:"
::msgcat::mcset vi "Standard (Fast, Semi-Redundant, Hardlinks)" "Ti\u00eau chu\u1ea9n (Nhanh, Semi-Redundant, Hardlinks)"
::msgcat::mcset vi "Full Copy (Slower, Redundant Backup)" "Sao ch\u00e9p to\u00e0n b\u1ed9 (Ch\u1eadm h\u01a1n, Redundant Backup)"
::msgcat::mcset vi "Shared (Fastest, Not Recommended, No Backup)" "Chia s\u1ebb (Nhanh nh\u1ea5t, Kh\u00f4ng n\u00ean d\u00f9ng, No Backup)"
::msgcat::mcset vi "Not a Git repository: %s" "Kh\u00f4ng ph\u1ea3i l\u00e0 kho git: %s"
::msgcat::mcset vi "Standard only available for local repository." "Ti\u00eau chu\u1ea9n ch\u1ec9 s\u1eb5n s\u00e0ng v\u1edbi kho n\u1ed9i b\u1ed9."
::msgcat::mcset vi "Shared only available for local repository." "'Chia s\u1ebb' ch\u1ec9 s\u1eb5n s\u00e0ng v\u1edbi kho n\u1ed9i b\u1ed9."
::msgcat::mcset vi "Location %s already exists." "Mi\u1ec1n \u0111\u1ecba ph\u01b0\u01a1ng %s \u0111\u00e3 s\u1eb5n c\u00f3."
::msgcat::mcset vi "Failed to configure origin" "G\u1eb7p l\u1ed7i khi c\u1ea5u h\u00ecnh b\u1ea3n g\u1ed1c"
::msgcat::mcset vi "Counting objects" "\u0110ang \u0111\u1ebfm s\u1ed1 \u0111\u1ed1i t\u01b0\u1ee3ng"
::msgcat::mcset vi "buckets" "x\u00f4"
::msgcat::mcset vi "Unable to copy objects/info/alternates: %s" "Kh\u00f4ng th\u1ec3 sao ch\u00e9p objects/info/alternates: %s"
::msgcat::mcset vi "Nothing to clone from %s." "Kh\u00f4ng c\u00f3 g\u00ec \u0111\u1ec3 nh\u00e2n b\u1ea3n t\u1eeb %s"
::msgcat::mcset vi "The 'master' branch has not been initialized." "Nh\u00e1nh 'master' ch\u01b0a \u0111\u01b0\u1ee3c kh\u1edfi t\u1ea1o."
::msgcat::mcset vi "Hardlinks are unavailable.  Falling back to copying." "Li\u00ean k\u1ebft c\u1ee9ng kh\u00f4ng s\u1eb5n s\u00e0ng. Tr\u1edf l\u1ea1i ch\u1ebf \u0111\u1ed9 sao ch\u00e9p."
::msgcat::mcset vi "Cloning from %s" "\u0110ang nh\u00e2n b\u1ea3n t\u1eeb %s"
::msgcat::mcset vi "Copying objects" "\u0110ang ch\u00e9p c\u00e1c \u0111\u1ed1i t\u01b0\u1ee3ng"
::msgcat::mcset vi "KiB" "KiB"
::msgcat::mcset vi "Unable to copy object: %s" "Kh\u00f4ng th\u1ec3 ch\u00e9p \u0111\u1ed1i t\u01b0\u1ee3ng: %s"
::msgcat::mcset vi "Linking objects" "\u0110ang li\u00ean k\u1ebft c\u00e1c \u0111\u1ed1i t\u01b0\u1ee3ng"
::msgcat::mcset vi "objects" "\u0111\u1ed1i t\u01b0\u1ee3ng"
::msgcat::mcset vi "Unable to hardlink object: %s" "Kh\u00f4ng th\u1ec3 t\u1ea1o li\u00ean k\u1ebft c\u1ee9ng \u0111\u1ed1i t\u01b0\u1ee3ng: %s"
::msgcat::mcset vi "Cannot fetch branches and objects.  See console output for details." "Kh\u00f4ng th\u1ec3 l\u1ea5y c\u00e1c nh\u00e1nh v\u00e0 \u0111\u1ed1i t\u01b0\u1ee3ng. Xem k\u1ebft xu\u1ea5t t\u1eeb b\u1ea3ng \u0111i\u1ec1u khi\u1ec3n \u0111\u1ec3 c\u00f3 th\u00eam th\u00f4ng tin."
::msgcat::mcset vi "Cannot fetch tags.  See console output for details." "Kh\u00f4ng th\u1ec3 l\u1ea5y v\u1ec1 c\u00e1c th\u1ebb. H\u00e3y xem k\u1ebft xu\u1ea5t t\u1eeb b\u1ea3ng \u0111i\u1ec1u khi\u1ec3n \u0111\u1ec3 c\u00f3 th\u00eam th\u00f4ng tin chi ti\u1ebft."
::msgcat::mcset vi "Cannot determine HEAD.  See console output for details." "Kh\u00f4ng th\u1ec3 d\u00f2 t\u00ecm HEAD. H\u00e3y xem k\u1ebft xu\u1ea5t t\u1eeb b\u1ea3ng \u0111i\u1ec1u khi\u1ec3n \u0111\u1ec3 c\u00f3 th\u00eam th\u00f4ng tin chi ti\u1ebft."
::msgcat::mcset vi "Unable to cleanup %s" "Kh\u00f4ng th\u1ec3 d\u1ecdn s\u1ea1ch %s"
::msgcat::mcset vi "Clone failed." "G\u1eb7p l\u1ed7i khi nh\u00e2n b\u1ea3n."
::msgcat::mcset vi "No default branch obtained." "Kh\u00f4ng t\u00ecm th\u1ea5y nh\u00e1nh m\u1eb7c \u0111\u1ecbnh."
::msgcat::mcset vi "Cannot resolve %s as a commit." "Kh\u00f4ng th\u1ec3 ph\u00e2n gi\u1ea3i %s nh\u01b0 l\u00e0 m\u1ed9t l\u1ea7n chuy\u1ec3n giao."
::msgcat::mcset vi "Creating working directory" "\u0110ang t\u1ea1o th\u01b0 m\u1ee5c l\u00e0m vi\u1ec7c"
::msgcat::mcset vi "files" "t\u1eadp tin"
::msgcat::mcset vi "Initial file checkout failed." "L\u1ea5y ra t\u1eadp tin kh\u1edfi t\u1ea1o g\u1eb7p l\u1ed7i."
::msgcat::mcset vi "Open" "M\u1edf"
::msgcat::mcset vi "Repository:" "Kho:"
::msgcat::mcset vi "Failed to open repository %s:" "G\u1eb7p l\u1ed7i khi m\u1edf kho %s:"
::msgcat::mcset vi "This Detached Checkout" "\u0110\u00e2y l\u00e0 vi\u1ec7c l\u1ea5y ra b\u1ecb t\u00e1ch r\u1eddi"
::msgcat::mcset vi "Revision Expression:" "Bi\u1ec3u th\u1ee9c \u0111i\u1ec3m x\u00e9t:"
::msgcat::mcset vi "Local Branch" "Nh\u00e1nh n\u1ed9i b\u1ed9"
::msgcat::mcset vi "Tracking Branch" "Nh\u00e1nh Theo d\u00f5i"
::msgcat::mcset vi "Tag" "Th\u1ebb"
::msgcat::mcset vi "Invalid revision: %s" "\u0110i\u1ec3m x\u00e9t duy\u1ec7t kh\u00f4ng h\u1ee3p l\u1ec7: %s"
::msgcat::mcset vi "No revision selected." "Ch\u01b0a ch\u1ecdn \u0111i\u1ec3m x\u00e9t duy\u1ec7t."
::msgcat::mcset vi "Revision expression is empty." "Bi\u1ec3u th\u1ee9c ch\u00ednh quy r\u1ed7ng."
::msgcat::mcset vi "Updated" "\u0110\u00e3 c\u1eadp nh\u1eadt"
::msgcat::mcset vi "URL" "URL"
::msgcat::mcset vi "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "\u1ede \u0111\u00e2y ch\u1eb3ng c\u00f3 g\u00ec \u0111\u1ec3 tu b\u1ed5 c\u1ea3.\n\nB\u1ea1n \u0111ang t\u1ea1o l\u1ea7n chuy\u1ec3n giao kh\u1edfi t\u1ea1o. \u1ede \u0111\u00e2y kh\u00f4ng c\u00f3 l\u1ea7n chuy\u1ec3n giao tr\u01b0\u1edbc n\u00e0o \u0111\u1ec3 m\u00e0 tu b\u1ed5.\n"
::msgcat::mcset vi "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "Kh\u00f4ng th\u1ec3 tu b\u1ed5 trong khi h\u00f2a tr\u1ed9n.\n\nB\u1ea1n hi\u1ec7n \u0111ang \u1edf gi\u1eefa qu\u00e1 tr\u00ecnh h\u00f2a tr\u00f4n, m\u00e0 n\u00f3 ch\u01b0a ho\u00e0n t\u1ea5t. B\u1ea1n kh\u00f4ng th\u1ec3 tu b\u1ed5  l\u1ea7n chuy\u1ec3n giao ti\u1ec1n nhi\u1ec7m tr\u1eeb phi b\u1ea1n b\u00e3i b\u1ecf l\u1ea7n h\u00f2a tr\u1ed9n hi\u1ec7n \u0111ang k\u00edch ho\u1ea1t.\n"
::msgcat::mcset vi "Error loading commit data for amend:" "G\u1eb7p l\u1ed7i khi t\u1ea3i d\u1eef li\u1ec7u chuy\u1ec3n giao cho l\u1ec7nh tu b\u1ed5:"
::msgcat::mcset vi "Unable to obtain your identity:" "Kh\u00f4ng th\u1ec3 l\u1ea5y \u0111\u01b0\u1ee3c \u0111\u1ecbnh danh c\u1ee7a b\u1ea1n:"
::msgcat::mcset vi "Invalid GIT_COMMITTER_IDENT:" "GIT_COMMITTER_IDENT kh\u00f4ng h\u1ee3p l\u1ec7:"
::msgcat::mcset vi "warning: Tcl does not support encoding '%s'." "c\u1ea3nh b\u00e1o: Tcl kh\u00f4ng h\u1ed7 tr\u1ee3 b\u1ea3ng m\u00e3 '%s'."
::msgcat::mcset vi "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "Tr\u1ea1ng th\u00e1i qu\u00e9t kh\u00f4ng kh\u1edbp v\u1edbi tr\u1ea1ng th\u00e1i kho.\n\nC\u00f3 Git kh\u00e1c \u0111\u00e3 s\u1eeda kho n\u00e0y k\u1ec3 t\u1eeb l\u1ea7n qu\u00e9t cu\u1ed1i. C\u1ea7n qu\u00e9t l\u1ea1i tr\u01b0\u1edbc khi th\u1ef1c hi\u1ec7n vi\u1ec7c chuy\u1ec3n giao kh\u00e1c.\n\nS\u1ebd th\u1ef1c hi\u1ec7n vi\u1ec7c qu\u00e9t l\u1ea1i ngay b\u00e2y gi\u1eddi.\n"
::msgcat::mcset vi "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "C\u00e1c t\u1eadp tin ch\u01b0a h\u00f2a tr\u1ed9n kh\u00f4ng th\u1ec3 \u0111\u01b0\u1ee3c chuy\u1ec3n giao.\n\nT\u1eadp tin %s c\u00f3 xung \u0111\u1ed9t h\u00f2a tr\u1ed9n. B\u1ea1n ph\u1ea3i gi\u1ea3i quy\u1ebft ch\u00fang v\u00e0 \u0111\u01b0a l\u00ean b\u1ec7 ph\u00f3ng tr\u01b0\u1edbc khi chuy\u1ec3n giao.\n"
::msgcat::mcset vi "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "T\u00ecm th\u1ea5y tr\u1ea1ng th\u00e1i t\u1eadp tim kh\u00f4ng hi\u1ec3u %s.\n\nT\u1eadp tin %s kh\u00f4ng th\u1ec3 \u0111\u01b0\u1ee3c chuy\u1ec3n giao b\u1edfi ch\u01b0\u01a1ng tr\u00ecnh n\u00e0y.\n"
::msgcat::mcset vi "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "Kh\u00f4ng c\u00f3 thay \u0111\u1ed5i n\u00e0o c\u1ea7n chuy\u1ec3n giao.\n\nB\u1ea1n ph\u1ea3i \u0111\u01b0a l\u00ean b\u1ec7 ph\u00f3ng \u00edt nh\u1ea5t l\u00e0 m\u1ed9t t\u1eadp tin tr\u01b0\u1edbc khi c\u00f3 th\u1ec3 chuy\u1ec3n giao.\n"
::msgcat::mcset vi "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "H\u00e3y cung c\u1ea5p l\u1eddi ch\u00fa gi\u1ea3i cho l\u1ea7n chuy\u1ec3n giao.\n\nL\u1eddi ch\u00fa gi\u1ea3i t\u1ed1t nh\u1ea5t n\u00ean c\u00f3 \u0111\u1ecbnh d\u1ea1ng sau:\n\n- D\u00f2ng \u0111\u1ea7u ti\u00ean: M\u00f4 t\u1ea3 nh\u1eefng g\u00ec b\u1ea1n \u0111\u00e3 l\u00e0m.\n- D\u00f2ng th\u1ee9 hai: \u0110\u1ec3 tr\u1ed1ng\n- C\u00e1c d\u00f2ng c\u00f2n l\u1ea1i: M\u00f4 t\u1ea3 xem v\u00ec sao nh\u1eefng thay \u0111\u1ed5i n\u00e0y l\u00e0 c\u1ea7n thi\u1ebft.\n"
::msgcat::mcset vi "Calling pre-commit hook..." "\u0110ang g\u1ecdi m\u00f3c (hook) pre-commit..."
::msgcat::mcset vi "Commit declined by pre-commit hook." "L\u1ea7n chuy\u1ec3n giao b\u1ecb kh\u01b0\u1edbc t\u1eeb do m\u00f3c pre-commit."
::msgcat::mcset vi "You are about to commit on a detached head. This is a potentially dangerous thing to do because if you switch to another branch you will lose your changes and it can be difficult to retrieve them later from the reflog. You should probably cancel this commit and create a new branch to continue.\n \n Do you really want to proceed with your Commit?" "B\u1ea1n th\u1ef1c hi\u1ec7n chuy\u1ec3n giao \u1edf ch\u1ed7 \u0111\u00e3 t\u00e1ch r\u1eddi kh\u1ecfi c\u00e1c \u0111\u1ea7u. \u0110i\u1ec1u n\u00e0y l\u00e0 nguy hi\u1ec3m b\u1edfi n\u1ebfu b\u1ea1n chuy\u1ec3n sang nh\u00e1nh kh\u00e1c th\u00ec b\u1ea1n s\u1ebd m\u1ea5t nh\u1eefng thay \u0111\u1ed5i n\u00e0y v\u00e0 vi\u1ec7c l\u1ea5y l\u1ea1i ch\u00fang t\u1eeb reflog c\u0169ng kh\u00f3 kh\u0103n. B\u1ea1n g\u1ea7n nh\u01b0 ch\u1eafc ch\u1eafn l\u00e0 n\u00ean h\u1ee7y b\u1ecf l\u1ea7n chuy\u1ec3n giao n\u00e0y v\u00e0 t\u1ea1o m\u1ed9t nh\u00e1nh m\u1edbi tr\u01b0\u1edbc khi ti\u1ebfp t\u1ee5c.\n \n B\u1ea1n c\u00f3 th\u1ef1c s\u1ef1 mu\u1ed1n ti\u1ebfp t\u1ee5c chuy\u1ec3n giao?"
::msgcat::mcset vi "Calling commit-msg hook..." "\u0110ang g\u1ecdi m\u00f3c commit-msg..."
::msgcat::mcset vi "Commit declined by commit-msg hook." "L\u1ea7n chuy\u1ec3n giao b\u1ecb kh\u01b0\u1edbc t\u1eeb do m\u00f3c commit-msg."
::msgcat::mcset vi "Committing changes..." "Chuy\u1ec3n giao c\u00e1c thay \u0111\u1ed5i..."
::msgcat::mcset vi "write-tree failed:" "g\u1eb7p l\u1ed7i khi write-tree:"
::msgcat::mcset vi "Commit failed." "G\u1eb7p l\u1ed7i khi chuy\u1ec3n giao."
::msgcat::mcset vi "Commit %s appears to be corrupt" "L\u1ea7n chuy\u1ec3n giao %s c\u00f3 v\u1ebb \u0111\u00e3 h\u01b0 h\u1ecfng"
::msgcat::mcset vi "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "Kh\u00f4ng c\u00f3 thay \u0111\u1ed5i n\u00e0o \u0111\u1ec3 chuy\u1ec3n giao.\n\nKh\u00f4ng c\u00f3 t\u1eadp tin n\u00e0o \u0111\u01b0\u1ee3c s\u1eeda b\u1edfi l\u1ea7n chuy\u1ec3n giao n\u00e0y v\u00e0 n\u00f3 kh\u00f4ng ph\u1ea3i l\u00e0 l\u1ea7n chuy\u1ec3n giao h\u00f2a tr\u1ed9n.\n\nS\u1ebd th\u1ef1c hi\u1ec7n vi\u1ec7c qu\u00e9t l\u1ea1i ngay b\u00e2y gi\u1edd.\n"
::msgcat::mcset vi "No changes to commit." "Kh\u00f4ng c\u00f3 thay \u0111\u1ed5i n\u00e0o \u0111\u1ec3 chuy\u1ec3n giao."
::msgcat::mcset vi "commit-tree failed:" "commit-tree g\u1eb7p l\u1ed7i:"
::msgcat::mcset vi "update-ref failed:" "c\u1eadp nh\u1eadt tham chi\u1ebfu th\u1ea5t b\u1ea1i:"
::msgcat::mcset vi "Created commit %s: %s" "L\u1ea7n chuy\u1ec3n giao \u0111\u00e3 t\u1ea1o %s: %s"
::msgcat::mcset vi "Working... please wait..." "\u0110ang ch\u1ea1y.. vui l\u00f2ng \u0111\u1ee3i..."
::msgcat::mcset vi "Success" "Th\u00e0nh c\u00f4ng"
::msgcat::mcset vi "Error: Command Failed" "L\u1ed7i: C\u00e2u l\u1ec7nh g\u1eb7p l\u1ed7i"
::msgcat::mcset vi "Number of loose objects" "S\u1ed1 l\u01b0\u1ee3ng \u0111\u1ed1i t\u01b0\u1ee3ng b\u1ecb m\u1ea5t"
::msgcat::mcset vi "Disk space used by loose objects" "Dung l\u01b0\u1ee3ng \u0111\u0129a \u0111\u01b0\u1ee3c d\u00f9ng b\u1edfi c\u00e1c \u0111\u1ed1i t\u01b0\u1ee3ng b\u1ecb m\u1ea5t"
::msgcat::mcset vi "Number of packed objects" "S\u1ed1 l\u01b0\u1ee3ng \u0111\u1ed1i t\u01b0\u1ee3ng \u0111\u01b0\u1ee3c \u0111\u00f3ng g\u00f3i"
::msgcat::mcset vi "Number of packs" "S\u1ed1 l\u01b0\u1ee3ng g\u00f3i"
::msgcat::mcset vi "Disk space used by packed objects" "Dung l\u01b0\u1ee3ng \u0111\u0129a \u0111\u01b0\u1ee3c d\u00f9ng b\u1edfi c\u00e1c \u0111\u1ed1i t\u01b0\u1ee3ng g\u00f3i"
::msgcat::mcset vi "Packed objects waiting for pruning" "C\u00e1c \u0111\u1ed1i t\u01b0\u1ee3ng g\u00f3i ch\u1edd x\u00e9n b\u1edbt"
::msgcat::mcset vi "Garbage files" "C\u00e1c t\u1eadp tin r\u00e1c"
::msgcat::mcset vi "Compressing the object database" "N\u00e9n c\u01a1 s\u1edf d\u1eef li\u1ec7u \u0111\u1ed1i t\u01b0\u1ee3ng"
::msgcat::mcset vi "Verifying the object database with fsck-objects" "\u0110ang ki\u1ec3m tra c\u01a1 s\u1edf d\u1eef li\u1ec7u \u0111\u1ed1i t\u01b0\u1ee3ng b\u1eb1ng l\u1ec7nh fsck"
::msgcat::mcset vi "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "Hi\u1ec7n kho n\u00e0y m\u1ea5t \u01b0\u1edbc ch\u1eebng kho\u1ea3ng %i \u0111\u1ed1i t\u01b0\u1ee3ng.\n\n\u0110\u1ec3 t\u1ed1i \u01b0u h\u00f3a hi\u1ec7u su\u1ea5t, khuy\u1ebfn ngh\u1ecb b\u1ea1n n\u00ean n\u00e9n c\u01a1 s\u1edf d\u1eef li\u1ec7u c\u1ee7a m\u00ecnh l\u1ea1i.\n\nN\u00e9n c\u01a1 s\u1edf d\u1eef li\u1ec7u ch\u1ee9?"
::msgcat::mcset vi "Invalid date from Git: %s" "Ng\u00e0y th\u00e1ng kh\u00f4ng h\u1ee3p l\u1ec7 t\u1eeb Git: %s"
::msgcat::mcset vi "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "Kh\u00f4ng t\u00ecm th\u1ea5y kh\u00e1c bi\u1ec7t g\u00ec.\n\n%s kh\u00f4ng thay \u0111\u1ed5i.\n\nTh\u1eddi gian s\u1eeda \u0111\u1ed5i c\u1ee7a t\u1eadp tin n\u00e0y \u0111\u01b0\u1ee3c c\u1eadp nh\u1eadt b\u1edfi \u1ee9ng d\u1ee5ng kh\u00e1c, nh\u01b0ng n\u1ed9i dung b\u00ean trong t\u1eadp tin th\u00ec kh\u00f4ng thay \u0111\u1ed5i.\n\nS\u1ebd th\u1ef1c hi\u1ec7n qu\u00e9t l\u1ea1i m\u1ed9t c\u00e1ch t\u1ef1 \u0111\u1ed9ng \u0111\u1ec3 t\u00ecm c\u00e1c t\u1eadp tin kh\u00e1c c\u00e1i m\u00e0 c\u00f3 th\u1ec3 c\u00f3 c\u00f9ng t\u00ecnh tr\u1ea1ng."
::msgcat::mcset vi "Loading diff of %s..." "\u0110ang t\u1ea3i diff c\u1ee7a %s..."
::msgcat::mcset vi "LOCAL: deleted\nREMOTE:\n" "N\u1ed8IB\u1ed8: \u0111\u00e3 xo\u00e1\nM\u00c1YCH\u1ee6:\n"
::msgcat::mcset vi "REMOTE: deleted\nLOCAL:\n" "M\u00c1YCH\u1ee6: \u0111\u00e3 xo\u00e1\nN\u1ed8IB\u1ed8:\n"
::msgcat::mcset vi "LOCAL:\n" "N\u1ed8I-B\u1ed8:\n"
::msgcat::mcset vi "REMOTE:\n" "M\u00c1Y-CH\u1ee6:\n"
::msgcat::mcset vi "Unable to display %s" "Kh\u00f4ng th\u1ec3 hi\u1ec3n th\u1ecb %s"
::msgcat::mcset vi "Error loading file:" "L\u1ed7i khi t\u1ea3i t\u1eadp tin:"
::msgcat::mcset vi "Git Repository (subproject)" "Kho Git (d\u1ef1 \u00e1n con)"
::msgcat::mcset vi "* Binary file (not showing content)." "* T\u1eadp tin nh\u1ecb ph\u00e2n (kh\u00f4ng hi\u1ec3n th\u1ecb n\u1ed9i dung)."
::msgcat::mcset vi "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* T\u1eadp tin ch\u01b0a theo d\u00f5i l\u00e0 %d byte.\n* Ch\u1ec9 hi\u1ec3n th\u1ecb %d byte \u0111\u1ea7u .\n"
::msgcat::mcset vi "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* T\u1eadp tin ch\u01b0a theo d\u00f5i \u0111\u01b0\u1ee3c c\u1eaft t\u1ea1i \u0111\u00e2y b\u1edfi %s.\n* \u0110\u1ec3 xem to\u00e0n b\u1ed9 t\u1eadp tin, h\u00e3y d\u00f9ng \u1ee9ng d\u1ee5ng bi\u00ean so\u1ea1n b\u00ean ngo\u00e0i.\n"
::msgcat::mcset vi "Failed to unstage selected hunk." "G\u1eb7p l\u1ed7i khi b\u1ecf ra kh\u1ecfi b\u1ec7 ph\u00f3ng kh\u1ed1i \u0111\u00e3 ch\u1ecdn"
::msgcat::mcset vi "Failed to stage selected hunk." "G\u1eb7p l\u1ed7i khi \u0111\u01b0a l\u00ean b\u1ec7 ph\u00f3ng kh\u1ed1i \u0111\u00e3 ch\u1ecdn"
::msgcat::mcset vi "Failed to unstage selected line." "G\u1eb7p l\u1ed7i khi b\u1ecf ra kh\u1ecfi b\u1ec7 ph\u00f3ng d\u00f2ng \u0111\u00e3 ch\u1ecdn"
::msgcat::mcset vi "Failed to stage selected line." "G\u1eb7p l\u1ed7i khi \u0111\u01b0a l\u00ean b\u1ec7 ph\u00f3ng d\u00f2ng \u0111\u00e3 ch\u1ecdn"
::msgcat::mcset vi "Default" "M\u1eb7c \u0111\u1ecbnh"
::msgcat::mcset vi "System (%s)" "H\u1ec7 th\u1ed1ng (%s)"
::msgcat::mcset vi "Other" "Kh\u00e1c"
::msgcat::mcset vi "error" "l\u1ed7i"
::msgcat::mcset vi "warning" "c\u1ea3nh b\u00e1o"
::msgcat::mcset vi "You must correct the above errors before committing." "B\u1ea1n ph\u1ea3i s\u1eeda c\u00e1c l\u1ed7i tr\u00ean tr\u01b0\u1edbc khi chuy\u1ec3n giao."
::msgcat::mcset vi "Unable to unlock the index." "Kh\u00f4ng th\u1ec3 b\u1ecf kh\u00f3a b\u1ea3ng m\u1ee5c l\u1ee5c"
::msgcat::mcset vi "Index Error" "L\u1ed7i m\u1ee5c l\u1ee5c"
::msgcat::mcset vi "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "C\u1eadp nh\u1eadt m\u1ee5c l\u1ee5c cho Git g\u1eb7p l\u1ed7i. Vi\u1ec7c qu\u00e9t l\u1ea1i s\u1ebd t\u1ef1 \u0111\u1ed9ng \u0111\u01b0\u1ee3c kh\u1edfi ch\u1ea1y \u0111\u1ec3 \u0111\u1ed3ng h\u00f3a l\u1ea1i v\u1edbi git-gui."
::msgcat::mcset vi "Continue" "Ti\u1ebfp t\u1ee5c"
::msgcat::mcset vi "Unlock Index" "B\u1ecf kh\u00f3a m\u1ee5c l\u1ee5c"
::msgcat::mcset vi "Unstaging %s from commit" "B\u1ecf %s ra kh\u1ecfi vi\u1ec7c chuy\u1ec3n giao"
::msgcat::mcset vi "Ready to commit." "\u0110\u00e3 chuy\u1ec3n giao r\u1ed3i."
::msgcat::mcset vi "Adding %s" "\u0110ang th\u00eam %s"
::msgcat::mcset vi "Stage %d untracked files?" "\u0110\u01b0a %d t\u1eadp tin ch\u01b0a theo d\u00f5i l\u00ean b\u1ec7 ph\u00f3ng \u0111\u1ec3 chuy\u1ec3n giao?"
::msgcat::mcset vi "Revert changes in file %s?" "Ho\u00e0n nguy\u00ean c\u00e1c thay \u0111\u1ed5i trong t\u1eadp tin %s?"
::msgcat::mcset vi "Revert changes in these %i files?" "Ho\u00e0n nguy\u00ean c\u00e1c thay \u0111\u1ed5i trong %i t\u1eadp tin?"
::msgcat::mcset vi "Any unstaged changes will be permanently lost by the revert." "M\u1ecdi thay \u0111\u1ed5i ch\u01b0a \u0111\u01b0\u1ee3c \u0111\u01b0a l\u00ean b\u1ec7 ph\u00f3ng s\u1ebd m\u1ea5t v\u0129nh vi\u1ec5n do l\u1ec7nh revert."
::msgcat::mcset vi "Do Nothing" "Kh\u00f4ng l\u00e0m g\u00ec"
::msgcat::mcset vi "Reverting selected files" "\u0110ang ho\u00e0n nguy\u00ean c\u00e1c t\u1eadp tin \u0111\u00e3 ch\u1ecdn"
::msgcat::mcset vi "Reverting %s" "\u0110ang ho\u00e0n nguy\u00ean %s"
::msgcat::mcset vi "Goto Line:" "Nh\u1ea3y \u0111\u1ebfn d\u00f2ng:"
::msgcat::mcset vi "Go" "Nh\u1ea3y"
::msgcat::mcset vi "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "Kh\u00f4ng th\u1ec3 h\u00f2a tr\u1ed9n trong khi tu b\u1ed5.\n\nB\u1ea1n ph\u1ea3i ho\u00e0n t\u1ea5t vi\u1ec7c tu b\u1ed5 l\u1ea7n chuy\u1ec3n giao tr\u01b0\u1edbc khi b\u1eaft \u0111\u1ea7u b\u1ea5t k\u1ef3 ki\u1ec3u h\u00f2a tr\u1ed9n n\u00e0o.\n"
::msgcat::mcset vi "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "Tr\u1ea1ng th\u00e1i qu\u00e9t kh\u00f4ng kh\u1edbp v\u1edbi tr\u1ea1ng th\u00e1i kho.\n\nC\u00f3 Git kh\u00e1c \u0111\u00e3 s\u1eeda kho n\u00e0y k\u1ec3 t\u1eeb l\u1ea7n qu\u00e9t cu\u1ed1i. C\u1ea7n qu\u00e9t l\u1ea1i tr\u01b0\u1edbc khi th\u1ef1c hi\u1ec7n vi\u1ec7c h\u00f2a tr\u1ed9n.\n\nS\u1ebd th\u1ef1c hi\u1ec7n vi\u1ec7c qu\u00e9t l\u1ea1i ngay b\u00e2y gi\u1eddi.\n"
::msgcat::mcset vi "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "B\u1ea1n \u0111ang \u1edf gi\u1eefa vi\u1ec7c thay \u0111\u1ed5i.\n\nT\u1eadp tin %s \u0111\u00e3 b\u1ecb s\u1eeda \u0111\u1ed5i.\n\nB\u1ea1n n\u00ean ho\u00e0n thi\u1ec7n l\u1ea7n chuy\u1ec3n giao hi\u1ec7n nay tr\u01b0\u1edbc khi h\u00f2a tr\u1ed9n. Ch\u1ec9 c\u00f3 th\u1ebf b\u1ea1n m\u1edbi c\u00f3 th\u1ec3 b\u1eaft \u0111\u1ea7u h\u00f2a tr\u1ed9n c\u00e1i .\n"
::msgcat::mcset vi "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "B\u1ea1n \u0111ang \u1edf gi\u1eefa vi\u1ec7c thay \u0111\u1ed5i.\n\nT\u1eadp tin %s \u0111\u00e3 b\u1ecb s\u1eeda \u0111\u1ed5i.\n\nB\u1ea1n n\u00ean ho\u00e0n thi\u1ec7n l\u1ea7n chuy\u1ec3n giao hi\u1ec7n nay tr\u01b0\u1edbc khi h\u00f2a tr\u1ed9n.  L\u00e0m nh\u01b0 v\u1eady gi\u00fap b\u1ea1n c\u00f3 th\u1ec3 lo\u1ea1i b\u1ecf vi\u1ec7c l\u1ed7i trong h\u00f2a tr\u1ed9n.\n"
::msgcat::mcset vi "%s of %s" "%s tr\u00ean %s"
::msgcat::mcset vi "Merging %s and %s..." "\u0110ang h\u00f2a tr\u1ed9n %s v\u00e0 %s..."
::msgcat::mcset vi "Merge completed successfully." "H\u00f2a tr\u1ed9n \u0111\u00e3 th\u1ef1c hi\u1ec7n th\u00e0nh c\u00f4ng."
::msgcat::mcset vi "Merge failed.  Conflict resolution is required." "H\u00f2a tr\u1ed9n g\u1eb7p l\u1ed7i. C\u1ea7n gi\u1ea3i quy\u1ebft c\u00e1c xung \u0111\u1ed9t tr\u01b0\u1edbc."
::msgcat::mcset vi "Merge Into %s" "H\u00f2a tr\u1ed9n v\u00e0o %s"
::msgcat::mcset vi "Revision To Merge" "\u0110i\u1ec3m c\u1ea7n h\u00f2a tr\u1ed9n"
::msgcat::mcset vi "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "Kh\u00f4ng th\u1ec3 h\u1ee7y b\u1ecf trong khi \u0111ang tu b\u1ed5.\n\nB\u1ea1n c\u1ea7n ph\u1ea3i ho\u00e0n t\u1ea5t vi\u1ec7c tu b\u1ed5 l\u1ea7n chuy\u1ec3n giao n\u00e0y.\n"
::msgcat::mcset vi "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "B\u00e3i b\u1ecf h\u00f2a tr\u1ed9n?\n\nB\u00e3i b\u1ecf h\u00f2a tr\u1ed9n hi\u1ec7n nay s\u1ebd l\u00e0m *T\u1ea4T C\u1ea2* c\u00e1c thay \u0111\u1ed5i ch\u01b0a \u0111\u01b0\u1ee3c chuy\u1ec3n giao b\u1ecb m\u1ea5t.\n\nTi\u1ebfp t\u1ee5c b\u00e3i b\u1ecf vi\u1ec7c h\u00f2a tr\u1ed9n hi\u1ec7n t\u1ea1i?"
::msgcat::mcset vi "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "\u0110\u1eb7t l\u1ea1i m\u1ecdi thay \u0111\u1ed5i?\n\nVi\u1ec7c \u0111\u1eb7t l\u1ea1i c\u00e1c thay \u0111\u1ed5i s\u1ebd l\u00e0m *M\u1eccI* thay \u0111\u1ed5i ch\u01b0a chuy\u1ec3n giao bi\u1ebfn m\u1ea5t.\n\nV\u1eabn ti\u1ebfp t\u1ee5c \u0111\u1eb7t l\u1ea1i c\u00e1c thay \u0111\u1ed5i hi\u1ec7n t\u1ea1i?"
::msgcat::mcset vi "Aborting" "B\u00e3i b\u1ecf"
::msgcat::mcset vi "files reset" "\u0111\u1eb7t l\u1ea1i c\u00e1c t\u1eadp tin"
::msgcat::mcset vi "Abort failed." "G\u1eb7p l\u1ed7i khi b\u00e3i b\u1ecf."
::msgcat::mcset vi "Abort completed.  Ready." "\u0110\u00e3 b\u00e3i b\u1ecf xong.  S\u1eb5n s\u00e0ng."
::msgcat::mcset vi "Force resolution to the base version?" "Bu\u1ed9c ph\u00e2n gi\u1ea3i th\u00e0nh nh\u00e1nh c\u01a1 s\u1edf?"
::msgcat::mcset vi "Force resolution to this branch?" "Bu\u1ed9c ph\u00e2n gi\u1ea3i th\u00e0nh nh\u00e1nh n\u00e0y?"
::msgcat::mcset vi "Force resolution to the other branch?" "Bu\u1ed9c ph\u00e2n gi\u1ea3i th\u00e0nh nh\u00e1nh kh\u00e1c?"
::msgcat::mcset vi "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "Ch\u00fa \u00fd l\u00e0 diff ch\u1ec9 hi\u1ec3n th\u1ecb nh\u1eefng thay \u0111\u1ed5i xung \u0111\u1ed9t.\n\n%s s\u1ebd b\u1ecb ghi \u0111\u00e8.\n\nThao t\u00e1c n\u00e0y ch\u1ec9 c\u00f3 th\u1ec3 b\u1ecf d\u1edf b\u1eb1ng c\u00e1ch kh\u1edfi \u0111\u1ed9ng l\u1ea1i vi\u1ec7c h\u00f2a tr\u1ed9n."
::msgcat::mcset vi "File %s seems to have unresolved conflicts, still stage?" "T\u1eadp tin %s c\u00f3 v\u1ebb ch\u01b0a \u0111\u01b0\u1ee3c gi\u1ea3i quy\u1ebft xung \u0111\u1ed9t, v\u1eabn \u0111\u00e1nh d\u1ea5u l\u00e0 c\u1ea7n chuy\u1ec3n giao?"
::msgcat::mcset vi "Adding resolution for %s" "\u0110ang ph\u00e2n gi\u1ea3i cho %s"
::msgcat::mcset vi "Cannot resolve deletion or link conflicts using a tool" "Kh\u00f4ng th\u1ec3 ph\u00e2n gi\u1ea3i xung \u0111\u1ed9t x\u00f3a hay li\u00ean k\u1ebft d\u00f9ng m\u1ed9t c\u00f4ng c\u1ee5"
::msgcat::mcset vi "Conflict file does not exist" "T\u1eadp tin xung \u0111\u1ed9t kh\u00f4ng t\u1ed3n t\u1ea1i"
::msgcat::mcset vi "Not a GUI merge tool: '%s'" "Kh\u00f4ng ph\u1ea3i l\u00e0 m\u1ed9t c\u00f4ng c\u1ee5 h\u00f2a tr\u1ed9n GUI: '%s'"
::msgcat::mcset vi "Unsupported merge tool '%s'" "Kh\u00f4ng h\u1ed7 tr\u1ee3 c\u00f4ng c\u1ee5 tr\u1ed9n '%s'"
::msgcat::mcset vi "Merge tool is already running, terminate it?" "C\u00f4ng c\u1ee5 h\u00f2a tr\u1ed9n \u0111ang ch\u1ea1y r\u1ed3i, ch\u1ea5m d\u1ee9t n\u00f3?"
::msgcat::mcset vi "Error retrieving versions:\n%s" "G\u1eb7p l\u1ed7i khi truy l\u1ea1i phi\u00ean b\u1ea3n:\n%s"
::msgcat::mcset vi "Could not start the merge tool:\n\n%s" "Kh\u00f4ng th\u1ec3 kh\u1edfi ch\u1ea1y c\u00f4ng c\u1ee5 h\u00f2a tr\u1ed9n:\n\n%s"
::msgcat::mcset vi "Running merge tool..." "\u0110ang ch\u1ea1y c\u00f4ng c\u1ee5 tr\u1ed9n..."
::msgcat::mcset vi "Merge tool failed." "C\u00f4ng c\u1ee5 tr\u1ed9n g\u1eb7p l\u1ed7i."
::msgcat::mcset vi "Invalid global encoding '%s'" "B\u1ea3ng m\u00e3 to\u00e0n c\u1ee5c kh\u00f4ng h\u1ee3p l\u1ec7 '%s'"
::msgcat::mcset vi "Invalid repo encoding '%s'" "B\u1ea3ng m\u00e3 kho ch\u1ee9a kh\u00f4ng h\u1ee3p l\u1ec7 '%s'"
::msgcat::mcset vi "Restore Defaults" "Ph\u1ee5c h\u1ed3i th\u00e0nh m\u1eb7c \u0111\u1ecbnh"
::msgcat::mcset vi "Save" "Ghi l\u1ea1i"
::msgcat::mcset vi "%s Repository" "%s kho"
::msgcat::mcset vi "Global (All Repositories)" "To\u00e0n c\u1ee5c (M\u1ecdi kho)"
::msgcat::mcset vi "User Name" "T\u00ean ng\u01b0\u1eddi d\u00f9ng"
::msgcat::mcset vi "Email Address" "\u0110\u1ecba ch\u1ec9 th\u01b0 \u0111i\u1ec7n t\u1eed"
::msgcat::mcset vi "Summarize Merge Commits" "T\u1ed5ng h\u1ee3p v\u1ec1 h\u00f2a tr\u1ed9n c\u00e1c l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Merge Verbosity" "Chi ti\u1ebft vi\u1ec7c h\u00f2a tr\u1ed9n"
::msgcat::mcset vi "Show Diffstat After Merge" "Hi\u1ec3n th\u1ecb th\u1ed1ng k\u00ea kh\u00e1c bi\u1ec7t sau h\u00f2a tr\u1ed9n"
::msgcat::mcset vi "Use Merge Tool" "D\u00f9ng C\u00f4ng c\u1ee5 tr\u1ed9n"
::msgcat::mcset vi "Trust File Modification Timestamps" "Tin d\u1ea5u v\u1ebft th\u1eddi gian s\u1eeda \u0111\u1ed5i t\u1eadp tin"
::msgcat::mcset vi "Prune Tracking Branches During Fetch" "X\u00e9n c\u00e1c nh\u00e1nh theo d\u00f5i trong khi l\u1ea5y v\u1ec1"
::msgcat::mcset vi "Match Tracking Branches" "Kh\u1edbp nh\u00e1nh theo d\u00f5i"
::msgcat::mcset vi "Use Textconv For Diffs and Blames" "D\u00f9ng Textconv Cho Diffs v\u00e0 Blames"
::msgcat::mcset vi "Blame Copy Only On Changed Files" "Ch\u1ec9 ch\u00e9p blame tr\u00ean c\u00e1c t\u1eadp tin thay \u0111\u1ed5i"
::msgcat::mcset vi "Maximum Length of Recent Repositories List" "S\u1ed1 l\u01b0\u1ee3ng kho m\u1edbi d\u00f9ng t\u1ed1i \u0111a \u0111\u01b0\u1ee3c l\u01b0u tr\u1eef"
::msgcat::mcset vi "Minimum Letters To Blame Copy On" "Ch\u1eef t\u1ed1i thi\u1ec3u \u0111\u1ec3 blame ch\u00e9p v\u00e0o"
::msgcat::mcset vi "Blame History Context Radius (days)" "B\u00e1n k\u00ednh ng\u1eef c\u1ea3nh l\u1ecbch s\u1eed blame (ng\u00e0y)"
::msgcat::mcset vi "Number of Diff Context Lines" "S\u1ed1 d\u00f2ng n\u1ed9i dung Diff"
::msgcat::mcset vi "Additional Diff Parameters" "\u0110\u1ed1i s\u1ed1 b\u1ed5 xung cho Diff"
::msgcat::mcset vi "Commit Message Text Width" "Chi\u1ec1u r\u1ed9ng c\u1ee7a ph\u1ea7n ch\u00fa th\u00edch"
::msgcat::mcset vi "New Branch Name Template" "M\u1eabu t\u00ean nh\u00e1nh m\u1edbi"
::msgcat::mcset vi "Default File Contents Encoding" "B\u1ea3ng m\u00e3 d\u00e0nh cho n\u1ed9i dung t\u1eadp tin m\u1eb7c \u0111\u1ecbnh"
::msgcat::mcset vi "Warn before committing to a detached head" "C\u1ea3nh b\u00e1o tr\u01b0\u1edbc khi chuy\u1ec3n giao m\u1ed9t \u0111\u1ea7u b\u1ecb t\u00e1ch r\u1eddi"
::msgcat::mcset vi "Staging of untracked files" "\u0110\u00e1nh d\u1ea5u nh\u1eefng t\u1eadp tin ch\u01b0a \u0111\u01b0\u1ee3c theo d\u00f5i l\u00e0 c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Show untracked files" "Hi\u1ec7n c\u00e1c t\u1eadp tin ch\u01b0a \u0111\u01b0\u1ee3c theo d\u00f5i"
::msgcat::mcset vi "Change" "Thay \u0111\u1ed5i"
::msgcat::mcset vi "Spelling Dictionary:" "T\u1eeb \u0111i\u1ec3n ch\u00ednh t\u1ea3:"
::msgcat::mcset vi "Change Font" "\u0110\u1ed5i ph\u00f4ng ch\u1eef"
::msgcat::mcset vi "Choose %s" "Ch\u1ecdn %s"
::msgcat::mcset vi "pt." "pt."
::msgcat::mcset vi "Preferences" "C\u00e1 nh\u00e2n h\u00f3a"
::msgcat::mcset vi "Failed to completely save options:" "G\u1eb7p l\u1ed7i khi ho\u00e0n t\u1ea5t ghi l\u1ea1i c\u00e1c t\u00f9y ch\u1ecdn:"
::msgcat::mcset vi "Add Remote" "Th\u00eam m\u00e1y ch\u1ee7"
::msgcat::mcset vi "Add New Remote" "Th\u00eam m\u00e1y ch\u1ee7 m\u1edbi"
::msgcat::mcset vi "Add" "Th\u00eam v\u00e0o"
::msgcat::mcset vi "Remote Details" "Chi ti\u1ebft v\u1ec1 m\u00e1y ch\u1ee7"
::msgcat::mcset vi "Location:" "V\u1ecb tr\u00ed:"
::msgcat::mcset vi "Further Action" "H\u00e0nh \u0111\u1ed9ng th\u00eam"
::msgcat::mcset vi "Fetch Immediately" "L\u1ea5y v\u1ec1 ngay l\u1eadp t\u1ee9c"
::msgcat::mcset vi "Initialize Remote Repository and Push" "Kh\u1edfi t\u1ea1o Kho m\u00e1y ch\u1ee7 v\u00e0 \u0111\u1ea9y d\u1eef li\u1ec7u l\u00ean"
::msgcat::mcset vi "Do Nothing Else Now" "Kh\u00f4ng l\u00e0m g\u00ec c\u1ea3"
::msgcat::mcset vi "Please supply a remote name." "H\u00e3y cung c\u1ea5p t\u00ean m\u00e1y ch\u1ee7."
::msgcat::mcset vi "'%s' is not an acceptable remote name." "'%s' kh\u00f4ng ph\u1ea3i l\u00e0 t\u00ean m\u00e1y ch\u1ee7 \u0111\u01b0\u1ee3c ch\u1ea5p nh\u1eadn."
::msgcat::mcset vi "Failed to add remote '%s' of location '%s'." "G\u1eb7p l\u1ed7i khi th\u00eam m\u00e1y ch\u1ee7 '%s' c\u1ee7a v\u1ecb tr\u00ed '%s'."
::msgcat::mcset vi "fetch %s" "l\u1ea5y v\u1ec1 %s"
::msgcat::mcset vi "Fetching the %s" "\u0110ang l\u1ea5y v\u1ec1 %s"
::msgcat::mcset vi "Do not know how to initialize repository at location '%s'." "Kh\u00f4ng hi\u1ec3u l\u00e0m th\u1ebf n\u00e0o \u0111\u1ec3 kh\u1edfi t\u1ea1o kho ch\u1ee9a t\u1ea1i v\u1ecb tr\u00ed '%s'."
::msgcat::mcset vi "push %s" "\u0111\u1ea9y %s l\u00ean m\u00e1y ch\u1ee7"
::msgcat::mcset vi "Setting up the %s (at %s)" "C\u00e0i \u0111\u1eb7t '%s' (t\u1ea1i %s)"
::msgcat::mcset vi "Delete Branch Remotely" "X\u00f3a nh\u00e1nh tr\u00ean m\u00e1y ch\u1ee7"
::msgcat::mcset vi "From Repository" "T\u1eeb Kho"
::msgcat::mcset vi "Remote:" "M\u00e1y ch\u1ee7:"
::msgcat::mcset vi "Arbitrary Location:" "\u0110\u1ecba \u0111i\u1ec3m t\u00f9y \u00fd:"
::msgcat::mcset vi "Branches" "Nh\u00e1nh"
::msgcat::mcset vi "Delete Only If" "Ch\u1ec9 xo\u00e1 N\u1ebfu"
::msgcat::mcset vi "Merged Into:" "\u0110\u00e3 tr\u1ed9n v\u00e0o:"
::msgcat::mcset vi "A branch is required for 'Merged Into'." "C\u1ea7n m\u1ed9t nh\u00e1nh cho 'H\u00f2a tr\u1ed9n v\u00e0o'."
::msgcat::mcset vi "The following branches are not completely merged into %s:\n\n - %s" "C\u00e1c nh\u00e1nh sau \u0111\u00e2y kh\u00f4ng \u0111\u01b0\u1ee3c h\u00f2a tr\u1ed9n ho\u00e0n to\u00e0n v\u00e0o %s:\n\n - %s"
::msgcat::mcset vi "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "M\u1ed9t hay nhi\u1ec1u h\u01a1n ki\u1ec3m tra h\u00f2a tr\u1ed9n kh\u00f4ng \u0111\u1ea1t b\u1edfi v\u00ec b\u1ea1n \u0111\u00e3 kh\u00f4ng l\u1ea5y v\u1ec1 nh\u1eefng l\u1ea7n chuy\u1ec3n giao c\u1ea7n thi\u1ebft. H\u00e3y l\u1ea5y v\u1ec1 t\u1eeb %s tr\u01b0\u1edbc \u0111\u00e3."
::msgcat::mcset vi "Please select one or more branches to delete." "Xin h\u00e3y ch\u1ecdn m\u1ed9t hay nhi\u1ec1u nh\u00e1nh c\u1ea7n x\u00f3a."
::msgcat::mcset vi "Deleting branches from %s" "\u0110ang xo\u00e1 c\u00e1c nh\u00e1nh t\u1eeb %s"
::msgcat::mcset vi "No repository selected." "Ch\u01b0a ch\u1ecdn kho."
::msgcat::mcset vi "Scanning %s..." "\u0110ang qu\u00e9t: %s..."
::msgcat::mcset vi "Push to" "\u0110\u1ea9y l\u00ean"
::msgcat::mcset vi "Remove Remote" "G\u1ee1 b\u1ecf M\u00e1y ch\u1ee7"
::msgcat::mcset vi "Prune from" "X\u00e9n t\u1eeb"
::msgcat::mcset vi "Fetch from" "L\u1ea5y v\u1ec1 t\u1eeb"
::msgcat::mcset vi "Find:" "T\u00ecm:"
::msgcat::mcset vi "Next" "Ti\u1ebfp"
::msgcat::mcset vi "Prev" "Tr\u01b0\u1edbc"
::msgcat::mcset vi "RegExp" "BTCQ"
::msgcat::mcset vi "Case" "Hoa"
::msgcat::mcset vi "Cannot write shortcut:" "Kh\u00f4ng th\u1ec3 ghi l\u1ed1i t\u1eaft:"
::msgcat::mcset vi "Cannot write icon:" "Kh\u00f4ng th\u1ec3 ghi bi\u1ec3u t\u01b0\u1ee3ng:"
::msgcat::mcset vi "Unsupported spell checker" "Kh\u00f4ng h\u1ed7 tr\u1ee3 ki\u1ec3m tra ch\u00ednh t\u1ea3"
::msgcat::mcset vi "Spell checking is unavailable" "Ki\u1ec3m tra ch\u00ednh t\u1ea3 kh\u00f4ng s\u1eb5n s\u00e0ng"
::msgcat::mcset vi "Invalid spell checking configuration" "C\u1ea5u h\u00ecnh b\u1ed9 so\u00e1t ch\u00ednh t\u1ea3 kh\u00f4ng h\u1ee3p l\u1ec7"
::msgcat::mcset vi "Reverting dictionary to %s." "\u0110ang ho\u00e0n nguy\u00ean t\u1eeb \u0111i\u1ec3n th\u00e0nh %s."
::msgcat::mcset vi "Spell checker silently failed on startup" "Ph\u1ea7n ki\u1ec3m tra ch\u00ednh t\u1ea3 \u0111\u00e3 g\u1eb7p l\u1ed7i khi kh\u1edfi \u0111\u1ed9ng"
::msgcat::mcset vi "Unrecognized spell checker" "Kh\u00f4ng ch\u1ea5p nh\u1eadn b\u1ed9 ki\u1ec3m tra ch\u00ednh t\u1ea3"
::msgcat::mcset vi "No Suggestions" "Kh\u00f4ng c\u00f3 g\u1ee3i \u00fd"
::msgcat::mcset vi "Unexpected EOF from spell checker" "G\u1eb7p k\u1ebft th\u00fac b\u1ea5t ng\u1edd t\u1eeb b\u1ed9 ki\u1ec3m tra ch\u00ednh t\u1ea3"
::msgcat::mcset vi "Spell Checker Failed" "Ki\u1ec3m tra ch\u00ednh t\u1ea3 kh\u00f4ng th\u00e0nh c\u00f4ng"
::msgcat::mcset vi "No keys found." "Kh\u00f4ng t\u00ecm th\u1ea5y kh\u00f3a n\u00e0o."
::msgcat::mcset vi "Found a public key in: %s" "T\u00ecm th\u1ea5y kho\u00e1 c\u00f4ng khai trong: %s"
::msgcat::mcset vi "Generate Key" "T\u1ea1o kho\u00e1"
::msgcat::mcset vi "Copy To Clipboard" "Ch\u00e9p v\u00e0o clipboard"
::msgcat::mcset vi "Your OpenSSH Public Key" "Kh\u00f3a c\u00f4ng OpenSSH c\u1ee7a b\u1ea1n"
::msgcat::mcset vi "Generating..." "\u0110ang t\u1ea1o..."
::msgcat::mcset vi "Could not start ssh-keygen:\n\n%s" "Kh\u00f4ng th\u1ec3 ch\u1ea1y ssh-keygen:\n\n%s"
::msgcat::mcset vi "Generation failed." "Vi\u1ec7c t\u1ea1o kho\u00e1 \u0111\u00e3 th\u1ea5t b\u1ea1i."
::msgcat::mcset vi "Generation succeeded, but no keys found." "Vi\u1ec7c t\u1ea1o th\u00e0nh c\u00f4ng nh\u01b0ng l\u1ea1i kh\u00f4ng t\u00ecm th\u1ea5y kh\u00f3a."
::msgcat::mcset vi "Your key is in: %s" "Kh\u00f3a c\u1ee7a b\u1ea1n trong: %s"
::msgcat::mcset vi "%s ... %*i of %*i %s (%3i%%)" "%s ... %*i trong %*i %s (%3i%%)"
::msgcat::mcset vi "Add Tool" "Th\u00eam c\u00f4ng c\u1ee5"
::msgcat::mcset vi "Add New Tool Command" "Th\u00eam l\u1ec7nh c\u00f4ng c\u1ee5 m\u1edbi"
::msgcat::mcset vi "Add globally" "Th\u00eam to\u00e0n c\u1ee5c"
::msgcat::mcset vi "Tool Details" "Chi ti\u1ebft c\u00f4ng c\u1ee5"
::msgcat::mcset vi "Use '/' separators to create a submenu tree:" "D\u00f9ng d\u1ea5u ng\u0103n c\u00e1ch '/' \u0111\u1ec3 t\u1ea1o c\u00e2y tr\u00ecnh \u0111\u01a1n con:"
::msgcat::mcset vi "Command:" "L\u1ec7nh:"
::msgcat::mcset vi "Show a dialog before running" "Hi\u1ec3n th\u1ecb h\u1ed9p tho\u1ea1i tr\u01b0\u1edbc khi ch\u1ea1y"
::msgcat::mcset vi "Ask the user to select a revision (sets \$REVISION)" "H\u1ecfi ng\u01b0\u1eddi d\u00f9ng ch\u1ecdn \u0111i\u1ec3m xem x\u00e9t (\u0111\u1eb7t bi\u1ebfn \$REVISION)"
::msgcat::mcset vi "Ask the user for additional arguments (sets \$ARGS)" "H\u1ecfi ng\u01b0\u1eddi d\u00f9ng c\u00e1c \u0111\u1ed1i s\u1ed1 b\u1ed5 xung th\u00eam (\u0111\u1eb7t bi\u1ebfn \$ARGS)"
::msgcat::mcset vi "Don't show the command output window" "Kh\u00f4ng hi\u1ec3n th\u1ecb c\u1eeda s\u1ed5 k\u1ebft xu\u1ea5t c\u00e2u l\u1ec7nh"
::msgcat::mcset vi "Run only if a diff is selected (\$FILENAME not empty)" "Ch\u1ec9 ch\u1ea1y n\u1ebfu diff \u0111\u01b0\u1ee3c ch\u1ecdn (bi\u1ebfn \$FILENAME kh\u00f4ng r\u1ed7ng)"
::msgcat::mcset vi "Please supply a name for the tool." "H\u00e3y cung c\u1ea5p t\u00ean cho c\u00f4ng c\u1ee5."
::msgcat::mcset vi "Tool '%s' already exists." "C\u00f4ng c\u1ee5 '%s' \u0111\u00e3 s\u1eb5n c\u00f3."
::msgcat::mcset vi "Could not add tool:\n%s" "Kh\u00f4ng th\u1ec3 th\u00eam c\u00f4ng c\u1ee5:\n%s"
::msgcat::mcset vi "Remove Tool" "G\u1ee1 b\u1ecf c\u00f4ng c\u1ee5"
::msgcat::mcset vi "Remove Tool Commands" "G\u1ee1 b\u1ecf c\u00f4ng c\u1ee5 l\u1ec7nh"
::msgcat::mcset vi "Remove" "G\u1ee1 b\u1ecf"
::msgcat::mcset vi "(Blue denotes repository-local tools)" "(C\u00e1c c\u00f4ng c\u1ee5 ch\u1ec9 th\u1ecb kho-n\u1ed9i-b\u1ed9 xanh)"
::msgcat::mcset vi "Run Command: %s" "Ch\u1ea1y l\u1ec7nh: %s"
::msgcat::mcset vi "Arguments" "\u0110\u1ed1i s\u1ed1"
::msgcat::mcset vi "OK" "\u0110\u1ed3ng \u00fd"
::msgcat::mcset vi "Running %s requires a selected file." "Ch\u1ea1y %s y\u00eau c\u1ea7u c\u1ea7n ph\u1ea3i ch\u1ecdn m\u1ed9t t\u1eadp tin."
::msgcat::mcset vi "Are you sure you want to run %1\$s on file \"%2\$s\"?" "B\u1ea1n c\u00f3 ch\u1eafc l\u00e0 mu\u1ed1n ch\u1ea1y %1\$s tr\u00ean t\u1eadp tin \"%2\$s\" kh\u00f4ng?"
::msgcat::mcset vi "Are you sure you want to run %s?" "B\u1ea1n c\u00f3 ch\u1eafc l\u00e0 mu\u1ed1n ch\u1ea1y %s kh\u00f4ng?"
::msgcat::mcset vi "Tool: %s" "C\u00f4ng c\u1ee5: %s"
::msgcat::mcset vi "Running: %s" "\u0110ang ch\u1ea1y: %s"
::msgcat::mcset vi "Tool completed successfully: %s" "C\u00f4ng c\u1ee5 \u0111\u01b0\u1ee3c bi\u00ean d\u1ecbch th\u00e0nh c\u00f4ng: %s"
::msgcat::mcset vi "Tool failed: %s" "C\u00f4ng c\u1ee5 g\u1eb7p l\u1ed7i: %s"
::msgcat::mcset vi "Fetching new changes from %s" "L\u1ea5y c\u00e1c thay \u0111\u1ed5i m\u1edbi t\u1eeb %s"
::msgcat::mcset vi "remote prune %s" "x\u00e9n b\u1edbt tr\u00ean m\u00e1y ch\u1ee7 %s"
::msgcat::mcset vi "Pruning tracking branches deleted from %s" "X\u00e9n b\u1edbt c\u00e1c nh\u00e1nh theo d\u00f5i b\u1ecb x\u00f3a t\u1eeb %s"
::msgcat::mcset vi "fetch all remotes" "l\u1ea5y v\u1ec1 t\u1eeb t\u1ea5t c\u1ea3 c\u00e1c m\u00e1y ch\u1ee7"
::msgcat::mcset vi "Fetching new changes from all remotes" "\u0110ang l\u1ea5y c\u00e1c thay \u0111\u1ed5i m\u1edbi t\u1eeb m\u1ecdi m\u00e1y ch\u1ee7"
::msgcat::mcset vi "remote prune all remotes" "x\u00e9n b\u1edbt m\u1ecdi m\u00e1y ch\u1ee7"
::msgcat::mcset vi "Pruning tracking branches deleted from all remotes" "X\u00e9n t\u1ec9a c\u00e1c nh\u00e1nh \u0111\u00e3 theo d\u00f5i b\u1ecb x\u00f3a t\u1eeb m\u1ecdi m\u00e1y ch\u1ee7"
::msgcat::mcset vi "Pushing changes to %s" "\u0110ang \u0111\u1ea9y c\u00e1c nh\u00e1nh l\u00ean %s"
::msgcat::mcset vi "Mirroring to %s" "B\u1ea3n sao \u0111\u1ebfn %s"
::msgcat::mcset vi "Pushing %s %s to %s" "\u0110ang (\u0111\u1ea9y) %s %s l\u00ean %s"
::msgcat::mcset vi "Push Branches" "\u0110\u1ea9y l\u00ean c\u00e1c nh\u00e1nh"
::msgcat::mcset vi "Source Branches" "Nh\u00e1nh ngu\u1ed3n"
::msgcat::mcset vi "Destination Repository" "Kho ch\u1ee9a \u0111\u00edch"
::msgcat::mcset vi "Transfer Options" "T\u00f9y ch\u1ecdn truy\u1ec1n"
::msgcat::mcset vi "Force overwrite existing branch (may discard changes)" "\u00c9p bu\u1ed9c ghi \u0111\u00e8 nh\u00e1nh s\u1eb5n c\u00f3 (c\u00f3 th\u1ec3 s\u1ebd lo\u1ea1i b\u1ecf c\u00e1c thay \u0111\u1ed5i)"
::msgcat::mcset vi "Use thin pack (for slow network connections)" "D\u00f9ng g\u00f3i m\u1ecfng (d\u00e0nh cho k\u1ebft n\u1ed1i m\u1ea1ng ch\u1eadm)"
::msgcat::mcset vi "Include tags" "Bao g\u1ed3m c\u00e1c th\u1ebb"
