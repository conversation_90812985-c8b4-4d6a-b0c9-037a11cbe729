.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs12_bag_set_privkey" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs12_bag_set_privkey \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs12.h>
.sp
.BI "int gnutls_pkcs12_bag_set_privkey(gnutls_pkcs12_bag_t " bag ", gnutls_x509_privkey_t " privkey ", const char * " password ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs12_bag_t bag" 12
The bag
.IP "gnutls_x509_privkey_t privkey" 12
the private key to be copied.
.IP "const char * password" 12
the password to protect the key with (may be \fBNULL\fP)
.IP "unsigned flags" 12
should be one of \fBgnutls_pkcs_encrypt_flags_t\fP elements bitwise or'd
.SH "DESCRIPTION"
This function will insert the given private key into the
bag. This is just a wrapper over \fBgnutls_pkcs12_bag_set_data()\fP.
.SH "RETURNS"
the index of the added bag on success, or a negative
value on failure.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
