<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_get1_builtin_sigalgs</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_get1_builtin_sigalgs - get list of built-in signature algorithms</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/tls1.h&gt;

char *SSL_get1_builtin_sigalgs(OSSL_LIB_CTX *libctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Return the colon-separated list of built-in and available TLS signature algorithms. The string returned must be freed by the user using <a href="../man3/OPENSSL_free.html">OPENSSL_free(3)</a>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The string may be empty (strlen==0) if none of the built-in TLS signature algorithms can be activated, e.g., if suitable providers are missing.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>NULL may be returned if no memory could be allocated. Otherwise, a newly allocated string is always returned but it may have strlen == 0.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>This function was added in OpenSSL 3.4.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


