GENERATED
---------

Is this source file generated as part of the build or CMake process.

.. versionchanged:: 3.20
   Turning on the ``GENERATED`` source file property in one directory allows
   the associated source file to be used across directories without the need
   to manually setting that property for other directory scopes, too.
   Additionally, it may now be set only to boolean values, and may not be
   turned off once turned on.  See policy :policy:`CMP0118`.

.. versionchanged:: 3.30
   Whether or not a source file is generated is an all-or-nothing global
   property of the source.  Consequently, the ``GENERATED`` source file
   property is now visible in all directories.  See policy :policy:`CMP0163`.

Tells the internal CMake engine that a source file is generated by an outside
process such as another build step, or the execution of CMake itself.
This information is then used to exempt the file from any existence or
validity checks.

Any file that is

- created by the execution of commands such as
  :command:`add_custom_command` which run during the build
- listed as one of the ``BYPRODUCTS`` of an :command:`add_custom_command`
  or :command:`add_custom_target` command, or
- created by a CMake ``AUTOGEN`` operation such as :prop_tgt:`AUTOMOC`,
  :prop_tgt:`AUTORCC`, or :prop_tgt:`AUTOUIC`

will be marked with the ``GENERATED`` property.

When a generated file created as the ``OUTPUT`` of an
:command:`add_custom_command` command is explicitly listed as a source file
for any target in the same directory scope (which usually means the same
``CMakeLists.txt`` file), CMake will automatically create a dependency to
make sure the file is generated before building that target.

The :ref:`Makefile Generators` will remove ``GENERATED`` files during
``make clean``.

Generated sources may be hidden in some IDE tools, while in others they might
be shown. For the special case of sources generated by CMake's :prop_tgt:`AUTOMOC`,
:prop_tgt:`AUTORCC` or :prop_tgt:`AUTOUIC` functionality, the
:prop_gbl:`AUTOGEN_SOURCE_GROUP`, :prop_gbl:`AUTOMOC_SOURCE_GROUP`,
:prop_gbl:`AUTORCC_SOURCE_GROUP` and :prop_gbl:`AUTOUIC_SOURCE_GROUP` target
properties may influence where the generated sources are grouped in the project's
file lists.
