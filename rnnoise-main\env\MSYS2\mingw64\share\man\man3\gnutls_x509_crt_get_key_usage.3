.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_key_usage" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_key_usage \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_key_usage(gnutls_x509_crt_t " cert ", unsigned int * " key_usage ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "unsigned int * key_usage" 12
where the key usage bits will be stored
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical
.SH "DESCRIPTION"
This function will return certificate's key usage, by reading the
keyUsage X.509 extension (*********). The key usage value will ORed
values of the: \fBGNUTLS_KEY_DIGITAL_SIGNATURE\fP,
\fBGNUTLS_KEY_NON_REPUDIATION\fP, \fBGNUTLS_KEY_KEY_ENCIPHERMENT\fP,
\fBGNUTLS_KEY_DATA_ENCIPHERMENT\fP, \fBGNUTLS_KEY_KEY_AGREEMENT\fP,
\fBGNUTLS_KEY_KEY_CERT_SIGN\fP, \fBGNUTLS_KEY_CRL_SIGN\fP,
\fBGNUTLS_KEY_ENCIPHER_ONLY\fP, \fBGNUTLS_KEY_DECIPHER_ONLY\fP.
.SH "RETURNS"
zero on success, or a negative error code in case of
parsing error.  If the certificate does not contain the keyUsage
extension \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be
returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
