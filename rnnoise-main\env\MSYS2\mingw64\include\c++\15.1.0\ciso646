// -*- C++ -*- forwarding header.

// Copyright (C) 2001-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file ciso646
 *  This is a Standard C++ Library file.  You should @c \#include this file
 *  in your programs, rather than any of the @a *.h implementation files.
 *
 *  This is the C++ version of the Standard C Library header @c iso646.h,
 *  which is empty in C++.
 */
#ifndef _GLIBCXX_CISO646
#define _GLIBCXX_CISO646

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#include <bits/c++config.h>

#if __cplusplus >= 202002L && ! _GLIBCXX_USE_DEPRECATED
#  error "<ciso646> is not a standard header in C++20, use <version> to detect implementation-specific macros"
#elif __cplusplus >= 201703L && defined __DEPRECATED
#  pragma GCC diagnostic push
#  pragma GCC diagnostic ignored "-Wc++23-extensions"
#  warning "<ciso646> is deprecated in C++17, use <version> to detect implementation-specific macros"
#  pragma GCC diagnostic pop
#endif

#endif
