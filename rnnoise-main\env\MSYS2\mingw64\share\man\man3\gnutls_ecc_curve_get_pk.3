.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ecc_curve_get_pk" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ecc_curve_get_pk \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_pk_algorithm_t gnutls_ecc_curve_get_pk(gnutls_ecc_curve_t " curve ");"
.SH ARGUMENTS
.IP "gnutls_ecc_curve_t curve" 12
is an ECC curve
.SH "RETURNS"
the public key algorithm associated with the named curve or \fBGNUTLS_PK_UNKNOWN\fP.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
