CMAKE_COMPILE_PDB_OUTPUT_DIRECTORY_<CONFIG>
-------------------------------------------

.. versionadded:: 3.1

Per-configuration output directory for MS debug symbol ``.pdb`` files
generated by the compiler while building source files.

This is a per-configuration version of
:variable:`CMAKE_COMPILE_PDB_OUTPUT_DIRECTORY`.
This variable is used to initialize the
:prop_tgt:`COMPILE_PDB_OUTPUT_DIRECTORY_<CONFIG>`
property on all the targets.
