.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_get_ptr" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_get_ptr \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_obj_get_ptr(gnutls_pkcs11_obj_t " obj ", void ** " ptr ", void ** " session ", void ** " ohandle ", unsigned long * " slot_id ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_t obj" 12
should contain a \fBgnutls_pkcs11_obj_t\fP type
.IP "void ** ptr" 12
will contain the CK_FUNCTION_LIST_PTR pointer (may be \fBNULL\fP)
.IP "void ** session" 12
will contain the CK_SESSION_HANDLE of the object
.IP "void ** ohandle" 12
will contain the CK_OBJECT_HANDLE of the object
.IP "unsigned long * slot_id" 12
the identifier of the slot (may be \fBNULL\fP)
.IP "unsigned int flags" 12
Or sequence of GNUTLS_PKCS11_OBJ_* flags
.SH "DESCRIPTION"
Obtains the PKCS\fB11\fP session handles of an object.  \fIsession\fP and  \fIohandle\fP must be deinitialized by the caller. The returned pointers are
independent of the  \fIobj\fP lifetime.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success or a negative error code
on error.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
