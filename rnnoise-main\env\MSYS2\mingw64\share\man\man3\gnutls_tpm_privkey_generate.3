.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_tpm_privkey_generate" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_tpm_privkey_generate \- API function
.SH SYNOPSIS
.B #include <gnutls/tpm.h>
.sp
.BI "int gnutls_tpm_privkey_generate(gnutls_pk_algorithm_t " pk ", unsigned int " bits ", const char * " srk_password ", const char * " key_password ", gnutls_tpmkey_fmt_t " format ", gnutls_x509_crt_fmt_t " pub_format ", gnutls_datum_t * " privkey ", gnutls_datum_t * " pubkey ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pk_algorithm_t pk" 12
the public key algorithm
.IP "unsigned int bits" 12
the security bits
.IP "const char * srk_password" 12
a password to protect the exported key (optional)
.IP "const char * key_password" 12
the password for the TPM (optional)
.IP "gnutls_tpmkey_fmt_t format" 12
the format of the private key
.IP "gnutls_x509_crt_fmt_t pub_format" 12
the format of the public key
.IP "gnutls_datum_t * privkey" 12
the generated key
.IP "gnutls_datum_t * pubkey" 12
the corresponding public key (may be null)
.IP "unsigned int flags" 12
should be a list of GNUTLS_TPM_* flags
.SH "DESCRIPTION"
This function will generate a private key in the TPM
chip. The private key will be generated within the chip
and will be exported in a wrapped with TPM's master key
form. Furthermore the wrapped key can be protected with
the provided  \fIpassword\fP .

Note that bits in TPM is quantized value. If the input value
is not one of the allowed values, then it will be quantized to
one of 512, 1024, 2048, 4096, 8192 and 16384.

Allowed flags are:
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
