<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>pldd</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="passwd.html" title="passwd"><link rel="next" href="profiler.html" title="profiler"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">pldd</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="passwd.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="profiler.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="pldd"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>pldd &#8212; List dynamic shared objects loaded into a process</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">pldd</code>   <em class="replaceable"><code>PID</code></em> </p></div><div class="cmdsynopsis"><p><code class="command">pldd</code>    -?  |   --usage  |   -V  </p></div></div><div class="refsect1"><a name="pldd-options"></a><h2>Options</h2><pre class="screen">
  -?, --help                 Give this help list
      --usage                Give a short usage message
  -V, --version              Print program version
</pre></div><div class="refsect1"><a name="pldd-desc"></a><h2>Description</h2><p><span class="command"><strong>pldd</strong></span> prints the shared libraries (DLLs) loaded by
      the process with the given PID.</p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="passwd.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="profiler.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">passwd&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;profiler</td></tr></table></div></body></html>
