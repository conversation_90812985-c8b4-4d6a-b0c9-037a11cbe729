.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_set_client_credentials" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_set_client_credentials \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_psk_set_client_credentials(gnutls_psk_client_credentials_t " res ", const char * " username ", const gnutls_datum_t * " key ", gnutls_psk_key_flags " flags ");"
.SH ARGUMENTS
.IP "gnutls_psk_client_credentials_t res" 12
is a \fBgnutls_psk_client_credentials_t\fP type.
.IP "const char * username" 12
is the user's zero\-terminated userid
.IP "const gnutls_datum_t * key" 12
is the user's key
.IP "gnutls_psk_key_flags flags" 12
indicate the format of the key, either
\fBGNUTLS_PSK_KEY_RAW\fP or \fBGNUTLS_PSK_KEY_HEX\fP.
.SH "DESCRIPTION"
This function sets the username and password, in a
gnutls_psk_client_credentials_t type.  Those will be used in
PSK authentication.   \fIusername\fP should be an ASCII string or UTF\-8
string. In case of a UTF\-8 string it is recommended to be following
the PRECIS framework for usernames (rfc8265). The key can be either
in raw byte format or in Hex format (without the 0x prefix).
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
