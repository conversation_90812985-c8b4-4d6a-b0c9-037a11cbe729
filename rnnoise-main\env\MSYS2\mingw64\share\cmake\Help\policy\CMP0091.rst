CMP0091
-------

.. versionadded:: 3.15

MSVC runtime library flags are selected by an abstraction.

Compilers targeting the MSVC ABI have flags to select the MSVC runtime library.
Runtime library selection typically varies with build configuration because
there is a separate runtime library for Debug builds.

In CMake 3.14 and below, MSVC runtime library selection flags are added to
the default :variable:`CMAKE_<LANG>_FLAGS_<CONFIG>` cache entries by <PERSON><PERSON>ake
automatically.  This allows users to edit their cache entries to adjust the
flags.  However, the presence of such default flags is problematic for
projects that want to choose a different runtime library programmatically.
In particular, it requires string editing of the
:variable:`CMAKE_<LANG>_FLAGS_<CONFIG>` variables with knowledge of the
CMake builtin defaults so they can be replaced.

CMake 3.15 and above prefer to leave the MSVC runtime library selection flags
out of the default :variable:`CMAKE_<LANG>_FLAGS_<CONFIG>` values and instead
offer a first-class abstraction.  The :variable:`CMAKE_MSVC_RUNTIME_LIBRARY`
variable and :prop_tgt:`MSVC_RUNTIME_LIBRARY` target property may be set to
select the MSVC runtime library.  If they are not set then <PERSON><PERSON><PERSON> uses the
default value ``MultiThreaded$<$<CONFIG:Debug>:Debug>DLL`` which is
equivalent to the original flags.

This policy provides compatibility with projects that have not been updated
to be aware of the abstraction.  The policy setting takes effect as of the
first :command:`project` or :command:`enable_language` command that enables
a language whose compiler targets the MSVC ABI.

.. note::

  Once the policy has taken effect at the top of a project, that choice
  must be used throughout the tree.  In projects that have nested projects
  in subdirectories, be sure to convert everything together.

The ``OLD`` behavior for this policy is to place MSVC runtime library
flags in the default :variable:`CMAKE_<LANG>_FLAGS_<CONFIG>` cache
entries and ignore the :variable:`CMAKE_MSVC_RUNTIME_LIBRARY` abstraction.
The ``NEW`` behavior for this policy is to *not* place MSVC runtime
library flags in the default cache entries and use the abstraction instead.

.. |INTRODUCED_IN_CMAKE_VERSION| replace:: 3.15
.. |WARNS_OR_DOES_NOT_WARN| replace:: does *not* warn
.. include:: STANDARD_ADVICE.txt

.. include:: DEPRECATED.txt
