.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs8_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs8_info \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_pkcs8_info(const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " format ", unsigned int * " schema ", unsigned int * " cipher ", void * " salt ", unsigned int * " salt_size ", unsigned int * " iter_count ", char ** " oid ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * data" 12
Holds the PKCS \fB8\fP data
.IP "gnutls_x509_crt_fmt_t format" 12
the format of the PKCS \fB8\fP data
.IP "unsigned int * schema" 12
indicate the schema as one of \fBgnutls_pkcs_encrypt_flags_t\fP
.IP "unsigned int * cipher" 12
the cipher used as \fBgnutls_cipher_algorithm_t\fP
.IP "void * salt" 12
PBKDF2 salt (if non\-NULL then  \fIsalt_size\fP initially holds its size)
.IP "unsigned int * salt_size" 12
PBKDF2 salt size
.IP "unsigned int * iter_count" 12
PBKDF2 iteration count
.IP "char ** oid" 12
if non\-NULL it will contain an allocated null\-terminated variable with the OID
.SH "DESCRIPTION"
This function will provide information on the algorithms used
in a particular PKCS \fB8\fP structure. If the structure algorithms
are unknown the code \fBGNUTLS_E_UNKNOWN_CIPHER_TYPE\fP will be returned,
and only  \fIoid\fP , will be set. That is,  \fIoid\fP will be set on encrypted PKCS \fB8\fP
structures whether supported or not. It must be deinitialized using \fBgnutls_free()\fP.
The other variables are only set on supported structures.
.SH "RETURNS"
\fBGNUTLS_E_INVALID_REQUEST\fP if the provided structure isn't an encrypted key,
\fBGNUTLS_E_UNKNOWN_CIPHER_TYPE\fP if the structure's encryption isn't supported, or
another negative error code in case of a failure. Zero on success.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
