<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Compiling p11-kit from Source: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="devel.html" title="Building, Packaging, and Contributing to p11-kit">
<link rel="prev" href="devel-commands.html" title="Customizing installed commands">
<link rel="next" href="devel-building-style.html" title="Coding Style">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="devel.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="devel-commands.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="devel-building-style.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="devel-building"></a>Compiling p11-kit from Source</h2></div></div></div>
<p>This describes how to compiling the p11-kit package from
		source code. This is normally only necessary for those wishing to
		contribute to the project or package p11-kit.</p>
<p>You can download
		<a class="ulink" href="https://github.com/p11-glue/p11-kit/releases" target="_top">tarballs
		of the releases</a> of p11-kit or
		<a class="ulink" href="https://github.com/p11-glue/p11-kit/" target="_top">check
		out the source code from git</a>. This documentation will not
		go into all the details of how to get your development environment
		set up and instead focus on the what's unique to compiling p11-kit.</p>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="devel-building-unix"></a>Building on UNIX</h3></div></div></div>
<p>p11-kit uses the standard GNU build system, using autoconf for package
			configuration and resolving portability issues, automake for building makefiles
			that comply with the GNU Coding Standards, and libtool for building shared
			libraries on multiple platforms. The normal sequence for compiling and
			installing the p11-kit library is thus:</p>
<pre class="programlisting">
$ ./configure --prefix=/path/to/prefix ...
$ make
$ make install
</pre>
<p>If you've checked out the source code from git, then the
			<span class="command"><strong>configure</strong></span> script does not yet exist. So use
			the following instead:</p>
<pre class="programlisting">
$ ./autogen.sh --prefix=/path/to/prefix ...
$ make
$ make install
</pre>
<p>The standard options provided by GNU autoconf may be passed to the configure
			script. Please see the autoconf documentation or run <code class="literal">./configure --help</code>
			for information about the standard options. In particular you probably want to adjust
			the <code class="literal">--prefix=/xxx</code> argument depending on your system and development
			environment.</p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="devel-building-dependencies"></a>Optional Dependencies</h3></div></div></div>
<p>On a modern GNU Linux system, p11-kit has no required dependencies other
			than the standard C library. However on older UNIX systems, some of the following
			may be required.</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p><span class="command"><strong>gettext</strong></span> is required if your system doesn't
				have the <code class="literal">gettext()</code> functionality for handling message
				translation databases. This can be provided by the libintl library from
				the <a class="ulink" href="http://www.gnu.org/software/gettext" target="_top">GNU gettext
				package</a>.</p></li>
<li class="listitem"><p><span class="command"><strong>pthread</strong></span> is required if your (ancient) system
				doesn't have this included in the base system. How this is provided is platform
				specific.</p></li>
</ul></div>
<p>In addition p11-kit has several optional dependencies. If these are not available
			during the build, then certain features will be disabled.</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p><span class="command"><strong>libffi</strong></span> for sharing of PKCS#11 modules
				between multiple callers in the same process. It is highly recommended that
				this dependency be treated as a required dependency.</p></li>
<li class="listitem"><p><span class="command"><strong>gtk-doc</strong></span> is required to build the reference
				manual. Use <code class="literal">--enable-doc</code> to control this
				dependency.</p></li>
<li class="listitem"><p><span class="command"><strong>xsltproc</strong></span> is required to build the command
				manual pages. Use <code class="literal">--enable-doc</code> to control this
				dependency.</p></li>
<li class="listitem"><p><span class="command"><strong>libtasn1</strong></span> is required to build the trust
				module and code that interacts with certificates.</p></li>
<li class="listitem"><p><span class="command"><strong>freebl3</strong></span> (developed as part of the NSS
				code base) is an optional dependency that may be used to meet policy
				requirements of system builders. Enabling this dependency provides no other
				advantage.</p></li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="devel-building-configure"></a>Extra Configuration Options</h3></div></div></div>
<p>In addition to the normal options, the configure script in the p11-kit library
			supports these additional arguments:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--disable-trust-module</code></span></p></td>
<td><p>Disables building of the trust policy module.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--disable-debug</code>, <code class="option">--enable-debug</code></span></p></td>
<td><p>By default p11-kit is built with debug symbols assertions and
					and precondition checks. Enabling the debug option configures even more
					detailed debug build, including disabling optimization. Disabling the debug
					option is not recommended, as it disables all assertions, preconditions and
					internal consistency checks, although it may result it a slightly faster
					library.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--enable-doc</code></span></p></td>
<td><p>Enables building of the documentation and command line manual.
					The documentation is built in the <code class="literal">doc/html/</code> directory of
					the build. Requires the <code class="literal">gtk-doc</code> and <code class="literal">xsltproc</code>
					dependencies.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--enable-strict</code></span></p></td>
<td><p>Enables strict checks during building of p11-kit. All
					compiler warnings become errors.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--with-hash-impl=freebl</code></span></p></td>
<td><p>Instead of using internal hash code, link to the freebl3
					library and use its hash implementations. The only advantage this brings is to
					meet the policy requirements of system builders.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--with-libtasn1</code>, <code class="option">--without-libtasn1</code></span></p></td>
<td><p>Build with a dependency on the libtasn1 library. This dependency
					allows the trust policy module to be built as well as other code that interacts with
					certificates.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--with-module-path</code></span></p></td>
<td><p>Specify the path to look for PKCS#11 modules which were
					listed in a module config file with a relative path.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--with-trust-paths</code></span></p></td>
<td><p>Specify the files or directories to look for certificate
					anchors and blocklists. Multiple files and/or directories are specified with
					a <code class="literal">:</code> in between them. The first path has the highest
					priority when searching for certificates.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--with-system-config</code></span></p></td>
<td><p>Specify the path to look for p11-kit config files. This
					usually defaults to something like <code class="literal">/etc/pkcs11</code></p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--with-user-config</code></span></p></td>
<td><p>Specify the path to look for user specific p11-kit config files. If 
					specify a path that begins with <code class="literal">~/</code> then this is expanded to the
					home directory of the user running p11-kit. If you specify a path that begins with
					<code class="literal">~/.config/</code> then this is expanded to the $XDG_CONFIG_HOME directory,
					as outlined in the
					<a class="ulink" href="http://standards.freedesktop.org/basedir-spec/basedir-spec-latest.html#variables" target="_top">XDG Base Dir specification</a>.
					This option defaults to <code class="literal">~/.pkcs11</code></p></td>
</tr>
</tbody>
</table></div>
<p></p>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>