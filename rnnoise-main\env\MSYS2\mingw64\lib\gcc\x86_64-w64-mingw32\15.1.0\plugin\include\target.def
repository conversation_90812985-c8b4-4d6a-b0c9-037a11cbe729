/* Target hook definitions.
   Copyright (C) 2001-2025 Free Software Foundation, Inc.

   This program is free software; you can redistribute it and/or modify it
   under the terms of the GNU General Public License as published by the
   Free Software Foundation; either version 3, or (at your option) any
   later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   ME<PERSON><PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program; see the file COPYING3.  If not see
   <http://www.gnu.org/licenses/>.

   In other words, you are welcome to use, share and improve this program.
   You are forbidden to forbid anyone else to use, share and improve
   what you give them.   Help stamp out software-hoarding!  */

/* See target-hooks-macros.h for details of macros that should be
   provided by the including file, and how to use them here.  */
#include "target-hooks-macros.h"

#undef HOOK_TYPE
#define HOOK_TYPE "Target Hook"

HOOK_VECTOR (TARGET_INITIALIZER, gcc_target)

/* Functions that output assembler for the target.  */
#define HOOK_PREFIX "TARGET_ASM_"
HOOK_VECTOR (TARGET_ASM_OUT, asm_out)

/* Opening and closing parentheses for asm expression grouping.  */
DEFHOOKPOD
(open_paren,
 "These target hooks are C string constants, describing the syntax in the\n\
assembler for grouping arithmetic expressions.  If not overridden, they\n\
default to normal parentheses, which is correct for most assemblers.",
 const char *, "(")
DEFHOOKPODX (close_paren, const char *, ")")

/* Assembler instructions for creating various kinds of integer object.  */
DEFHOOKPOD
(byte_op,
 "@deftypevrx {Target Hook} {const char *} TARGET_ASM_ALIGNED_HI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_ALIGNED_PSI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_ALIGNED_SI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_ALIGNED_PDI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_ALIGNED_DI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_ALIGNED_PTI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_ALIGNED_TI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_UNALIGNED_HI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_UNALIGNED_PSI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_UNALIGNED_SI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_UNALIGNED_PDI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_UNALIGNED_DI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_UNALIGNED_PTI_OP\n\
@deftypevrx {Target Hook} {const char *} TARGET_ASM_UNALIGNED_TI_OP\n\
These hooks specify assembly directives for creating certain kinds\n\
of integer object.  The @code{TARGET_ASM_BYTE_OP} directive creates a\n\
byte-sized object, the @code{TARGET_ASM_ALIGNED_HI_OP} one creates an\n\
aligned two-byte object, and so on.  Any of the hooks may be\n\
@code{NULL}, indicating that no suitable directive is available.\n\
\n\
The compiler will print these strings at the start of a new line,\n\
followed immediately by the object's initial value.  In most cases,\n\
the string should contain a tab, a pseudo-op, and then another tab.",
 const char *, "\t.byte\t")
DEFHOOKPOD (aligned_op, "*", struct asm_int_op, TARGET_ASM_ALIGNED_INT_OP)
DEFHOOKPOD (unaligned_op, "*", struct asm_int_op, TARGET_ASM_UNALIGNED_INT_OP)

/* Try to output the assembler code for an integer object whose
   value is given by X.  SIZE is the size of the object in bytes and
   ALIGNED_P indicates whether it is aligned.  Return true if
   successful.  Only handles cases for which BYTE_OP, ALIGNED_OP
   and UNALIGNED_OP are NULL.  */
DEFHOOK
(integer,
 "The @code{assemble_integer} function uses this hook to output an\n\
integer object.  @var{x} is the object's value, @var{size} is its size\n\
in bytes and @var{aligned_p} indicates whether it is aligned.  The\n\
function should return @code{true} if it was able to output the\n\
object.  If it returns false, @code{assemble_integer} will try to\n\
split the object into smaller parts.\n\
\n\
The default implementation of this hook will use the\n\
@code{TARGET_ASM_BYTE_OP} family of strings, returning @code{false}\n\
when the relevant string is @code{NULL}.",
 /* Only handles cases for which BYTE_OP, ALIGNED_OP and UNALIGNED_OP are
    NULL.  */
 bool, (rtx x, unsigned int size, int aligned_p),
 default_assemble_integer)

/* Assembly strings required after the .cfi_startproc label.  */
DEFHOOK
(post_cfi_startproc,
  "This target hook is used to emit assembly strings required by the target\n\
after the .cfi_startproc directive.  The first argument is the file stream to\n\
write the strings to and the second argument is the function\'s declaration.  The\n\
expected use is to add more .cfi_* directives.\n\
\n\
The default is to not output any assembly strings.",
  void, (FILE *, tree),
  hook_void_FILEptr_tree)

/* Notify the backend that we have completed emitting the data for a
   decl.  */
DEFHOOK
(decl_end,
 "Define this hook if the target assembler requires a special marker to\n\
terminate an initialized variable declaration.",
 void, (void),
 hook_void_void)

/* Output code that will globalize a label.  */
DEFHOOK
(globalize_label,
 "This target hook is a function to output to the stdio stream\n\
@var{stream} some commands that will make the label @var{name} global;\n\
that is, available for reference from other files.\n\
\n\
The default implementation relies on a proper definition of\n\
@code{GLOBAL_ASM_OP}.",
 void, (FILE *stream, const char *name),
 default_globalize_label)

/* Output code that will globalize a declaration.  */
DEFHOOK
(globalize_decl_name,
 "This target hook is a function to output to the stdio stream\n\
@var{stream} some commands that will make the name associated with @var{decl}\n\
global; that is, available for reference from other files.\n\
\n\
The default implementation uses the TARGET_ASM_GLOBALIZE_LABEL target hook.",
 void, (FILE *stream, tree decl), default_globalize_decl_name)

/* Output code that will declare an external variable.  */
DEFHOOK
(assemble_undefined_decl,
 "This target hook is a function to output to the stdio stream\n\
@var{stream} some commands that will declare the name associated with\n\
@var{decl} which is not defined in the current translation unit.  Most\n\
assemblers do not require anything to be output in this case.",
 void, (FILE *stream, const char *name, const_tree decl),
 hook_void_FILEptr_constcharptr_const_tree)

/* Output code that will emit a label for unwind info, if this
   target requires such labels.  Second argument is the decl the
   unwind info is associated with, third is a boolean: true if
   this is for exception handling, fourth is a boolean: true if
   this is only a placeholder for an omitted FDE.  */
DEFHOOK
(emit_unwind_label,
 "This target hook emits a label at the beginning of each FDE@.  It\n\
should be defined on targets where FDEs need special labels, and it\n\
should write the appropriate label, for the FDE associated with the\n\
function declaration @var{decl}, to the stdio stream @var{stream}.\n\
The third argument, @var{for_eh}, is a boolean: true if this is for an\n\
exception table.  The fourth argument, @var{empty}, is a boolean:\n\
true if this is a placeholder label for an omitted FDE@.\n\
\n\
The default is that FDEs are not given nonlocal labels.",
 void, (FILE *stream, tree decl, int for_eh, int empty),
 default_emit_unwind_label)

/* Output code that will emit a label to divide up the exception table.  */
DEFHOOK
(emit_except_table_label,
 "This target hook emits a label at the beginning of the exception table.\n\
It should be defined on targets where it is desirable for the table\n\
to be broken up according to function.\n\
\n\
The default is that no label is emitted.",
 void, (FILE *stream),
 default_emit_except_table_label)

/* Emit a directive for setting the personality for the function.  */
DEFHOOK
(emit_except_personality,
 "If the target implements @code{TARGET_ASM_UNWIND_EMIT}, this hook may be\n\
used to emit a directive to install a personality hook into the unwind\n\
info.  This hook should not be used if dwarf2 unwind info is used.",
 void, (rtx personality),
 NULL)

/* If necessary, modify personality and LSDA references to handle
   indirection.  This is used when the assembler supports CFI directives.  */
DEFHOOK
(make_eh_symbol_indirect,
 "If necessary, modify personality and LSDA references to handle indirection.\n\
The original symbol is in @code{origsymbol} and if @code{pubvis} is true\n\
the symbol is visible outside the TU.",
 rtx, (rtx origsymbol, bool pubvis),
 NULL)

/* Emit any directives required to unwind this instruction.  */
DEFHOOK
(unwind_emit,
 "This target hook emits assembly directives required to unwind the\n\
given instruction.  This is only used when @code{TARGET_EXCEPT_UNWIND_INFO}\n\
returns @code{UI_TARGET}.",
 void, (FILE *stream, rtx_insn *insn),
 NULL)

DEFHOOKPOD
(unwind_emit_before_insn,
 "True if the @code{TARGET_ASM_UNWIND_EMIT} hook should be called before\n\
the assembly for @var{insn} has been emitted, false if the hook should\n\
be called afterward.",
 bool, true)

/* Return true if the target needs extra instructions to restore the current
   frame address after a DW_CFA_restore_state opcode.  */
DEFHOOK
(should_restore_cfa_state,
 "For DWARF-based unwind frames, two CFI instructions provide for save and\n\
restore of register state.  GCC maintains the current frame address (CFA)\n\
separately from the register bank but the unwinder in libgcc preserves this\n\
state along with the registers (and this is expected by the code that writes\n\
the unwind frames).  This hook allows the target to specify that the CFA data\n\
is not saved/restored along with the registers by the target unwinder so that\n\
suitable additional instructions should be emitted to restore it.",
 bool, (void),
 hook_bool_void_false)

/* Generate an internal label.
   For now this is just a wrapper for ASM_GENERATE_INTERNAL_LABEL.  */
DEFHOOK_UNDOC
(generate_internal_label,
 "",
 void, (char *buf, const char *prefix, unsigned long labelno),
 default_generate_internal_label)

/* Output an internal label.  */
DEFHOOK
(internal_label,
 "A function to output to the stdio stream @var{stream} a label whose\n\
name is made from the string @var{prefix} and the number @var{labelno}.\n\
\n\
It is absolutely essential that these labels be distinct from the labels\n\
used for user-level functions and variables.  Otherwise, certain programs\n\
will have name conflicts with internal labels.\n\
\n\
It is desirable to exclude internal labels from the symbol table of the\n\
object file.  Most assemblers have a naming convention for labels that\n\
should be excluded; on many systems, the letter @samp{L} at the\n\
beginning of a label has this effect.  You should find out what\n\
convention your system uses, and follow it.\n\
\n\
The default version of this function utilizes @code{ASM_GENERATE_INTERNAL_LABEL}.",
 void, (FILE *stream, const char *prefix, unsigned long labelno),
 default_internal_label)

/* Output label for the constant.  */
DEFHOOK
(declare_constant_name,
 "A target hook to output to the stdio stream @var{file} any text necessary\n\
for declaring the name @var{name} of a constant which is being defined.  This\n\
target hook is responsible for outputting the label definition (perhaps using\n\
@code{assemble_label}).  The argument @var{exp} is the value of the constant,\n\
and @var{size} is the size of the constant in bytes.  The @var{name}\n\
will be an internal label.\n\
\n\
The default version of this target hook, define the @var{name} in the\n\
usual manner as a label (by means of @code{assemble_label}).\n\
\n\
You may wish to use @code{ASM_OUTPUT_TYPE_DIRECTIVE} in this target hook.",
 void, (FILE *file, const char *name, const_tree expr, HOST_WIDE_INT size),
 default_asm_declare_constant_name)

/* Emit a ttype table reference to a typeinfo object.  */
DEFHOOK
(ttype,
 "This hook is used to output a reference from a frame unwinding table to\n\
the type_info object identified by @var{sym}.  It should return @code{true}\n\
if the reference was output.  Returning @code{false} will cause the\n\
reference to be output using the normal Dwarf2 routines.",
 bool, (rtx sym),
 hook_bool_rtx_false)

/* Emit an assembler directive to set visibility for the symbol
   associated with the tree decl.  */
DEFHOOK
(assemble_visibility,
 "This target hook is a function to output to @var{asm_out_file} some\n\
commands that will make the symbol(s) associated with @var{decl} have\n\
hidden, protected or internal visibility as specified by @var{visibility}.",
 void, (tree decl, int visibility),
 default_assemble_visibility)

DEFHOOK
(print_patchable_function_entry,
 "Generate a patchable area at the function start, consisting of\n\
@var{patch_area_size} NOP instructions.  If the target supports named\n\
sections and if @var{record_p} is true, insert a pointer to the current\n\
location in the table of patchable functions.  The default implementation\n\
of the hook places the table of pointers in the special section named\n\
@code{__patchable_function_entries}.",
 void, (FILE *file, unsigned HOST_WIDE_INT patch_area_size, bool record_p),
 default_print_patchable_function_entry)

/* Output the assembler code for entry to a function.  */
DEFHOOK
(function_prologue,
 "If defined, a function that outputs the assembler code for entry to a\n\
function.  The prologue is responsible for setting up the stack frame,\n\
initializing the frame pointer register, saving registers that must be\n\
saved, and allocating @var{size} additional bytes of storage for the\n\
local variables.  @var{file} is a stdio stream to which the assembler\n\
code should be output.\n\
\n\
The label for the beginning of the function need not be output by this\n\
macro.  That has already been done when the macro is run.\n\
\n\
@findex regs_ever_live\n\
To determine which registers to save, the macro can refer to the array\n\
@code{regs_ever_live}: element @var{r} is nonzero if hard register\n\
@var{r} is used anywhere within the function.  This implies the function\n\
prologue should save register @var{r}, provided it is not one of the\n\
call-used registers.  (@code{TARGET_ASM_FUNCTION_EPILOGUE} must likewise use\n\
@code{regs_ever_live}.)\n\
\n\
On machines that have ``register windows'', the function entry code does\n\
not save on the stack the registers that are in the windows, even if\n\
they are supposed to be preserved by function calls; instead it takes\n\
appropriate steps to ``push'' the register stack, if any non-call-used\n\
registers are used in the function.\n\
\n\
@findex frame_pointer_needed\n\
On machines where functions may or may not have frame-pointers, the\n\
function entry code must vary accordingly; it must set up the frame\n\
pointer if one is wanted, and not otherwise.  To determine whether a\n\
frame pointer is in wanted, the macro can refer to the variable\n\
@code{frame_pointer_needed}.  The variable's value will be 1 at run\n\
time in a function that needs a frame pointer.  @xref{Elimination}.\n\
\n\
The function entry code is responsible for allocating any stack space\n\
required for the function.  This stack space consists of the regions\n\
listed below.  In most cases, these regions are allocated in the\n\
order listed, with the last listed region closest to the top of the\n\
stack (the lowest address if @code{STACK_GROWS_DOWNWARD} is defined, and\n\
the highest address if it is not defined).  You can use a different order\n\
for a machine if doing so is more convenient or required for\n\
compatibility reasons.  Except in cases where required by standard\n\
or by a debugger, there is no reason why the stack layout used by GCC\n\
need agree with that used by other compilers for a machine.",
 void, (FILE *file),
 default_function_pro_epilogue)

/* Output the assembler code for end of prologue.  */
DEFHOOK
(function_end_prologue,
 "If defined, a function that outputs assembler code at the end of a\n\
prologue.  This should be used when the function prologue is being\n\
emitted as RTL, and you have some extra assembler that needs to be\n\
emitted.  @xref{prologue instruction pattern}.",
 void, (FILE *file),
 no_asm_to_stream)

/* Output the assembler code for start of epilogue.  */
DEFHOOK
(function_begin_epilogue,
 "If defined, a function that outputs assembler code at the start of an\n\
epilogue.  This should be used when the function epilogue is being\n\
emitted as RTL, and you have some extra assembler that needs to be\n\
emitted.  @xref{epilogue instruction pattern}.",
 void, (FILE *file),
 no_asm_to_stream)

/* Output the assembler code for function exit.  */
DEFHOOK
(function_epilogue,
 "If defined, a function that outputs the assembler code for exit from a\n\
function.  The epilogue is responsible for restoring the saved\n\
registers and stack pointer to their values when the function was\n\
called, and returning control to the caller.  This macro takes the\n\
same argument as the macro @code{TARGET_ASM_FUNCTION_PROLOGUE}, and the\n\
registers to restore are determined from @code{regs_ever_live} and\n\
@code{CALL_USED_REGISTERS} in the same way.\n\
\n\
On some machines, there is a single instruction that does all the work\n\
of returning from the function.  On these machines, give that\n\
instruction the name @samp{return} and do not define the macro\n\
@code{TARGET_ASM_FUNCTION_EPILOGUE} at all.\n\
\n\
Do not define a pattern named @samp{return} if you want the\n\
@code{TARGET_ASM_FUNCTION_EPILOGUE} to be used.  If you want the target\n\
switches to control whether return instructions or epilogues are used,\n\
define a @samp{return} pattern with a validity condition that tests the\n\
target switches appropriately.  If the @samp{return} pattern's validity\n\
condition is false, epilogues will be used.\n\
\n\
On machines where functions may or may not have frame-pointers, the\n\
function exit code must vary accordingly.  Sometimes the code for these\n\
two cases is completely different.  To determine whether a frame pointer\n\
is wanted, the macro can refer to the variable\n\
@code{frame_pointer_needed}.  The variable's value will be 1 when compiling\n\
a function that needs a frame pointer.\n\
\n\
Normally, @code{TARGET_ASM_FUNCTION_PROLOGUE} and\n\
@code{TARGET_ASM_FUNCTION_EPILOGUE} must treat leaf functions specially.\n\
The C variable @code{current_function_is_leaf} is nonzero for such a\n\
function.  @xref{Leaf Functions}.\n\
\n\
On some machines, some functions pop their arguments on exit while\n\
others leave that for the caller to do.  For example, the 68020 when\n\
given @option{-mrtd} pops arguments in functions that take a fixed\n\
number of arguments.\n\
\n\
@findex pops_args\n\
@findex crtl->args.pops_args\n\
Your definition of the macro @code{RETURN_POPS_ARGS} decides which\n\
functions pop their own arguments.  @code{TARGET_ASM_FUNCTION_EPILOGUE}\n\
needs to know what was decided.  The number of bytes of the current\n\
function's arguments that this function should pop is available in\n\
@code{crtl->args.pops_args}.  @xref{Scalar Return}.",
 void, (FILE *file),
 default_function_pro_epilogue)

/* Initialize target-specific sections.  */
DEFHOOK
(init_sections,
 "Define this hook if you need to do something special to set up the\n\
@file{varasm.cc} sections, or if your target has some special sections\n\
of its own that you need to create.\n\
\n\
GCC calls this hook after processing the command line, but before writing\n\
any assembly code, and before calling any of the section-returning hooks\n\
described below.",
 void, (void),
 hook_void_void)

/* Tell assembler to change to section NAME with attributes FLAGS.
   If DECL is non-NULL, it is the VAR_DECL or FUNCTION_DECL with
   which this section is associated.  */
DEFHOOK
(named_section,
 "Output assembly directives to switch to section @var{name}.  The section\n\
should have attributes as specified by @var{flags}, which is a bit mask\n\
of the @code{SECTION_*} flags defined in @file{output.h}.  If @var{decl}\n\
is non-NULL, it is the @code{VAR_DECL} or @code{FUNCTION_DECL} with which\n\
this section is associated.",
 void, (const char *name, unsigned int flags, tree decl),
 default_no_named_section)

/* Tell assembler what section attributes to assign this elf section
   declaration, using their numerical value.  */
DEFHOOK
(elf_flags_numeric,
 "This hook can be used to encode ELF section flags for which no letter\n\
code has been defined in the assembler.  It is called by\n\
@code{default_asm_named_section} whenever the section flags need to be\n\
emitted in the assembler output.  If the hook returns true, then the\n\
numerical value for ELF section flags should be calculated from\n\
@var{flags} and saved in @var{*num}; the value is printed out instead of the\n\
normal sequence of letter codes.  If the hook is not defined, or if it\n\
returns false, then @var{num} is ignored and the traditional letter sequence\n\
is emitted.",
 bool, (unsigned int flags, unsigned int *num),
 hook_bool_uint_uintp_false)

/* Return preferred text (sub)section for function DECL.
   Main purpose of this function is to separate cold, normal and hot
   functions. STARTUP is true when function is known to be used only
   at startup (from static constructors or it is main()).
   EXIT is true when function is known to be used only at exit
   (from static destructors).
   Return NULL if function should go to default text section.  */
DEFHOOK
(function_section,
 "Return preferred text (sub)section for function @var{decl}.\n\
Main purpose of this function is to separate cold, normal and hot\n\
functions. @var{startup} is true when function is known to be used only\n\
at startup (from static constructors or it is @code{main()}).\n\
@var{exit} is true when function is known to be used only at exit\n\
(from static destructors).\n\
Return NULL if function should go to default text section.",
 section *, (tree decl, enum node_frequency freq, bool startup, bool exit),
 default_function_section)

/* Output the assembler code for function exit.  */
DEFHOOK
(function_switched_text_sections,
 "Used by the target to emit any assembler directives or additional\n\
labels needed when a function is partitioned between different\n\
sections.  Output should be written to @var{file}.  The function\n\
decl is available as @var{decl} and the new section is `cold' if\n\
@var{new_is_cold} is @code{true}.",
 void, (FILE *file, tree decl, bool new_is_cold),
 default_function_switched_text_sections)

/* Return a mask describing how relocations should be treated when
   selecting sections.  Bit 1 should be set if global relocations
   should be placed in a read-write section; bit 0 should be set if
   local relocations should be placed in a read-write section.  */
DEFHOOK
(reloc_rw_mask,
 "Return a mask describing how relocations should be treated when\n\
selecting sections.  Bit 1 should be set if global relocations\n\
should be placed in a read-write section; bit 0 should be set if\n\
local relocations should be placed in a read-write section.\n\
\n\
The default version of this function returns 3 when @option{-fpic}\n\
is in effect, and 0 otherwise.  The hook is typically redefined\n\
when the target cannot support (some kinds of) dynamic relocations\n\
in read-only sections even in executables.",
 int, (void),
 default_reloc_rw_mask)

 /* Return a flag for either generating ADDR_DIF_VEC table
 or ADDR_VEC table for jumps in case of -fPIC/-fPIE.  */
DEFHOOK
(generate_pic_addr_diff_vec,
"Return true to generate ADDR_DIF_VEC table\n\
or false to generate ADDR_VEC table for jumps in case of -fPIC.\n\
\n\
The default version of this function returns true if flag_pic\n\
equals true and false otherwise",
 bool, (void),
 default_generate_pic_addr_diff_vec)

 /* Return a section for EXP.  It may be a DECL or a constant.  RELOC
    is nonzero if runtime relocations must be applied; bit 1 will be
    set if the runtime relocations require non-local name resolution.
    ALIGN is the required alignment of the data.  */
DEFHOOK
(select_section,
 "Return the section into which @var{exp} should be placed.  You can\n\
assume that @var{exp} is either a @code{VAR_DECL} node or a constant of\n\
some sort.  @var{reloc} indicates whether the initial value of @var{exp}\n\
requires link-time relocations.  Bit 0 is set when variable contains\n\
local relocations only, while bit 1 is set for global relocations.\n\
@var{align} is the constant alignment in bits.\n\
\n\
The default version of this function takes care of putting read-only\n\
variables in @code{readonly_data_section}.\n\
\n\
See also @var{USE_SELECT_SECTION_FOR_FUNCTIONS}.",
 section *, (tree exp, int reloc, unsigned HOST_WIDE_INT align),
 default_select_section)

/* Return a section for X.  MODE is X's mode and ALIGN is its
   alignment in bits.  */
DEFHOOK
(select_rtx_section,
 "Return the section into which a constant @var{x}, of mode @var{mode},\n\
should be placed.  You can assume that @var{x} is some kind of\n\
constant in RTL@.  The argument @var{mode} is redundant except in the\n\
case of a @code{const_int} rtx.  @var{align} is the constant alignment\n\
in bits.\n\
\n\
The default version of this function takes care of putting symbolic\n\
constants in @code{flag_pic} mode in @code{data_section} and everything\n\
else in @code{readonly_data_section}.",
 section *, (machine_mode mode, rtx x, unsigned HOST_WIDE_INT align),
 default_select_rtx_section)

/* Select a unique section name for DECL.  RELOC is the same as
   for SELECT_SECTION.  */
DEFHOOK
(unique_section,
 "Build up a unique section name, expressed as a @code{STRING_CST} node,\n\
and assign it to @samp{DECL_SECTION_NAME (@var{decl})}.\n\
As with @code{TARGET_ASM_SELECT_SECTION}, @var{reloc} indicates whether\n\
the initial value of @var{exp} requires link-time relocations.\n\
\n\
The default version of this function appends the symbol name to the\n\
ELF section name that would normally be used for the symbol.  For\n\
example, the function @code{foo} would be placed in @code{.text.foo}.\n\
Whatever the actual target object format, this is often good enough.",
 void, (tree decl, int reloc),
 default_unique_section)

/* Return the readonly data or relocated readonly data section
   associated with function DECL.  */
DEFHOOK
(function_rodata_section,
 "Return the readonly data or reloc readonly data section associated with\n\
@samp{DECL_SECTION_NAME (@var{decl})}. @var{relocatable} selects the latter\n\
over the former.\n\
The default version of this function selects @code{.gnu.linkonce.r.name} if\n\
the function's section is @code{.gnu.linkonce.t.name}, @code{.rodata.name}\n\
or @code{.data.rel.ro.name} if function is in @code{.text.name}, and\n\
the normal readonly-data or reloc readonly data section otherwise.",
 section *, (tree decl, bool relocatable),
 default_function_rodata_section)

/* Nonnull if the target wants to override the default ".rodata" prefix
   for mergeable data sections.  */
DEFHOOKPOD
(mergeable_rodata_prefix,
 "Usually, the compiler uses the prefix @code{\".rodata\"} to construct\n\
section names for mergeable constant data.  Define this macro to override\n\
the string if a different section name should be used.",
 const char *, ".rodata")

/* Return the section to be used for transactional memory clone tables.  */
DEFHOOK
(tm_clone_table_section,
 "Return the section that should be used for transactional memory clone\n\
tables.",
 section *, (void), default_clone_table_section)

/* Output a constructor for a symbol with a given priority.  */
DEFHOOK
(constructor,
 "If defined, a function that outputs assembler code to arrange to call\n\
the function referenced by @var{symbol} at initialization time.\n\
\n\
Assume that @var{symbol} is a @code{SYMBOL_REF} for a function taking\n\
no arguments and with no return value.  If the target supports initialization\n\
priorities, @var{priority} is a value between 0 and @code{MAX_INIT_PRIORITY};\n\
otherwise it must be @code{DEFAULT_INIT_PRIORITY}.\n\
\n\
If this macro is not defined by the target, a suitable default will\n\
be chosen if (1) the target supports arbitrary section names, (2) the\n\
target defines @code{CTORS_SECTION_ASM_OP}, or (3) @code{USE_COLLECT2}\n\
is not defined.",
 void, (rtx symbol, int priority), NULL)

/* Output a destructor for a symbol with a given priority.  */
DEFHOOK
(destructor,
 "This is like @code{TARGET_ASM_CONSTRUCTOR} but used for termination\n\
functions rather than initialization functions.",
 void, (rtx symbol, int priority), NULL)

/* Output the assembler code for a thunk function.  THUNK_DECL is the
   declaration for the thunk function itself, FUNCTION is the decl for
   the target function.  DELTA is an immediate constant offset to be
   added to THIS.  If VCALL_OFFSET is nonzero, the word at
   *(*this + vcall_offset) should be added to THIS.  */
DEFHOOK
(output_mi_thunk,
 "A function that outputs the assembler code for a thunk\n\
function, used to implement C++ virtual function calls with multiple\n\
inheritance.  The thunk acts as a wrapper around a virtual function,\n\
adjusting the implicit object parameter before handing control off to\n\
the real function.\n\
\n\
First, emit code to add the integer @var{delta} to the location that\n\
contains the incoming first argument.  Assume that this argument\n\
contains a pointer, and is the one used to pass the @code{this} pointer\n\
in C++.  This is the incoming argument @emph{before} the function prologue,\n\
e.g.@: @samp{%o0} on a sparc.  The addition must preserve the values of\n\
all other incoming arguments.\n\
\n\
Then, if @var{vcall_offset} is nonzero, an additional adjustment should be\n\
made after adding @code{delta}.  In particular, if @var{p} is the\n\
adjusted pointer, the following adjustment should be made:\n\
\n\
@smallexample\n\
p += (*((ptrdiff_t **)p))[vcall_offset/sizeof(ptrdiff_t)]\n\
@end smallexample\n\
\n\
After the additions, emit code to jump to @var{function}, which is a\n\
@code{FUNCTION_DECL}.  This is a direct pure jump, not a call, and does\n\
not touch the return address.  Hence returning from @var{FUNCTION} will\n\
return to whoever called the current @samp{thunk}.\n\
\n\
The effect must be as if @var{function} had been called directly with\n\
the adjusted first argument.  This macro is responsible for emitting all\n\
of the code for a thunk function; @code{TARGET_ASM_FUNCTION_PROLOGUE}\n\
and @code{TARGET_ASM_FUNCTION_EPILOGUE} are not invoked.\n\
\n\
The @var{thunk_fndecl} is redundant.  (@var{delta} and @var{function}\n\
have already been extracted from it.)  It might possibly be useful on\n\
some targets, but probably not.\n\
\n\
If you do not define this macro, the target-independent code in the C++\n\
front end will generate a less efficient heavyweight thunk that calls\n\
@var{function} instead of jumping to it.  The generic approach does\n\
not support varargs.",
 void, (FILE *file, tree thunk_fndecl, HOST_WIDE_INT delta,
	HOST_WIDE_INT vcall_offset, tree function),
 NULL)

/* Determine whether output_mi_thunk would succeed.  */
/* ??? Ideally, this hook would not exist, and success or failure
   would be returned from output_mi_thunk directly.  But there's
   too much undo-able setup involved in invoking output_mi_thunk.
   Could be fixed by making output_mi_thunk emit rtl instead of
   text to the output file.  */
DEFHOOK
(can_output_mi_thunk,
 "A function that returns true if TARGET_ASM_OUTPUT_MI_THUNK would be able\n\
to output the assembler code for the thunk function specified by the\n\
arguments it is passed, and false otherwise.  In the latter case, the\n\
generic approach will be used by the C++ front end, with the limitations\n\
previously exposed.",
 bool, (const_tree thunk_fndecl, HOST_WIDE_INT delta,
	HOST_WIDE_INT vcall_offset, const_tree function),
 hook_bool_const_tree_hwi_hwi_const_tree_false)

/* Output any boilerplate text needed at the beginning of a
   translation unit.  */
DEFHOOK
(file_start,
 "Output to @code{asm_out_file} any text which the assembler expects to\n\
find at the beginning of a file.  The default behavior is controlled\n\
by two flags, documented below.  Unless your target's assembler is\n\
quite unusual, if you override the default, you should call\n\
@code{default_file_start} at some point in your target hook.  This\n\
lets other target files rely on these variables.",
 void, (void),
 default_file_start)

/* Output any boilerplate text needed at the end of a translation unit.  */
DEFHOOK
(file_end,
 "Output to @code{asm_out_file} any text which the assembler expects\n\
to find at the end of a file.  The default is to output nothing.",
 void, (void),
 hook_void_void)

/* Output any boilerplate text needed at the beginning of an
   LTO output stream.  */
DEFHOOK
(lto_start,
 "Output to @code{asm_out_file} any text which the assembler expects\n\
to find at the start of an LTO section.  The default is to output\n\
nothing.",
 void, (void),
 hook_void_void)

/* Output any boilerplate text needed at the end of an
   LTO output stream.  */
DEFHOOK
(lto_end,
 "Output to @code{asm_out_file} any text which the assembler expects\n\
to find at the end of an LTO section.  The default is to output\n\
nothing.",
 void, (void),
 hook_void_void)

/* Output any boilerplace text needed at the end of a
   translation unit before debug and unwind info is emitted.  */
DEFHOOK
(code_end,
 "Output to @code{asm_out_file} any text which is needed before emitting\n\
unwind info and debug info at the end of a file.  Some targets emit\n\
here PIC setup thunks that cannot be emitted at the end of file,\n\
because they couldn't have unwind info then.  The default is to output\n\
nothing.",
 void, (void),
 hook_void_void)

/* Output an assembler pseudo-op to declare a library function name
   external.  */
DEFHOOK
(external_libcall,
 "This target hook is a function to output to @var{asm_out_file} an assembler\n\
pseudo-op to declare a library function name external.  The name of the\n\
library function is given by @var{symref}, which is a @code{symbol_ref}.",
 void, (rtx symref),
 default_external_libcall)

/* Output an assembler directive to mark decl live. This instructs
   linker to not dead code strip this symbol.  */
DEFHOOK
(mark_decl_preserved,
 "This target hook is a function to output to @var{asm_out_file} an assembler\n\
directive to annotate @var{symbol} as used.  The Darwin target uses the\n\
.no_dead_code_strip directive.",
 void, (const char *symbol),
 hook_void_constcharptr)

/* Output a record of the command line switches that have been passed.  */
DEFHOOK
(record_gcc_switches,
 "Provides the target with the ability to record the gcc command line\n\
switches provided as argument.\n\
\n\
By default this hook is set to NULL, but an example implementation is\n\
provided for ELF based targets.  Called @var{elf_record_gcc_switches},\n\
it records the switches as ASCII text inside a new, string mergeable\n\
section in the assembler output file.  The name of the new section is\n\
provided by the @code{TARGET_ASM_RECORD_GCC_SWITCHES_SECTION} target\n\
hook.",
 void, (const char *),
 NULL)

/* The name of the section that the example ELF implementation of
   record_gcc_switches will use to store the information.  Target
   specific versions of record_gcc_switches may or may not use
   this information.  */
DEFHOOKPOD
(record_gcc_switches_section,
 "This is the name of the section that will be created by the example\n\
ELF implementation of the @code{TARGET_ASM_RECORD_GCC_SWITCHES} target\n\
hook.",
 const char *, ".GCC.command.line")

/* Output the definition of a section anchor.  */
DEFHOOK
(output_anchor,
 "Write the assembly code to define section anchor @var{x}, which is a\n\
@code{SYMBOL_REF} for which @samp{SYMBOL_REF_ANCHOR_P (@var{x})} is true.\n\
The hook is called with the assembly output position set to the beginning\n\
of @code{SYMBOL_REF_BLOCK (@var{x})}.\n\
\n\
If @code{ASM_OUTPUT_DEF} is available, the hook's default definition uses\n\
it to define the symbol as @samp{. + SYMBOL_REF_BLOCK_OFFSET (@var{x})}.\n\
If @code{ASM_OUTPUT_DEF} is not available, the hook's default definition\n\
is @code{NULL}, which disables the use of section anchors altogether.",
 void, (rtx x),
 default_asm_output_anchor)

DEFHOOK
(output_ident,
 "Output a string based on @var{name}, suitable for the @samp{#ident}\n\
directive, or the equivalent directive or pragma in non-C-family languages.\n\
If this hook is not defined, nothing is output for the @samp{#ident}\n\
directive.",
 void, (const char *name),
 hook_void_constcharptr)

/* Output a DTP-relative reference to a TLS symbol.  */
DEFHOOK
(output_dwarf_dtprel,
 "If defined, this target hook is a function which outputs a DTP-relative\n\
reference to the given TLS symbol of the specified size.",
 void, (FILE *file, int size, rtx x),
 NULL)

/* Some target machines need to postscan each insn after it is output.  */
DEFHOOK
(final_postscan_insn,
 "If defined, this target hook is a function which is executed just after the\n\
output of assembler code for @var{insn}, to change the mode of the assembler\n\
if necessary.\n\
\n\
Here the argument @var{opvec} is the vector containing the operands\n\
extracted from @var{insn}, and @var{noperands} is the number of\n\
elements of the vector which contain meaningful data for this insn.\n\
The contents of this vector are what was used to convert the insn\n\
template into assembler code, so you can change the assembler mode\n\
by checking the contents of the vector.",
 void, (FILE *file, rtx_insn *insn, rtx *opvec, int noperands),
 NULL)

/* Emit the trampoline template.  This hook may be NULL.  */
DEFHOOK
(trampoline_template,
 "This hook is called by @code{assemble_trampoline_template} to output,\n\
on the stream @var{f}, assembler code for a block of data that contains\n\
the constant parts of a trampoline.  This code should not include a\n\
label---the label is taken care of automatically.\n\
\n\
If you do not define this hook, it means no template is needed\n\
for the target.  Do not define this hook on systems where the block move\n\
code to copy the trampoline into place would be larger than the code\n\
to generate it on the spot.",
 void, (FILE *f),
 NULL)

DEFHOOK
(output_source_filename,
 "Output DWARF debugging information which indicates that filename\n\
@var{name} is the current source file to the stdio stream @var{file}.\n\
\n\
This target hook need not be defined if the standard form of output\n\
for the file format in use is appropriate.",
 void ,(FILE *file, const char *name),
 default_asm_output_source_filename)

DEFHOOK
(output_addr_const_extra,
 "A target hook to recognize @var{rtx} patterns that @code{output_addr_const}\n\
can't deal with, and output assembly code to @var{file} corresponding to\n\
the pattern @var{x}.  This may be used to allow machine-dependent\n\
@code{UNSPEC}s to appear within constants.\n\
\n\
If target hook fails to recognize a pattern, it must return @code{false},\n\
so that a standard error message is printed.  If it prints an error message\n\
itself, by calling, for example, @code{output_operand_lossage}, it may just\n\
return @code{true}.",
 bool, (FILE *file, rtx x),
 hook_bool_FILEptr_rtx_false)

/* ??? The TARGET_PRINT_OPERAND* hooks are part of the asm_out struct,
   even though that is not reflected in the macro name to override their
   initializers.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_"

/* Emit a machine-specific insn operand.  */
/* ??? tm.texi only documents the old macro PRINT_OPERAND,
   not this  hook, and uses a different name for the argument FILE.  */
DEFHOOK_UNDOC
(print_operand,
 "",
 void, (FILE *file, rtx x, int code),
 default_print_operand)

/* Emit a machine-specific memory address.  */
/* ??? tm.texi only documents the old macro PRINT_OPERAND_ADDRESS,
   not this  hook, and uses different argument names.  */
DEFHOOK_UNDOC
(print_operand_address,
 "",
 void, (FILE *file, machine_mode mode, rtx addr),
 default_print_operand_address)

/* Determine whether CODE is a valid punctuation character for the
   `print_operand' hook.  */
/* ??? tm.texi only documents the old macro PRINT_OPERAND_PUNCT_VALID_P,
   not this  hook.  */
DEFHOOK_UNDOC
(print_operand_punct_valid_p,
 "",
 bool ,(unsigned char code),
 default_print_operand_punct_valid_p)

/* Given a symbol name, perform same mangling as assemble_name and
   ASM_OUTPUT_LABELREF, returning result as an IDENTIFIER_NODE.  */
DEFHOOK
(mangle_assembler_name,
 "Given a symbol @var{name}, perform same mangling as @code{varasm.cc}'s\n\
@code{assemble_name}, but in memory rather than to a file stream, returning\n\
result as an @code{IDENTIFIER_NODE}.  Required for correct LTO symtabs.  The\n\
default implementation calls the @code{TARGET_STRIP_NAME_ENCODING} hook and\n\
then prepends the @code{USER_LABEL_PREFIX}, if any.",
 tree, (const char *name),
 default_mangle_assembler_name)

HOOK_VECTOR_END (asm_out)

/* Functions relating to instruction scheduling.  All of these
   default to null pointers, which haifa-sched.cc looks for and handles.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_SCHED_"
HOOK_VECTOR (TARGET_SCHED, sched)

/* Given the current cost, COST, of an insn, INSN, calculate and
   return a new cost based on its relationship to DEP_INSN through
   the dependence LINK.  The default is to make no adjustment.  */
DEFHOOK
(adjust_cost,
 "This function corrects the value of @var{cost} based on the\n\
relationship between @var{insn} and @var{dep_insn} through a\n\
dependence of type dep_type, and strength @var{dw}.  It should return the new\n\
value.  The default is to make no adjustment to @var{cost}.  This can be\n\
used for example to specify to the scheduler using the traditional pipeline\n\
description that an output- or anti-dependence does not incur the same cost\n\
as a data-dependence.  If the scheduler using the automaton based pipeline\n\
description, the cost of anti-dependence is zero and the cost of\n\
output-dependence is maximum of one and the difference of latency\n\
times of the first and the second insns.  If these values are not\n\
acceptable, you could use the hook to modify them too.  See also\n\
@pxref{Processor pipeline description}.",
 int, (rtx_insn *insn, int dep_type1, rtx_insn *dep_insn, int cost,
       unsigned int dw),
 NULL)

/* Adjust the priority of an insn as you see fit.  Returns the new priority.  */
DEFHOOK
(adjust_priority,
 "This hook adjusts the integer scheduling priority @var{priority} of\n\
@var{insn}.  It should return the new priority.  Increase the priority to\n\
execute @var{insn} earlier, reduce the priority to execute @var{insn}\n\
later.  Do not define this hook if you do not need to adjust the\n\
scheduling priorities of insns.",
 int, (rtx_insn *insn, int priority), NULL)

/* Function which returns the maximum number of insns that can be
   scheduled in the same machine cycle.  This must be constant
   over an entire compilation.  The default is 1.  */
DEFHOOK
(issue_rate,
 "This hook returns the maximum number of instructions that can ever\n\
issue at the same time on the target machine.  The default is one.\n\
Although the insn scheduler can define itself the possibility of issue\n\
an insn on the same cycle, the value can serve as an additional\n\
constraint to issue insns on the same simulated processor cycle (see\n\
hooks @samp{TARGET_SCHED_REORDER} and @samp{TARGET_SCHED_REORDER2}).\n\
This value must be constant over the entire compilation.  If you need\n\
it to vary depending on what the instructions are, you must use\n\
@samp{TARGET_SCHED_VARIABLE_ISSUE}.",
 int, (void), NULL)

/* Calculate how much this insn affects how many more insns we
   can emit this cycle.  Default is they all cost the same.  */
DEFHOOK
(variable_issue,
 "This hook is executed by the scheduler after it has scheduled an insn\n\
from the ready list.  It should return the number of insns which can\n\
still be issued in the current cycle.  The default is\n\
@samp{@w{@var{more} - 1}} for insns other than @code{CLOBBER} and\n\
@code{USE}, which normally are not counted against the issue rate.\n\
You should define this hook if some insns take more machine resources\n\
than others, so that fewer insns can follow them in the same cycle.\n\
@var{file} is either a null pointer, or a stdio stream to write any\n\
debug output to.  @var{verbose} is the verbose level provided by\n\
@option{-fsched-verbose-@var{n}}.  @var{insn} is the instruction that\n\
was scheduled.",
 int, (FILE *file, int verbose, rtx_insn *insn, int more), NULL)

/* Initialize machine-dependent scheduling code.  */
DEFHOOK
(init,
 "This hook is executed by the scheduler at the beginning of each block of\n\
instructions that are to be scheduled.  @var{file} is either a null\n\
pointer, or a stdio stream to write any debug output to.  @var{verbose}\n\
is the verbose level provided by @option{-fsched-verbose-@var{n}}.\n\
@var{max_ready} is the maximum number of insns in the current scheduling\n\
region that can be live at the same time.  This can be used to allocate\n\
scratch space if it is needed, e.g.@: by @samp{TARGET_SCHED_REORDER}.",
 void, (FILE *file, int verbose, int max_ready), NULL)

/* Finalize machine-dependent scheduling code.  */
DEFHOOK
(finish,
 "This hook is executed by the scheduler at the end of each block of\n\
instructions that are to be scheduled.  It can be used to perform\n\
cleanup of any actions done by the other scheduling hooks.  @var{file}\n\
is either a null pointer, or a stdio stream to write any debug output\n\
to.  @var{verbose} is the verbose level provided by\n\
@option{-fsched-verbose-@var{n}}.",
 void, (FILE *file, int verbose), NULL)

 /* Initialize machine-dependent function wide scheduling code.  */
DEFHOOK
(init_global,
 "This hook is executed by the scheduler after function level initializations.\n\
@var{file} is either a null pointer, or a stdio stream to write any debug output to.\n\
@var{verbose} is the verbose level provided by @option{-fsched-verbose-@var{n}}.\n\
@var{old_max_uid} is the maximum insn uid when scheduling begins.",
 void, (FILE *file, int verbose, int old_max_uid), NULL)

/* Finalize machine-dependent function wide scheduling code.  */
DEFHOOK
(finish_global,
 "This is the cleanup hook corresponding to @code{TARGET_SCHED_INIT_GLOBAL}.\n\
@var{file} is either a null pointer, or a stdio stream to write any debug output to.\n\
@var{verbose} is the verbose level provided by @option{-fsched-verbose-@var{n}}.",
 void, (FILE *file, int verbose), NULL)

/* Reorder insns in a machine-dependent fashion, in two different
       places.  Default does nothing.  */
DEFHOOK
(reorder,
 "This hook is executed by the scheduler after it has scheduled the ready\n\
list, to allow the machine description to reorder it (for example to\n\
combine two small instructions together on @samp{VLIW} machines).\n\
@var{file} is either a null pointer, or a stdio stream to write any\n\
debug output to.  @var{verbose} is the verbose level provided by\n\
@option{-fsched-verbose-@var{n}}.  @var{ready} is a pointer to the ready\n\
list of instructions that are ready to be scheduled.  @var{n_readyp} is\n\
a pointer to the number of elements in the ready list.  The scheduler\n\
reads the ready list in reverse order, starting with\n\
@var{ready}[@var{*n_readyp} @minus{} 1] and going to @var{ready}[0].  @var{clock}\n\
is the timer tick of the scheduler.  You may modify the ready list and\n\
the number of ready insns.  The return value is the number of insns that\n\
can issue this cycle; normally this is just @code{issue_rate}.  See also\n\
@samp{TARGET_SCHED_REORDER2}.",
 int, (FILE *file, int verbose, rtx_insn **ready, int *n_readyp, int clock), NULL)

DEFHOOK
(reorder2,
 "Like @samp{TARGET_SCHED_REORDER}, but called at a different time.  That\n\
function is called whenever the scheduler starts a new cycle.  This one\n\
is called once per iteration over a cycle, immediately after\n\
@samp{TARGET_SCHED_VARIABLE_ISSUE}; it can reorder the ready list and\n\
return the number of insns to be scheduled in the same cycle.  Defining\n\
this hook can be useful if there are frequent situations where\n\
scheduling one insn causes other insns to become ready in the same\n\
cycle.  These other insns can then be taken into account properly.",
 int, (FILE *file, int verbose, rtx_insn **ready, int *n_readyp, int clock), NULL)

DEFHOOK
(macro_fusion_p,
 "This hook is used to check whether target platform supports macro fusion.",
 bool, (void), NULL)

DEFHOOK
(macro_fusion_pair_p,
 "This hook is used to check whether two insns should be macro fused for\n\
a target microarchitecture. If this hook returns true for the given insn pair\n\
(@var{prev} and @var{curr}), the scheduler will put them into a sched\n\
group, and they will not be scheduled apart.  The two insns will be either\n\
two SET insns or a compare and a conditional jump and this hook should\n\
validate any dependencies needed to fuse the two insns together.",
 bool, (rtx_insn *prev, rtx_insn *curr), NULL)

/* The following member value is a pointer to a function called
   after evaluation forward dependencies of insns in chain given
   by two parameter values (head and tail correspondingly).  */
DEFHOOK
(dependencies_evaluation_hook,
 "This hook is called after evaluation forward dependencies of insns in\n\
chain given by two parameter values (@var{head} and @var{tail}\n\
correspondingly) but before insns scheduling of the insn chain.  For\n\
example, it can be used for better insn classification if it requires\n\
analysis of dependencies.  This hook can use backward and forward\n\
dependencies of the insn scheduler because they are already\n\
calculated.",
 void, (rtx_insn *head, rtx_insn *tail), NULL)

/* The values of the following four members are pointers to functions
   used to simplify the automaton descriptions.  dfa_pre_cycle_insn and
   dfa_post_cycle_insn give functions returning insns which are used to
   change the pipeline hazard recognizer state when the new simulated
   processor cycle correspondingly starts and finishes.  The function
   defined by init_dfa_pre_cycle_insn and init_dfa_post_cycle_insn are
   used to initialize the corresponding insns.  The default values of
   the members result in not changing the automaton state when the
   new simulated processor cycle correspondingly starts and finishes.  */

DEFHOOK
(init_dfa_pre_cycle_insn,
 "The hook can be used to initialize data used by the previous hook.",
 void, (void), NULL)

DEFHOOK
(dfa_pre_cycle_insn,
 "The hook returns an RTL insn.  The automaton state used in the\n\
pipeline hazard recognizer is changed as if the insn were scheduled\n\
when the new simulated processor cycle starts.  Usage of the hook may\n\
simplify the automaton pipeline description for some @acronym{VLIW}\n\
processors.  If the hook is defined, it is used only for the automaton\n\
based pipeline description.  The default is not to change the state\n\
when the new simulated processor cycle starts.",
 rtx, (void), NULL)

DEFHOOK
(init_dfa_post_cycle_insn,
 "The hook is analogous to @samp{TARGET_SCHED_INIT_DFA_PRE_CYCLE_INSN} but\n\
used to initialize data used by the previous hook.",
 void, (void), NULL)

DEFHOOK
(dfa_post_cycle_insn,
 "The hook is analogous to @samp{TARGET_SCHED_DFA_PRE_CYCLE_INSN} but used\n\
to changed the state as if the insn were scheduled when the new\n\
simulated processor cycle finishes.",
 rtx_insn *, (void), NULL)

/* The values of the following two members are pointers to
   functions used to simplify the automaton descriptions.
   dfa_pre_advance_cycle and dfa_post_advance_cycle are getting called
   immediately before and after cycle is advanced.  */

DEFHOOK
(dfa_pre_advance_cycle,
 "The hook to notify target that the current simulated cycle is about to finish.\n\
The hook is analogous to @samp{TARGET_SCHED_DFA_PRE_CYCLE_INSN} but used\n\
to change the state in more complicated situations - e.g., when advancing\n\
state on a single insn is not enough.",
 void, (void), NULL)

DEFHOOK
(dfa_post_advance_cycle,
 "The hook to notify target that new simulated cycle has just started.\n\
The hook is analogous to @samp{TARGET_SCHED_DFA_POST_CYCLE_INSN} but used\n\
to change the state in more complicated situations - e.g., when advancing\n\
state on a single insn is not enough.",
 void, (void), NULL)

/* The following member value is a pointer to a function returning value
   which defines how many insns in queue `ready' will we try for
   multi-pass scheduling.  If the member value is nonzero and the
   function returns positive value, the DFA based scheduler will make
   multi-pass scheduling for the first cycle.  In other words, we will
   try to choose ready insn which permits to start maximum number of
   insns on the same cycle.  */
DEFHOOK
(first_cycle_multipass_dfa_lookahead,
 "This hook controls better choosing an insn from the ready insn queue\n\
for the @acronym{DFA}-based insn scheduler.  Usually the scheduler\n\
chooses the first insn from the queue.  If the hook returns a positive\n\
value, an additional scheduler code tries all permutations of\n\
@samp{TARGET_SCHED_FIRST_CYCLE_MULTIPASS_DFA_LOOKAHEAD ()}\n\
subsequent ready insns to choose an insn whose issue will result in\n\
maximal number of issued insns on the same cycle.  For the\n\
@acronym{VLIW} processor, the code could actually solve the problem of\n\
packing simple insns into the @acronym{VLIW} insn.  Of course, if the\n\
rules of @acronym{VLIW} packing are described in the automaton.\n\
\n\
This code also could be used for superscalar @acronym{RISC}\n\
processors.  Let us consider a superscalar @acronym{RISC} processor\n\
with 3 pipelines.  Some insns can be executed in pipelines @var{A} or\n\
@var{B}, some insns can be executed only in pipelines @var{B} or\n\
@var{C}, and one insn can be executed in pipeline @var{B}.  The\n\
processor may issue the 1st insn into @var{A} and the 2nd one into\n\
@var{B}.  In this case, the 3rd insn will wait for freeing @var{B}\n\
until the next cycle.  If the scheduler issues the 3rd insn the first,\n\
the processor could issue all 3 insns per cycle.\n\
\n\
Actually this code demonstrates advantages of the automaton based\n\
pipeline hazard recognizer.  We try quickly and easy many insn\n\
schedules to choose the best one.\n\
\n\
The default is no multipass scheduling.",
 int, (void), NULL)

/* The following member value is pointer to a function controlling
   what insns from the ready insn queue will be considered for the
   multipass insn scheduling.  If the hook returns zero for insn
   passed as the parameter, the insn will be not chosen to be issued.  */
DEFHOOK
(first_cycle_multipass_dfa_lookahead_guard,
 "\n\
This hook controls what insns from the ready insn queue will be\n\
considered for the multipass insn scheduling.  If the hook returns\n\
zero for @var{insn}, the insn will be considered in multipass scheduling.\n\
Positive return values will remove @var{insn} from consideration on\n\
the current round of multipass scheduling.\n\
Negative return values will remove @var{insn} from consideration for given\n\
number of cycles.\n\
Backends should be careful about returning non-zero for highest priority\n\
instruction at position 0 in the ready list.  @var{ready_index} is passed\n\
to allow backends make correct judgements.\n\
\n\
The default is that any ready insns can be chosen to be issued.",
 int, (rtx_insn *insn, int ready_index), NULL)

/* This hook prepares the target for a new round of multipass
   scheduling.
   DATA is a pointer to target-specific data used for multipass scheduling.
   READY_TRY and N_READY represent the current state of search in the
   optimization space.  The target can filter out instructions that
   should not be tried during current round by setting corresponding
   elements in READY_TRY to non-zero.
   FIRST_CYCLE_INSN_P is true if this is the first round of multipass
   scheduling on current cycle.  */
DEFHOOK
(first_cycle_multipass_begin,
 "This hook prepares the target backend for a new round of multipass\n\
scheduling.",
 void, (void *data, signed char *ready_try, int n_ready, bool first_cycle_insn_p),
 NULL)

/* This hook is called when multipass scheduling evaluates instruction INSN.
   DATA is a pointer to target-specific data that can be used to record effects
   of INSN on CPU that are not described in DFA.
   READY_TRY and N_READY represent the current state of search in the
   optimization space.  The target can filter out instructions that
   should not be tried after issuing INSN by setting corresponding
   elements in READY_TRY to non-zero.
   INSN is the instruction being evaluated.
   PREV_DATA is a pointer to target-specific data corresponding
   to a state before issuing INSN.  */
DEFHOOK
(first_cycle_multipass_issue,
 "This hook is called when multipass scheduling evaluates instruction INSN.",
 void, (void *data, signed char *ready_try, int n_ready, rtx_insn *insn,
	const void *prev_data), NULL)

/* This hook is called when multipass scheduling backtracks from evaluation of
   instruction corresponding to DATA.
   DATA is a pointer to target-specific data that stores the effects
   of instruction from which the algorithm backtracks on CPU that are not
   described in DFA.
   READY_TRY and N_READY represent the current state of search in the
   optimization space.  The target can filter out instructions that
   should not be tried after issuing INSN by setting corresponding
   elements in READY_TRY to non-zero.  */
DEFHOOK
(first_cycle_multipass_backtrack,
 "This is called when multipass scheduling backtracks from evaluation of\n\
an instruction.",
 void, (const void *data, signed char *ready_try, int n_ready), NULL)

/* This hook notifies the target about the result of the concluded current
   round of multipass scheduling.
   DATA is a pointer.
   If DATA is non-NULL it points to target-specific data used for multipass
   scheduling which corresponds to instruction at the start of the chain of
   the winning solution.  DATA is NULL when multipass scheduling cannot find
   a good enough solution on current cycle and decides to retry later,
   usually after advancing the cycle count.  */
DEFHOOK
(first_cycle_multipass_end,
 "This hook notifies the target about the result of the concluded current\n\
round of multipass scheduling.",
 void, (const void *data), NULL)

/* This hook is called to initialize target-specific data for multipass
   scheduling after it has been allocated.
   DATA is a pointer to target-specific data that stores the effects
   of instruction from which the algorithm backtracks on CPU that are not
   described in DFA.  */
DEFHOOK
(first_cycle_multipass_init,
 "This hook initializes target-specific data used in multipass scheduling.",
 void, (void *data), NULL)

/* This hook is called to finalize target-specific data for multipass
   scheduling before it is deallocated.
   DATA is a pointer to target-specific data that stores the effects
   of instruction from which the algorithm backtracks on CPU that are not
   described in DFA.  */
DEFHOOK
(first_cycle_multipass_fini,
 "This hook finalizes target-specific data used in multipass scheduling.",
 void, (void *data), NULL)

/* The following member value is pointer to a function called by
   the insn scheduler before issuing insn passed as the third
   parameter on given cycle.  If the hook returns nonzero, the
   insn is not issued on given processors cycle.  Instead of that,
   the processor cycle is advanced.  If the value passed through
   the last parameter is zero, the insn ready queue is not sorted
   on the new cycle start as usually.  The first parameter passes
   file for debugging output.  The second one passes the scheduler
   verbose level of the debugging output.  The forth and the fifth
   parameter values are correspondingly processor cycle on which
   the previous insn has been issued and the current processor cycle.  */
DEFHOOK
(dfa_new_cycle,
 "This hook is called by the insn scheduler before issuing @var{insn}\n\
on cycle @var{clock}.  If the hook returns nonzero,\n\
@var{insn} is not issued on this processor cycle.  Instead,\n\
the processor cycle is advanced.  If *@var{sort_p}\n\
is zero, the insn ready queue is not sorted on the new cycle\n\
start as usually.  @var{dump} and @var{verbose} specify the file and\n\
verbosity level to use for debugging output.\n\
@var{last_clock} and @var{clock} are, respectively, the\n\
processor cycle on which the previous insn has been issued,\n\
and the current processor cycle.",
 int, (FILE *dump, int verbose, rtx_insn *insn, int last_clock,
       int clock, int *sort_p),
 NULL)

/* The following member value is a pointer to a function called by the
   insn scheduler.  It should return true if there exists a dependence
   which is considered costly by the target, between the insn
   DEP_PRO (&_DEP), and the insn DEP_CON (&_DEP).  The first parameter is
   the dep that represents the dependence between the two insns.  The
   second argument is the cost of the dependence as estimated by
   the scheduler.  The last argument is the distance in cycles
   between the already scheduled insn (first parameter) and the
   second insn (second parameter).  */
DEFHOOK
(is_costly_dependence,
 "This hook is used to define which dependences are considered costly by\n\
the target, so costly that it is not advisable to schedule the insns that\n\
are involved in the dependence too close to one another.  The parameters\n\
to this hook are as follows:  The first parameter @var{_dep} is the dependence\n\
being evaluated.  The second parameter @var{cost} is the cost of the\n\
dependence as estimated by the scheduler, and the third\n\
parameter @var{distance} is the distance in cycles between the two insns.\n\
The hook returns @code{true} if considering the distance between the two\n\
insns the dependence between them is considered costly by the target,\n\
and @code{false} otherwise.\n\
\n\
Defining this hook can be useful in multiple-issue out-of-order machines,\n\
where (a) it's practically hopeless to predict the actual data/resource\n\
delays, however: (b) there's a better chance to predict the actual grouping\n\
that will be formed, and (c) correctly emulating the grouping can be very\n\
important.  In such targets one may want to allow issuing dependent insns\n\
closer to one another---i.e., closer than the dependence distance;  however,\n\
not in cases of ``costly dependences'', which this hooks allows to define.",
 bool, (struct _dep *_dep, int cost, int distance), NULL)

/* The following member value is a pointer to a function called
   by the insn scheduler. This hook is called to notify the backend
   that new instructions were emitted.  */
DEFHOOK
(h_i_d_extended,
 "This hook is called by the insn scheduler after emitting a new instruction to\n\
the instruction stream.  The hook notifies a target backend to extend its\n\
per instruction data structures.",
 void, (void), NULL)

/* Next 5 functions are for multi-point scheduling.  */

/* Allocate memory for scheduler context.  */
DEFHOOK
(alloc_sched_context,
 "Return a pointer to a store large enough to hold target scheduling context.",
 void *, (void), NULL)

/* Fills the context from the local machine scheduler context.  */
DEFHOOK
(init_sched_context,
 "Initialize store pointed to by @var{tc} to hold target scheduling context.\n\
It @var{clean_p} is true then initialize @var{tc} as if scheduler is at the\n\
beginning of the block.  Otherwise, copy the current context into @var{tc}.",
 void, (void *tc, bool clean_p), NULL)

/* Sets local machine scheduler context to a saved value.  */
DEFHOOK
(set_sched_context,
 "Copy target scheduling context pointed to by @var{tc} to the current context.",
 void, (void *tc), NULL)

/* Clears a scheduler context so it becomes like after init.  */
DEFHOOK
(clear_sched_context,
 "Deallocate internal data in target scheduling context pointed to by @var{tc}.",
 void, (void *tc), NULL)

/* Frees the scheduler context.  */
DEFHOOK
(free_sched_context,
 "Deallocate a store for target scheduling context pointed to by @var{tc}.",
 void, (void *tc), NULL)

/* The following member value is a pointer to a function called
   by the insn scheduler.
   The first parameter is an instruction, the second parameter is the type
   of the requested speculation, and the third parameter is a pointer to the
   speculative pattern of the corresponding type (set if return value == 1).
   It should return
   -1, if there is no pattern, that will satisfy the requested speculation type,
   0, if current pattern satisfies the requested speculation type,
   1, if pattern of the instruction should be changed to the newly
   generated one.  */
DEFHOOK
(speculate_insn,
 "This hook is called by the insn scheduler when @var{insn} has only\n\
speculative dependencies and therefore can be scheduled speculatively.\n\
The hook is used to check if the pattern of @var{insn} has a speculative\n\
version and, in case of successful check, to generate that speculative\n\
pattern.  The hook should return 1, if the instruction has a speculative form,\n\
or @minus{}1, if it doesn't.  @var{request} describes the type of requested\n\
speculation.  If the return value equals 1 then @var{new_pat} is assigned\n\
the generated speculative pattern.",
 int, (rtx_insn *insn, unsigned int dep_status, rtx *new_pat), NULL)

/* The following member value is a pointer to a function called
   by the insn scheduler.  It should return true if the check instruction
   passed as the parameter needs a recovery block.  */
DEFHOOK
(needs_block_p,
 "This hook is called by the insn scheduler during generation of recovery code\n\
for @var{insn}.  It should return @code{true}, if the corresponding check\n\
instruction should branch to recovery code, or @code{false} otherwise.",
 bool, (unsigned int dep_status), NULL)

/* The following member value is a pointer to a function called
   by the insn scheduler.  It should return a pattern for the check
   instruction.
   The first parameter is a speculative instruction, the second parameter
   is the label of the corresponding recovery block (or null, if it is a
   simple check).  The third parameter is the kind of speculation that
   is being performed.  */
DEFHOOK
(gen_spec_check,
 "This hook is called by the insn scheduler to generate a pattern for recovery\n\
check instruction.  If @var{mutate_p} is zero, then @var{insn} is a\n\
speculative instruction for which the check should be generated.\n\
@var{label} is either a label of a basic block, where recovery code should\n\
be emitted, or a null pointer, when requested check doesn't branch to\n\
recovery code (a simple check).  If @var{mutate_p} is nonzero, then\n\
a pattern for a branchy check corresponding to a simple check denoted by\n\
@var{insn} should be generated.  In this case @var{label} can't be null.",
 rtx, (rtx_insn *insn, rtx_insn *label, unsigned int ds), NULL)

/* The following member value is a pointer to a function that provides
   information about the speculation capabilities of the target.
   The parameter is a pointer to spec_info variable.  */
DEFHOOK
(set_sched_flags,
 "This hook is used by the insn scheduler to find out what features should be\n\
enabled/used.\n\
The structure *@var{spec_info} should be filled in by the target.\n\
The structure describes speculation types that can be used in the scheduler.",
 void, (struct spec_info_def *spec_info), NULL)

DEFHOOK_UNDOC
(get_insn_spec_ds,
 "Return speculation types of instruction @var{insn}.",
 unsigned int, (rtx_insn *insn), NULL)

DEFHOOK_UNDOC
(get_insn_checked_ds,
 "Return speculation types that are checked for instruction @var{insn}",
 unsigned int, (rtx_insn *insn), NULL)

DEFHOOK
(can_speculate_insn,
 "Some instructions should never be speculated by the schedulers, usually\n\
 because the instruction is too expensive to get this wrong.  Often such\n\
 instructions have long latency, and often they are not fully modeled in the\n\
 pipeline descriptions.  This hook should return @code{false} if @var{insn}\n\
 should not be speculated.",
 bool, (rtx_insn *insn), hook_bool_rtx_insn_true)

DEFHOOK_UNDOC
(skip_rtx_p,
 "Return bool if rtx scanning should just skip current layer and\
 advance to the inner rtxes.",
 bool, (const_rtx x), NULL)

/* The following member value is a pointer to a function that provides
   information about the target resource-based lower bound which is
   used by the swing modulo scheduler.  The parameter is a pointer
   to ddg variable.  */
DEFHOOK
(sms_res_mii,
 "This hook is called by the swing modulo scheduler to calculate a\n\
resource-based lower bound which is based on the resources available in\n\
the machine and the resources required by each instruction.  The target\n\
backend can use @var{g} to calculate such bound.  A very simple lower\n\
bound will be used in case this hook is not implemented: the total number\n\
of instructions divided by the issue rate.",
 int, (struct ddg *g), NULL)

/* The following member value is a function that initializes dispatch
   schedling and adds instructions to dispatch window according to its
   parameters.  */
DEFHOOK
(dispatch_do,
"This hook is called by Haifa Scheduler.  It performs the operation specified\n\
in its second parameter.",
void, (rtx_insn *insn, int x),
hook_void_rtx_insn_int)

/* The following member value is a function that returns true is
   dispatch schedling is supported in hardware and condition passed
   as the second parameter is true.  */
DEFHOOK
(dispatch,
"This hook is called by Haifa Scheduler.  It returns true if dispatch scheduling\n\
is supported in hardware and the condition specified in the parameter is true.",
bool, (rtx_insn *insn, int x),
hook_bool_rtx_insn_int_false)

DEFHOOKPOD
(exposed_pipeline,
"True if the processor has an exposed pipeline, which means that not just\n\
the order of instructions is important for correctness when scheduling, but\n\
also the latencies of operations.",
bool, false)

/* The following member value is a function that returns number
   of operations reassociator should try to put in parallel for
   statements of the given type.  By default 1 is used.  */
DEFHOOK
(reassociation_width,
"This hook is called by tree reassociator to determine a level of\n\
parallelism required in output calculations chain.",
int, (unsigned int opc, machine_mode mode),
hook_int_uint_mode_1)

/* The following member value is a function that returns priority for
   fusion of each instruction via pointer parameters.  */
DEFHOOK
(fusion_priority,
"This hook is called by scheduling fusion pass.  It calculates fusion\n\
priorities for each instruction passed in by parameter.  The priorities\n\
are returned via pointer parameters.\n\
\n\
@var{insn} is the instruction whose priorities need to be calculated.\n\
@var{max_pri} is the maximum priority can be returned in any cases.\n\
@var{fusion_pri} is the pointer parameter through which @var{insn}'s\n\
fusion priority should be calculated and returned.\n\
@var{pri} is the pointer parameter through which @var{insn}'s priority\n\
should be calculated and returned.\n\
\n\
Same @var{fusion_pri} should be returned for instructions which should\n\
be scheduled together.  Different @var{pri} should be returned for\n\
instructions with same @var{fusion_pri}.  @var{fusion_pri} is the major\n\
sort key, @var{pri} is the minor sort key.  All instructions will be\n\
scheduled according to the two priorities.  All priorities calculated\n\
should be between 0 (exclusive) and @var{max_pri} (inclusive).  To avoid\n\
false dependencies, @var{fusion_pri} of instructions which need to be\n\
scheduled together should be smaller than @var{fusion_pri} of irrelevant\n\
instructions.\n\
\n\
Given below example:\n\
\n\
@smallexample\n\
    ldr r10, [r1, 4]\n\
    add r4, r4, r10\n\
    ldr r15, [r2, 8]\n\
    sub r5, r5, r15\n\
    ldr r11, [r1, 0]\n\
    add r4, r4, r11\n\
    ldr r16, [r2, 12]\n\
    sub r5, r5, r16\n\
@end smallexample\n\
\n\
On targets like ARM/AArch64, the two pairs of consecutive loads should be\n\
merged.  Since peephole2 pass can't help in this case unless consecutive\n\
loads are actually next to each other in instruction flow.  That's where\n\
this scheduling fusion pass works.  This hook calculates priority for each\n\
instruction based on its fustion type, like:\n\
\n\
@smallexample\n\
    ldr r10, [r1, 4]  ; fusion_pri=99,  pri=96\n\
    add r4, r4, r10   ; fusion_pri=100, pri=100\n\
    ldr r15, [r2, 8]  ; fusion_pri=98,  pri=92\n\
    sub r5, r5, r15   ; fusion_pri=100, pri=100\n\
    ldr r11, [r1, 0]  ; fusion_pri=99,  pri=100\n\
    add r4, r4, r11   ; fusion_pri=100, pri=100\n\
    ldr r16, [r2, 12] ; fusion_pri=98,  pri=88\n\
    sub r5, r5, r16   ; fusion_pri=100, pri=100\n\
@end smallexample\n\
\n\
Scheduling fusion pass then sorts all ready to issue instructions according\n\
to the priorities.  As a result, instructions of same fusion type will be\n\
pushed together in instruction flow, like:\n\
\n\
@smallexample\n\
    ldr r11, [r1, 0]\n\
    ldr r10, [r1, 4]\n\
    ldr r15, [r2, 8]\n\
    ldr r16, [r2, 12]\n\
    add r4, r4, r10\n\
    sub r5, r5, r15\n\
    add r4, r4, r11\n\
    sub r5, r5, r16\n\
@end smallexample\n\
\n\
Now peephole2 pass can simply merge the two pairs of loads.\n\
\n\
Since scheduling fusion pass relies on peephole2 to do real fusion\n\
work, it is only enabled by default when peephole2 is in effect.\n\
\n\
This is firstly introduced on ARM/AArch64 targets, please refer to\n\
the hook implementation for how different fusion types are supported.",
void, (rtx_insn *insn, int max_pri, int *fusion_pri, int *pri), NULL)

HOOK_VECTOR_END (sched)

/* Functions relating to OpenMP SIMD and __attribute__((simd)) clones.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_SIMD_CLONE_"
HOOK_VECTOR (TARGET_SIMD_CLONE, simd_clone)

DEFHOOK
(compute_vecsize_and_simdlen,
"This hook should set @var{vecsize_mangle}, @var{vecsize_int}, @var{vecsize_float}\n\
fields in @var{simd_clone} structure pointed by @var{clone_info} argument and also\n\
@var{simdlen} field if it was previously 0.\n\
@var{vecsize_mangle} is a marker for the backend only. @var{vecsize_int} and\n\
@var{vecsize_float} should be left zero on targets where the number of lanes is\n\
not determined by the bitsize (in which case @var{simdlen} is always used).\n\
The hook should return 0 if SIMD clones shouldn't be emitted,\n\
or number of @var{vecsize_mangle} variants that should be emitted.",
int, (struct cgraph_node *, struct cgraph_simd_clone *, tree, int, bool), NULL)

DEFHOOK
(adjust,
"This hook should add implicit @code{attribute(target(\"...\"))} attribute\n\
to SIMD clone @var{node} if needed.",
void, (struct cgraph_node *), NULL)

DEFHOOK
(usable,
"This hook should return -1 if SIMD clone @var{node} shouldn't be used\n\
in vectorized loops in current function with @var{vector_mode}, or\n\
non-negative number if it is usable.  In that case, the smaller the number\n\
is, the more desirable it is to use it.",
int, (struct cgraph_node *, machine_mode), NULL)

HOOK_VECTOR_END (simd_clone)

/* Functions relating to OpenMP SIMT vectorization transform.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_SIMT_"
HOOK_VECTOR (TARGET_SIMT, simt)

DEFHOOK
(vf,
"Return number of threads in SIMT thread group on the target.",
int, (void), NULL)

HOOK_VECTOR_END (simt)

/* Functions relating to OpenMP.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_OMP_"
HOOK_VECTOR (TARGET_OMP, omp)

DEFHOOK
(device_kind_arch_isa,
"Return 1 if @var{trait} @var{name} is present in the OpenMP context's\n\
device trait set, return 0 if not present in any OpenMP context in the\n\
whole translation unit, or -1 if not present in the current OpenMP context\n\
but might be present in another OpenMP context in the same TU.",
int, (enum omp_device_kind_arch_isa trait, const char *name), NULL)

HOOK_VECTOR_END (omp)

/* Functions relating to openacc.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_GOACC_"
HOOK_VECTOR (TARGET_GOACC, goacc)

DEFHOOK
(validate_dims,
"This hook should check the launch dimensions provided for an OpenACC\n\
compute region, or routine.  Defaulted values are represented as -1\n\
and non-constant values as 0.  The @var{fn_level} is negative for the\n\
function corresponding to the compute region.  For a routine it is the\n\
outermost level at which partitioned execution may be spawned.  The hook\n\
should verify non-default values.  If DECL is NULL, global defaults\n\
are being validated and unspecified defaults should be filled in.\n\
Diagnostics should be issued as appropriate.  Return\n\
true, if changes have been made.  You must override this hook to\n\
provide dimensions larger than 1.",
bool, (tree decl, int *dims, int fn_level, unsigned used),
default_goacc_validate_dims)

DEFHOOK
(dim_limit,
"This hook should return the maximum size of a particular dimension,\n\
or zero if unbounded.",
int, (int axis),
default_goacc_dim_limit)

DEFHOOK
(fork_join,
"This hook can be used to convert IFN_GOACC_FORK and IFN_GOACC_JOIN\n\
function calls to target-specific gimple, or indicate whether they\n\
should be retained.  It is executed during the oacc_device_lower pass.\n\
It should return true, if the call should be retained.  It should\n\
return false, if it is to be deleted (either because target-specific\n\
gimple has been inserted before it, or there is no need for it).\n\
The default hook returns false, if there are no RTL expanders for them.",
bool, (gcall *call, const int *dims, bool is_fork),
default_goacc_fork_join)

DEFHOOK
(reduction,
"This hook is used by the oacc_transform pass to expand calls to the\n\
@var{GOACC_REDUCTION} internal function, into a sequence of gimple\n\
instructions.  @var{call} is gimple statement containing the call to\n\
the function.  This hook removes statement @var{call} after the\n\
expanded sequence has been inserted.  This hook is also responsible\n\
for allocating any storage for reductions when necessary.",
void, (gcall *call),
default_goacc_reduction)

DEFHOOK
(adjust_private_decl,
"This hook, if defined, is used by accelerator target back-ends to adjust\n\
OpenACC variable declarations that should be made private to the given\n\
parallelism level (i.e. @code{GOMP_DIM_GANG}, @code{GOMP_DIM_WORKER} or\n\
@code{GOMP_DIM_VECTOR}).  A typical use for this hook is to force variable\n\
declarations at the @code{gang} level to reside in GPU shared memory.\n\
@var{loc} may be used for diagnostic purposes.\n\
\n\
You may also use the @code{TARGET_GOACC_EXPAND_VAR_DECL} hook if the\n\
adjusted variable declaration needs to be expanded to RTL in a non-standard\n\
way.",
tree, (location_t loc, tree var, int level),
NULL)

DEFHOOK
(expand_var_decl,
"This hook, if defined, is used by accelerator target back-ends to expand\n\
specially handled kinds of @code{VAR_DECL} expressions.  A particular use is\n\
to place variables with specific attributes inside special accelarator\n\
memories.  A return value of @code{NULL} indicates that the target does not\n\
handle this @code{VAR_DECL}, and normal RTL expanding is resumed.\n\
\n\
Only define this hook if your accelerator target needs to expand certain\n\
@code{VAR_DECL} nodes in a way that differs from the default.  You can also adjust\n\
private variables at OpenACC device-lowering time using the\n\
@code{TARGET_GOACC_ADJUST_PRIVATE_DECL} target hook.",
rtx, (tree var),
NULL)

DEFHOOK
(create_worker_broadcast_record,
"Create a record used to propagate local-variable state from an active\n\
worker to other workers.  A possible implementation might adjust the type\n\
of REC to place the new variable in shared GPU memory.\n\
\n\
Presence of this target hook indicates that middle end neutering/broadcasting\n\
be used.",
tree, (tree rec, bool sender, const char *name, unsigned HOST_WIDE_INT offset),
NULL)

DEFHOOK
(shared_mem_layout,
"Lay out a fixed shared-memory region on the target.  The LO and HI\n\
arguments should be set to a range of addresses that can be used for worker\n\
broadcasting. The dimensions, reduction size and gang-private size\n\
arguments are for the current offload region.",
void, (unsigned HOST_WIDE_INT *, unsigned HOST_WIDE_INT *, int[],
       unsigned HOST_WIDE_INT[], unsigned HOST_WIDE_INT[]),
NULL)

HOOK_VECTOR_END (goacc)

/* Functions relating to vectorization.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_VECTORIZE_"
HOOK_VECTOR (TARGET_VECTORIZE, vectorize)

/* The following member value is a pointer to a function called
   by the vectorizer, and return the decl of the target builtin
   function.  */
DEFHOOK
(builtin_mask_for_load,
 "This hook should return the DECL of a function @var{f} that given an\n\
address @var{addr} as an argument returns a mask @var{m} that can be\n\
used to extract from two vectors the relevant data that resides in\n\
@var{addr} in case @var{addr} is not properly aligned.\n\
\n\
The autovectorizer, when vectorizing a load operation from an address\n\
@var{addr} that may be unaligned, will generate two vector loads from\n\
the two aligned addresses around @var{addr}. It then generates a\n\
@code{REALIGN_LOAD} operation to extract the relevant data from the\n\
two loaded vectors. The first two arguments to @code{REALIGN_LOAD},\n\
@var{v1} and @var{v2}, are the two vectors, each of size @var{VS}, and\n\
the third argument, @var{OFF}, defines how the data will be extracted\n\
from these two vectors: if @var{OFF} is 0, then the returned vector is\n\
@var{v2}; otherwise, the returned vector is composed from the last\n\
@var{VS}-@var{OFF} elements of @var{v1} concatenated to the first\n\
@var{OFF} elements of @var{v2}.\n\
\n\
If this hook is defined, the autovectorizer will generate a call\n\
to @var{f} (using the DECL tree that this hook returns) and will\n\
use the return value of @var{f} as the argument @var{OFF} to\n\
@code{REALIGN_LOAD}. Therefore, the mask @var{m} returned by @var{f}\n\
should comply with the semantics expected by @code{REALIGN_LOAD}\n\
described above.\n\
If this hook is not defined, then @var{addr} will be used as\n\
the argument @var{OFF} to @code{REALIGN_LOAD}, in which case the low\n\
log2(@var{VS}) @minus{} 1 bits of @var{addr} will be considered.",
 tree, (void), NULL)

/* Returns a built-in function that realizes the vectorized version of
   a target-independent function, or NULL_TREE if not available.  */
DEFHOOK
(builtin_vectorized_function,
 "This hook should return the decl of a function that implements the\n\
vectorized variant of the function with the @code{combined_fn} code\n\
@var{code} or @code{NULL_TREE} if such a function is not available.\n\
The return type of the vectorized function shall be of vector type\n\
@var{vec_type_out} and the argument types should be @var{vec_type_in}.",
 tree, (unsigned code, tree vec_type_out, tree vec_type_in),
 default_builtin_vectorized_function)

/* Returns a built-in function that realizes the vectorized version of
   a target-specific function, or NULL_TREE if not available.  */
DEFHOOK
(builtin_md_vectorized_function,
 "This hook should return the decl of a function that implements the\n\
vectorized variant of target built-in function @code{fndecl}.  The\n\
return type of the vectorized function shall be of vector type\n\
@var{vec_type_out} and the argument types should be @var{vec_type_in}.",
 tree, (tree fndecl, tree vec_type_out, tree vec_type_in),
 default_builtin_md_vectorized_function)

/* Cost of different vector/scalar statements in vectorization cost
   model. In case of misaligned vector loads and stores the cost depends
   on the data type and misalignment value.  */
DEFHOOK
(builtin_vectorization_cost,
 "Returns cost of different scalar or vector statements for vectorization cost model.\n\
For vector memory operations the cost may depend on type (@var{vectype}) and\n\
misalignment value (@var{misalign}).",
 int, (enum vect_cost_for_stmt type_of_cost, tree vectype, int misalign),
 default_builtin_vectorization_cost)

DEFHOOK
(preferred_vector_alignment,
 "This hook returns the preferred alignment in bits for accesses to\n\
vectors of type @var{type} in vectorized code.  This might be less than\n\
or greater than the ABI-defined value returned by\n\
@code{TARGET_VECTOR_ALIGNMENT}.  It can be equal to the alignment of\n\
a single element, in which case the vectorizer will not try to optimize\n\
for alignment.\n\
\n\
The default hook returns @code{TYPE_ALIGN (@var{type})}, which is\n\
correct for most targets.",
 poly_uint64, (const_tree type),
 default_preferred_vector_alignment)

/* Returns whether the target has a preference for decomposing divisions using
   shifts rather than multiplies.  */
DEFHOOK
(preferred_div_as_shifts_over_mult,
 "Sometimes it is possible to implement a vector division using a sequence\n\
of two addition-shift pairs, giving four instructions in total.\n\
Return true if taking this approach for @var{vectype} is likely\n\
to be better than using a sequence involving highpart multiplication.\n\
Default is false if @code{can_mult_highpart_p}, otherwise true.",
 bool, (const_tree type),
 default_preferred_div_as_shifts_over_mult)

/* Return true if vector alignment is reachable (by peeling N
   iterations) for the given scalar type.  */
DEFHOOK
(vector_alignment_reachable,
 "Return true if vector alignment is reachable (by peeling N iterations)\n\
for the given scalar type @var{type}.  @var{is_packed} is false if the scalar\n\
access using @var{type} is known to be naturally aligned.",
 bool, (const_tree type, bool is_packed),
 default_builtin_vector_alignment_reachable)

DEFHOOK
(vec_perm_const,
 "This hook is used to test whether the target can permute up to two\n\
vectors of mode @var{op_mode} using the permutation vector @code{sel},\n\
producing a vector of mode @var{mode}.  The hook is also used to emit such\n\
a permutation.\n\
\n\
When the hook is being used to test whether the target supports a permutation,\n\
@var{in0}, @var{in1}, and @var{out} are all null.  When the hook is being used\n\
to emit a permutation, @var{in0} and @var{in1} are the source vectors of mode\n\
@var{op_mode} and @var{out} is the destination vector of mode @var{mode}.\n\
@var{in1} is the same as @var{in0} if @var{sel} describes a permutation on one\n\
vector instead of two.\n\
\n\
Return true if the operation is possible, emitting instructions for it\n\
if rtxes are provided.\n\
\n\
@cindex @code{vec_perm@var{m}} instruction pattern\n\
If the hook returns false for a mode with multibyte elements, GCC will\n\
try the equivalent byte operation.  If that also fails, it will try forcing\n\
the selector into a register and using the @var{vec_perm@var{mode}}\n\
instruction pattern.  There is no need for the hook to handle these two\n\
implementation approaches itself.",
 bool, (machine_mode mode, machine_mode op_mode, rtx output, rtx in0, rtx in1,
	const vec_perm_indices &sel),
 NULL)

/* Return true if the target supports misaligned store/load of a
   specific factor denoted in the third parameter.  The last parameter
   is true if the access is defined in a packed struct.  */
DEFHOOK
(support_vector_misalignment,
 "This hook should return true if the target supports misaligned vector\n\
store/load of a specific factor denoted in the @var{misalignment}\n\
parameter.  The vector store/load should be of machine mode @var{mode} and\n\
the elements in the vectors should be of type @var{type}.  @var{is_packed}\n\
parameter is true if the memory access is defined in a packed struct.",
 bool,
 (machine_mode mode, const_tree type, int misalignment, bool is_packed),
 default_builtin_support_vector_misalignment)

/* Returns the preferred mode for SIMD operations for the specified
   scalar mode.  */
DEFHOOK
(preferred_simd_mode,
 "This hook should return the preferred mode for vectorizing scalar\n\
mode @var{mode}.  The default is\n\
equal to @code{word_mode}, because the vectorizer can do some\n\
transformations even in absence of specialized @acronym{SIMD} hardware.",
 machine_mode,
 (scalar_mode mode),
 default_preferred_simd_mode)

/* Returns the preferred mode for splitting SIMD reductions to.  */
DEFHOOK
(split_reduction,
 "This hook should return the preferred mode to split the final reduction\n\
step on @var{mode} to.  The reduction is then carried out reducing upper\n\
against lower halves of vectors recursively until the specified mode is\n\
reached.  The default is @var{mode} which means no splitting.",
  machine_mode,
  (machine_mode),
  default_split_reduction)

/* Returns a mask of vector sizes to iterate over when auto-vectorizing
   after processing the preferred one derived from preferred_simd_mode.  */
DEFHOOK
(autovectorize_vector_modes,
 "If using the mode returned by @code{TARGET_VECTORIZE_PREFERRED_SIMD_MODE}\n\
is not the only approach worth considering, this hook should add one mode to\n\
@var{modes} for each useful alternative approach.  These modes are then\n\
passed to @code{TARGET_VECTORIZE_RELATED_MODE} to obtain the vector mode\n\
for a given element mode.\n\
\n\
The modes returned in @var{modes} should use the smallest element mode\n\
possible for the vectorization approach that they represent, preferring\n\
integer modes over floating-poing modes in the event of a tie.  The first\n\
mode should be the @code{TARGET_VECTORIZE_PREFERRED_SIMD_MODE} for its\n\
element mode.\n\
\n\
If @var{all} is true, add suitable vector modes even when they are generally\n\
not expected to be worthwhile.\n\
\n\
The hook returns a bitmask of flags that control how the modes in\n\
@var{modes} are used.  The flags are:\n\
@table @code\n\
@item VECT_COMPARE_COSTS\n\
Tells the loop vectorizer to try all the provided modes and pick the one\n\
with the lowest cost.  By default the vectorizer will choose the first\n\
mode that works.\n\
@end table\n\
\n\
The hook does not need to do anything if the vector returned by\n\
@code{TARGET_VECTORIZE_PREFERRED_SIMD_MODE} is the only one relevant\n\
for autovectorization.  The default implementation adds no modes and\n\
returns 0.",
 unsigned int,
 (vector_modes *modes, bool all),
 default_autovectorize_vector_modes)

DEFHOOK
(related_mode,
 "If a piece of code is using vector mode @var{vector_mode} and also wants\n\
to operate on elements of mode @var{element_mode}, return the vector mode\n\
it should use for those elements.  If @var{nunits} is nonzero, ensure that\n\
the mode has exactly @var{nunits} elements, otherwise pick whichever vector\n\
size pairs the most naturally with @var{vector_mode}.  Return an empty\n\
@code{opt_machine_mode} if there is no supported vector mode with the\n\
required properties.\n\
\n\
There is no prescribed way of handling the case in which @var{nunits}\n\
is zero.  One common choice is to pick a vector mode with the same size\n\
as @var{vector_mode}; this is the natural choice if the target has a\n\
fixed vector size.  Another option is to choose a vector mode with the\n\
same number of elements as @var{vector_mode}; this is the natural choice\n\
if the target has a fixed number of elements.  Alternatively, the hook\n\
might choose a middle ground, such as trying to keep the number of\n\
elements as similar as possible while applying maximum and minimum\n\
vector sizes.\n\
\n\
The default implementation uses @code{mode_for_vector} to find the\n\
requested mode, returning a mode with the same size as @var{vector_mode}\n\
when @var{nunits} is zero.  This is the correct behavior for most targets.",
 opt_machine_mode,
 (machine_mode vector_mode, scalar_mode element_mode, poly_uint64 nunits),
 default_vectorize_related_mode)

/* Function to get a target mode for a vector mask.  */
DEFHOOK
(get_mask_mode,
 "Return the mode to use for a vector mask that holds one boolean\n\
result for each element of vector mode @var{mode}.  The returned mask mode\n\
can be a vector of integers (class @code{MODE_VECTOR_INT}), a vector of\n\
booleans (class @code{MODE_VECTOR_BOOL}) or a scalar integer (class\n\
@code{MODE_INT}).  Return an empty @code{opt_machine_mode} if no such\n\
mask mode exists.\n\
\n\
The default implementation returns a @code{MODE_VECTOR_INT} with the\n\
same size and number of elements as @var{mode}, if such a mode exists.",
 opt_machine_mode,
 (machine_mode mode),
 default_get_mask_mode)

/* Function to say whether a conditional operation is expensive when
   compared to non-masked operations.  */
DEFHOOK
(conditional_operation_is_expensive,
 "This hook returns true if masked operation @var{ifn} (really of\n\
type @code{internal_fn}) should be considered more expensive to use than\n\
implementing the same operation without masking.  GCC can then try to use\n\
unconditional operations instead with extra selects.",
 bool,
 (unsigned ifn),
 default_conditional_operation_is_expensive)

/* Function to say whether a masked operation is expensive when the
   mask is all zeros.  */
DEFHOOK
(empty_mask_is_expensive,
 "This hook returns true if masked internal function @var{ifn} (really of\n\
type @code{internal_fn}) should be considered expensive when the mask is\n\
all zeros.  GCC can then try to branch around the instruction instead.",
 bool,
 (unsigned ifn),
 default_empty_mask_is_expensive)

/* Target builtin that implements vector gather operation.  */
DEFHOOK
(builtin_gather,
 "Target builtin that implements vector gather operation.  @var{mem_vectype}\n\
is the vector type of the load and @var{index_type} is scalar type of\n\
the index, scaled by @var{scale}.\n\
The default is @code{NULL_TREE} which means to not vectorize gather\n\
loads.",
 tree,
 (const_tree mem_vectype, const_tree index_type, int scale),
 NULL)

/* Target builtin that implements vector scatter operation.  */
DEFHOOK
(builtin_scatter,
"Target builtin that implements vector scatter operation.  @var{vectype}\n\
is the vector type of the store and @var{index_type} is scalar type of\n\
the index, scaled by @var{scale}.\n\
The default is @code{NULL_TREE} which means to not vectorize scatter\n\
stores.",
 tree,
 (const_tree vectype, const_tree index_type, int scale),
 NULL)

/* Target function to initialize the cost model for a loop or block.  */
DEFHOOK
(create_costs,
 "This hook should initialize target-specific data structures in preparation\n\
for modeling the costs of vectorizing a loop or basic block.  The default\n\
allocates three unsigned integers for accumulating costs for the prologue,\n\
body, and epilogue of the loop or basic block.  If @var{loop_info} is\n\
non-NULL, it identifies the loop being vectorized; otherwise a single block\n\
is being vectorized.  If @var{costing_for_scalar} is true, it indicates the\n\
current cost model is for the scalar version of a loop or block; otherwise\n\
it is for the vector version.",
 class vector_costs *,
 (vec_info *vinfo, bool costing_for_scalar),
 default_vectorize_create_costs)

HOOK_VECTOR_END (vectorize)

#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_"

DEFHOOK
(preferred_else_value,
 "This hook returns the target's preferred final argument for a call\n\
to conditional internal function @var{ifn} (really of type\n\
@code{internal_fn}).  @var{type} specifies the return type of the\n\
function and @var{ops} are the operands to the conditional operation,\n\
of which there are @var{nops}.\n\
\n\
For example, if @var{ifn} is @code{IFN_COND_ADD}, the hook returns\n\
a value of type @var{type} that should be used when @samp{@var{ops}[0]}\n\
and @samp{@var{ops}[1]} are conditionally added together.\n\
\n\
This hook is only relevant if the target supports conditional patterns\n\
like @code{cond_add@var{m}}.  The default implementation returns a zero\n\
constant of type @var{type}.",
 tree,
 (unsigned ifn, tree type, unsigned nops, tree *ops),
 default_preferred_else_value)

DEFHOOK
(record_offload_symbol,
 "Used when offloaded functions are seen in the compilation unit and no named\n\
sections are available.  It is called once for each symbol that must be\n\
recorded in the offload function and variable table.",
 void, (tree),
 hook_void_tree)

DEFHOOKPOD
(absolute_biggest_alignment,
 "If defined, this target hook specifies the absolute biggest alignment\n\
that a type or variable can have on this machine, otherwise,\n\
@code{BIGGEST_ALIGNMENT} is used.",
 HOST_WIDE_INT, BIGGEST_ALIGNMENT)

/* Allow target specific overriding of option settings after options have
  been changed by an attribute or pragma or when it is reset at the
  end of the code affected by an attribute or pragma.  */
DEFHOOK
(override_options_after_change,
 "This target function is similar to the hook @code{TARGET_OPTION_OVERRIDE}\n\
but is called when the optimize level is changed via an attribute or\n\
pragma or when it is reset at the end of the code affected by the\n\
attribute or pragma.  It is not called at the beginning of compilation\n\
when @code{TARGET_OPTION_OVERRIDE} is called so if you want to perform these\n\
actions then, you should have @code{TARGET_OPTION_OVERRIDE} call\n\
@code{TARGET_OVERRIDE_OPTIONS_AFTER_CHANGE}.",
 void, (void),
 hook_void_void)

DEFHOOK
(offload_options,
 "Used when writing out the list of options into an LTO file.  It should\n\
translate any relevant target-specific options (such as the ABI in use)\n\
into one of the @option{-foffload} options that exist as a common interface\n\
to express such options.  It should return a string containing these options,\n\
separated by spaces, which the caller will free.\n",
char *, (void), hook_charptr_void_null)

DEFHOOK_UNDOC
(eh_return_filter_mode,
 "Return machine mode for filter value.",
 scalar_int_mode, (void),
 default_eh_return_filter_mode)

/* Return machine mode for libgcc expanded cmp instructions.  */
DEFHOOK
(libgcc_cmp_return_mode,
 "This target hook should return the mode to be used for the return value\n\
of compare instructions expanded to libgcc calls.  If not defined\n\
@code{word_mode} is returned which is the right choice for a majority of\n\
targets.",
 scalar_int_mode, (void),
 default_libgcc_cmp_return_mode)

/* Return machine mode for libgcc expanded shift instructions.  */
DEFHOOK
(libgcc_shift_count_mode,
 "This target hook should return the mode to be used for the shift count operand\n\
of shift instructions expanded to libgcc calls.  If not defined\n\
@code{word_mode} is returned which is the right choice for a majority of\n\
targets.",
 scalar_int_mode, (void),
 default_libgcc_shift_count_mode)

/* Return machine mode to be used for _Unwind_Word type.  */
DEFHOOK
(unwind_word_mode,
 "Return machine mode to be used for @code{_Unwind_Word} type.\n\
The default is to use @code{word_mode}.",
 scalar_int_mode, (void),
 default_unwind_word_mode)

/* Given two decls, merge their attributes and return the result.  */
DEFHOOK
(merge_decl_attributes,
 "Define this target hook if the merging of decl attributes needs special\n\
handling.  If defined, the result is a list of the combined\n\
@code{DECL_ATTRIBUTES} of @var{olddecl} and @var{newdecl}.\n\
@var{newdecl} is a duplicate declaration of @var{olddecl}.  Examples of\n\
when this is needed are when one attribute overrides another, or when an\n\
attribute is nullified by a subsequent definition.  This function may\n\
call @code{merge_attributes} to handle machine-independent merging.\n\
\n\
@findex TARGET_DLLIMPORT_DECL_ATTRIBUTES\n\
If the only target-specific handling you require is @samp{dllimport}\n\
for Microsoft Windows targets, you should define the macro\n\
@code{TARGET_DLLIMPORT_DECL_ATTRIBUTES} to @code{1}.  The compiler\n\
will then define a function called\n\
@code{merge_dllimport_decl_attributes} which can then be defined as\n\
the expansion of @code{TARGET_MERGE_DECL_ATTRIBUTES}.  You can also\n\
add @code{handle_dll_attribute} in the attribute table for your port\n\
to perform initial processing of the @samp{dllimport} and\n\
@samp{dllexport} attributes.  This is done in @file{i386/cygwin.h} and\n\
@file{i386/i386.cc}, for example.",
 tree, (tree olddecl, tree newdecl),
 merge_decl_attributes)

/* Given two types, merge their attributes and return the result.  */
DEFHOOK
(merge_type_attributes,
 "Define this target hook if the merging of type attributes needs special\n\
handling.  If defined, the result is a list of the combined\n\
@code{TYPE_ATTRIBUTES} of @var{type1} and @var{type2}.  It is assumed\n\
that @code{comptypes} has already been called and returned 1.  This\n\
function may call @code{merge_attributes} to handle machine-independent\n\
merging.",
 tree, (tree type1, tree type2),
 merge_type_attributes)

/* Table of machine attributes and functions to handle them.
   Ignored if empty.  */
DEFHOOKPOD
(attribute_table,
 "If defined, this target hook provides an array of\n\
@samp{scoped_attribute_spec}s (defined in @file{attribs.h}) that specify the\n\
machine-specific attributes for this target.  The information includes some\n\
of the restrictions on the entities to which these attributes are applied\n\
and the arguments that the attributes take.\n\
\n\
In C and C++, these attributes are associated with two syntaxes:\n\
the traditional GNU @code{__attribute__} syntax and the standard\n\
@samp{[[]]} syntax.  Attributes that support the GNU syntax must be\n\
placed in the @code{gnu} namespace.  Such attributes can then also be\n\
written @samp{[[gnu::@dots{}]]}.  Attributes that use only the standard\n\
syntax should be placed in whichever namespace the attribute specification\n\
requires.  For example, a target might choose to support vendor-specific\n\
@samp{[[]]} attributes that the vendor places in their own namespace.\n\
\n\
Targets that only define attributes in the @code{gnu} namespace\n\
can uase the following shorthand to define the table:\n\
\n\
@smallexample\n\
TARGET_GNU_ATTRIBUTES (@var{cpu_attribute_table}, @{\n\
  @{ \"@var{attribute1}\", @dots{} @},\n\
  @{ \"@var{attribute2}\", @dots{} @},\n\
  @dots{},\n\
  @{ \"@var{attributen}\", @dots{} @},\n\
@});\n\
@end smallexample",
 array_slice<const struct scoped_attribute_specs *const>,)

/* Return true iff attribute NAME expects a plain identifier as its first
   argument.  */
DEFHOOK
(attribute_takes_identifier_p,
 "If defined, this target hook is a function which returns true if the\n\
machine-specific attribute named @var{name} expects an identifier\n\
given as its first argument to be passed on as a plain identifier, not\n\
subjected to name lookup.  If this is not defined, the default is\n\
false for all machine-specific attributes.",
 bool, (const_tree name),
 hook_bool_const_tree_false)

/* Return zero if the attributes on TYPE1 and TYPE2 are incompatible,
   one if they are compatible and two if they are nearly compatible
   (which causes a warning to be generated).  */
DEFHOOK
(comp_type_attributes,
 "If defined, this target hook is a function which returns zero if the attributes on\n\
@var{type1} and @var{type2} are incompatible, one if they are compatible,\n\
and two if they are nearly compatible (which causes a warning to be\n\
generated).  If this is not defined, machine-specific attributes are\n\
supposed always to be compatible.",
 int, (const_tree type1, const_tree type2),
 hook_int_const_tree_const_tree_1)

/* Assign default attributes to the newly defined TYPE.  */
DEFHOOK
(set_default_type_attributes,
 "If defined, this target hook is a function which assigns default attributes to\n\
the newly defined @var{type}.",
 void, (tree type),
 hook_void_tree)

/* Insert attributes on the newly created DECL.  */
DEFHOOK
(insert_attributes,
 "Define this target hook if you want to be able to add attributes to a decl\n\
when it is being created.  This is normally useful for back ends which\n\
wish to implement a pragma by using the attributes which correspond to\n\
the pragma's effect.  The @var{node} argument is the decl which is being\n\
created.  The @var{attr_ptr} argument is a pointer to the attribute list\n\
for this decl.  The list itself should not be modified, since it may be\n\
shared with other decls, but attributes may be chained on the head of\n\
the list and @code{*@var{attr_ptr}} modified to point to the new\n\
attributes, or a copy of the list may be made if further changes are\n\
needed.",
 void, (tree node, tree *attr_ptr),
 hook_void_tree_treeptr)

/* Perform additional target-specific processing of generic attributes.  */
DEFHOOK
(handle_generic_attribute,
 "Define this target hook if you want to be able to perform additional\n\
target-specific processing of an attribute which is handled generically\n\
by a front end.  The arguments are the same as those which are passed to\n\
attribute handlers.  So far this only affects the @var{noinit} and\n\
@var{section} attribute.",
 tree, (tree *node, tree name, tree args, int flags, bool *no_add_attrs),
 hook_tree_treeptr_tree_tree_int_boolptr_null)

/* Return true if FNDECL (which has at least one machine attribute)
   can be inlined despite its machine attributes, false otherwise.  */
DEFHOOK
(function_attribute_inlinable_p,
 "@cindex inlining\n\
This target hook returns @code{false} if the target-specific attributes on\n\
@var{fndecl} always block it getting inlined, @code{true} otherwise.  By\n\
default, if a function has a target specific attribute attached to it, it\n\
will not be inlined.",
 bool, (const_tree fndecl),
 hook_bool_const_tree_false)

/* Return true if bitfields in RECORD_TYPE should follow the
   Microsoft Visual C++ bitfield layout rules.  */
DEFHOOK
(ms_bitfield_layout_p,
 "This target hook returns @code{true} if bit-fields in the given\n\
@var{record_type} are to be laid out following the rules of Microsoft\n\
Visual C/C++, namely: (i) a bit-field won't share the same storage\n\
unit with the previous bit-field if their underlying types have\n\
different sizes, and the bit-field will be aligned to the highest\n\
alignment of the underlying types of itself and of the previous\n\
bit-field; (ii) a zero-sized bit-field will affect the alignment of\n\
the whole enclosing structure, even if it is unnamed; except that\n\
(iii) a zero-sized bit-field will be disregarded unless it follows\n\
another bit-field of nonzero size.  If this hook returns @code{true},\n\
other macros that control bit-field layout are ignored.\n\
\n\
When a bit-field is inserted into a packed record, the whole size\n\
of the underlying type is used by one or more same-size adjacent\n\
bit-fields (that is, if its long:3, 32 bits is used in the record,\n\
and any additional adjacent long bit-fields are packed into the same\n\
chunk of 32 bits.  However, if the size changes, a new field of that\n\
size is allocated).  In an unpacked record, this is the same as using\n\
alignment, but not equivalent when packing.\n\
\n\
If both MS bit-fields and @samp{__attribute__((packed))} are used,\n\
the latter will take precedence.  If @samp{__attribute__((packed))} is\n\
used on a single field when MS bit-fields are in use, it will take\n\
precedence for that field, but the alignment of the rest of the structure\n\
may affect its placement.",
 bool, (const_tree record_type),
 hook_bool_const_tree_false)

/* For now this is only an interface to WORDS_BIG_ENDIAN for
   target-independent code like the front ends, need performance testing
   before switching completely to the target hook.  */
DEFHOOK_UNDOC
(words_big_endian,
 "",
 bool, (void),
 targhook_words_big_endian)

/* Likewise for FLOAT_WORDS_BIG_ENDIAN.  */
DEFHOOK_UNDOC
(float_words_big_endian,
 "",
 bool, (void),
 targhook_float_words_big_endian)

DEFHOOK
(float_exceptions_rounding_supported_p,
 "Returns true if the target supports IEEE 754 floating-point exceptions\n\
and rounding modes, false otherwise.  This is intended to relate to the\n\
@code{float} and @code{double} types, but not necessarily @code{long double}.\n\
By default, returns true if the @code{adddf3} instruction pattern is\n\
available and false otherwise, on the assumption that hardware floating\n\
point supports exceptions and rounding modes but software floating point\n\
does not.",
 bool, (void),
 default_float_exceptions_rounding_supported_p)

/* True if the target supports decimal floating point.  */
DEFHOOK
(decimal_float_supported_p,
 "Returns true if the target supports decimal floating point.",
 bool, (void),
 default_decimal_float_supported_p)

/* True if the target supports fixed-point.  */
DEFHOOK
(fixed_point_supported_p,
 "Returns true if the target supports fixed-point arithmetic.",
 bool, (void),
 default_fixed_point_supported_p)

/* Return true if anonymous bitfields affect structure alignment.  */
DEFHOOK
(align_anon_bitfield,
 "When @code{PCC_BITFIELD_TYPE_MATTERS} is true this hook will determine\n\
whether unnamed bitfields affect the alignment of the containing\n\
structure.  The hook should return true if the structure should inherit\n\
the alignment requirements of an unnamed bitfield's type.",
 bool, (void),
 hook_bool_void_false)

/* Return true if volatile bitfields should use the narrowest type possible.
   Return false if they should use the container type.  */
DEFHOOK
(narrow_volatile_bitfield,
 "This target hook should return @code{true} if accesses to volatile bitfields\n\
should use the narrowest mode possible.  It should return @code{false} if\n\
these accesses should use the bitfield container type.\n\
\n\
The default is @code{false}.",
 bool, (void),
 hook_bool_void_false)

/* Set up target-specific built-in functions.  */
DEFHOOK
(init_builtins,
 "Define this hook if you have any machine-specific built-in functions\n\
that need to be defined.  It should be a function that performs the\n\
necessary setup.\n\
\n\
Machine specific built-in functions can be useful to expand special machine\n\
instructions that would otherwise not normally be generated because\n\
they have no equivalent in the source language (for example, SIMD vector\n\
instructions or prefetch instructions).\n\
\n\
To create a built-in function, call the function\n\
@code{lang_hooks.builtin_function}\n\
which is defined by the language front end.  You can use any type nodes set\n\
up by @code{build_common_tree_nodes};\n\
only language front ends that use those two functions will call\n\
@samp{TARGET_INIT_BUILTINS}.",
 void, (void),
 hook_void_void)

/* Initialize (if INITIALIZE_P is true) and return the target-specific
   built-in function decl for CODE.
   Return NULL if that is not possible.  Return error_mark_node if CODE
   is outside of the range of valid target builtin function codes.  */
DEFHOOK
(builtin_decl,
 "Define this hook if you have any machine-specific built-in functions\n\
that need to be defined.  It should be a function that returns the\n\
builtin function declaration for the builtin function code @var{code}.\n\
If there is no such builtin and it cannot be initialized at this time\n\
if @var{initialize_p} is true the function should return @code{NULL_TREE}.\n\
If @var{code} is out of range the function should return\n\
@code{error_mark_node}.",
 tree, (unsigned code, bool initialize_p), NULL)

/* Expand a target-specific builtin.  */
DEFHOOK
(expand_builtin,
 "\n\
Expand a call to a machine specific built-in function that was set up by\n\
@samp{TARGET_INIT_BUILTINS}.  @var{exp} is the expression for the\n\
function call; the result should go to @var{target} if that is\n\
convenient, and have mode @var{mode} if that is convenient.\n\
@var{subtarget} may be used as the target for computing one of\n\
@var{exp}'s operands.  @var{ignore} is nonzero if the value is to be\n\
ignored.  This function should return the result of the call to the\n\
built-in function.",
 rtx,
 (tree exp, rtx target, rtx subtarget, machine_mode mode, int ignore),
 default_expand_builtin)

/* Select a replacement for a target-specific builtin.  This is done
   *before* regular type checking, and so allows the target to
   implement a crude form of function overloading.  The result is a
   complete expression that implements the operation.  PARAMS really
   has type VEC(tree,gc)*, but we don't want to include tree.h here.  */
DEFHOOK
(resolve_overloaded_builtin,
 "Select a replacement for a machine specific built-in function that\n\
was set up by @samp{TARGET_INIT_BUILTINS}.  This is done\n\
@emph{before} regular type checking, and so allows the target to\n\
implement a crude form of function overloading.  @var{fndecl} is the\n\
declaration of the built-in function.  @var{arglist} is the list of\n\
arguments passed to the built-in function.  The result is a\n\
complete expression that implements the operation, usually\n\
another @code{CALL_EXPR}.\n\
@var{arglist} really has type @samp{VEC(tree,gc)*}\n\
@var{complain} is a boolean indicating whether invalid operations\n\
should emit errors.  This is set to @code{false} when the C++ templating\n\
context expects that errors should not be emitted (i.e. SFINAE).",
 tree, (location_t loc, tree fndecl, void *arglist, bool complain), NULL)

DEFHOOK
(check_builtin_call,
 "Perform semantic checking on a call to a machine-specific built-in\n\
function after its arguments have been constrained to the function\n\
signature.  Return true if the call is valid, otherwise report an error\n\
and return false.\n\
\n\
This hook is called after @code{TARGET_RESOLVE_OVERLOADED_BUILTIN}.\n\
The call was originally to built-in function @var{orig_fndecl},\n\
but after the optional @code{TARGET_RESOLVE_OVERLOADED_BUILTIN}\n\
step is now to built-in function @var{fndecl}.  @var{loc} is the\n\
location of the call and @var{args} is an array of function arguments,\n\
of which there are @var{nargs}.  @var{arg_loc} specifies the location\n\
of each argument.  @var{complain} is a boolean indicating whether invalid\n\
arguments should emitm errors.  This is set to @code{false} when the C++\n\
templating context expects that errors should not be emitted (i.e. SFINAE).",
 bool, (location_t loc, vec<location_t> arg_loc, tree fndecl,
	tree orig_fndecl, unsigned int nargs, tree *args, bool complain),
 NULL)

/* Fold a target-specific builtin to a tree valid for both GIMPLE
   and GENERIC.  */
DEFHOOK
(fold_builtin,
 "Fold a call to a machine specific built-in function that was set up by\n\
@samp{TARGET_INIT_BUILTINS}.  @var{fndecl} is the declaration of the\n\
built-in function.  @var{n_args} is the number of arguments passed to\n\
the function; the arguments themselves are pointed to by @var{argp}.\n\
The result is another tree, valid for both GIMPLE and GENERIC,\n\
containing a simplified expression for the call's result.  If\n\
@var{ignore} is true the value will be ignored.",
 tree, (tree fndecl, int n_args, tree *argp, bool ignore),
 hook_tree_tree_int_treep_bool_null)

/* Fold a target-specific builtin to a valid GIMPLE tree.  */
DEFHOOK
(gimple_fold_builtin,
 "Fold a call to a machine specific built-in function that was set up\n\
by @samp{TARGET_INIT_BUILTINS}.  @var{gsi} points to the gimple\n\
statement holding the function call.  Returns true if any change\n\
was made to the GIMPLE stream.",
 bool, (gimple_stmt_iterator *gsi),
 hook_bool_gsiptr_false)

/* Target hook is used to compare the target attributes in two functions to
   determine which function's features get higher priority.  This is used
   during function multi-versioning to figure out the order in which two
   versions must be dispatched.  A function version with a higher priority
   is checked for dispatching earlier.  DECL1 and DECL2 are
   the two function decls that will be compared. It returns positive value
   if DECL1 is higher priority,  negative value if DECL2 is higher priority
   and 0 if they are the same. */
DEFHOOK
(compare_version_priority,
 "This hook is used to compare the target attributes in two functions to\n\
determine which function's features get higher priority.  This is used\n\
during function multi-versioning to figure out the order in which two\n\
versions must be dispatched.  A function version with a higher priority\n\
is checked for dispatching earlier.  @var{decl1} and @var{decl2} are\n\
 the two function decls that will be compared.",
 int, (tree decl1, tree decl2), NULL)

/*  Target hook is used to generate the dispatcher logic to invoke the right
    function version at run-time for a given set of function versions.
    ARG points to the callgraph node of the dispatcher function whose body
    must be generated.  */
DEFHOOK
(generate_version_dispatcher_body,
 "This hook is used to generate the dispatcher logic to invoke the right\n\
function version at run-time for a given set of function versions.\n\
@var{arg} points to the callgraph node of the dispatcher function whose\n\
body must be generated.",
 tree, (void *arg), NULL)

/* Target hook is used to get the dispatcher function for a set of function
   versions.  The dispatcher function is called to invoke the right function
   version at run-time.  DECL is one version from a set of semantically
   identical versions.  */
DEFHOOK
(get_function_versions_dispatcher,
 "This hook is used to get the dispatcher function for a set of function\n\
versions.  The dispatcher function is called to invoke the right function\n\
version at run-time. @var{decl} is one version from a set of semantically\n\
identical versions.",
 tree, (void *decl), NULL)

/* Returns a code for a target-specific builtin that implements
   reciprocal of a target-specific function, or NULL_TREE if not available.  */
DEFHOOK
(builtin_reciprocal,
 "This hook should return the DECL of a function that implements the\n\
reciprocal of the machine-specific builtin function @var{fndecl}, or\n\
@code{NULL_TREE} if such a function is not available.",
 tree, (tree fndecl),
 default_builtin_reciprocal)

/* For a vendor-specific TYPE, return a pointer to a statically-allocated
   string containing the C++ mangling for TYPE.  In all other cases, return
   NULL.  */
DEFHOOK
(mangle_type,
 "If your target defines any fundamental types, or any types your target\n\
uses should be mangled differently from the default, define this hook\n\
to return the appropriate encoding for these types as part of a C++\n\
mangled name.  The @var{type} argument is the tree structure representing\n\
the type to be mangled.  The hook may be applied to trees which are\n\
not target-specific fundamental types; it should return @code{NULL}\n\
for all such types, as well as arguments it does not recognize.  If the\n\
return value is not @code{NULL}, it must point to a statically-allocated\n\
string constant.\n\
\n\
Target-specific fundamental types might be new fundamental types or\n\
qualified versions of ordinary fundamental types.  Encode new\n\
fundamental types as @samp{@w{u @var{n} @var{name}}}, where @var{name}\n\
is the name used for the type in source code, and @var{n} is the\n\
length of @var{name} in decimal.  Encode qualified versions of\n\
ordinary types as @samp{@w{U @var{n} @var{name} @var{code}}}, where\n\
@var{name} is the name used for the type qualifier in source code,\n\
@var{n} is the length of @var{name} as above, and @var{code} is the\n\
code used to represent the unqualified version of this type.  (See\n\
@code{write_builtin_type} in @file{cp/mangle.cc} for the list of\n\
codes.)  In both cases the spaces are for clarity; do not include any\n\
spaces in your string.\n\
\n\
This hook is applied to types prior to typedef resolution.  If the mangled\n\
name for a particular type depends only on that type's main variant, you\n\
can perform typedef resolution yourself using @code{TYPE_MAIN_VARIANT}\n\
before mangling.\n\
\n\
The default version of this hook always returns @code{NULL}, which is\n\
appropriate for a target that does not define any new fundamental\n\
types.",
 const char *, (const_tree type),
 hook_constcharptr_const_tree_null)

/* Temporarily add conditional target specific types for the purpose of
   emitting C++ fundamental type tinfos.  */
DEFHOOK
(emit_support_tinfos,
 "If your target defines any fundamental types which depend on ISA flags,\n\
they might need C++ tinfo symbols in libsupc++/libstdc++ regardless of\n\
ISA flags the library is compiled with.\n\
This hook allows creating tinfo symbols even for those cases, by temporarily\n\
creating each corresponding fundamental type trees, calling the\n\
@var{callback} function on it and setting the type back to @code{nullptr}.",
 void, (emit_support_tinfos_callback callback),
 default_emit_support_tinfos)

/* Make any adjustments to libfunc names needed for this target.  */
DEFHOOK
(init_libfuncs,
 "This hook should declare additional library routines or rename\n\
existing ones, using the functions @code{set_optab_libfunc} and\n\
@code{init_one_libfunc} defined in @file{optabs.cc}.\n\
@code{init_optabs} calls this macro after initializing all the normal\n\
library routines.\n\
\n\
The default is to do nothing.  Most ports don't need to define this hook.",
 void, (void),
 hook_void_void)

 /* Add a __gnu_ prefix to library functions rather than just __.  */
DEFHOOKPOD
(libfunc_gnu_prefix,
 "If false (the default), internal library routines start with two\n\
underscores.  If set to true, these routines start with @code{__gnu_}\n\
instead.  E.g., @code{__muldi3} changes to @code{__gnu_muldi3}.  This\n\
currently only affects functions defined in @file{libgcc2.c}.  If this\n\
is set to true, the @file{tm.h} file must also\n\
@code{#define LIBGCC2_GNU_PREFIX}.",
  bool, false)

/* Given a decl, a section name, and whether the decl initializer
   has relocs, choose attributes for the section.  */
/* ??? Should be merged with SELECT_SECTION and UNIQUE_SECTION.  */
DEFHOOK
(section_type_flags,
 "Choose a set of section attributes for use by @code{TARGET_ASM_NAMED_SECTION}\n\
based on a variable or function decl, a section name, and whether or not the\n\
declaration's initializer may contain runtime relocations.  @var{decl} may be\n\
null, in which case read-write data should be assumed.\n\
\n\
The default version of this function handles choosing code vs data,\n\
read-only vs read-write data, and @code{flag_pic}.  You should only\n\
need to override this if your target has special flags that might be\n\
set via @code{__attribute__}.",
 unsigned int, (tree decl, const char *name, int reloc),
 default_section_type_flags)

DEFHOOK
(libc_has_function,
 "This hook determines whether a function from a class of functions\n\
@var{fn_class} is present in the target C library.  If @var{type} is NULL,\n\
the caller asks for support for all standard (float, double, long double)\n\
types.  If @var{type} is non-NULL, the caller asks for support for a\n\
specific type.",
 bool, (enum function_class fn_class, tree type),
 default_libc_has_function)

DEFHOOK
(libc_has_fast_function,
 "This hook determines whether a function from a class of functions\n\
@code{(enum function_class)}@var{fcode} has a fast implementation.",
 bool, (int fcode),
 default_libc_has_fast_function)

DEFHOOK
(fortify_source_default_level,
 "This hook determines what value _FORTIFY_SOURCE will be set to when using\n\
the command-line option -fhardened.",
 unsigned, (void),
 default_fortify_source_default_level)

DEFHOOK
(libm_function_max_error,
 "This hook determines expected maximum errors for math functions measured\n\
in ulps (units of the last place).  0 means 0.5ulps precision (correctly\n\
rounded).  ~0U means unknown errors.  The @code{combined_fn} @var{cfn}\n\
argument should identify just which math built-in function it is rather than\n\
its variant, @var{mode} the variant in terms of floating-point machine mode.\n\
The hook should also take into account @code{flag_rounding_math} whether it\n\
is maximum error just in default rounding mode, or in all possible rounding\n\
modes.  @var{boundary_p} is @code{true} for maximum errors on intrinsic math\n\
boundaries of functions rather than errors inside of the usual result ranges\n\
of the functions.  E.g.@ the sin/cos function finite result is in between\n\
-1.0 and 1.0 inclusive, with @var{boundary_p} true the function returns how\n\
many ulps below or above those boundaries result could be.",
 unsigned, (unsigned cfn, machine_mode mode, bool boundary_p),
 default_libm_function_max_error)

/* True if new jumps cannot be created, to replace existing ones or
   not, at the current point in the compilation.  */
DEFHOOK
(cannot_modify_jumps_p,
 "This target hook returns @code{true} past the point in which new jump\n\
instructions could be created.  On machines that require a register for\n\
every jump such as the SHmedia ISA of SH5, this point would typically be\n\
reload, so this target hook should be defined to a function such as:\n\
\n\
@smallexample\n\
static bool\n\
cannot_modify_jumps_past_reload_p ()\n\
@{\n\
  return (reload_completed || reload_in_progress);\n\
@}\n\
@end smallexample",
 bool, (void),
 hook_bool_void_false)

/* True if FOLLOWER may be modified to follow FOLLOWEE.  */
DEFHOOK
(can_follow_jump,
 "FOLLOWER and FOLLOWEE are JUMP_INSN instructions;\n\
return true if FOLLOWER may be modified to follow FOLLOWEE;\n\
false, if it can't.\n\
For example, on some targets, certain kinds of branches can't be made to\n\
follow through a hot/cold partitioning.",
 bool, (const rtx_insn *follower, const rtx_insn *followee),
 hook_bool_const_rtx_insn_const_rtx_insn_true)

/* Return true if the target supports conditional execution.  */
DEFHOOK
(have_conditional_execution,
 "This target hook returns true if the target supports conditional execution.\n\
This target hook is required only when the target has several different\n\
modes and they have different conditional execution capability, such as ARM.",
 bool, (void),
 default_have_conditional_execution)

DEFHOOK
(gen_ccmp_first,
 "This function prepares to emit a comparison insn for the first compare in a\n\
 sequence of conditional comparisions.  It returns an appropriate comparison\n\
 with @code{CC} for passing to @code{gen_ccmp_next} or @code{cbranch_optab}.\n\
 The insns to prepare the compare are saved in @var{prep_seq} and the compare\n\
 insns are saved in @var{gen_seq}.  They will be emitted when all the\n\
 compares in the conditional comparision are generated without error.\n\
 @var{code} is the @code{rtx_code} of the compare for @var{op0} and @var{op1}.",
 rtx, (rtx_insn **prep_seq, rtx_insn **gen_seq, rtx_code code, tree op0, tree op1),
 NULL)

DEFHOOK
(gen_ccmp_next,
 "This function prepares to emit a conditional comparison within a sequence\n\
 of conditional comparisons.  It returns an appropriate comparison with\n\
 @code{CC} for passing to @code{gen_ccmp_next} or @code{cbranch_optab}.\n\
 The insns to prepare the compare are saved in @var{prep_seq} and the compare\n\
 insns are saved in @var{gen_seq}.  They will be emitted when all the\n\
 compares in the conditional comparision are generated without error.  The\n\
 @var{prev} expression is the result of a prior call to @code{gen_ccmp_first}\n\
 or @code{gen_ccmp_next}.  It may return @code{NULL} if the combination of\n\
 @var{prev} and this comparison is not supported, otherwise the result must\n\
 be appropriate for passing to @code{gen_ccmp_next} or @code{cbranch_optab}.\n\
 @var{code} is the @code{rtx_code} of the compare for @var{op0} and @var{op1}.\n\
 @var{bit_code} is @code{AND} or @code{IOR}, which is the op on the compares.",
 rtx, (rtx_insn **prep_seq, rtx_insn **gen_seq, rtx prev, rtx_code cmp_code, tree op0, tree op1, rtx_code bit_code),
 NULL)

/* Return true if the target supports conditional compare.  */
DEFHOOK
(have_ccmp,
 "This target hook returns true if the target supports conditional compare.\n\
This target hook is required only when the ccmp support is conditionally\n\
enabled, such as in response to command-line flags. The default implementation\n\
returns true iff @code{TARGET_GEN_CCMP_FIRST} is defined.",
 bool, (void),
 default_have_ccmp)

/* Return a new value for loop unroll size.  */
DEFHOOK
(loop_unroll_adjust,
 "This target hook returns a new value for the number of times @var{loop}\n\
should be unrolled. The parameter @var{nunroll} is the number of times\n\
the loop is to be unrolled. The parameter @var{loop} is a pointer to\n\
the loop, which is going to be checked for unrolling. This target hook\n\
is required only when the target has special constraints like maximum\n\
number of memory accesses.",
 unsigned, (unsigned nunroll, class loop *loop),
 NULL)

/* True if X is a legitimate MODE-mode immediate operand.  */
DEFHOOK
(legitimate_constant_p,
 "This hook returns true if @var{x} is a legitimate constant for a\n\
@var{mode}-mode immediate operand on the target machine.  You can assume that\n\
@var{x} satisfies @code{CONSTANT_P}, so you need not check this.\n\
\n\
The default definition returns true.",
 bool, (machine_mode mode, rtx x),
 hook_bool_mode_rtx_true)

/* True if X is a TLS operand whose value should be pre-computed.  */
DEFHOOK
(precompute_tls_p,
 "This hook returns true if @var{x} is a TLS operand on the target\n\
machine that should be pre-computed when used as the argument in a call.\n\
You can assume that @var{x} satisfies @code{CONSTANT_P}, so you need not \n\
check this.\n\
\n\
The default definition returns false.",
 bool, (machine_mode mode, rtx x),
 hook_bool_mode_rtx_false)

/* True if the constant X cannot be placed in the constant pool.  */
DEFHOOK
(cannot_force_const_mem,
 "This hook should return true if @var{x} is of a form that cannot (or\n\
should not) be spilled to the constant pool.  @var{mode} is the mode\n\
of @var{x}.\n\
\n\
The default version of this hook returns false.\n\
\n\
The primary reason to define this hook is to prevent reload from\n\
deciding that a non-legitimate constant would be better reloaded\n\
from the constant pool instead of spilling and reloading a register\n\
holding the constant.  This restriction is often true of addresses\n\
of TLS symbols for various targets.",
 bool, (machine_mode mode, rtx x),
 hook_bool_mode_rtx_false)

DEFHOOK_UNDOC
(cannot_copy_insn_p,
 "True if the insn @var{x} cannot be duplicated.",
 bool, (rtx_insn *), NULL)

/* True if X is considered to be commutative.  */
DEFHOOK
(commutative_p,
 "This target hook returns @code{true} if @var{x} is considered to be commutative.\n\
Usually, this is just COMMUTATIVE_P (@var{x}), but the HP PA doesn't consider\n\
PLUS to be commutative inside a MEM@.  @var{outer_code} is the rtx code\n\
of the enclosing rtl, if known, otherwise it is UNKNOWN.",
 bool, (const_rtx x, int outer_code),
 hook_bool_const_rtx_commutative_p)

/* True if ADDR is an address-expression whose effect depends
   on the mode of the memory reference it is used in.  */
DEFHOOK
(mode_dependent_address_p,
 "This hook returns @code{true} if memory address @var{addr} in address\n\
space @var{addrspace} can have\n\
different meanings depending on the machine mode of the memory\n\
reference it is used for or if the address is valid for some modes\n\
but not others.\n\
\n\
Autoincrement and autodecrement addresses typically have mode-dependent\n\
effects because the amount of the increment or decrement is the size\n\
of the operand being addressed.  Some machines have other mode-dependent\n\
addresses.  Many RISC machines have no mode-dependent addresses.\n\
\n\
You may assume that @var{addr} is a valid address for the machine.\n\
\n\
The default version of this hook returns @code{false}.",
 bool, (const_rtx addr, addr_space_t addrspace),
 default_mode_dependent_address_p)

/* Given an invalid address X for a given machine mode, try machine-specific
   ways to make it legitimate.  Return X or an invalid address on failure.  */
DEFHOOK
(legitimize_address,
 "This hook is given an invalid memory address @var{x} for an\n\
operand of mode @var{mode} and should try to return a valid memory\n\
address.\n\
\n\
@findex break_out_memory_refs\n\
@var{x} will always be the result of a call to @code{break_out_memory_refs},\n\
and @var{oldx} will be the operand that was given to that function to produce\n\
@var{x}.\n\
\n\
The code of the hook should not alter the substructure of\n\
@var{x}.  If it transforms @var{x} into a more legitimate form, it\n\
should return the new @var{x}.\n\
\n\
It is not necessary for this hook to come up with a legitimate address,\n\
with the exception of native TLS addresses (@pxref{Emulated TLS}).\n\
The compiler has standard ways of doing so in all cases.  In fact, if\n\
the target supports only emulated TLS, it\n\
is safe to omit this hook or make it return @var{x} if it cannot find\n\
a valid way to legitimize the address.  But often a machine-dependent\n\
strategy can generate better code.",
 rtx, (rtx x, rtx oldx, machine_mode mode),
 default_legitimize_address)

/* Given an address RTX, undo the effects of LEGITIMIZE_ADDRESS.  */
DEFHOOK
(delegitimize_address,
 "This hook is used to undo the possibly obfuscating effects of the\n\
@code{LEGITIMIZE_ADDRESS} and @code{LEGITIMIZE_RELOAD_ADDRESS} target\n\
macros.  Some backend implementations of these macros wrap symbol\n\
references inside an @code{UNSPEC} rtx to represent PIC or similar\n\
addressing modes.  This target hook allows GCC's optimizers to understand\n\
the semantics of these opaque @code{UNSPEC}s by converting them back\n\
into their original form.",
 rtx, (rtx x),
 delegitimize_mem_from_attrs)

/* Given an RTX, return true if it is not ok to emit it into debug info
   section.  */
DEFHOOK
(const_not_ok_for_debug_p,
 "This hook should return true if @var{x} should not be emitted into\n\
debug sections.",
 bool, (rtx x),
 default_const_not_ok_for_debug_p)

/* Given an address RTX, say whether it is valid.  */
DEFHOOK
(legitimate_address_p,
 "A function that returns whether @var{x} (an RTX) is a legitimate memory\n\
address on the target machine for a memory operand of mode @var{mode}.\n\
If @var{ch} is not @code{ERROR_MARK}, it can be called from middle-end to\n\
determine if it is valid to use @var{x} as a memory operand for RTX insn\n\
which is generated for the given code_helper @var{ch}.  For example,\n\
assuming the given @var{ch} is IFN_LEN_LOAD, on some target its underlying\n\
hardware instructions support fewer addressing modes than what are for the\n\
normal vector load and store, then with this @var{ch} target can know the\n\
actual use context and return more exact result.\n\
\n\
Legitimate addresses are defined in two variants: a strict variant and a\n\
non-strict one.  The @var{strict} parameter chooses which variant is\n\
desired by the caller.\n\
\n\
The strict variant is used in the reload pass.  It must be defined so\n\
that any pseudo-register that has not been allocated a hard register is\n\
considered a memory reference.  This is because in contexts where some\n\
kind of register is required, a pseudo-register with no hard register\n\
must be rejected.  For non-hard registers, the strict variant should look\n\
up the @code{reg_renumber} array; it should then proceed using the hard\n\
register number in the array, or treat the pseudo as a memory reference\n\
if the array holds @code{-1}.\n\
\n\
The non-strict variant is used in other passes.  It must be defined to\n\
accept all pseudo-registers in every context where some kind of\n\
register is required.\n\
\n\
Normally, constant addresses which are the sum of a @code{symbol_ref}\n\
and an integer are stored inside a @code{const} RTX to mark them as\n\
constant.  Therefore, there is no need to recognize such sums\n\
specifically as legitimate addresses.  Normally you would simply\n\
recognize any @code{const} as legitimate.\n\
\n\
Usually @code{PRINT_OPERAND_ADDRESS} is not prepared to handle constant\n\
sums that are not marked with  @code{const}.  It assumes that a naked\n\
@code{plus} indicates indexing.  If so, then you @emph{must} reject such\n\
naked constant sums as illegitimate addresses, so that none of them will\n\
be given to @code{PRINT_OPERAND_ADDRESS}.\n\
\n\
@cindex @code{TARGET_ENCODE_SECTION_INFO} and address validation\n\
On some machines, whether a symbolic address is legitimate depends on\n\
the section that the address refers to.  On these machines, define the\n\
target hook @code{TARGET_ENCODE_SECTION_INFO} to store the information\n\
into the @code{symbol_ref}, and then check for it here.  When you see a\n\
@code{const}, you will have to look inside it to find the\n\
@code{symbol_ref} in order to determine the section.  @xref{Assembler\n\
Format}.\n\
\n\
@cindex @code{GO_IF_LEGITIMATE_ADDRESS}\n\
Some ports are still using a deprecated legacy substitute for\n\
this hook, the @code{GO_IF_LEGITIMATE_ADDRESS} macro.  This macro\n\
has this syntax:\n\
\n\
@example\n\
#define GO_IF_LEGITIMATE_ADDRESS (@var{mode}, @var{x}, @var{label})\n\
@end example\n\
\n\
@noindent\n\
and should @code{goto @var{label}} if the address @var{x} is a valid\n\
address on the target machine for a memory operand of mode @var{mode}.\n\
\n\
@findex REG_OK_STRICT\n\
Compiler source files that want to use the strict variant of this\n\
macro define the macro @code{REG_OK_STRICT}.  You should use an\n\
@code{#ifdef REG_OK_STRICT} conditional to define the strict variant in\n\
that case and the non-strict variant otherwise.\n\
\n\
Using the hook is usually simpler because it limits the number of\n\
files that are recompiled when changes are made.",
 bool, (machine_mode mode, rtx x, bool strict, code_helper ch),
 default_legitimate_address_p)

/* True if the given constant can be put into an object_block.  */
DEFHOOK
(use_blocks_for_constant_p,
 "This hook should return true if pool entries for constant @var{x} can\n\
be placed in an @code{object_block} structure.  @var{mode} is the mode\n\
of @var{x}.\n\
\n\
The default version returns false for all constants.",
 bool, (machine_mode mode, const_rtx x),
 hook_bool_mode_const_rtx_false)

/* True if the given decl can be put into an object_block.  */
DEFHOOK
(use_blocks_for_decl_p,
 "This hook should return true if pool entries for @var{decl} should\n\
be placed in an @code{object_block} structure.\n\
\n\
The default version returns true for all decls.",
 bool, (const_tree decl),
 hook_bool_const_tree_true)

/* The minimum and maximum byte offsets for anchored addresses.  */
DEFHOOKPOD
(min_anchor_offset,
 "The minimum offset that should be applied to a section anchor.\n\
On most targets, it should be the smallest offset that can be\n\
applied to a base register while still giving a legitimate address\n\
for every mode.  The default value is 0.",
 HOST_WIDE_INT, 0)

DEFHOOKPOD
(max_anchor_offset,
 "Like @code{TARGET_MIN_ANCHOR_OFFSET}, but the maximum (inclusive)\n\
offset that should be applied to section anchors.  The default\n\
value is 0.",
 HOST_WIDE_INT, 0)

/* True if section anchors can be used to access the given symbol.  */
DEFHOOK
(use_anchors_for_symbol_p,
 "Return true if GCC should attempt to use anchors to access @code{SYMBOL_REF}\n\
@var{x}.  You can assume @samp{SYMBOL_REF_HAS_BLOCK_INFO_P (@var{x})} and\n\
@samp{!SYMBOL_REF_ANCHOR_P (@var{x})}.\n\
\n\
The default version is correct for most targets, but you might need to\n\
intercept this hook to handle things like target-specific attributes\n\
or target-specific sections.",
 bool, (const_rtx x),
 default_use_anchors_for_symbol_p)

/* True if target supports indirect functions.  */
DEFHOOK
(has_ifunc_p,
 "It returns true if the target supports GNU indirect functions.\n\
The support includes the assembler, linker and dynamic linker.\n\
The default value of this hook is based on target's libc.",
 bool, (void),
 default_has_ifunc_p)

/* True if it is OK to reference indirect function resolvers locally.  */
DEFHOOK
(ifunc_ref_local_ok,
 "Return true if it is OK to reference indirect function resolvers\n\
locally.  The default is to return false.",
 bool, (void),
 hook_bool_void_false)

/* True if it is OK to do sibling call optimization for the specified
   call expression EXP.  DECL will be the called function, or NULL if
   this is an indirect call.  */
DEFHOOK
(function_ok_for_sibcall,
 "True if it is OK to do sibling call optimization for the specified\n\
call expression @var{exp}.  @var{decl} will be the called function,\n\
or @code{NULL} if this is an indirect call.\n\
\n\
It is not uncommon for limitations of calling conventions to prevent\n\
tail calls to functions outside the current unit of translation, or\n\
during PIC compilation.  The hook is used to enforce these restrictions,\n\
as the @code{sibcall} md pattern cannot fail, or fall over to a\n\
``normal'' call.  The criteria for successful sibling call optimization\n\
may vary greatly between different architectures.",
 bool, (tree decl, tree exp),
 hook_bool_tree_tree_false)

/* Establish appropriate back-end context for processing the function
   FNDECL.  The argument might be NULL to indicate processing at top
   level, outside of any function scope.  */
DEFHOOK
(set_current_function,
 "The compiler invokes this hook whenever it changes its current function\n\
context (@code{cfun}).  You can define this function if\n\
the back end needs to perform any initialization or reset actions on a\n\
per-function basis.  For example, it may be used to implement function\n\
attributes that affect register usage or code generation patterns.\n\
The argument @var{decl} is the declaration for the new function context,\n\
and may be null to indicate that the compiler has left a function context\n\
and is returning to processing at the top level.\n\
The default hook function does nothing.\n\
\n\
GCC sets @code{cfun} to a dummy function context during initialization of\n\
some parts of the back end.  The hook function is not invoked in this\n\
situation; you need not worry about the hook being invoked recursively,\n\
or when the back end is in a partially-initialized state.\n\
@code{cfun} might be @code{NULL} to indicate processing at top level,\n\
outside of any function scope.",
 void, (tree decl), hook_void_tree)

/* True if EXP should be placed in a "small data" section.  */
DEFHOOK
(in_small_data_p,
 "Returns true if @var{exp} should be placed into a ``small data'' section.\n\
The default version of this hook always returns false.",
 bool, (const_tree exp),
 hook_bool_const_tree_false)

/* True if EXP names an object for which name resolution must resolve
   to the current executable or shared library.  */
DEFHOOK
(binds_local_p,
 "Returns true if @var{exp} names an object for which name resolution\n\
rules must resolve to the current ``module'' (dynamic shared library\n\
or executable image).\n\
\n\
The default version of this hook implements the name resolution rules\n\
for ELF, which has a looser model of global name binding than other\n\
currently supported object file formats.",
 bool, (const_tree exp),
 default_binds_local_p)

/* Check if profiling code is before or after prologue.  */
DEFHOOK
(profile_before_prologue,
 "It returns true if target wants profile code emitted before prologue.\n\n\
The default version of this hook use the target macro\n\
@code{PROFILE_BEFORE_PROLOGUE}.",
 bool, (void),
 default_profile_before_prologue)

/* Return true if a leaf function should stay leaf even with profiling
   enabled.  */
DEFHOOK
(keep_leaf_when_profiled,
 "This target hook returns true if the target wants the leaf flag for\n\
the current function to stay true even if it calls mcount.  This might\n\
make sense for targets using the leaf flag only to determine whether a\n\
stack frame needs to be generated or not and for which the call to\n\
mcount is generated before the function prologue.",
 bool, (void),
 default_keep_leaf_when_profiled)

/* Modify and return the identifier of a DECL's external name,
   originally identified by ID, as required by the target,
   (eg, append @nn to windows32 stdcall function names).
   The default is to return ID without modification. */
DEFHOOK
(mangle_decl_assembler_name,
 "Define this hook if you need to postprocess the assembler name generated\n\
by target-independent code.  The @var{id} provided to this hook will be\n\
the computed name (e.g., the macro @code{DECL_NAME} of the @var{decl} in C,\n\
or the mangled name of the @var{decl} in C++).  The return value of the\n\
hook is an @code{IDENTIFIER_NODE} for the appropriate mangled name on\n\
your target system.  The default implementation of this hook just\n\
returns the @var{id} provided.",
 tree, (tree decl, tree  id),
 default_mangle_decl_assembler_name)

/* Do something target-specific to record properties of the DECL into
   the associated SYMBOL_REF.  */
DEFHOOK
(encode_section_info,
 "Define this hook if references to a symbol or a constant must be\n\
treated differently depending on something about the variable or\n\
function named by the symbol (such as what section it is in).\n\
\n\
The hook is executed immediately after rtl has been created for\n\
@var{decl}, which may be a variable or function declaration or\n\
an entry in the constant pool.  In either case, @var{rtl} is the\n\
rtl in question.  Do @emph{not} use @code{DECL_RTL (@var{decl})}\n\
in this hook; that field may not have been initialized yet.\n\
\n\
In the case of a constant, it is safe to assume that the rtl is\n\
a @code{mem} whose address is a @code{symbol_ref}.  Most decls\n\
will also have this form, but that is not guaranteed.  Global\n\
register variables, for instance, will have a @code{reg} for their\n\
rtl.  (Normally the right thing to do with such unusual rtl is\n\
leave it alone.)\n\
\n\
The @var{new_decl_p} argument will be true if this is the first time\n\
that @code{TARGET_ENCODE_SECTION_INFO} has been invoked on this decl.  It will\n\
be false for subsequent invocations, which will happen for duplicate\n\
declarations.  Whether or not anything must be done for the duplicate\n\
declaration depends on whether the hook examines @code{DECL_ATTRIBUTES}.\n\
@var{new_decl_p} is always true when the hook is called for a constant.\n\
\n\
@cindex @code{SYMBOL_REF_FLAG}, in @code{TARGET_ENCODE_SECTION_INFO}\n\
The usual thing for this hook to do is to record flags in the\n\
@code{symbol_ref}, using @code{SYMBOL_REF_FLAG} or @code{SYMBOL_REF_FLAGS}.\n\
Historically, the name string was modified if it was necessary to\n\
encode more than one bit of information, but this practice is now\n\
discouraged; use @code{SYMBOL_REF_FLAGS}.\n\
\n\
The default definition of this hook, @code{default_encode_section_info}\n\
in @file{varasm.cc}, sets a number of commonly-useful bits in\n\
@code{SYMBOL_REF_FLAGS}.  Check whether the default does what you need\n\
before overriding it.",
 void, (tree decl, rtx rtl, int new_decl_p),
 default_encode_section_info)

/* Undo the effects of encode_section_info on the symbol string.  */
DEFHOOK
(strip_name_encoding,
 "Decode @var{name} and return the real name part, sans\n\
the characters that @code{TARGET_ENCODE_SECTION_INFO}\n\
may have added.",
 const char *, (const char *name),
 default_strip_name_encoding)

/* If shift optabs for MODE are known to always truncate the shift count,
   return the mask that they apply.  Return 0 otherwise.  */
DEFHOOK
(shift_truncation_mask,
 "This function describes how the standard shift patterns for @var{mode}\n\
deal with shifts by negative amounts or by more than the width of the mode.\n\
@xref{shift patterns}.\n\
\n\
On many machines, the shift patterns will apply a mask @var{m} to the\n\
shift count, meaning that a fixed-width shift of @var{x} by @var{y} is\n\
equivalent to an arbitrary-width shift of @var{x} by @var{y & m}.  If\n\
this is true for mode @var{mode}, the function should return @var{m},\n\
otherwise it should return 0.  A return value of 0 indicates that no\n\
particular behavior is guaranteed.\n\
\n\
Note that, unlike @code{SHIFT_COUNT_TRUNCATED}, this function does\n\
@emph{not} apply to general shift rtxes; it applies only to instructions\n\
that are generated by the named shift patterns.\n\
\n\
The default implementation of this function returns\n\
@code{GET_MODE_BITSIZE (@var{mode}) - 1} if @code{SHIFT_COUNT_TRUNCATED}\n\
and 0 otherwise.  This definition is always safe, but if\n\
@code{SHIFT_COUNT_TRUNCATED} is false, and some shift patterns\n\
nevertheless truncate the shift count, you may get better code\n\
by overriding it.",
 unsigned HOST_WIDE_INT, (machine_mode mode),
 default_shift_truncation_mask)

/* Return the number of divisions in the given MODE that should be present,
   so that it is profitable to turn the division into a multiplication by
   the reciprocal.  */
DEFHOOK
(min_divisions_for_recip_mul,
 "When @option{-ffast-math} is in effect, GCC tries to optimize\n\
divisions by the same divisor, by turning them into multiplications by\n\
the reciprocal.  This target hook specifies the minimum number of divisions\n\
that should be there for GCC to perform the optimization for a variable\n\
of mode @var{mode}.  The default implementation returns 3 if the machine\n\
has an instruction for the division, and 2 if it does not.",
 unsigned int, (machine_mode mode),
 default_min_divisions_for_recip_mul)

DEFHOOK
(truly_noop_truncation,
 "This hook returns true if it is safe to ``convert'' a value of\n\
@var{inprec} bits to one of @var{outprec} bits (where @var{outprec} is\n\
smaller than @var{inprec}) by merely operating on it as if it had only\n\
@var{outprec} bits.  The default returns true unconditionally, which\n\
is correct for most machines.  When @code{TARGET_TRULY_NOOP_TRUNCATION}\n\
returns false, the machine description should provide a @code{trunc}\n\
optab to specify the RTL that performs the required truncation.\n\
\n\
If @code{TARGET_MODES_TIEABLE_P} returns false for a pair of modes,\n\
suboptimal code can result if this hook returns true for the corresponding\n\
mode sizes.  Making this hook return false in such cases may improve things.",
 bool, (poly_uint64 outprec, poly_uint64 inprec),
 hook_bool_puint64_puint64_true)

/* If the representation of integral MODE is such that values are
   always sign-extended to a wider mode MODE_REP then return
   SIGN_EXTEND.  Return UNKNOWN otherwise.  */
/* Note that the return type ought to be RTX_CODE, but that's not
   necessarily defined at this point.  */
DEFHOOK
(mode_rep_extended,
 "The representation of an integral mode can be such that the values\n\
are always extended to a wider integral mode.  Return\n\
@code{SIGN_EXTEND} if values of @var{mode} are represented in\n\
sign-extended form to @var{rep_mode}.  Return @code{UNKNOWN}\n\
otherwise.  (Currently, none of the targets use zero-extended\n\
representation this way so unlike @code{LOAD_EXTEND_OP},\n\
@code{TARGET_MODE_REP_EXTENDED} is expected to return either\n\
@code{SIGN_EXTEND} or @code{UNKNOWN}.  Also no target extends\n\
@var{mode} to @var{rep_mode} so that @var{rep_mode} is not the next\n\
widest integral mode and currently we take advantage of this fact.)\n\
\n\
Similarly to @code{LOAD_EXTEND_OP} you may return a non-@code{UNKNOWN}\n\
value even if the extension is not performed on certain hard registers\n\
as long as for the @code{REGNO_REG_CLASS} of these hard registers\n\
@code{TARGET_CAN_CHANGE_MODE_CLASS} returns false.\n\
\n\
Note that @code{TARGET_MODE_REP_EXTENDED} and @code{LOAD_EXTEND_OP}\n\
describe two related properties.  If you define\n\
@code{TARGET_MODE_REP_EXTENDED (mode, word_mode)} you probably also want\n\
to define @code{LOAD_EXTEND_OP (mode)} to return the same type of\n\
extension.\n\
\n\
In order to enforce the representation of @code{mode},\n\
@code{TARGET_TRULY_NOOP_TRUNCATION} should return false when truncating to\n\
@code{mode}.",
 int, (scalar_int_mode mode, scalar_int_mode rep_mode),
 default_mode_rep_extended)

DEFHOOK
(setjmp_preserves_nonvolatile_regs_p,
 "On some targets, it is assumed that the compiler will spill all pseudos\n\
  that are live across a call to @code{setjmp}, while other targets treat\n\
  @code{setjmp} calls as normal function calls.\n\
  \n\
  This hook returns false if @code{setjmp} calls do not preserve all\n\
  non-volatile registers so that gcc that must spill all pseudos that are\n\
  live across @code{setjmp} calls.  Define this to return true if the\n\
  target does not need to spill all pseudos live across @code{setjmp} calls.\n\
  The default implementation conservatively assumes all pseudos must be\n\
  spilled across @code{setjmp} calls.",
 bool, (void),
 hook_bool_void_false)

/* True if MODE is valid for a pointer in __attribute__((mode("MODE"))).  */
DEFHOOK
(valid_pointer_mode,
 "Define this to return nonzero if the port can handle pointers\n\
with machine mode @var{mode}.  The default version of this\n\
hook returns true for both @code{ptr_mode} and @code{Pmode}.",
 bool, (scalar_int_mode mode),
 default_valid_pointer_mode)

/* Disambiguate with errno.  */
DEFHOOK
(ref_may_alias_errno,
 "Define this to return nonzero if the memory reference @var{ref}\n\
may alias with the system C library errno location.  The default\n\
version of this hook assumes the system C library errno location\n\
is either a declaration of type int or accessed by dereferencing\n\
a pointer to int.",
 bool, (ao_ref *ref),
 default_ref_may_alias_errno)

DEFHOOK
(mode_can_transfer_bits,
 "Define this to return false if the mode @var{mode} cannot be used\n\
for memory copying of @code{GET_MODE_SIZE (mode)} units.  This might be\n\
because a register class allowed for @var{mode} has registers that do\n\
not transparently transfer every bit pattern or because the load or\n\
store patterns available for @var{mode} have this issue.\n\
\n\
The default is to assume modes with the same precision as size are fine\n\
to be used.",
 bool, (machine_mode mode),
 NULL)

DEFHOOK
(redzone_clobber,
 "Define this to return some RTL for the @code{redzone} @code{asm} clobber\n\
if target has a red zone and wants to support the @code{redzone} clobber\n\
or return NULL if the clobber should be ignored.\n\
\n\
The default is to ignore the @code{redzone} clobber.",
 rtx, (),
 NULL)

/* Support for named address spaces.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_ADDR_SPACE_"
HOOK_VECTOR (TARGET_ADDR_SPACE_HOOKS, addr_space)

/* MODE to use for a pointer into another address space.  */
DEFHOOK
(pointer_mode,
 "Define this to return the machine mode to use for pointers to\n\
@var{address_space} if the target supports named address spaces.\n\
The default version of this hook returns @code{ptr_mode}.",
 scalar_int_mode, (addr_space_t address_space),
 default_addr_space_pointer_mode)

/* MODE to use for an address in another address space.  */
DEFHOOK
(address_mode,
 "Define this to return the machine mode to use for addresses in\n\
@var{address_space} if the target supports named address spaces.\n\
The default version of this hook returns @code{Pmode}.",
 scalar_int_mode, (addr_space_t address_space),
 default_addr_space_address_mode)

/* True if MODE is valid for a pointer in __attribute__((mode("MODE")))
   in another address space.  */
DEFHOOK
(valid_pointer_mode,
 "Define this to return nonzero if the port can handle pointers\n\
with machine mode @var{mode} to address space @var{as}.  This target\n\
hook is the same as the @code{TARGET_VALID_POINTER_MODE} target hook,\n\
except that it includes explicit named address space support.  The default\n\
version of this hook returns true for the modes returned by either the\n\
@code{TARGET_ADDR_SPACE_POINTER_MODE} or @code{TARGET_ADDR_SPACE_ADDRESS_MODE}\n\
target hooks for the given address space.",
 bool, (scalar_int_mode mode, addr_space_t as),
 default_addr_space_valid_pointer_mode)

/* True if an address is a valid memory address to a given named address
   space for a given mode.  */
DEFHOOK
(legitimate_address_p,
 "Define this to return true if @var{exp} is a valid address for mode\n\
@var{mode} in the named address space @var{as} with the use context\n\
@var{ch}.  The @var{strict} parameter says whether strict addressing\n\
is in effect after reload has finished.  The @var{ch} indicates what\n\
context @var{exp} will be used for.  This target hook is the same as the\n\
@code{TARGET_LEGITIMATE_ADDRESS_P} target hook, except that it includes\n\
explicit named address space support.",
 bool, (machine_mode mode, rtx exp, bool strict, addr_space_t as, code_helper ch),
 default_addr_space_legitimate_address_p)

/* Return an updated address to convert an invalid pointer to a named
   address space to a valid one.  If NULL_RTX is returned use machine
   independent methods to make the address valid.  */
DEFHOOK
(legitimize_address,
 "Define this to modify an invalid address @var{x} to be a valid address\n\
with mode @var{mode} in the named address space @var{as}.  This target\n\
hook is the same as the @code{TARGET_LEGITIMIZE_ADDRESS} target hook,\n\
except that it includes explicit named address space support.",
 rtx, (rtx x, rtx oldx, machine_mode mode, addr_space_t as),
 default_addr_space_legitimize_address)

/* True if one named address space is a subset of another named address. */
DEFHOOK
(subset_p,
 "Define this to return whether the @var{subset} named address space is\n\
contained within the @var{superset} named address space.  Pointers to\n\
a named address space that is a subset of another named address space\n\
will be converted automatically without a cast if used together in\n\
arithmetic operations.  Pointers to a superset address space can be\n\
converted to pointers to a subset address space via explicit casts.",
 bool, (addr_space_t subset, addr_space_t superset),
 default_addr_space_subset_p)

/* True if 0 is a valid address in the address space, or false if
   0 is a NULL in the address space.  */
DEFHOOK
(zero_address_valid,
 "Define this to modify the default handling of address 0 for the\n\
address space.  Return true if 0 should be considered a valid address.",
 bool, (addr_space_t as),
 default_addr_space_zero_address_valid)

/* Function to convert an rtl expression from one address space to another.  */
DEFHOOK
(convert,
 "Define this to convert the pointer expression represented by the RTL\n\
@var{op} with type @var{from_type} that points to a named address\n\
space to a new pointer expression with type @var{to_type} that points\n\
to a different named address space.  When this hook it called, it is\n\
guaranteed that one of the two address spaces is a subset of the other,\n\
as determined by the @code{TARGET_ADDR_SPACE_SUBSET_P} target hook.",
 rtx, (rtx op, tree from_type, tree to_type),
 default_addr_space_convert)

/* Function to encode an address space into dwarf.  */
DEFHOOK
(debug,
 "Define this to define how the address space is encoded in dwarf.\n\
The result is the value to be used with @code{DW_AT_address_class}.",
 int, (addr_space_t as),
 default_addr_space_debug)

/* Function to emit custom diagnostic if an address space is used.  */
DEFHOOK
(diagnose_usage,
 "Define this hook if the availability of an address space depends on\n\
command line options and some diagnostics should be printed when the\n\
address space is used.  This hook is called during parsing and allows\n\
to emit a better diagnostic compared to the case where the address space\n\
was not registered with @code{c_register_addr_space}.  @var{as} is\n\
the address space as registered with @code{c_register_addr_space}.\n\
@var{loc} is the location of the address space qualifier token.\n\
The default implementation does nothing.",
 void, (addr_space_t as, location_t loc),
 default_addr_space_diagnose_usage)

HOOK_VECTOR_END (addr_space)

#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_"

DEFHOOK
(lower_local_decl_alignment,
 "Define this hook to lower alignment of local, parm or result\n\
decl @samp{(@var{decl})}.",
 void, (tree decl),
 hook_void_tree)

DEFHOOK
(static_rtx_alignment,
 "This hook returns the preferred alignment in bits for a\n\
statically-allocated rtx, such as a constant pool entry.  @var{mode}\n\
is the mode of the rtx.  The default implementation returns\n\
@samp{GET_MODE_ALIGNMENT (@var{mode})}.",
 HOST_WIDE_INT, (machine_mode mode),
 default_static_rtx_alignment)

DEFHOOK
(constant_alignment,
 "This hook returns the alignment in bits of a constant that is being\n\
placed in memory.  @var{constant} is the constant and @var{basic_align}\n\
is the alignment that the object would ordinarily have.\n\
\n\
The default definition just returns @var{basic_align}.\n\
\n\
The typical use of this hook is to increase alignment for string\n\
constants to be word aligned so that @code{strcpy} calls that copy\n\
constants can be done inline.  The function\n\
@code{constant_alignment_word_strings} provides such a definition.",
 HOST_WIDE_INT, (const_tree constant, HOST_WIDE_INT basic_align),
 default_constant_alignment)

DEFHOOK
(translate_mode_attribute,
 "Define this hook if during mode attribute processing, the port should\n\
translate machine_mode @var{mode} to another mode.  For example, rs6000's\n\
@code{KFmode}, when it is the same as @code{TFmode}.\n\
\n\
The default version of the hook returns that mode that was passed in.",
 machine_mode, (machine_mode mode),
 default_translate_mode_attribute)

/* True if MODE is valid for the target.  By "valid", we mean able to
   be manipulated in non-trivial ways.  In particular, this means all
   the arithmetic is supported.  */
DEFHOOK
(scalar_mode_supported_p,
 "Define this to return nonzero if the port is prepared to handle\n\
insns involving scalar mode @var{mode}.  For a scalar mode to be\n\
considered supported, all the basic arithmetic and comparisons\n\
must work.\n\
\n\
The default version of this hook returns true for any mode\n\
required to handle the basic C types (as defined by the port).\n\
Included here are the double-word arithmetic supported by the\n\
code in @file{optabs.cc}.",
 bool, (scalar_mode mode),
 default_scalar_mode_supported_p)

/* Similarly for vector modes.  "Supported" here is less strict.  At
   least some operations are supported; need to check optabs or builtins
   for further details.  */
DEFHOOK
(vector_mode_supported_p,
 "Define this to return nonzero if the current target is prepared to handle\n\
insns involving vector mode @var{mode}.  At the very least, it\n\
must have move patterns for this mode.",
 bool, (machine_mode mode),
 hook_bool_mode_false)

DEFHOOK
(vector_mode_supported_any_target_p,
 "Define this to return nonzero if the port is prepared to handle\n\
insns involving vector mode @var{mode} in any target configuration.\n\
Returning @var{true} means that the mode can be used as the @samp{TYPE_MODE}\n\
for vector types.\n\
\n\
The default version of this hook returns true.  The final mode assigned to\n\
@samp{TYPE_MODE} will also be checked against\n\
@code{TARGET_VECTOR_MODE_SUPPORTED_P} to take target configuration into\n\
account.",
 bool, (machine_mode mode),
 hook_bool_mode_true)

DEFHOOK
(compatible_vector_types_p,
 "Return true if there is no target-specific reason for treating\n\
vector types @var{type1} and @var{type2} as distinct types.  The caller\n\
has already checked for target-independent reasons, meaning that the\n\
types are known to have the same mode, to have the same number of elements,\n\
and to have what the caller considers to be compatible element types.\n\
\n\
The main reason for defining this hook is to reject pairs of types\n\
that are handled differently by the target's calling convention.\n\
For example, when a new @var{N}-bit vector architecture is added\n\
to a target, the target may want to handle normal @var{N}-bit\n\
@code{VECTOR_TYPE} arguments and return values in the same way as\n\
before, to maintain backwards compatibility.  However, it may also\n\
provide new, architecture-specific @code{VECTOR_TYPE}s that are passed\n\
and returned in a more efficient way.  It is then important to maintain\n\
a distinction between the ``normal'' @code{VECTOR_TYPE}s and the new\n\
architecture-specific ones.\n\
\n\
The default implementation returns true, which is correct for most targets.",
 bool, (const_tree type1, const_tree type2),
 hook_bool_const_tree_const_tree_true)

DEFHOOK
(vector_alignment,
 "This hook can be used to define the alignment for a vector of type\n\
@var{type}, in order to comply with a platform ABI.  The default is to\n\
require natural alignment for vector types.  The alignment returned by\n\
this hook must be a power-of-two multiple of the default alignment of\n\
the vector element type.",
 HOST_WIDE_INT, (const_tree type),
 default_vector_alignment)

DEFHOOK
(array_mode,
 "Return the mode that GCC should use for an array that has\n\
@var{nelems} elements, with each element having mode @var{mode}.\n\
Return no mode if the target has no special requirements.  In the\n\
latter case, GCC looks for an integer mode of the appropriate size\n\
if available and uses BLKmode otherwise.  Usually the search for the\n\
integer mode is limited to @code{MAX_FIXED_MODE_SIZE}, but the\n\
@code{TARGET_ARRAY_MODE_SUPPORTED_P} hook allows a larger mode to be\n\
used in specific cases.\n\
\n\
The main use of this hook is to specify that an array of vectors should\n\
also have a vector mode.  The default implementation returns no mode.",
 opt_machine_mode, (machine_mode mode, unsigned HOST_WIDE_INT nelems),
 hook_optmode_mode_uhwi_none)

/* True if we should try to use a scalar mode to represent an array,
   overriding the usual MAX_FIXED_MODE limit.  */
DEFHOOK
(array_mode_supported_p,
 "Return true if GCC should try to use a scalar mode to store an array\n\
of @var{nelems} elements, given that each element has mode @var{mode}.\n\
Returning true here overrides the usual @code{MAX_FIXED_MODE} limit\n\
and allows GCC to use any defined integer mode.\n\
\n\
One use of this hook is to support vector load and store operations\n\
that operate on several homogeneous vectors.  For example, ARM NEON\n\
has operations like:\n\
\n\
@smallexample\n\
int8x8x3_t vld3_s8 (const int8_t *)\n\
@end smallexample\n\
\n\
where the return type is defined as:\n\
\n\
@smallexample\n\
typedef struct int8x8x3_t\n\
@{\n\
  int8x8_t val[3];\n\
@} int8x8x3_t;\n\
@end smallexample\n\
\n\
If this hook allows @code{val} to have a scalar mode, then\n\
@code{int8x8x3_t} can have the same mode.  GCC can then store\n\
@code{int8x8x3_t}s in registers rather than forcing them onto the stack.",
 bool, (machine_mode mode, unsigned HOST_WIDE_INT nelems),
 hook_bool_mode_uhwi_false)

DEFHOOK
(libgcc_floating_mode_supported_p,
 "Define this to return nonzero if libgcc provides support for the \n\
floating-point mode @var{mode}, which is known to pass \n\
@code{TARGET_SCALAR_MODE_SUPPORTED_P}.  The default version of this \n\
hook returns true for all of @code{SFmode}, @code{DFmode}, \n\
@code{XFmode} and @code{TFmode}, if such modes exist.",
 bool, (scalar_float_mode mode),
 default_libgcc_floating_mode_supported_p)

DEFHOOK
(floatn_mode,
 "Define this to return the machine mode to use for the type \n\
@code{_Float@var{n}}, if @var{extended} is false, or the type \n\
@code{_Float@var{n}x}, if @var{extended} is true.  If such a type is not\n\
supported, return @code{opt_scalar_float_mode ()}.  The default version of\n\
this hook returns @code{SFmode} for @code{_Float32}, @code{DFmode} for\n\
@code{_Float64} and @code{_Float32x} and @code{TFmode} for \n\
@code{_Float128}, if those modes exist and satisfy the requirements for \n\
those types and pass @code{TARGET_SCALAR_MODE_SUPPORTED_P} and \n\
@code{TARGET_LIBGCC_FLOATING_MODE_SUPPORTED_P}; for @code{_Float64x}, it \n\
returns the first of @code{XFmode} and @code{TFmode} that exists and \n\
satisfies the same requirements; for other types, it returns \n\
@code{opt_scalar_float_mode ()}.  The hook is only called for values\n\
of @var{n} and @var{extended} that are valid according to\n\
ISO/IEC TS 18661-3:2015; that is, @var{n} is one of 32, 64, 128, or,\n\
if @var{extended} is false, 16 or greater than 128 and a multiple of 32.",
 opt_scalar_float_mode, (int n, bool extended),
 default_floatn_mode)

DEFHOOK
(floatn_builtin_p,
  "Define this to return true if the @code{_Float@var{n}} and\n\
@code{_Float@var{n}x} built-in functions should implicitly enable the\n\
built-in function without the @code{__builtin_} prefix in addition to the\n\
normal built-in function with the @code{__builtin_} prefix.  The default is\n\
to only enable built-in functions without the @code{__builtin_} prefix for\n\
the GNU C langauge.  In strict ANSI/ISO mode, the built-in function without\n\
the @code{__builtin_} prefix is not enabled.  The argument @code{FUNC} is the\n\
@code{enum built_in_function} id of the function to be enabled.",
 bool, (int func),
 default_floatn_builtin_p)

/* Compute cost of moving data from a register of class FROM to one of
   TO, using MODE.  */
DEFHOOK
(register_move_cost,
 "This target hook should return the cost of moving data of mode @var{mode}\n\
from a register in class @var{from} to one in class @var{to}.  The classes\n\
are expressed using the enumeration values such as @code{GENERAL_REGS}.\n\
A value of 2 is the default; other values are interpreted relative to\n\
that.\n\
\n\
It is not required that the cost always equal 2 when @var{from} is the\n\
same as @var{to}; on some machines it is expensive to move between\n\
registers if they are not general registers.\n\
\n\
If reload sees an insn consisting of a single @code{set} between two\n\
hard registers, and if @code{TARGET_REGISTER_MOVE_COST} applied to their\n\
classes returns a value of 2, reload does not check to ensure that the\n\
constraints of the insn are met.  Setting a cost of other than 2 will\n\
allow reload to verify that the constraints are met.  You should do this\n\
if the @samp{mov@var{m}} pattern's constraints do not allow such copying.\n\
\n\
The default version of this function returns 2.",
 int, (machine_mode mode, reg_class_t from, reg_class_t to),
 default_register_move_cost)

/* Compute cost of moving registers to/from memory.  */
/* ??? Documenting the argument types for this hook requires a GFDL
   license grant.  Also, the documentation uses a different name for RCLASS.  */
DEFHOOK
(memory_move_cost,
 "This target hook should return the cost of moving data of mode @var{mode}\n\
between a register of class @var{rclass} and memory; @var{in} is @code{false}\n\
if the value is to be written to memory, @code{true} if it is to be read in.\n\
This cost is relative to those in @code{TARGET_REGISTER_MOVE_COST}.\n\
If moving between registers and memory is more expensive than between two\n\
registers, you should add this target hook to express the relative cost.\n\
\n\
If you do not add this target hook, GCC uses a default cost of 4 plus\n\
the cost of copying via a secondary reload register, if one is\n\
needed.  If your machine requires a secondary reload register to copy\n\
between memory and a register of @var{rclass} but the reload mechanism is\n\
more complex than copying via an intermediate, use this target hook to\n\
reflect the actual cost of the move.\n\
\n\
GCC defines the function @code{memory_move_secondary_cost} if\n\
secondary reloads are needed.  It computes the costs due to copying via\n\
a secondary register.  If your machine copies from memory using a\n\
secondary register in the conventional way but the default base value of\n\
4 is not correct for your machine, use this target hook to add some other\n\
value to the result of that function.  The arguments to that function\n\
are the same as to this target hook.",
 int, (machine_mode mode, reg_class_t rclass, bool in),
 default_memory_move_cost)

DEFHOOK
(callee_save_cost,
 "Return the one-off cost of saving or restoring callee-saved registers\n\
(also known as call-preserved registers or non-volatile registers).\n\
The parameters are as follows:\n\
\n\
@itemize\n\
@item\n\
@var{cost_type} is @samp{spill_cost_type::SAVE} for saving a register\n\
and @samp{spill_cost_type::RESTORE} for restoring a register.\n\
\n\
@item\n\
@var{hard_regno} and @var{mode} represent the whole register that\n\
the register allocator is considering using; of these,\n\
@var{nregs} registers are fully or partially callee-saved.\n\
\n\
@item\n\
@var{mem_cost} is the normal cost for storing (for saves)\n\
or loading (for restores) the @var{nregs} registers.\n\
\n\
@item\n\
@var{allocated_callee_regs} is the set of callee-saved registers\n\
that are already in use.\n\
\n\
@item\n\
@var{existing_spills_p} is true if the register allocator has\n\
already decided to spill registers to memory.\n\
@end itemize\n\
\n\
If @var{existing_spills_p} is false, the cost of a save should account\n\
for frame allocations in a way that is consistent with\n\
@code{TARGET_FRAME_ALLOCATION_COST}'s handling of allocations for spills.\n\
Similarly, the cost of a restore should then account for frame deallocations\n\
in a way that is consistent with @code{TARGET_FRAME_ALLOCATION_COST}'s\n\
handling of deallocations.\n\
\n\
Note that this hook should not attempt to apply a frequency scale\n\
to the cost: it is the caller's responsibility to do that where\n\
appropriate.\n\
\n\
The default implementation returns @var{mem_cost}, plus the allocation\n\
or deallocation cost returned by @code{TARGET_FRAME_ALLOCATION_COST},\n\
where appropriate.",
 int, (spill_cost_type cost_type, unsigned int hard_regno,
       machine_mode mode, unsigned int nregs, int mem_cost,
       const HARD_REG_SET &allocated_callee_regs, bool existing_spills_p),
 default_callee_save_cost)

DEFHOOK
(frame_allocation_cost,
 "Return the cost of allocating or deallocating a frame for the sake of\n\
a spill; @var{cost_type} chooses between allocation and deallocation.\n\
The term ``spill'' here includes both forcing a pseudo register to memory\n\
and using caller-saved registers for pseudo registers that are live across\n\
a call.\n\
\n\
This hook is only called if the register allocator has not so far\n\
decided to spill.  The allocator may have decided to use callee-saved\n\
registers; if so, @var{allocated_callee_regs} is the set of callee-saved\n\
registers that the allocator has used.  There might also be other reasons\n\
why a stack frame is already needed; for example, @samp{get_frame_size ()}\n\
might be nonzero, or the target might already require a frame for\n\
target-specific reasons.\n\
\n\
When the register allocator uses this hook to cost spills, it also uses\n\
@code{TARGET_CALLEE_SAVE_COST} to cost new callee-saved registers, passing\n\
@samp{false} as the @var{existing_spills_p} argument.  The intention is to\n\
allow the target to apply an apples-for-apples comparison between the\n\
cost of using callee-saved registers and using spills in cases where the\n\
allocator has not yet committed to using both strategies.\n\
\n\
The default implementation returns 0.",
 int, (frame_cost_type cost_type, const HARD_REG_SET &allocated_callee_regs),
 default_frame_allocation_cost)

DEFHOOK
(use_by_pieces_infrastructure_p,
 "GCC will attempt several strategies when asked to copy between\n\
two areas of memory, or to set, clear or store to memory, for example\n\
when copying a @code{struct}. The @code{by_pieces} infrastructure\n\
implements such memory operations as a sequence of load, store or move\n\
insns.  Alternate strategies are to expand the\n\
@code{cpymem} or @code{setmem} optabs, to emit a library call, or to emit\n\
unit-by-unit, loop-based operations.\n\
\n\
This target hook should return true if, for a memory operation with a\n\
given @var{size} and @var{alignment}, using the @code{by_pieces}\n\
infrastructure is expected to result in better code generation.\n\
Both @var{size} and @var{alignment} are measured in terms of storage\n\
units.\n\
\n\
The parameter @var{op} is one of: @code{CLEAR_BY_PIECES},\n\
@code{MOVE_BY_PIECES}, @code{SET_BY_PIECES}, @code{STORE_BY_PIECES} or\n\
@code{COMPARE_BY_PIECES}.  These describe the type of memory operation\n\
under consideration.\n\
\n\
The parameter @var{speed_p} is true if the code is currently being\n\
optimized for speed rather than size.\n\
\n\
Returning true for higher values of @var{size} can improve code generation\n\
for speed if the target does not provide an implementation of the\n\
@code{cpymem} or @code{setmem} standard names, if the @code{cpymem} or\n\
@code{setmem} implementation would be more expensive than a sequence of\n\
insns, or if the overhead of a library call would dominate that of\n\
the body of the memory operation.\n\
\n\
Returning true for higher values of @code{size} may also cause an increase\n\
in code size, for example where the number of insns emitted to perform a\n\
move would be greater than that of a library call.",
 bool, (unsigned HOST_WIDE_INT size, unsigned int alignment,
        enum by_pieces_operation op, bool speed_p),
 default_use_by_pieces_infrastructure_p)

DEFHOOK
(overlap_op_by_pieces_p,
 "This target hook should return true if when the @code{by_pieces}\n\
infrastructure is used, an offset adjusted unaligned memory operation\n\
in the smallest integer mode for the last piece operation of a memory\n\
region can be generated to avoid doing more than one smaller operations.",
 bool, (void),
 hook_bool_void_false)

DEFHOOK
(compare_by_pieces_branch_ratio,
 "When expanding a block comparison in MODE, gcc can try to reduce the\n\
number of branches at the expense of more memory operations.  This hook\n\
allows the target to override the default choice.  It should return the\n\
factor by which branches should be reduced over the plain expansion with\n\
one comparison per @var{mode}-sized piece.  A port can also prevent a\n\
particular mode from being used for block comparisons by returning a\n\
negative number from this hook.",
 int, (machine_mode mode),
 default_compare_by_pieces_branch_ratio)

DEFHOOK
(slow_unaligned_access,
 "This hook returns true if memory accesses described by the\n\
@var{mode} and @var{alignment} parameters have a cost many times greater\n\
than aligned accesses, for example if they are emulated in a trap handler.\n\
This hook is invoked only for unaligned accesses, i.e.@: when\n\
@code{@var{alignment} < GET_MODE_ALIGNMENT (@var{mode})}.\n\
\n\
When this hook returns true, the compiler will act as if\n\
@code{STRICT_ALIGNMENT} were true when generating code for block\n\
moves.  This can cause significantly more instructions to be produced.\n\
Therefore, do not make this hook return true if unaligned accesses only\n\
add a cycle or two to the time for a memory access.\n\
\n\
The hook must return true whenever @code{STRICT_ALIGNMENT} is true.\n\
The default implementation returns @code{STRICT_ALIGNMENT}.",
 bool, (machine_mode mode, unsigned int align),
 default_slow_unaligned_access)

DEFHOOK
(optab_supported_p,
 "Return true if the optimizers should use optab @var{op} with\n\
modes @var{mode1} and @var{mode2} for optimization type @var{opt_type}.\n\
The optab is known to have an associated @file{.md} instruction\n\
whose C condition is true.  @var{mode2} is only meaningful for conversion\n\
optabs; for direct optabs it is a copy of @var{mode1}.\n\
\n\
For example, when called with @var{op} equal to @code{rint_optab} and\n\
@var{mode1} equal to @code{DFmode}, the hook should say whether the\n\
optimizers should use optab @code{rintdf2}.\n\
\n\
The default hook returns true for all inputs.",
 bool, (int op, machine_mode mode1, machine_mode mode2,
	optimization_type opt_type),
 default_optab_supported_p)

/* True for MODE if the target expects that registers in this mode will
   be allocated to registers in a small register class.  The compiler is
   allowed to use registers explicitly used in the rtl as spill registers
   but it should prevent extending the lifetime of these registers.  */
DEFHOOK
(small_register_classes_for_mode_p,
 "Define this to return nonzero for machine modes for which the port has\n\
small register classes.  If this target hook returns nonzero for a given\n\
@var{mode}, the compiler will try to minimize the lifetime of registers\n\
in @var{mode}.  The hook may be called with @code{VOIDmode} as argument.\n\
In this case, the hook is expected to return nonzero if it returns nonzero\n\
for any mode.\n\
\n\
On some machines, it is risky to let hard registers live across arbitrary\n\
insns.  Typically, these machines have instructions that require values\n\
to be in specific registers (like an accumulator), and reload will fail\n\
if the required hard register is used for another purpose across such an\n\
insn.\n\
\n\
Passes before reload do not know which hard registers will be used\n\
in an instruction, but the machine modes of the registers set or used in\n\
the instruction are already known.  And for some machines, register\n\
classes are small for, say, integer registers but not for floating point\n\
registers.  For example, the AMD x86-64 architecture requires specific\n\
registers for the legacy x86 integer instructions, but there are many\n\
SSE registers for floating point operations.  On such targets, a good\n\
strategy may be to return nonzero from this hook for @code{INTEGRAL_MODE_P}\n\
machine modes but zero for the SSE register classes.\n\
\n\
The default version of this hook returns false for any mode.  It is always\n\
safe to redefine this hook to return with a nonzero value.  But if you\n\
unnecessarily define it, you will reduce the amount of optimizations\n\
that can be performed in some cases.  If you do not define this hook\n\
to return a nonzero value when it is required, the compiler will run out\n\
of spill registers and print a fatal error message.",
 bool, (machine_mode mode),
 hook_bool_mode_false)

/* Register number for a flags register.  Only needs to be defined if the
   target is constrainted to use post-reload comparison elimination.  */
DEFHOOKPOD
(flags_regnum,
 "If the target has a dedicated flags register, and it needs to use the\n\
post-reload comparison elimination pass, or the delay slot filler pass,\n\
then this value should be set appropriately.",
unsigned int, INVALID_REGNUM)

/* Compute a (partial) cost for rtx X.  Return true if the complete
   cost has been computed, and false if subexpressions should be
   scanned.  In either case, *TOTAL contains the cost result.  */
/* Note that OUTER_CODE ought to be RTX_CODE, but that's
   not necessarily defined at this point.  */
DEFHOOK
(rtx_costs,
 "This target hook describes the relative costs of RTL expressions.\n\
\n\
The cost may depend on the precise form of the expression, which is\n\
available for examination in @var{x}, and the fact that @var{x} appears\n\
as operand @var{opno} of an expression with rtx code @var{outer_code}.\n\
That is, the hook can assume that there is some rtx @var{y} such\n\
that @samp{GET_CODE (@var{y}) == @var{outer_code}} and such that\n\
either (a) @samp{XEXP (@var{y}, @var{opno}) == @var{x}} or\n\
(b) @samp{XVEC (@var{y}, @var{opno})} contains @var{x}.\n\
\n\
@var{mode} is @var{x}'s machine mode, or for cases like @code{const_int} that\n\
do not have a mode, the mode in which @var{x} is used.\n\
\n\
In implementing this hook, you can use the construct\n\
@code{COSTS_N_INSNS (@var{n})} to specify a cost equal to @var{n} fast\n\
instructions.\n\
\n\
On entry to the hook, @code{*@var{total}} contains a default estimate\n\
for the cost of the expression.  The hook should modify this value as\n\
necessary.  Traditionally, the default costs are @code{COSTS_N_INSNS (5)}\n\
for multiplications, @code{COSTS_N_INSNS (7)} for division and modulus\n\
operations, and @code{COSTS_N_INSNS (1)} for all other operations.\n\
\n\
When optimizing for code size, i.e.@: when @code{speed} is\n\
false, this target hook should be used to estimate the relative\n\
size cost of an expression, again relative to @code{COSTS_N_INSNS}.\n\
\n\
The hook returns true when all subexpressions of @var{x} have been\n\
processed, and false when @code{rtx_cost} should recurse.",
 bool, (rtx x, machine_mode mode, int outer_code, int opno, int *total, bool speed),
 hook_bool_rtx_mode_int_int_intp_bool_false)

/* Compute the cost of X, used as an address.  Never called with
   invalid addresses.  */
DEFHOOK
(address_cost,
 "This hook computes the cost of an addressing mode that contains\n\
@var{address}.  If not defined, the cost is computed from\n\
the @var{address} expression and the @code{TARGET_RTX_COST} hook.\n\
\n\
For most CISC machines, the default cost is a good approximation of the\n\
true cost of the addressing mode.  However, on RISC machines, all\n\
instructions normally have the same length and execution time.  Hence\n\
all addresses will have equal costs.\n\
\n\
In cases where more than one form of an address is known, the form with\n\
the lowest cost will be used.  If multiple forms have the same, lowest,\n\
cost, the one that is the most complex will be used.\n\
\n\
For example, suppose an address that is equal to the sum of a register\n\
and a constant is used twice in the same basic block.  When this macro\n\
is not defined, the address will be computed in a register and memory\n\
references will be indirect through that register.  On machines where\n\
the cost of the addressing mode containing the sum is no higher than\n\
that of a simple indirect reference, this will produce an additional\n\
instruction and possibly require an additional register.  Proper\n\
specification of this macro eliminates this overhead for such machines.\n\
\n\
This hook is never called with an invalid address.\n\
\n\
On machines where an address involving more than one register is as\n\
cheap as an address computation involving only one register, defining\n\
@code{TARGET_ADDRESS_COST} to reflect this can cause two registers to\n\
be live over a region of code where only one would have been if\n\
@code{TARGET_ADDRESS_COST} were not defined in that manner.  This effect\n\
should be considered in the definition of this macro.  Equivalent costs\n\
should probably only be given to addresses with different numbers of\n\
registers on machines with lots of registers.",
 int, (rtx address, machine_mode mode, addr_space_t as, bool speed),
 default_address_cost)

/* Compute a cost for INSN.  */
DEFHOOK
(insn_cost,
 "This target hook describes the relative costs of RTL instructions.\n\
\n\
In implementing this hook, you can use the construct\n\
@code{COSTS_N_INSNS (@var{n})} to specify a cost equal to @var{n} fast\n\
instructions.\n\
\n\
When optimizing for code size, i.e.@: when @code{speed} is\n\
false, this target hook should be used to estimate the relative\n\
size cost of an expression, again relative to @code{COSTS_N_INSNS}.",
 int, (rtx_insn *insn, bool speed), NULL)

/* Give a cost, in RTX Costs units, for an edge.  Like BRANCH_COST, but with
   well defined units.  */
DEFHOOK
(max_noce_ifcvt_seq_cost,
 "This hook returns a value in the same units as @code{TARGET_RTX_COSTS},\n\
giving the maximum acceptable cost for a sequence generated by the RTL\n\
if-conversion pass when conditional execution is not available.\n\
The RTL if-conversion pass attempts to convert conditional operations\n\
that would require a branch to a series of unconditional operations and\n\
@code{mov@var{mode}cc} insns.  This hook returns the maximum cost of the\n\
unconditional instructions and the @code{mov@var{mode}cc} insns.\n\
RTL if-conversion is cancelled if the cost of the converted sequence\n\
is greater than the value returned by this hook.\n\
\n\
@code{e} is the edge between the basic block containing the conditional\n\
branch to the basic block which would be executed if the condition\n\
were true.\n\
\n\
The default implementation of this hook uses the\n\
@code{max-rtl-if-conversion-[un]predictable} parameters if they are set,\n\
and uses a multiple of @code{BRANCH_COST} otherwise.",
unsigned int, (edge e),
default_max_noce_ifcvt_seq_cost)

/* Return true if the given instruction sequence is a good candidate
   as a replacement for the if-convertible sequence.  */
DEFHOOK
(noce_conversion_profitable_p,
 "This hook returns true if the instruction sequence @code{seq} is a good\n\
candidate as a replacement for the if-convertible sequence described in\n\
@code{if_info}.",
bool, (rtx_insn *seq, struct noce_if_info *if_info),
default_noce_conversion_profitable_p)

/* Return true if new_addr should be preferred over the existing address used by
   memref in insn.  */
DEFHOOK
(new_address_profitable_p,
 "Return @code{true} if it is profitable to replace the address in\n\
@var{memref} with @var{new_addr}.  This allows targets to prevent the\n\
scheduler from undoing address optimizations.  The instruction containing the\n\
memref is @var{insn}.  The default implementation returns @code{true}.",
bool, (rtx memref, rtx_insn * insn, rtx new_addr),
default_new_address_profitable_p)

DEFHOOK
(estimated_poly_value,
 "Return an estimate of the runtime value of @var{val}, for use in\n\
things like cost calculations or profiling frequencies.  @var{kind} is used\n\
to ask for the minimum, maximum, and likely estimates of the value through\n\
the @code{POLY_VALUE_MIN}, @code{POLY_VALUE_MAX} and\n\
@code{POLY_VALUE_LIKELY} values.  The default\n\
implementation returns the lowest possible value of @var{val}.",
 HOST_WIDE_INT, (poly_int64 val, poly_value_estimate_kind kind),
 default_estimated_poly_value)

/* Permit speculative instructions in delay slots during delayed-branch
   scheduling.  */
DEFHOOK
(no_speculation_in_delay_slots_p,
 "This predicate controls the use of the eager delay slot filler to disallow\n\
speculatively executed instructions being placed in delay slots.  Targets\n\
such as certain MIPS architectures possess both branches with and without\n\
delay slots.  As the eager delay slot filler can decrease performance,\n\
disabling it is beneficial when ordinary branches are available.  Use of\n\
delay slot branches filled using the basic filler is often still desirable\n\
as the delay slot can hide a pipeline bubble.",
  bool, (void),
  hook_bool_void_false)

/* Return where to allocate pseudo for a given hard register initial value.  */
DEFHOOK
(allocate_initial_value,
 "\n\
When the initial value of a hard register has been copied in a pseudo\n\
register, it is often not necessary to actually allocate another register\n\
to this pseudo register, because the original hard register or a stack slot\n\
it has been saved into can be used.  @code{TARGET_ALLOCATE_INITIAL_VALUE}\n\
is called at the start of register allocation once for each hard register\n\
that had its initial value copied by using\n\
@code{get_func_hard_reg_initial_val} or @code{get_hard_reg_initial_val}.\n\
Possible values are @code{NULL_RTX}, if you don't want\n\
to do any special allocation, a @code{REG} rtx---that would typically be\n\
the hard register itself, if it is known not to be clobbered---or a\n\
@code{MEM}.\n\
If you are returning a @code{MEM}, this is only a hint for the allocator;\n\
it might decide to use another register anyways.\n\
You may use @code{current_function_is_leaf} or \n\
@code{REG_N_SETS} in the hook to determine if the hard\n\
register in question will not be clobbered.\n\
The default value of this hook is @code{NULL}, which disables any special\n\
allocation.",
 rtx, (rtx hard_reg), NULL)

/* Return nonzero if evaluating UNSPEC X might cause a trap.
   FLAGS has the same meaning as in rtlanal.cc: may_trap_p_1.  */
DEFHOOK
(unspec_may_trap_p,
 "This target hook returns nonzero if @var{x}, an @code{unspec} might cause\n\
a trap.  Targets can use this hook to enhance precision of analysis for\n\
@code{unspec} operations.  You may call @code{may_trap_p_1} to analyze inner\n\
elements of @var{x} in which case @var{flags} should be passed along.",
 int, (const_rtx x, unsigned flags),
 default_unspec_may_trap_p)

/* Given a register, this hook should return a parallel of registers
   to represent where to find the register pieces.  Define this hook
   if the register and its mode are represented in Dwarf in
   non-contiguous locations, or if the register should be
   represented in more than one register in Dwarf.  Otherwise, this
   hook should return NULL_RTX.  */
DEFHOOK
(dwarf_register_span,
 "Given a register, this hook should return a parallel of registers to\n\
represent where to find the register pieces.  Define this hook if the\n\
register and its mode are represented in Dwarf in non-contiguous\n\
locations, or if the register should be represented in more than one\n\
register in Dwarf.  Otherwise, this hook should return @code{NULL_RTX}.\n\
If not defined, the default is to return @code{NULL_RTX}.",
 rtx, (rtx reg),
 hook_rtx_rtx_null)

/* Given a register return the mode of the corresponding DWARF frame
   register.  */
DEFHOOK
(dwarf_frame_reg_mode,
 "Given a register, this hook should return the mode which the\n\
corresponding Dwarf frame register should have.  This is normally\n\
used to return a smaller mode than the raw mode to prevent call\n\
clobbered parts of a register altering the frame register size",
 machine_mode, (int regno),
 default_dwarf_frame_reg_mode)

/* Print out architecture-specific CFI directives to the assembly file.  */
DEFHOOK
(output_cfi_directive,
 "This hook handles architecture-specific CFI directives and prints\n\
them out to the assembly file @var{f}.\n\
Return true if a architecture-specific directive was found, false\n\
otherwise.",
 bool, (FILE * f, dw_cfi_ref cfi),
 hook_bool_FILEptr_dwcfiptr_false)

DEFHOOK
(dw_cfi_oprnd1_desc,
 "This hook informs the caller what the architecture-specific directives\n\
takes as a first operand.\n\
Return true if a architecture-specific directive was found and\n\
@var{oprnd_type} is set, false otherwise and @var{oprnd_type} is not\n\
modified.",
 bool, (dwarf_call_frame_info cfi_opc, dw_cfi_oprnd_type & oprnd_type),
 hook_bool_dwcfi_dwcfioprndtyperef_false)

/* If expand_builtin_init_dwarf_reg_sizes needs to fill in table
   entries not corresponding directly to registers below
   FIRST_PSEUDO_REGISTER, this hook should generate the necessary
   code, given the address of the table.  */
DEFHOOK
(init_dwarf_reg_sizes_extra,
 "If some registers are represented in Dwarf-2 unwind information in\n\
multiple pieces, define this hook to fill in information about the\n\
sizes of those pieces in the table used by the unwinder at runtime.\n\
It will be called by @code{expand_builtin_init_dwarf_reg_sizes} after\n\
filling in a single size corresponding to each hard register;\n\
@var{address} is the address of the table.",
 void, (tree address),
 hook_void_tree)

/* Fetch the fixed register(s) which hold condition codes, for
   targets where it makes sense to look for duplicate assignments to
   the condition codes.  This should return true if there is such a
   register, false otherwise.  The arguments should be set to the
   fixed register numbers.  Up to two condition code registers are
   supported.  If there is only one for this target, the int pointed
   at by the second argument should be set to -1.  */
DEFHOOK
(fixed_condition_code_regs,
 "On targets which use a hard\n\
register rather than a pseudo-register to hold condition codes, the\n\
regular CSE passes are often not able to identify cases in which the\n\
hard register is set to a common value.  Use this hook to enable a\n\
small pass which optimizes such cases.  This hook should return true\n\
to enable this pass, and it should set the integers to which its\n\
arguments point to the hard register numbers used for condition codes.\n\
When there is only one such register, as is true on most systems, the\n\
integer pointed to by @var{p2} should be set to\n\
@code{INVALID_REGNUM}.\n\
\n\
The default version of this hook returns false.",
 bool, (unsigned int *p1, unsigned int *p2),
 hook_bool_uintp_uintp_false)

/* If two condition code modes are compatible, return a condition
     code mode which is compatible with both, such that a comparison
     done in the returned mode will work for both of the original
     modes.  If the condition code modes are not compatible, return
     VOIDmode.  */
DEFHOOK
(cc_modes_compatible,
 "On targets which use multiple condition code modes in class\n\
@code{MODE_CC}, it is sometimes the case that a comparison can be\n\
validly done in more than one mode.  On such a system, define this\n\
target hook to take two mode arguments and to return a mode in which\n\
both comparisons may be validly done.  If there is no such mode,\n\
return @code{VOIDmode}.\n\
\n\
The default version of this hook checks whether the modes are the\n\
same.  If they are, it returns that mode.  If they are different, it\n\
returns @code{VOIDmode}.",
 machine_mode, (machine_mode m1, machine_mode m2),
 default_cc_modes_compatible)

DEFHOOK
(use_late_prologue_epilogue,
 "Return true if the current function's prologue and epilogue should\n\
be emitted late in the pass pipeline, instead of at the usual point.\n\
\n\
Normally, the prologue and epilogue sequences are introduced soon after\n\
register allocation is complete.  The advantage of this approach is that\n\
it allows the prologue and epilogue instructions to be optimized and\n\
scheduled with other code in the function.  However, some targets\n\
require the prologue and epilogue to be the first and last sequences\n\
executed by the function, with no variation allowed.  This hook should\n\
return true on such targets.\n\
\n\
The default implementation returns false, which is correct for most\n\
targets.  The hook should only return true if there is a specific\n\
target limitation that cannot be described in RTL.  For example,\n\
the hook might return true if the prologue and epilogue need to switch\n\
between instruction sets.",
 bool, (),
 hook_bool_void_false)

DEFHOOK
(emit_epilogue_for_sibcall,
 "If defined, this hook emits an epilogue sequence for sibling (tail)\n\
call instruction @var{call}.  Another way of providing epilogues\n\
for sibling calls is to define the @code{sibcall_epilogue} instruction\n\
pattern; the main advantage of this hook over the pattern is that it\n\
has access to the call instruction.",
 void, (rtx_call_insn *call), NULL)

/* Do machine-dependent code transformations.  Called just before
     delayed-branch scheduling.  */
DEFHOOK
(machine_dependent_reorg,
 "If non-null, this hook performs a target-specific pass over the\n\
instruction stream.  The compiler will run it at all optimization levels,\n\
just before the point at which it normally does delayed-branch scheduling.\n\
\n\
The exact purpose of the hook varies from target to target.  Some use\n\
it to do transformations that are necessary for correctness, such as\n\
laying out in-function constant pools or avoiding hardware hazards.\n\
Others use it as an opportunity to do some machine-dependent optimizations.\n\
\n\
You need not implement the hook if it has nothing to do.  The default\n\
definition is null.",
 void, (void), NULL)

/* Create the __builtin_va_list type.  */
DEFHOOK
(build_builtin_va_list,
 "This hook returns a type node for @code{va_list} for the target.\n\
The default version of the hook returns @code{void*}.",
 tree, (void),
 std_build_builtin_va_list)

/* Enumerate the va list variants.  */
DEFHOOK
(enum_va_list_p,
 "This target hook is used in function @code{c_common_nodes_and_builtins}\n\
to iterate through the target specific builtin types for va_list. The\n\
variable @var{idx} is used as iterator. @var{pname} has to be a pointer\n\
to a @code{const char *} and @var{ptree} a pointer to a @code{tree} typed\n\
variable.\n\
The arguments @var{pname} and @var{ptree} are used to store the result of\n\
this macro and are set to the name of the va_list builtin type and its\n\
internal type.\n\
If the return value of this macro is zero, then there is no more element.\n\
Otherwise the @var{IDX} should be increased for the next call of this\n\
macro to iterate through all types.",
 int, (int idx, const char **pname, tree *ptree),
 NULL)

/* Get the cfun/fndecl calling abi __builtin_va_list type.  */
DEFHOOK
(fn_abi_va_list,
 "This hook returns the va_list type of the calling convention specified by\n\
@var{fndecl}.\n\
The default version of this hook returns @code{va_list_type_node}.",
 tree, (tree fndecl),
 std_fn_abi_va_list)

/* Get the __builtin_va_list type dependent on input type.  */
DEFHOOK
(canonical_va_list_type,
 "This hook returns the va_list type of the calling convention specified by the\n\
type of @var{type}. If @var{type} is not a valid va_list type, it returns\n\
@code{NULL_TREE}.",
 tree, (tree type),
 std_canonical_va_list_type)

/* ??? Documenting this hook requires a GFDL license grant.  */
DEFHOOK_UNDOC
(expand_builtin_va_start,
"Expand the @code{__builtin_va_start} builtin.",
 void, (tree valist, rtx nextarg), NULL)

/* Gimplifies a VA_ARG_EXPR.  */
DEFHOOK
(gimplify_va_arg_expr,
 "This hook performs target-specific gimplification of\n\
@code{VA_ARG_EXPR}.  The first two parameters correspond to the\n\
arguments to @code{va_arg}; the latter two are as in\n\
@code{gimplify.cc:gimplify_expr}.",
 tree, (tree valist, tree type, gimple_seq *pre_p, gimple_seq *post_p),
 std_gimplify_va_arg_expr)

/* Validity-checking routines for PCH files, target-specific.
   get_pch_validity returns a pointer to the data to be stored,
   and stores the size in its argument.  pch_valid_p gets the same
   information back and returns NULL if the PCH is valid,
   or an error message if not.  */
DEFHOOK
(get_pch_validity,
 "This hook returns a pointer to the data needed by\n\
@code{TARGET_PCH_VALID_P} and sets\n\
@samp{*@var{sz}} to the size of the data in bytes.",
 void *, (size_t *sz),
 default_get_pch_validity)

DEFHOOK
(pch_valid_p,
 "This hook checks whether the options used to create a PCH file are\n\
compatible with the current settings.  It returns @code{NULL}\n\
if so and a suitable error message if not.  Error messages will\n\
be presented to the user and must be localized using @samp{_(@var{msg})}.\n\
\n\
@var{data} is the data that was returned by @code{TARGET_GET_PCH_VALIDITY}\n\
when the PCH file was created and @var{sz} is the size of that data in bytes.\n\
It's safe to assume that the data was created by the same version of the\n\
compiler, so no format checking is needed.\n\
\n\
The default definition of @code{default_pch_valid_p} should be\n\
suitable for most targets.",
 const char *, (const void *data, size_t sz),
 default_pch_valid_p)

DEFHOOK
(prepare_pch_save,
 "Called before writing out a PCH file.  If the target has some\n\
garbage-collected data that needs to be in a particular state on PCH loads,\n\
it can use this hook to enforce that state.  Very few targets need\n\
to do anything here.",
 void, (void),
 hook_void_void)

/* If nonnull, this function checks whether a PCH file with the
   given set of target flags can be used.  It returns NULL if so,
   otherwise it returns an error message.  */
DEFHOOK
(check_pch_target_flags,
 "If this hook is nonnull, the default implementation of\n\
@code{TARGET_PCH_VALID_P} will use it to check for compatible values\n\
of @code{target_flags}.  @var{pch_flags} specifies the value that\n\
@code{target_flags} had when the PCH file was created.  The return\n\
value is the same as for @code{TARGET_PCH_VALID_P}.",
 const char *, (int pch_flags), NULL)

/* True if the compiler should give an enum type only as many
   bytes as it takes to represent the range of possible values of
   that type.  */
DEFHOOK
(default_short_enums,
 "This target hook should return true if the compiler should give an\n\
@code{enum} type only as many bytes as it takes to represent the range\n\
of possible values of that type.  It should return false if all\n\
@code{enum} types should be allocated like @code{int}.\n\
\n\
The default is to return false.",
 bool, (void),
 hook_bool_void_false)

/* This target hook returns an rtx that is used to store the address
   of the current frame into the built-in setjmp buffer.  */
DEFHOOK
(builtin_setjmp_frame_value,
 "This target hook should return an rtx that is used to store\n\
the address of the current frame into the built in @code{setjmp} buffer.\n\
The default value, @code{virtual_stack_vars_rtx}, is correct for most\n\
machines.  One reason you may need to define this target hook is if\n\
@code{hard_frame_pointer_rtx} is the appropriate value on your machine.",
 rtx, (void),
 default_builtin_setjmp_frame_value)

/* This target hook should manipulate the outputs, inputs, constraints,
   and clobbers the port wishes for pre-processing the asm.  */
DEFHOOK
(md_asm_adjust,
 "This target hook may add @dfn{clobbers} to @var{clobbers} and\n\
@var{clobbered_regs} for any hard regs the port wishes to automatically\n\
clobber for an asm.  It can also add hard registers that are used by the\n\
asm to @var{uses}.  The @var{outputs} and @var{inputs} may be inspected\n\
to avoid clobbering a register that is already used by the asm.  @var{loc}\n\
is the source location of the asm.\n\
\n\
It may modify the @var{outputs}, @var{inputs}, @var{input_modes}, and\n\
@var{constraints} as necessary for other pre-processing.  In this case the\n\
return value is a sequence of insns to emit after the asm.  Note that\n\
changes to @var{inputs} must be accompanied by the corresponding changes\n\
to @var{input_modes}.",
 rtx_insn *,
 (vec<rtx>& outputs, vec<rtx>& inputs, vec<machine_mode>& input_modes,
  vec<const char *>& constraints, vec<rtx>& usess, vec<rtx>& clobbers,
  HARD_REG_SET& clobbered_regs, location_t loc),
 NULL)

/* This target hook allows the backend to specify a calling convention
   in the debug information.  This function actually returns an
   enum dwarf_calling_convention, but because of forward declarations
   and not wanting to include dwarf2.h everywhere target.h is included
   the function is being declared as an int.  */
DEFHOOK
(dwarf_calling_convention,
 "Define this to enable the dwarf attribute @code{DW_AT_calling_convention} to\n\
be emitted for each function.  Instead of an integer return the enum\n\
value for the @code{DW_CC_} tag.",
 int, (const_tree function),
 hook_int_const_tree_0)

/* This target hook allows the backend to emit frame-related insns that
   contain UNSPECs or UNSPEC_VOLATILEs.  The call frame debugging info
   engine will invoke it on insns of the form
     (set (reg) (unspec [...] UNSPEC_INDEX))
   and
     (set (reg) (unspec_volatile [...] UNSPECV_INDEX))
   to let the backend emit the call frame instructions.  */
DEFHOOK
(dwarf_handle_frame_unspec,
 "This target hook allows the backend to emit frame-related insns that\n\
contain UNSPECs or UNSPEC_VOLATILEs.  The DWARF 2 call frame debugging\n\
info engine will invoke it on insns of the form\n\
@smallexample\n\
(set (reg) (unspec [@dots{}] UNSPEC_INDEX))\n\
@end smallexample\n\
and\n\
@smallexample\n\
(set (reg) (unspec_volatile [@dots{}] UNSPECV_INDEX)).\n\
@end smallexample\n\
to let the backend emit the call frame instructions.  @var{label} is\n\
the CFI label attached to the insn, @var{pattern} is the pattern of\n\
the insn and @var{index} is @code{UNSPEC_INDEX} or @code{UNSPECV_INDEX}.",
 void, (const char *label, rtx pattern, int index), NULL)

DEFHOOK
(dwarf_poly_indeterminate_value,
 "Express the value of @code{poly_int} indeterminate @var{i} as a DWARF\n\
expression, with @var{i} counting from 1.  Return the number of a DWARF\n\
register @var{R} and set @samp{*@var{factor}} and @samp{*@var{offset}} such\n\
that the value of the indeterminate is:\n\
@smallexample\n\
value_of(@var{R}) / @var{factor} - @var{offset}\n\
@end smallexample\n\
\n\
A target only needs to define this hook if it sets\n\
@samp{NUM_POLY_INT_COEFFS} to a value greater than 1.",
 unsigned int, (unsigned int i, unsigned int *factor, int *offset),
 default_dwarf_poly_indeterminate_value)

/* ??? Documenting this hook requires a GFDL license grant.  */
DEFHOOK_UNDOC
(stdarg_optimize_hook,
"Perform architecture specific checking of statements gimplified\
 from @code{VA_ARG_EXPR}.  @var{stmt} is the statement.  Returns true if\
 the statement doesn't need to be checked for @code{va_list} references.",
 bool, (struct stdarg_info *ai, const gimple *stmt), NULL)

/* This target hook allows the operating system to override the DECL
   that represents the external variable that contains the stack
   protection guard variable.  The type of this DECL is ptr_type_node.  */
DEFHOOK
(stack_protect_guard,
 "This hook returns a @code{DECL} node for the external variable to use\n\
for the stack protection guard.  This variable is initialized by the\n\
runtime to some random value and is used to initialize the guard value\n\
that is placed at the top of the local stack frame.  The type of this\n\
variable must be @code{ptr_type_node}.\n\
\n\
The default version of this hook creates a variable called\n\
@samp{__stack_chk_guard}, which is normally defined in @file{libgcc2.c}.",
 tree, (void),
 default_stack_protect_guard)

/* This target hook allows the operating system to override the CALL_EXPR
   that is invoked when a check vs the guard variable fails.  */
DEFHOOK
(stack_protect_fail,
 "This hook returns a @code{CALL_EXPR} that alerts the runtime that the\n\
stack protect guard variable has been modified.  This expression should\n\
involve a call to a @code{noreturn} function.\n\
\n\
The default version of this hook invokes a function called\n\
@samp{__stack_chk_fail}, taking no arguments.  This function is\n\
normally defined in @file{libgcc2.c}.",
 tree, (void),
 default_external_stack_protect_fail)

/* This target hook allows the operating system to disable the default stack
   protector runtime support.  */
DEFHOOK
(stack_protect_runtime_enabled_p,
 "Returns true if the target wants GCC's default stack protect runtime support,\n\
otherwise return false.  The default implementation always returns true.",
 bool, (void),
 hook_bool_void_true)

DEFHOOK
(have_strub_support_for,
 "Returns true if the target supports stack scrubbing for the given function\n\
or type, otherwise return false.  The default implementation always returns\n\
true.",
 bool, (tree),
 hook_bool_tree_true)

DEFHOOK
(have_speculation_safe_value,
"This hook is used to determine the level of target support for\n\
 @code{__builtin_speculation_safe_value}.  If called with an argument\n\
 of false, it returns true if the target has been modified to support\n\
 this builtin.  If called with an argument of true, it returns true\n\
 if the target requires active mitigation execution might be speculative.\n\
 \n\
 The default implementation returns false if the target does not define\n\
 a pattern named @code{speculation_barrier}.  Else it returns true\n\
 for the first case and whether the pattern is enabled for the current\n\
 compilation for the second case.\n\
 \n\
 For targets that have no processors that can execute instructions\n\
 speculatively an alternative implemenation of this hook is available:\n\
 simply redefine this hook to @code{speculation_safe_value_not_needed}\n\
 along with your other target hooks.",
bool, (bool active), default_have_speculation_safe_value)

DEFHOOK
(speculation_safe_value,
"This target hook can be used to generate a target-specific code\n\
 sequence that implements the @code{__builtin_speculation_safe_value}\n\
 built-in function.  The function must always return @var{val} in\n\
 @var{result} in mode @var{mode} when the cpu is not executing\n\
 speculatively, but must never return that when speculating until it\n\
 is known that the speculation will not be unwound.  The hook supports\n\
 two primary mechanisms for implementing the requirements.  The first\n\
 is to emit a speculation barrier which forces the processor to wait\n\
 until all prior speculative operations have been resolved; the second\n\
 is to use a target-specific mechanism that can track the speculation\n\
 state and to return @var{failval} if it can determine that\n\
 speculation must be unwound at a later time.\n\
 \n\
 The default implementation simply copies @var{val} to @var{result} and\n\
 emits a @code{speculation_barrier} instruction if that is defined.",
rtx, (machine_mode mode, rtx result, rtx val, rtx failval),
 default_speculation_safe_value)

DEFHOOK
(predict_doloop_p,
 "Return true if we can predict it is possible to use a low-overhead loop\n\
for a particular loop.  The parameter @var{loop} is a pointer to the loop.\n\
This target hook is required only when the target supports low-overhead\n\
loops, and will help ivopts to make some decisions.\n\
The default version of this hook returns false.",
 bool, (class loop *loop),
 default_predict_doloop_p)

DEFHOOKPOD
(have_count_reg_decr_p,
 "Return true if the target supports hardware count register for decrement\n\
and branch.\n\
The default value is false.",
 bool, false)

DEFHOOKPOD
(doloop_cost_for_generic,
 "One IV candidate dedicated for doloop is introduced in IVOPTs, we can\n\
calculate the computation cost of adopting it to any generic IV use by\n\
function get_computation_cost as before.  But for targets which have\n\
hardware count register support for decrement and branch, it may have to\n\
move IV value from hardware count register to general purpose register\n\
while doloop IV candidate is used for generic IV uses.  It probably takes\n\
expensive penalty.  This hook allows target owners to define the cost for\n\
this especially for generic IV uses.\n\
The default value is zero.",
 int64_t, 0)

DEFHOOKPOD
(doloop_cost_for_address,
 "One IV candidate dedicated for doloop is introduced in IVOPTs, we can\n\
calculate the computation cost of adopting it to any address IV use by\n\
function get_computation_cost as before.  But for targets which have\n\
hardware count register support for decrement and branch, it may have to\n\
move IV value from hardware count register to general purpose register\n\
while doloop IV candidate is used for address IV uses.  It probably takes\n\
expensive penalty.  This hook allows target owners to define the cost for\n\
this escpecially for address IV uses.\n\
The default value is zero.",
 int64_t, 0)

DEFHOOK
(can_use_doloop_p,
 "Return true if it is possible to use low-overhead loops (@code{doloop_end}\n\
and @code{doloop_begin}) for a particular loop.  @var{iterations} gives the\n\
exact number of iterations, or 0 if not known.  @var{iterations_max} gives\n\
the maximum number of iterations, or 0 if not known.  @var{loop_depth} is\n\
the nesting depth of the loop, with 1 for innermost loops, 2 for loops that\n\
contain innermost loops, and so on.  @var{entered_at_top} is true if the\n\
loop is only entered from the top.\n\
\n\
This hook is only used if @code{doloop_end} is available.  The default\n\
implementation returns true.  You can use @code{can_use_doloop_if_innermost}\n\
if the loop must be the innermost, and if there are no other restrictions.",
 bool, (const widest_int &iterations, const widest_int &iterations_max,
	unsigned int loop_depth, bool entered_at_top),
 hook_bool_wint_wint_uint_bool_true)

/* Returns NULL if target supports the insn within a doloop block,
   otherwise it returns an error message.  */
DEFHOOK
(invalid_within_doloop,
 "\n\
Take an instruction in @var{insn} and return NULL if it is valid within a\n\
low-overhead loop, otherwise return a string explaining why doloop\n\
could not be applied.\n\
\n\
Many targets use special registers for low-overhead looping. For any\n\
instruction that clobbers these this function should return a string indicating\n\
the reason why the doloop could not be applied.\n\
By default, the RTL loop optimizer does not use a present doloop pattern for\n\
loops containing function calls or branch on table instructions.",
 const char *, (const rtx_insn *insn),
 default_invalid_within_doloop)

/* Returns the machine mode which the target prefers for doloop IV.  */
DEFHOOK
(preferred_doloop_mode,
"This hook takes a @var{mode} for a doloop IV, where @code{mode} is the\n\
original mode for the operation.  If the target prefers an alternate\n\
@code{mode} for the operation, then this hook should return that mode;\n\
otherwise the original @code{mode} should be returned.  For example, on a\n\
64-bit target, @code{DImode} might be preferred over @code{SImode}.  Both the\n\
original and the returned modes should be @code{MODE_INT}.",
 machine_mode,
 (machine_mode mode),
 default_preferred_doloop_mode)

/* Returns true for a legitimate combined insn.  */
DEFHOOK
(legitimate_combined_insn,
"Take an instruction in @var{insn} and return @code{false} if the instruction\n\
is not appropriate as a combination of two or more instructions.  The\n\
default is to accept all instructions.",
 bool, (rtx_insn *insn),
 hook_bool_rtx_insn_true)

DEFHOOK
(valid_dllimport_attribute_p,
"@var{decl} is a variable or function with @code{__attribute__((dllimport))}\n\
specified.  Use this hook if the target needs to add extra validation\n\
checks to @code{handle_dll_attribute}.",
 bool, (const_tree decl),
 hook_bool_const_tree_true)

/* If non-zero, align constant anchors in CSE to a multiple of this
   value.  */
DEFHOOKPOD
(const_anchor,
 "On some architectures it can take multiple instructions to synthesize\n\
a constant.  If there is another constant already in a register that\n\
is close enough in value then it is preferable that the new constant\n\
is computed from this register using immediate addition or\n\
subtraction.  We accomplish this through CSE.  Besides the value of\n\
the constant we also add a lower and an upper constant anchor to the\n\
available expressions.  These are then queried when encountering new\n\
constants.  The anchors are computed by rounding the constant up and\n\
down to a multiple of the value of @code{TARGET_CONST_ANCHOR}.\n\
@code{TARGET_CONST_ANCHOR} should be the maximum positive value\n\
accepted by immediate-add plus one.  We currently assume that the\n\
value of @code{TARGET_CONST_ANCHOR} is a power of 2.  For example, on\n\
MIPS, where add-immediate takes a 16-bit signed value,\n\
@code{TARGET_CONST_ANCHOR} is set to @samp{0x8000}.  The default value\n\
is zero, which disables this optimization.",
 unsigned HOST_WIDE_INT, 0)

/* Defines, which target-dependent bits (upper 16) are used by port  */
DEFHOOK
(memmodel_check,
 "Validate target specific memory model mask bits. When NULL no target specific\n\
memory model bits are allowed.",
 unsigned HOST_WIDE_INT, (unsigned HOST_WIDE_INT val), NULL)

/* Defines an offset bitwise ored into shifted address to get corresponding
   Address Sanitizer shadow address, or -1 if Address Sanitizer is not
   supported by the target.  */
DEFHOOK
(asan_shadow_offset,
 "Return the offset bitwise ored into shifted address to get corresponding\n\
Address Sanitizer shadow memory address.  NULL if Address Sanitizer is not\n\
supported by the target.  May return 0 if Address Sanitizer is not supported\n\
or using dynamic shadow offset by a subtarget.",
 unsigned HOST_WIDE_INT, (void),
 NULL)

DEFHOOK
(asan_dynamic_shadow_offset_p,
 "Return true if asan should use dynamic shadow offset.",
 bool, (void),
 hook_bool_void_false)

/* Functions relating to calls - argument passing, returns, etc.  */
/* Members of struct call have no special macro prefix.  */
HOOK_VECTOR (TARGET_CALLS, calls)

DEFHOOK
(promote_function_mode,
 "Like @code{PROMOTE_MODE}, but it is applied to outgoing function arguments or\n\
function return values.  The target hook should return the new mode\n\
and possibly change @code{*@var{punsignedp}} if the promotion should\n\
change signedness.  This function is called only for scalar @emph{or\n\
pointer} types.\n\
\n\
@var{for_return} allows to distinguish the promotion of arguments and\n\
return values.  If it is @code{1}, a return value is being promoted and\n\
@code{TARGET_FUNCTION_VALUE} must perform the same promotions done here.\n\
If it is @code{2}, the returned mode should be that of the register in\n\
which an incoming parameter is copied, or the outgoing result is computed;\n\
then the hook should return the same mode as @code{promote_mode}, though\n\
the signedness may be different.\n\
\n\
@var{type} can be NULL when promoting function arguments of libcalls.\n\
\n\
The default is to not promote arguments and return values.  You can\n\
also define the hook to @code{default_promote_function_mode_always_promote}\n\
if you would like to apply the same rules given by @code{PROMOTE_MODE}.",
 machine_mode, (const_tree type, machine_mode mode, int *punsignedp,
		     const_tree funtype, int for_return),
 default_promote_function_mode)

DEFHOOK
(promote_prototypes,
 "This target hook returns @code{true} if an argument declared in a\n\
prototype as an integral type smaller than @code{int} should actually be\n\
passed as an @code{int}.  In addition to avoiding errors in certain\n\
cases of mismatch, it also makes for better code on certain machines.\n\
The default is to not promote prototypes.",
 bool, (const_tree fntype),
 hook_bool_const_tree_false)

DEFHOOK
(struct_value_rtx,
 "This target hook should return the location of the structure value\n\
address (normally a @code{mem} or @code{reg}), or 0 if the address is\n\
passed as an ``invisible'' first argument.  Note that @var{fndecl} may\n\
be @code{NULL}, for libcalls.  You do not need to define this target\n\
hook if the address is always passed as an ``invisible'' first\n\
argument.\n\
\n\
On some architectures the place where the structure value address\n\
is found by the called function is not the same place that the\n\
caller put it.  This can be due to register windows, or it could\n\
be because the function prologue moves it to a different place.\n\
@var{incoming} is @code{1} or @code{2} when the location is needed in\n\
the context of the called function, and @code{0} in the context of\n\
the caller.\n\
\n\
If @var{incoming} is nonzero and the address is to be found on the\n\
stack, return a @code{mem} which refers to the frame pointer. If\n\
@var{incoming} is @code{2}, the result is being used to fetch the\n\
structure value address at the beginning of a function.  If you need\n\
to emit adjusting code, you should do it at this point.",
 rtx, (tree fndecl, int incoming),
 hook_rtx_tree_int_null)

DEFHOOKPOD
(omit_struct_return_reg,
 "Normally, when a function returns a structure by memory, the address\n\
is passed as an invisible pointer argument, but the compiler also\n\
arranges to return the address from the function like it would a normal\n\
pointer return value.  Define this to true if that behavior is\n\
undesirable on your target.",
 bool, false)

DEFHOOK
(return_in_memory,
 "This target hook should return a nonzero value to say to return the\n\
function value in memory, just as large structures are always returned.\n\
Here @var{type} will be the data type of the value, and @var{fntype}\n\
will be the type of the function doing the returning, or @code{NULL} for\n\
libcalls.\n\
\n\
Note that values of mode @code{BLKmode} must be explicitly handled\n\
by this function.  Also, the option @option{-fpcc-struct-return}\n\
takes effect regardless of this macro.  On most systems, it is\n\
possible to leave the hook undefined; this causes a default\n\
definition to be used, whose value is the constant 1 for @code{BLKmode}\n\
values, and 0 otherwise.\n\
\n\
Do not use this hook to indicate that structures and unions should always\n\
be returned in memory.  You should instead use @code{DEFAULT_PCC_STRUCT_RETURN}\n\
to indicate this.",
 bool, (const_tree type, const_tree fntype),
 default_return_in_memory)

DEFHOOK
(return_in_msb,
 "This hook should return true if values of type @var{type} are returned\n\
at the most significant end of a register (in other words, if they are\n\
padded at the least significant end).  You can assume that @var{type}\n\
is returned in a register; the caller is required to check this.\n\
\n\
Note that the register provided by @code{TARGET_FUNCTION_VALUE} must\n\
be able to hold the complete return value.  For example, if a 1-, 2-\n\
or 3-byte structure is returned at the most significant end of a\n\
4-byte register, @code{TARGET_FUNCTION_VALUE} should provide an\n\
@code{SImode} rtx.",
 bool, (const_tree type),
 hook_bool_const_tree_false)

/* Return true if a parameter must be passed by reference.  TYPE may
   be null if this is a libcall.  CA may be null if this query is
   from __builtin_va_arg.  */
DEFHOOK
(pass_by_reference,
 "This target hook should return @code{true} if argument @var{arg} at the\n\
position indicated by @var{cum} should be passed by reference.  This\n\
predicate is queried after target independent reasons for being\n\
passed by reference, such as @code{TREE_ADDRESSABLE (@var{arg}.type)}.\n\
\n\
If the hook returns true, a copy of that argument is made in memory and a\n\
pointer to the argument is passed instead of the argument itself.\n\
The pointer is passed in whatever way is appropriate for passing a pointer\n\
to that type.",
 bool,
 (cumulative_args_t cum, const function_arg_info &arg),
 hook_bool_CUMULATIVE_ARGS_arg_info_false)

DEFHOOK
(expand_builtin_saveregs,
 "If defined, this hook produces the machine-specific code for a call to\n\
@code{__builtin_saveregs}.  This code will be moved to the very\n\
beginning of the function, before any parameter access are made.  The\n\
return value of this function should be an RTX that contains the value\n\
to use as the return of @code{__builtin_saveregs}.",
 rtx, (void),
 default_expand_builtin_saveregs)

/* Returns pretend_argument_size.  */
DEFHOOK
(setup_incoming_varargs,
 "This target hook offers an alternative to using\n\
@code{__builtin_saveregs} and defining the hook\n\
@code{TARGET_EXPAND_BUILTIN_SAVEREGS}.  Use it to store the anonymous\n\
register arguments into the stack so that all the arguments appear to\n\
have been passed consecutively on the stack.  Once this is done, you can\n\
use the standard implementation of varargs that works for machines that\n\
pass all their arguments on the stack.\n\
\n\
The argument @var{args_so_far} points to the @code{CUMULATIVE_ARGS} data\n\
structure, containing the values that are obtained after processing the\n\
named arguments.  The argument @var{arg} describes the last of these named\n\
arguments.  The argument @var{arg} should not be used if the function type\n\
satisfies @code{TYPE_NO_NAMED_ARGS_STDARG_P}, since in that case there are\n\
no named arguments and all arguments are accessed with @code{va_arg}.\n\
\n\
The target hook should do two things: first, push onto the stack all the\n\
argument registers @emph{not} used for the named arguments, and second,\n\
store the size of the data thus pushed into the @code{int}-valued\n\
variable pointed to by @var{pretend_args_size}.  The value that you\n\
store here will serve as additional offset for setting up the stack\n\
frame.\n\
\n\
Because you must generate code to push the anonymous arguments at\n\
compile time without knowing their data types,\n\
@code{TARGET_SETUP_INCOMING_VARARGS} is only useful on machines that\n\
have just a single category of argument register and use it uniformly\n\
for all data types.\n\
\n\
If the argument @var{second_time} is nonzero, it means that the\n\
arguments of the function are being analyzed for the second time.  This\n\
happens for an inline function, which is not actually compiled until the\n\
end of the source file.  The hook @code{TARGET_SETUP_INCOMING_VARARGS} should\n\
not generate any instructions in this case.",
 void, (cumulative_args_t args_so_far, const function_arg_info &arg,
	int *pretend_args_size, int second_time),
 default_setup_incoming_varargs)

DEFHOOK
(start_call_args,
 "This target hook is invoked while generating RTL for a function call,\n\
after the argument values have been computed, and after stack arguments\n\
have been initialized, but before register arguments have been moved into\n\
their ABI-defined hard register locations.  It precedes calls to the related\n\
hooks @code{TARGET_CALL_ARGS} and @code{TARGET_END_CALL_ARGS}.\n\
The significance of this position in the call expansion is that:\n\
\n\
@itemize @bullet\n\
@item\n\
No argument registers are live.\n\
@item\n\
Although a call sequence can in general involve subcalls (such as using\n\
@code{memcpy} to copy large arguments), no such subcall will occur between\n\
the call to this hook and the generation of the main call instruction.\n\
@end itemize\n\
\n\
The single argument @var{complete_args} is the state of the target\n\
function's cumulative argument information after the final call to\n\
@code{TARGET_FUNCTION_ARG}.\n\
\n\
The hook can be used for things like switching processor mode, in cases\n\
where different calls need different processor modes.  Most ports do not\n\
need to implement anything for this hook.",
 void, (cumulative_args_t complete_args),
 hook_void_CUMULATIVE_ARGS)

DEFHOOK
(call_args,
 "While generating RTL for a function call, this target hook is invoked once\n\
for each argument passed to the function, either a register returned by\n\
@code{TARGET_FUNCTION_ARG} or a memory location.  It is called just\n\
before the point where argument registers are stored.\n\
\n\
@var{complete_args} is the state of the target function's cumulative\n\
argument information after the final call to @code{TARGET_FUNCTION_ARG}.\n\
@var{loc} is the location of the argument.  @var{type} is the type of\n\
the function being called, or @code{NULL_TREE} for libcalls.\n\
\n\
For functions without arguments, the hook is called once with @code{pc_rtx}\n\
passed instead of an argument register.\n\
\n\
This functionality can be used to perform special setup of call argument\n\
registers, if a target needs it.  Most ports do not need to implement\n\
anything for this hook.",
 void, (cumulative_args_t complete_args, rtx loc, tree type),
 hook_void_CUMULATIVE_ARGS_rtx_tree)

DEFHOOK
(end_call_args,
 "This target hook is invoked while generating RTL for a function call,\n\
just after the point where the return reg is copied into a pseudo.  It\n\
signals that all the call argument and return registers for the just\n\
emitted call are now no longer in use.  @var{complete_args} is the\n\
state of the target function's cumulative argument information after\n\
the final call to @code{TARGET_FUNCTION_ARG}.\n\
\n\
Most ports do not need to implement anything for this hook.",
 void, (cumulative_args_t complete_args),
 hook_void_CUMULATIVE_ARGS)

DEFHOOK
(call_offset_return_label,
 "While generating call-site debug info for a CALL insn, or a SEQUENCE\n\
insn starting with a CALL, this target hook is invoked to compute the\n\
offset to be added to the debug label emitted after the call to obtain\n\
the return address that should be recorded as the return PC.",
 int, (rtx_insn *call_insn),
 hook_int_rtx_insn_0)

DEFHOOK
(push_argument,
 "This target hook returns @code{true} if push instructions will be\n\
used to pass outgoing arguments.  When the push instruction usage is\n\
optional, @var{npush} is nonzero to indicate the number of bytes to\n\
push.  Otherwise, @var{npush} is zero.  If the target machine does not\n\
have a push instruction or push instruction should be avoided,\n\
@code{false} should be returned.  That directs GCC to use an alternate\n\
strategy: to allocate the entire argument block and then store the\n\
arguments into it.  If this target hook may return @code{true},\n\
@code{PUSH_ROUNDING} must be defined.",
 bool, (unsigned int npush),
 default_push_argument)

DEFHOOK
(strict_argument_naming,
 "Define this hook to return @code{true} if the location where a function\n\
argument is passed depends on whether or not it is a named argument.\n\
\n\
This hook controls how the @var{named} argument to @code{TARGET_FUNCTION_ARG}\n\
is set for varargs and stdarg functions.  If this hook returns\n\
@code{true}, the @var{named} argument is always true for named\n\
arguments, and false for unnamed arguments.  If it returns @code{false},\n\
but @code{TARGET_PRETEND_OUTGOING_VARARGS_NAMED} returns @code{true},\n\
then all arguments are treated as named.  Otherwise, all named arguments\n\
except the last are treated as named.\n\
\n\
You need not define this hook if it always returns @code{false}.",
 bool, (cumulative_args_t ca),
 hook_bool_CUMULATIVE_ARGS_false)

/* Returns true if we should use
   targetm.calls.setup_incoming_varargs() and/or
   targetm.calls.strict_argument_naming().  */
DEFHOOK
(pretend_outgoing_varargs_named,
 "If you need to conditionally change ABIs so that one works with\n\
@code{TARGET_SETUP_INCOMING_VARARGS}, but the other works like neither\n\
@code{TARGET_SETUP_INCOMING_VARARGS} nor @code{TARGET_STRICT_ARGUMENT_NAMING} was\n\
defined, then define this hook to return @code{true} if\n\
@code{TARGET_SETUP_INCOMING_VARARGS} is used, @code{false} otherwise.\n\
Otherwise, you should not define this hook.",
 bool, (cumulative_args_t ca),
 default_pretend_outgoing_varargs_named)

/* Given a complex type T, return true if a parameter of type T
   should be passed as two scalars.  */
DEFHOOK
(split_complex_arg,
 "This hook should return true if parameter of type @var{type} are passed\n\
as two scalar parameters.  By default, GCC will attempt to pack complex\n\
arguments into the target's word size.  Some ABIs require complex arguments\n\
to be split and treated as their individual components.  For example, on\n\
AIX64, complex floats should be passed in a pair of floating point\n\
registers, even though a complex float would fit in one 64-bit floating\n\
point register.\n\
\n\
The default value of this hook is @code{NULL}, which is treated as always\n\
false.",
 bool, (const_tree type), NULL)

/* Return true if type T, mode MODE, may not be passed in registers,
   but must be passed on the stack.  */
/* ??? This predicate should be applied strictly after pass-by-reference.
   Need audit to verify that this is the case.  */
DEFHOOK
(must_pass_in_stack,
 "This target hook should return @code{true} if we should not pass @var{arg}\n\
solely in registers.  The file @file{expr.h} defines a\n\
definition that is usually appropriate, refer to @file{expr.h} for additional\n\
documentation.",
 bool, (const function_arg_info &arg),
 must_pass_in_stack_var_size_or_pad)

/* Return true if type TYPE, mode MODE, which is passed by reference,
   should have the object copy generated by the callee rather than
   the caller.  It is never called for TYPE requiring constructors.  */
DEFHOOK
(callee_copies,
 "The function argument described by the parameters to this hook is\n\
known to be passed by reference.  The hook should return true if the\n\
function argument should be copied by the callee instead of copied\n\
by the caller.\n\
\n\
For any argument for which the hook returns true, if it can be\n\
determined that the argument is not modified, then a copy need\n\
not be generated.\n\
\n\
The default version of this hook always returns false.",
 bool,
 (cumulative_args_t cum, const function_arg_info &arg),
 hook_bool_CUMULATIVE_ARGS_arg_info_false)

/* Return zero for arguments passed entirely on the stack or entirely
   in registers.  If passed in both, return the number of bytes passed
   in registers; the balance is therefore passed on the stack.  */
DEFHOOK
(arg_partial_bytes,
 "This target hook returns the number of bytes at the beginning of an\n\
argument that must be put in registers.  The value must be zero for\n\
arguments that are passed entirely in registers or that are entirely\n\
pushed on the stack.\n\
\n\
On some machines, certain arguments must be passed partially in\n\
registers and partially in memory.  On these machines, typically the\n\
first few words of arguments are passed in registers, and the rest\n\
on the stack.  If a multi-word argument (a @code{double} or a\n\
structure) crosses that boundary, its first few words must be passed\n\
in registers and the rest must be pushed.  This macro tells the\n\
compiler when this occurs, and how many bytes should go in registers.\n\
\n\
@code{TARGET_FUNCTION_ARG} for these arguments should return the first\n\
register to be used by the caller for this argument; likewise\n\
@code{TARGET_FUNCTION_INCOMING_ARG}, for the called function.",
 int, (cumulative_args_t cum, const function_arg_info &arg),
 hook_int_CUMULATIVE_ARGS_arg_info_0)

/* Update the state in CA to advance past an argument in the
   argument list.  The values MODE, TYPE, and NAMED describe that
   argument.  */
DEFHOOK
(function_arg_advance,
 "This hook updates the summarizer variable pointed to by @var{ca} to\n\
advance past argument @var{arg} in the argument list.  Once this is done,\n\
the variable @var{cum} is suitable for analyzing the @emph{following}\n\
argument with @code{TARGET_FUNCTION_ARG}, etc.\n\
\n\
This hook need not do anything if the argument in question was passed\n\
on the stack.  The compiler knows how to track the amount of stack space\n\
used for arguments without any special help.",
 void,
 (cumulative_args_t ca, const function_arg_info &arg),
 default_function_arg_advance)

DEFHOOK
(function_arg_offset,
 "This hook returns the number of bytes to add to the offset of an\n\
argument of type @var{type} and mode @var{mode} when passed in memory.\n\
This is needed for the SPU, which passes @code{char} and @code{short}\n\
arguments in the preferred slot that is in the middle of the quad word\n\
instead of starting at the top.  The default implementation returns 0.",
 HOST_WIDE_INT, (machine_mode mode, const_tree type),
 default_function_arg_offset)

DEFHOOK
(function_arg_padding,
 "This hook determines whether, and in which direction, to pad out\n\
an argument of mode @var{mode} and type @var{type}.  It returns\n\
@code{PAD_UPWARD} to insert padding above the argument, @code{PAD_DOWNWARD}\n\
to insert padding below the argument, or @code{PAD_NONE} to inhibit padding.\n\
\n\
The @emph{amount} of padding is not controlled by this hook, but by\n\
@code{TARGET_FUNCTION_ARG_ROUND_BOUNDARY}.  It is always just enough\n\
to reach the next multiple of that boundary.\n\
\n\
This hook has a default definition that is right for most systems.\n\
For little-endian machines, the default is to pad upward.  For\n\
big-endian machines, the default is to pad downward for an argument of\n\
constant size shorter than an @code{int}, and upward otherwise.",
 pad_direction, (machine_mode mode, const_tree type),
 default_function_arg_padding)

/* Return zero if the argument described by the state of CA should
   be placed on a stack, or a hard register in which to store the
   argument.  The values MODE, TYPE, and NAMED describe that
   argument.  */
DEFHOOK
(function_arg,
 "Return an RTX indicating whether function argument @var{arg} is passed\n\
in a register and if so, which register.  Argument @var{ca} summarizes all\n\
the previous arguments.\n\
\n\
The return value is usually either a @code{reg} RTX for the hard\n\
register in which to pass the argument, or zero to pass the argument\n\
on the stack.\n\
\n\
The value of the expression can also be a @code{parallel} RTX@.  This is\n\
used when an argument is passed in multiple locations.  The mode of the\n\
@code{parallel} should be the mode of the entire argument.  The\n\
@code{parallel} holds any number of @code{expr_list} pairs; each one\n\
describes where part of the argument is passed.  In each\n\
@code{expr_list} the first operand must be a @code{reg} RTX for the hard\n\
register in which to pass this part of the argument, and the mode of the\n\
register RTX indicates how large this part of the argument is.  The\n\
second operand of the @code{expr_list} is a @code{const_int} which gives\n\
the offset in bytes into the entire argument of where this part starts.\n\
As a special exception the first @code{expr_list} in the @code{parallel}\n\
RTX may have a first operand of zero.  This indicates that the entire\n\
argument is also stored on the stack.\n\
\n\
The last time this hook is called, it is called with @code{MODE ==\n\
VOIDmode}, and its result is passed to the @code{call} or @code{call_value}\n\
pattern as operands 2 and 3 respectively.\n\
\n\
@cindex @file{stdarg.h} and register arguments\n\
The usual way to make the ISO library @file{stdarg.h} work on a\n\
machine where some arguments are usually passed in registers, is to\n\
cause nameless arguments to be passed on the stack instead.  This is\n\
done by making @code{TARGET_FUNCTION_ARG} return 0 whenever\n\
@var{named} is @code{false}.\n\
\n\
@cindex @code{TARGET_MUST_PASS_IN_STACK}, and @code{TARGET_FUNCTION_ARG}\n\
@cindex @code{REG_PARM_STACK_SPACE}, and @code{TARGET_FUNCTION_ARG}\n\
You may use the hook @code{targetm.calls.must_pass_in_stack}\n\
in the definition of this macro to determine if this argument is of a\n\
type that must be passed in the stack.  If @code{REG_PARM_STACK_SPACE}\n\
is not defined and @code{TARGET_FUNCTION_ARG} returns nonzero for such an\n\
argument, the compiler will abort.  If @code{REG_PARM_STACK_SPACE} is\n\
defined, the argument will be computed in the stack and then loaded into\n\
a register.",
 rtx, (cumulative_args_t ca, const function_arg_info &arg),
 default_function_arg)

DEFHOOK
(function_incoming_arg,
 "Define this hook if the caller and callee on the target have different\n\
views of where arguments are passed.  Also define this hook if there are\n\
functions that are never directly called, but are invoked by the hardware\n\
and which have nonstandard calling conventions.\n\
\n\
In this case @code{TARGET_FUNCTION_ARG} computes the register in\n\
which the caller passes the value, and\n\
@code{TARGET_FUNCTION_INCOMING_ARG} should be defined in a similar\n\
fashion to tell the function being called where the arguments will\n\
arrive.\n\
\n\
@code{TARGET_FUNCTION_INCOMING_ARG} can also return arbitrary address\n\
computation using hard register, which can be forced into a register,\n\
so that it can be used to pass special arguments.\n\
\n\
If @code{TARGET_FUNCTION_INCOMING_ARG} is not defined,\n\
@code{TARGET_FUNCTION_ARG} serves both purposes.",
 rtx, (cumulative_args_t ca, const function_arg_info &arg),
 default_function_incoming_arg)

DEFHOOK
(function_arg_boundary,
 "This hook returns the alignment boundary, in bits, of an argument\n\
with the specified mode and type.  The default hook returns\n\
@code{PARM_BOUNDARY} for all arguments.",
 unsigned int, (machine_mode mode, const_tree type),
 default_function_arg_boundary)

DEFHOOK
(function_arg_round_boundary,
 "Normally, the size of an argument is rounded up to @code{PARM_BOUNDARY},\n\
which is the default value for this hook.  You can define this hook to\n\
return a different value if an argument size must be rounded to a larger\n\
value.",
 unsigned int, (machine_mode mode, const_tree type),
 default_function_arg_round_boundary)

/* Return the diagnostic message string if function without a prototype
   is not allowed for this 'val' argument; NULL otherwise. */
DEFHOOK
(invalid_arg_for_unprototyped_fn,
 "If defined, this macro returns the diagnostic message when it is\n\
illegal to pass argument @var{val} to function @var{funcdecl}\n\
with prototype @var{typelist}.",
 const char *, (const_tree typelist, const_tree funcdecl, const_tree val),
 hook_invalid_arg_for_unprototyped_fn)

/* Return an rtx for the return value location of the function
   specified by FN_DECL_OR_TYPE with a return type of RET_TYPE.  */
DEFHOOK
(function_value,
 "\n\
Define this to return an RTX representing the place where a function\n\
returns or receives a value of data type @var{ret_type}, a tree node\n\
representing a data type.  @var{fn_decl_or_type} is a tree node\n\
representing @code{FUNCTION_DECL} or @code{FUNCTION_TYPE} of a\n\
function being called.  If @var{outgoing} is false, the hook should\n\
compute the register in which the caller will see the return value.\n\
Otherwise, the hook should return an RTX representing the place where\n\
a function returns a value.\n\
\n\
On many machines, only @code{TYPE_MODE (@var{ret_type})} is relevant.\n\
(Actually, on most machines, scalar values are returned in the same\n\
place regardless of mode.)  The value of the expression is usually a\n\
@code{reg} RTX for the hard register where the return value is stored.\n\
The value can also be a @code{parallel} RTX, if the return value is in\n\
multiple places.  See @code{TARGET_FUNCTION_ARG} for an explanation of the\n\
@code{parallel} form.   Note that the callee will populate every\n\
location specified in the @code{parallel}, but if the first element of\n\
the @code{parallel} contains the whole return value, callers will use\n\
that element as the canonical location and ignore the others.  The m68k\n\
port uses this type of @code{parallel} to return pointers in both\n\
@samp{%a0} (the canonical location) and @samp{%d0}.\n\
\n\
If @code{TARGET_PROMOTE_FUNCTION_RETURN} returns true, you must apply\n\
the same promotion rules specified in @code{PROMOTE_MODE} if\n\
@var{valtype} is a scalar type.\n\
\n\
If the precise function being called is known, @var{func} is a tree\n\
node (@code{FUNCTION_DECL}) for it; otherwise, @var{func} is a null\n\
pointer.  This makes it possible to use a different value-returning\n\
convention for specific functions when all their calls are\n\
known.\n\
\n\
Some target machines have ``register windows'' so that the register in\n\
which a function returns its value is not the same as the one in which\n\
the caller sees the value.  For such machines, you should return\n\
different RTX depending on @var{outgoing}.\n\
\n\
@code{TARGET_FUNCTION_VALUE} is not used for return values with\n\
aggregate data types, because these are returned in another way.  See\n\
@code{TARGET_STRUCT_VALUE_RTX} and related macros, below.",
 rtx, (const_tree ret_type, const_tree fn_decl_or_type, bool outgoing),
 default_function_value)

/* Return the rtx for the result of a libcall of mode MODE,
   calling the function FN_NAME.  */
DEFHOOK
(libcall_value,
 "Define this hook if the back-end needs to know the name of the libcall\n\
function in order to determine where the result should be returned.\n\
\n\
The mode of the result is given by @var{mode} and the name of the called\n\
library function is given by @var{fun}.  The hook should return an RTX\n\
representing the place where the library function result will be returned.\n\
\n\
If this hook is not defined, then LIBCALL_VALUE will be used.",
 rtx, (machine_mode mode, const_rtx fun),
 default_libcall_value)

/* Return true if REGNO is a possible register number for
   a function value as seen by the caller.  */
DEFHOOK
(function_value_regno_p,
 "A target hook that return @code{true} if @var{regno} is the number of a hard\n\
register in which the values of called function may come back.\n\
\n\
A register whose use for returning values is limited to serving as the\n\
second of a pair (for a value of type @code{double}, say) need not be\n\
recognized by this target hook.\n\
\n\
If the machine has register windows, so that the caller and the called\n\
function use different registers for the return value, this target hook\n\
should recognize only the caller's register numbers.\n\
\n\
If this hook is not defined, then FUNCTION_VALUE_REGNO_P will be used.",
 bool, (const unsigned int regno),
 default_function_value_regno_p)

DEFHOOK
(fntype_abi,
 "Return the ABI used by a function with type @var{type}; see the\n\
definition of @code{predefined_function_abi} for details of the ABI\n\
descriptor.  Targets only need to define this hook if they support\n\
interoperability between several ABIs in the same translation unit.",
 const predefined_function_abi &, (const_tree type),
 NULL)

DEFHOOK
(insn_callee_abi,
 "This hook returns a description of the ABI used by the target of\n\
call instruction @var{insn}; see the definition of\n\
@code{predefined_function_abi} for details of the ABI descriptor.\n\
Only the global function @code{insn_callee_abi} should call this hook\n\
directly.\n\
\n\
Targets only need to define this hook if they support\n\
interoperability between several ABIs in the same translation unit.",
 const predefined_function_abi &, (const rtx_insn *insn),
 NULL)

/* ??? Documenting this hook requires a GFDL license grant.  */
DEFHOOK_UNDOC
(internal_arg_pointer,
"Return an rtx for the argument pointer incoming to the\
 current function.",
 rtx, (void),
 default_internal_arg_pointer)

/* Update the current function stack boundary if needed.  */
DEFHOOK
(update_stack_boundary,
 "Define this macro to update the current function stack boundary if\n\
necessary.",
 void, (void), NULL)

/* Handle stack alignment and return an rtx for Dynamic Realign
   Argument Pointer if necessary.  */
DEFHOOK
(get_drap_rtx,
 "This hook should return an rtx for Dynamic Realign Argument Pointer (DRAP) if a\n\
different argument pointer register is needed to access the function's\n\
argument list due to stack realignment.  Return @code{NULL} if no DRAP\n\
is needed.",
 rtx, (void), NULL)

/* Generate instruction sequence to zero call used registers.  */
DEFHOOK
(zero_call_used_regs,
 "This target hook emits instructions to zero the subset of @var{selected_regs}\n\
that could conceivably contain values that are useful to an attacker.\n\
Return the set of registers that were actually cleared.\n\
\n\
For most targets, the returned set of registers is a subset of\n\
@var{selected_regs}, however, for some of the targets (for example MIPS),\n\
clearing some registers that are in the @var{selected_regs} requires\n\
clearing other call used registers that are not in the @var{selected_regs},\n\
under such situation, the returned set of registers must be a subset of all\n\
call used registers.\n\
\n\
The default implementation uses normal move instructions to zero\n\
all the registers in @var{selected_regs}.  Define this hook if the\n\
target has more efficient ways of zeroing certain registers,\n\
or if you believe that certain registers would never contain\n\
values that are useful to an attacker.",
 HARD_REG_SET, (HARD_REG_SET selected_regs),
default_zero_call_used_regs)

/* Return true if all function parameters should be spilled to the
   stack.  */
DEFHOOK
(allocate_stack_slots_for_args,
 "When optimization is disabled, this hook indicates whether or not\n\
arguments should be allocated to stack slots.  Normally, GCC allocates\n\
stacks slots for arguments when not optimizing in order to make\n\
debugging easier.  However, when a function is declared with\n\
@code{__attribute__((naked))}, there is no stack frame, and the compiler\n\
cannot safely move arguments from the registers in which they are passed\n\
to the stack.  Therefore, this hook should return true in general, but\n\
false for naked functions.  The default implementation always returns true.",
 bool, (void),
 hook_bool_void_true)

/* Return an rtx for the static chain for FNDECL_OR_TYPE.  If INCOMING_P
   is true, then it should be for the callee; otherwise for the caller.  */
DEFHOOK
(static_chain,
 "This hook replaces the use of @code{STATIC_CHAIN_REGNUM} et al for\n\
targets that may use different static chain locations for different\n\
nested functions.  This may be required if the target has function\n\
attributes that affect the calling conventions of the function and\n\
those calling conventions use different static chain locations.\n\
\n\
The default version of this hook uses @code{STATIC_CHAIN_REGNUM} et al.\n\
\n\
If the static chain is passed in memory, this hook should be used to\n\
provide rtx giving @code{mem} expressions that denote where they are stored.\n\
Often the @code{mem} expression as seen by the caller will be at an offset\n\
from the stack pointer and the @code{mem} expression as seen by the callee\n\
will be at an offset from the frame pointer.\n\
@findex stack_pointer_rtx\n\
@findex frame_pointer_rtx\n\
@findex arg_pointer_rtx\n\
The variables @code{stack_pointer_rtx}, @code{frame_pointer_rtx}, and\n\
@code{arg_pointer_rtx} will have been initialized and should be used\n\
to refer to those items.",
 rtx, (const_tree fndecl_or_type, bool incoming_p),
 default_static_chain)

/* Fill in the trampoline at MEM with a call to FNDECL and a
   static chain value of CHAIN.  */
DEFHOOK
(trampoline_init,
 "This hook is called to initialize a trampoline.\n\
@var{m_tramp} is an RTX for the memory block for the trampoline; @var{fndecl}\n\
is the @code{FUNCTION_DECL} for the nested function; @var{static_chain} is an\n\
RTX for the static chain value that should be passed to the function\n\
when it is called.\n\
\n\
If the target defines @code{TARGET_ASM_TRAMPOLINE_TEMPLATE}, then the\n\
first thing this hook should do is emit a block move into @var{m_tramp}\n\
from the memory block returned by @code{assemble_trampoline_template}.\n\
Note that the block move need only cover the constant parts of the\n\
trampoline.  If the target isolates the variable parts of the trampoline\n\
to the end, not all @code{TRAMPOLINE_SIZE} bytes need be copied.\n\
\n\
If the target requires any other actions, such as flushing caches\n\
(possibly calling function maybe_emit_call_builtin___clear_cache) or\n\
enabling stack execution, these actions should be performed after\n\
initializing the trampoline proper.",
 void, (rtx m_tramp, tree fndecl, rtx static_chain),
 default_trampoline_init)

/* Emit a call to a function to clear the instruction cache.  */
DEFHOOK
(emit_call_builtin___clear_cache,
 "On targets that do not define a @code{clear_cache} insn expander,\n\
but that define the @code{CLEAR_CACHE_INSN} macro,\n\
maybe_emit_call_builtin___clear_cache relies on this target hook\n\
to clear an address range in the instruction cache.\n\
\n\
The default implementation calls the @code{__clear_cache} builtin,\n\
taking the assembler name from the builtin declaration.  Overriding\n\
definitions may call alternate functions, with alternate calling\n\
conventions, or emit alternate RTX to perform the job.",
 void, (rtx begin, rtx end),
 default_emit_call_builtin___clear_cache)

/* Adjust the address of the trampoline in a target-specific way.  */
DEFHOOK
(trampoline_adjust_address,
 "This hook should perform any machine-specific adjustment in\n\
the address of the trampoline.  Its argument contains the address of the\n\
memory block that was passed to @code{TARGET_TRAMPOLINE_INIT}.  In case\n\
the address to be used for a function call should be different from the\n\
address at which the template was stored, the different address should\n\
be returned; otherwise @var{addr} should be returned unchanged.\n\
If this hook is not defined, @var{addr} will be used for function calls.",
 rtx, (rtx addr), NULL)

DEFHOOKPOD
(custom_function_descriptors,
 "If the target can use GCC's generic descriptor mechanism for nested\n\
functions, define this hook to a power of 2 representing an unused bit\n\
in function pointers which can be used to differentiate descriptors at\n\
run time.  This value gives the number of bytes by which descriptor\n\
pointers are misaligned compared to function pointers.  For example, on\n\
targets that require functions to be aligned to a 4-byte boundary, a\n\
value of either 1 or 2 is appropriate unless the architecture already\n\
reserves the bit for another purpose, such as on ARM.\n\
\n\
Define this hook to 0 if the target implements ABI support for\n\
function descriptors in its standard calling sequence, like for example\n\
HPPA or IA-64.\n\
\n\
Using descriptors for nested functions\n\
eliminates the need for trampolines that reside on the stack and require\n\
it to be made executable.",
 int, -1)

/* Return the number of bytes of its own arguments that a function
   pops on returning, or 0 if the function pops no arguments and the
   caller must therefore pop them all after the function returns.  */
/* ??? tm.texi has no types for the parameters.  */
DEFHOOK
(return_pops_args,
 "This target hook returns the number of bytes of its own arguments that\n\
a function pops on returning, or 0 if the function pops no arguments\n\
and the caller must therefore pop them all after the function returns.\n\
\n\
@var{fundecl} is a C variable whose value is a tree node that describes\n\
the function in question.  Normally it is a node of type\n\
@code{FUNCTION_DECL} that describes the declaration of the function.\n\
From this you can obtain the @code{DECL_ATTRIBUTES} of the function.\n\
\n\
@var{funtype} is a C variable whose value is a tree node that\n\
describes the function in question.  Normally it is a node of type\n\
@code{FUNCTION_TYPE} that describes the data type of the function.\n\
From this it is possible to obtain the data types of the value and\n\
arguments (if known).\n\
\n\
When a call to a library function is being considered, @var{fundecl}\n\
will contain an identifier node for the library function.  Thus, if\n\
you need to distinguish among various library functions, you can do so\n\
by their names.  Note that ``library function'' in this context means\n\
a function used to perform arithmetic, whose name is known specially\n\
in the compiler and was not mentioned in the C code being compiled.\n\
\n\
@var{size} is the number of bytes of arguments passed on the\n\
stack.  If a variable number of bytes is passed, it is zero, and\n\
argument popping will always be the responsibility of the calling function.\n\
\n\
On the VAX, all functions always pop their arguments, so the definition\n\
of this macro is @var{size}.  On the 68000, using the standard\n\
calling convention, no functions pop their arguments, so the value of\n\
the macro is always 0 in this case.  But an alternative calling\n\
convention is available in which functions that take a fixed number of\n\
arguments pop them but other functions (such as @code{printf}) pop\n\
nothing (the caller pops all).  When this convention is in use,\n\
@var{funtype} is examined to determine whether a function takes a fixed\n\
number of arguments.",
 poly_int64, (tree fundecl, tree funtype, poly_int64 size),
 default_return_pops_args)

/* Return a mode wide enough to copy any function value that might be
   returned.  */
DEFHOOK
(get_raw_result_mode,
 "This target hook returns the mode to be used when accessing raw return\n\
registers in @code{__builtin_return}.  Define this macro if the value\n\
in @var{reg_raw_mode} is not correct.  Use @code{VOIDmode} if a register\n\
should be ignored for @code{__builtin_return} purposes.",
 fixed_size_mode, (int regno),
 default_get_reg_raw_mode)

/* Return a mode wide enough to copy any argument value that might be
   passed.  */
DEFHOOK
(get_raw_arg_mode,
 "This target hook returns the mode to be used when accessing raw argument\n\
registers in @code{__builtin_apply_args}.  Define this macro if the value\n\
in @var{reg_raw_mode} is not correct.  Use @code{VOIDmode} if a register\n\
should be ignored for @code{__builtin_apply_args} purposes.",
 fixed_size_mode, (int regno),
 default_get_reg_raw_mode)

/* Return true if a type is an empty record.  */
DEFHOOK
(empty_record_p,
 "This target hook returns true if the type is an empty record.  The default\n\
is to return @code{false}.",
 bool, (const_tree type),
 hook_bool_const_tree_false)

/* Warn about the change in empty class parameter passing ABI.  */
DEFHOOK
(warn_parameter_passing_abi,
 "This target hook warns about the change in empty class parameter passing\n\
ABI.",
 void, (cumulative_args_t ca, tree type),
 hook_void_CUMULATIVE_ARGS_tree)

HOOK_VECTOR_END (calls)

DEFHOOK
(use_pseudo_pic_reg,
 "This hook should return 1 in case pseudo register should be created\n\
for pic_offset_table_rtx during function expand.",
 bool, (void),
 hook_bool_void_false)

DEFHOOK
(init_pic_reg,
 "Perform a target dependent initialization of pic_offset_table_rtx.\n\
This hook is called at the start of register allocation.",
 void, (void),
 hook_void_void)

/* Return the diagnostic message string if conversion from FROMTYPE
   to TOTYPE is not allowed, NULL otherwise.  */
DEFHOOK
(invalid_conversion,
 "If defined, this macro returns the diagnostic message when it is\n\
invalid to convert from @var{fromtype} to @var{totype}, or @code{NULL}\n\
if validity should be determined by the front end.",
 const char *, (const_tree fromtype, const_tree totype),
 hook_constcharptr_const_tree_const_tree_null)

/* Return the diagnostic message string if the unary operation OP is
   not permitted on TYPE, NULL otherwise.  */
DEFHOOK
(invalid_unary_op,
 "If defined, this macro returns the diagnostic message when it is\n\
invalid to apply operation @var{op} (where unary plus is denoted by\n\
@code{CONVERT_EXPR}) to an operand of type @var{type}, or @code{NULL}\n\
if validity should be determined by the front end.",
 const char *, (int op, const_tree type),
 hook_constcharptr_int_const_tree_null)

/* Return the diagnostic message string if the binary operation OP
   is not permitted on TYPE1 and TYPE2, NULL otherwise.  */
DEFHOOK
(invalid_binary_op,
 "If defined, this macro returns the diagnostic message when it is\n\
invalid to apply operation @var{op} to operands of types @var{type1}\n\
and @var{type2}, or @code{NULL} if validity should be determined by\n\
the front end.",
 const char *, (int op, const_tree type1, const_tree type2),
 hook_constcharptr_int_const_tree_const_tree_null)

/* If values of TYPE are promoted to some other type when used in
   expressions (analogous to the integer promotions), return that type,
   or NULL_TREE otherwise.  */
DEFHOOK
(promoted_type,
 "If defined, this target hook returns the type to which values of\n\
@var{type} should be promoted when they appear in expressions,\n\
analogous to the integer promotions, or @code{NULL_TREE} to use the\n\
front end's normal promotion rules.  This hook is useful when there are\n\
target-specific types with special promotion rules.\n\
This is currently used only by the C and C++ front ends.",
 tree, (const_tree type),
 hook_tree_const_tree_null)

/* Convert EXPR to TYPE, if target-specific types with special conversion
   rules are involved.  Return the converted expression, or NULL to apply
   the standard conversion rules.  */
DEFHOOK
(convert_to_type,
 "If defined, this hook returns the result of converting @var{expr} to\n\
@var{type}.  It should return the converted expression,\n\
or @code{NULL_TREE} to apply the front end's normal conversion rules.\n\
This hook is useful when there are target-specific types with special\n\
conversion rules.\n\
This is currently used only by the C and C++ front ends.",
 tree, (tree type, tree expr),
 hook_tree_tree_tree_null)

DEFHOOK
(verify_type_context,
 "If defined, this hook returns false if there is a target-specific reason\n\
why type @var{type} cannot be used in the source language context described\n\
by @var{context}.  When @var{silent_p} is false, the hook also reports an\n\
error against @var{loc} for invalid uses of @var{type}.\n\
\n\
Calls to this hook should be made through the global function\n\
@code{verify_type_context}, which makes the @var{silent_p} parameter\n\
default to false and also handles @code{error_mark_node}.\n\
\n\
The default implementation always returns true.",
 bool, (location_t loc, type_context_kind context, const_tree type,
	bool silent_p),
 NULL)

DEFHOOK
(can_change_mode_class,
 "This hook returns true if it is possible to bitcast values held in\n\
registers of class @var{rclass} from mode @var{from} to mode @var{to}\n\
and if doing so preserves the low-order bits that are common to both modes.\n\
The result is only meaningful if @var{rclass} has registers that can hold\n\
both @code{from} and @code{to}.  The default implementation returns true.\n\
\n\
As an example of when such bitcasting is invalid, loading 32-bit integer or\n\
floating-point objects into floating-point registers on Alpha extends them\n\
to 64 bits.  Therefore loading a 64-bit object and then storing it as a\n\
32-bit object does not store the low-order 32 bits, as would be the case\n\
for a normal register.  Therefore, @file{alpha.h} defines\n\
@code{TARGET_CAN_CHANGE_MODE_CLASS} to return:\n\
\n\
@smallexample\n\
(GET_MODE_SIZE (from) == GET_MODE_SIZE (to)\n\
 || !reg_classes_intersect_p (FLOAT_REGS, rclass))\n\
@end smallexample\n\
\n\
Even if storing from a register in mode @var{to} would be valid,\n\
if both @var{from} and @code{raw_reg_mode} for @var{rclass} are wider\n\
than @code{word_mode}, then we must prevent @var{to} narrowing the\n\
mode.  This happens when the middle-end assumes that it can load\n\
or store pieces of an @var{N}-word pseudo, and that the pseudo will\n\
eventually be allocated to @var{N} @code{word_mode} hard registers.\n\
Failure to prevent this kind of mode change will result in the\n\
entire @code{raw_reg_mode} being modified instead of the partial\n\
value that the middle-end intended.",
 bool, (machine_mode from, machine_mode to, reg_class_t rclass),
 hook_bool_mode_mode_reg_class_t_true)

/* Change pseudo allocno class calculated by IRA.  */
DEFHOOK
(ira_change_pseudo_allocno_class,
 "A target hook which can change allocno class for given pseudo from\n\
  allocno and best class calculated by IRA.\n\
  \n\
  The default version of this target hook always returns given class.",
 reg_class_t, (int, reg_class_t, reg_class_t),
 default_ira_change_pseudo_allocno_class)

/* Return true if we use LRA instead of reload.  */
DEFHOOK
(lra_p,
 "A target hook which returns true if we use LRA instead of reload pass.\n\
\n\
The default version of this target hook returns true.  New ports\n\
should use LRA, and existing ports are encouraged to convert.",
 bool, (void),
 default_lra_p)

/* Return register priority of given hard regno for the current target.  */
DEFHOOK
(register_priority,
 "A target hook which returns the register priority number to which the\n\
register @var{hard_regno} belongs to.  The bigger the number, the\n\
more preferable the hard register usage (when all other conditions are\n\
the same).  This hook can be used to prefer some hard register over\n\
others in LRA.  For example, some x86-64 register usage needs\n\
additional prefix which makes instructions longer.  The hook can\n\
return lower priority number for such registers make them less favorable\n\
and as result making the generated code smaller.\n\
\n\
The default version of this target hook returns always zero.",
 int, (int),
 default_register_priority)

/* Return true if we need register usage leveling.  */
DEFHOOK
(register_usage_leveling_p,
 "A target hook which returns true if we need register usage leveling.\n\
That means if a few hard registers are equally good for the\n\
assignment, we choose the least used hard register.  The register\n\
usage leveling may be profitable for some targets.  Don't use the\n\
usage leveling for targets with conditional execution or targets\n\
with big register files as it hurts if-conversion and cross-jumping\n\
optimizations.\n\
\n\
The default version of this target hook returns always false.",
 bool, (void),
 default_register_usage_leveling_p)

/* Return true if maximal address displacement can be different.  */
DEFHOOK
(different_addr_displacement_p,
 "A target hook which returns true if an address with the same structure\n\
can have different maximal legitimate displacement.  For example, the\n\
displacement can depend on memory mode or on operand combinations in\n\
the insn.\n\
\n\
The default version of this target hook returns always false.",
 bool, (void),
 default_different_addr_displacement_p)

/* Determine class for spilling pseudos of given mode into registers
   instead of memory.  */
DEFHOOK
(spill_class,
 "This hook defines a class of registers which could be used for spilling\n\
pseudos of the given mode and class, or @code{NO_REGS} if only memory\n\
should be used.  Not defining this hook is equivalent to returning\n\
@code{NO_REGS} for all inputs.",
 reg_class_t, (reg_class_t, machine_mode),
 NULL)

/* Determine an additional allocno class.  */
DEFHOOK
(additional_allocno_class_p,
 "This hook should return @code{true} if given class of registers should\n\
be an allocno class in any way.  Usually RA uses only one register\n\
class from all classes containing the same register set.  In some\n\
complicated cases, you need to have two or more such classes as\n\
allocno ones for RA correct work.  Not defining this hook is\n\
equivalent to returning @code{false} for all inputs.",
 bool, (reg_class_t),
 hook_bool_reg_class_t_false)

DEFHOOK
(cstore_mode,
 "This hook defines the machine mode to use for the boolean result of\n\
conditional store patterns.  The ICODE argument is the instruction code\n\
for the cstore being performed.  Not definiting this hook is the same\n\
as accepting the mode encoded into operand 0 of the cstore expander\n\
patterns.",
  scalar_int_mode, (enum insn_code icode),
  default_cstore_mode)

/* This target hook allows the backend to compute the register pressure
   classes to use.  */
DEFHOOK
(compute_pressure_classes,
 "A target hook which lets a backend compute the set of pressure classes to\n\
be used by those optimization passes which take register pressure into\n\
account, as opposed to letting IRA compute them.  It returns the number of\n\
register classes stored in the array @var{pressure_classes}.",
 int, (enum reg_class *pressure_classes), NULL)

/* True if a structure, union or array with MODE containing FIELD should
   be accessed using BLKmode.  */
DEFHOOK
(member_type_forces_blk,
 "Return true if a structure, union or array containing @var{field} should\n\
be accessed using @code{BLKMODE}.\n\
\n\
If @var{field} is the only field in the structure, @var{mode} is its\n\
mode, otherwise @var{mode} is VOIDmode.  @var{mode} is provided in the\n\
case where structures of one field would require the structure's mode to\n\
retain the field's mode.\n\
\n\
Normally, this is not needed.",
 bool, (const_tree field, machine_mode mode),
 default_member_type_forces_blk)

/* See tree-ssa-math-opts.cc:divmod_candidate_p for conditions
   that gate the divod transform.  */
DEFHOOK
(expand_divmod_libfunc,
 "Define this hook for enabling divmod transform if the port does not have\n\
hardware divmod insn but defines target-specific divmod libfuncs.",
 void, (rtx libfunc, machine_mode mode, rtx op0, rtx op1, rtx *quot, rtx *rem),
 NULL)

/* Return the class for a secondary reload, and fill in extra information.  */
DEFHOOK
(secondary_reload,
 "Many machines have some registers that cannot be copied directly to or\n\
from memory or even from other types of registers.  An example is the\n\
@samp{MQ} register, which on most machines, can only be copied to or\n\
from general registers, but not memory.  Below, we shall be using the\n\
term 'intermediate register' when a move operation cannot be performed\n\
directly, but has to be done by copying the source into the intermediate\n\
register first, and then copying the intermediate register to the\n\
destination.  An intermediate register always has the same mode as\n\
source and destination.  Since it holds the actual value being copied,\n\
reload might apply optimizations to re-use an intermediate register\n\
and eliding the copy from the source when it can determine that the\n\
intermediate register still holds the required value.\n\
\n\
Another kind of secondary reload is required on some machines which\n\
allow copying all registers to and from memory, but require a scratch\n\
register for stores to some memory locations (e.g., those with symbolic\n\
address on the RT, and those with certain symbolic address on the SPARC\n\
when compiling PIC)@.  Scratch registers need not have the same mode\n\
as the value being copied, and usually hold a different value than\n\
that being copied.  Special patterns in the md file are needed to\n\
describe how the copy is performed with the help of the scratch register;\n\
these patterns also describe the number, register class(es) and mode(s)\n\
of the scratch register(s).\n\
\n\
In some cases, both an intermediate and a scratch register are required.\n\
\n\
For input reloads, this target hook is called with nonzero @var{in_p},\n\
and @var{x} is an rtx that needs to be copied to a register of class\n\
@var{reload_class} in @var{reload_mode}.  For output reloads, this target\n\
hook is called with zero @var{in_p}, and a register of class @var{reload_class}\n\
needs to be copied to rtx @var{x} in @var{reload_mode}.\n\
\n\
If copying a register of @var{reload_class} from/to @var{x} requires\n\
an intermediate register, the hook @code{secondary_reload} should\n\
return the register class required for this intermediate register.\n\
If no intermediate register is required, it should return NO_REGS.\n\
If more than one intermediate register is required, describe the one\n\
that is closest in the copy chain to the reload register.\n\
\n\
If scratch registers are needed, you also have to describe how to\n\
perform the copy from/to the reload register to/from this\n\
closest intermediate register.  Or if no intermediate register is\n\
required, but still a scratch register is needed, describe the\n\
copy  from/to the reload register to/from the reload operand @var{x}.\n\
\n\
You do this by setting @code{sri->icode} to the instruction code of a pattern\n\
in the md file which performs the move.  Operands 0 and 1 are the output\n\
and input of this copy, respectively.  Operands from operand 2 onward are\n\
for scratch operands.  These scratch operands must have a mode, and a\n\
single-register-class\n\
@c [later: or memory]\n\
output constraint.\n\
\n\
When an intermediate register is used, the @code{secondary_reload}\n\
hook will be called again to determine how to copy the intermediate\n\
register to/from the reload operand @var{x}, so your hook must also\n\
have code to handle the register class of the intermediate operand.\n\
\n\
@c [For later: maybe we'll allow multi-alternative reload patterns -\n\
@c   the port maintainer could name a mov<mode> pattern that has clobbers -\n\
@c   and match the constraints of input and output to determine the required\n\
@c   alternative.  A restriction would be that constraints used to match\n\
@c   against reloads registers would have to be written as register class\n\
@c   constraints, or we need a new target macro / hook that tells us if an\n\
@c   arbitrary constraint can match an unknown register of a given class.\n\
@c   Such a macro / hook would also be useful in other places.]\n\
\n\
\n\
@var{x} might be a pseudo-register or a @code{subreg} of a\n\
pseudo-register, which could either be in a hard register or in memory.\n\
Use @code{true_regnum} to find out; it will return @minus{}1 if the pseudo is\n\
in memory and the hard register number if it is in a register.\n\
\n\
Scratch operands in memory (constraint @code{\"=m\"} / @code{\"=&m\"}) are\n\
currently not supported.  For the time being, you will have to continue\n\
to use @code{TARGET_SECONDARY_MEMORY_NEEDED} for that purpose.\n\
\n\
@code{copy_cost} also uses this target hook to find out how values are\n\
copied.  If you want it to include some extra cost for the need to allocate\n\
(a) scratch register(s), set @code{sri->extra_cost} to the additional cost.\n\
Or if two dependent moves are supposed to have a lower cost than the sum\n\
of the individual moves due to expected fortuitous scheduling and/or special\n\
forwarding logic, you can set @code{sri->extra_cost} to a negative amount.",
 reg_class_t,
 (bool in_p, rtx x, reg_class_t reload_class, machine_mode reload_mode,
  secondary_reload_info *sri),
 default_secondary_reload)

DEFHOOK
(secondary_memory_needed,
 "Certain machines have the property that some registers cannot be copied\n\
to some other registers without using memory.  Define this hook on\n\
those machines to return true if objects of mode @var{m} in registers\n\
of @var{class1} can only be copied to registers of class @var{class2} by\n\
 storing a register of @var{class1} into memory and loading that memory\n\
location into a register of @var{class2}.  The default definition returns\n\
false for all inputs.",
 bool, (machine_mode mode, reg_class_t class1, reg_class_t class2),
 hook_bool_mode_reg_class_t_reg_class_t_false)

DEFHOOK
(secondary_memory_needed_mode,
 "If @code{TARGET_SECONDARY_MEMORY_NEEDED} tells the compiler to use memory\n\
when moving between two particular registers of mode @var{mode},\n\
this hook specifies the mode that the memory should have.\n\
\n\
The default depends on @code{TARGET_LRA_P}.  Without LRA, the default\n\
is to use a word-sized mode for integral modes that are smaller than a\n\
a word.  This is right thing to do on most machines because it ensures\n\
that all bits of the register are copied and prevents accesses to the\n\
registers in a narrower mode, which some machines prohibit for\n\
floating-point registers.\n\
\n\
However, this default behavior is not correct on some machines, such as\n\
the DEC Alpha, that store short integers in floating-point registers\n\
differently than in integer registers.  On those machines, the default\n\
widening will not work correctly and you must define this hook to\n\
suppress that widening in some cases.  See the file @file{alpha.cc} for\n\
details.\n\
\n\
With LRA, the default is to use @var{mode} unmodified.",
 machine_mode, (machine_mode mode),
 default_secondary_memory_needed_mode)

/* Given an rtx X being reloaded into a reg required to be in class CLASS,
   return the class of reg to actually use.  */
DEFHOOK
(preferred_reload_class,
 "A target hook that places additional restrictions on the register class\n\
to use when it is necessary to copy value @var{x} into a register in class\n\
@var{rclass}.  The value is a register class; perhaps @var{rclass}, or perhaps\n\
another, smaller class.\n\
\n\
The default version of this hook always returns value of @code{rclass} argument.\n\
\n\
Sometimes returning a more restrictive class makes better code.  For\n\
example, on the 68000, when @var{x} is an integer constant that is in range\n\
for a @samp{moveq} instruction, the value of this macro is always\n\
@code{DATA_REGS} as long as @var{rclass} includes the data registers.\n\
Requiring a data register guarantees that a @samp{moveq} will be used.\n\
\n\
One case where @code{TARGET_PREFERRED_RELOAD_CLASS} must not return\n\
@var{rclass} is if @var{x} is a legitimate constant which cannot be\n\
loaded into some register class.  By returning @code{NO_REGS} you can\n\
force @var{x} into a memory location.  For example, rs6000 can load\n\
immediate values into general-purpose registers, but does not have an\n\
instruction for loading an immediate value into a floating-point\n\
register, so @code{TARGET_PREFERRED_RELOAD_CLASS} returns @code{NO_REGS} when\n\
@var{x} is a floating-point constant.  If the constant can't be loaded\n\
into any kind of register, code generation will be better if\n\
@code{TARGET_LEGITIMATE_CONSTANT_P} makes the constant illegitimate instead\n\
of using @code{TARGET_PREFERRED_RELOAD_CLASS}.\n\
\n\
If an insn has pseudos in it after register allocation, reload will go\n\
through the alternatives and call repeatedly @code{TARGET_PREFERRED_RELOAD_CLASS}\n\
to find the best one.  Returning @code{NO_REGS}, in this case, makes\n\
reload add a @code{!} in front of the constraint: the x86 back-end uses\n\
this feature to discourage usage of 387 registers when math is done in\n\
the SSE registers (and vice versa).",
 reg_class_t,
 (rtx x, reg_class_t rclass),
 default_preferred_reload_class)

/* Like TARGET_PREFERRED_RELOAD_CLASS, but for output reloads instead of
   input reloads.  */
DEFHOOK
(preferred_output_reload_class,
 "Like @code{TARGET_PREFERRED_RELOAD_CLASS}, but for output reloads instead of\n\
input reloads.\n\
\n\
The default version of this hook always returns value of @code{rclass}\n\
argument.\n\
\n\
You can also use @code{TARGET_PREFERRED_OUTPUT_RELOAD_CLASS} to discourage\n\
reload from using some alternatives, like @code{TARGET_PREFERRED_RELOAD_CLASS}.",
 reg_class_t,
 (rtx x, reg_class_t rclass),
 default_preferred_output_reload_class)

DEFHOOK
(select_early_remat_modes,
 "On some targets, certain modes cannot be held in registers around a\n\
standard ABI call and are relatively expensive to spill to the stack.\n\
The early rematerialization pass can help in such cases by aggressively\n\
recomputing values after calls, so that they don't need to be spilled.\n\
\n\
This hook returns the set of such modes by setting the associated bits\n\
in @var{modes}.  The default implementation selects no modes, which has\n\
the effect of disabling the early rematerialization pass.",
 void, (sbitmap modes),
 default_select_early_remat_modes)

DEFHOOK
(class_likely_spilled_p,
 "A target hook which returns @code{true} if pseudos that have been assigned\n\
to registers of class @var{rclass} would likely be spilled because\n\
registers of @var{rclass} are needed for spill registers.\n\
\n\
The default version of this target hook returns @code{true} if @var{rclass}\n\
has exactly one register and @code{false} otherwise.  On most machines, this\n\
default should be used.  For generally register-starved machines, such as\n\
i386, or machines with right register constraints, such as SH, this hook\n\
can be used to avoid excessive spilling.\n\
\n\
This hook is also used by some of the global intra-procedural code\n\
transformations to throtle code motion, to avoid increasing register\n\
pressure.",
 bool, (reg_class_t rclass),
 default_class_likely_spilled_p)

/* Return the maximum number of consecutive registers
   needed to represent mode MODE in a register of class RCLASS.  */
DEFHOOK
(class_max_nregs,
 "A target hook returns the maximum number of consecutive registers\n\
of class @var{rclass} needed to hold a value of mode @var{mode}.\n\
\n\
This is closely related to the macro @code{TARGET_HARD_REGNO_NREGS}.\n\
In fact, the value returned by @code{TARGET_CLASS_MAX_NREGS (@var{rclass},\n\
@var{mode})} target hook should be the maximum value of\n\
@code{TARGET_HARD_REGNO_NREGS (@var{regno}, @var{mode})} for all @var{regno}\n\
values in the class @var{rclass}.\n\
\n\
This target hook helps control the handling of multiple-word values\n\
in the reload pass.\n\
\n\
The default version of this target hook returns the size of @var{mode}\n\
in words.",
 unsigned char, (reg_class_t rclass, machine_mode mode),
 default_class_max_nregs)

DEFHOOK
(preferred_rename_class,
 "A target hook that places additional preference on the register\n\
class to use when it is necessary to rename a register in class\n\
@var{rclass} to another class, or perhaps @var{NO_REGS}, if no\n\
preferred register class is found or hook @code{preferred_rename_class}\n\
is not implemented.\n\
Sometimes returning a more restrictive class makes better code.  For\n\
example, on ARM, thumb-2 instructions using @code{LO_REGS} may be\n\
smaller than instructions using @code{GENERIC_REGS}.  By returning\n\
@code{LO_REGS} from @code{preferred_rename_class}, code size can\n\
be reduced.",
 reg_class_t, (reg_class_t rclass),
 default_preferred_rename_class)

/* This target hook allows the backend to avoid unsafe substitution
   during register allocation.  */
DEFHOOK
(cannot_substitute_mem_equiv_p,
 "A target hook which returns @code{true} if @var{subst} can't\n\
substitute safely pseudos with equivalent memory values during\n\
register allocation.\n\
The default version of this target hook returns @code{false}.\n\
On most machines, this default should be used.  For generally\n\
machines with non orthogonal register usage for addressing, such\n\
as SH, this hook can be used to avoid excessive spilling.",
 bool, (rtx subst),
 hook_bool_rtx_false)

/* This target hook allows the backend to legitimize base plus
   displacement addressing.  */
DEFHOOK
(legitimize_address_displacement,
 "This hook tries to split address offset @var{orig_offset} into\n\
two parts: one that should be added to the base address to create\n\
a local anchor point, and an additional offset that can be applied\n\
to the anchor to address a value of mode @var{mode}.  The idea is that\n\
the local anchor could be shared by other accesses to nearby locations.\n\
\n\
The hook returns true if it succeeds, storing the offset of the\n\
anchor from the base in @var{offset1} and the offset of the final address\n\
from the anchor in @var{offset2}.  The default implementation returns false.",
 bool, (rtx *offset1, rtx *offset2, poly_int64 orig_offset, machine_mode mode),
 default_legitimize_address_displacement)

/* This target hook allows the backend to perform additional
   processing while initializing for variable expansion.  */
DEFHOOK
(expand_to_rtl_hook,
 "This hook is called just before expansion into rtl, allowing the target\n\
to perform additional initializations or analysis before the expansion.\n\
For example, the rs6000 port uses it to allocate a scratch stack slot\n\
for use in copying SDmode values between memory and floating point\n\
registers whenever the function being expanded has any SDmode\n\
usage.",
 void, (void),
 hook_void_void)

/* This target hook allows the backend to perform additional
   instantiations on rtx that are not actually in insns yet,
   but will be later.  */
DEFHOOK
(instantiate_decls,
 "This hook allows the backend to perform additional instantiations on rtl\n\
that are not actually in any insns yet, but will be later.",
 void, (void),
 hook_void_void)

DEFHOOK
(hard_regno_nregs,
 "This hook returns the number of consecutive hard registers, starting\n\
at register number @var{regno}, required to hold a value of mode\n\
@var{mode}.  This hook must never return zero, even if a register\n\
cannot hold the requested mode - indicate that with\n\
@code{TARGET_HARD_REGNO_MODE_OK} and/or\n\
@code{TARGET_CAN_CHANGE_MODE_CLASS} instead.\n\
\n\
The default definition returns the number of words in @var{mode}.",
 unsigned int, (unsigned int regno, machine_mode mode),
 default_hard_regno_nregs)

DEFHOOK
(hard_regno_mode_ok,
 "This hook returns true if it is permissible to store a value\n\
of mode @var{mode} in hard register number @var{regno} (or in several\n\
registers starting with that one).  The default definition returns true\n\
unconditionally.\n\
\n\
You need not include code to check for the numbers of fixed registers,\n\
because the allocation mechanism considers them to be always occupied.\n\
\n\
@cindex register pairs\n\
On some machines, double-precision values must be kept in even/odd\n\
register pairs.  You can implement that by defining this hook to reject\n\
odd register numbers for such modes.\n\
\n\
The minimum requirement for a mode to be OK in a register is that the\n\
@samp{mov@var{mode}} instruction pattern support moves between the\n\
register and other hard register in the same class and that moving a\n\
value into the register and back out not alter it.\n\
\n\
Since the same instruction used to move @code{word_mode} will work for\n\
all narrower integer modes, it is not necessary on any machine for\n\
this hook to distinguish between these modes, provided you define\n\
patterns @samp{movhi}, etc., to take advantage of this.  This is\n\
useful because of the interaction between @code{TARGET_HARD_REGNO_MODE_OK}\n\
and @code{TARGET_MODES_TIEABLE_P}; it is very desirable for all integer\n\
modes to be tieable.\n\
\n\
Many machines have special registers for floating point arithmetic.\n\
Often people assume that floating point machine modes are allowed only\n\
in floating point registers.  This is not true.  Any registers that\n\
can hold integers can safely @emph{hold} a floating point machine\n\
mode, whether or not floating arithmetic can be done on it in those\n\
registers.  Integer move instructions can be used to move the values.\n\
\n\
On some machines, though, the converse is true: fixed-point machine\n\
modes may not go in floating registers.  This is true if the floating\n\
registers normalize any value stored in them, because storing a\n\
non-floating value there would garble it.  In this case,\n\
@code{TARGET_HARD_REGNO_MODE_OK} should reject fixed-point machine modes in\n\
floating registers.  But if the floating registers do not automatically\n\
normalize, if you can store any bit pattern in one and retrieve it\n\
unchanged without a trap, then any machine mode may go in a floating\n\
register, so you can define this hook to say so.\n\
\n\
The primary significance of special floating registers is rather that\n\
they are the registers acceptable in floating point arithmetic\n\
instructions.  However, this is of no concern to\n\
@code{TARGET_HARD_REGNO_MODE_OK}.  You handle it by writing the proper\n\
constraints for those instructions.\n\
\n\
On some machines, the floating registers are especially slow to access,\n\
so that it is better to store a value in a stack frame than in such a\n\
register if floating point arithmetic is not being done.  As long as the\n\
floating registers are not in class @code{GENERAL_REGS}, they will not\n\
be used unless some pattern's constraint asks for one.",
 bool, (unsigned int regno, machine_mode mode),
 hook_bool_uint_mode_true)

DEFHOOK
(modes_tieable_p,
 "This hook returns true if a value of mode @var{mode1} is accessible\n\
in mode @var{mode2} without copying.\n\
\n\
If @code{TARGET_HARD_REGNO_MODE_OK (@var{r}, @var{mode1})} and\n\
@code{TARGET_HARD_REGNO_MODE_OK (@var{r}, @var{mode2})} are always\n\
the same for any @var{r}, then\n\
@code{TARGET_MODES_TIEABLE_P (@var{mode1}, @var{mode2})}\n\
should be true.  If they differ for any @var{r}, you should define\n\
this hook to return false unless some other mechanism ensures the\n\
accessibility of the value in a narrower mode.\n\
\n\
You should define this hook to return true in as many cases as\n\
possible since doing so will allow GCC to perform better register\n\
allocation.  The default definition returns true unconditionally.",
 bool, (machine_mode mode1, machine_mode mode2),
 hook_bool_mode_mode_true)

/* Return true if is OK to use a hard register REGNO as scratch register
   in peephole2.  */
DEFHOOK
(hard_regno_scratch_ok,
 "This target hook should return @code{true} if it is OK to use a hard register\n\
@var{regno} as scratch reg in peephole2.\n\
\n\
One common use of this macro is to prevent using of a register that\n\
is not saved by a prologue in an interrupt handler.\n\
\n\
The default version of this hook always returns @code{true}.",
 bool, (unsigned int regno),
 default_hard_regno_scratch_ok)

DEFHOOK
(hard_regno_call_part_clobbered,
 "ABIs usually specify that calls must preserve the full contents\n\
of a particular register, or that calls can alter any part of a\n\
particular register.  This information is captured by the target macro\n\
@code{CALL_REALLY_USED_REGISTERS}.  However, some ABIs specify that calls\n\
must preserve certain bits of a particular register but can alter others.\n\
This hook should return true if this applies to at least one of the\n\
registers in @samp{(reg:@var{mode} @var{regno})}, and if as a result the\n\
call would alter part of the @var{mode} value.  For example, if a call\n\
preserves the low 32 bits of a 64-bit hard register @var{regno} but can\n\
clobber the upper 32 bits, this hook should return true for a 64-bit mode\n\
but false for a 32-bit mode.\n\
\n\
The value of @var{abi_id} comes from the @code{predefined_function_abi}\n\
structure that describes the ABI of the call; see the definition of the\n\
structure for more details.  If (as is usual) the target uses the same ABI\n\
for all functions in a translation unit, @var{abi_id} is always 0.\n\
\n\
The default implementation returns false, which is correct\n\
for targets that don't have partly call-clobbered registers.",
 bool, (unsigned int abi_id, unsigned int regno, machine_mode mode),
 hook_bool_uint_uint_mode_false)

DEFHOOK
(get_multilib_abi_name,
 "This hook returns name of multilib ABI name.",
 const char *, (void),
 hook_constcharptr_void_null)

/* Return the smallest number of different values for which it is best to
   use a jump-table instead of a tree of conditional branches.  */
DEFHOOK
(case_values_threshold,
 "This function return the smallest number of different values for which it\n\
is best to use a jump-table instead of a tree of conditional branches.\n\
The default is four for machines with a @code{casesi} instruction and\n\
five otherwise.  This is best for most machines.",
 unsigned int, (void),
 default_case_values_threshold)

DEFHOOK
(starting_frame_offset,
 "This hook returns the offset from the frame pointer to the first local\n\
variable slot to be allocated.  If @code{FRAME_GROWS_DOWNWARD}, it is the\n\
offset to @emph{end} of the first slot allocated, otherwise it is the\n\
offset to @emph{beginning} of the first slot allocated.  The default\n\
implementation returns 0.",
 HOST_WIDE_INT, (void),
 hook_hwi_void_0)

/* Optional callback to advise the target to compute the frame layout.  */
DEFHOOK
(compute_frame_layout,
 "This target hook is called once each time the frame layout needs to be\n\
recalculated.  The calculations can be cached by the target and can then\n\
be used by @code{INITIAL_ELIMINATION_OFFSET} instead of re-computing the\n\
layout on every invocation of that hook.  This is particularly useful\n\
for targets that have an expensive frame layout function.  Implementing\n\
this callback is optional.",
 void, (void),
 hook_void_void)

/* Return true if a function must have and use a frame pointer.  */
DEFHOOK
(frame_pointer_required,
 "This target hook should return @code{true} if a function must have and use\n\
a frame pointer.  This target hook is called in the reload pass.  If its return\n\
value is @code{true} the function will have a frame pointer.\n\
\n\
This target hook can in principle examine the current function and decide\n\
according to the facts, but on most machines the constant @code{false} or the\n\
constant @code{true} suffices.  Use @code{false} when the machine allows code\n\
to be generated with no frame pointer, and doing so saves some time or space.\n\
Use @code{true} when there is no possible advantage to avoiding a frame\n\
pointer.\n\
\n\
In certain cases, the compiler does not know how to produce valid code\n\
without a frame pointer.  The compiler recognizes those cases and\n\
automatically gives the function a frame pointer regardless of what\n\
@code{targetm.frame_pointer_required} returns.  You don't need to worry about\n\
them.\n\
\n\
In a function that does not require a frame pointer, the frame pointer\n\
register can be allocated for ordinary usage, unless you mark it as a\n\
fixed register.  See @code{FIXED_REGISTERS} for more information.\n\
\n\
Default return value is @code{false}.",
 bool, (void),
 hook_bool_void_false)

/* Returns true if the compiler is allowed to try to replace register number
   from-reg with register number to-reg.  */
DEFHOOK
(can_eliminate,
 "This target hook should return @code{true} if the compiler is allowed to\n\
try to replace register number @var{from_reg} with register number\n\
@var{to_reg}.  This target hook will usually be @code{true}, since most of the\n\
cases preventing register elimination are things that the compiler already\n\
knows about.\n\
\n\
Default return value is @code{true}.",
 bool, (const int from_reg, const int to_reg),
 hook_bool_const_int_const_int_true)

/* Modify any or all of fixed_regs, call_used_regs, global_regs,
   reg_names, and reg_class_contents to account of the vagaries of the
   target.  */
DEFHOOK
(conditional_register_usage,
 "This hook may conditionally modify five variables\n\
@code{fixed_regs}, @code{call_used_regs}, @code{global_regs},\n\
@code{reg_names}, and @code{reg_class_contents}, to take into account\n\
any dependence of these register sets on target flags.  The first three\n\
of these are of type @code{char []} (interpreted as boolean vectors).\n\
@code{global_regs} is a @code{const char *[]}, and\n\
@code{reg_class_contents} is a @code{HARD_REG_SET}.  Before the macro is\n\
called, @code{fixed_regs}, @code{call_used_regs},\n\
@code{reg_class_contents}, and @code{reg_names} have been initialized\n\
from @code{FIXED_REGISTERS}, @code{CALL_USED_REGISTERS},\n\
@code{REG_CLASS_CONTENTS}, and @code{REGISTER_NAMES}, respectively.\n\
@code{global_regs} has been cleared, and any @option{-ffixed-@var{reg}},\n\
@option{-fcall-used-@var{reg}} and @option{-fcall-saved-@var{reg}}\n\
command options have been applied.\n\
\n\
@cindex disabling certain registers\n\
@cindex controlling register usage\n\
If the usage of an entire class of registers depends on the target\n\
flags, you may indicate this to GCC by using this macro to modify\n\
@code{fixed_regs} and @code{call_used_regs} to 1 for each of the\n\
registers in the classes which should not be used by GCC@.  Also make\n\
@code{define_register_constraint}s return @code{NO_REGS} for constraints\n\
that shouldn't be used.\n\
\n\
(However, if this class is not included in @code{GENERAL_REGS} and all\n\
of the insn patterns whose constraints permit this class are\n\
controlled by target switches, then GCC will automatically avoid using\n\
these registers when the target switches are opposed to them.)",
 void, (void),
 hook_void_void)

DEFHOOK
(stack_clash_protection_alloca_probe_range,
 "Some targets have an ABI defined interval for which no probing needs to be done.\n\
When a probe does need to be done this same interval is used as the probe distance\n\
up when doing stack clash protection for alloca.\n\
On such targets this value can be set to override the default probing up interval.\n\
Define this variable to return nonzero if such a probe range is required or zero otherwise.\n\
Defining this hook also requires your functions which make use of alloca to have at least 8 byes\n\
of outgoing arguments.  If this is not the case the stack will be corrupted.\n\
You need not define this macro if it would always have the value zero.",
 HOST_WIDE_INT, (void),
 default_stack_clash_protection_alloca_probe_range)


/* Functions specific to the C family of frontends.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_C_"
HOOK_VECTOR (TARGET_C, c)

/* ??? Documenting this hook requires a GFDL license grant.  */
DEFHOOK_UNDOC
(mode_for_suffix,
"Return machine mode for non-standard constant literal suffix @var{c},\
 or VOIDmode if non-standard suffixes are unsupported.",
 machine_mode, (char c),
 default_mode_for_suffix)

DEFHOOK
(excess_precision,
 "Return a value, with the same meaning as the C99 macro\n\
@code{FLT_EVAL_METHOD} that describes which excess precision should be\n\
applied.  @var{type} is either @code{EXCESS_PRECISION_TYPE_IMPLICIT},\n\
@code{EXCESS_PRECISION_TYPE_FAST},\n\
@code{EXCESS_PRECISION_TYPE_STANDARD}, or\n\
@code{EXCESS_PRECISION_TYPE_FLOAT16}.  For\n\
@code{EXCESS_PRECISION_TYPE_IMPLICIT}, the target should return which\n\
precision and range operations will be implictly evaluated in regardless\n\
of the excess precision explicitly added.  For\n\
@code{EXCESS_PRECISION_TYPE_STANDARD}, \n\
@code{EXCESS_PRECISION_TYPE_FLOAT16}, and\n\
@code{EXCESS_PRECISION_TYPE_FAST}, the target should return the\n\
explicit excess precision that should be added depending on the\n\
value set for @option{-fexcess-precision=@r{[}standard@r{|}fast@r{|}16@r{]}}.\n\
Note that unpredictable explicit excess precision does not make sense,\n\
so a target should never return @code{FLT_EVAL_METHOD_UNPREDICTABLE}\n\
when @var{type} is @code{EXCESS_PRECISION_TYPE_STANDARD},\n\
@code{EXCESS_PRECISION_TYPE_FLOAT16} or\n\
@code{EXCESS_PRECISION_TYPE_FAST}.",
 enum flt_eval_method, (enum excess_precision_type type),
 default_excess_precision)

/* Return true if _BitInt(N) is supported and fill details about it into
   *INFO.  */
DEFHOOK
(bitint_type_info,
 "This target hook returns true if @code{_BitInt(@var{N})} is supported and\n\
provides details on it.  @code{_BitInt(@var{N})} is to be represented as\n\
series of @code{info->abi_limb_mode}\n\
@code{CEIL (@var{N}, GET_MODE_PRECISION (info->abi_limb_mode))} limbs,\n\
ordered from least significant to most significant if\n\
@code{!info->big_endian}, otherwise from most significant to least\n\
significant.  If @code{info->extended} is false, the bits above or equal to\n\
@var{N} are undefined when stored in a register or memory, otherwise they\n\
are zero or sign extended depending on if it is\n\
@code{unsigned _BitInt(@var{N})} or one of @code{_BitInt(@var{N})} or\n\
@code{signed _BitInt(@var{N})}.  Alignment of the type is\n\
@code{GET_MODE_ALIGNMENT (info->limb_mode)}.",
 bool, (int n, struct bitint_info *info),
 default_bitint_type_info)

DEFHOOK
(mode_for_floating_type,
"Return machine mode for a C floating point type which is indicated by\n\
 a given @code{enum tree_index} @var{ti}, @var{ti} should be\n\
 @code{TI_FLOAT_TYPE}, @code{TI_DOUBLE_TYPE} or @code{TI_LONG_DOUBLE_TYPE}.\n\
 The default implementation returns @code{SFmode} for @code{TI_FLOAT_TYPE},\n\
 and @code{DFmode} for @code{TI_DOUBLE_TYPE} or @code{TI_LONG_DOUBLE_TYPE}.",
 machine_mode, (enum tree_index ti), default_mode_for_floating_type)

HOOK_VECTOR_END (c)

/* Functions specific to the C++ frontend.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_CXX_"
HOOK_VECTOR (TARGET_CXX, cxx)

/* Return the integer type used for guard variables.  */
DEFHOOK
(guard_type,
 "Define this hook to override the integer type used for guard variables.\n\
These are used to implement one-time construction of static objects.  The\n\
default is long_long_integer_type_node.",
 tree, (void),
 default_cxx_guard_type)

/* Return true if only the low bit of the guard should be tested.  */
DEFHOOK
(guard_mask_bit,
 "This hook determines how guard variables are used.  It should return\n\
@code{false} (the default) if the first byte should be used.  A return value of\n\
@code{true} indicates that only the least significant bit should be used.",
 bool, (void),
 hook_bool_void_false)

/* Returns the size of the array cookie for an array of type.  */
DEFHOOK
(get_cookie_size,
 "This hook returns the size of the cookie to use when allocating an array\n\
whose elements have the indicated @var{type}.  Assumes that it is already\n\
known that a cookie is needed.  The default is\n\
@code{max(sizeof (size_t), alignof(type))}, as defined in section 2.7 of the\n\
IA64/Generic C++ ABI@.",
 tree, (tree type),
 default_cxx_get_cookie_size)

/* Returns true if the element size should be stored in the array cookie.  */
DEFHOOK
(cookie_has_size,
 "This hook should return @code{true} if the element size should be stored in\n\
array cookies.  The default is to return @code{false}.",
 bool, (void),
 hook_bool_void_false)

/* Allows backends to perform additional processing when
   deciding if a class should be exported or imported.  */
DEFHOOK
(import_export_class,
 "If defined by a backend this hook allows the decision made to export\n\
class @var{type} to be overruled.  Upon entry @var{import_export}\n\
will contain 1 if the class is going to be exported, @minus{}1 if it is going\n\
to be imported and 0 otherwise.  This function should return the\n\
modified value and perform any other actions necessary to support the\n\
backend's targeted operating system.",
 int, (tree type, int import_export), NULL)

/* Returns true if constructors and destructors return "this".  */
DEFHOOK
(cdtor_returns_this,
 "This hook should return @code{true} if constructors and destructors return\n\
the address of the object created/destroyed.  The default is to return\n\
@code{false}.",
 bool, (void),
 hook_bool_void_false)

/* Returns true if the key method for a class can be an inline
   function, so long as it is not declared inline in the class
   itself.  Returning true is the behavior required by the Itanium C++ ABI.  */
DEFHOOK
(key_method_may_be_inline,
 "This hook returns true if the key method for a class (i.e., the method\n\
which, if defined in the current translation unit, causes the virtual\n\
table to be emitted) may be an inline function.  Under the standard\n\
Itanium C++ ABI the key method may be an inline function so long as\n\
the function is not declared inline in the class definition.  Under\n\
some variants of the ABI, an inline function can never be the key\n\
method.  The default is to return @code{true}.",
 bool, (void),
 hook_bool_void_true)

DEFHOOK
(determine_class_data_visibility,
"@var{decl} is a virtual table, virtual table table, typeinfo object,\n\
or other similar implicit class data object that will be emitted with\n\
external linkage in this translation unit.  No ELF visibility has been\n\
explicitly specified.  If the target needs to specify a visibility\n\
other than that of the containing class, use this hook to set\n\
@code{DECL_VISIBILITY} and @code{DECL_VISIBILITY_SPECIFIED}.",
 void, (tree decl),
 hook_void_tree)

/* Returns true (the default) if virtual tables and other
   similar implicit class data objects are always COMDAT if they
   have external linkage.  If this hook returns false, then
   class data for classes whose virtual table will be emitted in
   only one translation unit will not be COMDAT.  */
DEFHOOK
(class_data_always_comdat,
 "This hook returns true (the default) if virtual tables and other\n\
similar implicit class data objects are always COMDAT if they have\n\
external linkage.  If this hook returns false, then class data for\n\
classes whose virtual table will be emitted in only one translation\n\
unit will not be COMDAT.",
 bool, (void),
 hook_bool_void_true)

/* Returns true (the default) if the RTTI for the basic types,
   which is always defined in the C++ runtime, should be COMDAT;
   false if it should not be COMDAT.  */
DEFHOOK
(library_rtti_comdat,
 "This hook returns true (the default) if the RTTI information for\n\
the basic types which is defined in the C++ runtime should always\n\
be COMDAT, false if it should not be COMDAT.",
 bool, (void),
 hook_bool_void_true)

/* Returns true if __aeabi_atexit should be used to register static
   destructors.  */
DEFHOOK
(use_aeabi_atexit,
 "This hook returns true if @code{__aeabi_atexit} (as defined by the ARM EABI)\n\
should be used to register static destructors when @option{-fuse-cxa-atexit}\n\
is in effect.  The default is to return false to use @code{__cxa_atexit}.",
 bool, (void),
 hook_bool_void_false)

/* Returns true if target may use atexit in the same manner as
   __cxa_atexit to register static destructors.  */
DEFHOOK
(use_atexit_for_cxa_atexit,
 "This hook returns true if the target @code{atexit} function can be used\n\
in the same manner as @code{__cxa_atexit} to register C++ static\n\
destructors. This requires that @code{atexit}-registered functions in\n\
shared libraries are run in the correct order when the libraries are\n\
unloaded. The default is to return false.",
 bool, (void),
 hook_bool_void_false)

/* Returns modified FUNCTION_TYPE for cdtor callabi.  */
DEFHOOK
(adjust_cdtor_callabi_fntype,
 "This hook returns a possibly modified @code{FUNCTION_TYPE} for arguments\n\
to @code{__cxa_atexit}, @code{__cxa_thread_atexit} or @code{__cxa_throw}\n\
function pointers.  ABIs like mingw32 require special attributes to be added\n\
to function types pointed to by arguments of these functions.\n\
The default is to return the passed argument unmodified.",
 tree, (tree fntype),
 default_cxx_adjust_cdtor_callabi_fntype)

DEFHOOK
(adjust_class_at_definition,
"@var{type} is a C++ class (i.e., RECORD_TYPE or UNION_TYPE) that has just\n\
been defined.  Use this hook to make adjustments to the class (eg, tweak\n\
visibility or perform any other required target modifications).",
 void, (tree type),
 hook_void_tree)

DEFHOOK
(decl_mangling_context,
 "Return target-specific mangling context of @var{decl} or @code{NULL_TREE}.",
 tree, (const_tree decl),
 hook_tree_const_tree_null)

HOOK_VECTOR_END (cxx)

/* Functions and data for emulated TLS support.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_EMUTLS_"
HOOK_VECTOR (TARGET_EMUTLS, emutls)

/* Name of the address and common functions.  */
DEFHOOKPOD
(get_address,
 "Contains the name of the helper function that uses a TLS control\n\
object to locate a TLS instance.  The default causes libgcc's\n\
emulated TLS helper function to be used.",
 const char *, "__builtin___emutls_get_address")

DEFHOOKPOD
(register_common,
 "Contains the name of the helper function that should be used at\n\
program startup to register TLS objects that are implicitly\n\
initialized to zero.  If this is @code{NULL}, all TLS objects will\n\
have explicit initializers.  The default causes libgcc's emulated TLS\n\
registration function to be used.",
 const char *, "__builtin___emutls_register_common")

/* Prefixes for proxy variable and template.  */
DEFHOOKPOD
(var_section,
 "Contains the name of the section in which TLS control variables should\n\
be placed.  The default of @code{NULL} allows these to be placed in\n\
any section.",
 const char *, NULL)

DEFHOOKPOD
(tmpl_section,
 "Contains the name of the section in which TLS initializers should be\n\
placed.  The default of @code{NULL} allows these to be placed in any\n\
section.",
 const char *, NULL)

/* Prefixes for proxy variable and template.  */
DEFHOOKPOD
(var_prefix,
 "Contains the prefix to be prepended to TLS control variable names.\n\
The default of @code{NULL} uses a target-specific prefix.",
 const char *, NULL)

DEFHOOKPOD
(tmpl_prefix,
 "Contains the prefix to be prepended to TLS initializer objects.  The\n\
default of @code{NULL} uses a target-specific prefix.",
 const char *, NULL)

/* Function to generate field definitions of the proxy variable.  */
DEFHOOK
(var_fields,
 "Specifies a function that generates the FIELD_DECLs for a TLS control\n\
object type.  @var{type} is the RECORD_TYPE the fields are for and\n\
@var{name} should be filled with the structure tag, if the default of\n\
@code{__emutls_object} is unsuitable.  The default creates a type suitable\n\
for libgcc's emulated TLS function.",
 tree, (tree type, tree *name),
 default_emutls_var_fields)

/* Function to initialize a proxy variable.  */
DEFHOOK
(var_init,
 "Specifies a function that generates the CONSTRUCTOR to initialize a\n\
TLS control object.  @var{var} is the TLS control object, @var{decl}\n\
is the TLS object and @var{tmpl_addr} is the address of the\n\
initializer.  The default initializes libgcc's emulated TLS control object.",
 tree, (tree var, tree decl, tree tmpl_addr),
 default_emutls_var_init)

/* Whether we are allowed to alter the usual alignment of the
   proxy variable.  */
DEFHOOKPOD
(var_align_fixed,
 "Specifies whether the alignment of TLS control variable objects is\n\
fixed and should not be increased as some backends may do to optimize\n\
single objects.  The default is false.",
 bool, false)

/* Whether we can emit debug information for TLS vars.  */
DEFHOOKPOD
(debug_form_tls_address,
 "Specifies whether a DWARF @code{DW_OP_form_tls_address} location descriptor\n\
may be used to describe emulated TLS control objects.",
 bool, false)

HOOK_VECTOR_END (emutls)

#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_OPTION_"
HOOK_VECTOR (TARGET_OPTION_HOOKS, target_option_hooks)

/* Function to validate the attribute((target(...))) strings.  If
   the option is validated, the hook should also fill in
   DECL_FUNCTION_SPECIFIC_TARGET in the function decl node.  */
DEFHOOK
(valid_attribute_p,
 "This hook is called to parse @code{attribute(target(\"...\"))}, which\n\
allows setting target-specific options on individual functions.\n\
These function-specific options may differ\n\
from the options specified on the command line.  The hook should return\n\
@code{true} if the options are valid.\n\
\n\
The hook should set the @code{DECL_FUNCTION_SPECIFIC_TARGET} field in\n\
the function declaration to hold a pointer to a target-specific\n\
@code{struct cl_target_option} structure.",
 bool, (tree fndecl, tree name, tree args, int flags),
 default_target_option_valid_attribute_p)

/* Function to validate the attribute((target_version(...))) strings.  If
   the option is validated, the hook should also fill in
   DECL_FUNCTION_SPECIFIC_TARGET in the function decl node.  */
DEFHOOK
(valid_version_attribute_p,
 "This hook is called to parse @code{attribute(target_version(\"...\"))},\n\
which allows setting target-specific options on individual function versions.\n\
These function-specific options may differ\n\
from the options specified on the command line.  The hook should return\n\
@code{true} if the options are valid.\n\
\n\
The hook should set the @code{DECL_FUNCTION_SPECIFIC_TARGET} field in\n\
the function declaration to hold a pointer to a target-specific\n\
@code{struct cl_target_option} structure.",
 bool, (tree fndecl, tree name, tree args, int flags),
 default_target_option_valid_version_attribute_p)

/* Function to save any extra target state in the target options structure.  */
DEFHOOK
(save,
 "This hook is called to save any additional target-specific information\n\
in the @code{struct cl_target_option} structure for function-specific\n\
options from the @code{struct gcc_options} structure.\n\
@xref{Option file format}.",
 void, (struct cl_target_option *ptr, struct gcc_options *opts,
	struct gcc_options *opts_set), NULL)

/* Function to restore any extra target state from the target options
   structure.  */
DEFHOOK
(restore,
 "This hook is called to restore any additional target-specific\n\
information in the @code{struct cl_target_option} structure for\n\
function-specific options to the @code{struct gcc_options} structure.",
 void, (struct gcc_options *opts, struct gcc_options *opts_set,
	struct cl_target_option *ptr), NULL)

/* Function to update target-specific option information after being
   streamed in.  */
DEFHOOK
(post_stream_in,
 "This hook is called to update target-specific information in the\n\
@code{struct cl_target_option} structure after it is streamed in from\n\
LTO bytecode.",
 void, (struct cl_target_option *ptr), NULL)

/* Function to print any extra target state from the target options
   structure.  */
DEFHOOK
(print,
 "This hook is called to print any additional target-specific\n\
information in the @code{struct cl_target_option} structure for\n\
function-specific options.",
 void, (FILE *file, int indent, struct cl_target_option *ptr), NULL)

/* Function to parse arguments to be validated for #pragma target, and to
   change the state if the options are valid.  If the first argument is
   NULL, the second argument specifies the default options to use.  Return
   true if the options are valid, and set the current state.  */
DEFHOOK
(pragma_parse,
 "This target hook parses the options for @code{#pragma GCC target}, which\n\
sets the target-specific options for functions that occur later in the\n\
input stream.  The options accepted should be the same as those handled by the\n\
@code{TARGET_OPTION_VALID_ATTRIBUTE_P} hook.",
 bool, (tree args, tree pop_target),
 default_target_option_pragma_parse)

/* Do option overrides for the target.  */
DEFHOOK
(override,
 "Sometimes certain combinations of command options do not make sense on\n\
a particular target machine.  You can override the hook\n\
@code{TARGET_OPTION_OVERRIDE} to take account of this.  This hooks is called\n\
once just after all the command options have been parsed.\n\
\n\
Don't use this hook to turn on various extra optimizations for\n\
@option{-O}.  That is what @code{TARGET_OPTION_OPTIMIZATION} is for.\n\
\n\
If you need to do something whenever the optimization level is\n\
changed via the optimize attribute or pragma, see\n\
@code{TARGET_OVERRIDE_OPTIONS_AFTER_CHANGE}",
 void, (void),
 hook_void_void)

/* This function returns true if DECL1 and DECL2 are versions of the same
   function.  DECL1 and DECL2 are function versions if and only if they
   have the same function signature and different target specific attributes,
   that is, they are compiled for different target machines.  */
DEFHOOK
(function_versions,
 "This target hook returns @code{true} if @var{DECL1} and @var{DECL2} are\n\
versions of the same function.  @var{DECL1} and @var{DECL2} are function\n\
versions if and only if they have the same function signature and\n\
different target specific attributes, that is, they are compiled for\n\
different target machines.",
 bool, (tree decl1, tree decl2),
 hook_bool_tree_tree_false)

/* Function to determine if one function can inline another function.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_"
DEFHOOK
(can_inline_p,
 "This target hook returns @code{false} if the @var{caller} function\n\
cannot inline @var{callee}, based on target specific information.  By\n\
default, inlining is not allowed if the callee function has function\n\
specific target options and the caller does not use the same options.",
 bool, (tree caller, tree callee),
 default_target_can_inline_p)

DEFHOOK
(update_ipa_fn_target_info,
 "Allow target to analyze all gimple statements for the given function to\n\
record and update some target specific information for inlining.  A typical\n\
example is that a caller with one isa feature disabled is normally not\n\
allowed to inline a callee with that same isa feature enabled even which is\n\
attributed by always_inline, but with the conservative analysis on all\n\
statements of the callee if we are able to guarantee the callee does not\n\
exploit any instructions from the mismatch isa feature, it would be safe to\n\
allow the caller to inline the callee.\n\
@var{info} is one @code{unsigned int} value to record information in which\n\
one set bit indicates one corresponding feature is detected in the analysis,\n\
@var{stmt} is the statement being analyzed.  Return true if target still\n\
need to analyze the subsequent statements, otherwise return false to stop\n\
subsequent analysis.\n\
The default version of this hook returns false.",
 bool, (unsigned int& info, const gimple* stmt),
 default_update_ipa_fn_target_info)

DEFHOOK
(need_ipa_fn_target_info,
 "Allow target to check early whether it is necessary to analyze all gimple\n\
statements in the given function to update target specific information for\n\
inlining.  See hook @code{update_ipa_fn_target_info} for usage example of\n\
target specific information.  This hook is expected to be invoked ahead of\n\
the iterating with hook @code{update_ipa_fn_target_info}.\n\
@var{decl} is the function being analyzed, @var{info} is the same as what\n\
in hook @code{update_ipa_fn_target_info}, target can do one time update\n\
into @var{info} without iterating for some case.  Return true if target\n\
decides to analyze all gimple statements to collect information, otherwise\n\
return false.\n\
The default version of this hook returns false.",
 bool, (const_tree decl, unsigned int& info),
 default_need_ipa_fn_target_info)

DEFHOOK
(relayout_function,
"This target hook fixes function @var{fndecl} after attributes are processed.\n\
Default does nothing. On ARM, the default function's alignment is updated\n\
with the attribute target.",
 void, (tree fndecl),
 hook_void_tree)

HOOK_VECTOR_END (target_option)

/* For targets that need to mark extra registers as live on entry to
   the function, they should define this target hook and set their
   bits in the bitmap passed in. */
DEFHOOK
(extra_live_on_entry,
 "Add any hard registers to @var{regs} that are live on entry to the\n\
function.  This hook only needs to be defined to provide registers that\n\
cannot be found by examination of FUNCTION_ARG_REGNO_P, the callee saved\n\
registers, STATIC_CHAIN_INCOMING_REGNUM, STATIC_CHAIN_REGNUM,\n\
TARGET_STRUCT_VALUE_RTX, FRAME_POINTER_REGNUM, EH_USES,\n\
FRAME_POINTER_REGNUM, ARG_POINTER_REGNUM, and the PIC_OFFSET_TABLE_REGNUM.",
 void, (bitmap regs),
 hook_void_bitmap)

/* Targets should define this target hook to mark that non-callee clobbers are
   present in CALL_INSN_FUNCTION_USAGE for all the calls that bind to a local
   definition.  */
DEFHOOKPOD
(call_fusage_contains_non_callee_clobbers,
 "Set to true if each call that binds to a local definition explicitly\n\
clobbers or sets all non-fixed registers modified by performing the call.\n\
That is, by the call pattern itself, or by code that might be inserted by the\n\
linker (e.g.@: stubs, veneers, branch islands), but not including those\n\
modifiable by the callee.  The affected registers may be mentioned explicitly\n\
in the call pattern, or included as clobbers in CALL_INSN_FUNCTION_USAGE.\n\
The default version of this hook is set to false.  The purpose of this hook\n\
is to enable the fipa-ra optimization.",
 bool,
 false)

/* Fill in additional registers set up by prologue into a regset.  */
DEFHOOK
(set_up_by_prologue,
 "This hook should add additional registers that are computed by the prologue\n\
to the hard regset for shrink-wrapping optimization purposes.",
 void, (struct hard_reg_set_container *),
 NULL)

/* For targets that have attributes that can affect whether a
   function's return statements need checking.  For instance a 'naked'
   function attribute.  */
DEFHOOK
(warn_func_return,
 "True if a function's return statements should be checked for matching\n\
the function's return type.  This includes checking for falling off the end\n\
of a non-void function.  Return false if no such check should be made.",
 bool, (tree),
 hook_bool_tree_true)

#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_SHRINK_WRAP_"
HOOK_VECTOR (TARGET_SHRINK_WRAP_HOOKS, shrink_wrap)

DEFHOOK
(get_separate_components,
 "This hook should return an @code{sbitmap} with the bits set for those\n\
components that can be separately shrink-wrapped in the current function.\n\
Return @code{NULL} if the current function should not get any separate\n\
shrink-wrapping.\n\
Don't define this hook if it would always return @code{NULL}.\n\
If it is defined, the other hooks in this group have to be defined as well.",
 sbitmap, (void),
 NULL)

DEFHOOK
(components_for_bb,
 "This hook should return an @code{sbitmap} with the bits set for those\n\
components where either the prologue component has to be executed before\n\
the @code{basic_block}, or the epilogue component after it, or both.",
 sbitmap, (basic_block),
 NULL)

DEFHOOK
(disqualify_components,
 "This hook should clear the bits in the @var{components} bitmap for those\n\
components in @var{edge_components} that the target cannot handle on edge\n\
@var{e}, where @var{is_prologue} says if this is for a prologue or an\n\
epilogue instead.",
 void, (sbitmap components, edge e, sbitmap edge_components, bool is_prologue),
 NULL)

DEFHOOK
(emit_prologue_components,
 "Emit prologue insns for the components indicated by the parameter.",
 void, (sbitmap),
 NULL)

DEFHOOK
(emit_epilogue_components,
 "Emit epilogue insns for the components indicated by the parameter.",
 void, (sbitmap),
 NULL)

DEFHOOK
(set_handled_components,
 "Mark the components in the parameter as handled, so that the\n\
@code{prologue} and @code{epilogue} named patterns know to ignore those\n\
components.  The target code should not hang on to the @code{sbitmap}, it\n\
will be deleted after this call.",
 void, (sbitmap),
 NULL)

HOOK_VECTOR_END (shrink_wrap)
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_"

DEFHOOK
(avoid_store_forwarding_p,
 "Given a list of stores and a load instruction that reads from the location\n\
of the stores, this hook decides if it's profitable to emit additional code\n\
to avoid a potential store forwarding stall.  The additional instructions\n\
needed, the sequence cost and additional relevant information is given in\n\
the arguments so that the target can make an informed decision.",
 bool, (vec<store_fwd_info>, rtx, int, bool),
 default_avoid_store_forwarding_p)

/* Determine the type of unwind info to emit for debugging.  */
DEFHOOK
(debug_unwind_info,
 "This hook defines the mechanism that will be used for describing frame\n\
unwind information to the debugger.  Normally the hook will return\n\
@code{UI_DWARF2} if DWARF 2 debug information is enabled, and\n\
return @code{UI_NONE} otherwise.\n\
\n\
A target may return @code{UI_DWARF2} even when DWARF 2 debug information\n\
is disabled in order to always output DWARF 2 frame information.\n\
\n\
A target may return @code{UI_TARGET} if it has ABI specified unwind tables.\n\
This will suppress generation of the normal debug frame unwind information.",
 enum unwind_info_type, (void),
 default_debug_unwind_info)

DEFHOOK
(reset_location_view,
 "This hook, if defined, enables -ginternal-reset-location-views, and\n\
uses its result to override cases in which the estimated min insn\n\
length might be nonzero even when a PC advance (i.e., a view reset)\n\
cannot be taken for granted.\n\
\n\
If the hook is defined, it must return a positive value to indicate\n\
the insn definitely advances the PC, and so the view number can be\n\
safely assumed to be reset; a negative value to mean the insn\n\
definitely does not advance the PC, and os the view number must not\n\
be reset; or zero to decide based on the estimated insn length.\n\
\n\
If insn length is to be regarded as reliable, set the hook to\n\
@code{hook_int_rtx_insn_0}.",
 int, (rtx_insn *), NULL)

/* The code parameter should be of type enum rtx_code but this is not
   defined at this time.  */
DEFHOOK
(canonicalize_comparison,
 "On some machines not all possible comparisons are defined, but you can\n\
convert an invalid comparison into a valid one.  For example, the Alpha\n\
does not have a @code{GT} comparison, but you can use an @code{LT}\n\
comparison instead and swap the order of the operands.\n\
\n\
On such machines, implement this hook to do any required conversions.\n\
@var{code} is the initial comparison code and @var{op0} and @var{op1}\n\
are the left and right operands of the comparison, respectively.  If\n\
@var{op0_preserve_value} is @code{true} the implementation is not\n\
allowed to change the value of @var{op0} since the value might be used\n\
in RTXs which aren't comparisons.  E.g. the implementation is not\n\
allowed to swap operands in that case.\n\
\n\
GCC will not assume that the comparison resulting from this macro is\n\
valid but will see if the resulting insn matches a pattern in the\n\
@file{md} file.\n\
\n\
You need not to implement this hook if it would never change the\n\
comparison code or operands.",
 void, (int *code, rtx *op0, rtx *op1, bool op0_preserve_value),
 default_canonicalize_comparison)

DEFHOOK
(min_arithmetic_precision,
 "On some RISC architectures with 64-bit registers, the processor also\n\
maintains 32-bit condition codes that make it possible to do real 32-bit\n\
arithmetic, although the operations are performed on the full registers.\n\
\n\
On such architectures, defining this hook to 32 tells the compiler to try\n\
using 32-bit arithmetical operations setting the condition codes instead\n\
of doing full 64-bit arithmetic.\n\
\n\
More generally, define this hook on RISC architectures if you want the\n\
compiler to try using arithmetical operations setting the condition codes\n\
with a precision lower than the word precision.\n\
\n\
You need not define this hook if @code{WORD_REGISTER_OPERATIONS} is not\n\
defined to 1.",
 unsigned int, (void), default_min_arithmetic_precision)

DEFHOOKPOD
(atomic_test_and_set_trueval,
 "This value should be set if the result written by\n\
@code{atomic_test_and_set} is not exactly 1, i.e.@: the\n\
@code{bool} @code{true}.",
 unsigned char, 1)

/* Return an unsigned int representing the alignment (in bits) of the atomic
   type which maps to machine MODE.  This allows alignment to be overridden
   as needed.  */
DEFHOOK
(atomic_align_for_mode,
"If defined, this function returns an appropriate alignment in bits for an\n\
atomic object of machine_mode @var{mode}.  If 0 is returned then the\n\
default alignment for the specified mode is used.",
 unsigned int, (machine_mode mode),
 hook_uint_mode_0)

DEFHOOK
(atomic_assign_expand_fenv,
"ISO C11 requires atomic compound assignments that may raise floating-point\n\
exceptions to raise exceptions corresponding to the arithmetic operation\n\
whose result was successfully stored in a compare-and-exchange sequence.\n\
This requires code equivalent to calls to @code{feholdexcept},\n\
@code{feclearexcept} and @code{feupdateenv} to be generated at\n\
appropriate points in the compare-and-exchange sequence.  This hook should\n\
set @code{*@var{hold}} to an expression equivalent to the call to\n\
@code{feholdexcept}, @code{*@var{clear}} to an expression equivalent to\n\
the call to @code{feclearexcept} and @code{*@var{update}} to an expression\n\
equivalent to the call to @code{feupdateenv}.  The three expressions are\n\
@code{NULL_TREE} on entry to the hook and may be left as @code{NULL_TREE}\n\
if no code is required in a particular place.  The default implementation\n\
leaves all three expressions as @code{NULL_TREE}.  The\n\
@code{__atomic_feraiseexcept} function from @code{libatomic} may be of use\n\
as part of the code generated in @code{*@var{update}}.",
 void, (tree *hold, tree *clear, tree *update),
 default_atomic_assign_expand_fenv)

/* Leave the boolean fields at the end.  */

/* True if we can create zeroed data by switching to a BSS section
   and then using ASM_OUTPUT_SKIP to allocate the space.  */
DEFHOOKPOD
(have_switchable_bss_sections,
 "This flag is true if we can create zeroed data by switching to a BSS\n\
section and then using @code{ASM_OUTPUT_SKIP} to allocate the space.\n\
This is true on most ELF targets.",
 bool, false)

/* True if "native" constructors and destructors are supported,
   false if we're using collect2 for the job.  */
DEFHOOKPOD
(have_ctors_dtors,
 "This value is true if the target supports some ``native'' method of\n\
collecting constructors and destructors to be run at startup and exit.\n\
It is false if we must use @command{collect2}.",
 bool, false)

/* True if the target wants DTORs to be run from cxa_atexit.  */
DEFHOOKPOD
(dtors_from_cxa_atexit,
 "This value is true if the target wants destructors to be queued to be\n\
run from __cxa_atexit.  If this is the case then, for each priority level,\n\
a new constructor will be entered that registers the destructors for that\n\
level with __cxa_atexit (and there will be no destructors emitted).\n\
It is false the method implied by @code{have_ctors_dtors} is used.",
 bool, false)

/* True if thread-local storage is supported.  */
DEFHOOKPOD
(have_tls,
 "Contains the value true if the target supports thread-local storage.\n\
The default value is false.",
 bool, false)

/* True if a small readonly data section is supported.  */
DEFHOOKPOD
(have_srodata_section,
 "Contains the value true if the target places read-only\n\
``small data'' into a separate section.  The default value is false.",
 bool, false)

/* True if EH frame info sections should be zero-terminated.  */
DEFHOOKPOD
(terminate_dw2_eh_frame_info,
 "Contains the value true if the target should add a zero word onto the\n\
end of a Dwarf-2 frame info section when used for exception handling.\n\
Default value is false if @code{EH_FRAME_SECTION_NAME} is defined, and\n\
true otherwise.",
 bool, true)

/* True if #NO_APP should be emitted at the beginning of assembly output.  */
DEFHOOKPOD
(asm_file_start_app_off,
 "If this flag is true, the text of the macro @code{ASM_APP_OFF} will be\n\
printed as the very first line in the assembly file, unless\n\
@option{-fverbose-asm} is in effect.  (If that macro has been defined\n\
to the empty string, this variable has no effect.)  With the normal\n\
definition of @code{ASM_APP_OFF}, the effect is to notify the GNU\n\
assembler that it need not bother stripping comments or extra\n\
whitespace from its input.  This allows it to work a bit faster.\n\
\n\
The default is false.  You should not set it to true unless you have\n\
verified that your port does not generate any extra whitespace or\n\
comments that will cause GAS to issue errors in NO_APP mode.",
 bool, false)

/* True if output_file_directive should be called for main_input_filename
   at the beginning of assembly output.  */
DEFHOOKPOD
(asm_file_start_file_directive,
 "If this flag is true, @code{output_file_directive} will be called\n\
for the primary source file, immediately after printing\n\
@code{ASM_APP_OFF} (if that is enabled).  Most ELF assemblers expect\n\
this to be done.  The default is false.",
 bool, false)

/* Returns true if we should generate exception tables for use with the
   ARM EABI.  The effects the encoding of function exception specifications.  */
DEFHOOKPOD
(arm_eabi_unwinder,
 "This flag should be set to @code{true} on targets that use an ARM EABI\n\
based unwinding library, and @code{false} on other targets.  This effects\n\
the format of unwinding tables, and how the unwinder in entered after\n\
running a cleanup.  The default is @code{false}.",
 bool, false)

DEFHOOKPOD
(want_debug_pub_sections,
 "True if the @code{.debug_pubtypes} and @code{.debug_pubnames} sections\n\
should be emitted.  These sections are not used on most platforms, and\n\
in particular GDB does not use them.",
 bool, false)

DEFHOOKPOD
(delay_sched2,
 "True if sched2 is not to be run at its normal place.\n\
This usually means it will be run as part of machine-specific reorg.",
bool, false)

DEFHOOKPOD
(delay_vartrack,
 "True if vartrack is not to be run at its normal place.\n\
This usually means it will be run as part of machine-specific reorg.",
bool, false)

DEFHOOKPOD
(no_register_allocation,
 "True if register allocation and the passes\n\
following it should not be run.  Usually true only for virtual assembler\n\
targets.",
bool, false)

/* Leave the boolean fields at the end.  */

/* Functions related to mode switching.  */
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_MODE_"
HOOK_VECTOR (TARGET_TOGGLE_, mode_switching)

DEFHOOK
(emit,
 "Generate one or more insns to set @var{entity} to @var{mode}.\n\
@var{hard_reg_live} is the set of hard registers live at the point where\n\
the insn(s) are to be inserted. @var{prev_moxde} indicates the mode\n\
to switch from, or is the number of modes if the previous mode is not\n\
known.  Sets of a lower numbered entity will be emitted before\n\
sets of a higher numbered entity to a mode of the same or lower priority.",
 void, (int entity, int mode, int prev_mode, HARD_REG_SET regs_live), NULL)

DEFHOOK
(needed,
 "@var{entity} is an integer specifying a mode-switched entity.\n\
If @code{OPTIMIZE_MODE_SWITCHING} is defined, you must define this hook\n\
to return the mode that @var{entity} must be switched into prior to the\n\
execution of @var{insn}, or the number of modes if @var{insn} has no\n\
such requirement.  @var{regs_live} contains the set of hard registers\n\
that are live before @var{insn}.",
 int, (int entity, rtx_insn *insn, HARD_REG_SET regs_live), NULL)

DEFHOOK
(after,
 "@var{entity} is an integer specifying a mode-switched entity.\n\
If this hook is defined, it is evaluated for every @var{insn} during mode\n\
switching.  It returns the mode that @var{entity} is in after @var{insn}\n\
has been executed.  @var{mode} is the mode that @var{entity} was in\n\
before @var{insn} was executed, taking account of @var{TARGET_MODE_NEEDED}.\n\
@var{regs_live} is the set of hard registers that are live after @var{insn}\n\
has been executed.\n\
\n\
@var{mode} is equal to the number of modes defined for @var{entity}\n\
if the mode before @var{insn} is unknown.  The hook should likewise return\n\
the number of modes if it does not know what mode @var{entity} has after\n\
@var{insn}.\n\
\n\
Not defining the hook is equivalent to returning @var{mode}.",
 int, (int entity, int mode, rtx_insn *insn, HARD_REG_SET regs_live), NULL)

DEFHOOK
(confluence,
 "By default, the mode-switching pass assumes that a given entity's modes\n\
are mutually exclusive.  This means that the pass can only tell\n\
@code{TARGET_MODE_EMIT} about an entity's previous mode if all\n\
incoming paths of execution leave the entity in the same state.\n\
\n\
However, some entities might have overlapping, non-exclusive modes,\n\
so that it is sometimes possible to represent ``mode @var{mode1} or mode\n\
@var{mode2}'' with something more specific than ``mode not known''.\n\
If this is true for at least one entity, you should define this hook\n\
and make it return a mode that includes @var{mode1} and @var{mode2}\n\
as possibilities.  (The mode can include other possibilities too.)\n\
The hook should return the number of modes if no suitable mode exists\n\
for the given arguments.",
 int, (int entity, int mode1, int mode2), NULL)

DEFHOOK
(backprop,
 "If defined, the mode-switching pass uses this hook to back-propagate mode\n\
requirements through blocks that have no mode requirements of their own.\n\
Specifically, @var{mode1} is the mode that @var{entity} has on exit\n\
from a block B1 (say) and @var{mode2} is the mode that the next block\n\
requires @var{entity} to have.  B1 does not have any mode requirements\n\
of its own.\n\
\n\
The hook should return the mode that it prefers or requires @var{entity}\n\
to have in B1, or the number of modes if there is no such requirement.\n\
If the hook returns a required mode for more than one of B1's outgoing\n\
edges, those modes are combined as for @code{TARGET_MODE_CONFLUENCE}.\n\
\n\
For example, suppose there is a ``one-shot'' entity that,\n\
for a given execution of a function, either stays off or makes exactly\n\
one transition from off to on.  It is safe to make the transition at any\n\
time, but it is better not to do so unnecessarily.  This hook allows the\n\
function to manage such an entity without having to track its state at\n\
runtime.  Specifically. the entity would have two modes, 0 for off and\n\
1 for on, with 2 representing ``don't know''.  The system is forbidden from\n\
transitioning from 2 to 1, since 2 represents the possibility that the\n\
entity is already on (and the aim is to avoid having to emit code to\n\
check for that case).  This hook would therefore return 1 when @var{mode1}\n\
is 2 and @var{mode2} is 1, which would force the entity to be on in the\n\
source block.  Applying this inductively would remove all transitions\n\
in which the previous state is unknown.",
 int, (int entity, int mode1, int mode2), NULL)

DEFHOOK
(entry,
 "If this hook is defined, it is evaluated for every @var{entity} that\n\
needs mode switching.  It should return the mode that @var{entity} is\n\
guaranteed to be in on entry to the function, or the number of modes\n\
if there is no such guarantee.\n\
If @code{TARGET_MODE_ENTRY} is defined then @code{TARGET_MODE_EXIT}\n\
must be defined.",
 int, (int entity), NULL)

DEFHOOK
(exit,
 "If this hook is defined, it is evaluated for every @var{entity} that\n\
needs mode switching.  It should return the mode that @var{entity} must\n\
be in on return from the function, or the number of modes if there is no\n\
such requirement.\n\
If @code{TARGET_MODE_EXIT} is defined then @code{TARGET_MODE_ENTRY}\n\
must be defined.",
 int, (int entity), NULL)

DEFHOOK
(eh_handler,
 "If this hook is defined, it should return the mode that @var{entity} is\n\
guaranteed to be in on entry to an exception handler, or the number of modes\n\
if there is no such guarantee.",
 int, (int entity), NULL)

DEFHOOK
(priority,
 "This hook specifies the order in which modes for @var{entity}\n\
are processed. 0 is the highest priority,\n\
@code{NUM_MODES_FOR_MODE_SWITCHING[@var{entity}] - 1} the lowest.\n\
The hook returns an integer designating a mode\n\
for @var{entity}.  For any fixed @var{entity}, @code{mode_priority}\n\
(@var{entity}, @var{n}) shall be a bijection in 0 @dots{}\n\
@code{num_modes_for_mode_switching[@var{entity}] - 1}.",
 int, (int entity, int n), NULL)

HOOK_VECTOR_END (mode_switching)

#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_MEMTAG_"
HOOK_VECTOR (TARGET_MEMTAG_, memtag)

DEFHOOK
(can_tag_addresses,
 "True if the backend architecture naturally supports ignoring some region\n\
of pointers.  This feature means that @option{-fsanitize=hwaddress} can\n\
work.\n\
\n\
At preset, this feature does not support address spaces.  It also requires\n\
@code{Pmode} to be the same as @code{ptr_mode}.",
 bool, (), default_memtag_can_tag_addresses)

DEFHOOK
(tag_size,
 "Return the size of a tag (in bits) for this platform.\n\
\n\
The default returns 8.",
  uint8_t, (), default_memtag_tag_size)

DEFHOOK
(granule_size,
 "Return the size in real memory that each byte in shadow memory refers to.\n\
I.e. if a variable is @var{X} bytes long in memory, then this hook should\n\
return the value @var{Y} such that the tag in shadow memory spans\n\
@var{X}/@var{Y} bytes.\n\
\n\
Most variables will need to be aligned to this amount since two variables\n\
that are neighbors in memory and share a tag granule would need to share\n\
the same tag.\n\
\n\
The default returns 16.",
  uint8_t, (), default_memtag_granule_size)

DEFHOOK
(insert_random_tag,
 "Return an RTX representing the value of @var{untagged} but with a\n\
(possibly) random tag in it.\n\
Put that value into @var{target} if it is convenient to do so.\n\
This function is used to generate a tagged base for the current stack frame.",
  rtx, (rtx untagged, rtx target), default_memtag_insert_random_tag)

DEFHOOK
(add_tag,
 "Return an RTX that represents the result of adding @var{addr_offset} to\n\
the address in pointer @var{base} and @var{tag_offset} to the tag in pointer\n\
@var{base}.\n\
The resulting RTX must either be a valid memory address or be able to get\n\
put into an operand with @code{force_operand}.\n\
\n\
Unlike other memtag hooks, this must return an expression and not emit any\n\
RTL.",
  rtx, (rtx base, poly_int64 addr_offset, uint8_t tag_offset),
  default_memtag_add_tag)

DEFHOOK
(set_tag,
 "Return an RTX representing @var{untagged_base} but with the tag @var{tag}.\n\
Try and store this in @var{target} if convenient.\n\
@var{untagged_base} is required to have a zero tag when this hook is called.\n\
The default of this hook is to set the top byte of @var{untagged_base} to\n\
@var{tag}.",
  rtx, (rtx untagged_base, rtx tag, rtx target), default_memtag_set_tag)

DEFHOOK
(extract_tag,
 "Return an RTX representing the tag stored in @var{tagged_pointer}.\n\
Store the result in @var{target} if it is convenient.\n\
The default represents the top byte of the original pointer.",
  rtx, (rtx tagged_pointer, rtx target), default_memtag_extract_tag)

DEFHOOK
(untagged_pointer,
 "Return an RTX representing @var{tagged_pointer} with its tag set to zero.\n\
Store the result in @var{target} if convenient.\n\
The default clears the top byte of the original pointer.",
  rtx, (rtx tagged_pointer, rtx target), default_memtag_untagged_pointer)

HOOK_VECTOR_END (memtag)
#undef HOOK_PREFIX
#define HOOK_PREFIX "TARGET_"

#define DEF_TARGET_INSN(NAME, PROTO) \
  DEFHOOK_UNDOC (have_##NAME, "", bool, (void), false)
#include "target-insns.def"
#undef DEF_TARGET_INSN

#define DEF_TARGET_INSN(NAME, PROTO) \
  DEFHOOK_UNDOC (gen_##NAME, "", rtx_insn *, PROTO, NULL)
#include "target-insns.def"
#undef DEF_TARGET_INSN

#define DEF_TARGET_INSN(NAME, PROTO) \
  DEFHOOKPOD (code_for_##NAME, "*", enum insn_code, CODE_FOR_nothing)
#include "target-insns.def"
#undef DEF_TARGET_INSN

DEFHOOK
(run_target_selftests,
 "If selftests are enabled, run any selftests for this target.",
 void, (void),
 NULL)

/* This value represents whether the shadow call stack is implemented on
   the target platform.  */
DEFHOOKPOD
(have_shadow_call_stack,
 "This value is true if the target platform supports\n\
@option{-fsanitize=shadow-call-stack}.  The default value is false.",
 bool, false)

/* This value represents whether libatomic is available on
   the target platform.  */
DEFHOOKPOD
(have_libatomic,
 "This value is true if the target platform supports\n\
libatomic.  The default value is false.",
 bool, false)

/* This value represents whether libatomic is available on
   the target platform.  */
DEFHOOKPOD
(documentation_name,
 "If non-NULL, this value is a string used for locating target-specific\
 documentation for this target.\n\
The default value is NULL.",
 const char *, NULL)

/* Close the 'struct gcc_target' definition.  */
HOOK_VECTOR_END (C90_EMPTY_HACK)

