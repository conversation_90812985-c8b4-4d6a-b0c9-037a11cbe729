.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_list_verify" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_list_verify \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_list_verify(const gnutls_x509_crt_t * " cert_list ", unsigned " cert_list_length ", const gnutls_x509_crt_t * " CA_list ", unsigned " CA_list_length ", const gnutls_x509_crl_t * " CRL_list ", unsigned " CRL_list_length ", unsigned int " flags ", unsigned int * " verify ");"
.SH ARGUMENTS
.IP "const gnutls_x509_crt_t * cert_list" 12
is the certificate list to be verified
.IP "unsigned cert_list_length" 12
holds the number of certificate in cert_list
.IP "const gnutls_x509_crt_t * CA_list" 12
is the CA list which will be used in verification
.IP "unsigned CA_list_length" 12
holds the number of CA certificate in CA_list
.IP "const gnutls_x509_crl_t * CRL_list" 12
holds a list of CRLs.
.IP "unsigned CRL_list_length" 12
the length of CRL list.
.IP "unsigned int flags" 12
Flags that may be used to change the verification algorithm. Use OR of the gnutls_certificate_verify_flags enumerations.
.IP "unsigned int * verify" 12
will hold the certificate verification output.
.SH "DESCRIPTION"

This function will try to verify the given certificate list and
return its status. The details of the verification are the same
as in \fBgnutls_x509_trust_list_verify_crt2()\fP.

You must check the peer's name in order to check if the verified
certificate belongs to the actual peer.

The certificate verification output will be put in  \fIverify\fP and will
be one or more of the gnutls_certificate_status_t enumerated
elements bitwise or'd.  For a more detailed verification status use
\fBgnutls_x509_crt_verify()\fP per list element.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
