<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>p11-kit: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="tools.html" title="Manual Pages">
<link rel="prev" href="tools.html" title="Manual Pages">
<link rel="next" href="pkcs11-conf.html" title="pkcs11.conf">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="tools.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="tools.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="pkcs11-conf.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="refentry">
<a name="p11-kit"></a><div class="titlepage"></div>
<div class="refnamediv"><table width="100%"><tr>
<td valign="top">
<h2><span class="refentrytitle">p11-kit</span></h2>
<p>p11-kit — Tool for operating on configured PKCS#11 modules</p>
</td>
<td class="gallery_image" valign="top" align="right"></td>
</tr></table></div>
<div class="refsynopsisdiv">
<h2>Synopsis</h2>
<div class="cmdsynopsis"><p><code class="command">p11-kit list-modules</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit list-tokens ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit list-objects ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit import-object ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit export-object ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit delete-object ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit generate-keypair ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit list-profiles ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit add-profile ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit delete-profile ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit list-mechanisms ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit print-config</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit extract ...</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">p11-kit server ...</code> </p></div>
</div>
<div class="refsect1">
<a name="p11-kit-description"></a><h2>Description</h2>
<p><span class="command"><strong>p11-kit</strong></span> is a command line tool that
	can be used to perform operations on PKCS#11 modules configured on the
	system.</p>
<p>See the various sub commands below. The following global options
	can be used:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">-v, --verbose</code></span></p></td>
<td><p>Run in verbose mode with debug
			output.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">-q, --quiet</code></span></p></td>
<td><p>Run in quiet mode without warning or
			failure messages.</p></td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-list-modules"></a><h2>List Modules</h2>
<p>List system configured PKCS#11 modules.</p>
<pre class="programlisting">
$ p11-kit list-modules
</pre>
<p>The modules, information about them and the tokens present in
	the PKCS#11 modules will be displayed.</p>
</div>
<div class="refsect1">
<a name="p11-kit-list-tokens"></a><h2>List Tokens</h2>
<p>List all tokens available in system configured PKCS#11 modules.</p>
<pre class="programlisting">
$ p11-kit list-tokens pkcs11:token
</pre>
<p>This retrieves all tokens and displays some of their
	common attributes.</p>
<p>This subcommand takes the following options:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--only-uris</code></span></p></td>
<td><p>Print only the matching token URIs.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--provider=&lt;module_path&gt;</code></span></p></td>
<td>
<p>Load only the given PKCS#11 module instead of enumerating modules installed on the system. If <em class="parameter"><code>module_path</code></em> is a relative path, the module is loaded from the default module path. This path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-list-objects"></a><h2>List Objects</h2>
<p>List objects matching given PKCS#11 URI.</p>
<pre class="programlisting">
$ p11-kit list-objects pkcs11:object_on_token
</pre>
<p>This retrieves all objects that match given PKCS#11 URI and displays
        some of their common attributes. Storage objects also have their PKCS#11 URI
        displayed.</p>
<p>This subcommand takes the following options:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--login</code></span></p></td>
<td><p>Authenticate to the token before enumerating objects. The PIN value is read from either the <code class="literal">pin-value</code> attribute in the URI or from the terminal.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--provider=&lt;module_path&gt;</code></span></p></td>
<td>
<p>Load only the given PKCS#11 module instead of enumerating modules installed on the system. If <em class="parameter"><code>module_path</code></em> is a relative path, the module is loaded from the default module path. This path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-import-object"></a><h2>Import Object</h2>
<p>Import object into PKCS#11 token.</p>
<pre class="programlisting">
$ p11-kit import-object --file=file.pem [--label=label] [--id=object_id] pkcs11:token
</pre>
<p>Takes either an X.509 certificate or a public key in the form of a PEM file
	and imports it into the first token matched by given PKCS#11 URI.</p>
<p>The following options are required:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="option">--file=&lt;file.pem&gt;</code></span></p></td>
<td><p>File containing either an X.509 certificate
			or a public key in PEM format.</p></td>
</tr></tbody>
</table></div>
<p>This subcommand also accepts the following options:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--label=&lt;label&gt;</code></span></p></td>
<td><p>Assigns label to the imported object.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--id=&lt;object_id&gt;</code></span></p></td>
<td><p>Assigns ID to the imported object. The ID should be specified in hexadecimal format without '0x' prefix.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--login</code></span></p></td>
<td><p>Authenticate to the token before enumerating objects. The PIN value is read from either the <code class="literal">pin-value</code> attribute in the URI or from the terminal.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--provider=&lt;module_path&gt;</code></span></p></td>
<td>
<p>Load only the given PKCS#11 module instead of enumerating modules installed on the system. If <em class="parameter"><code>module_path</code></em> is a relative path, the module is loaded from the default module path. This path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-export-object"></a><h2>Export Object</h2>
<p>Export object matching PKCS#11 URI.</p>
<pre class="programlisting">
$ p11-kit export-object pkcs11:object_on_token
</pre>
<p>Displays PEM formatted contents of the first object matched by given PKCS#11 URI.
	The matched object has to either be an X.509 certificate or a public key.</p>
<p>This subcommand takes the following options:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--login</code></span></p></td>
<td><p>Authenticate to the token before enumerating objects. The PIN value is read from either the <code class="literal">pin-value</code> attribute in the URI or from the terminal.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--provider=&lt;module_path&gt;</code></span></p></td>
<td>
<p>Load only the given PKCS#11 module instead of enumerating modules installed on the system. If <em class="parameter"><code>module_path</code></em> is a relative path, the module is loaded from the default module path. This path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-delete-object"></a><h2>Delete Object</h2>
<p>Delete object matching PKCS#11 URI.</p>
<pre class="programlisting">
$ p11-kit delete-object pkcs11:object_on_token
</pre>
<p>Destroys the first object matched by given PKCS#11 URI.</p>
<p>This subcommand takes the following options:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--login</code></span></p></td>
<td><p>Authenticate to the token before enumerating objects. The PIN value is read from either the <code class="literal">pin-value</code> attribute in the URI or from the terminal.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--provider=&lt;module_path&gt;</code></span></p></td>
<td>
<p>Load only the given PKCS#11 module instead of enumerating modules installed on the system. If <em class="parameter"><code>module_path</code></em> is a relative path, the module is loaded from the default module path. This path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-generate-keypair"></a><h2>Generate Key-pair</h2>
<p>Generate key-pair on a PKCS#11 token.</p>
<pre class="programlisting">
$ p11-kit generate-keypair --type=algorithm {--bits=n|--curve=name} [--label=label] [--id=object_id] pkcs11:token
</pre>
<p>Generate private-public key-pair of given type on the first
	token matched by given PKCS#11 URI. This command should be used
	together with --type option and one of --bits or --curve options.</p>
<p>The following options are required:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--type=&lt;algorithm&gt;</code></span></p></td>
<td><p>Specify the type of keys to generate.
			Supported values are rsa, ecdsa and eddsa.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--bits=&lt;n&gt;</code></span></p></td>
<td><p>Specify the number of bits for the key-pair generation.
			Cannot be used together with --curve option.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--curve=&lt;name&gt;</code></span></p></td>
<td><p>Specify an elliptic curve for the key-pair generation.
                        Supported values are secp256r1, secp384r1, secp521r1, ed25519 and ed448.
			Cannot be used together with --bits option.</p></td>
</tr>
</tbody>
</table></div>
<p>This subcommand also accepts the following options:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--label=&lt;label&gt;</code></span></p></td>
<td><p>Assigns label to the generated key-pair objects.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--id=&lt;object_id&gt;</code></span></p></td>
<td><p>Assigns ID to the generated key-pair objects. The ID should be specified in hexadecimal format without '0x' prefix.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--login</code></span></p></td>
<td><p>Authenticate to the token before enumerating objects. The PIN value is read from either the <code class="literal">pin-value</code> attribute in the URI or from the terminal.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--provider=&lt;module_path&gt;</code></span></p></td>
<td>
<p>Load only the given PKCS#11 module instead of enumerating modules installed on the system. If <em class="parameter"><code>module_path</code></em> is a relative path, the module is loaded from the default module path. This path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-list-profiles"></a><h2>List Profiles</h2>
<p>List PKCS#11 profiles supported by the token.</p>
<pre class="programlisting">
$ p11-kit list-profiles pkcs11:token
</pre>
<p>Displays profile IDs of the first token matched by given
	PKCS#11 URI in human-readable form.</p>
<p>This subcommand takes the following options:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--login</code></span></p></td>
<td><p>Authenticate to the token before enumerating objects. The PIN value is read from either the <code class="literal">pin-value</code> attribute in the URI or from the terminal.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--provider=&lt;module_path&gt;</code></span></p></td>
<td>
<p>Load only the given PKCS#11 module instead of enumerating modules installed on the system. If <em class="parameter"><code>module_path</code></em> is a relative path, the module is loaded from the default module path. This path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-add-profile"></a><h2>Add Profile</h2>
<p>Add PKCS#11 profile to a token.</p>
<pre class="programlisting">
$ p11-kit add-profile --profile=profile pkcs11:token
</pre>
<p>Creates new profile object with given profile ID on the
	first token matched by given PKCS#11 URI. This command fails
	if the profile ID already exists on the token.</p>
<p>The following options are required:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="option">--profile=&lt;profile&gt;</code></span></p></td>
<td><p>Profile ID to add. Value can either
			be numerical or textual.</p></td>
</tr></tbody>
</table></div>
<p>This subcommand also accepts the following options:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--login</code></span></p></td>
<td><p>Authenticate to the token before enumerating objects. The PIN value is read from either the <code class="literal">pin-value</code> attribute in the URI or from the terminal.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--provider=&lt;module_path&gt;</code></span></p></td>
<td>
<p>Load only the given PKCS#11 module instead of enumerating modules installed on the system. If <em class="parameter"><code>module_path</code></em> is a relative path, the module is loaded from the default module path. This path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-delete-profile"></a><h2>Delete Profile</h2>
<p>Delete PKCS#11 profile from a token.</p>
<pre class="programlisting">
$ p11-kit delete-profile --profile=profile pkcs11:token
</pre>
<p>Destroys all profile objects with given profile ID from
	the first token matched by given PKCS#11 URI.</p>
<p>The following options are required:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="option">--profile=&lt;profile&gt;</code></span></p></td>
<td><p>Profile ID to delete. Value can either
			be numerical or textual.</p></td>
</tr></tbody>
</table></div>
<p>This subcommand also accepts the following options:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--login</code></span></p></td>
<td><p>Authenticate to the token before enumerating objects. The PIN value is read from either the <code class="literal">pin-value</code> attribute in the URI or from the terminal.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--provider=&lt;module_path&gt;</code></span></p></td>
<td>
<p>Load only the given PKCS#11 module instead of enumerating modules installed on the system. If <em class="parameter"><code>module_path</code></em> is a relative path, the module is loaded from the default module path. This path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-list-mechanisms"></a><h2>List Mechanisms</h2>
<p>List PKCS#11 mechanisms supported by the token.</p>
<pre class="programlisting">
$ p11-kit list-mechanisms pkcs11:token
</pre>
<p>This lists all available mechanimsms for a PKCS#11 token</p>
</div>
<div class="refsect1">
<a name="p11-kit-print-config"></a><h2>Print Config</h2>
<p>Print merged configuration.</p>
<pre class="programlisting">
$ p11-kit print-config
</pre>
<p>P11-kit provides 3 levels of configuration: system wide, per-module, and per-user.
	At run-time all 3 levels are merged into a single configuration. This command displays the
	merged configuration.</p>
</div>
<div class="refsect1">
<a name="p11-kit-extract"></a><h2>Extract</h2>
<p>Extract certificates from configured PKCS#11 modules.</p>
<p>This operation has been moved to a separate command <span class="command"><strong>trust extract</strong></span>.
	See </p>
<table border="0" summary="Simple list" class="simplelist"><tr><td><span class="citerefentry"><span class="refentrytitle">trust</span>(1)</span></td></tr></table>
<p>
	for more information</p>
</div>
<div class="refsect1">
<a name="p11-kit-server"></a><h2>Server</h2>
<p>Run a server process that exposes PKCS#11 module remotely.</p>
<pre class="programlisting">
$ p11-kit server pkcs11:token1 pkcs11:token2 ...
$ p11-kit server --provider /path/to/pkcs11-module.so pkcs11:token1 pkcs11:token2 ...
</pre>
<p>This launches a server that exposes the given PKCS#11 tokens on a local socket. The tokens must belong to the same module. To access the socket, use <code class="literal">p11-kit-client.so</code> module. The server address and PID are printed as a shell-script snippet which sets the appropriate environment variable: <code class="literal">P11_KIT_SERVER_ADDRESS</code> and <code class="literal">P11_KIT_SERVER_PID</code>.</p>
</div>
<div class="refsect1">
<a name="p11-kit-extract-trust"></a><h2>Extract Trust</h2>
<p>Extract standard trust information files.</p>
<p>This operation has been moved to a separate command <span class="command"><strong>trust extract-compat</strong></span>.
	See <span class="citerefentry"><span class="refentrytitle">trust</span>(1)</span>
	for more information</p>
</div>
<div class="refsect1">
<a name="p11-kit-remote"></a><h2>Remote</h2>
<p>Run a PKCS#11 module remotely.</p>
<pre class="programlisting">
$ p11-kit remote /path/to/pkcs11-module.so
$ p11-kit remote pkcs11:token1 pkcs11:token2 ...
</pre>
<p>This is not meant to be run directly from a terminal. But rather in a
	<code class="option">remote</code> option in a
	<span class="citerefentry"><span class="refentrytitle">pkcs11.conf</span>(5)</span>
	file.</p>
<p>This exposes the given PKCS#11 module or tokens over standard input and output. Those two forms, whether to expose a module or tokens, are mutually exclusive and if the second form is used, the tokens must belong to the same module.</p>
</div>
<div class="refsect1">
<a name="p11-kit-bugs"></a><h2>Bugs</h2>
<p>
    Please send bug reports to either the distribution bug tracker
    or the upstream bug tracker at
    <a class="ulink" href="https://github.com/p11-glue/p11-kit/issues/" target="_top">https://github.com/p11-glue/p11-kit/issues/</a>.
  </p>
</div>
<div class="refsect1">
<a name="p11-kit-see-also"></a><h2>See also</h2>
<span class="simplelist"><span class="citerefentry"><span class="refentrytitle">pkcs11.conf</span>(5)</span></span><p>
    Further details available in the p11-kit online documentation at
    <a class="ulink" href="https://p11-glue.github.io/p11-glue/p11-kit/manual/" target="_top">https://p11-glue.github.io/p11-glue/p11-kit/manual/</a>.
  </p>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>