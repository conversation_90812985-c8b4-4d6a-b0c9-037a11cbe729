------------------------------------------------------------------------
-- ddNextMinus.decTest -- decDouble next that is less [754r nextdown] --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decDoubles.
precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

ddnextm001 nextminus  0.9999999999999995 ->   0.9999999999999994
ddnextm002 nextminus  0.9999999999999996 ->   0.9999999999999995
ddnextm003 nextminus  0.9999999999999997 ->   0.9999999999999996
ddnextm004 nextminus  0.9999999999999998 ->   0.9999999999999997
ddnextm005 nextminus  0.9999999999999999 ->   0.9999999999999998
ddnextm006 nextminus  1.000000000000000  ->   0.9999999999999999
ddnextm007 nextminus  1.0         ->   0.9999999999999999
ddnextm008 nextminus  1           ->   0.9999999999999999
ddnextm009 nextminus  1.000000000000001  ->   1.000000000000000
ddnextm010 nextminus  1.000000000000002  ->   1.000000000000001
ddnextm011 nextminus  1.000000000000003  ->   1.000000000000002
ddnextm012 nextminus  1.000000000000004  ->   1.000000000000003
ddnextm013 nextminus  1.000000000000005  ->   1.000000000000004
ddnextm014 nextminus  1.000000000000006  ->   1.000000000000005
ddnextm015 nextminus  1.000000000000007  ->   1.000000000000006
ddnextm016 nextminus  1.000000000000008  ->   1.000000000000007
ddnextm017 nextminus  1.000000000000009  ->   1.000000000000008
ddnextm018 nextminus  1.000000000000010  ->   1.000000000000009
ddnextm019 nextminus  1.000000000000011  ->   1.000000000000010
ddnextm020 nextminus  1.000000000000012  ->   1.000000000000011

ddnextm021 nextminus -0.9999999999999995 ->  -0.9999999999999996
ddnextm022 nextminus -0.9999999999999996 ->  -0.9999999999999997
ddnextm023 nextminus -0.9999999999999997 ->  -0.9999999999999998
ddnextm024 nextminus -0.9999999999999998 ->  -0.9999999999999999
ddnextm025 nextminus -0.9999999999999999 ->  -1.000000000000000
ddnextm026 nextminus -1.000000000000000  ->  -1.000000000000001
ddnextm027 nextminus -1.0         ->  -1.000000000000001
ddnextm028 nextminus -1           ->  -1.000000000000001
ddnextm029 nextminus -1.000000000000001  ->  -1.000000000000002
ddnextm030 nextminus -1.000000000000002  ->  -1.000000000000003
ddnextm031 nextminus -1.000000000000003  ->  -1.000000000000004
ddnextm032 nextminus -1.000000000000004  ->  -1.000000000000005
ddnextm033 nextminus -1.000000000000005  ->  -1.000000000000006
ddnextm034 nextminus -1.000000000000006  ->  -1.000000000000007
ddnextm035 nextminus -1.000000000000007  ->  -1.000000000000008
ddnextm036 nextminus -1.000000000000008  ->  -1.000000000000009
ddnextm037 nextminus -1.000000000000009  ->  -1.000000000000010
ddnextm038 nextminus -1.000000000000010  ->  -1.000000000000011
ddnextm039 nextminus -1.000000000000011  ->  -1.000000000000012

-- ultra-tiny inputs
ddnextm062 nextminus  1E-398         ->   0E-398
ddnextm065 nextminus -1E-398         ->  -2E-398

-- Zeros
ddnextm100 nextminus -0           -> -1E-398
ddnextm101 nextminus  0           -> -1E-398
ddnextm102 nextminus  0.00        -> -1E-398
ddnextm103 nextminus -0.00        -> -1E-398
ddnextm104 nextminus  0E-300      -> -1E-398
ddnextm105 nextminus  0E+300      -> -1E-398
ddnextm106 nextminus  0E+30000    -> -1E-398
ddnextm107 nextminus -0E+30000    -> -1E-398

-- specials
ddnextm150 nextminus   Inf    ->  9.999999999999999E+384
ddnextm151 nextminus  -Inf    -> -Infinity
ddnextm152 nextminus   NaN    ->  NaN
ddnextm153 nextminus  sNaN    ->  NaN   Invalid_operation
ddnextm154 nextminus   NaN77  ->  NaN77
ddnextm155 nextminus  sNaN88  ->  NaN88 Invalid_operation
ddnextm156 nextminus  -NaN    -> -NaN
ddnextm157 nextminus -sNaN    -> -NaN   Invalid_operation
ddnextm158 nextminus  -NaN77  -> -NaN77
ddnextm159 nextminus -sNaN88  -> -NaN88 Invalid_operation

-- Nmax, Nmin, Ntiny, subnormals
ddnextm170 nextminus  9.999999999999999E+384   -> 9.999999999999998E+384
ddnextm171 nextminus  9.999999999999998E+384   -> 9.999999999999997E+384
ddnextm172 nextminus  1E-383                   -> 9.99999999999999E-384
ddnextm173 nextminus  1.000000000000000E-383   -> 9.99999999999999E-384
ddnextm174 nextminus  9E-398                   -> 8E-398
ddnextm175 nextminus  9.9E-397                 -> 9.8E-397
ddnextm176 nextminus  9.99999999999E-387       -> 9.99999999998E-387
ddnextm177 nextminus  9.99999999999999E-384    -> 9.99999999999998E-384
ddnextm178 nextminus  9.99999999999998E-384    -> 9.99999999999997E-384
ddnextm179 nextminus  9.99999999999997E-384    -> 9.99999999999996E-384
ddnextm180 nextminus  0E-398                   -> -1E-398
ddnextm181 nextminus  1E-398                   -> 0E-398
ddnextm182 nextminus  2E-398                   -> 1E-398

ddnextm183 nextminus  -0E-398                  -> -1E-398
ddnextm184 nextminus  -1E-398                  -> -2E-398
ddnextm185 nextminus  -2E-398                  -> -3E-398
ddnextm186 nextminus  -10E-398                 -> -1.1E-397
ddnextm187 nextminus  -100E-398                -> -1.01E-396
ddnextm188 nextminus  -100000E-398             -> -1.00001E-393
ddnextm189 nextminus  -1.00000000000E-383      -> -1.000000000000001E-383
ddnextm190 nextminus  -1.000000000000000E-383  -> -1.000000000000001E-383
ddnextm191 nextminus  -1E-383                  -> -1.000000000000001E-383
ddnextm192 nextminus  -9.999999999999998E+384  -> -9.999999999999999E+384
ddnextm193 nextminus  -9.999999999999999E+384  -> -Infinity

-- Null tests
ddnextm900 nextminus  # -> NaN Invalid_operation

