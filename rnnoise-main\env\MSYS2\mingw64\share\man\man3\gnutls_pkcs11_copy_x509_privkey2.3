.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_copy_x509_privkey2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_copy_x509_privkey2 \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_copy_x509_privkey2(const char * " token_url ", gnutls_x509_privkey_t " key ", const char * " label ", const gnutls_datum_t * " cid ", unsigned int " key_usage ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * token_url" 12
A PKCS \fB11\fP URL specifying a token
.IP "gnutls_x509_privkey_t key" 12
A private key
.IP "const char * label" 12
A name to be used for the stored data
.IP "const gnutls_datum_t * cid" 12
The CKA_ID to set for the object \-if NULL, the ID will be derived from the public key
.IP "unsigned int key_usage" 12
One of GNUTLS_KEY_*
.IP "unsigned int flags" 12
One of GNUTLS_PKCS11_OBJ_* flags
.SH "DESCRIPTION"
This function will copy a private key into a PKCS \fB11\fP token specified by
a URL.

Since 3.6.3 the objects are marked as sensitive by default unless
\fBGNUTLS_PKCS11_OBJ_FLAG_MARK_NOT_SENSITIVE\fP is specified.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
