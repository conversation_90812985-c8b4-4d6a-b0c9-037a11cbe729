cmake_minimum_required(VERSION 3.5)
project(rnnLib C)

# 设置包含目录
include_directories(../include)

# 收集源文件
set(RNN_SOURCES
    celt_lpc.c
    kiss_fft.c
    rnn_data.c
    rnn.c
    denoise.c
    pitch.c
)

# 创建静态库
add_library(rnnLib STATIC ${RNN_SOURCES})

# 创建训练数据生成程序
add_executable(denoise_training_gao
    denoise.c
    kiss_fft.c
    pitch.c
    celt_lpc.c
    rnn.c
    rnn_data.c
)

# 设置编译选项
target_compile_definitions(denoise_training_gao PRIVATE TRAINING=1)
target_link_libraries(denoise_training_gao m)