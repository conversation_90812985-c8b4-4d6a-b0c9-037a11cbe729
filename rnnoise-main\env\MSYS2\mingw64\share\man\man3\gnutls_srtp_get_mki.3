.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srtp_get_mki" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srtp_get_mki \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srtp_get_mki(gnutls_session_t " session ", gnutls_datum_t * " mki ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_datum_t * mki" 12
will hold the MKI
.SH "DESCRIPTION"
This function exports the negotiated Master Key Identifier,
received by the peer if any. The returned value in  \fImki\fP should be 
treated as constant and valid only during the session's lifetime.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.

Since 3.1.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
