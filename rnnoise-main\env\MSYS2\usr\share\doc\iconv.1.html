<!-- Creator     : groff version 1.22.3 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>ICONV</title>

</head>
<body>

<h1 align="center">ICONV</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#ENVIRONMENT">ENVIRONMENT</a><br>
<a href="#EXAMPLES">EXAMPLES</a><br>
<a href="#CONFORMING TO">CONFORMING TO</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">iconv &minus;
character set conversion</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">iconv
[<i>OPTION</i>...] [<b>&minus;f</b> <i>encoding</i>]
[<b>&minus;t</b> <i>encoding</i>] [<i>inputfile</i> ...]
<br>
iconv <b>&minus;l</b></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The
<b>iconv</b> program converts text from one encoding to
another encoding. More precisely, it converts <b>from</b>
the encoding given for the <b>&minus;f</b> option <b>to</b>
the encoding given for the <b>&minus;t</b> option. Either of
these encodings defaults to the encoding of the current
locale. All the <i>inputfile</i>s are read and converted in
turn; if no <i>inputfile</i> is given, the standard input is
used. The converted text is printed to standard output.</p>

<p style="margin-left:11%; margin-top: 1em">The encodings
permitted are system dependent. For the libiconv
implementation, they are listed in the iconv_open(3) manual
page.</p>

<p style="margin-left:11%; margin-top: 1em">Options
controlling the input and output format: <b><br>
&minus;f</b> <i>encoding</i>,
<b>&minus;&minus;from&minus;code=</b><i>encoding</i></p>

<p style="margin-left:22%;">Specifies the encoding of the
input.</p>

<p style="margin-left:11%;"><b>&minus;t</b>
<i>encoding</i>,
<b>&minus;&minus;to&minus;code=</b><i>encoding</i></p>

<p style="margin-left:22%;">Specifies the encoding of the
output.</p>

<p style="margin-left:11%; margin-top: 1em">Options
controlling conversion problems:</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>&minus;c</b></p></td>
<td width="8%"></td>
<td width="78%">


<p>When this option is given, characters that cannot be
converted are silently discarded, instead of leading to a
conversion error.</p></td></tr>
</table>


<p style="margin-left:11%;"><b>&minus;&minus;unicode&minus;subst=</b><i>formatstring</i></p>

<p style="margin-left:22%;">When this option is given,
Unicode characters that cannot be represented in the target
encoding are replaced with a placeholder string that is
constructed from the given <i>formatstring</i>, applied to
the Unicode code point. The <i>formatstring</i> must be a
format string in the same format as for the <i>printf</i>
command or the <i>printf()</i> function, taking either no
argument or exactly one unsigned integer argument.</p>


<p style="margin-left:11%;"><b>&minus;&minus;byte&minus;subst=</b><i>formatstring</i></p>

<p style="margin-left:22%;">When this option is given,
bytes in the input that are not valid in the source encoding
are replaced with a placeholder string that is constructed
from the given <i>formatstring</i>, applied to the
byte&rsquo;s value. The <i>formatstring</i> must be a format
string in the same format as for the <i>printf</i> command
or the <i>printf()</i> function, taking either no argument
or exactly one unsigned integer argument.</p>


<p style="margin-left:11%;"><b>&minus;&minus;widechar&minus;subst=</b><i>formatstring</i></p>

<p style="margin-left:22%;">When this option is given, wide
characters in the input that are not valid in the source
encoding are replaced with a placeholder string that is
constructed from the given <i>formatstring</i>, applied to
the byte&rsquo;s value. The <i>formatstring</i> must be a
format string in the same format as for the <i>printf</i>
command or the <i>printf()</i> function, taking either no
argument or exactly one unsigned integer argument.</p>

<p style="margin-left:11%; margin-top: 1em">Options
controlling error output: <b><br>
&minus;s</b>, <b>&minus;&minus;silent</b></p>

<p style="margin-left:22%;">When this option is given,
error messages about invalid or unconvertible characters are
omitted, but the actual converted text is unaffected.</p>

<p style="margin-left:11%; margin-top: 1em">The <b>iconv
&minus;l</b> or <b>iconv &minus;&minus;list</b> command
lists the names of the supported encodings, in a system
dependent format. For the libiconv implementation, the names
are printed in upper case, separated by whitespace, and
alias names of an encoding are listed on the same line as
the encoding itself.</p>

<h2>ENVIRONMENT
<a name="ENVIRONMENT"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Conversion
from/to EBCDIC encodings can be modified in a specific way:
If the environment variable <b>ICONV_EBCDIC_ZOS_UNIX</b> is
set to a non-empty value, the EBCDIC newline 0x15 will get
mapped to LF instead of NEL.</p>

<h2>EXAMPLES
<a name="EXAMPLES"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>iconv
&minus;f ISO&minus;8859&minus;1 &minus;t UTF&minus;8</b></p>

<p style="margin-left:22%;">converts input from the old
West-European encoding ISO&minus;8859&minus;1 to
Unicode.</p>

<p style="margin-left:11%; margin-top: 1em"><b>iconv
&minus;f KOI8&minus;R
&minus;&minus;byte&minus;subst=&quot;&lt;0x%x&gt;&quot; <br>

&minus;&minus;unicode&minus;subst=&quot;&lt;U+%04X&gt;&quot;</b></p>

<p style="margin-left:22%;">converts input from the old
Russian encoding KOI8&minus;R to the locale encoding,
substituting an angle bracket notation with hexadecimal
numbers for invalid bytes and for valid but unconvertible
characters.</p>

<p style="margin-left:11%;"><b>iconv
&minus;&minus;list</b></p>

<p style="margin-left:22%;">lists the supported
encodings.</p>

<h2>CONFORMING TO
<a name="CONFORMING TO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">POSIX:2024</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><b>iconv_open</b>(3),
<b>locale</b>(7)</p>
<hr>
</body>
</html>
