.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_proxy" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_proxy \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_proxy(gnutls_x509_crt_t " cert ", unsigned int * " critical ", int * " pathlen ", char ** " policyLanguage ", char ** " policy ", size_t * " sizeof_policy ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical
.IP "int * pathlen" 12
pointer to output integer indicating path length (may be
NULL), non\-negative error codes indicate a present pCPathLenConstraint
field and the actual value, \-1 indicate that the field is absent.
.IP "char ** policyLanguage" 12
output variable with OID of policy language
.IP "char ** policy" 12
output variable with policy data
.IP "size_t * sizeof_policy" 12
output variable size of policy data
.SH "DESCRIPTION"
This function will get information from a proxy certificate.  It
reads the ProxyCertInfo X.509 extension (*******.*******.14).
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
