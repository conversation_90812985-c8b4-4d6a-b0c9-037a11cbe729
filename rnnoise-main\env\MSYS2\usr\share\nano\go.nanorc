## Syntax highlighting for Go.

## Original author:  <PERSON>
## License:  CC0 (public domain)

syntax go "\.go$"
comment "//"

formatter gofmt -w

# Types.
color green "\<(bool|u?int(8|16|32|64)?|float(32|64)|complex(64|128)|byte|rune|uintptr|string|error)\>"
color green "\<(chan|const|func|interface|map|struct|type|var)\>"
color green "<-[[:blank:]]*chan\>|\<chan[[:blank:]]*<-"

# Predefined functions.
color blue "\<(append|cap|close|complex|copy|delete|imag|len|make|new|panic|print|println|real|recover)\>"

# Control structures.
color brightyellow "\<(case|default|defer|else|for|go|if|range|select|switch)\>"

# Control flow.
color magenta "\<(break|continue|fallthrough|goto|return)\>"

# Declarations.
color brightcyan "\<(package|import)\>"

# Literals.
color red "\<(true|false|nil|iota|_)\>"
color red "\<([1-9][0-9]*|0[0-7]*|0[xX][[:xdigit:]]+)\>"
color red "\<[0-9]+\.[0-9]*([eE][+-]?[0-9]+)?i?\>"
color red "\<[0-9]+[eE][+-]?[0-9]+i?\>"
color red "\B\.[0-9]+([eE][+-]?[0-9]+)?i?\>"
color red "\<[0-9]+i\>"

# Strings and characters; slightly fuzzy.
color red ""([^"\]|\\.)*"|'([^'\]|\\.)+'"
color red start=""([^"\]|\\.)*\\[[:blank:]]*$" end="^([^"\]|\\.)*""

# Comments.
color brightblue "//.*"
color brightblue start="/\*" end="\*/"

# Special comments.
color brightcyan "//[[:blank:]]*\+build[[:blank:]]+(([[:alnum:]_]+[[:blank:]]*)+,[[:blank:]]*)*[[:alnum:]_]+"

# Trailing whitespace.
color ,green "[[:space:]]+$"
