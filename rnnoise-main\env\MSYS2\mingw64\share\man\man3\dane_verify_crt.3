.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_verify_crt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_verify_crt \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "int dane_verify_crt(dane_state_t " s ", const gnutls_datum_t * " chain ", unsigned " chain_size ", gnutls_certificate_type_t " chain_type ", const char * " hostname ", const char * " proto ", unsigned int " port ", unsigned int " sflags ", unsigned int " vflags ", unsigned int * " verify ");"
.SH ARGUMENTS
.IP "dane_state_t s" 12
A DANE state structure (may be NULL)
.IP "const gnutls_datum_t * chain" 12
A certificate chain
.IP "unsigned chain_size" 12
The size of the chain
.IP "gnutls_certificate_type_t chain_type" 12
The type of the certificate chain
.IP "const char * hostname" 12
The hostname associated with the chain
.IP "const char * proto" 12
The protocol of the service connecting (e.g. tcp)
.IP "unsigned int port" 12
The port of the service connecting (e.g. 443)
.IP "unsigned int sflags" 12
Flags for the initialization of  \fIs\fP (if NULL)
.IP "unsigned int vflags" 12
Verification flags; an OR'ed list of \fBdane_verify_flags_t\fP.
.IP "unsigned int * verify" 12
An OR'ed list of \fBdane_verify_status_t\fP.
.SH "DESCRIPTION"
This function will verify the given certificate chain against the
CA constrains and/or the certificate available via DANE.
If no information via DANE can be obtained the flag \fBDANE_VERIFY_NO_DANE_INFO\fP
is set. If a DNSSEC signature is not available for the DANE
record then the verify flag \fBDANE_VERIFY_NO_DNSSEC_DATA\fP is set.

Due to the many possible options of DANE, there is no single threat
model countered. When notifying the user about DANE verification results
it may be better to mention: DANE verification did not reject the certificate,
rather than mentioning a successful DANE verification.

Note that this function is designed to be run in addition to
PKIX \- certificate chain \- verification. To be run independently
the \fBDANE_VFLAG_ONLY_CHECK_EE_USAGE\fP flag should be specified;
then the function will check whether the key of the peer matches the
key advertised in the DANE entry.
.SH "RETURNS"
a negative error code on error and \fBDANE_E_SUCCESS\fP (0)
when the DANE entries were successfully parsed, irrespective of
whether they were verified (see  \fIverify\fP for that information). If
no usable entries were encountered \fBDANE_E_REQUESTED_DATA_NOT_AVAILABLE\fP
will be returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
