<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_KDF-HKDF</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
      <li><a href="#Supported-parameters">Supported parameters</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_KDF-HKDF - The HKDF EVP_KDF implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing the <b>HKDF</b> KDF through the <b>EVP_KDF</b> API.</p>

<p>The EVP_KDF-HKDF algorithm implements the HKDF key derivation function. HKDF follows the &quot;extract-then-expand&quot; paradigm, where the KDF logically consists of two modules. The first stage takes the input keying material and &quot;extracts&quot; from it a fixed-length pseudorandom key K. The second stage &quot;expands&quot; the key K into several additional pseudorandom keys (the output of the KDF).</p>

<p>The output is considered to be keying material.</p>

<h2 id="Identity">Identity</h2>

<p>&quot;HKDF&quot; is the name for this implementation; it can be used with the EVP_KDF_fetch() function.</p>

<h2 id="Supported-parameters">Supported parameters</h2>

<p>The supported parameters are:</p>

<dl>

<dt id="properties-OSSL_KDF_PARAM_PROPERTIES-UTF8-string">&quot;properties&quot; (<b>OSSL_KDF_PARAM_PROPERTIES</b>) &lt;UTF8 string&gt;</dt>
<dd>

</dd>
<dt id="digest-OSSL_KDF_PARAM_DIGEST-UTF8-string">&quot;digest&quot; (<b>OSSL_KDF_PARAM_DIGEST</b>) &lt;UTF8 string&gt;</dt>
<dd>

</dd>
<dt id="key-OSSL_KDF_PARAM_KEY-octet-string">&quot;key&quot; (<b>OSSL_KDF_PARAM_KEY</b>) &lt;octet string&gt;</dt>
<dd>

</dd>
<dt id="salt-OSSL_KDF_PARAM_SALT-octet-string">&quot;salt&quot; (<b>OSSL_KDF_PARAM_SALT</b>) &lt;octet string&gt;</dt>
<dd>

<p>These parameters work as described in <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a>.</p>

</dd>
<dt id="info-OSSL_KDF_PARAM_INFO-octet-string">&quot;info&quot; (<b>OSSL_KDF_PARAM_INFO</b>) &lt;octet string&gt;</dt>
<dd>

<p>This parameter sets the info value. The length of the context info buffer cannot exceed 1024 bytes; this should be more than enough for any normal use of HKDF.</p>

</dd>
<dt id="mode-OSSL_KDF_PARAM_MODE-UTF8-string-or-integer">&quot;mode&quot; (<b>OSSL_KDF_PARAM_MODE</b>) &lt;UTF8 string&gt; or &lt;integer&gt;</dt>
<dd>

<p>This parameter sets the mode for the HKDF operation. There are three modes that are currently defined:</p>

<dl>

<dt id="EXTRACT_AND_EXPAND-or-EVP_KDF_HKDF_MODE_EXTRACT_AND_EXPAND">&quot;EXTRACT_AND_EXPAND&quot; or <b>EVP_KDF_HKDF_MODE_EXTRACT_AND_EXPAND</b></dt>
<dd>

<p>This is the default mode. Calling <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a> on an EVP_KDF_CTX set up for HKDF will perform an extract followed by an expand operation in one go. The derived key returned will be the result after the expand operation. The intermediate fixed-length pseudorandom key K is not returned.</p>

<p>In this mode the digest, key, salt and info values must be set before a key is derived otherwise an error will occur.</p>

</dd>
<dt id="EXTRACT_ONLY-or-EVP_KDF_HKDF_MODE_EXTRACT_ONLY">&quot;EXTRACT_ONLY&quot; or <b>EVP_KDF_HKDF_MODE_EXTRACT_ONLY</b></dt>
<dd>

<p>In this mode calling <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a> will just perform the extract operation. The value returned will be the intermediate fixed-length pseudorandom key K. The <i>keylen</i> parameter must match the size of K, which can be looked up by calling EVP_KDF_CTX_get_kdf_size() after setting the mode and digest.</p>

<p>The digest, key and salt values must be set before a key is derived otherwise an error will occur.</p>

</dd>
<dt id="EXPAND_ONLY-or-EVP_KDF_HKDF_MODE_EXPAND_ONLY">&quot;EXPAND_ONLY&quot; or <b>EVP_KDF_HKDF_MODE_EXPAND_ONLY</b></dt>
<dd>

<p>In this mode calling <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a> will just perform the expand operation. The input key should be set to the intermediate fixed-length pseudorandom key K returned from a previous extract operation.</p>

<p>The digest, key and info values must be set before a key is derived otherwise an error will occur.</p>

</dd>
</dl>

</dd>
</dl>

<p>The OpenSSL FIPS provider also supports the following parameters:</p>

<dl>

<dt id="fips-indicator-OSSL_KDF_PARAM_FIPS_APPROVED_INDICATOR-integer">&quot;fips-indicator&quot; (<b>OSSL_KDF_PARAM_FIPS_APPROVED_INDICATOR</b>) &lt;integer&gt;</dt>
<dd>

<p>A getter that returns 1 if the operation is FIPS approved, or 0 otherwise. This may be used after calling EVP_KDF_derive. It returns 0 if &quot;key-check&quot; is set to 0 and the check fails.</p>

</dd>
<dt id="key-check-OSSL_KDF_PARAM_FIPS_KEY_CHECK-integer">&quot;key-check&quot; (<b>OSSL_KDF_PARAM_FIPS_KEY_CHECK</b>) &lt;integer&gt;</dt>
<dd>

<p>The default value of 1 causes an error during EVP_KDF_CTX_set_params() if the length of used key-derivation key (<b>OSSL_KDF_PARAM_KEY</b>) is shorter than 112 bits. Setting this to zero will ignore the error and set the approved &quot;fips-indicator&quot; to 0. This option breaks FIPS compliance if it causes the approved &quot;fips-indicator&quot; to return 0.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>A context for HKDF can be obtained by calling:</p>

<pre><code>EVP_KDF *kdf = EVP_KDF_fetch(NULL, &quot;HKDF&quot;, NULL);
EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);</code></pre>

<p>The output length of an HKDF expand operation is specified via the <i>keylen</i> parameter to the <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a> function. When using EVP_KDF_HKDF_MODE_EXTRACT_ONLY the <i>keylen</i> parameter must equal the size of the intermediate fixed-length pseudorandom key otherwise an error will occur. For that mode, the fixed output size can be looked up by calling EVP_KDF_CTX_get_kdf_size() after setting the mode and digest on the <b>EVP_KDF_CTX</b>.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>This example derives 10 bytes using SHA-256 with the secret key &quot;secret&quot;, salt value &quot;salt&quot; and info value &quot;label&quot;:</p>

<pre><code>EVP_KDF *kdf;
EVP_KDF_CTX *kctx;
unsigned char out[10];
OSSL_PARAM params[5], *p = params;

kdf = EVP_KDF_fetch(NULL, &quot;HKDF&quot;, NULL);
kctx = EVP_KDF_CTX_new(kdf);
EVP_KDF_free(kdf);

*p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
                                        SN_sha256, strlen(SN_sha256));
*p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
                                         &quot;secret&quot;, (size_t)6);
*p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
                                         &quot;label&quot;, (size_t)5);
*p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
                                         &quot;salt&quot;, (size_t)4);
*p = OSSL_PARAM_construct_end();
if (EVP_KDF_derive(kctx, out, sizeof(out), params) &lt;= 0) {
    error(&quot;EVP_KDF_derive&quot;);
}

EVP_KDF_CTX_free(kctx);</code></pre>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>RFC 5869</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_KDF.html">EVP_KDF(3)</a>, <a href="../man3/EVP_KDF_CTX_new.html">EVP_KDF_CTX_new(3)</a>, <a href="../man3/EVP_KDF_CTX_free.html">EVP_KDF_CTX_free(3)</a>, <a href="../man3/EVP_KDF_CTX_get_kdf_size.html">EVP_KDF_CTX_get_kdf_size(3)</a>, <a href="../man3/EVP_KDF_CTX_set_params.html">EVP_KDF_CTX_set_params(3)</a>, <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a>, <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a>, <a href="../man7/EVP_KDF-TLS13_KDF.html">EVP_KDF-TLS13_KDF(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


