SOURCES
-------

This specifies the list of paths to source files for the target.
The following commands all set or add to the ``SOURCES`` target property
and are the usual way to manipulate it:

* :command:`add_executable`
* :command:`add_library`
* :command:`add_custom_target`
* :command:`target_sources`

Contents of ``SOURCES`` may use
:manual:`generator expressions <cmake-generator-expressions(7)>`.
If a path starts with a generator expression, it is expected to
evaluate to an absolute path. Not doing so is considered undefined behavior.

Paths that are for files generated by the build will be treated
as relative to the build directory of the target, if the path is not
already specified as an absolute path.  Note that whether a file is seen as
generated may be affected by policies :policy:`CMP0118` and :policy:`CMP0163`.

If a path does not start with a generator expression, is not an
absolute path and is not a generated file, it will be treated as relative to
the location selected by the first of the following that matches:

* If a file by the specified path exists relative to the target's source
  directory, use that file.
* If policy :policy:`CMP0115` is not set to ``NEW``, try appending each
  known source file extension to the path and check if that exists
  relative to the target's source directory.
* Repeat the above two steps, this time relative to the target's binary
  directory instead.

Note that the above decisions are made at generation time, not build time.

See the :manual:`cmake-buildsystem(7)` manual for more on defining
buildsystem properties.
