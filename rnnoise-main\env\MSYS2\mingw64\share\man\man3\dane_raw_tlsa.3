.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_raw_tlsa" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_raw_tlsa \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "int dane_raw_tlsa(dane_state_t " s ", dane_query_t * " r ", char *const * " dane_data ", const int * " dane_data_len ", int " secure ", int " bogus ");"
.SH ARGUMENTS
.IP "dane_state_t s" 12
The DANE state structure
.IP "dane_query_t * r" 12
A structure to place the result
.IP "char *const * dane_data" 12
array of DNS rdata items, terminated with a NULL pointer;
caller must guarantee that the referenced data remains
valid until \fBdane_query_deinit()\fP is called.
.IP "const int * dane_data_len" 12
the length n bytes of the dane_data items
.IP "int secure" 12
true if the result is validated securely, false if
validation failed or the domain queried has no security info
.IP "int bogus" 12
if the result was not secure (secure = 0) due to a security failure,
and the result is due to a security failure, bogus is true.
.SH "DESCRIPTION"
This function will fill in the TLSA (DANE) structure from
the given raw DNS record data. The  \fIdane_data\fP must be valid
during the lifetime of the query.
.SH "RETURNS"
On success, \fBDANE_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
