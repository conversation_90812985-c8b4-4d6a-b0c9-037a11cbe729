<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_filter Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structlzma__filter.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_filter Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Filter options.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;filter.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aef1d9709759f39e61db77547b2326929" id="r_aef1d9709759f39e61db77547b2326929"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aef1d9709759f39e61db77547b2326929">id</a></td></tr>
<tr class="memdesc:aef1d9709759f39e61db77547b2326929"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter ID.  <br /></td></tr>
<tr class="separator:aef1d9709759f39e61db77547b2326929"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10dfbaa1601793657d12320bef933ee6" id="r_a10dfbaa1601793657d12320bef933ee6"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a10dfbaa1601793657d12320bef933ee6">options</a></td></tr>
<tr class="memdesc:a10dfbaa1601793657d12320bef933ee6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to filter-specific options structure.  <br /></td></tr>
<tr class="separator:a10dfbaa1601793657d12320bef933ee6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Filter options. </p>
<p>This structure is used to pass a Filter ID and a pointer to the filter's options to liblzma. A few functions work with a single <a class="el" href="structlzma__filter.html" title="Filter options.">lzma_filter</a> structure, while most functions expect a filter chain.</p>
<p>A filter chain is indicated with an array of <a class="el" href="structlzma__filter.html" title="Filter options.">lzma_filter</a> structures. The array is terminated with .id = LZMA_VLI_UNKNOWN. Thus, the filter array must have LZMA_FILTERS_MAX + 1 elements (that is, five) to be able to hold any arbitrary filter chain. This is important when using <a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a> from <a class="el" href="block_8h.html" title=".xz Block handling">block.h</a>, because a filter array that is too small would make liblzma write past the end of the array. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aef1d9709759f39e61db77547b2326929" name="aef1d9709759f39e61db77547b2326929"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aef1d9709759f39e61db77547b2326929">&#9670;&#160;</a></span>id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_filter::id</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Filter ID. </p>
<p>Use constants whose name begin with 'LZMA_FILTER_' to specify different filters. In an array of <a class="el" href="structlzma__filter.html" title="Filter options.">lzma_filter</a> structures, use LZMA_VLI_UNKNOWN to indicate end of filters.</p>
<dl class="section note"><dt>Note</dt><dd>This is not an enum, because on some systems enums cannot be 64-bit. </dd></dl>

</div>
</div>
<a id="a10dfbaa1601793657d12320bef933ee6" name="a10dfbaa1601793657d12320bef933ee6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a10dfbaa1601793657d12320bef933ee6">&#9670;&#160;</a></span>options</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* lzma_filter::options</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to filter-specific options structure. </p>
<p>If the filter doesn't need options, set this to NULL. If id is set to LZMA_VLI_UNKNOWN, options is ignored, and thus doesn't need be initialized. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="filter_8h.html">filter.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structlzma__filter.html">lzma_filter</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
