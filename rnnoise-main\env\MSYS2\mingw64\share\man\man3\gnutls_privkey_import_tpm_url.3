.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_import_tpm_url" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_import_tpm_url \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_import_tpm_url(gnutls_privkey_t " pkey ", const char * " url ", const char * " srk_password ", const char * " key_password ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t pkey" 12
The private key
.IP "const char * url" 12
The URL of the TPM key to be imported
.IP "const char * srk_password" 12
The password for the SRK key (optional)
.IP "const char * key_password" 12
A password for the key (optional)
.IP "unsigned int flags" 12
One of the GNUTLS_PRIVKEY_* flags
.SH "DESCRIPTION"
This function will import the given private key to the abstract
\fBgnutls_privkey_t\fP type.

Note that unless \fBGNUTLS_PRIVKEY_DISABLE_CALLBACKS\fP
is specified, if incorrect (or NULL) passwords are given
the PKCS11 callback functions will be used to obtain the
correct passwords. Otherwise if the SRK password is wrong
\fBGNUTLS_E_TPM_SRK_PASSWORD_ERROR\fP is returned and if the key password
is wrong or not provided then \fBGNUTLS_E_TPM_KEY_PASSWORD_ERROR\fP
is returned. 
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
