OPTIMIZE_DEPENDENCIES
---------------------

.. versionadded:: 3.19

Activates dependency optimization of static and object libraries.

When this property is set to true, some dependencies for a static or object
library may be removed at generation time if they are not necessary to build
the library, since static and object libraries don't actually link against
anything.

If a static or object library has dependency optimization enabled, it first
discards all dependencies. Then, it looks through all of the direct and
indirect dependencies that it initially had, and adds them back if they meet
any of the following criteria:

* The dependency was added to the library by :command:`add_dependencies`.
* The dependency was added to the library through a source file in the library
  generated by a custom command that uses the dependency.
* The dependency has any ``PRE_BUILD``, ``PRE_LINK``, or ``POST_BUILD`` custom
  commands associated with it.
* The dependency contains any source files that were generated by a custom
  command.
* The dependency contains any languages which produce side effects that are
  relevant to the library. Currently, all languages except C, C++, Objective-C,
  Objective-C++, assembly, and CUDA are assumed to produce side effects.
  However, side effects from one language are assumed not to be relevant to
  another (for example, a Fortran library is assumed to not have any side
  effects that are relevant for a Swift library.)

As an example, assume you have a static Fortran library which depends on a
static C library, which in turn depends on a static Fortran library. The
top-level Fortran library has optimization enabled, but the middle C library
does not. If you build the top Fortran library, the bottom Fortran library will
also build, but not the middle C library, since the C library does not have any
side effects that are relevant for the Fortran library. However, if you build
the middle C library, the bottom Fortran library will also build, even though
it does not have any side effects that are relevant to the C library, since the
C library does not have optimization enabled.

This property is initialized by the value of the
:variable:`CMAKE_OPTIMIZE_DEPENDENCIES` variable when the target is created.
