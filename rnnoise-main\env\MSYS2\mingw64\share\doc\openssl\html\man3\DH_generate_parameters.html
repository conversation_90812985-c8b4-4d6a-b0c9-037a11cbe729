<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DH_generate_parameters</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>DH_generate_parameters_ex, DH_generate_parameters, DH_check, DH_check_params, DH_check_ex, DH_check_params_ex, DH_check_pub_key_ex - generate and check Diffie-Hellman parameters</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/dh.h&gt;</code></pre>

<p>The following functions have been deprecated since OpenSSL 3.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>int DH_generate_parameters_ex(DH *dh, int prime_len, int generator, BN_GENCB *cb);

int DH_check(DH *dh, int *codes);
int DH_check_params(DH *dh, int *codes);

int DH_check_ex(const DH *dh);
int DH_check_params_ex(const DH *dh);
int DH_check_pub_key_ex(const DH *dh, const BIGNUM *pub_key);</code></pre>

<p>The following functions have been deprecated since OpenSSL 0.9.8, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>DH *DH_generate_parameters(int prime_len, int generator,
                           void (*callback)(int, int, void *), void *cb_arg);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>All of the functions described on this page are deprecated. Applications should instead use <a href="../man3/EVP_PKEY_check.html">EVP_PKEY_check(3)</a>, <a href="../man3/EVP_PKEY_public_check.html">EVP_PKEY_public_check(3)</a>, <a href="../man3/EVP_PKEY_private_check.html">EVP_PKEY_private_check(3)</a> and <a href="../man3/EVP_PKEY_param_check.html">EVP_PKEY_param_check(3)</a>.</p>

<p>DH_generate_parameters_ex() generates Diffie-Hellman parameters that can be shared among a group of users, and stores them in the provided <b>DH</b> structure. The pseudo-random number generator must be seeded before calling it. The parameters generated by DH_generate_parameters_ex() should not be used in signature schemes.</p>

<p><b>prime_len</b> is the length in bits of the safe prime to be generated. <b>generator</b> is a small number &gt; 1, typically 2 or 5.</p>

<p>A callback function may be used to provide feedback about the progress of the key generation. If <b>cb</b> is not <b>NULL</b>, it will be called as described in <a href="../man3/BN_generate_prime.html">BN_generate_prime(3)</a> while a random prime number is generated, and when a prime has been found, <b>BN_GENCB_call(cb, 3, 0)</b> is called. See <a href="../man3/BN_generate_prime_ex.html">BN_generate_prime_ex(3)</a> for information on the BN_GENCB_call() function.</p>

<p>DH_generate_parameters() is similar to DH_generate_prime_ex() but expects an old-style callback function; see <a href="../man3/BN_generate_prime.html">BN_generate_prime(3)</a> for information on the old-style callback.</p>

<p>DH_check_params() confirms that the <b>p</b> and <b>g</b> are likely enough to be valid. This is a lightweight check, if a more thorough check is needed, use DH_check(). The value of <b>*codes</b> is updated with any problems found. If <b>*codes</b> is zero then no problems were found, otherwise the following bits may be set:</p>

<dl>

<dt id="DH_CHECK_P_NOT_PRIME">DH_CHECK_P_NOT_PRIME</dt>
<dd>

<p>The parameter <b>p</b> has been determined to not being an odd prime. Note that the lack of this bit doesn&#39;t guarantee that <b>p</b> is a prime.</p>

</dd>
<dt id="DH_NOT_SUITABLE_GENERATOR">DH_NOT_SUITABLE_GENERATOR</dt>
<dd>

<p>The generator <b>g</b> is not suitable. Note that the lack of this bit doesn&#39;t guarantee that <b>g</b> is suitable, unless <b>p</b> is known to be a strong prime.</p>

</dd>
<dt id="DH_MODULUS_TOO_SMALL">DH_MODULUS_TOO_SMALL</dt>
<dd>

<p>The modulus is too small.</p>

</dd>
<dt id="DH_MODULUS_TOO_LARGE">DH_MODULUS_TOO_LARGE</dt>
<dd>

<p>The modulus is too large.</p>

</dd>
</dl>

<p>DH_check() confirms that the Diffie-Hellman parameters <b>dh</b> are valid. The value of <b>*codes</b> is updated with any problems found. If <b>*codes</b> is zero then no problems were found, otherwise the following bits may be set:</p>

<dl>

<dt id="DH_CHECK_P_NOT_PRIME1">DH_CHECK_P_NOT_PRIME</dt>
<dd>

<p>The parameter <b>p</b> is not prime.</p>

</dd>
<dt id="DH_CHECK_P_NOT_SAFE_PRIME">DH_CHECK_P_NOT_SAFE_PRIME</dt>
<dd>

<p>The parameter <b>p</b> is not a safe prime and no <b>q</b> value is present.</p>

</dd>
<dt id="DH_UNABLE_TO_CHECK_GENERATOR">DH_UNABLE_TO_CHECK_GENERATOR</dt>
<dd>

<p>The generator <b>g</b> cannot be checked for suitability.</p>

</dd>
<dt id="DH_NOT_SUITABLE_GENERATOR1">DH_NOT_SUITABLE_GENERATOR</dt>
<dd>

<p>The generator <b>g</b> is not suitable.</p>

</dd>
<dt id="DH_CHECK_Q_NOT_PRIME">DH_CHECK_Q_NOT_PRIME</dt>
<dd>

<p>The parameter <b>q</b> is not prime.</p>

</dd>
<dt id="DH_CHECK_INVALID_Q_VALUE">DH_CHECK_INVALID_Q_VALUE</dt>
<dd>

<p>The parameter <b>q</b> is invalid.</p>

</dd>
<dt id="DH_CHECK_INVALID_J_VALUE">DH_CHECK_INVALID_J_VALUE</dt>
<dd>

<p>The parameter <b>j</b> is invalid.</p>

</dd>
</dl>

<p>If 0 is returned or <b>*codes</b> is set to a nonzero value the supplied parameters should not be used for Diffie-Hellman operations otherwise the security properties of the key exchange are not guaranteed.</p>

<p>DH_check_ex(), DH_check_params() and DH_check_pub_key_ex() are similar to DH_check() and DH_check_params() respectively, but the error reasons are added to the thread&#39;s error queue instead of provided as return values from the function.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>DH_generate_parameters_ex(), DH_check() and DH_check_params() return 1 if the check could be performed, 0 otherwise.</p>

<p>DH_generate_parameters() returns a pointer to the DH structure or NULL if the parameter generation fails.</p>

<p>DH_check_ex(), DH_check_params() and DH_check_pub_key_ex() return 1 if the check is successful, 0 for failed.</p>

<p>The error codes can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/DH_new.html">DH_new(3)</a>, <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man3/DH_free.html">DH_free(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>All of these functions were deprecated in OpenSSL 3.0.</p>

<p>DH_generate_parameters() was deprecated in OpenSSL 0.9.8; use DH_generate_parameters_ex() instead.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


