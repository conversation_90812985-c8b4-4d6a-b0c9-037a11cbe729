.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_get_data2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_get_data2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_get_data2(gnutls_session_t " session ", gnutls_datum_t * " data ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_datum_t * data" 12
is a pointer to a datum that will hold the session.
.SH "DESCRIPTION"
Returns necessary parameters to support resumption. The client
should call this function and store the returned session data. A session
can be resumed later by calling \fBgnutls_session_set_data()\fP with the returned
data. Note that under TLS 1.3, it is recommended for clients to use
session parameters only once, to prevent passive\-observers from correlating
the different connections.

The returned  \fIdata\fP are allocated and must be released using \fBgnutls_free()\fP.

This function will fail if called prior to handshake completion. In
case of false start TLS, the handshake completes only after data have
been successfully received from the peer.

Under TLS1.3 session resumption is possible only after a session ticket
is received by the client. To ensure that such a ticket has been received use
\fBgnutls_session_get_flags()\fP and check for flag \fBGNUTLS_SFLAGS_SESSION_TICKET\fP;
if this flag is not set, this function will wait for a new ticket within
an estimated roundtrip, and if not received will return dummy data which
cannot lead to resumption.

To get notified when new tickets are received by the server
use \fBgnutls_handshake_set_hook_function()\fP to wait for \fBGNUTLS_HANDSHAKE_NEW_SESSION_TICKET\fP
messages. Each call of \fBgnutls_session_get_data2()\fP after a ticket is
received, will return session resumption data corresponding to the last
received ticket.

Note that this function under TLS1.3 requires a callback to be set with
\fBgnutls_transport_set_pull_timeout_function()\fP for successful operation. There
was a bug before 3.6.10 which could make this function fail if that callback
was not set. On later versions if not set, the function will return a successful
error code, but will return dummy data that cannot lead to a resumption.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
