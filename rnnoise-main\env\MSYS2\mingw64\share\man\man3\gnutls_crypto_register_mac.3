.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_crypto_register_mac" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_crypto_register_mac \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_crypto_register_mac(gnutls_mac_algorithm_t " algorithm ", int " priority ", gnutls_mac_init_func " init ", gnutls_mac_setkey_func " setkey ", gnutls_mac_setnonce_func " setnonce ", gnutls_mac_hash_func " hash ", gnutls_mac_output_func " output ", gnutls_mac_deinit_func " deinit ", gnutls_mac_fast_func " hash_fast ");"
.SH ARGUMENTS
.IP "gnutls_mac_algorithm_t algorithm" 12
is the gnutls MAC identifier
.IP "int priority" 12
is the priority of the algorithm
.IP "gnutls_mac_init_func init" 12
A function which initializes the MAC
.IP "gnutls_mac_setkey_func setkey" 12
A function which sets the key of the MAC
.IP "gnutls_mac_setnonce_func setnonce" 12
A function which sets the nonce for the mac (may be \fBNULL\fP for common MAC algorithms)
.IP "gnutls_mac_hash_func hash" 12
Perform the hash operation
.IP "gnutls_mac_output_func output" 12
Provide the output of the MAC
.IP "gnutls_mac_deinit_func deinit" 12
A function which deinitializes the MAC
.IP "gnutls_mac_fast_func hash_fast" 12
Perform the MAC operation in one go
.SH "DESCRIPTION"
This function will register a MAC algorithm to be used by gnutls.
Any algorithm registered will override the included algorithms and
by convention kernel implemented algorithms have priority of 90
and CPU\-assisted of 80.
The algorithm with the lowest priority will be used by gnutls.
.SH "DEPRECATED"
since 3.7.0 it is no longer possible to override cipher implementation
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
