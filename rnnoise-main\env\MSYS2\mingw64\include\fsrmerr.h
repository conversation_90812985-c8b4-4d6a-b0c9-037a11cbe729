/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCL<PERSON><PERSON><PERSON> within this package.
 */

#ifndef __FSRMERROR_INC
#define __FSRMERROR_INC

#define FSRM_S_PARTIAL_BATCH ((HRESULT) 0x00045304)
#define FSRM_S_PARTIAL_CLASSIFICATION ((HRESULT) 0x00045305)
#define FSRM_S_CLASSIFICATION_SCAN_FAILURES ((HRESULT) 0x00045306)
#define FSRM_E_NOT_FOUND ((HRESULT) 0x80045301)
#define FSRM_E_INVALID_SCHEDULER_ARGUMENT ((HRESULT) 0x80045302)
#define FSRM_E_ALREADY_EXISTS ((HRESULT) 0x80045303)
#define FSRM_E_PATH_NOT_FOUND ((HRESULT) 0x80045304)
#define FSRM_E_INVALID_USER ((HRESULT) 0x80045305)
#define FSRM_E_INVALID_PATH ((HRESULT) 0x80045306)
#define FSRM_E_INVALID_LIMIT ((HRESULT) 0x80045307)
#define FSRM_E_INVALID_NAME ((HRESULT) 0x80045308)
#define FSRM_E_FAIL_BATCH ((HRESULT) 0x80045309)
#define FSRM_E_INVALID_TEXT ((HRESULT) 0x8004530a)
#define FSRM_E_INVALID_IMPORT_VERSION ((HRESULT) 0x8004530b)
#define FSRM_E_OUT_OF_RANGE ((HRESULT) 0x8004530d)
#define FSRM_E_REQD_PARAM_MISSING ((HRESULT) 0x8004530e)
#define FSRM_E_INVALID_COMBINATION ((HRESULT) 0x8004530f)
#define FSRM_E_DUPLICATE_NAME ((HRESULT) 0x80045310)
#define FSRM_E_NOT_SUPPORTED ((HRESULT) 0x80045311)
#define FSRM_E_DRIVER_NOT_READY ((HRESULT) 0x80045313)
#define FSRM_E_INSUFFICIENT_DISK ((HRESULT) 0x80045314)
#define FSRM_E_VOLUME_UNSUPPORTED ((HRESULT) 0x80045315)
#define FSRM_E_UNEXPECTED ((HRESULT) 0x80045316)
#define FSRM_E_INSECURE_PATH ((HRESULT) 0x80045317)
#define FSRM_E_INVALID_SMTP_SERVER ((HRESULT) 0x80045318)
#define FSRM_E_AUTO_QUOTA ((HRESULT) 0x0004531b)
#define FSRM_E_EMAIL_NOT_SENT ((HRESULT) 0x8004531c)
#define FSRM_E_INVALID_EMAIL_ADDRESS ((HRESULT) 0x8004531e)
#define FSRM_E_FILE_SYSTEM_CORRUPT ((HRESULT) 0x8004531f)
#define FSRM_E_LONG_CMDLINE ((HRESULT) 0x80045320)
#define FSRM_E_INVALID_FILEGROUP_DEFINITION ((HRESULT) 0x80045321)
#define FSRM_E_INVALID_DATASCREEN_DEFINITION ((HRESULT) 0x80045324)
#define FSRM_E_INVALID_REPORT_FORMAT ((HRESULT) 0x80045328)
#define FSRM_E_INVALID_REPORT_DESC ((HRESULT) 0x80045329)
#define FSRM_E_INVALID_FILENAME ((HRESULT) 0x8004532a)
#define FSRM_E_SHADOW_COPY ((HRESULT) 0x8004532c)
#define FSRM_E_XML_CORRUPTED ((HRESULT) 0x8004532d)
#define FSRM_E_CLUSTER_NOT_RUNNING ((HRESULT) 0x8004532e)
#define FSRM_E_STORE_NOT_INSTALLED ((HRESULT) 0x8004532f)
#define FSRM_E_NOT_CLUSTER_VOLUME ((HRESULT) 0x80045330)
#define FSRM_E_DIFFERENT_CLUSTER_GROUP ((HRESULT) 0x80045331)
#define FSRM_E_REPORT_TYPE_ALREADY_EXISTS ((HRESULT) 0x80045332)
#define FSRM_E_REPORT_JOB_ALREADY_RUNNING ((HRESULT) 0x80045333)
#define FSRM_E_REPORT_GENERATION_ERR ((HRESULT) 0x80045334)
#define FSRM_E_REPORT_TASK_TRIGGER ((HRESULT) 0x80045335)
#define FSRM_E_LOADING_DISABLED_MODULE ((HRESULT) 0x80045336)
#define FSRM_E_CANNOT_AGGREGATE ((HRESULT) 0x80045337)
#define FSRM_E_MESSAGE_LIMIT_EXCEEDED ((HRESULT) 0x80045338)
#define FSRM_E_OBJECT_IN_USE ((HRESULT) 0x80045339)
#define FSRM_E_CANNOT_RENAME_PROPERTY ((HRESULT) 0x8004533a)
#define FSRM_E_CANNOT_CHANGE_PROPERTY_TYPE ((HRESULT) 0x8004533b)
#define FSRM_E_MAX_PROPERTY_DEFINITIONS ((HRESULT) 0x8004533c)
#define FSRM_E_CLASSIFICATION_ALREADY_RUNNING ((HRESULT) 0x8004533d)
#define FSRM_E_CLASSIFICATION_NOT_RUNNING ((HRESULT) 0x8004533e)
#define FSRM_E_FILE_MANAGEMENT_JOB_ALREADY_RUNNING ((HRESULT) 0x8004533f)
#define FSRM_E_FILE_MANAGEMENT_JOB_EXPIRATION ((HRESULT) 0x80045340)
#define FSRM_E_FILE_MANAGEMENT_JOB_CUSTOM ((HRESULT) 0x80045341)
#define FSRM_E_FILE_MANAGEMENT_JOB_NOTIFICATION ((HRESULT) 0x80045342)
#define FSRM_E_FILE_OPEN_ERROR ((HRESULT) 0x80045343)
#define FSRM_E_UNSECURE_LINK_TO_HOSTED_MODULE ((HRESULT) 0x80045344)
#define FSRM_E_CACHE_INVALID ((HRESULT) 0x80045345)
#define FSRM_E_CACHE_MODULE_ALREADY_EXISTS ((HRESULT) 0x80045346)
#define FSRM_E_FILE_MANAGEMENT_EXPIRATION_DIR_IN_SCOPE ((HRESULT) 0x80045347)
#define FSRM_E_FILE_MANAGEMENT_JOB_ALREADY_EXISTS ((HRESULT) 0x80045348)
#define FSRM_E_PROPERTY_DELETED ((HRESULT) 0x80045349)
#define FSRM_E_LAST_ACCESS_UPDATE_DISABLED ((HRESULT) 0x80045350)
#define FSRM_E_NO_PROPERTY_VALUE ((HRESULT) 0x80045351)
#define FSRM_E_INPROC_MODULE_BLOCKED ((HRESULT) 0x80045352)
#define FSRM_E_ENUM_PROPERTIES_FAILED ((HRESULT) 0x80045353)
#define FSRM_E_SET_PROPERTY_FAILED ((HRESULT) 0x80045354)
#define FSRM_E_CANNOT_STORE_PROPERTIES ((HRESULT) 0x80045355)
#define FSRM_E_CANNOT_ALLOW_REPARSE_POINT_TAG ((HRESULT) 0x80045356)
#define FSRM_E_PARTIAL_CLASSIFICATION_PROPERTY_NOT_FOUND ((HRESULT) 0x80045357)
#define FSRM_E_TEXTREADER_NOT_INITIALIZED ((HRESULT) 0x80045358)
#define FSRM_E_TEXTREADER_IFILTER_NOT_FOUND ((HRESULT) 0x80045359)
#define FSRM_E_PERSIST_PROPERTIES_FAILED_ENCRYPTED ((HRESULT) 0x8004535a)
#define FSRM_E_TEXTREADER_IFILTER_CLSID_MALFORMED ((HRESULT) 0x80045360)
#define FSRM_E_TEXTREADER_STREAM_ERROR ((HRESULT) 0x80045361)
#define FSRM_E_TEXTREADER_FILENAME_TOO_LONG ((HRESULT) 0x80045362)
#define FSRM_E_INCOMPATIBLE_FORMAT ((HRESULT) 0x80045363)
#define FSRM_E_FILE_ENCRYPTED ((HRESULT) 0x80045364)
#define FSRM_E_PERSIST_PROPERTIES_FAILED ((HRESULT) 0x80045365)
#define FSRM_E_VOLUME_OFFLINE ((HRESULT) 0x80045366)
#define FSRM_E_FILE_MANAGEMENT_ACTION_TIMEOUT ((HRESULT) 0x80045367)
#define FSRM_E_FILE_MANAGEMENT_ACTION_GET_EXITCODE_FAILED ((HRESULT) 0x80045368)
#define FSRM_E_MODULE_INVALID_PARAM ((HRESULT) 0x80045369)
#define FSRM_E_MODULE_INITIALIZATION ((HRESULT) 0x8004536a)
#define FSRM_E_MODULE_SESSION_INITIALIZATION ((HRESULT) 0x8004536b)
#define FSRM_E_CLASSIFICATION_SCAN_FAIL ((HRESULT) 0x8004536c)
#define FSRM_E_FILE_MANAGEMENT_JOB_NOT_LEGACY_ACCESSIBLE ((HRESULT) 0x8004536d)
#define FSRM_E_FILE_MANAGEMENT_JOB_MAX_FILE_CONDITIONS ((HRESULT) 0x8004536e)
#define FSRM_E_CANNOT_USE_DEPRECATED_PROPERTY ((HRESULT) 0x8004536f)
#define FSRM_E_SYNC_TASK_TIMEOUT ((HRESULT) 0x80045370)
#define FSRM_E_CANNOT_USE_DELETED_PROPERTY ((HRESULT) 0x80045371)
#define FSRM_E_INVALID_AD_CLAIM ((HRESULT) 0x80045372)
#define FSRM_E_CLASSIFICATION_CANCELED ((HRESULT) 0x80045373)
#define FSRM_E_INVALID_FOLDER_PROPERTY_STORE ((HRESULT) 0x80045374)
#define FSRM_E_REBUILDING_FODLER_TYPE_INDEX ((HRESULT) 0x80045375)
#define FSRM_E_PROPERTY_MUST_APPLY_TO_FILES ((HRESULT) 0x80045376)
#define FSRM_E_CLASSIFICATION_TIMEOUT ((HRESULT) 0x80045377)
#define FSRM_E_CLASSIFICATION_PARTIAL_BATCH ((HRESULT) 0x80045378)
#define FSRM_E_CANNOT_DELETE_SYSTEM_PROPERTY ((HRESULT) 0x80045379)
#define FSRM_E_FILE_IN_USE ((HRESULT) 0x8004537a)
#define FSRM_E_ERROR_NOT_ENABLED ((HRESULT) 0x8004537b)
#define FSRM_E_CANNOT_CREATE_TEMP_COPY ((HRESULT) 0x8004537c)
#define FSRM_E_NO_EMAIL_ADDRESS ((HRESULT) 0x8004537d)
#define FSRM_E_ADR_MAX_EMAILS_SENT ((HRESULT) 0x8004537e)
#define FSRM_E_PATH_NOT_IN_NAMESPACE ((HRESULT) 0x8004537f)
#define FSRM_E_RMS_TEMPLATE_NOT_FOUND ((HRESULT) 0x80045380)
#define FSRM_E_SECURE_PROPERTIES_NOT_SUPPORTED ((HRESULT) 0x80045381)
#define FSRM_E_RMS_NO_PROTECTORS_INSTALLED ((HRESULT) 0x80045382)
#define FSRM_E_RMS_NO_PROTECTOR_INSTALLED_FOR_FILE ((HRESULT) 0x80045383)
#define FSRM_E_PROPERTY_MUST_APPLY_TO_FOLDERS ((HRESULT) 0x80045384)
#define FSRM_E_PROPERTY_MUST_BE_SECURE ((HRESULT) 0x80045385)
#define FSRM_E_PROPERTY_MUST_BE_GLOBAL ((HRESULT) 0x80045386)
#define FSRM_E_WMI_FAILURE ((HRESULT) 0x80045387)
#define FSRM_E_FILE_MANAGEMENT_JOB_RMS ((HRESULT) 0x80045388)
#define FSRM_E_SYNC_TASK_HAD_ERRORS ((HRESULT) 0x80045389)
#define FSRM_E_ADR_SRV_NOT_SUPPORTED ((HRESULT) 0x80045390)
#define FSRM_E_ADR_PATH_IS_LOCAL ((HRESULT) 0x80045391)
#define FSRM_E_ADR_NOT_DOMAIN_JOINED ((HRESULT) 0x80045392)
#define FSRM_E_CANNOT_REMOVE_READONLY ((HRESULT) 0x80045393)
#define FSRM_E_FILE_MANAGEMENT_JOB_INVALID_CONTINUOUS_CONFIG ((HRESULT) 0x80045394)
#define FSRM_E_LEGACY_SCHEDULE ((HRESULT) 0x80045395)
#define FSRM_E_CSC_PATH_NOT_SUPPORTED ((HRESULT) 0x80045396)
#define FSRM_E_EXPIRATION_PATH_NOT_WRITEABLE ((HRESULT) 0x80045397)
#define FSRM_E_EXPIRATION_PATH_TOO_LONG ((HRESULT) 0x80045398)
#define FSRM_E_EXPIRATION_VOLUME_NOT_NTFS ((HRESULT) 0x80045399)
#define FSRM_E_FILE_MANAGEMENT_JOB_DEPRECATED ((HRESULT) 0x8004539a)

#endif
