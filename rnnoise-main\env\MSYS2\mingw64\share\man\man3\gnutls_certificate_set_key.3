.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_key" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_key \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_certificate_set_key(gnutls_certificate_credentials_t " res ", const char ** " names ", int " names_size ", gnutls_pcert_st * " pcert_list ", int " pcert_list_size ", gnutls_privkey_t " key ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "const char ** names" 12
is an array of DNS names belonging to the public\-key (NULL if none)
.IP "int names_size" 12
holds the size of the names list
.IP "gnutls_pcert_st * pcert_list" 12
contains a certificate list (chain) or raw public\-key
.IP "int pcert_list_size" 12
holds the size of the certificate list
.IP "gnutls_privkey_t key" 12
is a \fBgnutls_privkey_t\fP key corresponding to the first public\-key in pcert_list
.SH "DESCRIPTION"
This function sets a public/private key pair in the
gnutls_certificate_credentials_t type. The given public key may be encapsulated
in a certificate or can be given as a raw key. This function may be
called more than once, in case multiple key pairs exist for
the server. For clients that want to send more than their own end\-
entity certificate (e.g., also an intermediate CA cert), the full
certificate chain must be provided in  \fIpcert_list\fP .

Note that the  \fIkey\fP will become part of the credentials structure and must
not be deallocated. It will be automatically deallocated when the  \fIres\fP structure
is deinitialized.

If this function fails, the  \fIres\fP structure is at an undefined state and it must
not be reused to load other keys or certificates.

Note that, this function by default returns zero on success and a negative value on error.
Since 3.5.6, when the flag \fBGNUTLS_CERTIFICATE_API_V2\fP is set using \fBgnutls_certificate_set_flags()\fP
it returns an index (greater or equal to zero). That index can be used for other functions to refer to the added key\-pair.

Since GnuTLS 3.6.6 this function also handles raw public keys.
.SH "RETURNS"
On success this functions returns zero, and otherwise a negative value on error (see above for modifying that behavior).
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
