.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_check_email" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_check_email \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "unsigned gnutls_x509_crt_check_email(gnutls_x509_crt_t " cert ", const char * " email ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain an gnutls_x509_crt_t type
.IP "const char * email" 12
A null terminated string that contains an email address (RFC822)
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will check if the given certificate's subject matches
the given email address.
.SH "RETURNS"
non\-zero for a successful match, and zero on failure.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
