.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_export" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_export \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_obj_export(gnutls_pkcs11_obj_t " obj ", void * " output_data ", size_t * " output_data_size ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_t obj" 12
Holds the object
.IP "void * output_data" 12
will contain the object data
.IP "size_t * output_data_size" 12
holds the size of output_data (and will be
replaced by the actual size of parameters)
.SH "DESCRIPTION"
This function will export the PKCS11 object data.  It is normal for
data to be inaccessible and in that case \fBGNUTLS_E_INVALID_REQUEST\fP
will be returned.

If the buffer provided is not long enough to hold the output, then
*output_data_size is updated and GNUTLS_E_SHORT_MEMORY_BUFFER will
be returned.
.SH "RETURNS"
In case of failure a negative error code will be
returned, and \fBGNUTLS_E_SUCCESS\fP (0) on success.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
