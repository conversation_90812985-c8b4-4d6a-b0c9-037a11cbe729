.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_x509_key" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_x509_key \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_get_x509_key(gnutls_certificate_credentials_t " res ", unsigned " index ", gnutls_x509_privkey_t * " key ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "unsigned index" 12
The index of the key to obtain.
.IP "gnutls_x509_privkey_t * key" 12
Location to store the key.
.SH "DESCRIPTION"
Obtains a X.509 private key that has been stored in  \fIres\fP with one of
\fBgnutls_certificate_set_x509_key()\fP, \fBgnutls_certificate_set_key()\fP,
\fBgnutls_certificate_set_x509_key_file()\fP,
\fBgnutls_certificate_set_x509_key_file2()\fP,
\fBgnutls_certificate_set_x509_key_mem()\fP, or
\fBgnutls_certificate_set_x509_key_mem2()\fP. The returned key must be deallocated
with \fBgnutls_x509_privkey_deinit()\fP when no longer needed.

The  \fIindex\fP matches the return value of \fBgnutls_certificate_set_x509_key()\fP and friends
functions, when the \fBGNUTLS_CERTIFICATE_API_V2\fP flag is set.

If there is no key with the given index,
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned. If the key with the
given index is not a X.509 key, \fBGNUTLS_E_INVALID_REQUEST\fP is returned.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success, or a negative error code.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
