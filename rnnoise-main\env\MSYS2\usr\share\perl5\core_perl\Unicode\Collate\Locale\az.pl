+{
   locale_version => 1.31,
# schwa doesn't require tailoring
   entry => <<'ENTRY', # for DUCET v13.0.0
00E7      ; [.1FD7.0020.0002] # LATIN SMALL LETTER C WITH CEDILLA
0063 0327 ; [.1FD7.0020.0002] # LATIN SMALL LETTER C WITH CEDILLA
00C7      ; [.1FD7.0020.0008] # LATIN CAPITAL LETTER C WITH CEDILLA
0043 0327 ; [.1FD7.0020.0008] # LATIN CAPITAL LETTER C WITH CEDILLA
011F      ; [.2052.0020.0002] # LATIN SMALL LETTER G WITH BREVE
0067 0306 ; [.2052.0020.0002] # LATIN SMALL LETTER G WITH BREVE
011E      ; [.2052.0020.0008] # LATIN CAPITAL LETTER G WITH BREVE
0047 0306 ; [.2052.0020.0008] # LATIN CAPITAL LETTER G WITH BREVE
0131      ; [.208F.0020.0002] # LATIN SMALL LETTER DOTLESS I
0049      ; [.208F.0020.0008] # LATIN CAPITAL LETTER I
00CC      ; [.208F.0020.0008][.0000.0025.0002] # LATIN CAPITAL LETTER I WITH GRAVE
00CD      ; [.208F.0020.0008][.0000.0024.0002] # LATIN CAPITAL LETTER I WITH ACUTE
00CE      ; [.208F.0020.0008][.0000.0027.0002] # LATIN CAPITAL LETTER I WITH CIRCUMFLEX
00CF      ; [.208F.0020.0008][.0000.002B.0002] # LATIN CAPITAL LETTER I WITH DIAERESIS
012A      ; [.208F.0020.0008][.0000.0032.0002] # LATIN CAPITAL LETTER I WITH MACRON
012C      ; [.208F.0020.0008][.0000.0026.0002] # LATIN CAPITAL LETTER I WITH BREVE
012E      ; [.208F.0020.0008][.0000.0031.0002] # LATIN CAPITAL LETTER I WITH OGONEK
0130      ; [.2090.0020.0008] # LATIN CAPITAL LETTER I WITH DOT ABOVE
0049 0307 ; [.2090.0020.0008] # LATIN CAPITAL LETTER I WITH DOT ABOVE
00F6      ; [.213D.0020.0002] # LATIN SMALL LETTER O WITH DIAERESIS
006F 0308 ; [.213D.0020.0002] # LATIN SMALL LETTER O WITH DIAERESIS
00D6      ; [.213D.0020.0008] # LATIN CAPITAL LETTER O WITH DIAERESIS
004F 0308 ; [.213D.0020.0008] # LATIN CAPITAL LETTER O WITH DIAERESIS
022B      ; [.213D.0020.0002][.0000.0032.0002] # LATIN SMALL LETTER O WITH DIAERESIS AND MACRON
022A      ; [.213D.0020.0008][.0000.0032.0002] # LATIN CAPITAL LETTER O WITH DIAERESIS AND MACRON
015F      ; [.21D3.0020.0002] # LATIN SMALL LETTER S WITH CEDILLA
0073 0327 ; [.21D3.0020.0002] # LATIN SMALL LETTER S WITH CEDILLA
015E      ; [.21D3.0020.0008] # LATIN CAPITAL LETTER S WITH CEDILLA
0053 0327 ; [.21D3.0020.0008] # LATIN CAPITAL LETTER S WITH CEDILLA
00FC      ; [.2218.0020.0002] # LATIN SMALL LETTER U WITH DIAERESIS
0075 0308 ; [.2218.0020.0002] # LATIN SMALL LETTER U WITH DIAERESIS
00DC      ; [.2218.0020.0008] # LATIN CAPITAL LETTER U WITH DIAERESIS
0055 0308 ; [.2218.0020.0008] # LATIN CAPITAL LETTER U WITH DIAERESIS
01DC      ; [.2218.0020.0002][.0000.0025.0002] # LATIN SMALL LETTER U WITH DIAERESIS AND GRAVE
01DB      ; [.2218.0020.0008][.0000.0025.0002] # LATIN CAPITAL LETTER U WITH DIAERESIS AND GRAVE
01D8      ; [.2218.0020.0002][.0000.0024.0002] # LATIN SMALL LETTER U WITH DIAERESIS AND ACUTE
01D7      ; [.2218.0020.0008][.0000.0024.0002] # LATIN CAPITAL LETTER U WITH DIAERESIS AND ACUTE
01D6      ; [.2218.0020.0002][.0000.0032.0002] # LATIN SMALL LETTER U WITH DIAERESIS AND MACRON
01D5      ; [.2218.0020.0008][.0000.0032.0002] # LATIN CAPITAL LETTER U WITH DIAERESIS AND MACRON
01DA      ; [.2218.0020.0002][.0000.0028.0002] # LATIN SMALL LETTER U WITH DIAERESIS AND CARON
01D9      ; [.2218.0020.0008][.0000.0028.0002] # LATIN CAPITAL LETTER U WITH DIAERESIS AND CARON
0071      ; [.20C5.0020.0002] # LATIN SMALL LETTER Q
0051      ; [.20C5.0020.0008] # LATIN CAPITAL LETTER Q
0078      ; [.2076.0020.0002] # LATIN SMALL LETTER X
0058      ; [.2076.0020.0008] # LATIN CAPITAL LETTER X
0077      ; [.2287.0020.0002] # LATIN SMALL LETTER W
0057      ; [.2287.0020.0008] # LATIN CAPITAL LETTER W
ENTRY
};
