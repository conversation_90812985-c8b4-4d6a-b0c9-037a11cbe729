/*
 * Copyright (C) 2010 <PERSON><PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifndef _INC_PROPKEY
#define _INC_PROPKEY

#include <propkeydef.h>

DEFINE_PROPERTYKEY(PKEY_Address_Country, 0xc07b4199,0xe1df,0x4493,0xb1,0xe1,0xde,0x59,0x46,0xfb,0x58,0xf8,100);
DEFINE_PROPERTYKEY(PKEY_Address_CountryCode, 0xc07b4199,0xe1df,0x4493,0xb1,0xe1,0xde,0x59,0x46,0xfb,0x58,0xf8,101);
DEFINE_PROPERTYKEY(PKEY_Address_Region, 0xc07b4199,0xe1df,0x4493,0xb1,0xe1,0xde,0x59,0x46,0xfb,0x58,0xf8,102);
DEFINE_PROPERTYKEY(PKEY_Address_RegionCode, 0xc07b4199,0xe1df,0x4493,0xb1,0xe1,0xde,0x59,0x46,0xfb,0x58,0xf8,103);
DEFINE_PROPERTYKEY(PKEY_Address_Town, 0xc07b4199,0xe1df,0x4493,0xb1,0xe1,0xde,0x59,0x46,0xfb,0x58,0xf8,104);

DEFINE_PROPERTYKEY(PKEY_Audio_ChannelCount, 0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x80, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03, 7);
DEFINE_PROPERTYKEY(PKEY_Audio_Compression, 0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x80, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03, 10);
DEFINE_PROPERTYKEY(PKEY_Audio_EncodingBitrate, 0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x70, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03, 4);
DEFINE_PROPERTYKEY(PKEY_Audio_Format, 0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x80, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03, 2);
DEFINE_PROPERTYKEY(PKEY_Audio_IsVariableBitRate, 0xe6822fee, 0x8c17, 0x4d62, 0x82, 0x3c, 0x8e, 0x9c, 0xfc, 0xbd, 0x1d, 0x5c, 100);
DEFINE_PROPERTYKEY(PKEY_Audio_PeakValue, 0x2579e5d0, 0x1116, 0x4084, 0xbd, 0x9a, 0x9b, 0x4f, 0x7c, 0xb4, 0xdf, 0x5e, 100);
DEFINE_PROPERTYKEY(PKEY_Audio_SampleRate, 0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x80, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03, 5);
DEFINE_PROPERTYKEY(PKEY_Audio_SampleSize, 0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x80, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03, 6);
DEFINE_PROPERTYKEY(PKEY_Audio_StreamName, 0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x80, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03, 9);
DEFINE_PROPERTYKEY(PKEY_Audio_StreamNumber, 0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x80, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03, 8);

DEFINE_PROPERTYKEY(PKEY_Calendar_Duration, 0x293ca35a,0x09aa,0x4dd2,0xb1,0x80,0x1f,0xe2,0x45,0x72,0x8a,0x52,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_IsOnline, 0xbfee9149,0xe3e2,0x49a7,0xa8,0x62,0xc0,0x59,0x88,0x14,0x5c,0xec,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_IsRecurring, 0x315b9c8d,0x80a9,0x4ef9,0xae,0x16,0x8e,0x74,0x6d,0xa5,0x1d,0x70,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_Location, 0xf6272d18,0xcecc,0x40b1,0xb2,0x6a,0x39,0x11,0x71,0x7a,0xa7,0xbd,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_OptionalAttendeeAddresses, 0xd55bae5a,0x3892,0x417a,0xa6,0x49,0xc6,0xac,0x5a,0xaa,0xea,0xb3,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_OptionalAttendeeNames, 0x09429607,0x582d,0x437f,0x84,0xc3,0xde,0x93,0xa2,0xb2,0x4c,0x3c,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_OrganizerAddress, 0x744c8242,0x4df5,0x456c,0xab,0x9e,0x01,0x4e,0xfb,0x90,0x21,0xe3,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_OrganizerName, 0xaaa660f9,0x9865,0x458e,0xb4,0x84,0x01,0xbc,0x7f,0xe3,0x97,0x3e,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_ReminderTime, 0x72fc5ba4,0x24f9,0x4011,0x9f,0x3f,0xad,0xd2,0x7a,0xfa,0xd8,0x18,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_RequiredAttendeeAddresses, 0x0ba7d6c3,0x568d,0x4159,0xab,0x91,0x78,0x1a,0x91,0xfb,0x71,0xe5,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_RequiredAttendeeNames, 0xb33af30b,0xf552,0x4584,0x93,0x6c,0xcb,0x93,0xe5,0xcd,0xa2,0x9f,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_Resources, 0x00f58a38,0xc54b,0x4c40,0x86,0x96,0x97,0x23,0x59,0x80,0xea,0xe1,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_ResponseStatus, 0x188c1f91,0x3c40,0x4132,0x9e,0xc5,0xd8,0xb0,0x3b,0x72,0xa8,0xa2,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_ShowTimeAs, 0x5bf396d4,0x5eb2,0x466f,0xbd,0xe9,0x2f,0xb3,0xf2,0x36,0x1d,0x6e,100);
DEFINE_PROPERTYKEY(PKEY_Calendar_ShowTimeAsText, 0x53da57cf,0x62c0,0x45c4,0x81,0xde,0x76,0x10,0xbc,0xef,0xd7,0xf5,100);

DEFINE_PROPERTYKEY(PKEY_Communication_AccountName, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,9);
DEFINE_PROPERTYKEY(PKEY_Communication_DateItemExpires, 0x428040ac,0xa177,0x4c8a,0x97,0x60,0xf6,0xf7,0x61,0x22,0x7f,0x9a,100);
DEFINE_PROPERTYKEY(PKEY_Communication_Direction, 0x8e531030,0xb960,0x4346,0xae,0x0d,0x66,0xbc,0x9a,0x86,0xfb,0x94,100);
DEFINE_PROPERTYKEY(PKEY_Communication_FollowupIconIndex, 0x83a6347e,0x6fe4,0x4f40,0xba,0x9c,0xc4,0x86,0x52,0x40,0xd1,0xf4,100);
DEFINE_PROPERTYKEY(PKEY_Communication_HeaderItem, 0xc9c34f84,0x2241,0x4401,0xb6,0x07,0xbd,0x20,0xed,0x75,0xae,0x7f,100);
DEFINE_PROPERTYKEY(PKEY_Communication_PolicyTag, 0xec0b4191,0xab0b,0x4c66,0x90,0xb6,0xc6,0x63,0x7c,0xde,0xbb,0xab,100);
DEFINE_PROPERTYKEY(PKEY_Communication_SecurityFlags, 0x8619a4b6,0x9f4d,0x4429,0x8c,0x0f,0xb9,0x96,0xca,0x59,0xe3,0x35,100);
DEFINE_PROPERTYKEY(PKEY_Communication_Suffix, 0x807b653a,0x9e91,0x43ef,0x8f,0x97,0x11,0xce,0x04,0xee,0x20,0xc5,100);
DEFINE_PROPERTYKEY(PKEY_Communication_TaskStatus, 0xbe1a72c6,0x9a1d,0x46b7,0xaf,0xe7,0xaf,0xaf,0x8c,0xef,0x49,0x99,100);
DEFINE_PROPERTYKEY(PKEY_Communication_TaskStatusText, 0xa6744477,0xc237,0x475b,0xa0,0x75,0x54,0xf3,0x44,0x98,0x29,0x2a,100);

DEFINE_PROPERTYKEY(PKEY_Computer_DecoratedFreeSpace, 0x9b174b35,0x40ff,0x11d2,0xa2,0x7e,0x00,0xc0,0x4f,0xc3,0x08,0x71,7);

DEFINE_PROPERTYKEY(PKEY_Contact_AccountPictureDynamicVideo, 0x0b8bb018,0x2725,0x4b44,0x92,0xba,0x79,0x33,0xae,0xb2,0xdd,0xe7,2);
DEFINE_PROPERTYKEY(PKEY_Contact_AccountPictureLarge, 0x0b8bb018,0x2725,0x4b44,0x92,0xba,0x79,0x33,0xae,0xb2,0xdd,0xe7,3);
DEFINE_PROPERTYKEY(PKEY_Contact_AccountPictureSmall, 0x0b8bb018,0x2725,0x4b44,0x92,0xba,0x79,0x33,0xae,0xb2,0xdd,0xe7,4);
DEFINE_PROPERTYKEY(PKEY_Contact_Anniversary, 0x9ad5badb,0xcea7,0x4470,0xa0,0x3d,0xb8,0x4e,0x51,0xb9,0x94,0x9e,100);
DEFINE_PROPERTYKEY(PKEY_Contact_AssistantName, 0xcd102c9c,0x5540,0x4a88,0xa6,0xf6,0x64,0xe4,0x98,0x1c,0x8c,0xd1,100);
DEFINE_PROPERTYKEY(PKEY_Contact_AssistantTelephone, 0x9a93244d,0xa7ad,0x4ff8,0x9b,0x99,0x45,0xee,0x4c,0xc0,0x9a,0xf6,100);
DEFINE_PROPERTYKEY(PKEY_Contact_Birthday, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,47);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress, 0x730fb6dd,0xcf7c,0x426b,0xa0,0x3f,0xbd,0x16,0x6c,0xc9,0xee,0x24,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress1Country, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,119);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress1Locality, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,117);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress1PostalCode, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,120);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress1Region, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,118);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress1Street, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,116);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress2Country, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,124);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress2Locality, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,122);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress2PostalCode, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,125);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress2Region, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,123);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress2Street, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,121);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress3Country, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,129);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress3Locality, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,127);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress3PostalCode, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,130);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress3Region, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,128);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddress3Street, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,126);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddressCity, 0x402b5934,0xec5a,0x48c3,0x93,0xe6,0x85,0xe8,0x6a,0x2d,0x93,0x4e,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddressCountry, 0xb0b87314,0xfcf6,0x4feb,0x8d,0xff,0xa5,0x0d,0xa6,0xaf,0x56,0x1c,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddressPostalCode, 0xe1d4a09e,0xd758,0x4cd1,0xb6,0xec,0x34,0xa8,0xb5,0xa7,0x3f,0x80,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddressPostOfficeBox, 0xbc4e71ce,0x17f9,0x48d5,0xbe,0xe9,0x02,0x1d,0xf0,0xea,0x54,0x09,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddressState, 0x446f787f,0x10c4,0x41cb,0xa6,0xc4,0x4d,0x03,0x43,0x55,0x15,0x97,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessAddressStreet, 0xddd1460f,0xc0bf,0x4553,0x8c,0xe4,0x10,0x43,0x3c,0x90,0x8f,0xb0,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessEmailAddresses, 0xf271c659,0x7e5e,0x471f,0xba,0x25,0x7f,0x77,0xb2,0x86,0xf8,0x36,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessFaxNumber, 0x91eff6f3,0x2e27,0x42ca,0x93,0x3e,0x7c,0x99,0x9f,0xbe,0x31,0x0b,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessHomePage, 0x56310920,0x2491,0x4919,0x99,0xce,0xea,0xdb,0x06,0xfa,0xfd,0xb2,100);
DEFINE_PROPERTYKEY(PKEY_Contact_BusinessTelephone, 0x6a15e5a0,0x0a1e,0x4cd7,0xbb,0x8c,0xd2,0xf1,0xb0,0xc9,0x29,0xbc,100);
DEFINE_PROPERTYKEY(PKEY_Contact_CallbackTelephone, 0xbf53d1c3,0x49e0,0x4f7f,0x85,0x67,0x5a,0x82,0x1d,0x8a,0xc5,0x42,100);
DEFINE_PROPERTYKEY(PKEY_Contact_CarTelephone, 0x8fdc6dea,0xb929,0x412b,0xba,0x90,0x39,0x7a,0x25,0x74,0x65,0xfe,100);
DEFINE_PROPERTYKEY(PKEY_Contact_Children, 0xd4729704,0x8ef1,0x43ef,0x90,0x24,0x2b,0xd3,0x81,0x18,0x7f,0xd5,100);
DEFINE_PROPERTYKEY(PKEY_Contact_CompanyMainTelephone, 0x8589e481,0x6040,0x473d,0xb1,0x71,0x7f,0xa8,0x9c,0x27,0x08,0xed,100);
DEFINE_PROPERTYKEY(PKEY_Contact_ConnectedServiceDisplayName, 0x39b77f4f,0xa104,0x4863,0xb3,0x95,0x2d,0xb2,0xad,0x8f,0x7b,0xc1,100);
DEFINE_PROPERTYKEY(PKEY_Contact_ConnectedServiceIdentities, 0x80f41eb8,0xafc4,0x4208,0xaa,0x5f,0xcc,0xe2,0x1a,0x62,0x72,0x81,100);
DEFINE_PROPERTYKEY(PKEY_Contact_ConnectedServiceName, 0xb5c84c9e,0x5927,0x46b5,0xa3,0xcc,0x93,0x3c,0x21,0xb7,0x84,0x69,100);
DEFINE_PROPERTYKEY(PKEY_Contact_ConnectedServiceSupportedActions, 0xa19fb7a9,0x024b,0x4371,0xa8,0xbf,0x4d,0x29,0xc3,0xe4,0xe9,0xc9,100);
DEFINE_PROPERTYKEY(PKEY_Contact_DataSuppliers, 0x9660c283,0xfc3a,0x4a08,0xa0,0x96,0xee,0xd3,0xaa,0xc4,0x6d,0xa2,100);
DEFINE_PROPERTYKEY(PKEY_Contact_Department, 0xfc9f7306,0xff8f,0x4d49,0x9f,0xb6,0x3f,0xfe,0x5c,0x09,0x51,0xec,100);
DEFINE_PROPERTYKEY(PKEY_Contact_DisplayBusinessPhoneNumbers, 0x364028da,0xd895,0x41fe,0xa5,0x84,0x30,0x2b,0x1b,0xb7,0x0a,0x76,100);
DEFINE_PROPERTYKEY(PKEY_Contact_DisplayHomePhoneNumbers, 0x5068bcdf,0xd697,0x4d85,0x8c,0x53,0x1f,0x1c,0xda,0xb0,0x17,0x63,100);
DEFINE_PROPERTYKEY(PKEY_Contact_DisplayMobilePhoneNumbers, 0x9cb0c358,0x9d7a,0x46b1,0xb4,0x66,0xdc,0xc6,0xf1,0xa3,0xd9,0x3d,100);
DEFINE_PROPERTYKEY(PKEY_Contact_DisplayOtherPhoneNumbers, 0x03089873,0x8ee8,0x4191,0xbd,0x60,0xd3,0x1f,0x72,0xb7,0x90,0x0b,100);
DEFINE_PROPERTYKEY(PKEY_Contact_EmailAddress, 0xf8fa7fa3,0xd12b,0x4785,0x8a,0x4e,0x69,0x1a,0x94,0xf7,0xa3,0xe7,100);
DEFINE_PROPERTYKEY(PKEY_Contact_EmailAddress2, 0x38965063,0xedc8,0x4268,0x84,0x91,0xb7,0x72,0x31,0x72,0xcf,0x29,100);
DEFINE_PROPERTYKEY(PKEY_Contact_EmailAddress3, 0x644d37b4,0xe1b3,0x4bad,0xb0,0x99,0x7e,0x7c,0x04,0x96,0x6a,0xca,100);
DEFINE_PROPERTYKEY(PKEY_Contact_EmailAddresses, 0x84d8f337,0x981d,0x44b3,0x96,0x15,0xc7,0x59,0x6d,0xba,0x17,0xe3,100);
DEFINE_PROPERTYKEY(PKEY_Contact_EmailName, 0xcc6f4f24,0x6083,0x4bd4,0x87,0x54,0x67,0x4d,0x0d,0xe8,0x7a,0xb8,100);
DEFINE_PROPERTYKEY(PKEY_Contact_FileAsName, 0xf1a24aa7,0x9ca7,0x40f6,0x89,0xec,0x97,0xde,0xf9,0xff,0xe8,0xdb,100);
DEFINE_PROPERTYKEY(PKEY_Contact_FirstName, 0x14977844,0x6b49,0x4aad,0xa7,0x14,0xa4,0x51,0x3b,0xf6,0x04,0x60,100);
DEFINE_PROPERTYKEY(PKEY_Contact_FullName, 0x635e9051,0x50a5,0x4ba2,0xb9,0xdb,0x4e,0xd0,0x56,0xc7,0x72,0x96,100);
DEFINE_PROPERTYKEY(PKEY_Contact_Gender, 0x3c8cee58,0xd4f0,0x4cf9,0xb7,0x56,0x4e,0x5d,0x24,0x44,0x7b,0xcd,100);
DEFINE_PROPERTYKEY(PKEY_Contact_GenderValue, 0x3c8cee58,0xd4f0,0x4cf9,0xb7,0x56,0x4e,0x5d,0x24,0x44,0x7b,0xcd,101);
DEFINE_PROPERTYKEY(PKEY_Contact_Hobbies, 0x5dc2253f,0x5e11,0x4adf,0x9c,0xfe,0x91,0x0d,0xd0,0x1e,0x3e,0x70,100);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress, 0x98f98354,0x617a,0x46b8,0x85,0x60,0x5b,0x1b,0x64,0xbf,0x1f,0x89,100);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress1Country, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,104);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress1Locality, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,102);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress1PostalCode, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,105);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress1Region, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,103);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress1Street, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,101);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress2Country, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,109);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress2Locality, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,107);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress2PostalCode, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,110);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress2Region, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,108);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress2Street, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,106);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress3Country, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,114);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress3Locality, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,112);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress3PostalCode, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,115);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress3Region, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,113);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddress3Street, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,111);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddressCity, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,65);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddressCountry, 0x08a65aa1,0xf4c9,0x43dd,0x9d,0xdf,0xa3,0x3d,0x8e,0x7e,0xad,0x85,100);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddressPostalCode, 0x8afcc170,0x8a46,0x4b53,0x9e,0xee,0x90,0xba,0xe7,0x15,0x1e,0x62,100);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddressPostOfficeBox, 0x7b9f6399,0x0a3f,0x4b12,0x89,0xbd,0x4a,0xdc,0x51,0xc9,0x18,0xaf,100);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddressState, 0xc89a23d0,0x7d6d,0x4eb8,0x87,0xd4,0x77,0x6a,0x82,0xd4,0x93,0xe5,100);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeAddressStreet, 0x0adef160,0xdb3f,0x4308,0x9a,0x21,0x06,0x23,0x7b,0x16,0xfa,0x2a,100);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeEmailAddresses, 0x56c90e9d,0x9d46,0x4963,0x88,0x6f,0x2e,0x1c,0xd9,0xa6,0x94,0xef,100);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeFaxNumber, 0x660e04d6,0x81ab,0x4977,0xa0,0x9f,0x82,0x31,0x31,0x13,0xab,0x26,100);
DEFINE_PROPERTYKEY(PKEY_Contact_HomeTelephone, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,20);
DEFINE_PROPERTYKEY(PKEY_Contact_IMAddress, 0xd68dbd8a,0x3374,0x4b81,0x99,0x72,0x3e,0xc3,0x06,0x82,0xdb,0x3d,100);
DEFINE_PROPERTYKEY(PKEY_Contact_Initials, 0xf3d8f40d,0x50cb,0x44a2,0x97,0x18,0x40,0xcb,0x91,0x19,0x49,0x5d,100);
DEFINE_PROPERTYKEY(PKEY_Contact_JA_CompanyNamePhonetic, 0x897b3694,0xfe9e,0x43e6,0x80,0x66,0x26,0x0f,0x59,0x0c,0x01,0x00,2);
DEFINE_PROPERTYKEY(PKEY_Contact_JA_FirstNamePhonetic, 0x897b3694,0xfe9e,0x43e6,0x80,0x66,0x26,0x0f,0x59,0x0c,0x01,0x00,3);
DEFINE_PROPERTYKEY(PKEY_Contact_JA_LastNamePhonetic, 0x897b3694,0xfe9e,0x43e6,0x80,0x66,0x26,0x0f,0x59,0x0c,0x01,0x00,4);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo1CompanyAddress, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,120);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo1CompanyName, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,102);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo1Department, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,106);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo1Manager, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,105);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo1OfficeLocation, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,104);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo1Title, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,103);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo1YomiCompanyName, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,101);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo2CompanyAddress, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,121);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo2CompanyName, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,108);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo2Department, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,113);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo2Manager, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,112);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo2OfficeLocation, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,110);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo2Title, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,109);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo2YomiCompanyName, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,107);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo3CompanyAddress, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,123);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo3CompanyName, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,115);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo3Department, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,119);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo3Manager, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,118);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo3OfficeLocation, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,117);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo3Title, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,116);
DEFINE_PROPERTYKEY(PKEY_Contact_JobInfo3YomiCompanyName, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,114);
DEFINE_PROPERTYKEY(PKEY_Contact_JobTitle, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,6);
DEFINE_PROPERTYKEY(PKEY_Contact_Label, 0x97b0ad89,0xdf49,0x49cc,0x83,0x4e,0x66,0x09,0x74,0xfd,0x75,0x5b,100);
DEFINE_PROPERTYKEY(PKEY_Contact_LastName, 0x8f367200,0xc270,0x457c,0xb1,0xd4,0xe0,0x7c,0x5b,0xcd,0x90,0xc7,100);
DEFINE_PROPERTYKEY(PKEY_Contact_MailingAddress, 0xc0ac206a,0x827e,0x4650,0x95,0xae,0x77,0xe2,0xbb,0x74,0xfc,0xc9,100);
DEFINE_PROPERTYKEY(PKEY_Contact_MiddleName, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,71);
DEFINE_PROPERTYKEY(PKEY_Contact_MobileTelephone, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,35);
DEFINE_PROPERTYKEY(PKEY_Contact_NickName, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,74);
DEFINE_PROPERTYKEY(PKEY_Contact_OfficeLocation, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,7);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress, 0x508161fa,0x313b,0x43d5,0x83,0xa1,0xc1,0xac,0xcf,0x68,0x62,0x2c,100);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress1Country, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,134);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress1Locality, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,132);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress1PostalCode, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,135);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress1Region, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,133);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress1Street, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,131);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress2Country, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,139);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress2Locality, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,137);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress2PostalCode, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,140);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress2Region, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,138);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress2Street, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,136);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress3Country, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,144);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress3Locality, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,142);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress3PostalCode, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,145);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress3Region, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,143);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddress3Street, 0xa7b6f596,0xd678,0x4bc1,0xb0,0x5f,0x02,0x03,0xd2,0x7e,0x8a,0xa1,141);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddressCity, 0x6e682923,0x7f7b,0x4f0c,0xa3,0x37,0xcf,0xca,0x29,0x66,0x87,0xbf,100);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddressCountry, 0x8f167568,0x0aae,0x4322,0x8e,0xd9,0x60,0x55,0xb7,0xb0,0xe3,0x98,100);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddressPostalCode, 0x95c656c1,0x2abf,0x4148,0x9e,0xd3,0x9e,0xc6,0x02,0xe3,0xb7,0xcd,100);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddressPostOfficeBox, 0x8b26ea41,0x058f,0x43f6,0xae,0xcc,0x40,0x35,0x68,0x1c,0xe9,0x77,100);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddressState, 0x71b377d6,0xe570,0x425f,0xa1,0x70,0x80,0x9f,0xae,0x73,0xe5,0x4e,100);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherAddressStreet, 0xff962609,0xb7d6,0x4999,0x86,0x2d,0x95,0x18,0x0d,0x52,0x9a,0xea,100);
DEFINE_PROPERTYKEY(PKEY_Contact_OtherEmailAddresses, 0x11d6336b,0x38c4,0x4ec9,0x84,0xd6,0xeb,0x38,0xd0,0xb1,0x50,0xaf,100);
DEFINE_PROPERTYKEY(PKEY_Contact_PagerTelephone, 0xd6304e01,0xf8f5,0x4f45,0x8b,0x15,0xd0,0x24,0xa6,0x29,0x67,0x89,100);
DEFINE_PROPERTYKEY(PKEY_Contact_PersonalTitle, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,69);
DEFINE_PROPERTYKEY(PKEY_Contact_PhoneNumbersCanonical, 0xd042d2a1,0x927e,0x40b5,0xa5,0x03,0x6e,0xdb,0xd4,0x2a,0x51,0x7e,100);
DEFINE_PROPERTYKEY(PKEY_Contact_Prefix, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,75);
DEFINE_PROPERTYKEY(PKEY_Contact_PrimaryAddressCity, 0xc8ea94f0,0xa9e3,0x4969,0xa9,0x4b,0x9c,0x62,0xa9,0x53,0x24,0xe0,100);
DEFINE_PROPERTYKEY(PKEY_Contact_PrimaryAddressCountry, 0xe53d799d,0x0f3f,0x466e,0xb2,0xff,0x74,0x63,0x4a,0x3c,0xb7,0xa4,100);
DEFINE_PROPERTYKEY(PKEY_Contact_PrimaryAddressPostalCode, 0x18bbd425,0xecfd,0x46ef,0xb6,0x12,0x7b,0x4a,0x60,0x34,0xed,0xa0,100);
DEFINE_PROPERTYKEY(PKEY_Contact_PrimaryAddressPostOfficeBox, 0xde5ef3c7,0x46e1,0x484e,0x99,0x99,0x62,0xc5,0x30,0x83,0x94,0xc1,100);
DEFINE_PROPERTYKEY(PKEY_Contact_PrimaryAddressState, 0xf1176dfe,0x7138,0x4640,0x8b,0x4c,0xae,0x37,0x5d,0xc7,0x0a,0x6d,100);
DEFINE_PROPERTYKEY(PKEY_Contact_PrimaryAddressStreet, 0x63c25b20,0x96be,0x488f,0x87,0x88,0xc0,0x9c,0x40,0x7a,0xd8,0x12,100);
DEFINE_PROPERTYKEY(PKEY_Contact_PrimaryEmailAddress, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,48);
DEFINE_PROPERTYKEY(PKEY_Contact_PrimaryTelephone, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,25);
DEFINE_PROPERTYKEY(PKEY_Contact_Profession, 0x7268af55,0x1ce4,0x4f6e,0xa4,0x1f,0xb6,0xe4,0xef,0x10,0xe4,0xa9,100);
DEFINE_PROPERTYKEY(PKEY_Contact_SpouseName, 0x9d2408b6,0x3167,0x422b,0x82,0xb0,0xf5,0x83,0xb7,0xa7,0xcf,0xe3,100);
DEFINE_PROPERTYKEY(PKEY_Contact_Suffix, 0x176dc63c,0x2688,0x4e89,0x81,0x43,0xa3,0x47,0x80,0x0f,0x25,0xe9,73);
DEFINE_PROPERTYKEY(PKEY_Contact_TelexNumber, 0xc554493c,0xc1f7,0x40c1,0xa7,0x6c,0xef,0x8c,0x06,0x14,0x00,0x3e,100);
DEFINE_PROPERTYKEY(PKEY_Contact_TTYTDDTelephone, 0xaaf16bac,0x2b55,0x45e6,0x9f,0x6d,0x41,0x5e,0xb9,0x49,0x10,0xdf,100);
DEFINE_PROPERTYKEY(PKEY_Contact_WebPage, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,18);
DEFINE_PROPERTYKEY(PKEY_Contact_Webpage2, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,124);
DEFINE_PROPERTYKEY(PKEY_Contact_Webpage3, 0x00f63dd8,0x22bd,0x4a5d,0xba,0x34,0x5c,0xb0,0xb9,0xbd,0xcb,0x03,125);

DEFINE_PROPERTYKEY(PKEY_AcquisitionID, 0x65a98875,0x3c80,0x40ab,0xab,0xbc,0xef,0xda,0xf7,0x7d,0xbe,0xe2,100);
DEFINE_PROPERTYKEY(PKEY_ApplicationDefinedProperties, 0xcdbfc167,0x337e,0x41d8,0xaf,0x7c,0x8c,0x09,0x20,0x54,0x29,0xc7,100);
DEFINE_PROPERTYKEY(PKEY_ApplicationName, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,18);
DEFINE_PROPERTYKEY(PKEY_AppZoneIdentifier, 0x502cfeab,0x47eb,0x459c,0xb9,0x60,0xe6,0xd8,0x72,0x8f,0x77,0x01,102);
DEFINE_PROPERTYKEY(PKEY_Author, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,4);
DEFINE_PROPERTYKEY(PKEY_CachedFileUpdaterContentIdForConflictResolution, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,114);
DEFINE_PROPERTYKEY(PKEY_CachedFileUpdaterContentIdForStream, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,113);
DEFINE_PROPERTYKEY(PKEY_Capacity, 0x9b174b35,0x40ff,0x11d2,0xa2,0x7e,0x00,0xc0,0x4f,0xc3,0x08,0x71,3);
DEFINE_PROPERTYKEY(PKEY_Category, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,2);
DEFINE_PROPERTYKEY(PKEY_Comment, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,6);
DEFINE_PROPERTYKEY(PKEY_Company, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,15);
DEFINE_PROPERTYKEY(PKEY_ComputerName, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,5);
DEFINE_PROPERTYKEY(PKEY_ContainedItems, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,29);
DEFINE_PROPERTYKEY(PKEY_ContentId, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,132);
DEFINE_PROPERTYKEY(PKEY_ContentStatus, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,27);
DEFINE_PROPERTYKEY(PKEY_ContentType, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,26);
DEFINE_PROPERTYKEY(PKEY_ContentUri, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,131);
DEFINE_PROPERTYKEY(PKEY_Copyright, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,11);
DEFINE_PROPERTYKEY(PKEY_CreatorAppId, 0xc2ea046e,0x033c,0x4e91,0xbd,0x5b,0xd4,0x94,0x2f,0x6b,0xbe,0x49,2);
DEFINE_PROPERTYKEY(PKEY_CreatorOpenWithUIOptions, 0xc2ea046e,0x033c,0x4e91,0xbd,0x5b,0xd4,0x94,0x2f,0x6b,0xbe,0x49,3);
DEFINE_PROPERTYKEY(PKEY_DataObjectFormat, 0x1e81a3f8,0xa30f,0x4247,0xb9,0xee,0x1d,0x03,0x68,0xa9,0x42,0x5c,2);
DEFINE_PROPERTYKEY(PKEY_DateAccessed, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,16);
DEFINE_PROPERTYKEY(PKEY_DateAcquired, 0x2cbaa8f5,0xd81f,0x47ca,0xb1,0x7a,0xf8,0xd8,0x22,0x30,0x01,0x31,100);
DEFINE_PROPERTYKEY(PKEY_DateArchived, 0x43f8d7b7,0xa444,0x4f87,0x93,0x83,0x52,0x27,0x1c,0x9b,0x91,0x5c,100);
DEFINE_PROPERTYKEY(PKEY_DateCompleted, 0x72fab781,0xacda,0x43e5,0xb1,0x55,0xb2,0x43,0x4f,0x85,0xe6,0x78,100);
DEFINE_PROPERTYKEY(PKEY_DateCreated, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,15);
DEFINE_PROPERTYKEY(PKEY_DateImported, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,18258);
DEFINE_PROPERTYKEY(PKEY_DateModified, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,14);
DEFINE_PROPERTYKEY(PKEY_DefaultSaveLocationDisplay, 0x5d76b67f,0x9b3d,0x44bb,0xb6,0xae,0x25,0xda,0x4f,0x63,0x8a,0x67,10);
DEFINE_PROPERTYKEY(PKEY_DueDate, 0x3f8472b5,0xe0af,0x4db2,0x80,0x71,0xc5,0x3f,0xe7,0x6a,0xe7,0xce,100);
DEFINE_PROPERTYKEY(PKEY_EndDate, 0xc75faa05,0x96fd,0x49e7,0x9c,0xb4,0x9f,0x60,0x10,0x82,0xd5,0x53,100);
DEFINE_PROPERTYKEY(PKEY_ExpandoProperties, 0x6fa20de6,0xd11c,0x4d9d,0xa1,0x54,0x64,0x31,0x76,0x28,0xc1,0x2d,100);
DEFINE_PROPERTYKEY(PKEY_FileAllocationSize, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,18);
DEFINE_PROPERTYKEY(PKEY_FileAttributes, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,13);
DEFINE_PROPERTYKEY(PKEY_FileCount, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,12);
DEFINE_PROPERTYKEY(PKEY_FileDescription, 0x0cef7d53,0xfa64,0x11d1,0xa2,0x03,0x00,0x00,0xf8,0x1f,0xed,0xee,3);
DEFINE_PROPERTYKEY(PKEY_FileExtension, 0xe4f10a3c,0x49e6,0x405d,0x82,0x88,0xa2,0x3b,0xd4,0xee,0xaa,0x6c,100);
DEFINE_PROPERTYKEY(PKEY_FileFRN, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,21);
DEFINE_PROPERTYKEY(PKEY_FileName, 0x41cf5ae0,0xf75a,0x4806,0xbd,0x87,0x59,0xc7,0xd9,0x24,0x8e,0xb9,100);
DEFINE_PROPERTYKEY(PKEY_FileOfflineAvailabilityStatus, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,100);
DEFINE_PROPERTYKEY(PKEY_FileOwner, 0x9b174b34,0x40ff,0x11d2,0xa2,0x7e,0x00,0xc0,0x4f,0xc3,0x08,0x71,4);
DEFINE_PROPERTYKEY(PKEY_FilePlaceholderStatus, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,2);
DEFINE_PROPERTYKEY(PKEY_FileVersion, 0x0cef7d53,0xfa64,0x11d1,0xa2,0x03,0x00,0x00,0xf8,0x1f,0xed,0xee,4);
DEFINE_PROPERTYKEY(PKEY_FindData, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,0);
DEFINE_PROPERTYKEY(PKEY_FlagColor, 0x67df94de,0x0ca7,0x4d6f,0xb7,0x92,0x05,0x3a,0x3e,0x4f,0x03,0xcf,100);
DEFINE_PROPERTYKEY(PKEY_FlagColorText, 0x45eae747,0x8e2a,0x40ae,0x8c,0xbf,0xca,0x52,0xab,0xa6,0x15,0x2a,100);
DEFINE_PROPERTYKEY(PKEY_FlagStatus, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,12);
DEFINE_PROPERTYKEY(PKEY_FlagStatusText, 0xdc54fd2e,0x189d,0x4871,0xaa,0x01,0x08,0xc2,0xf5,0x7a,0x4a,0xbc,100);
DEFINE_PROPERTYKEY(PKEY_FolderKind, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,101);
DEFINE_PROPERTYKEY(PKEY_FolderNameDisplay, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,25);
DEFINE_PROPERTYKEY(PKEY_FreeSpace, 0x9b174b35,0x40ff,0x11d2,0xa2,0x7e,0x00,0xc0,0x4f,0xc3,0x08,0x71,2);
DEFINE_PROPERTYKEY(PKEY_FullText, 0x1e3ee840,0xbc2b,0x476c,0x82,0x37,0x2a,0xcd,0x1a,0x83,0x9b,0x22,6);
DEFINE_PROPERTYKEY(PKEY_HighKeywords, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,24);
DEFINE_PROPERTYKEY(PKEY_Identity, 0xa26f4afc,0x7346,0x4299,0xbe,0x47,0xeb,0x1a,0xe6,0x13,0x13,0x9f,100);
DEFINE_PROPERTYKEY(PKEY_Identity_Blob, 0x8c3b93a4,0xbaed,0x1a83,0x9a,0x32,0x10,0x2e,0xe3,0x13,0xf6,0xeb,100);
DEFINE_PROPERTYKEY(PKEY_Identity_DisplayName, 0x7d683fc9,0xd155,0x45a8,0xbb,0x1f,0x89,0xd1,0x9b,0xcb,0x79,0x2f,100);
DEFINE_PROPERTYKEY(PKEY_Identity_InternetSid, 0x6d6d5d49,0x265d,0x4688,0x9f,0x4e,0x1f,0xdd,0x33,0xe7,0xcc,0x83,100);
DEFINE_PROPERTYKEY(PKEY_Identity_IsMeIdentity, 0xa4108708,0x09df,0x4377,0x9d,0xfc,0x6d,0x99,0x98,0x6d,0x5a,0x67,100);
DEFINE_PROPERTYKEY(PKEY_Identity_KeyProviderContext, 0xa26f4afc,0x7346,0x4299,0xbe,0x47,0xeb,0x1a,0xe6,0x13,0x13,0x9f,17);
DEFINE_PROPERTYKEY(PKEY_Identity_KeyProviderName, 0xa26f4afc,0x7346,0x4299,0xbe,0x47,0xeb,0x1a,0xe6,0x13,0x13,0x9f,16);
DEFINE_PROPERTYKEY(PKEY_Identity_LogonStatusString, 0xf18dedf3,0x337f,0x42c0,0x9e,0x03,0xce,0xe0,0x87,0x08,0xa8,0xc3,100);
DEFINE_PROPERTYKEY(PKEY_Identity_PrimaryEmailAddress, 0xfcc16823,0xbaed,0x4f24,0x9b,0x32,0xa0,0x98,0x21,0x17,0xf7,0xfa,100);
DEFINE_PROPERTYKEY(PKEY_Identity_PrimarySid, 0x2b1b801e,0xc0c1,0x4987,0x9e,0xc5,0x72,0xfa,0x89,0x81,0x47,0x87,100);
DEFINE_PROPERTYKEY(PKEY_Identity_ProviderData, 0xa8a74b92,0x361b,0x4e9a,0xb7,0x22,0x7c,0x4a,0x73,0x30,0xa3,0x12,100);
DEFINE_PROPERTYKEY(PKEY_Identity_ProviderID, 0x74a7de49,0xfa11,0x4d3d,0xa0,0x06,0xdb,0x7e,0x08,0x67,0x59,0x16,100);
DEFINE_PROPERTYKEY(PKEY_Identity_QualifiedUserName, 0xda520e51,0xf4e9,0x4739,0xac,0x82,0x02,0xe0,0xa9,0x5c,0x90,0x30,100);
DEFINE_PROPERTYKEY(PKEY_Identity_UniqueID, 0xe55fc3b0,0x2b60,0x4220,0x91,0x8e,0xb2,0x1e,0x8b,0xf1,0x60,0x16,100);
DEFINE_PROPERTYKEY(PKEY_Identity_UserName, 0xc4322503,0x78ca,0x49c6,0x9a,0xcc,0xa6,0x8e,0x2a,0xfd,0x7b,0x6b,100);
DEFINE_PROPERTYKEY(PKEY_IdentityProvider_Name, 0xb96eff7b,0x35ca,0x4a35,0x86,0x07,0x29,0xe3,0xa5,0x4c,0x46,0xea,100);
DEFINE_PROPERTYKEY(PKEY_IdentityProvider_Picture, 0x2425166f,0x5642,0x4864,0x99,0x2f,0x98,0xfd,0x98,0xf2,0x94,0xc3,100);
DEFINE_PROPERTYKEY(PKEY_ImageParsingName, 0xd7750ee0,0xc6a4,0x48ec,0xb5,0x3e,0xb8,0x7b,0x52,0xe6,0xd0,0x73,100);
DEFINE_PROPERTYKEY(PKEY_Importance, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,11);

DEFINE_PROPERTYKEY(PKEY_ImportanceText, 0xa3b29791,0x7713,0x4e1d,0xbb,0x40,0x17,0xdb,0x85,0xf0,0x18,0x31,100);
DEFINE_PROPERTYKEY(PKEY_IsAttachment, 0xf23f425c,0x71a1,0x4fa8,0x92,0x2f,0x67,0x8e,0xa4,0xa6,0x04,0x08,100);
DEFINE_PROPERTYKEY(PKEY_IsDefaultNonOwnerSaveLocation, 0x5d76b67f,0x9b3d,0x44bb,0xb6,0xae,0x25,0xda,0x4f,0x63,0x8a,0x67,5);
DEFINE_PROPERTYKEY(PKEY_IsDefaultSaveLocation, 0x5d76b67f,0x9b3d,0x44bb,0xb6,0xae,0x25,0xda,0x4f,0x63,0x8a,0x67,3);
DEFINE_PROPERTYKEY(PKEY_IsDeleted, 0x5cda5fc8,0x33ee,0x4ff3,0x90,0x94,0xae,0x7b,0xd8,0x86,0x8c,0x4d,100);
DEFINE_PROPERTYKEY(PKEY_IsEncrypted, 0x90e5e14e,0x648b,0x4826,0xb2,0xaa,0xac,0xaf,0x79,0x0e,0x35,0x13,10);
DEFINE_PROPERTYKEY(PKEY_IsFlagged, 0x5da84765,0xe3ff,0x4278,0x86,0xb0,0xa2,0x79,0x67,0xfb,0xdd,0x03,100);
DEFINE_PROPERTYKEY(PKEY_IsFlaggedComplete, 0xa6f360d2,0x55f9,0x48de,0xb9,0x09,0x62,0x0e,0x09,0x0a,0x64,0x7c,100);
DEFINE_PROPERTYKEY(PKEY_IsIncomplete, 0x346c8bd1,0x2e6a,0x4c45,0x89,0xa4,0x61,0xb7,0x8e,0x8e,0x70,0x0f,100);
DEFINE_PROPERTYKEY(PKEY_IsLocationSupported, 0x5d76b67f,0x9b3d,0x44bb,0xb6,0xae,0x25,0xda,0x4f,0x63,0x8a,0x67,8);
DEFINE_PROPERTYKEY(PKEY_IsPinnedToNameSpaceTree, 0x5d76b67f,0x9b3d,0x44bb,0xb6,0xae,0x25,0xda,0x4f,0x63,0x8a,0x67,2);
DEFINE_PROPERTYKEY(PKEY_IsRead, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,10);
DEFINE_PROPERTYKEY(PKEY_IsSearchOnlyItem, 0x5d76b67f,0x9b3d,0x44bb,0xb6,0xae,0x25,0xda,0x4f,0x63,0x8a,0x67,4);
DEFINE_PROPERTYKEY(PKEY_IsSendToTarget, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,33);
DEFINE_PROPERTYKEY(PKEY_IsShared, 0xef884c5b,0x2bfe,0x41bb,0xaa,0xe5,0x76,0xee,0xdf,0x4f,0x99,0x02,100);
DEFINE_PROPERTYKEY(PKEY_ItemAuthors, 0xd0a04f0a,0x462a,0x48a4,0xbb,0x2f,0x37,0x06,0xe8,0x8d,0xbd,0x7d,100);
DEFINE_PROPERTYKEY(PKEY_ItemClassType, 0x048658ad,0x2db8,0x41a4,0xbb,0xb6,0xac,0x1e,0xf1,0x20,0x7e,0xb1,100);
DEFINE_PROPERTYKEY(PKEY_ItemDate, 0xf7db74b4,0x4287,0x4103,0xaf,0xba,0xf1,0xb1,0x3d,0xcd,0x75,0xcf,100);
DEFINE_PROPERTYKEY(PKEY_ItemFolderNameDisplay, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,2);
DEFINE_PROPERTYKEY(PKEY_ItemFolderPathDisplay, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,6);
DEFINE_PROPERTYKEY(PKEY_ItemFolderPathDisplayNarrow, 0xdabd30ed,0x0043,0x4789,0xa7,0xf8,0xd0,0x13,0xa4,0x73,0x66,0x22,100);
DEFINE_PROPERTYKEY(PKEY_ItemName, 0x6b8da074,0x3b5c,0x43bc,0x88,0x6f,0x0a,0x2c,0xdc,0xe0,0x0b,0x6f,100);
DEFINE_PROPERTYKEY(PKEY_ItemNameDisplay, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,10);
DEFINE_PROPERTYKEY(PKEY_ItemNameDisplayWithoutExtension, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,24);
DEFINE_PROPERTYKEY(PKEY_ItemNamePrefix, 0xd7313ff1,0xa77a,0x401c,0x8c,0x99,0x3d,0xbd,0xd6,0x8a,0xdd,0x36,100);
DEFINE_PROPERTYKEY(PKEY_ItemNameSortOverride, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,23);
DEFINE_PROPERTYKEY(PKEY_ItemParticipants, 0xd4d0aa16,0x9948,0x41a4,0xaa,0x85,0xd9,0x7f,0xf9,0x64,0x69,0x93,100);
DEFINE_PROPERTYKEY(PKEY_ItemPathDisplay, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,7);
DEFINE_PROPERTYKEY(PKEY_ItemPathDisplayNarrow, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,8);
DEFINE_PROPERTYKEY(PKEY_ItemSubType, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,37);
DEFINE_PROPERTYKEY(PKEY_ItemType, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,11);
DEFINE_PROPERTYKEY(PKEY_ItemTypeText, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,4);
DEFINE_PROPERTYKEY(PKEY_ItemUrl, 0x49691c90,0x7e17,0x101a,0xa9,0x1c,0x08,0x00,0x2b,0x2e,0xcd,0xa9,9);
DEFINE_PROPERTYKEY(PKEY_Keywords, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,5);
DEFINE_PROPERTYKEY(PKEY_Kind, 0x1e3ee840,0xbc2b,0x476c,0x82,0x37,0x2a,0xcd,0x1a,0x83,0x9b,0x22,3);
DEFINE_PROPERTYKEY(PKEY_KindText, 0xf04bef95,0xc585,0x4197,0xa2,0xb7,0xdf,0x46,0xfd,0xc9,0xee,0x6d,100);
DEFINE_PROPERTYKEY(PKEY_Language, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,28);
DEFINE_PROPERTYKEY(PKEY_LastSyncError, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,107);
DEFINE_PROPERTYKEY(PKEY_LastSyncWarning, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,128);
DEFINE_PROPERTYKEY(PKEY_LastWriterPackageFamilyName, 0x502cfeab,0x47eb,0x459c,0xb9,0x60,0xe6,0xd8,0x72,0x8f,0x77,0x01,101);
DEFINE_PROPERTYKEY(PKEY_LowKeywords, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,25);
DEFINE_PROPERTYKEY(PKEY_MediumKeywords, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,26);
DEFINE_PROPERTYKEY(PKEY_MileageInformation, 0xfdf84370,0x031a,0x4add,0x9e,0x91,0x0d,0x77,0x5f,0x1c,0x66,0x05,100);
DEFINE_PROPERTYKEY(PKEY_MIMEType, 0x0b63e350,0x9ccc,0x11d0,0xbc,0xdb,0x00,0x80,0x5f,0xcc,0xce,0x04,5);
DEFINE_PROPERTYKEY(PKEY_Null, 0x00000000,0x0000,0x0000,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0);
DEFINE_PROPERTYKEY(PKEY_OfflineAvailability, 0xa94688b6,0x7d9f,0x4570,0xa6,0x48,0xe3,0xdf,0xc0,0xab,0x2b,0x3f,100);
DEFINE_PROPERTYKEY(PKEY_OfflineStatus, 0x6d24888f,0x4718,0x4bda,0xaf,0xed,0xea,0x0f,0xb4,0x38,0x6c,0xd8,100);
DEFINE_PROPERTYKEY(PKEY_OriginalFileName, 0x0cef7d53,0xfa64,0x11d1,0xa2,0x03,0x00,0x00,0xf8,0x1f,0xed,0xee,6);
DEFINE_PROPERTYKEY(PKEY_OwnerSID, 0x5d76b67f,0x9b3d,0x44bb,0xb6,0xae,0x25,0xda,0x4f,0x63,0x8a,0x67,6);
DEFINE_PROPERTYKEY(PKEY_ParentalRating, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,21);
DEFINE_PROPERTYKEY(PKEY_ParentalRatingReason, 0x10984e0a,0xf9f2,0x4321,0xb7,0xef,0xba,0xf1,0x95,0xaf,0x43,0x19,100);
DEFINE_PROPERTYKEY(PKEY_ParentalRatingsOrganization, 0xa7fe0840,0x1344,0x46f0,0x8d,0x37,0x52,0xed,0x71,0x2a,0x4b,0xf9,100);
DEFINE_PROPERTYKEY(PKEY_ParsingBindContext, 0xdfb9a04d,0x362f,0x4ca3,0xb3,0x0b,0x02,0x54,0xb1,0x7b,0x5b,0x84,100);
DEFINE_PROPERTYKEY(PKEY_ParsingName, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,24);
DEFINE_PROPERTYKEY(PKEY_ParsingPath, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,30);
DEFINE_PROPERTYKEY(PKEY_PerceivedType, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,9);
DEFINE_PROPERTYKEY(PKEY_PercentFull, 0x9b174b35,0x40ff,0x11d2,0xa2,0x7e,0x00,0xc0,0x4f,0xc3,0x08,0x71,5);
DEFINE_PROPERTYKEY(PKEY_Priority, 0x9c1fcf74,0x2d97,0x41ba,0xb4,0xae,0xcb,0x2e,0x36,0x61,0xa6,0xe4,5);
DEFINE_PROPERTYKEY(PKEY_PriorityText, 0xd98be98b,0xb86b,0x4095,0xbf,0x52,0x9d,0x23,0xb2,0xe0,0xa7,0x52,100);
DEFINE_PROPERTYKEY(PKEY_Project, 0x39a7f922,0x477c,0x48de,0x8b,0xc8,0xb2,0x84,0x41,0xe3,0x42,0xe3,100);
DEFINE_PROPERTYKEY(PKEY_ProviderItemID, 0xf21d9941,0x81f0,0x471a,0xad,0xee,0x4e,0x74,0xb4,0x92,0x17,0xed,100);
DEFINE_PROPERTYKEY(PKEY_Rating, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,9);
DEFINE_PROPERTYKEY(PKEY_RatingText, 0x90197ca7,0xfd8f,0x4e8c,0x9d,0xa3,0xb5,0x7e,0x1e,0x60,0x92,0x95,100);
DEFINE_PROPERTYKEY(PKEY_RemoteConflictingFile, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,115);
DEFINE_PROPERTYKEY(PKEY_Security_AllowedEnterpriseDataProtectionIdentities, 0x38d43380,0xd418,0x4830,0x84,0xd5,0x46,0x93,0x5a,0x81,0xc5,0xc6,32);
DEFINE_PROPERTYKEY(PKEY_Security_EncryptionOwners, 0x5f5aff6a,0x37e5,0x4780,0x97,0xea,0x80,0xc7,0x56,0x5c,0xf5,0x35,34);
DEFINE_PROPERTYKEY(PKEY_Security_EncryptionOwnersDisplay, 0xde621b8f,0xe125,0x43a3,0xa3,0x2d,0x56,0x65,0x44,0x6d,0x63,0x2a,25);
DEFINE_PROPERTYKEY(PKEY_Sensitivity, 0xf8d3f6ac,0x4874,0x42cb,0xbe,0x59,0xab,0x45,0x4b,0x30,0x71,0x6a,100);
DEFINE_PROPERTYKEY(PKEY_SensitivityText, 0xd0c7f054,0x3f72,0x4725,0x85,0x27,0x12,0x9a,0x57,0x7c,0xb2,0x69,100);
DEFINE_PROPERTYKEY(PKEY_SFGAOFlags, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,25);
DEFINE_PROPERTYKEY(PKEY_SharedWith, 0xef884c5b,0x2bfe,0x41bb,0xaa,0xe5,0x76,0xee,0xdf,0x4f,0x99,0x02,200);
DEFINE_PROPERTYKEY(PKEY_ShareUserRating, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,12);
DEFINE_PROPERTYKEY(PKEY_SharingStatus, 0xef884c5b,0x2bfe,0x41bb,0xaa,0xe5,0x76,0xee,0xdf,0x4f,0x99,0x02,300);
DEFINE_PROPERTYKEY(PKEY_Shell_OmitFromView, 0xde35258c,0xc695,0x4cbc,0xb9,0x82,0x38,0xb0,0xad,0x24,0xce,0xd0,2);
DEFINE_PROPERTYKEY(PKEY_SimpleRating, 0xa09f084e,0xad41,0x489f,0x80,0x76,0xaa,0x5b,0xe3,0x08,0x2b,0xca,100);
DEFINE_PROPERTYKEY(PKEY_Size, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,12);
DEFINE_PROPERTYKEY(PKEY_SoftwareUsed, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,305);
DEFINE_PROPERTYKEY(PKEY_SourceItem, 0x668cdfa5,0x7a1b,0x4323,0xae,0x4b,0xe5,0x27,0x39,0x3a,0x1d,0x81,100);
DEFINE_PROPERTYKEY(PKEY_SourcePackageFamilyName, 0xffae9db7,0x1c8d,0x43ff,0x81,0x8c,0x84,0x40,0x3a,0xa3,0x73,0x2d,100);
DEFINE_PROPERTYKEY(PKEY_StartDate, 0x48fd6ec8,0x8a12,0x4cdf,0xa0,0x3e,0x4e,0xc5,0xa5,0x11,0xed,0xde,100);
DEFINE_PROPERTYKEY(PKEY_Status, 0x000214a1,0x0000,0x0000,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x46,9);
DEFINE_PROPERTYKEY(PKEY_StorageProviderCallerVersionInformation, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,7);
DEFINE_PROPERTYKEY(PKEY_StorageProviderCustomPrimaryIcon, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,12);
DEFINE_PROPERTYKEY(PKEY_StorageProviderError, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,109);
DEFINE_PROPERTYKEY(PKEY_StorageProviderFileChecksum, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,5);
DEFINE_PROPERTYKEY(PKEY_StorageProviderFileCreatedBy, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,10);
DEFINE_PROPERTYKEY(PKEY_StorageProviderFileFlags, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,8);
DEFINE_PROPERTYKEY(PKEY_StorageProviderFileHasConflict, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,9);
DEFINE_PROPERTYKEY(PKEY_StorageProviderFileIdentifier, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,3);
DEFINE_PROPERTYKEY(PKEY_StorageProviderFileModifiedBy, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,11);
DEFINE_PROPERTYKEY(PKEY_StorageProviderFileRemoteUri, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,112);
DEFINE_PROPERTYKEY(PKEY_StorageProviderFileVersion, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,4);
DEFINE_PROPERTYKEY(PKEY_StorageProviderFileVersionWaterline, 0xb2f9b9d6,0xfec4,0x4dd5,0x94,0xd7,0x89,0x57,0x48,0x8c,0x80,0x7b,6);
DEFINE_PROPERTYKEY(PKEY_StorageProviderId, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,108);
DEFINE_PROPERTYKEY(PKEY_StorageProviderShareStatuses, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,111);
DEFINE_PROPERTYKEY(PKEY_StorageProviderSharingStatus, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,117);
DEFINE_PROPERTYKEY(PKEY_StorageProviderStatus, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,110);
DEFINE_PROPERTYKEY(PKEY_Subject, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,3);
DEFINE_PROPERTYKEY(PKEY_SyncTransferStatus, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,103);
DEFINE_PROPERTYKEY(PKEY_Thumbnail, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,17);
DEFINE_PROPERTYKEY(PKEY_ThumbnailCacheId, 0x446d16b1,0x8dad,0x4870,0xa7,0x48,0x40,0x2e,0xa4,0x3d,0x78,0x8c,100);
DEFINE_PROPERTYKEY(PKEY_ThumbnailStream, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,27);

DEFINE_PROPERTYKEY(PKEY_Title, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,2);

DEFINE_PROPERTYKEY(PKEY_TitleSortOverride, 0xf0f7984d,0x222e,0x4ad2,0x82,0xab,0x1d,0xd8,0xea,0x40,0xe5,0x7e,300);
DEFINE_PROPERTYKEY(PKEY_TotalFileSize, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,14);
DEFINE_PROPERTYKEY(PKEY_Trademarks, 0x0cef7d53,0xfa64,0x11d1,0xa2,0x03,0x00,0x00,0xf8,0x1f,0xed,0xee,9);
DEFINE_PROPERTYKEY(PKEY_TransferOrder, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,106);
DEFINE_PROPERTYKEY(PKEY_TransferPosition, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,104);
DEFINE_PROPERTYKEY(PKEY_TransferSize, 0xfceff153,0xe839,0x4cf3,0xa9,0xe7,0xea,0x22,0x83,0x20,0x94,0xb8,105);
DEFINE_PROPERTYKEY(PKEY_VolumeId, 0x446d16b1,0x8dad,0x4870,0xa7,0x48,0x40,0x2e,0xa4,0x3d,0x78,0x8c,104);
DEFINE_PROPERTYKEY(PKEY_ZoneIdentifier, 0x502cfeab,0x47eb,0x459c,0xb9,0x60,0xe6,0xd8,0x72,0x8f,0x77,0x01,100);
DEFINE_PROPERTYKEY(PKEY_Device_PrinterURL, 0x0b48f35a,0xbe6e,0x4f17,0xb1,0x08,0x3c,0x40,0x73,0xd1,0x66,0x9a,15);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_DeviceAddress, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,1);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_Flags, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,3);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_LastConnectedTime, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,11);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_Manufacturer, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,4);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_ModelNumber, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,5);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_ProductId, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,8);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_ProductVersion, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,9);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_ServiceGuid, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,2);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_VendorId, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,7);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Bluetooth_VendorIdSource, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,6);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Hid_IsReadOnly, 0xcbf38310,0x4a17,0x4310,0xa1,0xeb,0x24,0x7f,0x0b,0x67,0x59,0x3b,4);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Hid_ProductId, 0xcbf38310,0x4a17,0x4310,0xa1,0xeb,0x24,0x7f,0x0b,0x67,0x59,0x3b,6);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Hid_UsageId, 0xcbf38310,0x4a17,0x4310,0xa1,0xeb,0x24,0x7f,0x0b,0x67,0x59,0x3b,3);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Hid_UsagePage, 0xcbf38310,0x4a17,0x4310,0xa1,0xeb,0x24,0x7f,0x0b,0x67,0x59,0x3b,2);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Hid_VendorId, 0xcbf38310,0x4a17,0x4310,0xa1,0xeb,0x24,0x7f,0x0b,0x67,0x59,0x3b,5);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Hid_VersionNumber, 0xcbf38310,0x4a17,0x4310,0xa1,0xeb,0x24,0x7f,0x0b,0x67,0x59,0x3b,7);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_PrinterDriverDirectory, 0x847c66de,0xb8d6,0x4af9,0xab,0xc3,0x6f,0x4f,0x92,0x6b,0xc0,0x39,14);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_PrinterDriverName, 0xafc47170,0x14f5,0x498c,0x8f,0x30,0xb0,0xd1,0x9b,0xe4,0x49,0xc6,11);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_PrinterEnumerationFlag, 0xa00742a1,0xcd8c,0x4b37,0x95,0xab,0x70,0x75,0x55,0x87,0x76,0x7a,3);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_PrinterName, 0x0a7b84ef,0x0c27,0x463f,0x84,0xef,0x06,0xc5,0x07,0x00,0x01,0xbe,10);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_PrinterPortName, 0xeec7b761,0x6f94,0x41b1,0x94,0x9f,0xc7,0x29,0x72,0x0d,0xd1,0x3c,12);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Proximity_SupportsNfc, 0xfb3842cd,0x9e2a,0x4f83,0x8f,0xcc,0x4b,0x07,0x61,0x13,0x9a,0xe9,2);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Serial_PortName, 0x4c6bf15c,0x4c03,0x4aac,0x91,0xf5,0x64,0xc0,0xf8,0x52,0xbc,0xf4,4);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Serial_UsbProductId, 0x4c6bf15c,0x4c03,0x4aac,0x91,0xf5,0x64,0xc0,0xf8,0x52,0xbc,0xf4,3);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_Serial_UsbVendorId, 0x4c6bf15c,0x4c03,0x4aac,0x91,0xf5,0x64,0xc0,0xf8,0x52,0xbc,0xf4,2);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_WinUsb_DeviceInterfaceClasses, 0x95e127b5,0x79cc,0x4e83,0x9c,0x9e,0x84,0x22,0x18,0x7b,0x3e,0x0e,7);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_WinUsb_UsbClass, 0x95e127b5,0x79cc,0x4e83,0x9c,0x9e,0x84,0x22,0x18,0x7b,0x3e,0x0e,4);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_WinUsb_UsbProductId, 0x95e127b5,0x79cc,0x4e83,0x9c,0x9e,0x84,0x22,0x18,0x7b,0x3e,0x0e,3);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_WinUsb_UsbProtocol, 0x95e127b5,0x79cc,0x4e83,0x9c,0x9e,0x84,0x22,0x18,0x7b,0x3e,0x0e,6);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_WinUsb_UsbSubClass, 0x95e127b5,0x79cc,0x4e83,0x9c,0x9e,0x84,0x22,0x18,0x7b,0x3e,0x0e,5);
DEFINE_PROPERTYKEY(PKEY_DeviceInterface_WinUsb_UsbVendorId, 0x95e127b5,0x79cc,0x4e83,0x9c,0x9e,0x84,0x22,0x18,0x7b,0x3e,0x0e,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_AepId, 0x3b2ce006,0x5e61,0x4fde,0xba,0xb8,0x9b,0x8a,0xac,0x9b,0x26,0xdf,8);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Major, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Minor, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,3);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Services_Audio, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,10);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Services_Capturing, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,8);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Services_Information, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,12);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Services_LimitedDiscovery, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,4);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Services_Networking, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,6);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Services_ObjectXfer, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,9);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Services_Positioning, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,5);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Services_Rendering, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,7);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Cod_Services_Telephony, 0x5fbd34cd,0x561a,0x412e,0xba,0x98,0x47,0x8a,0x6b,0x0f,0xef,0x1d,11);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_LastSeenTime, 0x2bd67d8b,0x8beb,0x48d5,0x87,0xe0,0x6c,0xda,0x34,0x28,0x04,0x0a,12);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Le_AddressType, 0x995ef0b0,0x7eb3,0x4a8b,0xb9,0xce,0x06,0x8b,0xb3,0xf4,0xaf,0x69,4);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Le_Appearance, 0x995ef0b0,0x7eb3,0x4a8b,0xb9,0xce,0x06,0x8b,0xb3,0xf4,0xaf,0x69,1);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Le_Appearance_Category, 0x995ef0b0,0x7eb3,0x4a8b,0xb9,0xce,0x06,0x8b,0xb3,0xf4,0xaf,0x69,5);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Le_Appearance_Subcategory, 0x995ef0b0,0x7eb3,0x4a8b,0xb9,0xce,0x06,0x8b,0xb3,0xf4,0xaf,0x69,6);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Le_IsCallControlClient, 0x995ef0b0,0x7eb3,0x4a8b,0xb9,0xce,0x06,0x8b,0xb3,0xf4,0xaf,0x69,12);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Bluetooth_Le_IsConnectable, 0x995ef0b0,0x7eb3,0x4a8b,0xb9,0xce,0x06,0x8b,0xb3,0xf4,0xaf,0x69,8);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_CanPair, 0xe7c3fb29,0xcaa7,0x4f47,0x8c,0x8b,0xbe,0x59,0xb3,0x30,0xd4,0xc5,3);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Category, 0xa35996ab,0x11cf,0x4935,0x8b,0x61,0xa6,0x76,0x10,0x81,0xec,0xdf,17);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_ContainerId, 0xe7c3fb29,0xcaa7,0x4f47,0x8c,0x8b,0xbe,0x59,0xb3,0x30,0xd4,0xc5,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_DeviceAddress, 0xa35996ab,0x11cf,0x4935,0x8b,0x61,0xa6,0x76,0x10,0x81,0xec,0xdf,12);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_IsConnected, 0xa35996ab,0x11cf,0x4935,0x8b,0x61,0xa6,0x76,0x10,0x81,0xec,0xdf,7);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_IsPaired, 0xa35996ab,0x11cf,0x4935,0x8b,0x61,0xa6,0x76,0x10,0x81,0xec,0xdf,16);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_IsPresent, 0xa35996ab,0x11cf,0x4935,0x8b,0x61,0xa6,0x76,0x10,0x81,0xec,0xdf,9);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_Manufacturer, 0xa35996ab,0x11cf,0x4935,0x8b,0x61,0xa6,0x76,0x10,0x81,0xec,0xdf,5);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_ModelId, 0xa35996ab,0x11cf,0x4935,0x8b,0x61,0xa6,0x76,0x10,0x81,0xec,0xdf,4);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_ModelName, 0xa35996ab,0x11cf,0x4935,0x8b,0x61,0xa6,0x76,0x10,0x81,0xec,0xdf,3);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_PointOfService_ConnectionTypes, 0xd4bf61b3,0x442e,0x4ada,0x88,0x2d,0xfa,0x7b,0x70,0xc8,0x32,0xd9,6);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_ProtocolId, 0x3b2ce006,0x5e61,0x4fde,0xba,0xb8,0x9b,0x8a,0xac,0x9b,0x26,0xdf,5);
DEFINE_PROPERTYKEY(PKEY_Devices_Aep_SignalStrength, 0xa35996ab,0x11cf,0x4935,0x8b,0x61,0xa6,0x76,0x10,0x81,0xec,0xdf,6);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_CanPair, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,3);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_Categories, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,9);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_Children, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,2);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_ContainerId, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,12);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_DialProtocol_InstalledApplications, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,6);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_IsPaired, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,4);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_IsPresent, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,11);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_Manufacturer, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,6);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_ModelIds, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,8);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_ModelName, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,7);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_ProtocolIds, 0x0bba1ede,0x7566,0x4f47,0x90,0xec,0x25,0xfc,0x56,0x7c,0xed,0x2a,13);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportedUriSchemes, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,5);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsAudio, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,2);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsCapturing, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,11);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsImages, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,4);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsInformation, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,14);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsLimitedDiscovery, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,7);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsNetworking, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,9);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsObjectTransfer, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,12);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsPositioning, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,8);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsRendering, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,10);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsTelephony, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,13);
DEFINE_PROPERTYKEY(PKEY_Devices_AepContainer_SupportsVideo, 0x6af55d45,0x38db,0x4495,0xac,0xb0,0xd4,0x72,0x8a,0x3b,0x83,0x14,3);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_AepId, 0xc9c141a9,0x1b4c,0x4f17,0xa9,0xd1,0xf2,0x98,0x53,0x8c,0xad,0xb8,6);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_Bluetooth_CacheMode, 0x9744311e,0x7951,0x4b2e,0xb6,0xf0,0xec,0xb2,0x93,0xca,0xc1,0x19,5);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_Bluetooth_ServiceGuid, 0xa399aac7,0xc265,0x474e,0xb0,0x73,0xff,0xce,0x57,0x72,0x17,0x16,2);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_Bluetooth_TargetDevice, 0x9744311e,0x7951,0x4b2e,0xb6,0xf0,0xec,0xb2,0x93,0xca,0xc1,0x19,6);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_ContainerId, 0x71724756,0x3e74,0x4432,0x9b,0x59,0xe7,0xb2,0xf6,0x68,0xa5,0x93,4);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_FriendlyName, 0x71724756,0x3e74,0x4432,0x9b,0x59,0xe7,0xb2,0xf6,0x68,0xa5,0x93,2);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_IoT_ServiceInterfaces, 0x79d94e82,0x4d79,0x45aa,0x82,0x1a,0x74,0x85,0x8b,0x4e,0x4c,0xa6,2);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_ParentAepIsPaired, 0xc9c141a9,0x1b4c,0x4f17,0xa9,0xd1,0xf2,0x98,0x53,0x8c,0xad,0xb8,7);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_ProtocolId, 0xc9c141a9,0x1b4c,0x4f17,0xa9,0xd1,0xf2,0x98,0x53,0x8c,0xad,0xb8,5);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_ServiceClassId, 0x71724756,0x3e74,0x4432,0x9b,0x59,0xe7,0xb2,0xf6,0x68,0xa5,0x93,3);
DEFINE_PROPERTYKEY(PKEY_Devices_AepService_ServiceId, 0xc9c141a9,0x1b4c,0x4f17,0xa9,0xd1,0xf2,0x98,0x53,0x8c,0xad,0xb8,2);
DEFINE_PROPERTYKEY(PKEY_Devices_AppPackageFamilyName, 0x51236583,0x0c4a,0x4fe8,0xb8,0x1f,0x16,0x6a,0xec,0x13,0xf5,0x10,100);
DEFINE_PROPERTYKEY(PKEY_Devices_AudioDevice_Microphone_EqCoefficientsDb, 0x8943b373,0x388c,0x4395,0xb5,0x57,0xbc,0x6d,0xba,0xff,0xaf,0xdb,7);
DEFINE_PROPERTYKEY(PKEY_Devices_AudioDevice_Microphone_IsFarField, 0x8943b373,0x388c,0x4395,0xb5,0x57,0xbc,0x6d,0xba,0xff,0xaf,0xdb,6);
DEFINE_PROPERTYKEY(PKEY_Devices_AudioDevice_Microphone_SensitivityInDbfs, 0x8943b373,0x388c,0x4395,0xb5,0x57,0xbc,0x6d,0xba,0xff,0xaf,0xdb,3);
DEFINE_PROPERTYKEY(PKEY_Devices_AudioDevice_Microphone_SensitivityInDbfs2, 0x8943b373,0x388c,0x4395,0xb5,0x57,0xbc,0x6d,0xba,0xff,0xaf,0xdb,5);
DEFINE_PROPERTYKEY(PKEY_Devices_AudioDevice_Microphone_SignalToNoiseRatioInDb, 0x8943b373,0x388c,0x4395,0xb5,0x57,0xbc,0x6d,0xba,0xff,0xaf,0xdb,4);
DEFINE_PROPERTYKEY(PKEY_Devices_AudioDevice_RawProcessingSupported, 0x8943b373,0x388c,0x4395,0xb5,0x57,0xbc,0x6d,0xba,0xff,0xaf,0xdb,2);
DEFINE_PROPERTYKEY(PKEY_Devices_AudioDevice_SpeechProcessingSupported, 0xfb1de864,0xe06d,0x47f4,0x82,0xa6,0x8a,0x0a,0xef,0x44,0x49,0x3c,2);
DEFINE_PROPERTYKEY(PKEY_Devices_BatteryLife, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,10);
DEFINE_PROPERTYKEY(PKEY_Devices_BatteryPlusCharging, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,22);
DEFINE_PROPERTYKEY(PKEY_Devices_BatteryPlusChargingText, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,23);
DEFINE_PROPERTYKEY(PKEY_Devices_Category, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,91);
DEFINE_PROPERTYKEY(PKEY_Devices_CategoryGroup, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,94);
DEFINE_PROPERTYKEY(PKEY_Devices_CategoryIds, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,90);
DEFINE_PROPERTYKEY(PKEY_Devices_CategoryPlural, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,92);
DEFINE_PROPERTYKEY(PKEY_Devices_ChallengeAep, 0x0774315e,0xb714,0x48ec,0x8d,0xe8,0x81,0x25,0xc0,0x77,0xac,0x11,2);
DEFINE_PROPERTYKEY(PKEY_Devices_ChargingState, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,11);
DEFINE_PROPERTYKEY(PKEY_Devices_Children, 0x4340a6c5,0x93fa,0x4706,0x97,0x2c,0x7b,0x64,0x80,0x08,0xa5,0xa7,9);
DEFINE_PROPERTYKEY(PKEY_Devices_ClassGuid, 0xa45c254e,0xdf1c,0x4efd,0x80,0x20,0x67,0xd1,0x46,0xa8,0x50,0xe0,10);
DEFINE_PROPERTYKEY(PKEY_Devices_CompatibleIds, 0xa45c254e,0xdf1c,0x4efd,0x80,0x20,0x67,0xd1,0x46,0xa8,0x50,0xe0,4);
DEFINE_PROPERTYKEY(PKEY_Devices_Connected, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,55);
DEFINE_PROPERTYKEY(PKEY_Devices_ContainerId, 0x8c7ed206,0x3f8a,0x4827,0xb3,0xab,0xae,0x9e,0x1f,0xae,0xfc,0x6c,2);
DEFINE_PROPERTYKEY(PKEY_Devices_DefaultTooltip, 0x880f70a2,0x6082,0x47ac,0x8a,0xab,0xa7,0x39,0xd1,0xa3,0x00,0xc3,153);
DEFINE_PROPERTYKEY(PKEY_Devices_DeviceCapabilities, 0xa45c254e,0xdf1c,0x4efd,0x80,0x20,0x67,0xd1,0x46,0xa8,0x50,0xe0,17);
DEFINE_PROPERTYKEY(PKEY_Devices_DeviceCharacteristics, 0xa45c254e,0xdf1c,0x4efd,0x80,0x20,0x67,0xd1,0x46,0xa8,0x50,0xe0,29);
DEFINE_PROPERTYKEY(PKEY_Devices_DeviceDescription1, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,81);
DEFINE_PROPERTYKEY(PKEY_Devices_DeviceDescription2, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,82);
DEFINE_PROPERTYKEY(PKEY_Devices_DeviceHasProblem, 0x540b947e,0x8b40,0x45bc,0xa8,0xa2,0x6a,0x0b,0x89,0x4c,0xbd,0xa2,6);
DEFINE_PROPERTYKEY(PKEY_Devices_DeviceInstanceId, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,256);
DEFINE_PROPERTYKEY(PKEY_Devices_DeviceManufacturer, 0xa45c254e,0xdf1c,0x4efd,0x80,0x20,0x67,0xd1,0x46,0xa8,0x50,0xe0,13);
DEFINE_PROPERTYKEY(PKEY_Devices_DevObjectType, 0x13673f42,0xa3d6,0x49f6,0xb4,0xda,0xae,0x46,0xe0,0xc5,0x23,0x7c,2);
DEFINE_PROPERTYKEY(PKEY_Devices_DialProtocol_InstalledApplications, 0x6845cc72,0x1b71,0x48c3,0xaf,0x86,0xb0,0x91,0x71,0xa1,0x9b,0x14,3);
DEFINE_PROPERTYKEY(PKEY_Devices_DiscoveryMethod, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,52);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_Domain, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,3);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_FullName, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,5);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_HostName, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,7);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_InstanceName, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,4);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_NetworkAdapterId, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,11);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_PortNumber, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,12);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_Priority, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,9);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_ServiceName, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_TextAttributes, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,6);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_Ttl, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,10);
DEFINE_PROPERTYKEY(PKEY_Devices_Dnssd_Weight, 0xbf79c0ab,0xbb74,0x4cee,0xb0,0x70,0x47,0x0b,0x5a,0xe2,0x02,0xea,8);
DEFINE_PROPERTYKEY(PKEY_Devices_FriendlyName, 0x656a3bb3,0xecc0,0x43fd,0x84,0x77,0x4a,0xe0,0x40,0x4a,0x96,0xcd,12288);
DEFINE_PROPERTYKEY(PKEY_Devices_FunctionPaths, 0xd08dd4c0,0x3a9e,0x462e,0x82,0x90,0x7b,0x63,0x6b,0x25,0x76,0xb9,3);
DEFINE_PROPERTYKEY(PKEY_Devices_GlyphIcon, 0x51236583,0x0c4a,0x4fe8,0xb8,0x1f,0x16,0x6a,0xec,0x13,0xf5,0x10,123);
DEFINE_PROPERTYKEY(PKEY_Devices_HardwareIds, 0xa45c254e,0xdf1c,0x4efd,0x80,0x20,0x67,0xd1,0x46,0xa8,0x50,0xe0,3);
DEFINE_PROPERTYKEY(PKEY_Devices_Icon, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,57);
DEFINE_PROPERTYKEY(PKEY_Devices_InLocalMachineContainer, 0x8c7ed206,0x3f8a,0x4827,0xb3,0xab,0xae,0x9e,0x1f,0xae,0xfc,0x6c,4);
DEFINE_PROPERTYKEY(PKEY_Devices_InterfaceClassGuid, 0x026e516e,0xb814,0x414b,0x83,0xcd,0x85,0x6d,0x6f,0xef,0x48,0x22,4);
DEFINE_PROPERTYKEY(PKEY_Devices_InterfaceEnabled, 0x026e516e,0xb814,0x414b,0x83,0xcd,0x85,0x6d,0x6f,0xef,0x48,0x22,3);
DEFINE_PROPERTYKEY(PKEY_Devices_InterfacePaths, 0xd08dd4c0,0x3a9e,0x462e,0x82,0x90,0x7b,0x63,0x6b,0x25,0x76,0xb9,2);
DEFINE_PROPERTYKEY(PKEY_Devices_IpAddress, 0x656a3bb3,0xecc0,0x43fd,0x84,0x77,0x4a,0xe0,0x40,0x4a,0x96,0xcd,12297);
DEFINE_PROPERTYKEY(PKEY_Devices_IsDefault, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,86);
DEFINE_PROPERTYKEY(PKEY_Devices_IsNetworkConnected, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,85);
DEFINE_PROPERTYKEY(PKEY_Devices_IsShared, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,84);
DEFINE_PROPERTYKEY(PKEY_Devices_IsSoftwareInstalling, 0x83da6326,0x97a6,0x4088,0x94,0x53,0xa1,0x92,0x3f,0x57,0x3b,0x29,9);
DEFINE_PROPERTYKEY(PKEY_Devices_LaunchDeviceStageFromExplorer, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,77);
DEFINE_PROPERTYKEY(PKEY_Devices_LocalMachine, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,70);
DEFINE_PROPERTYKEY(PKEY_Devices_LocationPaths, 0xa45c254e,0xdf1c,0x4efd,0x80,0x20,0x67,0xd1,0x46,0xa8,0x50,0xe0,37);
DEFINE_PROPERTYKEY(PKEY_Devices_Manufacturer, 0x656a3bb3,0xecc0,0x43fd,0x84,0x77,0x4a,0xe0,0x40,0x4a,0x96,0xcd,8192);
DEFINE_PROPERTYKEY(PKEY_Devices_MetadataPath, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,71);
DEFINE_PROPERTYKEY(PKEY_Devices_MicrophoneArray_Geometry, 0xa1829ea2,0x27eb,0x459e,0x93,0x5d,0xb2,0xfa,0xd7,0xb0,0x77,0x62,2);
DEFINE_PROPERTYKEY(PKEY_Devices_MissedCalls, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,5);
DEFINE_PROPERTYKEY(PKEY_Devices_ModelId, 0x80d81ea6,0x7473,0x4b0c,0x82,0x16,0xef,0xc1,0x1a,0x2c,0x4c,0x8b,2);
DEFINE_PROPERTYKEY(PKEY_Devices_ModelName, 0x656a3bb3,0xecc0,0x43fd,0x84,0x77,0x4a,0xe0,0x40,0x4a,0x96,0xcd,8194);
DEFINE_PROPERTYKEY(PKEY_Devices_ModelNumber, 0x656a3bb3,0xecc0,0x43fd,0x84,0x77,0x4a,0xe0,0x40,0x4a,0x96,0xcd,8195);
DEFINE_PROPERTYKEY(PKEY_Devices_NetworkedTooltip, 0x880f70a2,0x6082,0x47ac,0x8a,0xab,0xa7,0x39,0xd1,0xa3,0x00,0xc3,152);
DEFINE_PROPERTYKEY(PKEY_Devices_NetworkName, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,7);
DEFINE_PROPERTYKEY(PKEY_Devices_NetworkType, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,8);
DEFINE_PROPERTYKEY(PKEY_Devices_NewPictures, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,4);
DEFINE_PROPERTYKEY(PKEY_Devices_Notification, 0x06704b0c,0xe830,0x4c81,0x91,0x78,0x91,0xe4,0xe9,0x5a,0x80,0xa0,3);
DEFINE_PROPERTYKEY(PKEY_Devices_Notifications_LowBattery, 0xc4c07f2b,0x8524,0x4e66,0xae,0x3a,0xa6,0x23,0x5f,0x10,0x3b,0xeb,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Notifications_MissedCall, 0x6614ef48,0x4efe,0x4424,0x9e,0xda,0xc7,0x9f,0x40,0x4e,0xdf,0x3e,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Notifications_NewMessage, 0x2be9260a,0x2012,0x4742,0xa5,0x55,0xf4,0x1b,0x63,0x8b,0x7d,0xcb,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Notifications_NewVoicemail, 0x59569556,0x0a08,0x4212,0x95,0xb9,0xfa,0xe2,0xad,0x64,0x13,0xdb,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Notifications_StorageFull, 0xa0e00ee1,0xf0c7,0x4d41,0xb8,0xe7,0x26,0xa7,0xbd,0x8d,0x38,0xb0,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Notifications_StorageFullLinkText, 0xa0e00ee1,0xf0c7,0x4d41,0xb8,0xe7,0x26,0xa7,0xbd,0x8d,0x38,0xb0,3);
DEFINE_PROPERTYKEY(PKEY_Devices_NotificationStore, 0x06704b0c,0xe830,0x4c81,0x91,0x78,0x91,0xe4,0xe9,0x5a,0x80,0xa0,2);
DEFINE_PROPERTYKEY(PKEY_Devices_NotWorkingProperly, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,83);
DEFINE_PROPERTYKEY(PKEY_Devices_Paired, 0x78c34fc8,0x104a,0x4aca,0x9e,0xa4,0x52,0x4d,0x52,0x99,0x6e,0x57,56);
DEFINE_PROPERTYKEY(PKEY_Devices_Panel_PanelGroup, 0x8dbc9c86,0x97a9,0x4bff,0x9b,0xc6,0xbf,0xe9,0x5d,0x3e,0x6d,0xad,3);
DEFINE_PROPERTYKEY(PKEY_Devices_Panel_PanelId, 0x8dbc9c86,0x97a9,0x4bff,0x9b,0xc6,0xbf,0xe9,0x5d,0x3e,0x6d,0xad,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Parent, 0x4340a6c5,0x93fa,0x4706,0x97,0x2c,0x7b,0x64,0x80,0x08,0xa5,0xa7,8);
DEFINE_PROPERTYKEY(PKEY_Devices_PhoneLineTransportDevice_Connected, 0xaecf2fe8,0x1d00,0x4fee,0x8a,0x6d,0xa7,0x0d,0x71,0x9b,0x77,0x2b,2);
DEFINE_PROPERTYKEY(PKEY_Devices_PhysicalDeviceLocation, 0x540b947e,0x8b40,0x45bc,0xa8,0xa2,0x6a,0x0b,0x89,0x4c,0xbd,0xa2,9);
DEFINE_PROPERTYKEY(PKEY_Devices_PlaybackPositionPercent, 0x3633de59,0x6825,0x4381,0xa4,0x9b,0x9f,0x6b,0xa1,0x3a,0x14,0x71,5);
DEFINE_PROPERTYKEY(PKEY_Devices_PlaybackState, 0x3633de59,0x6825,0x4381,0xa4,0x9b,0x9f,0x6b,0xa1,0x3a,0x14,0x71,2);
DEFINE_PROPERTYKEY(PKEY_Devices_PlaybackTitle, 0x3633de59,0x6825,0x4381,0xa4,0x9b,0x9f,0x6b,0xa1,0x3a,0x14,0x71,3);
DEFINE_PROPERTYKEY(PKEY_Devices_Present, 0x540b947e,0x8b40,0x45bc,0xa8,0xa2,0x6a,0x0b,0x89,0x4c,0xbd,0xa2,5);
DEFINE_PROPERTYKEY(PKEY_Devices_PresentationUrl, 0x656a3bb3,0xecc0,0x43fd,0x84,0x77,0x4a,0xe0,0x40,0x4a,0x96,0xcd,8198);
DEFINE_PROPERTYKEY(PKEY_Devices_PrimaryCategory, 0xd08dd4c0,0x3a9e,0x462e,0x82,0x90,0x7b,0x63,0x6b,0x25,0x76,0xb9,10);
DEFINE_PROPERTYKEY(PKEY_Devices_RemainingDuration, 0x3633de59,0x6825,0x4381,0xa4,0x9b,0x9f,0x6b,0xa1,0x3a,0x14,0x71,4);
DEFINE_PROPERTYKEY(PKEY_Devices_RestrictedInterface, 0x026e516e,0xb814,0x414b,0x83,0xcd,0x85,0x6d,0x6f,0xef,0x48,0x22,6);
DEFINE_PROPERTYKEY(PKEY_Devices_Roaming, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,9);
DEFINE_PROPERTYKEY(PKEY_Devices_SafeRemovalRequired, 0xafd97640,0x86a3,0x4210,0xb6,0x7c,0x28,0x9c,0x41,0xaa,0xbe,0x55,2);
DEFINE_PROPERTYKEY(PKEY_Devices_SchematicName, 0x026e516e,0xb814,0x414b,0x83,0xcd,0x85,0x6d,0x6f,0xef,0x48,0x22,9);
DEFINE_PROPERTYKEY(PKEY_Devices_ServiceAddress, 0x656a3bb3,0xecc0,0x43fd,0x84,0x77,0x4a,0xe0,0x40,0x4a,0x96,0xcd,16384);
DEFINE_PROPERTYKEY(PKEY_Devices_ServiceId, 0x656a3bb3,0xecc0,0x43fd,0x84,0x77,0x4a,0xe0,0x40,0x4a,0x96,0xcd,16385);
DEFINE_PROPERTYKEY(PKEY_Devices_SharedTooltip, 0x880f70a2,0x6082,0x47ac,0x8a,0xab,0xa7,0x39,0xd1,0xa3,0x00,0xc3,151);
DEFINE_PROPERTYKEY(PKEY_Devices_SignalStrength, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,2);
DEFINE_PROPERTYKEY(PKEY_Devices_SmartCards_ReaderKind, 0xd6b5b883,0x18bd,0x4b4d,0xb2,0xec,0x9e,0x38,0xaf,0xfe,0xda,0x82,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Status, 0xd08dd4c0,0x3a9e,0x462e,0x82,0x90,0x7b,0x63,0x6b,0x25,0x76,0xb9,259);
DEFINE_PROPERTYKEY(PKEY_Devices_Status1, 0xd08dd4c0,0x3a9e,0x462e,0x82,0x90,0x7b,0x63,0x6b,0x25,0x76,0xb9,257);
DEFINE_PROPERTYKEY(PKEY_Devices_Status2, 0xd08dd4c0,0x3a9e,0x462e,0x82,0x90,0x7b,0x63,0x6b,0x25,0x76,0xb9,258);
DEFINE_PROPERTYKEY(PKEY_Devices_StorageCapacity, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,12);
DEFINE_PROPERTYKEY(PKEY_Devices_StorageFreeSpace, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,13);
DEFINE_PROPERTYKEY(PKEY_Devices_StorageFreeSpacePercent, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,14);
DEFINE_PROPERTYKEY(PKEY_Devices_TextMessages, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,3);
DEFINE_PROPERTYKEY(PKEY_Devices_Voicemail, 0x49cd1f76,0x5626,0x4b17,0xa4,0xe8,0x18,0xb4,0xaa,0x1a,0x22,0x13,6);
DEFINE_PROPERTYKEY(PKEY_Devices_WiaDeviceType, 0x6bdd1fc6,0x810f,0x11d0,0xbe,0xc7,0x08,0x00,0x2b,0xe2,0x09,0x2f,2);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFi_InterfaceGuid, 0xef1167eb,0xcbfc,0x4341,0xa5,0x68,0xa7,0xc9,0x1a,0x68,0x98,0x2c,2);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_DeviceAddress, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,13);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_GroupId, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,4);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_InformationElements, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,12);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_InterfaceAddress, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,2);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_InterfaceGuid, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,3);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_IsConnected, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,5);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_IsLegacyDevice, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,7);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_IsMiracastLcpSupported, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,9);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_IsVisible, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,6);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_MiracastVersion, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,8);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_Services, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,10);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirect_SupportedChannelList, 0x1506935d,0xe3e7,0x450f,0x86,0x37,0x82,0x23,0x3e,0xbe,0x5f,0x6e,11);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirectServices_AdvertisementId, 0x31b37743,0x7c5e,0x4005,0x93,0xe6,0xe9,0x53,0xf9,0x2b,0x82,0xe9,5);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirectServices_RequestServiceInformation, 0x31b37743,0x7c5e,0x4005,0x93,0xe6,0xe9,0x53,0xf9,0x2b,0x82,0xe9,7);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirectServices_ServiceAddress, 0x31b37743,0x7c5e,0x4005,0x93,0xe6,0xe9,0x53,0xf9,0x2b,0x82,0xe9,2);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirectServices_ServiceConfigMethods, 0x31b37743,0x7c5e,0x4005,0x93,0xe6,0xe9,0x53,0xf9,0x2b,0x82,0xe9,6);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirectServices_ServiceInformation, 0x31b37743,0x7c5e,0x4005,0x93,0xe6,0xe9,0x53,0xf9,0x2b,0x82,0xe9,4);
DEFINE_PROPERTYKEY(PKEY_Devices_WiFiDirectServices_ServiceName, 0x31b37743,0x7c5e,0x4005,0x93,0xe6,0xe9,0x53,0xf9,0x2b,0x82,0xe9,3);
DEFINE_PROPERTYKEY(PKEY_Devices_WinPhone8CameraFlags, 0xb7b4d61c,0x5a64,0x4187,0xa5,0x2e,0xb1,0x53,0x9f,0x35,0x90,0x99,2);
DEFINE_PROPERTYKEY(PKEY_Devices_Wwan_InterfaceGuid, 0xff1167eb,0xcbfc,0x4341,0xa5,0x68,0xa7,0xc9,0x1a,0x68,0x98,0x2c,2);
DEFINE_PROPERTYKEY(PKEY_Storage_Portable, 0x4d1ebee8,0x0803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x02,0x65,0xe9,2);
DEFINE_PROPERTYKEY(PKEY_Storage_RemovableMedia, 0x4d1ebee8,0x0803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x02,0x65,0xe9,3);
DEFINE_PROPERTYKEY(PKEY_Storage_SystemCritical, 0x4d1ebee8,0x0803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x02,0x65,0xe9,4);
DEFINE_PROPERTYKEY(PKEY_Document_ByteCount, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,4);
DEFINE_PROPERTYKEY(PKEY_Document_CharacterCount, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,16);
DEFINE_PROPERTYKEY(PKEY_Document_ClientID, 0x276d7bb0,0x5b34,0x4fb0,0xaa,0x4b,0x15,0x8e,0xd1,0x2a,0x18,0x09,100);
DEFINE_PROPERTYKEY(PKEY_Document_Contributor, 0xf334115e,0xda1b,0x4509,0x9b,0x3d,0x11,0x95,0x04,0xdc,0x7a,0xbb,100);
DEFINE_PROPERTYKEY(PKEY_Document_DateCreated, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,12);
DEFINE_PROPERTYKEY(PKEY_Document_DatePrinted, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,11);
DEFINE_PROPERTYKEY(PKEY_Document_DateSaved, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,13);
DEFINE_PROPERTYKEY(PKEY_Document_Division, 0x1e005ee6,0xbf27,0x428b,0xb0,0x1c,0x79,0x67,0x6a,0xcd,0x28,0x70,100);
DEFINE_PROPERTYKEY(PKEY_Document_DocumentID, 0xe08805c8,0xe395,0x40df,0x80,0xd2,0x54,0xf0,0xd6,0xc4,0x31,0x54,100);
DEFINE_PROPERTYKEY(PKEY_Document_HiddenSlideCount, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,9);
DEFINE_PROPERTYKEY(PKEY_Document_LastAuthor, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,8);
DEFINE_PROPERTYKEY(PKEY_Document_LineCount, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,5);
DEFINE_PROPERTYKEY(PKEY_Document_Manager, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,14);
DEFINE_PROPERTYKEY(PKEY_Document_MultimediaClipCount, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,10);
DEFINE_PROPERTYKEY(PKEY_Document_NoteCount, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,8);
DEFINE_PROPERTYKEY(PKEY_Document_PageCount, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,14);
DEFINE_PROPERTYKEY(PKEY_Document_ParagraphCount, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,6);
DEFINE_PROPERTYKEY(PKEY_Document_PresentationFormat, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,3);
DEFINE_PROPERTYKEY(PKEY_Document_RevisionNumber, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,9);
DEFINE_PROPERTYKEY(PKEY_Document_Security, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,19);
DEFINE_PROPERTYKEY(PKEY_Document_SlideCount, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,7);
DEFINE_PROPERTYKEY(PKEY_Document_Template, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,7);
DEFINE_PROPERTYKEY(PKEY_Document_TotalEditingTime, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,10);
DEFINE_PROPERTYKEY(PKEY_Document_Version, 0xd5cdd502,0x2e9c,0x101b,0x93,0x97,0x08,0x00,0x2b,0x2c,0xf9,0xae,29);
DEFINE_PROPERTYKEY(PKEY_Document_WordCount, 0xf29f85e0,0x4ff9,0x1068,0xab,0x91,0x08,0x00,0x2b,0x27,0xb3,0xd9,15);
DEFINE_PROPERTYKEY(PKEY_DRM_DatePlayExpires, 0xaeac19e4,0x89ae,0x4508,0xb9,0xb7,0xbb,0x86,0x7a,0xbe,0xe2,0xed,6);
DEFINE_PROPERTYKEY(PKEY_DRM_DatePlayStarts, 0xaeac19e4,0x89ae,0x4508,0xb9,0xb7,0xbb,0x86,0x7a,0xbe,0xe2,0xed,5);
DEFINE_PROPERTYKEY(PKEY_DRM_Description, 0xaeac19e4,0x89ae,0x4508,0xb9,0xb7,0xbb,0x86,0x7a,0xbe,0xe2,0xed,3);
DEFINE_PROPERTYKEY(PKEY_DRM_IsDisabled, 0xaeac19e4,0x89ae,0x4508,0xb9,0xb7,0xbb,0x86,0x7a,0xbe,0xe2,0xed,7);
DEFINE_PROPERTYKEY(PKEY_DRM_IsProtected, 0xaeac19e4,0x89ae,0x4508,0xb9,0xb7,0xbb,0x86,0x7a,0xbe,0xe2,0xed,2);
DEFINE_PROPERTYKEY(PKEY_DRM_PlayCount, 0xaeac19e4,0x89ae,0x4508,0xb9,0xb7,0xbb,0x86,0x7a,0xbe,0xe2,0xed,4);
DEFINE_PROPERTYKEY(PKEY_GPS_Altitude, 0x827edb4f,0x5b73,0x44a7,0x89,0x1d,0xfd,0xff,0xab,0xea,0x35,0xca,100);
DEFINE_PROPERTYKEY(PKEY_GPS_AltitudeDenominator, 0x78342dcb,0xe358,0x4145,0xae,0x9a,0x6b,0xfe,0x4e,0x0f,0x9f,0x51,100);
DEFINE_PROPERTYKEY(PKEY_GPS_AltitudeNumerator, 0x2dad1eb7,0x816d,0x40d3,0x9e,0xc3,0xc9,0x77,0x3b,0xe2,0xaa,0xde,100);
DEFINE_PROPERTYKEY(PKEY_GPS_AltitudeRef, 0x46ac629d,0x75ea,0x4515,0x86,0x7f,0x6d,0xc4,0x32,0x1c,0x58,0x44,100);
DEFINE_PROPERTYKEY(PKEY_GPS_AreaInformation, 0x972e333e,0xac7e,0x49f1,0x8a,0xdf,0xa7,0x0d,0x07,0xa9,0xbc,0xab,100);
DEFINE_PROPERTYKEY(PKEY_GPS_Date, 0x3602c812,0x0f3b,0x45f0,0x85,0xad,0x60,0x34,0x68,0xd6,0x94,0x23,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestBearing, 0xc66d4b3c,0xe888,0x47cc,0xb9,0x9f,0x9d,0xca,0x3e,0xe3,0x4d,0xea,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestBearingDenominator, 0x7abcf4f8,0x7c3f,0x4988,0xac,0x91,0x8d,0x2c,0x2e,0x97,0xec,0xa5,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestBearingNumerator, 0xba3b1da9,0x86ee,0x4b5d,0xa2,0xa4,0xa2,0x71,0xa4,0x29,0xf0,0xcf,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestBearingRef, 0x9ab84393,0x2a0f,0x4b75,0xbb,0x22,0x72,0x79,0x78,0x69,0x77,0xcb,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestDistance, 0xa93eae04,0x6804,0x4f24,0xac,0x81,0x09,0xb2,0x66,0x45,0x21,0x18,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestDistanceDenominator, 0x9bc2c99b,0xac71,0x4127,0x9d,0x1c,0x25,0x96,0xd0,0xd7,0xdc,0xb7,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestDistanceNumerator, 0x2bda47da,0x08c6,0x4fe1,0x80,0xbc,0xa7,0x2f,0xc5,0x17,0xc5,0xd0,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestDistanceRef, 0xed4df2d3,0x8695,0x450b,0x85,0x6f,0xf5,0xc1,0xc5,0x3a,0xcb,0x66,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestLatitude, 0x9d1d7cc5,0x5c39,0x451c,0x86,0xb3,0x92,0x8e,0x2d,0x18,0xcc,0x47,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestLatitudeDenominator, 0x3a372292,0x7fca,0x49a7,0x99,0xd5,0xe4,0x7b,0xb2,0xd4,0xe7,0xab,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestLatitudeNumerator, 0xecf4b6f6,0xd5a6,0x433c,0xbb,0x92,0x40,0x76,0x65,0x0f,0xc8,0x90,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestLatitudeRef, 0xcea820b9,0xce61,0x4885,0xa1,0x28,0x00,0x5d,0x90,0x87,0xc1,0x92,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestLongitude, 0x47a96261,0xcb4c,0x4807,0x8a,0xd3,0x40,0xb9,0xd9,0xdb,0xc6,0xbc,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestLongitudeDenominator, 0x425d69e5,0x48ad,0x4900,0x8d,0x80,0x6e,0xb6,0xb8,0xd0,0xac,0x86,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestLongitudeNumerator, 0xa3250282,0xfb6d,0x48d5,0x9a,0x89,0xdb,0xca,0xce,0x75,0xcc,0xcf,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DestLongitudeRef, 0x182c1ea6,0x7c1c,0x4083,0xab,0x4b,0xac,0x6c,0x9f,0x4e,0xd1,0x28,100);
DEFINE_PROPERTYKEY(PKEY_GPS_Differential, 0xaaf4ee25,0xbd3b,0x4dd7,0xbf,0xc4,0x47,0xf7,0x7b,0xb0,0x0f,0x6d,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DOP, 0x0cf8fb02,0x1837,0x42f1,0xa6,0x97,0xa7,0x01,0x7a,0xa2,0x89,0xb9,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DOPDenominator, 0xa0be94c5,0x50ba,0x487b,0xbd,0x35,0x06,0x54,0xbe,0x88,0x81,0xed,100);
DEFINE_PROPERTYKEY(PKEY_GPS_DOPNumerator, 0x47166b16,0x364f,0x4aa0,0x9f,0x31,0xe2,0xab,0x3d,0xf4,0x49,0xc3,100);
DEFINE_PROPERTYKEY(PKEY_GPS_ImgDirection, 0x16473c91,0xd017,0x4ed9,0xba,0x4d,0xb6,0xba,0xa5,0x5d,0xbc,0xf8,100);
DEFINE_PROPERTYKEY(PKEY_GPS_ImgDirectionDenominator, 0x10b24595,0x41a2,0x4e20,0x93,0xc2,0x57,0x61,0xc1,0x39,0x5f,0x32,100);
DEFINE_PROPERTYKEY(PKEY_GPS_ImgDirectionNumerator, 0xdc5877c7,0x225f,0x45f7,0xba,0xc7,0xe8,0x13,0x34,0xb6,0x13,0x0a,100);
DEFINE_PROPERTYKEY(PKEY_GPS_ImgDirectionRef, 0xa4aaa5b7,0x1ad0,0x445f,0x81,0x1a,0x0f,0x8f,0x6e,0x67,0xf6,0xb5,100);
DEFINE_PROPERTYKEY(PKEY_GPS_Latitude, 0x8727cfff,0x4868,0x4ec6,0xad,0x5b,0x81,0xb9,0x85,0x21,0xd1,0xab,100);
DEFINE_PROPERTYKEY(PKEY_GPS_LatitudeDecimal, 0x0f55cde2,0x4f49,0x450d,0x92,0xc1,0xdc,0xd1,0x63,0x01,0xb1,0xb7,100);
DEFINE_PROPERTYKEY(PKEY_GPS_LatitudeDenominator, 0x16e634ee,0x2bff,0x497b,0xbd,0x8a,0x43,0x41,0xad,0x39,0xee,0xb9,100);
DEFINE_PROPERTYKEY(PKEY_GPS_LatitudeNumerator, 0x7ddaaad1,0xccc8,0x41ae,0xb7,0x50,0xb2,0xcb,0x80,0x31,0xae,0xa2,100);
DEFINE_PROPERTYKEY(PKEY_GPS_LatitudeRef, 0x029c0252,0x5b86,0x46c7,0xac,0xa0,0x27,0x69,0xff,0xc8,0xe3,0xd4,100);
DEFINE_PROPERTYKEY(PKEY_GPS_Longitude, 0xc4c4dbb2,0xb593,0x466b,0xbb,0xda,0xd0,0x3d,0x27,0xd5,0xe4,0x3a,100);
DEFINE_PROPERTYKEY(PKEY_GPS_LongitudeDecimal, 0x4679c1b5,0x844d,0x4590,0xba,0xf5,0xf3,0x22,0x23,0x1f,0x1b,0x81,100);
DEFINE_PROPERTYKEY(PKEY_GPS_LongitudeDenominator, 0xbe6e176c,0x4534,0x4d2c,0xac,0xe5,0x31,0xde,0xda,0xc1,0x60,0x6b,100);
DEFINE_PROPERTYKEY(PKEY_GPS_LongitudeNumerator, 0x02b0f689,0xa914,0x4e45,0x82,0x1d,0x1d,0xda,0x45,0x2e,0xd2,0xc4,100);
DEFINE_PROPERTYKEY(PKEY_GPS_LongitudeRef, 0x33dcf22b,0x28d5,0x464c,0x80,0x35,0x1e,0xe9,0xef,0xd2,0x52,0x78,100);
DEFINE_PROPERTYKEY(PKEY_GPS_MapDatum, 0x2ca2dae6,0xeddc,0x407d,0xbe,0xf1,0x77,0x39,0x42,0xab,0xfa,0x95,100);
DEFINE_PROPERTYKEY(PKEY_GPS_MeasureMode, 0xa015ed5d,0xaaea,0x4d58,0x8a,0x86,0x3c,0x58,0x69,0x20,0xea,0x0b,100);
DEFINE_PROPERTYKEY(PKEY_GPS_ProcessingMethod, 0x59d49e61,0x840f,0x4aa9,0xa9,0x39,0xe2,0x09,0x9b,0x7f,0x63,0x99,100);
DEFINE_PROPERTYKEY(PKEY_GPS_Satellites, 0x467ee575,0x1f25,0x4557,0xad,0x4e,0xb8,0xb5,0x8b,0x0d,0x9c,0x15,100);
DEFINE_PROPERTYKEY(PKEY_GPS_Speed, 0xda5d0862,0x6e76,0x4e1b,0xba,0xbd,0x70,0x02,0x1b,0xd2,0x54,0x94,100);
DEFINE_PROPERTYKEY(PKEY_GPS_SpeedDenominator, 0x7d122d5a,0xae5e,0x4335,0x88,0x41,0xd7,0x1e,0x7c,0xe7,0x2f,0x53,100);
DEFINE_PROPERTYKEY(PKEY_GPS_SpeedNumerator, 0xacc9ce3d,0xc213,0x4942,0x8b,0x48,0x6d,0x08,0x20,0xf2,0x1c,0x6d,100);
DEFINE_PROPERTYKEY(PKEY_GPS_SpeedRef, 0xecf7f4c9,0x544f,0x4d6d,0x9d,0x98,0x8a,0xd7,0x9a,0xda,0xf4,0x53,100);
DEFINE_PROPERTYKEY(PKEY_GPS_Status, 0x125491f4,0x818f,0x46b2,0x91,0xb5,0xd5,0x37,0x75,0x36,0x17,0xb2,100);
DEFINE_PROPERTYKEY(PKEY_GPS_Track, 0x76c09943,0x7c33,0x49e3,0x9e,0x7e,0xcd,0xba,0x87,0x2c,0xfa,0xda,100);
DEFINE_PROPERTYKEY(PKEY_GPS_TrackDenominator, 0xc8d1920c,0x01f6,0x40c0,0xac,0x86,0x2f,0x3a,0x4a,0xd0,0x07,0x70,100);
DEFINE_PROPERTYKEY(PKEY_GPS_TrackNumerator, 0x702926f4,0x44a6,0x43e1,0xae,0x71,0x45,0x62,0x71,0x16,0x89,0x3b,100);
DEFINE_PROPERTYKEY(PKEY_GPS_TrackRef, 0x35dbe6fe,0x44c3,0x4400,0xaa,0xae,0xd2,0xc7,0x99,0xc4,0x07,0xe8,100);
DEFINE_PROPERTYKEY(PKEY_GPS_VersionID, 0x22704da4,0xc6b2,0x4a99,0x8e,0x56,0xf1,0x6d,0xf8,0xc9,0x25,0x99,100);
DEFINE_PROPERTYKEY(PKEY_History_VisitCount, 0x5cbf2787,0x48cf,0x4208,0xb9,0x0e,0xee,0x5e,0x5d,0x42,0x02,0x94,7);
DEFINE_PROPERTYKEY(PKEY_Image_BitDepth, 0x6444048f,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,7);
DEFINE_PROPERTYKEY(PKEY_Image_ColorSpace, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,40961);
DEFINE_PROPERTYKEY(PKEY_Image_CompressedBitsPerPixel, 0x364b6fa9,0x37ab,0x482a,0xbe,0x2b,0xae,0x02,0xf6,0x0d,0x43,0x18,100);
DEFINE_PROPERTYKEY(PKEY_Image_CompressedBitsPerPixelDenominator, 0x1f8844e1,0x24ad,0x4508,0x9d,0xfd,0x53,0x26,0xa4,0x15,0xce,0x02,100);
DEFINE_PROPERTYKEY(PKEY_Image_CompressedBitsPerPixelNumerator, 0xd21a7148,0xd32c,0x4624,0x89,0x00,0x27,0x72,0x10,0xf7,0x9c,0x0f,100);
DEFINE_PROPERTYKEY(PKEY_Image_Compression, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,259);
DEFINE_PROPERTYKEY(PKEY_Image_CompressionText, 0x3f08e66f,0x2f44,0x4bb9,0xa6,0x82,0xac,0x35,0xd2,0x56,0x23,0x22,100);
DEFINE_PROPERTYKEY(PKEY_Image_Dimensions, 0x6444048f,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,13);
DEFINE_PROPERTYKEY(PKEY_Image_HorizontalResolution, 0x6444048f,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,5);
DEFINE_PROPERTYKEY(PKEY_Image_HorizontalSize, 0x6444048f,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,3);
DEFINE_PROPERTYKEY(PKEY_Image_ImageID, 0x10dabe05,0x32aa,0x4c29,0xbf,0x1a,0x63,0xe2,0xd2,0x20,0x58,0x7f,100);
DEFINE_PROPERTYKEY(PKEY_Image_ResolutionUnit, 0x19b51fa6,0x1f92,0x4a5c,0xab,0x48,0x7d,0xf0,0xab,0xd6,0x74,0x44,100);
DEFINE_PROPERTYKEY(PKEY_Image_VerticalResolution, 0x6444048f,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,6);
DEFINE_PROPERTYKEY(PKEY_Image_VerticalSize, 0x6444048f,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,4);
DEFINE_PROPERTYKEY(PKEY_Journal_Contacts, 0xdea7c82c,0x1d89,0x4a66,0x94,0x27,0xa4,0xe3,0xde,0xba,0xbc,0xb1,100);
DEFINE_PROPERTYKEY(PKEY_Journal_EntryType, 0x95beb1fc,0x326d,0x4644,0xb3,0x96,0xcd,0x3e,0xd9,0x0e,0x6d,0xdf,100);
DEFINE_PROPERTYKEY(PKEY_LayoutPattern_ContentViewModeForBrowse, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,500);
DEFINE_PROPERTYKEY(PKEY_LayoutPattern_ContentViewModeForSearch, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,501);
DEFINE_PROPERTYKEY(PKEY_History_SelectionCount, 0x1ce0d6bc,0x536c,0x4600,0xb0,0xdd,0x7e,0x0c,0x66,0xb3,0x50,0xd5,8);
DEFINE_PROPERTYKEY(PKEY_History_TargetUrlHostName, 0x1ce0d6bc,0x536c,0x4600,0xb0,0xdd,0x7e,0x0c,0x66,0xb3,0x50,0xd5,9);
DEFINE_PROPERTYKEY(PKEY_Link_Arguments, 0x436f2667,0x14e2,0x4feb,0xb3,0x0a,0x14,0x6c,0x53,0xb5,0xb6,0x74,100);
DEFINE_PROPERTYKEY(PKEY_Link_Comment, 0xb9b4b3fc,0x2b51,0x4a42,0xb5,0xd8,0x32,0x41,0x46,0xaf,0xcf,0x25,5);
DEFINE_PROPERTYKEY(PKEY_Link_DateVisited, 0x5cbf2787,0x48cf,0x4208,0xb9,0x0e,0xee,0x5e,0x5d,0x42,0x02,0x94,23);
DEFINE_PROPERTYKEY(PKEY_Link_Description, 0x5cbf2787,0x48cf,0x4208,0xb9,0x0e,0xee,0x5e,0x5d,0x42,0x02,0x94,21);
DEFINE_PROPERTYKEY(PKEY_Link_FeedItemLocalId, 0x8a2f99f9,0x3c37,0x465d,0xa8,0xd7,0x69,0x77,0x7a,0x24,0x6d,0x0c,2);
DEFINE_PROPERTYKEY(PKEY_Link_Status, 0xb9b4b3fc,0x2b51,0x4a42,0xb5,0xd8,0x32,0x41,0x46,0xaf,0xcf,0x25,3);
DEFINE_PROPERTYKEY(PKEY_Link_TargetExtension, 0x7a7d76f4,0xb630,0x4bd7,0x95,0xff,0x37,0xcc,0x51,0xa9,0x75,0xc9,2);
DEFINE_PROPERTYKEY(PKEY_Link_TargetParsingPath, 0xb9b4b3fc,0x2b51,0x4a42,0xb5,0xd8,0x32,0x41,0x46,0xaf,0xcf,0x25,2);
DEFINE_PROPERTYKEY(PKEY_Link_TargetSFGAOFlags, 0xb9b4b3fc,0x2b51,0x4a42,0xb5,0xd8,0x32,0x41,0x46,0xaf,0xcf,0x25,8);
DEFINE_PROPERTYKEY(PKEY_Link_TargetUrlHostName, 0x8a2f99f9,0x3c37,0x465d,0xa8,0xd7,0x69,0x77,0x7a,0x24,0x6d,0x0c,5);
DEFINE_PROPERTYKEY(PKEY_Link_TargetUrlPath, 0x8a2f99f9,0x3c37,0x465d,0xa8,0xd7,0x69,0x77,0x7a,0x24,0x6d,0x0c,6);
DEFINE_PROPERTYKEY(PKEY_Media_AuthorUrl, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,32);
DEFINE_PROPERTYKEY(PKEY_Media_AverageLevel, 0x09edd5b6,0xb301,0x43c5,0x99,0x90,0xd0,0x03,0x02,0xef,0xfd,0x46,100);
DEFINE_PROPERTYKEY(PKEY_Media_ClassPrimaryID, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,13);
DEFINE_PROPERTYKEY(PKEY_Media_ClassSecondaryID, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,14);
DEFINE_PROPERTYKEY(PKEY_Media_CollectionGroupID, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,24);
DEFINE_PROPERTYKEY(PKEY_Media_CollectionID, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,25);
DEFINE_PROPERTYKEY(PKEY_Media_ContentDistributor, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,18);
DEFINE_PROPERTYKEY(PKEY_Media_ContentID, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,26);
DEFINE_PROPERTYKEY(PKEY_Media_CreatorApplication, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,27);
DEFINE_PROPERTYKEY(PKEY_Media_CreatorApplicationVersion, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,28);
DEFINE_PROPERTYKEY(PKEY_Media_DateEncoded, 0x2e4b640d,0x5019,0x46d8,0x88,0x81,0x55,0x41,0x4c,0xc5,0xca,0xa0,100);
DEFINE_PROPERTYKEY(PKEY_Media_DateReleased, 0xde41cc29,0x6971,0x4290,0xb4,0x72,0xf5,0x9f,0x2e,0x2f,0x31,0xe2,100);
DEFINE_PROPERTYKEY(PKEY_Media_DlnaProfileID, 0xcfa31b45,0x525d,0x4998,0xbb,0x44,0x3f,0x7d,0x81,0x54,0x2f,0xa4,100);
DEFINE_PROPERTYKEY(PKEY_Media_Duration, 0x64440490,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,3);
DEFINE_PROPERTYKEY(PKEY_Media_DVDID, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,15);
DEFINE_PROPERTYKEY(PKEY_Media_EncodedBy, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,36);
DEFINE_PROPERTYKEY(PKEY_Media_EncodingSettings, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,37);
DEFINE_PROPERTYKEY(PKEY_Media_EpisodeNumber, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,100);
DEFINE_PROPERTYKEY(PKEY_Media_FrameCount, 0x6444048f,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,12);
DEFINE_PROPERTYKEY(PKEY_Media_MCDI, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,16);
DEFINE_PROPERTYKEY(PKEY_Media_MetadataContentProvider, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,17);
DEFINE_PROPERTYKEY(PKEY_Media_Producer, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,22);
DEFINE_PROPERTYKEY(PKEY_Media_PromotionUrl, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,33);
DEFINE_PROPERTYKEY(PKEY_Media_ProtectionType, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,38);
DEFINE_PROPERTYKEY(PKEY_Media_ProviderRating, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,39);
DEFINE_PROPERTYKEY(PKEY_Media_ProviderStyle, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,40);
DEFINE_PROPERTYKEY(PKEY_Media_Publisher, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,30);
DEFINE_PROPERTYKEY(PKEY_Media_SeasonNumber, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,101);
DEFINE_PROPERTYKEY(PKEY_Media_SeriesName, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,42);
DEFINE_PROPERTYKEY(PKEY_Media_SubscriptionContentId, 0x9aebae7a,0x9644,0x487d,0xa9,0x2c,0x65,0x75,0x85,0xed,0x75,0x1a,100);
DEFINE_PROPERTYKEY(PKEY_Media_SubTitle, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,38);
DEFINE_PROPERTYKEY(PKEY_Media_ThumbnailLargePath, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,47);
DEFINE_PROPERTYKEY(PKEY_Media_ThumbnailLargeUri, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,48);
DEFINE_PROPERTYKEY(PKEY_Media_ThumbnailSmallPath, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,49);
DEFINE_PROPERTYKEY(PKEY_Media_ThumbnailSmallUri, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,50);
DEFINE_PROPERTYKEY(PKEY_Media_UniqueFileIdentifier, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,35);
DEFINE_PROPERTYKEY(PKEY_Media_UserNoAutoInfo, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,41);
DEFINE_PROPERTYKEY(PKEY_Media_UserWebUrl, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,34);
DEFINE_PROPERTYKEY(PKEY_Media_Writer, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,23);
DEFINE_PROPERTYKEY(PKEY_Media_Year, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,5);

DEFINE_PROPERTYKEY(PKEY_Message_AttachmentContents, 0x3143bf7c,0x80a8,0x4854,0x88,0x80,0xe2,0xe4,0x01,0x89,0xbd,0xd0,100);
DEFINE_PROPERTYKEY(PKEY_Message_AttachmentNames, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,21);
DEFINE_PROPERTYKEY(PKEY_Message_BccAddress, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,2);
DEFINE_PROPERTYKEY(PKEY_Message_BccName, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,3);
DEFINE_PROPERTYKEY(PKEY_Message_CcAddress, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,4);
DEFINE_PROPERTYKEY(PKEY_Message_CcName, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,5);
DEFINE_PROPERTYKEY(PKEY_Message_ConversationID, 0xdc8f80bd,0xaf1e,0x4289,0x85,0xb6,0x3d,0xfc,0x1b,0x49,0x39,0x92,100);
DEFINE_PROPERTYKEY(PKEY_Message_ConversationIndex, 0xdc8f80bd,0xaf1e,0x4289,0x85,0xb6,0x3d,0xfc,0x1b,0x49,0x39,0x92,101);
DEFINE_PROPERTYKEY(PKEY_Message_DateReceived, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,20);
DEFINE_PROPERTYKEY(PKEY_Message_DateSent, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,19);
DEFINE_PROPERTYKEY(PKEY_Message_Flags, 0xa82d9ee7,0xca67,0x4312,0x96,0x5e,0x22,0x6b,0xce,0xa8,0x50,0x23,100);
DEFINE_PROPERTYKEY(PKEY_Message_FromAddress, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,13);
DEFINE_PROPERTYKEY(PKEY_Message_FromName, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,14);
DEFINE_PROPERTYKEY(PKEY_Message_HasAttachments, 0x9c1fcf74,0x2d97,0x41ba,0xb4,0xae,0xcb,0x2e,0x36,0x61,0xa6,0xe4,8);
DEFINE_PROPERTYKEY(PKEY_Message_IsFwdOrReply, 0x9a9bc088,0x4f6d,0x469e,0x99,0x19,0xe7,0x05,0x41,0x20,0x40,0xf9,100);
DEFINE_PROPERTYKEY(PKEY_Message_MessageClass, 0xcd9ed458,0x08ce,0x418f,0xa7,0x0e,0xf9,0x12,0xc7,0xbb,0x9c,0x5c,103);
DEFINE_PROPERTYKEY(PKEY_Message_Participants, 0x1a9ba605,0x8e7c,0x4d11,0xad,0x7d,0xa5,0x0a,0xda,0x18,0xba,0x1b,2);
DEFINE_PROPERTYKEY(PKEY_Message_ProofInProgress, 0x9098f33c,0x9a7d,0x48a8,0x8d,0xe5,0x2e,0x12,0x27,0xa6,0x4e,0x91,100);
DEFINE_PROPERTYKEY(PKEY_Message_SenderAddress, 0x0be1c8e7,0x1981,0x4676,0xae,0x14,0xfd,0xd7,0x8f,0x05,0xa6,0xe7,100);
DEFINE_PROPERTYKEY(PKEY_Message_SenderName, 0x0da41cfa,0xd224,0x4a18,0xae,0x2f,0x59,0x61,0x58,0xdb,0x4b,0x3a,100);
DEFINE_PROPERTYKEY(PKEY_Message_Store, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,15);
DEFINE_PROPERTYKEY(PKEY_Message_ToAddress, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,16);
DEFINE_PROPERTYKEY(PKEY_Message_ToDoFlags, 0x1f856a9f,0x6900,0x4aba,0x95,0x05,0x2d,0x5f,0x1b,0x4d,0x66,0xcb,100);
DEFINE_PROPERTYKEY(PKEY_Message_ToDoTitle, 0xbccc8a3c,0x8cef,0x42e5,0x9b,0x1c,0xc6,0x90,0x79,0x39,0x8b,0xc7,100);
DEFINE_PROPERTYKEY(PKEY_Message_ToName, 0xe3e0584c,0xb788,0x4a5a,0xbb,0x20,0x7f,0x5a,0x44,0xc9,0xac,0xdd,17);

DEFINE_PROPERTYKEY(PKEY_MsGraph_ActivityType, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,14);
DEFINE_PROPERTYKEY(PKEY_MsGraph_CompositeId, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,2);
DEFINE_PROPERTYKEY(PKEY_MsGraph_DateLastShared, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,9);
DEFINE_PROPERTYKEY(PKEY_MsGraph_DriveId, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,3);
DEFINE_PROPERTYKEY(PKEY_MsGraph_IconUrl, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,15);
DEFINE_PROPERTYKEY(PKEY_MsGraph_ItemId, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,4);
DEFINE_PROPERTYKEY(PKEY_MsGraph_PrimaryActivityActorDisplayName, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,13);
DEFINE_PROPERTYKEY(PKEY_MsGraph_PrimaryActivityActorUpn, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,12);
DEFINE_PROPERTYKEY(PKEY_MsGraph_RecommendationReason, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,8);
DEFINE_PROPERTYKEY(PKEY_MsGraph_RecommendationReferenceId, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,5);
DEFINE_PROPERTYKEY(PKEY_MsGraph_RecommendationResultSourceId, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,7);
DEFINE_PROPERTYKEY(PKEY_MsGraph_SharedByEmail, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,11);
DEFINE_PROPERTYKEY(PKEY_MsGraph_SharedByName, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,10);
DEFINE_PROPERTYKEY(PKEY_MsGraph_WebAccountId, 0x4f85567e,0xfff0,0x4df5,0xb1,0xd9,0x98,0xb3,0x14,0xff,0x07,0x29,6);

DEFINE_PROPERTYKEY(PKEY_Music_AlbumArtist, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,13);
DEFINE_PROPERTYKEY(PKEY_Music_AlbumArtistSortOverride, 0xf1fdb4af,0xf78c,0x466c,0xbb,0x05,0x56,0xe9,0x2d,0xb0,0xb8,0xec,103);
DEFINE_PROPERTYKEY(PKEY_Music_AlbumID, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,100);
DEFINE_PROPERTYKEY(PKEY_Music_AlbumTitle, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,4);
DEFINE_PROPERTYKEY(PKEY_Music_AlbumTitleSortOverride, 0x13eb7ffc,0xec89,0x4346,0xb1,0x9d,0xcc,0xc6,0xf1,0x78,0x42,0x23,101);
DEFINE_PROPERTYKEY(PKEY_Music_Artist, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,2);
DEFINE_PROPERTYKEY(PKEY_Music_ArtistSortOverride, 0xdeeb2db5,0x0696,0x4ce0,0x94,0xfe,0xa0,0x1f,0x77,0xa4,0x5f,0xb5,102);
DEFINE_PROPERTYKEY(PKEY_Music_BeatsPerMinute, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,35);
DEFINE_PROPERTYKEY(PKEY_Music_Composer, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,19);
DEFINE_PROPERTYKEY(PKEY_Music_ComposerSortOverride, 0x00bc20a3,0xbd48,0x4085,0x87,0x2c,0xa8,0x8d,0x77,0xf5,0x09,0x7e,105);
DEFINE_PROPERTYKEY(PKEY_Music_Conductor, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,36);
DEFINE_PROPERTYKEY(PKEY_Music_ContentGroupDescription, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,33);
DEFINE_PROPERTYKEY(PKEY_Music_DiscNumber, 0x6afe7437,0x9bcd,0x49c7,0x80,0xfe,0x4a,0x5c,0x65,0xfa,0x58,0x74,104);
DEFINE_PROPERTYKEY(PKEY_Music_DisplayArtist, 0xfd122953,0xfa93,0x4ef7,0x92,0xc3,0x04,0xc9,0x46,0xb2,0xf7,0xc8,100);
DEFINE_PROPERTYKEY(PKEY_Music_Genre, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,11);
DEFINE_PROPERTYKEY(PKEY_Music_InitialKey, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,34);
DEFINE_PROPERTYKEY(PKEY_Music_IsCompilation, 0xc449d5cb,0x9ea4,0x4809,0x82,0xe8,0xaf,0x9d,0x59,0xde,0xd6,0xd1,100);
DEFINE_PROPERTYKEY(PKEY_Music_Lyrics, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,12);
DEFINE_PROPERTYKEY(PKEY_Music_Mood, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,39);
DEFINE_PROPERTYKEY(PKEY_Music_PartOfSet, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,37);
DEFINE_PROPERTYKEY(PKEY_Music_Period, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,31);
DEFINE_PROPERTYKEY(PKEY_Music_SynchronizedLyrics, 0x6b223b6a,0x162e,0x4aa9,0xb3,0x9f,0x05,0xd6,0x78,0xfc,0x6d,0x77,100);
DEFINE_PROPERTYKEY(PKEY_Music_TrackNumber, 0x56a3372e,0xce9c,0x11d2,0x9f,0x0e,0x00,0x60,0x97,0xc6,0x86,0xf6,7);

DEFINE_PROPERTYKEY(PKEY_Note_Color, 0x4776cafa,0xbce4,0x4cb1,0xa2,0x3e,0x26,0x5e,0x76,0xd8,0xeb,0x11,100);
DEFINE_PROPERTYKEY(PKEY_Note_ColorText, 0x46b4e8de,0xcdb2,0x440d,0x88,0x5c,0x16,0x58,0xeb,0x65,0xb9,0x14,100);

DEFINE_PROPERTYKEY(PKEY_Photo_Aperture, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,37378);
DEFINE_PROPERTYKEY(PKEY_Photo_ApertureDenominator, 0xe1a9a38b,0x6685,0x46bd,0x87,0x5e,0x57,0x0d,0xc7,0xad,0x73,0x20,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ApertureNumerator, 0x0337ecec,0x39fb,0x4581,0xa0,0xbd,0x4c,0x4c,0xc5,0x1e,0x99,0x14,100);
DEFINE_PROPERTYKEY(PKEY_Photo_Brightness, 0x1a701bf6,0x478c,0x4361,0x83,0xab,0x37,0x01,0xbb,0x05,0x3c,0x58,100);
DEFINE_PROPERTYKEY(PKEY_Photo_BrightnessDenominator, 0x6ebe6946,0x2321,0x440a,0x90,0xf0,0xc0,0x43,0xef,0xd3,0x24,0x76,100);
DEFINE_PROPERTYKEY(PKEY_Photo_BrightnessNumerator, 0x9e7d118f,0xb314,0x45a0,0x8c,0xfb,0xd6,0x54,0xb9,0x17,0xc9,0xe9,100);
DEFINE_PROPERTYKEY(PKEY_Photo_CameraManufacturer, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,271);
DEFINE_PROPERTYKEY(PKEY_Photo_CameraModel, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,272);
DEFINE_PROPERTYKEY(PKEY_Photo_CameraSerialNumber, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,273);
DEFINE_PROPERTYKEY(PKEY_Photo_Contrast, 0x2a785ba9,0x8d23,0x4ded,0x82,0xe6,0x60,0xa3,0x50,0xc8,0x6a,0x10,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ContrastText, 0x59dde9f2,0x5253,0x40ea,0x9a,0x8b,0x47,0x9e,0x96,0xc6,0x24,0x9a,100);
DEFINE_PROPERTYKEY(PKEY_Photo_DateTaken, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,36867);
DEFINE_PROPERTYKEY(PKEY_Photo_DigitalZoom, 0xf85bf840,0xa925,0x4bc2,0xb0,0xc4,0x8e,0x36,0xb5,0x98,0x67,0x9e,100);
DEFINE_PROPERTYKEY(PKEY_Photo_DigitalZoomDenominator, 0x745baf0e,0xe5c1,0x4cfb,0x8a,0x1b,0xd0,0x31,0xa0,0xa5,0x23,0x93,100);
DEFINE_PROPERTYKEY(PKEY_Photo_DigitalZoomNumerator, 0x16cbb924,0x6500,0x473b,0xa5,0xbe,0xf1,0x59,0x9b,0xcb,0xe4,0x13,100);
DEFINE_PROPERTYKEY(PKEY_Photo_Event, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,18248);
DEFINE_PROPERTYKEY(PKEY_Photo_EXIFVersion, 0xd35f743a,0xeb2e,0x47f2,0xa2,0x86,0x84,0x41,0x32,0xcb,0x14,0x27,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureBias, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,37380);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureBiasDenominator, 0xab205e50,0x04b7,0x461c,0xa1,0x8c,0x2f,0x23,0x38,0x36,0xe6,0x27,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureBiasNumerator, 0x738bf284,0x1d87,0x420b,0x92,0xcf,0x58,0x34,0xbf,0x6e,0xf9,0xed,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureIndex, 0x967b5af8,0x995a,0x46ed,0x9e,0x11,0x35,0xb3,0xc5,0xb9,0x78,0x2d,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureIndexDenominator, 0x93112f89,0xc28b,0x492f,0x8a,0x9d,0x4b,0xe2,0x06,0x2c,0xee,0x8a,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureIndexNumerator, 0xcdedcf30,0x8919,0x44df,0x8f,0x4c,0x4e,0xb2,0xff,0xdb,0x8d,0x89,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureProgram, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,34850);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureProgramText, 0xfec690b7,0x5f30,0x4646,0xae,0x47,0x4c,0xaa,0xfb,0xa8,0x84,0xa3,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureTime, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,33434);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureTimeDenominator, 0x55e98597,0xad16,0x42e0,0xb6,0x24,0x21,0x59,0x9a,0x19,0x98,0x38,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ExposureTimeNumerator, 0x257e44e2,0x9031,0x4323,0xac,0x38,0x85,0xc5,0x52,0x87,0x1b,0x2e,100);
DEFINE_PROPERTYKEY(PKEY_Photo_Flash, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,37385);
DEFINE_PROPERTYKEY(PKEY_Photo_FlashEnergy, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,41483);
DEFINE_PROPERTYKEY(PKEY_Photo_FlashEnergyDenominator, 0xd7b61c70,0x6323,0x49cd,0xa5,0xfc,0xc8,0x42,0x77,0x16,0x2c,0x97,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FlashEnergyNumerator, 0xfcad3d3d,0x0858,0x400f,0xaa,0xa3,0x2f,0x66,0xcc,0xe2,0xa6,0xbc,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FlashManufacturer, 0xaabaf6c9,0xe0c5,0x4719,0x85,0x85,0x57,0xb1,0x03,0xe5,0x84,0xfe,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FlashModel, 0xfe83bb35,0x4d1a,0x42e2,0x91,0x6b,0x06,0xf3,0xe1,0xaf,0x71,0x9e,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FlashText, 0x6b8b68f6,0x200b,0x47ea,0x8d,0x25,0xd8,0x05,0x0f,0x57,0x33,0x9f,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FNumber, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,33437);
DEFINE_PROPERTYKEY(PKEY_Photo_FNumberDenominator, 0xe92a2496,0x223b,0x4463,0xa4,0xe3,0x30,0xea,0xbb,0xa7,0x9d,0x80,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FNumberNumerator, 0x1b97738a,0xfdfc,0x462f,0x9d,0x93,0x19,0x57,0xe0,0x8b,0xe9,0x0c,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalLength, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,37386);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalLengthDenominator, 0x305bc615,0xdca1,0x44a5,0x9f,0xd4,0x10,0xc0,0xba,0x79,0x41,0x2e,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalLengthInFilm, 0xa0e74609,0xb84d,0x4f49,0xb8,0x60,0x46,0x2b,0xd9,0x97,0x1f,0x98,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalLengthNumerator, 0x776b6b3b,0x1e3d,0x4b0c,0x9a,0x0e,0x8f,0xba,0xf2,0xa8,0x49,0x2a,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalPlaneXResolution, 0xcfc08d97,0xc6f7,0x4484,0x89,0xdd,0xeb,0xef,0x43,0x56,0xfe,0x76,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalPlaneXResolutionDenominator, 0x0933f3f5,0x4786,0x4f46,0xa8,0xe8,0xd6,0x4d,0xd3,0x7f,0xa5,0x21,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalPlaneXResolutionNumerator, 0xdccb10af,0xb4e2,0x4b88,0x95,0xf9,0x03,0x1b,0x4d,0x5a,0xb4,0x90,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalPlaneYResolution, 0x4fffe4d0,0x914f,0x4ac4,0x8d,0x6f,0xc9,0xc6,0x1d,0xe1,0x69,0xb1,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalPlaneYResolutionDenominator, 0x1d6179a6,0xa876,0x4031,0xb0,0x13,0x33,0x47,0xb2,0xb6,0x4d,0xc8,100);
DEFINE_PROPERTYKEY(PKEY_Photo_FocalPlaneYResolutionNumerator, 0xa2e541c5,0x4440,0x4ba8,0x86,0x7e,0x75,0xcf,0xc0,0x68,0x28,0xcd,100);
DEFINE_PROPERTYKEY(PKEY_Photo_GainControl, 0xfa304789,0x00c7,0x4d80,0x90,0x4a,0x1e,0x4d,0xcc,0x72,0x65,0xaa,100);
DEFINE_PROPERTYKEY(PKEY_Photo_GainControlDenominator, 0x42864dfd,0x9da4,0x4f77,0xbd,0xed,0x4a,0xad,0x7b,0x25,0x67,0x35,100);
DEFINE_PROPERTYKEY(PKEY_Photo_GainControlNumerator, 0x8e8ecf7c,0xb7b8,0x4eb8,0xa6,0x3f,0x0e,0xe7,0x15,0xc9,0x6f,0x9e,100);
DEFINE_PROPERTYKEY(PKEY_Photo_GainControlText, 0xc06238b2,0x0bf9,0x4279,0xa7,0x23,0x25,0x85,0x67,0x15,0xcb,0x9d,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ISOSpeed, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,34855);
DEFINE_PROPERTYKEY(PKEY_Photo_LensManufacturer, 0xe6ddcaf7,0x29c5,0x4f0a,0x9a,0x68,0xd1,0x94,0x12,0xec,0x70,0x90,100);
DEFINE_PROPERTYKEY(PKEY_Photo_LensModel, 0xe1277516,0x2b5f,0x4869,0x89,0xb1,0x2e,0x58,0x5b,0xd3,0x8b,0x7a,100);
DEFINE_PROPERTYKEY(PKEY_Photo_LightSource, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,37384);
DEFINE_PROPERTYKEY(PKEY_Photo_MakerNote, 0xfa303353,0xb659,0x4052,0x85,0xe9,0xbc,0xac,0x79,0x54,0x9b,0x84,100);
DEFINE_PROPERTYKEY(PKEY_Photo_MakerNoteOffset, 0x813f4124,0x34e6,0x4d17,0xab,0x3e,0x6b,0x1f,0x3c,0x22,0x47,0xa1,100);
DEFINE_PROPERTYKEY(PKEY_Photo_MaxAperture, 0x08f6d7c2,0xe3f2,0x44fc,0xaf,0x1e,0x5a,0xa5,0xc8,0x1a,0x2d,0x3e,100);
DEFINE_PROPERTYKEY(PKEY_Photo_MaxApertureDenominator, 0xc77724d4,0x601f,0x46c5,0x9b,0x89,0xc5,0x3f,0x93,0xbc,0xeb,0x77,100);
DEFINE_PROPERTYKEY(PKEY_Photo_MaxApertureNumerator, 0xc107e191,0xa459,0x44c5,0x9a,0xe6,0xb9,0x52,0xad,0x4b,0x90,0x6d,100);
DEFINE_PROPERTYKEY(PKEY_Photo_MeteringMode, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,37383);
DEFINE_PROPERTYKEY(PKEY_Photo_MeteringModeText, 0xf628fd8c,0x7ba8,0x465a,0xa6,0x5b,0xc5,0xaa,0x79,0x26,0x3a,0x9e,100);
DEFINE_PROPERTYKEY(PKEY_Photo_Orientation, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,274);
DEFINE_PROPERTYKEY(PKEY_Photo_OrientationText, 0xa9ea193c,0xc511,0x498a,0xa0,0x6b,0x58,0xe2,0x77,0x6d,0xcc,0x28,100);
DEFINE_PROPERTYKEY(PKEY_Photo_PeopleNames, 0xe8309b6e,0x084c,0x49b4,0xb1,0xfc,0x90,0xa8,0x03,0x31,0xb6,0x38,100);
DEFINE_PROPERTYKEY(PKEY_Photo_PhotometricInterpretation, 0x341796f1,0x1df9,0x4b1c,0xa5,0x64,0x91,0xbd,0xef,0xa4,0x38,0x77,100);
DEFINE_PROPERTYKEY(PKEY_Photo_PhotometricInterpretationText, 0x821437d6,0x9eab,0x4765,0xa5,0x89,0x3b,0x1c,0xbb,0xd2,0x2a,0x61,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ProgramMode, 0x6d217f6d,0x3f6a,0x4825,0xb4,0x70,0x5f,0x03,0xca,0x2f,0xbe,0x9b,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ProgramModeText, 0x7fe3aa27,0x2648,0x42f3,0x89,0xb0,0x45,0x4e,0x5c,0xb1,0x50,0xc3,100);
DEFINE_PROPERTYKEY(PKEY_Photo_RelatedSoundFile, 0x318a6b45,0x087f,0x4dc2,0xb8,0xcc,0x05,0x35,0x95,0x51,0xfc,0x9e,100);
DEFINE_PROPERTYKEY(PKEY_Photo_Saturation, 0x49237325,0xa95a,0x4f67,0xb2,0x11,0x81,0x6b,0x2d,0x45,0xd2,0xe0,100);
DEFINE_PROPERTYKEY(PKEY_Photo_SaturationText, 0x61478c08,0xb600,0x4a84,0xbb,0xe4,0xe9,0x9c,0x45,0xf0,0xa0,0x72,100);
DEFINE_PROPERTYKEY(PKEY_Photo_Sharpness, 0xfc6976db,0x8349,0x4970,0xae,0x97,0xb3,0xc5,0x31,0x6a,0x08,0xf0,100);
DEFINE_PROPERTYKEY(PKEY_Photo_SharpnessText, 0x51ec3f47,0xdd50,0x421d,0x87,0x69,0x33,0x4f,0x50,0x42,0x4b,0x1e,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ShutterSpeed, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,37377);
DEFINE_PROPERTYKEY(PKEY_Photo_ShutterSpeedDenominator, 0xe13d8975,0x81c7,0x4948,0xae,0x3f,0x37,0xca,0xe1,0x1e,0x8f,0xf7,100);
DEFINE_PROPERTYKEY(PKEY_Photo_ShutterSpeedNumerator, 0x16ea4042,0xd6f4,0x4bca,0x83,0x49,0x7c,0x78,0xd3,0x0f,0xb3,0x33,100);
DEFINE_PROPERTYKEY(PKEY_Photo_SubjectDistance, 0x14b81da1,0x0135,0x4d31,0x96,0xd9,0x6c,0xbf,0xc9,0x67,0x1a,0x99,37382);
DEFINE_PROPERTYKEY(PKEY_Photo_SubjectDistanceDenominator, 0x0c840a88,0xb043,0x466d,0x97,0x66,0xd4,0xb2,0x6d,0xa3,0xfa,0x77,100);
DEFINE_PROPERTYKEY(PKEY_Photo_SubjectDistanceNumerator, 0x8af4961c,0xf526,0x43e5,0xaa,0x81,0xdb,0x76,0x82,0x19,0x17,0x8d,100);
DEFINE_PROPERTYKEY(PKEY_Photo_TagViewAggregate, 0xb812f15d,0xc2d8,0x4bbf,0xba,0xcd,0x79,0x74,0x43,0x46,0x11,0x3f,100);
DEFINE_PROPERTYKEY(PKEY_Photo_TranscodedForSync, 0x9a8ebb75,0x6458,0x4e82,0xba,0xcb,0x35,0xc0,0x09,0x5b,0x03,0xbb,100);
DEFINE_PROPERTYKEY(PKEY_Photo_WhiteBalance, 0xee3d3d8a,0x5381,0x4cfa,0xb1,0x3b,0xaa,0xf6,0x6b,0x5f,0x4e,0xc9,100);
DEFINE_PROPERTYKEY(PKEY_Photo_WhiteBalanceText, 0x6336b95e,0xc7a7,0x426d,0x86,0xfd,0x7a,0xe3,0xd3,0x9c,0x84,0xb4,100);

DEFINE_PROPERTYKEY(PKEY_PropGroup_Advanced, 0x900a403b,0x097b,0x4b95,0x8a,0xe2,0x07,0x1f,0xda,0xee,0xb1,0x18,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Audio, 0x2804d469,0x788f,0x48aa,0x85,0x70,0x71,0xb9,0xc1,0x87,0xe1,0x38,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Calendar, 0x9973d2b5,0xbfd8,0x438a,0xba,0x94,0x53,0x49,0xb2,0x93,0x18,0x1a,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Camera, 0xde00de32,0x547e,0x4981,0xad,0x4b,0x54,0x2f,0x2e,0x90,0x07,0xd8,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Contact, 0xdf975fd3,0x250a,0x4004,0x85,0x8f,0x34,0xe2,0x9a,0x3e,0x37,0xaa,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Content, 0xd0dab0ba,0x368a,0x4050,0xa8,0x82,0x6c,0x01,0x0f,0xd1,0x9a,0x4f,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Description, 0x8969b275,0x9475,0x4e00,0xa8,0x87,0xff,0x93,0xb8,0xb4,0x1e,0x44,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_FileSystem, 0xe3a7d2c1,0x80fc,0x4b40,0x8f,0x34,0x30,0xea,0x11,0x1b,0xdc,0x2e,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_General, 0xcc301630,0xb192,0x4c22,0xb3,0x72,0x9f,0x4c,0x6d,0x33,0x8e,0x07,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_GPS, 0xf3713ada,0x90e3,0x4e11,0xaa,0xe5,0xfd,0xc1,0x76,0x85,0xb9,0xbe,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Image, 0xe3690a87,0x0fa8,0x4a2a,0x9a,0x9f,0xfc,0xe8,0x82,0x70,0x55,0xac,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Media, 0x61872cf7,0x6b5e,0x4b4b,0xac,0x2d,0x59,0xda,0x84,0x45,0x92,0x48,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_MediaAdvanced, 0x8859a284,0xde7e,0x4642,0x99,0xba,0xd4,0x31,0xd0,0x44,0xb1,0xec,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Message, 0x7fd7259d,0x16b4,0x4135,0x9f,0x97,0x7c,0x96,0xec,0xd2,0xfa,0x9e,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Music, 0x68dd6094,0x7216,0x40f1,0xa0,0x29,0x43,0xfe,0x71,0x27,0x04,0x3f,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Origin, 0x2598d2fb,0x5569,0x4367,0x95,0xdf,0x5c,0xd3,0xa1,0x77,0xe1,0xa5,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_PhotoAdvanced, 0x0cb2bf5a,0x9ee7,0x4a86,0x82,0x22,0xf0,0x1e,0x07,0xfd,0xad,0xaf,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_RecordedTV, 0xe7b33238,0x6584,0x4170,0xa5,0xc0,0xac,0x25,0xef,0xd9,0xda,0x56,100);
DEFINE_PROPERTYKEY(PKEY_PropGroup_Video, 0xbebe0920,0x7671,0x4c54,0xa3,0xeb,0x49,0xfd,0xdf,0xc1,0x91,0xee,100);

DEFINE_PROPERTYKEY(PKEY_InfoTipText, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,17);

DEFINE_PROPERTYKEY(PKEY_PropList_ConflictPrompt, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,11);
DEFINE_PROPERTYKEY(PKEY_PropList_ContentViewModeForBrowse, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,13);
DEFINE_PROPERTYKEY(PKEY_PropList_ContentViewModeForSearch, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,14);
DEFINE_PROPERTYKEY(PKEY_PropList_ExtendedTileInfo, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,9);
DEFINE_PROPERTYKEY(PKEY_PropList_FileOperationPrompt, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,10);
DEFINE_PROPERTYKEY(PKEY_PropList_FullDetails, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,2);
DEFINE_PROPERTYKEY(PKEY_PropList_InfoTip, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,4);
DEFINE_PROPERTYKEY(PKEY_PropList_NonPersonal, 0x49d1091f,0x082e,0x493f,0xb2,0x3f,0xd2,0x30,0x8a,0xa9,0x66,0x8c,100);
DEFINE_PROPERTYKEY(PKEY_PropList_PreviewDetails, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,8);
DEFINE_PROPERTYKEY(PKEY_PropList_PreviewTitle, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,6);
DEFINE_PROPERTYKEY(PKEY_PropList_QuickTip, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,5);
DEFINE_PROPERTYKEY(PKEY_PropList_TileInfo, 0xc9944a21,0xa406,0x48fe,0x82,0x25,0xae,0xc7,0xe2,0x4c,0x21,0x1b,3);
DEFINE_PROPERTYKEY(PKEY_PropList_XPDetailsPanel, 0xf2275480,0xf782,0x4291,0xbd,0x94,0xf1,0x36,0x93,0x51,0x3a,0xec,0);

DEFINE_PROPERTYKEY(PKEY_RecordedTV_ChannelNumber, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,7);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_Credits, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,4);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_DateContentExpires, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,15);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_EpisodeName, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,2);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_IsATSCContent, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,16);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_IsClosedCaptioningAvailable, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,12);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_IsDTVContent, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,17);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_IsHDContent, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,18);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_IsRepeatBroadcast, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,13);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_IsSAP, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,14);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_NetworkAffiliation, 0x2c53c813,0xfb63,0x4e22,0xa1,0xab,0x0b,0x33,0x1c,0xa1,0xe2,0x73,100);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_OriginalBroadcastDate, 0x4684fe97,0x8765,0x4842,0x9c,0x13,0xf0,0x06,0x44,0x7b,0x17,0x8c,100);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_ProgramDescription, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,3);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_RecordingTime, 0xa5477f61,0x7a82,0x4eca,0x9d,0xde,0x98,0xb6,0x9b,0x24,0x79,0xb3,100);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_StationCallSign, 0x6d748de2,0x8d38,0x4cc3,0xac,0x60,0xf0,0x09,0xb0,0x57,0xc5,0x57,5);
DEFINE_PROPERTYKEY(PKEY_RecordedTV_StationName, 0x1b5439e7,0xeba1,0x4af8,0xbd,0xd7,0x7a,0xf1,0xd4,0x54,0x94,0x93,100);

DEFINE_PROPERTYKEY(PKEY_LocationEmptyString, 0x62d2d9ab,0x8b64,0x498d,0xb8,0x65,0x40,0x2d,0x47,0x96,0xf8,0x65,3);

DEFINE_PROPERTYKEY(PKEY_Search_AutoSummary, 0x560c36c0,0x503a,0x11cf,0xba,0xa1,0x00,0x00,0x4c,0x75,0x2a,0x9a,2);
DEFINE_PROPERTYKEY(PKEY_Search_ContainerHash, 0xbceee283,0x35df,0x4d53,0x82,0x6a,0xf3,0x6a,0x3e,0xef,0xc6,0xbe,100);
DEFINE_PROPERTYKEY(PKEY_Search_Contents, 0xb725f130,0x47ef,0x101a,0xa5,0xf1,0x02,0x60,0x8c,0x9e,0xeb,0xac,19);
DEFINE_PROPERTYKEY(PKEY_Search_EntryID, 0x49691c90,0x7e17,0x101a,0xa9,0x1c,0x08,0x00,0x2b,0x2e,0xcd,0xa9,5);
DEFINE_PROPERTYKEY(PKEY_Search_ExtendedProperties, 0x7b03b546,0xfa4f,0x4a52,0xa2,0xfe,0x03,0xd5,0x31,0x1e,0x58,0x65,100);
DEFINE_PROPERTYKEY(PKEY_Search_GatherTime, 0x0b63e350,0x9ccc,0x11d0,0xbc,0xdb,0x00,0x80,0x5f,0xcc,0xce,0x04,8);
DEFINE_PROPERTYKEY(PKEY_Search_HitCount, 0x49691c90,0x7e17,0x101a,0xa9,0x1c,0x08,0x00,0x2b,0x2e,0xcd,0xa9,4);
DEFINE_PROPERTYKEY(PKEY_Search_IsClosedDirectory, 0x0b63e343,0x9ccc,0x11d0,0xbc,0xdb,0x00,0x80,0x5f,0xcc,0xce,0x04,23);
DEFINE_PROPERTYKEY(PKEY_Search_IsFullyContained, 0x0b63e343,0x9ccc,0x11d0,0xbc,0xdb,0x00,0x80,0x5f,0xcc,0xce,0x04,24);
DEFINE_PROPERTYKEY(PKEY_Search_MatchKind, 0x49691c90,0x7e17,0x101a,0xa9,0x1c,0x08,0x00,0x2b,0x2e,0xcd,0xa9,29);
DEFINE_PROPERTYKEY(PKEY_Search_QueryFocusedSummary, 0x560c36c0,0x503a,0x11cf,0xba,0xa1,0x00,0x00,0x4c,0x75,0x2a,0x9a,3);
DEFINE_PROPERTYKEY(PKEY_Search_QueryFocusedSummaryWithFallback, 0x560c36c0,0x503a,0x11cf,0xba,0xa1,0x00,0x00,0x4c,0x75,0x2a,0x9a,4);
DEFINE_PROPERTYKEY(PKEY_Search_QueryPropertyHits, 0x49691c90,0x7e17,0x101a,0xa9,0x1c,0x08,0x00,0x2b,0x2e,0xcd,0xa9,21);
DEFINE_PROPERTYKEY(PKEY_Search_Rank, 0x49691c90,0x7e17,0x101a,0xa9,0x1c,0x08,0x00,0x2b,0x2e,0xcd,0xa9,3);
DEFINE_PROPERTYKEY(PKEY_Search_Store, 0xa06992b3,0x8caf,0x4ed7,0xa5,0x47,0xb2,0x59,0xe3,0x2a,0xc9,0xfc,100);
DEFINE_PROPERTYKEY(PKEY_Search_UrlToIndex, 0x0b63e343,0x9ccc,0x11d0,0xbc,0xdb,0x00,0x80,0x5f,0xcc,0xce,0x04,2);
DEFINE_PROPERTYKEY(PKEY_Search_UrlToIndexWithModificationTime, 0x0b63e343,0x9ccc,0x11d0,0xbc,0xdb,0x00,0x80,0x5f,0xcc,0xce,0x04,12);

DEFINE_PROPERTYKEY(PKEY_Supplemental_Album, 0x0c73b141,0x39d6,0x4653,0xa6,0x83,0xca,0xb2,0x91,0xea,0xf9,0x5b,6);
DEFINE_PROPERTYKEY(PKEY_Supplemental_AlbumID, 0x0c73b141,0x39d6,0x4653,0xa6,0x83,0xca,0xb2,0x91,0xea,0xf9,0x5b,2);
DEFINE_PROPERTYKEY(PKEY_Supplemental_Location, 0x0c73b141,0x39d6,0x4653,0xa6,0x83,0xca,0xb2,0x91,0xea,0xf9,0x5b,5);
DEFINE_PROPERTYKEY(PKEY_Supplemental_Person, 0x0c73b141,0x39d6,0x4653,0xa6,0x83,0xca,0xb2,0x91,0xea,0xf9,0x5b,7);
DEFINE_PROPERTYKEY(PKEY_Supplemental_ResourceId, 0x0c73b141,0x39d6,0x4653,0xa6,0x83,0xca,0xb2,0x91,0xea,0xf9,0x5b,3);
DEFINE_PROPERTYKEY(PKEY_Supplemental_Tag, 0x0c73b141,0x39d6,0x4653,0xa6,0x83,0xca,0xb2,0x91,0xea,0xf9,0x5b,4);

DEFINE_PROPERTYKEY(PKEY_ActivityInfo, 0x30c8eef4,0xa832,0x41e2,0xab,0x32,0xe3,0xc3,0xca,0x28,0xfd,0x29,17);
DEFINE_PROPERTYKEY(PKEY_DescriptionID, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,2);
DEFINE_PROPERTYKEY(PKEY_Home_Grouping, 0x30c8eef4,0xa832,0x41e2,0xab,0x32,0xe3,0xc3,0xca,0x28,0xfd,0x29,2);
DEFINE_PROPERTYKEY(PKEY_Home_IsPinned, 0x30c8eef4,0xa832,0x41e2,0xab,0x32,0xe3,0xc3,0xca,0x28,0xfd,0x29,4);
DEFINE_PROPERTYKEY(PKEY_Home_ItemFolderPathDisplay, 0x30c8eef4,0xa832,0x41e2,0xab,0x32,0xe3,0xc3,0xca,0x28,0xfd,0x29,6);
DEFINE_PROPERTYKEY(PKEY_Home_Recommended, 0x30c8eef4,0xa832,0x41e2,0xab,0x32,0xe3,0xc3,0xca,0x28,0xfd,0x29,20);
DEFINE_PROPERTYKEY(PKEY_InternalName, 0x0cef7d53,0xfa64,0x11d1,0xa2,0x03,0x00,0x00,0xf8,0x1f,0xed,0xee,5);
DEFINE_PROPERTYKEY(PKEY_LibraryLocationsCount, 0x908696c7,0x8f87,0x44f2,0x80,0xed,0xa8,0xc1,0xc6,0x89,0x45,0x75,2);
DEFINE_PROPERTYKEY(PKEY_Link_TargetSFGAOFlagsStrings, 0xd6942081,0xd53b,0x443d,0xad,0x47,0x5e,0x05,0x9d,0x9c,0xd2,0x7a,3);
DEFINE_PROPERTYKEY(PKEY_Link_TargetUrl, 0x5cbf2787,0x48cf,0x4208,0xb9,0x0e,0xee,0x5e,0x5d,0x42,0x02,0x94,2);
DEFINE_PROPERTYKEY(PKEY_NamespaceCLSID, 0x28636aa6,0x953d,0x11d2,0xb5,0xd6,0x00,0xc0,0x4f,0xd9,0x18,0xd0,6);
DEFINE_PROPERTYKEY(PKEY_Shell_SFGAOFlagsStrings, 0xd6942081,0xd53b,0x443d,0xad,0x47,0x5e,0x05,0x9d,0x9c,0xd2,0x7a,2);
DEFINE_PROPERTYKEY(PKEY_StatusBarSelectedItemCount, 0x26dc287c,0x6e3d,0x4bd3,0xb2,0xb0,0x6a,0x26,0xba,0x2e,0x34,0x6d,3);
DEFINE_PROPERTYKEY(PKEY_StatusBarViewItemCount, 0x26dc287c,0x6e3d,0x4bd3,0xb2,0xb0,0x6a,0x26,0xba,0x2e,0x34,0x6d,2);
DEFINE_PROPERTYKEY(PKEY_StorageProviderState, 0xe77e90df,0x6271,0x4f5b,0x83,0x4f,0x2d,0xd1,0xf2,0x45,0xdd,0xa4,3);
DEFINE_PROPERTYKEY(PKEY_StorageProviderTransferProgress, 0xe77e90df,0x6271,0x4f5b,0x83,0x4f,0x2d,0xd1,0xf2,0x45,0xdd,0xa4,4);
DEFINE_PROPERTYKEY(PKEY_WebAccountID, 0x30c8eef4,0xa832,0x41e2,0xab,0x32,0xe3,0xc3,0xca,0x28,0xfd,0x29,7);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_ExcludeFromShowInNewInstall, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,8);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_ID, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,5);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_IsDestListSeparator, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,6);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_IsDualMode, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,11);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_PreventPinning, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,9);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_RelaunchCommand, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,2);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_RelaunchDisplayNameResource, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,4);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_RelaunchIconResource, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,3);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_SettingsCommand, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,38);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_StartPinOption, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,12);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_ToastActivatorCLSID, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,26);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_UninstallCommand, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,37);
DEFINE_PROPERTYKEY(PKEY_AppUserModel_VisualElementsManifestHintPath, 0x9f4c2855,0x9f79,0x4b39,0xa8,0xd0,0xe1,0xd4,0x2d,0xe1,0xd5,0xf3,31);
DEFINE_PROPERTYKEY(PKEY_EdgeGesture_DisableTouchWhenFullscreen, 0x32ce38b2,0x2c9a,0x41b1,0x9b,0xc5,0xb3,0x78,0x43,0x94,0xaa,0x44,2);
DEFINE_PROPERTYKEY(PKEY_Software_DateLastUsed, 0x841e4f90,0xff59,0x4d16,0x89,0x47,0xe8,0x1b,0xbf,0xfa,0xb3,0x6d,16);
DEFINE_PROPERTYKEY(PKEY_Software_ProductName, 0x0cef7d53,0xfa64,0x11d1,0xa2,0x03,0x00,0x00,0xf8,0x1f,0xed,0xee,7);
DEFINE_PROPERTYKEY(PKEY_Sync_Comments, 0x7bd5533e,0xaf15,0x44db,0xb8,0xc8,0xbd,0x66,0x24,0xe1,0xd0,0x32,13);
DEFINE_PROPERTYKEY(PKEY_Sync_ConflictDescription, 0xce50c159,0x2fb8,0x41fd,0xbe,0x68,0xd3,0xe0,0x42,0xe2,0x74,0xbc,4);
DEFINE_PROPERTYKEY(PKEY_Sync_ConflictFirstLocation, 0xce50c159,0x2fb8,0x41fd,0xbe,0x68,0xd3,0xe0,0x42,0xe2,0x74,0xbc,6);
DEFINE_PROPERTYKEY(PKEY_Sync_ConflictSecondLocation, 0xce50c159,0x2fb8,0x41fd,0xbe,0x68,0xd3,0xe0,0x42,0xe2,0x74,0xbc,7);
DEFINE_PROPERTYKEY(PKEY_Sync_HandlerCollectionID, 0x7bd5533e,0xaf15,0x44db,0xb8,0xc8,0xbd,0x66,0x24,0xe1,0xd0,0x32,2);
DEFINE_PROPERTYKEY(PKEY_Sync_HandlerID, 0x7bd5533e,0xaf15,0x44db,0xb8,0xc8,0xbd,0x66,0x24,0xe1,0xd0,0x32,3);
DEFINE_PROPERTYKEY(PKEY_Sync_HandlerName, 0xce50c159,0x2fb8,0x41fd,0xbe,0x68,0xd3,0xe0,0x42,0xe2,0x74,0xbc,2);
DEFINE_PROPERTYKEY(PKEY_Sync_HandlerType, 0x7bd5533e,0xaf15,0x44db,0xb8,0xc8,0xbd,0x66,0x24,0xe1,0xd0,0x32,8);
DEFINE_PROPERTYKEY(PKEY_Sync_HandlerTypeLabel, 0x7bd5533e,0xaf15,0x44db,0xb8,0xc8,0xbd,0x66,0x24,0xe1,0xd0,0x32,9);
DEFINE_PROPERTYKEY(PKEY_Sync_ItemID, 0x7bd5533e,0xaf15,0x44db,0xb8,0xc8,0xbd,0x66,0x24,0xe1,0xd0,0x32,6);
DEFINE_PROPERTYKEY(PKEY_Sync_ItemName, 0xce50c159,0x2fb8,0x41fd,0xbe,0x68,0xd3,0xe0,0x42,0xe2,0x74,0xbc,3);
DEFINE_PROPERTYKEY(PKEY_Sync_ProgressPercentage, 0x7bd5533e,0xaf15,0x44db,0xb8,0xc8,0xbd,0x66,0x24,0xe1,0xd0,0x32,23);
DEFINE_PROPERTYKEY(PKEY_Sync_State, 0x7bd5533e,0xaf15,0x44db,0xb8,0xc8,0xbd,0x66,0x24,0xe1,0xd0,0x32,24);
DEFINE_PROPERTYKEY(PKEY_Sync_Status, 0x7bd5533e,0xaf15,0x44db,0xb8,0xc8,0xbd,0x66,0x24,0xe1,0xd0,0x32,10);
DEFINE_PROPERTYKEY(PKEY_Task_BillingInformation, 0xd37d52c6,0x261c,0x4303,0x82,0xb3,0x08,0xb9,0x26,0xac,0x6f,0x12,100);
DEFINE_PROPERTYKEY(PKEY_Task_CompletionStatus, 0x084d8a0a,0xe6d5,0x40de,0xbf,0x1f,0xc8,0x82,0x0e,0x7c,0x87,0x7c,100);
DEFINE_PROPERTYKEY(PKEY_Task_Owner, 0x08c7cc5f,0x60f2,0x4494,0xad,0x75,0x55,0xe3,0xe0,0xb5,0xad,0xd0,100);

DEFINE_PROPERTYKEY(PKEY_Video_Compression, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,10);
DEFINE_PROPERTYKEY(PKEY_Video_Director, 0x64440492,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,20);
DEFINE_PROPERTYKEY(PKEY_Video_EncodingBitrate, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,8);
DEFINE_PROPERTYKEY(PKEY_Video_FourCC, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,44);
DEFINE_PROPERTYKEY(PKEY_Video_FrameHeight, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,4);
DEFINE_PROPERTYKEY(PKEY_Video_FrameRate, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,6);
DEFINE_PROPERTYKEY(PKEY_Video_FrameWidth, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,3);
DEFINE_PROPERTYKEY(PKEY_Video_HorizontalAspectRatio, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,42);
DEFINE_PROPERTYKEY(PKEY_Video_IsSpherical, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,100);
DEFINE_PROPERTYKEY(PKEY_Video_IsStereo, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,98);
DEFINE_PROPERTYKEY(PKEY_Video_Orientation, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,99);
DEFINE_PROPERTYKEY(PKEY_Video_SampleSize, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,9);
DEFINE_PROPERTYKEY(PKEY_Video_StreamName, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,2);
DEFINE_PROPERTYKEY(PKEY_Video_StreamNumber, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,11);
DEFINE_PROPERTYKEY(PKEY_Video_TotalBitrate, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,43);
DEFINE_PROPERTYKEY(PKEY_Video_TranscodedForSync, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,46);
DEFINE_PROPERTYKEY(PKEY_Video_VerticalAspectRatio, 0x64440491,0x4c8b,0x11d1,0x8b,0x70,0x08,0x00,0x36,0xb1,0x1a,0x03,45);

DEFINE_PROPERTYKEY(PKEY_Volume_FileSystem, 0x9b174b35,0x40ff,0x11d2,0xa2,0x7e,0x00,0xc0,0x4f,0xc3,0x08,0x71,4);
DEFINE_PROPERTYKEY(PKEY_Volume_IsMappedDrive, 0x149c0b69,0x2c2d,0x48fc,0x80,0x8f,0xd3,0x18,0xd7,0x8c,0x46,0x36,2);
DEFINE_PROPERTYKEY(PKEY_Volume_IsRoot, 0x9b174b35,0x40ff,0x11d2,0xa2,0x7e,0x00,0xc0,0x4f,0xc3,0x08,0x71,10);

#endif /*_INC_PROPKEY*/
