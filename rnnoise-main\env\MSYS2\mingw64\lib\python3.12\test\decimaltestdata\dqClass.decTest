------------------------------------------------------------------------
-- dqClass.decTest -- decQuad Class operations                        --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- [New 2006.11.27]

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

dqcla001  class    0                        -> +Zero
dqcla002  class    0.00                     -> +Zero
dqcla003  class    0E+5                     -> +Zero
dqcla004  class    1E-6176                  -> +Subnormal
dqcla005  class  0.1E-6143                  -> +Subnormal
dqcla006  class  0.99999999999999999999999999999999E-6143     -> +Subnormal
dqcla007  class  1.00000000000000000000000000000000E-6143     -> +Normal
dqcla008  class   1E-6143                   -> +Normal
dqcla009  class   1E-100                    -> +Normal
dqcla010  class   1E-10                     -> +Normal
dqcla012  class   1E-1                      -> +Normal
dqcla013  class   1                         -> +Normal
dqcla014  class   2.50                      -> +Normal
dqcla015  class   100.100                   -> +Normal
dqcla016  class   1E+30                     -> +Normal
dqcla017  class   1E+6144                   -> +Normal
dqcla018  class   9.99999999999999999999999999999999E+6144    -> +Normal
dqcla019  class   Inf                       -> +Infinity

dqcla021  class   -0                        -> -Zero
dqcla022  class   -0.00                     -> -Zero
dqcla023  class   -0E+5                     -> -Zero
dqcla024  class   -1E-6176                  -> -Subnormal
dqcla025  class  -0.1E-6143                 -> -Subnormal
dqcla026  class  -0.99999999999999999999999999999999E-6143    -> -Subnormal
dqcla027  class  -1.00000000000000000000000000000000E-6143    -> -Normal
dqcla028  class  -1E-6143                   -> -Normal
dqcla029  class  -1E-100                    -> -Normal
dqcla030  class  -1E-10                     -> -Normal
dqcla032  class  -1E-1                      -> -Normal
dqcla033  class  -1                         -> -Normal
dqcla034  class  -2.50                      -> -Normal
dqcla035  class  -100.100                   -> -Normal
dqcla036  class  -1E+30                     -> -Normal
dqcla037  class  -1E+6144                   -> -Normal
dqcla0614  class  -9.99999999999999999999999999999999E+6144    -> -Normal
dqcla039  class  -Inf                       -> -Infinity

dqcla041  class   NaN                       -> NaN
dqcla042  class  -NaN                       -> NaN
dqcla043  class  +NaN12345                  -> NaN
dqcla044  class   sNaN                      -> sNaN
dqcla045  class  -sNaN                      -> sNaN
dqcla046  class  +sNaN12345                 -> sNaN



