set ::msgcat::header "Project-Id-Version: sv\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2023-10-26 21:42+0100\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Swedish <<EMAIL>>\nLanguage: sv\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Gtranslator 3.38.0\n"
::msgcat::mcset sv "Couldn't get list of unmerged files:" "Kunde inte h\u00e4mta lista \u00f6ver ej sammanslagna filer:"
::msgcat::mcset sv "Color words" "F\u00e4rga ord"
::msgcat::mcset sv "Markup words" "M\u00e4rk upp ord"
::msgcat::mcset sv "Error parsing revisions:" "Fel vid tolkning av revisioner:"
::msgcat::mcset sv "Error executing --argscmd command:" "Fel vid k\u00f6rning av --argscmd-kommando:"
::msgcat::mcset sv "No files selected: --merge specified but no files are unmerged." "Inga filer valdes: --merge angavs men det finns inga filer som inte har slagits samman."
::msgcat::mcset sv "No files selected: --merge specified but no unmerged files are within file limit." "Inga filer valdes: --merge angavs men det finns inga filer inom filbegr\u00e4nsningen."
::msgcat::mcset sv "Error executing git log:" "Fel vid k\u00f6rning av git log:"
::msgcat::mcset sv "Reading" "L\u00e4ser"
::msgcat::mcset sv "Reading commits..." "L\u00e4ser incheckningar..."
::msgcat::mcset sv "No commits selected" "Inga incheckningar markerade"
::msgcat::mcset sv "Command line" "Kommandorad"
::msgcat::mcset sv "Can't parse git log output:" "Kan inte tolka utdata fr\u00e5n git log:"
::msgcat::mcset sv "No commit information available" "Ingen incheckningsinformation \u00e4r tillg\u00e4nglig"
::msgcat::mcset sv "OK" "OK"
::msgcat::mcset sv "Cancel" "Avbryt"
::msgcat::mcset sv "&Update" "&Uppdatera"
::msgcat::mcset sv "&Reload" "L\u00e4s &om"
::msgcat::mcset sv "Reread re&ferences" "L\u00e4s om &referenser"
::msgcat::mcset sv "&List references" "&Visa referenser"
::msgcat::mcset sv "Start git &gui" "Starta git &gui"
::msgcat::mcset sv "&Quit" "&Avsluta"
::msgcat::mcset sv "&File" "&Arkiv"
::msgcat::mcset sv "&Preferences" "&Inst\u00e4llningar"
::msgcat::mcset sv "&Edit" "&Redigera"
::msgcat::mcset sv "&New view..." "&Ny vy..."
::msgcat::mcset sv "&Edit view..." "&\u00c4ndra vy..."
::msgcat::mcset sv "&Delete view" "&Ta bort vy"
::msgcat::mcset sv "&All files" "&Alla filer"
::msgcat::mcset sv "&View" "&Visa"
::msgcat::mcset sv "&About gitk" "&Om gitk"
::msgcat::mcset sv "&Key bindings" "&Tangentbordsbindningar"
::msgcat::mcset sv "&Help" "&Hj\u00e4lp"
::msgcat::mcset sv "SHA1 ID:" "SHA1-id:"
::msgcat::mcset sv "Row" "Rad"
::msgcat::mcset sv "Find" "S\u00f6k"
::msgcat::mcset sv "commit" "incheckning"
::msgcat::mcset sv "containing:" "som inneh\u00e5ller:"
::msgcat::mcset sv "touching paths:" "som r\u00f6r s\u00f6kv\u00e4g:"
::msgcat::mcset sv "adding/removing string:" "som l\u00e4gger/till tar bort str\u00e4ng:"
::msgcat::mcset sv "changing lines matching:" "\u00e4ndrar rader som matchar:"
::msgcat::mcset sv "Exact" "Exakt"
::msgcat::mcset sv "IgnCase" "IgnVersaler"
::msgcat::mcset sv "Regexp" "Reg.uttr."
::msgcat::mcset sv "All fields" "Alla f\u00e4lt"
::msgcat::mcset sv "Headline" "Rubrik"
::msgcat::mcset sv "Comments" "Kommentarer"
::msgcat::mcset sv "Author" "F\u00f6rfattare"
::msgcat::mcset sv "Committer" "Incheckare"
::msgcat::mcset sv "Search" "S\u00f6k"
::msgcat::mcset sv "Diff" "Diff"
::msgcat::mcset sv "Old version" "Gammal version"
::msgcat::mcset sv "New version" "Ny version"
::msgcat::mcset sv "Lines of context" "Rader sammanhang"
::msgcat::mcset sv "Ignore space change" "Ignorera \u00e4ndringar i blanksteg"
::msgcat::mcset sv "Line diff" "Rad-diff"
::msgcat::mcset sv "Patch" "Patch"
::msgcat::mcset sv "Tree" "Tr\u00e4d"
::msgcat::mcset sv "Diff this -> selected" "Diff denna -> markerad"
::msgcat::mcset sv "Diff selected -> this" "Diff markerad -> denna"
::msgcat::mcset sv "Make patch" "Skapa patch"
::msgcat::mcset sv "Create tag" "Skapa tagg"
::msgcat::mcset sv "Copy commit reference" "Kopiera incheckningsreferens"
::msgcat::mcset sv "Write commit to file" "Skriv incheckning till fil"
::msgcat::mcset sv "Create new branch" "Skapa ny gren"
::msgcat::mcset sv "Cherry-pick this commit" "Plocka denna incheckning"
::msgcat::mcset sv "Reset HEAD branch to here" "\u00c5terst\u00e4ll HEAD-grenen hit"
::msgcat::mcset sv "Mark this commit" "Markera denna incheckning"
::msgcat::mcset sv "Return to mark" "\u00c5terg\u00e5 till markering"
::msgcat::mcset sv "Find descendant of this and mark" "Hitta efterf\u00f6ljare till denna och markera"
::msgcat::mcset sv "Compare with marked commit" "J\u00e4mf\u00f6r med markerad incheckning"
::msgcat::mcset sv "Diff this -> marked commit" "Diff denna -> markerad incheckning"
::msgcat::mcset sv "Diff marked commit -> this" "Diff markerad incheckning -> denna"
::msgcat::mcset sv "Revert this commit" "\u00c5ngra denna incheckning"
::msgcat::mcset sv "Check out this branch" "Checka ut denna gren"
::msgcat::mcset sv "Rename this branch" "Byt namn p\u00e5 denna gren"
::msgcat::mcset sv "Remove this branch" "Ta bort denna gren"
::msgcat::mcset sv "Copy branch name" "Kopiera namn p\u00e5 gren"
::msgcat::mcset sv "Highlight this too" "Markera \u00e4ven detta"
::msgcat::mcset sv "Highlight this only" "Markera bara detta"
::msgcat::mcset sv "External diff" "Extern diff"
::msgcat::mcset sv "Blame parent commit" "Klandra f\u00f6r\u00e4ldraincheckning"
::msgcat::mcset sv "Copy path" "Kopiera s\u00f6kv\u00e4g"
::msgcat::mcset sv "Show origin of this line" "Visa ursprunget f\u00f6r den h\u00e4r raden"
::msgcat::mcset sv "Run git gui blame on this line" "K\u00f6r git gui blame p\u00e5 den h\u00e4r raden"
::msgcat::mcset sv "About gitk" "Om gitk"
::msgcat::mcset sv "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk - en incheckningsvisare f\u00f6r git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nAnv\u00e4nd och vidaref\u00f6rmedla enligt villkoren i GNU General Public License"
::msgcat::mcset sv "Close" "St\u00e4ng"
::msgcat::mcset sv "Gitk key bindings" "Tangentbordsbindningar f\u00f6r Gitk"
::msgcat::mcset sv "Gitk key bindings:" "Tangentbordsbindningar f\u00f6r Gitk:"
::msgcat::mcset sv "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Avsluta"
::msgcat::mcset sv "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009St\u00e4ng f\u00f6nster"
::msgcat::mcset sv "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009G\u00e5 till f\u00f6rsta incheckning"
::msgcat::mcset sv "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009G\u00e5 till sista incheckning"
::msgcat::mcset sv "<Up>, p, k\u0009Move up one commit" "<Upp>, p, k\u0009G\u00e5 en incheckning upp"
::msgcat::mcset sv "<Down>, n, j\u0009Move down one commit" "<Ned>, n, j\u0009G\u00e5 en incheckning ned"
::msgcat::mcset sv "<Left>, z, h\u0009Go back in history list" "<V\u00e4nster>, z, h\u0009G\u00e5 bak\u00e5t i historiken"
::msgcat::mcset sv "<Right>, x, l\u0009Go forward in history list" "<H\u00f6ger>, x, l\u0009G\u00e5 fram\u00e5t i historiken"
::msgcat::mcset sv "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n>\u0009G\u00e5 till aktuell inchecknings n:te f\u00f6r\u00e4lder i historielistan"
::msgcat::mcset sv "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009G\u00e5 upp en sida i incheckningslistan"
::msgcat::mcset sv "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009G\u00e5 ned en sida i incheckningslistan"
::msgcat::mcset sv "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009Rulla till b\u00f6rjan av incheckningslistan"
::msgcat::mcset sv "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009Rulla till slutet av incheckningslistan"
::msgcat::mcset sv "<%s-Up>\u0009Scroll commit list up one line" "<%s-Upp>\u0009Rulla incheckningslistan upp ett steg"
::msgcat::mcset sv "<%s-Down>\u0009Scroll commit list down one line" "<%s-Ned>\u0009Rulla incheckningslistan ned ett steg"
::msgcat::mcset sv "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009Rulla incheckningslistan upp en sida"
::msgcat::mcset sv "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009Rulla incheckningslistan ned en sida"
::msgcat::mcset sv "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Skift-Upp>\u0009S\u00f6k bak\u00e5t (upp\u00e5t, senare incheckningar)"
::msgcat::mcset sv "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Skift-Ned>\u0009S\u00f6k fram\u00e5t (ned\u00e5t, tidigare incheckningar)"
::msgcat::mcset sv "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009Rulla diffvisningen upp en sida"
::msgcat::mcset sv "<Backspace>\u0009Scroll diff view up one page" "<Baksteg>\u0009Rulla diffvisningen upp en sida"
::msgcat::mcset sv "<Space>\u0009\u0009Scroll diff view down one page" "<Blanksteg>\u0009Rulla diffvisningen ned en sida"
::msgcat::mcset sv "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009Rulla diffvisningen upp 18 rader"
::msgcat::mcset sv "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009Rulla diffvisningen ned 18 rader"
::msgcat::mcset sv "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009S\u00f6k"
::msgcat::mcset sv "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009G\u00e5 till n\u00e4sta s\u00f6ktr\u00e4ff"
::msgcat::mcset sv "<Return>\u0009Move to next find hit" "<Return>\u0009\u0009G\u00e5 till n\u00e4sta s\u00f6ktr\u00e4ff"
::msgcat::mcset sv "g\u0009\u0009Go to commit" "g\u0009\u0009G\u00e5 till incheckning"
::msgcat::mcset sv "/\u0009\u0009Focus the search box" "/\u0009\u0009Fokusera s\u00f6krutan"
::msgcat::mcset sv "?\u0009\u0009Move to previous find hit" "?\u0009\u0009G\u00e5 till f\u00f6reg\u00e5ende s\u00f6ktr\u00e4ff"
::msgcat::mcset sv "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009Rulla diffvisningen till n\u00e4sta fil"
::msgcat::mcset sv "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009G\u00e5 till n\u00e4sta s\u00f6ktr\u00e4ff i diffvisningen"
::msgcat::mcset sv "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009G\u00e5 till f\u00f6reg\u00e5ende s\u00f6ktr\u00e4ff i diffvisningen"
::msgcat::mcset sv "<%s-KP+>\u0009Increase font size" "<%s-Num+>\u0009\u00d6ka teckenstorlek"
::msgcat::mcset sv "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009\u00d6ka teckenstorlek"
::msgcat::mcset sv "<%s-KP->\u0009Decrease font size" "<%s-Num->\u0009Minska teckenstorlek"
::msgcat::mcset sv "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009Minska teckenstorlek"
::msgcat::mcset sv "<F5>\u0009\u0009Update" "<F5>\u0009\u0009Uppdatera"
::msgcat::mcset sv "Error creating temporary directory %s:" "Fel vid skapande av tempor\u00e4r katalog %s:"
::msgcat::mcset sv "Error getting \"%s\" from %s:" "Fel vid h\u00e4mtning av  \u201d%s\u201d fr\u00e5n %s:"
::msgcat::mcset sv "command failed:" "kommando misslyckades:"
::msgcat::mcset sv "No such commit" "Incheckning saknas"
::msgcat::mcset sv "git gui blame: command failed:" "git gui blame: kommando misslyckades:"
::msgcat::mcset sv "Couldn't read merge head: %s" "Kunde inte l\u00e4sa sammanslagningshuvud: %s"
::msgcat::mcset sv "Error reading index: %s" "Fel vid l\u00e4sning av index: %s"
::msgcat::mcset sv "Couldn't start git blame: %s" "Kunde inte starta git blame: %s"
::msgcat::mcset sv "Searching" "S\u00f6ker"
::msgcat::mcset sv "Error running git blame: %s" "Fel vid k\u00f6rning av git blame: %s"
::msgcat::mcset sv "That line comes from commit %s,  which is not in this view" "Raden kommer fr\u00e5n incheckningen %s, som inte finns i denna vy"
::msgcat::mcset sv "External diff viewer failed:" "Externt diff-verktyg misslyckades:"
::msgcat::mcset sv "All files" "Alla filer"
::msgcat::mcset sv "View" "Visa"
::msgcat::mcset sv "Gitk view definition" "Definition av Gitk-vy"
::msgcat::mcset sv "Remember this view" "Spara denna vy"
::msgcat::mcset sv "References (space separated list):" "Referenser (blankstegsavdelad lista):"
::msgcat::mcset sv "Branches & tags:" "Grenar & taggar:"
::msgcat::mcset sv "All refs" "Alla referenser"
::msgcat::mcset sv "All (local) branches" "Alla (lokala) grenar"
::msgcat::mcset sv "All tags" "Alla taggar"
::msgcat::mcset sv "All remote-tracking branches" "Alla fj\u00e4rrsp\u00e5rande grenar"
::msgcat::mcset sv "Commit Info (regular expressions):" "Incheckningsinfo (regulj\u00e4ra uttryck):"
::msgcat::mcset sv "Author:" "F\u00f6rfattare:"
::msgcat::mcset sv "Committer:" "Incheckare:"
::msgcat::mcset sv "Commit Message:" "Incheckningsmeddelande:"
::msgcat::mcset sv "Matches all Commit Info criteria" "Motsvarar alla kriterier f\u00f6r incheckningsinfo"
::msgcat::mcset sv "Matches no Commit Info criteria" "Motsvarar inga kriterier f\u00f6r incheckningsinfo"
::msgcat::mcset sv "Changes to Files:" "\u00c4ndringar av filer:"
::msgcat::mcset sv "Fixed String" "Fast str\u00e4ng"
::msgcat::mcset sv "Regular Expression" "Regulj\u00e4rt uttryck"
::msgcat::mcset sv "Search string:" "S\u00f6kstr\u00e4ng:"
::msgcat::mcset sv "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "Incheckningsdatum (\u201d2 weeks ago\u201d, \u201d2009-03-17 15:27:38\u201d, \u201dMarch 17, 2009 15:27:38\u201d):"
::msgcat::mcset sv "Since:" "Fr\u00e5n:"
::msgcat::mcset sv "Until:" "Till:"
::msgcat::mcset sv "Limit and/or skip a number of revisions (positive integer):" "Begr\u00e4nsa och/eller hoppa \u00f6ver ett antal revisioner (positivt heltal):"
::msgcat::mcset sv "Number to show:" "Antal att visa:"
::msgcat::mcset sv "Number to skip:" "Antal att hoppa \u00f6ver:"
::msgcat::mcset sv "Miscellaneous options:" "Diverse alternativ:"
::msgcat::mcset sv "Strictly sort by date" "Strikt datumsortering"
::msgcat::mcset sv "Mark branch sides" "Markera sidogrenar"
::msgcat::mcset sv "Limit to first parent" "Begr\u00e4nsa till f\u00f6rsta f\u00f6r\u00e4lder"
::msgcat::mcset sv "Simple history" "Enkel historik"
::msgcat::mcset sv "Additional arguments to git log:" "Ytterligare argument till git log:"
::msgcat::mcset sv "Enter files and directories to include, one per line:" "Ange filer och kataloger att ta med, en per rad:"
::msgcat::mcset sv "Command to generate more commits to include:" "Kommando f\u00f6r att generera fler incheckningar att ta med:"
::msgcat::mcset sv "Gitk: edit view" "Gitk: redigera vy"
::msgcat::mcset sv "-- criteria for selecting revisions" " - kriterier f\u00f6r val av revisioner"
::msgcat::mcset sv "View Name" "Namn p\u00e5 vy"
::msgcat::mcset sv "Apply (F5)" "Anv\u00e4nd (F5)"
::msgcat::mcset sv "Error in commit selection arguments:" "Fel i argument f\u00f6r val av incheckningar:"
::msgcat::mcset sv "None" "Inget"
::msgcat::mcset sv "Descendant" "Avkomling"
::msgcat::mcset sv "Not descendant" "Inte avkomling"
::msgcat::mcset sv "Ancestor" "F\u00f6rfader"
::msgcat::mcset sv "Not ancestor" "Inte f\u00f6rfader"
::msgcat::mcset sv "Local changes checked in to index but not committed" "Lokala \u00e4ndringar sparade i indexet men inte incheckade"
::msgcat::mcset sv "Local uncommitted changes, not checked in to index" "Lokala \u00e4ndringar, ej sparade i indexet"
::msgcat::mcset sv "Error starting web browser:" "Fel n\u00e4r webbl\u00e4saren skulle startas:"
::msgcat::mcset sv "and many more" "med m\u00e5nga flera"
::msgcat::mcset sv "many" "m\u00e5nga"
::msgcat::mcset sv "Tags:" "Taggar:"
::msgcat::mcset sv "Parent" "F\u00f6r\u00e4lder"
::msgcat::mcset sv "Child" "Barn"
::msgcat::mcset sv "Branch" "Gren"
::msgcat::mcset sv "Follows" "F\u00f6ljer"
::msgcat::mcset sv "Precedes" "F\u00f6reg\u00e5r"
::msgcat::mcset sv "Error getting diffs: %s" "Fel vid h\u00e4mtning av diff: %s"
::msgcat::mcset sv "Goto:" "G\u00e5 till:"
::msgcat::mcset sv "Short SHA1 id %s is ambiguous" "F\u00f6rkortat SHA1-id %s \u00e4r tvetydigt"
::msgcat::mcset sv "Revision %s is not known" "Revisionen %s \u00e4r inte k\u00e4nd"
::msgcat::mcset sv "SHA1 id %s is not known" "SHA-id:t %s \u00e4r inte k\u00e4nt"
::msgcat::mcset sv "Revision %s is not in the current view" "Revisionen %s finns inte i den nuvarande vyn"
::msgcat::mcset sv "Date" "Datum"
::msgcat::mcset sv "Children" "Barn"
::msgcat::mcset sv "Reset %s branch to here" "\u00c5terst\u00e4ll grenen %s hit"
::msgcat::mcset sv "Detached head: can't reset" "Fr\u00e5nkopplad head: kan inte \u00e5terst\u00e4lla"
::msgcat::mcset sv "Skipping merge commit " "Hoppar \u00f6ver sammanslagningsincheckning "
::msgcat::mcset sv "Error getting patch ID for " "Fel vid h\u00e4mtning av patch-id f\u00f6r "
::msgcat::mcset sv " - stopping\n" " - stannar\n"
::msgcat::mcset sv "Commit " "Incheckning "
::msgcat::mcset sv " is the same patch as\n       " " \u00e4r samma patch som\n       "
::msgcat::mcset sv " differs from\n       " " skiljer sig fr\u00e5n\n       "
::msgcat::mcset sv "Diff of commits:\n\n" "Skillnad mellan incheckningar:\n\n"
::msgcat::mcset sv " has %s children - stopping\n" " har %s barn - stannar\n"
::msgcat::mcset sv "Error writing commit to file: %s" "Fel vid skrivning av incheckning till fil: %s"
::msgcat::mcset sv "Error diffing commits: %s" "Fel vid j\u00e4mf\u00f6relse av incheckningar: %s"
::msgcat::mcset sv "Top" "Topp"
::msgcat::mcset sv "From" "Fr\u00e5n"
::msgcat::mcset sv "To" "Till"
::msgcat::mcset sv "Generate patch" "Generera patch"
::msgcat::mcset sv "From:" "Fr\u00e5n:"
::msgcat::mcset sv "To:" "Till:"
::msgcat::mcset sv "Reverse" "V\u00e4nd"
::msgcat::mcset sv "Output file:" "Utdatafil:"
::msgcat::mcset sv "Generate" "Generera"
::msgcat::mcset sv "Error creating patch:" "Fel vid generering av patch:"
::msgcat::mcset sv "ID:" "Id:"
::msgcat::mcset sv "Tag name:" "Taggnamn:"
::msgcat::mcset sv "Tag message is optional" "Taggmeddelandet \u00e4r valfritt"
::msgcat::mcset sv "Tag message:" "Taggmeddelande:"
::msgcat::mcset sv "Create" "Skapa"
::msgcat::mcset sv "No tag name specified" "Inget taggnamn angavs"
::msgcat::mcset sv "Tag \"%s\" already exists" "Taggen \u201d%s\u201d finns redan"
::msgcat::mcset sv "Error creating tag:" "Fel vid skapande av tagg:"
::msgcat::mcset sv "Command:" "Kommando:"
::msgcat::mcset sv "Write" "Skriv"
::msgcat::mcset sv "Error writing commit:" "Fel vid skrivning av incheckning:"
::msgcat::mcset sv "Create branch" "Skapa gren"
::msgcat::mcset sv "Rename branch %s" "Byt namn p\u00e5 grenen %s"
::msgcat::mcset sv "Rename" "Byt namn"
::msgcat::mcset sv "Name:" "Namn:"
::msgcat::mcset sv "Please specify a name for the new branch" "Ange ett namn f\u00f6r den nya grenen"
::msgcat::mcset sv "Branch '%s' already exists. Overwrite?" "Grenen \u201d%s\u201d finns redan. Skriva \u00f6ver?"
::msgcat::mcset sv "Please specify a new name for the branch" "Ange ett nytt namn f\u00f6r grenen"
::msgcat::mcset sv "Commit %s is already included in branch %s -- really re-apply it?" "Incheckningen %s finns redan p\u00e5 grenen %s -- skall den verkligen appliceras p\u00e5 nytt?"
::msgcat::mcset sv "Cherry-picking" "Plockar"
::msgcat::mcset sv "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "Cherry-pick misslyckades p\u00e5 grund av lokala \u00e4ndringar i filen \u201d%s\u201d.\nChecka in, \u00e5terst\u00e4ll eller spara undan (stash) dina \u00e4ndringar och f\u00f6rs\u00f6k igen."
::msgcat::mcset sv "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "Cherry-pick misslyckades p\u00e5 grund av en sammanslagningskonflikt.\nVill du k\u00f6ra git citool f\u00f6r att l\u00f6sa den?"
::msgcat::mcset sv "No changes committed" "Inga \u00e4ndringar incheckade"
::msgcat::mcset sv "Commit %s is not included in branch %s -- really revert it?" "Incheckningen %s finns inte p\u00e5 grenen %s -- vill du verkligen \u00e5ngra?"
::msgcat::mcset sv "Reverting" "\u00c5ngrar"
::msgcat::mcset sv "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "Misslyckades med att \u00e5ngra p\u00e5 grund av lokala \u00e4ndringar i f\u00f6ljande filer:%s. Checka in, \u00e5terst\u00e4ll eller spara undan (stash) dina \u00e4ndringar och f\u00f6rs\u00f6k igen."
::msgcat::mcset sv "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "Misslyckades med att \u00e5ngra p\u00e5 grund av en sammanslagningskonflikt.\n Vill du k\u00f6ra git citool f\u00f6r att l\u00f6sa den?"
::msgcat::mcset sv "Confirm reset" "Bekr\u00e4fta \u00e5terst\u00e4llning"
::msgcat::mcset sv "Reset branch %s to %s?" "\u00c5terst\u00e4lla grenen %s till %s?"
::msgcat::mcset sv "Reset type:" "Typ av \u00e5terst\u00e4llning:"
::msgcat::mcset sv "Soft: Leave working tree and index untouched" "Mjuk: R\u00f6r inte utcheckning och index"
::msgcat::mcset sv "Mixed: Leave working tree untouched, reset index" "Blandad: R\u00f6r inte utcheckning, \u00e5terst\u00e4ll index"
::msgcat::mcset sv "Hard: Reset working tree and index\n(discard ALL local changes)" "H\u00e5rd: \u00c5terst\u00e4ll utcheckning och index\n(f\u00f6rkastar ALLA lokala \u00e4ndringar)"
::msgcat::mcset sv "Resetting" "\u00c5terst\u00e4ller"
::msgcat::mcset sv "A local branch named %s exists already" "Det finns redan en lokal gren som heter %s"
::msgcat::mcset sv "Checking out" "Checkar ut"
::msgcat::mcset sv "Cannot delete the currently checked-out branch" "Kan inte ta bort den just nu utcheckade grenen"
::msgcat::mcset sv "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "Incheckningarna p\u00e5 grenen %s existerar inte p\u00e5 n\u00e5gon annan gren.\nVill du verkligen ta bort grenen %s?"
::msgcat::mcset sv "Tags and heads: %s" "Taggar och huvuden: %s"
::msgcat::mcset sv "Filter" "Filter"
::msgcat::mcset sv "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "Fel vid l\u00e4sning av information om incheckningstopologi; information om grenar och f\u00f6reg\u00e5ende/senare taggar kommer inte vara komplett."
::msgcat::mcset sv "Tag" "Tagg"
::msgcat::mcset sv "Id" "Id"
::msgcat::mcset sv "Gitk font chooser" "Teckensnittsv\u00e4ljare f\u00f6r Gitk"
::msgcat::mcset sv "B" "F"
::msgcat::mcset sv "I" "K"
::msgcat::mcset sv "Commit list display options" "Alternativ f\u00f6r incheckningslistvy"
::msgcat::mcset sv "Maximum graph width (lines)" "Maximal grafbredd (rader)"
::msgcat::mcset sv "Maximum graph width (% of pane)" "Maximal grafbredd (% av ruta)"
::msgcat::mcset sv "Show local changes" "Visa lokala \u00e4ndringar"
::msgcat::mcset sv "Auto-select SHA1 (length)" "V\u00e4lj SHA1 (l\u00e4ngd) automatiskt"
::msgcat::mcset sv "Hide remote refs" "D\u00f6lj fj\u00e4rr-referenser"
::msgcat::mcset sv "Diff display options" "Alternativ f\u00f6r diffvy"
::msgcat::mcset sv "Tab spacing" "Blanksteg f\u00f6r tabulatortecken"
::msgcat::mcset sv "Display nearby tags/heads" "Visa n\u00e4rliggande taggar/huvuden"
::msgcat::mcset sv "Maximum # tags/heads to show" "Maximalt antal taggar/huvuden att visa"
::msgcat::mcset sv "Limit diffs to listed paths" "Begr\u00e4nsa diff till listade s\u00f6kv\u00e4gar"
::msgcat::mcset sv "Support per-file encodings" "St\u00f6d f\u00f6r filspecifika teckenkodningar"
::msgcat::mcset sv "External diff tool" "Externt diff-verktyg"
::msgcat::mcset sv "Choose..." "V\u00e4lj..."
::msgcat::mcset sv "Web browser" "Webbl\u00e4sare"
::msgcat::mcset sv "General options" "Allm\u00e4nna inst\u00e4llningar"
::msgcat::mcset sv "Use themed widgets" "Anv\u00e4nd tema p\u00e5 f\u00f6nsterelement"
::msgcat::mcset sv "(change requires restart)" "(\u00e4ndringen kr\u00e4ver omstart)"
::msgcat::mcset sv "(currently unavailable)" "(f\u00f6r n\u00e4rvarande inte tillg\u00e4ngligt)"
::msgcat::mcset sv "Colors: press to choose" "F\u00e4rger: tryck f\u00f6r att v\u00e4lja"
::msgcat::mcset sv "Interface" "Gr\u00e4nssnitt"
::msgcat::mcset sv "interface" "gr\u00e4nssnitt"
::msgcat::mcset sv "Background" "Bakgrund"
::msgcat::mcset sv "background" "bakgrund"
::msgcat::mcset sv "Foreground" "F\u00f6rgrund"
::msgcat::mcset sv "foreground" "f\u00f6rgrund"
::msgcat::mcset sv "Diff: old lines" "Diff: gamla rader"
::msgcat::mcset sv "diff old lines" "diff gamla rader"
::msgcat::mcset sv "Diff: old lines bg" "Diff: gamla rader bg"
::msgcat::mcset sv "diff old lines bg" "diff gamla rader bg"
::msgcat::mcset sv "Diff: new lines" "Diff: nya rader"
::msgcat::mcset sv "diff new lines" "diff nya rader"
::msgcat::mcset sv "Diff: new lines bg" "Diff: nya rader bg"
::msgcat::mcset sv "diff new lines bg" "diff nya rader bg"
::msgcat::mcset sv "Diff: hunk header" "Diff: delhuvud"
::msgcat::mcset sv "diff hunk header" "diff delhuvud"
::msgcat::mcset sv "Marked line bg" "Markerad rad bakgrund"
::msgcat::mcset sv "marked line background" "markerad rad bakgrund"
::msgcat::mcset sv "Select bg" "Markerad bakgrund"
::msgcat::mcset sv "Fonts: press to choose" "Teckensnitt: tryck f\u00f6r att v\u00e4lja"
::msgcat::mcset sv "Main font" "Huvudteckensnitt"
::msgcat::mcset sv "Diff display font" "Teckensnitt f\u00f6r diffvisning"
::msgcat::mcset sv "User interface font" "Teckensnitt f\u00f6r anv\u00e4ndargr\u00e4nssnitt"
::msgcat::mcset sv "Gitk preferences" "Inst\u00e4llningar f\u00f6r Gitk"
::msgcat::mcset sv "General" "Allm\u00e4nt"
::msgcat::mcset sv "Colors" "F\u00e4rger"
::msgcat::mcset sv "Fonts" "Teckensnitt"
::msgcat::mcset sv "Gitk: choose color for %s" "Gitk: v\u00e4lj f\u00e4rg f\u00f6r %s"
::msgcat::mcset sv "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "Gitk kan tyv\u00e4rr inte k\u00f6ra med denna version av Tcl/Tk.\n Gitk kr\u00e4ver \u00e5tminstone Tcl/Tk 8.4."
::msgcat::mcset sv "Cannot find a git repository here." "Hittar inget git-arkiv h\u00e4r."
::msgcat::mcset sv "Ambiguous argument '%s': both revision and filename" "Tvetydigt argument \u201d%s\u201d: b\u00e5de revision och filnamn"
::msgcat::mcset sv "Bad arguments to gitk:" "Felaktiga argument till gitk:"
