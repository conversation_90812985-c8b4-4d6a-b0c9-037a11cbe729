.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_early_prf_hash_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_early_prf_hash_get \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_digest_algorithm_t gnutls_early_prf_hash_get(const gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "const gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
Get the hash algorithm used as a PRF to derive keys for encrypting
early data in TLS 1.3.
.SH "RETURNS"
the hash algorithm used for early data, a
\fBgnutls_digest_algorithm_t\fP value.
.SH "SINCE"
3.7.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
