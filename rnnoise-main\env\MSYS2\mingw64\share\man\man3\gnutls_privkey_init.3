.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_init \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_init(gnutls_privkey_t * " key ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t * key" 12
A pointer to the type to be initialized
.SH "DESCRIPTION"
This function will initialize a private key object. The object can
be used to generate, import, and perform cryptographic operations
on the associated private key.

Note that when the underlying private key is a PKCS\fB11\fP key (i.e.,
when imported with a PKCS\fB11\fP URI), the limitations of \fBgnutls_pkcs11_privkey_init()\fP
apply to this object as well. In versions of GnuTLS later than 3.5.11 the object
is protected using locks and a single \fBgnutls_privkey_t\fP can be re\-used
by many threads. However, for performance it is recommended to utilize
one object per key per thread.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
