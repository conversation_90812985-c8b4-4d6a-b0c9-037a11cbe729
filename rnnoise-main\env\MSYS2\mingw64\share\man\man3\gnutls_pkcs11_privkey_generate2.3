.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_privkey_generate2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_privkey_generate2 \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_privkey_generate2(const char * " url ", gnutls_pk_algorithm_t " pk ", unsigned int " bits ", const char * " label ", gnutls_x509_crt_fmt_t " fmt ", gnutls_datum_t * " pubkey ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * url" 12
a token URL
.IP "gnutls_pk_algorithm_t pk" 12
the public key algorithm
.IP "unsigned int bits" 12
the security bits
.IP "const char * label" 12
a label
.IP "gnutls_x509_crt_fmt_t fmt" 12
the format of output params. PEM or DER
.IP "gnutls_datum_t * pubkey" 12
will hold the public key (may be \fBNULL\fP)
.IP "unsigned int flags" 12
zero or an OR'ed sequence of \fBGNUTLS_PKCS11_OBJ_FLAGs\fP
.SH "DESCRIPTION"
This function will generate a private key in the specified
by the  \fIurl\fP token. The private key will be generate within
the token and will not be exportable. This function will
store the DER\-encoded public key in the SubjectPublicKeyInfo format
in  \fIpubkey\fP . The  \fIpubkey\fP should be deinitialized using \fBgnutls_free()\fP.

Note that when generating an elliptic curve key, the curve
can be substituted in the place of the bits parameter using the
\fBGNUTLS_CURVE_TO_BITS()\fP macro.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
