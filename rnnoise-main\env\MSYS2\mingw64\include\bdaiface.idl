/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#ifndef DO_NO_IMPORTS
import "unknwn.idl";
import "strmif.idl";
import "bdatypes.h";
#endif

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

#include "bdaiface_enums.h"

cpp_quote("")
cpp_quote("#define PBDA_Encoder_Audio_AlgorithmType_MPEG1LayerII 0x0")
cpp_quote("#define PBDA_Encoder_Audio_AlgorithmType_AC3 0x1")
cpp_quote("")
cpp_quote("#define PBDA_Encoder_Video_MPEG2PartII 0x0")
cpp_quote("#define PBDA_Encoder_Video_MPEG4Part10 0x1")
cpp_quote("")
cpp_quote("#define PBDA_Encoder_Video_AVC 0x1")
cpp_quote("#define PBDA_Encoder_Video_H264 0x1")
cpp_quote("")
cpp_quote("#define PBDA_Encoder_BitrateMode_Constant 1")
cpp_quote("#define PBDA_Encoder_BitrateMode_Variable 2")
cpp_quote("#define PBDA_Encoder_BitrateMode_Average 3")

typedef enum {
  KSPROPERTY_IPSINK_MULTICASTLIST,
  KSPROPERTY_IPSINK_ADAPTER_DESCRIPTION,
  KSPROPERTY_IPSINK_ADAPTER_ADDRESS
} KSPROPERTY_IPSINK;

#if 0
typedef enum {
  MEDIA_TRANSPORT_PACKET,
  MEDIA_ELEMENTARY_STREAM,
  MEDIA_MPEG2_PSI,
  MEDIA_TRANSPORT_PAYLOAD
} MEDIA_SAMPLE_CONTENT;

typedef struct {
  ULONG ulPID;
  MEDIA_SAMPLE_CONTENT MediaSampleContent;
} PID_MAP;
#endif

[object, uuid (fd501041-8ebe-11ce-8183-00aa00577da2), pointer_default (unique)]
interface IBDA_NetworkProvider : IUnknown {
  HRESULT PutSignalSource ([in] ULONG ulSignalSource);
  HRESULT GetSignalSource ([in, out] ULONG *pulSignalSource);
  HRESULT GetNetworkType ([in, out] GUID *pguidNetworkType);
  HRESULT PutTuningSpace ([in] REFGUID guidTuningSpace);
  HRESULT GetTuningSpace ([in, out] GUID *pguidTuingSpace);
  HRESULT RegisterDeviceFilter ([in] IUnknown *pUnkFilterControl,[in, out] ULONG *ppvRegisitrationContext);
  HRESULT UnRegisterDeviceFilter ([in] ULONG pvRegistrationContext);
}

[object, uuid (71985f43-1ca1-11d3-9cc8-00c04f7971e0), pointer_default (unique)]
interface IBDA_EthernetFilter : IUnknown {
  HRESULT GetMulticastListSize ([in, out] ULONG *pulcbAddresses);
  HRESULT PutMulticastList ([in] ULONG ulcbAddresses,[in, size_is (ulcbAddresses)] BYTE pAddressList []);
  HRESULT GetMulticastList ([in, out] ULONG *pulcbAddresses,[in, out, size_is (*pulcbAddresses)] BYTE pAddressList []);
  HRESULT PutMulticastMode ([in] ULONG ulModeMask);
  HRESULT GetMulticastMode ([out] ULONG *pulModeMask);
};

[object, uuid (71985f44-1ca1-11d3-9cc8-00c04f7971e0), pointer_default (unique)]
interface IBDA_IPV4Filter : IUnknown {
  HRESULT GetMulticastListSize ([in, out] ULONG *pulcbAddresses);
  HRESULT PutMulticastList ([in] ULONG ulcbAddresses,[in, size_is (ulcbAddresses)] BYTE pAddressList []);
  HRESULT GetMulticastList ([in, out] ULONG *pulcbAddresses,[out, size_is (*pulcbAddresses)] BYTE pAddressList []);
  HRESULT PutMulticastMode ([in] ULONG ulModeMask);
  HRESULT GetMulticastMode ([out] ULONG *pulModeMask);
};

[object, uuid (E1785A74-2a23-4fb3-9245-A8F88017EF33), pointer_default (unique)]
interface IBDA_IPV6Filter : IUnknown {
  HRESULT GetMulticastListSize ([in, out] ULONG *pulcbAddresses);
  HRESULT PutMulticastList ([in] ULONG ulcbAddresses,[in, size_is (ulcbAddresses)] BYTE pAddressList []);
  HRESULT GetMulticastList ([in, out] ULONG *pulcbAddresses,[out, size_is (*pulcbAddresses)] BYTE pAddressList []);
  HRESULT PutMulticastMode ([in] ULONG ulModeMask);
  HRESULT GetMulticastMode ([out] ULONG *pulModeMask);
};

[object, uuid (FD0A5AF3-B41D-11d2-9c95-00c04f7971e0), pointer_default (unique)]
interface IBDA_DeviceControl : IUnknown {
  HRESULT StartChanges (void);
  HRESULT CheckChanges (void);
  HRESULT CommitChanges (void);
  HRESULT GetChangeState ([in, out] ULONG *pState);
};

[object, uuid (0ded49d5-A8B7-4d5d-97a1-12b0c195874d), pointer_default (unique)]
interface IBDA_PinControl : IUnknown {
  HRESULT GetPinID ([in, out] ULONG *pulPinID);
  HRESULT GetPinType ([in, out] ULONG *pulPinType);
  HRESULT RegistrationContext ([in, out] ULONG *pulRegistrationCtx);
};

[object, uuid (D2F1644B-B409-11d2-BC69-00a0c9ee9e16), pointer_default (unique)]
interface IBDA_SignalProperties : IUnknown {
  HRESULT PutNetworkType ([in] REFGUID guidNetworkType);
  HRESULT GetNetworkType ([in, out] GUID *pguidNetworkType);
  HRESULT PutSignalSource ([in] ULONG ulSignalSource);
  HRESULT GetSignalSource ([in, out] ULONG *pulSignalSource);
  HRESULT PutTuningSpace ([in] REFGUID guidTuningSpace);
  HRESULT GetTuningSpace ([in, out] GUID *pguidTuingSpace);
};

[object, uuid (1347d106-CF3A-428a-A5CB-AC0D9A2A4338), pointer_default (unique)]
interface IBDA_SignalStatistics : IUnknown {
  HRESULT put_SignalStrength ([in] LONG lDbStrength);
  HRESULT get_SignalStrength ([in, out] LONG *plDbStrength);
  HRESULT put_SignalQuality ([in] LONG lPercentQuality);
  HRESULT get_SignalQuality ([in, out] LONG *plPercentQuality);
  HRESULT put_SignalPresent ([in] BOOLEAN fPresent);
  HRESULT get_SignalPresent ([in, out] BOOLEAN *pfPresent);
  HRESULT put_SignalLocked ([in] BOOLEAN fLocked);
  HRESULT get_SignalLocked ([in, out] BOOLEAN *pfLocked);
  HRESULT put_SampleTime ([in] LONG lmsSampleTime);
  HRESULT get_SampleTime ([in, out] LONG *plmsSampleTime);
};

[object, uuid (79b56888-7fea-4690-B45D-38fd3c7849be), pointer_default (unique)]
interface IBDA_Topology : IUnknown {
  HRESULT GetNodeTypes ([in, out] ULONG *pulcNodeTypes,[in] ULONG ulcNodeTypesMax,[in, out, size_is (ulcNodeTypesMax)] ULONG rgulNodeTypes[]);
  HRESULT GetNodeDescriptors ([in, out] ULONG *ulcNodeDescriptors,[in] ULONG ulcNodeDescriptorsMax,[in, out, size_is (ulcNodeDescriptorsMax)] BDANODE_DESCRIPTOR rgNodeDescriptors[]);
  HRESULT GetNodeInterfaces ([in] ULONG ulNodeType,[in, out] ULONG *pulcInterfaces,[in] ULONG ulcInterfacesMax,[in, out, size_is (ulcInterfacesMax)] GUID rgguidInterfaces[]);
  HRESULT GetPinTypes ([in, out] ULONG *pulcPinTypes,[in] ULONG ulcPinTypesMax,[in, out, size_is (ulcPinTypesMax)] ULONG rgulPinTypes[]);
  HRESULT GetTemplateConnections ([in, out] ULONG *pulcConnections,[in] ULONG ulcConnectionsMax,[in, out, size_is (ulcConnectionsMax)] BDA_TEMPLATE_CONNECTION rgConnections[]);
  HRESULT CreatePin ([in] ULONG ulPinType,[in, out] ULONG *pulPinId);
  HRESULT DeletePin ([in] ULONG ulPinId);
  HRESULT SetMediaType ([in] ULONG ulPinId,[in] AM_MEDIA_TYPE *pMediaType);
  HRESULT SetMedium ([in] ULONG ulPinId,[in] REGPINMEDIUM *pMedium);
  HRESULT CreateTopology ([in] ULONG ulInputPinId,[in] ULONG ulOutputPinId);
  HRESULT GetControlNode ([in] ULONG ulInputPinId,[in] ULONG ulOutputPinId,[in] ULONG ulNodeType,[in, out] IUnknown **ppControlNode);
};

[object, uuid (71985f46-1ca1-11d3-9cc8-00c04f7971e0), pointer_default (unique)]
interface IBDA_VoidTransform : IUnknown {
  HRESULT Start (void);
  HRESULT Stop (void);
};

[object, uuid (DDF15B0D-BD25-11d2-9ca0-00c04f7971e0), pointer_default (unique)]
interface IBDA_NullTransform : IUnknown {
  HRESULT Start (void);
  HRESULT Stop (void);
};

[object, uuid (71985f47-1ca1-11d3-9cc8-00c04f7971e0), pointer_default (unique)]
interface IBDA_FrequencyFilter : IUnknown {
  HRESULT put_Autotune ([in] ULONG ulTransponder);
  HRESULT get_Autotune ([in, out] ULONG *pulTransponder);
  HRESULT put_Frequency ([in] ULONG ulFrequency);
  HRESULT get_Frequency ([in, out] ULONG *pulFrequency);
  HRESULT put_Polarity ([in] Polarisation Polarity);
  HRESULT get_Polarity ([in, out] Polarisation *pPolarity);
  HRESULT put_Range ([in] ULONG ulRange);
  HRESULT get_Range ([in, out] ULONG *pulRange);
  HRESULT put_Bandwidth ([in] ULONG ulBandwidth);
  HRESULT get_Bandwidth ([in, out] ULONG *pulBandwidth);
  HRESULT put_FrequencyMultiplier ([in] ULONG ulMultiplier);
  HRESULT get_FrequencyMultiplier ([in, out] ULONG *pulMultiplier);
};

[object, uuid (992cf102-49f9-4719-A664-C4F23E2408F4), pointer_default (unique)]
interface IBDA_LNBInfo : IUnknown {
  HRESULT put_LocalOscilatorFrequencyLowBand ([in] ULONG ulLOFLow);
  HRESULT get_LocalOscilatorFrequencyLowBand ([in, out] ULONG *pulLOFLow);
  HRESULT put_LocalOscilatorFrequencyHighBand ([in] ULONG ulLOFHigh);
  HRESULT get_LocalOscilatorFrequencyHighBand ([in, out] ULONG *pulLOFHigh);
  HRESULT put_HighLowSwitchFrequency ([in] ULONG ulSwitchFrequency);
  HRESULT get_HighLowSwitchFrequency ([in, out] ULONG *pulSwitchFrequency);
};

[object, uuid (F84E2AB0-3c6b-45e3-A0FC-8669d4b81f11), pointer_default (unique)]
interface IBDA_DiseqCommand : IUnknown {
  HRESULT put_EnableDiseqCommands ([in] BOOLEAN bEnable);
  HRESULT put_DiseqLNBSource ([in] ULONG ulLNBSource);
  HRESULT put_DiseqUseToneBurst ([in] BOOLEAN bUseToneBurst);
  HRESULT put_DiseqRepeats ([in] ULONG ulRepeats);
  HRESULT put_DiseqSendCommand ([in] ULONG ulRequestId,[in] ULONG ulcbCommandLen,[in, size_is (ulcbCommandLen)] BYTE *pbCommand);
  HRESULT get_DiseqResponse ([in] ULONG ulRequestId,[in, out] ULONG *pulcbResponseLen,[in, out, size_is (*pulcbResponseLen)] BYTE pbResponse[]);
};

[object, uuid (DDF15B12-BD25-11d2-9ca0-00c04f7971e0), pointer_default (unique)]
interface IBDA_AutoDemodulate : IUnknown {
  HRESULT put_AutoDemodulate (void);
};

[object, uuid (34518d13-1182-48e6-B28F-B24987787326), pointer_default (unique)]
interface IBDA_AutoDemodulateEx : IBDA_AutoDemodulate {
  HRESULT get_SupportedDeviceNodeTypes ([in] ULONG ulcDeviceNodeTypesMax,[out] ULONG *pulcDeviceNodeTypes,[in, out] GUID *pguidDeviceNodeTypes);
  HRESULT get_SupportedVideoFormats ([out] ULONG *pulAMTunerModeType,[out] ULONG *pulAnalogVideoStandard);
  HRESULT get_AuxInputCount ([in, out] ULONG *pulCompositeCount,[in, out] ULONG *pulSvideoCount);
};

[object, uuid (EF30F379-985b-4d10-B640-A79D5E04E1E0), pointer_default (unique)]
interface IBDA_DigitalDemodulator : IUnknown {
  HRESULT put_ModulationType ([in] ModulationType *pModulationType);
  HRESULT get_ModulationType ([in, out] ModulationType *pModulationType);
  HRESULT put_InnerFECMethod ([in] FECMethod *pFECMethod);
  HRESULT get_InnerFECMethod ([in, out] FECMethod *pFECMethod);
  HRESULT put_InnerFECRate ([in] BinaryConvolutionCodeRate *pFECRate);
  HRESULT get_InnerFECRate ([in, out] BinaryConvolutionCodeRate *pFECRate);
  HRESULT put_OuterFECMethod ([in] FECMethod *pFECMethod);
  HRESULT get_OuterFECMethod ([in, out] FECMethod *pFECMethod);
  HRESULT put_OuterFECRate ([in] BinaryConvolutionCodeRate *pFECRate);
  HRESULT get_OuterFECRate ([in, out] BinaryConvolutionCodeRate *pFECRate);
  HRESULT put_SymbolRate ([in] ULONG *pSymbolRate);
  HRESULT get_SymbolRate ([in, out] ULONG *pSymbolRate);
  HRESULT put_SpectralInversion ([in] SpectralInversion *pSpectralInversion);
  HRESULT get_SpectralInversion ([in, out] SpectralInversion *pSpectralInversion);
};

[object, uuid (525ed3ee-5cf3-4e1e-9a06-5368a84f9a6e), pointer_default (unique)]
interface IBDA_DigitalDemodulator2 : IBDA_DigitalDemodulator {
  HRESULT put_GuardInterval ([in] GuardInterval *pGuardInterval);
  HRESULT get_GuardInterval ([in, out] GuardInterval *pGuardInterval);
  HRESULT put_TransmissionMode ([in] TransmissionMode *pTransmissionMode);
  HRESULT get_TransmissionMode ([in, out] TransmissionMode *pTransmissionMode);
  HRESULT put_RollOff ([in] RollOff *pRollOff);
  HRESULT get_RollOff ([in, out] RollOff *pRollOff);
  HRESULT put_Pilot ([in] Pilot *pPilot);
  HRESULT get_Pilot ([in, out] Pilot *pPilot);
};

[object, uuid (13f19604-7d32-4359-93a2-A05205D90AC9), pointer_default (unique)]
interface IBDA_DigitalDemodulator3 : IBDA_DigitalDemodulator2 {
  HRESULT put_SignalTimeouts ([in] BDA_SIGNAL_TIMEOUTS *pSignalTimeouts);
  HRESULT get_SignalTimeouts ([in, out] BDA_SIGNAL_TIMEOUTS *pSignalTimeouts);
  HRESULT put_PLPNumber ([in] ULONG *pPLPNumber);
  HRESULT get_PLPNumber ([in, out] ULONG *pPLPNumber);
};

[object, uuid (4b2bd7ea-8347-467b-8dbf-62f784929cc3), pointer_default (unique)]
interface ICCSubStreamFiltering : IUnknown {
  [propget, helpstring ("property SubstreamTypes")] HRESULT SubstreamTypes ([out, retval] long *pTypes);
  [propput, helpstring ("property SubstreamTypes")] HRESULT SubstreamTypes ([in] long Types);
};

[object, uuid (3f4dc8e2-4050-11d3-8f4b-00c04f7971e2), pointer_default (unique)]
interface IBDA_IPSinkControl : IUnknown {
  HRESULT GetMulticastList ([in, out] unsigned long *pulcbSize,[in, out] BYTE **pbBuffer);
  HRESULT GetAdapterIPAddress ([in, out] unsigned long *pulcbSize,[in, out] BYTE **pbBuffer);
};

[object, uuid (A750108F-492e-4d51-95f7-649b23ff7ad7), pointer_default (unique)]
interface IBDA_IPSinkInfo : IUnknown {
  HRESULT get_MulticastList ([in, out] ULONG *pulcbAddresses,[out, size_is (*pulcbAddresses)] BYTE **ppbAddressList);
  HRESULT get_AdapterIPAddress ([out] BSTR *pbstrBuffer);
  HRESULT get_AdapterDescription ([out] BSTR *pbstrBuffer);
};

[object, uuid (afb6c2a2-2c41-11d3-8a60-0000f81e0e4a), pointer_default (unique)]
interface IEnumPIDMap : IUnknown {
  HRESULT Next ([in] ULONG cRequest,[in, out, size_is (cRequest)] PID_MAP *pPIDMap,[out] ULONG *pcReceived);
  HRESULT Skip ([in] ULONG cRecords);
  HRESULT Reset ();
  HRESULT Clone ([out] IEnumPIDMap **ppIEnumPIDMap);
};

[object, uuid (afb6c2a1-2c41-11d3-8a60-0000f81e0e4a), pointer_default (unique)]
interface IMPEG2PIDMap : IUnknown {
  HRESULT MapPID ([in] ULONG culPID,[in] ULONG *pulPID,[in] MEDIA_SAMPLE_CONTENT MediaSampleContent);
  HRESULT UnmapPID ([in] ULONG culPID,[in] ULONG *pulPID);
  HRESULT EnumPIDMap ([out] IEnumPIDMap **pIEnumPIDMap);
};

[object, uuid (06fb45c1-693c-4ea7-B79F-7a6a54d8def2), pointer_default (unique), hidden, restricted]
interface IFrequencyMap : IUnknown {
  HRESULT get_FrequencyMapping ([out] ULONG *ulCount,[out, size_is (1,*ulCount)] ULONG **ppulList);
  HRESULT put_FrequencyMapping ([in] ULONG ulCount,[in, size_is (ulCount)] ULONG pList[]);
  HRESULT get_CountryCode ([out] ULONG *pulCountryCode);
  HRESULT put_CountryCode ([in] ULONG ulCountryCode);
  HRESULT get_DefaultFrequencyMapping ([in] ULONG ulCountryCode,[out] ULONG *pulCount,[out, size_is (1,*pulCount)] ULONG **ppulList);
  HRESULT get_CountryCodeList ([out] ULONG *pulCount,[out, size_is (1,*pulCount)] ULONG **ppulList);
};

[object, uuid (D806973D-3ebe-46de-8fbb-6358fe784208), pointer_default (unique), hidden, restricted]
interface IBDA_EasMessage : IUnknown {
  HRESULT get_EasMessage ([in] ULONG ulEventID,[in, out] IUnknown **ppEASObject);
};

[object, uuid (8e882535-5f86-47ab-86cf-C281A72A0549), pointer_default (unique), hidden, restricted]
interface IBDA_TransportStreamInfo : IUnknown {
  HRESULT get_PatTableTickCount ([out] ULONG *pPatTickCount);
};

[object, uuid (CD51F1E0-7be9-4123-8482-A2A796C0A6B0), pointer_default (unique)]
interface IBDA_ConditionalAccess : IUnknown {
  HRESULT get_SmartCardStatus ([out] SmartCardStatusType *pCardStatus,[out] SmartCardAssociationType *pCardAssociation,[out] BSTR *pbstrCardError,[out] VARIANT_BOOL *pfOOBLocked);
  HRESULT get_SmartCardInfo ([out] BSTR *pbstrCardName,[out] BSTR *pbstrCardManufacturer,[out] VARIANT_BOOL *pfDaylightSavings,[out] BYTE *pbyRatingRegion,[out] LONG *plTimeZoneOffsetMinutes,[out] BSTR *pbstrLanguage,[out] EALocationCodeType *pEALocationCode);
  HRESULT get_SmartCardApplications ([in, out] ULONG *pulcApplications,[in] ULONG ulcApplicationsMax,[in, out, size_is (ulcApplicationsMax)] SmartCardApplication rgApplications[]);
  HRESULT get_Entitlement ([in] USHORT usVirtualChannel,[out] EntitlementType *pEntitlement);
  HRESULT TuneByChannel ([in] USHORT usVirtualChannel);
  HRESULT SetProgram ([in] USHORT usProgramNumber);
  HRESULT AddProgram ([in] USHORT usProgramNumber);
  HRESULT RemoveProgram ([in] USHORT usProgramNumber);
  HRESULT GetModuleUI ([in] BYTE byDialogNumber,[out] BSTR *pbstrURL);
  HRESULT InformUIClosed ([in] BYTE byDialogNumber,[in] UICloseReasonType CloseReason);
};

[object, uuid (20e80cb5-c543-4c1b-8eb3-49e719eee7d4), pointer_default (unique)]
interface IBDA_DiagnosticProperties : IPropertyBag {
};

[object, uuid (F98D88B0-1992-4cd6-A6D9-B9AFAB99330D), pointer_default (unique)]
interface IBDA_DRM : IUnknown {
  HRESULT GetDRMPairingStatus ([out] DWORD *pdwStatus,[out] HRESULT *phError);
  HRESULT PerformDRMPairing (WINBOOL fSync);
};

[object, uuid (7f0b3150-7b81-4ad4-98e3-7e9097094301), pointer_default (unique)]
interface IBDA_NameValueService : IUnknown {
  HRESULT GetValueNameByIndex ([in] ULONG ulIndex,[out] BSTR *pbstrName);
  HRESULT GetValue ([in] BSTR bstrName,[in] BSTR bstrLanguage,[out] BSTR *pbstrValue);
  HRESULT SetValue ([in] ULONG ulDialogRequest,[in] BSTR bstrLanguage,[in] BSTR bstrName,[in] BSTR bstrValue,[in] ULONG ulReserved);
};

[object, uuid (497c3418-23cb-44ba-BB62-769f506fcea7), pointer_default (unique)]
interface IBDA_ConditionalAccessEx : IUnknown {
  HRESULT CheckEntitlementToken ([in] ULONG ulDialogRequest,[in] BSTR bstrLanguage,[in] BDA_CONDITIONALACCESS_REQUESTTYPE RequestType,[in] ULONG ulcbEntitlementTokenLen,[in, size_is (ulcbEntitlementTokenLen)] BYTE *pbEntitlementToken,[out] ULONG *pulDescrambleStatus);
  HRESULT SetCaptureToken ([in] ULONG ulcbCaptureTokenLen,[in, size_is (ulcbCaptureTokenLen)] BYTE *pbCaptureToken);
  HRESULT OpenBroadcastMmi ([in] ULONG ulDialogRequest,[in] BSTR bstrLanguage,[in] ULONG EventId);
  HRESULT CloseMmiDialog ([in] ULONG ulDialogRequest,[in] BSTR bstrLanguage,[in] ULONG ulDialogNumber,[in] BDA_CONDITIONALACCESS_MMICLOSEREASON ReasonCode,[out] ULONG *pulSessionResult);
  HRESULT CreateDialogRequestNumber ([out] ULONG *pulDialogRequestNumber);
};

[object, uuid (5e68c627-16c2-4e6c-B1E2-D00170CDAA0F), pointer_default (unique)]
interface IBDA_ISDBConditionalAccess : IUnknown {
  HRESULT SetIsdbCasRequest ([in] ULONG ulRequestId,[in] ULONG ulcbRequestBufferLen,[in, size_is (ulcbRequestBufferLen)] BYTE *pbRequestBuffer);
};

[object, uuid (207c413f-00dc-4c61-BAD6-6fee1ff07064), helpstring ("IBDA_EventingService Interface"), pointer_default (unique)] interface IBDA_EventingService : IUnknown {
  HRESULT CompleteEvent ([in] ULONG ulEventID,[in] ULONG ulEventResult);
};

[object, uuid (7def4c09-6e66-4567-A819-F0E17F4A81AB), helpstring ("IBDA_AUX Interface"), pointer_default (unique)] interface IBDA_AUX : IUnknown {
  HRESULT QueryCapabilities ([out] DWORD *pdwNumAuxInputsBSTR);
  HRESULT EnumCapability ([in] DWORD dwIndex,[out] DWORD *dwInputID,[out] GUID *pConnectorType,[out] DWORD *ConnTypeNum,[out] DWORD *NumVideoStds,[out] ULONGLONG *AnalogStds);
};

[object, uuid (3a8bad59-59fe-4559-a0ba-396cfaa98ae3), pointer_default (unique)]
interface IBDA_Encoder : IUnknown {
  HRESULT QueryCapabilities ([out] DWORD *NumAudioFmts,[out] DWORD *NumVideoFmts);
  HRESULT EnumAudioCapability ([in] DWORD FmtIndex,[out] DWORD *MethodID,[out] DWORD *AlgorithmType,[out] DWORD *SamplingRate,[out] DWORD *BitDepth,[out] DWORD *NumChannels);
  HRESULT EnumVideoCapability ([in] DWORD FmtIndex,[out] DWORD *MethodID,[out] DWORD *AlgorithmType,[out] DWORD *VerticalSize,[out] DWORD *HorizontalSize,[out] DWORD *AspectRatio,[out] DWORD *FrameRateCode,[out] DWORD *ProgressiveSequence);
  HRESULT SetParameters ([in] DWORD AudioBitrateMode,[in] DWORD AudioBitrate,[in] DWORD AudioMethodID,[in] DWORD AudioProgram,[in] DWORD VideoBitrateMode,[in] DWORD VideoBitrate,[in] DWORD VideoMethodID);
  HRESULT GetState ([out] DWORD *AudioBitrateMax,[out] DWORD *AudioBitrateMin,[out] DWORD *AudioBitrateMode,[out] DWORD *AudioBitrateStepping,[out] DWORD *AudioBitrate,[out] DWORD *AudioMethodID,[out] DWORD *AvailableAudioPrograms,[out] DWORD *AudioProgram,[out] DWORD *VideoBitrateMax,[out] DWORD *VideoBitrateMin,[out] DWORD *VideoBitrateMode,[out] DWORD *VideoBitrate,[out] DWORD *VideoBitrateStepping,[out] DWORD *VideoMethodID,[out] DWORD *SignalSourceID,[out] ULONGLONG *SignalFormat,[out] WINBOOL *SignalLock,[out] LONG *SignalLevel,[out] DWORD *SignalToNoiseRatio);
};

[object, uuid (138adc7e-58ae-437f-b0b4-c9fe19d5b4ac), pointer_default (unique)]
interface IBDA_FDC : IUnknown {
  HRESULT GetStatus ([out] DWORD *CurrentBitrate,[out] WINBOOL *CarrierLock,[out] DWORD *CurrentFrequency,[out] WINBOOL *CurrentSpectrumInversion,[out] BSTR *CurrentPIDList,[out] BSTR *CurrentTIDList,[out] WINBOOL *Overflow);
  HRESULT RequestTables ([in] BSTR TableIDs);
  HRESULT AddPid ([in] BSTR PidsToAdd,[out] DWORD *RemainingFilterEntries);
  HRESULT RemovePid ([in] BSTR PidsToRemove);
  HRESULT AddTid ([in] BSTR TidsToAdd,[out] BSTR *CurrentTidList);
  HRESULT RemoveTid ([in] BSTR TidsToRemove);
  HRESULT GetTableSection ([out] DWORD *Pid,[in] DWORD MaxBufferSize,[out] DWORD *ActualSize,[out] BYTE *SecBuffer);
};

[object, uuid (C0AFCB73-23e7-4bc6-BAFA-FDC167B4719F), pointer_default (unique)]
interface IBDA_GuideDataDeliveryService: IUnknown {
  HRESULT GetGuideDataType ([out] GUID *pguidDataType);
  HRESULT GetGuideData ([in, out] ULONG *pulcbBufferLen,[out] BYTE *pbBuffer,[out] ULONG *pulGuideDataPercentageProgress);
  HRESULT RequestGuideDataUpdate ();
  HRESULT GetTuneXmlFromServiceIdx ([in] ULONG64 ul64ServiceIdx,[out] BSTR *pbstrTuneXml);
  HRESULT GetServices ([in, out] ULONG *pulcbBufferLen,[out] BYTE *pbBuffer);
  HRESULT GetServiceInfoFromTuneXml ([in] BSTR bstrTuneXml,[out] BSTR *pbstrServiceDescription);
};

[object, uuid (BFF6B5BB-B0AE-484c-9dca-73528fb0b46e), pointer_default (unique)]
interface IBDA_DRMService: IUnknown {
  HRESULT SetDRM ([in] GUID *puuidNewDrm);
  HRESULT GetDRMStatus ([out] BSTR *pbstrDrmUuidList,[out] GUID *DrmUuid);
};

[object, uuid (4be6fa3d-07cd-4139-8b80-8c18ba3aec88), pointer_default (unique)]
interface IBDA_WMDRMSession: IUnknown {
  HRESULT GetStatus ([out] ULONG *MaxCaptureToken,[out] ULONG *MaxStreamingPid,[out] ULONG *MaxLicense,[out] ULONG *MinSecurityLevel,[out] ULONG *RevInfoSequenceNumber,[out] ULONGLONG *RevInfoIssuedTime,[out] ULONG *RevInfoTTL,[out] ULONG *RevListVersion,[out] ULONG *ulState);
  HRESULT SetRevInfo ([in] ULONG ulRevInfoLen,[in, size_is (ulRevInfoLen)] BYTE *pbRevInfo);
  HRESULT SetCrl ([in] ULONG ulCrlLen,[in, size_is (ulCrlLen)] BYTE *pbCrlLen);
  HRESULT TransactMessage ([in] ULONG ulcbRequest,[in, size_is (ulcbRequest)] BYTE *pbRequest,[in, out] ULONG *pulcbResponse,[in, out] BYTE *pbResponse);
  HRESULT GetLicense ([in] GUID *uuidKey,[in, out] ULONG *pulPackageLen,[in, out] BYTE *pbPackage);
  HRESULT ReissueLicense ([in] GUID *uuidKey);
  HRESULT RenewLicense ([in] ULONG ulInXmrLicenseLen,[in, size_is (ulInXmrLicenseLen)] BYTE *pbInXmrLicense,[in] ULONG ulEntitlementTokenLen,[in, size_is (ulEntitlementTokenLen)] BYTE *pbEntitlementToken,[out] ULONG *pulDescrambleStatus,[in, out] ULONG *pulOutXmrLicenseLen,[in, out] BYTE *pbOutXmrLicense);
  HRESULT GetKeyInfo ([in, out] ULONG *pulKeyInfoLen,[in, out] BYTE *pbKeyInfo);
};

[object, uuid (86d979cf-A8A7-4f94-B5FB-14c0aca68fe6), pointer_default (unique)]
interface IBDA_WMDRMTuner: IUnknown {
  HRESULT PurchaseEntitlement ([in] ULONG ulDialogRequest,[in] BSTR bstrLanguage,[in] ULONG ulPurchaseTokenLen,[in, size_is (ulPurchaseTokenLen)] BYTE *pbPurchaseToken,[out] ULONG *pulDescrambleStatus,[in, out] ULONG *pulCaptureTokenLen,[in, out] BYTE *pbCaptureToken);
  HRESULT CancelCaptureToken ([in] ULONG ulCaptureTokenLen,[in, size_is (ulCaptureTokenLen)] BYTE *pbCaptureToken);
  HRESULT SetPidProtection ([in] ULONG ulPid,[in] GUID *uuidKey);
  HRESULT GetPidProtection ([in] ULONG pulPid,[out] GUID *uuidKey);
  HRESULT SetSyncValue ([in] ULONG ulSyncValue);
  HRESULT GetStartCodeProfile ([in, out] ULONG *pulStartCodeProfileLen,[in, out] BYTE *pbStartCodeProfile);
};

[object, uuid (1f9bc2a5-44a3-4c52-AAB1-0bbce5a1381d), pointer_default (unique)]
interface IBDA_DRIDRMService: IUnknown {
  HRESULT SetDRM ([in] BSTR bstrNewDrm);
  HRESULT GetDRMStatus ([out] BSTR *pbstrDrmUuidList,[out] GUID *DrmUuid);
  HRESULT GetPairingStatus ([in, out] BDA_DrmPairingError *penumPairingStatus);
};

[object, uuid (05c690f8-56db-4bb2-B053-79c12098bb26), pointer_default (unique)]
interface IBDA_DRIWMDRMSession: IUnknown {
  HRESULT AcknowledgeLicense ([in] HRESULT hrLicenseAck);
  HRESULT ProcessLicenseChallenge ([in] DWORD dwcbLicenseMessage,[in, size_is (dwcbLicenseMessage)] BYTE *pbLicenseMessage,[in, out] DWORD *pdwcbLicenseResponse,[in, out, size_is (*pdwcbLicenseResponse)] BYTE **ppbLicenseResponse);
  HRESULT ProcessRegistrationChallenge ([in] DWORD dwcbRegistrationMessage,[in, size_is (dwcbRegistrationMessage)] BYTE *pbRegistrationMessage,[in, out] DWORD *pdwcbRegistrationResponse,[in, out] BYTE **ppbRegistrationResponse);
  HRESULT SetRevInfo ([in] DWORD dwRevInfoLen,[in, size_is (dwRevInfoLen)] BYTE *pbRevInfo,[in, out] DWORD *pdwResponse);
  HRESULT SetCrl ([in] DWORD dwCrlLen,[in, size_is (dwCrlLen)] BYTE *pbCrlLen,[in, out] DWORD *pdwResponse);
  HRESULT GetHMSAssociationData ();
  HRESULT GetLastCardeaError ([in, out] DWORD *pdwError);
};

[object, uuid (942aafec-4c05-4c74-B8EB-8706c2a4943f), pointer_default (unique)]
interface IBDA_MUX: IUnknown {
  HRESULT SetPidList ([in] ULONG ulPidListCount,[in, size_is (ulPidListCount)] BDA_MUX_PIDLISTITEM *pbPidListBuffer);
  HRESULT GetPidList ([in, out] ULONG *pulPidListCount,[in, out] BDA_MUX_PIDLISTITEM *pbPidListBuffer);
};

[object, uuid (1dcfafe9-B45E-41b3-BB2A-561eb129ae98), pointer_default (unique)]
interface IBDA_TransportStreamSelector : IUnknown {
  HRESULT SetTSID ([in] USHORT usTSID);
  HRESULT GetTSInformation ([in, out] ULONG *pulTSInformationBufferLen,[out, size_is (*pulTSInformationBufferLen)] BYTE *pbTSInformationBuffer);
};

[object, uuid (53b14189-E478-4b7a-A1FF-506db4b99dfe), pointer_default (unique)]
interface IBDA_UserActivityService: IUnknown {
  HRESULT SetCurrentTunerUseReason ([in] DWORD dwUseReason);
  HRESULT GetUserActivityInterval ([out] DWORD *pdwActivityInterval);
  HRESULT UserActivityDetected ();
};

cpp_quote("#define SID_BDA_EasMessage __uuidof (IBDA_EasMessage)")
cpp_quote("#define SID_BDA_TransportStreamInfo __uuidof (IBDA_TransportStreamInfo)")
cpp_quote("#define SID_BDA_ConditionalAccess __uuidof (IBDA_ConditionalAccess)")
cpp_quote("#define SID_BDA_DiagnosticProperties __uuidof (IBDA_DiagnosticProperties)")
cpp_quote("#define SID_BDA_DRM __uuidof (IBDA_DRM)")
cpp_quote("#define SID_BDA_NameValueService __uuidof (IBDA_NameValueService)")
cpp_quote("#define SID_BDA_ConditionalAccessEx __uuidof (IBDA_ConditionalAccessEx)")
cpp_quote("#define SID_BDA_ISDBConditionalAccess __uuidof (IBDA_ISDBConditionalAccess)")
cpp_quote("#define SID_BDA_EventingService __uuidof (IBDA_EventingService)")
cpp_quote("#define SID_BDA_AUX __uuidof (IBDA_AUX)")
cpp_quote("#define SID_BDA_Encoder __uuidof (IBDA_Encoder)")
cpp_quote("#define SID_BDA_FDC __uuidof (IBDA_FDC")
cpp_quote("#define SID_BDA_GuideDataDeliveryService  __uuidof (IBDA_GuideDataDeliveryService)")
cpp_quote("#define SID_BDA_DRMService __uuidof (IBDA_DRMService)")
cpp_quote("#define SID_BDA_WMDRMSession __uuidof (IBDA_WMDRMSession)")
cpp_quote("#define SID_BDA_WMDRMTuner __uuidof (IBDA_WMDRMTuner)")
cpp_quote("#define SID_BDA_DRIDRMService __uuidof (IBDA_DRIDRMService)")
cpp_quote("#define SID_BDA_DRIWMDRMSession __uuidof (IBDA_DRIWMDRMSession)")
cpp_quote("#define SID_BDA_MUX __uuidof (IBDA_MUX)")
cpp_quote("#define SID_BDA_TransportStreamSelector __uuidof (IBDA_TransportStreamSelector)")
cpp_quote("#define SID_BDA_UserActivityService __uuidof (IBDA_UserActivityService)")
cpp_quote("")

cpp_quote("DEFINE_GUID(CLSID_PBDA_Encoder_DATA_TYPE, 0x728fd6bc, 0x5546, 0x4716, 0xb1, 0x03, 0xf8, 0x99, 0xf5, 0xa1, 0xfa, 0x68);")
cpp_quote("DEFINE_GUID(CLSID_PBDA_FDC_DATA_TYPE, 0xe7dbf9a0, 0x22ab, 0x4047, 0x8e, 0x67, 0xef, 0x9a, 0xd5, 0x4, 0xe7, 0x29);")
cpp_quote("DEFINE_GUID(CLSID_PBDA_GDDS_DATA_TYPE, 0xC80C0DF3, 0x6052, 0x4c16, 0x9F, 0x56, 0xC4, 0x4C, 0x21, 0xF7, 0x3C, 0x45);")
cpp_quote("DEFINE_GUID(PBDA_AUX_CONNECTOR_TYPE_SVideo, 0xa0e905f4,0x24c9,0x4a54, 0xb7, 0x61, 0x21, 0x33, 0x55, 0xef, 0xc1, 0x3A);")
cpp_quote("DEFINE_GUID(PBDA_AUX_CONNECTOR_TYPE_Composite, 0xf6298b4c,0xc725,0x4d42, 0x84, 0x9b, 0x41, 0x0b, 0xbb, 0x14, 0xea, 0x62);")
cpp_quote("DEFINE_GUID(CLSID_PBDA_AUX_DATA_TYPE, 0xfd456373, 0x3323, 0x4090, 0xad, 0xca, 0x8e, 0xd4, 0x5f, 0x55, 0xcf, 0x10);")

cpp_quote("#endif")
