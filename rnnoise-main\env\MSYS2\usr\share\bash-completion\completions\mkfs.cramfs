_mkfs.cramfs_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-b')
			COMPREPLY=( $(compgen -W "blksize" -- $cur) )
			return 0
			;;
		'-e')
			COMPREPLY=( $(compgen -W "edition" -- $cur) )
			return 0
			;;
		'-N')
			COMPREPLY=( $(compgen -W "big little host" -- $cur) )
			return 0
			;;
		'-i')
			COMPREPLY=( $(compgen -f -- $cur) )
			return 0
			;;
		'-n')
			COMPREPLY=( $(compgen -W "name" -- $cur) )
			return 0
			;;
		'-h'|'-V')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="-h -v -E -b -e -N -i -n -p -s -z"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	compopt -o bashdefault -o default
	COMPREPLY=( $(compgen -W "$(lsblk -pnro name)" -- $cur) )
	return 0
}
complete -F _mkfs.cramfs_module mkfs.cramfs
