'\" t
.\"     Title: cygwin_posix_path_list_p
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/18/2025
.\"    Manual: Cygwin API Reference
.\"    Source: Cygwin API Reference
.\"  Language: English
.\"
.TH "CYGWIN_POSIX_PATH_LIST_P" "3" "06/18/2025" "Cygwin API Reference" "Cygwin API Reference"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
cygwin_posix_path_list_p
.SH "SYNOPSIS"
.sp
.ft B
.nf
#include <sys/cygwin\&.h>
.fi
.ft
.HP \w'int\ cygwin_posix_path_list_p('u
.BI "int cygwin_posix_path_list_p(const\ char\ *" "path" ");"
.SH "DESCRIPTION"
.PP
This function tells you if the supplied
\fIpath\fR
is a POSIX\-style path (i\&.e\&. posix names, forward slashes, colon delimiters) or a Win32\-style path (drive letters, reverse slashes, semicolon delimiters\&. The return value is true if the path is a POSIX path\&. Note that "_p" means "predicate", a lisp term meaning that the function tells you something about the parameter\&.
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
