_logger_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-f'|'--file'|'--journald')
			local IFS=$'\n'
			compopt -o filenames
			COMPREPLY=( $(compgen -f -- $cur) )
			return 0
			;;
		'-n'|'--server')
			COMPREPLY=( $(compgen -A hostname -- $cur) )
			return 0
			;;
		'-P'|'--port')
			COMPREPLY=( $(compgen -W "$(awk '$1 ~ /^syslog$/  {split($2, a, "/"); print a[1]}' /etc/services)" -- $cur) )
			return 0
			;;
		'-p'|'--priority')
			COMPREPLY=( $(compgen -W "{auth,authpriv,cron,daemon,ftp,lpr,mail,news,security}.{alert,crit,debug,emerg,err,error}" -- $cur) )
			return 0
			;;
		'-t'|'--tag')
			COMPREPLY=( $(compgen -W "tag" -- $cur) )
			return 0
			;;
		'-u'|'--socket')
			COMPREPLY=( $(compgen -W "$(awk '$NF ~ /^\// {print $NF}' /proc/net/unix)" -- $cur) )
			return 0
			;;
		'--socket-errors')
			COMPREPLY=( $(compgen -W "on off auto" -- $cur) )
			return 0
			;;
		'--msgid')
			COMPREPLY=( $(compgen -W "msgid" -- $cur) )
			return 0
			;;
		'--sd-id')
			COMPREPLY=( $(compgen -W "timeQuality origin meta" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="
				--file
				--help
				--id
				--journald
				--msgid
				--no-act
				--octet-count
				--port
				--prio-prefix
				--priority
				--rfc3164
				--rfc5424
				--sd-id
				--sd-param
				--server
				--size
				--skip-empty
				--socket
				--socket-errors
				--stderr
				--tag
				--tcp
				--udp
				--version
			"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	return 0
}
complete -F _logger_module logger
