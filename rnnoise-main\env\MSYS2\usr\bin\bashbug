#!/bin/sh -
#
# bashbug - create a bug report and mail it to the bug address
#
# The bug address depends on the release status of the shell.  Versions
# with status `devel', `alpha', `beta', or `rc' mail bug reports to
# <EMAIL> and, optionally, to <EMAIL>.
# Other versions send <NAME_EMAIL>.
#
# Copyright (C) 1996-2021 Free Software Foundation, Inc.
#
#   This program is free software: you can redistribute it and/or modify
#   it under the terms of the GNU General Public License as published by
#   the Free Software Foundation, either version 3 of the License, or
#   (at your option) any later version.
#
#   This program is distributed in the hope that it will be useful,
#   but WITHOUT ANY WARRANTY; without even the implied warranty of
#   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#   GNU General Public License for more details.
#
#   You should have received a copy of the GNU General Public License
#   along with this program.  If not, see <http://www.gnu.org/licenses/>.

#
# configuration section:
#	these variables are filled in by the make target in Makefile
#
MACHINE="x86_64"
OS="cygwin"
CC="gcc"
CFLAGS="-march=nocona -msahf -mtune=generic -O2 -pipe -D_STATIC_BUILD"
RELEASE="5.2"
PATCHLEVEL="37"
RELSTATUS="release"
MACHTYPE="x86_64-pc-cygwin"

PATH=/bin:/usr/bin:/usr/local/bin:$PATH
export PATH

# Check if TMPDIR is set, default to /tmp
: ${TMPDIR:=/tmp}

#Securely create a temporary directory for the temporary files
TEMPDIR=$TMPDIR/bbug.$$
(umask 077 && mkdir "$TEMPDIR") || {
	echo "$0: could not create temporary directory" >&2
	exit 1
}

TEMPFILE1=$TEMPDIR/bbug1
TEMPFILE2=$TEMPDIR/bbug2
        
USAGE="Usage: $0 [--help] [--version] [bug-report-email-address]"
VERSTR="GNU bashbug, version ${RELEASE}.${PATCHLEVEL}-${RELSTATUS}"

do_help= do_version=

while [ $# -gt 0 ]; do
	case "$1" in
	--help)		shift ; do_help=y ;;
	--version)	shift ; do_version=y ;;
	--)		shift ; break ;;
	-*)		echo "bashbug: ${1}: invalid option" >&2
			echo "$USAGE" >&2
			exit 2 ;;
	*)		break ;;
	esac
done

if [ -n "$do_version" ]; then
	echo "${VERSTR}"
	exit 0
fi

if [ -n "$do_help" ]; then
	echo "${VERSTR}"
	echo "${USAGE}"
	echo
	cat << HERE_EOF
Bashbug is used to send mail to the Bash maintainers
for when Bash doesn't behave like you'd like, or expect.

Bashbug will start up your editor (as defined by the shell's
EDITOR environment variable) with a preformatted bug report
template for you to fill in. The report will be mailed to the
bug-bash mailing list by default. See the manual for details.

If you invoke bashbug by accident, just quit your editor without
saving any changes to the template, and no bug report will be sent.
HERE_EOF
	exit 0
fi

# Figure out how to echo a string without a trailing newline
N=`echo 'hi there\c'`
case "$N" in
*c)	n=-n c= ;;
*)	n= c='\c' ;;
esac

BASHTESTERS="<EMAIL>"

case "$RELSTATUS" in
alpha*|beta*|devel*|rc*)	BUGBASH=<EMAIL> ;;
*)				BUGBASH=<EMAIL> ;;
esac

case "$RELSTATUS" in
alpha*|beta*|devel*|rc*)
		echo "$0: This is a testing release.  Would you like your bug report"
		echo "$0: to be sent to the bash-testers mailing list?"
		echo $n "$0: Send to bash-testers? $c"
		read ans
		case "$ans" in
		y*|Y*)	BUGBASH="${BUGBASH},${BASHTESTERS}" ;;
		esac ;;
esac

BUGADDR="${1-$BUGBASH}"

if [ -z "$DEFEDITOR" ] && [ -z "$EDITOR" ]; then
	if [ -x /usr/bin/editor ]; then
		DEFEDITOR=editor
	elif [ -x /usr/local/bin/ce ]; then
		DEFEDITOR=ce
	elif [ -x /usr/local/bin/emacs ]; then
		DEFEDITOR=emacs
	elif [ -x /usr/contrib/bin/emacs ]; then
		DEFEDITOR=emacs
	elif [ -x /usr/bin/emacs ]; then
		DEFEDITOR=emacs
	elif [ -x /usr/bin/xemacs ]; then
		DEFEDITOR=xemacs
	elif [ -x /usr/bin/vim; then
		DEFEDITOR=vim
	elif [ -x /usr/bin/gvim; then
		DEFEDITOR=gvim
	elif [ -x /usr/bin/nano ]; then
		DEFEDITOR=nano
	elif [ -x /usr/contrib/bin/jove ]; then
		DEFEDITOR=jove
	elif [ -x /usr/local/bin/jove ]; then
		DEFEDITOR=jove
	elif [ -x /usr/bin/vi ]; then
		DEFEDITOR=vi
	else
		echo "$0: No default editor found: attempting to use vi" >&2
		DEFEDITOR=vi
	fi
fi


: ${EDITOR=$DEFEDITOR}

: ${USER=${LOGNAME-`whoami`}}

trap 'rm -rf "$TEMPDIR"; exit 1' 1 2 3 13 15
trap 'rm -rf "$TEMPDIR"' 0

UN=
if (uname) >/dev/null 2>&1; then
	UN=`uname -a`
fi

if [ -f /usr/lib/sendmail ] ; then
	RMAIL="/usr/lib/sendmail"
	SMARGS="-i -t"
elif [ -f /usr/sbin/sendmail ] ; then
	RMAIL="/usr/sbin/sendmail"
	SMARGS="-i -t"
else
	RMAIL=rmail
	SMARGS="$BUGADDR"
fi

INITIAL_SUBJECT='[50 character or so descriptive subject here (for reference)]'

cat > "$TEMPFILE1" <<EOF
From: ${USER}
To: ${BUGADDR}
Subject: ${INITIAL_SUBJECT}

Configuration Information [Automatically generated, do not change]:
Machine: $MACHINE
OS: $OS
Compiler: $CC
Compilation CFLAGS: $CFLAGS
uname output: $UN
Machine Type: $MACHTYPE

Bash Version: $RELEASE
Patch Level: $PATCHLEVEL
Release Status: $RELSTATUS

Description:
	[Detailed description of the problem, suggestion, or complaint.]

Repeat-By:
	[Describe the sequence of events that causes the problem
	to occur.]

Fix:
	[Description of how to fix the problem.  If you don't know a
	fix for the problem, don't include this section.]
EOF

cp "$TEMPFILE1" "$TEMPFILE2"
chmod u+w "$TEMPFILE1"

trap '' 2		# ignore interrupts while in editor

edstat=1
while [ $edstat -ne 0 ]; do
	$EDITOR "$TEMPFILE1"
	edstat=$?

	if [ $edstat -ne 0 ]; then
		echo "$0: editor \`$EDITOR' exited with nonzero status."
		echo "$0: Perhaps it was interrupted."
		echo "$0: Type \`y' to give up, and lose your bug report;"
		echo "$0: type \`n' to re-enter the editor."
		echo $n "$0: Do you want to give up? $c"

		read ans
		case "$ans" in
		[Yy]*) exit 1 ;;
		esac

		continue
	fi

	# find the subject from the temp file and see if it's been changed
	CURR_SUB=`grep '^Subject: ' "$TEMPFILE1" | sed 's|^Subject:[ 	]*||' | sed 1q`

	case "$CURR_SUB" in
	"${INITIAL_SUBJECT}")
		echo
		echo "$0: You have not changed the subject from the default."
		echo "$0: Please use a more descriptive subject header."
		echo "$0: Type \`y' to give up, and lose your bug report;"
		echo "$0: type \`n' to re-enter the editor."
		echo $n "$0: Do you want to give up? $c"

		read ans
		case "$ans" in
		[Yy]*) exit 1 ;;
		esac

		echo "$0:  The editor will be restarted in five seconds."
		sleep 5
		edstat=1
		;;
	esac

done

trap 'rm -rf "$TEMPDIR"; exit 1' 2	# restore trap on SIGINT

if cmp -s "$TEMPFILE1" "$TEMPFILE2"
then
	echo "File not changed, no bug report submitted."
	exit
fi

echo $n "Send bug report to ${BUGADDR}? [y/n] $c"
read ans
case "$ans" in
[Nn]*)	exit 0 ;;
esac

${RMAIL} $SMARGS < "$TEMPFILE1" || {
	cat "$TEMPFILE1" >> $HOME/dead.bashbug
	echo "$0: mail to ${BUGADDR} failed: report saved in $HOME/dead.bashbug" >&2
	echo "$0: please send it manually to ${BUGADDR}" >&2
}

exit 0
