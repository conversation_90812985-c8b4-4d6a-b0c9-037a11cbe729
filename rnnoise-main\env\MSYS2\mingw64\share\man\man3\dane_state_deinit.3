.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_state_deinit" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_state_deinit \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "void dane_state_deinit(dane_state_t " s ");"
.SH ARGUMENTS
.IP "dane_state_t s" 12
The structure to be deinitialized
.SH "DESCRIPTION"
This function will deinitialize a DANE query structure.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
