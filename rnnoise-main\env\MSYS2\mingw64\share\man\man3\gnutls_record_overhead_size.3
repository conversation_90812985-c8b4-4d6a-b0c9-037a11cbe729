.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_overhead_size" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_overhead_size \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "size_t gnutls_record_overhead_size(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is \fBgnutls_session_t\fP
.SH "DESCRIPTION"
This function will return the size in bytes of the overhead
due to TLS (or DTLS) per record. On certain occasions
(e.g., CBC ciphers) the returned value is the maximum
possible overhead.
.SH "SINCE"
3.2.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
