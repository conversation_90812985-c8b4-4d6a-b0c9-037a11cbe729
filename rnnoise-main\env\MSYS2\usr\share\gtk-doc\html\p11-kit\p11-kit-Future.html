<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Future: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="reference.html" title="API Reference">
<link rel="prev" href="p11-kit-Utilities.html" title="Utilities">
<link rel="next" href="p11-kit-Deprecated.html" title="Deprecated">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts">
<a href="#" class="shortcut">Top</a><span id="nav_description">  <span class="dim">|</span> 
                  <a href="#p11-kit-Future.description" class="shortcut">Description</a></span>
</td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="reference.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="p11-kit-Utilities.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="p11-kit-Deprecated.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="refentry">
<a name="p11-kit-Future"></a><div class="titlepage"></div>
<div class="refnamediv"><table width="100%"><tr>
<td valign="top">
<h2><span class="refentrytitle"><a name="p11-kit-Future.top_of_page"></a>Future</span></h2>
<p>Future — Future Unstable API</p>
</td>
<td class="gallery_image" valign="top" align="right"></td>
</tr></table></div>
<div class="refsect1">
<a name="p11-kit-Future.functions"></a><h2>Functions</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="functions_proto_type">
<col class="functions_proto_name">
</colgroup>
<tbody>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-set-progname" title="p11_kit_set_progname ()">p11_kit_set_progname</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<span class="c_punctuation">(</span><a class="link" href="p11-kit-Future.html#p11-kit-destroyer" title="p11_kit_destroyer ()">*p11_kit_destroyer</a><span class="c_punctuation">)</span> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="returnvalue">P11KitIter</span></a> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-new" title="p11_kit_iter_new ()">p11_kit_iter_new</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-set-uri" title="p11_kit_iter_set_uri ()">p11_kit_iter_set_uri</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-add-callback" title="p11_kit_iter_add_callback ()">p11_kit_iter_add_callback</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-add-filter" title="p11_kit_iter_add_filter ()">p11_kit_iter_add_filter</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<span class="c_punctuation">(</span><a class="link" href="p11-kit-Future.html#p11-kit-iter-callback" title="p11_kit_iter_callback ()">*p11_kit_iter_callback</a><span class="c_punctuation">)</span> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-begin" title="p11_kit_iter_begin ()">p11_kit_iter_begin</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-begin-with" title="p11_kit_iter_begin_with ()">p11_kit_iter_begin_with</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()">p11_kit_iter_next</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-Future.html#P11KitIterKind" title="enum P11KitIterKind"><span class="returnvalue">P11KitIterKind</span></a>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-kind" title="p11_kit_iter_get_kind ()">p11_kit_iter_get_kind</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_FUNCTION_LIST_PTR</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-module" title="p11_kit_iter_get_module ()">p11_kit_iter_get_module</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_SLOT_ID</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-slot" title="p11_kit_iter_get_slot ()">p11_kit_iter_get_slot</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_SLOT_INFO</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-slot-info" title="p11_kit_iter_get_slot_info ()">p11_kit_iter_get_slot_info</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_TOKEN_INFO</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-token" title="p11_kit_iter_get_token ()">p11_kit_iter_get_token</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_SESSION_HANDLE</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-session" title="p11_kit_iter_get_session ()">p11_kit_iter_get_session</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_SESSION_HANDLE</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-keep-session" title="p11_kit_iter_keep_session ()">p11_kit_iter_keep_session</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_OBJECT_HANDLE</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-object" title="p11_kit_iter_get_object ()">p11_kit_iter_get_object</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-attributes" title="p11_kit_iter_get_attributes ()">p11_kit_iter_get_attributes</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-load-attributes" title="p11_kit_iter_load_attributes ()">p11_kit_iter_load_attributes</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-destroy-object" title="p11_kit_iter_destroy_object ()">p11_kit_iter_destroy_object</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-iter-free" title="p11_kit_iter_free ()">p11_kit_iter_free</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-remote-serve-module" title="p11_kit_remote_serve_module ()">p11_kit_remote_serve_module</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-remote-serve-token" title="p11_kit_remote_serve_token ()">p11_kit_remote_serve_token</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Future.html#p11-kit-remote-serve-tokens" title="p11_kit_remote_serve_tokens ()">p11_kit_remote_serve_tokens</a> <span class="c_punctuation">()</span>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-Future.other"></a><h2>Types and Values</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="other_proto_type">
<col class="other_proto_name">
</colgroup>
<tbody>
<tr>
<td class="typedef_keyword">typedef</td>
<td class="function_name"><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter">P11KitIter</a></td>
</tr>
<tr>
<td class="typedef_keyword">typedef</td>
<td class="function_name"><a class="link" href="p11-kit-Future.html#p11-kit-iter" title="p11_kit_iter">p11_kit_iter</a></td>
</tr>
<tr>
<td class="datatype_keyword">enum</td>
<td class="function_name"><a class="link" href="p11-kit-Future.html#P11KitIterKind" title="enum P11KitIterKind">P11KitIterKind</a></td>
</tr>
<tr>
<td class="datatype_keyword">enum</td>
<td class="function_name"><a class="link" href="p11-kit-Future.html#P11KitIterBehavior" title="enum P11KitIterBehavior">P11KitIterBehavior</a></td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-Future.description"></a><h2>Description</h2>
<p>API that is not yet stable enough to be enabled by default. In all likelihood
this will be included in the next release. To use this API you must define a
MACRO. See the p11-kit.h header for more details.</p>
</div>
<div class="refsect1">
<a name="p11-kit-Future.functions_details"></a><h2>Functions</h2>
<div class="refsect2">
<a name="p11-kit-set-progname"></a><h3>p11_kit_set_progname ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_set_progname (<em class="parameter"><code>const <span class="type">char</span> *progname</code></em>);</pre>
<p>Set the program base name that is used by the <code class="literal">enable-in</code>
and <code class="literal">disable-in</code> module configuration options.</p>
<p>Normally this is automatically calculated from the program's argument list.
You would usually call this before initializing p11-kit modules.</p>
<div class="refsect3">
<a name="p11-kit-set-progname.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>progname</p></td>
<td class="parameter_description"><p>the program base name</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-destroyer"></a><h3>p11_kit_destroyer ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
<span class="c_punctuation">(</span>*p11_kit_destroyer<span class="c_punctuation">)</span> (<em class="parameter"><code><span class="type">void</span> *data</code></em>);</pre>
<p>A callback called to free a resource.</p>
<div class="refsect3">
<a name="p11-kit-destroyer.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>data</p></td>
<td class="parameter_description"><p>data to destroy</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-new"></a><h3>p11_kit_iter_new ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="returnvalue">P11KitIter</span></a> *
p11_kit_iter_new (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                  <em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIterBehavior" title="enum P11KitIterBehavior"><span class="type">P11KitIterBehavior</span></a> behavior</code></em>);</pre>
<p>Create a new PKCS#11 iterator for iterating over objects. Only
objects that match the <em class="parameter"><code>uri</code></em>
 will be returned by the iterator.
Relevant information in <em class="parameter"><code>uri</code></em>
 is copied, and you need not keep
<em class="parameter"><code>uri</code></em>
 around.</p>
<p>If no <em class="parameter"><code>uri</code></em>
 is specified then the iterator will iterate over all
objects, unless otherwise filtered.</p>
<div class="refsect3">
<a name="p11-kit-iter-new.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>a PKCS#11 URI to filter on, or <code class="literal">NULL</code>. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="NULL is ok, both for passing and for returning."><span class="acronym">allow-none</span></acronym>]</span></td>
</tr>
<tr>
<td class="parameter_name"><p>behavior</p></td>
<td class="parameter_description"><p>various behavior flags for iterator</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-new.returns"></a><h4>Returns</h4>
<p>a new iterator, which should be freed
with <a class="link" href="p11-kit-Future.html#p11-kit-iter-free" title="p11_kit_iter_free ()"><code class="function">p11_kit_iter_free()</code></a>. </p>
<p><span class="annotation">[<acronym title="Free data after the code is done."><span class="acronym">transfer full</span></acronym>]</span></p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-set-uri"></a><h3>p11_kit_iter_set_uri ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_iter_set_uri (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>,
                      <em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Set the PKCS#11 uri for iterator. Only
objects that match the <em class="parameter"><code>uri</code></em>
 will be returned by the iterator.
Relevant information in <em class="parameter"><code>uri</code></em>
 is copied, and you need not keep
<em class="parameter"><code>uri</code></em>
 around.</p>
<p>If no <em class="parameter"><code>uri</code></em>
 is specified then the iterator will iterate over all
objects, unless otherwise filtered.</p>
<p>This function should be called at most once, and should be
called before iterating begins.</p>
<div class="refsect3">
<a name="p11-kit-iter-set-uri.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>a PKCS#11 URI to filter on, or <code class="literal">NULL</code>. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="NULL is ok, both for passing and for returning."><span class="acronym">allow-none</span></acronym>]</span></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-add-callback"></a><h3>p11_kit_iter_add_callback ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_iter_add_callback (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>,
                           <em class="parameter"><code><a class="link" href="p11-kit-Future.html#p11-kit-iter-callback" title="p11_kit_iter_callback ()"><span class="type">p11_kit_iter_callback</span></a> callback</code></em>,
                           <em class="parameter"><code><span class="type">void</span> *callback_data</code></em>,
                           <em class="parameter"><code><a class="link" href="p11-kit-Future.html#p11-kit-destroyer" title="p11_kit_destroyer ()"><span class="type">p11_kit_destroyer</span></a> callback_destroy</code></em>);</pre>
<p>Adds a callback to the iterator which will be called each time
that an object is iterated.</p>
<p>These callbacks can also perform filtering. If any callback
indicates through it's <code class="literal">matches</code> argument that
the object should not match, then that object will not be iterated
as far as <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> is concerned.</p>
<p>The callbacks will be called with the <code class="literal">matches</code>
set to <code class="literal">CK_TRUE</code> and it's up to filters to change
it to <code class="literal">CK_FALSE</code> when necessary.</p>
<div class="refsect3">
<a name="p11-kit-iter-add-callback.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>callback</p></td>
<td class="parameter_description"><p>a function to call for each iteration</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>callback_data</p></td>
<td class="parameter_description"><p>data to pass to the function. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="NULL is ok, both for passing and for returning."><span class="acronym">allow-none</span></acronym>]</span></td>
</tr>
<tr>
<td class="parameter_name"><p>callback_destroy</p></td>
<td class="parameter_description"><p>used to cleanup the data. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="NULL is ok, both for passing and for returning."><span class="acronym">allow-none</span></acronym>]</span></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-add-filter"></a><h3>p11_kit_iter_add_filter ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_iter_add_filter (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>,
                         <em class="parameter"><code><span class="type">CK_ATTRIBUTE</span> *matching</code></em>,
                         <em class="parameter"><code><span class="type">CK_ULONG</span> count</code></em>);</pre>
<p>Add a filter to limit the objects that the iterator iterates over.</p>
<p>Only objects matching the passed in attributes will be iterated.
This function can be called multiple times.</p>
<p>The <em class="parameter"><code>matching</code></em>
 attributes are copied.</p>
<div class="refsect3">
<a name="p11-kit-iter-add-filter.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>matching</p></td>
<td class="parameter_description"><p>the attributes that the objects should match. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="Parameter points to an array of items."><span class="acronym">array</span></acronym> length=count]</span></td>
</tr>
<tr>
<td class="parameter_name"><p>count</p></td>
<td class="parameter_description"><p>the number of attributes</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-callback"></a><h3>p11_kit_iter_callback ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
<span class="c_punctuation">(</span>*p11_kit_iter_callback<span class="c_punctuation">)</span> (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>,
                          <em class="parameter"><code><span class="type">CK_BBOOL</span> *matches</code></em>,
                          <em class="parameter"><code><span class="type">void</span> *data</code></em>);</pre>
<p>A callback setup with <a class="link" href="p11-kit-Future.html#p11-kit-iter-add-callback" title="p11_kit_iter_add_callback ()"><code class="function">p11_kit_iter_add_callback()</code></a>. This callback is
called for each object iterated.</p>
<p>If the callback sets <em class="parameter"><code>matches</code></em>
 to CK_FALSE, then this object is
skipped and not matched by <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a>. If you return
anything but CKR_OK, then the iteration is stopped, and
<a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> returns the result code.</p>
<div class="refsect3">
<a name="p11-kit-iter-callback.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>matches</p></td>
<td class="parameter_description"><p>whether to match the current object. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="Parameter for returning results. Default is transfer full."><span class="acronym">out</span></acronym>]</span></td>
</tr>
<tr>
<td class="parameter_name"><p>data</p></td>
<td class="parameter_description"><p>callback data</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-callback.returns"></a><h4>Returns</h4>
<p> CKR_OK to continue iterating, CKR_CANCEL to stop, or
anything else to fail</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-begin"></a><h3>p11_kit_iter_begin ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_iter_begin (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>,
                    <em class="parameter"><code><span class="type">CK_FUNCTION_LIST_PTR</span> *modules</code></em>);</pre>
<p>Begin iterating PKCS#11 objects in the given <em class="parameter"><code>modules</code></em>
.</p>
<p>The <em class="parameter"><code>modules</code></em>
 arguments should be a null-terminated list of
pointers to the modules' PKCS#11 function pointers.</p>
<p>For each module, all initialized slots will be iterated over,
having sessions opened for each of them in turn, and searched
for objects matching the search criteria.</p>
<div class="refsect3">
<a name="p11-kit-iter-begin.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>modules</p></td>
<td class="parameter_description"><p>null-terminated list of
modules to iterate over. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="Parameter points to an array of items."><span class="acronym">array</span></acronym> zero-terminated=1]</span></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-begin-with"></a><h3>p11_kit_iter_begin_with ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_iter_begin_with (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>,
                         <em class="parameter"><code><span class="type">CK_FUNCTION_LIST_PTR</span> module</code></em>,
                         <em class="parameter"><code><span class="type">CK_SLOT_ID</span> slot</code></em>,
                         <em class="parameter"><code><span class="type">CK_SESSION_HANDLE</span> session</code></em>);</pre>
<p>Begin iterating PKCS#11 objects in the given <em class="parameter"><code>module</code></em>
.</p>
<p>If <em class="parameter"><code>slot</code></em>
 is non-zero then the iteration will be limited to that
slot.</p>
<p>If <em class="parameter"><code>session</code></em>
 is non-zero then the iteration will be limited to
objects visible through that session, which implies that they
are also limited to the slot which the session was opened for.</p>
<div class="refsect3">
<a name="p11-kit-iter-begin-with.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>the module to iterate over</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>slot</p></td>
<td class="parameter_description"><p>the slot to iterate objects in, or zero. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="NULL is ok, both for passing and for returning."><span class="acronym">allow-none</span></acronym>]</span></td>
</tr>
<tr>
<td class="parameter_name"><p>session</p></td>
<td class="parameter_description"><p>the session to search for objects on, or zero. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="NULL is ok, both for passing and for returning."><span class="acronym">allow-none</span></acronym>]</span></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-next"></a><h3>p11_kit_iter_next ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_iter_next (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Iterate to the next matching object.</p>
<p>To access the object, session and so on, use the <a class="link" href="p11-kit-Future.html#p11-kit-iter-get-object" title="p11_kit_iter_get_object ()"><code class="function">p11_kit_iter_get_object()</code></a>,
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-session" title="p11_kit_iter_get_session ()"><code class="function">p11_kit_iter_get_session()</code></a>, and <a class="link" href="p11-kit-Future.html#p11-kit-iter-get-module" title="p11_kit_iter_get_module ()"><code class="function">p11_kit_iter_get_module()</code></a> functions.</p>
<p>This call must only be called after either <a class="link" href="p11-kit-Future.html#p11-kit-iter-begin" title="p11_kit_iter_begin ()"><code class="function">p11_kit_iter_begin()</code></a>
or <a class="link" href="p11-kit-Future.html#p11-kit-iter-begin-with" title="p11_kit_iter_begin_with ()"><code class="function">p11_kit_iter_begin_with()</code></a> have been called.</p>
<p>Objects which are skipped by callbacks will not be returned here
as matching objects.</p>
<div class="refsect3">
<a name="p11-kit-iter-next.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-next.returns"></a><h4>Returns</h4>
<p> CKR_OK if an object matched, CKR_CANCEL if no more objects, or another error</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-get-kind"></a><h3>p11_kit_iter_get_kind ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-Future.html#P11KitIterKind" title="enum P11KitIterKind"><span class="returnvalue">P11KitIterKind</span></a>
p11_kit_iter_get_kind (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Get the kind of the current match (a module, slot, token, or an
object).</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-get-kind.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-get-kind.returns"></a><h4>Returns</h4>
<p> a <a class="link" href="p11-kit-Future.html#P11KitIterKind" title="enum P11KitIterKind"><span class="type">P11KitIterKind</span></a> value</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-get-module"></a><h3>p11_kit_iter_get_module ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_FUNCTION_LIST_PTR</span>
p11_kit_iter_get_module (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Get the module function pointers for the current matching object.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-get-module.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-get-module.returns"></a><h4>Returns</h4>
<p> the module which the current matching object is in</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-get-slot"></a><h3>p11_kit_iter_get_slot ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_SLOT_ID</span>
p11_kit_iter_get_slot (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Get the slot which the current matching object is on.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-get-slot.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-get-slot.returns"></a><h4>Returns</h4>
<p> the slot of the current matching object</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-get-slot-info"></a><h3>p11_kit_iter_get_slot_info ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_SLOT_INFO</span> *
p11_kit_iter_get_slot_info (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Get the slot info for the slot which the current matching object is on.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-get-slot-info.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-get-slot-info.returns"></a><h4>Returns</h4>
<p> the slot of the current matching object.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-get-token"></a><h3>p11_kit_iter_get_token ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_TOKEN_INFO</span> *
p11_kit_iter_get_token (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Get the token info for the token which the current matching object is on.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-get-token.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-get-token.returns"></a><h4>Returns</h4>
<p> the slot of the current matching object.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-get-session"></a><h3>p11_kit_iter_get_session ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_SESSION_HANDLE</span>
p11_kit_iter_get_session (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Get the session which the current matching object is accessible
through.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<p>The session may be closed after the next <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> call
unless <a class="link" href="p11-kit-Future.html#p11-kit-iter-keep-session" title="p11_kit_iter_keep_session ()"><code class="function">p11_kit_iter_keep_session()</code></a> is called.</p>
<div class="refsect3">
<a name="p11-kit-iter-get-session.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-get-session.returns"></a><h4>Returns</h4>
<p> the session used to find the current matching object</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-keep-session"></a><h3>p11_kit_iter_keep_session ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_SESSION_HANDLE</span>
p11_kit_iter_keep_session (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>After calling this function the session open for iterating
the current object will not be automatically closed by
the iterator after later calls to <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> or
<a class="link" href="p11-kit-Future.html#p11-kit-iter-free" title="p11_kit_iter_free ()"><code class="function">p11_kit_iter_free()</code></a>.</p>
<p>It is the callers responsibility to close this session,
after the iterator has been freed. The session may still be
used by the iterator if further iterations are performed.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-keep-session.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-keep-session.returns"></a><h4>Returns</h4>
<p> the current session</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-get-object"></a><h3>p11_kit_iter_get_object ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_OBJECT_HANDLE</span>
p11_kit_iter_get_object (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Get the current matching object.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-get-object.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-get-object.returns"></a><h4>Returns</h4>
<p> the current matching object</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-get-attributes"></a><h3>p11_kit_iter_get_attributes ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_iter_get_attributes (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>,
                             <em class="parameter"><code><span class="type">CK_ATTRIBUTE</span> *templ</code></em>,
                             <em class="parameter"><code><span class="type">CK_ULONG</span> count</code></em>);</pre>
<p>Get attributes for the current matching object.</p>
<p>This calls <code class="literal">C_GetAttributeValue</code> for the object
currently iterated to. Return value and attribute memory behavior
is identical to the PKCS#11 <code class="literal">C_GetAttributeValue</code>
function.</p>
<p>You might choose to use <a class="link" href="p11-kit-Future.html#p11-kit-iter-load-attributes" title="p11_kit_iter_load_attributes ()"><code class="function">p11_kit_iter_load_attributes()</code></a> for a more
helpful variant.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-get-attributes.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>templ</p></td>
<td class="parameter_description"><p>the attributes to get. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="Parameter points to an array of items."><span class="acronym">array</span></acronym> length=count][<acronym title="Parameter for input and for returning results. Default is transfer full."><span class="acronym">inout</span></acronym>]</span></td>
</tr>
<tr>
<td class="parameter_name"><p>count</p></td>
<td class="parameter_description"><p>the number of attributes</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-get-attributes.returns"></a><h4>Returns</h4>
<p> The result from <code class="literal">C_GetAttributeValue</code>.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-load-attributes"></a><h3>p11_kit_iter_load_attributes ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_iter_load_attributes (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>,
                              <em class="parameter"><code><span class="type">CK_ATTRIBUTE</span> *templ</code></em>,
                              <em class="parameter"><code><span class="type">CK_ULONG</span> count</code></em>);</pre>
<p>Retrieve attributes for the current matching object.</p>
<p>Each attribute in the array will be filled in with the value
of that attribute retrieved from the object. After use the
attribute value memory pointed to by the <code class="literal">pValue</code>
of each attribute should be freed with the <code class="literal">free()</code>
function.</p>
<p>If the <code class="literal">pValue</code> of an attribute is not <code class="literal">NULL</code> passed
to this function, then it will be passed to
<code class="literal">realloc()</code> to allocate the correct amount
of space for the attribute value.</p>
<p>If any attribute is not present on the object, or is sensitive and
cannot be retrieved, then the <code class="literal">pValue</code> will be NULL.
If <code class="literal">pValue</code> was not <code class="literal">NULL</code> when passed to this function
then it will be freed with <code class="literal">free()</code>. In these
cases <code class="literal">CKR_OK</code> is returned.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-load-attributes.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>templ</p></td>
<td class="parameter_description"><p>the attributes to load. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="Parameter points to an array of items."><span class="acronym">array</span></acronym> length=count][<acronym title="Parameter for input and for returning results. Default is transfer full."><span class="acronym">inout</span></acronym>]</span></td>
</tr>
<tr>
<td class="parameter_name"><p>count</p></td>
<td class="parameter_description"><p>the number of attributes</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-load-attributes.returns"></a><h4>Returns</h4>
<p> CKR_OK or a failure code</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-destroy-object"></a><h3>p11_kit_iter_destroy_object ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_iter_destroy_object (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Destroy the current matching object.</p>
<p>This can only be called after <a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()"><code class="function">p11_kit_iter_next()</code></a> succeeds.</p>
<div class="refsect3">
<a name="p11-kit-iter-destroy-object.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-iter-destroy-object.returns"></a><h4>Returns</h4>
<p> CKR_OK or a failure code</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter-free"></a><h3>p11_kit_iter_free ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_iter_free (<em class="parameter"><code><a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter"><span class="type">P11KitIter</span></a> *iter</code></em>);</pre>
<p>Frees the iterator and all resources, such as sessions
or callbacks held by the iterator.</p>
<div class="refsect3">
<a name="p11-kit-iter-free.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>iter</p></td>
<td class="parameter_description"><p>the iterator</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-remote-serve-module"></a><h3>p11_kit_remote_serve_module ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_remote_serve_module (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *module</code></em>,
                             <em class="parameter"><code><span class="type">int</span> in_fd</code></em>,
                             <em class="parameter"><code><span class="type">int</span> out_fd</code></em>);</pre>
<p>Run a module on a given pair of input/output FDs.</p>
<div class="refsect3">
<a name="p11-kit-remote-serve-module.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>a pointer to a loaded module</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>in_fd</p></td>
<td class="parameter_description"><p>input fd</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>out_fd</p></td>
<td class="parameter_description"><p>output fd</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-remote-serve-module.returns"></a><h4>Returns</h4>
<p> 0 if success, non-zero otherwise.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-remote-serve-token"></a><h3>p11_kit_remote_serve_token ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_remote_serve_token (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *module</code></em>,
                            <em class="parameter"><code><span class="type">CK_TOKEN_INFO</span> *token</code></em>,
                            <em class="parameter"><code><span class="type">int</span> in_fd</code></em>,
                            <em class="parameter"><code><span class="type">int</span> out_fd</code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_remote_serve_token</code> is deprecated and should not be used in newly-written code.</p>
<p>use <a class="link" href="p11-kit-Future.html#p11-kit-remote-serve-tokens" title="p11_kit_remote_serve_tokens ()"><code class="function">p11_kit_remote_serve_tokens()</code></a></p>
</div>
<p>Run a token wrapped in a module on a given pair of input/output FDs.</p>
<div class="refsect3">
<a name="p11-kit-remote-serve-token.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>a pointer to a loaded module</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>token</p></td>
<td class="parameter_description"><p>a token info</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>in_fd</p></td>
<td class="parameter_description"><p>input fd</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>out_fd</p></td>
<td class="parameter_description"><p>output fd</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-remote-serve-token.returns"></a><h4>Returns</h4>
<p> 0 if success, non-zero otherwise.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-remote-serve-tokens"></a><h3>p11_kit_remote_serve_tokens ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_remote_serve_tokens (<em class="parameter"><code>const <span class="type">char</span> **tokens</code></em>,
                             <em class="parameter"><code><span class="type">size_t</span> n_tokens</code></em>,
                             <em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *provider</code></em>,
                             <em class="parameter"><code><span class="type">int</span> in_fd</code></em>,
                             <em class="parameter"><code><span class="type">int</span> out_fd</code></em>);</pre>
<p>Expose tokens on a given pair of input/output FDs.  If <em class="parameter"><code>provider</code></em>
 is
not NULL, all the tokens must be provided by the same module.</p>
<div class="refsect3">
<a name="p11-kit-remote-serve-tokens.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>tokens</p></td>
<td class="parameter_description"><p>a list of token URIs</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>n_tokens</p></td>
<td class="parameter_description"><p>the length of <em class="parameter"><code>tokens</code></em>
</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>provider</p></td>
<td class="parameter_description"><p>a PKCS#11 module that provides the tokens. </p></td>
<td class="parameter_annotations"><span class="annotation">[<a href="http://foldoc.org/nullable"><span class="acronym">nullable</span></a>]</span></td>
</tr>
<tr>
<td class="parameter_name"><p>in_fd</p></td>
<td class="parameter_description"><p>input fd</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>out_fd</p></td>
<td class="parameter_description"><p>output fd</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-remote-serve-tokens.returns"></a><h4>Returns</h4>
<p> 0 if success, non-zero otherwise.</p>
</div>
</div>
</div>
<div class="refsect1">
<a name="p11-kit-Future.other_details"></a><h2>Types and Values</h2>
<div class="refsect2">
<a name="P11KitIter"></a><h3>P11KitIter</h3>
<p>Used to iterate over PKCS#11 objects, tokens, slots, and modules.</p>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-iter"></a><h3>p11_kit_iter</h3>
</div>
<hr>
<div class="refsect2">
<a name="P11KitIterKind"></a><h3>enum P11KitIterKind</h3>
<p>The kind of the current match.</p>
<div class="refsect3">
<a name="P11KitIterKind.members"></a><h4>Members</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="300px" class="enum_members_name">
<col class="enum_members_description">
<col width="200px" class="enum_members_annotations">
</colgroup>
<tbody>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-KIND-MODULE:CAPS"></a>P11_KIT_ITER_KIND_MODULE</p></td>
<td class="enum_member_description">
<p>The iterator is pointing to a module.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-KIND-SLOT:CAPS"></a>P11_KIT_ITER_KIND_SLOT</p></td>
<td class="enum_member_description">
<p>The iterator is pointing to a slot.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-KIND-TOKEN:CAPS"></a>P11_KIT_ITER_KIND_TOKEN</p></td>
<td class="enum_member_description">
<p>The iterator is pointing to a token.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-KIND-OBJECT:CAPS"></a>P11_KIT_ITER_KIND_OBJECT</p></td>
<td class="enum_member_description">
<p>The iterator is pointing to an object.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-KIND-SESSION:CAPS"></a>P11_KIT_ITER_KIND_SESSION</p></td>
<td class="enum_member_description">
<p>The iterator is pointing to a token with an
active session.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-KIND-UNKNOWN:CAPS"></a>P11_KIT_ITER_KIND_UNKNOWN</p></td>
<td class="enum_member_description">
<p>The iterator doesn't point to anything.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="P11KitIterBehavior"></a><h3>enum P11KitIterBehavior</h3>
<p>Various flags controlling the behavior of the iterator.</p>
<div class="refsect3">
<a name="P11KitIterBehavior.members"></a><h4>Members</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="300px" class="enum_members_name">
<col class="enum_members_description">
<col width="200px" class="enum_members_annotations">
</colgroup>
<tbody>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-BUSY-SESSIONS:CAPS"></a>P11_KIT_ITER_BUSY_SESSIONS</p></td>
<td class="enum_member_description">
<p>Allow the iterator's sessions to be
in a busy state when the iterator returns an object.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-WANT-WRITABLE:CAPS"></a>P11_KIT_ITER_WANT_WRITABLE</p></td>
<td class="enum_member_description">
<p>Try to open read-write sessions when
iterating over objects.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-WITH-MODULES:CAPS"></a>P11_KIT_ITER_WITH_MODULES</p></td>
<td class="enum_member_description">
<p>Stop at each module while iterating.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-WITH-SLOTS:CAPS"></a>P11_KIT_ITER_WITH_SLOTS</p></td>
<td class="enum_member_description">
<p>Stop at each slot while iterating.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-WITH-TOKENS:CAPS"></a>P11_KIT_ITER_WITH_TOKENS</p></td>
<td class="enum_member_description">
<p>Stop at each token while iterating.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-WITHOUT-OBJECTS:CAPS"></a>P11_KIT_ITER_WITHOUT_OBJECTS</p></td>
<td class="enum_member_description">
<p>Ignore objects while iterating.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-WITH-LOGIN:CAPS"></a>P11_KIT_ITER_WITH_LOGIN</p></td>
<td> </td>
<td> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-ITER-WITH-SESSIONS:CAPS"></a>P11_KIT_ITER_WITH_SESSIONS</p></td>
<td class="enum_member_description">
<p>Stop at each token while iterating (after
opening a session).</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>