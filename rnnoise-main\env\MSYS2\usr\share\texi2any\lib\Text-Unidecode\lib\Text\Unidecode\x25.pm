# Time-stamp: "2016-07-25 07:04:30 MDT"
$Text::Unidecode::Char[0x25] = [
qq{-}, qq{-}, qq{|}, qq{|}, qq{-}, qq{-}, qq{|}, qq{|}, qq{-}, qq{-}, qq{|}, qq{|}, qq{+}, qq{+}, qq{+}, qq{+},
qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+},
qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+},
qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+},
qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{-}, qq{-}, qq{|}, qq{|},
qq{-}, qq{|}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+},
qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+}, qq{+},
qq{+}, qq{/}, qq{\\}, 'X', qq{-}, qq{|}, qq{-}, qq{|}, qq{-}, qq{|}, qq{-}, qq{|}, qq{-}, qq{|}, qq{-}, qq{|},
qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#},
qq{#}, qq{#}, qq{#}, qq{#}, qq{-}, qq{|}, '[?]', '[?]', '[?]', '[?]', '[?]', '[?]', '[?]', '[?]', '[?]', '[?]',
qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#},
qq{#}, qq{#}, qq{^}, qq{^}, qq{^}, qq{^}, qq{>}, qq{>}, qq{>}, qq{>}, qq{>}, qq{>}, 'V', 'V', 'V', 'V',
qq{<}, qq{<}, qq{<}, qq{<}, qq{<}, qq{<}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*},
qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*},
qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{*}, qq{#}, qq{#}, qq{#}, qq{#}, qq{#}, qq{^}, qq{^}, qq{^}, 'O',

qq{#}, qq{#}, qq{#}, qq{#},
qq{O}, qq{O}, qq{O}, qq{O},
'/', '\\', '\\', '#',
'#', '#', '#', '/',
];
1;
