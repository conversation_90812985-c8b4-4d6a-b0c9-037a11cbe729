<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_KDF-X942-CONCAT</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
    </ul>
  </li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_KDF-X942-CONCAT - The X942 Concat EVP_KDF implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP_KDF-X942-CONCAT algorithm is identical to EVP_KDF-X963. It is used for key agreement to derive a key using input such as a shared secret key and shared info.</p>

<h2 id="Identity">Identity</h2>

<p>&quot;X942KDF_CONCAT&quot; is the name for this implementation; it can be used with the EVP_KDF_fetch() function.</p>

<p>This is an alias for &quot;X963KDF&quot;.</p>

<p>See <a href="../man7/EVP_KDF-X963.html">EVP_KDF-X963(7)</a> for a list of supported parameters and examples.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


