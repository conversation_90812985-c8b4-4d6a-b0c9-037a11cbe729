# help.ro.txt - ro GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
Este sarcina d-voastră să atribuiţi o valoare aici; această valoare
nu va fi niciodată exportată pentru o terţă parte.  Trebuie să
implementăm reţeaua-de-încredere; aceasta nu are nimic în comun cu
certificatele-de-reţea (create implicit).
.

.gpg.edit_ownertrust.set_ultimate.okay
Pentru a construi Reţeaua-de-Încredere, GnuPG trebuie să ştie care chei
au nivel de încredere suprem - acestea de obicei sunt cheile pentru care
aveţi acces la cheia secretă.  Răspundeţi "da" pentru a seta
această cheie cu nivel de încredere suprem

.

.gpg.untrusted_key.override
Dacă doriţi oricum să folosiţi această cheie fără încredere, răspundeţi "da".
.

.gpg.pklist.user_id.enter
Introduceţi ID-ul utilizator al destinatarului mesajului.
.

.gpg.keygen.algo
Selectaţi algoritmul de folosit.

DSA (aka DSS) este Digital Signature Algorithm şi poate fi folosit numai
pentru semnături.

Elgamal este un algoritm numai pentru cifrare.

RSA poate fi folosit pentru semnături sau cifrare.

Prima cheie (primară) trebuie să fie întotdeauna o cheie cu care se poate semna.
.

.gpg.keygen.algo.rsa_se
În general nu este o idee bună să folosiţi aceeaşi cheie şi pentru
semnare şi pentru cifrare.  Acest algoritm ar trebui folosit numai
în anumite domenii.  Vă rugăm consultaţi mai întâi un expert în domeniu.
.

.gpg.keygen.size
Introduceţi lungimea cheii
.

.gpg.keygen.size.huge.okay
Răspundeţi "da" sau "nu"
.

.gpg.keygen.size.large.okay
Răspundeţi "da" sau "nu"
.

.gpg.keygen.valid
Introduceţi valoarea cerută precum a arătat la prompt.
Este posibil să introduceţi o dată ISO (AAAA-LL-ZZ) dar nu veţi
obţine un răspuns de eroare bun - în loc sistemul încearcă să
interpreteze valoare dată ca un interval.
.

.gpg.keygen.valid.okay
Răspundeţi "da" sau "nu"
.

.gpg.keygen.name
Introduceţi numele deţinătorului cheii
.

.gpg.keygen.email
vă rugăm introduceţi o adresă de email (opţională dar recomandată)
.

.gpg.keygen.comment
Vă rugăm introduceţi un comentriu opţional
.

.gpg.keygen.userid.cmd
N  pentru a schimba numele.
C  pentru a schimba comentariul.
E  pentru a schimba adresa de email.
O  pentru a continua cu generarea cheii.
T  pentru a termina generarea cheii.
.

.gpg.keygen.sub.okay
Răspundeţi "da" (sau numai "d") dacă sunteţi OK să generaţi subcheia.
.

.gpg.sign_uid.okay
Răspundeţi "da" sau "nu"
.

.gpg.sign_uid.class
Când semnaţi un ID utilizator pe o cheie ar trebui să verificaţi mai întâi
că cheia aparţine persoanei numite în ID-ul utilizator.  Este util şi altora
să ştie cât de atent aţi verificat acest lucru.

"0" înseamnă că nu pretindeţi nimic despre cât de atent aţi verificat cheia
"1" înseamnă că credeţi că cheia este a persoanei ce pretinde că este
    proprietarul ei, dar n-aţi putut, sau nu aţi verificat deloc cheia.
    Aceasta este utilă pentru verificare "persona", unde semnaţi cheia
    unui utilizator pseudonim.

"2" înseamnă că aţi făcut o verificare supericială a cheii.  De exemplu,
    aceasta ar putea însemna că aţi verificat amprenta cheii şi aţi verificat
    ID-ul utilizator de pe cheie cu un ID cu poză.

"3" înseamnă că aţi făcut o verificare extensivă a cheii.  De exemplu,
    aceasta ar putea însemna că aţi verificat amprenta cheii cu proprietarul
    cheii în persoană, că aţi verificat folosind un document dificil de
    falsificat cu poză (cum ar fi un paşaport) că numele proprietarului cheii
    este acelaşi cu numele ID-ului utilizator al cheii şi că aţi verificat
    (schimbând emailuri) că adresa de email de pe cheie aparţine proprietarului
cheii.

De notat că exemplele date pentru nivelele 2 şi 3 ceva mai sus sunt *numai*
exemple. La urma urmei, d-voastră decideţi ce înseamnă "superficial" şi
"extensiv" pentru d-voastră când semnaţi alte chei.

Dacă nu ştiţi care este răspunsul, răspundeţi "0".
.

.gpg.change_passwd.empty.okay
Răspundeţi "da" sau "nu"
.

.gpg.keyedit.save.okay
Răspundeţi "da" sau "nu"
.

.gpg.keyedit.cancel.okay
Răspundeţi "da" sau "nu"
.

.gpg.keyedit.sign_all.okay
Răspundeţi "da" dacă doriţi să semnaţi TOATE ID-urile utilizator
.

.gpg.keyedit.remove.uid.okay
Răspundeţi "da" dacă într-adevăr doriţi să ştergeţi acest ID utilizator.
Toate certificatele sunt de asemenea pierdute!
.

.gpg.keyedit.remove.subkey.okay
Răspundeţi "da" dacă este OK să ştergeţi subcheia
.

.gpg.keyedit.delsig.valid
Aceasta este o semnătură validă pe cheie; în mod normal n-ar trebui
să ştergeţi această semnătură pentru că aceasta ar putea fi importantăla stabilirea conexiunii de încredere la cheie sau altă cheie certificată
de această cheie.
.

.gpg.keyedit.delsig.unknown
Această semnătură nu poate fi verificată pentru că nu aveţi cheia
corespunzătoare.  Ar trebui să amânaţi ştergerea sa până ştiţi care
cheie a fost folosită pentru că această cheie de semnare ar putea
constitui o conexiune de încredere spre o altă cheie deja certificată.
.

.gpg.keyedit.delsig.invalid
Semnătura nu este validă.  Aceasta ar trebui ştearsă de pe inelul
d-voastră de chei.
.

.gpg.keyedit.delsig.selfsig
Aceasta este o semnătură care leagă ID-ul utilizator de cheie.
De obicei nu este o idee bună să ştergeţi o asemenea semnătură.
De fapt, GnuPG ar putea să nu mai poată folosi această cheie.
Aşa că faceţi acest lucru numai dacă această auto-semnătură este
dintr-o oarecare cauză invalidă şi o a doua este disponibilă.
.

.gpg.keyedit.updpref.okay
Schimbaţi toate preferinţele ale tuturor ID-urilor utilizator (sau doar
cele selectate) conform cu lista curentă de preferinţe.  Timestamp-urile
tuturor auto-semnăturilor afectate vor fi avansate cu o secundă.

.

.gpg.passphrase.enter
Vă rugăm introduceţi fraza-parolă; aceasta este o propoziţie secretă 

.

.gpg.passphrase.repeat
Vă rugăm repetaţi ultima frază-parolă, pentru a fi sigur(ă) ce aţi tastat.
.

.gpg.detached_signature.filename
Daţi numele fişierului la care se aplică semnătura
.

.gpg.openfile.overwrite.okay
Răspundeţi "da" dacă este OK să suprascrieţi fişierul
.

.gpg.openfile.askoutname
Vă rugăm introduceţi un nou nume-fişier. Dacă doar apăsaţi RETURN,
va fi folosit fişierul implicit (arătat în paranteze).
.

.gpg.ask_revocation_reason.code
Ar trebui să specificaţi un motiv pentru certificare.  În funcţie de
context aveţi posibilitatea să alegeţi din această listă:
  "Cheia a fost compromisă"
      Folosiţi această opţiune dacă aveţi un motiv să credeţi că persoane
      neautorizate au avut acces la cheia d-voastră secretă.
  "Cheia este înlocuită"
      Folosiţi această opţiune dacă înlocuiţi cheia cu una nouă.
  "Cheia nu mai este folosită"
      Folosiţi această opţiune dacă pensionaţi cheia.
  "ID-ul utilizator nu mai este valid"
      Folosiţi această opţiune dacă ID-ul utilizator nu mai trebuie folosit;
      de obicei folosită pentru a marca o adresă de email ca invalidă.

.

.gpg.ask_revocation_reason.text
Dacă doriţi, puteţi introduce un text descriind de ce publicaţi acest
certificat de revocare.  Vă rugăm fiţi concis.
O linie goală termină textul.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
