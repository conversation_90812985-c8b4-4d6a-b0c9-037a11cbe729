.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_fingerprint" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_fingerprint \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_fingerprint(gnutls_x509_crt_t " cert ", gnutls_digest_algorithm_t " algo ", void * " buf ", size_t * " buf_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "gnutls_digest_algorithm_t algo" 12
is a digest algorithm
.IP "void * buf" 12
a pointer to a structure to hold the fingerprint (may be null)
.IP "size_t * buf_size" 12
initially holds the size of  \fIbuf\fP 
.SH "DESCRIPTION"
This function will calculate and copy the certificate's fingerprint
in the provided buffer. The fingerprint is a hash of the DER\-encoded
data of the certificate.

If the buffer is null then only the size will be filled.
.SH "RETURNS"
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if the provided buffer is
not long enough, and in that case the *buf_size will be updated
with the required size.  On success 0 is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
