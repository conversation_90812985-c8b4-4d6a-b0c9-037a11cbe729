.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pem_base64_encode" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pem_base64_encode \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_pem_base64_encode(const char * " msg ", const gnutls_datum_t * " data ", char * " result ", size_t * " result_size ");"
.SH ARGUMENTS
.IP "const char * msg" 12
is a message to be put in the header (may be \fBNULL\fP)
.IP "const gnutls_datum_t * data" 12
contain the raw data
.IP "char * result" 12
the place where base64 data will be copied
.IP "size_t * result_size" 12
holds the size of the result
.SH "DESCRIPTION"
This function will convert the given data to printable data, using
the base64 encoding. This is the encoding used in PEM messages.

The output string will be null terminated, although the output size will
not include the terminating null.
.SH "RETURNS"
On success \fBGNUTLS_E_SUCCESS\fP (0) is returned,
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP is returned if the buffer given is
not long enough, or 0 on success.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
