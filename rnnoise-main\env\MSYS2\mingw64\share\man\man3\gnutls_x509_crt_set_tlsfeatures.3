.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_tlsfeatures" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_tlsfeatures \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_tlsfeatures(gnutls_x509_crt_t " crt ", gnutls_x509_tlsfeatures_t " features ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
A X.509 certificate
.IP "gnutls_x509_tlsfeatures_t features" 12
If the function succeeds, the
features will be added to the certificate.
.SH "DESCRIPTION"
This function will set the certificates
X.509 TLS extension from the given structure.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error value.
.SH "SINCE"
3.5.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
