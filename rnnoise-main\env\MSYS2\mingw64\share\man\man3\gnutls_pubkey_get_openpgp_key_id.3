.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_get_openpgp_key_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_get_openpgp_key_id \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_get_openpgp_key_id(gnutls_pubkey_t " key ", unsigned int " flags ", unsigned char * " output_data ", size_t * " output_data_size ", unsigned int * " subkey ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
Holds the public key
.IP "unsigned int flags" 12
should be one of the flags from \fBgnutls_keyid_flags_t\fP
.IP "unsigned char * output_data" 12
will contain the key ID
.IP "size_t * output_data_size" 12
holds the size of output_data (and will be
replaced by the actual size of parameters)
.IP "unsigned int * subkey" 12
ignored
.SH "DESCRIPTION"
This function is no\-op.
.SH "RETURNS"
\fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
