.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_copy_attached_extension" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_copy_attached_extension \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_copy_attached_extension(const char * " token_url ", gnutls_x509_crt_t " crt ", gnutls_datum_t * " data ", const char * " label ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * token_url" 12
A PKCS \fB11\fP URL specifying a token
.IP "gnutls_x509_crt_t crt" 12
An X.509 certificate object
.IP "gnutls_datum_t * data" 12
the attached extension
.IP "const char * label" 12
A name to be used for the attached extension (may be \fBNULL\fP)
.IP "unsigned int flags" 12
One of GNUTLS_PKCS11_OBJ_FLAG_*
.SH "DESCRIPTION"
This function will copy an the attached extension in  \fIdata\fP for
the certificate provided in  \fIcrt\fP in the PKCS \fB11\fP token specified
by the URL (typically a trust module). The extension must be in
RFC5280 Extension format.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.3.8
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
