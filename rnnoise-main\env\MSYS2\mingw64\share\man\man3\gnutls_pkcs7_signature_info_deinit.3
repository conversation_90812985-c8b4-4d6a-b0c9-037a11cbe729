.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_signature_info_deinit" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_signature_info_deinit \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "void gnutls_pkcs7_signature_info_deinit(gnutls_pkcs7_signature_info_st * " info ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_signature_info_st * info" 12
should point to a \fBgnutls_pkcs7_signature_info_st\fP structure
.SH "DESCRIPTION"
This function will deinitialize any allocated value in the
provided \fBgnutls_pkcs7_signature_info_st\fP.
.SH "SINCE"
3.4.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
