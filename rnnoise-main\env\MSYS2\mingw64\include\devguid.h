/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

DEFINE_GUID(GUID_DEVCLASS_1394,0x6bdd1fc1,0x810f,0x11d0,0xbe,0xc7,0x08,0x00,0x2b,0xe2,0x09,0x2f);
DEFINE_GUID(GUID_DEVCLASS_1394DEBUG,0x66f250d6,0x7801,0x4a64,0xb1,0x39,0xee,0xa8,0x0a,0x45,0x0b,0x24);
DEFINE_GUID(GUID_DEVCLASS_61883,0x7ebefbc0,0x3200,0x11d2,0xb4,0xc2,0x00,0xa0,0xc9,0x69,0x7d,0x07);
DEFINE_GUID(GUID_DEVCLASS_ADAPTER,0x4d36e964,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_APMSUPPORT,0xd45b1c18,0xc8fa,0x11d1,0x9f,0x77,0x00,0x00,0xf8,0x05,0xf5,0x30);
DEFINE_GUID(GUID_DEVCLASS_AVC,0xc06ff265,0xae09,0x48f0,0x81,0x2c,0x16,0x75,0x3d,0x7c,0xba,0x83);
DEFINE_GUID(GUID_DEVCLASS_BATTERY,0x72631e54,0x78a4,0x11d0,0xbc,0xf7,0x00,0xaa,0x00,0xb7,0xb3,0x2a);
DEFINE_GUID(GUID_DEVCLASS_BIOMETRIC,0x53d29ef7,0x377c,0x4d14,0x86,0x4b,0xeb,0x3a,0x85,0x76,0x93,0x59);
DEFINE_GUID(GUID_DEVCLASS_BLUETOOTH,0xe0cbf06c,0xcd8b,0x4647,0xbb,0x8a,0x26,0x3b,0x43,0xf0,0xf9,0x74);
DEFINE_GUID(GUID_DEVCLASS_CAMERA,0xca3e7ab9,0xb4c3,0x4ae6,0x82,0x51,0x57,0x9e,0xf9,0x33,0x89,0x0f);
DEFINE_GUID(GUID_DEVCLASS_CDROM,0x4d36e965,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_COMPUTEACCELERATOR,0xf01a9d53,0x3ff6,0x48d2,0x9f,0x97,0xc8,0xa7,0x00,0x4b,0xe1,0x0c);
DEFINE_GUID(GUID_DEVCLASS_COMPUTER,0x4d36e966,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_DECODER,0x6bdd1fc2,0x810f,0x11d0,0xbe,0xc7,0x08,0x00,0x2b,0xe2,0x09,0x2f);
DEFINE_GUID(GUID_DEVCLASS_DISKDRIVE,0x4d36e967,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_DISPLAY,0x4d36e968,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_DOT4,0x48721b56,0x6795,0x11d2,0xb1,0xa8,0x00,0x80,0xc7,0x2e,0x74,0xa2);
DEFINE_GUID(GUID_DEVCLASS_DOT4PRINT,0x49ce6ac8,0x6f86,0x11d2,0xb1,0xe5,0x00,0x80,0xc7,0x2e,0x74,0xa2);
DEFINE_GUID(GUID_DEVCLASS_EHSTORAGESILO,0x9da2b80f,0xf89f,0x4a49,0xa5,0xc2,0x51,0x1b,0x08,0x5b,0x9e,0x8a);
DEFINE_GUID(GUID_DEVCLASS_ENUM1394,0xc459df55,0xdb08,0x11d1,0xb0,0x09,0x00,0xa0,0xc9,0x08,0x1f,0xf6);
DEFINE_GUID(GUID_DEVCLASS_EXTENSION,0xe2f84ce7,0x8efa,0x411c,0xaa,0x69,0x97,0x45,0x4c,0xa4,0xcb,0x57);
DEFINE_GUID(GUID_DEVCLASS_FDC,0x4d36e969,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_FIRMWARE,0xf2e7dd72,0x6468,0x4e36,0xb6,0xf1,0x64,0x88,0xf4,0x2c,0x1b,0x52);
DEFINE_GUID(GUID_DEVCLASS_FLOPPYDISK,0x4d36e980,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_GPS,0x6bdd1fc3,0x810f,0x11d0,0xbe,0xc7,0x08,0x00,0x2b,0xe2,0x09,0x2f);
DEFINE_GUID(GUID_DEVCLASS_HDC,0x4d36e96a,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_HIDCLASS,0x745a17a0,0x74d3,0x11d0,0xb6,0xfe,0x00,0xa0,0xc9,0x0f,0x57,0xda);
DEFINE_GUID(GUID_DEVCLASS_HOLOGRAPHIC,0xd612553d,0x06b1,0x49ca,0x89,0x38,0xe3,0x9e,0xf8,0x0e,0xb1,0x6f);
DEFINE_GUID(GUID_DEVCLASS_IMAGE,0x6bdd1fc6,0x810f,0x11d0,0xbe,0xc7,0x08,0x00,0x2b,0xe2,0x09,0x2f);
DEFINE_GUID(GUID_DEVCLASS_INFINIBAND,0x30ef7132,0xd858,0x4a0c,0xac,0x24,0xb9,0x02,0x8a,0x5c,0xca,0x3f);
DEFINE_GUID(GUID_DEVCLASS_INFRARED,0x6bdd1fc5,0x810f,0x11d0,0xbe,0xc7,0x08,0x00,0x2b,0xe2,0x09,0x2f);
DEFINE_GUID(GUID_DEVCLASS_KEYBOARD,0x4d36e96b,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_LEGACYDRIVER,0x8ecc055d,0x047f,0x11d1,0xa5,0x37,0x00,0x00,0xf8,0x75,0x3e,0xd1);
DEFINE_GUID(GUID_DEVCLASS_MEDIA,0x4d36e96c,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_MEDIUM_CHANGER,0xce5939ae,0xebde,0x11d0,0xb1,0x81,0x00,0x00,0xf8,0x75,0x3e,0xc4);
DEFINE_GUID(GUID_DEVCLASS_MEMORY,0x5099944a,0xf6b9,0x4057,0xa0,0x56,0x8c,0x55,0x02,0x28,0x54,0x4c);
DEFINE_GUID(GUID_DEVCLASS_MODEM,0x4d36e96d,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_MONITOR,0x4d36e96e,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_MOUSE,0x4d36e96f,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_MTD,0x4d36e970,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_MULTIFUNCTION,0x4d36e971,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_MULTIPORTSERIAL,0x50906cb8,0xba12,0x11d1,0xbf,0x5d,0x00,0x00,0xf8,0x05,0xf5,0x30);
DEFINE_GUID(GUID_DEVCLASS_NET,0x4d36e972,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_NETCLIENT,0x4d36e973,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_NETDRIVER,0x87ef9ad1,0x8f70,0x49ee,0xb2,0x15,0xab,0x1f,0xca,0xdc,0xbe,0x3c);
DEFINE_GUID(GUID_DEVCLASS_NETSERVICE,0x4d36e974,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_NETTRANS,0x4d36e975,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_NODRIVER,0x4d36e976,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_PCMCIA,0x4d36e977,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_PNPPRINTERS,0x4658ee7e,0xf050,0x11d1,0xb6,0xbd,0x00,0xc0,0x4f,0xa3,0x72,0xa7);
DEFINE_GUID(GUID_DEVCLASS_PORTS,0x4d36e978,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_PRINTER,0x4d36e979,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_PRINTERUPGRADE,0x4d36e97a,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_PRINTQUEUE,0x1ed2bbf9,0x11f0,0x4084,0xb2,0x1f,0xad,0x83,0xa8,0xe6,0xdc,0xdc);
DEFINE_GUID(GUID_DEVCLASS_PROCESSOR,0x50127dc3,0x0f36,0x415e,0xa6,0xcc,0x4c,0xb3,0xbe,0x91,0x0B,0x65);
DEFINE_GUID(GUID_DEVCLASS_SBP2,0xd48179be,0xec20,0x11d1,0xb6,0xb8,0x00,0xc0,0x4f,0xa3,0x72,0xa7);
DEFINE_GUID(GUID_DEVCLASS_SCMDISK,0x53966cb1,0x4d46,0x4166,0xbf,0x23,0xc5,0x22,0x40,0x3c,0xd4,0x95);
DEFINE_GUID(GUID_DEVCLASS_SCMVOLUME,0x53ccb149,0xe543,0x4c84,0xb6,0xe0,0xbc,0xe4,0xf6,0xb7,0xe8,0x06);
DEFINE_GUID(GUID_DEVCLASS_SCSIADAPTER,0x4d36e97b,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_SECURITYACCELERATOR,0x268c95a1,0xedfe,0x11d3,0x95,0xc3,0x00,0x10,0xdc,0x40,0x50,0xa5);
DEFINE_GUID(GUID_DEVCLASS_SENSOR,0x5175d334,0xc371,0x4806,0xb3,0xba,0x71,0xfd,0x53,0xc9,0x25,0x8d);
DEFINE_GUID(GUID_DEVCLASS_SIDESHOW,0x997b5d8d,0xc442,0x4f2e,0xba,0xf3,0x9c,0x8e,0x67,0x1e,0x9e,0x21);
DEFINE_GUID(GUID_DEVCLASS_SMARTCARDREADER,0x50dd5230,0xba8a,0x11d1,0xbf,0x5d,0x00,0x00,0xf8,0x05,0xf5,0x30);
DEFINE_GUID(GUID_DEVCLASS_SMRDISK,0x53487c23,0x680f,0x4585,0xac,0xc3,0x1f,0x10,0xd6,0x77,0x7e,0x82);
DEFINE_GUID(GUID_DEVCLASS_SMRVOLUME,0x53b3cf03,0x8f5a,0x4788,0x91,0xb6,0xd1,0x9e,0xd9,0xfc,0xcc,0xbf);
DEFINE_GUID(GUID_DEVCLASS_SOFTWARECOMPONENT,0x5c4c3332,0x344d,0x483c,0x87,0x39,0x25,0x9e,0x93,0x4c,0x9c,0xc8);
DEFINE_GUID(GUID_DEVCLASS_SOUND,0x4d36e97c,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_SYSTEM,0x4d36e97d,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_TAPEDRIVE,0x6d807884,0x7d21,0x11cf,0x80,0x1c,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_UNKNOWN,0x4d36e97e,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_DEVCLASS_UCM,0xe6f1aa1c,0x7f3b,0x4473,0xb2,0xe8,0xc9,0x7d,0x8a,0xc7,0x1d,0x53);
DEFINE_GUID(GUID_DEVCLASS_USB,0x36fc9e60,0xc465,0x11cf,0x80,0x56,0x44,0x45,0x53,0x54,0x00,0x00);
DEFINE_GUID(GUID_DEVCLASS_VOLUME,0x71a27cdd,0x812a,0x11d0,0xbe,0xc7,0x08,0x00,0x2b,0xe2,0x09,0x2f);
DEFINE_GUID(GUID_DEVCLASS_VOLUMESNAPSHOT,0x533c5b84,0xec70,0x11d2,0x95,0x05,0x00,0xc0,0x4f,0x79,0xde,0xaf);
DEFINE_GUID(GUID_DEVCLASS_WCEUSBS,0x25dbce51,0x6c8f,0x4a72,0x8a,0x6d,0xb5,0x4c,0x2b,0x4f,0xc8,0x35);
DEFINE_GUID(GUID_DEVCLASS_WPD,0xeec5ad98,0x8080,0x425f,0x92,0x2a,0xda,0xbf,0x3d,0xe3,0xf6,0x9a);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_TOP,0xb369baf4,0x5568,0x4e82,0xa8,0x7e,0xa9,0x3e,0xb1,0x6b,0xca,0x87);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_ACTIVITYMONITOR,0xb86dff51,0xa31e,0x4bac,0xb3,0xcf,0xe8,0xcf,0xe7,0x5c,0x9f,0xc2);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_UNDELETE,0xfe8f1572,0xc67a,0x48c0,0xbb,0xac,0x0b,0x5c,0x6d,0x66,0xca,0xfb);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_ANTIVIRUS,0xb1d1a169,0xc54f,0x4379,0x81,0xdb,0xbe,0xe7,0xd8,0x8d,0x74,0x54);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_REPLICATION,0x48d3ebc4,0x4cf8,0x48ff,0xb8,0x69,0x9c,0x68,0xad,0x42,0xeb,0x9f);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_CONTINUOUSBACKUP,0x71aa14f8,0x6fad,0x4622,0xad,0x77,0x92,0xbb,0x9d,0x7e,0x69,0x47);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_CONTENTSCREENER,0x3e3f0674,0xc83c,0x4558,0xbb,0x26,0x98,0x20,0xe1,0xeb,0xa5,0xc5);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_QUOTAMANAGEMENT,0x8503c911,0xa6c7,0x4919,0x8f,0x79,0x50,0x28,0xf5,0x86,0x6b,0x0c);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_SYSTEMRECOVERY,0x2db15374,0x706e,0x4131,0xa0,0xc7,0xd7,0xc7,0x8e,0xb0,0x28,0x9a);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_CFSMETADATASERVER,0xcdcf0939,0xb75b,0x4630,0xbf,0x76,0x80,0xf7,0xba,0x65,0x58,0x84);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_HSM,0xd546500a,0x2aeb,0x45f6,0x94,0x82,0xf4,0xb1,0x79,0x9c,0x31,0x77);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_COMPRESSION,0xf3586baf,0xb5aa,0x49b5,0x8d,0x6c,0x05,0x69,0x28,0x4c,0x63,0x9f);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_ENCRYPTION,0xa0a701c0,0xa511,0x42ff,0xaa,0x6c,0x06,0xdc,0x03,0x95,0x57,0x6f);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_VIRTUALIZATION,0xf75a86c0,0x10d8,0x4c3a,0xb2,0x33,0xed,0x60,0xe4,0xcd,0xfa,0xac);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_PHYSICALQUOTAMANAGEMENT,0x6a0a8e78,0xbba6,0x4fc4,0xa7,0x09,0x1e,0x33,0xcd,0x09,0xd6,0x7e);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_OPENFILEBACKUP,0xf8ecafa6,0x66d1,0x41a5,0x89,0x9b,0x66,0x58,0x5d,0x72,0x16,0xb7);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_SECURITYENHANCER,0xd02bc3da,0x0c8e,0x4945,0x9b,0xd5,0xf1,0x88,0x3c,0x22,0x6c,0x8c);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_COPYPROTECTION,0x89786ff1,0x9c12,0x402f,0x9c,0x9e,0x17,0x75,0x3c,0x7f,0x43,0x75);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_BOTTOM,0x37765ea0,0x5958,0x4fc9,0xb0,0x4b,0x2f,0xdf,0xef,0x97,0xe5,0x9e);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_SYSTEM,0x5d1b9aaa,0x01e2,0x46af,0x84,0x9f,0x27,0x2b,0x3f,0x32,0x4c,0x46);
DEFINE_GUID(GUID_DEVCLASS_FSFILTER_INFRASTRUCTURE,0xe55fa6f9,0x128c,0x4d04,0xab,0xab,0x63,0x0c,0x74,0xb1,0x45,0x3a);

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */
