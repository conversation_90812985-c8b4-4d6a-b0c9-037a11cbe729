set ::msgcat::header "Project-Id-Version: Git Russian Localization Project\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2016-12-14 22:23+0000\nLast-Translator: <PERSON><PERSON> <<EMAIL>>\nLanguage-Team: Russian (http://www.transifex.com/djm00n/git-po-ru/language/ru/)\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nLanguage: ru\nPlural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"
::msgcat::mcset ru "Couldn't get list of unmerged files:" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u043f\u043e\u043b\u0443\u0447\u0438\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u0444\u0430\u0439\u043b\u043e\u0432 \u043d\u0435\u0437\u0430\u0432\u0435\u0440\u0448\u0451\u043d\u043d\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u044f:"
::msgcat::mcset ru "Color words" "\u0426\u0432\u0435\u0442\u043d\u044b\u0435 \u0441\u043b\u043e\u0432\u0430"
::msgcat::mcset ru "Markup words" "\u041f\u043e\u043c\u0435\u0447\u0435\u043d\u044b\u0435 \u0441\u043b\u043e\u0432\u0430"
::msgcat::mcset ru "Error parsing revisions:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u043f\u0440\u0438 \u0440\u0430\u0437\u0431\u043e\u0440\u0435 \u0440\u0435\u0434\u0430\u043a\u0446\u0438\u0438:"
::msgcat::mcset ru "Error executing --argscmd command:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u044f \u043a\u043e\u043c\u0430\u043d\u0434\u044b \u0437\u0430\u0434\u0430\u043d\u043d\u043e\u0439 --argscmd:"
::msgcat::mcset ru "No files selected: --merge specified but no files are unmerged." "\u0424\u0430\u0439\u043b\u044b \u043d\u0435 \u0432\u044b\u0431\u0440\u0430\u043d\u044b: \u0443\u043a\u0430\u0437\u0430\u043d --merge, \u043d\u043e \u043d\u0435 \u0431\u044b\u043b\u043e \u043d\u0430\u0439\u0434\u0435\u043d\u043e \u043d\u0438 \u043e\u0434\u043d\u043e\u0433\u043e \u0444\u0430\u0439\u043b\u0430 \u0433\u0434\u0435 \u044d\u0442\u0430 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044f \u0434\u043e\u043b\u0436\u043d\u0430 \u0431\u044b\u0442\u044c \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u0430."
::msgcat::mcset ru "No files selected: --merge specified but no unmerged files are within file limit." "\u0424\u0430\u0439\u043b\u044b \u043d\u0435 \u0432\u044b\u0431\u0440\u0430\u043d\u044b: \u0443\u043a\u0430\u0437\u0430\u043d --merge, \u043d\u043e \u0432 \u0440\u0430\u043c\u043a\u0430\u0445 \u0443\u043a\u0430\u0437\u0430\u043d\u043d\u043e\u0433\u043e \u043e\u0433\u0440\u0430\u043d\u0438\u0447\u0435\u043d\u0438\u044f \u043d\u0430 \u0438\u043c\u0435\u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432 \u043d\u0435\u0442 \u043d\u0438 \u043e\u0434\u043d\u043e\u0433\u043e \u0433\u0434\u0435 \u044d\u0442\u0430 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044f \u0434\u043e\u043b\u0436\u043d\u0430 \u0431\u044b\u0442\u044c \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u0430."
::msgcat::mcset ru "Error executing git log:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0437\u0430\u043f\u0443\u0441\u043a\u0430 git log:"
::msgcat::mcset ru "Reading" "\u0427\u0442\u0435\u043d\u0438\u0435"
::msgcat::mcset ru "Reading commits..." "\u0427\u0442\u0435\u043d\u0438\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432..."
::msgcat::mcset ru "No commits selected" "\u041d\u0438\u0447\u0435\u0433\u043e \u043d\u0435 \u0432\u044b\u0431\u0440\u0430\u043d\u043e"
::msgcat::mcset ru "Command line" "\u041a\u043e\u043c\u0430\u043d\u0434\u043d\u0430\u044f \u0441\u0442\u0440\u043e\u043a\u0430"
::msgcat::mcset ru "Can't parse git log output:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u043e\u0431\u0440\u0430\u0431\u043e\u0442\u043a\u0438 \u0432\u044b\u0432\u043e\u0434\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u044b git log:"
::msgcat::mcset ru "No commit information available" "\u041d\u0435\u0442 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u0438 \u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0435"
::msgcat::mcset ru "OK" "Ok"
::msgcat::mcset ru "Cancel" "\u041e\u0442\u043c\u0435\u043d\u0430"
::msgcat::mcset ru "&Update" "\u041e\u0431\u043d\u043e\u0432\u0438\u0442\u044c"
::msgcat::mcset ru "&Reload" "\u041f\u0435\u0440\u0435\u0447\u0438\u0442\u0430\u0442\u044c"
::msgcat::mcset ru "Reread re&ferences" "\u041e\u0431\u043d\u043e\u0432\u0438\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u0441\u0441\u044b\u043b\u043e\u043a"
::msgcat::mcset ru "&List references" "\u0421\u043f\u0438\u0441\u043e\u043a \u0441\u0441\u044b\u043b\u043e\u043a"
::msgcat::mcset ru "Start git &gui" "\u0417\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c git gui"
::msgcat::mcset ru "&Quit" "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044c"
::msgcat::mcset ru "&File" "\u0424\u0430\u0439\u043b"
::msgcat::mcset ru "&Preferences" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438"
::msgcat::mcset ru "&Edit" "\u0420\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c"
::msgcat::mcset ru "&New view..." "\u041d\u043e\u0432\u043e\u0435 \u043f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0435..."
::msgcat::mcset ru "&Edit view..." "\u0420\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0435..."
::msgcat::mcset ru "&Delete view" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u043f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0435"
::msgcat::mcset ru "&All files" "\u0412\u0441\u0435 \u0444\u0430\u0439\u043b\u044b"
::msgcat::mcset ru "&View" "\u041f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0435"
::msgcat::mcset ru "&About gitk" "\u041e gitk"
::msgcat::mcset ru "&Key bindings" "\u041d\u0430\u0437\u043d\u0430\u0447\u0435\u043d\u0438\u044f \u043a\u043b\u0430\u0432\u0438\u0430\u0442\u0443\u0440\u044b"
::msgcat::mcset ru "&Help" "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0430"
::msgcat::mcset ru "SHA1 ID:" "SHA1 ID:"
::msgcat::mcset ru "Row" "\u0421\u0442\u0440\u043e\u043a\u0430"
::msgcat::mcset ru "Find" "\u041f\u043e\u0438\u0441\u043a"
::msgcat::mcset ru "commit" "\u043a\u043e\u043c\u043c\u0438\u0442"
::msgcat::mcset ru "containing:" "\u0441\u043e\u0434\u0435\u0440\u0436\u0430\u0449\u0435\u0435:"
::msgcat::mcset ru "touching paths:" "\u043a\u0430\u0441\u0430\u0442\u0435\u043b\u044c\u043d\u043e \u0444\u0430\u0439\u043b\u043e\u0432:"
::msgcat::mcset ru "adding/removing string:" "\u0434\u043e\u0431\u0430\u0432\u0438\u0432/\u0443\u0434\u0430\u043b\u0438\u0432 \u0441\u0442\u0440\u043e\u043a\u0443:"
::msgcat::mcset ru "changing lines matching:" "\u0438\u0437\u043c\u0435\u043d\u044f\u044f \u0441\u043e\u0432\u043f\u0430\u0434\u0430\u044e\u0449\u0438\u0435 \u0441\u0442\u0440\u043e\u043a\u0438:"
::msgcat::mcset ru "Exact" "\u0422\u043e\u0447\u043d\u043e"
::msgcat::mcset ru "IgnCase" "\u0418\u0433\u043d\u043e\u0440\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0431\u043e\u043b\u044c\u0448\u0438\u0435/\u043c\u0430\u043b\u0435\u043d\u044c\u043a\u0438\u0435"
::msgcat::mcset ru "Regexp" "\u0420\u0435\u0433\u0443\u043b\u044f\u0440\u043d\u044b\u0435 \u0432\u044b\u0440\u0430\u0436\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "All fields" "\u0412\u043e \u0432\u0441\u0435\u0445 \u043f\u043e\u043b\u044f\u0445"
::msgcat::mcset ru "Headline" "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a"
::msgcat::mcset ru "Comments" "\u041a\u043e\u043c\u043c\u0435\u043d\u0442\u0430\u0440\u0438\u0438"
::msgcat::mcset ru "Author" "\u0410\u0432\u0442\u043e\u0440"
::msgcat::mcset ru "Committer" "\u041a\u043e\u043c\u043c\u0438\u0442\u0435\u0440"
::msgcat::mcset ru "Search" "\u041d\u0430\u0439\u0442\u0438"
::msgcat::mcset ru "Diff" "\u0421\u0440\u0430\u0432\u043d\u0438\u0442\u044c"
::msgcat::mcset ru "Old version" "\u0421\u0442\u0430\u0440\u0430\u044f \u0432\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset ru "New version" "\u041d\u043e\u0432\u0430\u044f \u0432\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset ru "Lines of context" "\u0421\u0442\u0440\u043e\u043a \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442\u0430"
::msgcat::mcset ru "Ignore space change" "\u0418\u0433\u043d\u043e\u0440\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043f\u0440\u043e\u0431\u0435\u043b\u044b"
::msgcat::mcset ru "Line diff" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0441\u0442\u0440\u043e\u043a"
::msgcat::mcset ru "Patch" "\u041f\u0430\u0442\u0447"
::msgcat::mcset ru "Tree" "\u0424\u0430\u0439\u043b\u044b"
::msgcat::mcset ru "Diff this -> selected" "\u0421\u0440\u0430\u0432\u043d\u0438\u0442\u044c \u044d\u0442\u043e\u0442 \u043a\u043e\u043c\u043c\u0438\u0442 \u0441 \u0432\u044b\u0434\u0435\u043b\u0435\u043d\u043d\u044b\u043c"
::msgcat::mcset ru "Diff selected -> this" "\u0421\u0440\u0430\u0432\u043d\u0438\u0442\u044c \u0432\u044b\u0434\u0435\u043b\u0435\u043d\u043d\u044b\u0439 \u0441 \u044d\u0442\u0438\u043c \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u043c"
::msgcat::mcset ru "Make patch" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c \u043f\u0430\u0442\u0447"
::msgcat::mcset ru "Create tag" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c \u043c\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Copy commit summary" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044e \u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0435"
::msgcat::mcset ru "Write commit to file" "\u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u043a\u043e\u043c\u043c\u0438\u0442 \u0432 \u0444\u0430\u0439\u043b"
::msgcat::mcset ru "Create new branch" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Cherry-pick this commit" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u044d\u0442\u043e\u0442 \u043a\u043e\u043c\u043c\u0438\u0442 \u0432 \u0442\u0435\u043a\u0443\u0449\u0443\u044e \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Reset HEAD branch to here" "\u0423\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u044c HEAD \u043d\u0430 \u044d\u0442\u043e\u0442 \u043a\u043e\u043c\u043c\u0438\u0442"
::msgcat::mcset ru "Mark this commit" "\u041f\u043e\u043c\u0435\u0442\u0438\u0442\u044c \u044d\u0442\u043e\u0442 \u043a\u043e\u043c\u043c\u0438\u0442"
::msgcat::mcset ru "Return to mark" "\u0412\u0435\u0440\u043d\u0443\u0442\u044c\u0441\u044f \u043d\u0430 \u043f\u043e\u043c\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Find descendant of this and mark" "\u041d\u0430\u0439\u0442\u0438 \u0438 \u043f\u043e\u043c\u0435\u0442\u0438\u0442\u044c \u043f\u043e\u0442\u043e\u043c\u043a\u0430 \u044d\u0442\u043e\u0433\u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0430"
::msgcat::mcset ru "Compare with marked commit" "\u0421\u0440\u0430\u0432\u043d\u0438\u0442\u044c \u0441 \u043f\u043e\u043c\u0435\u0447\u0435\u043d\u043d\u044b\u043c \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u043c"
::msgcat::mcset ru "Diff this -> marked commit" "\u0421\u0440\u0430\u0432\u043d\u0438\u0442\u044c \u0432\u044b\u0434\u0435\u043b\u0435\u043d\u043d\u043e\u0435 \u0441 \u043f\u043e\u043c\u0435\u0447\u0435\u043d\u043d\u044b\u043c \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u043c"
::msgcat::mcset ru "Diff marked commit -> this" "\u0421\u0440\u0430\u0432\u043d\u0438\u0442\u044c \u043f\u043e\u043c\u0435\u0447\u0435\u043d\u043d\u044b\u0439 \u0441 \u044d\u0442\u0438\u043c \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u043c"
::msgcat::mcset ru "Revert this commit" "\u041e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u044d\u0442\u043e\u0433\u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0430"
::msgcat::mcset ru "Check out this branch" "\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u044d\u0442\u0443 \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Rename this branch" "\u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u0442\u044c \u044d\u0442\u0443 \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Remove this branch" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u044d\u0442\u0443 \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Copy branch name" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0438\u043c\u044f \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Highlight this too" "\u041f\u043e\u0434\u0441\u0432\u0435\u0442\u0438\u0442\u044c \u044d\u0442\u043e\u0442 \u0442\u043e\u0436\u0435"
::msgcat::mcset ru "Highlight this only" "\u041f\u043e\u0434\u0441\u0432\u0435\u0442\u0438\u0442\u044c \u0442\u043e\u043b\u044c\u043a\u043e \u044d\u0442\u043e\u0442"
::msgcat::mcset ru "External diff" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u0441\u0440\u0430\u0432\u043d\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Blame parent commit" "\u0410\u0432\u0442\u043e\u0440\u044b \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0440\u043e\u0434\u0438\u0442\u0435\u043b\u044c\u0441\u043a\u043e\u0433\u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0430"
::msgcat::mcset ru "Copy path" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043f\u0443\u0442\u044c"
::msgcat::mcset ru "Show origin of this line" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0438\u0441\u0442\u043e\u0447\u043d\u0438\u043a \u044d\u0442\u043e\u0439 \u0441\u0442\u0440\u043e\u043a\u0438"
::msgcat::mcset ru "Run git gui blame on this line" "\u0417\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c git gui blame \u0434\u043b\u044f \u044d\u0442\u043e\u0439 \u0441\u0442\u0440\u043e\u043a\u0438"
::msgcat::mcset ru "About gitk" "\u041e gitk"
::msgcat::mcset ru "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk \u2014 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430 \u0438\u0441\u0442\u043e\u0440\u0438\u0438 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0435\u0432 git\n\n\u00a9 2005-2016 Paul Mackerras\n\n\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u0435 \u0438 \u0440\u0430\u0441\u043f\u0440\u043e\u0441\u0442\u0440\u0430\u043d\u0435\u043d\u0438\u0435 \u0441\u043e\u0433\u043b\u0430\u0441\u043d\u043e \u0443\u0441\u043b\u043e\u0432\u0438\u044f\u043c GNU General Public License"
::msgcat::mcset ru "Close" "\u0417\u0430\u043a\u0440\u044b\u0442\u044c"
::msgcat::mcset ru "Gitk key bindings" "\u041d\u0430\u0437\u043d\u0430\u0447\u0435\u043d\u0438\u044f \u043a\u043b\u0430\u0432\u0438\u0430\u0442\u0443\u0440\u044b \u0432 Gitk"
::msgcat::mcset ru "Gitk key bindings:" "\u041d\u0430\u0437\u043d\u0430\u0447\u0435\u043d\u0438\u044f \u043a\u043b\u0430\u0432\u0438\u0430\u0442\u0443\u0440\u044b \u0432 Gitk:"
::msgcat::mcset ru "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044c"
::msgcat::mcset ru "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009\u0417\u0430\u043a\u0440\u044b\u0442\u044c \u043e\u043a\u043d\u043e"
::msgcat::mcset ru "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043a \u043f\u0435\u0440\u0432\u043e\u043c\u0443 \u043a\u043e\u043c\u043c\u0438\u0442\u0443"
::msgcat::mcset ru "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043a \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u043c\u0443 \u043a\u043e\u043c\u043c\u0438\u0442\u0443"
::msgcat::mcset ru "<Up>, p, k\u0009Move up one commit" "<Up>, p, k\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u043e\u0434\u0438\u043d \u043a\u043e\u043c\u043c\u0438\u0442 \u0432\u0432\u0435\u0440\u0445"
::msgcat::mcset ru "<Down>, n, j\u0009Move down one commit" "<Down>, n, j\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u043e\u0434\u0438\u043d \u043a\u043e\u043c\u043c\u0438\u0442 \u0432\u043d\u0438\u0437"
::msgcat::mcset ru "<Left>, z, h\u0009Go back in history list" "<Left>, z, h\u0009\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0440\u0430\u043d\u0435\u0435 \u043f\u043e\u0441\u0435\u0449\u0451\u043d\u043d\u043e\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435"
::msgcat::mcset ru "<Right>, x, l\u0009Go forward in history list" "<Right>, x, l\u0009\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0438\u0439 \u043f\u043e\u0441\u0435\u0449\u0451\u043d\u043d\u044b\u0439 \u043a\u043e\u043c\u043c\u0438\u0442"
::msgcat::mcset ru "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n>\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 n \u0440\u043e\u0434\u0438\u0442\u0435\u043b\u044f \u043e\u0442 \u0442\u0435\u043a\u0443\u0449\u0435\u0433\u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0430"
::msgcat::mcset ru "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0443 \u0432\u044b\u0448\u0435 \u0432 \u0441\u043f\u0438\u0441\u043a\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432"
::msgcat::mcset ru "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0443 \u043d\u0438\u0436\u0435 \u0432 \u0441\u043f\u0438\u0441\u043a\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432"
::msgcat::mcset ru "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u043d\u0430\u0447\u0430\u043b\u043e \u0441\u043f\u0438\u0441\u043a\u0430 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432"
::msgcat::mcset ru "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u043a\u043e\u043d\u0435\u0446 \u0441\u043f\u0438\u0441\u043a\u0430 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432"
::msgcat::mcset ru "<%s-Up>\u0009Scroll commit list up one line" "<%s-Up>\u0009\u041f\u0440\u043e\u0432\u0435\u0440\u043d\u0443\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432 \u0432\u0432\u0435\u0440\u0445"
::msgcat::mcset ru "<%s-Down>\u0009Scroll commit list down one line" "<%s-Down>\u0009\u041f\u0440\u043e\u0432\u0435\u0440\u043d\u0443\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432 \u0432\u043d\u0438\u0437"
::msgcat::mcset ru "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009\u041f\u0440\u043e\u0432\u0435\u0440\u043d\u0443\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432 \u043d\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0443 \u0432\u0432\u0435\u0440\u0445"
::msgcat::mcset ru "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009\u041f\u0440\u043e\u0432\u0435\u0440\u043d\u0443\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432 \u043d\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0443 \u0432\u043d\u0438\u0437"
::msgcat::mcset ru "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Up>\u0009\u041f\u043e\u0438\u0441\u043a \u0432 \u043e\u0431\u0440\u0430\u0442\u043d\u043e\u043c \u043f\u043e\u0440\u044f\u0434\u043a\u0435 (\u0432\u0432\u0435\u0440\u0445, \u0441\u0440\u0435\u0434\u0438 \u043d\u043e\u0432\u044b\u0445 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432)"
::msgcat::mcset ru "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Down>\u0009\u041f\u043e\u0438\u0441\u043a (\u0432\u043d\u0438\u0437, \u0441\u0440\u0435\u0434\u0438 \u0441\u0442\u0430\u0440\u044b\u0445 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432)"
::msgcat::mcset ru "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009\u041f\u0440\u043e\u043a\u0440\u0443\u0442\u0438\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043d\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0443 \u0432\u044b\u0448\u0435"
::msgcat::mcset ru "<Backspace>\u0009Scroll diff view up one page" "<Backspace>\u0009\u041f\u0440\u043e\u043a\u0440\u0443\u0442\u0438\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043d\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0443 \u0432\u044b\u0448\u0435"
::msgcat::mcset ru "<Space>\u0009\u0009Scroll diff view down one page" "<Leertaste>\u0009\u0009\u041f\u0440\u043e\u043a\u0440\u0443\u0442\u0438\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043d\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0443 \u043d\u0438\u0436\u0435"
::msgcat::mcset ru "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009\u041f\u0440\u043e\u043a\u0440\u0443\u0442\u0438\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043d\u0430 18 \u0441\u0442\u0440\u043e\u043a \u0432\u0432\u0435\u0440\u0445"
::msgcat::mcset ru "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009\u041f\u0440\u043e\u043a\u0440\u0443\u0442\u0438\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043d\u0430 18 \u0441\u0442\u0440\u043e\u043a \u0432\u043d\u0438\u0437"
::msgcat::mcset ru "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009\u041f\u043e\u0438\u0441\u043a"
::msgcat::mcset ru "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043a \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0435\u043c\u0443 \u043d\u0430\u0439\u0434\u0435\u043d\u043d\u043e\u043c\u0443 \u043a\u043e\u043c\u043c\u0438\u0442\u0443"
::msgcat::mcset ru "<Return>\u0009Move to next find hit" "<Return>\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043a \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0435\u043c\u0443 \u043d\u0430\u0439\u0434\u0435\u043d\u043d\u043e\u043c\u0443 \u043a\u043e\u043c\u043c\u0438\u0442\u0443"
::msgcat::mcset ru "g\u0009\u0009Go to commit" "g\u0009\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u043a\u043e\u043c\u043c\u0438\u0442"
::msgcat::mcset ru "/\u0009\u0009Focus the search box" "/\u0009\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043a \u043f\u043e\u043b\u044e \u043f\u043e\u0438\u0441\u043a\u0430"
::msgcat::mcset ru "?\u0009\u0009Move to previous find hit" "?\u0009\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043a \u043f\u0440\u0435\u0434\u044b\u0434\u0443\u0449\u0435\u043c\u0443 \u043d\u0430\u0439\u0434\u0435\u043d\u043d\u043e\u043c\u0443 \u043a\u043e\u043c\u043c\u0438\u0442\u0443"
::msgcat::mcset ru "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009\u041f\u0440\u043e\u043a\u0440\u0443\u0442\u0438\u0442\u044c \u0441\u043f\u0438\u0441\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043a \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0435\u043c\u0443 \u0444\u0430\u0439\u043b\u0443"
::msgcat::mcset ru "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009\u041f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u044c \u043f\u043e\u0438\u0441\u043a \u0432 \u0441\u043f\u0438\u0441\u043a\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043a \u043f\u0440\u0435\u0434\u044b\u0434\u0443\u0449\u0435\u043c\u0443 \u043d\u0430\u0439\u0434\u0435\u043d\u043d\u043e\u043c\u0443 \u0442\u0435\u043a\u0441\u0442\u0443 \u0432 \u0441\u043f\u0438\u0441\u043a\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009\u0423\u0432\u0435\u043b\u0438\u0447\u0438\u0442\u044c \u0440\u0430\u0437\u043c\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset ru "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009\u0423\u0432\u0435\u043b\u0438\u0447\u0438\u0442\u044c \u0440\u0430\u0437\u043c\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset ru "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009\u0423\u043c\u0435\u043d\u044c\u0448\u0438\u0442\u044c \u0440\u0430\u0437\u043c\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset ru "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009\u0423\u043c\u0435\u043d\u044c\u0448\u0438\u0442\u044c \u0440\u0430\u0437\u043c\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset ru "<F5>\u0009\u0009Update" "<F5>\u0009\u0009\u041e\u0431\u043d\u043e\u0432\u0438\u0442\u044c"
::msgcat::mcset ru "Error creating temporary directory %s:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f \u0432\u0440\u0435\u043c\u0435\u043d\u043d\u043e\u0433\u043e \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0430 %s:"
::msgcat::mcset ru "Error getting \"%s\" from %s:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u043f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u044f \u00ab%s\u00bb \u0438\u0437 %s:"
::msgcat::mcset ru "command failed:" "\u043e\u0448\u0438\u0431\u043a\u0430 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u044f \u043a\u043e\u043c\u0430\u043d\u0434\u044b:"
::msgcat::mcset ru "No such commit" "\u041a\u043e\u043c\u043c\u0438\u0442 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d"
::msgcat::mcset ru "git gui blame: command failed:" "git gui blame: \u043e\u0448\u0438\u0431\u043a\u0430 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u044f \u043a\u043e\u043c\u0430\u043d\u0434\u044b:"
::msgcat::mcset ru "Couldn't read merge head: %s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0447\u0442\u0435\u043d\u0438\u044f MERGE_HEAD: %s"
::msgcat::mcset ru "Error reading index: %s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0447\u0442\u0435\u043d\u0438\u044f \u0438\u043d\u0434\u0435\u043a\u0441\u0430: %s"
::msgcat::mcset ru "Couldn't start git blame: %s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0437\u0430\u043f\u0443\u0441\u043a\u0430 git blame: %s"
::msgcat::mcset ru "Searching" "\u041f\u043e\u0438\u0441\u043a"
::msgcat::mcset ru "Error running git blame: %s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u044f git blame: %s"
::msgcat::mcset ru "That line comes from commit %s,  which is not in this view" "\u042d\u0442\u0430 \u0441\u0442\u0440\u043e\u043a\u0430 \u043f\u0440\u0438\u043d\u0430\u0434\u043b\u0435\u0436\u0438\u0442 \u043a\u043e\u043c\u043c\u0438\u0442\u0443 %s, \u043a\u043e\u0442\u043e\u0440\u044b\u0439 \u043d\u0435 \u043f\u043e\u043a\u0430\u0437\u0430\u043d \u0432 \u044d\u0442\u043e\u043c \u043f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0438"
::msgcat::mcset ru "External diff viewer failed:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b \u0441\u0440\u0430\u0432\u043d\u0435\u043d\u0438\u044f:"
::msgcat::mcset ru "All files" "\u0412\u0441\u0435 \u0444\u0430\u0439\u043b\u044b"
::msgcat::mcset ru "View" "\u041f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0435"
::msgcat::mcset ru "Gitk view definition" "Gitk \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438\u0435 \u043f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "Remember this view" "\u0417\u0430\u043f\u043e\u043c\u043d\u0438\u0442\u044c \u043f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0435"
::msgcat::mcset ru "References (space separated list):" "\u0421\u0441\u044b\u043b\u043a\u0438 (\u0440\u0430\u0437\u0434\u0435\u043b\u0451\u043d\u043d\u044b\u0435 \u043f\u0440\u043e\u0431\u0435\u043b\u043e\u043c):"
::msgcat::mcset ru "Branches & tags:" "\u0412\u0435\u0442\u043a\u0438 \u0438 \u043c\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "All refs" "\u0412\u0441\u0435 \u0441\u0441\u044b\u043b\u043a\u0438"
::msgcat::mcset ru "All (local) branches" "\u0412\u0441\u0435 (\u043b\u043e\u043a\u0430\u043b\u044c\u043d\u044b\u0435) \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "All tags" "\u0412\u0441\u0435 \u043c\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "All remote-tracking branches" "\u0412\u0441\u0435 \u0432\u043d\u0435\u0448\u043d\u0438\u0435 \u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0435 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Commit Info (regular expressions):" "\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0435 (\u0440\u0435\u0433\u0443\u043b\u044f\u0440\u043d\u044b\u0435 \u0432\u044b\u0440\u0430\u0436\u0435\u043d\u0438\u044f):"
::msgcat::mcset ru "Author:" "\u0410\u0432\u0442\u043e\u0440:"
::msgcat::mcset ru "Committer:" "\u041a\u043e\u043c\u043c\u0438\u0442\u0435\u0440:"
::msgcat::mcset ru "Commit Message:" "\u0421\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u0430:"
::msgcat::mcset ru "Matches all Commit Info criteria" "\u0421\u043e\u0432\u043f\u0430\u0434\u0430\u0435\u0442 \u0441\u043e \u0432\u0441\u0435\u043c\u0438 \u0443\u0441\u043b\u043e\u0432\u0438\u044f\u043c\u0438 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u0438 \u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0435"
::msgcat::mcset ru "Matches no Commit Info criteria" "\u041d\u0435 \u0441\u043e\u0432\u043f\u0430\u0434\u0430\u0435\u0442 \u0441 \u0443\u0441\u043b\u043e\u0432\u0438\u044f\u043c\u0438 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u0438 \u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0435"
::msgcat::mcset ru "Changes to Files:" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0444\u0430\u0439\u043b\u043e\u0432:"
::msgcat::mcset ru "Fixed String" "\u041e\u0431\u044b\u0447\u043d\u0430\u044f \u0441\u0442\u0440\u043e\u043a\u0430"
::msgcat::mcset ru "Regular Expression" "\u0420\u0435\u0433\u0443\u043b\u044f\u0440\u043d\u043e\u0435 \u0432\u044b\u0440\u0430\u0436\u0435\u043d\u0438\u0435:"
::msgcat::mcset ru "Search string:" "\u0421\u0442\u0440\u043e\u043a\u0430 \u0434\u043b\u044f \u043f\u043e\u0438\u0441\u043a\u0430:"
::msgcat::mcset ru "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "\u0414\u0430\u0442\u044b \u043a\u043e\u043c\u043c\u0438\u0442\u0430 (\u00ab2 \u043d\u0435\u0434\u0435\u043b\u0438 \u043d\u0430\u0437\u0430\u0434\u00bb, \u00ab2009-03-17 15:27:38\u00bb, \u00ab17 \u043c\u0430\u0440\u0442\u0430 2009 15:27:38\u00bb):"
::msgcat::mcset ru "Since:" "\u0421 \u0434\u0430\u0442\u044b:"
::msgcat::mcset ru "Until:" "\u041f\u043e \u0434\u0430\u0442\u0443:"
::msgcat::mcset ru "Limit and/or skip a number of revisions (positive integer):" "\u041e\u0433\u0440\u0430\u043d\u0438\u0447\u0438\u0442\u044c \u0438/\u0438\u043b\u0438 \u043f\u0440\u043e\u043f\u0443\u0441\u0442\u0438\u0442\u044c \u043a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u0440\u0435\u0434\u0430\u043a\u0446\u0438\u0439 (\u043f\u043e\u043b\u043e\u0436\u0438\u0442\u0435\u043b\u044c\u043d\u043e\u0435 \u0447\u0438\u0441\u043b\u043e):"
::msgcat::mcset ru "Number to show:" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u043a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e:"
::msgcat::mcset ru "Number to skip:" "\u041f\u0440\u043e\u043f\u0443\u0441\u0442\u0438\u0442\u044c \u043a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e:"
::msgcat::mcset ru "Miscellaneous options:" "\u0420\u0430\u0437\u043b\u0438\u0447\u043d\u044b\u0435 \u043e\u043f\u0446\u0438\u0438:"
::msgcat::mcset ru "Strictly sort by date" "\u0421\u0442\u0440\u043e\u0433\u0430\u044f \u0441\u043e\u0440\u0442\u0438\u0440\u043e\u0432\u043a\u0430 \u043f\u043e \u0434\u0430\u0442\u0435"
::msgcat::mcset ru "Mark branch sides" "\u041e\u0442\u043c\u0435\u0442\u0438\u0442\u044c \u0441\u0442\u043e\u0440\u043e\u043d\u044b \u0432\u0435\u0442\u043e\u043a"
::msgcat::mcset ru "Limit to first parent" "\u041e\u0433\u0440\u0430\u043d\u0438\u0447\u0438\u0442\u044c \u043f\u0435\u0440\u0432\u044b\u043c \u043f\u0440\u0435\u0434\u043a\u043e\u043c"
::msgcat::mcset ru "Simple history" "\u0423\u043f\u0440\u043e\u0449\u0435\u043d\u043d\u0430\u044f \u0438\u0441\u0442\u043e\u0440\u0438\u044f"
::msgcat::mcset ru "Additional arguments to git log:" "\u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u044b\u0435 \u0430\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u044b \u0434\u043b\u044f git log:"
::msgcat::mcset ru "Enter files and directories to include, one per line:" "\u0424\u0430\u0439\u043b\u044b \u0438 \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0438 \u0434\u043b\u044f \u043e\u0433\u0440\u0430\u043d\u0438\u0447\u0435\u043d\u0438\u044f \u0438\u0441\u0442\u043e\u0440\u0438\u0438, \u043f\u043e \u043e\u0434\u043d\u043e\u043c\u0443 \u043d\u0430 \u0441\u0442\u0440\u043e\u043a\u0443:"
::msgcat::mcset ru "Command to generate more commits to include:" "\u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u0430\u044f \u043a\u043e\u043c\u0430\u043d\u0434\u0430 \u0434\u043b\u044f \u0441\u043f\u0438\u0441\u043a\u0430 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432:"
::msgcat::mcset ru "Gitk: edit view" "Gitk: \u0438\u0437\u043c\u0435\u043d\u0438\u0442\u044c \u043f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0435"
::msgcat::mcset ru "-- criteria for selecting revisions" "\u2014 \u043a\u0440\u0438\u0442\u0435\u0440\u0438\u0439 \u043f\u043e\u0438\u0441\u043a\u0430 \u0440\u0435\u0434\u0430\u043a\u0446\u0438\u0439"
::msgcat::mcset ru "View Name" "\u0418\u043c\u044f \u043f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Apply (F5)" "\u041f\u0440\u0438\u043c\u0435\u043d\u0438\u0442\u044c (F5)"
::msgcat::mcset ru "Error in commit selection arguments:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0432 \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u0430\u0445 \u0432\u044b\u0431\u043e\u0440\u0430 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432:"
::msgcat::mcset ru "None" "\u041d\u0438 \u043e\u0434\u043d\u043e\u0433\u043e"
::msgcat::mcset ru "Descendant" "\u041f\u043e\u0440\u043e\u0436\u0434\u0451\u043d\u043d\u043e\u0435"
::msgcat::mcset ru "Not descendant" "\u041d\u0435 \u043f\u043e\u0440\u043e\u0436\u0434\u0451\u043d\u043d\u043e\u0435"
::msgcat::mcset ru "Ancestor" "\u041f\u0440\u0435\u0434\u043e\u043a"
::msgcat::mcset ru "Not ancestor" "\u041d\u0435 \u043f\u0440\u0435\u0434\u043e\u043a"
::msgcat::mcset ru "Local changes checked in to index but not committed" "\u041f\u0440\u043e\u0438\u043d\u0434\u0435\u043a\u0441\u0438\u0440\u043e\u0432\u0430\u043d\u043d\u044b\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Local uncommitted changes, not checked in to index" "\u041d\u0435\u043f\u0440\u043e\u0438\u043d\u0434\u0435\u043a\u0441\u0438\u0440\u043e\u0432\u0430\u043d\u043d\u044b\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "and many more" "\u0438 \u043c\u043d\u043e\u0433\u043e\u0435 \u0434\u0440\u0443\u0433\u043e\u0435"
::msgcat::mcset ru "many" "\u043c\u043d\u043e\u0433\u043e"
::msgcat::mcset ru "Tags:" "\u041c\u0435\u0442\u043a\u0438:"
::msgcat::mcset ru "Parent" "\u041f\u0440\u0435\u0434\u043e\u043a"
::msgcat::mcset ru "Child" "\u041f\u043e\u0442\u043e\u043c\u043e\u043a"
::msgcat::mcset ru "Branch" "\u0412\u0435\u0442\u043a\u0430"
::msgcat::mcset ru "Follows" "\u0421\u043b\u0435\u0434\u0443\u0435\u0442 \u0437\u0430"
::msgcat::mcset ru "Precedes" "\u041f\u0440\u0435\u0434\u0448\u0435\u0441\u0442\u0432\u0443\u0435\u0442"
::msgcat::mcset ru "Error getting diffs: %s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u043f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u044f \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439: %s"
::msgcat::mcset ru "Goto:" "\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043a:"
::msgcat::mcset ru "Short SHA1 id %s is ambiguous" "\u0421\u043e\u043a\u0440\u0430\u0449\u0451\u043d\u043d\u044b\u0439 SHA1 \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 %s \u043d\u0435\u043e\u0434\u043d\u043e\u0437\u043d\u0430\u0447\u0435\u043d"
::msgcat::mcset ru "Revision %s is not known" "\u0420\u0435\u0434\u0430\u043a\u0446\u0438\u044f %s \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d\u0430"
::msgcat::mcset ru "SHA1 id %s is not known" "SHA1 \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 %s \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d"
::msgcat::mcset ru "Revision %s is not in the current view" "\u0420\u0435\u0434\u0430\u043a\u0446\u0438\u044f %s \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d\u0430 \u0432 \u0442\u0435\u043a\u0443\u0449\u0435\u043c \u043f\u0440\u0435\u0434\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0438"
::msgcat::mcset ru "Date" "\u0414\u0430\u0442\u0430"
::msgcat::mcset ru "Children" "\u041f\u043e\u0442\u043e\u043c\u043a\u0438"
::msgcat::mcset ru "Reset %s branch to here" "\u0421\u0431\u0440\u043e\u0441\u0438\u0442\u044c \u0432\u0435\u0442\u043a\u0443 %s \u043d\u0430 \u044d\u0442\u043e\u0442 \u043a\u043e\u043c\u043c\u0438\u0442"
::msgcat::mcset ru "Detached head: can't reset" "\u041a\u043e\u043c\u043c\u0438\u0442 \u043d\u0435 \u043f\u0440\u0438\u043d\u0430\u0434\u043b\u0435\u0436\u0438\u0442 \u043d\u0438 \u043e\u0434\u043d\u043e\u0439 \u0432\u0435\u0442\u043a\u0435, \u0441\u0431\u0440\u043e\u0441\u0438\u0442\u044c \u043d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e"
::msgcat::mcset ru "Skipping merge commit " "\u041f\u0440\u043e\u043f\u0443\u0441\u043a\u0430\u044e \u043a\u043e\u043c\u043c\u0438\u0442-\u0441\u043b\u0438\u044f\u043d\u0438\u0435"
::msgcat::mcset ru "Error getting patch ID for " "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043f\u043e\u043b\u0443\u0447\u0438\u0442\u044c \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 \u043f\u0430\u0442\u0447\u0430 \u0434\u043b\u044f "
::msgcat::mcset ru " - stopping\n" " \u2014 \u043e\u0441\u0442\u0430\u043d\u043e\u0432\n"
::msgcat::mcset ru "Commit " "\u041a\u043e\u043c\u043c\u0438\u0442"
::msgcat::mcset ru " is the same patch as\n       " " \u0442\u0430\u043a\u043e\u0439 \u0436\u0435 \u043f\u0430\u0442\u0447, \u043a\u0430\u043a \u0438\n       "
::msgcat::mcset ru " differs from\n       " " \u043e\u0442\u043b\u0438\u0447\u0430\u0435\u0442\u0441\u044f \u043e\u0442\n       "
::msgcat::mcset ru "Diff of commits:\n\n" "\u0420\u0430\u0437\u043b\u0438\u0447\u0438\u044f \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432:\n\n"
::msgcat::mcset ru " has %s children - stopping\n" " \u044f\u0432\u043b\u044f\u0435\u0442\u0441\u044f %s \u043f\u043e\u0442\u043e\u043c\u043a\u043e\u043c \u2014 \u043e\u0441\u0442\u0430\u043d\u043e\u0432\n"
::msgcat::mcset ru "Error writing commit to file: %s" "\u041f\u0440\u043e\u0438\u0437\u043e\u0448\u043b\u0430 \u043e\u0448\u0438\u0431\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u043f\u0438\u0441\u0438 \u043a\u043e\u043c\u043c\u0438\u0442\u0430 \u0432 \u0444\u0430\u0439\u043b: %s"
::msgcat::mcset ru "Error diffing commits: %s" "\u041f\u0440\u043e\u0438\u0437\u043e\u0448\u043b\u0430 \u043e\u0448\u0438\u0431\u043a\u0430 \u043f\u0440\u0438 \u0432\u044b\u0432\u043e\u0434\u0435 \u0440\u0430\u0437\u043b\u0438\u0447\u0438\u0439 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432: %s"
::msgcat::mcset ru "Top" "\u0412\u0435\u0440\u0445"
::msgcat::mcset ru "From" "\u041e\u0442"
::msgcat::mcset ru "To" "\u0414\u043e"
::msgcat::mcset ru "Generate patch" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c \u043f\u0430\u0442\u0447"
::msgcat::mcset ru "From:" "\u041e\u0442:"
::msgcat::mcset ru "To:" "\u0414\u043e:"
::msgcat::mcset ru "Reverse" "\u0412 \u043e\u0431\u0440\u0430\u0442\u043d\u043e\u043c \u043f\u043e\u0440\u044f\u0434\u043a\u0435"
::msgcat::mcset ru "Output file:" "\u0424\u0430\u0439\u043b \u0434\u043b\u044f \u0441\u043e\u0445\u0440\u0430\u043d\u0435\u043d\u0438\u044f:"
::msgcat::mcset ru "Generate" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c"
::msgcat::mcset ru "Error creating patch:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f \u043f\u0430\u0442\u0447\u0430:"
::msgcat::mcset ru "ID:" "ID:"
::msgcat::mcset ru "Tag name:" "\u0418\u043c\u044f \u043c\u0435\u0442\u043a\u0438:"
::msgcat::mcset ru "Tag message is optional" "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u043c\u0435\u0442\u043a\u0438 \u0443\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u043d\u0435 \u043e\u0431\u044f\u0437\u0430\u0442\u0435\u043b\u044c\u043d\u043e"
::msgcat::mcset ru "Tag message:" "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u043c\u0435\u0442\u043a\u0438:"
::msgcat::mcset ru "Create" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c"
::msgcat::mcset ru "No tag name specified" "\u041d\u0435 \u0437\u0430\u0434\u0430\u043d\u043e \u0438\u043c\u044f \u043c\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Tag \"%s\" already exists" "\u041c\u0435\u0442\u043a\u0430 \u00ab%s\u00bb \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442"
::msgcat::mcset ru "Error creating tag:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f \u043c\u0435\u0442\u043a\u0438:"
::msgcat::mcset ru "Command:" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430:"
::msgcat::mcset ru "Write" "\u0417\u0430\u043f\u0438\u0441\u044c"
::msgcat::mcset ru "Error writing commit:" "\u041f\u0440\u043e\u0438\u0437\u043e\u0448\u043b\u0430 \u043e\u0448\u0438\u0431\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u043f\u0438\u0441\u0438 \u043a\u043e\u043c\u043c\u0438\u0442\u0430:"
::msgcat::mcset ru "Create branch" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Rename branch %s" "\u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u0442\u044c \u0432\u0435\u0442\u043a\u0443 %s"
::msgcat::mcset ru "Rename" "\u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u0442\u044c"
::msgcat::mcset ru "Name:" "\u0418\u043c\u044f:"
::msgcat::mcset ru "Please specify a name for the new branch" "\u0423\u043a\u0430\u0436\u0438\u0442\u0435 \u0438\u043c\u044f \u0434\u043b\u044f \u043d\u043e\u0432\u043e\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Branch '%s' already exists. Overwrite?" "\u0412\u0435\u0442\u043a\u0430 \u00ab%s\u00bb \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442. \u041f\u0435\u0440\u0435\u043f\u0438\u0441\u0430\u0442\u044c?"
::msgcat::mcset ru "Please specify a new name for the branch" "\u0423\u043a\u0430\u0436\u0438\u0442\u0435 \u0438\u043c\u044f \u0434\u043b\u044f \u043d\u043e\u0432\u043e\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Commit %s is already included in branch %s -- really re-apply it?" "\u041a\u043e\u043c\u043c\u0438\u0442 %s \u0443\u0436\u0435 \u0432\u043a\u043b\u044e\u0447\u0451\u043d \u0432 \u0432\u0435\u0442\u043a\u0443 %s. \u041f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044e?"
::msgcat::mcset ru "Cherry-picking" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u0430"
::msgcat::mcset ru "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u0430 \u043d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0438\u0437-\u0437\u0430 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0432 \u0444\u0430\u0439\u043b\u0435 \u00ab%s\u00bb.\n\u0417\u0430\u043a\u043e\u043c\u043c\u0438\u0442\u044c\u0442\u0435, \u0441\u0431\u0440\u043e\u0441\u044c\u0442\u0435 \u0438\u043b\u0438 \u0441\u043f\u0440\u044f\u0447\u044c\u0442\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0438 \u043f\u043e\u0432\u0442\u043e\u0440\u0438\u0442\u0435 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044e."
::msgcat::mcset ru "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0438\u0437-\u0437\u0430 \u043d\u0435\u0437\u0430\u0432\u0435\u0440\u0448\u0451\u043d\u043d\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u044f.\n\u0417\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c git citool \u0434\u043b\u044f \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u0438\u044f \u044d\u0442\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438?"
::msgcat::mcset ru "No changes committed" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u043d\u0435 \u0437\u0430\u043a\u043e\u043c\u043c\u0438\u0447\u0435\u043d\u044b"
::msgcat::mcset ru "Commit %s is not included in branch %s -- really revert it?" "\u041a\u043e\u043c\u043c\u0438\u0442 %s \u043d\u0435 \u0432\u043a\u043b\u044e\u0447\u0451\u043d \u0432 \u0432\u0435\u0442\u043a\u0443 %s. \u041f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044e?"
::msgcat::mcset ru "Reverting" "\u041e\u0431\u0440\u0430\u0449\u0435\u043d\u0438\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "\u0412\u043e\u0437\u0432\u0440\u0430\u0442 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043a\u043e\u043c\u043c\u0438\u0442\u0430 \u043d\u0435 \u0443\u0434\u0430\u043b\u0441\u044f \u0438\u0437-\u0437\u0430 \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u044b\u0445 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0432 \u0443\u043a\u0430\u0437\u0430\u043d\u043d\u044b\u0445 \u0444\u0430\u0439\u043b\u0430\u0445: %s\n\u0417\u0430\u043a\u043e\u043c\u043c\u0438\u0442\u044c\u0442\u0435, \u0441\u0431\u0440\u043e\u0441\u044c\u0442\u0435 \u0438\u043b\u0438 \u0441\u043f\u0440\u044f\u0447\u044c\u0442\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0438 \u043f\u043e\u0432\u0442\u043e\u0440\u0438\u0442\u0435 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044e."
::msgcat::mcset ru "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "\u0412\u043e\u0437\u0432\u0440\u0430\u0442 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u0435\u043d \u0438\u0437-\u0437\u0430 \u043d\u0435\u0437\u0430\u0432\u0435\u0440\u0448\u0451\u043d\u043d\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u044f.\n\u0417\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c git citool \u0434\u043b\u044f \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u0438\u044f \u044d\u0442\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438?"
::msgcat::mcset ru "Confirm reset" "\u041f\u043e\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u0435 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044e \u043f\u0435\u0440\u0435\u0445\u043e\u0434\u0430"
::msgcat::mcset ru "Reset branch %s to %s?" "\u0421\u0431\u0440\u043e\u0441\u0438\u0442\u044c \u0432\u0435\u0442\u043a\u0443 %s \u043d\u0430 \u043a\u043e\u043c\u043c\u0438\u0442 %s?"
::msgcat::mcset ru "Reset type:" "\u0422\u0438\u043f \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u043f\u0435\u0440\u0435\u0445\u043e\u0434\u0430:"
::msgcat::mcset ru "Soft: Leave working tree and index untouched" "\u041b\u0451\u0433\u043a\u0438\u0439: \u043e\u0441\u0442\u0430\u0432\u0438\u0442\u044c \u0440\u0430\u0431\u043e\u0447\u0438\u0439 \u043a\u0430\u0442\u0430\u043b\u043e\u0433 \u0438 \u0438\u043d\u0434\u0435\u043a\u0441 \u043d\u0435\u0438\u0437\u043c\u0435\u043d\u043d\u044b\u043c\u0438"
::msgcat::mcset ru "Mixed: Leave working tree untouched, reset index" "\u0421\u043c\u0435\u0448\u0430\u043d\u043d\u044b\u0439: \u043e\u0441\u0442\u0430\u0432\u0438\u0442\u044c \u0440\u0430\u0431\u043e\u0447\u0438\u0439 \u043a\u0430\u0442\u0430\u043b\u043e\u0433 \u043d\u0435\u0438\u0437\u043c\u0435\u043d\u043d\u044b\u043c, \u0443\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u044c \u0438\u043d\u0434\u0435\u043a\u0441"
::msgcat::mcset ru "Hard: Reset working tree and index\n(discard ALL local changes)" "\u0416\u0435\u0441\u0442\u043a\u0438\u0439: \u043f\u0435\u0440\u0435\u043f\u0438\u0441\u0430\u0442\u044c \u0438\u043d\u0434\u0435\u043a\u0441 \u0438 \u0440\u0430\u0431\u043e\u0447\u0438\u0439 \u043a\u0430\u0442\u0430\u043b\u043e\u0433\n(\u0432\u0441\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432 \u0440\u0430\u0431\u043e\u0447\u0435\u043c \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0435 \u0431\u0443\u0434\u0443\u0442 \u043f\u043e\u0442\u0435\u0440\u044f\u043d\u044b)"
::msgcat::mcset ru "Resetting" "\u0421\u0431\u0440\u043e\u0441"
::msgcat::mcset ru "A local branch named %s exists already" "\u041b\u043e\u043a\u0430\u043b\u044c\u043d\u0430\u044f \u0432\u0435\u0442\u043a\u0430 \u0441 \u0438\u043c\u0435\u043d\u0435\u043c %s \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442"
::msgcat::mcset ru "Checking out" "\u041f\u0435\u0440\u0435\u0445\u043e\u0434"
::msgcat::mcset ru "Cannot delete the currently checked-out branch" "\u0410\u043a\u0442\u0438\u0432\u043d\u0430\u044f \u0432\u0435\u0442\u043a\u0430 \u043d\u0435 \u043c\u043e\u0436\u0435\u0442 \u0431\u044b\u0442\u044c \u0443\u0434\u0430\u043b\u0435\u043d\u0430"
::msgcat::mcset ru "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "\u041a\u043e\u043c\u043c\u0438\u0442\u044b \u0438\u0437 \u0432\u0435\u0442\u043a\u0438 %s \u043d\u0435 \u043f\u0440\u0438\u043d\u0430\u0434\u043b\u0435\u0436\u0430\u0442 \u0431\u043e\u043b\u044c\u0448\u0435 \u043d\u0438\u043a\u0430\u043a\u043e\u0439 \u0434\u0440\u0443\u0433\u043e\u0439 \u0432\u0435\u0442\u043a\u0435.\n\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u0442\u0435\u043b\u044c\u043d\u043e \u0443\u0434\u0430\u043b\u0438\u0442\u044c \u0432\u0435\u0442\u043a\u0443 %s?"
::msgcat::mcset ru "Tags and heads: %s" "\u041c\u0435\u0442\u043a\u0438 \u0438 \u0432\u0435\u0442\u043a\u0438: %s"
::msgcat::mcset ru "Filter" "\u0424\u0438\u043b\u044c\u0442\u0440\u043e\u0432\u0430\u0442\u044c"
::msgcat::mcset ru "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "\u041e\u0448\u0438\u0431\u043a\u0430 \u0447\u0442\u0435\u043d\u0438\u044f \u0438\u0441\u0442\u043e\u0440\u0438\u0438 \u043f\u0440\u043e\u0435\u043a\u0442\u0430; \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u0432\u0435\u0442\u043a\u0430\u0445 \u0438 \u043a\u043e\u043c\u043c\u0438\u0442\u0430\u0445 \u0432\u043e\u043a\u0440\u0443\u0433 \u043c\u0435\u0442\u043e\u043a (\u0434\u043e/\u043f\u043e\u0441\u043b\u0435) \u043c\u043e\u0436\u0435\u0442 \u0431\u044b\u0442\u044c \u043d\u0435\u043f\u043e\u043b\u043d\u043e\u0439."
::msgcat::mcset ru "Tag" "\u041c\u0435\u0442\u043a\u0430"
::msgcat::mcset ru "Id" "Id"
::msgcat::mcset ru "Gitk font chooser" "\u0428\u0440\u0438\u0444\u0442 Gitk"
::msgcat::mcset ru "B" "\u0416"
::msgcat::mcset ru "I" "\u041a"
::msgcat::mcset ru "Commit list display options" "\u041f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u044b \u043f\u043e\u043a\u0430\u0437\u0430 \u0441\u043f\u0438\u0441\u043a\u0430 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432"
::msgcat::mcset ru "Maximum graph width (lines)" "\u041c\u0430\u043a\u0441. \u0448\u0438\u0440\u0438\u043d\u0430 \u0433\u0440\u0430\u0444\u0430 (\u0441\u0442\u0440\u043e\u043a)"
::msgcat::mcset ru "Maximum graph width (% of pane)" "\u041c\u0430\u043a\u0441. \u0448\u0438\u0440\u0438\u043d\u0430 \u0433\u0440\u0430\u0444\u0430 (% \u0448\u0438\u0440\u0438\u043d\u044b \u043f\u0430\u043d\u0435\u043b\u0438)"
::msgcat::mcset ru "Show local changes" "\u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432 \u0440\u0430\u0431\u043e\u0447\u0435\u043c \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0435"
::msgcat::mcset ru "Auto-select SHA1 (length)" "\u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438 \u0432\u044b\u0434\u0435\u043b\u0438\u0442\u044c SHA1 (\u0434\u043b\u0438\u043d\u043d\u0430)"
::msgcat::mcset ru "Hide remote refs" "\u0421\u043a\u0440\u044b\u0442\u044c \u0432\u043d\u0435\u0448\u043d\u0438\u0435 \u0441\u0441\u044b\u043b\u043a\u0438"
::msgcat::mcset ru "Diff display options" "\u041f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u044b \u043f\u043e\u043a\u0430\u0437\u0430 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "Tab spacing" "\u0428\u0438\u0440\u0438\u043d\u0430 \u0442\u0430\u0431\u0443\u043b\u044f\u0446\u0438\u0438"
::msgcat::mcset ru "Display nearby tags/heads" "\u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0431\u043b\u0438\u0437\u043a\u0438\u0435 \u043c\u0435\u0442\u043a\u0438/\u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Maximum # tags/heads to show" "\u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u043c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u044c\u043d\u043e\u0435 \u043a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u043c\u0435\u0442\u043e\u043a/\u0432\u0435\u0442\u043e\u043a"
::msgcat::mcset ru "Limit diffs to listed paths" "\u041e\u0433\u0440\u0430\u043d\u0438\u0447\u0438\u0442\u044c \u043f\u043e\u043a\u0430\u0437 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u044b\u043c\u0438 \u0444\u0430\u0439\u043b\u0430\u043c\u0438"
::msgcat::mcset ru "Support per-file encodings" "\u041f\u043e\u0434\u0434\u0435\u0440\u0436\u043a\u0430 \u043a\u043e\u0434\u0438\u0440\u043e\u0432\u043e\u043a \u0432 \u043e\u0442\u0434\u0435\u043b\u044c\u043d\u044b\u0445 \u0444\u0430\u0439\u043b\u0430\u0445"
::msgcat::mcset ru "External diff tool" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u0434\u043b\u044f \u043f\u043e\u043a\u0430\u0437\u0430 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "Choose..." "\u0412\u044b\u0431\u0435\u0440\u0438\u0442\u0435..."
::msgcat::mcset ru "General options" "\u041e\u0431\u0449\u0438\u0435 \u043e\u043f\u0446\u0438\u0438"
::msgcat::mcset ru "Use themed widgets" "\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u0441\u0442\u0438\u043b\u0438 \u0432\u0438\u0434\u0436\u0435\u0442\u043e\u0432"
::msgcat::mcset ru "(change requires restart)" "(\u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435 \u043f\u043e\u0442\u0440\u0435\u0431\u0443\u0435\u0442 \u043f\u0435\u0440\u0435\u0437\u0430\u043f\u0443\u0441\u043a)"
::msgcat::mcset ru "(currently unavailable)" "(\u043d\u0435\u0434\u043e\u0441\u0442\u0443\u043f\u043d\u043e \u0432 \u0434\u0430\u043d\u043d\u044b\u0439 \u043c\u043e\u043c\u0435\u043d\u0442)"
::msgcat::mcset ru "Colors: press to choose" "\u0426\u0432\u0435\u0442\u0430: \u043d\u0430\u0436\u043c\u0438\u0442\u0435 \u0434\u043b\u044f \u0432\u044b\u0431\u043e\u0440\u0430"
::msgcat::mcset ru "Interface" "\u0418\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441"
::msgcat::mcset ru "interface" "\u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441"
::msgcat::mcset ru "Background" "\u0424\u043e\u043d"
::msgcat::mcset ru "background" "\u0444\u043e\u043d"
::msgcat::mcset ru "Foreground" "\u041f\u0435\u0440\u0435\u0434\u043d\u0438\u0439 \u043f\u043b\u0430\u043d"
::msgcat::mcset ru "foreground" "\u043f\u0435\u0440\u0435\u0434\u043d\u0438\u0439 \u043f\u043b\u0430\u043d"
::msgcat::mcset ru "Diff: old lines" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f: \u0441\u0442\u0430\u0440\u044b\u0439 \u0442\u0435\u043a\u0441\u0442"
::msgcat::mcset ru "diff old lines" "\u0441\u0442\u0430\u0440\u044b\u0439 \u0442\u0435\u043a\u0441\u0442 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Diff: new lines" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f: \u043d\u043e\u0432\u044b\u0439 \u0442\u0435\u043a\u0441\u0442"
::msgcat::mcset ru "diff new lines" "\u043d\u043e\u0432\u044b\u0439 \u0442\u0435\u043a\u0441\u0442 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Diff: hunk header" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f: \u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a \u0431\u043b\u043e\u043a\u0430"
::msgcat::mcset ru "diff hunk header" "\u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a \u0431\u043b\u043e\u043a\u0430 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "Marked line bg" "\u0424\u043e\u043d \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u043e\u0439 \u0441\u0442\u0440\u043e\u043a\u0438"
::msgcat::mcset ru "marked line background" "\u0444\u043e\u043d \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u043e\u0439 \u0441\u0442\u0440\u043e\u043a\u0438"
::msgcat::mcset ru "Select bg" "\u0412\u044b\u0431\u0435\u0440\u0438\u0442\u0435 \u0444\u043e\u043d"
::msgcat::mcset ru "Fonts: press to choose" "\u0428\u0440\u0438\u0444\u0442: \u043d\u0430\u0436\u043c\u0438\u0442\u0435 \u0434\u043b\u044f \u0432\u044b\u0431\u043e\u0440\u0430"
::msgcat::mcset ru "Main font" "\u041e\u0441\u043d\u043e\u0432\u043d\u043e\u0439 \u0448\u0440\u0438\u0444\u0442"
::msgcat::mcset ru "Diff display font" "\u0428\u0440\u0438\u0444\u0442 \u043f\u043e\u043a\u0430\u0437\u0430 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "User interface font" "\u0428\u0440\u0438\u0444\u0442 \u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441\u0430"
::msgcat::mcset ru "Gitk preferences" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 Gitk"
::msgcat::mcset ru "General" "\u041e\u0431\u0449\u0438\u0435"
::msgcat::mcset ru "Colors" "\u0426\u0432\u0435\u0442\u0430"
::msgcat::mcset ru "Fonts" "\u0428\u0440\u0438\u0444\u0442\u044b"
::msgcat::mcset ru "Gitk: choose color for %s" "Gitk: \u0432\u044b\u0431\u0435\u0440\u0438\u0442\u0435 \u0446\u0432\u0435\u0442 \u0434\u043b\u044f %s"
::msgcat::mcset ru "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "\u041a \u0441\u043e\u0436\u0430\u043b\u0435\u043d\u0438\u044e gitk \u043d\u0435 \u043c\u043e\u0436\u0435\u0442 \u0440\u0430\u0431\u043e\u0442\u0430\u0442\u044c \u0441 \u044d\u0442\u043e\u0439 \u0432\u0435\u0440\u0441\u0438\u0439 Tcl/Tk.\n\u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044f \u043a\u0430\u043a \u043c\u0438\u043d\u0438\u043c\u0443\u043c Tcl/Tk 8.4."
::msgcat::mcset ru "Cannot find a git repository here." "Git-\u0440\u0435\u043f\u043e\u0437\u0438\u0442\u0430\u0440\u0438\u0439 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d \u0432 \u0442\u0435\u043a\u0443\u0449\u0435\u043c \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0435."
::msgcat::mcset ru "Ambiguous argument '%s': both revision and filename" "\u041d\u0435\u043e\u0434\u043d\u043e\u0437\u043d\u0430\u0447\u043d\u044b\u0439 \u0430\u0440\u0433\u0443\u043c\u0435\u043d\u0442 \u00ab%s\u00bb: \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442 \u043a\u0430\u043a \u0440\u0435\u0434\u0430\u043a\u0446\u0438\u044f \u0438 \u043a\u0430\u043a \u0438\u043c\u044f \u0444\u0430\u0439\u043b\u0430"
::msgcat::mcset ru "Bad arguments to gitk:" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u044c\u043d\u044b\u0435 \u0430\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u044b \u0434\u043b\u044f gitk:"
