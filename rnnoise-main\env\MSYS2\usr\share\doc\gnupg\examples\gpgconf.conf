# gpgconf.conf - configuration for gpgconf
#----------------------------------------------------------------------
#
# === The use of this feature is deprecated ===
# == Please use the more powerful global options. ==
#
# This file is read by gpgconf(1) to setup defaults for all or
# specified users and groups.  It may be used to change the hardwired
# defaults in gpgconf and to enforce certain values for the various
# GnuPG related configuration files.
#
# NOTE: This is a legacy mechanism.  The modern way is to use global
#       configuration files like /etc/gnupg/gpg.conf which are more
#       flexible and better integrated into the configuration system.
#
# Empty lines and comment lines, indicated by a hash mark as first non
# white space character, are ignored.  The line is separated by white
# space into fields. The first field is used to match the user or
# group and must start at the first column, the file is processed
# sequential until a matching rule is found.  A rule may contain
# several lines; continuation lines are indicated by a indenting them.
#
# Syntax of a line:
# <key>|WS  <component> <option> ["["<flag>"]"] [<value>]
#
# Examples for the <key> field:
#   foo         - Matches the user "foo".
#   foo:        - Matches the user "foo".
#   foo:staff   - Matches the user "foo" or the group "staff".
#   :staff      - Matches the group "staff".
#   *           - Matches any user.
# All other variants are not defined and reserved for future use.
#
# <component> and <option> are as specified by gpgconf.
# <flag> may be one of:
#   default     - Delete the option so that the default is used.
#   no-change   - Mark the field as non changeable by gpgconf.
#   change      - Mark the field as changeable by gpgconf.
#
# Example file:
#==========
# :staff  gpg-agent min-passphrase-len 6 [change]
#
# *       gpg-agent min-passphrase-len [no-change] 8
#         gpg-agent min-passphrase-nonalpha [no-change] 1
#         gpg-agent max-passphrase-days [no-change] 700
#         gpg-agent enable-passphrase-history [no-change]
#         gpg-agent enforce-passphrase-constraints [default]
#         gpg-agent enforce-passphrase-constraints [no-change]
#         gpg-agent max-cache-ttl [no-change] 10800
#         gpg-agent max-cache-ttl-ssh [no-change] 10800
#         gpgsm     enable-ocsp
#         gpg       compliance [no-change]
#         gpgsm     compliance [no-change]
#===========
# All users in the group "staff" are allowed to change the value for
# --allow-mark-trusted; gpgconf's default is not to allow a change
# through its interface.  When "gpgconf --apply-defaults" is used,
# "allow-mark-trusted" will get enabled and "min-passphrase-len" set
# to 6.  All other users are not allowed to change
# "min-passphrase-len" and "allow-mark-trusted".  When "gpgconf
# --apply-defaults" is used for them, "min-passphrase-len" is set to
# 8, "allow-mark-trusted" deleted from the config file and
# "enable-ocsp" is put into the config file of gpgsm.  The latter may
# be changed by any user.
#-------------------------------------------------------------------
