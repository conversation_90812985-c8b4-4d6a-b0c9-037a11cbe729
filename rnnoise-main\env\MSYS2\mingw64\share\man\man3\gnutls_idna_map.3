.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_idna_map" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_idna_map \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_idna_map(const char * " input ", unsigned " ilen ", gnutls_datum_t * " out ", unsigned " flags ");"
.SH ARGUMENTS
.IP "const char * input" 12
contain the UTF\-8 formatted domain name
.IP "unsigned ilen" 12
the length of the provided string
.IP "gnutls_datum_t * out" 12
the result in an null\-terminated allocated string
.IP "unsigned flags" 12
should be zero
.SH "DESCRIPTION"
This function will convert the provided UTF\-8 domain name, to
its IDNA mapping in an allocated variable. Note that depending on the flags the used gnutls
library was compiled with, the output of this function may vary (i.e.,
may be IDNA2008, or IDNA2003).

To force IDNA2008 specify the flag \fBGNUTLS_IDNA_FORCE_2008\fP. In
the case GnuTLS is not compiled with the necessary dependencies,
\fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP will be returned to indicate that
gnutls is unable to perform the requested conversion.

Note also, that this function will return an empty string if an
empty string is provided as input.
.SH "RETURNS"
\fBGNUTLS_E_INVALID_UTF8_STRING\fP on invalid UTF\-8 data, or 0 on success.
.SH "SINCE"
3.5.8
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
