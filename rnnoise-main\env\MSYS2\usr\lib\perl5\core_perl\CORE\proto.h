/* -*- mode: C; buffer-read-only: t -*-
 *
 *    proto.h
 *
 *    Copyright (C) 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001,
 *    2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013,
 *    2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022
 *    by <PERSON> and others
 *
 *    You may distribute under the terms of either the GNU General Public
 *    License or the Artistic License, as specified in the README file.
 *
 * !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
 * This file is built by regen/embed.pl from embed.fnc, intrpvar.h,
 * perlvars.h, regen/opcodes, regen/embed.pl, regen/embed_lib.pl and
 * regen/HeaderParser.pm.
 * Any changes made here will be lost!
 *
 * Edit those files and run 'make regen_headers' to effect changes.
 */

START_EXTERN_C
PERL_CALLCONV int
Perl_Gv_AMupdate(pTHX_ HV *stash, bool destructing);
#define PERL_ARGS_ASSERT_GV_AMUPDATE            \
        assert(stash)

PERL_CALLCONV const char *
Perl_PerlIO_context_layers(pTHX_ const char *mode);
#define PERL_ARGS_ASSERT_PERLIO_CONTEXT_LAYERS

PERL_CALLCONV int
Perl_PerlLIO_dup2_cloexec(pTHX_ int oldfd, int newfd)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PERLLIO_DUP2_CLOEXEC

PERL_CALLCONV int
Perl_PerlLIO_dup_cloexec(pTHX_ int oldfd)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PERLLIO_DUP_CLOEXEC

PERL_CALLCONV int
Perl_PerlLIO_open3_cloexec(pTHX_ const char *file, int flag, int perm)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PERLLIO_OPEN3_CLOEXEC  \
        assert(file)

PERL_CALLCONV int
Perl_PerlLIO_open_cloexec(pTHX_ const char *file, int flag)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PERLLIO_OPEN_CLOEXEC   \
        assert(file)

/* PERL_CALLCONV const XOP *
Perl_custom_op_xop(pTHX_ const OP *o); */

PERL_CALLCONV HV *
Perl_localeconv(pTHX);
#define PERL_ARGS_ASSERT_PERL_LOCALECONV

PERL_CALLCONV const char *
Perl_setlocale(const int category, const char *locale);
#define PERL_ARGS_ASSERT_PERL_SETLOCALE

PERL_CALLCONV void *
Perl_Slab_Alloc(pTHX_ size_t sz)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SLAB_ALLOC

PERL_CALLCONV void
Perl_Slab_Free(pTHX_ void *op);
#define PERL_ARGS_ASSERT_SLAB_FREE              \
        assert(op)

/* PERL_CALLCONV void
SvREFCNT_dec_set_NULL(pTHX_ SV *sv); */

PERL_CALLCONV char *
Perl__byte_dump_string(pTHX_ const U8 * const start, const STRLEN len, const bool format);
#define PERL_ARGS_ASSERT__BYTE_DUMP_STRING      \
        assert(start)

PERL_CALLCONV void
Perl__force_out_malformed_utf8_message(pTHX_ const U8 * const p, const U8 * const e, const U32 flags, const bool die_here);
#define PERL_ARGS_ASSERT__FORCE_OUT_MALFORMED_UTF8_MESSAGE \
        assert(p); assert(e)

PERL_CALLCONV Size_t
Perl__inverse_folds(pTHX_ const UV cp, U32 *first_folds_to, const U32 **remaining_folds_to)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT__INVERSE_FOLDS         \
        assert(first_folds_to); assert(remaining_folds_to)

PERL_CALLCONV bool
Perl__is_in_locale_category(pTHX_ const bool compiling, const int category);
#define PERL_ARGS_ASSERT__IS_IN_LOCALE_CATEGORY

PERL_CALLCONV bool
Perl__is_uni_FOO(pTHX_ const U8 classnum, const UV c)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT__IS_UNI_FOO

PERL_CALLCONV bool
Perl__is_uni_perl_idcont(pTHX_ UV c)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT__IS_UNI_PERL_IDCONT

PERL_CALLCONV bool
Perl__is_uni_perl_idstart(pTHX_ UV c)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT__IS_UNI_PERL_IDSTART

PERL_CALLCONV bool
Perl__is_utf8_FOO(pTHX_ const U8 classnum, const U8 *p, const U8 * const e)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT__IS_UTF8_FOO           \
        assert(p); assert(e)

PERL_CALLCONV bool
Perl__is_utf8_perl_idcont(pTHX_ const U8 *p, const U8 * const e)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT__IS_UTF8_PERL_IDCONT   \
        assert(p); assert(e)

PERL_CALLCONV bool
Perl__is_utf8_perl_idstart(pTHX_ const U8 *p, const U8 * const e)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT__IS_UTF8_PERL_IDSTART  \
        assert(p); assert(e)

PERL_CALLCONV UV
Perl__to_uni_fold_flags(pTHX_ UV c, U8 *p, STRLEN *lenp, U8 flags);
#define PERL_ARGS_ASSERT__TO_UNI_FOLD_FLAGS     \
        assert(p); assert(lenp)

PERL_CALLCONV UV
Perl__to_utf8_fold_flags(pTHX_ const U8 *p, const U8 *e, U8 *ustrp, STRLEN *lenp, U8 flags);
#define PERL_ARGS_ASSERT__TO_UTF8_FOLD_FLAGS    \
        assert(p); assert(ustrp)

PERL_CALLCONV UV
Perl__to_utf8_lower_flags(pTHX_ const U8 *p, const U8 *e, U8 *ustrp, STRLEN *lenp, bool flags);
#define PERL_ARGS_ASSERT__TO_UTF8_LOWER_FLAGS   \
        assert(p); assert(ustrp)

PERL_CALLCONV UV
Perl__to_utf8_title_flags(pTHX_ const U8 *p, const U8 *e, U8 *ustrp, STRLEN *lenp, bool flags);
#define PERL_ARGS_ASSERT__TO_UTF8_TITLE_FLAGS   \
        assert(p); assert(ustrp)

PERL_CALLCONV UV
Perl__to_utf8_upper_flags(pTHX_ const U8 *p, const U8 *e, U8 *ustrp, STRLEN *lenp, bool flags);
#define PERL_ARGS_ASSERT__TO_UTF8_UPPER_FLAGS   \
        assert(p); assert(ustrp)

PERL_CALLCONV UV
Perl__utf8n_to_uvchr_msgs_helper(const U8 *s, STRLEN curlen, STRLEN *retlen, const U32 flags, U32 *errors, AV **msgs);
#define PERL_ARGS_ASSERT__UTF8N_TO_UVCHR_MSGS_HELPER \
        assert(s)

PERL_CALLCONV void
Perl__warn_problematic_locale(void);
#define PERL_ARGS_ASSERT__WARN_PROBLEMATIC_LOCALE

PERL_CALLCONV_NO_RET void
Perl_abort_execution(pTHX_ SV *msg_sv, const char * const name)
        __attribute__noreturn__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_ABORT_EXECUTION        \
        assert(name)

PERL_CALLCONV LOGOP *
Perl_alloc_LOGOP(pTHX_ I32 type, OP *first, OP *other)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_ALLOC_LOGOP

PERL_CALLCONV PADOFFSET
Perl_allocmy(pTHX_ const char * const name, const STRLEN len, const U32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_ALLOCMY                \
        assert(name)

PERL_CALLCONV bool
Perl_amagic_applies(pTHX_ SV *sv, int method, int flags);
#define PERL_ARGS_ASSERT_AMAGIC_APPLIES         \
        assert(sv)

PERL_CALLCONV SV *
Perl_amagic_call(pTHX_ SV *left, SV *right, int method, int dir);
#define PERL_ARGS_ASSERT_AMAGIC_CALL            \
        assert(left); assert(right)

PERL_CALLCONV SV *
Perl_amagic_deref_call(pTHX_ SV *ref, int method);
#define PERL_ARGS_ASSERT_AMAGIC_DEREF_CALL      \
        assert(ref)

PERL_CALLCONV bool
Perl_amagic_is_enabled(pTHX_ int method)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_AMAGIC_IS_ENABLED

PERL_CALLCONV I32
Perl_apply(pTHX_ I32 type, SV **mark, SV **sp)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_APPLY                  \
        assert(mark); assert(sp)

PERL_CALLCONV void
Perl_apply_attrs_string(pTHX_ const char *stashpv, CV *cv, const char *attrstr, STRLEN len);
#define PERL_ARGS_ASSERT_APPLY_ATTRS_STRING     \
        assert(stashpv); assert(cv); assert(attrstr)

PERL_CALLCONV OP *
Perl_apply_builtin_cv_attributes(pTHX_ CV *cv, OP *attrlist);
#define PERL_ARGS_ASSERT_APPLY_BUILTIN_CV_ATTRIBUTES \
        assert(cv)

PERL_CALLCONV void
Perl_atfork_lock(void);
#define PERL_ARGS_ASSERT_ATFORK_LOCK

PERL_CALLCONV void
Perl_atfork_unlock(void);
#define PERL_ARGS_ASSERT_ATFORK_UNLOCK

PERL_CALLCONV SV **
Perl_av_arylen_p(pTHX_ AV *av);
#define PERL_ARGS_ASSERT_AV_ARYLEN_P            \
        assert(av)

PERL_CALLCONV void
Perl_av_clear(pTHX_ AV *av);
#define PERL_ARGS_ASSERT_AV_CLEAR               \
        assert(av)

PERL_CALLCONV void
Perl_av_create_and_push(pTHX_ AV ** const avp, SV * const val);
#define PERL_ARGS_ASSERT_AV_CREATE_AND_PUSH     \
        assert(avp); assert(val)

PERL_CALLCONV SV **
Perl_av_create_and_unshift_one(pTHX_ AV ** const avp, SV * const val);
#define PERL_ARGS_ASSERT_AV_CREATE_AND_UNSHIFT_ONE \
        assert(avp); assert(val)

PERL_CALLCONV SV *
Perl_av_delete(pTHX_ AV *av, SSize_t key, I32 flags);
#define PERL_ARGS_ASSERT_AV_DELETE              \
        assert(av)

PERL_CALLCONV void
Perl_av_dump(pTHX_ AV *av);
#define PERL_ARGS_ASSERT_AV_DUMP

PERL_CALLCONV bool
Perl_av_exists(pTHX_ AV *av, SSize_t key)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_AV_EXISTS              \
        assert(av)

PERL_CALLCONV void
Perl_av_extend(pTHX_ AV *av, SSize_t key);
#define PERL_ARGS_ASSERT_AV_EXTEND              \
        assert(av)

PERL_CALLCONV void
Perl_av_extend_guts(pTHX_ AV *av, SSize_t key, SSize_t *maxp, SV ***allocp, SV ***arrayp)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_AV_EXTEND_GUTS         \
        assert(maxp); assert(allocp); assert(arrayp)

PERL_CALLCONV SV **
Perl_av_fetch(pTHX_ AV *av, SSize_t key, I32 lval)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_AV_FETCH               \
        assert(av)

PERL_CALLCONV void
Perl_av_fill(pTHX_ AV *av, SSize_t fill);
#define PERL_ARGS_ASSERT_AV_FILL                \
        assert(av)

PERL_CALLCONV IV *
Perl_av_iter_p(pTHX_ AV *av);
#define PERL_ARGS_ASSERT_AV_ITER_P              \
        assert(av)

PERL_CALLCONV SSize_t
Perl_av_len(pTHX_ AV *av)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_AV_LEN                 \
        assert(av)

PERL_CALLCONV AV *
Perl_av_make(pTHX_ SSize_t size, SV **strp)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_AV_MAKE                \
        assert(strp)

PERL_CALLCONV SV *
Perl_av_nonelem(pTHX_ AV *av, SSize_t ix)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_AV_NONELEM             \
        assert(av)

PERL_CALLCONV SV *
Perl_av_pop(pTHX_ AV *av);
#define PERL_ARGS_ASSERT_AV_POP                 \
        assert(av)

PERL_CALLCONV void
Perl_av_push(pTHX_ AV *av, SV *val);
#define PERL_ARGS_ASSERT_AV_PUSH                \
        assert(av); assert(val)

PERL_CALLCONV void
Perl_av_reify(pTHX_ AV *av);
#define PERL_ARGS_ASSERT_AV_REIFY               \
        assert(av)

PERL_CALLCONV SV *
Perl_av_shift(pTHX_ AV *av)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_AV_SHIFT               \
        assert(av)

PERL_CALLCONV SV **
Perl_av_store(pTHX_ AV *av, SSize_t key, SV *val);
#define PERL_ARGS_ASSERT_AV_STORE               \
        assert(av)

/* PERL_CALLCONV SSize_t
av_tindex(pTHX_ AV *av)
        __attribute__warn_unused_result__; */

/* PERL_CALLCONV SSize_t
av_top_index(pTHX_ AV *av)
        __attribute__warn_unused_result__; */

PERL_CALLCONV void
Perl_av_undef(pTHX_ AV *av);
#define PERL_ARGS_ASSERT_AV_UNDEF               \
        assert(av)

PERL_CALLCONV void
Perl_av_unshift(pTHX_ AV *av, SSize_t num);
#define PERL_ARGS_ASSERT_AV_UNSHIFT             \
        assert(av)

PERL_CALLCONV OP *
Perl_bind_match(pTHX_ I32 type, OP *left, OP *right)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_BIND_MATCH             \
        assert(left); assert(right)

PERL_CALLCONV OP *
Perl_block_end(pTHX_ I32 floor, OP *seq)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_BLOCK_END

PERL_CALLCONV U8
Perl_block_gimme(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_BLOCK_GIMME

PERL_CALLCONV int
Perl_block_start(pTHX_ int full)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_BLOCK_START

PERL_CALLCONV void
Perl_blockhook_register(pTHX_ BHK *hk);
#define PERL_ARGS_ASSERT_BLOCKHOOK_REGISTER     \
        assert(hk)

PERL_CALLCONV void
Perl_boot_core_PerlIO(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_BOOT_CORE_PERLIO

PERL_CALLCONV void
Perl_boot_core_UNIVERSAL(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_BOOT_CORE_UNIVERSAL

PERL_CALLCONV void
Perl_boot_core_builtin(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_BOOT_CORE_BUILTIN

PERL_CALLCONV void
Perl_boot_core_mro(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_BOOT_CORE_MRO

PERL_CALLCONV OP *
Perl_build_infix_plugin(pTHX_ OP *lhs, OP *rhs, void *tokendata)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_BUILD_INFIX_PLUGIN     \
        assert(lhs); assert(rhs); assert(tokendata)

PERL_CALLCONV int
Perl_bytes_cmp_utf8(pTHX_ const U8 *b, STRLEN blen, const U8 *u, STRLEN ulen);
#define PERL_ARGS_ASSERT_BYTES_CMP_UTF8         \
        assert(b); assert(u)

PERL_CALLCONV U8 *
Perl_bytes_from_utf8(pTHX_ const U8 *s, STRLEN *lenp, bool *is_utf8p);
#define PERL_ARGS_ASSERT_BYTES_FROM_UTF8        \
        assert(s); assert(lenp); assert(is_utf8p)

PERL_CALLCONV U8 *
Perl_bytes_from_utf8_loc(const U8 *s, STRLEN *lenp, bool *is_utf8p, const U8 **first_unconverted);
#define PERL_ARGS_ASSERT_BYTES_FROM_UTF8_LOC    \
        assert(s); assert(lenp); assert(is_utf8p)

PERL_CALLCONV U8 *
Perl_bytes_to_utf8(pTHX_ const U8 *s, STRLEN *lenp);
#define PERL_ARGS_ASSERT_BYTES_TO_UTF8          \
        assert(s); assert(lenp)

PERL_CALLCONV I32
Perl_call_argv(pTHX_ const char *sub_name, I32 flags, char **argv);
#define PERL_ARGS_ASSERT_CALL_ARGV              \
        assert(sub_name); assert(argv)

PERL_CALLCONV void
Perl_call_atexit(pTHX_ ATEXIT_t fn, void *ptr);
#define PERL_ARGS_ASSERT_CALL_ATEXIT

PERL_CALLCONV void
Perl_call_list(pTHX_ I32 oldscope, AV *paramList);
#define PERL_ARGS_ASSERT_CALL_LIST              \
        assert(paramList)

PERL_CALLCONV I32
Perl_call_method(pTHX_ const char *methname, I32 flags);
#define PERL_ARGS_ASSERT_CALL_METHOD            \
        assert(methname)

PERL_CALLCONV I32
Perl_call_pv(pTHX_ const char *sub_name, I32 flags);
#define PERL_ARGS_ASSERT_CALL_PV                \
        assert(sub_name)

PERL_CALLCONV I32
Perl_call_sv(pTHX_ SV *sv, volatile I32 flags);
#define PERL_ARGS_ASSERT_CALL_SV                \
        assert(sv)

PERL_CALLCONV const PERL_CONTEXT *
Perl_caller_cx(pTHX_ I32 level, const PERL_CONTEXT **dbcxp);
#define PERL_ARGS_ASSERT_CALLER_CX

PERL_CALLCONV Malloc_t
Perl_calloc(MEM_SIZE elements, MEM_SIZE size)
        __attribute__malloc__
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_CALLOC

PERL_CALLCONV bool
Perl_cando(pTHX_ Mode_t mode, bool effective, const Stat_t *statbufp)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CANDO                  \
        assert(statbufp)

PERL_CALLCONV I32
Perl_cast_i32(NV f)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_CAST_I32

PERL_CALLCONV IV
Perl_cast_iv(NV f)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_CAST_IV

PERL_CALLCONV U32
Perl_cast_ulong(NV f)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_CAST_ULONG

PERL_CALLCONV UV
Perl_cast_uv(NV f)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_CAST_UV

PERL_CALLCONV bool
Perl_check_utf8_print(pTHX_ const U8 *s, const STRLEN len)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CHECK_UTF8_PRINT       \
        assert(s)

PERL_CALLCONV OP *
Perl_ck_entersub_args_core(pTHX_ OP *entersubop, GV *namegv, SV *protosv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CK_ENTERSUB_ARGS_CORE  \
        assert(entersubop); assert(namegv); assert(protosv)

PERL_CALLCONV OP *
Perl_ck_entersub_args_list(pTHX_ OP *entersubop);
#define PERL_ARGS_ASSERT_CK_ENTERSUB_ARGS_LIST  \
        assert(entersubop)

PERL_CALLCONV OP *
Perl_ck_entersub_args_proto(pTHX_ OP *entersubop, GV *namegv, SV *protosv);
#define PERL_ARGS_ASSERT_CK_ENTERSUB_ARGS_PROTO \
        assert(entersubop); assert(namegv); assert(protosv)

PERL_CALLCONV OP *
Perl_ck_entersub_args_proto_or_list(pTHX_ OP *entersubop, GV *namegv, SV *protosv);
#define PERL_ARGS_ASSERT_CK_ENTERSUB_ARGS_PROTO_OR_LIST \
        assert(entersubop); assert(namegv); assert(protosv)

PERL_CALLCONV void
Perl_ck_warner(pTHX_ U32 err, const char *pat, ...)
        __attribute__format__(__printf__,pTHX_2,pTHX_3);
#define PERL_ARGS_ASSERT_CK_WARNER              \
        assert(pat)

PERL_CALLCONV void
Perl_ck_warner_d(pTHX_ U32 err, const char *pat, ...)
        __attribute__format__(__printf__,pTHX_2,pTHX_3);
#define PERL_ARGS_ASSERT_CK_WARNER_D            \
        assert(pat)

PERL_CALLCONV bool
Perl_ckwarn(pTHX_ U32 w)
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_CKWARN

PERL_CALLCONV bool
Perl_ckwarn_d(pTHX_ U32 w)
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_CKWARN_D

PERL_CALLCONV void
Perl_clear_defarray(pTHX_ AV *av, bool abandon);
#define PERL_ARGS_ASSERT_CLEAR_DEFARRAY         \
        assert(av)

PERL_CALLCONV const COP *
Perl_closest_cop(pTHX_ const COP *cop, const OP *o, const OP *curop, bool opnext)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CLOSEST_COP            \
        assert(cop)

PERL_CALLCONV OP *
Perl_cmpchain_extend(pTHX_ I32 type, OP *ch, OP *right)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CMPCHAIN_EXTEND        \
        assert(ch)

PERL_CALLCONV OP *
Perl_cmpchain_finish(pTHX_ OP *ch)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CMPCHAIN_FINISH        \
        assert(ch)

PERL_CALLCONV OP *
Perl_cmpchain_start(pTHX_ I32 type, OP *left, OP *right)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CMPCHAIN_START

PERL_CALLCONV const char *
Perl_cntrl_to_mnemonic(const U8 c)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_CNTRL_TO_MNEMONIC

PERL_CALLCONV const char *
Perl_cop_fetch_label(pTHX_ COP * const cop, STRLEN *len, U32 *flags);
#define PERL_ARGS_ASSERT_COP_FETCH_LABEL        \
        assert(cop)

PERL_CALLCONV void
Perl_cop_store_label(pTHX_ COP * const cop, const char *label, STRLEN len, U32 flags);
#define PERL_ARGS_ASSERT_COP_STORE_LABEL        \
        assert(cop); assert(label)

PERL_CALLCONV SV *
Perl_core_prototype(pTHX_ SV *sv, const char *name, const int code, int * const opnum)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CORE_PROTOTYPE         \
        assert(name)

PERL_CALLCONV OP *
Perl_coresub_op(pTHX_ SV * const coreargssv, const int code, const int opnum)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CORESUB_OP             \
        assert(coreargssv)

PERL_CALLCONV void
Perl_create_eval_scope(pTHX_ OP *retop, U32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CREATE_EVAL_SCOPE

PERL_CALLCONV_NO_RET void
Perl_croak(pTHX_ const char *pat, ...)
        __attribute__noreturn__
        __attribute__format__null_ok__(__printf__,pTHX_1,pTHX_2);
#define PERL_ARGS_ASSERT_CROAK

PERL_CALLCONV_NO_RET void
Perl_croak_caller(const char *pat, ...)
        __attribute__noreturn__
        __attribute__visibility__("hidden")
        __attribute__format__null_ok__(__printf__,1,2);
#define PERL_ARGS_ASSERT_CROAK_CALLER

PERL_STATIC_NO_RET void
Perl_croak_memory_wrap(void)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_CROAK_MEMORY_WRAP

PERL_CALLCONV_NO_RET void
Perl_croak_no_mem(void)
        __attribute__noreturn__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CROAK_NO_MEM

PERL_CALLCONV_NO_RET void
Perl_croak_no_modify(void)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_CROAK_NO_MODIFY

PERL_CALLCONV_NO_RET void
Perl_croak_popstack(void)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_CROAK_POPSTACK

PERL_CALLCONV_NO_RET void
Perl_croak_sv(pTHX_ SV *baseex)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_CROAK_SV               \
        assert(baseex)

PERL_CALLCONV_NO_RET void
Perl_croak_xs_usage(const CV * const cv, const char * const params)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_CROAK_XS_USAGE         \
        assert(cv); assert(params)

PERL_CALLCONV Signal_t
Perl_csighandler1(int sig);
#define PERL_ARGS_ASSERT_CSIGHANDLER1

PERL_CALLCONV Signal_t
Perl_csighandler3(int sig, Siginfo_t *info, void *uap);
#define PERL_ARGS_ASSERT_CSIGHANDLER3

PERL_CALLCONV regexp_engine const *
Perl_current_re_engine(pTHX);
#define PERL_ARGS_ASSERT_CURRENT_RE_ENGINE

PERL_CALLCONV XOPRETANY
Perl_custom_op_get_field(pTHX_ const OP *o, const xop_flags_enum field)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_CUSTOM_OP_GET_FIELD    \
        assert(o)

PERL_CALLCONV void
Perl_custom_op_register(pTHX_ Perl_ppaddr_t ppaddr, const XOP *xop);
#define PERL_ARGS_ASSERT_CUSTOM_OP_REGISTER     \
        assert(ppaddr); assert(xop)

PERL_CALLCONV void
Perl_cv_ckproto_len_flags(pTHX_ const CV *cv, const GV *gv, const char *p, const STRLEN len, const U32 flags);
#define PERL_ARGS_ASSERT_CV_CKPROTO_LEN_FLAGS   \
        assert(cv)

PERL_CALLCONV CV *
Perl_cv_clone(pTHX_ CV *proto);
#define PERL_ARGS_ASSERT_CV_CLONE               \
        assert(proto)

PERL_CALLCONV CV *
Perl_cv_clone_into(pTHX_ CV *proto, CV *target)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CV_CLONE_INTO          \
        assert(proto); assert(target)

PERL_CALLCONV SV *
Perl_cv_const_sv(const CV * const cv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_CV_CONST_SV

PERL_CALLCONV SV *
Perl_cv_const_sv_or_av(const CV * const cv)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CV_CONST_SV_OR_AV

PERL_CALLCONV void
Perl_cv_forget_slab(pTHX_ CV *cv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CV_FORGET_SLAB

PERL_CALLCONV void
Perl_cv_get_call_checker(pTHX_ CV *cv, Perl_call_checker *ckfun_p, SV **ckobj_p);
#define PERL_ARGS_ASSERT_CV_GET_CALL_CHECKER    \
        assert(cv); assert(ckfun_p); assert(ckobj_p)

PERL_CALLCONV void
Perl_cv_get_call_checker_flags(pTHX_ CV *cv, U32 gflags, Perl_call_checker *ckfun_p, SV **ckobj_p, U32 *ckflags_p);
#define PERL_ARGS_ASSERT_CV_GET_CALL_CHECKER_FLAGS \
        assert(cv); assert(ckfun_p); assert(ckobj_p); assert(ckflags_p)

PERL_CALLCONV SV *
Perl_cv_name(pTHX_ CV *cv, SV *sv, U32 flags);
#define PERL_ARGS_ASSERT_CV_NAME                \
        assert(cv)

PERL_CALLCONV void
Perl_cv_set_call_checker(pTHX_ CV *cv, Perl_call_checker ckfun, SV *ckobj);
#define PERL_ARGS_ASSERT_CV_SET_CALL_CHECKER    \
        assert(cv); assert(ckfun); assert(ckobj)

PERL_CALLCONV void
Perl_cv_set_call_checker_flags(pTHX_ CV *cv, Perl_call_checker ckfun, SV *ckobj, U32 ckflags);
#define PERL_ARGS_ASSERT_CV_SET_CALL_CHECKER_FLAGS \
        assert(cv); assert(ckfun); assert(ckobj)

PERL_CALLCONV void
Perl_cv_undef(pTHX_ CV *cv);
#define PERL_ARGS_ASSERT_CV_UNDEF               \
        assert(cv)

PERL_CALLCONV void
Perl_cv_undef_flags(pTHX_ CV *cv, U32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_CV_UNDEF_FLAGS         \
        assert(cv)

PERL_CALLCONV GV *
Perl_cvgv_from_hek(pTHX_ CV *cv);
#define PERL_ARGS_ASSERT_CVGV_FROM_HEK          \
        assert(cv)

PERL_CALLCONV void
Perl_cvgv_set(pTHX_ CV *cv, GV *gv);
#define PERL_ARGS_ASSERT_CVGV_SET               \
        assert(cv)

PERL_CALLCONV void
Perl_cvstash_set(pTHX_ CV *cv, HV *stash);
#define PERL_ARGS_ASSERT_CVSTASH_SET            \
        assert(cv)

PERL_CALLCONV void
Perl_cx_dump(pTHX_ PERL_CONTEXT *cx);
#define PERL_ARGS_ASSERT_CX_DUMP                \
        assert(cx)

PERL_CALLCONV I32
Perl_cxinc(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_CXINC

PERL_CALLCONV void
Perl_deb(pTHX_ const char *pat, ...)
        __attribute__format__(__printf__,pTHX_1,pTHX_2);
#define PERL_ARGS_ASSERT_DEB                    \
        assert(pat)

PERL_CALLCONV void
Perl_deb_stack_all(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DEB_STACK_ALL

PERL_CALLCONV I32
Perl_debop(pTHX_ const OP *o);
#define PERL_ARGS_ASSERT_DEBOP                  \
        assert(o)

PERL_CALLCONV void
Perl_debprofdump(pTHX);
#define PERL_ARGS_ASSERT_DEBPROFDUMP

PERL_CALLCONV I32
Perl_debstack(pTHX);
#define PERL_ARGS_ASSERT_DEBSTACK

PERL_CALLCONV I32
Perl_debstackptrs(pTHX);
#define PERL_ARGS_ASSERT_DEBSTACKPTRS

PERL_CALLCONV void
Perl_debug_hash_seed(pTHX_ bool via_debug_h)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DEBUG_HASH_SEED

PERL_CALLCONV SV *
Perl_defelem_target(pTHX_ SV *sv, MAGIC *mg)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DEFELEM_TARGET         \
        assert(sv)

PERL_CALLCONV void
Perl_delete_eval_scope(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DELETE_EVAL_SCOPE

PERL_CALLCONV char *
Perl_delimcpy(char *to, const char *to_end, const char *from, const char *from_end, const int delim, I32 *retlen);
#define PERL_ARGS_ASSERT_DELIMCPY               \
        assert(to); assert(to_end); assert(from); assert(from_end); assert(retlen)

PERL_CALLCONV char *
Perl_delimcpy_no_escape(char *to, const char *to_end, const char *from, const char *from_end, const int delim, I32 *retlen);
#define PERL_ARGS_ASSERT_DELIMCPY_NO_ESCAPE     \
        assert(to); assert(to_end); assert(from); assert(from_end); assert(retlen)

PERL_CALLCONV void
Perl_despatch_signals(pTHX);
#define PERL_ARGS_ASSERT_DESPATCH_SIGNALS

PERL_CALLCONV_NO_RET OP *
Perl_die(pTHX_ const char *pat, ...)
        __attribute__noreturn__
        __attribute__format__null_ok__(__printf__,pTHX_1,pTHX_2);
#define PERL_ARGS_ASSERT_DIE

PERL_CALLCONV_NO_RET OP *
Perl_die_sv(pTHX_ SV *baseex)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_DIE_SV                 \
        assert(baseex)

PERL_CALLCONV_NO_RET void
Perl_die_unwind(pTHX_ SV *msv)
        __attribute__noreturn__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DIE_UNWIND             \
        assert(msv)

PERL_CALLCONV bool
Perl_do_aexec5(pTHX_ SV *really, SV **mark, SV **sp, int fd, int do_report)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_AEXEC5              \
        assert(mark); assert(sp)

PERL_CALLCONV bool
Perl_do_close(pTHX_ GV *gv, bool is_explicit);
#define PERL_ARGS_ASSERT_DO_CLOSE

PERL_CALLCONV void
Perl_do_dump_pad(pTHX_ I32 level, PerlIO *file, PADLIST *padlist, int full)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_DUMP_PAD            \
        assert(file)

PERL_CALLCONV bool
Perl_do_eof(pTHX_ GV *gv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_EOF                 \
        assert(gv)

PERL_CALLCONV void
Perl_do_gv_dump(pTHX_ I32 level, PerlIO *file, const char *name, GV *sv);
#define PERL_ARGS_ASSERT_DO_GV_DUMP             \
        assert(file); assert(name)

PERL_CALLCONV void
Perl_do_gvgv_dump(pTHX_ I32 level, PerlIO *file, const char *name, GV *sv);
#define PERL_ARGS_ASSERT_DO_GVGV_DUMP           \
        assert(file); assert(name)

PERL_CALLCONV void
Perl_do_hv_dump(pTHX_ I32 level, PerlIO *file, const char *name, HV *sv);
#define PERL_ARGS_ASSERT_DO_HV_DUMP             \
        assert(file); assert(name)

PERL_CALLCONV void
Perl_do_join(pTHX_ SV *sv, SV *delim, SV **mark, SV **sp);
#define PERL_ARGS_ASSERT_DO_JOIN                \
        assert(sv); assert(delim); assert(mark); assert(sp)

PERL_CALLCONV void
Perl_do_magic_dump(pTHX_ I32 level, PerlIO *file, const MAGIC *mg, I32 nest, I32 maxnest, bool dumpops, STRLEN pvlim);
#define PERL_ARGS_ASSERT_DO_MAGIC_DUMP          \
        assert(file)

PERL_CALLCONV I32
Perl_do_ncmp(pTHX_ SV * const left, SV * const right)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_NCMP                \
        assert(left); assert(right)

PERL_CALLCONV void
Perl_do_op_dump(pTHX_ I32 level, PerlIO *file, const OP *o);
#define PERL_ARGS_ASSERT_DO_OP_DUMP             \
        assert(file)

PERL_CALLCONV bool
Perl_do_open6(pTHX_ GV *gv, const char *oname, STRLEN len, PerlIO *supplied_fp, SV **svp, U32 num)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_OPEN6               \
        assert(gv); assert(oname)

PERL_CALLCONV bool
Perl_do_open_raw(pTHX_ GV *gv, const char *oname, STRLEN len, int rawmode, int rawperm, Stat_t *statbufp)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_OPEN_RAW            \
        assert(gv); assert(oname)

PERL_CALLCONV bool
Perl_do_openn(pTHX_ GV *gv, const char *oname, I32 len, int as_raw, int rawmode, int rawperm, PerlIO *supplied_fp, SV **svp, I32 num);
#define PERL_ARGS_ASSERT_DO_OPENN               \
        assert(gv); assert(oname)

PERL_CALLCONV void
Perl_do_pmop_dump(pTHX_ I32 level, PerlIO *file, const PMOP *pm);
#define PERL_ARGS_ASSERT_DO_PMOP_DUMP           \
        assert(file)

PERL_CALLCONV bool
Perl_do_print(pTHX_ SV *sv, PerlIO *fp)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_PRINT               \
        assert(fp)

PERL_CALLCONV OP *
Perl_do_readline(pTHX)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_READLINE

PERL_CALLCONV bool
Perl_do_seek(pTHX_ GV *gv, Off_t pos, int whence)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_SEEK

PERL_CALLCONV void
Perl_do_sprintf(pTHX_ SV *sv, SSize_t len, SV **sarg);
#define PERL_ARGS_ASSERT_DO_SPRINTF             \
        assert(sv); assert(sarg)

PERL_CALLCONV void
Perl_do_sv_dump(pTHX_ I32 level, PerlIO *file, SV *sv, I32 nest, I32 maxnest, bool dumpops, STRLEN pvlim);
#define PERL_ARGS_ASSERT_DO_SV_DUMP             \
        assert(file)

PERL_CALLCONV Off_t
Perl_do_sysseek(pTHX_ GV *gv, Off_t pos, int whence)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_SYSSEEK             \
        assert(gv)

PERL_CALLCONV Off_t
Perl_do_tell(pTHX_ GV *gv)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_TELL                \
        assert(gv)

PERL_CALLCONV Size_t
Perl_do_trans(pTHX_ SV *sv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_TRANS               \
        assert(sv)

PERL_CALLCONV I16
Perl_do_uniprop_match(const char * const key, const U16 key_len)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_DO_UNIPROP_MATCH       \
        assert(key)

PERL_CALLCONV UV
Perl_do_vecget(pTHX_ SV *sv, STRLEN offset, int size)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_VECGET              \
        assert(sv)

PERL_CALLCONV void
Perl_do_vecset(pTHX_ SV *sv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_VECSET              \
        assert(sv)

PERL_CALLCONV void
Perl_do_vop(pTHX_ I32 optype, SV *sv, SV *left, SV *right)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DO_VOP                 \
        assert(sv); assert(left); assert(right)

PERL_CALLCONV OP *
Perl_dofile(pTHX_ OP *term, I32 force_builtin)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DOFILE                 \
        assert(term)

PERL_CALLCONV bool
Perl_doing_taint(int argc, char **argv, char **env)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_DOING_TAINT

PERL_CALLCONV OP *
Perl_doref(pTHX_ OP *o, I32 type, bool set_op_ref);
#define PERL_ARGS_ASSERT_DOREF                  \
        assert(o)

PERL_CALLCONV void
Perl_dounwind(pTHX_ I32 cxix);
#define PERL_ARGS_ASSERT_DOUNWIND

PERL_CALLCONV U8
Perl_dowantarray(pTHX)
        __attribute__deprecated__
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_DOWANTARRAY

PERL_CALLCONV void
Perl_drand48_init_r(perl_drand48_t *random_state, U32 seed);
#define PERL_ARGS_ASSERT_DRAND48_INIT_R         \
        assert(random_state)

PERL_CALLCONV double
Perl_drand48_r(perl_drand48_t *random_state);
#define PERL_ARGS_ASSERT_DRAND48_R              \
        assert(random_state)

PERL_CALLCONV void
Perl_dump_all(pTHX);
#define PERL_ARGS_ASSERT_DUMP_ALL

PERL_CALLCONV void
Perl_dump_all_perl(pTHX_ bool justperl)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DUMP_ALL_PERL

PERL_CALLCONV void
Perl_dump_eval(pTHX);
#define PERL_ARGS_ASSERT_DUMP_EVAL

PERL_CALLCONV void
Perl_dump_form(pTHX_ const GV *gv);
#define PERL_ARGS_ASSERT_DUMP_FORM              \
        assert(gv)

PERL_CALLCONV void
Perl_dump_indent(pTHX_ I32 level, PerlIO *file, const char *pat, ...)
        __attribute__format__(__printf__,pTHX_3,pTHX_4);
#define PERL_ARGS_ASSERT_DUMP_INDENT            \
        assert(file); assert(pat)

PERL_CALLCONV void
Perl_dump_packsubs(pTHX_ const HV *stash);
#define PERL_ARGS_ASSERT_DUMP_PACKSUBS          \
        assert(stash)

PERL_CALLCONV void
Perl_dump_packsubs_perl(pTHX_ const HV *stash, bool justperl)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DUMP_PACKSUBS_PERL     \
        assert(stash)

PERL_CALLCONV void
Perl_dump_sub(pTHX_ const GV *gv);
#define PERL_ARGS_ASSERT_DUMP_SUB               \
        assert(gv)

PERL_CALLCONV void
Perl_dump_sub_perl(pTHX_ const GV *gv, bool justperl)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_DUMP_SUB_PERL          \
        assert(gv)

PERL_CALLCONV void
Perl_dump_vindent(pTHX_ I32 level, PerlIO *file, const char *pat, va_list *args);
#define PERL_ARGS_ASSERT_DUMP_VINDENT           \
        assert(file); assert(pat)

PERL_CALLCONV char  *
Perl_dup_warnings(pTHX_ char *warnings);
#define PERL_ARGS_ASSERT_DUP_WARNINGS

PERL_CALLCONV void
Perl_emulate_cop_io(pTHX_ const COP * const c, SV * const sv);
#define PERL_ARGS_ASSERT_EMULATE_COP_IO         \
        assert(c); assert(sv)

PERL_CALLCONV SV *
Perl_eval_pv(pTHX_ const char *p, I32 croak_on_error);
#define PERL_ARGS_ASSERT_EVAL_PV                \
        assert(p)

PERL_CALLCONV I32
Perl_eval_sv(pTHX_ SV *sv, I32 flags);
#define PERL_ARGS_ASSERT_EVAL_SV                \
        assert(sv)

PERL_CALLCONV void
Perl_fbm_compile(pTHX_ SV *sv, U32 flags);
#define PERL_ARGS_ASSERT_FBM_COMPILE            \
        assert(sv)

PERL_CALLCONV char *
Perl_fbm_instr(pTHX_ unsigned char *big, unsigned char *bigend, SV *littlestr, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_FBM_INSTR              \
        assert(big); assert(bigend); assert(littlestr)

PERL_CALLCONV SV *
Perl_filter_add(pTHX_ filter_t funcp, SV *datasv);
#define PERL_ARGS_ASSERT_FILTER_ADD

PERL_CALLCONV void
Perl_filter_del(pTHX_ filter_t funcp);
#define PERL_ARGS_ASSERT_FILTER_DEL             \
        assert(funcp)

PERL_CALLCONV I32
Perl_filter_read(pTHX_ int idx, SV *buf_sv, int maxlen)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_FILTER_READ            \
        assert(buf_sv)

PERL_CALLCONV CV *
Perl_find_lexical_cv(pTHX_ PADOFFSET off)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_FIND_LEXICAL_CV

PERL_CALLCONV CV *
Perl_find_runcv(pTHX_ U32 *db_seqp)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_FIND_RUNCV

PERL_CALLCONV CV *
Perl_find_runcv_where(pTHX_ U8 cond, IV arg, U32 *db_seqp)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_FIND_RUNCV_WHERE

PERL_CALLCONV SV *
Perl_find_rundefsv(pTHX);
#define PERL_ARGS_ASSERT_FIND_RUNDEFSV

PERL_CALLCONV char *
Perl_find_script(pTHX_ const char *scriptname, bool dosearch, const char * const * const search_ext, I32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_FIND_SCRIPT            \
        assert(scriptname)

/* PERL_CALLCONV I32
foldEQ_utf8(pTHX_ const char *s1, char **pe1, UV l1, bool u1, const char *s2, char **pe2, UV l2, bool u2); */

PERL_CALLCONV I32
Perl_foldEQ_utf8_flags(pTHX_ const char *s1, char **pe1, UV l1, bool u1, const char *s2, char **pe2, UV l2, bool u2, U32 flags);
#define PERL_ARGS_ASSERT_FOLDEQ_UTF8_FLAGS      \
        assert(s1); assert(s2)

PERL_CALLCONV void
Perl_forbid_outofblock_ops(pTHX_ OP *o, const char *blockname);
#define PERL_ARGS_ASSERT_FORBID_OUTOFBLOCK_OPS  \
        assert(o); assert(blockname)

PERL_CALLCONV void
Perl_force_locale_unlock(void)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_FORCE_LOCALE_UNLOCK

PERL_CALLCONV char *
Perl_form(pTHX_ const char *pat, ...)
        __attribute__format__(__printf__,pTHX_1,pTHX_2);
#define PERL_ARGS_ASSERT_FORM                   \
        assert(pat)

PERL_CALLCONV void
Perl_free_tied_hv_pool(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_FREE_TIED_HV_POOL

PERL_CALLCONV void
Perl_free_tmps(pTHX);
#define PERL_ARGS_ASSERT_FREE_TMPS

PERL_CALLCONV SV *
Perl_get_and_check_backslash_N_name(pTHX_ const char *s, const char *e, const bool is_utf8, const char **error_msg)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GET_AND_CHECK_BACKSLASH_N_NAME \
        assert(s); assert(e); assert(error_msg)

PERL_CALLCONV AV *
Perl_get_av(pTHX_ const char *name, I32 flags);
#define PERL_ARGS_ASSERT_GET_AV                 \
        assert(name)

PERL_CALLCONV CV *
Perl_get_cv(pTHX_ const char *name, I32 flags);
#define PERL_ARGS_ASSERT_GET_CV                 \
        assert(name)

PERL_CALLCONV CV *
Perl_get_cvn_flags(pTHX_ const char *name, STRLEN len, I32 flags);
#define PERL_ARGS_ASSERT_GET_CVN_FLAGS          \
        assert(name)

PERL_CALLCONV void
Perl_get_db_sub(pTHX_ SV **svp, CV *cv);
#define PERL_ARGS_ASSERT_GET_DB_SUB             \
        assert(cv)

PERL_CALLCONV const char *
Perl_get_deprecated_property_msg(const Size_t warning_offset)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GET_DEPRECATED_PROPERTY_MSG

PERL_CALLCONV int
Perl_get_extended_os_errno(void)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_GET_EXTENDED_OS_ERRNO

PERL_CALLCONV void
Perl_get_hash_seed(pTHX_ unsigned char * const seed_buffer)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_GET_HASH_SEED          \
        assert(seed_buffer)

PERL_CALLCONV HV *
Perl_get_hv(pTHX_ const char *name, I32 flags);
#define PERL_ARGS_ASSERT_GET_HV                 \
        assert(name)

PERL_CALLCONV const char *
Perl_get_no_modify(pTHX)
        __attribute__deprecated__
        __attribute__warn_unused_result__
        __attribute__pure__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_GET_NO_MODIFY

PERL_CALLCONV char **
Perl_get_op_descs(pTHX)
        __attribute__deprecated__
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_GET_OP_DESCS

PERL_CALLCONV char **
Perl_get_op_names(pTHX)
        __attribute__deprecated__
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_GET_OP_NAMES

PERL_CALLCONV U32 *
Perl_get_opargs(pTHX)
        __attribute__deprecated__
        __attribute__warn_unused_result__
        __attribute__pure__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_GET_OPARGS

PERL_CALLCONV PPADDR_t *
Perl_get_ppaddr(pTHX)
        __attribute__deprecated__
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_GET_PPADDR

PERL_CALLCONV SV *
Perl_get_prop_definition(pTHX_ const int table_index)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GET_PROP_DEFINITION

PERL_CALLCONV const char * const *
Perl_get_prop_values(const int table_index)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GET_PROP_VALUES

PERL_CALLCONV REGEXP *
Perl_get_re_arg(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_GET_RE_ARG

PERL_CALLCONV SV *
Perl_get_sv(pTHX_ const char *name, I32 flags);
#define PERL_ARGS_ASSERT_GET_SV                 \
        assert(name)

PERL_CALLCONV int
Perl_getcwd_sv(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_GETCWD_SV              \
        assert(sv)

PERL_CALLCONV void
Perl_gp_free(pTHX_ GV *gv);
#define PERL_ARGS_ASSERT_GP_FREE

PERL_CALLCONV GP *
Perl_gp_ref(pTHX_ GP *gp);
#define PERL_ARGS_ASSERT_GP_REF

PERL_CALLCONV bool
Perl_grok_atoUV(const char *pv, UV *valptr, const char **endptr);
#define PERL_ARGS_ASSERT_GROK_ATOUV             \
        assert(pv); assert(valptr)

PERL_CALLCONV UV
Perl_grok_bin(pTHX_ const char *start, STRLEN *len_p, I32 *flags, NV *result);
#define PERL_ARGS_ASSERT_GROK_BIN               \
        assert(start); assert(len_p); assert(flags)

PERL_CALLCONV UV
Perl_grok_bin_oct_hex(pTHX_ const char *start, STRLEN *len_p, I32 *flags, NV *result, const unsigned shift, const U8 lookup_bit, const char prefix);
#define PERL_ARGS_ASSERT_GROK_BIN_OCT_HEX       \
        assert(start); assert(len_p); assert(flags)

PERL_CALLCONV UV
Perl_grok_hex(pTHX_ const char *start, STRLEN *len_p, I32 *flags, NV *result);
#define PERL_ARGS_ASSERT_GROK_HEX               \
        assert(start); assert(len_p); assert(flags)

PERL_CALLCONV int
Perl_grok_infnan(pTHX_ const char **sp, const char *send);
#define PERL_ARGS_ASSERT_GROK_INFNAN            \
        assert(sp); assert(send)

PERL_CALLCONV int
Perl_grok_number(pTHX_ const char *pv, STRLEN len, UV *valuep);
#define PERL_ARGS_ASSERT_GROK_NUMBER            \
        assert(pv)

PERL_CALLCONV int
Perl_grok_number_flags(pTHX_ const char *pv, STRLEN len, UV *valuep, U32 flags);
#define PERL_ARGS_ASSERT_GROK_NUMBER_FLAGS      \
        assert(pv)

PERL_CALLCONV bool
Perl_grok_numeric_radix(pTHX_ const char **sp, const char *send)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GROK_NUMERIC_RADIX     \
        assert(sp); assert(send)

PERL_CALLCONV UV
Perl_grok_oct(pTHX_ const char *start, STRLEN *len_p, I32 *flags, NV *result);
#define PERL_ARGS_ASSERT_GROK_OCT               \
        assert(start); assert(len_p); assert(flags)

PERL_CALLCONV GV *
Perl_gv_add_by_type(pTHX_ GV *gv, svtype type);
#define PERL_ARGS_ASSERT_GV_ADD_BY_TYPE

/* PERL_CALLCONV GV *
gv_autoload4(pTHX_ HV *stash, const char *name, STRLEN len, I32 method)
        __attribute__warn_unused_result__; */

PERL_CALLCONV GV *
Perl_gv_autoload_pv(pTHX_ HV *stash, const char *namepv, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GV_AUTOLOAD_PV         \
        assert(namepv)

PERL_CALLCONV GV *
Perl_gv_autoload_pvn(pTHX_ HV *stash, const char *name, STRLEN len, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GV_AUTOLOAD_PVN        \
        assert(name)

PERL_CALLCONV GV *
Perl_gv_autoload_sv(pTHX_ HV *stash, SV *namesv, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GV_AUTOLOAD_SV         \
        assert(namesv)

PERL_CALLCONV void
Perl_gv_check(pTHX_ HV *stash);
#define PERL_ARGS_ASSERT_GV_CHECK               \
        assert(stash)

PERL_CALLCONV SV *
Perl_gv_const_sv(pTHX_ GV *gv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GV_CONST_SV            \
        assert(gv)

PERL_CALLCONV void
Perl_gv_dump(pTHX_ GV *gv);
#define PERL_ARGS_ASSERT_GV_DUMP

PERL_CALLCONV void
Perl_gv_efullname4(pTHX_ SV *sv, const GV *gv, const char *prefix, bool keepmain);
#define PERL_ARGS_ASSERT_GV_EFULLNAME4          \
        assert(sv); assert(gv)

PERL_CALLCONV GV *
Perl_gv_fetchfile(pTHX_ const char *name);
#define PERL_ARGS_ASSERT_GV_FETCHFILE           \
        assert(name)

PERL_CALLCONV GV *
Perl_gv_fetchfile_flags(pTHX_ const char * const name, const STRLEN len, const U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHFILE_FLAGS     \
        assert(name)

/* PERL_CALLCONV GV *
gv_fetchmeth(pTHX_ HV *stash, const char *name, STRLEN len, I32 level); */

/* PERL_CALLCONV GV *
gv_fetchmeth_autoload(pTHX_ HV *stash, const char *name, STRLEN len, I32 level); */

PERL_CALLCONV GV *
Perl_gv_fetchmeth_pv(pTHX_ HV *stash, const char *name, I32 level, U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHMETH_PV        \
        assert(name)

PERL_CALLCONV GV *
Perl_gv_fetchmeth_pv_autoload(pTHX_ HV *stash, const char *name, I32 level, U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHMETH_PV_AUTOLOAD \
        assert(name)

PERL_CALLCONV GV *
Perl_gv_fetchmeth_pvn(pTHX_ HV *stash, const char *name, STRLEN len, I32 level, U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHMETH_PVN       \
        assert(name)

PERL_CALLCONV GV *
Perl_gv_fetchmeth_pvn_autoload(pTHX_ HV *stash, const char *name, STRLEN len, I32 level, U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHMETH_PVN_AUTOLOAD \
        assert(name)

PERL_CALLCONV GV *
Perl_gv_fetchmeth_sv(pTHX_ HV *stash, SV *namesv, I32 level, U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHMETH_SV        \
        assert(namesv)

PERL_CALLCONV GV *
Perl_gv_fetchmeth_sv_autoload(pTHX_ HV *stash, SV *namesv, I32 level, U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHMETH_SV_AUTOLOAD \
        assert(namesv)

PERL_CALLCONV GV *
Perl_gv_fetchmethod_autoload(pTHX_ HV *stash, const char *name, I32 autoload);
#define PERL_ARGS_ASSERT_GV_FETCHMETHOD_AUTOLOAD \
        assert(stash); assert(name)

PERL_CALLCONV GV *
Perl_gv_fetchmethod_pv_flags(pTHX_ HV *stash, const char *name, U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHMETHOD_PV_FLAGS \
        assert(stash); assert(name)

PERL_CALLCONV GV *
Perl_gv_fetchmethod_pvn_flags(pTHX_ HV *stash, const char *name, const STRLEN len, U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHMETHOD_PVN_FLAGS \
        assert(stash); assert(name)

PERL_CALLCONV GV *
Perl_gv_fetchmethod_sv_flags(pTHX_ HV *stash, SV *namesv, U32 flags);
#define PERL_ARGS_ASSERT_GV_FETCHMETHOD_SV_FLAGS \
        assert(stash); assert(namesv)

PERL_CALLCONV GV *
Perl_gv_fetchpv(pTHX_ const char *nambeg, I32 flags, const svtype sv_type);
#define PERL_ARGS_ASSERT_GV_FETCHPV             \
        assert(nambeg)

PERL_CALLCONV GV *
Perl_gv_fetchpvn_flags(pTHX_ const char *name, STRLEN len, I32 flags, const svtype sv_type);
#define PERL_ARGS_ASSERT_GV_FETCHPVN_FLAGS      \
        assert(name)

PERL_CALLCONV GV *
Perl_gv_fetchsv(pTHX_ SV *name, I32 flags, const svtype sv_type);
#define PERL_ARGS_ASSERT_GV_FETCHSV             \
        assert(name)

PERL_CALLCONV void
Perl_gv_fullname4(pTHX_ SV *sv, const GV *gv, const char *prefix, bool keepmain);
#define PERL_ARGS_ASSERT_GV_FULLNAME4           \
        assert(sv); assert(gv)

PERL_CALLCONV CV *
Perl_gv_handler(pTHX_ HV *stash, I32 id)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_GV_HANDLER

/* PERL_CALLCONV void
gv_init(pTHX_ GV *gv, HV *stash, const char *name, STRLEN len, int multi); */

PERL_CALLCONV void
Perl_gv_init_pv(pTHX_ GV *gv, HV *stash, const char *name, U32 flags);
#define PERL_ARGS_ASSERT_GV_INIT_PV             \
        assert(gv); assert(name)

PERL_CALLCONV void
Perl_gv_init_pvn(pTHX_ GV *gv, HV *stash, const char *name, STRLEN len, U32 flags);
#define PERL_ARGS_ASSERT_GV_INIT_PVN            \
        assert(gv); assert(name)

PERL_CALLCONV void
Perl_gv_init_sv(pTHX_ GV *gv, HV *stash, SV *namesv, U32 flags);
#define PERL_ARGS_ASSERT_GV_INIT_SV             \
        assert(gv); assert(namesv)

PERL_CALLCONV void
Perl_gv_name_set(pTHX_ GV *gv, const char *name, U32 len, U32 flags);
#define PERL_ARGS_ASSERT_GV_NAME_SET            \
        assert(gv); assert(name)

PERL_CALLCONV GV *
Perl_gv_override(pTHX_ const char * const name, const STRLEN len);
#define PERL_ARGS_ASSERT_GV_OVERRIDE            \
        assert(name)

PERL_CALLCONV void
Perl_gv_setref(pTHX_ SV * const dsv, SV * const ssv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_GV_SETREF              \
        assert(dsv); assert(ssv)

PERL_CALLCONV HV *
Perl_gv_stashpv(pTHX_ const char *name, I32 flags);
#define PERL_ARGS_ASSERT_GV_STASHPV             \
        assert(name)

PERL_CALLCONV HV *
Perl_gv_stashpvn(pTHX_ const char *name, U32 namelen, I32 flags);
#define PERL_ARGS_ASSERT_GV_STASHPVN            \
        assert(name)

PERL_CALLCONV HV *
Perl_gv_stashsv(pTHX_ SV *sv, I32 flags);
#define PERL_ARGS_ASSERT_GV_STASHSV             \
        assert(sv)

PERL_CALLCONV void
Perl_gv_try_downgrade(pTHX_ GV *gv);
#define PERL_ARGS_ASSERT_GV_TRY_DOWNGRADE       \
        assert(gv)

PERL_CALLCONV struct xpvhv_aux *
Perl_hv_auxalloc(pTHX_ HV *hv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_HV_AUXALLOC            \
        assert(hv)

PERL_CALLCONV AV **
Perl_hv_backreferences_p(pTHX_ HV *hv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_HV_BACKREFERENCES_P    \
        assert(hv)

PERL_CALLCONV SV *
Perl_hv_bucket_ratio(pTHX_ HV *hv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_BUCKET_RATIO        \
        assert(hv)

PERL_CALLCONV void
Perl_hv_clear(pTHX_ HV *hv);
#define PERL_ARGS_ASSERT_HV_CLEAR

PERL_CALLCONV void
Perl_hv_clear_placeholders(pTHX_ HV *hv);
#define PERL_ARGS_ASSERT_HV_CLEAR_PLACEHOLDERS  \
        assert(hv)

PERL_CALLCONV void *
Perl_hv_common(pTHX_ HV *hv, SV *keysv, const char *key, STRLEN klen, int flags, int action, SV *val, U32 hash);
#define PERL_ARGS_ASSERT_HV_COMMON

PERL_CALLCONV void *
Perl_hv_common_key_len(pTHX_ HV *hv, const char *key, I32 klen_i32, const int action, SV *val, const U32 hash);
#define PERL_ARGS_ASSERT_HV_COMMON_KEY_LEN      \
        assert(key)

PERL_CALLCONV HV *
Perl_hv_copy_hints_hv(pTHX_ HV * const ohv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_COPY_HINTS_HV

PERL_CALLCONV void
Perl_hv_delayfree_ent(pTHX_ HV *notused, HE *entry);
#define PERL_ARGS_ASSERT_HV_DELAYFREE_ENT

PERL_CALLCONV void
Perl_hv_dump(pTHX_ HV *hv);
#define PERL_ARGS_ASSERT_HV_DUMP

PERL_CALLCONV HE **
Perl_hv_eiter_p(pTHX_ HV *hv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_EITER_P             \
        assert(hv)

PERL_CALLCONV void
Perl_hv_eiter_set(pTHX_ HV *hv, HE *eiter);
#define PERL_ARGS_ASSERT_HV_EITER_SET           \
        assert(hv)

PERL_CALLCONV void
Perl_hv_ename_add(pTHX_ HV *hv, const char *name, U32 len, U32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_HV_ENAME_ADD           \
        assert(hv); assert(name)

PERL_CALLCONV void
Perl_hv_ename_delete(pTHX_ HV *hv, const char *name, U32 len, U32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_HV_ENAME_DELETE        \
        assert(hv); assert(name)

PERL_CALLCONV STRLEN
Perl_hv_fill(pTHX_ HV * const hv);
#define PERL_ARGS_ASSERT_HV_FILL                \
        assert(hv)

PERL_CALLCONV void
Perl_hv_free_ent(pTHX_ HV *notused, HE *entry);
#define PERL_ARGS_ASSERT_HV_FREE_ENT

PERL_CALLCONV I32
Perl_hv_iterinit(pTHX_ HV *hv);
#define PERL_ARGS_ASSERT_HV_ITERINIT            \
        assert(hv)

PERL_CALLCONV char *
Perl_hv_iterkey(pTHX_ HE *entry, I32 *retlen)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_ITERKEY             \
        assert(entry); assert(retlen)

PERL_CALLCONV SV *
Perl_hv_iterkeysv(pTHX_ HE *entry)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_ITERKEYSV           \
        assert(entry)

PERL_CALLCONV HE *
Perl_hv_iternext_flags(pTHX_ HV *hv, I32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_ITERNEXT_FLAGS      \
        assert(hv)

PERL_CALLCONV SV *
Perl_hv_iternextsv(pTHX_ HV *hv, char **key, I32 *retlen)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_ITERNEXTSV          \
        assert(hv); assert(key); assert(retlen)

PERL_CALLCONV SV *
Perl_hv_iterval(pTHX_ HV *hv, HE *entry)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_ITERVAL             \
        assert(hv); assert(entry)

PERL_CALLCONV void
Perl_hv_ksplit(pTHX_ HV *hv, IV newmax);
#define PERL_ARGS_ASSERT_HV_KSPLIT              \
        assert(hv)

PERL_CALLCONV void
Perl_hv_name_set(pTHX_ HV *hv, const char *name, U32 len, U32 flags);
#define PERL_ARGS_ASSERT_HV_NAME_SET            \
        assert(hv)

PERL_CALLCONV I32
Perl_hv_placeholders_get(pTHX_ const HV *hv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_PLACEHOLDERS_GET    \
        assert(hv)

PERL_CALLCONV SSize_t *
Perl_hv_placeholders_p(pTHX_ HV *hv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_PLACEHOLDERS_P      \
        assert(hv)

PERL_CALLCONV void
Perl_hv_placeholders_set(pTHX_ HV *hv, I32 ph);
#define PERL_ARGS_ASSERT_HV_PLACEHOLDERS_SET    \
        assert(hv)

PERL_CALLCONV void
Perl_hv_pushkv(pTHX_ HV *hv, U32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_HV_PUSHKV              \
        assert(hv)

PERL_CALLCONV void
Perl_hv_rand_set(pTHX_ HV *hv, U32 new_xhv_rand);
#define PERL_ARGS_ASSERT_HV_RAND_SET            \
        assert(hv)

PERL_CALLCONV I32 *
Perl_hv_riter_p(pTHX_ HV *hv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_RITER_P             \
        assert(hv)

PERL_CALLCONV void
Perl_hv_riter_set(pTHX_ HV *hv, I32 riter);
#define PERL_ARGS_ASSERT_HV_RITER_SET           \
        assert(hv)

PERL_CALLCONV SV *
Perl_hv_scalar(pTHX_ HV *hv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_HV_SCALAR              \
        assert(hv)

/* PERL_CALLCONV SV **
hv_stores(pTHX_ HV *hv, const char * const key, SV *val); */

/* PERL_CALLCONV void
hv_undef(pTHX_ HV *hv); */

PERL_CALLCONV void
Perl_hv_undef_flags(pTHX_ HV *hv, U32 flags);
#define PERL_ARGS_ASSERT_HV_UNDEF_FLAGS

/* PERL_CALLCONV I32
ibcmp(pTHX_ const char *a, const char *b, I32 len)
        __attribute__warn_unused_result__
        __attribute__pure__; */

/* PERL_CALLCONV I32
ibcmp_locale(pTHX_ const char *a, const char *b, I32 len)
        __attribute__warn_unused_result__
        __attribute__pure__; */

/* PERL_CALLCONV I32
ibcmp_utf8(pTHX_ const char *s1, char **pe1, UV l1, bool u1, const char *s2, char **pe2, UV l2, bool u2); */

PERL_CALLCONV STRLEN
Perl_infix_plugin_standard(pTHX_ char *operator_ptr, STRLEN operator_len, struct Perl_custom_infix **def);
#define PERL_ARGS_ASSERT_INFIX_PLUGIN_STANDARD  \
        assert(operator_ptr); assert(def)

PERL_CALLCONV void
Perl_init_argv_symbols(pTHX_ int argc, char **argv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_INIT_ARGV_SYMBOLS      \
        assert(argv)

PERL_CALLCONV void
Perl_init_constants(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_INIT_CONSTANTS

PERL_CALLCONV void
Perl_init_dbargs(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_INIT_DBARGS

PERL_CALLCONV void
Perl_init_debugger(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_INIT_DEBUGGER

PERL_CALLCONV int
Perl_init_i18nl10n(pTHX_ int printwarn);
#define PERL_ARGS_ASSERT_INIT_I18NL10N

PERL_CALLCONV void
Perl_init_named_cv(pTHX_ CV *cv, OP *nameop);
#define PERL_ARGS_ASSERT_INIT_NAMED_CV          \
        assert(cv); assert(nameop)

PERL_CALLCONV void
Perl_init_stacks(pTHX);
#define PERL_ARGS_ASSERT_INIT_STACKS

PERL_CALLCONV void
Perl_init_tm(pTHX_ struct tm *ptm);
#define PERL_ARGS_ASSERT_INIT_TM                \
        assert(ptm)

PERL_CALLCONV void
Perl_init_uniprops(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_INIT_UNIPROPS

PERL_CALLCONV U32
Perl_intro_my(pTHX);
#define PERL_ARGS_ASSERT_INTRO_MY

PERL_CALLCONV OP *
Perl_invert(pTHX_ OP *cmd)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_INVERT

PERL_CALLCONV void
Perl_invmap_dump(pTHX_ SV *invlist, UV *map)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_INVMAP_DUMP            \
        assert(invlist); assert(map)

PERL_CALLCONV bool
Perl_io_close(pTHX_ IO *io, GV *gv, bool is_explicit, bool warn_on_fail)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_IO_CLOSE               \
        assert(io)

/* PERL_CALLCONV bool
is_ascii_string(const U8 * const s, STRLEN len)
        __attribute__warn_unused_result__
        __attribute__pure__; */

/* PERL_CALLCONV bool
is_c9strict_utf8_string(const U8 *s, STRLEN len)
        __attribute__warn_unused_result__; */

/* PERL_CALLCONV bool
is_c9strict_utf8_string_loc(const U8 *s, STRLEN len, const U8 **ep); */

/* PERL_CALLCONV bool
is_invariant_string(const U8 * const s, STRLEN len)
        __attribute__warn_unused_result__
        __attribute__pure__; */

PERL_CALLCONV I32
Perl_is_lvalue_sub(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_IS_LVALUE_SUB

/* PERL_CALLCONV bool
is_strict_utf8_string(const U8 *s, STRLEN len)
        __attribute__warn_unused_result__; */

/* PERL_CALLCONV bool
is_strict_utf8_string_loc(const U8 *s, STRLEN len, const U8 **ep); */

PERL_CALLCONV Size_t
Perl_is_utf8_FF_helper_(const U8 * const s0, const U8 * const e, const bool require_partial)
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_IS_UTF8_FF_HELPER_     \
        assert(s0); assert(e)

PERL_CALLCONV STRLEN
Perl_is_utf8_char_helper_(const U8 * const s, const U8 *e, const U32 flags)
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_IS_UTF8_CHAR_HELPER_   \
        assert(s); assert(e)

/* PERL_CALLCONV bool
is_utf8_fixed_width_buf_flags(const U8 * const s, STRLEN len, const U32 flags); */

/* PERL_CALLCONV bool
is_utf8_fixed_width_buf_loc_flags(const U8 * const s, STRLEN len, const U8 **ep, const U32 flags); */

/* PERL_CALLCONV bool
is_utf8_invariant_string(const U8 * const s, STRLEN len)
        __attribute__warn_unused_result__; */

/* PERL_CALLCONV bool
is_utf8_string(const U8 *s, STRLEN len)
        __attribute__warn_unused_result__; */

/* PERL_CALLCONV bool
is_utf8_string_loc_flags(const U8 *s, STRLEN len, const U8 **ep, const U32 flags); */

/* PERL_CALLCONV bool
is_utf8_valid_partial_char(const U8 * const s0, const U8 * const e)
        __attribute__warn_unused_result__
        __attribute__pure__; */

PERL_CALLCONV bool
Perl_isinfnan(NV nv)
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_ISINFNAN

PERL_CALLCONV bool
Perl_isinfnansv(pTHX_ SV *sv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_ISINFNANSV             \
        assert(sv)

PERL_CALLCONV OP *
Perl_jmaybe(pTHX_ OP *o)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_JMAYBE                 \
        assert(o)

PERL_CALLCONV I32
Perl_keyword(pTHX_ const char *name, I32 len, bool all_keywords)
        __attribute__warn_unused_result__
        __attribute__pure__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_KEYWORD                \
        assert(name)

PERL_CALLCONV int
Perl_keyword_plugin_standard(pTHX_ char *keyword_ptr, STRLEN keyword_len, OP **op_ptr);
#define PERL_ARGS_ASSERT_KEYWORD_PLUGIN_STANDARD \
        assert(keyword_ptr); assert(op_ptr)

PERL_CALLCONV void
Perl_leave_adjust_stacks(pTHX_ SV **from_sp, SV **to_sp, U8 gimme, int filter);
#define PERL_ARGS_ASSERT_LEAVE_ADJUST_STACKS    \
        assert(from_sp); assert(to_sp)

PERL_CALLCONV void
Perl_leave_scope(pTHX_ I32 base);
#define PERL_ARGS_ASSERT_LEAVE_SCOPE

PERL_CALLCONV bool
Perl_lex_bufutf8(pTHX);
#define PERL_ARGS_ASSERT_LEX_BUFUTF8

PERL_CALLCONV void
Perl_lex_discard_to(pTHX_ char *ptr);
#define PERL_ARGS_ASSERT_LEX_DISCARD_TO         \
        assert(ptr)

PERL_CALLCONV char *
Perl_lex_grow_linestr(pTHX_ STRLEN len);
#define PERL_ARGS_ASSERT_LEX_GROW_LINESTR

PERL_CALLCONV bool
Perl_lex_next_chunk(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_LEX_NEXT_CHUNK

PERL_CALLCONV I32
Perl_lex_peek_unichar(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_LEX_PEEK_UNICHAR

PERL_CALLCONV void
Perl_lex_read_space(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_LEX_READ_SPACE

PERL_CALLCONV void
Perl_lex_read_to(pTHX_ char *ptr);
#define PERL_ARGS_ASSERT_LEX_READ_TO            \
        assert(ptr)

PERL_CALLCONV I32
Perl_lex_read_unichar(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_LEX_READ_UNICHAR

PERL_CALLCONV void
Perl_lex_start(pTHX_ SV *line, PerlIO *rsfp, U32 flags);
#define PERL_ARGS_ASSERT_LEX_START

PERL_CALLCONV void
Perl_lex_stuff_pv(pTHX_ const char *pv, U32 flags);
#define PERL_ARGS_ASSERT_LEX_STUFF_PV           \
        assert(pv)

PERL_CALLCONV void
Perl_lex_stuff_pvn(pTHX_ const char *pv, STRLEN len, U32 flags);
#define PERL_ARGS_ASSERT_LEX_STUFF_PVN          \
        assert(pv)

PERL_CALLCONV void
Perl_lex_stuff_sv(pTHX_ SV *sv, U32 flags);
#define PERL_ARGS_ASSERT_LEX_STUFF_SV           \
        assert(sv)

PERL_CALLCONV void
Perl_lex_unstuff(pTHX_ char *ptr);
#define PERL_ARGS_ASSERT_LEX_UNSTUFF            \
        assert(ptr)

PERL_CALLCONV OP *
Perl_list(pTHX_ OP *o)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_LIST

PERL_CALLCONV HV *
Perl_load_charnames(pTHX_ SV *char_name, const char *context, const STRLEN context_len, const char **error_msg)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_LOAD_CHARNAMES         \
        assert(char_name); assert(context); assert(error_msg)

PERL_CALLCONV void
Perl_load_module(pTHX_ U32 flags, SV *name, SV *ver, ...);
#define PERL_ARGS_ASSERT_LOAD_MODULE            \
        assert(name)

PERL_CALLCONV_NO_RET void
Perl_locale_panic(const char *msg, const char *file_name, const line_t line, const int errnum)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_LOCALE_PANIC           \
        assert(msg); assert(file_name)

PERL_CALLCONV OP *
Perl_localize(pTHX_ OP *o, I32 lex)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_LOCALIZE               \
        assert(o)

PERL_CALLCONV I32
Perl_looks_like_number(pTHX_ SV * const sv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_LOOKS_LIKE_NUMBER      \
        assert(sv)

PERL_CALLCONV int
Perl_magic_clear_all_env(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEAR_ALL_ENV    \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_cleararylen_p(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEARARYLEN_P    \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_clearenv(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEARENV         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_clearhint(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEARHINT        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_clearhints(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEARHINTS       \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_clearhook(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEARHOOK        \
        assert(mg)

PERL_CALLCONV int
Perl_magic_clearhookall(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEARHOOKALL     \
        assert(mg)

PERL_CALLCONV int
Perl_magic_clearisa(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEARISA         \
        assert(mg)

PERL_CALLCONV int
Perl_magic_clearpack(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEARPACK        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_clearsig(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_CLEARSIG         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_copycallchecker(pTHX_ SV *sv, MAGIC *mg, SV *nsv, const char *name, I32 namlen)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_COPYCALLCHECKER  \
        assert(sv); assert(mg); assert(nsv)

PERL_CALLCONV void
Perl_magic_dump(pTHX_ const MAGIC *mg);
#define PERL_ARGS_ASSERT_MAGIC_DUMP

PERL_CALLCONV int
Perl_magic_existspack(pTHX_ SV *sv, const MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_EXISTSPACK       \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_freearylen_p(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_FREEARYLEN_P     \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_freedestruct(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_FREEDESTRUCT     \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_freemglob(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_FREEMGLOB        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_freeovrld(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_FREEOVRLD        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_freeutf8(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_FREEUTF8         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_get(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GET              \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getarylen(pTHX_ SV *sv, const MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETARYLEN        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getdebugvar(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETDEBUGVAR      \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getdefelem(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETDEFELEM       \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getnkeys(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETNKEYS         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getpack(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETPACK          \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getpos(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETPOS           \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getsig(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETSIG           \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getsubstr(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETSUBSTR        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_gettaint(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETTAINT         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getuvar(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETUVAR          \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_getvec(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_GETVEC           \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_killbackrefs(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_KILLBACKREFS     \
        assert(sv); assert(mg)

PERL_CALLCONV SV *
Perl_magic_methcall(pTHX_ SV *sv, const MAGIC *mg, SV *meth, U32 flags, U32 argc, ...)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_METHCALL         \
        assert(sv); assert(mg); assert(meth)

PERL_CALLCONV int
Perl_magic_nextpack(pTHX_ SV *sv, MAGIC *mg, SV *key)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_NEXTPACK         \
        assert(sv); assert(mg); assert(key)

PERL_CALLCONV U32
Perl_magic_regdata_cnt(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_REGDATA_CNT      \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_regdatum_get(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_REGDATUM_GET     \
        assert(sv); assert(mg)

PERL_CALLCONV SV *
Perl_magic_scalarpack(pTHX_ HV *hv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SCALARPACK       \
        assert(hv); assert(mg)

PERL_CALLCONV int
Perl_magic_set(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SET              \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_set_all_env(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SET_ALL_ENV      \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setarylen(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETARYLEN        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setdbline(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETDBLINE        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setdebugvar(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETDEBUGVAR      \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setdefelem(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETDEFELEM       \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setenv(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETENV           \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_sethint(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETHINT          \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_sethook(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETHOOK          \
        assert(mg)

PERL_CALLCONV int
Perl_magic_sethookall(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETHOOKALL       \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setisa(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETISA           \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setlvref(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETLVREF         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setmglob(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETMGLOB         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setnkeys(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETNKEYS         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setnonelem(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETNONELEM       \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setpack(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETPACK          \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setpos(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETPOS           \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setregexp(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETREGEXP        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setsig(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETSIG           \
        assert(mg)

PERL_CALLCONV int
Perl_magic_setsigall(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETSIGALL        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setsubstr(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETSUBSTR        \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_settaint(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETTAINT         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setutf8(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETUTF8          \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setuvar(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETUVAR          \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setvec(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SETVEC           \
        assert(sv); assert(mg)

PERL_CALLCONV U32
Perl_magic_sizepack(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_SIZEPACK         \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_wipepack(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MAGIC_WIPEPACK         \
        assert(sv); assert(mg)

PERL_CALLCONV Malloc_t
Perl_malloc(MEM_SIZE nbytes)
        __attribute__malloc__
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_MALLOC

PERL_CALLCONV I32 *
Perl_markstack_grow(pTHX);
#define PERL_ARGS_ASSERT_MARKSTACK_GROW

PERL_CALLCONV int
Perl_mbtowc_(pTHX_ const wchar_t *pwc, const char *s, const Size_t len);
#define PERL_ARGS_ASSERT_MBTOWC_

PERL_CALLCONV SV *
Perl_mess(pTHX_ const char *pat, ...)
        __attribute__format__(__printf__,pTHX_1,pTHX_2);
#define PERL_ARGS_ASSERT_MESS                   \
        assert(pat)

PERL_CALLCONV SV *
Perl_mess_sv(pTHX_ SV *basemsg, bool consume);
#define PERL_ARGS_ASSERT_MESS_SV                \
        assert(basemsg)

PERL_CALLCONV Free_t
Perl_mfree(Malloc_t where);
#define PERL_ARGS_ASSERT_MFREE

PERL_CALLCONV int
Perl_mg_clear(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_MG_CLEAR               \
        assert(sv)

PERL_CALLCONV int
Perl_mg_copy(pTHX_ SV *sv, SV *nsv, const char *key, I32 klen);
#define PERL_ARGS_ASSERT_MG_COPY                \
        assert(sv); assert(nsv)

PERL_CALLCONV MAGIC *
Perl_mg_find(const SV *sv, int type)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_MG_FIND

PERL_CALLCONV MAGIC *
Perl_mg_find_mglob(pTHX_ SV *sv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_MG_FIND_MGLOB          \
        assert(sv)

PERL_CALLCONV MAGIC *
Perl_mg_findext(const SV *sv, int type, const MGVTBL *vtbl)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_MG_FINDEXT

PERL_CALLCONV int
Perl_mg_free(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_MG_FREE                \
        assert(sv)

PERL_CALLCONV void
Perl_mg_free_type(pTHX_ SV *sv, int how);
#define PERL_ARGS_ASSERT_MG_FREE_TYPE           \
        assert(sv)

PERL_CALLCONV void
Perl_mg_freeext(pTHX_ SV *sv, int how, const MGVTBL *vtbl);
#define PERL_ARGS_ASSERT_MG_FREEEXT             \
        assert(sv)

PERL_CALLCONV int
Perl_mg_get(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_MG_GET                 \
        assert(sv)

PERL_CALLCONV void
Perl_mg_localize(pTHX_ SV *sv, SV *nsv, bool setmagic)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MG_LOCALIZE            \
        assert(sv); assert(nsv)

PERL_CALLCONV void
Perl_mg_magical(SV *sv);
#define PERL_ARGS_ASSERT_MG_MAGICAL             \
        assert(sv)

PERL_CALLCONV int
Perl_mg_set(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_MG_SET                 \
        assert(sv)

PERL_CALLCONV I32
Perl_mg_size(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_MG_SIZE                \
        assert(sv)

PERL_CALLCONV void
Perl_mini_mktime(struct tm *ptm);
#define PERL_ARGS_ASSERT_MINI_MKTIME            \
        assert(ptm)

PERL_CALLCONV int
Perl_mode_from_discipline(pTHX_ const char *s, STRLEN len)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MODE_FROM_DISCIPLINE

PERL_CALLCONV void *
Perl_more_bodies(pTHX_ const svtype sv_type, const size_t body_size, const size_t arena_size);
#define PERL_ARGS_ASSERT_MORE_BODIES

PERL_CALLCONV const char *
Perl_moreswitches(pTHX_ const char *s);
#define PERL_ARGS_ASSERT_MORESWITCHES           \
        assert(s)

PERL_CALLCONV void
Perl_mortal_destructor_sv(pTHX_ SV *coderef, SV *args);
#define PERL_ARGS_ASSERT_MORTAL_DESTRUCTOR_SV   \
        assert(coderef)

PERL_CALLCONV void
Perl_mortal_svfunc_x(pTHX_ SVFUNC_t f, SV *p);
#define PERL_ARGS_ASSERT_MORTAL_SVFUNC_X

PERL_CALLCONV const struct mro_alg *
Perl_mro_get_from_name(pTHX_ SV *name);
#define PERL_ARGS_ASSERT_MRO_GET_FROM_NAME      \
        assert(name)

PERL_CALLCONV AV *
Perl_mro_get_linear_isa(pTHX_ HV *stash);
#define PERL_ARGS_ASSERT_MRO_GET_LINEAR_ISA     \
        assert(stash)

PERL_CALLCONV SV *
Perl_mro_get_private_data(pTHX_ struct mro_meta * const smeta, const struct mro_alg * const which);
#define PERL_ARGS_ASSERT_MRO_GET_PRIVATE_DATA   \
        assert(smeta); assert(which)

PERL_CALLCONV void
Perl_mro_isa_changed_in(pTHX_ HV *stash)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MRO_ISA_CHANGED_IN     \
        assert(stash)

PERL_CALLCONV struct mro_meta *
Perl_mro_meta_init(pTHX_ HV *stash);
#define PERL_ARGS_ASSERT_MRO_META_INIT          \
        assert(stash)

PERL_CALLCONV void
Perl_mro_method_changed_in(pTHX_ HV *stash);
#define PERL_ARGS_ASSERT_MRO_METHOD_CHANGED_IN  \
        assert(stash)

PERL_CALLCONV void
Perl_mro_package_moved(pTHX_ HV * const stash, HV * const oldstash, const GV * const gv, U32 flags);
#define PERL_ARGS_ASSERT_MRO_PACKAGE_MOVED      \
        assert(gv)

PERL_CALLCONV void
Perl_mro_register(pTHX_ const struct mro_alg *mro);
#define PERL_ARGS_ASSERT_MRO_REGISTER           \
        assert(mro)

PERL_CALLCONV void
Perl_mro_set_mro(pTHX_ struct mro_meta * const meta, SV * const name);
#define PERL_ARGS_ASSERT_MRO_SET_MRO            \
        assert(meta); assert(name)

PERL_CALLCONV SV *
Perl_mro_set_private_data(pTHX_ struct mro_meta * const smeta, const struct mro_alg * const which, SV * const data);
#define PERL_ARGS_ASSERT_MRO_SET_PRIVATE_DATA   \
        assert(smeta); assert(which); assert(data)

PERL_CALLCONV SV *
Perl_multiconcat_stringify(pTHX_ const OP *o);
#define PERL_ARGS_ASSERT_MULTICONCAT_STRINGIFY  \
        assert(o)

PERL_CALLCONV SV *
Perl_multideref_stringify(pTHX_ const OP *o, CV *cv);
#define PERL_ARGS_ASSERT_MULTIDEREF_STRINGIFY   \
        assert(o)

PERL_CALLCONV NV
Perl_my_atof(pTHX_ const char *s);
#define PERL_ARGS_ASSERT_MY_ATOF                \
        assert(s)

PERL_CALLCONV char *
Perl_my_atof2(pTHX_ const char *orig, NV *value);
#define PERL_ARGS_ASSERT_MY_ATOF2               \
        assert(orig); assert(value)

PERL_CALLCONV char *
Perl_my_atof3(pTHX_ const char *orig, NV *value, const STRLEN len);
#define PERL_ARGS_ASSERT_MY_ATOF3               \
        assert(orig); assert(value)

PERL_CALLCONV OP *
Perl_my_attrs(pTHX_ OP *o, OP *attrs)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MY_ATTRS               \
        assert(o)

PERL_CALLCONV void
Perl_my_clearenv(pTHX);
#define PERL_ARGS_ASSERT_MY_CLEARENV

PERL_CALLCONV int
Perl_my_dirfd(DIR *dir);
#define PERL_ARGS_ASSERT_MY_DIRFD

PERL_CALLCONV_NO_RET void
Perl_my_exit(pTHX_ U32 status)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_MY_EXIT

PERL_CALLCONV_NO_RET void
Perl_my_failure_exit(pTHX)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_MY_FAILURE_EXIT

PERL_CALLCONV I32
Perl_my_fflush_all(pTHX);
#define PERL_ARGS_ASSERT_MY_FFLUSH_ALL

PERL_CALLCONV Pid_t
Perl_my_fork(void);
#define PERL_ARGS_ASSERT_MY_FORK

/* PERL_CALLCONV I32
my_lstat(pTHX); */

PERL_CALLCONV I32
Perl_my_lstat_flags(pTHX_ const U32 flags);
#define PERL_ARGS_ASSERT_MY_LSTAT_FLAGS

PERL_CALLCONV int
Perl_my_mkostemp_cloexec(char *templte, int flags)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MY_MKOSTEMP_CLOEXEC    \
        assert(templte)

PERL_CALLCONV int
Perl_my_mkstemp_cloexec(char *templte)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MY_MKSTEMP_CLOEXEC     \
        assert(templte)

PERL_CALLCONV PerlIO *
Perl_my_popen_list(pTHX_ const char *mode, int n, SV **args);
#define PERL_ARGS_ASSERT_MY_POPEN_LIST          \
        assert(mode); assert(args)

PERL_CALLCONV void
Perl_my_setenv(pTHX_ const char *nam, const char *val);
#define PERL_ARGS_ASSERT_MY_SETENV

PERL_CALLCONV int
Perl_my_snprintf(char *buffer, const Size_t len, const char *format, ...)
        __attribute__format__(__printf__,3,4);
#define PERL_ARGS_ASSERT_MY_SNPRINTF            \
        assert(buffer); assert(format)

PERL_CALLCONV int
Perl_my_socketpair(int family, int type, int protocol, int fd[2]);
#define PERL_ARGS_ASSERT_MY_SOCKETPAIR

/* PERL_CALLCONV I32
my_stat(pTHX); */

PERL_CALLCONV I32
Perl_my_stat_flags(pTHX_ const U32 flags);
#define PERL_ARGS_ASSERT_MY_STAT_FLAGS

PERL_CALLCONV const char *
Perl_my_strerror(pTHX_ const int errnum, utf8ness_t *utf8ness)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MY_STRERROR            \
        assert(utf8ness)

PERL_CALLCONV char *
Perl_my_strftime(pTHX_ const char *fmt, int sec, int min, int hour, int mday, int mon, int year, int wday, int yday, int isdst)
        __attribute__format__(__strftime__,pTHX_1,0);
#define PERL_ARGS_ASSERT_MY_STRFTIME            \
        assert(fmt)

PERL_CALLCONV char *
Perl_my_strftime8_temp(pTHX_ const char *fmt, int sec, int min, int hour, int mday, int mon, int year, int wday, int yday, int isdst, utf8ness_t *utf8ness)
        __attribute__format__(__strftime__,pTHX_1,0);
#define PERL_ARGS_ASSERT_MY_STRFTIME8_TEMP      \
        assert(fmt)

PERL_CALLCONV NV
Perl_my_strtod(const char * const s, char **e)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_MY_STRTOD              \
        assert(s)

PERL_CALLCONV void
Perl_my_unexec(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_MY_UNEXEC

PERL_CALLCONV int
Perl_my_vsnprintf(char *buffer, const Size_t len, const char *format, va_list ap);
#define PERL_ARGS_ASSERT_MY_VSNPRINTF           \
        assert(buffer); assert(format)

PERL_CALLCONV OP *
Perl_newANONATTRSUB(pTHX_ I32 floor, OP *proto, OP *attrs, OP *block);
#define PERL_ARGS_ASSERT_NEWANONATTRSUB

PERL_CALLCONV OP *
Perl_newANONHASH(pTHX_ OP *o)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWANONHASH

PERL_CALLCONV OP *
Perl_newANONLIST(pTHX_ OP *o)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWANONLIST

PERL_CALLCONV OP *
Perl_newANONSUB(pTHX_ I32 floor, OP *proto, OP *block);
#define PERL_ARGS_ASSERT_NEWANONSUB

PERL_CALLCONV OP *
Perl_newARGDEFELEMOP(pTHX_ I32 flags, OP *expr, I32 argindex)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWARGDEFELEMOP        \
        assert(expr)

PERL_CALLCONV OP *
Perl_newASSIGNOP(pTHX_ I32 flags, OP *left, I32 optype, OP *right)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWASSIGNOP

/* PERL_CALLCONV CV *
newATTRSUB(pTHX_ I32 floor, OP *o, OP *proto, OP *attrs, OP *block); */

PERL_CALLCONV CV *
Perl_newATTRSUB_x(pTHX_ I32 floor, OP *o, OP *proto, OP *attrs, OP *block, bool o_is_gv);
#define PERL_ARGS_ASSERT_NEWATTRSUB_X

PERL_CALLCONV OP *
Perl_newAVREF(pTHX_ OP *o)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWAVREF               \
        assert(o)

/* PERL_CALLCONV AV *
newAV_alloc_x(pTHX_ SSize_t size)
        __attribute__warn_unused_result__; */

/* PERL_CALLCONV AV *
newAV_alloc_xz(pTHX_ SSize_t size)
        __attribute__warn_unused_result__; */

PERL_CALLCONV AV *
Perl_newAVav(pTHX_ AV *oav)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWAVAV

PERL_CALLCONV AV *
Perl_newAVhv(pTHX_ HV *ohv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWAVHV

PERL_CALLCONV OP *
Perl_newBINOP(pTHX_ I32 type, I32 flags, OP *first, OP *last)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWBINOP

PERL_CALLCONV OP *
Perl_newCONDOP(pTHX_ I32 flags, OP *first, OP *trueop, OP *falseop)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWCONDOP              \
        assert(first)

PERL_CALLCONV CV *
Perl_newCONSTSUB(pTHX_ HV *stash, const char *name, SV *sv);
#define PERL_ARGS_ASSERT_NEWCONSTSUB

PERL_CALLCONV CV *
Perl_newCONSTSUB_flags(pTHX_ HV *stash, const char *name, STRLEN len, U32 flags, SV *sv);
#define PERL_ARGS_ASSERT_NEWCONSTSUB_FLAGS

PERL_CALLCONV OP *
Perl_newCVREF(pTHX_ I32 flags, OP *o)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWCVREF

PERL_CALLCONV OP *
Perl_newDEFEROP(pTHX_ I32 flags, OP *block)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWDEFEROP             \
        assert(block)

PERL_CALLCONV OP *
Perl_newDEFSVOP(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWDEFSVOP

PERL_CALLCONV void
Perl_newFORM(pTHX_ I32 floor, OP *o, OP *block);
#define PERL_ARGS_ASSERT_NEWFORM

PERL_CALLCONV OP *
Perl_newFOROP(pTHX_ I32 flags, OP *sv, OP *expr, OP *block, OP *cont)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWFOROP               \
        assert(expr)

PERL_CALLCONV OP *
Perl_newGIVENOP(pTHX_ OP *cond, OP *block, PADOFFSET defsv_off)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWGIVENOP             \
        assert(cond); assert(block)

PERL_CALLCONV GP *
Perl_newGP(pTHX_ GV * const gv);
#define PERL_ARGS_ASSERT_NEWGP                  \
        assert(gv)

PERL_CALLCONV OP *
Perl_newGVOP(pTHX_ I32 type, I32 flags, GV *gv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWGVOP                \
        assert(gv)

PERL_CALLCONV OP *
Perl_newGVREF(pTHX_ I32 type, OP *o)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWGVREF

/* PERL_CALLCONV GV *
newGVgen(pTHX_ const char *pack); */

PERL_CALLCONV GV *
Perl_newGVgen_flags(pTHX_ const char *pack, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWGVGEN_FLAGS         \
        assert(pack)

PERL_CALLCONV OP *
Perl_newHVREF(pTHX_ OP *o)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWHVREF               \
        assert(o)

PERL_CALLCONV HV *
Perl_newHVhv(pTHX_ HV *hv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWHVHV

PERL_CALLCONV OP *
Perl_newLISTOP(pTHX_ I32 type, I32 flags, OP *first, OP *last)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWLISTOP

PERL_CALLCONV OP *
Perl_newLOGOP(pTHX_ I32 optype, I32 flags, OP *first, OP *other)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWLOGOP               \
        assert(first); assert(other)

PERL_CALLCONV OP *
Perl_newLOOPEX(pTHX_ I32 type, OP *label)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWLOOPEX              \
        assert(label)

PERL_CALLCONV OP *
Perl_newLOOPOP(pTHX_ I32 flags, I32 debuggable, OP *expr, OP *block)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWLOOPOP              \
        assert(expr)

PERL_CALLCONV OP *
Perl_newMETHOP(pTHX_ I32 type, I32 flags, OP *dynamic_meth)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWMETHOP              \
        assert(dynamic_meth)

PERL_CALLCONV OP *
Perl_newMETHOP_named(pTHX_ I32 type, I32 flags, SV * const_meth)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWMETHOP_NAMED        \
        assert(const_meth)

PERL_CALLCONV CV *
Perl_newMYSUB(pTHX_ I32 floor, OP *o, OP *proto, OP *attrs, OP *block);
#define PERL_ARGS_ASSERT_NEWMYSUB               \
        assert(o)

PERL_CALLCONV OP *
Perl_newNULLLIST(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWNULLLIST

PERL_CALLCONV OP *
Perl_newOP(pTHX_ I32 optype, I32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWOP

PERL_CALLCONV PADNAMELIST *
Perl_newPADNAMELIST(size_t max)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWPADNAMELIST

PERL_CALLCONV PADNAME *
Perl_newPADNAMEouter(PADNAME *outer)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWPADNAMEOUTER        \
        assert(outer)

PERL_CALLCONV PADNAME *
Perl_newPADNAMEpvn(const char *s, STRLEN len)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWPADNAMEPVN          \
        assert(s)

PERL_CALLCONV OP *
Perl_newPMOP(pTHX_ I32 type, I32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWPMOP

PERL_CALLCONV void
Perl_newPROG(pTHX_ OP *o);
#define PERL_ARGS_ASSERT_NEWPROG                \
        assert(o)

PERL_CALLCONV OP *
Perl_newPVOP(pTHX_ I32 type, I32 flags, char *pv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWPVOP

PERL_CALLCONV OP *
Perl_newRANGE(pTHX_ I32 flags, OP *left, OP *right)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWRANGE               \
        assert(left); assert(right)

PERL_CALLCONV SV *
Perl_newRV(pTHX_ SV * const sv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWRV                  \
        assert(sv)

PERL_CALLCONV OP *
Perl_newSLICEOP(pTHX_ I32 flags, OP *subscript, OP *listop)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSLICEOP

PERL_CALLCONV OP *
Perl_newSTATEOP(pTHX_ I32 flags, char *label, OP *o)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSTATEOP

PERL_CALLCONV CV *
Perl_newSTUB(pTHX_ GV *gv, bool fake)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_NEWSTUB                \
        assert(gv)

PERL_CALLCONV SV *
Perl_newSV(pTHX_ const STRLEN len)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSV

PERL_CALLCONV OP *
Perl_newSVOP(pTHX_ I32 type, I32 flags, SV *sv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVOP                \
        assert(sv)

PERL_CALLCONV OP *
Perl_newSVREF(pTHX_ OP *o)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVREF               \
        assert(o)

PERL_CALLCONV SV *
Perl_newSV_false(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSV_FALSE

PERL_CALLCONV SV *
Perl_newSV_true(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSV_TRUE

PERL_CALLCONV SV *
Perl_newSVavdefelem(pTHX_ AV *av, SSize_t ix, bool extendible)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_NEWSVAVDEFELEM         \
        assert(av)

PERL_CALLCONV SV *
Perl_newSVbool(pTHX_ const bool bool_val)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVBOOL

PERL_CALLCONV SV *
Perl_newSVhek(pTHX_ const HEK * const hek)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVHEK

PERL_CALLCONV SV *
Perl_newSVhek_mortal(pTHX_ const HEK * const hek)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVHEK_MORTAL

PERL_CALLCONV SV *
Perl_newSViv(pTHX_ const IV i)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVIV

PERL_CALLCONV SV *
Perl_newSVnv(pTHX_ const NV n)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVNV

PERL_CALLCONV SV *
Perl_newSVpv(pTHX_ const char * const s, const STRLEN len)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVPV

PERL_CALLCONV SV *
Perl_newSVpv_share(pTHX_ const char *s, U32 hash)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVPV_SHARE

PERL_CALLCONV SV *
Perl_newSVpvf(pTHX_ const char * const pat, ...)
        __attribute__warn_unused_result__
        __attribute__format__(__printf__,pTHX_1,pTHX_2);
#define PERL_ARGS_ASSERT_NEWSVPVF               \
        assert(pat)

PERL_CALLCONV SV *
Perl_newSVpvn(pTHX_ const char * const buffer, const STRLEN len)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVPVN

PERL_CALLCONV SV *
Perl_newSVpvn_flags(pTHX_ const char * const s, const STRLEN len, const U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVPVN_FLAGS

PERL_CALLCONV SV *
Perl_newSVpvn_share(pTHX_ const char *s, I32 len, U32 hash)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVPVN_SHARE

PERL_CALLCONV SV *
Perl_newSVrv(pTHX_ SV * const rv, const char * const classname);
#define PERL_ARGS_ASSERT_NEWSVRV                \
        assert(rv)

PERL_CALLCONV SV *
Perl_newSVsv_flags(pTHX_ SV * const old, I32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVSV_FLAGS

/* PERL_CALLCONV SV *
newSVsv_nomg(pTHX_ SV * const old)
        __attribute__warn_unused_result__; */

PERL_CALLCONV SV *
Perl_newSVuv(pTHX_ const UV u)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWSVUV

PERL_CALLCONV OP *
Perl_newTRYCATCHOP(pTHX_ I32 flags, OP *tryblock, OP *catchvar, OP *catchblock)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWTRYCATCHOP          \
        assert(tryblock); assert(catchvar); assert(catchblock)

PERL_CALLCONV OP *
Perl_newUNOP(pTHX_ I32 type, I32 flags, OP *first)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWUNOP

PERL_CALLCONV OP *
Perl_newUNOP_AUX(pTHX_ I32 type, I32 flags, OP *first, UNOP_AUX_item *aux)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWUNOP_AUX

PERL_CALLCONV OP *
Perl_newWHENOP(pTHX_ OP *cond, OP *block)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWWHENOP              \
        assert(block)

PERL_CALLCONV OP *
Perl_newWHILEOP(pTHX_ I32 flags, I32 debuggable, LOOP *loop, OP *expr, OP *block, OP *cont, I32 has_my)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEWWHILEOP

PERL_CALLCONV CV *
Perl_newXS(pTHX_ const char *name, XSUBADDR_t subaddr, const char *filename);
#define PERL_ARGS_ASSERT_NEWXS                  \
        assert(subaddr); assert(filename)

PERL_CALLCONV CV *
Perl_newXS_deffile(pTHX_ const char *name, XSUBADDR_t subaddr);
#define PERL_ARGS_ASSERT_NEWXS_DEFFILE          \
        assert(name); assert(subaddr)

PERL_CALLCONV CV *
Perl_newXS_flags(pTHX_ const char *name, XSUBADDR_t subaddr, const char * const filename, const char * const proto, U32 flags);
#define PERL_ARGS_ASSERT_NEWXS_FLAGS            \
        assert(subaddr); assert(filename)

PERL_CALLCONV CV *
Perl_newXS_len_flags(pTHX_ const char *name, STRLEN len, XSUBADDR_t subaddr, const char * const filename, const char * const proto, SV ** const_svp, U32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_NEWXS_LEN_FLAGS        \
        assert(subaddr)

PERL_CALLCONV PERL_SI *
Perl_new_stackinfo(pTHX_ I32 stitems, I32 cxitems)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEW_STACKINFO

PERL_CALLCONV SV *
Perl_new_version(pTHX_ SV *ver);
#define PERL_ARGS_ASSERT_NEW_VERSION            \
        assert(ver)

PERL_CALLCONV char *
Perl_new_warnings_bitfield(pTHX_ char *buffer, const char * const bits, STRLEN size)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_NEW_WARNINGS_BITFIELD  \
        assert(bits)

PERL_CALLCONV PerlIO *
Perl_nextargv(pTHX_ GV *gv, bool nomagicopen)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_NEXTARGV               \
        assert(gv)

PERL_CALLCONV char *
Perl_ninstr(const char *big, const char *bigend, const char *little, const char *lend)
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_NINSTR                 \
        assert(big); assert(bigend); assert(little); assert(lend)

PERL_CALLCONV void
Perl_no_bareword_filehandle(pTHX_ const char *fhname)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_NO_BAREWORD_FILEHANDLE \
        assert(fhname)

PERL_CALLCONV_NO_RET void
Perl_noperl_die(const char *pat, ...)
        __attribute__noreturn__
        __attribute__format__(__printf__,1,2);
#define PERL_ARGS_ASSERT_NOPERL_DIE             \
        assert(pat)

PERL_CALLCONV int
Perl_nothreadhook(pTHX);
#define PERL_ARGS_ASSERT_NOTHREADHOOK

PERL_CALLCONV void
Perl_notify_parser_that_changed_to_utf8(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_NOTIFY_PARSER_THAT_CHANGED_TO_UTF8

PERL_CALLCONV OP *
Perl_oopsAV(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_OOPSAV                 \
        assert(o)

PERL_CALLCONV OP *
Perl_oopsHV(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_OOPSHV                 \
        assert(o)

PERL_CALLCONV OP *
Perl_op_append_elem(pTHX_ I32 optype, OP *first, OP *last);
#define PERL_ARGS_ASSERT_OP_APPEND_ELEM

PERL_CALLCONV OP *
Perl_op_append_list(pTHX_ I32 optype, OP *first, OP *last);
#define PERL_ARGS_ASSERT_OP_APPEND_LIST

PERL_CALLCONV OPclass
Perl_op_class(pTHX_ const OP *o);
#define PERL_ARGS_ASSERT_OP_CLASS

PERL_CALLCONV void
Perl_op_clear(pTHX_ OP *o);
#define PERL_ARGS_ASSERT_OP_CLEAR               \
        assert(o)

PERL_CALLCONV OP *
Perl_op_contextualize(pTHX_ OP *o, I32 context);
#define PERL_ARGS_ASSERT_OP_CONTEXTUALIZE       \
        assert(o)

PERL_CALLCONV OP *
Perl_op_convert_list(pTHX_ I32 optype, I32 flags, OP *o)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_OP_CONVERT_LIST

PERL_CALLCONV void
Perl_op_dump(pTHX_ const OP *o);
#define PERL_ARGS_ASSERT_OP_DUMP                \
        assert(o)

PERL_CALLCONV OP *
Perl_op_force_list(pTHX_ OP *o);
#define PERL_ARGS_ASSERT_OP_FORCE_LIST

PERL_CALLCONV void
Perl_op_free(pTHX_ OP *arg);
#define PERL_ARGS_ASSERT_OP_FREE

PERL_CALLCONV OP *
Perl_op_linklist(pTHX_ OP *o);
#define PERL_ARGS_ASSERT_OP_LINKLIST            \
        assert(o)

/* PERL_CALLCONV OP *
op_lvalue(pTHX_ OP *o, I32 type); */

PERL_CALLCONV OP *
Perl_op_lvalue_flags(pTHX_ OP *o, I32 type, U32 flags);
#define PERL_ARGS_ASSERT_OP_LVALUE_FLAGS

PERL_CALLCONV void
Perl_op_null(pTHX_ OP *o);
#define PERL_ARGS_ASSERT_OP_NULL                \
        assert(o)

PERL_CALLCONV OP *
Perl_op_parent(OP *o);
#define PERL_ARGS_ASSERT_OP_PARENT              \
        assert(o)

PERL_CALLCONV OP *
Perl_op_prepend_elem(pTHX_ I32 optype, OP *first, OP *last);
#define PERL_ARGS_ASSERT_OP_PREPEND_ELEM

PERL_CALLCONV void
Perl_op_refcnt_lock(pTHX);
#define PERL_ARGS_ASSERT_OP_REFCNT_LOCK

PERL_CALLCONV void
Perl_op_refcnt_unlock(pTHX);
#define PERL_ARGS_ASSERT_OP_REFCNT_UNLOCK

PERL_CALLCONV OP *
Perl_op_scope(pTHX_ OP *o);
#define PERL_ARGS_ASSERT_OP_SCOPE

PERL_CALLCONV OP *
Perl_op_sibling_splice(OP *parent, OP *start, int del_count, OP *insert);
#define PERL_ARGS_ASSERT_OP_SIBLING_SPLICE

PERL_CALLCONV OP *
Perl_op_unscope(pTHX_ OP *o)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_OP_UNSCOPE

PERL_CALLCONV OP *
Perl_op_wrap_finally(pTHX_ OP *block, OP *finally)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_OP_WRAP_FINALLY        \
        assert(block); assert(finally)

PERL_CALLCONV void
Perl_package(pTHX_ OP *o)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PACKAGE                \
        assert(o)

PERL_CALLCONV void
Perl_package_version(pTHX_ OP *v)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PACKAGE_VERSION        \
        assert(v)

PERL_CALLCONV void
Perl_packlist(pTHX_ SV *cat, const char *pat, const char *patend, SV **beglist, SV **endlist);
#define PERL_ARGS_ASSERT_PACKLIST               \
        assert(cat); assert(pat); assert(patend); assert(beglist); assert(endlist)

PERL_CALLCONV PADOFFSET
Perl_pad_add_anon(pTHX_ CV *func, I32 optype);
#define PERL_ARGS_ASSERT_PAD_ADD_ANON           \
        assert(func)

PERL_CALLCONV PADOFFSET
Perl_pad_add_name_pv(pTHX_ const char *name, const U32 flags, HV *typestash, HV *ourstash);
#define PERL_ARGS_ASSERT_PAD_ADD_NAME_PV        \
        assert(name)

PERL_CALLCONV PADOFFSET
Perl_pad_add_name_pvn(pTHX_ const char *namepv, STRLEN namelen, U32 flags, HV *typestash, HV *ourstash);
#define PERL_ARGS_ASSERT_PAD_ADD_NAME_PVN       \
        assert(namepv)

PERL_CALLCONV PADOFFSET
Perl_pad_add_name_sv(pTHX_ SV *name, U32 flags, HV *typestash, HV *ourstash);
#define PERL_ARGS_ASSERT_PAD_ADD_NAME_SV        \
        assert(name)

PERL_CALLCONV void
Perl_pad_add_weakref(pTHX_ CV *func)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PAD_ADD_WEAKREF        \
        assert(func)

PERL_CALLCONV PADOFFSET
Perl_pad_alloc(pTHX_ I32 optype, U32 tmptype);
#define PERL_ARGS_ASSERT_PAD_ALLOC

PERL_CALLCONV void
Perl_pad_block_start(pTHX_ int full)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PAD_BLOCK_START

PERL_CALLCONV PADOFFSET
Perl_pad_findmy_pv(pTHX_ const char *name, U32 flags);
#define PERL_ARGS_ASSERT_PAD_FINDMY_PV          \
        assert(name)

PERL_CALLCONV PADOFFSET
Perl_pad_findmy_pvn(pTHX_ const char *namepv, STRLEN namelen, U32 flags);
#define PERL_ARGS_ASSERT_PAD_FINDMY_PVN         \
        assert(namepv)

PERL_CALLCONV PADOFFSET
Perl_pad_findmy_sv(pTHX_ SV *name, U32 flags);
#define PERL_ARGS_ASSERT_PAD_FINDMY_SV          \
        assert(name)

PERL_CALLCONV void
Perl_pad_fixup_inner_anons(pTHX_ PADLIST *padlist, CV *old_cv, CV *new_cv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PAD_FIXUP_INNER_ANONS  \
        assert(padlist); assert(old_cv); assert(new_cv)

PERL_CALLCONV void
Perl_pad_free(pTHX_ PADOFFSET po)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PAD_FREE

PERL_CALLCONV OP *
Perl_pad_leavemy(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PAD_LEAVEMY

PERL_CALLCONV PADLIST *
Perl_pad_new(pTHX_ int flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_PAD_NEW

PERL_CALLCONV void
Perl_pad_push(pTHX_ PADLIST *padlist, int depth);
#define PERL_ARGS_ASSERT_PAD_PUSH               \
        assert(padlist)

PERL_CALLCONV void
Perl_pad_swipe(pTHX_ PADOFFSET po, bool refadjust)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PAD_SWIPE

PERL_CALLCONV void
Perl_pad_tidy(pTHX_ padtidy_type type);
#define PERL_ARGS_ASSERT_PAD_TIDY

PERL_CALLCONV PAD **
Perl_padlist_store(pTHX_ PADLIST *padlist, I32 key, PAD *val)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PADLIST_STORE          \
        assert(padlist)

PERL_CALLCONV void
Perl_padname_free(pTHX_ PADNAME *pn);
#define PERL_ARGS_ASSERT_PADNAME_FREE           \
        assert(pn)

PERL_CALLCONV PADNAME *
Perl_padnamelist_fetch(PADNAMELIST *pnl, SSize_t key)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_PADNAMELIST_FETCH      \
        assert(pnl)

PERL_CALLCONV void
Perl_padnamelist_free(pTHX_ PADNAMELIST *pnl);
#define PERL_ARGS_ASSERT_PADNAMELIST_FREE       \
        assert(pnl)

PERL_CALLCONV PADNAME **
Perl_padnamelist_store(pTHX_ PADNAMELIST *pnl, SSize_t key, PADNAME *val);
#define PERL_ARGS_ASSERT_PADNAMELIST_STORE      \
        assert(pnl)

PERL_CALLCONV OP *
Perl_parse_arithexpr(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_ARITHEXPR

PERL_CALLCONV OP *
Perl_parse_barestmt(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_BARESTMT

PERL_CALLCONV OP *
Perl_parse_block(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_BLOCK

PERL_CALLCONV OP *
Perl_parse_fullexpr(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_FULLEXPR

PERL_CALLCONV OP *
Perl_parse_fullstmt(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_FULLSTMT

PERL_CALLCONV SV *
Perl_parse_label(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_LABEL

PERL_CALLCONV OP *
Perl_parse_listexpr(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_LISTEXPR

PERL_CALLCONV OP *
Perl_parse_stmtseq(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_STMTSEQ

PERL_CALLCONV OP *
Perl_parse_subsignature(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_SUBSIGNATURE

PERL_CALLCONV OP *
Perl_parse_termexpr(pTHX_ U32 flags);
#define PERL_ARGS_ASSERT_PARSE_TERMEXPR

PERL_CALLCONV U32
Perl_parse_unicode_opts(pTHX_ const char **popt)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PARSE_UNICODE_OPTS     \
        assert(popt)

PERL_CALLCONV void
Perl_parser_free(pTHX_ const yy_parser *parser)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PARSER_FREE            \
        assert(parser)

PERL_CALLCONV void
Perl_peep(pTHX_ OP *o)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PEEP

PERL_CALLCONV PerlInterpreter *
perl_alloc(void);
#define PERL_ARGS_ASSERT_PERL_ALLOC

PERL_CALLCONV void
perl_construct(PerlInterpreter *my_perl);
#define PERL_ARGS_ASSERT_PERL_CONSTRUCT         \
        assert(my_perl)

PERL_CALLCONV int
perl_destruct(PerlInterpreter *my_perl);
#define PERL_ARGS_ASSERT_PERL_DESTRUCT          \
        assert(my_perl)

PERL_CALLCONV void
perl_free(PerlInterpreter *my_perl);
#define PERL_ARGS_ASSERT_PERL_FREE              \
        assert(my_perl)

PERL_CALLCONV int
perl_parse(PerlInterpreter *my_perl, XSINIT_t xsinit, int argc, char **argv, char **env);
#define PERL_ARGS_ASSERT_PERL_PARSE             \
        assert(my_perl)

PERL_CALLCONV int
perl_run(PerlInterpreter *my_perl);
#define PERL_ARGS_ASSERT_PERL_RUN               \
        assert(my_perl)

PERL_CALLCONV Signal_t
Perl_perly_sighandler(int sig, Siginfo_t *info, void *uap, bool safe);
#define PERL_ARGS_ASSERT_PERLY_SIGHANDLER

/* PERL_CALLCONV const char * const
phase_name(pTHX_ enum perl_phase); */

PERL_CALLCONV void
Perl_pmop_dump(pTHX_ PMOP *pm);
#define PERL_ARGS_ASSERT_PMOP_DUMP

PERL_CALLCONV OP *
Perl_pmruntime(pTHX_ OP *o, OP *expr, OP *repl, UV flags, I32 floor)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_PMRUNTIME              \
        assert(o); assert(expr)

PERL_CALLCONV void
Perl_pop_scope(pTHX);
#define PERL_ARGS_ASSERT_POP_SCOPE

PERL_CALLCONV void
Perl_populate_isa(pTHX_ const char *name, STRLEN len, ...)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_POPULATE_ISA           \
        assert(name)

PERL_CALLCONV REGEXP *
Perl_pregcomp(pTHX_ SV * const pattern, const U32 flags);
#define PERL_ARGS_ASSERT_PREGCOMP               \
        assert(pattern)

PERL_CALLCONV I32
Perl_pregexec(pTHX_ REGEXP * const prog, char *stringarg, char *strend, char *strbeg, SSize_t minend, SV *screamer, U32 nosave);
#define PERL_ARGS_ASSERT_PREGEXEC               \
        assert(prog); assert(stringarg); assert(strend); assert(strbeg); \
        assert(screamer)

PERL_CALLCONV void
Perl_pregfree(pTHX_ REGEXP *r);
#define PERL_ARGS_ASSERT_PREGFREE

PERL_CALLCONV void
Perl_pregfree2(pTHX_ REGEXP *rx);
#define PERL_ARGS_ASSERT_PREGFREE2              \
        assert(rx)

PERL_CALLCONV const char *
Perl_prescan_version(pTHX_ const char *s, bool strict, const char **errstr, bool *sqv, int *ssaw_decimal, int *swidth, bool *salpha);
#define PERL_ARGS_ASSERT_PRESCAN_VERSION        \
        assert(s)

PERL_CALLCONV void *
Perl_ptr_table_fetch(pTHX_ PTR_TBL_t * const tbl, const void * const sv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_PTR_TABLE_FETCH        \
        assert(tbl)

PERL_CALLCONV void
Perl_ptr_table_free(pTHX_ PTR_TBL_t * const tbl);
#define PERL_ARGS_ASSERT_PTR_TABLE_FREE

PERL_CALLCONV PTR_TBL_t *
Perl_ptr_table_new(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_PTR_TABLE_NEW

PERL_CALLCONV void
Perl_ptr_table_split(pTHX_ PTR_TBL_t * const tbl);
#define PERL_ARGS_ASSERT_PTR_TABLE_SPLIT        \
        assert(tbl)

PERL_CALLCONV void
Perl_ptr_table_store(pTHX_ PTR_TBL_t * const tbl, const void * const oldsv, void * const newsv);
#define PERL_ARGS_ASSERT_PTR_TABLE_STORE        \
        assert(tbl); assert(newsv)

PERL_CALLCONV void
Perl_push_scope(pTHX);
#define PERL_ARGS_ASSERT_PUSH_SCOPE

PERL_CALLCONV char *
Perl_pv_display(pTHX_ SV *dsv, const char *pv, STRLEN cur, STRLEN len, STRLEN pvlim);
#define PERL_ARGS_ASSERT_PV_DISPLAY             \
        assert(dsv); assert(pv)

PERL_CALLCONV char *
Perl_pv_escape(pTHX_ SV *dsv, char const * const str, const STRLEN count, STRLEN max, STRLEN * const escaped, U32 flags);
#define PERL_ARGS_ASSERT_PV_ESCAPE              \
        assert(str)

PERL_CALLCONV char *
Perl_pv_pretty(pTHX_ SV *dsv, char const * const str, const STRLEN count, const STRLEN max, char const * const start_color, char const * const end_color, const U32 flags);
#define PERL_ARGS_ASSERT_PV_PRETTY              \
        assert(dsv); assert(str)

PERL_CALLCONV char *
Perl_pv_uni_display(pTHX_ SV *dsv, const U8 *spv, STRLEN len, STRLEN pvlim, UV flags);
#define PERL_ARGS_ASSERT_PV_UNI_DISPLAY         \
        assert(dsv); assert(spv)

PERL_CALLCONV void
Perl_qerror(pTHX_ SV *err);
#define PERL_ARGS_ASSERT_QERROR

PERL_CALLCONV char *
Perl_rcpv_copy(pTHX_ char * const pv);
#define PERL_ARGS_ASSERT_RCPV_COPY

PERL_CALLCONV char *
Perl_rcpv_free(pTHX_ char * const pv);
#define PERL_ARGS_ASSERT_RCPV_FREE

PERL_CALLCONV char *
Perl_rcpv_new(pTHX_ const char * const pv, STRLEN len, U32 flags)
        __attribute__malloc__
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_RCPV_NEW

PERL_CALLCONV REGEXP *
Perl_re_compile(pTHX_ SV * const pattern, U32 orig_rx_flags);
#define PERL_ARGS_ASSERT_RE_COMPILE             \
        assert(pattern)

PERL_CALLCONV char *
Perl_re_intuit_start(pTHX_ REGEXP * const rx, SV *sv, const char * const strbeg, char *strpos, char *strend, const U32 flags, re_scream_pos_data *data);
#define PERL_ARGS_ASSERT_RE_INTUIT_START        \
        assert(rx); assert(strbeg); assert(strpos); assert(strend)

PERL_CALLCONV SV *
Perl_re_intuit_string(pTHX_ REGEXP  * const r);
#define PERL_ARGS_ASSERT_RE_INTUIT_STRING       \
        assert(r)

PERL_CALLCONV REGEXP *
Perl_re_op_compile(pTHX_ SV ** const patternp, int pat_count, OP *expr, const regexp_engine *eng, REGEXP *old_re, bool *is_bare_re, const U32 rx_flags, const U32 pm_flags);
#define PERL_ARGS_ASSERT_RE_OP_COMPILE          \
        assert(eng)

PERL_CALLCONV Malloc_t
Perl_realloc(Malloc_t where, MEM_SIZE nbytes)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_REALLOC

PERL_CALLCONV void
Perl_reentrant_free(pTHX);
#define PERL_ARGS_ASSERT_REENTRANT_FREE

PERL_CALLCONV void
Perl_reentrant_init(pTHX);
#define PERL_ARGS_ASSERT_REENTRANT_INIT

PERL_CALLCONV void *
Perl_reentrant_retry(const char *f, ...);
#define PERL_ARGS_ASSERT_REENTRANT_RETRY        \
        assert(f)

PERL_CALLCONV void
Perl_reentrant_size(pTHX);
#define PERL_ARGS_ASSERT_REENTRANT_SIZE

PERL_CALLCONV HV *
Perl_refcounted_he_chain_2hv(pTHX_ const struct refcounted_he *c, U32 flags);
#define PERL_ARGS_ASSERT_REFCOUNTED_HE_CHAIN_2HV

PERL_CALLCONV SV *
Perl_refcounted_he_fetch_pv(pTHX_ const struct refcounted_he *chain, const char *key, U32 hash, U32 flags);
#define PERL_ARGS_ASSERT_REFCOUNTED_HE_FETCH_PV \
        assert(key)

PERL_CALLCONV SV *
Perl_refcounted_he_fetch_pvn(pTHX_ const struct refcounted_he *chain, const char *keypv, STRLEN keylen, U32 hash, U32 flags);
#define PERL_ARGS_ASSERT_REFCOUNTED_HE_FETCH_PVN \
        assert(keypv)

PERL_CALLCONV SV *
Perl_refcounted_he_fetch_sv(pTHX_ const struct refcounted_he *chain, SV *key, U32 hash, U32 flags);
#define PERL_ARGS_ASSERT_REFCOUNTED_HE_FETCH_SV \
        assert(key)

PERL_CALLCONV void
Perl_refcounted_he_free(pTHX_ struct refcounted_he *he);
#define PERL_ARGS_ASSERT_REFCOUNTED_HE_FREE

PERL_CALLCONV struct refcounted_he *
Perl_refcounted_he_inc(pTHX_ struct refcounted_he *he);
#define PERL_ARGS_ASSERT_REFCOUNTED_HE_INC

PERL_CALLCONV struct refcounted_he *
Perl_refcounted_he_new_pv(pTHX_ struct refcounted_he *parent, const char *key, U32 hash, SV *value, U32 flags);
#define PERL_ARGS_ASSERT_REFCOUNTED_HE_NEW_PV   \
        assert(key)

PERL_CALLCONV struct refcounted_he *
Perl_refcounted_he_new_pvn(pTHX_ struct refcounted_he *parent, const char *keypv, STRLEN keylen, U32 hash, SV *value, U32 flags);
#define PERL_ARGS_ASSERT_REFCOUNTED_HE_NEW_PVN  \
        assert(keypv)

PERL_CALLCONV struct refcounted_he *
Perl_refcounted_he_new_sv(pTHX_ struct refcounted_he *parent, SV *key, U32 hash, SV *value, U32 flags);
#define PERL_ARGS_ASSERT_REFCOUNTED_HE_NEW_SV   \
        assert(key)

PERL_CALLCONV SV *
Perl_reg_named_buff(pTHX_ REGEXP * const rx, SV * const key, SV * const value, const U32 flags);
#define PERL_ARGS_ASSERT_REG_NAMED_BUFF         \
        assert(rx)

PERL_CALLCONV SV *
Perl_reg_named_buff_all(pTHX_ REGEXP * const rx, const U32 flags);
#define PERL_ARGS_ASSERT_REG_NAMED_BUFF_ALL     \
        assert(rx)

PERL_CALLCONV bool
Perl_reg_named_buff_exists(pTHX_ REGEXP * const rx, SV * const key, const U32 flags);
#define PERL_ARGS_ASSERT_REG_NAMED_BUFF_EXISTS  \
        assert(rx); assert(key)

PERL_CALLCONV SV *
Perl_reg_named_buff_fetch(pTHX_ REGEXP * const rx, SV * const namesv, const U32 flags);
#define PERL_ARGS_ASSERT_REG_NAMED_BUFF_FETCH   \
        assert(rx); assert(namesv)

PERL_CALLCONV SV *
Perl_reg_named_buff_firstkey(pTHX_ REGEXP * const rx, const U32 flags);
#define PERL_ARGS_ASSERT_REG_NAMED_BUFF_FIRSTKEY \
        assert(rx)

PERL_CALLCONV SV *
Perl_reg_named_buff_iter(pTHX_ REGEXP * const rx, const SV * const lastkey, const U32 flags);
#define PERL_ARGS_ASSERT_REG_NAMED_BUFF_ITER    \
        assert(rx)

PERL_CALLCONV SV *
Perl_reg_named_buff_nextkey(pTHX_ REGEXP * const rx, const U32 flags);
#define PERL_ARGS_ASSERT_REG_NAMED_BUFF_NEXTKEY \
        assert(rx)

PERL_CALLCONV SV *
Perl_reg_named_buff_scalar(pTHX_ REGEXP * const rx, const U32 flags);
#define PERL_ARGS_ASSERT_REG_NAMED_BUFF_SCALAR  \
        assert(rx)

PERL_CALLCONV void
Perl_reg_numbered_buff_fetch(pTHX_ REGEXP * const re, const I32 paren, SV * const sv);
#define PERL_ARGS_ASSERT_REG_NUMBERED_BUFF_FETCH \
        assert(re)

PERL_CALLCONV void
Perl_reg_numbered_buff_fetch_flags(pTHX_ REGEXP * const re, const I32 paren, SV * const sv, U32 flags);
#define PERL_ARGS_ASSERT_REG_NUMBERED_BUFF_FETCH_FLAGS \
        assert(re)

PERL_CALLCONV I32
Perl_reg_numbered_buff_length(pTHX_ REGEXP * const rx, const SV * const sv, const I32 paren);
#define PERL_ARGS_ASSERT_REG_NUMBERED_BUFF_LENGTH \
        assert(rx); assert(sv)

PERL_CALLCONV void
Perl_reg_numbered_buff_store(pTHX_ REGEXP * const rx, const I32 paren, SV const * const value);
#define PERL_ARGS_ASSERT_REG_NUMBERED_BUFF_STORE \
        assert(rx)

PERL_CALLCONV SV *
Perl_reg_qr_package(pTHX_ REGEXP * const rx);
#define PERL_ARGS_ASSERT_REG_QR_PACKAGE         \
        assert(rx)

PERL_CALLCONV REGEXP *
Perl_reg_temp_copy(pTHX_ REGEXP *dsv, REGEXP *ssv);
#define PERL_ARGS_ASSERT_REG_TEMP_COPY          \
        assert(ssv)

PERL_CALLCONV void
Perl_regdump(pTHX_ const regexp *r);
#define PERL_ARGS_ASSERT_REGDUMP                \
        assert(r)

PERL_CALLCONV I32
Perl_regexec_flags(pTHX_ REGEXP * const rx, char *stringarg, char *strend, char *strbeg, SSize_t minend, SV *sv, void *data, U32 flags);
#define PERL_ARGS_ASSERT_REGEXEC_FLAGS          \
        assert(rx); assert(stringarg); assert(strend); assert(strbeg); assert(sv)

PERL_CALLCONV void
Perl_regfree_internal(pTHX_ REGEXP * const rx);
#define PERL_ARGS_ASSERT_REGFREE_INTERNAL       \
        assert(rx)

PERL_CALLCONV void
Perl_reginitcolors(pTHX);
#define PERL_ARGS_ASSERT_REGINITCOLORS

PERL_CALLCONV void
Perl_repeatcpy(char *to, const char *from, I32 len, IV count);
#define PERL_ARGS_ASSERT_REPEATCPY              \
        assert(to); assert(from)

PERL_CALLCONV void
Perl_report_evil_fh(pTHX_ const GV *gv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_REPORT_EVIL_FH

PERL_CALLCONV void
Perl_report_uninit(pTHX_ const SV *uninit_sv);
#define PERL_ARGS_ASSERT_REPORT_UNINIT

PERL_CALLCONV void
Perl_report_wrongway_fh(pTHX_ const GV *gv, const char have)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_REPORT_WRONGWAY_FH

PERL_CALLCONV void
Perl_require_pv(pTHX_ const char *pv);
#define PERL_ARGS_ASSERT_REQUIRE_PV             \
        assert(pv)

PERL_CALLCONV void
Perl_resume_compcv(pTHX_ struct suspended_compcv *buffer, bool save);
#define PERL_ARGS_ASSERT_RESUME_COMPCV          \
        assert(buffer)

/* PERL_CALLCONV void
resume_compcv_and_save(pTHX_ struct suspended_compcv *buffer); */

/* PERL_CALLCONV void
resume_compcv_final(pTHX_ struct suspended_compcv *buffer); */

PERL_CALLCONV char *
Perl_rninstr(const char *big, const char *bigend, const char *little, const char *lend)
        __attribute__warn_unused_result__
        __attribute__pure__;
#define PERL_ARGS_ASSERT_RNINSTR                \
        assert(big); assert(bigend); assert(little); assert(lend)

PERL_CALLCONV void
Perl_rpeep(pTHX_ OP *o)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_RPEEP

PERL_CALLCONV Sighandler_t
Perl_rsignal(pTHX_ int i, Sighandler_t t);
#define PERL_ARGS_ASSERT_RSIGNAL

PERL_CALLCONV int
Perl_rsignal_restore(pTHX_ int i, Sigsave_t *t)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_RSIGNAL_RESTORE

PERL_CALLCONV int
Perl_rsignal_save(pTHX_ int i, Sighandler_t t1, Sigsave_t *save)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_RSIGNAL_SAVE           \
        assert(save)

PERL_CALLCONV Sighandler_t
Perl_rsignal_state(pTHX_ int i);
#define PERL_ARGS_ASSERT_RSIGNAL_STATE

PERL_CALLCONV int
Perl_runops_debug(pTHX);
#define PERL_ARGS_ASSERT_RUNOPS_DEBUG

PERL_CALLCONV int
Perl_runops_standard(pTHX);
#define PERL_ARGS_ASSERT_RUNOPS_STANDARD

PERL_CALLCONV CV *
Perl_rv2cv_op_cv(pTHX_ OP *cvop, U32 flags);
#define PERL_ARGS_ASSERT_RV2CV_OP_CV            \
        assert(cvop)

PERL_CALLCONV void
Perl_rxres_save(pTHX_ void **rsp, REGEXP *rx)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_RXRES_SAVE             \
        assert(rsp); assert(rx)

PERL_CALLCONV Malloc_t
Perl_safesyscalloc(MEM_SIZE elements, MEM_SIZE size)
        __attribute__malloc__
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SAFESYSCALLOC

PERL_CALLCONV Free_t
Perl_safesysfree(Malloc_t where);
#define PERL_ARGS_ASSERT_SAFESYSFREE

PERL_CALLCONV Malloc_t
Perl_safesysmalloc(MEM_SIZE nbytes)
        __attribute__malloc__
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SAFESYSMALLOC

PERL_CALLCONV Malloc_t
Perl_safesysrealloc(Malloc_t where, MEM_SIZE nbytes)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SAFESYSREALLOC

PERL_CALLCONV void
Perl_save_I16(pTHX_ I16 *intp);
#define PERL_ARGS_ASSERT_SAVE_I16               \
        assert(intp)

PERL_CALLCONV void
Perl_save_I32(pTHX_ I32 *intp);
#define PERL_ARGS_ASSERT_SAVE_I32               \
        assert(intp)

PERL_CALLCONV void
Perl_save_I8(pTHX_ I8 *bytep);
#define PERL_ARGS_ASSERT_SAVE_I8                \
        assert(bytep)

PERL_CALLCONV void
Perl_save_adelete(pTHX_ AV *av, SSize_t key);
#define PERL_ARGS_ASSERT_SAVE_ADELETE           \
        assert(av)

/* PERL_CALLCONV void
save_aelem(pTHX_ AV *av, SSize_t idx, SV **sptr); */

PERL_CALLCONV void
Perl_save_aelem_flags(pTHX_ AV *av, SSize_t idx, SV **sptr, const U32 flags);
#define PERL_ARGS_ASSERT_SAVE_AELEM_FLAGS       \
        assert(av); assert(sptr)

PERL_CALLCONV SSize_t
Perl_save_alloc(pTHX_ SSize_t size, I32 pad);
#define PERL_ARGS_ASSERT_SAVE_ALLOC

PERL_CALLCONV void
Perl_save_aptr(pTHX_ AV **aptr);
#define PERL_ARGS_ASSERT_SAVE_APTR              \
        assert(aptr)

PERL_CALLCONV AV *
Perl_save_ary(pTHX_ GV *gv);
#define PERL_ARGS_ASSERT_SAVE_ARY               \
        assert(gv)

PERL_CALLCONV void
Perl_save_bool(pTHX_ bool *boolp);
#define PERL_ARGS_ASSERT_SAVE_BOOL              \
        assert(boolp)

PERL_CALLCONV void
Perl_save_clearsv(pTHX_ SV **svp);
#define PERL_ARGS_ASSERT_SAVE_CLEARSV           \
        assert(svp)

PERL_CALLCONV void
Perl_save_delete(pTHX_ HV *hv, char *key, I32 klen);
#define PERL_ARGS_ASSERT_SAVE_DELETE            \
        assert(hv); assert(key)

PERL_CALLCONV void
Perl_save_destructor(pTHX_ DESTRUCTORFUNC_NOCONTEXT_t f, void *p);
#define PERL_ARGS_ASSERT_SAVE_DESTRUCTOR        \
        assert(p)

PERL_CALLCONV void
Perl_save_destructor_x(pTHX_ DESTRUCTORFUNC_t f, void *p);
#define PERL_ARGS_ASSERT_SAVE_DESTRUCTOR_X

PERL_CALLCONV void
Perl_save_freercpv(pTHX_ char *rcpv);
#define PERL_ARGS_ASSERT_SAVE_FREERCPV          \
        assert(rcpv)

PERL_CALLCONV void
Perl_save_generic_pvref(pTHX_ char **str);
#define PERL_ARGS_ASSERT_SAVE_GENERIC_PVREF     \
        assert(str)

PERL_CALLCONV void
Perl_save_generic_svref(pTHX_ SV **sptr);
#define PERL_ARGS_ASSERT_SAVE_GENERIC_SVREF     \
        assert(sptr)

PERL_CALLCONV void
Perl_save_gp(pTHX_ GV *gv, I32 empty);
#define PERL_ARGS_ASSERT_SAVE_GP                \
        assert(gv)

PERL_CALLCONV HV *
Perl_save_hash(pTHX_ GV *gv);
#define PERL_ARGS_ASSERT_SAVE_HASH              \
        assert(gv)

PERL_CALLCONV void
Perl_save_hdelete(pTHX_ HV *hv, SV *keysv);
#define PERL_ARGS_ASSERT_SAVE_HDELETE           \
        assert(hv); assert(keysv)

/* PERL_CALLCONV void
save_helem(pTHX_ HV *hv, SV *key, SV **sptr); */

PERL_CALLCONV void
Perl_save_helem_flags(pTHX_ HV *hv, SV *key, SV **sptr, const U32 flags);
#define PERL_ARGS_ASSERT_SAVE_HELEM_FLAGS       \
        assert(hv); assert(key); assert(sptr)

PERL_CALLCONV void
Perl_save_hints(pTHX);
#define PERL_ARGS_ASSERT_SAVE_HINTS

PERL_CALLCONV void
Perl_save_hptr(pTHX_ HV **hptr);
#define PERL_ARGS_ASSERT_SAVE_HPTR              \
        assert(hptr)

PERL_CALLCONV void
Perl_save_int(pTHX_ int *intp);
#define PERL_ARGS_ASSERT_SAVE_INT               \
        assert(intp)

PERL_CALLCONV void
Perl_save_item(pTHX_ SV *item);
#define PERL_ARGS_ASSERT_SAVE_ITEM              \
        assert(item)

PERL_CALLCONV void
Perl_save_iv(pTHX_ IV *ivp);
#define PERL_ARGS_ASSERT_SAVE_IV                \
        assert(ivp)

PERL_CALLCONV void
Perl_save_padsv_and_mortalize(pTHX_ PADOFFSET off);
#define PERL_ARGS_ASSERT_SAVE_PADSV_AND_MORTALIZE

PERL_CALLCONV void
Perl_save_pptr(pTHX_ char **pptr);
#define PERL_ARGS_ASSERT_SAVE_PPTR              \
        assert(pptr)

PERL_CALLCONV void
Perl_save_pushi32ptr(pTHX_ const I32 i, void * const ptr, const int type);
#define PERL_ARGS_ASSERT_SAVE_PUSHI32PTR

PERL_CALLCONV void
Perl_save_pushptr(pTHX_ void * const ptr, const int type);
#define PERL_ARGS_ASSERT_SAVE_PUSHPTR

PERL_CALLCONV void
Perl_save_pushptrptr(pTHX_ void * const ptr1, void * const ptr2, const int type);
#define PERL_ARGS_ASSERT_SAVE_PUSHPTRPTR

PERL_CALLCONV void
Perl_save_rcpv(pTHX_ char **prcpv);
#define PERL_ARGS_ASSERT_SAVE_RCPV              \
        assert(prcpv)

PERL_CALLCONV void
Perl_save_re_context(pTHX);
#define PERL_ARGS_ASSERT_SAVE_RE_CONTEXT

PERL_CALLCONV SV *
Perl_save_scalar(pTHX_ GV *gv);
#define PERL_ARGS_ASSERT_SAVE_SCALAR            \
        assert(gv)

PERL_CALLCONV void
Perl_save_set_svflags(pTHX_ SV *sv, U32 mask, U32 val);
#define PERL_ARGS_ASSERT_SAVE_SET_SVFLAGS       \
        assert(sv)

PERL_CALLCONV void
Perl_save_shared_pvref(pTHX_ char **str);
#define PERL_ARGS_ASSERT_SAVE_SHARED_PVREF      \
        assert(str)

PERL_CALLCONV void
Perl_save_sptr(pTHX_ SV **sptr);
#define PERL_ARGS_ASSERT_SAVE_SPTR              \
        assert(sptr)

PERL_CALLCONV void
Perl_save_strlen(pTHX_ STRLEN *ptr);
#define PERL_ARGS_ASSERT_SAVE_STRLEN            \
        assert(ptr)

PERL_CALLCONV SV *
Perl_save_svref(pTHX_ SV **sptr);
#define PERL_ARGS_ASSERT_SAVE_SVREF             \
        assert(sptr)

PERL_CALLCONV void
Perl_save_vptr(pTHX_ void *ptr);
#define PERL_ARGS_ASSERT_SAVE_VPTR              \
        assert(ptr)

PERL_CALLCONV char *
Perl_savesharedpv(pTHX_ const char *pv)
        __attribute__malloc__
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SAVESHAREDPV

PERL_CALLCONV char *
Perl_savesharedpvn(pTHX_ const char * const pv, const STRLEN len)
        __attribute__malloc__
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SAVESHAREDPVN

PERL_CALLCONV void
Perl_savestack_grow(pTHX);
#define PERL_ARGS_ASSERT_SAVESTACK_GROW

PERL_CALLCONV void
Perl_savestack_grow_cnt(pTHX_ I32 need);
#define PERL_ARGS_ASSERT_SAVESTACK_GROW_CNT

PERL_CALLCONV void
Perl_savetmps(pTHX);
#define PERL_ARGS_ASSERT_SAVETMPS

PERL_CALLCONV OP *
Perl_sawparens(pTHX_ OP *o)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SAWPARENS

PERL_CALLCONV OP *
Perl_scalar(pTHX_ OP *o)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SCALAR

PERL_CALLCONV OP *
Perl_scalarvoid(pTHX_ OP *o)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SCALARVOID             \
        assert(o)

PERL_CALLCONV NV
Perl_scan_bin(pTHX_ const char *start, STRLEN len, STRLEN *retlen);
#define PERL_ARGS_ASSERT_SCAN_BIN               \
        assert(start); assert(retlen)

PERL_CALLCONV NV
Perl_scan_hex(pTHX_ const char *start, STRLEN len, STRLEN *retlen);
#define PERL_ARGS_ASSERT_SCAN_HEX               \
        assert(start); assert(retlen)

PERL_CALLCONV char *
Perl_scan_num(pTHX_ const char *s, YYSTYPE *lvalp);
#define PERL_ARGS_ASSERT_SCAN_NUM               \
        assert(s); assert(lvalp)

PERL_CALLCONV NV
Perl_scan_oct(pTHX_ const char *start, STRLEN len, STRLEN *retlen);
#define PERL_ARGS_ASSERT_SCAN_OCT               \
        assert(start); assert(retlen)

PERL_CALLCONV char *
Perl_scan_str(pTHX_ char *start, int keep_quoted, int keep_delims, int re_reparse, char **delimp)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SCAN_STR               \
        assert(start)

PERL_CALLCONV const char *
Perl_scan_version(pTHX_ const char *s, SV *rv, bool qv);
#define PERL_ARGS_ASSERT_SCAN_VERSION           \
        assert(s); assert(rv)

PERL_CALLCONV char *
Perl_scan_vstring(pTHX_ const char *s, const char * const e, SV *sv);
#define PERL_ARGS_ASSERT_SCAN_VSTRING           \
        assert(s); assert(e); assert(sv)

PERL_CALLCONV char *
Perl_scan_word(pTHX_ char *s, char *dest, STRLEN destlen, int allow_package, STRLEN *slp);
#define PERL_ARGS_ASSERT_SCAN_WORD              \
        assert(s); assert(dest); assert(slp)

PERL_CALLCONV char *
Perl_scan_word6(pTHX_ char *s, char *dest, STRLEN destlen, int allow_package, STRLEN *slp, bool warn_tick);
#define PERL_ARGS_ASSERT_SCAN_WORD6             \
        assert(s); assert(dest); assert(slp)

PERL_CALLCONV U32
Perl_seed(pTHX);
#define PERL_ARGS_ASSERT_SEED

PERL_CALLCONV void
Perl_set_caret_X(pTHX);
#define PERL_ARGS_ASSERT_SET_CARET_X

PERL_CALLCONV void
Perl_set_context(void *t);
#define PERL_ARGS_ASSERT_SET_CONTEXT            \
        assert(t)

PERL_CALLCONV void
Perl_set_numeric_standard(pTHX);
#define PERL_ARGS_ASSERT_SET_NUMERIC_STANDARD

PERL_CALLCONV void
Perl_set_numeric_underlying(pTHX);
#define PERL_ARGS_ASSERT_SET_NUMERIC_UNDERLYING

PERL_CALLCONV void
Perl_setdefout(pTHX_ GV *gv);
#define PERL_ARGS_ASSERT_SETDEFOUT              \
        assert(gv)

PERL_CALLCONV void
Perl_setfd_cloexec(int fd)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SETFD_CLOEXEC

PERL_CALLCONV void
Perl_setfd_cloexec_for_nonsysfd(pTHX_ int fd)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SETFD_CLOEXEC_FOR_NONSYSFD

PERL_CALLCONV void
Perl_setfd_cloexec_or_inhexec_by_sysfdness(pTHX_ int fd)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SETFD_CLOEXEC_OR_INHEXEC_BY_SYSFDNESS

PERL_CALLCONV void
Perl_setfd_inhexec(int fd)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SETFD_INHEXEC

PERL_CALLCONV void
Perl_setfd_inhexec_for_sysfd(pTHX_ int fd)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SETFD_INHEXEC_FOR_SYSFD

PERL_CALLCONV HEK *
Perl_share_hek(pTHX_ const char *str, SSize_t len, U32 hash);
#define PERL_ARGS_ASSERT_SHARE_HEK              \
        assert(str)

PERL_CALLCONV Signal_t
Perl_sighandler1(int sig)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SIGHANDLER1

PERL_CALLCONV Signal_t
Perl_sighandler3(int sig, Siginfo_t *info, void *uap)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SIGHANDLER3

PERL_CALLCONV char *
Perl_skipspace_flags(pTHX_ char *s, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SKIPSPACE_FLAGS        \
        assert(s)

PERL_CALLCONV void
Perl_sortsv(pTHX_ SV **array, size_t num_elts, SVCOMPARE_t cmp);
#define PERL_ARGS_ASSERT_SORTSV                 \
        assert(cmp)

PERL_CALLCONV void
Perl_sortsv_flags(pTHX_ SV **array, size_t num_elts, SVCOMPARE_t cmp, U32 flags);
#define PERL_ARGS_ASSERT_SORTSV_FLAGS           \
        assert(cmp)

PERL_CALLCONV SV **
Perl_stack_grow(pTHX_ SV **sp, SV **p, SSize_t n);
#define PERL_ARGS_ASSERT_STACK_GROW             \
        assert(sp); assert(p)

PERL_CALLCONV PerlIO *
Perl_start_glob(pTHX_ SV *tmpglob, IO *io)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_START_GLOB             \
        assert(tmpglob); assert(io)

PERL_CALLCONV I32
Perl_start_subparse(pTHX_ I32 is_format, U32 flags);
#define PERL_ARGS_ASSERT_START_SUBPARSE

PERL_CALLCONV NV
Perl_str_to_version(pTHX_ SV *sv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_STR_TO_VERSION         \
        assert(sv)

PERL_CALLCONV void
Perl_sub_crush_depth(pTHX_ CV *cv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SUB_CRUSH_DEPTH        \
        assert(cv)

PERL_CALLCONV void
Perl_suspend_compcv(pTHX_ struct suspended_compcv *buffer);
#define PERL_ARGS_ASSERT_SUSPEND_COMPCV         \
        assert(buffer)

PERL_CALLCONV bool
Perl_sv_2bool_flags(pTHX_ SV *sv, I32 flags);
#define PERL_ARGS_ASSERT_SV_2BOOL_FLAGS         \
        assert(sv)

PERL_CALLCONV CV *
Perl_sv_2cv(pTHX_ SV *sv, HV ** const st, GV ** const gvp, const I32 lref);
#define PERL_ARGS_ASSERT_SV_2CV                 \
        assert(st); assert(gvp)

PERL_CALLCONV IO *
Perl_sv_2io(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_2IO                 \
        assert(sv)

PERL_CALLCONV IV
Perl_sv_2iv_flags(pTHX_ SV * const sv, const I32 flags);
#define PERL_ARGS_ASSERT_SV_2IV_FLAGS           \
        assert(sv)

PERL_CALLCONV SV *
Perl_sv_2mortal(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_2MORTAL

PERL_CALLCONV SV *
Perl_sv_2num(pTHX_ SV * const sv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SV_2NUM                \
        assert(sv)

PERL_CALLCONV NV
Perl_sv_2nv_flags(pTHX_ SV * const sv, const I32 flags);
#define PERL_ARGS_ASSERT_SV_2NV_FLAGS           \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_2pv_flags(pTHX_ SV * const sv, STRLEN * const lp, const U32 flags);
#define PERL_ARGS_ASSERT_SV_2PV_FLAGS           \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_2pvbyte_flags(pTHX_ SV *sv, STRLEN * const lp, const U32 flags);
#define PERL_ARGS_ASSERT_SV_2PVBYTE_FLAGS       \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_2pvutf8_flags(pTHX_ SV *sv, STRLEN * const lp, const U32 flags);
#define PERL_ARGS_ASSERT_SV_2PVUTF8_FLAGS       \
        assert(sv)

PERL_CALLCONV UV
Perl_sv_2uv_flags(pTHX_ SV * const sv, const I32 flags);
#define PERL_ARGS_ASSERT_SV_2UV_FLAGS           \
        assert(sv)

PERL_CALLCONV void
Perl_sv_backoff(SV * const sv);
#define PERL_ARGS_ASSERT_SV_BACKOFF             \
        assert(sv)

PERL_CALLCONV SV *
Perl_sv_bless(pTHX_ SV * const sv, HV * const stash);
#define PERL_ARGS_ASSERT_SV_BLESS               \
        assert(sv); assert(stash)

PERL_CALLCONV bool
Perl_sv_cat_decode(pTHX_ SV *dsv, SV *encoding, SV *ssv, int *offset, char *tstr, int tlen);
#define PERL_ARGS_ASSERT_SV_CAT_DECODE          \
        assert(dsv); assert(encoding); assert(ssv); assert(offset); assert(tstr)

PERL_CALLCONV void
Perl_sv_catpv(pTHX_ SV * const dsv, const char *sstr);
#define PERL_ARGS_ASSERT_SV_CATPV               \
        assert(dsv)

PERL_CALLCONV void
Perl_sv_catpv_flags(pTHX_ SV *dsv, const char *sstr, const I32 flags);
#define PERL_ARGS_ASSERT_SV_CATPV_FLAGS         \
        assert(dsv); assert(sstr)

PERL_CALLCONV void
Perl_sv_catpv_mg(pTHX_ SV * const dsv, const char * const sstr);
#define PERL_ARGS_ASSERT_SV_CATPV_MG            \
        assert(dsv)

PERL_CALLCONV void
Perl_sv_catpvf(pTHX_ SV * const sv, const char * const pat, ...)
        __attribute__format__(__printf__,pTHX_2,pTHX_3);
#define PERL_ARGS_ASSERT_SV_CATPVF              \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_catpvf_mg(pTHX_ SV * const sv, const char * const pat, ...)
        __attribute__format__(__printf__,pTHX_2,pTHX_3);
#define PERL_ARGS_ASSERT_SV_CATPVF_MG           \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_catpvn_flags(pTHX_ SV * const dsv, const char *sstr, const STRLEN len, const I32 flags);
#define PERL_ARGS_ASSERT_SV_CATPVN_FLAGS        \
        assert(dsv); assert(sstr)

PERL_CALLCONV void
Perl_sv_catsv_flags(pTHX_ SV * const dsv, SV * const sstr, const I32 flags);
#define PERL_ARGS_ASSERT_SV_CATSV_FLAGS         \
        assert(dsv)

PERL_CALLCONV void
Perl_sv_chop(pTHX_ SV * const sv, const char * const ptr);
#define PERL_ARGS_ASSERT_SV_CHOP                \
        assert(sv)

PERL_CALLCONV I32
Perl_sv_clean_all(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SV_CLEAN_ALL

PERL_CALLCONV void
Perl_sv_clean_objs(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SV_CLEAN_OBJS

PERL_CALLCONV void
Perl_sv_clear(pTHX_ SV * const orig_sv);
#define PERL_ARGS_ASSERT_SV_CLEAR               \
        assert(orig_sv)

PERL_CALLCONV I32
Perl_sv_cmp(pTHX_ SV * const sv1, SV * const sv2);
#define PERL_ARGS_ASSERT_SV_CMP

PERL_CALLCONV I32
Perl_sv_cmp_flags(pTHX_ SV * const sv1, SV * const sv2, const U32 flags);
#define PERL_ARGS_ASSERT_SV_CMP_FLAGS

PERL_CALLCONV I32
Perl_sv_cmp_locale(pTHX_ SV * const sv1, SV * const sv2);
#define PERL_ARGS_ASSERT_SV_CMP_LOCALE

PERL_CALLCONV I32
Perl_sv_cmp_locale_flags(pTHX_ SV * const sv1, SV * const sv2, const U32 flags);
#define PERL_ARGS_ASSERT_SV_CMP_LOCALE_FLAGS

PERL_CALLCONV void
Perl_sv_copypv_flags(pTHX_ SV * const dsv, SV * const ssv, const I32 flags);
#define PERL_ARGS_ASSERT_SV_COPYPV_FLAGS        \
        assert(dsv); assert(ssv)

/* PERL_CALLCONV void
sv_copypv_nomg(pTHX_ SV * const dsv, SV * const ssv); */

PERL_CALLCONV void
Perl_sv_dec(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_DEC

PERL_CALLCONV void
Perl_sv_dec_nomg(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_DEC_NOMG

PERL_CALLCONV void
Perl_sv_del_backref(pTHX_ SV * const tsv, SV * const sv);
#define PERL_ARGS_ASSERT_SV_DEL_BACKREF         \
        assert(tsv); assert(sv)

PERL_CALLCONV bool
Perl_sv_derived_from(pTHX_ SV *sv, const char * const name)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_DERIVED_FROM        \
        assert(sv); assert(name)

PERL_CALLCONV bool
Perl_sv_derived_from_hv(pTHX_ SV *sv, HV *hv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_DERIVED_FROM_HV     \
        assert(sv); assert(hv)

PERL_CALLCONV bool
Perl_sv_derived_from_pv(pTHX_ SV *sv, const char * const name, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_DERIVED_FROM_PV     \
        assert(sv); assert(name)

PERL_CALLCONV bool
Perl_sv_derived_from_pvn(pTHX_ SV *sv, const char * const name, const STRLEN len, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_DERIVED_FROM_PVN    \
        assert(sv); assert(name)

PERL_CALLCONV bool
Perl_sv_derived_from_sv(pTHX_ SV *sv, SV *namesv, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_DERIVED_FROM_SV     \
        assert(sv); assert(namesv)

PERL_CALLCONV bool
Perl_sv_destroyable(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_SV_DESTROYABLE

PERL_CALLCONV bool
Perl_sv_does(pTHX_ SV *sv, const char * const name)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_DOES                \
        assert(sv); assert(name)

PERL_CALLCONV bool
Perl_sv_does_pv(pTHX_ SV *sv, const char * const name, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_DOES_PV             \
        assert(sv); assert(name)

PERL_CALLCONV bool
Perl_sv_does_pvn(pTHX_ SV *sv, const char * const name, const STRLEN len, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_DOES_PVN            \
        assert(sv); assert(name)

PERL_CALLCONV bool
Perl_sv_does_sv(pTHX_ SV *sv, SV *namesv, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_DOES_SV             \
        assert(sv); assert(namesv)

PERL_CALLCONV void
Perl_sv_dump(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_SV_DUMP

PERL_CALLCONV void
Perl_sv_dump_depth(pTHX_ SV *sv, I32 depth);
#define PERL_ARGS_ASSERT_SV_DUMP_DEPTH

PERL_CALLCONV I32
Perl_sv_eq_flags(pTHX_ SV *sv1, SV *sv2, const U32 flags);
#define PERL_ARGS_ASSERT_SV_EQ_FLAGS

PERL_CALLCONV void
Perl_sv_force_normal_flags(pTHX_ SV * const sv, const U32 flags);
#define PERL_ARGS_ASSERT_SV_FORCE_NORMAL_FLAGS  \
        assert(sv)

PERL_CALLCONV void
Perl_sv_free(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_FREE

PERL_CALLCONV void
Perl_sv_free2(pTHX_ SV * const sv, const U32 refcnt);
#define PERL_ARGS_ASSERT_SV_FREE2               \
        assert(sv)

PERL_CALLCONV void
Perl_sv_free_arenas(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SV_FREE_ARENAS

PERL_CALLCONV SV *
Perl_sv_get_backrefs(SV * const sv);
#define PERL_ARGS_ASSERT_SV_GET_BACKREFS        \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_gets(pTHX_ SV * const sv, PerlIO * const fp, I32 append);
#define PERL_ARGS_ASSERT_SV_GETS                \
        assert(sv); assert(fp)

PERL_CALLCONV char *
Perl_sv_grow(pTHX_ SV * const sv, STRLEN newlen);
#define PERL_ARGS_ASSERT_SV_GROW                \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_grow_fresh(pTHX_ SV * const sv, STRLEN newlen);
#define PERL_ARGS_ASSERT_SV_GROW_FRESH          \
        assert(sv)

PERL_CALLCONV void
Perl_sv_inc(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_INC

PERL_CALLCONV void
Perl_sv_inc_nomg(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_INC_NOMG

PERL_CALLCONV void
Perl_sv_insert_flags(pTHX_ SV * const bigstr, const STRLEN offset, const STRLEN len, const char *little, const STRLEN littlelen, const U32 flags);
#define PERL_ARGS_ASSERT_SV_INSERT_FLAGS        \
        assert(bigstr); assert(little)

PERL_CALLCONV int
Perl_sv_isa(pTHX_ SV *sv, const char * const name);
#define PERL_ARGS_ASSERT_SV_ISA                 \
        assert(name)

PERL_CALLCONV bool
Perl_sv_isa_sv(pTHX_ SV *sv, SV *namesv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_ISA_SV              \
        assert(sv); assert(namesv)

PERL_CALLCONV int
Perl_sv_isobject(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_SV_ISOBJECT

PERL_CALLCONV STRLEN
Perl_sv_len(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_LEN

PERL_CALLCONV STRLEN
Perl_sv_len_utf8(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_LEN_UTF8

PERL_CALLCONV STRLEN
Perl_sv_len_utf8_nomg(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_LEN_UTF8_NOMG       \
        assert(sv)

PERL_CALLCONV void
Perl_sv_magic(pTHX_ SV * const sv, SV * const obj, const int how, const char * const name, const I32 namlen);
#define PERL_ARGS_ASSERT_SV_MAGIC               \
        assert(sv)

PERL_CALLCONV MAGIC *
Perl_sv_magicext(pTHX_ SV * const sv, SV * const obj, const int how, const MGVTBL * const vtbl, const char * const name, const I32 namlen);
#define PERL_ARGS_ASSERT_SV_MAGICEXT            \
        assert(sv)

PERL_CALLCONV MAGIC *
Perl_sv_magicext_mglob(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_SV_MAGICEXT_MGLOB      \
        assert(sv)

PERL_CALLCONV SV *
Perl_sv_mortalcopy_flags(pTHX_ SV * const oldsv, U32 flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_MORTALCOPY_FLAGS

PERL_CALLCONV SV *
Perl_sv_newmortal(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_NEWMORTAL

PERL_CALLCONV SV *
Perl_sv_newref(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_NEWREF

PERL_CALLCONV void
Perl_sv_nosharing(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_SV_NOSHARING

/* PERL_CALLCONV bool
sv_numeq(pTHX_ SV *sv1, SV *sv2); */

PERL_CALLCONV bool
Perl_sv_numeq_flags(pTHX_ SV *sv1, SV *sv2, const U32 flags);
#define PERL_ARGS_ASSERT_SV_NUMEQ_FLAGS

PERL_CALLCONV char *
Perl_sv_peek(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_SV_PEEK

PERL_CALLCONV void
Perl_sv_pos_b2u(pTHX_ SV * const sv, I32 * const offsetp);
#define PERL_ARGS_ASSERT_SV_POS_B2U             \
        assert(offsetp)

PERL_CALLCONV STRLEN
Perl_sv_pos_b2u_flags(pTHX_ SV * const sv, STRLEN const offset, U32 flags);
#define PERL_ARGS_ASSERT_SV_POS_B2U_FLAGS       \
        assert(sv)

PERL_CALLCONV void
Perl_sv_pos_u2b(pTHX_ SV * const sv, I32 * const offsetp, I32 * const lenp);
#define PERL_ARGS_ASSERT_SV_POS_U2B             \
        assert(offsetp)

PERL_CALLCONV STRLEN
Perl_sv_pos_u2b_flags(pTHX_ SV * const sv, STRLEN uoffset, STRLEN * const lenp, U32 flags);
#define PERL_ARGS_ASSERT_SV_POS_U2B_FLAGS       \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_pvbyten_force(pTHX_ SV * const sv, STRLEN * const lp);
#define PERL_ARGS_ASSERT_SV_PVBYTEN_FORCE       \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_pvn_force_flags(pTHX_ SV * const sv, STRLEN * const lp, const U32 flags);
#define PERL_ARGS_ASSERT_SV_PVN_FORCE_FLAGS     \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_pvutf8n_force(pTHX_ SV * const sv, STRLEN * const lp);
#define PERL_ARGS_ASSERT_SV_PVUTF8N_FORCE       \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_recode_to_utf8(pTHX_ SV *sv, SV *encoding);
#define PERL_ARGS_ASSERT_SV_RECODE_TO_UTF8      \
        assert(sv); assert(encoding)

PERL_CALLCONV SV *
Perl_sv_ref(pTHX_ SV *dst, const SV * const sv, const int ob);
#define PERL_ARGS_ASSERT_SV_REF                 \
        assert(sv)

PERL_CALLCONV const char *
Perl_sv_reftype(pTHX_ const SV * const sv, const int ob)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_REFTYPE             \
        assert(sv)

PERL_CALLCONV void
Perl_sv_replace(pTHX_ SV * const sv, SV * const nsv);
#define PERL_ARGS_ASSERT_SV_REPLACE             \
        assert(sv); assert(nsv)

PERL_CALLCONV void
Perl_sv_report_used(pTHX);
#define PERL_ARGS_ASSERT_SV_REPORT_USED

PERL_CALLCONV void
Perl_sv_reset(pTHX_ const char *s, HV * const stash);
#define PERL_ARGS_ASSERT_SV_RESET               \
        assert(s)

PERL_CALLCONV void
Perl_sv_resetpvn(pTHX_ const char *s, STRLEN len, HV * const stash)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_SV_RESETPVN

PERL_CALLCONV SV *
Perl_sv_rvunweaken(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_RVUNWEAKEN          \
        assert(sv)

PERL_CALLCONV SV *
Perl_sv_rvweaken(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_RVWEAKEN            \
        assert(sv)

PERL_CALLCONV void
Perl_sv_set_bool(pTHX_ SV *sv, const bool bool_val);
#define PERL_ARGS_ASSERT_SV_SET_BOOL            \
        assert(sv)

PERL_CALLCONV void
Perl_sv_set_false(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_SV_SET_FALSE           \
        assert(sv)

PERL_CALLCONV void
Perl_sv_set_true(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_SV_SET_TRUE            \
        assert(sv)

PERL_CALLCONV void
Perl_sv_set_undef(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_SV_SET_UNDEF           \
        assert(sv)

PERL_CALLCONV void
Perl_sv_sethek(pTHX_ SV * const sv, const HEK * const hek);
#define PERL_ARGS_ASSERT_SV_SETHEK              \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setiv(pTHX_ SV * const sv, const IV num);
#define PERL_ARGS_ASSERT_SV_SETIV               \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setiv_mg(pTHX_ SV * const sv, const IV i);
#define PERL_ARGS_ASSERT_SV_SETIV_MG            \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setnv(pTHX_ SV * const sv, const NV num);
#define PERL_ARGS_ASSERT_SV_SETNV               \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setnv_mg(pTHX_ SV * const sv, const NV num);
#define PERL_ARGS_ASSERT_SV_SETNV_MG            \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setpv(pTHX_ SV * const sv, const char * const ptr);
#define PERL_ARGS_ASSERT_SV_SETPV               \
        assert(sv)

PERL_CALLCONV char  *
Perl_sv_setpv_bufsize(pTHX_ SV * const sv, const STRLEN cur, const STRLEN len);
#define PERL_ARGS_ASSERT_SV_SETPV_BUFSIZE       \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setpv_mg(pTHX_ SV * const sv, const char * const ptr);
#define PERL_ARGS_ASSERT_SV_SETPV_MG            \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setpvf(pTHX_ SV * const sv, const char * const pat, ...)
        __attribute__format__(__printf__,pTHX_2,pTHX_3);
#define PERL_ARGS_ASSERT_SV_SETPVF              \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_setpvf_mg(pTHX_ SV * const sv, const char * const pat, ...)
        __attribute__format__(__printf__,pTHX_2,pTHX_3);
#define PERL_ARGS_ASSERT_SV_SETPVF_MG           \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_setpvn(pTHX_ SV * const sv, const char * const ptr, const STRLEN len);
#define PERL_ARGS_ASSERT_SV_SETPVN              \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setpvn_fresh(pTHX_ SV * const sv, const char * const ptr, const STRLEN len);
#define PERL_ARGS_ASSERT_SV_SETPVN_FRESH        \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setpvn_mg(pTHX_ SV * const sv, const char * const ptr, const STRLEN len);
#define PERL_ARGS_ASSERT_SV_SETPVN_MG           \
        assert(sv); assert(ptr)

PERL_CALLCONV SV *
Perl_sv_setref_iv(pTHX_ SV * const rv, const char * const classname, const IV iv);
#define PERL_ARGS_ASSERT_SV_SETREF_IV           \
        assert(rv)

PERL_CALLCONV SV *
Perl_sv_setref_nv(pTHX_ SV * const rv, const char * const classname, const NV nv);
#define PERL_ARGS_ASSERT_SV_SETREF_NV           \
        assert(rv)

PERL_CALLCONV SV *
Perl_sv_setref_pv(pTHX_ SV * const rv, const char * const classname, void * const pv);
#define PERL_ARGS_ASSERT_SV_SETREF_PV           \
        assert(rv)

PERL_CALLCONV SV *
Perl_sv_setref_pvn(pTHX_ SV * const rv, const char * const classname, const char * const pv, const STRLEN n);
#define PERL_ARGS_ASSERT_SV_SETREF_PVN          \
        assert(rv); assert(pv)

PERL_CALLCONV SV *
Perl_sv_setref_uv(pTHX_ SV * const rv, const char * const classname, const UV uv);
#define PERL_ARGS_ASSERT_SV_SETREF_UV           \
        assert(rv)

PERL_CALLCONV void
Perl_sv_setrv_inc(pTHX_ SV * const sv, SV * const ref);
#define PERL_ARGS_ASSERT_SV_SETRV_INC           \
        assert(sv); assert(ref)

PERL_CALLCONV void
Perl_sv_setrv_inc_mg(pTHX_ SV * const sv, SV * const ref);
#define PERL_ARGS_ASSERT_SV_SETRV_INC_MG        \
        assert(sv); assert(ref)

PERL_CALLCONV void
Perl_sv_setrv_noinc(pTHX_ SV * const sv, SV * const ref);
#define PERL_ARGS_ASSERT_SV_SETRV_NOINC         \
        assert(sv); assert(ref)

PERL_CALLCONV void
Perl_sv_setrv_noinc_mg(pTHX_ SV * const sv, SV * const ref);
#define PERL_ARGS_ASSERT_SV_SETRV_NOINC_MG      \
        assert(sv); assert(ref)

PERL_CALLCONV void
Perl_sv_setsv_flags(pTHX_ SV *dsv, SV *ssv, const I32 flags);
#define PERL_ARGS_ASSERT_SV_SETSV_FLAGS         \
        assert(dsv)

PERL_CALLCONV void
Perl_sv_setsv_mg(pTHX_ SV * const dsv, SV * const ssv);
#define PERL_ARGS_ASSERT_SV_SETSV_MG            \
        assert(dsv)

PERL_CALLCONV void
Perl_sv_setuv(pTHX_ SV * const sv, const UV num);
#define PERL_ARGS_ASSERT_SV_SETUV               \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setuv_mg(pTHX_ SV * const sv, const UV u);
#define PERL_ARGS_ASSERT_SV_SETUV_MG            \
        assert(sv)

/* PERL_CALLCONV bool
sv_streq(pTHX_ SV *sv1, SV *sv2); */

PERL_CALLCONV bool
Perl_sv_streq_flags(pTHX_ SV *sv1, SV *sv2, const U32 flags);
#define PERL_ARGS_ASSERT_SV_STREQ_FLAGS

PERL_CALLCONV SV *
Perl_sv_string_from_errnum(pTHX_ int errnum, SV *tgtsv);
#define PERL_ARGS_ASSERT_SV_STRING_FROM_ERRNUM

PERL_CALLCONV bool
Perl_sv_tainted(pTHX_ SV * const sv)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_TAINTED             \
        assert(sv)

PERL_CALLCONV I32
Perl_sv_true(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_TRUE

PERL_CALLCONV char *
Perl_sv_uni_display(pTHX_ SV *dsv, SV *ssv, STRLEN pvlim, UV flags)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_SV_UNI_DISPLAY         \
        assert(dsv); assert(ssv)

PERL_CALLCONV int
Perl_sv_unmagic(pTHX_ SV * const sv, const int type);
#define PERL_ARGS_ASSERT_SV_UNMAGIC             \
        assert(sv)

PERL_CALLCONV int
Perl_sv_unmagicext(pTHX_ SV * const sv, const int type, const MGVTBL *vtbl);
#define PERL_ARGS_ASSERT_SV_UNMAGICEXT          \
        assert(sv)

PERL_CALLCONV void
Perl_sv_unref_flags(pTHX_ SV * const ref, const U32 flags);
#define PERL_ARGS_ASSERT_SV_UNREF_FLAGS         \
        assert(ref)

PERL_CALLCONV void
Perl_sv_untaint(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_UNTAINT             \
        assert(sv)

PERL_CALLCONV void
Perl_sv_upgrade(pTHX_ SV * const sv, svtype new_type);
#define PERL_ARGS_ASSERT_SV_UPGRADE             \
        assert(sv)

PERL_CALLCONV void
Perl_sv_usepvn_flags(pTHX_ SV * const sv, char *ptr, const STRLEN len, const U32 flags);
#define PERL_ARGS_ASSERT_SV_USEPVN_FLAGS        \
        assert(sv)

PERL_CALLCONV bool
Perl_sv_utf8_decode(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_UTF8_DECODE         \
        assert(sv)

PERL_CALLCONV bool
Perl_sv_utf8_downgrade_flags(pTHX_ SV * const sv, const bool fail_ok, const U32 flags);
#define PERL_ARGS_ASSERT_SV_UTF8_DOWNGRADE_FLAGS \
        assert(sv)

/* PERL_CALLCONV bool
sv_utf8_downgrade_nomg(pTHX_ SV * const sv, const bool fail_ok); */

PERL_CALLCONV void
Perl_sv_utf8_encode(pTHX_ SV * const sv);
#define PERL_ARGS_ASSERT_SV_UTF8_ENCODE         \
        assert(sv)

/* PERL_CALLCONV STRLEN
sv_utf8_upgrade_flags(pTHX_ SV * const sv, const I32 flags); */

PERL_CALLCONV STRLEN
Perl_sv_utf8_upgrade_flags_grow(pTHX_ SV * const sv, const I32 flags, STRLEN extra);
#define PERL_ARGS_ASSERT_SV_UTF8_UPGRADE_FLAGS_GROW \
        assert(sv)

/* PERL_CALLCONV STRLEN
sv_utf8_upgrade_nomg(pTHX_ SV *sv); */

PERL_CALLCONV void
Perl_sv_vcatpvf(pTHX_ SV * const sv, const char * const pat, va_list * const args);
#define PERL_ARGS_ASSERT_SV_VCATPVF             \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_vcatpvf_mg(pTHX_ SV * const sv, const char * const pat, va_list * const args);
#define PERL_ARGS_ASSERT_SV_VCATPVF_MG          \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_vcatpvfn(pTHX_ SV * const sv, const char * const pat, const STRLEN patlen, va_list * const args, SV ** const svargs, const Size_t sv_count, bool * const maybe_tainted);
#define PERL_ARGS_ASSERT_SV_VCATPVFN            \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_vcatpvfn_flags(pTHX_ SV * const sv, const char * const pat, const STRLEN patlen, va_list * const args, SV ** const svargs, const Size_t sv_count, bool * const maybe_tainted, const U32 flags);
#define PERL_ARGS_ASSERT_SV_VCATPVFN_FLAGS      \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_vsetpvf(pTHX_ SV * const sv, const char * const pat, va_list * const args);
#define PERL_ARGS_ASSERT_SV_VSETPVF             \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_vsetpvf_mg(pTHX_ SV * const sv, const char * const pat, va_list * const args);
#define PERL_ARGS_ASSERT_SV_VSETPVF_MG          \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_vsetpvfn(pTHX_ SV * const sv, const char * const pat, const STRLEN patlen, va_list * const args, SV ** const svargs, const Size_t sv_count, bool * const maybe_tainted);
#define PERL_ARGS_ASSERT_SV_VSETPVFN            \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_switch_to_global_locale(pTHX);
#define PERL_ARGS_ASSERT_SWITCH_TO_GLOBAL_LOCALE

PERL_CALLCONV bool
Perl_sync_locale(pTHX);
#define PERL_ARGS_ASSERT_SYNC_LOCALE

PERL_CALLCONV void
Perl_sys_init(int *argc, char ***argv);
#define PERL_ARGS_ASSERT_SYS_INIT               \
        assert(argc); assert(argv)

PERL_CALLCONV void
Perl_sys_init3(int *argc, char ***argv, char ***env);
#define PERL_ARGS_ASSERT_SYS_INIT3              \
        assert(argc); assert(argv); assert(env)

PERL_CALLCONV void
Perl_sys_term(void);
#define PERL_ARGS_ASSERT_SYS_TERM

PERL_CALLCONV void
Perl_taint_env(pTHX);
#define PERL_ARGS_ASSERT_TAINT_ENV

PERL_CALLCONV void
Perl_taint_proper(pTHX_ const char *f, const char * const s);
#define PERL_ARGS_ASSERT_TAINT_PROPER           \
        assert(s)

PERL_CALLCONV void
Perl_thread_locale_init(pTHX);
#define PERL_ARGS_ASSERT_THREAD_LOCALE_INIT

PERL_CALLCONV void
Perl_thread_locale_term(pTHX);
#define PERL_ARGS_ASSERT_THREAD_LOCALE_TERM

PERL_CALLCONV OP *
Perl_tied_method(pTHX_ SV *methname, SV **sp, SV * const sv, const MAGIC * const mg, const U32 flags, U32 argc, ...)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_TIED_METHOD            \
        assert(methname); assert(sp); assert(sv); assert(mg)

PERL_CALLCONV SSize_t
Perl_tmps_grow_p(pTHX_ SSize_t ix);
#define PERL_ARGS_ASSERT_TMPS_GROW_P

/* PERL_CALLCONV UV
to_uni_fold(pTHX_ UV c, U8 *p, STRLEN *lenp); */

PERL_CALLCONV UV
Perl_to_uni_lower(pTHX_ UV c, U8 *p, STRLEN *lenp);
#define PERL_ARGS_ASSERT_TO_UNI_LOWER           \
        assert(p); assert(lenp)

PERL_CALLCONV UV
Perl_to_uni_title(pTHX_ UV c, U8 *p, STRLEN *lenp);
#define PERL_ARGS_ASSERT_TO_UNI_TITLE           \
        assert(p); assert(lenp)

PERL_CALLCONV UV
Perl_to_uni_upper(pTHX_ UV c, U8 *p, STRLEN *lenp);
#define PERL_ARGS_ASSERT_TO_UNI_UPPER           \
        assert(p); assert(lenp)

PERL_CALLCONV bool
Perl_try_amagic_bin(pTHX_ int method, int flags);
#define PERL_ARGS_ASSERT_TRY_AMAGIC_BIN

PERL_CALLCONV bool
Perl_try_amagic_un(pTHX_ int method, int flags);
#define PERL_ARGS_ASSERT_TRY_AMAGIC_UN

PERL_CALLCONV SSize_t
Perl_unpackstring(pTHX_ const char *pat, const char *patend, const char *s, const char *strend, U32 flags);
#define PERL_ARGS_ASSERT_UNPACKSTRING           \
        assert(pat); assert(patend); assert(s); assert(strend)

PERL_CALLCONV void
Perl_unshare_hek(pTHX_ HEK *hek);
#define PERL_ARGS_ASSERT_UNSHARE_HEK

PERL_CALLCONV void
Perl_unsharepvn(pTHX_ const char *sv, I32 len, U32 hash);
#define PERL_ARGS_ASSERT_UNSHAREPVN

PERL_CALLCONV SV *
Perl_upg_version(pTHX_ SV *ver, bool qv);
#define PERL_ARGS_ASSERT_UPG_VERSION            \
        assert(ver)

PERL_CALLCONV U8 *
Perl_utf16_to_utf8(pTHX_ U8 *p, U8 *d, Size_t bytelen, Size_t *newlen);
#define PERL_ARGS_ASSERT_UTF16_TO_UTF8          \
        assert(p); assert(d); assert(newlen)

PERL_CALLCONV U8 *
Perl_utf16_to_utf8_base(pTHX_ U8 *p, U8 *d, Size_t bytelen, Size_t *newlen, const bool high, const bool low);
#define PERL_ARGS_ASSERT_UTF16_TO_UTF8_BASE     \
        assert(p); assert(d); assert(newlen)

PERL_CALLCONV U8 *
Perl_utf16_to_utf8_reversed(pTHX_ U8 *p, U8 *d, Size_t bytelen, Size_t *newlen);
#define PERL_ARGS_ASSERT_UTF16_TO_UTF8_REVERSED \
        assert(p); assert(d); assert(newlen)

PERL_CALLCONV STRLEN
Perl_utf8_length(pTHX_ const U8 *s0, const U8 *e)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_UTF8_LENGTH            \
        assert(s0); assert(e)

PERL_CALLCONV U8 *
Perl_utf8_to_bytes(pTHX_ U8 *s, STRLEN *lenp);
#define PERL_ARGS_ASSERT_UTF8_TO_BYTES          \
        assert(s); assert(lenp)

PERL_CALLCONV U8 *
Perl_utf8_to_utf16_base(pTHX_ U8 *s, U8 *d, Size_t bytelen, Size_t *newlen, const bool high, const bool low);
#define PERL_ARGS_ASSERT_UTF8_TO_UTF16_BASE     \
        assert(s); assert(d); assert(newlen)

PERL_CALLCONV UV
Perl_utf8_to_uvchr_buf(pTHX_ const U8 *s, const U8 *send, STRLEN *retlen);
#define PERL_ARGS_ASSERT_UTF8_TO_UVCHR_BUF      \
        assert(s); assert(send)

PERL_CALLCONV UV
Perl_utf8n_to_uvchr(const U8 *s, STRLEN curlen, STRLEN *retlen, const U32 flags);
#define PERL_ARGS_ASSERT_UTF8N_TO_UVCHR         \
        assert(s)

PERL_CALLCONV UV
Perl_utf8n_to_uvchr_error(const U8 *s, STRLEN curlen, STRLEN *retlen, const U32 flags, U32 *errors);
#define PERL_ARGS_ASSERT_UTF8N_TO_UVCHR_ERROR   \
        assert(s)

PERL_CALLCONV void
Perl_utilize(pTHX_ int aver, I32 floor, OP *version, OP *idop, OP *arg)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_UTILIZE                \
        assert(idop)

/* PERL_CALLCONV U8 *
uvchr_to_utf8(pTHX_ U8 *d, UV uv); */

/* PERL_CALLCONV U8 *
uvchr_to_utf8_flags(pTHX_ U8 *d, UV uv, UV flags); */

/* PERL_CALLCONV U8 *
uvchr_to_utf8_flags_msgs(pTHX_ U8 *d, UV uv, UV flags, HV **msgs); */

PERL_CALLCONV U8 *
Perl_uvoffuni_to_utf8_flags(pTHX_ U8 *d, UV uv, UV flags);
#define PERL_ARGS_ASSERT_UVOFFUNI_TO_UTF8_FLAGS \
        assert(d)

PERL_CALLCONV U8 *
Perl_uvoffuni_to_utf8_flags_msgs(pTHX_ U8 *d, UV input_uv, const UV flags, HV **msgs);
#define PERL_ARGS_ASSERT_UVOFFUNI_TO_UTF8_FLAGS_MSGS \
        assert(d)

PERL_CALLCONV U8 *
Perl_uvuni_to_utf8(pTHX_ U8 *d, UV uv);
#define PERL_ARGS_ASSERT_UVUNI_TO_UTF8          \
        assert(d)

PERL_CALLCONV bool
Perl_validate_proto(pTHX_ SV *name, SV *proto, bool warn, bool curstash);
#define PERL_ARGS_ASSERT_VALIDATE_PROTO         \
        assert(name)

PERL_CALLCONV int
Perl_vcmp(pTHX_ SV *lhv, SV *rhv);
#define PERL_ARGS_ASSERT_VCMP                   \
        assert(lhv); assert(rhv)

PERL_CALLCONV_NO_RET void
Perl_vcroak(pTHX_ const char *pat, va_list *args)
        __attribute__noreturn__;
#define PERL_ARGS_ASSERT_VCROAK

PERL_CALLCONV void
Perl_vdeb(pTHX_ const char *pat, va_list *args);
#define PERL_ARGS_ASSERT_VDEB                   \
        assert(pat)

PERL_CALLCONV char *
Perl_vform(pTHX_ const char *pat, va_list *args);
#define PERL_ARGS_ASSERT_VFORM                  \
        assert(pat)

PERL_CALLCONV void
Perl_vivify_defelem(pTHX_ SV *sv);
#define PERL_ARGS_ASSERT_VIVIFY_DEFELEM         \
        assert(sv)

PERL_CALLCONV SV *
Perl_vivify_ref(pTHX_ SV *sv, U32 to_what)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_VIVIFY_REF             \
        assert(sv)

PERL_CALLCONV void
Perl_vload_module(pTHX_ U32 flags, SV *name, SV *ver, va_list *args);
#define PERL_ARGS_ASSERT_VLOAD_MODULE           \
        assert(name)

PERL_CALLCONV SV *
Perl_vmess(pTHX_ const char *pat, va_list *args);
#define PERL_ARGS_ASSERT_VMESS                  \
        assert(pat)

PERL_CALLCONV SV *
Perl_vnewSVpvf(pTHX_ const char * const pat, va_list * const args)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_VNEWSVPVF              \
        assert(pat)

PERL_CALLCONV SV *
Perl_vnormal(pTHX_ SV *vs);
#define PERL_ARGS_ASSERT_VNORMAL                \
        assert(vs)

PERL_CALLCONV SV *
Perl_vnumify(pTHX_ SV *vs);
#define PERL_ARGS_ASSERT_VNUMIFY                \
        assert(vs)

PERL_CALLCONV SV *
Perl_vstringify(pTHX_ SV *vs);
#define PERL_ARGS_ASSERT_VSTRINGIFY             \
        assert(vs)

PERL_CALLCONV SV *
Perl_vverify(pTHX_ SV *vs);
#define PERL_ARGS_ASSERT_VVERIFY                \
        assert(vs)

PERL_CALLCONV void
Perl_vwarn(pTHX_ const char *pat, va_list *args);
#define PERL_ARGS_ASSERT_VWARN                  \
        assert(pat)

PERL_CALLCONV void
Perl_vwarner(pTHX_ U32 err, const char *pat, va_list *args);
#define PERL_ARGS_ASSERT_VWARNER                \
        assert(pat)

PERL_CALLCONV I32
Perl_wait4pid(pTHX_ Pid_t pid, int *statusp, int flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_WAIT4PID               \
        assert(statusp)

PERL_CALLCONV void
Perl_warn(pTHX_ const char *pat, ...)
        __attribute__format__(__printf__,pTHX_1,pTHX_2);
#define PERL_ARGS_ASSERT_WARN                   \
        assert(pat)

PERL_CALLCONV void
Perl_warn_sv(pTHX_ SV *baseex);
#define PERL_ARGS_ASSERT_WARN_SV                \
        assert(baseex)

PERL_CALLCONV void
Perl_warner(pTHX_ U32 err, const char *pat, ...)
        __attribute__format__(__printf__,pTHX_2,pTHX_3);
#define PERL_ARGS_ASSERT_WARNER                 \
        assert(pat)

PERL_CALLCONV I32
Perl_was_lvalue_sub(pTHX)
        __attribute__warn_unused_result__;
#define PERL_ARGS_ASSERT_WAS_LVALUE_SUB

PERL_CALLCONV void
Perl_watch(pTHX_ char **addr)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_WATCH                  \
        assert(addr)

/* PERL_CALLCONV I32
whichsig(pTHX_ const char *sig); */

PERL_CALLCONV I32
Perl_whichsig_pv(pTHX_ const char *sig);
#define PERL_ARGS_ASSERT_WHICHSIG_PV            \
        assert(sig)

PERL_CALLCONV I32
Perl_whichsig_pvn(pTHX_ const char *sig, STRLEN len);
#define PERL_ARGS_ASSERT_WHICHSIG_PVN           \
        assert(sig)

PERL_CALLCONV I32
Perl_whichsig_sv(pTHX_ SV *sigsv);
#define PERL_ARGS_ASSERT_WHICHSIG_SV            \
        assert(sigsv)

PERL_CALLCONV void
Perl_wrap_infix_plugin(pTHX_ Perl_infix_plugin_t new_plugin, Perl_infix_plugin_t *old_plugin_p);
#define PERL_ARGS_ASSERT_WRAP_INFIX_PLUGIN      \
        assert(new_plugin); assert(old_plugin_p)

PERL_CALLCONV void
Perl_wrap_keyword_plugin(pTHX_ Perl_keyword_plugin_t new_plugin, Perl_keyword_plugin_t *old_plugin_p);
#define PERL_ARGS_ASSERT_WRAP_KEYWORD_PLUGIN    \
        assert(new_plugin); assert(old_plugin_p)

PERL_CALLCONV void
Perl_wrap_op_checker(pTHX_ Optype opcode, Perl_check_t new_checker, Perl_check_t *old_checker_p);
#define PERL_ARGS_ASSERT_WRAP_OP_CHECKER        \
        assert(new_checker); assert(old_checker_p)

PERL_CALLCONV void
Perl_write_to_stderr(pTHX_ SV *msv)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_WRITE_TO_STDERR        \
        assert(msv)

PERL_CALLCONV void
Perl_xs_boot_epilog(pTHX_ const I32 ax);
#define PERL_ARGS_ASSERT_XS_BOOT_EPILOG

PERL_CALLCONV I32
Perl_xs_handshake(const U32 key, void *v_my_perl, const char *file, ...);
#define PERL_ARGS_ASSERT_XS_HANDSHAKE           \
        assert(v_my_perl); assert(file)

PERL_CALLCONV int
Perl_yyerror(pTHX_ const char * const s)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_YYERROR                \
        assert(s)

PERL_CALLCONV int
Perl_yyerror_pv(pTHX_ const char * const s, U32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_YYERROR_PV             \
        assert(s)

PERL_CALLCONV int
Perl_yyerror_pvn(pTHX_ const char * const s, STRLEN len, U32 flags)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_YYERROR_PVN

PERL_CALLCONV int
Perl_yylex(pTHX);
#define PERL_ARGS_ASSERT_YYLEX

PERL_CALLCONV int
Perl_yyparse(pTHX_ int gramtype)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_YYPARSE

PERL_CALLCONV void
Perl_yyquit(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_YYQUIT

PERL_CALLCONV void
Perl_yyunlex(pTHX)
        __attribute__visibility__("hidden");
#define PERL_ARGS_ASSERT_YYUNLEX

#if defined(DEBUGGING)
PERL_CALLCONV int
Perl_get_debug_opts(pTHX_ const char **s, bool givehelp)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_GET_DEBUG_OPTS        \
        assert(s)

PERL_CALLCONV void
Perl_hv_assert(pTHX_ HV *hv);
# define PERL_ARGS_ASSERT_HV_ASSERT             \
        assert(hv)

PERL_CALLCONV void
Perl_pad_setsv(pTHX_ PADOFFSET po, SV *sv);
# define PERL_ARGS_ASSERT_PAD_SETSV             \
        assert(sv)

PERL_CALLCONV SV *
Perl_pad_sv(pTHX_ PADOFFSET po);
# define PERL_ARGS_ASSERT_PAD_SV

PERL_CALLCONV void
Perl_set_padlist(CV *cv, PADLIST *padlist);
# define PERL_ARGS_ASSERT_SET_PADLIST           \
        assert(cv)

#endif /* defined(DEBUGGING) */
#if defined(DEBUG_LEAKING_SCALARS_FORK_DUMP)
PERL_CALLCONV void
Perl_dump_sv_child(pTHX_ SV *sv)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DUMP_SV_CHILD         \
        assert(sv)

#endif
#if !defined(EBCDIC)

# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE unsigned int
Perl_variant_byte_number(PERL_UINTMAX_T word)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_VARIANT_BYTE_NUMBER

# endif
#endif
#if defined(F_FREESP) && !defined(HAS_CHSIZE) && !defined(HAS_TRUNCATE)
PERL_CALLCONV I32
Perl_my_chsize(pTHX_ int fd, Off_t length)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_MY_CHSIZE

#endif
#if !defined(HAS_GETENV_LEN)
PERL_CALLCONV char *
Perl_getenv_len(pTHX_ const char *env_elem, unsigned long *len)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_GETENV_LEN            \
        assert(env_elem); assert(len)

#endif
#if !defined(HAS_MKOSTEMP)
PERL_CALLCONV int
Perl_my_mkostemp(char *templte, int flags)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MY_MKOSTEMP           \
        assert(templte)

#endif
#if !defined(HAS_MKSTEMP)
PERL_CALLCONV int
Perl_my_mkstemp(char *templte)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MY_MKSTEMP            \
        assert(templte)

#endif
#if defined(HAS_MSG) || defined(HAS_SEM) || defined(HAS_SHM)
PERL_CALLCONV I32
Perl_do_ipcctl(pTHX_ I32 optype, SV **mark, SV **sp)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DO_IPCCTL             \
        assert(mark); assert(sp)

PERL_CALLCONV I32
Perl_do_ipcget(pTHX_ I32 optype, SV **mark, SV **sp)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DO_IPCGET             \
        assert(mark); assert(sp)

PERL_CALLCONV I32
Perl_do_msgrcv(pTHX_ SV **mark, SV **sp)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DO_MSGRCV             \
        assert(mark); assert(sp)

PERL_CALLCONV I32
Perl_do_msgsnd(pTHX_ SV **mark, SV **sp)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DO_MSGSND             \
        assert(mark); assert(sp)

PERL_CALLCONV I32
Perl_do_semop(pTHX_ SV **mark, SV **sp)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DO_SEMOP              \
        assert(mark); assert(sp)

PERL_CALLCONV I32
Perl_do_shmio(pTHX_ I32 optype, SV **mark, SV **sp)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DO_SHMIO              \
        assert(mark); assert(sp)

#endif /* defined(HAS_MSG) || defined(HAS_SEM) || defined(HAS_SHM) */
#if defined(HAS_NL_LANGINFO) && defined(PERL_LANGINFO_H)
PERL_CALLCONV const char *
Perl_langinfo(const nl_item item);
# define PERL_ARGS_ASSERT_PERL_LANGINFO

PERL_CALLCONV const char *
Perl_langinfo8(const nl_item item, utf8ness_t *utf8ness);
# define PERL_ARGS_ASSERT_PERL_LANGINFO8

#else
PERL_CALLCONV const char *
Perl_langinfo(const int item);
# define PERL_ARGS_ASSERT_PERL_LANGINFO

PERL_CALLCONV const char *
Perl_langinfo8(const int item, utf8ness_t *utf8ness);
# define PERL_ARGS_ASSERT_PERL_LANGINFO8

#endif
#if defined(HAS_PIPE)
PERL_CALLCONV int
Perl_PerlProc_pipe_cloexec(pTHX_ int *pipefd)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_PERLPROC_PIPE_CLOEXEC \
        assert(pipefd)

#endif
#if !defined(HAS_RENAME)
PERL_CALLCONV I32
Perl_same_dirent(pTHX_ const char *a, const char *b)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SAME_DIRENT           \
        assert(a); assert(b)

#endif
#if !defined(HAS_SIGNBIT)
PERL_CALLCONV int
Perl_signbit(NV f)
        __attribute__warn_unused_result__
        __attribute__pure__;
# define PERL_ARGS_ASSERT_PERL_SIGNBIT

#endif
#if defined(HAS_SOCKET)
PERL_CALLCONV int
Perl_PerlSock_accept_cloexec(pTHX_ int listenfd, struct sockaddr *addr, Sock_size_t *addrlen)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_PERLSOCK_ACCEPT_CLOEXEC

PERL_CALLCONV int
Perl_PerlSock_socket_cloexec(pTHX_ int domain, int type, int protocol)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_PERLSOCK_SOCKET_CLOEXEC

#endif /* defined(HAS_SOCKET) */
#if   defined(HAS_SOCKETPAIR) ||                                     \
    ( defined(AF_INET) && defined(HAS_SOCKET) && defined(PF_INET) && \
      defined(SOCK_DGRAM) )
PERL_CALLCONV int
Perl_PerlSock_socketpair_cloexec(pTHX_ int domain, int type, int protocol, int *pairfd)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_PERLSOCK_SOCKETPAIR_CLOEXEC \
        assert(pairfd)

#endif
#if !defined(HAS_STRLCPY)
STATIC Size_t
Perl_my_strlcpy(char *dst, const char *src, Size_t size);
# define PERL_ARGS_ASSERT_MY_STRLCPY

#endif
#if defined(HAVE_INTERP_INTERN)
PERL_CALLCONV void
Perl_sys_intern_clear(pTHX);
# define PERL_ARGS_ASSERT_SYS_INTERN_CLEAR

PERL_CALLCONV void
Perl_sys_intern_init(pTHX);
# define PERL_ARGS_ASSERT_SYS_INTERN_INIT

# if defined(USE_ITHREADS)
PERL_CALLCONV void
Perl_sys_intern_dup(pTHX_ struct interp_intern *src, struct interp_intern *dst);
#   define PERL_ARGS_ASSERT_SYS_INTERN_DUP      \
        assert(src); assert(dst)

# endif
#endif /* defined(HAVE_INTERP_INTERN) */
#if defined(_MSC_VER)
PERL_CALLCONV int
Perl_magic_regdatum_set(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MAGIC_REGDATUM_SET    \
        assert(sv); assert(mg)

#else
PERL_CALLCONV_NO_RET int
Perl_magic_regdatum_set(pTHX_ SV *sv, MAGIC *mg)
        __attribute__noreturn__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MAGIC_REGDATUM_SET    \
        assert(sv); assert(mg)

#endif
#if defined(MULTIPLICITY)
PERL_CALLCONV_NO_RET void
Perl_croak_nocontext(const char *pat, ...)
        __attribute__noreturn__
        __attribute__format__null_ok__(__printf__,1,2);
# define PERL_ARGS_ASSERT_CROAK_NOCONTEXT

PERL_CALLCONV void
Perl_deb_nocontext(const char *pat, ...)
        __attribute__format__(__printf__,1,2);
# define PERL_ARGS_ASSERT_DEB_NOCONTEXT         \
        assert(pat)

PERL_CALLCONV_NO_RET OP *
Perl_die_nocontext(const char *pat, ...)
        __attribute__noreturn__
        __attribute__format__null_ok__(__printf__,1,2);
# define PERL_ARGS_ASSERT_DIE_NOCONTEXT

PERL_CALLCONV char *
Perl_form_nocontext(const char *pat, ...)
        __attribute__format__(__printf__,1,2);
# define PERL_ARGS_ASSERT_FORM_NOCONTEXT        \
        assert(pat)

PERL_CALLCONV void
Perl_load_module_nocontext(U32 flags, SV *name, SV *ver, ...);
# define PERL_ARGS_ASSERT_LOAD_MODULE_NOCONTEXT \
        assert(name)

PERL_CALLCONV SV *
Perl_mess_nocontext(const char *pat, ...)
        __attribute__format__(__printf__,1,2);
# define PERL_ARGS_ASSERT_MESS_NOCONTEXT        \
        assert(pat)

PERL_CALLCONV void *
Perl_my_cxt_init(pTHX_ int *indexp, size_t size);
# define PERL_ARGS_ASSERT_MY_CXT_INIT           \
        assert(indexp)

PERL_CALLCONV SV *
Perl_newSVpvf_nocontext(const char * const pat, ...)
        __attribute__format__(__printf__,1,2);
# define PERL_ARGS_ASSERT_NEWSVPVF_NOCONTEXT    \
        assert(pat)

PERL_CALLCONV void
Perl_sv_catpvf_mg_nocontext(SV * const sv, const char * const pat, ...)
        __attribute__format__(__printf__,2,3);
# define PERL_ARGS_ASSERT_SV_CATPVF_MG_NOCONTEXT \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_catpvf_nocontext(SV * const sv, const char * const pat, ...)
        __attribute__format__(__printf__,2,3);
# define PERL_ARGS_ASSERT_SV_CATPVF_NOCONTEXT   \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_setpvf_mg_nocontext(SV * const sv, const char * const pat, ...)
        __attribute__format__(__printf__,2,3);
# define PERL_ARGS_ASSERT_SV_SETPVF_MG_NOCONTEXT \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_sv_setpvf_nocontext(SV * const sv, const char * const pat, ...)
        __attribute__format__(__printf__,2,3);
# define PERL_ARGS_ASSERT_SV_SETPVF_NOCONTEXT   \
        assert(sv); assert(pat)

PERL_CALLCONV void
Perl_warn_nocontext(const char *pat, ...)
        __attribute__format__(__printf__,1,2);
# define PERL_ARGS_ASSERT_WARN_NOCONTEXT        \
        assert(pat)

PERL_CALLCONV void
Perl_warner_nocontext(U32 err, const char *pat, ...)
        __attribute__format__(__printf__,2,3);
# define PERL_ARGS_ASSERT_WARNER_NOCONTEXT      \
        assert(pat)

#endif /* defined(MULTIPLICITY) */
#if defined(MYMALLOC)
PERL_CALLCONV void
Perl_dump_mstats(pTHX_ const char *s);
# define PERL_ARGS_ASSERT_DUMP_MSTATS           \
        assert(s)

PERL_CALLCONV int
Perl_get_mstats(pTHX_ perl_mstats_t *buf, int buflen, int level);
# define PERL_ARGS_ASSERT_GET_MSTATS            \
        assert(buf)

PERL_CALLCONV MEM_SIZE
Perl_malloc_good_size(size_t nbytes)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MALLOC_GOOD_SIZE

PERL_CALLCONV MEM_SIZE
Perl_malloced_size(void *p)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MALLOCED_SIZE         \
        assert(p)

#endif /* defined(MYMALLOC) */
#if !defined(NO_MATHOMS)
PERL_CALLCONV bool
Perl_do_aexec(pTHX_ SV *really, SV **mark, SV **sp);
# define PERL_ARGS_ASSERT_DO_AEXEC              \
        assert(mark); assert(sp)

PERL_CALLCONV bool
Perl_do_open(pTHX_ GV *gv, const char *name, I32 len, int as_raw, int rawmode, int rawperm, PerlIO *supplied_fp);
# define PERL_ARGS_ASSERT_DO_OPEN               \
        assert(gv); assert(name)

PERL_CALLCONV GV *
Perl_gv_AVadd(pTHX_ GV *gv);
# define PERL_ARGS_ASSERT_GV_AVADD

PERL_CALLCONV GV *
Perl_gv_HVadd(pTHX_ GV *gv);
# define PERL_ARGS_ASSERT_GV_HVADD

PERL_CALLCONV GV *
Perl_gv_IOadd(pTHX_ GV *gv);
# define PERL_ARGS_ASSERT_GV_IOADD

PERL_CALLCONV void
Perl_gv_efullname3(pTHX_ SV *sv, const GV *gv, const char *prefix);
# define PERL_ARGS_ASSERT_GV_EFULLNAME3         \
        assert(sv); assert(gv)

PERL_CALLCONV GV *
Perl_gv_fetchmethod(pTHX_ HV *stash, const char *name);
# define PERL_ARGS_ASSERT_GV_FETCHMETHOD        \
        assert(stash); assert(name)

PERL_CALLCONV void
Perl_gv_fullname3(pTHX_ SV *sv, const GV *gv, const char *prefix);
# define PERL_ARGS_ASSERT_GV_FULLNAME3          \
        assert(sv); assert(gv)

PERL_CALLCONV SV *
Perl_hv_delete(pTHX_ HV *hv, const char *key, I32 klen, I32 flags);
# define PERL_ARGS_ASSERT_HV_DELETE             \
        assert(key)

PERL_CALLCONV SV *
Perl_hv_delete_ent(pTHX_ HV *hv, SV *keysv, I32 flags, U32 hash);
# define PERL_ARGS_ASSERT_HV_DELETE_ENT         \
        assert(keysv)

PERL_CALLCONV bool
Perl_hv_exists(pTHX_ HV *hv, const char *key, I32 klen)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_HV_EXISTS             \
        assert(key)

PERL_CALLCONV bool
Perl_hv_exists_ent(pTHX_ HV *hv, SV *keysv, U32 hash)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_HV_EXISTS_ENT         \
        assert(keysv)

PERL_CALLCONV SV **
Perl_hv_fetch(pTHX_ HV *hv, const char *key, I32 klen, I32 lval);
# define PERL_ARGS_ASSERT_HV_FETCH              \
        assert(key)

PERL_CALLCONV HE *
Perl_hv_fetch_ent(pTHX_ HV *hv, SV *keysv, I32 lval, U32 hash);
# define PERL_ARGS_ASSERT_HV_FETCH_ENT          \
        assert(keysv)

PERL_CALLCONV HE *
Perl_hv_iternext(pTHX_ HV *hv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_HV_ITERNEXT           \
        assert(hv)

PERL_CALLCONV void
Perl_hv_magic(pTHX_ HV *hv, GV *gv, int how);
# define PERL_ARGS_ASSERT_HV_MAGIC              \
        assert(hv)

PERL_CALLCONV SV **
Perl_hv_store(pTHX_ HV *hv, const char *key, I32 klen, SV *val, U32 hash);
# define PERL_ARGS_ASSERT_HV_STORE

PERL_CALLCONV HE *
Perl_hv_store_ent(pTHX_ HV *hv, SV *key, SV *val, U32 hash);
# define PERL_ARGS_ASSERT_HV_STORE_ENT

PERL_CALLCONV SV **
Perl_hv_store_flags(pTHX_ HV *hv, const char *key, I32 klen, SV *val, U32 hash, int flags);
# define PERL_ARGS_ASSERT_HV_STORE_FLAGS

PERL_CALLCONV char *
Perl_instr(const char *big, const char *little)
        __attribute__warn_unused_result__
        __attribute__pure__;
# define PERL_ARGS_ASSERT_INSTR                 \
        assert(big); assert(little)

PERL_CALLCONV STRLEN
Perl_is_utf8_char_buf(const U8 *buf, const U8 *buf_end);
# define PERL_ARGS_ASSERT_IS_UTF8_CHAR_BUF      \
        assert(buf); assert(buf_end)

PERL_CALLCONV bool
Perl_is_utf8_string_loc(const U8 *s, const STRLEN len, const U8 **ep);
# define PERL_ARGS_ASSERT_IS_UTF8_STRING_LOC    \
        assert(s); assert(ep)

PERL_CALLCONV AV *
Perl_newAV(pTHX)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEWAV

PERL_CALLCONV HV *
Perl_newHV(pTHX)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEWHV

PERL_CALLCONV IO *
Perl_newIO(pTHX)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEWIO

PERL_CALLCONV CV *
Perl_newSUB(pTHX_ I32 floor, OP *o, OP *proto, OP *block);
# define PERL_ARGS_ASSERT_NEWSUB

PERL_CALLCONV SV *
Perl_newSVsv(pTHX_ SV * const old)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEWSVSV

PERL_CALLCONV void
Perl_save_freeop(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_SAVE_FREEOP

PERL_CALLCONV void
Perl_save_freepv(pTHX_ char *pv);
# define PERL_ARGS_ASSERT_SAVE_FREEPV

PERL_CALLCONV void
Perl_save_freesv(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SAVE_FREESV

PERL_CALLCONV void
Perl_save_mortalizesv(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SAVE_MORTALIZESV      \
        assert(sv)

PERL_CALLCONV void
Perl_save_op(pTHX);
# define PERL_ARGS_ASSERT_SAVE_OP

PERL_CALLCONV bool
Perl_sv_2bool(pTHX_ SV * const sv);
# define PERL_ARGS_ASSERT_SV_2BOOL              \
        assert(sv)

PERL_CALLCONV IV
Perl_sv_2iv(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SV_2IV                \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_2pv(pTHX_ SV *sv, STRLEN *lp);
# define PERL_ARGS_ASSERT_SV_2PV                \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_2pv_nolen(pTHX_ SV *sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_2PV_NOLEN          \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_2pvbyte(pTHX_ SV *sv, STRLEN * const lp);
# define PERL_ARGS_ASSERT_SV_2PVBYTE            \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_2pvbyte_nolen(pTHX_ SV *sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_2PVBYTE_NOLEN      \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_2pvutf8(pTHX_ SV *sv, STRLEN * const lp);
# define PERL_ARGS_ASSERT_SV_2PVUTF8            \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_2pvutf8_nolen(pTHX_ SV *sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_2PVUTF8_NOLEN      \
        assert(sv)

PERL_CALLCONV UV
Perl_sv_2uv(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SV_2UV                \
        assert(sv)

PERL_CALLCONV void
Perl_sv_catpvn(pTHX_ SV *dsv, const char *sstr, STRLEN len);
# define PERL_ARGS_ASSERT_SV_CATPVN             \
        assert(dsv); assert(sstr)

PERL_CALLCONV void
Perl_sv_catpvn_mg(pTHX_ SV *dsv, const char *sstr, STRLEN len);
# define PERL_ARGS_ASSERT_SV_CATPVN_MG          \
        assert(dsv); assert(sstr)

PERL_CALLCONV void
Perl_sv_catsv(pTHX_ SV *dsv, SV *sstr);
# define PERL_ARGS_ASSERT_SV_CATSV              \
        assert(dsv)

PERL_CALLCONV void
Perl_sv_catsv_mg(pTHX_ SV *dsv, SV *sstr);
# define PERL_ARGS_ASSERT_SV_CATSV_MG           \
        assert(dsv)

PERL_CALLCONV void
Perl_sv_copypv(pTHX_ SV * const dsv, SV * const ssv);
# define PERL_ARGS_ASSERT_SV_COPYPV             \
        assert(dsv); assert(ssv)

PERL_CALLCONV I32
Perl_sv_eq(pTHX_ SV *sv1, SV *sv2);
# define PERL_ARGS_ASSERT_SV_EQ

PERL_CALLCONV void
Perl_sv_force_normal(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SV_FORCE_NORMAL       \
        assert(sv)

PERL_CALLCONV void
Perl_sv_insert(pTHX_ SV * const bigstr, const STRLEN offset, const STRLEN len, const char * const little, const STRLEN littlelen);
# define PERL_ARGS_ASSERT_SV_INSERT             \
        assert(bigstr); assert(little)

PERL_CALLCONV SV *
Perl_sv_mortalcopy(pTHX_ SV * const oldsv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_MORTALCOPY

PERL_CALLCONV void
Perl_sv_nolocking(pTHX_ SV *sv)
        __attribute__deprecated__;
# define PERL_ARGS_ASSERT_SV_NOLOCKING

PERL_CALLCONV void
Perl_sv_nounlocking(pTHX_ SV *sv)
        __attribute__deprecated__;
# define PERL_ARGS_ASSERT_SV_NOUNLOCKING

PERL_CALLCONV char *
Perl_sv_pv(pTHX_ SV *sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_PV                 \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_pvbyte(pTHX_ SV *sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_PVBYTE             \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_pvn_force(pTHX_ SV *sv, STRLEN *lp);
# define PERL_ARGS_ASSERT_SV_PVN_FORCE          \
        assert(sv)

PERL_CALLCONV char *
Perl_sv_pvutf8(pTHX_ SV *sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_PVUTF8             \
        assert(sv)

PERL_CALLCONV void
Perl_sv_setsv(pTHX_ SV *dsv, SV *ssv);
# define PERL_ARGS_ASSERT_SV_SETSV              \
        assert(dsv)

PERL_CALLCONV void
Perl_sv_taint(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SV_TAINT              \
        assert(sv)

PERL_CALLCONV void
Perl_sv_unref(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SV_UNREF              \
        assert(sv)

PERL_CALLCONV void
Perl_sv_usepvn(pTHX_ SV *sv, char *ptr, STRLEN len);
# define PERL_ARGS_ASSERT_SV_USEPVN             \
        assert(sv)

PERL_CALLCONV void
Perl_sv_usepvn_mg(pTHX_ SV *sv, char *ptr, STRLEN len);
# define PERL_ARGS_ASSERT_SV_USEPVN_MG          \
        assert(sv)

PERL_CALLCONV bool
Perl_sv_utf8_downgrade(pTHX_ SV * const sv, const bool fail_ok);
# define PERL_ARGS_ASSERT_SV_UTF8_DOWNGRADE     \
        assert(sv)

PERL_CALLCONV STRLEN
Perl_sv_utf8_upgrade(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SV_UTF8_UPGRADE       \
        assert(sv)

PERL_CALLCONV UV
Perl_utf8_to_uvchr(pTHX_ const U8 *s, STRLEN *retlen)
        __attribute__deprecated__;
# define PERL_ARGS_ASSERT_UTF8_TO_UVCHR         \
        assert(s)

PERL_CALLCONV UV
Perl_utf8_to_uvuni(pTHX_ const U8 *s, STRLEN *retlen)
        __attribute__deprecated__;
# define PERL_ARGS_ASSERT_UTF8_TO_UVUNI         \
        assert(s)

PERL_CALLCONV UV
Perl_utf8n_to_uvuni(pTHX_ const U8 *s, STRLEN curlen, STRLEN *retlen, U32 flags)
        __attribute__deprecated__;
# define PERL_ARGS_ASSERT_UTF8N_TO_UVUNI        \
        assert(s)

# if defined(PERL_DONT_CREATE_GVSV)
PERL_CALLCONV GV *
Perl_gv_SVadd(pTHX_ GV *gv);
#   define PERL_ARGS_ASSERT_GV_SVADD

# endif
# if defined(PERL_IN_MATHOMS_C) || defined(PERL_IN_OP_C) || \
     defined(PERL_IN_PERLY_C)   || defined(PERL_IN_TOKE_C)
PERL_CALLCONV OP *
Perl_ref(pTHX_ OP *o, I32 type);
#   define PERL_ARGS_ASSERT_REF

# endif
# if defined(USE_LOCALE_COLLATE)
PERL_CALLCONV char *
Perl_sv_collxfrm(pTHX_ SV * const sv, STRLEN * const nxp);
#   define PERL_ARGS_ASSERT_SV_COLLXFRM         \
        assert(sv); assert(nxp)

# endif
#endif /* !defined(NO_MATHOMS) */
#if defined(PERL_ANY_COW)
PERL_CALLCONV SV *
Perl_sv_setsv_cow(pTHX_ SV *dsv, SV *ssv);
# define PERL_ARGS_ASSERT_SV_SETSV_COW          \
        assert(ssv)

#endif
#if defined(PERL_CORE)
PERL_CALLCONV void
Perl_opslab_force_free(pTHX_ OPSLAB *slab)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_OPSLAB_FORCE_FREE     \
        assert(slab)

PERL_CALLCONV void
Perl_opslab_free(pTHX_ OPSLAB *slab)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_OPSLAB_FREE           \
        assert(slab)

PERL_CALLCONV void
Perl_opslab_free_nopad(pTHX_ OPSLAB *slab)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_OPSLAB_FREE_NOPAD     \
        assert(slab)

PERL_CALLCONV void
Perl_parser_free_nexttoke_ops(pTHX_ yy_parser *parser, OPSLAB *slab)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_PARSER_FREE_NEXTTOKE_OPS \
        assert(parser); assert(slab)

# if defined(PERL_DEBUG_READONLY_OPS)
PERL_CALLCONV void
Perl_Slab_to_ro(pTHX_ OPSLAB *slab);
#   define PERL_ARGS_ASSERT_SLAB_TO_RO          \
        assert(slab)

PERL_CALLCONV void
Perl_Slab_to_rw(pTHX_ OPSLAB * const slab);
#   define PERL_ARGS_ASSERT_SLAB_TO_RW          \
        assert(slab)

# endif /* defined(PERL_DEBUG_READONLY_OPS) */
# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE bool
S_should_warn_nl(const char *pv)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_SHOULD_WARN_NL      \
        assert(pv)

# endif
#endif /* defined(PERL_CORE) */
#if defined(PERL_CORE) || defined(PERL_EXT)
PERL_CALLCONV bool
Perl_isSCRIPT_RUN(pTHX_ const U8 *s, const U8 *send, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISSCRIPT_RUN          \
        assert(s); assert(send)

#endif /* defined(PERL_CORE) || defined(PERL_EXT) */
#if defined(PERL_CORE) || defined(PERL_USE_VOLATILE_API)
PERL_CALLCONV void
Perl_finalize_optree(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_FINALIZE_OPTREE       \
        assert(o)

PERL_CALLCONV void
Perl_optimize_optree(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_OPTIMIZE_OPTREE       \
        assert(o)

#endif /* defined(PERL_CORE) || defined(PERL_USE_VOLATILE_API) */
#if defined(PERL_DEBUG_READONLY_COW)
PERL_CALLCONV void
Perl_sv_buf_to_ro(pTHX_ SV *sv)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SV_BUF_TO_RO          \
        assert(sv)

#endif
#if defined(PERL_DEBUG_READONLY_OPS)
PERL_CALLCONV PADOFFSET
Perl_op_refcnt_dec(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_OP_REFCNT_DEC         \
        assert(o)

PERL_CALLCONV OP *
Perl_op_refcnt_inc(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_OP_REFCNT_INC

#endif /* defined(PERL_DEBUG_READONLY_OPS) */
#if defined(PERL_DEFAULT_DO_EXEC3_IMPLEMENTATION)
PERL_CALLCONV bool
Perl_do_exec(pTHX_ const char *cmd)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DO_EXEC               \
        assert(cmd)

#else
PERL_CALLCONV bool
Perl_do_exec(pTHX_ const char *cmd)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DO_EXEC               \
        assert(cmd)

#endif
#if defined(PERL_IMPLICIT_SYS)
PERL_CALLCONV PerlInterpreter *
perl_alloc_using(struct IPerlMem *ipM, struct IPerlMem *ipMS, struct IPerlMem *ipMP, struct IPerlEnv *ipE, struct IPerlStdIO *ipStd, struct IPerlLIO *ipLIO, struct IPerlDir *ipD, struct IPerlSock *ipS, struct IPerlProc *ipP);
# define PERL_ARGS_ASSERT_PERL_ALLOC_USING      \
        assert(ipM); assert(ipMS); assert(ipMP); assert(ipE); assert(ipStd); \
        assert(ipLIO); assert(ipD); assert(ipS); assert(ipP)

# if defined(USE_ITHREADS)
PERL_CALLCONV PerlInterpreter *
perl_clone_using(PerlInterpreter *proto_perl, UV flags, struct IPerlMem *ipM, struct IPerlMem *ipMS, struct IPerlMem *ipMP, struct IPerlEnv *ipE, struct IPerlStdIO *ipStd, struct IPerlLIO *ipLIO, struct IPerlDir *ipD, struct IPerlSock *ipS, struct IPerlProc *ipP);
#   define PERL_ARGS_ASSERT_PERL_CLONE_USING    \
        assert(proto_perl); assert(ipM); assert(ipMS); assert(ipMP); assert(ipE); \
        assert(ipStd); assert(ipLIO); assert(ipD); assert(ipS); assert(ipP)

# endif
#else /* if !defined(PERL_IMPLICIT_SYS) */
PERL_CALLCONV I32
Perl_my_pclose(pTHX_ PerlIO *ptr);
# define PERL_ARGS_ASSERT_MY_PCLOSE

PERL_CALLCONV PerlIO *
Perl_my_popen(pTHX_ const char *cmd, const char *mode);
# define PERL_ARGS_ASSERT_MY_POPEN              \
        assert(cmd); assert(mode)

#endif /* !defined(PERL_IMPLICIT_SYS) */
#if defined(PERL_IN_AV_C)
STATIC MAGIC *
S_get_aux_mg(pTHX_ AV *av);
# define PERL_ARGS_ASSERT_GET_AUX_MG            \
        assert(av)

#endif
#if defined(PERL_IN_CLASS_C) || defined(PERL_IN_OP_C)    || \
    defined(PERL_IN_PAD_C)   || defined(PERL_IN_PERLY_C) || \
    defined(PERL_IN_TOKE_C)
PERL_CALLCONV void
Perl_class_add_ADJUST(pTHX_ HV *stash, CV *cv);
# define PERL_ARGS_ASSERT_CLASS_ADD_ADJUST      \
        assert(stash); assert(cv)

PERL_CALLCONV void
Perl_class_add_field(pTHX_ HV *stash, PADNAME *pn);
# define PERL_ARGS_ASSERT_CLASS_ADD_FIELD       \
        assert(stash); assert(pn)

PERL_CALLCONV void
Perl_class_apply_attributes(pTHX_ HV *stash, OP *attrlist);
# define PERL_ARGS_ASSERT_CLASS_APPLY_ATTRIBUTES \
        assert(stash)

PERL_CALLCONV void
Perl_class_apply_field_attributes(pTHX_ PADNAME *pn, OP *attrlist);
# define PERL_ARGS_ASSERT_CLASS_APPLY_FIELD_ATTRIBUTES \
        assert(pn)

PERL_CALLCONV void
Perl_class_prepare_initfield_parse(pTHX);
# define PERL_ARGS_ASSERT_CLASS_PREPARE_INITFIELD_PARSE

PERL_CALLCONV void
Perl_class_prepare_method_parse(pTHX_ CV *cv);
# define PERL_ARGS_ASSERT_CLASS_PREPARE_METHOD_PARSE \
        assert(cv)

PERL_CALLCONV void
Perl_class_seal_stash(pTHX_ HV *stash);
# define PERL_ARGS_ASSERT_CLASS_SEAL_STASH      \
        assert(stash)

PERL_CALLCONV void
Perl_class_set_field_defop(pTHX_ PADNAME *pn, OPCODE defmode, OP *defop);
# define PERL_ARGS_ASSERT_CLASS_SET_FIELD_DEFOP \
        assert(pn); assert(defop)

PERL_CALLCONV void
Perl_class_setup_stash(pTHX_ HV *stash);
# define PERL_ARGS_ASSERT_CLASS_SETUP_STASH     \
        assert(stash)

PERL_CALLCONV OP *
Perl_class_wrap_method_body(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_CLASS_WRAP_METHOD_BODY

PERL_CALLCONV void
Perl_croak_kw_unless_class(pTHX_ const char *kw);
# define PERL_ARGS_ASSERT_CROAK_KW_UNLESS_CLASS \
        assert(kw)

#endif /* defined(PERL_IN_CLASS_C) || defined(PERL_IN_OP_C)    ||
          defined(PERL_IN_PAD_C)   || defined(PERL_IN_PERLY_C) ||
          defined(PERL_IN_TOKE_C) */
#if defined(PERL_IN_DEB_C)
STATIC void
S_deb_stack_n(pTHX_ SV **stack_base, I32 stack_min, I32 stack_max, I32 mark_min, I32 mark_max);
# define PERL_ARGS_ASSERT_DEB_STACK_N           \
        assert(stack_base)

#endif
#if defined(PERL_IN_DOIO_C)
STATIC bool
S_argvout_final(pTHX_ MAGIC *mg, IO *io, bool is_explicit);
# define PERL_ARGS_ASSERT_ARGVOUT_FINAL         \
        assert(mg); assert(io)

STATIC void
S_exec_failed(pTHX_ const char *cmd, int fd, int do_report);
# define PERL_ARGS_ASSERT_EXEC_FAILED           \
        assert(cmd)

STATIC bool
S_ingroup(pTHX_ Gid_t testgid, bool effective)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_INGROUP

STATIC bool
S_openn_cleanup(pTHX_ GV *gv, IO *io, PerlIO *fp, char *mode, const char *oname, PerlIO *saveifp, PerlIO *saveofp, int savefd, char savetype, int writing, bool was_fdopen, const char *type, Stat_t *statbufp);
# define PERL_ARGS_ASSERT_OPENN_CLEANUP         \
        assert(gv); assert(io); assert(mode); assert(oname)

STATIC IO *
S_openn_setup(pTHX_ GV *gv, char *mode, PerlIO **saveifp, PerlIO **saveofp, int *savefd, char *savetype);
# define PERL_ARGS_ASSERT_OPENN_SETUP           \
        assert(gv); assert(mode); assert(saveifp); assert(saveofp); assert(savefd); \
        assert(savetype)

#endif /* defined(PERL_IN_DOIO_C) */
#if defined(PERL_IN_DOOP_C)
STATIC Size_t
S_do_trans_complex(pTHX_ SV * const sv, const OPtrans_map * const tbl)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DO_TRANS_COMPLEX      \
        assert(sv); assert(tbl)

STATIC Size_t
S_do_trans_count(pTHX_ SV * const sv, const OPtrans_map * const tbl)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DO_TRANS_COUNT        \
        assert(sv); assert(tbl)

STATIC Size_t
S_do_trans_count_invmap(pTHX_ SV * const sv, AV * const map)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DO_TRANS_COUNT_INVMAP \
        assert(sv); assert(map)

STATIC Size_t
S_do_trans_invmap(pTHX_ SV * const sv, AV * const map)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DO_TRANS_INVMAP       \
        assert(sv); assert(map)

STATIC Size_t
S_do_trans_simple(pTHX_ SV * const sv, const OPtrans_map * const tbl)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DO_TRANS_SIMPLE       \
        assert(sv); assert(tbl)

#endif /* defined(PERL_IN_DOOP_C) */
#if defined(PERL_IN_DOOP_C)    || defined(PERL_IN_OP_C)        || \
    defined(PERL_IN_PP_C)      || defined(PERL_IN_REGCOMP_ANY) || \
    defined(PERL_IN_REGEXEC_C) || defined(PERL_IN_TOKE_C)      || \
    defined(PERL_IN_UTF8_C)

PERL_CALLCONV SSize_t
Perl__invlist_search(SV * const invlist, const UV cp)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT__INVLIST_SEARCH       \
        assert(invlist)

#endif /* defined(PERL_IN_DOOP_C)    || defined(PERL_IN_OP_C)        ||
          defined(PERL_IN_PP_C)      || defined(PERL_IN_REGCOMP_ANY) ||
          defined(PERL_IN_REGEXEC_C) || defined(PERL_IN_TOKE_C)      ||
          defined(PERL_IN_UTF8_C) */
#if defined(PERL_IN_DOOP_C) || defined(PERL_IN_OP_C) || \
    defined(PERL_IN_REGCOMP_ANY)

#endif
#if defined(PERL_IN_DOOP_C)      || defined(PERL_IN_OP_C) || \
    defined(PERL_IN_REGCOMP_ANY) || defined(PERL_IN_UTF8_C)
PERL_CALLCONV SV *
Perl__add_range_to_invlist(pTHX_ SV *invlist, UV start, UV end)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT__ADD_RANGE_TO_INVLIST

/* PERL_CALLCONV void
_invlist_intersection(pTHX_ SV * const a, SV * const b, SV **i); */

PERL_CALLCONV void
Perl__invlist_intersection_maybe_complement_2nd(pTHX_ SV * const a, SV * const b, const bool complement_b, SV **i);
# define PERL_ARGS_ASSERT__INVLIST_INTERSECTION_MAYBE_COMPLEMENT_2ND \
        assert(b); assert(i)

PERL_CALLCONV void
Perl__invlist_invert(pTHX_ SV * const invlist);
# define PERL_ARGS_ASSERT__INVLIST_INVERT       \
        assert(invlist)

/* PERL_CALLCONV void
_invlist_subtract(pTHX_ SV * const a, SV * const b, SV **result); */

/* PERL_CALLCONV void
_invlist_union(pTHX_ SV * const a, SV * const b, SV **output); */

PERL_CALLCONV void
Perl__invlist_union_maybe_complement_2nd(pTHX_ SV * const a, SV * const b, const bool complement_b, SV **output);
# define PERL_ARGS_ASSERT__INVLIST_UNION_MAYBE_COMPLEMENT_2ND \
        assert(b); assert(output)

PERL_CALLCONV SV *
Perl__new_invlist(pTHX_ IV initial_size)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT__NEW_INVLIST

PERL_CALLCONV SV *
Perl__setup_canned_invlist(pTHX_ const STRLEN size, const UV element0, UV **other_elements_ptr)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT__SETUP_CANNED_INVLIST \
        assert(other_elements_ptr)

#endif /* defined(PERL_IN_DOOP_C)      || defined(PERL_IN_OP_C) ||
          defined(PERL_IN_REGCOMP_ANY) || defined(PERL_IN_UTF8_C) */
#if defined(PERL_IN_DQUOTE_C) || defined(PERL_IN_REGCOMP_C) || \
    defined(PERL_IN_TOKE_C)
PERL_CALLCONV const char *
Perl_form_alien_digit_msg(pTHX_ const U8 which, const STRLEN valids_len, const char * const first_bad, const char * const send, const bool UTF, const bool braced)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_FORM_ALIEN_DIGIT_MSG  \
        assert(first_bad); assert(send)

PERL_CALLCONV bool
Perl_grok_bslash_c(pTHX_ const char source, U8 *result, const char **message, U32 *packed_warn)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_GROK_BSLASH_C         \
        assert(result); assert(message)

PERL_CALLCONV bool
Perl_grok_bslash_o(pTHX_ char **s, const char * const send, UV *uv, const char **message, U32 *packed_warn, const bool strict, const bool allow_UV_MAX, const bool utf8)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_GROK_BSLASH_O         \
        assert(s); assert(send); assert(uv); assert(message)

PERL_CALLCONV bool
Perl_grok_bslash_x(pTHX_ char **s, const char * const send, UV *uv, const char **message, U32 *packed_warn, const bool strict, const bool allow_UV_MAX, const bool utf8)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_GROK_BSLASH_X         \
        assert(s); assert(send); assert(uv); assert(message)

#endif /* defined(PERL_IN_DQUOTE_C) || defined(PERL_IN_REGCOMP_C) ||
          defined(PERL_IN_TOKE_C) */
#if defined(PERL_IN_DQUOTE_C) || defined(PERL_IN_REGCOMP_C) || \
    defined(PERL_IN_TOKE_C)   || defined(PERL_IN_UTF8_C)
PERL_CALLCONV const char *
Perl_form_cp_too_large_msg(pTHX_ const U8 which, const char *string, const Size_t len, const UV cp)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_FORM_CP_TOO_LARGE_MSG

#endif
#if defined(PERL_IN_DUMP_C)
STATIC CV *
S_deb_curcv(pTHX_ I32 ix);
# define PERL_ARGS_ASSERT_DEB_CURCV

STATIC void
S_debprof(pTHX_ const OP *o);
# define PERL_ARGS_ASSERT_DEBPROF               \
        assert(o)

STATIC SV *
S_pm_description(pTHX_ const PMOP *pm);
# define PERL_ARGS_ASSERT_PM_DESCRIPTION        \
        assert(pm)

STATIC UV
S_sequence_num(pTHX_ const OP *o);
# define PERL_ARGS_ASSERT_SEQUENCE_NUM

#endif /* defined(PERL_IN_DUMP_C) */
#if defined(PERL_IN_DUMP_C)  || defined(PERL_IN_HV_C) || \
    defined(PERL_IN_SCOPE_C) || defined(PERL_IN_SV_C)
PERL_CALLCONV void
Perl_hv_kill_backrefs(pTHX_ HV *hv)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_HV_KILL_BACKREFS      \
        assert(hv)

#endif
#if defined(PERL_IN_DUMP_C) || defined(PERL_IN_OP_C) || \
    defined(PERL_IN_REGCOMP_ANY)
PERL_CALLCONV void
Perl__invlist_dump(pTHX_ PerlIO *file, I32 level, const char * const indent, SV * const invlist);
# define PERL_ARGS_ASSERT__INVLIST_DUMP         \
        assert(file); assert(indent); assert(invlist)

#endif
#if defined(PERL_IN_GLOBALS_C) || defined(PERL_IN_OP_C) || \
    defined(PERL_IN_PEEP_C)
PERL_CALLCONV OP *
Perl_ck_anoncode(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_ANONCODE           \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_backtick(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_BACKTICK           \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_bitop(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_BITOP              \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_cmp(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_CMP                \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_concat(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_CONCAT             \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_defined(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_DEFINED            \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_delete(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_DELETE             \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_each(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_EACH               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_eof(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_EOF                \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_eval(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_EVAL               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_exec(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_EXEC               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_exists(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_EXISTS             \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_ftst(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_FTST               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_fun(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_FUN                \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_glob(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_GLOB               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_grep(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_GREP               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_helemexistsor(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_HELEMEXISTSOR      \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_index(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_INDEX              \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_isa(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_ISA                \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_join(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_JOIN               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_length(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_LENGTH             \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_lfun(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_LFUN               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_listiob(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_LISTIOB            \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_match(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_MATCH              \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_method(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_METHOD             \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_null(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_NULL               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_open(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_OPEN               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_prototype(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_PROTOTYPE          \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_readline(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_READLINE           \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_refassign(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_REFASSIGN          \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_repeat(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_REPEAT             \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_require(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_REQUIRE            \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_return(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_RETURN             \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_rfun(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_RFUN               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_rvconst(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_RVCONST            \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_sassign(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SASSIGN            \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_select(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SELECT             \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_shift(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SHIFT              \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_smartmatch(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SMARTMATCH         \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_sort(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SORT               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_spair(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SPAIR              \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_split(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SPLIT              \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_stringify(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_STRINGIFY          \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_subr(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SUBR               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_substr(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SUBSTR             \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_svconst(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_SVCONST            \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_tell(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_TELL               \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_trunc(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_TRUNC              \
        assert(o)

PERL_CALLCONV OP *
Perl_ck_trycatch(pTHX_ OP *o)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CK_TRYCATCH           \
        assert(o)

#endif /* defined(PERL_IN_GLOBALS_C) || defined(PERL_IN_OP_C) ||
          defined(PERL_IN_PEEP_C) */
#if defined(PERL_IN_GV_C)
STATIC bool
S_find_default_stash(pTHX_ HV **stash, const char *name, STRLEN len, const U32 is_utf8, const I32 add, const svtype sv_type);
# define PERL_ARGS_ASSERT_FIND_DEFAULT_STASH    \
        assert(stash); assert(name)

STATIC void
S_gv_init_svtype(pTHX_ GV *gv, const svtype sv_type);
# define PERL_ARGS_ASSERT_GV_INIT_SVTYPE        \
        assert(gv)

STATIC bool
S_gv_is_in_main(pTHX_ const char *name, STRLEN len, const U32 is_utf8);
# define PERL_ARGS_ASSERT_GV_IS_IN_MAIN         \
        assert(name)

STATIC bool
S_gv_magicalize(pTHX_ GV *gv, HV *stash, const char *name, STRLEN len, const svtype sv_type);
# define PERL_ARGS_ASSERT_GV_MAGICALIZE         \
        assert(gv); assert(stash); assert(name)

STATIC void
S_gv_magicalize_isa(pTHX_ GV *gv);
# define PERL_ARGS_ASSERT_GV_MAGICALIZE_ISA     \
        assert(gv)

STATIC void
S_maybe_multimagic_gv(pTHX_ GV *gv, const char *name, const svtype sv_type);
# define PERL_ARGS_ASSERT_MAYBE_MULTIMAGIC_GV   \
        assert(gv); assert(name)

STATIC bool
S_parse_gv_stash_name(pTHX_ HV **stash, GV **gv, const char **name, STRLEN *len, const char *nambeg, STRLEN full_len, const U32 is_utf8, const I32 add);
# define PERL_ARGS_ASSERT_PARSE_GV_STASH_NAME   \
        assert(stash); assert(gv); assert(name); assert(len); assert(nambeg)

STATIC void
S_require_tie_mod(pTHX_ GV *gv, const char varname, const char *name, STRLEN len, const U32 flags);
# define PERL_ARGS_ASSERT_REQUIRE_TIE_MOD       \
        assert(gv); assert(varname); assert(name)

# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE GV *
S_gv_fetchmeth_internal(pTHX_ HV *stash, SV *meth, const char *name, STRLEN len, I32 level, U32 flags);
#   define PERL_ARGS_ASSERT_GV_FETCHMETH_INTERNAL

PERL_STATIC_INLINE HV *
S_gv_stashpvn_internal(pTHX_ const char *name, U32 namelen, I32 flags);
#   define PERL_ARGS_ASSERT_GV_STASHPVN_INTERNAL \
        assert(name)

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_GV_C) */
#if defined(PERL_IN_GV_C)  || defined(PERL_IN_OP_C) || \
    defined(PERL_IN_PAD_C) || defined(PERL_IN_SV_C)
PERL_CALLCONV void
Perl_sv_add_backref(pTHX_ SV * const tsv, SV * const sv)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SV_ADD_BACKREF        \
        assert(tsv); assert(sv)

#endif
#if defined(PERL_IN_GV_C) || defined(PERL_IN_UNIVERSAL_C)
PERL_CALLCONV HV *
Perl_gv_stashsvpvn_cached(pTHX_ SV *namesv, const char *name, U32 namelen, I32 flags)
        __attribute__visibility__("hidden");

#endif
#if defined(PERL_IN_HV_C)
STATIC void
S_clear_placeholders(pTHX_ HV *hv, U32 items);
# define PERL_ARGS_ASSERT_CLEAR_PLACEHOLDERS    \
        assert(hv)

STATIC void
S_hsplit(pTHX_ HV *hv, STRLEN const oldsize, STRLEN newsize);
# define PERL_ARGS_ASSERT_HSPLIT                \
        assert(hv)

STATIC struct xpvhv_aux *
S_hv_auxinit(pTHX_ HV *hv);
# define PERL_ARGS_ASSERT_HV_AUXINIT            \
        assert(hv)

STATIC SV *
S_hv_delete_common(pTHX_ HV *hv, SV *keysv, const char *key, STRLEN klen, int k_flags, I32 d_flags, U32 hash);
# define PERL_ARGS_ASSERT_HV_DELETE_COMMON

STATIC SV *
S_hv_free_ent_ret(pTHX_ HE *entry);
# define PERL_ARGS_ASSERT_HV_FREE_ENT_RET       \
        assert(entry)

STATIC void
S_hv_free_entries(pTHX_ HV *hv);
# define PERL_ARGS_ASSERT_HV_FREE_ENTRIES       \
        assert(hv)

STATIC void
S_hv_magic_check(HV *hv, bool *needs_copy, bool *needs_store);
# define PERL_ARGS_ASSERT_HV_MAGIC_CHECK        \
        assert(hv); assert(needs_copy); assert(needs_store)

PERL_STATIC_NO_RET void
S_hv_notallowed(pTHX_ int flags, const char *key, I32 klen, const char *msg)
        __attribute__noreturn__;
# define PERL_ARGS_ASSERT_HV_NOTALLOWED         \
        assert(key); assert(msg)

STATIC SV *
S_refcounted_he_value(pTHX_ const struct refcounted_he *he);
# define PERL_ARGS_ASSERT_REFCOUNTED_HE_VALUE   \
        assert(he)

STATIC HEK *
S_save_hek_flags(const char *str, I32 len, U32 hash, int flags)
        __attribute__malloc__
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SAVE_HEK_FLAGS        \
        assert(str)

STATIC HEK *
S_share_hek_flags(pTHX_ const char *str, STRLEN len, U32 hash, int flags)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SHARE_HEK_FLAGS       \
        assert(str)

STATIC void
S_unshare_hek_or_pvn(pTHX_ const HEK *hek, const char *str, I32 len, U32 hash);
# define PERL_ARGS_ASSERT_UNSHARE_HEK_OR_PVN

# if !defined(PURIFY)
STATIC HE *
S_new_he(pTHX)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_NEW_HE

# endif
#endif /* defined(PERL_IN_HV_C) */
#if defined(PERL_IN_HV_C) || defined(PERL_IN_MG_C) || defined(PERL_IN_SV_C)
PERL_CALLCONV void
Perl_sv_kill_backrefs(pTHX_ SV * const sv, AV * const av)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SV_KILL_BACKREFS      \
        assert(sv)

#endif
#if defined(PERL_IN_HV_C) || defined(PERL_IN_SV_C)
PERL_CALLCONV SV *
Perl_hfree_next_entry(pTHX_ HV *hv, STRLEN *indexp)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_HFREE_NEXT_ENTRY      \
        assert(hv); assert(indexp)

#endif
#if defined(PERL_IN_LOCALE_C)
STATIC utf8ness_t
S_get_locale_string_utf8ness_i(pTHX_ const char *string, const locale_utf8ness_t known_utf8, const char *locale, const unsigned cat_index);
# define PERL_ARGS_ASSERT_GET_LOCALE_STRING_UTF8NESS_I

STATIC bool
S_is_locale_utf8(pTHX_ const char *locale);
# define PERL_ARGS_ASSERT_IS_LOCALE_UTF8        \
        assert(locale)

# if defined(HAS_LOCALECONV)
STATIC HV *
S_my_localeconv(pTHX_ const int item);
#   define PERL_ARGS_ASSERT_MY_LOCALECONV

STATIC void
S_populate_hash_from_localeconv(pTHX_ HV *hv, const char *locale, const U32 which_mask, const lconv_offset_t *strings[2], const lconv_offset_t *integers);
#   define PERL_ARGS_ASSERT_POPULATE_HASH_FROM_LOCALECONV \
        assert(hv); assert(locale); assert(strings)

# endif /* defined(HAS_LOCALECONV) */
# if defined(USE_LOCALE)
STATIC unsigned int
S_get_category_index(const int category, const char *locale);
#   define PERL_ARGS_ASSERT_GET_CATEGORY_INDEX

STATIC int
S_get_category_index_nowarn(const int category);
#   define PERL_ARGS_ASSERT_GET_CATEGORY_INDEX_NOWARN

STATIC void
S_new_LC_ALL(pTHX_ const char *unused, bool force);
#   define PERL_ARGS_ASSERT_NEW_LC_ALL

STATIC void
S_restore_toggled_locale_i(pTHX_ const unsigned cat_index, const char *original_locale, const line_t caller_line);
#   define PERL_ARGS_ASSERT_RESTORE_TOGGLED_LOCALE_I

STATIC const char *
S_save_to_buffer(const char *string, const char **buf, Size_t *buf_size);
#   define PERL_ARGS_ASSERT_SAVE_TO_BUFFER

PERL_STATIC_NO_RET void
S_setlocale_failure_panic_i(pTHX_ const unsigned int cat_index, const char *current, const char *failed, const line_t caller_0_line, const line_t caller_1_line)
        __attribute__noreturn__;
#   define PERL_ARGS_ASSERT_SETLOCALE_FAILURE_PANIC_I \
        assert(failed)

STATIC const char *
S_stdize_locale(pTHX_ const int category, const char *input_locale, const char **buf, Size_t *buf_size, line_t caller_line);
#   define PERL_ARGS_ASSERT_STDIZE_LOCALE

STATIC const char *
S_toggle_locale_i(pTHX_ const unsigned switch_cat_index, const char *new_locale, const line_t caller_line);
#   define PERL_ARGS_ASSERT_TOGGLE_LOCALE_I     \
        assert(new_locale)

#   if defined(DEBUGGING)
STATIC char *
S_my_setlocale_debug_string_i(pTHX_ const unsigned cat_index, const char *locale, const char *retval, const line_t line)
        __attribute__warn_unused_result__;
#     define PERL_ARGS_ASSERT_MY_SETLOCALE_DEBUG_STRING_I

#   endif
#   if defined(HAS_NL_LANGINFO) || defined(HAS_NL_LANGINFO_L)
STATIC const char *
S_my_langinfo_i(pTHX_ const nl_item item, const unsigned int cat_index, const char *locale, const char **retbufp, Size_t *retbuf_sizep, utf8ness_t *utf8ness);
#     define PERL_ARGS_ASSERT_MY_LANGINFO_I     \
        assert(locale); assert(retbufp)

#   else
STATIC const char *
S_my_langinfo_i(pTHX_ const int item, const unsigned int cat_index, const char *locale, const char **retbufp, Size_t *retbuf_sizep, utf8ness_t *utf8ness);
#     define PERL_ARGS_ASSERT_MY_LANGINFO_I     \
        assert(locale); assert(retbufp)

#   endif
#   if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE const char *
S_mortalized_pv_copy(pTHX_ const char * const pv)
        __attribute__warn_unused_result__;
#     define PERL_ARGS_ASSERT_MORTALIZED_PV_COPY

#   endif
#   if defined(USE_LOCALE_COLLATE)
STATIC void
S_new_collate(pTHX_ const char *newcoll, bool force);
#     define PERL_ARGS_ASSERT_NEW_COLLATE       \
        assert(newcoll)

#     if defined(DEBUGGING)
STATIC void
S_print_collxfrm_input_and_return(pTHX_ const char *s, const char *e, const char *xbuf, const STRLEN xlen, const bool is_utf8);
#       define PERL_ARGS_ASSERT_PRINT_COLLXFRM_INPUT_AND_RETURN \
        assert(s); assert(e)

#     endif
#   endif /* defined(USE_LOCALE_COLLATE) */
#   if defined(USE_LOCALE_CTYPE)
STATIC bool
S_is_codeset_name_UTF8(const char *name);
#     define PERL_ARGS_ASSERT_IS_CODESET_NAME_UTF8 \
        assert(name)

STATIC void
S_new_ctype(pTHX_ const char *newctype, bool force);
#     define PERL_ARGS_ASSERT_NEW_CTYPE         \
        assert(newctype)

#   endif /* defined(USE_LOCALE_CTYPE) */
#   if defined(USE_LOCALE_NUMERIC)
STATIC void
S_new_numeric(pTHX_ const char *newnum, bool force);
#     define PERL_ARGS_ASSERT_NEW_NUMERIC       \
        assert(newnum)

#   endif
#   if defined(USE_PERL_SWITCH_LOCALE_CONTEXT) || defined(DEBUGGING)
STATIC const char *
S_get_LC_ALL_display(pTHX);
#     define PERL_ARGS_ASSERT_GET_LC_ALL_DISPLAY

#   endif
#   if defined(USE_POSIX_2008_LOCALE)
STATIC const char *
S_emulate_setlocale_i(pTHX_ const unsigned int index, const char *new_locale, const recalc_lc_all_t recalc_LC_ALL, const line_t line);
#     define PERL_ARGS_ASSERT_EMULATE_SETLOCALE_I

STATIC const char *
S_my_querylocale_i(pTHX_ const unsigned int index);
#     define PERL_ARGS_ASSERT_MY_QUERYLOCALE_I

STATIC const char *
S_setlocale_from_aggregate_LC_ALL(pTHX_ const char *locale, const line_t line);
#     define PERL_ARGS_ASSERT_SETLOCALE_FROM_AGGREGATE_LC_ALL \
        assert(locale)

STATIC locale_t
S_use_curlocale_scratch(pTHX);
#     define PERL_ARGS_ASSERT_USE_CURLOCALE_SCRATCH

#     if defined(USE_QUERYLOCALE)
STATIC const char *
S_calculate_LC_ALL(pTHX_ const locale_t cur_obj);
#       define PERL_ARGS_ASSERT_CALCULATE_LC_ALL

#     else
STATIC const char *
S_update_PL_curlocales_i(pTHX_ const unsigned int index, const char *new_locale, recalc_lc_all_t recalc_LC_ALL);
#       define PERL_ARGS_ASSERT_UPDATE_PL_CURLOCALES_I \
        assert(new_locale)

#     endif
#   elif  defined(USE_LOCALE_THREADS) &&                  \
         !defined(USE_THREAD_SAFE_LOCALE) &&              \
         !defined(USE_THREAD_SAFE_LOCALE_EMULATION) /* &&
         !defined(USE_POSIX_2008_LOCALE) */
STATIC const char *
S_less_dicey_setlocale_r(pTHX_ const int category, const char *locale);
#     define PERL_ARGS_ASSERT_LESS_DICEY_SETLOCALE_R

STATIC void
S_less_dicey_void_setlocale_i(pTHX_ const unsigned cat_index, const char *locale, const line_t line);
#     define PERL_ARGS_ASSERT_LESS_DICEY_VOID_SETLOCALE_I \
        assert(locale)

#     if 0
STATIC bool
S_less_dicey_bool_setlocale_r(pTHX_ const int cat, const char *locale);
#       define PERL_ARGS_ASSERT_LESS_DICEY_BOOL_SETLOCALE_R \
        assert(locale)

#     endif
#   endif /*  defined(USE_LOCALE_THREADS) &&
             !defined(USE_POSIX_2008_LOCALE) &&
             !defined(USE_THREAD_SAFE_LOCALE) &&
             !defined(USE_THREAD_SAFE_LOCALE_EMULATION) */
#   if !(  defined(USE_POSIX_2008_LOCALE) && defined(USE_QUERYLOCALE) ) && \
        ( !defined(LC_ALL) || defined(USE_POSIX_2008_LOCALE) ||            \
           defined(WIN32) )
STATIC const char *
S_calculate_LC_ALL(pTHX_ const char **individ_locales);
#     define PERL_ARGS_ASSERT_CALCULATE_LC_ALL  \
        assert(individ_locales)

#   endif
#   if defined(WIN32)
STATIC wchar_t *
S_Win_byte_string_to_wstring(const UINT code_page, const char *byte_string);
#     define PERL_ARGS_ASSERT_WIN_BYTE_STRING_TO_WSTRING

STATIC char *
S_Win_wstring_to_byte_string(const UINT code_page, const wchar_t *wstring);
#     define PERL_ARGS_ASSERT_WIN_WSTRING_TO_BYTE_STRING

STATIC const char *
S_win32_setlocale(pTHX_ int category, const char *locale);
#     define PERL_ARGS_ASSERT_WIN32_SETLOCALE

STATIC const char *
S_wrap_wsetlocale(pTHX_ const int category, const char *locale);
#     define PERL_ARGS_ASSERT_WRAP_WSETLOCALE

#   endif /* defined(WIN32) */
#   if   defined(WIN32) || \
       ( defined(USE_POSIX_2008_LOCALE) && !defined(USE_QUERYLOCALE) )
STATIC const char *
S_find_locale_from_environment(pTHX_ const unsigned int index);
#     define PERL_ARGS_ASSERT_FIND_LOCALE_FROM_ENVIRONMENT

#   endif
# endif /* defined(USE_LOCALE) */
# if defined(USE_POSIX_2008_LOCALE) || defined(DEBUGGING)
STATIC const char *
S_get_displayable_string(pTHX_ const char * const s, const char * const e, const bool is_utf8);
#   define PERL_ARGS_ASSERT_GET_DISPLAYABLE_STRING \
        assert(s); assert(e)

# endif
#endif /* defined(PERL_IN_LOCALE_C) */
#if defined(PERL_IN_MALLOC_C)
STATIC int
S_adjust_size_and_find_bucket(size_t *nbytes_p);
# define PERL_ARGS_ASSERT_ADJUST_SIZE_AND_FIND_BUCKET \
        assert(nbytes_p)

#endif
#if defined(PERL_IN_MG_C)
STATIC void
S_fixup_errno_string(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_FIXUP_ERRNO_STRING    \
        assert(sv)

STATIC SV *
S_magic_methcall1(pTHX_ SV *sv, const MAGIC *mg, SV *meth, U32 flags, int n, SV *val);
# define PERL_ARGS_ASSERT_MAGIC_METHCALL1       \
        assert(sv); assert(mg); assert(meth)

STATIC int
S_magic_methpack(pTHX_ SV *sv, const MAGIC *mg, SV *meth);
# define PERL_ARGS_ASSERT_MAGIC_METHPACK        \
        assert(sv); assert(mg); assert(meth)

STATIC void
S_restore_magic(pTHX_ const void *p);
# define PERL_ARGS_ASSERT_RESTORE_MAGIC

STATIC void
S_save_magic_flags(pTHX_ SSize_t mgs_ix, SV *sv, U32 flags);
# define PERL_ARGS_ASSERT_SAVE_MAGIC_FLAGS      \
        assert(sv)

STATIC void
S_unwind_handler_stack(pTHX_ const void *p);
# define PERL_ARGS_ASSERT_UNWIND_HANDLER_STACK

#endif /* defined(PERL_IN_MG_C) */
#if defined(PERL_IN_MG_C) || defined(PERL_IN_PP_C)
PERL_CALLCONV bool
Perl_translate_substr_offsets(STRLEN curlen, IV pos1_iv, bool pos1_is_uv, IV len_iv, bool len_is_uv, STRLEN *posp, STRLEN *lenp)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_TRANSLATE_SUBSTR_OFFSETS \
        assert(posp); assert(lenp)

#endif
#if defined(PERL_IN_MRO_C)
STATIC void
S_mro_clean_isarev(pTHX_ HV * const isa, const char * const name, const STRLEN len, HV * const exceptions, U32 hash, U32 flags);
# define PERL_ARGS_ASSERT_MRO_CLEAN_ISAREV      \
        assert(isa); assert(name)

STATIC void
S_mro_gather_and_rename(pTHX_ HV * const stashes, HV * const seen_stashes, HV *stash, HV *oldstash, SV *namesv);
# define PERL_ARGS_ASSERT_MRO_GATHER_AND_RENAME \
        assert(stashes); assert(seen_stashes); assert(namesv)

STATIC AV *
S_mro_get_linear_isa_dfs(pTHX_ HV *stash, U32 level);
# define PERL_ARGS_ASSERT_MRO_GET_LINEAR_ISA_DFS \
        assert(stash)

#endif /* defined(PERL_IN_MRO_C) */
#if defined(PERL_IN_NUMERIC_C)
STATIC void
S_output_non_portable(pTHX_ const U8 shift);
# define PERL_ARGS_ASSERT_OUTPUT_NON_PORTABLE

#endif
#if defined(PERL_IN_OP_C)
STATIC void
S_apply_attrs(pTHX_ HV *stash, SV *target, OP *attrs);
# define PERL_ARGS_ASSERT_APPLY_ATTRS           \
        assert(stash); assert(target)

STATIC void
S_apply_attrs_my(pTHX_ HV *stash, OP *target, OP *attrs, OP **imopsp);
# define PERL_ARGS_ASSERT_APPLY_ATTRS_MY        \
        assert(stash); assert(target); assert(imopsp)

STATIC I32
S_assignment_type(pTHX_ const OP *o)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ASSIGNMENT_TYPE

STATIC void
S_bad_type_gv(pTHX_ I32 n, GV *gv, const OP *kid, const char *t);
# define PERL_ARGS_ASSERT_BAD_TYPE_GV           \
        assert(gv); assert(kid); assert(t)

STATIC void
S_bad_type_pv(pTHX_ I32 n, const char *t, const OP *o, const OP *kid);
# define PERL_ARGS_ASSERT_BAD_TYPE_PV           \
        assert(t); assert(o); assert(kid)

STATIC void
S_clear_special_blocks(pTHX_ const char * const fullname, GV * const gv, CV * const cv);
# define PERL_ARGS_ASSERT_CLEAR_SPECIAL_BLOCKS  \
        assert(fullname); assert(gv); assert(cv)

STATIC void
S_cop_free(pTHX_ COP *cop);
# define PERL_ARGS_ASSERT_COP_FREE              \
        assert(cop)

STATIC OP *
S_dup_attrlist(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_DUP_ATTRLIST          \
        assert(o)

STATIC void
S_find_and_forget_pmops(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_FIND_AND_FORGET_PMOPS \
        assert(o)

STATIC OP *
S_fold_constants(pTHX_ OP * const o);
# define PERL_ARGS_ASSERT_FOLD_CONSTANTS        \
        assert(o)

STATIC OP *
S_force_list(pTHX_ OP *arg, bool nullit);
# define PERL_ARGS_ASSERT_FORCE_LIST

STATIC void
S_forget_pmop(pTHX_ PMOP * const o);
# define PERL_ARGS_ASSERT_FORGET_PMOP           \
        assert(o)

STATIC void
S_gen_constant_list(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_GEN_CONSTANT_LIST

STATIC void
S_inplace_aassign(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_INPLACE_AASSIGN       \
        assert(o)

STATIC bool
S_is_handle_constructor(const OP *o, I32 numargs)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_IS_HANDLE_CONSTRUCTOR \
        assert(o)

STATIC OP *
S_listkids(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_LISTKIDS

STATIC bool
S_looks_like_bool(pTHX_ const OP *o);
# define PERL_ARGS_ASSERT_LOOKS_LIKE_BOOL       \
        assert(o)

STATIC OP *
S_modkids(pTHX_ OP *o, I32 type);
# define PERL_ARGS_ASSERT_MODKIDS

STATIC void
S_move_proto_attr(pTHX_ OP **proto, OP **attrs, const GV *name, bool curstash);
# define PERL_ARGS_ASSERT_MOVE_PROTO_ATTR       \
        assert(proto); assert(attrs); assert(name)

STATIC OP *
S_my_kid(pTHX_ OP *o, OP *attrs, OP **imopsp);
# define PERL_ARGS_ASSERT_MY_KID                \
        assert(imopsp)

STATIC OP *
S_newGIVWHENOP(pTHX_ OP *cond, OP *block, I32 enter_opcode, I32 leave_opcode, PADOFFSET entertarg);
# define PERL_ARGS_ASSERT_NEWGIVWHENOP          \
        assert(block)

STATIC OP *
S_new_logop(pTHX_ I32 type, I32 flags, OP **firstp, OP **otherp)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEW_LOGOP             \
        assert(firstp); assert(otherp)

STATIC OP *
S_no_fh_allowed(pTHX_ OP *o)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NO_FH_ALLOWED         \
        assert(o)

STATIC OP *
S_pmtrans(pTHX_ OP *o, OP *expr, OP *repl);
# define PERL_ARGS_ASSERT_PMTRANS               \
        assert(o); assert(expr); assert(repl)

STATIC bool
S_process_special_blocks(pTHX_ I32 floor, const char * const fullname, GV * const gv, CV * const cv);
# define PERL_ARGS_ASSERT_PROCESS_SPECIAL_BLOCKS \
        assert(fullname); assert(gv); assert(cv)

STATIC OP *
S_ref_array_or_hash(pTHX_ OP *cond);
# define PERL_ARGS_ASSERT_REF_ARRAY_OR_HASH

STATIC OP *
S_refkids(pTHX_ OP *o, I32 type);
# define PERL_ARGS_ASSERT_REFKIDS

STATIC bool
S_scalar_mod_type(const OP *o, I32 type)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SCALAR_MOD_TYPE

STATIC OP *
S_scalarboolean(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_SCALARBOOLEAN         \
        assert(o)

STATIC OP *
S_scalarkids(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_SCALARKIDS

STATIC OP *
S_search_const(pTHX_ OP *o)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SEARCH_CONST          \
        assert(o)

STATIC void
S_simplify_sort(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_SIMPLIFY_SORT         \
        assert(o)

STATIC OP *
S_too_few_arguments_pv(pTHX_ OP *o, const char *name, U32 flags)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_TOO_FEW_ARGUMENTS_PV  \
        assert(o); assert(name)

STATIC OP *
S_too_many_arguments_pv(pTHX_ OP *o, const char *name, U32 flags);
# define PERL_ARGS_ASSERT_TOO_MANY_ARGUMENTS_PV \
        assert(o); assert(name)

STATIC OP *
S_voidnonfinal(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_VOIDNONFINAL

# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE OP *
S_newMETHOP_internal(pTHX_ I32 type, I32 flags, OP *dynamic_meth, SV * const_meth);
#   define PERL_ARGS_ASSERT_NEWMETHOP_INTERNAL

PERL_STATIC_INLINE OP *
S_op_integerize(pTHX_ OP *o);
#   define PERL_ARGS_ASSERT_OP_INTEGERIZE       \
        assert(o)

PERL_STATIC_INLINE OP *
S_op_std_init(pTHX_ OP *o);
#   define PERL_ARGS_ASSERT_OP_STD_INIT         \
        assert(o)

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_OP_C) */
#if defined(PERL_IN_OP_C) || defined(PERL_IN_PEEP_C)
PERL_CALLCONV void
Perl_check_hash_fields_and_hekify(pTHX_ UNOP *rop, SVOP *key_op, int real)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CHECK_HASH_FIELDS_AND_HEKIFY

PERL_CALLCONV void
Perl_no_bareword_allowed(pTHX_ OP *o)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_NO_BAREWORD_ALLOWED   \
        assert(o)

PERL_CALLCONV void
Perl_op_prune_chain_head(OP **op_p)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_OP_PRUNE_CHAIN_HEAD   \
        assert(op_p)

PERL_CALLCONV SV *
Perl_op_varname(pTHX_ const OP *o)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_OP_VARNAME            \
        assert(o)

PERL_CALLCONV void
Perl_warn_elem_scalar_context(pTHX_ const OP *o, SV *name, bool is_hash, bool is_slice)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_WARN_ELEM_SCALAR_CONTEXT \
        assert(o); assert(name)

#endif /* defined(PERL_IN_OP_C) || defined(PERL_IN_PEEP_C) */
#if defined(PERL_IN_OP_C) || defined(PERL_IN_REGCOMP_ANY)

#endif
#if defined(PERL_IN_OP_C) || defined(PERL_IN_SV_C)
PERL_CALLCONV void
Perl_report_redefined_cv(pTHX_ const SV *name, const CV *old_cv, SV * const *new_const_svp)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_REPORT_REDEFINED_CV   \
        assert(name); assert(old_cv)

PERL_CALLCONV SV *
Perl_varname(pTHX_ const GV * const gv, const char gvtype, PADOFFSET targ, const SV * const keyname, SSize_t aindex, int subscript_type)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_VARNAME

#endif /* defined(PERL_IN_OP_C) || defined(PERL_IN_SV_C) */
#if defined(PERL_IN_PAD_C)
STATIC PADOFFSET
S_pad_alloc_name(pTHX_ PADNAME *name, U32 flags, HV *typestash, HV *ourstash);
# define PERL_ARGS_ASSERT_PAD_ALLOC_NAME        \
        assert(name)

STATIC void
S_pad_check_dup(pTHX_ PADNAME *name, U32 flags, const HV *ourstash);
# define PERL_ARGS_ASSERT_PAD_CHECK_DUP         \
        assert(name)

STATIC PADOFFSET
S_pad_findlex(pTHX_ const char *namepv, STRLEN namelen, U32 flags, const CV *cv, U32 seq, int warn, SV **out_capture, PADNAME **out_name, int *out_flags);
# define PERL_ARGS_ASSERT_PAD_FINDLEX           \
        assert(namepv); assert(cv); assert(out_name); assert(out_flags)

STATIC void
S_pad_reset(pTHX);
# define PERL_ARGS_ASSERT_PAD_RESET

# if defined(DEBUGGING)
STATIC void
S_cv_dump(pTHX_ const CV *cv, const char *title);
#   define PERL_ARGS_ASSERT_CV_DUMP             \
        assert(cv); assert(title)

# endif
#endif /* defined(PERL_IN_PAD_C) */
#if defined(PERL_IN_PEEP_C)
STATIC void
S_finalize_op(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_FINALIZE_OP           \
        assert(o)

STATIC void
S_optimize_op(pTHX_ OP *o);
# define PERL_ARGS_ASSERT_OPTIMIZE_OP           \
        assert(o)

STATIC OP *
S_traverse_op_tree(pTHX_ OP *top, OP *o);
# define PERL_ARGS_ASSERT_TRAVERSE_OP_TREE      \
        assert(top); assert(o)

#endif /* defined(PERL_IN_PEEP_C) */
#if defined(PERL_IN_PERL_C)
STATIC void
S_find_beginning(pTHX_ SV *linestr_sv, PerlIO *rsfp);
# define PERL_ARGS_ASSERT_FIND_BEGINNING        \
        assert(linestr_sv); assert(rsfp)

STATIC void
S_forbid_setid(pTHX_ const char flag, const bool suidscript);
# define PERL_ARGS_ASSERT_FORBID_SETID

STATIC void
S_incpush(pTHX_ const char * const dir, STRLEN len, U32 flags);
# define PERL_ARGS_ASSERT_INCPUSH               \
        assert(dir)

STATIC void
S_incpush_use_sep(pTHX_ const char *p, STRLEN len, U32 flags);
# define PERL_ARGS_ASSERT_INCPUSH_USE_SEP       \
        assert(p)

STATIC void
S_init_ids(pTHX);
# define PERL_ARGS_ASSERT_INIT_IDS

STATIC void
S_init_interp(pTHX);
# define PERL_ARGS_ASSERT_INIT_INTERP

STATIC void
S_init_main_stash(pTHX);
# define PERL_ARGS_ASSERT_INIT_MAIN_STASH

STATIC void
S_init_perllib(pTHX);
# define PERL_ARGS_ASSERT_INIT_PERLLIB

STATIC void
S_init_postdump_symbols(pTHX_ int argc, char **argv, char **env);
# define PERL_ARGS_ASSERT_INIT_POSTDUMP_SYMBOLS \
        assert(argv)

STATIC void
S_init_predump_symbols(pTHX);
# define PERL_ARGS_ASSERT_INIT_PREDUMP_SYMBOLS

STATIC SV *
S_mayberelocate(pTHX_ const char * const dir, STRLEN len, U32 flags);
# define PERL_ARGS_ASSERT_MAYBERELOCATE         \
        assert(dir)

PERL_STATIC_NO_RET void
S_minus_v(pTHX)
        __attribute__noreturn__;
# define PERL_ARGS_ASSERT_MINUS_V

PERL_STATIC_NO_RET void
S_my_exit_jump(pTHX)
        __attribute__noreturn__;
# define PERL_ARGS_ASSERT_MY_EXIT_JUMP

STATIC void
S_nuke_stacks(pTHX);
# define PERL_ARGS_ASSERT_NUKE_STACKS

STATIC PerlIO *
S_open_script(pTHX_ const char *scriptname, bool dosearch, bool *suidscript);
# define PERL_ARGS_ASSERT_OPEN_SCRIPT           \
        assert(scriptname); assert(suidscript)

STATIC void *
S_parse_body(pTHX_ char **env, XSINIT_t xsinit);
# define PERL_ARGS_ASSERT_PARSE_BODY

PERL_STATIC_NO_RET void
S_run_body(pTHX_ I32 oldscope)
        __attribute__noreturn__;
# define PERL_ARGS_ASSERT_RUN_BODY

PERL_STATIC_NO_RET void
S_usage(pTHX)
        __attribute__noreturn__;
# define PERL_ARGS_ASSERT_USAGE

# if !defined(PERL_IS_MINIPERL)
STATIC SV *
S_incpush_if_exists(pTHX_ AV * const av, SV *dir, SV * const stem);
#   define PERL_ARGS_ASSERT_INCPUSH_IF_EXISTS   \
        assert(av); assert(dir); assert(stem)

# endif
# if !defined(SETUID_SCRIPTS_ARE_SECURE_NOW)
STATIC void
S_validate_suid(pTHX_ PerlIO *rsfp);
#   define PERL_ARGS_ASSERT_VALIDATE_SUID       \
        assert(rsfp)

# endif
#endif /* defined(PERL_IN_PERL_C) */
#if defined(PERL_IN_PERL_C) || defined(PERL_IN_REGCOMP_ANY) || \
    defined(PERL_IN_UTF8_C)
PERL_CALLCONV bool
Perl__invlistEQ(pTHX_ SV * const a, SV * const b, const bool complement_b);
# define PERL_ARGS_ASSERT__INVLISTEQ            \
        assert(a); assert(b)

PERL_CALLCONV SV *
Perl__new_invlist_C_array(pTHX_ const UV * const list)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT__NEW_INVLIST_C_ARRAY  \
        assert(list)

#endif /* defined(PERL_IN_PERL_C) || defined(PERL_IN_REGCOMP_ANY) ||
          defined(PERL_IN_UTF8_C) */
#if defined(PERL_IN_PP_C)
STATIC size_t
S_do_chomp(pTHX_ SV *retval, SV *sv, bool chomping);
# define PERL_ARGS_ASSERT_DO_CHOMP              \
        assert(retval); assert(sv)

STATIC OP *
S_do_delete_local(pTHX);
# define PERL_ARGS_ASSERT_DO_DELETE_LOCAL

STATIC SV *
S_refto(pTHX_ SV *sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REFTO                 \
        assert(sv)

#endif /* defined(PERL_IN_PP_C) */
#if defined(PERL_IN_PP_C) || defined(PERL_IN_PP_HOT_C)

PERL_CALLCONV GV *
Perl_softref2xv(pTHX_ SV * const sv, const char * const what, const svtype type, SV ***spp)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SOFTREF2XV            \
        assert(sv); assert(what); assert(spp)

#endif
#if defined(PERL_IN_PP_C) || defined(PERL_IN_UTF8_C)
PERL_CALLCONV UV
Perl__to_upper_title_latin1(pTHX_ const U8 c, U8 *p, STRLEN *lenp, const char S_or_s)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT__TO_UPPER_TITLE_LATIN1 \
        assert(p); assert(lenp)

#endif
#if defined(PERL_IN_PP_CTL_C)
STATIC PerlIO *
S_check_type_and_open(pTHX_ SV *name)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_CHECK_TYPE_AND_OPEN   \
        assert(name)

STATIC void
S_destroy_matcher(pTHX_ PMOP *matcher);
# define PERL_ARGS_ASSERT_DESTROY_MATCHER       \
        assert(matcher)

STATIC OP *
S_do_smartmatch(pTHX_ HV *seen_this, HV *seen_other, const bool copied);
# define PERL_ARGS_ASSERT_DO_SMARTMATCH

STATIC OP *
S_docatch(pTHX_ Perl_ppaddr_t firstpp)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DOCATCH

STATIC bool
S_doeval_compile(pTHX_ U8 gimme, CV *outside, U32 seq, HV *hh);
# define PERL_ARGS_ASSERT_DOEVAL_COMPILE

STATIC OP *
S_dofindlabel(pTHX_ OP *o, const char *label, STRLEN len, U32 flags, OP **opstack, OP **oplimit)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DOFINDLABEL           \
        assert(o); assert(label); assert(opstack); assert(oplimit)

STATIC MAGIC *
S_doparseform(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_DOPARSEFORM           \
        assert(sv)

STATIC I32
S_dopoptoeval(pTHX_ I32 startingblock)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DOPOPTOEVAL

STATIC I32
S_dopoptogivenfor(pTHX_ I32 startingblock)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DOPOPTOGIVENFOR

STATIC I32
S_dopoptolabel(pTHX_ const char *label, STRLEN len, U32 flags)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DOPOPTOLABEL          \
        assert(label)

STATIC I32
S_dopoptoloop(pTHX_ I32 startingblock)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DOPOPTOLOOP

STATIC I32
S_dopoptosub_at(pTHX_ const PERL_CONTEXT *cxstk, I32 startingblock)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DOPOPTOSUB_AT         \
        assert(cxstk)

STATIC I32
S_dopoptowhen(pTHX_ I32 startingblock)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DOPOPTOWHEN

STATIC PMOP *
S_make_matcher(pTHX_ REGEXP *re)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_MAKE_MATCHER          \
        assert(re)

STATIC bool
S_matcher_matches_sv(pTHX_ PMOP *matcher, SV *sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_MATCHER_MATCHES_SV    \
        assert(matcher); assert(sv)

STATIC bool
S_num_overflow(NV value, I32 fldsize, I32 frcsize)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NUM_OVERFLOW

STATIC I32
S_run_user_filter(pTHX_ int idx, SV *buf_sv, int maxlen)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_RUN_USER_FILTER       \
        assert(buf_sv)

STATIC void
S_rxres_free(pTHX_ void **rsp);
# define PERL_ARGS_ASSERT_RXRES_FREE            \
        assert(rsp)

STATIC void
S_rxres_restore(pTHX_ void **rsp, REGEXP *rx);
# define PERL_ARGS_ASSERT_RXRES_RESTORE         \
        assert(rsp); assert(rx)

STATIC void
S_save_lines(pTHX_ AV *array, SV *sv);
# define PERL_ARGS_ASSERT_SAVE_LINES            \
        assert(sv)

# if !defined(PERL_DISABLE_PMC)
STATIC PerlIO *
S_doopen_pm(pTHX_ SV *name)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_DOOPEN_PM           \
        assert(name)

# endif
# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE bool
S_path_is_searchable(const char *name)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_PATH_IS_SEARCHABLE  \
        assert(name)

# endif
#endif /* defined(PERL_IN_PP_CTL_C) */
#if defined(PERL_IN_PP_CTL_C) || defined(PERL_IN_UTIL_C)
PERL_CALLCONV bool
Perl_invoke_exception_hook(pTHX_ SV *ex, bool warn)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_INVOKE_EXCEPTION_HOOK

#endif
#if defined(PERL_IN_PP_HOT_C)
STATIC void
S_do_oddball(pTHX_ SV **oddkey, SV **firstkey);
# define PERL_ARGS_ASSERT_DO_ODDBALL            \
        assert(oddkey); assert(firstkey)

# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE HV *
S_opmethod_stash(pTHX_ SV *meth);
#   define PERL_ARGS_ASSERT_OPMETHOD_STASH      \
        assert(meth)

PERL_STATIC_FORCE_INLINE bool
S_should_we_output_Debug_r(pTHX_ regexp *prog)
        __attribute__warn_unused_result__
        __attribute__always_inline__;
#   define PERL_ARGS_ASSERT_SHOULD_WE_OUTPUT_DEBUG_R \
        assert(prog)

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_PP_HOT_C) */
#if defined(PERL_IN_PP_PACK_C)
STATIC int
S_div128(pTHX_ SV *pnum, bool *done);
# define PERL_ARGS_ASSERT_DIV128                \
        assert(pnum); assert(done)

STATIC char
S_first_symbol(const char *pat, const char *patend);
# define PERL_ARGS_ASSERT_FIRST_SYMBOL          \
        assert(pat); assert(patend)

STATIC const char *
S_get_num(pTHX_ const char *patptr, SSize_t *lenptr)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_GET_NUM               \
        assert(patptr); assert(lenptr)

STATIC const char *
S_group_end(pTHX_ const char *patptr, const char *patend, char ender);
# define PERL_ARGS_ASSERT_GROUP_END             \
        assert(patptr); assert(patend)

STATIC SV *
S_is_an_int(pTHX_ const char *s, STRLEN l)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_IS_AN_INT             \
        assert(s)

STATIC SSize_t
S_measure_struct(pTHX_ struct tempsym *symptr);
# define PERL_ARGS_ASSERT_MEASURE_STRUCT        \
        assert(symptr)

STATIC SV *
S_mul128(pTHX_ SV *sv, U8 m);
# define PERL_ARGS_ASSERT_MUL128                \
        assert(sv)

STATIC char *
S_my_bytes_to_utf8(const U8 *start, STRLEN len, char *dest, const bool needs_swap)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_MY_BYTES_TO_UTF8      \
        assert(start); assert(dest)

STATIC bool
S_need_utf8(const char *pat, const char *patend);
# define PERL_ARGS_ASSERT_NEED_UTF8             \
        assert(pat); assert(patend)

STATIC bool
S_next_symbol(pTHX_ struct tempsym *symptr);
# define PERL_ARGS_ASSERT_NEXT_SYMBOL           \
        assert(symptr)

STATIC SV **
S_pack_rec(pTHX_ SV *cat, struct tempsym *symptr, SV **beglist, SV **endlist);
# define PERL_ARGS_ASSERT_PACK_REC              \
        assert(cat); assert(symptr); assert(beglist); assert(endlist)

STATIC char *
S_sv_exp_grow(pTHX_ SV *sv, STRLEN needed)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_EXP_GROW           \
        assert(sv)

STATIC SSize_t
S_unpack_rec(pTHX_ struct tempsym *symptr, const char *s, const char *strbeg, const char *strend, const char **new_s);
# define PERL_ARGS_ASSERT_UNPACK_REC            \
        assert(symptr); assert(s); assert(strbeg); assert(strend)

#endif /* defined(PERL_IN_PP_PACK_C) */
#if defined(PERL_IN_PP_SORT_C)
STATIC I32
S_sortcv(pTHX_ SV * const a, SV * const b);
# define PERL_ARGS_ASSERT_SORTCV                \
        assert(a); assert(b)

STATIC I32
S_sortcv_stacked(pTHX_ SV * const a, SV * const b);
# define PERL_ARGS_ASSERT_SORTCV_STACKED        \
        assert(a); assert(b)

STATIC I32
S_sortcv_xsub(pTHX_ SV * const a, SV * const b);
# define PERL_ARGS_ASSERT_SORTCV_XSUB           \
        assert(a); assert(b)

# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE I32
S_amagic_cmp(pTHX_ SV * const str1, SV * const str2);
#   define PERL_ARGS_ASSERT_AMAGIC_CMP          \
        assert(str1); assert(str2)

PERL_STATIC_INLINE I32
S_amagic_cmp_desc(pTHX_ SV * const str1, SV * const str2);
#   define PERL_ARGS_ASSERT_AMAGIC_CMP_DESC     \
        assert(str1); assert(str2)

PERL_STATIC_INLINE I32
S_amagic_i_ncmp(pTHX_ SV * const a, SV * const b);
#   define PERL_ARGS_ASSERT_AMAGIC_I_NCMP       \
        assert(a); assert(b)

PERL_STATIC_INLINE I32
S_amagic_i_ncmp_desc(pTHX_ SV * const a, SV * const b);
#   define PERL_ARGS_ASSERT_AMAGIC_I_NCMP_DESC  \
        assert(a); assert(b)

PERL_STATIC_INLINE I32
S_amagic_ncmp(pTHX_ SV * const a, SV * const b);
#   define PERL_ARGS_ASSERT_AMAGIC_NCMP         \
        assert(a); assert(b)

PERL_STATIC_INLINE I32
S_amagic_ncmp_desc(pTHX_ SV * const a, SV * const b);
#   define PERL_ARGS_ASSERT_AMAGIC_NCMP_DESC    \
        assert(a); assert(b)

PERL_STATIC_INLINE I32
S_cmp_desc(pTHX_ SV * const str1, SV * const str2);
#   define PERL_ARGS_ASSERT_CMP_DESC            \
        assert(str1); assert(str2)

PERL_STATIC_FORCE_INLINE void
S_sortsv_flags_impl(pTHX_ SV **array, size_t num_elts, SVCOMPARE_t cmp, U32 flags)
        __attribute__always_inline__;
#   define PERL_ARGS_ASSERT_SORTSV_FLAGS_IMPL   \
        assert(cmp)

PERL_STATIC_INLINE I32
S_sv_i_ncmp(pTHX_ SV * const a, SV * const b);
#   define PERL_ARGS_ASSERT_SV_I_NCMP           \
        assert(a); assert(b)

PERL_STATIC_INLINE I32
S_sv_i_ncmp_desc(pTHX_ SV * const a, SV * const b);
#   define PERL_ARGS_ASSERT_SV_I_NCMP_DESC      \
        assert(a); assert(b)

PERL_STATIC_INLINE I32
S_sv_ncmp(pTHX_ SV * const a, SV * const b);
#   define PERL_ARGS_ASSERT_SV_NCMP             \
        assert(a); assert(b)

PERL_STATIC_INLINE I32
S_sv_ncmp_desc(pTHX_ SV * const a, SV * const b);
#   define PERL_ARGS_ASSERT_SV_NCMP_DESC        \
        assert(a); assert(b)

#   if defined(USE_LOCALE_COLLATE)
PERL_STATIC_INLINE I32
S_amagic_cmp_locale(pTHX_ SV * const str1, SV * const str2);
#     define PERL_ARGS_ASSERT_AMAGIC_CMP_LOCALE \
        assert(str1); assert(str2)

PERL_STATIC_INLINE I32
S_amagic_cmp_locale_desc(pTHX_ SV * const str1, SV * const str2);
#     define PERL_ARGS_ASSERT_AMAGIC_CMP_LOCALE_DESC \
        assert(str1); assert(str2)

PERL_STATIC_INLINE I32
S_cmp_locale_desc(pTHX_ SV * const str1, SV * const str2);
#     define PERL_ARGS_ASSERT_CMP_LOCALE_DESC   \
        assert(str1); assert(str2)

#   endif /* defined(USE_LOCALE_COLLATE) */
# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_PP_SORT_C) */
#if defined(PERL_IN_PP_SYS_C)
STATIC OP *
S_doform(pTHX_ CV *cv, GV *gv, OP *retop);
# define PERL_ARGS_ASSERT_DOFORM                \
        assert(cv); assert(gv)

STATIC SV *
S_space_join_names_mortal(pTHX_ char * const *array);
# define PERL_ARGS_ASSERT_SPACE_JOIN_NAMES_MORTAL

# if !defined(HAS_MKDIR) || !defined(HAS_RMDIR)
STATIC int
S_dooneliner(pTHX_ const char *cmd, const char *filename)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_DOONELINER          \
        assert(cmd); assert(filename)

# endif
#endif /* defined(PERL_IN_PP_SYS_C) */
#if defined(PERL_IN_REGCOMP_ANY)
PERL_CALLCONV void
Perl_add_above_Latin1_folds(pTHX_ RExC_state_t *pRExC_state, const U8 cp, SV **invlist)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_ADD_ABOVE_LATIN1_FOLDS \
        assert(pRExC_state); assert(invlist)

PERL_CALLCONV regnode *
Perl_construct_ahocorasick_from_trie(pTHX_ RExC_state_t *pRExC_state, regnode *source, U32 depth)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_CONSTRUCT_AHOCORASICK_FROM_TRIE \
        assert(pRExC_state); assert(source)

PERL_CALLCONV SV *
Perl_get_ANYOFHbbm_contents(pTHX_ const regnode *n)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_GET_ANYOFHBBM_CONTENTS \
        assert(n)

PERL_CALLCONV SV *
Perl_get_ANYOFM_contents(pTHX_ const regnode *n)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_GET_ANYOFM_CONTENTS   \
        assert(n)

PERL_CALLCONV bool
Perl_is_ssc_worth_it(const RExC_state_t *pRExC_state, const regnode_ssc *ssc)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_IS_SSC_WORTH_IT       \
        assert(pRExC_state); assert(ssc)

PERL_CALLCONV U32
Perl_join_exact(pTHX_ RExC_state_t *pRExC_state, regnode *scan, UV *min_subtract, bool *unfolded_multi_char, U32 flags, regnode *val, U32 depth)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_JOIN_EXACT            \
        assert(pRExC_state); assert(scan); assert(min_subtract); assert(unfolded_multi_char)

PERL_CALLCONV I32
Perl_make_trie(pTHX_ RExC_state_t *pRExC_state, regnode *startbranch, regnode *first, regnode *last, regnode *tail, U32 word_count, U32 flags, U32 depth)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MAKE_TRIE             \
        assert(pRExC_state); assert(startbranch); assert(first); assert(last); \
        assert(tail)

PERL_CALLCONV void
Perl_populate_anyof_bitmap_from_invlist(pTHX_ regnode *node, SV **invlist_ptr)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_POPULATE_ANYOF_BITMAP_FROM_INVLIST \
        assert(node); assert(invlist_ptr)

PERL_CALLCONV U32
Perl_reg_add_data(RExC_state_t * const pRExC_state, const char * const s, const U32 n)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_REG_ADD_DATA          \
        assert(pRExC_state); assert(s)

PERL_CALLCONV void
Perl_scan_commit(pTHX_ const RExC_state_t *pRExC_state, struct scan_data_t *data, SSize_t *minlenp, int is_inf)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SCAN_COMMIT           \
        assert(pRExC_state); assert(data); assert(minlenp)

PERL_CALLCONV void
Perl_set_ANYOF_arg(pTHX_ RExC_state_t * const pRExC_state, regnode * const node, SV * const cp_list, SV * const runtime_defns, SV * const only_utf8_locale_list)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SET_ANYOF_ARG         \
        assert(pRExC_state); assert(node)

PERL_CALLCONV void
Perl_ssc_finalize(pTHX_ RExC_state_t *pRExC_state, regnode_ssc *ssc)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SSC_FINALIZE          \
        assert(pRExC_state); assert(ssc)

PERL_CALLCONV void
Perl_ssc_init(pTHX_ const RExC_state_t *pRExC_state, regnode_ssc *ssc)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SSC_INIT              \
        assert(pRExC_state); assert(ssc)

PERL_CALLCONV SSize_t
Perl_study_chunk(pTHX_ RExC_state_t *pRExC_state, regnode **scanp, SSize_t *minlenp, SSize_t *deltap, regnode *last, struct scan_data_t *data, I32 stopparen, U32 recursed_depth, regnode_ssc *and_withp, U32 flags, U32 depth, bool was_mutate_ok)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_STUDY_CHUNK           \
        assert(pRExC_state); assert(scanp); assert(minlenp); assert(deltap); \
        assert(last)

# if defined(PERL_IN_REGCOMP_TRIE_C) && defined(DEBUGGING)
STATIC void
S_dump_trie(pTHX_ const struct _reg_trie_data *trie, HV *widecharmap, AV *revcharmap, U32 depth);
#   define PERL_ARGS_ASSERT_DUMP_TRIE           \
        assert(trie); assert(revcharmap)

STATIC void
S_dump_trie_interim_list(pTHX_ const struct _reg_trie_data *trie, HV *widecharmap, AV *revcharmap, U32 next_alloc, U32 depth);
#   define PERL_ARGS_ASSERT_DUMP_TRIE_INTERIM_LIST \
        assert(trie); assert(revcharmap)

STATIC void
S_dump_trie_interim_table(pTHX_ const struct _reg_trie_data *trie, HV *widecharmap, AV *revcharmap, U32 next_alloc, U32 depth);
#   define PERL_ARGS_ASSERT_DUMP_TRIE_INTERIM_TABLE \
        assert(trie); assert(revcharmap)

# endif /* defined(PERL_IN_REGCOMP_TRIE_C) && defined(DEBUGGING) */
# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE SV *
S_invlist_contents(pTHX_ SV * const invlist, const bool traditional_style)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_INVLIST_CONTENTS    \
        assert(invlist)

PERL_STATIC_INLINE UV
S_invlist_highest_range_start(SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_INVLIST_HIGHEST_RANGE_START \
        assert(invlist)

PERL_STATIC_INLINE bool
S_invlist_is_iterating(const SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_INVLIST_IS_ITERATING \
        assert(invlist)

PERL_STATIC_INLINE UV
S_invlist_lowest(SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_INVLIST_LOWEST      \
        assert(invlist)

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_REGCOMP_ANY) */
#if defined(PERL_IN_REGCOMP_ANY) || defined(PERL_IN_SV_C)
PERL_CALLCONV SV *
Perl_invlist_clone(pTHX_ SV * const invlist, SV *newlist);
# define PERL_ARGS_ASSERT_INVLIST_CLONE         \
        assert(invlist)

#endif
#if defined(PERL_IN_REGCOMP_C)
STATIC AV *
S_add_multi_match(pTHX_ AV *multi_char_matches, SV *multi_string, const STRLEN cp_count);
# define PERL_ARGS_ASSERT_ADD_MULTI_MATCH       \
        assert(multi_string)

STATIC void
S_change_engine_size(pTHX_ RExC_state_t *pRExC_state, const Ptrdiff_t size);
# define PERL_ARGS_ASSERT_CHANGE_ENGINE_SIZE    \
        assert(pRExC_state)

STATIC REGEXP *
S_compile_wildcard(pTHX_ const char *subpattern, const STRLEN len, const bool ignore_case)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_COMPILE_WILDCARD      \
        assert(subpattern)

STATIC U8
S_compute_EXACTish(RExC_state_t *pRExC_state);
# define PERL_ARGS_ASSERT_COMPUTE_EXACTISH      \
        assert(pRExC_state)

STATIC int
S_edit_distance(const UV *src, const UV *tgt, const STRLEN x, const STRLEN y, const SSize_t maxDistance)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_EDIT_DISTANCE         \
        assert(src); assert(tgt)

STATIC I32
S_execute_wildcard(pTHX_ REGEXP * const prog, char *stringarg, char *strend, char *strbeg, SSize_t minend, SV *screamer, U32 nosave);
# define PERL_ARGS_ASSERT_EXECUTE_WILDCARD      \
        assert(prog); assert(stringarg); assert(strend); assert(strbeg); \
        assert(screamer)

STATIC U32
S_get_quantifier_value(pTHX_ RExC_state_t *pRExC_state, const char *start, const char *end);
# define PERL_ARGS_ASSERT_GET_QUANTIFIER_VALUE  \
        assert(pRExC_state); assert(start); assert(end)

STATIC bool
S_grok_bslash_N(pTHX_ RExC_state_t *pRExC_state, regnode_offset *nodep, UV *code_point_p, int *cp_count, I32 *flagp, const bool strict, const U32 depth);
# define PERL_ARGS_ASSERT_GROK_BSLASH_N         \
        assert(pRExC_state); assert(flagp)

STATIC regnode_offset
S_handle_named_backref(pTHX_ RExC_state_t *pRExC_state, I32 *flagp, char *backref_parse_start, char ch);
# define PERL_ARGS_ASSERT_HANDLE_NAMED_BACKREF  \
        assert(pRExC_state); assert(flagp); assert(backref_parse_start)

STATIC bool
S_handle_names_wildcard(pTHX_ const char *wname, const STRLEN wname_len, SV **prop_definition, AV **strings);
# define PERL_ARGS_ASSERT_HANDLE_NAMES_WILDCARD \
        assert(wname); assert(prop_definition); assert(strings)

STATIC int
S_handle_possible_posix(pTHX_ RExC_state_t *pRExC_state, const char * const s, char **updated_parse_ptr, AV **posix_warnings, const bool check_only);
# define PERL_ARGS_ASSERT_HANDLE_POSSIBLE_POSIX \
        assert(pRExC_state); assert(s)

STATIC regnode_offset
S_handle_regex_sets(pTHX_ RExC_state_t *pRExC_state, SV **return_invlist, I32 *flagp, U32 depth);
# define PERL_ARGS_ASSERT_HANDLE_REGEX_SETS     \
        assert(pRExC_state); assert(flagp)

STATIC SV *
S_handle_user_defined_property(pTHX_ const char *name, const STRLEN name_len, const bool is_utf8, const bool to_fold, const bool runtime, const bool deferrable, SV *contents, bool *user_defined_ptr, SV *msg, const STRLEN level);
# define PERL_ARGS_ASSERT_HANDLE_USER_DEFINED_PROPERTY \
        assert(name); assert(contents); assert(user_defined_ptr); assert(msg)

STATIC void
S_nextchar(pTHX_ RExC_state_t *pRExC_state);
# define PERL_ARGS_ASSERT_NEXTCHAR              \
        assert(pRExC_state)

STATIC U8
S_optimize_regclass(pTHX_ RExC_state_t *pRExC_state, SV *cp_list, SV *only_utf8_locale_list, SV *upper_latin1_only_utf8_matches, const U32 has_runtime_dependency, const U32 posixl, U8 *anyof_flags, bool *invert, regnode_offset *ret, I32 *flagp);
# define PERL_ARGS_ASSERT_OPTIMIZE_REGCLASS     \
        assert(pRExC_state); assert(anyof_flags); assert(invert); assert(ret); \
        assert(flagp)

STATIC void
S_output_posix_warnings(pTHX_ RExC_state_t *pRExC_state, AV *posix_warnings);
# define PERL_ARGS_ASSERT_OUTPUT_POSIX_WARNINGS \
        assert(pRExC_state); assert(posix_warnings)

STATIC void
S_parse_lparen_question_flags(pTHX_ RExC_state_t *pRExC_state);
# define PERL_ARGS_ASSERT_PARSE_LPAREN_QUESTION_FLAGS \
        assert(pRExC_state)

STATIC SV *
S_parse_uniprop_string(pTHX_ const char * const name, Size_t name_len, const bool is_utf8, const bool to_fold, const bool runtime, const bool deferrable, AV **strings, bool *user_defined_ptr, SV *msg, const STRLEN level);
# define PERL_ARGS_ASSERT_PARSE_UNIPROP_STRING  \
        assert(name); assert(user_defined_ptr); assert(msg)

PERL_STATIC_NO_RET void
S_re_croak(pTHX_ bool utf8, const char *pat, ...)
        __attribute__noreturn__
        __attribute__format__(__printf__,pTHX_2,pTHX_3);
# define PERL_ARGS_ASSERT_RE_CROAK              \
        assert(pat)

STATIC regnode_offset
S_reg(pTHX_ RExC_state_t *pRExC_state, I32 paren, I32 *flagp, U32 depth);
# define PERL_ARGS_ASSERT_REG                   \
        assert(pRExC_state); assert(flagp)

STATIC regnode_offset
S_reg1node(pTHX_ RExC_state_t *pRExC_state, U8 op, U32 arg);
# define PERL_ARGS_ASSERT_REG1NODE              \
        assert(pRExC_state)

STATIC regnode_offset
S_reg2node(pTHX_ RExC_state_t *pRExC_state, const U8 op, const U32 arg1, const I32 arg2);
# define PERL_ARGS_ASSERT_REG2NODE              \
        assert(pRExC_state)

STATIC regnode_offset
S_reg_la_NOTHING(pTHX_ RExC_state_t *pRExC_state, U32 flags, const char *type);
# define PERL_ARGS_ASSERT_REG_LA_NOTHING        \
        assert(pRExC_state); assert(type)

STATIC regnode_offset
S_reg_la_OPFAIL(pTHX_ RExC_state_t *pRExC_state, U32 flags, const char *type);
# define PERL_ARGS_ASSERT_REG_LA_OPFAIL         \
        assert(pRExC_state); assert(type)

STATIC regnode_offset
S_reg_node(pTHX_ RExC_state_t *pRExC_state, U8 op);
# define PERL_ARGS_ASSERT_REG_NODE              \
        assert(pRExC_state)

STATIC SV *
S_reg_scan_name(pTHX_ RExC_state_t *pRExC_state, U32 flags);
# define PERL_ARGS_ASSERT_REG_SCAN_NAME         \
        assert(pRExC_state)

STATIC regnode_offset
S_regatom(pTHX_ RExC_state_t *pRExC_state, I32 *flagp, U32 depth);
# define PERL_ARGS_ASSERT_REGATOM               \
        assert(pRExC_state); assert(flagp)

STATIC regnode_offset
S_regbranch(pTHX_ RExC_state_t *pRExC_state, I32 *flagp, I32 first, U32 depth);
# define PERL_ARGS_ASSERT_REGBRANCH             \
        assert(pRExC_state); assert(flagp)

STATIC regnode_offset
S_regclass(pTHX_ RExC_state_t *pRExC_state, I32 *flagp, U32 depth, const bool stop_at_1, bool allow_multi_fold, const bool silence_non_portable, const bool strict, bool optimizable, SV **ret_invlist);
# define PERL_ARGS_ASSERT_REGCLASS              \
        assert(pRExC_state); assert(flagp)

STATIC unsigned int
S_regex_set_precedence(const U8 my_operator)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGEX_SET_PRECEDENCE

STATIC void
S_reginsert(pTHX_ RExC_state_t *pRExC_state, const U8 op, const regnode_offset operand, const U32 depth);
# define PERL_ARGS_ASSERT_REGINSERT             \
        assert(pRExC_state)

STATIC regnode_offset
S_regnode_guts(pTHX_ RExC_state_t *pRExC_state, const STRLEN extra_len);
# define PERL_ARGS_ASSERT_REGNODE_GUTS          \
        assert(pRExC_state)

STATIC regnode_offset
S_regpiece(pTHX_ RExC_state_t *pRExC_state, I32 *flagp, U32 depth);
# define PERL_ARGS_ASSERT_REGPIECE              \
        assert(pRExC_state); assert(flagp)

STATIC regnode_offset
S_regpnode(pTHX_ RExC_state_t *pRExC_state, U8 op, SV *arg);
# define PERL_ARGS_ASSERT_REGPNODE              \
        assert(pRExC_state); assert(arg)

STATIC bool
S_regtail(pTHX_ RExC_state_t *pRExC_state, const regnode_offset p, const regnode_offset val, const U32 depth)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGTAIL               \
        assert(pRExC_state); assert(p); assert(val)

STATIC void
S_set_regex_pv(pTHX_ RExC_state_t *pRExC_state, REGEXP *Rx);
# define PERL_ARGS_ASSERT_SET_REGEX_PV          \
        assert(pRExC_state); assert(Rx)

STATIC void
S_skip_to_be_ignored_text(pTHX_ RExC_state_t *pRExC_state, char **p, const bool force_to_xmod);
# define PERL_ARGS_ASSERT_SKIP_TO_BE_IGNORED_TEXT \
        assert(pRExC_state); assert(p)

# if defined(DEBUGGING)
STATIC regnode_offset
S_regnode_guts_debug(pTHX_ RExC_state_t *pRExC_state, const U8 op, const STRLEN extra_len);
#   define PERL_ARGS_ASSERT_REGNODE_GUTS_DEBUG  \
        assert(pRExC_state)

STATIC bool
S_regtail_study(pTHX_ RExC_state_t *pRExC_state, regnode_offset p, const regnode_offset val, U32 depth)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_REGTAIL_STUDY       \
        assert(pRExC_state); assert(p); assert(val)

#   if defined(ENABLE_REGEX_SETS_DEBUGGING)
STATIC void
S_dump_regex_sets_structures(pTHX_ RExC_state_t *pRExC_state, AV *stack, const IV fence, AV *fence_stack);
#     define PERL_ARGS_ASSERT_DUMP_REGEX_SETS_STRUCTURES \
        assert(pRExC_state); assert(stack); assert(fence_stack)

#   endif
# endif /* defined(DEBUGGING) */
# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE Size_t
S_find_first_differing_byte_pos(const U8 *s1, const U8 *s2, const Size_t max);
#   define PERL_ARGS_ASSERT_FIND_FIRST_DIFFERING_BYTE_POS \
        assert(s1); assert(s2)

PERL_STATIC_INLINE char *
S_reg_skipcomment(RExC_state_t *pRExC_state, char *p);
#   define PERL_ARGS_ASSERT_REG_SKIPCOMMENT     \
        assert(pRExC_state); assert(p)

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_REGCOMP_C) */
#if defined(PERL_IN_REGCOMP_C) || defined(PERL_IN_REGCOMP_INVLIST_C)
PERL_CALLCONV void
Perl_populate_bitmap_from_invlist(pTHX_ SV *invlist, const UV offset, const U8 *bitmap, const Size_t len)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_POPULATE_BITMAP_FROM_INVLIST \
        assert(invlist); assert(bitmap)

PERL_CALLCONV void
Perl_populate_invlist_from_bitmap(pTHX_ const U8 *bitmap, const Size_t bitmap_len, SV **invlist, const UV offset)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_POPULATE_INVLIST_FROM_BITMAP \
        assert(bitmap); assert(invlist)

#endif /* defined(PERL_IN_REGCOMP_C) || defined(PERL_IN_REGCOMP_INVLIST_C) */
#if defined(PERL_IN_REGCOMP_C) || defined(PERL_IN_REGEXEC_C) || \
    defined(PERL_IN_TOKE_C)
PERL_CALLCONV bool
Perl_is_grapheme(pTHX_ const U8 *strbeg, const U8 *s, const U8 *strend, const UV cp)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_IS_GRAPHEME           \
        assert(strbeg); assert(s); assert(strend)

#endif
#if defined(PERL_IN_REGCOMP_C) || defined(PERL_IN_REGEXEC_C) || \
    defined(PERL_IN_UTF8_C)
PERL_CALLCONV UV
Perl__to_fold_latin1(const U8 c, U8 *p, STRLEN *lenp, const unsigned int flags);
# define PERL_ARGS_ASSERT__TO_FOLD_LATIN1       \
        assert(p); assert(lenp)

#endif
#if defined(PERL_IN_REGCOMP_C) || defined(PERL_IN_TOKE_C)
PERL_CALLCONV bool
Perl_regcurly(const char *s, const char *e, const char *result[5])
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGCURLY              \
        assert(s); assert(e)

#endif
#if defined(PERL_IN_REGCOMP_DEBUG_C) && defined(DEBUGGING)
STATIC U8
S_put_charclass_bitmap_innards(pTHX_ SV *sv, char *bitmap, SV *nonbitmap_invlist, SV *only_utf8_locale_invlist, const regnode * const node, const U8 flags, const bool force_as_is_display);
# define PERL_ARGS_ASSERT_PUT_CHARCLASS_BITMAP_INNARDS \
        assert(sv)

STATIC SV *
S_put_charclass_bitmap_innards_common(pTHX_ SV *invlist, SV *posixes, SV *only_utf8, SV *not_utf8, SV *only_utf8_locale, const bool invert);
# define PERL_ARGS_ASSERT_PUT_CHARCLASS_BITMAP_INNARDS_COMMON \
        assert(invlist)

STATIC void
S_put_charclass_bitmap_innards_invlist(pTHX_ SV *sv, SV *invlist);
# define PERL_ARGS_ASSERT_PUT_CHARCLASS_BITMAP_INNARDS_INVLIST \
        assert(sv); assert(invlist)

STATIC void
S_put_code_point(pTHX_ SV *sv, UV c);
# define PERL_ARGS_ASSERT_PUT_CODE_POINT        \
        assert(sv)

STATIC void
S_put_range(pTHX_ SV *sv, UV start, const UV end, const bool allow_literals);
# define PERL_ARGS_ASSERT_PUT_RANGE             \
        assert(sv)

STATIC void
S_regdump_extflags(pTHX_ const char *lead, const U32 flags);
# define PERL_ARGS_ASSERT_REGDUMP_EXTFLAGS

STATIC void
S_regdump_intflags(pTHX_ const char *lead, const U32 flags);
# define PERL_ARGS_ASSERT_REGDUMP_INTFLAGS

#endif /* defined(PERL_IN_REGCOMP_DEBUG_C) && defined(DEBUGGING) */
#if defined(PERL_IN_REGCOMP_INVLIST_C) && !defined(PERL_EXT_RE_BUILD)
STATIC void
S__append_range_to_invlist(pTHX_ SV * const invlist, const UV start, const UV end);
# define PERL_ARGS_ASSERT__APPEND_RANGE_TO_INVLIST \
        assert(invlist)

STATIC void
S_initialize_invlist_guts(pTHX_ SV *invlist, const Size_t initial_size);
# define PERL_ARGS_ASSERT_INITIALIZE_INVLIST_GUTS \
        assert(invlist)

STATIC void
S_invlist_replace_list_destroys_src(pTHX_ SV *dest, SV *src);
# define PERL_ARGS_ASSERT_INVLIST_REPLACE_LIST_DESTROYS_SRC \
        assert(dest); assert(src)

# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE UV *
S__invlist_array_init(SV * const invlist, const bool will_have_0)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT__INVLIST_ARRAY_INIT \
        assert(invlist)

PERL_STATIC_INLINE IV *
S_get_invlist_previous_index_addr(SV *invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_GET_INVLIST_PREVIOUS_INDEX_ADDR \
        assert(invlist)

PERL_STATIC_INLINE void
S_invlist_clear(pTHX_ SV *invlist);
#   define PERL_ARGS_ASSERT_INVLIST_CLEAR       \
        assert(invlist)

PERL_STATIC_INLINE UV
S_invlist_max(const SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_INVLIST_MAX         \
        assert(invlist)

PERL_STATIC_INLINE IV
S_invlist_previous_index(SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_INVLIST_PREVIOUS_INDEX \
        assert(invlist)

PERL_STATIC_INLINE void
S_invlist_set_previous_index(SV * const invlist, const IV index);
#   define PERL_ARGS_ASSERT_INVLIST_SET_PREVIOUS_INDEX \
        assert(invlist)

PERL_STATIC_INLINE void
S_invlist_trim(SV *invlist);
#   define PERL_ARGS_ASSERT_INVLIST_TRIM        \
        assert(invlist)

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_REGCOMP_INVLIST_C) && !defined(PERL_EXT_RE_BUILD) */
#if defined(PERL_IN_REGCOMP_STUDY_C)
STATIC SV *
S_get_ANYOF_cp_list_for_ssc(pTHX_ const RExC_state_t *pRExC_state, const regnode_charclass * const node);
# define PERL_ARGS_ASSERT_GET_ANYOF_CP_LIST_FOR_SSC \
        assert(pRExC_state); assert(node)

STATIC SV *
S_make_exactf_invlist(pTHX_ RExC_state_t *pRExC_state, regnode *node)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_MAKE_EXACTF_INVLIST   \
        assert(pRExC_state); assert(node)

STATIC void
S_rck_elide_nothing(pTHX_ regnode *node);
# define PERL_ARGS_ASSERT_RCK_ELIDE_NOTHING     \
        assert(node)

STATIC void
S_ssc_add_range(pTHX_ regnode_ssc *ssc, UV const start, UV const end);
# define PERL_ARGS_ASSERT_SSC_ADD_RANGE         \
        assert(ssc)

STATIC void
S_ssc_and(pTHX_ const RExC_state_t *pRExC_state, regnode_ssc *ssc, const regnode_charclass *and_with);
# define PERL_ARGS_ASSERT_SSC_AND               \
        assert(pRExC_state); assert(ssc); assert(and_with)

STATIC void
S_ssc_anything(pTHX_ regnode_ssc *ssc);
# define PERL_ARGS_ASSERT_SSC_ANYTHING          \
        assert(ssc)

STATIC void
S_ssc_clear_locale(regnode_ssc *ssc);
# define PERL_ARGS_ASSERT_SSC_CLEAR_LOCALE      \
        assert(ssc)

STATIC void
S_ssc_cp_and(pTHX_ regnode_ssc *ssc, UV const cp);
# define PERL_ARGS_ASSERT_SSC_CP_AND            \
        assert(ssc)

STATIC void
S_ssc_intersection(pTHX_ regnode_ssc *ssc, SV * const invlist, const bool invert_2nd);
# define PERL_ARGS_ASSERT_SSC_INTERSECTION      \
        assert(ssc); assert(invlist)

STATIC int
S_ssc_is_anything(const regnode_ssc *ssc)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SSC_IS_ANYTHING       \
        assert(ssc)

STATIC int
S_ssc_is_cp_posixl_init(const RExC_state_t *pRExC_state, const regnode_ssc *ssc)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SSC_IS_CP_POSIXL_INIT \
        assert(pRExC_state); assert(ssc)

STATIC void
S_ssc_or(pTHX_ const RExC_state_t *pRExC_state, regnode_ssc *ssc, const regnode_charclass *or_with);
# define PERL_ARGS_ASSERT_SSC_OR                \
        assert(pRExC_state); assert(ssc); assert(or_with)

STATIC void
S_ssc_union(pTHX_ regnode_ssc *ssc, SV * const invlist, const bool invert_2nd);
# define PERL_ARGS_ASSERT_SSC_UNION             \
        assert(ssc); assert(invlist)

STATIC void
S_unwind_scan_frames(pTHX_ const void *p);
# define PERL_ARGS_ASSERT_UNWIND_SCAN_FRAMES    \
        assert(p)

#endif /* defined(PERL_IN_REGCOMP_STUDY_C) */
#if defined(PERL_IN_REGEXEC_C)
STATIC LB_enum
S_advance_one_LB(pTHX_ U8 **curpos, const U8 * const strend, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ADVANCE_ONE_LB        \
        assert(curpos); assert(strend)

STATIC SB_enum
S_advance_one_SB(pTHX_ U8 **curpos, const U8 * const strend, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ADVANCE_ONE_SB        \
        assert(curpos); assert(strend)

STATIC WB_enum
S_advance_one_WB(pTHX_ U8 **curpos, const U8 * const strend, const bool utf8_target, const bool skip_Extend_Format)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ADVANCE_ONE_WB        \
        assert(curpos); assert(strend)

STATIC GCB_enum
S_backup_one_GCB(pTHX_ const U8 * const strbeg, U8 **curpos, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_BACKUP_ONE_GCB        \
        assert(strbeg); assert(curpos)

STATIC LB_enum
S_backup_one_LB(pTHX_ const U8 * const strbeg, U8 **curpos, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_BACKUP_ONE_LB         \
        assert(strbeg); assert(curpos)

STATIC SB_enum
S_backup_one_SB(pTHX_ const U8 * const strbeg, U8 **curpos, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_BACKUP_ONE_SB         \
        assert(strbeg); assert(curpos)

STATIC WB_enum
S_backup_one_WB(pTHX_ WB_enum *previous, const U8 * const strbeg, U8 **curpos, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_BACKUP_ONE_WB         \
        assert(previous); assert(strbeg); assert(curpos)

STATIC char *
S_find_byclass(pTHX_ regexp *prog, const regnode *c, char *s, const char *strend, regmatch_info *reginfo)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_FIND_BYCLASS          \
        assert(prog); assert(c); assert(s); assert(strend)

STATIC U8 *
S_find_next_masked(U8 *s, const U8 *send, const U8 byte, const U8 mask)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_FIND_NEXT_MASKED      \
        assert(s); assert(send)

STATIC U8 *
S_find_span_end(U8 *s, const U8 *send, const U8 span_byte)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_FIND_SPAN_END         \
        assert(s); assert(send)

STATIC U8 *
S_find_span_end_mask(U8 *s, const U8 *send, const U8 span_byte, const U8 mask)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_FIND_SPAN_END_MASK    \
        assert(s); assert(send)

STATIC bool
S_isFOO_lc(pTHX_ const U8 classnum, const U8 character)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISFOO_LC

STATIC bool
S_isFOO_utf8_lc(pTHX_ const U8 classnum, const U8 *character, const U8 *e)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISFOO_UTF8_LC         \
        assert(character); assert(e)

STATIC bool
S_isGCB(pTHX_ const GCB_enum before, const GCB_enum after, const U8 * const strbeg, const U8 * const curpos, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISGCB                 \
        assert(strbeg); assert(curpos)

STATIC bool
S_isLB(pTHX_ LB_enum before, LB_enum after, const U8 * const strbeg, const U8 * const curpos, const U8 * const strend, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISLB                  \
        assert(strbeg); assert(curpos); assert(strend)

STATIC bool
S_isSB(pTHX_ SB_enum before, SB_enum after, const U8 * const strbeg, const U8 * const curpos, const U8 * const strend, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISSB                  \
        assert(strbeg); assert(curpos); assert(strend)

STATIC bool
S_isWB(pTHX_ WB_enum previous, WB_enum before, WB_enum after, const U8 * const strbeg, const U8 * const curpos, const U8 * const strend, const bool utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISWB                  \
        assert(strbeg); assert(curpos); assert(strend)

STATIC I32
S_reg_check_named_buff_matched(const regexp *rex, const regnode *scan)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REG_CHECK_NAMED_BUFF_MATCHED \
        assert(rex); assert(scan)

STATIC void
S_regcp_restore(pTHX_ regexp *rex, I32 ix, U32 *maxopenparen_p comma_pDEPTH);
# define PERL_ARGS_ASSERT_REGCP_RESTORE         \
        assert(rex); assert(maxopenparen_p)

STATIC void
S_regcppop(pTHX_ regexp *rex, U32 *maxopenparen_p comma_pDEPTH);
# define PERL_ARGS_ASSERT_REGCPPOP              \
        assert(rex); assert(maxopenparen_p)

STATIC CHECKPOINT
S_regcppush(pTHX_ const regexp *rex, I32 parenfloor, U32 maxopenparen comma_pDEPTH);
# define PERL_ARGS_ASSERT_REGCPPUSH             \
        assert(rex)

STATIC U8 *
S_reghop3(U8 *s, SSize_t off, const U8 *lim)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGHOP3               \
        assert(s); assert(lim)

STATIC U8 *
S_reghop4(U8 *s, SSize_t off, const U8 *llim, const U8 *rlim)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGHOP4               \
        assert(s); assert(llim); assert(rlim)

STATIC U8 *
S_reghopmaybe3(U8 *s, SSize_t off, const U8 * const lim)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGHOPMAYBE3          \
        assert(s); assert(lim)

STATIC bool
S_reginclass(pTHX_ regexp * const prog, const regnode * const n, const U8 * const p, const U8 * const p_end, bool const utf8_target)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGINCLASS            \
        assert(n); assert(p); assert(p_end)

STATIC SSize_t
S_regmatch(pTHX_ regmatch_info *reginfo, char *startpos, regnode *prog)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGMATCH              \
        assert(reginfo); assert(startpos); assert(prog)

STATIC I32
S_regrepeat(pTHX_ regexp *prog, char **startposp, const regnode *p, char *loceol, regmatch_info * const reginfo, I32 max comma_pDEPTH)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGREPEAT             \
        assert(prog); assert(startposp); assert(p); assert(loceol); assert(reginfo); \
        assert(max)

STATIC bool
S_regtry(pTHX_ regmatch_info *reginfo, char **startposp)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_REGTRY                \
        assert(reginfo); assert(startposp)

STATIC bool
S_to_byte_substr(pTHX_ regexp *prog);
# define PERL_ARGS_ASSERT_TO_BYTE_SUBSTR        \
        assert(prog)

STATIC void
S_to_utf8_substr(pTHX_ regexp *prog);
# define PERL_ARGS_ASSERT_TO_UTF8_SUBSTR        \
        assert(prog)

# if defined(DEBUGGING)
STATIC void
S_debug_start_match(pTHX_ const REGEXP *prog, const bool do_utf8, const char *start, const char *end, const char *blurb);
#   define PERL_ARGS_ASSERT_DEBUG_START_MATCH   \
        assert(prog); assert(start); assert(end); assert(blurb)

STATIC void
S_dump_exec_pos(pTHX_ const char *locinput, const regnode *scan, const char *loc_regeol, const char *loc_bostr, const char *loc_reg_starttry, const bool do_utf8, const U32 depth);
#   define PERL_ARGS_ASSERT_DUMP_EXEC_POS       \
        assert(locinput); assert(scan); assert(loc_regeol); assert(loc_bostr); \
        assert(loc_reg_starttry)

PERL_CALLCONV int
Perl_re_exec_indentf(pTHX_ const char *fmt, U32 depth, ...)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_RE_EXEC_INDENTF     \
        assert(fmt)

# endif /* defined(DEBUGGING) */
# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE void
S_capture_clear(pTHX_ regexp *rex, U16 from_ix, U16 to_ix, const char *str comma_pDEPTH);
#   define PERL_ARGS_ASSERT_CAPTURE_CLEAR       \
        assert(rex); assert(str)

PERL_STATIC_INLINE I32
S_foldEQ_latin1_s2_folded(pTHX_ const char *a, const char *b, I32 len);
#   define PERL_ARGS_ASSERT_FOLDEQ_LATIN1_S2_FOLDED \
        assert(a); assert(b)

PERL_STATIC_INLINE void
S_unwind_paren(pTHX_ regexp *rex, U32 lp, U32 lcp comma_pDEPTH);
#   define PERL_ARGS_ASSERT_UNWIND_PAREN        \
        assert(rex)

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_REGEXEC_C) */
#if defined(PERL_IN_REGEX_ENGINE)

# if defined(DEBUGGING)
PERL_CALLCONV void
Perl_debug_peep(pTHX_ const char *str, const RExC_state_t *pRExC_state, regnode *scan, U32 depth, U32 flags)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_DEBUG_PEEP          \
        assert(str); assert(pRExC_state)

PERL_CALLCONV void
Perl_debug_show_study_flags(pTHX_ U32 flags, const char *open_str, const char *close_str)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_DEBUG_SHOW_STUDY_FLAGS \
        assert(open_str); assert(close_str)

PERL_CALLCONV void
Perl_debug_studydata(pTHX_ const char *where, scan_data_t *data, U32 depth, int is_inf, SSize_t min, SSize_t stopmin, SSize_t delta)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_DEBUG_STUDYDATA     \
        assert(where)

PERL_CALLCONV const regnode *
Perl_dumpuntil(pTHX_ const regexp *r, const regnode *start, const regnode *node, const regnode *last, const regnode *plast, SV *sv, I32 indent, U32 depth)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_DUMPUNTIL           \
        assert(r); assert(start); assert(node); assert(sv)

PERL_CALLCONV int
Perl_re_indentf(pTHX_ const char *fmt, U32 depth, ...)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_RE_INDENTF          \
        assert(fmt)

PERL_CALLCONV int
Perl_re_printf(pTHX_ const char *fmt, ...)
        __attribute__visibility__("hidden")
        __attribute__format__(__printf__,pTHX_1,pTHX_2);
#   define PERL_ARGS_ASSERT_RE_PRINTF           \
        assert(fmt)

PERL_CALLCONV void
Perl_regprop(pTHX_ const regexp *prog, SV *sv, const regnode *o, const regmatch_info *reginfo, const RExC_state_t *pRExC_state)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_REGPROP             \
        assert(sv); assert(o)

# endif /* defined(DEBUGGING) */
# if defined(PERL_EXT_RE_BUILD)
PERL_CALLCONV SV *
Perl_get_re_gclass_aux_data(pTHX_ const regexp *prog, const struct regnode *node, bool doinit, SV **listsvp, SV **lonly_utf8_locale, SV **output_invlist)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_GET_RE_GCLASS_AUX_DATA \
        assert(node)

# else
PERL_CALLCONV SV *
Perl_get_regclass_aux_data(pTHX_ const regexp *prog, const struct regnode *node, bool doinit, SV **listsvp, SV **lonly_utf8_locale, SV **output_invlist)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_GET_REGCLASS_AUX_DATA \
        assert(node)

# endif
# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE bool
Perl_check_regnode_after(pTHX_ const regnode *p, const STRLEN extra)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_CHECK_REGNODE_AFTER

PERL_STATIC_INLINE regnode *
Perl_regnext(pTHX_ const regnode *p)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_REGNEXT

PERL_STATIC_INLINE regnode *
Perl_regnode_after(pTHX_ const regnode *p, bool varies)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_REGNODE_AFTER

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_REGEX_ENGINE) */
#if defined(PERL_IN_SCOPE_C)
STATIC void
S_save_pushptri32ptr(pTHX_ void * const ptr1, const I32 i, void * const ptr2, const int type);
# define PERL_ARGS_ASSERT_SAVE_PUSHPTRI32PTR

STATIC SV *
S_save_scalar_at(pTHX_ SV **sptr, const U32 flags);
# define PERL_ARGS_ASSERT_SAVE_SCALAR_AT        \
        assert(sptr)

#endif /* defined(PERL_IN_SCOPE_C) */
#if defined(PERL_IN_SV_C)
STATIC char *
S_F0convert(NV nv, char * const endbuf, STRLEN * const len);
# define PERL_ARGS_ASSERT_F0CONVERT             \
        assert(endbuf); assert(len)

STATIC void
S_anonymise_cv_maybe(pTHX_ GV *gv, CV *cv);
# define PERL_ARGS_ASSERT_ANONYMISE_CV_MAYBE    \
        assert(gv); assert(cv)

STATIC void
S_assert_uft8_cache_coherent(pTHX_ const char * const func, STRLEN from_cache, STRLEN real, SV * const sv);
# define PERL_ARGS_ASSERT_ASSERT_UFT8_CACHE_COHERENT \
        assert(func); assert(sv)

STATIC bool
S_curse(pTHX_ SV * const sv, const bool check_refcnt);
# define PERL_ARGS_ASSERT_CURSE                 \
        assert(sv)

STATIC STRLEN
S_expect_number(pTHX_ const char ** const pattern)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_EXPECT_NUMBER         \
        assert(pattern)

STATIC SSize_t
S_find_array_subscript(pTHX_ const AV * const av, const SV * const val);
# define PERL_ARGS_ASSERT_FIND_ARRAY_SUBSCRIPT  \
        assert(val)

STATIC SV *
S_find_hash_subscript(pTHX_ const HV * const hv, const SV * const val);
# define PERL_ARGS_ASSERT_FIND_HASH_SUBSCRIPT   \
        assert(val)

STATIC SV *
S_find_uninit_var(pTHX_ const OP * const obase, const SV * const uninit_sv, bool match, const char **desc_p);
# define PERL_ARGS_ASSERT_FIND_UNINIT_VAR       \
        assert(desc_p)

STATIC bool
S_glob_2number(pTHX_ GV * const gv);
# define PERL_ARGS_ASSERT_GLOB_2NUMBER          \
        assert(gv)

STATIC void
S_glob_assign_glob(pTHX_ SV * const dsv, SV * const ssv, const int dtype);
# define PERL_ARGS_ASSERT_GLOB_ASSIGN_GLOB      \
        assert(dsv); assert(ssv)

PERL_CALLCONV SV *
Perl_more_sv(pTHX);
# define PERL_ARGS_ASSERT_MORE_SV

STATIC void
S_not_a_number(pTHX_ SV * const sv);
# define PERL_ARGS_ASSERT_NOT_A_NUMBER          \
        assert(sv)

STATIC void
S_not_incrementable(pTHX_ SV * const sv);
# define PERL_ARGS_ASSERT_NOT_INCREMENTABLE     \
        assert(sv)

STATIC PTR_TBL_ENT_t *
S_ptr_table_find(PTR_TBL_t * const tbl, const void * const sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_PTR_TABLE_FIND        \
        assert(tbl)

STATIC bool
S_sv_2iuv_common(pTHX_ SV * const sv);
# define PERL_ARGS_ASSERT_SV_2IUV_COMMON        \
        assert(sv)

STATIC void
S_sv_add_arena(pTHX_ char * const ptr, const U32 size, const U32 flags);
# define PERL_ARGS_ASSERT_SV_ADD_ARENA          \
        assert(ptr)

STATIC const char *
S_sv_display(pTHX_ SV * const sv, char *tmpbuf, STRLEN tmpbuf_size);
# define PERL_ARGS_ASSERT_SV_DISPLAY            \
        assert(sv); assert(tmpbuf)

STATIC STRLEN
S_sv_pos_b2u_midway(pTHX_ const U8 * const s, const U8 * const target, const U8 *end, STRLEN endu);
# define PERL_ARGS_ASSERT_SV_POS_B2U_MIDWAY     \
        assert(s); assert(target); assert(end)

STATIC STRLEN
S_sv_pos_u2b_cached(pTHX_ SV * const sv, MAGIC ** const mgp, const U8 * const start, const U8 * const send, STRLEN uoffset, STRLEN uoffset0, STRLEN boffset0);
# define PERL_ARGS_ASSERT_SV_POS_U2B_CACHED     \
        assert(sv); assert(mgp); assert(start); assert(send)

STATIC STRLEN
S_sv_pos_u2b_forwards(const U8 * const start, const U8 * const send, STRLEN * const uoffset, bool * const at_end, bool *canonical_position);
# define PERL_ARGS_ASSERT_SV_POS_U2B_FORWARDS   \
        assert(start); assert(send); assert(uoffset); assert(at_end); assert(canonical_position)

STATIC STRLEN
S_sv_pos_u2b_midway(const U8 * const start, const U8 *send, STRLEN uoffset, const STRLEN uend);
# define PERL_ARGS_ASSERT_SV_POS_U2B_MIDWAY     \
        assert(start); assert(send)

STATIC void
S_utf8_mg_len_cache_update(pTHX_ SV * const sv, MAGIC ** const mgp, const STRLEN ulen);
# define PERL_ARGS_ASSERT_UTF8_MG_LEN_CACHE_UPDATE \
        assert(sv); assert(mgp)

STATIC void
S_utf8_mg_pos_cache_update(pTHX_ SV * const sv, MAGIC ** const mgp, const STRLEN byte, const STRLEN utf8, const STRLEN blen);
# define PERL_ARGS_ASSERT_UTF8_MG_POS_CACHE_UPDATE \
        assert(sv); assert(mgp)

STATIC I32
S_visit(pTHX_ SVFUNC_t f, const U32 flags, const U32 mask);
# define PERL_ARGS_ASSERT_VISIT                 \
        assert(f)

# if defined(DEBUGGING)
STATIC void
S_del_sv(pTHX_ SV *p);
#   define PERL_ARGS_ASSERT_DEL_SV              \
        assert(p)

# endif
# if !defined(NV_PRESERVES_UV)
#   if defined(DEBUGGING)
STATIC int
S_sv_2iuv_non_preserve(pTHX_ SV * const sv, I32 numtype);
#     define PERL_ARGS_ASSERT_SV_2IUV_NON_PRESERVE \
        assert(sv)

#   else
STATIC int
S_sv_2iuv_non_preserve(pTHX_ SV * const sv);
#     define PERL_ARGS_ASSERT_SV_2IUV_NON_PRESERVE \
        assert(sv)

#   endif
# endif /* !defined(NV_PRESERVES_UV) */
# if defined(PERL_DEBUG_READONLY_COW)
STATIC void
S_sv_buf_to_rw(pTHX_ SV *sv);
#   define PERL_ARGS_ASSERT_SV_BUF_TO_RW        \
        assert(sv)

# endif
# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE void
S_sv_unglob(pTHX_ SV * const sv, U32 flags);
#   define PERL_ARGS_ASSERT_SV_UNGLOB           \
        assert(sv)

PERL_STATIC_INLINE char *
S_uiv_2buf(char * const buf, const IV iv, UV uv, const int is_uv, char ** const peob)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_UIV_2BUF            \
        assert(buf); assert(peob)

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
# if defined(USE_ITHREADS)
STATIC SV *
S_sv_dup_common(pTHX_ const SV * const ssv, CLONE_PARAMS * const param)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_SV_DUP_COMMON       \
        assert(ssv); assert(param)

STATIC void
S_sv_dup_hvaux(pTHX_ const SV * const ssv, SV *dsv, CLONE_PARAMS * const param);
#   define PERL_ARGS_ASSERT_SV_DUP_HVAUX        \
        assert(ssv); assert(dsv); assert(param)

STATIC SV **
S_sv_dup_inc_multiple(pTHX_ SV * const *source, SV **dest, SSize_t items, CLONE_PARAMS * const param);
#   define PERL_ARGS_ASSERT_SV_DUP_INC_MULTIPLE \
        assert(source); assert(dest); assert(param)

STATIC void
S_unreferenced_to_tmp_stack(pTHX_ AV * const unreferenced);
#   define PERL_ARGS_ASSERT_UNREFERENCED_TO_TMP_STACK \
        assert(unreferenced)

# endif /* defined(USE_ITHREADS) */
#endif /* defined(PERL_IN_SV_C) */
#if defined(PERL_IN_TOKE_C)
STATIC int
S_ao(pTHX_ int toketype);
# define PERL_ARGS_ASSERT_AO

STATIC void
S_check_uni(pTHX);
# define PERL_ARGS_ASSERT_CHECK_UNI

STATIC void
S_checkcomma(pTHX_ const char *s, const char *name, const char *what);
# define PERL_ARGS_ASSERT_CHECKCOMMA            \
        assert(s); assert(name); assert(what)

STATIC char *
S_filter_gets(pTHX_ SV *sv, STRLEN append)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_FILTER_GETS           \
        assert(sv)

STATIC HV *
S_find_in_my_stash(pTHX_ const char *pkgname, STRLEN len)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_FIND_IN_MY_STASH      \
        assert(pkgname)

STATIC void
S_force_ident(pTHX_ const char *s, int kind);
# define PERL_ARGS_ASSERT_FORCE_IDENT           \
        assert(s)

STATIC void
S_force_ident_maybe_lex(pTHX_ char pit);
# define PERL_ARGS_ASSERT_FORCE_IDENT_MAYBE_LEX

STATIC void
S_force_next(pTHX_ I32 type);
# define PERL_ARGS_ASSERT_FORCE_NEXT

STATIC char *
S_force_strict_version(pTHX_ char *s);
# define PERL_ARGS_ASSERT_FORCE_STRICT_VERSION  \
        assert(s)

STATIC char *
S_force_version(pTHX_ char *s, int guessing);
# define PERL_ARGS_ASSERT_FORCE_VERSION         \
        assert(s)

STATIC char *
S_force_word(pTHX_ char *start, int token, int check_keyword, int allow_pack);
# define PERL_ARGS_ASSERT_FORCE_WORD            \
        assert(start)

STATIC SV *
S_get_and_check_backslash_N_name_wrapper(pTHX_ const char *s, const char * const e)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_GET_AND_CHECK_BACKSLASH_N_NAME_WRAPPER \
        assert(s); assert(e)

STATIC void
S_incline(pTHX_ const char *s, const char *end);
# define PERL_ARGS_ASSERT_INCLINE               \
        assert(s); assert(end)

STATIC int
S_intuit_method(pTHX_ char *s, SV *ioname, CV *cv);
# define PERL_ARGS_ASSERT_INTUIT_METHOD         \
        assert(s)

STATIC int
S_intuit_more(pTHX_ char *s, char *e);
# define PERL_ARGS_ASSERT_INTUIT_MORE           \
        assert(s); assert(e)

STATIC I32
S_lop(pTHX_ I32 f, U8 x, char *s);
# define PERL_ARGS_ASSERT_LOP                   \
        assert(s)

PERL_STATIC_NO_RET void
S_missingterm(pTHX_ char *s, STRLEN len)
        __attribute__noreturn__;
# define PERL_ARGS_ASSERT_MISSINGTERM

STATIC SV *
S_new_constant(pTHX_ const char *s, STRLEN len, const char *key, STRLEN keylen, SV *sv, SV *pv, const char *type, STRLEN typelen, const char **error_msg);
# define PERL_ARGS_ASSERT_NEW_CONSTANT          \
        assert(key); assert(sv)

STATIC void
S_no_op(pTHX_ const char * const what, char *s);
# define PERL_ARGS_ASSERT_NO_OP                 \
        assert(what)

STATIC void
S_parse_ident(pTHX_ char **s, char **d, char * const e, int allow_package, bool is_utf8, bool check_dollar, bool tick_warn);
# define PERL_ARGS_ASSERT_PARSE_IDENT           \
        assert(s); assert(d); assert(e)

STATIC int
S_pending_ident(pTHX);
# define PERL_ARGS_ASSERT_PENDING_IDENT

STATIC char *
S_scan_const(pTHX_ char *start)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SCAN_CONST            \
        assert(start)

STATIC char *
S_scan_formline(pTHX_ char *s)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SCAN_FORMLINE         \
        assert(s)

STATIC char *
S_scan_heredoc(pTHX_ char *s)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SCAN_HEREDOC          \
        assert(s)

STATIC char *
S_scan_ident(pTHX_ char *s, char *dest, STRLEN destlen, I32 ck_uni);
# define PERL_ARGS_ASSERT_SCAN_IDENT            \
        assert(s); assert(dest)

STATIC char *
S_scan_inputsymbol(pTHX_ char *start)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SCAN_INPUTSYMBOL      \
        assert(start)

STATIC char *
S_scan_pat(pTHX_ char *start, I32 type)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SCAN_PAT              \
        assert(start)

STATIC char *
S_scan_subst(pTHX_ char *start)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SCAN_SUBST            \
        assert(start)

STATIC char *
S_scan_trans(pTHX_ char *start)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SCAN_TRANS            \
        assert(start)

STATIC I32
S_sublex_done(pTHX)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SUBLEX_DONE

STATIC I32
S_sublex_push(pTHX)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SUBLEX_PUSH

STATIC I32
S_sublex_start(pTHX)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SUBLEX_START

STATIC char *
S_swallow_bom(pTHX_ U8 *s)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SWALLOW_BOM           \
        assert(s)

STATIC char *
S_tokenize_use(pTHX_ int is_use, char *s)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_TOKENIZE_USE          \
        assert(s)

STATIC SV *
S_tokeq(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_TOKEQ                 \
        assert(sv)

STATIC void
S_update_debugger_info(pTHX_ SV *orig_sv, const char * const buf, STRLEN len);
# define PERL_ARGS_ASSERT_UPDATE_DEBUGGER_INFO

STATIC int
S_yywarn(pTHX_ const char * const s, U32 flags);
# define PERL_ARGS_ASSERT_YYWARN                \
        assert(s)

# if defined(DEBUGGING)
STATIC void
S_printbuf(pTHX_ const char * const fmt, const char * const s)
        __attribute__format__(__printf__,pTHX_1,0);
#   define PERL_ARGS_ASSERT_PRINTBUF            \
        assert(fmt); assert(s)

STATIC int
S_tokereport(pTHX_ I32 rv, const YYSTYPE *lvalp);
#   define PERL_ARGS_ASSERT_TOKEREPORT          \
        assert(lvalp)

# endif /* defined(DEBUGGING) */
# if defined(PERL_CR_FILTER)
STATIC I32
S_cr_textfilter(pTHX_ int idx, SV *sv, int maxlen);
#   define PERL_ARGS_ASSERT_CR_TEXTFILTER

STATIC void
S_strip_return(pTHX_ SV *sv);
#   define PERL_ARGS_ASSERT_STRIP_RETURN        \
        assert(sv)

# endif /* defined(PERL_CR_FILTER) */
# if !defined(PERL_NO_UTF16_FILTER)
STATIC U8 *
S_add_utf16_textfilter(pTHX_ U8 * const s, bool reversed);
#   define PERL_ARGS_ASSERT_ADD_UTF16_TEXTFILTER \
        assert(s)

STATIC I32
S_utf16_textfilter(pTHX_ int idx, SV *sv, int maxlen);
#   define PERL_ARGS_ASSERT_UTF16_TEXTFILTER    \
        assert(sv)

# endif /* !defined(PERL_NO_UTF16_FILTER) */
#endif /* defined(PERL_IN_TOKE_C) */
#if defined(PERL_IN_UNIVERSAL_C)
STATIC bool
S_isa_lookup(pTHX_ HV *stash, SV *namesv, const char *name, STRLEN len, U32 flags);

STATIC bool
S_sv_derived_from_svpvn(pTHX_ SV *sv, SV *namesv, const char *name, const STRLEN len, U32 flags);

#endif
#if defined(PERL_IN_UTF8_C)
STATIC UV
S__to_utf8_case(pTHX_ const UV original, const U8 *p, U8 *ustrp, STRLEN *lenp, SV *invlist, const I32 * const invmap, const U32 * const * const aux_tables, const U8 * const aux_table_lengths, const char * const normal);
# define PERL_ARGS_ASSERT__TO_UTF8_CASE         \
        assert(ustrp); assert(lenp); assert(invlist); assert(invmap); assert(normal)

STATIC UV
S_check_locale_boundary_crossing(pTHX_ const U8 * const p, const UV result, U8 * const ustrp, STRLEN *lenp)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_CHECK_LOCALE_BOUNDARY_CROSSING \
        assert(p); assert(ustrp); assert(lenp)

STATIC HV *
S_new_msg_hv(pTHX_ const char * const message, U32 categories, U32 flag)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEW_MSG_HV            \
        assert(message)

STATIC UV
S_to_case_cp_list(pTHX_ const UV original, const U32 ** const remaining_list, Size_t *remaining_count, SV *invlist, const I32 * const invmap, const U32 * const * const aux_tables, const U8 * const aux_table_lengths, const char * const normal);
# define PERL_ARGS_ASSERT_TO_CASE_CP_LIST       \
        assert(invlist); assert(invmap); assert(normal)

STATIC U8
S_to_lower_latin1(const U8 c, U8 *p, STRLEN *lenp, const char dummy)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_TO_LOWER_LATIN1

STATIC UV
S_turkic_fc(pTHX_ const U8 * const p, const U8 * const e, U8 *ustrp, STRLEN *lenp);
# define PERL_ARGS_ASSERT_TURKIC_FC             \
        assert(p); assert(e); assert(ustrp); assert(lenp)

STATIC UV
S_turkic_lc(pTHX_ const U8 * const p0, const U8 * const e, U8 *ustrp, STRLEN *lenp);
# define PERL_ARGS_ASSERT_TURKIC_LC             \
        assert(p0); assert(e); assert(ustrp); assert(lenp)

STATIC UV
S_turkic_uc(pTHX_ const U8 * const p, const U8 * const e, U8 *ustrp, STRLEN *lenp);
# define PERL_ARGS_ASSERT_TURKIC_UC             \
        assert(p); assert(e); assert(ustrp); assert(lenp)

STATIC char *
S_unexpected_non_continuation_text(pTHX_ const U8 * const s, STRLEN print_len, const STRLEN non_cont_byte_pos, const STRLEN expect_len)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_UNEXPECTED_NON_CONTINUATION_TEXT \
        assert(s)

# if 0
STATIC void
S_warn_on_first_deprecated_use(pTHX_ U32 category, const char * const name, const char * const alternative, const bool use_locale, const char * const file, const unsigned line);
#   define PERL_ARGS_ASSERT_WARN_ON_FIRST_DEPRECATED_USE \
        assert(name); assert(alternative); assert(file)

# endif
# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE int
S_does_utf8_overflow(const U8 * const s, const U8 *e, const bool consider_overlongs)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_DOES_UTF8_OVERFLOW  \
        assert(s); assert(e)

PERL_STATIC_INLINE int
S_isFF_overlong(const U8 * const s, const STRLEN len)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_ISFF_OVERLONG       \
        assert(s)

PERL_STATIC_INLINE bool
S_is_utf8_common(pTHX_ const U8 * const p, const U8 * const e, SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_IS_UTF8_COMMON      \
        assert(p); assert(e)

PERL_STATIC_INLINE int
S_is_utf8_overlong(const U8 * const s, const STRLEN len)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_IS_UTF8_OVERLONG    \
        assert(s)

# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#endif /* defined(PERL_IN_UTF8_C) */
#if defined(PERL_IN_UTIL_C)
STATIC bool
S_ckwarn_common(pTHX_ U32 w);
# define PERL_ARGS_ASSERT_CKWARN_COMMON

STATIC SV *
S_mess_alloc(pTHX);
# define PERL_ARGS_ASSERT_MESS_ALLOC

STATIC SV *
S_with_queued_errors(pTHX_ SV *ex);
# define PERL_ARGS_ASSERT_WITH_QUEUED_ERRORS    \
        assert(ex)

STATIC void
S_xs_version_bootcheck(pTHX_ U32 items, U32 ax, const char *xs_p, STRLEN xs_len);
# define PERL_ARGS_ASSERT_XS_VERSION_BOOTCHECK  \
        assert(xs_p)

# if defined(PERL_MEM_LOG) && !defined(PERL_MEM_LOG_NOIMPL)
STATIC void
S_mem_log_common(enum mem_log_type mlt, const UV n, const UV typesize, const char *type_name, const SV *sv, Malloc_t oldalloc, Malloc_t newalloc, const char *filename, const int linenumber, const char *funcname);
#   define PERL_ARGS_ASSERT_MEM_LOG_COMMON      \
        assert(type_name); assert(filename); assert(funcname)

# endif
# if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE U32
S_ptr_hash(PTRV u);
#   define PERL_ARGS_ASSERT_PTR_HASH

# endif
# if defined(PERL_USES_PL_PIDSTATUS)
STATIC void
S_pidgone(pTHX_ Pid_t pid, int status);
#   define PERL_ARGS_ASSERT_PIDGONE

# endif
#endif /* defined(PERL_IN_UTIL_C) */
#if defined(PERL_MEM_LOG)
PERL_CALLCONV Malloc_t
Perl_mem_log_alloc(const UV nconst, UV typesize, const char *type_name, Malloc_t newalloc, const char *filename, const int linenumber, const char *funcname);
# define PERL_ARGS_ASSERT_MEM_LOG_ALLOC         \
        assert(type_name); assert(filename); assert(funcname)

PERL_CALLCONV void
Perl_mem_log_del_sv(const SV *sv, const char *filename, int linenumber, const char *funcname);
# define PERL_ARGS_ASSERT_MEM_LOG_DEL_SV        \
        assert(sv); assert(filename); assert(funcname)

PERL_CALLCONV Malloc_t
Perl_mem_log_free(Malloc_t oldalloc, const char *filename, const int linenumber, const char *funcname);
# define PERL_ARGS_ASSERT_MEM_LOG_FREE          \
        assert(filename); assert(funcname)

PERL_CALLCONV void
Perl_mem_log_new_sv(const SV *sv, const char *filename, int linenumber, const char *funcname);
# define PERL_ARGS_ASSERT_MEM_LOG_NEW_SV        \
        assert(sv); assert(filename); assert(funcname)

PERL_CALLCONV Malloc_t
Perl_mem_log_realloc(const UV n, const UV typesize, const char *type_name, Malloc_t oldalloc, Malloc_t newalloc, const char *filename, const int linenumber, const char *funcname);
# define PERL_ARGS_ASSERT_MEM_LOG_REALLOC       \
        assert(type_name); assert(filename); assert(funcname)

#endif /* defined(PERL_MEM_LOG) */
#if !defined(PERL_NO_INLINE_FUNCTIONS)
PERL_STATIC_INLINE I32 *
Perl_CvDEPTH(const CV * const sv);
# define PERL_ARGS_ASSERT_CVDEPTH               \
        assert(sv)

PERL_STATIC_INLINE GV *
Perl_CvGV(pTHX_ CV *sv);
# define PERL_ARGS_ASSERT_CVGV                  \
        assert(sv)

PERL_STATIC_INLINE I32
Perl_POPMARK(pTHX);
# define PERL_ARGS_ASSERT_POPMARK

PERL_STATIC_INLINE struct regexp *
Perl_ReANY(const REGEXP * const re);
# define PERL_ARGS_ASSERT_REANY                 \
        assert(re)

PERL_STATIC_INLINE void
Perl_SvAMAGIC_off(SV *sv);
# define PERL_ARGS_ASSERT_SVAMAGIC_OFF          \
        assert(sv)

PERL_STATIC_INLINE void
Perl_SvAMAGIC_on(SV *sv);
# define PERL_ARGS_ASSERT_SVAMAGIC_ON           \
        assert(sv)

PERL_STATIC_INLINE void
Perl_SvGETMAGIC(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVGETMAGIC            \
        assert(sv)

PERL_STATIC_INLINE IV
Perl_SvIV(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVIV                  \
        assert(sv)

PERL_STATIC_INLINE IV
Perl_SvIV_nomg(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVIV_NOMG             \
        assert(sv)

PERL_STATIC_INLINE NV
Perl_SvNV(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVNV                  \
        assert(sv)

PERL_STATIC_INLINE NV
Perl_SvNV_nomg(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVNV_NOMG             \
        assert(sv)

PERL_STATIC_FORCE_INLINE bool
Perl_SvPVXtrue(pTHX_ SV *sv)
        __attribute__always_inline__;
# define PERL_ARGS_ASSERT_SVPVXTRUE             \
        assert(sv)

PERL_STATIC_INLINE void
Perl_SvREFCNT_dec(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVREFCNT_DEC

PERL_STATIC_INLINE void
Perl_SvREFCNT_dec_NN(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVREFCNT_DEC_NN       \
        assert(sv)

PERL_STATIC_INLINE SV *
Perl_SvREFCNT_dec_ret_NULL(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVREFCNT_DEC_RET_NULL

PERL_STATIC_INLINE SV *
Perl_SvREFCNT_inc(SV *sv);
# define PERL_ARGS_ASSERT_SVREFCNT_INC

PERL_STATIC_INLINE SV *
Perl_SvREFCNT_inc_NN(SV *sv);
# define PERL_ARGS_ASSERT_SVREFCNT_INC_NN       \
        assert(sv)

PERL_STATIC_INLINE void
Perl_SvREFCNT_inc_void(SV *sv);
# define PERL_ARGS_ASSERT_SVREFCNT_INC_VOID

PERL_STATIC_INLINE bool
Perl_SvTRUE(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVTRUE

PERL_STATIC_INLINE bool
Perl_SvTRUE_NN(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVTRUE_NN             \
        assert(sv)

PERL_STATIC_INLINE bool
Perl_SvTRUE_common(pTHX_ SV *sv, const bool sv_2bool_is_fallback);
# define PERL_ARGS_ASSERT_SVTRUE_COMMON         \
        assert(sv)

PERL_STATIC_INLINE bool
Perl_SvTRUE_nomg(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVTRUE_NOMG

PERL_STATIC_INLINE UV
Perl_SvUV(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVUV                  \
        assert(sv)

PERL_STATIC_INLINE UV
Perl_SvUV_nomg(pTHX_ SV *sv);
# define PERL_ARGS_ASSERT_SVUV_NOMG             \
        assert(sv)

PERL_STATIC_INLINE I32
Perl_TOPMARK(pTHX);
# define PERL_ARGS_ASSERT_TOPMARK

PERL_STATIC_INLINE void
Perl_append_utf8_from_native_byte(const U8 byte, U8 **dest);
# define PERL_ARGS_ASSERT_APPEND_UTF8_FROM_NATIVE_BYTE \
        assert(dest)

PERL_STATIC_INLINE Size_t
Perl_av_count(pTHX_ AV *av)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_AV_COUNT              \
        assert(av)

PERL_STATIC_INLINE SV **
Perl_av_fetch_simple(pTHX_ AV *av, SSize_t key, I32 lval)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_AV_FETCH_SIMPLE       \
        assert(av)

PERL_STATIC_INLINE AV *
Perl_av_new_alloc(pTHX_ SSize_t size, bool zeroflag)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_AV_NEW_ALLOC

PERL_STATIC_INLINE void
Perl_av_push_simple(pTHX_ AV *av, SV *val);
# define PERL_ARGS_ASSERT_AV_PUSH_SIMPLE        \
        assert(av); assert(val)

PERL_STATIC_INLINE SV **
Perl_av_store_simple(pTHX_ AV *av, SSize_t key, SV *val);
# define PERL_ARGS_ASSERT_AV_STORE_SIMPLE       \
        assert(av)

PERL_STATIC_INLINE I32
Perl_foldEQ(pTHX_ const char *a, const char *b, I32 len);
# define PERL_ARGS_ASSERT_FOLDEQ                \
        assert(a); assert(b)

PERL_STATIC_INLINE I32
Perl_foldEQ_latin1(pTHX_ const char *a, const char *b, I32 len);
# define PERL_ARGS_ASSERT_FOLDEQ_LATIN1         \
        assert(a); assert(b)

PERL_STATIC_INLINE I32
Perl_foldEQ_locale(pTHX_ const char *a, const char *b, I32 len);
# define PERL_ARGS_ASSERT_FOLDEQ_LOCALE         \
        assert(a); assert(b)

PERL_STATIC_INLINE MGVTBL *
Perl_get_vtbl(pTHX_ int vtbl_id)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_GET_VTBL

PERL_STATIC_INLINE Size_t
Perl_isC9_STRICT_UTF8_CHAR(const U8 * const s0, const U8 * const e)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISC9_STRICT_UTF8_CHAR \
        assert(s0); assert(e)

PERL_STATIC_INLINE Size_t
Perl_isSTRICT_UTF8_CHAR(const U8 * const s0, const U8 * const e)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISSTRICT_UTF8_CHAR    \
        assert(s0); assert(e)

PERL_STATIC_INLINE Size_t
Perl_isUTF8_CHAR(const U8 * const s0, const U8 * const e)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISUTF8_CHAR           \
        assert(s0); assert(e)

PERL_STATIC_INLINE Size_t
Perl_isUTF8_CHAR_flags(const U8 * const s0, const U8 * const e, const U32 flags)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ISUTF8_CHAR_FLAGS     \
        assert(s0); assert(e)

PERL_STATIC_INLINE bool
Perl_is_c9strict_utf8_string_loclen(const U8 *s, STRLEN len, const U8 **ep, STRLEN *el);
# define PERL_ARGS_ASSERT_IS_C9STRICT_UTF8_STRING_LOCLEN \
        assert(s)

PERL_STATIC_INLINE bool
Perl_is_safe_syscall(pTHX_ const char *pv, STRLEN len, const char *what, const char *op_name)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_IS_SAFE_SYSCALL       \
        assert(pv); assert(what); assert(op_name)

PERL_STATIC_INLINE bool
Perl_is_strict_utf8_string_loclen(const U8 *s, STRLEN len, const U8 **ep, STRLEN *el);
# define PERL_ARGS_ASSERT_IS_STRICT_UTF8_STRING_LOCLEN \
        assert(s)

PERL_STATIC_INLINE bool
Perl_is_utf8_fixed_width_buf_loclen_flags(const U8 * const s, STRLEN len, const U8 **ep, STRLEN *el, const U32 flags);
# define PERL_ARGS_ASSERT_IS_UTF8_FIXED_WIDTH_BUF_LOCLEN_FLAGS \
        assert(s)

PERL_STATIC_INLINE bool
Perl_is_utf8_invariant_string_loc(const U8 * const s, STRLEN len, const U8 **ep)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_IS_UTF8_INVARIANT_STRING_LOC \
        assert(s)

PERL_STATIC_INLINE bool
Perl_is_utf8_string_flags(const U8 *s, STRLEN len, const U32 flags)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_IS_UTF8_STRING_FLAGS  \
        assert(s)

PERL_STATIC_INLINE bool
Perl_is_utf8_string_loclen(const U8 *s, STRLEN len, const U8 **ep, STRLEN *el);
# define PERL_ARGS_ASSERT_IS_UTF8_STRING_LOCLEN \
        assert(s)

PERL_STATIC_INLINE bool
Perl_is_utf8_string_loclen_flags(const U8 *s, STRLEN len, const U8 **ep, STRLEN *el, const U32 flags);
# define PERL_ARGS_ASSERT_IS_UTF8_STRING_LOCLEN_FLAGS \
        assert(s)

PERL_STATIC_INLINE bool
Perl_is_utf8_valid_partial_char_flags(const U8 * const s0, const U8 * const e, const U32 flags)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_IS_UTF8_VALID_PARTIAL_CHAR_FLAGS \
        assert(s0); assert(e)

PERL_STATIC_INLINE unsigned
Perl_lsbit_pos32(U32 word)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_LSBIT_POS32

PERL_STATIC_INLINE char *
Perl_mortal_getenv(const char *str)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_MORTAL_GETENV         \
        assert(str)

PERL_STATIC_INLINE unsigned
Perl_msbit_pos32(U32 word)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_MSBIT_POS32

PERL_STATIC_INLINE OP *
Perl_newPADxVOP(pTHX_ I32 type, I32 flags, PADOFFSET padix)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEWPADXVOP

PERL_STATIC_INLINE SV *
Perl_newRV_noinc(pTHX_ SV * const tmpRef)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEWRV_NOINC           \
        assert(tmpRef)

PERL_STATIC_INLINE SV *
Perl_newSV_type(pTHX_ const svtype type)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEWSV_TYPE

PERL_STATIC_FORCE_INLINE SV *
Perl_newSV_type_mortal(pTHX_ const svtype type)
        __attribute__warn_unused_result__
        __attribute__always_inline__;
# define PERL_ARGS_ASSERT_NEWSV_TYPE_MORTAL

PERL_STATIC_INLINE char *
Perl_savepv(pTHX_ const char *pv)
        __attribute__malloc__
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SAVEPV

PERL_STATIC_INLINE char *
Perl_savepvn(pTHX_ const char *pv, Size_t len)
        __attribute__malloc__
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SAVEPVN

PERL_STATIC_INLINE char *
Perl_savesharedsvpv(pTHX_ SV *sv)
        __attribute__malloc__
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SAVESHAREDSVPV        \
        assert(sv)

PERL_STATIC_INLINE char *
Perl_savesvpv(pTHX_ SV *sv)
        __attribute__malloc__
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SAVESVPV              \
        assert(sv)

PERL_STATIC_INLINE unsigned
Perl_single_1bit_pos32(U32 word)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SINGLE_1BIT_POS32

PERL_STATIC_INLINE bool
Perl_sv_only_taint_gmagic(SV *sv);
# define PERL_ARGS_ASSERT_SV_ONLY_TAINT_GMAGIC  \
        assert(sv)

PERL_STATIC_INLINE char *
Perl_sv_pvbyten_force_wrapper(pTHX_ SV * const sv, STRLEN * const lp, const U32 dummy);
# define PERL_ARGS_ASSERT_SV_PVBYTEN_FORCE_WRAPPER \
        assert(sv)

PERL_STATIC_INLINE char *
Perl_sv_pvutf8n_force_wrapper(pTHX_ SV * const sv, STRLEN * const lp, const U32 dummy);
# define PERL_ARGS_ASSERT_SV_PVUTF8N_FORCE_WRAPPER \
        assert(sv)

PERL_STATIC_INLINE char  *
Perl_sv_setpv_freshbuf(pTHX_ SV * const sv);
# define PERL_ARGS_ASSERT_SV_SETPV_FRESHBUF     \
        assert(sv)

PERL_STATIC_INLINE IV
Perl_utf8_distance(pTHX_ const U8 *a, const U8 *b)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_UTF8_DISTANCE         \
        assert(a); assert(b)

PERL_STATIC_INLINE U8 *
Perl_utf8_hop(const U8 *s, SSize_t off)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_UTF8_HOP              \
        assert(s)

PERL_STATIC_INLINE U8 *
Perl_utf8_hop_back(const U8 *s, SSize_t off, const U8 *start)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_UTF8_HOP_BACK         \
        assert(s); assert(start)

PERL_STATIC_INLINE U8 *
Perl_utf8_hop_forward(const U8 *s, SSize_t off, const U8 *end)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_UTF8_HOP_FORWARD      \
        assert(s); assert(end)

PERL_STATIC_INLINE U8 *
Perl_utf8_hop_safe(const U8 *s, SSize_t off, const U8 *start, const U8 *end)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_UTF8_HOP_SAFE         \
        assert(s); assert(start); assert(end)

PERL_STATIC_INLINE UV
Perl_utf8_to_uvchr_buf_helper(pTHX_ const U8 *s, const U8 *send, STRLEN *retlen);
# define PERL_ARGS_ASSERT_UTF8_TO_UVCHR_BUF_HELPER \
        assert(s); assert(send)

PERL_STATIC_INLINE UV
Perl_utf8n_to_uvchr_msgs(const U8 *s, STRLEN curlen, STRLEN *retlen, const U32 flags, U32 *errors, AV **msgs);
# define PERL_ARGS_ASSERT_UTF8N_TO_UVCHR_MSGS   \
        assert(s)

PERL_STATIC_INLINE UV
Perl_valid_utf8_to_uvchr(const U8 *s, STRLEN *retlen)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_VALID_UTF8_TO_UVCHR   \
        assert(s)

PERL_STATIC_INLINE void
Perl_cx_popblock(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_POPBLOCK           \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_popeval(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_POPEVAL            \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_popformat(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_POPFORMAT          \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_popgiven(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_POPGIVEN           \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_poploop(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_POPLOOP            \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_popsub(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_POPSUB             \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_popsub_args(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_POPSUB_ARGS        \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_popsub_common(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_POPSUB_COMMON      \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_popwhen(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_POPWHEN            \
        assert(cx)

PERL_STATIC_INLINE PERL_CONTEXT *
Perl_cx_pushblock(pTHX_ U8 type, U8 gimme, SV **sp, I32 saveix);
# define PERL_ARGS_ASSERT_CX_PUSHBLOCK          \
        assert(sp)

PERL_STATIC_INLINE void
Perl_cx_pusheval(pTHX_ PERL_CONTEXT *cx, OP *retop, SV *namesv);
# define PERL_ARGS_ASSERT_CX_PUSHEVAL           \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_pushformat(pTHX_ PERL_CONTEXT *cx, CV *cv, OP *retop, GV *gv);
# define PERL_ARGS_ASSERT_CX_PUSHFORMAT         \
        assert(cx); assert(cv)

PERL_STATIC_INLINE void
Perl_cx_pushgiven(pTHX_ PERL_CONTEXT *cx, SV *orig_defsv);
# define PERL_ARGS_ASSERT_CX_PUSHGIVEN          \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_pushloop_for(pTHX_ PERL_CONTEXT *cx, void *itervarp, SV *itersave);
# define PERL_ARGS_ASSERT_CX_PUSHLOOP_FOR       \
        assert(cx); assert(itervarp)

PERL_STATIC_INLINE void
Perl_cx_pushloop_plain(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_PUSHLOOP_PLAIN     \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_pushsub(pTHX_ PERL_CONTEXT *cx, CV *cv, OP *retop, bool hasargs);
# define PERL_ARGS_ASSERT_CX_PUSHSUB            \
        assert(cx); assert(cv)

PERL_STATIC_INLINE void
Perl_cx_pushtry(pTHX_ PERL_CONTEXT *cx, OP *retop);
# define PERL_ARGS_ASSERT_CX_PUSHTRY            \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_pushwhen(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_PUSHWHEN           \
        assert(cx)

PERL_STATIC_INLINE void
Perl_cx_topblock(pTHX_ PERL_CONTEXT *cx);
# define PERL_ARGS_ASSERT_CX_TOPBLOCK           \
        assert(cx)

PERL_STATIC_INLINE U8
Perl_gimme_V(pTHX);
# define PERL_ARGS_ASSERT_GIMME_V

# if !defined(HAS_STRLCAT)
PERL_STATIC_INLINE Size_t
Perl_my_strlcat(char *dst, const char *src, Size_t size);
#   define PERL_ARGS_ASSERT_MY_STRLCAT

# endif
# if !defined(HAS_STRNLEN)
PERL_STATIC_INLINE Size_t
Perl_my_strnlen(const char *str, Size_t maxlen);
#   define PERL_ARGS_ASSERT_MY_STRNLEN          \
        assert(str)

# endif
# if defined(PERL_CORE) || defined(PERL_EXT)
PERL_STATIC_INLINE bool
Perl_is_utf8_non_invariant_string(const U8 * const s, STRLEN len)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_IS_UTF8_NON_INVARIANT_STRING \
        assert(s)

PERL_STATIC_INLINE STRLEN
S_sv_or_pv_pos_u2b(pTHX_ SV *sv, const char *pv, STRLEN pos, STRLEN *lenp);
#   define PERL_ARGS_ASSERT_SV_OR_PV_POS_U2B    \
        assert(sv); assert(pv)

PERL_STATIC_INLINE Size_t
S_variant_under_utf8_count(const U8 * const s, const U8 * const e)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_VARIANT_UNDER_UTF8_COUNT \
        assert(s); assert(e)

#   if !defined(HAS_MEMRCHR)
PERL_STATIC_INLINE void *
S_my_memrchr(const char *s, const char c, const STRLEN len);
#     define PERL_ARGS_ASSERT_MY_MEMRCHR        \
        assert(s)

#   endif
# endif /* defined(PERL_CORE) || defined(PERL_EXT) */
# if defined(PERL_IN_DOOP_C)    || defined(PERL_IN_OP_C)        || \
     defined(PERL_IN_PP_C)      || defined(PERL_IN_REGCOMP_ANY) || \
     defined(PERL_IN_REGEXEC_C) || defined(PERL_IN_TOKE_C)      || \
     defined(PERL_IN_UTF8_C)
PERL_STATIC_INLINE bool
S__invlist_contains_cp(SV * const invlist, const UV cp)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT__INVLIST_CONTAINS_CP \
        assert(invlist)

PERL_STATIC_INLINE UV
S__invlist_len(SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT__INVLIST_LEN        \
        assert(invlist)

PERL_STATIC_INLINE bool *
S_get_invlist_offset_addr(SV *invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_GET_INVLIST_OFFSET_ADDR \
        assert(invlist)

PERL_STATIC_INLINE UV *
S_invlist_array(SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_INVLIST_ARRAY       \
        assert(invlist)

PERL_STATIC_INLINE bool
S_is_invlist(const SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_IS_INVLIST

# endif /* defined(PERL_IN_DOOP_C)    || defined(PERL_IN_OP_C)        ||
           defined(PERL_IN_PP_C)      || defined(PERL_IN_REGCOMP_ANY) ||
           defined(PERL_IN_REGEXEC_C) || defined(PERL_IN_TOKE_C)      ||
           defined(PERL_IN_UTF8_C) */
# if defined(PERL_IN_DOOP_C) || defined(PERL_IN_OP_C) || \
     defined(PERL_IN_REGCOMP_ANY)
PERL_STATIC_INLINE SV *
S_add_cp_to_invlist(pTHX_ SV *invlist, const UV cp)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_ADD_CP_TO_INVLIST

PERL_STATIC_INLINE void
S_invlist_extend(pTHX_ SV * const invlist, const UV len);
#   define PERL_ARGS_ASSERT_INVLIST_EXTEND      \
        assert(invlist)

PERL_STATIC_INLINE UV
S_invlist_highest(SV * const invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_INVLIST_HIGHEST     \
        assert(invlist)

PERL_STATIC_INLINE void
S_invlist_set_len(pTHX_ SV * const invlist, const UV len, const bool offset);
#   define PERL_ARGS_ASSERT_INVLIST_SET_LEN     \
        assert(invlist)

# endif /* defined(PERL_IN_DOOP_C) || defined(PERL_IN_OP_C) ||
           defined(PERL_IN_REGCOMP_ANY) */
# if defined(PERL_IN_OP_C) || defined(PERL_IN_PAD_C)
PERL_STATIC_INLINE bool
S_PadnameIN_SCOPE(const PADNAME * const pn, const U32 seq);
#   define PERL_ARGS_ASSERT_PADNAMEIN_SCOPE     \
        assert(pn)

# endif
# if defined(PERL_IN_OP_C) || defined(PERL_IN_REGCOMP_ANY)
PERL_STATIC_INLINE STRLEN *
S_get_invlist_iter_addr(SV *invlist)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_GET_INVLIST_ITER_ADDR \
        assert(invlist)

PERL_STATIC_INLINE void
S_invlist_iterfinish(SV *invlist);
#   define PERL_ARGS_ASSERT_INVLIST_ITERFINISH  \
        assert(invlist)

PERL_STATIC_INLINE void
S_invlist_iterinit(SV *invlist);
#   define PERL_ARGS_ASSERT_INVLIST_ITERINIT    \
        assert(invlist)

PERL_STATIC_INLINE bool
S_invlist_iternext(SV *invlist, UV *start, UV *end)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_INVLIST_ITERNEXT    \
        assert(invlist); assert(start); assert(end)

# endif /* defined(PERL_IN_OP_C) || defined(PERL_IN_REGCOMP_ANY) */
# if defined(PERL_IN_PP_C) || defined(PERL_IN_PP_HOT_C)
PERL_STATIC_INLINE bool
S_lossless_NV_to_IV(const NV nv, IV *ivp)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_LOSSLESS_NV_TO_IV   \
        assert(ivp)

# endif
# if defined(PERL_IN_PP_C)   || defined(PERL_IN_REGCOMP_ANY) || \
     defined(PERL_IN_TOKE_C) || defined(PERL_IN_UNIVERSAL_C)
PERL_STATIC_INLINE const char *
S_get_regex_charset_name(const U32 flags, STRLEN * const lenp);
#   define PERL_ARGS_ASSERT_GET_REGEX_CHARSET_NAME \
        assert(lenp)

# endif
# if defined(U64TYPE)
PERL_STATIC_INLINE unsigned
Perl_lsbit_pos64(U64 word)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_LSBIT_POS64

PERL_STATIC_INLINE unsigned
Perl_msbit_pos64(U64 word)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_MSBIT_POS64

PERL_STATIC_INLINE unsigned
Perl_single_1bit_pos64(U64 word)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_SINGLE_1BIT_POS64

# endif /* defined(U64TYPE) */
# if defined(USE_ITHREADS)
PERL_STATIC_INLINE AV *
Perl_cop_file_avn(pTHX_ const COP *cop);
#   define PERL_ARGS_ASSERT_COP_FILE_AVN        \
        assert(cop)

#   if !defined(PERL_IMPLICIT_SYS)
PERL_STATIC_INLINE bool
S_PerlEnv_putenv(pTHX_ char *str);
#     define PERL_ARGS_ASSERT_PERLENV_PUTENV    \
        assert(str)

#   endif
# endif /* defined(USE_ITHREADS) */
# if !defined(WIN32)
PERL_STATIC_INLINE void *
Perl_get_context(void)
        __attribute__warn_unused_result__;
#   define PERL_ARGS_ASSERT_GET_CONTEXT

# endif
#endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
#if defined(PERL_USE_3ARG_SIGHANDLER)
PERL_CALLCONV Signal_t
Perl_csighandler(int sig, Siginfo_t *info, void *uap);
# define PERL_ARGS_ASSERT_CSIGHANDLER

PERL_CALLCONV Signal_t
Perl_sighandler(int sig, Siginfo_t *info, void *uap)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SIGHANDLER

#else /* if !defined(PERL_USE_3ARG_SIGHANDLER) */
PERL_CALLCONV Signal_t
Perl_csighandler(int sig);
# define PERL_ARGS_ASSERT_CSIGHANDLER

PERL_CALLCONV Signal_t
Perl_sighandler(int sig)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_SIGHANDLER

#endif /* !defined(PERL_USE_3ARG_SIGHANDLER) */
#if defined(U64TYPE)

#endif
#if defined(UNLINK_ALL_VERSIONS)
PERL_CALLCONV I32
Perl_unlnk(pTHX_ const char *f);
# define PERL_ARGS_ASSERT_UNLNK                 \
        assert(f)

#endif
#if defined(USE_C_BACKTRACE)
PERL_CALLCONV bool
Perl_dump_c_backtrace(pTHX_ PerlIO *fp, int max_depth, int skip);
# define PERL_ARGS_ASSERT_DUMP_C_BACKTRACE      \
        assert(fp)

/* PERL_CALLCONV void
free_c_backtrace(pTHX_ Perl_c_backtrace *bt); */

PERL_CALLCONV Perl_c_backtrace *
Perl_get_c_backtrace(pTHX_ int max_depth, int skip)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_GET_C_BACKTRACE

PERL_CALLCONV SV *
Perl_get_c_backtrace_dump(pTHX_ int max_depth, int skip);
# define PERL_ARGS_ASSERT_GET_C_BACKTRACE_DUMP

#endif /* defined(USE_C_BACKTRACE) */
#if defined(USE_DTRACE)
PERL_CALLCONV void
Perl_dtrace_probe_call(pTHX_ CV *cv, bool is_call);
# define PERL_ARGS_ASSERT_DTRACE_PROBE_CALL     \
        assert(cv)

PERL_CALLCONV void
Perl_dtrace_probe_load(pTHX_ const char *name, bool is_loading);
# define PERL_ARGS_ASSERT_DTRACE_PROBE_LOAD     \
        assert(name)

PERL_CALLCONV void
Perl_dtrace_probe_op(pTHX_ const OP *op);
# define PERL_ARGS_ASSERT_DTRACE_PROBE_OP       \
        assert(op)

PERL_CALLCONV void
Perl_dtrace_probe_phase(pTHX_ enum perl_phase phase);
# define PERL_ARGS_ASSERT_DTRACE_PROBE_PHASE

#endif /* defined(USE_DTRACE) */
#if defined(USE_ITHREADS)
PERL_CALLCONV PADOFFSET
Perl_alloccopstash(pTHX_ HV *hv);
# define PERL_ARGS_ASSERT_ALLOCCOPSTASH         \
        assert(hv)

PERL_CALLCONV void *
Perl_any_dup(pTHX_ void *v, const PerlInterpreter *proto_perl)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_ANY_DUP               \
        assert(proto_perl)

PERL_CALLCONV void
Perl_clone_params_del(CLONE_PARAMS *param);
# define PERL_ARGS_ASSERT_CLONE_PARAMS_DEL      \
        assert(param)

PERL_CALLCONV CLONE_PARAMS *
Perl_clone_params_new(PerlInterpreter * const from, PerlInterpreter * const to)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_CLONE_PARAMS_NEW      \
        assert(from); assert(to)

PERL_CALLCONV PERL_CONTEXT *
Perl_cx_dup(pTHX_ PERL_CONTEXT *cx, I32 ix, I32 max, CLONE_PARAMS *param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_CX_DUP                \
        assert(param)

PERL_CALLCONV DIR *
Perl_dirp_dup(pTHX_ DIR * const dp, CLONE_PARAMS * const param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_DIRP_DUP              \
        assert(param)

PERL_CALLCONV PerlIO *
Perl_fp_dup(pTHX_ PerlIO * const fp, const char type, CLONE_PARAMS * const param);
# define PERL_ARGS_ASSERT_FP_DUP                \
        assert(param)

PERL_CALLCONV GP *
Perl_gp_dup(pTHX_ GP * const gp, CLONE_PARAMS * const param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_GP_DUP                \
        assert(param)

PERL_CALLCONV HE *
Perl_he_dup(pTHX_ const HE *e, bool shared, CLONE_PARAMS *param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_HE_DUP                \
        assert(param)

PERL_CALLCONV HEK *
Perl_hek_dup(pTHX_ HEK *e, CLONE_PARAMS *param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_HEK_DUP               \
        assert(param)

PERL_CALLCONV MAGIC *
Perl_mg_dup(pTHX_ MAGIC *mg, CLONE_PARAMS * const param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_MG_DUP                \
        assert(param)

PERL_CALLCONV struct mro_meta *
Perl_mro_meta_dup(pTHX_ struct mro_meta *smeta, CLONE_PARAMS *param)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MRO_META_DUP          \
        assert(smeta); assert(param)

PERL_CALLCONV OP *
Perl_newPADOP(pTHX_ I32 type, I32 flags, SV *sv)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_NEWPADOP              \
        assert(sv)

PERL_CALLCONV PADLIST *
Perl_padlist_dup(pTHX_ PADLIST *srcpad, CLONE_PARAMS *param)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_PADLIST_DUP           \
        assert(srcpad); assert(param)

PERL_CALLCONV PADNAME *
Perl_padname_dup(pTHX_ PADNAME *src, CLONE_PARAMS *param)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_PADNAME_DUP           \
        assert(src); assert(param)

PERL_CALLCONV PADNAMELIST *
Perl_padnamelist_dup(pTHX_ PADNAMELIST *srcpad, CLONE_PARAMS *param)
        __attribute__warn_unused_result__
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_PADNAMELIST_DUP       \
        assert(srcpad); assert(param)

PERL_CALLCONV yy_parser *
Perl_parser_dup(pTHX_ const yy_parser * const proto, CLONE_PARAMS * const param);
# define PERL_ARGS_ASSERT_PARSER_DUP            \
        assert(param)

PERL_CALLCONV PerlInterpreter *
perl_clone(PerlInterpreter *proto_perl, UV flags);
# define PERL_ARGS_ASSERT_PERL_CLONE            \
        assert(proto_perl)

PERL_CALLCONV void
Perl_re_dup_guts(pTHX_ const REGEXP *sstr, REGEXP *dstr, CLONE_PARAMS *param);
# define PERL_ARGS_ASSERT_RE_DUP_GUTS           \
        assert(sstr); assert(dstr); assert(param)

PERL_CALLCONV void *
Perl_regdupe_internal(pTHX_ REGEXP * const r, CLONE_PARAMS *param);
# define PERL_ARGS_ASSERT_REGDUPE_INTERNAL      \
        assert(r); assert(param)

PERL_CALLCONV void
Perl_rvpv_dup(pTHX_ SV * const dsv, const SV * const ssv, CLONE_PARAMS * const param);
# define PERL_ARGS_ASSERT_RVPV_DUP              \
        assert(dsv); assert(ssv); assert(param)

PERL_CALLCONV PERL_SI *
Perl_si_dup(pTHX_ PERL_SI *si, CLONE_PARAMS *param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SI_DUP                \
        assert(param)

PERL_CALLCONV ANY *
Perl_ss_dup(pTHX_ PerlInterpreter *proto_perl, CLONE_PARAMS *param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SS_DUP                \
        assert(proto_perl); assert(param)

PERL_CALLCONV SV *
Perl_sv_dup(pTHX_ const SV * const ssv, CLONE_PARAMS * const param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_DUP                \
        assert(param)

PERL_CALLCONV SV *
Perl_sv_dup_inc(pTHX_ const SV * const ssv, CLONE_PARAMS * const param)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_SV_DUP_INC            \
        assert(param)

# if defined(PERL_IN_OP_C) || defined(PERL_IN_PEEP_C)
PERL_CALLCONV void
Perl_op_relocate_sv(pTHX_ SV **svp, PADOFFSET *targp)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_OP_RELOCATE_SV      \
        assert(svp); assert(targp)

# endif
#else /* if !defined(USE_ITHREADS) */
/* PERL_CALLCONV void
CopFILEGV_set(pTHX_ COP *c, GV *gv); */

#endif
#if defined(USE_LOCALE_COLLATE)
PERL_CALLCONV int
Perl_magic_freecollxfrm(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MAGIC_FREECOLLXFRM    \
        assert(sv); assert(mg)

PERL_CALLCONV int
Perl_magic_setcollxfrm(pTHX_ SV *sv, MAGIC *mg)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_MAGIC_SETCOLLXFRM     \
        assert(sv); assert(mg)

PERL_CALLCONV SV *
Perl_strxfrm(pTHX_ SV *src);
# define PERL_ARGS_ASSERT_STRXFRM               \
        assert(src)

PERL_CALLCONV char *
Perl_sv_collxfrm_flags(pTHX_ SV * const sv, STRLEN * const nxp, I32 const flags);
# define PERL_ARGS_ASSERT_SV_COLLXFRM_FLAGS     \
        assert(sv); assert(nxp)

# if defined(PERL_IN_LOCALE_C) || defined(PERL_IN_MATHOMS_C) || \
     defined(PERL_IN_SV_C)
PERL_CALLCONV char *
Perl_mem_collxfrm_(pTHX_ const char *input_string, STRLEN len, STRLEN *xlen, bool utf8)
        __attribute__visibility__("hidden");
#   define PERL_ARGS_ASSERT_MEM_COLLXFRM_       \
        assert(input_string); assert(xlen)

# endif
#endif /* defined(USE_LOCALE_COLLATE) */
#if defined(USE_PERLIO)
PERL_CALLCONV void
Perl_PerlIO_clearerr(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_CLEARERR

PERL_CALLCONV int
Perl_PerlIO_close(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_CLOSE

PERL_CALLCONV int
Perl_PerlIO_eof(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_EOF

PERL_CALLCONV int
Perl_PerlIO_error(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_ERROR

PERL_CALLCONV int
Perl_PerlIO_fileno(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_FILENO

PERL_CALLCONV int
Perl_PerlIO_fill(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_FILL

PERL_CALLCONV int
Perl_PerlIO_flush(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_FLUSH

PERL_CALLCONV STDCHAR *
Perl_PerlIO_get_base(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_GET_BASE

PERL_CALLCONV SSize_t
Perl_PerlIO_get_bufsiz(pTHX_ PerlIO *f)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_PERLIO_GET_BUFSIZ

PERL_CALLCONV SSize_t
Perl_PerlIO_get_cnt(pTHX_ PerlIO *f)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_PERLIO_GET_CNT

PERL_CALLCONV STDCHAR *
Perl_PerlIO_get_ptr(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_GET_PTR

PERL_CALLCONV SSize_t
Perl_PerlIO_read(pTHX_ PerlIO *f, void *vbuf, Size_t count);
# define PERL_ARGS_ASSERT_PERLIO_READ           \
        assert(vbuf)

PERL_CALLCONV void
Perl_PerlIO_restore_errno(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_RESTORE_ERRNO

PERL_CALLCONV void
Perl_PerlIO_save_errno(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_SAVE_ERRNO

PERL_CALLCONV int
Perl_PerlIO_seek(pTHX_ PerlIO *f, Off_t offset, int whence);
# define PERL_ARGS_ASSERT_PERLIO_SEEK

PERL_CALLCONV void
Perl_PerlIO_set_cnt(pTHX_ PerlIO *f, SSize_t cnt);
# define PERL_ARGS_ASSERT_PERLIO_SET_CNT

PERL_CALLCONV void
Perl_PerlIO_set_ptrcnt(pTHX_ PerlIO *f, STDCHAR *ptr, SSize_t cnt);
# define PERL_ARGS_ASSERT_PERLIO_SET_PTRCNT

PERL_CALLCONV void
Perl_PerlIO_setlinebuf(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_SETLINEBUF

PERL_CALLCONV PerlIO *
Perl_PerlIO_stderr(pTHX)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_PERLIO_STDERR

PERL_CALLCONV PerlIO *
Perl_PerlIO_stdin(pTHX)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_PERLIO_STDIN

PERL_CALLCONV PerlIO *
Perl_PerlIO_stdout(pTHX)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_PERLIO_STDOUT

PERL_CALLCONV Off_t
Perl_PerlIO_tell(pTHX_ PerlIO *f);
# define PERL_ARGS_ASSERT_PERLIO_TELL

PERL_CALLCONV SSize_t
Perl_PerlIO_unread(pTHX_ PerlIO *f, const void *vbuf, Size_t count);
# define PERL_ARGS_ASSERT_PERLIO_UNREAD         \
        assert(vbuf)

PERL_CALLCONV SSize_t
Perl_PerlIO_write(pTHX_ PerlIO *f, const void *vbuf, Size_t count);
# define PERL_ARGS_ASSERT_PERLIO_WRITE          \
        assert(vbuf)

#endif /* defined(USE_PERLIO) */
#if defined(USE_PERL_SWITCH_LOCALE_CONTEXT)
PERL_CALLCONV void
Perl_switch_locale_context(void);
# define PERL_ARGS_ASSERT_SWITCH_LOCALE_CONTEXT

#endif
#if defined(USE_QUADMATH)
PERL_CALLCONV bool
Perl_quadmath_format_needed(const char *format)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_QUADMATH_FORMAT_NEEDED \
        assert(format)

PERL_CALLCONV bool
Perl_quadmath_format_valid(const char *format)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_QUADMATH_FORMAT_VALID \
        assert(format)

#endif /* defined(USE_QUADMATH) */
#if defined(VMS) || defined(WIN32)
PERL_CALLCONV int
Perl_do_aspawn(pTHX_ SV *really, SV **mark, SV **sp);
# define PERL_ARGS_ASSERT_DO_ASPAWN             \
        assert(mark); assert(sp)

PERL_CALLCONV int
Perl_do_spawn(pTHX_ char *cmd);
# define PERL_ARGS_ASSERT_DO_SPAWN              \
        assert(cmd)

PERL_CALLCONV int
Perl_do_spawn_nowait(pTHX_ char *cmd);
# define PERL_ARGS_ASSERT_DO_SPAWN_NOWAIT       \
        assert(cmd)

#endif /* defined(VMS) || defined(WIN32) */
#if defined(WIN32)
PERL_CALLCONV void *
Perl_get_context(void)
        __attribute__warn_unused_result__;
# define PERL_ARGS_ASSERT_GET_CONTEXT

PERL_CALLCONV bool
Perl_get_win32_message_utf8ness(pTHX_ const char *string)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_GET_WIN32_MESSAGE_UTF8NESS

PERL_CALLCONV_NO_RET void
win32_croak_not_implemented(const char *fname)
        __attribute__noreturn__;
# define PERL_ARGS_ASSERT_WIN32_CROAK_NOT_IMPLEMENTED \
        assert(fname)

#else /* if !defined(WIN32) */
PERL_CALLCONV bool
Perl_do_exec3(pTHX_ const char *incmd, int fd, int do_report)
        __attribute__visibility__("hidden");
# define PERL_ARGS_ASSERT_DO_EXEC3              \
        assert(incmd)

#endif

#ifdef PERL_CORE
#  include "pp_proto.h"
#endif
END_EXTERN_C

/* ex: set ro ft=c: */
