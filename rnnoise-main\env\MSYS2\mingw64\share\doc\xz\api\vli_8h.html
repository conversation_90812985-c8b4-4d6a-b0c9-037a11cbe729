<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/vli.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('vli_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">vli.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Variable-length integer handling.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a7b782528bd1934db7c020adbedb20ec9" id="r_a7b782528bd1934db7c020adbedb20ec9"><td class="memItemLeft" align="right" valign="top"><a id="a7b782528bd1934db7c020adbedb20ec9" name="a7b782528bd1934db7c020adbedb20ec9"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VLI_MAX</b>&#160;&#160;&#160;(UINT64_MAX / 2)</td></tr>
<tr class="memdesc:a7b782528bd1934db7c020adbedb20ec9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum supported value of a variable-length integer. <br /></td></tr>
<tr class="separator:a7b782528bd1934db7c020adbedb20ec9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a4b28254a30c859018b896ed371d69a" id="r_a5a4b28254a30c859018b896ed371d69a"><td class="memItemLeft" align="right" valign="top"><a id="a5a4b28254a30c859018b896ed371d69a" name="a5a4b28254a30c859018b896ed371d69a"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VLI_UNKNOWN</b>&#160;&#160;&#160;UINT64_MAX</td></tr>
<tr class="memdesc:a5a4b28254a30c859018b896ed371d69a"><td class="mdescLeft">&#160;</td><td class="mdescRight">VLI value to denote that the value is unknown. <br /></td></tr>
<tr class="separator:a5a4b28254a30c859018b896ed371d69a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a063ecff4133aa2f8899b9fa3fdefd310" id="r_a063ecff4133aa2f8899b9fa3fdefd310"><td class="memItemLeft" align="right" valign="top"><a id="a063ecff4133aa2f8899b9fa3fdefd310" name="a063ecff4133aa2f8899b9fa3fdefd310"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VLI_BYTES_MAX</b>&#160;&#160;&#160;9</td></tr>
<tr class="memdesc:a063ecff4133aa2f8899b9fa3fdefd310"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum supported encoded length of variable length integers. <br /></td></tr>
<tr class="separator:a063ecff4133aa2f8899b9fa3fdefd310"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d8bf5322898bfa11945848420585881" id="r_a2d8bf5322898bfa11945848420585881"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(n)</td></tr>
<tr class="memdesc:a2d8bf5322898bfa11945848420585881"><td class="mdescLeft">&#160;</td><td class="mdescRight">VLI constant suffix.  <br /></td></tr>
<tr class="separator:a2d8bf5322898bfa11945848420585881"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f67ed698215d865a2b87a95ab1320dd" id="r_a4f67ed698215d865a2b87a95ab1320dd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4f67ed698215d865a2b87a95ab1320dd">lzma_vli_is_valid</a>(vli)</td></tr>
<tr class="memdesc:a4f67ed698215d865a2b87a95ab1320dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Validate a variable-length integer.  <br /></td></tr>
<tr class="separator:a4f67ed698215d865a2b87a95ab1320dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a1dbc0ffc3e72748f64df8f7f71898272" id="r_a1dbc0ffc3e72748f64df8f7f71898272"><td class="memItemLeft" align="right" valign="top">typedef uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td></tr>
<tr class="memdesc:a1dbc0ffc3e72748f64df8f7f71898272"><td class="mdescLeft">&#160;</td><td class="mdescRight">Variable-length integer type.  <br /></td></tr>
<tr class="separator:a1dbc0ffc3e72748f64df8f7f71898272"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a50bbb77e9ec3b72c25586aa700c20970" id="r_a50bbb77e9ec3b72c25586aa700c20970"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a50bbb77e9ec3b72c25586aa700c20970">lzma_vli_encode</a> (<a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> vli, size_t *vli_pos, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow</td></tr>
<tr class="memdesc:a50bbb77e9ec3b72c25586aa700c20970"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encode a variable-length integer.  <br /></td></tr>
<tr class="separator:a50bbb77e9ec3b72c25586aa700c20970"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b7d50e1074e0e2bcd81c29a5f7461c7" id="r_a7b7d50e1074e0e2bcd81c29a5f7461c7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7b7d50e1074e0e2bcd81c29a5f7461c7">lzma_vli_decode</a> (<a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> *vli, size_t *vli_pos, const uint8_t *in, size_t *in_pos, size_t in_size) lzma_nothrow</td></tr>
<tr class="memdesc:a7b7d50e1074e0e2bcd81c29a5f7461c7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode a variable-length integer.  <br /></td></tr>
<tr class="separator:a7b7d50e1074e0e2bcd81c29a5f7461c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d53e0b69934b43da8721fa6f1e8cc4f" id="r_a8d53e0b69934b43da8721fa6f1e8cc4f"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8d53e0b69934b43da8721fa6f1e8cc4f">lzma_vli_size</a> (<a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> vli) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a8d53e0b69934b43da8721fa6f1e8cc4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the number of bytes required to encode a VLI.  <br /></td></tr>
<tr class="separator:a8d53e0b69934b43da8721fa6f1e8cc4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Variable-length integer handling. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead.</dd></dl>
<p>In the .xz format, most integers are encoded in a variable-length representation, which is sometimes called little endian base-128 encoding. This saves space when smaller values are more likely than bigger values.</p>
<p>The encoding scheme encodes seven bits to every byte, using minimum number of bytes required to represent the given value. Encodings that use non-minimum number of bytes are invalid, thus every integer has exactly one encoded representation. The maximum number of bits in a VLI is 63, thus the vli argument must be less than or equal to UINT64_MAX / 2. You should use LZMA_VLI_MAX for clarity. </p>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="a2d8bf5322898bfa11945848420585881" name="a2d8bf5322898bfa11945848420585881"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d8bf5322898bfa11945848420585881">&#9670;&#160;</a></span>LZMA_VLI_C</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_VLI_C</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>n</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">UINT64_C(n)</div>
</div><!-- fragment -->
<p>VLI constant suffix. </p>

</div>
</div>
<a id="a4f67ed698215d865a2b87a95ab1320dd" name="a4f67ed698215d865a2b87a95ab1320dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f67ed698215d865a2b87a95ab1320dd">&#9670;&#160;</a></span>lzma_vli_is_valid</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define lzma_vli_is_valid</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>vli</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    ((vli) &lt;= <a class="code hl_define" href="#a7b782528bd1934db7c020adbedb20ec9" title="Maximum supported value of a variable-length integer.">LZMA_VLI_MAX</a> || (vli) == <a class="code hl_define" href="#a5a4b28254a30c859018b896ed371d69a" title="VLI value to denote that the value is unknown.">LZMA_VLI_UNKNOWN</a>)</div>
</div><!-- fragment -->
<p>Validate a variable-length integer. </p>
<p>This is useful to test that application has given acceptable values for example in the uncompressed_size and compressed_size variables.</p>
<dl class="section return"><dt>Returns</dt><dd>True if the integer is representable as a VLI or if it indicates an unknown value. False otherwise. </dd></dl>

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="a1dbc0ffc3e72748f64df8f7f71898272" name="a1dbc0ffc3e72748f64df8f7f71898272"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1dbc0ffc3e72748f64df8f7f71898272">&#9670;&#160;</a></span>lzma_vli</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint64_t <a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Variable-length integer type. </p>
<p>Valid VLI values are in the range [0, LZMA_VLI_MAX]. Unknown value is indicated with LZMA_VLI_UNKNOWN, which is the maximum value of the underlying integer type.</p>
<p><a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272" title="Variable-length integer type.">lzma_vli</a> will be uint64_t for the foreseeable future. If a bigger size is needed in the future, it is guaranteed that 2 * LZMA_VLI_MAX will not overflow <a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272" title="Variable-length integer type.">lzma_vli</a>. This simplifies integer overflow detection. </p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a50bbb77e9ec3b72c25586aa700c20970" name="a50bbb77e9ec3b72c25586aa700c20970"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a50bbb77e9ec3b72c25586aa700c20970">&#9670;&#160;</a></span>lzma_vli_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_vli_encode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td>          <td class="paramname"><span class="paramname"><em>vli</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>vli_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Encode a variable-length integer. </p>
<p>This function has two modes: single-call and multi-call. Single-call mode encodes the whole integer at once; it is an error if the output buffer is too small. Multi-call mode saves the position in *vli_pos, and thus it is possible to continue encoding if the buffer becomes full before the whole integer has been encoded.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">vli</td><td>Integer to be encoded </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">vli_pos</td><td>How many VLI-encoded bytes have already been written out. When starting to encode a new integer in multi-call mode, *vli_pos must be set to zero. To use single-call encoding, set vli_pos to NULL. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Slightly different return values are used in multi-call and single-call modes.</dd></dl>
<p>Single-call (vli_pos == NULL):</p><ul>
<li>LZMA_OK: Integer successfully encoded.</li>
<li>LZMA_PROG_ERROR: Arguments are not sane. This can be due to too little output space; single-call mode doesn't use LZMA_BUF_ERROR, since the application should have checked the encoded size with <a class="el" href="#a8d53e0b69934b43da8721fa6f1e8cc4f" title="Get the number of bytes required to encode a VLI.">lzma_vli_size()</a>.</li>
</ul>
<p>Multi-call (vli_pos != NULL):</p><ul>
<li>LZMA_OK: So far all OK, but the integer is not completely written out yet.</li>
<li>LZMA_STREAM_END: Integer successfully encoded.</li>
<li>LZMA_BUF_ERROR: No output space was provided.</li>
<li>LZMA_PROG_ERROR: Arguments are not sane. </li>
</ul>

</div>
</div>
<a id="a7b7d50e1074e0e2bcd81c29a5f7461c7" name="a7b7d50e1074e0e2bcd81c29a5f7461c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7b7d50e1074e0e2bcd81c29a5f7461c7">&#9670;&#160;</a></span>lzma_vli_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_vli_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> *</td>          <td class="paramname"><span class="paramname"><em>vli</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>vli_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>in_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Decode a variable-length integer. </p>
<p>Like <a class="el" href="#a50bbb77e9ec3b72c25586aa700c20970" title="Encode a variable-length integer.">lzma_vli_encode()</a>, this function has single-call and multi-call modes.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">vli</td><td>Pointer to decoded integer. The decoder will initialize it to zero when *vli_pos == 0, so application isn't required to initialize *vli. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">vli_pos</td><td>How many bytes have already been decoded. When starting to decode a new integer in multi-call mode, *vli_pos must be initialized to zero. To use single-call decoding, set vli_pos to NULL. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">in_pos</td><td>The next byte will be read from in[*in_pos]. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer; the first byte that won't be read is in[in_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Slightly different return values are used in multi-call and single-call modes.</dd></dl>
<p>Single-call (vli_pos == NULL):</p><ul>
<li>LZMA_OK: Integer successfully decoded.</li>
<li>LZMA_DATA_ERROR: Integer is corrupt. This includes hitting the end of the input buffer before the whole integer was decoded; providing no input at all will use LZMA_DATA_ERROR.</li>
<li>LZMA_PROG_ERROR: Arguments are not sane.</li>
</ul>
<p>Multi-call (vli_pos != NULL):</p><ul>
<li>LZMA_OK: So far all OK, but the integer is not completely decoded yet.</li>
<li>LZMA_STREAM_END: Integer successfully decoded.</li>
<li>LZMA_DATA_ERROR: Integer is corrupt.</li>
<li>LZMA_BUF_ERROR: No input was provided.</li>
<li>LZMA_PROG_ERROR: Arguments are not sane. </li>
</ul>

</div>
</div>
<a id="a8d53e0b69934b43da8721fa6f1e8cc4f" name="a8d53e0b69934b43da8721fa6f1e8cc4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8d53e0b69934b43da8721fa6f1e8cc4f">&#9670;&#160;</a></span>lzma_vli_size()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_vli_size </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td>          <td class="paramname"><span class="paramname"><em>vli</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the number of bytes required to encode a VLI. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">vli</td><td>Integer whose encoded size is to be determined</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of bytes on success (1-9). If vli isn't valid, zero is returned. </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li><li class="navelem"><a class="el" href="vli_8h.html">vli.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
