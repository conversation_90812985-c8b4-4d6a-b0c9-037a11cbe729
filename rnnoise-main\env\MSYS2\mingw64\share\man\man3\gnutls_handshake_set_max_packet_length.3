.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake_set_max_packet_length" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake_set_max_packet_length \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_handshake_set_max_packet_length(gnutls_session_t " session ", size_t " max ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "size_t max" 12
is the maximum number.
.SH "DESCRIPTION"
This function will set the maximum size of all handshake messages.
Handshakes over this size are rejected with
\fBGNUTLS_E_HANDSHAKE_TOO_LARGE\fP error code.  The default value is
128kb which is typically large enough.  Set this to 0 if you do not
want to set an upper limit.

The reason for restricting the handshake message sizes are to
limit Denial of Service attacks.

Note that the maximum handshake size was increased to 128kb
from 48kb in GnuTLS 3.5.5.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
