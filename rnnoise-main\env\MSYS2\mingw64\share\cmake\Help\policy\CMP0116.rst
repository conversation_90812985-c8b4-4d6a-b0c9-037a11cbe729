CMP0116
-------

.. versionadded:: 3.20

Ninja generators transform ``DEP<PERSON><PERSON>`` s from :command:`add_custom_command`.

In CMake 3.19 and below, files given to the ``DEPFILE`` argument of
:command:`add_custom_command` were passed directly to <PERSON>'s ``depfile``
variable without any path resolution. This meant that if
:command:`add_custom_command` was called from a subdirectory (created by
:command:`add_subdirectory`), the ``DEPFILE`` argument would have to be either
an absolute path or a path relative to :variable:`CMAKE_BINARY_DIR`, rather
than :variable:`CMAKE_CURRENT_BINARY_DIR`. In addition, no transformation was
done on the file listed in ``DEPFILE``, which meant that the paths within the
``DEPFILE`` had the same restrictions.

Starting with CMake 3.20, the ``DEPFILE`` argument is relative to
:variable:`CMAKE_CURRENT_BINARY_DIR` (unless it is absolute), and the paths in
the ``DEPFILE`` are also relative to :variable:`CMAKE_CURRENT_BINARY_DIR`.
<PERSON><PERSON><PERSON> automatically transforms the paths in the ``DEPFILE`` (unless they are
absolute) after the custom command is run. The file listed in ``DEP<PERSON>LE`` is
not modified in any way. Instead, C<PERSON>ake writes the transformation to its own
internal file, and passes this internal file to Ninja's ``depfile`` variable.
This transformation happens regardless of whether or not ``DEPFILE`` is
relative, and regardless of whether or not :command:`add_custom_command` is
called from a subdirectory.

The ``OLD`` behavior for this policy is to pass the ``DEPFILE`` to Ninja
unaltered. The ``NEW`` behavior for this policy is to transform the ``DEPFILE``
after running the custom command. The status of ``CMP0116`` is recorded at the
time of the custom command's creation, and you can have custom commands in the
same directory with different values for ``CMP0116`` by setting the policy
before each custom command.

.. |INTRODUCED_IN_CMAKE_VERSION| replace:: 3.20
.. |WARNS_OR_DOES_NOT_WARN| replace::
   does *not* warn by default (unless ``DEPFILE`` is used in a subdirectory)
.. include:: STANDARD_ADVICE.txt

See documentation of the
:variable:`CMAKE_POLICY_WARNING_CMP0116 <CMAKE_POLICY_WARNING_CMP<NNNN>>`
variable to control the warning.

.. include:: DEPRECATED.txt
