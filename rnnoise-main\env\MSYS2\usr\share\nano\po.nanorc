## Syntax highlighting for PO files.

## Original author:  <PERSON><PERSON>
## License:  GPL version 3 or newer

syntax po "\.pot?$"
comment "#"

# Comments.
color green "^#.*"
color yellow "Copyright|\(C\)"
# Header fields.
color brightred "^"X-Bugs:.*"$"
color brightmagenta "\<(Project-Id-Version|Report-Msgid-Bugs-To|Last-Translator|Language(-Team)?|X-Bugs|X-Generator|Plural-Forms)\>"
color cyan "\<(POT-Creation-Date|PO-Revision-Date|MIME-Version|Content-Type|Content-Transfer-Encoding)\>"
# Encodings and numbers.
color yellow "\<(UTF|ISO|Windows|Mac|IBM)-[0-9]+"
color yellow "\<((pre|rc)?[0-9]+|[0-9]bit)\>"
# Main keywords.
color brightblue "^(msgid|msgid_plural|msgstr)\>"
# Flags.
color red " fuzzy(,|$)"
color yellow " (no-)?([[:lower:]-]+|c\+\+)-format(,|$)"
# Format specifiers.
color brightmagenta "%([1-9]\$)?[#0 +'I-]?(\*([1-9]\$)?|[0-9]\.?|[0-9]?\.[0-9])?(hh|ll|[hlLjzt])?([diouxXeEfFgGaAcspnmr]|<PRI[diouxX](32|64)>)|%%"
color rosy "%\([[:alpha:]][[:alnum:]_]*\)([0-9]\.?|[0-9]?\.[0-9])?[#0 +-]?[diouxXeEfFgGcrs]"
# Quotes and escaped characters.
color yellow """
color cyan "\\([abcefnrtv"\]|x[[:xdigit:]]{2}|[0-7]{3})"
# Context.
color slate "^msgctxt.*"
# Reminders.
color brightwhite,yellow "\<(FIXME|TODO|XXX)\>"
# Obsolete strings.
color red "#~.*"
# Stray control codes.
color bold,pink,red "[[:cntrl:]]"
# Tabs.
color mint "[[:blank:]]"
