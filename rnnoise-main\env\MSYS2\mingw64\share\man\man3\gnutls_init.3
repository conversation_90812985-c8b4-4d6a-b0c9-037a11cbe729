.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_init \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_init(gnutls_session_t * " session ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t * session" 12
is a pointer to a \fBgnutls_session_t\fP type.
.IP "unsigned int flags" 12
indicate if this session is to be used for server or client.
.SH "DESCRIPTION"
This function initializes the provided session. Every session must
be initialized before use, and after successful initialization and
use must be deinitialized by calling \fBgnutls_deinit()\fP.

 \fIflags\fP can be any combination of flags from \fBgnutls_init_flags_t\fP.

Note that since version 3.1.2 this function enables some common
TLS extensions such as session tickets and OCSP certificate status
request in client side by default. To prevent that use the \fBGNUTLS_NO_DEFAULT_EXTENSIONS\fP
flag.

Note that it is never mandatory to use \fBgnutls_deinit()\fP after this
function fails.  Since gnutls 3.8.0, it is safe to unconditionally
use \fBgnutls_deinit()\fP even after failure regardless of whether the
memory was initialized prior to \fBgnutls_init()\fP; however, clients
wanting to be portable to older versions of the library should
either skip deinitialization on failure, or pre\-initialize the
memory passed in to \fBgnutls_init()\fP to all zeroes via \fBmemset()\fP or
similar.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
