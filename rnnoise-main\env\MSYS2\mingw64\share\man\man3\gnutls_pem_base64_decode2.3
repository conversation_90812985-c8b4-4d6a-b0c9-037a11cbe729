.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pem_base64_decode2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pem_base64_decode2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_pem_base64_decode2(const char * " header ", const gnutls_datum_t * " b64_data ", gnutls_datum_t * " result ");"
.SH ARGUMENTS
.IP "const char * header" 12
The PEM header (eg. CERTIFICATE)
.IP "const gnutls_datum_t * b64_data" 12
contains the encoded data
.IP "gnutls_datum_t * result" 12
the location of decoded data
.SH "DESCRIPTION"
This function will decode the given encoded data. The decoded data
will be allocated, and stored into result.  If the header given is
non null this function will search for "\-\-\-\-\-BEGIN header" and
decode only this part. Otherwise it will decode the first PEM
packet found.

You should use \fBgnutls_free()\fP to free the returned data.

Note, that prior to GnuTLS 3.4.0 this function was available
under the name \fBgnutls_pem_base64_decode_alloc()\fP. There is
compatibility macro pointing to this function.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
