.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hash_copy" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hash_copy \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "gnutls_hash_hd_t gnutls_hash_copy(gnutls_hash_hd_t " handle ");"
.SH ARGUMENTS
.IP "gnutls_hash_hd_t handle" 12
is a \fBgnutls_hash_hd_t\fP type
.SH "DESCRIPTION"
This function will create a copy of Message Digest context, containing all
its current state. Copying contexts for Message Digests registered using
\fBgnutls_crypto_register_digest()\fP is not supported and will always result in
an error. In addition to that, some of the Message Digest implementations do
not support this operation. Applications should check the return value and
provide a proper fallback.
.SH "RETURNS"
new Message Digest context or NULL in case of an error.
.SH "SINCE"
3.6.9
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
