<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>URIs: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="reference.html" title="API Reference">
<link rel="prev" href="p11-kit-Modules.html" title="Modules">
<link rel="next" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts">
<a href="#" class="shortcut">Top</a><span id="nav_description">  <span class="dim">|</span> 
                  <a href="#p11-kit-URIs.description" class="shortcut">Description</a></span>
</td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="reference.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="p11-kit-Modules.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="p11-kit-PIN-Callbacks.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="refentry">
<a name="p11-kit-URIs"></a><div class="titlepage"></div>
<div class="refnamediv"><table width="100%"><tr>
<td valign="top">
<h2><span class="refentrytitle"><a name="p11-kit-URIs.top_of_page"></a>URIs</span></h2>
<p>URIs — Parsing and formatting PKCS#11 URIs</p>
</td>
<td class="gallery_image" valign="top" align="right"></td>
</tr></table></div>
<div class="refsect1">
<a name="p11-kit-URIs.functions"></a><h2>Functions</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="functions_proto_type">
<col class="functions_proto_name">
</colgroup>
<tbody>
<tr>
<td class="function_type">
<a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="returnvalue">P11KitUri</span></a> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-new" title="p11_kit_uri_new ()">p11_kit_uri_new</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_INFO_PTR</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-module-info" title="p11_kit_uri_get_module_info ()">p11_kit_uri_get_module_info</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-module-info" title="p11_kit_uri_match_module_info ()">p11_kit_uri_match_module_info</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_SLOT_INFO_PTR</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-slot-info" title="p11_kit_uri_get_slot_info ()">p11_kit_uri_get_slot_info</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-slot-info" title="p11_kit_uri_match_slot_info ()">p11_kit_uri_match_slot_info</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_SLOT_ID</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-slot-id" title="p11_kit_uri_get_slot_id ()">p11_kit_uri_get_slot_id</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-slot-id" title="p11_kit_uri_set_slot_id ()">p11_kit_uri_set_slot_id</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_TOKEN_INFO_PTR</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-token-info" title="p11_kit_uri_get_token_info ()">p11_kit_uri_get_token_info</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-token-info" title="p11_kit_uri_match_token_info ()">p11_kit_uri_match_token_info</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_ATTRIBUTE_PTR</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-attributes" title="p11_kit_uri_get_attributes ()">p11_kit_uri_get_attributes</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-attributes" title="p11_kit_uri_set_attributes ()">p11_kit_uri_set_attributes</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-clear-attributes" title="p11_kit_uri_clear_attributes ()">p11_kit_uri_clear_attributes</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-attributes" title="p11_kit_uri_match_attributes ()">p11_kit_uri_match_attributes</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_ATTRIBUTE_PTR</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-attribute" title="p11_kit_uri_get_attribute ()">p11_kit_uri_get_attribute</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-attribute" title="p11_kit_uri_set_attribute ()">p11_kit_uri_set_attribute</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-clear-attribute" title="p11_kit_uri_clear_attribute ()">p11_kit_uri_clear_attribute</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-unrecognized" title="p11_kit_uri_set_unrecognized ()">p11_kit_uri_set_unrecognized</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-any-unrecognized" title="p11_kit_uri_any_unrecognized ()">p11_kit_uri_any_unrecognized</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">const <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-pin-value" title="p11_kit_uri_get_pin_value ()">p11_kit_uri_get_pin_value</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-pin-value" title="p11_kit_uri_set_pin_value ()">p11_kit_uri_set_pin_value</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">const <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-pin-source" title="p11_kit_uri_get_pin_source ()">p11_kit_uri_get_pin_source</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-pin-source" title="p11_kit_uri_set_pin_source ()">p11_kit_uri_set_pin_source</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">const <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-pinfile" title="p11_kit_uri_get_pinfile ()">p11_kit_uri_get_pinfile</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-pinfile" title="p11_kit_uri_set_pinfile ()">p11_kit_uri_set_pinfile</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">const <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-module-name" title="p11_kit_uri_get_module_name ()">p11_kit_uri_get_module_name</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-module-name" title="p11_kit_uri_set_module_name ()">p11_kit_uri_set_module_name</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">const <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-module-path" title="p11_kit_uri_get_module_path ()">p11_kit_uri_get_module_path</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-module-path" title="p11_kit_uri_set_module_path ()">p11_kit_uri_set_module_path</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">const <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-vendor-query" title="p11_kit_uri_get_vendor_query ()">p11_kit_uri_get_vendor_query</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-vendor-query" title="p11_kit_uri_set_vendor_query ()">p11_kit_uri_set_vendor_query</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-format" title="p11_kit_uri_format ()">p11_kit_uri_format</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-parse" title="p11_kit_uri_parse ()">p11_kit_uri_parse</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-free" title="p11_kit_uri_free ()">p11_kit_uri_free</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">const <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-message" title="p11_kit_uri_message ()">p11_kit_uri_message</a> <span class="c_punctuation">()</span>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-URIs.other"></a><h2>Types and Values</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="other_proto_type">
<col class="other_proto_name">
</colgroup>
<tbody>
<tr>
<td class="define_keyword">#define</td>
<td class="function_name"><a class="link" href="p11-kit-URIs.html#P11-KIT-URI-SCHEME:CAPS" title="P11_KIT_URI_SCHEME">P11_KIT_URI_SCHEME</a></td>
</tr>
<tr>
<td class="define_keyword">#define</td>
<td class="function_name"><a class="link" href="p11-kit-URIs.html#P11-KIT-URI-SCHEME-LEN:CAPS" title="P11_KIT_URI_SCHEME_LEN">P11_KIT_URI_SCHEME_LEN</a></td>
</tr>
<tr>
<td class="datatype_keyword">enum</td>
<td class="function_name"><a class="link" href="p11-kit-URIs.html#P11KitUriType" title="enum P11KitUriType">P11KitUriType</a></td>
</tr>
<tr>
<td class="datatype_keyword">enum</td>
<td class="function_name"><a class="link" href="p11-kit-URIs.html#P11KitUriResult" title="enum P11KitUriResult">P11KitUriResult</a></td>
</tr>
<tr>
<td class="typedef_keyword">typedef</td>
<td class="function_name"><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri">P11KitUri</a></td>
</tr>
<tr>
<td class="typedef_keyword">typedef</td>
<td class="function_name"><a class="link" href="p11-kit-URIs.html#p11-kit-uri" title="p11_kit_uri">p11_kit_uri</a></td>
</tr>
<tr>
<td class="define_keyword">#define</td>
<td class="function_name"><a class="link" href="p11-kit-URIs.html#P11-KIT-URI-NO-MEMORY:CAPS" title="P11_KIT_URI_NO_MEMORY">P11_KIT_URI_NO_MEMORY</a></td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-URIs.description"></a><h2>Description</h2>
<p>PKCS#11 URIs can be used in configuration files or applications to represent
PKCS#11 modules, tokens or objects. An example of a URI might be:</p>
<code class="code"><div class="literallayout"><p><br>
     pkcs11:token=The\%20Software\%20PKCS#11\%20softtoken;<br>
         manufacturer=Snake\%20Oil,\%20Inc.;serial=;object=my-certificate;<br>
         model=1.0;type=cert;id=\%69\%95\%3e\%5c\%f4\%bd\%ec\%91<br>
</p></div></code><p>You can use <a class="link" href="p11-kit-URIs.html#p11-kit-uri-parse" title="p11_kit_uri_parse ()"><code class="function">p11_kit_uri_parse()</code></a> to parse such a URI, and <a class="link" href="p11-kit-URIs.html#p11-kit-uri-format" title="p11_kit_uri_format ()"><code class="function">p11_kit_uri_format()</code></a>
to build one. URIs are represented by the <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> structure. You can match
a parsed URI against PKCS#11 tokens with <a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-token-info" title="p11_kit_uri_match_token_info ()"><code class="function">p11_kit_uri_match_token_info()</code></a>
or attributes with <a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-attributes" title="p11_kit_uri_match_attributes ()"><code class="function">p11_kit_uri_match_attributes()</code></a>.</p>
<p>Since URIs can represent different sorts of things, when parsing or formatting
a URI a 'context' can be used to indicate which sort of URI is expected.</p>
<p>URIs have an <code class="code">unrecognized</code> flag. This flag is set during parsing
if any parts of the URI are not recognized. This may be because the part is
from a newer version of the PKCS#11 spec or because that part was not valid
inside of the desired context used when parsing.</p>
</div>
<div class="refsect1">
<a name="p11-kit-URIs.functions_details"></a><h2>Functions</h2>
<div class="refsect2">
<a name="p11-kit-uri-new"></a><h3>p11_kit_uri_new ()</h3>
<pre class="programlisting"><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="returnvalue">P11KitUri</span></a> *
p11_kit_uri_new (<em class="parameter"><code><span class="type">void</span></code></em>);</pre>
<p>Create a new blank PKCS#11 URI.</p>
<p>The new URI is in the right state to parse a string into. All relevant fields
are zeroed out. Formatting this URI will produce a valid but empty URI.</p>
<div class="refsect3">
<a name="p11-kit-uri-new.returns"></a><h4>Returns</h4>
<p> A newly allocated URI. This should be freed with <a class="link" href="p11-kit-URIs.html#p11-kit-uri-free" title="p11_kit_uri_free ()"><code class="function">p11_kit_uri_free()</code></a>.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-module-info"></a><h3>p11_kit_uri_get_module_info ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_INFO_PTR</span>
p11_kit_uri_get_module_info (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Get the <code class="code">CK_INFO</code> structure associated with this URI.</p>
<p>If this is a parsed URI, then the fields corresponding to library parts of
the URI will be filled in. Any library URI parts that were missing will have
their fields filled with zeros.</p>
<p>If the caller wishes to setup information for building a URI, then relevant
fields should be filled in. Fields that should not appear as parts in the
resulting URI should be filled with zeros.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-module-info.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>the URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-module-info.returns"></a><h4>Returns</h4>
<p> A pointer to the <code class="code">CK_INFO</code> structure.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-match-module-info"></a><h3>p11_kit_uri_match_module_info ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_match_module_info (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                               <em class="parameter"><code>const <span class="type">CK_INFO</span> *info</code></em>);</pre>
<p>Match a <code class="code">CK_INFO</code> structure against the library parts of this URI.</p>
<p>Only the fields of the <code class="code">CK_INFO</code> structure that are valid for use
in a URI will be matched. A URI part that was not specified in the URI will
match any value in the structure. If during the URI parsing any unrecognized
parts were encountered then this match will fail.</p>
<div class="refsect3">
<a name="p11-kit-uri-match-module-info.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>the URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>info</p></td>
<td class="parameter_description"><p>the structure to match against the URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-match-module-info.returns"></a><h4>Returns</h4>
<p> 1 if the URI matches, 0 if not.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-slot-info"></a><h3>p11_kit_uri_get_slot_info ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_SLOT_INFO_PTR</span>
p11_kit_uri_get_slot_info (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Get the <code class="code">CK_SLOT_INFO</code> structure associated with this URI.</p>
<p>If this is a parsed URI, then the fields corresponding to slot parts of
the URI will be filled in. Any slot URI parts that were missing will have
their fields filled with zeros.</p>
<p>If the caller wishes to setup information for building a URI, then relevant
fields should be filled in. Fields that should not appear as parts in the
resulting URI should be filled with zeros.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-slot-info.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>the URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-slot-info.returns"></a><h4>Returns</h4>
<p> A pointer to the <code class="code">CK_INFO</code> structure.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-match-slot-info"></a><h3>p11_kit_uri_match_slot_info ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_match_slot_info (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                             <em class="parameter"><code>const <span class="type">CK_SLOT_INFO</span> *slot_info</code></em>);</pre>
<p>Match a <code class="code">CK_SLOT_INFO</code> structure against the slot parts of this
URI.</p>
<p>Only the fields of the <code class="code">CK_SLOT_INFO</code> structure that are valid
for use in a URI will be matched. A URI part that was not specified in the
URI will match any value in the structure. If during the URI parsing any
unrecognized parts were encountered then this match will fail.</p>
<div class="refsect3">
<a name="p11-kit-uri-match-slot-info.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>the URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>slot_info</p></td>
<td class="parameter_description"><p>the structure to match against the URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-match-slot-info.returns"></a><h4>Returns</h4>
<p> 1 if the URI matches, 0 if not.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-slot-id"></a><h3>p11_kit_uri_get_slot_id ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_SLOT_ID</span>
p11_kit_uri_get_slot_id (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Get the 'slot-id' part of the URI.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-slot-id.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-slot-id.returns"></a><h4>Returns</h4>
<p> The slot-id or <code class="code">(CK_SLOT_ID)-1</code> if not set.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-slot-id"></a><h3>p11_kit_uri_set_slot_id ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_uri_set_slot_id (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                         <em class="parameter"><code><span class="type">CK_SLOT_ID</span> slot_id</code></em>);</pre>
<p>Set the 'slot-id' part of the URI.</p>
<div class="refsect3">
<a name="p11-kit-uri-set-slot-id.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>slot_id</p></td>
<td class="parameter_description"><p>The new slot-id</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-token-info"></a><h3>p11_kit_uri_get_token_info ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_TOKEN_INFO_PTR</span>
p11_kit_uri_get_token_info (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Get the <code class="code">CK_TOKEN_INFO</code> structure associated with this URI.</p>
<p>If this is a parsed URI, then the fields corresponding to token parts of
the URI will be filled in. Any token URI parts that were missing will have
their fields filled with zeros.</p>
<p>If the caller wishes to setup information for building a URI, then relevant
fields should be filled in. Fields that should not appear as parts in the
resulting URI should be filled with zeros.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-token-info.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>the URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-token-info.returns"></a><h4>Returns</h4>
<p> A pointer to the <code class="code">CK_INFO</code> structure.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-match-token-info"></a><h3>p11_kit_uri_match_token_info ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_match_token_info (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                              <em class="parameter"><code>const <span class="type">CK_TOKEN_INFO</span> *token_info</code></em>);</pre>
<p>Match a <code class="code">CK_TOKEN_INFO</code> structure against the token parts of this
URI.</p>
<p>Only the fields of the <code class="code">CK_TOKEN_INFO</code> structure that are valid
for use in a URI will be matched. A URI part that was not specified in the
URI will match any value in the structure. If during the URI parsing any
unrecognized parts were encountered then this match will fail.</p>
<div class="refsect3">
<a name="p11-kit-uri-match-token-info.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>the URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>token_info</p></td>
<td class="parameter_description"><p>the structure to match against the URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-match-token-info.returns"></a><h4>Returns</h4>
<p> 1 if the URI matches, 0 if not.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-attributes"></a><h3>p11_kit_uri_get_attributes ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_ATTRIBUTE_PTR</span>
p11_kit_uri_get_attributes (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                            <em class="parameter"><code><span class="type">CK_ULONG</span> *n_attrs</code></em>);</pre>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-attributes"></a><h3>p11_kit_uri_set_attributes ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_set_attributes (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                            <em class="parameter"><code><span class="type">CK_ATTRIBUTE_PTR</span> attrs</code></em>,
                            <em class="parameter"><code><span class="type">CK_ULONG</span> n_attrs</code></em>);</pre>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-clear-attributes"></a><h3>p11_kit_uri_clear_attributes ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_uri_clear_attributes (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-match-attributes"></a><h3>p11_kit_uri_match_attributes ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_match_attributes (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                              <em class="parameter"><code>const <span class="type">CK_ATTRIBUTE</span> *attrs</code></em>,
                              <em class="parameter"><code><span class="type">CK_ULONG</span> n_attrs</code></em>);</pre>
<p>Match a attributes against the object parts of this URI.</p>
<p>Only the attributes that are valid for use in a URI will be matched. A URI
part that was not specified in the URI will match any attribute value. If
during the URI parsing any unrecognized parts were encountered then this
match will fail.</p>
<div class="refsect3">
<a name="p11-kit-uri-match-attributes.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>attrs</p></td>
<td class="parameter_description"><p>The attributes to match</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>n_attrs</p></td>
<td class="parameter_description"><p>The number of attributes</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-match-attributes.returns"></a><h4>Returns</h4>
<p> 1 if the URI matches, 0 if not.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-attribute"></a><h3>p11_kit_uri_get_attribute ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_ATTRIBUTE_PTR</span>
p11_kit_uri_get_attribute (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                           <em class="parameter"><code><span class="type">CK_ATTRIBUTE_TYPE</span> attr_type</code></em>);</pre>
<p>Get a pointer to an attribute present in this URI.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-attribute.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>attr_type</p></td>
<td class="parameter_description"><p>The attribute type</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-attribute.returns"></a><h4>Returns</h4>
<p> A pointer to the attribute, or <code class="code">NULL</code> if not present.
The attribute is owned by the URI and should not be freed.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-attribute"></a><h3>p11_kit_uri_set_attribute ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_set_attribute (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                           <em class="parameter"><code><span class="type">CK_ATTRIBUTE_PTR</span> attr</code></em>);</pre>
<p>Set an attribute on the URI.</p>
<p>Only attributes that map to parts in a PKCS#11 URI will be accepted.</p>
<div class="refsect3">
<a name="p11-kit-uri-set-attribute.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>attr</p></td>
<td class="parameter_description"><p>The attribute to set</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-set-attribute.returns"></a><h4>Returns</h4>
<p> <a class="link" href="p11-kit-URIs.html#P11-KIT-URI-OK:CAPS"><code class="literal">P11_KIT_URI_OK</code></a> if the attribute was successfully set.
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-NOT-FOUND:CAPS"><code class="literal">P11_KIT_URI_NOT_FOUND</code></a> if the attribute was not valid for a URI.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-clear-attribute"></a><h3>p11_kit_uri_clear_attribute ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_clear_attribute (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                             <em class="parameter"><code><span class="type">CK_ATTRIBUTE_TYPE</span> attr_type</code></em>);</pre>
<p>Clear an attribute on the URI.</p>
<p>Only attributes that map to parts in a PKCS#11 URI will be accepted.</p>
<div class="refsect3">
<a name="p11-kit-uri-clear-attribute.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>attr_type</p></td>
<td class="parameter_description"><p>The type of the attribute to clear</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-clear-attribute.returns"></a><h4>Returns</h4>
<p> <a class="link" href="p11-kit-URIs.html#P11-KIT-URI-OK:CAPS"><code class="literal">P11_KIT_URI_OK</code></a> if the attribute was successfully cleared.
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-NOT-FOUND:CAPS"><code class="literal">P11_KIT_URI_NOT_FOUND</code></a> if the attribute was not valid for a URI.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-unrecognized"></a><h3>p11_kit_uri_set_unrecognized ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_uri_set_unrecognized (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                              <em class="parameter"><code><span class="type">int</span> unrecognized</code></em>);</pre>
<p>Set the unrecognized flag on this URI.</p>
<p>The unrecognized flag is automatically set to 1 when during parsing any part
of the URI is unrecognized. If the unrecognized flag is set to 1, then
matching against this URI will always fail.</p>
<div class="refsect3">
<a name="p11-kit-uri-set-unrecognized.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>unrecognized</p></td>
<td class="parameter_description"><p>The new unregognized flag value</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-any-unrecognized"></a><h3>p11_kit_uri_any_unrecognized ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_any_unrecognized (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Get the unrecognized flag for this URI.</p>
<p>The unrecognized flag is automatically set to 1 when during parsing any part
of the URI is unrecognized. If the unrecognized flag is set to 1, then
matching against this URI will always fail.</p>
<div class="refsect3">
<a name="p11-kit-uri-any-unrecognized.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-any-unrecognized.returns"></a><h4>Returns</h4>
<p> 1 if unrecognized flag is set, 0 otherwise.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-pin-value"></a><h3>p11_kit_uri_get_pin_value ()</h3>
<pre class="programlisting">const <span class="returnvalue">char</span> *
p11_kit_uri_get_pin_value (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Get the 'pin-value' part of the URI. This is used by some applications to
read the PIN for logging into a PKCS#11 token.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-pin-value.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-pin-value.returns"></a><h4>Returns</h4>
<p> The pin-value or <code class="literal">NULL</code> if not present.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-pin-value"></a><h3>p11_kit_uri_set_pin_value ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_uri_set_pin_value (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                           <em class="parameter"><code>const <span class="type">char</span> *pin</code></em>);</pre>
<p>Set the 'pin-value' part of the URI. This is used by some applications to
specify the PIN for logging into a PKCS#11 token.</p>
<div class="refsect3">
<a name="p11-kit-uri-set-pin-value.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin</p></td>
<td class="parameter_description"><p>The new pin-value</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-pin-source"></a><h3>p11_kit_uri_get_pin_source ()</h3>
<pre class="programlisting">const <span class="returnvalue">char</span> *
p11_kit_uri_get_pin_source (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Get the 'pin-source' part of the URI. This is used by some applications to
lookup a PIN for logging into a PKCS#11 token.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-pin-source.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-pin-source.returns"></a><h4>Returns</h4>
<p> The pin-source or <code class="literal">NULL</code> if not present.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-pin-source"></a><h3>p11_kit_uri_set_pin_source ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_uri_set_pin_source (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                            <em class="parameter"><code>const <span class="type">char</span> *pin_source</code></em>);</pre>
<p>Set the 'pin-source' part of the URI. This is used by some applications to
lookup a PIN for logging into a PKCS#11 token.</p>
<div class="refsect3">
<a name="p11-kit-uri-set-pin-source.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pin_source</p></td>
<td class="parameter_description"><p>The new pin-source</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-pinfile"></a><h3>p11_kit_uri_get_pinfile ()</h3>
<pre class="programlisting">const <span class="returnvalue">char</span> *
p11_kit_uri_get_pinfile (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_uri_get_pinfile</code> is deprecated and should not be used in newly-written code.</p>
<p>use <a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-pin-source" title="p11_kit_uri_get_pin_source ()"><code class="function">p11_kit_uri_get_pin_source()</code></a>.</p>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-pinfile.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-pinfile"></a><h3>p11_kit_uri_set_pinfile ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_uri_set_pinfile (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                         <em class="parameter"><code>const <span class="type">char</span> *pinfile</code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_uri_set_pinfile</code> is deprecated and should not be used in newly-written code.</p>
<p>use <a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-pin-source" title="p11_kit_uri_set_pin_source ()"><code class="function">p11_kit_uri_set_pin_source()</code></a>.</p>
</div>
<div class="refsect3">
<a name="p11-kit-uri-set-pinfile.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>pinfile</p></td>
<td class="parameter_description"><p>The pinfile</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-module-name"></a><h3>p11_kit_uri_get_module_name ()</h3>
<pre class="programlisting">const <span class="returnvalue">char</span> *
p11_kit_uri_get_module_name (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Get the 'module-name' part of the URI. This is used by some
applications to explicitly specify the name of a PKCS#11 module.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-module-name.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-module-name.returns"></a><h4>Returns</h4>
<p> The module-name or <code class="literal">NULL</code> if not present.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-module-name"></a><h3>p11_kit_uri_set_module_name ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_uri_set_module_name (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                             <em class="parameter"><code>const <span class="type">char</span> *name</code></em>);</pre>
<p>Set the 'module-name' part of the URI. This is used by some
applications to explicitly specify the name of a PKCS#11 module.</p>
<div class="refsect3">
<a name="p11-kit-uri-set-module-name.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>name</p></td>
<td class="parameter_description"><p>The new module-name</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-module-path"></a><h3>p11_kit_uri_get_module_path ()</h3>
<pre class="programlisting">const <span class="returnvalue">char</span> *
p11_kit_uri_get_module_path (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Get the 'module-path' part of the URI. This is used by some
applications to explicitly specify the path of a PKCS#11 module.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-module-path.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-module-path.returns"></a><h4>Returns</h4>
<p> The module-path or <code class="literal">NULL</code> if not present.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-module-path"></a><h3>p11_kit_uri_set_module_path ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_uri_set_module_path (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                             <em class="parameter"><code>const <span class="type">char</span> *path</code></em>);</pre>
<p>Set the 'module-path' part of the URI. This is used by some
applications to explicitly specify the path of a PKCS#11 module.</p>
<div class="refsect3">
<a name="p11-kit-uri-set-module-path.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>path</p></td>
<td class="parameter_description"><p>The new module-path</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-get-vendor-query"></a><h3>p11_kit_uri_get_vendor_query ()</h3>
<pre class="programlisting">const <span class="returnvalue">char</span> *
p11_kit_uri_get_vendor_query (<em class="parameter"><code>const <a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                              <em class="parameter"><code>const <span class="type">char</span> *name</code></em>);</pre>
<p>Get the vendor query part of the URI, identified by <em class="parameter"><code>name</code></em>
. This is
used by some applications to explicitly specify the path of a
PKCS#11 module.</p>
<div class="refsect3">
<a name="p11-kit-uri-get-vendor-query.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>name</p></td>
<td class="parameter_description"><p>The name of vendor query</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-get-vendor-query.returns"></a><h4>Returns</h4>
<p> The value of vendor query or <code class="literal">NULL</code> if not present.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-set-vendor-query"></a><h3>p11_kit_uri_set_vendor_query ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_set_vendor_query (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                              <em class="parameter"><code>const <span class="type">char</span> *name</code></em>,
                              <em class="parameter"><code>const <span class="type">char</span> *value</code></em>);</pre>
<p>Set the vendor query part of the URI, identified by <em class="parameter"><code>name</code></em>
. This is
used by some applications to explicitly specify the path of a
PKCS#11 module.</p>
<div class="refsect3">
<a name="p11-kit-uri-set-vendor-query.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>name</p></td>
<td class="parameter_description"><p>The name of vendor query</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>value</p></td>
<td class="parameter_description"><p>The value of vendor query. </p></td>
<td class="parameter_annotations"><span class="annotation">[<acronym title="NULL is ok, both for passing and for returning."><span class="acronym">allow-none</span></acronym>]</span></td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-set-vendor-query.returns"></a><h4>Returns</h4>
<p> 1 if the vendor query is set or removed, 0 if not.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-format"></a><h3>p11_kit_uri_format ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_format (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>,
                    <em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUriType" title="enum P11KitUriType"><span class="type">P11KitUriType</span></a> uri_type</code></em>,
                    <em class="parameter"><code><span class="type">char</span> **string</code></em>);</pre>
<p>Format a PKCS#11 URI into a string.</p>
<p>Fields which are zeroed out will not be included in the resulting string.
Attributes which are not present will also not be included.</p>
<p>The uri_type of URI specified limits the different parts of the resulting
URI. To format a URI containing all possible information use
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-FOR-ANY:CAPS"><code class="literal">P11_KIT_URI_FOR_ANY</code></a></p>
<p>It's up to the caller to guarantee that the attributes set in <em class="parameter"><code>uri</code></em>
 are
those appropriate for inclusion in a URI, specifically:
<code class="literal">CKA_ID</code>, <code class="literal">CKA_LABEL</code>
and <code class="literal">CKA_CLASS</code>. The class must be one of
<code class="literal">CKO_DATA</code>, <code class="literal">CKO_SECRET_KEY</code>,
<code class="literal">CKO_CERTIFICATE</code>, <code class="literal">CKO_PUBLIC_KEY</code>,
<code class="literal">CKO_PRIVATE_KEY</code>.</p>
<p>The resulting string should be freed with <code class="function">free()</code>.</p>
<div class="refsect3">
<a name="p11-kit-uri-format.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI.</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>uri_type</p></td>
<td class="parameter_description"><p>The type of URI that should be produced.</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>string</p></td>
<td class="parameter_description"><p>Location to store a newly allocated string.</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-format.returns"></a><h4>Returns</h4>
<p> <a class="link" href="p11-kit-URIs.html#P11-KIT-URI-OK:CAPS"><code class="literal">P11_KIT_URI_OK</code></a> if the URI was formatted successfully,
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-UNEXPECTED:CAPS"><code class="literal">P11_KIT_URI_UNEXPECTED</code></a> if the data in <em class="parameter"><code>uri</code></em>
is invalid for a URI.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-parse"></a><h3>p11_kit_uri_parse ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_uri_parse (<em class="parameter"><code>const <span class="type">char</span> *string</code></em>,
                   <em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUriType" title="enum P11KitUriType"><span class="type">P11KitUriType</span></a> uri_type</code></em>,
                   <em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Parse a PKCS#11 URI string.</p>
<p>PKCS#11 URIs can represent tokens, objects or modules. The uri_type argument
allows the caller to specify what type of URI is expected and the sorts of
things the URI should match. <a class="link" href="p11-kit-URIs.html#P11-KIT-URI-FOR-ANY:CAPS"><code class="literal">P11_KIT_URI_FOR_ANY</code></a> can be used to parse a URI
for any context. It's then up to the caller to make sense of the way that
it is used.</p>
<p>If the PKCS#11 URI contains unrecognized URI parts or parts not applicable
to the specified context, then the unrecognized flag will be set. This will
prevent the URI from matching using the various match functions.</p>
<div class="refsect3">
<a name="p11-kit-uri-parse.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>string</p></td>
<td class="parameter_description"><p>The string to parse</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>uri_type</p></td>
<td class="parameter_description"><p>The type of URI that is expected</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The blank URI to parse the values into</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-parse.returns"></a><h4>Returns</h4>
<p> <a class="link" href="p11-kit-URIs.html#P11-KIT-URI-OK:CAPS"><code class="literal">P11_KIT_URI_OK</code></a> if the URI was parsed successfully.
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-BAD-SCHEME:CAPS"><code class="literal">P11_KIT_URI_BAD_SCHEME</code></a> if this was not a PKCS#11 URI.
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-BAD-SYNTAX:CAPS"><code class="literal">P11_KIT_URI_BAD_SYNTAX</code></a> if the URI syntax was bad.
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-BAD-VERSION:CAPS"><code class="literal">P11_KIT_URI_BAD_VERSION</code></a> if a version number was bad.
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-BAD-ENCODING:CAPS"><code class="literal">P11_KIT_URI_BAD_ENCODING</code></a> if the URI encoding was invalid.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-free"></a><h3>p11_kit_uri_free ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_uri_free (<em class="parameter"><code><a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri"><span class="type">P11KitUri</span></a> *uri</code></em>);</pre>
<p>Free a PKCS#11 URI.</p>
<div class="refsect3">
<a name="p11-kit-uri-free.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>uri</p></td>
<td class="parameter_description"><p>The URI</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri-message"></a><h3>p11_kit_uri_message ()</h3>
<pre class="programlisting">const <span class="returnvalue">char</span> *
p11_kit_uri_message (<em class="parameter"><code><span class="type">int</span> code</code></em>);</pre>
<p>Lookup a message for the uri error code. These codes are the P11_KIT_URI_XXX
error codes that can be returned from <a class="link" href="p11-kit-URIs.html#p11-kit-uri-parse" title="p11_kit_uri_parse ()"><code class="function">p11_kit_uri_parse()</code></a> or
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-format" title="p11_kit_uri_format ()"><code class="function">p11_kit_uri_format()</code></a>. As a special case <code class="literal">NULL</code>, will be returned for
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-OK:CAPS"><code class="literal">P11_KIT_URI_OK</code></a>.</p>
<div class="refsect3">
<a name="p11-kit-uri-message.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>code</p></td>
<td class="parameter_description"><p>The error code</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-uri-message.returns"></a><h4>Returns</h4>
<p> The message for the error code. This string is owned by the p11-kit
library.</p>
</div>
</div>
</div>
<div class="refsect1">
<a name="p11-kit-URIs.other_details"></a><h2>Types and Values</h2>
<div class="refsect2">
<a name="P11-KIT-URI-SCHEME:CAPS"></a><h3>P11_KIT_URI_SCHEME</h3>
<pre class="programlisting">#define             P11_KIT_URI_SCHEME</pre>
<p>String of URI scheme for PKCS#11 URIs.</p>
</div>
<hr>
<div class="refsect2">
<a name="P11-KIT-URI-SCHEME-LEN:CAPS"></a><h3>P11_KIT_URI_SCHEME_LEN</h3>
<pre class="programlisting">#define             P11_KIT_URI_SCHEME_LEN</pre>
<p>Length of <a class="link" href="p11-kit-URIs.html#P11-KIT-URI-SCHEME:CAPS" title="P11_KIT_URI_SCHEME"><code class="literal">P11_KIT_URI_SCHEME</code></a>.</p>
</div>
<hr>
<div class="refsect2">
<a name="P11KitUriType"></a><h3>enum P11KitUriType</h3>
<p>A PKCS#11 URI can represent different kinds of things. This flag is used by
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-parse" title="p11_kit_uri_parse ()"><code class="function">p11_kit_uri_parse()</code></a> to denote in what context the URI will be used.</p>
<p>The various types can be combined.</p>
<div class="refsect3">
<a name="P11KitUriType.members"></a><h4>Members</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="300px" class="enum_members_name">
<col class="enum_members_description">
<col width="200px" class="enum_members_annotations">
</colgroup>
<tbody>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-FOR-OBJECT:CAPS"></a>P11_KIT_URI_FOR_OBJECT</p></td>
<td class="enum_member_description">
<p>The URI represents one or more objects</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-FOR-TOKEN:CAPS"></a>P11_KIT_URI_FOR_TOKEN</p></td>
<td class="enum_member_description">
<p>The URI represents one or more tokens</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-FOR-SLOT:CAPS"></a>P11_KIT_URI_FOR_SLOT</p></td>
<td class="enum_member_description">
<p>The URI represents one or more slots</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-FOR-MODULE:CAPS"></a>P11_KIT_URI_FOR_MODULE</p></td>
<td class="enum_member_description">
<p>The URI represents one or more modules</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-FOR-MODULE-WITH-VERSION:CAPS"></a>P11_KIT_URI_FOR_MODULE_WITH_VERSION</p></td>
<td class="enum_member_description">
<p>The URI represents a module with
a specific version.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-FOR-OBJECT-ON-TOKEN:CAPS"></a>P11_KIT_URI_FOR_OBJECT_ON_TOKEN</p></td>
<td class="enum_member_description">
<p>The URI represents one or more objects
that are present on a specific token.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-FOR-OBJECT-ON-TOKEN-AND-MODULE:CAPS"></a>P11_KIT_URI_FOR_OBJECT_ON_TOKEN_AND_MODULE</p></td>
<td class="enum_member_description">
<p>The URI represents one or more
objects that are present on a specific token, being used with a certain
module.</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-FOR-ANY:CAPS"></a>P11_KIT_URI_FOR_ANY</p></td>
<td class="enum_member_description">
<p>The URI can represent anything</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="P11KitUriResult"></a><h3>enum P11KitUriResult</h3>
<p>Error codes returned by various functions. The functions each clearly state
which error codes they are capable of returning.</p>
<div class="refsect3">
<a name="P11KitUriResult.members"></a><h4>Members</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="300px" class="enum_members_name">
<col class="enum_members_description">
<col width="200px" class="enum_members_annotations">
</colgroup>
<tbody>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-OK:CAPS"></a>P11_KIT_URI_OK</p></td>
<td class="enum_member_description">
<p>Success</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-UNEXPECTED:CAPS"></a>P11_KIT_URI_UNEXPECTED</p></td>
<td class="enum_member_description">
<p>Unexpected or internal system error</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-BAD-SCHEME:CAPS"></a>P11_KIT_URI_BAD_SCHEME</p></td>
<td class="enum_member_description">
<p>The URI had a bad scheme</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-BAD-ENCODING:CAPS"></a>P11_KIT_URI_BAD_ENCODING</p></td>
<td class="enum_member_description">
<p>The URI had a bad encoding</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-BAD-SYNTAX:CAPS"></a>P11_KIT_URI_BAD_SYNTAX</p></td>
<td class="enum_member_description">
<p>The URI had a bad syntax</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-BAD-VERSION:CAPS"></a>P11_KIT_URI_BAD_VERSION</p></td>
<td class="enum_member_description">
<p>The URI contained a bad version number</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
<tr>
<td class="enum_member_name"><p><a name="P11-KIT-URI-NOT-FOUND:CAPS"></a>P11_KIT_URI_NOT_FOUND</p></td>
<td class="enum_member_description">
<p>A requested part of the URI was not found</p>
</td>
<td class="enum_member_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="P11KitUri"></a><h3>P11KitUri</h3>
<p>A structure representing a PKCS#11 URI. There are no public fields
visible in this structure. Use the various accessor functions.</p>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-uri"></a><h3>p11_kit_uri</h3>
</div>
<hr>
<div class="refsect2">
<a name="P11-KIT-URI-NO-MEMORY:CAPS"></a><h3>P11_KIT_URI_NO_MEMORY</h3>
<pre class="programlisting">#define             P11_KIT_URI_NO_MEMORY</pre>
<p>Unexpected memory allocation failure result. Same as <a class="link" href="p11-kit-URIs.html#P11-KIT-URI-UNEXPECTED:CAPS"><span class="type">P11_KIT_URI_UNEXPECTED</span></a>.</p>
</div>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>