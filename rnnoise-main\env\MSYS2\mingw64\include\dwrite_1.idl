/*
 * Copyright 2013 Nikolay Sivov for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "dwrite.idl";

typedef enum DWRITE_PANOSE_FAMILY
{
    DWRITE_PANOSE_FAMILY_ANY,
    DWRITE_PANOSE_FAMILY_NO_FIT,
    DWRITE_PANOSE_FAMILY_TEXT_DISPLAY,
    DWRITE_PANOSE_FAMILY_SCRIPT,
    DWRITE_PANOSE_FAMILY_DECORATIVE,
    DWRITE_PANOSE_FAMILY_SYMBOL,
    DWRITE_PANOSE_FAMILY_PICTORIAL = DWRITE_PANOSE_FAMILY_SYMBOL
} DWRITE_PANOSE_FAMILY;

typedef enum DWRITE_PANOSE_SERIF_STYLE
{
    DWRITE_PANOSE_SERIF_STYLE_ANY,
    DWRITE_PANOSE_SERIF_STYLE_NO_FIT,
    DWRITE_PANOSE_SERIF_STYLE_COVE,
    DWRITE_PANOSE_SERIF_STYLE_OBTUSE_COVE,
    DWRITE_PANOSE_SERIF_STYLE_SQUARE_COVE,
    DWRITE_PANOSE_SERIF_STYLE_OBTUSE_SQUARE_COVE,
    DWRITE_PANOSE_SERIF_STYLE_SQUARE,
    DWRITE_PANOSE_SERIF_STYLE_THIN,
    DWRITE_PANOSE_SERIF_STYLE_OVAL,
    DWRITE_PANOSE_SERIF_STYLE_EXAGGERATED,
    DWRITE_PANOSE_SERIF_STYLE_TRIANGLE,
    DWRITE_PANOSE_SERIF_STYLE_NORMAL_SANS,
    DWRITE_PANOSE_SERIF_STYLE_OBTUSE_SANS,
    DWRITE_PANOSE_SERIF_STYLE_PERPENDICULAR_SANS,
    DWRITE_PANOSE_SERIF_STYLE_FLARED,
    DWRITE_PANOSE_SERIF_STYLE_ROUNDED,
    DWRITE_PANOSE_SERIF_STYLE_SCRIPT,
    DWRITE_PANOSE_SERIF_STYLE_PERP_SANS = DWRITE_PANOSE_SERIF_STYLE_PERPENDICULAR_SANS,
    DWRITE_PANOSE_SERIF_STYLE_BONE = DWRITE_PANOSE_SERIF_STYLE_OVAL
} DWRITE_PANOSE_SERIF_STYLE;

typedef enum DWRITE_PANOSE_WEIGHT
{
    DWRITE_PANOSE_WEIGHT_ANY,
    DWRITE_PANOSE_WEIGHT_NO_FIT,
    DWRITE_PANOSE_WEIGHT_VERY_LIGHT,
    DWRITE_PANOSE_WEIGHT_LIGHT,
    DWRITE_PANOSE_WEIGHT_THIN,
    DWRITE_PANOSE_WEIGHT_BOOK,
    DWRITE_PANOSE_WEIGHT_MEDIUM,
    DWRITE_PANOSE_WEIGHT_DEMI,
    DWRITE_PANOSE_WEIGHT_BOLD,
    DWRITE_PANOSE_WEIGHT_HEAVY,
    DWRITE_PANOSE_WEIGHT_BLACK,
    DWRITE_PANOSE_WEIGHT_EXTRA_BLACK,
    DWRITE_PANOSE_WEIGHT_NORD = DWRITE_PANOSE_WEIGHT_EXTRA_BLACK
} DWRITE_PANOSE_WEIGHT;

typedef enum DWRITE_PANOSE_PROPORTION
{
    DWRITE_PANOSE_PROPORTION_ANY,
    DWRITE_PANOSE_PROPORTION_NO_FIT,
    DWRITE_PANOSE_PROPORTION_OLD_STYLE,
    DWRITE_PANOSE_PROPORTION_MODERN,
    DWRITE_PANOSE_PROPORTION_EVEN_WIDTH,
    DWRITE_PANOSE_PROPORTION_EXPANDED,
    DWRITE_PANOSE_PROPORTION_CONDENSED,
    DWRITE_PANOSE_PROPORTION_VERY_EXPANDED,
    DWRITE_PANOSE_PROPORTION_VERY_CONDENSED,
    DWRITE_PANOSE_PROPORTION_MONOSPACED
} DWRITE_PANOSE_PROPORTION;

typedef enum DWRITE_PANOSE_CONTRAST
{
    DWRITE_PANOSE_CONTRAST_ANY,
    DWRITE_PANOSE_CONTRAST_NO_FIT,
    DWRITE_PANOSE_CONTRAST_NONE,
    DWRITE_PANOSE_CONTRAST_VERY_LOW,
    DWRITE_PANOSE_CONTRAST_LOW,
    DWRITE_PANOSE_CONTRAST_MEDIUM_LOW,
    DWRITE_PANOSE_CONTRAST_MEDIUM,
    DWRITE_PANOSE_CONTRAST_MEDIUM_HIGH,
    DWRITE_PANOSE_CONTRAST_HIGH,
    DWRITE_PANOSE_CONTRAST_VERY_HIGH,
    DWRITE_PANOSE_CONTRAST_HORIZONTAL_LOW,
    DWRITE_PANOSE_CONTRAST_HORIZONTAL_MEDIUM,
    DWRITE_PANOSE_CONTRAST_HORIZONTAL_HIGH,
    DWRITE_PANOSE_CONTRAST_BROKEN
} DWRITE_PANOSE_CONTRAST;

typedef enum DWRITE_PANOSE_STROKE_VARIATION
{
    DWRITE_PANOSE_STROKE_VARIATION_ANY,
    DWRITE_PANOSE_STROKE_VARIATION_NO_FIT,
    DWRITE_PANOSE_STROKE_VARIATION_NO_VARIATION,
    DWRITE_PANOSE_STROKE_VARIATION_GRADUAL_DIAGONAL,
    DWRITE_PANOSE_STROKE_VARIATION_GRADUAL_TRANSITIONAL,
    DWRITE_PANOSE_STROKE_VARIATION_GRADUAL_VERTICAL,
    DWRITE_PANOSE_STROKE_VARIATION_GRADUAL_HORIZONTAL,
    DWRITE_PANOSE_STROKE_VARIATION_RAPID_VERTICAL,
    DWRITE_PANOSE_STROKE_VARIATION_RAPID_HORIZONTAL,
    DWRITE_PANOSE_STROKE_VARIATION_INSTANT_VERTICAL,
    DWRITE_PANOSE_STROKE_VARIATION_INSTANT_HORIZONTAL
} DWRITE_PANOSE_STROKE_VARIATION;

typedef enum DWRITE_PANOSE_ARM_STYLE
{
    DWRITE_PANOSE_ARM_STYLE_ANY,
    DWRITE_PANOSE_ARM_STYLE_NO_FIT,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_HORIZONTAL,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_WEDGE,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_VERTICAL,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_SINGLE_SERIF,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_DOUBLE_SERIF,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_HORIZONTAL,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_WEDGE,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_VERTICAL,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_SINGLE_SERIF,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_DOUBLE_SERIF,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_HORZ = DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_HORIZONTAL,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_VERT = DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_VERTICAL,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_HORZ = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_HORIZONTAL,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_WEDGE = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_WEDGE,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_VERT = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_VERTICAL,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_SINGLE_SERIF = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_SINGLE_SERIF,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_DOUBLE_SERIF = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_DOUBLE_SERIF
} DWRITE_PANOSE_ARM_STYLE;

typedef enum DWRITE_PANOSE_LETTERFORM
{
    DWRITE_PANOSE_LETTERFORM_ANY,
    DWRITE_PANOSE_LETTERFORM_NO_FIT,
    DWRITE_PANOSE_LETTERFORM_NORMAL_CONTACT,
    DWRITE_PANOSE_LETTERFORM_NORMAL_WEIGHTED,
    DWRITE_PANOSE_LETTERFORM_NORMAL_BOXED,
    DWRITE_PANOSE_LETTERFORM_NORMAL_FLATTENED,
    DWRITE_PANOSE_LETTERFORM_NORMAL_ROUNDED,
    DWRITE_PANOSE_LETTERFORM_NORMAL_OFF_CENTER,
    DWRITE_PANOSE_LETTERFORM_NORMAL_SQUARE,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_CONTACT,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_WEIGHTED,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_BOXED,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_FLATTENED,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_ROUNDED,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_OFF_CENTER,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_SQUARE
} DWRITE_PANOSE_LETTERFORM;

typedef enum DWRITE_PANOSE_MIDLINE
{
    DWRITE_PANOSE_MIDLINE_ANY,
    DWRITE_PANOSE_MIDLINE_NO_FIT,
    DWRITE_PANOSE_MIDLINE_STANDARD_TRIMMED,
    DWRITE_PANOSE_MIDLINE_STANDARD_POINTED,
    DWRITE_PANOSE_MIDLINE_STANDARD_SERIFED,
    DWRITE_PANOSE_MIDLINE_HIGH_TRIMMED,
    DWRITE_PANOSE_MIDLINE_HIGH_POINTED,
    DWRITE_PANOSE_MIDLINE_HIGH_SERIFED,
    DWRITE_PANOSE_MIDLINE_CONSTANT_TRIMMED,
    DWRITE_PANOSE_MIDLINE_CONSTANT_POINTED,
    DWRITE_PANOSE_MIDLINE_CONSTANT_SERIFED,
    DWRITE_PANOSE_MIDLINE_LOW_TRIMMED,
    DWRITE_PANOSE_MIDLINE_LOW_POINTED,
    DWRITE_PANOSE_MIDLINE_LOW_SERIFED
} DWRITE_PANOSE_MIDLINE;

typedef enum DWRITE_PANOSE_XHEIGHT
{
    DWRITE_PANOSE_XHEIGHT_ANY,
    DWRITE_PANOSE_XHEIGHT_NO_FIT,
    DWRITE_PANOSE_XHEIGHT_CONSTANT_SMALL,
    DWRITE_PANOSE_XHEIGHT_CONSTANT_STANDARD,
    DWRITE_PANOSE_XHEIGHT_CONSTANT_LARGE,
    DWRITE_PANOSE_XHEIGHT_DUCKING_SMALL,
    DWRITE_PANOSE_XHEIGHT_DUCKING_STANDARD,
    DWRITE_PANOSE_XHEIGHT_DUCKING_LARGE,
    DWRITE_PANOSE_XHEIGHT_CONSTANT_STD = DWRITE_PANOSE_XHEIGHT_CONSTANT_STANDARD,
    DWRITE_PANOSE_XHEIGHT_DUCKING_STD = DWRITE_PANOSE_XHEIGHT_DUCKING_STANDARD
} DWRITE_PANOSE_XHEIGHT;

typedef enum DWRITE_PANOSE_TOOL_KIND
{
    DWRITE_PANOSE_TOOL_KIND_ANY,
    DWRITE_PANOSE_TOOL_KIND_NO_FIT,
    DWRITE_PANOSE_TOOL_KIND_FLAT_NIB,
    DWRITE_PANOSE_TOOL_KIND_PRESSURE_POINT,
    DWRITE_PANOSE_TOOL_KIND_ENGRAVED,
    DWRITE_PANOSE_TOOL_KIND_BALL,
    DWRITE_PANOSE_TOOL_KIND_BRUSH,
    DWRITE_PANOSE_TOOL_KIND_ROUGH,
    DWRITE_PANOSE_TOOL_KIND_FELT_PEN_BRUSH_TIP,
    DWRITE_PANOSE_TOOL_KIND_WILD_BRUSH
} DWRITE_PANOSE_TOOL_KIND;

typedef enum DWRITE_PANOSE_SPACING
{
    DWRITE_PANOSE_SPACING_ANY,
    DWRITE_PANOSE_SPACING_NO_FIT,
    DWRITE_PANOSE_SPACING_PROPORTIONAL_SPACED,
    DWRITE_PANOSE_SPACING_MONOSPACED
} DWRITE_PANOSE_SPACING;

typedef enum DWRITE_PANOSE_ASPECT_RATIO
{
    DWRITE_PANOSE_ASPECT_RATIO_ANY,
    DWRITE_PANOSE_ASPECT_RATIO_NO_FIT,
    DWRITE_PANOSE_ASPECT_RATIO_VERY_CONDENSED,
    DWRITE_PANOSE_ASPECT_RATIO_CONDENSED,
    DWRITE_PANOSE_ASPECT_RATIO_NORMAL,
    DWRITE_PANOSE_ASPECT_RATIO_EXPANDED,
    DWRITE_PANOSE_ASPECT_RATIO_VERY_EXPANDED
} DWRITE_PANOSE_ASPECT_RATIO;

typedef enum DWRITE_PANOSE_SCRIPT_TOPOLOGY
{
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_ANY,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_NO_FIT,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_ROMAN_DISCONNECTED,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_ROMAN_TRAILING,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_ROMAN_CONNECTED,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_CURSIVE_DISCONNECTED,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_CURSIVE_TRAILING,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_CURSIVE_CONNECTED,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_BLACKLETTER_DISCONNECTED,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_BLACKLETTER_TRAILING,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_BLACKLETTER_CONNECTED
} DWRITE_PANOSE_SCRIPT_TOPOLOGY;

typedef enum DWRITE_PANOSE_SCRIPT_FORM
{
    DWRITE_PANOSE_SCRIPT_FORM_ANY,
    DWRITE_PANOSE_SCRIPT_FORM_NO_FIT,
    DWRITE_PANOSE_SCRIPT_FORM_UPRIGHT_NO_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_UPRIGHT_SOME_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_UPRIGHT_MORE_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_UPRIGHT_EXTREME_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_OBLIQUE_NO_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_OBLIQUE_SOME_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_OBLIQUE_MORE_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_OBLIQUE_EXTREME_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_EXAGGERATED_NO_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_EXAGGERATED_SOME_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_EXAGGERATED_MORE_WRAPPING,
    DWRITE_PANOSE_SCRIPT_FORM_EXAGGERATED_EXTREME_WRAPPING
} DWRITE_PANOSE_SCRIPT_FORM;

typedef enum DWRITE_PANOSE_FINIALS
{
    DWRITE_PANOSE_FINIALS_ANY,
    DWRITE_PANOSE_FINIALS_NO_FIT,
    DWRITE_PANOSE_FINIALS_NONE_NO_LOOPS,
    DWRITE_PANOSE_FINIALS_NONE_CLOSED_LOOPS,
    DWRITE_PANOSE_FINIALS_NONE_OPEN_LOOPS,
    DWRITE_PANOSE_FINIALS_SHARP_NO_LOOPS,
    DWRITE_PANOSE_FINIALS_SHARP_CLOSED_LOOPS,
    DWRITE_PANOSE_FINIALS_SHARP_OPEN_LOOPS,
    DWRITE_PANOSE_FINIALS_TAPERED_NO_LOOPS,
    DWRITE_PANOSE_FINIALS_TAPERED_CLOSED_LOOPS,
    DWRITE_PANOSE_FINIALS_TAPERED_OPEN_LOOPS,
    DWRITE_PANOSE_FINIALS_ROUND_NO_LOOPS,
    DWRITE_PANOSE_FINIALS_ROUND_CLOSED_LOOPS,
    DWRITE_PANOSE_FINIALS_ROUND_OPEN_LOOPS
} DWRITE_PANOSE_FINIALS;

typedef enum DWRITE_PANOSE_XASCENT
{
    DWRITE_PANOSE_XASCENT_ANY,
    DWRITE_PANOSE_XASCENT_NO_FIT,
    DWRITE_PANOSE_XASCENT_VERY_LOW,
    DWRITE_PANOSE_XASCENT_LOW,
    DWRITE_PANOSE_XASCENT_MEDIUM,
    DWRITE_PANOSE_XASCENT_HIGH,
    DWRITE_PANOSE_XASCENT_VERY_HIGH
} DWRITE_PANOSE_XASCENT;

typedef enum DWRITE_PANOSE_DECORATIVE_CLASS
{
    DWRITE_PANOSE_DECORATIVE_CLASS_ANY,
    DWRITE_PANOSE_DECORATIVE_CLASS_NO_FIT,
    DWRITE_PANOSE_DECORATIVE_CLASS_DERIVATIVE,
    DWRITE_PANOSE_DECORATIVE_CLASS_NONSTANDARD_TOPOLOGY,
    DWRITE_PANOSE_DECORATIVE_CLASS_NONSTANDARD_ELEMENTS,
    DWRITE_PANOSE_DECORATIVE_CLASS_NONSTANDARD_ASPECT,
    DWRITE_PANOSE_DECORATIVE_CLASS_INITIALS,
    DWRITE_PANOSE_DECORATIVE_CLASS_CARTOON,
    DWRITE_PANOSE_DECORATIVE_CLASS_PICTURE_STEMS,
    DWRITE_PANOSE_DECORATIVE_CLASS_ORNAMENTED,
    DWRITE_PANOSE_DECORATIVE_CLASS_TEXT_AND_BACKGROUND,
    DWRITE_PANOSE_DECORATIVE_CLASS_COLLAGE,
    DWRITE_PANOSE_DECORATIVE_CLASS_MONTAGE
} DWRITE_PANOSE_DECORATIVE_CLASS;

typedef enum DWRITE_PANOSE_ASPECT
{
    DWRITE_PANOSE_ASPECT_ANY,
    DWRITE_PANOSE_ASPECT_NO_FIT,
    DWRITE_PANOSE_ASPECT_SUPER_CONDENSED,
    DWRITE_PANOSE_ASPECT_VERY_CONDENSED,
    DWRITE_PANOSE_ASPECT_CONDENSED,
    DWRITE_PANOSE_ASPECT_NORMAL,
    DWRITE_PANOSE_ASPECT_EXTENDED,
    DWRITE_PANOSE_ASPECT_VERY_EXTENDED,
    DWRITE_PANOSE_ASPECT_SUPER_EXTENDED,
    DWRITE_PANOSE_ASPECT_MONOSPACED
} DWRITE_PANOSE_ASPECT;

typedef enum DWRITE_PANOSE_FILL
{
    DWRITE_PANOSE_FILL_ANY,
    DWRITE_PANOSE_FILL_NO_FIT,
    DWRITE_PANOSE_FILL_STANDARD_SOLID_FILL,
    DWRITE_PANOSE_FILL_NO_FILL,
    DWRITE_PANOSE_FILL_PATTERNED_FILL,
    DWRITE_PANOSE_FILL_COMPLEX_FILL,
    DWRITE_PANOSE_FILL_SHAPED_FILL,
    DWRITE_PANOSE_FILL_DRAWN_DISTRESSED
} DWRITE_PANOSE_FILL;

typedef enum DWRITE_PANOSE_LINING
{
    DWRITE_PANOSE_LINING_ANY,
    DWRITE_PANOSE_LINING_NO_FIT,
    DWRITE_PANOSE_LINING_NONE,
    DWRITE_PANOSE_LINING_INLINE,
    DWRITE_PANOSE_LINING_OUTLINE,
    DWRITE_PANOSE_LINING_ENGRAVED,
    DWRITE_PANOSE_LINING_SHADOW,
    DWRITE_PANOSE_LINING_RELIEF,
    DWRITE_PANOSE_LINING_BACKDROP
} DWRITE_PANOSE_LINING;

typedef enum DWRITE_PANOSE_DECORATIVE_TOPOLOGY
{
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_ANY,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_NO_FIT,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_STANDARD,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_SQUARE,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_MULTIPLE_SEGMENT,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_ART_DECO,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_UNEVEN_WEIGHTING,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_DIVERSE_ARMS,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_DIVERSE_FORMS,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_LOMBARDIC_FORMS,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_UPPER_CASE_IN_LOWER_CASE,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_IMPLIED_TOPOLOGY,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_HORSESHOE_E_AND_A,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_CURSIVE,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_BLACKLETTER,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_SWASH_VARIANCE
} DWRITE_PANOSE_DECORATIVE_TOPOLOGY;

typedef enum DWRITE_PANOSE_CHARACTER_RANGES
{
    DWRITE_PANOSE_CHARACTER_RANGES_ANY,
    DWRITE_PANOSE_CHARACTER_RANGES_NO_FIT,
    DWRITE_PANOSE_CHARACTER_RANGES_EXTENDED_COLLECTION,
    DWRITE_PANOSE_CHARACTER_RANGES_LITERALS,
    DWRITE_PANOSE_CHARACTER_RANGES_NO_LOWER_CASE,
    DWRITE_PANOSE_CHARACTER_RANGES_SMALL_CAPS
} DWRITE_PANOSE_CHARACTER_RANGES;

typedef enum DWRITE_PANOSE_SYMBOL_KIND
{
    DWRITE_PANOSE_SYMBOL_KIND_ANY,
    DWRITE_PANOSE_SYMBOL_KIND_NO_FIT,
    DWRITE_PANOSE_SYMBOL_KIND_MONTAGES,
    DWRITE_PANOSE_SYMBOL_KIND_PICTURES,
    DWRITE_PANOSE_SYMBOL_KIND_SHAPES,
    DWRITE_PANOSE_SYMBOL_KIND_SCIENTIFIC,
    DWRITE_PANOSE_SYMBOL_KIND_MUSIC,
    DWRITE_PANOSE_SYMBOL_KIND_EXPERT,
    DWRITE_PANOSE_SYMBOL_KIND_PATTERNS,
    DWRITE_PANOSE_SYMBOL_KIND_BOARDERS,
    DWRITE_PANOSE_SYMBOL_KIND_ICONS,
    DWRITE_PANOSE_SYMBOL_KIND_LOGOS,
    DWRITE_PANOSE_SYMBOL_KIND_INDUSTRY_SPECIFIC
} DWRITE_PANOSE_SYMBOL_KIND;

typedef enum DWRITE_PANOSE_SYMBOL_ASPECT_RATIO
{
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_ANY,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_NO_FIT,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_NO_WIDTH,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_EXCEPTIONALLY_WIDE,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_SUPER_WIDE,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_VERY_WIDE,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_WIDE,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_NORMAL,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_NARROW,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_VERY_NARROW
} DWRITE_PANOSE_SYMBOL_ASPECT_RATIO;

typedef enum DWRITE_OUTLINE_THRESHOLD
{
    DWRITE_OUTLINE_THRESHOLD_ANTIALIASED,
    DWRITE_OUTLINE_THRESHOLD_ALIASED
} DWRITE_OUTLINE_THRESHOLD;

typedef enum DWRITE_BASELINE
{
    DWRITE_BASELINE_DEFAULT,
    DWRITE_BASELINE_ROMAN,
    DWRITE_BASELINE_CENTRAL,
    DWRITE_BASELINE_MATH,
    DWRITE_BASELINE_HANGING,
    DWRITE_BASELINE_IDEOGRAPHIC_BOTTOM,
    DWRITE_BASELINE_IDEOGRAPHIC_TOP,
    DWRITE_BASELINE_MINIMUM,
    DWRITE_BASELINE_MAXIMUM
} DWRITE_BASELINE;

typedef enum DWRITE_VERTICAL_GLYPH_ORIENTATION
{
    DWRITE_VERTICAL_GLYPH_ORIENTATION_DEFAULT,
    DWRITE_VERTICAL_GLYPH_ORIENTATION_STACKED
} DWRITE_VERTICAL_GLYPH_ORIENTATION;

typedef enum DWRITE_GLYPH_ORIENTATION_ANGLE
{
    DWRITE_GLYPH_ORIENTATION_ANGLE_0_DEGREES,
    DWRITE_GLYPH_ORIENTATION_ANGLE_90_DEGREES,
    DWRITE_GLYPH_ORIENTATION_ANGLE_180_DEGREES,
    DWRITE_GLYPH_ORIENTATION_ANGLE_270_DEGREES
} DWRITE_GLYPH_ORIENTATION_ANGLE;

typedef struct DWRITE_FONT_METRICS1
{
    UINT16 designUnitsPerEm;
    UINT16 ascent;
    UINT16 descent;
    INT16 lineGap;
    UINT16 capHeight;
    UINT16 xHeight;
    INT16 underlinePosition;
    UINT16 underlineThickness;
    INT16 strikethroughPosition;
    UINT16 strikethroughThickness;
    INT16 glyphBoxLeft;
    INT16 glyphBoxTop;
    INT16 glyphBoxRight;
    INT16 glyphBoxBottom;
    INT16 subscriptPositionX;
    INT16 subscriptPositionY;
    INT16 subscriptSizeX;
    INT16 subscriptSizeY;
    INT16 superscriptPositionX;
    INT16 superscriptPositionY;
    INT16 superscriptSizeX;
    INT16 superscriptSizeY;
    BOOL hasTypographicMetrics;
} DWRITE_FONT_METRICS1;

typedef struct DWRITE_CARET_METRICS
{
    INT16 slopeRise;
    INT16 slopeRun;
    INT16 offset;
} DWRITE_CARET_METRICS;

typedef union DWRITE_PANOSE
{
    UINT8 values[10];
    UINT8 familyKind;
    struct
    {
        UINT8 familyKind;
        UINT8 serifStyle;
        UINT8 weight;
        UINT8 proportion;
        UINT8 contrast;
        UINT8 strokeVariation;
        UINT8 armStyle;
        UINT8 letterform;
        UINT8 midline;
        UINT8 xHeight;
    } text;
    struct
    {
        UINT8 familyKind;
        UINT8 toolKind;
        UINT8 weight;
        UINT8 spacing;
        UINT8 aspectRatio;
        UINT8 contrast;
        UINT8 scriptTopology;
        UINT8 scriptForm;
        UINT8 finials;
        UINT8 xAscent;
    } script;
    struct
    {
        UINT8 familyKind;
        UINT8 decorativeClass;
        UINT8 weight;
        UINT8 aspect;
        UINT8 contrast;
        UINT8 serifVariant;
        UINT8 fill;
        UINT8 lining;
        UINT8 decorativeTopology;
        UINT8 characterRange;
    } decorative;
    struct
    {
        UINT8 familyKind;
        UINT8 symbolKind;
        UINT8 weight;
        UINT8 spacing;
        UINT8 aspectRatioAndContrast;
        UINT8 aspectRatio94;
        UINT8 aspectRatio119;
        UINT8 aspectRatio157;
        UINT8 aspectRatio163;
        UINT8 aspectRatio211;
    } symbol;
} DWRITE_PANOSE;

typedef struct DWRITE_UNICODE_RANGE
{
    UINT32 first;
    UINT32 last;
} DWRITE_UNICODE_RANGE;

typedef struct DWRITE_SCRIPT_PROPERTIES
{
    UINT32 isoScriptCode;
    UINT32 isoScriptNumber;
    UINT32 clusterLookahead;
    UINT32 justificationCharacter;
    UINT32 restrictCaretToClusters : 1;
    UINT32 usesWordDividers : 1;
    UINT32 isDiscreteWriting : 1;
    UINT32 isBlockWriting : 1;
    UINT32 isDistributedWithinCluster : 1;
    UINT32 isConnectedWriting : 1;
    UINT32 isCursiveWriting : 1;
    UINT32 reserved : 25;
} DWRITE_SCRIPT_PROPERTIES;

typedef struct DWRITE_JUSTIFICATION_OPPORTUNITY
{
    FLOAT expansionMinimum;
    FLOAT expansionMaximum;
    FLOAT compressionMaximum;
    UINT32 expansionPriority : 8;
    UINT32 compressionPriority : 8;
    UINT32 allowResidualExpansion : 1;
    UINT32 allowResidualCompression : 1;
    UINT32 applyToLeadingEdge : 1;
    UINT32 applyToTrailingEdge : 1;
    UINT32 reserved : 12;
} DWRITE_JUSTIFICATION_OPPORTUNITY;

interface IDWriteTextAnalysisSource1;
interface IDWriteTextAnalysisSink1;
interface IDWriteRenderingParams1;

[
local,
object,
uuid(30572f99-dac6-41db-a16e-0486307e606a)
]
interface IDWriteFactory1 : IDWriteFactory
{
    HRESULT GetEudcFontCollection(
        [out] IDWriteFontCollection **collection,
        [in, defaultvalue(FALSE)] BOOL check_for_updates
    );
    HRESULT CreateCustomRenderingParams(
        [in] FLOAT gamma,
        [in] FLOAT enhcontrast,
        [in] FLOAT enhcontrast_grayscale,
        [in] FLOAT cleartype_level,
        [in] DWRITE_PIXEL_GEOMETRY geometry,
        [in] DWRITE_RENDERING_MODE mode,
        [out] IDWriteRenderingParams1 **params
    );
}

[
local,
object,
uuid(a71efdb4-9fdb-4838-ad90-cfc3be8c3daf)
]
interface IDWriteFontFace1 : IDWriteFontFace
{
    void GetMetrics(
        [out] DWRITE_FONT_METRICS1 *metrics
    );
    HRESULT GetGdiCompatibleMetrics(
        [in] FLOAT em_size,
        [in] FLOAT pixels_per_dip,
        [in] const DWRITE_MATRIX *transform,
        [out] DWRITE_FONT_METRICS1 *metrics
    );
    void GetCaretMetrics(
        [out] DWRITE_CARET_METRICS *metrics
    );
    HRESULT GetUnicodeRanges(
        [in] UINT32 max_count,
        [out] DWRITE_UNICODE_RANGE *ranges,
        [out] UINT32 *count
    );
    BOOL IsMonospacedFont();
    HRESULT GetDesignGlyphAdvances(
        [in] UINT32 glyph_count,
        [in] UINT16 const *indices,
        [out] INT32 *advances,
        [in, defaultvalue(FALSE)] BOOL is_sideways
    );
    HRESULT GetGdiCompatibleGlyphAdvances(
        [in] FLOAT em_size,
        [in] FLOAT pixels_per_dip,
        [in] const DWRITE_MATRIX *transform,
        [in] BOOL use_gdi_natural,
        [in] BOOL is_sideways,
        [in] UINT32 glyph_count,
        [in] const UINT16 *indices,
        [out] INT32 *advances
    );
    HRESULT GetKerningPairAdjustments(
        [in] UINT32 glyph_count,
        [in] const UINT16 *indices,
        [out] INT32 *adjustments
    );
    BOOL HasKerningPairs();
    HRESULT GetRecommendedRenderingMode(
        [in] FLOAT font_emsize,
        [in] FLOAT dpiX,
        [in] FLOAT dpiY,
        [in] const DWRITE_MATRIX *transform,
        [in] BOOL is_sideways,
        [in] DWRITE_OUTLINE_THRESHOLD threshold,
        [in] DWRITE_MEASURING_MODE measuring_mode,
        [out] DWRITE_RENDERING_MODE *rendering_mode
    );
    HRESULT GetVerticalGlyphVariants(
        [in] UINT32 glyph_count,
        [in] const UINT16 *nominal_indices,
        [out] UINT16 *vertical_indices
    );
    BOOL HasVerticalGlyphVariants();
}

[
local,
object,
uuid(acd16696-8c14-4f5d-877e-fe3fc1d32738)
]
interface IDWriteFont1 : IDWriteFont
{
    void GetMetrics(
        [out] DWRITE_FONT_METRICS1 *metrics
    );
    void GetPanose(
        [out] DWRITE_PANOSE *panose
    );
    HRESULT GetUnicodeRanges(
        [in] UINT32 max_count,
        [out] DWRITE_UNICODE_RANGE *ranges,
        [out] UINT32 *count
    );
    BOOL IsMonospacedFont();
}

[
local,
object,
uuid(94413cf4-a6fc-4248-8b50-6674348fcad3)
]
interface IDWriteRenderingParams1 : IDWriteRenderingParams
{
    FLOAT GetGrayscaleEnhancedContrast();
}

[
local,
object,
uuid(80dad800-e21f-4e83-96ce-bfcce500db7c)
]
interface IDWriteTextAnalyzer1 : IDWriteTextAnalyzer
{
    HRESULT ApplyCharacterSpacing(
        [in] FLOAT leading_spacing,
        [in] FLOAT trailing_spacing,
        [in] FLOAT min_advance_width,
        [in] UINT32 len,
        [in] UINT32 glyph_count,
        [in] UINT16 const *clustermap,
        [in] FLOAT const *advances,
        [in] DWRITE_GLYPH_OFFSET const *offsets,
        [in] DWRITE_SHAPING_GLYPH_PROPERTIES const *props,
        [out] FLOAT *modified_advances,
        [out] DWRITE_GLYPH_OFFSET *modified_offsets
    );
    HRESULT GetBaseline(
        [in] IDWriteFontFace *face,
        [in] DWRITE_BASELINE baseline,
        [in] BOOL vertical,
        [in] BOOL is_simulation_allowed,
        [in] DWRITE_SCRIPT_ANALYSIS sa,
        [in] const WCHAR *localeName,
        [out] INT32 *baseline_coord,
        [out] BOOL *exists
    );
    HRESULT AnalyzeVerticalGlyphOrientation(
        [in] IDWriteTextAnalysisSource1 *source,
        [in] UINT32 text_pos,
        [in] UINT32 len,
        [out] IDWriteTextAnalysisSink1 *sink
    );
    HRESULT GetGlyphOrientationTransform(
        [in] DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        [in] BOOL is_sideways,
        [out] DWRITE_MATRIX *transform
    );
    HRESULT GetScriptProperties(
        [in] DWRITE_SCRIPT_ANALYSIS sa,
        [out] DWRITE_SCRIPT_PROPERTIES *props
    );
    HRESULT GetTextComplexity(
        [in] const WCHAR *text,
        [in] UINT32 len,
        [in] IDWriteFontFace *face,
        [out] BOOL *is_simple,
        [out] UINT32 *len_read,
        [out] UINT16 *indices
    );
    HRESULT GetJustificationOpportunities(
        [in] IDWriteFontFace *face,
        [in] FLOAT font_em_size,
        [in] DWRITE_SCRIPT_ANALYSIS sa,
        [in] UINT32 length,
        [in] UINT32 glyph_count,
        [in] const WCHAR *text,
        [in] const UINT16 *clustermap,
        [in] const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,
        [out] DWRITE_JUSTIFICATION_OPPORTUNITY *jo
    );
    HRESULT JustifyGlyphAdvances(
        [in] FLOAT width,
        [in] UINT32 glyph_count,
        [in] const DWRITE_JUSTIFICATION_OPPORTUNITY *jo,
        [in] const FLOAT *advances,
        [in] const DWRITE_GLYPH_OFFSET *offsets,
        [out] FLOAT *justifiedadvances,
        [out] DWRITE_GLYPH_OFFSET *justifiedoffsets
    );
    HRESULT GetJustifiedGlyphs(
        [in] IDWriteFontFace *face,
        [in] FLOAT font_em_size,
        [in] DWRITE_SCRIPT_ANALYSIS sa,
        [in] UINT32 length,
        [in] UINT32 glyph_count,
        [in] UINT32 max_glyphcount,
        [in] const UINT16 *clustermap,
        [in] const UINT16 *indices,
        [in] const FLOAT *advances,
        [in] const FLOAT *justifiedadvances,
        [in] const DWRITE_GLYPH_OFFSET *justifiedoffsets,
        [in] const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,
        [out] UINT32 *actual_count,
        [out] UINT16 *modified_clustermap,
        [out] UINT16 *modified_indices,
        [out] FLOAT *modified_advances,
        [out] DWRITE_GLYPH_OFFSET *modified_offsets
    );
}

[
local,
object,
uuid(639cfad8-0fb4-4b21-a58a-067920120009)
]
interface IDWriteTextAnalysisSource1 : IDWriteTextAnalysisSource
{
    HRESULT GetVerticalGlyphOrientation(
        [in] UINT32 pos,
        [out] UINT32 *length,
        [out] DWRITE_VERTICAL_GLYPH_ORIENTATION *orientation,
        [out] UINT8 *bidi_level
    );
}

[
local,
object,
uuid(b0d941a0-85e7-4d8b-9fd3-5ced9934482a)
]
interface IDWriteTextAnalysisSink1 : IDWriteTextAnalysisSink
{
    HRESULT SetGlyphOrientation(
        [in] UINT32 pos,
        [in] UINT32 length,
        [in] DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        [in] UINT8 adjusted_bidilevel,
        [in] BOOL is_sideways,
        [in] BOOL is_rtl
    );
}

[
local,
object,
uuid(9064d822-80a7-465c-a986-df65f78b8feb)
]
interface IDWriteTextLayout1 : IDWriteTextLayout
{
    HRESULT SetPairKerning(
        [in] BOOL is_pairkerning_enabled,
        [in] DWRITE_TEXT_RANGE range);

    HRESULT GetPairKerning(
        [in] UINT32 position,
        [out] BOOL *is_pairkerning_enabled,
        [out] DWRITE_TEXT_RANGE *range);

    HRESULT SetCharacterSpacing(
        [in] FLOAT leading_spacing,
        [in] FLOAT trailing_spacing,
        [in] FLOAT minimum_advance_width,
        [in] DWRITE_TEXT_RANGE range);

    HRESULT GetCharacterSpacing(
        [in] UINT32 position,
        [out] FLOAT *leading_spacing,
        [out] FLOAT *trailing_spacing,
        [out] FLOAT *minimum_advance_width,
        [out, defaultvalue(NULL)] DWRITE_TEXT_RANGE *range
    );
}

typedef enum DWRITE_TEXT_ANTIALIAS_MODE
{
    DWRITE_TEXT_ANTIALIAS_MODE_CLEARTYPE,
    DWRITE_TEXT_ANTIALIAS_MODE_GRAYSCALE
} DWRITE_TEXT_ANTIALIAS_MODE;

[
local,
object,
uuid(791e8298-3ef3-4230-9880-c9bdecc42064)
]
interface IDWriteBitmapRenderTarget1 : IDWriteBitmapRenderTarget
{
    DWRITE_TEXT_ANTIALIAS_MODE GetTextAntialiasMode();
    HRESULT SetTextAntialiasMode(
        [in] DWRITE_TEXT_ANTIALIAS_MODE mode
    );
}
