/* Copyright (C) 2008-2025 Free Software Foundation, Inc.

   This file is part of GCC.

   GCC is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation; either version 3, or (at your option)
   any later version.

   GCC is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   Under Section 7 of GPL version 3, you are granted additional
   permissions described in the GCC Runtime Library Exception, version
   3.1, as published by the Free Software Foundation.

   You should have received a copy of the GNU General Public License and
   a copy of the GCC Runtime Library Exception along with this program;
   see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
   <http://www.gnu.org/licenses/>.  */

#ifndef _IMMINTRIN_H_INCLUDED
#define _IMMINTRIN_H_INCLUDED

#include <x86gprintrin.h>

#include <mmintrin.h>

#include <xmmintrin.h>

#include <emmintrin.h>

#include <pmmintrin.h>

#include <tmmintrin.h>

#include <smmintrin.h>

#include <wmmintrin.h>

#include <avxintrin.h>

#include <avxvnniintrin.h>

#include <avxifmaintrin.h>

#include <avxvnniint8intrin.h>

#include <avxvnniint16intrin.h>

#include <avx2intrin.h>

#include <avx512fintrin.h>

#include <avx512cdintrin.h>

#include <avx512vlintrin.h>

#include <avx512bwintrin.h>

#include <avx512dqintrin.h>

#include <avx512vlbwintrin.h>

#include <avx512vldqintrin.h>

#include <avx512ifmaintrin.h>

#include <avx512ifmavlintrin.h>

#include <avx512vbmiintrin.h>

#include <avx512vbmivlintrin.h>

#include <avx512vpopcntdqintrin.h>

#include <avx512vbmi2intrin.h>

#include <avx512vbmi2vlintrin.h>

#include <avx512vnniintrin.h>

#include <avx512vnnivlintrin.h>

#include <avx512vpopcntdqvlintrin.h>

#include <avx512bitalgintrin.h>

#include <avx512bitalgvlintrin.h>

#include <avx512vp2intersectintrin.h>

#include <avx512vp2intersectvlintrin.h>

#include <avx512fp16intrin.h>

#include <avx512fp16vlintrin.h>

#include <shaintrin.h>

#include <sm3intrin.h>

#include <sha512intrin.h>

#include <sm4intrin.h>

#include <fmaintrin.h>

#include <f16cintrin.h>

#include <rtmintrin.h>

#include <gfniintrin.h>

#include <vaesintrin.h>

#include <vpclmulqdqintrin.h>

#include <avx512bf16vlintrin.h>

#include <avx512bf16intrin.h>

#include <avxneconvertintrin.h>

#include <amxtileintrin.h>

#include <amxint8intrin.h>

#include <amxbf16intrin.h>

#include <amxcomplexintrin.h>

#include <amxavx512intrin.h>

#include <amxtf32intrin.h>

#include <amxtransposeintrin.h>

#include <amxfp8intrin.h>

#include <prfchwintrin.h>

#include <keylockerintrin.h>

#include <amxfp16intrin.h>

#include <avx10_2mediaintrin.h>

#include <avx10_2-512mediaintrin.h>

#include <avx10_2convertintrin.h>

#include <avx10_2-512convertintrin.h>

#include <avx10_2bf16intrin.h>

#include <avx10_2-512bf16intrin.h>

#include <avx10_2satcvtintrin.h>

#include <avx10_2-512satcvtintrin.h>

#include <avx10_2minmaxintrin.h>

#include <avx10_2-512minmaxintrin.h>

#include <avx10_2copyintrin.h>

#include <movrsintrin.h>

#include <amxmovrsintrin.h>
#endif /* _IMMINTRIN_H_INCLUDED */
