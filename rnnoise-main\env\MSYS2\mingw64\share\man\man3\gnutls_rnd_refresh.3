.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_rnd_refresh" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_rnd_refresh \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "void gnutls_rnd_refresh( " void ");"
.SH ARGUMENTS
.IP " void" 12
.SH "DESCRIPTION"

This function refreshes the random generator state.
That is the current precise time, CPU usage, and
other values are input into its state.

On a slower rate input from /dev/urandom is mixed too.
.SH "SINCE"
3.1.7
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
