.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_tpm_key_list_get_url" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_tpm_key_list_get_url \- API function
.SH SYNOPSIS
.B #include <gnutls/tpm.h>
.sp
.BI "int gnutls_tpm_key_list_get_url(gnutls_tpm_key_list_t " list ", unsigned int " idx ", char ** " url ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_tpm_key_list_t list" 12
a list of the keys
.IP "unsigned int idx" 12
The index of the key (starting from zero)
.IP "char ** url" 12
The URL to be returned
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will return for each given index a URL of
the corresponding key.
If the provided index is out of bounds then \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
is returned.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
