------------------------------------------------------------------------
-- ddCompare.decTest -- decDouble comparison that allows quiet NaNs   --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- Note that we cannot assume add/subtract tests cover paths adequately,
-- here, because the code might be quite different (comparison cannot
-- overflow or underflow, so actual subtractions are not necessary).

-- All operands and results are decDoubles.
precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- sanity checks
ddcom001 compare  -2  -2  -> 0
ddcom002 compare  -2  -1  -> -1
ddcom003 compare  -2   0  -> -1
ddcom004 compare  -2   1  -> -1
ddcom005 compare  -2   2  -> -1
ddcom006 compare  -1  -2  -> 1
ddcom007 compare  -1  -1  -> 0
ddcom008 compare  -1   0  -> -1
ddcom009 compare  -1   1  -> -1
ddcom010 compare  -1   2  -> -1
ddcom011 compare   0  -2  -> 1
ddcom012 compare   0  -1  -> 1
ddcom013 compare   0   0  -> 0
ddcom014 compare   0   1  -> -1
ddcom015 compare   0   2  -> -1
ddcom016 compare   1  -2  -> 1
ddcom017 compare   1  -1  -> 1
ddcom018 compare   1   0  -> 1
ddcom019 compare   1   1  -> 0
ddcom020 compare   1   2  -> -1
ddcom021 compare   2  -2  -> 1
ddcom022 compare   2  -1  -> 1
ddcom023 compare   2   0  -> 1
ddcom025 compare   2   1  -> 1
ddcom026 compare   2   2  -> 0

ddcom031 compare  -20  -20  -> 0
ddcom032 compare  -20  -10  -> -1
ddcom033 compare  -20   00  -> -1
ddcom034 compare  -20   10  -> -1
ddcom035 compare  -20   20  -> -1
ddcom036 compare  -10  -20  -> 1
ddcom037 compare  -10  -10  -> 0
ddcom038 compare  -10   00  -> -1
ddcom039 compare  -10   10  -> -1
ddcom040 compare  -10   20  -> -1
ddcom041 compare   00  -20  -> 1
ddcom042 compare   00  -10  -> 1
ddcom043 compare   00   00  -> 0
ddcom044 compare   00   10  -> -1
ddcom045 compare   00   20  -> -1
ddcom046 compare   10  -20  -> 1
ddcom047 compare   10  -10  -> 1
ddcom048 compare   10   00  -> 1
ddcom049 compare   10   10  -> 0
ddcom050 compare   10   20  -> -1
ddcom051 compare   20  -20  -> 1
ddcom052 compare   20  -10  -> 1
ddcom053 compare   20   00  -> 1
ddcom055 compare   20   10  -> 1
ddcom056 compare   20   20  -> 0

ddcom061 compare  -2.0  -2.0  -> 0
ddcom062 compare  -2.0  -1.0  -> -1
ddcom063 compare  -2.0   0.0  -> -1
ddcom064 compare  -2.0   1.0  -> -1
ddcom065 compare  -2.0   2.0  -> -1
ddcom066 compare  -1.0  -2.0  -> 1
ddcom067 compare  -1.0  -1.0  -> 0
ddcom068 compare  -1.0   0.0  -> -1
ddcom069 compare  -1.0   1.0  -> -1
ddcom070 compare  -1.0   2.0  -> -1
ddcom071 compare   0.0  -2.0  -> 1
ddcom072 compare   0.0  -1.0  -> 1
ddcom073 compare   0.0   0.0  -> 0
ddcom074 compare   0.0   1.0  -> -1
ddcom075 compare   0.0   2.0  -> -1
ddcom076 compare   1.0  -2.0  -> 1
ddcom077 compare   1.0  -1.0  -> 1
ddcom078 compare   1.0   0.0  -> 1
ddcom079 compare   1.0   1.0  -> 0
ddcom080 compare   1.0   2.0  -> -1
ddcom081 compare   2.0  -2.0  -> 1
ddcom082 compare   2.0  -1.0  -> 1
ddcom083 compare   2.0   0.0  -> 1
ddcom085 compare   2.0   1.0  -> 1
ddcom086 compare   2.0   2.0  -> 0
ddcom087 compare   1.0   0.1  -> 1
ddcom088 compare   0.1   1.0  -> -1

-- now some cases which might overflow if subtract were used
ddcom095 compare  9.999999999999999E+384 9.999999999999999E+384  -> 0
ddcom096 compare -9.999999999999999E+384 9.999999999999999E+384  -> -1
ddcom097 compare  9.999999999999999E+384 -9.999999999999999E+384 -> 1
ddcom098 compare -9.999999999999999E+384 -9.999999999999999E+384 -> 0

-- some differing length/exponent cases
ddcom100 compare   7.0    7.0    -> 0
ddcom101 compare   7.0    7      -> 0
ddcom102 compare   7      7.0    -> 0
ddcom103 compare   7E+0   7.0    -> 0
ddcom104 compare   70E-1  7.0    -> 0
ddcom105 compare   0.7E+1 7      -> 0
ddcom106 compare   70E-1  7      -> 0
ddcom107 compare   7.0    7E+0   -> 0
ddcom108 compare   7.0    70E-1  -> 0
ddcom109 compare   7      0.7E+1 -> 0
ddcom110 compare   7      70E-1  -> 0

ddcom120 compare   8.0    7.0    -> 1
ddcom121 compare   8.0    7      -> 1
ddcom122 compare   8      7.0    -> 1
ddcom123 compare   8E+0   7.0    -> 1
ddcom124 compare   80E-1  7.0    -> 1
ddcom125 compare   0.8E+1 7      -> 1
ddcom126 compare   80E-1  7      -> 1
ddcom127 compare   8.0    7E+0   -> 1
ddcom128 compare   8.0    70E-1  -> 1
ddcom129 compare   8      0.7E+1  -> 1
ddcom130 compare   8      70E-1  -> 1

ddcom140 compare   8.0    9.0    -> -1
ddcom141 compare   8.0    9      -> -1
ddcom142 compare   8      9.0    -> -1
ddcom143 compare   8E+0   9.0    -> -1
ddcom144 compare   80E-1  9.0    -> -1
ddcom145 compare   0.8E+1 9      -> -1
ddcom146 compare   80E-1  9      -> -1
ddcom147 compare   8.0    9E+0   -> -1
ddcom148 compare   8.0    90E-1  -> -1
ddcom149 compare   8      0.9E+1 -> -1
ddcom150 compare   8      90E-1  -> -1

-- and again, with sign changes -+ ..
ddcom200 compare  -7.0    7.0    -> -1
ddcom201 compare  -7.0    7      -> -1
ddcom202 compare  -7      7.0    -> -1
ddcom203 compare  -7E+0   7.0    -> -1
ddcom204 compare  -70E-1  7.0    -> -1
ddcom205 compare  -0.7E+1 7      -> -1
ddcom206 compare  -70E-1  7      -> -1
ddcom207 compare  -7.0    7E+0   -> -1
ddcom208 compare  -7.0    70E-1  -> -1
ddcom209 compare  -7      0.7E+1 -> -1
ddcom210 compare  -7      70E-1  -> -1

ddcom220 compare  -8.0    7.0    -> -1
ddcom221 compare  -8.0    7      -> -1
ddcom222 compare  -8      7.0    -> -1
ddcom223 compare  -8E+0   7.0    -> -1
ddcom224 compare  -80E-1  7.0    -> -1
ddcom225 compare  -0.8E+1 7      -> -1
ddcom226 compare  -80E-1  7      -> -1
ddcom227 compare  -8.0    7E+0   -> -1
ddcom228 compare  -8.0    70E-1  -> -1
ddcom229 compare  -8      0.7E+1 -> -1
ddcom230 compare  -8      70E-1  -> -1

ddcom240 compare  -8.0    9.0    -> -1
ddcom241 compare  -8.0    9      -> -1
ddcom242 compare  -8      9.0    -> -1
ddcom243 compare  -8E+0   9.0    -> -1
ddcom244 compare  -80E-1  9.0    -> -1
ddcom245 compare  -0.8E+1 9      -> -1
ddcom246 compare  -80E-1  9      -> -1
ddcom247 compare  -8.0    9E+0   -> -1
ddcom248 compare  -8.0    90E-1  -> -1
ddcom249 compare  -8      0.9E+1 -> -1
ddcom250 compare  -8      90E-1  -> -1

-- and again, with sign changes +- ..
ddcom300 compare   7.0    -7.0    -> 1
ddcom301 compare   7.0    -7      -> 1
ddcom302 compare   7      -7.0    -> 1
ddcom303 compare   7E+0   -7.0    -> 1
ddcom304 compare   70E-1  -7.0    -> 1
ddcom305 compare   .7E+1  -7      -> 1
ddcom306 compare   70E-1  -7      -> 1
ddcom307 compare   7.0    -7E+0   -> 1
ddcom308 compare   7.0    -70E-1  -> 1
ddcom309 compare   7      -.7E+1  -> 1
ddcom310 compare   7      -70E-1  -> 1

ddcom320 compare   8.0    -7.0    -> 1
ddcom321 compare   8.0    -7      -> 1
ddcom322 compare   8      -7.0    -> 1
ddcom323 compare   8E+0   -7.0    -> 1
ddcom324 compare   80E-1  -7.0    -> 1
ddcom325 compare   .8E+1  -7      -> 1
ddcom326 compare   80E-1  -7      -> 1
ddcom327 compare   8.0    -7E+0   -> 1
ddcom328 compare   8.0    -70E-1  -> 1
ddcom329 compare   8      -.7E+1  -> 1
ddcom330 compare   8      -70E-1  -> 1

ddcom340 compare   8.0    -9.0    -> 1
ddcom341 compare   8.0    -9      -> 1
ddcom342 compare   8      -9.0    -> 1
ddcom343 compare   8E+0   -9.0    -> 1
ddcom344 compare   80E-1  -9.0    -> 1
ddcom345 compare   .8E+1  -9      -> 1
ddcom346 compare   80E-1  -9      -> 1
ddcom347 compare   8.0    -9E+0   -> 1
ddcom348 compare   8.0    -90E-1  -> 1
ddcom349 compare   8      -.9E+1  -> 1
ddcom350 compare   8      -90E-1  -> 1

-- and again, with sign changes -- ..
ddcom400 compare   -7.0    -7.0    -> 0
ddcom401 compare   -7.0    -7      -> 0
ddcom402 compare   -7      -7.0    -> 0
ddcom403 compare   -7E+0   -7.0    -> 0
ddcom404 compare   -70E-1  -7.0    -> 0
ddcom405 compare   -.7E+1  -7      -> 0
ddcom406 compare   -70E-1  -7      -> 0
ddcom407 compare   -7.0    -7E+0   -> 0
ddcom408 compare   -7.0    -70E-1  -> 0
ddcom409 compare   -7      -.7E+1  -> 0
ddcom410 compare   -7      -70E-1  -> 0

ddcom420 compare   -8.0    -7.0    -> -1
ddcom421 compare   -8.0    -7      -> -1
ddcom422 compare   -8      -7.0    -> -1
ddcom423 compare   -8E+0   -7.0    -> -1
ddcom424 compare   -80E-1  -7.0    -> -1
ddcom425 compare   -.8E+1  -7      -> -1
ddcom426 compare   -80E-1  -7      -> -1
ddcom427 compare   -8.0    -7E+0   -> -1
ddcom428 compare   -8.0    -70E-1  -> -1
ddcom429 compare   -8      -.7E+1  -> -1
ddcom430 compare   -8      -70E-1  -> -1

ddcom440 compare   -8.0    -9.0    -> 1
ddcom441 compare   -8.0    -9      -> 1
ddcom442 compare   -8      -9.0    -> 1
ddcom443 compare   -8E+0   -9.0    -> 1
ddcom444 compare   -80E-1  -9.0    -> 1
ddcom445 compare   -.8E+1  -9      -> 1
ddcom446 compare   -80E-1  -9      -> 1
ddcom447 compare   -8.0    -9E+0   -> 1
ddcom448 compare   -8.0    -90E-1  -> 1
ddcom449 compare   -8      -.9E+1  -> 1
ddcom450 compare   -8      -90E-1  -> 1

-- misalignment traps for little-endian
ddcom451 compare      1.0       0.1  -> 1
ddcom452 compare      0.1       1.0  -> -1
ddcom453 compare     10.0       0.1  -> 1
ddcom454 compare      0.1      10.0  -> -1
ddcom455 compare      100       1.0  -> 1
ddcom456 compare      1.0       100  -> -1
ddcom457 compare     1000      10.0  -> 1
ddcom458 compare     10.0      1000  -> -1
ddcom459 compare    10000     100.0  -> 1
ddcom460 compare    100.0     10000  -> -1
ddcom461 compare   100000    1000.0  -> 1
ddcom462 compare   1000.0    100000  -> -1
ddcom463 compare  1000000   10000.0  -> 1
ddcom464 compare  10000.0   1000000  -> -1

-- testcases that subtract to lots of zeros at boundaries [pgr]
ddcom473 compare 123.4560000000000E-89 123.456E-89 -> 0
ddcom474 compare 123.456000000000E+89 123.456E+89 -> 0
ddcom475 compare 123.45600000000E-89 123.456E-89 -> 0
ddcom476 compare 123.4560000000E+89 123.456E+89 -> 0
ddcom477 compare 123.456000000E-89 123.456E-89 -> 0
ddcom478 compare 123.45600000E+89 123.456E+89 -> 0
ddcom479 compare 123.4560000E-89 123.456E-89 -> 0
ddcom480 compare 123.456000E+89 123.456E+89 -> 0
ddcom481 compare 123.45600E-89 123.456E-89 -> 0
ddcom482 compare 123.4560E+89 123.456E+89 -> 0
ddcom483 compare 123.456E-89 123.456E-89 -> 0
ddcom487 compare 123.456E+89 123.4560000000000E+89 -> 0
ddcom488 compare 123.456E-89 123.456000000000E-89 -> 0
ddcom489 compare 123.456E+89 123.45600000000E+89 -> 0
ddcom490 compare 123.456E-89 123.4560000000E-89 -> 0
ddcom491 compare 123.456E+89 123.456000000E+89 -> 0
ddcom492 compare 123.456E-89 123.45600000E-89 -> 0
ddcom493 compare 123.456E+89 123.4560000E+89 -> 0
ddcom494 compare 123.456E-89 123.456000E-89 -> 0
ddcom495 compare 123.456E+89 123.45600E+89 -> 0
ddcom496 compare 123.456E-89 123.4560E-89 -> 0
ddcom497 compare 123.456E+89 123.456E+89 -> 0

-- wide-ranging, around precision; signs equal
ddcom500 compare    1     1E-15    -> 1
ddcom501 compare    1     1E-14    -> 1
ddcom502 compare    1     1E-13    -> 1
ddcom503 compare    1     1E-12    -> 1
ddcom504 compare    1     1E-11    -> 1
ddcom505 compare    1     1E-10    -> 1
ddcom506 compare    1     1E-9     -> 1
ddcom507 compare    1     1E-8     -> 1
ddcom508 compare    1     1E-7     -> 1
ddcom509 compare    1     1E-6     -> 1
ddcom510 compare    1     1E-5     -> 1
ddcom511 compare    1     1E-4     -> 1
ddcom512 compare    1     1E-3     -> 1
ddcom513 compare    1     1E-2     -> 1
ddcom514 compare    1     1E-1     -> 1
ddcom515 compare    1     1E-0     -> 0
ddcom516 compare    1     1E+1     -> -1
ddcom517 compare    1     1E+2     -> -1
ddcom518 compare    1     1E+3     -> -1
ddcom519 compare    1     1E+4     -> -1
ddcom521 compare    1     1E+5     -> -1
ddcom522 compare    1     1E+6     -> -1
ddcom523 compare    1     1E+7     -> -1
ddcom524 compare    1     1E+8     -> -1
ddcom525 compare    1     1E+9     -> -1
ddcom526 compare    1     1E+10    -> -1
ddcom527 compare    1     1E+11    -> -1
ddcom528 compare    1     1E+12    -> -1
ddcom529 compare    1     1E+13    -> -1
ddcom530 compare    1     1E+14    -> -1
ddcom531 compare    1     1E+15    -> -1
-- LR swap
ddcom540 compare    1E-15  1       -> -1
ddcom541 compare    1E-14  1       -> -1
ddcom542 compare    1E-13  1       -> -1
ddcom543 compare    1E-12  1       -> -1
ddcom544 compare    1E-11  1       -> -1
ddcom545 compare    1E-10  1       -> -1
ddcom546 compare    1E-9   1       -> -1
ddcom547 compare    1E-8   1       -> -1
ddcom548 compare    1E-7   1       -> -1
ddcom549 compare    1E-6   1       -> -1
ddcom550 compare    1E-5   1       -> -1
ddcom551 compare    1E-4   1       -> -1
ddcom552 compare    1E-3   1       -> -1
ddcom553 compare    1E-2   1       -> -1
ddcom554 compare    1E-1   1       -> -1
ddcom555 compare    1E-0   1       ->  0
ddcom556 compare    1E+1   1       ->  1
ddcom557 compare    1E+2   1       ->  1
ddcom558 compare    1E+3   1       ->  1
ddcom559 compare    1E+4   1       ->  1
ddcom561 compare    1E+5   1       ->  1
ddcom562 compare    1E+6   1       ->  1
ddcom563 compare    1E+7   1       ->  1
ddcom564 compare    1E+8   1       ->  1
ddcom565 compare    1E+9   1       ->  1
ddcom566 compare    1E+10  1       ->  1
ddcom567 compare    1E+11  1       ->  1
ddcom568 compare    1E+12  1       ->  1
ddcom569 compare    1E+13  1       ->  1
ddcom570 compare    1E+14  1       ->  1
ddcom571 compare    1E+15  1       ->  1
-- similar with a useful coefficient, one side only
ddcom580 compare  0.000000987654321     1E-15    -> 1
ddcom581 compare  0.000000987654321     1E-14    -> 1
ddcom582 compare  0.000000987654321     1E-13    -> 1
ddcom583 compare  0.000000987654321     1E-12    -> 1
ddcom584 compare  0.000000987654321     1E-11    -> 1
ddcom585 compare  0.000000987654321     1E-10    -> 1
ddcom586 compare  0.000000987654321     1E-9     -> 1
ddcom587 compare  0.000000987654321     1E-8     -> 1
ddcom588 compare  0.000000987654321     1E-7     -> 1
ddcom589 compare  0.000000987654321     1E-6     -> -1
ddcom590 compare  0.000000987654321     1E-5     -> -1
ddcom591 compare  0.000000987654321     1E-4     -> -1
ddcom592 compare  0.000000987654321     1E-3     -> -1
ddcom593 compare  0.000000987654321     1E-2     -> -1
ddcom594 compare  0.000000987654321     1E-1     -> -1
ddcom595 compare  0.000000987654321     1E-0     -> -1
ddcom596 compare  0.000000987654321     1E+1     -> -1
ddcom597 compare  0.000000987654321     1E+2     -> -1
ddcom598 compare  0.000000987654321     1E+3     -> -1
ddcom599 compare  0.000000987654321     1E+4     -> -1

-- check some unit-y traps
ddcom600 compare   12            12.2345 -> -1
ddcom601 compare   12.0          12.2345 -> -1
ddcom602 compare   12.00         12.2345 -> -1
ddcom603 compare   12.000        12.2345 -> -1
ddcom604 compare   12.0000       12.2345 -> -1
ddcom605 compare   12.00000      12.2345 -> -1
ddcom606 compare   12.000000     12.2345 -> -1
ddcom607 compare   12.0000000    12.2345 -> -1
ddcom608 compare   12.00000000   12.2345 -> -1
ddcom609 compare   12.000000000  12.2345 -> -1
ddcom610 compare   12.1234 12            ->  1
ddcom611 compare   12.1234 12.0          ->  1
ddcom612 compare   12.1234 12.00         ->  1
ddcom613 compare   12.1234 12.000        ->  1
ddcom614 compare   12.1234 12.0000       ->  1
ddcom615 compare   12.1234 12.00000      ->  1
ddcom616 compare   12.1234 12.000000     ->  1
ddcom617 compare   12.1234 12.0000000    ->  1
ddcom618 compare   12.1234 12.00000000   ->  1
ddcom619 compare   12.1234 12.000000000  ->  1
ddcom620 compare  -12           -12.2345 ->  1
ddcom621 compare  -12.0         -12.2345 ->  1
ddcom622 compare  -12.00        -12.2345 ->  1
ddcom623 compare  -12.000       -12.2345 ->  1
ddcom624 compare  -12.0000      -12.2345 ->  1
ddcom625 compare  -12.00000     -12.2345 ->  1
ddcom626 compare  -12.000000    -12.2345 ->  1
ddcom627 compare  -12.0000000   -12.2345 ->  1
ddcom628 compare  -12.00000000  -12.2345 ->  1
ddcom629 compare  -12.000000000 -12.2345 ->  1
ddcom630 compare  -12.1234 -12           -> -1
ddcom631 compare  -12.1234 -12.0         -> -1
ddcom632 compare  -12.1234 -12.00        -> -1
ddcom633 compare  -12.1234 -12.000       -> -1
ddcom634 compare  -12.1234 -12.0000      -> -1
ddcom635 compare  -12.1234 -12.00000     -> -1
ddcom636 compare  -12.1234 -12.000000    -> -1
ddcom637 compare  -12.1234 -12.0000000   -> -1
ddcom638 compare  -12.1234 -12.00000000  -> -1
ddcom639 compare  -12.1234 -12.000000000 -> -1

-- extended zeros
ddcom640 compare   0     0   -> 0
ddcom641 compare   0    -0   -> 0
ddcom642 compare   0    -0.0 -> 0
ddcom643 compare   0     0.0 -> 0
ddcom644 compare  -0     0   -> 0
ddcom645 compare  -0    -0   -> 0
ddcom646 compare  -0    -0.0 -> 0
ddcom647 compare  -0     0.0 -> 0
ddcom648 compare   0.0   0   -> 0
ddcom649 compare   0.0  -0   -> 0
ddcom650 compare   0.0  -0.0 -> 0
ddcom651 compare   0.0   0.0 -> 0
ddcom652 compare  -0.0   0   -> 0
ddcom653 compare  -0.0  -0   -> 0
ddcom654 compare  -0.0  -0.0 -> 0
ddcom655 compare  -0.0   0.0 -> 0

ddcom656 compare  -0E1   0.0 -> 0
ddcom657 compare  -0E2   0.0 -> 0
ddcom658 compare   0E1   0.0 -> 0
ddcom659 compare   0E2   0.0 -> 0
ddcom660 compare  -0E1   0   -> 0
ddcom661 compare  -0E2   0   -> 0
ddcom662 compare   0E1   0   -> 0
ddcom663 compare   0E2   0   -> 0
ddcom664 compare  -0E1  -0E1 -> 0
ddcom665 compare  -0E2  -0E1 -> 0
ddcom666 compare   0E1  -0E1 -> 0
ddcom667 compare   0E2  -0E1 -> 0
ddcom668 compare  -0E1  -0E2 -> 0
ddcom669 compare  -0E2  -0E2 -> 0
ddcom670 compare   0E1  -0E2 -> 0
ddcom671 compare   0E2  -0E2 -> 0
ddcom672 compare  -0E1   0E1 -> 0
ddcom673 compare  -0E2   0E1 -> 0
ddcom674 compare   0E1   0E1 -> 0
ddcom675 compare   0E2   0E1 -> 0
ddcom676 compare  -0E1   0E2 -> 0
ddcom677 compare  -0E2   0E2 -> 0
ddcom678 compare   0E1   0E2 -> 0
ddcom679 compare   0E2   0E2 -> 0

-- trailing zeros; unit-y
ddcom680 compare   12    12           -> 0
ddcom681 compare   12    12.0         -> 0
ddcom682 compare   12    12.00        -> 0
ddcom683 compare   12    12.000       -> 0
ddcom684 compare   12    12.0000      -> 0
ddcom685 compare   12    12.00000     -> 0
ddcom686 compare   12    12.000000    -> 0
ddcom687 compare   12    12.0000000   -> 0
ddcom688 compare   12    12.00000000  -> 0
ddcom689 compare   12    12.000000000 -> 0
ddcom690 compare   12              12 -> 0
ddcom691 compare   12.0            12 -> 0
ddcom692 compare   12.00           12 -> 0
ddcom693 compare   12.000          12 -> 0
ddcom694 compare   12.0000         12 -> 0
ddcom695 compare   12.00000        12 -> 0
ddcom696 compare   12.000000       12 -> 0
ddcom697 compare   12.0000000      12 -> 0
ddcom698 compare   12.00000000     12 -> 0
ddcom699 compare   12.000000000    12 -> 0

-- first, second, & last digit
ddcom700 compare   1234567890123456 1234567890123455 -> 1
ddcom701 compare   1234567890123456 1234567890123456 -> 0
ddcom702 compare   1234567890123456 1234567890123457 -> -1
ddcom703 compare   1234567890123456 0234567890123456 -> 1
ddcom704 compare   1234567890123456 1234567890123456 -> 0
ddcom705 compare   1234567890123456 2234567890123456 -> -1
ddcom706 compare   1134567890123456 1034567890123456 -> 1
ddcom707 compare   1134567890123456 1134567890123456 -> 0
ddcom708 compare   1134567890123456 1234567890123456 -> -1

-- miscellaneous
ddcom721 compare 12345678000 1 -> 1
ddcom722 compare 1 12345678000 -> -1
ddcom723 compare 1234567800  1 -> 1
ddcom724 compare 1 1234567800  -> -1
ddcom725 compare 1234567890  1 -> 1
ddcom726 compare 1 1234567890  -> -1
ddcom727 compare 1234567891  1 -> 1
ddcom728 compare 1 1234567891  -> -1
ddcom729 compare 12345678901 1 -> 1
ddcom730 compare 1 12345678901 -> -1
ddcom731 compare 1234567896  1 -> 1
ddcom732 compare 1 1234567896  -> -1

-- residue cases at lower precision
ddcom740 compare  1  0.9999999  -> 1
ddcom741 compare  1  0.999999   -> 1
ddcom742 compare  1  0.99999    -> 1
ddcom743 compare  1  1.0000     -> 0
ddcom744 compare  1  1.00001    -> -1
ddcom745 compare  1  1.000001   -> -1
ddcom746 compare  1  1.0000001  -> -1
ddcom750 compare  0.9999999  1  -> -1
ddcom751 compare  0.999999   1  -> -1
ddcom752 compare  0.99999    1  -> -1
ddcom753 compare  1.0000     1  -> 0
ddcom754 compare  1.00001    1  -> 1
ddcom755 compare  1.000001   1  -> 1
ddcom756 compare  1.0000001  1  -> 1

-- Specials
ddcom780 compare  Inf  -Inf   ->  1
ddcom781 compare  Inf  -1000  ->  1
ddcom782 compare  Inf  -1     ->  1
ddcom783 compare  Inf  -0     ->  1
ddcom784 compare  Inf   0     ->  1
ddcom785 compare  Inf   1     ->  1
ddcom786 compare  Inf   1000  ->  1
ddcom787 compare  Inf   Inf   ->  0
ddcom788 compare -1000  Inf   -> -1
ddcom789 compare -Inf   Inf   -> -1
ddcom790 compare -1     Inf   -> -1
ddcom791 compare -0     Inf   -> -1
ddcom792 compare  0     Inf   -> -1
ddcom793 compare  1     Inf   -> -1
ddcom794 compare  1000  Inf   -> -1
ddcom795 compare  Inf   Inf   ->  0

ddcom800 compare -Inf  -Inf   ->  0
ddcom801 compare -Inf  -1000  -> -1
ddcom802 compare -Inf  -1     -> -1
ddcom803 compare -Inf  -0     -> -1
ddcom804 compare -Inf   0     -> -1
ddcom805 compare -Inf   1     -> -1
ddcom806 compare -Inf   1000  -> -1
ddcom807 compare -Inf   Inf   -> -1
ddcom808 compare -Inf  -Inf   ->  0
ddcom809 compare -1000 -Inf   ->  1
ddcom810 compare -1    -Inf   ->  1
ddcom811 compare -0    -Inf   ->  1
ddcom812 compare  0    -Inf   ->  1
ddcom813 compare  1    -Inf   ->  1
ddcom814 compare  1000 -Inf   ->  1
ddcom815 compare  Inf  -Inf   ->  1

ddcom821 compare  NaN -Inf    ->  NaN
ddcom822 compare  NaN -1000   ->  NaN
ddcom823 compare  NaN -1      ->  NaN
ddcom824 compare  NaN -0      ->  NaN
ddcom825 compare  NaN  0      ->  NaN
ddcom826 compare  NaN  1      ->  NaN
ddcom827 compare  NaN  1000   ->  NaN
ddcom828 compare  NaN  Inf    ->  NaN
ddcom829 compare  NaN  NaN    ->  NaN
ddcom830 compare -Inf  NaN    ->  NaN
ddcom831 compare -1000 NaN    ->  NaN
ddcom832 compare -1    NaN    ->  NaN
ddcom833 compare -0    NaN    ->  NaN
ddcom834 compare  0    NaN    ->  NaN
ddcom835 compare  1    NaN    ->  NaN
ddcom836 compare  1000 NaN    ->  NaN
ddcom837 compare  Inf  NaN    ->  NaN
ddcom838 compare -NaN -NaN    -> -NaN
ddcom839 compare +NaN -NaN    ->  NaN
ddcom840 compare -NaN +NaN    -> -NaN

ddcom841 compare  sNaN -Inf   ->  NaN  Invalid_operation
ddcom842 compare  sNaN -1000  ->  NaN  Invalid_operation
ddcom843 compare  sNaN -1     ->  NaN  Invalid_operation
ddcom844 compare  sNaN -0     ->  NaN  Invalid_operation
ddcom845 compare  sNaN  0     ->  NaN  Invalid_operation
ddcom846 compare  sNaN  1     ->  NaN  Invalid_operation
ddcom847 compare  sNaN  1000  ->  NaN  Invalid_operation
ddcom848 compare  sNaN  NaN   ->  NaN  Invalid_operation
ddcom849 compare  sNaN sNaN   ->  NaN  Invalid_operation
ddcom850 compare  NaN  sNaN   ->  NaN  Invalid_operation
ddcom851 compare -Inf  sNaN   ->  NaN  Invalid_operation
ddcom852 compare -1000 sNaN   ->  NaN  Invalid_operation
ddcom853 compare -1    sNaN   ->  NaN  Invalid_operation
ddcom854 compare -0    sNaN   ->  NaN  Invalid_operation
ddcom855 compare  0    sNaN   ->  NaN  Invalid_operation
ddcom856 compare  1    sNaN   ->  NaN  Invalid_operation
ddcom857 compare  1000 sNaN   ->  NaN  Invalid_operation
ddcom858 compare  Inf  sNaN   ->  NaN  Invalid_operation
ddcom859 compare  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
ddcom860 compare  NaN9 -Inf   ->  NaN9
ddcom861 compare  NaN8  999   ->  NaN8
ddcom862 compare  NaN77 Inf   ->  NaN77
ddcom863 compare -NaN67 NaN5  -> -NaN67
ddcom864 compare -Inf  -NaN4  -> -NaN4
ddcom865 compare -999  -NaN33 -> -NaN33
ddcom866 compare  Inf   NaN2  ->  NaN2
ddcom867 compare -NaN41 -NaN42 -> -NaN41
ddcom868 compare +NaN41 -NaN42 ->  NaN41
ddcom869 compare -NaN41 +NaN42 -> -NaN41
ddcom870 compare +NaN41 +NaN42 ->  NaN41

ddcom871 compare -sNaN99 -Inf    -> -NaN99 Invalid_operation
ddcom872 compare  sNaN98 -11     ->  NaN98 Invalid_operation
ddcom873 compare  sNaN97  NaN    ->  NaN97 Invalid_operation
ddcom874 compare  sNaN16 sNaN94  ->  NaN16 Invalid_operation
ddcom875 compare  NaN85  sNaN83  ->  NaN83 Invalid_operation
ddcom876 compare -Inf    sNaN92  ->  NaN92 Invalid_operation
ddcom877 compare  088    sNaN81  ->  NaN81 Invalid_operation
ddcom878 compare  Inf    sNaN90  ->  NaN90 Invalid_operation
ddcom879 compare  NaN   -sNaN89  -> -NaN89 Invalid_operation

-- wide range
ddcom880 compare +1.23456789012345E-0 9E+384 -> -1
ddcom881 compare 9E+384 +1.23456789012345E-0 ->  1
ddcom882 compare +0.100 9E-383               ->  1
ddcom883 compare 9E-383 +0.100               -> -1
ddcom885 compare -1.23456789012345E-0 9E+384 -> -1
ddcom886 compare 9E+384 -1.23456789012345E-0 ->  1
ddcom887 compare -0.100 9E-383               -> -1
ddcom888 compare 9E-383 -0.100               ->  1

-- spread zeros
ddcom900 compare   0E-383  0       ->  0
ddcom901 compare   0E-383 -0       ->  0
ddcom902 compare  -0E-383  0       ->  0
ddcom903 compare  -0E-383 -0       ->  0
ddcom904 compare   0E-383  0E+384  ->  0
ddcom905 compare   0E-383 -0E+384  ->  0
ddcom906 compare  -0E-383  0E+384  ->  0
ddcom907 compare  -0E-383 -0E+384  ->  0
ddcom908 compare   0       0E+384  ->  0
ddcom909 compare   0      -0E+384  ->  0
ddcom910 compare  -0       0E+384  ->  0
ddcom911 compare  -0      -0E+384  ->  0
ddcom930 compare   0E+384  0       ->  0
ddcom931 compare   0E+384 -0       ->  0
ddcom932 compare  -0E+384  0       ->  0
ddcom933 compare  -0E+384 -0       ->  0
ddcom934 compare   0E+384  0E-383  ->  0
ddcom935 compare   0E+384 -0E-383  ->  0
ddcom936 compare  -0E+384  0E-383  ->  0
ddcom937 compare  -0E+384 -0E-383  ->  0
ddcom938 compare   0       0E-383  ->  0
ddcom939 compare   0      -0E-383  ->  0
ddcom940 compare  -0       0E-383  ->  0
ddcom941 compare  -0      -0E-383  ->  0

-- signs
ddcom961 compare  1e+77  1e+11 ->  1
ddcom962 compare  1e+77 -1e+11 ->  1
ddcom963 compare -1e+77  1e+11 -> -1
ddcom964 compare -1e+77 -1e+11 -> -1
ddcom965 compare  1e-77  1e-11 -> -1
ddcom966 compare  1e-77 -1e-11 ->  1
ddcom967 compare -1e-77  1e-11 -> -1
ddcom968 compare -1e-77 -1e-11 ->  1

-- full alignment range, both ways
ddcomp1001 compare 1 1.000000000000000  -> 0
ddcomp1002 compare 1 1.00000000000000   -> 0
ddcomp1003 compare 1 1.0000000000000    -> 0
ddcomp1004 compare 1 1.000000000000     -> 0
ddcomp1005 compare 1 1.00000000000      -> 0
ddcomp1006 compare 1 1.0000000000       -> 0
ddcomp1007 compare 1 1.000000000        -> 0
ddcomp1008 compare 1 1.00000000         -> 0
ddcomp1009 compare 1 1.0000000          -> 0
ddcomp1010 compare 1 1.000000           -> 0
ddcomp1011 compare 1 1.00000            -> 0
ddcomp1012 compare 1 1.0000             -> 0
ddcomp1013 compare 1 1.000              -> 0
ddcomp1014 compare 1 1.00               -> 0
ddcomp1015 compare 1 1.0                -> 0
ddcomp1021 compare 1.000000000000000  1 -> 0
ddcomp1022 compare 1.00000000000000   1 -> 0
ddcomp1023 compare 1.0000000000000    1 -> 0
ddcomp1024 compare 1.000000000000     1 -> 0
ddcomp1025 compare 1.00000000000      1 -> 0
ddcomp1026 compare 1.0000000000       1 -> 0
ddcomp1027 compare 1.000000000        1 -> 0
ddcomp1028 compare 1.00000000         1 -> 0
ddcomp1029 compare 1.0000000          1 -> 0
ddcomp1030 compare 1.000000           1 -> 0
ddcomp1031 compare 1.00000            1 -> 0
ddcomp1032 compare 1.0000             1 -> 0
ddcomp1033 compare 1.000              1 -> 0
ddcomp1034 compare 1.00               1 -> 0
ddcomp1035 compare 1.0                1 -> 0

-- check MSD always detected non-zero
ddcomp1040 compare 0 0.000000000000000  -> 0
ddcomp1041 compare 0 1.000000000000000  -> -1
ddcomp1042 compare 0 2.000000000000000  -> -1
ddcomp1043 compare 0 3.000000000000000  -> -1
ddcomp1044 compare 0 4.000000000000000  -> -1
ddcomp1045 compare 0 5.000000000000000  -> -1
ddcomp1046 compare 0 6.000000000000000  -> -1
ddcomp1047 compare 0 7.000000000000000  -> -1
ddcomp1048 compare 0 8.000000000000000  -> -1
ddcomp1049 compare 0 9.000000000000000  -> -1
ddcomp1050 compare 0.000000000000000  0 -> 0
ddcomp1051 compare 1.000000000000000  0 -> 1
ddcomp1052 compare 2.000000000000000  0 -> 1
ddcomp1053 compare 3.000000000000000  0 -> 1
ddcomp1054 compare 4.000000000000000  0 -> 1
ddcomp1055 compare 5.000000000000000  0 -> 1
ddcomp1056 compare 6.000000000000000  0 -> 1
ddcomp1057 compare 7.000000000000000  0 -> 1
ddcomp1058 compare 8.000000000000000  0 -> 1
ddcomp1059 compare 9.000000000000000  0 -> 1

-- Null tests
ddcom9990 compare 10  # -> NaN Invalid_operation
ddcom9991 compare  # 10 -> NaN Invalid_operation
