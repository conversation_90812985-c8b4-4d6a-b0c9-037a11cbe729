/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef TAPI_H
#define TAPI_H

#include <_mingw_unicode.h>

#ifndef TAPI_CURRENT_VERSION
#define TAPI_CURRENT_VERSION 0x00030001
#endif

#include <windows.h>
#include <basetsd.h>
#include <oaidl.h>

#pragma pack(1)

#ifdef __cplusplus
extern "C" {
#endif

#define DECLARE_OPAQUE32(name) typedef DWORD name

  DECLARE_OPAQUE32(HCALL);
  typedef HCALL *LPHCALL;

  DECLARE_OPAQUE32(HCALLHUB);
  typedef HCALLHUB *LPHCALLHUB;

  DECLARE_OPAQUE32(HLINE);
  typedef HLINE *LPHLINE;

  DECLARE_OPAQUE32(HPHONE);
  typedef HPHONE *LPHPHONE;

  DECLARE_OPAQUE32(HLINEAPP);
  typedef HLINEAPP *LPHLINEAPP;

  DECLARE_OPAQUE32(HPHONEAPP);
  typedef HPHONEAPP *LPHPHONEAPP;

  DECLARE_OPAQUE32(HAGENTSESSION);
  typedef HAGENTSESSION *LPHAGENTSESSION;

  DECLARE_OPAQUE32(HAGENT);
  typedef HAGENT *LPHAGENT;

  DECLARE_OPAQUE32(HPRIVATECHANNEL);
  typedef HPRIVATECHANNEL *LPHPRIVATECHANNEL;

  typedef HICON *LPHICON;

  typedef void (CALLBACK *LINECALLBACK)(DWORD hDevice,DWORD dwMessage,DWORD_PTR dwInstance,DWORD_PTR dwParam1,DWORD_PTR dwParam2,DWORD_PTR dwParam3);
  typedef void (CALLBACK *PHONECALLBACK)(DWORD hDevice,DWORD dwMessage,DWORD_PTR dwInstance,DWORD_PTR dwParam1,DWORD_PTR dwParam2,DWORD_PTR dwParam3);

#define LINE_ADDRESSSTATE __MSABI_LONG(0)
#define LINE_CALLINFO __MSABI_LONG(1)
#define LINE_CALLSTATE __MSABI_LONG(2)
#define LINE_CLOSE __MSABI_LONG(3)
#define LINE_DEVSPECIFIC __MSABI_LONG(4)
#define LINE_DEVSPECIFICFEATURE __MSABI_LONG(5)
#define LINE_GATHERDIGITS __MSABI_LONG(6)
#define LINE_GENERATE __MSABI_LONG(7)
#define LINE_LINEDEVSTATE __MSABI_LONG(8)
#define LINE_MONITORDIGITS __MSABI_LONG(9)
#define LINE_MONITORMEDIA __MSABI_LONG(10)
#define LINE_MONITORTONE __MSABI_LONG(11)
#define LINE_REPLY __MSABI_LONG(12)
#define LINE_REQUEST __MSABI_LONG(13)
#define PHONE_BUTTON __MSABI_LONG(14)
#define PHONE_CLOSE __MSABI_LONG(15)
#define PHONE_DEVSPECIFIC __MSABI_LONG(16)
#define PHONE_REPLY __MSABI_LONG(17)
#define PHONE_STATE __MSABI_LONG(18)
#define LINE_CREATE __MSABI_LONG(19)
#define PHONE_CREATE __MSABI_LONG(20)
#define LINE_AGENTSPECIFIC __MSABI_LONG(21)
#define LINE_AGENTSTATUS __MSABI_LONG(22)
#define LINE_APPNEWCALL __MSABI_LONG(23)
#define LINE_PROXYREQUEST __MSABI_LONG(24)
#define LINE_REMOVE __MSABI_LONG(25)
#define PHONE_REMOVE __MSABI_LONG(26)

#define LINE_AGENTSESSIONSTATUS __MSABI_LONG(27)
#define LINE_QUEUESTATUS __MSABI_LONG(28)
#define LINE_AGENTSTATUSEX __MSABI_LONG(29)
#define LINE_GROUPSTATUS __MSABI_LONG(30)
#define LINE_PROXYSTATUS __MSABI_LONG(31)

#define LINE_APPNEWCALLHUB __MSABI_LONG(32)
#define LINE_CALLHUBCLOSE __MSABI_LONG(33)
#define LINE_DEVSPECIFICEX __MSABI_LONG(34)

#define INITIALIZE_NEGOTIATION __MSABI_LONG(0xFFFFFFFFU)

#define LINEADDRCAPFLAGS_FWDNUMRINGS 0x00000001
#define LINEADDRCAPFLAGS_PICKUPGROUPID 0x00000002
#define LINEADDRCAPFLAGS_SECURE 0x00000004
#define LINEADDRCAPFLAGS_BLOCKIDDEFAULT 0x00000008
#define LINEADDRCAPFLAGS_BLOCKIDOVERRIDE 0x00000010
#define LINEADDRCAPFLAGS_DIALED 0x00000020
#define LINEADDRCAPFLAGS_ORIGOFFHOOK 0x00000040
#define LINEADDRCAPFLAGS_DESTOFFHOOK 0x00000080
#define LINEADDRCAPFLAGS_FWDCONSULT 0x00000100
#define LINEADDRCAPFLAGS_SETUPCONFNULL 0x00000200
#define LINEADDRCAPFLAGS_AUTORECONNECT 0x00000400
#define LINEADDRCAPFLAGS_COMPLETIONID 0x00000800
#define LINEADDRCAPFLAGS_TRANSFERHELD 0x00001000
#define LINEADDRCAPFLAGS_TRANSFERMAKE 0x00002000
#define LINEADDRCAPFLAGS_CONFERENCEHELD 0x00004000
#define LINEADDRCAPFLAGS_CONFERENCEMAKE 0x00008000
#define LINEADDRCAPFLAGS_PARTIALDIAL 0x00010000
#define LINEADDRCAPFLAGS_FWDSTATUSVALID 0x00020000
#define LINEADDRCAPFLAGS_FWDINTEXTADDR 0x00040000
#define LINEADDRCAPFLAGS_FWDBUSYNAADDR 0x00080000
#define LINEADDRCAPFLAGS_ACCEPTTOALERT 0x00100000
#define LINEADDRCAPFLAGS_CONFDROP 0x00200000
#define LINEADDRCAPFLAGS_PICKUPCALLWAIT 0x00400000
#define LINEADDRCAPFLAGS_PREDICTIVEDIALER 0x00800000
#define LINEADDRCAPFLAGS_QUEUE 0x01000000
#define LINEADDRCAPFLAGS_ROUTEPOINT 0x02000000
#define LINEADDRCAPFLAGS_HOLDMAKESNEW 0x04000000
#define LINEADDRCAPFLAGS_NOINTERNALCALLS 0x08000000
#define LINEADDRCAPFLAGS_NOEXTERNALCALLS 0x10000000
#define LINEADDRCAPFLAGS_SETCALLINGID 0x20000000
#define LINEADDRCAPFLAGS_ACDGROUP 0x40000000
#define LINEADDRCAPFLAGS_NOPSTNADDRESSTRANSLATION 0x80000000

#define LINEADDRESSMODE_ADDRESSID 0x00000001
#define LINEADDRESSMODE_DIALABLEADDR 0x00000002

#define LINEADDRESSSHARING_PRIVATE 0x00000001
#define LINEADDRESSSHARING_BRIDGEDEXCL 0x00000002
#define LINEADDRESSSHARING_BRIDGEDNEW 0x00000004
#define LINEADDRESSSHARING_BRIDGEDSHARED 0x00000008
#define LINEADDRESSSHARING_MONITORED 0x00000010

#define LINEADDRESSSTATE_OTHER 0x00000001
#define LINEADDRESSSTATE_DEVSPECIFIC 0x00000002
#define LINEADDRESSSTATE_INUSEZERO 0x00000004
#define LINEADDRESSSTATE_INUSEONE 0x00000008
#define LINEADDRESSSTATE_INUSEMANY 0x00000010
#define LINEADDRESSSTATE_NUMCALLS 0x00000020
#define LINEADDRESSSTATE_FORWARD 0x00000040
#define LINEADDRESSSTATE_TERMINALS 0x00000080
#define LINEADDRESSSTATE_CAPSCHANGE 0x00000100

#define LINEADDRESSTYPE_PHONENUMBER 0x00000001
#define LINEADDRESSTYPE_SDP 0x00000002
#define LINEADDRESSTYPE_EMAILNAME 0x00000004
#define LINEADDRESSTYPE_DOMAINNAME 0x00000008
#define LINEADDRESSTYPE_IPADDRESS 0x00000010

#define LINEADDRFEATURE_FORWARD 0x00000001
#define LINEADDRFEATURE_MAKECALL 0x00000002
#define LINEADDRFEATURE_PICKUP 0x00000004
#define LINEADDRFEATURE_SETMEDIACONTROL 0x00000008
#define LINEADDRFEATURE_SETTERMINAL 0x00000010
#define LINEADDRFEATURE_SETUPCONF 0x00000020
#define LINEADDRFEATURE_UNCOMPLETECALL 0x00000040
#define LINEADDRFEATURE_UNPARK 0x00000080
#define LINEADDRFEATURE_PICKUPHELD 0x00000100
#define LINEADDRFEATURE_PICKUPGROUP 0x00000200
#define LINEADDRFEATURE_PICKUPDIRECT 0x00000400
#define LINEADDRFEATURE_PICKUPWAITING 0x00000800
#define LINEADDRFEATURE_FORWARDFWD 0x00001000
#define LINEADDRFEATURE_FORWARDDND 0x00002000

#define LINEAGENTFEATURE_SETAGENTGROUP 0x00000001
#define LINEAGENTFEATURE_SETAGENTSTATE 0x00000002
#define LINEAGENTFEATURE_SETAGENTACTIVITY 0x00000004
#define LINEAGENTFEATURE_AGENTSPECIFIC 0x00000008
#define LINEAGENTFEATURE_GETAGENTACTIVITYLIST 0x00000010
#define LINEAGENTFEATURE_GETAGENTGROUP 0x00000020

#define LINEAGENTSTATE_LOGGEDOFF 0x00000001
#define LINEAGENTSTATE_NOTREADY 0x00000002
#define LINEAGENTSTATE_READY 0x00000004
#define LINEAGENTSTATE_BUSYACD 0x00000008
#define LINEAGENTSTATE_BUSYINCOMING 0x00000010
#define LINEAGENTSTATE_BUSYOUTBOUND 0x00000020
#define LINEAGENTSTATE_BUSYOTHER 0x00000040
#define LINEAGENTSTATE_WORKINGAFTERCALL 0x00000080
#define LINEAGENTSTATE_UNKNOWN 0x00000100
#define LINEAGENTSTATE_UNAVAIL 0x00000200

#define LINEAGENTSTATUS_GROUP 0x00000001
#define LINEAGENTSTATUS_STATE 0x00000002
#define LINEAGENTSTATUS_NEXTSTATE 0x00000004
#define LINEAGENTSTATUS_ACTIVITY 0x00000008
#define LINEAGENTSTATUS_ACTIVITYLIST 0x00000010
#define LINEAGENTSTATUS_GROUPLIST 0x00000020
#define LINEAGENTSTATUS_CAPSCHANGE 0x00000040
#define LINEAGENTSTATUS_VALIDSTATES 0x00000080
#define LINEAGENTSTATUS_VALIDNEXTSTATES 0x00000100

#define LINEAGENTSTATEEX_NOTREADY 0x00000001
#define LINEAGENTSTATEEX_READY 0x00000002
#define LINEAGENTSTATEEX_BUSYACD 0x00000004
#define LINEAGENTSTATEEX_BUSYINCOMING 0x00000008
#define LINEAGENTSTATEEX_BUSYOUTGOING 0x00000010
#define LINEAGENTSTATEEX_UNKNOWN 0x00000020
#define LINEAGENTSTATEEX_RELEASED 0x00000040

#define LINEAGENTSTATUSEX_NEWAGENT 0x00000001
#define LINEAGENTSTATUSEX_STATE 0x00000002
#define LINEAGENTSTATUSEX_UPDATEINFO 0x00000004

#define LINEAGENTSESSIONSTATE_NOTREADY 0x00000001
#define LINEAGENTSESSIONSTATE_READY 0x00000002
#define LINEAGENTSESSIONSTATE_BUSYONCALL 0x00000004
#define LINEAGENTSESSIONSTATE_BUSYWRAPUP 0x00000008
#define LINEAGENTSESSIONSTATE_ENDED 0x00000010
#define LINEAGENTSESSIONSTATE_RELEASED 0x00000020

#define LINEAGENTSESSIONSTATUS_NEWSESSION 0x00000001
#define LINEAGENTSESSIONSTATUS_STATE 0x00000002
#define LINEAGENTSESSIONSTATUS_UPDATEINFO 0x00000004

#define LINEQUEUESTATUS_UPDATEINFO 0x00000001
#define LINEQUEUESTATUS_NEWQUEUE 0x00000002
#define LINEQUEUESTATUS_QUEUEREMOVED 0x00000004

#define LINEGROUPSTATUS_NEWGROUP 0x00000001
#define LINEGROUPSTATUS_GROUPREMOVED 0x00000002

#define LINEPROXYSTATUS_OPEN 0x00000001
#define LINEPROXYSTATUS_CLOSE 0x00000002
#define LINEPROXYSTATUS_ALLOPENFORACD 0x00000004

#define LINEANSWERMODE_NONE 0x00000001
#define LINEANSWERMODE_DROP 0x00000002
#define LINEANSWERMODE_HOLD 0x00000004

#define LINEBEARERMODE_VOICE 0x00000001
#define LINEBEARERMODE_SPEECH 0x00000002
#define LINEBEARERMODE_MULTIUSE 0x00000004
#define LINEBEARERMODE_DATA 0x00000008
#define LINEBEARERMODE_ALTSPEECHDATA 0x00000010
#define LINEBEARERMODE_NONCALLSIGNALING 0x00000020
#define LINEBEARERMODE_PASSTHROUGH 0x00000040
#define LINEBEARERMODE_RESTRICTEDDATA 0x00000080

#define LINEBUSYMODE_STATION 0x00000001
#define LINEBUSYMODE_TRUNK 0x00000002
#define LINEBUSYMODE_UNKNOWN 0x00000004
#define LINEBUSYMODE_UNAVAIL 0x00000008

#define LINECALLCOMPLCOND_BUSY 0x00000001
#define LINECALLCOMPLCOND_NOANSWER 0x00000002

#define LINECALLCOMPLMODE_CAMPON 0x00000001
#define LINECALLCOMPLMODE_CALLBACK 0x00000002
#define LINECALLCOMPLMODE_INTRUDE 0x00000004
#define LINECALLCOMPLMODE_MESSAGE 0x00000008

#define LINECALLFEATURE_ACCEPT 0x00000001
#define LINECALLFEATURE_ADDTOCONF 0x00000002
#define LINECALLFEATURE_ANSWER 0x00000004
#define LINECALLFEATURE_BLINDTRANSFER 0x00000008
#define LINECALLFEATURE_COMPLETECALL 0x00000010
#define LINECALLFEATURE_COMPLETETRANSF 0x00000020
#define LINECALLFEATURE_DIAL 0x00000040
#define LINECALLFEATURE_DROP 0x00000080
#define LINECALLFEATURE_GATHERDIGITS 0x00000100
#define LINECALLFEATURE_GENERATEDIGITS 0x00000200
#define LINECALLFEATURE_GENERATETONE 0x00000400
#define LINECALLFEATURE_HOLD 0x00000800
#define LINECALLFEATURE_MONITORDIGITS 0x00001000
#define LINECALLFEATURE_MONITORMEDIA 0x00002000
#define LINECALLFEATURE_MONITORTONES 0x00004000
#define LINECALLFEATURE_PARK 0x00008000
#define LINECALLFEATURE_PREPAREADDCONF 0x00010000
#define LINECALLFEATURE_REDIRECT 0x00020000
#define LINECALLFEATURE_REMOVEFROMCONF 0x00040000
#define LINECALLFEATURE_SECURECALL 0x00080000
#define LINECALLFEATURE_SENDUSERUSER 0x00100000
#define LINECALLFEATURE_SETCALLPARAMS 0x00200000
#define LINECALLFEATURE_SETMEDIACONTROL 0x00400000
#define LINECALLFEATURE_SETTERMINAL 0x00800000
#define LINECALLFEATURE_SETUPCONF 0x01000000
#define LINECALLFEATURE_SETUPTRANSFER 0x02000000
#define LINECALLFEATURE_SWAPHOLD 0x04000000
#define LINECALLFEATURE_UNHOLD 0x08000000
#define LINECALLFEATURE_RELEASEUSERUSERINFO 0x10000000
#define LINECALLFEATURE_SETTREATMENT 0x20000000
#define LINECALLFEATURE_SETQOS 0x40000000
#define LINECALLFEATURE_SETCALLDATA 0x80000000
#define LINECALLFEATURE2_NOHOLDCONFERENCE 0x00000001
#define LINECALLFEATURE2_ONESTEPTRANSFER 0x00000002
#define LINECALLFEATURE2_COMPLCAMPON 0x00000004
#define LINECALLFEATURE2_COMPLCALLBACK 0x00000008
#define LINECALLFEATURE2_COMPLINTRUDE 0x00000010
#define LINECALLFEATURE2_COMPLMESSAGE 0x00000020
#define LINECALLFEATURE2_TRANSFERNORM 0x00000040
#define LINECALLFEATURE2_TRANSFERCONF 0x00000080
#define LINECALLFEATURE2_PARKDIRECT 0x00000100
#define LINECALLFEATURE2_PARKNONDIRECT 0x00000200

#define LINECALLHUBTRACKING_NONE 0x00000000
#define LINECALLHUBTRACKING_PROVIDERLEVEL 0x00000001
#define LINECALLHUBTRACKING_ALLCALLS 0x00000002

#define LINECALLINFOSTATE_OTHER 0x00000001
#define LINECALLINFOSTATE_DEVSPECIFIC 0x00000002
#define LINECALLINFOSTATE_BEARERMODE 0x00000004
#define LINECALLINFOSTATE_RATE 0x00000008
#define LINECALLINFOSTATE_MEDIAMODE 0x00000010
#define LINECALLINFOSTATE_APPSPECIFIC 0x00000020
#define LINECALLINFOSTATE_CALLID 0x00000040
#define LINECALLINFOSTATE_RELATEDCALLID 0x00000080
#define LINECALLINFOSTATE_ORIGIN 0x00000100
#define LINECALLINFOSTATE_REASON 0x00000200
#define LINECALLINFOSTATE_COMPLETIONID 0x00000400
#define LINECALLINFOSTATE_NUMOWNERINCR 0x00000800
#define LINECALLINFOSTATE_NUMOWNERDECR 0x00001000
#define LINECALLINFOSTATE_NUMMONITORS 0x00002000
#define LINECALLINFOSTATE_TRUNK 0x00004000
#define LINECALLINFOSTATE_CALLERID 0x00008000
#define LINECALLINFOSTATE_CALLEDID 0x00010000
#define LINECALLINFOSTATE_CONNECTEDID 0x00020000
#define LINECALLINFOSTATE_REDIRECTIONID 0x00040000
#define LINECALLINFOSTATE_REDIRECTINGID 0x00080000
#define LINECALLINFOSTATE_DISPLAY 0x00100000
#define LINECALLINFOSTATE_USERUSERINFO 0x00200000
#define LINECALLINFOSTATE_HIGHLEVELCOMP 0x00400000
#define LINECALLINFOSTATE_LOWLEVELCOMP 0x00800000
#define LINECALLINFOSTATE_CHARGINGINFO 0x01000000
#define LINECALLINFOSTATE_TERMINAL 0x02000000
#define LINECALLINFOSTATE_DIALPARAMS 0x04000000
#define LINECALLINFOSTATE_MONITORMODES 0x08000000
#define LINECALLINFOSTATE_TREATMENT 0x10000000
#define LINECALLINFOSTATE_QOS 0x20000000
#define LINECALLINFOSTATE_CALLDATA 0x40000000

#define LINECALLORIGIN_OUTBOUND 0x00000001
#define LINECALLORIGIN_INTERNAL 0x00000002
#define LINECALLORIGIN_EXTERNAL 0x00000004
#define LINECALLORIGIN_UNKNOWN 0x00000010
#define LINECALLORIGIN_UNAVAIL 0x00000020
#define LINECALLORIGIN_CONFERENCE 0x00000040
#define LINECALLORIGIN_INBOUND 0x00000080

#define LINECALLPARAMFLAGS_SECURE 0x00000001
#define LINECALLPARAMFLAGS_IDLE 0x00000002
#define LINECALLPARAMFLAGS_BLOCKID 0x00000004
#define LINECALLPARAMFLAGS_ORIGOFFHOOK 0x00000008
#define LINECALLPARAMFLAGS_DESTOFFHOOK 0x00000010
#define LINECALLPARAMFLAGS_NOHOLDCONFERENCE 0x00000020
#define LINECALLPARAMFLAGS_PREDICTIVEDIAL 0x00000040
#define LINECALLPARAMFLAGS_ONESTEPTRANSFER 0x00000080

#define LINECALLPARTYID_BLOCKED 0x00000001
#define LINECALLPARTYID_OUTOFAREA 0x00000002
#define LINECALLPARTYID_NAME 0x00000004
#define LINECALLPARTYID_ADDRESS 0x00000008
#define LINECALLPARTYID_PARTIAL 0x00000010
#define LINECALLPARTYID_UNKNOWN 0x00000020
#define LINECALLPARTYID_UNAVAIL 0x00000040

#define LINECALLPRIVILEGE_NONE 0x00000001
#define LINECALLPRIVILEGE_MONITOR 0x00000002
#define LINECALLPRIVILEGE_OWNER 0x00000004

#define LINECALLREASON_DIRECT 0x00000001
#define LINECALLREASON_FWDBUSY 0x00000002
#define LINECALLREASON_FWDNOANSWER 0x00000004
#define LINECALLREASON_FWDUNCOND 0x00000008
#define LINECALLREASON_PICKUP 0x00000010
#define LINECALLREASON_UNPARK 0x00000020
#define LINECALLREASON_REDIRECT 0x00000040
#define LINECALLREASON_CALLCOMPLETION 0x00000080
#define LINECALLREASON_TRANSFER 0x00000100
#define LINECALLREASON_REMINDER 0x00000200
#define LINECALLREASON_UNKNOWN 0x00000400
#define LINECALLREASON_UNAVAIL 0x00000800
#define LINECALLREASON_INTRUDE 0x00001000
#define LINECALLREASON_PARKED 0x00002000
#define LINECALLREASON_CAMPEDON 0x00004000
#define LINECALLREASON_ROUTEREQUEST 0x00008000

#define LINECALLSELECT_LINE 0x00000001
#define LINECALLSELECT_ADDRESS 0x00000002
#define LINECALLSELECT_CALL 0x00000004
#define LINECALLSELECT_DEVICEID 0x00000008
#define LINECALLSELECT_CALLID 0x00000010

#define LINECALLSTATE_IDLE 0x00000001
#define LINECALLSTATE_OFFERING 0x00000002
#define LINECALLSTATE_ACCEPTED 0x00000004
#define LINECALLSTATE_DIALTONE 0x00000008
#define LINECALLSTATE_DIALING 0x00000010
#define LINECALLSTATE_RINGBACK 0x00000020
#define LINECALLSTATE_BUSY 0x00000040
#define LINECALLSTATE_SPECIALINFO 0x00000080
#define LINECALLSTATE_CONNECTED 0x00000100
#define LINECALLSTATE_PROCEEDING 0x00000200
#define LINECALLSTATE_ONHOLD 0x00000400
#define LINECALLSTATE_CONFERENCED 0x00000800
#define LINECALLSTATE_ONHOLDPENDCONF 0x00001000
#define LINECALLSTATE_ONHOLDPENDTRANSFER 0x00002000
#define LINECALLSTATE_DISCONNECTED 0x00004000
#define LINECALLSTATE_UNKNOWN 0x00008000

#define LINECALLTREATMENT_SILENCE 0x00000001
#define LINECALLTREATMENT_RINGBACK 0x00000002
#define LINECALLTREATMENT_BUSY 0x00000003
#define LINECALLTREATMENT_MUSIC 0x00000004

#define LINECARDOPTION_PREDEFINED 0x00000001
#define LINECARDOPTION_HIDDEN 0x00000002

#define LINECONNECTEDMODE_ACTIVE 0x00000001
#define LINECONNECTEDMODE_INACTIVE 0x00000002
#define LINECONNECTEDMODE_ACTIVEHELD 0x00000004
#define LINECONNECTEDMODE_INACTIVEHELD 0x00000008
#define LINECONNECTEDMODE_CONFIRMED 0x00000010

#define LINEDEVCAPFLAGS_CROSSADDRCONF 0x00000001
#define LINEDEVCAPFLAGS_HIGHLEVCOMP 0x00000002
#define LINEDEVCAPFLAGS_LOWLEVCOMP 0x00000004
#define LINEDEVCAPFLAGS_MEDIACONTROL 0x00000008
#define LINEDEVCAPFLAGS_MULTIPLEADDR 0x00000010
#define LINEDEVCAPFLAGS_CLOSEDROP 0x00000020
#define LINEDEVCAPFLAGS_DIALBILLING 0x00000040
#define LINEDEVCAPFLAGS_DIALQUIET 0x00000080
#define LINEDEVCAPFLAGS_DIALDIALTONE 0x00000100
#define LINEDEVCAPFLAGS_MSP 0x00000200
#define LINEDEVCAPFLAGS_CALLHUB 0x00000400
#define LINEDEVCAPFLAGS_CALLHUBTRACKING 0x00000800
#define LINEDEVCAPFLAGS_PRIVATEOBJECTS 0x00001000
#define LINEDEVCAPFLAGS_LOCAL 0x00002000

#define LINEDEVSTATE_OTHER 0x00000001
#define LINEDEVSTATE_RINGING 0x00000002
#define LINEDEVSTATE_CONNECTED 0x00000004
#define LINEDEVSTATE_DISCONNECTED 0x00000008
#define LINEDEVSTATE_MSGWAITON 0x00000010
#define LINEDEVSTATE_MSGWAITOFF 0x00000020
#define LINEDEVSTATE_INSERVICE 0x00000040
#define LINEDEVSTATE_OUTOFSERVICE 0x00000080
#define LINEDEVSTATE_MAINTENANCE 0x00000100
#define LINEDEVSTATE_OPEN 0x00000200
#define LINEDEVSTATE_CLOSE 0x00000400
#define LINEDEVSTATE_NUMCALLS 0x00000800
#define LINEDEVSTATE_NUMCOMPLETIONS 0x00001000
#define LINEDEVSTATE_TERMINALS 0x00002000
#define LINEDEVSTATE_ROAMMODE 0x00004000
#define LINEDEVSTATE_BATTERY 0x00008000
#define LINEDEVSTATE_SIGNAL 0x00010000
#define LINEDEVSTATE_DEVSPECIFIC 0x00020000
#define LINEDEVSTATE_REINIT 0x00040000
#define LINEDEVSTATE_LOCK 0x00080000
#define LINEDEVSTATE_CAPSCHANGE 0x00100000
#define LINEDEVSTATE_CONFIGCHANGE 0x00200000
#define LINEDEVSTATE_TRANSLATECHANGE 0x00400000
#define LINEDEVSTATE_COMPLCANCEL 0x00800000
#define LINEDEVSTATE_REMOVED 0x01000000

#define LINEDEVSTATUSFLAGS_CONNECTED 0x00000001
#define LINEDEVSTATUSFLAGS_MSGWAIT 0x00000002
#define LINEDEVSTATUSFLAGS_INSERVICE 0x00000004
#define LINEDEVSTATUSFLAGS_LOCKED 0x00000008

#define LINEDIALTONEMODE_NORMAL 0x00000001
#define LINEDIALTONEMODE_SPECIAL 0x00000002
#define LINEDIALTONEMODE_INTERNAL 0x00000004
#define LINEDIALTONEMODE_EXTERNAL 0x00000008
#define LINEDIALTONEMODE_UNKNOWN 0x00000010
#define LINEDIALTONEMODE_UNAVAIL 0x00000020

#define LINEDIGITMODE_PULSE 0x00000001
#define LINEDIGITMODE_DTMF 0x00000002
#define LINEDIGITMODE_DTMFEND 0x00000004

#define LINEDISCONNECTMODE_NORMAL 0x00000001
#define LINEDISCONNECTMODE_UNKNOWN 0x00000002
#define LINEDISCONNECTMODE_REJECT 0x00000004
#define LINEDISCONNECTMODE_PICKUP 0x00000008
#define LINEDISCONNECTMODE_FORWARDED 0x00000010
#define LINEDISCONNECTMODE_BUSY 0x00000020
#define LINEDISCONNECTMODE_NOANSWER 0x00000040
#define LINEDISCONNECTMODE_BADADDRESS 0x00000080
#define LINEDISCONNECTMODE_UNREACHABLE 0x00000100
#define LINEDISCONNECTMODE_CONGESTION 0x00000200
#define LINEDISCONNECTMODE_INCOMPATIBLE 0x00000400
#define LINEDISCONNECTMODE_UNAVAIL 0x00000800
#define LINEDISCONNECTMODE_NODIALTONE 0x00001000
#define LINEDISCONNECTMODE_NUMBERCHANGED 0x00002000
#define LINEDISCONNECTMODE_OUTOFORDER 0x00004000
#define LINEDISCONNECTMODE_TEMPFAILURE 0x00008000
#define LINEDISCONNECTMODE_QOSUNAVAIL 0x00010000
#define LINEDISCONNECTMODE_BLOCKED 0x00020000
#define LINEDISCONNECTMODE_DONOTDISTURB 0x00040000
#define LINEDISCONNECTMODE_CANCELLED 0x00080000

#define LINEERR_ALLOCATED 0x80000001
#define LINEERR_BADDEVICEID 0x80000002
#define LINEERR_BEARERMODEUNAVAIL 0x80000003
#define LINEERR_CALLUNAVAIL 0x80000005
#define LINEERR_COMPLETIONOVERRUN 0x80000006
#define LINEERR_CONFERENCEFULL 0x80000007
#define LINEERR_DIALBILLING 0x80000008
#define LINEERR_DIALDIALTONE 0x80000009
#define LINEERR_DIALPROMPT 0x8000000A
#define LINEERR_DIALQUIET 0x8000000B
#define LINEERR_INCOMPATIBLEAPIVERSION 0x8000000C
#define LINEERR_INCOMPATIBLEEXTVERSION 0x8000000D
#define LINEERR_INIFILECORRUPT 0x8000000E
#define LINEERR_INUSE 0x8000000F
#define LINEERR_INVALADDRESS 0x80000010
#define LINEERR_INVALADDRESSID 0x80000011
#define LINEERR_INVALADDRESSMODE 0x80000012
#define LINEERR_INVALADDRESSSTATE 0x80000013
#define LINEERR_INVALAPPHANDLE 0x80000014
#define LINEERR_INVALAPPNAME 0x80000015
#define LINEERR_INVALBEARERMODE 0x80000016
#define LINEERR_INVALCALLCOMPLMODE 0x80000017
#define LINEERR_INVALCALLHANDLE 0x80000018
#define LINEERR_INVALCALLPARAMS 0x80000019
#define LINEERR_INVALCALLPRIVILEGE 0x8000001A
#define LINEERR_INVALCALLSELECT 0x8000001B
#define LINEERR_INVALCALLSTATE 0x8000001C
#define LINEERR_INVALCALLSTATELIST 0x8000001D
#define LINEERR_INVALCARD 0x8000001E
#define LINEERR_INVALCOMPLETIONID 0x8000001F
#define LINEERR_INVALCONFCALLHANDLE 0x80000020
#define LINEERR_INVALCONSULTCALLHANDLE 0x80000021
#define LINEERR_INVALCOUNTRYCODE 0x80000022
#define LINEERR_INVALDEVICECLASS 0x80000023
#define LINEERR_INVALDEVICEHANDLE 0x80000024
#define LINEERR_INVALDIALPARAMS 0x80000025
#define LINEERR_INVALDIGITLIST 0x80000026
#define LINEERR_INVALDIGITMODE 0x80000027
#define LINEERR_INVALDIGITS 0x80000028
#define LINEERR_INVALEXTVERSION 0x80000029
#define LINEERR_INVALGROUPID 0x8000002A
#define LINEERR_INVALLINEHANDLE 0x8000002B
#define LINEERR_INVALLINESTATE 0x8000002C
#define LINEERR_INVALLOCATION 0x8000002D
#define LINEERR_INVALMEDIALIST 0x8000002E
#define LINEERR_INVALMEDIAMODE 0x8000002F
#define LINEERR_INVALMESSAGEID 0x80000030
#define LINEERR_INVALPARAM 0x80000032
#define LINEERR_INVALPARKID 0x80000033
#define LINEERR_INVALPARKMODE 0x80000034
#define LINEERR_INVALPOINTER 0x80000035
#define LINEERR_INVALPRIVSELECT 0x80000036
#define LINEERR_INVALRATE 0x80000037
#define LINEERR_INVALREQUESTMODE 0x80000038
#define LINEERR_INVALTERMINALID 0x80000039
#define LINEERR_INVALTERMINALMODE 0x8000003A
#define LINEERR_INVALTIMEOUT 0x8000003B
#define LINEERR_INVALTONE 0x8000003C
#define LINEERR_INVALTONELIST 0x8000003D
#define LINEERR_INVALTONEMODE 0x8000003E
#define LINEERR_INVALTRANSFERMODE 0x8000003F
#define LINEERR_LINEMAPPERFAILED 0x80000040
#define LINEERR_NOCONFERENCE 0x80000041
#define LINEERR_NODEVICE 0x80000042
#define LINEERR_NODRIVER 0x80000043
#define LINEERR_NOMEM 0x80000044
#define LINEERR_NOREQUEST 0x80000045
#define LINEERR_NOTOWNER 0x80000046
#define LINEERR_NOTREGISTERED 0x80000047
#define LINEERR_OPERATIONFAILED 0x80000048
#define LINEERR_OPERATIONUNAVAIL 0x80000049
#define LINEERR_RATEUNAVAIL 0x8000004A
#define LINEERR_RESOURCEUNAVAIL 0x8000004B
#define LINEERR_REQUESTOVERRUN 0x8000004C
#define LINEERR_STRUCTURETOOSMALL 0x8000004D
#define LINEERR_TARGETNOTFOUND 0x8000004E
#define LINEERR_TARGETSELF 0x8000004F
#define LINEERR_UNINITIALIZED 0x80000050
#define LINEERR_USERUSERINFOTOOBIG 0x80000051
#define LINEERR_REINIT 0x80000052
#define LINEERR_ADDRESSBLOCKED 0x80000053
#define LINEERR_BILLINGREJECTED 0x80000054
#define LINEERR_INVALFEATURE 0x80000055
#define LINEERR_NOMULTIPLEINSTANCE 0x80000056
#define LINEERR_INVALAGENTID 0x80000057
#define LINEERR_INVALAGENTGROUP 0x80000058
#define LINEERR_INVALPASSWORD 0x80000059
#define LINEERR_INVALAGENTSTATE 0x8000005A
#define LINEERR_INVALAGENTACTIVITY 0x8000005B
#define LINEERR_DIALVOICEDETECT 0x8000005C
#define LINEERR_USERCANCELLED 0x8000005D
#define LINEERR_INVALADDRESSTYPE 0x8000005E
#define LINEERR_INVALAGENTSESSIONSTATE 0x8000005F
#define LINEERR_DISCONNECTED 0X80000060
#define LINEERR_SERVICE_NOT_RUNNING 0x80000061

#define LINEFEATURE_DEVSPECIFIC 0x00000001
#define LINEFEATURE_DEVSPECIFICFEAT 0x00000002
#define LINEFEATURE_FORWARD 0x00000004
#define LINEFEATURE_MAKECALL 0x00000008
#define LINEFEATURE_SETMEDIACONTROL 0x00000010
#define LINEFEATURE_SETTERMINAL 0x00000020
#define LINEFEATURE_SETDEVSTATUS 0x00000040
#define LINEFEATURE_FORWARDFWD 0x00000080
#define LINEFEATURE_FORWARDDND 0x00000100

#define LINEFORWARDMODE_UNCOND 0x00000001
#define LINEFORWARDMODE_UNCONDINTERNAL 0x00000002
#define LINEFORWARDMODE_UNCONDEXTERNAL 0x00000004
#define LINEFORWARDMODE_UNCONDSPECIFIC 0x00000008
#define LINEFORWARDMODE_BUSY 0x00000010
#define LINEFORWARDMODE_BUSYINTERNAL 0x00000020
#define LINEFORWARDMODE_BUSYEXTERNAL 0x00000040
#define LINEFORWARDMODE_BUSYSPECIFIC 0x00000080
#define LINEFORWARDMODE_NOANSW 0x00000100
#define LINEFORWARDMODE_NOANSWINTERNAL 0x00000200
#define LINEFORWARDMODE_NOANSWEXTERNAL 0x00000400
#define LINEFORWARDMODE_NOANSWSPECIFIC 0x00000800
#define LINEFORWARDMODE_BUSYNA 0x00001000
#define LINEFORWARDMODE_BUSYNAINTERNAL 0x00002000
#define LINEFORWARDMODE_BUSYNAEXTERNAL 0x00004000
#define LINEFORWARDMODE_BUSYNASPECIFIC 0x00008000
#define LINEFORWARDMODE_UNKNOWN 0x00010000
#define LINEFORWARDMODE_UNAVAIL 0x00020000

#define LINEGATHERTERM_BUFFERFULL 0x00000001
#define LINEGATHERTERM_TERMDIGIT 0x00000002
#define LINEGATHERTERM_FIRSTTIMEOUT 0x00000004
#define LINEGATHERTERM_INTERTIMEOUT 0x00000008
#define LINEGATHERTERM_CANCEL 0x00000010

#define LINEGENERATETERM_DONE 0x00000001
#define LINEGENERATETERM_CANCEL 0x00000002

#define LINEINITIALIZEEXOPTION_USEHIDDENWINDOW 0x00000001
#define LINEINITIALIZEEXOPTION_USEEVENT 0x00000002
#define LINEINITIALIZEEXOPTION_USECOMPLETIONPORT 0x00000003
#define LINEINITIALIZEEXOPTION_CALLHUBTRACKING 0x80000000
#define LINELOCATIONOPTION_PULSEDIAL 0x00000001

#define LINEMAPPER 0xFFFFFFFF

#define LINEMEDIACONTROL_NONE 0x00000001
#define LINEMEDIACONTROL_START 0x00000002
#define LINEMEDIACONTROL_RESET 0x00000004
#define LINEMEDIACONTROL_PAUSE 0x00000008
#define LINEMEDIACONTROL_RESUME 0x00000010
#define LINEMEDIACONTROL_RATEUP 0x00000020
#define LINEMEDIACONTROL_RATEDOWN 0x00000040
#define LINEMEDIACONTROL_RATENORMAL 0x00000080
#define LINEMEDIACONTROL_VOLUMEUP 0x00000100
#define LINEMEDIACONTROL_VOLUMEDOWN 0x00000200
#define LINEMEDIACONTROL_VOLUMENORMAL 0x00000400

#define LINEMEDIAMODE_UNKNOWN 0x00000002
#define LINEMEDIAMODE_INTERACTIVEVOICE 0x00000004
#define LINEMEDIAMODE_AUTOMATEDVOICE 0x00000008
#define LINEMEDIAMODE_DATAMODEM 0x00000010
#define LINEMEDIAMODE_G3FAX 0x00000020
#define LINEMEDIAMODE_TDD 0x00000040
#define LINEMEDIAMODE_G4FAX 0x00000080
#define LINEMEDIAMODE_DIGITALDATA 0x00000100
#define LINEMEDIAMODE_TELETEX 0x00000200
#define LINEMEDIAMODE_VIDEOTEX 0x00000400
#define LINEMEDIAMODE_TELEX 0x00000800
#define LINEMEDIAMODE_MIXED 0x00001000
#define LINEMEDIAMODE_ADSI 0x00002000
#define LINEMEDIAMODE_VOICEVIEW 0x00004000
#define LINEMEDIAMODE_VIDEO 0x00008000
#define LAST_LINEMEDIAMODE 0x00008000

#define LINEOFFERINGMODE_ACTIVE 0x00000001
#define LINEOFFERINGMODE_INACTIVE 0x00000002

#define LINEOPENOPTION_SINGLEADDRESS 0x80000000
#define LINEOPENOPTION_PROXY 0x40000000

#define LINEPARKMODE_DIRECTED 0x00000001
#define LINEPARKMODE_NONDIRECTED 0x00000002

#define LINEPROXYREQUEST_SETAGENTGROUP 0x00000001
#define LINEPROXYREQUEST_SETAGENTSTATE 0x00000002
#define LINEPROXYREQUEST_SETAGENTACTIVITY 0x00000003
#define LINEPROXYREQUEST_GETAGENTCAPS 0x00000004
#define LINEPROXYREQUEST_GETAGENTSTATUS 0x00000005
#define LINEPROXYREQUEST_AGENTSPECIFIC 0x00000006
#define LINEPROXYREQUEST_GETAGENTACTIVITYLIST 0x00000007
#define LINEPROXYREQUEST_GETAGENTGROUPLIST 0x00000008

#define LINEPROXYREQUEST_CREATEAGENT 0x00000009
#define LINEPROXYREQUEST_SETAGENTMEASUREMENTPERIOD 0x0000000A
#define LINEPROXYREQUEST_GETAGENTINFO 0x0000000B
#define LINEPROXYREQUEST_CREATEAGENTSESSION 0x0000000C
#define LINEPROXYREQUEST_GETAGENTSESSIONLIST 0x0000000D
#define LINEPROXYREQUEST_SETAGENTSESSIONSTATE 0x0000000E
#define LINEPROXYREQUEST_GETAGENTSESSIONINFO 0x0000000F
#define LINEPROXYREQUEST_GETQUEUELIST 0x00000010
#define LINEPROXYREQUEST_SETQUEUEMEASUREMENTPERIOD 0x00000011
#define LINEPROXYREQUEST_GETQUEUEINFO 0x00000012
#define LINEPROXYREQUEST_GETGROUPLIST 0x00000013
#define LINEPROXYREQUEST_SETAGENTSTATEEX 0x00000014

#define LINEREMOVEFROMCONF_NONE 0x00000001
#define LINEREMOVEFROMCONF_LAST 0x00000002
#define LINEREMOVEFROMCONF_ANY 0x00000003

#define LINEREQUESTMODE_MAKECALL 0x00000001
#define LINEREQUESTMODE_MEDIACALL 0x00000002
#define LINEREQUESTMODE_DROP 0x00000004
#define LAST_LINEREQUESTMODE LINEREQUESTMODE_MEDIACALL

#define LINEROAMMODE_UNKNOWN 0x00000001
#define LINEROAMMODE_UNAVAIL 0x00000002
#define LINEROAMMODE_HOME 0x00000004
#define LINEROAMMODE_ROAMA 0x00000008
#define LINEROAMMODE_ROAMB 0x00000010

#define LINESPECIALINFO_NOCIRCUIT 0x00000001
#define LINESPECIALINFO_CUSTIRREG 0x00000002
#define LINESPECIALINFO_REORDER 0x00000004
#define LINESPECIALINFO_UNKNOWN 0x00000008
#define LINESPECIALINFO_UNAVAIL 0x00000010

#define LINETERMDEV_PHONE 0x00000001
#define LINETERMDEV_HEADSET 0x00000002
#define LINETERMDEV_SPEAKER 0x00000004

#define LINETERMMODE_BUTTONS 0x00000001
#define LINETERMMODE_LAMPS 0x00000002
#define LINETERMMODE_DISPLAY 0x00000004
#define LINETERMMODE_RINGER 0x00000008
#define LINETERMMODE_HOOKSWITCH 0x00000010
#define LINETERMMODE_MEDIATOLINE 0x00000020
#define LINETERMMODE_MEDIAFROMLINE 0x00000040
#define LINETERMMODE_MEDIABIDIRECT 0x00000080

#define LINETERMSHARING_PRIVATE 0x00000001
#define LINETERMSHARING_SHAREDEXCL 0x00000002
#define LINETERMSHARING_SHAREDCONF 0x00000004

#define LINETOLLLISTOPTION_ADD 0x00000001
#define LINETOLLLISTOPTION_REMOVE 0x00000002

#define LINETONEMODE_CUSTOM 0x00000001
#define LINETONEMODE_RINGBACK 0x00000002
#define LINETONEMODE_BUSY 0x00000004
#define LINETONEMODE_BEEP 0x00000008
#define LINETONEMODE_BILLING 0x00000010

#define LINETRANSFERMODE_TRANSFER 0x00000001
#define LINETRANSFERMODE_CONFERENCE 0x00000002

#define LINETRANSLATEOPTION_CARDOVERRIDE 0x00000001
#define LINETRANSLATEOPTION_CANCELCALLWAITING 0x00000002
#define LINETRANSLATEOPTION_FORCELOCAL 0x00000004
#define LINETRANSLATEOPTION_FORCELD 0x00000008

#define LINETRANSLATERESULT_CANONICAL 0x00000001
#define LINETRANSLATERESULT_INTERNATIONAL 0x00000002
#define LINETRANSLATERESULT_LONGDISTANCE 0x00000004
#define LINETRANSLATERESULT_LOCAL 0x00000008
#define LINETRANSLATERESULT_INTOLLLIST 0x00000010
#define LINETRANSLATERESULT_NOTINTOLLLIST 0x00000020
#define LINETRANSLATERESULT_DIALBILLING 0x00000040
#define LINETRANSLATERESULT_DIALQUIET 0x00000080
#define LINETRANSLATERESULT_DIALDIALTONE 0x00000100
#define LINETRANSLATERESULT_DIALPROMPT 0x00000200
#define LINETRANSLATERESULT_VOICEDETECT 0x00000400
#define LINETRANSLATERESULT_NOTRANSLATION 0x00000800

#define PHONEBUTTONFUNCTION_UNKNOWN 0x00000000
#define PHONEBUTTONFUNCTION_CONFERENCE 0x00000001
#define PHONEBUTTONFUNCTION_TRANSFER 0x00000002
#define PHONEBUTTONFUNCTION_DROP 0x00000003
#define PHONEBUTTONFUNCTION_HOLD 0x00000004
#define PHONEBUTTONFUNCTION_RECALL 0x00000005
#define PHONEBUTTONFUNCTION_DISCONNECT 0x00000006
#define PHONEBUTTONFUNCTION_CONNECT 0x00000007
#define PHONEBUTTONFUNCTION_MSGWAITON 0x00000008
#define PHONEBUTTONFUNCTION_MSGWAITOFF 0x00000009
#define PHONEBUTTONFUNCTION_SELECTRING 0x0000000A
#define PHONEBUTTONFUNCTION_ABBREVDIAL 0x0000000B
#define PHONEBUTTONFUNCTION_FORWARD 0x0000000C
#define PHONEBUTTONFUNCTION_PICKUP 0x0000000D
#define PHONEBUTTONFUNCTION_RINGAGAIN 0x0000000E
#define PHONEBUTTONFUNCTION_PARK 0x0000000F
#define PHONEBUTTONFUNCTION_REJECT 0x00000010
#define PHONEBUTTONFUNCTION_REDIRECT 0x00000011
#define PHONEBUTTONFUNCTION_MUTE 0x00000012
#define PHONEBUTTONFUNCTION_VOLUMEUP 0x00000013
#define PHONEBUTTONFUNCTION_VOLUMEDOWN 0x00000014
#define PHONEBUTTONFUNCTION_SPEAKERON 0x00000015
#define PHONEBUTTONFUNCTION_SPEAKEROFF 0x00000016
#define PHONEBUTTONFUNCTION_FLASH 0x00000017
#define PHONEBUTTONFUNCTION_DATAON 0x00000018
#define PHONEBUTTONFUNCTION_DATAOFF 0x00000019
#define PHONEBUTTONFUNCTION_DONOTDISTURB 0x0000001A
#define PHONEBUTTONFUNCTION_INTERCOM 0x0000001B
#define PHONEBUTTONFUNCTION_BRIDGEDAPP 0x0000001C
#define PHONEBUTTONFUNCTION_BUSY 0x0000001D
#define PHONEBUTTONFUNCTION_CALLAPP 0x0000001E
#define PHONEBUTTONFUNCTION_DATETIME 0x0000001F
#define PHONEBUTTONFUNCTION_DIRECTORY 0x00000020
#define PHONEBUTTONFUNCTION_COVER 0x00000021
#define PHONEBUTTONFUNCTION_CALLID 0x00000022
#define PHONEBUTTONFUNCTION_LASTNUM 0x00000023
#define PHONEBUTTONFUNCTION_NIGHTSRV 0x00000024
#define PHONEBUTTONFUNCTION_SENDCALLS 0x00000025
#define PHONEBUTTONFUNCTION_MSGINDICATOR 0x00000026
#define PHONEBUTTONFUNCTION_REPDIAL 0x00000027
#define PHONEBUTTONFUNCTION_SETREPDIAL 0x00000028
#define PHONEBUTTONFUNCTION_SYSTEMSPEED 0x00000029
#define PHONEBUTTONFUNCTION_STATIONSPEED 0x0000002A
#define PHONEBUTTONFUNCTION_CAMPON 0x0000002B
#define PHONEBUTTONFUNCTION_SAVEREPEAT 0x0000002C
#define PHONEBUTTONFUNCTION_QUEUECALL 0x0000002D
#define PHONEBUTTONFUNCTION_NONE 0x0000002E
#define PHONEBUTTONFUNCTION_SEND 0x0000002F

#define PHONEBUTTONMODE_DUMMY 0x00000001
#define PHONEBUTTONMODE_CALL 0x00000002
#define PHONEBUTTONMODE_FEATURE 0x00000004
#define PHONEBUTTONMODE_KEYPAD 0x00000008
#define PHONEBUTTONMODE_LOCAL 0x00000010
#define PHONEBUTTONMODE_DISPLAY 0x00000020

#define PHONEBUTTONSTATE_UP 0x00000001
#define PHONEBUTTONSTATE_DOWN 0x00000002
#define PHONEBUTTONSTATE_UNKNOWN 0x00000004
#define PHONEBUTTONSTATE_UNAVAIL 0x00000008

#define PHONEERR_ALLOCATED 0x90000001
#define PHONEERR_BADDEVICEID 0x90000002
#define PHONEERR_INCOMPATIBLEAPIVERSION 0x90000003
#define PHONEERR_INCOMPATIBLEEXTVERSION 0x90000004
#define PHONEERR_INIFILECORRUPT 0x90000005
#define PHONEERR_INUSE 0x90000006
#define PHONEERR_INVALAPPHANDLE 0x90000007
#define PHONEERR_INVALAPPNAME 0x90000008
#define PHONEERR_INVALBUTTONLAMPID 0x90000009
#define PHONEERR_INVALBUTTONMODE 0x9000000A
#define PHONEERR_INVALBUTTONSTATE 0x9000000B
#define PHONEERR_INVALDATAID 0x9000000C
#define PHONEERR_INVALDEVICECLASS 0x9000000D
#define PHONEERR_INVALEXTVERSION 0x9000000E
#define PHONEERR_INVALHOOKSWITCHDEV 0x9000000F
#define PHONEERR_INVALHOOKSWITCHMODE 0x90000010
#define PHONEERR_INVALLAMPMODE 0x90000011
#define PHONEERR_INVALPARAM 0x90000012
#define PHONEERR_INVALPHONEHANDLE 0x90000013
#define PHONEERR_INVALPHONESTATE 0x90000014
#define PHONEERR_INVALPOINTER 0x90000015
#define PHONEERR_INVALPRIVILEGE 0x90000016
#define PHONEERR_INVALRINGMODE 0x90000017
#define PHONEERR_NODEVICE 0x90000018
#define PHONEERR_NODRIVER 0x90000019
#define PHONEERR_NOMEM 0x9000001A
#define PHONEERR_NOTOWNER 0x9000001B
#define PHONEERR_OPERATIONFAILED 0x9000001C
#define PHONEERR_OPERATIONUNAVAIL 0x9000001D
#define PHONEERR_RESOURCEUNAVAIL 0x9000001F
#define PHONEERR_REQUESTOVERRUN 0x90000020
#define PHONEERR_STRUCTURETOOSMALL 0x90000021
#define PHONEERR_UNINITIALIZED 0x90000022
#define PHONEERR_REINIT 0x90000023
#define PHONEERR_DISCONNECTED 0x90000024
#define PHONEERR_SERVICE_NOT_RUNNING 0x90000025

#define PHONEFEATURE_GETBUTTONINFO 0x00000001
#define PHONEFEATURE_GETDATA 0x00000002
#define PHONEFEATURE_GETDISPLAY 0x00000004
#define PHONEFEATURE_GETGAINHANDSET 0x00000008
#define PHONEFEATURE_GETGAINSPEAKER 0x00000010
#define PHONEFEATURE_GETGAINHEADSET 0x00000020
#define PHONEFEATURE_GETHOOKSWITCHHANDSET 0x00000040
#define PHONEFEATURE_GETHOOKSWITCHSPEAKER 0x00000080
#define PHONEFEATURE_GETHOOKSWITCHHEADSET 0x00000100
#define PHONEFEATURE_GETLAMP 0x00000200
#define PHONEFEATURE_GETRING 0x00000400
#define PHONEFEATURE_GETVOLUMEHANDSET 0x00000800
#define PHONEFEATURE_GETVOLUMESPEAKER 0x00001000
#define PHONEFEATURE_GETVOLUMEHEADSET 0x00002000
#define PHONEFEATURE_SETBUTTONINFO 0x00004000
#define PHONEFEATURE_SETDATA 0x00008000
#define PHONEFEATURE_SETDISPLAY 0x00010000
#define PHONEFEATURE_SETGAINHANDSET 0x00020000
#define PHONEFEATURE_SETGAINSPEAKER 0x00040000
#define PHONEFEATURE_SETGAINHEADSET 0x00080000
#define PHONEFEATURE_SETHOOKSWITCHHANDSET 0x00100000
#define PHONEFEATURE_SETHOOKSWITCHSPEAKER 0x00200000
#define PHONEFEATURE_SETHOOKSWITCHHEADSET 0x00400000
#define PHONEFEATURE_SETLAMP 0x00800000
#define PHONEFEATURE_SETRING 0x01000000
#define PHONEFEATURE_SETVOLUMEHANDSET 0x02000000
#define PHONEFEATURE_SETVOLUMESPEAKER 0x04000000
#define PHONEFEATURE_SETVOLUMEHEADSET 0x08000000
#define PHONEFEATURE_GENERICPHONE 0x10000000

#define PHONEHOOKSWITCHDEV_HANDSET 0x00000001
#define PHONEHOOKSWITCHDEV_SPEAKER 0x00000002
#define PHONEHOOKSWITCHDEV_HEADSET 0x00000004

#define PHONEHOOKSWITCHMODE_ONHOOK 0x00000001
#define PHONEHOOKSWITCHMODE_MIC 0x00000002
#define PHONEHOOKSWITCHMODE_SPEAKER 0x00000004
#define PHONEHOOKSWITCHMODE_MICSPEAKER 0x00000008
#define PHONEHOOKSWITCHMODE_UNKNOWN 0x00000010

#define PHONEINITIALIZEEXOPTION_USEHIDDENWINDOW 0x00000001
#define PHONEINITIALIZEEXOPTION_USEEVENT 0x00000002
#define PHONEINITIALIZEEXOPTION_USECOMPLETIONPORT 0x00000003

#define PHONELAMPMODE_DUMMY 0x00000001
#define PHONELAMPMODE_OFF 0x00000002
#define PHONELAMPMODE_STEADY 0x00000004
#define PHONELAMPMODE_WINK 0x00000008
#define PHONELAMPMODE_FLASH 0x00000010
#define PHONELAMPMODE_FLUTTER 0x00000020
#define PHONELAMPMODE_BROKENFLUTTER 0x00000040
#define PHONELAMPMODE_UNKNOWN 0x00000080

#define PHONEPRIVILEGE_MONITOR 0x00000001
#define PHONEPRIVILEGE_OWNER 0x00000002

#define PHONESTATE_OTHER 0x00000001
#define PHONESTATE_CONNECTED 0x00000002
#define PHONESTATE_DISCONNECTED 0x00000004
#define PHONESTATE_OWNER 0x00000008
#define PHONESTATE_MONITORS 0x00000010
#define PHONESTATE_DISPLAY 0x00000020
#define PHONESTATE_LAMP 0x00000040
#define PHONESTATE_RINGMODE 0x00000080
#define PHONESTATE_RINGVOLUME 0x00000100
#define PHONESTATE_HANDSETHOOKSWITCH 0x00000200
#define PHONESTATE_HANDSETVOLUME 0x00000400
#define PHONESTATE_HANDSETGAIN 0x00000800
#define PHONESTATE_SPEAKERHOOKSWITCH 0x00001000
#define PHONESTATE_SPEAKERVOLUME 0x00002000
#define PHONESTATE_SPEAKERGAIN 0x00004000
#define PHONESTATE_HEADSETHOOKSWITCH 0x00008000
#define PHONESTATE_HEADSETVOLUME 0x00010000
#define PHONESTATE_HEADSETGAIN 0x00020000
#define PHONESTATE_SUSPEND 0x00040000
#define PHONESTATE_RESUME 0x00080000
#define PHONESTATE_DEVSPECIFIC 0x00100000
#define PHONESTATE_REINIT 0x00200000
#define PHONESTATE_CAPSCHANGE 0x00400000
#define PHONESTATE_REMOVED 0x00800000

#define PHONESTATUSFLAGS_CONNECTED 0x00000001
#define PHONESTATUSFLAGS_SUSPENDED 0x00000002

#define STRINGFORMAT_ASCII 0x00000001
#define STRINGFORMAT_DBCS 0x00000002
#define STRINGFORMAT_UNICODE 0x00000003
#define STRINGFORMAT_BINARY 0x00000004

#define TAPI_REPLY WM_USER + 99

#define TAPIERR_CONNECTED __MSABI_LONG(0)
#define TAPIERR_DROPPED __MSABI_LONG(-1)
#define TAPIERR_NOREQUESTRECIPIENT __MSABI_LONG(-2)
#define TAPIERR_REQUESTQUEUEFULL __MSABI_LONG(-3)
#define TAPIERR_INVALDESTADDRESS __MSABI_LONG(-4)
#define TAPIERR_INVALWINDOWHANDLE __MSABI_LONG(-5)
#define TAPIERR_INVALDEVICECLASS __MSABI_LONG(-6)
#define TAPIERR_INVALDEVICEID __MSABI_LONG(-7)
#define TAPIERR_DEVICECLASSUNAVAIL __MSABI_LONG(-8)
#define TAPIERR_DEVICEIDUNAVAIL __MSABI_LONG(-9)
#define TAPIERR_DEVICEINUSE __MSABI_LONG(-10)
#define TAPIERR_DESTBUSY __MSABI_LONG(-11)
#define TAPIERR_DESTNOANSWER __MSABI_LONG(-12)
#define TAPIERR_DESTUNAVAIL __MSABI_LONG(-13)
#define TAPIERR_UNKNOWNWINHANDLE __MSABI_LONG(-14)
#define TAPIERR_UNKNOWNREQUESTID __MSABI_LONG(-15)
#define TAPIERR_REQUESTFAILED __MSABI_LONG(-16)
#define TAPIERR_REQUESTCANCELLED __MSABI_LONG(-17)
#define TAPIERR_INVALPOINTER __MSABI_LONG(-18)
#define TAPIERR_NOTADMIN __MSABI_LONG(-19)
#define TAPIERR_MMCWRITELOCKED __MSABI_LONG(-20)
#define TAPIERR_PROVIDERALREADYINSTALLED __MSABI_LONG(-21)
#define TAPIERR_SCP_ALREADY_EXISTS __MSABI_LONG(-22)
#define TAPIERR_SCP_DOES_NOT_EXIST __MSABI_LONG(-23)

#define TAPIMAXDESTADDRESSSIZE __MSABI_LONG(80)
#define TAPIMAXAPPNAMESIZE __MSABI_LONG(40)
#define TAPIMAXCALLEDPARTYSIZE __MSABI_LONG(40)
#define TAPIMAXCOMMENTSIZE __MSABI_LONG(80)
#define TAPIMAXDEVICECLASSSIZE __MSABI_LONG(40)
#define TAPIMAXDEVICEIDSIZE __MSABI_LONG(40)

#ifndef GUID_DEFINED
#define GUID_DEFINED
  typedef struct _GUID {
    unsigned __LONG32 Data1;
    unsigned short Data2;
    unsigned short Data3;
    unsigned char Data4[8];
  } GUID;
#endif

#ifndef __LPGUID_DEFINED__
#define __LPGUID_DEFINED__
  typedef GUID *LPGUID;
#endif

  typedef struct lineaddresscaps_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwLineDeviceID;
    DWORD dwAddressSize;
    DWORD dwAddressOffset;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
    DWORD dwAddressSharing;
    DWORD dwAddressStates;
    DWORD dwCallInfoStates;
    DWORD dwCallerIDFlags;
    DWORD dwCalledIDFlags;
    DWORD dwConnectedIDFlags;
    DWORD dwRedirectionIDFlags;
    DWORD dwRedirectingIDFlags;
    DWORD dwCallStates;
    DWORD dwDialToneModes;
    DWORD dwBusyModes;
    DWORD dwSpecialInfo;
    DWORD dwDisconnectModes;
    DWORD dwMaxNumActiveCalls;
    DWORD dwMaxNumOnHoldCalls;
    DWORD dwMaxNumOnHoldPendingCalls;
    DWORD dwMaxNumConference;
    DWORD dwMaxNumTransConf;
    DWORD dwAddrCapFlags;
    DWORD dwCallFeatures;
    DWORD dwRemoveFromConfCaps;
    DWORD dwRemoveFromConfState;
    DWORD dwTransferModes;
    DWORD dwParkModes;
    DWORD dwForwardModes;
    DWORD dwMaxForwardEntries;
    DWORD dwMaxSpecificEntries;
    DWORD dwMinFwdNumRings;
    DWORD dwMaxFwdNumRings;
    DWORD dwMaxCallCompletions;
    DWORD dwCallCompletionConds;
    DWORD dwCallCompletionModes;
    DWORD dwNumCompletionMessages;
    DWORD dwCompletionMsgTextEntrySize;
    DWORD dwCompletionMsgTextSize;
    DWORD dwCompletionMsgTextOffset;
    DWORD dwAddressFeatures;
    DWORD dwPredictiveAutoTransferStates;
    DWORD dwNumCallTreatments;
    DWORD dwCallTreatmentListSize;
    DWORD dwCallTreatmentListOffset;
    DWORD dwDeviceClassesSize;
    DWORD dwDeviceClassesOffset;
    DWORD dwMaxCallDataSize;
    DWORD dwCallFeatures2;
    DWORD dwMaxNoAnswerTimeout;
    DWORD dwConnectedModes;
    DWORD dwOfferingModes;
    DWORD dwAvailableMediaModes;
  } LINEADDRESSCAPS,*LPLINEADDRESSCAPS;

  typedef struct lineaddressstatus_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumInUse;
    DWORD dwNumActiveCalls;
    DWORD dwNumOnHoldCalls;
    DWORD dwNumOnHoldPendCalls;
    DWORD dwAddressFeatures;
    DWORD dwNumRingsNoAnswer;
    DWORD dwForwardNumEntries;
    DWORD dwForwardSize;
    DWORD dwForwardOffset;
    DWORD dwTerminalModesSize;
    DWORD dwTerminalModesOffset;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
  } LINEADDRESSSTATUS,*LPLINEADDRESSSTATUS;

  typedef struct lineagentactivityentry_tag {
    DWORD dwID;
    DWORD dwNameSize;
    DWORD dwNameOffset;
  } LINEAGENTACTIVITYENTRY,*LPLINEAGENTACTIVITYENTRY;

  typedef struct lineagentactivitylist_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumEntries;
    DWORD dwListSize;
    DWORD dwListOffset;
  } LINEAGENTACTIVITYLIST,*LPLINEAGENTACTIVITYLIST;

  typedef struct lineagentcaps_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwAgentHandlerInfoSize;
    DWORD dwAgentHandlerInfoOffset;
    DWORD dwCapsVersion;
    DWORD dwFeatures;
    DWORD dwStates;
    DWORD dwNextStates;
    DWORD dwMaxNumGroupEntries;
    DWORD dwAgentStatusMessages;
    DWORD dwNumAgentExtensionIDs;
    DWORD dwAgentExtensionIDListSize;
    DWORD dwAgentExtensionIDListOffset;
    GUID ProxyGUID;
  } LINEAGENTCAPS,*LPLINEAGENTCAPS;

  typedef struct lineagentgroupentry_tag {
    struct {
      DWORD dwGroupID1;
      DWORD dwGroupID2;
      DWORD dwGroupID3;
      DWORD dwGroupID4;
    } GroupID;
    DWORD dwNameSize;
    DWORD dwNameOffset;
  } LINEAGENTGROUPENTRY,*LPLINEAGENTGROUPENTRY;

  typedef struct lineagentgrouplist_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumEntries;
    DWORD dwListSize;
    DWORD dwListOffset;
  } LINEAGENTGROUPLIST,*LPLINEAGENTGROUPLIST;

  typedef struct lineagentstatus_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumEntries;
    DWORD dwGroupListSize;
    DWORD dwGroupListOffset;
    DWORD dwState;
    DWORD dwNextState;
    DWORD dwActivityID;
    DWORD dwActivitySize;
    DWORD dwActivityOffset;
    DWORD dwAgentFeatures;
    DWORD dwValidStates;
    DWORD dwValidNextStates;
  } LINEAGENTSTATUS,*LPLINEAGENTSTATUS;

  typedef struct lineappinfo_tag {
    DWORD dwMachineNameSize;
    DWORD dwMachineNameOffset;
    DWORD dwUserNameSize;
    DWORD dwUserNameOffset;
    DWORD dwModuleFilenameSize;
    DWORD dwModuleFilenameOffset;
    DWORD dwFriendlyNameSize;
    DWORD dwFriendlyNameOffset;
    DWORD dwMediaModes;
    DWORD dwAddressID;
  } LINEAPPINFO,*LPLINEAPPINFO;

  typedef struct lineagententry_tag {
    HAGENT hAgent;
    DWORD dwNameSize;
    DWORD dwNameOffset;
    DWORD dwIDSize;
    DWORD dwIDOffset;
    DWORD dwPINSize;
    DWORD dwPINOffset;
  } LINEAGENTENTRY,*LPLINEAGENTENTRY;

  typedef struct lineagentlist_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumEntries;
    DWORD dwListSize;
    DWORD dwListOffset;
  } LINEAGENTLIST,*LPLINEAGENTLIST;

  typedef struct lineagentinfo_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwAgentState;
    DWORD dwNextAgentState;
    DWORD dwMeasurementPeriod;
    CURRENCY cyOverallCallRate;
    DWORD dwNumberOfACDCalls;
    DWORD dwNumberOfIncomingCalls;
    DWORD dwNumberOfOutgoingCalls;
    DWORD dwTotalACDTalkTime;
    DWORD dwTotalACDCallTime;
    DWORD dwTotalACDWrapUpTime;
  } LINEAGENTINFO,*LPLINEAGENTINFO;

  typedef struct lineagentsession_tag {
    HAGENTSESSION hAgentSession;
    HAGENT hAgent;
    GUID GroupID;
    DWORD dwWorkingAddressID;
  } LINEAGENTSESSIONENTRY ,*LPLINEAGENTSESSIONENTRY;

  typedef struct lineagentsessionlist_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumEntries;
    DWORD dwListSize;
    DWORD dwListOffset;
  } LINEAGENTSESSIONLIST,*LPLINEAGENTSESSIONLIST;

  typedef struct lineagentsessioninfo_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwAgentSessionState;
    DWORD dwNextAgentSessionState;
    DATE dateSessionStartTime;
    DWORD dwSessionDuration;
    DWORD dwNumberOfCalls;
    DWORD dwTotalTalkTime;
    DWORD dwAverageTalkTime;
    DWORD dwTotalCallTime;
    DWORD dwAverageCallTime;
    DWORD dwTotalWrapUpTime;
    DWORD dwAverageWrapUpTime;
    CURRENCY cyACDCallRate;
    DWORD dwLongestTimeToAnswer;
    DWORD dwAverageTimeToAnswer;
  } LINEAGENTSESSIONINFO,*LPLINEAGENTSESSIONINFO;

  typedef struct linequeueentry_tag {
    DWORD dwQueueID;
    DWORD dwNameSize;
    DWORD dwNameOffset;
  } LINEQUEUEENTRY,*LPLINEQUEUEENTRY;

  typedef struct linequeuelist_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumEntries;
    DWORD dwListSize;
    DWORD dwListOffset;
  } LINEQUEUELIST,*LPLINEQUEUELIST;

  typedef struct linequeueinfo_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwMeasurementPeriod;
    DWORD dwTotalCallsQueued;
    DWORD dwCurrentCallsQueued;
    DWORD dwTotalCallsAbandoned;
    DWORD dwTotalCallsFlowedIn;
    DWORD dwTotalCallsFlowedOut;
    DWORD dwLongestEverWaitTime;
    DWORD dwCurrentLongestWaitTime;
    DWORD dwAverageWaitTime;
    DWORD dwFinalDisposition;
  } LINEQUEUEINFO,*LPLINEQUEUEINFO;

  typedef struct lineproxyrequestlist_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumEntries;
    DWORD dwListSize;
    DWORD dwListOffset;
  } LINEPROXYREQUESTLIST,*LPLINEPROXYREQUESTLIST;

  typedef struct linecallhubtrackinginfo_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwAvailableTracking;
    DWORD dwCurrentTracking;
  } LINECALLHUBTRACKINGINFO,*LPLINECALLHUBTRACKINGINFO;

  typedef struct linedialparams_tag {
    DWORD dwDialPause;
    DWORD dwDialSpeed;
    DWORD dwDigitDuration;
    DWORD dwWaitForDialtone;
  } LINEDIALPARAMS,*LPLINEDIALPARAMS;

  typedef struct linecallinfo_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    HLINE hLine;
    DWORD dwLineDeviceID;
    DWORD dwAddressID;
    DWORD dwBearerMode;
    DWORD dwRate;
    DWORD dwMediaMode;
    DWORD dwAppSpecific;
    DWORD dwCallID;
    DWORD dwRelatedCallID;
    DWORD dwCallParamFlags;
    DWORD dwCallStates;
    DWORD dwMonitorDigitModes;
    DWORD dwMonitorMediaModes;
    LINEDIALPARAMS DialParams;
    DWORD dwOrigin;
    DWORD dwReason;
    DWORD dwCompletionID;
    DWORD dwNumOwners;
    DWORD dwNumMonitors;
    DWORD dwCountryCode;
    DWORD dwTrunk;
    DWORD dwCallerIDFlags;
    DWORD dwCallerIDSize;
    DWORD dwCallerIDOffset;
    DWORD dwCallerIDNameSize;
    DWORD dwCallerIDNameOffset;
    DWORD dwCalledIDFlags;
    DWORD dwCalledIDSize;
    DWORD dwCalledIDOffset;
    DWORD dwCalledIDNameSize;
    DWORD dwCalledIDNameOffset;
    DWORD dwConnectedIDFlags;
    DWORD dwConnectedIDSize;
    DWORD dwConnectedIDOffset;
    DWORD dwConnectedIDNameSize;
    DWORD dwConnectedIDNameOffset;
    DWORD dwRedirectionIDFlags;
    DWORD dwRedirectionIDSize;
    DWORD dwRedirectionIDOffset;
    DWORD dwRedirectionIDNameSize;
    DWORD dwRedirectionIDNameOffset;
    DWORD dwRedirectingIDFlags;
    DWORD dwRedirectingIDSize;
    DWORD dwRedirectingIDOffset;
    DWORD dwRedirectingIDNameSize;
    DWORD dwRedirectingIDNameOffset;
    DWORD dwAppNameSize;
    DWORD dwAppNameOffset;
    DWORD dwDisplayableAddressSize;
    DWORD dwDisplayableAddressOffset;
    DWORD dwCalledPartySize;
    DWORD dwCalledPartyOffset;
    DWORD dwCommentSize;
    DWORD dwCommentOffset;
    DWORD dwDisplaySize;
    DWORD dwDisplayOffset;
    DWORD dwUserUserInfoSize;
    DWORD dwUserUserInfoOffset;
    DWORD dwHighLevelCompSize;
    DWORD dwHighLevelCompOffset;
    DWORD dwLowLevelCompSize;
    DWORD dwLowLevelCompOffset;
    DWORD dwChargingInfoSize;
    DWORD dwChargingInfoOffset;
    DWORD dwTerminalModesSize;
    DWORD dwTerminalModesOffset;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
    DWORD dwCallTreatment;
    DWORD dwCallDataSize;
    DWORD dwCallDataOffset;
    DWORD dwSendingFlowspecSize;
    DWORD dwSendingFlowspecOffset;
    DWORD dwReceivingFlowspecSize;
    DWORD dwReceivingFlowspecOffset;
    DWORD dwCallerIDAddressType;
    DWORD dwCalledIDAddressType;
    DWORD dwConnectedIDAddressType;
    DWORD dwRedirectionIDAddressType;
    DWORD dwRedirectingIDAddressType;
  } LINECALLINFO,*LPLINECALLINFO;

  typedef struct linecalllist_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwCallsNumEntries;
    DWORD dwCallsSize;
    DWORD dwCallsOffset;
  } LINECALLLIST,*LPLINECALLLIST;

  typedef struct linecallparams_tag {
    DWORD dwTotalSize;
    DWORD dwBearerMode;
    DWORD dwMinRate;
    DWORD dwMaxRate;
    DWORD dwMediaMode;
    DWORD dwCallParamFlags;
    DWORD dwAddressMode;
    DWORD dwAddressID;
    LINEDIALPARAMS DialParams;
    DWORD dwOrigAddressSize;
    DWORD dwOrigAddressOffset;
    DWORD dwDisplayableAddressSize;
    DWORD dwDisplayableAddressOffset;
    DWORD dwCalledPartySize;
    DWORD dwCalledPartyOffset;
    DWORD dwCommentSize;
    DWORD dwCommentOffset;
    DWORD dwUserUserInfoSize;
    DWORD dwUserUserInfoOffset;
    DWORD dwHighLevelCompSize;
    DWORD dwHighLevelCompOffset;
    DWORD dwLowLevelCompSize;
    DWORD dwLowLevelCompOffset;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
    DWORD dwPredictiveAutoTransferStates;
    DWORD dwTargetAddressSize;
    DWORD dwTargetAddressOffset;
    DWORD dwSendingFlowspecSize;
    DWORD dwSendingFlowspecOffset;
    DWORD dwReceivingFlowspecSize;
    DWORD dwReceivingFlowspecOffset;
    DWORD dwDeviceClassSize;
    DWORD dwDeviceClassOffset;
    DWORD dwDeviceConfigSize;
    DWORD dwDeviceConfigOffset;
    DWORD dwCallDataSize;
    DWORD dwCallDataOffset;
    DWORD dwNoAnswerTimeout;
    DWORD dwCallingPartyIDSize;
    DWORD dwCallingPartyIDOffset;
    DWORD dwAddressType;
  } LINECALLPARAMS,*LPLINECALLPARAMS;

  typedef struct linecallstatus_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwCallState;
    DWORD dwCallStateMode;
    DWORD dwCallPrivilege;
    DWORD dwCallFeatures;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
    DWORD dwCallFeatures2;
    SYSTEMTIME tStateEntryTime;
  } LINECALLSTATUS,*LPLINECALLSTATUS;

  typedef struct linecalltreatmententry_tag {
    DWORD dwCallTreatmentID;
    DWORD dwCallTreatmentNameSize;
    DWORD dwCallTreatmentNameOffset;
  } LINECALLTREATMENTENTRY,*LPLINECALLTREATMENTENTRY;

  typedef struct linecardentry_tag {
    DWORD dwPermanentCardID;
    DWORD dwCardNameSize;
    DWORD dwCardNameOffset;
    DWORD dwCardNumberDigits;
    DWORD dwSameAreaRuleSize;
    DWORD dwSameAreaRuleOffset;
    DWORD dwLongDistanceRuleSize;
    DWORD dwLongDistanceRuleOffset;
    DWORD dwInternationalRuleSize;
    DWORD dwInternationalRuleOffset;
    DWORD dwOptions;
  } LINECARDENTRY,*LPLINECARDENTRY;

  typedef struct linecountryentry_tag {
    DWORD dwCountryID;
    DWORD dwCountryCode;
    DWORD dwNextCountryID;
    DWORD dwCountryNameSize;
    DWORD dwCountryNameOffset;
    DWORD dwSameAreaRuleSize;
    DWORD dwSameAreaRuleOffset;
    DWORD dwLongDistanceRuleSize;
    DWORD dwLongDistanceRuleOffset;
    DWORD dwInternationalRuleSize;
    DWORD dwInternationalRuleOffset;
  } LINECOUNTRYENTRY,*LPLINECOUNTRYENTRY;

  typedef struct linecountrylist_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumCountries;
    DWORD dwCountryListSize;
    DWORD dwCountryListOffset;
  } LINECOUNTRYLIST,*LPLINECOUNTRYLIST;

  typedef struct linedevcaps_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwProviderInfoSize;
    DWORD dwProviderInfoOffset;
    DWORD dwSwitchInfoSize;
    DWORD dwSwitchInfoOffset;
    DWORD dwPermanentLineID;
    DWORD dwLineNameSize;
    DWORD dwLineNameOffset;
    DWORD dwStringFormat;
    DWORD dwAddressModes;
    DWORD dwNumAddresses;
    DWORD dwBearerModes;
    DWORD dwMaxRate;
    DWORD dwMediaModes;
    DWORD dwGenerateToneModes;
    DWORD dwGenerateToneMaxNumFreq;
    DWORD dwGenerateDigitModes;
    DWORD dwMonitorToneMaxNumFreq;
    DWORD dwMonitorToneMaxNumEntries;
    DWORD dwMonitorDigitModes;
    DWORD dwGatherDigitsMinTimeout;
    DWORD dwGatherDigitsMaxTimeout;
    DWORD dwMedCtlDigitMaxListSize;
    DWORD dwMedCtlMediaMaxListSize;
    DWORD dwMedCtlToneMaxListSize;
    DWORD dwMedCtlCallStateMaxListSize;
    DWORD dwDevCapFlags;
    DWORD dwMaxNumActiveCalls;
    DWORD dwAnswerMode;
    DWORD dwRingModes;
    DWORD dwLineStates;
    DWORD dwUUIAcceptSize;
    DWORD dwUUIAnswerSize;
    DWORD dwUUIMakeCallSize;
    DWORD dwUUIDropSize;
    DWORD dwUUISendUserUserInfoSize;
    DWORD dwUUICallInfoSize;
    LINEDIALPARAMS MinDialParams;
    LINEDIALPARAMS MaxDialParams;
    LINEDIALPARAMS DefaultDialParams;
    DWORD dwNumTerminals;
    DWORD dwTerminalCapsSize;
    DWORD dwTerminalCapsOffset;
    DWORD dwTerminalTextEntrySize;
    DWORD dwTerminalTextSize;
    DWORD dwTerminalTextOffset;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
    DWORD dwLineFeatures;
    DWORD dwSettableDevStatus;
    DWORD dwDeviceClassesSize;
    DWORD dwDeviceClassesOffset;
    GUID PermanentLineGuid;
    DWORD dwAddressTypes;
    GUID ProtocolGuid;
    DWORD dwAvailableTracking;
  } LINEDEVCAPS,*LPLINEDEVCAPS;

  typedef struct linedevstatus_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumOpens;
    DWORD dwOpenMediaModes;
    DWORD dwNumActiveCalls;
    DWORD dwNumOnHoldCalls;
    DWORD dwNumOnHoldPendCalls;
    DWORD dwLineFeatures;
    DWORD dwNumCallCompletions;
    DWORD dwRingMode;
    DWORD dwSignalLevel;
    DWORD dwBatteryLevel;
    DWORD dwRoamMode;
    DWORD dwDevStatusFlags;
    DWORD dwTerminalModesSize;
    DWORD dwTerminalModesOffset;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
    DWORD dwAvailableMediaModes;
    DWORD dwAppInfoSize;
    DWORD dwAppInfoOffset;
  } LINEDEVSTATUS,*LPLINEDEVSTATUS;

  typedef struct lineextensionid_tag {
    DWORD dwExtensionID0;
    DWORD dwExtensionID1;
    DWORD dwExtensionID2;
    DWORD dwExtensionID3;
  } LINEEXTENSIONID,*LPLINEEXTENSIONID;

  typedef struct lineforward_tag {
    DWORD dwForwardMode;
    DWORD dwCallerAddressSize;
    DWORD dwCallerAddressOffset;
    DWORD dwDestCountryCode;
    DWORD dwDestAddressSize;
    DWORD dwDestAddressOffset;
    DWORD dwCallerAddressType;
    DWORD dwDestAddressType;
  } LINEFORWARD,*LPLINEFORWARD;

  typedef struct lineforwardlist_tag {
    DWORD dwTotalSize;
    DWORD dwNumEntries;
    LINEFORWARD ForwardList[1];
  } LINEFORWARDLIST,*LPLINEFORWARDLIST;

  typedef struct linegeneratetone_tag {
    DWORD dwFrequency;
    DWORD dwCadenceOn;
    DWORD dwCadenceOff;
    DWORD dwVolume;
  } LINEGENERATETONE,*LPLINEGENERATETONE;

  typedef struct lineinitializeexparams_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwOptions;
    union {
      HANDLE hEvent;
      HANDLE hCompletionPort;
    } Handles;
    DWORD dwCompletionKey;
  } LINEINITIALIZEEXPARAMS,*LPLINEINITIALIZEEXPARAMS;

  typedef struct linelocationentry_tag {
    DWORD dwPermanentLocationID;
    DWORD dwLocationNameSize;
    DWORD dwLocationNameOffset;
    DWORD dwCountryCode;
    DWORD dwCityCodeSize;
    DWORD dwCityCodeOffset;
    DWORD dwPreferredCardID;
    DWORD dwLocalAccessCodeSize;
    DWORD dwLocalAccessCodeOffset;
    DWORD dwLongDistanceAccessCodeSize;
    DWORD dwLongDistanceAccessCodeOffset;
    DWORD dwTollPrefixListSize;
    DWORD dwTollPrefixListOffset;
    DWORD dwCountryID;
    DWORD dwOptions;
    DWORD dwCancelCallWaitingSize;
    DWORD dwCancelCallWaitingOffset;
  } LINELOCATIONENTRY,*LPLINELOCATIONENTRY;

  typedef struct linemediacontrolcallstate_tag {
    DWORD dwCallStates;
    DWORD dwMediaControl;
  } LINEMEDIACONTROLCALLSTATE,*LPLINEMEDIACONTROLCALLSTATE;

  typedef struct linemediacontroldigit_tag {
    DWORD dwDigit;
    DWORD dwDigitModes;
    DWORD dwMediaControl;
  } LINEMEDIACONTROLDIGIT,*LPLINEMEDIACONTROLDIGIT;

  typedef struct linemediacontrolmedia_tag {
    DWORD dwMediaModes;
    DWORD dwDuration;
    DWORD dwMediaControl;
  } LINEMEDIACONTROLMEDIA,*LPLINEMEDIACONTROLMEDIA;

  typedef struct linemediacontroltone_tag {
    DWORD dwAppSpecific;
    DWORD dwDuration;
    DWORD dwFrequency1;
    DWORD dwFrequency2;
    DWORD dwFrequency3;
    DWORD dwMediaControl;
  } LINEMEDIACONTROLTONE,*LPLINEMEDIACONTROLTONE;

  typedef struct linemessage_tag {
    DWORD hDevice;
    DWORD dwMessageID;
    DWORD_PTR dwCallbackInstance;
    DWORD_PTR dwParam1;
    DWORD_PTR dwParam2;
    DWORD_PTR dwParam3;
  } LINEMESSAGE,*LPLINEMESSAGE;

  typedef struct linemonitortone_tag {
    DWORD dwAppSpecific;
    DWORD dwDuration;
    DWORD dwFrequency1;
    DWORD dwFrequency2;
    DWORD dwFrequency3;
  } LINEMONITORTONE,*LPLINEMONITORTONE;

  typedef struct lineproviderentry_tag {
    DWORD dwPermanentProviderID;
    DWORD dwProviderFilenameSize;
    DWORD dwProviderFilenameOffset;
  } LINEPROVIDERENTRY,*LPLINEPROVIDERENTRY;

  typedef struct lineproviderlist_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumProviders;
    DWORD dwProviderListSize;
    DWORD dwProviderListOffset;
  } LINEPROVIDERLIST,*LPLINEPROVIDERLIST;

  typedef struct lineproxyrequest_tag {
    DWORD dwSize;
    DWORD dwClientMachineNameSize;
    DWORD dwClientMachineNameOffset;
    DWORD dwClientUserNameSize;
    DWORD dwClientUserNameOffset;
    DWORD dwClientAppAPIVersion;
    DWORD dwRequestType;
    __C89_NAMELESS union {
      struct {
	DWORD dwAddressID;
	LINEAGENTGROUPLIST GroupList;
      } SetAgentGroup;
      struct {
	DWORD dwAddressID;
	DWORD dwAgentState;
	DWORD dwNextAgentState;
      } SetAgentState;
      struct {
	DWORD dwAddressID;
	DWORD dwActivityID;
      } SetAgentActivity;
      struct {
	DWORD dwAddressID;
	LINEAGENTCAPS AgentCaps;
      } GetAgentCaps;
      struct {
	DWORD dwAddressID;
	LINEAGENTSTATUS AgentStatus;
      } GetAgentStatus;
      struct {
	DWORD dwAddressID;
	DWORD dwAgentExtensionIDIndex;
	DWORD dwSize;
	BYTE Params[1];
      } AgentSpecific;
      struct {
	DWORD dwAddressID;
	LINEAGENTACTIVITYLIST ActivityList;
      } GetAgentActivityList;
      struct {
	DWORD dwAddressID;
	LINEAGENTGROUPLIST GroupList;
      } GetAgentGroupList;
      struct {
	HAGENT hAgent;
	DWORD dwAgentIDSize;
	DWORD dwAgentIDOffset;
	DWORD dwAgentPINSize;
	DWORD dwAgentPINOffset;
      } CreateAgent;
      struct {
	HAGENT hAgent;
	DWORD dwAgentState;
	DWORD dwNextAgentState;
      } SetAgentStateEx;
      struct {
	HAGENT hAgent;
	DWORD dwMeasurementPeriod;
      } SetAgentMeasurementPeriod;
      struct {
	HAGENT hAgent;
	LINEAGENTINFO AgentInfo;
      } GetAgentInfo;
      struct {
	HAGENTSESSION hAgentSession;
	DWORD dwAgentPINSize;
	DWORD dwAgentPINOffset;
	HAGENT hAgent;
	GUID GroupID;
	DWORD dwWorkingAddressID;
      } CreateAgentSession;
      struct {
	HAGENT hAgent;
	LINEAGENTSESSIONLIST SessionList;
      } GetAgentSessionList;
      struct {
	HAGENTSESSION hAgentSession;
	LINEAGENTSESSIONINFO SessionInfo;
      } GetAgentSessionInfo;
      struct {
	HAGENTSESSION hAgentSession;
	DWORD dwAgentSessionState;
	DWORD dwNextAgentSessionState;
      } SetAgentSessionState;
      struct {
	GUID GroupID;
	LINEQUEUELIST QueueList;
      } GetQueueList;
      struct {
	DWORD dwQueueID;
	DWORD dwMeasurementPeriod;
      } SetQueueMeasurementPeriod;
      struct {
	DWORD dwQueueID;
	LINEQUEUEINFO QueueInfo;
      } GetQueueInfo;
      struct {
	LINEAGENTGROUPLIST GroupList;
      } GetGroupList;
    };
  } LINEPROXYREQUEST,*LPLINEPROXYREQUEST;

  typedef struct linereqmakecall_tag {
    char szDestAddress[TAPIMAXDESTADDRESSSIZE];
    char szAppName[TAPIMAXAPPNAMESIZE];
    char szCalledParty[TAPIMAXCALLEDPARTYSIZE];
    char szComment[TAPIMAXCOMMENTSIZE];
  } LINEREQMAKECALL,*LPLINEREQMAKECALL;

  typedef struct linereqmakecallW_tag {
    WCHAR szDestAddress[TAPIMAXDESTADDRESSSIZE];
    WCHAR szAppName[TAPIMAXAPPNAMESIZE];
    WCHAR szCalledParty[TAPIMAXCALLEDPARTYSIZE];
    WCHAR szComment[TAPIMAXCOMMENTSIZE];
  } LINEREQMAKECALLW,*LPLINEREQMAKECALLW;

#if defined(UNICODE)
#define LINEREQMAKECALL LINEREQMAKECALLW
#endif

  typedef struct linereqmediacall_tag {
    HWND hWnd;
    WPARAM wRequestID;
    char szDeviceClass[TAPIMAXDEVICECLASSSIZE];
    unsigned char ucDeviceID[TAPIMAXDEVICEIDSIZE];
    DWORD dwSize;
    DWORD dwSecure;
    char szDestAddress[TAPIMAXDESTADDRESSSIZE];
    char szAppName[TAPIMAXAPPNAMESIZE];
    char szCalledParty[TAPIMAXCALLEDPARTYSIZE];
    char szComment[TAPIMAXCOMMENTSIZE];
  } LINEREQMEDIACALL,*LPLINEREQMEDIACALL;

  typedef struct linereqmediacallW_tag {
    HWND hWnd;
    WPARAM wRequestID;
    WCHAR szDeviceClass[TAPIMAXDEVICECLASSSIZE];
    unsigned char ucDeviceID[TAPIMAXDEVICEIDSIZE];
    DWORD dwSize;
    DWORD dwSecure;
    WCHAR szDestAddress[TAPIMAXDESTADDRESSSIZE];
    WCHAR szAppName[TAPIMAXAPPNAMESIZE];
    WCHAR szCalledParty[TAPIMAXCALLEDPARTYSIZE];
    WCHAR szComment[TAPIMAXCOMMENTSIZE];
  } LINEREQMEDIACALLW,*LPLINEREQMEDIACALLW;

#if defined(UNICODE)
#define LINEREQMEDIACALL LINEREQMEDIACALLW
#endif

  typedef struct linetermcaps_tag {
    DWORD dwTermDev;
    DWORD dwTermModes;
    DWORD dwTermSharing;
  } LINETERMCAPS,*LPLINETERMCAPS;

  typedef struct linetranslatecaps_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwNumLocations;
    DWORD dwLocationListSize;
    DWORD dwLocationListOffset;
    DWORD dwCurrentLocationID;
    DWORD dwNumCards;
    DWORD dwCardListSize;
    DWORD dwCardListOffset;
    DWORD dwCurrentPreferredCardID;
  } LINETRANSLATECAPS,*LPLINETRANSLATECAPS;

  typedef struct linetranslateoutput_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwDialableStringSize;
    DWORD dwDialableStringOffset;
    DWORD dwDisplayableStringSize;
    DWORD dwDisplayableStringOffset;
    DWORD dwCurrentCountry;
    DWORD dwDestCountry;
    DWORD dwTranslateResults;
  } LINETRANSLATEOUTPUT,*LPLINETRANSLATEOUTPUT;

  typedef struct phonebuttoninfo_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwButtonMode;
    DWORD dwButtonFunction;
    DWORD dwButtonTextSize;
    DWORD dwButtonTextOffset;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
    DWORD dwButtonState;
  } PHONEBUTTONINFO,*LPPHONEBUTTONINFO;

  typedef struct phonecaps_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwProviderInfoSize;
    DWORD dwProviderInfoOffset;
    DWORD dwPhoneInfoSize;
    DWORD dwPhoneInfoOffset;
    DWORD dwPermanentPhoneID;
    DWORD dwPhoneNameSize;
    DWORD dwPhoneNameOffset;
    DWORD dwStringFormat;
    DWORD dwPhoneStates;
    DWORD dwHookSwitchDevs;
    DWORD dwHandsetHookSwitchModes;
    DWORD dwSpeakerHookSwitchModes;
    DWORD dwHeadsetHookSwitchModes;
    DWORD dwVolumeFlags;
    DWORD dwGainFlags;
    DWORD dwDisplayNumRows;
    DWORD dwDisplayNumColumns;
    DWORD dwNumRingModes;
    DWORD dwNumButtonLamps;
    DWORD dwButtonModesSize;
    DWORD dwButtonModesOffset;
    DWORD dwButtonFunctionsSize;
    DWORD dwButtonFunctionsOffset;
    DWORD dwLampModesSize;
    DWORD dwLampModesOffset;
    DWORD dwNumSetData;
    DWORD dwSetDataSize;
    DWORD dwSetDataOffset;
    DWORD dwNumGetData;
    DWORD dwGetDataSize;
    DWORD dwGetDataOffset;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
    DWORD dwDeviceClassesSize;
    DWORD dwDeviceClassesOffset;
    DWORD dwPhoneFeatures;
    DWORD dwSettableHandsetHookSwitchModes;
    DWORD dwSettableSpeakerHookSwitchModes;
    DWORD dwSettableHeadsetHookSwitchModes;
    DWORD dwMonitoredHandsetHookSwitchModes;
    DWORD dwMonitoredSpeakerHookSwitchModes;
    DWORD dwMonitoredHeadsetHookSwitchModes;
    GUID PermanentPhoneGuid;
  } PHONECAPS,*LPPHONECAPS;

  typedef struct phoneextensionid_tag {
    DWORD dwExtensionID0;
    DWORD dwExtensionID1;
    DWORD dwExtensionID2;
    DWORD dwExtensionID3;
  } PHONEEXTENSIONID,*LPPHONEEXTENSIONID;

  typedef struct phoneinitializeexparams_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwOptions;
    union {
      HANDLE hEvent;
      HANDLE hCompletionPort;
    } Handles;
    DWORD dwCompletionKey;
  } PHONEINITIALIZEEXPARAMS,*LPPHONEINITIALIZEEXPARAMS;

  typedef struct phonemessage_tag {
    DWORD hDevice;
    DWORD dwMessageID;
    DWORD_PTR dwCallbackInstance;
    DWORD_PTR dwParam1;
    DWORD_PTR dwParam2;
    DWORD_PTR dwParam3;
  } PHONEMESSAGE,*LPPHONEMESSAGE;

  typedef struct phonestatus_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwStatusFlags;
    DWORD dwNumOwners;
    DWORD dwNumMonitors;
    DWORD dwRingMode;
    DWORD dwRingVolume;
    DWORD dwHandsetHookSwitchMode;
    DWORD dwHandsetVolume;
    DWORD dwHandsetGain;
    DWORD dwSpeakerHookSwitchMode;
    DWORD dwSpeakerVolume;
    DWORD dwSpeakerGain;
    DWORD dwHeadsetHookSwitchMode;
    DWORD dwHeadsetVolume;
    DWORD dwHeadsetGain;
    DWORD dwDisplaySize;
    DWORD dwDisplayOffset;
    DWORD dwLampModesSize;
    DWORD dwLampModesOffset;
    DWORD dwOwnerNameSize;
    DWORD dwOwnerNameOffset;
    DWORD dwDevSpecificSize;
    DWORD dwDevSpecificOffset;
    DWORD dwPhoneFeatures;
  } PHONESTATUS,*LPPHONESTATUS;

  typedef struct varstring_tag {
    DWORD dwTotalSize;
    DWORD dwNeededSize;
    DWORD dwUsedSize;
    DWORD dwStringFormat;
    DWORD dwStringSize;
    DWORD dwStringOffset;
  } VARSTRING,*LPVARSTRING;

  LONG WINAPI lineAccept(HCALL hCall,LPCSTR lpsUserUserInfo,DWORD dwSize);
  LONG WINAPI lineAddProvider(LPCSTR lpszProviderFilename,HWND hwndOwner,LPDWORD lpdwPermanentProviderID);
  LONG WINAPI lineAddProviderA(LPCSTR lpszProviderFilename,HWND hwndOwner,LPDWORD lpdwPermanentProviderID);
  LONG WINAPI lineAddProviderW(LPCWSTR lpszProviderFilename,HWND hwndOwner,LPDWORD lpdwPermanentProviderID);
  LONG WINAPI lineAddToConference(HCALL hConfCall,HCALL hConsultCall);
  LONG WINAPI lineAgentSpecific(HLINE hLine,DWORD dwAddressID,DWORD dwAgentExtensionIDIndex,LPVOID lpParams,DWORD dwSize);
  LONG WINAPI lineAnswer(HCALL hCall,LPCSTR lpsUserUserInfo,DWORD dwSize);
  LONG WINAPI lineBlindTransfer(HCALL hCall,LPCSTR lpszDestAddress,DWORD dwCountryCode);
  LONG WINAPI lineBlindTransferA(HCALL hCall,LPCSTR lpszDestAddress,DWORD dwCountryCode);
  LONG WINAPI lineBlindTransferW(HCALL hCall,LPCWSTR lpszDestAddressW,DWORD dwCountryCode);
  LONG WINAPI lineClose(HLINE hLine);
  LONG WINAPI lineCompleteCall(HCALL hCall,LPDWORD lpdwCompletionID,DWORD dwCompletionMode,DWORD dwMessageID);
  LONG WINAPI lineCompleteTransfer(HCALL hCall,HCALL hConsultCall,LPHCALL lphConfCall,DWORD dwTransferMode);
  LONG WINAPI lineConfigDialog(DWORD dwDeviceID,HWND hwndOwner,LPCSTR lpszDeviceClass);
  LONG WINAPI lineConfigDialogA(DWORD dwDeviceID,HWND hwndOwner,LPCSTR lpszDeviceClass);
  LONG WINAPI lineConfigDialogW(DWORD dwDeviceID,HWND hwndOwner,LPCWSTR lpszDeviceClass);
  LONG WINAPI lineConfigDialogEdit(DWORD dwDeviceID,HWND hwndOwner,LPCSTR lpszDeviceClass,LPVOID const lpDeviceConfigIn,DWORD dwSize,LPVARSTRING lpDeviceConfigOut);
  LONG WINAPI lineConfigDialogEditA(DWORD dwDeviceID,HWND hwndOwner,LPCSTR lpszDeviceClass,LPVOID const lpDeviceConfigIn,DWORD dwSize,LPVARSTRING lpDeviceConfigOut);
  LONG WINAPI lineConfigDialogEditW(DWORD dwDeviceID,HWND hwndOwner,LPCWSTR lpszDeviceClass,LPVOID const lpDeviceConfigIn,DWORD dwSize,LPVARSTRING lpDeviceConfigOut);
  LONG WINAPI lineConfigProvider(HWND hwndOwner,DWORD dwPermanentProviderID);
  LONG WINAPI lineCreateAgentW(HLINE hLine,LPWSTR lpszAgentID,LPWSTR lpszAgentPIN,LPHAGENT lphAgent);
  LONG WINAPI lineCreateAgentA(HLINE hLine,LPSTR lpszAgentID,LPSTR lpszAgentPIN,LPHAGENT lphAgent);
  LONG WINAPI lineCreateAgentSessionW(HLINE hLine,HAGENT hAgent,LPWSTR lpszAgentPIN,DWORD dwWorkingAddressID,LPGUID lpGroupID,LPHAGENTSESSION lphAgentSession);
  LONG WINAPI lineCreateAgentSessionA(HLINE hLine,HAGENT hAgent,LPSTR lpszAgentPIN,DWORD dwWorkingAddressID,LPGUID lpGroupID,LPHAGENTSESSION lphAgentSession);
  LONG WINAPI lineDeallocateCall(HCALL hCall);
  LONG WINAPI lineDevSpecific(HLINE hLine,DWORD dwAddressID,HCALL hCall,LPVOID lpParams,DWORD dwSize);
  LONG WINAPI lineDevSpecificFeature(HLINE hLine,DWORD dwFeature,LPVOID lpParams,DWORD dwSize);
  LONG WINAPI lineDial(HCALL hCall,LPCSTR lpszDestAddress,DWORD dwCountryCode);
  LONG WINAPI lineDialA(HCALL hCall,LPCSTR lpszDestAddress,DWORD dwCountryCode);
  LONG WINAPI lineDialW(HCALL hCall,LPCWSTR lpszDestAddress,DWORD dwCountryCode);
  LONG WINAPI lineDrop(HCALL hCall,LPCSTR lpsUserUserInfo,DWORD dwSize);
  LONG WINAPI lineForward(HLINE hLine,DWORD bAllAddresses,DWORD dwAddressID,LPLINEFORWARDLIST const lpForwardList,DWORD dwNumRingsNoAnswer,LPHCALL lphConsultCall,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineForwardA(HLINE hLine,DWORD bAllAddresses,DWORD dwAddressID,LPLINEFORWARDLIST const lpForwardList,DWORD dwNumRingsNoAnswer,LPHCALL lphConsultCall,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineForwardW(HLINE hLine,DWORD bAllAddresses,DWORD dwAddressID,LPLINEFORWARDLIST const lpForwardList,DWORD dwNumRingsNoAnswer,LPHCALL lphConsultCall,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineGatherDigits(HCALL hCall,DWORD dwDigitModes,LPSTR lpsDigits,DWORD dwNumDigits,LPCSTR lpszTerminationDigits,DWORD dwFirstDigitTimeout,DWORD dwInterDigitTimeout);
  LONG WINAPI lineGatherDigitsA(HCALL hCall,DWORD dwDigitModes,LPSTR lpsDigits,DWORD dwNumDigits,LPCSTR lpszTerminationDigits,DWORD dwFirstDigitTimeout,DWORD dwInterDigitTimeout);
  LONG WINAPI lineGatherDigitsW(HCALL hCall,DWORD dwDigitModes,LPWSTR lpsDigits,DWORD dwNumDigits,LPCWSTR lpszTerminationDigits,DWORD dwFirstDigitTimeout,DWORD dwInterDigitTimeout);
  LONG WINAPI lineGenerateDigits(HCALL hCall,DWORD dwDigitMode,LPCSTR lpszDigits,DWORD dwDuration);
  LONG WINAPI lineGenerateDigitsA(HCALL hCall,DWORD dwDigitMode,LPCSTR lpszDigits,DWORD dwDuration);
  LONG WINAPI lineGenerateDigitsW(HCALL hCall,DWORD dwDigitMode,LPCWSTR lpszDigits,DWORD dwDuration);
  LONG WINAPI lineGenerateTone(HCALL hCall,DWORD dwToneMode,DWORD dwDuration,DWORD dwNumTones,LPLINEGENERATETONE const lpTones);
  LONG WINAPI lineGetAddressCaps(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAddressID,DWORD dwAPIVersion,DWORD dwExtVersion,LPLINEADDRESSCAPS lpAddressCaps);
  LONG WINAPI lineGetAddressCapsA(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAddressID,DWORD dwAPIVersion,DWORD dwExtVersion,LPLINEADDRESSCAPS lpAddressCaps);
  LONG WINAPI lineGetAddressCapsW(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAddressID,DWORD dwAPIVersion,DWORD dwExtVersion,LPLINEADDRESSCAPS lpAddressCaps);
  LONG WINAPI lineGetAddressID(HLINE hLine,LPDWORD lpdwAddressID,DWORD dwAddressMode,LPCSTR lpsAddress,DWORD dwSize);
  LONG WINAPI lineGetAddressIDA(HLINE hLine,LPDWORD lpdwAddressID,DWORD dwAddressMode,LPCSTR lpsAddress,DWORD dwSize);
  LONG WINAPI lineGetAddressIDW(HLINE hLine,LPDWORD lpdwAddressID,DWORD dwAddressMode,LPCWSTR lpsAddress,DWORD dwSize);
  LONG WINAPI lineGetAddressStatus(HLINE hLine,DWORD dwAddressID,LPLINEADDRESSSTATUS lpAddressStatus);
  LONG WINAPI lineGetAddressStatusA(HLINE hLine,DWORD dwAddressID,LPLINEADDRESSSTATUS lpAddressStatus);
  LONG WINAPI lineGetAddressStatusW(HLINE hLine,DWORD dwAddressID,LPLINEADDRESSSTATUS lpAddressStatus);
  LONG WINAPI lineGetAgentActivityListA(HLINE hLine,DWORD dwAddressID,LPLINEAGENTACTIVITYLIST lpAgentActivityList);
  LONG WINAPI lineGetAgentActivityListW(HLINE hLine,DWORD dwAddressID,LPLINEAGENTACTIVITYLIST lpAgentActivityList);
  LONG WINAPI lineGetAgentCapsA(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAddressID,DWORD dwAppAPIVersion,LPLINEAGENTCAPS lpAgentCaps);
  LONG WINAPI lineGetAgentCapsW(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAddressID,DWORD dwAppAPIVersion,LPLINEAGENTCAPS lpAgentCaps);
  LONG WINAPI lineGetAgentGroupListA(HLINE hLine,DWORD dwAddressID,LPLINEAGENTGROUPLIST lpAgentGroupList);
  LONG WINAPI lineGetAgentGroupListW(HLINE hLine,DWORD dwAddressID,LPLINEAGENTGROUPLIST lpAgentGroupList);
  LONG WINAPI lineGetAgentInfo(HLINE hLine,HAGENT hAgent,LPLINEAGENTINFO lpAgentInfo);
  LONG WINAPI lineGetAgentSessionInfo(HLINE hLine,HAGENTSESSION hAgentSession,LPLINEAGENTSESSIONINFO lpAgentSessionInfo);
  LONG WINAPI lineGetAgentSessionList(HLINE hLine,HAGENT hAgent,LPLINEAGENTSESSIONLIST lpAgentSessionList);
  LONG WINAPI lineGetAgentStatusA(HLINE hLine,DWORD dwAddressID,LPLINEAGENTSTATUS lpAgentStatus);
  LONG WINAPI lineGetAgentStatusW(HLINE hLine,DWORD dwAddressID,LPLINEAGENTSTATUS lpAgentStatus);
  LONG WINAPI lineGetAppPriority(LPCSTR lpszAppFilename,DWORD dwMediaMode,LPLINEEXTENSIONID lpExtensionID,DWORD dwRequestMode,LPVARSTRING lpExtensionName,LPDWORD lpdwPriority);
  LONG WINAPI lineGetAppPriorityA(LPCSTR lpszAppFilename,DWORD dwMediaMode,LPLINEEXTENSIONID lpExtensionID,DWORD dwRequestMode,LPVARSTRING lpExtensionName,LPDWORD lpdwPriority);
  LONG WINAPI lineGetAppPriorityW(LPCWSTR lpszAppFilename,DWORD dwMediaMode,LPLINEEXTENSIONID lpExtensionID,DWORD dwRequestMode,LPVARSTRING lpExtensionName,LPDWORD lpdwPriority);
  LONG WINAPI lineGetCallInfo(HCALL hCall,LPLINECALLINFO lpCallInfo);
  LONG WINAPI lineGetCallInfoA(HCALL hCall,LPLINECALLINFO lpCallInfo);
  LONG WINAPI lineGetCallInfoW(HCALL hCall,LPLINECALLINFO lpCallInfo);
  LONG WINAPI lineGetCallStatus(HCALL hCall,LPLINECALLSTATUS lpCallStatus);
  LONG WINAPI lineGetConfRelatedCalls(HCALL hCall,LPLINECALLLIST lpCallList);
  LONG WINAPI lineGetCountry(DWORD dwCountryID,DWORD dwAPIVersion,LPLINECOUNTRYLIST lpLineCountryList);
  LONG WINAPI lineGetCountryA(DWORD dwCountryID,DWORD dwAPIVersion,LPLINECOUNTRYLIST lpLineCountryList);
  LONG WINAPI lineGetCountryW(DWORD dwCountryID,DWORD dwAPIVersion,LPLINECOUNTRYLIST lpLineCountryList);
  LONG WINAPI lineGetDevCaps(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,DWORD dwExtVersion,LPLINEDEVCAPS lpLineDevCaps);
  LONG WINAPI lineGetDevCapsA(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,DWORD dwExtVersion,LPLINEDEVCAPS lpLineDevCaps);
  LONG WINAPI lineGetDevCapsW(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,DWORD dwExtVersion,LPLINEDEVCAPS lpLineDevCaps);
  LONG WINAPI lineGetDevConfig(DWORD dwDeviceID,LPVARSTRING lpDeviceConfig,LPCSTR lpszDeviceClass);
  LONG WINAPI lineGetDevConfigA(DWORD dwDeviceID,LPVARSTRING lpDeviceConfig,LPCSTR lpszDeviceClass);
  LONG WINAPI lineGetDevConfigW(DWORD dwDeviceID,LPVARSTRING lpDeviceConfig,LPCWSTR lpszDeviceClass);
  LONG WINAPI lineGetGroupListA(HLINE hLine,LPLINEAGENTGROUPLIST lpGroupList);
  LONG WINAPI lineGetGroupListW(HLINE hLine,LPLINEAGENTGROUPLIST lpGroupList);
  LONG WINAPI lineGetIcon(DWORD dwDeviceID,LPCSTR lpszDeviceClass,LPHICON lphIcon);
  LONG WINAPI lineGetIconA(DWORD dwDeviceID,LPCSTR lpszDeviceClass,LPHICON lphIcon);
  LONG WINAPI lineGetIconW(DWORD dwDeviceID,LPCWSTR lpszDeviceClass,LPHICON lphIcon);
  LONG WINAPI lineGetID(HLINE hLine,DWORD dwAddressID,HCALL hCall,DWORD dwSelect,LPVARSTRING lpDeviceID,LPCSTR lpszDeviceClass);
  LONG WINAPI lineGetIDA(HLINE hLine,DWORD dwAddressID,HCALL hCall,DWORD dwSelect,LPVARSTRING lpDeviceID,LPCSTR lpszDeviceClass);
  LONG WINAPI lineGetIDW(HLINE hLine,DWORD dwAddressID,HCALL hCall,DWORD dwSelect,LPVARSTRING lpDeviceID,LPCWSTR lpszDeviceClass);
  LONG WINAPI lineGetLineDevStatus(HLINE hLine,LPLINEDEVSTATUS lpLineDevStatus);
  LONG WINAPI lineGetLineDevStatusA(HLINE hLine,LPLINEDEVSTATUS lpLineDevStatus);
  LONG WINAPI lineGetLineDevStatusW(HLINE hLine,LPLINEDEVSTATUS lpLineDevStatus);
  LONG WINAPI lineGetMessage(HLINEAPP hLineApp,LPLINEMESSAGE lpMessage,DWORD dwTimeout);
  LONG WINAPI lineGetNewCalls(HLINE hLine,DWORD dwAddressID,DWORD dwSelect,LPLINECALLLIST lpCallList);
  LONG WINAPI lineGetNumRings(HLINE hLine,DWORD dwAddressID,LPDWORD lpdwNumRings);
  LONG WINAPI lineGetProviderList(DWORD dwAPIVersion,LPLINEPROVIDERLIST lpProviderList);
  LONG WINAPI lineGetProviderListA(DWORD dwAPIVersion,LPLINEPROVIDERLIST lpProviderList);
  LONG WINAPI lineGetProviderListW(DWORD dwAPIVersion,LPLINEPROVIDERLIST lpProviderList);
  LONG WINAPI lineGetProxyStatus(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAppAPIVersion,LPLINEPROXYREQUESTLIST lpLineProxyReqestList);
  LONG WINAPI lineGetQueueInfo(HLINE hLine,DWORD dwQueueID,LPLINEQUEUEINFO lpLineQueueInfo);
  LONG WINAPI lineGetQueueListA(HLINE hLine,LPGUID lpGroupID,LPLINEQUEUELIST lpQueueList);
  LONG WINAPI lineGetQueueListW(HLINE hLine,LPGUID lpGroupID,LPLINEQUEUELIST lpQueueList);
  LONG WINAPI lineGetRequest(HLINEAPP hLineApp,DWORD dwRequestMode,LPVOID lpRequestBuffer);
  LONG WINAPI lineGetRequestA(HLINEAPP hLineApp,DWORD dwRequestMode,LPVOID lpRequestBuffer);
  LONG WINAPI lineGetRequestW(HLINEAPP hLineApp,DWORD dwRequestMode,LPVOID lpRequestBuffer);
  LONG WINAPI lineGetStatusMessages(HLINE hLine,LPDWORD lpdwLineStates,LPDWORD lpdwAddressStates);
  LONG WINAPI lineGetTranslateCaps(HLINEAPP hLineApp,DWORD dwAPIVersion,LPLINETRANSLATECAPS lpTranslateCaps);
  LONG WINAPI lineGetTranslateCapsA(HLINEAPP hLineApp,DWORD dwAPIVersion,LPLINETRANSLATECAPS lpTranslateCaps);
  LONG WINAPI lineGetTranslateCapsW(HLINEAPP hLineApp,DWORD dwAPIVersion,LPLINETRANSLATECAPS lpTranslateCaps);
  LONG WINAPI lineHandoff(HCALL hCall,LPCSTR lpszFileName,DWORD dwMediaMode);
  LONG WINAPI lineHandoffA(HCALL hCall,LPCSTR lpszFileName,DWORD dwMediaMode);
  LONG WINAPI lineHandoffW(HCALL hCall,LPCWSTR lpszFileName,DWORD dwMediaMode);
  LONG WINAPI lineHold(HCALL hCall);
  LONG WINAPI lineInitialize(LPHLINEAPP lphLineApp,HINSTANCE hInstance,LINECALLBACK lpfnCallback,LPCSTR lpszAppName,LPDWORD lpdwNumDevs);
  LONG WINAPI lineInitializeExA(LPHLINEAPP lphLineApp,HINSTANCE hInstance,LINECALLBACK lpfnCallback,LPCSTR lpszFriendlyAppName,LPDWORD lpdwNumDevs,LPDWORD lpdwAPIVersion,LPLINEINITIALIZEEXPARAMS lpLineInitializeExParams);
  LONG WINAPI lineInitializeExW(LPHLINEAPP lphLineApp,HINSTANCE hInstance,LINECALLBACK lpfnCallback,LPCWSTR lpszFriendlyAppName,LPDWORD lpdwNumDevs,LPDWORD lpdwAPIVersion,LPLINEINITIALIZEEXPARAMS lpLineInitializeExParams);
  LONG WINAPI lineMakeCall(HLINE hLine,LPHCALL lphCall,LPCSTR lpszDestAddress,DWORD dwCountryCode,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineMakeCallA(HLINE hLine,LPHCALL lphCall,LPCSTR lpszDestAddress,DWORD dwCountryCode,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineMakeCallW(HLINE hLine,LPHCALL lphCall,LPCWSTR lpszDestAddress,DWORD dwCountryCode,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineMonitorDigits(HCALL hCall,DWORD dwDigitModes);
  LONG WINAPI lineMonitorMedia(HCALL hCall,DWORD dwMediaModes);
  LONG WINAPI lineMonitorTones(HCALL hCall,LPLINEMONITORTONE const lpToneList,DWORD dwNumEntries);
  LONG WINAPI lineNegotiateAPIVersion(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPILowVersion,DWORD dwAPIHighVersion,LPDWORD lpdwAPIVersion,LPLINEEXTENSIONID lpExtensionID);
  LONG WINAPI lineNegotiateExtVersion(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,DWORD dwExtLowVersion,DWORD dwExtHighVersion,LPDWORD lpdwExtVersion);
  LONG WINAPI lineOpen(HLINEAPP hLineApp,DWORD dwDeviceID,LPHLINE lphLine,DWORD dwAPIVersion,DWORD dwExtVersion,DWORD_PTR dwCallbackInstance,DWORD dwPrivileges,DWORD dwMediaModes,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineOpenA(HLINEAPP hLineApp,DWORD dwDeviceID,LPHLINE lphLine,DWORD dwAPIVersion,DWORD dwExtVersion,DWORD_PTR dwCallbackInstance,DWORD dwPrivileges,DWORD dwMediaModes,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineOpenW(HLINEAPP hLineApp,DWORD dwDeviceID,LPHLINE lphLine,DWORD dwAPIVersion,DWORD dwExtVersion,DWORD_PTR dwCallbackInstance,DWORD dwPrivileges,DWORD dwMediaModes,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI linePark(HCALL hCall,DWORD dwParkMode,LPCSTR lpszDirAddress,LPVARSTRING lpNonDirAddress);
  LONG WINAPI lineParkA(HCALL hCall,DWORD dwParkMode,LPCSTR lpszDirAddress,LPVARSTRING lpNonDirAddress);
  LONG WINAPI lineParkW(HCALL hCall,DWORD dwParkMode,LPCWSTR lpszDirAddress,LPVARSTRING lpNonDirAddress);
  LONG WINAPI linePickup(HLINE hLine,DWORD dwAddressID,LPHCALL lphCall,LPCSTR lpszDestAddress,LPCSTR lpszGroupID);
  LONG WINAPI linePickupA(HLINE hLine,DWORD dwAddressID,LPHCALL lphCall,LPCSTR lpszDestAddress,LPCSTR lpszGroupID);
  LONG WINAPI linePickupW(HLINE hLine,DWORD dwAddressID,LPHCALL lphCall,LPCWSTR lpszDestAddress,LPCWSTR lpszGroupID);
  LONG WINAPI linePrepareAddToConference(HCALL hConfCall,LPHCALL lphConsultCall,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI linePrepareAddToConferenceA(HCALL hConfCall,LPHCALL lphConsultCall,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI linePrepareAddToConferenceW(HCALL hConfCall,LPHCALL lphConsultCall,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineProxyMessage(HLINE hLine,HCALL hCall,DWORD dwMsg,DWORD dwParam1,DWORD dwParam2,DWORD dwParam3);
  LONG WINAPI lineProxyResponse(HLINE hLine,LPLINEPROXYREQUEST lpProxyRequest,DWORD dwResult);
  LONG WINAPI lineRedirect(HCALL hCall,LPCSTR lpszDestAddress,DWORD dwCountryCode);
  LONG WINAPI lineRedirectA(HCALL hCall,LPCSTR lpszDestAddress,DWORD dwCountryCode);
  LONG WINAPI lineRedirectW(HCALL hCall,LPCWSTR lpszDestAddress,DWORD dwCountryCode);
  LONG WINAPI lineRegisterRequestRecipient(HLINEAPP hLineApp,DWORD dwRegistrationInstance,DWORD dwRequestMode,DWORD bEnable);
  LONG WINAPI lineReleaseUserUserInfo(HCALL hCall);
  LONG WINAPI lineRemoveFromConference(HCALL hCall);
  LONG WINAPI lineRemoveProvider(DWORD dwPermanentProviderID,HWND hwndOwner);
  LONG WINAPI lineSecureCall(HCALL hCall);
  LONG WINAPI lineSendUserUserInfo(HCALL hCall,LPCSTR lpsUserUserInfo,DWORD dwSize);
  LONG WINAPI lineSetAgentActivity(HLINE hLine,DWORD dwAddressID,DWORD dwActivityID);
  LONG WINAPI lineSetAgentGroup(HLINE hLine,DWORD dwAddressID,LPLINEAGENTGROUPLIST lpAgentGroupList);
  LONG WINAPI lineSetAgentMeasurementPeriod(HLINE hLine,HAGENT hAgent,DWORD dwMeasurementPeriod);
  LONG WINAPI lineSetAgentSessionState(HLINE hLine,HAGENTSESSION hAgentSession,DWORD dwAgentSessionState,DWORD dwNextAgentSessionState);
  LONG WINAPI lineSetAgentStateEx(HLINE hLine,HAGENT hAgent,DWORD dwAgentState,DWORD dwNextAgentState);
  LONG WINAPI lineSetAgentState(HLINE hLine,DWORD dwAddressID,DWORD dwAgentState,DWORD dwNextAgentState);
  LONG WINAPI lineSetAppPriority(LPCSTR lpszAppFilename,DWORD dwMediaMode,LPLINEEXTENSIONID lpExtensionID,DWORD dwRequestMode,LPCSTR lpszExtensionName,DWORD dwPriority);
  LONG WINAPI lineSetAppPriorityA(LPCSTR lpszAppFilename,DWORD dwMediaMode,LPLINEEXTENSIONID lpExtensionID,DWORD dwRequestMode,LPCSTR lpszExtensionName,DWORD dwPriority);
  LONG WINAPI lineSetAppPriorityW(LPCWSTR lpszAppFilename,DWORD dwMediaMode,LPLINEEXTENSIONID lpExtensionID,DWORD dwRequestMode,LPCWSTR lpszExtensionName,DWORD dwPriority);
  LONG WINAPI lineSetAppSpecific(HCALL hCall,DWORD dwAppSpecific);
  LONG WINAPI lineSetCallData(HCALL hCall,LPVOID lpCallData,DWORD dwSize);
  LONG WINAPI lineSetCallParams(HCALL hCall,DWORD dwBearerMode,DWORD dwMinRate,DWORD dwMaxRate,LPLINEDIALPARAMS const lpDialParams);
  LONG WINAPI lineSetCallPrivilege(HCALL hCall,DWORD dwCallPrivilege);
  LONG WINAPI lineSetCallQualityOfService(HCALL hCall,LPVOID lpSendingFlowspec,DWORD dwSendingFlowspecSize,LPVOID lpReceivingFlowspec,DWORD dwReceivingFlowspecSize);
  LONG WINAPI lineSetCallTreatment(HCALL hCall,DWORD dwTreatment);
  LONG WINAPI lineSetCurrentLocation(HLINEAPP hLineApp,DWORD dwLocation);
  LONG WINAPI lineSetDevConfig(DWORD dwDeviceID,LPVOID const lpDeviceConfig,DWORD dwSize,LPCSTR lpszDeviceClass);
  LONG WINAPI lineSetDevConfigA(DWORD dwDeviceID,LPVOID const lpDeviceConfig,DWORD dwSize,LPCSTR lpszDeviceClass);
  LONG WINAPI lineSetDevConfigW(DWORD dwDeviceID,LPVOID const lpDeviceConfig,DWORD dwSize,LPCWSTR lpszDeviceClass);
  LONG WINAPI lineSetLineDevStatus(HLINE hLine,DWORD dwStatusToChange,DWORD fStatus);
  LONG WINAPI lineSetMediaControl(HLINE hLine,DWORD dwAddressID,HCALL hCall,DWORD dwSelect,LPLINEMEDIACONTROLDIGIT const lpDigitList,DWORD dwDigitNumEntries,LPLINEMEDIACONTROLMEDIA const lpMediaList,DWORD dwMediaNumEntries,LPLINEMEDIACONTROLTONE const lpToneList,DWORD dwToneNumEntries,LPLINEMEDIACONTROLCALLSTATE const lpCallStateList,DWORD dwCallStateNumEntries);
  LONG WINAPI lineSetMediaMode(HCALL hCall,DWORD dwMediaModes);
  LONG WINAPI lineSetQueueMeasurementPeriod(HLINE hLine,DWORD dwQueueID,DWORD dwMeasurementPeriod);
  LONG WINAPI lineSetNumRings(HLINE hLine,DWORD dwAddressID,DWORD dwNumRings);
  LONG WINAPI lineSetStatusMessages(HLINE hLine,DWORD dwLineStates,DWORD dwAddressStates);
  LONG WINAPI lineSetTerminal(HLINE hLine,DWORD dwAddressID,HCALL hCall,DWORD dwSelect,DWORD dwTerminalModes,DWORD dwTerminalID,DWORD bEnable);
  LONG WINAPI lineSetTollList(HLINEAPP hLineApp,DWORD dwDeviceID,LPCSTR lpszAddressIn,DWORD dwTollListOption);
  LONG WINAPI lineSetTollListA(HLINEAPP hLineApp,DWORD dwDeviceID,LPCSTR lpszAddressIn,DWORD dwTollListOption);
  LONG WINAPI lineSetTollListW(HLINEAPP hLineApp,DWORD dwDeviceID,LPCWSTR lpszAddressInW,DWORD dwTollListOption);
  LONG WINAPI lineSetupConference(HCALL hCall,HLINE hLine,LPHCALL lphConfCall,LPHCALL lphConsultCall,DWORD dwNumParties,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineSetupConferenceA(HCALL hCall,HLINE hLine,LPHCALL lphConfCall,LPHCALL lphConsultCall,DWORD dwNumParties,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineSetupConferenceW(HCALL hCall,HLINE hLine,LPHCALL lphConfCall,LPHCALL lphConsultCall,DWORD dwNumParties,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineSetupTransfer(HCALL hCall,LPHCALL lphConsultCall,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineSetupTransferA(HCALL hCall,LPHCALL lphConsultCall,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineSetupTransferW(HCALL hCall,LPHCALL lphConsultCall,LPLINECALLPARAMS const lpCallParams);
  LONG WINAPI lineShutdown(HLINEAPP hLineApp);
  LONG WINAPI lineSwapHold(HCALL hActiveCall,HCALL hHeldCall);
  LONG WINAPI lineTranslateAddress(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,LPCSTR lpszAddressIn,DWORD dwCard,DWORD dwTranslateOptions,LPLINETRANSLATEOUTPUT lpTranslateOutput);
  LONG WINAPI lineTranslateAddressA(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,LPCSTR lpszAddressIn,DWORD dwCard,DWORD dwTranslateOptions,LPLINETRANSLATEOUTPUT lpTranslateOutput);
  LONG WINAPI lineTranslateAddressW(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,LPCWSTR lpszAddressIn,DWORD dwCard,DWORD dwTranslateOptions,LPLINETRANSLATEOUTPUT lpTranslateOutput);
  LONG WINAPI lineTranslateDialog(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,HWND hwndOwner,LPCSTR lpszAddressIn);
  LONG WINAPI lineTranslateDialogA(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,HWND hwndOwner,LPCSTR lpszAddressIn);
  LONG WINAPI lineTranslateDialogW(HLINEAPP hLineApp,DWORD dwDeviceID,DWORD dwAPIVersion,HWND hwndOwner,LPCWSTR lpszAddressIn);
  LONG WINAPI lineUncompleteCall(HLINE hLine,DWORD dwCompletionID);
  LONG WINAPI lineUnhold(HCALL hCall);
  LONG WINAPI lineUnpark(HLINE hLine,DWORD dwAddressID,LPHCALL lphCall,LPCSTR lpszDestAddress);
  LONG WINAPI lineUnparkA(HLINE hLine,DWORD dwAddressID,LPHCALL lphCall,LPCSTR lpszDestAddress);
  LONG WINAPI lineUnparkW(HLINE hLine,DWORD dwAddressID,LPHCALL lphCall,LPCWSTR lpszDestAddress);
  LONG WINAPI phoneClose(HPHONE hPhone);
  LONG WINAPI phoneConfigDialog(DWORD dwDeviceID,HWND hwndOwner,LPCSTR lpszDeviceClass);
  LONG WINAPI phoneConfigDialogA(DWORD dwDeviceID,HWND hwndOwner,LPCSTR lpszDeviceClass);
  LONG WINAPI phoneConfigDialogW(DWORD dwDeviceID,HWND hwndOwner,LPCWSTR lpszDeviceClass);
  LONG WINAPI phoneDevSpecific(HPHONE hPhone,LPVOID lpParams,DWORD dwSize);
  LONG WINAPI phoneGetButtonInfo(HPHONE hPhone,DWORD dwButtonLampID,LPPHONEBUTTONINFO lpButtonInfo);
  LONG WINAPI phoneGetButtonInfoA(HPHONE hPhone,DWORD dwButtonLampID,LPPHONEBUTTONINFO lpButtonInfo);
  LONG WINAPI phoneGetButtonInfoW(HPHONE hPhone,DWORD dwButtonLampID,LPPHONEBUTTONINFO lpButtonInfo);
  LONG WINAPI phoneGetData(HPHONE hPhone,DWORD dwDataID,LPVOID lpData,DWORD dwSize);
  LONG WINAPI phoneGetDevCaps(HPHONEAPP hPhoneApp,DWORD dwDeviceID,DWORD dwAPIVersion,DWORD dwExtVersion,LPPHONECAPS lpPhoneCaps);
  LONG WINAPI phoneGetDevCapsA(HPHONEAPP hPhoneApp,DWORD dwDeviceID,DWORD dwAPIVersion,DWORD dwExtVersion,LPPHONECAPS lpPhoneCaps);
  LONG WINAPI phoneGetDevCapsW(HPHONEAPP hPhoneApp,DWORD dwDeviceID,DWORD dwAPIVersion,DWORD dwExtVersion,LPPHONECAPS lpPhoneCaps);
  LONG WINAPI phoneGetDisplay(HPHONE hPhone,LPVARSTRING lpDisplay);
  LONG WINAPI phoneGetGain(HPHONE hPhone,DWORD dwHookSwitchDev,LPDWORD lpdwGain);
  LONG WINAPI phoneGetHookSwitch(HPHONE hPhone,LPDWORD lpdwHookSwitchDevs);
  LONG WINAPI phoneGetIcon(DWORD dwDeviceID,LPCSTR lpszDeviceClass,LPHICON lphIcon);
  LONG WINAPI phoneGetIconA(DWORD dwDeviceID,LPCSTR lpszDeviceClass,LPHICON lphIcon);
  LONG WINAPI phoneGetIconW(DWORD dwDeviceID,LPCWSTR lpszDeviceClass,LPHICON lphIcon);
  LONG WINAPI phoneGetID(HPHONE hPhone,LPVARSTRING lpDeviceID,LPCSTR lpszDeviceClass);
  LONG WINAPI phoneGetIDA(HPHONE hPhone,LPVARSTRING lpDeviceID,LPCSTR lpszDeviceClass);
  LONG WINAPI phoneGetIDW(HPHONE hPhone,LPVARSTRING lpDeviceID,LPCWSTR lpszDeviceClass);
  LONG WINAPI phoneGetLamp(HPHONE hPhone,DWORD dwButtonLampID,LPDWORD lpdwLampMode);
  LONG WINAPI phoneGetMessage(HPHONEAPP hPhoneApp,LPPHONEMESSAGE lpMessage,DWORD dwTimeout);
  LONG WINAPI phoneGetRing(HPHONE hPhone,LPDWORD lpdwRingMode,LPDWORD lpdwVolume);
  LONG WINAPI phoneGetStatus(HPHONE hPhone,LPPHONESTATUS lpPhoneStatus);
  LONG WINAPI phoneGetStatusA(HPHONE hPhone,LPPHONESTATUS lpPhoneStatus);
  LONG WINAPI phoneGetStatusW(HPHONE hPhone,LPPHONESTATUS lpPhoneStatus);
  LONG WINAPI phoneGetStatusMessages(HPHONE hPhone,LPDWORD lpdwPhoneStates,LPDWORD lpdwButtonModes,LPDWORD lpdwButtonStates);
  LONG WINAPI phoneGetVolume(HPHONE hPhone,DWORD dwHookSwitchDev,LPDWORD lpdwVolume);
  LONG WINAPI phoneInitialize(LPHPHONEAPP lphPhoneApp,HINSTANCE hInstance,PHONECALLBACK lpfnCallback,LPCSTR lpszAppName,LPDWORD lpdwNumDevs);
  LONG WINAPI phoneInitializeExA(LPHPHONEAPP lphPhoneApp,HINSTANCE hInstance,PHONECALLBACK lpfnCallback,LPCSTR lpszFriendlyAppName,LPDWORD lpdwNumDevs,LPDWORD lpdwAPIVersion,LPPHONEINITIALIZEEXPARAMS lpPhoneInitializeExParams);
  LONG WINAPI phoneInitializeExW(LPHPHONEAPP lphPhoneApp,HINSTANCE hInstance,PHONECALLBACK lpfnCallback,LPCWSTR lpszFriendlyAppName,LPDWORD lpdwNumDevs,LPDWORD lpdwAPIVersion,LPPHONEINITIALIZEEXPARAMS lpPhoneInitializeExParams);
  LONG WINAPI phoneNegotiateAPIVersion(HPHONEAPP hPhoneApp,DWORD dwDeviceID,DWORD dwAPILowVersion,DWORD dwAPIHighVersion,LPDWORD lpdwAPIVersion,LPPHONEEXTENSIONID lpExtensionID);
  LONG WINAPI phoneNegotiateExtVersion(HPHONEAPP hPhoneApp,DWORD dwDeviceID,DWORD dwAPIVersion,DWORD dwExtLowVersion,DWORD dwExtHighVersion,LPDWORD lpdwExtVersion);
  LONG WINAPI phoneOpen(HPHONEAPP hPhoneApp,DWORD dwDeviceID,LPHPHONE lphPhone,DWORD dwAPIVersion,DWORD dwExtVersion,DWORD_PTR dwCallbackInstance,DWORD dwPrivilege);
  LONG WINAPI phoneSetButtonInfo(HPHONE hPhone,DWORD dwButtonLampID,LPPHONEBUTTONINFO const lpButtonInfo);
  LONG WINAPI phoneSetButtonInfoA(HPHONE hPhone,DWORD dwButtonLampID,LPPHONEBUTTONINFO const lpButtonInfo);
  LONG WINAPI phoneSetButtonInfoW(HPHONE hPhone,DWORD dwButtonLampID,LPPHONEBUTTONINFO const lpButtonInfo);
  LONG WINAPI phoneSetData(HPHONE hPhone,DWORD dwDataID,LPVOID const lpData,DWORD dwSize);
  LONG WINAPI phoneSetDisplay(HPHONE hPhone,DWORD dwRow,DWORD dwColumn,LPCSTR lpsDisplay,DWORD dwSize);
  LONG WINAPI phoneSetGain(HPHONE hPhone,DWORD dwHookSwitchDev,DWORD dwGain);
  LONG WINAPI phoneSetHookSwitch(HPHONE hPhone,DWORD dwHookSwitchDevs,DWORD dwHookSwitchMode);
  LONG WINAPI phoneSetLamp(HPHONE hPhone,DWORD dwButtonLampID,DWORD dwLampMode);
  LONG WINAPI phoneSetRing(HPHONE hPhone,DWORD dwRingMode,DWORD dwVolume);
  LONG WINAPI phoneSetStatusMessages(HPHONE hPhone,DWORD dwPhoneStates,DWORD dwButtonModes,DWORD dwButtonStates);
  LONG WINAPI phoneSetVolume(HPHONE hPhone,DWORD dwHookSwitchDev,DWORD dwVolume);
  LONG WINAPI phoneShutdown(HPHONEAPP hPhoneApp);
  LONG WINAPI tapiGetLocationInfo(LPSTR lpszCountryCode,LPSTR lpszCityCode);
  LONG WINAPI tapiGetLocationInfoA(LPSTR lpszCountryCode,LPSTR lpszCityCode);
  LONG WINAPI tapiGetLocationInfoW(LPWSTR lpszCountryCodeW,LPWSTR lpszCityCodeW);
  LONG WINAPI tapiRequestDrop(HWND hwnd,WPARAM wRequestID);
  LONG WINAPI tapiRequestMakeCall(LPCSTR lpszDestAddress,LPCSTR lpszAppName,LPCSTR lpszCalledParty,LPCSTR lpszComment);
  LONG WINAPI tapiRequestMakeCallA(LPCSTR lpszDestAddress,LPCSTR lpszAppName,LPCSTR lpszCalledParty,LPCSTR lpszComment);
  LONG WINAPI tapiRequestMakeCallW(LPCWSTR lpszDestAddress,LPCWSTR lpszAppName,LPCWSTR lpszCalledParty,LPCWSTR lpszComment);
  LONG WINAPI tapiRequestMediaCall(HWND hwnd,WPARAM wRequestID,LPCSTR lpszDeviceClass,LPCSTR lpDeviceID,DWORD dwSize,DWORD dwSecure,LPCSTR lpszDestAddress,LPCSTR lpszAppName,LPCSTR lpszCalledParty,LPCSTR lpszComment);
  LONG WINAPI tapiRequestMediaCallA(HWND hwnd,WPARAM wRequestID,LPCSTR lpszDeviceClass,LPCSTR lpDeviceID,DWORD dwSize,DWORD dwSecure,LPCSTR lpszDestAddress,LPCSTR lpszAppName,LPCSTR lpszCalledParty,LPCSTR lpszComment);
  LONG WINAPI tapiRequestMediaCallW(HWND hwnd,WPARAM wRequestID,LPCWSTR lpszDeviceClass,LPCWSTR lpDeviceID,DWORD dwSize,DWORD dwSecure,LPCWSTR lpszDestAddress,LPCWSTR lpszAppName,LPCWSTR lpszCalledParty,LPCWSTR lpszComment);

#if defined(UNICODE) || (TAPI_CURRENT_VERSION >= 0x00020000)
#define lineAddProvider __MINGW_NAME_AW(lineAddProvider)
#define lineBlindTransfer __MINGW_NAME_AW(lineBlindTransfer)
#define lineConfigDialog __MINGW_NAME_AW(lineConfigDialog)
#define lineConfigDialogEdit __MINGW_NAME_AW(lineConfigDialogEdit)
#define lineDial __MINGW_NAME_AW(lineDial)
#define lineGatherDigits __MINGW_NAME_AW(lineGatherDigits)
#define lineGenerateDigits __MINGW_NAME_AW(lineGenerateDigits)
#define lineGetAddressID __MINGW_NAME_AW(lineGetAddressID)
#define lineGetAppPriority __MINGW_NAME_AW(lineGetAppPriority)
#define lineGetDevConfig __MINGW_NAME_AW(lineGetDevConfig)
#define lineGetIcon __MINGW_NAME_AW(lineGetIcon)
#define lineGetID __MINGW_NAME_AW(lineGetID)
#define lineHandoff __MINGW_NAME_AW(lineHandoff)
#define lineMakeCall __MINGW_NAME_AW(lineMakeCall)
#define linePark __MINGW_NAME_AW(linePark)
#define linePickup __MINGW_NAME_AW(linePickup)
#define lineRedirect __MINGW_NAME_AW(lineRedirect)
#define lineSetAppPriority __MINGW_NAME_AW(lineSetAppPriority)
#define lineSetDevConfig __MINGW_NAME_AW(lineSetDevConfig)
#define lineSetTollList __MINGW_NAME_AW(lineSetTollList)
#define lineTranslateAddress __MINGW_NAME_AW(lineTranslateAddress)
#define lineTranslateDialog __MINGW_NAME_AW(lineTranslateDialog)
#define lineUnpark __MINGW_NAME_AW(lineUnpark)

#define phoneConfigDialog __MINGW_NAME_AW(phoneConfigDialog)
#define phoneGetIcon __MINGW_NAME_AW(phoneGetIcon)
#define phoneGetID __MINGW_NAME_AW(phoneGetID)

#define tapiGetLocationInfo __MINGW_NAME_AW(tapiGetLocationInfo)
#define tapiRequestMakeCall __MINGW_NAME_AW(tapiRequestMakeCall)
#define tapiRequestMediaCall __MINGW_NAME_AW(tapiRequestMediaCall)
#endif /* UNICODE || TAPI2 */

#define lineCreateAgent __MINGW_NAME_AW(lineCreateAgent)
#define lineCreateAgent __MINGW_NAME_AW(lineCreateAgent)
#define lineForward __MINGW_NAME_AW(lineForward)
#define lineGetAddressCaps __MINGW_NAME_AW(lineGetAddressCaps)
#define lineGetAddressStatus __MINGW_NAME_AW(lineGetAddressStatus)
#define lineGetAgentActivityList __MINGW_NAME_AW(lineGetAgentActivityList)
#define lineGetAgentCaps __MINGW_NAME_AW(lineGetAgentCaps)
#define lineGetAgentGroupList __MINGW_NAME_AW(lineGetAgentGroupList)
#define lineGetAgentStatus __MINGW_NAME_AW(lineGetAgentStatus)
#define lineGetCallInfo __MINGW_NAME_AW(lineGetCallInfo)
#define lineGetCountry __MINGW_NAME_AW(lineGetCountry)
#define lineGetDevCaps __MINGW_NAME_AW(lineGetDevCaps)
#define lineGetGroupList __MINGW_NAME_AW(lineGetGroupList)
#define lineGetDevStatus __MINGW_NAME_AW(lineGetDevStatus)
#define lineGetProviderList __MINGW_NAME_AW(lineGetProviderList)
#define lineGetQueueList __MINGW_NAME_AW(lineGetQueueList)
#define lineGetRequest __MINGW_NAME_AW(lineGetRequest)
#define lineGetTranslateCaps __MINGW_NAME_AW(lineGetTranslateCaps)
#define lineInitializeEx __MINGW_NAME_AW(lineInitializeEx)
#define lineOpen __MINGW_NAME_AW(lineOpen)
#define linePrepareAddToConference __MINGW_NAME_AW(linePrepareAddToConference)
#define lineSetupConference __MINGW_NAME_AW(lineSetupConference)
#define lineSetupTransfer __MINGW_NAME_AW(lineSetupTransfer)

#define phoneGetButtonInfo __MINGW_NAME_AW(phoneGetButtonInfo)
#define phoneGetDevCaps __MINGW_NAME_AW(phoneGetDevCaps)
#define phoneGetStatus __MINGW_NAME_AW(phoneGetStatus)
#define phoneInitializeEx __MINGW_NAME_AW(phoneInitializeEx)
#define phoneSetButtonInfo __MINGW_NAME_AW(phoneSetButtonInfo)

#define TAPIERROR_FORMATMESSAGE(__ErrCode__) (((__ErrCode__) > 0xFFFF0000) ? ((__ErrCode__) & 0x0000FFFF) : (((__ErrCode__) & 0x10000000) ? ((__ErrCode__) - 0x90000000 + 0xF000) : ((__ErrCode__) - 0x80000000 + 0xE000)))

#ifdef __cplusplus
}
#endif

#pragma pack()
#endif
