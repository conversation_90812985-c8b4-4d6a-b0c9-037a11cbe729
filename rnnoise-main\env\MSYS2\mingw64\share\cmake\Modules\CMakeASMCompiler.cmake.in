set(C<PERSON><PERSON>_ASM@ASM_DIALECT@_COMPILER "@_CMAKE_ASM_COMPILER@")
set(C<PERSON>KE_ASM@ASM_DIALECT@_COMPILER_ARG1 "@_CMAKE_ASM_COMPILER_ARG1@")
set(C<PERSON><PERSON>_AR "@CMAKE_AR@")
set(CMAKE_ASM@ASM_DIALECT@_COMPILER_AR "@_CMAKE_ASM_COMPILER_AR@")
set(CMAKE_RANLIB "@CMAKE_RANLIB@")
set(CMAKE_ASM@ASM_DIALECT@_COMPILER_RANLIB "@_CMAKE_ASM_COMPILER_RANLIB@")
set(CMAKE_LINKER "@CMAKE_LINKER@")
set(CMAKE_LINKER_LINK "@CMAKE_LINKER_LINK@")
set(CMAKE_LINKER_LLD "@CMAKE_LINKER_LLD@")
set(CMAKE_ASM@ASM_DIALECT@_COMPILER_LINKER "@_CMAKE_ASM_COMPILER_LINKER@")
set(<PERSON><PERSON><PERSON>_ASM@ASM_DIALECT@_COMPILER_LINKER_ID "@_CMAKE_ASM_COMPILER_LINKER_ID@")
set(CMAKE_ASM@ASM_DIALECT@_COMPILER_LINKER_VERSION @_CMAKE_ASM_COMPILER_LINKER_VERSION@)
set(CMAKE_ASM@ASM_DIALECT@_COMPILER_LINKER_FRONTEND_VARIANT @_CMAKE_ASM_COMPILER_LINKER_FRONTEND_VARIANT@)
set(CMAKE_MT "@CMAKE_MT@")
set(CMAKE_TAPI "@CMAKE_TAPI@")
set(CMAKE_ASM@ASM_DIALECT@_COMPILER_LOADED 1)
set(CMAKE_ASM@ASM_DIALECT@_COMPILER_ID "@_CMAKE_ASM_COMPILER_ID@")
set(CMAKE_ASM@ASM_DIALECT@_COMPILER_VERSION "@_CMAKE_ASM_COMPILER_VERSION@")
set(CMAKE_ASM@ASM_DIALECT@_COMPILER_ENV_VAR "@_CMAKE_ASM_COMPILER_ENV_VAR@")
@_SET_CMAKE_ASM_COMPILER_ID_VENDOR_MATCH@
@_SET_CMAKE_ASM_COMPILER_ARCHITECTURE_ID@
@_SET_CMAKE_ASM_COMPILER_SYSROOT@

set(CMAKE_ASM@ASM_DIALECT@_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_ASM@ASM_DIALECT@_LINKER_PREFERENCE 0)
set(CMAKE_ASM@ASM_DIALECT@_LINKER_DEPFILE_SUPPORTED @_CMAKE_ASM_LINKER_DEPFILE_SUPPORTED@)
set(CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED @CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED@)
set(CMAKE_ASM@ASM_DIALECT@_LINKER_PUSHPOP_STATE_SUPPORTED @_CMAKE_ASM_LINKER_PUSHPOP_STATE_SUPPORTED@)

@CMAKE_ASM_COMPILER_CUSTOM_CODE@
