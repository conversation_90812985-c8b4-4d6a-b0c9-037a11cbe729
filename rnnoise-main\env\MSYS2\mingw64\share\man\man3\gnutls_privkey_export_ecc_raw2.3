.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_export_ecc_raw2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_export_ecc_raw2 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_export_ecc_raw2(gnutls_privkey_t " key ", gnutls_ecc_curve_t * " curve ", gnutls_datum_t * " x ", gnutls_datum_t * " y ", gnutls_datum_t * " k ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
Holds the public key
.IP "gnutls_ecc_curve_t * curve" 12
will hold the curve
.IP "gnutls_datum_t * x" 12
will hold the x\-coordinate
.IP "gnutls_datum_t * y" 12
will hold the y\-coordinate
.IP "gnutls_datum_t * k" 12
will hold the private key
.IP "unsigned int flags" 12
flags from \fBgnutls_abstract_export_flags_t\fP
.SH "DESCRIPTION"
This function will export the ECC private key's parameters found
in the given structure. The new parameters will be allocated using
\fBgnutls_malloc()\fP and will be stored in the appropriate datum.

In EdDSA curves the  \fIy\fP parameter will be \fBNULL\fP and the other parameters
will be in the native format for the curve.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
