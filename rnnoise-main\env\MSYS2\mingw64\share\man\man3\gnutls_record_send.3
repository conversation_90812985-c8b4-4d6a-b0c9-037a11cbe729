.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_send" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_send \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_send(gnutls_session_t " session ", const void * " data ", size_t " data_size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const void * data" 12
contains the data to send
.IP "size_t data_size" 12
is the length of the data
.SH "DESCRIPTION"
This function has the similar semantics with \fBsend()\fP.  The only
difference is that it accepts a GnuTLS session, and uses different
error codes.
Note that if the send buffer is full, \fBsend()\fP will block this
function.  See the \fBsend()\fP documentation for more information.

You can replace the default push function which is \fBsend()\fP, by using
\fBgnutls_transport_set_push_function()\fP.

If the EINTR is returned by the internal push function
then \fBGNUTLS_E_INTERRUPTED\fP will be returned. If
\fBGNUTLS_E_INTERRUPTED\fP or \fBGNUTLS_E_AGAIN\fP is returned, you must
call this function again with the exact same parameters, or provide a
\fBNULL\fP pointer for  \fIdata\fP and 0 for  \fIdata_size\fP , in order to write the
same data as before. If you wish to discard the previous data instead
of retrying, you must call \fBgnutls_record_discard_queued()\fP before
calling this function with different parameters. Note that the latter
works only on special transports (e.g., UDP).
cf. \fBgnutls_record_get_direction()\fP.

Note that in DTLS this function will return the \fBGNUTLS_E_LARGE_PACKET\fP
error code if the send data exceed the data MTU value \- as returned
by \fBgnutls_dtls_get_data_mtu()\fP. The errno value EMSGSIZE
also maps to \fBGNUTLS_E_LARGE_PACKET\fP.
Note that since 3.2.13 this function can be called under cork in DTLS
mode, and will refuse to send data over the MTU size by returning
\fBGNUTLS_E_LARGE_PACKET\fP.
.SH "RETURNS"
The number of bytes sent, or a negative error code.  The
number of bytes sent might be less than  \fIdata_size\fP .  The maximum
number of bytes this function can send in a single call depends
on the negotiated maximum record size.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
