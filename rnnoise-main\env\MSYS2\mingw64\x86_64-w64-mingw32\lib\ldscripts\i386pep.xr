/* Script for -r */
/* Copyright (C) 2014-2025 Free Software Foundation, Inc.
   Copying and distribution of this script, with or without modification,
   are permitted in any medium without royalty provided the copyright
   notice and this notice are preserved.  */
OUTPUT_FORMAT(pe-x86-64)
SEARCH_DIR("=/mingw64/x86_64-w64-mingw32/lib"); SEARCH_DIR("=/mingw64/lib"); SEARCH_DIR("=/usr/local/lib"); SEARCH_DIR("=/lib"); SEARCH_DIR("=/usr/lib");
SECTIONS
{
  .text  :
  {
    *(.text)
  }
  /* The Cygwin32 library uses a section to avoid copying certain data
     on fork.  This used to be named ".data".  The linker used
     to include this between __data_start__ and __data_end__, but that
     breaks building the cygwin32 dll.  Instead, we name the section
     ".data_cygwin_nocopy" and explicitly include it after __data_end__. */
  .data  :
  {
    *(.data)
    KEEP(*(.jcr))
  }
  .rdata  :
  {
    *(.rdata)
    . = ALIGN(4);
    /* read-only parts of .didat */
    /* This cannot currently be handled with grouped sections.
	See pe.em:sort_sections.  */
    /* .ctors & .dtors */
    /* .CRT */
    /* ___crt_xl_end__ is defined in the TLS Directory support code */
  }
  .eh_frame  :
  {
    KEEP (*(.eh_frame))
  }
  .pdata  :
  {
    KEEP(*(.pdata))
  }
  .xdata  :
  {
    KEEP(*(.xdata))
  }
  .bss  :
  {
    *(.bss)
    *(COMMON)
  }
  .edata  :
  {
    *(.edata)
  }
  /DISCARD/ :
  {
    *(.debug$S)
    *(.debug$T)
    *(.debug$F)
  }
  .idata  :
  {
    /* This cannot currently be handled with grouped sections.
	See pep.em:sort_sections.  */
  }
  .didat  :
  {
    /* This cannot currently be handled with grouped sections.
	See pep.em:sort_sections.  */
  }
  /* Windows TLS expects .tls$AAA to be at the start and .tls$ZZZ to be
     at the end of the .tls section.  This is important because _tls_start MUST
     be at the beginning of the section to enable SECREL32 relocations with TLS
     data.  */
  .tls  :
  {
    *(.tls)
  }
  .endjunk  :
  {
    /* end is deprecated, don't use it */
  }
  .rsrc  : SUBALIGN(4)
  {
    *(.rsrc)
  }
  .reloc  :
  {
    *(.reloc)
  }
  .stab   :
  {
    *(.stab)
  }
  .stabstr   :
  {
    *(.stabstr)
  }
  /* DWARF debug sections.
     Symbols in the DWARF debugging sections are relative to the beginning
     of the section.  Unlike other targets that fake this by putting the
     section VMA at 0, the PE format will not allow it.  */
  /* DWARF 1.1 and DWARF 2.  */
  .debug_aranges   :
  {
    *(.debug_aranges)
  }
  .zdebug_aranges   :
  {
    *(.zdebug_aranges)
  }
  .debug_pubnames   :
  {
    *(.debug_pubnames)
  }
  .zdebug_pubnames   :
  {
    *(.zdebug_pubnames)
  }
  /* DWARF 2.  */
  .debug_info   :
  {
    *(.debug_info)
  }
  .zdebug_info   :
  {
    *(.zdebug_info)
  }
  .debug_abbrev   :
  {
    *(.debug_abbrev)
  }
  .zdebug_abbrev   :
  {
    *(.zdebug_abbrev)
  }
  .debug_line   :
  {
    *(.debug_line)
  }
  .zdebug_line   :
  {
    *(.zdebug_line)
  }
  .debug_frame   :
  {
    *(.debug_frame*)
  }
  .zdebug_frame   :
  {
    *(.zdebug_frame*)
  }
  .debug_str   :
  {
    *(.debug_str)
  }
  .zdebug_str   :
  {
    *(.zdebug_str)
  }
  .debug_loc   :
  {
    *(.debug_loc)
  }
  .zdebug_loc   :
  {
    *(.zdebug_loc)
  }
  .debug_macinfo   :
  {
    *(.debug_macinfo)
  }
  .zdebug_macinfo   :
  {
    *(.zdebug_macinfo)
  }
  /* SGI/MIPS DWARF 2 extensions.  */
  .debug_weaknames   :
  {
    *(.debug_weaknames)
  }
  .zdebug_weaknames   :
  {
    *(.zdebug_weaknames)
  }
  .debug_funcnames   :
  {
    *(.debug_funcnames)
  }
  .zdebug_funcnames   :
  {
    *(.zdebug_funcnames)
  }
  .debug_typenames   :
  {
    *(.debug_typenames)
  }
  .zdebug_typenames   :
  {
    *(.zdebug_typenames)
  }
  .debug_varnames   :
  {
    *(.debug_varnames)
  }
  .zdebug_varnames   :
  {
    *(.zdebug_varnames)
  }
  /* DWARF 3.  */
  .debug_pubtypes   :
  {
    *(.debug_pubtypes)
  }
  .zdebug_pubtypes   :
  {
    *(.zdebug_pubtypes)
  }
  .debug_ranges   :
  {
    *(.debug_ranges)
  }
  .zdebug_ranges   :
  {
    *(.zdebug_ranges)
  }
  /* DWARF 4.  */
  .debug_types   :
  {
    *(.debug_types)
  }
  .zdebug_types   :
  {
    *(.zdebug_types)
  }
  /* DWARF 5.  */
  .debug_addr   :
  {
    *(.debug_addr)
  }
  .zdebug_addr   :
  {
    *(.zdebug_addr)
  }
  .debug_line_str   :
  {
    *(.debug_line_str)
  }
  .zdebug_line_str   :
  {
    *(.zdebug_line_str)
  }
  .debug_loclists   :
  {
    *(.debug_loclists)
  }
  .zdebug_loclists   :
  {
    *(.zdebug_loclists)
  }
  .debug_macro   :
  {
    *(.debug_macro)
  }
  .zdebug_macro   :
  {
    *(.zdebug_macro)
  }
  .debug_names   :
  {
    *(.debug_names)
  }
  .zdebug_names   :
  {
    *(.zdebug_names)
  }
  .debug_rnglists   :
  {
    *(.debug_rnglists)
  }
  .zdebug_rnglists   :
  {
    *(.zdebug_rnglists)
  }
  .debug_str_offsets   :
  {
    *(.debug_str_offsets)
  }
  .zdebug_str_offsets   :
  {
    *(.zdebug_str_offsets)
  }
  .debug_sup   :
  {
    *(.debug_sup)
  }
  /* For Go and Rust.  */
  .debug_gdb_scripts   :
  {
    *(.debug_gdb_scripts)
  }
  .zdebug_gdb_scripts   :
  {
    *(.zdebug_gdb_scripts)
  }
}
