<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/stream_flags.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('stream__flags_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">stream_flags.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>.xz Stream Header and Stream Footer encoder and decoder  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Options for encoding/decoding Stream Header and Stream Footer.  <a href="structlzma__stream__flags.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ada7e0a4f5e7146f547962cb9e9ef08ee" id="r_ada7e0a4f5e7146f547962cb9e9ef08ee"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ada7e0a4f5e7146f547962cb9e9ef08ee">LZMA_STREAM_HEADER_SIZE</a>&#160;&#160;&#160;12</td></tr>
<tr class="memdesc:ada7e0a4f5e7146f547962cb9e9ef08ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Size of Stream Header and Stream Footer.  <br /></td></tr>
<tr class="separator:ada7e0a4f5e7146f547962cb9e9ef08ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8da8190f1396f66332073946bc45634" id="r_ae8da8190f1396f66332073946bc45634"><td class="memItemLeft" align="right" valign="top"><a id="ae8da8190f1396f66332073946bc45634" name="ae8da8190f1396f66332073946bc45634"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_BACKWARD_SIZE_MIN</b>&#160;&#160;&#160;4</td></tr>
<tr class="memdesc:ae8da8190f1396f66332073946bc45634"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum value for <a class="el" href="structlzma__stream__flags.html#aaa65ed7a55a098f829f04dba25d0f212" title="Backward Size.">lzma_stream_flags.backward_size</a>. <br /></td></tr>
<tr class="separator:ae8da8190f1396f66332073946bc45634"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e5e09010880f8caa6cd6539c7341239" id="r_a2e5e09010880f8caa6cd6539c7341239"><td class="memItemLeft" align="right" valign="top"><a id="a2e5e09010880f8caa6cd6539c7341239" name="a2e5e09010880f8caa6cd6539c7341239"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_BACKWARD_SIZE_MAX</b>&#160;&#160;&#160;(<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(1) &lt;&lt; 34)</td></tr>
<tr class="memdesc:a2e5e09010880f8caa6cd6539c7341239"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum value for <a class="el" href="structlzma__stream__flags.html#aaa65ed7a55a098f829f04dba25d0f212" title="Backward Size.">lzma_stream_flags.backward_size</a>. <br /></td></tr>
<tr class="separator:a2e5e09010880f8caa6cd6539c7341239"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a2ebb8d6dff23daeb3de398913b845eff" id="r_a2ebb8d6dff23daeb3de398913b845eff"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2ebb8d6dff23daeb3de398913b845eff">lzma_stream_header_encode</a> (const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *options, uint8_t *out) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a2ebb8d6dff23daeb3de398913b845eff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encode Stream Header.  <br /></td></tr>
<tr class="separator:a2ebb8d6dff23daeb3de398913b845eff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a438249a75ea8da952a7474b92bfe7b7a" id="r_a438249a75ea8da952a7474b92bfe7b7a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a438249a75ea8da952a7474b92bfe7b7a">lzma_stream_footer_encode</a> (const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *options, uint8_t *out) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a438249a75ea8da952a7474b92bfe7b7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encode Stream Footer.  <br /></td></tr>
<tr class="separator:a438249a75ea8da952a7474b92bfe7b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae03198e464f0d296e601ff841e100805" id="r_ae03198e464f0d296e601ff841e100805"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae03198e464f0d296e601ff841e100805">lzma_stream_header_decode</a> (<a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *options, const uint8_t *in) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:ae03198e464f0d296e601ff841e100805"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode Stream Header.  <br /></td></tr>
<tr class="separator:ae03198e464f0d296e601ff841e100805"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa92a383f85753bb79ee23227fa68186c" id="r_aa92a383f85753bb79ee23227fa68186c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa92a383f85753bb79ee23227fa68186c">lzma_stream_footer_decode</a> (<a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *options, const uint8_t *in) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:aa92a383f85753bb79ee23227fa68186c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode Stream Footer.  <br /></td></tr>
<tr class="separator:aa92a383f85753bb79ee23227fa68186c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e25ca4205021302882a696283d45263" id="r_a3e25ca4205021302882a696283d45263"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3e25ca4205021302882a696283d45263">lzma_stream_flags_compare</a> (const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *a, const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *b) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a3e25ca4205021302882a696283d45263"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compare two <a class="el" href="structlzma__stream__flags.html" title="Options for encoding/decoding Stream Header and Stream Footer.">lzma_stream_flags</a> structures.  <br /></td></tr>
<tr class="separator:a3e25ca4205021302882a696283d45263"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>.xz Stream Header and Stream Footer encoder and decoder </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ada7e0a4f5e7146f547962cb9e9ef08ee" name="ada7e0a4f5e7146f547962cb9e9ef08ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada7e0a4f5e7146f547962cb9e9ef08ee">&#9670;&#160;</a></span>LZMA_STREAM_HEADER_SIZE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_STREAM_HEADER_SIZE&#160;&#160;&#160;12</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Size of Stream Header and Stream Footer. </p>
<p>Stream Header and Stream Footer have the same size and they are not going to change even if a newer version of the .xz file format is developed in future. </p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a2ebb8d6dff23daeb3de398913b845eff" name="a2ebb8d6dff23daeb3de398913b845eff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2ebb8d6dff23daeb3de398913b845eff">&#9670;&#160;</a></span>lzma_stream_header_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_header_encode </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Encode Stream Header. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">options</td><td>Stream Header options to be encoded. options-&gt;backward_size is ignored and doesn't need to be initialized. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer of LZMA_STREAM_HEADER_SIZE bytes.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Encoding was successful.</li>
<li>LZMA_OPTIONS_ERROR: options-&gt;version is not supported by this liblzma version.</li>
<li>LZMA_PROG_ERROR: Invalid options. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a438249a75ea8da952a7474b92bfe7b7a" name="a438249a75ea8da952a7474b92bfe7b7a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a438249a75ea8da952a7474b92bfe7b7a">&#9670;&#160;</a></span>lzma_stream_footer_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_footer_encode </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Encode Stream Footer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">options</td><td>Stream Footer options to be encoded. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer of LZMA_STREAM_HEADER_SIZE bytes.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Encoding was successful.</li>
<li>LZMA_OPTIONS_ERROR: options-&gt;version is not supported by this liblzma version.</li>
<li>LZMA_PROG_ERROR: Invalid options. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="ae03198e464f0d296e601ff841e100805" name="ae03198e464f0d296e601ff841e100805"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae03198e464f0d296e601ff841e100805">&#9670;&#160;</a></span>lzma_stream_header_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_header_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Decode Stream Header. </p>
<p>options-&gt;backward_size is always set to LZMA_VLI_UNKNOWN. This is to help comparing Stream Flags from Stream Header and Stream Footer with <a class="el" href="#a3e25ca4205021302882a696283d45263" title="Compare two lzma_stream_flags structures.">lzma_stream_flags_compare()</a>.</p>
<dl class="section note"><dt>Note</dt><dd>When decoding .xz files that contain multiple Streams, it may make sense to print "file format not recognized" only if decoding of the Stream Header of the <em class="arg">first</em> Stream gives LZMA_FORMAT_ERROR. If non-first Stream Header gives LZMA_FORMAT_ERROR, the message used for LZMA_DATA_ERROR is probably more appropriate. For example, the Stream decoder in liblzma uses LZMA_DATA_ERROR if LZMA_FORMAT_ERROR is returned by <a class="el" href="#ae03198e464f0d296e601ff841e100805" title="Decode Stream Header.">lzma_stream_header_decode()</a> when decoding non-first Stream.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">options</td><td>Target for the decoded Stream Header options. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer of LZMA_STREAM_HEADER_SIZE bytes.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Decoding was successful.</li>
<li>LZMA_FORMAT_ERROR: Magic bytes don't match, thus the given buffer cannot be Stream Header.</li>
<li>LZMA_DATA_ERROR: CRC32 doesn't match, thus the header is corrupt.</li>
<li>LZMA_OPTIONS_ERROR: Unsupported options are present in the header. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="aa92a383f85753bb79ee23227fa68186c" name="aa92a383f85753bb79ee23227fa68186c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa92a383f85753bb79ee23227fa68186c">&#9670;&#160;</a></span>lzma_stream_footer_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_footer_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Decode Stream Footer. </p>
<dl class="section note"><dt>Note</dt><dd>If Stream Header was already decoded successfully, but decoding Stream Footer returns LZMA_FORMAT_ERROR, the application should probably report some other error message than "file format not recognized". The file likely is corrupt (possibly truncated). The Stream decoder in liblzma uses LZMA_DATA_ERROR in this situation.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">options</td><td>Target for the decoded Stream Footer options. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer of LZMA_STREAM_HEADER_SIZE bytes.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Decoding was successful.</li>
<li>LZMA_FORMAT_ERROR: Magic bytes don't match, thus the given buffer cannot be Stream Footer.</li>
<li>LZMA_DATA_ERROR: CRC32 doesn't match, thus the Stream Footer is corrupt.</li>
<li>LZMA_OPTIONS_ERROR: Unsupported options are present in Stream Footer. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a3e25ca4205021302882a696283d45263" name="a3e25ca4205021302882a696283d45263"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3e25ca4205021302882a696283d45263">&#9670;&#160;</a></span>lzma_stream_flags_compare()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_flags_compare </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *</td>          <td class="paramname"><span class="paramname"><em>a</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *</td>          <td class="paramname"><span class="paramname"><em>b</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Compare two <a class="el" href="structlzma__stream__flags.html" title="Options for encoding/decoding Stream Header and Stream Footer.">lzma_stream_flags</a> structures. </p>
<p>backward_size values are compared only if both are not LZMA_VLI_UNKNOWN.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a</td><td>Pointer to <a class="el" href="structlzma__stream__flags.html" title="Options for encoding/decoding Stream Header and Stream Footer.">lzma_stream_flags</a> structure </td></tr>
    <tr><td class="paramname">b</td><td>Pointer to <a class="el" href="structlzma__stream__flags.html" title="Options for encoding/decoding Stream Header and Stream Footer.">lzma_stream_flags</a> structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Both are equal. If either had backward_size set to LZMA_VLI_UNKNOWN, backward_size values were not compared or validated.</li>
<li>LZMA_DATA_ERROR: The structures differ.</li>
<li>LZMA_OPTIONS_ERROR: version in either structure is greater than the maximum supported version (currently zero).</li>
<li>LZMA_PROG_ERROR: Invalid value, e.g. invalid check or backward_size. </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li><li class="navelem"><a class="el" href="stream__flags_8h.html">stream_flags.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
