'\" t
.\"     Title: minidumper
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/18/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "MINIDUMPER" "1" "06/18/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
minidumper \- Write minidump from WIN32PID to FILENAME\&.dmp
.SH "SYNOPSIS"
.HP \w'\fBminidumper\fR\ 'u
\fBminidumper\fR [\-d] [\-n] [\-q] [\-t\ \fITYPE\fR] \fIFILENAME\fR \fIWIN32PID\fR
.HP \w'\fBminidumper\fR\ 'u
\fBminidumper\fR \-h | \-V 
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
\-t, \-\-type     minidump type flags
\-n, \-\-nokill   don\*(Aqt terminate the dumped process
\-d, \-\-verbose  be verbose while dumping
\-h, \-\-help     output help information and exit
\-q, \-\-quiet    be quiet while dumping (default)
\-V, \-\-version  output version information and exit
  
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
The
\fBminidumper\fR
utility can be used to create a minidump of a running Windows process\&. This minidump can be later analysed using breakpad or Windows debugging tools\&.
.PP
\fBminidumper\fR
can be used with cygwin\*(Aqs Just\-In\-Time debugging facility by adding
error_start=minidumper
to the
CYGWIN
environment variable\&. If
CYGWIN
is set this way, then
\fBminidumper\fR
will be started whenever a program encounters a fatal exception\&.
.PP
\fBminidumper\fR
can also be started from the command line to create a minidump of any running process\&. For compatibility with
\fBdumper\fR
the target process is terminated after dumping unless the
\-n
option is given\&.
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
