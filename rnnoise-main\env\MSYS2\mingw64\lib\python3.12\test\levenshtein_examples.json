[["", "", 0], ["", "AabBbb", 12], ["", "AbaC", 8], ["", "B", 2], ["", "Ba", 4], ["", "CBaACCCa", 16], ["", "CCbBC", 10], ["", "CcbCbaaAB", 18], ["", "bAa", 6], ["", "bAaCABb", 14], ["A", "A", 0], ["A", "AA", 2], ["A", "AABCAabcC", 16], ["A", "AACB", 6], ["A", "AACC", 6], ["A", "ABAaBbc", 12], ["A", "ABCcC", 8], ["A", "ABa", 4], ["A", "ABbBabcaa", 16], ["A", "ABbbCBcA", 14], ["A", "ACCBcBbBb", 16], ["A", "ACa", 4], ["A", "ACb", 4], ["A", "ACcbbaAB", 14], ["A", "AaAaccAb", 14], ["A", "AaabbCC", 12], ["A", "AabB", 6], ["A", "AbBa", 6], ["A", "AbBaA", 8], ["A", "AbCBBCaC", 14], ["A", "AbCb", 6], ["A", "AbcB", 6], ["A", "B", 2], ["A", "BA", 2], ["A", "BABbCc", 10], ["A", "BABca", 8], ["A", "BB", 4], ["A", "BBaBBaa", 13], ["A", "BBbbaAA", 12], ["A", "BCCAAc", 10], ["A", "BCa", 5], ["A", "BCcaC", 9], ["A", "BCccaa", 11], ["A", "BaacCbC", 13], ["A", "BabCC", 9], ["A", "Bac", 5], ["A", "BbCbb", 10], ["A", "BbaBAC", 10], ["A", "Bbc", 6], ["A", "BcB", 6], ["A", "BcCAbCBA", 14], ["A", "BcaaCbCB", 15], ["A", "C", 2], ["A", "CA", 2], ["A", "CABAacB", 12], ["A", "CABBABBc", 14], ["A", "CAaaBc", 10], ["A", "CBAA", 6], ["A", "CBCcBaBAB", 16], ["A", "CBcbc", 10], ["A", "CC", 4], ["A", "CCA", 4], ["A", "CCB", 6], ["A", "CCBaACcC", 14], ["A", "CCBcAAacb", 16], ["A", "CaCCaA", 10], ["A", "CbAB", 6], ["A", "CbBaAACC", 14], ["A", "CbCCbB", 12], ["A", "CbacBb", 11], ["A", "CbbABc", 10], ["A", "CcC", 6], ["A", "CcCacAAAC", 16], ["A", "CccCbaCcb", 17], ["A", "a", 1], ["A", "aABABBa", 12], ["A", "aABBA", 8], ["A", "aABCaBCa", 14], ["A", "aACBabAC", 14], ["A", "aBCb", 7], ["A", "aBaB", 7], ["A", "aBbBb", 9], ["A", "aC", 3], ["A", "aCA", 4], ["A", "aaBBb", 9], ["A", "aaC", 5], ["A", "aaCb", 7], ["A", "aaCbABbb", 14], ["A", "aaCcbb", 11], ["A", "aaaaCcAB", 14], ["A", "aaabb", 9], ["A", "aaacCabCC", 17], ["A", "aacBccCAC", 16], ["A", "aacC", 7], ["A", "abCAb", 8], ["A", "abbBcaccc", 17], ["A", "acCAB", 8], ["A", "acacBcAA", 14], ["A", "b", 2], ["A", "bABACaB", 12], ["A", "bAbA", 6], ["A", "bAbaBc", 10], ["A", "bBAa", 6], ["A", "bBa", 5], ["A", "bBaCAab", 12], ["A", "bBaCaCBbB", 17], ["A", "bBbAbaBa", 14], ["A", "bBbacabAb", 16], ["A", "bCCbA", 8], ["A", "bCCbB", 10], ["A", "bCbbACc", 12], ["A", "bCbbc", 10], ["A", "baACBCB", 12], ["A", "babABb", 10], ["A", "bacB", 7], ["A", "bacacA", 10], ["A", "bb", 4], ["A", "bbCa", 7], ["A", "bbCbaa", 11], ["A", "bbbAcb", 10], ["A", "bc", 4], ["A", "bcAAcCAb", 14], ["A", "bcC", 6], ["A", "bcbbaab", 13], ["A", "c", 2], ["A", "cAB", 4], ["A", "cACAc", 8], ["A", "cAaBccaC", 14], ["A", "cAcAaCAc", 14], ["A", "cBAaC", 8], ["A", "cBAac", 8], ["A", "cBCAABbc", 14], ["A", "cBaCbab", 13], ["A", "cBacA", 8], ["A", "cBb", 6], ["A", "cC", 4], ["A", "cCBABaC", 12], ["A", "cCCCa", 9], ["A", "cCa", 5], ["A", "cCaAaCaCB", 16], ["A", "cCabbbCa", 15], ["A", "caacCacAb", 16], ["A", "cabbC", 9], ["A", "cb", 4], ["A", "cbC", 6], ["A", "cbCAaAaAB", 16], ["A", "cbbaaAbbB", 16], ["A", "cc", 4], ["A", "ccCcCBaB", 15], ["A", "ccabbC", 11], ["AA", "AA", 0], ["AA", "ACAA", 4], ["AA", "BAc", 4], ["AA", "BC", 4], ["AA", "BaccaAA", 10], ["AA", "Bba", 5], ["AA", "BbcbcccAB", 16], ["AA", "CAb", 4], ["AA", "Cc", 4], ["AA", "CcBACCa", 11], ["AA", "aCBa", 6], ["AA", "aaBA", 5], ["AA", "aaBac", 8], ["AA", "bAAabBA", 10], ["AA", "bABbacbb", 13], ["AA", "bBbBbc", 12], ["AA", "bC", 4], ["AA", "bCabb", 9], ["AA", "baAB", 5], ["AA", "bbBaBcCBB", 17], ["AA", "bbBbAaaB", 13], ["AA", "c", 4], ["AA", "cACaCbab", 13], ["AA", "cBbCbBcA", 14], ["AA", "cBba", 7], ["AA", "cCbBBCca", 15], ["AA", "cacaca", 10], ["AA", "ccbccAa", 11], ["AAA", "B", 6], ["AAA", "C", 6], ["AAA", "aBBbcAaBa", 14], ["AAA", "aacbbBbbc", 16], ["AAA", "abAb", 5], ["AAA", "cccaAbBcB", 15], ["AAAAABA", "CaBcAaa", 10], ["AAABACbC", "BBBC", 11], ["AAABAbaaA", "CBbbacb", 12], ["AAABa", "AA", 6], ["AAABc", "cbBabcaBA", 14], ["AAAC", "acBcCCcC", 13], ["AAACAbAaa", "BCc", 16], ["AAACBACc", "CaCabAc", 10], ["AAACCbbcA", "bc", 14], ["AAACa", "A", 8], ["AAACbCaa", "acABAAa", 9], ["AAACbaaa", "BBa", 13], ["AAAa", "bBaaCc", 9], ["AAAaAB", "BCB", 10], ["AAAaAcC", "CAbBcBBC", 12], ["AAAaCAAb", "aBbAbaaA", 12], ["AAAaCaA", "cBcABaAb", 11], ["AAAaaaaA", "ABBBcaA", 10], ["AAAb", "AcC", 6], ["AAAb", "B", 7], ["AAAbAB", "cCBaAbcc", 11], ["AAAbBcbc", "C", 15], ["AAAbaAABb", "cCcAaca", 15], ["AAAbbaA", "Cbcc", 12], ["AAAbbc", "ABcaCCbca", 11], ["AAAbcAcBA", "bBaACAC", 13], ["AAAbcbCa", "cabCCbCba", 10], ["AAAcAcCC", "cbBCAAB", 13], ["AAAcBAbCB", "cbbABC", 12], ["AAB", "CB", 4], ["AAB", "accAC", 7], ["AAB", "bCCbaBbAc", 15], ["AAB", "bbb", 5], ["AAB", "bcCBcC", 10], ["AABA", "AAc", 4], ["AABA", "cBCAbA", 7], ["AABABCcCc", "abAC", 12], ["AABAaB", "BAB", 6], ["AABAbAa", "BcaB", 11], ["AABAcb", "cca", 10], ["AABBA", "AbAaCBb", 8], ["AABBAACcC", "A", 16], ["AABBBcb", "B", 12], ["AABBCaB", "bAbBA", 8], ["AABBCabC", "BCC", 10], ["AABBCcB", "cABBBAACc", 10], ["AABBabAA", "A", 14], ["AABBac", "BACCCB", 10], ["AABBc", "CAaBaA", 7], ["AABCb", "CcaCab", 8], ["AABCbccb", "B", 14], ["AABCcA", "BabC", 8], ["AABCcB", "BAB", 8], ["AABCca", "AbBBC", 7], ["AABCcbBB", "Bab", 12], ["AABa", "ACCcBA", 7], ["AABa", "BCcbaAa", 11], ["AABa", "C", 8], ["AABaAc", "Ccaa", 9], ["AABaC", "aA", 7], ["AABaCccb", "AABaAAbc", 8], ["AABabcc", "ccaABC", 11], ["AABacB", "cCbaa", 9], ["AABacBac", "AcAa", 10], ["AABaccb", "CcbBbbc", 12], ["AABbBa", "bBAcbaaB", 10], ["AABbCc", "AccbaB", 8], ["AABba", "cBbAc", 7], ["AABbaBa", "BBaAbb", 10], ["AABc", "BccAAA", 10], ["AABcABaaA", "cbB", 14], ["AABcAC", "acabAC", 7], ["AABcBbcb", "baAACAbAB", 11], ["AABcCB", "ca", 10], ["AABcaBC", "AaAaab", 8], ["AAC", "CBabAaB", 11], ["AAC", "aBA", 5], ["AAC", "aCBCBbac", 13], ["AAC", "aCa", 5], ["AAC", "baaBAba", 11], ["AAC", "bbAbc", 7], ["AACA", "aaCB", 4], ["AACA", "ab", 7], ["AACA", "cc", 7], ["AACAba", "CBAAaAB", 9], ["AACAba", "b", 10], ["AACAbbc", "AbaCABCcB", 8], ["AACBACbcB", "b", 16], ["AACBC", "Abbc", 6], ["AACBC", "aaCcAb", 8], ["AACBCA", "A", 10], ["AACBCcbaa", "cbBca", 12], ["AACBaB", "AAcABBA", 7], ["AACBc", "aABCaACA", 10], ["AACC", "bB", 8], ["AACCA", "b", 10], ["AACCAC", "Ccab", 8], ["AACCBBAac", "abbabb", 14], ["AACCCCbc", "BBCCaB", 11], ["AACCCc", "bcCbaBACA", 14], ["AACCaBcA", "BaC", 13], ["AACCaca", "ccbaBBaB", 13], ["AACCc", "b", 10], ["AACa", "BAC", 4], ["AACaAaBA", "AbcbB", 11], ["AACaac", "C", 10], ["AACacB", "CcbCBaBb", 11], ["AACb", "AC", 4], ["AACb", "ac", 6], ["AACbAC", "BBCCBCa", 10], ["AACbC", "bb", 8], ["AACbbcaa", "bbaaBCBb", 14], ["AACbc", "AbcbbBcCB", 11], ["AACbc", "aCCB", 6], ["AACbcAaAb", "BABcaBBAA", 12], ["AACbcb", "cabBc", 8], ["AACc", "BbCC", 5], ["AACc", "bbb", 8], ["AACcAABAC", "CBAcBaABa", 11], ["AACcAb", "CBCbCACcb", 11], ["AACcCAAc", "AbAACbCC", 11], ["AACcabbC", "cAAccbAB", 9], ["AACcbA", "BCCCcaCc", 12], ["AAa", "ACCC", 6], ["AAa", "BcAC", 6], ["AAa", "CACcaAAAb", 13], ["AAa", "CAaCA", 6], ["AAa", "aCcBACb", 11], ["AAa", "abACAA", 7], ["AAa", "b", 6], ["AAa", "cAAaBA", 6], ["AAa", "cBaB", 6], ["AAaA", "ca", 6], ["AAaACcb", "ABCcb", 6], ["AAaB", "aCAC", 6], ["AAaB", "cbAb", 6], ["AAaBA", "bBAc", 8], ["AAaBB", "ABCCaCBb", 9], ["AAaBa", "AcB", 6], ["AAaBaB", "aCcb", 9], ["AAaBacBaC", "CacABc", 12], ["AAaBbAb", "cC", 14], ["AAaBbcaa", "caB", 12], ["AAaC", "aCbBcBCB", 13], ["AAaCACAB", "cBa", 14], ["AAaCCaCaA", "baACABCCA", 10], ["AAaCCcbb", "aa", 13], ["AAaCaccb", "AAA", 11], ["AAaCbAAAc", "babcbBccb", 14], ["AAaCcB", "aA", 9], ["AAaa", "Abcbb", 8], ["AAaaA", "ACaCAcaa", 9], ["AAaaB", "cAaaAB", 4], ["AAaaBbaC", "bbCaCBc", 12], ["AAaaCBabC", "bBb", 14], ["AAaacacca", "C", 17], ["AAaaccbA", "CB", 14], ["AAabAbac", "cbABacbbc", 11], ["AAabBBaA", "caCcAAB", 13], ["AAabCA", "CbACAA", 9], ["AAabCbCA", "ACb", 10], ["AAabb", "B", 9], ["AAabc", "cBc", 7], ["AAacA", "CBBcB", 8], ["AAacA", "Cc", 8], ["AAacCBCBc", "bAaacCc", 9], ["AAacCaBa", "a", 14], ["AAacCbCCC", "BaB", 15], ["AAaccABAA", "bBCb", 16], ["AAb", "AbB", 3], ["AAb", "B", 5], ["AAb", "aCBbBBa", 11], ["AAb", "bAAc", 4], ["AAb", "bABA", 5], ["AAb", "bBCbBbaCA", 16], ["AAb", "bbBBaB", 10], ["AAb", "bbCcAb", 8], ["AAbA", "Bbb", 6], ["AAbAAaBCC", "b", 16], ["AAbABB", "Ca", 11], ["AAbAaBBcB", "BABaCB", 10], ["AAbBBb", "a", 11], ["AAbBCCb", "bA", 12], ["AAbBbBcC", "C", 14], ["AAbBbbBcc", "aCA", 16], ["AAbC", "cBbBCBBb", 12], ["AAbCbBcC", "BaABcCaCc", 11], ["AAbCcCbC", "a", 15], ["AAba", "cBCabCC", 11], ["AAba", "cBcBCcaBc", 15], ["AAbaCabC", "Aa", 12], ["AAbabb", "cbBAca", 10], ["AAbb", "AcbaaAB", 9], ["AAbb", "bccb", 6], ["AAbbAcAB", "cbacaBBBA", 14], ["AAbbBAaAC", "bcbaabc", 11], ["AAbbbB", "bCa", 10], ["AAbcBbA", "aaccCbB", 8], ["AAbcaBBBA", "ABb", 13], ["AAbcacaB", "cAAAbBb", 12], ["AAbcbacC", "abbbAaCCa", 10], ["AAc", "ACAc", 2], ["AAc", "AbB", 4], ["AAc", "BAabA", 7], ["AAc", "aAbc", 3], ["AAc", "aaaaacAA", 12], ["AAc", "aab", 4], ["AAc", "c", 4], ["AAcA", "AAbAAA", 6], ["AAcABCA", "bBBAbab", 11], ["AAcAC", "cCAAa", 8], ["AAcAac", "cAcA", 6], ["AAcAbBcB", "AAAAB", 8], ["AAcAccAb", "Ccac", 11], ["AAcBBBcCB", "A", 16], ["AAcBbA", "Aa", 9], ["AAcC", "bCbcaCCa", 12], ["AAcCabaB", "cACAC", 11], ["AAcaACCBA", "abcBCca", 11], ["AAcaCBAcB", "cCaBCcC", 11], ["AAcab", "A", 8], ["AAcab", "AcaabCccc", 12], ["AAcb", "aCBCcCBcC", 14], ["AAcbAccc", "CcccbA", 12], ["AAcbCCAcB", "BbbCaACc", 11], ["AAcbCca", "a", 12], ["AAcbc", "caCcb", 7], ["AAcc", "CcaBbcC", 10], ["AAccB", "cCca", 7], ["AAccb", "aA", 7], ["AAccccbAA", "caAaa", 14], ["AB", "A", 2], ["AB", "AACca", 8], ["AB", "ABcAc", 6], ["AB", "ACCACCbC", 13], ["AB", "BAA", 4], ["AB", "BAbac", 7], ["AB", "BC", 4], ["AB", "BbaCAc", 10], ["AB", "Bbac", 7], ["AB", "BcA", 6], ["AB", "CAAacB", 8], ["AB", "CC", 4], ["AB", "CCaabBcb", 13], ["AB", "CCcB", 6], ["AB", "CbA", 5], ["AB", "aCAC", 6], ["AB", "bBA", 4], ["AB", "bCbB", 6], ["AB", "bCccbbb", 13], ["AB", "bbb", 5], ["AB", "bcAbAAA", 11], ["AB", "bcbC", 7], ["AB", "c", 4], ["AB", "cCabccbCB", 15], ["AB", "caCBB", 7], ["AB", "caaac", 9], ["AB", "cababCa", 12], ["ABA", "A", 4], ["ABA", "ACBcBa", 7], ["ABA", "AcAb", 4], ["ABA", "abbBaaC", 10], ["ABA", "c", 6], ["ABA", "caAaCCbc", 13], ["ABAAB", "cbB", 7], ["ABAABacbC", "Ba", 14], ["ABAAaCAa", "cABb", 14], ["ABAAaaBb", "BCACaB", 8], ["ABAAabb", "bAcABc", 9], ["ABAAabbCC", "cbcBc", 15], ["ABAAbBCb", "aCACb", 9], ["ABAAc", "AbCc", 5], ["ABAAcC", "A", 10], ["ABABBCaBb", "aAcBcc", 12], ["ABABCAB", "cbBBCaA", 8], ["ABABaaCc", "BBBBBAB", 11], ["ABABbCCab", "aAC", 13], ["ABAC", "abaAC", 4], ["ABACAccCC", "BBCBcbCAc", 11], ["ABACCAACA", "aAacA", 11], ["ABACaa", "BA", 8], ["ABACb", "b", 8], ["ABACbaCcC", "cCBBbCbC", 12], ["ABAa", "A", 6], ["ABAaAbbc", "ccaCBA", 13], ["ABAaB", "bb", 8], ["ABAaCCAB", "acba", 12], ["ABAaaBCBB", "BaCC", 12], ["ABAacbAbA", "ABAb", 10], ["ABAb", "AcA", 4], ["ABAb", "cCab", 5], ["ABAbACCB", "cBCB", 10], ["ABAbBC", "BBAacabC", 9], ["ABAbCCb", "BAA", 10], ["ABAbaCBB", "aaCbbbBB", 9], ["ABAbaCcC", "bac", 10], ["ABAc", "CBACCbBA", 11], ["ABAcAcB", "Ac", 10], ["ABAca", "aCcCaAbC", 12], ["ABAcaBc", "bCaabABAb", 14], ["ABAcaac", "BcA", 9], ["ABAcabA", "aa", 11], ["ABAcb", "cCa", 9], ["ABB", "acaccCcA", 15], ["ABB", "bAaAAC", 10], ["ABB", "bBbAA", 7], ["ABB", "bac", 6], ["ABBAAaCc", "CaCaCAaCc", 9], ["ABBAAbb", "acBbCba", 9], ["ABBAAc", "bBccabAcB", 11], ["ABBAbbAA", "abAaaa", 10], ["ABBAcBaa", "Ab", 13], ["ABBAcbAc", "abBccbA", 6], ["ABBBAa", "ccBbcBa", 9], ["ABBBAbAcc", "abCaCAbcc", 10], ["ABBBB", "cAaCBCbCb", 12], ["ABBBCaB", "BaAbBabBa", 12], ["ABBCC", "aBBbaBAab", 13], ["ABBCCc", "aAcAbB", 11], ["ABBCCcab", "AcBcbcbAc", 10], ["ABBa", "bA", 6], ["ABBaACA", "cbBcAB", 9], ["ABBaACCcb", "bbCCcCb", 10], ["ABBaa", "bcabCCC", 12], ["ABBabAbAC", "AbcbbAcCc", 10], ["ABBaba", "BCcbAaBCA", 13], ["ABBacb", "CAbcBAbBa", 11], ["ABBb", "Aabc", 5], ["ABBb", "aAcAb", 6], ["ABBbBAbCA", "CCBACAAb", 14], ["ABBbCbb", "bccbcAc", 11], ["ABBbb", "aAabbCbCC", 11], ["ABBcBBC", "bCaB", 10], ["ABBcCCC", "bABbaCCC", 5], ["ABBcb", "aCc", 7], ["ABBcbcC", "AACBbAC", 8], ["ABBccBcAa", "ABa", 12], ["ABBccCBA", "CCCACa", 12], ["ABBccc", "B", 10], ["ABC", "BAaACA", 8], ["ABC", "BCc", 4], ["ABC", "CAa", 6], ["ABC", "CBca", 5], ["ABC", "CbCbbCB", 11], ["ABC", "bacacB", 10], ["ABC", "c", 5], ["ABCAB", "bAaAc", 8], ["ABCAC", "BaCBaC", 7], ["ABCAa", "aABbb", 8], ["ABCAbAacA", "cbCAa", 11], ["ABCBC", "AB", 6], ["ABCBa", "CcAACBC", 8], ["ABCBa", "caBbccC", 10], ["ABCCAa", "A", 10], ["ABCCC", "CC", 6], ["ABCCbcBc", "AbCBcBB", 6], ["ABCCcCBB", "bbccAbBBc", 11], ["ABCCccaB", "aCcACCCa", 10], ["ABCaBa", "BBb", 8], ["ABCaBa", "bbAcAA", 10], ["ABCaC", "bcCcab", 8], ["ABCaa", "AbCcaAb", 6], ["ABCab", "b", 8], ["ABCabB", "AAc", 9], ["ABCb", "CBBBC", 7], ["ABCb", "aABCcBC", 7], ["ABCbAA", "Ba", 9], ["ABCbBBbc", "cabbbCbcB", 11], ["ABCbC", "cc", 8], ["ABCbCaCCc", "aABB", 15], ["ABCbaaCbb", "CBbBBABBB", 12], ["ABCbaaac", "aaCaCCCca", 13], ["ABCbbA", "BCaAa", 7], ["ABCbcC", "ccbBBa", 11], ["ABCc", "Aa", 6], ["ABCcAabbB", "cCCaBBc", 11], ["ABCcBcbC", "Bcbcc", 8], ["ABCcb", "CacBAb", 9], ["ABCccaAcC", "BaAcBbC", 12], ["ABa", "AaabCa", 7], ["ABa", "CaA", 5], ["ABaABc", "caC", 9], ["ABaAC", "Caca", 8], ["ABaAb", "bAaC", 7], ["ABaB", "c", 8], ["ABaBa", "aacCAc", 10], ["ABaBb", "acbaBCcb", 8], ["ABaBccB", "BacbBBBcb", 11], ["ABaCAb", "aba", 8], ["ABaCCC", "aCAaacbc", 10], ["ABaCc", "bB", 8], ["ABaa", "CCcCC", 10], ["ABabAbb", "BCAcB", 9], ["ABabBC", "acc", 9], ["ABabBaAc", "AbBCbaBBB", 12], ["ABababAC", "aabaaBAA", 8], ["ABabb", "bBb", 6], ["ABabcBacC", "aC", 14], ["ABabcBcc", "BAcB", 9], ["ABacCac", "bbaCBcbcB", 11], ["ABacbAAab", "bC", 16], ["ABacbbaCC", "CbCbcbBAA", 13], ["ABb", "AaAcB", 7], ["ABb", "B", 4], ["ABb", "BB", 3], ["ABb", "BBaCCAAA", 14], ["ABb", "bBBaA", 7], ["ABb", "baCaaBaa", 13], ["ABbA", "CaACAABa", 12], ["ABbACAC", "BAcBBaB", 12], ["ABbACAaAa", "Cca", 14], ["ABbAaa", "cbacaa", 7], ["ABbAaaB", "BBc", 11], ["ABbAacbAC", "bAbAaacaa", 11], ["ABbAbA", "aaAaB", 9], ["ABbBAb", "a", 11], ["ABbBcCbcA", "acaAacBAB", 15], ["ABbCC", "caACcc", 9], ["ABbCaAbb", "b", 14], ["ABbCb", "aABCBBca", 10], ["ABbCbA", "A", 10], ["ABbCbA", "AbCbB", 4], ["ABbCbbBa", "B", 14], ["ABbCcB", "AaCccab", 8], ["ABbaACBCc", "baBCbCbcB", 11], ["ABbaabcCC", "aaaacAAAA", 15], ["ABbacAAA", "CAAccb", 13], ["ABbb", "cAaC", 8], ["ABbbA", "BCcbb", 8], ["ABbbC", "ABBc", 4], ["ABbbbB", "BCabaCc", 12], ["ABbc", "bBcAAac", 10], ["ABbcc", "AbBABaaa", 11], ["ABbccBA", "cACaCcCC", 11], ["ABc", "a", 5], ["ABc", "aA", 5], ["ABc", "b", 5], ["ABcA", "CB", 6], ["ABcAa", "BcbAC", 6], ["ABcAaB", "ab", 9], ["ABcAbAa", "AcAABbb", 9], ["ABcAc", "c", 8], ["ABcAcCAc", "cAcBcCaC", 8], ["ABcBBcCCB", "BaAcBbC", 12], ["ABcBbAb", "aAbACcAca", 13], ["ABcBcCCbB", "cbAC", 13], ["ABcC", "Cabc", 6], ["ABcC", "c", 6], ["ABcCAaCc", "C", 14], ["ABcCAbCba", "CcBa", 12], ["ABcCBb", "CAb", 8], ["ABcCCa", "bCaca", 7], ["ABcCCcBa", "Cbc", 12], ["ABcCbbBbC", "b", 16], ["ABcaBC", "Bcc", 7], ["ABcaBb", "CB", 9], ["ABcb", "a", 7], ["ABcbAcc", "AbBa", 9], ["ABcbBC", "cCcAa", 10], ["ABcbCacCb", "ba", 14], ["ABcc", "bAAcbCa", 9], ["ABccA", "CA", 7], ["ABcccb", "CB", 10], ["AC", "A", 2], ["AC", "AAb", 4], ["AC", "AB", 2], ["AC", "ABBc", 5], ["AC", "ABaaaBc", 11], ["AC", "ACBaAbb", 10], ["AC", "AaBcBC", 8], ["AC", "AaCCCB", 8], ["AC", "AabB", 6], ["AC", "AacAaBc", 11], ["AC", "AbBCacbA", 12], ["AC", "AbbbcbACb", 14], ["AC", "B", 4], ["AC", "BBbA", 8], ["AC", "BCaa", 6], ["AC", "BbCaaA", 10], ["AC", "BcAab", 8], ["AC", "C", 2], ["AC", "CAcb", 5], ["AC", "Ca", 4], ["AC", "CabAB", 8], ["AC", "aABaABAB", 14], ["AC", "aAacbbcAC", 14], ["AC", "aBCaCBba", 13], ["AC", "aa", 3], ["AC", "aaaBBabAb", 16], ["AC", "baccAbcB", 13], ["AC", "c", 3], ["AC", "cACCaBA", 10], ["AC", "cCaaAacaA", 15], ["AC", "cbcbbcC", 12], ["ACA", "Aa", 3], ["ACA", "C", 4], ["ACA", "CbaaBc", 11], ["ACA", "aAABAAcb", 12], ["ACA", "cACCbaAaC", 12], ["ACAA", "AcaAAb", 5], ["ACAABBaC", "A", 14], ["ACAABcA", "bCBACacC", 10], ["ACAAC", "bB", 10], ["ACAAC", "cBaaCabaC", 11], ["ACAAa", "CAa", 4], ["ACAAaCaA", "AAacb", 9], ["ACAAbcaAa", "bba", 14], ["ACABA", "bAB", 6], ["ACABA", "c", 9], ["ACABABA", "CcC", 12], ["ACABACaB", "bcBAaAB", 8], ["ACABbCaBc", "cA", 15], ["ACAC", "aaBAA", 7], ["ACACB", "aaCcbaBC", 11], ["ACACBB", "cCb", 8], ["ACACBbccA", "BABbAc", 10], ["ACACC", "BaAA", 8], ["ACACa", "Bccc", 8], ["ACACbB", "aCaacAaC", 11], ["ACACbab", "cC", 11], ["ACAaA", "Aaabbc", 9], ["ACAaB", "caBBcB", 9], ["ACAaBA", "CAbCC", 8], ["ACAaCBccb", "c", 16], ["ACAabbCAc", "BB", 16], ["ACAb", "AaaCC", 7], ["ACAbBCaCA", "A", 16], ["ACAbbaaBA", "aacacABbc", 15], ["ACAbcB", "bcBCB", 7], ["ACAc", "CCbcaabA", 12], ["ACAcBCc", "AcaAbcb", 8], ["ACAcaabA", "CACbBC", 10], ["ACB", "ACbbaBb", 8], ["ACB", "aCbbc", 6], ["ACB", "baBacBAB", 12], ["ACB", "cabbCcBA", 11], ["ACBA", "CbCCCaCAc", 14], ["ACBAC", "CaABbbcBA", 13], ["ACBACBB", "cAABCbBC", 9], ["ACBACCba", "ACBCcCb", 5], ["ACBAbCBc", "bC", 12], ["ACBAba", "aAaCaC", 10], ["ACBAcb", "aCACc", 6], ["ACBB", "bAbb", 6], ["ACBBAB", "BbBCb", 8], ["ACBBABBa", "bbabA", 11], ["ACBBBaA", "bccbA", 10], ["ACBBBaCA", "bCCCB", 12], ["ACBBcAccc", "ab", 16], ["ACBCCC", "a", 11], ["ACBCbaa", "ACCAbCCb", 10], ["ACBaA", "BcBcBcA", 9], ["ACBaBa", "AaAa", 6], ["ACBabaa", "abcAbbb", 10], ["ACBabbab", "CAa", 11], ["ACBac", "BcbcCb", 9], ["ACBb", "bcCAca", 10], ["ACBbCaaBB", "Cc", 15], ["ACBba", "CcaAbbc", 10], ["ACBbaAbAC", "aCabbAC", 7], ["ACBbcAcB", "cb", 13], ["ACBcAB", "cb", 9], ["ACBcaaAAb", "ACBA", 10], ["ACC", "BcccCaAba", 15], ["ACC", "Ccb", 5], ["ACC", "aB", 5], ["ACC", "bBcABBbAC", 14], ["ACC", "cABA", 6], ["ACC", "caACC", 4], ["ACCA", "BaaBc", 9], ["ACCA", "BacAabAAa", 14], ["ACCACABB", "ABAAbBaBa", 11], ["ACCAbB", "CcbAacC", 11], ["ACCAccAB", "ACbCC", 10], ["ACCBAAC", "Ac", 11], ["ACCBAaBC", "a", 14], ["ACCBB", "BcCccabbA", 13], ["ACCBBcac", "CCB", 10], ["ACCBCBC", "AcaaCacba", 11], ["ACCBac", "aCACaCc", 7], ["ACCBb", "BaCac", 8], ["ACCBbcbAC", "AAaCaCBCC", 12], ["ACCBcCBB", "aBBABb", 10], ["ACCBcaaCa", "CBb", 14], ["ACCBcc", "bbAa", 11], ["ACCCAaCcC", "CBAc", 12], ["ACCCBac", "CcCBACBB", 9], ["ACCCC", "CACCACc", 5], ["ACCCb", "CbBaCcb", 9], ["ACCCbaCAc", "abACCC", 11], ["ACCaAACa", "C", 14], ["ACCaACAa", "a", 14], ["ACCaB", "A", 8], ["ACCaCA", "a", 10], ["ACCaaAc", "A", 12], ["ACCac", "a", 8], ["ACCbA", "aCCBa", 3], ["ACCbB", "BaAa", 10], ["ACCbC", "CcbaCC", 7], ["ACCbCba", "a", 12], ["ACCbaBbb", "aA", 14], ["ACCbba", "aaCBCbccB", 11], ["ACCbbbC", "BCbABAbaA", 13], ["ACCbcBa", "aCBaBcbC", 9], ["ACCbcbccB", "caAAbaCcb", 13], ["ACCcAccCc", "AccAb", 11], ["ACCcBcB", "AcBcAAca", 9], ["ACCcC", "a", 9], ["ACCcaC", "cCA", 8], ["ACCcacC", "BBaaAcAab", 14], ["ACa", "AcbCAbaB", 10], ["ACa", "Bca", 3], ["ACa", "ba", 4], ["ACa", "baAAaba", 10], ["ACaA", "A", 6], ["ACaA", "ABCAba", 6], ["ACaA", "acbCc", 8], ["ACaA", "cc", 7], ["ACaAC", "cABCBAc", 7], ["ACaAcABa", "cCAACC", 10], ["ACaB", "ABAbccB", 9], ["ACaB", "bAAAaAbAA", 13], ["ACaBBBCB", "AaAC", 10], ["ACaBBaAa", "AcaBcBbcB", 9], ["ACaBCB", "cc", 10], ["ACaBCBb", "b", 12], ["ACaBacCc", "bb", 15], ["ACaBcb", "BcCc", 9], ["ACaCACc", "c", 12], ["ACaCBccBc", "CcABCcBcb", 9], ["ACaCCBA", "AAcacC", 8], ["ACaa", "BAAc", 7], ["ACaaBAbc", "ACAccba", 9], ["ACaabb", "BCAb", 7], ["ACaacBa", "cBCBAc", 11], ["ACabABBA", "CAcCbB", 10], ["ACabC", "B", 9], ["ACabac", "BACcbCcbB", 10], ["ACabbcA", "aCacc", 7], ["ACaccaa", "AA", 11], ["ACb", "A", 4], ["ACb", "cBAab", 6], ["ACb", "ccbccbB", 11], ["ACbA", "AaBacA", 7], ["ACbA", "BaCCbA", 5], ["ACbA", "abACa", 7], ["ACbACcbaB", "ccc", 14], ["ACbAbaCBC", "acbaba", 9], ["ACbB", "aCACBAbb", 9], ["ACbBA", "CACB", 6], ["ACbBCC", "bcbCaAB", 11], ["ACbBa", "B", 8], ["ACbBaCab", "cbB", 11], ["ACbCCbBb", "aaca", 14], ["ACbCaA", "bAbC", 8], ["ACbCb", "c", 9], ["ACbCc", "ABc", 5], ["ACbCcbA", "accAAAaCc", 15], ["ACba", "acABAcbC", 11], ["ACba", "bCabba", 6], ["ACbaAaA", "BaaAbcC", 12], ["ACbaAbB", "AaBbbbb", 8], ["ACbaAc", "ABaBbCBcB", 12], ["ACbaCAC", "bBcaC", 8], ["ACbaCaB", "cbaA", 8], ["ACbaaaB", "CAcA", 10], ["ACbabABB", "BCBABBAc", 11], ["ACbabcac", "ABBA", 11], ["ACbbBB", "cAbbaC", 8], ["ACbbBb", "abAAbCab", 10], ["ACbbCc", "AccCbBAAB", 11], ["ACbbca", "cbABC", 9], ["ACbbccB", "b", 12], ["ACbcBBc", "CBacBbCbA", 11], ["ACbcCba", "abBBc", 10], ["ACbcCccA", "BBaA", 13], ["ACc", "AABbabcbb", 14], ["ACc", "ABbCcC", 6], ["ACc", "ABcBaB", 8], ["ACc", "a", 5], ["ACc", "b", 6], ["ACc", "baCBbbCbc", 13], ["ACc", "bcAcC", 6], ["ACc", "c", 4], ["ACcA", "CbcBBcbb", 13], ["ACcA", "aBBA", 5], ["ACcAAb", "ccAa", 6], ["ACcABac", "cA", 10], ["ACcAc", "baC", 8], ["ACcBCBbbB", "aA", 17], ["ACcBa", "ABBbBAbca", 12], ["ACcBa", "abbaca", 9], ["ACcBba", "baaAcAcA", 12], ["ACcBbaa", "CbCcCCBAC", 12], ["ACcC", "AcCABAC", 8], ["ACcCACb", "CABB", 9], ["ACcCC", "ccabBBC", 11], ["ACcCa", "Bcb", 8], ["ACcCacAa", "CbCba", 10], ["ACcCc", "bbBCC", 7], ["ACca", "cBBCBcbC", 12], ["ACcaCAb", "aaA", 9], ["ACcaaAb", "CaCAc", 8], ["ACcabc", "AAACb", 8], ["ACcac", "abBCBCc", 9], ["ACcbB", "caBaa", 10], ["ACcbC", "aCA", 7], ["ACcbaBb", "aCACCCAaA", 12], ["ACcbbC", "cacb", 8], ["ACcbcCBB", "A", 14], ["ACcc", "Cca", 4], ["ACcca", "accBbB", 8], ["ACccabBC", "Bb", 14], ["ACccb", "C", 8], ["ACccbCccb", "BbCacaA", 14], ["ACccca", "A", 10], ["Aa", "A", 2], ["Aa", "AAcac", 6], ["Aa", "ABBa", 4], ["Aa", "AbAabCcB", 12], ["Aa", "AbCCCCbcC", 16], ["Aa", "AbccAA", 9], ["Aa", "B", 4], ["Aa", "BA", 3], ["Aa", "BB", 4], ["Aa", "BBaccBAc", 14], ["Aa", "BCAaBb", 8], ["Aa", "BaBBAc", 10], ["Aa", "BaBcCaBc", 13], ["Aa", "BaCc", 6], ["Aa", "BabcCcAb", 14], ["Aa", "BbBB", 8], ["Aa", "C", 4], ["Aa", "CAabcaaAB", 14], ["Aa", "CAbbCbCB", 14], ["Aa", "CBBaBccb", 14], ["Aa", "CBCAccba", 12], ["Aa", "a", 2], ["Aa", "aAc", 4], ["Aa", "aacb", 5], ["Aa", "acAAacaB", 12], ["Aa", "acaA", 5], ["Aa", "acbc", 7], ["Aa", "b", 4], ["Aa", "bBA", 5], ["Aa", "bBABbc", 10], ["Aa", "baABcc", 10], ["Aa", "babbc", 8], ["Aa", "bbabBCac", 13], ["Aa", "cAAB", 5], ["Aa", "cC", 4], ["Aa", "caCAB", 8], ["Aa", "ccabB", 8], ["AaA", "Acbc", 6], ["AaA", "BBAcB", 8], ["AaA", "BaAbb", 6], ["AaA", "CAcBB", 8], ["AaA", "Cc", 6], ["AaA", "a", 4], ["AaA", "bAcaab", 7], ["AaA", "bCbCac", 10], ["AaA", "bCcaBAa", 10], ["AaA", "cAbaCAbB", 10], ["AaAA", "bcacBb", 10], ["AaAABca", "aACbAbaa", 9], ["AaAAbA", "CAbA", 6], ["AaAAbBCcc", "bbcAb", 14], ["AaAAcbcbB", "aAcCCBba", 11], ["AaABbA", "BBaACcACA", 12], ["AaABc", "cCcaBB", 9], ["AaABcBAbb", "BABAbaB", 11], ["AaACCAb", "C", 12], ["AaACCaac", "aabBbAbCA", 13], ["AaACa", "BBb", 10], ["AaAaABB", "CABcCCcCA", 16], ["AaAaBbCc", "B", 14], ["AaAaa", "cba", 8], ["AaAabb", "CACAA", 9], ["AaAaccCB", "bAaacBbaA", 12], ["AaAb", "cABAcb", 6], ["AaAbAaAb", "a", 14], ["AaAbAbbB", "aA", 12], ["AaAbBba", "CCBac", 12], ["AaAbb", "BbaB", 8], ["AaAbbc", "cAAcCC", 8], ["AaAbc", "a", 8], ["AaAc", "aAaCA", 6], ["AaAcA", "BCAAAba", 8], ["AaAccaBbc", "baAcAACaa", 11], ["AaB", "AbbAbb", 8], ["AaB", "Bca", 6], ["AaB", "bbaAb", 7], ["AaB", "bcABBBCc", 12], ["AaB", "caAb", 5], ["AaBA", "cBAB", 6], ["AaBACC", "BCAAbB", 10], ["AaBACaBA", "cAc", 13], ["AaBAa", "c", 10], ["AaBAc", "CcACba", 10], ["AaBAcBcaa", "cb", 15], ["AaBBAC", "ACAca", 9], ["AaBBAcbcc", "CbCcBb", 14], ["AaBBbcA", "A", 12], ["AaBCcbCB", "CCcAb", 11], ["AaBa", "abA", 4], ["AaBa", "bbbCabA", 10], ["AaBaaaCBa", "A", 16], ["AaBabBCb", "aBac", 9], ["AaBbB", "bBaa", 8], ["AaBba", "ab", 6], ["AaBbba", "caAbBAa", 7], ["AaBbbaac", "Cbcca", 12], ["AaBbbbA", "AabcBAa", 7], ["AaBc", "bAaaBaCCB", 11], ["AaBcA", "aCbCbB", 9], ["AaBcAbBC", "CBCACaBBA", 12], ["AaBccbA", "BBcaaccb", 10], ["AaC", "AACcabcAa", 13], ["AaC", "ABAaabA", 10], ["AaC", "Acc", 3], ["AaC", "aCCB", 5], ["AaC", "bbbbbbAA", 15], ["AaCA", "CCc", 6], ["AaCAAAcac", "ABB", 16], ["AaCAAbaa", "CCbABc", 11], ["AaCACCa", "BCcBACc", 10], ["AaCACCbcc", "CAacc", 10], ["AaCAcAcB", "bbA", 14], ["AaCB", "BbBC", 8], ["AaCB", "bCCbBAA", 10], ["AaCBa", "ACAbC", 7], ["AaCBaC", "Ba", 8], ["AaCBaaAAC", "CaBc", 13], ["AaCBbBBa", "CBbAcCBb", 12], ["AaCCAAC", "ACaBb", 9], ["AaCCAbcBB", "BCaab", 13], ["AaCCCB", "AaaAA", 8], ["AaCCCBB", "bB", 11], ["AaCCcbB", "BCcaCcB", 9], ["AaCCcc", "cbc", 9], ["AaCaAAa", "ccccBbcaC", 15], ["AaCaCccb", "aCB", 11], ["AaCaaaA", "a", 12], ["AaCaac", "CBabAA", 10], ["AaCac", "AbaBC", 7], ["AaCbbbBaB", "ccC", 16], ["AaCbcCb", "ccAAaB", 12], ["AaCc", "a", 6], ["AaCcABAa", "B", 14], ["AaCcAbC", "Bb", 12], ["AaCcBBcc", "cBb", 11], ["AaCcaCBaA", "ABca", 12], ["AaCcab", "bBcB", 9], ["AaCcacA", "BBBAcbC", 13], ["AaCcbBbCa", "BBBAaabCb", 14], ["AaCcc", "abcBAb", 10], ["AaCccBBCb", "cB", 14], ["AaCccbC", "baaaaAb", 11], ["Aaa", "BaAA", 5], ["Aaa", "CbabcBBB", 14], ["Aaa", "aAa", 2], ["Aaa", "aBB", 5], ["Aaa", "bAAbBaBcc", 13], ["Aaa", "ca", 4], ["AaaA", "aBabbB", 9], ["AaaA", "baCb", 6], ["AaaAAC", "a", 10], ["AaaAAaC", "b", 14], ["AaaACAcbA", "CBAcAC", 12], ["AaaACa", "BABcccC", 11], ["AaaAbBAab", "bacccCc", 16], ["AaaAcBCB", "Bbbb", 14], ["AaaAcb", "AcabAA", 8], ["AaaB", "aaCAC", 6], ["AaaBABa", "bBaC", 11], ["AaaBCaCC", "c", 15], ["AaaBa", "CBCBc", 8], ["AaaC", "aaAc", 3], ["AaaC", "b", 8], ["AaaC", "caaBCAABc", 12], ["AaaCB", "ABcbBCCA", 12], ["AaaCcbcc", "B", 15], ["Aaaa", "abCBCBa", 11], ["AaaaA", "CccAB", 9], ["AaaaBcb", "BCabBBBC", 11], ["AaaaCCC", "BBcaacaCB", 11], ["AaaacbC", "CcAcBACcb", 13], ["AaabA", "cB", 9], ["AaabACBca", "bAc", 12], ["AaabCAC", "bCaacCbB", 10], ["Aaaba", "BbccCaa", 12], ["AaabaBba", "aBaB", 9], ["<PERSON><PERSON><PERSON><PERSON>", "BcbBCcc", 13], ["AaabcBb", "abab", 8], ["<PERSON><PERSON><PERSON>", "BAbaab", 6], ["AaacACBc", "ACbbCaABA", 12], ["AaaccCBcb", "CBabaCACb", 11], ["AaacccCc", "c", 14], ["Aab", "BcBaaCBC", 12], ["Aab", "CBBaACcBA", 15], ["Aab", "a", 4], ["AabA", "acCbacA", 9], ["AabAAcBBB", "aa", 15], ["AabAbBbcB", "AcCcA", 14], ["AabAccB", "AcabBCb", 8], ["AabBABc", "AAabbCcCc", 9], ["AabBCAaC", "aBcCbc", 10], ["AabBCC", "A", 10], ["AabBaBcBB", "cbcc", 14], ["AabBacAaa", "aAc", 13], ["AabBba", "bcabb", 8], ["AabBbc", "CcAaA", 12], ["AabBcBca", "Ca", 13], ["AabBcbc", "ac", 10], ["AabCbA", "B", 11], ["AabCbC", "ccBbc", 8], ["A<PERSON>", "BBaa", 6], ["AabaACCC", "aaBBcccCa", 10], ["AabaCCccc", "BaB", 15], ["AabbA", "AB", 7], ["AabbABCBb", "aBB", 12], ["AabbB", "AA", 7], ["AabbB", "bCCC", 10], ["AabbbbbC", "bBbBaB", 10], ["AabbcCACc", "AAa", 14], ["Aabc", "bBCbc", 6], ["Aabc", "bbA", 6], ["AabcAAbCc", "Aa", 14], ["AabcBBb", "bAbAcCBB", 8], ["AabcBb", "bb", 8], ["AabcC", "BbbBcbbC", 10], ["AabcbCa", "CB", 12], ["Aac", "Bccc", 6], ["Aac", "CBABCbaA", 12], ["Aac", "Cb", 6], ["Aac", "a", 4], ["Aac", "aBbCCaaa", 13], ["Aac", "baBAcA", 8], ["Aac", "c", 4], ["Aac", "caAABac", 8], ["AacA", "ACcBA", 4], ["AacA", "BaaAccBcA", 11], ["AacAABbaa", "a", 16], ["AacABAB", "CCc", 12], ["AacAcbbC", "cbCa", 12], ["AacBBCc", "BcBAA", 10], ["AacBaA", "AAa", 7], ["AacBbaCBB", "CcbC", 12], ["AacBbcccc", "CbACCaBab", 18], ["AacC", "BcBcAbb", 12], ["AacCaBcaB", "CAAb", 13], ["AacCacC", "CaABC", 9], ["AacCcCcAC", "CCBBabBA", 16], ["AacCcc", "Aab", 8], ["AacaaCc", "bAaA", 10], ["AacacB", "aaBBB", 7], ["AacacBC", "CBBcAcba", 10], ["AacacBb", "AAabbcaC", 10], ["Aacb", "c", 6], ["AacbAAA", "BbcBaCCCb", 14], ["Aacbaa", "aCBbbCaAc", 12], ["Aacc", "ACAA", 6], ["Aacc", "CBAcB", 7], ["AaccAB", "baAcAcbcc", 11], ["AaccABc", "bB", 12], ["AaccAaAb", "acb", 10], ["AaccB", "ac", 6], ["AaccBcc", "BC", 11], ["AaccC", "bAbacBAa", 10], ["AaccaAc", "Bc", 12], ["AaccaBa", "B", 12], ["AaccaCcBb", "bABCca", 13], ["Aaccac", "c", 10], ["AacccAa", "AC", 11], ["AacccAcC", "ab", 14], ["AacccBBcC", "Bb", 15], ["Ab", "A", 2], ["Ab", "AA", 2], ["Ab", "AaabCAAA", 12], ["Ab", "AbACAaCAA", 14], ["Ab", "AbCCAAACc", 14], ["Ab", "AbCbAa", 8], ["Ab", "Ac", 2], ["Ab", "AcaABbc", 10], ["Ab", "BAcCa", 8], ["Ab", "BBc", 5], ["Ab", "BbcAcCCaa", 16], ["Ab", "CABa", 5], ["Ab", "CAaB", 5], ["Ab", "CCBcAbbc", 12], ["Ab", "CaBc", 6], ["Ab", "CbBCAbaBc", 14], ["Ab", "aCAccA", 10], ["Ab", "aab", 3], ["Ab", "acaCBAa", 12], ["Ab", "bA", 4], ["Ab", "bCBc", 7], ["Ab", "c", 4], ["Ab", "cBBB", 7], ["Ab", "cC", 4], ["Ab", "cCAaBbbBC", 14], ["Ab", "cCaAcc", 10], ["Ab", "caA", 5], ["Ab", "caCacCcBC", 16], ["Ab", "cbcacAC", 12], ["Ab", "ccAbB", 6], ["Ab", "ccb", 4], ["AbA", "BCBAaC", 9], ["AbA", "BbBAaA", 8], ["AbA", "CcBa", 6], ["AbA", "aAC", 5], ["AbA", "b", 4], ["AbA", "caABB", 7], ["AbA", "ccc", 6], ["AbAA", "CBba", 6], ["AbAAC", "BAAC", 3], ["AbAAC", "bbab", 7], ["AbAACaAb", "C", 14], ["AbAACbbBB", "BCbaa", 13], ["AbAAbC", "caCaAAAbA", 11], ["AbAAcbBa", "BabBBCa", 11], ["AbAB", "Ab", 4], ["AbAB", "Bcb", 6], ["AbAB", "ac", 7], ["AbABCa", "cC", 10], ["AbABa", "BAaaCB", 9], ["AbACABc", "aBCACA", 8], ["AbACBCA", "aBB", 10], ["AbACaa", "AcAba", 6], ["AbACbA", "A", 10], ["AbACbAA", "aACA", 7], ["AbACccccb", "cCbbCBA", 15], ["AbAaABB", "BcaaA", 9], ["AbAaAb", "CB", 11], ["AbAaC", "AacbbACba", 11], ["AbAaCAab", "CabaacCaA", 9], ["AbAaaa", "AaC", 8], ["AbAacaaac", "abACABAc", 8], ["AbAb", "bbccBB", 9], ["AbAbCba", "BCbA", 8], ["AbAbabac", "bc", 12], ["AbAbbBaab", "bbabb", 10], ["AbAc", "BcbBCbC", 11], ["AbAcACAa", "cBcBBA", 11], ["AbAcB", "c", 8], ["AbAcbCbca", "AcBaaB", 13], ["AbAccAbBc", "aACcAACbA", 11], ["AbAccBCa", "BACCAaA", 10], ["AbB", "AABC", 4], ["AbB", "AbCbCCc", 9], ["AbB", "BcaAbAAAb", 13], ["AbB", "bBBA", 5], ["AbB", "cAaaaB", 8], ["AbB", "cbbABB", 7], ["AbBAcCAC", "CAAbcCC", 9], ["AbBB", "AAAAC", 8], ["AbBBabc", "Bcb", 10], ["AbBBbCaA", "AaCccAAA", 11], ["AbBBcaAaa", "bcbBbcCb", 13], ["AbBCAacC", "c", 14], ["AbBCAcaa", "aa", 12], ["AbBCBbA", "abbcBb", 5], ["AbBaABC", "AabAAbc", 6], ["AbBaBB", "AAaAca", 9], ["AbBaCC", "CAb", 10], ["AbBaaAb", "ACcBcca", 11], ["AbBabBa", "bccBAbb", 10], ["AbBb", "A", 6], ["AbBb", "BbaAB", 7], ["AbBbA", "babCBABbb", 11], ["AbBbAABc", "AbAa", 9], ["AbBbCb", "bcAACCcac", 15], ["AbBbb", "aCAC", 9], ["AbBbbacbC", "CcbccAb", 14], ["AbBbcb", "Ab", 8], ["AbBbcbabC", "Ba", 14], ["AbBcBaaCc", "BbcAB", 13], ["AbBcaaCCA", "aba", 13], ["AbBcb", "BAaAcb", 6], ["AbBcc", "AaccBc", 6], ["AbBcccB", "cB", 10], ["AbC", "BccBcC", 9], ["AbC", "C", 4], ["AbCAA", "CCcCcacBa", 14], ["AbCABa", "ACBC", 6], ["AbCACa", "BcaBacAA", 12], ["AbCACcBbB", "C", 16], ["AbCAc", "BbCbcBCAb", 12], ["AbCB", "Caa", 8], ["AbCB", "acbAc", 7], ["AbCB", "caaaba", 10], ["AbCBbB", "BacABACaB", 13], ["AbCBbbC", "cACb", 10], ["AbCCcAbc", "abB", 12], ["AbCCca", "C", 10], ["AbCCcacba", "aaAaabcb", 13], ["AbCa", "CcAaCAaA", 10], ["AbCa", "aBcccBBa", 11], ["AbCaA", "bBbAB", 8], ["AbCaAAbab", "BCbCcb", 13], ["AbCaC", "AbABC", 4], ["AbCaabBCB", "Cb", 14], ["AbCabbc", "BcCaB", 9], ["AbCacAacb", "aaAAAAC", 12], ["AbCb", "A", 6], ["AbCb", "BbaBcaCb", 10], ["AbCbC", "Cc", 7], ["AbCbaB", "aCac", 7], ["AbCc", "bAaCac", 6], ["AbCcBBACC", "cACC", 10], ["AbCcbaBCA", "cccCAbbCA", 11], ["Aba", "ACA", 3], ["Aba", "C", 6], ["Aba", "aB", 4], ["Aba", "bccccA", 11], ["Aba", "cabbcC", 9], ["AbaAACBCb", "CACabC", 13], ["AbaAabca", "CBCBCA", 12], ["AbaAb", "cb", 8], ["AbaB", "aCaB", 3], ["AbaBAaCbC", "cbbacCbA", 10], ["AbaBAbaB", "cbaC", 12], ["AbaBBcbAA", "bCaaC", 14], ["AbaBCa", "B", 10], ["AbaBCbaB", "acBcb", 10], ["AbaBaca", "acAAABBB", 13], ["AbaBcCba", "accBCABAB", 12], ["AbaC", "BAAb", 7], ["AbaC", "aA", 6], ["AbaCb", "B", 9], ["AbaCbBc", "ACBAaCAcA", 11], ["Abaa", "aAcBb", 8], ["Abaa", "cbbAaabCc", 12], ["AbaaAbab", "CCaaBc", 11], ["AbaaAc", "bAba", 8], ["AbaaabCBb", "aa", 14], ["Abab", "bbbBb", 6], ["Abab", "cab", 4], ["AbabBBb", "CaAAc", 12], ["AbabCC", "CACcAabA", 12], ["AbabcCbb", "AbcCcbB", 7], ["AbacAa", "bCAAabcCa", 10], ["AbacB", "CBaCBAcBa", 11], ["AbacBCc", "B", 12], ["AbacBbBCC", "bC", 14], ["AbacaaCB", "B", 14], ["AbacaaaBC", "AacAa", 9], ["Abacb", "AcccbAb", 8], ["Abb", "Aba", 2], ["Abb", "Ac", 4], ["Abb", "BCcba", 8], ["AbbA", "BCb", 6], ["AbbABab", "baacCBb", 11], ["AbbACbab", "ccAcA", 12], ["AbbAcA", "CabbA", 7], ["AbbB", "b", 6], ["AbbBbCbAC", "bCCaBA", 13], ["AbbBbb", "BBcAbaAbB", 11], ["AbbBcaaBa", "aAAAaCAba", 13], ["AbbCACaB", "CbBBAbcB", 9], ["AbbCB", "AAC", 6], ["AbbCBbCA", "aAa", 14], ["AbbCCA", "bBBcbaAcC", 13], ["AbbCCaBCB", "AabbCB", 9], ["AbbCCcAAb", "ABCbbBCC", 13], ["AbbCaCCa", "B", 15], ["AbbCbBC", "Cc", 11], ["AbbCcA", "BaaCcBac", 11], ["AbbaBacBC", "BaCaCBcC", 10], ["<PERSON><PERSON><PERSON>", "aCbBbc", 8], ["Abbb", "bBab", 5], ["Abbbba", "BbbaA", 6], ["AbbcBbA", "cCbCcaC", 11], ["Abc", "CBAaABCCc", 13], ["Abc", "Cab", 5], ["Abc", "ba", 4], ["Abc", "cCBCa", 8], ["AbcAAAa", "CBcBBAabc", 11], ["AbcABaa", "cCcBCb", 10], ["AbcAcA", "B", 11], ["AbcB", "AbaCBBaA", 9], ["AbcBAA", "BAAb", 8], ["AbcBBaBB", "CCBACAA", 13], ["AbcBCcAA", "abbABCcCa", 8], ["AbcCACAbB", "aBcCb", 10], ["AbcCAacab", "a", 16], ["AbcCa", "cBAaab", 9], ["AbcCbbc", "ACccaBbAB", 10], ["AbcaA", "cac", 6], ["AbcaBABac", "aA", 14], ["AbcaBbBcA", "CBbA", 11], ["AbcaCAAAB", "ac", 15], ["AbcacabA", "ABbaAC", 10], ["Abcb", "AbCCcbb", 6], ["AbcbA", "b", 8], ["AbcbABc", "bCBCabbBb", 12], ["AbcbB", "A", 8], ["AbcbbC", "bABbBAa", 10], ["Abcc", "bAABaA", 9], ["Abcc", "cAABAAa", 11], ["AbccB", "cA", 8], ["Abccc", "bba", 8], ["Ac", "ACbAa", 7], ["Ac", "AbB", 4], ["Ac", "BCB", 5], ["Ac", "BCacCBaBb", 15], ["Ac", "BaaCB", 8], ["Ac", "BacBbABCa", 15], ["Ac", "Bacc", 5], ["Ac", "BbaBaACcC", 14], ["Ac", "BccaAa", 10], ["Ac", "CA", 4], ["Ac", "CABacA", 8], ["Ac", "CABbbCA", 11], ["Ac", "CAa", 4], ["Ac", "CBCaAcBaB", 14], ["Ac", "CCA", 5], ["Ac", "CCcbBBc", 12], ["Ac", "CbBBBBbBA", 18], ["Ac", "Cbba", 8], ["Ac", "aCaa", 6], ["Ac", "aCbca", 7], ["Ac", "aaBC", 6], ["Ac", "aaCAa", 8], ["Ac", "aaaA", 7], ["Ac", "aabca", 7], ["Ac", "abcAAaB", 11], ["Ac", "acaBBbaAC", 15], ["Ac", "bAaa", 6], ["Ac", "bCCCCC", 11], ["Ac", "baAcB", 6], ["Ac", "bc", 2], ["Ac", "cB", 4], ["Ac", "cBaaaAAAb", 16], ["Ac", "cC", 3], ["Ac", "cCAcbCA", 10], ["Ac", "cCcCAAA", 12], ["Ac", "cbAAcc", 8], ["AcA", "ABAACCbc", 12], ["AcA", "BBBb", 8], ["AcA", "CcabCab", 11], ["AcA", "acA", 1], ["AcA", "bAbCc", 7], ["AcA", "bccBB", 8], ["AcA", "cCCA", 5], ["AcAAAb", "Acba", 7], ["AcAABAc", "bCaa", 11], ["AcAABBc", "c", 12], ["AcAACA", "cCb", 8], ["AcAACBb", "AbaA", 9], ["AcAAbb", "abBbA", 9], ["AcAAcBaB", "caB", 10], ["AcABCCba", "Ca", 12], ["AcABa", "aAbBBCA", 9], ["AcAC", "c", 6], ["AcACB", "aBBCbBabC", 13], ["AcACCBA", "c", 12], ["AcACCaACA", "aCAAAcABC", 12], ["AcACaA", "Ac", 8], ["AcACcC", "bABBAcC", 8], ["AcACccA", "CaacccB", 8], ["AcAa", "b", 8], ["AcAaaCBA", "abB", 12], ["AcAac", "BBCCC", 9], ["AcAb", "cCBBcBCAc", 14], ["AcAbA", "cCbbBcb", 11], ["AcAbBabb", "BBCBa", 12], ["AcAbBcABC", "baCcCcb", 15], ["AcAbCB", "ccCCaC", 10], ["AcAba", "CcBcBcC", 11], ["AcAbb", "bAAbc", 6], ["AcAcB", "aAcBAbC", 8], ["AcAcCB", "bc", 10], ["AcAcCCCbb", "BAaBC", 14], ["AcAcbacA", "cb", 12], ["AcB", "AbCbCa", 8], ["AcB", "CCABca", 8], ["AcB", "aBA", 5], ["AcB", "b", 5], ["AcB", "bCB", 3], ["AcB", "cBBaB", 8], ["AcB", "caBcC", 7], ["AcB", "caCCCaA", 12], ["AcBAAa", "ac", 9], ["AcBAbbAA", "ACCB", 12], ["AcBB", "AAAccBcA", 10], ["AcBB", "BaccCCACc", 15], ["AcBB", "bBCA", 8], ["AcBB", "cCAaaBaBb", 12], ["AcBBAC", "AaCA", 8], ["AcBBACA", "aAA", 9], ["AcBBACCaC", "abBcCAC", 8], ["AcBBaB", "aC", 10], ["AcBBbbaBc", "CC", 16], ["AcBBbcb", "ABBAAc", 8], ["AcBBcabc", "a", 14], ["AcBC", "ACabcCbBB", 12], ["AcBC", "AacAcBCcA", 10], ["AcBC", "bCbcAbbA", 13], ["AcBCAcb", "AB", 10], ["AcBCCaAbc", "ca", 14], ["AcBCCb", "aCcCAccc", 11], ["AcBCaccAA", "cBAcc", 9], ["AcBCb", "aBbaBac", 11], ["AcBCcbCb", "AAABCa", 11], ["AcBCcbbaB", "bBbC", 14], ["AcBa", "BBbCBAA", 10], ["AcBaCCbCa", "bbBBb", 14], ["AcBaCCc", "aaCcca", 8], ["AcBaacCb", "BBbbCbcC", 13], ["AcBbAb", "BabcbBbc", 11], ["AcBbAc", "cbAbC", 7], ["AcBbaac", "bA", 11], ["AcBbbbB", "ACaa", 11], ["AcBc", "abAcbb", 7], ["AcBcAcA", "aaCBAaAAa", 11], ["AcBcBBB", "a", 13], ["AcBcCba", "BCb", 8], ["AcBca", "Ab", 7], ["AcBcbbA", "ABb", 8], ["AcBcbbA", "BB", 11], ["AcBcbcA", "B", 12], ["AcBccbbbC", "CbBAAbB", 13], ["AcBcccC", "b", 13], ["AcC", "ACAbc", 6], ["AcC", "bBBA", 8], ["AcC", "bbCBACbcA", 14], ["AcC", "c", 4], ["AcC", "cBBa", 8], ["AcCACC", "bbAcCCcc", 8], ["AcCACaCA", "CBcCCB", 11], ["AcCAb", "bCBAACBC", 12], ["AcCAbA", "Bac", 11], ["AcCAbcaBb", "Cb", 14], ["AcCAcCCCA", "AcCC", 10], ["AcCBA", "a", 9], ["AcCBBaBb", "AC", 12], ["AcCBba", "CaaBCAa", 10], ["AcCCAA", "BcA", 8], ["AcCCBcc", "bbBACCa", 13], ["AcCCCbb", "AcaAc", 9], ["AcCCb", "bb", 8], ["AcCCba", "BC", 10], ["AcCCcBACB", "cb", 15], ["AcCCccCB", "baaCaAaB", 12], ["AcCa", "AAaCcC", 8], ["AcCaAcccA", "AaaaBaCBB", 13], ["AcCaBCACC", "a", 16], ["AcCaC", "CAcC", 6], ["AcCaa", "Ccab", 6], ["AcCabb", "BbCBa", 9], ["AcCb", "A", 6], ["AcCbA", "BaCcaC", 9], ["AcCbA", "aA", 7], ["AcCbBb", "CCAcb", 7], ["AcCba", "cBBbCbCc", 12], ["AcCbac", "cCBa", 5], ["AcCbbbc", "BbBbABCBc", 12], ["AcCbc", "ABBaaAc", 10], ["AcCc", "BBBAA", 10], ["AcCc", "CABBBCaCb", 13], ["AcCc", "acCbACCcc", 11], ["AcCcA", "AbCbabBc", 11], ["AcCcABb", "cCBaAc", 9], ["AcCcBB", "AAaCAaA", 10], ["AcCcabBCB", "BcA", 15], ["AcCcb", "bC", 8], ["AcCccABB", "bbCbbCB", 12], ["AcCccbBBb", "ACABAa", 12], ["Aca", "BaAA", 6], ["Aca", "CCBCCB", 11], ["Aca", "cA", 3], ["AcaAAA", "CcbbaB", 9], ["AcaABAaCb", "CcCACC", 12], ["AcaABCa", "bacAcA", 9], ["AcaAcBC", "ccbAaa", 10], ["AcaBACCb", "ACacBaBAb", 8], ["AcaBAabCb", "ACACAbaCB", 9], ["AcaBC", "ABacC", 4], ["AcaBCa", "AbBC", 6], ["AcaBacc", "CBB", 11], ["AcaBcba", "AbAbCBcC", 10], ["AcaC", "Aca", 2], ["AcaCB", "C", 8], ["AcaCBB", "cbacccC", 11], ["AcaCBb", "CCcaBC", 8], ["AcaCa", "bCA", 7], ["AcaCaC", "BB", 12], ["AcaCbcC", "aBBCccaC", 9], ["AcaCc", "CaaCBBb", 10], ["AcaCc", "aBaCb", 5], ["AcaCcAc", "acA", 8], ["Acaa", "BACcCA", 7], ["AcaaA", "bacbCca", 10], ["Acaaa", "ACccca", 6], ["AcaaaaaBA", "b", 17], ["AcaaabbAC", "AACbc", 12], ["AcaabBaAc", "aCAbBBCaA", 10], ["AcaacaAAB", "CA", 15], ["AcaacbaC", "A", 14], ["Acab", "AB", 5], ["AcabA", "BCcca", 8], ["AcabBAcB", "B", 14], ["AcabCaab", "cCCaBB", 9], ["AcabcC", "AC", 8], ["Acabca", "cCCC", 9], ["AcacaAbbb", "abB", 13], ["AcacaBC", "bbaCB", 9], ["AcacaCCCc", "aBbbcACA", 14], ["AcaccB", "aaBc", 7], ["Acb", "BcbA", 4], ["Acb", "BccCaB", 9], ["Acb", "aC", 4], ["Acb", "bAccAa", 8], ["Acb", "ccAcbaB", 8], ["AcbA", "cA", 4], ["AcbAAcaA", "Cba", 11], ["AcbAB", "cbBbAa", 8], ["AcbACBAaA", "cc", 15], ["AcbB", "C", 7], ["AcbBa", "aAABbACcB", 13], ["AcbBaCb", "bCcacBbA", 12], ["AcbBaba", "CbbCBaB", 9], ["AcbBb", "abBbA", 5], ["AcbC", "bbCaA", 8], ["AcbCAC", "CcACABBb", 10], ["AcbCAc", "aaBCaAbAa", 12], ["AcbCBcc", "aA", 13], ["AcbCaCb", "ccAABC", 10], ["AcbCcBCa", "bbbcAcB", 11], ["AcbCcbCa", "CcaABBcCa", 11], ["Acba", "aACbbBcc", 11], ["AcbaCAA", "bAaBbaca", 10], ["Acbab", "acBc", 6], ["AcbbAcCa", "cCb", 12], ["AcbbC", "B", 9], ["Acbbb", "cCbCAACA", 13], ["AcbbcAB", "cca", 9], ["Acbc", "AbCaCB", 8], ["Acbc", "Bac", 6], ["AcbcBB", "ABBBB", 5], ["AcbcBcA", "AaaBaba", 11], ["AcbcCbcbA", "ac", 15], ["Acc", "BbBCABBb", 14], ["Acc", "aAbBbBca", 12], ["Acc", "aa", 5], ["Acc", "aaBcC", 6], ["Acc", "bCbAaCab", 13], ["AccA", "BCBA", 5], ["AccA", "aC", 6], ["AccAB", "Bcb", 7], ["AccABcbBC", "AAcBcAcc", 9], ["AccACBbA", "c", 14], ["AccAbcC", "cbbB", 10], ["AccBbab", "bcAaaaA", 10], ["AccC", "BBbca", 8], ["AccCBBACa", "b", 17], ["AccCCCcaa", "aabC", 15], ["AccCcAA", "CCbAaABCA", 13], ["Acca", "C", 7], ["Acca", "aB", 7], ["AccaCABA", "CBBBc", 13], ["AccaaAbbC", "BaAB", 13], ["Accb", "baBaaCaCa", 15], ["Accbbbb", "bA", 12], ["Accbcc", "aaAAcb", 9], ["Accc", "ABbAB", 8], ["AcccAC", "BAAbCacBb", 13], ["AcccAbbB", "BaBCa", 14], ["AcccBAAbC", "CCbCb", 13], ["AcccCACac", "BAbccaC", 11], ["AcccCabab", "bBAAaBaA", 13], ["AcccCb", "BACacbcC", 9], ["AcccaCb", "CBBCcaCB", 8], ["Acccac", "acCaAcaCb", 9], ["B", "A", 2], ["B", "AAABB", 8], ["B", "AAAcc", 10], ["B", "AABBcC", 10], ["B", "AABCBb", 10], ["B", "AAC", 6], ["B", "AACA", 8], ["B", "AACaaaa", 14], ["B", "AAcaBa", 10], ["B", "AAcacbab", 15], ["B", "ABBc", 6], ["B", "ABCBcBCB", 14], ["B", "ABaAba", 10], ["B", "ABaB", 6], ["B", "ABaBba", 10], ["B", "ABaaCcBa", 14], ["B", "ABc", 4], ["B", "ACB", 4], ["B", "ACCCCCBc", 14], ["B", "ACbbC", 9], ["B", "ACbbCA", 11], ["B", "AaA", 6], ["B", "AaACbbaaB", 16], ["B", "AaAaCCbaa", 17], ["B", "AaBaBcba", 14], ["B", "AabcAaa", 13], ["B", "AacCacAb", 15], ["B", "AaccbBacB", 16], ["B", "Ab", 3], ["B", "AbBc", 6], ["B", "AbaBCcaBa", 16], ["B", "Abc", 5], ["B", "AcA", 6], ["B", "AcBA", 6], ["B", "AcBaBbCc", 14], ["B", "AcCc", 8], ["B", "AcaC", 8], ["B", "AccBacc", 12], ["B", "B", 0], ["B", "BAA", 4], ["B", "BBAbCc", 10], ["B", "BBAccA", 10], ["B", "BBBCb", 8], ["B", "BBa", 4], ["B", "BBaAcacab", 16], ["B", "BBc", 4], ["B", "BC", 2], ["B", "BCC", 4], ["B", "BCCbaBA", 12], ["B", "BCbCb", 8], ["B", "BCcC", 6], ["B", "BCcCccC", 12], ["B", "BCcb", 6], ["B", "BCcc", 6], ["B", "Ba", 2], ["B", "BaBCAaCAa", 16], ["B", "Baca", 6], ["B", "BacbcaCaC", 16], ["B", "BbcCCbAbb", 16], ["B", "BcACbAAC", 14], ["B", "BcACbbbc", 14], ["B", "BcabCBbab", 16], ["B", "C", 2], ["B", "CAAb", 7], ["B", "CAAba", 9], ["B", "CACcA", 10], ["B", "CB", 2], ["B", "CBCcAcbb", 14], ["B", "CBc", 4], ["B", "CBcA", 6], ["B", "CBcCC", 8], ["B", "CCaaCaac", 16], ["B", "CCcBBCb", 12], ["B", "Ca", 4], ["B", "CaCC", 8], ["B", "Caa", 6], ["B", "Cab", 5], ["B", "Cacbc", 9], ["B", "Cb", 3], ["B", "Cbc", 5], ["B", "CbcaAaACC", 17], ["B", "CbccCbca", 15], ["B", "Cc", 4], ["B", "CcAacCAA", 16], ["B", "CcBb", 6], ["B", "CcbBb", 8], ["B", "Ccc", 6], ["B", "a", 2], ["B", "aAA", 6], ["B", "aAACAb", 11], ["B", "aABCB", 8], ["B", "aACaCCBbA", 16], ["B", "aB", 2], ["B", "aBBbc", 8], ["B", "aBabc", 8], ["B", "aBcbCA", 10], ["B", "aBccCCBC", 14], ["B", "aC", 4], ["B", "aCBBAAc", 12], ["B", "aCBcA", 8], ["B", "aCCaAabB", 14], ["B", "aCCcbbac", 15], ["B", "aCa", 6], ["B", "aCaBABACa", 16], ["B", "aCcbCA", 11], ["B", "aaBBac", 10], ["B", "aaBCC", 8], ["B", "aab", 5], ["B", "aabBcbBa", 14], ["B", "aacbAB", 10], ["B", "abA", 5], ["B", "abAAA", 9], ["B", "abBBCBBcb", 16], ["B", "abC", 5], ["B", "abCcacAC", 15], ["B", "ac", 4], ["B", "acAbAB", 10], ["B", "acBcBba", 12], ["B", "b", 1], ["B", "bA", 3], ["B", "bAAbcbb", 13], ["B", "bABAacc", 12], ["B", "bACA", 7], ["B", "bAaAbBbcb", 16], ["B", "bAc", 5], ["B", "bB", 2], ["B", "bC", 3], ["B", "bCAbccaCA", 17], ["B", "bCCA", 7], ["B", "bCabCB", 10], ["B", "bCabcCa", 13], ["B", "ba", 3], ["B", "baba", 7], ["B", "babaa", 9], ["B", "bb", 3], ["B", "bbc", 5], ["B", "bbcbCCCb", 15], ["B", "bc", 3], ["B", "bcCBCacb", 14], ["B", "bcaAcbbB", 14], ["B", "c", 2], ["B", "cABAAb", 10], ["B", "cAaaACc", 14], ["B", "cAcCA", 10], ["B", "cAccC", 10], ["B", "cB", 2], ["B", "cBBBa", 8], ["B", "cBaCCba", 12], ["B", "cBbbaAaBC", 16], ["B", "cBcCaA", 10], ["B", "cC", 4], ["B", "cCABB", 8], ["B", "cCBAacA", 12], ["B", "cCbBBAccB", 16], ["B", "cCbcaBaca", 16], ["B", "cCcBaC", 10], ["B", "cCcbcaCcc", 17], ["B", "cabBca", 10], ["B", "cabccaAAB", 16], ["B", "cb", 3], ["B", "cbAACcCCb", 17], ["B", "cbAC", 7], ["B", "cbBbCC", 10], ["B", "cbC", 5], ["B", "cbCaba", 11], ["B", "cbCbBBaa", 14], ["B", "cbaaAAA", 13], ["B", "cbaaCAb", 13], ["B", "cbbCaccA", 15], ["B", "cbbb", 7], ["B", "cbcCa", 9], ["B", "ccABaB", 10], ["B", "ccAc", 8], ["B", "ccCbCbc", 13], ["B", "ccabaC", 11], ["B", "ccb", 5], ["BA", "A", 2], ["BA", "ABaabAbAc", 14], ["BA", "B", 2], ["BA", "BBABbCAaB", 14], ["BA", "BBcaC", 7], ["BA", "BBcacAbcb", 14], ["BA", "BCbCbbcAB", 14], ["BA", "CACCAaac", 14], ["BA", "CAaaacb", 12], ["BA", "CBC", 4], ["BA", "aACbbcba", 14], ["BA", "ab", 4], ["BA", "abCAa", 7], ["BA", "b", 3], ["BA", "bABaAca", 10], ["BA", "baCCBbC", 12], ["BA", "bbBcB", 8], ["BA", "bbbCC", 9], ["BA", "bbbaBA", 8], ["BA", "bbbbCBb", 12], ["BA", "cBABaC", 8], ["BA", "cBBbbA", 8], ["BA", "cC", 4], ["BA", "ccB", 6], ["BA", "ccCa", 7], ["BAA", "A", 4], ["BAA", "abA", 4], ["BAA", "acCAc", 8], ["BAA", "bB", 5], ["BAA", "bbcACCCAB", 13], ["BAA", "bcBC", 7], ["BAA", "cBaCaBc", 10], ["BAAA", "B", 6], ["BAAA", "BBCacaC", 10], ["BAAA", "bBAc", 5], ["BAAAAAA", "CbcbAc", 12], ["BAAAACb", "cbcACabBc", 13], ["BAAAC", "cbCBcbCC", 12], ["BAAAaCCAB", "bcCB", 12], ["BAAAabC", "CCaaaAcc", 11], ["BAAAbcBa", "aCcAAAcc", 12], ["BAABC", "bccab", 9], ["BAABaC", "Cbb", 11], ["BAABabAb", "ACC", 14], ["BAAC", "bB", 7], ["BAAC", "ccaBab", 10], ["BAAC", "ccbC", 6], ["BAACAA", "a", 11], ["BAACaC", "Cac", 7], ["BAAaA", "a", 8], ["BAAaACaC", "bb", 15], ["BAAaB", "ccAcbA", 9], ["BAAaa", "b", 9], ["BAAaa", "cacCaBc", 11], ["BAAaaac", "c", 12], ["BAAaabB", "aaAAA", 9], ["BAAaac", "BCaCcCAB", 12], ["BAAacA", "ccBAcCAc", 11], ["BAAb", "AA", 4], ["BAAbAC", "BbAccCba", 10], ["BAAbCbbC", "CCCBBc", 11], ["BAAbacbC", "aBAabc", 9], ["BAAbbACC", "CCABb", 11], ["BAAcCBa", "ACcA", 9], ["BAAcaBBCa", "AccB", 12], ["BAAcac", "Bb", 10], ["BAAcc", "CACbabA", 11], ["BAB", "AcbBaaBa", 11], ["BAB", "BB", 2], ["BAB", "Caa", 5], ["BAB", "cc", 6], ["BABA", "abbccc", 11], ["BABA", "caCca", 8], ["BABB", "bBCCBcAcb", 13], ["BABBAAc", "caAaa", 10], ["BABBbCA", "bAca", 9], ["BABBc", "BCCcAcCC", 11], ["BABCB", "AB", 6], ["BABCcBbA", "CBb", 10], ["BABa", "abbbaccAa", 14], ["BABabAB", "b", 12], ["BABacA", "abcBA", 8], ["BABb", "cC", 8], ["BABbC", "BaA", 7], ["BABbb", "Ccc", 10], ["BABc", "aB", 5], ["BABcACCaC", "CbbBaCbaC", 10], ["BABcBA", "CA", 9], ["BABcBCaBc", "CBAaBCC", 11], ["BABcBcBa", "bCBACcc", 11], ["BABcaBB", "bAaabb", 7], ["BABcb", "CcCAaC", 11], ["BABcbcaa", "BbbCcAAab", 11], ["BABccba", "cCbbbA", 10], ["BAC", "BB", 4], ["BAC", "BBacAbC", 8], ["BAC", "BaacBaC", 9], ["BAC", "BbbBaB", 9], ["BAC", "CAAaa", 8], ["BAC", "CBBbBccbc", 15], ["BAC", "b", 5], ["BAC", "babAAcA", 10], ["BACACcCcc", "cbB", 16], ["BACB", "AaCABa", 7], ["BACBAACba", "BaA", 13], ["BACBCAC", "A", 12], ["BACBaB", "Bcbc", 8], ["BACBbCbB", "ACaAAaCb", 12], ["BACC", "ab", 7], ["BACCBcbaa", "ab", 15], ["BACCCAaCA", "bCCcBb", 12], ["BACCbABA", "AAAAcAB", 10], ["BACCbAac", "AcBCaacB", 10], ["BACCc", "BbAAcA", 7], ["BACCc", "aBbbBBCa", 12], ["BACaC", "CB", 8], ["BACab", "bCA", 6], ["BACb", "AcBC", 6], ["BACbAbCbC", "BbC", 12], ["BACbbaaAc", "ACabC", 11], ["BACcBbcCb", "AcbB", 11], ["BACcbBbc", "AC", 12], ["BACccabb", "bAcAAa", 10], ["BACcccA", "aacC", 10], ["BAa", "AC", 4], ["BAa", "AbCBcA", 9], ["BAa", "CabbAabb", 11], ["BAa", "cBaAa", 4], ["BAaA", "BCB", 6], ["BAaABbB", "ABabABcbC", 9], ["BAaAbCc", "acb", 10], ["BAaAcCb", "AbbC", 10], ["BAaAcbB", "Aa", 10], ["BAaBBaAb", "bBaBcaAAa", 9], ["BAaBaB", "A", 10], ["BAaBbCc", "cacBaAcaB", 13], ["BAaBbabc", "A", 14], ["BAaBcCaaa", "ab", 15], ["BAaBcCb", "aCAA", 11], ["BAaCB", "BBb", 7], ["BAaCB", "aABACA", 7], ["BAaCaA", "acB", 9], ["BAaCabCCa", "b", 16], ["BAaCbAC", "bA", 10], ["BAaCbB", "aCcbbAAA", 13], ["BAaCcc", "bc", 9], ["BAaaACaA", "Ccbcaa", 12], ["BAaaCCb", "bCBC", 11], ["BAab", "c", 8], ["BAab", "ccaba", 6], ["BAabAbBB", "ABaBaaBa", 10], ["BAabCA", "Cb", 10], ["BAabCCA", "CCbBcb", 11], ["BAac", "bBbcaca", 8], ["BAacBb", "cABAca", 9], ["BAacbBbCb", "bBAca", 13], ["BAacbaBC", "ABCbcaCAc", 12], ["BAaccAba", "BAbBbCA", 11], ["BAb", "Aa", 4], ["BAb", "AbaccbCbC", 14], ["BAb", "BaCa", 5], ["BAb", "CCaBA", 8], ["BAb", "CbCb", 5], ["BAb", "caAAC", 8], ["BAb", "ccCAAAABC", 15], ["BAbABaBbC", "CAAcAb", 11], ["BAbACcA", "aBaaCcc", 8], ["BAbAa", "ABbCbabac", 11], ["BAbAa", "c", 10], ["BAbAc", "cBc", 7], ["BAbAcABa", "ac", 13], ["BAbAcbB", "aCAcBB", 6], ["BAbBA", "BcaCBcaBa", 11], ["BAbBAB", "ABBCCb", 8], ["BAbBBaB", "abaA", 9], ["BAbBCc", "aaAcAcAAC", 14], ["BAbBc", "ABcAAABb", 10], ["BAbCCbBC", "bc", 13], ["BAbCbBacB", "acBca", 12], ["BAbCc", "BBbcBAab", 11], ["BAbCcB", "CbacAaaa", 14], ["BAbCcBB", "ccaBBcACC", 14], ["BAba", "Bc", 6], ["BAbaBCAB", "bbCbBa", 11], ["BAbaBbBc", "baAC", 11], ["BAbaCc", "C", 10], ["BAbabca", "cBabaCb", 8], ["BAbacCaa", "CCa", 11], ["BAbb", "baBaBaB", 9], ["BAbbBAA", "ACbcccc", 12], ["BAbc", "B", 6], ["BAbc", "bABCB", 5], ["BAbc", "bcBAb", 6], ["BAbcAcA", "ccA", 8], ["BAbcaBab", "CaACACbcB", 13], ["BAbccAcB", "BA", 12], ["BAbccaBA", "CCCcaa", 10], ["BAc", "AAAccAa", 10], ["BAc", "Bbc", 2], ["BAc", "ba", 4], ["BAcA", "Ac", 4], ["BAcAA", "acbCAA", 7], ["BAcAACc", "accA", 9], ["BAcAAb", "AbC", 10], ["BAcAB", "aAacCcbac", 13], ["BAcACCb", "cccBC", 10], ["BAcACcBA", "BCAbccA", 7], ["BAcAcC", "aAcBb", 8], ["BAcBa", "CCAbBCc", 10], ["BAcBcba", "aaa", 11], ["BAcCBCbaA", "bCb", 13], ["BAcCC", "ACCaC", 5], ["BAcCCBAbA", "b", 16], ["BAcCaB", "cCBBCaa", 10], ["BAcCbC", "CAB", 9], ["BAcCbaC", "CCBBaCabB", 14], ["BAcCbc", "Cb", 8], ["BAcCcAACa", "aacaCaA", 11], ["BAcaBBBc", "a", 14], ["BAcaBc", "CAbaa", 8], ["BAcaC", "cAcaCA", 4], ["BAcaab", "ACbA", 8], ["BAcab", "BaCbca", 7], ["BAcbA", "AbA", 4], ["BAcbA", "bACBCbbCA", 10], ["BAcbAcCB", "ac", 13], ["BAcbBCC", "bBaba", 11], ["BAcbCC", "c", 10], ["BAcbCc", "B", 10], ["BAcbabcB", "aAa", 12], ["BAcbbba", "Cb", 11], ["BAcbcABbA", "baABcBCa", 10], ["BAccAAA", "AaaC", 10], ["BAccAAca", "BBCbbCcab", 11], ["BAccAbCB", "Cba", 13], ["BAccCAb", "cCBcCcac", 11], ["BAccCAbC", "BCabCBA", 11], ["BAccCCcA", "CbbBaCB", 14], ["BAccCcCB", "bcCAB", 9], ["BAcca", "cBcCBBcbC", 13], ["BAccaAaC", "cbBcAaA", 10], ["BAcccCbb", "Bb", 12], ["BB", "A", 4], ["BB", "AACBAacBB", 14], ["BB", "ABaA", 6], ["BB", "ACbbCcc", 12], ["BB", "AbAABC", 9], ["BB", "AbbbBBa", 10], ["BB", "BC", 2], ["BB", "Ba", 2], ["BB", "BbcAaACB", 12], ["BB", "CAcaACbBb", 15], ["BB", "CBabcc", 9], ["BB", "CaCBb", 7], ["BB", "CaaAAaAAc", 18], ["BB", "Cab", 5], ["BB", "Cb", 3], ["BB", "CbAAC", 9], ["BB", "CbcbCcBc", 13], ["BB", "Ccccc", 10], ["BB", "a", 4], ["BB", "aAAbcc", 11], ["BB", "aACBABa", 10], ["BB", "aACbBCaac", 15], ["BB", "aAcCCb", 11], ["BB", "aCACcBC", 12], ["BB", "aCa", 6], ["BB", "aa", 4], ["BB", "bAABCCabC", 15], ["BB", "bABBCb", 8], ["BB", "bBacaAb", 11], ["BB", "bc", 3], ["BB", "cBBcCBaAa", 14], ["BB", "cCaAaBc", 12], ["BB", "cCb", 5], ["BB", "cCc", 6], ["BB", "caBcCA", 10], ["BB", "cac", 6], ["BB", "cbb", 4], ["BB", "ccaC", 8], ["BBA", "BB", 2], ["BBA", "BCCaaAb", 10], ["BBA", "Bb", 3], ["BBA", "Bba", 2], ["BBA", "CBbBab", 7], ["BBA", "aaBbAc", 7], ["BBA", "ab", 5], ["BBA", "bAAaBCbA", 11], ["BBA", "cbcBbC", 9], ["BBAACb", "BBaBbb", 5], ["BBAAb", "abaBbac", 10], ["BBAAbbCc", "a", 15], ["BBAAcAb", "cCBCaBCA", 12], ["BBAAcbba", "aCACbcCc", 13], ["BBABA", "bbccbbb", 11], ["BBABACCCB", "c", 17], ["BBABBAca", "aCCAAcCA", 13], ["BBABBaBA", "CaAaaa", 11], ["BBABBaaBB", "CccbccAc", 16], ["BBABa", "b", 9], ["BBABcC", "aCAA", 10], ["BBABccb", "C", 13], ["BBAC", "bBCAcBaC", 9], ["BBACB", "bAb", 6], ["BBACC", "CBccc", 6], ["BBACC", "aCBBBB", 10], ["BBACaBC", "C", 12], ["BBACcCbBa", "CCCcCC", 12], ["BBAbA", "AbCaCcB", 12], ["BBAbCcacC", "aBaacBcb", 11], ["BBAba", "BCbBcB", 9], ["BBAbcB", "BBc", 6], ["BBAbcBBaA", "bCcAB", 13], ["BBAbcC", "BacA", 7], ["BBAcA", "bBcaAacbb", 11], ["BBAcC", "cC", 6], ["BBB", "BaBbB", 4], ["BBB", "Bbcbc", 6], ["BBB", "CC", 6], ["BBB", "CCCAbc", 11], ["BBB", "CcbcBc", 9], ["BBB", "aaAbb", 8], ["BBBAAA", "AA", 8], ["BBBAAAcB", "BAcAcCCA", 11], ["BBBACc", "AaBCBb", 10], ["BBBAbAaAB", "aAcBABA", 13], ["BBBAccBbC", "ccbCbAb", 14], ["BBBB", "baABcbb", 9], ["BBBBCCacA", "bbCCc", 10], ["BBBBcCA", "bBAabcB", 10], ["BBBBccCCC", "C", 16], ["BBBCABbb", "cAAaAB", 12], ["BBBCab", "abBACbBc", 10], ["BBBa", "CCabbCcaB", 14], ["BBBa", "cAa", 6], ["BBBaCA", "AAbbaaACB", 12], ["BBBaaBb", "cBcaac", 8], ["BBBab", "bAa", 7], ["BBBac", "aaCcCBcAB", 15], ["BBBb", "B", 6], ["BBBbCCb", "BbBaCc", 6], ["BBBba", "BCaC", 8], ["BBBbcBcCA", "BbBB", 11], ["BBBbccc", "A", 14], ["BBBc", "cCAba", 9], ["BBBcAB", "AbCAAbc", 10], ["BBBcBAa", "AbBCCa", 8], ["BBBcBabbB", "aCC", 16], ["BBBcbBA", "AbACCCbCb", 14], ["BBBcc", "CCbbabCb", 12], ["BBBccBaaB", "BcCB", 11], ["BBC", "CaAAAaCbB", 16], ["BBC", "Cbcbcc", 9], ["BBC", "abBBacA", 9], ["BBC", "b", 5], ["BBC", "bCBAA", 7], ["BBC", "bcCaAbb", 11], ["BBC", "cba", 5], ["BBCA", "BAcCC", 6], ["BBCAbbbc", "BAa", 12], ["BBCAcb", "Ca", 9], ["BBCBb", "aac", 9], ["BBCBbc", "CaaBB", 9], ["BBCBcaBb", "ccCcCbBC", 11], ["BBCBcb", "CBcCBcacC", 10], ["BBCC", "C", 6], ["BBCCBC", "aCbbA", 9], ["BBCCBab", "cBCaaC", 8], ["BBCCCbaCB", "AAA", 17], ["BBCCaBbB", "BaBAcCbC", 11], ["BBCCccBCB", "bCaaCA", 13], ["BBCa", "aB", 6], ["BBCaa", "AccACcAAC", 14], ["BBCabCCBB", "BcAcCaCC", 12], ["BBCbB", "BBb", 4], ["BBCbB", "CB", 6], ["BBCbB", "caABAb", 10], ["BBCbc", "BAAbcaaBc", 11], ["BBCc", "BabAb", 7], ["BBCc", "cbabab", 10], ["BBCcbACcB", "Ab", 15], ["BBCccCAba", "aCcCCBAb", 9], ["BBa", "AcBcBAb", 9], ["BBa", "Ccb", 6], ["BBa", "aCCcbCAc", 14], ["BBa", "bBaCBa", 6], ["BBa", "cbAcBBaA", 10], ["BBaA", "bAAaBAAAB", 12], ["BBaAAccbb", "CABB", 14], ["BBaACBaB", "cbcC", 13], ["BBaAab", "BCB", 9], ["BBaAcAAcb", "cbCBBCAcb", 11], ["BBaAcB", "ACbAcb", 7], ["BBaBbcB", "C", 13], ["BBaCB", "cC", 8], ["BBaCCA", "ABAcc", 7], ["BBaCccb", "Cbcbb", 9], ["BBaa", "ba", 5], ["BBaaCCBA", "AbCbC", 12], ["BBaaCca", "aaBBcbAB", 13], ["BBaaa", "B", 8], ["BBaab", "bbAcbB", 7], ["BBaabcA", "BACBAC", 10], ["BBaacbcaB", "Abacab", 10], ["BBaacc", "C", 11], ["BBabBa", "BbBBcAACB", 13], ["BBabcBab", "abB", 10], ["BBabcbb", "BbbCbA", 6], ["BBac", "cBAB", 5], ["BBacAcaAa", "Bba", 13], ["BBacCaBBa", "aa", 14], ["BBacCc", "bcaBA", 9], ["BBacaABc", "<PERSON><PERSON><PERSON>", 10], ["BBacb", "AbbaCBCb", 9], ["BBacb", "abCACCBBA", 14], ["BBb", "BAcaAaca", 14], ["BBb", "BBa", 2], ["BBb", "CAcBCAAc", 14], ["BBb", "aBACcCCC", 14], ["BBb", "acbCc", 8], ["BBbA", "ca", 7], ["BBbAACb", "ACCbb", 10], ["BBbABAaAB", "cAbaCba", 13], ["BBbABbac", "cAB", 12], ["BBbAabbCA", "A", 16], ["BBbAbcB", "AcAAcbABa", 12], ["BBbAcaAB", "cBB", 12], ["BBbAcc", "bBAb", 7], ["BBbB", "ccABABc", 10], ["BBbBB", "a", 10], ["BBbBBbba", "aACbb", 12], ["BBbBBcacc", "AcAa", 15], ["BBbBCbBc", "BbABC", 9], ["BBbBaa", "bbBbbbaAb", 9], ["BBbCCCCbc", "ccaAB", 15], ["BBbCCcbAc", "bABcac", 11], ["BBbCbcb", "Ab", 12], ["BBba", "a", 6], ["BBbaBc", "ac", 8], ["BBbab", "AcAB", 8], ["BBbabb", "CCbAa", 9], ["BBbabbab", "bCBBbaA", 9], ["BBbb", "BA", 6], ["BBbbACc", "A", 12], ["BBbbCCBB", "AaCcBAAA", 15], ["BBbcABBbC", "AaC", 14], ["BBbcB", "ACbA", 8], ["BBbcCac", "cbb", 11], ["BBbccCCBB", "BbaBcb", 12], ["BBc", "ABCbAA", 9], ["BBc", "BbbABAACA", 13], ["BBc", "BcccAbBaC", 13], ["BBc", "aBaCccA", 10], ["BBc", "aC", 5], ["BBc", "aCacBbCCa", 14], ["BBcAAA", "Acbc", 10], ["BBcAAac", "acCcAbc", 9], ["BBcABAA", "BbBaABaC", 7], ["BBcB", "AAacBC", 8], ["BBcB", "aAaabB", 10], ["BBcB", "acb", 5], ["BBcB", "bAcAAbac", 12], ["BBcBACa", "CBACA", 6], ["BBcBCB", "c", 10], ["BBcBbCBA", "cBaaAca", 12], ["BBcBbcA", "bBbca", 6], ["BBcC", "AcAcAcCc", 12], ["BBcCBCac", "ababc", 12], ["BBcCC", "ccB", 7], ["BBcCCabAA", "BCC", 12], ["BBcCCcab", "bCcc", 10], ["BBcCa", "BBAA", 5], ["BBcCcb", "BCb", 6], ["BBca", "b", 7], ["BBcaA", "AaCCAbCCC", 15], ["BBcaA", "b", 9], ["BBcaACbB", "AbCb", 11], ["BBcaC", "CCccCCCC", 12], ["BBcaa", "aBaa", 4], ["BBcaa", "bAAAC", 8], ["BBcaa", "ccCCcABA", 12], ["BBcaaCBB", "BbCcbC", 10], ["BBcab", "aaBc", 8], ["BBcbA", "BcBabbA", 6], ["BBcbaA", "caBbCcaa", 9], ["BBcbbaB", "ccCbbAccB", 10], ["BBcbbabC", "BB", 12], ["BBcc", "AcbAAB", 11], ["BBcc", "CCcBccBbC", 12], ["BBccAb", "aCACbAC", 11], ["BBccB", "BcB", 4], ["BBccc", "BBAbCAcbB", 11], ["BBcccCAAB", "ABABBAca", 14], ["BC", "A", 4], ["BC", "AAcba", 9], ["BC", "AC", 2], ["BC", "ACBACCA", 10], ["BC", "AaA", 6], ["BC", "AaCABABAb", 16], ["BC", "Ac", 3], ["BC", "AcCCcc", 10], ["BC", "B", 2], ["BC", "BAbC", 4], ["BC", "BC", 0], ["BC", "BCCCC", 6], ["BC", "BbAaaBAbb", 16], ["BC", "BbB", 4], ["BC", "BcB", 3], ["BC", "C", 2], ["BC", "CA", 4], ["BC", "CaaaCAcaC", 16], ["BC", "CbCC", 5], ["BC", "CcC", 4], ["BC", "CcCCCab", 12], ["BC", "CcaAbbbcb", 16], ["BC", "CccAb", 9], ["BC", "aAA", 6], ["BC", "aAbbcbac", 14], ["BC", "aCbAaAbBB", 16], ["BC", "abbba", 9], ["BC", "acAACAC", 12], ["BC", "acbCA", 7], ["BC", "acccbAAB", 15], ["BC", "bAABa", 8], ["BC", "bBBAcBbCC", 14], ["BC", "bC", 1], ["BC", "bCaCaCaa", 13], ["BC", "bCbaCa", 9], ["BC", "bCbaaaAAC", 15], ["BC", "babCAbca", 13], ["BC", "babbBBaB", 14], ["BC", "bbCA", 5], ["BC", "c", 3], ["BC", "cAAB", 8], ["BC", "cABCBCAB", 12], ["BC", "cBaACA", 8], ["BC", "cBbaAcB", 11], ["BC", "cCCBb", 8], ["BC", "cb", 4], ["BC", "cbBBB", 8], ["BC", "cba", 5], ["BC", "cbaB", 7], ["BC", "cbaCbBAC", 12], ["BC", "cbbCabA", 11], ["BC", "ccAACb", 10], ["BC", "ccccbb", 11], ["BCA", "A", 4], ["BCA", "AbcCbCaA", 11], ["BCA", "abccbBCC", 12], ["BCAABABCB", "abBbBaB", 11], ["BCAABc", "bBACcBa", 9], ["BCAAbBc", "AaABC", 8], ["BCAAbbbc", "A", 14], ["BCAAcbb", "CAaCAcBbC", 9], ["BCAAccAa", "Aaaa", 10], ["BCABBA", "AbBba", 8], ["BCABBBAa", "bCAbBA", 6], ["BCABa", "b", 9], ["BCABcB", "bcAAbCb", 7], ["BCABcCCc", "CACcbBAc", 10], ["BCABccC", "B", 12], ["BCABccaB", "cAaCbBCc", 13], ["BCAC", "CAcCB", 6], ["BCACBaACB", "aaCaC", 11], ["BCACBaba", "BbAaAbc", 9], ["BCACBb", "aCbCA", 8], ["BCACb", "CAABCa", 8], ["BCACcABBB", "C", 16], ["BCACcCAC", "aBcAaaB", 12], ["BCACcb", "AC", 8], ["BCAa", "A", 6], ["BCAa", "Cc", 6], ["BCAacBCB", "CaCC", 9], ["BCAbBAbAc", "aBcbB", 13], ["BCAbBaCB", "caCba", 11], ["BCAbCB", "ccBcBCC", 10], ["BCAbaCC", "aB", 12], ["BCAbbAcBA", "A", 16], ["BCAbbBA", "BBAAAbac", 10], ["BCAbc", "Cb", 6], ["BCAbcaBCC", "ccBacBbA", 13], ["BCAbccbb", "bAcaCCbAB", 12], ["BCAc", "BaBb", 6], ["BCAcC", "Ccc", 5], ["BCAcCabcc", "CBC", 14], ["BCAcabbBc", "bcbC", 12], ["BCB", "caa", 6], ["BCB", "ccB", 3], ["BCBA", "B", 6], ["BCBA", "bAB", 5], ["BCBAACcc", "aBB", 14], ["BCBACbAbA", "ccABAa", 11], ["BCBACbCb", "cBA", 11], ["BCBAaBcA", "baC", 12], ["BCBAabB", "Bcb", 9], ["BCBAb", "CccbcCcBb", 13], ["BCBAcbbC", "AccaABA", 13], ["BCBB", "A", 8], ["BCBBA", "ACAC", 8], ["BCBBACa", "AbbAA", 9], ["BCBBbCbC", "Ccb", 11], ["BCBBbab", "cCAccAbb", 11], ["BCBBcCa", "BCCCBbA", 9], ["BCBBcaCBA", "CbBCCBacA", 10], ["BCBBccB", "B", 12], ["BCBC", "CbaBa", 7], ["BCBC", "cabAcCaCc", 13], ["BCBCAaB", "CAcb", 9], ["BCBCBaAA", "b", 15], ["BCBCBbbc", "CBBA", 10], ["BCBCbbBC", "CACcaACaB", 14], ["BCBCc", "aBAA", 8], ["BCBaACb", "aCB", 9], ["BCBaAcb", "AaC", 11], ["BCBaC", "BC", 6], ["BCBaCCabc", "aAcbC", 12], ["BCBb", "AaaBabaAa", 14], ["BCBbAABB", "A", 14], ["BCBbBCaA", "B", 14], ["BCBbc", "AbBba", 6], ["BCBbcbBC", "ca", 14], ["BCBcB", "bbC", 7], ["BCBcaACa", "bbCaabC", 10], ["BCBcabB", "aaBaaaAac", 14], ["BCC", "AabbC", 7], ["BCC", "AbbBBa", 10], ["BCC", "CCBcABb", 11], ["BCCA", "bcbbc", 8], ["BCCA", "cBBbacAc", 11], ["BCCAAbbC", "ccCB", 12], ["BCCABc", "c", 10], ["BCCAaba", "CAbB", 8], ["BCCB", "cBca", 7], ["BCCBCC", "ACCBaa", 6], ["BCCBbcAAA", "aaaCCB", 16], ["BCCCAbA", "CabCcbc", 10], ["BCCCCAB", "BC", 10], ["BCCCCBAB", "cABCcc", 13], ["BCCCb", "cCCAc", 6], ["BCCaAcbB", "Cabccaa", 12], ["BCCaB", "cAaAcAa", 12], ["BCCaBABCc", "a", 16], ["BCCaCBCAA", "cBbCaccaA", 9], ["BCCac", "CbC", 7], ["BCCbAc", "AcCAaBcCc", 12], ["BCCbCbaB", "caa", 13], ["BCCbaBBbC", "baBAB", 11], ["BCCbb", "aAAcc", 10], ["BCCbbb", "BCaB", 7], ["BCCbc", "BBaAbbBCC", 13], ["BCCbc", "caBaAbbC", 11], ["BCCcaAccC", "bacbC", 11], ["BCCcbAcBa", "CA", 14], ["BCCcbaBc", "CcCcCAba", 9], ["BCCccbaC", "C", 14], ["BCa", "CBbbAac", 10], ["BCa", "aAbCCb", 9], ["BCa", "bbcAB", 7], ["BCa", "c", 5], ["BCaABBaAB", "acCcBAC", 13], ["BCaAa", "AC", 8], ["BCaAaCac", "aaAB", 11], ["BCaAba", "a", 10], ["BCaAcAbC", "B", 14], ["BCaBAC", "bCAcCAaB", 10], ["BCaBcCB", "BaAA", 10], ["BCaCAc", "CBAaBc", 8], ["BCaCCCcc", "ccbaBCaC", 12], ["BCaCbbbc", "aaAAa", 14], ["BCaa", "bc", 6], ["BCaaAbaB", "ccCA", 13], ["BCaaab", "cAbBBa", 11], ["BCaaabC", "Bbbcb", 10], ["BCaabcbA", "BabcCAb", 8], ["BCabaB", "BaBAbcB", 7], ["BCabacC", "a", 12], ["BCabbbABB", "BBCcABb", 10], ["BCabbcCCC", "ABcCAcbc", 13], ["BCabcaAAA", "Baa", 12], ["BCacBBAAB", "aBC", 14], ["BCacBBbB", "CBCB", 10], ["BCacCB", "bB", 9], ["BCacCba", "BaBbaAAba", 10], ["BCb", "BBa", 4], ["BCb", "bBCCa", 6], ["BCb", "bCAAAAC", 11], ["BCbA", "Bccc", 5], ["BCbAAC", "aA", 9], ["BCbAcbcCB", "cbBcba", 11], ["BCbBCacaC", "AACB", 16], ["BCbBa", "cbbBC", 6], ["BCbBaa", "aCAAbACc", 12], ["BCbBca", "bBaB", 8], ["BCbCA", "cB", 8], ["BCbCccA", "aaABcCAB", 11], ["BCbaAABaa", "AA", 14], ["BCbaBAb", "cAAAcbbaA", 15], ["BCbaCbBAc", "CBCcabcAb", 10], ["BCbaaCCaa", "cCbac", 11], ["BCbaabbBC", "abcCBbB", 11], ["BCbaccCC", "bbBc", 11], ["BCbbc", "AcCbBaACc", 11], ["BCbbcbbB", "AbbCca", 11], ["BCbc", "cbcCCcBAA", 13], ["BCbcBbaC", "babCBCACb", 9], ["BCbcaACBB", "aab", 14], ["BCbccccB", "Ca", 14], ["BCc", "AAaCBcAbc", 13], ["BCc", "AcBbBbcba", 14], ["BCc", "CAcB", 6], ["BCc", "bABBC", 7], ["BCcA", "Aac", 6], ["BCcA", "bbbCc", 7], ["BCcACaB", "CBcbcaBAA", 11], ["BCcACaCca", "cca", 12], ["BCcAcb", "bbcacbb", 6], ["BCcBBAccb", "cBB", 12], ["BCcBBacB", "B", 14], ["BCcBBc", "ca", 10], ["BCcBac", "Cb", 9], ["BCcBbBAb", "BBcBBcA", 7], ["BCcBbBCc", "A", 16], ["BCcBc", "a", 10], ["BCcCB", "CABB", 6], ["BCcCC", "baB", 9], ["BCcCcaaB", "CAaa", 10], ["BCca", "aBcCcA", 5], ["BCcaAc", "aaAAA", 9], ["BCcaBbAb", "A", 14], ["BCcab", "CA", 7], ["BCcacCbcc", "CBcabaBCC", 11], ["BCcb", "CCbBACCcc", 12], ["BCcb", "Ca", 6], ["BCcb", "bC", 5], ["BCcbAc", "B", 10], ["BCcbAcBAA", "AacbBc", 12], ["BCcbBac", "Aaa", 12], ["BCcbaC", "cB", 9], ["BCcbc", "BBa", 7], ["BCccAac", "ABB", 12], ["BCccBb", "Cc", 8], ["BCccCC", "BBA", 10], ["Ba", "AAACAbbb", 15], ["Ba", "AAaBBbBaa", 14], ["Ba", "ABBaAabBa", 14], ["Ba", "AaccaB", 10], ["Ba", "AcABCBc", 12], ["Ba", "AcB", 6], ["Ba", "AcaAcb", 10], ["Ba", "B", 2], ["Ba", "BAcBc", 7], ["Ba", "Ba", 0], ["Ba", "BcAACc", 9], ["Ba", "BcCa", 4], ["Ba", "BcbBb", 8], ["Ba", "C", 4], ["Ba", "CABbaBcb", 12], ["Ba", "CBBAAB", 9], ["Ba", "CC", 4], ["Ba", "CCCcABBC", 14], ["Ba", "CbCbCa", 9], ["Ba", "Cc", 4], ["Ba", "a", 2], ["Ba", "aA", 3], ["Ba", "aB", 4], ["Ba", "aCB", 6], ["Ba", "aCbCa", 7], ["Ba", "acBbb", 8], ["Ba", "b", 3], ["Ba", "bcBCAbcCB", 15], ["Ba", "c", 4], ["Ba", "cAaAABbCB", 16], ["Ba", "cBBa", 4], ["Ba", "cbBaCAb", 10], ["Ba", "ccBcACA", 11], ["Ba", "ccC", 6], ["BaA", "CACaA", 6], ["BaA", "CBAbbaca", 11], ["BaA", "aaBB", 6], ["BaA", "bA", 3], ["BaA", "baACAbcA", 11], ["BaA", "cbc", 6], ["BaA", "ccBAAB", 7], ["BaAAA", "BB", 8], ["BaAAa", "ca", 8], ["BaAAaCBaA", "ACCC", 14], ["BaAAcCA", "abCCBA", 9], ["BaAAcc", "aaAAbac", 6], ["BaAAccaCA", "CcABcBCc", 12], ["BaAB", "BBAB", 2], ["BaAB", "ac", 6], ["BaAB", "bBB", 5], ["BaABAACBA", "bAb", 14], ["BaABCaACb", "bCbabAaBb", 13], ["BaABa", "abC", 7], ["BaABcbab", "cAaca", 10], ["BaACAcCc", "BC", 12], ["BaACB", "bcAa", 7], ["BaACBAbAC", "Bac", 13], ["BaACaCbC", "AACACCC", 6], ["BaAa", "BBCC", 6], ["BaAa", "BcbccbCC", 14], ["BaAa", "aAbB", 6], ["BaAaAaAB", "Ccab", 13], ["BaAaaAAb", "ABBCBAcB", 13], ["BaAacA", "bACCCb", 9], ["BaAb", "AbCBcCba", 12], ["BaAbCBa", "CCCcAb", 13], ["BaAbab", "aAbaBCc", 7], ["BaAbcCCB", "CBcA", 13], ["BaAcAB", "BabCCCaC", 10], ["BaAcAa", "abaCCCBAA", 11], ["BaAcAbbC", "ABaBBAC", 10], ["BaAcB", "CBAACbCa", 9], ["BaAcCbb", "Abb", 8], ["BaAcCbca", "BcCbCAAcB", 12], ["BaAcbcb", "c", 12], ["BaB", "CACAb", 8], ["BaB", "aCACaB", 8], ["BaB", "bc", 5], ["BaB", "cBBB", 4], ["BaB", "cCacaaB", 10], ["BaBA", "cABACbbA", 10], ["BaBAA", "bbAacbA", 9], ["BaBAB", "acAaABBA", 11], ["BaBABabC", "C", 14], ["BaBACA", "ACAcbbccb", 15], ["BaBAc", "BaBABaaC", 7], ["BaBB", "aBBCaA", 8], ["BaBBaABA", "AaabaABb", 7], ["BaBBb", "BC", 8], ["BaBBc", "C", 9], ["BaBBcAaAc", "C", 17], ["BaBBcbcb", "bbabb", 10], ["BaBCAbab", "A", 14], ["BaBCBC", "BA", 9], ["BaBCaCac", "cACc", 10], ["BaBCc", "cBcCbAab", 13], ["BaBa", "bBBABAabb", 11], ["BaBa", "bCAbaAbCb", 13], ["BaBaBB", "C", 12], ["BaBb", "CC", 8], ["BaBb", "bbAbCcbBc", 13], ["BaBbABA", "ababcCAc", 11], ["BaBbBb", "cCAAb", 10], ["BaBbC", "BBACac", 8], ["BaBbabbCb", "b", 16], ["BaBc", "CB", 6], ["BaBcACac", "AbCaBA", 11], ["BaBcB", "AaabaaBAa", 13], ["BaBcBbbAa", "BCACcab", 13], ["BaBcCA", "CbCACA", 8], ["BaBcCA", "bCa", 8], ["BaBcaCCbB", "BCC", 12], ["BaBcacC", "ACB", 12], ["BaC", "ABAbc", 6], ["BaC", "ABbb", 6], ["BaC", "ACa", 5], ["BaC", "A<PERSON>", 6], ["BaC", "BACccaaBA", 13], ["BaC", "BC", 2], ["BaC", "Bccb", 5], ["BaC", "a", 4], ["BaC", "bCbbBcB", 12], ["BaC", "baBcbC", 7], ["BaC", "cBAabACAa", 12], ["BaC", "cBaBAaCc", 10], ["BaCAA", "cbbcBBbb", 14], ["BaCAAbbB", "BAcB", 10], ["BaCAB", "cC", 8], ["BaCABCB", "bB", 11], ["BaCABccaB", "CAacBCCA", 12], ["BaCAaAc", "bCa", 9], ["BaCB", "B", 6], ["BaCBa", "BbAbaBC", 9], ["BaCBaCcCC", "aCCAc", 11], ["BaCBbCB", "acB", 9], ["BaCBc", "ABCB", 6], ["BaCBcBC", "cc", 11], ["BaCCAcAb", "Bab", 10], ["BaCCC", "CcBCabAAa", 14], ["BaCCCAcb", "bbcAaA", 12], ["BaCCCbb", "bCCaaCb", 9], ["BaCCbAA", "acCBcBB", 10], ["BaCCc", "Ca", 8], ["BaCCcCcc", "baAbcaCc", 8], ["BaCa", "bbAba", 6], ["BaCaAAaca", "CCbCb", 15], ["BaCaCCABa", "bCaC", 11], ["BaCb", "bbaA", 7], ["BaCb", "cABC", 7], ["BaCbAB", "BAcbaAC", 6], ["BaCbBA", "bcAacBaa", 10], ["BaCbBCaa", "acBCAAcA", 11], ["BaCbBcbB", "bc", 12], ["BaCbbbBaa", "CcCAcCCC", 16], ["BaCc", "Bc", 4], ["BaCc", "CCBcbAAA", 13], ["BaCcBbAb", "BcaAaBa", 11], ["BaCcCcab", "abBAcBA", 12], ["BaCcCcbAc", "BacCAaaaA", 11], ["BaCcaAabB", "BBc", 14], ["BaCcaBAa", "AaCb", 11], ["BaCccaaaA", "cBacBCA", 12], ["Baa", "A", 5], ["Baa", "AcccabBa", 12], ["Baa", "BaaBCABcB", 12], ["Baa", "CAAa", 5], ["Baa", "CBBcBcC", 12], ["Baa", "CCABBacBb", 14], ["Baa", "CbABbAcbb", 15], ["Baa", "CbCaAbc", 10], ["Baa", "bBaBbabAc", 12], ["Baa", "bBccbbaAC", 13], ["Baa", "bcAbaAACA", 14], ["BaaA", "CaBBc", 8], ["BaaA", "baBabC", 7], ["BaaABa", "bCbcaa", 9], ["BaaACcAAc", "bccAaa", 11], ["BaaAaCc", "cB", 13], ["BaaB", "C", 8], ["BaaB", "cAa", 5], ["BaaBAbAb", "baccCC", 13], ["BaaBBcA", "ccC", 12], ["BaaC", "bCb", 7], ["BaaC", "cacAcCBcC", 13], ["BaaCACAaB", "cAaCabab", 9], ["BaaCBCBBc", "ac", 14], ["BaaCbAC", "AbCAbca", 11], ["BaaCcCa", "cBACb", 11], ["BaaaBcCab", "BaBbbbAaC", 11], ["BaaaC", "accb", 8], ["<PERSON><PERSON>a", "B", 8], ["<PERSON><PERSON>a", "CaA", 7], ["BaaababB", "bCAcBCB", 11], ["BaaacbA", "aBCba", 8], ["<PERSON>ab", "cCAAcBa", 11], ["<PERSON>ab", "ccaAACcAC", 15], ["BaabA", "b", 8], ["BaabAA", "A", 10], ["BaabBAcC", "aaBCaab", 10], ["BaabBcc", "cCCAaCbc", 12], ["BaabCBCC", "CcCaB", 14], ["BaabCa", "cAaAB", 9], ["BaabCb", "CA", 10], ["Baabacaa", "bAcbccB", 10], ["<PERSON><PERSON><PERSON>", "bBbAbbA", 7], ["BaabbAaA", "BBA", 11], ["BaabcAb", "aBaC", 10], ["<PERSON><PERSON>", "AaCacBCA", 10], ["<PERSON><PERSON>", "CcBaBAb", 9], ["<PERSON><PERSON>", "a", 6], ["<PERSON><PERSON>", "aaAB", 5], ["<PERSON><PERSON>", "cCcBC", 9], ["<PERSON><PERSON>", "ccCb", 8], ["BaacCABbA", "cA", 14], ["Bab", "BaCABaCa", 11], ["Bab", "C", 6], ["Bab", "a", 4], ["Bab", "acabAacac", 14], ["Bab", "bbCaccBaB", 13], ["BabA", "a", 6], ["BabA", "cbAc", 6], ["BabAAccaB", "abb", 13], ["BabACA", "b", 10], ["BabAaC", "baCCBcC", 9], ["BabAb", "Aa", 8], ["BabAbACA", "CBAaCaaa", 12], ["BabAbCA", "CccAab", 12], ["BabBBAaCa", "AcCAccAB", 15], ["BabBa", "ABbACcCcb", 15], ["BabBcA", "ACC", 10], ["BabBcBcA", "acbCABcB", 10], ["BabC", "aABb", 6], ["BabCaBca", "AA", 14], ["Baba", "BaBCbCBB", 10], ["BabaBaAA", "AaB", 11], ["BabacAbAb", "cCaA", 14], ["<PERSON>bb", "CcAa", 8], ["<PERSON>bb", "aaACcbc", 10], ["BabbA", "CaCb", 6], ["BabbAaaBA", "BbCcACC", 13], ["BabbAccb", "cacBB", 11], ["BabbBcBA", "cAabCbba", 11], ["BabbCBb", "aCCcBcB", 10], ["BabbCCbCA", "AbAcBa", 12], ["BabbcBAAa", "aCacA", 12], ["BabcBAbcc", "CabA", 12], ["BabcCbCA", "cbC", 10], ["BabcaC", "ABB", 10], ["Babcac", "bcccAbBC", 11], ["Bac", "BBcaBcC", 8], ["Bac", "CbcAbAbB", 14], ["Bac", "bacBAccb", 11], ["Bac", "bbACab", 9], ["BacAa", "bBCb", 8], ["BacAaaCaB", "ccaaAb", 10], ["BacAb", "aBBaBa", 9], ["BacAbA", "caAaBbCAb", 11], ["BacBBa", "AbBBAAA", 10], ["BacBCAB", "cCBaBCc", 10], ["BacBab", "CbBABcB", 10], ["BacCAaBAB", "ccAb", 12], ["BacCBBCc", "B", 14], ["BacCCaA", "bcaBA", 9], ["BacCCcbB", "cCbaaCBB", 12], ["BacCa", "cCBBB", 10], ["BacCab", "bA", 10], ["BacCabcAc", "CCbBC", 12], ["BacCccBa", "ABCBA", 10], ["BacaB", "bbac", 7], ["BacaC", "AAC", 6], ["BacaCaBB", "bB", 13], ["BacaCbA", "caBCAa", 9], ["BacbB", "CcaACc", 10], ["BacbC", "AAabcbbCB", 10], ["BacbCbBca", "cCcCC", 13], ["BacbbCaAa", "aCACaC", 11], ["Bacc", "b", 7], ["BaccACa", "B", 12], ["BaccAaacb", "A", 16], ["BaccBcCBB", "acb", 13], ["BaccCB", "BaC", 6], ["Bacca", "Ac", 7], ["BaccaCC", "CcbA", 11], ["Baccb", "aCc", 5], ["Baccba", "cbaaBcbc", 9], ["Bb", "AAABBB", 9], ["Bb", "AAccaCAaa", 18], ["Bb", "AaA", 6], ["Bb", "AaAAc", 10], ["Bb", "B", 2], ["Bb", "BACBbABA", 12], ["Bb", "BBaBb", 6], ["Bb", "BacCBA", 9], ["Bb", "BbCBCABAC", 14], ["Bb", "Bba", 2], ["Bb", "BcBAACA", 11], ["Bb", "CC", 4], ["Bb", "CcaaBa", 10], ["Bb", "a", 4], ["Bb", "aAbbb", 7], ["Bb", "aBa", 4], ["Bb", "aBbcCbAAC", 14], ["Bb", "aCCAB", 9], ["Bb", "aCccacCB", 15], ["Bb", "aacA", 8], ["Bb", "abBaBbAa", 12], ["Bb", "b", 2], ["Bb", "bAcCbc", 9], ["Bb", "bBaCBc", 9], ["Bb", "bBbBCccc", 12], ["Bb", "bCCc", 7], ["Bb", "bCaBcC", 10], ["Bb", "bCcbbc", 9], ["Bb", "baB", 4], ["Bb", "bc", 3], ["Bb", "bcCaacCC", 15], ["Bb", "bcaBccAcb", 14], ["Bb", "cab", 4], ["Bb", "cbAACCBb", 12], ["Bb", "cbabCcbb", 13], ["Bb", "cc", 4], ["BbA", "BCaAcABab", 14], ["BbA", "C", 6], ["BbA", "aacCbAc", 10], ["BbA", "cbCcbaAAA", 13], ["BbAAaA", "AbbBccC", 11], ["BbAAaA", "bBc", 10], ["BbABB", "BBAACAa", 9], ["BbABBbAa", "acaCCbbcB", 14], ["BbABC", "c", 9], ["BbABa", "accBbBcBb", 12], ["BbABaacaA", "b", 16], ["BbAC", "CaCCBcAa", 12], ["BbAC", "caAcaab", 11], ["BbACAbcB", "aaaAB", 11], ["BbAa", "ACcA", 7], ["BbAa", "cCA", 6], ["BbAaAaaa", "cBbbcCB", 14], ["BbAaCab", "cA", 12], ["BbAaa", "Bbbcacaa", 7], ["BbAac", "b", 8], ["BbAb", "CccCbabaC", 13], ["BbAbC", "CABA", 7], ["BbAbC", "ccA", 8], ["BbAbCab", "bcabABC", 10], ["BbAbCcBab", "CAaAbcbAC", 12], ["BbAbCcaAC", "ABbcCa", 11], ["BbAbbcbb", "B", 14], ["BbAbc", "aABB", 7], ["BbAcaaaa", "bBccaac", 8], ["BbAcbca", "acCCcbBCB", 13], ["BbB", "ACbcc", 8], ["BbB", "Acaca", 10], ["BbB", "bBcb", 5], ["BbBA", "CA", 6], ["BbBABCcA", "aaC", 13], ["BbBAaBa", "b", 12], ["BbBAb", "aaCCcb", 10], ["BbBAbABAc", "cBbbBCcB", 12], ["BbBBCbca", "cbBcCbC", 7], ["BbBBa", "cC", 10], ["BbBBaBBba", "bAC", 15], ["BbBBabAb", "cbB", 12], ["BbBC", "cbbbcaA", 9], ["BbBCAbb", "baCCbBbb", 9], ["BbBCbAb", "BbCcBbbac", 9], ["BbBa", "aCaaaA", 10], ["BbBa", "caCcb", 10], ["BbBaC", "BA", 7], ["BbBaCbCcB", "baAbCbaCc", 11], ["BbBaaCBbc", "bcAAAbC", 11], ["BbBacBCca", "c", 16], ["BbBbA", "BbaaA", 4], ["BbBbBAb", "CbcCbAaCc", 13], ["BbBbBa", "A", 11], ["BbBbBbC", "cBbacbBa", 10], ["BbBbbbBAb", "AabCcBa", 13], ["BbBcBc", "bcaaC", 9], ["BbBcaAC", "cB", 12], ["BbBcabAa", "BaBAaACc", 10], ["BbBcbbC", "bCBAabbaa", 11], ["BbC", "A", 6], ["BbC", "Cc", 5], ["BbC", "aAAAc", 9], ["BbC", "aAAbca", 9], ["BbC", "bAaBabC", 8], ["BbC", "bBCcCa", 8], ["BbC", "baCACBbc", 11], ["BbC", "bcBccBcBa", 14], ["BbCAACcac", "AcCbB", 14], ["BbCAACcb", "b", 14], ["BbCAaCaC", "BBc", 12], ["BbCAbc", "aaa", 11], ["BbCAcCaCC", "CCcB", 13], ["BbCAcaab", "BCbACaABc", 9], ["BbCBCB", "bAAccbCc", 11], ["BbCBCbAc", "CcACA", 11], ["BbCCAAab", "ccCCC", 12], ["BbCCABABa", "Acaab", 14], ["BbCCBBb", "aacCb", 9], ["BbCCCBCC", "BCA", 12], ["BbCCa", "aaaCAc", 9], ["BbCCcCcCc", "AaAAA", 18], ["BbCCcbc", "BaaBCc", 9], ["BbCa", "aCC", 6], ["BbCaB", "A", 9], ["BbCaBAA", "AbAABc", 9], ["BbCaCbACB", "AAA", 15], ["BbCaCcC", "aAAcA", 11], ["BbCaCcaC", "A", 15], ["BbCbC", "bAACAAcAC", 13], ["BbCbCCb", "AbBAA", 11], ["BbCbaAB", "C", 12], ["BbCbaB", "aAcC", 11], ["BbCbaBB", "<PERSON><PERSON><PERSON>", 8], ["BbCbbBc", "b", 12], ["BbCbbb", "cCcAcBAcA", 16], ["BbCbcc", "AAACbaaAa", 14], ["BbCc", "A", 8], ["BbCc", "aac", 6], ["BbCcCACB", "cB", 12], ["BbCcaBBC", "CB", 12], ["BbCccBCB", "cCcbBb", 9], ["Bba", "ABBBcACb", 12], ["Bba", "AbAcAbCcA", 14], ["Bba", "CaBCAaaaB", 14], ["Bba", "CacBBCBA", 12], ["Bba", "aBAcbCC", 10], ["Bba", "aCCcAaAc", 14], ["Bba", "abcA", 5], ["Bba", "bcCa", 5], ["BbaAB", "BbB", 4], ["BbaABacc", "bccbc", 11], ["BbaAbBCAA", "caAcCbc", 12], ["BbaAcAb", "bacACCbC", 9], ["BbaB", "aBbAaCb", 7], ["BbaB", "bBbBAbBC", 9], ["BbaBCB", "bcA", 9], ["BbaBcbacc", "CCBAC", 14], ["BbaC", "C", 6], ["BbaCBC", "C", 10], ["BbaCCCb", "Cca", 11], ["BbaCa", "AcCC", 8], ["BbaCa", "ccbA", 9], ["BbaCcBba", "ACb", 11], ["BbaCcC", "cbCCAAbcc", 11], ["Bbaa", "abbaAA", 6], ["BbaaAbAA", "Acc", 14], ["BbaaBAb", "CBbaCbaBB", 9], ["BbaacA", "ccAbCabAa", 12], ["BbaacBcbC", "cabbCaC", 12], ["Bbab", "BCBb", 4], ["BbabA", "Cbc", 8], ["BbabBc", "AcA", 11], ["BbabC", "ab", 6], ["BbabCcBA", "AabACBc", 9], ["Bbaba", "AAa", 7], ["BbabbBb", "b", 12], ["Bbac", "bbb", 5], ["BbacB", "CBcBB", 7], ["BbacCc", "Cb", 10], ["Bbacaa", "AaAaac", 8], ["Bbb", "ACCCCc", 12], ["Bbb", "BCABAa", 9], ["Bbb", "BabbbBcCA", 12], ["Bbb", "acaCCaa", 14], ["Bbb", "bAAcBa", 10], ["Bbb", "ccacA", 10], ["BbbA", "ACccA", 8], ["BbbAAB", "baBAb", 7], ["BbbABccc", "AbaABca", 8], ["BbbAC", "bcAaBBBca", 14], ["BbbAaAba", "a", 14], ["BbbB", "Bcc", 6], ["BbbBCCA", "cBBb", 11], ["BbbBCba", "BA", 11], ["BbbBcaa", "b", 12], ["BbbCAaA", "cbbBaBaCA", 9], ["BbbCAb", "AACC", 10], ["BbbCAc", "cbaB", 9], ["BbbCBcac", "bAbaBCba", 10], ["BbbCCB", "C", 10], ["BbbCCBAc", "cbbBcCCAC", 8], ["Bbba", "AAaAB", 9], ["Bbba", "abAc", 6], ["BbbaAC", "aBABcAAaB", 12], ["BbbaC", "AbBaaAaA", 11], ["BbbaabCCA", "CbcAb", 13], ["BbbacCbb", "AaccA", 11], ["BbbbCAaA", "cACCaCcAC", 15], ["BbbbbAc", "AbbcCb", 10], ["BbbbcA", "AaCcBabb", 14], ["BbbbcbbA", "bAA", 12], ["Bbbc", "CbccaAc", 10], ["BbbcABb", "bbbcc", 7], ["BbbcB", "bbacbbc", 8], ["BbbcbAcbB", "aCaCbaAc", 13], ["Bbbcbcab", "caCaAAbc", 14], ["BbcA", "CBaA", 5], ["BbcA", "CBcAB", 5], ["BbcABcB", "AcA", 10], ["BbcACB", "BaaCCBBC", 10], ["BbcACCcB", "bcbbaaBba", 14], ["BbcAbCa", "CbbaCba", 9], ["BbcAcaB", "Aca", 8], ["BbcAccCab", "abCCaA", 11], ["BbcB", "aACCccCaA", 16], ["BbcBbB", "baacAca", 11], ["BbcBbbaC", "Ab", 14], ["BbcC", "AccAcaBaC", 14], ["BbcCB", "bBca", 6], ["BbcCBaB", "AcCcaCbb", 11], ["BbcCaab", "cACaC", 10], ["BbcaBbCCb", "acBBcc", 11], ["BbcaCcaa", "AbcccaBB", 9], ["BbcaaACb", "A", 14], ["BbcbACAa", "a", 14], ["BbcbBCCA", "AcaCB", 12], ["BbcbCcCCc", "a", 18], ["Bbcbaa", "a", 10], ["BbcbbbBab", "aaBcBcCB", 14], ["BbcbccB", "bAACb", 10], ["Bbcc", "caAACBBb", 15], ["BbccBcb", "BCAbccb", 8], ["BbccC", "CaaAC", 8], ["Bbcca", "bBcb", 6], ["BbccccCC", "aAbcc", 12], ["Bc", "A", 4], ["Bc", "AB", 4], ["Bc", "ABAccAcC", 12], ["Bc", "ABBbBBbb", 14], ["Bc", "AaA", 6], ["Bc", "AaaBaCcCc", 14], ["Bc", "AabBBC", 9], ["Bc", "Aac", 4], ["Bc", "Aba", 5], ["Bc", "Abccbabbc", 15], ["Bc", "AcAbBC", 9], ["Bc", "B", 2], ["Bc", "BACaACBC", 13], ["Bc", "BBcBCbCB", 12], ["Bc", "BcBAAAaA", 12], ["Bc", "BcaaABCcc", 14], ["Bc", "C", 3], ["Bc", "CBCccCbaA", 14], ["Bc", "CCaBABaBA", 16], ["Bc", "CaBCBbBbc", 14], ["Bc", "CbaBCbb", 11], ["Bc", "a", 4], ["Bc", "aC", 3], ["Bc", "abAbBBcc", 12], ["Bc", "abaCAABA", 14], ["Bc", "abaabAbc", 13], ["Bc", "abcbBBB", 11], ["Bc", "ac", 2], ["Bc", "acCCcb", 10], ["Bc", "acCacaaAa", 16], ["Bc", "ba", 3], ["Bc", "cC", 3], ["Bc", "cb", 4], ["Bc", "cbACCabca", 15], ["BcA", "AC", 5], ["BcA", "BAaB", 5], ["BcA", "BBc", 4], ["BcA", "CBababB", 11], ["BcA", "CcB", 4], ["BcA", "CcCb", 6], ["BcA", "a", 5], ["BcA", "bAcab", 6], ["BcA", "bBAbb", 7], ["BcA", "bbbc", 7], ["BcA", "c", 4], ["BcAABaB", "bACcAc", 10], ["BcAAaA", "BcCAbaBb", 8], ["BcAB", "aBCCcACc", 10], ["BcABAc", "BaAa", 7], ["BcABaAaA", "aCCaCcb", 13], ["BcABaa", "CaaBBaa", 7], ["BcAC", "BCAbcbCAa", 11], ["BcACAA", "aCb", 9], ["BcACB", "bCcC", 6], ["BcACBaBCa", "BCcBAbACc", 10], ["BcACC", "aa", 9], ["BcAa", "Cb", 7], ["BcAaA", "CBBc", 9], ["BcAaA", "abBbBBcB", 14], ["BcAaACA", "cbCABAaCb", 10], ["BcAaCCBB", "cBbCaBbA", 11], ["BcAaCbB", "ACB", 8], ["BcAaaaC", "cC", 10], ["BcAb", "BB", 5], ["BcAb", "CC", 7], ["BcAbAbCac", "bab", 13], ["BcAbB", "B", 8], ["BcAbabBA", "acb", 12], ["BcAbabBBB", "BA", 14], ["BcAbabC", "BBbaCAB", 10], ["BcAbbcC", "AaAcbC", 8], ["BcAbc", "BAC", 5], ["BcAbc", "BbbcbBabB", 11], ["BcAbcAAC", "A", 14], ["BcAc", "bC", 6], ["BcAcAaACb", "aAbBBB", 14], ["BcAcB", "caaAbbccB", 12], ["BcAcaccb", "cbAbAb", 11], ["BcAcba", "aA", 10], ["BcAcc", "BCaCBcAcC", 9], ["BcB", "AcCCAC", 10], ["BcB", "BBAcaAaB", 10], ["BcB", "BabAcacCA", 14], ["BcB", "aaBcAb", 7], ["BcB", "abcC", 5], ["BcB", "bcBBCbab", 11], ["BcB", "cAAc", 8], ["BcB", "ca", 4], ["BcB", "caBaaAAb", 13], ["BcBA", "CAabC", 9], ["BcBA", "baaC", 7], ["BcBAAc", "cBBaaabBB", 14], ["BcBAB", "aCaAaBb", 9], ["BcBABCB", "aaB", 11], ["BcBAccAc", "c", 14], ["BcBBBA", "ACc", 11], ["BcBBC", "cCABb", 7], ["BcBBbb", "bbBAa", 8], ["BcBBbcAaA", "CA", 15], ["BcBC", "c", 6], ["BcBCA", "cAaCC", 8], ["BcBCAAAcc", "abc", 15], ["BcBCBBC", "BbC", 9], ["BcBCbAc", "cBBCaC", 8], ["BcBCcbccc", "BcBC", 10], ["BcBCccAa", "acb", 13], ["BcBa", "aCB", 5], ["BcBaAcAAc", "bccBBBcBc", 11], ["BcBaC", "acBbcABAa", 11], ["BcBab", "CbCACC", 10], ["BcBb", "acAABAA", 10], ["BcBbA", "Ccba", 5], ["BcBbAbABB", "a", 17], ["BcBbbCAa", "baACcBCcC", 14], ["BcBbbCa", "AcCBAa", 9], ["BcBbc", "CcABb", 6], ["BcBc", "cBBcc", 6], ["BcBcAbbc", "babC", 11], ["BcBcBc", "cAAc", 8], ["BcC", "ABabcbC", 8], ["BcC", "Ac", 4], ["BcC", "C", 4], ["BcC", "acbcaAaa", 13], ["BcC", "bAbB", 7], ["BcCA", "ccaBaaAac", 14], ["BcCAABBB", "BCabBB", 6], ["BcCAaAC", "acCaB", 8], ["BcCAaBc", "Bcab", 7], ["BcCAbB", "ACaccCB", 10], ["BcCAbaACc", "aCAAcCBC", 12], ["BcCAbcbA", "bccAbCC", 7], ["BcCAcA", "BbB", 10], ["BcCBABA", "accb", 10], ["BcCBAaC", "b", 13], ["BcCBbabcc", "cCb", 12], ["BcCBc", "ba", 9], ["BcCBcaAcc", "B", 16], ["BcCBcaCbC", "CA", 15], ["BcCCAA", "caaA", 7], ["BcCCBaaa", "AaaCBb", 12], ["BcCCCAA", "b", 13], ["BcCCaaaA", "BccaAabBa", 9], ["BcCCbCac", "AcAa", 12], ["BcCa", "AB", 8], ["BcCa", "cB", 6], ["BcCaACC", "cbACaA", 9], ["BcCaACCc", "aaaCb", 11], ["BcCaBbCAC", "AbaAc", 12], ["BcCaBc", "CCAAB", 8], ["BcCaCacc", "CaCbbbc", 10], ["BcCaaC", "AcC", 8], ["BcCb", "aBacB", 6], ["BcCbAcCb", "CAA", 12], ["BcCbCCCa", "CBCC", 9], ["BcCbaBabc", "abcbB", 13], ["BcCbbb", "ccBCb", 6], ["BcCc", "bBb", 7], ["BcCcBBb", "aCCC", 10], ["BcCcBcAb", "aacbBbC", 13], ["BcCca", "CCcCaaB", 8], ["Bca", "AcbCBab", 10], ["Bca", "Bb", 4], ["Bca", "Bcc", 2], ["Bca", "CAAbA", 9], ["Bca", "CcBc", 6], ["Bca", "aAAccCBB", 14], ["Bca", "aCbbBBcCb", 14], ["Bca", "ac", 4], ["Bca", "c", 4], ["BcaAAbACa", "cAaacBB", 13], ["BcaAaC", "B", 10], ["BcaB", "A", 7], ["BcaB", "ABC", 7], ["BcaB", "AcAacA", 8], ["BcaB", "CAba", 7], ["BcaB", "cc", 6], ["BcaBACC", "aabAcbb", 10], ["BcaBACbA", "cB", 12], ["BcaBAc", "CAcBaCC", 10], ["BcaBb", "bABA", 6], ["BcaBcab", "aAaA", 10], ["BcaC", "abbAb", 8], ["BcaC", "ba", 5], ["BcaC", "ccaCBCB", 8], ["BcaCBAbA", "BA", 12], ["BcaCC", "CAbBbB", 12], ["BcaCCbb", "bbb", 9], ["BcaCc", "AaCABAACA", 13], ["Bcaa", "C", 7], ["Bcaa", "CbBABaa", 8], ["Bcaa", "cbbBcc", 10], ["BcaaAc", "AcaCCb", 8], ["BcaaBBa", "bBBCAAb", 12], ["BcaaBcB", "cCBbbAAcA", 13], ["Bcaaba", "ACbcA", 10], ["Bcaabc", "Bba", 8], ["BcaacbcC", "CBBBbCCA", 12], ["Bcab", "AcABabB", 8], ["Bcab", "abA", 6], ["BcabB", "BcbCc", 6], ["BcabBB", "BbAbbbA", 7], ["BcabCA", "b", 10], ["BcabCac", "BbAAaca", 9], ["Bcaba", "aCcbA", 6], ["BcabaABA", "CAbB", 10], ["Bcac", "CcCAbaaa", 12], ["Bcac", "ccc", 4], ["BcacAC", "abBc", 9], ["BcacAaca", "acbAbc", 10], ["BcacCB", "cbabacC", 9], ["BcacaBC", "ACCac", 9], ["BcacabbA", "Bcb", 10], ["Bcacc", "b", 9], ["Bcb", "AaAcacaB", 13], ["Bcb", "Cc", 4], ["Bcb", "aBBC", 6], ["BcbAACc", "aBBcA", 11], ["BcbAAb", "ab", 9], ["BcbAC", "AcC", 6], ["BcbAaaacc", "cc", 14], ["BcbB", "BbcbBbBC", 8], ["BcbB", "aB", 6], ["BcbBcB", "bCbb", 7], ["BcbCA", "bCc", 6], ["BcbCAAba", "CCaa", 10], ["BcbCBCA", "BcBa", 7], ["BcbCC", "b", 8], ["BcbCCcc", "Abb", 12], ["BcbCabC", "A", 13], ["BcbCcB", "cbaccCa", 9], ["Bcba", "ACa", 5], ["Bcbb", "baaAa", 9], ["BcbbA", "BBb", 5], ["BcbbC", "ccaBAABCC", 12], ["BcbcA", "bcBcAAcB", 8], ["BcbcB", "Ccc", 6], ["Bcc", "Ab", 6], ["Bcc", "BCAbacBc", 10], ["Bcc", "aacb", 6], ["Bcc", "baAa", 7], ["BccABAbcA", "Aba", 13], ["BccACA", "aa", 10], ["BccAcAbbC", "caC", 13], ["BccBA", "BACCAC", 7], ["BccBAAbb", "aac", 14], ["BccBCbAA", "BBCCC", 10], ["BccBaAaa", "AACAbbbbB", 16], ["BccBac", "CCabACa", 10], ["BccC", "ac", 6], ["BccC", "bCA", 6], ["BccCa", "caaBBaaB", 14], ["BccCab", "Cbcbac", 8], ["BccCacBC", "BBbA", 13], ["BccCcA", "BcCBCAB", 6], ["Bcca", "AAb", 8], ["BccaA", "cCC", 7], ["BccaA", "ccc", 6], ["BccaBbB", "AA", 13], ["Bccac", "CcbaCCa", 9], ["Bccb", "AcbC", 6], ["BccbB", "bbccA", 7], ["BccbBCAA", "AA", 12], ["BccbBaCbb", "CbcBBCAA", 11], ["BccbacAAc", "CAac", 12], ["BccbbACa", "acCAbCAc", 10], ["BcccAC", "cccBbBaac", 12], ["BcccBB", "bABCc", 10], ["BcccBBb", "Cbacbaa", 11], ["C", "A", 2], ["C", "AAA", 6], ["C", "AAAb", 8], ["C", "AAB", 6], ["C", "AAacba", 11], ["C", "AAbb", 8], ["C", "ABA", 6], ["C", "ABAB", 8], ["C", "ABAcBAabA", 17], ["C", "ABCB", 6], ["C", "ABCbbbBAC", 16], ["C", "ABabc", 9], ["C", "ACCCcAcAa", 16], ["C", "ACc", 4], ["C", "ACcbaABc", 14], ["C", "AaAAa", 10], ["C", "AaAa", 8], ["C", "AaAbcaBb", 15], ["C", "AabbbC", 10], ["C", "AacCCABaB", 16], ["C", "AacaaaB", 13], ["C", "Ab", 4], ["C", "AbBbBACab", 16], ["C", "AbBcCcBAb", 16], ["C", "AbC", 4], ["C", "AbCCaCbc", 14], ["C", "Abb", 6], ["C", "AbbCcBb", 12], ["C", "Acbc", 7], ["C", "B", 2], ["C", "BAAbAbCa", 14], ["C", "BAAc", 7], ["C", "BACcBbAcB", 16], ["C", "BBBaAcB", 13], ["C", "BBacaaA", 13], ["C", "BC", 2], ["C", "BCCbA", 8], ["C", "BCba", 6], ["C", "Ba", 4], ["C", "BaA", 6], ["C", "BaBCCBcb", 14], ["C", "BaBcbbCBA", 16], ["C", "BaaC", 6], ["C", "BabbcCBa", 14], ["C", "BacccCB", 12], ["C", "BbABc", 9], ["C", "BbAaAAC", 12], ["C", "BbBBcc", 11], ["C", "BbCBCB", 10], ["C", "BbcCCbAC", 14], ["C", "BcB", 5], ["C", "BcBBBbAaa", 17], ["C", "BcCAcacAB", 16], ["C", "BcCB", 6], ["C", "BcacCaB", 12], ["C", "BccAaa", 11], ["C", "BccaAa", 11], ["C", "C", 0], ["C", "CA", 2], ["C", "CAAbCa", 10], ["C", "CABBB", 8], ["C", "CAaAa", 8], ["C", "CAbAABb", 12], ["C", "CBBCaABbB", 16], ["C", "CBaacB", 10], ["C", "CBc", 4], ["C", "CC", 2], ["C", "CCA", 4], ["C", "CCBbccA", 12], ["C", "CCc", 4], ["C", "CCcbbCBCc", 16], ["C", "CCcc", 6], ["C", "CCccCaCa", 14], ["C", "Ca", 2], ["C", "CaA", 4], ["C", "Caab", 6], ["C", "Cacaacba", 14], ["C", "Cb", 2], ["C", "CbaCBcAb", 14], ["C", "CbbBCbC", 12], ["C", "Cc", 2], ["C", "CcAcCA", 10], ["C", "CcBBCbBB", 14], ["C", "CcCBBbB", 12], ["C", "a", 2], ["C", "aA", 4], ["C", "aAABBb", 12], ["C", "aACBaca", 12], ["C", "aAaCB", 8], ["C", "aAaaBcaB", 15], ["C", "aAabb", 10], ["C", "aAccAABB", 15], ["C", "aBAaa", 10], ["C", "aBbab", 10], ["C", "aCBccCbBc", 16], ["C", "aCCBcbcBc", 16], ["C", "aCCC", 6], ["C", "aCcB", 6], ["C", "aCcccA", 10], ["C", "aa", 4], ["C", "aaBA", 8], ["C", "aaBAbA", 12], ["C", "aaBabBB", 14], ["C", "aaCAC", 8], ["C", "aaCBb", 8], ["C", "aaaBcBBc", 15], ["C", "aaab", 8], ["C", "aab", 6], ["C", "aacCCabb", 14], ["C", "abBbAcaCc", 16], ["C", "abbbAaaC", 14], ["C", "abcBBB", 11], ["C", "ac", 3], ["C", "b", 2], ["C", "bAAAccb", 13], ["C", "bAAaCBaCa", 16], ["C", "bAaABACCb", 16], ["C", "bAbAcCAc", 14], ["C", "bBABA", 10], ["C", "bBB", 6], ["C", "bBacA", 9], ["C", "bBcbbCabb", 16], ["C", "bC", 2], ["C", "bCcAcACba", 16], ["C", "ba", 4], ["C", "baAA", 8], ["C", "baAba", 10], ["C", "baCABab", 12], ["C", "baaA", 8], ["C", "baaB", 8], ["C", "baaCbCb", 12], ["C", "bbABBCA", 12], ["C", "bbABcCb", 12], ["C", "bbCab", 8], ["C", "bbacCaabC", 16], ["C", "bbbcaAb", 13], ["C", "bc", 3], ["C", "bcAca", 9], ["C", "bcBAABAAC", 16], ["C", "bcBcA", 9], ["C", "bcC", 4], ["C", "bcCAb", 8], ["C", "bcaAb", 9], ["C", "bcacBAC", 12], ["C", "bcb", 5], ["C", "bcbBba", 11], ["C", "bcbacaAA", 15], ["C", "bccC", 6], ["C", "bccccCA", 12], ["C", "c", 1], ["C", "cAc", 5], ["C", "cAcBCbCb", 14], ["C", "cAcaABCA", 14], ["C", "cBAA", 7], ["C", "cBBB", 7], ["C", "cBc", 5], ["C", "cBccbAA", 13], ["C", "cC", 2], ["C", "cCBAAB", 10], ["C", "cCaACAAC", 14], ["C", "cCbA", 6], ["C", "cCbbABa", 12], ["C", "cCcabCbBb", 16], ["C", "ca", 3], ["C", "caCACCCaB", 16], ["C", "cabCa", 8], ["C", "cacACaBB", 14], ["C", "cacBaACc", 14], ["C", "cbACabc", 12], ["C", "cbCCB", 8], ["C", "cbcC", 6], ["C", "cc", 3], ["C", "ccBAbBBC", 14], ["C", "ccBaC", 8], ["C", "ccaCbbCbb", 16], ["C", "ccabC", 8], ["C", "ccbBbCCc", 14], ["CA", "A", 2], ["CA", "AAa", 4], ["CA", "<PERSON>aa<PERSON>bb", 11], ["CA", "AcC", 5], ["CA", "BBaB", 7], ["CA", "BcAcbBC", 11], ["CA", "C", 2], ["CA", "CBCAa", 6], ["CA", "CacbbBaa", 13], ["CA", "CcBcBAB", 10], ["CA", "CcbCaBB", 11], ["CA", "a", 3], ["CA", "aAABAaC", 12], ["CA", "aABBa", 8], ["CA", "abbCC", 8], ["CA", "acbb", 7], ["CA", "bAbAAcbA", 13], ["CA", "bAcBcACC", 13], ["CA", "bCBaBBaB", 13], ["CA", "bbbB", 8], ["CA", "cAaaaA", 9], ["CA", "caCBCcaaA", 14], ["CAA", "CCbc", 6], ["CAA", "CbCcba", 9], ["CAA", "bb", 6], ["CAA", "c", 5], ["CAA", "cACAaa", 7], ["CAA", "cAaabbacA", 13], ["CAAA", "bb", 8], ["CAAAAAcCB", "bC", 16], ["CAAAB", "ba", 9], ["CAAABaBAc", "CaaC", 12], ["CAAACa", "AAcbAAc", 9], ["CAAACaAC", "aABBcbcBa", 15], ["CAAAaBAa", "BCBc", 14], ["CAAAba", "CacBaB", 8], ["CAAAbb", "cCaCb", 8], ["CAAB", "CbcCbBBA", 12], ["CAABAAcBC", "bccb", 14], ["CAABAAcbB", "ABBa", 13], ["CAABCaaBa", "aBCACcBca", 12], ["CAABCcAAa", "ccCB", 15], ["CAABbaA", "AbBaAac", 10], ["CAAC", "cacABc", 7], ["CAACAAC", "Ab", 12], ["CAACAbca", "Bb", 14], ["CAACCAc", "CAAB", 8], ["CAACb", "CA", 6], ["CAAa", "cBCC", 7], ["CAAaBaAC", "AaccBBB", 13], ["CAAaaA", "CaAaACca", 7], ["CAAab", "b", 8], ["CAAabac", "Accbbc", 8], ["CAAbCbCcC", "B", 17], ["CAAbb", "caC", 8], ["CAAbbAbb", "BC", 15], ["CAAcBB", "aCBCbAAb", 13], ["CAAccAcC", "abA", 13], ["CAB", "ABabCcc", 12], ["CAB", "ABccbC", 10], ["CAB", "CABba", 4], ["CAB", "CcbB", 4], ["CAB", "aababa", 10], ["CAB", "bC", 6], ["CAB", "cAbbABAac", 13], ["CABA", "CaabCCc", 10], ["CABAAa", "abC", 10], ["CABACacA", "cAbaaABC", 10], ["CABACcA", "B", 12], ["CABAa", "aac", 8], ["CABAcBa", "b", 13], ["CABBAa", "bbBcabcC", 13], ["CABBBAa", "B", 12], ["CABBBCaa", "ACb", 12], ["CABBBb", "cacaAACa", 14], ["CABC", "cCbacA", 9], ["CABCAcaC", "B", 14], ["CABa", "B", 6], ["CABaCc", "BCaaaAAcb", 11], ["CABab", "baB", 6], ["CABacBA", "BaACb", 10], ["CABb", "bCcBaCba", 10], ["CABbABcBA", "aBaAccabA", 10], ["CABbbC", "CbACCAcc", 11], ["CABcAaAb", "bb", 13], ["CABcaa", "C", 10], ["CABcaa", "aCaBABAab", 10], ["CABcabCAa", "AcbaBcCA", 11], ["CABcbCAc", "ABAcc", 9], ["CABcbbA", "BBbcA", 8], ["CABcbcacA", "cb", 14], ["CAC", "aCAAaABb", 12], ["CAC", "abCCACaBc", 12], ["CAC", "bbcbB", 9], ["CAC", "caBAa", 7], ["CAC", "ccBaBcb", 11], ["CACAACccb", "bc", 16], ["CACABBA", "cCACAcCcC", 10], ["CACACAcBB", "aBAACCc", 12], ["CACAabc", "cbAaBbbbB", 13], ["CACAcaAC", "ACcACc", 8], ["CACAcbC", "Cc", 10], ["CACB", "Cbc", 5], ["CACBb", "C", 8], ["CACC", "bCa", 6], ["CACCAAB", "bACabAC", 8], ["CACCAb", "abBBB", 10], ["CACCCBA", "cabaac", 12], ["CACCc", "ba", 9], ["CACaAcc", "acaBbCbb", 13], ["CACaCBBc", "a", 14], ["CACaabcBb", "aCabba", 10], ["CACacaABb", "b", 16], ["CACb", "ABbb", 6], ["CACb", "AabbaCa", 11], ["CACbA", "bCbbC", 8], ["CACbaCB", "caacaaC", 9], ["CACbaaCa", "bBacacB", 12], ["CACbbBBc", "Bc", 12], ["CACbc", "Cc", 6], ["CACbcAcac", "cCAcA", 10], ["CACbcba", "caABaCb", 10], ["CACcBabCc", "baAbbaBB", 13], ["CACcCacC", "ABcC", 10], ["CACccCAC", "B", 16], ["CAa", "Ab", 4], ["CAa", "CabcCc", 9], ["CAa", "aaCAbaBc", 10], ["CAa", "bABABaCA", 12], ["CAa", "bBAaB", 6], ["CAa", "bca", 4], ["CAa", "cbC", 5], ["CAaAAaA", "abAbbcbaC", 14], ["CAaAAbaaB", "CAcBB", 11], ["CAaAcb", "bBCBB", 11], ["CAaBAA", "AacAcBABB", 11], ["CAaBB", "Cc", 8], ["CAaBBACa", "CAaacBccC", 9], ["CAaBC", "b", 9], ["CAaBbbaBA", "BBB", 13], ["CAaC", "AaBCacAba", 14], ["CAaC", "BcccCbBb", 13], ["CAaCBbcab", "CbC", 13], ["CAaCc", "AA", 7], ["CAaCcabB", "cBABCb", 11], ["CAaCccCc", "A", 14], ["CAaa", "bcABBaAb", 10], ["CAaa", "cbabABAC", 12], ["CAaaB", "b", 9], ["CAaabBcC", "a", 14], ["CAaabbCac", "bAC", 14], ["CAaabcaB", "cabCacbaA", 12], ["CAabABbAa", "bcbaCa", 13], ["CAabac", "baaABbB", 10], ["CAabb", "b", 8], ["CAabb", "cACACBaBB", 10], ["CAac", "aaB", 5], ["CAac", "cccBB", 9], ["CAacAB", "a", 10], ["CAaccC", "cacbc", 6], ["CAb", "ACCCAcaA", 12], ["CAb", "Aca", 6], ["CAb", "BCcBBAA", 10], ["CAb", "C", 4], ["CAb", "CCAcbAB", 8], ["CAb", "CcCbCBC", 10], ["CAb", "acAc", 5], ["CAb", "bab", 3], ["CAb", "cA", 3], ["CAbAAaAA", "abc", 13], ["CAbAc", "ABBCCB", 10], ["CAbB", "acC", 7], ["CAbBBBB", "BCcaBbabB", 10], ["CAbBCCBAC", "baA", 14], ["CAbBab", "cbABcBc", 10], ["CAbBc", "bBcAcCA", 11], ["CAbBc", "b<PERSON><PERSON><PERSON>", 7], ["CAbC", "AaAc", 6], ["CAbC", "cBAC", 5], ["CAbCBaBb", "cAbcBa", 6], ["CAbCCC", "c", 11], ["CAbCabbA", "BbAABACba", 12], ["CAbCcc", "babBA", 9], ["CAba", "abbB", 6], ["CAbaAbBBC", "CbBBAbB", 9], ["CAbaAcCCC", "AcbB", 14], ["CAbaBb", "A", 10], ["CAbaa", "cc", 9], ["CAbacbba", "cBCccabA", 10], ["CAbbAA", "BAAaCbC", 12], ["CAbbAAcC", "CBBACbBCB", 12], ["CAbbBCcB", "Abca", 10], ["CAbbBbC", "ABcaca", 11], ["CAbbbAC", "bcB", 11], ["CAbbcCABA", "CbB", 12], ["CAbc", "B", 7], ["CAbcA", "CAcaccACb", 10], ["CAbcACCB", "BB", 13], ["CAbcBAa", "bccBACB", 10], ["CAbcCca", "cAc", 9], ["CAbcaBbca", "ccaacBaaa", 12], ["CAc", "AAcacbcC", 12], ["CAc", "AbbAbbcc", 12], ["CAc", "BAB", 4], ["CAc", "ab", 5], ["CAc", "b", 6], ["CAc", "bbaaac", 9], ["CAcAaCcaC", "AAa", 12], ["CAcAbaaA", "ccBcBc", 12], ["CAcAcC", "BAbCb", 9], ["CAcAca", "CCbb", 9], ["CAcAcbbAc", "Cca", 13], ["CAcB", "cBcaaCcB", 10], ["CAcBbCccb", "BBAbAbAB", 15], ["CAcBbbAaA", "aACbbCbaa", 9], ["CAcBbcbCa", "bcBc", 12], ["CAcBcBC", "abCaCb", 11], ["CAcC", "CBcB", 4], ["CAcCACCA", "cAAa", 10], ["CAcCaCBCC", "aac", 14], ["CAca", "AaBACbAab", 13], ["CAcaABcab", "aCCbC", 14], ["CAcaaa", "bbccA", 9], ["CAcaacBB", "c", 14], ["CAcb", "B", 7], ["CAcb", "aBCBAb", 8], ["CAcbBA", "bBAaba", 9], ["CAcbBB", "CCcCBA", 6], ["CAcbBacb", "AbbbB", 10], ["CAcbCCaaC", "bccB", 14], ["CAcbCbc", "bAcCA", 8], ["CAcbbabC", "abAccCaac", 11], ["CAcbcA", "BBca", 8], ["CAcbcB", "Ab", 8], ["CAcc", "b", 8], ["CAccAaAcc", "bccbAB", 12], ["CAccBaAC", "abbAAaB", 13], ["CAccBaaCc", "BCAb", 15], ["CAccBbA", "bcBbBcBc", 13], ["CAccac", "aAc", 8], ["CB", "A", 4], ["CB", "ABac", 6], ["CB", "AC", 4], ["CB", "ACbbcbb", 11], ["CB", "ACcCCBAC", 12], ["CB", "AaAAcBc", 11], ["CB", "AaBCab", 9], ["CB", "BCc", 4], ["CB", "BaCaB", 6], ["CB", "BccbcabCB", 14], ["CB", "C", 2], ["CB", "CAAAb", 7], ["CB", "CCba", 5], ["CB", "CaAcA", 8], ["CB", "CcCaAccb", 13], ["CB", "a", 4], ["CB", "aAa", 6], ["CB", "aBCBaBbC", 12], ["CB", "aCbcAc", 9], ["CB", "ac", 4], ["CB", "bAa", 6], ["CB", "bBAAbcb", 12], ["CB", "bCB", 2], ["CB", "bCaCCcaCA", 16], ["CB", "ba", 4], ["CB", "baBCcCc", 12], ["CB", "bbcbCB", 8], ["CB", "bcaab", 8], ["CB", "c", 3], ["CB", "cACca", 8], ["CB", "cBCbAB", 8], ["CB", "cCB", 2], ["CB", "cbb", 4], ["CB", "cbbBbACCA", 15], ["CBA", "ba", 4], ["CBA", "cbcBb", 7], ["CBAA", "ABBaBcB", 11], ["CBAA", "Ba", 5], ["CBAA", "bcc", 7], ["CBAABa", "BAaBA", 4], ["CBAAC", "CBAac", 2], ["CBAAa", "cccbAb", 9], ["CBAAaac", "bcAAb", 10], ["CBAAbCc", "bbaABBB", 9], ["CBAAbbBC", "CbBAaAc", 10], ["CBAAc", "AAA", 6], ["CBAAc", "bCbBbAAaC", 9], ["CBAB", "A", 6], ["CBABAbcBA", "b", 16], ["CBABAcBc", "cAb", 12], ["CBABCacb", "aCCbbBABA", 14], ["CBABaa", "cBbcCcc", 11], ["CBABbbCa", "BbAaA", 11], ["CBABbcCCC", "cCAC", 12], ["CBABc", "cCc", 7], ["CBABccBbc", "caAaaACbb", 13], ["CBACAA", "CCc", 8], ["CBACAcC", "aCbbc", 10], ["CBACa", "BACaBBaB", 10], ["CBACbcB", "a", 13], ["CBACc", "baCCCA", 9], ["CBACcAccA", "aAb", 15], ["CBAaAC", "ccbC", 9], ["CBAaBaC", "bCccccCb", 14], ["CBAaCB", "acabB", 8], ["CBAaCa", "<PERSON><PERSON><PERSON>", 8], ["CBAaCc", "cBBBBBaC", 11], ["CBAabb", "bbCabaBa", 10], ["CBAacCa", "Bccbacb", 11], ["CBAb", "cCAcbc", 7], ["CBAbCcaB", "aC", 13], ["CBAba", "CbbabA", 5], ["CBAbabb", "BABa", 7], ["CBAbba", "ba", 8], ["CBAcAAbcb", "BBBBBbABA", 15], ["CBAcC", "acbabBA", 11], ["CBAccB", "cb", 9], ["CBAccab", "cbB", 11], ["CBB", "Aa", 6], ["CBB", "BbAaC", 9], ["CBB", "CB", 2], ["CBB", "CCA", 4], ["CBB", "CcbCb", 6], ["CBB", "baBB", 4], ["CBB", "cAcBBccA", 11], ["CBB", "cBccaB", 7], ["CBBABB", "BbBb", 6], ["CBBABC", "AABcaac", 10], ["CBBAbACb", "CCBCbAbcB", 8], ["CBBBA", "bB", 7], ["CBBBBaAC", "ca", 13], ["CBBBBbbcC", "BaAbCaB", 14], ["CBBBb", "bCbaBBAb", 7], ["CBBBbCbcA", "ABCCcABc", 13], ["CBBC", "ACaca", 8], ["CBBC", "cB", 5], ["CBBCAAB", "b", 13], ["CBBCAb", "CBA", 6], ["CBBCCAB", "AbCa", 10], ["CBBCCa", "c", 11], ["CBBCabAa", "Cca", 11], ["CBBaCc", "CbbA", 7], ["CBBab", "acBCcA", 9], ["CBBabb", "BBCcBb", 7], ["CBBacBacB", "caCCccBc", 12], ["CBBbAb", "Bbab", 5], ["CBBba", "bcCcb", 9], ["CBBbaAcbc", "ABcABaAAc", 11], ["CBBbbbabc", "baAaBC", 12], ["CBBbcaaC", "bABABABB", 13], ["CBBc", "abCABc", 6], ["CBBcACB", "abBbC", 9], ["CBBcBcc", "A", 14], ["CBBcbA", "ccbBAca", 9], ["CBBcbaBc", "C", 14], ["CBBcbaC", "bBcAAbaaC", 9], ["CBBccBCCB", "bacCC", 11], ["CBBccc", "aBabc", 8], ["CBC", "ACB", 4], ["CBC", "CAbCabB", 9], ["CBC", "aCaAaAC", 10], ["CBCAA", "C", 8], ["CBCAABa", "BBcCbBa", 7], ["CBCAACB", "bBBcAAB", 7], ["CBCACAa", "ABbA", 10], ["CBCAaCA", "BbaAbac", 10], ["CBCAacCc", "cCb", 12], ["CBCB", "aAAaA", 10], ["CBCBBA", "acc", 11], ["CBCBBCAA", "ABC", 12], ["CBCBC", "aBccbCB", 8], ["CBCBCC", "BAbc", 8], ["CBCC", "CbaaA", 7], ["CBCCBC", "aACAA", 10], ["CBCCBCcCb", "Ba", 16], ["CBCCCB", "cacbAbbaA", 15], ["CBCCCbaB", "cacAbc", 12], ["CBCCacb", "a", 12], ["CBCCbcc", "BbbBcbCBc", 11], ["CBCCcCbA", "CBaAa", 11], ["CBCa", "aaAbbabA", 13], ["CBCa", "b", 7], ["CBCaA", "ab", 8], ["CBCaAaBaa", "acBbAa", 12], ["CBCaB", "caa", 7], ["CBCacBBBB", "BcabACCb", 12], ["CBCb", "AabACCa", 11], ["CBCb", "aABc", 7], ["CBCbBa", "abaCaB", 9], ["CBCbbc", "CcBCBCcbA", 9], ["CBCbccaA", "AbCbbCba", 9], ["CBCc", "bbcCBBAcb", 12], ["CBCcB", "BAa", 8], ["CBCcBbb", "ccaCBbA", 8], ["CBCcccacB", "BA", 15], ["CBa", "ACaba", 5], ["CBa", "CAaB", 4], ["CBa", "CBBCccc", 10], ["CBa", "CacAccB", 11], ["CBa", "c", 5], ["CBaAAa", "caCcaBAA", 9], ["CBaAC", "CBb", 6], ["CBaAbaaAA", "CbbcC", 13], ["CBaB", "aabBAbbC", 12], ["CBaBBAa", "AaAcA", 10], ["CBaBCABC", "AAA", 13], ["CBaBaCcB", "cBaAbBAAa", 12], ["CBaBaa", "AAB", 9], ["CBaCAbC", "BbCaCC", 7], ["CBaCBba", "bcABB", 10], ["CBaCCAB", "cBcBAcA", 10], ["CBaCabccB", "Bba", 14], ["CBaaA", "AacbaC", 10], ["CBaaAcac", "aacbcbACc", 13], ["CBaaB", "CbaAcB", 4], ["CBaaBc", "Ba", 8], ["CBaaCaCa", "c", 15], ["CBaaaBB", "B", 12], ["CBaab", "BaabBcba", 10], ["CBaab", "bBB", 7], ["CBaabAB", "CBCC", 10], ["CBaacACCC", "a", 16], ["CBab", "CB", 4], ["CBab", "aBcc", 6], ["CBabACCaA", "A", 16], ["CBabCbbaC", "AcbAc", 12], ["CBacA", "acBCCaCB", 10], ["CBacaCBCB", "aB", 14], ["CBacbacB", "CaBa", 9], ["CBb", "Cbb", 1], ["CBb", "a", 6], ["CBb", "bBbcc", 6], ["CBb", "bCaaaA", 10], ["CBbA", "bAaCbBC", 10], ["CBbAAa", "aabB", 10], ["CBbAB", "A", 8], ["CBbAB", "bbb", 6], ["CBbACca", "CbCAcB", 7], ["CBbACcbC", "bCcbBAb", 12], ["CBbBB", "C", 8], ["CBbBB", "CBCBcCBa", 8], ["CBbBBCaBB", "AbAca", 13], ["CBbCA", "aca", 8], ["CBbCCB", "BAbC", 8], ["CBbCCCa", "Ca", 10], ["CBbCCb", "CACbBBb", 8], ["CBbCbbBc", "ABaA", 14], ["CBbaAcAAc", "ccAACB", 12], ["CBbaAca", "bB", 12], ["CBbaB", "B", 8], ["CBbaBBCaC", "baaCcA", 12], ["CBbaC", "c", 9], ["CBbaCa", "cbbcAa", 6], ["CBbaCbB", "AcCcACbA", 10], ["CBbabC", "abB", 8], ["CBbb", "C", 6], ["CBbb", "a", 8], ["CBbbbaB", "B", 12], ["CBbc", "BaAABc", 9], ["CBbcBAAB", "cAbCAA", 8], ["CBbcbb", "cBCaC", 8], ["CBc", "Bcab", 6], ["CBc", "CBAAacc", 8], ["CBc", "abaCa", 8], ["CBc", "cAabccaaA", 14], ["CBcA", "CCA", 3], ["CBcAAaccC", "abBcAbaBC", 10], ["CBcAaCCCb", "aB", 15], ["CBcAbAA", "BabCcAAc", 11], ["CBcAcB", "CbbAAA", 7], ["CBcB", "a", 8], ["CBcB", "bcCBCCcbb", 11], ["CBcBA", "cBbCb", 7], ["CBcBBABcc", "bbBcabA", 13], ["CBcBC", "cAbCAAbcA", 13], ["CBcBaaACB", "cCc", 14], ["CBcBb", "c", 8], ["CBcC", "bCBBbBa", 10], ["CBcC", "c", 6], ["CBcCC", "aB", 8], ["CBca", "cC", 6], ["CBcaAa", "A", 10], ["CBcaAba", "AaAAaaCaA", 13], ["CBcaBCc", "AcBcb", 9], ["CBcabbbba", "bCCBc", 15], ["CBcacCbCC", "bCAaab", 13], ["CBcb", "Acb", 4], ["CBcb", "CbcABCA", 8], ["CBcb", "cBAac", 7], ["CBcbCbc", "cBBaBc", 7], ["CBcbCcaBc", "BAACAaC", 11], ["CBcba", "abCA", 7], ["CBcbab", "ABBabBC", 9], ["CBcbbaC", "ABcCa", 8], ["CBcbcAAA", "aaAbb", 14], ["CBcbcbC", "c", 12], ["CBccaC", "CAAa", 8], ["CBccaCaaa", "ccbBabcBB", 15], ["CBccac", "aB", 10], ["CBccbaAbb", "bBaacaC", 14], ["CBccbbc", "ACaAaCB", 13], ["CC", "AA", 4], ["CC", "AAaacAc", 12], ["CC", "ACACbCABB", 14], ["CC", "ACBaCAb", 10], ["CC", "ACaaB", 8], ["CC", "ACcCAaabb", 14], ["CC", "AacCBBc", 11], ["CC", "BaAACABCC", 14], ["CC", "BaBcaCbb", 13], ["CC", "Bc", 3], ["CC", "CBC", 2], ["CC", "CCAAAbab", 12], ["CC", "CCc", 2], ["CC", "Cb", 2], ["CC", "CbbBb", 8], ["CC", "CcAc", 5], ["CC", "a", 4], ["CC", "aACcC", 6], ["CC", "aBC", 4], ["CC", "aBbc", 7], ["CC", "aa", 4], ["CC", "bA", 4], ["CC", "c", 3], ["CC", "cBBB", 7], ["CC", "cBcCbBcab", 15], ["CC", "cCabBBC", 10], ["CC", "cCbBB", 7], ["CCA", "A", 4], ["CCA", "BaaabCA", 10], ["CCA", "CBaCacAB", 10], ["CCA", "aba", 5], ["CCA", "bb", 6], ["CCAA", "ABBCA", 8], ["CCAA", "BcCbBA", 7], ["CCAA", "babAabAA", 12], ["CCAA", "cAcC", 7], ["CCAABbAAc", "CA", 14], ["CCAABc", "BacACaaB", 11], ["CCAACAbaa", "BA", 16], ["CCAAaB", "CaCCcCa", 10], ["CCAAbabbc", "cc", 15], ["CCAAcCCcA", "ABcACaBc", 13], ["CCAAcb", "C", 10], ["CCAB", "bbc", 8], ["CCABAAB", "BCABbccB", 8], ["CCABBCcbB", "CabB", 11], ["CCABCCAaa", "CBCc", 11], ["CCABb", "Bcc", 9], ["CCABcCBAa", "bbBa", 13], ["CCABcaC", "caCc", 10], ["CCABcbBaC", "cAAaAAaC", 11], ["CCAC", "aBAccBA", 11], ["CCACb", "BABcBc", 10], ["CCACbACc", "<PERSON><PERSON><PERSON><PERSON>", 11], ["CCACcaCB", "ABbCACbB", 11], ["CCAa", "aACCbaaac", 11], ["CCAb", "ACCBba", 6], ["CCAbBcABA", "cAaCBc", 12], ["CCAbBcb", "baCB", 10], ["CCAbCAb", "BaCCaa", 10], ["CCAbaC", "BBbcaa", 10], ["CCAbaCBa", "bcccA", 12], ["CCAcAB", "ccAAbCa", 9], ["CCAcACA", "acBA", 9], ["CCAcBAa", "aCacBaBAA", 8], ["CCAcC", "BbcBbC", 9], ["CCAcCB", "cBcaBBBA", 11], ["CCAcaC", "CcaB", 6], ["CCAcaaca", "cC", 13], ["CCAcacccc", "cAcabcB", 9], ["CCAcbCb", "ACCa", 9], ["CCAcbbCBc", "CcAbBCa", 8], ["CCB", "ACbaAACcB", 12], ["CCB", "Cc", 3], ["CCB", "abaAB", 8], ["CCB", "baCac", 8], ["CCBA", "Aaaaa", 9], ["CCBAA", "a", 9], ["CCBABcc", "BCABb", 8], ["CCBAC", "bCCcaCAc", 9], ["CCBAbAC", "bCA", 10], ["CCBAc", "BC", 7], ["CCBAcaacb", "aCCAA", 13], ["CCBBCBccb", "BBAa", 14], ["CCBBbB", "CbcAACa", 11], ["CCBBccC", "c", 12], ["CCBC", "CBCaABac", 9], ["CCBCAaAA", "CaC", 12], ["CCBCBCcc", "bcCccbAbb", 13], ["CCBCb", "c", 9], ["CCBaAaB", "bACAC", 11], ["CCBaAaB", "cbcCBca", 11], ["CCBaAcba", "BaaaCAA", 11], ["CCBaBbbBC", "a", 16], ["CCBaCcAa", "ccBbACCb", 10], ["CCBbAAAB", "CCcaBabc", 11], ["CCBbACc", "bbbaacBb", 12], ["CCBbB", "cBBBACC", 10], ["CCBbBC", "bAcCCab", 12], ["CCBbabCB", "bCcaB", 10], ["CCBbca", "bc", 8], ["CCBcB", "ACbbCa", 8], ["CCBca", "BacCaaAA", 12], ["CCBcbbC", "cB", 11], ["CCC", "CcaB", 5], ["CCC", "baaaC", 8], ["CCCAABC", "CC", 10], ["CCCAbbBCb", "cBBaaBab", 12], ["CCCAc", "BaccCaB", 9], ["CCCBAbcc", "accAb", 10], ["CCCBBcab", "ACCA", 11], ["CCCBCaAcC", "aaCcAbAa", 14], ["CCCBbbbC", "aCb", 12], ["CCCBcBBaa", "aBACAbAA", 14], ["CCCBccaB", "ccBcc", 8], ["CCCC", "bBcAaAbaC", 15], ["CCCCA", "BBACa", 7], ["CCCCA", "bAcAbbC", 13], ["CCCCA", "cccAC", 7], ["CCCCAbaa", "CaBaccBB", 14], ["CCCCBC", "ABbBc", 9], ["CCCCCa", "BCACA", 7], ["CCCCaBCA", "Bacbcab", 13], ["CCCCbaC", "b", 12], ["CCCCbbba", "BAbcAbcBb", 14], ["CCCCcA", "bbCCbbc", 10], ["CCCCcBAC", "acACBcC", 9], ["CCCa", "bAb", 8], ["CCCaABacb", "bacBBcAA", 14], ["CCCaCcA", "bAcAC", 10], ["CCCaCcbBA", "bAbc", 15], ["CCCaaCaBA", "AbAbBCb", 15], ["CCCabaaab", "cAbca", 12], ["CCCacacb", "CAcCBcA", 10], ["CCCb", "C", 6], ["CCCbBBB", "A", 14], ["CCCba", "BbAcaC", 10], ["CCCbbAA", "a", 13], ["CCCbbaA", "cCB", 10], ["CCCbbcc", "BCA", 12], ["CCCc", "CBbBBaBBa", 16], ["CCCcBBba", "CcBbcAa", 9], ["CCCcBaA", "CCCBAAAcA", 9], ["CCa", "Ca", 2], ["CCa", "CbabbAAcA", 14], ["CCa", "bbbCBA", 9], ["CCa", "cCAcbAbCC", 14], ["CCaA", "c", 7], ["CCaAA", "BBACcB", 11], ["CCaAAb", "cBa", 9], ["CCaABbBaA", "CcACab", 11], ["CCaAcb", "ABbBCaCa", 13], ["CCaBAcbAC", "CBbc", 11], ["CCaBBA", "bAbBB", 8], ["CCaCAAbA", "AA", 12], ["CCaCACBcb", "caC", 13], ["CCaCAaA", "BCAaCaa", 7], ["CCaCB", "CAAA", 7], ["CCaCBcAC", "bcCa", 12], ["CCaCCC", "CACcBa", 8], ["CCaCaCc", "BcCba", 11], ["CCaCaCcBA", "a", 16], ["CCaCabaAa", "CcccAacAC", 11], ["CCaCb", "bAcaCbCAb", 11], ["CCaCbaaaC", "CaCBCBBc", 10], ["CCaaBbbA", "BAAc", 14], ["CCaaCbCab", "aBbB", 13], ["CCaaaaccC", "AABcca", 12], ["CCaacBa", "a", 12], ["CCabA", "acbCAAbb", 10], ["CCabA", "bbaacCA", 10], ["CCabCAac", "ABABA", 12], ["CCabcCbc", "BCbc", 9], ["CCac", "a", 6], ["CCacAac", "bbBbaC", 11], ["CCacAbCcb", "bCbBa", 14], ["CCacBca", "C", 12], ["CCacCb", "cBAC", 8], ["CCacab", "Aa", 9], ["CCacbb", "CAb", 7], ["CCacbcB", "Cca", 9], ["CCb", "BAbBAaB", 12], ["CCb", "CAAAbca", 10], ["CCb", "bAc", 6], ["CCb", "bcabBaB", 11], ["CCb", "cbCcaAaac", 15], ["CCbA", "AABAC", 7], ["CCbA", "BbBCabCaa", 13], ["CCbAAa", "BcABbC", 11], ["CCbABCC", "a", 13], ["CCbACCBa", "BbbaBbC", 12], ["CCbAaaA", "aB", 12], ["CCbAbC", "cAacBCa", 10], ["CCbAbbBB", "abcaA", 13], ["CCbB", "Aa", 8], ["CCbB", "cbaBCBBA", 10], ["CCbBBB", "CACCacCBc", 12], ["CCbBCC", "BbCAbcAA", 12], ["CCbBbaBa", "BcbccCAc", 13], ["CCbCBCbB", "cCbbB", 7], ["CCbCbA", "ABACcA", 8], ["CCbCbcacC", "bBC", 13], ["CCba", "aBaAcAaCC", 15], ["CCba", "caCAcc", 9], ["CCba", "cbaCcba", 7], ["CCbaB", "Ba", 7], ["CCbaBB", "CCaccA", 8], ["CCbaCC", "aBbcaa", 10], ["CCbaaBcAB", "a", 16], ["CCbaba", "ACbaBAAb", 8], ["CCbb", "aBbacACBa", 14], ["CCbbACcA", "CBabaAA", 9], ["CCbbAabCa", "cAABb", 13], ["CCbbBabb", "aBBcc", 13], ["CCbbaBC", "cBC", 9], ["CCbbca", "bAC", 9], ["CCbbcaCBA", "CCaAaBBAB", 10], ["CCbcAac", "a", 12], ["CCbcBC", "aabbAc", 9], ["CCbcBca", "B", 12], ["CCbcCAc", "Bb", 12], ["CCbcb", "cBaC", 8], ["CCbcc", "aBaccBcB", 11], ["CCc", "A", 6], ["CCc", "Ac", 4], ["CCc", "Bc", 4], ["CCc", "Cacbb", 6], ["CCc", "bCcbAbbAB", 14], ["CCcAC", "CABCA", 7], ["CCcACBCC", "B", 14], ["CCcACc", "cACCCA", 8], ["CCcAaaba", "CCCBcABca", 9], ["CCcAc", "abaaaCCc", 13], ["CCcB", "aCAaBc", 8], ["CCcBAB", "CacAbbcbB", 11], ["CCcBAcC", "bB", 12], ["CCcBbAb", "aBbC", 10], ["CCcBcaB", "cBbb", 9], ["CCcCaaAC", "CAAbccC", 12], ["CCcCb", "B", 9], ["CCcCbacba", "AAa", 15], ["CCcCcABB", "cAcBabacb", 13], ["CCcCcccAB", "BAaACCCAB", 11], ["CCca", "Acc", 5], ["CCca", "a", 6], ["CCcaBCabB", "cAabc", 11], ["CCcaC", "bbCACAA", 10], ["CCcaCca", "CabacBb", 9], ["CCcaaBaB", "ccaAabb", 9], ["CCcaaabbc", "BCaCC", 13], ["CCcabA", "bCCAAcaBC", 9], ["CCcacbB", "<PERSON>bb", 9], ["CCcb", "C", 6], ["CCcbB", "abB", 6], ["CCcbBBC", "aAAaCCCc", 14], ["CCcbC", "ACcBBBBc", 10], ["CCcbC", "a", 10], ["CCcbCACab", "Accba", 11], ["CCcbcaaaA", "ccaaAbaaB", 12], ["CCccAAB", "CCaaa", 8], ["CCccB", "AACBAbC", 11], ["CCcca", "baAaCccaa", 10], ["CCccaAA", "b", 14], ["CCccbb", "babC", 10], ["Ca", "ABCCb", 8], ["Ca", "AaCcCBaab", 14], ["Ca", "Aac", 4], ["Ca", "BAAABca", 11], ["Ca", "BACaCCb", 10], ["Ca", "BAc", 5], ["Ca", "BBAA", 7], ["Ca", "BCB", 4], ["Ca", "BCBbBaAC", 12], ["Ca", "BaCCAcBAa", 14], ["Ca", "BabB", 6], ["Ca", "BababbBb", 14], ["Ca", "BbCB", 6], ["Ca", "Bbb", 6], ["Ca", "CAbc", 5], ["Ca", "CBAbac", 8], ["Ca", "CBB", 4], ["Ca", "CBaA", 4], ["Ca", "CCAB", 5], ["Ca", "CCaCBaaA", 12], ["Ca", "CCbaAA", 8], ["Ca", "CCbb", 6], ["Ca", "Cb", 2], ["Ca", "aACCba", 8], ["Ca", "aB", 4], ["Ca", "aBBA", 7], ["Ca", "aac", 4], ["Ca", "acBacBcAA", 15], ["Ca", "b", 4], ["Ca", "bAbBbbAa", 14], ["Ca", "bBCA", 5], ["Ca", "bbBAAAa", 12], ["Ca", "c", 3], ["Ca", "cCBCBbAAa", 14], ["Ca", "caBabaBc", 13], ["Ca", "ccCaCCC", 10], ["CaA", "BcBcAb", 9], ["CaA", "CCBBA", 6], ["CaA", "aBa", 5], ["CaA", "aBcbcb", 11], ["CaA", "bBcCBcbcc", 16], ["CaA", "bCABC", 7], ["CaA", "bCcBaAaA", 10], ["CaA", "c", 5], ["CaAA", "bacCbbA", 10], ["CaAABbc", "c", 12], ["CaAAC", "c", 9], ["CaAACB", "bB", 10], ["CaAAa", "AcCbA", 9], ["CaAAbcCa", "a", 14], ["CaAB", "BCaCaCB", 7], ["CaAB", "Ba", 6], ["CaAB", "bCb", 7], ["CaABBc", "b", 11], ["CaABCBA", "bBb", 11], ["CaABa", "B", 8], ["CaABbACB", "BA", 12], ["CaABbC", "AAacAc", 9], ["CaAC", "BBCbCcaaC", 11], ["CaAC", "aCAABab", 9], ["CaAC", "acCBbcbA", 13], ["CaACB", "B", 8], ["CaACbacC", "BAbabAAB", 12], ["CaACbc", "CBA", 8], ["CaACcBACc", "aAcc", 10], ["CaAa", "Bababa", 7], ["CaAa", "ccAC", 5], ["CaAaAC", "ACa", 9], ["CaAaACCc", "BaabbBCbC", 12], ["CaAaBbB", "cbAb", 9], ["CaAaa", "cAAACBa", 7], ["CaAab", "BBbcbaa", 12], ["CaAabcABB", "ACcBacC", 14], ["CaAbC", "bC", 6], ["CaAbbBa", "c", 13], ["CaAc", "Ac", 4], ["CaAc", "abB", 6], ["CaAcCAb", "cABCaAA", 9], ["CaAcCa", "bBabcCCC", 10], ["CaAca", "bcAcBAABA", 13], ["CaAcb", "aBbACB", 8], ["CaAcc", "ACc", 5], ["CaB", "B", 4], ["CaB", "C", 4], ["CaB", "acbCAcC", 11], ["CaB", "bbAaAaBB", 12], ["CaB", "cabbb", 6], ["CaBA", "cbcAB", 7], ["CaBAAB", "BBAC", 8], ["CaBAAB", "a", 10], ["CaBAAaBB", "AACB", 10], ["CaBAbCb", "baA", 10], ["CaBAcBCcb", "A", 16], ["CaBAcc", "AACCaA", 11], ["CaBAcc", "bAcBbA", 11], ["CaBB", "aCCBbcaba", 13], ["CaBB", "babABcb", 9], ["CaBBBa", "Cc", 10], ["CaBBBbBB", "CAAaC", 13], ["CaBBCccC", "bAac", 13], ["CaBBaA", "a", 10], ["CaBBabBcc", "b", 16], ["CaBBcC", "BA", 10], ["CaBBccac", "accCBA", 11], ["CaBC", "CaaacC", 6], ["CaBCACabA", "CbcABbc", 10], ["CaBCBbC", "ccbBbaC", 8], ["CaBCCcaCc", "CaC", 12], ["CaBaaBcA", "C", 14], ["CaBacBc", "c", 12], ["CaBba", "bbcCaAa", 10], ["CaBbac", "A", 11], ["CaBcAc", "CAAab", 8], ["CaBcBAbB", "BABCCca", 12], ["CaBcaA", "bcC", 9], ["CaBccaAc", "bBa", 12], ["CaC", "aC", 2], ["CaCA", "bcBA", 6], ["CaCAAbCcB", "aBCc", 11], ["CaCABCcbA", "bB", 16], ["CaCACCaA", "BCBBaaBCA", 13], ["CaCAbBA", "bCBb", 10], ["CaCB", "AB", 5], ["CaCB", "bAb", 6], ["CaCBa", "ACabCC", 8], ["CaCBa", "abBaa", 6], ["CaCBaaC", "bcACBa", 8], ["CaCBbBBb", "bab", 12], ["CaCBc", "cAC", 6], ["CaCCbB", "babA", 8], ["CaCCbc", "BAbcCcBB", 11], ["CaCa", "Aab", 6], ["CaCa", "CCccAab", 9], ["CaCa", "CcbcAAC", 10], ["CaCa", "abCCa", 6], ["CaCaAB", "aBb", 9], ["CaCb", "bCab", 4], ["CaCbBcCCC", "CaBBab", 11], ["CaCbbCAcb", "aABcAc", 10], ["CaCbbaB", "ABB", 10], ["CaCc", "A", 7], ["CaCc", "ccBbc", 7], ["CaCcAAb", "baCBCB", 9], ["CaCcB", "AA", 9], ["CaCcBa", "cAc", 8], ["CaCcaa", "CABabbB", 11], ["CaCcbBA", "CAbccCaa", 9], ["Caa", "Acc", 6], ["Caa", "BAcBcBCbb", 16], ["Caa", "abbAb", 9], ["CaaABcBab", "acACccaCa", 12], ["CaaAaAa", "bbaabAc", 9], ["CaaAaAaba", "aaCBc", 13], ["CaaAbCcAb", "bc", 14], ["CaaBAab", "CcC", 12], ["CaaBcB", "Caaa", 6], ["CaaC", "AAbBaC", 7], ["CaaCA", "A", 8], ["CaaCbCB", "c", 13], ["Caaa", "BBaBcA", 9], ["CaaaCabaC", "CAa", 13], ["Caaab", "a", 8], ["CaaabAbc", "ACc", 12], ["CaaabCbB", "ABcB", 11], ["Caaabcc", "babAaAC", 10], ["Caaac", "AAbbccabb", 15], ["Caab", "BacAc", 7], ["CaacAaCb", "AbccbacB", 10], ["CaacCb", "acbcCABBB", 13], ["Caacaccbc", "bCcbB", 13], ["CaacccACC", "baBCAaCA", 12], ["Cab", "AbBCbcBbc", 14], ["Cab", "AcC", 6], ["Cab", "BCA", 5], ["Cab", "BaB", 3], ["Cab", "c", 5], ["CabA", "b", 6], ["CabAabA", "cbCaBBb", 10], ["CabAbcCB", "acbCa", 10], ["CabB", "AACcCBcab", 14], ["CabB", "BabB", 2], ["CabB", "Ccba", 4], ["CabBCa", "AaCaab", 10], ["CabBacbC", "C", 14], ["CabBbab", "Bc", 12], ["CabBbc", "BbBCbcBca", 12], ["CabBcC", "AaAcCAC", 9], ["CabCB", "AaCBCab", 8], ["CabCaAABb", "BbaB", 12], ["CabCb", "BcCbacbb", 10], ["Caba", "AA", 6], ["Caba", "CBbBBbCC", 12], ["CabaAAcbc", "aA", 14], ["CabaAaab", "Bccb", 13], ["CabaBCc", "acBcBb", 11], ["CabaCAC", "bAaa", 10], ["CababAAcB", "a", 16], ["<PERSON><PERSON><PERSON>", "aBbABaA", 11], ["CabbAAABB", "ccA", 15], ["CabbACc", "ABCCca", 10], ["CabbB", "BabCaAC", 10], ["CabbB", "CbBbAccbc", 12], ["CabbCaa", "bbaBbcacC", 10], ["CabbaAba", "CABaCBAbA", 9], ["CabbbAACB", "Babac", 12], ["CabcB", "aBAbaA", 9], ["CabcBaC", "Cac", 8], ["CabcaABb", "cBBc", 12], ["CabcaBcB", "bAABcbCA", 12], ["Cabcb", "Ba", 8], ["Cabccb", "BBBA", 11], ["Cac", "A", 5], ["Cac", "BAcb", 5], ["Cac", "BCcCCac", 8], ["Cac", "Bc", 4], ["Cac", "CBaacCcCb", 12], ["Cac", "cC", 4], ["Cac", "cbACbC", 9], ["CacA", "BaBabBAA", 12], ["CacA", "CacbC", 4], ["CacAb", "acCAb", 4], ["CacAcAa", "ACbcC", 10], ["CacAcBc", "caABbAAAA", 13], ["CacBBBC", "B", 12], ["CacBCa", "CcbcBA", 7], ["CacBaCbca", "aaBaaaBc", 11], ["CacBbbB", "CaCbbaBbb", 7], ["CacBc", "bBbAC", 9], ["CacC", "AcC", 3], ["CacCbaAcc", "bCaAaCB", 13], ["CacaAc", "aAbC", 8], ["Cacab", "AaBCAa", 8], ["Cacab", "c", 8], ["CacacC", "AABCCAA", 12], ["Cacb", "ac", 4], ["CacbB", "BCbcBaa", 9], ["CacbC", "bBbCaBBB", 11], ["CacbC", "cCAAcCa", 9], ["CacbCAccB", "AAaBBcbBc", 14], ["Cacc", "AACc", 4], ["CaccAC", "BabBAaC", 8], ["CaccBca", "aCbb", 10], ["CaccaAbbC", "BBbC", 13], ["CaccaCCa", "cc", 12], ["Cb", "A", 4], ["Cb", "AACB", 5], ["Cb", "AACCbcBc", 12], ["Cb", "ABcAcaCcC", 16], ["Cb", "ACCBAaCaA", 15], ["Cb", "AaBCBCc", 11], ["Cb", "Aaa", 6], ["Cb", "AacabCcB", 13], ["Cb", "Ac", 4], ["Cb", "BABB", 7], ["Cb", "BBcAabC", 11], ["Cb", "Ba", 4], ["Cb", "BaaB", 7], ["Cb", "BbCaabCCC", 14], ["Cb", "CA", 2], ["Cb", "CCcBaAB", 11], ["Cb", "Cbb", 2], ["Cb", "CcBccBacC", 15], ["Cb", "aBcaCb", 8], ["Cb", "aCBCcAACA", 15], ["Cb", "aaAbc", 8], ["Cb", "aaCBBaB", 11], ["Cb", "aaaB", 7], ["Cb", "abBaC", 8], ["Cb", "abaaaCABA", 15], ["Cb", "acBbAAca", 13], ["Cb", "acbbabbCC", 15], ["Cb", "bcCCBA", 9], ["Cb", "c", 3], ["Cb", "cAbcbCA", 11], ["Cb", "cAcCbBA", 10], ["Cb", "cB", 2], ["Cb", "cBcB", 6], ["Cb", "caacb", 7], ["Cb", "cbCBBbacA", 14], ["Cb", "cbcbccA", 11], ["CbA", "AAC", 6], ["CbA", "BBCBaCC", 10], ["CbA", "BBcBB", 8], ["CbA", "baaA", 6], ["CbA", "c", 5], ["CbA", "caBAbA", 7], ["CbAABaAC", "A", 14], ["CbAACACcC", "ba", 15], ["CbAACa", "AaCaAccab", 11], ["CbAAaAB", "BCaBCcaaC", 12], ["CbAAb", "bacAabaAB", 11], ["CbAAcAb", "caC", 11], ["CbAAcC", "ABbcaCa", 10], ["CbABACcaB", "<PERSON>bb", 14], ["CbABC", "b", 8], ["CbABaBaAC", "acaC", 12], ["CbAC", "A", 6], ["CbAC", "c", 7], ["CbACaB", "aBcA", 9], ["CbACb", "bcABcCC", 10], ["CbACc", "BbabcaBCA", 13], ["CbACcABba", "baABAcBc", 12], ["CbAaAaaa", "ACbBACbbc", 13], ["CbAaabbC", "A", 14], ["CbAacB", "c", 10], ["CbAacBB", "CacbBBbBA", 12], ["CbAb", "baBac", 8], ["CbAbBaC", "aCAaACb", 11], ["CbAba", "CbaA", 4], ["CbAbb", "Bc", 9], ["CbAc", "a", 7], ["CbAcAC", "bcbCB", 8], ["CbAcB", "aac", 7], ["CbAcBCcaa", "ccABcacAc", 11], ["CbAcCb", "AC", 8], ["CbAcaAAA", "accCbbACC", 15], ["CbAcc", "AABbcBBc", 11], ["CbB", "ABcCCBb", 10], ["CbB", "B", 4], ["CbB", "BB", 3], ["CbB", "BBb", 4], ["CbB", "ababacC", 11], ["CbB", "bAaaABc", 12], ["CbB", "cCb", 4], ["CbBABACb", "BCcBAA", 10], ["CbBABBaA", "BacaabAc", 13], ["CbBAb", "cB", 7], ["CbBB", "CcBCBCcAC", 12], ["CbBBB", "BCCcb", 9], ["CbBBaC", "ab", 10], ["CbBBcCCBb", "B", 16], ["CbBCCbbB", "cC", 13], ["CbBa", "a", 6], ["CbBa", "aAb", 7], ["CbBabA", "CcCB", 9], ["CbBb", "aBccCBA", 11], ["CbBbC", "CCCbbC", 5], ["CbBbbba", "BaCCABCcC", 15], ["CbBbc", "C", 8], ["CbBc", "aAaAAbBA", 12], ["CbBcB", "b", 8], ["CbC", "AcacbAB", 11], ["CbC", "BCAc", 5], ["CbC", "Bc", 4], ["CbC", "CbB", 2], ["CbC", "b", 4], ["CbCAAbb", "bAAaC", 8], ["CbCABcaC", "aCcAACB", 12], ["CbCACA", "baAac", 8], ["CbCAa", "cCbabBb", 10], ["CbCB", "BAcbcAba", 11], ["CbCBBb", "cAbACcA", 11], ["CbCBC", "cacCccCb", 11], ["CbCBCAB", "bBBcC", 9], ["CbCBcCAaa", "aAAaAa", 14], ["CbCCCc", "BcBCaba", 10], ["CbCCbAccA", "cCbBB", 13], ["CbCCcCCaB", "bcaaab", 11], ["CbCa", "cBBaCacA", 10], ["CbCaAaB", "acaa", 9], ["CbCaB", "ACbaa", 6], ["CbCaB", "AcbAa", 7], ["CbCaBCb", "bcA", 10], ["CbCaa", "ABCBCcAb", 10], ["CbCaa", "ba", 6], ["CbCaaaC", "bAcBcBaAa", 12], ["CbCb", "Bbc", 5], ["CbCbaBa", "AbacBbabc", 10], ["CbCbcC", "Ca", 10], ["CbCc", "ACC", 5], ["CbCc", "<PERSON><PERSON><PERSON>", 6], ["CbCcAaBaA", "cbC", 13], ["CbCcAcAc", "AcBaCaAC", 11], ["CbCcBAb", "c", 12], ["CbCcBC", "CacBbCAc", 10], ["CbCcBac", "AAbAC", 11], ["Cba", "ABBBbaab", 12], ["Cba", "AcbAbB", 8], ["Cba", "C", 4], ["Cba", "CCCbbbC", 10], ["Cba", "CCaaAa", 8], ["Cba", "CcBCCAAAA", 14], ["Cba", "aABbaC", 8], ["CbaA", "CAbCBaB", 8], ["CbaA", "CBb", 5], ["CbaABAC", "aBCBCBc", 12], ["CbaAaBCaB", "AcCcB", 12], ["CbaAcBb", "C", 12], ["CbaAcc", "acaCaaccA", 10], ["CbaBA", "ab", 7], ["CbaBBcBaB", "bB", 14], ["CbaBa", "CBC", 6], ["CbaBb", "CCCbcB", 8], ["CbaBba", "BC", 10], ["CbaBcAcbC", "cc", 14], ["CbaC", "B", 7], ["CbaCA", "BAbaAacA", 9], ["CbaCABBb", "cAB", 11], ["CbaCABcbA", "BbaCbC", 10], ["CbaCAaCc", "a", 14], ["CbaCAcCba", "BCcaB", 12], ["CbaCbCA", "ccBBAcBc", 12], ["CbaaA", "BA", 7], ["CbaaBA", "AcccAAaac", 14], ["CbabAACaC", "cABaCcbB", 13], ["CbabACa", "A", 12], ["CbabAac", "aaaB", 9], ["CbabAcC", "cbb", 9], ["CbabBBBcc", "c", 16], ["CbabBCCab", "bB", 14], ["CbabC", "CacBB", 7], ["CbabbBbb", "BaAABCC", 11], ["Cbabc", "aACbC", 7], ["CbabcB", "CBcbAcb", 6], ["CbacAab", "c", 12], ["CbacCAB", "cCC", 10], ["CbacCCb", "caaC", 9], ["CbacCaCAB", "Cbbba", 12], ["Cbacaca", "BACAc", 8], ["Cbb", "Ababcb", 8], ["Cbb", "Bb", 3], ["Cbb", "CACbbBCb", 10], ["Cbb", "CBaABccaA", 14], ["Cbb", "abcb", 4], ["Cbb", "bACBBCCaa", 14], ["Cbb", "caCCACBaB", 14], ["Cbb", "cbAcABac", 12], ["CbbA", "cAACAA", 9], ["CbbAACbC", "ccaABC", 9], ["CbbABCCA", "CBCABCA", 5], ["CbbAb", "B", 9], ["CbbAbbac", "bCBbca", 10], ["CbbAcaCbC", "CAbBbcAc", 11], ["CbbB", "A", 8], ["CbbB", "C", 6], ["CbbBA", "BBCabAB", 10], ["CbbBAbcA", "CcCCAaBb", 12], ["CbbBAcCba", "BBbbCaBA", 11], ["CbbBaBcB", "cCabACB", 10], ["CbbCCacBc", "cbbAcacab", 8], ["CbbaAacC", "bcbcbbaBa", 13], ["CbbaCBAb", "aBCBBcC", 13], ["CbbbBb", "BA", 10], ["CbbbaA", "aacCccBbB", 15], ["CbbbaACba", "aCBba", 12], ["Cbbbab", "AABAbbc", 11], ["Cbbc", "bcaAb", 9], ["Cbbc", "cba", 5], ["CbbcBcAc", "BbC", 12], ["CbbcCBacB", "AaC", 15], ["Cbbcbb", "bBaBCBA", 10], ["CbbcbcabC", "AcBcBcba", 10], ["Cbbcc", "B", 9], ["Cbbcc", "CcbA", 6], ["CbbccBAaB", "cCA", 13], ["Cbbccc", "BcA", 9], ["Cbc", "A", 6], ["Cbc", "BAa", 6], ["Cbc", "BaabcacC", 12], ["Cbc", "CBbAc", 4], ["Cbc", "aCCbab", 8], ["Cbc", "baB", 6], ["Cbc", "bcB", 4], ["Cbc", "caACbaC", 9], ["Cbc", "cbabc", 5], ["CbcAB", "AabCABAb", 9], ["CbcAaaBa", "aACA", 12], ["CbcB", "CBcC", 3], ["CbcBCbB", "CAAacC", 11], ["CbcBb", "aaCACAAcC", 15], ["CbcBcb", "AabcC", 9], ["CbcBcbC", "aCa", 13], ["CbcCAAc", "aaBBbbc", 12], ["CbcCAB", "bbaCCcc", 9], ["CbcCB", "c", 8], ["CbcCBB", "aB", 10], ["CbcCCa", "BB", 11], ["CbcCcA", "aCaacCaAa", 10], ["Cbca", "cABAAaaA", 12], ["CbcaC", "bCcabCa", 8], ["Cbcb", "AA", 8], ["Cbcb", "AaCc", 7], ["Cbcb", "CbBAa", 6], ["CbcbA", "bccbACcC", 10], ["CbcbB", "aC", 9], ["CbcbBCBC", "cBB", 10], ["CbcbC", "a", 10], ["Cbcbb", "cccAB", 6], ["Cbcbcbc", "CbaccAcCC", 9], ["Cbcc", "BbAcAabc", 10], ["Cbcc", "bbBBA", 8], ["CbccCAcBA", "acCA", 12], ["CbccaACc", "AC", 12], ["Cc", "AAbABC", 11], ["Cc", "AAcbCAca", 12], ["Cc", "ABaC", 7], ["Cc", "AC", 3], ["Cc", "ACCcCa", 8], ["Cc", "AaCCC", 7], ["Cc", "BBCBCbbb", 13], ["Cc", "BbAbcC", 10], ["Cc", "Bc", 2], ["Cc", "Bccba", 7], ["Cc", "CABACbAcB", 14], ["Cc", "CAaCbCA", 11], ["Cc", "CCCCAaC", 11], ["Cc", "CCbCaAb", 11], ["Cc", "Ccb", 2], ["Cc", "a", 4], ["Cc", "aAABCBacb", 14], ["Cc", "aAAccbC", 11], ["Cc", "aBBCbc", 8], ["Cc", "aCAcaCca", 12], ["Cc", "bB", 4], ["Cc", "baABAABbC", 17], ["Cc", "bbacA", 8], ["Cc", "bc", 2], ["Cc", "c", 2], ["Cc", "cAaAAbc", 11], ["Cc", "cBbbBbcA", 13], ["Cc", "ca", 3], ["Cc", "ccAC", 5], ["Cc", "ccCb", 5], ["CcA", "BbcbaAB", 10], ["CcA", "aCA", 3], ["CcA", "bAAbca", 9], ["CcA", "bBccA", 5], ["CcA", "bCCBb", 7], ["CcA", "cBAbaC", 9], ["CcAA", "bcACbc", 8], ["CcAAAAC", "abCa", 12], ["CcAAAB", "bBAbaC", 9], ["CcAAAcCb", "cAcBcBBC", 11], ["CcAABaC", "BbC", 10], ["CcAABbc", "BCaAbab", 9], ["CcAACB", "b", 11], ["CcAAaC", "CAbaab", 7], ["CcAB", "BbcCBc", 8], ["CcAB", "accAaa", 7], ["CcABACACb", "abBBbCAA", 12], ["CcABBccA", "cc", 12], ["CcABb", "a", 9], ["CcABbabBA", "cCA", 14], ["CcABcAaa", "Ba", 12], ["CcACAbc", "AbBB", 11], ["CcACbaCA", "abbBaBbBa", 16], ["CcACcCAa", "AaB", 13], ["CcAa", "aAbBCCc", 13], ["CcAa", "ccBC", 5], ["CcAaA", "cbcC", 8], ["CcAaB", "babCBbCAC", 15], ["CcAaCABA", "cbaBaCCCb", 12], ["CcAaCcbcB", "aBbaCAAC", 13], ["CcAac", "Cabaac", 5], ["CcAacBAaC", "bCACB", 12], ["CcAb", "b", 6], ["CcAbBcBaa", "CBc", 12], ["CcAbaaA", "cCa", 10], ["CcAbcaB", "bcA", 9], ["CcAc", "AAaaABABA", 16], ["CcAcAB", "aabAabCcb", 14], ["CcAcCcC", "c<PERSON><PERSON><PERSON>", 10], ["CcAcCcc", "A", 12], ["CcB", "AbcBBCAac", 14], ["CcB", "Ac", 4], ["CcB", "Ccb", 1], ["CcB", "ba", 6], ["CcB", "cab", 4], ["CcB", "cbc", 5], ["CcBABc", "cAAC", 7], ["CcBACb", "ac", 10], ["CcBAaAa", "aCCcB", 12], ["CcBB", "bab", 7], ["CcBBA", "B", 8], ["CcBBAb", "aBCAAc", 10], ["CcBBCAAC", "A", 14], ["CcBBbac", "Bb", 10], ["CcBCBaa", "bbbbB", 11], ["CcBCCaAbC", "aAaCaCA", 14], ["CcBCCbABb", "BbBCbccaC", 14], ["CcBCaacCB", "CAbCcbA", 11], ["CcBCc", "C", 8], ["CcBCc", "cBcba", 7], ["CcBCccbb", "abaAAAa", 15], ["CcBaBb", "BAabCcBcB", 12], ["CcBaa", "abBac", 6], ["CcBabaabc", "bCB", 15], ["CcBacBC", "aa", 12], ["CcBb", "AAB", 6], ["CcBb", "BaAcbA", 9], ["CcBbC", "A", 10], ["CcBbaCbb", "abcBCCBab", 11], ["CcBbbaB", "baCb", 11], ["CcBc", "aBabCbbcB", 13], ["CcBc", "cabcACa", 10], ["CcBcABAC", "AAC", 10], ["CcBcBCBaa", "CBA", 13], ["CcC", "CBAacacBa", 13], ["CcC", "CBbcac", 7], ["CcCAA", "cB", 8], ["CcCABC", "ACA", 8], ["CcCACCb", "CbAbCccC", 11], ["CcCAaCAaB", "ABbAAaBb", 12], ["CcCAbB", "abb", 8], ["CcCAbCBc", "cbaCBaB", 11], ["CcCB", "ABbCBAc", 10], ["CcCB", "bcaabaccc", 14], ["CcCB", "cbBaC", 8], ["CcCBA", "CbAbcb", 9], ["CcCBCcCA", "BbCcaa", 10], ["CcCBb", "aBBC", 8], ["CcCBc", "BCBb", 6], ["CcCC", "b", 8], ["CcCCAB", "CCbB", 6], ["CcCCBACCa", "ABAABAAAb", 14], ["CcCCacbB", "aACA", 13], ["CcCaA", "baa", 7], ["CcCaaaaA", "bCbbACC", 13], ["CcCacAB", "BCCBacCcb", 10], ["CcCacbC", "ACBabB", 9], ["CcCbB", "BABCbb", 7], ["CcCbCA", "aBcA", 8], ["CcCbCbBbc", "ccabB", 11], ["CcCba", "BCb", 6], ["CcCbaAbB", "aBbcaCB", 11], ["CcCbcaC", "CcBbaCbCb", 9], ["CcCc", "cBAACa", 9], ["CcCcAB", "aCBbA", 9], ["CcCcAc", "baBacaB", 11], ["CcCcBB", "bcbBbcAbB", 11], ["CcCcCCAb", "BCBBaABab", 13], ["CcCca", "bCaBAB", 10], ["CcCcaB", "aAacCCb", 10], ["CcCcbCb", "BBBCBaCC", 12], ["CcCcbCb", "cC", 10], ["CcCccCbbc", "cabBaCB", 14], ["Cca", "AbbaBABCA", 16], ["Cca", "AcacBcc", 10], ["Cca", "a", 4], ["Cca", "b", 6], ["Cca", "bAbaCBB", 12], ["CcaAB", "B", 8], ["CcaABA", "a", 10], ["CcaAC", "BaCbAbaAa", 12], ["CcaACB", "BcBbAAA", 10], ["CcaACCA", "bCacCB", 8], ["CcaAa", "cCC", 8], ["CcaAbCCC", "AbCBCb", 10], ["CcaAcb", "bcBAa", 8], ["CcaAcc", "babAAAcbB", 13], ["CcaB", "BbaCbBbCB", 14], ["CcaBA", "ACCCABCBC", 12], ["CcaBaBC", "bacAbAab", 11], ["CcaBaa", "A", 11], ["CcaBcBAB", "Ac", 13], ["CcaBcC", "A", 11], ["CcaCAaabB", "CABCacccB", 11], ["CcaCCAAA", "BCAaCCcc", 10], ["CcaCa", "BAa", 7], ["CcaCabAcb", "BaaaBAC", 10], ["CcaCacbba", "bccAB", 13], ["CcaCbbA", "CcB", 9], ["CcaCcBc", "cbaCa", 9], ["Ccaa", "AabAcaC", 10], ["CcaaA", "aaCAA", 7], ["CcaaAAbc", "aABCaCacc", 13], ["CcaaBC", "abBCaC", 10], ["Ccaaa", "cc", 7], ["CcaaaAB", "bc", 12], ["Ccaaaa", "ABbbbb", 12], ["Ccaaabccc", "cA", 15], ["CcaabbCCA", "Bbc", 14], ["Ccab", "AC", 7], ["Ccab", "Cba", 4], ["Ccab", "abBAaBB", 11], ["Ccab", "abbBCaBb", 11], ["CcabACcb", "cCAaC", 10], ["Ccac", "cbCCA", 8], ["CcacB", "cCBbaCB", 7], ["CcacBabBC", "CaBBaB", 8], ["CcacC", "AbbacBa", 10], ["CcacCABB", "ba", 14], ["CcacCaA", "CaAC", 8], ["CcacaBB", "B", 12], ["CcacbC", "cCb", 7], ["Ccb", "AcbABaCC", 12], ["Ccb", "CAA", 4], ["Ccb", "CbAcC", 6], ["Ccb", "c", 4], ["CcbA", "BccAc", 6], ["CcbA", "aabCBb", 10], ["CcbA", "bABb", 7], ["CcbAACA", "bcc", 11], ["CcbAACB", "acBBAcac", 10], ["CcbAAaA", "ccBCaCa", 8], ["CcbACBA", "A", 12], ["CcbACC", "bbaC", 7], ["CcbAaBcAA", "bAcac", 11], ["CcbAc", "AcbbBBBa", 12], ["CcbBAbCcC", "AAb", 14], ["CcbBaCC", "ABcA", 11], ["CcbBac", "BCAAA", 10], ["CcbBbBBA", "AbB", 12], ["CcbBbbbbb", "bb", 14], ["CcbBc", "cBAcacB", 11], ["CcbBcAC", "BbCcAbcbB", 11], ["CcbBcaA", "bbcca", 8], ["CcbBcc", "bbcc", 5], ["CcbCA", "a", 9], ["CcbCA", "aabAA", 6], ["CcbCbB", "CACab", 7], ["CcbCbCA", "BcaCaCAb", 8], ["CcbCc", "AC", 8], ["CcbCc", "cBbbc", 5], ["CcbCcC", "c", 10], ["CcbaccBCb", "bbCBA", 13], ["CcbbABb", "CbCbacB", 8], ["CcbbaAbbB", "bc", 16], ["Ccbbb", "CBAcACB", 9], ["CcbbbC", "cBacabCb", 11], ["CcbbbCbc", "BbCAaBaB", 15], ["CcbbcABBa", "BcAcCcBCB", 13], ["Ccbc", "ABAaaBAb", 15], ["Ccc", "B", 6], ["Ccc", "BABa", 8], ["Ccc", "BcaBAc", 8], ["Ccc", "CBB", 4], ["Ccc", "CC", 3], ["Ccc", "aaaaCC", 10], ["Ccc", "ac", 4], ["Ccc", "bb", 6], ["CccABA", "AAbBcA", 10], ["CccACCB", "AcbaC", 9], ["CccAaB", "baCaBcCa", 12], ["CccAb", "aABBc", 10], ["CccAcbbca", "c", 16], ["CccBBBcB", "bBbbCa", 11], ["CccBBbC", "Bba", 10], ["CccBa", "B", 8], ["CccBa", "caBA", 5], ["CccBaaAB", "C", 14], ["CccBbBCb", "a", 16], ["CccBbca", "BacAbCBbC", 12], ["CccCAa", "CcBcBCCc", 8], ["CccCCa", "Ba", 10], ["CccCa", "aA", 9], ["CccCb", "baA", 10], ["Ccca", "A", 7], ["CccaABbBb", "aAcB", 12], ["CccaBBCb", "BBbaaCCC", 12], ["Cccaa", "BbbC", 10], ["CccacCBb", "ccBa", 10], ["CccbABBbb", "B", 16], ["Cccbbc", "CCcBcBa", 7], ["Cccc", "BaCbb", 9], ["CcccAB", "aCBAB", 7], ["CcccAbbA", "BCBcBc", 12], ["CcccBacc", "BCAcB", 11], ["CcccBcCB", "cCAb", 12], ["a", "AA", 3], ["a", "AABaCbca", 14], ["a", "AACaBBAC", 14], ["a", "ABCaa", 8], ["a", "ABaaBCCb", 14], ["a", "ABab", 6], ["a", "ABcB", 7], ["a", "ACbcCBCB", 15], ["a", "ACbcaA", 10], ["a", "AaAA", 6], ["a", "AaABacaAA", 16], ["a", "AaAbBca", 12], ["a", "<PERSON><PERSON><PERSON><PERSON><PERSON>", 14], ["a", "AabCbCb", 12], ["a", "AaccB", 8], ["a", "Ab", 3], ["a", "AbBBaB", 10], ["a", "AbCb", 7], ["a", "AbCc", 7], ["a", "AbbbBacAa", 16], ["a", "AbcCaCbac", 16], ["a", "AcABaA", 10], ["a", "AcACCA", 11], ["a", "AcCbc", 9], ["a", "AcaB", 6], ["a", "B", 2], ["a", "BA", 3], ["a", "BABAAcBBc", 17], ["a", "BABaBa", 10], ["a", "BAC", 5], ["a", "BAaAbAc", 12], ["a", "BAabccCBc", 16], ["a", "BAbaaa", 10], ["a", "BAbb", 7], ["a", "BBAcbBB", 13], ["a", "BBBaCab", 12], ["a", "BBC", 6], ["a", "BBaCbBAC", 14], ["a", "BBcca", 8], ["a", "BCA", 5], ["a", "BCBab", 8], ["a", "BaAAAcAbc", 16], ["a", "BaAcABBC", 14], ["a", "BaBcaC", 10], ["a", "BaC", 4], ["a", "BaabBAabb", 16], ["a", "BbCaCa", 10], ["a", "BbcACCA", 13], ["a", "Bbcba", 8], ["a", "BcAb", 7], ["a", "BcBABC", 11], ["a", "BcBaAacC", 14], ["a", "Bca", 4], ["a", "Bcabba", 10], ["a", "BccCbbAb", 15], ["a", "C", 2], ["a", "CA", 3], ["a", "CABAcBbc", 15], ["a", "CABCB", 9], ["a", "CAbC", 7], ["a", "CAba", 6], ["a", "CAcCAa", 10], ["a", "CAcc", 7], ["a", "CBA", 5], ["a", "CBABB", 9], ["a", "CBAa", 6], ["a", "CBBCcB", 12], ["a", "CBBcA", 9], ["a", "CBaCAC", 10], ["a", "CBbaCCbC", 14], ["a", "CCBc", 8], ["a", "CCcaC", 8], ["a", "Ca", 2], ["a", "CaAaC", 8], ["a", "CaBacbb", 12], ["a", "CaabacC", 12], ["a", "CbBc", 8], ["a", "CbbcAB", 11], ["a", "CcBBaaCc", 14], ["a", "CcBbabBAc", 16], ["a", "CcaACcB", 12], ["a", "Ccca", 6], ["a", "a", 0], ["a", "aA", 2], ["a", "aAACAbCC", 14], ["a", "aACABcAb", 14], ["a", "aAbaB", 8], ["a", "aBB", 4], ["a", "aBBCb", 8], ["a", "aBaCbb", 10], ["a", "aBb", 4], ["a", "aC", 2], ["a", "aCCCCcab", 14], ["a", "aCaBBABa", 14], ["a", "aCaC", 6], ["a", "aCb", 4], ["a", "aCcBaAa", 12], ["a", "aa", 2], ["a", "aaBbC", 8], ["a", "aaCA", 6], ["a", "ab", 2], ["a", "abACBBAcC", 16], ["a", "abBbBba", 12], ["a", "abCaAaCbC", 16], ["a", "aba", 4], ["a", "abcac", 8], ["a", "acAcBAbaB", 16], ["a", "acBcAA", 10], ["a", "accCCCc", 12], ["a", "b", 2], ["a", "bA", 3], ["a", "bAAaB", 8], ["a", "bABBa", 8], ["a", "bACca", 8], ["a", "bAbAccb", 13], ["a", "bAccbcaAB", 16], ["a", "bBAca", 8], ["a", "bBBC", 8], ["a", "bBBCaccC", 14], ["a", "bBCb", 8], ["a", "bBbC", 8], ["a", "bBbCABa", 12], ["a", "bC", 4], ["a", "bCBacAcC", 14], ["a", "bCC", 6], ["a", "bCCbaccA", 14], ["a", "bCbA", 7], ["a", "bCbBab", 10], ["a", "bCcaCbc", 12], ["a", "bCcaaCbB", 14], ["a", "ba", 2], ["a", "baa", 4], ["a", "baacacaba", 16], ["a", "babcA", 8], ["a", "babcCAA", 12], ["a", "bb", 4], ["a", "bbAc", 7], ["a", "bbB", 6], ["a", "bbBA", 7], ["a", "bbBBcaBc", 14], ["a", "bbBbAaAAc", 16], ["a", "bbCCaACb", 14], ["a", "bbCaca", 10], ["a", "bbaC", 6], ["a", "bbbAcAB", 13], ["a", "bbbBAABC", 15], ["a", "bbcCBcB", 14], ["a", "bcAca", 8], ["a", "bcAcacCB", 14], ["a", "bcaAcBAbC", 16], ["a", "bcb", 6], ["a", "bcc", 6], ["a", "c", 2], ["a", "cAC", 5], ["a", "cACBACca", 14], ["a", "cACaCa", 10], ["a", "cAa", 4], ["a", "cBAaa", 8], ["a", "cBbBbab", 12], ["a", "cC", 4], ["a", "cCBA", 7], ["a", "cCCa", 6], ["a", "cCCbaa", 10], ["a", "cCaCcb", 10], ["a", "cCacac", 10], ["a", "cCcBbCBAa", 16], ["a", "cabbcaBBc", 16], ["a", "cb", 4], ["a", "cbAcAab", 12], ["a", "cbAcCB", 11], ["a", "cbBaC", 8], ["a", "cbCcbC", 12], ["a", "cbaCbbBb", 14], ["a", "cbbAaCA", 12], ["a", "ccACCaBAa", 16], ["a", "ccACbaC", 12], ["a", "ccAbAC", 11], ["a", "ccAbbaBA", 14], ["a", "ccCBaCcB", 14], ["a", "ccac", 6], ["aA", "AAc", 3], ["aA", "ABcACbCcb", 15], ["aA", "ACcbCBA", 11], ["aA", "AacACCa", 10], ["aA", "B", 4], ["aA", "BabBBcCbC", 16], ["aA", "<PERSON>bb", 6], ["aA", "BcAcB", 8], ["aA", "CAacB", 8], ["aA", "CBABBaAB", 12], ["aA", "CaBbbbA", 10], ["aA", "CcAabB", 10], ["aA", "CcaAb", 6], ["aA", "a", 2], ["aA", "aCAcAb", 8], ["aA", "aCBb", 6], ["aA", "aCacaAA", 10], ["aA", "aaACBacA", 12], ["aA", "aaC", 3], ["aA", "abbAaBC", 10], ["aA", "abbBBbc", 12], ["aA", "bAc", 4], ["aA", "bB", 4], ["aA", "bBCBC", 10], ["aA", "baCBAbCa", 12], ["aA", "cABBaA", 8], ["aA", "cBc", 6], ["aA", "cabbabbA", 12], ["aA", "ccbcB", 10], ["aAA", "aACbBCCb", 12], ["aAA", "ac", 4], ["aAA", "bAc", 4], ["aAAA", "AaAC", 4], ["aAAAA", "c", 10], ["aAAAAC", "CbaaCCBBa", 14], ["aAAAAaAA", "cbAabBb", 13], ["aAAABb", "BABBaB", 9], ["aAAAC", "bC", 8], ["aAAAaAb", "AAC", 10], ["aAAAabBca", "AB", 14], ["aAAAcCbCB", "CcBbABBB", 15], ["aAAB", "A", 6], ["aAAB", "bcabAb", 7], ["aAAB", "c", 8], ["aAAB", "cCAB", 4], ["aAABCcCcC", "CbBBCAB", 14], ["aAABbBACb", "aACb", 10], ["aAACC", "ccbbaBA", 13], ["aAACaCa", "ACbabAc", 11], ["aAAaAAaa", "ACBcbbcaA", 14], ["aAAaaaB", "BABbaaAbA", 10], ["aAAabaAbA", "bcCBbaBB", 13], ["aAAacaaC", "a", 14], ["aAAb", "aCAA", 4], ["aAAbBbaA", "BaaaAbb", 11], ["aAAbCbCcC", "cBC", 14], ["aAAbbA", "bcbbaBa", 10], ["aAAc", "AccBbB", 10], ["aAAcA", "BBBABa", 9], ["aAAcA", "bb", 10], ["aAAcAA", "aCAA", 5], ["aAAcAa", "CbCAa", 7], ["aAAcAaa", "bAAACb", 8], ["aAAcB", "AbcACA", 8], ["aAAcBBba", "BcaAcACb", 11], ["aAAcCCbCB", "C", 16], ["aAAcCaCc", "caACAaAa", 10], ["aAAcCb", "bbAbA", 10], ["aAAcabaab", "cAACAbb", 8], ["aAAcbACab", "bCcAaBbCC", 15], ["aAAcc", "aaAC", 4], ["aAB", "AAaAcCaa", 12], ["aAB", "AbCBB", 7], ["aAB", "AcaAB", 4], ["aAB", "CAbaA", 7], ["aAB", "CCBAcbaCC", 15], ["aAB", "aBcbbB", 8], ["aAB", "aCCcaBCC", 11], ["aAB", "bcbccABCA", 14], ["aABA", "cCac", 8], ["aABAB", "AaaBBcCA", 11], ["aABACAa", "CcBBbA", 10], ["aABAaA", "C", 12], ["aABAb", "BbBCC", 8], ["aABAb", "ca", 9], ["aABAbab", "a", 12], ["aABAc", "AabAabaB", 10], ["aABAcCbbA", "aB", 14], ["aABB", "CCAaaccbA", 14], ["aABB", "aCac", 6], ["aABB", "cb", 7], ["aABBBBCc", "cBAcbb", 13], ["aABBBBa", "CaBaCCBB", 11], ["aABBCc", "cc", 9], ["aABBacCA", "BBbacBc", 9], ["aABBbA", "aCBbC", 6], ["aABBbaB", "CbbBCbacb", 10], ["aABCAa", "bBbC", 9], ["aABCB", "cAB", 6], ["aABCCcbB", "CBbaCcbc", 9], ["aABCaca", "Bb", 12], ["aABCbCba", "CabccBaB", 11], ["aABaBA", "BC", 10], ["aABaBbAAA", "B", 16], ["aABb", "BC", 6], ["aABbAa", "bCbBcC", 10], ["aABbBbCca", "ABCCBAbAc", 12], ["aABbaA", "bcBC", 10], ["aABbac", "AbBbABAAb", 12], ["aABbccca", "BcaA", 11], ["aABcACB", "A", 12], ["aABccB", "A", 10], ["aAC", "BA", 4], ["aAC", "CAbbaab", 11], ["aAC", "CbC", 4], ["aAC", "b", 6], ["aAC", "bACCbC", 8], ["aAC", "bcaBacc", 10], ["aACABAcCa", "ccCB", 13], ["aACBA", "Aa", 7], ["aACBACA", "BaCBa", 8], ["aACBBcBC", "A", 14], ["aACCA", "Cb", 8], ["aACCAAb", "Bca", 12], ["aACCBc", "AcbAaCB", 9], ["aACCa", "ACccABA", 10], ["aACCbAA", "AAaa", 9], ["aACCc", "BBcc", 7], ["aACCc", "cbaBAC", 10], ["aACaaA", "bcACaCbA", 8], ["aACacACa", "aaBccaB", 10], ["aACacCb", "C", 12], ["aACaccCAC", "cC", 14], ["aACbAC", "AB", 9], ["aACbBBcb", "b", 14], ["aACbbbaBC", "aBBACCB", 13], ["aACcaBb", "ABabA", 9], ["aACcbCa", "b", 12], ["aAa", "BbBb", 8], ["aAa", "CBCBAabb", 12], ["aAa", "Cc", 6], ["aAa", "bCaaCBcAa", 12], ["aAa", "bc", 6], ["aAaA", "b", 8], ["aAaAcc", "B", 12], ["aAaAccAb", "BCAbabcAA", 11], ["aAaBBCb", "Cbbc", 11], ["aAaBaBBBc", "cAcCCCA", 16], ["aAaBaBa", "bBbBc", 10], ["aAaCB", "cCc", 8], ["aAaCCCc", "bbcBabaAA", 16], ["aAaCaaAc", "BCaA", 10], ["aAaa", "bb", 8], ["aAaaAcCA", "cBbcccB", 13], ["aAaaBbbb", "bCca", 14], ["aAaaC", "aCAC", 5], ["aAaaCBa", "aaCBCc", 8], ["aAaaaA", "aBACbaBAC", 10], ["aAaabBcb", "AcCAcBA", 12], ["aAab", "C", 8], ["aAab", "bCaB", 5], ["aAab", "bbaacCCB", 12], ["aAabC", "ccbBa", 9], ["aAabCAc", "aBCb", 9], ["aAabCCBaa", "bBC", 14], ["aAabbbbB", "CBAACBCCb", 13], ["aAabbcCaA", "A", 16], ["aAac", "cCBbCAaBB", 14], ["aAacB", "cAcbcBa", 8], ["aAacBcCBa", "cba", 13], ["aAb", "A", 4], ["aAb", "a", 4], ["aAb", "b", 4], ["aAbA", "bccaaaCAB", 13], ["aAbABAA", "bCBAcabBb", 14], ["aAbAC", "cACcCa", 8], ["aAbACaBAA", "CB", 14], ["aAbAb", "cBBabc", 8], ["aAbAcCb", "c", 12], ["aAbBB", "AC", 8], ["aAbBaBcAA", "C", 17], ["aAbCAcbCC", "BAAaA", 14], ["aAbCBA", "aCBaCABc", 9], ["aAbCCACCb", "AbcacbAb", 11], ["aAbCCC", "Aca", 9], ["aAbCa", "bABc", 6], ["aAbCbCa", "AabbBCbA", 8], ["aAbCbbaba", "CcBbccaB", 14], ["aAbCc", "cCa", 8], ["aAba", "BbcbcCBcC", 16], ["aAbaBA", "aBAAAA", 7], ["aAbaBBba", "Cb", 14], ["aAbaCAA", "cAB", 11], ["aAbaaBA", "bcCbaccBA", 10], ["aAbb", "bBBaACCBC", 13], ["aAbbABabC", "cbACbB", 12], ["aAbbBab", "caaA", 11], ["aAbbCAA", "BBbBcbA", 8], ["aAbbCbCCa", "AAB", 14], ["aAbbCbcb", "CBc", 11], ["aAbbaB", "a", 10], ["aAbbacAb", "BccBb", 11], ["aAbbcbA", "A", 12], ["aAbcCaA", "CCc", 11], ["aAbcaABCA", "BCAcbCab", 13], ["aAbcaAa", "CCababB", 11], ["aAbcbbB", "baba", 10], ["aAc", "aBbBCbac", 11], ["aAc", "abbAbaC", 9], ["aAcA", "A", 6], ["aAcA", "ACABBCB", 10], ["aAcAAb", "ACC", 9], ["aAcAAbB", "cAbbBaCBb", 13], ["aAcAabacA", "aCbBcA", 9], ["aAcAacaba", "caACb", 11], ["aAcAbaCac", "bBc", 14], ["aAcAcAbaB", "bcCCa", 13], ["aAcB", "aaaCC", 6], ["aAcB", "caC", 6], ["aAcBACBB", "a", 14], ["aAcBC", "aCca", 6], ["aAcBbABB", "AA", 12], ["aAcBbAaBC", "BbCbaCcaC", 14], ["aAcCAaC", "b", 14], ["aAcCCcAc", "baabCBAaA", 12], ["aAcCccBB", "AaAa", 14], ["aAcaAaB", "AcC", 10], ["aAcaBaA", "ACcaaA", 5], ["aAcaaac", "BaABaBaBA", 10], ["aAcb", "AABbA", 5], ["aAcb", "aABC", 4], ["aAcbA", "cbcAAC", 8], ["aAcbBb", "CbcccCBcC", 14], ["aAcbBcca", "A", 14], ["aAcba", "acBcBBBAc", 12], ["aAcba", "b", 8], ["aAcc", "CACC", 4], ["aAccac", "ACABCBBAC", 12], ["aAccbCaba", "BCBbBc", 14], ["aB", "A", 3], ["aB", "AA", 3], ["aB", "AB", 1], ["aB", "ABAb", 5], ["aB", "ACcb", 6], ["aB", "Aabaaa", 9], ["aB", "AccabABB", 12], ["aB", "B", 2], ["aB", "BBcbB", 8], ["aB", "BCc", 6], ["aB", "Bca", 6], ["aB", "C", 4], ["aB", "CAAaa", 8], ["aB", "CbccACC", 13], ["aB", "Cc", 4], ["aB", "CcAcCC", 11], ["aB", "aCbBBc", 8], ["aB", "aaAbCb", 9], ["aB", "aaaAb", 7], ["aB", "aabCACc", 11], ["aB", "abAC", 5], ["aB", "bB", 2], ["aB", "bBB", 4], ["aB", "bb", 3], ["aB", "bbbca", 9], ["aB", "bbc", 5], ["aB", "bc", 4], ["aB", "bcb", 5], ["aB", "cB", 2], ["aB", "cCC", 6], ["aB", "cc", 4], ["aBA", "BAaAaaCBc", 14], ["aBAAA", "CBCAbbcAA", 10], ["aBAAB", "BacBABcAc", 10], ["aBAAaB", "caAc", 9], ["aBAAacaa", "acabBBcBC", 13], ["aBAAbCcB", "BCB", 10], ["aBAAcbCc", "cBCBBcAAB", 14], ["aBAB", "aaCc", 6], ["aBAB", "bbaAbA", 8], ["aBABa", "bacAa", 6], ["aBABaABc", "AAaCCC", 10], ["aBABcaABA", "CBcbB", 12], ["aBACC", "a", 8], ["aBACa", "c", 9], ["aBACba", "C", 10], ["aBACcAA", "B", 12], ["aBAa", "aaB", 5], ["aBAaC", "cCACbbBb", 14], ["aBAabaAB", "cacCbb", 13], ["aBAabba", "AaBABbCBc", 9], ["aBAacCbbb", "aBaCaAc", 10], ["aBAacc", "aAaAAcABC", 10], ["aBAb", "aBBbAac", 8], ["aBAbAC", "aABAbCAc", 5], ["aBAbaaab", "CCbbAaba", 11], ["aBAbbB", "aCacbAB", 7], ["aBAbbcbb", "bcBAb", 11], ["aBAcABbb", "Ca", 14], ["aBAcBa", "CbBcCaa", 9], ["aBAcBbC", "aaAaBac", 7], ["aBAca", "AACACCcAa", 11], ["aBAcabBA", "BBACb", 9], ["aBAcba", "bBaaCcB", 10], ["aBAccCaba", "CAcabBBbC", 14], ["aBAcca", "ABAABbCa", 8], ["aBB", "AaAbaAaBb", 13], ["aBB", "AaCaB", 6], ["aBB", "Acbc", 6], ["aBB", "aC", 4], ["aBB", "aa", 4], ["aBB", "bbCAAaBBB", 12], ["aBBA", "BAAc", 6], ["aBBABAaAA", "AbCAcbAA", 10], ["aBBABaaaa", "bCC", 17], ["aBBABca", "bA", 11], ["aBBACC", "caaCB", 9], ["aBBAc", "aCAA", 6], ["aBBB", "AcBcBa", 7], ["aBBBA", "CcbaAB", 9], ["aBBBABAB", "BBA", 10], ["aBBBACB", "Aa", 12], ["aBBBAaA", "bB", 11], ["aBBBB", "AaBb", 6], ["aBBBCCBbb", "BCBca", 12], ["aBBBaBaAa", "cCCB", 16], ["aBBC", "AbabA", 7], ["aBBC", "CcbcBBCb", 10], ["aBBC", "cCbbBa", 9], ["aBBCBac", "AbacacaCC", 12], ["aBBCBbab", "bb", 12], ["aBBCaCCAB", "ccBAcA", 12], ["aBBCbAb", "cACACb", 10], ["aBBCbB", "aCabbA", 8], ["aBBCcabCb", "CCacAacc", 14], ["aBBaB", "BCaccaa", 10], ["aBBaB", "bACCbA", 11], ["aBBaCCab", "CBcAbcC", 12], ["aBBaa", "a", 8], ["aBBabAb", "bACbcAa", 11], ["aBBac", "BbAa", 6], ["aBBacABac", "CbCbc", 13], ["aBBb", "BCAaBa", 9], ["aBBbA", "aaa", 7], ["aBBbB", "bbBaba", 7], ["aBBbba", "bCCcacbB", 14], ["aBBc", "BccbaCCac", 14], ["aBBcABB", "BBcAbCBAB", 8], ["aBBcBAab", "CcCBBA", 12], ["aBBcBBA", "cC", 12], ["aBBcBcb", "BBBca", 6], ["aBBcaB", "AbBAa", 6], ["aBBccB", "abcac", 7], ["aBBcca", "bABb", 10], ["aBBcccbCB", "bCcab", 12], ["aBC", "AABCBaAAC", 13], ["aBC", "CcaCAB", 10], ["aBC", "aACCc", 6], ["aBC", "aaaCcCcB", 12], ["aBC", "bABBAa", 9], ["aBC", "bC", 3], ["aBC", "baCCa", 6], ["aBC", "bc", 4], ["aBCA", "AbbcbAb", 9], ["aBCA", "CBaAa", 6], ["aBCAa", "BbCCAbAAb", 12], ["aBCAcBAa", "BC", 12], ["aBCAcBCAC", "AABaCbBcA", 12], ["aBCAccBCB", "b", 17], ["aBCBA", "b", 9], ["aBCBBCa", "aCCAaa", 8], ["aBCBC", "bCccB", 8], ["aBCBC", "bbB", 7], ["aBCBabb", "a", 12], ["aBCCcc", "caAAc", 10], ["aBCaB", "cB", 7], ["aBCaCa", "aCA", 7], ["aBCaCbC", "BcaaBaABA", 13], ["aBCab", "cAacAcCaA", 12], ["aBCababA", "ccbABc", 11], ["aBCac", "CbCBbaAB", 11], ["aBCb", "acabaC", 8], ["aBCbb", "C", 8], ["aBCbbB", "bCcBb", 7], ["aBCbcCBc", "cBaCAc", 10], ["aBCc", "bABcbCa", 9], ["aBCcAc", "cCAaaAc", 10], ["aBCcAcAC", "BCcBaca", 8], ["aBCcCBaa", "b", 15], ["aBCcCBbcC", "ba", 16], ["aBCcaBAaC", "bBbcaCC", 10], ["aBa", "cBCbb", 8], ["aBa", "caB", 4], ["aBaA", "caBAC", 5], ["aBaAaAcC", "CAbBbbabc", 13], ["aBaAcCBBA", "AcAcc", 12], ["aBaAcCC", "Bc", 10], ["aBaB", "CBBbc", 7], ["aBaB", "bBAaA", 6], ["aBaBC", "CBbcaaBA", 10], ["aBaBbA", "CCabBBAaC", 11], ["aBaBbCcc", "aB", 12], ["aBaBc", "CcCAb", 10], ["aBaC", "A", 7], ["aBaC", "aa", 4], ["aBaC", "accAbAC", 8], ["aBaCCAb", "cbBC", 11], ["aBaCaA", "Aa", 9], ["aBaCaC", "CbaCccAc", 9], ["aBaCaabAc", "bCcACb", 13], ["aBaCba", "baB", 8], ["aBaa", "CBaBAABbB", 12], ["aBaa", "Cb", 7], ["aBaaA", "aAAaABCB", 9], ["aBaabC", "bCBbAabAC", 9], ["aBaabCba", "bCCaB", 12], ["aBaabaB", "AA", 12], ["aBaacBbca", "cCc", 14], ["aBabACAA", "acABb", 12], ["aBabBcb", "bAbA", 10], ["aBabbaBbc", "cbaccAb", 12], ["aBac", "c", 6], ["aBacAAcA", "aaCB", 11], ["aBacBB", "cb", 9], ["aBacC", "ACCCcAb", 11], ["aBacCB", "abccAac", 9], ["aBaccCAa", "aBcacaaCb", 10], ["aBb", "BCBAabbac", 13], ["aBb", "Ccb", 4], ["aBb", "aaac", 6], ["aBb", "bbcbbbAaC", 15], ["aBb", "cbCaBb", 6], ["aBb", "cbaB", 6], ["aBbA", "bbaaAbab", 11], ["aBbACC", "cCcccBCC", 12], ["aBbAcCBCA", "cCBab", 12], ["aBbB", "BacA", 8], ["aBbB", "aCcCb", 7], ["aBbBA", "aAcC", 8], ["aBbBB", "aBCc", 6], ["aBbBCbBAa", "Ac", 16], ["aBbBa", "bAa", 6], ["aBbBbAbC", "Abc", 11], ["aBbBbaBA", "AbbBCccC", 10], ["aBbBbcBcb", "abaBAAAAB", 12], ["aBbC", "bCbBAcBc", 12], ["aBbCABBA", "Caa", 12], ["aBbCACaCb", "A", 16], ["aBbCBCaBC", "aCcc", 12], ["aBbCaa", "cbAC", 9], ["aBbCbbbBA", "aA", 14], ["aBbCbcCAb", "BcAC", 12], ["aBbCc", "AcACCCba", 12], ["aBba", "BcAcCBB", 12], ["aBbaAACBA", "BacacbaC", 12], ["aBbaAB", "bbCc", 9], ["aBbaBcC", "c", 12], ["aBbaCcbC", "bbbbcab", 10], ["aBbaaCcaA", "bBABBAbCc", 13], ["aBbabCc", "CaCB", 10], ["aBbb", "Bb", 4], ["aBbb", "cccAbB", 9], ["aBbbA", "AAC", 9], ["aBbbAB", "C", 12], ["aBbbCab", "AaCBacaba", 10], ["aBbbbBA", "BcbbBBA", 5], ["aBbcCBaCc", "C", 16], ["aBbcCba", "BbAcaBac", 9], ["aBc", "A", 5], ["aBc", "Bac", 4], ["aBc", "bCCc", 6], ["aBc", "baC", 5], ["aBcAA", "ABc", 5], ["aBcAABabC", "cab", 12], ["aBcAaAccc", "BacacaA", 12], ["aBcAabAA", "aBcB", 9], ["aBcAc", "bCCbbCc", 11], ["aBcB", "c", 6], ["aBcBBBA", "BbBBaAAab", 12], ["aBcBBCAbb", "AAA", 15], ["aBcBaa", "bbB", 9], ["aBcBbbCb", "cAcBCca", 11], ["aBcC", "CCAAA", 10], ["aBcCABCc", "BCbbcC", 9], ["aBcCBbAc", "cAbABaAa", 12], ["aBcCCbA", "cbA", 8], ["aBcCcbaAC", "aBBbBaB", 11], ["aBca", "BacbCCcB", 11], ["aBca", "CBAcCcCA", 11], ["aBcaCC", "c", 10], ["aBcaaBB", "BAbBaACbc", 12], ["aBcbABAAa", "bCCc", 16], ["aBcbCAb", "abA", 8], ["aBcba", "cAbCC", 9], ["aBccABa", "baBbCAAB", 9], ["aBccAbac", "CAaaCbb", 14], ["aBccacCBa", "ccbc", 12], ["aBcccC", "CccbACAcc", 12], ["aC", "AABacca", 11], ["aC", "ABABACC", 11], ["aC", "AcaBa", 8], ["aC", "AcaCBcaCB", 14], ["aC", "BBbbBcA", 13], ["aC", "BCABB", 8], ["aC", "Ba", 4], ["aC", "BaACBc", 8], ["aC", "BacccCb", 10], ["aC", "BcACaaCa", 12], ["aC", "BcB", 5], ["aC", "CAAcA", 8], ["aC", "CBbB", 8], ["aC", "CCA", 4], ["aC", "CaAA", 6], ["aC", "a", 2], ["aC", "aACaBBaB", 12], ["aC", "abCBcb", 8], ["aC", "abcabC", 8], ["aC", "bA", 4], ["aC", "bABAabc", 11], ["aC", "bBAAbCAcB", 15], ["aC", "bBaaaA", 10], ["aC", "bCBA", 6], ["aC", "baaaCC", 8], ["aC", "babcC", 6], ["aC", "bbc", 5], ["aC", "caCbAcaCc", 14], ["aC", "cbCaCaB", 10], ["aCA", "BCcBBaBc", 13], ["aCA", "CAACacC", 10], ["aCA", "bAaaC", 8], ["aCA", "babAaaBaC", 14], ["aCA", "baccbBBC", 13], ["aCA", "cBCc", 6], ["aCAAA", "aB", 8], ["aCAAAaAba", "BAbB", 14], ["aCAABBBBb", "BBABB", 11], ["aCAACBcA", "aA", 12], ["aCAAaaa", "AcbacaACb", 12], ["aCAAbCBbA", "cBc", 15], ["aCAAcbC", "CAaAcCcCc", 10], ["aCAB", "AbabAb", 7], ["aCAB", "BA", 6], ["aCAB", "CBAC", 6], ["aCABAcBac", "CCBCcbCC", 10], ["aCABBC", "ABb", 7], ["aCABaAaC", "BCaaABcC", 9], ["aCABaBca", "c", 14], ["aCABaa", "C", 10], ["aCACACAC", "baAbACC", 8], ["aCACBBC", "aCACBaAac", 7], ["aCACBaaA", "acCcCB", 10], ["aCACCca", "aBA", 10], ["aCACa", "baBbABB", 10], ["aCACcbaa", "CbAbBCBb", 14], ["aCAa", "bBCCcc", 10], ["aCAaCba", "AabAc", 9], ["aCAab", "CCCcCCAA", 13], ["aCAacCAAa", "abAcBacA", 10], ["aCAb", "<PERSON><PERSON><PERSON>", 6], ["aCAbCC", "bca", 9], ["aCAbCC", "cCABABac", 10], ["aCAbCaCca", "cACA", 12], ["aCAbaBab", "BABbA", 11], ["aCAbca", "B", 11], ["aCAcBbbC", "acaAbACAB", 12], ["aCAcaCB", "CbC", 10], ["aCAcacc", "aA", 10], ["aCAcbA", "aa", 9], ["aCAcbabB", "Aabbca", 12], ["aCAccCa", "AaBca", 8], ["aCB", "A", 5], ["aCB", "aAaCbBcA", 10], ["aCB", "aBABAaccA", 14], ["aCB", "aBcAb", 6], ["aCB", "abCbBA", 6], ["aCB", "b", 5], ["aCB", "cCcaBbc", 10], ["aCBA", "BCcCccBA", 10], ["aCBA", "cCCbcA", 7], ["aCBAabBa", "aA", 12], ["aCBAb", "B", 8], ["aCBAbb", "c", 11], ["aCBB", "bAb", 7], ["aCBBAAB", "bcbCAAAbc", 11], ["aCBBBAab", "CBcBbBcb", 9], ["aCBBCcC", "aBaCAa", 8], ["aCBBabca", "bCaaC", 11], ["aCBBbb", "cb", 9], ["aCBBcBBC", "c", 14], ["aCBCCa", "AaaABcA", 10], ["aCBCc", "bacBbaaaA", 13], ["aCBa", "cAA", 6], ["aCBaABA", "ba", 11], ["aCBaAC", "a", 10], ["aCBaacBc", "cacacCBB", 10], ["aCBb", "Ba", 6], ["aCBb", "CaAc", 8], ["aCBb", "CaBb", 4], ["aCBbABbA", "CbBcaA", 10], ["aCBbACC", "cAaBBC", 10], ["aCBbBAcBB", "aabBbCcaB", 9], ["aCBbBc", "aBabCbbaC", 10], ["aCBbcccA", "cCA", 11], ["aCBc", "ABAAaC", 10], ["aCBc", "BCcc", 4], ["aCBcB", "bBCCAC", 10], ["aCBcaC", "A", 11], ["aCC", "AB", 5], ["aCC", "Ac", 4], ["aCC", "abABbCBc", 11], ["aCC", "cBAcBCcb", 12], ["aCC", "cCCcBaa", 10], ["aCCAA", "C", 8], ["aCCAB", "bbCB", 6], ["aCCABabA", "aCa", 10], ["aCCAcCbA", "cBbCc", 13], ["aCCAccAA", "CB", 14], ["aCCBBB", "cccacc", 10], ["aCCBCcA", "aAc", 10], ["aCCBCcCC", "acBCbA", 9], ["aCCC", "ACbCcBa", 8], ["aCCCBAC", "AB", 11], ["aCCCBCBa", "a", 14], ["aCCCBaAAa", "CcBb", 13], ["aCCCCB", "cCABAcbA", 12], ["aCCCCbCc", "B", 15], ["aCCCCcCb", "AAB", 14], ["aCCCab", "BAaBcaC", 10], ["aCCCc", "aBabBBaa", 14], ["aCCaA", "aAAbb", 8], ["aCCaA", "cAA", 6], ["aCCaBCcb", "BAcc", 12], ["aCCaC", "CcacABac", 10], ["aCCbBBbCB", "baAcABCC", 13], ["aCCbbAC", "BAcbcB", 11], ["aCCbbbaaB", "abbC", 12], ["aCCc", "bAa", 8], ["aCCc", "cbaB", 8], ["aCCcABBCB", "bbC", 14], ["aCCcBBABA", "bABaacb", 15], ["aCCcBCCBc", "a", 16], ["aCCcbaa", "B", 13], ["aCCccBBa", "baAB", 14], ["aCa", "B", 6], ["aCa", "BCcC", 6], ["aCa", "bbbbCBA", 11], ["aCaAAACa", "AcCAA", 10], ["aCaAAb", "BCBccaB", 10], ["aCaABBc", "aCAbcbB", 8], ["aCaABbAb", "aBc", 12], ["aCaAb", "aCc", 6], ["aCaAcCBb", "baCcbb", 9], ["aCaAcaC", "BBCcCcbc", 11], ["aCaB", "BacaaAcBA", 11], ["aCaB", "C", 6], ["aCaBAC", "ab", 9], ["aCaBBa", "B", 10], ["aCaBCB", "a", 10], ["aCaBCCB", "cAccc", 10], ["aCaBa", "aC", 6], ["aCaBacB", "AAbcaB", 9], ["aCaC", "BBcc", 7], ["aCaC", "CAAaCAB", 9], ["aCaC", "accBcAAbA", 14], ["aCaCAAac", "CcCAB", 10], ["aCaCBaaBb", "CC", 14], ["aCaCacAba", "cbaAbBB", 13], ["aCaCccca", "CbACAc", 11], ["aCaa", "BAbB", 8], ["aCaaC", "ABacC", 5], ["aCaac", "AcabAC", 6], ["aCaacB", "CA", 9], ["aCaacacbC", "b", 16], ["aCab", "AcccBcB", 11], ["aCab", "BaaacBbbA", 12], ["aCabBacCB", "AAc", 14], ["aCabCBBB", "CBBcBbBBB", 10], ["aCabCbbb", "CBCCCcA", 12], ["aCabaA", "bABbaB", 8], ["aCabbbCA", "Ba", 14], ["aCabcb", "bBBb", 9], ["aCacA", "BbAB", 9], ["aCacCa", "baABcBcAb", 12], ["aCacCcAaC", "CbAbBa", 14], ["aCacbC", "CcbABcb", 10], ["aCb", "AaBaAc", 10], ["aCb", "BB", 5], ["aCb", "a", 4], ["aCb", "cCbBC", 6], ["aCbA", "CcCccBc", 11], ["aCbA", "bBaC", 8], ["aCbAa", "AABbbcCAC", 13], ["aCbBAA", "BcCC", 11], ["aCbBAB", "bcCBb", 8], ["aCbBB", "B", 8], ["aCbBCaB", "aCcAcaAbB", 9], ["aCbBb", "CBbA", 6], ["aCbCBB", "CAaACaA", 11], ["aCbCBc", "ABaBAcAcC", 13], ["aCbCcCBCB", "CBbc", 13], ["aCba", "aCba", 0], ["aCbaa", "BacbBccb", 11], ["aCbb", "bBcCBAAcc", 15], ["aCbbCCCB", "AaAC", 13], ["aCbbaBAa", "BbB", 11], ["aCbbc", "BBCAb", 8], ["aCbbcAcB", "accCaaB", 9], ["aCbca", "AbAc", 7], ["aCbcb", "cBcacaa", 11], ["aCbcc", "cBCbBAbB", 12], ["aCbcca", "CBbCaaCC", 11], ["aCbccb", "cAAab", 9], ["aCc", "AaAbC", 7], ["aCc", "BCbb", 6], ["aCc", "CCABBCAa", 13], ["aCc", "aAB", 4], ["aCc", "bbAcb", 8], ["aCc", "bbCcccb", 10], ["aCc", "caCAC", 5], ["aCcA", "a", 6], ["aCcA", "aC", 4], ["aCcA", "aaAAaaCBa", 13], ["aCcA", "cABb", 8], ["aCcAAA", "AbaBACBC", 13], ["aCcABAc", "BC", 11], ["aCcABCB", "b", 13], ["aCcACA", "baCcaaAa", 7], ["aCcAaAba", "aCAca", 8], ["aCcAbB", "cBCAB", 7], ["aCcB", "CCbcBaBBb", 12], ["aCcBbCAC", "CACbBb", 11], ["aCcCC", "A", 9], ["aCcCCa", "CcCabCc", 8], ["aCcCabC", "BcACaABCC", 10], ["aCcCcAaA", "baaAacBAb", 13], ["aCcCca", "cBCbCCAcb", 11], ["aCcaAABb", "BCACAAb", 8], ["aCcaCAA", "AabCbBBAC", 12], ["aCcacbabC", "a", 16], ["aCcbAb", "CbbcbaCAB", 11], ["aCcbB", "aA", 8], ["aCcbBbAcC", "bBBc", 11], ["aCcbaCbca", "bcBCCcbC", 12], ["aCcbb", "ccAabbABa", 13], ["aCcbbaccb", "AaCaCaCC", 12], ["aCcbcBBA", "CaBccABCa", 11], ["aCcbcb", "AcaacBCc", 10], ["aCcc", "baCACCa", 8], ["aCccAaBa", "BAaBcB", 12], ["aCccAb", "bBbaAcCC", 13], ["aCccBB", "CCa", 9], ["aCccaaABC", "CcBac", 11], ["aCcccAaba", "ccBbaA", 11], ["aa", "AACcaACB", 13], ["aa", "AcBCa", 7], ["aa", "Bbaba", 6], ["aa", "Bbc", 6], ["aa", "BcaBA", 7], ["aa", "C", 4], ["aa", "CBcC", 8], ["aa", "CbB", 6], ["aa", "CbBC", 8], ["aa", "a", 2], ["aa", "aABACBBb", 13], ["aa", "aBC", 4], ["aa", "aBcaaCAC", 12], ["aa", "aCbbAaaCa", 14], ["aa", "aaAaA", 6], ["aa", "aaAbcb", 8], ["aa", "aabCb", 6], ["aa", "abCBc", 8], ["aa", "abCbAb", 9], ["aa", "acAAbAAB", 13], ["aa", "bAaB", 5], ["aa", "bB", 4], ["aa", "bBAaBCA", 11], ["aa", "bCaaBbCA", 12], ["aa", "baCCBCBB", 14], ["aa", "bbAabBcCc", 15], ["aa", "bbcAC", 9], ["aa", "cAaabc", 8], ["aa", "cB", 4], ["aa", "cBB", 6], ["aa", "cBcBAAbA", 14], ["aa", "cCAB", 7], ["aa", "cCB", 6], ["aa", "caccAcB", 11], ["aa", "cb", 4], ["aa", "cbbcCB", 12], ["aaA", "A", 4], ["aaA", "ACBACBC", 11], ["aaA", "bCCA", 6], ["aaA", "bcaaAB", 6], ["aaAA", "aab", 4], ["aaAAaaAaB", "BAA", 14], ["aaAAbbAC", "cCbBbCAab", 14], ["aaAAcC", "cabbCa", 9], ["aaAB", "cAa", 6], ["aaAB", "cCaBCcB", 10], ["aaABAbAC", "CCAB", 12], ["aaABAbb", "CC", 14], ["aaABB", "Ccb", 9], ["aaABCC", "acABACB", 6], ["aaABCb", "CABbAaa", 12], ["aaABaaA", "CaaaBBabc", 9], ["aaABbbCbc", "B", 16], ["aaAC", "Ac", 5], ["aaAC", "bccBBBb", 14], ["aaACa", "cbAAaCB", 9], ["aaAaAcbBC", "A", 16], ["aaAaaaAb", "bcBc", 16], ["aaAabBCbc", "B", 16], ["aaAabCaAa", "BbbbcA", 13], ["aaAabbbaC", "bbbCaAbC", 14], ["aaAac", "cBCCc", 8], ["aaAbaBbBa", "baaCCa", 12], ["aaAbcbBa", "BbBbaaC", 12], ["aaAc", "bbCBca", 10], ["aaAccCbC", "BBa", 15], ["aaB", "bAAa", 6], ["aaB", "cccCAcAaC", 15], ["aaBA", "ABccb", 9], ["aaBA", "aca", 5], ["aaBABCc", "Aaa", 10], ["aaBABaCc", "acBaac", 7], ["aaBABbCAc", "cCb", 16], ["aaBB", "BbbBbcAB", 12], ["aaBB", "bAaAaAB", 8], ["aaBB", "bBabBA", 7], ["aaBBa", "caaB", 6], ["aaBC", "BacaAB", 8], ["aaBCAaaaB", "aaACA", 10], ["aaBCC", "BAAaBb", 9], ["aaBCCa", "CA", 9], ["aaBCCbAac", "AcaaBacc", 13], ["aaBa", "BabbcC", 9], ["aaBaAaCa", "aBbcAcAb", 11], ["aaBaC", "bcCAbac", 9], ["aaBaCc", "BCAaCCb", 9], ["aaBaa", "AAb", 7], ["aaBaa", "ccba", 7], ["aaBaaAcBA", "aCcBCAc", 12], ["aaBabACBC", "CCCc", 15], ["aaBabbBA", "aAbB", 9], ["aaBacaB", "BCABCCCbc", 13], ["aaBbAaBA", "BAc", 12], ["aaBbb", "aA", 7], ["aaBbcA", "CBcb", 8], ["aaBbcbb", "C", 13], ["aaBcBaBa", "aaBaaaB", 6], ["aaBccACb", "bCCBccC", 10], ["aaC", "ABcAca", 9], ["aaC", "BAbaCaa", 9], ["aaC", "CaCbcCc", 10], ["aaC", "Cc", 5], ["aaC", "aCa", 4], ["aaC", "caBBCb", 8], ["aaCA", "B", 8], ["aaCAA", "BcbBC", 10], ["aaCABbb", "CCcbBAb", 9], ["aaCACa", "Bb", 12], ["aaCAcbc", "AaaAAa", 9], ["aaCAccaAB", "CcCABcA", 10], ["aaCC", "B", 8], ["aaCCC", "ABAcC", 6], ["aaCCCc", "cCAaaAbAb", 14], ["aaCCa", "CAA", 7], ["aaCCbaCaB", "cCCcCAccC", 13], ["aaCCcbCA", "acAcb", 9], ["aaCa", "aCBca", 5], ["aaCa", "cAAca", 5], ["aaCaABa", "ba", 11], ["aaCaABbc", "B", 14], ["aaCaBaA", "aBcAa", 8], ["aaCaa", "Ac", 8], ["aaCacA", "CbabcA", 8], ["aaCacAAb", "CcCAc", 10], ["aaCacaBC", "BBaCcA", 11], ["aaCacaba", "ACbc", 11], ["aaCbAAB", "ABc", 12], ["aaCbabb", "Bc", 13], ["aaCbabc", "C", 12], ["aaCbbAAc", "BaBa", 12], ["aaCcAcc", "aC", 10], ["aaCcaBcB", "ccAaAbaBC", 12], ["aaCcaC", "AAABCccC", 8], ["aaCccbAaa", "B", 17], ["aaa", "ABAbCbC", 12], ["aaa", "abABCcbA", 12], ["aaa", "abaC", 4], ["aaa", "baa", 2], ["aaa", "cCbC", 8], ["aaaA", "BaCacbbc", 12], ["aaaA", "aaaabAC", 6], ["aaaAAb", "abCC", 10], ["aaaAAcb", "cbABcb", 8], ["aaaABb", "BBCCbBaba", 14], ["aaaACcA", "aaa", 8], ["aaaAac", "CCAaC", 7], ["aaaAb", "ACc", 9], ["aaaB", "b", 7], ["aaaBBB", "CcACCaCB", 13], ["aaaBCAbBc", "ba<PERSON>aab", 12], ["aaaBaBCAb", "BaBccAb", 9], ["aaaBbAC", "baBCAc", 7], ["aaaBbBA", "a", 12], ["aaaC", "aca", 4], ["aaaCAAcAA", "cCacb", 13], ["aaaCBacCc", "aA", 15], ["aaaCaBcCC", "ACAbBb", 13], ["aaaCaaC", "C", 12], ["aaaCaaCb", "bC", 14], ["aaaCb", "bcBbaB", 11], ["aaaCbC", "AabAc", 8], ["aaaCcB", "A", 11], ["aaaa", "CacBC", 8], ["aaaaB", "aCBcbC", 9], ["aaaaBBCc", "aCcCcaab", 14], ["aaaaBCc", "bcbaB", 10], ["aaaaC", "CBb", 10], ["aaabCBaAB", "cbBccA", 14], ["aaac", "BAaB", 5], ["aaacCCB", "aaAaacA", 8], ["aab", "CcCcaACB", 12], ["aab", "ab", 2], ["aab", "b", 4], ["aab", "bBb", 4], ["aab", "ccbCc", 8], ["aabAA", "CBCcBcac", 14], ["aabACB", "AACA", 7], ["aabACB", "Ca", 10], ["aabAacba", "bCbcBC", 11], ["aabAbBc", "AbCaCcCB", 13], ["aabBAA", "CbaACAbAA", 10], ["aabBBcB", "CbACCCc", 13], ["aabCAAc", "CBaA", 10], ["aabCAcB", "BBCA", 9], ["aabaCc", "Acab", 9], ["aabb", "a", 6], ["aabbA", "bA", 6], ["aabbCAA", "AaAAAbbC", 11], ["aabbacA", "AbC", 10], ["aabbcABba", "AC", 16], ["aabc", "A", 7], ["aabc", "<PERSON>ab", 4], ["aabcA", "cabBcCc", 8], ["aabcBcB", "Ab", 11], ["aabcaAA", "abccacA", 6], ["aabcbBc", "BaCaaaA", 12], ["aabcbaaac", "BbCbccCc", 11], ["aac", "BCC", 5], ["aac", "BbCaCBbBc", 14], ["aac", "C", 5], ["aac", "a", 4], ["aacA", "CABCabaa", 12], ["aacAaCCA", "CBCbB", 13], ["aacAaCCAb", "BBCAB", 13], ["aacAcb", "baAacaCAB", 9], ["aacB", "ABcCcBAca", 13], ["aacB", "CACAABc", 10], ["aacB", "c", 6], ["aacBAabc", "caCBa", 9], ["aacBc", "aBCaCbAc", 8], ["aacC", "Ba", 6], ["aacC", "Bc", 6], ["aacC", "CAbBAbCaA", 14], ["aacCCb", "ccBAc", 10], ["aacCc", "CaB", 8], ["aacaA", "Bbaa", 7], ["aacaa", "cCB", 8], ["aacaaCbBc", "BCab", 13], ["aacabaBA", "cbbA", 9], ["aacac", "ABBCabcc", 10], ["aacacAaB", "aAbbA", 11], ["aacb", "BCcAb", 6], ["aacbAAC", "BbabBAC", 8], ["aacbAcA", "acbccC", 6], ["aacbCABAC", "AAbaCAbbA", 11], ["aacbb", "BBAbAC", 10], ["aacc", "acACBAa", 10], ["aaccAaABA", "BAbBacAC", 14], ["aacccAccB", "BBCAb", 14], ["ab", "ABca", 6], ["ab", "ACA", 5], ["ab", "AaB", 3], ["ab", "B", 3], ["ab", "BAabAa", 8], ["ab", "BAacbb", 8], ["ab", "Ba", 4], ["ab", "BbaBBa", 9], ["ab", "CACABaC", 12], ["ab", "CCbaA", 8], ["ab", "CbBACbc", 11], ["ab", "CbcbcbB", 12], ["ab", "CcB", 5], ["ab", "CcacBC", 9], ["ab", "aBBAbbaB", 12], ["ab", "aBCbBCACa", 14], ["ab", "abAbbc", 8], ["ab", "abB", 2], ["ab", "bAbCcBAaA", 15], ["ab", "baBcBa", 9], ["ab", "bb", 2], ["ab", "c", 4], ["ab", "cAAabacc", 12], ["ab", "cABcCCb", 11], ["ab", "cCbbbCa", 12], ["ab", "cbcb", 6], ["ab", "ccb", 4], ["ab", "cccaaCAA", 14], ["abA", "AaacB", 8], ["abA", "BAB", 5], ["abA", "abca", 3], ["abA", "bAAcC", 8], ["abA", "bBbaa", 7], ["abA", "bbbAcabb", 12], ["abA", "cAbCCcbb", 13], ["abA", "cBaaC", 8], ["abAAB", "BAbACC", 7], ["abAAcaCa", "c", 14], ["abAB", "AaCBBbb", 10], ["abABAA", "aBABbaabA", 8], ["abABcAaC", "aCbAcb", 10], ["abABcb", "b", 10], ["abAC", "BAC", 3], ["abACAC", "CBCaaCcA", 12], ["abACC", "BbCbBb", 10], ["abACCBBa", "abb", 11], ["abACCaABb", "aAcbb", 10], ["abACb", "Ca", 8], ["abACbBAaA", "cAAcCaCaB", 13], ["abACccA", "bBaCBC", 9], ["abAaAaCCC", "cACCbAbCC", 13], ["abAaBAAC", "ABca", 11], ["abAacCCaa", "acbCAC", 11], ["abAbAcCc", "b", 14], ["abAbbBC", "CAbbBbBb", 8], ["abAc", "bC", 5], ["abAcBBbBA", "CabccbcaA", 11], ["abAccAbba", "caCACCC", 14], ["abAccBA", "ACAbbABaa", 12], ["abAccCbBa", "cbccBbBac", 8], ["abB", "BAAA", 7], ["abB", "BACbb", 6], ["abB", "BcaBabA", 10], ["abB", "a", 4], ["abB", "cbBaACaAB", 14], ["abBA", "AACBCAbA", 11], ["abBAA", "bA", 6], ["abBAAcaA", "AACCC", 11], ["abBAAcbb", "bCCcbb", 8], ["abBAB", "aa", 7], ["abBABBA", "BBCBccbaA", 12], ["abBAaBa", "bbaBCcbc", 11], ["abBAaCacb", "bbaca", 10], ["abBAaCcc", "ccCACB", 12], ["abBAc", "bB", 6], ["abBAc", "bBB", 6], ["abBB", "BbaBbCbb", 10], ["abBBCc", "b", 10], ["abBBaAcBc", "bbCaB", 11], ["abBBaBbCc", "Cac", 14], ["abBBac", "BAc", 7], ["abBBb", "A", 9], ["abBBb", "ACAAc", 9], ["abBBbB", "b", 10], ["abBCACBBc", "B", 16], ["abBCBAB", "cCA", 10], ["abBCCb", "AbaCcCcA", 9], ["abBCCcc", "BcBCCbCbb", 11], ["abBCac", "cCcbACcC", 11], ["abBCcCbA", "bC", 12], ["abBa", "cbabba", 5], ["abBaA", "CA", 8], ["abBaAAc", "BB", 11], ["abBaBcabA", "BBBAabAba", 10], ["abBaCbB", "CaCaacbC", 9], ["abBabAb", "Aacb", 9], ["abBacbc", "cAccCaaa", 15], ["abBacca", "bAACac", 10], ["abBb", "ABCaACb", 10], ["abBb", "BAABc", 7], ["abBb", "c", 8], ["abBb", "cAB", 6], ["abBbBB", "Aaccb", 10], ["abBbC", "aaccbac", 9], ["abBbCBBa", "C", 14], ["abBbb", "BBA", 7], ["abBbba", "CBbAAb", 9], ["abBc", "AABBCcBcc", 12], ["abBc", "bcbbCABca", 12], ["abBcB", "CbCaCca", 10], ["abBcBBCA", "BCCCacAac", 15], ["abBcaBa", "bcBCCc", 11], ["abC", "ACAbb", 7], ["abC", "Cc", 5], ["abC", "aAabBb", 8], ["abC", "b", 4], ["abC", "bccBAC", 9], ["abC", "bccabCBc", 10], ["abC", "cBA", 5], ["abC", "cCB", 6], ["abCA", "Acb", 6], ["abCA", "cc", 7], ["abCAA", "BAcbCCBc", 11], ["abCAAcbCB", "acCabB", 9], ["abCAaA", "AcbaAB", 8], ["abCB", "BABBbAABA", 13], ["abCBB", "Bb", 7], ["abCBCB", "baCaaa", 10], ["abCC", "CA", 6], ["abCC", "CCcCAcaA", 13], ["abCCAacb", "babaA", 12], ["abCCBB", "baB", 8], ["abCCBBC", "aAc", 11], ["abCCCBaAC", "CCBbCbBA", 13], ["abCCaAca", "bBCBCaC", 10], ["abCCbBBaA", "Ab", 15], ["abCCc", "BcbBabBc", 12], ["abCCcAcBB", "BaAAAaAB", 13], ["abCa", "ABA", 5], ["abCaBC", "bCCaCaA", 10], ["abCaC", "acc", 6], ["abCaCAbB", "aCaba", 8], ["abCabA", "ACba", 6], ["abCabbccb", "CcAcA", 14], ["abCacaaC", "aC", 12], ["abCb", "cBCBaA", 8], ["abCbBCcAa", "a", 16], ["abCbaCC", "CAabaabBB", 14], ["abCbbba", "CcbBCBAA", 12], ["abCc", "Ca", 6], ["abCcAbbC", "B", 15], ["abCcCCC", "BbAaca", 11], ["abCcCc", "ACacA", 8], ["abCcaA", "aca", 6], ["abCcaABB", "AbcBb", 8], ["aba", "CBb", 5], ["aba", "CBbbaAb", 10], ["aba", "aCccA", 7], ["aba", "acaB", 4], ["aba", "bcCCa", 8], ["aba", "cBB", 5], ["abaAAA", "cAbB", 10], ["abaACCcC", "abacCBA", 7], ["abaAaccC", "CCaacc", 8], ["abaAbb", "a", 10], ["abaBCABc", "aCcccB", 11], ["abaBaCca", "aAcAbaB", 12], ["abaC", "Bba", 4], ["abaCACccC", "BBBBB", 17], ["abaCBABaB", "abacbacc", 9], ["abaCCbcA", "AcBBc", 11], ["abaCbB", "cC", 10], ["abaCbaCB", "AAcC", 11], ["abaCbaa", "aCCaCCA", 9], ["abaaACcB", "cBBCBBAbC", 16], ["abab", "CcaCC", 8], ["abab", "aCAAaaaB", 11], ["abab", "acBbA", 6], ["ababA", "CBBC", 8], ["ababACcC", "BC", 13], ["ababCBBbB", "CbC", 14], ["ababa", "CABCcCcb", 14], ["ababcCb", "aCAbCc", 7], ["abacA", "ABb", 8], ["abacABc", "babCCAbb", 8], ["abacAc", "BacbCaaB", 11], ["abacBBa", "CbCAa", 9], ["abacBbaCC", "a", 16], ["abacCb", "b", 10], ["abaca", "cAB", 9], ["abacaCbBc", "bACcACB", 10], ["abaccABb", "cBcCc", 12], ["abaccaAcA", "B", 17], ["abacccc", "Cba", 10], ["abb", "B", 5], ["abb", "CbBBAc", 9], ["abb", "aC", 4], ["abb", "bbbcaca", 10], ["abbAAABBb", "CbCcB", 14], ["abbAcB", "BbaaBc", 8], ["abbBBCcB", "aaa", 14], ["abbBbAbcc", "c", 16], ["abbBbbbcC", "aBbBaB", 10], ["abbCBA", "aA", 8], ["abbCBA", "bb", 8], ["abbCBabac", "CBAB", 12], ["abbCaBB", "Ab", 11], ["abbCaBC", "aBcc", 9], ["abbCbbBB", "cbbaaca", 12], ["abbCc", "bC", 6], ["abbaA", "BabaAAaBb", 11], ["abbaBaaBc", "CAcBbc", 13], ["abbaBbc", "aABbAcc", 8], ["abbaCcba", "Cba", 10], ["abbbA", "ACAcc", 9], ["abbbBAbAA", "aCACcAbA", 10], ["abbbCCCaC", "acBCBABcC", 13], ["abbbb", "cCcbBcb", 9], ["abbbc", "C", 9], ["abbbca", "BBAcBbAB", 12], ["abbcABB", "ac", 10], ["abbcB", "ABbcCB", 4], ["abbcBac", "bACca", 9], ["abbccAa", "AcC", 10], ["abbccb", "cbCb", 7], ["abc", "AbC", 2], ["abc", "BAAb", 7], ["abc", "C", 5], ["abc", "aB", 3], ["abcAA", "CAAabC", 11], ["abcAaaccb", "cAcAAcbcA", 11], ["abcAbB", "AbcCacc", 8], ["abcAcaB", "acBcBb", 7], ["abcAcbbb", "CaABc", 13], ["abcB", "AaCcAbCa", 11], ["abcB", "bbab", 5], ["abcBaBb", "cBBa", 8], ["abcBacc", "ACBa", 8], ["abcBb", "BBbcccBB", 9], ["abcBbBAb", "cB", 12], ["abcC", "BCc", 5], ["abcCAB", "CbCcC", 8], ["abcCBAc", "ACCBaA", 7], ["abcCaBcCB", "AAba", 15], ["abcCbBCA", "Ba", 13], ["abca", "cbaccAA", 9], ["abcaCB", "CCaaAB", 8], ["abcb", "a", 6], ["abcbbC", "BBc", 9], ["abcbbbcb", "b", 14], ["abccC", "bbcBab", 8], ["abccCBabC", "AbABaaabc", 10], ["abccCaB", "Bbaa", 10], ["abccCcAa", "BCbb", 13], ["abccaB", "aCABaba", 9], ["abccaC", "ABbCB", 9], ["abccaCcBB", "AAB", 14], ["abccbCC", "BAc", 11], ["ac", "A", 3], ["ac", "AAaCcbBa", 12], ["ac", "AAcCbbCca", 15], ["ac", "ACAcCa", 9], ["ac", "ACCBbAcCc", 15], ["ac", "B", 4], ["ac", "BAab", 6], ["ac", "BCCBbccb", 14], ["ac", "BCc", 4], ["ac", "BaBbbAA", 12], ["ac", "BcCACbB", 12], ["ac", "CBA", 6], ["ac", "CccBcaB", 12], ["ac", "aB", 2], ["ac", "aBBCacBbC", 14], ["ac", "aC", 1], ["ac", "aCcCC", 6], ["ac", "aaCaCaca", 12], ["ac", "ab", 2], ["ac", "acacCAbBA", 14], ["ac", "bACaaCCCC", 15], ["ac", "bAbaCCcBC", 14], ["ac", "bCCaAaa", 12], ["ac", "cBAcc", 7], ["acA", "AaCCAaabc", 13], ["acA", "aABBBACc", 12], ["acA", "ac", 2], ["acA", "cBcBb", 8], ["acAA", "bc", 6], ["acAAABBa", "cbcC", 13], ["acAACBA", "BcbaCbbA", 8], ["acAB", "ccbAAAa", 10], ["acABCCA", "BCcABBAa", 9], ["acABc", "BBcABbCba", 11], ["acAC", "A", 6], ["acACCB", "AAcBCba", 9], ["acAaaAcba", "BabCcbCbb", 15], ["acAaaaBc", "BacCBc", 10], ["acAabACa", "acaCB", 8], ["acAabBbA", "cabCb", 8], ["acAacCc", "BBCC", 11], ["acAb", "cc", 6], ["acAbabcBa", "ABABcaB", 11], ["acAc", "ABa", 6], ["acAc", "CcBCaa", 9], ["acAc", "aBbCaB", 8], ["acAcBCCa", "BaB", 13], ["acAcCAc", "cAbab", 9], ["acAcaC", "B", 12], ["acAcabB", "baaacbB", 7], ["acAcbcAC", "ccaCbCcc", 8], ["acB", "ABcAcBAcb", 13], ["acB", "acb", 1], ["acB", "bCccA", 8], ["acB", "cbabb", 7], ["acBA", "BcAaBCBAA", 11], ["acBAcc", "AAabb", 10], ["acBB", "AaaAbB", 7], ["acBB", "cA", 6], ["acBBAb", "bCcAbBCAC", 11], ["acBBAbacB", "aaCBB", 12], ["acBBBbAcb", "BAa", 14], ["acBBCC", "ccAbcBbCb", 10], ["acBBCc", "bcCaAbAa", 13], ["acBBbBa", "Cba", 9], ["acBBcCC", "ABbb", 10], ["acBCBcabA", "caCbca", 9], ["acBCCbab", "ccA", 12], ["acBCa", "BabbAcb", 10], ["acBa", "AC", 6], ["acBaA", "ABBCC", 7], ["acBaAacc", "bbab", 13], ["acBaBCab", "ac", 12], ["acBaBaC", "bbccbac", 10], ["acBacCbAc", "bBBACbaAA", 11], ["acBb", "CA", 7], ["acBb", "bCBAAB", 8], ["acBbAc", "aaaBCA", 8], ["acBbCca", "acA", 9], ["acBcCcaB", "CC", 13], ["acBcabcAC", "Aac", 13], ["acC", "AABcbaC", 9], ["acC", "AcBAaC", 7], ["acC", "CabBabaBC", 14], ["acC", "b", 6], ["acC", "baBCBCbAC", 13], ["acC", "baaCBCBC", 11], ["acCA", "C", 6], ["acCA", "aabBCaaaC", 13], ["acCAAaCB", "ba", 14], ["acCAB", "BccCcCbB", 10], ["acCAa", "c", 8], ["acCAabBcc", "Bb", 16], ["acCAb", "abB", 7], ["acCAcc", "cccCCacCC", 10], ["acCB", "B", 6], ["acCBAcb", "acaBcA", 6], ["acCBBA", "caccBbabC", 9], ["acCBBb", "aCbc", 7], ["acCBCbaBC", "bCabBc", 11], ["acCBaaAc", "BAAABCA", 13], ["acCC", "BAB", 8], ["acCCABBb", "Bbcca", 13], ["acCCCaAb", "acCCbAcca", 9], ["acCCab", "aB", 9], ["acCCbA", "CCAbCbBA", 9], ["acCCbBcc", "aAAaA", 14], ["acCCcaC", "AcbBabcCb", 13], ["acCCcab", "ccab", 6], ["acCa", "aaAcAbBbB", 14], ["acCaAA", "BAbcAcbC", 13], ["acCaABcbb", "A", 16], ["acCaC", "abBACBCB", 10], ["acCaCa", "aBa", 8], ["acCb", "aACBa", 5], ["acCbBBa", "BbbACCccb", 15], ["acCbCCCBb", "B", 16], ["acCbbBB", "bcca", 11], ["acCc", "A", 7], ["acCc", "AABBa", 9], ["acCcA", "CBBcaBCCA", 11], ["acCcAcB", "b", 13], ["acCcB", "BBCCBA", 7], ["acCcaAA", "b", 14], ["acCcacB", "cbAccBcb", 10], ["acCcc", "AC", 7], ["aca", "ACAB", 5], ["aca", "AaAc", 6], ["aca", "cAAbacBCa", 12], ["aca", "cbCCabAaB", 14], ["acaA", "C", 7], ["acaA", "aCbcACABa", 11], ["acaAAAbc", "B", 15], ["acaABAcA", "Cacca", 10], ["acaAcb", "cbc", 8], ["acaBA", "baBCbCBBC", 13], ["acaBcacBc", "b", 17], ["acaBccaC", "C", 14], ["acaC", "bccCAccA", 12], ["acaCAaBB", "Bc", 14], ["acaCBBB", "A", 13], ["acaCaA", "BCB", 10], ["acaCaC", "c", 10], ["acaCbC", "acaabCaa", 6], ["acaCcaAB", "ABAaaaba", 12], ["acaCcbcB", "CcbACBBc", 10], ["acaCcbcc", "C", 14], ["acaa", "AabBa", 6], ["acaaBAA", "cBBbaAB", 10], ["acaaCC", "cAACA", 6], ["acaaaCCA", "AaC", 11], ["acaacAbB", "bCbb", 12], ["acaaca", "cBACccCa", 11], ["acaacb", "ccCbBca", 10], ["acabCb", "ccaCbA", 6], ["acabaBCbB", "cab", 12], ["acabcA", "AB", 10], ["acacA", "bcBACcaa", 10], ["acacbA", "A", 10], ["acacbABa", "bCbcBa", 9], ["acacbBa", "BAbA", 10], ["acb", "AabAbA", 8], ["acb", "BCCa", 7], ["acb", "cBCB", 6], ["acbA", "AcBCbCa", 8], ["acbA", "aAaBCAB", 9], ["acbAA", "bc", 8], ["acbACbbA", "a", 14], ["acbAbacc", "caAcB", 10], ["acbB", "aBAb", 5], ["acbBBC", "caB", 8], ["acbBaAAb", "bbBcABBaC", 13], ["acbBaC", "CaA", 9], ["acbBc", "abB", 4], ["acbC", "ABABaBbca", 13], ["acbC", "bbBbcaaA", 13], ["acbCA", "A", 8], ["acbCA", "cBb", 7], ["acbCBb", "acAbCCaBc", 8], ["acbCaaAaB", "CaCc", 14], ["acbCabC", "cBACbcBC", 10], ["acbaBaA", "BBcA", 9], ["acbabAa", "CaAbcbc", 10], ["acbabbaB", "c", 14], ["acbb", "AAa", 7], ["acbb", "AbCaBCcBA", 13], ["acbb", "BbAca", 9], ["acbb", "CBaCBcCbA", 12], ["acbbA", "AcCAAbc", 9], ["acbbAbA", "CCCaccab", 13], ["acbbcABBB", "BcCBbb", 11], ["acbc", "aBAbB", 6], ["acbcAa", "BaCC", 10], ["acbcBbc", "B", 12], ["acbcCAC", "ACaAab", 11], ["acbcCab", "BAb", 10], ["acbcaacbc", "CbccCCBcb", 11], ["acbcbbcc", "CcAbaCCbb", 13], ["acbccACA", "b", 14], ["acbccBcB", "CCcc", 10], ["acbccCB", "bbbaaCAAB", 12], ["acc", "C", 5], ["acc", "bAac", 5], ["accA", "CBBaA", 8], ["accAA", "C", 9], ["accABbb", "cBbAAbaC", 12], ["accAac", "CCcacb", 7], ["accAb", "bcbAbB", 6], ["accAcbAa", "B", 15], ["accB", "bBbAb", 9], ["accBC", "bCCCCab", 10], ["accBbcCaa", "aaCcaBACB", 12], ["accC", "CCcbBc", 8], ["accC", "aa", 6], ["accCAcACa", "AABbCCc", 14], ["accCCaCB", "aCBba", 11], ["accCCcA", "cBbA", 10], ["accCaB", "aBaB", 6], ["accCbbC", "cBBcaa", 12], ["acca", "BBCAcbaba", 13], ["acca", "BBcCC", 7], ["accaACAAc", "cbbCABA", 12], ["accaaAcAb", "ccB", 13], ["accaaC", "CbA", 10], ["accab", "AAaAcA", 9], ["accabbcB", "aa", 12], ["accac", "C", 9], ["accb", "CBCAcCaBB", 13], ["accbAA", "Ba", 10], ["accbAa", "Ca", 9], ["accbBac", "caC", 9], ["accbCcBAc", "cbA", 12], ["accbaAc", "ACAaCAAA", 11], ["accbb", "CCcC", 7], ["acccAB", "cb", 9], ["acccC", "baaaaB", 10], ["acccaAaB", "C", 15], ["acccc", "cAAC", 7], ["b", "A", 2], ["b", "AAbAaCb", 12], ["b", "ABBBC", 9], ["b", "ABbCCBC", 12], ["b", "ABc", 5], ["b", "ABcba", 8], ["b", "ACAAC", 10], ["b", "ACBbAcabc", 16], ["b", "ACBbCBcCC", 16], ["b", "ACCaA", 10], ["b", "Aa", 4], ["b", "AaAbBCA", 12], ["b", "Aac", 6], ["b", "AacaBc", 11], ["b", "AbaC", 6], ["b", "Abcba", 8], ["b", "Ac", 4], ["b", "AcCbB", 8], ["b", "AcCbBca", 12], ["b", "AcaaCaAa", 16], ["b", "B", 1], ["b", "BBA", 5], ["b", "BBaaabB", 12], ["b", "BBbaBcba", 14], ["b", "BCBaC", 9], ["b", "BCcAABb", 12], ["b", "Ba", 3], ["b", "BaC", 5], ["b", "BaCbBc", 10], ["b", "Bac", 5], ["b", "BacBAc", 11], ["b", "Bb", 2], ["b", "BbA", 4], ["b", "BbBacbb", 12], ["b", "BbacaCc", 12], ["b", "BbbcAcAab", 16], ["b", "Bc", 3], ["b", "BcBA", 7], ["b", "BcBCAAaCB", 17], ["b", "BcC", 5], ["b", "BcCa", 7], ["b", "C", 2], ["b", "CAB", 5], ["b", "CACCBca", 13], ["b", "CBABaBAB", 15], ["b", "CBCCa", 9], ["b", "CBCCcC", 11], ["b", "CBCcaBBAB", 17], ["b", "CBaBaaCbB", 16], ["b", "CBaabA", 10], ["b", "CCBABBcC", 15], ["b", "CCC", 6], ["b", "CCbCA", 8], ["b", "Ca", 4], ["b", "CaCBcA", 11], ["b", "Cabacc", 10], ["b", "CabccCBCB", 16], ["b", "CacAbAAac", 16], ["b", "CbA", 4], ["b", "CbcBbA", 10], ["b", "Cc", 4], ["b", "CcAABbA", 12], ["b", "CcabBB", 10], ["b", "CcacCcAcC", 18], ["b", "CcbCACB", 12], ["b", "Ccbc", 6], ["b", "a", 2], ["b", "aA", 4], ["b", "aABBCcC", 13], ["b", "aABccbaCc", 16], ["b", "aACCCCba", 14], ["b", "aACCa", 10], ["b", "aAaBA", 9], ["b", "aAacBab", 12], ["b", "aB", 3], ["b", "aBCAac", 11], ["b", "aBCBC", 9], ["b", "aBCbacab", 14], ["b", "aBaCa", 9], ["b", "aBc", 5], ["b", "aC", 4], ["b", "aCBA", 7], ["b", "aCCa", 8], ["b", "aa", 4], ["b", "aabCCBca", 14], ["b", "aabbAb", 10], ["b", "aabbcC", 10], ["b", "ab", 2], ["b", "abB", 4], ["b", "abBBcb", 10], ["b", "abCCBCA", 12], ["b", "abcACcaa", 14], ["b", "abcBccCa", 14], ["b", "abca", 6], ["b", "ac", 4], ["b", "acACCaAca", 18], ["b", "acC", 6], ["b", "aca", 6], ["b", "acaabCC", 12], ["b", "b", 0], ["b", "bACAbB", 10], ["b", "bBAccb", 10], ["b", "bBBC", 6], ["b", "bBbaAA", 10], ["b", "bC", 2], ["b", "bCA", 4], ["b", "bCBCAbaCA", 16], ["b", "bCCbA", 8], ["b", "bCaAC", 8], ["b", "bCaCAB", 10], ["b", "bCabBB", 10], ["b", "bCbBbcA", 12], ["b", "bCcbAb", 10], ["b", "baBACBaaB", 16], ["b", "baCCbBC", 12], ["b", "baabBABb", 14], ["b", "baabbaCBB", 16], ["b", "baaccc", 10], ["b", "babBAcb", 12], ["b", "bb", 2], ["b", "bcBACbaBb", 16], ["b", "bcCa", 6], ["b", "bcCcAcbc", 14], ["b", "c", 2], ["b", "cABCaB", 11], ["b", "cAC", 6], ["b", "cAbBbBcc", 14], ["b", "cAbaBABBc", 16], ["b", "cAbaCBAa", 14], ["b", "cBAAAcaA", 15], ["b", "cBBcbAabb", 16], ["b", "cBCCAcb", 12], ["b", "cBCCa", 9], ["b", "cBCaCAc", 13], ["b", "cBbaBbcB", 14], ["b", "cBbbBaAa", 14], ["b", "cCAACba", 12], ["b", "cCaccAaCb", 16], ["b", "cCcbA", 8], ["b", "caA", 6], ["b", "caAb", 6], ["b", "caAccaCbb", 16], ["b", "caB", 5], ["b", "cabBbcA", 12], ["b", "cacbbBAb", 14], ["b", "cb", 2], ["b", "cbAa", 6], ["b", "cbAaCa", 10], ["b", "cbcBAcCbA", 16], ["b", "ccCaacc", 14], ["b", "ccCbaaaa", 14], ["b", "ccaC", 8], ["b", "ccbbcb", 10], ["bA", "AAAcACAbA", 14], ["bA", "AAbB", 6], ["bA", "ABaC", 6], ["bA", "ACBbbA", 8], ["bA", "ACBbccB", 12], ["bA", "ACbbBB", 10], ["bA", "Accc", 8], ["bA", "BAcCabc", 11], ["bA", "BCCcccB", 13], ["bA", "BabcCBCaa", 15], ["bA", "BbCaCBca", 13], ["bA", "BbbcbCCB", 14], ["bA", "Bbca", 5], ["bA", "C", 4], ["bA", "CAbABbBC", 12], ["bA", "CB", 4], ["bA", "CBCBa", 8], ["bA", "CbAaAaB", 10], ["bA", "CbcAAc", 8], ["bA", "CcAAcbcBC", 16], ["bA", "aAc", 4], ["bA", "aBCCcAcb", 13], ["bA", "aCb", 6], ["bA", "aCcbbc", 10], ["bA", "bAbcaAA", 10], ["bA", "bAc", 2], ["bA", "ba", 1], ["bA", "bb", 2], ["bA", "cAcCABaAb", 15], ["bA", "cCAABbBb", 14], ["bA", "cCC", 6], ["bA", "cabc", 6], ["bA", "ccCbcC", 10], ["bAA", "AabBB", 8], ["bAA", "BBAAaBCbC", 13], ["bAA", "C", 6], ["bAA", "CBaBcBb", 12], ["bAA", "CCBAc", 7], ["bAA", "abBBBAA", 8], ["bAA", "baac<PERSON>bb", 9], ["bAA", "bcbBccAb", 12], ["bAAA", "C", 8], ["bAAA", "bBAAC", 4], ["bAAAA", "c", 10], ["bAAAAbaCa", "abacbC", 12], ["bAAACbbb", "Aaa", 12], ["bAAACccC", "CC", 12], ["bAAAacAc", "caC", 12], ["bAAAbABC", "CBCbBa", 12], ["bAAB", "bC", 6], ["bAABaCA", "A", 12], ["bAABb", "CaBBB", 6], ["bAABcbbB", "CbACCB", 11], ["bAAC", "AabbbaBA", 13], ["bAAC", "Acb", 6], ["bAAC", "aaB", 6], ["bAACcACbc", "Aaa", 14], ["bAAa", "AaABbabC", 11], ["bAAa", "bCAbbaa", 7], ["bAAaCba", "bAca", 7], ["bAAaaaa", "BC", 13], ["bAAbAab", "CBcA", 12], ["bAAbCa", "C", 10], ["bAAbb", "A", 8], ["bAAc", "CCAbca", 8], ["bAAc", "cCBAba", 9], ["bAAcA", "bbc", 6], ["bAAccABbb", "aAc", 13], ["bAAccaCC", "A", 14], ["bAB", "ACBcB", 7], ["bAB", "CbAA", 4], ["bAB", "bbABC", 4], ["bABAC", "BCaabaCBC", 12], ["bABACCCB", "abB", 12], ["bABAaB", "cCCCBbc", 13], ["bABAaCBBA", "BAbA", 11], ["bABAaba", "A", 12], ["bABAba", "BBBAccBA", 9], ["bABAbbCbB", "bA", 14], ["bABAcCCb", "abCaB", 11], ["bABBCCC", "bbCbBc", 10], ["bABBa", "bA", 6], ["bABBcAc", "bAC", 9], ["bABC", "AbBca", 7], ["bABCBcAc", "cBCAA", 10], ["bABCC", "aBCa", 5], ["bABCCbbbC", "cABACBBa", 10], ["bABCaBCa", "CaBbB", 10], ["bABCbaB", "ACCaCBc", 10], ["bABa", "BA", 5], ["bABa", "BAB", 3], ["bABaCBBAa", "Aa", 14], ["bABb", "acA", 7], ["bABb", "ccCA", 8], ["bABbAB", "ABCBCACc", 11], ["bABbAC", "bcBaCcAb", 10], ["bABbBCB", "cb", 12], ["bABbaB", "cAcAbbCAb", 11], ["bABbaBAA", "C", 16], ["bABbbABBB", "cAcb", 14], ["bABbbbC", "a", 13], ["bABbcCab", "ccccaa", 11], ["bAC", "AcAbc", 7], ["bAC", "bCcbAabb", 12], ["bACA", "ACaAAAaC", 12], ["bACA", "CAc", 5], ["bACA", "c", 7], ["bACACAAa", "cbBbC", 14], ["bACAb", "Cbc", 8], ["bACAcc", "BacBcAB", 9], ["bACB", "cBccC", 8], ["bACBAC", "BCCABaC", 6], ["bACBBaA", "bBACB", 8], ["bACBCbb", "b", 12], ["bACBa", "BCC", 7], ["bACBaAcAb", "C", 16], ["bACBabABB", "aCA", 13], ["bACBcB", "accBbaCcC", 13], ["bACCaCcBb", "aBaBcb", 11], ["bACaBC", "bA", 8], ["bACaCCBAB", "aaBacb", 13], ["bACaCcC", "aBCac", 8], ["bACab", "aBcBcAB", 10], ["bACac", "Bc", 7], ["bACac", "cBAacCAcB", 10], ["bACbAA", "B", 11], ["bACbBbc", "CbcAcBcA", 11], ["bACc", "bcA", 5], ["bACc", "cCb", 6], ["bACcABb", "BaC", 10], ["bACcCaaC", "AAbAbb", 13], ["bAa", "AaaACbAcc", 14], ["bAa", "AcAabb", 8], ["bAa", "bCbBCABBA", 13], ["bAaA", "aACcbaB", 10], ["bAaAA", "BacbCbBaa", 14], ["bAaAAAa", "cabAbA", 9], ["bAaABAAC", "AB", 12], ["bAaAcA", "cbC", 11], ["bAaB", "B", 6], ["bAaB", "acCCbb", 11], ["bAaBAA", "a", 10], ["bAaBAcac", "AABcaa", 7], ["bAaBC", "CBcaaaBa", 10], ["bAaBa", "C", 10], ["bAaBaCBAB", "CCbCCC", 15], ["bAaBb", "bCAA", 7], ["bAaBbBc", "AC", 11], ["bAaBbc", "cbbcAaab", 10], ["bAaBc", "a", 8], ["bAaBc", "cbCCBBbcc", 12], ["bAaC", "ABA", 6], ["bAaC", "BAaACAB", 7], ["bAaCCa", "CbCBB", 10], ["bAaCa", "bCBaABcbB", 13], ["bAaCab", "caBC", 9], ["bAaa", "AAcCbAC", 11], ["bAaa", "bBAAb", 5], ["bAaaA", "AbA", 6], ["bAaaB", "CABBC", 8], ["bAaaB", "bABBaC", 6], ["bAaabaAc", "cBaa", 12], ["bAab", "CcC", 8], ["bAab", "bAc", 4], ["bAabAbC", "A", 12], ["bAabBACA", "cCca", 14], ["bAabBBCb", "CbaCbA", 12], ["bAabCAB", "aA", 10], ["bAabb", "AaacAAA", 11], ["bAabbC", "babAb", 6], ["bAabcA", "bA", 8], ["bAabcbc", "BaAabcAaa", 9], ["bAabccAa", "aCbaB", 12], ["bAac", "ccACbbAA", 13], ["bAacA", "BBaACC", 8], ["bAacAb", "bacccBCc", 10], ["bAacAbBca", "C", 17], ["bAacC", "bbaAb", 6], ["bAaccCB", "c", 12], ["bAb", "ABCCAACCA", 15], ["bAb", "CCBAA", 7], ["bAbACaC", "bABcCAb", 6], ["bAbAaC", "BAbbA", 6], ["bAbAabcab", "CAabcBa", 10], ["bAbAbcA", "CAABBca", 8], ["bAbBC", "AACcbCca", 11], ["bAbBCbaB", "acacab", 11], ["bAbBaCBcb", "BaA", 14], ["bAbBbbBbb", "cAaacAC", 16], ["bAbCAABaB", "ACcBAccB", 11], ["bAbCbCcCA", "aBB", 15], ["bAbCcB", "aCAbA", 10], ["bAba", "CaAAb", 8], ["bAbaCC", "Bba", 7], ["bAbab", "CbCAcBcB", 10], ["bAbb", "acAbCBb", 8], ["bAbb", "b", 6], ["bAbb", "bAaAcBBab", 11], ["bAbbB", "AaACcBacC", 14], ["b<PERSON><PERSON>a", "cCBC", 11], ["bAbbbbBc", "cABcBBC", 9], ["bAbbbc", "aCA", 11], ["bAbc", "Accbacc", 9], ["bAbcBB", "A", 10], ["bAbcCaA", "bACaCAcbC", 11], ["bAbcCab", "ABC", 9], ["bAbcCbAA", "BcBb", 11], ["bAbcbA", "CBCBCac", 11], ["bAc", "aaC", 4], ["bAcA", "bb", 6], ["bAcAABA", "aAAbca", 9], ["bAcAAbA", "BABabc", 8], ["bAcAAbb", "aC", 12], ["bAcAbc", "CAAcaaBa", 10], ["bAcAc", "BaC", 7], ["bAcB", "bcBC", 4], ["bAcB", "bccaA", 6], ["bAcBAaaCa", "aABCccaBa", 12], ["bAcBAbBcC", "B", 16], ["bAcBBBABa", "caA", 14], ["bAcBCCbac", "accBBAB", 12], ["bAcBabbC", "ca", 12], ["bAcBbAb", "cbaBA", 9], ["bAcBbaA", "AaBaaA", 6], ["bAcC", "Acac", 5], ["bAcC", "bba", 6], ["bAcCAa", "C", 10], ["bAcCaCc", "abbCcb", 11], ["bAcCb", "Bbcacac", 9], ["bAcCbACCB", "aA", 15], ["bAcCcACA", "ABCAaBB", 11], ["bAcaAbA", "baCCbBca", 10], ["bAcac", "BBbAbCccA", 11], ["bAcb", "aa", 7], ["bAcb", "cAbbb", 6], ["bAcbAbBAC", "CbBc", 12], ["bAcbBbcb", "cCcBBCaa", 11], ["bAcc", "aCcABC", 9], ["bAccA", "ccBaaCA", 9], ["bAccCabB", "aABCAAaAA", 13], ["bAccCbb", "BaAAcB", 10], ["bAcca", "CBbcBBBba", 13], ["bAccaACaa", "CCcBCaA", 10], ["bAccaCCA", "aABAABBCb", 13], ["bAcccAC", "ABC", 10], ["bAcccAbaA", "CBcCAbcC", 11], ["bAcccaB", "BccACBBC", 10], ["bB", "ACaAbC", 10], ["bB", "BCbCb", 7], ["bB", "Baa", 5], ["bB", "BbbbBAb", 10], ["bB", "Bcba", 6], ["bB", "CAAcBb", 10], ["bB", "CbbC", 5], ["bB", "aACCab", 11], ["bB", "aBaccb", 10], ["bB", "aBbC", 6], ["bB", "aCACcaA", 14], ["bB", "aaAABaCCc", 16], ["bB", "ac", 4], ["bB", "acACccAac", 18], ["bB", "bAAacb", 9], ["bB", "bBCccbcC", 12], ["bB", "bBbbcb", 8], ["bB", "bCbBAAbCc", 14], ["bB", "ba", 2], ["bB", "baAbcC", 9], ["bB", "bcBbACaa", 12], ["bB", "c", 4], ["bB", "cBCcc", 8], ["bB", "cCBa", 6], ["bB", "caBc", 6], ["bB", "caCCACb", 13], ["bBA", "AB", 4], ["bBA", "B", 4], ["bBA", "abBCbccC", 12], ["bBA", "bCaaa", 7], ["bBA", "cBB", 4], ["bBAA", "cAb", 6], ["bBAAba", "C", 12], ["bBAAcb", "BCcBABB", 10], ["bBAB", "ccaa", 7], ["bBABbBc", "Bb", 10], ["bBAC", "aCBb", 8], ["bBACA", "bbcaBB", 8], ["bBACBA", "CA", 8], ["bBACBABc", "ca", 14], ["bBACCbCB", "B", 14], ["bBACbaC", "aabAbaCC", 9], ["bBAa", "CBBbBaa", 7], ["bBAa", "CabC", 8], ["bBAa", "cCcCCCA", 13], ["bBAaBAcB", "CbCaaAbAA", 12], ["bBAaBcAaB", "ACcb", 13], ["bBAaC", "acBaAcC", 8], ["bBAaCB", "bcBbCBBca", 12], ["bBAaCc", "Aca", 9], ["bBAac", "AcccCbBc", 14], ["bBAacA", "aBCbaAAA", 10], ["bBAbCA", "cBAaaCACb", 10], ["bBAba", "B", 8], ["bBAbac", "cBbAaccb", 10], ["bBAc", "Bc", 4], ["bBAcAbca", "baB", 12], ["bBAcBA", "aAa", 9], ["bBAcC", "abbBbCBc", 10], ["bBAcC", "c", 8], ["bBAca", "Ba", 6], ["bBAca", "b", 8], ["bBAca", "c", 8], ["bBB", "ABACC", 8], ["bBB", "AcaBacAc", 14], ["bBB", "BCCBCB", 7], ["bBB", "Cba", 5], ["bBB", "bACbcbAc", 12], ["bBBABBb", "Cbab", 10], ["bBBAa", "c", 10], ["bBBBB", "AacCA", 10], ["bBBBBAa", "AbbBAB", 8], ["bBBBCb", "B", 10], ["bBBBabC", "abBacCCBA", 13], ["bBBBc", "AB", 8], ["bBBC", "B", 6], ["bBBC", "abAbBBcab", 11], ["bBBCABbB", "caaCC", 14], ["bBBCAbB", "BcBCBCcBc", 10], ["bBBa", "abbCAA", 8], ["bBBaB", "cacCCbAA", 14], ["bBBaC", "Bcc", 7], ["bBBaaBC", "bcbcbBba", 11], ["bBBaaBb", "a", 12], ["bBBaabc", "caaBba", 10], ["bBBabac", "aaAA", 11], ["bBBb", "<PERSON><PERSON><PERSON>", 10], ["bBBb", "aAacAbca", 14], ["bBBbAcBB", "BBAbBAb", 9], ["bBBbBbbbB", "cc", 18], ["bBBbCa", "BcC", 8], ["bBBbaB", "caAaaC", 10], ["bBBbaBbCa", "a", 16], ["bBBbacabB", "Ba", 14], ["bBBbcc", "bcA", 8], ["bBBc", "ABBbACc", 8], ["bBBc", "BAAb", 7], ["bBBcA", "caB", 8], ["bBBcaca", "CBBBbABcb", 10], ["bBC", "AB", 4], ["bBC", "aaCaCAcBc", 15], ["bBC", "bcaBACB", 8], ["bBC", "cAB", 6], ["bBC", "cBaCcbca", 12], ["bBCA", "AAc", 7], ["bBCA", "bCCACBc", 8], ["bBCA", "babCCBAC", 9], ["bBCA", "cBaabcA", 9], ["bBCAB", "ACcBBBBab", 13], ["bBCABBA", "BCabbCAB", 9], ["bBCAbAb", "caAbA", 8], ["bBCAbBba", "CbbBbbC", 10], ["bBCAcBcCA", "B", 16], ["bBCBa", "cbAAB", 8], ["bBCBaBCaa", "bA", 15], ["bBCBaC", "aABCAca", 10], ["bBCBabBC", "cCAc", 12], ["bBCC", "baAbacaA", 12], ["bBCCA", "aa", 9], ["bBCCCCABc", "babacAA", 13], ["bBCCCb", "ACc", 9], ["bBCCbAb", "a", 13], ["bBCaABB", "CbACac", 10], ["bBCaCCC", "ABccBC", 8], ["bBCaabcAA", "CBABCC", 13], ["bBCac", "BA", 7], ["bBCacbCA", "BaCBcCBC", 10], ["bBCb", "bBcCA", 4], ["bBCbAAaAC", "CCbCCA", 12], ["bBCbBa", "AbBaACCB", 10], ["bBCbb", "aCcaC", 9], ["bBCc", "ccB", 7], ["bBCcaAAa", "caabAbC", 13], ["bBa", "Bba", 2], ["bBa", "b", 4], ["bBa", "bBcAca", 6], ["bBa", "bCA", 3], ["bBa", "cACaAbBa", 10], ["bBa", "ccBcbcAcc", 15], ["bBaABCcC", "ABA", 12], ["bBaACBaB", "BCBBABbb", 10], ["bBaAaA", "Bb", 10], ["bBaAaBc", "BAbcaACBc", 8], ["bBaAaC", "cCBACaaA", 10], ["bBaAb", "bACbbacAB", 10], ["bBaBCAAaa", "CACBAc", 13], ["bBaBCCcA", "bb", 13], ["bBaBCaAaB", "bCAbCbbB", 10], ["bBaBCcBcB", "cabcc", 11], ["bBaBa", "BC", 8], ["bBaBbAbAB", "aBAAbCCBA", 12], ["bBaBbb", "BaCAbbb", 7], ["bBaC", "BAaAcb", 8], ["bBaCbcC", "bA", 11], ["bBaCcAA", "cAbb", 12], ["bBaaBb", "Caa", 8], ["bBaaBcBbb", "cABAaA", 15], ["bBaaCABa", "bcbaBcbC", 11], ["bBaaCa", "bAAC", 6], ["bBaabbbbc", "cbA", 16], ["bBaacaC", "bccaBC", 8], ["bBabAcBA", "ccaAbA", 9], ["bBabB", "BabcBBB", 8], ["bBabC", "ABc", 7], ["bBabaAC", "ccaCb", 12], ["bBac", "AbaAcAaB", 11], ["bBac", "BBCc", 3], ["bBacbBcb", "bCbc", 9], ["bBb", "AA", 6], ["bBb", "AbCaacc", 12], ["bBb", "CaaCCCaA", 16], ["bBb", "Ccac", 8], ["bBb", "a", 6], ["bBb", "aCC", 6], ["bBb", "baAC", 6], ["bBb", "cBA", 4], ["bBb", "caBaCBa", 11], ["bBbA", "C", 8], ["bBbA", "CBcBAA", 7], ["bBbA", "caBBABaBa", 13], ["bBbAAa", "aabbb", 10], ["bBbB", "AcbAA", 8], ["bBbB", "a", 8], ["bBbBBaBAc", "c", 16], ["bBbBBba", "Bbc", 10], ["bBbBb", "B", 8], ["bBbCA", "abBBcBBbc", 12], ["bBbCCca", "aCaCBCB", 11], ["bBba", "aAA", 7], ["bBba", "abB", 6], ["bBbaA", "cBCBCcCCB", 15], ["bBbaC", "bc", 7], ["bBbaCba", "aBBccAaB", 10], ["bBbabBAC", "bCBa", 11], ["bBbacbBCA", "BA", 14], ["bBbbCA", "bACbBAa", 8], ["bBbbbc", "c", 10], ["bBbcAAba", "cC", 14], ["bBbcBBCa", "b", 14], ["bBbcCC", "aacCBb", 10], ["bBbcCC", "acabCCb", 9], ["bBbcbA", "BcBCabB", 9], ["bBbcbA", "cBaCcCbB", 10], ["bBbcbBa", "BbA", 9], ["bBc", "A", 6], ["bBc", "ACA", 6], ["bBc", "CC", 5], ["bBc", "bCbA", 5], ["bBc", "bbcA", 3], ["bBcA", "BaCba", 7], ["bBcAACbB", "bCba", 10], ["bBcABCb", "BAca", 9], ["bBcAa", "Ca", 7], ["bBcAcBabB", "ccaCAb", 11], ["bBcAccBAc", "cAc", 12], ["bBcB", "Ca", 7], ["bBcBAbB", "A", 12], ["bBcBAca", "BcbC", 8], ["bBcBc", "C", 9], ["bBcCCbcb", "CAAcacCaC", 14], ["bBcCaAb", "CbbbCa", 9], ["bBcCbAAa", "c", 14], ["bBcaaaCAC", "ACBbc", 16], ["bBcb", "bCCac", 7], ["bBcba", "bba", 4], ["bBcbaB", "BaCCbc", 10], ["bBcbaBAbB", "AAb", 13], ["bBcbaBbAB", "c", 16], ["bBcc", "CcBABA", 10], ["bBcc", "aAc", 6], ["bBccACBbA", "CaCCCB", 12], ["bBccBc", "<PERSON><PERSON><PERSON>", 9], ["bBccC", "bAcc", 4], ["bBccb", "bccAbBbC", 10], ["bBccb", "caBAA", 10], ["bBccbBbCb", "AAAbbaBA", 15], ["bBccbBccC", "c", 16], ["bBccbb", "AaABa", 11], ["bBcccB", "bcCAca", 7], ["bC", "A", 4], ["bC", "ACBBaBa", 12], ["bC", "Abc", 3], ["bC", "BAAcb", 8], ["bC", "BAa", 5], ["bC", "BAcaBCAcc", 15], ["bC", "BCbcCA", 8], ["bC", "BacAcb", 10], ["bC", "CC", 2], ["bC", "CbBcAbcc", 13], ["bC", "aBaCA", 7], ["bC", "aCCbCABBB", 14], ["bC", "aaBBCAA", 11], ["bC", "aaCbaAcB", 13], ["bC", "acCcbcB", 11], ["bC", "b", 2], ["bC", "bBBBC", 6], ["bC", "bBb", 4], ["bC", "bC", 0], ["bC", "baAAcb", 9], ["bC", "bac", 3], ["bC", "bbBb", 6], ["bC", "c", 3], ["bC", "cABAac", 10], ["bC", "cABcACACa", 15], ["bC", "cAaCb", 8], ["bC", "cBbB", 6], ["bC", "cBcCbcc", 11], ["bC", "cBcCcAa", 11], ["bC", "cccCBcaba", 16], ["bCA", "BbCaCc", 7], ["bCA", "ac", 5], ["bCA", "bCB", 2], ["bCA", "ba", 3], ["bCA", "cbcbAc", 7], ["bCAAA", "Cc", 8], ["bCAAA", "cBabbA", 9], ["bCAAABc", "<PERSON><PERSON><PERSON>", 11], ["bCAABaCbc", "Cab", 12], ["bCAABccA", "AaacCBBcB", 13], ["bCAAC", "bBAaCcc", 7], ["bCAACaCa", "CcAACaC", 5], ["bCAAaBbCA", "CbaAcB", 13], ["bCAB", "bAAA", 4], ["bCABB", "bA", 6], ["bCABBCAA", "bBCbcCc", 11], ["bCABBcB", "bBbACbaBb", 11], ["bCABa", "AaAa", 6], ["bCABaBAcc", "CBbaCCba", 13], ["bCAC", "ABCcc", 6], ["bCAC", "caCa", 6], ["bCAC", "cbCcb", 6], ["bCACBA", "cbac", 10], ["bCACBB", "Bcb", 9], ["bCACa", "c", 9], ["bCACaaC", "Cacaaa", 6], ["bCACaaa", "cC", 11], ["bCACb", "B", 9], ["bCACbA", "Cb", 8], ["bCAaCB", "aCAbcCbB", 8], ["bCAaCbac", "a", 14], ["bCAaccaCC", "b", 16], ["bCAbAAcA", "bBBCAabAc", 10], ["bCAbab", "ca", 9], ["bCAbb", "CBAcBAcBB", 12], ["bCAc", "BaCAaaBa", 11], ["bCAcBA", "CBBcCCA", 10], ["bCAcC", "BbaccaCA", 10], ["bCAcC", "BcA", 6], ["bCAcc", "cbCAB", 6], ["bCB", "CCabaC", 9], ["bCB", "abaaAbbAC", 15], ["bCB", "bCAca", 6], ["bCB", "cBAB", 5], ["bCBA", "BccBbcAA", 10], ["bCBAAa", "c", 11], ["bCBBBCbc", "CAbbbB", 10], ["bCBBBcB", "BBaBCBbC", 10], ["bCBBa", "CccaaC", 9], ["bCBBbCc", "bBaAB", 10], ["bCBC", "AbaCcaCB", 10], ["bCBC", "CcbaCaA", 10], ["bCBC", "aCAbcbCC", 10], ["bCBCAB", "C", 10], ["bCBCACBb", "bBacBCaA", 11], ["bCBCBcbc", "bA", 14], ["bCBCaAc", "a", 12], ["bCBCbC", "A", 12], ["bCBa", "Ba", 4], ["bCBaAb", "CABCCcb", 10], ["bCBaabC", "ACb", 10], ["bCBabCa", "cAbaccCC", 11], ["bCBacABab", "ccAacAaCc", 11], ["bCBacaAB", "ACbABB", 10], ["bCBb", "aaAbAbbC", 11], ["bCBb", "cBabcA", 9], ["bCBb", "cBcccaA", 12], ["bCBbAaAb", "BabA", 11], ["bCBbAac", "CBcca", 8], ["bCBbAcC", "CBbBbb", 8], ["bCBbC", "CA", 8], ["bCBbaCCA", "BAAab", 13], ["bCBbabb", "BcCBA", 10], ["bCBbbCb", "ABcbAba", 10], ["bCBc", "bbBACc", 6], ["bCBcB", "bCAbccACb", 10], ["bCBcBBc", "b", 12], ["bCBca", "cAABaCCa", 11], ["bCBcabA", "CAcbbcBcB", 14], ["bCBcba", "ABAcbc", 8], ["bCBcccbC", "Cbbbc", 10], ["bCC", "A", 6], ["bCC", "ABBbCCC", 8], ["bCC", "AaC", 4], ["bCC", "BB", 5], ["bCC", "a", 6], ["bCC", "aCbBaaCB", 12], ["bCC", "b", 4], ["bCC", "bbBaCaCAc", 12], ["bCC", "cB", 5], ["bCCAACA", "bc", 11], ["bCCAAbAa", "aC", 14], ["bCCACBb", "BcAA", 10], ["bCCAc", "bcBCABabC", 10], ["bCCBACAa", "cCB", 11], ["bCCBC", "CbbbACC", 10], ["bCCBaBa", "BC", 11], ["bCCBbBAA", "ACBbbBc", 9], ["bCCCABcb", "BaBbbAb", 12], ["bCCCa", "aA", 9], ["bCCCbccc", "aCAA", 14], ["bCCa", "AabcB", 9], ["bCCa", "BccBa", 5], ["bCCa", "bCACAaBb", 8], ["bCCaA", "cbAbaAA", 8], ["bCCaAbBB", "CcC", 13], ["bCCaBa", "AcCabCAc", 9], ["bCCaCc", "abAcCA", 9], ["bCCaa", "cA", 8], ["bCCaaCaa", "cbcc", 14], ["bCCab", "B", 9], ["bCCaccbbB", "cC", 15], ["bCCb", "cBBaBB", 10], ["bCCbA", "bcBC", 6], ["bCCbACBBB", "CA", 14], ["bCCbB", "aaBcCbAA", 10], ["bCCbCc", "aACbC", 6], ["bCCbcA", "caACc", 10], ["bCCcAcAB", "aCAB", 10], ["bCCcc", "aACCcacB", 8], ["bCCccC", "aabAb", 12], ["bCa", "Cb", 4], ["bCa", "aBbb", 7], ["bCa", "bBACAbba", 10], ["bCa", "baba", 4], ["bCa", "cbacb", 7], ["bCa", "ccaCaCCc", 12], ["bCaA", "AABbbA", 9], ["bCaA", "CBc", 6], ["bCaAC", "ACaaABaCa", 10], ["bCaBAc", "bbbBcbcAc", 10], ["bCaBBBA", "caCcBBCcB", 12], ["bCaBBC", "cacbBC", 6], ["bCaBbCAC", "aaCAcCAbC", 11], ["bCaCAabc", "bBaB", 11], ["bCaCAcaC", "bcccaa", 8], ["bCaCCC", "cAbabbACA", 14], ["bCaCcB", "ABbbcCABC", 12], ["bCaCcac", "CBbCAA", 10], ["bCaa", "a", 6], ["bCaa", "aBC", 7], ["bCaabBC", "CCABcA", 10], ["bCabAacbC", "BAB", 14], ["bCabCc", "CA", 9], ["bCabaAAB", "A", 14], ["bCabbAacb", "Cba", 12], ["bCabbC", "CAacA", 9], ["bCac", "C", 6], ["bCac", "cC", 6], ["bCacAbBa", "a", 14], ["bCacBA", "abB", 8], ["bCacCcB", "aAabBBbC", 13], ["bCaccBAa", "CB", 12], ["bCb", "aCBc", 5], ["bCb", "ab", 4], ["bCb", "ccA", 5], ["bCbA", "ab", 6], ["bCbAAAB", "AccbcBaB", 10], ["bCbAB", "BcBB", 5], ["bCbACcbc", "b", 14], ["bCbAa", "acCBABB", 9], ["bCbAbcBc", "b", 14], ["bCbB", "b", 6], ["bCbBabC", "cbacBCaBc", 10], ["bCbBacaA", "aBAaBCaa", 11], ["bCbBbbbC", "a", 16], ["bCbBbcAb", "ba", 13], ["bCbCAbB", "CABBbb", 9], ["bCbCCCAcb", "a", 17], ["bCbCbb", "AAACCCB", 11], ["bCbCccc", "abbAbC", 11], ["bCbaACBbb", "bCCccaa", 13], ["bCbaAc", "BA", 9], ["bCbaaBc", "CbAAcaBc", 7], ["bCbac", "aAcBABbaA", 13], ["bCbac", "b", 8], ["bCbacABa", "cCaac", 10], ["bCbb", "bbCBabAC", 9], ["bCbbb", "AcBc", 8], ["bCbbba", "AaBbc", 9], ["bCbc", "ABb", 6], ["bCbc", "CCb", 4], ["bCbcB", "AbAab", 9], ["bCbcCBA", "BCbBacC", 9], ["bCbcCC", "BbbABA", 9], ["bCbcCbAC", "ccccC", 10], ["bCbca", "bcbBCBa", 6], ["bCbcaAAAA", "A", 16], ["bCbcaca", "bBBAcc", 8], ["bCbcbbBbC", "CaC", 14], ["bCc", "BabBBCb", 10], ["bCc", "CCBcAA", 8], ["bCc", "CCCcCBcC", 12], ["bCc", "aaAABCCaB", 14], ["bCc", "aabbbaaB", 14], ["bCc", "bccB", 3], ["bCcABC", "aAcBcCC", 10], ["bCcACCBa", "cac", 12], ["bCcACCaC", "AbcaC", 9], ["bCcB", "BBB", 5], ["bCcBA", "CcAbcCbaA", 11], ["bCcBB", "cBb", 5], ["bCcC", "ACBaB", 8], ["bCcCb", "ccbBac", 10], ["bCcCcCCCB", "BCAaAa", 15], ["bCca", "CCaaaBbc", 12], ["bCcaA", "cACB", 9], ["bCcaBbac", "bBa", 10], ["bCcaCc", "A", 11], ["bCcaCcC", "bcCB", 8], ["bCcaaa", "B", 11], ["bCcaabCB", "bbAbcaAA", 13], ["bCcacBCA", "aACBccC", 11], ["bCcbAaBBc", "c", 16], ["bCcbCCbb", "bBbacaC", 11], ["bCcbbaCab", "BC", 15], ["bCcbbc", "bBBCB", 9], ["bCcbc", "acBCAcc", 9], ["bCcc", "CAbacbA", 10], ["bCccbbbc", "bBABAaBAc", 13], ["ba", "ABbbC", 8], ["ba", "BA", 2], ["ba", "BAABBb", 10], ["ba", "BacAAbA", 11], ["ba", "BbAaaCACc", 14], ["ba", "BcCAAC", 10], ["ba", "Bcc", 5], ["ba", "CCccCCa", 12], ["ba", "CaAbAb", 9], ["ba", "Cba", 2], ["ba", "Cca", 4], ["ba", "Ccac", 6], ["ba", "a", 2], ["ba", "aBABA", 8], ["ba", "aBaABB", 9], ["ba", "aCcaCbAbB", 15], ["ba", "bBC", 4], ["ba", "bBCC", 6], ["ba", "baABc", 6], ["ba", "baCAbB", 8], ["ba", "cA", 3], ["ba", "cABA", 6], ["ba", "cBbCCaB", 10], ["ba", "cBc", 5], ["ba", "cCCBcC", 11], ["ba", "caBBCAca", 13], ["ba", "cbBbb", 8], ["ba", "ccb", 6], ["baA", "AbAAaCaa", 11], ["baA", "B", 5], ["baA", "BBB", 5], ["baA", "a", 4], ["baA", "aCaaB", 7], ["baA", "cAaBCBaCB", 15], ["baA", "cBBcccca", 14], ["baA", "ccABbC", 10], ["baAA", "BBAaCacBC", 14], ["baAA", "cbbbCcbac", 15], ["baAAA", "CCcC", 10], ["baAAABbC", "BcACCbBa", 11], ["baAAAbAC", "caBb", 12], ["baAAa", "cACaAB", 8], ["baAAc", "bC", 7], ["baAAcCa", "CCABACcBC", 12], ["baABBcc", "cbcaaABc", 9], ["baABCaCA", "CABCbCAC", 8], ["baABaBaAA", "CAabb", 13], ["baABbAcC", "BcCBCCC", 10], ["baAC", "bCbAbBCA", 10], ["baACCCcaA", "Cb", 16], ["baACca", "bBcB", 8], ["baAa", "AbAab", 6], ["baAaCAcc", "acAbB", 11], ["baAbCAc", "baAacAac", 5], ["baAbaAaba", "cBA", 15], ["baAbabAb", "CC", 16], ["baAcABAC", "aabCBbBcB", 13], ["baAcB", "bbcb", 5], ["baAcCcaaa", "ccAA", 12], ["baAca", "aaAcCc", 6], ["baAcaA", "C", 11], ["baAcab", "B", 11], ["baAcc", "AbacACCCb", 10], ["baB", "BbccbbBbC", 14], ["baB", "acbAB", 5], ["baB", "accCa", 10], ["baB", "cA", 5], ["baB", "ca", 4], ["baB", "ccBACC", 10], ["baBA", "aa", 5], ["baBAC", "aA", 6], ["baBAbccc", "BCBaC", 11], ["baBB", "bCCaAca", 10], ["baBB", "cacbb", 6], ["baBBC", "BbBAA", 7], ["baBBCAAc", "bBaabAccc", 11], ["baBBCcBCa", "ABbBca", 9], ["baBBaa", "BCABAa", 6], ["baBBbAb", "bACa", 10], ["baBBbCbBC", "cBABabC", 11], ["baBBcaBcC", "CCCb", 16], ["baBBcb", "BBCbCCb", 9], ["baBCcbbA", "BBacCC", 11], ["baBa", "aA", 5], ["baBa", "bBbcAAb", 10], ["baBa", "cCbbbcBC", 12], ["baBaABBb", "bBcB", 10], ["baBaaC", "A", 11], ["baBaaC", "bBbAcc", 7], ["baBbC", "ACAc", 8], ["baBbcCbaC", "C", 16], ["baBc", "BAAbCa", 8], ["baBcAAC", "aBAAbACa", 8], ["baBcC", "Abc", 6], ["baBcCCBBA", "bbacBaaaC", 14], ["baBcCCBb", "Bccacb", 9], ["baBcaAc", "bc", 10], ["baBcbccA", "bCCa", 11], ["baBccBC", "C", 12], ["baBccccb", "AAbbaBCb", 11], ["baC", "AABBAb", 10], ["baC", "AB", 5], ["baC", "BBa", 5], ["baC", "CAaCCcbb", 12], ["baC", "CC", 4], ["baC", "caBcB", 7], ["baCA", "Bc", 6], ["baCACBCac", "a", 16], ["baCAaAaBa", "bcBcCab", 12], ["baCAbc", "CCc", 8], ["baCB", "B", 6], ["baCBACbBa", "b", 16], ["baCBC", "CAAcaCaB", 12], ["baCBbCB", "abc", 9], ["baCCCCBB", "b", 14], ["baCCCcC", "bbcAcABac", 13], ["baCCbAB", "CbBAC", 9], ["baCCc", "BcACCa", 6], ["baCCc", "ab", 8], ["baCa", "BAabaACcC", 12], ["baCa", "CCAABc", 11], ["baCaAAB", "aCB", 8], ["baCaCa", "caC", 7], ["baCaCcbAb", "bCB", 13], ["baCb", "CAbA", 7], ["baCbCAB", "aCBccCcC", 11], ["baCbCC", "ACCbA", 8], ["baCbbCb", "bACAc", 8], ["baCbc", "aaAbAb", 8], ["baCbcACBc", "CACcBb", 11], ["baCc", "BbcaaB", 8], ["baCcA", "bbbBcB", 8], ["baCcaaC", "cCCA", 10], ["baCcb", "BaCacAa", 7], ["baa", "ACbBca", 8], ["baa", "BaC", 3], ["baaABBcBb", "ABcacAC", 14], ["baaAC", "aCAb", 6], ["baaAaBB", "ccABBac", 12], ["baaAb", "bAcBabC", 8], ["baaAcA", "B", 11], ["baaBAA", "CbabaCCAA", 8], ["baaBAAabB", "cCB", 16], ["baaBBBb", "aC", 12], ["baa<PERSON><PERSON><PERSON>", "cAaB", 9], ["baaBb", "CBbaBAbC", 9], ["baaBcaa", "CcCCbb", 13], ["baaCCcAB", "cCA", 11], ["baa<PERSON>aac", "aCAaCCbA", 11], ["baaCbA", "ccBAAcC", 12], ["baaa", "CaCbCb", 10], ["baaa", "bBbBAAC", 10], ["baaaAc", "CBBbBBB", 13], ["baaaCA", "aab", 8], ["baaaaabB", "BCbbccA", 15], ["baaacac", "ABa", 11], ["baab", "aABbABC", 11], ["baabA", "cACC", 9], ["baabaBaab", "aCcA", 15], ["baac", "ACBbca", 10], ["baacACa", "bccCB", 8], ["baacBbA", "AaaCCbaaA", 9], ["baacCc", "CACBAA", 11], ["baacaCcb", "BBbbbAaBA", 16], ["baacabBAA", "aaaaBaB", 9], ["bab", "Cc", 6], ["bab", "CcBC", 7], ["bab", "a", 4], ["bab", "aabbAaBca", 13], ["babA", "AB", 6], ["babA", "bCb", 4], ["babA", "cccABCc", 12], ["babAAaBC", "Cb", 14], ["babAaAA", "bc", 12], ["babAaBA", "BbcBAbC", 10], ["babBBB", "b", 10], ["bab<PERSON>a", "BB", 7], ["babBbAbAC", "CbabB", 12], ["babC", "BCB", 6], ["babC", "CABB", 6], ["babCABaa", "BbcCAab", 9], ["babCB", "bcCCaABBC", 12], ["babCBb", "AAcbb", 7], ["babCCCc", "cCAbbca", 12], ["babCCa", "c", 11], ["babCabCab", "caac", 13], ["babCbcc", "AaAAcA", 10], ["baba", "abBcbAc", 9], ["babaBA", "CbcBbBabB", 11], ["babaC", "CbAaAA", 8], ["babaCBaa", "BbCa", 9], ["babaa", "C", 10], ["babaaCBCC", "BbaaAc", 10], ["babb", "AcB", 6], ["babbA", "BabaAAaB", 9], ["babbCaaCB", "cCaAAaBc", 15], ["babbCbC", "AA", 13], ["babbCc", "acbACaB", 10], ["babbbbA", "acA", 10], ["babbcccAA", "bAA", 12], ["babcacA", "c", 12], ["bac", "CBC", 5], ["bac", "CcBc", 6], ["bac", "Ccbca", 8], ["bac", "aacAc", 6], ["bac", "ba", 2], ["bacABcBc", "ABccc", 8], ["bacAbca", "aBCACc", 9], ["bacBCbBb", "ccAbcABA", 12], ["bacBCc", "cBcBB", 8], ["bacBaBc", "AcbcAcA", 10], ["bacC", "bbAbaCAB", 11], ["bacCACCA", "b", 14], ["bacCAa", "b", 10], ["bacCCCccC", "CCBbcacA", 13], ["bacCbaBcB", "Cab", 13], ["bacCcBC", "aBcbcccB", 10], ["bacaCC", "BAaAbcAC", 10], ["bacaCcA", "BcBAc", 9], ["bacabaAA", "AAACCBaC", 13], ["bacacBa", "BCAAaAA", 11], ["bacbABaB", "bCABBAc", 10], ["bacbCAbbb", "BaA", 13], ["bacc", "aAcAA", 7], ["baccAA", "C", 11], ["baccAa", "abCCB", 9], ["baccBAA", "ccCCbcCC", 13], ["baccBBc", "CAb", 12], ["baccBbBaa", "AbcaCcbb", 12], ["baccCaCA", "CAccb", 11], ["baccaBc", "AccABCbCC", 11], ["baccccCB", "cC", 12], ["bb", "A", 4], ["bb", "AABcCbCB", 13], ["bb", "AC", 4], ["bb", "Aabc", 6], ["bb", "AbC", 4], ["bb", "AbCC", 6], ["bb", "AbCCbbB", 10], ["bb", "Acaa", 8], ["bb", "B", 3], ["bb", "BAbcaB", 9], ["bb", "BCAC", 7], ["bb", "Bbccc", 7], ["bb", "CC", 4], ["bb", "a", 4], ["bb", "aBcCcc", 11], ["bb", "aaBBCc", 10], ["bb", "acCc", 8], ["bb", "accBa", 9], ["bb", "bA", 2], ["bb", "bAc", 4], ["bb", "bCbAcbaC", 12], ["bb", "baAB", 5], ["bb", "cAbBAbC", 10], ["bb", "cB", 3], ["bb", "cBC", 5], ["bb", "cbAacCaca", 16], ["bb", "cbBbABC", 10], ["bbA", "ABC", 5], ["bbA", "BBCabAA", 9], ["bbA", "BcCC", 7], ["bbA", "CB", 5], ["bbA", "bBbaababa", 13], ["bbA", "cCabBCAc", 11], ["bbAA", "B", 7], ["bbAA", "CC", 8], ["bbAAaBb", "AcC", 12], ["bbAAb", "BBBaaACAb", 10], ["bbAAc", "bBaCb", 6], ["bbAAcAca", "CCbBaccCB", 12], ["bbAAcBca", "bcA", 11], ["bbAAcbCb", "bAabCBAAC", 12], ["bbAB", "AaABCCB", 10], ["bbAB", "AbabABcC", 8], ["bbAB", "cBb", 6], ["bbABACAbC", "Ccabbc", 13], ["bbABBC", "CaAA", 10], ["bbABCBA", "ACb", 9], ["bbABCBCbc", "b", 16], ["bbABab", "CAcaba", 8], ["bbABc", "Bac", 6], ["bbABcBBc", "Aa", 14], ["bbABcb", "c", 10], ["bbACABB", "acC", 12], ["bbACCcCbb", "CCcC", 10], ["bbAa", "CbCaC", 6], ["bbAa", "cBcBBC", 10], ["bbAaC", "bBcCcAB", 10], ["bbAaCaBAB", "aabBc", 13], ["bbAabBcca", "BbaabB", 8], ["bbAaccbAb", "aCbBAAc", 14], ["bbAb", "B", 7], ["bbAb", "baaCABAa", 11], ["bbAb", "caAABCaac", 15], ["bbAbAcaCA", "cacab", 13], ["bbAbB", "ccAaCc", 10], ["bbAbBc", "AbcAbaBa", 8], ["bbAbC", "BBa", 7], ["bbAbC", "BcAcaCaaa", 13], ["bbAbcBA", "cAbbcBC", 8], ["bbAc", "bB", 5], ["bbAcBabb", "b", 14], ["bbAcCaAC", "CccacCABB", 12], ["bbAcaA", "caCabBaAB", 13], ["bbAcabA", "bBcbCb", 9], ["bbB", "Ac", 6], ["bbB", "aCbba", 6], ["bbB", "cAcbbBb", 8], ["bbBA", "B", 6], ["bbBACc", "AcCCBa", 12], ["bbBAab", "caAaBCbCb", 14], ["bbBAbaa", "aa", 10], ["bbBBA", "A", 8], ["bbBBaaC", "aCB", 12], ["bbBBbc", "ab", 10], ["bbBCCCAbA", "aAcCBBc", 14], ["bbBCCaa", "AACaAbAA", 14], ["bbBCaaAC", "bBAc", 9], ["bbBCcbcbc", "BBBaCcbBc", 7], ["bbBa", "BBcbc", 7], ["bbBa", "Cc", 8], ["bbBaAcBaC", "Bcc", 13], ["bbBaCacbC", "BAAaBcCa", 13], ["bbBbA", "Bbca", 6], ["bbBbB", "bBCaCbABa", 11], ["bbBbBc", "ABbBbc", 5], ["bbBbCcBa", "ba", 12], ["bbBbaaaBB", "C", 18], ["bbBbabc", "ABACBBacC", 11], ["bbBbbcCA", "AaabaCabb", 15], ["bbBbc", "Bab", 7], ["bbBc", "AaCcBCbCb", 15], ["bbBcBCA", "acaba", 11], ["bbBcCc", "BAc", 8], ["bbBcab", "cAabaC", 10], ["bbBcbAba", "b", 14], ["bbBccBaCB", "AcbAaCc", 12], ["bbC", "A", 6], ["bbC", "AAccbCBAA", 14], ["bbC", "BCAC", 5], ["bbC", "BacBCA", 8], ["bbC", "acBbCB", 7], ["bbCAAb", "aBAAcAc", 9], ["bbCABbc", "aCa", 11], ["bbCACA", "cBc", 10], ["bbCAaBb", "babcacCA", 10], ["bbCAaa", "cBaBbCa", 11], ["bbCBCca", "bC", 10], ["bbCBacAB", "aC", 13], ["bbCBcBBcb", "bCCCaa", 13], ["bbCC", "cAbBccA", 9], ["bbCCAA", "b", 10], ["bbCCCBc", "CCBAcbab", 14], ["bbCCaccaA", "ba", 14], ["bbCCbBA", "A", 12], ["bbCCcABa", "CBBbC", 13], ["bbCCcCc", "BbaabbaCc", 11], ["bbCa", "BC", 5], ["bbCa", "abBbA", 6], ["bbCaAB", "CCACcabAC", 12], ["bbCaBabbC", "CaBABbacC", 10], ["bbCaaC", "BbaBCcbCA", 11], ["bbCaaCA", "BcBBB", 12], ["bbCabA", "BaBBA", 8], ["bbCabcAA", "bc", 12], ["bbCbAaBaA", "CbAaAAc", 9], ["bbCbCB", "AaBB", 9], ["bbCbaACb", "Abbc", 11], ["bbCbcaBA", "AB", 13], ["bbCc", "aBaC", 6], ["bbCcAA", "ccaBAabBA", 14], ["bbCcBaA", "aAbCCabAb", 11], ["bbCcaABc", "a", 14], ["bbCcc", "BBAAA", 8], ["bbCccBCCa", "cacaCc", 12], ["bba", "ABBcccabc", 14], ["bba", "Bb", 3], ["bba", "C", 6], ["bba", "CBACA", 8], ["bba", "aCCAABCcb", 17], ["bba", "bbba", 2], ["bbaA", "CaCcCbb", 14], ["bbaACCB", "bAcAccABC", 10], ["bbaAaaacc", "CbaACB", 11], ["bbaAcBA", "cBbcaa", 10], ["bbaB", "Acc", 8], ["bbaBAccC", "cBBAb", 11], ["bbaBBCc", "C", 12], ["bbaBCaAb", "cCbA", 12], ["bbaBaAc", "cABCaA", 9], ["bbaBbCbCb", "aBccbBC", 11], ["bbaBbaAbC", "acaccca", 15], ["bbaBc", "bAbbCc", 6], ["bbaCbCB", "CCCA", 10], ["bbaCbbC", "c", 13], ["bbaCc", "CbCaBAaC", 11], ["bbaCcBccb", "C", 16], ["bbab", "ACB", 7], ["bbab", "CbbCbCbcC", 12], ["bbabAbca", "cBbbAB", 10], ["bbabBcc", "CAa", 12], ["bbabC", "abBAaAba", 9], ["bbabC", "cBb", 7], ["bbabc", "AAccbABAc", 12], ["bbacC", "aabaacA", 8], ["bbacacb", "acbCBaAcC", 12], ["bbacccaa", "AbBABACaa", 9], ["bbb", "ABC", 5], ["bbb", "C", 6], ["bbb", "aCBc", 7], ["bbbA", "bccbbcCCB", 12], ["bbbABA", "a", 11], ["bbbACCCa", "CBbcC", 10], ["bbbAaBB", "B", 12], ["bbbAaCac", "cCc", 12], ["bbbAcbcC", "aBBACacb", 9], ["bbbB", "ABAc", 7], ["bbbB", "AbABcABAB", 12], ["bbbB", "BB", 5], ["bbbB", "aC", 8], ["bbbB", "cCCcBcbCb", 14], ["bbbBBBBCb", "BcaBa", 14], ["bbbBbA", "bbA", 6], ["bbbBbCBcB", "bbcBcCC", 9], ["bbbCa", "CCB", 8], ["bbbCccAA", "bBAa", 10], ["bbbaABaac", "abaCbCB", 13], ["bbbaAa", "b", 10], ["bbbaB", "AC", 9], ["bbbaa", "CBBccC", 10], ["bbbaaBba", "Baa", 11], ["bbbaacbb", "cBbaC", 10], ["bbbacBbBc", "bcbcbCCC", 10], ["bbbb", "AbaACaC", 12], ["bbbbbAB", "aaAaCb", 13], ["bbbbc", "BBbC", 5], ["bbbcAB", "aBAbBBa", 11], ["bbbcAcCAC", "AaBbcCCcb", 12], ["bbbcB", "bcCc", 6], ["bbbcBa", "CAAccB", 10], ["bbbcCB", "aAbBABa", 10], ["bbbca", "cBbcabCC", 9], ["bbbcbBAbc", "AAacbcc", 12], ["bbbcbcbc", "<PERSON><PERSON><PERSON>", 10], ["bbc", "ACBaABaCC", 15], ["bbc", "BcCBaBcCc", 14], ["bbc", "CcB", 6], ["bbc", "bCCcBAac", 11], ["bbc", "c", 4], ["bbcA", "AAC", 7], ["bbcACCca", "bAABA", 11], ["bbcAbBa", "CaaA", 11], ["bbcAbca", "CCCCc", 11], ["bbcAcbBab", "CBacCca", 12], ["bbcB", "BaAbbccBb", 10], ["bbcBAa", "baaacc", 10], ["bbcBCa", "CaBabc", 11], ["bbcCA", "CAb", 8], ["bbcCBACB", "bcBBCaBc", 10], ["bbcCBbAA", "CcAc", 12], ["bbcCC", "BcbCc", 6], ["bbcCCcB", "b", 12], ["bbca", "AbbcaAcCa", 10], ["bbca", "acaBbbB", 11], ["bbcaACCab", "aACBab", 8], ["bbcaAb", "b", 10], ["bbcaCab", "B", 13], ["bbcaaBCA", "BCaaC", 8], ["bbcaacCC", "bCCcBACB", 11], ["bbcab", "bABaa", 6], ["bbcabAB", "BcC", 11], ["bbcb", "abAA", 6], ["bbcbB", "bCbAab", 7], ["bbcbBccAa", "bA", 14], ["bbcbCcAB", "cacaCCaBA", 10], ["bbcc", "B", 7], ["bbcc", "CcBBaABa", 14], ["bbccAcBCB", "CbacCAaB", 11], ["bbccC", "bac", 6], ["bbccCa", "baacc", 7], ["bbccCcA", "CbbbC", 10], ["bbccaca", "bb", 10], ["bbccb", "ABCA", 8], ["bbccbB", "Cbccbcb", 5], ["bbccbBB", "cbCB", 8], ["bc", "ACcCb", 8], ["bc", "Aa", 4], ["bc", "Ac", 2], ["bc", "BBAA", 7], ["bc", "BcA", 3], ["bc", "BcbBaCaCB", 15], ["bc", "CACcCBc", 11], ["bc", "CBBaAccB", 13], ["bc", "CaBa", 7], ["bc", "CcCaaABa", 14], ["bc", "a", 4], ["bc", "aAA", 6], ["bc", "aACBcB", 9], ["bc", "aBacc", 7], ["bc", "aBbAa", 8], ["bc", "acb", 4], ["bc", "b", 2], ["bc", "bA", 2], ["bc", "bBCcbA", 8], ["bc", "c", 2], ["bc", "cBb", 5], ["bc", "caaabc", 8], ["bc", "ccAB", 6], ["bcA", "ABbA", 5], ["bcA", "BBB", 5], ["bcA", "aabaAAB", 10], ["bcA", "acacbc", 9], ["bcA", "cbaACAB", 9], ["bcAA", "BaCc", 7], ["bcAABAab", "CB", 13], ["bcAABbbBa", "ABcccc", 14], ["bcAABcb", "c", 12], ["bcAAbc", "B", 11], ["bcAB", "a", 7], ["bcAB", "bcB", 2], ["bcABAABcB", "Cc", 15], ["bcABAbBC", "c", 14], ["bcABAcB", "bcCBCAbCa", 9], ["bcAC", "AAaC", 5], ["bcACABab", "c", 14], ["bcACBbBaC", "baaAbBbB", 10], ["bcACa", "bbaAcCBB", 10], ["bcACbCa", "aaaBBAbC", 13], ["bcAa", "BC", 6], ["bcAa", "b", 6], ["bcAaAabA", "abBB", 13], ["bcAaBABA", "CABac", 10], ["bcAaBAaC", "AaC", 10], ["bcAaBCc", "ca", 10], ["bcAaaC", "cbA", 9], ["bcAabc", "BAABb", 7], ["bcAb", "BcbbbA", 7], ["bcAbAAAAb", "abCBaAABc", 11], ["bcAbACaB", "cCaCAC", 10], ["bcAbacAbc", "BccaBBCbb", 12], ["bcAc", "BCBcaBCaB", 13], ["bcAca", "A", 8], ["bcAcba", "CcAc", 6], ["bcAcbccB", "<PERSON><PERSON><PERSON>", 10], ["bcAccA", "bbabbAcC", 11], ["bcAccab", "CCACcbcC", 10], ["bcB", "AaCbCccbb", 13], ["bcB", "BaAACbA", 11], ["bcB", "CAAABA", 10], ["bcB", "aaAaBccC", 13], ["bcB", "bAAAACCA", 13], ["bcB", "cAbcb", 5], ["bcBA", "bBBAb", 4], ["bcBAAc", "BccA", 7], ["bcBACabcB", "abBA", 14], ["bcBAab", "baaaBbcc", 11], ["bcBAcAC", "aCCC", 10], ["bcBBC", "c", 8], ["bcBBCbAbA", "bcCCb", 10], ["bcBBaA", "ABCBb", 9], ["bcBBabABb", "BcbaBBBCa", 11], ["bcBBbB", "bbbCC", 8], ["bcBCAaaaB", "CC", 15], ["bcBCBC", "aCB", 8], ["bcBCCAB", "ABCbc", 10], ["bcBCCB", "cAb", 9], ["bcBCCcbA", "c", 14], ["bcBCa", "CBccAbA", 10], ["bcBa", "abaBbcaba", 11], ["bcBaBB", "CB", 9], ["bcBaCc", "aaCBa", 9], ["bcBaa", "BcBB", 5], ["bcBabcbc", "accAbcB", 8], ["bcBac", "BB", 7], ["bcBbAB", "aCAbA", 7], ["bcBbACCB", "BAbcCc", 10], ["bcBbACCac", "CB", 15], ["bcBbBCB", "BcCbb", 8], ["bcBbbAcac", "ACbccac", 9], ["bcBbc", "caC", 7], ["bcBc", "A", 8], ["bcBcaa", "BbbCAaBB", 10], ["bcBccBCBA", "ABaCCAba", 13], ["bcBccbcC", "aC", 14], ["bcC", "ACabb", 9], ["bcC", "Ac", 4], ["bcC", "CACac", 8], ["bcC", "CAaCbCbb", 13], ["bcC", "CacBAbbac", 15], ["bcC", "a", 6], ["bcC", "abBBcB", 8], ["bcC", "acaAAaCbb", 14], ["bcC", "bBCaBBC", 9], ["bcC", "bbbCCABb", 11], ["bcC", "cBBBBCac", 13], ["bcC", "cCAAb", 8], ["bcC", "ccBcB", 7], ["bcCAAA", "CCCBbBAC", 11], ["bcCAaA", "AbCabC", 9], ["bcCAb", "CbBccBC", 9], ["bcCAcAa", "cB", 12], ["bcCAcbC", "AcAAbc", 7], ["bcCB", "a", 8], ["bcCBAB", "AaBAcB", 8], ["bcCBBcb", "CaCbaa", 11], ["bcCBC", "cb", 7], ["bcCBaCAAc", "aBb", 16], ["bcCBaCb", "aaaacC", 11], ["bcCCAbB", "BBcCAcA", 8], ["bcCCa", "BACbBaAcC", 13], ["bcCCaa", "cBcC", 9], ["bcCCcbA", "AAaAcCCA", 12], ["bcCa", "bCCC", 3], ["bcCaaAB", "cAAc", 9], ["bcCaacC", "BbcaccCCB", 10], ["bcCab", "BcCABbCAC", 10], ["bcCacAA", "CcbB", 10], ["bcCacCaa", "ABc", 14], ["bcCacba", "Bc", 11], ["bcCb", "aCbcBbCc", 10], ["bcCbB", "bAbCc", 8], ["bcCbBCbaB", "BC", 14], ["bcCbaa", "BbAACBAaC", 10], ["bcCbbc", "baAA", 10], ["bcCc", "AAA", 8], ["bcCcBB", "BaAccBC", 8], ["bcCcaCCca", "AaCc", 12], ["bcCcbAAc", "Ca", 13], ["bca", "AB", 6], ["bca", "AcaaBC", 8], ["bca", "CACBbcC", 10], ["bca", "bccb", 4], ["bcaA", "aBaCBBAa", 12], ["bcaAAbCbC", "CCcAA", 13], ["bcaABAa", "Ac", 12], ["bcaACb", "bAbCaCB", 8], ["bcaAa", "BbCcc", 9], ["bcaAaaAC", "acaaABc", 8], ["bcaAba", "BabBAaccb", 14], ["bcaB", "bCBCBAA", 9], ["bcaBBAC", "Cb", 12], ["bcaCA", "ACA", 5], ["bcaCA", "aB", 8], ["bcaCAABcB", "bBAbCcbc", 12], ["bcaCB", "AbaACbaC", 10], ["bcaCBa", "BaAbAB", 9], ["bcaCa", "cCbccbA", 9], ["bcaCbBA", "bcBabAb", 8], ["bcaCbCbaA", "bbCBb", 11], ["bcaCcB", "cAab", 8], ["bcaa", "CC", 7], ["bcaa", "aaBA", 7], ["bcaaA", "BAAacaAba", 11], ["bcaaABBA", "baa", 10], ["bcaaAcbC", "aBACAcaab", 13], ["bcaaBaAb", "b", 14], ["bcaacb", "acCb", 7], ["bcab", "a", 6], ["bcab", "cCAabCBCb", 13], ["bcabAA", "BAAcca", 9], ["bcabC", "Aac", 7], ["bcabb", "aAaA", 8], ["bcac", "CCAAbCbC", 12], ["bcb", "A", 6], ["bcb", "aBabCcB", 9], ["bcb", "abACACc", 11], ["bcbA", "AcbAcbCaB", 11], ["bcbAAA", "a", 11], ["bcbAAAB", "a", 13], ["bcbACB", "CBbcbCca", 9], ["bcbACCA", "AabaACAcB", 11], ["bcbACCC", "c", 12], ["bcbAbCbB", "caA", 12], ["bcbAc", "BACCccbCb", 13], ["bcbBA", "baaaa", 7], ["bcbBAABCa", "cBAcaa", 10], ["bcbCAaAC", "bB", 13], ["bcbCAc", "caccbaABC", 11], ["bcbCB", "c", 8], ["bcbCcCa", "CCCbACb", 11], ["bcbaAACb", "aBAcA", 11], ["bcbaBBB", "AA", 13], ["bcbaccb", "ACACCB", 9], ["bcbb", "cBcACA", 9], ["bcbb", "cCCBaaA", 12], ["bcbbABC", "CAAaa", 11], ["bcbbB", "ABB", 7], ["bcbbBc", "cCc", 8], ["bcbbCBabA", "cCccbccBA", 12], ["bcbc", "AAA", 8], ["bcbc", "Ca", 7], ["bcbcAC", "bBAaaacb", 12], ["bcc", "C", 5], ["bcc", "CaACa", 9], ["bcc", "bACb", 5], ["bcc", "bbcCcAa", 8], ["bccA", "C", 7], ["bccA", "aAb", 8], ["bccA", "ab", 8], ["bccABAba", "acacC", 13], ["bccAC", "BbbBcA", 8], ["bccAbaA", "aAc", 12], ["bccAbcbcb", "CbbcbbA", 11], ["bccB", "BcacaCB", 7], ["bccB", "cAcb", 5], ["bccBABc", "bAacbb", 10], ["bccBBB", "aAbbbbC", 11], ["bccBBCbBa", "bbbaabCbB", 11], ["bccBBc", "cCcbac", 6], ["bccBBccA", "bB", 12], ["bccBCCAC", "bABBCAabC", 9], ["bccBCCBc", "ACBaa", 13], ["bccBa", "CAcbB", 7], ["bccBbA", "aBCcBBBC", 9], ["bccCA", "BaBAAba", 11], ["bccCAbbB", "ccC", 10], ["bccCBab", "c", 12], ["bccCCAaBB", "bBa", 14], ["bccCaBCC", "Bb", 14], ["bcca", "AaAaabA", 12], ["bccaCBb", "bAbcabA", 9], ["bccaCCC", "CCBCBbAAA", 16], ["bccaa", "C", 9], ["bccaab", "cAcc", 9], ["bccbAbab", "b", 14], ["bccbBCcCA", "B", 16], ["bccbBb", "ccba", 6], ["bccbC", "bBC", 5], ["bccbbCC", "bBccA", 9], ["bccbbbCB", "BbcABC", 10], ["bccbccAc", "bBC", 12], ["bccc", "ACcca", 5], ["bcccAA", "bCBAbca", 10], ["bcccAaBca", "BaC", 14], ["bcccBBCaA", "bcAcaCcb", 10], ["bcccC", "aAAaa", 10], ["bcccCCCbc", "Bac", 15], ["c", "A", 2], ["c", "AA", 4], ["c", "AABacBC", 12], ["c", "AAbBBaBB", 16], ["c", "AB", 4], ["c", "ABA", 6], ["c", "ABBaCCBBB", 17], ["c", "ABBbBBCBA", 17], ["c", "ABbCb", 9], ["c", "ABbaaba", 14], ["c", "AC", 3], ["c", "ACABa", 9], ["c", "ACCB", 7], ["c", "ACCcbCCbC", 16], ["c", "ACbBBC", 11], ["c", "Aa", 4], ["c", "AaABA", 10], ["c", "Aaa", 6], ["c", "AaaaBbBC", 15], ["c", "AaabCAb", 13], ["c", "Ab", 4], ["c", "AbaCAAA", 13], ["c", "AcCACaAA", 14], ["c", "AcCCBAcaB", 16], ["c", "Acb", 4], ["c", "AcccbCacB", 16], ["c", "B", 2], ["c", "BA", 4], ["c", "BAAAAbB", 14], ["c", "BACA", 7], ["c", "BACbAC", 11], ["c", "BAbBbAc", 12], ["c", "BAbb", 8], ["c", "BAcBb", 8], ["c", "BB", 4], ["c", "BBAACAb", 13], ["c", "BBABBC", 11], ["c", "BBBcBccc", 14], ["c", "BBbbc", 8], ["c", "BBbcbbac", 14], ["c", "BBcBc", 8], ["c", "BC", 3], ["c", "BCA", 5], ["c", "BCAAcBC", 12], ["c", "BCAbCb", 11], ["c", "BCBc", 6], ["c", "BCa", 5], ["c", "BCcbaB", 10], ["c", "Ba", 4], ["c", "BaAaaA", 12], ["c", "BaBacca", 12], ["c", "BaCCBaCAC", 17], ["c", "BaaBcAcBb", 16], ["c", "BaaccC", 10], ["c", "BabCcAAcA", 16], ["c", "BbABABaBC", 17], ["c", "BbCa", 7], ["c", "BbaAacbb", 14], ["c", "BbaCAAcBa", 16], ["c", "BbcBb", 8], ["c", "Bbcc", 6], ["c", "Bc", 2], ["c", "BcBa", 6], ["c", "Bcb", 4], ["c", "Bcccb", 8], ["c", "C", 1], ["c", "CB", 3], ["c", "CBABBC", 11], ["c", "CBAcCA", 10], ["c", "CBBAbbA", 13], ["c", "CBCBb", 9], ["c", "CBCaBa", 11], ["c", "CBb", 5], ["c", "CCCBaaCB", 15], ["c", "CCCCCC", 11], ["c", "Ca", 3], ["c", "CaAb", 7], ["c", "CaCa", 7], ["c", "Cab", 5], ["c", "CabC", 7], ["c", "CbAC", 7], ["c", "CbabCAaAc", 16], ["c", "CcAABA", 10], ["c", "CccaaAa", 12], ["c", "a", 2], ["c", "aAAa", 8], ["c", "aAAc", 6], ["c", "aACbcB", 10], ["c", "aAbACcCA", 14], ["c", "aAbab", 10], ["c", "aB", 4], ["c", "aBAC", 7], ["c", "aBBbbc", 10], ["c", "aBaaA", 10], ["c", "aBb", 6], ["c", "aBcABcCCb", 16], ["c", "aBcBBCBbc", 16], ["c", "aCa", 5], ["c", "aa", 4], ["c", "aaCBCbC", 13], ["c", "aabcBAAcc", 16], ["c", "aabcBcA", 12], ["c", "aacABB", 10], ["c", "aacb", 6], ["c", "aaccAca", 12], ["c", "ab", 4], ["c", "abAa", 8], ["c", "abCAcC", 10], ["c", "abCCAB", 11], ["c", "abCc", 6], ["c", "ac", 2], ["c", "accbaBBAB", 16], ["c", "b", 2], ["c", "bAAccBAa", 14], ["c", "bAaBAbcCc", 16], ["c", "bAcacAbb", 14], ["c", "bBAaCa", 11], ["c", "bBAcaCaca", 16], ["c", "bBBCbaB", 13], ["c", "bBBa", 8], ["c", "bBab", 8], ["c", "bBcCbcABC", 16], ["c", "bBcCca", 10], ["c", "bCBC", 7], ["c", "bCC", 5], ["c", "bCCAbcBBa", 16], ["c", "bCCa", 7], ["c", "bCabaBC", 13], ["c", "bCc", 4], ["c", "bCcA", 6], ["c", "baaAB", 10], ["c", "baaABBC", 13], ["c", "baaAccba", 14], ["c", "bbBbbBBa", 16], ["c", "bbCccAAC", 14], ["c", "bc", 2], ["c", "bcAbC", 8], ["c", "bcBAcAcB", 14], ["c", "bcbBbCcb", 14], ["c", "bccAAa", 10], ["c", "c", 0], ["c", "cAABBb", 10], ["c", "cAACa", 8], ["c", "cABc", 6], ["c", "cAaCBc", 10], ["c", "cAacbCCA", 14], ["c", "cBCCAbA", 12], ["c", "cBacAc", 10], ["c", "cBcCBb", 10], ["c", "cCAB", 6], ["c", "cCCbbBBcA", 16], ["c", "cCcaBbcba", 16], ["c", "ca", 2], ["c", "caC", 4], ["c", "caa", 4], ["c", "cabA", 6], ["c", "cb", 2], ["c", "cbCaBBab", 14], ["c", "cbCbAa", 10], ["c", "cbbBbCcC", 14], ["c", "cbcaAAC", 12], ["c", "cc", 2], ["c", "ccB", 4], ["c", "ccCBaBAC", 14], ["c", "ccc", 4], ["cA", "A", 2], ["cA", "AAbaCB", 10], ["cA", "AC", 4], ["cA", "Ac", 4], ["cA", "Acc", 4], ["cA", "BABCB", 8], ["cA", "BBc", 6], ["cA", "BaaBCbA", 11], ["cA", "Bbb", 6], ["cA", "BbbAAccB", 14], ["cA", "Bc", 4], ["cA", "C", 3], ["cA", "CAaCAABb", 13], ["cA", "CAaacAcc", 12], ["cA", "CbaAccc", 11], ["cA", "Cbbb", 7], ["cA", "CcC", 4], ["cA", "aB", 4], ["cA", "aC", 4], ["cA", "ab", 4], ["cA", "abAAcc", 10], ["cA", "abC", 6], ["cA", "bBCAbCa", 11], ["cA", "bCbbBCAC", 13], ["cA", "bCcCBcCA", 12], ["cA", "bcaCcbC", 11], ["cA", "cABaBCb", 10], ["cA", "cBaAB", 6], ["cA", "cCBababA", 12], ["cA", "ccCBbBab", 13], ["cA", "cca", 3], ["cAA", "AaaCaBcCb", 16], ["cAA", "AbBC", 8], ["cAA", "a", 5], ["cAA", "cb", 4], ["cAAA", "cBaC", 5], ["cAAAA", "bcA", 8], ["cAAAC", "aBAaCcCca", 13], ["cAAAacbCB", "BBaACbA", 12], ["cAAAbabc", "BCAacBCBc", 10], ["cAAAbbc", "AAbb", 6], ["cAAAcB", "bbAB", 8], ["cAAB", "ABCaCbCbC", 15], ["cAAB", "Aab", 4], ["cAABaBAa", "bAaBaBA", 5], ["cAAC", "CAB", 5], ["cAACA", "CaABCCbAb", 10], ["cAACAC", "BC", 10], ["cAACaCc", "AcabA", 9], ["cAACb", "BCAcca", 8], ["cAACbABCc", "CCbAB", 9], ["cAACbb", "aAaC", 7], ["cAACcAA", "bAaCcacb", 8], ["cAAa", "caABcbBAB", 12], ["cAAaB", "BBaAb", 7], ["cAAaBAAA", "aCb", 14], ["cAAaCbCc", "CC", 12], ["cAAaba", "bcb", 10], ["cAAabcCCA", "aAa", 13], ["cAAbB", "AAcaBaa", 10], ["cAAbBA", "caCcABAB", 9], ["cAAbBBaCa", "CabcB", 12], ["cAAbC", "BAbbb", 6], ["cAAba", "CCBAabaAB", 10], ["cAAc", "bCcCCB", 10], ["cAAcABCcb", "BCBB", 13], ["cAAcB", "ca", 7], ["cAAcCCAc", "Cb", 14], ["cAAcaabB", "A", 14], ["cAAccaaB", "BBbbAcBc", 15], ["cAB", "B", 4], ["cAB", "BCAAacb", 10], ["cAB", "CaCbCca", 11], ["cAB", "CbacbCcbB", 14], ["cAB", "b", 5], ["cAB", "baabbCA", 12], ["cAB", "cbbaBbCb", 11], ["cAB", "cccBBCBC", 12], ["cAB", "cccCCCCb", 13], ["cABA", "CA", 5], ["cABAABc", "acabCCb", 11], ["cABABAACA", "cBaBCcaa", 10], ["cABAa", "c", 8], ["cABAcaBb", "BCBaBCBc", 11], ["cABBAaCac", "aAacACCcc", 10], ["cABBCaBbc", "bBAaBcACC", 13], ["cABBc", "B", 8], ["cABBcacb", "bBcCcCAA", 13], ["cABC", "B", 6], ["cABCAB", "bAcb", 8], ["cABCAc", "cBCBa", 6], ["cABCAca", "ABaCCCbcc", 12], ["cABCBbbCA", "ABBccc", 11], ["cABCa", "CaA", 7], ["cABaA", "aAAbaBb", 9], ["cABaAaA", "BAB", 10], ["cABaBAC", "acCa", 12], ["cABaCAbbA", "A", 16], ["cABac", "BaCc", 6], ["cABbACA", "BbccCBCBc", 15], ["cABba", "AAC", 8], ["cABc", "AaccB", 7], ["cABcABc", "cAcBBca", 6], ["cABcBcb", "cbBCa", 8], ["cABcb", "Cac", 6], ["cABcba", "BAab", 8], ["cAC", "BCaCAbaCa", 13], ["cAC", "C", 4], ["cAC", "CABbA", 7], ["cAC", "CAcABbA", 10], ["cAC", "bAa", 4], ["cAC", "cBBac", 6], ["cACAACCbc", "CAcABb", 10], ["cACABcACC", "CAc", 12], ["cACACbC", "BbCcAa", 11], ["cACAabC", "BbcbBcAAa", 14], ["cACBCaC", "C", 12], ["cACBCcCBb", "bBaaBaCbC", 14], ["cACBaaB", "bccBcBaB", 9], ["cACCBBcb", "Ba", 14], ["cACCaa", "aBcCaab", 7], ["cACCc", "ccAa", 7], ["cACa", "aBBcB", 9], ["cACa", "cAbCCA", 5], ["cACaB", "cAbbaBB", 6], ["cACac", "cBCBbbbB", 12], ["cACbCBB", "bBbAC", 12], ["cACbaCc", "AacCACBaB", 11], ["cACbaba", "acAACBcCC", 11], ["cACbbC", "Cba", 8], ["cACbbb", "ccacBccbA", 11], ["cACbcb", "b", 10], ["cACc", "Cc", 4], ["cACc", "bAba", 6], ["cACcA", "Caccc", 5], ["cACcAcc", "bbB", 14], ["cACca", "acabb", 9], ["cACcaA", "bB", 12], ["cACccAacA", "bcCBabCc", 14], ["cAa", "BacbCAbcA", 13], ["cAa", "Bbcb", 8], ["cAa", "C", 5], ["cAa", "aCBcCAcc", 12], ["cAa", "aCacc", 8], ["cAa", "caBcA", 6], ["cAa", "cacAbaAb", 10], ["cAa", "ccccAcaBC", 12], ["cAaA", "CbbaAaba", 10], ["cAaAA", "CaCbcbab", 13], ["cAaAA", "acAc", 7], ["cAaAABAcb", "bAba", 14], ["cAaACBaa", "cbBabcaac", 11], ["cAaACc", "ccbBB", 10], ["cAaAaaB", "CBCbBccBa", 15], ["cAaAc", "ABCACcb", 10], ["cAaB", "C", 7], ["cAaB", "aBaBA", 6], ["cAaBCBbAA", "aAc", 14], ["cAaBaA", "a", 10], ["cAaC", "BcbAbbCb", 10], ["cAaC", "CbB", 7], ["cAaCAc", "ac", 8], ["cAaCaAc", "bBBBbaC", 12], ["cAaCaC", "ABBa", 8], ["cAaCc", "c", 8], ["cAaaAAA", "BBCAa", 11], ["cAaaaBCA", "AcABc", 10], ["cAaaacAB", "aAbCB", 10], ["cAaaacaa", "ABB", 14], ["cAaacB", "AaCBbACB", 10], ["cAaaca", "bAbbc", 8], ["cAabACa", "bcaabaaAb", 9], ["cAabCA", "CbACAAcb", 11], ["cAaba", "a", 8], ["cAabcacaa", "aA", 15], ["cAacAaBBC", "bBaBAbcaC", 12], ["cAaca", "AcacAA", 7], ["cAaca", "BcCcBBBA", 13], ["cAacc", "BBCaCA", 9], ["cAacc", "a", 8], ["cAacccAb", "ccbbBACA", 13], ["cAb", "B", 5], ["cAb", "BAbABAC", 10], ["cAb", "BBAAc", 8], ["cAb", "CBABCa", 8], ["cAb", "CbBBaA", 10], ["cAb", "caBACCa", 10], ["cAbA", "Cacc", 6], ["cAbA", "aAAACA", 8], ["cAbABbab", "aCA", 13], ["cAbAC", "BAAc", 5], ["cAbACB", "bAcCC", 8], ["cAbACcCaa", "cBa", 13], ["cAbAb", "acBC", 9], ["cAbAbA", "bACaC", 9], ["cAbAbAAAa", "A", 16], ["cAbBB", "AbaaC", 8], ["cAbBaAAAA", "<PERSON><PERSON><PERSON><PERSON>", 9], ["cAbBaB", "AcB", 8], ["cAbaBbBC", "a", 14], ["cAbab", "Ba", 7], ["cAbabb", "bC", 10], ["cAbbB", "aBBcaAA", 13], ["cAbbBCAcB", "aabbaC", 11], ["cAbbBaca", "CbCAcBaC", 12], ["cAbbBbcC", "CB", 13], ["cAbbb", "cCbaBAAAc", 13], ["cAbcAcBA", "AcA", 10], ["cAbcaCccC", "ABbAbbcB", 12], ["cAbcbB", "AAABaac", 11], ["cAbccBCBc", "AAACAcbAc", 12], ["cAc", "AbBCcaBB", 13], ["cAc", "BCCcCaBac", 13], ["cAc", "CB", 5], ["cAc", "bBb", 6], ["cAc", "bCaCaCb", 11], ["cAc", "bcb", 6], ["cAc", "cAAcaa", 6], ["cAcA", "CaaB", 6], ["cAcAA", "cbAcBbB", 8], ["cAcABAAc", "aABaBBc", 9], ["cAcACcBb", "cacbCccC", 7], ["cAcAb", "aBbAA", 8], ["cAcAcAcCc", "caaAAcbc", 7], ["cAcAcCB", "ccbcbBbC", 10], ["cAcBBaBCb", "C", 16], ["cAcBCb", "abCbAAB", 11], ["cAcBCcBba", "A", 16], ["cAcBbA", "aBabBACbB", 13], ["cAcBbB", "ABBcacBBA", 10], ["cAcCAB", "CBBbc", 11], ["cAcCBCa", "CCA", 9], ["cAcCBcBBa", "CacACBBBB", 8], ["cAcCCBCBA", "ccCcbcbc", 8], ["cAcCbCcAB", "caC", 13], ["cAcCcbaaA", "CcBaa", 9], ["cAca", "BAcbCbC", 10], ["cAcaAa", "caAAaab", 7], ["cAcaBAAC", "BBB", 14], ["cAcaaa", "BaBaBC", 9], ["cAcaaca", "aaCBA", 10], ["cAcabCbCB", "BacBcbABb", 11], ["cAcabaB", "Cc", 11], ["cAcac", "acbA", 7], ["cAcbABCBB", "aABc", 12], ["cAcbBCBac", "bCbAAacB", 13], ["cAcbbccAc", "AABAaBbCb", 16], ["cAcc", "aCa", 6], ["cAcc", "bCAAcaCC", 10], ["cAcc", "cc", 4], ["cAccCB", "aAABca", 9], ["cAccaBCAb", "BBcBCCAbb", 12], ["cAccaaABa", "bCAcB", 13], ["cAcccAA", "bCAb", 11], ["cAcccaCaA", "abaB", 14], ["cAcccacab", "BbCCbaB", 13], ["cB", "AbAB", 6], ["cB", "AbBbCA", 10], ["cB", "B", 2], ["cB", "BACbBc", 9], ["cB", "BAcB", 4], ["cB", "BBaBaaccC", 16], ["cB", "BcAaA", 8], ["cB", "C", 3], ["cB", "CAA", 5], ["cB", "CAAA", 7], ["cB", "CAacabCCC", 15], ["cB", "CBBB", 5], ["cB", "CCBCABaaA", 15], ["cB", "Cc", 3], ["cB", "CcBAc", 6], ["cB", "a", 4], ["cB", "aACCba", 10], ["cB", "aacbcC", 9], ["cB", "acaaAABB", 12], ["cB", "b", 3], ["cB", "ba", 4], ["cB", "baacB", 6], ["cB", "bc", 4], ["cB", "cABAbCBc", 12], ["cB", "cCAB", 4], ["cB", "caAAbbBC", 12], ["cB", "caBAAa", 8], ["cBA", "Ac", 6], ["cBA", "CB", 3], ["cBA", "a", 5], ["cBA", "ac", 6], ["cBA", "b", 5], ["cBA", "bbaBac", 9], ["cBA", "cAaabbab", 12], ["cBAA", "AAB", 6], ["cBAA", "aCabbA", 8], ["cBAAB", "CCa", 8], ["cBAABa", "bA", 9], ["cBAABccc", "caBcbAAa", 13], ["cBAAbCcC", "Bcc", 11], ["cBAAcAbAC", "b<PERSON><PERSON><PERSON>b", 10], ["cBAB", "cBBcaCCb", 10], ["cBABaBCc", "BAABc", 7], ["cBABaac", "baCbbb", 12], ["cBABbBAaA", "c", 16], ["cBABba", "AaAccABac", 12], ["cBABca", "aBcB", 7], ["cBACBAAcA", "BBCbCCBb", 13], ["cBACa", "cbaabBBcA", 12], ["cBACc", "cBbBaCb", 7], ["cBACcBBC", "AbaaBbcC", 11], ["cBACcbCaa", "ab", 15], ["cBAa", "cCCBaAc", 8], ["cBAaBcaB", "b", 15], ["cBAacbBa", "aaBb", 11], ["cBAbCaBA", "ccaB", 9], ["cBAba", "B", 8], ["cBAbaa", "CaccCBA", 12], ["cBAbb", "ca", 7], ["cBAbbCaCc", "aaCA", 13], ["cBAbca", "aaACbA", 9], ["cBAc", "BA", 4], ["cBAc", "aCCcbbbcC", 13], ["cBAcAb", "BCabCbcc", 13], ["cBAcAbaa", "AAC", 12], ["cBAcBBc", "cCcaaa", 10], ["cBAcCCb", "cCcAc", 9], ["cBAcb", "CaAAACba", 10], ["cBB", "BacacBcCA", 14], ["cBB", "Bc", 4], ["cBB", "bAACA", 10], ["cBB", "bCbBBC", 7], ["cBBA", "AcAAcABa", 11], ["cBBA", "Cca", 6], ["cBBABC", "bCcbaAB", 9], ["cBBABC", "cCCcBcBc", 9], ["cBBAac", "CbbbcB", 9], ["cBBBABBA", "AAAbAAAB", 13], ["cBBBCA", "caacccAbA", 13], ["cBBBCacAB", "<PERSON>ccab", 13], ["cBBBcbA", "cB", 10], ["cBBC", "acaC", 6], ["cBBCBaCbA", "CBcBaAc", 10], ["cBBCCAca", "ABCcCa", 8], ["cBBCb", "cBBbaaC", 8], ["cBBCbB", "aCbBaCaB", 8], ["cBBaBaAac", "C", 17], ["cBBaaCbBb", "baCCBa", 11], ["cBBabaAc", "b", 14], ["cBBac", "ba", 7], ["cBBacC", "aBcCAC", 8], ["cBBbAaabA", "aaA", 12], ["cBBbB", "BCcAACb", 11], ["cBBbBaBcA", "bbCCCc", 13], ["cBBbCbc", "ACccCBaCB", 13], ["cBBbbaBcb", "Cc", 15], ["cBBbbbb", "acBbaBca", 10], ["cBBbbcAC", "bCaBbbcbB", 9], ["cBBbbcacb", "BcCcaaBA", 13], ["cBBbca", "BbA", 7], ["cBBcAccA", "AcbbAC", 11], ["cBBcBb", "BaCb", 7], ["cBBcaBcB", "Aa", 14], ["cBBcbC", "bcCBacb", 8], ["cBC", "cACacBc", 9], ["cBC", "cCa", 4], ["cBCA", "C", 6], ["cBCACaCaC", "ccBBBCaac", 9], ["cBCAaac", "cB", 10], ["cBCAacA", "aaAcA", 8], ["cBCAcCccC", "cbB", 15], ["cBCAcbC", "CcAbCbbaA", 13], ["cBCB", "bbabCAAbB", 13], ["cBCBCbCa", "bcCAaCCa", 10], ["cBCBacA", "AcbBaA", 7], ["cBCBbCBa", "c", 14], ["cBCCABcC", "Baa", 13], ["cBCCB", "a", 10], ["cBCCBcaBa", "AcAAba", 13], ["cBCCC", "A", 10], ["cBCCcBc", "ccaBCB", 9], ["cBCaB", "BBbAA", 7], ["cBCab", "aaABbC", 10], ["cBCacA", "abcbAbAa", 11], ["cBCb", "aCbCc", 6], ["cBCb", "b", 6], ["cBCb", "cAA", 6], ["cBCbCAc", "ACacAB", 9], ["cBCbCca", "aaB", 13], ["cBCbabA", "bbBbABB", 9], ["cBCbbBBbc", "aCaBBbbb", 10], ["cBCbcCC", "ACBbBBc", 10], ["cBCc", "AcaCBCb", 8], ["cBCcAbcac", "aBcac", 10], ["cBCcbccaa", "bBbCaBB", 13], ["cBa", "b", 5], ["cBa", "bAAaA", 8], ["cBaAA", "baaCbca", 11], ["cBaAAB", "BcbB", 8], ["cBaAACacA", "bBa", 14], ["cBaABacb", "ACb", 11], ["cBaB", "BabB", 4], ["cBaB", "CBBBC", 5], ["cBaBBAB", "B", 12], ["cBaBBCc", "BacCACa", 10], ["cBaBaABBB", "bcc", 17], ["cBaBbCBC", "cAAB", 11], ["cBaBc", "bbC", 7], ["cBaC", "BacB", 5], ["cBaC", "bA", 6], ["cBaCA", "CAcabbb", 11], ["cBaCAbCC", "B", 14], ["cBaCAccbc", "bbccAcCBB", 10], ["cBaCCC", "ab", 10], ["cBaCaBC", "Cb", 11], ["cBaCab", "BC", 8], ["cBaa", "BAab", 5], ["cBaaA", "Bb", 8], ["cBaaCabBb", "aCACcCbbb", 11], ["cBaaaBBac", "bcaAbBcC", 10], ["cBaab", "acAacAbCc", 11], ["cBab", "ACCAB", 7], ["cBabB", "C", 9], ["cBabBCaca", "C", 16], ["cBabBcab", "CbcAAAa", 12], ["cBabCaA", "aaAbAaac", 10], ["cBabbBCBb", "C", 16], ["cBac", "A", 7], ["cBacCAB", "B", 12], ["cBacabC", "aAAaB", 10], ["cBacac", "c", 10], ["cBb", "BbbA", 5], ["cBb", "CAbcC", 7], ["cBb", "acAB", 5], ["cBbACBB", "bCCCAbbC", 12], ["cBbACC", "ACa", 8], ["cBbAcabB", "aA", 14], ["cBbC", "abAabbbc", 12], ["cBbCAACc", "bBB", 13], ["cBbCAB", "cbCb", 5], ["cBbCAa", "cacaBBAb", 11], ["cBbCAaA", "AB", 12], ["cBbCAb", "bC", 8], ["cBbCBAAA", "AbBbc", 12], ["cBbCCC", "BCba", 8], ["cBbCc", "ccaCBA", 8], ["cBbCccaC", "bA", 13], ["cBba", "cb", 4], ["cBbaACaa", "cC", 12], ["cBbaCbc", "ccaaca", 9], ["cBbaaac", "b", 12], ["cBbaab", "BbbBcC", 9], ["cBbaccBA", "c", 14], ["cBbb", "CCcCaa", 10], ["cBbb", "abCb", 5], ["cBbbA", "A", 8], ["cBbbA", "B", 8], ["cBbbBAAb", "AAaa", 13], ["cBbbBBacb", "cCaCCcAac", 14], ["cBbbBCaa", "AaBc", 13], ["cBbbCa", "CB", 9], ["cBbbcBB", "bAACBBb", 10], ["cBbcBaCBC", "abA", 15], ["cBbcCAC", "b", 12], ["cBbcCb", "caBBbb", 7], ["cBbcaBAAc", "bB", 14], ["cBbcbAAb", "aba", 13], ["cBbcbcBB", "AccaA", 12], ["cBbccCcc", "aaCBabcBB", 15], ["cBbccb", "caaABcBb", 9], ["cBc", "BCaAAAbB", 14], ["cBc", "CAAC", 6], ["cBc", "aAc", 4], ["cBc", "aBaAAc", 8], ["cBc", "aabbAc", 9], ["cBc", "aac", 4], ["cBc", "c", 4], ["cBcA", "bC", 6], ["cBcAAbbcb", "BAbb", 10], ["cBcACAA", "a", 13], ["cBcACb", "Bc", 8], ["cBcAaACbc", "abbB", 14], ["cBcBaaC", "AbC", 11], ["cBcBbCC", "aBbCaba", 11], ["cBcBbCbbA", "cBa", 13], ["cBcBbb", "Acb", 8], ["cBcCaaAcb", "baccACCCc", 13], ["cBcCbcCa", "aCa", 12], ["cBcCccaAa", "BCaCCabbA", 12], ["cBcaAaC", "bACBCAB", 12], ["cBcaAbBCb", "baCbbaa", 12], ["cBcaAbc", "aa", 11], ["cBcaAcaaA", "CACbcC", 14], ["cBcaBcBc", "aBbBCACc", 11], ["cBcaCcCA", "Bcc", 10], ["cBcabC", "bAAAabb", 10], ["cBcbACbBB", "ACBc", 12], ["cBcbBAbAC", "A", 16], ["cBcbC", "AAbBcCa", 10], ["cBcbcaCb", "cCbcCB", 6], ["cBcc", "bAcAcaaba", 14], ["cBcc", "cacc", 2], ["cBccaAac", "aab", 12], ["cBccaBCB", "Caa", 13], ["cBccacbCA", "bBCBAca", 11], ["cBccbc", "CAbA", 9], ["cBcccCAC", "BA", 12], ["cC", "A", 4], ["cC", "ABCCCa", 9], ["cC", "AbAacCAc", 12], ["cC", "AccAAABc", 13], ["cC", "BBAabcC", 10], ["cC", "BBc", 5], ["cC", "BBcaACaA", 12], ["cC", "BCABBCAaC", 15], ["cC", "BaAbcaBc", 13], ["cC", "BaBaccb", 11], ["cC", "CBA", 5], ["cC", "CacCac", 8], ["cC", "CcACcacbB", 14], ["cC", "CcBbb", 8], ["cC", "aACbbCAB", 13], ["cC", "aBABCBbc", 14], ["cC", "aCCcAAca", 13], ["cC", "aaBaBbAA", 16], ["cC", "acAb", 6], ["cC", "b", 4], ["cC", "bA", 4], ["cC", "bACBaA", 10], ["cC", "bacAcbc", 11], ["cC", "bcaCBaB", 10], ["cC", "c", 2], ["cC", "cA", 2], ["cC", "cABAc", 7], ["cC", "cAbc", 5], ["cC", "cBA", 4], ["cC", "cBCAccaCb", 14], ["cC", "cabcBCB", 10], ["cC", "cccaBa", 9], ["cCA", "c", 4], ["cCAABb", "ACbaBC", 7], ["cCAACb", "bbC", 10], ["cCAAacBb", "a", 14], ["cCAAc", "ABbCcbBb", 14], ["cCAAc", "AC", 7], ["cCAB", "AAaCAABab", 12], ["cCABbAb", "Ab", 10], ["cCABbaBaA", "aacC", 15], ["cCAC", "bCbbbbAC", 10], ["cCACAb", "AbC", 10], ["cCACCc", "cccbAabc", 9], ["cCACc", "BbAcaB", 9], ["cCAa", "CBA", 5], ["cCAaabBbA", "bCCCcacb", 13], ["cCAac", "BBA", 8], ["cCAacBCc", "CCBBaA", 11], ["cCAacCCbA", "bCACCa", 9], ["cCAbA", "aBBcaccbA", 11], ["cCAbABC", "bbACCaACB", 13], ["cCAbACcc", "AbBAccbb", 11], ["cCAbCacAA", "BcABBAC", 12], ["cCAbbaA", "CcACabA", 8], ["cCAbcA", "BABABC", 10], ["cCAcAcbaB", "BccBaCC", 13], ["cCAcCAB", "bcccCbbab", 11], ["cCAcaabBA", "bBcbCCBa", 13], ["cCAcbBBa", "AaBca", 10], ["cCAcbcB", "c", 12], ["cCB", "BBCAbba", 11], ["cCB", "BCcab", 7], ["cCB", "CC", 3], ["cCB", "CbBa", 5], ["cCB", "a", 6], ["cCB", "aC", 4], ["cCBA", "abcA", 6], ["cCBACb", "abBccaC", 11], ["cCBAbCCa", "a", 14], ["cCBAbaC", "aAbaAa", 10], ["cCBAcCB", "CbaCB", 6], ["cCBBAb", "cBcaC", 7], ["cCBBBc", "Cc", 8], ["cCBBaa", "CBC", 8], ["cCBCBAc", "ABCBCBbCB", 9], ["cCBCa", "AcacaBCAc", 10], ["cCBCbB", "Cc", 9], ["cCBCcbB", "cCBaAaBc", 8], ["cCBaAac", "C", 12], ["cCBaCc", "CcbBBbB", 10], ["cCBac", "bb", 9], ["cCBbAC", "abAbC", 8], ["cCBbAaabb", "BCc", 16], ["cCBbAcCAC", "acBCCbA", 12], ["cCBbAccB", "abA", 12], ["cCBbbB", "bbaaCCBbB", 11], ["cCBc", "ABbacCc", 10], ["cCBc", "cAcBb", 5], ["cCBcABc", "ac", 11], ["cCBcACBa", "cACcB", 10], ["cCBcACCa", "BACAAaa", 11], ["cCBca", "cbCacC", 6], ["cCBcb", "BCbAABAba", 12], ["cCBcbCaCb", "cbBCa", 10], ["cCC", "BbacaAb", 12], ["cCC", "CB", 4], ["cCC", "accaCCCcb", 12], ["cCC", "bAbcCcbcb", 13], ["cCC", "bB", 6], ["cCC", "bCBbABBCc", 14], ["cCC", "baAAb", 10], ["cCC", "cABBc", 7], ["cCC", "cCCbaac", 8], ["cCCAbbbaa", "BccccA", 15], ["cCCAc", "ccAbbb", 9], ["cCCBBaBC", "bcaA", 13], ["cCCBCAaA", "baB", 13], ["cCCBc", "bAccaAcB", 11], ["cCCCC", "cBC", 6], ["cCCCaA", "cCbB", 8], ["cCCCaac", "BCAcCb", 11], ["cCCCbA", "CcBBBBb", 11], ["cCCCbb", "ABccCcA", 10], ["cCCCbbbAa", "cA", 14], ["cCCa", "AccBc", 7], ["cCCaA", "BcACCA", 6], ["cCCaAAAC", "aBbcC", 12], ["cCCaCB", "CaaAcbCAB", 12], ["cCCaccA", "bc", 12], ["cCCaccAC", "abAACcCa", 12], ["cCCb", "C", 6], ["cCCb", "cAcBa", 6], ["cCCbBCBab", "CCcaca", 11], ["cCCbb", "BCBbbcA", 8], ["cCCbc", "bCCC", 5], ["cCCbcbabA", "aaCCc", 14], ["cCCcABBB", "aBBbABcb", 11], ["cCCcCCcAa", "bBCab", 15], ["cCCcaaC", "aBC", 10], ["cCa", "BacAaCAcA", 13], ["cCa", "C", 4], ["cCa", "CAACAAaC", 11], ["cCa", "aBBcbc", 10], ["cCa", "aCBab", 6], ["cCa", "aaCCAAAb", 12], ["cCa", "bBAcCcB", 10], ["cCa", "cBBcaCB", 9], ["cCaA", "CCaca", 4], ["cCaA", "cBcccC", 9], ["cCaAAACA", "cBBbacc", 12], ["cCaAAacCc", "cbCaBcA", 12], ["cCaABCAAA", "A", 16], ["cCaAb", "aBaAB", 5], ["cCaAbAABA", "CCabcAAbb", 8], ["cCaAc", "ABAbbBBB", 15], ["cCaAcACaa", "caaBcBc", 11], ["cCaAcCBca", "ABcc", 12], ["cCaAcbA", "aBCBcCCba", 12], ["cCaB", "ABABAbBBc", 15], ["cCaBBaAb", "aA", 12], ["cCaBBaCAC", "bC", 15], ["cCaBBba", "aa", 10], ["cCaBCbb", "BcaCB", 8], ["cCaBb", "Ac", 9], ["cCaC", "AC", 5], ["cCaCA", "BBBbbCAC", 12], ["cCaCBBBCb", "B", 16], ["cCaCCB", "BcBac", 9], ["cCaCaB", "AbcA", 10], ["cCaCaaaA", "abcCcc", 14], ["cCaCc", "aA", 8], ["cCaa", "CcCbcbA", 9], ["cCaaBCb", "A", 13], ["cCaab", "a", 8], ["cCabB", "AbbA", 8], ["cCabB", "AcaaACCb", 11], ["cCabC", "aBcB", 8], ["cCabaBC", "aB", 10], ["cCabc", "cabcccAB", 10], ["cCabcBa", "abccAaAB", 12], ["cCac", "AacBAaA", 10], ["cCacacaa", "CACbAbcC", 13], ["cCacba", "aAbAbBAc", 13], ["cCb", "AccbcbcB", 11], ["cCb", "BaC", 6], ["cCb", "BabCcAA", 12], ["cCb", "aCaBbBbC", 12], ["cCb", "accCcBAbA", 12], ["cCb", "cC", 2], ["cCbAB", "bcaac", 8], ["cCbAB", "cca", 6], ["cCbAc", "baB", 7], ["cCbAcaB", "AaBB", 10], ["cCbBAc", "bbBBBCa", 10], ["cCbBBAbC", "BCa", 13], ["cCbBa", "AAcCCaAB", 11], ["cCbBacc", "aBAbCAc", 11], ["cCbBb", "aacaBa", 9], ["cCbBb", "baAAacaCA", 17], ["cCbBbc", "acaAbC", 8], ["cCbC", "B", 7], ["cCbC", "CcBaA", 7], ["cCbC", "aC", 6], ["cCbCCbCaA", "acBBbCcC", 12], ["cCbCaCBa", "aBBAAbaA", 13], ["cCbCac", "CBAB", 8], ["cCbCcaCc", "cAbbAab", 10], ["cCbCcbcbA", "cbb", 12], ["cCbaACaB", "AB", 12], ["cCbaAcaa", "BCcCcb", 12], ["cCbaB", "aCCbc", 7], ["cCbbAaaba", "CccBCA", 14], ["cCbbAbBC", "acaAcBC", 9], ["cCbbBC", "bca", 10], ["cCbbCB", "AaBb", 9], ["cCbbCBc", "aBBAbba", 12], ["cCbbCaA", "A", 12], ["cCbbabAC", "B", 15], ["cCbbccCb", "bAcCc", 10], ["cCbcABAa", "ABaACa", 11], ["cCbcB", "bBBcbbA", 10], ["cCbcbc", "Aa", 12], ["cCbccbB", "abaaBc", 11], ["cCbccbbaC", "aCBAcBB", 11], ["cCc", "ABcAcB", 8], ["cCc", "BbaaC", 9], ["cCc", "CAbAabcAc", 14], ["cCc", "CccC", 4], ["cCcA", "CBaCbc", 9], ["cCcAAC", "cCbaCa", 7], ["cCcAAbbcB", "A", 16], ["cCcAB", "BCCa", 6], ["cCcACAaCB", "cccb", 12], ["cCcAbbBbc", "CBbaAb", 12], ["cCcAcAa", "c", 12], ["cCcBC", "CBBAbBAcC", 13], ["cCcBCcbcb", "C", 16], ["cCcC", "aAcbBCb", 10], ["cCcCAb", "bAaCcaCCB", 11], ["cCcCaCaCB", "AC", 15], ["cCcCcB", "b", 11], ["cCcaAbCC", "cAAaCC", 7], ["cCcaBA", "C", 10], ["cCcaBBB", "BCaCCcB", 10], ["cCcaC", "CCAcbCcCc", 11], ["cCcaCa", "AAcb", 10], ["cCcaabba", "A", 15], ["cCcabc", "bc", 8], ["cCcb", "BBaAbbaAb", 16], ["cCcbAbcb", "AcCbcAAA", 12], ["cCcbBCCBC", "cC", 14], ["cCcbCCB", "BAbABC", 12], ["cCcbCaaC", "cbbcccbca", 12], ["cCcbaCca", "baBCABcC", 13], ["cCcbaba", "cCBBA", 7], ["cCcc", "a", 8], ["cCccBaa", "CBcBA", 7], ["cCccCaCbA", "caABbAcB", 13], ["cCccaA", "abb", 12], ["cCccbA", "BBabb", 10], ["cCccbB", "abAcB", 8], ["cCcccAAb", "aa", 14], ["cCcccC", "CBcBaAAc", 12], ["cCcccCA", "bcCACb", 9], ["ca", "AC", 4], ["ca", "ACA", 4], ["ca", "ACACb", 8], ["ca", "ACa", 3], ["ca", "Aaaa", 6], ["ca", "BAacabbab", 14], ["ca", "BB", 4], ["ca", "BBcaAbBcb", 14], ["ca", "BacCBC", 10], ["ca", "BbA", 5], ["ca", "BbBbBC", 12], ["ca", "BbCACC", 10], ["ca", "Bcbc", 6], ["ca", "BccA", 5], ["ca", "C", 3], ["ca", "CA", 2], ["ca", "CAC", 4], ["ca", "CCcbBaaab", 14], ["ca", "CbCaAaB", 11], ["ca", "a", 2], ["ca", "aBAA", 7], ["ca", "aC", 4], ["ca", "aaAbcACCc", 15], ["ca", "acccCcBc", 14], ["ca", "bAAaCCAc", 14], ["ca", "bBcAABbbB", 15], ["ca", "bbb", 6], ["ca", "cBC", 4], ["ca", "cCbba", 6], ["ca", "ccBBaba", 10], ["caA", "B", 6], ["caA", "CcCcAA", 7], ["caA", "aCAbCCCA", 12], ["caA", "aCcCbbAAC", 13], ["caA", "bBbac", 8], ["caA", "bCACCACC", 12], ["caA", "bbaA", 4], ["caA", "bbbBA", 8], ["caA", "cbAacAbbc", 12], ["caAA", "bAbaa", 7], ["caAAAB", "ab", 9], ["caAAAcaA", "CacaAcA", 6], ["caAACbc", "CBBCABbbb", 13], ["caAB", "A", 6], ["caAB", "BCAcbbB", 10], ["caAB", "CAaAab", 6], ["caABabc", "ACbbCCC", 12], ["caABb", "AaCaBC", 7], ["caAC", "CbCcB", 8], ["caAC", "bBBBCCBA", 14], ["caACABAA", "aa", 13], ["caACBa", "BbbBcAc", 13], ["caACCBAaa", "bbAcA", 13], ["caACCac", "a", 12], ["caAa", "abBcaB", 10], ["caAa", "acaCcBc", 10], ["caAaBaBc", "bcbCa", 14], ["caAaa", "ac", 8], ["caAaaaaa", "ccc", 14], ["caAac", "ACcACAbc", 9], ["caAbAAcBb", "c", 16], ["caAbAcACB", "CBCCaAaaB", 13], ["caAbBBAbC", "bacC", 13], ["caAbBCb", "bbaaaCc", 11], ["caAbbBa", "bAC", 12], ["caAbbb", "bbCACabaC", 13], ["caAc", "baCBB", 8], ["caAcCc", "ACc", 6], ["caAcbbaCB", "cbcBc", 12], ["caAcc", "BBbabc", 9], ["caAcccaAb", "Acab", 10], ["caB", "CbAcacAa", 12], ["caB", "ccBA", 4], ["caBAA", "aCbc", 8], ["caBABbbC", "CB", 13], ["caBAabAcC", "aCA", 14], ["caBAbAAc", "bcCBBa", 12], ["caBAbaAb", "ba", 12], ["caBAcB", "BaaaBA", 9], ["caBBA", "Bc", 8], ["caBBA", "CacB", 5], ["caBBaBAB", "acAbaa", 11], ["caBBbaaA", "bB", 13], ["caBC", "CBaaBaAa", 11], ["caBCBaBC", "aABaB", 8], ["caBCac", "b", 11], ["caBCc", "CBCabB", 9], ["caBa", "CaBabAC", 7], ["caBa", "CcbccA", 9], ["caBaB", "BcA", 8], ["caBaCAb", "AacCAA", 8], ["caBaCc", "cCCA", 8], ["caBb", "AaCbCAAa", 12], ["caBb", "BCBC", 6], ["caBb", "b", 6], ["caBbCaa", "caCCbb", 8], ["caBbCbbBB", "aBCBCabca", 11], ["caBbCcBC", "cccBaB", 11], ["caBbac", "bac", 6], ["caBbbB", "BcBBA", 9], ["caBbbcBa", "b", 14], ["caBc", "bA", 7], ["caBc", "bBA", 6], ["caBc", "cc", 4], ["caBcAA", "ccacac", 7], ["caBcacCb", "bBc", 12], ["caC", "BCbb", 7], ["caC", "BbBcA", 9], ["caC", "babCacB", 10], ["caC", "bb", 6], ["caC", "cCbAaa", 8], ["caCAB", "CBA", 7], ["caCAC", "AcaaCaacc", 10], ["caCAb", "BaC", 6], ["caCAccca", "bACc", 11], ["caCBA", "aBBBBcA", 10], ["caCBC", "BcBBabA", 11], ["caCBC", "ac", 7], ["caCBba", "cBA", 7], ["caCBbaBB", "a", 14], ["caCBbc", "A", 11], ["caCC", "CaBC", 3], ["caCCAa", "<PERSON><PERSON><PERSON>", 10], ["caCCAcaa", "aC", 12], ["caCCC", "bCBaAAbC", 11], ["caCCaa", "acabCacA", 7], ["caCCbCc", "aCBcC", 7], ["caCCcc", "cbaCAacc", 6], ["caCaBB", "ccbBaBA", 8], ["caCaBbcAA", "baCcAB", 10], ["caCaCAaaC", "ABB", 16], ["caCabcBa", "CAcCABcc", 10], ["caCb", "BcAcABAB", 11], ["caCb", "bAbBCb", 7], ["caCbAAcb", "aaBbAB", 9], ["caCbCBBb", "A", 15], ["caCbbC", "cCcc", 7], ["caCbbcbC", "CBAAbCC", 10], ["caCbc", "B", 9], ["caCcCaaB", "AacCbcca", 11], ["caa", "CCAcAcaab", 12], ["caa", "b", 6], ["caa", "bAB", 5], ["caa", "bacAa", 5], ["caaA", "BaCbcC", 10], ["caaAABaac", "AC", 15], ["caaACA", "cB", 10], ["caaAa", "CaBaAbcB", 9], ["caaAa", "bbC", 10], ["caaAbbc", "Aa", 11], ["caaB", "bAc", 7], ["caaB", "cB", 4], ["caaBA", "C", 9], ["caaBAabC", "BACB", 11], ["caaBCC", "Ab", 10], ["caaBCc", "CCb", 9], ["caaCCAa", "bCccbA", 11], ["caaCaBcBb", "aCabaa", 11], ["caaCbaB", "CCc", 11], ["caaCc", "b", 10], ["caaCcc", "cbA", 9], ["caaa", "AcCc", 8], ["caaa", "bCabaCaa", 9], ["caaaCb", "AabC", 7], ["caaaaC", "CaaBCCaC", 7], ["caaab", "BCC", 10], ["caaac", "AaBbACcBa", 13], ["caab", "C", 7], ["caabACbb", "bb", 12], ["caabB", "AbaCbac", 10], ["caabaABA", "cbacccaA", 10], ["caabcbcBB", "abaAAaC", 14], ["caac", "ABb", 7], ["caacAABaB", "CbBcbaB", 10], ["caacaccC", "BAaccaBA", 11], ["caacbaAc", "Abccbc", 10], ["cab", "CB", 4], ["cab", "a", 4], ["cab", "abCCb", 7], ["cab", "bAaaBBA", 11], ["cab", "bBbCABC", 11], ["cabAA", "c", 8], ["cabACCCCb", "b", 16], ["cabACc", "bBBAb", 9], ["cabBAaaB", "BbCCBaBc", 12], ["cabBC", "A", 9], ["cabBCa", "C", 10], ["cabBCcBbA", "BbCbAAaAb", 16], ["cabBaA", "cCa", 8], ["cabC", "BBCabBCa", 9], ["cabC", "BCBb", 7], ["cabC", "Baccb", 7], ["cabCCAcC", "cCcA", 9], ["cabCCaBca", "BCbBc", 11], ["cabCaba", "a", 12], ["cabCb", "cB", 7], ["cabCcbB", "BBbAcBaac", 13], ["cabaBBb", "AbC", 11], ["cabaa", "ccBCAbAB", 10], ["cababBBaB", "AcBCAC", 14], ["cabac", "BCa", 8], ["cabb", "CACcCcac", 14], ["cabb", "a", 6], ["cabbBBA", "CCab", 11], ["cabbBc", "aCccAAcc", 12], ["cabbbACA", "bcaCC", 11], ["cabc", "BCBcBca", 10], ["cabc", "CaaBBAABA", 14], ["cabcBCc", "BABAC", 10], ["cabca", "aAaC", 8], ["cac", "BBbaabba", 14], ["cac", "bb", 6], ["cac", "bcBa", 6], ["cac", "cBaaBa", 8], ["cacABBaB", "BaBAabBbA", 11], ["cacAaA", "bc", 10], ["cacAaaaBb", "Aba", 14], ["cacAbbcCB", "AcCcAaBC", 11], ["cacAc", "cAcaAB", 5], ["cacB", "BCcbAAcbB", 11], ["cacB", "c", 6], ["cacBAA", "BbbCCab", 12], ["cacBAc", "bbaBB", 10], ["cacBaAa", "aBBBCbbbA", 15], ["cacBab", "AC", 10], ["cacCCaB", "ABCc", 10], ["caca", "bCbbAa", 9], ["cacaAbAAA", "bc", 16], ["cacaAcc", "bc", 12], ["cacaBc", "BaabCbbB", 12], ["cacaaBc", "caAacCAAa", 11], ["cacaabBCa", "AACaccAc", 13], ["cacacBAbC", "C", 16], ["cacaca", "aB", 10], ["cacbAAaCa", "b", 16], ["cacbB", "AbBaCB", 9], ["cacbCcbcB", "cc", 14], ["cacbc", "BbAACAAC", 13], ["cacc", "CAccbBacC", 11], ["caccA", "ccc", 4], ["caccbc", "ab", 8], ["cb", "AAaBCAa", 13], ["cb", "AAaC", 8], ["cb", "ABAcbc", 8], ["cb", "ABccA", 8], ["cb", "ACABcb", 8], ["cb", "Aa", 4], ["cb", "AbBAaB", 10], ["cb", "BCcbbACB", 12], ["cb", "BabCaAAB", 14], ["cb", "BbBCACCBb", 15], ["cb", "C", 3], ["cb", "CAAbCCB", 11], ["cb", "CABBCa", 10], ["cb", "CCACAAB", 12], ["cb", "aBcA", 6], ["cb", "aCBAaabaa", 15], ["cb", "aCBaacAac", 16], ["cb", "aCC", 5], ["cb", "aaBcCC", 10], ["cb", "acBAC", 7], ["cb", "bCBcaCbA", 12], ["cb", "bcCCC", 8], ["cb", "cBCaBCC", 11], ["cb", "cCabBBabA", 14], ["cb", "cb", 0], ["cb", "cccCa", 8], ["cbA", "AaAaB", 8], ["cbA", "AcACCa", 9], ["cbA", "BbCBABAA", 12], ["cbA", "aBcCCAAca", 14], ["cbA", "aaab", 7], ["cbAA", "BAbBc", 8], ["cbAAaBcc", "cABABCbb", 10], ["cbAAcB", "a", 11], ["cbAAcC", "cacC", 5], ["cbAB", "CAABa", 5], ["cbABBb", "acBA", 9], ["cbABc", "cbcAaCb", 7], ["cbAC", "C", 6], ["cbACAbc", "aABCAbA", 8], ["cbACcAC", "bbcAcbB", 10], ["cbACcBBC", "cABbcbbaB", 12], ["cbACcb", "cabbaAC", 10], ["cbAaB", "babCC", 9], ["cbAaBc", "Acc", 8], ["cbAaBcCA", "C", 14], ["cbAaCBbB", "A", 14], ["cbAabcC", "BBBCAc", 11], ["cbAbAaBbc", "bC", 15], ["cbAbBBAA", "bcaBbAc", 9], ["cbAbCBA", "acABcaC", 10], ["cbAbCBBB", "AAaaa", 14], ["cbAbCaA", "cbAcBB", 7], ["cbAbaCc", "BACabcaa", 11], ["cbAbcCA", "ccC", 8], ["cbAbcaB", "cc", 10], ["cbAbcbAbB", "AccBbbcA", 13], ["cbAbcbb", "caCaAB", 10], ["cbAc", "BABcCB", 9], ["cbAc", "bacb", 5], ["cbAcCBb", "ABac", 10], ["cbAcbcccB", "CCC", 15], ["cbB", "Aaa", 6], ["cbB", "CbC", 3], ["cbB", "caCaB", 6], ["cbBA", "BcBBccBA", 9], ["cbBA", "aCcbB", 6], ["cbBAAc", "bcABA", 8], ["cbBAB", "AbcBc", 8], ["cbBABc", "Cbba", 7], ["cbBACACa", "aA", 13], ["cbBACBccA", "Ca", 15], ["cbBAba", "AcAca", 8], ["cbBAbb", "CbaaC", 8], ["cbBB", "cAb", 5], ["cbBB", "cAba", 5], ["cbBBbB", "BBBBcaA", 9], ["cbBBbca", "cACABCb", 10], ["cbBBcACBb", "AACbBbbb", 13], ["cbBCCaA", "AccCcBc", 11], ["cbBCacA", "cB", 10], ["cbBCb", "CAAbBA", 9], ["cbBa", "aaCC", 8], ["cbBa", "cBaB", 4], ["cbBa", "cccB", 6], ["cbBaBcAc", "Aab", 13], ["cbBaCAC", "CCcCCc", 10], ["cbBac", "c", 8], ["cbBacA", "bCaaaBaa", 12], ["cbBb", "AABA", 6], ["cbBb", "CCaa", 7], ["cbBbAbB", "cC", 12], ["cbBbCACCC", "Ccc", 14], ["cbBba", "ccb", 6], ["cbBbbb", "BccCbaAAB", 13], ["cbBc", "A", 8], ["cbBcCAa", "acab", 11], ["cbBcCCbAA", "caAbaCaBc", 14], ["cbBcaCB", "bA", 11], ["cbBcbaccC", "CaAACBAAA", 16], ["cbBcbc", "ABBBAAc", 9], ["cbBcc", "CACaBC", 10], ["cbC", "AcABa", 7], ["cbC", "bCCBBa", 10], ["cbCAbaAa", "ccBCaC", 11], ["cbCB", "AABCCaaA", 13], ["cbCBABAAb", "a", 17], ["cbCBAcacb", "CAa", 12], ["cbCBC", "cbcaCaaba", 11], ["cbCBccbcc", "BcCbA", 11], ["cbCC", "B", 7], ["cbCCBBCB", "baBaCbC", 11], ["cbCCBccBb", "BaAaC", 16], ["cbCCC", "AACACB", 8], ["cbCCbABBB", "CcaABC", 11], ["cbCCbBac", "CAaA", 12], ["cbCa", "CBA", 5], ["cbCa", "bbcacb", 7], ["cbCaB", "cbBaCb", 5], ["cbCbAaCCa", "ACAAb", 13], ["cbCbaC", "BACcBC", 8], ["cbCbaCcaC", "AaaaBC", 14], ["cbCbb", "A", 10], ["cbCbbCAAc", "caccc", 12], ["cbCbcC", "B", 11], ["cbCcAAcA", "AcaAaCcCa", 13], ["cbCcCAc", "babCC", 9], ["cbCcCB", "BCABA", 9], ["cbCcCbA", "cB", 11], ["cbCcb", "AacBB", 8], ["cbCcbca", "BCB", 10], ["cbCccca", "AAA", 13], ["cba", "A", 5], ["cba", "b", 4], ["cba", "bBCaaCc", 11], ["cba", "cAAbc", 6], ["cba", "caABABbab", 12], ["cba", "ccAB", 5], ["cbaAa", "cBbbcB", 8], ["cbaAc", "cAAc", 3], ["cbaBACBAc", "a", 16], ["cbaBCAAC", "BAAcCc", 12], ["cbaBCbbcC", "BcAaabac", 12], ["cbaBbCa", "BBACcA", 10], ["cbaC", "BBCaACabc", 13], ["cbaC", "aaA", 6], ["cbaCABA", "BbAaBccBC", 11], ["cbaCCA", "cbCABA", 6], ["cbaCCCa", "a", 12], ["cbaCacac", "AcBBbAA", 13], ["cbaCcaA", "C", 12], ["cbaa", "aaBcCBcBa", 13], ["cbaaA", "cBaa", 3], ["cbaabCcC", "bB", 13], ["cbabAA", "Bc", 11], ["cbabBbc", "AbcacAC", 11], ["cbabC", "CBbaAa", 7], ["cbabbC", "aCacAcBAC", 13], ["cbac", "cCbBcACa", 10], ["cbacAAAA", "BcAbcAbc", 12], ["cbacAAacc", "ab", 16], ["cbacAC", "BaaABAbC", 10], ["cbacACa", "AcBbcBaC", 10], ["cbacB", "CaBAa", 9], ["cbacCCab", "Acbc", 12], ["cbacCabBB", "CCcc", 14], ["cbb", "BBbB", 5], ["cbb", "CcAaAC", 10], ["cbb", "bBcABAcCC", 15], ["cbb", "cbccAbA", 8], ["cbbABaCBa", "Ca", 14], ["cbbAa", "aBBcAa", 6], ["cbbAaC", "cbc", 7], ["cbbAbB", "BABaBA", 9], ["cbbAbaaB", "CCababBa", 10], ["cbbBAC", "AacCcb", 12], ["cbbBbA", "AbCAACc", 12], ["cbbBbb", "b", 10], ["cbbBbbbCA", "bBB", 13], ["cbbBcBbBb", "BaABc", 14], ["cbbBcC", "C", 10], ["cbbCAaB", "A", 12], ["cbbCbC", "cBaCCABb", 10], ["cbbCcbA", "baCBaBab", 12], ["cbbaCA", "aCAbbbCcA", 9], ["cbbaCb", "ABaCaB", 8], ["cbbaaC", "BcAAbAba", 11], ["cbbabc", "BaAaacCAc", 14], ["cbbacB", "b", 10], ["cbbb", "CC", 7], ["cbbb", "bBcCaCAC", 14], ["cbbbB", "abacb", 7], ["cbbbBBcb", "cA", 14], ["cbbbaBbC", "CbAABAC", 8], ["cbbbc", "c", 8], ["cbbc", "aCBAcAc", 10], ["cbbc", "abBBCAB", 10], ["cbbcBC", "BAB", 9], ["cbbcBcCCb", "bc", 14], ["cbc", "AACAAbCCB", 14], ["cbc", "BcbaA", 6], ["cbc", "CbBbACB", 10], ["cbc", "b", 4], ["cbc", "cBCcBaC", 9], ["cbcA", "ABBAcC", 9], ["cbcAA", "c", 8], ["cbcACAabb", "bccBcbAaA", 13], ["cbcAaAAb", "BBccBbCA", 13], ["cbcAb", "CCCbbbcba", 13], ["cbcB", "Aba", 6], ["cbcB", "bCaaaAba", 14], ["cbcBacB", "acbB", 9], ["cbcBbb", "cBa", 8], ["cbcBcc", "bbCcA", 7], ["cbcC", "aABacCCC", 11], ["cbcCA", "bBAaba", 10], ["cbcCBcbc", "acBa", 12], ["cbcCcBa", "CbbACa", 8], ["cbcaB", "bCaAAc", 9], ["cbcabBAac", "bcBCbCBb", 13], ["cbcb", "bba", 6], ["cbcbCCA", "AaaCCabcc", 15], ["cbcbaCBb", "BccCAb", 9], ["cbcbbC", "BAaa", 11], ["cbcbcaCC", "BaBbbB", 14], ["cbcccaCc", "acAAAa", 13], ["cc", "AAAbBbC", 13], ["cc", "ABBaccbbB", 14], ["cc", "ABa", 6], ["cc", "ABaA", 8], ["cc", "BAbbAab", 14], ["cc", "BAbc", 6], ["cc", "BBacCb", 9], ["cc", "BCbaB", 9], ["cc", "BaBcBAAc", 12], ["cc", "BaC", 5], ["cc", "Bbca", 6], ["cc", "CAbbcb", 9], ["cc", "CCAcCb", 9], ["cc", "Ccb", 3], ["cc", "aAaBCaA", 13], ["cc", "aB", 4], ["cc", "aBA", 6], ["cc", "acCcCabA", 12], ["cc", "b", 4], ["cc", "bACABABA", 15], ["cc", "baCaAAAc", 13], ["cc", "bacAbacB", 12], ["cc", "bcbA", 6], ["cc", "cABcAA", 8], ["cc", "cACac", 6], ["cc", "cAbba", 8], ["cc", "cB", 2], ["cc", "cCac", 4], ["cc", "cCbbAcaA", 12], ["cc", "cb", 2], ["cc", "ccBc", 4], ["cc", "ccCab", 6], ["ccA", "AcCccAcc", 10], ["ccA", "CBAaC", 7], ["ccA", "b", 6], ["ccAABB", "ccCBBbC", 7], ["ccAAb", "BaABC", 8], ["ccAAbaA", "BaA", 9], ["ccAAbbbaB", "AABBCc", 12], ["ccAB", "BcA", 4], ["ccABA", "BcBBcbBA", 8], ["ccABBAA", "bCCac", 12], ["ccABcAA", "AABBaBaA", 11], ["ccAC", "CAACBCaCA", 13], ["ccAC", "bA", 6], ["ccACAAcb", "AbabCa", 12], ["ccACABbAc", "BCbAb", 12], ["ccACB", "CBbabcAC", 11], ["ccACBAaBa", "ca", 14], ["ccACBca", "Cb", 11], ["ccACC", "CcaAba", 7], ["ccACC", "aCc", 6], ["ccACaABcb", "CBBCCABCc", 10], ["ccACaBbAC", "cBaAaB", 11], ["ccACcAAB", "AbCbaCAbb", 13], ["ccACcbb", "abCcBCb", 9], ["ccAa", "bca", 4], ["ccAa", "c", 6], ["ccAaACB", "BCabaaA", 11], ["ccAaAbccA", "AAbAAb", 12], ["ccAaC", "bAaaACAB", 11], ["ccAcAC", "c", 10], ["ccAcAb", "AbABBCbBA", 14], ["ccAcb", "bcAb", 4], ["ccAcccc", "ccCca", 7], ["ccB", "B", 4], ["ccB", "CcBAB", 5], ["ccB", "aAa", 6], ["ccB", "b", 5], ["ccB", "cAaAbaa", 11], ["ccB", "cBbCACaAB", 13], ["ccBA", "B", 6], ["ccBA", "CAac", 7], ["ccBACbca", "Abb", 12], ["ccBAc", "baaaCaaB", 14], ["ccBB", "BcC", 6], ["ccBBA", "BbACCaA", 11], ["ccBBbAa", "b", 12], ["ccBBbb", "cBbBb", 4], ["ccBBbcC", "CAbacA", 10], ["ccBC", "AaBAA", 8], ["ccBC", "Baba", 7], ["ccBC", "b", 7], ["ccBC", "cbba", 5], ["ccBCAaBc", "bBCCCAA", 12], ["ccBCAbC", "abC", 9], ["ccBCBBcac", "BCabBA", 12], ["ccBCC", "AbbC", 7], ["ccBCCCa", "BaCaB", 10], ["ccBCCaBcB", "AcC", 14], ["ccBCCc", "aBb", 10], ["ccBCbBcC", "ca", 14], ["ccBCcCaA", "bcbBAcaBA", 10], ["ccBa", "ACCaABcCa", 12], ["ccBaC", "AC", 7], ["ccBaaAa", "Cc", 11], ["ccBab", "BACC", 9], ["ccBabcaBA", "CBbc", 11], ["ccBacCAbB", "cCCcA", 11], ["ccBbAABB", "ccb", 10], ["ccBbAaB", "Cc", 11], ["ccBbaCAb", "Bcc", 13], ["ccBbab", "cCbBc", 7], ["ccBbbbcB", "Aacaaa", 16], ["ccBcb", "bbcACCb", 9], ["ccC", "Ab", 6], ["ccC", "CBcCaAbAc", 13], ["ccC", "aaCB", 6], ["ccC", "bBBAA", 10], ["ccCAA", "aCC", 7], ["ccCAcCB", "baAAbBac", 14], ["ccCBAa", "AABbcA", 10], ["ccCBCc", "caBcB", 7], ["ccCBbC", "BbbA", 9], ["ccCC", "A", 8], ["ccCC", "CCaBcACBC", 11], ["ccCC", "aab", 8], ["ccCCAaa", "B", 14], ["ccCCBAaaC", "Bb", 16], ["ccCCBbCaA", "AcACCaB", 10], ["ccCCCaaCb", "BaAbBB", 16], ["ccCCacA", "ACAAa", 10], ["ccCa", "acbBa", 6], ["ccCaa", "aCBCa", 7], ["ccCab", "CCAb", 4], ["ccCbBB", "AAcabCa", 10], ["ccCba", "aacAbCBb", 11], ["ccCbbCBA", "AaCBcAb", 12], ["ccCbc", "aCbBCcb", 10], ["ccCbcCcA", "aAA", 14], ["ccCbcaaAC", "abB", 16], ["ccCcAbCA", "AB", 13], ["ccCcAccA", "CCABcCCbc", 13], ["ccCcBaA", "caC", 10], ["ccCcBbc", "aaacAca", 12], ["ccCcBcC", "CBCCCBAA", 9], ["ccCcabcb", "baAb", 12], ["ccCccbABc", "b", 16], ["cca", "ABc", 6], ["cca", "aa", 4], ["cca", "bACA", 6], ["ccaA", "AcBa", 5], ["ccaA", "Ca", 5], ["ccaABaccb", "AAaaCBb", 10], ["ccaACAC", "BAc", 11], ["ccaAba", "Ccb", 7], ["ccaAc", "B", 10], ["ccaAcCB", "ccBB", 8], ["ccaB", "BbcB", 6], ["ccaBC", "CbbbB", 8], ["ccaCA", "ABBbb", 10], ["ccaCAB", "cCbcb", 7], ["ccaCAcAB", "acbb", 11], ["ccaCAcBb", "ccCbb", 7], ["ccaCCCcCC", "AACAc", 13], ["ccaCaABb", "bA", 14], ["ccaCc", "babb", 8], ["ccaCcAAaB", "BcAAaB", 8], ["ccaCcB", "B", 10], ["ccaaBCB", "AAA", 12], ["ccaaaa", "Aa", 9], ["ccaab", "acbbC", 8], ["ccaac", "BCBBb", 9], ["ccab", "C", 7], ["ccab", "aabBBcABa", 14], ["ccabaB", "CACba", 7], ["ccabaCa", "ccAac", 6], ["ccababcA", "abc", 10], ["ccacAbBBB", "Cb", 15], ["ccacb", "cbCA", 7], ["ccb", "A", 6], ["ccb", "Aa", 6], ["ccb", "Aca", 4], ["ccb", "acBAcb", 6], ["ccb", "acBccbAcA", 12], ["ccbA", "ba", 5], ["ccbAAA", "BAaca", 9], ["ccbAACAc", "CaaCBcBAa", 14], ["ccbABAA", "BBaccbb", 13], ["ccbACa", "cabACc", 4], ["ccbAaCA", "cCccaCCA", 7], ["ccbAb", "bbbCaACa", 12], ["ccbAbA", "cbcaBb", 8], ["ccbAbBB", "cBcbA", 8], ["ccbAcC", "AbBCcaacB", 12], ["ccbBbcb", "bCbCAC", 10], ["ccbC", "ACccBAc", 8], ["ccbC", "CB", 6], ["ccbC", "aCAA", 7], ["ccbCC", "AcCcCcb", 8], ["ccbCa", "bcBBcb", 8], ["ccbCaACca", "BBcaA", 12], ["ccbCbbB", "b", 12], ["ccba", "CAbbBc", 9], ["ccba", "Caa", 5], ["ccba", "bA", 5], ["ccba", "cbc", 4], ["ccbabbC", "AB", 12], ["ccbbBbAb", "CBc", 13], ["ccbbabB", "bCcaCa", 11], ["ccbbcB", "ACbcaaA", 11], ["ccbbccCAC", "CcccBcBC", 10], ["ccbcABBCb", "aAcAacC", 12], ["ccbcAbBbc", "a", 17], ["ccbcB", "CBaCAAcBc", 12], ["ccbcBBAbB", "cBccCc", 13], ["ccbcCaab", "cCaAAB", 10], ["ccbcCbcB", "CCCa", 12], ["ccbcba", "CA", 10], ["ccc", "AcBcbBcb", 10], ["ccc", "BbcCbB", 9], ["ccc", "CACABCbb", 13], ["ccc", "ac", 4], ["ccc", "baAABCbcc", 13], ["ccc", "c", 4], ["ccc", "cabACBAbB", 15], ["cccA", "cbBAbbB", 10], ["cccAAA", "AcB", 10], ["cccAABCcA", "ba", 16], ["cccAC", "ABCaa", 8], ["cccAaccc", "bcCC", 12], ["cccAbC", "cBbCAb", 7], ["cccB", "bACBcb", 8], ["cccB", "c", 6], ["cccBAA", "cbB", 8], ["cccBAbb", "bCCacb", 9], ["cccBBAC", "bbbcB", 12], ["cccBBCc", "CAaaAb", 13], ["cccC", "ACbB", 7], ["cccC", "bbbA", 8], ["cccCabBA", "AaBc", 12], ["cccCbbcc", "CcC", 11], ["cccCccaC", "b", 16], ["ccca", "cCcCAa", 5], ["cccaAb", "Abb", 9], ["cccaCbA", "cBcCBBbaA", 9], ["cccaaa", "BaABcbcca", 14], ["cccacB", "abBB", 10], ["cccb", "AABc", 8], ["cccb", "CbBAAbA", 11], ["cccbABcc", "a", 15], ["cccbB", "bcCAcaaba", 11], ["cccbaBC", "cbACAac", 10], ["cccbabBb", "ACc", 13], ["cccbbA", "bbcAc", 10], ["cccbcaCC", "AbCACBcCb", 13], ["cccbcaaaC", "baCcCc", 14], ["ccccBa", "abcbaaCbA", 13], ["ccccCBcCA", "Cbbab", 15], ["ccccCCc", "aabB", 14], ["ccccaCA", "Cb", 12], ["cccccb", "a", 12], ["ccccccAcc", "AB", 16]]