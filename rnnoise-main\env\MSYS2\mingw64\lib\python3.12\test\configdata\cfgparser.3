  # INI with as many tricky parts as possible
  # Most of them could not be used before 3.2
     
  # This will be parsed with the following options
    # delimiters = {'='}
    # comment_prefixes = {'#'}
    # allow_no_value = True

[DEFAULT]
go = %(interpolate)s

[strange]
  values = that are indented # and end with hash comments
  other = that do continue
    in     # and still have
    other  # comments mixed
    lines  # with the values





[corruption]
  value = that is 


    actually still here


      and holds all these weird newlines


        # but not for the lines that are comments
        nor the indentation

  another value = # empty string
  yet another # None!

  [yeah, sections can be indented as well]
  and that does not mean = anything
  are they subsections = False
  if you want subsections = use XML
  lets use some Unicode = 片仮名

  [another one!]
 even if values are indented like this = seriously
yes, this still applies to = section "another one!"
this too = are there people with configurations broken as this? 
 beware, this is going to be a continuation
 of the value for
 key "this too"
 even if it has a = character
 this is still the continuation
 your editor probably highlights it wrong
 but that's life
# let's set this value so there is no error
# when getting all items for this section:
interpolate = anything will do

[no values here]
# but there's this `go` in DEFAULT

    [tricky interpolation]
      interpolate = do this
      lets = %(go)s
      
    [more interpolation]
      interpolate = go shopping
      lets = %(go)s
