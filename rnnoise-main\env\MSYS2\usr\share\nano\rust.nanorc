## Syntax highlighting for Rust.

## Copyright 2015 Luke <PERSON>an<PERSON>l.
## Licensed under GPL version 3 or newer.

syntax rust "\.(rlib|rs)$"
comment "//"

# Function definitions
color magenta "fn [[:lower:][:digit:]_]+"

# Reserved words
color yellow "\<(abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|false|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|true|try|type|typeof|unsafe|unsized|use|virtual|where|while|yield)\>"

# Macros
color red "[[:lower:]_]+!"

# Constants
color magenta "[[:upper:]][[:upper:][:digit:]_]+"

# Traits/Enums/Structs/Types/...
color magenta "[[:upper:]][[:alnum:]]+"

# Strings
color green ""([^"]|\\")*""
color green start=""([^"]|\\")*\\$" end="(^|.*[^\])""
## Inaccurate, but it's not possible to balance the number of hashes.
color green start="r#+"" end=""#+"

# Comments
color blue "//.*"
color blue start="/\*" end="\*/"

# Attributes
color magenta start="#!\[" end="\]"

# Some common markers
color brightcyan "XXX|TODO|FIXME|\?\?\?"
