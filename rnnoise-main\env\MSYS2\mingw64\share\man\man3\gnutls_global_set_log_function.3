.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_global_set_log_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_global_set_log_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_global_set_log_function(gnutls_log_func " log_func ");"
.SH ARGUMENTS
.IP "gnutls_log_func log_func" 12
it's a log function
.SH "DESCRIPTION"
This is the function where you set the logging function gnutls is
going to use.  This function only accepts a character array.
Normally you may not use this function since it is only used for
debugging purposes.

 \fIgnutls_log_func\fP is of the form,
void (*gnutls_log_func)( int level, const char*);
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
