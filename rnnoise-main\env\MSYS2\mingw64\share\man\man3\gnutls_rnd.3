.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_rnd" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_rnd \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_rnd(gnutls_rnd_level_t " level ", void * " data ", size_t " len ");"
.SH ARGUMENTS
.IP "gnutls_rnd_level_t level" 12
a security level
.IP "void * data" 12
place to store random bytes
.IP "size_t len" 12
The requested size
.SH "DESCRIPTION"
This function will generate random data and store it to output
buffer. The value of  \fIlevel\fP should be one of \fBGNUTLS_RND_NONCE\fP,
\fBGNUTLS_RND_RANDOM\fP and \fBGNUTLS_RND_KEY\fP. See the manual and
\fBgnutls_rnd_level_t\fP for detailed information.

This function is thread\-safe and also fork\-safe.
.SH "RETURNS"
Zero on success, or a negative error code on error.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
