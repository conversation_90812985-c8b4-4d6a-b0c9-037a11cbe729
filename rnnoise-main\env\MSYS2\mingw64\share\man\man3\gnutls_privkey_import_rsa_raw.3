.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_import_rsa_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_import_rsa_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_import_rsa_raw(gnutls_privkey_t " key ", const gnutls_datum_t * " m ", const gnutls_datum_t * " e ", const gnutls_datum_t * " d ", const gnutls_datum_t * " p ", const gnutls_datum_t * " q ", const gnutls_datum_t * " u ", const gnutls_datum_t * " e1 ", const gnutls_datum_t * " e2 ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
The structure to store the parsed key
.IP "const gnutls_datum_t * m" 12
holds the modulus
.IP "const gnutls_datum_t * e" 12
holds the public exponent
.IP "const gnutls_datum_t * d" 12
holds the private exponent (optional)
.IP "const gnutls_datum_t * p" 12
holds the first prime (p)
.IP "const gnutls_datum_t * q" 12
holds the second prime (q)
.IP "const gnutls_datum_t * u" 12
holds the coefficient (optional)
.IP "const gnutls_datum_t * e1" 12
holds e1 = d mod (p\-1) (optional)
.IP "const gnutls_datum_t * e2" 12
holds e2 = d mod (q\-1) (optional)
.SH "DESCRIPTION"
This function will convert the given RSA raw parameters to the
native \fBgnutls_privkey_t\fP format.  The output will be stored in
 \fIkey\fP .
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
