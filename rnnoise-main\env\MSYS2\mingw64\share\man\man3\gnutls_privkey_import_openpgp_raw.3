.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_import_openpgp_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_import_openpgp_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_import_openpgp_raw(gnutls_privkey_t " pkey ", const gnutls_datum_t * " data ", gnutls_openpgp_crt_fmt_t " format ", const gnutls_openpgp_keyid_t " keyid ", const char * " password ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t pkey" 12
The private key
.IP "const gnutls_datum_t * data" 12
The private key data to be imported
.IP "gnutls_openpgp_crt_fmt_t format" 12
The format of the private key
.IP "const gnutls_openpgp_keyid_t keyid" 12
The key id to use (optional)
.IP "const char * password" 12
A password (optional)
.SH "DESCRIPTION"
This function is no\-op.
.SH "RETURNS"
\fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
