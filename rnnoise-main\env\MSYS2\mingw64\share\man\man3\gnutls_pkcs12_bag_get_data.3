.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs12_bag_get_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs12_bag_get_data \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs12.h>
.sp
.BI "int gnutls_pkcs12_bag_get_data(gnutls_pkcs12_bag_t " bag ", unsigned " indx ", gnutls_datum_t * " data ");"
.SH ARGUMENTS
.IP "gnutls_pkcs12_bag_t bag" 12
The bag
.IP "unsigned indx" 12
The element of the bag to get the data from
.IP "gnutls_datum_t * data" 12
where the bag's data will be. Should be treated as constant.
.SH "DESCRIPTION"
This function will return the bag's data. The data is a constant
that is stored into the bag.  Should not be accessed after the bag
is deleted.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
