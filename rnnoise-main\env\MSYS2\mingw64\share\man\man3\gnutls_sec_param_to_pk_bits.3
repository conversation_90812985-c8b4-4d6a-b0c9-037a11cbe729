.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_sec_param_to_pk_bits" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_sec_param_to_pk_bits \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned int gnutls_sec_param_to_pk_bits(gnutls_pk_algorithm_t " algo ", gnutls_sec_param_t " param ");"
.SH ARGUMENTS
.IP "gnutls_pk_algorithm_t algo" 12
is a public key algorithm
.IP "gnutls_sec_param_t param" 12
is a security parameter
.SH "DESCRIPTION"
When generating private and public key pairs a difficult question
is which size of "bits" the modulus will be in RSA and the group size
in DSA. The easy answer is 1024, which is also wrong. This function
will convert a human understandable security parameter to an
appropriate size for the specific algorithm.
.SH "RETURNS"
The number of bits, or (0).
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
