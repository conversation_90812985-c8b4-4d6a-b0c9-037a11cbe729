.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_server_set_request" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_server_set_request \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_certificate_server_set_request(gnutls_session_t " session ", gnutls_certificate_request_t " req ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_certificate_request_t req" 12
is one of GNUTLS_CERT_REQUEST, GNUTLS_CERT_REQUIRE, GNUTLS_CERT_IGNORE
.SH "DESCRIPTION"
This function specifies if we (in case of a server) are going to
send a certificate request message to the client. If  \fIreq\fP is
GNUTLS_CERT_REQUIRE then the server will return the \fBGNUTLS_E_NO_CERTIFICATE_FOUND\fP
error if the peer does not provide a certificate. If you do not call this
function then the client will not be asked to send a certificate. Invoking
the function with  \fIreq\fP GNUTLS_CERT_IGNORE has the same effect.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
