<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>x509</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>x509 - X.509 certificate handling</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>An X.509 certificate is a structured grouping of information about an individual, a device, or anything one can imagine. An X.509 CRL (certificate revocation list) is a tool to help determine if a certificate is still valid. The exact definition of those can be found in the X.509 document from ITU-T, or in RFC3280 from PKIX. In OpenSSL, the type X509 is used to express such a certificate, and the type X509_CRL is used to express a CRL.</p>

<p>A related structure is a certificate request, defined in PKCS#10 from RSA Security, Inc, also reflected in RFC2896. In OpenSSL, the type X509_REQ is used to express such a certificate request.</p>

<p>To handle some complex parts of a certificate, there are the types X509_NAME (to express a certificate name), X509_ATTRIBUTE (to express a certificate attribute), X509_EXTENSION (to express a certificate extension) and a few more.</p>

<p>Finally, there&#39;s the supertype X509_INFO, which can contain a CRL, a certificate and a corresponding private key.</p>

<p><b>X509_</b><i>XXX</i>, <b>d2i_X509_</b><i>XXX</i>, and <b>i2d_X509_</b><i>XXX</i> functions handle X.509 certificates, with some exceptions, shown below.</p>

<p><b>X509_CRL_</b><i>XXX</i>, <b>d2i_X509_CRL_</b><i>XXX</i>, and <b>i2d_X509_CRL_</b><i>XXX</i> functions handle X.509 CRLs.</p>

<p><b>X509_REQ_</b><i>XXX</i>, <b>d2i_X509_REQ_</b><i>XXX</i>, and <b>i2d_X509_REQ_</b><i>XXX</i> functions handle PKCS#10 certificate requests.</p>

<p><b>X509_NAME_</b><i>XXX</i> functions handle certificate names.</p>

<p><b>X509_ATTRIBUTE_</b><i>XXX</i> functions handle certificate attributes.</p>

<p><b>X509_EXTENSION_</b><i>XXX</i> functions handle certificate extensions.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_NAME_ENTRY_get_object.html">X509_NAME_ENTRY_get_object(3)</a>, <a href="../man3/X509_NAME_add_entry_by_txt.html">X509_NAME_add_entry_by_txt(3)</a>, <a href="../man3/X509_NAME_add_entry_by_NID.html">X509_NAME_add_entry_by_NID(3)</a>, <a href="../man3/X509_NAME_print_ex.html">X509_NAME_print_ex(3)</a>, <a href="../man3/X509_NAME_new.html">X509_NAME_new(3)</a>, <a href="../man3/PEM_X509_INFO_read.html">PEM_X509_INFO_read(3)</a>, <a href="../man3/d2i_X509.html">d2i_X509(3)</a>, <a href="../man3/d2i_X509_ALGOR.html">d2i_X509_ALGOR(3)</a>, <a href="../man3/d2i_X509_CRL.html">d2i_X509_CRL(3)</a>, <a href="../man3/d2i_X509_NAME.html">d2i_X509_NAME(3)</a>, <a href="../man3/d2i_X509_REQ.html">d2i_X509_REQ(3)</a>, <a href="../man3/d2i_X509_SIG.html">d2i_X509_SIG(3)</a>, <a href="../man7/crypto.html">crypto(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2003-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


