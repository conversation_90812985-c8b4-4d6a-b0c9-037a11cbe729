<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="NsightTegraProject">
    <NsightTegraProjectRevisionNumber>6</NsightTegraProjectRevisionNumber>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|@id_platform@">
      <Configuration>Debug</Configuration>
      <Platform>@id_platform@</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CAE07175-D007-4FC3-BFE8-47B392814159}</ProjectGuid>
    <RootNamespace>CompilerId@id_lang@</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@id_platform@'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    @id_toolset@
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|@id_platform@'">.\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|@id_platform@'">$(Configuration)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|@id_platform@'">false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@id_platform@'">
    <ClCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
    </Link>
    <PostBuildEvent>
      <Command>
if "$(ToolchainName)"=="gcc" (
  for %%i in ($(ToolchainPrebuiltRoot)\bin\*@id_gcc@.exe) do (
    @echo CMAKE_@id_lang@_COMPILER=%%i
    goto :done
    )
)
if "$(ToolchainName)"=="clang" (
  for %%i in ($(ToolchainPrebuiltRoot)\bin\*@id_clang@.exe) do (
    @echo CMAKE_@id_lang@_COMPILER=%%i
    goto :done
  )
)
:done
</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="@id_src@" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>
