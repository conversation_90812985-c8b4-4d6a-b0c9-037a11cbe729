.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_global_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_global_init \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_global_init( " void ");"
.SH ARGUMENTS
.IP " void" 12
.SH "DESCRIPTION"

Since GnuTLS 3.3.0 this function is no longer necessary to be explicitly
called. To disable the implicit call (in a library constructor) of this
function set the environment variable \fBGNUTLS_NO_IMPLICIT_INIT\fP to 1.

This function performs any required precalculations, detects
the supported CPU capabilities and initializes the underlying
cryptographic backend. In order to free any resources 
taken by this call you should \fBgnutls_global_deinit()\fP 
when gnutls usage is no longer needed.

This function increments a global counter, so that
\fBgnutls_global_deinit()\fP only releases resources when it has been
called as many times as \fBgnutls_global_init()\fP.  This is useful when
GnuTLS is used by more than one library in an application.  This
function can be called many times, but will only do something the
first time. It is thread safe since GnuTLS 3.3.0.

A subsequent call of this function if the initial has failed will
return the same error code.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
