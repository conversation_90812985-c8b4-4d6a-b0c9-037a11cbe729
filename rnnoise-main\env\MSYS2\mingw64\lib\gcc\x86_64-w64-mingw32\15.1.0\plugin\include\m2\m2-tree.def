/* gm2-tree.def a component of a C header file used to define a SET type.

Copyright (C) 2006-2025 Free Software Foundation, Inc.
Contributed by <PERSON> <<EMAIL>>.

This file is part of GNU Modula-2.

GNU Modula-2 is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 3, or (at your option)
any later version.

GNU Modula-2 is distributed in the hope that it will be useful, but
WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
General Public License for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

/* A SET_TYPE type.  */
DEFTREECODE (SET_TYPE, "set_type", tcc_type, 0)
