.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_transport_set_errno" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_transport_set_errno \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_transport_set_errno(gnutls_session_t " session ", int " err ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "int err" 12
error value to store in session\-specific errno variable.
.SH "DESCRIPTION"
Store  \fIerr\fP in the session\-specific errno variable.  Useful values
for  \fIerr\fP are EINTR, EAGAIN and EMSGSIZE, other values are treated will be
treated as real errors in the push/pull function.

This function is useful in replacement push and pull functions set by
\fBgnutls_transport_set_push_function()\fP and
\fBgnutls_transport_set_pull_function()\fP under Windows, where the
replacements may not have access to the same  \fIerrno\fP variable that is used by GnuTLS (e.g., the application is linked to
msvcr71.dll and gnutls is linked to msvcrt.dll).

This function is unreliable if you are using the same
 \fIsession\fP in different threads for sending and receiving.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
