.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_fips140_run_self_tests" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_fips140_run_self_tests \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_fips140_run_self_tests( " void ");"
.SH ARGUMENTS
.IP " void" 12
.SH "DESCRIPTION"

Manually perform the second round of the FIPS140 self\-tests,
including:

\- Known answer tests (KAT) for the selected set of symmetric
cipher, MAC, public key, KDF, and DRBG
\- Library integrity checks

Upon failure with FIPS140 mode enabled, it makes the library
unusable.  This function is not thread\-safe.
.SH "RETURNS"
0 upon success, a negative error code otherwise
.SH "SINCE"
3.7.7
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
