.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_register_custom_url" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_register_custom_url \- API function
.SH SYNOPSIS
.B #include <gnutls/urls.h>
.sp
.BI "int gnutls_register_custom_url(const gnutls_custom_url_st * " st ");"
.SH ARGUMENTS
.IP "const gnutls_custom_url_st * st" 12
A \fBgnutls_custom_url_st\fP structure
.SH "DESCRIPTION"
Register a custom URL. This will affect the following functions:
\fBgnutls_url_is_supported()\fP, \fBgnutls_privkey_import_url()\fP,
gnutls_pubkey_import_url, \fBgnutls_x509_crt_import_url()\fP 
and all functions that depend on
them, e.g., \fBgnutls_certificate_set_x509_key_file2()\fP.

The provided structure and callback functions must be valid throughout
the lifetime of the process. The registration of an existing URL type
will fail with \fBGNUTLS_E_INVALID_REQUEST\fP. Since GnuTLS 3.5.0 this function
can be used to override the builtin URLs.

This function is not thread safe.
.SH "RETURNS"
returns zero if the given structure was imported or a negative value otherwise.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
