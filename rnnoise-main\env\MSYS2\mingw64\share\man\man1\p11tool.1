.de1 NOP
.  it 1 an-trap
.  if \\n[.$] \,\\$*\/
..
.ie t \
.ds B-Font [CB]
.ds I-Font [CI]
.ds R-Font [CR]
.el \
.ds B-Font B
.ds I-Font I
.ds R-Font R
.TH p11tool 1 "08 Feb 2025" "3.8.9" "User Commands"
.SH NAME
\f\*[B-Font]p11tool\fP
\- GnuTLS PKCS #11 tool
.SH SYNOPSIS
\f\*[B-Font]p11tool\fP
.\" Mixture of short (flag) options and long options
[\f\*[B-Font]\-flags\f[]]
[\f\*[B-Font]\-flag\f[] [\f\*[I-Font]value\f[]]]
[\f\*[B-Font]\-\-option-name\f[][[=| ]\f\*[I-Font]value\f[]]]
[url]
.sp \n(Ppu
.ne 2

Operands and options may be intermixed.  They will be reordered.
.sp \n(Ppu
.ne 2
.SH "DESCRIPTION"
Program that allows operations on PKCS #11 smart cards
and security modules. 
.sp
To use PKCS #11 tokens with GnuTLS the p11-kit configuration files need to be setup.
That is create a .module file in /etc/pkcs11/modules with the contents 'module: /path/to/pkcs11.so'.
Alternatively the configuration file /etc/gnutls/pkcs11.conf has to exist and contain a number
of lines of the form 'load=/usr/lib/opensc-pkcs11.so'.
.sp
You can provide the PIN to be used for the PKCS #11 operations with the environment variables
GNUTLS_PIN and GNUTLS_SO_PIN.
.sp
.SH "OPTIONS"
.SS "Tokens"
.TP
.NOP \f\*[B-Font]\-\-list\-tokens\f[]
List all available tokens.
.sp
.TP
.NOP \f\*[B-Font]\-\-list\-token\-urls\f[]
List the URLs available tokens.
.sp
This is a more compact version of \-\-list\-tokens.
.TP
.NOP \f\*[B-Font]\-\-list\-mechanisms\f[]
List all available mechanisms in a token.
.sp
.TP
.NOP \f\*[B-Font]\-\-initialize\f[]
Initializes a PKCS #11 token.
.sp
.TP
.NOP \f\*[B-Font]\-\-initialize\-pin\f[]
Initializes/Resets a PKCS #11 token user PIN.
.sp
.TP
.NOP \f\*[B-Font]\-\-initialize\-so\-pin\f[]
Initializes/Resets a PKCS #11 token security officer PIN.
.sp
This initializes the security officer's PIN. When used non\-interactively use the GNUTLS_NEW_SO_PIN
environment variables to initialize SO's PIN.
.TP
.NOP \f\*[B-Font]\-\-set\-pin\f[]=\f\*[I-Font]str\f[]
Specify the PIN to use on token operations.
.sp
Alternatively the GNUTLS_PIN environment variable may be used.
.TP
.NOP \f\*[B-Font]\-\-set\-so\-pin\f[]=\f\*[I-Font]str\f[]
Specify the Security Officer's PIN to use on token initialization.
.sp
Alternatively the GNUTLS_SO_PIN environment variable may be used.
.SS "Object listing"
.TP
.NOP \f\*[B-Font]\-\-list\-all\f[]
List all available objects in a token.
.sp
All objects available in the token will be listed. That includes
objects which are potentially unaccessible using this tool.
.TP
.NOP \f\*[B-Font]\-\-list\-all\-certs\f[]
List all available certificates in a token.
.sp
That option will also provide more information on the
certificates, for example, expand the attached extensions in a trust
token (like p11\-kit\-trust).
.TP
.NOP \f\*[B-Font]\-\-list\-certs\f[]
List all certificates that have an associated private key.
.sp
That option will only display certificates which have a private
key associated with them (share the same ID).
.TP
.NOP \f\*[B-Font]\-\-list\-all\-privkeys\f[]
List all available private keys in a token.
.sp
Lists all the private keys in a token that match the specified URL.
.TP
.NOP \f\*[B-Font]\-\-list\-privkeys\f[]
This is an alias for the \fI--list-all-privkeys\fR option.
.TP
.NOP \f\*[B-Font]\-\-list\-keys\f[]
This is an alias for the \fI--list-all-privkeys\fR option.
.TP
.NOP \f\*[B-Font]\-\-list\-all\-trusted\f[]
List all available certificates marked as trusted.
.sp
.TP
.NOP \f\*[B-Font]\-\-export\f[]
Export the object specified by the URL.
This option must not appear in combination with any of the following options:
export-stapled, export-chain, export-pubkey.
.sp
.TP
.NOP \f\*[B-Font]\-\-export\-stapled\f[]
Export the certificate object specified by the URL.
This option must not appear in combination with any of the following options:
export, export-chain, export-pubkey.
.sp
Exports the certificate specified by the URL while including any attached extensions to it.
Since attached extensions are a p11\-kit extension, this option is only
available on p11\-kit registered trust modules.
.TP
.NOP \f\*[B-Font]\-\-export\-chain\f[]
Export the certificate specified by the URL and its chain of trust.
This option must not appear in combination with any of the following options:
export-stapled, export, export-pubkey.
.sp
Exports the certificate specified by the URL and generates its chain of trust based on the stored certificates in the module.
.TP
.NOP \f\*[B-Font]\-\-export\-pubkey\f[]
Export the public key for a private key.
This option must not appear in combination with any of the following options:
export-stapled, export, export-chain.
.sp
Exports the public key for the specified private key
.TP
.NOP \f\*[B-Font]\-\-info\f[]
List information on an available object in a token.
.sp
.TP
.NOP \f\*[B-Font]\-\-trusted\f[]
This is an alias for the \fI--mark-trusted\fR option.
.TP
.NOP \f\*[B-Font]\-\-distrusted\f[]
This is an alias for the \fI--mark-distrusted\fR option.
.SS "Key generation"
.TP
.NOP \f\*[B-Font]\-\-generate\-privkey\f[]=\f\*[I-Font]str\f[]
Generate private-public key pair of given type.
.sp
Generates a private\-public key pair in the specified token.
Acceptable types are RSA, ECDSA, Ed25519, and DSA. Should be combined with \-\-sec\-param or \-\-bits.
.TP
.NOP \f\*[B-Font]\-\-generate\-rsa\f[]
Generate an RSA private-public key pair.
.sp
Generates an RSA private\-public key pair on the specified token.
Should be combined with \-\-sec\-param or \-\-bits.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-generate\-dsa\f[]
Generate a DSA private-public key pair.
.sp
Generates a DSA private\-public key pair on the specified token.
Should be combined with \-\-sec\-param or \-\-bits.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-generate\-ecc\f[]
Generate an ECDSA private-public key pair.
.sp
Generates an ECDSA private\-public key pair on the specified token.
Should be combined with \-\-curve, \-\-sec\-param or \-\-bits.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-bits\f[]=\f\*[I-Font]num\f[]
Specify the number of bits for the key generate.
This option takes an integer number as its argument.
.sp
For applications which have no key\-size restrictions the
\-\-sec\-param option is recommended, as the sec\-param levels will adapt
to the acceptable security levels with the new versions of gnutls.
.TP
.NOP \f\*[B-Font]\-\-curve\f[]=\f\*[I-Font]str\f[]
Specify the curve used for EC key generation.
.sp
Supported values are secp192r1, secp224r1, secp256r1, secp384r1 and secp521r1.
.TP
.NOP \f\*[B-Font]\-\-sec\-param\f[]=\f\*[I-Font]security parameter\f[]
Specify the security level.
.sp
This is alternative to the bits option. Available options are [low, legacy, medium, high, ultra].
.SS "Writing objects"
.TP
.NOP \f\*[B-Font]\-\-set\-id\f[]=\f\*[I-Font]str\f[]
Set the CKA_ID (in hex) for the specified by the URL object.
This option must not appear in combination with any of the following options:
write.
.sp
Modifies or sets the CKA_ID in the specified by the URL object. The ID should be specified in hexadecimal format without a '0x' prefix.
.TP
.NOP \f\*[B-Font]\-\-set\-label\f[]=\f\*[I-Font]str\f[]
Set the CKA_LABEL for the specified by the URL object.
This option must not appear in combination with any of the following options:
write, set-id.
.sp
Modifies or sets the CKA_LABEL in the specified by the URL object
.TP
.NOP \f\*[B-Font]\-\-write\f[]
Writes the loaded objects to a PKCS #11 token.
.sp
It can be used to write private, public keys, certificates or secret keys to a token. Must be combined with one of \-\-load\-privkey, \-\-load\-pubkey, \-\-load\-certificate option.
.sp
When writing a certificate object, its CKA_ID is set to the same CKA_ID of the corresponding public key, if it exists on the token; otherwise it will be derived from the X.509 Subject Key Identifier of the certificate. If this behavior is undesired, write the public key to the token beforehand.
.TP
.NOP \f\*[B-Font]\-\-delete\f[]
Deletes the objects matching the given PKCS #11 URL.
.sp
.TP
.NOP \f\*[B-Font]\-\-label\f[]=\f\*[I-Font]str\f[]
Sets a label for the write operation.
.sp
.TP
.NOP \f\*[B-Font]\-\-id\f[]=\f\*[I-Font]str\f[]
Sets an ID for the write operation.
.sp
Sets the CKA_ID to be set by the write operation. The ID should be specified in hexadecimal format without a '0x' prefix.
.TP
.NOP \f\*[B-Font]\-\-mark\-wrap\f[], \f\*[B-Font]\-\-no\-mark\-wrap\f[]
Marks the generated key to be a wrapping key.
The \fIno\-mark\-wrap\fP form will disable the option.
.sp
Marks the generated key with the CKA_WRAP flag.
.TP
.NOP \f\*[B-Font]\-\-mark\-trusted\f[], \f\*[B-Font]\-\-no\-mark\-trusted\f[]
Marks the object to be written as trusted.
This option must not appear in combination with any of the following options:
mark-distrusted.
The \fIno\-mark\-trusted\fP form will disable the option.
.sp
Marks the object to be generated/written with the CKA_TRUST flag.
.TP
.NOP \f\*[B-Font]\-\-mark\-distrusted\f[]
When retrieving objects, it requires the objects to be distrusted.
This option must not appear in combination with any of the following options:
mark-trusted.
.sp
Ensures that the objects retrieved have the CKA_X_TRUST flag.
This is p11\-kit trust module extension, thus this flag is only valid with
p11\-kit registered trust modules.
.TP
.NOP \f\*[B-Font]\-\-mark\-decrypt\f[], \f\*[B-Font]\-\-no\-mark\-decrypt\f[]
Marks the object to be written for decryption.
The \fIno\-mark\-decrypt\fP form will disable the option.
.sp
Marks the object to be generated/written with the CKA_DECRYPT flag set to true.
.TP
.NOP \f\*[B-Font]\-\-mark\-sign\f[], \f\*[B-Font]\-\-no\-mark\-sign\f[]
Marks the object to be written for signature generation.
The \fIno\-mark\-sign\fP form will disable the option.
.sp
Marks the object to be generated/written with the CKA_SIGN flag set to true.
.TP
.NOP \f\*[B-Font]\-\-mark\-ca\f[], \f\*[B-Font]\-\-no\-mark\-ca\f[]
Marks the object to be written as a CA.
The \fIno\-mark\-ca\fP form will disable the option.
.sp
Marks the object to be generated/written with the CKA_CERTIFICATE_CATEGORY as CA.
.TP
.NOP \f\*[B-Font]\-\-mark\-private\f[], \f\*[B-Font]\-\-no\-mark\-private\f[]
Marks the object to be written as private.
The \fIno\-mark\-private\fP form will disable the option.
.sp
Marks the object to be generated/written with the CKA_PRIVATE flag. The written object will require a PIN to be used.
.TP
.NOP \f\*[B-Font]\-\-ca\f[]
This is an alias for the \fI--mark-ca\fR option.
.TP
.NOP \f\*[B-Font]\-\-private\f[]
This is an alias for the \fI--mark-private\fR option.
.TP
.NOP \f\*[B-Font]\-\-mark\-always\-authenticate\f[], \f\*[B-Font]\-\-no\-mark\-always\-authenticate\f[]
Marks the object to be written as always authenticate.
The \fIno\-mark\-always\-authenticate\fP form will disable the option.
.sp
Marks the object to be generated/written with the CKA_ALWAYS_AUTHENTICATE flag. The written object will Mark the object as requiring authentication (pin entry) before every operation.
.TP
.NOP \f\*[B-Font]\-\-secret\-key\f[]=\f\*[I-Font]str\f[]
Provide a hex encoded secret key.
.sp
This secret key will be written to the module if \-\-write is specified.
.TP
.NOP \f\*[B-Font]\-\-load\-privkey\f[]=\f\*[I-Font]file\f[]
Private key file to use.
.sp
.TP
.NOP \f\*[B-Font]\-\-load\-pubkey\f[]=\f\*[I-Font]file\f[]
Public key file to use.
.sp
.TP
.NOP \f\*[B-Font]\-\-load\-certificate\f[]=\f\*[I-Font]file\f[]
Certificate file to use.
.sp
.SS "Other options"
.TP
.NOP \f\*[B-Font]\-d\f[] \f\*[I-Font]num\f[], \f\*[B-Font]\-\-debug\f[]=\f\*[I-Font]num\f[]
Enable debugging.
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 9999
.fi
.in -4
.sp
Specifies the debug level.
.TP
.NOP \f\*[B-Font]\-\-outfile\f[]=\f\*[I-Font]str\f[]
Output file.
.sp
.TP
.NOP \f\*[B-Font]\-\-login\f[], \f\*[B-Font]\-\-no\-login\f[]
Force (user) login to token.
The \fIno\-login\fP form will disable the option.
.sp
.TP
.NOP \f\*[B-Font]\-\-so\-login\f[], \f\*[B-Font]\-\-no\-so\-login\f[]
Force security officer login to token.
The \fIno\-so\-login\fP form will disable the option.
.sp
Forces login to the token as security officer (admin).
.TP
.NOP \f\*[B-Font]\-\-admin\-login\f[]
This is an alias for the \fI--so-login\fR option.
.TP
.NOP \f\*[B-Font]\-\-test\-sign\f[]
Tests the signature operation of the provided object.
.sp
It can be used to test the correct operation of the signature operation.
If both a private and a public key are available this operation will sign and verify
the signed data.
.TP
.NOP \f\*[B-Font]\-\-sign\-params\f[]=\f\*[I-Font]str\f[]
Sign with a specific signature algorithm.
.sp
This option can be combined with \-\-test\-sign, to sign with
a specific signature algorithm variant. The only option supported is 'RSA\-PSS', and should be
specified in order to use RSA\-PSS signature on RSA keys.
.TP
.NOP \f\*[B-Font]\-\-hash\f[]=\f\*[I-Font]str\f[]
Hash algorithm to use for signing.
.sp
This option can be combined with test\-sign. Available hash functions are SHA1, RMD160, SHA256, SHA384, SHA512, SHA3\-224, SHA3\-256, SHA3\-384, SHA3\-512.
.TP
.NOP \f\*[B-Font]\-\-generate\-random\f[]=\f\*[I-Font]num\f[]
Generate random data.
This option takes an integer number as its argument.
.sp
Asks the token to generate a number of bytes of random bytes.
.TP
.NOP \f\*[B-Font]\-8\f[], \f\*[B-Font]\-\-pkcs8\f[]
Use PKCS #8 format for private keys.
.sp
.TP
.NOP \f\*[B-Font]\-\-inder\f[], \f\*[B-Font]\-\-no\-inder\f[]
Use DER/RAW format for input.
The \fIno\-inder\fP form will disable the option.
.sp
Use DER/RAW format for input certificates and private keys.
.TP
.NOP \f\*[B-Font]\-\-inraw\f[]
This is an alias for the \fI--inder\fR option.
.TP
.NOP \f\*[B-Font]\-\-outder\f[], \f\*[B-Font]\-\-no\-outder\f[]
Use DER format for output certificates, private keys, and DH parameters.
The \fIno\-outder\fP form will disable the option.
.sp
The output will be in DER or RAW format.
.TP
.NOP \f\*[B-Font]\-\-outraw\f[]
This is an alias for the \fI--outder\fR option.
.TP
.NOP \f\*[B-Font]\-\-provider\f[]=\f\*[I-Font]file\f[]
Specify the PKCS #11 provider library.
.sp
This will override the default options in /etc/gnutls/pkcs11.conf
.TP
.NOP \f\*[B-Font]\-\-provider\-opts\f[]=\f\*[I-Font]str\f[]
Specify parameters for the PKCS #11 provider library.
.sp
This is a PKCS#11 internal option used by few modules.
    Mainly for testing PKCS#11 modules.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-detailed\-url\f[], \f\*[B-Font]\-\-no\-detailed\-url\f[]
Print detailed URLs.
The \fIno\-detailed\-url\fP form will disable the option.
.sp
.TP
.NOP \f\*[B-Font]\-\-only\-urls\f[]
Print a compact listing using only the URLs.
.sp
.TP
.NOP \f\*[B-Font]\-\-batch\f[]
Disable all interaction with the tool.
.sp
In batch mode there will be no prompts, all parameters need to be specified on command line.
.TP
.NOP \f\*[B-Font]\-v\f[] \f\*[I-Font]arg\f[], \f\*[B-Font]\-\-version\f[]=\f\*[I-Font]arg\f[]
Output version of program and exit.  The default mode is `v', a simple
version.  The `c' mode will print copyright information and `n' will
print the full copyright notice.
.TP
.NOP \f\*[B-Font]\-h\f[], \f\*[B-Font]\-\-help\f[]
Display usage information and exit.
.TP
.NOP \f\*[B-Font]\-!\f[], \f\*[B-Font]\-\-more\-help\f[]
Pass the extended usage information through a pager.

.sp
.SH EXAMPLES
To view all tokens in your system use:
.br
.in +4
.nf
$ p11tool \-\-list\-tokens
.in -4
.fi
.sp
To view all objects in a token use:
.br
.in +4
.nf
$ p11tool \-\-login \-\-list\-all "pkcs11:TOKEN\-URL"
.in -4
.fi
.sp
To store a private key and a certificate in a token run:
.br
.in +4
.nf
$ p11tool \-\-login \-\-write "pkcs11:URL" \-\-load\-privkey key.pem \
          \-\-label "Mykey"
$ p11tool \-\-login \-\-write "pkcs11:URL" \-\-load\-certificate cert.pem \
          \-\-label "Mykey"
.in -4
.fi
Note that some tokens require the same label to be used for the certificate
and its corresponding private key.
.sp
To generate an RSA private key inside the token use:
.br
.in +4
.nf
$ p11tool \-\-login \-\-generate\-privkey rsa \-\-bits 1024 \-\-label "MyNewKey" \
          \-\-outfile MyNewKey.pub "pkcs11:TOKEN\-URL"
.in -4
.fi
The bits parameter in the above example is explicitly set because some
tokens only support limited choices in the bit length. The output file is the
corresponding public key. This key can be used to general a certificate
request with certtool.
.br
.in +4
.nf
certtool \-\-generate\-request \-\-load\-privkey "pkcs11:KEY\-URL" \
   \-\-load\-pubkey MyNewKey.pub \-\-outfile request.pem
.in -4
.fi
.SH "EXIT STATUS"
One of the following exit values will be returned:
.TP
.NOP 0 " (EXIT_SUCCESS)"
Successful program execution.
.TP
.NOP 1 " (EXIT_FAILURE)"
The operation failed or the command syntax was not valid.
.PP
.SH "SEE ALSO"
certtool (1)
.SH "AUTHORS"

.SH "COPYRIGHT"
Copyright (C) 2020-2023 Free Software Foundation, and others all rights reserved.
This program is released under the terms of
the GNU General Public License, version 3 or later
.
.SH "BUGS"
Please send bug reports to: <EMAIL>
