<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>provider-digest</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Context-Management-Functions">Context Management Functions</a></li>
      <li><a href="#Digest-Generation-Functions">Digest Generation Functions</a></li>
      <li><a href="#Digest-Parameters">Digest Parameters</a></li>
      <li><a href="#Digest-Context-Parameters">Digest Context Parameters</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>provider-digest - The digest library &lt;-&gt; provider functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/core_dispatch.h&gt;
#include &lt;openssl/core_names.h&gt;

/*
 * Digests support the following function signatures in OSSL_DISPATCH arrays.
 * (The function signatures are not actual functions).
 */

/* Context management */
void *OSSL_FUNC_digest_newctx(void *provctx);
void OSSL_FUNC_digest_freectx(void *dctx);
void *OSSL_FUNC_digest_dupctx(void *dctx);
void OSSL_FUNC_digest_copyctx(void *voutctx, void *vinctx);

/* Digest generation */
int OSSL_FUNC_digest_init(void *dctx, const OSSL_PARAM params[]);
int OSSL_FUNC_digest_update(void *dctx, const unsigned char *in, size_t inl);
int OSSL_FUNC_digest_final(void *dctx, unsigned char *out, size_t *outl,
                           size_t outsz);
int OSSL_FUNC_digest_digest(void *provctx, const unsigned char *in, size_t inl,
                            unsigned char *out, size_t *outl, size_t outsz);

/* Digest parameter descriptors */
const OSSL_PARAM *OSSL_FUNC_digest_gettable_params(void *provctx);

/* Digest operation parameter descriptors */
const OSSL_PARAM *OSSL_FUNC_digest_gettable_ctx_params(void *dctx,
                                                       void *provctx);
const OSSL_PARAM *OSSL_FUNC_digest_settable_ctx_params(void *dctx,
                                                       void *provctx);

/* Digest parameters */
int OSSL_FUNC_digest_get_params(OSSL_PARAM params[]);

/* Digest operation parameters */
int OSSL_FUNC_digest_set_ctx_params(void *dctx, const OSSL_PARAM params[]);
int OSSL_FUNC_digest_get_ctx_params(void *dctx, OSSL_PARAM params[]);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This documentation is primarily aimed at provider authors. See <a href="../man7/provider.html">provider(7)</a> for further information.</p>

<p>The DIGEST operation enables providers to implement digest algorithms and make them available to applications via the API functions <a href="../man3/EVP_DigestInit_ex.html">EVP_DigestInit_ex(3)</a>, <a href="../man3/EVP_DigestUpdate.html">EVP_DigestUpdate(3)</a> and <a href="../man3/EVP_DigestFinal.html">EVP_DigestFinal(3)</a> (and other related functions).</p>

<p>All &quot;functions&quot; mentioned here are passed as function pointers between <i>libcrypto</i> and the provider in <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays via <a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a> arrays that are returned by the provider&#39;s provider_query_operation() function (see <a href="../man7/provider-base.html">&quot;Provider Functions&quot; in provider-base(7)</a>).</p>

<p>All these &quot;functions&quot; have a corresponding function type definition named <b>OSSL_FUNC_{name}_fn</b>, and a helper function to retrieve the function pointer from an <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> element named <b>OSSL_FUNC_{name}</b>. For example, the &quot;function&quot; OSSL_FUNC_digest_newctx() has these:</p>

<pre><code>typedef void *(OSSL_FUNC_digest_newctx_fn)(void *provctx);
static ossl_inline OSSL_FUNC_digest_newctx_fn
    OSSL_FUNC_digest_newctx(const OSSL_DISPATCH *opf);</code></pre>

<p><a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays are indexed by numbers that are provided as macros in <a href="../man7/openssl-core_dispatch.h.html">openssl-core_dispatch.h(7)</a>, as follows:</p>

<pre><code>OSSL_FUNC_digest_newctx               OSSL_FUNC_DIGEST_NEWCTX
OSSL_FUNC_digest_freectx              OSSL_FUNC_DIGEST_FREECTX
OSSL_FUNC_digest_dupctx               OSSL_FUNC_DIGEST_DUPCTX
OSSL_FUNC_digest_copyctx              OSSL_FUNC_DIGEST_COPYCTX

OSSL_FUNC_digest_init                 OSSL_FUNC_DIGEST_INIT
OSSL_FUNC_digest_update               OSSL_FUNC_DIGEST_UPDATE
OSSL_FUNC_digest_final                OSSL_FUNC_DIGEST_FINAL
OSSL_FUNC_digest_digest               OSSL_FUNC_DIGEST_DIGEST

OSSL_FUNC_digest_get_params           OSSL_FUNC_DIGEST_GET_PARAMS
OSSL_FUNC_digest_get_ctx_params       OSSL_FUNC_DIGEST_GET_CTX_PARAMS
OSSL_FUNC_digest_set_ctx_params       OSSL_FUNC_DIGEST_SET_CTX_PARAMS

OSSL_FUNC_digest_gettable_params      OSSL_FUNC_DIGEST_GETTABLE_PARAMS
OSSL_FUNC_digest_gettable_ctx_params  OSSL_FUNC_DIGEST_GETTABLE_CTX_PARAMS
OSSL_FUNC_digest_settable_ctx_params  OSSL_FUNC_DIGEST_SETTABLE_CTX_PARAMS</code></pre>

<p>A digest algorithm implementation may not implement all of these functions. In order to be usable all or none of OSSL_FUNC_digest_newctx, OSSL_FUNC_digest_freectx, OSSL_FUNC_digest_init, OSSL_FUNC_digest_update, OSSL_FUNC_digest_final and OSSL_FUNC_digest_get_params should be implemented. All other functions are optional.</p>

<h2 id="Context-Management-Functions">Context Management Functions</h2>

<p>OSSL_FUNC_digest_newctx() should create and return a pointer to a provider side structure for holding context information during a digest operation. A pointer to this context will be passed back in a number of the other digest operation function calls. The parameter <i>provctx</i> is the provider context generated during provider initialisation (see <a href="../man7/provider.html">provider(7)</a>).</p>

<p>OSSL_FUNC_digest_freectx() is passed a pointer to the provider side digest context in the <i>dctx</i> parameter. This function should free any resources associated with that context.</p>

<p>OSSL_FUNC_digest_dupctx() should duplicate the provider side digest context in the <i>dctx</i> parameter and return the duplicate copy.</p>

<p>OSSL_FUNC_digest_copyctx() should copy the provider side digest context in the <i>vinctx</i> parameter to the <i>voutctx</i> parameter which is the another provider side context. The OSSL_FUNC_digest_copyctx function is used in the EVP_MD_CTX_copy_ex function to speed up HMAC operations in the PBKDF2. This function is optional, and dupctx will be used if there is no EVP_MD_CTX_copy_ex function.</p>

<h2 id="Digest-Generation-Functions">Digest Generation Functions</h2>

<p>OSSL_FUNC_digest_init() initialises a digest operation given a newly created provider side digest context in the <i>dctx</i> parameter. The <i>params</i>, if not NULL, should be set on the context in a manner similar to using OSSL_FUNC_digest_set_ctx_params().</p>

<p>OSSL_FUNC_digest_update() is called to supply data to be digested as part of a previously initialised digest operation. The <i>dctx</i> parameter contains a pointer to a previously initialised provider side context. OSSL_FUNC_digest_update() should digest <i>inl</i> bytes of data at the location pointed to by <i>in</i>. OSSL_FUNC_digest_update() may be called multiple times for a single digest operation.</p>

<p>OSSL_FUNC_digest_final() generates a digest started through previous OSSL_FUNC_digest_init() and OSSL_FUNC_digest_update() calls. The <i>dctx</i> parameter contains a pointer to the provider side context. The digest should be written to <i>*out</i> and the length of the digest to <i>*outl</i>. The digest should not exceed <i>outsz</i> bytes.</p>

<p>OSSL_FUNC_digest_digest() is a &quot;oneshot&quot; digest function. No provider side digest context is used. Instead the provider context that was created during provider initialisation is passed in the <i>provctx</i> parameter (see <a href="../man7/provider.html">provider(7)</a>). <i>inl</i> bytes at <i>in</i> should be digested and the result should be stored at <i>out</i>. The length of the digest should be stored in <i>*outl</i> which should not exceed <i>outsz</i> bytes.</p>

<h2 id="Digest-Parameters">Digest Parameters</h2>

<p>See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for further details on the parameters structure used by these functions.</p>

<p>OSSL_FUNC_digest_get_params() gets details of the algorithm implementation and stores them in <i>params</i>.</p>

<p>OSSL_FUNC_digest_set_ctx_params() sets digest operation parameters for the provider side digest context <i>dctx</i> to <i>params</i>. Any parameter settings are additional to any that were previously set. Passing NULL for <i>params</i> should return true.</p>

<p>OSSL_FUNC_digest_get_ctx_params() gets digest operation details details from the given provider side digest context <i>dctx</i> and stores them in <i>params</i>. Passing NULL for <i>params</i> should return true.</p>

<p>OSSL_FUNC_digest_gettable_params() returns a constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array containing descriptors of the parameters that OSSL_FUNC_digest_get_params() can handle.</p>

<p>OSSL_FUNC_digest_gettable_ctx_params() and OSSL_FUNC_digest_settable_ctx_params() both return constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> arrays as descriptors of the parameters that OSSL_FUNC_digest_get_ctx_params() and OSSL_FUNC_digest_set_ctx_params() can handle, respectively. The array is based on the current state of the provider side context if <i>dctx</i> is not NULL and on the provider side algorithm <i>provctx</i> otherwise.</p>

<p>Parameters currently recognised by built-in digests with this function are as follows. Not all parameters are relevant to, or are understood by all digests:</p>

<dl>

<dt id="blocksize-OSSL_DIGEST_PARAM_BLOCK_SIZE-unsigned-integer">&quot;blocksize&quot; (<b>OSSL_DIGEST_PARAM_BLOCK_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>The digest block size. The length of the &quot;blocksize&quot; parameter should not exceed that of a <b>size_t</b>.</p>

</dd>
<dt id="size-OSSL_DIGEST_PARAM_SIZE-unsigned-integer">&quot;size&quot; (<b>OSSL_DIGEST_PARAM_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>The digest output size. The length of the &quot;size&quot; parameter should not exceed that of a <b>size_t</b>.</p>

</dd>
<dt id="flags-OSSL_DIGEST_PARAM_FLAGS-unsigned-integer">&quot;flags&quot; (<b>OSSL_DIGEST_PARAM_FLAGS</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Diverse flags that describe exceptional behaviour for the digest:</p>

<dl>

<dt id="EVP_MD_FLAG_ONESHOT"><b>EVP_MD_FLAG_ONESHOT</b></dt>
<dd>

<p>This digest method can only handle one block of input.</p>

</dd>
<dt id="EVP_MD_FLAG_XOF"><b>EVP_MD_FLAG_XOF</b></dt>
<dd>

<p>This digest method is an extensible-output function (XOF).</p>

</dd>
<dt id="EVP_MD_FLAG_DIGALGID_NULL"><b>EVP_MD_FLAG_DIGALGID_NULL</b></dt>
<dd>

<p>When setting up a DigestAlgorithmIdentifier, this flag will have the parameter set to NULL by default. Use this for PKCS#1. <i>Note: if combined with EVP_MD_FLAG_DIGALGID_ABSENT, the latter will override.</i></p>

</dd>
<dt id="EVP_MD_FLAG_DIGALGID_ABSENT"><b>EVP_MD_FLAG_DIGALGID_ABSENT</b></dt>
<dd>

<p>When setting up a DigestAlgorithmIdentifier, this flag will have the parameter be left absent by default. <i>Note: if combined with EVP_MD_FLAG_DIGALGID_NULL, the latter will be overridden.</i></p>

</dd>
<dt id="EVP_MD_FLAG_DIGALGID_CUSTOM"><b>EVP_MD_FLAG_DIGALGID_CUSTOM</b></dt>
<dd>

<p>Custom DigestAlgorithmIdentifier handling via ctrl, with <b>EVP_MD_FLAG_DIGALGID_ABSENT</b> as default. <i>Note: if combined with EVP_MD_FLAG_DIGALGID_NULL, the latter will be overridden.</i> Currently unused.</p>

</dd>
</dl>

<p>The length of the &quot;flags&quot; parameter should equal that of an <b>unsigned long int</b>.</p>

</dd>
</dl>

<h2 id="Digest-Context-Parameters">Digest Context Parameters</h2>

<p>OSSL_FUNC_digest_set_ctx_params() sets digest parameters associated with the given provider side digest context <i>dctx</i> to <i>params</i>. Any parameter settings are additional to any that were previously set. See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for further details on the parameters structure.</p>

<p>OSSL_FUNC_digest_get_ctx_params() gets details of currently set parameters values associated with the give provider side digest context <i>dctx</i> and stores them in <i>params</i>. See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for further details on the parameters structure.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_FUNC_digest_newctx() and OSSL_FUNC_digest_dupctx() should return the newly created provider side digest context, or NULL on failure.</p>

<p>OSSL_FUNC_digest_init(), OSSL_FUNC_digest_update(), OSSL_FUNC_digest_final(), OSSL_FUNC_digest_digest(), OSSL_FUNC_digest_set_params() and OSSL_FUNC_digest_get_params() should return 1 for success or 0 on error.</p>

<p>OSSL_FUNC_digest_size() should return the digest size.</p>

<p>OSSL_FUNC_digest_block_size() should return the block size of the underlying digest algorithm.</p>

<h1 id="BUGS">BUGS</h1>

<p>The EVP_Q_digest(), EVP_Digest() and EVP_DigestFinal_ex() API calls do not expect the digest size to be larger than EVP_MAX_MD_SIZE. Any algorithm which produces larger digests is unusable with those API calls.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider.html">provider(7)</a>, <a href="../man7/OSSL_PROVIDER-FIPS.html">OSSL_PROVIDER-FIPS(7)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a>, <a href="../man7/OSSL_PROVIDER-legacy.html">OSSL_PROVIDER-legacy(7)</a>, <a href="../man7/EVP_MD-common.html">EVP_MD-common(7)</a>, <a href="../man7/EVP_MD-BLAKE2.html">EVP_MD-BLAKE2(7)</a>, <a href="../man7/EVP_MD-MD2.html">EVP_MD-MD2(7)</a>, <a href="../man7/EVP_MD-MD4.html">EVP_MD-MD4(7)</a>, <a href="../man7/EVP_MD-MD5.html">EVP_MD-MD5(7)</a>, <a href="../man7/EVP_MD-MD5-SHA1.html">EVP_MD-MD5-SHA1(7)</a>, <a href="../man7/EVP_MD-MDC2.html">EVP_MD-MDC2(7)</a>, <a href="../man7/EVP_MD-RIPEMD160.html">EVP_MD-RIPEMD160(7)</a>, <a href="../man7/EVP_MD-SHA1.html">EVP_MD-SHA1(7)</a>, <a href="../man7/EVP_MD-SHA2.html">EVP_MD-SHA2(7)</a>, <a href="../man7/EVP_MD-SHA3.html">EVP_MD-SHA3(7)</a>, <a href="../man7/EVP_MD-KECCAK.html">EVP_MD-KECCAK(7)</a> <a href="../man7/EVP_MD-SHAKE.html">EVP_MD-SHAKE(7)</a>, <a href="../man7/EVP_MD-SM3.html">EVP_MD-SM3(7)</a>, <a href="../man7/EVP_MD-WHIRLPOOL.html">EVP_MD-WHIRLPOOL(7)</a>, <a href="../man7/EVP_MD-NULL.html">EVP_MD-NULL(7)</a>, <a href="../man7/life_cycle-digest.html">life_cycle-digest(7)</a>, <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The provider DIGEST interface was introduced in OpenSSL 3.0. OSSL_FUNC_digest_copyctx() was added in 3.5 version.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2025 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


