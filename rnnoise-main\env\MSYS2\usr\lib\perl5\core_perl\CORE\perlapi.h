/*
 *
 *    perlapi.h
 *
 *    Copyright (C) 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001,
 *    2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009 by <PERSON> and others
 *
 *    You may distribute under the terms of either the GNU General Public
 *    License or the Artistic License, as specified in the README file.
 *
 */

/*
 * This file used to declare accessor functions for Perl variables
 * when PERL_GLOBAL_STRUCT was enabled, but that no longer exists.
 * This file is kept for backwards compatibility with XS code that
 * might include it.
 */
#ifndef __perlapi_h__
#define __perlapi_h__

#endif /* __perlapi_h__ */
