--- 
type: map
mapping:
  comment:
    type: text
  depends:
    type: map
    mapping:
      configure_requires:
        &requires_common
        type: map
        mapping:
          =:
            type: text
      build_requires: *requires_common
      requires:       *requires_common
  match:
    type: map
    mapping:
      distribution:
        type: text
      module:
        type: text
      perl:
        type: text
      perlconfig:
        &matchhash_common
        type: map
        mapping:
          =:
            type: text
      env: *matchhash_common
  install:
    &args_env_expect
    type: map
    mapping:
      args:
        type: seq
        sequence:
          - type: text
      commandline:
        type: text
      env:
        type: map
        mapping:
          =:
            type: text
      expect:
        type: seq
        sequence:
          - type: text
      eexpect:
        type: map
        mapping:
          mode:
            type: text
            enum:
              - deterministic
              - anyorder
          timeout:
            type: number
          reuse:
            type: int
          talk:
            type: seq
            sequence:
              - type: text
  make: *args_env_expect
  pl:   *args_env_expect
  test: *args_env_expect
  patches:
    type: seq
    sequence:
      - type: text
  disabled:
    type: int
    enum:
      - 0
      - 1
  goto:
    type: text
  cpanconfig:
    type: map
    mapping:
      =:
        type: text
  features:
    type: seq
    sequence:
      - type: text
  reminder:
    type: text
