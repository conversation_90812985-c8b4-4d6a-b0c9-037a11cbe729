.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_get_certs" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_get_certs \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_get_certs(gnutls_ocsp_resp_const_t " resp ", gnutls_x509_crt_t ** " certs ", size_t * " ncerts ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
should contain a \fBgnutls_ocsp_resp_t\fP type
.IP "gnutls_x509_crt_t ** certs" 12
newly allocated array with \fBgnutls_x509_crt_t\fP certificates
.IP "size_t * ncerts" 12
output variable with number of allocated certs.
.SH "DESCRIPTION"
This function will extract the X.509 certificates found in the
Basic OCSP Response.  The  \fIcerts\fP output variable will hold a newly
allocated zero\-terminated array with X.509 certificates.

Every certificate in the array needs to be de\-allocated with
\fBgnutls_x509_crt_deinit()\fP and the array itself must be freed using
\fBgnutls_free()\fP.

Both the  \fIcerts\fP and  \fIncerts\fP variables may be NULL.  Then the
function will work as normal but will not return the NULL:d
information.  This can be used to get the number of certificates
only, or to just get the certificate array without its size.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
