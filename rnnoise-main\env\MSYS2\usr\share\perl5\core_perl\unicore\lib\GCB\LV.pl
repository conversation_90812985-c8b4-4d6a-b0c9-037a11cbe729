# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V798
44032
44033
44060
44061
44088
44089
44116
44117
44144
44145
44172
44173
44200
44201
44228
44229
44256
44257
44284
44285
44312
44313
44340
44341
44368
44369
44396
44397
44424
44425
44452
44453
44480
44481
44508
44509
44536
44537
44564
44565
44592
44593
44620
44621
44648
44649
44676
44677
44704
44705
44732
44733
44760
44761
44788
44789
44816
44817
44844
44845
44872
44873
44900
44901
44928
44929
44956
44957
44984
44985
45012
45013
45040
45041
45068
45069
45096
45097
45124
45125
45152
45153
45180
45181
45208
45209
45236
45237
45264
45265
45292
45293
45320
45321
45348
45349
45376
45377
45404
45405
45432
45433
45460
45461
45488
45489
45516
45517
45544
45545
45572
45573
45600
45601
45628
45629
45656
45657
45684
45685
45712
45713
45740
45741
45768
45769
45796
45797
45824
45825
45852
45853
45880
45881
45908
45909
45936
45937
45964
45965
45992
45993
46020
46021
46048
46049
46076
46077
46104
46105
46132
46133
46160
46161
46188
46189
46216
46217
46244
46245
46272
46273
46300
46301
46328
46329
46356
46357
46384
46385
46412
46413
46440
46441
46468
46469
46496
46497
46524
46525
46552
46553
46580
46581
46608
46609
46636
46637
46664
46665
46692
46693
46720
46721
46748
46749
46776
46777
46804
46805
46832
46833
46860
46861
46888
46889
46916
46917
46944
46945
46972
46973
47000
47001
47028
47029
47056
47057
47084
47085
47112
47113
47140
47141
47168
47169
47196
47197
47224
47225
47252
47253
47280
47281
47308
47309
47336
47337
47364
47365
47392
47393
47420
47421
47448
47449
47476
47477
47504
47505
47532
47533
47560
47561
47588
47589
47616
47617
47644
47645
47672
47673
47700
47701
47728
47729
47756
47757
47784
47785
47812
47813
47840
47841
47868
47869
47896
47897
47924
47925
47952
47953
47980
47981
48008
48009
48036
48037
48064
48065
48092
48093
48120
48121
48148
48149
48176
48177
48204
48205
48232
48233
48260
48261
48288
48289
48316
48317
48344
48345
48372
48373
48400
48401
48428
48429
48456
48457
48484
48485
48512
48513
48540
48541
48568
48569
48596
48597
48624
48625
48652
48653
48680
48681
48708
48709
48736
48737
48764
48765
48792
48793
48820
48821
48848
48849
48876
48877
48904
48905
48932
48933
48960
48961
48988
48989
49016
49017
49044
49045
49072
49073
49100
49101
49128
49129
49156
49157
49184
49185
49212
49213
49240
49241
49268
49269
49296
49297
49324
49325
49352
49353
49380
49381
49408
49409
49436
49437
49464
49465
49492
49493
49520
49521
49548
49549
49576
49577
49604
49605
49632
49633
49660
49661
49688
49689
49716
49717
49744
49745
49772
49773
49800
49801
49828
49829
49856
49857
49884
49885
49912
49913
49940
49941
49968
49969
49996
49997
50024
50025
50052
50053
50080
50081
50108
50109
50136
50137
50164
50165
50192
50193
50220
50221
50248
50249
50276
50277
50304
50305
50332
50333
50360
50361
50388
50389
50416
50417
50444
50445
50472
50473
50500
50501
50528
50529
50556
50557
50584
50585
50612
50613
50640
50641
50668
50669
50696
50697
50724
50725
50752
50753
50780
50781
50808
50809
50836
50837
50864
50865
50892
50893
50920
50921
50948
50949
50976
50977
51004
51005
51032
51033
51060
51061
51088
51089
51116
51117
51144
51145
51172
51173
51200
51201
51228
51229
51256
51257
51284
51285
51312
51313
51340
51341
51368
51369
51396
51397
51424
51425
51452
51453
51480
51481
51508
51509
51536
51537
51564
51565
51592
51593
51620
51621
51648
51649
51676
51677
51704
51705
51732
51733
51760
51761
51788
51789
51816
51817
51844
51845
51872
51873
51900
51901
51928
51929
51956
51957
51984
51985
52012
52013
52040
52041
52068
52069
52096
52097
52124
52125
52152
52153
52180
52181
52208
52209
52236
52237
52264
52265
52292
52293
52320
52321
52348
52349
52376
52377
52404
52405
52432
52433
52460
52461
52488
52489
52516
52517
52544
52545
52572
52573
52600
52601
52628
52629
52656
52657
52684
52685
52712
52713
52740
52741
52768
52769
52796
52797
52824
52825
52852
52853
52880
52881
52908
52909
52936
52937
52964
52965
52992
52993
53020
53021
53048
53049
53076
53077
53104
53105
53132
53133
53160
53161
53188
53189
53216
53217
53244
53245
53272
53273
53300
53301
53328
53329
53356
53357
53384
53385
53412
53413
53440
53441
53468
53469
53496
53497
53524
53525
53552
53553
53580
53581
53608
53609
53636
53637
53664
53665
53692
53693
53720
53721
53748
53749
53776
53777
53804
53805
53832
53833
53860
53861
53888
53889
53916
53917
53944
53945
53972
53973
54000
54001
54028
54029
54056
54057
54084
54085
54112
54113
54140
54141
54168
54169
54196
54197
54224
54225
54252
54253
54280
54281
54308
54309
54336
54337
54364
54365
54392
54393
54420
54421
54448
54449
54476
54477
54504
54505
54532
54533
54560
54561
54588
54589
54616
54617
54644
54645
54672
54673
54700
54701
54728
54729
54756
54757
54784
54785
54812
54813
54840
54841
54868
54869
54896
54897
54924
54925
54952
54953
54980
54981
55008
55009
55036
55037
55064
55065
55092
55093
55120
55121
55148
55149
55176
55177
END
