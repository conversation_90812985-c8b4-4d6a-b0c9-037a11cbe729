set ::msgcat::header "Project-Id-Version: gitk @@GIT_VERSION@@\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2015-09-15 07:33+0700\nLast-Translator: Tr\u1ea7n Ng\u1ecdc Qu\u00e2n <<EMAIL>>\nLanguage-Team: Vietnamese <<EMAIL>>\nLanguage: vi\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=1; plural=0;\nX-Generator: Gtranslator 2.91.7\n"
::msgcat::mcset vi "Couldn't get list of unmerged files:" "Kh\u00f4ng th\u1ec3 l\u1ea5y danh s\u00e1ch c\u00e1c t\u1eadp-tin ch\u01b0a \u0111\u01b0\u1ee3c h\u00f2a tr\u1ed9n:"
::msgcat::mcset vi "Color words" "T\u00f4 m\u00e0u ch\u1eef"
::msgcat::mcset vi "Markup words" "\u0110\u00e1nh d\u1ea5u ch\u1eef"
::msgcat::mcset vi "Error parsing revisions:" "G\u1eb7p l\u1ed7i khi ph\u00e2n t\u00edch \u0111i\u1ec3m x\u00e9t duy\u1ec7t:"
::msgcat::mcset vi "Error executing --argscmd command:" "G\u1eb7p l\u1ed7i khi th\u1ef1c hi\u1ec7n l\u1ec7nh --argscmd:"
::msgcat::mcset vi "No files selected: --merge specified but no files are unmerged." "Ch\u01b0a ch\u1ecdn t\u1eadp tin: --merge \u0111\u00e3 ch\u1ec9 \u0111\u1ecbnh nh\u01b0ng kh\u00f4ng c\u00f3 t\u1eadp tin ch\u01b0a h\u00f2a tr\u1ed9n."
::msgcat::mcset vi "No files selected: --merge specified but no unmerged files are within file limit." "Ch\u01b0a ch\u1ecdn t\u1eadp tin: --merge \u0111\u00e3 ch\u1ec9 \u0111\u1ecbnh nh\u01b0ng kh\u00f4ng c\u00f3 t\u1eadp tin ch\u01b0a h\u00f2a tr\u1ed9n trong gi\u1edbi h\u1ea1n t\u1eadp tin."
::msgcat::mcset vi "Error executing git log:" "G\u1eb7p l\u1ed7i khi th\u1ef1c hi\u1ec7n l\u1ec7nh git log:"
::msgcat::mcset vi "Reading" "\u0110ang \u0111\u1ecdc"
::msgcat::mcset vi "Reading commits..." "\u0110ang \u0111\u1ecdc c\u00e1c l\u1ea7n chuy\u1ec3n giao\u2026"
::msgcat::mcset vi "No commits selected" "Ch\u01b0a ch\u1ecdn c\u00e1c l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Command line" "D\u00f2ng l\u1ec7nh"
::msgcat::mcset vi "Can't parse git log output:" "Kh\u00f4ng th\u1ec3 ph\u00e2n t\u00edch k\u1ebft xu\u1ea5t t\u1eeb l\u1ec7nh git log:"
::msgcat::mcset vi "No commit information available" "Kh\u00f4ng c\u00f3 th\u00f4ng tin v\u1ec1 l\u1ea7n chuy\u1ec3n giao n\u00e0o"
::msgcat::mcset vi "OK" "\u0110\u1ed3ng \u00fd"
::msgcat::mcset vi "Cancel" "Th\u00f4i"
::msgcat::mcset vi "&Update" "C\u1eadp nh\u1eadt"
::msgcat::mcset vi "&Reload" "T\u1ea3i l\u1ea1i"
::msgcat::mcset vi "Reread re&ferences" "\u0110\u1ecdc l\u1ea1i tham chi\u1ebfu"
::msgcat::mcset vi "&List references" "Li\u1ec7t k\u00ea c\u00e1c tham chi\u1ebfu"
::msgcat::mcset vi "Start git &gui" "Kh\u1edfi ch\u1ea1y git gui"
::msgcat::mcset vi "&Quit" "Tho\u00e1t"
::msgcat::mcset vi "&File" "Ch\u00ednh"
::msgcat::mcset vi "&Preferences" "T\u00f9y th\u00edch"
::msgcat::mcset vi "&Edit" "Ch\u1ec9nh s\u1eeda"
::msgcat::mcset vi "&New view..." "Th\u00eam tr\u00ecnh b\u00e0y m\u1edbi\u2026"
::msgcat::mcset vi "&Edit view..." "S\u1eeda c\u00e1ch tr\u00ecnh b\u00e0y\u2026"
::msgcat::mcset vi "&Delete view" "X\u00f3a c\u00e1ch tr\u00ecnh b\u00e0y"
::msgcat::mcset vi "&All files" "M\u1ecdi t\u1eadp tin"
::msgcat::mcset vi "&View" "Tr\u00ecnh b\u00e0y"
::msgcat::mcset vi "&About gitk" "Gi\u1edbi thi\u1ec7u v\u1ec1 gitk"
::msgcat::mcset vi "&Key bindings" "T\u1ed5 h\u1ee3p ph\u00edm"
::msgcat::mcset vi "&Help" "Tr\u1ee3 gi\u00fap"
::msgcat::mcset vi "SHA1 ID:" "SHA1 ID:"
::msgcat::mcset vi "Row" "H\u00e0ng"
::msgcat::mcset vi "Find" "T\u00ecm"
::msgcat::mcset vi "commit" "l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "containing:" "c\u00f3 ch\u1ee9a:"
::msgcat::mcset vi "touching paths:" "\u0111ang ch\u1ea1m \u0111\u01b0\u1eddng d\u1eabn:"
::msgcat::mcset vi "adding/removing string:" "th\u00eam/g\u1ee1 b\u1ecf chu\u1ed7i:"
::msgcat::mcset vi "changing lines matching:" "nh\u1eefng d\u00f2ng thay \u0111\u1ed5i kh\u1edbp m\u1eabu:"
::msgcat::mcset vi "Exact" "Ch\u00ednh x\u00e1c"
::msgcat::mcset vi "IgnCase" "BquaHt"
::msgcat::mcset vi "Regexp" "BTCQ"
::msgcat::mcset vi "All fields" "M\u1ecdi tr\u01b0\u1eddng"
::msgcat::mcset vi "Headline" "N\u1ed9i dung ch\u00ednh"
::msgcat::mcset vi "Comments" "Ghi ch\u00fa"
::msgcat::mcset vi "Author" "T\u00e1c gi\u1ea3"
::msgcat::mcset vi "Committer" "Ng\u01b0\u1eddi chuy\u1ec3n giao"
::msgcat::mcset vi "Search" "T\u00ecm ki\u1ebfm"
::msgcat::mcset vi "Diff" "So s\u00e1nh"
::msgcat::mcset vi "Old version" "Phi\u00ean b\u1ea3n c\u0169"
::msgcat::mcset vi "New version" "Phi\u00ean b\u1ea3n m\u1edbi"
::msgcat::mcset vi "Lines of context" "C\u00e1c d\u00f2ng c\u1ee7a n\u1ed9i dung"
::msgcat::mcset vi "Ignore space change" "Kh\u00f4ng x\u00e9t \u0111\u1ebfn thay \u0111\u1ed5i do kho\u1ea3ng tr\u1eafng"
::msgcat::mcset vi "Line diff" "Kh\u00e1c bi\u1ec7t theo d\u00f2ng"
::msgcat::mcset vi "Patch" "V\u00e1"
::msgcat::mcset vi "Tree" "C\u00e2y"
::msgcat::mcset vi "Diff this -> selected" "So s\u00e1nh c\u00e1i n\u00e0y -> c\u00e1i \u0111\u00e3 ch\u1ecdn"
::msgcat::mcset vi "Diff selected -> this" "So s\u00e1nh c\u00e1i \u0111\u00e3 ch\u1ecdn -> c\u00e1i n\u00e0y"
::msgcat::mcset vi "Make patch" "T\u1ea1o mi\u1ebfng v\u00e1"
::msgcat::mcset vi "Create tag" "T\u1ea1o th\u1ebb"
::msgcat::mcset vi "Write commit to file" "Ghi l\u1ea7n chuy\u1ec3n giao ra t\u1eadp tin"
::msgcat::mcset vi "Create new branch" "T\u1ea1o nh\u00e1nh m\u1edbi"
::msgcat::mcset vi "Cherry-pick this commit" "Cherry-pick l\u1ea7n chuy\u1ec3n giao n\u00e0y"
::msgcat::mcset vi "Reset HEAD branch to here" "\u0110\u1eb7t l\u1ea1i HEAD c\u1ee7a nh\u00e1nh v\u00e0o \u0111\u00e2y"
::msgcat::mcset vi "Mark this commit" "\u0110\u00e1nh d\u1ea5u l\u1ea7n chuy\u1ec3n giao n\u00e0y"
::msgcat::mcset vi "Return to mark" "Quay l\u1ea1i v\u1ecb tr\u00ed d\u1ea5u"
::msgcat::mcset vi "Find descendant of this and mark" "T\u00ecm con ch\u00e1u c\u1ee7a c\u00e1i n\u00e0y v\u00e0 c\u00e1i \u0111\u00e3 \u0111\u00e1nh d\u1ea5u"
::msgcat::mcset vi "Compare with marked commit" "So s\u00e1nh v\u1edbi l\u1ea7n chuy\u1ec3n giao \u0111\u00e3 \u0111\u00e1nh d\u1ea5u"
::msgcat::mcset vi "Diff this -> marked commit" "So s\u00e1nh c\u00e1i n\u00e0y -> l\u1ea7n chuy\u1ec3n giao \u0111\u00e3 \u0111\u00e1nh d\u1ea5u"
::msgcat::mcset vi "Diff marked commit -> this" "So s\u00e1nh l\u1ea7n chuy\u1ec3n giao \u0111\u00e3 \u0111\u00e1nh d\u1ea5u -> c\u00e1i n\u00e0y"
::msgcat::mcset vi "Revert this commit" "Ho\u00e0n l\u1ea1i l\u1ea7n chuy\u1ec3n giao n\u00e0y"
::msgcat::mcset vi "Check out this branch" "L\u1ea5y ra nh\u00e1nh n\u00e0y"
::msgcat::mcset vi "Remove this branch" "G\u1ee1 b\u1ecf nh\u00e1nh n\u00e0y"
::msgcat::mcset vi "Copy branch name" "Ch\u00e9p t\u00ean nh\u00e1nh"
::msgcat::mcset vi "Highlight this too" "C\u0169ng t\u00f4 s\u00e1ng n\u00f3"
::msgcat::mcset vi "Highlight this only" "Ch\u1ec9 t\u00f4 s\u00e1ng c\u00e1i n\u00e0y"
::msgcat::mcset vi "External diff" "diff t\u1eeb b\u00ean ngo\u00e0i"
::msgcat::mcset vi "Blame parent commit" "Xem c\u00f4ng tr\u1ea1ng l\u1ea7n chuy\u1ec3n giao cha m\u1eb9"
::msgcat::mcset vi "Copy path" "Ch\u00e9p \u0111\u01b0\u1eddng d\u1eabn"
::msgcat::mcset vi "Show origin of this line" "Hi\u1ec3n th\u1ecb nguy\u00ean g\u1ed1c c\u1ee7a d\u00f2ng n\u00e0y"
::msgcat::mcset vi "Run git gui blame on this line" "Ch\u1ea1y l\u1ec7nh git gui blame cho d\u00f2ng n\u00e0y"
::msgcat::mcset vi "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk - \u1ee9ng d\u1ee5ng \u0111\u1ec3 xem c\u00e1c l\u1ea7n chuy\u1ec3n giao d\u00e0nh cho git\n\nB\u1ea3n quy\u1ec1n \u00a9 2005-2016 Paul Mackerras\n\nD\u00f9ng v\u00e0 ph\u00e2n ph\u1ed1i l\u1ea1i ph\u1ea7n m\u1ec1m n\u00e0y theo c\u00e1c \u0111i\u1ec1u kho\u1ea3n c\u1ee7a Gi\u1ea5y Ph\u00e9p C\u00f4ng GNU"
::msgcat::mcset vi "Close" "\u0110\u00f3ng"
::msgcat::mcset vi "Gitk key bindings" "T\u1ed5 h\u1ee3p ph\u00edm gitk"
::msgcat::mcset vi "Gitk key bindings:" "T\u1ed5 h\u1ee3p ph\u00edm gitk:"
::msgcat::mcset vi "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Tho\u00e1t"
::msgcat::mcset vi "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009\u0110\u00f3ng c\u1eeda s\u1ed5"
::msgcat::mcset vi "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009Chuy\u1ec3n \u0111\u1ebfn l\u1ea7n chuy\u1ec3n giao \u0111\u1ea7u ti\u00ean"
::msgcat::mcset vi "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009Chuy\u1ec3n \u0111\u1ebfn l\u1ea7n chuy\u1ec3n giao cu\u1ed1i"
::msgcat::mcset vi "<Up>, p, k\u0009Move up one commit" "<Up>, p, k\u0009Di chuy\u1ec3n l\u00ean m\u1ed9t l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "<Down>, n, j\u0009Move down one commit" "<Down>, n, j\u0009Di chuy\u1ec3n xu\u1ed1ng m\u1ed9t l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "<Left>, z, h\u0009Go back in history list" "<Left>, z, h\u0009Quay tr\u1edf l\u1ea1i danh s\u00e1ch l\u1ecbch s\u1eed"
::msgcat::mcset vi "<Right>, x, l\u0009Go forward in history list" "<Right>, x, l\u0009Di chuy\u1ec3n ti\u1ebfp trong danh s\u00e1ch l\u1ecbch s\u1eed"
::msgcat::mcset vi "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n>\u0009\u0110\u1ebfn cha th\u1ee9 n c\u1ee7a l\u1ea7n chuy\u1ec3n giao hi\u1ec7n t\u1ea1i trong danh s\u00e1ch l\u1ecbch s\u1eed"
::msgcat::mcset vi "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009Di chuy\u1ec3n l\u00ean m\u1ed9t trang trong danh s\u00e1ch l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009Di chuy\u1ec3n xu\u1ed1ng m\u1ed9t trang trong danh s\u00e1ch l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009Cu\u1ed9n l\u00ean tr\u00ean c\u00f9ng c\u1ee7a danh s\u00e1ch l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009Cu\u1ed9n xu\u1ed1ng d\u01b0\u1edbi c\u00f9ng c\u1ee7a danh s\u00e1ch l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "<%s-Up>\u0009Scroll commit list up one line" "<%s-Up>\u0009Cu\u1ed9n danh s\u00e1ch l\u1ea7n chuy\u1ec3n giao l\u00ean m\u1ed9t d\u00f2ng"
::msgcat::mcset vi "<%s-Down>\u0009Scroll commit list down one line" "<%s-Down>\u0009Cu\u1ed9n danh s\u00e1ch l\u1ea7n chuy\u1ec3n giao xu\u1ed1ng m\u1ed9t d\u00f2ng"
::msgcat::mcset vi "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009Cu\u1ed9n danh s\u00e1ch l\u1ea7n chuy\u1ec3n giao l\u00ean m\u1ed9t trang"
::msgcat::mcset vi "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009Cu\u1ed9n danh s\u00e1ch l\u1ea7n chuy\u1ec3n giao xu\u1ed1ng m\u1ed9t trang"
::msgcat::mcset vi "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Up>\u0009T\u00ecm v\u1ec1 ph\u00eda sau (h\u01b0\u1edbng l\u00ean tr\u00ean, l\u1ea7n chuy\u1ec3n giao sau n\u00e0y)"
::msgcat::mcset vi "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Down>\u0009T\u00ecm v\u1ec1 ph\u00eda tr\u01b0\u1edbc (h\u01b0\u1edbng xu\u1ed1ng d\u01b0\u1edbi, l\u1ea7n chuy\u1ec3n giao tr\u01b0\u1edbc \u0111\u00e2y)"
::msgcat::mcset vi "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009Cu\u1ed9n ph\u1ea7n tr\u00ecnh b\u00e0y diff l\u00ean m\u1ed9t trang"
::msgcat::mcset vi "<Backspace>\u0009Scroll diff view up one page" "<Backspace>\u0009Cu\u1ed9n ph\u1ea7n tr\u00ecnh b\u00e0y diff l\u00ean m\u1ed9t trang"
::msgcat::mcset vi "<Space>\u0009\u0009Scroll diff view down one page" "<Space>\u0009\u0009Cu\u1ed9n ph\u1ea7n tr\u00ecnh b\u00e0y diff xu\u1ed1ng m\u1ed9t trang"
::msgcat::mcset vi "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009Cu\u1ed9n ph\u1ea7n tr\u00ecnh b\u00e0y diff l\u00ean 18 d\u00f2ng"
::msgcat::mcset vi "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009Cu\u1ed9n ph\u1ea7n tr\u00ecnh b\u00e0y diff xu\u1ed1ng 18 d\u00f2ng"
::msgcat::mcset vi "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009T\u00ecm ki\u1ebfm"
::msgcat::mcset vi "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009Di chuy\u1ec3n \u0111\u1ebfn ch\u1ed7 g\u1eb7p k\u1ebf ti\u1ebfp"
::msgcat::mcset vi "<Return>\u0009Move to next find hit" "<Return>\u0009\u0009Di chuy\u1ec3n \u0111\u1ebfn ch\u1ed7 g\u1eb7p k\u1ebf ti\u1ebfp"
::msgcat::mcset vi "g\u0009\u0009Go to commit" "g\u0009\u0009Chuy\u1ec3n \u0111\u1ebfn l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "/\u0009\u0009Focus the search box" "/\u0009\u0009\u0110\u01b0a con tr\u1ecf chu\u1ed9t v\u00e0o \u00f4 t\u00ecm ki\u1ebfm"
::msgcat::mcset vi "?\u0009\u0009Move to previous find hit" "?\u0009\u0009Di chuy\u1ec3n \u0111\u1ebfn ch\u1ed7 g\u1eb7p k\u1ebf tr\u01b0\u1edbc"
::msgcat::mcset vi "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009Cu\u1ed9n ph\u1ea7n tr\u00ecnh b\u00e0y diff sang t\u1eadp-tin k\u1ebf"
::msgcat::mcset vi "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009T\u00ecm \u0111\u1ebfn ch\u1ed7 kh\u00e1c bi\u1ec7t k\u1ebf ti\u1ebfp"
::msgcat::mcset vi "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009T\u00ecm \u0111\u1ebfn ch\u1ed7 kh\u00e1c bi\u1ec7t k\u1ebf tr\u01b0\u1edbc"
::msgcat::mcset vi "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009T\u0103ng c\u1ee1 ch\u1eef"
::msgcat::mcset vi "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009T\u0103ng c\u1ee1 ch\u1eef"
::msgcat::mcset vi "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009Gi\u1ea3m c\u1ee1 ch\u1eef"
::msgcat::mcset vi "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009Gi\u1ea3m c\u1ee1 ch\u1eef"
::msgcat::mcset vi "<F5>\u0009\u0009Update" "<F5>\u0009\u0009C\u1eadp nh\u1eadt"
::msgcat::mcset vi "Error creating temporary directory %s:" "G\u1eb7p l\u1ed7i khi t\u1ea1o th\u01b0 m\u1ee5c t\u1ea1m %s:"
::msgcat::mcset vi "Error getting \"%s\" from %s:" "L\u1ed7i ch\u00e0o h\u1ecfi \"%s\" t\u1eeb %s:"
::msgcat::mcset vi "command failed:" "l\u1ec7nh g\u1eb7p l\u1ed7i:"
::msgcat::mcset vi "No such commit" "Kh\u00f4ng c\u00f3 l\u1ea7n chuy\u1ec3n giao nh\u01b0 v\u1eady"
::msgcat::mcset vi "git gui blame: command failed:" "git gui blame: l\u1ec7nh g\u1eb7p l\u1ed7i:"
::msgcat::mcset vi "Couldn't read merge head: %s" "Kh\u00f4ng th\u1ec3 \u0111\u1ed9c \u0111\u1ea7u c\u1ee7a h\u00f2a tr\u1ed9n: %s"
::msgcat::mcset vi "Error reading index: %s" "G\u1eb7p l\u1ed7i khi \u0111\u1ecdc ch\u1ec9 m\u1ee5c: %s"
::msgcat::mcset vi "Couldn't start git blame: %s" "Kh\u00f4ng th\u1ec3 kh\u1edfi ch\u1ea1y git blame: %s"
::msgcat::mcset vi "Searching" "\u0110ang t\u00ecm ki\u1ebfm"
::msgcat::mcset vi "Error running git blame: %s" "G\u1eb7p l\u1ed7i khi ch\u1ea1y git blame: %s"
::msgcat::mcset vi "That line comes from commit %s,  which is not in this view" "D\u00f2ng \u0111\u1ebfn t\u1eeb l\u1ea7n chuy\u1ec3n giao %s, c\u00e1i m\u00e0 kh\u00f4ng trong tr\u00ecnh b\u00e0y n\u00e0y"
::msgcat::mcset vi "External diff viewer failed:" "B\u1ed9 tr\u00ecnh b\u00e0y diff t\u1eeb b\u00ean ngo\u00e0i g\u1eb7p l\u1ed7i:"
::msgcat::mcset vi "Gitk view definition" "\u0110\u1ecbnh ngh\u0129a c\u00e1ch tr\u00ecnh b\u00e0y gitk"
::msgcat::mcset vi "Remember this view" "Nh\u1edb c\u00e1ch tr\u00ecnh b\u00e0y n\u00e0y"
::msgcat::mcset vi "References (space separated list):" "Tham chi\u1ebfu (danh s\u00e1ch ng\u0103n c\u00e1ch b\u1eb1ng d\u1ea5u c\u00e1ch):"
::msgcat::mcset vi "Branches & tags:" "Nh\u00e1nh & th\u1ebb:"
::msgcat::mcset vi "All refs" "M\u1ecdi tham chi\u1ebfu"
::msgcat::mcset vi "All (local) branches" "M\u1ecdi nh\u00e1nh (n\u1ed9i b\u1ed9)"
::msgcat::mcset vi "All tags" "M\u1ecdi th\u1ebb"
::msgcat::mcset vi "All remote-tracking branches" "M\u1ecdi nh\u00e1nh remote-tracking"
::msgcat::mcset vi "Commit Info (regular expressions):" "Th\u00f4ng tin chuy\u1ec3n giao (bi\u1ec3u th\u1ee9c ch\u00ednh quy):"
::msgcat::mcset vi "Author:" "T\u00e1c gi\u1ea3:"
::msgcat::mcset vi "Committer:" "Ng\u01b0\u1eddi chuy\u1ec3n giao:"
::msgcat::mcset vi "Commit Message:" "Ch\u00fa th\u00edch c\u1ee7a l\u1ea7n chuy\u1ec3n giao:"
::msgcat::mcset vi "Matches all Commit Info criteria" "Kh\u1edbp m\u1ecdi \u0111i\u1ec1u ki\u1ec7n Th\u00f4ng tin Chuy\u1ec3n giao"
::msgcat::mcset vi "Matches no Commit Info criteria" "Kh\u1edbp kh\u00f4ng \u0111i\u1ec1u ki\u1ec7n Th\u00f4ng tin Chuy\u1ec3n giao"
::msgcat::mcset vi "Changes to Files:" "\u0110\u1ed5i th\u00e0nh T\u1eadp tin:"
::msgcat::mcset vi "Fixed String" "Chu\u1ed7i c\u1ed1 \u0111\u1ecbnh"
::msgcat::mcset vi "Regular Expression" "Bi\u1ec3u th\u1ee9c ch\u00ednh quy"
::msgcat::mcset vi "Search string:" "Chu\u1ed7i t\u00ecm ki\u1ebfm:"
::msgcat::mcset vi "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "Ng\u00e0y chuy\u1ec3n giao (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):"
::msgcat::mcset vi "Since:" "K\u1ec3 t\u1eeb:"
::msgcat::mcset vi "Until:" "\u0110\u1ebfn:"
::msgcat::mcset vi "Limit and/or skip a number of revisions (positive integer):" "Gi\u1edbi h\u1ea1n v\u00e0/ho\u1eb7c b\u1ecf s\u1ed1 c\u1ee7a \u0111i\u1ec3m x\u00e9t (s\u1ed1 nguy\u00ean \u00e2m):"
::msgcat::mcset vi "Number to show:" "S\u1ed1 l\u01b0\u1ee3ng hi\u1ec3n th\u1ecb:"
::msgcat::mcset vi "Number to skip:" "S\u1ed1 l\u01b0\u1ee3ng s\u1ebd b\u1ecf qua:"
::msgcat::mcset vi "Miscellaneous options:" "T\u00f9y ch\u1ecdn h\u1ed7n h\u1ee3p:"
::msgcat::mcset vi "Strictly sort by date" "S\u1eafp x\u1ebfp ch\u1eb7t ch\u1ebd theo ng\u00e0y"
::msgcat::mcset vi "Mark branch sides" "\u0110\u00e1nh d\u1ea5u c\u00e1c c\u1ea1nh nh\u00e1nh"
::msgcat::mcset vi "Limit to first parent" "Gi\u1edbi h\u1ea1n th\u00e0nh cha m\u1eb9 \u0111\u1ea7u ti\u00ean"
::msgcat::mcset vi "Simple history" "L\u1ecbch s\u1eed d\u1ea1ng \u0111\u01a1n gi\u1ea3n"
::msgcat::mcset vi "Additional arguments to git log:" "\u0110\u1ed1i s\u1ed1 b\u1ed5 xung cho l\u1ec7nh git log:"
::msgcat::mcset vi "Enter files and directories to include, one per line:" "Nh\u1eadp v\u00e0o c\u00e1c t\u1eadp tin v\u00e0 th\u01b0 m\u1ee5c bao g\u1ed3m, m\u1ed7i d\u00f2ng m\u1ed9t c\u00e1i:"
::msgcat::mcset vi "Command to generate more commits to include:" "L\u1ec7nh t\u1ea1o ra nhi\u1ec1u l\u1ea7n chuy\u1ec3n giao h\u01a1n bao g\u1ed3m:"
::msgcat::mcset vi "Gitk: edit view" "Gitk: s\u1eeda c\u00e1ch tr\u00ecnh b\u00e0y"
::msgcat::mcset vi "-- criteria for selecting revisions" "-- ti\u00eau chu\u1ea9n ch\u1ecdn \u0111i\u1ec3m x\u00e9t duy\u1ec7t"
::msgcat::mcset vi "View Name" "T\u00ean c\u00e1ch tr\u00ecnh b\u00e0y"
::msgcat::mcset vi "Apply (F5)" "\u00c1p d\u1ee5ng (F5)"
::msgcat::mcset vi "Error in commit selection arguments:" "L\u1ed7i trong c\u00e1c \u0111\u1ed1i s\u1ed1 ch\u1ecdn chuy\u1ec3n giao:"
::msgcat::mcset vi "None" "Kh\u00f4ng"
::msgcat::mcset vi "Descendant" "Con ch\u00e1u"
::msgcat::mcset vi "Not descendant" "Kh\u00f4ng c\u00f3 con ch\u00e1u"
::msgcat::mcset vi "Ancestor" "T\u1ed5 ti\u00ean chung"
::msgcat::mcset vi "Not ancestor" "Kh\u00f4ng c\u00f3 chung t\u1ed5 ti\u00ean"
::msgcat::mcset vi "Local changes checked in to index but not committed" "C\u00f3 thay \u0111\u1ed5i n\u1ed9i b\u1ed9 \u0111\u00e3 \u0111\u01b0\u1ee3c \u0111\u01b0a v\u00e0o b\u1ea3ng m\u1ee5c l\u1ee5c, nh\u01b0ng ch\u01b0a \u0111\u01b0\u1ee3c chuy\u1ec3n giao"
::msgcat::mcset vi "Local uncommitted changes, not checked in to index" "C\u00f3 thay \u0111\u1ed5i n\u1ed9i b\u1ed9, nh\u01b0ng ch\u01b0a \u0111\u01b0\u1ee3c \u0111\u01b0a v\u00e0o b\u1ea3ng m\u1ee5c l\u1ee5c"
::msgcat::mcset vi "and many more" "v\u00e0 nhi\u1ec1u n\u1eefa"
::msgcat::mcset vi "many" "nhi\u1ec1u"
::msgcat::mcset vi "Tags:" "Th\u1ebb:"
::msgcat::mcset vi "Parent" "Cha"
::msgcat::mcset vi "Child" "Con"
::msgcat::mcset vi "Branch" "Nh\u00e1nh"
::msgcat::mcset vi "Follows" "\u0110\u1ee9ng sau"
::msgcat::mcset vi "Precedes" "\u0110\u1ee9ng tr\u01b0\u1edbc"
::msgcat::mcset vi "Error getting diffs: %s" "L\u1ed7i l\u1ea5y diff: %s"
::msgcat::mcset vi "Goto:" "Nh\u1ea3y t\u1edbi:"
::msgcat::mcset vi "Short SHA1 id %s is ambiguous" "\u0110\u1ecbnh danh SHA1 d\u1ea1ng ng\u1eafn %s l\u00e0 ch\u01b0a \u0111\u1ee7 r\u00f5 r\u00e0ng"
::msgcat::mcset vi "Revision %s is not known" "Kh\u00f4ng hi\u1ec3u \u0111i\u1ec3m x\u00e9t duy\u1ec7t %s"
::msgcat::mcset vi "SHA1 id %s is not known" "Kh\u00f4ng hi\u1ec3u \u0111\u1ecbnh danh SHA1 %s"
::msgcat::mcset vi "Revision %s is not in the current view" "\u0110i\u1ec3m %s kh\u00f4ng \u1edf trong ph\u1ea7n hi\u1ec3n th\u1ecb hi\u1ec7n t\u1ea1i"
::msgcat::mcset vi "Date" "Ng\u00e0y"
::msgcat::mcset vi "Children" "Con ch\u00e1u"
::msgcat::mcset vi "Reset %s branch to here" "\u0110\u1eb7t l\u1ea1i nh\u00e1nh %s t\u1ea1i \u0111\u00e2y"
::msgcat::mcset vi "Detached head: can't reset" "Head \u0111\u00e3 b\u1ecb t\u00e1ch r\u1eddi: kh\u00f4ng th\u1ec3 \u0111\u1eb7t l\u1ea1i"
::msgcat::mcset vi "Skipping merge commit " "B\u1ecf qua l\u1ea7n chuy\u1ec3n giao h\u00f2a tr\u1ed9n "
::msgcat::mcset vi "Error getting patch ID for " "G\u1eb7p l\u1ed7i khi l\u1ea5y ID mi\u1ebfng v\u00e1 cho "
::msgcat::mcset vi " - stopping\n" " - d\u1eebng\n"
::msgcat::mcset vi "Commit " "Commit "
::msgcat::mcset vi " is the same patch as\n       " " l\u00e0 c\u00f9ng m\u1ed9t mi\u1ebfng v\u00e1 v\u1edbi\n       "
::msgcat::mcset vi " differs from\n       " " kh\u00e1c bi\u1ec7t t\u1eeb\n       "
::msgcat::mcset vi "Diff of commits:\n\n" "Kh\u00e1c bi\u1ec7t c\u1ee7a l\u1ea7n chuy\u1ec3n giao (commit):\n\n"
::msgcat::mcset vi " has %s children - stopping\n" " c\u00f3 %s con - d\u1eebng\n"
::msgcat::mcset vi "Error writing commit to file: %s" "G\u1eb7p l\u1ed7i trong qu\u00e1 tr\u00ecnh ghi l\u1ea7n chuy\u1ec3n giao v\u00e0o t\u1eadp tin: %s"
::msgcat::mcset vi "Error diffing commits: %s" "G\u1eb7p l\u1ed7i khi so s\u00e1nh s\u1ef1 kh\u00e1c bi\u1ec7t gi\u1eefa c\u00e1c l\u1ea7n chuy\u1ec3n giao: %s"
::msgcat::mcset vi "Top" "\u0110\u1ec9nh"
::msgcat::mcset vi "From" "T\u1eeb"
::msgcat::mcset vi "To" "\u0110\u1ebfn"
::msgcat::mcset vi "Generate patch" "T\u1ea1o mi\u1ebfng v\u00e1"
::msgcat::mcset vi "From:" "T\u1eeb:"
::msgcat::mcset vi "To:" "\u0110\u1ebfn:"
::msgcat::mcset vi "Reverse" "\u0110\u1ea3o ng\u01b0\u1ee3c"
::msgcat::mcset vi "Output file:" "T\u1eadp tin k\u1ebft xu\u1ea5t:"
::msgcat::mcset vi "Generate" "T\u1ea1o"
::msgcat::mcset vi "Error creating patch:" "G\u1eb7p l\u1ed7i khi t\u1ea1o mi\u1ebfng v\u00e1:"
::msgcat::mcset vi "ID:" "M\u00e3 s\u1ed1:"
::msgcat::mcset vi "Tag name:" "T\u00ean th\u1ebb:"
::msgcat::mcset vi "Tag message is optional" "Ghi ch\u00fa th\u1ebb ch\u1ec9 l\u00e0 t\u00f9y ch\u1ecdn"
::msgcat::mcset vi "Tag message:" "Ghi ch\u00fa cho th\u1ebb:"
::msgcat::mcset vi "Create" "T\u1ea1o"
::msgcat::mcset vi "No tag name specified" "Ch\u01b0a ch\u1ec9 ra t\u00ean c\u1ee7a th\u1ebb"
::msgcat::mcset vi "Tag \"%s\" already exists" "Th\u1ebb \u201c%s\u201d \u0111\u00e3 c\u00f3 s\u1eb5n r\u1ed3i"
::msgcat::mcset vi "Error creating tag:" "G\u1eb7p l\u1ed7i khi t\u1ea1o th\u1ebb:"
::msgcat::mcset vi "Command:" "L\u1ec7nh:"
::msgcat::mcset vi "Write" "Ghi"
::msgcat::mcset vi "Error writing commit:" "G\u1eb7p l\u1ed7i trong qu\u00e1 tr\u00ecnh ghi chuy\u1ec3n giao:"
::msgcat::mcset vi "Name:" "T\u00ean:"
::msgcat::mcset vi "Please specify a name for the new branch" "Vui l\u00f2ng ch\u1ec9 \u0111\u1ecbnh t\u00ean cho nh\u00e1nh m\u1edbi"
::msgcat::mcset vi "Branch '%s' already exists. Overwrite?" "Nh\u00e1nh \u201c%s\u201d \u0111\u00e3 c\u00f3 t\u1eeb tr\u01b0\u1edbc r\u1ed3i. Ghi \u0111\u00e8?"
::msgcat::mcset vi "Commit %s is already included in branch %s -- really re-apply it?" "L\u1ea7n chuy\u1ec3n giao %s \u0111\u00e3 s\u1eb5n \u0111\u01b0\u1ee3c bao g\u1ed3m trong nh\u00e1nh %s -- b\u1ea1n c\u00f3 th\u1ef1c s\u1ef1 mu\u1ed1n \u00e1p d\u1ee5ng l\u1ea1i n\u00f3 kh\u00f4ng?"
::msgcat::mcset vi "Cherry-picking" "\u0110ang cherry-pick"
::msgcat::mcset vi "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "Cherry-pick g\u1eb7p l\u1ed7i b\u1edfi v\u00ec c\u00e1c thay \u0111\u1ed5i n\u1ed9i b\u1ed9 t\u1eadp tin \u201c%s\u201d.\nXin h\u00e3y chuy\u1ec3n giao, reset hay stash c\u00e1c thay \u0111\u1ed5i c\u1ee7a b\u1ea1n sau \u0111\u00f3 th\u1eed l\u1ea1i."
::msgcat::mcset vi "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "Cherry-pick g\u1eb7p l\u1ed7i b\u1edfi v\u00ec xung \u0111\u1ed9t trong h\u00f2a tr\u1ed9n.\nB\u1ea1n c\u00f3 mu\u1ed1n ch\u1ea1y l\u1ec7nh \u201cgit citool\u201d \u0111\u1ec3 gi\u1ea3i quy\u1ebft v\u1ea5n \u0111\u1ec1 n\u00e0y kh\u00f4ng?"
::msgcat::mcset vi "No changes committed" "Kh\u00f4ng c\u00f3 thay \u0111\u1ed5i n\u00e0o c\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Commit %s is not included in branch %s -- really revert it?" "L\u1ea7n chuy\u1ec3n giao %s kh\u00f4ng \u0111\u01b0\u1ee3c bao g\u1ed3m trong nh\u00e1nh %s -- b\u1ea1n c\u00f3 th\u1ef1c s\u1ef1 mu\u1ed1n \u201crevert\u201d n\u00f3 kh\u00f4ng?"
::msgcat::mcset vi "Reverting" "\u0110ang ho\u00e0n t\u00e1c"
::msgcat::mcset vi "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "Revert g\u1eb7p l\u1ed7i b\u1edfi v\u00ec t\u1eadp tin sau \u0111\u00e3 \u0111\u01b0\u1ee3c thay \u0111\u1ed5i n\u1ed9i b\u1ed9:%s\nXin h\u00e3y ch\u1ea1y l\u1ec7nh \u201ccommit\u201d, \u201creset\u201d ho\u1eb7c \u201cstash\u201d r\u1ed3i th\u1eed l\u1ea1i."
::msgcat::mcset vi "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "Revert g\u1eb7p l\u1ed7i b\u1edfi v\u00ec xung \u0111\u1ed9t h\u00f2a tr\u1ed9n.\n B\u1ea1n c\u00f3 mu\u1ed1n ch\u1ea1y l\u1ec7nh \u201cgit citool\u201d \u0111\u1ec3 ph\u00e2n gi\u1ea3i n\u00f3 kh\u00f4ng?"
::msgcat::mcset vi "Confirm reset" "X\u00e1c nh\u1eadt \u0111\u1eb7t l\u1ea1i"
::msgcat::mcset vi "Reset branch %s to %s?" "\u0110\u1eb7t l\u1ea1i nh\u00e1nh \u201c%s\u201d th\u00e0nh \u201c%s\u201d?"
::msgcat::mcset vi "Reset type:" "Ki\u1ec3u \u0111\u1eb7t l\u1ea1i:"
::msgcat::mcset vi "Soft: Leave working tree and index untouched" "M\u1ec1m: Kh\u00f4ng \u0111\u1ed9ng \u0111\u1ebfn th\u01b0 m\u1ee5c l\u00e0m vi\u1ec7c v\u00e0 b\u1ea3ng m\u1ee5c l\u1ee5c"
::msgcat::mcset vi "Mixed: Leave working tree untouched, reset index" "Pha tr\u1ed9n: Kh\u00f4ng \u0111\u1ed9ng ch\u1ea1m \u0111\u1ebfn th\u01b0 m\u1ee5c l\u00e0m vi\u1ec7c nh\u01b0ng \u0111\u1eb7t l\u1ea1i b\u1ea3ng m\u1ee5c l\u1ee5c"
::msgcat::mcset vi "Hard: Reset working tree and index\n(discard ALL local changes)" "Hard: \u0110\u1eb7t l\u1ea1i c\u00e2y l\u00e0m vi\u1ec7c v\u00e0 m\u1ee5c l\u1ee5c\n(h\u1ee7y b\u1ecf M\u1eccI thay \u0111\u1ed5i n\u1ed9i b\u1ed9)"
::msgcat::mcset vi "Resetting" "\u0110ang \u0111\u1eb7t l\u1ea1i"
::msgcat::mcset vi "Checking out" "\u0110ang checkout"
::msgcat::mcset vi "Cannot delete the currently checked-out branch" "Kh\u00f4ng th\u1ec3 x\u00f3a nh\u00e1nh hi\u1ec7n t\u1ea1i \u0111ang \u0111\u01b0\u1ee3c l\u1ea5y ra"
::msgcat::mcset vi "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "C\u00e1c l\u1ea7n chuy\u1ec3n giao tr\u00ean nh\u00e1nh %s kh\u00f4ng \u1edf tr\u00ean nh\u00e1nh kh\u00e1c.\nTh\u1ef1c s\u1ef1 mu\u1ed1n x\u00f3a nh\u00e1nh %s?"
::msgcat::mcset vi "Tags and heads: %s" "Th\u1ebb v\u00e0 \u0110\u1ea7u: %s"
::msgcat::mcset vi "Filter" "B\u1ed9 l\u1ecdc"
::msgcat::mcset vi "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "G\u1eb7p l\u1ed7i khi \u0111\u1ecdc th\u00f4ng tin h\u00ecnh h\u1ecdc l\u1ea7n chuy\u1ec3n giao; th\u00f4ng tin nh\u00e1nh v\u00e0 th\u1ebb tr\u01b0\u1edbc/sau s\u1ebd kh\u00f4ng ho\u00e0n thi\u1ec7n."
::msgcat::mcset vi "Tag" "Th\u1ebb"
::msgcat::mcset vi "Id" "Id"
::msgcat::mcset vi "Gitk font chooser" "H\u1ed9p tho\u1ea1i ch\u1ecdn ph\u00f4ng Gitk"
::msgcat::mcset vi "B" "B"
::msgcat::mcset vi "I" "I"
::msgcat::mcset vi "Commit list display options" "C\u00e1c t\u00f9y ch\u1ecdn v\u1ec1 hi\u1ec3n th\u1ecb danh s\u00e1ch l\u1ea7n chuy\u1ec3n giao"
::msgcat::mcset vi "Maximum graph width (lines)" "\u0110\u1ed9 r\u1ed9ng bi\u1ec3u \u0111\u1ed3 t\u1ed1i \u0111a (d\u00f2ng)"
::msgcat::mcset vi "Maximum graph width (% of pane)" "\u0110\u1ed9 r\u1ed9ng \u0111\u1ed3 th\u1ecb t\u1ed1i \u0111a (% c\u1ee7a b\u1ea3ng)"
::msgcat::mcset vi "Show local changes" "Hi\u1ec3n th\u1ecb c\u00e1c thay \u0111\u1ed5i n\u1ed9i b\u1ed9"
::msgcat::mcset vi "Auto-select SHA1 (length)" "T\u1ef1 ch\u1ecdn (\u0111\u1ed9 d\u00e0i) SHA1"
::msgcat::mcset vi "Hide remote refs" "\u1ea8n tham chi\u1ebfu \u0111\u1ebfn m\u00e1y ch\u1ee7"
::msgcat::mcset vi "Diff display options" "C\u00e1c t\u00f9y ch\u1ecdn tr\u00ecnh b\u00e0y c\u00e1c kh\u00e1c bi\u1ec7t"
::msgcat::mcset vi "Tab spacing" "Kho\u1ea3ng c\u00e1ch tab"
::msgcat::mcset vi "Display nearby tags/heads" "Hi\u1ec3n th\u1ecb c\u00e1c th\u1ebb/\u0111\u1ea7u xung quanh"
::msgcat::mcset vi "Maximum # tags/heads to show" "S\u1ed1 l\u01b0\u1ee3ng th\u1ebb/\u0111\u1ea7u t\u1ed1i \u0111a s\u1ebd hi\u1ec3n th\u1ecb"
::msgcat::mcset vi "Limit diffs to listed paths" "Gi\u1edbi h\u1ea1n c\u00e1c kh\u00e1c bi\u1ec7t cho \u0111\u01b0\u1eddng d\u1eabn \u0111\u00e3 li\u1ec7t k\u00ea"
::msgcat::mcset vi "Support per-file encodings" "H\u1ed7 tr\u1ee3 m\u00e3 h\u00f3a m\u1ed7i-d\u00f2ng"
::msgcat::mcset vi "External diff tool" "C\u00f4ng c\u1ee5 so s\u00e1nh t\u1eeb b\u00ean ngo\u00e0i"
::msgcat::mcset vi "Choose..." "Ch\u1ecdn\u2026"
::msgcat::mcset vi "General options" "C\u00e1c t\u00f9y ch\u1ecdn chung"
::msgcat::mcset vi "Use themed widgets" "D\u00f9ng c\u00e1c widget ch\u1ee7 \u0111\u1ec1"
::msgcat::mcset vi "(change requires restart)" "(\u0111\u1ec3 thay \u0111\u1ed5i c\u1ea7n kh\u1edfi \u0111\u1ed9ng l\u1ea1i)"
::msgcat::mcset vi "(currently unavailable)" "(hi\u1ec7n t\u1ea1i kh\u00f4ng s\u1eb5n s\u00e0ng)"
::msgcat::mcset vi "Colors: press to choose" "M\u00e0u s\u1eafc: b\u1ea5m v\u00e0o n\u00fat ph\u00eda d\u01b0\u1edbi \u0111\u1ec3 ch\u1ecdn m\u00e0u"
::msgcat::mcset vi "Interface" "Giao di\u1ec7n"
::msgcat::mcset vi "interface" "giao di\u1ec7n"
::msgcat::mcset vi "Background" "N\u1ec1n"
::msgcat::mcset vi "background" "n\u1ec1n"
::msgcat::mcset vi "Foreground" "Ti\u1ec1n c\u1ea3nh"
::msgcat::mcset vi "foreground" "ti\u1ec1n c\u1ea3nh"
::msgcat::mcset vi "Diff: old lines" "So s\u00e1nh: d\u00f2ng c\u0169"
::msgcat::mcset vi "diff old lines" "diff d\u00f2ng c\u0169"
::msgcat::mcset vi "Diff: new lines" "So s\u00e1nh: d\u00f2ng m\u1edbi"
::msgcat::mcset vi "diff new lines" "m\u00e0u d\u00f2ng m\u1edbi"
::msgcat::mcset vi "Diff: hunk header" "So s\u00e1nh: ph\u1ea7n \u0111\u1ea7u c\u1ee7a \u0111o\u1ea1n"
::msgcat::mcset vi "diff hunk header" "m\u00e0u c\u1ee7a ph\u1ea7n \u0111\u1ea7u c\u1ee7a \u0111o\u1ea1n khi so s\u00e1nh"
::msgcat::mcset vi "Marked line bg" "N\u1ec1n d\u00f2ng \u0111\u00e1nh d\u1ea5u"
::msgcat::mcset vi "marked line background" "n\u1ec1n d\u00f2ng \u0111\u01b0\u1ee3c \u0111\u00e1nh d\u1ea5u"
::msgcat::mcset vi "Select bg" "M\u00e0u n\u1ec1n"
::msgcat::mcset vi "Fonts: press to choose" "Ph\u00f4ng ch\u1eef: b\u1ea5m v\u00e0o c\u00e1c n\u00fat \u1edf d\u01b0\u1edbi \u0111\u1ec3 ch\u1ecdn"
::msgcat::mcset vi "Main font" "Ph\u00f4ng ch\u1eef ch\u00ednh"
::msgcat::mcset vi "Diff display font" "Ph\u00f4ng ch\u1eef d\u00f9ng khi so s\u00e1nh"
::msgcat::mcset vi "User interface font" "Ph\u00f4ng ch\u1eef giao di\u1ec7n"
::msgcat::mcset vi "Gitk preferences" "C\u00e1 nh\u00e2n h\u00f3a c\u00e1c c\u00e0i \u0111\u1eb7t cho Gitk"
::msgcat::mcset vi "General" "Chung"
::msgcat::mcset vi "Colors" "M\u00e0u s\u1eafc"
::msgcat::mcset vi "Fonts" "Ph\u00f4ng ch\u1eef"
::msgcat::mcset vi "Gitk: choose color for %s" "Gitk: ch\u1ecdn m\u00e0u cho %s"
::msgcat::mcset vi "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "R\u1ea5t ti\u1ebfc, gitk kh\u00f4ng th\u1ec3 ch\u1ea1y Tcl/Tk phi\u00ean b\u1ea3n n\u00e0y.\n Gitk c\u1ea7n \u00edt nh\u1ea5t l\u00e0 Tcl/Tk 8.4."
::msgcat::mcset vi "Cannot find a git repository here." "Kh\u00f4ng th\u1ec3 t\u00ecm th\u1ea5y kho git \u1edf \u0111\u00e2y."
::msgcat::mcset vi "Ambiguous argument '%s': both revision and filename" "\u0110\u1ed1i s\u1ed1 \u201c%s\u201d ch\u01b0a r\u00f5 r\u00e0ng: v\u1eeba l\u00e0 \u0111i\u1ec3m x\u00e9t duy\u1ec7t v\u1eeba l\u00e0 t\u00ean t\u1eadp tin"
::msgcat::mcset vi "Bad arguments to gitk:" "\u0110\u1ed1i s\u1ed1 cho gitk kh\u00f4ng h\u1ee3p l\u1ec7:"
