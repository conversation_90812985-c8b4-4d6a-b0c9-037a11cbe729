# Global symbol without underscore.
set(FortranCInterface_GLO<PERSON>L_SYMBOL  "@FortranCInterface_GLOBAL_SYMBOL@")
set(FortranCInterface_GLOBAL_PREFIX  "@FortranCInterface_GLOBAL_PREFIX@")
set(FortranCInterface_GLOBAL_SUFFIX  "@FortranCInterface_GLOBAL_SUFFIX@")
set(FortranCInterface_GLOBAL_CASE    "@FortranCInterface_GLOBAL_CASE@")
set(FortranCInterface_GLOBAL_MACRO   "@FortranCInterface_GLOBAL_MACRO@")

# Global symbol with underscore.
set(FortranCInterface_GLOBAL__SYMBOL "@FortranCInterface_GLOBAL__SYMBOL@")
set(FortranCInterface_GLOBAL__PREFIX "@FortranCInterface_GLOBAL__PREFIX@")
set(FortranCInterface_GLOBAL__SUFFIX "@FortranCInterface_GLOBAL__SUFFIX@")
set(FortranCInterface_GLOBAL__CASE   "@FortranCInterface_GLOBAL__CASE@")
set(FortranCInterface_GLOBAL__MACRO  "@FortranCInterface_GLOBAL__MACRO@")

# Module symbol without underscore.
set(FortranCInterface_MODULE_SYMBOL  "@FortranCInterface_MODULE_SYMBOL@")
set(FortranCInterface_MODULE_PREFIX  "@FortranCInterface_MODULE_PREFIX@")
set(FortranCInterface_MODULE_MIDDLE  "@FortranCInterface_MODULE_MIDDLE@")
set(FortranCInterface_MODULE_SUFFIX  "@FortranCInterface_MODULE_SUFFIX@")
set(FortranCInterface_MODULE_CASE    "@FortranCInterface_MODULE_CASE@")
set(FortranCInterface_MODULE_MACRO   "@FortranCInterface_MODULE_MACRO@")

# Module symbol with underscore.
set(FortranCInterface_MODULE__SYMBOL "@FortranCInterface_MODULE__SYMBOL@")
set(FortranCInterface_MODULE__PREFIX "@FortranCInterface_MODULE__PREFIX@")
set(FortranCInterface_MODULE__MIDDLE "@FortranCInterface_MODULE__MIDDLE@")
set(FortranCInterface_MODULE__SUFFIX "@FortranCInterface_MODULE__SUFFIX@")
set(FortranCInterface_MODULE__CASE   "@FortranCInterface_MODULE__CASE@")
set(FortranCInterface_MODULE__MACRO  "@FortranCInterface_MODULE__MACRO@")

# Summarize what was found.
set(FortranCInterface_GLOBAL_FOUND @FortranCInterface_GLOBAL_FOUND@)
set(FortranCInterface_MODULE_FOUND @FortranCInterface_MODULE_FOUND@)
