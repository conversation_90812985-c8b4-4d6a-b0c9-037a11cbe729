.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_name_constraints" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_name_constraints \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_name_constraints(gnutls_x509_crt_t " crt ", gnutls_x509_name_constraints_t " nc ", unsigned int " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
The certificate
.IP "gnutls_x509_name_constraints_t nc" 12
The nameconstraints structure
.IP "unsigned int critical" 12
whether this extension will be critical
.SH "DESCRIPTION"
This function will set the provided name constraints to
the certificate extension list. This extension is always
marked as critical.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
