<?xml version="1.0"?>
<!DOCTYPE syscalls_info SYSTEM "gdb-syscalls.dtd">
<!-- Copyright (C) 2009-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->
<!-- This file was generated using the following file:

     <sys/syscall.h>

     The file mentioned above belongs to the Linux Kernel.  -->
<syscalls_info>
  <syscall name="io_setup" number="0" groups="memory"/>
  <syscall name="io_destroy" number="1" groups="memory"/>
  <syscall name="io_submit" number="2"/>
  <syscall name="io_cancel" number="3"/>
  <syscall name="io_getevents" number="4"/>
  <syscall name="setxattr" number="5" groups="file"/>
  <syscall name="lsetxattr" number="6" groups="file"/>
  <syscall name="fsetxattr" number="7" groups="descriptor"/>
  <syscall name="getxattr" number="8" groups="file"/>
  <syscall name="lgetxattr" number="9" groups="file"/>
  <syscall name="fgetxattr" number="10" groups="descriptor"/>
  <syscall name="listxattr" number="11" groups="file"/>
  <syscall name="llistxattr" number="12" groups="file"/>
  <syscall name="flistxattr" number="13" groups="descriptor"/>
  <syscall name="removexattr" number="14" groups="file"/>
  <syscall name="lremovexattr" number="15" groups="file"/>
  <syscall name="fremovexattr" number="16" groups="descriptor"/>
  <syscall name="getcwd" number="17" groups="file"/>
  <syscall name="lookup_dcookie" number="18"/>
  <syscall name="eventfd2" number="19" groups="descriptor"/>
  <syscall name="epoll_create1" number="20" groups="descriptor"/>
  <syscall name="epoll_ctl" number="21" groups="descriptor"/>
  <syscall name="epoll_pwait" number="22" groups="descriptor"/>
  <syscall name="dup" number="23" groups="descriptor"/>
  <syscall name="dup3" number="24" groups="descriptor"/>
  <syscall name="fcntl" number="25" groups="descriptor"/>
  <syscall name="inotify_init1" number="26" groups="descriptor"/>
  <syscall name="inotify_add_watch" number="27" groups="descriptor,file"/>
  <syscall name="inotify_rm_watch" number="28" groups="descriptor"/>
  <syscall name="ioctl" number="29" groups="descriptor"/>
  <syscall name="ioprio_set" number="30"/>
  <syscall name="ioprio_get" number="31"/>
  <syscall name="flock" number="32" groups="descriptor"/>
  <syscall name="mknodat" number="33" groups="descriptor,file"/>
  <syscall name="mkdirat" number="34" groups="descriptor,file"/>
  <syscall name="unlinkat" number="35" groups="descriptor,file"/>
  <syscall name="symlinkat" number="36" groups="descriptor,file"/>
  <syscall name="linkat" number="37" groups="descriptor,file"/>
  <syscall name="umount2" number="39" groups="file"/>
  <syscall name="mount" number="40" groups="file"/>
  <syscall name="pivot_root" number="41" groups="file"/>
  <syscall name="nfsservctl" number="42"/>
  <syscall name="statfs" number="43" groups="file"/>
  <syscall name="fstatfs" number="44" groups="descriptor"/>
  <syscall name="truncate" number="45" groups="file"/>
  <syscall name="ftruncate" number="46" groups="descriptor"/>
  <syscall name="fallocate" number="47" groups="descriptor"/>
  <syscall name="faccessat" number="48" groups="descriptor,file"/>
  <syscall name="chdir" number="49" groups="file"/>
  <syscall name="fchdir" number="50" groups="descriptor"/>
  <syscall name="chroot" number="51" groups="file"/>
  <syscall name="fchmod" number="52" groups="descriptor"/>
  <syscall name="fchmodat" number="53" groups="descriptor,file"/>
  <syscall name="fchownat" number="54" groups="descriptor,file"/>
  <syscall name="fchown" number="55" groups="descriptor"/>
  <syscall name="openat" number="56" groups="descriptor,file"/>
  <syscall name="close" number="57" groups="descriptor"/>
  <syscall name="vhangup" number="58"/>
  <syscall name="pipe2" number="59" groups="descriptor"/>
  <syscall name="quotactl" number="60" groups="file"/>
  <syscall name="getdents64" number="61" groups="descriptor"/>
  <syscall name="lseek" number="62" groups="descriptor"/>
  <syscall name="read" number="63" groups="descriptor"/>
  <syscall name="write" number="64" groups="descriptor"/>
  <syscall name="readv" number="65" groups="descriptor"/>
  <syscall name="writev" number="66" groups="descriptor"/>
  <syscall name="pread64" number="67" groups="descriptor"/>
  <syscall name="pwrite64" number="68" groups="descriptor"/>
  <syscall name="preadv" number="69" groups="descriptor"/>
  <syscall name="pwritev" number="70" groups="descriptor"/>
  <syscall name="sendfile" number="71" groups="descriptor,network"/>
  <syscall name="pselect6" number="72" groups="descriptor"/>
  <syscall name="ppoll" number="73" groups="descriptor"/>
  <syscall name="signalfd4" number="74" groups="descriptor,signal"/>
  <syscall name="vmsplice" number="75" groups="descriptor"/>
  <syscall name="splice" number="76" groups="descriptor"/>
  <syscall name="tee" number="77" groups="descriptor"/>
  <syscall name="readlinkat" number="78" groups="descriptor,file"/>
  <syscall name="fstatat" number="79"/>
  <syscall name="fstat" number="80" groups="descriptor"/>
  <syscall name="sync" number="81"/>
  <syscall name="fsync" number="82" groups="descriptor"/>
  <syscall name="fdatasync" number="83" groups="descriptor"/>
  <syscall name="sync_file_range" number="84" groups="descriptor"/>
  <syscall name="timerfd_create" number="85" groups="descriptor"/>
  <syscall name="timerfd_settime" number="86" groups="descriptor"/>
  <syscall name="timerfd_gettime" number="87" groups="descriptor"/>
  <syscall name="utimensat" number="88" groups="descriptor,file"/>
  <syscall name="acct" number="89" groups="file"/>
  <syscall name="capget" number="90"/>
  <syscall name="capset" number="91"/>
  <syscall name="personality" number="92"/>
  <syscall name="exit" number="93" groups="process"/>
  <syscall name="exit_group" number="94" groups="process"/>
  <syscall name="waitid" number="95" groups="process"/>
  <syscall name="set_tid_address" number="96"/>
  <syscall name="unshare" number="97"/>
  <syscall name="futex" number="98"/>
  <syscall name="set_robust_list" number="99"/>
  <syscall name="get_robust_list" number="100"/>
  <syscall name="nanosleep" number="101"/>
  <syscall name="getitimer" number="102"/>
  <syscall name="setitimer" number="103"/>
  <syscall name="kexec_load" number="104"/>
  <syscall name="init_module" number="105"/>
  <syscall name="delete_module" number="106"/>
  <syscall name="timer_create" number="107"/>
  <syscall name="timer_gettime" number="108"/>
  <syscall name="timer_getoverrun" number="109"/>
  <syscall name="timer_settime" number="110"/>
  <syscall name="timer_delete" number="111"/>
  <syscall name="clock_settime" number="112"/>
  <syscall name="clock_gettime" number="113"/>
  <syscall name="clock_getres" number="114"/>
  <syscall name="clock_nanosleep" number="115"/>
  <syscall name="syslog" number="116"/>
  <syscall name="ptrace" number="117"/>
  <syscall name="sched_setparam" number="118"/>
  <syscall name="sched_setscheduler" number="119"/>
  <syscall name="sched_getscheduler" number="120"/>
  <syscall name="sched_getparam" number="121"/>
  <syscall name="sched_setaffinity" number="122"/>
  <syscall name="sched_getaffinity" number="123"/>
  <syscall name="sched_yield" number="124"/>
  <syscall name="sched_get_priority_max" number="125"/>
  <syscall name="sched_get_priority_min" number="126"/>
  <syscall name="sched_rr_get_interval" number="127"/>
  <syscall name="restart_syscall" number="128"/>
  <syscall name="kill" number="129" groups="signal,process"/>
  <syscall name="tkill" number="130" groups="signal,process"/>
  <syscall name="tgkill" number="131" groups="signal,process"/>
  <syscall name="sigaltstack" number="132" groups="signal"/>
  <syscall name="rt_sigsuspend" number="133" groups="signal"/>
  <syscall name="rt_sigaction" number="134" groups="signal"/>
  <syscall name="rt_sigprocmask" number="135" groups="signal"/>
  <syscall name="rt_sigpending" number="136" groups="signal"/>
  <syscall name="rt_sigtimedwait" number="137" groups="signal"/>
  <syscall name="rt_sigqueueinfo" number="138" groups="signal,process"/>
  <syscall name="rt_sigreturn" number="139" groups="signal"/>
  <syscall name="setpriority" number="140"/>
  <syscall name="getpriority" number="141"/>
  <syscall name="reboot" number="142"/>
  <syscall name="setregid" number="143"/>
  <syscall name="setgid" number="144"/>
  <syscall name="setreuid" number="145"/>
  <syscall name="setuid" number="146"/>
  <syscall name="setresuid" number="147"/>
  <syscall name="getresuid" number="148"/>
  <syscall name="setresgid" number="149"/>
  <syscall name="getresgid" number="150"/>
  <syscall name="setfsuid" number="151"/>
  <syscall name="setfsgid" number="152"/>
  <syscall name="times" number="153"/>
  <syscall name="setpgid" number="154"/>
  <syscall name="getpgid" number="155"/>
  <syscall name="getsid" number="156"/>
  <syscall name="setsid" number="157"/>
  <syscall name="getgroups" number="158"/>
  <syscall name="setgroups" number="159"/>
  <syscall name="uname" number="160"/>
  <syscall name="sethostname" number="161"/>
  <syscall name="setdomainname" number="162"/>
  <syscall name="getrusage" number="165"/>
  <syscall name="umask" number="166"/>
  <syscall name="prctl" number="167"/>
  <syscall name="getcpu" number="168"/>
  <syscall name="gettimeofday" number="169"/>
  <syscall name="settimeofday" number="170"/>
  <syscall name="adjtimex" number="171"/>
  <syscall name="getpid" number="172"/>
  <syscall name="getppid" number="173"/>
  <syscall name="getuid" number="174"/>
  <syscall name="geteuid" number="175"/>
  <syscall name="getgid" number="176"/>
  <syscall name="getegid" number="177"/>
  <syscall name="gettid" number="178"/>
  <syscall name="sysinfo" number="179"/>
  <syscall name="mq_open" number="180" groups="descriptor"/>
  <syscall name="mq_unlink" number="181"/>
  <syscall name="mq_timedsend" number="182" groups="descriptor"/>
  <syscall name="mq_timedreceive" number="183" groups="descriptor"/>
  <syscall name="mq_notify" number="184" groups="descriptor"/>
  <syscall name="mq_getsetattr" number="185" groups="descriptor"/>
  <syscall name="msgget" number="186" groups="ipc"/>
  <syscall name="msgctl" number="187" groups="ipc"/>
  <syscall name="msgrcv" number="188" groups="ipc"/>
  <syscall name="msgsnd" number="189" groups="ipc"/>
  <syscall name="semget" number="190" groups="ipc"/>
  <syscall name="semctl" number="191" groups="ipc"/>
  <syscall name="semtimedop" number="192" groups="ipc"/>
  <syscall name="semop" number="193" groups="ipc"/>
  <syscall name="shmget" number="194" groups="ipc"/>
  <syscall name="shmctl" number="195" groups="ipc"/>
  <syscall name="shmat" number="196" groups="ipc,memory"/>
  <syscall name="shmdt" number="197" groups="ipc,memory"/>
  <syscall name="socket" number="198" groups="network"/>
  <syscall name="socketpair" number="199" groups="network"/>
  <syscall name="bind" number="200" groups="network"/>
  <syscall name="listen" number="201" groups="network"/>
  <syscall name="accept" number="202" groups="network"/>
  <syscall name="connect" number="203" groups="network"/>
  <syscall name="getsockname" number="204" groups="network"/>
  <syscall name="getpeername" number="205" groups="network"/>
  <syscall name="sendto" number="206" groups="network"/>
  <syscall name="recvfrom" number="207" groups="network"/>
  <syscall name="setsockopt" number="208" groups="network"/>
  <syscall name="getsockopt" number="209" groups="network"/>
  <syscall name="shutdown" number="210" groups="network"/>
  <syscall name="sendmsg" number="211" groups="network"/>
  <syscall name="recvmsg" number="212" groups="network"/>
  <syscall name="readahead" number="213" groups="descriptor"/>
  <syscall name="brk" number="214" groups="memory"/>
  <syscall name="munmap" number="215" groups="memory"/>
  <syscall name="mremap" number="216" groups="memory"/>
  <syscall name="add_key" number="217"/>
  <syscall name="request_key" number="218"/>
  <syscall name="keyctl" number="219"/>
  <syscall name="clone" number="220" groups="process"/>
  <syscall name="execve" number="221" groups="file,process"/>
  <syscall name="mmap" number="222" groups="descriptor,memory"/>
  <syscall name="fadvise64" number="223" groups="descriptor"/>
  <syscall name="swapon" number="224" groups="file"/>
  <syscall name="swapoff" number="225" groups="file"/>
  <syscall name="mprotect" number="226" groups="memory"/>
  <syscall name="msync" number="227" groups="memory"/>
  <syscall name="mlock" number="228" groups="memory"/>
  <syscall name="munlock" number="229" groups="memory"/>
  <syscall name="mlockall" number="230" groups="memory"/>
  <syscall name="munlockall" number="231" groups="memory"/>
  <syscall name="mincore" number="232" groups="memory"/>
  <syscall name="madvise" number="233" groups="memory"/>
  <syscall name="remap_file_pages" number="234" groups="memory"/>
  <syscall name="mbind" number="235" groups="memory"/>
  <syscall name="get_mempolicy" number="236" groups="memory"/>
  <syscall name="set_mempolicy" number="237" groups="memory"/>
  <syscall name="migrate_pages" number="238" groups="memory"/>
  <syscall name="move_pages" number="239" groups="memory"/>
  <syscall name="rt_tgsigqueueinfo" number="240" groups="process,signal"/>
  <syscall name="perf_event_open" number="241" groups="descriptor"/>
  <syscall name="accept4" number="242" groups="network"/>
  <syscall name="recvmmsg" number="243" groups="network"/>
  <syscall name="arch_specific_syscall" number="244"/>
  <syscall name="wait4" number="260" groups="process"/>
  <syscall name="prlimit64" number="261"/>
  <syscall name="fanotify_init" number="262" groups="descriptor"/>
  <syscall name="fanotify_mark" number="263" groups="descriptor,file"/>
  <syscall name="name_to_handle_at" number="264" groups="descriptor,file"/>
  <syscall name="open_by_handle_at" number="265" groups="descriptor"/>
  <syscall name="clock_adjtime" number="266"/>
  <syscall name="syncfs" number="267" groups="descriptor"/>
  <syscall name="setns" number="268" groups="descriptor"/>
  <syscall name="sendmmsg" number="269" groups="network"/>
  <syscall name="process_vm_readv" number="270"/>
  <syscall name="process_vm_writev" number="271"/>
  <syscall name="kcmp" number="272"/>
  <syscall name="finit_module" number="273" groups="descriptor"/>
  <syscall name="sched_setattr" number="274"/>
  <syscall name="sched_getattr" number="275"/>
  <syscall name="renameat2" number="276" groups="descriptor,file"/>
  <syscall name="seccomp" number="277"/>
  <syscall name="getrandom" number="278"/>
  <syscall name="memfd_create" number="279" groups="descriptor"/>
  <syscall name="bpf" number="280" groups="descriptor"/>
  <syscall name="execveat" number="281" groups="descriptor,file,process"/>
  <syscall name="userfaultfd" number="282" groups="descriptor"/>
  <syscall name="membarrier" number="283"/>
  <syscall name="mlock2" number="284" groups="memory"/>
  <syscall name="copy_file_range" number="285" groups="descriptor"/>
  <syscall name="preadv2" number="286" groups="descriptor"/>
  <syscall name="pwritev2" number="287" groups="descriptor"/>
  <syscall name="pkey_mprotect" number="288" groups="memory"/>
  <syscall name="pkey_alloc" number="289"/>
  <syscall name="pkey_free" number="290"/>
  <syscall name="statx" number="291" groups="descriptor,file"/>
  <syscall name="io_pgetevents" number="292"/>
  <syscall name="rseq" number="293"/>
  <syscall name="kexec_file_load" number="294" groups="descriptor"/>
  <syscall name="pidfd_send_signal" number="424" groups="descriptor,signal,process"/>
  <syscall name="io_uring_setup" number="425" groups="descriptor"/>
  <syscall name="io_uring_enter" number="426" groups="descriptor,signal"/>
  <syscall name="io_uring_register" number="427" groups="descriptor,memory"/>
  <syscall name="open_tree" number="428" groups="descriptor,file"/>
  <syscall name="move_mount" number="429" groups="descriptor,file"/>
  <syscall name="fsopen" number="430" groups="descriptor"/>
  <syscall name="fsconfig" number="431" groups="descriptor,file"/>
  <syscall name="fsmount" number="432" groups="descriptor"/>
  <syscall name="fspick" number="433" groups="descriptor,file"/>
  <syscall name="pidfd_open" number="434" groups="descriptor"/>
  <syscall name="clone3" number="435" groups="process"/>
  <syscall name="close_range" number="436"/>
  <syscall name="openat2" number="437" groups="descriptor,file"/>
  <syscall name="pidfd_getfd" number="438" groups="descriptor"/>
  <syscall name="faccessat2" number="439" groups="descriptor,file"/>
  <syscall name="process_madvise" number="440" groups="descriptor"/>
  <syscall name="epoll_pwait2" number="441" groups="descriptor"/>
  <syscall name="mount_setattr" number="442" groups="descriptor,file"/>
  <syscall name="quotactl_fd" number="443" groups="descriptor"/>
  <syscall name="landlock_create_ruleset" number="444" groups="descriptor"/>
  <syscall name="landlock_add_rule" number="445" groups="descriptor"/>
  <syscall name="landlock_restrict_self" number="446" groups="descriptor"/>
  <syscall name="process_mrelease" number="448" groups="descriptor"/>
  <syscall name="futex_waitv" number="449"/>
  <syscall name="set_mempolicy_home_node" number="450" groups="memory"/>
  <syscall name="cachestat" number="451" groups="descriptor"/>
  <syscall name="fchmodat2" number="452" groups="descriptor,file"/>
  <syscall name="map_shadow_stack" number="453" groups="memory"/>
  <syscall name="futex_wake" number="454"/>
  <syscall name="futex_wait" number="455"/>
  <syscall name="futex_requeue" number="456"/>
  <syscall name="statmount" number="457"/>
  <syscall name="listmount" number="458"/>
  <syscall name="lsm_get_self_attr" number="459"/>
  <syscall name="lsm_set_self_attr" number="460"/>
  <syscall name="lsm_list_modules" number="461"/>
  <syscall name="mseal" number="462" groups="memory"/>
  <syscall name="setxattrat" number="463"/>
  <syscall name="getxattrat" number="464"/>
  <syscall name="listxattrat" number="465"/>
  <syscall name="removexattrat" number="466"/>
  <syscall name="syscalls" number="467"/>
</syscalls_info>
