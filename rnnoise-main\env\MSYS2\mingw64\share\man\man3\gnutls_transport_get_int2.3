.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_transport_get_int2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_transport_get_int2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_transport_get_int2(gnutls_session_t " session ", int * " recv_int ", int * " send_int ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "int * recv_int" 12
will hold the value for the pull function
.IP "int * send_int" 12
will hold the value for the push function
.SH "DESCRIPTION"
Used to get the arguments of the transport functions (like PUSH
and PULL).  These should have been set using
\fBgnutls_transport_set_int2()\fP.
.SH "SINCE"
3.1.9
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
