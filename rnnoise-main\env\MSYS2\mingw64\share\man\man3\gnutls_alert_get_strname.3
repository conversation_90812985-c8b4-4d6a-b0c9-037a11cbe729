.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_alert_get_strname" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_alert_get_strname \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_alert_get_strname(gnutls_alert_description_t " alert ");"
.SH ARGUMENTS
.IP "gnutls_alert_description_t alert" 12
is an alert number.
.SH "DESCRIPTION"
This function will return a string of the name of the alert.
.SH "RETURNS"
string corresponding to \fBgnutls_alert_description_t\fP value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
