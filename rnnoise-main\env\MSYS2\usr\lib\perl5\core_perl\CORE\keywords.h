/* -*- buffer-read-only: t -*-
 *
 *    keywords.h
 *
 *    Copyright (C) 1994, 1995, 1996, 1997, 1999, 2000, 2001, 2002, 2005,
 *    2006, 2007 by <PERSON> and others
 *
 *    You may distribute under the terms of either the GNU General Public
 *    License or the Artistic License, as specified in the README file.
 *
 * !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
 * This file is built by regen/keywords.pl from its data.
 * Any changes made here will be lost!
 */

#define KEY_NULL		0
#define KEY___FILE__		1
#define KEY___LINE__		2
#define KEY___PACKAGE__		3
#define KEY___DATA__		4
#define KEY___END__		5
#define KEY___SUB__		6
#define KEY_ADJUST		7
#define KEY_AUTOLOAD		8
#define KEY_BEGIN		9
#define KEY_UNITCHECK		10
#define KEY_DESTROY		11
#define KEY_END			12
#define KEY_INIT		13
#define KEY_CHECK		14
#define KEY_abs			15
#define KEY_accept		16
#define KEY_alarm		17
#define KEY_and			18
#define KEY_atan2		19
#define KEY_bind		20
#define KEY_binmode		21
#define KEY_bless		22
#define KEY_break		23
#define KEY_caller		24
#define KEY_catch		25
#define KEY_chdir		26
#define KEY_chmod		27
#define KEY_chomp		28
#define KEY_chop		29
#define KEY_chown		30
#define KEY_chr			31
#define KEY_chroot		32
#define KEY_class		33
#define KEY_close		34
#define KEY_closedir		35
#define KEY_cmp			36
#define KEY_connect		37
#define KEY_continue		38
#define KEY_cos			39
#define KEY_crypt		40
#define KEY_dbmclose		41
#define KEY_dbmopen		42
#define KEY_default		43
#define KEY_defer		44
#define KEY_defined		45
#define KEY_delete		46
#define KEY_die			47
#define KEY_do			48
#define KEY_dump		49
#define KEY_each		50
#define KEY_else		51
#define KEY_elsif		52
#define KEY_endgrent		53
#define KEY_endhostent		54
#define KEY_endnetent		55
#define KEY_endprotoent		56
#define KEY_endpwent		57
#define KEY_endservent		58
#define KEY_eof			59
#define KEY_eq			60
#define KEY_eval		61
#define KEY_evalbytes		62
#define KEY_exec		63
#define KEY_exists		64
#define KEY_exit		65
#define KEY_exp			66
#define KEY_fc			67
#define KEY_fcntl		68
#define KEY_field		69
#define KEY_fileno		70
#define KEY_finally		71
#define KEY_flock		72
#define KEY_for			73
#define KEY_foreach		74
#define KEY_fork		75
#define KEY_format		76
#define KEY_formline		77
#define KEY_ge			78
#define KEY_getc		79
#define KEY_getgrent		80
#define KEY_getgrgid		81
#define KEY_getgrnam		82
#define KEY_gethostbyaddr	83
#define KEY_gethostbyname	84
#define KEY_gethostent		85
#define KEY_getlogin		86
#define KEY_getnetbyaddr	87
#define KEY_getnetbyname	88
#define KEY_getnetent		89
#define KEY_getpeername		90
#define KEY_getpgrp		91
#define KEY_getppid		92
#define KEY_getpriority		93
#define KEY_getprotobyname	94
#define KEY_getprotobynumber	95
#define KEY_getprotoent		96
#define KEY_getpwent		97
#define KEY_getpwnam		98
#define KEY_getpwuid		99
#define KEY_getservbyname	100
#define KEY_getservbyport	101
#define KEY_getservent		102
#define KEY_getsockname		103
#define KEY_getsockopt		104
#define KEY_given		105
#define KEY_glob		106
#define KEY_gmtime		107
#define KEY_goto		108
#define KEY_grep		109
#define KEY_gt			110
#define KEY_hex			111
#define KEY_if			112
#define KEY_index		113
#define KEY_int			114
#define KEY_ioctl		115
#define KEY_isa			116
#define KEY_join		117
#define KEY_keys		118
#define KEY_kill		119
#define KEY_last		120
#define KEY_lc			121
#define KEY_lcfirst		122
#define KEY_le			123
#define KEY_length		124
#define KEY_link		125
#define KEY_listen		126
#define KEY_local		127
#define KEY_localtime		128
#define KEY_lock		129
#define KEY_log			130
#define KEY_lstat		131
#define KEY_lt			132
#define KEY_m			133
#define KEY_map			134
#define KEY_method		135
#define KEY_mkdir		136
#define KEY_msgctl		137
#define KEY_msgget		138
#define KEY_msgrcv		139
#define KEY_msgsnd		140
#define KEY_my			141
#define KEY_ne			142
#define KEY_next		143
#define KEY_no			144
#define KEY_not			145
#define KEY_oct			146
#define KEY_open		147
#define KEY_opendir		148
#define KEY_or			149
#define KEY_ord			150
#define KEY_our			151
#define KEY_pack		152
#define KEY_package		153
#define KEY_pipe		154
#define KEY_pop			155
#define KEY_pos			156
#define KEY_print		157
#define KEY_printf		158
#define KEY_prototype		159
#define KEY_push		160
#define KEY_q			161
#define KEY_qq			162
#define KEY_qr			163
#define KEY_quotemeta		164
#define KEY_qw			165
#define KEY_qx			166
#define KEY_rand		167
#define KEY_read		168
#define KEY_readdir		169
#define KEY_readline		170
#define KEY_readlink		171
#define KEY_readpipe		172
#define KEY_recv		173
#define KEY_redo		174
#define KEY_ref			175
#define KEY_rename		176
#define KEY_require		177
#define KEY_reset		178
#define KEY_return		179
#define KEY_reverse		180
#define KEY_rewinddir		181
#define KEY_rindex		182
#define KEY_rmdir		183
#define KEY_s			184
#define KEY_say			185
#define KEY_scalar		186
#define KEY_seek		187
#define KEY_seekdir		188
#define KEY_select		189
#define KEY_semctl		190
#define KEY_semget		191
#define KEY_semop		192
#define KEY_send		193
#define KEY_setgrent		194
#define KEY_sethostent		195
#define KEY_setnetent		196
#define KEY_setpgrp		197
#define KEY_setpriority		198
#define KEY_setprotoent		199
#define KEY_setpwent		200
#define KEY_setservent		201
#define KEY_setsockopt		202
#define KEY_shift		203
#define KEY_shmctl		204
#define KEY_shmget		205
#define KEY_shmread		206
#define KEY_shmwrite		207
#define KEY_shutdown		208
#define KEY_sin			209
#define KEY_sleep		210
#define KEY_socket		211
#define KEY_socketpair		212
#define KEY_sort		213
#define KEY_splice		214
#define KEY_split		215
#define KEY_sprintf		216
#define KEY_sqrt		217
#define KEY_srand		218
#define KEY_stat		219
#define KEY_state		220
#define KEY_study		221
#define KEY_sub			222
#define KEY_substr		223
#define KEY_symlink		224
#define KEY_syscall		225
#define KEY_sysopen		226
#define KEY_sysread		227
#define KEY_sysseek		228
#define KEY_system		229
#define KEY_syswrite		230
#define KEY_tell		231
#define KEY_telldir		232
#define KEY_tie			233
#define KEY_tied		234
#define KEY_time		235
#define KEY_times		236
#define KEY_tr			237
#define KEY_try			238
#define KEY_truncate		239
#define KEY_uc			240
#define KEY_ucfirst		241
#define KEY_umask		242
#define KEY_undef		243
#define KEY_unless		244
#define KEY_unlink		245
#define KEY_unpack		246
#define KEY_unshift		247
#define KEY_untie		248
#define KEY_until		249
#define KEY_use			250
#define KEY_utime		251
#define KEY_values		252
#define KEY_vec			253
#define KEY_wait		254
#define KEY_waitpid		255
#define KEY_wantarray		256
#define KEY_warn		257
#define KEY_when		258
#define KEY_while		259
#define KEY_write		260
#define KEY_x			261
#define KEY_xor			262
#define KEY_y			263

/* Generated from:
 * eb67e851da14ede1aad67aec4a387fa250c1345407fad0a02988d2d8d3cc27f2 regen/keywords.pl
 * ex: set ro: */
