.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_aki_set_cert_issuer" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_aki_set_cert_issuer \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_aki_set_cert_issuer(gnutls_x509_aki_t " aki ", unsigned int " san_type ", const gnutls_datum_t * " san ", const char * " othername_oid ", const gnutls_datum_t * " serial ");"
.SH ARGUMENTS
.IP "gnutls_x509_aki_t aki" 12
The authority key ID
.IP "unsigned int san_type" 12
the type of the name (of \fBgnutls_subject_alt_names_t\fP), may be null
.IP "const gnutls_datum_t * san" 12
The alternative name data
.IP "const char * othername_oid" 12
The object identifier if  \fIsan_type\fP is \fBGNUTLS_SAN_OTHERNAME\fP
.IP "const gnutls_datum_t * serial" 12
The authorityCertSerialNumber number (may be null)
.SH "DESCRIPTION"
This function will set the authorityCertIssuer name and the authorityCertSerialNumber 
to be stored in the  \fIaki\fP type. When storing multiple names, the serial
should be set on the first call, and subsequent calls should use a \fBNULL\fP serial.

Since version 3.5.7 the \fBGNUTLS_SAN_RFC822NAME\fP, \fBGNUTLS_SAN_DNSNAME\fP, and
\fBGNUTLS_SAN_OTHERNAME_XMPP\fP are converted to ACE format when necessary.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
