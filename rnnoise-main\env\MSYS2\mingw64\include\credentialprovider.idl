/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

cpp_quote("#include <winapifamily.h>")

cpp_quote("#if WIN<PERSON>I_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

import "wtypes.idl";
import "shobjidl.idl";
import "propsys.idl";
import "unknwn.idl";

[v1_enum] typedef enum _CREDENTIAL_PROVIDER_USAGE_SCENARIO {
  CPUS_INVALID = 0,
  CPUS_LOGON,
  CPUS_UNLOCK_WORKSTATION,
  CPUS_CHANGE_PASSWORD,
  CPUS_CREDUI,
  CPUS_PLAP,
} CREDENTIAL_PROVIDER_USAGE_SCENARIO;

[v1_enum] typedef enum _CREDENTIAL_PROVIDER_FIELD_TYPE {
  CPFT_INVALID = 0,
  CPFT_LARGE_TEXT,
  CPFT_SMALL_TEXT,
  CPFT_COMMAND_LINK,
  CPFT_EDIT_TEXT,
  CPFT_PASSWORD_TEXT,
  CPFT_TILE_IMAGE,
  CPFT_CHECKBOX,
  CPFT_COMBOBOX,
  CPFT_SUBMIT_BUTTON,
} CREDENTIAL_PROVIDER_FIELD_TYPE;

[v1_enum] typedef enum _CREDENTIAL_PROVIDER_FIELD_STATE {
  CPFS_HIDDEN = 0,
  CPFS_DISPLAY_IN_SELECTED_TILE,
  CPFS_DISPLAY_IN_DESELECTED_TILE,
  CPFS_DISPLAY_IN_BOTH,
} CREDENTIAL_PROVIDER_FIELD_STATE;

[v1_enum] typedef enum _CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE {
  CPFIS_NONE = 0,
  CPFIS_READONLY,
  CPFIS_DISABLED,
  CPFIS_FOCUSED,
} CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE;

typedef struct _CREDENTIAL_PROVIDER_FIELD_DESCRIPTOR {
  DWORD dwFieldID;
  CREDENTIAL_PROVIDER_FIELD_TYPE cpft;
  LPWSTR pszLabel;
  GUID guidFieldType;
} CREDENTIAL_PROVIDER_FIELD_DESCRIPTOR;

[v1_enum] typedef enum _CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE {
  CPGSR_NO_CREDENTIAL_NOT_FINISHED,
  CPGSR_NO_CREDENTIAL_FINISHED,
  CPGSR_RETURN_CREDENTIAL_FINISHED,
  CPGSR_RETURN_NO_CREDENTIAL_FINISHED,
} CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE;

[v1_enum] typedef enum _CREDENTIAL_PROVIDER_STATUS_ICON {
  CPSI_NONE = 0,
  CPSI_ERROR,
  CPSI_WARNING,
  CPSI_SUCCESS,
} CREDENTIAL_PROVIDER_STATUS_ICON;

typedef struct _CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION {
  ULONG ulAuthenticationPackage;
  GUID clsidCredentialProvider;
  ULONG cbSerialization;
  [size_is(cbSerialization)] byte* rgbSerialization;
} CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION;

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
[v1_enum] typedef enum CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS {
  CPAO_NONE = 0x00000000,
  CPAO_EMPTY_LOCAL = 0x00000001,
  CPAO_EMPTY_CONNECTED = 0x00000002,
} CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS)")

[v1_enum] typedef enum CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS {
  CPCFO_NONE = 0x00000000,
  CPCFO_ENABLE_PASSWORD_REVEAL = 0x00000001,
  CPCFO_IS_EMAIL_ADDRESS = 0x00000002,
  CPCFO_ENABLE_TOUCH_KEYBOARD_AUTO_INVOKE = 0x00000004,
  CPCFO_NUMBERS_ONLY = 0x00000008,
  CPCFO_SHOW_ENGLISH_KEYBOARD = 0x00000010,
} CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS)")
cpp_quote("#endif /* (NTDDI_VERSION >= NTDDI_WIN8) */")

cpp_quote("#ifdef __WIDL__")
typedef LONG NTSTATUS;
cpp_quote("#else")
cpp_quote("#ifndef NTSTATUS")
cpp_quote("typedef LONG NTSTATUS;")
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("#define CREDENTIAL_PROVIDER_NO_DEFAULT ((DWORD)-1)")

interface ICredentialProviderCredentialEvents;

[
  local,
  object,
  pointer_default(ref),
  uuid(63913a93-40c1-481a-818d-4072ff8c70cc),
]
interface ICredentialProviderCredential : IUnknown
{
  HRESULT Advise([in] ICredentialProviderCredentialEvents* pcpce);
  HRESULT UnAdvise();

  HRESULT SetSelected([out] WINBOOL* pbAutoLogon);
  HRESULT SetDeselected();

  HRESULT GetFieldState([in] DWORD dwFieldID,
              [out] CREDENTIAL_PROVIDER_FIELD_STATE* pcpfs,
              [out] CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE* pcpfis);

  HRESULT GetStringValue([in] DWORD dwFieldID, [out, string] LPWSTR* ppsz);
  HRESULT GetBitmapValue([in] DWORD dwFieldID, [out] HBITMAP* phbmp);
  HRESULT GetCheckboxValue([in] DWORD dwFieldID, [out] WINBOOL* pbChecked, [out, string] LPWSTR* ppszLabel);
  HRESULT GetSubmitButtonValue([in] DWORD dwFieldID, [out] DWORD* pdwAdjacentTo);

  HRESULT GetComboBoxValueCount([in] DWORD dwFieldID, [out] DWORD* pcItems, [out] DWORD* pdwSelectedItem);
  HRESULT GetComboBoxValueAt([in] DWORD dwFieldID, DWORD dwItem, [out, string] LPWSTR* ppszItem);

  HRESULT SetStringValue([in] DWORD dwFieldID, [in, string] LPCWSTR psz);
  HRESULT SetCheckboxValue([in] DWORD dwFieldID, [in] WINBOOL bChecked);
  HRESULT SetComboBoxSelectedValue([in] DWORD dwFieldID, [in] DWORD dwSelectedItem);
  HRESULT CommandLinkClicked([in] DWORD dwFieldID);

  HRESULT GetSerialization([out] CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE* pcpgsr,
               [out] CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION* pcpcs,
               [out] LPWSTR* ppszOptionalStatusText,
               [out] CREDENTIAL_PROVIDER_STATUS_ICON* pcpsiOptionalStatusIcon);
  HRESULT ReportResult([in] NTSTATUS ntsStatus,
             [in] NTSTATUS ntsSubstatus,
             [out] LPWSTR* ppszOptionalStatusText,
             [out] CREDENTIAL_PROVIDER_STATUS_ICON* pcpsiOptionalStatusIcon);
}

[
  local,
  object,
  pointer_default(ref),
  uuid(9090be5b-502b-41fb-bccc-0049a6c7254b),
]
interface IQueryContinueWithStatus : IQueryContinue
{
  HRESULT SetStatusMessage([in, string] LPCWSTR psz);
}

[
  local,
  object,
  pointer_default(ref),
  uuid(9387928b-ac75-4bf9-8ab2-2b93c4a55290),
]
interface IConnectableCredentialProviderCredential : ICredentialProviderCredential
{
  HRESULT Connect([in] IQueryContinueWithStatus* pqcws);
  HRESULT Disconnect();
}

[
  object,
  pointer_default(ref),
  uuid(fa6fa76b-66b7-4b11-95f1-86171118e816),
]
interface ICredentialProviderCredentialEvents : IUnknown
{
  HRESULT SetFieldState([in] ICredentialProviderCredential* pcpc,
              [in] DWORD dwFieldID,
              [in] CREDENTIAL_PROVIDER_FIELD_STATE cpfs);
  HRESULT SetFieldInteractiveState([in] ICredentialProviderCredential* pcpc,
                   [in] DWORD dwFieldID,
                   [in] CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE cpfis);

  HRESULT SetFieldString([in] ICredentialProviderCredential* pcpc,
               [in] DWORD dwFieldID,
               [in, string, unique] LPCWSTR psz);
  HRESULT SetFieldCheckbox([in] ICredentialProviderCredential* pcpc,
               [in] DWORD dwFieldID,
               [in] WINBOOL bChecked,
               [in] LPCWSTR pszLabel);
  HRESULT SetFieldBitmap([in] ICredentialProviderCredential* pcpc,
               [in] DWORD dwFieldID,
               [in] HBITMAP hbmp);

  HRESULT SetFieldComboBoxSelectedItem([in] ICredentialProviderCredential* pcpc,
                     [in] DWORD dwFieldID,
                     [in] DWORD dwSelectedItem);
  HRESULT DeleteFieldComboBoxItem([in] ICredentialProviderCredential* pcpc,
                  [in] DWORD dwFieldID,
                  [in] DWORD dwItem);
  HRESULT AppendFieldComboBoxItem([in] ICredentialProviderCredential* pcpc,
                  [in] DWORD dwFieldID,
                  [in, string] LPCWSTR pszItem);

  HRESULT SetFieldSubmitButton([in] ICredentialProviderCredential* pcpc,
                 [in] DWORD dwFieldID,
                 [in] DWORD dwAdjacentTo);
  HRESULT OnCreatingWindow([out] HWND* phwndOwner);
}

interface ICredentialProviderEvents;

[
  local,
  object,
  pointer_default(ref),
  uuid(d27c3481-5a1c-45b2-8aaa-c20ebbe8229e),
]
interface ICredentialProvider : IUnknown
{
  HRESULT SetUsageScenario([in] CREDENTIAL_PROVIDER_USAGE_SCENARIO cpus,
               [in] DWORD dwFlags);
  HRESULT SetSerialization([in] const CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION* pcpcs);

  HRESULT Advise([in] ICredentialProviderEvents* pcpe, [in] UINT_PTR upAdviseContext);
  HRESULT UnAdvise();

  HRESULT GetFieldDescriptorCount([out] DWORD* pdwCount);
  HRESULT GetFieldDescriptorAt([in] DWORD dwIndex,
                 [out] CREDENTIAL_PROVIDER_FIELD_DESCRIPTOR** ppcpfd);

  HRESULT GetCredentialCount([out] DWORD* pdwCount,
                 [out] DWORD* pdwDefault,
                 [out] WINBOOL* pbAutoLogonWithDefault);
  HRESULT GetCredentialAt([in] DWORD dwIndex,
              [out] ICredentialProviderCredential** ppcpc);
}

[
  object,
  pointer_default(ref),
  uuid(34201e5a-a787-41a3-a5a4-bd6dcf2a854e),
]
interface ICredentialProviderEvents : IUnknown
{
  HRESULT CredentialsChanged([in] UINT_PTR upAdviseContext);
}

[
  local,
  object,
  pointer_default(ref),
  uuid(a5da53f9-d475-4080-a120-910c4a739880),
]
interface ICredentialProviderFilter : IUnknown
{
  HRESULT Filter([in] CREDENTIAL_PROVIDER_USAGE_SCENARIO cpus,
           [in] DWORD dwFlags,
           [in, size_is(cProviders)] GUID* rgclsidProviders,
           [in, out, size_is(cProviders)] WINBOOL* rgbAllow,
           [in] DWORD cProviders);
  HRESULT UpdateRemoteCredential([in] const CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION* pcpcsIn,
                   [out] CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION* pcpcsOut);
}

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
[
  local,
  object,
  pointer_default(ref),
  uuid(fd672c54-40ea-4d6e-9b49-cfb1a7507bd7),
]
interface ICredentialProviderCredential2 : ICredentialProviderCredential
{
  HRESULT GetUserSid([out, string] LPWSTR *sid);
}

[
  local,
  object,
  pointer_default(ref),
  uuid(dbc6fb30-c843-49e3-a645-573e6f39446a),
]
interface ICredentialProviderCredentialWithFieldOptions : IUnknown
{
  HRESULT GetFieldOptions([in] DWORD fieldID,
              [out] CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS *options);
}

[
  object,
  pointer_default(ref),
  uuid(b53c00b6-9922-4b78-b1f4-ddfe774dc39b),
]
interface ICredentialProviderCredentialEvents2 : ICredentialProviderCredentialEvents
{
  HRESULT BeginFieldUpdates();
  HRESULT EndFieldUpdates();
  HRESULT SetFieldOptions([in] ICredentialProviderCredential *credential,
              [in] DWORD fieldID,
              [in] CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS options);
}

[
  local,
  uuid(13793285-3ea6-40fd-b420-15f47da41fbb),
  object,
  pointer_default(ref),
]
interface ICredentialProviderUser : IUnknown
{
  HRESULT GetSid([out, string] LPWSTR *sid);
  HRESULT GetProviderID([out] GUID *providerID);
  HRESULT GetStringValue([in] REFPROPERTYKEY key, [out, string] LPWSTR *stringValue);
  HRESULT GetValue([in] REFPROPERTYKEY key, [out] PROPVARIANT *value);
}

cpp_quote("DEFINE_GUID(Identity_LocalUserProvider, 0xA198529B, 0x730F, 0x4089, 0xB6, 0x46, 0xA1, 0x25, 0x57, 0xF5, 0x66, 0x5E);")

[
  local,
  uuid(90c119ae-0f18-4520-a1f1-114366a40fe8),
  object,
  pointer_default(ref),
]
interface ICredentialProviderUserArray : IUnknown
{
  HRESULT SetProviderFilter([in] REFGUID guidProviderToFilterTo);
  HRESULT GetAccountOptions([out] CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS *credentialProviderAccountOptions);
  HRESULT GetCount([out] DWORD *userCount);
  HRESULT GetAt([in] DWORD userIndex, [out] ICredentialProviderUser **user);
}

[
  local,
  uuid(095c1484-1c0c-4388-9c6d-500e61bf84bd),
  object,
  pointer_default(ref),
]
interface ICredentialProviderSetUserArray : IUnknown
{
  HRESULT SetUserArray([in] ICredentialProviderUserArray *users);
}

cpp_quote("#endif /* (NTDDI_VERSION >= NTDDI_WIN8) */")

[
  uuid(d545db01-e522-4a63-af83-d8ddf954004f),
]
library CredentialProviders
{
  [uuid(60b78e88-ead8-445c-9cfd-0b87f74ea6cd)]
  coclass PasswordCredentialProvider { interface ICredentialProvider; }

  [uuid(6f45dc1e-5384-457a-bc13-2cd81b0d28ed)]
  coclass V1PasswordCredentialProvider { interface ICredentialProvider; }

  [uuid(cb82ea12-9f71-446d-89e1-8d0924e1256e)]
  coclass PINLogonCredentialProvider { interface ICredentialProvider; }

  [uuid(3dd6bec0-8193-4ffe-ae25-e08e39ea4063)]
  coclass NPCredentialProvider { interface ICredentialProvider; }

  [uuid(8fd7e19c-3bf7-489b-a72c-846ab3678c96)]
  coclass SmartcardCredentialProvider { interface ICredentialProvider; }

  [uuid(8bf9a910-a8ff-457f-999f-a5ca10b4a885)]
  coclass V1SmartcardCredentialProvider { interface ICredentialProvider; }

  [uuid(94596c7e-3744-41ce-893e-bbf09122f76a)]
  coclass SmartcardPinProvider { interface ICredentialProvider; }

  [uuid(1b283861-754f-4022-ad47-a5eaaa618894)]
  coclass SmartcardReaderSelectionProvider { interface ICredentialProvider; }

  [uuid(1ee7337f-85ac-45e2-a23c-37c753209769)]
  coclass SmartcardWinRTProvider { interface ICredentialProvider; }

  [uuid(25CBB996-92ED-457e-B28C-4774084BD562)]
  coclass GenericCredentialProvider { interface ICredentialProvider; }

  [uuid(5537e283-b1e7-4ef8-9c6e-7ab0afe5056d)]
  coclass RASProvider { interface ICredentialProvider; }

  [uuid(07aa0886-cc8d-4e19-a410-1c75af686e62)]
  coclass OnexCredentialProvider { interface ICredentialProvider; }

  [uuid(33c86cd6-705f-4ba1-9adb-67070b837775)]
  coclass OnexPlapSmartcardCredentialProvider { interface ICredentialProvider; }

  [uuid(503739d0-4c5e-4cfd-b3ba-d881334f0df2)]
  coclass VaultProvider { interface ICredentialProvider; }

  [uuid(bec09223-b018-416d-a0ac-523971b639f5)]
  coclass WinBioCredentialProvider { interface ICredentialProvider; }

  [uuid(ac3ac249-e820-4343-a65b-377ac634dc09)]
  coclass V1WinBioCredentialProvider { interface ICredentialProvider; }
}

cpp_quote("#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */")
