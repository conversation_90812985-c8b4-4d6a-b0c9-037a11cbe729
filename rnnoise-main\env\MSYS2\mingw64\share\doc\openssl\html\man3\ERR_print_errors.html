<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ERR_print_errors</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ERR_print_errors, ERR_print_errors_fp, ERR_print_errors_cb - print error messages</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/err.h&gt;

void ERR_print_errors(BIO *bp);
void ERR_print_errors_fp(FILE *fp);
void ERR_print_errors_cb(int (*cb)(const char *str, size_t len, void *u),
                         void *u);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>ERR_print_errors() is a convenience function that prints the error strings for all errors that OpenSSL has recorded to <b>bp</b>, thus emptying the error queue.</p>

<p>ERR_print_errors_fp() is the same, except that the output goes to a <b>FILE</b>.</p>

<p>ERR_print_errors_cb() is the same, except that the callback function, <b>cb</b>, is called for each error line with the string, length, and userdata <b>u</b> as the callback parameters.</p>

<p>The error strings will have the following format:</p>

<pre><code>[pid]:error:[error code]:[library name]:[function name]:[reason string]:[filename]:[line]:[optional text message]</code></pre>

<p><i>error code</i> is an 8 digit hexadecimal number. <i>library name</i>, <i>function name</i> and <i>reason string</i> are ASCII text, as is <i>optional text message</i> if one was set for the respective error code.</p>

<p>If there is no text string registered for the given error code, the error string will contain the numeric code.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>ERR_print_errors() and ERR_print_errors_fp() return no values.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_error_string.html">ERR_error_string(3)</a>, <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


