<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_STORE_CTX_get_by_subject</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_STORE_CTX_get_by_subject, X509_STORE_CTX_get_obj_by_subject - X509 and X509_CRL lookup functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509_vfy.h&gt;

int X509_STORE_CTX_get_by_subject(const X509_STORE_CTX *vs,
                                  X509_LOOKUP_TYPE type,
                                  const X509_NAME *name, X509_OBJECT *ret);
X509_OBJECT *X509_STORE_CTX_get_obj_by_subject(X509_STORE_CTX *vs,
                                               X509_LOOKUP_TYPE type,
                                               const X509_NAME *name);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_STORE_CTX_get_by_subject() tries to find an object of given <i>type</i>, which may be <b>X509_LU_X509</b> or <b>X509_LU_CRL</b>, and subject <i>name</i> from the store in the provided store context <i>vs</i>. If found and <i>ret</i> is not NULL, it increments the reference count and stores the looked up object in <i>ret</i>.</p>

<p>X509_STORE_CTX_get_obj_by_subject() is like X509_STORE_CTX_get_by_subject() but returns the found object on success, else NULL.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_STORE_CTX_get_by_subject() returns 1 if the lookup was successful, else 0.</p>

<p>X509_STORE_CTX_get_obj_by_subject() returns an object on success, else NULL.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_LOOKUP_meth_set_get_by_subject.html">X509_LOOKUP_meth_set_get_by_subject(3)</a>, <a href="../man3/X509_LOOKUP_by_subject.html">X509_LOOKUP_by_subject(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


