# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.



# The name this table is to be known by, with the format of the mappings in
# the main body of the table, and what all code points missing from this file
# map to.
$Unicode::UCD::SwashInfo{'ToBpb'}{'format'} = 'x'; # non-negative hex whole number; a code point
$Unicode::UCD::SwashInfo{'ToBpb'}{'missing'} = ''; # code point maps to the empty string

return <<'END';
28		0029
29		0028
5B		005D
5D		005B
7B		007D
7D		007B
F3A		0F3B
F3B		0F3A
F3C		0F3D
F3D		0F3C
169B		169C
169C		169B
2045		2046
2046		2045
207D		207E
207E		207D
208D		208E
208E		208D
2308		2309
2309		2308
230A		230B
230B		230A
2329		232A
232A		2329
2768		2769
2769		2768
276A		276B
276B		276A
276C		276D
276D		276C
276E		276F
276F		276E
2770		2771
2771		2770
2772		2773
2773		2772
2774		2775
2775		2774
27C5		27C6
27C6		27C5
27E6		27E7
27E7		27E6
27E8		27E9
27E9		27E8
27EA		27EB
27EB		27EA
27EC		27ED
27ED		27EC
27EE		27EF
27EF		27EE
2983		2984
2984		2983
2985		2986
2986		2985
2987		2988
2988		2987
2989		298A
298A		2989
298B		298C
298C		298B
298D		2990
298E		298F
298F		298E
2990		298D
2991		2992
2992		2991
2993		2994
2994		2993
2995		2996
2996		2995
2997		2998
2998		2997
29D8		29D9
29D9		29D8
29DA		29DB
29DB		29DA
29FC		29FD
29FD		29FC
2E22		2E23
2E23		2E22
2E24		2E25
2E25		2E24
2E26		2E27
2E27		2E26
2E28		2E29
2E29		2E28
2E55		2E56
2E56		2E55
2E57		2E58
2E58		2E57
2E59		2E5A
2E5A		2E59
2E5B		2E5C
2E5C		2E5B
3008		3009
3009		3008
300A		300B
300B		300A
300C		300D
300D		300C
300E		300F
300F		300E
3010		3011
3011		3010
3014		3015
3015		3014
3016		3017
3017		3016
3018		3019
3019		3018
301A		301B
301B		301A
FE59		FE5A
FE5A		FE59
FE5B		FE5C
FE5C		FE5B
FE5D		FE5E
FE5E		FE5D
FF08		FF09
FF09		FF08
FF3B		FF3D
FF3D		FF3B
FF5B		FF5D
FF5D		FF5B
FF5F		FF60
FF60		FF5F
FF62		FF63
FF63		FF62
END
