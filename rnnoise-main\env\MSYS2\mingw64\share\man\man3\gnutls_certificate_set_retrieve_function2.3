.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_retrieve_function2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_retrieve_function2 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "void gnutls_certificate_set_retrieve_function2(gnutls_certificate_credentials_t " cred ", gnutls_certificate_retrieve_function2 * " func ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t cred" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "gnutls_certificate_retrieve_function2 * func" 12
is the callback function
.SH "DESCRIPTION"
This function sets a callback to be called in order to retrieve the
certificate to be used in the handshake. The callback will take control
only if a certificate is requested by the peer.

The callback's function prototype is:
int (*callback)(gnutls_session_t, const gnutls_datum_t* req_ca_dn, int nreqs,
const gnutls_pk_algorithm_t* pk_algos, int pk_algos_length, gnutls_pcert_st** pcert,
unsigned int *pcert_length, gnutls_privkey_t * pkey);

 \fIreq_ca_dn\fP is only used in X.509 certificates.
Contains a list with the CA names that the server considers trusted.
This is a hint and typically the client should send a certificate that is signed
by one of these CAs. These names, when available, are DER encoded. To get a more
meaningful value use the function \fBgnutls_x509_rdn_get()\fP.

 \fIpk_algos\fP contains a list with server's acceptable public key algorithms.
The certificate returned should support the server's given algorithms.

 \fIpcert\fP should contain a single certificate and public key or a list of them.

 \fIpcert_length\fP is the size of the previous list.

 \fIpkey\fP is the private key.

If the callback function is provided then gnutls will call it, in the
handshake, after the certificate request message has been received.
All the provided by the callback values will not be released or
modified by gnutls.

In server side pk_algos and req_ca_dn are NULL.

The callback function should set the certificate list to be sent,
and return 0 on success. If no certificate was selected then the
number of certificates should be set to zero. The value (\-1)
indicates error and the handshake will be terminated. If both certificates
are set in the credentials and a callback is available, the callback
takes predence.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
