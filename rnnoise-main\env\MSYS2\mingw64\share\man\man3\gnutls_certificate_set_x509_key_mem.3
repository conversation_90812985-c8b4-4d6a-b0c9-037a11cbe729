.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_x509_key_mem" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_x509_key_mem \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_x509_key_mem(gnutls_certificate_credentials_t " res ", const gnutls_datum_t * " cert ", const gnutls_datum_t * " key ", gnutls_x509_crt_fmt_t " type ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "const gnutls_datum_t * cert" 12
contains a certificate list (path) for the specified private key
.IP "const gnutls_datum_t * key" 12
is the private key, or \fBNULL\fP
.IP "gnutls_x509_crt_fmt_t type" 12
is PEM or DER
.SH "DESCRIPTION"
This function sets a certificate/private key pair in the
gnutls_certificate_credentials_t type. This function may be called
more than once, in case multiple keys/certificates exist for the
server.

Note that the keyUsage (*********) PKIX extension in X.509 certificates
is supported. This means that certificates intended for signing cannot
be used for ciphersuites that require encryption.

If the certificate and the private key are given in PEM encoding
then the strings that hold their values must be null terminated.

The  \fIkey\fP may be \fBNULL\fP if you are using a sign callback, see
\fBgnutls_sign_callback_set()\fP.

Note that, this function by default returns zero on success and a negative value on error.
Since 3.5.6, when the flag \fBGNUTLS_CERTIFICATE_API_V2\fP is set using \fBgnutls_certificate_set_flags()\fP
it returns an index (greater or equal to zero). That index can be used to other functions to refer to the added key\-pair.
.SH "RETURNS"
On success this functions returns zero, and otherwise a negative value on error (see above for modifying that behavior).
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
