.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_tdb_set_store_func" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_tdb_set_store_func \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_tdb_set_store_func(gnutls_tdb_t " tdb ", gnutls_tdb_store_func " store ");"
.SH ARGUMENTS
.IP "gnutls_tdb_t tdb" 12
The trust storage
.IP "gnutls_tdb_store_func store" 12
The storage function
.SH "DESCRIPTION"
This function will associate a storage function with the
trust storage structure. The function is of the following form.

int gnutls_tdb_store_func(const char* db_name, const char* host,
const char* service, time_t expiration,
const gnutls_datum_t* pubkey);

The  \fIdb_name\fP should be used to pass any private data to this function.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
