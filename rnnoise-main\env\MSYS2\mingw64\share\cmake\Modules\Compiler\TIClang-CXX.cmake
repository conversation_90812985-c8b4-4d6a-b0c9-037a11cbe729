include(Compiler/Clang-CXX)
include(Compiler/TIClang)
__compiler_ticlang(CXX)

if((NOT DEFINED CMAKE_DEPENDS_USE_COMPILER OR CMAKE_DEPENDS_USE_COMPILER)
    AND CMAKE_GENERATOR MATCHES "Makefiles|WMake"
    AND CMAKE_DEPFILE_FLAGS_CXX)
  # dependencies are computed by the compiler itself
  set(CMAKE_CXX_DEPFILE_FORMAT gcc)
  set(CMAKE_CXX_DEPENDS_USE_COMPILER TRUE)
endif()

set(CMAKE_CXX98_STANDARD_COMPILE_OPTION "-std=c++98")
set(CMAKE_CXX98_EXTENSION_COMPILE_OPTION "-std=gnu++98")
set(CMAKE_CXX98_STANDARD__HAS_FULL_SUPPORT ON)

set(CMAKE_CXX11_STANDARD_COMPILE_OPTION "-std=c++11")
set(CMAKE_CXX11_EXTENSION_COMPILE_OPTION "-std=gnu++11")

set(CMAKE_CXX14_STANDARD_COMPILE_OPTION "-std=c++14")
set(CMAKE_CXX14_EXTENSION_COMPILE_OPTION "-std=gnu++14")
set(CMAKE_CXX14_STANDARD__HAS_FULL_SUPPORT ON)

set(CMAKE_CXX17_STANDARD_COMPILE_OPTION "-std=c++17")
set(CMAKE_CXX17_EXTENSION_COMPILE_OPTION "-std=gnu++17")

set(CMAKE_CXX_STANDARD_LATEST 17)

# Including the "${CMAKE_ROOT}/Modules/Compiler/Clang-CXX.cmake" script above may set several other compile option
# variables which do not necessarily apply here. So, we unset those variables accordingly.
unset(CMAKE_CXX20_STANDARD_COMPILE_OPTION)
unset(CMAKE_CXX20_EXTENSION_COMPILE_OPTION)

unset(CMAKE_CXX23_STANDARD_COMPILE_OPTION)
unset(CMAKE_CXX23_EXTENSION_COMPILE_OPTION)

unset(CMAKE_CXX26_STANDARD_COMPILE_OPTION)
unset(CMAKE_CXX26_EXTENSION_COMPILE_OPTION)
