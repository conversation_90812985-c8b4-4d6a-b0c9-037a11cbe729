.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_serial" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_serial \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_serial(gnutls_x509_crt_t " cert ", void * " result ", size_t * " result_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "void * result" 12
The place where the serial number will be copied
.IP "size_t * result_size" 12
Holds the size of the result field.
.SH "DESCRIPTION"
This function will return the X.509 certificate's serial number.
This is obtained by the X509 Certificate serialNumber field. Serial
is not always a 32 or 64bit number. Some CAs use large serial
numbers, thus it may be wise to handle it as something uint8_t.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
