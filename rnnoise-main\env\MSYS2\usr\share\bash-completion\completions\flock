_flock_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-w'|'--timeout')
			COMPREPLY=( $(compgen -W "seconds" -- $cur) )
			return 0
			;;
		'-E'|'--conflict-exit-code')
			COMPREPLY=( $(compgen -W "{0..255}" -- $cur) )
			return 0
			;;
		'-c'|'--command')
			compopt -o bashdefault
			COMPREPLY=( $(compgen -c -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--shared
				--exclusive
				--unlock
				--nonblock
				--timeout
				--conflict-exit-code
				--close
				--command
				--no-fork
				--help
				--version"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	local IFS=$'\n'
	compopt -o filenames
	COMPREPLY=( $(compgen -f -- ${cur:-"/"}) )
	return 0
}
complete -F _flock_module flock
