This document details the changes between this version, bash-5.2-release, and
the previous version, bash-5.2-rc4.

1. Changes to Bash

2. Changes to Readline

a. When replacing a history entry, make sure the existing entry has a non-NULL
   timestamp before copying it; it may have been added by the application, not
   the history library.

3. New Features in Bash

4. New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.2-rc4, and
the previous version, bash-5.2-rc3.

1. Changes to Bash

a. Changed how the compatibility mode enabling of extglob works in conjunction
   with parsing conditional commands.

b. Fixed a problem with aliases containing command substitutions.

2. Changes to Readline

3. New Features in Bash

4. New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.2-rc3, and
the previous version, bash-5.2-rc2.

1. Changes to Bash

a. Added a compatibility mode feature that causes the parser to parse command
   substitutions as if extglob were enabled. If it is enabled before execution,
   parse at execution will succeed. If not, the subsequent execution parse will
   fail.

b. Fixed an issue with handling a `return' executed in a trap action if the
   trap is executed while running in a shell function.

2. Changes to Readline

3. New Features in Bash

4. New Features in Readline

a. Readline now checks for changes to locale settings (LC_ALL/LC_CTYPE/LANG)
   each time it is called, and modifies the appropriate locale-specific display
   and key binding variables when the locale changes.

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.2-rc2, and
the previous version, bash-5.2-rc1.

1. Changes to Bash

a. Fixed a bug that could disable history saving after a compound array
   assignment in an interactive shell.

b. Fixed a bug that could cause incorrect error messages when running a DEBUG
   trap during a conditional or arithmetic command.

c. Fixed a bug that caused test to print an error message when given ! ! arg
   as arguments.

d. Fixed a bug that resulted in incorrect error messages when completing a
   backquoted command substitution.

e. Changed command substitution parsing to reproduce the original text more
   closely when reconsituting the command string from the parsed command.

f. Fixed a bug that could cause an EXIT trap to use the wrong variable context
   when the errexit option is set.

g. Fixed a bug that could cause the parser to incorrectly delimit a token when
   an alias expansion ended with a multibyte character.

2. Changes to Readline

3. New Features in Bash

a. Since there is no `declare -' equivalent of `local -', make sure to use
   `local -' in the output of `local -p'.

b. Null anchored matches in pattern substitution now process `&' in the
   replacement string, like sed.

4. New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.2-rc1, and
the previous version, bash-5.2-beta.

1. Changes to Bash

a. Changes to `wait -p' and how it sets the variable name in the presence of
   valid and invalid PID arguments.

b. Fixed a bug that caused compgen to dump core if the completion engine was
   not initialized.

c. Fixed a memory leak in the variable name programmable completion code.

d. Here-documents and here-strings use tempfiles if the shell compatibility
   level is 50 or lower.

e. Non-interactive shells exit on a syntax error encountered while parsing a
   command substitution.

f. Fixed a bug with inherited parser state while parsing a command substitution.

g. Fixed a bug that caused the shell not to check for terminating signals
   after executing the command_string supplied with `-c' and before executing
   the exit trap.

h. Changes to avoid a make race condition while generating builtins.c.

i. Make it explicit that BASH_REMATCH is always a global variable, and that
   local copies are (currently) ignored.

j. Fixed a bug that caused an ambiguous redirection (>&word) to be printed
   incorrectly (>&word) if no file descriptor was supplied.

2. Changes to Readline

a. Fixed a bug that caused rl_eof_found to be set prematurely while reading a
   multi-character key sequence in callback mode.

3. New Features in Bash

a. In posix mode, the `printf' builtin checks for the `L' length modifier and
   uses long double for floating point conversion specifiers if it's present,
   double otherwise.

b. The `globbing' completion code now takes the `globstar' option into account.

c. `suspend -f' now forces the shell to suspend even if job control is not
   currently enabled.

4. New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.2-beta, and
the previous version, bash-5.2-alpha.

1. Changes to Bash

a. Fixed a problem with command-oriented history and multi-line commands that
   caused embedded blank lines to be run together.

b. Changed the way `&' is quoted when performing pattern substitution and
   `patsub_replacement' is enabled.

c. Fixed some integer overflows when expanding strings or reading the output
   of command substitution larger than 2GB.

d. `wait -p' without the `-n' option now does something useful if there are no
   jobs.

e. Fixed an issue with read timeouts in posix mode.

f. Changed here-document processing to process $'...' and $"..." only when they
   appear in the WORD portion of ${PARAM OP WORD} in the here-document body
   and the body is being expanded.

g. Changed alias expansion in command substitution to be posix-conformant
   (performed while initially parsing the command substitution) when in posix
   mode.

h. Bash optimizes away more forks in subshells.

i. Here-document construction now performs quote removal on the here-document
   delimiter only if it's marked as quoted, which prevents quote characters in
   command substitutions from being removed.

j. Prompt string expansion now gives invisible characters in the expansion of
   the \w, \W, and \s escape sequences a visible representation to avoid
   problems with redisplay.

k. Fixed a problem with SIGINT during the execution of a command bound with
   `bind -x' affecting the saved terminal settings.

l. Fixed an inconsistency with how $@ expands in a construct like ${@:+set}
   or ${array[@]:+set} in the presence of null positional parameters or
   array elements.

2. Changes to Readline

a. Prevent some display problems when running a command as the result of a
   trap or one bound using `bind -x' and the command generates output.

b. Fixed an issue with multi-line prompt strings that have one or more
   invisible characters at the end of a physical line.

c. Fixed an issue that caused a history line's undo list to be cleared when
   it should not have been.

3. New Features in Bash

a. There is a new bindable readline command name: `vi-edit-and-execute-command'.

4. New Features in Readline

a. Two new bindable string variables: active-region-start-color and
   active-region-end-color. The first sets the color used to display the
   active region; the second turns it off. If set, these are used in place
   of terminal standout mode.

b. New readline state (RL_STATE_EOF) and application-visible variable
   (rl_eof_found) to allow applications to detect when readline reads EOF
   before calling the deprep-terminal hook.

c. There is a new configuration option: --with-shared-termcap-library, which
   forces linking the shared readline library with the shared termcap (or
   curses/ncurses/termlib) library so applications don't have to do it.

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.2-alpha, and
the previous version, bash-5.1-release.

1. Changes to Bash

a. Fixed a bug that assigned a value to the variable name supplied as an
   argument to `wait -p' when there were no jobs.

b. Fixed a bug that resulted in performing incorrect word expansion on the
   key/value pairs in a compound array assignment.

c. Fixed a bug that could put the child forked to run a command substitution
   into the wrong process group.

d. Fixed a problem that could cause the lastpipe option to work incorrectly if
   file descriptor 0 was closed.

e. Bash tries to suppress traps if a forked child receives a trapped signal
   before it has a chance to reset its signal handlers.

f. Fixed several memory leaks in compound array assignments.

g. Fixed a problem with performing an assignment with `+=' to an array element
   that was the value of a nameref.

h. Fixed a bug that could cause a nameref containing an array reference using
   `@' or `*' not to expand to multiple words.

i. Fixed a bug where extended glob functions could match `.' or `..' when it
   wasn't explicitly specified, even if dotglob was set.

j. Fixed a bug that caused non-interactive posix-mode shells not to exit on a
   variable assignment error while assigning into the temporary environment.

k. Fixed a bug that caused parsing errors if an alias contained a compound
   array assignment.

l. Fixed a couple of instances where bash checked syntax too aggressively when
   trying to determine how to add a partial command to command-oriented
   history.

m. Fixed a parser problem that caused it not to allow reserved words to follow
   the `((' and `[[' commands.

n. Fixed a bad offset calculation when using negative offsets to `history -d'.

o. Fixed an off-by-one error that caused a read past the end of a buffer when
   reading a multibyte character from the output of a command substitution.

p. Fixed a problem with a failed `exec' command not setting $? to the right
   value for an exit trap.

q. Fixed a problem that caused bash not to unlink FIFOs created as part of
   expanding redirections for an external command.

r. Fixed a bug that could cause aliases not to be expanded in case statements.

s. Fixed a bug that could cause word completion to attempt programmable
   completion for the target of a redirection, instead of filename completion.

t. Fixed a bug that could result in errors after rebinding a key sequence with
   `bind -x' multiple times.

u. Fixed a problem that could result in not quoting the result when performing
   command name completion with a glob pattern in the command name.

v. `mapfile' now uses fully-buffered reads in more cases, which should improve
   bulk read performance.

w. Fixed a bug that caused `wait -n' to not reset its internal state when
   interrupted by a signal, resulting in subsequent calls failing.

x. Fixed a bug with parsing numeric arguments to readline key sequences
   installed with `bind -x'.

y. Bash suppresses forking in several additional cases, including most uses
   of $(<file).

z. If there are multiple `!' tokens in a [[ command, toggle the invert state
   instead of setting it unconditionally.

aa. Fixed a bug where running `fc' on an empty history list would cause the
    shell to crash.

bb. Word completion now checks whether or not a quote character closes a
    quoted string begun on a previous line, so readline doesn't interpret the
    quote as starting a new quoted string.

cc. Fixed a typo that translated \UNNNNNNNN characters that were not valid in
    the current locale encoding as \uNNNNNNNN.

dd. Fixed an issue that could cause bash to print timing statistics for the
    wrong command when `set -e' causes a command to fail.

ee. Bash now runs the exit trap in function context if a failed command in
    the function causes the shell to exit.

ff. Some fixes to how subshells modify $SHLVL.

gg. Fixed a bug that caused `mapfile -t' not to remove the delimiter when the
    delimiter is > 128 in the current encoding.

hh. Fixed a problem that could cause the shell to attempt to free unallocated
    memory if an expansion error occurred.

ii. Fixed a bug in the bash malloc implementation of malloc_usable_size. Bash
    did not use it, but it could cause problems with library functions that
    did.

jj. If the `exec' builtin fails, and the shell does not exit, it restores
    trapped signals to their trapped state.

kk. Fixed a bug that could cause variable assignment arguments to `declare' to
    expand variables using attributes that the call to declare was turning off.

ll. Fixed a bug with LINENO and arithmetic for commands.

mm. Fixed a posix-mode bug that caused tildes not to be expanded after an
    unquoted colon on the right side of an assignment statement.

nn. Fixed a problem with `caller' and line numbers when executing an ERR trap.

oo. Fixed a problem that could make the value returned by ${a:=b} not be the
    final value assigned to a (e.g., if `a' has an attribute that modifies
    the value on assignment).

pp. Fixed a problem with saving multi-line here-documents to the history list
    where the here-document delimiter does not appear on the first line.

qq. Fixed a bug with using += to assign to dynamic variables like RANDOM.

rr. Fixed a bug that caused `set -n' to modify $? if set after execution had
    started.

ss. Builtins like printf/read/wait now behave more consistently when assigning
    arbitrary keys to associative arrays (like `]'. when appropriately quoted).

tt. Fixed a problem with here-document collection printing the wrong prompt
    when parsing a compound list in an interactive shell.

uu. Fixed a problem with quoting shell expansion characters (like `$') when
    they appear in a tab-completed word along with characters that do need
    quoting (e.g.. $HOME/VirtualBox VMs).

2. Changes to Readline

a. Fixed a problem with cleaning up active marks when using callback mode.

b. Fixed a problem with arithmetic comparison operators checking the version.

c. Fixed a problem that could cause readline not to build on systems without
   POSIX signal functions.

d. Fixed a bug that could cause readline to crash if the application removed
   the callback line handler before readline read all typeahead.

e. Added additional checks for read errors in the middle of readline commands.

f. Fixed a redisplay problem that occurred when switching from the digit-
   argument prompt `(arg: N)' back to the regular prompt and the regular
   prompt contained invisible characters.

g. Fixed a problem with restoring the prompt when aborting an incremental
   search.

h. Fix a problem with characters > 128 not being displayed correctly in certain
   single-byte encodings.

i. Fixed a problem with unix-filename-rubout that caused it to delete too much
   when applied to a pathname consisting only of one or more slashes.

j. Fixed a display problem that caused the prompt to be wrapped incorrectly if
   the screen changed dimensions during a call to readline() and the prompt
   became longer than the screen width.

k. Fixed a problem that caused the \r output by turning off bracketed paste
   to overwrite the line if terminal echo was disabled.

l. Fixed a bug that could cause colored-completion-prefix to not display if
   completion-prefix-display-length was set.

m. Fixed a problem with line wrapping prompts when a group of invisible
   characters runs to the right edge of the screen and the prompt extends
   longer then the screen width.

n. Fixed a couple problems that could cause rl_end to be set incorrectly by
   transpose-words.

3. New Features in Bash

a. The bash malloc returns memory that is aligned on 16-byte boundaries.

b. There is a new internal timer framework used for read builtin timeouts.

c. Rewrote the command substitution parsing code to call the parser recursively
   and rebuild the command string from the parsed command. This allows better
   syntax checking and catches errors much earlier. Along with this, if
   command substitution parsing completes with here-documents remaining to be
   read, the shell prints a warning message and reads the here-document bodies
   from the current input stream.

d. The `ulimit' builtin now treats an operand remaining after all of the options
   and arguments are parsed as an argument to the last command specified by
   an option. This is for POSIX compatibility.

e. Here-document parsing now handles $'...' and $"..." quoting when reading the
   here-document body.

f. The `shell-expand-line' and `history-and-alias-expand-line' bindable readline
   commands now understand $'...' and $"..." quoting.

g. There is a new `spell-correct-word' bindable readline command to perform
   spelling correction on the current word.

h. The `unset' builtin now attempts to treat arguments as array subscripts
   without parsing or expanding the subscript, even when `assoc_expand_once'
   is not set.

i. There is a default value for $BASH_LOADABLES_PATH in config-top.h.

j. Associative array assignment and certain instances of referencing (e.g.,
   `test -v') now allow `@' and `*' to be used as keys.

k. Bash attempts to expand indexed and associative array subscripts only
   once when executing shell constructs and word expansions.

l. The `unset' builtin allows a subscript of `@' or `*' to unset a key with
   that value for associative arrays instead of unsetting the entire array
   (which you can still do with `unset arrayname'). For indexed arrays, it
   removes all elements of the array without unsetting it (like `A=()').

m. Additional builtins (printf/test/read/wait) do a better job of not
   parsing array subscripts if array_expand_once is set.

n. New READLINE_ARGUMENT variable set to numeric argument for readline commands
   defined using `bind -x'.

o. The new `varredir_close' shell option causes bash to automatically close
   file descriptors opened with {var}<fn and other styles of varassign
   redirection unless they're arguments to the `exec' builtin.

p. The `$0' special parameter is now set to the name of the script when running
   any (non-interactive) startup files such as $BASH_ENV.

q. The `enable' builtin tries to load a loadable builtin using the default
   search path if `enable name' (without any options) attempts to enable a
   non-existent builtin.

r. The `printf' builtin has a new format specifier: %Q. This acts like %q but
   applies any specified precision to the original unquoted argument, then
   quotes and outputs the result.

s. The new `noexpand_translations' option controls whether or not the translated
   output of $"..." is single-quoted.

t. There is a new parameter transformation operator: @k. This is like @K, but
   expands the result to separate words after word splitting.

u. There is an alternate array implementation, selectable at `configure' time,
   that optimizes access speed over memory use (use the new configure
    --enable-alt-array-implementation option).

v. If an [N]<&WORD- or [N]>&WORD- redirection has WORD expand to the empty
   string, treat the redirection as [N]<&- or [N]>&- and close file descriptor
   N (default 0).

w. Invalid parameter transformation operators are now invalid word expansions,
   and so cause fatal errors in non-interactive shells.

x. New shell option: patsub_replacement. When enabled, a `&' in the replacement
   string of the pattern substitution expansion is replaced by the portion of
   the string that matched the pattern. Backslash will escape the `&' and
   insert a literal `&'.

y. `command -p' no longer looks in the hash table for the specified command.

z. The new `--enable-translatable-strings' option to `configure' allows $"..."
   support to be compiled in or out.

aa. The new `globskipdots' shell option forces pathname expansion never to
    return `.' or `..' unless explicitly matched. It is enabled by default.

bb. Array references using `@' and `*' that are the value of nameref variables
    (declare -n ref='v[@]' ; echo $ref) no longer cause the shell to exit if
    set -u is enabled and the array (v) is unset.

4. New Features in Readline

a. There is now an HS_HISTORY_VERSION containing the version number of the
   history library for applications to use.

b. History expansion better understands multiple history expansions that may
   contain strings that would ordinarily inhibit history expansion (e.g.,
   `abc!$!$').

c. There is a new framework for readline timeouts, including new public
   functions to set timeouts and query how much time is remaining before a
   timeout hits, and a hook function that can trigger when readline times
   out. There is a new state value to indicate a timeout.

d. Automatically bind termcap key sequences for page-up and page-down to
   history-search-backward and history-search-forward, respectively.

e. There is a new `fetch-history' bindable command that retrieves the history
   entry corresponding to its numeric argument. Negative arguments count back
   from the end of the history.

f. `vi-undo' is now a bindable command.

g. There is a new option: `enable-active-region'. This separates control of
   the active region and bracketed-paste. It has the same default value as
   bracketed-paste, and enabling bracketed paste enables the active region.
   Users can now turn off the active region while leaving bracketed paste
   enabled.

h. rl_completer_word_break_characters is now `const char *' like
   rl_basic_word_break_characters.

i. Readline looks in $LS_COLORS for a custom filename extension
   (*.readline-colored-completion-prefix) and uses that as the default color
   for the common prefix displayed when `colored-completion-prefix' is set.

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.1-rc3, and
the previous version, bash-5.1-rc2.

1. Changes to Bash

a. The `assoc_expand_once' option now affects the evaluation of the -v primary
   to test and the [[ compound command.

2. Changes to Readline

a. Fixed a bug that could cause point to be set beyond the end of the line
   buffer when aborting an incremental search.

3. New Features in Bash

4. New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.1-rc2, and
the previous version, bash-5.1-rc1.

1. Changes to Bash

a. Process substitutions started from an interactive shell no longer have their
   standard input implicitly redirected from /dev/null.

b. Fixed an issue with setting the SIGINT trap handler in an interactive shell
   when temporarily running $PROMPT_COMMAND non-interactively.

2. Changes to Readline

a. Terminals that are named "dumb" or unknown do not enable bracketed paste
   by default.

b. Ensure that disabling bracketed paste turns off highlighting the incremental
   search string when the search is successful.

3. New Features in Bash

4. New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.1-rc1, and
the previous version, bash-5.1-beta.

1. Changes to Bash

a. Fixed an inconsistency in the way HISTCMD is calculated when it's expanded
   during a multi-line command.

b. Modified the change to here-document expansion containing backslash-quoted
   double quotes.

c. Fixed a case where the shells's exit status could be greater than 255.

d. Modified changed to process substitution so the executed command has its
   stdin redirected from /dev/null if it was previously interactive and
   reading commands from the terminal.

2. New Features in Bash

a. There is a new contributed loadable builtin: asort.

3. Changes to Readline

a. Fixed a bug that could cause an application with an application-specific
   redisplay function to crash if the line data structures had not been
   initialized.

4. New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.1-beta, and
the previous version, bash-5.1-alpha.

1. Changes to Bash

a. Fixed a bug that caused name references to variables to not update the
   referenced variable's assignment side effects.

b. Tightened up the parameter transformation error checking for invalid
   transformation operators.

c. System-specific changes for: FreeBSD

d. A few minor changes to move potential uses of stdio functions out of signal
   handling paths.

e. Make sure SIGCHLD is blocked in all cases where waitchld() is not called
   from a signal handler.

f. Changed `command' builtin processing so it no longer starts an extra process
   when run asynchronously (command x &).

g. Avoid performing tilde expansion after `:' in words that look like assignment
   statements when in posix mode.

h. Slight changes to how the `complete' builtin prints out options and actions
   for completion specifications.

i. Several changes to how `local -' restores the values of options and
   $SHELLOPTS.

j. Don't treat a word in a compound assignment as an assignment statement
   unless it has a valid subscript before the `='.

k. Fixed a bug with the DEBUG trap and process substitution that caused the
   terminal's process group to be set incorrectly.

l. Fixed a bug that left readline's signal handlers installed while running a
   shell command from a bindable readline command.

m. Fixed the `fc' builtin to clamp out of range history specifications at the
   boundaries of the history list for POSIX conformance.

n. Fixed a bug that caused ${foo@a} to treat foo as an unset variable if it
   was an array without a value for subscript 0/"0" but had other set
   elements.

o. Fixed a bug that caused the history code to attempt to parse command
   substitutions looking for shell comments before adding them to the history,
   even while parsing here-documents.

p. Fixed a bug that could cause a syntax error in a command read by `eval' to
   exit an interactive shell.

2. New Features in Bash

a. If the hash builtin is listing hashed filenames portably, don't print
   anything if the table is empty.

b. GLOBIGNORE now ignores `.' and `..' as a terminal pathname component.

c. Bash attempts to optimize away forks in the last command in a function body
   under appropriate circumstances.

d. The globbing code now uses fnmatch(3) to check collation elements (if
   available) even in cases without multibyte characters.

e. The `fg' and `bg' builtins now return an error in a command substitution
   when asked to restart a job inherited from the parent shell.

f. The shell now attempts to unlink all FIFOs on exit, whether a consuming
   process has finished with them or not.

3. Changes to Readline

a. Make sure that all undo groups are closed when leaving vi insertion mode.

b. Make sure that the vi-mode `C' and `c' commands enter insert mode even if
   the motion command doesn't have any effect.

c. Fixed several potential memory leaks in the callback mode context handling.

d. If readline is handling a SIGTTOU, make sure SIGTTOU is blocked while
   executing the terminal cleanup code, since it's no longer run in a signal
   handling context.

4. New Features in Readline

a. The signal cleanup code now blocks SIGINT while processing after a SIGINT.

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.1-alpha, and
the previous version, bash-5.0-release.

1. Changes to Bash

a. Fixed a bug that caused a posix-mode shell to not exit if the return builtin
   was executed outside a function or dot script.

b. Fixed a bug where `declare +f' could potentially turn off the function
   attribute.

c. Restored bash-4.4 pathname expansion behavior when a word to be expanded had
   only backslashes, not any of the other globbing characters. This came after
   an extensive POSIX group discussion (interp #1234).

d. There are more changes to avoid performing word expansions multiple times on
   arithmetic expressions.

e. Fixed a bug with alias expansion when the alias ends with a TAB.

f. Fixed a bug that caused programmable completion to return an error if the
   shell function name supplied as an argument to `complete -F' was invalid.

g. There are several fixes to buffer overflows found as the result of fuzzing
   with random input.

h. Fixed a bug that caused the edit-and-execute-command editing command to
   start with the previous history line if invoked on an empty line.

i. Fixed a bug that potentially caused `bind --help' to change readline's
   output stream.

j. Turning off posix mode now restores the vi-insertion mode binding for TAB
   that was in effect when posix mode was enabled.

k. Restore the previous state of job control being enabled if `exec' fails in
   an interactive shell.

l. Fixed a bug that caused the terminal's process group to be set incorrectly
   if job control was turned off before starting an interactive shell.

m. Fixed a bug that caused a crash when HISTSIZE=0.

n. Fixed a word expansion bug that caused null strings on the rhs of expansions
   to be discarded incorrectly.

o. History list management does a better job of handling the situation where
   the number of history entries from the current shell session is greater than
   the number of entries in the history list.

p. Fixed a bug that caused the `fc' builtin to attempt to dereference a newly-
   freed history entry.

q. Fixed a bug that made the `Q' variable transformation not work well with
   `set -u'.

r. There are several word expansion fixes for expanding $* and $@ in contexts
   where word splitting is not going to be performed, since each positional
   parameter must expand to a separate word.

s. Fixed a bug that could cause ^D to exit bash immediately even if there were
   stopped jobs.

t. Fixed a bug with double-quoting and backslash-quoting strings containing
   multibyte characters for reuse.

u. Fixed a bug that caused the line number to be reported incorrectly if the
   shell executed a (command) subshell.

v. Fixed a bug that caused the shell to fail to reap process substitutions
   when they went out of scope, which had unpredictable results.

w. Fixed a bug that caused null strings in arguments to [[ conditional command
   operators to compare incorrectly.

x. Changed the behavior of `wait' without arguments to only wait for background
   processes the current shell instance started, not background children it may
   have inherited.

y. Fixed a bug that could cause command substitution to leave file descriptors
   open if the shell received many SIGINTs.

z. Bash now behaves better if the `**' filename expansion operator encounters a
   symbolic link to a directory, avoiding more cases where it might return
   duplicate pathnames.

aa. Programmable completion now canonicalizes directory names in the same way
    as bash word completion, so `..' is handled consistently.

bb. Fixed a bug when using RETURN as the delimiter to the read builtin; it
    caused readline to set the binding for RETURN incorrectly.

cc. Fixed a bug that caused `history -d' to delay printing an out-of-range
    error message.

dd. Fixed a bug with `bash -c command' where `command' ends with an expanded
    alias.

ee. Fixed a bug that could result in `history -n' adding spurious line feeds to
    commands in the history list.

ff. The $RANDOM random number generator now XORs the top and bottom halves of
    the internal 32-bit value to introduce more randomness. Setting the shell
    compatibility level to 50 or lower undoes this.

gg. Fixed several problems caused by running the DEBUG trap on simple commands
    executed as part of a pipeline.

ii. Fixed a bug that didn't allow `bind -r' to remove the binding for \C-@.

jj. Several fixes to the bash-backward-shellword bindable readline command to
    behave better when at the last character on the line.

kk. If `set -x' is enabled, bash doesn't print a command twice if it's run by
    the `command' builtin.

ll. Fixed a bug with printing function definitions containing here documents.

mm. Fixed a bug that could cause the `bind' builtin to set $? to -1.

nn. Fixed a bug that didn't reset the timezone information correctly when the
    TZ variable was unset.

oo. Fixed several issues with assigning an associative array variable using a
    compound assignment that expands the value of the same variable.

pp. Fixed several places where the shell set $? without setting PIPESTATUS.

qq. Fixed a problem with glob bracket expressions containing invalid character
    classes, collating symbols, or equivalence classes -- they should not
    require a closing right bracket.

rr. Fixed a bug where running a builtin in a subshell did not run the EXIT trap.

ss. Fixed several problems with posix-mode variable assignments preceding
    shell function calls and posix special builtins, so that they create and
    modify variables at the current scope.

tt. Fix history initialization so `bash +o history' works as expected.

uu. Fixed a bug in the bindable edit-and-execute-command command that could
    interfere with the shell's parsing state.

vv. Fixed an issue with nested traps running command substitutions in command
    lines with command substitutions.

ww. Fixed a bug with globbing pathnames that contain invalid multibyte
    characters (sequences that don't correspond to a character in the current
    locale).

xx. Fixed a bug that caused the shell not to exit if a function definition
    failed while errexit was enabled.

yy. Process substitution processes now get their input from /dev/null, since
    they are asynchronous, not interactive, and not jobs.

zz. Setting nocaseglob no longer turns on case-insensitive regexp matching.

aaa. Fixed a bug that resulted in extra blank lines being added to some history
     entries containing here-documents.

bbb. Fixed a bug that resulted in incorrect matching of some patterns in word
     expansion if they expanded to the empty string.

ccc. Fixed here-string expansion so it behaves the same as expansion of the
     rhs of an assignment statement.

ddd. Changed here-document parsing to no longer allow the end of the here
     document to delimit a command substitution.

eee. Several fixes to history expansion: multiple :p modifiers work, a ^ word
     designator works as part of a range, and a `-' is treated as part of a
     search string if it immediately follows a `!'.

fff. Fixed a bug in pattern substitution with null matches in a string
     containing multibyte characters.

ggg. Unbinding a key sequence bound with `bind -x' now removes the key sequence
     from the additional keymap `bind -x' uses.

hhh. Fixed a bug with command start detection for completion so that it doesn't
     mistake brace expansion for the start of a command.

iii. Fixed a bug that caused local variables with the same name as variables
     appearing in a function's temporary environment to not be marked as local.

jjj. Fixed a bug that could cause SIGCHLD to be blocked when executing return
     or exec in the rightmost pipeline element with lastpipe enabled.

kkk. Fixed a bug that could result in commands without the execute bit set
     being added to the command hash table.

lll. Fixed a bug that allowed non-digits to follow the `#' in a `base#number'
     integer constant.

mmm. Fixed a bug that made `time -- command' attempt to execute `--'.

nnn. Fixed a couple of bugs with variable transformation using arrays
     subscripted with `*' or `@'.

ooo. A failure to create a variable using `declare' in a function no longer
     causes the function to return immediately.

ppp. Fixed a bug that could cause the := word expansion to add a non-null
     value if attempting to assign a null string when double-quoted.

qqq. Fixed a bug that could cause backslashes quoting double quotes in here
     document bodies to not be removed when expanding the body.

rrr. Fixed a bug that caused commands following a subshell while the shell is
     reading input from stdin but not interactive, while job control is
     enabled, to be executed twice.

sss. Fixed a bug where receiving SIGTERM from a different process while
     readline was active could cause the shell to terminate.

ttt. In posix mode, running a trap after the read builtin now sees the exit
     status of the read builtin (e.g., 130 after a SIGINT) in $?.

uuu. Fixed a bug with nameref variables referencing array subscripts used in
     arithmetic expressions.

vvv. Fixed a bug that caused the pipeline process group id to be reset in the
     middle of a command list run by a shell started to run a command
     substitution.

www. Restricted shells can no longer read and write history files with pathnames
     containing slashes.

xxx. Fixed a couple of problems with 0 and -0 used as arguments to `fc' when
     not listing commands from the history.

yyy. When `test' is supplied four or more arguments, treat an argument that
     looks like an operator (e.g., -e), but is in a place where only a string
     is valid, as a string, as it would be when using the POSIX rules, instead
     of an operator with a missing argument.

zzz. There is no `compat50' shopt option. Changes to the shell compatibility
     level should use the BASH_COMPAT variable.

aaaa. Redirection failures with compound commands are now treated as errors
      that cause the shell to exit if `errexit' is enabled.

bbbb. Redirection failure error messages no longer expand the word in the
      redirection again.

cccc. History expansion is no longer performed while parsing a here-document
      inside a command substitution.

2. Changes to Readline

a. There are a number of fixes that were found as the result of fuzzing with
   random input.

b. Changed the revert-all-at-newline behavior to make sure to start at the end
   of the history list when doing it, instead of the line where the user hit
   return.

c. When parsing `set' commands from the inputrc file or an application, readline
   now allows trailing whitespace.

d. Fixed a bug that left a file descriptor open to the history file if the
   file size was 0.

e. Fixed a problem with binding key sequences containing meta characters.

f. Fixed a bug that caused the wrong line to be displayed if the user tried to
   move back beyond the beginning of the history list, or forward past the end
   of the history list.

g. If readline catches SIGTSTP, it now sets a hook that allows the calling
   application to handle it if it desires.

h. Fixed a redisplay problem with a prompt string containing embedded newlines.

i. Fixed a problem with completing filenames containing invalid multibyte
   sequences when case-insensitive comparisons are enabled.

j. Fixed a redisplay problem with prompt strings containing invisible multibyte
   characters.

k. Fixed a problem with multibyte characters mapped to editing commands that
   modify the search string in incremental search.

l. Fixed a bug with maintaining the key sequence while resolving a bound
   command in the presence of ambiguous sequences (sequences with a common
   prefix), in most cases while attempting to unbind it.

m. Fixed several buffer overflows found as the result of fuzzing.

n. Reworked backslash handling when translating key sequences for key binding
   to be more uniform and consistent, which introduces a slight backwards
   incompatibility.

o. Fixed a bug with saving the history that resulted in errors not being
   propagated to the calling application when the history file is not writable.

p. Readline only calls chown(2) on a newly-written history file if it really
   needs to, instead of having it be a no-op.

q. Readline now behaves better when operate-and-get-next is used when the
   history list is `full': when there are already $HISTSIZE entries.

r. Fixed a bug that could cause vi redo (`.') of a replace command not to work
   correctly in the C or POSIX locale.

s. Fixed a bug with vi-mode digit arguments that caused the last command to be
   set incorrectly. This prevents yank-last-arg from working as intended, for
   example.

3. New Features in Bash

a. `bind -x' now supports different bindings for different editing modes and
   keymaps.

b. Bash attempts to optimize the number of times it forks when executing
   commands in subshells and from `bash -c'.

c. Here documents and here strings now use pipes for the expanded document if
   it's smaller than the pipe buffer size, reverting to temporary files if it's
   larger.

d. There are new loadable builtins: mktemp, accept, mkfifo, csv, cut/lcut

e. In posix mode, `trap -p' now displays signals whose disposition is SIG_DFL
   and those that were SIG_IGN when the shell starts.

f. The shell now expands the history number (e.g., in PS1) even if it is not
   currently saving commands to the history list.

g. `read -e' may now be used with arbitrary file descriptors (`read -u N').

h. The `select' builtin now runs traps if its internal call to the read builtin
   is interrupted by a signal.

i. SRANDOM: a new variable that expands to a 32-bit random number that is not
   produced by an LCRNG, and uses getrandom/getentropy, falling back to
   /dev/urandom or arc4random if available. There is a fallback generator if
   none of these are available.

j. shell-transpose-words: a new bindable readline command that uses the same
   definition of word as shell-forward-word, etc.

k. The shell now adds default bindings for shell-forward-word,
   shell-backward-word, shell-transpose-words, and shell-kill-word.

l. Bash now allows ARGV0 appearing in the initial shell environment to set $0.

m. If `unset' is executed without option arguments, bash tries to unset a shell
   function if a name argument cannot be a shell variable name because it's not
   an identifier.

n. The `test -N' operator uses nanosecond timestamp granularity if it's
   available.

o. Bash posix mode now treats assignment statements preceding shell function
   definitions the same as in its default mode, since POSIX has changed and
   no longer requires those assignments to persist after the function returns
   (POSIX interp 654).

p. BASH_REMATCH is no longer readonly.

q. wait: has a new -p VARNAME option, which stores the PID returned by `wait -n'
   or `wait' without arguments.

r. Sorting the results of pathname expansion now uses byte-by-byte comparisons
   if two strings collate equally to impose a total order; the result of a
   POSIX interpretation (#963 and #1070).

s. Bash now allows SIGINT trap handlers to execute recursively.

t. Bash now saves and restores state around setting and unsetting posix mode,
   instead of having unsetting posix mode set a known state.

u. Process substitution is now available in posix mode.

v. READLINE_MARK: a new variable available while executing commands bound with
   `bind -x', contains the value of the mark.

w. Bash removes SIGCHLD from the set of blocked signals if it's blocked at shell
   startup.

x. `test -v N' can now test whether or not positional parameter N is set.

y. `local' now honors the `-p' option to display all local variables at the
    current context.

z. The `@a' variable transformation now prints attributes for unset array
   variables.

aa. The `@A' variable transformation now prints a declare command that sets a
    variable's attributes if the variable has attributes but is unset.

bb. `declare' and `local' now have a -I option that inherits attributes and
    value from a variable with the same name at a previous scope.

cc. When run from a -c command, `jobs' now reports the status of completed jobs.

dd. New `U', `u', and `L' parameter transformations to convert to uppercase,
    convert first character to uppercase, and convert to lowercase,
    respectively.

ee. PROMPT_COMMAND: can now be an  array variable, each element of which can
    contain a command to be executed like a string PROMPT_COMMAND variable.

ff. `ulimit' has a -R option to report and set the RLIMIT_RTTIME resource.

gg. Associative arrays may be assigned using a list of key-value pairs within
    a compound assignment. Compound assignments where the words are not of
    the form [key]=value are assumed to be key-value assignments. A missing or
    empty key is an error; a missing value is treated as NULL. Assignments may
    not mix the two forms.

hh. New `K' parameter transformation to display associative arrays as key-
    value pairs.

ii. Writing history to syslog now handles messages longer than the syslog max
    length by writing multiple messages with a sequence number.

jj. SECONDS and RANDOM may now be assigned using arithmetic expressions, since
    they are nominally integer variables. LINENO is not an integer variable.

kk. Bash temporarily suppresses the verbose option when running the DEBUG trap
    while running a command from the `fc' builtin.

ll. `wait -n' now accepts a list of job specifications as arguments and will
    wait for the first one in the list to change state.

mm. The associative array implementation can now dynamically increase the
    size of the hash table based on insertion patterns.

nn. HISTFILE is now readonly in a restricted shell.

oo. The bash malloc now returns memory that is 16-byte aligned on 64-bit
    systems.

4. New Features in Readline

a. If a second consecutive completion attempt produces matches where the first
   did not, treat it as a new completion attempt and insert a match as
   appropriate.

b. Bracketed paste mode works in more places: incremental search strings, vi
   overstrike mode, character search, and reading numeric arguments.

c. Readline automatically switches to horizontal scrolling if the terminal has
   only one line.

d. Unbinding all key sequences bound to a particular readline function now
   descends into keymaps for multi-key sequences.

e. rl-clear-display: new bindable command that clears the screen and, if
   possible, the scrollback buffer (bound to emacs mode M-C-l by default).

f. New active mark and face feature: when enabled, it will highlight the text
   inserted by a bracketed paste (the `active region') and the text found by
   incremental and non-incremental history searches. This is tied to bracketed
   paste and can be disabled by turning off bracketed paste.

g. Readline sets the mark in several additional commands.

h. Bracketed paste mode is enabled by default.

i. Readline tries to take advantage of the more regular structure of UTF-8
   characters to identify the beginning and end of characters when moving
   through the line buffer.

j. The bindable operate-and-get-next command (and its default bindings) are
   now part of readline instead of a bash-specific addition.

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.0-release, and
the previous version, bash-5.0-rc1.

1. Changes to Bash

a. Tilde expansion isn't performed on indexed array subscripts, even for
   backwards compatibility.

b. The shell doesn't exit in posix mode if the eval builtin gets a parse
   error when run by the command builtin.

c. Fixed a bug that caused a shell comment in an alias to not find the end
   of the alias properly.

d. Reverted a change from April, 2018 that caused strings containing
   backslashes to be flagged as glob patterns.

2. Changes to Readline

3. New Features in Bash

4. New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.0-rc1, and
the previous version, bash-5.0-beta2.

1. Changes to Bash

a. Fix to initial word completion detection code.

b. Fixed a bug that caused issues with assignment statements containing ^A in
   the value assigned when IFS contains ^A.

c. Added a fallback to fnmatch() when strcoll can't correctly deal with
   bracket expression character equivalence classes.

d. Fixed a bug that caused $BASH_COMMAND to contain the trap handler command
   when running a trap handler containing [[ or (( commands.

e. Fixed a bug that caused nameref assignments in the temporary environment
   to potentially create variables with invalid names.

f. Fixed a bug that caused `local -' to turn off alias expansion in scripts.

g. Fixed a parser issue with a command string containing EOF after an invalid
   command as an argument to a special builtin not causing a posix-mode shell
   to exit.

h. Made a slight change to the FNV-1 string hash algorithm used for associative
   arrays (corrected the initial seed).

2. Changes to Readline

3. New Features in Bash

a. The `select' command now supports command forms without a word list
   following `in'.

4. New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.0-beta2, and
the previous version, bash-5.0-beta.

1. Changes to Bash

a. Fixed a bug that could cause a seg fault while parsing a subshell command
   inside a command substitution.

b. Fixed several small memory leaks uncovered by coverity.

c. Fixed a problem with command substitution inside an interactive shell that
   could cause the parent to receive a SIGHUP.

d. Fixed a problem with using `*' and `@' as subscripts when assigning values
   to an associative array with assoc_expand_once enabled.

e. Fixed a bug that could cause a huge memory allocation when completing a
   word beginning with an invalid tilde expansion.

f. Cleaned up some incompatibilities with bash-4.4 when expanding indexed array
   subscripts used in arithmetic expansions when assoc_expand_once is enabled.

g. The ${parameter@a} expansion will display attributes even if `parameter' is
   unset.

h. Fixed a bug that caused the output of `set' to cut off some variables before
   printing the value.

i. Treat a failure to assign a variable when using the ${x:=value} expansion
   as an expansion error, so non-interactive posix-mode shells exit

j. Fixed a problem when expanding $* in a context where word splitting is not
   performed when IFS is NULL.

k. Temp files used to store here documents are forced readable, no matter what
   the user's umask says.

l. Fixed a problem where an interrupted brace expansion could cause the shell
   to attempt to free an invalid memory location.

m. Make sure to check for any terminating signals after running a trap
   handler; don't wait until the next time we process traps.

n. Fixed a bug that caused "return" to act like a special builtin with respect
   to variable assignments even when preceded by "command".

o. POSIX-mode shells now return failure if the cd builtin fails due to the
   absolute directory name being longer than PATH_MAX, instead of trying
   again with a relative pathname.

p. Fixed a problem with FUNCNAME occasionally being visible when not executing
   a shell function.

q. Fixed a problem with the expansions performed on the WORD in the case
   command.

r. Fixed a slight POSIX compatibility when removing "IFS whitespace" during
   word splitting and the read builtin.

s. Fixed a problem with expanding an array with subscript `*' when all the
   elements expand to the empty string, and making sure the expansion honors
   the `:' specifier.

2. Changes to Readline

a. Fixed a bug with adding multibyte characters to an incremental search string.

b. Fixed a bug with redoing text insertions in vi mode.

c. Fixed a bug with pasting text into an incremental search string if bracketed
   paste mode is enabled. ESC cannot be one of the incremental search
   terminator characters for this to work.

d. Fixed a bug with anchored search patterns when performing searches in vi
   mode.

3. New Features in Bash

a. Associative and indexed arrays now allow subscripts consisting solely of
   whitespace.

b. `checkwinsize' is now enabled by default.

c. The `localvar_unset' shopt option is now visible and documented.

d. The `progcomp_alias' shopt option is now visible and documented.

e. The signal name processing code now understands `SIGRTMIN+n' all the way
   up to SIGRTMAX.

f. There is a new `seq' loadable builtin.

g. Trap execution now honors the (internal) max invocations of `eval', since
   traps are supposed to be executed as if using `eval'.

h. The $_ variable doesn't change when the shell executes a command that forks.

i. The `kill' builtin now supports -sSIGNAME and -nSIGNUM, even though
   conforming applications aren't supposed to use them.

j. POSIX mode now enables the `shift_verbose' option.

4. New Features in Readline

a. Readline now allows application-defined keymap names; there is a new public
   function, rl_set_keymap_name(), to do that.

b. The "Insert" keypad key, if available, now puts readline into overwrite
   mode.

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.0-beta, and
the previous version, bash-5.0-alpha.

1.  Changes to Bash

a. Fixed a bug that allowed subshells to "inherit" enclosing loops -- this
   is where POSIX says the subshell is not "enclosed" by the loop.

b. Added more UTF-8-specific versions of multibyte functions, and optimized
   existing functions if the current locale uses UTF-8 encoding.

c. In POSIX mode, assignments preceding regular builtins should not persist
   when the builtin completes.

d. Added additional checks to special array assignment (e.g., BASH_ALIASES)
   so it can't be used to bypass validity checks performed in other places.

e. The `!!' history expansion now refers to the previous history entry as
   expected, even if used on the second or subsequent line of a multi-line
   history entry.

f. Fixed a bug that could cause the shell to dereference a NULL pointer if
   the environment (`environ') is set to NULL.

g. Bash uses slightly better integer overflow handling for brace sequence
   expansion on systems where ints are 32 bits and intmax_t is 64 bits.

h. Fixed a bug setting attributes for a variable named as an argument to
   `declare' that also appears as a nameref in the temporary environment.

i. Fixed several bugs that could cause assignments to namerefs to create
   variables with invalid names.

j. Fixed a bug that could result in the SIGINT handler being set incorrectly
   in asynchronous subshells.

k. Fixed a bug that could cause `bash -t' to not execute the specified command.

l. Fixed several bugs that caused the shell to operate on the wrong variable
   when using namerefs with the same name as a global variable in shell
   functions.

m. Internal changes to how the shell handles variables with invalid names in
   the initial environment and to prevent variables with invalid names from
   being added to the environment instead of passing them on to children.

n. Changes to make sure that an expansion that results in a quoted null string
   is reflected in the expansion, even if the word expands to nothing.

o. Changes to make sure that $* and ${array[*]} (and $@/${array[@]}) expand
   the same way after the recent changes for POSIX interpretation 888.

p. Saving and restoring the positional parameters at function entry and exit
   is considerably more efficient; noticeably so when there are large numbers
   of positional parameters.

q. Fixed a bug that caused `lastpipe' and `pipefail' to return an incorrect
   status for the pipeline if there was more than one external command in a
   loop body appearing in the last pipeline element.

r. Fixed a bug that caused value conversion errors with the printf builtin's
   %u and %f conversion specifications and invalid constants.

2.  Changes to Readline

a. Added more UTF-8-specific versions of multibyte functions, and optimized
   existing functions if the current locale uses UTF-8 encoding.

b. Fixed a problem with bracketed-paste inserting more than one character and
   interacting with other readline functions.

c. Fixed a bug that caused the history library to attempt to append a history
   line to a non-existent history entry.

d. If using bracketed paste mode, output a newline after the \r that is the
   last character of the mode disable string to avoid overwriting output.

e. Fixes to the vi-mode `b', `B', `w', `W', `e', and `E' commands to better
   handle multibyte characters.

f. Fixed a redisplay problem that caused an extra newline to be generated on
   accept-line when the line length is exactly the screenwidth.

3.  New Features in Bash

a. Bash no longer allows variable assignments preceding a special builtin that
   changes variable attributes to propagate back to the calling environment
   unless the compatibility level is 44 or lower.

b. You can set the default value for $HISTSIZE at build time in config-top.h.

c. The `complete' builtin now accepts a -I option that applies the completion
   to the initial word on the line.

d. The internal bash malloc now uses mmap (if available) to satisfy requests
   greater than 128K bytes, so free can use mfree to return the pages to the
   kernel.

e. The shell doesn't automatically set BASH_ARGC and BASH_ARGV at startup
   unless it's in debugging mode, as the documentation has always said, but
   will dynamically create them if a script references them at the top level
   without having enabled debugging mode.

f. The localvar_inherit option will not attempt to inherit a value from a
   variable of an incompatible type (indexed vs. associative arrays, for
   example).

g. The `globasciiranges' option is now enabled by default; it can be set to
   off by default at configuration time.

4.  New Features in Readline

a. The history expansion library now understands command and process
   substitution and extended globbing and allows them to appear anywhere in a
   word.

b. The history library has a new variable that allows applications to set the
   initial quoting state, so quoting state can be inherited from a previous
   line.

------------------------------------------------------------------------------
This document details the changes between this version, bash-5.0-alpha, and
the previous version, bash-4.4-release.

1.  Changes to Bash

a. Fixed a bug that could cause traps in background jobs to give the terminal
   to the wrong process group.

b. Fixed a bug that caused `kill -l 0' to print an out-of-range error.

c. Fixed a problem that could result in here-documents being displayed in
   the wrong order.

d. Fixed a number of out-of-bounds and free memory read errors found via
   fuzzing.

e. Fixed a subshell inheritance problem that could cause a subshell to wait for
   the wrong process.

f. Fixed a bug that caused SHLVL to be incremented one too many times when
   creating subshells.

g. A job dying due to SIGINT can now interrupt sourcing a file in a shell with
   job control enabled.

h. Fixed a spurious warning about unterminated ${ or $( constructs during
   word completion.

i. The shell no longer runs traps if a signal arrives while reading command
   substitution output.

j. Fixed an arithmetic expansion error that could allow ++var++ as valid
   syntax.

k. Fixed an error that allowed out-of-bounds references to the directory stack.

l. The shell does a better job of saving multi-line history entries with
   embedded comments.

m. Fixed a bug that could cause quoted bracket expressions in regular expression
   arguments to `[[' to not match correctly.

n. Fixed a bug that could cause an IFS character in a word to result in an
   extra '\001' character in the expansion.

o. A trailing backslash in a glob pattern can match a trailing backslash in the
   string.

p. Fixed a memory leak in the process creation code path when job control is
   enabled.

q. Fixed a bug that caused `printf' to output broken surrogate pairs for
   Japanese locales.

r. Fixed a bug that caused a SIGINT generated from `kill' in a dot script to
   kill an interactive shell.

s. Fixed a bug that could cause the `read' builtin to not fully read a
   multibyte character.

t. Fixed a bug that could cause identifiers to be evaluated in conditional
   arithmetic expressions even when evaluation is suppressed.

u. Fixed a bug that could result in command substitution, when executed in a
   context where word splitting is not performed, to leave a stray '\001'
   character in the string.

v. Fixed a bug that could cause history expansion to be disabled in a non-
   interactive shell even if `-o histexpand' is supplied at startup.

w. Fixed a bug that caused `read -N' to strip leading whitespace IFS characters.

x. Fixed a bug that caused spurious tilde expansion in arithmetic expressions.

y. If indirect expansion attempts to indirectly reference through an unset
   variable, report an error.

z. Added a guard to prevent the shell from looping while receiving an endless
   stream of SIGTTIN at shell startup.

aa. Fixed a bug with parsing here documents inside a command substitution when
    looking for the closing delimiter.

bb. Fixed a bug that caused printf to not quote all <blank> characters in the
    current locale when using the `%q' format specifier.

cc. Fixed a bug with bash's internal buffered I/O system that caused the input
    pointer to not be reset when read(2) returned an EOF.

dd. Bash now installs its SIGWINCH signal handler with SA_RESTART, so it will
    not interrupt open/read/write system calls.

ee. The ERR trap now reports line numbers more reliably.

ff. The shell no longer tries to manipulate the terminal process group if a
    command or process substitution is killed by SIGTERM when job control is
    enabled.

gg. Fixed a bug that caused extglob patterns to match filenames beginning with
    a period.

hh. File descriptors open for writing to here documents are no longer available
    to subshells.

ii. Make sure word completion doesn't perform command or process substitution.

jj. Fixed a bug with parsing $$'...' inside a command substitution.

kk. Fixed a bug that caused bash to remove backslash-newline pairs from the
    body of a here-document with a quoted delimiter inside a command
    substitution.

ll. Fixed a bug that could cause the shell to hang when adding a pid to the
    table of background process exit statuses.

mm. Fixed a bug that could cause 0x01 characters to be doubled in the output
    of process substitution.

nn. Restricted shells now clear the hash table before making the PATH variable
    read-only.

oo. There are a number of changes to the expansion of $* and $@ in contexts
    where word splitting does not occur (quoted and unquoted), with IFS set
    to NULL or a non-standard value, mostly to deal with the consequences of
    the behavior defined in Posix interpretation 888.

pp. There are a number of changes to nameref variable handling to avoid
    creating variables with invalid names.

qq. A non-interactive posix mode shell no longer exits when an assignment
    statement fails if the assignment is utimately being performed by the
    `command' builtin.

rr. When using character class names for globbing, don't allow case
    insensitivity, even if nocaseglob is enabled.

ss. Fixed a bug that allowed some redirections to stay in place if a later
    redirection failed.

tt. Fixed a bug in how command and process substitutions are recognized within
    other parameter expansions.

uu. Fixed a bug that caused bash to loop under certain circumstances when
    performing arithmetic expansion on a variable whose value is an invalid
    expression.

vv. Fixed a bug that could cause bash to expand aliases inappropriately while
    parsing compound commands like `case'.

ww. Fixed a bug that could cause `read -N' to fail to read complete multibyte
    characters, even when the sequences are incomplete or invalid, with or
    without readline.

xx. Fixed a bug that could cause `case' to fail to match patterns containing
    0x01 characters.

yy. Fixed a bug that caused exported functions to contain stray 0x01 characters.

zz. Fixed some inconsistencies with how the history number is handled in the
    various prompt strings.

aaa. Fixed a bug that could cause a core dump if READLINE_LINE was unset
     inside a shell function bound to a key sequence with `bind -x'.

bbb. Fixed a bug that could cause bash to not read a token terminator correctly
     if a command substitution was used inside an arithmetic `for' command.

ccc. Fixed problems that could occur with a fatal arithmetic expansion error
     in a context (like prompt expansion) where you can't jump back to the
     top level.

ddd. Expression errors in arithmetic `for' commands are treated more like
     shell syntax errors.

eee. Fixed a parser synchronization error resulting from a syntax error
     followed immediately by an EOF.

fff. When executing a shell function, the first line in the function ($LINENO)
     is line 1 instead of line 0, as Posix requires.

ggg. In Posix mode, bash will canonicalize the value of PWD it inherits from
     the environment and use that to set its idea of the current directory.

hhh. If LINENO is exported, bash needs to regenerate its value each time it
     constructs the environment.

iii. Fixed a bug with restoring the SIGINT handler when using `wait -n'.

jjj. Make sure the `coproc' command returns an appropriate status if the NAME
     argument is invalid.

kkk. Fixed a problem with arithmetic expressions containing array references
     that contain arithmetic expressions with syntax errors.

lll. The `select' command and help builtin will use $COLUMNS before the window
     size returned from the kernel as the terminal width.

mmm. `read -n 0' and `read -N 0' now try a zero-length read in an attempt to
     detect file descriptor errors.

nnn. The `read' builtin now does a better job of acting on signals that don't
     interrupt read(2).

ooo. Fixed some cases where `printf -v' did not return failure status on a
     variable assignment error.

ppp. Fixed temporary environment propagation back to the current environment
     so that it doesn't happen for special builtins run by the `command'
     builtin.

qqq. Fixed a bug when searching for the end of a here-document delimiter in a
     command substitution.

rrr. Fixed a bug that could cause `cd ${DIRSTACK[0]}' to fail.

sss. Fixed a bug that could cause reserved words to not be recognized in a
     for statement without the `in' inside a command substitution.

ttt. Fixed a bug that could cause a double-free in a timed command with an
     expansion error.

uuu. Fixed a bug that could cause a core dump if a script switches from a UTF-8
     locale to a different locale after displaying a lone surrogate character.

vvv. Fixed cases where bash prematurely removed FIFOs attached to process
     substitutions.

www. Fixed a problem with calculating the size of the table that stores exit
     statuses from background processes when the child process resource limit
     is very large.

xxx. Fixed a memory leak with functions using `return' when using FIFOs for
     standard input.

yyy. `wait' without arguments attempts to wait for all active process
     substitution processes.

zzz. Fixed a bug where an indirect parameter was subjected to word splitting
     when trying to find the indirected variable name.

aaaa. Fixed a bug that could allow restricted shell users to add commands to
      the hash table.

bbbb. When using the `!(patlist)' extended globbing operator, a filename
      beginning with a `.' that doesn't match any of the patterns is not
      returned as a match if leading dots must be matched explicitly.

cccc. Fixed a bug that could cause line number and source file information for
      a function definition to be incorrect if there are multiple definitions.

dddd. Fixed a bug that could cause builtins like `readonly' to behave
      differently when applied to arrays and scalar variables within functions.

eeee. Fixed a bug that could cause alias expansion to add an extra space to
      a quoted string that begins outside the alias expansion.

ffff. Fixed a bug that could result in unwanted alias expansion after timing
      the null command.

gggg. Fixed a bug that could cause a core dump if a timestamp in a history
      file overflowed a time_t.

hhhh. Restricted shells can no longer redirect from /dev/tcp or /dev/udp, since
      the kernel calls make those file descriptors read-write.

iiii. Fixed a problem with splitting double-quoted words for programmable
      completion when the double quote immediately follows another word
      delimiter.

jjjj. Fixed a bug resulting in a use-after-free if two file descriptors share
      the same input buffer.

kkkk. The error message resulting from ${x:?} and ${x?} now differs depending
      on whether the variable is null or unset.

llll. In Posix mode, the shell exits if a variable assignment fails and precedes
      an empty simple command (after expansion).

mmmm. Fixed a timing problem with SIGALRM that could cause the read builtin to
      drop characters.

nnnn. Added code to deal with kill(2) failing to send the shell a fatal signal
      due to Linux pid namespace peculiarities.

oooo. Fixed a bug that made \C-@ (NUL) unusable in key sequences used for
      `bind -x' commands.

pppp. Fixed a bug that could cause SIGINT recursion when running an external
      command in a trap the shell takes after a command exits due to SIGINT.

qqqq. Make sure the shell turns off job control before running the command-
      not-found handle, so the command doesn't try to manipulate process
      groups.

rrrr. Fixed a problem with timing process substitutions that caused the shell
      to print timing information for the calling command.

ssss. Fixed a bug that caused backquotes in a here-document delimiter to mark
      the delimiter as quoted (inhibiting expansion of the here-document
      contents).

tttt. Fixed several problems with 0x01 and 0x177 in case pattern lists and
      conditional command pattern matches.

uuuu. Fixed a bug that could cause the pattern matching engine to not recognize
      locale-specific character classes.

vvvv. The auto-configuration now tests for /dev/stdin and /dev/fd independently.

wwww. The `globstar' code now skips over symbolic links to directories,
      preventing them from being scanned twice.

xxxx. When running `bind -x' commands, bash now sets READLINE_POINT based on
      the number of characters in the readline line buffer, not the number of
      bytes.

yyyy. Fixed a problem that could cause recursive trap evaluation of the RETURN
      trap when using `eval return'.

zzzz. Fixed a bug with expanding 0x01 in an unquoted here-document.

aaaaa. The process substitution code now closes and unlinks FIFOs when the
       process on the other side exits, in order to prevent SIGPIPE or
       waiting until a FIFO opened for read has a writer.

bbbbb. Fixed a bug with recursive calls to the parser overwriting the token in
       an {id}>foo construct.

ccccc. After a Posix discussion, the pattern matching engine just skips over
       invalid character classes in bracket expressions, instead of matching
       them like individual characters in the expression.

ddddd. Fixed a posix-mode problem with variable scoping when creating variables
       from assignment statements preceding special builtins.

eeeee. Fixed a bug that could cause patterns containing backslashes to not be
       run through the pattern matching engine.

fffff. Fixed a bug that could cause redirections to compound commands to not
       be `undone' if the file descriptor in the redirection was closed when
       the redirection was initially processed.

ggggg. Fixed a bug that could cause buffer corruption when using `bind -x' in
       a command execute as a result of a key binding installed by `bind -x'.

2.  Changes to Readline

a. Added a guard to prevent nested macros from causing an infinite expansion
   loop.

b. Instead of allocating enough history list entries to hold the maximum list
   size, cap the number allocated initially.

c. Added a strategy to avoid allocating huge amounts of memory if a block of
   history entries without timestamps occurs after a block with timestamps.

d. Added support for keyboard timeouts when an ESC character is the last
   character in a macro.

e. There are several performance improvements when in a UTF-8 locale.

f. Readline does a better job of preserving the original set of blocked
   signals when using pselect() to wait for input.

g. Fixed a bug that caused multibyte characters in macros to be mishandled.

h. Fixed several bugs in the code that calculates line breaks when expanding
   prompts that span several lines, contain multibyte characters, and contain
   invisible character seqeuences.

i. Fixed several bugs in cursor positioning when displaying lines with prompts
   containing invisible characters and multibyte characters.

j. When performing case-insensitive completion, Readline no longer sorts the
   list of matches unless directed to do so.

k. Fixed a problem with key sequences ending with a backslash.

l. Fixed out-of-bounds and free memory read errors found via fuzzing.

m. Fixed several cases where the mark was set to an invalid value.

n. Fixed a problem with the case-changing operators in the case where the
   lower and upper case versions of a character do not have the same number
   of bytes.

o. Handle incremental and non-incremental search character reads returning EOF.

p. Handle the case where a failing readline command at the end of a multi-key
   sequence could be misinterpreted.

q. The history library now prints a meaningful error message if the history
   file isn't a regular file.

r. Fixed a problem with vi-mode redo (`.') on a command when trying to replace
   a multibyte character.

s. The key binding code now attempts to remove a keymap if a key unbinding
   leaves it empty.

t. Fixed a line-wrapping issue that caused problems for some terminal
   emulators.

u. If there is a key bound to the tty's VDISCARD special character, readline
   disables VDISCARD while it is active.

v. Fixed a problem with exiting bracketed paste mode on terminals that assume
   the bracketed paste mode character sequence contains visible characters.

w. Fixed a bug that could cause a key binding command to refer to an
   uninitialized variable.

3.  New Features in Bash

a. The `wait' builtin can now wait for the last process substitution created.

b. There is an EPOCHSECONDS variable, which expands to the time in seconds
   since the Unix epoch.

c. There is an EPOCHREALTIME variable, which expands to the time in seconds
   since the Unix epoch with microsecond granularity.

d. New loadable builtins: rm, stat, fdflags.

e. BASH_ARGV0: a new variable that expands to $0 and sets $0 on assignment.

f. When supplied a numeric argument, the shell-expand-line bindable readline
   command does not perform quote removal and suppresses command and process
   substitution.

g. `history -d' understands negative arguments: negative arguments offset from
   the end of the history list.

h. The `name' argument to the `coproc' reserved word now undergoes word
   expansion, so unique coprocs can be created in loops.

i. A nameref name resolution loop in a function now resolves to a variable by
   that name in the global scope.

j. The `wait' builtin now has a `-f' option, which signifies to wait until the
   specified job or process terminates, instead of waiting until it changes
   state.

k. There is a define in config-top.h that allows the shell to use a static
   value for $PATH, overriding whatever is in the environment at startup, for
   use by the restricted shell.

l. Process substitution does not inherit the `v' option, like command
   substitution.

m. If a non-interactive shell with job control enabled detects that a foreground
   job died due to SIGINT, it acts as if it received the SIGINT.

n. The SIGCHLD trap is run once for each exiting child process even if job
   control is not enabled when the shell is in Posix mode.

o. A new shopt option: localvar_inherit; if set, a local variable inherits the
   value of a variable with the same name at the nearest preceding scope.

p. `bind -r' now checks whether a key sequence is bound before binding it to
   NULL, to avoid creating keymaps for a multi-key sequence.

q. A numeric argument to the line editing `operate-and-get-next' command
   specifies which history entry to use.

r. The positional parameters are now assigned before running the shell startup
   files, so startup files can use $@.

s. There is a compile-time option that forces the shell to disable the check
   for an inherited OLDPWD being a directory.

t. The `history' builtin can now delete ranges of history entries using
   `-d start-end'.

u. The `vi-edit-and-execute-command' bindable readline command now puts readline
   back in vi insertion mode after executing commands from the edited file.

v. The command completion code now matches aliases and shell function names
   case-insensitively if the readline completion-ignore-case variable is set.

w. There is a new `assoc_expand_once' shell option that attempts to expand
   associative array subscripts only once.

x. The shell only sets up BASH_ARGV and BASH_ARGC at startup if extended
   debugging mode is active. The old behavior of unconditionally setting them
   is available as part of the shell compatibility options.

y. The `umask' builtin now allows modes and masks greater than octal 777.

z. The `times' builtin now honors the current locale when printing a decimal
   point.

aa. There is a new (disabled by default, undocumented) shell option to enable
    and disable sending history to syslog at runtime.

4.  New Features in Readline

a. Non-incremental vi-mode search (`N', `n') can search for a shell pattern, as
   Posix specifies (uses fnmatch(3) if available).

b. There are new `next-screen-line' and `previous-screen-line' bindable
   commands, which move the cursor to the same column in the next, or previous,
   physical line, respectively.

c. There are default key bindings for control-arrow-key key combinations.

d. A negative argument (-N) to `quoted-insert' means to insert the next N
   characters using quoted-insert.

e. New public function: rl_check_signals(), which allows applications to
   respond to signals that readline catches while waiting for input using
   a custom read function.

f. There is new support for conditionally testing the readline version in an
   inputrc file, with a full set of arithmetic comparison operators available.

g. There is a simple variable comparison facility available for use within an
   inputrc file. Allowable operators are equality and inequality; string
   variables may be compared to a value; boolean variables must be compared to
   either `on' or `off'; variable names are separated from the operator by
   whitespace.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.4-release, and
the previous version, bash-4.4-rc2.

1.  Changes to Bash

a.  Fixed a bug that could potentially result in a crash due to an integer
    overflow.

b.  Fixed a bug in where commands printed due to `set -x' could be incorrectly
    quoted if being printed in contexts where they haven't yet been expanded.

c.  Fixed several memory leaks.

d.  Fixed a bug that could potentially cause the terminal attributes to be
    set incorrectly by a command run from a programmable completion.

e.  Fixed several potential buffer overflow issues in the word expansion code.

2.  Changes to Readline

3.  New Features in Bash

4.  New Features in Readline
------------------------------------------------------------------------------
This document details the changes between this version, bash-4.4-rc2, and
the previous version, bash-4.4-beta2.

1.  Changes to Bash

a.  Fixed an out-of-bounds read in the redirection operator completion code.

b.  Fixed execution context so `until continue' doesn't disable execution for
    subsequent commands.

c.  Fixed trap handling code so traps don't inherit a command's temporary
    environment.

d.  Fixed a bug that resulted in incorrect line numbers when a function is
    defined as part of another function's execution.

e.  Fixed a bug in the expansion of ${a[@]} in contexts where word splitting
    is not performed and $IFS is not the default.

f.  Fixed a bug that caused ''"$@" to not expand to an empty argument when
    there are no positional parameters.

g.  Fixed a bug that caused a shell compiled without job control to use the
    incorrect exit status for builtin commands preceded by a command executed
    from the file system that causes the shell to call waitpid().

h.  Improved word completion for quoted strings containing unterminated command
    substitutions with embedded double quotes.

2.  Changes to Readline

a.  Fixed a bug that caused mode strings to be displayed incorrectly if the
    prompt was shorter than the mode string.

3.  New Features in Bash

a.  Using ${a[@]} or ${a[*]} with an array without any assigned elements when
    the nounset option is enabled no longer throws an unbound variable error.

4.  New Features in Readline

a.  New application-callable function: rl_pending_signal(): returns the signal
    number of any signal readline has caught but not yet handled.

b.  New application-settable variable: rl_persistent_signal_handlers: if set
    to a non-zero value, readline will enable the readline-6.2 signal handler
    behavior in callback mode: handlers are installed when
    rl_callback_handler_install is called and removed removed when a complete
    line has been read.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.4-beta2, and
the previous version, bash-4.4-rc1.

1.  Changes to Bash

a.  Fixed a memory leak when processing ${!var[@]}.

b.  Fixed a bug that caused subshells to free trap strings associated with
    inherited signals.

c.  Inheriting BASHOPTS from the environment now works to force actions
    associated with enabling an option, instead of just marking the option
    as enabled.

d.  Fixed a bug that allowed assignments to BASH_CMDS when the shell was in
    restricted mode.

e.  Fixed a bug caused by an accidental omission of part of the original patch
    for EXECIGNORE.

e.  Prompt expansion now quotes the results of the \s, \h, and \H expansions.

f.  Fixed a bug that caused parsing errors in command substitutions with
    consecutive case statements separated by newlines.

g.  Updated logic used to decide whether bash is running inside an emacs
    terminal emulator to work with future emacs versions.

h.  Fixed two extended pattern matching bugs caused by premature short-
    circuiting.

i.  Fixed a memory leak in the code that removes duplicate history entries.

j.  There are a number of bug fixes to coproc, mapfile, declare, unset,
    and assignment statements that prevent nameref variables from creating
    and unsetting variables with invalid names.

k.  Fixed a bug that caused variables to be inadvertently marked as both an
    associative and an indexed array.

l.  Fixed a bug that caused `bash -c' to not run a trap specified in the
    command string.

j.  There are a number of bug fixes to coproc, mapfile, declare, and assignment
    statements that prevent nameref variables from overwriting or modifying
    attributes of readonly variables.

k.  Fixed a bug that caused command substitution to attempt to set the
    terminal's process group incorrectly.
    
l.  Fixed a bug that could cause prompt string expansion to display error
    messages when the `nounset' shell option is set.

m.  Fixed a bug that caused "$@" to not expand to an empty string under the
    circumstances when Posix says it should ("${@-${@-$@}}").

n.  Fixed several bugs caused by referencing nameref variables whose values
    are names of unset variables (or names that are valid for referencing
    but not assignment), including creating variables in the temporary
    environment.

o.  Function tracing and error tracing are disabled if --debugger is supplied
    at startup but the shell can't find the debugger start file.

p.  Fixed a bug when IFS is used as the control variable in a for statement.

q.  Fixed a bug with SIGINT received by a command substitution in an interactive
    shell.

r.  The checks for nameref variable self-references are more thorough.

s.  Fixed several bugs with multi-line aliases.

t.  Fixed `test' to handle the four-argument case where $1 == '(' and
    $4 == ')'.

u.  Fixed a bug in the expansion of $* in the cases where word splitting is
    not performed.

v.  Fixed a bug in execution of case statements where IFS includes the
    pattern matching characters.

2.  Changes to Readline

a.  When refreshing the line as the result of a key sequence, Readline attempts
    to redraw only the last line of a multiline prompt.

b.  Fixed an issue that caused completion of git commands to display
    incorrectly when using colored-completion-prefix.

c.  Fixed several redisplay bugs having to do with multibyte characters and
    invisible characters in prompt strings.

3.  New Features in Bash

a.  Value conversions (arithmetic expansions, case modification, etc.) now
    happen when assigning elements of an array using compound assignment.

b.  There is a new option settable in config-top.h that makes multiple
    directory arguments to `cd' a fatal error.

c.  Bash now uses mktemp() when creating internal temporary files; it produces
    a warning at build time on many Linux systems.

4.  New Features in Readline

a.  The default binding for ^W in vi mode now uses word boundaries specified
    by Posix (vi-unix-word-rubout is bindable command name).

b.  rl_clear_visible_line: new application-callable function; clears all
    screen lines occupied by the current visible readline line.

c.  rl_tty_set_echoing: application-callable function that controls whether
    or not readline thinks it is echoing terminal output.

d.  Handle >| and strings of digits preceding and following redirection
    specifications as single tokens when tokenizing the line for history
    expansion.

e.  Fixed a bug with displaying completions when the prefix display length
    is greater than the length of the completions to be displayed.

f.  The :p history modifier now applies to the entire line, so any expansion
    specifying :p causes the line to be printed instead of expanded.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.4-rc1, and
the previous version, bash-4.4-beta.

1.  Changes to Bash

a.  Fixed several problems with bash completion not special-casing bash syntax
    constructs.

b.  Fixed a bug that caused the mapfile builtin to not create array variables
    when a variable of the same name appears in the temporary environment.

c.  Fixed a bug that caused prompt expansion to loop when PS1 contained a
    syntax error.

d.  Fixed a bug that caused the ${array[@]@A} expansion to split the results
    even when double-quoted.

e.  There is a new implementation of the code that saves the last CHILD_MAX
    exited background pids so their status can be queried later.

f.  Bash-4.4 can now be configured and built on very old versions of Solaris 2.

g.  Fixed problems with --help support for several builtins.

h.  Fixed values added to BASH_SOURCE and BASH_LINENO for functions inherited
    from the environment.

i.  Fixed a bug that caused background processes run from non-interactive shells
    with job control enabled to place the terminal in the wrong process group
    under certain circumstances.

j.  Fixed a bug that caused `fc' to return an incorrect exit status when
    executing commands from the history list.

k.  Fixed a bug that caused the shell to exit when a process substitution
    received a SIGINT when run in certain terminal emulators.

l.  EXECIGNORE now honors the setting of `extglob' when attempting to match
    executable names.

m.  Fixed a bug where `return' used the wrong exit status when executed in a
    DEBUG trap.

n.  Fixed a bug that caused a command containing a here-document and an escaped
    newline to be stored in the history list incorrectly.

o.  Fixed a bug that caused set -e to be honored in cases of builtins invoking
    other builtins when it should be ignored.

p.  Fixed a bug that caused `readonly' and `export' to create local array
    variables when used within shell functions.

q.  Fixed a bug that allowed subshells begun to execute process substitutions
    to have access to the command's temporary environment.

r.  Fixed a bug that could cause the shell to dump core when receiving a
    SIGCHLD for which a trap has been set while running in posix mode.

s.  Fixed a bug that caused bash to not restore BASH_ARGC, BASH_ARGV,
    BASH_SOURCE, BASH_LINENO, and FUNCNAME if the shell received a SIGINT
    while reading commands from a file while executing  `.'.

t.  Fixed a bug that caused the `-o history' option to have no effect when
    supplied on the command line when starting a new shell.

u.  Fixed a bug that caused history expansions occurring in command
    substitutions to not be performed.

v.  Fixed a bug that caused `eval' run in a non-interactive shell to disable
    history expansion for the remainder of the shell script, even if the script
    had previously enabled it.

w.  Fixed a bug that caused "$@" to not expand to multiple words when IFS is set
    to the empty string.

x.  Fixed a bug that caused process and command substitution to inherit output
    buffered in the stdio library but not written.

y.  Fixed a bug that caused a terminating signal received during `echo' to run
    an exit trap in a signal handler context.

z.  Fixed a bug that caused a builtin command containing a process substitution
    to return the wrong exit status.

aa. Fixed a bug that caused `()' subshells with piped input to incorrectly
    redirect the standard input of some of the commands in the subshell from
    /dev/null.

bb. The history builtin now uses more descriptive error messages for missing or
    invalid timestamps.

2.  Changes to Readline

a.  The history file writing functions only attempt to create and use a backup
    history file if the history file exists and is a regular file.

b.  Fixed an out-of-bounds read in readline's internal tilde expansion
    interface.

c.  Fixed several redisplay bugs with prompt strings containing multibyte
    and non-visible characters whose physical length is longer than the screen
    width.

d.  Fixed a redisplay bug with prompt strings containing invisible characters
    whose physical length exceeds the screen width and using incremental search.

e.  Readline prints more descriptive error messages when it encounters errors
    while reading an inputrc file.

f.  Fixed a bug in the character insertion code that attempts to optimize
    typeahead when it reads a character that is not bound to self-insert and
    resets the key sequence state.

3.  New Features in Bash

a.  BASH_COMPAT and FUNCNEST can be inherited and set from the shell's initial
    environment.

b.  inherit_errexit: a new `shopt' option that, when set, causes command
    substitutions to inherit the -e option.  By default, those subshells disable
    -e.  It's enabled as part of turning on posix mode.

c.  New prompt string: PS0.  Expanded and displayed by interactive shells after
    reading a complete command but before executing it.

d.  Interactive shells now behave as if SIGTSTP/SIGTTIN/SIGTTOU are set to
    SIG_DFL when the shell is started, so they are set to SIG_DFL in child
    processes.

e.  Posix-mode shells now allow double quotes to quote the history expansion
    character.

f.  OLDPWD can be inherited from the environment if it names a directory.

g.  Shells running as root no longer inherit PS4 from the environment, closing
    a security hole involving PS4 expansion performing command substitution.

h.  If executing an implicit `cd' when the `autocd' option is set, bash will
    now invoke a function named `cd' if one exists before executing the `cd'
    builtin.

4.  New Features in Readline

a.  If an incremental search string has its last character removed with DEL,
    the resulting empty search string no longer matches the previous line.

b.  If readline reads a history file that begins with `#' (or the value of
    the history comment character) and has enabled history timestamps, the
    history entries are assumed to be delimited by timestamps.  This allows
    multi-line history entries.

c.  Readline now throws an error if it parses a key binding without a
    terminating `:' or whitespace.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.4-beta, and
the previous version, bash-4.4-alpha.

1.  Changes to Bash

a.  Fixed two bugs that caused out-of-bounds reads when skipping over assignment
    statements while finding the word on which to perform programmable
    completion.

b.  Fixed a memory leak in programmable completion.

c.  Fixed a bug that could cause the shell to crash when interrupting the
    wait builtin.

d.  Fixed a bug that caused ${!vvv@} to be interpreted as introducing the new
    `@' operator.

e.  Fixed a bug that caused the && and || operators to be incorrectly optimized.

f.  The shell now undoes redirections before exiting the shell when the `-e'
    option is enabled, and a shell function fails.

g.  History expansion now skips over the history expansion character in command
    and process substitution.

h.  Fixed a bug that caused stray '\001' characters to be added to the output
    of `declare -p'.

i.  Fixed a memory leak when processing declare commands that perform compound
    array assignments.

j.  Fixed a bug that could cause the shell to crash when reading input from a
    file and the limit on open file descriptors is high.

k.  Fixed a bug that caused the ERR and RETURN traps to be unset if they were
    set in a shell function but unset previously.

l.  Fixed a bug that caused several signals to be ignored if `exec' failed in
    an interactive shell.

m.  A posix-mode non-interactive shell now considers a parameter expansion error
    to be a fatal error.

n.  The `time' command now prints timing statistics for failed commands when
    the -e option is enabled.

o.  Fixed a bug that caused the shell to crash when attempting to indirectly
    expand a shell variable with an invalid name.

p.  Fixed a bug that caused the shell to crash when running a trap containing
    a process substitution.

q.  Bash now prints the keyword `function' before a function with the same name
    as a reserved word when using `declare -f' to avoid parse errors when
    reusing the output as input.

r.  Fixed a bug that caused the shell to crash when using declare -g to attempt
    to redefine an existing global indexed array variable as an associative
    array.

s.  Fixed a memory leak that occurred when interrupting brace expansions
    generating a sequence.

t.  Fixed a bug that resulted in alias expansion in redirections.

u.  The `declare -a' and `declare -A' commands now print fewer warnings when
    attempting to create and initialize an array at the same time, but
    relying on word expansions to construct the compound assignment.

v.  The `help' builtin now behaves better in locales where each wide
    character occupies more than one display column.

w.  The `read' builtin no longer has a possible race condition when a timeout
    occurs.

x.  Fixed several expansion problems encountered when IFS="'".

y.  Fixed a problem with the expansion of $'\c?'.

z.  Bash no longer splits the expansion of here-strings, as the documentation
    has always said.

aa. Bash now puts `s' in the value of $- if the shell is reading from standard
    input, as Posix requires.

bb. Fixed a bug that caused the shell to crash if invoked with a NULL
    environment.

cc. The shell now only trusts an inherited value for $PWD if it begins with a
    `/'.

dd. Fixed a memory leak when creating local array variables and assigning to
    them using compound assignment with the `declare' builtin.

ee. Fixed a bug that could cause the shell to crash when processing nested here
    documents inside a command substitution.

ff. Array keys and values are now displayed using $'...' quoting where
    appropriate.

gg. Fixed a bug that could cause the shell to crash if the replacement string
    in pattern substitution was NULL.

hh. Fixed a bug that could cause the shell to crash if a command substitution
    contained a non-fatal syntax error.

ii. Fixed a bug that could cause the shell to crash if variable indirection
    resulted in a NULL variable.

jj. Fixed a bug that could cause the shell to crash if a long string contained
    multiple unterminated parameter expansion constructs.

kk. Improved the code that acts on SIGINT received while waiting for a child
    process only if the child exits due to SIGINT.

ll. $BASH_SUBSHELL now has more consistent values in asynchronous simple
    commands.

2.  Changes to Readline

a.  Colored completion prefixes are now displayed using a different color, less
    likely to collide with files.

b.  Fixed a bug that caused vi-mode character search to misbehave when
    running in callback mode.

c.  Fixed a bug that caused output to be delayed when input is coming from a
    macro in vi-mode.

d.  Fixed a bug that caused the vi-mode `.' command to misbehave when redoing
    a multi-key key sequence via a macro.

e.  Fixed a bug that caused problems with applications that supply their own
    input function when performing completion.

f.  When read returns -1/EIO when attempting to read a key, return an error
    instead of line termination back to the caller.

g.  Updated tty auditing feature based on patch from Red Hat.

h.  Fixed a bug that could cause the history library to crash on overflows
    introduced by malicious editing of timestamps in the history file.

3.  New Features in Bash

a.  `make install' now installs the example loadable builtins and a set of
    bash headers to use when developing new loadable builtins.

b.  `enable -f' now attempts to call functions named BUILTIN_builtin_load when
    loading BUILTIN, and BUILTIN_builtin_unload when deleting it.  This allows
    loadable builtins to run initialization and cleanup code.

c.  There is a new BASH_LOADABLES_PATH variable containing a list of directories
    where the `enable -f' command looks for shared objects containing loadable
    builtins.

d.  The `complete_fullquote' option to `shopt' changes filename completion to
    quote all shell metacharacters in filenames and directory names.

e.  The `kill' builtin now has a `-L' option, equivalent to `-l', for
    compatibility with Linux standalone versions of kill.

4.  New Features in Readline
------------------------------------------------------------------------------
This document details the changes between this version, bash-4.4-alpha, and
the previous version, bash-4.3-release.

1.  Changes to Bash

a.  A bug that caused pipelines to be corrupted while running the DEBUG trap
    was fixed.

b.  A bug that accidentally omitted the `-R' unary operator from `test' was
    fixed.

c.  A bug that could cause the shell to not restore the terminal's process
    group on shell exit was fixed.

d.  Several changes were made to programmable completion to accommodate
    assumptions made by the bash-completion package.

e.  Bash no longer inhibits C-style escape processing ($'...') while performing
    pattern substitution word expansions.

f.  Fixed a bug that caused `return' executed from a trap handler to use the
    wrong return status when one was not supplied as an argument.

g.  In Posix mode, defining a function with the same name as a special
    builtin is now an error, fatal only when the shell is not interactive.

h.  Fixed a bug that caused compound array assignments to discard unset or null
    variables used as subscripts, thereby making it appear as if the index was
    not present.

i.  Fixed a bug that caused extended glob patterns to incorrectly match
    filenames with a leading `.'.

j.  Fixed a bug involving sign extension when reallocating the input line
    after a history expansion, causing segmentation faults.

k.  Bash now does a better job at identifying syntax errors during word
    completion and tailoring completion appropriately.

l.  Bash now uses the current locale's decimal point in command timing output.

m.  Fixed a bug that caused segmentation faults while reading here documents if
    PS2 contains a command substitution.

n.  There are several changes to how $@ is expanded when unquoted but in a
    context where word splitting is not performed (e.g., on the rhs of an
    assignment or in a conditional command).

o.  Bash now quotes command hash table entries that contain shell metacharacters
    when displaying hash table contents.

p.  Fixed a potential file descriptor leak when dup2() fails while performing a
    redirection.

q.  Fixed a bug that caused directory names evaluated during word completion to
    be dequoted twice.

r.  Fixed several bugs which could result in indirect variable expansion and
    namerefs creating variables with invalid names or referencing variables
    set to the empty string.

s.  Fixed a bug that caused bash to not expand $0 in word expansions where it
    should.

t.  Fixed a bug that caused bash to perform process substitution if <(
    appeared inside an arithmetic context.

u.  Fixed a bug in extglob pattern parsing that caused slashes in the pattern
    to be confused as directory names.

v.  Fixed several bugs with treatment of invisible variables (variables with
    attributes that are unset because they have never been assigned values).

w.  Fixed a bug that caused the `read' builtin to not clean up readline's
    state when using the -e and -t options together and the read timed out.

x.  Fixed a bug that caused the shell to exit with the wrong (but non-zero)
    value if a command was not found or was not executable.

y.  Fixed a bug that caused the `time' reserved word to not be recognized as
    such in all contexts where it should have been.

z.  Fixed a bug that caused the shell to close process substitution file
    descriptors when executing a script without the `#!' leading line.

aa. Fixed a typo that caused the `compat42' shell option to set the wrong
    compatibility level.

bb. The shell now handles process substitution commands with embedded
    parentheses the same way as it does when parsing command substitution.

cc. Fixed a bug that caused nested pipelines and the `lastpipe' shell option
    to produce core dumps.

dd. Fixed a bug that caused patterns containing `*' to match pathnames in cases
    where slashes must be matched explicitly.

ee. Fixed a problem with patterns containing `:' in colon-separated variables
    like GLOBIGNORE. 

ff. Fixed a bug that caused indirect variable expansion using indexed arrays to
    always use index 0.

gg. Fixed a parsing problem that caused quoted newlines immediately following a
    command substitution to be mishandled in certain cases.

hh. Fixed a potential buffer overflow on systems without locale_charset or the
    bash replacement.

ii. Fixed a bug that caused background processes to modify the terminal's
    process group under certain circumstances.

jj. Asynchronous commands now always set $? to 0 and are not affected by
    whether or not the command's exit status is being inverted.

kk. Fixed a problem that caused a line ending with an escaped newline and
    containingh a prior `eval' to be incorrectly parsed.

ll. Fixed an issue with programmable completion and `!' in extglob patterns
    used as arguments to `compgen -X'.

mm. Word completion now treats the two-character token `>|' as requiring
    filename expansion.

nn. Bash no longer expands tildes in $PATH elements while in Posix mode.

oo. Fixed a bug that caused bash to not clean up readline's state, including
    the terminal settings, if it received a fatal signal while in a readline()
    call (including `read -e' and `read -s').

pp. Fixed bug that caused importing shell functions from the environment to
    execute additional commands following the function.

qq. Fixed a bug that caused the parser to return a lookahead character pushed
    back by a previous call, even when on another line.

rr. Fixed a bug that caused many here-documents or many nested case statements
    to overflow an internal stack.

ss. Changed the way bash encodes exported functions for inclusion in the
    environment to avoid name collisions with valid variable names and to
    indicate that they are exported functions.

tt. Fixed a bug that could result in an invalid memory access when processing
    a here document delimited by end of file or end of string.

uu. Fixed a bug that could cause an invalid memory access if a command was run
    before initializing the job control framework.

vv. When in Posix mode, bash prints shell function definitions as Posix
    specifies them, without the leading `function' keyword.

ww. The variable attribute display builtins no longer display variables with
    invalid names that were imported from the environment.

xx. Fixed a bug that could allow `break' or `continue' executed from shell
    functions to affect loops running outside of the function.

yy. Fixed a bug that could cause a restricted shell to attempt to import shell
    functions from the environment.

zz. The shell now allows double-quoted identifiers in arithmetic expressions.

aaa. Fixed a bug that could allow scalar variables subscripted using [@] in
     word expansions to be incorrectly quoted.

bbb. The shell now makes sure to ignore SIGTSTP/SIGTTIN/SIGTTOU in child
     processes if they were ignored at shell startup, even if job control is
     in effect.

ccc. Fixed a bug that could cause $* to be split on spaces when IFS is set to
     the empty string.

ddd. Posix says that expanding $* in a pattern context where the expansion is
     double-quoted should not treat $* as if it were double quoted.

eee. Bash now restores getopts' internal state between calls to getopts even if
     a shell function declares a local copy of OPTIND.

fff. Fixed a bug that could cause `history -r' or `history -n' to read identical
     lines from the history file more than once.

ggg. The commands executed by `bind -x' now redisplay at most the final line
     of a multi-line prompt, and those commands may return 124 to indicate that
     the entire prompt should be redrawn.

hhh. Fixed a bug that could cause `mapfile' to create variables with invalid
     names.

iii. The shell only goes into debugging mode when --debugger is supplied if
     the debugger initialization file is present.

jjj. Fixed a bug that disallowed an assignment to (implicit) subscript 0 of an
     existing array in a declare command that set the readonly attribute.

kkk. Fixed a bug that inadvertently allowed assignments to FUNCNAME to disable
     its special status.

lll. Appending to an existing array variable using the compound assignment
     syntax (var+=(aaa)) should not affect assignments to existing subscripts
     appearing in the compound assignment.

mmm. Fixed a bug that could cause the shell to crash when a variable with a
     null value was passed in the temporary environment and the variable's
     attributes are modified using `declare' while performing a redirection.

nnn. Fixed a bug in printf so that a missing precision specifier after a `.'
     is treated as 0.

ooo. Fixed a bug that attempted to use the internal command timing to time
     pipeline components if the pipeline elements are separated by newlines.

ppp. Fixed a bug that caused `declare -al foo=(ONE TWO THREE)' to not lowercase
     the values on assignment.

qqq. Bash does a better job of determining whether or not files are executable
     when running on Windows, since the X_OK flag to access(2) is not supported.

rrr. Fixed a bug that caused some of the shell's internal traps (e.g., ERR) to
     be interrupted (and leave incorrect state) by pending SIGINTs.

sss. Fixed a bug in the bash interface to history expansion to avoid attempting
     expansion if the history expansion character occurs in a command
     substitution.

ttt. Fixed a bug that caused the select command to crash if the REPLY variable
     ends up empty (e.g., if it's made readonly)

uuu. Bash handles backslash-quoting of multibyte characters better when quoting
     output that is intended to be reused.

vvv. System-specific changes for: Windows, Cygwin.

www. Fixes for upper and lower-casing multibyte characters, some locales have
     characters whose upper and lowercase versions have different numbers of
     bytes.

xxx. Fixed a bug that caused the ERR trap in a shell function to have the
     wrong value for $LINENO.

yyy. Fixed a bug that resulted in incorrect quoting of regexps for the =~
     operator when an open brace appears without a close brace.

zzz. Fixed a bug in the array unset operation that caused it to attempt to
     parse embedded single and double quotes.

aaaa. Fixed a bug that caused $* to not expand with the first character of
      $IFS as a separator in a context where word splitting will not take
      place.

bbbb. Fixed two bugs that could cause the shell to dereference a null pointer
      while attempting to print an error message from arithmetic expansion.

cccc. Fixed a bug that resulted in short-circuited evaluation when reading
      commands from a string ending in an unquoted backslash, or when sourcing
      a file that ends with an unquoted backslash.

dddd. Fixed a bug that resulted in the no-fork optimization not cleaning up
      any FIFOs created by process substitution.

eeee. If the -T option is not set, allow the source builtin and shell functions
      to set a DEBUG trap that persists after the sourced file or function
      returns, instead of restoring the old (unset) value unconditionally.

ffff. Fixed a bug that caused redirections to not be undone on some syntax
      errors, e.g., when parsing a command substitution.

gggg. Bash only adds asynchronous commands to the table of background pids
      whose status it remembers, to avoid it growing too large during scripts
      that create and reap large numbers of child processes.  This means that
      `wait' no longer works on synchronous jobs, but $? can be used to get
      the exit status in those cases.

hhhh. Bash now checks whether or not a shell script argument is a directory
      before trying to open it; Posix says implementations may allow open(2)
      on a directory.

iiii. Fixed a bug that could cause the shell to set the terminal's process
      group to a background process group when running as part of a pipeline.

jjjj. Made a few changes to strings to avoid possible potential negative effects
      caused by malicious translations.

kkkk. Fixed a bug that caused the `unset' builtin to continue to treat its
      arguments as functions after unsetting a function when invoked with no
      options.

llll. Fixed a bug that would not replace empty strings using pattern
      substitution even if the pattern matched the empty string.

mmmm. Fixed a bug with word completion that prevented some characters from
      being backslash-quoted (backquote, dollar sign).

nnnn. Fixed a bug that prevented a command from the history re-executed by the
      `fc' builtin from setting verbose mode.

oooo. Fixed a bug that caused the shell to not enable and disable function
      tracing with changes to the `extdebug' shell option.

pppp. Fixed a bug that caused assignments to nameref variables pointing to
      unset variables with attributes but no values to create variables with
      incorrect names.

qqqq. Fixed a bug that caused `unset' on nameref variables (without -n) to
      unset the wrong variable under certain circumstances.

rrrr. Fixed a bug that caused close braces occurring in brace expansions within
      command substitutions to incorrectly terminate parameter expansions.

ssss. Fixed a bug that caused `command -p' to temporarily alter $PATH.

tttt. Fixed a bug that caused interactive shells compiled without job control
      to return incorrect status values for child processes when running a
      single command that creates enough children to use the entire PID space.

uuuu. `esac' should not be recognized as a reserved word when it appears as the
      second or later pattern in a case statement pattern list.

vvvv. Fixed a bug that caused the completion code to read past the end of the
      readline line buffer while skipping assignment statements to find the
      command name.

wwww. Fixed a bug that caused case statements within loops contained in a
      command substitution to be parsed incorrectly.

xxxx. Fixed a bug that could cause SIGCHLD handling to be delayed after
      running `wait' with no arguments and interrupting it with ^C without
      a trap handler installed.

2.  Changes to Readline

a.  A bug that caused vi-mode `.' to be unable to redo `c', `d', and `y'
    commands with modifiers was fixed.

b.  Fixed a bug that caused callback mode to dump core when reading a
    multiple-key sequence (e.g., arrow keys).

c.  Fixed a bug that caused the redisplay code to erase some of the line when
    using horizontal scrolling with incremental search.

d.  Readline's input handler now performs signal processing if read(2) is
    interrupted by SIGALRM or SIGVTALRM.

e.  Fixed a problem with revert-all-at-newline freeing freed memory.

f.  Clarified the documentation for the history_quotes_inhibit_expansion
    variable to note that it inhibits scanning for the history comment
    character and that it only affects double-quoted strings.

g.  Fixed an off-by-one error in the prompt printed when performing searches.

h.  Use pselect(2), if available, to wait for input before calling read(2), so
    a SIGWINCH can interrupt it, since it doesn't interrupt read(2).

i.  Some memory leaks caused by signals interrupting filename completion have
    been fixed.

j.  Reading EOF twice on a non-empty line causes EOF to be returned, rather
    than the partial line.  This can cause partial lines to be executed on
    SIGHUP, for example.

k.  Fixed a bug concerning deleting multibyte characters from the search
    string while performing an incremental search.

l.  Fixed a bug with tilde expanding directory names in filename completion.

m.  Fixed a bug that did not allow binding sequences beginning with a `\'.

n.  Fixed a redisplay bug involving incorrect line wrapping when the prompt
    contains a multibyte character in the last screen column.

o.  Fixed a bug that caused history expansion to disregard characters that are
    documented to delimit a history event specifier without requiring `:'.

p.  Fixed a bug that could cause reading past the end of a string when reading
    the value when binding the set of isearch terminators.

q.  Fixed a bug that caused readline commands that depend on knowing which
    key invoked them to misbehave when dispatching key sequences that are
    prefixes of other key bindings.

r.  Paren matching now works in vi insert mode.

3.  New Features in Bash

a.  There is now a settable configuration #define that will cause the shell
    to exit if the shell is running setuid without the -p option and setuid
    to the real uid fails.

b.  Command and process substitutions now turn off the `-v' option when
    executing, as other shells seem to do.

c.  The default value for the `checkhash' shell option may now be set at
    compile time with a #define.

d.  The `mapfile' builtin now has a -d option to use an arbitrary character
    as the record delimiter, and a -t option  to strip the delimiter as
    supplied with -d.

e.  The maximum number of nested recursive calls to `eval' is now settable in
    config-top.h; the default is no limit.

f.  The `-p' option to declare and similar builtins will display attributes for
    named variables even when those variables have not been assigned values
    (which are technically unset).

g.  The maximum number of nested recursive calls to `source' is now settable
    in config-top.h; the default is no limit.

h.  All builtin commands recognize the `--help' option and print a usage
    summary.

i.  Bash does not allow function names containing `/' and `=' to be exported.

j.  The `ulimit' builtin has new -k (kqueues) and -P (pseudoterminals) options.

k.  The shell now allows `time ; othercommand' to time null commands.

l.  There is a new `--enable-function-import' configuration option to allow
    importing shell functions from the environment; import is enabled by
    default.

m.  `printf -v var ""' will now set `var' to the empty string, as if `var=""'
    had been executed.

n.  GLOBIGNORE, the pattern substitution word expansion, and programmable
    completion match filtering now honor the value of the `nocasematch' option.

o.  There is a new ${parameter@spec} family of operators to transform the
    value of `parameter'.

p.  Bash no longer attempts to perform compound assignment if a variable on the
    rhs of an assignment statement argument to `declare' has the form of a
    compound assignment (e.g., w='(word)' ; declare foo=$w); compound
    assignments are accepted if the variable was already declared as an array,
    but with a warning.

q.  The declare builtin no longer displays array variables using the compound
    assignment syntax with quotes; that will generate warnings when re-used as
    input, and isn't necessary.

r.  Executing the rhs of && and || will no longer cause the shell to fork if
    it's not necessary.

s.  The `local' builtin takes a new argument: `-', which will cause it to save
    the single-letter shell options and restore their previous values at
    function return.

t.  `complete' and `compgen' have a new `-o nosort' option, which forces
    readline to not sort the completion matches.

u.  Bash now allows waiting for the most recent process substitution, since it
    appears as $!.

v.  The `unset' builtin now unsets a scalar variable if it is subscripted with
    a `0', analogous to the ${var[0]} expansion.

w.  `set -i' is no longer valid, as in other shells.

x.  BASH_SUBSHELL is now updated for process substitution and group commands
    in pipelines, and is available with the same value when running any exit
    trap.

y.  Bash now checks $INSIDE_EMACS as well as $EMACS when deciding whether or
    not bash is being run in a GNU Emacs shell window.

z.  Bash now treats SIGINT received when running a non-builtin command in a
    loop the way it has traditionally treated running a builtin command:
    running any trap handler and breaking out of the loop.

aa. New variable: EXECIGNORE; a colon-separate list of patterns that will
    cause matching filenames to be ignored when searching for commands.

bb. Aliases whose value ends in a shell metacharacter now expand in a way to
    allow them to be `pasted' to the next token, which can potentially change
    the meaning of a command (e.g., turning `&' into `&&').

4.  New Features in Readline

a.  The history truncation code now uses the same error recovery mechanism as
    the history writing code, and restores the old version of the history file
    on error.  The error recovery mechanism handles symlinked history files.

b.  There is a new bindable variable, `enable-bracketed-paste', which enables
    support for a terminal's bracketed paste mode.

c.  The editing mode indicators can now be strings and are user-settable
    (new `emacs-mode-string', `vi-cmd-mode-string' and `vi-ins-mode-string'
    variables).  Mode strings can contain invisible character sequences.
    Setting mode strings to null strings restores the defaults.

d.  Prompt expansion adds the mode string to the last line of a multi-line
    prompt (one with embedded newlines).

e.  There is a new bindable variable, `colored-completion-prefix', which, if
    set, causes the common prefix of a set of possible completions to be
    displayed in color.

f.  There is a new bindable command `vi-yank-pop', a vi-mode version of emacs-
    mode yank-pop.

g.  The redisplay code underwent several efficiency improvements for multibyte
    locales.

h.  The insert-char function attempts to batch-insert all pending typeahead
    that maps to self-insert, as long as it is coming from the terminal.

i.  rl_callback_sigcleanup: a new application function that can clean up and
    unset any state set by readline's callback mode.  Intended to be used
    after a signal.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.3-release, and
the previous version, bash-4.3-rc2.

1.  Changes to Bash

a.  Only Posix-mode shells should exit on an assignment failure in the
    temporary environment preceding a special builtin.  This is how it's been
    documented.

b.  Fixed a bug that caused a failed special builtin to not exit a posix-mode
    shell if the failing builtin was on the LHS of a && or ||.

c.  Changed the handling of unquoted backslashes in regular expressions to be
    closer to bash-4.2.

d.  globstar (**) no longer traverses symbolic links that resolve to
    directories.  This eliminates some duplicate entries.

e.  Fixed a bug that caused a SIGCHLD trap handler to not be able to change the
    SIGCHLD disposition.

f.  Fixed a bug that caused a crash when -x was enabled and a command
    contained a printable multibyte (wide) character.

g.  Fixed a bug that caused an interactive shell without line editing enabled
    to read invalid data after receiving a SIGINT.

h.  Fixed a bug that caused command word completion to fail if the directory in
    $PATH where the completion would be found contained single or double quotes.

i.  Fixed a bug that caused a shell with -v enabled to print commands in $()
    multiple times.

2.  Changes to Readline

a.  Fixed a bug that caused `undo' to reference freed memory or null pointers.

3.  New Features in Bash

a.  The [[ -v ]] option now understands array references (foo[1]) and returns
    success if the referenced element has a value.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.3-rc2, and the
previous version, bash-4.3-rc1.

1.  Changes to Bash

a. Fixed a bug that left variables set by printf -v marked as invisible.

b. Fixed an off-by-one error in a job control warning message.

c. Fixed a bug that caused the shell to crash after declaring a nameref variable
   without a value.

d. Fixed a bug that caused asynchronous commands to not set $? correctly.

e. Fixed a bug that caused out-of-order execution when executing aliases with
   embedded newlines containing `.' commands.

f. Fixed a bug that caused error messages generated by expansion errors in
   `for' commands to have the wrong line number.

g. Fixed a bug that caused the `wait' builtin to not be interruptible in an
   interactive shell with job control enabled.

h. Fixed a bug that caused SIGINT and SIGQUIT to not be trappable in
   asynchronous subshell commands.

i. Bash now requires that the value assigned to a nameref variable be a valid
   shell identifier (variable name or array reference).

j. Converting an existing variable to a nameref variable now turns off the
   -i/-l/-u/-c attributes.

k. Displaying a nameref variable with `declare -p' now displays the nameref
   variable and its value rather than following the nameref chain.

l. Fixed a problem with interrupt handling that caused a second and subsequent
   SIGINT to be ignored by interactive shells.

m. Fixed a bug that caused certain positional parameter and array expansions
   to mishandle (discard) null positional parameters and array elements.

n. The shell no longer blocks receipt of signals while running trap handlers
   for those signals, and allows most trap handlers to be run recursively
   (running trap handlers while a trap handler is executing).

o. The shell now handles backslashes in regular expression arguments to the
   [[ command's =~ operator slightly differently, resulting in more
   consistent behavior.

2.  Changes to Readline

a. Fixed a bug that could cause readline to crash and seg fault attempting to
   expand an empty history entry.

b. Fixed a bug that caused a bad entry in the $LS_COLORS variable to abort all
   color processing but leave color enabled.

c. Fixed a bug that caused display problems with multi-line prompts containing
   invisible characters on multiple lines.

d. Fixed a bug that caused effects made by undoing changes to a history line to
   be discarded.

3.  New Features in Bash

4.  New Features in Readline

a.  When creating shared libraries on Mac OS X, the pathname written into the
    library (install_name) no longer includes the minor version number.
------------------------------------------------------------------------------
This document details the changes between this version, bash-4.3-rc1, and the
previous version, bash-4.3-beta2.

1.  Changes to Bash

a. Fixed a bug in bash completion that caused a tilde to be expanded even if
   the `direxpand' option was not enabled.

b. Fixed a potential bug that could cause corrupted input in interactive shells
   running without line editing and with `ignoreeof' enabled.

c. Fixed a bug that could cause failures when opening pipes back to shells
   created to run process substitutions.

d. Fixed a bug that caused an assignment to TEXTDOMAIN to require TEXTDOMAINDIR
   to be set in order to actually change the current text domain.

e. Changed the way redirections are printed to avoid confusion when the target
   of an output redirection is a process substitution beginning with `>'.

2.  Changes to Readline

a. Shared library building is now supported on Mac OS X 10.9 (Darwin 13).

3.  New Features in Bash

a. `cd' has a new `-@' option to browse a file's extended attributes on
   systems that support O_XATTR.

4.  New Features in Readline

a. There are additional default key bindings for MinGW32

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.3-beta2, and the
previous version, bash-4.3-beta.

1.  Changes to Bash

a.  Fixed a bug that caused assignment to an unset variable using a negative
    subscript to result in a segmentation fault.

b.  Fixed a bug that caused assignment to a string variable using a negative
    subscript to use the incorrect index.

c.  Fixed a bug that caused some strings to be interpreted as invalid
    extended globbing expressions when used with the help builtin.

d.  Fixed a bug that caused an attempt to trap a signal whose disposition
    cannot be changed to reference uninitialized memory.

e.  Command completion now skips assignment statements preceding a command
    name and completes the command.

f.  Fixed a bug that caused `compgen -f' in a non-interactive shell to dump
    core under certain circumstances.

g.  Fixed a bug that caused the `read -N' to misbehave when the input stream
    contains 0xff.

2.  Changes to Readline

a.  Changed message when an incremental search fails to include "failed" in
    the prompt and display the entire search string instead of just the last
    matching portion.

b.  Fixed a bug that caused an arrow key typed to an incremental search prompt
    to process the key sequence incorrectly.

c.  Additional key bindings for arrow keys on MinGW.

3.  New Features in Bash

a.  The help builtin now attempts substring matching (as it did through
    bash-4.2) if exact string matching fails.

b.  The fc builtin now interprets -0 as the current command line.

c.  Completing directory names containing shell variables now adds a trailing
    slash if the expanded result is a directory.

4.  New Features in Readline

a.  rl_change_environment: new application-settable variable that controls
    whether or not Readline modifies the environment (currently readline
    modifies only LINES and COLUMNS).

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.3-beta, and the
previous version, bash-4.3-alpha.

1.  Changes to Bash

a.  Fixed a bug in the prompt directory name "trimming" code that caused
    memory corruption and garbled the results.

b.  Fixed a bug that caused single quotes that resulted from $'...' quoting
    in the replacement portion of a double-quoted ${word/pat/rep} expansion
    to be treated as quote characters.

c.  Fixed a bug that caused assignment statements preceding a command word to
    result in assignment statements following a declaration command to not be
    expanded like assignment statements.

d.  Fixed a bug with variable search order in the presence of local variables
    with the same name as variables in the temporary environment supplied to
    a shell function.

e.  Fixed a bug that caused constructs like 1<(2) to be interpreted as process
    substitutions even in an arithmetic context.

f.  Fixed several cases where `invisible' variables (variables with attributes
    but no values, which are technically unset) were treated incorrectly.

g.  Fixed a bug that caused group commands in pipelines that were not the
    last element to not run the EXIT trap.

h.  Fixed a bug that caused `unset -n' to not unset a nameref variable in
    certain cases.

i.  Fixed the nameref circular reference checking to be less strict and only
    disallow a nameref variable with the same value as its name at the global
    scope.

j.  Fixed a bug that caused trap handlers to be executed recursively,
    corrupting internal data structures.

k.  Fixed a bug that could result in bash not compiling if certain options were
    not enabled.

l.  Fixed a bug that caused the arithmetic expansion code to attempt variable
    assignments when operator precedence prohibited them.

m.  Word expansions like ${foo##bar} now understand indirect variable references.

n.  Fixed a bug that caused `declare -fp name' to not display a function 
    definition.

o.  Fixed a bug that caused asynchronous child processes to modify the stdin
    file pointer when bash was using it to read a script, which modified the
    parent's value as well.

2.  Changes to Readline

a.  Fixed a bug in vi mode that caused the arrow keys to set the saved last
    vi-mode command to the wrong value.

b.  Fixed a bug that caused double-quoted strings to be scanned incorrectly
    when being used as the value of a readline variable assignment.

c.  Fixed a bug with vi mode that prevented `.' from repeating a command
    entered on a previous line (command).

d.  Fixed a bug that could cause completion to core dump if it was interrupted
    by a signal.

e.  Readline now sends the meta-key enable string to the terminal if the
    terminal has been successfully initialized.

f.  Readline now calls the signal hook after resizing the terminal when it
    receives a SIGWINCH.

g.  Fixed a bug that could cause the history list code to perform an out-of-
    bounds array reference if the history list is empty.

3.  New Features in Bash

a.  Shells started to run process substitutions now run any trap set on EXIT.

b.  There is now a configure-time option to enable the globasciiranges option
    by default.

c.  The read builtin now checks its first variable argument for validity before
    trying to read any input.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.3-alpha,
and the previous version, bash-4.2-release.

1.  Changes to Bash

a.  Fixed several bugs concerning incomplete bracket expressions in filename
    generation (globbing) patterns.

b.  Fixed a bug with single quotes and WORD in ${param op WORD} when running
    in Posix mode.

c.  Fixed a bug that caused the pattern removal and pattern substitution word
    expansions and case statement word expansion to not match the empty string.

d.  Fixed a bug that caused the tzset() function to not work after changing
    the TZ environment variable.

e.  Fixed a bug that caused the RHS of an assignment statement to undergo
    word splitting when it contained an unquoted $@.

f.  Fixed bugs that caused the shell to not react to a SIGINT sent while
    waiting for a child process to exit.

g.  Bash doesn't try to run things in a signal handler context when it gets a
    signal (SIGINT/SIGHUP/etc) while reading input using readline but still
    be responsive to terminating signals.

h.  Fixed a bug that caused bash to go into an infinite loop if a filename
    to be matched contained an invalid multibyte character.

i.  Fixed a bug that caused PS4 to end up being truncated if it is longer
    than 128 bytes.

j.  Fixed a bug that caused brace expansion to not skip over double-quoted
    command substitution.

k.  System-specific updates for: DJGPP, HP/UX, Mac OS X

l.  Fixed a bug in displaying commands that caused redirections to be associated
    with the wrong part of the command.

m.  Fixed the coproc cleanup to unset the appropriate shell variables when a
    coproc terminates.

n.  Fixed a bug that caused `fc' to dump core due to incorrect calculation of
    the last history entry.

o.  Added workarounds for FreeBSD's implementation of faccessat/eaccess and
    `test -x'.

p.  Fixed a bug that caused the shell to not match patterns containing
    control-A.

q.  Fixed a bug that could result in doubled error messages when the `printf'
    builtin got a write error.

r.  Fixed a bug that caused the shell to not correctly expand words containing
    multiple consecutive quoted empty strings (""""""aa).

s.  Fixed a bug that caused the shell to not correctly parse multi-line
    process substitutions containing comments and quoted strings.

t.  Fixed a problem with the bash malloc's internal idea of the top of the
    memory heap that resulted in incorrect decisions to try to reduce the
    break and give memory back to the kernel.

u.  There are changes to the expansions performed on compound array assignments,
    in an effort to make foo=( [ind1]=bar [ind2]=baz ) identical to
    foo[ind1]=bar foo[ind2]=baz.

v.  Bash now reports an error if `read -a name' is used when `name' is an
    existing associative array.

w.  Fixed a bug that allowed an attempted assignment to a readonly variable
    in an arithmetic expression to not return failure.

x.  Fixed several bugs that caused completion functions to be invoked even when
    the cursor was before the first word in the command.

y.  Fixed a bug that caused parsing a command substitution to overwrite the
    parsing state associated with the complete input line.

z.  Fixed several bugs with the built-in snprintf replacement and field widths
    and floating point.

aa. Fixed a bug that caused incorrect offset calculations and input buffer
    corruption when reading files longer than 2^31 bytes.

bb. Fixed several bugs where bash performed arithmetic evaluation in contexts
    where evaluation is suppressed.

cc. Fixed a bug that caused bash to close FIFOs used for process substitution
    too early when a shell function was executing, but protect against using
    all file descriptors when the shell functions are invoked inside loops.

dd. Added checks for printable (and non-printable) multibyte characters for
    use in error messages.

ee. Fixed a bug that caused ^O (operate-and-get-next) to not work correctly
    at the end of the history list.

ff. Fixed a bug that caused command-oriented history to incorrectly combine
    here documents into one line.

gg. Fixed a bug that caused importing SHELLOPTS from the environment into a
    Posix-mode shell to print an error message and refuse to parse it.

hh. Fixed a bug that caused the shell to delete an extra history entry when
    using `history -s'.

ii. Fixed a bug that caused floating-point exceptions and overflow errors
    for the / and % arithmetic operators when using INTMAX_MIN and -1.

jj. Fixed a bug that caused parsing errors when reading an arithmetic for
    loop inside a command substitution.

kk. Fixed a bug that caused a readonly function to be unset when unset was
    called without the -f or -v option.

ll. Fixed several bugs in the code that quotes characters special to regular
    expressions when used in a quoted string on the RHS of the =~ operator
    to the [[ command.

mm. Fixed a bug that caused redirections to fail because the file descriptor
    limit was set to a value less than 10.

nn. Fixed a bug that caused the `read' builtin to execute code in a signal
    handler context if read timed out.

oo. Fixed a bug that caused extended globbing patterns to not match files
    beginning with `.' correctly when a `.' was explicitly supplied in the
    pattern.

pp. Fixed a bug that caused key sequences longer than two characters to not
    work when used with `bind -x'.

qq. Fixed a bug that resulted in redefined functions having the wrong source
    file names in BASH_SOURCE.

rr. Fixed a bug that caused the read builtin to assign null strings to variables
    when using `read -N', which caused core dumps when referenced

ss. Fixed a bug that caused `bash -m script' to not enable job control while
    running the script.

tt. Fixed a bug that caused `printf -v var' to dump core when used with the
    %b format code.

uu. Fixed a bug that caused the shell to exit with the wrong status if -e was
    active and the shell exited on a substitution error.

vv. Fixed a bug that caused the shell to seg fault if an array variable with
    the same name as an existing associative array was implicitly created by
    an assignment (declare a[n]=b).

ww. Fixed a bug that caused a redirection to misbehave if the number specified
    for a file descriptor overflows an intmax_t.

xx. Fixed several bugs with the handling of valid and invalid unicode character
    values when used with the \u and \U escape sequences to printf and $'...'.

yy. Fixed a bug that caused tildes to not be escaped in expanded filenames,
    making them subject to later expansion.

zz. When using the pattern substitution word expansion, bash now runs the
    replacement string through quote removal, since it allows quotes in that
    string to act as escape characters.  This is not backwards compatible, so
    it can be disabled by setting the bash compatibility mode to 4.2.

aaa. Fixed the rest of the cases where the shell runs non-allowed code in a
     signal handler context.

bbb. Fixed a bug that caused spurious DEL characters (\177) to appear in
     double-quoted expansion where the RHS is evaluated to the empty string.

ccc. Fixed a bug that caused the use of the shell's internal random number
     generator for temporary file names to perturb the random number
     sequence.

ddd. Fixed several bugs that caused `declare -g' to not set the right global
     variables or to misbehave when declaring global indexed arrays.

eee. Fixed a logic bug that caused extended globbing in a multibyte locale to
     cause failures when using the pattern substititution word expansions.

fff. Fixed a bug that caused the `lastpipe' option to corrupt the file
     descriptor used to read the script.

ggg. Fixed a bug that causes the shell to delete DEL characters in the
     expanded value of variables used in the same quoted string as variables
     that expand to nothing.

hhh. Fixed a bug that caused the shell to assign the wrong value from an
     assignment like (( x=7 )) when `x' was an existing array variable.

iii. Fixed a bug that caused the shell to misbehave when generating sequences
     and the boundary values overflow an intmax_t.

jjj. Fixed a bug caused expansion errors if an expansion of "$@" appeared
     next to another expansion (e.g.. "${@}${x}").

kkk. Fixed a potential buffer overflow bug when performing /dev/fd expansion.

lll. Fixed a bug that resulted in an extra semicolon being added to compound
     assignments when they were added to the history list.

mmm. Fixed a bug that caused mapfile to read one extra line from the input.

nnn. Fixed a bug that caused the mail checking code to use uninitialized
     values.

ooo. Fixed a bug that prevented history timestamps from being saved if the
     history comment character is unset.

ppp. Fixed a bug that caused the case-modifying expansions to not work with
     multibyte characters.

qqq. Fixed a bug that caused the edit-and-execute bindable readline command
     to see the wrong data if invoked in the middle of a multi-line quoted
     string.

rrr. Fixed a bug that resulted in the shell returning the wrong exit status
     for a background command on systems that recycle PIDs very quickly.

sss. Fixed a bug that caused asynchronous group commands to not run any EXIT
     trap defined in the body of the command.

ttt. Fixed a bug that caused `eval "... ; return"' to not clean up properly.

uuu. Fixed a bug that caused the shell to dump core if `read' reads an escaped
     IFS whitespace character.

vvv. Fixed a bug that caused BASH_COMMAND to be set to an incorrect value when
     executing a (...) subshell.

www. Fixed a couple of pointer aliasing bugs with the token string in arithmetic
     evaluation.

xxx. Fixed a bug with parsing multi-line command substitutions when reading
     the `do' keyword followed by whitespace.

yyy. Fixed a bug that caused the shell to seg fault if the time given to the
     printf %(...)T format overflowed the value accepted by localtime(3).

zzz. Fixed a problem with displaying help topics in two columns when the
     translated text contained multibyte characters.

aaaa. Fixed a bug with the extended globbing pattern matcher where a `*' was
      followed by a negated extended glob pattern.

bbbb. Fixed a race condition with short-lived coproc creation and reaping that
      caused the child process to be reaped before the various coproc shell
      variables were initialized.

cccc. Fixed a bug where turning off `errexit' in command substitution subshells
      was not reflected in $SHELLOPTS.

dddd. Partially fixed an inconsistency in how the shell treated shell
      functions run from an EXIT trap.

eeee. Fixed a bug in how the shell invalidated FIFOs used for process
      substitution when executing a pipeline (once rather than in every child).

ffff. Fixed a bug that occurred when expanding a special variable ($@, $*)
      within double quotes and the expansion resulted in an empty string.

gggg. Fixed bugs with executing a SIGCHLD trap handler to make sure that it's
      executed once per exited child.

hhhh. Fixed a bug that caused `declare' and `test' to find variables that
      had been given attributes but not assigned values.  Such variables are
      not set.

iiii. Fixed a bug that caused commands in process substitutions to not look in
      the local temporary environment when performing word expansions.

jjjj. Fixed several problems with globstar expansions (**/**) returning null
      filenames and multiple instances of the same pathname.

kkkk. Fixed an oversight that did not allow the exit status of `coproc' to
      be inverted using `!'.

llll. Fixed a bug that caused the -e option to be re-enabled using `set -e'
      even when executing in a context where -e is ignored.

mmmm. Fixed a (mostly theoretical) bug with input lines longer than SIZE_MAX.

nnnn. Fixed a bug that could result in double evaluation of command
      substitutions when they appear in failed redirections.

oooo. Fixed a bug that could cause seg faults during `mapfile' callbacks if
      the callback unsets the array variable mapfile is using.

pppp. Fixed several problems with variable assignments using ${var:=value}
      when the variable assignment is supposed to have side effects.

qqqq. Fixed a bug that caused a failure of an assignment statement preceding a
      builtin caused the next invocation of a special builtin to exit the shell.

rrrr. Fixed several problems with IFS when it appears in the temporary environment
      and is used in redirections.

ssss. Fixed a problem that caused IFS changes using ${IFS:=value} to modify
      how preceding expansions were split.

tttt. Fixed a problem that caused subshells to not run an EXIT trap they set.

uuuu. Fixed a problem that caused shells started in posix mode to attempt to
      import shell functions with invalid names from the environment.  We now
      print a warning.

vvvv. Worked around a kernel problem that caused SIGCHLD to interrupt open(2)
      on a FIFO used for process substitution, even if the SIGCHLD handler was
      installed with the SA_RESTART flag.

wwww. Fixed a problem that resulted in inconsistent expansion of $* and ${a[*]}.

xxxx. Fixed a problem that caused `read -t' to crash when interrupted by
      SIGINT.

yyyy. Fixed a problem that caused pattern removal to fail randomly because the
      pattern matcher read beyond the end of a string.

zzzz. Fixed a bug that caused core dumps when shell functions tried to create
      local shadow copies of special variables like GROUPS.

aaaaa. Fixed a bug that caused SIGTERM to be occasionally lost by children of
       interactive shells when it arrived before the child process reset the
       handler from SIG_DFL.

bbbbb. Fixed a bug that caused redirections like <&n- to leave file descriptor
       n closed if executed with a builtin command.

ccccc. Fixed a bug that caused incorrect completion quoting when completing a
       word containing a globbing character with `show-all-if-ambiguous' set.

ddddd. Fixed a bug that caused printf's %q format specifier not to quote a
       tilde even if it appeared in a location where it would be subject to
       tilde expansion.

2.  Changes to Readline

a.  Fixed a bug that did not allow the `dd', `cc', or `yy' vi editing mode
    commands to work on the entire line.

b.  Fixed a bug that caused redisplay problems with prompts longer than 128
    characters and history searches.

c.  Fixed a bug that caused readline to try and run code to modify its idea
    of the screen size in a signal handler context upon receiving a SIGWINCH.

d.  Fixed a bug that caused the `meta' key to be enabled beyond the duration
    of an individual call top readline().
    
e.  Added a workaround for a wcwidth bug in Mac OS X that caused readline's
    redisplay to mishandle zero-width combining characters.

f.  Fixed a bug that caused readline to `forget' part of a key sequence when
    a multiple-key sequence caused it to break out of an incremental search.

g.  Fixed bugs that caused readline to execute code in a signal handler
    context if interrupted while reading from the file system during completion.

h.  Fixed a bug that caused readline to `forget' part of a key sequence when
    reading an unbound multi-character key sequence.

i.  Fixed a bug that caused Readline's signal handlers to be installed beyond
    the bounds of a single call to readline().

j.  Fixed a bug that caused the `.' command to not redo the most recent `R'
    command in vi mode.

k.  Fixed a bug that caused ignoring case in completion matches to result in
    readline using the wrong match.

l.  Paren matching now works in vi insert mode.

m.  Fix menu-completion to make show-all-if-ambiguous and menu-complete-display-prefix
    work together.

n.  Fixed a bug that didn't allow the `cc', `dd', or `yy' commands to be redone
    in vi editing mode.

o.  Fixed a bug that caused the filename comparison code to not compare
    multibyte characters correctly when using case-sensitive or case-mapping
    comparisons.

p.  Fixed the input reading loop to call the input hook function only when there
    is no terminal input available.

q.  Fixed a bug that caused binding a macro to a multi-character key sequence
    where the sequence and macro value share a common prefix to not perform
    the macro replacement.

r.  Fixed several redisplay errors with multibyte characters and prompts
    containing invisible characters when using horizontal scrolling.

s.  Fixed a bug that caused redisplay errors when trying to overwrite
    existing characters using multibyte characters.

3.  New Features in Bash

a.  The `helptopic' completion action now maps to all the help topics, not just
    the shell builtins.

b.  The `help' builtin no longer does prefix substring matching, so `help read'
    does not match `readonly'.

c.  The shell can be compiled to not display a message about processes that
    terminate due to SIGTERM.

d.  Non-interactive shells now react to the setting of checkwinsize and set
    LINES and COLUMNS after a foreground job exits.

e.  There is a new shell option, `globasciiranges', which, when set to on,
    forces globbing range comparisons to use character ordering as if they
    were run in the C locale.

f.  There is a new shell option, `direxpand', which makes filename completion
    expand variables in directory names in the way bash-4.1 did.

g.  In Posix mode, the `command' builtin does not change whether or not a
    builtin it shadows is treated as an assignment builtin.

h.  The `return' and `exit' builtins accept negative exit status arguments.

i.  The word completion code checks whether or not a filename containing a
    shell variable expands to a directory name and appends `/' to the word
    as appropriate.  The same code expands shell variables in command names
    when performing command completion.

j.  In Posix mode, it is now an error to attempt to define a shell function
    with the same name as a Posix special builtin.

k.  When compiled for strict Posix conformance, history expansion is disabled
    by default.

l.  The history expansion character (!) does not cause history expansion when
    followed by the closing quote in a double-quoted string.

m.  `complete' and its siblings compgen/compopt now takes a new `-o noquote'
    option to inhibit quoting of the completions.

n.  Setting HISTSIZE to a value less than zero causes the history list to be
    unlimited (setting it 0 zero disables the history list).

o.  Setting HISTFILESIZE to a value less than zero causes the history file size
    to be unlimited (setting it to 0 causes the history file to be truncated
    to zero size).

p.  The `read' builtin now skips NUL bytes in the input.

q.  There is a new `bind -X' option to print all key sequences bound to Unix
    commands.

r.  When in Posix mode, `read' is interruptible by a trapped signal.  After
    running the trap handler, read returns 128+signal and throws away any
    partially-read input.

s.  The command completion code skips whitespace and assignment statements
    before looking for the command name word to be completed.

t.  The build process has a new mechanism for constructing separate help files
    that better reflects the current set of compilation options.

u.  The -nt and -ot options to test now work with files with nanosecond
    timestamp resolution.

v.  The shell saves the command history in any shell for which history is
    enabled and HISTFILE is set, not just interactive shells.

w.  The shell has `nameref' variables and new -n(/+n) options to declare and
    unset to use them, and a `test -R' option to test for them.

x.  The shell now allows assigning, referencing, and unsetting elements of
    indexed arrays using negative subscripts (a[-1]=2, echo ${a[-1]}) which
    count back from the last element of the array.

y.  The {x}<word redirection feature now allows words like {array[ind]} and
    can use variables with special meanings to the shell (e.g., BASH_XTRACEFD).

z.  There is a new CHILD_MAX special shell variable; its value controls the
    number of exited child statuses the shell remembers.

aa. There is a new configuration option (--enable-direxpand-default) that
    causes the `direxpand' shell option to be enabled by default.

bb. Bash does not do anything special to ensure that the file descriptor
    assigned to X in {x}<foo remains open after the block containing it
    completes.

cc. The `wait' builtin has a new `-n' option to wait for the next child to
    change status.

dd. The `printf' %(...)T format specifier now uses the current time if no
    argument is supplied.

ee. There is a new variable, BASH_COMPAT, that controls the current shell
    compatibility level.

ff. The `popd' builtin now treats additional arguments as errors.

gg. The brace expansion code now treats a failed sequence expansion as a
    simple string and will continue to expand brace terms in the remainder
    of the word.

4.  New Features in Readline

a.  Readline is now more responsive to SIGHUP and other fatal signals when
    reading input from the terminal or performing word completion but no
    longer attempts to run any not-allowable functions from a signal handler
    context.

b.  There are new bindable commands to search the history for the string of
    characters between the beginning of the line and the point
    (history-substring-search-forward, history-substring-search-backward)

c.  Readline allows quoted strings as the values of variables when setting
    them with `set'.  As a side effect, trailing spaces and tabs are ignored
    when setting a string variable's value.

d.  The history library creates a backup of the history file when writing it
    and restores the backup on a write error.

e.  New application-settable variable: rl_filename_stat_hook: a function called
    with a filename before using it in a call to stat(2).  Bash uses it to
    expand shell variables so things like $HOME/Downloads have a slash
    appended.

f.  New bindable function `print-last-kbd-macro', prints the most-recently-
    defined keyboard macro in a reusable format.

g.  New user-settable variable `colored-stats', enables use of colored text
    to denote file types when displaying possible completions (colored analog
    of visible-stats).

h.  New user-settable variable `keyseq-timout', acts as an inter-character
    timeout when reading input or incremental search strings.

i.  New application-callable function: rl_clear_history. Clears the history list
    and frees all readline-associated private data.

j.  New user-settable variable, show-mode-in-prompt, adds a characters to the
    beginning of the prompt indicating the current editing mode.

k.  New application-settable variable: rl_input_available_hook; function to be
    called when readline needs to check whether there is data available on its
    input source.  The default hook checks rl_instream.

l.  Readline calls an application-set event hook (rl_signal_event_hook) after
    it gets a signal while reading input (read returns -1/EINTR but readline
    does not handle the signal immediately) to allow the application to handle
    or otherwise note it.  Not currently called for SIGHUP or SIGTERM.

m.  If the user-settable variable `history-size' is set to a value less than
    0, the history list size is unlimited.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.2-release,
and the previous version, bash-4.2-rc2.

1.  Changes to Bash

a.  Fixed a bug that caused some variables to be clobbered by a longjmp,
    resulting in stack corruption.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.2-rc2,
and the previous version, bash-4.2-rc1.

1.  Changes to Bash

a.  Changes to bash_directory_completion_hook so that it's assigned to the
    readline rl_directory_rewrite_hook variable, which modifies the directory
    name passed to opendir without modifying the directory name the user
    typed.

b.  Fixed bug in select builtin that caused it to not terminate correctly if
    the read timed out due to $TMOUT.

c.  Fixed a problem that resulted in non-repeatable sequences of random
    numbers when RANDOM=0.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.2-rc1,
and the previous version, bash-4.2-beta.

1.  Changes to Bash

a.  Fixed a bug that caused some redirection errors to leak file descriptors.

b.  Fixed a bug that caused unary `+' and `-' arithmetic operators to have a
    higher precedence than unary `!' and `~'.

c.  Fixed a bug that caused simple commands in a pipeline to affect the exit
    status ($?) seen by subsequent pipeline commands.

d.  A number of cygwin-specific changes to avoid the use of text-mode files
    and file access, and to make sure that \r is handled correctly.

e.  Fixed a bug that caused the read builtin to not return failure if an
    attempt is made to assign to a readonly variable.

f.  Fixed a bug that caused some builtin usage messages to not be translated.

g.  Fixed a bug that caused the getopts builtin to not return failure if an
    attempt is made to assign to a readonly variable.  Now it returns 2.

h.  Fixed the cd and pwd builtins to return failure if PWD is readonly and
    cannot be assigned to.

i.  Added code to check the return value of access(2) on Solaris systems,
    since it returns success for executable tests (e.g., `test -x') when
    run by root, even if the file permissions don't allow execution.

2.  Changes to Readline

a.  Fixed a bug that caused directory names in words to be completed to not
    be dequoted correctly.

3.  New Features in Bash

4.  New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.2-beta,
and the previous version, bash-4.2-alpha.

1.  Changes to Bash

a.  Fixed a bug that caused the \W prompt string escape to not add a closing
    NULL.

b.  Fixed a bug that caused partially-quoted words that were not subject to
    word splitting to retained quoted NULLs.

c.  Added considerable efficiency speedups when pattern matching in multibyte
    locales by skipping multibyte character functions where possible.

d.  Added considerable speedups to variable expansion when in multibyte locales.

e.  Fixed a bug that caused the expansion of $* when there are no positional
    parameters to cause the shell to dump core when used in a pattern
    matching context.

f.  Fixed a bug that caused variable expansions preceding regular builtins to
    not change the shell environment during their execution.

2.  Changes to Readline

a.  Fixed a bug that made an explicit argument of 0 to yank-last-arg behave
    as if it were a negative argument.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.2-alpha,
and the previous version, bash-4.1-release.

1.  Changes to Bash

a.  Fixed a bug in the parser when processing alias expansions containing
    quoted newlines.

b.  Fixed a memory leak in associative array expansion.

c.  Fixed a bug that caused quoted here-strings to be requoted when printed.

d.  Fixed a bug in arithmetic expansion that caused the index in an array
    expansion to be evaluated twice under certain circumstances.

e.  Fixed several bugs with the expansion and display of variables that have
    been given attributes but not values and are technically unset.

f.  Fixed a bug that caused core dumps when using filename completion that
    expands to a filename containing a globbing character.

g.  Fixed a bug that caused assignment statements preceding a special builtin
    when running in Posix mode to not persist after the builtin completed
    when the special builtin was executed in a shell function without any
    local variables.

h.  Fixed a bug that caused a command to remain in the hash table even after
    `hash command' did not find anything if there was already an existing
    hashed pathname.

i.  Fixed several bugs caused by executing unsafe functions from a signal
    handler in the cases where a signal handler is executed immediately
    rather than setting a flag for later execution.

j.  Fixed a bug that caused some internal flag variables to be set
    incorrectly if `read -t' timed out.

k.  Fixed a Posix compatibility issue by making sure that a backslash escaping
    a `}' within a double-quoted ${...} parameter expansion is removed as part
    of the parameter expansion.

l.  Fixed a bug that caused execution of a trap to overwrite PIPESTATUS.

m.  Fixed a bug that caused here documents to not be displayed correctly
    when attached to commands inside compound commands.

n.  Fixed a bug that caused the printf builtin to use the wrong precision
    when using the `*' modifier.

o.  Fixed a bug that caused an arriving SIGCHLD to interrupt output functions
    like those invoked by echo or printf.

p.  Changed to use a more robust mechanism than eaccess(2) when test is
    checking filenames for execution permission.

q.  Fixed a bug that caused spurious semicolons to be added into the command
    history in certain cases.

r.  Fixed a bug that caused the shell to free non-allocated memory when
    unsetting element 0 of an associative array after it was assigned
    implicitly.

s.  Fixed a bug that could cause the shell to dump core if using the `v'
    vi editing command on a multi-line command.

t.  Fixed a bug that left FIFOs opened by process substitutions open long
    enough to potentially cause file descriptor exhaustion when running a
    shell function or shell builtin.

u.  Fixed a bug that caused the history expansion functions to not recognize
    process substitution or extended glob patterns as single words.

v.  Fixed a bug that caused restricted shells to set a restricted command's
    exit status incorrectly.

w.  Fixed a bug that caused bash to ignore the wrong set of filenames when
    completing a command using the `complete-filename' readline command.

x.  Fixed a bug that caused a -PID argument following a -s sig or -n sig to
    not be interpreted as a signal specification.

y.  Changed posix-mode behavior of a parse error in a `.' script or `eval'
    command to exit the shell under Posix-specified conditions.  Previous
    versions printed a warning.

z.  Fixed a bug in \W prompt expansion that resulted in incorrect expansion
    in the event of overlapping strings.

aa. Fixed a bug that caused the := parameter expansion operator to return the
    wrong value as the result of the expansion.

bb. When in Posix mode, a single quote is not treated specially in a
    double-quoted ${...} expansion, unless the expansion operator is
    # or % or the non-Posix `//', `^', and `,'.  In particular, it does
    not define a new quoting context.  This is from Posix interpretation 221.

cc. Fixed a bug that inadvertently allowed program names containing slashes
    to be entered into the command hash table.

dd. Fixed a bug that caused the select builtin to incorrectly compute the
    display width of the arguments in the presence of multibyte characters.

ee. Fixed a bug that caused bash to not change the xtrace file descriptor if
    BASH_XTRACEFD was found in the shell environment at startup.

ff. Fixed a memory leak in the pattern removal parameter expansion.

gg. Fixed a bug that caused SIGINT to fail to interrupt a nested loop if the
    loop was in a pipeline.

hh. Fixed a problem in $(...) parsing that caused the parser to add an extra
    space to a here-document delimiter if the first word contained a `/'.

ii. Fixed a bug that caused functions defined with the `function' reserved
    word to require braces around the function body.

jj. Fixed a bug that caused bash to dump core when a variable expansion being
    used as an array subscript failed.

kk. Fixed a bug that caused bash to dump core if the case-modification
    expansions were used on a variable with a null value.

ll. Fixed a bug that caused partially-quoted strings to be split incorrectly
    if a variable with a null value was expanded within double quotes.

mm. The pattern substitution word expansion has been sped up dramatically
    when running in a locale with multibyte characters.

nn. Fixed a bug that caused history -a to not write the correct lines to
    the history file if all the new lines in the history list were added
    since the last time the history file was read or written.

oo. Fixed a bug that caused completion of a word with an unclosed `` command
    substitution to set the prompt incorrectly.

pp. Fixed a bug that caused extended globbing patterns in $HISTIGNORE or
    $GLOBIGNORE to be incorrectly scanned.

qq. Fixed a bug caused by closing file descriptors 3-20 on shell startup.  The
    shell now sets them to close-on-exec.

rr. Fixed a bug that caused the exit status of `exec file' to be set incorrectly
    if `file' was a directory.

ss. Fixed a bug in the `.' builtin to make a non-interactive posix-mode shell
    exit if the file argument to `.' is not found.  Prefixing exec with 
    `command' makes the shell not exit. Posix requires this behavior.

tt. Fixed a bug that caused `sh -c 'command exec; exit 1' to hang.

uu. Fixed a bug in $(...) command substitution parsing that caused the shell
    to treat backslash-newline incorrectly when parsing a comment.

vv. Fixed bug that caused brace expansion sequence generation to misbehave
    when supplied integers greater than 2**31 - 1.

ww. Fixed a bug that caused failure to save file descriptors for redirections
    to corrupt shell file descriptors.

xx. Fixed a bug that caused bash-forward-shellword to not correctly handle
    quoted strings.

2.  Changes to Readline

a.  Fixed a bug that caused the unconverted filename to be added to the list of
    completions when the application specified filename conversion functions.

b.  Fixed a bug that caused the wrong filename to be passed to opendir when the
    application has specified a filename dequoting function.

c.  Fixed a bug when repeating a character search in vi mode in the case where
    there was no search to repeat.

d.  When show-all-if-ambiguous is set, the completion routines no longer insert
    a common match prefix that is shorter than the text being completed.

e.  The full set of vi editing commands may now be used in callback mode.

f.  Fixed a bug that caused readline to not update its idea of the terminal
    dimensions while running in `no-echo' mode.

h.  Fixed a bug that caused readline to dump core if an application called
    rl_prep_terminal without setting rl_instream.

i.  Fixed a bug that caused meta-prefixed characters bound to incremental
    search forward or backward to not be recognized if they were typed
    subsequently.

j.  The incremental search code treats key sequences that map to the same
    functions as (default) ^G, ^W, and ^Y as equivalent to those characters.

k.  Fixed a bug in menu-complete that caused it to misbehave with large
    negative argument.

l.  Fixed a bug that caused vi-mode yank-last-arg to ring the bell when invoked
    at the end of the line.

3.  New Features in Bash

a.  `exec -a foo' now sets $0 to `foo' in an executable shell script without a
    leading #!.

b.  Subshells begun to execute command substitutions or run shell functions or
    builtins in subshells do not reset trap strings until a new trap is
    specified.  This allows $(trap) to display the caller's traps and the
    trap strings to persist until a new trap is set.

c.  `trap -p' will now show signals ignored at shell startup, though their
    disposition still cannot be modified.

d.  $'...', echo, and printf understand \uXXXX and \UXXXXXXXX escape sequences.

e.  declare/typeset has a new `-g' option, which creates variables in the
    global scope even when run in a shell function.

f.  test/[/[[ have a new -v variable unary operator, which returns success if
    `variable' has been set.

g.  Posix parsing changes to allow `! time command' and multiple consecutive
    instances of `!' (which toggle) and `time' (which have no cumulative
    effect).

h.  Posix change to allow `time' as a command by itself to print the elapsed
    user, system, and real times for the shell and its children.

j.  $((...)) is always parsed as an arithmetic expansion first, instead of as
    a potential nested command substitution, as Posix requires.

k.  A new FUNCNEST variable to allow the user to control the maximum shell
    function nesting (recursive execution) level.

l.  The mapfile builtin now supplies a third argument to the callback command:
    the line about to be assigned to the supplied array index.

m.  The printf builtin has a new %(fmt)T specifier, which allows time values
    to use strftime-like formatting.

n.  There is a new `compat41' shell option.

o.  The cd builtin has a new Posix-mandated `-e' option.

p.  Negative subscripts to indexed arrays, previously errors, now are treated
    as offsets from the maximum assigned index + 1.

q.  Negative length specifications in the ${var:offset:length} expansion,
    previously errors, are now treated as offsets from the end of the variable.

r.  Parsing change to allow `time -p --'.

s.  Posix-mode parsing change to not recognize `time' as a keyword if the
    following token begins with a `-'.  This means no more Posix-mode
    `time -p'.  Posix interpretation 267.

t.  There is a new `lastpipe' shell option that runs the last command of a
    pipeline in the current shell context.  The lastpipe option has no
    effect if job control is enabled.

u.  History expansion no longer expands the `$!' variable expansion.

v.  Posix mode shells no longer exit if a variable assignment error occurs
    with an assignment preceding a command that is not a special builtin.

w.  Non-interactive mode shells exit if -u is enabled and an attempt is made
    to use an unset variable with the % or # expansions, the `//', `^', or
    `,' expansions, or the parameter length expansion.

x.  Posix-mode shells use the argument passed to `.' as-is if a $PATH search
    fails, effectively searching the current directory.  Posix-2008 change.

4.  New Features in Readline

a.  The history library does not try to write the history filename in the
    current directory if $HOME is unset.  This closes a potential security
    problem if the application does not specify a history filename.

b.  New bindable variable `completion-display-width' to set the number of
    columns used when displaying completions.

c.  New bindable variable `completion-case-map' to cause case-insensitive
    completion to treat `-' and `_' as identical.

d.  There are new bindable vi-mode command names to avoid readline's case-
    insensitive matching not allowing them to be bound separately.

e.  New bindable variable `menu-complete-display-prefix' causes the menu
    completion code to display the common prefix of the possible completions
    before cycling through the list, instead of after.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.1-rc,
and the previous version, bash-4.1-beta.

1.  Changes to Bash

a.  Fixed a bug that caused printf to not return a partial value when it
    encountered an error while converting an integer argument.

b.  Fixed a bug that caused setting one of the compatNN options to not
    turn off the others.

c.  The (undocumented) --wordexp option is no longer included by default.

d.  Fixed a bug in conditional command execution that caused it to not
    correctly ignore the exit status under certain circumstances.

e.  Added a configure-time check for correctly-working asprintf/snprintf.

f.  Fixed some problems with line number calculation and display when sourcing
    a file in an interactive shell.

g.  Fixed a bug that caused the shell to crash when using `declare -A foo=bar'.

h.  Fixed a bug that caused an off-by-one error when calculating the directories
    to display with the PROMPT_DIRTRIM option.

2.  Changes to Readline

a.  Fixed a bug that caused applications using the callback interface to not
    react to SIGINT (or other signals) until another character arrived.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.1-beta,
and the previous version, bash-4.1-alpha.

1.  Changes to Bash

a.  Fixed a bug in mapfile that caused the shell to crash if it was passed the
    name of an associative array.

b.  Fixed a bug that caused the shell to incorrectly split case patterns if
    they contained characters in $IFS.

c.  Fixed a bug that caused the shell to set $? to the wrong value when using
    a construct ending with a variable assignment with set -x enabled and PS4
    containing a command substitution.

d.  Fixed a bug that caused the shell to read commands incorrectly if an
    expansion error occurred under certain conditions in a user-specified
    subshell.

e.  Fixed a bug that caused the shell to set $? incorrectly if a parse error
    occurred in an evaluation context ("eval", trap command, dot script, etc.)

f.  Fixed a bug that caused the shell to attempt command substitution
    completion within a single-quoted string.

g.  Fixed a bug that caused the shell to insert an extra single quote during
    word completion.

h.  Fixed a bug that caused the shell to crash if invoked with the environment
    variable EMACS having a null value.

i.  Fixed a bug that caused bash to incorrectly report the presence of new
    mail in a `maildir' environment.

j.  Fixed a bug that caused the shell to not recognize a here-document ending
    delimiter inside a command substitution.

k.  Fixed a bug that caused the shell to crash when a a dynamic array variable
    was assigned a scalar value.

2.  Changes to Readline

3.  New Features in Bash

a.  The mapfile/readarray builtin no longer stores the commands it invokes via
    callbacks in the history list.

b.  There is a new `compat40' shopt option.

c.  The < and > operators to [[ do string comparisons using the current locale
    only if the compatibility level is greater than 40 (set to 41 by default).

4.  New Features in Readline

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.1-alpha,
and the previous version, bash-4.0-release.

1.  Changes to Bash

a.  Fixed bugs in the parser involving new parsing of the commands contained
    in command substitution when the substitution is read.

b.  Fixed a bug that caused the shell to dump core when performing programmable
    completion using a shell function.

c.  Fixed a bug in `mapfile' that caused it to invoke callbacks at the wrong
    time.

d.  Fixed a bug that caused the shell to dump core when listing jobs in the
    `exit' builtin.

e.  Fixed several bugs encountered when reading subscripts in associative
    array assignments and expansions.

f.  Fixed a bug that under some circumstances caused an associative array to
    be converted to an indexed array.

g.  Fixed a bug that caused syntax errors and SIGINT interrupts to not set
    $? to a value > 128.

h.  Fixed a bug that caused the shell to remove FIFOs associated with process
    substitution inside shell functions.

i.  Fixed a bug that caused terminal attributes to not be reset when the
    `read' builtin timed out.

j.  Fixed a bug in brace expansion that caused unwanted zero padding of the
    expanded terms.

k.  Fixed a bug that prevented the |& construct from working as intended when
    used with a simple command with additional redirections.

l.  Fixed a bug with the case statement ;& terminator that caused the shell to
    dereference a NULL pointer.

m.  Fixed a bug that caused assignment statements or redirections preceding
    a simple command name to inhibit alias expansion.

n.  Fixed the behavior of `set -u' to conform to the latest Posix interpretation:
    every expansion of an unset variable except $@ and $* will cause the
    shell to exit.

o.  Fixed a bug that caused double-quoted expansions of $* inside word
    expansions like ${x#$*} to not expand properly when $IFS is empty.

p.  Fixed a bug that caused traps to set $LINENO to the wrong value when they
    execute.

q.  Fixed a bug that caused off-by-one errors when computing history lines in
    the `fc' builtin.

r.  Fixed a bug that caused some terminating signals to not exit the shell
    quickly enough, forcing the kernel to send the signal (e.g., SIGSEGV)
    multiple times.

s.  Fixed a bug that caused the shell to attempt to add empty lines to the
    history list when reading here documents.

t.  Made some internal changes that dramatically speeds up sequential indexed
    array access.

u.  Fixed a bug that caused the shell to write past the end of a string when
    completing a double-quoted string ending in a backslash.

v.  Fixed a bug that caused the shell to replace too many characters when a
    pattern match was null in a ${foo//bar} expansion.

w.  Fixed bugs in the expansion of ** that caused duplicate directory names
    and the contents of the current directory to be omitted.

x.  Fixed a bug that caused $? to not be set correctly when referencing an
    unset variable with set -u and set -e enabled.

y.  Fixed a bug caused by executing an external program from the DEBUG trap
    while a pipeline was running.  The effect was to disturb the pipeline
    state, occasionally causing it to hang.

z.  Fixed a bug that caused the ** glob expansion to dump core if it
    encountered an unsearchable directory.

aa. Fixed a bug that caused `command -v' and `command -V' to not honor the
    path set by the -p option.

bb. Fixed a bug that caused brace expansion to take place too soon in some
    compound array assignments.

cc. Fixed a bug that caused programmable completion functions' changes to
    READLINE_POINT to not be reflected back to readline.

dd. Fixed a bug that caused the shell to dump core if a trap was executed
    during a shell assignment statement.

ee. Fixed an off-by-one error when computing the number of positional
    parameters for the ${@:0:n} expansion.

ff. Fixed a problem with setting COMP_CWORD for programmable completion
    functions that could leave it set to -1.

gg. Fixed a bug that caused the ERR trap to be triggered in some cases where
    `set -e' would not have caused the shell to exit.

hh. Fixed a bug that caused changes made by `compopt' to not persist past the
    completion function in which compopt was executed.

ii. Fixed a bug that caused the list of hostname completions to not be cleared
    when HOSTNAME was unset.

jj. Fixed a bug that caused variable expansion in here documents to look in
    any temporary environment.

kk. Bash and readline can now convert file names between precomposed and
    decomposed Unicode on Mac OS X ("keyboard" and file system forms,
    respectively).  This affects filename completion (using new
    rl_filename_rewrite_hook), globbing, and readline redisplay.

ll. The ERR and EXIT traps now see a non-zero value for $? when a parser
    error after set -e has been enabled causes the shell to exit.

mm. Fixed a bug that in brace expansion that caused zero-prefixed terms to
    not contain the correct number of digits.

nn. Fixed a bug that caused the shell to free non-allocated memory when
    unsetting an associative array which had had a value implicitly assigned
    to index "0".

oo. Fixed a memory leak in the ${!prefix@} expansion.

pp. Fixed a bug that caused printf to not correctly report all write errors.

qq. Fixed a bug that caused single and double quotes to act as delimiters
    when splitting a command line into words for programmable completion.

rr. Fixed a bug that caused ** globbing that caused **/path/* to match every
    directory, not just those matching `path'.

ss. Fixed a bug that caused the shell to dump core when running `help' without
    arguments if the terminal width was fewer than 7 characters.

2.  Changes to Readline

a.  The SIGWINCH signal handler now avoids calling the redisplay code if
    one arrives while in the middle of redisplay.

b.  Changes to the timeout code to make sure that timeout values greater
    than one second are handled better.

c.  Fixed a bug in the redisplay code that was triggered by a prompt
    containing invisible characters exactly the width of the screen.

d.  Fixed a bug in the redisplay code encountered when running in horizontal
    scroll mode.

e.  Fixed a bug that prevented menu completion from properly completing
    filenames.

f.  Fixed a redisplay bug caused by a multibyte character causing a line to
    wrap.

g.  Fixed a bug that caused key sequences of two characters to not be
    recognized when a longer sequence identical in the first two characters
    was bound.

h.  Fixed a bug that caused history expansion to be attempted on $'...'
    single-quoted strings.

i.  Fixed a bug that caused incorrect redisplay when the prompt contained
    multibyte characters in an `invisible' sequence bracketed by \[ and
    \].

j.  Fixed a bug that caused history expansion to short-circuit after
    encountering a multibyte character.

3.  New Features in Bash

a.  Here-documents within $(...) command substitutions may once more be
    delimited by the closing right paren, instead of requiring a newline.

b.  Bash's file status checks (executable, readable, etc.) now take file
    system ACLs into account on file systems that support them.

c.  Bash now passes environment variables with names that are not valid
    shell variable names through into the environment passed to child
    processes.

d.  The `execute-unix-command' readline function now attempts to clear and
    reuse the current line rather than move to a new one after the command
    executes.

e.  `printf -v' can now assign values to array indices.

f.  New `complete -E' and `compopt -E' options that work on the "empty"
    completion: completion attempted on an empty command line.

g.  New complete/compgen/compopt -D option to define a `default' completion:
    a completion to be invoked on command for which no completion has been
    defined.  If this function returns 124, programmable completion is
    attempted again, allowing a user to dynamically build a set of completions
    as completion is attempted by having the default completion function
    install individual completion functions each time it is invoked.

h.  When displaying associative arrays, subscripts are now quoted.

i.  Changes to dabbrev-expand to make it more `emacs-like': no space appended
    after matches, completions are not sorted, and most recent history entries
    are presented first.

j.  The [[ and (( commands are now subject to the setting of `set -e' and the
    ERR trap.

k.  The source/. builtin now removes NUL bytes from the file before attempting
    to parse commands.

l.  There is a new configuration option (in config-top.h) that forces bash to
    forward all history entries to syslog.

m.  A new variable $BASHOPTS to export shell options settable using `shopt' to
    child processes.

n.  There is a new configure option that forces the extglob option to be
    enabled by default.

o.  New variable $BASH_XTRACEFD; when set to an integer bash will write xtrace
    output to that file descriptor.

p.  If the optional left-hand-side of a redirection is of the form {var}, the
    shell assigns the file descriptor used to $var or uses $var as the file
    descriptor to move or close, depending on the redirection operator.

q.  The < and > operators to the [[ conditional command now do string
    comparison according to the current locale.

r.  Programmable completion now uses the completion for `b' instead of `a'
    when completion is attempted on a line like: a $(b c.

s.  Force extglob on temporarily when parsing the pattern argument to
    the == and != operators to the [[ command, for compatibility.

t.  Changed the behavior of interrupting the wait builtin when a SIGCHLD is
    received and a trap on SIGCHLD is set to be Posix-mode only.

u.  The read builtin has a new `-N nchars' option, which reads exactly NCHARS
    characters, ignoring delimiters like newline.

4.  New Features in Readline

a.  New bindable function: menu-complete-backward.

b.  In the vi insertion keymap, C-n is now bound to menu-complete by default,
    and C-p to menu-complete-backward.

c.  When in vi command mode, repeatedly hitting ESC now does nothing, even
    when ESC introduces a bound key sequence.  This is closer to how
    historical vi behaves.

d.  New bindable function: skip-csi-sequence.  Can be used as a default to
    consume key sequences generated by keys like Home and End without having
    to bind all keys.

e.  New application-settable function: rl_filename_rewrite_hook.  Can be used
    to rewrite or modify filenames read from the file system before they are
    compared to the word to be completed.

f.  New bindable variable: skip-completed-text, active when completing in the
    middle of a word.  If enabled, it means that characters in the completion
    that match characters in the remainder of the word are "skipped" rather
    than inserted into the line.

g.  The pre-readline-6.0 version of menu completion is available as
    "old-menu-complete" for users who do not like the readline-6.0 version.

h.  New bindable variable: echo-control-characters.  If enabled, and the
    tty ECHOCTL bit is set, controls the echoing of characters corresponding
    to keyboard-generated signals.

i.  New bindable variable: enable-meta-key.  Controls whether or not readline
    sends the smm/rmm sequences if the terminal indicates it has a meta key
    that enables eight-bit characters.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.0-release,
and the previous version, bash-4.0-rc1.

1.  Changes to Bash

a.  Changed the message printed when setlocale(3) fails to only include the
    strerror error text if the call changes errno.

b.  Changed trap command execution to reset the line number before running a
    trap (except DEBUG and RETURN traps).

c.  Fixed behavior of case-modifiying word expansions to not work on
    individual words within a variable's value.

d.  Fixed a bug that caused mapfile to not be interruptible when run in an
    interactive shell.

e.  Fixed a bug that caused mapfile to not run callbacks for the first line
    read.

f.  Fixed a bug that caused mapfile to not honor EOF typed in an interactive
    shell.

g.  Fixed the coprocess reaping code to not run straight from a signal handler.

h.  Fixed a bug that caused printf -b to ignore the first % conversion specifier
    in the format string on 64-bit systems.

i.  Fixed a bug that caused incorrect word splitting when `:', `=', or `~'
    appeared in $IFS.

j.  Fixed a bug that caused data corruption in the programmable completion code
    when a shell function called from a completion aborted execution.

k.  Fixed a bug that caused the CPU usage reported by the `time' builtin to be
    capped at 100%.

l.  Changed behavior of shell when -e option is in effect to reflect consensus
    of Posix shell standardization working group.

m.  Fixed a bug introduced in bash-4.0-alpha that caused redirections to not
    be displayed by `type' or `declare' when appearing in functions under
    certain circumstances.

2.  Changes to Readline

a.  Fixed a bug that caused !(...) extended glob patterns to inhibit later
    history expansion.

b.  Reworked the signal handling to avoid calling disallowed functions from a
    signal handler.

3.  New Features in Bash

a.  `readarray' is now a synonym for `mapfile'.
------------------------------------------------------------------------------
This document details the changes between this version, bash-4.0-rc1,
and the previous version, bash-4.0-beta2.

1.  Changes to Bash

a.  Fixed a bug that caused parsing errors when a $()-style command
    substitution was followed immediately by a quoted newline.

b.  Fixed a bug that caused extended shell globbing patterns beginning with
    `*(' to not work when used with pattern substitution word expansions.
 
------------------------------------------------------------------------------
This document details the changes between this version, bash-4.0-beta2,
and the previous version, bash-4.0-beta.

1.  Changes to Bash

a.  Fixed a bug that caused failed word expansions to set $? but not
    PIPESTATUS.

b.  Changed filename completion to quote the tilde in a filename with a
    leading tilde that exists in the current directory.

c.  Fixed a bug that caused a file descriptor leak when performing
    redirections attached to a compound command.

d.  Fixed a bug that caused expansions of $@ and $* to not exit the shell if
    the -u option was enabled and there were no posititional parameters.

e.  Fixed a bug that resulted in bash not terminating immediately if a
    terminating signal was received while performing output.

f.  Fixed a bug that caused the shell to crash after creating 256 process
    substitutions during word completion.

2.  Changes to Readline

a.  Fixed a bug that caused redisplay errors when using prompts with invisible
    characters and numeric arguments to a command in a multibyte locale.

b.  Fixed a bug that caused redisplay errors when using prompts with invisible
    characters spanning more than two physical screen lines.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.0-beta,
and the previous version, bash-4.0-alpha.

1.  Changes to Bash

a.  Fixed a typo that caused a variable to be used before initialization
    while parsing Posix-style command substitutions.

b.  Fixed a bug that caused stray ^? when the expansion of a parameter used
    as part of a pattern removal expansion is empty, but part of a non-
    empty string.

c.  Fixed a bug that could cause strings not converted to numbers by strtol
    to be treated as if the conversion had been successful.

d.  The `return' builtin now accepts no options and requires a `--' before
    a negative return value, as Posix requires.

e.  Fixed a bug that caused local variables to be created with the empty
    string for a value rather than no value.

f.  Changed behavior so the shell now acts as if it received an interrupt
    when a pipeline is killed by SIGINT while executing a list.

g.  Fixed a bug that caused `declare var' and `typeset var' to initialize
    `var' to the empty string.

h.  Changed `bind' builtin to print a warning but proceed if invoked when
    line editing is not active.

i.  Fixed a bug that caused the shell to exit when the `errexit' option is
    set and a command in a pipeline returns a non-zero exit status.

j.  Fixed a bug that caused the shell to not run the exit trap in a command
    run with `bash -c' under some circumstances.

k.  Fixed a bug that caused parser errors to occasionally not set $? when
    running commands with `eval'.

l.  Fixed a bug that caused stray control characters when evaluating compound
    array assignments containing $'\x7f' escapes.

m.  Fixed a bug that caused redirections involving file descriptor 10 as the
    target to behave incorrectly.

n.  Fixed a bug that could cause memory to be freed multiple times when
    assigning to COMP_WORDBREAKS.

o.  Fixed a bug that could cause NULL pointer dereferences when COMP_WORDBREAKS
    was unset.

2.  Changes to Readline

3.  New Features in Bash

a.  A value of 0 for the -t option to `read' now returns success if there is
    input available to be read from the specified file descriptor.

b.  CDPATH and GLOBIGNORE are ignored when the shell is running in privileged
    mode.

c.  New bindable readline functions shell-forward-word and shell-backward-word,
    which move forward and backward words delimited by shell metacharacters
    and honor shell quoting.

d.  New bindable readline functions shell-backward-kill-word and shell-kill-word
    which kill words backward and forward, but use the same word boundaries
    as shell-forward-word and shell-backward-word.

4.  New Features in Readline

a.  If the kernel supports it, readline displays special characters
    corresponding to a keyboard-generated signal when the signal is received.

------------------------------------------------------------------------------
This document details the changes between this version, bash-4.0-alpha,
and the previous version, bash-3.2-release.

1.  Changes to Bash

a.  Fixed several bugs in old-style `` command substitution parsing, including
    comment parsing and quoted string handling.

b.  Fixed problems parsing arguments to the [[ command's =~ regular expression
    matching operator:  metacharacter and whitespace parsing.

c.  Fixed a bug that caused the shell to inappropriately reuse high-numbered
    file descriptors it used internally.

d.  Fixed a bug in pattern replacement word expansions that caused a `/' as
    the first character of an expanded pattern to be mistaken for a global
    replacement specifier.

e.  Fixed several problems with the asprintf and snprintf replacement functions
    that caused hangs and crashes.

f.  Fixed a bug in the calculation of the current and previous job that caused
    it to refer to incorrect jobs.

g.  Fixed a bug in the check for the validity of a hashed command pathname that
    caused unnecessary hash table deletions and additions.

h.  Fixed a bug that caused child processes to inherit the wrong value for $!.

i.  Fixed a bug that caused `.' to fail to read and execute commands from non-
    regular files such as devices or named pipes.

j.  Fixed a bug in printf formatting for the %x and %X expansions that occurred
    on some systems.

k.  Fixed a bug that caused the shell to crash when creating temporary files if
    $TMPDIR named a non-writable directory.

l.  Fixed a bug that caused the shell to ignore $TMPDIR when creating temporary
    files under some circumstances.

m.  Fixed a bug that caused named pipes created by process substitution to not
    be cleaned up.

n.  Fixed a bug that caused HISTTIMEFORMAT to not be honored when it appeared
    in the initial shell environment.

o.  Fixed several bugs in the expansion of $* and $@ (quoted and unquoted)
    when IFS is null or contains non-whitespace characters; the same changes
    apply to arrays subscripted with * or @.

p.  Fixed several problems with pattern substitution expansions on the
    positional parameters and arrays subscripted with * or @ that occurred
    when $IFS was set to the empty string.

q.  Made a change to the default locale initialization code that should
    result in better behavior from the locale-aware library functions.

r.  Fixed a bug that caused compacting the jobs list to drop jobs.

s.  Fixed a bug that caused jumps back to the top-level processing loop from
    a builtin command to leave the shell in an inconsistent state.

t.  Fixed a bug that caused characters that would be escaped internally to be
    doubled when escaped with a backslash.

u.  Fixed the initialization of mailboxes to not cause maildirs to be read
    (and stat(2) called for every message file) at shell startup.

v.  Fixed a bug that caused the shell to not display $PS2 when the read builtin
    reads a line continued with a backslash.

w.  Fixed a bug that caused errors in word splitting when $IFS contained
    characters used for internal quoting.

x.  Fixed bugs that caused problems with output from shell builtins not being
    completely displayed on some systems.

y.  Fixed a bug that caused output to be lost when a redirection is acting on
    the shell's output file descriptor.

z.  Fixed bugs caused by shell builtins not checking for all write errors.

aa. Fixed a problem that caused the shell to dump core if expansions on the
    pattern passed to the pattern removal word expansions resulted in expansion
    errors.

bb. Fixed a bug that caused bash to loop infinitely after creating and
    waiting for 4096 jobs.

cc. Fixed a bug that caused bash to lose the status of a background job under
    certain circumstances.

dd. Fixed a bug that caused bash to not look in the temporary environment
    when performing variable lookup under certain circumstances.

ee. Fixed a bug that caused bash to close file descriptors greater than 10
    when they were used in redirections.

ff. Fixed a problem that caused the shell to attempt to read from the standard
    input when called as `bash -i script'.

gg. Fixed a memory leak and variable initialization problems when the -v option
    was supplied to `printf' that could cause incorrect results.

hh. Fixed a bug that caused the `read' builtin to count bytes when the -n option
    was supplied, rather than (possibly multibyte) characters.

ii. Fixed a bug when displaying a function due to not converting the function
    to an external form.

jj. Changed job control initialization to ensure that the shell has a tty
    as its controlling terminal before enabling job control.

kk. Fixed a bug with the `test' builtin that caused it to misinterpret
    arguments beginning with `-' but containing more than one character.

ll. Fixed bug that could cause the shell to dump core in certain cases where
    a command sets the SIGINT disposition to the default.

mm. Fixed a bug in the pattern replacement (affecting both word expansion
    and the `fc' builtin) that occurred when the pattern and replacement
    strings were empty.

nn. Fixed a bug that caused an arithmetic evaluation error to disable all
    further evaluation.

oo. Fixed a bug in pathname expansion that caused it to interpret backslashes
    in the pathname as quoting characters.

pp. Fixed a bug in the replacement getcwd() implementation that could cause
    memory to be overwritten.

qq. When in Posix mode, the `ulimit' builtin now uses a block size of 512 for
    the `-c' and `-f' options.

rr. Brace expansion now allows process substitutions to pass through unchanged.

ss. Fixed a problem in the command name completion code to avoid quoting
    escaped special characters twice when the command name begins with a tilde.

tt. Fixed a problem in the printf builtin that resulted in single-byte
    output for the "'" escape, even when using multibyte characters.

uu. Fixed a bug that caused the failure exit status to be lost when redirections
    attached to a compound command failed.

vv. Fixed a bug that caused the internal random number generator to not be
    re-seeded correctly when creating a subshell.

ww. Fixed a bug that could cause the bash replacement getcwd to overwrite
    memory.

xx. Fixed a bug that caused the shell to not receive SIGINT if it was sent
    while the shell was waiting for a command substitution to terminate, and
    make sure the exit status is correct when it does.

yy. Fixed a bug that resulted in the second and subsequent children spawned
    by a shell begun to run a command substitution being placed into the
    wrong process group.

zz. Fixed a bug that caused the results of successful tilde expansion to be
    subject to pathname expansion and word splitting.

aaa. Fixed a bug that could cause the shell to hang if it encountered an
     error that caused it to jump back to the top processing loop during a
     command substitution or `eval' command.

bbb. Fixed a bug that caused the `read' builtin to use the tty's attributes
     instead of those of the file descriptor passed with the -u option when
     processing the -n and -d options.

ccc. Fixed a bug that caused incorrect expansion of ${array[@]:foo} if the
     first character of $IFS was not whitespace.

ddd. Fixed a bug that occurred when scanning for the ending delimiter of a
     ${parameter/pat/sub} expansion.

eee. Fixed a bug that caused the shell to inappropriately expand command
     substitutions in words when expanding directory names for completion.

fff. Fixed a bug that caused the `fc' builtin to look too far back in the
     history list under certain circumstances.

ggg. Fixed a bug that caused a shell running in Posix mode to search $PWD for
     a file specified as an argument to source/. when the file was not found
     in $PATH.

hhh. Fixed a bug that caused the shell to modify the case of a command word
     found via command completion when the shell was performing case-
     insensitive completion.

iii. Fixed a bug that caused the shell to search $PATH for an argument to
     source/. even when it contained a `/'.

jjj. Fixed a bug that caused brace expansion to misorder expansions when the
     locale did not have a collating order like aAbBcC...zZ.

kkk. Fixed a bug that did not allow `set +o history' to have any effect when
     run in a startup file or from a sourced file.

lll. Fixed a bug with the precedence of the ?: conditional arithmetic operator.

mmm. Fixed a bug that caused side effects of temporary variable assignments
     to persist in the shell environment.

nnn. Fixed a bug that caused the terminal to be left in non-canonical mode
     when using editing commands that invoke the an editor on the current
     command line.

ooo. Fixed a bug that caused globbing characters and characters in $IFS to not
     be quoted appropriately when displaying assignment statements.

ppp. Fixed a bug that caused the `-e' option to be inherited when sourcing a
     file or evaluating a command with `eval' even if the return value of the
     command was supposed to be ignored.

qqq. Fixed a bug that caused the shell to attempt to created variables with
     invalid names if such names appeared in the initial environment.

rrr. Fixed a bug with quote removal in strings where the final character is a
     backslash.

sss. Fixed a bug that caused the effects of special variables to persist even
     when the variables were unset as part of the shell reinitializing itself
     to execute a shell script.

ttt. Fixed a bug that caused the history to not be saved after `history -c' or
     `history -d' was executed until a sufficient number of commands had been
     saved to the history.

uuu. Bash now parses command substitutions according to Posix rules: parsing
     the command contained in $() to find the closing delimiter.

vvv. Fixed a bug that caused traps on SIGCHLD set in a SIGCHLD handler to
     not persist.

www. Fixed a bug that didn't allow SIGCHLD to interrupt the `wait' builtin
     as Posix specifies.

xxx. Invalid numeric arguments to shell builtins no longer cause the shell to
     short-circuit any executing compound command.

yyy. Fixed a bug that caused the exit status to be lost when `break' was
     used to short-circuit a loop's execution.

zzz. Fixed a bug that caused stray ^? characters to be left in expansions of
     "${array[*]}".

aaaa. Bash now prints better error messages for here documents terminated by
      EOF and for identifying the incorrect token in an invalid arithmetic
      expression.

bbbb. Fixed a bug in the variable length word expansion that caused it to
      incorrectly calculate the number of multibyte characters.

cccc. Fixed a race condition that could result in the top-level shell setting
      the terminal's process group to an incorrect value if the process
      group was changed by a child of a child of the shell.

dddd. Fixed a bug that caused here documents belonging to commands within a
      compound command to be displayed in a syntactially-incorrect form, which
      prevented them from being re-read as input.

eeee. The shell displays more warnings about failures to set the locale.

ffff. Fixed a bug that caused the body of a here-document to not be saved to
      the history list.

gggg. Fixed a bug that caused configure to incorrectly conclude that FreeBSD
      had /dev/fd available, resulting in problems with process substitution.

2.  Changes to Readline

a.  Fixed a number of redisplay errors in environments supporting multibyte 
    characters.

b.  Fixed bugs in vi command mode that caused motion commands to inappropriately
    set the mark.

c.  When using the arrow keys in vi insertion mode, readline allows movement
    beyond the current end of the line (unlike command mode).

d.  Fixed bugs that caused readline to loop when the terminal has been taken
    away and reads return -1/EIO.

e.  Fixed bugs in redisplay occurring when displaying prompts containing
    invisible characters.

f.  Fixed a bug that caused the completion append character to not be reset to
    the default after an application-specified completion function changed it.

g.  Fixed a problem that caused incorrect positioning of the cursor while in
    emacs editing mode when moving forward at the end of a line while using
    a locale supporting multibyte characters.

h.  Fixed an off-by-one error that caused readline to drop every 511th
    character of buffered input.

i.  Fixed a bug that resulted in SIGTERM not being caught or cleaned up.

j.  Fixed redisplay bugs caused by multiline prompts with invisible characters
    or no characters following the final newline.

k.  Fixed redisplay bug caused by prompts consisting solely of invisible
    characters.

l.  Fixed a bug in the code that buffers characters received very quickly in
    succession which caused characters to be dropped.

m.  Fixed a bug that caused readline to reference uninitialized data structures
    if it received a SIGWINCH before completing initialization.

n.  Fixed a bug that caused the vi-mode `last command' to be set incorrectly
    and therefore unrepeatable.

o.  Fixed a bug that caused readline to disable echoing when it was being used
    with an output file descriptor that was not a terminal.

p.  Readline now blocks SIGINT while manipulating internal data structures
    during redisplay.

q.  Fixed a bug in redisplay that caused readline to segfault when pasting a
    very long line (over 130,000 characters).

r.  Fixed bugs in redisplay when using prompts with no visible printing
    characters.

3.  New Features in Bash

a.  When using substring expansion on the positional parameters, a starting
    index of 0 now causes $0 to be prefixed to the list.

b.  The `help' builtin now prints its columns with entries sorted vertically
    rather than horizontally.

c.  There is a new variable, $BASHPID, which always returns the process id of
    the current shell.

d.  There is a new `autocd' option that, when enabled, causes bash to attempt
    to `cd' to a directory name that is supplied as the first word of a
    simple command.

e.  There is a new `checkjobs' option that causes the shell to check for and
    report any running or stopped jobs at exit.

f.  The programmable completion code exports a new COMP_TYPE variable, set to
    a character describing the type of completion being attempted.

g.  The programmable completion code exports a new COMP_KEY variable, set to
    the character that caused the completion to be invoked (e.g., TAB).

h.  If creation of a child process fails due to insufficient resources, bash
    will try again several times before reporting failure.

i.  The programmable completion code now uses the same set of characters as
    readline when breaking the command line into a list of words.

j.  The block multiplier for the ulimit -c and -f options is now 512 when in
    Posix mode, as Posix specifies.

k.  Changed the behavior of the read builtin to save any partial input received
    in the specified variable when the read builtin times out.  This also
    results in variables specified as arguments to read to be set to the empty
    string when there is no input available.  When the read builtin times out,
    it returns an exit status greater than 128.

l.  The shell now has the notion of a `compatibility level', controlled by
    new variables settable by `shopt'.  Setting this variable currently
    restores the bash-3.1 behavior when processing quoted strings on the rhs
    of the `=~' operator to the `[[' command.

m.  The `ulimit' builtin now has new -b (socket buffer size) and -T (number
    of threads) options.

n.  The -p option to `declare' now displays all variable values and attributes
    (or function values and attributes if used with -f).

o.  There is a new `compopt' builtin that allows completion functions to modify
    completion options for existing completions or the completion currently
    being executed.

p.  The `read' builtin has a new -i option which inserts text into the reply
    buffer when using readline.

q.  A new `-E' option to the complete builtin allows control of the default
    behavior for completion on an empty line.

r.  There is now limited support for completing command name words containing
    globbing characters.

s.  Changed format of internal help documentation for all builtins to roughly
    follow man page format.

t.  The `help' builtin now has a new -d option, to display a short description,
    and a -m option, to print help information in a man page-like format.

u.  There is a new `mapfile' builtin to populate an array with lines from a
    given file.

v.  If a command is not found, the shell attempts to execute a shell function
    named `command_not_found_handle', supplying the command words as the
    function arguments.

w.  There is a new shell option: `globstar'.  When enabled, the globbing code
    treats `**' specially -- it matches all directories (and files within
    them, when appropriate) recursively.

x.  There is a new shell option: `dirspell'.  When enabled, the filename
    completion code performs spelling correction on directory names during
    completion.

y.  The `-t' option to the `read' builtin now supports fractional timeout
    values.

z.  Brace expansion now allows zero-padding of expanded numeric values and
    will add the proper number of zeroes to make sure all values contain the
    same number of digits.

aa. There is a new bash-specific bindable readline function: `dabbrev-expand'.
    It uses menu completion on a set of words taken from the history list.

bb. The command assigned to a key sequence with `bind -x' now sets two new
    variables in the environment of the executed command:  READLINE_LINE_BUFFER
    and READLINE_POINT.  The command can change the current readline line
    and cursor position by modifying READLINE_LINE_BUFFER and READLINE_POINT,
    respectively.

cc. There is a new &>> redirection operator, which appends the standard output
    and standard error to the named file.

dd. The parser now understands `|&' as a synonym for `2>&1 |', which redirects
    the standard error for a command through a pipe.

ee. The new `;&' case statement action list terminator causes execution to
    continue with the action associated with the next pattern in the
    statement rather than terminating the command.

ff. The new `;;&' case statement action list terminator causes the shell to
    test the next set of patterns after completing execution of the current
    action, rather than terminating the command.

gg. The shell understands a new variable: PROMPT_DIRTRIM.  When set to an
    integer value greater than zero, prompt expansion of \w and \W  will
    retain only that number of trailing pathname components and replace
    the intervening characters with `...'.

hh. There are new case-modifying word expansions: uppercase (^[^]) and
    lowercase (,[,]).  They can work on either the first character or
    array element, or globally.  They accept an optional shell pattern
    that determines which characters to modify.  There is an optionally-
    configured feature to include capitalization operators.

ii. The shell provides associative array variables, with the appropriate
    support to create, delete, assign values to, and expand them.

jj. The `declare' builtin now has new -l (convert value to lowercase upon
    assignment) and -u (convert value to uppercase upon assignment) options.
    There is an optionally-configurable -c option to capitalize a value at
    assignment.

kk. There is a new `coproc' reserved word that specifies a coprocess: an
    asynchronous command run with two pipes connected to the creating shell.
    Coprocs can be named.  The input and output file descriptors and the
    PID of the coprocess are available to the calling shell in variables
    with coproc-specific names.

4.  New Features in Readline

a.  A new variable, rl_sort_completion_matches; allows applications to inhibit
    match list sorting (but beware: some things don't work right if
    applications do this).

b.  A new variable, rl_completion_invoking_key; allows applications to discover
    the key that invoked rl_complete or rl_menu_complete.

c.  The functions rl_block_sigint and rl_release_sigint are now public and
    available to calling applications who want to protect critical sections
    (like redisplay).

d.  The functions rl_save_state and rl_restore_state are now public and
    available to calling applications; documented rest of readline's state
    flag values.

e.  A new user-settable variable, `history-size', allows setting the maximum
    number of entries in the history list.

f.  There is a new implementation of menu completion, with several improvements
    over the old; the most notable improvement is a better `completions
    browsing' mode.

g.  The menu completion code now uses the rl_menu_completion_entry_function
    variable, allowing applications to provide their own menu completion
    generators.

h.  There is support for replacing a prefix  of a pathname with a `...' when
    displaying possible completions.  This is controllable by setting the
    `completion-prefix-display-length' variable.  Matches with a common prefix
    longer than this value have the common prefix replaced with `...'.

i.  There is a new `revert-all-at-newline' variable.  If enabled, readline will
    undo all outstanding changes to all history lines when `accept-line' is
    executed.

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.2-release,
and the previous version, bash-3.2-beta.

1.  Changes to Bash

a.  Fixed a bug that caused the temporary environment passed to a command to
    affect the shell's environment under certain circumstances.

b.  Fixed a bug in the printf builtin that caused the %q format specifier to
    ignore empty string arguments.

c.  Improved multibyte character environment detection at configuration time.

d.  Fixed a bug in the read builtin that left spurious escape characters in the
    input after processing backslashes when assigning to an array variable.

2.  Changes to Readline

a.  Fixed a redisplay bug that occurred in multibyte-capable locales when the
    prompt was one character longer than the screen width.
------------------------------------------------------------------------------
This document details the changes between this version, bash-3.2-beta,
and the previous version, bash-3.2-alpha.

1.  Changes to Bash

a.  Changed the lexical analyzer to treat locale-specific blank characters as
    white space.

b.  Fixed a bug in command printing to avoid confusion between redirections and
    process substitution.

c.  Fixed problems with cross-compiling originating from inherited environment
    variables.

d.  Added write error reporting to printf builtin.

e.  Fixed a bug in the variable expansion code that could cause a core dump in
    a multi-byte locale.

f.  Fixed a bug that caused substring expansion of a null string to return
    incorrect results.

g.  BASH_COMMAND now retains its previous value while executing commands as the
    result of a trap, as the documentation states.

2.  Changes to Readline

a.  Fixed a bug with prompt redisplay in a multi-byte locale to avoid redrawing
    the prompt and input line multiple times.

b.  Fixed history expansion to not be confused by here-string redirection.

c.  Readline no longer treats read errors by converting them to newlines, as
    it does with EOF.  This caused partial lines to be returned from readline().

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.2-alpha,
and the previous version, bash-3.1-release.

1.  Changes to Bash

a.  Fixed a source bug that caused the minimal configuration to not compile.

b.  Fixed memory leaks in error handling for the `read' builtin.

c.  Changed the [[ and (( compound commands to set PIPESTATUS with their exit
    status.

d.  Fixed some parsing problems with compound array assignments.

e.  Added additional configuration changes for: NetBSD (incomplete multibyte
    character support)

f.  Fixed two bugs with local array variable creation when shadowing a variable
    of the same name from a previous context.

g.  Fixed the `read' builtin to restore the correct set of completion functions
    if a timeout occurs.

h.  Added code to defer the initialization of HISTSIZE (and its stifling of the
    history list) until the history file is loaded, allowing a startup file to
    override the default value.

i.  Tightened up the arithmetic expression parsing to produce better error
    messages when presented with invalid operators.

j.  Fixed the cross-compilation support to build the signal list at shell
    invocation rather than compile time if cross-compiling.

k.  Fixed multibyte support for non-gcc compilers (or compilers that do not
    allow automatic array variable sizing based on a non-constant value).

l.  Several fixes to the code that manages the list of terminated jobs and
    their exit statuses, and the list of active and recently-terminated jobs
    to avoid pid aliasing/wraparound and allocation errors.

m.  Fixed a problem that allowed scripts to die due to SIGINT while waiting
    for children, even when started in the background or otherwise ignoring
    SIGINT.

n.  Fixed a bug that caused shells invoked as -/bin/bash from not being
    recognized as login shells.

o.  Fixed a problem that caused shells in the background to give the terminal
    to a process group other than the foreground shell process group.

p.  Fixed a problem with extracting the `varname' in ${#varname}.

q.  Fixed the code that handles SIGQUIT to not exit immediately -- thereby
    calling functions that may not be called in a signal handler context --
    but set a flag and exit afterward (like SIGINT).

r.  Changed the brace expansion code to skip over braces that don't begin a
    valid matched brace expansion construct.

s.  Fixed `typeset' and `declare' to not require that their shell function
    operands to be valid shell identifiers.

t.  Changed `test' to use access(2) with a temporary uid/euid swap when testing
    file attributes and running setuid, and access(2) in most other cases.

u.  Changed completion code to not attempt command name completion on a line
    consisting solely of whitespace when no_empty_command_completion is set.

v.  The `hash' builtin now prints nothing in posix mode when the hash table is
    empty, and prints a message to that effect to stdout instead of stderr
    when not in posix mode.

w.  Fixed a bug in the extended pattern matching code that caused it to fail to
    match periods with certain patterns.

x.  Fixed a bug that caused the shell to dump core when performing filename
    generation in directories with thousands of files.

y.  Returned to the original Bourne shell rules for parsing ``:  no recursive
    parsing of embedded quoted strings or ${...} constructs.

z.  The inheritance of the DEBUG, RETURN, and ERR traps is now dependent only
    on the settings of the `functrace' and `errtrace' shell options, rather
    than whether or not the shell is in debugging mode.

aa. Fixed a problem with $HOME being converted to ~ in the expansion of
    members of the DIRSTACK array.

bb. Fixed a problem with quoted arguments to arithmetic expansions in certain
    constructs.

cc. The command word completion code now no longer returns matching directories
    while searching $PATH.

dd. Fixed a bug with zero-padding and precision handling in snprintf()
    replacement.

ee. Fixed a bug that caused the command substitution code not to take embedded
    shell comments into account.

ff. Fixed a bug that caused $((...);(...)) to be misinterpreted as an
    arithmetic substitution.

gg. Fixed a bug in the prompt expansion code that inappropriately added a
    \001 before a \002 under certain circumstances.

hh. Fixed a bug that caused `unset LANG' to not properly reset the locale
    (previous versions would set the locale back to what it was when bash
    was started rather than the system's "native" locale).

ii. Fixed a bug that could cause file descriptors > 10 to not be closed even
    when closed explicitly by a script.

jj. Fixed a bug that caused single quotes to be stripped from ANSI-C quoting
    inside double-quoted command substitutions.

kk. Fixed a bug that could cause core dumps when `return' was executed as the
    last element of a pipeline inside a shell function.

ll. Fixed a bug that caused DEBUG trap strings to overwrite commands stored in
    the jobs list.

2.  Changes to Readline

a.  Fixed a problem that caused segmentation faults when using readline in
    callback mode and typing consecutive DEL characters on an empty line.

b.  Fixed several redisplay problems with multibyte characters, all having to
    do with the different code paths and variable meanings between single-byte
    and multibyte character redisplay.

c.  Fixed a problem with key sequence translation when presented with the
    sequence \M-\C-x.

d.  Fixed a problem that prevented the `a' command in vi mode from being
    undone and redone properly.

e.  Fixed a problem that prevented empty inserts in vi mode from being undone
    properly.

f.  Fixed a problem that caused readline to initialize with an incorrect idea
    of whether or not the terminal can autowrap.

g.  Fixed output of key bindings (like bash `bind -p') to honor the setting of
    convert-meta and use \e where appropriate.

h.  Changed the default filename completion function to call the filename
    dequoting function if the directory completion hook isn't set.  This means
    that any directory completion hooks need to dequote the directory name,
    since application-specific hooks need to know how the word was quoted,
    even if no other changes are made.

i.  Fixed a bug with creating the prompt for a non-interactive search string
    when there are non-printing characters in the primary prompt.

j.  Fixed a bug that caused prompts with invisible characters to be redrawn
    multiple times in a multibyte locale.

k.  Fixed a bug that could cause the key sequence scanning code to return the
    wrong function.

l.  Fixed a problem with the callback interface that caused it to fail when
    using multi-character keyboard macros.

m.  Fixed a bug that could cause a core dump when an edited history entry was
    re-executed under certain conditions.

n.  Fixed a bug that caused readline to reference freed memory when attmpting
    to display a portion of the prompt.

3.  New Features in Bash

a.  Changed the parameter pattern replacement functions to not anchor the
    pattern at the beginning of the string if doing global replacement - that
    combination doesn't make any sense.

b.  When running in `word expansion only' mode (--wordexp option), inhibit
    process substitution.

c.  Loadable builtins now work on MacOS X 10.[34].

d.  Shells running in posix mode no longer set $HOME, as POSIX requires.

e.  The code that checks for binary files being executed as shell scripts now
    checks only for NUL rather than any non-printing character.

f.  Quoting the string argument to the [[ command's  =~ operator now forces
    string matching, as with the other pattern-matching operators.

4.  New Features in Readline

a.  Calling applications can now set the keyboard timeout to 0, allowing
    poll-like behavior.

b.  The value of SYS_INPUTRC (configurable at compilation time) is now used as
    the default last-ditch startup file.

c.  The history file reading functions now allow windows-like \r\n line
    terminators.

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.1-release,
and the previous version, bash-3.1-rc2.

1.  Changes to Readline

a.  Several changes to the multibyte redisplay code to fix problems with
    prompts containing invisible characters.

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.1-rc2,
and the previous version, bash-3.1-rc1.

1.  Changes to Bash

a.  Fixed a bug that caused a DEBUG trap to overwrite a command string that's
    eventually attached to a background job.

b.  Changed some code so that filenames with leading tildes with spaces in the
    name aren't tilde-expanded by the bash completion code.

c.  Fixed a bug that caused the pushd builtin to fail to change to
    directories with leading `-'.

d.  Fixed a small memory leak in the programmable completion code.

2.  Changes to Readline

a.  Fixed a redisplay bug caused by moving the cursor vertically to a line
    with invisible characters in the prompt in a multibyte locale.

b.  Fixed a bug that could cause the terminal special chars to be bound in the
    wrong keymap in vi mode.

3.  New Features in Bash

a.  If compiled for strict POSIX conformance, LINES and COLUMNS may now
    override the true terminal size.

4.  New Features in Readline

a.  A new external application-controllable variable that allows the LINES
    and COLUMNS environment variables to set the window size regardless of
    what the kernel returns.

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.1-rc1,
and the previous version, bash-3.1-beta1.

1.  Changes to Bash

a.  Fixed a bug that could cause core dumps due to accessing the current
    pipeline while in the middle of modifying it.

b.  Fixed a bug that caused pathnames with backslashes still quoting characters
    to be passed to opendir().

c.  Command word completion now obeys the setting of completion-ignore-case.

d.  Fixed a problem with redirection that caused file descriptors greater than
    2 to be inappropriately marked as close-on-exec.

e.  In Posix mode, after `wait' is called to wait for a particular process
    explicitly, that process is removed from the list of processes known to
    the shell, and subsequent attempts to wait for it return errors.

f.  Fixed a bug that caused extended pattern matching to incorrectly scan
    backslash-escaped pattern characters.

g.  Fixed a synchronization problem that could cause core dumps when handling
    a SIGWINCH.

h.  Fixed a bug that caused an unmatched backquote to be accepted without an
    error when processing here documents.

i.  Fixed a small memory leak in the `cd' builtin.

j.  Fix for MacOS X so it gets the values for the HOSTTYPE, MACHTYPE, and
    OSTYPE variables at build time, to support universal binaries.

k.  Fixed a bug that could cause an exit trap to return the exit status of
    the trap command rather than the status as it was before the trap was
    run as the shell's exit status.

2.  New Features in Bash

3.  Changes to Readline

a.  Fixed a bug that caused reversing the incremental search direction to
    not work correctly.

b.  Fixed the vi-mode `U' command to only undo up to the first time insert mode
    was entered, as Posix specifies.

c.  Fixed a bug in the vi-mode `r' command that left the cursor in the wrong
    place.

4.  New Features in Readline

a.  New application-callable auxiliary function, rl_variable_value, returns
    a string corresponding to a readline variable's value.

b.  When parsing inputrc files and variable binding commands, the parser
    strips trailing whitespace from values assigned to boolean variables
    before checking them.


------------------------------------------------------------------------------
This document details the changes between this version, bash-3.1-beta1,
and the previous version, bash-3.1-alpha1.

1.  Changes to Bash

a.  Added some system-specific signal names.

b.  Fixed a typo in the ulimit builtin to make `x' the right option to
    manipulate the limit on file locks.

c.  Fixed a problem with using += to append to index 0 of an array variable
    when not using subscript syntax.

d.  A few changes to configure.in to remove calls to obsolete or outdated
    macros.

e.  Make sure changes to variables bash handles specially (e.g., LC_ALL) are
    made when the variable is set in the temporary environment to a command.

f.  Make sure changes to variables bash handles specially (e.g., LC_ALL) are
    made when the variable is modified using `printf -v'.

g.  The export environment is now remade on cygwin when HOME is changed, so
    DLLs bash is linked against pick up the new value.  This fixes problems
    with tilde expansion when linking against and already-installed readline.

h.  Small fix to the logic for performing tilde expansion in posix mode, so
    expansion on the right-hand side of an assignment statement takes place.

i.  Fixed a bug that prevented redirections associated with a shell function
    from being executed when in a subshell.

j.  Fixed `source' and `.' builtins to not require an executable file when
    searching $PATH for a file to source.

k.  Fixed a bug that caused incorrect word splitting in a function when IFS
    was declared local, then unset.

l.  Fixed a problem with the `kill' builtin that prevented sending signals
    to a process group under certain circumstances when providing a pid < 0.

m.  When in POSIX mode, `pwd' now checks that the value it prints is the same
    directory as `.', even when displaying $PWD.

n.  Fixed a problem with the `read' builtin when reading a script from standard
    input and reading data from the same file.

o.  Fixed a problem with the `type' and `command' builtins that caused absolute
    pathnames to be displayed incorrectly.

p.  Some changes to the `bg' builtin for POSIX conformance.

q.  The `fc' builtin now removes the `fc' command that caused it to invoke an
    editor on specified history entries from the history entirely, rather than
    simply ignoring it.

r.  When in POSIX mode, the `v' command in vi editing mode simply invokes vi
    on the current command, rather than checking $FCEDIT and $EDITOR.

s.  Fixed a small memory leak in the pathname canonicalization code.

t.  Fixed a bug that caused the expanded value of a $'...' string to be
    incorrectly re-quoted if it occurred within a double-quoted ${...}
    parameter expansion.

u.  Restored default emacs-mode key binding of M-TAB to dynamic-complete-history.

v.  Fixed a bug that caused core dumps when interrupting loops running builtins
    on some systems.

w.  Make sure that some of the functions bash provides replacements for are
    not cpp defines.

x.  The code that scans embedded commands for the parser (`...` and $(...)) is
    now more aware of embedded comments and their effect on quoted strings.

y.  Changed the `-n' option to the `history' builtin to not reset the number of
    history lines read in the current session after reading the new lines from
    the history file if the history is being appended when it is written to
    the file, since the appending takes care of the problem that the adjustment
    was intended to solve.

z.  Improved the error message displayed when a shell script fails to execute
    because the environment and size of command line arguments are too large.

aa. A small fix to make sure that $HISTCMD is evaluated whenever the shell is
    saving commands to the history list, not just when HISTSIZE is defined.

2.  Changes to Readline

a.  The `change-case' command now correctly changes the case of multibyte
    characters.

b.  Changes to the shared library construction scripts to deal with Windows
    DLL naming conventions for Cygwin.

c.  Fixed the redisplay code to avoid core dumps resulting from a poorly-timed
    SIGWINCH.

d.  Fixed the non-incremental search code in vi mode to dispose of any current
    undo list when copying a line from the history into the current editing
    buffer.

e.  The variable assignment code now ignores whitespace at the end of lines
    when assigning to boolean variables.

f.  The `C-w' binding in incremental search now understands multibyte
    characters.

3.  New Features in Bash

a.  A new configuration option, `--enable-strict-posix-default', which will
    build bash to be POSIX conforming by default.

4.  New Features in Readline

a.  If the rl_completion_query_items is set to a value < 0, readline never
    asks the user whether or not to view the possible completions.

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.1-alpha1,
and the previous version, bash-3.0-release.

1.  Changes to Bash

a.  Fixed a bug that caused bash to crash if referencing an unset local array.

b.  Fixed a problem that caused tilde expansion to not be performed before
    attempting globbing word completion.

c.  Fixed an incompatibility so that a first argument to trap that's a valid
    signal number will be trated as a signal rather than a command to execute.

d.  Fixed ${#word} expansion to correctly compute the length of a string
    containing multibyte characters.

e.  Fixed a bug that caused bash to not pass the correct flags for signal
    disposition to child processes.

f.  Fixed a bug that caused `fc -l' to list one too many history entries.

g.  Some fixes to `fc' for POSIX conformance.

h.  Some fixes to job status display for POSIX conformance.

i.  Fixed a bug that caused `command -v' to display output if a command was not
    found -- it should be silent.

j.  In POSIX mode, `type' and `command -[vV]' do not report non-executable
    files, even if the shell will attempt to execute them.

k.  Fixed a bug that caused the `==' operator to the [[ command to not attempt
    extended pattern matching.

l.  Fixed the brace expansion code to handle characters whose value exceeds 128.

m.  Fixed `printf' to handle strings with a leading `\0' whose length is
    non-zero.

n.  Fixed a couple of problems with brace expansion where `${' was handled
    incorrectly.

o.  Fixed off-by-one error when calculating the upper bound of `offset' when
    processing the ${array[@]:offset:length} expansion.

p.  System-specific configuration changes for: FreeBSD 5.x, Interix, MacOS X
    10.4, Linux 2.4+ kernels, Linux 3.x kernels, Dragonfly BSD, QNX 6.x,
    Cygwin

q.  Fixed a bug that caused the shell to ignore the status of the rightmost
    command in a pipeline when the `pipefail' option was enabled.

r.  Fixed a completion bug that caused core dumps when expanding a directory
    name.

s.  Fixed a bug that prevented `hash -d' from removing commands from the hash
    table.

t.  Fixed word splitting to avoid really bad quadratic performance when
    expanding long lists.

u.  Fixed a bug that caused negative offsets in substring expansion to use the
    wrong values.

v.  Fixed a bug in printf that caused it to not return failure on write errors.

w.  Fixed a bug that caused commands in subshells to not be properly timed.

x.  The shell parser no longer attempts to parse a compound assignment specially
    unless in a position where an assignment statement is acceptable or parsing
    arguments to a builtin that accepts assignment statements.

y.  Fixed a problem that caused a `case' statement to be added to the history
    incorrectly as a single command if the `case word' was on one line and the
    `in' on another.

z.  Fixed a problem that caused internal shell quoting characters to be
    incorrectly quoted with backslashes under some circumstances.

aa. The shell now performs correct word splitting when IFS contains multibyte
    characters.

bb. The mail checking code now resets the cached file information if the size
    drops to 0, even if the times don't change.

cc. A completed command name that is found in $PATH as well as the name of a
    directory in the current directory no longer has a slash appended in certain
    circumstances:  a single instance found in $PATH when `.' is not in $PATH,
    and multiple instances found in $PATH, even when `.' is in $PATH.

dd. Incorporated tilde expansion into the word expansion code rather than as a
    separately-called function, fixing some cases where it was performed
    inappropriately (e.g., after the second `=' in an assignment statement or
    in a double-quoted parameter expansion).

ee. Fixed several bugs encountered when parsing compound assignment statements,
    so that compound assignments appearing as arguments to builtins are no
    longer double-expanded.

ff. Fixed a bug in the command execution code that caused asynchronous commands
    containing command substitutions to not put the terminal in the wrong
    process group.

gg. Bash now handles the case where the WCONTINUED flag causes waitpid() to
    return -1/EINVAL at runtime as well as configuration time.

hh. Fixed parser to generate an error when the pipeline `argument' to `!' or
    `time' is NULL.

ii. The shell now takes a little more care when manipulating file descriptors
    greater than 9 with the `exec' builtin.

jj. Fixed a bug that caused variable assignments preceding the `command' builtin
    preceding a special builtin to be preserved after the command completed in
    POSIX mode.

kk. Fixed a bug that allowed variables beginning with a digit to be created.

ll. Fixed a bug that caused a \<newline> to be removed when parsing a $'...'
    construct.

mm. A shell whose name begins with `-' will now be a restricted shell if the
    remainder of the name indicates it should be restricted.

nn. Fixed a bug that could cause a core dump if FUNCNAME were changed or unset
    during a function's execution.

oo. Fixed a bug that caused executing a `return' in a function to not execute
    a RETURN trap.  The RETURN trap is inherited by shell functions only if
    function tracing is globally enabled or has been enabled for that function.

pp. Fixed cases where var[@] was not handled exactly like var, when var is a
    scalar variable.

qq. Fixed a bug that caused the first character after a SIGINT to be discarded
    under certain circumstances.

rr. Fixed exit status code so that a suspended job returns 128+signal as its
    exit status (preventing commands after it in `&&' lists from being
    executed).

ss. Fixed a bug that caused the shell parser state to be changed by executing
    a shell function as a result of word completion.

tt. Fixed a long-standing bug that caused '\177' characters in variable
    values to be discarded when expanded in double-quoted strings.

uu. Fixed a bug that caused $RANDOM to be re-seeded multiple times in a
    subshell environment.

vv. Extensive changes to the job management code to avoid the pid-reuse and
    pid-aliasing problems caused by retaining the exit status of too many jobs,
    but still retain as many background job statuses as POSIX requires.

ww. Fixed a parser bug in processing \<newline> that caused things like

		((echo 5) \
		 (echo 6))

    to not work correctly.

xx. `pwd -P' now sets $PWD to a directory name containing no symbolic links
    when in posix mode, as POSIX requires.

yy. In posix mode, bash no longer sets $PWD to a name containing no symbolic
    links if a directory is chosen from $CDPATH.

zz. The word splitting code now treats an IFS character that is not space,
    tab, or newline and any adjacent IFS white space as a single delimiter, as
    SUSv3/XPG6 require.

aaa. The `read' builtin now checks whether or not the number of fields read is
     exactly the same as the number of variables instead of just assigning the
     rest of the line (minus any trailing IFS white space) to the last
     variable.  This is what POSIX/SUS/XPG all require.

bbb. Fixed a bug that caused `read' to always check whether or not fd 0 was a
     pipe, even when reading from another file descriptor.

ccc. Fixed a bug that caused short-circuiting of execution even if the return
     value was being inverted.

ddd. Fixed a bug that caused a core dump while decoding \W escapes in PS1 if
     PWD was unset.

eee. Fixed a bug in `read' that counted internal quoting characters for the
     purposes of `read -n'.

fff. Fixed a bug so that a function definition in a pipeline causes a child
     process to be forked at the right time.

ggg. Bash will not attempt to link against a readline library that doesn't
     have rl_gnu_readline_p == 1.

hhh. Fixed a bug that caused `read' to consume one too many characters when
     reading a fixed number of characters and the Nth character is a backslash.

iii. Fixed a bug that caused `unset' on variables in the temporary environment
     to leave them set when `unset' completed.

jjj. Fixed a bug that caused bash to close fd 2 if an `exec' failed and the
     shell didn't exit.

kkk. The completion code is more careful to not turn `/' or `///' into `//',
     for those systems on which `//' has special meaning.

lll. Fixed a bug that caused command substitution in asynchronous commands to
     close the wrong file descriptors.

mmm. The shell no longer prints status messages about terminated background
     processes unless job control is active.

nnn. Fixed a bug that prevented multiple consecutive invocations of `history -s'
     from adding all the commands to the history list.

ooo. Added a couple of changes to make arithmetic expansion more consistent in
     all its contexts (still not perfect).

ppp. Fixed a bug that caused the parser to occasionally not find the right
     terminating "`" in an old-style command substitution.

qqq. Fixed a bug that caused core dumps when the shell was reading its non-
     interactive input from fd 0 and fd 0 was duplicated and restored using a
     combination of `exec' (to save) and redirection (to restore).

rrr. Fixed a problem that caused loops in sourced scripts to not be cleaned
     up properly when a `return' is executed.

sss. Change internal command substitution completion function to append a slash
     to directory names in the command.

2.  Changes to Readline

a.  Fixed a bug that caused multiliine prompts to be wrapped and displayed
    incorrectly.

b.  Fixed a bug that caused ^P/^N in emacs mode to fail to display the current
    line correctly.

c.  Fixed a problem in computing the number of invisible characters on the first
    line of a prompt whose length exceeds the screen width.

d.  Fixed vi-mode searching so that failure preserves the current line rather
    than the last line in the history list.

e.  Fixed the vi-mode `~' command (change-case) to have the correct behavior at
    end-of-line when manipulating multibyte characters.

f.  Fixed the vi-mode `r' command (change-char) to have the correct behavior at
    end-of-line when manipulating multibyte characters.

g.  Fixed multiple bugs in the redisplay of multibyte characters:  displaying
    prompts longer than the screen width containing multibyte characters, 

h.  Fix the calculation of the number of physical characters in the prompt
    string when it contains multibyte characters.

i.  A non-zero value for the `rl_complete_suppress_append' variable now causes
    no `/' to be appended to a directory name.

j.  Fixed forward-word and backward-word to work when words contained
    multibyte characters.

k.  Fixed a bug in finding the delimiter of a `?' substring when performing
    history expansion in a locale that supports multibyte characters.

l.  Fixed a memory leak caused by not freeing the timestamp in a history entry.

m.  Fixed a bug that caused "\M-x" style key bindings to not obey the setting
    of the `convert-meta' variable.

n.  Fixed saving and restoring primary prompt when prompting for incremental
    and non-incremental searches; search prompts now display multibyte
    characters correctly.

o.  Fixed a bug that caused keys originally bound to self-insert but shadowed
    by a multi-character key sequence to not be inserted.

p.  Fixed code so rl_prep_term_function and rl_deprep_term_function aren't
    dereferenced if NULL (matching the documentation).

q.  Extensive changes to readline to add enough state so that commands
    requiring additional characters (searches, multi-key sequences, numeric
    arguments, commands requiring an additional specifier character like
    vi-mode change-char, etc.) work without synchronously waiting for
    additional input.

r.  Lots of changes so readline builds and runs on MinGW.

s.  Readline no longer tries to modify the terminal settings when running in
    callback mode.

t.  The Readline display code no longer sets the location of the last invisible
    character in the prompt if the \[\] sequence is empty.

3.  New Features in Bash

a.  Bash now understands LC_TIME as a special variable so that time display
    tracks the current locale.

b.  BASH_ARGC, BASH_ARGV, BASH_SOURCE, and BASH_LINENO are no longer created
    as `invisible' variables and may not be unset.

c.  In POSIX mode, if `xpg_echo' option is enabled, the `echo' builtin doesn't
    try to interpret any options at all, as POSIX requires.

d.  The `bg' builtin now accepts multiple arguments, as POSIX seems to specify.

e.  Fixed vi-mode word completion and glob expansion to perform tilde
    expansion.

f.  The `**' mathematic exponentiation operator is now right-associative.

g.  The `ulimit' builtin has new options: -i (max number of pending signals),
    -q (max size of POSIX message queues), and -x (max number of file locks).

h.  A bare `%' once again expands to the current job when used as a job
    specifier.

i.  The `+=' assignment operator (append to the value of a string or array) is
    now supported for assignment statements and arguments to builtin commands
    that accept assignment statements.

j.  BASH_COMMAND now preserves its value when a DEBUG trap is executed.

k.  The `gnu_errfmt' option is enabled automatically if the shell is running
    in an emacs terminal window.

l.  New configuration option:  --single-help-strings.  Causes long help text
    to be written as a single string; intended to ease translation.

m.  The COMP_WORDBREAKS variable now causes the list of word break characters
    to be emptied when the variable is unset.

n.  An unquoted expansion of $* when $IFS is empty now causes the positional
    parameters to be concatenated if the expansion doesn't undergo word
    splitting.

o.  Bash now inherits $_ from the environment if it appears there at startup.

p.  New shell option: nocasematch.  If non-zero, shell pattern matching ignores
    case when used by `case' and `[[' commands.

q.  The `printf' builtin takes a new option: -v var.  That causes the output
    to be placed into var instead of on stdout.

r.  By default, the shell no longer reports processes dying from SIGPIPE.

s.  Bash now sets the extern variable `environ' to the export environment it
    creates, so C library functions that call getenv() (and can't use the
    shell-provided replacement) get current values of environment variables.

4.  New Features in Readline

a.  The key sequence sent by the keypad `delete' key is now automatically
    bound to delete-char.

b.  A negative argument to menu-complete now cycles backward through the
    completion list.

c.  A new bindable readline variable:  bind-tty-special-chars.  If non-zero,
    readline will bind the terminal special characters to their readline
    equivalents when it's called (on by default).

d.  New bindable command: vi-rubout.  Saves deleted text for possible
    reinsertion, as with any vi-mode `text modification' command; `X' is bound
    to this in vi command mode.

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.0-release,
and the previous version, bash-3.0-rc1.

1.  Changes to Bash

a.  Fixed a boundary overrun that could cause segmentation faults when the
    completion code hands an incomplete construct to the word expansion
    functions.

b.  Changed posix mode behavior so that an error in a variable assignment
    preceding a special builtin causes a non-interactive shell to exit.

c.  Change the directory expansion portion of the completion code to not
    expand embedded command substitutions if the directory name appears in
    the file system.

d.  Fixed a problem that caused `bash -r' to turn on restrictions before
    reading the startup files.

e.  Fixed a problem with the default operation of the `umask' builtin.

2.  Changes to Readline

a.  Fixed a problem with readline saving the contents of the current line
    before beginning a non-interactive search.

b.  Fixed a problem with EOF detection when using rl_event_hook.

c.  Fixed a problem with the vi mode `p' and `P' commands ignoring numeric
    arguments.

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.0-rc1,
and the previous version, bash-3.0-beta1.

1.  Changes to Bash

a.  Fixed a bug that caused incorrect behavior when referecing element 0 of
    an array using $array, element 0 was unset, and `set -u' was enabled.

b.  System-specific changes for: SCO Unix 3.2, Tandem.

c.  Fixed a bug that caused inappropriate word splitting when a variable was
    expanded within a double-quoted string that also included $@.

d.  Fixed a bug that caused `pwd' to not display anything in physical mode
    when the file system had changed underneath the shell.

e.  Fixed a bug in the pre- and post- increment and decrement parsing in the
    expression evaluator that caused errors when the operands and corresponding
    operators were separated by whitespace.

f.  Fixed a bug that caused `history -p' to add an entry to the history list,
    counter to the documentation.  (Keeps the history expansions invoked by
    emacs-mode command line editing from doing that as well.)

g.  Fixed a bug that could cause a core dump if `cd' is asked to print out a
    pathname longer than PATH_MAX characters.

h.  Fixed a bug that caused jobs to be put into the wrong process group under
    some circumstances after enabling job control with `set -m'.

i.  `unalias' now  returns failure if no alias name arguments are supplied.

j.  Documented the characters not allowed to appear in an alias name.

k.  $* is no longer expanded as if in double quotes when it appears in the
    body of a here document, as the SUS seems to require.

l.  The `bashbug' script now uses a directory in $TMPDIR for exclusive
    access rather than trying to guess how the underlying OS provides for
    secure temporary file creation.

m.  Fixed a few problems with `cd' and `pwd' when asked to operate on pathnames
    longer than PATH_MAX characters.

n.  Fixed a memory leak caused when creating multiple local array variables
    with identical names.

o.  Fixed a problem with calls to getcwd() so that bash now operates better
    when the full pathname to the current directory is longer than PATH_MAX
    bytes.

p.  The `trap' builtin now reports an error if a single non-signal argument
    is specified.

q.  Fixed a bug that caused `umask' to not work correctly when presented
    with a mask of all 0s.

r.  When `getopts' reaches the end of options, OPTARG is unset, as POSIX
    appears to specify.

s.  Interactive mode now depends on whether or not stdin and stderr are
    connected to a tty; formerly it  was stdin and stdout.  POSIX requires
    this.

t.  Fixed vi-mode completion to work more as POSIX specifies (e.g., doing the
    right kind of filename generation).

2.  Changes to Readline

a.  Fixed a problem that could cause readline to refer to freed memory when
    moving between history lines while doing searches.

b.  Improvements to the code that expands and displays prompt strings
    containing multibyte characters.

c.  Fixed a problem with vi-mode not correctly remembering the numeric argument
    to the last `c'hange command for later use with `.'.

d.  Fixed a bug in vi-mode that caused multi-digit count arguments to work
    incorrectly.

e.  Fixed a problem in vi-mode that caused the last text modification command
    to not be remembered across different command lines.

f.  Fixed problems with changing characters and changing case at the end of
    the line.

3.  New Features in Bash

a.  The `jobs', `kill', and `wait' builtins now accept job control notation
    even if job control is not enabled.

b.  The historical behavior of `trap' that allows a missing `action' argument
    to cause each specified signal's handling to be reset to its default is
    now only supported when `trap' is given a single non-option argument.

4.  New Features in Readline

a.  When listing completions, directories have a `/' appended if the
    `mark-directories' option has been enabled.

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.0-beta1,
and the previous version, bash-3.0-alpha.

1.  Changes to Bash

a.  Fixes to build correctly when arrays are not compiled into the shell.

b.  Fixed command substitution to run any exit trap defined in the command
    substitution before returning; the exit trap is not inherited from the
    calling shell.

c.  Fixes to process group synchronization code so that every child process
    attempts to set the terminal's process group; fixes some synchronization
    problems on Linux kernels that schedule the child to always run before
    the parent.

d.  Fixed processing of octal and hex constants in printf builtin for POSIX.2
    compliance.

e.  Fixed a couple of core dumps in the pattern removal code.

f.  Fixes to the array subrange extraction code to deal better with sparse
    arrays.

g.  Parser errors and other errors that result in the shell exiting now cause
    the exit trap to be run.

h.  Change the command substitution completion functions to not append any
    closing quote, because it would be inserted a closing "`" or ")".

i.  Fix history initialization so assignments to $histchars made in startup
    files are honored.

j.  If an exit trap does not contain a call to `exit', the shell now uses
    the exit status of the last command executed before the trap as the exit
    status of the shell.

k.  The parser now prompts with $PS2 if it reads a newline while parsing a
    compound array assignment statement.

l.  When performing a compound array assignment, the parser doesn't treat
    words of the form [index]=value as assignments if they're the result of
    expansions.

m.  Fixed a bug that caused `return' executed in a trap command to make the
    shell think it was still running the trap.

n.  Fixed the value of errno set by the pathname canonicalization functions.

o.  Changed the grammar so that `time' alone on a line times a null command
    rather than being a syntax error.

p.  The pattern substitution code no longer performs quote removal on the
    pattern before trying to match it, as the pattern removal functions do.

q.  Fixed a bug that could cause core dumps when checking whether a quoted
    command name was being completed.

r.  Fixes to the pattern removal and pattern replacement expansions to deal
    with multibyte characters better (and faster).

s.  Fix to the substring expansion (${param:off[:len]}) to deal with (possibly
    multibyte) characters instead of raw bytes.

t.  Fixed a bug that caused some key bindings set in an inputrc to be ignored
    at shell startup.

u.  Fixed a bug that caused unsetting a local variable within a function to
    not work correctly.

v.  Fixed a bug that caused invalid variables to be created when using
    `read -a'.

w.  Fixed a bug that caused "$@" to expand incorrectly when used as the right
    hand side of a parameter expansion such as ${word:="$@"} if the first
    character of $IFS was not a space.

x.  Fixed a slight cosmetic problem when printing commands containing a
    `>&word' redirection.

y.  Fixed a problem that could cause here documents to not be created correctly
    if the system temporary directory did not allow writing.

2.  Changes to Readline

a.  Change to history expansion functions to treat `^' as equivalent to word
    one, as the documentation states.

b.  Some changes to the display code to improve display and redisplay of
    multibyte characters.

c.  Changes to speed up the multibyte character redisplay code.

d.  Fixed a bug in the vi-mode `E' command that caused it to skip over the
    last character of a word if invoked while point was on the word's
    next-to-last character.

e.  Fixed a bug that could cause incorrect filename quoting when
    case-insensitive completion was enabled and the word being completed
    contained backslashes quoting word break characters.

f.  Fixed a bug in redisplay triggered when the prompt string contains
    invisible characters.

g.  Fixed some display (and other) bugs encountered in multibyte locales
    when a non-ascii character was the last character on a line.

h.  Fixed some display bugs caused by multibyte characters in prompt strings.

i.  Fixed a problem with history expansion caused by non-whitespace characters
    used as history word delimiters.

3.  New Features in Bash

a.  printf builtin understands two new escape sequences:  \" and \?.

b.  `echo -e' understands two new escape sequences:  \" and \?.

c.  The GNU `gettext' package and libintl have been integrated; the shell's
    messages can be translated into different languages.

d.  The `\W' prompt expansion now abbreviates $HOME as `~', like `\w'.

e.  The error message printed when bash cannot open a shell script supplied
    as argument 1 now includes the name of the shell, to better identify
    the error as coming from bash.

4.  New Features in Readline

a.  New application variable, rl_completion_quote_character, set to any
    quote character readline finds before it calls the application completion
    function.

b.  New application variable, rl_completion_suppress_quote, settable by an
    application completion function.  If set to non-zero, readline does not
    attempt to append a closing quote to a completed word.

c.  New application variable, rl_completion_found_quote, set to a non-zero
    value if readline determines that the word to be completed is quoted.
    Set before readline calls any application completion function.

d.  New function hook, rl_completion_word_break_hook, called when readline
    needs to break a line into words when completion is attempted.  Allows
    the word break characters to vary based on position in the line.

e.  New bindable command: unix-filename-rubout.  Does the same thing as
    unix-word-rubout, but adds `/' to the set of word delimiters.

------------------------------------------------------------------------------
This document details the changes between this version, bash-3.0-alpha,
and the previous version, bash-2.05b-release.

1.  Changes to Bash

a.  Fixes so that the shell will compile without some of the default options
    defined.

b.  Fixed an error message that did not pass enough arguments to printf.

c.  Fixed a bug that caused input redirection to a builtin inside a script
    being read from standard input to result in the rest of the already-
    read and buffered script to be discarded.

d.  Fixed a bug that caused subshell initialization to close the file
    descriptor from which the shell was reading a script under certain
    circumstances.

e.  Fixed a bug that caused the shell to not advance a string pointer over
    a null wide character when doing string operations.

f.  Fixed the internal logout code so that shells that time out waiting for
    input (using $TMOUT) run ~/.bash_logout.

g.  Portability and configuration changes for: cygwin, HP/UX, GNU/FreeBSD.

h.  The parser no longer adds implicit double quotes to ((...)) arithmetic
    commands.

i.  The ((...)) arithmetic command evaluation code was fixed to not dump core
    when the expanded string is null.

j.  The ((...)) arithmetic command evaluation code was fixed to not perform
    variable assignments while expanding the expression.

k.  Fixed a bug that caused word splitting to be performed incorrectly when
    IFS is set, but null.

l.  Fixed a bug in brace expansion that caused a quoted `$' preceding an
    open brace to inhibit brace expansion.

m.  Fixed a bug that caused a leading `-' in the shell's name to cause it to
    not be recognized as a restricted shell.

n.  Fixed a bug in the arithmetic evaluation code that could cause longjmps
    to an invalid location and result in a core dump.

o.  Fixed a bug in the calculation of how many history lines are new in a
    single shell session when reading new history lines from a file with
    `history -n'.

p.  Fixed a bug in pathname canonicalization that caused the shell to dump
    core when presented with a pathname longer than PATH_MAX.

q.  Fixed the parser so that it doesn't try to compare a char variable to
    EOF, which fails when chars are unsigned.

r.  Fixed a bug in the simple command execution code that caused occasional
    core dumps.

s.  The shell does a better job of saving any partial parsing state during
    operations which cause a command to be executed while a line is being
    entered and parsed.

t.  The completion code now splits words more like the expansion code when
    $IFS is used to split.

u.  The locale code does a better job of recomputing the various locale
    variable values when LC_ALL is unset.

v.  The programmable completion code does a better job of dequoting expanded
    word lists before comparing them against the word to be matched.

w.  The shell no longer seg faults if the expanded value of $PS4 is null
    and `set -x' is enabled.

x.  Fixed a bug that caused core dumps when a here string expanded to NULL.

y.  The mail checking code now makes sure the mailbox is bigger before
    reporting the existence of new mail.

z.  The parser does not try to expand $'...' and $"..." when the appear
    within double quotes unless the `extquote' option has been enabled with
    `shopt'.  For backwards compatibility, it is enabled by default.

aa. Fixed a bug that caused `for x; do ...' and `select x; do ... to use
    $@ instead of "$@" for the implicit list of arguments.

bb. Fixed a bug that caused a subshell of a restricted shell (e.g., one
    spawned to execute a pipeline) to not exit immediately if attempting
    to use a command containing a slash.

cc. Fixed a problem with empty replacements for a pattern that doesn't match
    when performing ${param/word/} expansion.

dd. Word expansions performed while expanding redirections no longer search
    a command's temporary environment to expand variable values.

ee. Improvements to the alias expansion code when expanding subsequent words
    because an aliase's value ends with a space.

ff. `cd -' now prints the current working directory after a successful chdir
    even when the shell is not interactive, as the standard requires.

gg. The shell does a better job of ensuring a child process dies of SIGINT
    before resending SIGINT to itself.

hh. The arithmetic expansion variable assignment code now does the right
    thing when assigning to `special' variables like OPTIND.

ii. When history expansion verification is enabled, the bash readline helper
    functions that do history expansion on the current line don't print
    the results.

jj. Fixed bugs with multiple consecutive alias expansion when one of the
    expansions ends with a space.

kk. Fixed a problem in the programmable completion code that could cause core
    dumps when trying to initialize a set of possible completions from a
    list of variables.

ll. The \[ and \] escape characters are now ignored when decoding the prompt
    string if the shell is started with editing disabled.

mm. Fixed a bug that could leave extra characters in a string when doing
    quoted null character removal.

nn. Command substitution and other subshell operations no longer reset the
    line number (aids the bash debugger).

oo. Better line number management when executing simple commands, conditional
    commands, for commands, and select commands.

pp. The globbing code now uses malloc, with its better failure properties,
    rather than alloca().

qq. Fixed a bug that caused expansions like #{a[2]:=value} to create the
    appropriate array element instead of a variable named `a[2]'.

rr. Fixed a bug in the handling of a `?(...)' pattern immediately following
    a `*' when extglob is enabled.

ss. Fixed a bug that caused a `return' invoked in an exit trap when exit is
    invoked in a function to misbehave.

tt. Fixed a bug that caused CTLESC and CTLNUL characters to not be escaped
    by the internal shell string quoting functions.

uu. Fixed a bug that caused quoted null characters in an expanded word list
    to be inappropriately assigned to an array variable when using `read -a'.

vv. Fixed a bug that caused redirections accompanying a null command to persist
    in the current shell.

ww. Fixed a bug that caused the prompt to be printed when the shell was
    expanding a multiline alias.

xx. Fixed a bug that resulted in core dumps when the completion for a command
    changed the compspec.

yy. Fixed a bug that caused evaluation of programmable completions to print
    notifications of completed jobs.

zz. Bash now disables line editing when $EMACS == `t' and $TERM == `dumb'
    (which is what emacs shell windows do).

aaa. In posix mode, `kill -l' causes signal names to be displayed without
     a leading `SIG'.

bbb. Clear error flag on standard output so it doesn't persist across multiple
     builtin commands.

ccc. In posix mode, `alias' displays alias values without the leading `alias',
     so the output cannot be used as subsequent input.

ddd. In posix mode, the `trap' builtin doesn't check whether or not its
     first argument is a signal specification and revert the signal handling
     to its original disposition if it is.

eee. Fixed several bugs in the handling of "$*" and "${array[*]}" by the
     pattern substitution and removal expansions.

fff. Fixed several problems with the handling of ${array[@]}, ${array[*]},
     $@, and $* by the indirect variable expansion code.

ggg. Fixed a bug that did not allow `time' to be aliased.

hhh. Improved the mail checking code so it won't check (and possibly cause an
     NFS file system mount) until MAILPATH or MAIL is given a value -- there
     is no default if DEFAULT_MAIL_DIRECTORY is not defined at compile time.
     (It is computed by configure, but can be #undef'd in config-bot.h.)

iii. If the `chkwinsize' option is enabled, the shell checks for window size
     changes if a child process exits due to a signal.

jjj. Removed the attempts to avoid adding a slash at the end of a completed
     executable name if there was a directory with the same name in the
     current directory.

kkk. Fixed PATH lookup code so it treats the permission bits separately for
     owner, group, and other, rather than checking them all.

lll. Fixed the locale code to reset the parser's idea of the character class
     <blank>, which controls how it splits tokens, when the locale changes.

mmm. The shell now binds its special readline functions and key bindings only
     if the user's inputrc file has not already bound them.

nnn. The shell now reports on processes that dump core due to signals when
     invoked as `-c command'.

2.  Changes to Readline

a.  Fixes to avoid core dumps because of null pointer references in the
    multibyte character code.

b.  Fix to avoid infinite recursion caused by certain key combinations.

c.  Fixed a bug that caused the vi-mode `last command' to be set incorrectly.

d.  Readline no longer tries to read ahead more than one line of input, even
    when more is available.

e.  Fixed the code that adjusts the point to not mishandle null wide
    characters.

f.  Fixed a bug in the history expansion `g' modifier that caused it to skip
    every other match.

g.  Fixed a bug that caused the prompt to overwrite previous output when the
    output doesn't contain a newline and the locale supports multibyte
    characters.  This same change fixes the problem of readline redisplay
    slowing down dramatically as the line gets longer in multibyte locales.

h.  History traversal with arrow keys in vi insertion mode causes the cursor
    to be placed at the end of the new line, like in emacs mode.

i.  The locale initialization code does a better job of using the right
    precedence and defaulting when checking the appropriate environment
    variables.

j.  Fixed the history word tokenizer to handle <( and >( better when used as
    part of bash.

k.  The overwrite mode code received several bug fixes to improve undo.

l.  Many speedups to the multibyte character redisplay code.

m.  The callback character reading interface should not hang waiting to read
    keyboard input.

n.  Fixed a bug with redoing vi-mode `s' command.

o.  The code that initializes the terminal tracks changes made to the terminal
    special characters with stty(1) (or equivalent), so that these changes
    are reflected in the readline bindings.  New application-callable function
    to make it work:  rl_tty_unset_default_bindings().

p.  Fixed a bug that could cause garbage to be inserted in the buffer when
    changing character case in vi mode when using a multibyte locale.

q.  Fixed a bug in the redisplay code that caused problems on systems
    supporting multibyte characters when moving between history lines when the
    new line has more glyphs but fewer bytes.

r.  Undo and redo now work better after exiting vi insertion mode.

s.  Make sure system calls are restarted after a SIGWINCH is received using
    SA_RESTART.

t.  Improvements to the code that displays possible completions when using
    multibyte characters.

u.  Fixed a problem when parsing nested if statements in inputrc files.

v.  The completer now takes multibyte characters into account when looking for
    quoted substrings on which to perform completion.

w.  The history search functions now perform better bounds checking on the
    history list.

3.  New Features in Bash

a.  ANSI string expansion now implements the \x{hexdigits} escape.

b.  There is a new loadable `strftime' builtin.

c.  New variable, COMP_WORDBREAKS, which controls the readline completer's
    idea of word break characters.

d.  The `type' builtin no longer reports on aliases unless alias expansion
    will actually be performed.    

e.  HISTCONTROL is now a colon-separated list of values, which permits
    more extensibility and backwards compatibility.

f.  HISTCONTROL may now include the `erasedups' option, which causes all lines
    matching a line being added to be removed from the history list.

g.  `configure' has a new `--enable-multibyte' argument that permits multibyte
    character support to be disabled even on systems that support it.

h.  New variables to support the bash debugger:  BASH_ARGC, BASH_ARGV,
    BASH_SOURCE, BASH_LINENO, BASH_SUBSHELL, BASH_EXECUTION_STRING,
    BASH_COMMAND

i.  FUNCNAME has been changed to support the debugger: it's now an array
    variable.

j.  for, case, select, arithmetic commands now keep line number information
    for the debugger.

k.  There is a new `RETURN' trap executed when a function or sourced script
    returns (not inherited child processes; inherited by command substitution
    if function tracing is enabled and the debugger is active).

l.  New invocation option:  --debugger.  Enables debugging and turns on new
    `extdebug' shell option.

m.  New `functrace' and `errtrace' options to `set -o' cause DEBUG and ERR
    traps, respectively, to be inherited by shell functions.  Equivalent to
    `set -T' and `set -E' respectively.  The `functrace' option also controls
    whether or not the DEBUG trap is inherited by sourced scripts.

n.  The DEBUG trap is run before binding the variable and running the action
    list in a `for' command, binding the selection variable and running the
    query in a `select' command, and before attempting a match in a `case'
    command.

o.  New `--enable-debugger' option to `configure' to compile in the debugger
    support code.

p.  `declare -F' now prints out extra line number and source file information
    if the `extdebug' option is set.

q.  If `extdebug' is enabled, a non-zero return value from a DEBUG trap causes
    the next command to be skipped, and a return value of 2 while in a
    function or sourced script forces a `return'.

r.  New `caller' builtin to provide a call stack for the bash debugger.

s.  The DEBUG trap is run just before the first command in a function body is
    executed, for the debugger.

t.  `for', `select', and `case' command heads are printed when `set -x' is
    enabled.

u.  There is a new {x..y} brace expansion, which is shorthand for {x.x+1,
    x+2,...,y}.  x and y can be integers or single characters; the sequence
    may ascend or descend; the increment is always 1.

v.  New ksh93-like ${!array[@]} expansion, expands to all the keys (indices)
    of array.

w.  New `force_fignore' shopt option; if enabled, suffixes specified by
    FIGNORE cause words to be ignored when performing word completion even
    if they're the only possibilities.

x.  New `gnu_errfmt' shopt option; if enabled, error messages follow the `gnu
    style' (filename:lineno:message) format.

y.  New `-o bashdefault' option to complete and compgen; if set, causes the
    whole set of bash completions to be performed if the compspec doesn't
    result in a match.

z.  New `-o plusdirs' option to complete and compgen; if set, causes directory
    name completion to be performed and the results added to the rest of the
    possible completions.

aa. `kill' is available as a builtin even when the shell is built without
    job control.

bb. New HISTTIMEFORMAT variable; value is a format string to pass to
    strftime(3).  If set and not null, the `history' builtin prints out
    timestamp information according to the specified format when displaying
    history entries.  If set, bash tells the history library to write out
    timestamp information when the history file is written.

cc. The [[ ... ]] command has a new binary `=~' operator that performs
    extended regular expression (egrep-like) matching.

dd. `configure' has a new `--enable-cond-regexp' option (enabled by default)
    to enable the =~ operator and regexp matching in [[ ... ]].

ee. Subexpressions matched by the =~ operator are placed in the new
    BASH_REMATCH array variable.

ff. New `failglob' option that causes an expansion error when pathname
    expansion fails to produce a match.

gg. New `set -o pipefail' option that causes a pipeline to return a failure
    status if any of the processes in the pipeline fail, not just the last
    one.

4.  New Features in Readline

a.  History expansion has a new `a' modifier equivalent to the `g' modifier
    for compatibility with the BSD csh.

b.  History expansion has a new `G' modifier equivalent to the BSD csh `g'
    modifier, which performs a substitution once per word.

c.  All non-incremental search operations may now undo the operation of
    replacing the current line with the history line.

d.  The text inserted by an `a' command in vi mode can be reinserted with
    `.'.

e.  New bindable variable, `show-all-if-unmodified'.  If set, the readline
    completer will list possible completions immediately if there is more
    than one completion and partial completion cannot be performed.

f.  There is a new application-callable `free_history_entry()' function.

g.  History list entries now contain timestamp information; the history file
    functions know how to read and write timestamp information associated
    with each entry.

h.  Four new key binding functions have been added:

	rl_bind_key_if_unbound()
	rl_bind_key_if_unbound_in_map()
	rl_bind_keyseq_if_unbound()
	rl_bind_keyseq_if_unbound_in_map()

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05b-release,
and the previous version, bash-2.05b-beta2.

1.  Changes to Bash

a.  Fixed an off-by-one error in the function that translates job
    specifications.

b.  Note that we're running under Emacs and disable line editing if
    $EMACS == `t'.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05b-beta2,
and the previous version, bash-2.05b-beta1.

1.  Changes to Bash

a.  Fixed the /= and %= arithmetic operators to catch division by zero.

b.  Added putenv, setenv, unsetenv to getenv replacement for completeness.

c.  Fixed a bug that could cause the -O expand_aliases invocation option
    to not take effect.

d.  Fixed a problem with process substitution that resulted in incorrect
    behavior when the number of process substitutions in an individual
    command approached 64.

2.  Changes to Readline

a.  Fixed a problem with backward-char-search when on a system with support
    for multibyte characters when running in a locale without any multibyte
    characters.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05b-beta1,
and the previous version, bash-2.05b-alpha1.

1.  Changes to Bash

a.  Fixed a problem when parsing a POSIX.2 character class name while
    evaluating a bracket expression containing multibyte characters.

b.  Changed the help text for `bind' to make it clear that any command
    that may be placed in ~/.inputrc is a valid argument to `bind'.

c.  Added `help' builtin entries for `((', `[[', and arithmetic for.

d.  malloc updated again:
	o slightly better overflow and underflow detection by putting the
	  chunk size at the beginning and end of the chunk and making
	  sure they match in free/realloc
	o partial page allocated to make things page-aligned no longer
	  completely wasted
	o block coalescing now enabled by default
	o splitting and coalescing enabled for 32-byte chunks, the most
	  common size requested
	o fixed a problem that resulted in spurious underflow messages and
	  aborts
	o bin sizes are precomputed and stored in an array rather than
	  being computed at run time
	o malloc will return memory blocks back to the system if the block
	  being freed is at the top of the heap and of sufficient size to
	  make it worthwhile
	o malloc/free/realloc now inline memset instead of calling the
	  libc function; uses Duff's device for good performance

e.  Check for getservent(); make the service name completion code dependent
    on its presence.

f.  Changed the readline callback that executes a command bound to a key
    sequence to not save the executed command on the history list and to
    save and restore the parsing state.

g.  Changes to lib/sh/snprintf.c:  fixed some bugs in the `g' and `G'
    floating point format display; implemented the "'" flag character
    that turns on thousands' grouping; fixed behavior on systems where
    MB_CUR_MAX does not evaluate to a constant.

h.  The `unset' builtin no longer returns a failure status when asked to
    unset a previously-unset variable or function.

i.  Changes to the build system to make it easier to cross-compile bash
    for different systems.

j.  Added `,' to  the characters that are backslash-escaped during filename
    completion, to avoid problems with complete-into-braces and RCS filenames
    containing commas.

k.  Some changes to the multibyte character support code to avoid many calls
    to strlen().

l.  Bash now correctly honors setting LANG to some value when LC_ALL does not
    already have a value.

m.  Fixed a bug that could cause SIGSEGV when processing nested traps with
    trap handlers.

n.  The `source/.' builtin now restores the positional parameters when it
    returns unless they were changed using the `set' builtin during the file's
    execution.

o.  Fixed a bug that caused a syntax error when a command was terminated by
    EOF.

2.  New Features in Bash

a.  There is now support for placing the long help text into separate files
    installed into ${datadir}/bash.  Not enabled by default; can be turned
    on with `--enable-separate-helpfiles' option to configure.

b.  All builtins that take operands accept a `--' pseudo-option, except
    `echo'.

c.  The `echo' builtin now accepts \0xxx (zero to three octal digits following
    the `0') in addition to \xxx (one to three octal digits) for SUSv3/XPG6/
    POSIX.1-2001 compliance.

3.  Changes to Readline

a.  Fixed a small problem in _rl_insert_char with multibyte characters.

b.  Fixes from IBM for line wrapping problems when using multibyte characters.

c.  Fixed a problem which caused the display to be messed up when the last
    line of a multi-line prompt (possibly containing invisible characters)
    was longer than the screen width.

d.  Fixed a problem with the vi-mode `r' command that occurred on systems with
    support for multibyte characters when running in a locale without any
    multibyte characters.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05b-alpha1,
and the previous version, bash-2.05a-release.

1.  Changes to Bash

a.  Some changes to work around inlining differences between compilers.

b.  Added more prototypes for internal shell typedefs, to catch argument
    passing errors when using pointers to functions.

c.  The `cd' builtin now fails in posix mode when a valid directory cannot be
    constructed from a relative pathname argument and the $PWD using pathname
    canonicalization, and the -P option has not been supplied.  Previously,
    the shell would attempt to use what the user typed, leading to weird
    values for $PWD and discrepancies between the value of $PWD and the
    actual working directory.

d.  The `cd' builtin now resets $PWD when canonicalization fails but a chdir
    to the pathname passed as an argument succeeds (when not in posix mode).

e.  The `fc' builtin has been fixed, as POSIX requires, to use the closest
    history position in range when given an out-of-range argument.

f.  The history file loading code was changed to allow lines to be saved in
    the history list from the shell startup files.

g.  `history -s args' now works better in compound commands.

h.  The tilde expansion code was fixed to better recognize when it's being
    invoked in an assignment context, which enables expansion after `='
    and `:'.

i.  Fixed the command name completion code so a slash is no longer appended
    to a single match if there happens to be a directory with that name in
    $PWD.

j.  Fixed compound array assignment to no longer perform alias expansion, to
    allow reserved words as array members, and to not produce extra output
    when the `-v' option had been enabled.

k.  Fixed the programmable completion code to better handle newlines in lists
    of possible completions (e.g., `complete -W').

l.  Removed the reserved words from the `bash-builtins' manual page.

m.  Parser error reporting now attempts to do a better job of identifying the
    token in error rather than doing straight textual analysis.

n.  Fixes for Inf/NaN, locales, wide/multibyte characters and zero-length
    arguments in the library snprintf(3) replacement.

o.  `read -e' no longer does command name completion on the first word on
    the line being read.

p.  `select' now returns failure if the read of the user's selection fails.

q.  Fixed a bug that could cause a core dump when setting $PIPESTATUS.

r.  Fixes to not allocate so many job slots when the shell is running a loop
    with job control enabled in a subshell of an interactive shell.

s.  Fixed a bug in the trap code that caused traps to be inherited by
    command substitutions in some cases.

t.  Fixed a bug that could cause alias expansion to inappropriately expand
    the word following the alias.

u.  Fixed a bug in the `kill' builtin that mishandled negative pid arguments.

v.  The parser is less lenient when parsing assignment statements where the
    characters before the `=' don't comprise a valid identifier.

w.  The arithmetic expression evaluation code now honors the setting of the
    `-u' option when expanding variable names.

x.  Fixed the arithmetic evaluation code to allow array subscripts to be
    assigned (`let b[7]=42') and auto-incremented and auto-decremented
    (e.g., b[7]++).

y.  Reimplemented the existing prompt string date and time expansions using
    strftime(3), which changed the output of \@ in some locales.

z.  Fixed a bug that could cause a core dump when a special shell variable
    (like RANDOM) was converted to an array with a variable assignment.

aa. Fixed a bug that would reset the handler for a signal the user had
    trapped to a function that would exit the shell when setting the exit
    trap in a non-interactive shell.

bb. Changed the execve(2) wrapper code to check whether or not a failing
    command is a directory before looking at whether a `#!' interpreter
    failed for some reason.

cc. Fixed a bug in the command printing code so it no longer inserts a `;'
    after a newline, which produces a syntax error when reused as input.

dd. The code that expands $PS4 no longer inherits the `-x' flag.

ee. The bash-specific completion functions may now take advantage of the
    double-TAB and M-?  features of the standard readline completion
    functions.

ff. The mail checking code no longer prints a message if the checked file's
    size has not increased, even if the access time is less than the modification time.

gg. Rewrote the variable symbol table code: there is now a stack of
    contexts, each possibly including a separate symbol table; there can
    be more than one temporary environment supplied to nested invocations
    of `./source'; the temporary environments no longer require so much
    special-case code; shell functions now handle the temporary environment
    and local variables more consistently; function scope exit is faster now
    that the entire symbol table does not have to be traversed to dispose of
    local variables; it is now easier to push vars from the temporary
    environment to the shell's variable table in posix mode; some duplicated
    code has been removed.

hh. Regularized the error message printing code; builtin_error is now called
    more consistently, and common error message strings are handled by small
    functions.  This should make eventual message translation easier.

ii. Error messages now include the line number in a script when the shell
    is not interactive.

jj. Array subscript expansion now takes place even when the array variable is
    unset, so side effects will take place.

kk. Fixed a bug in the SICGHLD child-reaping code so that it won't find
    jobs already marked as terminated if the OS reuses pids quickly enough.

ll. Fixed a bug that could cause a signal to not interrupt the `wait'
    builtin while it was waiting for a background process to terminate.

mm. A couple of changes to make it easier for multiple shells to share history
    files using `history -n', `history -r', and `history -w'.

nn. The `getopts' builtin always increments OPTIND to point to the next
    option to be handled when an option is returned, whether it's valid
    or not, as POSIX 1003.x-2001 requires.

oo. Changed some parts of the expansion code to avoid allocating and
    immediately freeing memory without using the results for anything.

pp. The shell now keeps track of $IFS internally, updating its internal map
    each time the variable is assigned a new value (or at local scope exit).
    This saves thousands of hash lookups for IFS, which, while individually
    cheap, add up.

qq. Rewrote the hash table code:  searching and insertion are much faster now,
    and it uses a better string hashing function; augmented the function
    interface to simplify other parts of the code and remove duplicated code

rr. The shell now uses a simple, generic `object cache' for allocating and
    caching words and word lists, which were the major users of
    malloc/free.

ss. Fixed the assignment statement parsing code to allow whitespace and
    newlines in subscripts when performing array element assignment.

tt. The shell now issues many fewer calls to sigprocmask and other signal
    masking system calls.

uu. Fixed the `test' and conditional command file comparison operators to
    work right when one file has a non-positive timestamp and the other
    does not exist.

vv. Fixed some cases where the special characters '\001' and '\177' in the
    values of variables or positional parameters caused incorrect expansion
    results.

2.  Changes to Readline

a.  Fixed output of comment-begin character when listing variable values.

b.  Added some default key bindings for common escape sequences produced by
    HOME and END keys.

c.  Fixed the mark handling code to be more emacs-compatible.

d.  A bug was fixed in the code that prints possible completions to keep it
    from printing empty strings in certain circumstances.

e.  Change the key sequence printing code to print ESC as M\- if ESC is a
    meta-prefix character -- it's easier for users to understand than \e.

f.  Fixed unstifle_history() to return values that match the documentation.

g.  Fixed the event loop (rl_event_hook) to handle the case where the input
    file descriptor is invalidated.

h.  Fixed the prompt display code to work better when the application has a
    custom redisplay function.

i.  Changes to make reading and writing the history file a little faster, and
    to cope with huge history files without calling abort(3) from xmalloc.

j.  The vi-mode `S' and `s' commands are now undone correctly.

3.  New Features in Bash

a.  If set, TMOUT is the default timeout for the `read' builtin.

b.  `type' has two new options:  `-f' suppresses shell function lookup, and
    `-P' forces a $PATH search.

c.  New code to handle multibyte characters.

d.  `select' was changed to be more ksh-compatible, in that the menu is
    reprinted each time through the loop only if REPLY is set to NULL.
    The previous behavior is available as a compile-time option.

e.  `complete -d' and `complete -o dirnames' now force a slash to be
    appended to names which are symlinks to directories.

f.  There is now a bindable edit-and-execute-command readline command,
    like the vi-mode `v' command, bound to C-xC-e in emacs mode.

g.  Added support for ksh93-like [:word:] character class in pattern matching.

h.  The  $'...' quoting construct now expands \cX to Control-X.

i.  A new \D{...} prompt expansion; passes the `...' to strftime and inserts
    the result into the expanded prompt.

j.  The shell now performs arithmetic in the largest integer size the
    machine supports (intmax_t), instead of long.

k.  If a numeric argument is supplied to one of the bash globbing completion
    functions, a `*' is appended to the word before expansion is attempted.

l.  The bash globbing completion functions now allow completions to be listed
    with double tabs or if `show-all-if-ambiguous' is set.

m.  New `-o nospace' option for `complete' and `compgen' builtins; suppresses
    readline's appending a space to the completed word.

n.  New `here-string' redirection operator:  <<< word.

o.  When displaying variables, function attributes and definitions are shown
    separately, allowing them to be re-used as input (attempting to re-use
    the old output would result in syntax errors).

p.  There is a new configuration option `--enable-mem-scramble', controls
    bash malloc behavior of writing garbage characters into memory at
    allocation and free time.

q.  The `complete' and `compgen' builtins now have a new `-s/-A service'
    option to complete on names from /etc/services.

r.  `read' has a new `-u fd' option to read from a specified file descriptor.

s.  Fix the completion code so that expansion errors in a directory name
    don't cause a longjmp back to the command loop.

t.  Fixed word completion inside command substitution to work a little more
    intuitively.

u.  The `printf' %q format specifier now uses $'...' quoting to print the
    argument if it contains non-printing characters.

v.  The `declare' and `typeset' builtins have a new `-t' option.  When applied
    to functions, it causes the DEBUG trap to be inherited by the named
    function.  Currently has no effect on variables.

w.  The DEBUG trap is now run *before* simple commands, ((...)) commands,
    [[...]] conditional commands, and for ((...)) loops.

x.  The expansion of $LINENO inside a shell function is only relative to the
    function start if the shell is interactive -- if the shell is running a
    script, $LINENO expands to the line number in the script.  This is as
    POSIX-2001 requires.

y.  The bash debugger in examples/bashdb has been modified to work with the
    new DEBUG trap semantics, the command set has been made more gdb-like,
    and the changes to $LINENO make debugging functions work better.  Code
    from Gary Vaughan.

z.  New [n]<&word- and [n]>&word- redirections from ksh93 -- move fds (dup
    and close).

aa. There is a new `-l' invocation option, equivalent to `--login'.

bb. The `hash' builtin has a new `-l' option to list contents in a reusable
    format, and a `-d' option to remove a name from the hash table.

4.  New Features in Readline

a.  Support for key `subsequences':  allows, e.g., ESC and ESC-a to both
    be bound to readline functions.  Now the arrow keys may be used in vi
    insert mode.

b.  When listing completions, and the number of lines displayed is more than
    the screen length, readline uses an internal pager to display the results.
    This is controlled by the `page-completions' variable (default on).

c.  New code to handle editing and displaying multibyte characters.

d.  The behavior introduced in bash-2.05a of deciding whether or not to
    append a slash to a completed name that is a symlink to a directory has
    been made optional, controlled by the `mark-symlinked-directories'
    variable (default is the 2.05a behavior).

e.  The `insert-comment' command now acts as a toggle if given a numeric
    argument:  if the first characters on the line don't specify a
    comment, insert one; if they do, delete the comment text

f.  New application-settable completion variable:
    rl_completion_mark_symlink_dirs, allows an application's completion
    function to temporarily override the user's preference for appending
    slashes to names which are symlinks to directories.

g.  New function available to application completion functions:
    rl_completion_mode, to tell how the completion function was invoked
    and decide which argument to supply to rl_complete_internal (to list
    completions, etc.).

h.  Readline now has an overwrite mode, toggled by the `overwrite-mode'
    bindable command, which could be bound to `Insert'.

i.  New application-settable completion variable:
    rl_completion_suppress_append, inhibits appending of
    rl_completion_append_character to completed words.

j.  New key bindings when reading an incremental search string:  ^W yanks
    the currently-matched word out of the current line into the search
    string; ^Y yanks the rest of the current line into the search string,
    DEL or ^H deletes characters from the search string.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05a-release,
and the previous version, bash-2.05a-rc1.

1.  Changes to Bash

a.  Fixed the `printf' builtin so that the variable name supplied as an
    argument to a %n conversion must be a valid shell identifier.

b.  Improved the random number generator slightly.

c.  Changes to configuration to not put -I/usr/include into $CFLAGS, since
    it messes up some includes.

d.  Corrected description of POSIXLY_CORRECT in man page and info manual.

e.  Fixed a couple of cases of incorrect function prototypes that sneaked
    through and caused compilation problems.

f.  A few changes to avoid potential core dumps in the programmable completion
    code.

g.  Fixed a configure problem that could cause a non-existent file to show
    up in LIBOBJS.

h.  Fixed a configure problem that could cause siglist.o to not be built when
    required.

i.  Changes to the strtoimax and strtoumax replacement functions to work
    around buggy compilers.

j.  Fixed a problem with the snprintf replacement function that could
    potentially cause a core dump.

2.  Changes to Readline

a.  Fixed a locale-specific problem in the vi-mode `goto mark' command.

b.  Fixed Makefile to not put -I/usr/include into CFLAGS, since it can cause
    include file problems.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05a-rc1,
and the previous version, bash-2.05a-beta1.

1.  Changes to Bash

a.  Fixed the snprintf replacement to correctly implement the `alternate form'
    of the %g and %G conversions.

b.  Fixed snprintf to correctly handle the optional precision with the %g and
    %G conversions.

c.  Fixed the arithmetic evaluation code to correct the values of `@' and `_'
    when translating base-64 constants (they were backwards).

d.  New library functions for formatting long and long long ints.

e.  Fixed a few places where negative array subscripts could have occurred,
    mostly as the result of systems using signed characters.

f.  Fixed a few places that assumed a pid_t was no wider than an int.

g.  Fixed the `maildir' mail checking code to work on systems where a
    `struct stat' doesn't include an `st_blocks' member.

h.  Fixed snprintf to make `unsigned long long' conversion formats (%llu)
    work better.

i.  Fixed snprintf to not print a sign when asked to do an unsigned conversion.

j.  Made configure changes to avoid compiling empty source files in lib/sh.

k.  New replacement functions (if necessary) for strtoull, strtoll, strtoimax,
    strtoumax.

l.  The `printf' builtin now handles the `ll' and `j' length modifiers
    directly, since they can affect the type and width of the argument
    passed to printf(3).

m.  Renamed a number of the bash-specific autoconf macros in aclocal.m4 to
    have more systematic naming, with accompanying changes to configure.in.

n.  Fixed snprintf to handle long doubles and the %a/%A conversions by
    falling back to sprintf, as long as sprintf supports them.

o.  Fixed return value from vsnprintf/snprintf to be the number of characters
    that would have been printed, even if that number exceeds the buffer
    size passed as an argument.

p.  Bash no longer attempts to define its own versions of some ctype macros
    if they are implemented as functions in libc but not as macros in
    <ctype.h>.

q.  Changed the variable printing code (used by `set', `export', etc.) to
    not use the $'...' syntax when in posix mode, since that caused
    interoperability problems with other shells (most notably with autoconf).
    When not in posix mode, it uses $'...' if the string to be printed
    contains non-printing characters and regular single quotes otherwise.

r.  snprintf now recognizes the %F conversion.

s.  Fixed a bug that could cause the wrong status to be returned by a shell
    function when the shell is compiled without job control and a null
    command containing a command substutition was executed in the function.

t.  When in posix mode, the default value for MAILCHECK is 600.

u.  Bash only initializes FUNCNAME, GROUPS, and DIRSTACK as special variables
    if they're not in the initial environment.

v.  If SECONDS appears in the initial environment with a valid integer value,
    bash uses that as the starting value, as if an assignment had been
    performed.

w.  Bash no longer auto-exports HOME, PATH, SHELL, or TERM, even though it
    gives them default values if they don't appear in the initial environment.

x.  Bash no longer auto-exports HOSTNAME, HOSTTYPE, MACHTYPE, or OSTYPE,
    even if it assigns them default values.

y.  Bash no longer removes the export attribute from SSH_CLIENT or SSH2_CLIENT
    if they appear in the initial environment.

z.  Bash no longer attempts to discover if it's being run by sshd in order to
    run the startup files.  If the SSH_SOURCE_BASHRC is uncommented in
    config-top.h it will attempt to do so as previously, but that's commented
    out in the distributed version.

aa. Fixed a typo in the code that tests for LC_NUMERIC.

bb. The POSIXLY_CORRECT shell variable and its effects are now documented.

cc. Some changes to several of the support shell scripts included in the
    definitions to try to avoid race conditions and attacks.

dd. Several changes to avoid warnings from `gcc -Wall'.

ee. Fixed a problem with the `unset' builtin that could cause incorrect
    results if asked to unset a variable and an array subscript in the
    same command.

ff. A few changes to the shell's temporary file creation code to avoid
    potential file descriptor leaks and to prefer the system's idea of
    the temporary directory to use.

gg. Fixes to build with the C alloca in lib/malloc/alloca.c if the system
    requires it but the shell has been configured --without-bash-malloc.

hh. Updated the documentation to note that only interactive shells resend
    SIGHUP to all jobs before exiting.

ii. Fixes to only pass unquoted tilde words to tilde_expand, rather than
    rely on tilde_expand or getpwnam(3) to handle the quotes (MacOS 10.x
    will remove backslashes in any login name passed to getpwnam(3)).

jj. Small change from Paul Eggert to make LINENO right in commands run with
    `bash -c'.

2.  New Features in Bash

a.  The `printf' builtin now handles the %a and %A conversions if they're
    implemented by printf(3).

b.  The `printf' builtin now handles the %F conversion (just about like %f).

c.  The `printf' builtin now handles the %n conversion like printf(3).  The
    corresponding argument is the name of a shell variable to which the
    value is assigned.

3.  Changes to Readline

a.  Fixed a few places where negative array subscripts could have occurred.

b.  Fixed the vi-mode code to use a better method to determine the bounds of
    the array used to hold the marks.

c.  Fixed the defines in chardefs.h to work better when chars are signed.

d.  Fixed configure.in to use the new names for bash autoconf macros.

e.  Readline no longer attempts to define its own versions of some ctype
    macros if they are implemented as functions in libc but not as macros in
    <ctype.h>.

f.  Fixed a problem where rl_backward could possibly set point to before
    the beginning of the line.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05a-beta1,
and the previous version, bash-2.05a-alpha1.

1.  Changes to Bash

a.  Fixed a bug in the evaluation of arithmetic `for' statements when the
    expanded expression is NULL.

b.  Fixed an unassigned variable problem in the redirection printing code.

c.  Added more prototypes to extern function declarations in the header
    files and to static function declarations in C source files.

d.  Make sure called functions have a prototype in scope, to get the arguments
    and return values right instead of casting.  Removed extern function
    declarations from C source files that were already included in header
    files.

e.  Changed some function arguments to use function typedefs in general.h so
    the prototypes can be checked.  The only use of Function and VFunction
    now is for unwind-protects.

f.  More const changes to function arguments and appropriate variables.

g.  Changed the mail checking support to handle `maildir'-style mail
    directories.

h.  Augmented the bash malloc to pass in the file and line number information
    for each malloc, realloc, and free.  This should result in better error
    messages.

i.  The `old' gnu malloc is no longer a configuration option.

j.  Augmented the bash malloc with optional tracing and registering allocated
    and freed memory.

k.  Prompt string decoding now saves and restores the value of $? when it
    expands the prompt string, so command substitutions don't change $?.

i.  Array indices are now `long', since shell arithmetic is performed as long,
    and the internal arrayind_t type is used consistently.

j.  Some more `unsigned char *' fixes from Paul Eggert.

k.  Fixed a bad call to builtin_error that could cause core dumps when making
    local variables.

l.  `return' may no longer be used to terminate a `select' command, for
    compatibility with ksh.

m.  Changed code that reads octal numbers to do a better job of detecting
    overflows.

n.  The time formatting code no longer uses absolute indices into a buffer,
    because the buffer size changes depending on the size of a `time_t'.

o.  `umask' now prints four digits when printing in octal mode, for
    compatibility with other shells.

p.  Lots of changes to the `printf' builtin from Paul Eggert:  it handles `L'
    formats and long doubles better, and internal functions have been
    simplified where appropriate.

q.  Some `time_t' fixes for machines were a time_t is bigger than a long.

r.  Replaced some bash-specific autoconf macros with standard equivalents.

s.  Improvmed the code that constructs temporary filenames to make the
    generated names a bit more random.

t.  Added code that checks for ascii before calling any of the is* ctype
    functions.

u.  Changed some places where a `char' was used as an array subscript to use
    `unsigned char', since a `char' can be negative if it's signed by default.

v.  Lots of changes to the `ulimit' builtin from Paul Eggert to add support
    for the new POSIX-200x RLIM_SAVED_CUR and RLIM_SAVED_MAX values and
    simplify the code.

w.  `ulimit' now prints the description of a resource in any error message
    relating to fetching or setting that resource's limits.

x.  The `snprintf' replacement now computes maximum values at compile
    time rather than using huge constants for things like long long.

y.  Interactive shells now ignore `set -n'.

z.  Changed the malloc bookkeeping information so that it's now 8 bytes
    instead of 12 on most 32-bit machines (saving 4 bytes per allocation),
    restoring 8-byte alignment.

aa. The malloc error reporting code now attempts to print the file and line
    number of the call that caused the error.

bb. Changed the redirection error reporting code to catch EBADF errors and
    report the file descriptor number rather than the file being redirected
    to or from (e.g., things like `exec 4242<x' where 4242 is an out-of-range
    file descriptor).

cc. `printf', `echo -e', and the $'...' code now process only two hex digits
    after a `\x' escape sequence for compatibility with other shells, and
    the documentation was changed to note that the octal and hex escape
    sequences result in an eight-bit value rather than strict ASCII.

2.  Changes to Readline

a.  The completion code now attempts to do a better job of preserving the
    case of the word the user typed if ignoring case in completions.

b.  Readline defaults to not echoing the input and lets the terminal
    initialization code enable echoing if there is a controlling terminal.

c.  The key binding code now processes only two hex digits after a `\x'
    escape sequence, and the documentation was changed to note that the
    octal and hex escape sequences result in an eight-bit value rather
    than strict ASCII.

3.  New Features in Bash

a.  The builtin `ulimit' now takes two new non-numeric arguments:  `hard',
    meaning the current hard limit, and `soft', meaning the current soft
    limit, in addition to `unlimited'

b.  `ulimit' now prints the option letter associated with a particular
    resource when printing more than one limit.

c.  `ulimit' prints `hard' or `soft' when a value is not `unlimited' but is
    one of RLIM_SAVED_MAX or RLIM_SAVED_CUR, respectively.

4.  New Features in Readline

a.  New bindable variable `history-preserve-point'.  If set, the history
    code attempts to place the user at the same location on each history
    line retrieved with previous-history or next-history.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05a-alpha1,
and the previous version, bash-2.05-release.

1.  Changes to Bash

a.  Better checks in the redirection code for write errors.

b.  bashbug now uses $TMPDIR, defaulting to /tmp, and uses mktemp(1) more
    portably.

c.  System-specific configuration changes for:  Interix, OpenBSD, FreeBSD,
    MacOS X.

d.  Some more `const' cleanups through the code.

e.  Fixed a typo in the /dev/fd redirection code, better checks for valid
    numeric fds in /dev/fd.

f.  Fixed many parts of the shell to handle integer overflow more gracefully
    and to do more stringent checks for valid numbers.

g.  Fixed mksignames to include config.h.

h.  Fixed an uninitialized variable problem that could cause the shell to core
    dump when replacing characters in a string.

i.  New mechanism for updating the patch level when official patches are
    released (patchlevel.h).

j.  configure.in changed to no longer require external files _distribution and
    _patchlevel.

k.  Fixed non-interactive shell initialization problem when bash started as
    `bash -i filename'.

l.  Fixed printf builtin conversion error handling to be POSIX.2-conformant.

m.  autoconf-2.52 is now used to build configure; autoconf-2.50 or newer is
    required.  Some of the bash-specific macros were removed, since they are
    now standard.

n.  Startup files and files read with source or `.' are no longer required to
    be regular files.

o.  Fixed core dump in builtin printf when user-supplied precision or field
    width is 0.

p.  Fixed builtin printf to treat a negative field width as a positive field
    width with left-justification.

r.  New unwind-protect implementation from Paul Eggert.

s.  Fixed an inadvertently-unclosed comment in the bash completion code that
    caused programmable completions to not add trailing slashes or spaces to
    completions.

t.  Fixed the process substitution code to cope better when stdin is closed.

v.  Fixes, mostly from Paul Eggert, for a few possible buffer overflows in
    the shell.

w.  Fixes from Paul Eggert to avoid most of the type casts in the shell code,
    and use more appropriate types for a number of variables.

x.  Command substitution no longer inherits the DEBUG trap.

y.  Some fixes to the process substitution code on machines without /dev/fd so
    that named pipes are not removed inappropriately.

z.  The loadable `getconf' builtin is now much more complete, and will become
    part of the shell in the future.

aa. The select command no longer terminates on a `return', so it can be used
    to return from an enclosing function (as ksh does it).

bb. Fixed the extended pattern matching code to behave better when presented
    with incorrectly-formed patterns.

cc. Some changes were made with the intent of making cross-compilation easier.

dd. The network code (/dev/tcp and /dev/udp redirections) uses getaddrinfo(3)
    if it's available, which adds support for IPv6.

ee. Subshells of login shells no longer source ~/.bash_logout when they exit.

ff. Fixes so that subshells don't exit inappropriately if the -e option has
    been set.

gg. Restricted shells no longer allow functions to be exported.

hh. Changes to the pattern matching code so extended pattern matching works
    on systems with deficient shared library implementations, like MacOS X.

ii. Better error messages when a script with a leading `#!interp' fails
    to execute because of problems with `interp'.

jj. Fixed `compgen' to handle the `-o default' option better.

kk. Fixed the job control code to force an asynchronous process's standard
    input to /dev/null only if job control is not active.

ll. Fixed a possible infinite recursion problem when `fc ""=abc' (a null
    pattern) is used to re-execute a previous command.

mm. Fixed `declare [-a] var=value' to assign VALUE to element 0 if VAR is an
    array variable.  Similarly for `declare [-a] var[N]=value'.  This is like
    ksh93.

nn. Fixed a bug that caused `read -a aname' to work even if ANAME had been
    declared readonly.

oo. Fixed a possible integer overflow problem when constructing names for
    temporary files.

2.  New Features in Bash

a.  Added support for DESTDIR installation root prefix, so you can do a
    `make install DESTDIR=bash-root' and do easier binary packaging.

b.  Added support for builtin printf "'" flag character as per latest POSIX
    drafts.

c.  Support for POSIX.2 printf(1) length specifiers `j', `t', and `z' (from
    ISO C99).

d.  New autoconf macro, RL_LIB_READLINE_VERSION, for use by other applications
    (bash doesn't use very much of what it returns).

e.  `set [-+]o nolog' is recognized as required by the latest POSIX drafts,
    but ignored.

f.  New read-only `shopt' option:  login_shell.  Set to non-zero value if the
    shell is a login shell.

g.  New `\A' prompt string escape sequence; expands to time in 24 HH:MM format.

h.  New `-A group/-g' option to complete and compgen; does group name
    completion.

i.  New `-t' option to `hash' to list hash values for each filename argument.

j.  New [-+]O invocation option to set and unset `shopt' options at startup.

k.  configure's `--with-installed-readline' option now takes an optional
    `=PATH' suffix to set the root of the tree where readline is installed
    to PATH.

l.  The ksh-like `ERR' trap has been added.  The `ERR' trap will be run
    whenever the shell would have exited if the -e option were enabled.
    It is not inherited by shell functions.

m.  `readonly', `export', and `declare' now print variables which have been
    given attributes but not set by assigning a value as just a command and
    a variable name (like `export foo') when listing, as the latest POSIX
    drafts require.

n.  `bashbug' now requires that the subject be changed from the default.

o.  configure has a new `--enable-largefile' option, like other GNU utilities.

p.  `for' loops now allow empty word lists after `in', like the latest POSIX
    drafts require.

3.  Changes to Readline

a.  More `const' and type casting fixes.

b.  Changed rl_message() to use vsnprintf(3) (if available) to fix buffer
    overflow problems.

c.  The completion code no longer appends a `/' or ` ' to a match when
    completing a symbolic link that resolves to a directory name, unless
    the match does not add anything to the word being completed.  This
    means that a tab will complete the word up to the full name, but not
    add anything, and a subsequent tab will add a slash.

d.  Fixed a trivial typo that made the vi-mode `dT' command not work.

e.  Fixed the tty code so that ^S and ^Q can be inserted with rl_quoted_insert.

f.  Fixed the tty code so that ^V works more than once.

g.  Changed the use of __P((...)) for function prototypes to PARAMS((...))
    because the use of __P in typedefs conflicted g++ and glibc.

4.  New Features in Readline

a.  Added extern declaration for rl_get_termcap to readline.h, making it a
    public function (it was always there, just not in readline.h).

b.  New #defines in readline.h:  RL_READLINE_VERSION, currently 0x0402,
    RL_VERSION_MAJOR, currently 4, and RL_VERSION_MINOR, currently 2.

c.  New readline variable:  rl_readline_version, mirrors RL_READLINE_VERSION.

d.  New bindable boolean readline variable:  match-hidden-files.  Controls
    completion of files beginning with a `.' (on Unix).  Enabled by default.

e.  The history expansion code now allows any character to terminate a
    `:first-' modifier, like csh.

f.  The incremental search code remembers the last search string and uses
    it if ^R^R is typed without a search string.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05-release,
and the previous version, bash-2.05-beta2.

1.  Changes to Bash

a.  Make sure we note that the first line of a multi-line command was not
    saved in the history if the tests for HISTCONTROL succeed, but the
    HISTIGNORE check fails.

b.  Fixed a bug in the pattern matching code that caused `[' to be treated
    as a special character inside a `[...]' bracket expression.

c.  Fixed a bug in the pattern matching code that caused `]' to terminate
    a bracket expression even if it was the first character after the `['
    (or a leading `!' or `^').

d.  Made a small change to report a more user-friendly error message if
    execve(2) fails because of an error with the interpreter in a script
    with a leading `#! interpreter'.

e.  If the OS does not support an exec(2) magic number of `#!', make sure we
    have a non-null interpreter name before attempting to execute it.

f.  Fixed a bug that caused the shell process to end up in a different
    process group than the controlling terminal if a job-control shell was
    run with `exec' in the startup files.

g.  When started in POSIX mode, either by `bash --posix', `bash -o posix', or
    `sh', $SHELLOPTS includes `posix' and POSIXLY_CORRECT is set.

h.  Fixed a problem that caused the `\W' prompt string escape sequence to
    expand to nothing when $PWD was `//'.

i.  The `bashbug' shell script no longer uses $(...) command substitution.

j.  When `set' is invoked without options in POSIX mode, it no longer prints
    the names and definitions of shell functions.

2.  Changes to Readline

a.  rl_set_paren_blink_timeout() is now documented.

b.  Corrected history.3 man page: `$' is not in the default value of
    history_word_delimiters.

c.  If a hook function assigned to rl_event_hook sets rl_done to a non-zero
    value, rl_read_key() now immediately returns '\n' (which is assumed to
    be bound to accept-line).

3.  New Features in Bash

a.  The `>&word' redirection now works in POSIX mode as it does by default,
    since POSIX.2 leaves it unspecified.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05-beta2,
and the previous version, bash-2.05-beta1.

1.  Changes to Bash

a.  Fixed a bug in the arithmetic evaluation code so that a^=b is supported.

b.  Fixed startup so posixly_correct is retained across subshells begun to
    execute scripts without a leading `#!'.

c.  Fixed a bug that caused $(< file) to not work in a (...) subshell.

d.  Added config support for Linux running on the IBM S390.

e.  Fixed a bug that caused bash to get its input pointer out of sync when
    reading commands through a pipe and running a command with standard
    input redirected from a file.

f.  Made a change so that command completion now makes about half as many
    stat(2) calls when searching the $PATH.

g.  Fixed a bug that caused variable assignments preceding `return' to not
    be propagated to the shell environment in POSIX mode.

h.  Fixed a bug with ${parameter[:]?word} -- tilde expansion was not performed
    on `word'.

i.  In POSIX mode, `break' and `continue' do not complain and return success
    if called when the shell is not executing a loop.

j.  Fixed `bash -o posix' to work the same as `bash --posix'.

k.  Fixed a bug where variable assignments preceding `eval' or `source/.'
    would not show up in the environment exported to subshells run by the
    commands.

l.  In POSIX mode, shells started to execute command substitutions inherit
    the value of the `-e' option from their parent shell.

m.  In POSIX mode, aliases are expanded even in non-interactive shells.

n.  Changed some of the job control messages to display the text required by
    POSIX.2 when the shell is in POSIX mode.

o.  Fixed a bug in `test' that caused it to occasionally return incorrect
    results when non-numeric arguments were supplied to `-t'.

2.  Changes to Readline

a.  Some changes were made to avoid gcc warnings with -Wall.

b.  rl_get_keymap_by_name now finds keymaps case-insensitively, so
    `set keymap EMACS' works.

c.  The history file writing and truncation functions now return a useful
    status on error.

d.  Fixed a bug that could cause applications to dereference a NULL pointer
    if a NULL second argument was passed to history_expand().

3.  New Features in Bash

a.  doc/readline.3 has been moved to the readline distribution.

4.  New Features in Readline

a.  New function, rl_get_screen_size (int *rows, int *columns), returns
    readline's idea of the screen dimensions.

b.  The timeout in rl_gather_tyi (readline keyboard input polling function)
    is now settable via a function (rl_set_keyboard_input_timeout()).

c.  Renamed the max_input_history variable to history_max_entries; the old
    variable is maintained for backwards compatibility.

d.  The list of characters that separate words for the history tokenizer is
    now settable with a variable:  history_word_delimiters.  The default
    value is as before.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05-beta1,
and the previous version, bash-2.05-alpha1.

1.  Changes to Bash

a.  Changes to allow shared library and object building on the GNU Hurd.

b.  Fixes to the way exported functions are placed into the environment and
    cached.

c.  The globbing library once again respects locales when processing ranges
    in bracket expressions while doing pattern matching.

d.  System-specific configuration changes for:  Tru 64, Interix

e.  Bashbug now uses /usr/bin/editor as one of the editing alternatives, and
    will use mktemp(1) or tempfile(1), if present, for temporary file creation.

f.  Bash no longer performs a binary file check on a script argument that's
    really a tty (like /dev/fd/0 or /dev/stdin).

g.  Fixed a bug in the execution of shell scripts that caused the effects of
    $BASH_ENV to be undone in some cases.

h.  Fixed several bugs that made `bash [-i] /dev/stdin' not work correctly.

i.  Several changes to the job control code to avoid some signal state
    manipulation.

j.  The Bash malloc no longer blocks signals as often, which should make it
    faster.

k.  Fixed a parsing bug that did not allow backslash to escape a single quote
    inside a $'...' construct.

l.  Fixed a bug that caused things like ${var:=$'value'} to be parsed
    incorrectly.  This showed up in newer versions of autoconf.

m.  Fixed a bug in the bash-specific readline initialization that caused
    key bindings to bash-specific function names appearing in .inputrc to
    not be honored.

n.  Bash now sets the file descriptor it uses to save the file descriptor
    opened on a shell script to close on exec.

o.  Fixed a bug in the prompt string decoding that caused it to misbehave
    when presented an octal sequence of fewer than three characters.

p.  Fixed the `test' builtin to return an error if `[' is supplied a single
    argument that is not `]'.

q.  Fixed a bug that caused subshells started to run executable shell scripts
    without a leading `#!' to incorrectly inherit an argument list preceding
    a shell builtin (like such a script called from a script sourced with `.',
    where there were variable assignments preceding the `.' command)

r.  Fixed a bug that caused changes to variables supplied in an assignment
    statement preceding a shell builtin to not be honored (like a script
    run with `.').

s.  HOSTTYPE, OSTYPE, and MACHTYPE are set only if they do not have values
    when the shell is started.

t.  Fixed a bug that caused SIGINT to kill shell scripts after the script
    called `wait'.

u.  The `fc' builtin now tries to create its temporary files in the directory
    named by $TMPDIR.

v.  Bash no longer calls any Readline functions or uses any Readline variables
    not declared in readline.h.

w.  Fixed a bug that caused some substitutions involving $@ to not be split
    correctly, especially expansions of the form ${paramterOPword}.

x.  SSH2_CLIENT is now treated like SSH_CLIENT and not auto-exported if it
    appears in the initial environment.

y.  Fixed a couple of problems with shell scripts without a leading `#!'
    being executed out of shell functions that could cause core dumps if
    such a script attempted to execute `return'.

z.  Fixed a problem with the `-nt' and `-ot' binary operators for the
    `test/[' builtin and the `[[' conditional command that caused wrong
    return values if one of the file arguments did not exist.

aa. Fixed a bug that caused non-interactive shells which had previously
    executed `shopt -s expand_aliases' to fail to expand aliases in a
    command like `(command) &'.
 
2.  Changes to Readline

a.  Changes to make most (but not yet all -- there is still crlf()) of the
    exported readline functions declared in readline.h have an rl_ prefix.

b.  More `const' changes in function arguments, mostly for completion
    functions.

c.  Fixed a bug in rl_forward that could cause the point to be set to before
    the beginning of the line in vi mode.

d.  Fixed a bug in the callback read-char interface to make it work when a
    readline function pushes some input onto the input stream with
    rl_execute_next (like the incremental search functions).

e.  Fixed a file descriptor leak in the history file manipulation code that
    was tripped when attempting to truncate a non-regular file (like
    /dev/null).

f.  Some existing variables are now documented and part of the public
    interface (declared in readline.h):  rl_explict_arg, rl_numeric_arg,
    rl_editing_mode, rl_last_func.

g.  Renamed rltty_set_default_bindings to rl_tty_set_default_bindings and
    crlf to rl_crlf, so there are no public functions declared in readline.h
    without an `rl_' prefix.  The old functions still exist for backwards
    compatibility.

3.  New Features in Bash

a.  A new loadable builtin, realpath, which canonicalizes and expands symlinks
    in pathname arguments.

b.  When `set' is called without options, it prints function definitions in a
    way that allows them to be reused as input.  This affects `declare' and
    `declare -p' as well.

4.  New Features in Readline

a.  New application-callable function rl_set_prompt(const char *prompt):
    expands its prompt string argument and sets rl_prompt to the result.

b.  New application-callable function rl_set_screen_size(int rows, int cols):
    public method for applications to set readline's idea of the screen
    dimensions.

c.  The history example program (examples/histexamp.c) is now built as one
    of the examples.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.05-alpha1,
and the previous version, bash-2.04-release.

1.  Changes to Bash

a.  A fix was made to allow newlines in compond array assignments.

b.  configure now checks for real-time signals with unusable values.

c.  Interactive shells no longer exit if a substitution fails because of an
    unset variable within a sourced file.

d.  Fixed a problem with incorrect matching of extended glob patterns when
    doing pattern substitution.

e.  `{' is now quoted by the completion code when it appears in a filename.

f.  Fixed an error in pattern matching that caused the matcher to not
    correctly skip the rest of a bracket expression after a character
    matched.

g.  Fixed a bug in the IFS word splitting code to make a non-whitespace IFS
    character preceded by IFS whitespace part of the current delimiter rather
    than generating a separate field.

h.  The {!prefix@} expansion now generates separate words, analogous to $@,
    when double-quoted.

i.  Command substitution now ignores NUL bytes in the command output, and the
    parser ignores them on input.

j.  A fix was made to the job control code to prevent hanging processes when
    the shell thinks background processes are running but the kernel returns
    -1/ECHILD from waitpid().

k.  `pwd' now prints an error message if the write fails when displaying the
    current directory.

l.  When in POSIX mode, the shell prints trap dispostions without a leading
    `SIG' in the signal specification.

m.  Fixed a parser bug that caused the current command's line count to be
    messed up by a compound array assignment.

n.  Fixed a bug in the unwind-protect code that caused bad behavior on machines
    where ints and pointers are not the same size.

o.  System-specific configure changes for:  MacOS X.

p.  Changes for Cygwin to translate \r\n and \r to \n and to set file
    descriptors used for reading input to text mode in various places.

q.  Fixed a bug that caused `!' to occasionally not be honored when in
    a (...) subshell.

r.  Bash no longer assumes that getcwd() will return any useful error message
    in the buffer passed as an argument if the call fails.

s.  The `source', `.', and `fc' builtins no longer check whether a file is
    binary before reading commands from it.

t.  Subshells no longer turn off job control when they exit, since that
    sometimes resulted in the terminal being reset to the wrong process
    group.

u.  The history code no longer tries to save the second and subsequent lines
    of a multi-line command if the first line was not saved.

v.  The history saving code now does a better job of saving blank lines in a
    multi-line command.

w.  Removed a `feature' that made `ulimit' silently translate `unlimited' to
    the current hard limit, which obscured some kernel error returns.

x.  Fixed the grammar so that `}' is recognized as a reserved word after
    another reserved word, rather than requiring a `;' or newline.  This
    means that constructs like

	{ { echo a b c ; } }

    work as expected.

y.  Conditional commands ([[...]]) now perform tilde expansion on their
    arguments.

z.  Noted in the documentation that `set -a' will cause functions to be
    exported if they are defined after `set -a' is executed.

aa. When an interactive login shell starts, if $PWD and $HOME refer to the
    same directory but are not the same string, $PWD is set to $HOME.

bb. Fixed `printf' to handle invalid floating point numbers better.

cc. Temporary files are now created with random filenames, to improve security.

dd. The readline initialization code now binds the custom bash functions and
    key bindings after the readline defaults are set up.

ee. Fixed the `source' builtin to no longer overwrite a shell function's
    argument list, even if the sourced file changes the positional parameters.

ff. A bug fix was made in the expansion of `$*' in contexts where it should
    not be split, like assignment statements.

gg. Fixed a bug in the parameter substring expansion to handle conditional
    arithmetic expressions ( exp ? val1 : val2 ) without cutting the expression
    off at the wrong `:'.

hh. The `<>' redirection is no longer subject to the current setting of
    `noclobber', as POSIX.2 specifies.

ii. Fixed a bug in the conditional command parsing code that caused expressions
    in parentheses to occasionally be parsed incorrectly.

jj. Fixed a bug in the ((...)) arithmetic command to allow do...done or
    {...} to follow the )) without an intervening list terminator.

kk. `printf' now treats `\E' the same as `\e' when performing backslash escape
    expansion for the `%b' format specifier.

ll. When in POSIX mode, the shell no longer searches the current directory for
    a file to be sourced with `.' or `source' if `.' is not in $PATH.

mm. Interactive comments are no longer turned off when POSIX mode is disabled.

nn. The UID, EUID, HOSTNAME variables are not set if they are in the shell's
    environment when it starts up.

oo. Fixed a bug in the `command' builtin so the effect of a command like
    `command exec 4<file' is as if the `command' had been omitted.

pp. ${foo[@]} and ${foo[*]} now work as in ksh93 if `foo' is not an array
    variable.

qq. ${#foo[X]}, where X is 0, @, or *, now work as in ksh93 if `foo' is not
    an array variable.

rr. The shell's idea of an absolute pathname now takes into account a
    possible drive specification on Cygwin and other Windows systems.

ss. Fixed a bug which caused incorrect parsing of some multi-character
    constructs if they were split across input lines with backslash-newline
    line continuation.

tt. Fixed a bug that caused restricted shell mode to be set inappropriately
    when trying to execute a shell script without a leading `#!'.

uu. Shell function definitions no longer require that the body be a group
    command ( {...} ), as POSIX.2 requires.

vv. The `cd' and `pwd' builtins now process symlinks in pathnames internally
    and should require many fewer calls to getcwd().

ww. Fixed a bug that caused a pipeline's process group to be set incorrectly
    if one of the pipeline elements contained a command substitution.

xx. Fixed a bug that caused core dumps when expanding the value of HISTIGNORE.

yy. The output of `set' is now quoted using $'...' so invisible characters are
    displayed as escape sequences.

zz. Fixed the help text for `unset', since PATH and IFS may both be unset.

aaa. The shell no longer puts directory names into the command hash table.

bbb. Fixed a bug in `read' that caused it to occasionally free memory twice if
     it was interrupted after reading a large amount of data.

ccc. Assignment statements that attempt to assign values to readonly variables
     now cause the command to return an error status.

ddd. Fixed a bug that could cause incorrect output if a $(<file) construct was
     interrupted.

eee. GROUPS and FUNCNAME now return an error status when assignment is
     attempted, but may be unset (in which case they lose their special
     properties).  In all respects except unsetting, they are readonly.

fff. The string-to-integer conversion code now ignores trailing whitespace in
     the string, even if strtol(3) does not.

ggg. The tcsh magic-space function now does a better job of inserting the
     space close to where the point was before the history expansion, rather
     than just appending it.

hhh. Fixed a bug which caused a file sourced from an interactive shell to
     fill up the jobs table if it ran lots of jobs.

iii. Fixed a bug in the parameter pattern substitution code to avoid infinite
     recursion on zero-length matches.

2.  Changes to Readline

a.  When setting the terminal attributes on systems using `struct termio',
    readline waits for output to drain before changing the attributes.

b.  A fix was made to the history word tokenization code to avoid attempts to
    dereference a null pointer.

c.  Readline now defaults rl_terminal_name to $TERM if the calling application
    has left it unset, and tries to initialize with the resultant value.

d.  Instead of calling (*rl_getc_function)() directly to get input in certain
    places, readline now calls rl_read_key() consistently.

e.  Fixed a bug in the completion code that allowed a backslash to quote a
    single quote inside a single-quoted string.

f.  rl_prompt is no longer assigned directly from the argument to readline(),
    but uses memory allocated by readline.  This allows constant strings to
    be passed to readline without problems arising when the prompt processing
    code wants to modify the string.

g.  Fixed a bug that caused non-interactive history searches to return the
    wrong line when performing multiple searches backward for the same string.

h.  Many variables, function arguments, and function return values are now
    declared `const' where appropriate, to improve behavior when linking with
    C++ code.

i.  The control character detection code now works better on systems where
    `char' is unsigned by default.

j.  The vi-mode numeric argument is now capped at 999999, just like emacs mode.

k.  The Function, CPFunction, CPPFunction, and VFunction typedefs have been
    replaced with a set of specific prototyped typedefs, though they are
    still in the readline header files for backwards compatibility.

m.  Nearly all of the (undocumented) internal global variables in the library
    now have an _rl_ prefix -- there were a number that did not, like
    screenheight, screenwidth, alphabetic, etc.

n.  The ding() convenience function has been renamed to rl_ding(), though the
    old function is still defined for backwards compatibility.

o.  The completion convenience functions filename_completion_function,
    username_completion_function, and completion_matches now have an rl_
    prefix, though the old names are still defined for backwards compatibility.

p.  The functions shared by readline and bash (linkage is satisfied from bash
    when compiling with bash, and internally otherwise) now have an sh_ prefix.

q.  Changed the shared library creation procedure on Linux and BSD/OS 4.x so
    that the `soname' contains only the major version number rather than the
    major and minor numbers.

r.  Fixed a redisplay bug that occurred when the prompt spanned more than one
    physical line and contained invisible characters.

3.  New Features in Bash

a.  Added a new `--init-file' invocation argument as a synonym for `--rcfile',
    per the new GNU coding standards.

b.  The /dev/tcp and /dev/udp redirections now accept service names as well as
    port numbers.

c.  `complete' and `compgen' now take a `-o value' option, which controls some
    of the aspects of that compspec.  Valid values are:

	default - perform bash default completion if programmable
		  completion produces no matches
	dirnames - perform directory name completion if programmable
		   completion produces no matches
	filenames - tell readline that the compspec produces filenames,
		    so it can do things like append slashes to
		    directory names and suppress trailing spaces

4.  New Features in Readline

a.  The blink timeout for paren matching is now settable by applications.

b.  _rl_executing_macro has been renamed to rl_executing_macro, which means
    it's now part of the public interface.

c.  Readline has a new variable, rl_readline_state, which is a bitmap that
    encapsulates the current state of the library; intended for use by
    callbacks and hook functions.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.04-release,
and the previous version, bash-2.04-beta5.

1.  Changes to Bash

a.  Better compile-time and configure-time checks for the necessity of
    inet_aton().

b.  A bug was fixed in the expansion of "${@:-}" when there are positional
    parameters.

c.  A typo was fixed in the output of `complete'.

d.  The matches generated for a word by the `-W' argument to complete and
    compgen are now matched against the word being completed, and only
    matches are returned as the result.

e.  Some fixes were made for systems which do not restart read(2) when a
    signal caught by bash is received.

f.  A bug was fixed which caused the umask to be set to 0 when an invalid
    symbolic mode mask was parsed.

g.  Fixed a bug that could cause a core dump if a SIGCHLD was received while
    performing an assignment statement using command substitution.

h.  Changed the word splitting function for programmable completion so cases
    in which the cursor is between words are handled a bit better.

2.  Changes to Readline

a.  rl_funmap_names() is now documented.

3.  New Features in Bash

a.  The LC_NUMERIC variable is now treated specially, and used to set the
    LC_NUMERIC locale category for number formatting, e.g., when `printf'
    displays floating-point numbers.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.04-beta5,
and the previous version, bash-2.04-beta4.

1.  Changes to Bash

a.  A couple of changes were made to the Makefiles for easier building on
    non-Unix systems.

b.  Fixed a bug where the current prompt would be set to $PS2 at startup.

c.  The shell script that tests an already-installed version was changed to
    remove the directory it created its test programs in at exit.

d.  Several changes were made to the code that tokenizes an input line for
    the programmable completion code.  Shell metacharacters will now appear
    as individual words in the word list passed to the completion functions.
    Some of the example completion shell functions were changed to understand
    redirection operators.

e.  A bug was fixed that, under obscure circumstances, could confuse the
    parser when a shell function was run by the programmable completion code.

f.  A bug was fixed in the ulimit builtin for systems not using getrlimit().

g.  The execution code now propagates the correct exit status back to the rest
    of the code if the return value of a subshell command was being inverted.
    Some new test cases for inverting return values with the `!' reserved
    word have been added.

h.  Negative exponents in the arithmetic evaluation of v**e now return an
    evaluation error.

i.  A bug that caused bash to check the wrong process in a pipeline for
    abnormal termination (and consequently resetting the terminal attributes)
    was fixed.

j.  Fixed a bug that caused $PS2 to be displayed after PROMPT_COMMAND was
    executed.

2.  Changes to Readline

1.  Fixed a bug in a C preprocessor define that caused the keypad control
    functions to be compiled out for all platforms except DJGPP.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.04-beta4,
and the previous version, bash-2.04-beta3.

1.  Changes to Bash

a.  A couple of changes were made to the redirection to attempt to avoid
    race conditions and malicious file replacement.

2.  A change was made to the string evaluation code (used for command 
    substitution, `eval', and the `.' builtin) to fix an obscure core
    dump on alpha machines.

3.  A bug that caused $LINENO to be wrong when executing arithmetic for
    commands was fixed.

4.  A couple of memory leaks in the programmable completion code were fixed.

5.  A bug that could cause a core dump by freeing memory twice during a call
    to `eval' if `set -u' had been enabled and an undefined variable was
    referenced was fixed.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.04-beta3,
and the previous version, bash-2.04-beta2.

1.  Changes to Bash

a.  Bash should run the appropriate startup files when invoked by ssh2.

b.  Fixed a bug in the parsing of conditional commands that could cause a
    core dump.

c.  Fixed a bug in parsing job specifications that occasionally caused
    core dumps when an out-of-range job was referenced.

d.  Fixed the `type' and `command' builtins to do better reporting of
    commands that are not found in $PATH or the hash table.

e.  Fixed a POSIX.2 compliance problem in the command builtin -- commands
    are supposed to be reported as full pathnames.

f.  The `echo' builtin now returns failure if a write error occurs.

g.  Fixed a bug which caused the locale to not be reset correctly when
    LC_ALL was unset.

h.  Changed description of `getopts' in man page and reference manual to make
    it clear that option characters may be characters other than letters.

i.  If the shell exits while in a function, make sure that any trap on EXIT
    doesn't think the function is still executing.

j.  Bashbug now tries harder to find a usable editor if $EDITOR is not set,
    rather than simply defaulting to `emacs'.

k.  Changes to the scripts that guess and canonicalize the system type, from
    the latest `automake' distribution via Debian.

l.  When using named pipes for process substitution, make sure the file
    descriptors opened for reading are set to non-blocking mode.

m.  Fixed a bug that caused termination of pipelines that are killed by a
    signal to not be reported in some cases.

n.  When not in literal-history mode, shell comment lines are not added to
    the history list.

o.  When running in POSIX.2 mode, bash no longer performs word splitting on
    the expanded value of the word supplied as the filename argument to
    redirection operators.

p.  The prompt string decoding code now backslash-quotes only characters that
    are special within double quotes when expanding the \w and \W escape
    sequences.

q.  Fixed a bug in the prompt decoding code that could cause a non-interactive
    shell to seg fault if `\u' was used in PS4 and the shell was in xtrace
    mode.

r.  Fixed a bug that caused function definitions to be printed with any
    redirections that should be attached to the entire function before the
    closing brace.

s.  Changed the tilde expansion code for Cygwin systems to avoid creating
    pathnames beginning with `//' if $HOME == `/'.

t.  Fixed a couple of autoconf tests to avoid creating files with fixed names
    in /tmp.

u.  The `trap' and `kill' builtins now know the names of the POSIX.1b real-
    time signals on systems which support them.

2.  Changes to Readline

a.  Fixed a problem with the single-quote quoting function that could cause
    buffer overflows.

b.  Fixed a bug that caused incorrect `stat characters' to be printed if
    the files being completed were in the root directory and visible-stats
    was enabled.

3.  New Features in Bash

a.  There is a new `rbash.1' manual page, from the Debian release.

b.  The `--enable-usg-echo-default' option to `configure' has been renamed to
    `--enable-xpg-echo-default'.  The old option is still there for backwards
    compatibility.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.04-beta2,
and the previous version, bash-2.04-beta1.

1.  Changes to Bash

a.  Fixed a bug that could cause pipes to be closed inappropriately in
    some obscure cases.

b.  Fixed a bug that caused creation of the exported environment to clobber
    the current command string if there were any exported shell functions.

c.  Some changes were made to reduce bash's memory usage.

d.  Fixed a problem with programmable completion and filenames to be
    completed containing quote characters.

e.  Changed the code the removes named pipes created for the <(...) and >(...)
    expansions to defer removal until after any current shell function has
    finished executing.

f.  Fixed a bug in `select' which caused it to not handle the `continue'
    builtin correctly.

g.  Autoconf tests added for cygwin32 and mingw32.

2.  New Features in Bash

a.  The `--with-bash-malloc' configure option replaces `--with-gnu-malloc'
    (which is still there for backwards compatibility).

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.04-beta1,
and the previous version, bash-2.04-alpha1.

1.  Changes to Bash

a.  Fixed a bug in the programmable completion code that occurred when
    trying to complete command lines containing a `;' or `@'.

b.  The file descriptor from which the shell is reading a script is now
    moved to a file descriptor above the user-addressible range.

c.  Changes to `printf' so that it can handle integers beginning with 0
    or 0x as octal and hex, respectively.

d.  Fixes to the programmable completion code so it handles nonsense like
    `compgen -C xyz' gracefully.

e.  The shell no longer modifies the signal handler for SIGPROF, allowing
    profiling again on certain systems.

f.  The shell checks for a new window size, if the user has requested it,
    after a process exits due to a signal.

g.  Fixed a bug with variables with null values in a program's temporary
    environment and the bash getenv() replacement.

h.  `declare' and the other builtins that take variable assignments as
    arguments now honor `set -a' and mark modified variables for export.

i.  Some changes were made for --dump-po-strings mode when writing strings
    with embedded newlines.

j.  The code that caches export strings from the initial environment now
    duplicates the string rather than just pointing into the environment.

k.  The filename completion quoting code now uses single quotes by default
    if the filename being completed contains newlines, since \<newline>
    has a special meaning to the parser.

l.  Bash now uses typedefs bits32_t and u_bits32_t instead of int32_t and
    u_int32_t, respectively to avoid conflicts on certain Unix versions.

m.  Configuration changes were made for: Rhapsody, Mac OS, FreeBSD-3.x.

n.  Fixed a problem with hostname-to-ip-address translation in the
    /dev/(tcp|udp)/hostname/port redirection code.

o.  The texinfo manual has been reorganized slightly.

p.  Filename generation (globbing) range comparisons in bracket expressions
    no longer use strcoll(3) even if it is available, since it has unwanted
    effects in certain locales.

q.  Fixed a cosmetic problem in the source that caused the shell to not
    compile if DPAREN_ARITHMETIC was not defined but ARITH_FOR_COMMAND was.

r.  Fixed a bug in the here-document code tripped when the file descriptor
    opened to the file containing the text of the here document was the
    same as a redirector specified by the user.

s.  Fixed a bug where the INVERT_RETURN flag was not being set for `pipeline'
    in `time ! pipeline'.

t.  Fixed a bug with the `wait' builtin which manifested itself when an
    interrupt was received while the shell was waiting for asynchronous
    processes in a shell script.

u.  Fixed the DEBUG trap code so that it has the correct value of $?.

v.  Fixed a bug in the parameter pattern substitution code that could cause
    the shell to attempt to free unallocated memory if the pattern started
    with `/' and an expansion error occurs.

w.  Fixed a bug in the positional parameter substring code that could
    cause the shell to loop freeing freed memory.

x.  Fixed a bug in the positional parameter pattern substitution code so
    that it correctly handles null replacement strings with a pattern
    string prefixed with `%' or `#'.

y.  The shell no longer attempts to import functions from the environment if
    started with `-n'.

z.  Fixed a bug that caused `return' in a command substitution executed in
    a shell function to return from the function in a subshell and continue
    execution.

aa. `hash -p /pathname/with/slashes name' is no longer allowed when the shell
    is restricted.

bb. The wait* job control functions now behave better if called when there
    are no unwaited-for children.

cc. Command substitution no longer unconditionally disables job control in
    the subshell started to run the command.

dd. A bug was fixed that occasionally caused traps to mess up the parser
    state.

ee. `bashbug' now honors user headers in the mail message it sends.

ff. A bug was fixed that caused the `:p' history modifier to not print the
    history expansion if the `histverify' option was set.

2.  Changes to Readline

a.  Fixed a bug in the redisplay code for lines with more than 256 line
    breaks.

b.  A bug was fixed which caused invisible character markers to not be
    stripped from the prompt string if the terminal was in no-echo mode.

c.  Readline no longer tries to get the variables it needs for redisplay
    from the termcap entry if the calling application has specified its
    own redisplay function.  Readline treats the terminal as `dumb' in
    this case.

d.  Fixes to the SIGWINCH code so that a multiple-line prompt with escape
    sequences is redrawn correctly.

3.  New Features in Bash

a.  `bashbug' now accepts `--help' and `--version' options.

b.  There is a new `xpg_echo' option to `shopt' that controls the behavior
    of echo with respect to backslash-escaped characters at runtime.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.04-alpha1,
and the previous version, bash-2.04-devel.

1.  Changes to Bash

a.  Fixed a bug that could cause core dumps when performing substring
    expansion.

b.  Shared object configuration changes for:  Solaris, OSF/1

c.  The POSIX_GLOB_LIBRARY code that uses the POSIX.2 globbing facilities
    for pathname expansion now understands GLOBIGNORE.

d.  The code that implements `eval' was changed to save the value of the
    current prompt, so an eval in a shell function called by the programmable
    completion code will not change the prompt to $PS2.

e.  Restored the undocumented NON_INTERACTIVE_LOGIN_SHELLS #define to
    config-top.h.  If this is defined, all login shells will read the
    startup files, not just interactive and non-interactive started with
    the `--login' option.

f.  Fixed a bug that caused the expansion code to occasionally dump core if
    IFS contained characters > 128.

g.  Fixed a problem with the grammar so that a newline is not required
    after the `))' in the new-style arithmetic for statement; a semicolon
    may be used as expected.

h.  Variable indirection may now reference the shell's special variables.

i.  The $'...' and $"..." constructs are now added to the history correctly
    if they contain newlines and command-oriented history is enabled.

j.  It is now an error to try to assign a value to a function-local copy
    of a readonly shell variable (declared with the `local' builtin).

2.  Changes to Readline

a.  The history file code now uses O_BINARY mode when reading and writing
    the history file on cygwin32.

3.  New Features in Bash

a.  A new programmable completion facility, with two new builtin commands:
    complete and compgen.

b.  configure has a new option, `--enable-progcomp', to compile in the
    programmable completion features (enabled by default).

c.  `shopt' has a new option, `progcomp', to enable and disable programmable
    completion at runtime.

d.  Unsetting HOSTFILE now clears the list of hostnames used for completion.

4.  New Features in Readline

a.  A new variable, rl_gnu_readline_p, always 1.  The intent is that an
    application can verify whether or not it is linked with the `real'
    readline library or some substitute.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.04-devel,
and the previous version, bash-2.03-release.

1.  Changes to Bash

a.  System-specific configuration and source changes for:  Interix, Rhapsody

b.  Fixed a bug in execute_cmd.c that resulted in a compile-time error if
    JOB_CONTROL was not defined.

c.  An obscure race condition in the trap code was fixed.

d.  The string resulting from $'...' is now requoted to avoid any further
    expansion.

e.  The $'...' quoting syntax now allows backslash to escape a single quote,
    for ksh-93 compatibility.

f.  The $"..." quoting syntax now escapes backslashes and double quotes in
    the translated string when displaying them with the --dump-po-strings
    option.

g.  `echo -e' no longer converts \' to '.

h.  Fixes were made to the extended globbing code to handle embedded (...)
    patterns better.

i.  Some improvements were made to the code that unsets `nodelay' mode on
    the file descriptor from which bash is reading input.

j.  Some changes were made to the replacement termcap library for better
    operation on MS-DOS.

k.  Some changes were made to the tilde expansion code to handle backslash
    as a pathname separator on MS-DOS.

l.  The source has been reorganized a little bit -- there is now an `include'
    subdirectory, and lib/posixheaders has been removed.

m.  Improvements were made to the `read' builtin so that it makes many
    fewer read(2) system calls.

n.  The expansion of $- will include `c' and `s' when those options are
    supplied at shell invocation.

o.  Several improvements were made to the completion code:  variable completion
    now works better when there are unterminated expansions, command
    completion understands quotes better, and completion now works in certain
    unclosed $(... constructs.

p.  The arithmetic expansion code was fixed to not need the value of a
    variable being assigned a value (fixes the "ss=09; let ss=10" bug).

q.  Some changes were made to make exported environment creation faster.

r.  The html documentation will be installed into $(htmldir) if that variable
    has a value when `make install' is run.

s.  Fixed a bug that would cause the bashrc file to be sourced inappropriately
    when bash is started by sshd.

t.  The SSH_CLIENT environment variable is no longer auto-exported.

u.  A bug that caused redirections with (...) subshells to be performed in
    the wrong order was fixed.

v.  A bug that occasionally caused inappropriate expansion of assignment
    statements in compound array assignments was fixed.

w.  The code that parses the words in a compound array assignment was
    simplified considerably and should work better now.

x.  Fixes to the non-job-control code in nojobs.c to make it POSIX.2-compliant
    when a user attempts to retrieve the status of a terminated background
    process.

y.  Fixes to the `printf' builtin so that it doesn't try to expand all
    backslash escape sequences in the format string before parsing it for
    % format specifiers.

2.  Changes to Readline

a.  The history library tries to truncate the history file only if it is a
    regular file.

b.  A bug that caused _rl_dispatch to address negative array indices on
    systems with signed chars was fixed.

c.  rl-yank-nth-arg now leaves the history position the same as when it was
    called.

d.  Changes to the completion code to handle MS-DOS drive-letter:pathname
    filenames.

e.  Completion is now case-insensitive by default on MS-DOS.

f.  Fixes to the history file manipulation code for MS-DOS.

g.  Readline attempts to bind the arrow keys to appropriate defaults on MS-DOS.

h.  Some fixes were made to the redisplay code for better operation on MS-DOS.

i.  The quoted-insert code will now insert tty special chars like ^C.

j.  A bug was fixed that caused the display code to reference memory before
    the start of the prompt string.

k.  More support for __EMX__ (OS/2).

l.  A bug was fixed in readline's signal handling that could cause infinite
    recursion in signal handlers.

m.  A bug was fixed that caused the point to be less than zero when rl_forward
    was given a very large numeric argument.

n.  The vi-mode code now gets characters via the application-settable value
    of rl_getc_function rather than calling rl_getc directly.

3.  New Features in Bash

a.  The history builtin has a `-d offset' option to delete the history entry
    at position `offset'.

b.  The prompt expansion code has two new escape sequences: \j, the number of
    active jobs; and \l, the basename of the shell's tty device name.

c.  The `bind' builtin has a new `-x' option to bind key sequences to shell
    commands.

d.  There is a new shell option, no_empty_command_completion, which, when
    enabled, disables command completion when TAB is typed on an empty line.

e.  The `help' builtin has a `-s' option to just print a builtin's usage
    synopsis.

f.  There are several new arithmetic operators:  id++, id-- (variable
    post-increment/decrement), ++id, --id (variabl pre-increment/decrement),
    expr1 , expr2 (comma operator).

g.  There is a new ksh-93 style arithmetic for command:
	for ((expr1 ; expr2; expr3 )); do list; done

h.  The `read' builtin has a number of new options:
	-t timeout	only wait timeout seconds for input
	-n nchars	only read nchars from input instead of a full line
	-d delim	read until delim rather than newline
	-s		don't echo input chars as they are read

i.  The redirection code now handles several filenames specially:
    /dev/fd/N, /dev/stdin, /dev/stdout, and /dev/stderr, whether or
    not they are present in the file system.

j.  The redirection code now recognizes pathnames of the form
    /dev/tcp/host/port and /dev/udp/host/port, and tries to open a socket
    of the appropriate type to the specified port on the specified host.

k.  The ksh-93 ${!prefix*} expansion, which expands to the names of all
    shell variables whose names start with prefix, has been implemented.

l.  There is a new dynamic variable, FUNCNAME, which expands to the name of
    a currently-executing function.  Assignments to FUNCNAME have no effect.

m.  The GROUPS variable is no longer readonly; assignments to it are silently
    discarded.  This means it can be unset.

4.  New Features in Readline

a.  Parentheses matching is now always compiled into readline, and enabled
    or disabled when the value of the `blink-matching-paren' variable is
    changed.

b.  MS-DOS systems now use ~/_inputrc as the last-ditch inputrc filename.

c.  MS-DOS systems now use ~/_history as the default history file.

d.  history-search-{forward,backward} now leave the point at the end of the
    line when the string to search for is empty, like
    {reverse,forward}-search-history.

e.  history-search-{forward,backward} now leave the last history line found
    in the readline buffer if the second or subsequent search fails.

f.  New function for use by applications:  rl_on_new_line_with_prompt, used
    when an application displays the prompt itself before calling readline().

g.  New variable for use by applications:  rl_already_prompted.  An application
    that displays the prompt itself before calling readline() must set this to
    a non-zero value.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.03-release,
and the previous version, bash-2.03-beta2.

1.  Changes to Bash

a.  A file descriptor leak in the `fc' builtin was fixed.

b.  A bug was fixed in the `read' builtin that caused occasional spurious
    failures when using `read -e'.

c.  The version code needed to use the value of the cpp variable
    CONF_MACHTYPE rather than MACHTYPE.

d.  A new test was added to exercise the command printing and copying code.

e.  A bug was fixed that caused `time' to be recognized as a reserved word
    if it was the first pattern in a `case' statement pattern list.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.03-beta2,
and the previous version, bash-2.03-beta1.

1.  Changes to Bash

a.  Slight additions to support/shobj-conf, mostly for the benefit of AIX 4.2.

b.  config.{guess,sub} support added for the NEC SX4.

c.  Changed some of the cross-compiling sections of the configure macros in
    aclocal.m4 so that configure won't abort.

d.  Slight changes to how the HTML versions of the bash and readline manuals
    are generated.

e.  Fixed conditional command printing to avoid interpreting printf `%'-escapes
    in arguments to [[.

f.  Don't include the bash malloc on all variants of the alpha processor.

g.  Changes to configure to make --enable-profiling work on Solaris 2.x.

h.  Fixed a bug that manifested itself when shell functions were called
    between calls to `getopts'.

i.  Fixed pattern substitution so that a bare `#'as a pattern causes the
    replacement string to be prefixed to the search string, and a bare
    `%' causes the replacement string to be appended to the search string.

j.  Fixed a bug in the command execution code that caused child processes
    to occasionally have the wrong value for $!.

2.  Changes to Readline

a.  Added code to the history library to catch history substitutions using
    `&' without a previous history substitution or search having been
    performed.

3.  New Features in Bash

4.  New Features in Readline

a.  New bindable variable: `isearch-terminators'.

b.  New bindable function: `forward-backward-delete-char' (unbound by default).

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.03-beta1,
and the previous version, bash-2.03-alpha.
    
1.  Changes to Bash

a.  A change was made to the help text for `{...}' to make it clear that a
    semicolon is required before the closing brace.

b.  A fix was made to the `test' builtin so that syntax errors cause test
    to return an exit status > 1.

c.  Globbing is no longer performed on assignment statements that appear as
    arguments to `assignment builtins' such as `export'.

d.  System-specific configuration changes were made for:  Rhapsody,
    AIX 4.2/gcc, BSD/OS 4.0.

e.  New loadable builtins: ln, unlink.

f.  Some fixes were made to the globbing code to handle extended glob patterns
    which immediately follow a `*'.

g.  A fix was made to the command printing code to ensure that redirections
    following compound commands have a space separating them from the rest
    of the command.

h.  The pathname canonicalization code was changed to produce fewer leading
    `//' sequences, since those are interpreted as network file system
    pathnames on some systems.

i.  A fix was made so that loops containing `eval' commands in commands passed
    to `bash -c' would not exit prematurely.

j.  Some changes were made to the job reaping code when the shell is not
    interactive, so the shell will retain exit statuses longer for examination
    by `wait'.

k.  A fix was made so that `jobs | command' works again.

l.  The erroneous compound array assignment var=((...)) is now a syntax error.

m.  A change was made to the dynamic loading code in `enable' to support
    Tenon's MachTen.

n.  A fix was made to the globbing code so that extended globbing patterns
    will correctly match `.' in a bracket expression.

2.  Changes to Readline

a.  A fix was made to the completion code in which a typo caused the wrong
    value to be passed to the function that computed the longest common
    prefix of the list of matches.

b.  The completion code now checks the value of rl_filename_completion_desired,
    which is set by application-supplied completion functions to indicate
    that filename completion is being performed, to decide whether or not to
    call an application-supplied `ignore completions' function.

3.  New Features in Bash

a.  A change was made to the startup file code so that any shell begun with
    the `--login' option, even non-interactive shells, will source the login
    shell startup files.

4.  New Features in Readline

a.  A new variable, rl_erase_empty_line, which, if set by an application using
    readline, will cause readline to erase, prompt and all, lines on which the
    only thing typed was a newline.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.03-alpha,
and the previous version, bash-2.02.1-release.

1.  Changes to Bash

a.  System-specific configuration changes were made for: Irix 6.x, Unixware 7.

b.  The texi2dvi and texi2html scripts were updated to the latest versions
    from the net.

c.  The configure tests that determine which native type is 32 bits were
    changed to not require a compiled program.

d.  Fixed a bug in shell_execve that could cause memory to be freed twice
    after a failed exec.

e.  The `printf' test uses `diff -a' if it's available to prevent confusion
    due to the non-ascii output.

f.  Shared object configuration is now performed by a shell script,
    support/shobj-conf, which generates values to be substituted into
    makefiles by configure.

g.  Some changes were made to `ulimit' to avoid the use of RLIM_INVALID as a
    return value.

h.  Changes were made to `ulimit' to work around HPUX 9.x's peculiar
    handling of RLIMIT_FILESIZE.

i.  Some new loadable builtins were added: id, printenv, sync, whoami, push,
    mkdir.  `pushd', `popd', and `dirs' can now be built as regular or
    loadable builtins from the same source file.

j.  Changes were made to `printf' to handle NUL bytes in the expanded format
    string.

k.  The various `make clean' Makefile targets now descend into lib/sh.

l.  The `type' builtin was changed to use the internal `getopt' so that things
    like `type -ap' work as expected.

m.  There is a new configuration option, --with-installed-readline, to link
    bash with a locally-installed version of readline.  Only readline version
    4.0 and later releases can support this.  Shared and static libraries
    are supported.  The installed include files are used.

n.  There is a new autoconf macro used to find which basic type is 64 bits.

o.  Dynamic linking and loadable builtins should now work on SCO 3.2v5*,
    AIX 4.2 with gcc, Unixware 7, and many other systems using gcc, where
    the `-shared' options works correctly.

p.  A bug was fixed in the bash filename completion code that caused memory to
    be freed twice if a directory name containing an unset variable was
    completed and the -u option was set.

q.  The prompt expansion code now quotes the `$' in the `\$' expansion so it
    is not processed by subsequent parameter expansion.

r.  Fixed a parsing bug that caused a single or double quote after a `$$' to
    trigger ANSI C expansion or locale translation.

s.  Fixed a bug in the globbing code that caused quoted filenames containing
    no globbing characters to sometimes be incorrectly expanded.

t.  Changes to the default prompt strings if prompt string decoding is not
    compiled into the shell.

u.  Added `do', `then', `else', `{', and `(' to the list of keywords that may
    precede the `time' reserved word.

v.  The shell may now be cross-built for BeOS as well as cygwin32.

w.  The conditional command execution code now treats `=' the same as `=='
    for deciding when to perform pattern matching.

x.  The `-e' option no longer causes the shell to exit if a command exits
    with a non-zero status while running the startup files.

y.  The `printf' builtin no longer dumps core if a modifier is supplied in
    the format string without a conversion character (e.g. `%h').

z.  Array assignments of the form a=(...) no longer show up in the history
    list.

aa. The parser was fixed to obey the POSIX.2 rules for finding the closing
    `}' in a ${...} expression.

bb. The history file is now opened with mode 0600 rather than 0666, so bash
    no longer relies on the user's umask being set appropriately.

cc. Setting LANG no longer causes LC_ALL to be assigned a value; bash now
    relies on proper behavior from the C library.

dd. Minor changes were made to allow quoted variable expansions using
    ${...} to be completed correctly if there is no closing `"'.

ee. Changes were made to builtins/Makefile.in so that configuring the shell
    with `--enable-profiling' works right and builtins/mkbuiltins is
    generated.

2.  Changes to Readline

a.  The version number is now 4.0.

b.  There is no longer any #ifdef SHELL code in the source files.

c.  Some changes were made to the key binding code to fix memory leaks and
    better support Win32 systems.

d.  Fixed a silly typo in the paren matching code -- it's microseconds, not
    milliseconds.

e.  The readline library should be compilable by C++ compilers.

f.  The readline.h public header file now includes function prototypes for
    all readline functions, and some changes were made to fix errors in the
    source files uncovered by the use of prototypes.

g.  The maximum numeric argument is now clamped at 1000000.

h.  Fixes to rl_yank_last_arg to make it behave better.

i.  Fixed a bug in the display code that caused core dumps if the prompt
    string length exceeded 1024 characters.

j.  The menu completion code was fixed to properly insert a single completion
    if there is only one match.

k.  A bug was fixed that caused the display code to improperly display tabs
    after newlines.

3.  New Features in Bash

a.  New `shopt' option, `restricted_shell', indicating whether or not the
    shell was started in restricted mode, for use in startup files.

b.  Filename generation is now performed on the words between ( and ) in
    array assignments (which it probably should have done all along).

c.  OLDPWD is now auto-exported, as POSIX.2 seems to require.

d.  ENV and BASH_ENV are read-only variables in a restricted shell.

4.  New Features in Readline

a.  Many changes to the signal handling:
	o Readline now catches SIGQUIT and cleans up the tty before returning;
	o A new variable, rl_catch_signals, is available to application writers 
	  to indicate to readline whether or not it should install its own
	  signal handlers for SIGINT, SIGTERM, SIGQUIT, SIGALRM, SIGTSTP,
	  SIGTTIN, and SIGTTOU;
	o A new variable, rl_catch_sigwinch, is available to application
	  writers to indicate to readline whether or not it should install its
	  own signal handler for SIGWINCH, which will chain to the calling
	  applications's SIGWINCH handler, if one is installed;
	o There is a new function, rl_free_line_state, for application signal
	  handlers to call to free up the state associated with the current
	  line after receiving a signal;
	o There is a new function, rl_cleanup_after_signal, to clean up the
	  display and terminal state after receiving a signal;
	o There is a new function, rl_reset_after_signal, to reinitialize the
	  terminal and display state after an application signal handler
	  returns and readline continues

b.  There is a new function, rl_resize_terminal, to reset readline's idea of
    the screen size after a SIGWINCH.

c.  New public functions: rl_save_prompt and rl_restore_prompt.  These were
    previously private functions with a `_' prefix.

d.  New function hook: rl_pre_input_hook, called just before readline starts
    reading input, after initialization.

e.  New function hook: rl_display_matches_hook, called when readline would
    display the list of completion matches.  The new function
    rl_display_match_list is what readline uses internally, and is available
    for use by application functions called via this hook.

f.  New bindable function, delete-char-or-list, like tcsh.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.02.1-release,
and the previous version, bash-2.02-release.

1.  Changes to Bash

a.  A bug that caused the bash readline support to not compile unless aliases
    and csh-style history were configured into the shell was fixed.

b.  Fixed a bug that could cause a core dump when here documents contained
    more than 1000 characters.

c.  Fixed a bug that caused a CDPATH entry of "" to not be treated the same
    as the current directory when in POSIX mode.

d.  Fixed an alignment problem with the memory returned by the bash malloc,
    so returned memory is now 64-bit aligned.

e.  Fixed a bug that caused command substitutions executed within pipelines
    to put the terminal in the wrong process group.

f.  Fixes to support/config.sub for: alphas, SCO Open Server and Open Desktop,
    Unixware 2, and Unixware 7.

g.  Fixes to the pattern matching code to make it work correctly for eight-bit
    characters.

h.  Fixed a problem that occasionally caused the shell to display the wrong
    value for the new working directory when changing to a directory found
    in $CDPATH when in physical mode.

i.  Fixed a bug that caused core dumps when using conditional commands in
    shell functions.

j.  Fixed a bug that caused the printf builtin to loop forever if the format
    string did not consume any of the arguments.

k.  Fixed a bug in the parameter expansion code that caused "$@" to be
    incorrectly split if $IFS did not contain a space character.

l.  Fixed a bug that could cause a core dump when completing hostnames if
    the number of matching hostnames was an exact multiple of 16.

m.  Fixed a bug that caused the shell to fork too early when a command
    such as `%2 &' was given.

2.  Changes to Readline

a.  Fixed a problem with redisplay that showed up when the prompt string was
    longer than the screen width and the prompt contained invisible characters.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.02-release,
and the previous version, bash-2.02-beta2.

1.  Changes to Bash

a.  A bug was fixed that caused the terminal process group to be set
    incorrectly when performing command substitution of builtins in a
    pipeline.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.02-beta2,
and the previous version, bash-2.02-beta1.

1.  Changes to Bash

a.  Attempting to `wait' for stopped jobs now generates a warning message.

b.  Pipelines which exit due to SIGPIPE in non-interactive shells are now
    not reported if the shell is compiled -DDONT_REPORT_SIGPIPE.

c.  Some changes were made to builtins/psize.sh and support/bashbug.sh to
    attempt to avoid some /tmp file races and surreptitious file
    substitutions.

d.  Fixed a bug that caused the shell not to compile if configured with
    dparen arithmetic but without aliases.

e.  Fixed a bug that caused the input stream to be switched when assigning
    empty arrays with `bash -c'.

f.  A bug was fixed in the readline expansion glue code that caused bash to
    dump core when expanding lines with an unclosed single quote.

g.  A fix was made to the `cd' builtin so that using a non-empty directory
    from $CDPATH results in an absolute pathname of the new current working
    directory to be displayed after the current directory is changed.

h.  Fixed a bug in the variable assignment code that caused the shell to
    dump core when referencing an unset variable with `set -u' enabled in
    an assignment statement preceding a command.

i.  Fixed a bug in the exit trap code that caused reserved words to not be
    recognized under certain circumstances.

j.  Fixed a bug in the parameter pattern substitution code so that quote
    removal is performed.

k.  The shell should now configure correctly on Apple Rhapsody systems.

l.  The `kill' builtin now prints a usage message if it is not passed any
    arguments.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.02-beta1,
and the previous version, bash-2.02-alpha1.

1.  Changes to Bash

a.  A few compilation bugs were fixed in the new extended globbing code.

b.  Executing arithmetic commands now sets the command name to `((' so
    error messages look right.

c.  Fixed some build problems with various configuration options.

d.  The `printf' builtin now aborts immediately if an illegal format
    character is encountered.

e.  The code that creates here-documents now behaves better if the file it's
    trying to create already exists for some reason.

f.  Fixed a problem with the extended globbing code that made patterns like
    `x+*' expand incorrectly.

g.  The prompt string expansion code no longer quotes tildes with backslashes.

h.  The bash getcwd() implementation in lib/sh/getcwd.c now behaves better in
    the presence of lstat(2) failures.

i.  Fixed a bug with strsub() that caused core dumps when executing `fc -s'.

j.  The mail checking code now ensures that it has a valid default mailpath.

k.  A bug was fixed that caused local variables to be unset inappropriately
    when sourcing a script from within another sourced script.

l.  A bug was fixed in the history saving code so that functions are saved
    in the history list correctly if `cmdhist' is enabled, but `lithist'
    is not.

m.  A bug was fixed that caused printf overflows when displaying error
    messages.

n.  It should be easier to build the loadble builtins in examples/loadables,
    though some manual editing of the generated Makefile is still required.

o.  The user's primary group is now always ${GROUPS[0]}.

p.  Some updates were made to support/config.guess from the GNU master copy.

q.  Some changes were made to the autoconf support for Solaris 2.6 large
    files.

r.  The `command' builtins now does the right thing when confstr(3) cannot
    find a value for _CS_PATH.

s.  Extended globbing expressions like `*.!(c)' are not history expanded if
    `extglob' is enabled.

t.  Using the `-P' option to `cd' will force the value that is assigned to
    PWD to not contain any symbolic links.

2.  Changes to Readline

a.  The code that prints completion listings now behaves better if one or
    more of the filenames contains non-printable characters.

b.  The time delay when showing matching parentheses is now 0.5 seconds.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.02-alpha1,
and the previous version, bash-2.01.1-release.

1.  Changes to Bash

a.  OS-specific configuration changes for:  BSD/OS 3.x, Minix 2.x,
    Solaris 2.6, SINIX SVR4.

b.  Changes were made to the generated `info' files so that `install-info'
    works correctly.

c.  PWD is now auto-exported.

d.  A fix was made to the pipeline code to make sure that the shell forks
    to execute simple commands consisting solely of assignment statements.

e.  Changes to the test suite for systems with 14-character filenames.

f.  The default sizes of some internal hash tables have been made smaller
    to reduce the shell's memory footprint.

g.  The `((...))' arithmetic command is now executed directly instead of
    being translated into `let "..."'.

h.  Fixes were made to the expansion code so that "$*", "$@", "${array[@]}",
    and "${array[@]}" expand correctly when IFS does not contain a space
    character, is unset, or is set to NULL.

i.  The indirect expansion code (${!var}) was changed so that the only
    valid values of `var' are variable names, positional parameters, `#',
    `@', and `*'.

j.  An arithmetic expression error in a $((...)) expansion now causes a
    non-interactive shell running in posix mode to exit.

k.  Compound array assignment now splits the words within the parentheses
    on shell metacharacters like the parser would before expansing them
    and performing the assignment.  This is for compatibility with ksh-93.

l.  The internal shell backslash-quoting code (used in the output of `set'
    and completion) now quotes tildes if they appear at the start of the
    string or after a `=' or `:'.

m.  A couple of bugs with `shopt -o' were fixed.

n.  `bash +o' now displays the same output as `set +o' before starting an
    interactive shell.

o.  A bug that caused command substitution and the `eval' builtin to
    occasionally free memory twice when an error was encountered was fixed.

p.  The filename globbing code no longer requires read permission for a
    directory when the filename to be matched does not contain any globbing
    characters, as POSIX.2 specifies.

q.  A bug was fixed so that the job containing the last asynchronous
    process is not removed from the job table until a `wait' is executed
    for that process or another asynchronous process is started.  This
    satisfies a POSIX.2 requirement.

r.  A `select' bug was fixed so that a non-numeric user response is treated
    the same as a numeric response that is out of range.

s.  The shell no longer parses the value of SHELLOPTS from the environment
    if it is restricted, running setuid, or running in `privileged mode'.

t.  Fixes were made to enable large file support on systems such as
    Solaris 2.6, where the size of a file may be larger than can be held
    in an `int'.

u.  The filename hashing code was fixed to not add `./' to the beginning of
    filenames which already begin with `./'.

v.  The configure script was changed so that the GNU termcap library is not
    compiled in if `prefer-curses' has been specified.

w.  HISTCONTROL and HISTIGNORE are no longer applied to the second and
    subsequent lines of a multi-line command.

x.  A fix was made to `disown' so that it does a better job of catching
    out-of-range jobs.

y.  Non-interactive shells no longer report the status of processes terminated
    due to SIGINT, even if the standard output is a terminal.

z.  A bug that caused the output of `jobs' to have extra carriage returns
    was fixed.

aa. A bug that caused PIPESTATUS to not be set when builtins or shell
    functions were executed in the foreground was fixed.

bb. Bash now attempts to detect when it is being run by sshd, and treats
    that case identically to being run by rshd.

cc. A bug that caused `set -a' to export SHELLOPTS when one of the shell
    options was changed was fixed.

dd. The `kill' builtin now disallows empty or missing process id arguments
    instead of treating them as identical to `0', which means the current
    process.

ee. `var=value declare -x var' now behaves identically to
    `var=value export var'.  Similarly for `var=value declare -r var' and
    `var=value readonly var'.

ff. A few memory leaks were fixed.

gg. `alias' and `unalias' now print error messages when passed an argument
    that is not an alias for printing or deletion, even when the shell is
    not interactive, as POSIX.2 specifies.

hh. `alias' and `alias -p' now return a status of 0 when no aliases are
    defined, as POSIX.2 specifies.

ii. `cd -' now prints the pathname of the new working directory if the shell
    is interactive.

jj. A fix was made so that the code that binds $PWD now copes with getcwd()
    returning NULL.

kk. `unset' now checks whether or not a function name it's trying to unset
    is a valid shell identifier only when the shell is running in posix mode.

ll. A change was made to the code that generates filenames for here documents
    to make them less prone to name collisions.

mm. The parser was changed so that `time' is recognized as a reserved word
    only at the beginning of a pipeline.

nn. The pathname canonicalization code was changed so that `//' is converted
    into `/', but all other pathnames beginning with `//' are left alone, as
    POSIX.2 specifies.

oo. The `logout' builtin will no longer exit a non-interactive non-login
    shell.

2.  Changes to Readline

a.  Fixed a problem in the readline test program rltest.c that caused a core
    dump.

b.  The code that handles parser directives in inputrc files now displays
    more error messages.

c.  The history expansion code was fixed so that the appearance of the
    history comment character at the beginning of a word inhibits history
    expansion for that word and the rest of the input line.

3.  New Features in Bash

a.  A new version of malloc, based on the older GNU malloc, that has many
    changes, is more page-based, is more conservative with memory usage,
    and does not `orphan' large blocks when they are freed.

b.  A new version of gmalloc, based on the old GLIBC malloc, with many
    changes and range checking included by default.

c.  A new implementation of fnmatch(3) that includes full POSIX.2 Basic
    Regular Expression matching, including character classes, collating
    symbols, equivalence classes, and support for case-insensitive pattern
    matching.

d.  ksh-88 egrep-style extended pattern matching ([@+*?!](patlist)) has been
    implemented, controlled by a new `shopt' option, `extglob'.

e.  There is a new ksh-like `[[' compound command, which implements
    extended `test' functionality.

f.  There is a new `printf' builtin, implemented according to the POSIX.2
    specification.

g.  There is a new feature for command substitution: $(< filename) now expands
    to the contents of `filename', with any trailing newlines removed
    (equivalent to $(cat filename)).

h.  There are new tilde prefixes which expand to directories from the
    directory stack.

i.  There is a new `**' arithmetic operator to do exponentiation.

j.  There are new configuration options to control how bash is linked:
    `--enable-profiling', to allow bash to be profiled with gprof, and
    `--enable-static-link', to allow bash to be linked statically.

k.  There is a new configuration option, `--enable-cond-command', which
    controls whether or not the `[[' command is included.  It is on by
    default.

l.  There is a new configuration option, `--enable-extended-glob', which
    controls whether or not the ksh extended globbing feature is included.
    It is enabled by default.

m.  There is a new configuration #define in config.h.top that, when enabled,
    will cause all login shells to source /etc/profile and one of the user-
    specific login shell startup files, whether or not the shell is
    interactive.

n.  There is a new invocation option, `--dump-po-strings', to dump
    a shell script's translatable strings ($"...") in GNU `po' format.

o.  There is a new `shopt' option, `nocaseglob', to enable case-insensitive
    pattern matching when globbing filenames and using the `case' construct.

p.  There is a new `shopt' option, `huponexit', which, when enabled, causes
    the shell to send SIGHUP to all jobs when an interactive login shell
    exits.

q.  `bind' has a new `-u' option, which takes a readline function name as an
    argument and unbinds all key sequences bound to that function in a
    specified keymap.

r.  `disown' now has `-a' and `-r' options, to limit operation to all jobs
    and running jobs, respectively.

s.  The `shopt' `-p' option now causes output to be displayed in a reusable
    format.

t.  `test' has a new `-N' option, which returns true if the filename argument
    has been modified since it was last accessed.

u.  `umask' now has a `-p' option to print output in a reusable format.

v.  A new escape sequence, `\xNNN', has been added to the `echo -e' and $'...'
    translation code.  It expands to the character whose ascii code is NNN
    in hexadecimal.

w.  The prompt string expansion code has a new `\r' escape sequence.

x.  The shell may now be cross-compiled for the CYGWIN32 environment on
    a Unix machine.

4.  New Features in Readline

a.  There is now an option for `iterative' yank-last-arg handline, so a user
    can keep entering `M-.', yanking the last argument of successive history
    lines.

b.  New variable, `print-completions-horizontally', which causes completion
    matches to be displayed across the screen (like `ls -x') rather than up
    and down the screen (like `ls').

c.  New variable, `completion-ignore-case', which causes filename completion
    and matching to be performed case-insensitively.

d.  There is a new bindable command, `magic-space', which causes history
    expansion to be performed on the current readline buffer and a space to
    be inserted into the result.

e.  There is a new bindable command, `menu-complete', which enables tcsh-like
    menu completion (successive executions of menu-complete insert a single
    completion match, cycling through the list of possible completions).

f.  There is a new bindable command, `paste-from-clipboard', for use on Win32
    systems, to insert the text from the Win32 clipboard into the editing
    buffer.

g.  The key sequence translation code now understands printf-style backslash
    escape sequences, including \NNN octal escapes.  These escape sequences
    may be used in key sequence definitions or macro values.

h.  An `$include' inputrc file parser directive has been added.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.01.1-release,
and the previous version, bash-2.01-release.

1.  Changes to Bash

a.  The select command was fixed to check the validity of the user's
    input more strenuously.

b.  A bug was fixed that prevented `time' from timing commands correctly
    when supplied as an argument to `bash -c'.

c.  A fix was made to the mail checking code to keep from adding the same
    mail file to the list of files to check multiple times when parsing
    $MAILPATH.

d.  Fixed an off-by-one error in the tilde expansion library.

e.  When using the compound array assignment syntax, the old value of
    the array is cleared before assigning the new value.

f.  Fixed a bug that could cause a core dump when a trap handler was reset
    to the default in the trap command associated with that signal.

g.  Fixed a bug in the locale code that occurred when assigning a value
    to LC_ALL.

h.  A change was made to the parser so that words of the form xxx=(...)
    are not considered compound assignment statements unless there are
    characters before the `='.

i.  A fix was made to the command tracing code to correctly quote each
    word of output.

j.  Some changes were made to the bash-specific autoconf tests to make them
    more portable.

k.  Completion of words with globbing characters now correctly quotes the
    result.

l.  The directory /var/spool/mail is now preferred to /usr/spool/mail when
    configure is deciding on the default mail directory.

m.  The brace completion code was fixed to not quote the `{' and `}'.

n.  Some fixes were made to make $RANDOM more random in subshells.

o.  System-specific changes were made to configure for: SVR4.2

p.  Changes were made so that completion of words containing globbing chars
    substitutes the result only if a single filename was matched.

q.  The window size is now recomputed after a job is stopped with SIGTSTP if
    the user has set `checkwinsize' with `shopt'.

r.  When doing substring expansion, out-of-range substring specifiers now
    cause nothing to be substituted rather than an expansion error.

s.  A fix was made so that you can no longer trap `SIGEXIT' or `SIGDEBUG' --
    only `EXIT' and `DEBUG' are accepted.

t.  The display of trapped signals now uses the signal number if signals
    for which bash does not know the name are trapped.

u.  A fix was made so that `bash -r' does not turn on restricted mode until
    after the startup files are executed.

v.  A bug was fixed that occasionally caused a core dump when a variable
    found in the temporary environment of export/declare/readonly had a
    null value.

w.  A bug that occasionally caused unallocated memory to be passed to free()
    when doing arithmetic substitution was fixed.

x.  A bug that caused a buffer overrun when expanding a prompt string
    containing `\w' and ${#PWD} exceeded PATH_MAX was fixed.

y.  A problem with the completion code that occasionally caused it to
    refer to a character before the beginning of the readline line buffer
    was fixed.

z.  A bug was fixed so that the `read' builtin restarts reads when
    interrupted by signals other than SIGINT.

aa. Fixed a bug that caused a command to be freed twice when there was
    an evaluation error in the `eval' command.

2.  Changes to Readline

a.  Added a missing `extern' to a declaration in readline.h that kept
    readline from compiling cleanly on some systems.

b.  The history file is now opened with mode 0600 when it is written for
    better security.

c.  Changes were made to the SIGWINCH handling code so that prompt redisplay
    is done better.

d.  ^G now interrupts incremental searches correctly.

e.  A bug that caused a core dump when the set of characters to be quoted
    when completing words was empty was fixed.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.01-release,
and the previous version, bash-2.01-beta2.

1.  Changes to Bash

a.  The `distclean' target should remove the `printenv' executable if it
    has been created.

b.  The test suite was changed slightly to ensure that the error messages
    are printed in English.

c.  A bug that caused the shell to dump core when a filename containing a
    `/' was passed to `hash' was fixed.

d.  Pathname canonicalization now leaves a leading `//' intact, as POSIX.1
    requires.

e.  A memory leak when completing commands was fixed.

f.  A memory leak that occurred when checking the hash table for commands
    with relative paths was fixed.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.01-beta2,
and the previous version, bash-2.01-beta1.

1.  Changes to Bash

a.  The `ulimit' builtin translates RLIM_INFINITY to the hard limit only if
    the current (soft) limit is less than or equal to the hard limit.

b.  Fixed a bug that caused the bash emulation of strcasecmp to produce
    incorrect results.

c.  A bug that caused memory to be freed twice when a trap handler resets
    the trap more than once was fixed.

d.  A bug that caused machines where sizeof (pointer) > sizeof (int) to
    fail (and possibly dump core) when trying to unwind-protect a null
    pointer was fixed.

e.  The startup files should not be run with job control enabled.  This fix
    allows SIGINT to once again interrupt startup file execution.

f.  Bash should not change the SIGPROF handler if it is set to something
    other than SIG_DFL.

g.  The completion code that provides bash-specific completions for readline
    now quotes characters that the readline code would treat as word break
    characters if they appear in a file name.

h.  The completion code now correctly quotes filenames containing a `!',
    even if the user attempted to use double quotes when attempting
    completion.

i.  A bug that caused the shell to dump core when `disown' was called without
    arguments and there was no current job was fixed.

j.  A construct like $((foo);bar) is now processed as a command substitution
    rather than as a bad arithmetic substitution.

k.  A couple of bugs that caused `fc' to not obey the `cmdhist' and `lithist'
    shell options when editing and re-executing a series of commands were
    fixed.

l.  A fix was made to the grammar -- the list of commands between `do' and
    `done' in the body of a `for' command should be treated the same as a
    while loop.

2.  Changes to Readline

a.  A couple of bugs that caused the history search functions to attempt to
    free a NULL pointer were fixed.

b.  If the C library provides setlocale(3), readline does not need to look
    at various environment variables to decide whether or not to go into
    eight-bit mode automatically -- just check whether the current locale
    is not `C' or `POSIX'.

c.  If the filename completion function finds that a directory was not closed
    by a previous (interrupted) completion, it closes the directory with
    closedir().

3.  New Features in Bash

a.  New bindable readline commands:  history-and-alias-expand-line and
    alias-expand-line.  The code was always in there, there was just no
    way to execute it.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.01-beta1,
and the previous version, bash-2.01-alpha1.

1.  Changes to Bash

a.  Fixed a problem that could cause file descriptors used for process
    substitution to conflict with those used explicitly in redirections.

b.  Made it easier to regenerate configure if the user changes configure.in.

c.  ${GROUPS[0]} should always be the primary group, even on systems without
    multiple groups.

d.  Spelling correction is no longer enabled by default.

e.  Fixes to quoting problems in `bashbug'.

f.  OS-specific configuration changes were made for: Irix 6.

g.  OS-specific code changes were made for: QNX.

h.  A more meaningful message is now printed when the file in /tmp for a
    here document cannot be created.

i.  Many changes to the shell's variable initialization code to speed
    non-interactive startup.

j.  Changes to the non-job-control code so that it does not try to open
    /dev/tty.

k.  The output of `set' and `export' is once again sorted, as POSIX wants.

l.  Fixed a problem caused by a recursive call reparsing the value of
    $SHELLOPTS.

m.  The tilde code no longer calls getenv() when it's compiled as part of
    the shell, which should eliminate problems on systems that cannot
    redefine getenv(), like the NeXT OS.

n.  Fixed a problem that caused `bash -o' or `bash +o' to not list all
    the shell options.

o.  Fixed `ulimit' to convert RLIM_INFINITY to the appropriate hard limit
    only if the hard limit is greater than the current (soft) limit.

p.  Fixed a problem that arose when building bash in a different directory
    than the source and y.tab.[ch] were remade with something other than
    bison.  This came up most often on NetBSD.

q.  Fixed a problem with completion -- it thought that `pwd`/[TAB] indicated
    an unfinished command completion (`/), which generated errors.

r.  The bash special tilde expansions (~-, ~+) are now attempted before
    calling the standard tilde expansion code, which should eliminate the
    problems people have been seeing with this on Solaris 2.5.1.

s.  Added support for <stdarg.h> to places where it was missing.

t.  Changed the code that reads the output of a command substitution to not
    go through stdio.  This reduces the memory requirements and is faster.

u.  A number of changes to speed up export environment creation were made.

v.  A number of memory leaks were fixed as the result of running the test
    scripts through Purify.

w.  Fixed a bug that caused subshells forked to interpret executable
    scripts without a leading `#!' to not reinitialize the values of
    the shell options.

2.  Changes to Readline

a.  History library has less `#ifdef SHELL' code -- abstracted stuff out
    into application-specific function hooks.

b.  Readline no longer calls getenv() if it's compiled as part of the shell,
    which should eliminate problems on systems that cannot redefine getenv(),
    like the NeXT OS.

c.  Fixed translation of ESC when `untranslating' macro values.

d.  The region kill operation now fixes the mark if it ends up beyond the
    boundaries of the line after the region is deleted.

3.  New Features in Bash

a.  New argument for `configure':  `--with-curses'.  This can be used to
    override the selection of the termcap library on systems where it is
    deficient.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.01-alpha1,
and the previous version, bash-2.0-release.

1.  Changes to Bash

a.  System-specific configuration changes for: FreeBSD, SunOS4, Irix,
    MachTen, QNX 4.2, Harris Night Hawk, SunOS5.

b.  System-specific code changes were made for: Linux, 4.4 BSD, QNX 4.2,
    HP-UX, AIX 4.2.

c.  A bug that caused the exec builtin to fail because the full pathname of
    the command could not be found was fixed.

d.  The code that performs output redirections is now more resistant to
    race conditions and possible security exploits.

e.  A bug that caused the shell to dump core when performing pattern
    substitutions on variable values was fixed.

f.  More hosts are now recognized by the auto-configuration mechanism
    (OpenBSD, QNX, others).

g.  Assignments to read-only variables that attempt to convert them to
    arrays are now errors.

h.  A bug that caused shell scripts using array assignments in POSIX mode
    to exit after the assignment was performed was fixed.

i.  The substring expansion code is now more careful about running off the
    ends of the expanded variable value.

j.  A bug that caused completion to fail if a backquoted command substitution
    appeared anywhere on the line was fixed.

k.  The `source' builtin no longer turns off history if it has been enabled
    in a non-interactive shell.

l.  A bug that caused the shell to crash when `disown' was given a pid
    instead of a job number was fixed.

m.  The `cd' spelling correction code will not try to change to `.' if no
    directory entries match a single-character argument.

n.  A bad variable name supplied to `declare', `export', or `readonly' no
    longer causes a non-interactive shell in POSIX mode to exit.

o.  Some fixes were made to the test suite to handle peculiarities of
    various Unix versions.

p.  The bash completion code now quotes characters that readline would
    treat as word breaks for completion but are not shell metacharacters.

q.  Bad options supplied at invocation now cause a usage message to be
    displayed.

r.  Fixes were made to the code that handles DEBUG traps so that the trap
    string is not freed inappropriately.

s.  Some changes were made to the bash debugger in examples/bashdb -- it
    should be closer to working now.

t.  A problem that caused the default filename used for mail checking to be
    wrong was fixed.

u.  A fix was made to the `echo' builtin so that NUL characters printed with
    `echo -e' do not cause the output to be truncated.

v.  A fix was made to the job control code so that the shell behaves better
    when monitor mode is enabled in a non-interactive shell.

w.  Bash no longer catches all of the terminating signals in a non-
    interactive shell until a trap is set on EXIT, which should result in
    quicker startup.

x.  A fix was made to the command timing code so that `time' can be used in
    a loop.

y.  A fix was made to the parser so that `((cmd); cmd2)' is now parsed as
    a nested subshell rather than strictly as an (erroneous) arithmetic
    command.

z.  A fix was made to the globbing code so that it correctly matches quoted
    filenames beginning with a `.'.

aa. A bug in `fc' that caused some multi-line commands to not be stored as
    one command in the history when they were re-executed after editing
    (with `fc -e') was fixed.

bb. The `ulimit' builtin now attempts to catch some classes of integer
    overflows.

cc. The command-oriented-history code no longer attempts to add `;'
    inappropriately when a newline appears while reading a $(...) command
    substitution.

dd. A bug that caused the shell to dump core when `help --' was executed
    was fixed.

ee. A bug that caused the shell to crash when an unset variable appeared
    in the body of a here document after `set -u' had been executed was
    fixed.

ff. Implicit input redirections from /dev/null for asynchronous commands
    are now handled better.

gg. A bug that caused the shell to fail to compile when configured with
    `--disable-readline' was fixed.

hh. The globbing code should now be interruptible.

ii. Bash now notices when the `kill' builtin is used to send SIGCONT to a
    stopped job and adjusts the data structures accordingly, as if `bg' had
    been executed instead.

jj. A bug that caused the shell to crash when mixing calls to `getopts'
    and `shift' on the same set of positional parameters was fixed.

kk. The command printing code now preserves the `-p' flag to `time'.

ll. The command printing code now handles here documents better when there
    are other redirections associated with the command.

mm. The special glibc environment variable (NNN_GNU_nonoption_argv_flags_)
    is no longer placed into the environment of executed commands -- users
    of glibc had too many problems with it.

nn. Reorganized the code that generates signames.h.  The signal_names list
    is now more complete but may be slightly different (SIGABRT is favored
    over SIGIOT, for example).  The preferred signal names are those
    listed in the POSIX.2 standard.

oo. `bashbug' now uses a filename shorter than 14 characters for its
    temporary file, and asks for confirmation before sending the bug
    report.

pp. A bug that caused TAB completion in vi editing mode to not be turned
    off when `set -o posix' was executed or back on when `set +o posix'
    was executed was fixed.

qq. A bug in the brace expansion code that caused brace expansions appearing
    in new-style $(...) command substitutions to be inappropriately expanded
    was fixed.

rr. A bug in the readline hook shell-expand-line that could cause memory to
    be inappropriately freed was fixed.

ss. A bug that caused some arithmetic expressions containing `&&' and `||'
    to be parsed with the wrong precedence has been fixed.

tt. References to unbound variables after `set -u' has been executed now
    cause the shell to exit immediately, as they should.

uu. A bug that caused the shell to exit inappropriately when `set -e' had
    been executed and a command's return status was being inverted with the
    `!' reserved word was fixed.

vv. A bug that could occasionally cause the shell to crash with a
    divide-by-zero error when timing a command was fixed.

ww. A bug that caused parameter pattern substitution to leave stray
    backslashes in the replacement string when the expression is in
    double quotes was fixed.

xx. The `break' and `continue' builtins now break out of all loops when an
    invalid count argument is supplied.

yy. Fixed a bug that caused PATH to be set to the empty string if
    `command -p' is executed with PATH unset.

zz. Fixed `kill -l signum' to print the signal name without the `SIG' prefix,
    as POSIX specifies.

aaa. Fixed a bug that caused the shell to crash while setting $SHELLOPTS
     if there were no shell options set.

bbb. Fixed `export -p' and `readonly -p' so that when the shell is in POSIX
     mode, their output is as POSIX.2 specifies.

ccc. Fixed a bug in `readonly' so that `readonly -a avar=(...)' actually
     creates an array variable.

ddd. Fixed a bug that prevented `time' from correctly timing background
     pipelines.

2.  Changes to Readline

a.  A bug that caused an extra newline to be printed when the cursor was on
    an otherwise empty line was fixed.

b.  An instance of memory being used after it was freed was corrected.

c.  The redisplay code now works when the prompt is longer than the screen
    width.

d.  `dump-macros' is now a bindable name, as it should have been all along.

e.  Non-printable characters are now expanded when displaying macros and
    their values.

f.  The `dump-variables' and `dump-macros' commands now output a leading
    newline if they're called as the result of a key sequence, rather
    than directly by an application.

3.  New Features in Bash

a.  There is a new builtin array variable: GROUPS, the set of groups to which
    the user belongs.  This is used by the test suite.

4.  New Features in Readline

a.  If a key sequence bound to `universal-argument' is read while reading a
    numeric argument started with `universal-argument', it terminates the
    argument but is otherwise ignored.  This provides a way to insert multiple
    instances of a digit string, and is how GNU emacs does it.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.0-release,
and the previous version, bash-2.0-beta3.

1.  Changes to Bash

a.  Fix to the `getopts' builtin so that it does the right thing when a
    required option argument is not present.

b.  The completion code now updates the common prefix of matched names
    after FIGNORE processing is done, since any names that were removed
    may have changed the common prefix.

c.  Fixed a bug that made messages in MAILPATH entries not work correctly.

d.  Fixed a serious documentation error in the description of the new
    ${parameter:offset[:length]} expansion.

e.  Fixes to make parameter substring expansion ({$param:offset[:length]})
    work when within double quotes.

f.  Fixes to make ^A (CTLESC) survive an unquoted expansion of positional
    parameters.

g.  Corrected a misspelling of `unlimited' in the output of `ulimit'.

h.  Fixed a bug that caused executable scripts without a leading `#!' to
    occasionally pick up the wrong set of positional parameters.

i.  Linux systems now have a working `ulimit -v', using RLIMIT_AS.

j.  Updated config.guess so that many more machine types are recognized.

k.  Fixed a bug with backslash-quoted slashes in the ${param/pat[/sub]}
    expansion.

l.  If the shell is named `-su', and `-c command' is supplied, read and
    execute the login shell startup files even though the shell is not
    interactive.  This is to support the `-' option to `su'.

m.  Fixed a bug that caused core dumps when the DEBUG trap was ignored
    with `trap "" DEBUG' and a shell function was subsequently executed.

n.  Fixed a bug that caused core dumps in the read builtin when IFS was
    set to the null string and the input had leading whitespace.

2.  Changes to Readline

a.  Fixed a bug that caused a numeric argument of 1024 to be ignored when
    inserting text.

b.  Fixed the display code so that the numeric argument is displayed as it's
    being entered.

c.  Fixed the numeric argument reading code so that `M-- command' is
    equivalent to `M--1 command', as the prompt implies.

3.  New Features in Bash

a.  `ulimit' now sets both hard and soft limits and reports the soft limit
    by default (when neither -H nor -S is specified).  This is compatible
    with versions of sh and ksh that implement `ulimit'.

b.  Integer constants have been extended to base 64.

4.  New Features in Readline

a.  The `home' and `end' keys are now bound to beginning-of-line and
    end-of-line, respectively, if the corresponding termcap capabilities
    are present.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.0-beta3,
and the previous version, bash-2.0-beta2.

1.  Changes to Bash

a.  System-specific changes for: AIX 4.2, SCO 3.2v[45], HP-UX.

b.  When in POSIX mode, variable assignments preceding a special builtin
    persist in the shell environment after the builtin completes.

c.  Changed all calls to getwd() to getcwd().  Improved check for systems
    where the libc getcwd() calls popen(), since that breaks on some
    systems when job control is being used.

d.  Fixed a bug that caused seg faults when executing scripts with the
    execute bit set but without a leading `#!'.

e.  The environment passed to executed commands is never sorted.

f.  A bug was fixed in the code that expands ${name[@]} to the number of
    elements in an array variable.

g.  A bug was fixed in the array compound assignment code ( A=( ... ) ).

h.  Window size changes now correctly propagate down to readline if
    the shopt `checkwinsize' option is enabled.

i.  A fix was made in the code that expands to the length of a variable
    value (${#var}).

j.  A fix was made to the command builtin so that it did not turn on the
    `no fork' flag inappropriately.

k.  A fix was made to make `set -n' work more reliably.

l.  A fix was made to the job control initialization code so that the
    terminal process group is set to the shell's process group if the
    shell changes its own process group.

2.  Changes to Readline

a.  System-specific changes for: SCO 3.2v[45].

b.  The behavior of the vi-mode `.' when redoing an `i' command was changed
    to insert the text previously inserted by the `i' command rather than
    simply entering insert mode.

3.  New features in Bash

a.  There is a new version of the autoload function package, in
    examples/functions/autoload.v2, that uses arrays and provides more
    functionality.

b.  Support for LC_COLLATE and locale-specific sorting of the results of
    pathname expansion if strcoll() is available.

4.  New Features in Readline

a.  Support for locale-specific sorting of completion possibilities if
    strcoll() is available.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.0-beta2,
and the previous version, bash-2.0-beta1.

1.  Changes to Bash

a.  `pushd -' is once again equivalent to `pushd $OLDPWD'.

b.  OS-specific changes for: SCO 3.2v[45].

c.  A change was made to the fix for the recently-reported security hole
    when reading characters with octal value 255 to make it work better on
    systems with restartable system calls when not using readline.

d.  Some changes were made to the test suite so that it works if you
    configure bash with --enable-usg-echo-default.

e.  A fix was made to the parsing of conditional arithmetic expressions.

f.  Illegal arithmetic bases now cause an arithmetic evaluation error rather
    than being silently reset.

g.  Multiple arithmetic bases now cause an arithmetic evaluation error
    instead of being ignored.

h.  A fix was made to the evaluation of ${param?word} to conform to POSIX.2.

i.  A bug that sometimes caused array indices to be evaluated twice (which
    would cause errors when they contained assignment statements) was fixed.

j.  `ulimit' was rewritten to avoid problems with getrlimit(2) returning
    unsigned values and to simplify the code.

k.  A bug in the command-oriented-history code that caused it to sometimes
    put semicolons after right parens inappropriately was fixed.

l.  The values inserted into the prompt by the \w and \W escape sequences
    are now quoted to prevent further expansion.

m.  An interactive shell invoked as `sh' now reads and executes commands
    from the file named by $ENV when it starts up.  If it's a login shell,
    it does this after reading /etc/profile and ~/.profile.

n.  The file named by $ENV is never read by non-interactive shells.

2.  Changes to Readline

a.  A few changes were made to hide some macros and functions that should not
    be public.

b.  An off-by-one error that caused seg faults in the history expansion code
    was fixed.

3.  New Features in Bash

a.  The ksh-style ((...)) arithmetic command was implemented.  It is exactly
    identical to let "...".  This is controlled by a new option to configure,
    `--enable-dparen-arithmetic', which is on by default.

b.  There is a new #define available in config.h.top: SYS_BASH_LOGOUT.  If
    defined to a filename, bash reads and executes commands from that file
    when a login shell exits.  It's commented out by default.

c.  `ulimit' has a `-l' option that reports the maximum amount of data that
    may be locked into memory on 4.4BSD-based systems.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.0-beta1,
and the previous version, bash-2.0-alpha4.

1.  Changes to Bash

a.  A bug that sometimes caused traps to be ignored on signals the
    shell treats specially was fixed.

b.  The internationalization code was changed to track the values of
    LC_* variables and call setlocale() as appropriate.  The TEXTDOMAIN
    and TEXTDOMAINDIR variables are also tracked; changes cause calls
    to textdomain() and bindtextdomain(), if available.

c.  A bug was fixed that sometimes caused double-quoted strings to be
    parsed incorrectly.

d.  Changes were made so that the siglist code compiles correctly on
    Solaris 2.5.

e.  Added `:' to the set of characters that cause word breaks for the
    completion code so that pathnames in assignments to $PATH can be
    completed.

f.  The `select' command was fixed to print $PS3 to stderr.

g.  Fixed an error in the manual page section describing the effect that
    setting and unsetting GLOBIGNORE has on the setting of the `dotglob'
    option.

h.  The time conversion code now uses CLK_TCK rather than CLOCKS_PER_SEC
    on systems without gettimeofday() and resources.

i.  The getopt static variables are now initialized each time a subshell
    is started, so subshells using `getopts' work right.

j.  A sign-extension bug that caused a possible security hole was fixed.

k.  The parser now reads characters between backquotes within a double-
    quoted string as a single word, so double quotes in the backquoted
    string don't terminate the enclosing double-quoted string.

l.  A bug that caused `^O' to work incorrectly when typed as the first
    thing to an interactive shell was fixed.

m.  A rarely-exercised off-by-one error in the code that quotes variable
    values was fixed.

n.  Some memory and file descriptor leaks encountered when running a
    shell script that is executable but does not have a leading `#!'
    were plugged.

2.  Changes to Readline

a.  A bug that sometimes caused incorrect results when trying to read
    typeahead on systems without FIONREAD was fixed.

3.  New Features in Bash

a.  The command timing code now uses the value of the TIMEFORMAT variable
    to format and display timing statistics.

b.  The `time' reserved word now accepts a `-p' option to force the
    POSIX.2 output format.

c.  There are a couple of new and updated scripts to convert csh startup
    files to bash format.

d.  There is a new builtin array variable: BASH_VERSINFO.  The various
    members hold the parts of the version information in BASH_VERSION,
    plus the value of MACHTYPE.

4.  New Features in Readline

a.  Setting LANG to `en_US.ISO8859-1' now causes readline to enter
    eight-bit mode.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.0-alpha4,
and the previous version, bash-2.0-alpha3.

1.  Changes to Bash

a.  There is better detection of rsh connections on Solaris 2.

b.  Assignments to read-only variables preceding a command name are now
    variable assignment errors.  Variable assignment errors cause
    non-interactive shells running in posix mode to exit.

c.  The word tokenizer was rewritten to handle nested quotes and pairs
    ('', "", ``, ${...}, $(...), $[...], $'...', $"...", <(...), >(...))
    correctly.  Some of the parameter expansion code was updated as a
    consequence.

d.  A fix was made to `test' when given three arguments so that a binary
    operator is checked for first, before checking that the first argument
    is `!'.

e.  2''>/dev/null is no longer equivalent to 2>/dev/null.

f.  Parser error messages were regularized, and in most cases the name of
    the shell script being read by a non-interactive shell is not printed
    twice.

g.  A fix was made to the completion code so that it no longer removes the
    text the user typed in some cases.

h.  The special glibc `getopt' environment variable is no longer put into
    the environment on machines with small values of ARG_MAX.

i.  The expansion of ${...} now follows the POSIX.2 rules for finding the
    closing `}'.

j.  The shell no longer displays spurious status messages for background
    jobs in shell scripts that complete successfully when the script is
    run from a terminal.

k.  `shopt -o' now correctly updates $SHELLOPTS.

l.  A bug that caused the $PATH searching code to return a non-executable
    file even when an executable file with the same name appeared later in
    $PATH was fixed.

m.  The shell now does tilde expansions on unquoted `:~' in assignment
    statements when not in posix mode.

n.  Variable assignment errors when a command consists only of assignments
    now cause non-interactive shells to exit when in posix mode.

o.  If the variable in a `for' or `select' command is read-only, or not a
    legal shell identifier, a variable assignment error occurs.

p.  `test' now handles `-a' and `-o' as binary operators when three arguments
    are supplied, and correctly parses `( word )' as equivalent to `word'.

q.  `test' was fixed so that file names of the form /dev/fd/NN mean the same
    thing on all systems, even Linux.

r.  Fixed a bug in the globbing code that caused patterns with multiple
    consecutive `*'s to not be matched correctly.

s.  Fixed a bug that caused $PS2 to not be printed when an interactive shell
    not using readline is reading a here document.

t.  Fixed a bug that caused history expansion to be performed inappropriately
    when a single-quoted string spanned more than one line.

u.  `getopts' now checks that the variable name passed by the user as the
    second argument is a legal shell identifier and that the variable is
    not read-only.

v.  Fixed `getopts' to obey POSIX.2 rules for setting $OPTIND when it
    encounters an error.

w.  Fixed `set' to display variable values in a form that can be re-read.

x.  Fixed a bug in the code that keeps track of whether or not local variables
    have been declared at the current level of function nesting.

y.  Non-interactive shells in posix mode now exit if the name in a function
    declaration is not a legal identifier.

z.  The job control code now ignores stopped children when the shell is not
    interactive.

aa. The `cd' builtin no longer attempts spelling correction on the directory
    name if the shell is not interactive, regardless of the setting of the
    `cdspell' option.

bb. Some OS-specific changes were made for SCO 3.2v[45] and AIX 4.2.

cc. `time' now prints its output to stderr, as POSIX.2 specifies.

2.  Fixes to Readline

a.  After printing possible completions, all lines of a multi-line prompt
    are redisplayed.

b.  Some changes were made to the terminal handling code in rltty.c to
    work around AIX 4.2 bugs.

3.  New Features in Bash

a.  There is a new loadable builtin: sprintf, with calling syntax
		sprintf var format [args]
    This provides an easy way to simulate ksh left- and right-justified
    variable values.

b.  The expansions of \h and \H in prompt strings were swapped.  \h now
    expands to the hostname up to the first `.', as in bash-1.14.

4.  New Features in Readline

a.  The bash-1.14 behavior when ^M is typed while doing an incremental
    search was restored.  ^J may now be used to terminate the search without
    accepting the line.

b.  There is a new bindable variable: disable-completion.  This inhibits
    word completion and causes the completion character to be inserted as
    if it had been bound to self-insert.

------------------------------------------------------------------------------
This document details the changes between this version, bash-2.0-alpha3,
and the previous version, bash-2.0-alpha2.

There is now a file `COMPAT' included in the distribution that lists the
user-visible incompatibilities between 1.14 and 2.0.

1. Changes to Bash

a. Some work was done so that word splitting of the rhs of assignment
   statements conforms more closely to historical practice.

b. A couple of errant memory frees were fixed.

c. A fix was made to the test builtin so it recognizes `<' and `>' as
   binary operators.

d. The GNU malloc in lib/malloc/malloc.c now scrambles memory as it's
   allocated and freed.  This is to catch callers that refer to freed
   memory or assume something about newly-allocated memory.

e. Fixed a problem with conversion to 12-hour time in the prompt
   expansion code.

f. Fixed a problem with configure's argument parsing order.  Now you can
   correctly turn on specific options after using --enable-minimal-config.

g. The configure script now automatically disables the use of GNU malloc
   on systems where it's appropriate (better than having people read the
   NOTES file and do it manually).

h. There are new prompt expansions (\v and \V) to insert version information
   into the prompt strings.

i. The default prompt string now includes the version number.

j. Most of the builtins that take no options were changed to use the
   internal getopt so they can produce proper error messages for -?
   and incorrect options.

k. Some system-specific changes were made for SVR4.2 and Solaris 2.5.

l. Bash now uses PATH_MAX instead of MAXPATHLEN and NAME_MAX instead of
   MAXNAMLEN.

m. A couple of problems caused by uninitialized variables were fixed.

n. There are a number of new loadable builtin examples: logname, basename,
   dirname, tty, pathchk, tee, head, and rmdir.  All of these conform to
   POSIX.2.

o. Bash now notices changes in TZ and calls tzset() if present, so
   changing TZ will alter the time printed by prompt expansions.

p. The source was reorganized a bit so I don't have to wait so long for
   some files to compile, and to facilitate the creation of a `shell
   library' at some future point.

q. Bash no longer turns off job control if called as `sh', since the
   POSIX.2 spec includes job control as a standard feature.

r. `bash -o posix' now works as intended.

s. Fixed a problem with the completion code: when completing a filename
   that contained globbing characters, if show-all-if-ambiguous was set,
   the completion code would remove the user's text.

t. Fixed ulimit so that (hopefully) the full range of limits is available
   on HPUX systems.

u. A new `shopt' option (`hostcomplete') enables and disables hostname
   completion.

v. The shell no longer attempts to save the history on an abort(),
   which is usually called by programming_error().

w. The `-s' option to `fc' was changed to echo the command to be executed
   to stderr instead of stdout.

x. If the editor invoked by `fc -e' exits with a non-zero status, no
   commands are executed.

y. Fixed a bug that made the shopt `histverify' option work incorrectly.

z. There is a new variable `MACHTYPE' whose value is the GNU-style
   `cpu-company-system' system description as set by configure.  (The
   values of MACHTYPE and HOSTTYPE should really be swapped.)

aa. The `ulimit' builtin now allows the maximum virtual memory size to be
    set via setrlimit(2) if RLIMIT_VMEM is defined.

bb. `bash -nc 'command'' no longer runs `command'.

2. Changes to Readline

a. Fixed a typo in the code that checked for FIONREAD in input.c.

b. Fixed a bug in the code that outputs keybindings, so things like C-\
   are quoted properly.

c. Fixed a bug in the inputrc file parsing code to handle the problems
   caused by inputrc files created from the output of `bind -p' in
   previous versions of bash.  The problem was due to the bug fixed
   in item b above.

d. Readline no longer turns off the terminal's meta key, and turns it on
   once the first time it's called.

------------------------------------------------------------------------------
This file documents the changes between this version, bash-2.0-alpha2,
and the previous version, bash-2.0-alpha.

1. Changes to Bash

a. The shell no longer thinks directories are executable.

b. `disown' has a new option, `h', which inhibits the resending of SIGHUP
   but does not remove the job from the jobs table.

c. The varargs functions in error.c now use ANSI-C `stdarg' if available.

d. The build process now treats the `build version' in .build as local to
   the build directory, so different versions built from the same source
   tree have different `build versions'.

e. Some problems with the grammar have been fixed. (It used `list' in a few
   productions where `compound_list' was needed.  A `list' must be terminated
   with a newline or semicolon; a `compound_list' need not be.)

f. A fix was made to keep `wait' from hanging when waiting for all background
   jobs.

g. `bash --help' now writes its output to stdout, like the GNU Coding Standards
   specify, and includes the machine type (the value of MACHTYPE).

h. `bash --version' now prints more information and exits successfully, like
   the GNU Coding Standards specify.

i. The output of `time' and `times' now prints fractional seconds with three
   places after the decimal point.

j. A bug that caused process substitutions to screw up the pipeline printed
   by `jobs' was fixed.

k. Fixes were made to the code that implements $'...' and $"..." so they
   work as documented.

l. The process substitution code now opens named pipes for reading with
   O_NONBLOCK to avoid hanging.

m. Fixes were made to the trap code so the shell cleans up correctly if the
   trap command contains a `return' and we're executing a function or
   sourcing a script with `.'.

n. Fixes to doc/Makefile.in so that it doesn't try to remake all of the
   documentation (ps, dvi, etc.) on a `make install'.

o. Fixed an auto-increment error that caused bash -c args to sometimes dump
   core.

p. Fixed a bug that caused $HISTIGNORE to fail when the history line
   contained globbing characters.

2. Changes to Readline

a. There is a new string variable, rl_library_version, available for use by
   applications.  The current value is "2.1".

b. A bug encountered when expand-tilde was enabled and file completion was
   attempted on a word beginning with `~/' was fixed.

c. A slight change was made to the incremental search termination behavior.
   ESC still terminates the search, but if input is pending or arrives
   within 0.1 seconds (on systems with select(2)), it is used as a prefix
   character.  This is intended to allow users to terminate searches with
   the arrow keys and get the behavior they expect.
