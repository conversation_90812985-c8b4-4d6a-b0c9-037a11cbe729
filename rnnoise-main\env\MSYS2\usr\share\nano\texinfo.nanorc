## Syntax highlighting for Texinfo files.

## Original author:  <PERSON><PERSON>
## License:  GPL version 3 or newer

syntax texinfo "\.texi$"
header "^\\input texinfo"
magic "Texinfo source"
comment "@c "

# How the manual gets listed in the directory node.
color purple start="^@direntry" end="^@end direntry"

# Command arguments, trailing and enclosed.
color cyan "^@[[:lower:]]+[[:space:]]+.*"
color lightmagenta "@([[:lower:]]+|,|H|U)\{([^}]|@\}|@[[:lower:]]+\{[^}]*\})*\}"
# Commands themselves.
color yellow "^@[[:lower:]]+([[:space:]]|$)|@([[:lower:]]+|,|H|U|AA|AE|DH|L|OE?|(La)?TeX|TH)\{|\}"
color pink "@[!"'&*./:=?@\^`{}~-]"

# Special separator for headings and footings.
color mint "@\|"

# Menu items.
color brightred "^\*[[:space:]]+.*::.*"

# Comments.
color green "@c(omment)?[[:space:]]+.*"

# Trailing whitespace.
color ,green "[[:space:]]+$"
