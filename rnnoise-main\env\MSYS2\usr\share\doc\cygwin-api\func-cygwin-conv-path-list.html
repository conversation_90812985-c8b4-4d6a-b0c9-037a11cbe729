<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_conv_path_list</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="cygwin-functions.html#func-cygwin-path" title="Path conversion functions"><link rel="prev" href="func-cygwin-conv-path.html" title="cygwin_conv_path"><link rel="next" href="func-cygwin-create-path.html" title="cygwin_create_path"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_conv_path_list</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="func-cygwin-conv-path.html">Prev</a>&#160;</td><th width="60%" align="center">Path conversion functions</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-create-path.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-conv-path-list"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_conv_path_list</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">ssize_t
<b class="fsfunc">cygwin_conv_path_list</b>(</code>cygwin_conv_path_t <var class="pdparam">what</var>, const void * <var class="pdparam">from</var>, void * <var class="pdparam">to</var>, size_t <var class="pdparam">size</var><code>)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-conv-path-list-desc"></a><h2>Description</h2><p>This is the same as <code class="function">cygwin_conv_path</code>, but the
input is treated as a path list in $PATH or %PATH% notation.</p><p>If <em class="parameter"><code>what</code></em> is CCP_POSIX_TO_WIN_A or
CCP_POSIX_TO_WIN_W, given a POSIX $PATH-style string (i.e. /foo:/bar)
convert it to the equivalent Win32 %PATH%-style string (i.e. d:\;e:\bar).</p><p>If <em class="parameter"><code>what</code></em> is CCP_WIN_A_TO_POSIX or
CCP_WIN_W_TO_POSIX, given a Win32 %PATH%-style string (i.e. d:\;e:\bar)
convert it to the equivalent POSIX $PATH-style string (i.e. /foo:/bar).</p><p><em class="parameter"><code>size</code></em> is the size of the buffer pointed to by
<em class="parameter"><code>to</code></em> in bytes.</p></div><div class="refsect1"><a name="func-cygwin-conv-path-list-also"></a><h2>See also</h2><p>See also <a class="link" href="func-cygwin-conv-path.html" title="cygwin_conv_path">cygwin_conv_path</a></p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="func-cygwin-conv-path.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="cygwin-functions.html#func-cygwin-path">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-create-path.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">cygwin_conv_path&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;cygwin_create_path</td></tr></table></div></body></html>
