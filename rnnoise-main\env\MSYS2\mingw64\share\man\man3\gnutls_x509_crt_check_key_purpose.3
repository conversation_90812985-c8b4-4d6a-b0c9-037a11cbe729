.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_check_key_purpose" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_check_key_purpose \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "unsigned gnutls_x509_crt_check_key_purpose(gnutls_x509_crt_t " cert ", const char * " purpose ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "const char * purpose" 12
a key purpose OID (e.g., \fBGNUTLS_KP_CODE_SIGNING\fP)
.IP "unsigned flags" 12
zero or \fBGNUTLS_KP_FLAG_DISALLOW_ANY\fP
.SH "DESCRIPTION"
This function will check whether the given certificate matches
the provided key purpose. If  \fIflags\fP contains \fBGNUTLS_KP_FLAG_ALLOW_ANY\fP then
it a certificate marked for any purpose will not match.
.SH "RETURNS"
zero if the key purpose doesn't match, and non\-zero otherwise.
.SH "SINCE"
3.5.6
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
