# bash completion for sha256(1) and friends                -*- shell-script -*-

_comp_cmd_sha256sum()
{
    local cur prev words cword was_split comp_args
    _comp_initialize -s -- "$@" || return

    case $prev in
        -h | --help | --version)
            return
            ;;
    esac

    [[ $was_split ]] && return

    if [[ $cur == -* ]]; then
        _comp_complete_longopt "$@"
        return
    fi

    local sumtype=${1##*/}
    sumtype=${sumtype%sum}

    local opt
    for opt in "${words[@]}"; do
        if [[ $opt == -@(c|-check) ]]; then
            _comp_compgen_filedir "$sumtype"
            return
        fi
    done

    local files
    _comp_compgen -v files filedir &&
        _comp_compgen -- -X "*.$sumtype" -W '"${files[@]}"'
} &&
    complete -F _comp_cmd_sha256sum b2sum md5sum sha{,1,224,256,384,512}sum

# ex: filetype=sh
