.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_subject_alt_names_set" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_subject_alt_names_set \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_subject_alt_names_set(gnutls_subject_alt_names_t " sans ", unsigned int " san_type ", const gnutls_datum_t * " san ", const char * " othername_oid ");"
.SH ARGUMENTS
.IP "gnutls_subject_alt_names_t sans" 12
The alternative names
.IP "unsigned int san_type" 12
The type of the name (of \fBgnutls_subject_alt_names_t\fP)
.IP "const gnutls_datum_t * san" 12
The alternative name data
.IP "const char * othername_oid" 12
The object identifier if  \fIsan_type\fP is \fBGNUTLS_SAN_OTHERNAME\fP
.SH "DESCRIPTION"
This function will store the specified alternative name in
the  \fIsans\fP .

Since version 3.5.7 the \fBGNUTLS_SAN_RFC822NAME\fP, \fBGNUTLS_SAN_DNSNAME\fP, and
\fBGNUTLS_SAN_OTHERNAME_XMPP\fP are converted to ACE format when necessary.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0), otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
