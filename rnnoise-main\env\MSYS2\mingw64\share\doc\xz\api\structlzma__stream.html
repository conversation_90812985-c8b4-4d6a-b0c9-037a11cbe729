<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_stream Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structlzma__stream.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_stream Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Passing data to and from liblzma.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;base.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a72fdc738c793f07a5c29715aa57802cf" id="r_a72fdc738c793f07a5c29715aa57802cf"><td class="memItemLeft" align="right" valign="top">const uint8_t *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a72fdc738c793f07a5c29715aa57802cf">next_in</a></td></tr>
<tr class="separator:a72fdc738c793f07a5c29715aa57802cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb680ecea31910cbda1d7a6ad4f191c0" id="r_abb680ecea31910cbda1d7a6ad4f191c0"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abb680ecea31910cbda1d7a6ad4f191c0">avail_in</a></td></tr>
<tr class="separator:abb680ecea31910cbda1d7a6ad4f191c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a411e1755d6185756caefabc3932c7b" id="r_a1a411e1755d6185756caefabc3932c7b"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1a411e1755d6185756caefabc3932c7b">total_in</a></td></tr>
<tr class="separator:a1a411e1755d6185756caefabc3932c7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14ee64ed636ddcb775edf87e2b9f42ec" id="r_a14ee64ed636ddcb775edf87e2b9f42ec"><td class="memItemLeft" align="right" valign="top">uint8_t *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a14ee64ed636ddcb775edf87e2b9f42ec">next_out</a></td></tr>
<tr class="separator:a14ee64ed636ddcb775edf87e2b9f42ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ff28ea4e39148723c19f59811627904" id="r_a5ff28ea4e39148723c19f59811627904"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5ff28ea4e39148723c19f59811627904">avail_out</a></td></tr>
<tr class="separator:a5ff28ea4e39148723c19f59811627904"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80d703ffdfd7661e344fe7b61ff737fa" id="r_a80d703ffdfd7661e344fe7b61ff737fa"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a80d703ffdfd7661e344fe7b61ff737fa">total_out</a></td></tr>
<tr class="separator:a80d703ffdfd7661e344fe7b61ff737fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4eb2f3e87e32cc4bea613898b0bd353f" id="r_a4eb2f3e87e32cc4bea613898b0bd353f"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4eb2f3e87e32cc4bea613898b0bd353f">allocator</a></td></tr>
<tr class="memdesc:a4eb2f3e87e32cc4bea613898b0bd353f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Custom memory allocation functions.  <br /></td></tr>
<tr class="separator:a4eb2f3e87e32cc4bea613898b0bd353f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a209da54c2fb5dea40ad011c8408300d0" id="r_a209da54c2fb5dea40ad011c8408300d0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#ab1a60127c640135687a5bcc232cec906">lzma_internal</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a209da54c2fb5dea40ad011c8408300d0">internal</a></td></tr>
<tr class="separator:a209da54c2fb5dea40ad011c8408300d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7c43a61f3dfeb0b9c8487b7f275054e" id="r_af7c43a61f3dfeb0b9c8487b7f275054e"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af7c43a61f3dfeb0b9c8487b7f275054e">seek_pos</a></td></tr>
<tr class="memdesc:af7c43a61f3dfeb0b9c8487b7f275054e"><td class="mdescLeft">&#160;</td><td class="mdescRight">New seek input position for LZMA_SEEK_NEEDED.  <br /></td></tr>
<tr class="separator:af7c43a61f3dfeb0b9c8487b7f275054e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Passing data to and from liblzma. </p>
<p>The <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> structure is used for</p><ul>
<li>passing pointers to input and output buffers to liblzma;</li>
<li>defining custom memory handler functions; and</li>
<li>holding a pointer to coder-specific internal data structures.</li>
</ul>
<p>Typical usage:</p>
<ul>
<li>After allocating <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> (on stack or with malloc()), it must be initialized to LZMA_STREAM_INIT (see LZMA_STREAM_INIT for details).</li>
<li>Initialize a coder to the <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a>, for example by using <a class="el" href="container_8h.html#acbdad999c544872f0f5d242f0d1a4ed4" title="Initialize .xz Stream encoder using a preset number.">lzma_easy_encoder()</a> or <a class="el" href="container_8h.html#a21cbebf2771617bb1e956385cfb353e3" title="Decode .xz, .lzma, and .lz (lzip) files with autodetection.">lzma_auto_decoder()</a>. Some notes:<ul>
<li>In contrast to zlib, strm-&gt;next_in and strm-&gt;next_out are ignored by all initialization functions, thus it is safe to not initialize them yet.</li>
<li>The initialization functions always set strm-&gt;total_in and strm-&gt;total_out to zero.</li>
<li>If the initialization function fails, no memory is left allocated that would require freeing with <a class="el" href="base_8h.html#a854ff37464ae1225febf14db1af43308" title="Free memory allocated for the coder data structures.">lzma_end()</a> even if some memory was associated with the <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> structure when the initialization function was called.</li>
</ul>
</li>
<li>Use <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> to do the actual work.</li>
<li>Once the coding has been finished, the existing <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> can be reused. It is OK to reuse <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> with different initialization function without calling <a class="el" href="base_8h.html#a854ff37464ae1225febf14db1af43308" title="Free memory allocated for the coder data structures.">lzma_end()</a> first. Old allocations are automatically freed.</li>
<li>Finally, use <a class="el" href="base_8h.html#a854ff37464ae1225febf14db1af43308" title="Free memory allocated for the coder data structures.">lzma_end()</a> to free the allocated memory. <a class="el" href="base_8h.html#a854ff37464ae1225febf14db1af43308" title="Free memory allocated for the coder data structures.">lzma_end()</a> never frees the <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> structure itself.</li>
</ul>
<p>Application may modify the values of total_in and total_out as it wants. They are updated by liblzma to match the amount of data read and written but aren't used for anything else except as a possible return values from <a class="el" href="base_8h.html#ab6447cd68eeecbd6b88f21daeb8ce751" title="Get progress information.">lzma_get_progress()</a>. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a72fdc738c793f07a5c29715aa57802cf" name="a72fdc738c793f07a5c29715aa57802cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a72fdc738c793f07a5c29715aa57802cf">&#9670;&#160;</a></span>next_in</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint8_t* lzma_stream::next_in</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Pointer to the next input byte. </p>

</div>
</div>
<a id="abb680ecea31910cbda1d7a6ad4f191c0" name="abb680ecea31910cbda1d7a6ad4f191c0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abb680ecea31910cbda1d7a6ad4f191c0">&#9670;&#160;</a></span>avail_in</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_stream::avail_in</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Number of available input bytes in next_in. </p>

</div>
</div>
<a id="a1a411e1755d6185756caefabc3932c7b" name="a1a411e1755d6185756caefabc3932c7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a411e1755d6185756caefabc3932c7b">&#9670;&#160;</a></span>total_in</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_stream::total_in</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Total number of bytes read by liblzma. </p>

</div>
</div>
<a id="a14ee64ed636ddcb775edf87e2b9f42ec" name="a14ee64ed636ddcb775edf87e2b9f42ec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a14ee64ed636ddcb775edf87e2b9f42ec">&#9670;&#160;</a></span>next_out</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t* lzma_stream::next_out</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Pointer to the next output position. </p>

</div>
</div>
<a id="a5ff28ea4e39148723c19f59811627904" name="a5ff28ea4e39148723c19f59811627904"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5ff28ea4e39148723c19f59811627904">&#9670;&#160;</a></span>avail_out</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_stream::avail_out</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Amount of free space in next_out. </p>

</div>
</div>
<a id="a80d703ffdfd7661e344fe7b61ff737fa" name="a80d703ffdfd7661e344fe7b61ff737fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a80d703ffdfd7661e344fe7b61ff737fa">&#9670;&#160;</a></span>total_out</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_stream::total_out</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Total number of bytes written by liblzma. </p>

</div>
</div>
<a id="a4eb2f3e87e32cc4bea613898b0bd353f" name="a4eb2f3e87e32cc4bea613898b0bd353f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4eb2f3e87e32cc4bea613898b0bd353f">&#9670;&#160;</a></span>allocator</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a>* lzma_stream::allocator</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Custom memory allocation functions. </p>
<p>In most cases this is NULL which makes liblzma use the standard malloc() and free().</p>
<dl class="section note"><dt>Note</dt><dd>In 5.0.x this is not a const pointer. </dd></dl>

</div>
</div>
<a id="a209da54c2fb5dea40ad011c8408300d0" name="a209da54c2fb5dea40ad011c8408300d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a209da54c2fb5dea40ad011c8408300d0">&#9670;&#160;</a></span>internal</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#ab1a60127c640135687a5bcc232cec906">lzma_internal</a>* lzma_stream::internal</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Internal state is not visible to applications. </p>

</div>
</div>
<a id="af7c43a61f3dfeb0b9c8487b7f275054e" name="af7c43a61f3dfeb0b9c8487b7f275054e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af7c43a61f3dfeb0b9c8487b7f275054e">&#9670;&#160;</a></span>seek_pos</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_stream::seek_pos</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>New seek input position for LZMA_SEEK_NEEDED. </p>
<p>When <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> returns LZMA_SEEK_NEEDED, the new input position needed by liblzma will be available seek_pos. The value is guaranteed to not exceed the file size that was specified when this <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> was initialized.</p>
<p>In all other situations the value of this variable is undefined. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="base_8h.html">base.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structlzma__stream.html">lzma_stream</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
