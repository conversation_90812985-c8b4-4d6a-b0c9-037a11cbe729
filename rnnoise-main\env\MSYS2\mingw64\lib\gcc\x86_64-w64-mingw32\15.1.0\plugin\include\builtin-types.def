/* Copyright (C) 2001-2025 Free Software Foundation, Inc.

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

/* This header provides a declarative way of describing the types that
   are used when declaring builtin functions.

   Before including this header, you must define the following macros:

   DEF_PRIMITIVE_TYPE (ENUM, TYPE)

     The ENUM is an identifier indicating which type is being defined.
     TYPE is an expression for a `tree' that represents the type.

   DEF_FUNCTION_TYPE_0 (ENUM, RETURN)
   DEF_FUNCTION_TYPE_1 (<PERSON>NU<PERSON>, RETUR<PERSON>, ARG1)
   DEF_FUNCTION_TYPE_2 (<PERSON><PERSON><PERSON>, <PERSON><PERSON>UR<PERSON>, ARG1, ARG2)
   [...]

     These macros describe function types.  ENUM is as above.  The
     RETURN type is one of the enumerals already defined.  ARG1, ARG2,
     etc, give the types of the arguments, similarly.

   DEF_FUNCTION_TYPE_VAR_0 (ENUM, RETURN)
   DEF_FUNCTION_TYPE_VAR_1 (ENUM, RETURN, ARG1)
   DEF_FUNCTION_TYPE_VAR_2 (ENUM, RETURN, ARG1, ARG2)
   [...]

     Similar, but for function types that take variable arguments.
     For example:

       DEF_FUNCTION_TYPE_1 (BT_INT_DOUBLE, BT_INT, BT_DOUBLE)

     describes the type `int ()(double)', using the enumeral
     BT_INT_DOUBLE, whereas:

       DEF_FUNCTION_TYPE_VAR_1 (BT_INT_DOUBLE_VAR, BT_INT, BT_DOUBLE)

     describes the type `int ()(double, ...)'.

  DEF_POINTER_TYPE (ENUM, TYPE)

    This macro describes a pointer type.  ENUM is as above; TYPE is
    the type pointed to.  */

DEF_PRIMITIVE_TYPE (BT_VOID, void_type_node)
DEF_PRIMITIVE_TYPE (BT_BOOL, boolean_type_node)
DEF_PRIMITIVE_TYPE (BT_INT, integer_type_node)
DEF_PRIMITIVE_TYPE (BT_UINT, unsigned_type_node)
DEF_PRIMITIVE_TYPE (BT_LONG, long_integer_type_node)
DEF_PRIMITIVE_TYPE (BT_ULONG, long_unsigned_type_node)
DEF_PRIMITIVE_TYPE (BT_LONGLONG, long_long_integer_type_node)
DEF_PRIMITIVE_TYPE (BT_ULONGLONG, long_long_unsigned_type_node)
DEF_PRIMITIVE_TYPE (BT_INTMAX, intmax_type_node)
DEF_PRIMITIVE_TYPE (BT_UINTMAX, uintmax_type_node)
DEF_PRIMITIVE_TYPE (BT_INT8, signed_char_type_node)
DEF_PRIMITIVE_TYPE (BT_INT16, short_integer_type_node)
DEF_PRIMITIVE_TYPE (BT_UINT8, unsigned_char_type_node)
DEF_PRIMITIVE_TYPE (BT_UINT16, uint16_type_node)
DEF_PRIMITIVE_TYPE (BT_UINT32, uint32_type_node)
DEF_PRIMITIVE_TYPE (BT_UINT64, uint64_type_node)
DEF_PRIMITIVE_TYPE (BT_UINT128, uint128_type_node
				? uint128_type_node
				: error_mark_node)
DEF_PRIMITIVE_TYPE (BT_WORD, (*lang_hooks.types.type_for_mode) (word_mode, 1))
DEF_PRIMITIVE_TYPE (BT_UNWINDWORD, (*lang_hooks.types.type_for_mode)
				    (targetm.unwind_word_mode (), 1))
DEF_PRIMITIVE_TYPE (BT_FLOAT, float_type_node)
DEF_PRIMITIVE_TYPE (BT_DOUBLE, double_type_node)
DEF_PRIMITIVE_TYPE (BT_LONGDOUBLE, long_double_type_node)
DEF_PRIMITIVE_TYPE (BT_BFLOAT16, (bfloat16_type_node
				  ? bfloat16_type_node
				  : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT16, (float16_type_node
				 ? float16_type_node
				 : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT32, (float32_type_node
				 ? float32_type_node
				 : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT64, (float64_type_node
				 ? float64_type_node
				 : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT128, (float128_type_node
				  ? float128_type_node
				  : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT32X, (float32x_type_node
				  ? float32x_type_node
				  : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT64X, (float64x_type_node
				  ? float64x_type_node
				  : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT128X, (float128x_type_node
				   ? float128x_type_node
				   : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_COMPLEX_FLOAT, complex_float_type_node)
DEF_PRIMITIVE_TYPE (BT_COMPLEX_DOUBLE, complex_double_type_node)
DEF_PRIMITIVE_TYPE (BT_COMPLEX_LONGDOUBLE, complex_long_double_type_node)
DEF_PRIMITIVE_TYPE (BT_COMPLEX_FLOAT16, (float16_type_node
					 ? build_complex_type
						(float16_type_node)
					 : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_COMPLEX_FLOAT32, (float32_type_node
					 ? build_complex_type
						(float32_type_node)
					 : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_COMPLEX_FLOAT64, (float64_type_node
					 ? build_complex_type
						(float64_type_node)
					 : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_COMPLEX_FLOAT128, (float128_type_node
					  ? build_complex_type
						(float128_type_node)
					  : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_COMPLEX_FLOAT32X, (float32x_type_node
					  ? build_complex_type
						(float32x_type_node)
					  : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_COMPLEX_FLOAT64X, (float64x_type_node
					  ? build_complex_type
						(float64x_type_node)
					  : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_COMPLEX_FLOAT128X, (float128x_type_node
					   ? build_complex_type
						(float128x_type_node)
					   : error_mark_node))

DEF_PRIMITIVE_TYPE (BT_PTR, ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_FILEPTR, fileptr_type_node)
DEF_PRIMITIVE_TYPE (BT_CONST_TM_PTR, const_tm_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_FENV_T_PTR, fenv_t_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_CONST_FENV_T_PTR, const_fenv_t_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_FEXCEPT_T_PTR, fexcept_t_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_CONST_FEXCEPT_T_PTR, const_fexcept_t_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_CONST_PTR, const_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_VOLATILE_PTR,
		    build_pointer_type
		     (build_qualified_type (void_type_node,
					    TYPE_QUAL_VOLATILE)))
DEF_PRIMITIVE_TYPE (BT_CONST_VOLATILE_PTR,
		    build_pointer_type
		     (build_qualified_type (void_type_node,
					  TYPE_QUAL_VOLATILE|TYPE_QUAL_CONST)))
DEF_PRIMITIVE_TYPE (BT_PTRMODE, (*lang_hooks.types.type_for_mode)(ptr_mode, 0))
DEF_PRIMITIVE_TYPE (BT_INT_PTR, integer_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_FLOAT_PTR, float_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_DOUBLE_PTR, double_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_CONST_DOUBLE_PTR,
		    build_pointer_type
		     (build_qualified_type (double_type_node,
		     			    TYPE_QUAL_CONST)))
DEF_PRIMITIVE_TYPE (BT_LONGDOUBLE_PTR, long_double_ptr_type_node)
DEF_PRIMITIVE_TYPE (BT_FLOAT16_PTR, (float16_type_node
				      ? build_pointer_type (float16_type_node)
				      : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT32_PTR, (float32_type_node
				      ? build_pointer_type (float32_type_node)
				      : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT64_PTR, (float64_type_node
				      ? build_pointer_type (float64_type_node)
				      : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT128_PTR, (float128_type_node
				      ? build_pointer_type (float128_type_node)
				      : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT32X_PTR, (float32x_type_node
				      ? build_pointer_type (float32x_type_node)
				      : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT64X_PTR, (float64x_type_node
				      ? build_pointer_type (float64x_type_node)
				      : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_FLOAT128X_PTR, (float128x_type_node
				      ? build_pointer_type (float128x_type_node)
				      : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_PID, pid_type_node)
DEF_PRIMITIVE_TYPE (BT_SIZE, size_type_node)
DEF_PRIMITIVE_TYPE (BT_CONST_SIZE,
		    build_qualified_type (size_type_node, TYPE_QUAL_CONST))
DEF_PRIMITIVE_TYPE (BT_SSIZE, signed_size_type_node)
DEF_PRIMITIVE_TYPE (BT_WINT, wint_type_node)
DEF_PRIMITIVE_TYPE (BT_STRING, string_type_node)
DEF_PRIMITIVE_TYPE (BT_CONST_STRING, const_string_type_node)

DEF_PRIMITIVE_TYPE (BT_DFLOAT32, (dfloat32_type_node
				  ? dfloat32_type_node
				  : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_DFLOAT64, (dfloat64_type_node
				  ? dfloat64_type_node
				  : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_DFLOAT128, (dfloat128_type_node
				   ? dfloat128_type_node
				   : error_mark_node))
DEF_PRIMITIVE_TYPE (BT_DFLOAT64X, (dfloat64x_type_node
				   ? dfloat64x_type_node
				   : error_mark_node))

DEF_PRIMITIVE_TYPE (BT_VALIST_REF, va_list_ref_type_node)
DEF_PRIMITIVE_TYPE (BT_VALIST_ARG, va_list_arg_type_node)

DEF_PRIMITIVE_TYPE (BT_I1, builtin_type_for_size (BITS_PER_UNIT*1, 1))
DEF_PRIMITIVE_TYPE (BT_I2, builtin_type_for_size (BITS_PER_UNIT*2, 1))
DEF_PRIMITIVE_TYPE (BT_I4, builtin_type_for_size (BITS_PER_UNIT*4, 1))
DEF_PRIMITIVE_TYPE (BT_I8, builtin_type_for_size (BITS_PER_UNIT*8, 1))
DEF_PRIMITIVE_TYPE (BT_I16, builtin_type_for_size (BITS_PER_UNIT*16, 1))

/* The C type `char * const *'.  */
DEF_PRIMITIVE_TYPE (BT_PTR_CONST_STRING,
		    build_pointer_type
		     (build_qualified_type (string_type_node,
					    TYPE_QUAL_CONST)))

DEF_POINTER_TYPE (BT_PTR_UINT, BT_UINT)
DEF_POINTER_TYPE (BT_PTR_LONG, BT_LONG)
DEF_POINTER_TYPE (BT_PTR_ULONG, BT_ULONG)
DEF_POINTER_TYPE (BT_PTR_LONGLONG, BT_LONGLONG)
DEF_POINTER_TYPE (BT_PTR_ULONGLONG, BT_ULONGLONG)
DEF_POINTER_TYPE (BT_PTR_PTR, BT_PTR)

DEF_FUNCTION_TYPE_0 (BT_FN_VOID, BT_VOID)
DEF_FUNCTION_TYPE_0 (BT_FN_BOOL, BT_BOOL)
DEF_FUNCTION_TYPE_0 (BT_FN_PTR, BT_PTR)
DEF_FUNCTION_TYPE_0 (BT_FN_CONST_STRING, BT_CONST_STRING)
DEF_FUNCTION_TYPE_0 (BT_FN_PID, BT_PID)
DEF_FUNCTION_TYPE_0 (BT_FN_INT, BT_INT)
DEF_FUNCTION_TYPE_0 (BT_FN_UINT, BT_UINT)
DEF_FUNCTION_TYPE_0 (BT_FN_ULONG, BT_ULONG)
DEF_FUNCTION_TYPE_0 (BT_FN_FLOAT, BT_FLOAT)
DEF_FUNCTION_TYPE_0 (BT_FN_DOUBLE, BT_DOUBLE)
/* For "long double" we use LONGDOUBLE (not LONG_DOUBLE) to
   distinguish it from two types in sequence, "long" followed by
   "double".  */
DEF_FUNCTION_TYPE_0 (BT_FN_LONGDOUBLE, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_0 (BT_FN_FLOAT16, BT_FLOAT16)
DEF_FUNCTION_TYPE_0 (BT_FN_FLOAT32, BT_FLOAT32)
DEF_FUNCTION_TYPE_0 (BT_FN_FLOAT64, BT_FLOAT64)
DEF_FUNCTION_TYPE_0 (BT_FN_FLOAT128, BT_FLOAT128)
DEF_FUNCTION_TYPE_0 (BT_FN_FLOAT32X, BT_FLOAT32X)
DEF_FUNCTION_TYPE_0 (BT_FN_FLOAT64X, BT_FLOAT64X)
DEF_FUNCTION_TYPE_0 (BT_FN_FLOAT128X, BT_FLOAT128X)
DEF_FUNCTION_TYPE_0 (BT_FN_DFLOAT32, BT_DFLOAT32)
DEF_FUNCTION_TYPE_0 (BT_FN_DFLOAT64, BT_DFLOAT64)
DEF_FUNCTION_TYPE_0 (BT_FN_DFLOAT128, BT_DFLOAT128)
DEF_FUNCTION_TYPE_0 (BT_FN_DFLOAT64X, BT_DFLOAT64X)

DEF_FUNCTION_TYPE_1 (BT_FN_LONG_LONG, BT_LONG, BT_LONG)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_LONGLONG, BT_LONGLONG, BT_LONGLONG)
DEF_FUNCTION_TYPE_1 (BT_FN_UINTMAX_INTMAX, BT_UINTMAX, BT_INTMAX)
DEF_FUNCTION_TYPE_1 (BT_FN_INTMAX_INTMAX, BT_INTMAX, BT_INTMAX)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT_FLOAT, BT_FLOAT, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_DOUBLE_DOUBLE, BT_DOUBLE, BT_DOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGDOUBLE_LONGDOUBLE,
		     BT_LONGDOUBLE, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT16_FLOAT16, BT_FLOAT16, BT_FLOAT16)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT32_FLOAT32, BT_FLOAT32, BT_FLOAT32)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT64_FLOAT64, BT_FLOAT64, BT_FLOAT64)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT128_FLOAT128, BT_FLOAT128, BT_FLOAT128)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT32X_FLOAT32X, BT_FLOAT32X, BT_FLOAT32X)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT64X_FLOAT64X, BT_FLOAT64X, BT_FLOAT64X)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT128X_FLOAT128X, BT_FLOAT128X, BT_FLOAT128X)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_FLOAT_COMPLEX_FLOAT,
		     BT_COMPLEX_FLOAT, BT_COMPLEX_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_DOUBLE_COMPLEX_DOUBLE,
		     BT_COMPLEX_DOUBLE, BT_COMPLEX_DOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_LONGDOUBLE_COMPLEX_LONGDOUBLE,
		     BT_COMPLEX_LONGDOUBLE, BT_COMPLEX_LONGDOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_FLOAT16_COMPLEX_FLOAT16,
		     BT_COMPLEX_FLOAT16, BT_COMPLEX_FLOAT16)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_FLOAT32_COMPLEX_FLOAT32,
		     BT_COMPLEX_FLOAT32, BT_COMPLEX_FLOAT32)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_FLOAT64_COMPLEX_FLOAT64,
		     BT_COMPLEX_FLOAT64, BT_COMPLEX_FLOAT64)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_FLOAT128_COMPLEX_FLOAT128,
		     BT_COMPLEX_FLOAT128, BT_COMPLEX_FLOAT128)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_FLOAT32X_COMPLEX_FLOAT32X,
		     BT_COMPLEX_FLOAT32X, BT_COMPLEX_FLOAT32X)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_FLOAT64X_COMPLEX_FLOAT64X,
		     BT_COMPLEX_FLOAT64X, BT_COMPLEX_FLOAT64X)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_FLOAT128X_COMPLEX_FLOAT128X,
		     BT_COMPLEX_FLOAT128X, BT_COMPLEX_FLOAT128X)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT_COMPLEX_FLOAT,
		     BT_FLOAT, BT_COMPLEX_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_DOUBLE_COMPLEX_DOUBLE,
		     BT_DOUBLE, BT_COMPLEX_DOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGDOUBLE_COMPLEX_LONGDOUBLE,
		     BT_LONGDOUBLE, BT_COMPLEX_LONGDOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT16_COMPLEX_FLOAT16,
		     BT_FLOAT16, BT_COMPLEX_FLOAT16)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT32_COMPLEX_FLOAT32,
		     BT_FLOAT32, BT_COMPLEX_FLOAT32)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT64_COMPLEX_FLOAT64,
		     BT_FLOAT64, BT_COMPLEX_FLOAT64)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT128_COMPLEX_FLOAT128,
		     BT_FLOAT128, BT_COMPLEX_FLOAT128)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT32X_COMPLEX_FLOAT32X,
		     BT_FLOAT32X, BT_COMPLEX_FLOAT32X)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT64X_COMPLEX_FLOAT64X,
		     BT_FLOAT64X, BT_COMPLEX_FLOAT64X)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT128X_COMPLEX_FLOAT128X,
		     BT_FLOAT128X, BT_COMPLEX_FLOAT128X)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_FLOAT_FLOAT,
		     BT_COMPLEX_FLOAT, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_DOUBLE_DOUBLE,
		     BT_COMPLEX_DOUBLE, BT_DOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_COMPLEX_LONGDOUBLE_LONGDOUBLE,
		     BT_COMPLEX_LONGDOUBLE, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_PTR_UINT, BT_PTR, BT_UINT)
DEF_FUNCTION_TYPE_1 (BT_FN_PTR_SIZE, BT_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_INT, BT_INT, BT_INT)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_UINT, BT_INT, BT_UINT)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_LONG, BT_INT, BT_LONG)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_ULONG, BT_INT, BT_ULONG)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_LONGLONG, BT_INT, BT_LONGLONG)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_ULONGLONG, BT_INT, BT_ULONGLONG)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_INTMAX, BT_INT, BT_INTMAX)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_UINTMAX, BT_INT, BT_UINTMAX)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_PTR, BT_INT, BT_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_FLOAT, BT_INT, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_DOUBLE, BT_INT, BT_DOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_LONGDOUBLE, BT_INT, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_FLOAT16, BT_INT, BT_FLOAT16)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_FLOAT32, BT_INT, BT_FLOAT32)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_FLOAT64, BT_INT, BT_FLOAT64)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_FLOAT128, BT_INT, BT_FLOAT128)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_FLOAT32X, BT_INT, BT_FLOAT32X)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_FLOAT64X, BT_INT, BT_FLOAT64X)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_FLOAT128X, BT_INT, BT_FLOAT128X)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_DFLOAT32, BT_INT, BT_DFLOAT32)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_DFLOAT64, BT_INT, BT_DFLOAT64)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_DFLOAT128, BT_INT, BT_DFLOAT128)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_FLOAT, BT_LONG, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_DOUBLE, BT_LONG, BT_DOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_LONGDOUBLE, BT_LONG, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_FLOAT16, BT_LONG, BT_FLOAT16)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_FLOAT32, BT_LONG, BT_FLOAT32)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_FLOAT64, BT_LONG, BT_FLOAT64)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_FLOAT128, BT_LONG, BT_FLOAT128)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_FLOAT32X, BT_LONG, BT_FLOAT32X)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_FLOAT64X, BT_LONG, BT_FLOAT64X)
DEF_FUNCTION_TYPE_1 (BT_FN_LONG_FLOAT128X, BT_LONG, BT_FLOAT128X)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_FLOAT, BT_LONGLONG, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_DOUBLE, BT_LONGLONG, BT_DOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_LONGDOUBLE, BT_LONGLONG, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_FLOAT16, BT_LONGLONG, BT_FLOAT16)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_FLOAT32, BT_LONGLONG, BT_FLOAT32)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_FLOAT64, BT_LONGLONG, BT_FLOAT64)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_FLOAT128, BT_LONGLONG, BT_FLOAT128)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_FLOAT32X, BT_LONGLONG, BT_FLOAT32X)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_FLOAT64X, BT_LONGLONG, BT_FLOAT64X)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGLONG_FLOAT128X, BT_LONGLONG, BT_FLOAT128X)
DEF_FUNCTION_TYPE_1 (BT_FN_VOID_PTR, BT_VOID, BT_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_SIZE_CONST_STRING, BT_SIZE, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_CONST_STRING, BT_INT, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_PTR_PTR, BT_PTR, BT_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_VOID_VALIST_REF, BT_VOID, BT_VALIST_REF)
DEF_FUNCTION_TYPE_1 (BT_FN_VOID_INT, BT_VOID, BT_INT)
DEF_FUNCTION_TYPE_1 (BT_FN_VOID_BOOL, BT_VOID, BT_BOOL)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT_CONST_STRING, BT_FLOAT, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_DOUBLE_CONST_STRING, BT_DOUBLE, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_LONGDOUBLE_CONST_STRING,
		     BT_LONGDOUBLE, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_BFLOAT16_CONST_STRING, BT_BFLOAT16, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT16_CONST_STRING, BT_FLOAT16, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT32_CONST_STRING, BT_FLOAT32, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT64_CONST_STRING, BT_FLOAT64, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT128_CONST_STRING, BT_FLOAT128, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT32X_CONST_STRING, BT_FLOAT32X, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT64X_CONST_STRING, BT_FLOAT64X, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT128X_CONST_STRING, BT_FLOAT128X, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_DFLOAT32_CONST_STRING, BT_DFLOAT32, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_DFLOAT64_CONST_STRING, BT_DFLOAT64, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_DFLOAT128_CONST_STRING,
		     BT_DFLOAT128, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_DFLOAT64X_CONST_STRING, BT_DFLOAT64X,
		     BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_STRING_CONST_STRING, BT_STRING, BT_CONST_STRING)
DEF_FUNCTION_TYPE_1 (BT_FN_UNWINDWORD_PTR, BT_UNWINDWORD, BT_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_WINT, BT_INT, BT_WINT)
DEF_FUNCTION_TYPE_1 (BT_FN_WINT_WINT, BT_WINT, BT_WINT)
DEF_FUNCTION_TYPE_1 (BT_FN_DFLOAT32_DFLOAT32, BT_DFLOAT32, BT_DFLOAT32)
DEF_FUNCTION_TYPE_1 (BT_FN_DFLOAT64_DFLOAT64, BT_DFLOAT64, BT_DFLOAT64)
DEF_FUNCTION_TYPE_1 (BT_FN_DFLOAT128_DFLOAT128, BT_DFLOAT128, BT_DFLOAT128)
DEF_FUNCTION_TYPE_1 (BT_FN_DFLOAT64X_DFLOAT64X, BT_DFLOAT64X, BT_DFLOAT64X)
DEF_FUNCTION_TYPE_1 (BT_FN_VOID_VPTR, BT_VOID, BT_VOLATILE_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_VOID_PTRPTR, BT_VOID, BT_PTR_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_VOID_CONST_PTR, BT_VOID, BT_CONST_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT_UINT, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT_INT, BT_UINT, BT_INT)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT_ULONG, BT_UINT, BT_ULONG)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT_LONG, BT_UINT, BT_LONG)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT_PTR, BT_UINT, BT_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT_CONST_PTR, BT_UINT, BT_CONST_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_ULONG_PTR, BT_ULONG, BT_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_ULONG_CONST_PTR, BT_ULONG, BT_CONST_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_ULONG_ULONG, BT_ULONG, BT_ULONG)
DEF_FUNCTION_TYPE_1 (BT_FN_ULONG_LONG, BT_ULONG, BT_LONG)
DEF_FUNCTION_TYPE_1 (BT_FN_ULONGLONG_ULONGLONG, BT_ULONGLONG, BT_ULONGLONG)
DEF_FUNCTION_TYPE_1 (BT_FN_ULONGLONG_LONGLONG, BT_ULONGLONG, BT_LONGLONG)
DEF_FUNCTION_TYPE_1 (BT_FN_INT8_FLOAT, BT_INT8, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_INT16_FLOAT, BT_INT16, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT32_FLOAT, BT_UINT32, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT16_FLOAT, BT_UINT16, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT8_FLOAT, BT_UINT8, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT16_UINT16, BT_UINT16, BT_UINT16)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT32_UINT32, BT_UINT32, BT_UINT32)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT64_UINT64, BT_UINT64, BT_UINT64)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT128_UINT128, BT_UINT128, BT_UINT128)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT64_FLOAT, BT_UINT64, BT_FLOAT)
DEF_FUNCTION_TYPE_1 (BT_FN_BOOL_INT, BT_BOOL, BT_INT)
DEF_FUNCTION_TYPE_1 (BT_FN_BOOL_PTR, BT_BOOL, BT_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_PTR_CONST_PTR, BT_PTR, BT_CONST_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_CONST_PTR_CONST_PTR, BT_CONST_PTR, BT_CONST_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT16_UINT32, BT_UINT16, BT_UINT32)
DEF_FUNCTION_TYPE_1 (BT_FN_UINT32_UINT16, BT_UINT32, BT_UINT16)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_FENV_T_PTR, BT_INT, BT_FENV_T_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_INT_CONST_FENV_T_PTR, BT_INT, BT_CONST_FENV_T_PTR)

DEF_POINTER_TYPE (BT_PTR_FN_VOID_PTR, BT_FN_VOID_PTR)

DEF_FUNCTION_TYPE_2 (BT_FN_VOID_PTR_INT, BT_VOID, BT_PTR, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_STRING_STRING_CONST_STRING,
		     BT_STRING, BT_STRING, BT_CONST_STRING)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_CONST_STRING_CONST_STRING,
		     BT_INT, BT_CONST_STRING, BT_CONST_STRING)
DEF_FUNCTION_TYPE_2 (BT_FN_STRING_CONST_STRING_CONST_STRING,
		     BT_STRING, BT_CONST_STRING, BT_CONST_STRING)
DEF_FUNCTION_TYPE_2 (BT_FN_SIZE_CONST_STRING_CONST_STRING,
		     BT_SIZE, BT_CONST_STRING, BT_CONST_STRING)
DEF_FUNCTION_TYPE_2 (BT_FN_STRING_CONST_STRING_INT,
		     BT_STRING, BT_CONST_STRING, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_STRING_CONST_STRING_SIZE,
		     BT_STRING, BT_CONST_STRING, BT_SIZE)
DEF_FUNCTION_TYPE_2 (BT_FN_SIZE_CONST_STRING_SIZE,
		     BT_SIZE, BT_CONST_STRING, BT_SIZE)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_CONST_STRING_FILEPTR,
		     BT_INT, BT_CONST_STRING, BT_FILEPTR)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_INT_FILEPTR,
		     BT_INT, BT_INT, BT_FILEPTR)
DEF_FUNCTION_TYPE_2 (BT_FN_UINT16_UINT16_UINT16,
		     BT_UINT16, BT_UINT16, BT_UINT16)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_PTR_INT,
     		     BT_INT, BT_PTR, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_UINT_PTR_UINT,
     		     BT_UINT, BT_PTR, BT_UINT)
DEF_FUNCTION_TYPE_2 (BT_FN_LONG_PTR_LONG,
		     BT_LONG, BT_PTR, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_ULONG_PTR_ULONG,
		     BT_ULONG, BT_PTR, BT_ULONG)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_PTRMODE_PTR,
		     BT_VOID, BT_PTRMODE, BT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_PTR_PTRMODE,
		     BT_VOID, BT_PTR, BT_PTRMODE)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_UINT8_UINT8,
     		     BT_VOID, BT_UINT8, BT_UINT8)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_UINT16_UINT16,
     		     BT_VOID, BT_UINT16, BT_UINT16)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_UINT32_UINT32,
     		     BT_VOID, BT_UINT32, BT_UINT32)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_UINT64_UINT64,
     		     BT_VOID, BT_UINT64, BT_UINT64)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_FLOAT_FLOAT,
     		     BT_VOID, BT_FLOAT, BT_FLOAT)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_DOUBLE_DOUBLE,
     		     BT_VOID, BT_DOUBLE, BT_DOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_UINT64_PTR,
     		     BT_VOID, BT_UINT64, BT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VALIST_REF_VALIST_ARG,
		     BT_VOID, BT_VALIST_REF, BT_VALIST_ARG)
DEF_FUNCTION_TYPE_2 (BT_FN_LONG_LONG_LONG,
		     BT_LONG, BT_LONG, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_UINT8_UINT8_UINT8,
		     BT_UINT8, BT_UINT8, BT_UINT8)
DEF_FUNCTION_TYPE_2 (BT_FN_INT8_INT8_INT8,
		     BT_INT8, BT_INT8, BT_INT8)
DEF_FUNCTION_TYPE_2 (BT_FN_INT16_INT16_INT16,
		     BT_INT16, BT_INT16, BT_INT16)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_INT_INT,
		     BT_INT, BT_INT, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_UINT_FLOAT_UINT,
		     BT_UINT, BT_FLOAT, BT_UINT)
DEF_FUNCTION_TYPE_2 (BT_FN_UINT_DOUBLE_UINT,
		     BT_UINT, BT_DOUBLE, BT_UINT)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT_UINT_UINT,
		     BT_FLOAT, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_2 (BT_FN_ULONG_UINT_UINT,
		     BT_ULONG, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_2 (BT_FN_ULONG_UINT_PTR,
		     BT_ULONG, BT_UINT, BT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_ULONG_ULONG_ULONG,
		     BT_ULONG, BT_ULONG, BT_ULONG)
DEF_FUNCTION_TYPE_2 (BT_FN_UINT_UINT_UINT,
		     BT_UINT, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_PTR_CONST_STRING,
		     BT_INT, BT_PTR, BT_CONST_STRING)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_PTR_SIZE,
		     BT_VOID, BT_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_UINT_PTR,
		     BT_VOID, BT_UINT, BT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT_FLOAT_FLOAT,
		     BT_FLOAT, BT_FLOAT, BT_FLOAT)
DEF_FUNCTION_TYPE_2 (BT_FN_DOUBLE_DOUBLE_DOUBLE,
		     BT_DOUBLE, BT_DOUBLE, BT_DOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_LONGDOUBLE_LONGDOUBLE_LONGDOUBLE,
		     BT_LONGDOUBLE, BT_LONGDOUBLE, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT16_FLOAT16_FLOAT16,
		     BT_FLOAT16, BT_FLOAT16, BT_FLOAT16)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32_FLOAT32_FLOAT32,
		     BT_FLOAT32, BT_FLOAT32, BT_FLOAT32)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64_FLOAT64_FLOAT64,
		     BT_FLOAT64, BT_FLOAT64, BT_FLOAT64)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128_FLOAT128_FLOAT128,
		     BT_FLOAT128, BT_FLOAT128, BT_FLOAT128)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32X_FLOAT32X_FLOAT32X,
		     BT_FLOAT32X, BT_FLOAT32X, BT_FLOAT32X)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64X_FLOAT64X_FLOAT64X,
		     BT_FLOAT64X, BT_FLOAT64X, BT_FLOAT64X)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128X_FLOAT128X_FLOAT128X,
		     BT_FLOAT128X, BT_FLOAT128X, BT_FLOAT128X)
DEF_FUNCTION_TYPE_2 (BT_FN_BFLOAT16_BFLOAT16_BFLOAT16,
		     BT_BFLOAT16, BT_BFLOAT16, BT_BFLOAT16)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT_FLOAT_FLOATPTR,
		     BT_FLOAT, BT_FLOAT, BT_FLOAT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_DOUBLE_DOUBLE_DOUBLEPTR,
		     BT_DOUBLE, BT_DOUBLE, BT_DOUBLE_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_LONGDOUBLE_LONGDOUBLE_LONGDOUBLEPTR,
		     BT_LONGDOUBLE, BT_LONGDOUBLE, BT_LONGDOUBLE_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT16_FLOAT16_FLOAT16PTR,
		     BT_FLOAT16, BT_FLOAT16, BT_FLOAT16_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32_FLOAT32_FLOAT32PTR,
		     BT_FLOAT32, BT_FLOAT32, BT_FLOAT32_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64_FLOAT64_FLOAT64PTR,
		     BT_FLOAT64, BT_FLOAT64, BT_FLOAT64_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128_FLOAT128_FLOAT128PTR,
		     BT_FLOAT128, BT_FLOAT128, BT_FLOAT128_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32X_FLOAT32X_FLOAT32XPTR,
		     BT_FLOAT32X, BT_FLOAT32X, BT_FLOAT32X_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64X_FLOAT64X_FLOAT64XPTR,
		     BT_FLOAT64X, BT_FLOAT64X, BT_FLOAT64X_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128X_FLOAT128X_FLOAT128XPTR,
		     BT_FLOAT128X, BT_FLOAT128X, BT_FLOAT128X_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT_FLOAT_LONGDOUBLE,
		     BT_FLOAT, BT_FLOAT, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_DOUBLE_DOUBLE_LONGDOUBLE,
		     BT_DOUBLE, BT_DOUBLE, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT_FLOAT_INT,
		     BT_FLOAT, BT_FLOAT, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_DOUBLE_DOUBLE_INT,
		     BT_DOUBLE, BT_DOUBLE, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_LONGDOUBLE_LONGDOUBLE_INT,
		     BT_LONGDOUBLE, BT_LONGDOUBLE, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT16_FLOAT16_INT,
		     BT_FLOAT16, BT_FLOAT16, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32_FLOAT32_INT,
		     BT_FLOAT32, BT_FLOAT32, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64_FLOAT64_INT,
		     BT_FLOAT64, BT_FLOAT64, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128_FLOAT128_INT,
		     BT_FLOAT128, BT_FLOAT128, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32X_FLOAT32X_INT,
		     BT_FLOAT32X, BT_FLOAT32X, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64X_FLOAT64X_INT,
		     BT_FLOAT64X, BT_FLOAT64X, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128X_FLOAT128X_INT,
		     BT_FLOAT128X, BT_FLOAT128X, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT_FLOAT_INTPTR,
		     BT_FLOAT, BT_FLOAT, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_DOUBLE_DOUBLE_INTPTR,
		     BT_DOUBLE, BT_DOUBLE, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_LONGDOUBLE_LONGDOUBLE_INTPTR,
		     BT_LONGDOUBLE, BT_LONGDOUBLE, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT16_FLOAT16_INTPTR,
		     BT_FLOAT16, BT_FLOAT16, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32_FLOAT32_INTPTR,
		     BT_FLOAT32, BT_FLOAT32, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64_FLOAT64_INTPTR,
		     BT_FLOAT64, BT_FLOAT64, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128_FLOAT128_INTPTR,
		     BT_FLOAT128, BT_FLOAT128, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32X_FLOAT32X_INTPTR,
		     BT_FLOAT32X, BT_FLOAT32X, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64X_FLOAT64X_INTPTR,
		     BT_FLOAT64X, BT_FLOAT64X, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128X_FLOAT128X_INTPTR,
		     BT_FLOAT128X, BT_FLOAT128X, BT_INT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT_INT_FLOAT,
		     BT_FLOAT, BT_INT, BT_FLOAT)
DEF_FUNCTION_TYPE_2 (BT_FN_DOUBLE_INT_DOUBLE,
		     BT_DOUBLE, BT_INT, BT_DOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_LONGDOUBLE_INT_LONGDOUBLE,
		     BT_LONGDOUBLE, BT_INT, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT_FLOAT_LONG,
		     BT_FLOAT, BT_FLOAT, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_DOUBLE_DOUBLE_LONG,
		     BT_DOUBLE, BT_DOUBLE, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_LONGDOUBLE_LONGDOUBLE_LONG,
		     BT_LONGDOUBLE, BT_LONGDOUBLE, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT16_FLOAT16_LONG,
		     BT_FLOAT16, BT_FLOAT16, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32_FLOAT32_LONG,
		     BT_FLOAT32, BT_FLOAT32, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64_FLOAT64_LONG,
		     BT_FLOAT64, BT_FLOAT64, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128_FLOAT128_LONG,
		     BT_FLOAT128, BT_FLOAT128, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT32X_FLOAT32X_LONG,
		     BT_FLOAT32X, BT_FLOAT32X, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT64X_FLOAT64X_LONG,
		     BT_FLOAT64X, BT_FLOAT64X, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_FLOAT128X_FLOAT128X_LONG,
		     BT_FLOAT128X, BT_FLOAT128X, BT_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_CONST_STRING_VALIST_ARG,
		     BT_INT, BT_CONST_STRING, BT_VALIST_ARG)
DEF_FUNCTION_TYPE_2 (BT_FN_PTR_SIZE_SIZE,
		     BT_PTR, BT_SIZE, BT_SIZE)
DEF_FUNCTION_TYPE_2 (BT_FN_PTR_PTR_SIZE,
		     BT_PTR, BT_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_FLOAT_COMPLEX_FLOAT_COMPLEX_FLOAT,
		     BT_COMPLEX_FLOAT, BT_COMPLEX_FLOAT, BT_COMPLEX_FLOAT)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_DOUBLE_COMPLEX_DOUBLE_COMPLEX_DOUBLE,
		     BT_COMPLEX_DOUBLE, BT_COMPLEX_DOUBLE, BT_COMPLEX_DOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_LONGDOUBLE_COMPLEX_LONGDOUBLE_COMPLEX_LONGDOUBLE,
		     BT_COMPLEX_LONGDOUBLE, BT_COMPLEX_LONGDOUBLE, BT_COMPLEX_LONGDOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_FLOAT16_COMPLEX_FLOAT16_COMPLEX_FLOAT16,
		     BT_COMPLEX_FLOAT16, BT_COMPLEX_FLOAT16, BT_COMPLEX_FLOAT16)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_FLOAT32_COMPLEX_FLOAT32_COMPLEX_FLOAT32,
		     BT_COMPLEX_FLOAT32, BT_COMPLEX_FLOAT32, BT_COMPLEX_FLOAT32)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_FLOAT64_COMPLEX_FLOAT64_COMPLEX_FLOAT64,
		     BT_COMPLEX_FLOAT64, BT_COMPLEX_FLOAT64, BT_COMPLEX_FLOAT64)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_FLOAT128_COMPLEX_FLOAT128_COMPLEX_FLOAT128,
		     BT_COMPLEX_FLOAT128, BT_COMPLEX_FLOAT128, BT_COMPLEX_FLOAT128)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_FLOAT32X_COMPLEX_FLOAT32X_COMPLEX_FLOAT32X,
		     BT_COMPLEX_FLOAT32X, BT_COMPLEX_FLOAT32X, BT_COMPLEX_FLOAT32X)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_FLOAT64X_COMPLEX_FLOAT64X_COMPLEX_FLOAT64X,
		     BT_COMPLEX_FLOAT64X, BT_COMPLEX_FLOAT64X, BT_COMPLEX_FLOAT64X)
DEF_FUNCTION_TYPE_2 (BT_FN_COMPLEX_FLOAT128X_COMPLEX_FLOAT128X_COMPLEX_FLOAT128X,
		     BT_COMPLEX_FLOAT128X, BT_COMPLEX_FLOAT128X, BT_COMPLEX_FLOAT128X)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_PTR_PTR, BT_VOID, BT_PTR, BT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_CONST_STRING_PTR_CONST_STRING,
		     BT_INT, BT_CONST_STRING, BT_PTR_CONST_STRING)
DEF_FUNCTION_TYPE_2 (BT_FN_SIZE_CONST_PTR_INT, BT_SIZE, BT_CONST_PTR, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_I1_VPTR_I1, BT_I1, BT_VOLATILE_PTR, BT_I1)
DEF_FUNCTION_TYPE_2 (BT_FN_I2_VPTR_I2, BT_I2, BT_VOLATILE_PTR, BT_I2)
DEF_FUNCTION_TYPE_2 (BT_FN_I4_VPTR_I4, BT_I4, BT_VOLATILE_PTR, BT_I4)
DEF_FUNCTION_TYPE_2 (BT_FN_I8_VPTR_I8, BT_I8, BT_VOLATILE_PTR, BT_I8)
DEF_FUNCTION_TYPE_2 (BT_FN_I16_VPTR_I16, BT_I16, BT_VOLATILE_PTR, BT_I16)
DEF_FUNCTION_TYPE_2 (BT_FN_BOOL_LONGPTR_LONGPTR,
		     BT_BOOL, BT_PTR_LONG, BT_PTR_LONG)
DEF_FUNCTION_TYPE_2 (BT_FN_BOOL_ULONGLONGPTR_ULONGLONGPTR,
		     BT_BOOL, BT_PTR_ULONGLONG, BT_PTR_ULONGLONG)
DEF_FUNCTION_TYPE_2 (BT_FN_I1_CONST_VPTR_INT, BT_I1, BT_CONST_VOLATILE_PTR,
		     BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_I2_CONST_VPTR_INT, BT_I2, BT_CONST_VOLATILE_PTR,
		     BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_I4_CONST_VPTR_INT, BT_I4, BT_CONST_VOLATILE_PTR,
		     BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_I8_CONST_VPTR_INT, BT_I8, BT_CONST_VOLATILE_PTR,
		     BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_I16_CONST_VPTR_INT, BT_I16, BT_CONST_VOLATILE_PTR,
		     BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VPTR_INT, BT_VOID, BT_VOLATILE_PTR, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_BOOL_VPTR_INT, BT_BOOL, BT_VOLATILE_PTR, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_BOOL_SIZE_CONST_VPTR, BT_BOOL, BT_SIZE,
		     BT_CONST_VOLATILE_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_BOOL_INT_BOOL, BT_BOOL, BT_INT, BT_BOOL)
DEF_FUNCTION_TYPE_2 (BT_FN_UINT_UINT_PTR, BT_UINT, BT_UINT, BT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_UINT_UINT_CONST_PTR, BT_UINT, BT_UINT, BT_CONST_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_PTR_CONST_PTR_SIZE, BT_PTR, BT_CONST_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_2 (BT_FN_PTR_CONST_PTR_CONST_PTR, BT_PTR, BT_CONST_PTR, BT_CONST_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_PTRPTR_CONST_PTR, BT_VOID, BT_PTR_PTR, BT_CONST_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_CONST_PTR_SIZE, BT_VOID, BT_CONST_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_2 (BT_FN_CONST_PTR_CONST_PTR_CONST_PTR, BT_CONST_PTR, BT_CONST_PTR, BT_CONST_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_UINT32_UINT64_PTR,
		     BT_UINT32, BT_UINT64, BT_PTR)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_FEXCEPT_T_PTR_INT, BT_INT, BT_FEXCEPT_T_PTR,
		     BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_INT_CONST_FEXCEPT_T_PTR_INT, BT_INT,
		     BT_CONST_FEXCEPT_T_PTR, BT_INT)
DEF_FUNCTION_TYPE_2 (BT_FN_PTR_CONST_PTR_UINT8, BT_PTR, BT_CONST_PTR, BT_UINT8)
DEF_FUNCTION_TYPE_2 (BT_FN_PTR_CONST_PTR_INT, BT_PTR, BT_CONST_PTR, BT_INT)

DEF_POINTER_TYPE (BT_PTR_FN_VOID_PTR_PTR, BT_FN_VOID_PTR_PTR)

DEF_FUNCTION_TYPE_3 (BT_FN_STRING_STRING_CONST_STRING_SIZE,
		     BT_STRING, BT_STRING, BT_CONST_STRING, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_INT_CONST_STRING_CONST_STRING_SIZE,
		     BT_INT, BT_CONST_STRING, BT_CONST_STRING, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_PTRMODE_PTR_INT_PTR,
		     BT_PTRMODE, BT_PTR, BT_INT, BT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_PTR_PTR_CONST_PTR_SIZE,
		     BT_PTR, BT_PTR, BT_CONST_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_PTR_CONST_PTR_SIZE,
		     BT_VOID, BT_PTR, BT_CONST_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_INT_CONST_PTR_CONST_PTR_SIZE,
		     BT_INT, BT_CONST_PTR, BT_CONST_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_PTR_PTR_INT_SIZE,
		     BT_PTR, BT_PTR, BT_INT, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_PTR_INT_SIZE,
		     BT_VOID, BT_PTR, BT_INT, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_PTR_INT_INT,
		     BT_VOID, BT_PTR, BT_INT, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_CONST_PTR_PTR_SIZE,
		     BT_VOID, BT_CONST_PTR, BT_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_INT_STRING_CONST_STRING_VALIST_ARG,
		     BT_INT, BT_STRING, BT_CONST_STRING, BT_VALIST_ARG)
DEF_FUNCTION_TYPE_3 (BT_FN_INT_CONST_STRING_CONST_STRING_VALIST_ARG,
		     BT_INT, BT_CONST_STRING, BT_CONST_STRING, BT_VALIST_ARG)
DEF_FUNCTION_TYPE_3 (BT_FN_INT_FILEPTR_CONST_STRING_VALIST_ARG,
		     BT_INT, BT_FILEPTR, BT_CONST_STRING, BT_VALIST_ARG)
DEF_FUNCTION_TYPE_3 (BT_FN_INT_INT_UINT_UINT,
		     BT_INT, BT_INT, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT_UINT_UINT_UINT,
		     BT_UINT, BT_UINT, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT_UINT_UINT_PTR,
		     BT_UINT, BT_UINT, BT_UINT, BT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT_ULONG_ULONG_UINT,
		     BT_UINT, BT_ULONG, BT_ULONG, BT_UINT)
DEF_FUNCTION_TYPE_3 (BT_FN_ULONG_ULONG_ULONG_ULONG,
		     BT_ULONG, BT_ULONG, BT_ULONG, BT_ULONG)
DEF_FUNCTION_TYPE_3 (BT_FN_LONG_LONG_UINT_UINT,
		     BT_LONG, BT_LONG, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_3 (BT_FN_LONG_LONG_LONG_DOUBLE,
		     BT_LONG, BT_LONG, BT_LONG, BT_DOUBLE)
DEF_FUNCTION_TYPE_3 (BT_FN_ULONG_ULONG_UINT_UINT,
		     BT_ULONG, BT_ULONG, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_3 (BT_FN_STRING_CONST_STRING_CONST_STRING_INT,
		     BT_STRING, BT_CONST_STRING, BT_CONST_STRING, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT_FLOAT_FLOAT_FLOAT,
		     BT_FLOAT, BT_FLOAT, BT_FLOAT, BT_FLOAT)
DEF_FUNCTION_TYPE_3 (BT_FN_DOUBLE_DOUBLE_DOUBLE_DOUBLE,
		     BT_DOUBLE, BT_DOUBLE, BT_DOUBLE, BT_DOUBLE)
DEF_FUNCTION_TYPE_3 (BT_FN_LONGDOUBLE_LONGDOUBLE_LONGDOUBLE_LONGDOUBLE,
		     BT_LONGDOUBLE, BT_LONGDOUBLE, BT_LONGDOUBLE, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT16_FLOAT16_FLOAT16_FLOAT16,
		     BT_FLOAT16, BT_FLOAT16, BT_FLOAT16, BT_FLOAT16)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT32_FLOAT32_FLOAT32_FLOAT32,
		     BT_FLOAT32, BT_FLOAT32, BT_FLOAT32, BT_FLOAT32)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT64_FLOAT64_FLOAT64_FLOAT64,
		     BT_FLOAT64, BT_FLOAT64, BT_FLOAT64, BT_FLOAT64)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT128_FLOAT128_FLOAT128_FLOAT128,
		     BT_FLOAT128, BT_FLOAT128, BT_FLOAT128, BT_FLOAT128)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT32X_FLOAT32X_FLOAT32X_FLOAT32X,
		     BT_FLOAT32X, BT_FLOAT32X, BT_FLOAT32X, BT_FLOAT32X)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT64X_FLOAT64X_FLOAT64X_FLOAT64X,
		     BT_FLOAT64X, BT_FLOAT64X, BT_FLOAT64X, BT_FLOAT64X)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT128X_FLOAT128X_FLOAT128X_FLOAT128X,
		     BT_FLOAT128X, BT_FLOAT128X, BT_FLOAT128X, BT_FLOAT128X)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT_FLOAT_FLOAT_INTPTR,
		     BT_FLOAT, BT_FLOAT, BT_FLOAT, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_DOUBLE_DOUBLE_DOUBLE_INTPTR,
		     BT_DOUBLE, BT_DOUBLE, BT_DOUBLE, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_LONGDOUBLE_LONGDOUBLE_LONGDOUBLE_INTPTR,
		     BT_LONGDOUBLE, BT_LONGDOUBLE, BT_LONGDOUBLE, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT16_FLOAT16_FLOAT16_INTPTR,
		     BT_FLOAT16, BT_FLOAT16, BT_FLOAT16, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT32_FLOAT32_FLOAT32_INTPTR,
		     BT_FLOAT32, BT_FLOAT32, BT_FLOAT32, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT64_FLOAT64_FLOAT64_INTPTR,
		     BT_FLOAT64, BT_FLOAT64, BT_FLOAT64, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT128_FLOAT128_FLOAT128_INTPTR,
		     BT_FLOAT128, BT_FLOAT128, BT_FLOAT128, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT32X_FLOAT32X_FLOAT32X_INTPTR,
		     BT_FLOAT32X, BT_FLOAT32X, BT_FLOAT32X, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT64X_FLOAT64X_FLOAT64X_INTPTR,
		     BT_FLOAT64X, BT_FLOAT64X, BT_FLOAT64X, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_FLOAT128X_FLOAT128X_FLOAT128X_INTPTR,
		     BT_FLOAT128X, BT_FLOAT128X, BT_FLOAT128X, BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_FLOAT_FLOATPTR_FLOATPTR,
		     BT_VOID, BT_FLOAT, BT_FLOAT_PTR, BT_FLOAT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_DOUBLE_DOUBLEPTR_DOUBLEPTR,
		     BT_VOID, BT_DOUBLE, BT_DOUBLE_PTR, BT_DOUBLE_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_LONGDOUBLE_LONGDOUBLEPTR_LONGDOUBLEPTR,
		     BT_VOID, BT_LONGDOUBLE, BT_LONGDOUBLE_PTR, BT_LONGDOUBLE_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_PTR_PTR_PTR, BT_VOID, BT_PTR, BT_PTR, BT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_PTR_PTR_UINT32, BT_VOID, BT_PTR, BT_PTR, BT_UINT32)
DEF_FUNCTION_TYPE_3 (BT_FN_INT_CONST_STRING_PTR_CONST_STRING_PTR_CONST_STRING,
		     BT_INT, BT_CONST_STRING, BT_PTR_CONST_STRING, BT_PTR_CONST_STRING)
DEF_FUNCTION_TYPE_3 (BT_FN_INT_INT_CONST_STRING_VALIST_ARG,
		     BT_INT, BT_INT, BT_CONST_STRING, BT_VALIST_ARG)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_VPTR_I1_I1, BT_BOOL, BT_VOLATILE_PTR,
		     BT_I1, BT_I1)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_VPTR_I2_I2, BT_BOOL, BT_VOLATILE_PTR,
		     BT_I2, BT_I2)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_VPTR_I4_I4, BT_BOOL, BT_VOLATILE_PTR,
		     BT_I4, BT_I4)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_VPTR_I8_I8, BT_BOOL, BT_VOLATILE_PTR,
		     BT_I8, BT_I8)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_VPTR_I16_I16, BT_BOOL, BT_VOLATILE_PTR,
		     BT_I16, BT_I16)
DEF_FUNCTION_TYPE_3 (BT_FN_I1_VPTR_I1_I1, BT_I1, BT_VOLATILE_PTR, BT_I1, BT_I1)
DEF_FUNCTION_TYPE_3 (BT_FN_I2_VPTR_I2_I2, BT_I2, BT_VOLATILE_PTR, BT_I2, BT_I2)
DEF_FUNCTION_TYPE_3 (BT_FN_I4_VPTR_I4_I4, BT_I4, BT_VOLATILE_PTR, BT_I4, BT_I4)
DEF_FUNCTION_TYPE_3 (BT_FN_I8_VPTR_I8_I8, BT_I8, BT_VOLATILE_PTR, BT_I8, BT_I8)
DEF_FUNCTION_TYPE_3 (BT_FN_I16_VPTR_I16_I16, BT_I16, BT_VOLATILE_PTR,
		     BT_I16, BT_I16)
DEF_FUNCTION_TYPE_3 (BT_FN_PTR_CONST_PTR_INT_SIZE, BT_PTR,
		     BT_CONST_PTR, BT_INT, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_I1_VPTR_I1_INT, BT_I1, BT_VOLATILE_PTR, BT_I1, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_I2_VPTR_I2_INT, BT_I2, BT_VOLATILE_PTR, BT_I2, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_I4_VPTR_I4_INT, BT_I4, BT_VOLATILE_PTR, BT_I4, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_I8_VPTR_I8_INT, BT_I8, BT_VOLATILE_PTR, BT_I8, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_I16_VPTR_I16_INT, BT_I16, BT_VOLATILE_PTR, BT_I16, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_VPTR_I1_INT, BT_VOID, BT_VOLATILE_PTR, BT_I1, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_VPTR_I2_INT, BT_VOID, BT_VOLATILE_PTR, BT_I2, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_VPTR_I4_INT, BT_VOID, BT_VOLATILE_PTR, BT_I4, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_VPTR_I8_INT, BT_VOID, BT_VOLATILE_PTR, BT_I8, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_VPTR_I16_INT, BT_VOID, BT_VOLATILE_PTR, BT_I16, BT_INT)
DEF_FUNCTION_TYPE_3 (BT_FN_INT_PTRPTR_SIZE_SIZE, BT_INT, BT_PTR_PTR, BT_SIZE, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_PTR_CONST_PTR_CONST_PTR_SIZE, BT_PTR, BT_CONST_PTR, BT_CONST_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_INT_INT_INTPTR, BT_BOOL, BT_INT, BT_INT,
		     BT_INT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_LONG_LONG_LONGPTR, BT_BOOL, BT_LONG, BT_LONG,
		     BT_PTR_LONG)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_LONGLONG_LONGLONG_LONGLONGPTR, BT_BOOL,
		     BT_LONGLONG, BT_LONGLONG, BT_PTR_LONGLONG)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_UINT_UINT_UINTPTR, BT_BOOL, BT_UINT, BT_UINT,
		     BT_PTR_UINT)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_ULONG_ULONG_ULONGPTR, BT_BOOL, BT_ULONG,
		     BT_ULONG, BT_PTR_ULONG)
DEF_FUNCTION_TYPE_3 (BT_FN_BOOL_ULONGLONG_ULONGLONG_ULONGLONGPTR, BT_BOOL,
		     BT_ULONGLONG, BT_ULONGLONG, BT_PTR_ULONGLONG)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_UINT32_UINT64_PTR,
		     BT_VOID, BT_UINT32, BT_UINT64, BT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_UINT32_UINT32_PTR,
		     BT_VOID, BT_UINT32, BT_UINT32, BT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_SIZE_SIZE_PTR, BT_VOID, BT_SIZE, BT_SIZE,
		     BT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT_UINT_PTR_PTR, BT_UINT, BT_UINT, BT_PTR, BT_PTR)
DEF_FUNCTION_TYPE_3 (BT_FN_PTR_PTR_CONST_SIZE_BOOL,
		     BT_PTR, BT_PTR, BT_CONST_SIZE, BT_BOOL)
DEF_FUNCTION_TYPE_3 (BT_FN_PTR_SIZE_SIZE_PTRMODE,
		     BT_PTR, BT_SIZE, BT_SIZE, BT_PTRMODE)
DEF_FUNCTION_TYPE_3 (BT_FN_VOID_PTR_UINT8_PTRMODE, BT_VOID, BT_PTR, BT_UINT8,
		     BT_PTRMODE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT8_UINT8_UINT8_CONST_SIZE, BT_UINT8, BT_UINT8,
		     BT_UINT8, BT_CONST_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT16_UINT16_UINT8_CONST_SIZE, BT_UINT16, BT_UINT16,
		     BT_UINT8, BT_CONST_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT16_UINT16_UINT16_CONST_SIZE, BT_UINT16,
		     BT_UINT16, BT_UINT16, BT_CONST_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT32_UINT32_UINT8_CONST_SIZE, BT_UINT32, BT_UINT32,
		     BT_UINT8, BT_CONST_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT32_UINT32_UINT16_CONST_SIZE, BT_UINT32,
		     BT_UINT32, BT_UINT16, BT_CONST_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT32_UINT32_UINT32_CONST_SIZE, BT_UINT32,
		     BT_UINT32, BT_UINT32, BT_CONST_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT64_UINT64_UINT8_CONST_SIZE, BT_UINT64, BT_UINT64,
		     BT_UINT8, BT_CONST_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT64_UINT64_UINT16_CONST_SIZE, BT_UINT64,
		     BT_UINT64, BT_UINT16, BT_CONST_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT64_UINT64_UINT32_CONST_SIZE, BT_UINT64,
		     BT_UINT64, BT_UINT32, BT_CONST_SIZE)
DEF_FUNCTION_TYPE_3 (BT_FN_UINT64_UINT64_UINT64_CONST_SIZE, BT_UINT64,
		     BT_UINT64, BT_UINT64, BT_CONST_SIZE)

DEF_FUNCTION_TYPE_4 (BT_FN_SIZE_CONST_PTR_SIZE_SIZE_FILEPTR,
		     BT_SIZE, BT_CONST_PTR, BT_SIZE, BT_SIZE, BT_FILEPTR)
DEF_FUNCTION_TYPE_4 (BT_FN_INT_STRING_SIZE_CONST_STRING_VALIST_ARG,
		BT_INT, BT_STRING, BT_SIZE, BT_CONST_STRING, BT_VALIST_ARG)
DEF_FUNCTION_TYPE_4 (BT_FN_SIZE_STRING_SIZE_CONST_STRING_CONST_TM_PTR,
		BT_SIZE, BT_STRING, BT_SIZE, BT_CONST_STRING, BT_CONST_TM_PTR)
DEF_FUNCTION_TYPE_4 (BT_FN_PTR_PTR_CONST_PTR_SIZE_SIZE,
		     BT_PTR, BT_PTR, BT_CONST_PTR, BT_SIZE, BT_SIZE)
DEF_FUNCTION_TYPE_4 (BT_FN_PTR_PTR_INT_SIZE_SIZE,
		     BT_PTR, BT_PTR, BT_INT, BT_SIZE, BT_SIZE)
DEF_FUNCTION_TYPE_4 (BT_FN_PTR_PTR_SIZE_PTRMODE_PTRMODE,
		     BT_PTR, BT_PTR, BT_SIZE, BT_PTRMODE, BT_PTRMODE)
DEF_FUNCTION_TYPE_4 (BT_FN_UINT_UINT_UINT_UINT_UINT,
		     BT_UINT, BT_UINT, BT_UINT, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_4 (BT_FN_UINT_UINT_UINT_UINT_UINTPTR,
		     BT_UINT, BT_UINT, BT_UINT, BT_UINT, BT_PTR_UINT)
DEF_FUNCTION_TYPE_4 (BT_FN_UINT_FLOAT_FLOAT_FLOAT_FLOAT,
		     BT_UINT, BT_FLOAT, BT_FLOAT, BT_FLOAT, BT_FLOAT)
DEF_FUNCTION_TYPE_4 (BT_FN_ULONG_ULONG_ULONG_UINT_UINT,
		     BT_ULONG, BT_ULONG, BT_ULONG, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_4 (BT_FN_ULONG_ULONG_ULONG_ULONG_ULONGPTR,
		     BT_ULONG, BT_ULONG, BT_ULONG, BT_ULONG, BT_PTR_ULONG)
DEF_FUNCTION_TYPE_4 (BT_FN_ULONGLONG_ULONGLONG_ULONGLONG_ULONGLONG_ULONGLONGPTR,
		     BT_ULONGLONG, BT_ULONGLONG, BT_ULONGLONG, BT_ULONGLONG,
		     BT_PTR_ULONGLONG)
DEF_FUNCTION_TYPE_4 (BT_FN_STRING_STRING_CONST_STRING_SIZE_SIZE,
		     BT_STRING, BT_STRING, BT_CONST_STRING, BT_SIZE, BT_SIZE)
DEF_FUNCTION_TYPE_4 (BT_FN_INT_FILEPTR_INT_CONST_STRING_VALIST_ARG,
		     BT_INT, BT_FILEPTR, BT_INT, BT_CONST_STRING, BT_VALIST_ARG)
DEF_FUNCTION_TYPE_4 (BT_FN_VOID_OMPFN_PTR_UINT_UINT,
		     BT_VOID, BT_PTR_FN_VOID_PTR, BT_PTR, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_4 (BT_FN_UINT_OMPFN_PTR_UINT_UINT,
		     BT_UINT, BT_PTR_FN_VOID_PTR, BT_PTR, BT_UINT, BT_UINT)
DEF_FUNCTION_TYPE_4 (BT_FN_VOID_PTR_WORD_WORD_PTR,
		     BT_VOID, BT_PTR, BT_WORD, BT_WORD, BT_PTR)
DEF_FUNCTION_TYPE_4 (BT_FN_VOID_SIZE_VPTR_PTR_INT, BT_VOID, BT_SIZE,
		     BT_VOLATILE_PTR, BT_PTR, BT_INT)
DEF_FUNCTION_TYPE_4 (BT_FN_VOID_SIZE_CONST_VPTR_PTR_INT, BT_VOID, BT_SIZE,
		     BT_CONST_VOLATILE_PTR, BT_PTR, BT_INT)
DEF_FUNCTION_TYPE_4 (BT_FN_BOOL_UINT_LONGPTR_LONGPTR_LONGPTR,
		     BT_BOOL, BT_UINT, BT_PTR_LONG, BT_PTR_LONG, BT_PTR_LONG)
DEF_FUNCTION_TYPE_4 (BT_FN_BOOL_UINT_ULLPTR_ULLPTR_ULLPTR,
		     BT_BOOL, BT_UINT, BT_PTR_ULONGLONG, BT_PTR_ULONGLONG,
		     BT_PTR_ULONGLONG)
DEF_FUNCTION_TYPE_4 (BT_FN_VOID_UINT_PTR_INT_PTR, BT_VOID, BT_INT, BT_PTR,
		     BT_INT, BT_PTR)
DEF_FUNCTION_TYPE_4 (BT_FN_BOOL_UINT_UINT_UINT_BOOL,
		     BT_BOOL, BT_UINT, BT_UINT, BT_UINT, BT_BOOL)

DEF_FUNCTION_TYPE_5 (BT_FN_INT_STRING_INT_SIZE_CONST_STRING_VALIST_ARG,
		     BT_INT, BT_STRING, BT_INT, BT_SIZE, BT_CONST_STRING,
		     BT_VALIST_ARG)
DEF_FUNCTION_TYPE_5 (BT_FN_BOOL_LONG_LONG_LONG_LONGPTR_LONGPTR,
		     BT_BOOL, BT_LONG, BT_LONG, BT_LONG,
		     BT_PTR_LONG, BT_PTR_LONG)
DEF_FUNCTION_TYPE_5 (BT_FN_VOID_SIZE_VPTR_PTR_PTR_INT, BT_VOID, BT_SIZE,
		     BT_VOLATILE_PTR, BT_PTR, BT_PTR, BT_INT)
DEF_FUNCTION_TYPE_5 (BT_FN_BOOL_VPTR_PTR_I1_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I1, BT_INT, BT_INT)
DEF_FUNCTION_TYPE_5 (BT_FN_BOOL_VPTR_PTR_I2_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I2, BT_INT, BT_INT)
DEF_FUNCTION_TYPE_5 (BT_FN_BOOL_VPTR_PTR_I4_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I4, BT_INT, BT_INT)
DEF_FUNCTION_TYPE_5 (BT_FN_BOOL_VPTR_PTR_I8_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I8, BT_INT, BT_INT)
DEF_FUNCTION_TYPE_5 (BT_FN_BOOL_VPTR_PTR_I16_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I16, BT_INT, BT_INT)
DEF_FUNCTION_TYPE_5 (BT_FN_VOID_INT_SIZE_PTR_PTR_PTR,
		     BT_VOID, BT_INT, BT_SIZE, BT_PTR, BT_PTR, BT_PTR)
DEF_FUNCTION_TYPE_5 (BT_FN_VOID_OMPFN_PTR_UINT_UINT_UINT,
		     BT_VOID, BT_PTR_FN_VOID_PTR, BT_PTR, BT_UINT, BT_UINT,
		     BT_UINT)
DEF_FUNCTION_TYPE_5 (BT_FN_BOOL_UINT_LONGPTR_LONG_LONGPTR_LONGPTR,
		     BT_BOOL, BT_UINT, BT_PTR_LONG, BT_LONG, BT_PTR_LONG,
		     BT_PTR_LONG)
DEF_FUNCTION_TYPE_5 (BT_FN_BOOL_UINT_ULLPTR_ULL_ULLPTR_ULLPTR,
		     BT_BOOL, BT_UINT, BT_PTR_ULONGLONG, BT_ULONGLONG,
		     BT_PTR_ULONGLONG, BT_PTR_ULONGLONG)

DEF_FUNCTION_TYPE_6 (BT_FN_INT_STRING_SIZE_INT_SIZE_CONST_STRING_VALIST_ARG,
		     BT_INT, BT_STRING, BT_SIZE, BT_INT, BT_SIZE,
		     BT_CONST_STRING, BT_VALIST_ARG)
DEF_FUNCTION_TYPE_6 (BT_FN_BOOL_LONG_LONG_LONG_LONG_LONGPTR_LONGPTR,
		     BT_BOOL, BT_LONG, BT_LONG, BT_LONG, BT_LONG,
		     BT_PTR_LONG, BT_PTR_LONG)
DEF_FUNCTION_TYPE_6 (BT_FN_BOOL_BOOL_ULL_ULL_ULL_ULLPTR_ULLPTR,
		     BT_BOOL, BT_BOOL, BT_ULONGLONG, BT_ULONGLONG,
		     BT_ULONGLONG, BT_PTR_ULONGLONG, BT_PTR_ULONGLONG)
DEF_FUNCTION_TYPE_6 (BT_FN_BOOL_VPTR_PTR_I1_BOOL_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I1, BT_BOOL, BT_INT,
		     BT_INT)
DEF_FUNCTION_TYPE_6 (BT_FN_BOOL_VPTR_PTR_I2_BOOL_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I2, BT_BOOL, BT_INT,
		     BT_INT)
DEF_FUNCTION_TYPE_6 (BT_FN_BOOL_VPTR_PTR_I4_BOOL_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I4, BT_BOOL, BT_INT,
		     BT_INT)
DEF_FUNCTION_TYPE_6 (BT_FN_BOOL_VPTR_PTR_I8_BOOL_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I8, BT_BOOL, BT_INT,
		     BT_INT)
DEF_FUNCTION_TYPE_6 (BT_FN_BOOL_VPTR_PTR_I16_BOOL_INT_INT,
		     BT_BOOL, BT_VOLATILE_PTR, BT_PTR, BT_I16, BT_BOOL, BT_INT,
		     BT_INT)
DEF_FUNCTION_TYPE_6 (BT_FN_BOOL_SIZE_VPTR_PTR_PTR_INT_INT, BT_BOOL, BT_SIZE,
		     BT_VOLATILE_PTR, BT_PTR, BT_PTR, BT_INT, BT_INT)

DEF_FUNCTION_TYPE_7 (BT_FN_VOID_OMPFN_PTR_UINT_LONG_LONG_LONG_UINT,
		     BT_VOID, BT_PTR_FN_VOID_PTR, BT_PTR, BT_UINT,
		     BT_LONG, BT_LONG, BT_LONG, BT_UINT)
DEF_FUNCTION_TYPE_7 (BT_FN_BOOL_BOOL_ULL_ULL_ULL_ULL_ULLPTR_ULLPTR,
		     BT_BOOL, BT_BOOL, BT_ULONGLONG, BT_ULONGLONG,
		     BT_ULONGLONG, BT_ULONGLONG,
		     BT_PTR_ULONGLONG, BT_PTR_ULONGLONG)
DEF_FUNCTION_TYPE_7 (BT_FN_VOID_INT_SIZE_PTR_PTR_PTR_UINT_PTR,
		     BT_VOID, BT_INT, BT_SIZE, BT_PTR, BT_PTR, BT_PTR, BT_UINT,
		     BT_PTR)

DEF_FUNCTION_TYPE_8 (BT_FN_VOID_OMPFN_PTR_UINT_LONG_LONG_LONG_LONG_UINT,
		     BT_VOID, BT_PTR_FN_VOID_PTR, BT_PTR, BT_UINT,
		     BT_LONG, BT_LONG, BT_LONG, BT_LONG, BT_UINT)
DEF_FUNCTION_TYPE_8 (BT_FN_BOOL_UINT_LONGPTR_LONG_LONG_LONGPTR_LONGPTR_PTR_PTR,
		     BT_BOOL, BT_UINT, BT_PTR_LONG, BT_LONG, BT_LONG,
		     BT_PTR_LONG, BT_PTR_LONG, BT_PTR, BT_PTR)
DEF_FUNCTION_TYPE_8 (BT_FN_BOOL_UINT_ULLPTR_LONG_ULL_ULLPTR_ULLPTR_PTR_PTR,
		     BT_BOOL, BT_UINT, BT_PTR_ULONGLONG, BT_LONG, BT_ULONGLONG,
		     BT_PTR_ULONGLONG, BT_PTR_ULONGLONG, BT_PTR, BT_PTR)

DEF_FUNCTION_TYPE_9 (BT_FN_VOID_INT_OMPFN_SIZE_PTR_PTR_PTR_UINT_PTR_PTR,
		     BT_VOID, BT_INT, BT_PTR_FN_VOID_PTR, BT_SIZE, BT_PTR,
		     BT_PTR, BT_PTR, BT_UINT, BT_PTR, BT_PTR)
DEF_FUNCTION_TYPE_9 (BT_FN_BOOL_LONG_LONG_LONG_LONG_LONG_LONGPTR_LONGPTR_PTR_PTR,
		     BT_BOOL, BT_LONG, BT_LONG, BT_LONG, BT_LONG, BT_LONG,
		     BT_PTR_LONG, BT_PTR_LONG, BT_PTR, BT_PTR)

DEF_FUNCTION_TYPE_10 (BT_FN_VOID_OMPFN_PTR_OMPCPYFN_LONG_LONG_BOOL_UINT_PTR_INT_PTR,
		      BT_VOID, BT_PTR_FN_VOID_PTR, BT_PTR,
		      BT_PTR_FN_VOID_PTR_PTR, BT_LONG, BT_LONG,
		      BT_BOOL, BT_UINT, BT_PTR, BT_INT, BT_PTR)
DEF_FUNCTION_TYPE_10 (BT_FN_BOOL_BOOL_ULL_ULL_ULL_LONG_ULL_ULLPTR_ULLPTR_PTR_PTR,
		      BT_BOOL, BT_BOOL, BT_ULONGLONG, BT_ULONGLONG,
		      BT_ULONGLONG, BT_LONG, BT_ULONGLONG, BT_PTR_ULONGLONG,
		      BT_PTR_ULONGLONG, BT_PTR, BT_PTR)

DEF_FUNCTION_TYPE_11 (BT_FN_VOID_OMPFN_PTR_OMPCPYFN_LONG_LONG_UINT_LONG_INT_LONG_LONG_LONG,
		      BT_VOID, BT_PTR_FN_VOID_PTR, BT_PTR,
		      BT_PTR_FN_VOID_PTR_PTR, BT_LONG, BT_LONG,
		      BT_UINT, BT_LONG, BT_INT, BT_LONG, BT_LONG, BT_LONG)
DEF_FUNCTION_TYPE_11 (BT_FN_VOID_OMPFN_PTR_OMPCPYFN_LONG_LONG_UINT_LONG_INT_ULL_ULL_ULL,
		      BT_VOID, BT_PTR_FN_VOID_PTR, BT_PTR,
		      BT_PTR_FN_VOID_PTR_PTR, BT_LONG, BT_LONG,
		      BT_UINT, BT_LONG, BT_INT,
		      BT_ULONGLONG, BT_ULONGLONG, BT_ULONGLONG)
DEF_FUNCTION_TYPE_11 (BT_FN_VOID_INT_INT_PTR_PTR_PTR_INT_PTR_INT_PTR_UINT_PTR,
		      BT_VOID, BT_INT, BT_INT, BT_PTR, BT_PTR, BT_PTR, BT_INT,
		      BT_PTR, BT_INT, BT_PTR, BT_UINT, BT_PTR)

DEF_FUNCTION_TYPE_VAR_0 (BT_FN_VOID_VAR, BT_VOID)
DEF_FUNCTION_TYPE_VAR_0 (BT_FN_INT_VAR, BT_INT)
DEF_FUNCTION_TYPE_VAR_0 (BT_FN_PTR_VAR, BT_PTR)
DEF_FUNCTION_TYPE_VAR_0 (BT_FN_BOOL_VAR, BT_BOOL)

DEF_FUNCTION_TYPE_VAR_1 (BT_FN_VOID_VALIST_REF_VAR,
			 BT_VOID, BT_VALIST_REF)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_VOID_CONST_PTR_VAR,
			 BT_VOID, BT_CONST_PTR)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_INT_CONST_STRING_VAR,
			 BT_INT, BT_CONST_STRING)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_UINT32_UINT32_VAR,
			 BT_UINT32, BT_UINT32)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_VOID_LONG_VAR,
			 BT_VOID, BT_LONG)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_VOID_ULL_VAR,
			 BT_VOID, BT_ULONGLONG)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_PTR_PTR_VAR, BT_PTR, BT_PTR)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_I1_I1_VAR, BT_I1, BT_I1)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_I2_I2_VAR, BT_I2, BT_I2)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_I4_I4_VAR, BT_I4, BT_I4)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_I8_I8_VAR, BT_I8, BT_I8)
DEF_FUNCTION_TYPE_VAR_1 (BT_FN_I16_I16_VAR, BT_I16, BT_I16)

DEF_FUNCTION_TYPE_VAR_2 (BT_FN_INT_FILEPTR_CONST_STRING_VAR,
			 BT_INT, BT_FILEPTR, BT_CONST_STRING)
DEF_FUNCTION_TYPE_VAR_2 (BT_FN_INT_STRING_CONST_STRING_VAR,
			 BT_INT, BT_STRING, BT_CONST_STRING)
DEF_FUNCTION_TYPE_VAR_2 (BT_FN_INT_CONST_STRING_CONST_STRING_VAR,
			 BT_INT, BT_CONST_STRING, BT_CONST_STRING)
DEF_FUNCTION_TYPE_VAR_2 (BT_FN_INT_INT_CONST_STRING_VAR,
			 BT_INT, BT_INT, BT_CONST_STRING)
DEF_FUNCTION_TYPE_VAR_2 (BT_FN_PTR_CONST_PTR_SIZE_VAR, BT_PTR,
			 BT_CONST_PTR, BT_SIZE)
DEF_FUNCTION_TYPE_VAR_2 (BT_FN_VOID_INT_INT_VAR, BT_VOID,
			 BT_INT, BT_INT)

DEF_FUNCTION_TYPE_VAR_3 (BT_FN_INT_STRING_SIZE_CONST_STRING_VAR,
			 BT_INT, BT_STRING, BT_SIZE, BT_CONST_STRING)
DEF_FUNCTION_TYPE_VAR_3 (BT_FN_SSIZE_STRING_SIZE_CONST_STRING_VAR,
			 BT_SSIZE, BT_STRING, BT_SIZE, BT_CONST_STRING)
DEF_FUNCTION_TYPE_VAR_3 (BT_FN_INT_FILEPTR_INT_CONST_STRING_VAR,
			 BT_INT, BT_FILEPTR, BT_INT, BT_CONST_STRING)

DEF_FUNCTION_TYPE_VAR_4 (BT_FN_INT_STRING_INT_SIZE_CONST_STRING_VAR,
			 BT_INT, BT_STRING, BT_INT, BT_SIZE, BT_CONST_STRING)

DEF_FUNCTION_TYPE_VAR_5 (BT_FN_INT_STRING_SIZE_INT_SIZE_CONST_STRING_VAR,
			 BT_INT, BT_STRING, BT_SIZE, BT_INT, BT_SIZE,
			 BT_CONST_STRING)

DEF_FUNCTION_TYPE_VAR_5 (BT_FN_INT_INT_INT_INT_INT_INT_VAR,
			 BT_INT, BT_INT, BT_INT, BT_INT, BT_INT, BT_INT)

DEF_FUNCTION_TYPE_VAR_6 (BT_FN_VOID_INT_OMPFN_SIZE_PTR_PTR_PTR_VAR,
			 BT_VOID, BT_INT, BT_PTR_FN_VOID_PTR, BT_SIZE,
			 BT_PTR, BT_PTR, BT_PTR)

DEF_FUNCTION_TYPE_VAR_7 (BT_FN_VOID_INT_SIZE_PTR_PTR_PTR_INT_INT_VAR,
			 BT_VOID, BT_INT, BT_SIZE, BT_PTR, BT_PTR,
			 BT_PTR, BT_INT, BT_INT)

DEF_POINTER_TYPE (BT_PTR_FN_VOID_VAR, BT_FN_VOID_VAR)
DEF_FUNCTION_TYPE_3 (BT_FN_PTR_PTR_FN_VOID_VAR_PTR_SIZE,
		     BT_PTR, BT_PTR_FN_VOID_VAR, BT_PTR, BT_SIZE)


DEF_FUNCTION_TYPE_1 (BT_FN_I1_VPTR, BT_I1, BT_VOLATILE_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_I2_VPTR, BT_I2, BT_VOLATILE_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_I4_VPTR, BT_I4, BT_VOLATILE_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_I8_VPTR, BT_I8, BT_VOLATILE_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_FLOAT_VPTR, BT_FLOAT, BT_VOLATILE_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_DOUBLE_CONST_DOUBLE_PTR, BT_DOUBLE, BT_DOUBLE_PTR)
DEF_FUNCTION_TYPE_1 (BT_FN_LDOUBLE_VPTR, BT_LONGDOUBLE, BT_VOLATILE_PTR)

DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VPTR_I1, BT_VOID, BT_VOLATILE_PTR, BT_I1)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VPTR_I2, BT_VOID, BT_VOLATILE_PTR, BT_I2)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VPTR_I4, BT_VOID, BT_VOLATILE_PTR, BT_I4)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VPTR_I8, BT_VOID, BT_VOLATILE_PTR, BT_I8)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VPTR_FLOAT, BT_VOID, BT_VOLATILE_PTR, BT_FLOAT)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VPTR_DOUBLE, BT_VOID,
		     BT_VOLATILE_PTR, BT_DOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VPTR_LDOUBLE, BT_VOID,
		     BT_VOLATILE_PTR, BT_LONGDOUBLE)
DEF_FUNCTION_TYPE_2 (BT_FN_VOID_VPTR_SIZE, BT_VOID,
		     BT_VOLATILE_PTR, BT_SIZE)
