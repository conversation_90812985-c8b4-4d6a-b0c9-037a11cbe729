.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_import_rsa_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_import_rsa_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_import_rsa_raw(gnutls_pubkey_t " key ", const gnutls_datum_t * " m ", const gnutls_datum_t * " e ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
The key
.IP "const gnutls_datum_t * m" 12
holds the modulus
.IP "const gnutls_datum_t * e" 12
holds the public exponent
.SH "DESCRIPTION"
This function will replace the parameters in the given structure.
The new parameters should be stored in the appropriate
gnutls_datum.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an negative error code.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
