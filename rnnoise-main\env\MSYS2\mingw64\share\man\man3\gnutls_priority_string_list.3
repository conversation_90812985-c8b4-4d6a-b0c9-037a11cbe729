.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_priority_string_list" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_priority_string_list \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_priority_string_list(unsigned " iter ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "unsigned iter" 12
an integer counter starting from zero
.IP "unsigned int flags" 12
one of \fBGNUTLS_PRIORITY_LIST_INIT_KEYWORDS\fP, \fBGNUTLS_PRIORITY_LIST_SPECIAL\fP
.SH "DESCRIPTION"
Can be used to iterate all available priority strings.
Due to internal implementation details, there are cases where this
function can return the empty string. In that case that string should be ignored.
When no strings are available it returns \fBNULL\fP.
.SH "RETURNS"
a priority string
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
