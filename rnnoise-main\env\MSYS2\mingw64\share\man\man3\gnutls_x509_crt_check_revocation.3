.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_check_revocation" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_check_revocation \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_check_revocation(gnutls_x509_crt_t " cert ", const gnutls_x509_crl_t * " crl_list ", unsigned " crl_list_length ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "const gnutls_x509_crl_t * crl_list" 12
should contain a list of gnutls_x509_crl_t types
.IP "unsigned crl_list_length" 12
the length of the crl_list
.SH "DESCRIPTION"
This function will check if the given certificate is
revoked.  It is assumed that the CRLs have been verified before.
.SH "RETURNS"
0 if the certificate is NOT revoked, and 1 if it is.  A
negative error code is returned on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
