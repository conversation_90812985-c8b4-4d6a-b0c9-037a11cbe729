.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_retrieve_function3" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_retrieve_function3 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "void gnutls_certificate_set_retrieve_function3(gnutls_certificate_credentials_t " cred ", gnutls_certificate_retrieve_function3 * " func ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t cred" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "gnutls_certificate_retrieve_function3 * func" 12
is the callback function
.SH "DESCRIPTION"
This function sets a callback to be called in order to retrieve the
certificate and OCSP responses to be used in the handshake.  \fIfunc\fP will
be called only if the peer requests a certificate either during handshake
or during post\-handshake authentication.

The callback's function prototype is defined in `abstract.h':

int gnutls_certificate_retrieve_function3(
gnutls_session_t,
const struct gnutls_cert_retr_st *info,
gnutls_pcert_st **certs,
unsigned int *certs_length,
gnutls_ocsp_data_st **ocsp,
unsigned int *ocsp_length,
gnutls_privkey_t *privkey,
unsigned int *flags);

The info field of the callback contains:
 \fIreq_ca_dn\fP which is a list with the CA names that the server considers trusted.
This is a hint and typically the client should send a certificate that is signed
by one of these CAs. These names, when available, are DER encoded. To get a more
meaningful value use the function \fBgnutls_x509_rdn_get()\fP.
 \fIpk_algos\fP contains a list with server's acceptable public key algorithms.
The certificate returned should support the server's given algorithms.

The callback should fill\-in the following values:

 \fIcerts\fP should contain an allocated list of certificates and public keys.
 \fIcerts_length\fP is the size of the previous list.
 \fIocsp\fP should contain an allocated list of OCSP responses.
 \fIocsp_length\fP is the size of the previous list.
 \fIprivkey\fP is the private key.

If flags in the callback are set to \fBGNUTLS_CERT_RETR_DEINIT_ALL\fP then
all provided values must be allocated using \fBgnutls_malloc()\fP, and will
be released by gnutls; otherwise they will not be touched by gnutls.

The callback function should set the certificate and OCSP response
list to be sent, and return 0 on success. If no certificates are available,
the  \fIcerts_length\fP and  \fIocsp_length\fP should be set to zero. The return
value (\-1) indicates error and the handshake will be terminated. If both
certificates are set in the credentials and a callback is available, the
callback takes predence.

Raw public\-keys:
In case raw public\-keys are negotiated as certificate type, certificates
that would normally hold the public\-key material are not available. In that case,
 \fIcerts\fP contains an allocated list with only the public key. Since there is no
certificate, there is also no certificate status. Therefore, OCSP information
should not be set.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
