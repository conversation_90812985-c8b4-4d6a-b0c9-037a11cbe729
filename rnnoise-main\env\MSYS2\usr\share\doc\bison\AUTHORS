Authors of GNU Bison.

Bison was written primarily by <PERSON>.

<PERSON> made it Yacc-compatible.

<PERSON><PERSON><PERSON> of Carnegie Mellon University added multicharacter
string literals and other features (Bison 1.25, 1995).

<PERSON><PERSON> rewrote the parser in Bison, and changed the back end to
use M4 (1.50, 2002).

<PERSON> added GLR support (Bison 1.50, 2002).

<PERSON> contributed canonical-LR support, and invented and added
IELR and LAC (Lookahead Correction) support (Bison 2.5, 2011).

<PERSON> contributed Java support (Bison 2.4, 2008).

<PERSON> added named reference support (Bison 2.5, 2011).

<PERSON> fixed a million portability issues, arbitrary limitations,
and nasty bugs.

-----

Copyright (C) 1998-2015, 2018-2021 Free Software Foundation, Inc.

This file is part of Bison, the GNU Compiler Compiler.

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <https://www.gnu.org/licenses/>.
