.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_export" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_export \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_export(gnutls_x509_crq_t " crq ", gnutls_x509_crt_fmt_t " format ", void * " output_data ", size_t * " output_data_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "gnutls_x509_crt_fmt_t format" 12
the format of output params. One of PEM or DER.
.IP "void * output_data" 12
will contain a certificate request PEM or DER encoded
.IP "size_t * output_data_size" 12
holds the size of output_data (and will be
replaced by the actual size of parameters)
.SH "DESCRIPTION"
This function will export the certificate request to a PEM or DER
encoded PKCS10 structure.

If the buffer provided is not long enough to hold the output, then
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP will be returned and
* \fIoutput_data_size\fP will be updated.

If the structure is PEM encoded, it will have a header of "BEGIN
NEW CERTIFICATE REQUEST".
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
