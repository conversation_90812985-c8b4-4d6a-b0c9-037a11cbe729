------------------------------------------------------------------------
-- scaleb.decTest -- scale a number by powers of 10                   --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 999
minExponent: -999

-- Max |rhs| is 2*(999+9) = 2016

-- Sanity checks
scbx001 scaleb       7.50   10 -> 7.50E+10
scbx002 scaleb       7.50    3 -> 7.50E+3
scbx003 scaleb       7.50    2 -> 750
scbx004 scaleb       7.50    1 -> 75.0
scbx005 scaleb       7.50    0 -> 7.50
scbx006 scaleb       7.50   -1 -> 0.750
scbx007 scaleb       7.50   -2 -> 0.0750
scbx008 scaleb       7.50  -10 -> 7.50E-10
scbx009 scaleb      -7.50    3 -> -7.50E+3
scbx010 scaleb      -7.50    2 -> -750
scbx011 scaleb      -7.50    1 -> -75.0
scbx012 scaleb      -7.50    0 -> -7.50
scbx013 scaleb      -7.50   -1 -> -0.750

-- Infinities
scbx014 scaleb  Infinity   1 -> Infinity
scbx015 scaleb  -Infinity  2 -> -Infinity
scbx016 scaleb  Infinity  -1 -> Infinity
scbx017 scaleb  -Infinity -2 -> -Infinity

-- Next two are somewhat undefined in 754r; treat as non-integer
scbx018 scaleb  10  Infinity -> NaN Invalid_operation
scbx019 scaleb  10 -Infinity -> NaN Invalid_operation

-- NaNs are undefined in 754r; assume usual processing
-- NaNs, 0 payload
scbx021 scaleb         NaN  1 -> NaN
scbx022 scaleb        -NaN -1 -> -NaN
scbx023 scaleb        sNaN  1 -> NaN Invalid_operation
scbx024 scaleb       -sNaN  1 -> -NaN Invalid_operation
scbx025 scaleb    4    NaN    -> NaN
scbx026 scaleb -Inf   -NaN    -> -NaN
scbx027 scaleb    4   sNaN    -> NaN Invalid_operation
scbx028 scaleb  Inf  -sNaN    -> -NaN Invalid_operation

-- non-integer RHS
scbx030 scaleb  1.23    1    ->  12.3
scbx031 scaleb  1.23    1.00 ->  NaN Invalid_operation
scbx032 scaleb  1.23    1.1  ->  NaN Invalid_operation
scbx033 scaleb  1.23    1.01 ->  NaN Invalid_operation
scbx034 scaleb  1.23    0.01 ->  NaN Invalid_operation
scbx035 scaleb  1.23    0.11 ->  NaN Invalid_operation
scbx036 scaleb  1.23    0.999999999 ->  NaN Invalid_operation
scbx037 scaleb  1.23   -1    ->  0.123
scbx038 scaleb  1.23   -1.00 ->  NaN Invalid_operation
scbx039 scaleb  1.23   -1.1  ->  NaN Invalid_operation
scbx040 scaleb  1.23   -1.01 ->  NaN Invalid_operation
scbx041 scaleb  1.23   -0.01 ->  NaN Invalid_operation
scbx042 scaleb  1.23   -0.11 ->  NaN Invalid_operation
scbx043 scaleb  1.23   -0.999999999 ->  NaN Invalid_operation
scbx044 scaleb  1.23    0.1         ->  NaN Invalid_operation
scbx045 scaleb  1.23    1E+1        ->  NaN Invalid_operation
scbx046 scaleb  1.23    1.1234E+6   ->  NaN Invalid_operation
scbx047 scaleb  1.23    1.123E+4    ->  NaN Invalid_operation


scbx120 scaleb  1.23    2015        ->  Infinity Overflow Inexact Rounded
scbx121 scaleb  1.23    2016        ->  Infinity Overflow Inexact Rounded
scbx122 scaleb  1.23    2017        ->  NaN Invalid_operation
scbx123 scaleb  1.23    2018        ->  NaN Invalid_operation
scbx124 scaleb  1.23   -2015        ->  0E-1007 Underflow Subnormal Inexact Rounded Clamped
scbx125 scaleb  1.23   -2016        ->  0E-1007 Underflow Subnormal Inexact Rounded Clamped
scbx126 scaleb  1.23   -2017        ->  NaN Invalid_operation
scbx127 scaleb  1.23   -2018        ->  NaN Invalid_operation

-- NaNs, non-0 payload
-- propagating NaNs
scbx861 scaleb  NaN01   -Inf     ->  NaN1
scbx862 scaleb -NaN02   -1000    -> -NaN2
scbx863 scaleb  NaN03    1000    ->  NaN3
scbx864 scaleb  NaN04    Inf     ->  NaN4
scbx865 scaleb  NaN05    NaN61   ->  NaN5
scbx866 scaleb -Inf     -NaN71   -> -NaN71
scbx867 scaleb -1000     NaN81   ->  NaN81
scbx868 scaleb  1000     NaN91   ->  NaN91
scbx869 scaleb  Inf      NaN101  ->  NaN101
scbx871 scaleb  sNaN011  -Inf    ->  NaN11  Invalid_operation
scbx872 scaleb  sNaN012  -1000   ->  NaN12  Invalid_operation
scbx873 scaleb -sNaN013   1000   -> -NaN13  Invalid_operation
scbx874 scaleb  sNaN014   NaN171 ->  NaN14  Invalid_operation
scbx875 scaleb  sNaN015  sNaN181 ->  NaN15  Invalid_operation
scbx876 scaleb  NaN016   sNaN191 ->  NaN191 Invalid_operation
scbx877 scaleb -Inf      sNaN201 ->  NaN201 Invalid_operation
scbx878 scaleb -1000     sNaN211 ->  NaN211 Invalid_operation
scbx879 scaleb  1000    -sNaN221 -> -NaN221 Invalid_operation
scbx880 scaleb  Inf      sNaN231 ->  NaN231 Invalid_operation
scbx881 scaleb  NaN025   sNaN241 ->  NaN241 Invalid_operation

-- finites
scbx051 scaleb          7   -2  -> 0.07
scbx052 scaleb         -7   -2  -> -0.07
scbx053 scaleb         75   -2  -> 0.75
scbx054 scaleb        -75   -2  -> -0.75
scbx055 scaleb       7.50   -2  -> 0.0750
scbx056 scaleb      -7.50   -2  -> -0.0750
scbx057 scaleb       7.500  -2  -> 0.07500
scbx058 scaleb      -7.500  -2  -> -0.07500
scbx061 scaleb          7   -1  -> 0.7
scbx062 scaleb         -7   -1  -> -0.7
scbx063 scaleb         75   -1  -> 7.5
scbx064 scaleb        -75   -1  -> -7.5
scbx065 scaleb       7.50   -1  -> 0.750
scbx066 scaleb      -7.50   -1  -> -0.750
scbx067 scaleb       7.500  -1  -> 0.7500
scbx068 scaleb      -7.500  -1  -> -0.7500
scbx071 scaleb          7    0  -> 7
scbx072 scaleb         -7    0  -> -7
scbx073 scaleb         75    0  -> 75
scbx074 scaleb        -75    0  -> -75
scbx075 scaleb       7.50    0  -> 7.50
scbx076 scaleb      -7.50    0  -> -7.50
scbx077 scaleb       7.500   0  -> 7.500
scbx078 scaleb      -7.500   0  -> -7.500
scbx081 scaleb          7    1  -> 7E+1
scbx082 scaleb         -7    1  -> -7E+1
scbx083 scaleb         75    1  -> 7.5E+2
scbx084 scaleb        -75    1  -> -7.5E+2
scbx085 scaleb       7.50    1  -> 75.0
scbx086 scaleb      -7.50    1  -> -75.0
scbx087 scaleb       7.500   1  -> 75.00
scbx088 scaleb      -7.500   1  -> -75.00
scbx091 scaleb          7    2  -> 7E+2
scbx092 scaleb         -7    2  -> -7E+2
scbx093 scaleb         75    2  -> 7.5E+3
scbx094 scaleb        -75    2  -> -7.5E+3
scbx095 scaleb       7.50    2  -> 750
scbx096 scaleb      -7.50    2  -> -750
scbx097 scaleb       7.500   2  -> 750.0
scbx098 scaleb      -7.500   2  -> -750.0

-- zeros
scbx111 scaleb          0  1 -> 0E+1
scbx112 scaleb         -0  2 -> -0E+2
scbx113 scaleb       0E+4  3 -> 0E+7
scbx114 scaleb      -0E+4  4 -> -0E+8
scbx115 scaleb     0.0000  5 -> 0E+1
scbx116 scaleb    -0.0000  6 -> -0E+2
scbx117 scaleb      0E-141 7 -> 0E-134
scbx118 scaleb     -0E-141 8 -> -0E-133

-- Nmax, Nmin, Ntiny
scbx132 scaleb  9.99999999E+999 +999 -> Infinity    Overflow Inexact Rounded
scbx133 scaleb  9.99999999E+999  +10 -> Infinity     Overflow Inexact Rounded
scbx134 scaleb  9.99999999E+999  +1  -> Infinity     Overflow Inexact Rounded
scbx135 scaleb  9.99999999E+999   0  -> 9.99999999E+999
scbx136 scaleb  9.99999999E+999  -1  -> 9.99999999E+998
scbx137 scaleb  1E-999           +1  -> 1E-998
scbx138 scaleb  1E-999           -0  -> 1E-999
scbx139 scaleb  1E-999           -1  -> 1E-1000         Subnormal
scbx140 scaleb  1.00000000E-999  +1  -> 1.00000000E-998
scbx141 scaleb  1.00000000E-999   0  -> 1.00000000E-999
scbx142 scaleb  1.00000000E-999  -1  -> 1.0000000E-1000 Subnormal Rounded
scbx143 scaleb  1E-1007          +1  -> 1E-1006         Subnormal
scbx144 scaleb  1E-1007          -0  -> 1E-1007         Subnormal
scbx145 scaleb  1E-1007          -1  -> 0E-1007         Underflow Subnormal Inexact Rounded Clamped

scbx150 scaleb  -1E-1007         +1  -> -1E-1006        Subnormal
scbx151 scaleb  -1E-1007         -0  -> -1E-1007        Subnormal
scbx152 scaleb  -1E-1007         -1  -> -0E-1007        Underflow Subnormal Inexact Rounded Clamped
scbx153 scaleb  -1.00000000E-999 +1  -> -1.00000000E-998
scbx154 scaleb  -1.00000000E-999 +0  -> -1.00000000E-999
scbx155 scaleb  -1.00000000E-999 -1  -> -1.0000000E-1000 Subnormal Rounded
scbx156 scaleb  -1E-999          +1  -> -1E-998
scbx157 scaleb  -1E-999          -0  -> -1E-999
scbx158 scaleb  -1E-999          -1  -> -1E-1000         Subnormal
scbx159 scaleb  -9.99999999E+999 +1  -> -Infinity        Overflow Inexact Rounded
scbx160 scaleb  -9.99999999E+999 +0  -> -9.99999999E+999
scbx161 scaleb  -9.99999999E+999 -1  -> -9.99999999E+998
scbx162 scaleb  -9E+999          +1  -> -Infinity        Overflow Inexact Rounded
scbx163 scaleb  -1E+999          +1  -> -Infinity        Overflow Inexact Rounded

-- Krah examples
precision:   34
maxExponent: 999999999
minExponent: -999999999
-- integer overflow in 3.61 or earlier
scbx164 scaleb  1E-999999999  -1200000000  -> NaN Invalid_operation
-- out of range
scbx165 scaleb  -1E-999999999  +1200000000  -> NaN Invalid_operation
