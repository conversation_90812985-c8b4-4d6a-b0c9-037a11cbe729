.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_list_import2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_list_import2 \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_list_import2(gnutls_ocsp_resp_t ** " ocsps ", unsigned int * " size ", const gnutls_datum_t * " resp_data ", gnutls_x509_crt_fmt_t " format ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_t ** ocsps" 12
Will hold the parsed OCSP response list.
.IP "unsigned int * size" 12
It will contain the size of the list.
.IP "const gnutls_datum_t * resp_data" 12
The PEM encoded OCSP list.
.IP "gnutls_x509_crt_fmt_t format" 12
One of \fBGNUTLS_X509_FMT_PEM\fP or \fBGNUTLS_X509_FMT_DER\fP
.IP "unsigned int flags" 12
must be (0) or an OR'd sequence of gnutls_certificate_import_flags.
.SH "DESCRIPTION"
This function will convert the given PEM encoded OCSP response list
to the native gnutls_ocsp_resp_t format. The output will be stored
in  \fIocsps\fP which will be allocated and initialized.

The OCSP responses should have a header of "OCSP RESPONSE".

To deinitialize responses, you need to deinitialize each \fBgnutls_ocsp_resp_t\fP
structure independently, and use \fBgnutls_free()\fP at  \fIocsps\fP .

In PEM files, when no OCSP responses are detected
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be returned.
.SH "RETURNS"
the number of responses read or a negative error value.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
