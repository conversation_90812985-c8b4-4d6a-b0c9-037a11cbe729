/*
 * Copyright (C) 2023 Mohamad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.graphics.directx.idl";

namespace Windows.Graphics.DirectX.Direct3D11 {
    typedef struct Direct3DMultisampleDescription Direct3DMultisampleDescription;
    typedef struct Direct3DSurfaceDescription Direct3DSurfaceDescription;

    interface IDirect3DDevice;
    interface IDirect3DSurface;

    declare {
        interface Windows.Foundation.Collections.IIterable<Windows.Graphics.DirectX.Direct3D11.IDirect3DSurface *>;
        interface Windows.Foundation.Collections.IIterator<Windows.Graphics.DirectX.Direct3D11.IDirect3DSurface *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.Graphics.DirectX.Direct3D11.IDirect3DSurface *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    struct Direct3DMultisampleDescription
    {
        INT32 Count;
        INT32 Quality;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    struct Direct3DSurfaceDescription
    {
        INT32 Width;
        INT32 Height;
        Windows.Graphics.DirectX.DirectXPixelFormat Format;
        Windows.Graphics.DirectX.Direct3D11.Direct3DMultisampleDescription MultisampleDescription;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(a37624ab-8d5f-4650-9d3e-9eae3d9bc670)
    ]
    interface IDirect3DDevice : IInspectable
        requires Windows.Foundation.IClosable
    {
        HRESULT Trim();
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(0bf4a146-13c1-4694-bee3-7abf15eaf586)
    ]
    interface IDirect3DSurface : IInspectable
        requires Windows.Foundation.IClosable
    {
        [propget] HRESULT Description(
            [out, retval] Windows.Graphics.DirectX.Direct3D11.Direct3DSurfaceDescription *value);
    }
}
