/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef PORTABLEDEVICE_H
#define PORTABLEDEVICE_H

#include <propkeydef.h>

DEFINE_GUID(GUID_DEVINTERFACE_WPD, 0x6ac27878, 0xa6fa, 0x4155, 0xba, 0x85, 0xf9, 0x8f, 0x49, 0x1d, 0x4f, 0x33);
DEFINE_GUID(GUID_DEVINTERFACE_WPD_PRIVATE, 0xba0c718f, 0x4ded, 0x49b7, 0xbd, 0xd3, 0xfa, 0xbe, 0x28, 0x66, 0x12, 0x11);
DEFINE_GUID(GUID_DEVINTERFACE_WPD_SERVICE, 0x9ef44f80, 0x3d64, 0x4246, 0xa6, 0xaa, 0x20, 0x6f, 0x32, 0x8d, 0x1e, 0xdc);

#define WPD_CONTROL_FUNCTION_GENERIC_MESSAGE 0x42
#define IOCTL_WPD_MESSAGE_READWRITE_ACCESS CTL_CODE(FILE_DEVICE_WPD, WPD_CONTROL_FUNCTION_GENERIC_MESSAGE, METHOD_BUFFERED, (FILE_READ_ACCESS | FILE_WRITE_ACCESS))
#define IOCTL_WPD_MESSAGE_READ_ACCESS CTL_CODE(FILE_DEVICE_WPD, WPD_CONTROL_FUNCTION_GENERIC_MESSAGE, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IS_WPD_IOCTL(ControlCode) ((ControlCode == IOCTL_WPD_MESSAGE_READWRITE_ACCESS) || (ControlCode == IOCTL_WPD_MESSAGE_READ_ACCESS))

#define WPD_DEVICE_OBJECT_ID L"DEVICE"

#define WMDRMDEVICEAPP_USE_WPD_DEVICE_PTR ((ULONG_PTR)-1)

#define PORTABLE_DEVICE_TYPE L"PortableDeviceType"
#define PORTABLE_DEVICE_ICON L"Icons"
#define PORTABLE_DEVICE_NAMESPACE_TIMEOUT L"PortableDeviceNameSpaceTimeout"
#define PORTABLE_DEVICE_NAMESPACE_EXCLUDE_FROM_SHELL L"PortableDeviceNameSpaceExcludeFromShell"
#define PORTABLE_DEVICE_NAMESPACE_THUMBNAIL_CONTENT_TYPES L"PortableDeviceNameSpaceThumbnailContentTypes"
#define PORTABLE_DEVICE_IS_MASS_STORAGE L"PortableDeviceIsMassStorage"
#define PORTABLE_DEVICE_DRM_SCHEME_WMDRM10_PD L"WMDRM10-PD"
#define PORTABLE_DEVICE_DRM_SCHEME_PDDRM L"PDDRM"

typedef enum tagDELETE_OBJECT_OPTIONS {
  PORTABLE_DEVICE_DELETE_NO_RECURSION = 0,
  PORTABLE_DEVICE_DELETE_WITH_RECURSION = 1
} DELETE_OBJECT_OPTIONS;

typedef enum tagWPD_DEVICE_TYPES {
  WPD_DEVICE_TYPE_GENERIC = 0,
  WPD_DEVICE_TYPE_CAMERA = 1,
  WPD_DEVICE_TYPE_MEDIA_PLAYER = 2,
  WPD_DEVICE_TYPE_PHONE = 3,
  WPD_DEVICE_TYPE_VIDEO = 4,
  WPD_DEVICE_TYPE_PERSONAL_INFORMATION_MANAGER = 5,
  WPD_DEVICE_TYPE_AUDIO_RECORDER = 6
} WPD_DEVICE_TYPES;

typedef enum tagWpdAttributeForm {
  WPD_PROPERTY_ATTRIBUTE_FORM_UNSPECIFIED = 0,
  WPD_PROPERTY_ATTRIBUTE_FORM_RANGE = 1,
  WPD_PROPERTY_ATTRIBUTE_FORM_ENUMERATION = 2,
  WPD_PROPERTY_ATTRIBUTE_FORM_REGULAR_EXPRESSION = 3,
  WPD_PROPERTY_ATTRIBUTE_FORM_OBJECT_IDENTIFIER = 4
} WpdAttributeForm;

typedef enum tagWpdParameterAttributeForm {
  WPD_PARAMETER_ATTRIBUTE_FORM_UNSPECIFIED = 0,
  WPD_PARAMETER_ATTRIBUTE_FORM_RANGE = 1,
  WPD_PARAMETER_ATTRIBUTE_FORM_ENUMERATION = 2,
  WPD_PARAMETER_ATTRIBUTE_FORM_REGULAR_EXPRESSION = 3,
  WPD_PARAMETER_ATTRIBUTE_FORM_OBJECT_IDENTIFIER = 4
} WpdParameterAttributeForm;

typedef enum tagWPD_DEVICE_TRANSPORTS {
  WPD_DEVICE_TRANSPORT_UNSPECIFIED = 0,
  WPD_DEVICE_TRANSPORT_USB = 1,
  WPD_DEVICE_TRANSPORT_IP = 2,
  WPD_DEVICE_TRANSPORT_BLUETOOTH = 3
} WPD_DEVICE_TRANSPORTS;

typedef enum tagWPD_STORAGE_TYPE_VALUES {
  WPD_STORAGE_TYPE_UNDEFINED = 0,
  WPD_STORAGE_TYPE_FIXED_ROM = 1,
  WPD_STORAGE_TYPE_REMOVABLE_ROM = 2,
  WPD_STORAGE_TYPE_FIXED_RAM = 3,
  WPD_STORAGE_TYPE_REMOVABLE_RAM = 4
} WPD_STORAGE_TYPE_VALUES;

typedef enum tagWPD_STORAGE_ACCESS_CAPABILITY_VALUES {
  WPD_STORAGE_ACCESS_CAPABILITY_READWRITE = 0,
  WPD_STORAGE_ACCESS_CAPABILITY_READ_ONLY_WITHOUT_OBJECT_DELETION = 1,
  WPD_STORAGE_ACCESS_CAPABILITY_READ_ONLY_WITH_OBJECT_DELETION = 2
} WPD_STORAGE_ACCESS_CAPABILITY_VALUES;

typedef enum tagWPD_SMS_ENCODING_TYPES {
  SMS_ENCODING_7_BIT = 0,
  SMS_ENCODING_8_BIT = 1,
  SMS_ENCODING_UTF_16 = 2
} WPD_SMS_ENCODING_TYPES;

typedef enum tagSMS_MESSAGE_TYPES {
  SMS_TEXT_MESSAGE = 0,
  SMS_BINARY_MESSAGE = 1
} SMS_MESSAGE_TYPES;

typedef enum tagWPD_POWER_SOURCES {
  WPD_POWER_SOURCE_BATTERY = 0,
  WPD_POWER_SOURCE_EXTERNAL = 1
} WPD_POWER_SOURCES;

typedef enum tagWPD_WHITE_BALANCE_SETTINGS {
  WPD_WHITE_BALANCE_UNDEFINED = 0,
  WPD_WHITE_BALANCE_MANUAL = 1,
  WPD_WHITE_BALANCE_AUTOMATIC = 2,
  WPD_WHITE_BALANCE_ONE_PUSH_AUTOMATIC = 3,
  WPD_WHITE_BALANCE_DAYLIGHT = 4,
  WPD_WHITE_BALANCE_FLORESCENT = 5,
  WPD_WHITE_BALANCE_TUNGSTEN = 6,
  WPD_WHITE_BALANCE_FLASH = 7
} WPD_WHITE_BALANCE_SETTINGS;

typedef enum tagWPD_FOCUS_MODES {
  WPD_FOCUS_UNDEFINED = 0,
  WPD_FOCUS_MANUAL = 1,
  WPD_FOCUS_AUTOMATIC = 2,
  WPD_FOCUS_AUTOMATIC_MACRO = 3
} WPD_FOCUS_MODES;

typedef enum tagWPD_EXPOSURE_METERING_MODES {
  WPD_EXPOSURE_METERING_MODE_UNDEFINED = 0,
  WPD_EXPOSURE_METERING_MODE_AVERAGE = 1,
  WPD_EXPOSURE_METERING_MODE_CENTER_WEIGHTED_AVERAGE = 2,
  WPD_EXPOSURE_METERING_MODE_MULTI_SPOT = 3,
  WPD_EXPOSURE_METERING_MODE_CENTER_SPOT = 4
} WPD_EXPOSURE_METERING_MODES;

typedef enum tagWPD_FLASH_MODES {
  WPD_FLASH_MODE_UNDEFINED = 0,
  WPD_FLASH_MODE_AUTO = 1,
  WPD_FLASH_MODE_OFF = 2,
  WPD_FLASH_MODE_FILL = 3,
  WPD_FLASH_MODE_RED_EYE_AUTO = 4,
  WPD_FLASH_MODE_RED_EYE_FILL = 5,
  WPD_FLASH_MODE_EXTERNAL_SYNC = 6
} WPD_FLASH_MODES;

typedef enum tagWPD_EXPOSURE_PROGRAM_MODES {
  WPD_EXPOSURE_PROGRAM_MODE_UNDEFINED = 0,
  WPD_EXPOSURE_PROGRAM_MODE_MANUAL = 1,
  WPD_EXPOSURE_PROGRAM_MODE_AUTO = 2,
  WPD_EXPOSURE_PROGRAM_MODE_APERTURE_PRIORITY = 3,
  WPD_EXPOSURE_PROGRAM_MODE_SHUTTER_PRIORITY = 4,
  WPD_EXPOSURE_PROGRAM_MODE_CREATIVE = 5,
  WPD_EXPOSURE_PROGRAM_MODE_ACTION = 6,
  WPD_EXPOSURE_PROGRAM_MODE_PORTRAIT = 7
} WPD_EXPOSURE_PROGRAM_MODES;

typedef enum tagWPD_CAPTURE_MODES {
  WPD_CAPTURE_MODE_UNDEFINED = 0,
  WPD_CAPTURE_MODE_NORMAL = 1,
  WPD_CAPTURE_MODE_BURST = 2,
  WPD_CAPTURE_MODE_TIMELAPSE = 3
} WPD_CAPTURE_MODES;

typedef enum tagWPD_EFFECT_MODES {
  WPD_EFFECT_MODE_UNDEFINED = 0,
  WPD_EFFECT_MODE_COLOR = 1,
  WPD_EFFECT_MODE_BLACK_AND_WHITE = 2,
  WPD_EFFECT_MODE_SEPIA = 3
} WPD_EFFECT_MODES;

typedef enum tagWPD_FOCUS_METERING_MODES {
  WPD_FOCUS_METERING_MODE_UNDEFINED = 0,
  WPD_FOCUS_METERING_MODE_CENTER_SPOT = 1,
  WPD_FOCUS_METERING_MODE_MULTI_SPOT = 2
} WPD_FOCUS_METERING_MODES;

typedef enum tagWPD_BITRATE_TYPES {
  WPD_BITRATE_TYPE_UNUSED = 0,
  WPD_BITRATE_TYPE_DISCRETE = 1,
  WPD_BITRATE_TYPE_VARIABLE = 2,
  WPD_BITRATE_TYPE_FREE = 3
} WPD_BITRATE_TYPES;

typedef enum tagWPD_META_GENRES {
  WPD_META_GENRE_UNUSED = 0x0,
  WPD_META_GENRE_GENERIC_MUSIC_AUDIO_FILE = 0x1,
  WPD_META_GENRE_GENERIC_NON_MUSIC_AUDIO_FILE = 0x11,
  WPD_META_GENRE_SPOKEN_WORD_AUDIO_BOOK_FILES = 0x12,
  WPD_META_GENRE_SPOKEN_WORD_FILES_NON_AUDIO_BOOK = 0x13,
  WPD_META_GENRE_SPOKEN_WORD_NEWS = 0x14,
  WPD_META_GENRE_SPOKEN_WORD_TALK_SHOWS = 0x15,
  WPD_META_GENRE_GENERIC_VIDEO_FILE = 0x21,
  WPD_META_GENRE_NEWS_VIDEO_FILE = 0x22,
  WPD_META_GENRE_MUSIC_VIDEO_FILE = 0x23,
  WPD_META_GENRE_HOME_VIDEO_FILE = 0x24,
  WPD_META_GENRE_FEATURE_FILM_VIDEO_FILE = 0x25,
  WPD_META_GENRE_TELEVISION_VIDEO_FILE = 0x26,
  WPD_META_GENRE_TRAINING_EDUCATIONAL_VIDEO_FILE = 0x27,
  WPD_META_GENRE_PHOTO_MONTAGE_VIDEO_FILE = 0x28,
  WPD_META_GENRE_GENERIC_NON_AUDIO_NON_VIDEO = 0x30,
  WPD_META_GENRE_AUDIO_PODCAST = 0x40,
  WPD_META_GENRE_VIDEO_PODCAST = 0x41,
  WPD_META_GENRE_MIXED_PODCAST = 0x42
} WPD_META_GENRES;

typedef enum tagWPD_CROPPED_STATUS_VALUES {
  WPD_CROPPED_STATUS_NOT_CROPPED = 0,
  WPD_CROPPED_STATUS_CROPPED = 1,
  WPD_CROPPED_STATUS_SHOULD_NOT_BE_CROPPED = 2
} WPD_CROPPED_STATUS_VALUES;

typedef enum tagWPD_COLOR_CORRECTED_STATUS_VALUES {
  WPD_COLOR_CORRECTED_STATUS_NOT_CORRECTED = 0,
  WPD_COLOR_CORRECTED_STATUS_CORRECTED = 1,
  WPD_COLOR_CORRECTED_STATUS_SHOULD_NOT_BE_CORRECTED = 2
} WPD_COLOR_CORRECTED_STATUS_VALUES;

typedef enum tagWPD_VIDEO_SCAN_TYPES {
  WPD_VIDEO_SCAN_TYPE_UNUSED = 0,
  WPD_VIDEO_SCAN_TYPE_PROGRESSIVE = 1,
  WPD_VIDEO_SCAN_TYPE_FIELD_INTERLEAVED_UPPER_FIRST = 2,
  WPD_VIDEO_SCAN_TYPE_FIELD_INTERLEAVED_LOWER_FIRST = 3,
  WPD_VIDEO_SCAN_TYPE_FIELD_SINGLE_UPPER_FIRST = 4,
  WPD_VIDEO_SCAN_TYPE_FIELD_SINGLE_LOWER_FIRST = 5,
  WPD_VIDEO_SCAN_TYPE_MIXED_INTERLACE = 6,
  WPD_VIDEO_SCAN_TYPE_MIXED_INTERLACE_AND_PROGRESSIVE = 7
} WPD_VIDEO_SCAN_TYPES;

typedef enum tagWPD_OPERATION_STATES {
  WPD_OPERATION_STATE_UNSPECIFIED = 0,
  WPD_OPERATION_STATE_STARTED = 1,
  WPD_OPERATION_STATE_RUNNING = 2,
  WPD_OPERATION_STATE_PAUSED = 3,
  WPD_OPERATION_STATE_CANCELLED = 4,
  WPD_OPERATION_STATE_FINISHED = 5,
  WPD_OPERATION_STATE_ABORTED = 6
} WPD_OPERATION_STATES;

typedef enum tagWPD_SECTION_DATA_UNITS_VALUES {
  WPD_SECTION_DATA_UNITS_BYTES = 0,
  WPD_SECTION_DATA_UNITS_MILLISECONDS = 1
} WPD_SECTION_DATA_UNITS_VALUES;

typedef enum tagWPD_RENDERING_INFORMATION_PROFILE_ENTRY_TYPES {
  WPD_RENDERING_INFORMATION_PROFILE_ENTRY_TYPE_OBJECT = 0,
  WPD_RENDERING_INFORMATION_PROFILE_ENTRY_TYPE_RESOURCE = 1
} WPD_RENDERING_INFORMATION_PROFILE_ENTRY_TYPES;

typedef enum tagWPD_COMMAND_ACCESS_TYPES {
  WPD_COMMAND_ACCESS_READ = 1,
  WPD_COMMAND_ACCESS_READWRITE = 3,
  WPD_COMMAND_ACCESS_FROM_PROPERTY_WITH_STGM_ACCESS = 4,
  WPD_COMMAND_ACCESS_FROM_PROPERTY_WITH_FILE_ACCESS = 8,
  WPD_COMMAND_ACCESS_FROM_ATTRIBUTE_WITH_METHOD_ACCESS = 16
} WPD_COMMAND_ACCESS_TYPES;

typedef enum tagWPD_SERVICE_INHERITANCE_TYPES {
  WPD_SERVICE_INHERITANCE_IMPLEMENTATION = 0
} WPD_SERVICE_INHERITANCE_TYPES;

typedef enum tagWPD_PARAMETER_USAGE_TYPES {
  WPD_PARAMETER_USAGE_RETURN = 0,
  WPD_PARAMETER_USAGE_IN = 1,
  WPD_PARAMETER_USAGE_OUT = 2,
  WPD_PARAMETER_USAGE_INOUT = 3
} WPD_PARAMETER_USAGE_TYPES;

#define FACILITY_WPD 42

#define E_WPD_DEVICE_ALREADY_OPENED MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 1)
#define E_WPD_DEVICE_NOT_OPEN MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 2)
#define E_WPD_OBJECT_ALREADY_ATTACHED_TO_DEVICE MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 3)
#define E_WPD_OBJECT_NOT_ATTACHED_TO_DEVICE MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 4)
#define E_WPD_OBJECT_NOT_COMMITED MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 5)
#define E_WPD_DEVICE_IS_HUNG MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 6)
#define E_WPD_SMS_INVALID_RECIPIENT MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 100)
#define E_WPD_SMS_INVALID_MESSAGE_BODY MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 101)
#define E_WPD_SMS_SERVICE_UNAVAILABLE MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 102)
#define E_WPD_SERVICE_ALREADY_OPENED MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 200)
#define E_WPD_SERVICE_NOT_OPEN MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 201)
#define E_WPD_OBJECT_ALREADY_ATTACHED_TO_SERVICE MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 202)
#define E_WPD_OBJECT_NOT_ATTACHED_TO_SERVICE MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 203)
#define E_WPD_SERVICE_BAD_PARAMETER_ORDER MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WPD, 204)

DEFINE_GUID(WPD_EVENT_NOTIFICATION, 0x2ba2e40a, 0x6b4c, 0x4295, 0xbb, 0x43, 0x26, 0x32, 0x2b, 0x99, 0xae, 0xb2);
DEFINE_GUID(WPD_EVENT_OBJECT_ADDED, 0xa726da95, 0xe207, 0x4b02, 0x8d, 0x44, 0xbe, 0xf2, 0xe8, 0x6c, 0xbf, 0xfc);
DEFINE_GUID(WPD_EVENT_OBJECT_REMOVED, 0xbe82ab88, 0xa52c, 0x4823, 0x96, 0xe5, 0xd0, 0x27, 0x26, 0x71, 0xfc, 0x38);
DEFINE_GUID(WPD_EVENT_OBJECT_UPDATED, 0x1445a759, 0x2e01, 0x485d, 0x9f, 0x27, 0xff, 0x07, 0xda, 0xe6, 0x97, 0xab);
DEFINE_GUID(WPD_EVENT_DEVICE_RESET, 0x7755cf53, 0xc1ed, 0x44f3, 0xb5, 0xa2, 0x45, 0x1e, 0x2c, 0x37, 0x6b, 0x27);
DEFINE_GUID(WPD_EVENT_DEVICE_CAPABILITIES_UPDATED, 0x36885aa1, 0xcd54, 0x4daa, 0xb3, 0xd0, 0xaf, 0xb3, 0xe0, 0x3f, 0x59, 0x99);
DEFINE_GUID(WPD_EVENT_STORAGE_FORMAT, 0x3782616b, 0x22bc, 0x4474, 0xa2, 0x51, 0x30, 0x70, 0xf8, 0xd3, 0x88, 0x57);
DEFINE_GUID(WPD_EVENT_OBJECT_TRANSFER_REQUESTED, 0x8d16a0a1, 0xf2c6, 0x41da, 0x8f, 0x19, 0x5e, 0x53, 0x72, 0x1a, 0xdb, 0xf2);
DEFINE_GUID(WPD_EVENT_DEVICE_REMOVED, 0xe4cbca1b, 0x6918, 0x48b9, 0x85, 0xee, 0x02, 0xbe, 0x7c, 0x85, 0x0a, 0xf9);
DEFINE_GUID(WPD_EVENT_SERVICE_METHOD_COMPLETE, 0x8a33f5f8, 0x0acc, 0x4d9b, 0x9c, 0xc4, 0x11, 0x2d, 0x35, 0x3b, 0x86, 0xca);
DEFINE_GUID(WPD_CONTENT_TYPE_FUNCTIONAL_OBJECT, 0x99ed0160, 0x17ff, 0x4c44, 0x9d, 0x98, 0x1d, 0x7a, 0x6f, 0x94, 0x19, 0x21);
DEFINE_GUID(WPD_CONTENT_TYPE_FOLDER, 0x27e2e392, 0xa111, 0x48e0, 0xab, 0x0c, 0xe1, 0x77, 0x05, 0xa0, 0x5f, 0x85);
DEFINE_GUID(WPD_CONTENT_TYPE_IMAGE, 0xef2107d5, 0xa52a, 0x4243, 0xa2, 0x6b, 0x62, 0xd4, 0x17, 0x6d, 0x76, 0x03);
DEFINE_GUID(WPD_CONTENT_TYPE_DOCUMENT, 0x680adf52, 0x950a, 0x4041, 0x9b, 0x41, 0x65, 0xe3, 0x93, 0x64, 0x81, 0x55);
DEFINE_GUID(WPD_CONTENT_TYPE_CONTACT, 0xeaba8313, 0x4525, 0x4707, 0x9f, 0x0e, 0x87, 0xc6, 0x80, 0x8e, 0x94, 0x35);
DEFINE_GUID(WPD_CONTENT_TYPE_CONTACT_GROUP, 0x346b8932, 0x4c36, 0x40d8, 0x94, 0x15, 0x18, 0x28, 0x29, 0x1f, 0x9d, 0xe9);
DEFINE_GUID(WPD_CONTENT_TYPE_AUDIO, 0x4ad2c85e, 0x5e2d, 0x45e5, 0x88, 0x64, 0x4f, 0x22, 0x9e, 0x3c, 0x6c, 0xf0);
DEFINE_GUID(WPD_CONTENT_TYPE_VIDEO, 0x9261b03c, 0x3d78, 0x4519, 0x85, 0xe3, 0x02, 0xc5, 0xe1, 0xf5, 0x0b, 0xb9);
DEFINE_GUID(WPD_CONTENT_TYPE_TELEVISION, 0x60a169cf, 0xf2ae, 0x4e21, 0x93, 0x75, 0x96, 0x77, 0xf1, 0x1c, 0x1c, 0x6e);
DEFINE_GUID(WPD_CONTENT_TYPE_PLAYLIST, 0x1a33f7e4, 0xaf13, 0x48f5, 0x99, 0x4e, 0x77, 0x36, 0x9d, 0xfe, 0x04, 0xa3);
DEFINE_GUID(WPD_CONTENT_TYPE_MIXED_CONTENT_ALBUM, 0x00f0c3ac, 0xa593, 0x49ac, 0x92, 0x19, 0x24, 0xab, 0xca, 0x5a, 0x25, 0x63);
DEFINE_GUID(WPD_CONTENT_TYPE_AUDIO_ALBUM, 0xaa18737e, 0x5009, 0x48fa, 0xae, 0x21, 0x85, 0xf2, 0x43, 0x83, 0xb4, 0xe6);
DEFINE_GUID(WPD_CONTENT_TYPE_IMAGE_ALBUM, 0x75793148, 0x15f5, 0x4a30, 0xa8, 0x13, 0x54, 0xed, 0x8a, 0x37, 0xe2, 0x26);
DEFINE_GUID(WPD_CONTENT_TYPE_VIDEO_ALBUM, 0x012b0db7, 0xd4c1, 0x45d6, 0xb0, 0x81, 0x94, 0xb8, 0x77, 0x79, 0x61, 0x4f);
DEFINE_GUID(WPD_CONTENT_TYPE_MEMO, 0x9cd20ecf, 0x3b50, 0x414f, 0xa6, 0x41, 0xe4, 0x73, 0xff, 0xe4, 0x57, 0x51);
DEFINE_GUID(WPD_CONTENT_TYPE_EMAIL, 0x8038044a, 0x7e51, 0x4f8f, 0x88, 0x3d, 0x1d, 0x06, 0x23, 0xd1, 0x45, 0x33);
DEFINE_GUID(WPD_CONTENT_TYPE_APPOINTMENT, 0x0fed060e, 0x8793, 0x4b1e, 0x90, 0xc9, 0x48, 0xac, 0x38, 0x9a, 0xc6, 0x31);
DEFINE_GUID(WPD_CONTENT_TYPE_TASK, 0x63252f2c, 0x887f, 0x4cb6, 0xb1, 0xac, 0xd2, 0x98, 0x55, 0xdc, 0xef, 0x6c);
DEFINE_GUID(WPD_CONTENT_TYPE_PROGRAM, 0xd269f96a, 0x247c, 0x4bff, 0x98, 0xfb, 0x97, 0xf3, 0xc4, 0x92, 0x20, 0xe6);
DEFINE_GUID(WPD_CONTENT_TYPE_GENERIC_FILE, 0x0085e0a6, 0x8d34, 0x45d7, 0xbc, 0x5c, 0x44, 0x7e, 0x59, 0xc7, 0x3d, 0x48);
DEFINE_GUID(WPD_CONTENT_TYPE_CALENDAR, 0xa1fd5967, 0x6023, 0x49a0, 0x9d, 0xf1, 0xf8, 0x06, 0x0b, 0xe7, 0x51, 0xb0);
DEFINE_GUID(WPD_CONTENT_TYPE_GENERIC_MESSAGE, 0xe80eaaf8, 0xb2db, 0x4133, 0xb6, 0x7e, 0x1b, 0xef, 0x4b, 0x4a, 0x6e, 0x5f);
DEFINE_GUID(WPD_CONTENT_TYPE_NETWORK_ASSOCIATION, 0x031da7ee, 0x18c8, 0x4205, 0x84, 0x7e, 0x89, 0xa1, 0x12, 0x61, 0xd0, 0xf3);
DEFINE_GUID(WPD_CONTENT_TYPE_CERTIFICATE, 0xdc3876e8, 0xa948, 0x4060, 0x90, 0x50, 0xcb, 0xd7, 0x7e, 0x8a, 0x3d, 0x87);
DEFINE_GUID(WPD_CONTENT_TYPE_WIRELESS_PROFILE, 0x0bac070a, 0x9f5f, 0x4da4, 0xa8, 0xf6, 0x3d, 0xe4, 0x4d, 0x68, 0xfd, 0x6c);
DEFINE_GUID(WPD_CONTENT_TYPE_MEDIA_CAST, 0x5e88b3cc, 0x3e65, 0x4e62, 0xbf, 0xff, 0x22, 0x94, 0x95, 0x25, 0x3a, 0xb0);
DEFINE_GUID(WPD_CONTENT_TYPE_SECTION, 0x821089f5, 0x1d91, 0x4dc9, 0xbe, 0x3c, 0xbb, 0xb1, 0xb3, 0x5b, 0x18, 0xce);
DEFINE_GUID(WPD_CONTENT_TYPE_UNSPECIFIED, 0x28d8d31e, 0x249c, 0x454e, 0xaa, 0xbc, 0x34, 0x88, 0x31, 0x68, 0xe6, 0x34);
DEFINE_GUID(WPD_CONTENT_TYPE_ALL, 0x80e170d2, 0x1055, 0x4a3e, 0xb9, 0x52, 0x82, 0xcc, 0x4f, 0x8a, 0x86, 0x89);
DEFINE_GUID(WPD_FUNCTIONAL_CATEGORY_DEVICE, 0x08ea466b, 0xe3a4, 0x4336, 0xa1, 0xf3, 0xa4, 0x4d, 0x2b, 0x5c, 0x43, 0x8c);
DEFINE_GUID(WPD_FUNCTIONAL_CATEGORY_STORAGE, 0x23f05bbc, 0x15de, 0x4c2a, 0xa5, 0x5b, 0xa9, 0xaf, 0x5c, 0xe4, 0x12, 0xef);
DEFINE_GUID(WPD_FUNCTIONAL_CATEGORY_STILL_IMAGE_CAPTURE, 0x613ca327, 0xab93, 0x4900, 0xb4, 0xfa, 0x89, 0x5b, 0xb5, 0x87, 0x4b, 0x79);
DEFINE_GUID(WPD_FUNCTIONAL_CATEGORY_AUDIO_CAPTURE, 0x3f2a1919, 0xc7c2, 0x4a00, 0x85, 0x5d, 0xf5, 0x7c, 0xf0, 0x6d, 0xeb, 0xbb);
DEFINE_GUID(WPD_FUNCTIONAL_CATEGORY_VIDEO_CAPTURE, 0xe23e5f6b, 0x7243, 0x43aa, 0x8d, 0xf1, 0x0e, 0xb3, 0xd9, 0x68, 0xa9, 0x18);
DEFINE_GUID(WPD_FUNCTIONAL_CATEGORY_SMS, 0x0044a0b1, 0xc1e9, 0x4afd, 0xb3, 0x58, 0xa6, 0x2c, 0x61, 0x17, 0xc9, 0xcf);
DEFINE_GUID(WPD_FUNCTIONAL_CATEGORY_RENDERING_INFORMATION, 0x08600ba4, 0xa7ba, 0x4a01, 0xab, 0x0e, 0x00, 0x65, 0xd0, 0xa3, 0x56, 0xd3);
DEFINE_GUID(WPD_FUNCTIONAL_CATEGORY_NETWORK_CONFIGURATION, 0x48f4db72, 0x7c6a, 0x4ab0, 0x9e, 0x1a, 0x47, 0x0e, 0x3c, 0xdb, 0xf2, 0x6a);
DEFINE_GUID(WPD_FUNCTIONAL_CATEGORY_ALL, 0x2d8a6512, 0xa74c, 0x448e, 0xba, 0x8a, 0xf4, 0xac, 0x07, 0xc4, 0x93, 0x99);
DEFINE_GUID(WPD_OBJECT_FORMAT_ICON, 0x077232ed, 0x102c, 0x4638, 0x9c, 0x22, 0x83, 0xf1, 0x42, 0xbf, 0xc8, 0x22);
DEFINE_GUID(WPD_OBJECT_FORMAT_M4A, 0x30aba7ac, 0x6ffd, 0x4c23, 0xa3, 0x59, 0x3e, 0x9b, 0x52, 0xf3, 0xf1, 0xc8);
DEFINE_GUID(WPD_OBJECT_FORMAT_NETWORK_ASSOCIATION, 0xb1020000, 0xae6c, 0x4804, 0x98, 0xba, 0xc5, 0x7b, 0x46, 0x96, 0x5f, 0xe7);
DEFINE_GUID(WPD_OBJECT_FORMAT_X509V3CERTIFICATE, 0xb1030000, 0xae6c, 0x4804, 0x98, 0xba, 0xc5, 0x7b, 0x46, 0x96, 0x5f, 0xe7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MICROSOFT_WFC, 0xb1040000, 0xae6c, 0x4804, 0x98, 0xba, 0xc5, 0x7b, 0x46, 0x96, 0x5f, 0xe7);
DEFINE_GUID(WPD_OBJECT_FORMAT_3GPA, 0xe5172730, 0xf971, 0x41ef, 0xa1, 0x0b, 0x22, 0x71, 0xa0, 0x01, 0x9d, 0x7a);
DEFINE_GUID(WPD_OBJECT_FORMAT_3G2A, 0x1a11202d, 0x8759, 0x4e34, 0xba, 0x5e, 0xb1, 0x21, 0x10, 0x87, 0xee, 0xe4);
DEFINE_GUID(WPD_OBJECT_FORMAT_ALL, 0xc1f62eb2, 0x4bb3, 0x479c, 0x9c, 0xfa, 0x05, 0xb5, 0xf3, 0xa5, 0x7b, 0x22);
DEFINE_GUID(WPD_CATEGORY_NULL, 0x00000000, 0x0000, 0x0000, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00);
DEFINE_PROPERTYKEY(WPD_PROPERTY_NULL, 0x00000000, 0x0000, 0x0000, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0);
DEFINE_GUID(WPD_OBJECT_PROPERTIES_V1, 0xef6b490d, 0x5cd8, 0x437a, 0xaf, 0xfc, 0xda, 0x8b, 0x60, 0xee, 0x4a, 0x3c);
DEFINE_PROPERTYKEY(WPD_OBJECT_CONTENT_TYPE, 0xef6b490d, 0x5cd8, 0x437a, 0xaf, 0xfc, 0xda, 0x8b, 0x60, 0xee, 0x4a, 0x3c, 7);
DEFINE_PROPERTYKEY(WPD_OBJECT_REFERENCES, 0xef6b490d, 0x5cd8, 0x437a, 0xaf, 0xfc, 0xda, 0x8b, 0x60, 0xee, 0x4a, 0x3c, 14);
DEFINE_PROPERTYKEY(WPD_OBJECT_CONTAINER_FUNCTIONAL_OBJECT_ID, 0xef6b490d, 0x5cd8, 0x437a, 0xaf, 0xfc, 0xda, 0x8b, 0x60, 0xee, 0x4a, 0x3c, 23);
DEFINE_PROPERTYKEY(WPD_OBJECT_GENERATE_THUMBNAIL_FROM_RESOURCE, 0xef6b490d, 0x5cd8, 0x437a, 0xaf, 0xfc, 0xda, 0x8b, 0x60, 0xee, 0x4a, 0x3c, 24);
DEFINE_PROPERTYKEY(WPD_OBJECT_HINT_LOCATION_DISPLAY_NAME, 0xef6b490d, 0x5cd8, 0x437a, 0xaf, 0xfc, 0xda, 0x8b, 0x60, 0xee, 0x4a, 0x3c, 25);
DEFINE_GUID(WPD_OBJECT_PROPERTIES_V2, 0x0373cd3d, 0x4a46, 0x40d7, 0xb4, 0xd8, 0x73, 0xe8, 0xda, 0x74, 0xe7, 0x75);
DEFINE_PROPERTYKEY(WPD_OBJECT_SUPPORTED_UNITS, 0x0373cd3d, 0x4a46, 0x40d7, 0xb4, 0xd8, 0x73, 0xe8, 0xda, 0x74, 0xe7, 0x75, 2);
DEFINE_GUID(WPD_FUNCTIONAL_OBJECT_PROPERTIES_V1, 0x8f052d93, 0xabca, 0x4fc5, 0xa5, 0xac, 0xb0, 0x1d, 0xf4, 0xdb, 0xe5, 0x98);
DEFINE_PROPERTYKEY(WPD_FUNCTIONAL_OBJECT_CATEGORY, 0x8f052d93, 0xabca, 0x4fc5, 0xa5, 0xac, 0xb0, 0x1d, 0xf4, 0xdb, 0xe5, 0x98, 2);
DEFINE_GUID(WPD_STORAGE_OBJECT_PROPERTIES_V1, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a);
DEFINE_PROPERTYKEY(WPD_STORAGE_TYPE, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 2);
DEFINE_PROPERTYKEY(WPD_STORAGE_FILE_SYSTEM_TYPE, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 3);
DEFINE_PROPERTYKEY(WPD_STORAGE_CAPACITY, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 4);
DEFINE_PROPERTYKEY(WPD_STORAGE_FREE_SPACE_IN_BYTES, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 5);
DEFINE_PROPERTYKEY(WPD_STORAGE_FREE_SPACE_IN_OBJECTS, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 6);
DEFINE_PROPERTYKEY(WPD_STORAGE_DESCRIPTION, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 7);
DEFINE_PROPERTYKEY(WPD_STORAGE_SERIAL_NUMBER, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 8);
DEFINE_PROPERTYKEY(WPD_STORAGE_MAX_OBJECT_SIZE, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 9);
DEFINE_PROPERTYKEY(WPD_STORAGE_CAPACITY_IN_OBJECTS, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 10);
DEFINE_PROPERTYKEY(WPD_STORAGE_ACCESS_CAPABILITY, 0x01a3057a, 0x74d6, 0x4e80, 0xbe, 0xa7, 0xdc, 0x4c, 0x21, 0x2c, 0xe5, 0x0a, 11);
DEFINE_GUID(WPD_NETWORK_ASSOCIATION_PROPERTIES_V1, 0xe4c93c1f, 0xb203, 0x43f1, 0xa1, 0x00, 0x5a, 0x07, 0xd1, 0x1b, 0x02, 0x74);
DEFINE_PROPERTYKEY(WPD_NETWORK_ASSOCIATION_HOST_NETWORK_IDENTIFIERS, 0xe4c93c1f, 0xb203, 0x43f1, 0xa1, 0x00, 0x5a, 0x07, 0xd1, 0x1b, 0x02, 0x74, 2);
DEFINE_PROPERTYKEY(WPD_NETWORK_ASSOCIATION_X509V3SEQUENCE, 0xe4c93c1f, 0xb203, 0x43f1, 0xa1, 0x00, 0x5a, 0x07, 0xd1, 0x1b, 0x02, 0x74, 3);
DEFINE_GUID(WPD_STILL_IMAGE_CAPTURE_OBJECT_PROPERTIES_V1, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_CAPTURE_RESOLUTION, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 2);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_CAPTURE_FORMAT, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 3);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_COMPRESSION_SETTING, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 4);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_WHITE_BALANCE, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 5);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_RGB_GAIN, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 6);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_FNUMBER, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 7);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_FOCAL_LENGTH, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 8);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_FOCUS_DISTANCE, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 9);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_FOCUS_MODE, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 10);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_EXPOSURE_METERING_MODE, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 11);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_FLASH_MODE, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 12);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_EXPOSURE_TIME, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 13);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_EXPOSURE_PROGRAM_MODE, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 14);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_EXPOSURE_INDEX, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 15);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_EXPOSURE_BIAS_COMPENSATION, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 16);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_CAPTURE_DELAY, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 17);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_CAPTURE_MODE, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 18);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_CONTRAST, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 19);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_SHARPNESS, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 20);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_DIGITAL_ZOOM, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 21);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_EFFECT_MODE, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 22);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_BURST_NUMBER, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 23);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_BURST_INTERVAL, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 24);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_TIMELAPSE_NUMBER, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 25);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_TIMELAPSE_INTERVAL, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 26);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_FOCUS_METERING_MODE, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 27);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_UPLOAD_URL, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 28);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_ARTIST, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 29);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_CAMERA_MODEL, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 30);
DEFINE_PROPERTYKEY(WPD_STILL_IMAGE_CAMERA_MANUFACTURER, 0x58c571ec, 0x1bcb, 0x42a7, 0x8a, 0xc5, 0xbb, 0x29, 0x15, 0x73, 0xa2, 0x60, 31);
DEFINE_GUID(WPD_RENDERING_INFORMATION_OBJECT_PROPERTIES_V1, 0xc53d039f, 0xee23, 0x4a31, 0x85, 0x90, 0x76, 0x39, 0x87, 0x98, 0x70, 0xb4);
DEFINE_PROPERTYKEY(WPD_RENDERING_INFORMATION_PROFILES, 0xc53d039f, 0xee23, 0x4a31, 0x85, 0x90, 0x76, 0x39, 0x87, 0x98, 0x70, 0xb4, 2);
DEFINE_PROPERTYKEY(WPD_RENDERING_INFORMATION_PROFILE_ENTRY_TYPE, 0xc53d039f, 0xee23, 0x4a31, 0x85, 0x90, 0x76, 0x39, 0x87, 0x98, 0x70, 0xb4, 3);
DEFINE_PROPERTYKEY(WPD_RENDERING_INFORMATION_PROFILE_ENTRY_CREATABLE_RESOURCES, 0xc53d039f, 0xee23, 0x4a31, 0x85, 0x90, 0x76, 0x39, 0x87, 0x98, 0x70, 0xb4, 4);
DEFINE_GUID(WPD_CLIENT_INFORMATION_PROPERTIES_V1, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59);
DEFINE_PROPERTYKEY(WPD_CLIENT_NAME, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 2);
DEFINE_PROPERTYKEY(WPD_CLIENT_MAJOR_VERSION, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 3);
DEFINE_PROPERTYKEY(WPD_CLIENT_MINOR_VERSION, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 4);
DEFINE_PROPERTYKEY(WPD_CLIENT_REVISION, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 5);
DEFINE_PROPERTYKEY(WPD_CLIENT_WMDRM_APPLICATION_PRIVATE_KEY, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 6);
DEFINE_PROPERTYKEY(WPD_CLIENT_WMDRM_APPLICATION_CERTIFICATE, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 7);
DEFINE_PROPERTYKEY(WPD_CLIENT_SECURITY_QUALITY_OF_SERVICE, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 8);
DEFINE_PROPERTYKEY(WPD_CLIENT_DESIRED_ACCESS, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 9);
DEFINE_PROPERTYKEY(WPD_CLIENT_SHARE_MODE, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 10);
DEFINE_PROPERTYKEY(WPD_CLIENT_EVENT_COOKIE, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 11);
DEFINE_PROPERTYKEY(WPD_CLIENT_MINIMUM_RESULTS_BUFFER_SIZE, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 12);
DEFINE_PROPERTYKEY(WPD_CLIENT_MANUAL_CLOSE_ON_DISCONNECT, 0x204d9f0c, 0x2292, 0x4080, 0x9f, 0x42, 0x40, 0x66, 0x4e, 0x70, 0xf8, 0x59, 13);
DEFINE_GUID(WPD_PROPERTY_ATTRIBUTES_V1, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_FORM, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 2);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_CAN_READ, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 3);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_CAN_WRITE, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 4);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_CAN_DELETE, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 5);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_DEFAULT_VALUE, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 6);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_FAST_PROPERTY, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 7);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_RANGE_MIN, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 8);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_RANGE_MAX, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 9);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_RANGE_STEP, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 10);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_ENUMERATION_ELEMENTS, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 11);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_REGULAR_EXPRESSION, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 12);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_MAX_SIZE, 0xab7943d8, 0x6332, 0x445f, 0xa0, 0x0d, 0x8d, 0x5e, 0xf1, 0xe9, 0x6f, 0x37, 13);
DEFINE_GUID(WPD_PROPERTY_ATTRIBUTES_V2, 0x5d9da160, 0x74ae, 0x43cc, 0x85, 0xa9, 0xfe, 0x55, 0x5a, 0x80, 0x79, 0x8e);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_NAME, 0x5d9da160, 0x74ae, 0x43cc, 0x85, 0xa9, 0xfe, 0x55, 0x5a, 0x80, 0x79, 0x8e, 2);
DEFINE_PROPERTYKEY(WPD_PROPERTY_ATTRIBUTE_VARTYPE, 0x5d9da160, 0x74ae, 0x43cc, 0x85, 0xa9, 0xfe, 0x55, 0x5a, 0x80, 0x79, 0x8e, 3);
DEFINE_GUID(WPD_CLASS_EXTENSION_OPTIONS_V1, 0x6309FFEF, 0xA87C, 0x4CA7, 0x84, 0x34, 0x79, 0x75, 0x76, 0xE4, 0x0A, 0x96);
DEFINE_PROPERTYKEY(WPD_CLASS_EXTENSION_OPTIONS_SUPPORTED_CONTENT_TYPES, 0x6309FFEF, 0xA87C, 0x4CA7, 0x84, 0x34, 0x79, 0x75, 0x76, 0xE4, 0x0A, 0x96, 2);
DEFINE_PROPERTYKEY(WPD_CLASS_EXTENSION_OPTIONS_DONT_REGISTER_WPD_DEVICE_INTERFACE, 0x6309FFEF, 0xA87C, 0x4CA7, 0x84, 0x34, 0x79, 0x75, 0x76, 0xE4, 0x0A, 0x96, 3);
DEFINE_PROPERTYKEY(WPD_CLASS_EXTENSION_OPTIONS_REGISTER_WPD_PRIVATE_DEVICE_INTERFACE, 0x6309FFEF, 0xA87C, 0x4CA7, 0x84, 0x34, 0x79, 0x75, 0x76, 0xE4, 0x0A, 0x96, 4);
DEFINE_GUID(WPD_CLASS_EXTENSION_OPTIONS_V2, 0x3E3595DA, 0x4D71, 0x49FE, 0xA0, 0xB4, 0xD4, 0x40, 0x6C, 0x3A, 0xE9, 0x3F);
DEFINE_PROPERTYKEY(WPD_CLASS_EXTENSION_OPTIONS_MULTITRANSPORT_MODE, 0x3E3595DA, 0x4D71, 0x49FE, 0xA0, 0xB4, 0xD4, 0x40, 0x6C, 0x3A, 0xE9, 0x3F, 2);
DEFINE_PROPERTYKEY(WPD_CLASS_EXTENSION_OPTIONS_DEVICE_IDENTIFICATION_VALUES, 0x3E3595DA, 0x4D71, 0x49FE, 0xA0, 0xB4, 0xD4, 0x40, 0x6C, 0x3A, 0xE9, 0x3F, 3);
DEFINE_PROPERTYKEY(WPD_CLASS_EXTENSION_OPTIONS_TRANSPORT_BANDWIDTH, 0x3E3595DA, 0x4D71, 0x49FE, 0xA0, 0xB4, 0xD4, 0x40, 0x6C, 0x3A, 0xE9, 0x3F, 4);
DEFINE_GUID(WPD_CLASS_EXTENSION_OPTIONS_V3, 0x65C160F8, 0x1367, 0x4CE2, 0x93, 0x9D, 0x83, 0x10, 0x83, 0x9F, 0x0D, 0x30);
DEFINE_PROPERTYKEY(WPD_CLASS_EXTENSION_OPTIONS_SILENCE_AUTOPLAY, 0x65C160F8, 0x1367, 0x4CE2, 0x93, 0x9D, 0x83, 0x10, 0x83, 0x9F, 0x0D, 0x30, 2);
DEFINE_GUID(WPD_RESOURCE_ATTRIBUTES_V1, 0x1EB6F604, 0x9278, 0x429F, 0x93, 0xCC, 0x5B, 0xB8, 0xC0, 0x66, 0x56, 0xB6);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ATTRIBUTE_TOTAL_SIZE, 0x1EB6F604, 0x9278, 0x429F, 0x93, 0xCC, 0x5B, 0xB8, 0xC0, 0x66, 0x56, 0xB6, 2);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ATTRIBUTE_CAN_READ, 0x1EB6F604, 0x9278, 0x429F, 0x93, 0xCC, 0x5B, 0xB8, 0xC0, 0x66, 0x56, 0xB6, 3);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ATTRIBUTE_CAN_WRITE, 0x1EB6F604, 0x9278, 0x429F, 0x93, 0xCC, 0x5B, 0xB8, 0xC0, 0x66, 0x56, 0xB6, 4);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ATTRIBUTE_CAN_DELETE, 0x1EB6F604, 0x9278, 0x429F, 0x93, 0xCC, 0x5B, 0xB8, 0xC0, 0x66, 0x56, 0xB6, 5);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ATTRIBUTE_OPTIMAL_READ_BUFFER_SIZE, 0x1EB6F604, 0x9278, 0x429F, 0x93, 0xCC, 0x5B, 0xB8, 0xC0, 0x66, 0x56, 0xB6, 6);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ATTRIBUTE_OPTIMAL_WRITE_BUFFER_SIZE, 0x1EB6F604, 0x9278, 0x429F, 0x93, 0xCC, 0x5B, 0xB8, 0xC0, 0x66, 0x56, 0xB6, 7);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ATTRIBUTE_FORMAT, 0x1EB6F604, 0x9278, 0x429F, 0x93, 0xCC, 0x5B, 0xB8, 0xC0, 0x66, 0x56, 0xB6, 8);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ATTRIBUTE_RESOURCE_KEY, 0x1EB6F604, 0x9278, 0x429F, 0x93, 0xCC, 0x5B, 0xB8, 0xC0, 0x66, 0x56, 0xB6, 9);
DEFINE_GUID(WPD_DEVICE_PROPERTIES_V1, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC);
DEFINE_PROPERTYKEY(WPD_DEVICE_SYNC_PARTNER, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  2);
DEFINE_PROPERTYKEY(WPD_DEVICE_FIRMWARE_VERSION, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  3);
DEFINE_PROPERTYKEY(WPD_DEVICE_POWER_LEVEL, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  4);
DEFINE_PROPERTYKEY(WPD_DEVICE_POWER_SOURCE, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  5);
DEFINE_PROPERTYKEY(WPD_DEVICE_PROTOCOL, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  6);
DEFINE_PROPERTYKEY(WPD_DEVICE_MANUFACTURER, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  7);
DEFINE_PROPERTYKEY(WPD_DEVICE_MODEL, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  8);
DEFINE_PROPERTYKEY(WPD_DEVICE_SERIAL_NUMBER, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  9);
DEFINE_PROPERTYKEY(WPD_DEVICE_SUPPORTS_NON_CONSUMABLE, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  10);
DEFINE_PROPERTYKEY(WPD_DEVICE_DATETIME, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  11);
DEFINE_PROPERTYKEY(WPD_DEVICE_FRIENDLY_NAME, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  12);
DEFINE_PROPERTYKEY(WPD_DEVICE_SUPPORTED_DRM_SCHEMES, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  13);
DEFINE_PROPERTYKEY(WPD_DEVICE_SUPPORTED_FORMATS_ARE_ORDERED, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  14);
DEFINE_PROPERTYKEY(WPD_DEVICE_TYPE, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  15);
DEFINE_PROPERTYKEY(WPD_DEVICE_NETWORK_IDENTIFIER, 0x26D4979A, 0xE643, 0x4626, 0x9E, 0x2B, 0x73, 0x6D, 0xC0, 0xC9, 0x2F, 0xDC,  16);
DEFINE_GUID(WPD_DEVICE_PROPERTIES_V2, 0x463DD662, 0x7FC4, 0x4291, 0x91, 0x1C, 0x7F, 0x4C, 0x9C, 0xCA, 0x97, 0x99);
DEFINE_PROPERTYKEY(WPD_DEVICE_FUNCTIONAL_UNIQUE_ID, 0x463DD662, 0x7FC4, 0x4291, 0x91, 0x1C, 0x7F, 0x4C, 0x9C, 0xCA, 0x97, 0x99,  2);
DEFINE_PROPERTYKEY(WPD_DEVICE_MODEL_UNIQUE_ID, 0x463DD662, 0x7FC4, 0x4291, 0x91, 0x1C, 0x7F, 0x4C, 0x9C, 0xCA, 0x97, 0x99,  3);
DEFINE_PROPERTYKEY(WPD_DEVICE_TRANSPORT, 0x463DD662, 0x7FC4, 0x4291, 0x91, 0x1C, 0x7F, 0x4C, 0x9C, 0xCA, 0x97, 0x99,  4);
DEFINE_PROPERTYKEY(WPD_DEVICE_USE_DEVICE_STAGE, 0x463DD662, 0x7FC4, 0x4291, 0x91, 0x1C, 0x7F, 0x4C, 0x9C, 0xCA, 0x97, 0x99,  5);
DEFINE_GUID(WPD_DEVICE_PROPERTIES_V3, 0x6C2B878C, 0xC2EC, 0x490D, 0xB4, 0x25, 0xD7, 0xA7, 0x5E, 0x23, 0xE5, 0xED);
DEFINE_PROPERTYKEY(WPD_DEVICE_EDP_IDENTITY, 0x6C2B878C, 0xC2EC, 0x490D, 0xB4, 0x25, 0xD7, 0xA7, 0x5E, 0x23, 0xE5, 0xED,  1);
DEFINE_GUID(WPD_SERVICE_PROPERTIES_V1, 0x7510698A, 0xCB54, 0x481C, 0xB8, 0xDB, 0x0D, 0x75, 0xC9, 0x3F, 0x1C, 0x06);
DEFINE_PROPERTYKEY(WPD_SERVICE_VERSION, 0x7510698A, 0xCB54, 0x481C, 0xB8, 0xDB, 0x0D, 0x75, 0xC9, 0x3F, 0x1C, 0x06,  2);
DEFINE_GUID(WPD_EVENT_PROPERTIES_V1, 0x15AB1953, 0xF817, 0x4FEF, 0xA9, 0x21, 0x56, 0x76, 0xE8, 0x38, 0xF6, 0xE0);
DEFINE_PROPERTYKEY(WPD_EVENT_PARAMETER_PNP_DEVICE_ID, 0x15AB1953, 0xF817, 0x4FEF, 0xA9, 0x21, 0x56, 0x76, 0xE8, 0x38, 0xF6, 0xE0,  2);
DEFINE_PROPERTYKEY(WPD_EVENT_PARAMETER_EVENT_ID, 0x15AB1953, 0xF817, 0x4FEF, 0xA9, 0x21, 0x56, 0x76, 0xE8, 0x38, 0xF6, 0xE0,  3);
DEFINE_PROPERTYKEY(WPD_EVENT_PARAMETER_OPERATION_STATE, 0x15AB1953, 0xF817, 0x4FEF, 0xA9, 0x21, 0x56, 0x76, 0xE8, 0x38, 0xF6, 0xE0,  4);
DEFINE_PROPERTYKEY(WPD_EVENT_PARAMETER_OPERATION_PROGRESS, 0x15AB1953, 0xF817, 0x4FEF, 0xA9, 0x21, 0x56, 0x76, 0xE8, 0x38, 0xF6, 0xE0,  5);
DEFINE_PROPERTYKEY(WPD_EVENT_PARAMETER_OBJECT_PARENT_PERSISTENT_UNIQUE_ID, 0x15AB1953, 0xF817, 0x4FEF, 0xA9, 0x21, 0x56, 0x76, 0xE8, 0x38, 0xF6, 0xE0,  6);
DEFINE_PROPERTYKEY(WPD_EVENT_PARAMETER_OBJECT_CREATION_COOKIE, 0x15AB1953, 0xF817, 0x4FEF, 0xA9, 0x21, 0x56, 0x76, 0xE8, 0x38, 0xF6, 0xE0,  7);
DEFINE_PROPERTYKEY(WPD_EVENT_PARAMETER_CHILD_HIERARCHY_CHANGED, 0x15AB1953, 0xF817, 0x4FEF, 0xA9, 0x21, 0x56, 0x76, 0xE8, 0x38, 0xF6, 0xE0,  8);
DEFINE_GUID(WPD_EVENT_PROPERTIES_V2, 0x52807B8A, 0x4914, 0x4323, 0x9B, 0x9A, 0x74, 0xF6, 0x54, 0xB2, 0xB8, 0x46);
DEFINE_PROPERTYKEY(WPD_EVENT_PARAMETER_SERVICE_METHOD_CONTEXT, 0x52807B8A, 0x4914, 0x4323, 0x9B, 0x9A, 0x74, 0xF6, 0x54, 0xB2, 0xB8, 0x46,  2);
DEFINE_GUID(WPD_EVENT_OPTIONS_V1, 0xB3D8DAD7, 0xA361, 0x4B83, 0x8A, 0x48, 0x5B, 0x02, 0xCE, 0x10, 0x71, 0x3B);
DEFINE_PROPERTYKEY(WPD_EVENT_OPTION_IS_BROADCAST_EVENT, 0xB3D8DAD7, 0xA361, 0x4B83, 0x8A, 0x48, 0x5B, 0x02, 0xCE, 0x10, 0x71, 0x3B,  2);
DEFINE_PROPERTYKEY(WPD_EVENT_OPTION_IS_AUTOPLAY_EVENT, 0xB3D8DAD7, 0xA361, 0x4B83, 0x8A, 0x48, 0x5B, 0x02, 0xCE, 0x10, 0x71, 0x3B,  3);
DEFINE_GUID(WPD_EVENT_ATTRIBUTES_V1, 0x10C96578, 0x2E81, 0x4111, 0xAD, 0xDE, 0xE0, 0x8C, 0xA6, 0x13, 0x8F, 0x6D);
DEFINE_PROPERTYKEY(WPD_EVENT_ATTRIBUTE_NAME, 0x10C96578, 0x2E81, 0x4111, 0xAD, 0xDE, 0xE0, 0x8C, 0xA6, 0x13, 0x8F, 0x6D,  2);
DEFINE_PROPERTYKEY(WPD_EVENT_ATTRIBUTE_PARAMETERS, 0x10C96578, 0x2E81, 0x4111, 0xAD, 0xDE, 0xE0, 0x8C, 0xA6, 0x13, 0x8F, 0x6D,  3);
DEFINE_PROPERTYKEY(WPD_EVENT_ATTRIBUTE_OPTIONS, 0x10C96578, 0x2E81, 0x4111, 0xAD, 0xDE, 0xE0, 0x8C, 0xA6, 0x13, 0x8F, 0x6D,  4);
DEFINE_GUID(WPD_API_OPTIONS_V1, 0x10E54A3E, 0x052D, 0x4777, 0xA1, 0x3C, 0xDE, 0x76, 0x14, 0xBE, 0x2B, 0xC4);
DEFINE_PROPERTYKEY(WPD_API_OPTION_USE_CLEAR_DATA_STREAM, 0x10E54A3E, 0x052D, 0x4777, 0xA1, 0x3C, 0xDE, 0x76, 0x14, 0xBE, 0x2B, 0xC4,  2);
DEFINE_PROPERTYKEY(WPD_API_OPTION_IOCTL_ACCESS, 0x10E54A3E, 0x052D, 0x4777, 0xA1, 0x3C, 0xDE, 0x76, 0x14, 0xBE, 0x2B, 0xC4, 3);
DEFINE_GUID(WPD_FORMAT_ATTRIBUTES_V1, 0xA0A02000, 0xBCAF, 0x4BE8, 0xB3, 0xF5, 0x23, 0x3F, 0x23, 0x1C, 0xF5, 0x8F);
DEFINE_PROPERTYKEY(WPD_FORMAT_ATTRIBUTE_NAME, 0xA0A02000, 0xBCAF, 0x4BE8, 0xB3, 0xF5, 0x23, 0x3F, 0x23, 0x1C, 0xF5, 0x8F,  2);
DEFINE_PROPERTYKEY(WPD_FORMAT_ATTRIBUTE_MIMETYPE, 0xA0A02000, 0xBCAF, 0x4BE8, 0xB3, 0xF5, 0x23, 0x3F, 0x23, 0x1C, 0xF5, 0x8F,  3);
DEFINE_GUID(WPD_METHOD_ATTRIBUTES_V1, 0xF17A5071, 0xF039, 0x44AF, 0x8E, 0xFE, 0x43, 0x2C, 0xF3, 0x2E, 0x43, 0x2A);
DEFINE_PROPERTYKEY(WPD_METHOD_ATTRIBUTE_NAME, 0xF17A5071, 0xF039, 0x44AF, 0x8E, 0xFE, 0x43, 0x2C, 0xF3, 0x2E, 0x43, 0x2A,  2);
DEFINE_PROPERTYKEY(WPD_METHOD_ATTRIBUTE_ASSOCIATED_FORMAT, 0xF17A5071, 0xF039, 0x44AF, 0x8E, 0xFE, 0x43, 0x2C, 0xF3, 0x2E, 0x43, 0x2A,  3);
DEFINE_PROPERTYKEY(WPD_METHOD_ATTRIBUTE_ACCESS, 0xF17A5071, 0xF039, 0x44AF, 0x8E, 0xFE, 0x43, 0x2C, 0xF3, 0x2E, 0x43, 0x2A,  4);
DEFINE_PROPERTYKEY(WPD_METHOD_ATTRIBUTE_PARAMETERS, 0xF17A5071, 0xF039, 0x44AF, 0x8E, 0xFE, 0x43, 0x2C, 0xF3, 0x2E, 0x43, 0x2A,  5);
DEFINE_GUID(WPD_PARAMETER_ATTRIBUTES_V1, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_ORDER, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58,  2);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_USAGE, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58,  3);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_FORM, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58, 4);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_DEFAULT_VALUE, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58, 5);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_RANGE_MIN, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58, 6);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_RANGE_MAX, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58, 7);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_RANGE_STEP, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58, 8);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_ENUMERATION_ELEMENTS, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58, 9);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_REGULAR_EXPRESSION, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58, 10);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_MAX_SIZE, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58, 11);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_VARTYPE, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58,  12);
DEFINE_PROPERTYKEY(WPD_PARAMETER_ATTRIBUTE_NAME, 0xE6864DD7, 0xF325, 0x45EA, 0xA1, 0xD5, 0x97, 0xCF, 0x73, 0xB6, 0xCA, 0x58,  13);
DEFINE_GUID(WPD_CATEGORY_COMMON, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A);
DEFINE_PROPERTYKEY(WPD_COMMAND_COMMON_RESET_DEVICE, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 2);
DEFINE_PROPERTYKEY(WPD_COMMAND_COMMON_GET_OBJECT_IDS_FROM_PERSISTENT_UNIQUE_IDS, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 3);
DEFINE_PROPERTYKEY(WPD_COMMAND_COMMON_SAVE_CLIENT_INFORMATION, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 4);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_COMMAND_CATEGORY, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_COMMAND_ID, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_HRESULT, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_DRIVER_ERROR_CODE, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1004);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_COMMAND_TARGET, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1006);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_PERSISTENT_UNIQUE_IDS, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1007);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_OBJECT_IDS, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1008);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_CLIENT_INFORMATION, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1009);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_CLIENT_INFORMATION_CONTEXT, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1010);
DEFINE_PROPERTYKEY(WPD_PROPERTY_COMMON_ACTIVITY_ID, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A, 1011);
DEFINE_PROPERTYKEY(WPD_OPTION_VALID_OBJECT_IDS, 0xF0422A9C, 0x5DC8, 0x4440, 0xB5, 0xBD, 0x5D, 0xF2, 0x88, 0x35, 0x65, 0x8A,  5001);
DEFINE_GUID(WPD_CATEGORY_OBJECT_ENUMERATION, 0xB7474E91, 0xE7F8, 0x4AD9, 0xB4, 0x00, 0xAD, 0x1A, 0x4B, 0x58, 0xEE, 0xEC);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_ENUMERATION_START_FIND, 0xB7474E91, 0xE7F8, 0x4AD9, 0xB4, 0x00, 0xAD, 0x1A, 0x4B, 0x58, 0xEE, 0xEC, 2);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_ENUMERATION_FIND_NEXT, 0xB7474E91, 0xE7F8, 0x4AD9, 0xB4, 0x00, 0xAD, 0x1A, 0x4B, 0x58, 0xEE, 0xEC, 3);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_ENUMERATION_END_FIND, 0xB7474E91, 0xE7F8, 0x4AD9, 0xB4, 0x00, 0xAD, 0x1A, 0x4B, 0x58, 0xEE, 0xEC, 4);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_ENUMERATION_PARENT_ID, 0xB7474E91, 0xE7F8, 0x4AD9, 0xB4, 0x00, 0xAD, 0x1A, 0x4B, 0x58, 0xEE, 0xEC, 1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_ENUMERATION_FILTER, 0xB7474E91, 0xE7F8, 0x4AD9, 0xB4, 0x00, 0xAD, 0x1A, 0x4B, 0x58, 0xEE, 0xEC, 1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_ENUMERATION_OBJECT_IDS, 0xB7474E91, 0xE7F8, 0x4AD9, 0xB4, 0x00, 0xAD, 0x1A, 0x4B, 0x58, 0xEE, 0xEC, 1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_ENUMERATION_CONTEXT, 0xB7474E91, 0xE7F8, 0x4AD9, 0xB4, 0x00, 0xAD, 0x1A, 0x4B, 0x58, 0xEE, 0xEC, 1004);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_ENUMERATION_NUM_OBJECTS_REQUESTED, 0xB7474E91, 0xE7F8, 0x4AD9, 0xB4, 0x00, 0xAD, 0x1A, 0x4B, 0x58, 0xEE, 0xEC, 1005);
DEFINE_GUID(WPD_CATEGORY_OBJECT_PROPERTIES, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_GET_SUPPORTED, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 2);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_GET_ATTRIBUTES, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 3);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_GET, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 4);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_SET, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 5);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_GET_ALL, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 6);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_DELETE, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 7);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_OBJECT_ID, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_PROPERTY_KEYS, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_PROPERTY_ATTRIBUTES, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_PROPERTY_VALUES, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 1004);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_PROPERTY_WRITE_RESULTS, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 1005);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_PROPERTY_DELETE_RESULTS, 0x9E5582E4, 0x0814, 0x44E6, 0x98, 0x1A, 0xB2, 0x99, 0x8D, 0x58, 0x38, 0x04, 1006);
DEFINE_GUID(WPD_CATEGORY_OBJECT_PROPERTIES_BULK, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_BULK_GET_VALUES_BY_OBJECT_LIST_START, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 2);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_BULK_GET_VALUES_BY_OBJECT_LIST_NEXT, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 3);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_BULK_GET_VALUES_BY_OBJECT_LIST_END, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 4);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_BULK_GET_VALUES_BY_OBJECT_FORMAT_START, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 5);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_BULK_GET_VALUES_BY_OBJECT_FORMAT_NEXT, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 6);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_BULK_GET_VALUES_BY_OBJECT_FORMAT_END, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 7);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_BULK_SET_VALUES_BY_OBJECT_LIST_START, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 8);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_BULK_SET_VALUES_BY_OBJECT_LIST_NEXT, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 9);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_PROPERTIES_BULK_SET_VALUES_BY_OBJECT_LIST_END, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 10);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_BULK_OBJECT_IDS, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_BULK_CONTEXT, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_BULK_VALUES, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_BULK_PROPERTY_KEYS, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 1004);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_BULK_DEPTH, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 1005);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_BULK_PARENT_OBJECT_ID, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 1006);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_BULK_OBJECT_FORMAT, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 1007);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_PROPERTIES_BULK_WRITE_RESULTS, 0x11C824DD, 0x04CD, 0x4E4E, 0x8C, 0x7B, 0xF6, 0xEF, 0xB7, 0x94, 0xD8, 0x4E, 1008);
DEFINE_GUID(WPD_CATEGORY_OBJECT_RESOURCES, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_GET_SUPPORTED, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 2);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_GET_ATTRIBUTES, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 3);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_OPEN, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 4);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_READ, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 5);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_WRITE, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 6);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_CLOSE, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 7);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_DELETE, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 8);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_CREATE_RESOURCE, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 9);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_REVERT, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 10);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_SEEK, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 11);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_COMMIT, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 12);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_RESOURCES_SEEK_IN_UNITS, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 13);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_OBJECT_ID, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_ACCESS_MODE, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_RESOURCE_KEYS, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_RESOURCE_ATTRIBUTES, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1004);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_CONTEXT, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1005);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_NUM_BYTES_TO_READ, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1006);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_NUM_BYTES_READ, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1007);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_NUM_BYTES_TO_WRITE, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1008);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_NUM_BYTES_WRITTEN, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1009);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_DATA, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1010);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_OPTIMAL_TRANSFER_BUFFER_SIZE, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1011);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_SEEK_OFFSET, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1012);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_SEEK_ORIGIN_FLAG, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1013);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_POSITION_FROM_START, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1014);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_SUPPORTS_UNITS, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1015);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_RESOURCES_STREAM_UNITS, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A, 1016);
DEFINE_PROPERTYKEY(WPD_OPTION_OBJECT_RESOURCES_SEEK_ON_READ_SUPPORTED, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A,  5001);
DEFINE_PROPERTYKEY(WPD_OPTION_OBJECT_RESOURCES_SEEK_ON_WRITE_SUPPORTED, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A,  5002);
DEFINE_PROPERTYKEY(WPD_OPTION_OBJECT_RESOURCES_NO_INPUT_BUFFER_ON_READ, 0xB3A2B22D, 0xA595, 0x4108, 0xBE, 0x0A, 0xFC, 0x3C, 0x96, 0x5F, 0x3D, 0x4A,  5003);
DEFINE_GUID(WPD_CATEGORY_OBJECT_MANAGEMENT, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_MANAGEMENT_CREATE_OBJECT_WITH_PROPERTIES_ONLY, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 2);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_MANAGEMENT_CREATE_OBJECT_WITH_PROPERTIES_AND_DATA, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 3);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_MANAGEMENT_WRITE_OBJECT_DATA, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 4);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_MANAGEMENT_COMMIT_OBJECT, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 5);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_MANAGEMENT_REVERT_OBJECT, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 6);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_MANAGEMENT_DELETE_OBJECTS, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 7);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_MANAGEMENT_MOVE_OBJECTS, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89,  8);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_MANAGEMENT_COPY_OBJECTS, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89,  9);
DEFINE_PROPERTYKEY(WPD_COMMAND_OBJECT_MANAGEMENT_UPDATE_OBJECT_WITH_PROPERTIES_AND_DATA, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89,  10);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_CREATION_PROPERTIES, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_CONTEXT, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_NUM_BYTES_TO_WRITE, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_NUM_BYTES_WRITTEN, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1004);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_DATA, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1005);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_OBJECT_ID, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1006);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_DELETE_OPTIONS, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1007);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_OPTIMAL_TRANSFER_BUFFER_SIZE, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1008);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_OBJECT_IDS, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1009);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_DELETE_RESULTS, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1010);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_DESTINATION_FOLDER_OBJECT_ID, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1011);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_MOVE_RESULTS, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1012);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_COPY_RESULTS, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1013);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_UPDATE_PROPERTIES, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1014);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_PROPERTY_KEYS, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1015);
DEFINE_PROPERTYKEY(WPD_PROPERTY_OBJECT_MANAGEMENT_OBJECT_FORMAT, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89, 1016);
DEFINE_PROPERTYKEY(WPD_OPTION_OBJECT_MANAGEMENT_RECURSIVE_DELETE_SUPPORTED, 0xEF1E43DD, 0xA9ED, 0x4341, 0x8B, 0xCC, 0x18, 0x61, 0x92, 0xAE, 0xA0, 0x89,  5001);
DEFINE_GUID(WPD_CATEGORY_CAPABILITIES, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_SUPPORTED_COMMANDS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 2);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_COMMAND_OPTIONS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 3);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_SUPPORTED_FUNCTIONAL_CATEGORIES, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 4);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_FUNCTIONAL_OBJECTS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 5);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_SUPPORTED_CONTENT_TYPES, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 6);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_SUPPORTED_FORMATS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 7);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_SUPPORTED_FORMAT_PROPERTIES, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 8);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_FIXED_PROPERTY_ATTRIBUTES, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 9);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_SUPPORTED_EVENTS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 10);
DEFINE_PROPERTYKEY(WPD_COMMAND_CAPABILITIES_GET_EVENT_OPTIONS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 11);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_SUPPORTED_COMMANDS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_COMMAND, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_COMMAND_OPTIONS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_FUNCTIONAL_CATEGORIES, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1004);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_FUNCTIONAL_CATEGORY, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1005);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_FUNCTIONAL_OBJECTS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1006);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_CONTENT_TYPES, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1007);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_CONTENT_TYPE, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1008);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_FORMATS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1009);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_FORMAT, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1010);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_PROPERTY_KEYS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1011);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_PROPERTY_ATTRIBUTES, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1012);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_SUPPORTED_EVENTS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1013);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_EVENT, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1014);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CAPABILITIES_EVENT_OPTIONS, 0x0CABEC78, 0x6B74, 0x41C6, 0x92, 0x16, 0x26, 0x39, 0xD1, 0xFC, 0xE3, 0x56, 1015);
DEFINE_GUID(WPD_CATEGORY_STORAGE, 0xD8F907A6, 0x34CC, 0x45FA, 0x97, 0xFB, 0xD0, 0x07, 0xFA, 0x47, 0xEC, 0x94);
DEFINE_PROPERTYKEY(WPD_COMMAND_STORAGE_FORMAT, 0xD8F907A6, 0x34CC, 0x45FA, 0x97, 0xFB, 0xD0, 0x07, 0xFA, 0x47, 0xEC, 0x94,  2);
DEFINE_PROPERTYKEY(WPD_COMMAND_STORAGE_EJECT, 0xD8F907A6, 0x34CC, 0x45FA, 0x97, 0xFB, 0xD0, 0x07, 0xFA, 0x47, 0xEC, 0x94,  4);
DEFINE_PROPERTYKEY(WPD_PROPERTY_STORAGE_OBJECT_ID, 0xD8F907A6, 0x34CC, 0x45FA, 0x97, 0xFB, 0xD0, 0x07, 0xFA, 0x47, 0xEC, 0x94,  1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_STORAGE_DESTINATION_OBJECT_ID, 0xD8F907A6, 0x34CC, 0x45FA, 0x97, 0xFB, 0xD0, 0x07, 0xFA, 0x47, 0xEC, 0x94,  1002);
DEFINE_GUID(WPD_CATEGORY_SMS, 0xAFC25D66, 0xFE0D, 0x4114, 0x90, 0x97, 0x97, 0x0C, 0x93, 0xE9, 0x20, 0xD1);
DEFINE_PROPERTYKEY(WPD_COMMAND_SMS_SEND, 0xAFC25D66, 0xFE0D, 0x4114, 0x90, 0x97, 0x97, 0x0C, 0x93, 0xE9, 0x20, 0xD1,  2);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SMS_RECIPIENT, 0xAFC25D66, 0xFE0D, 0x4114, 0x90, 0x97, 0x97, 0x0C, 0x93, 0xE9, 0x20, 0xD1,  1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SMS_MESSAGE_TYPE, 0xAFC25D66, 0xFE0D, 0x4114, 0x90, 0x97, 0x97, 0x0C, 0x93, 0xE9, 0x20, 0xD1,  1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SMS_TEXT_MESSAGE, 0xAFC25D66, 0xFE0D, 0x4114, 0x90, 0x97, 0x97, 0x0C, 0x93, 0xE9, 0x20, 0xD1,  1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SMS_BINARY_MESSAGE, 0xAFC25D66, 0xFE0D, 0x4114, 0x90, 0x97, 0x97, 0x0C, 0x93, 0xE9, 0x20, 0xD1,  1004);
DEFINE_PROPERTYKEY(WPD_OPTION_SMS_BINARY_MESSAGE_SUPPORTED, 0xAFC25D66, 0xFE0D, 0x4114, 0x90, 0x97, 0x97, 0x0C, 0x93, 0xE9, 0x20, 0xD1,  5001);
DEFINE_GUID(WPD_CATEGORY_STILL_IMAGE_CAPTURE, 0x4FCD6982, 0x22A2, 0x4B05, 0xA4, 0x8B, 0x62, 0xD3, 0x8B, 0xF2, 0x7B, 0x32);
DEFINE_PROPERTYKEY(WPD_COMMAND_STILL_IMAGE_CAPTURE_INITIATE, 0x4FCD6982, 0x22A2, 0x4B05, 0xA4, 0x8B, 0x62, 0xD3, 0x8B, 0xF2, 0x7B, 0x32,  2);
DEFINE_GUID(WPD_CATEGORY_MEDIA_CAPTURE, 0x59B433BA, 0xFE44, 0x4D8D, 0x80, 0x8C, 0x6B, 0xCB, 0x9B, 0x0F, 0x15, 0xE8);
DEFINE_PROPERTYKEY(WPD_COMMAND_MEDIA_CAPTURE_START, 0x59B433BA, 0xFE44, 0x4D8D, 0x80, 0x8C, 0x6B, 0xCB, 0x9B, 0x0F, 0x15, 0xE8,  2);
DEFINE_PROPERTYKEY(WPD_COMMAND_MEDIA_CAPTURE_STOP, 0x59B433BA, 0xFE44, 0x4D8D, 0x80, 0x8C, 0x6B, 0xCB, 0x9B, 0x0F, 0x15, 0xE8,  3);
DEFINE_PROPERTYKEY(WPD_COMMAND_MEDIA_CAPTURE_PAUSE, 0x59B433BA, 0xFE44, 0x4D8D, 0x80, 0x8C, 0x6B, 0xCB, 0x9B, 0x0F, 0x15, 0xE8,  4);
DEFINE_GUID(WPD_CATEGORY_DEVICE_HINTS, 0x0D5FB92B, 0xCB46, 0x4C4F, 0x83, 0x43, 0x0B, 0xC3, 0xD3, 0xF1, 0x7C, 0x84);
DEFINE_PROPERTYKEY(WPD_COMMAND_DEVICE_HINTS_GET_CONTENT_LOCATION, 0x0D5FB92B, 0xCB46, 0x4C4F, 0x83, 0x43, 0x0B, 0xC3, 0xD3, 0xF1, 0x7C, 0x84,  2);
DEFINE_PROPERTYKEY(WPD_PROPERTY_DEVICE_HINTS_CONTENT_TYPE, 0x0D5FB92B, 0xCB46, 0x4C4F, 0x83, 0x43, 0x0B, 0xC3, 0xD3, 0xF1, 0x7C, 0x84,  1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_DEVICE_HINTS_CONTENT_LOCATIONS, 0x0D5FB92B, 0xCB46, 0x4C4F, 0x83, 0x43, 0x0B, 0xC3, 0xD3, 0xF1, 0x7C, 0x84,  1002);
DEFINE_GUID(WPD_CLASS_EXTENSION_V1, 0x33FB0D11, 0x64A3, 0x4FAC, 0xB4, 0xC7, 0x3D, 0xFE, 0xAA, 0x99, 0xB0, 0x51);
DEFINE_PROPERTYKEY(WPD_COMMAND_CLASS_EXTENSION_WRITE_DEVICE_INFORMATION, 0x33FB0D11, 0x64A3, 0x4FAC, 0xB4, 0xC7, 0x3D, 0xFE, 0xAA, 0x99, 0xB0, 0x51,  2);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CLASS_EXTENSION_DEVICE_INFORMATION_VALUES, 0x33FB0D11, 0x64A3, 0x4FAC, 0xB4, 0xC7, 0x3D, 0xFE, 0xAA, 0x99, 0xB0, 0x51,  1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CLASS_EXTENSION_DEVICE_INFORMATION_WRITE_RESULTS, 0x33FB0D11, 0x64A3, 0x4FAC, 0xB4, 0xC7, 0x3D, 0xFE, 0xAA, 0x99, 0xB0, 0x51, 1002);
DEFINE_GUID(WPD_CLASS_EXTENSION_V2, 0x7F0779B5, 0xFA2B, 0x4766, 0x9C, 0xB2, 0xF7, 0x3B, 0xA3, 0x0B, 0x67, 0x58);
DEFINE_PROPERTYKEY(WPD_COMMAND_CLASS_EXTENSION_REGISTER_SERVICE_INTERFACES, 0x7F0779B5, 0xFA2B, 0x4766, 0x9C, 0xB2, 0xF7, 0x3B, 0xA3, 0x0B, 0x67, 0x58,  2);
DEFINE_PROPERTYKEY(WPD_COMMAND_CLASS_EXTENSION_UNREGISTER_SERVICE_INTERFACES, 0x7F0779B5, 0xFA2B, 0x4766, 0x9C, 0xB2, 0xF7, 0x3B, 0xA3, 0x0B, 0x67, 0x58,  3);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CLASS_EXTENSION_SERVICE_OBJECT_ID, 0x7F0779B5, 0xFA2B, 0x4766, 0x9C, 0xB2, 0xF7, 0x3B, 0xA3, 0x0B, 0x67, 0x58,  1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CLASS_EXTENSION_SERVICE_INTERFACES, 0x7F0779B5, 0xFA2B, 0x4766, 0x9C, 0xB2, 0xF7, 0x3B, 0xA3, 0x0B, 0x67, 0x58, 1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_CLASS_EXTENSION_SERVICE_REGISTRATION_RESULTS, 0x7F0779B5, 0xFA2B, 0x4766, 0x9C, 0xB2, 0xF7, 0x3B, 0xA3, 0x0B, 0x67, 0x58, 1003);
DEFINE_GUID(WPD_CATEGORY_NETWORK_CONFIGURATION, 0x78F9C6FC, 0x79B8, 0x473C, 0x90, 0x60, 0x6B, 0xD2, 0x3D, 0xD0, 0x72, 0xC4);
DEFINE_PROPERTYKEY(WPD_COMMAND_GENERATE_KEYPAIR, 0x78F9C6FC, 0x79B8, 0x473C, 0x90, 0x60, 0x6B, 0xD2, 0x3D, 0xD0, 0x72, 0xC4,  2);
DEFINE_PROPERTYKEY(WPD_COMMAND_COMMIT_KEYPAIR, 0x78F9C6FC, 0x79B8, 0x473C, 0x90, 0x60, 0x6B, 0xD2, 0x3D, 0xD0, 0x72, 0xC4,  3);
DEFINE_PROPERTYKEY(WPD_COMMAND_PROCESS_WIRELESS_PROFILE, 0x78F9C6FC, 0x79B8, 0x473C, 0x90, 0x60, 0x6B, 0xD2, 0x3D, 0xD0, 0x72, 0xC4,  4);
DEFINE_PROPERTYKEY(WPD_PROPERTY_PUBLIC_KEY, 0x78F9C6FC, 0x79B8, 0x473C, 0x90, 0x60, 0x6B, 0xD2, 0x3D, 0xD0, 0x72, 0xC4,  1001);
DEFINE_GUID(WPD_CATEGORY_SERVICE_COMMON, 0x322F071D, 0x36EF, 0x477F, 0xB4, 0xB5, 0x6F, 0x52, 0xD7, 0x34, 0xBA, 0xEE);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_COMMON_GET_SERVICE_OBJECT_ID, 0x322F071D, 0x36EF, 0x477F, 0xB4, 0xB5, 0x6F, 0x52, 0xD7, 0x34, 0xBA, 0xEE,  2);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_OBJECT_ID, 0x322F071D, 0x36EF, 0x477F, 0xB4, 0xB5, 0x6F, 0x52, 0xD7, 0x34, 0xBA, 0xEE,  1001);
DEFINE_GUID(WPD_CATEGORY_SERVICE_CAPABILITIES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_SUPPORTED_METHODS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  2);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_SUPPORTED_METHODS_BY_FORMAT, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  3);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_METHOD_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  4);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_METHOD_PARAMETER_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  5);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_SUPPORTED_FORMATS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  6);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_FORMAT_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  7);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_SUPPORTED_FORMAT_PROPERTIES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  8);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_FORMAT_PROPERTY_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  9);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_SUPPORTED_EVENTS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  10);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_EVENT_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  11);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_EVENT_PARAMETER_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  12);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_INHERITED_SERVICES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  13);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_FORMAT_RENDERING_PROFILES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  14);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_SUPPORTED_COMMANDS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  15);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_CAPABILITIES_GET_COMMAND_OPTIONS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89, 16);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_SUPPORTED_METHODS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_FORMAT, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_METHOD, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_METHOD_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1004);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_PARAMETER, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1005);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_PARAMETER_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1006);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_FORMATS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1007);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_FORMAT_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1008);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_PROPERTY_KEYS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1009);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_PROPERTY_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1010);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_SUPPORTED_EVENTS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1011);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_EVENT, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1012);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_EVENT_ATTRIBUTES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1013);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_INHERITANCE_TYPE, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1014);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_INHERITED_SERVICES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1015);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_RENDERING_PROFILES, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89,  1016);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_SUPPORTED_COMMANDS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89, 1017);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_COMMAND, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89, 1018);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_CAPABILITIES_COMMAND_OPTIONS, 0x24457E74, 0x2E9F, 0x44F9, 0x8C, 0x57, 0x1D, 0x1B, 0xCB, 0x17, 0x0B, 0x89, 1019);
DEFINE_GUID(WPD_CATEGORY_SERVICE_METHODS, 0x2D521CA8, 0xC1B0, 0x4268, 0xA3, 0x42, 0xCF, 0x19, 0x32, 0x15, 0x69, 0xBC);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_METHODS_START_INVOKE, 0x2D521CA8, 0xC1B0, 0x4268, 0xA3, 0x42, 0xCF, 0x19, 0x32, 0x15, 0x69, 0xBC,  2);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_METHODS_CANCEL_INVOKE, 0x2D521CA8, 0xC1B0, 0x4268, 0xA3, 0x42, 0xCF, 0x19, 0x32, 0x15, 0x69, 0xBC,  3);
DEFINE_PROPERTYKEY(WPD_COMMAND_SERVICE_METHODS_END_INVOKE, 0x2D521CA8, 0xC1B0, 0x4268, 0xA3, 0x42, 0xCF, 0x19, 0x32, 0x15, 0x69, 0xBC,  4);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_METHOD, 0x2D521CA8, 0xC1B0, 0x4268, 0xA3, 0x42, 0xCF, 0x19, 0x32, 0x15, 0x69, 0xBC,  1001);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_METHOD_PARAMETER_VALUES, 0x2D521CA8, 0xC1B0, 0x4268, 0xA3, 0x42, 0xCF, 0x19, 0x32, 0x15, 0x69, 0xBC,  1002);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_METHOD_RESULT_VALUES, 0x2D521CA8, 0xC1B0, 0x4268, 0xA3, 0x42, 0xCF, 0x19, 0x32, 0x15, 0x69, 0xBC,  1003);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_METHOD_CONTEXT, 0x2D521CA8, 0xC1B0, 0x4268, 0xA3, 0x42, 0xCF, 0x19, 0x32, 0x15, 0x69, 0xBC,  1004);
DEFINE_PROPERTYKEY(WPD_PROPERTY_SERVICE_METHOD_HRESULT, 0x2D521CA8, 0xC1B0, 0x4268, 0xA3, 0x42, 0xCF, 0x19, 0x32, 0x15, 0x69, 0xBC,  1005);
DEFINE_PROPERTYKEY(WPD_RESOURCE_DEFAULT, 0xE81E79BE, 0x34F0, 0x41BF, 0xB5, 0x3F, 0xF1, 0xA0, 0x6A, 0xE8, 0x78, 0x42, 0);
DEFINE_PROPERTYKEY(WPD_RESOURCE_CONTACT_PHOTO, 0x2C4D6803, 0x80EA, 0x4580, 0xAF, 0x9A, 0x5B, 0xE1, 0xA2, 0x3E, 0xDD, 0xCB, 0);
DEFINE_PROPERTYKEY(WPD_RESOURCE_THUMBNAIL, 0xC7C407BA, 0x98FA, 0x46B5, 0x99, 0x60, 0x23, 0xFE, 0xC1, 0x24, 0xCF, 0xDE, 0);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ICON, 0xF195FED8, 0xAA28, 0x4EE3, 0xB1, 0x53, 0xE1, 0x82, 0xDD, 0x5E, 0xDC, 0x39, 0);
DEFINE_PROPERTYKEY(WPD_RESOURCE_AUDIO_CLIP, 0x3BC13982, 0x85B1, 0x48E0, 0x95, 0xA6, 0x8D, 0x3A, 0xD0, 0x6B, 0xE1, 0x17, 0);
DEFINE_PROPERTYKEY(WPD_RESOURCE_ALBUM_ART, 0xF02AA354, 0x2300, 0x4E2D, 0xA1, 0xB9, 0x3B, 0x67, 0x30, 0xF7, 0xFA, 0x21, 0);
DEFINE_PROPERTYKEY(WPD_RESOURCE_GENERIC, 0xB9B9F515, 0xBA70, 0x4647, 0x94, 0xDC, 0xFA, 0x49, 0x25, 0xE9, 0x5A, 0x07, 0);
DEFINE_PROPERTYKEY(WPD_RESOURCE_VIDEO_CLIP, 0xB566EE42, 0x6368, 0x4290, 0x86, 0x62, 0x70, 0x18, 0x2F, 0xB7, 0x9F, 0x20, 0);
DEFINE_PROPERTYKEY(WPD_RESOURCE_BRANDING_ART, 0xB633B1AE, 0x6CAF, 0x4A87, 0x95, 0x89, 0x22, 0xDE, 0xD6, 0xDD, 0x58, 0x99, 0);

#ifndef WPD_SERVICES_STRICT
DEFINE_GUID(WPD_OBJECT_FORMAT_PROPERTIES_ONLY, 0x30010000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_UNSPECIFIED, 0x30000000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_SCRIPT, 0x30020000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_EXECUTABLE, 0x30030000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_TEXT, 0x30040000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_HTML, 0x30050000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_DPOF, 0x30060000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_AIFF, 0x30070000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_WAVE, 0x30080000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MP3, 0x30090000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_AVI, 0x300A0000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MPEG, 0x300B0000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_ASF, 0x300C0000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_EXIF, 0x38010000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_TIFFEP, 0x38020000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_FLASHPIX, 0x38030000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_BMP, 0x38040000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_CIFF, 0x38050000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_GIF, 0x38070000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_JFIF, 0x38080000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_PCD, 0x38090000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_PICT, 0x380A0000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_PNG, 0x380B0000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_TIFF, 0x380D0000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_TIFFIT, 0x380E0000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_JP2, 0x380F0000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_JPX, 0x38100000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_WBMP, 0xB8030000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_JPEGXR, 0xB8040000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_WINDOWSIMAGEFORMAT, 0xB8810000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_WMA, 0xB9010000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_WMV, 0xB9810000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_WPLPLAYLIST, 0xBA100000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_M3UPLAYLIST, 0xBA110000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MPLPLAYLIST, 0xBA120000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_ASXPLAYLIST, 0xBA130000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_PLSPLAYLIST, 0xBA140000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_ABSTRACT_CONTACT_GROUP, 0xBA060000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_ABSTRACT_MEDIA_CAST, 0xBA0B0000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_VCALENDAR1, 0xBE020000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_ICALENDAR, 0xBE030000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_ABSTRACT_CONTACT, 0xBB810000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_VCARD2, 0xBB820000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_VCARD3, 0xBB830000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_XML, 0xBA820000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_AAC, 0xB9030000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_AUDIBLE, 0xB9040000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_FLAC, 0xB9060000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_QCELP, 0xB9070000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_AMR, 0xB9080000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_OGG, 0xB9020000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MP4, 0xB9820000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MP2, 0xB9830000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MICROSOFT_WORD, 0xBA830000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MHT_COMPILED_HTML, 0xBA840000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MICROSOFT_EXCEL, 0xBA850000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MICROSOFT_POWERPOINT, 0xBA860000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_3GP, 0xB9840000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_3G2, 0xB9850000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_AVCHD, 0xB9860000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_ATSCTS, 0xB9870000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_DVBTS, 0xB9880000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xC5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_GUID(WPD_OBJECT_FORMAT_MKV, 0xB9900000, 0xAE6C, 0x4804, 0x98, 0xBA, 0xc5, 0x7B, 0x46, 0x96, 0x5F, 0xE7);
DEFINE_PROPERTYKEY(WPD_OBJECT_ID, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 2);
DEFINE_PROPERTYKEY(WPD_OBJECT_PARENT_ID, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 3);
DEFINE_PROPERTYKEY(WPD_OBJECT_NAME, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 4);
DEFINE_PROPERTYKEY(WPD_OBJECT_PERSISTENT_UNIQUE_ID, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 5);
DEFINE_PROPERTYKEY(WPD_OBJECT_FORMAT, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 6);
DEFINE_PROPERTYKEY(WPD_OBJECT_ISHIDDEN, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 9);
DEFINE_PROPERTYKEY(WPD_OBJECT_ISSYSTEM, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 10);
DEFINE_PROPERTYKEY(WPD_OBJECT_SIZE, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 11);
DEFINE_PROPERTYKEY(WPD_OBJECT_ORIGINAL_FILE_NAME, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 12);
DEFINE_PROPERTYKEY(WPD_OBJECT_NON_CONSUMABLE, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 13);
DEFINE_PROPERTYKEY(WPD_OBJECT_KEYWORDS, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 15);
DEFINE_PROPERTYKEY(WPD_OBJECT_SYNC_ID, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 16);
DEFINE_PROPERTYKEY(WPD_OBJECT_IS_DRM_PROTECTED, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 17);
DEFINE_PROPERTYKEY(WPD_OBJECT_DATE_CREATED, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 18);
DEFINE_PROPERTYKEY(WPD_OBJECT_DATE_MODIFIED, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 19);
DEFINE_PROPERTYKEY(WPD_OBJECT_DATE_AUTHORED, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 20);
DEFINE_PROPERTYKEY(WPD_OBJECT_BACK_REFERENCES, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 21);
DEFINE_PROPERTYKEY(WPD_OBJECT_CAN_DELETE, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 26);
DEFINE_PROPERTYKEY(WPD_OBJECT_LANGUAGE_LOCALE, 0xEF6B490D, 0x5CD8, 0x437A, 0xAF, 0xFC, 0xDA, 0x8B, 0x60, 0xEE, 0x4A, 0x3C, 27);
DEFINE_GUID(WPD_FOLDER_OBJECT_PROPERTIES_V1, 0x7E9A7ABF, 0xE568, 0x4B34, 0xAA, 0x2F, 0x13, 0xBB, 0x12, 0xAB, 0x17, 0x7D);
DEFINE_PROPERTYKEY(WPD_FOLDER_CONTENT_TYPES_ALLOWED, 0x7E9A7ABF, 0xE568, 0x4B34, 0xAA, 0x2F, 0x13, 0xBB, 0x12, 0xAB, 0x17, 0x7D, 2);
DEFINE_GUID(WPD_IMAGE_OBJECT_PROPERTIES_V1, 0x63D64908, 0x9FA1, 0x479F, 0x85, 0xBA, 0x99, 0x52, 0x21, 0x64, 0x47, 0xDB);
DEFINE_PROPERTYKEY(WPD_IMAGE_BITDEPTH, 0x63D64908, 0x9FA1, 0x479F, 0x85, 0xBA, 0x99, 0x52, 0x21, 0x64, 0x47, 0xDB, 3);
DEFINE_PROPERTYKEY(WPD_IMAGE_CROPPED_STATUS, 0x63D64908, 0x9FA1, 0x479F, 0x85, 0xBA, 0x99, 0x52, 0x21, 0x64, 0x47, 0xDB, 4);
DEFINE_PROPERTYKEY(WPD_IMAGE_COLOR_CORRECTED_STATUS, 0x63D64908, 0x9FA1, 0x479F, 0x85, 0xBA, 0x99, 0x52, 0x21, 0x64, 0x47, 0xDB, 5);
DEFINE_PROPERTYKEY(WPD_IMAGE_FNUMBER, 0x63D64908, 0x9FA1, 0x479F, 0x85, 0xBA, 0x99, 0x52, 0x21, 0x64, 0x47, 0xDB, 6);
DEFINE_PROPERTYKEY(WPD_IMAGE_EXPOSURE_TIME, 0x63D64908, 0x9FA1, 0x479F, 0x85, 0xBA, 0x99, 0x52, 0x21, 0x64, 0x47, 0xDB, 7);
DEFINE_PROPERTYKEY(WPD_IMAGE_EXPOSURE_INDEX, 0x63D64908, 0x9FA1, 0x479F, 0x85, 0xBA, 0x99, 0x52, 0x21, 0x64, 0x47, 0xDB, 8);
DEFINE_PROPERTYKEY(WPD_IMAGE_HORIZONTAL_RESOLUTION, 0x63D64908, 0x9FA1, 0x479F, 0x85, 0xBA, 0x99, 0x52, 0x21, 0x64, 0x47, 0xDB, 9);
DEFINE_PROPERTYKEY(WPD_IMAGE_VERTICAL_RESOLUTION, 0x63D64908, 0x9FA1, 0x479F, 0x85, 0xBA, 0x99, 0x52, 0x21, 0x64, 0x47, 0xDB, 10);
DEFINE_GUID(WPD_DOCUMENT_OBJECT_PROPERTIES_V1, 0x0B110203, 0xEB95, 0x4F02, 0x93, 0xE0, 0x97, 0xC6, 0x31, 0x49, 0x3A, 0xD5);
DEFINE_GUID(WPD_MEDIA_PROPERTIES_V1, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8);
DEFINE_PROPERTYKEY(WPD_MEDIA_TOTAL_BITRATE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 2);
DEFINE_PROPERTYKEY(WPD_MEDIA_BITRATE_TYPE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 3);
DEFINE_PROPERTYKEY(WPD_MEDIA_COPYRIGHT, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 4);
DEFINE_PROPERTYKEY(WPD_MEDIA_SUBSCRIPTION_CONTENT_ID, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 5);
DEFINE_PROPERTYKEY(WPD_MEDIA_USE_COUNT, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 6);
DEFINE_PROPERTYKEY(WPD_MEDIA_SKIP_COUNT, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 7);
DEFINE_PROPERTYKEY(WPD_MEDIA_LAST_ACCESSED_TIME, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 8);
DEFINE_PROPERTYKEY(WPD_MEDIA_PARENTAL_RATING, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 9);
DEFINE_PROPERTYKEY(WPD_MEDIA_META_GENRE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 10);
DEFINE_PROPERTYKEY(WPD_MEDIA_COMPOSER, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 11);
DEFINE_PROPERTYKEY(WPD_MEDIA_EFFECTIVE_RATING, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 12);
DEFINE_PROPERTYKEY(WPD_MEDIA_SUB_TITLE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 13);
DEFINE_PROPERTYKEY(WPD_MEDIA_RELEASE_DATE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 14);
DEFINE_PROPERTYKEY(WPD_MEDIA_SAMPLE_RATE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 15);
DEFINE_PROPERTYKEY(WPD_MEDIA_STAR_RATING, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 16);
DEFINE_PROPERTYKEY(WPD_MEDIA_USER_EFFECTIVE_RATING, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 17);
DEFINE_PROPERTYKEY(WPD_MEDIA_TITLE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 18);
DEFINE_PROPERTYKEY(WPD_MEDIA_DURATION, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 19);
DEFINE_PROPERTYKEY(WPD_MEDIA_BUY_NOW, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 20);
DEFINE_PROPERTYKEY(WPD_MEDIA_ENCODING_PROFILE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 21);
DEFINE_PROPERTYKEY(WPD_MEDIA_WIDTH, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 22);
DEFINE_PROPERTYKEY(WPD_MEDIA_HEIGHT, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 23);
DEFINE_PROPERTYKEY(WPD_MEDIA_ARTIST, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 24);
DEFINE_PROPERTYKEY(WPD_MEDIA_ALBUM_ARTIST, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 25);
DEFINE_PROPERTYKEY(WPD_MEDIA_OWNER, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 26);
DEFINE_PROPERTYKEY(WPD_MEDIA_MANAGING_EDITOR, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 27);
DEFINE_PROPERTYKEY(WPD_MEDIA_WEBMASTER, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 28);
DEFINE_PROPERTYKEY(WPD_MEDIA_SOURCE_URL, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 29);
DEFINE_PROPERTYKEY(WPD_MEDIA_DESTINATION_URL, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 30);
DEFINE_PROPERTYKEY(WPD_MEDIA_DESCRIPTION, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 31);
DEFINE_PROPERTYKEY(WPD_MEDIA_GENRE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 32);
DEFINE_PROPERTYKEY(WPD_MEDIA_TIME_BOOKMARK, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 33);
DEFINE_PROPERTYKEY(WPD_MEDIA_OBJECT_BOOKMARK, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 34);
DEFINE_PROPERTYKEY(WPD_MEDIA_LAST_BUILD_DATE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 35);
DEFINE_PROPERTYKEY(WPD_MEDIA_BYTE_BOOKMARK, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 36);
DEFINE_PROPERTYKEY(WPD_MEDIA_TIME_TO_LIVE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 37);
DEFINE_PROPERTYKEY(WPD_MEDIA_GUID, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 38);
DEFINE_PROPERTYKEY(WPD_MEDIA_SUB_DESCRIPTION, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 39);
DEFINE_PROPERTYKEY(WPD_MEDIA_AUDIO_ENCODING_PROFILE, 0x2ED8BA05, 0x0AD3, 0x42DC, 0xB0, 0xD0, 0xBC, 0x95, 0xAC, 0x39, 0x6A, 0xC8, 49);
DEFINE_GUID(WPD_CONTACT_OBJECT_PROPERTIES_V1, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B);
DEFINE_PROPERTYKEY(WPD_CONTACT_DISPLAY_NAME, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 2);
DEFINE_PROPERTYKEY(WPD_CONTACT_FIRST_NAME, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 3);
DEFINE_PROPERTYKEY(WPD_CONTACT_MIDDLE_NAMES, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 4);
DEFINE_PROPERTYKEY(WPD_CONTACT_LAST_NAME, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 5);
DEFINE_PROPERTYKEY(WPD_CONTACT_PREFIX, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 6);
DEFINE_PROPERTYKEY(WPD_CONTACT_SUFFIX, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 7);
DEFINE_PROPERTYKEY(WPD_CONTACT_PHONETIC_FIRST_NAME, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 8);
DEFINE_PROPERTYKEY(WPD_CONTACT_PHONETIC_LAST_NAME, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 9);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_FULL_POSTAL_ADDRESS, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 10);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_POSTAL_ADDRESS_LINE1, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 11);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_POSTAL_ADDRESS_LINE2, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 12);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_POSTAL_ADDRESS_CITY, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 13);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_POSTAL_ADDRESS_REGION, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 14);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_POSTAL_ADDRESS_POSTAL_CODE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 15);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_POSTAL_ADDRESS_COUNTRY, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 16);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_FULL_POSTAL_ADDRESS, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 17);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_POSTAL_ADDRESS_LINE1, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 18);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_POSTAL_ADDRESS_LINE2, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 19);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_POSTAL_ADDRESS_CITY, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 20);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_POSTAL_ADDRESS_REGION, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 21);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_POSTAL_ADDRESS_POSTAL_CODE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 22);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_POSTAL_ADDRESS_COUNTRY, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 23);
DEFINE_PROPERTYKEY(WPD_CONTACT_OTHER_FULL_POSTAL_ADDRESS, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 24);
DEFINE_PROPERTYKEY(WPD_CONTACT_OTHER_POSTAL_ADDRESS_LINE1, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 25);
DEFINE_PROPERTYKEY(WPD_CONTACT_OTHER_POSTAL_ADDRESS_LINE2, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 26);
DEFINE_PROPERTYKEY(WPD_CONTACT_OTHER_POSTAL_ADDRESS_CITY, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 27);
DEFINE_PROPERTYKEY(WPD_CONTACT_OTHER_POSTAL_ADDRESS_REGION, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 28);
DEFINE_PROPERTYKEY(WPD_CONTACT_OTHER_POSTAL_ADDRESS_POSTAL_CODE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 29);
DEFINE_PROPERTYKEY(WPD_CONTACT_OTHER_POSTAL_ADDRESS_POSTAL_COUNTRY, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 30);
DEFINE_PROPERTYKEY(WPD_CONTACT_PRIMARY_EMAIL_ADDRESS, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 31);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_EMAIL, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 32);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_EMAIL2, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 33);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_EMAIL, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 34);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_EMAIL2, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 35);
DEFINE_PROPERTYKEY(WPD_CONTACT_OTHER_EMAILS, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 36);
DEFINE_PROPERTYKEY(WPD_CONTACT_PRIMARY_PHONE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 37);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_PHONE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 38);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_PHONE2, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 39);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_PHONE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 40);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_PHONE2, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 41);
DEFINE_PROPERTYKEY(WPD_CONTACT_MOBILE_PHONE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 42);
DEFINE_PROPERTYKEY(WPD_CONTACT_MOBILE_PHONE2, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 43);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_FAX, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 44);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_FAX, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 45);
DEFINE_PROPERTYKEY(WPD_CONTACT_PAGER, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 46);
DEFINE_PROPERTYKEY(WPD_CONTACT_OTHER_PHONES, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 47);
DEFINE_PROPERTYKEY(WPD_CONTACT_PRIMARY_WEB_ADDRESS, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 48);
DEFINE_PROPERTYKEY(WPD_CONTACT_PERSONAL_WEB_ADDRESS, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 49);
DEFINE_PROPERTYKEY(WPD_CONTACT_BUSINESS_WEB_ADDRESS, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 50);
DEFINE_PROPERTYKEY(WPD_CONTACT_INSTANT_MESSENGER, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 51);
DEFINE_PROPERTYKEY(WPD_CONTACT_INSTANT_MESSENGER2, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 52);
DEFINE_PROPERTYKEY(WPD_CONTACT_INSTANT_MESSENGER3, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 53);
DEFINE_PROPERTYKEY(WPD_CONTACT_COMPANY_NAME, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 54);
DEFINE_PROPERTYKEY(WPD_CONTACT_PHONETIC_COMPANY_NAME, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 55);
DEFINE_PROPERTYKEY(WPD_CONTACT_ROLE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 56);
DEFINE_PROPERTYKEY(WPD_CONTACT_BIRTHDATE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 57);
DEFINE_PROPERTYKEY(WPD_CONTACT_PRIMARY_FAX, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 58);
DEFINE_PROPERTYKEY(WPD_CONTACT_SPOUSE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 59);
DEFINE_PROPERTYKEY(WPD_CONTACT_CHILDREN, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 60);
DEFINE_PROPERTYKEY(WPD_CONTACT_ASSISTANT, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 61);
DEFINE_PROPERTYKEY(WPD_CONTACT_ANNIVERSARY_DATE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 62);
DEFINE_PROPERTYKEY(WPD_CONTACT_RINGTONE, 0xFBD4FDAB, 0x987D, 0x4777, 0xB3, 0xF9, 0x72, 0x61, 0x85, 0xA9, 0x31, 0x2B, 63);
DEFINE_GUID(WPD_MUSIC_OBJECT_PROPERTIES_V1, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6);
DEFINE_PROPERTYKEY(WPD_MUSIC_ALBUM, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6, 3);
DEFINE_PROPERTYKEY(WPD_MUSIC_TRACK, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6, 4);
DEFINE_PROPERTYKEY(WPD_MUSIC_LYRICS, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6, 6);
DEFINE_PROPERTYKEY(WPD_MUSIC_MOOD, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6, 8);
DEFINE_PROPERTYKEY(WPD_AUDIO_BITRATE, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6, 9);
DEFINE_PROPERTYKEY(WPD_AUDIO_CHANNEL_COUNT, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6, 10);
DEFINE_PROPERTYKEY(WPD_AUDIO_FORMAT_CODE, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6, 11);
DEFINE_PROPERTYKEY(WPD_AUDIO_BIT_DEPTH, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6, 12);
DEFINE_PROPERTYKEY(WPD_AUDIO_BLOCK_ALIGNMENT, 0xB324F56A, 0xDC5D, 0x46E5, 0xB6, 0xDF, 0xD2, 0xEA, 0x41, 0x48, 0x88, 0xC6, 13);
DEFINE_GUID(WPD_VIDEO_OBJECT_PROPERTIES_V1, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A);
DEFINE_PROPERTYKEY(WPD_VIDEO_AUTHOR, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 2);
DEFINE_PROPERTYKEY(WPD_VIDEO_RECORDEDTV_STATION_NAME, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 4);
DEFINE_PROPERTYKEY(WPD_VIDEO_RECORDEDTV_CHANNEL_NUMBER, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 5);
DEFINE_PROPERTYKEY(WPD_VIDEO_RECORDEDTV_REPEAT, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 7);
DEFINE_PROPERTYKEY(WPD_VIDEO_BUFFER_SIZE, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 8);
DEFINE_PROPERTYKEY(WPD_VIDEO_CREDITS, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 9);
DEFINE_PROPERTYKEY(WPD_VIDEO_KEY_FRAME_DISTANCE, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 10);
DEFINE_PROPERTYKEY(WPD_VIDEO_QUALITY_SETTING, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 11);
DEFINE_PROPERTYKEY(WPD_VIDEO_SCAN_TYPE, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 12);
DEFINE_PROPERTYKEY(WPD_VIDEO_BITRATE, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 13);
DEFINE_PROPERTYKEY(WPD_VIDEO_FOURCC_CODE, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 14);
DEFINE_PROPERTYKEY(WPD_VIDEO_FRAMERATE, 0x346F2163, 0xF998, 0x4146, 0x8B, 0x01, 0xD1, 0x9B, 0x4C, 0x00, 0xDE, 0x9A, 15);
DEFINE_GUID(WPD_COMMON_INFORMATION_OBJECT_PROPERTIES_V1, 0xB28AE94B, 0x05A4, 0x4E8E, 0xBE, 0x01, 0x72, 0xCC, 0x7E, 0x09, 0x9D, 0x8F);
DEFINE_PROPERTYKEY(WPD_COMMON_INFORMATION_SUBJECT, 0xB28AE94B, 0x05A4, 0x4E8E, 0xBE, 0x01, 0x72, 0xCC, 0x7E, 0x09, 0x9D, 0x8F, 2);
DEFINE_PROPERTYKEY(WPD_COMMON_INFORMATION_BODY_TEXT, 0xB28AE94B, 0x05A4, 0x4E8E, 0xBE, 0x01, 0x72, 0xCC, 0x7E, 0x09, 0x9D, 0x8F, 3);
DEFINE_PROPERTYKEY(WPD_COMMON_INFORMATION_PRIORITY, 0xB28AE94B, 0x05A4, 0x4E8E, 0xBE, 0x01, 0x72, 0xCC, 0x7E, 0x09, 0x9D, 0x8F, 4);
DEFINE_PROPERTYKEY(WPD_COMMON_INFORMATION_START_DATETIME, 0xB28AE94B, 0x05A4, 0x4E8E, 0xBE, 0x01, 0x72, 0xCC, 0x7E, 0x09, 0x9D, 0x8F, 5);
DEFINE_PROPERTYKEY(WPD_COMMON_INFORMATION_END_DATETIME, 0xB28AE94B, 0x05A4, 0x4E8E, 0xBE, 0x01, 0x72, 0xCC, 0x7E, 0x09, 0x9D, 0x8F, 6);
DEFINE_PROPERTYKEY(WPD_COMMON_INFORMATION_NOTES, 0xB28AE94B, 0x05A4, 0x4E8E, 0xBE, 0x01, 0x72, 0xCC, 0x7E, 0x09, 0x9D, 0x8F, 7);
DEFINE_GUID(WPD_MEMO_OBJECT_PROPERTIES_V1, 0x5FFBFC7B, 0x7483, 0x41AD, 0xAF, 0xB9, 0xDA, 0x3F, 0x4E, 0x59, 0x2B, 0x8D);
DEFINE_GUID(WPD_EMAIL_OBJECT_PROPERTIES_V1, 0x41F8F65A, 0x5484, 0x4782, 0xB1, 0x3D, 0x47, 0x40, 0xDD, 0x7C, 0x37, 0xC5);
DEFINE_PROPERTYKEY(WPD_EMAIL_TO_LINE, 0x41F8F65A, 0x5484, 0x4782, 0xB1, 0x3D, 0x47, 0x40, 0xDD, 0x7C, 0x37, 0xC5, 2);
DEFINE_PROPERTYKEY(WPD_EMAIL_CC_LINE, 0x41F8F65A, 0x5484, 0x4782, 0xB1, 0x3D, 0x47, 0x40, 0xDD, 0x7C, 0x37, 0xC5, 3);
DEFINE_PROPERTYKEY(WPD_EMAIL_BCC_LINE, 0x41F8F65A, 0x5484, 0x4782, 0xB1, 0x3D, 0x47, 0x40, 0xDD, 0x7C, 0x37, 0xC5, 4);
DEFINE_PROPERTYKEY(WPD_EMAIL_HAS_BEEN_READ, 0x41F8F65A, 0x5484, 0x4782, 0xB1, 0x3D, 0x47, 0x40, 0xDD, 0x7C, 0x37, 0xC5, 7);
DEFINE_PROPERTYKEY(WPD_EMAIL_RECEIVED_TIME, 0x41F8F65A, 0x5484, 0x4782, 0xB1, 0x3D, 0x47, 0x40, 0xDD, 0x7C, 0x37, 0xC5, 8);
DEFINE_PROPERTYKEY(WPD_EMAIL_HAS_ATTACHMENTS, 0x41F8F65A, 0x5484, 0x4782, 0xB1, 0x3D, 0x47, 0x40, 0xDD, 0x7C, 0x37, 0xC5, 9);
DEFINE_PROPERTYKEY(WPD_EMAIL_SENDER_ADDRESS, 0x41F8F65A, 0x5484, 0x4782, 0xB1, 0x3D, 0x47, 0x40, 0xDD, 0x7C, 0x37, 0xC5, 10);
DEFINE_GUID(WPD_APPOINTMENT_OBJECT_PROPERTIES_V1, 0xF99EFD03, 0x431D, 0x40D8, 0xA1, 0xC9, 0x4E, 0x22, 0x0D, 0x9C, 0x88, 0xD3);
DEFINE_PROPERTYKEY(WPD_APPOINTMENT_LOCATION, 0xF99EFD03, 0x431D, 0x40D8, 0xA1, 0xC9, 0x4E, 0x22, 0x0D, 0x9C, 0x88, 0xD3, 3);
DEFINE_PROPERTYKEY(WPD_APPOINTMENT_TYPE, 0xF99EFD03, 0x431D, 0x40D8, 0xA1, 0xC9, 0x4E, 0x22, 0x0D, 0x9C, 0x88, 0xD3, 7);
DEFINE_PROPERTYKEY(WPD_APPOINTMENT_REQUIRED_ATTENDEES, 0xF99EFD03, 0x431D, 0x40D8, 0xA1, 0xC9, 0x4E, 0x22, 0x0D, 0x9C, 0x88, 0xD3, 8);
DEFINE_PROPERTYKEY(WPD_APPOINTMENT_OPTIONAL_ATTENDEES, 0xF99EFD03, 0x431D, 0x40D8, 0xA1, 0xC9, 0x4E, 0x22, 0x0D, 0x9C, 0x88, 0xD3, 9);
DEFINE_PROPERTYKEY(WPD_APPOINTMENT_ACCEPTED_ATTENDEES, 0xF99EFD03, 0x431D, 0x40D8, 0xA1, 0xC9, 0x4E, 0x22, 0x0D, 0x9C, 0x88, 0xD3, 10);
DEFINE_PROPERTYKEY(WPD_APPOINTMENT_RESOURCES, 0xF99EFD03, 0x431D, 0x40D8, 0xA1, 0xC9, 0x4E, 0x22, 0x0D, 0x9C, 0x88, 0xD3, 11);
DEFINE_PROPERTYKEY(WPD_APPOINTMENT_TENTATIVE_ATTENDEES, 0xF99EFD03, 0x431D, 0x40D8, 0xA1, 0xC9, 0x4E, 0x22, 0x0D, 0x9C, 0x88, 0xD3, 12);
DEFINE_PROPERTYKEY(WPD_APPOINTMENT_DECLINED_ATTENDEES, 0xF99EFD03, 0x431D, 0x40D8, 0xA1, 0xC9, 0x4E, 0x22, 0x0D, 0x9C, 0x88, 0xD3, 13);
DEFINE_GUID(WPD_TASK_OBJECT_PROPERTIES_V1, 0xE354E95E, 0xD8A0, 0x4637, 0xA0, 0x3A, 0x0C, 0xB2, 0x68, 0x38, 0xDB, 0xC7);
DEFINE_PROPERTYKEY(WPD_TASK_STATUS, 0xE354E95E, 0xD8A0, 0x4637, 0xA0, 0x3A, 0x0C, 0xB2, 0x68, 0x38, 0xDB, 0xC7, 6);
DEFINE_PROPERTYKEY(WPD_TASK_PERCENT_COMPLETE, 0xE354E95E, 0xD8A0, 0x4637, 0xA0, 0x3A, 0x0C, 0xB2, 0x68, 0x38, 0xDB, 0xC7, 8);
DEFINE_PROPERTYKEY(WPD_TASK_REMINDER_DATE, 0xE354E95E, 0xD8A0, 0x4637, 0xA0, 0x3A, 0x0C, 0xB2, 0x68, 0x38, 0xDB, 0xC7, 10);
DEFINE_PROPERTYKEY(WPD_TASK_OWNER, 0xE354E95E, 0xD8A0, 0x4637, 0xA0, 0x3A, 0x0C, 0xB2, 0x68, 0x38, 0xDB, 0xC7, 11);
DEFINE_GUID(WPD_SMS_OBJECT_PROPERTIES_V1, 0x7E1074CC, 0x50FF, 0x4DD1, 0xA7, 0x42, 0x53, 0xBE, 0x6F, 0x09, 0x3A, 0x0D);
DEFINE_PROPERTYKEY(WPD_SMS_PROVIDER, 0x7E1074CC, 0x50FF, 0x4DD1, 0xA7, 0x42, 0x53, 0xBE, 0x6F, 0x09, 0x3A, 0x0D, 2);
DEFINE_PROPERTYKEY(WPD_SMS_TIMEOUT, 0x7E1074CC, 0x50FF, 0x4DD1, 0xA7, 0x42, 0x53, 0xBE, 0x6F, 0x09, 0x3A, 0x0D, 3);
DEFINE_PROPERTYKEY(WPD_SMS_MAX_PAYLOAD, 0x7E1074CC, 0x50FF, 0x4DD1, 0xA7, 0x42, 0x53, 0xBE, 0x6F, 0x09, 0x3A, 0x0D, 4);
DEFINE_PROPERTYKEY(WPD_SMS_ENCODING, 0x7E1074CC, 0x50FF, 0x4DD1, 0xA7, 0x42, 0x53, 0xBE, 0x6F, 0x09, 0x3A, 0x0D, 5);
DEFINE_GUID(WPD_SECTION_OBJECT_PROPERTIES_V1, 0x516AFD2B, 0xC64E, 0x44F0, 0x98, 0xDC, 0xBE, 0xE1, 0xC8, 0x8F, 0x7D, 0x66);
DEFINE_PROPERTYKEY(WPD_SECTION_DATA_OFFSET, 0x516AFD2B, 0xC64E, 0x44F0, 0x98, 0xDC, 0xBE, 0xE1, 0xC8, 0x8F, 0x7D, 0x66, 2);
DEFINE_PROPERTYKEY(WPD_SECTION_DATA_LENGTH, 0x516AFD2B, 0xC64E, 0x44F0, 0x98, 0xDC, 0xBE, 0xE1, 0xC8, 0x8F, 0x7D, 0x66, 3);
DEFINE_PROPERTYKEY(WPD_SECTION_DATA_UNITS, 0x516AFD2B, 0xC64E, 0x44F0, 0x98, 0xDC, 0xBE, 0xE1, 0xC8, 0x8F, 0x7D, 0x66, 4);
DEFINE_PROPERTYKEY(WPD_SECTION_DATA_REFERENCED_OBJECT_RESOURCE, 0x516AFD2B, 0xC64E, 0x44F0, 0x98, 0xDC, 0xBE, 0xE1, 0xC8, 0x8F, 0x7D, 0x66, 5);
#endif /* WPD_SERVICES_STRICT */

typedef struct tagWPD_COMMAND_ACCESS_LOOKUP_ENTRY {
  PROPERTYKEY Command;
  DWORD AccessType;
  PROPERTYKEY AccessProperty;
} WPD_COMMAND_ACCESS_LOOKUP_ENTRY;

#endif /* PORTABLEDEVICE_H */
