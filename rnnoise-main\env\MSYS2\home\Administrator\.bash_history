pacman -S mingw-w64-x86_64-gcc~
pacman -S mingw-w64-x86_64-gcc
gcc --version 
gcc --version 
gcc --version 
cmake --version
pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git
pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git
pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git
pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git
pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git
git clone https://github.com/xiph/rnnoise.git
cd rnnoise
mkdir build && cd build
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release
make -j4
/rnnoise/build
cd ~/rnnoise/build
make              # 重新编译
cd ~/rnnoise
rm -rf build
mkdir build && cd build
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release
make -j4
cd ~  # 切换到 MSYS2 用户目录
git clone https://github.com/xiph/rnnoise.git
cd rnnoise
ls    # 检查是否有 CMakeLists.txt
mkdir build && cd build
cmake .. -G "MinGW Makefiles"  # 注意是 ".." 表示上级目录
cd ~/rnnoise   # 确保进入正确的 RNNoise 源码目录
rm -rf build   # 强制删除旧的 build 目录
ls CMakeLists.txt  # 检查文件是否存在
cd ~
rm -rf rnnoise   # 删除旧目录
git clone https://github.com/xiph/rnnoise.git
pwd  # 查看当前完整路径
ls   # 列出所有文件，检查是否有以下关键文件：
     # - CMakeLists.txt
     # - src/
     # - include/
cd ~
rm -rf rnnoise  # 彻底删除旧目录
git clone https://github.com/xiph/rnnoise.git
cd rnnoise
ls              # 确认 CMakeLists.txt 存在
cd ~/rnnoise
git checkout experimental  # 切换到包含CMakeLists.txt的分支
ls CMakeLists.txt         # 现在应该能看到文件
# 清理并创建 build 目录
rm -rf build
mkdir build && cd build
# 运行 CMake
cmake .. -G "MinGW Makefiles"
make -j4
# 验证
ls -l denoise.exe
make --version  # 应输出版本信息
cd
make --version  # 应输出版本信息
make --version  # 应输出版本信息
pacman -S mingw-w64-x86_64-toolchain
make --version  # 应输出版本信息
pacman -S mingw-w64-x86_64-toolchain
make --version  # 应输出版本信息
export PATH="/mingw64/bin:$PATH"
make --version
pacman -S mingw-w64-x86_64-toolchain
make --version
find /mingw64 -name "make.exe"
export PATH="/mingw64/bin:$PATH"
make --version  # 立即测试
pacman -Rsc mingw-w64-x86_64-make
pacman -Scc
pacman -S mingw-w64-x86_64-make --force
pacman -Scc
pacman -S mingw-w64-x86_64-make --force
gcc --version
gcc --version
gcc --version
gcc --version
pacman -Syu
pacman -S make
make --version
pacman -S git
git clone https://github.com/XHAKA3456/rnnoise
cd ~/rnnoise
rm -rf rnnoise
ls
git clone https://github.com/XHAKA3456/rnnoise
git clone https://github.com/rokith/rnnoise-plus
lsgit clone https://github.com/xiph/rnnoise.git
git clone https://github.com/xiph/rnnoise.git
git clone https://github.com/XHAKA3456/rnnoise.git
gcc --version
pacman -S base-devel
pacman -S base-devel
pacman -S mingw-w64-x86_64-toolchain
cd D:\RNN\rnnoise-main\src
cd /d/RNN/rnnoise-main/src
gcc -DTRAINING=1 -Wall -W -O3 -O0 -g -Wunused-variable -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
export TMPDIR=/d/RNN/rnnoise-main/env/MSYS2/tmp
gcc -DTRAINING=1 -Wall -W -O3 -O0 -g -Wunused-variable -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
cd ~
export TMPDIR=/d/RNN/rnnoise-main/env/MSYS2/tmp
cd /d/RNN/rnnoise-main/src
gcc -DTRAINING=1 -Wall -W -O3 -O0 -g -Wunused-variable -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
export TMPDIR=/d/RNN/rnnoise-main/env/MSYS2/tmp
gcc -DTRAINING=1 -Wall -W -O3 -O0 -g -Wunused-variable -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -O0 -g -Wunused-variable -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include -L/path/to/library -lname_of_library denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c other_file_with_definition.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include -mconsole denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include -mconsole denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include -march=native denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
cd --version
gcc --version

cd /d/RNN/rnnoise-main/src
gcc -DTRAINING=1 -Wall -W -O3 -O0 -g -Wunused-variable -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training -lm
[200~cmake --version~
cmake --version
