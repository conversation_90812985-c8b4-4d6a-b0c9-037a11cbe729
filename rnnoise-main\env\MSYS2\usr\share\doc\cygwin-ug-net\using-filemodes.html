<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>File permissions</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using.html" title="Chapter&#160;3.&#160;Using Cygwin"><link rel="prev" href="using-textbinary.html" title="Text and Binary modes"><link rel="next" href="using-specialnames.html" title="Special filenames"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">File permissions</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="using-textbinary.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;3.&#160;Using Cygwin</th><td width="20%" align="right">&#160;<a accesskey="n" href="using-specialnames.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="using-filemodes"></a>File permissions</h2></div></div></div><p>On FAT or FAT32 filesystems, files are always readable, and Cygwin
uses the DOS read-only attribute to determine if they are writable. Files are
considered to be executable if the filename ends with .bat, .com or .exe, or
if its content starts with #!. Consequently <span class="command"><strong>chmod</strong></span> can
only affect the "w" mode, it silently ignores actions involving the other
modes.  This means that <span class="command"><strong>ls -l</strong></span>
needs to open and read files. It can thus be relatively slow.</p><p>On NTFS, file permissions are evaluated using the Access Control
Lists (ACLs) attached to a file.  This can be switched off by using the
"noacl" option to the respective mount point in the
<code class="filename">/etc/fstab</code> or <code class="filename">/etc/fstab.d/$USER</code>
file.  For more information on file permissions, see



<a class="xref" href="ntsec.html" title="POSIX accounts, permission, and security">the section called &#8220;POSIX accounts, permission, and security&#8221;</a>.
</p><p>On NFS shares, file permissions are exactly the POSIX permissions
transmitted from the server using the NFSv3 protocol, if the NFS client
is the one from Microsoft's "Services For Unix", or the one built into
Windows Vista or later.
</p><p>Only the user and group ownership is not necessarily correct.</p></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="using-textbinary.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="using-specialnames.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Text and Binary modes&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Special filenames</td></tr></table></div></body></html>
