# RNN噪声抑制与人声增强系统改进技术文档

## 项目概述

基于现有RNNoise系统，在保持原有噪声抑制功能的基础上，增加人声增强功能。系统将通过扩展特征维度和双重损失函数训练，实现降噪与人声增强的同步处理。

## 核心技术架构

### 1. 特征扩展方案

#### 1.1 特征维度变更
```c
// 原始特征定义
#define ORIGINAL_FEATURES 38
#define VOICE_ENHANCEMENT_FEATURES 30
#define TOTAL_INPUT_FEATURES (ORIGINAL_FEATURES + VOICE_ENHANCEMENT_FEATURES)  // 68维

// 输出维度定义
#define NOISE_SUPPRESSION_BANDS 18
#define VOICE_ENHANCEMENT_BANDS 18
#define TOTAL_OUTPUT_BANDS (NOISE_SUPPRESSION_BANDS + VOICE_ENHANCEMENT_BANDS)  // 36维

// 完整特征向量
#define TOTAL_FEATURE_VECTOR (TOTAL_INPUT_FEATURES + TOTAL_OUTPUT_BANDS + 1)  // 103维
```

#### 1.2 人声特有特征提取函数

```c
// 人声增强特征提取
void extract_voice_enhancement_features(DenoiseState *st, kiss_fft_cpx *X, 
                                       float *voice_features, const float *in) {
    float pitch_features[8];
    float formant_features[6]; 
    float harmonic_features[8];
    float clarity_features[8];
    
    // 基频相关特征提取
    extract_pitch_features(st, X, pitch_features, in);
    
    // 共振峰特征提取
    extract_formant_features(st, X, formant_features);
    
    // 谐波结构特征提取
    extract_harmonic_features(st, X, harmonic_features);
    
    // 语音清晰度特征提取
    extract_clarity_features(st, X, clarity_features);
    
    // 组合所有人声特征
    memcpy(voice_features, pitch_features, 8 * sizeof(float));
    memcpy(voice_features + 8, formant_features, 6 * sizeof(float));
    memcpy(voice_features + 14, harmonic_features, 8 * sizeof(float));
    memcpy(voice_features + 22, clarity_features, 8 * sizeof(float));
}

// 基频特征提取实现
void extract_pitch_features(DenoiseState *st, kiss_fft_cpx *X, 
                           float *pitch_features, const float *in) {
    float pitch_value, pitch_stability, pitch_strength, pitch_confidence;
    float pitch_smoothness, pitch_harmonics[3];
    
    // 基频检测
    pitch_value = estimate_fundamental_frequency(X, st->last_period);
    
    // 基频稳定性
    pitch_stability = calculate_pitch_stability(st, pitch_value);
    
    // 基频强度和置信度
    pitch_strength = calculate_pitch_strength(X, pitch_value);
    pitch_confidence = calculate_pitch_confidence(X, pitch_value);
    
    // 基频轨迹平滑度
    pitch_smoothness = calculate_pitch_smoothness(st, pitch_value);
    
    // 基频倍频程分布
    calculate_pitch_harmonics(X, pitch_value, pitch_harmonics);
    
    // 填充特征向量
    pitch_features[0] = pitch_value / 500.0f;  // 归一化到[0,1]
    pitch_features[1] = pitch_stability;
    pitch_features[2] = pitch_strength;
    pitch_features[3] = pitch_confidence;
    pitch_features[4] = pitch_smoothness;
    pitch_features[5] = pitch_harmonics[0];
    pitch_features[6] = pitch_harmonics[1];
    pitch_features[7] = pitch_harmonics[2];
}

// 共振峰特征提取实现
void extract_formant_features(DenoiseState *st, kiss_fft_cpx *X, float *formant_features) {
    float formants[3], bandwidths[3];
    
    // 线性预测系数计算
    float lpc_coeffs[12];
    compute_lpc_coefficients(X, lpc_coeffs, 12);
    
    // 从LPC系数提取共振峰
    extract_formants_from_lpc(lpc_coeffs, formants, bandwidths, 3);
    
    // 归一化并填充特征
    for (int i = 0; i < 3; i++) {
        formant_features[i] = formants[i] / 4000.0f;  // 频率归一化
        formant_features[i + 3] = bandwidths[i] / 500.0f;  // 带宽归一化
    }
}

// 谐波结构特征提取实现
void extract_harmonic_features(DenoiseState *st, kiss_fft_cpx *X, float *harmonic_features) {
    float hnr, harmonic_regularity, harmonic_energy[4], harmonic_distortion;
    
    // 谐波-噪声比计算
    hnr = calculate_harmonic_to_noise_ratio(X, st->last_period);
    
    // 谐波规律性
    harmonic_regularity = calculate_harmonic_regularity(X, st->last_period);
    
    // 谐波能量分布
    calculate_harmonic_energy_distribution(X, st->last_period, harmonic_energy, 4);
    
    // 谐波失真度量
    harmonic_distortion = calculate_harmonic_distortion(X, st->last_period);
    
    // 填充特征向量
    harmonic_features[0] = tanh(hnr / 20.0f);  // HNR归一化
    harmonic_features[1] = harmonic_regularity;
    harmonic_features[2] = harmonic_energy[0];
    harmonic_features[3] = harmonic_energy[1];
    harmonic_features[4] = harmonic_energy[2];
    harmonic_features[5] = harmonic_energy[3];
    harmonic_features[6] = harmonic_distortion;
    harmonic_features[7] = 0.0f;  // 保留位
}

// 语音清晰度特征提取实现
void extract_clarity_features(DenoiseState *st, kiss_fft_cpx *X, float *clarity_features) {
    float spectral_clarity, modulation_features[3];
    float envelope_features[2], dynamic_range[2];
    
    // 频谱清晰度指数
    spectral_clarity = calculate_spectral_clarity_index(X);
    
    // 调制频谱特征
    calculate_modulation_spectrum_features(st, X, modulation_features);
    
    // 语音包络特征
    calculate_speech_envelope_features(st, X, envelope_features);
    
    // 动态范围指标
    calculate_dynamic_range_indicators(st, X, dynamic_range);
    
    // 填充特征向量
    clarity_features[0] = spectral_clarity;
    clarity_features[1] = modulation_features[0];
    clarity_features[2] = modulation_features[1];
    clarity_features[3] = modulation_features[2];
    clarity_features[4] = envelope_features[0];
    clarity_features[5] = envelope_features[1];
    clarity_features[6] = dynamic_range[0];
    clarity_features[7] = dynamic_range[1];
}
```

### 2. 扩展的训练数据生成

#### 2.1 修改后的主训练函数

```c
// 扩展的训练数据生成主函数
int main(int argc, char **argv) {
    // ... 原有初始化代码 ...
    
    while (1) {
        // ... 原有音频读取和混合代码 ...
        
        // 扩展特征提取
        float extended_features[TOTAL_INPUT_FEATURES];
        float noise_targets[NOISE_SUPPRESSION_BANDS];
        float voice_targets[VOICE_ENHANCEMENT_BANDS];
        
        // 原有噪声抑制特征 (前38维)
        int silence = compute_frame_features(noisy, X, P, Ex, Ep, Exp, 
                                           extended_features, xn);
        
        // 新增人声增强特征 (后30维)
        extract_voice_enhancement_features(st, Y, 
                                         extended_features + ORIGINAL_FEATURES, x);
        
        // 计算双重目标
        compute_dual_targets(st, noise_state, noisy, Y, N, X, 
                           noise_targets, voice_targets, vad);
        
        // 输出扩展的特征向量 (103维)
        output_extended_training_data(extended_features, noise_targets, 
                                    voice_targets, vad);
        
        count++;
        if (count == 1000) break;
    }
    
    return 0;
}

// 双重目标计算函数
void compute_dual_targets(DenoiseState *st, DenoiseState *noise_state, 
                         DenoiseState *noisy, kiss_fft_cpx *Y, kiss_fft_cpx *N, 
                         kiss_fft_cpx *X, float *noise_targets, 
                         float *voice_targets, float vad) {
    float Ey[NB_BANDS], En[NB_BANDS], Ex[NB_BANDS];
    
    // 计算能量
    compute_band_energy(Ey, Y);  // 干净语音能量
    compute_band_energy(En, N);  // 噪声能量  
    compute_band_energy(Ex, X);  // 混合信号能量
    
    // 噪声抑制目标 (原有逻辑)
    for (int i = 0; i < NB_BANDS; i++) {
        noise_targets[i] = sqrt((Ey[i] + 1e-3) / (Ex[i] + 1e-3));
        if (noise_targets[i] > 1) noise_targets[i] = 1;
        if (vad == 0) noise_targets[i] = -1;
    }
    
    // 人声增强目标 (新增逻辑)
    for (int i = 0; i < NB_BANDS; i++) {
        // 基于谐波增强的目标计算
        float harmonic_boost = calculate_harmonic_boost_factor(Y, i, st->last_period);
        float clarity_boost = calculate_clarity_boost_factor(Y, i);
        
        voice_targets[i] = noise_targets[i] * (1.0f + 0.3f * harmonic_boost + 0.2f * clarity_boost);
        voice_targets[i] = MIN(voice_targets[i], 1.5f);  // 限制最大增强
        
        if (vad == 0 || Ey[i] < 1e-3) voice_targets[i] = -1;
    }
}

// 输出扩展训练数据
void output_extended_training_data(float *features, float *noise_targets, 
                                 float *voice_targets, float vad) {
    // 输出68维输入特征
    for (int i = 0; i < TOTAL_INPUT_FEATURES; i++) {
        printf("%f ", features[i]);
    }
    
    // 输出18维噪声抑制目标
    for (int i = 0; i < NOISE_SUPPRESSION_BANDS; i++) {
        printf("%f ", noise_targets[i]);
    }
    
    // 输出18维人声增强目标
    for (int i = 0; i < VOICE_ENHANCEMENT_BANDS; i++) {
        printf("%f ", voice_targets[i]);
    }
    
    // 输出VAD标签
    printf("%f\n", vad);
}
```

### 3. Python训练脚本

#### 3.1 扩展的训练脚本

```python
#!/usr/bin/env python3
"""
RNN噪声抑制与人声增强联合训练脚本
"""

import tensorflow as tf
import keras
from keras.models import Model
from keras.layers import Input, Dense, GRU, concatenate
from keras.callbacks import ModelCheckpoint
from keras import regularizers
from keras.constraints import Constraint
import h5py
import numpy as np
import os

# 设置CPU运行
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

# 特征维度定义
INPUT_FEATURES = 68  # 38 + 30
NOISE_SUPPRESSION_BANDS = 18
VOICE_ENHANCEMENT_BANDS = 18
TOTAL_OUTPUT_BANDS = 36
VAD_DIM = 1

class WeightClip(Constraint):
    def __init__(self, c=0.499):
        self.c = c
    
    def __call__(self, p):
        return tf.clip_by_value(p, -self.c, self.c)
    
    def get_config(self):
        return {'name': self.__class__.__name__, 'c': self.c}

# 损失函数定义
def noise_suppression_loss(y_true, y_pred):
    """噪声抑制损失函数"""
    mask = tf.minimum(y_true + 1., 1.)
    mse = tf.square(tf.sqrt(y_pred) - tf.sqrt(y_true))
    return tf.reduce_mean(mask * mse, axis=-1)

def voice_enhancement_loss(y_true, y_pred):
    """人声增强损失函数"""
    mask = tf.minimum(y_true + 1., 1.)
    
    # 基础MSE损失
    mse = tf.square(tf.sqrt(y_pred) - tf.sqrt(y_true))
    
    # 高频增强权重
    freq_weights = tf.constant([1.0, 1.0, 1.1, 1.1, 1.2, 1.2, 1.3, 1.3, 1.4, 
                               1.4, 1.5, 1.5, 1.6, 1.6, 1.7, 1.7, 1.8, 1.8])
    weighted_mse = mse * freq_weights
    
    # 谐波保持损失
    harmonic_loss = tf.square(y_pred - y_true) * 0.1
    
    total_loss = weighted_mse + harmonic_loss
    return tf.reduce_mean(mask * total_loss, axis=-1)

def vad_loss(y_true, y_pred):
    """VAD损失函数"""
    return tf.reduce_mean(2 * tf.abs(y_true - 0.5) * 
                         tf.keras.losses.binary_crossentropy(y_true, y_pred), axis=-1)

# 构建模型
def build_enhanced_model():
    """构建增强的RNN模型"""
    
    # 输入层
    main_input = Input(shape=(None, INPUT_FEATURES), name='main_input')
    
    # 输入特征分离
    noise_input = main_input[:, :, :38]  # 前38维用于噪声抑制
    voice_input = main_input[:, :, 38:]  # 后30维用于人声增强
    
    # 共享特征提取层
    shared_dense = Dense(32, activation='tanh', name='shared_dense',
                        kernel_constraint=WeightClip(), bias_constraint=WeightClip())
    
    # 噪声抑制分支
    noise_features = shared_dense(noise_input)
    noise_gru1 = GRU(24, activation='tanh', recurrent_activation='sigmoid', 
                     return_sequences=True, name='noise_gru1',
                     kernel_regularizer=regularizers.l2(0.000001),
                     recurrent_regularizer=regularizers.l2(0.000001),
                     kernel_constraint=WeightClip(), 
                     recurrent_constraint=WeightClip(),
                     bias_constraint=WeightClip())(noise_features)
    
    # 人声增强分支  
    voice_features = shared_dense(voice_input)
    voice_gru1 = GRU(30, activation='tanh', recurrent_activation='sigmoid',
                     return_sequences=True, name='voice_gru1',
                     kernel_regularizer=regularizers.l2(0.000001),
                     recurrent_regularizer=regularizers.l2(0.000001),
                     kernel_constraint=WeightClip(),
                     recurrent_constraint=WeightClip(), 
                     bias_constraint=WeightClip())(voice_features)
    
    # VAD分支
    vad_gru = GRU(24, activation='tanh', recurrent_activation='sigmoid',
                  return_sequences=True, name='vad_gru',
                  kernel_regularizer=regularizers.l2(0.000001),
                  recurrent_regularizer=regularizers.l2(0.000001),
                  kernel_constraint=WeightClip(),
                  recurrent_constraint=WeightClip(),
                  bias_constraint=WeightClip())(noise_features)
    
    vad_output = Dense(1, activation='sigmoid', name='vad_output',
                      kernel_constraint=WeightClip(), 
                      bias_constraint=WeightClip())(vad_gru)
    
    # 特征融合
    fused_features = concatenate([noise_gru1, voice_gru1, vad_gru, main_input])
    
    # 最终处理层
    final_gru = GRU(96, activation='tanh', recurrent_activation='sigmoid',
                    return_sequences=True, name='final_gru',
                    kernel_regularizer=regularizers.l2(0.000001),
                    recurrent_regularizer=regularizers.l2(0.000001),
                    kernel_constraint=WeightClip(),
                    recurrent_constraint=WeightClip(),
                    bias_constraint=WeightClip())(fused_features)
    
    # 输出层
    noise_output = Dense(NOISE_SUPPRESSION_BANDS, activation='sigmoid', 
                        name='noise_suppression_output',
                        kernel_constraint=WeightClip(), 
                        bias_constraint=WeightClip())(final_gru)
    
    voice_output = Dense(VOICE_ENHANCEMENT_BANDS, activation='sigmoid',
                        name='voice_enhancement_output', 
                        kernel_constraint=WeightClip(),
                        bias_constraint=WeightClip())(final_gru)
    
    # 构建模型
    model = Model(inputs=main_input, 
                  outputs=[noise_output, voice_output, vad_output])
    
    return model

# 主训练函数
def main():
    print('构建增强模型...')
    model = build_enhanced_model()
    
    # 编译模型
    model.compile(
        loss={
            'noise_suppression_output': noise_suppression_loss,
            'voice_enhancement_output': voice_enhancement_loss, 
            'vad_output': vad_loss
        },
        loss_weights={
            'noise_suppression_output': 10.0,
            'voice_enhancement_output': 8.0,
            'vad_output': 0.5
        },
        optimizer='adam',
        metrics=['mse']
    )
    
    print('加载训练数据...')
    with h5py.File('enhanced_training_data.h5', 'r') as hf:
        all_data = hf['data'][:]
    print('数据加载完成.')
    
    # 数据预处理
    window_size = 2000
    nb_sequences = len(all_data) // window_size
    print(f'{nb_sequences} 个训练序列')
    
    # 输入特征 (68维)
    x_train = all_data[:nb_sequences*window_size, :INPUT_FEATURES]
    x_train = np.reshape(x_train, (nb_sequences, window_size, INPUT_FEATURES))
    
    # 噪声抑制目标 (18维)
    noise_train = all_data[:nb_sequences*window_size, 
                          INPUT_FEATURES:INPUT_FEATURES+NOISE_SUPPRESSION_BANDS]
    noise_train = np.reshape(noise_train, (nb_sequences, window_size, NOISE_SUPPRESSION_BANDS))
    
    # 人声增强目标 (18维)
    voice_train = all_data[:nb_sequences*window_size, 
                          INPUT_FEATURES+NOISE_SUPPRESSION_BANDS:INPUT_FEATURES+TOTAL_OUTPUT_BANDS]
    voice_train = np.reshape(voice_train, (nb_sequences, window_size, VOICE_ENHANCEMENT_BANDS))
    
    # VAD目标 (1维)
    vad_train = all_data[:nb_sequences*window_size, -1:]
    vad_train = np.reshape(vad_train, (nb_sequences, window_size, 1))
    
    print(f'训练数据形状: x={x_train.shape}, noise={noise_train.shape}, '
          f'voice={voice_train.shape}, vad={vad_train.shape}')
    
    # 设置回调
    filepath = "models/enhanced-weights-{epoch:02d}-{loss:.5f}.keras"
    checkpoint = ModelCheckpoint(filepath, monitor='loss', verbose=1, 
                               save_best_only=True, mode='min')
    callbacks_list = [checkpoint]
    
    # 开始训练
    print('开始训练...')
    model.fit(
        x_train, 
        {
            'noise_suppression_output': noise_train,
            'voice_enhancement_output': voice_train,
            'vad_output': vad_train
        },
        batch_size=32,
        epochs=150,
        validation_split=0.1,
        callbacks=callbacks_list
    )
    
    # 保存最终模型
    model.save("enhanced_rnnoise_model.keras")
    print('训练完成!')

if __name__ == '__main__':
    main()
```

### 4. 模型导出与C代码集成

#### 4.1 扩展的模型导出脚本

```python
#!/usr/bin/env python3
"""
增强模型导出脚本 - dump_enhanced_rnn.py
"""

from keras.models import load_model
import numpy as np
import sys
import re

def printVector(f, vector, name):
    """输出权重向量到C文件"""
    v = np.reshape(vector, (-1))
    f.write('static const rnn_weight {}[{}] = {{\n   '.format(name, len(v)))
    for i in range(0, len(v)):
        f.write('{}'.format(min(127, int(round(256*v[i])))))
        if (i != len(v)-1):
            f.write(',')
        else:
            break
        if (i % 8 == 7):
            f.write("\n   ")
        else:
            f.write(" ")
    f.write('\n};\n\n')

def printEnhancedLayer(f, hf, layer, layer_type=''):
    """输出增强层定义"""
    weights = layer.get_weights()
    layer_name = layer.name

    printVector(f, weights[0], layer_name + '_weights')
    if len(weights) > 2:
        printVector(f, weights[1], layer_name + '_recurrent_weights')
    printVector(f, weights[-1], layer_name + '_bias')

    activation = re.search('function (.*) at', str(layer.activation)).group(1).upper()

    if len(weights) > 2:  # GRU层
        f.write('const GRULayer {} = {{\n   {}_bias,\n   {}_weights,\n   {}_recurrent_weights,\n   {}, {}, ACTIVATION_{}\n}};\n\n'
                .format(layer_name, layer_name, layer_name, layer_name,
                       weights[0].shape[0], weights[0].shape[1]//3, activation))
        hf.write('#define {}_SIZE {}\n'.format(layer_name.upper(), weights[0].shape[1]//3))
        hf.write('extern const GRULayer {};\n\n'.format(layer_name))
    else:  # Dense层
        f.write('const DenseLayer {} = {{\n   {}_bias,\n   {}_weights,\n   {}, {}, ACTIVATION_{}\n}};\n\n'
                .format(layer_name, layer_name, layer_name,
                       weights[0].shape[0], weights[0].shape[1], activation))
        hf.write('#define {}_SIZE {}\n'.format(layer_name.upper(), weights[0].shape[1]))
        hf.write('extern const DenseLayer {};\n\n'.format(layer_name))

def export_enhanced_model(model_path, c_file, h_file):
    """导出增强模型到C代码"""

    # 加载模型
    model = load_model(model_path, custom_objects={
        'noise_suppression_loss': lambda y_true, y_pred: y_true,
        'voice_enhancement_loss': lambda y_true, y_pred: y_true,
        'vad_loss': lambda y_true, y_pred: y_true,
        'WeightClip': lambda: None
    })

    # 打开输出文件
    f = open(c_file, 'w')
    hf = open(h_file, 'w')

    # 写入文件头
    f.write('/*增强RNN模型 - 自动生成*/\n\n')
    f.write('#ifdef HAVE_CONFIG_H\n#include "config.h"\n#endif\n\n')
    f.write('#include "rnn.h"\n#include "enhanced_rnn.h"\n\n')

    hf.write('/*增强RNN模型头文件 - 自动生成*/\n\n')
    hf.write('#ifndef ENHANCED_RNN_DATA_H\n#define ENHANCED_RNN_DATA_H\n\n')
    hf.write('#include "rnn.h"\n\n')

    # 导出各层权重
    gru_layers = []
    for i, layer in enumerate(model.layers):
        if len(layer.get_weights()) > 0:
            printEnhancedLayer(f, hf, layer)
            if len(layer.get_weights()) > 2:  # GRU层
                gru_layers.append(layer.name)

    # 生成状态结构
    hf.write('struct EnhancedRNNState {\n')
    for name in gru_layers:
        hf.write('  float {}_state[{}_SIZE];\n'.format(name, name.upper()))
    hf.write('};\n\n')

    # 添加处理函数声明
    hf.write('/* 增强处理函数 */\n')
    hf.write('void enhanced_rnn_process(struct EnhancedRNNState *rnn, float *gains_noise, float *gains_voice, float *vad_prob, const float *input);\n')
    hf.write('struct EnhancedRNNState *enhanced_rnn_create(void);\n')
    hf.write('void enhanced_rnn_destroy(struct EnhancedRNNState *rnn);\n\n')

    hf.write('#endif\n')

    f.close()
    hf.close()

    print(f'模型导出完成: {c_file}, {h_file}')

if __name__ == '__main__':
    if len(sys.argv) != 4:
        print('用法: python dump_enhanced_rnn.py <model.keras> <output.c> <output.h>')
        sys.exit(1)

    export_enhanced_model(sys.argv[1], sys.argv[2], sys.argv[3])
```

#### 4.2 增强的C处理函数

```c
// enhanced_rnn.c - 增强RNN处理实现

#include "enhanced_rnn.h"
#include <stdlib.h>
#include <string.h>
#include <math.h>

// 创建增强RNN状态
struct EnhancedRNNState *enhanced_rnn_create(void) {
    struct EnhancedRNNState *rnn = calloc(1, sizeof(struct EnhancedRNNState));
    return rnn;
}

// 销毁增强RNN状态
void enhanced_rnn_destroy(struct EnhancedRNNState *rnn) {
    free(rnn);
}

// 增强RNN处理函数
void enhanced_rnn_process(struct EnhancedRNNState *rnn, float *gains_noise,
                         float *gains_voice, float *vad_prob, const float *input) {
    float dense_out[32];
    float noise_gru_out[24];
    float voice_gru_out[30];
    float vad_gru_out[24];
    float fused_input[68 + 24 + 30 + 24];  // 146维
    float final_gru_out[96];

    // 共享Dense层处理
    compute_dense(&shared_dense, dense_out, input);

    // 噪声抑制GRU分支
    compute_gru(&noise_gru1, noise_gru_out, rnn->noise_gru1_state, dense_out);

    // 人声增强GRU分支
    compute_gru(&voice_gru1, voice_gru_out, rnn->voice_gru1_state, dense_out + 38);

    // VAD GRU分支
    compute_gru(&vad_gru, vad_gru_out, rnn->vad_gru_state, dense_out);

    // 特征融合
    memcpy(fused_input, noise_gru_out, 24 * sizeof(float));
    memcpy(fused_input + 24, voice_gru_out, 30 * sizeof(float));
    memcpy(fused_input + 54, vad_gru_out, 24 * sizeof(float));
    memcpy(fused_input + 78, input, 68 * sizeof(float));

    // 最终GRU处理
    compute_gru(&final_gru, final_gru_out, rnn->final_gru_state, fused_input);

    // 输出层
    compute_dense(&noise_suppression_output, gains_noise, final_gru_out);
    compute_dense(&voice_enhancement_output, gains_voice, final_gru_out);

    float vad_out[1];
    compute_dense(&vad_output, vad_out, vad_gru_out);
    *vad_prob = vad_out[0];
}

// 增强的帧处理函数
float enhanced_rnnoise_process_frame(EnhancedDenoiseState *st, float *out, const float *in) {
    int i;
    kiss_fft_cpx X[FREQ_SIZE];
    kiss_fft_cpx P[WINDOW_SIZE];
    float x[FRAME_SIZE];
    float Ex[NB_BANDS], Ep[NB_BANDS];
    float Exp[NB_BANDS];
    float features[TOTAL_INPUT_FEATURES];
    float noise_gains[NB_BANDS];
    float voice_gains[NB_BANDS];
    float combined_gains[NB_BANDS];
    float gf[FREQ_SIZE] = {1};
    float vad_prob = 0;
    int silence;

    // 高通滤波
    static const float a_hp[2] = {-1.99599, 0.99600};
    static const float b_hp[2] = {-2, 1};
    biquad(x, st->mem_hp_x, in, b_hp, a_hp, FRAME_SIZE);

    // 计算原有特征 (前38维)
    silence = compute_frame_features(st, X, P, Ex, Ep, Exp, features, x);

    if (!silence) {
        // 计算人声增强特征 (后30维)
        extract_voice_enhancement_features(st, X, features + ORIGINAL_FEATURES, x);

        // RNN处理
        enhanced_rnn_process(&st->enhanced_rnn, noise_gains, voice_gains, &vad_prob, features);

        // 增益融合策略
        for (i = 0; i < NB_BANDS; i++) {
            float alpha = 0.7f;  // 噪声抑制权重
            float beta = 0.3f;   // 人声增强权重

            // 自适应权重调整
            if (vad_prob > 0.8f) {
                beta = 0.5f;  // 强语音时增加人声增强
                alpha = 0.5f;
            }

            combined_gains[i] = alpha * noise_gains[i] + beta * voice_gains[i];
            combined_gains[i] = MAX16(combined_gains[i], 0.1f * st->lastg[i]);
            st->lastg[i] = combined_gains[i];
        }

        // 应用增益到频域
        interp_band_gain(gf, combined_gains);
        for (i = 0; i < FREQ_SIZE; i++) {
            X[i].r *= gf[i];
            X[i].i *= gf[i];
        }
    }

    // 帧合成
    frame_synthesis(st, out, X);
    return vad_prob;
}
```

### 5. Makefile集成

#### 5.1 扩展的Makefile

```makefile
# 增强RNN噪声抑制与人声增强系统 Makefile

CC = gcc
CFLAGS = -Wall -W -O3 -g -ffast-math -msse2 -msse4.1
INCLUDES = -I./include
LIBS = -lm

# 源文件定义
CORE_SOURCES = src/kiss_fft.c src/celt_lpc.c src/pitch.c
RNN_SOURCES = src/rnn.c src/rnn_data.c src/enhanced_rnn_data.c
DENOISE_SOURCES = src/denoise.c src/enhanced_denoise.c
TRAINING_SOURCES = src/denoise_training.c src/enhanced_training.c

# 目标文件
CORE_OBJS = $(CORE_SOURCES:.c=.o)
RNN_OBJS = $(RNN_SOURCES:.c=.o)
DENOISE_OBJS = $(DENOISE_SOURCES:.c=.o)
TRAINING_OBJS = $(TRAINING_SOURCES:.c=.o)

# 可执行文件目标
TARGETS = bin/rnnoise_enhanced bin/enhanced_training_generator

.PHONY: all clean install

all: $(TARGETS)

# 增强的噪声抑制程序
bin/rnnoise_enhanced: $(CORE_OBJS) $(RNN_OBJS) $(DENOISE_OBJS) src/main_enhanced.o
	@mkdir -p bin
	$(CC) $(CFLAGS) -o $@ $^ $(LIBS)

# 增强的训练数据生成程序
bin/enhanced_training_generator: $(CORE_OBJS) $(RNN_OBJS) $(TRAINING_OBJS)
	@mkdir -p bin
	$(CC) $(CFLAGS) -DTRAINING=1 -DENHANCED_TRAINING=1 -o $@ $^ $(LIBS)

# 编译规则
%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 特殊编译规则
src/enhanced_training.o: src/enhanced_training.c
	$(CC) $(CFLAGS) $(INCLUDES) -DTRAINING=1 -DENHANCED_TRAINING=1 -c $< -o $@

# 清理
clean:
	rm -f $(CORE_OBJS) $(RNN_OBJS) $(DENOISE_OBJS) $(TRAINING_OBJS)
	rm -f src/main_enhanced.o
	rm -f $(TARGETS)

# 安装
install: $(TARGETS)
	cp bin/rnnoise_enhanced /usr/local/bin/
	cp bin/enhanced_training_generator /usr/local/bin/

# 训练数据生成
generate_training_data: bin/enhanced_training_generator
	./bin/enhanced_training_generator data/clean_voice data/wind_noise_voice mixed.wav > training/enhanced_training_data.f32

# Python训练
train_model: training/enhanced_training_data.f32
	cd training && python bin2hdf5.py enhanced_training_data.f32 auto 103 enhanced_training_data.h5
	cd training && python enhanced_rnn_train.py

# 模型导出
export_model: training/enhanced_rnnoise_model.keras
	cd training && python dump_enhanced_rnn.py enhanced_rnnoise_model.keras ../src/enhanced_rnn_data.c ../include/enhanced_rnn_data.h

# 完整构建流程
full_build: generate_training_data train_model export_model all
	@echo "增强RNN系统构建完成!"
```

### 6. 使用说明

#### 6.1 完整构建流程

```bash
# 1. 生成训练数据
make generate_training_data

# 2. 训练模型
make train_model

# 3. 导出模型
make export_model

# 4. 编译最终程序
make all

# 或者一键完成所有步骤
make full_build
```

#### 6.2 程序使用

```bash
# 使用增强的噪声抑制与人声增强
./bin/rnnoise_enhanced input.wav output.wav

# 生成训练数据
./bin/enhanced_training_generator clean_speech_dir noise_dir mixed.wav > training_data.f32
```

## 技术特点总结

1. **特征扩展**: 从38维扩展到68维，新增30维人声特有特征
2. **双重损失**: 分别优化噪声抑制和人声增强，权重可调
3. **模块化设计**: 保持与原系统的兼容性，支持独立编译
4. **实时处理**: 优化的C实现确保实时性能
5. **完整工具链**: 从数据生成到模型训练到部署的完整流程
