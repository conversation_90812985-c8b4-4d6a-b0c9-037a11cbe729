/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _dhtmliid_h_
#define _dhtmliid_h_

#ifdef __cplusplus
extern "C"{
#endif

  EXTERN_C const IID LIBID_DHTMLEDLib;
  EXTERN_C const CLSID CLSID_DHTMLEdit;
  EXTERN_C const CLSID CLSID_DHTMLSafe;
  EXTERN_C const CLSID CLSID_DEInsertTableParam;
  EXTERN_C const CLSID CLSID_DEGetBlockFmtNamesParam;
  EXTERN_C const IID DIID__DHTMLSafeEvents;
  EXTERN_C const IID DIID__DHTMLEditEvents;
  EXTERN_C const IID IID_IDHTMLEdit;
  EXTERN_C const IID IID_IDHTMLSafe;
  EXTERN_C const IID IID_IDEInsertTableParam;
  EXTERN_C const IID IID_IDEGetBlockFmtNamesParam;

  DEFINE_GUID(LIBID_DHTMLEDLib,0x683364A1,0xB37D,0x11D1,0xAD,0xC5,0x00,0x60,0x08,0xA5,0x84,0x8C);
  DEFINE_GUID(CLSID_DHTMLEdit,0x2D360200,0xFFF5,0x11d1,0x8D,0x03,0x00,0xA0,0xC9,0x59,0xBC,0x0A);
  DEFINE_GUID(CLSID_DHTMLSafe,0x2D360201,0xFFF5,0x11d1,0x8D,0x03,0x00,0xA0,0xC9,0x59,0xBC,0x0A);
  DEFINE_GUID(CLSID_DEInsertTableParam,0x47B0DFC7,0xB7A3,0x11D1,0xAD,0xC5,0x00,0x60,0x08,0xA5,0x84,0x8C);
  DEFINE_GUID(CLSID_DEGetBlockFmtNamesParam,0x8D91090E,0xB955,0x11D1,0xAD,0xC5,0x00,0x60,0x08,0xA5,0x84,0x8C);
  DEFINE_GUID(DIID__DHTMLSafeEvents,0xD1FC78E8,0xB380,0x11d1,0xAD,0xC5,0x00,0x60,0x08,0xA5,0x84,0x8C);
  DEFINE_GUID(DIID__DHTMLEditEvents,0x588D5040,0xCF28,0x11d1,0x8C,0xD3,0x00,0xA0,0xC9,0x59,0xBC,0x0A);
  DEFINE_GUID(IID_IDHTMLEdit,0xCE04B591,0x2B1F,0x11d2,0x8D,0x1E,0x00,0xA0,0xC9,0x59,0xBC,0x0A);
  DEFINE_GUID(IID_IDHTMLSafe,0xCE04B590,0x2B1F,0x11d2,0x8D,0x1E,0x00,0xA0,0xC9,0x59,0xBC,0x0A0);
  DEFINE_GUID(IID_IDEInsertTableParam,0x47B0DFC6,0xB7A3,0x11D1,0xAD,0xC5,0x00,0x60,0x08,0xA5,0x84,0x8C);
  DEFINE_GUID(IID_IDEGetBlockFmtNamesParam,0x8D91090D,0xB955,0x11D1,0xAD,0xC5,0x00,0x60,0x08,0xA5,0x84,0x8C);

#ifdef __cplusplus
}
#endif
#endif
