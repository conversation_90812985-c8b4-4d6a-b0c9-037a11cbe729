/*
 * Copyright (C) 2009 <PERSON><PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "unknwn.idl";
import "devicetopology.idl";

typedef struct AUDIO_VOLUME_NOTIFICATION_DATA
{
    GUID guidEventContext;
    BOOL bMuted;
    FLOAT fMasterVolume;
    UINT nChannels;
    FLOAT afChannelVolumes[1];
} AUDIO_VOLUME_NOTIFICATION_DATA;

cpp_quote("typedef struct AUDIO_VOLUME_NOTIFICATION_DATA *PAUDIO_VOLUME_NOTIFICATION_DATA;")

cpp_quote("#define ENDPOINT_HARDWARE_SUPPORT_VOLUME 0x1")
cpp_quote("#define ENDPOINT_HARDWARE_SUPPORT_MUTE 0x2")
cpp_quote("#define ENDPOINT_HARDWARE_SUPPORT_METER 0x4")

interface IAudioEndpointVolumeCallback;
interface IAudioEndpointVolume;
interface IAudioEndpointVolumeEx;
interface IAudioMeterInformation;

[
    pointer_default(unique),
    nonextensible,
    uuid(657804fa-d6ad-4496-8a60-352752af4f89),
    local,
    object
]
interface IAudioEndpointVolumeCallback : IUnknown
{
    HRESULT OnNotify(
        AUDIO_VOLUME_NOTIFICATION_DATA *pNotify
    );
}

[
    pointer_default(unique),
    nonextensible,
    uuid(5cdf2c82-841e-4546-9722-0cf74078229a),
    local,
    object
]
interface IAudioEndpointVolume : IUnknown
{
    HRESULT RegisterControlChangeNotify(
        [in] IAudioEndpointVolumeCallback *pNotify
    );
    HRESULT UnregisterControlChangeNotify(
        [in] IAudioEndpointVolumeCallback *pNotify
    );
    HRESULT GetChannelCount(
        [out] UINT *pnChannelCount
    );
    HRESULT SetMasterVolumeLevel(
        [in] FLOAT fLevelDB,
        [unique,in] LPCGUID pguidEventContext
    );
    HRESULT SetMasterVolumeLevelScalar(
        [in] FLOAT fLevel,
        [unique,in] LPCGUID pguidEventContext
    );
    HRESULT GetMasterVolumeLevel(
        [out] FLOAT *fLevelDB
    );
    HRESULT GetMasterVolumeLevelScalar(
        [out] FLOAT *fLevel
    );
    HRESULT SetChannelVolumeLevel(
        [in] UINT nChannel,
        [in] FLOAT fLevelDB,
        [unique,in] LPCGUID pguidEventContext
    );
    HRESULT SetChannelVolumeLevelScalar(
        [in] UINT nChannel,
        [in] FLOAT fLevel,
        [unique,in] LPCGUID pguidEventContext
    );
    HRESULT GetChannelVolumeLevel(
        [in] UINT nChannel,
        [out] FLOAT *fLevelDB
    );
    HRESULT GetChannelVolumeLevelScalar(
        [in] UINT nChannel,
        [out] FLOAT *fLevel
    );
    HRESULT SetMute(
        [in] BOOL bMute,
        [unique,in] LPCGUID pguidEventContext
    );
    HRESULT GetMute(
        [out] BOOL *bMute
    );
    HRESULT GetVolumeStepInfo(
        [out] UINT *pnStep,
        [out] UINT *pnStepCount
    );
    HRESULT VolumeStepUp(
        [unique,in] LPCGUID pguidEventContext
    );
    HRESULT VolumeStepDown(
        [unique,in] LPCGUID pguidEventContext
    );
    HRESULT QueryHardwareSupport(
        [out] DWORD *pdwHardwareSupportMask
    );
    HRESULT GetVolumeRange(
        [out] FLOAT *pflVolumeMindB,
        [out] FLOAT *pflVolumeMaxdB,
        [out] FLOAT *pflVolumeIncrementdB
    );
}

[
    pointer_default(unique),
    nonextensible,
    uuid(66e11784-f695-4f28-a505-a7080081a78f),
    local,
    object
]
interface IAudioEndpointVolumeEx : IAudioEndpointVolume
{
    HRESULT GetVolumeRangeChannel(
        [in] UINT iChannel,
        [out] FLOAT *pflVolumeMindB,
        [out] FLOAT *pflVolumeMaxdB,
        [out] FLOAT *pflVolumeIncrementdB
    );
}
