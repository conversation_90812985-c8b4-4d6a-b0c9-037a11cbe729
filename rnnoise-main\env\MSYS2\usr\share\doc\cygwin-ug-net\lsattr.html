<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>lsattr</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="locale.html" title="locale"><link rel="next" href="minidumper.html" title="minidumper"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">lsattr</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="locale.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="minidumper.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="lsattr"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>lsattr &#8212; List file attributes</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">lsattr</code>  [-Radln] [<em class="replaceable"><code>FILE</code></em>...]</p></div><div class="cmdsynopsis"><p><code class="command">lsattr</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="lsattr-options"></a><h2>Options</h2><pre class="screen">
  -R, --recursive     recursively list attributes of directories and their
                      contents
  -V, --version       display the program version
  -a, --all           list all files in directories, including files that
                      start with '.'
  -d, --directory     list directories like other files, rather than listing
                      their contents.
  -l, --long          print options using long names instead of single
                      character abbreviations
  -n, --no-headers    don't print directory headers when recursing
  -h, --help          this help text
</pre></div><div class="refsect1"><a name="lsattr-desc"></a><h2>Description</h2><p>The <span class="command"><strong>lsattr</strong></span> program allows to list file
      attributes, namely DOS attributes, file sparseness, FS level
      encryption and compression state, as well as directories'
      case sensitivity setting.
    </p><p>Supported attributes:</p><pre class="screen">
  'r', 'Readonly':      file is read-only, directory is system-marked
  'h', 'Hidden':        file or directory is hidden
  's', 'System':        file or directory that the operating system uses
  'a', 'Archive':       file or directory has the archive marker set
  't', 'Temporary':     file is being used for temporary storage
  'S', 'Sparse':        file is sparse
  'r', 'Reparse':       file or directory that has a reparse point
  'c', 'Compressed':    file or directory is compressed
  'o', 'Offline':       the data of a file is moved to offline storage
  'n', 'Notindexed':    file or directory is not to be indexed by the
                        content indexing service
  'e', 'Encrypted':     file is encrypted
  'p', 'Pinned':        file is pinned
  'u', 'Unpinned':      file is unpinned
  'C', 'Casesensitive': directory is handled case sensitive
                        (Windows 10 1803 or later, local NTFS only,
                         WSL must be installed)
    </pre></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="locale.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="minidumper.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">locale&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;minidumper</td></tr></table></div></body></html>
