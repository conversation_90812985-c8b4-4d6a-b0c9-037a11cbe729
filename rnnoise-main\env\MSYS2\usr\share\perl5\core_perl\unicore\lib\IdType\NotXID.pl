# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V708
9
14
32
39
40
45
47
48
59
65
91
95
96
97
123
127
133
134
161
168
169
170
171
173
174
175
176
178
182
183
187
188
191
192
215
216
247
248
706
710
722
728
734
736
741
748
749
750
751
768
1014
1015
1154
1155
1160
1162
1370
1376
1417
1418
1421
1424
1470
1471
1472
1473
1475
1476
1478
1479
1536
1552
1563
1564
1565
1568
1642
1646
1748
1749
1757
1759
1769
1770
1792
1806
1807
1808
2038
2042
2046
2048
2096
2111
2142
2143
2184
2185
2192
2194
2274
2275
2404
2406
2416
2417
2546
2556
2557
2558
2678
2679
2800
2802
2928
2929
2930
2936
3056
3067
3191
3200
3204
3205
3407
3408
3416
3423
3440
3450
3572
3573
3647
3648
3663
3664
3674
3676
3841
3851
3853
3864
3866
3872
3882
3893
3894
3895
3896
3897
3898
3902
3973
3974
4030
4038
4039
4045
4046
4059
4170
4176
4254
4256
4347
4348
4960
4969
4978
4989
5008
5018
5120
5121
5741
5743
5760
5761
5787
5789
5867
5870
5941
5943
6100
6103
6104
6108
6128
6138
6144
6155
6464
6465
6468
6470
6622
6656
6686
6688
6816
6823
6824
6830
6846
6847
7002
7019
7028
7039
7164
7168
7227
7232
7294
7296
7360
7368
7379
7380
8210
8215
8216
8217
8218
8228
8232
8234
8240
8243
8245
8246
8248
8252
8253
8254
8257
8263
8266
8276
8277
8279
8280
8287
8352
8360
8361
8385
8413
8417
8418
8421
8452
8453
8456
8457
8468
8469
8471
8472
8478
8480
8483
8484
8485
8486
8487
8488
8489
8490
8506
8507
8513
8517
8522
8526
8527
8528
8586
8588
8592
8748
8750
8751
8753
9001
9003
9255
9280
9291
9451
10764
10765
10868
10871
10972
10973
11124
11126
11158
11159
11264
11493
11499
11513
11520
11632
11633
11776
11870
11904
11930
11931
11935
11936
12019
12272
12284
12289
12293
12296
12321
12336
12337
12343
12344
12349
12352
12688
12690
12736
12772
12872
12880
12927
12928
19904
19968
42128
42183
42238
42240
42509
42512
42608
42612
42622
42623
42738
42744
42752
42775
42784
42786
42889
42891
43048
43052
43056
43066
43124
43128
43214
43216
43256
43259
43260
43261
43310
43312
43359
43360
43457
43470
43486
43488
43612
43616
43639
43642
43742
43744
43760
43762
43867
43868
43882
43884
44011
44012
64434
64451
64830
64848
64975
64976
65021
65024
65093
65095
65529
65534
65792
65795
65799
65844
65847
65856
65909
65935
65936
65949
65952
65953
66000
66045
66273
66300
66336
66340
66463
66464
66512
66513
66927
66928
67671
67680
67703
67712
67751
67760
67835
67840
67862
67868
67871
67872
67903
67904
68028
68030
68032
68048
68050
68096
68160
68169
68176
68185
68221
68224
68253
68256
68296
68297
68331
68343
68409
68416
68440
68448
68472
68480
68505
68509
68521
68528
68858
68864
69216
69247
69293
69294
69405
69415
69457
69466
69510
69514
69573
69580
69703
69710
69714
69734
69819
69826
69837
69838
69952
69956
70004
70006
70085
70089
70093
70094
70107
70108
70109
70112
70113
70133
70200
70206
70313
70314
70731
70736
70746
70748
70749
70750
70854
70855
71105
71128
71233
71236
71264
71277
71353
71354
71482
71488
71739
71740
71914
71923
72004
72007
72162
72163
72255
72263
72346
72349
72350
72355
72448
72458
72769
72774
72794
72813
72816
72818
73463
73465
73539
73552
73664
73714
73727
73728
74864
74869
77809
77811
78896
78912
92782
92784
92917
92918
92983
92992
92996
92998
93019
93026
93824
93851
94178
94179
113820
113821
113823
113824
118608
118724
118784
119030
119040
119079
119081
119134
119146
119149
119171
119173
119180
119210
119214
119227
119233
119275
119296
119362
119365
119366
119488
119508
119520
119540
119552
119639
119648
119673
120832
121344
121399
121403
121453
121461
121462
121476
121477
121484
123215
123216
123647
123648
125127
125136
125278
125280
126065
126133
126209
126270
126704
126706
126976
127020
127024
127124
127136
127151
127153
127168
127169
127184
127185
127222
127243
127248
127279
127280
127312
127338
127341
127376
127377
127406
127462
127488
127584
127590
127744
128728
128732
128749
128752
128765
128768
128887
128891
128986
128992
129004
129008
129009
129024
129036
129040
129096
129104
129114
129120
129160
129168
129198
129200
129202
129280
129620
129632
129646
129648
129661
129664
129673
129680
129726
129727
129734
129742
129756
129760
129769
129776
129785
129792
129939
129940
129995
END
