/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _D2D1_EFFECTS_
#define _D2D1_EFFECTS_

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)           

DEFINE_GUID(CLSID_D2D12DAffineTransform,      0x6aa97485,0x6354,0x4cfc,0x90,0x8c,0xe4,0xa7,0x4f,0x62,0xc9,0x6c);
DEFINE_GUID(CLSID_D2D13DPerspectiveTransform, 0xc2844d0b,0x3d86,0x46e7,0x85,0xba,0x52,0x6c,0x92,0x40,0xf3,0xfb);
DEFINE_GUID(CLSID_D2D13DTransform,            0xe8467b04,0xec61,0x4b8a,0xb5,0xde,0xd4,0xd7,0x3d,0xeb,0xea,0x5a);
DEFINE_GUID(CLSID_D2D1ArithmeticComposite,    0xfc151437,0x049a,0x4784,0xa2,0x4a,0xf1,0xc4,0xda,0xf2,0x09,0x87);
DEFINE_GUID(CLSID_D2D1Atlas,                  0x913e2be4,0xfdcf,0x4fe2,0xa5,0xf0,0x24,0x54,0xf1,0x4f,0xf4,0x8);
DEFINE_GUID(CLSID_D2D1BitmapSource,           0x5fb6c24d,0xc6dd,0x4231,0x94,0x04,0x50,0xf4,0xd5,0xc3,0x25,0x2d);
DEFINE_GUID(CLSID_D2D1Blend,                  0x81c5b77b,0x13f8,0x4cdd,0xad,0x20,0xc8,0x90,0x54,0x7a,0xc6,0x5d);
DEFINE_GUID(CLSID_D2D1Border,                 0x2a2d49c0,0x4acf,0x43c7,0x8c,0x6a,0x7c,0x4a,0x27,0x87,0x4d,0x27);
DEFINE_GUID(CLSID_D2D1Brightness,             0x8cea8d1e,0x77b0,0x4986,0xb3,0xb9,0x2f,0x0c,0x0e,0xae,0x78,0x87);
DEFINE_GUID(CLSID_D2D1ColorManagement,        0x1a28524c,0xfdd6,0x4aa4,0xae,0x8f,0x83,0x7e,0xb8,0x26,0x7b,0x37);
DEFINE_GUID(CLSID_D2D1ColorMatrix,            0x921f03d6,0x641c,0x47df,0x85,0x2d,0xb4,0xbb,0x61,0x53,0xae,0x11);
DEFINE_GUID(CLSID_D2D1Composite,              0x48fc9f51,0xf6ac,0x48f1,0x8b,0x58,0x3b,0x28,0xac,0x46,0xf7,0x6d);
DEFINE_GUID(CLSID_D2D1ConvolveMatrix,         0x407f8c08,0x5533,0x4331,0xa3,0x41,0x23,0xcc,0x38,0x77,0x84,0x3e);
DEFINE_GUID(CLSID_D2D1Crop,                   0xe23f7110,0x0e9a,0x4324,0xaf,0x47,0x6a,0x2c,0x0c,0x46,0xf3,0x5b);
DEFINE_GUID(CLSID_D2D1DirectionalBlur,        0x174319a6,0x58e9,0x49b2,0xbb,0x63,0xca,0xf2,0xc8,0x11,0xa3,0xdb);
DEFINE_GUID(CLSID_D2D1DiscreteTransfer,       0x90866fcd,0x488e,0x454b,0xaf,0x06,0xe5,0x04,0x1b,0x66,0xc3,0x6c);
DEFINE_GUID(CLSID_D2D1DisplacementMap,        0xedc48364,0x0417,0x4111,0x94,0x50,0x43,0x84,0x5f,0xa9,0xf8,0x90);
DEFINE_GUID(CLSID_D2D1DistantDiffuse,         0x3e7efd62,0xa32d,0x46d4,0xa8,0x3c,0x52,0x78,0x88,0x9a,0xc9,0x54);
DEFINE_GUID(CLSID_D2D1DistantSpecular,        0x428c1ee5,0x77b8,0x4450,0x8a,0xb5,0x72,0x21,0x9c,0x21,0xab,0xda);
DEFINE_GUID(CLSID_D2D1DpiCompensation,        0x6c26c5c7,0x34e0,0x46fc,0x9c,0xfd,0xe5,0x82,0x37,0x6, 0xe2,0x28);
DEFINE_GUID(CLSID_D2D1Flood,                  0x61c23c20,0xae69,0x4d8e,0x94,0xcf,0x50,0x07,0x8d,0xf6,0x38,0xf2);
DEFINE_GUID(CLSID_D2D1GammaTransfer,          0x409444c4,0xc419,0x41a0,0xb0,0xc1,0x8c,0xd0,0xc0,0xa1,0x8e,0x42);
DEFINE_GUID(CLSID_D2D1GaussianBlur,           0x1feb6d69,0x2fe6,0x4ac9,0x8c,0x58,0x1d,0x7f,0x93,0xe7,0xa6,0xa5);
DEFINE_GUID(CLSID_D2D1Scale,                  0x9daf9369,0x3846,0x4d0e,0xa4,0x4e,0xc, 0x60,0x79,0x34,0xa5,0xd7);
DEFINE_GUID(CLSID_D2D1Histogram,              0x881db7d0,0xf7ee,0x4d4d,0xa6,0xd2,0x46,0x97,0xac,0xc6,0x6e,0xe8);
DEFINE_GUID(CLSID_D2D1HueRotation,            0x0f4458ec,0x4b32,0x491b,0x9e,0x85,0xbd,0x73,0xf4,0x4d,0x3e,0xb6);
DEFINE_GUID(CLSID_D2D1LinearTransfer,         0xad47c8fd,0x63ef,0x4acc,0x9b,0x51,0x67,0x97,0x9c,0x03,0x6c,0x06);
DEFINE_GUID(CLSID_D2D1LuminanceToAlpha,       0x41251ab7,0x0beb,0x46f8,0x9d,0xa7,0x59,0xe9,0x3f,0xcc,0xe5,0xde);
DEFINE_GUID(CLSID_D2D1Morphology,             0xeae6c40d,0x626a,0x4c2d,0xbf,0xcb,0x39,0x10,0x01,0xab,0xe2,0x02);
DEFINE_GUID(CLSID_D2D1OpacityMetadata,        0x6c53006a,0x4450,0x4199,0xaa,0x5b,0xad,0x16,0x56,0xfe,0xce,0x5e);
DEFINE_GUID(CLSID_D2D1PointDiffuse,           0xb9e303c3,0xc08c,0x4f91,0x8b,0x7b,0x38,0x65,0x6b,0xc4,0x8c,0x20);
DEFINE_GUID(CLSID_D2D1PointSpecular,          0x09c3ca26,0x3ae2,0x4f09,0x9e,0xbc,0xed,0x38,0x65,0xd5,0x3f,0x22);
DEFINE_GUID(CLSID_D2D1Premultiply,            0x06eab419,0xdeed,0x4018,0x80,0xd2,0x3e,0x1d,0x47,0x1a,0xde,0xb2);
DEFINE_GUID(CLSID_D2D1Saturation,             0x5cb2d9cf,0x327d,0x459f,0xa0,0xce,0x40,0xc0,0xb2,0x08,0x6b,0xf7);
DEFINE_GUID(CLSID_D2D1Shadow,                 0xc67ea361,0x1863,0x4e69,0x89,0xdb,0x69,0x5d,0x3e,0x9a,0x5b,0x6b);
DEFINE_GUID(CLSID_D2D1SpotDiffuse,            0x818a1105,0x7932,0x44f4,0xaa,0x86,0x08,0xae,0x7b,0x2f,0x2c,0x93);
DEFINE_GUID(CLSID_D2D1SpotSpecular,           0xedae421e,0x7654,0x4a37,0x9d,0xb8,0x71,0xac,0xc1,0xbe,0xb3,0xc1);
DEFINE_GUID(CLSID_D2D1TableTransfer,          0x5bf818c3,0x5e43,0x48cb,0xb6,0x31,0x86,0x83,0x96,0xd6,0xa1,0xd4);
DEFINE_GUID(CLSID_D2D1Tile,                   0xb0784138,0x3b76,0x4bc5,0xb1,0x3b,0x0f,0xa2,0xad,0x02,0x65,0x9f);
DEFINE_GUID(CLSID_D2D1Turbulence,             0xcf2bb6ae,0x889a,0x4ad7,0xba,0x29,0xa2,0xfd,0x73,0x2c,0x9f,0xc9);
DEFINE_GUID(CLSID_D2D1UnPremultiply,          0xfb9ac489,0xad8d,0x41ed,0x99,0x99,0xbb,0x63,0x47,0xd1,0x10,0xf7);

typedef enum D2D1_SHADOW_PROP {
    D2D1_SHADOW_PROP_BLUR_STANDARD_DEVIATION = 0,
    D2D1_SHADOW_PROP_COLOR                   = 1,
    D2D1_SHADOW_PROP_OPTIMIZATION            = 2,
    D2D1_SHADOW_PROP_FORCE_DWORD = 0xffffffff
} D2D1_SHADOW_PROP;

typedef enum D2D1_2DAFFINETRANSFORM_PROP {
    D2D1_2DAFFINETRANSFORM_PROP_INTERPOLATION_MODE = 0,
    D2D1_2DAFFINETRANSFORM_PROP_BORDER_MODE        = 1,
    D2D1_2DAFFINETRANSFORM_PROP_TRANSFORM_MATRIX   = 2,
    D2D1_2DAFFINETRANSFORM_PROP_SHARPNESS          = 3,
    D2D1_2DAFFINETRANSFORM_PROP_FORCE_DWORD = 0xffffffff
} D2D1_2DAFFINETRANSFORM_PROP;

typedef enum D2D1_COMPOSITE_PROP {
    D2D1_COMPOSITE_PROP_MODE = 0,
    D2D1_COMPOSITE_PROP_FORCE_DWORD = 0xffffffff
} D2D1_COMPOSITE_PROP;

typedef enum D2D1_CHANNEL_SELECTOR {
    D2D1_CHANNEL_SELECTOR_R = 0,
    D2D1_CHANNEL_SELECTOR_G = 1,
    D2D1_CHANNEL_SELECTOR_B = 2,
    D2D1_CHANNEL_SELECTOR_A = 3,
    D2D1_CHANNEL_SELECTOR_FORCE_DWORD = 0xffffffff
} D2D1_CHANNEL_SELECTOR;

typedef enum D2D1_BORDER_MODE {
    D2D1_BORDER_MODE_SOFT = 0,
    D2D1_BORDER_MODE_HARD = 1,
    D2D1_BORDER_MODE_FORCE_DWORD = 0xffffffff
} D2D1_BORDER_MODE;

typedef enum D2D1_COLORMATRIX_PROP {
    D2D1_COLORMATRIX_PROP_COLOR_MATRIX = 0,
    D2D1_COLORMATRIX_PROP_ALPHA_MODE   = 1,
    D2D1_COLORMATRIX_PROP_CLAMP_OUTPUT = 2,
    D2D1_COLORMATRIX_PROP_FORCE_DWORD  = 0xffffffff
} D2D1_COLORMATRIX_PROP;

typedef enum D2D1_COLORMATRIX_ALPHA_MODE {
    D2D1_COLORMATRIX_ALPHA_MODE_PREMULTIPLIED = 1,
    D2D1_COLORMATRIX_ALPHA_MODE_STRAIGHT      = 2,
    D2D1_COLORMATRIX_ALPHA_MODE_FORCE_DWORD   = 0xffffffff
} D2D1_COLORMATRIX_ALPHA_MODE;

typedef enum D2D1_2DAFFINETRANSFORM_INTERPOLATION_MODE {
    D2D1_2DAFFINETRANSFORM_INTERPOLATION_MODE_NEAREST_NEIGHBOR    = 0,
    D2D1_2DAFFINETRANSFORM_INTERPOLATION_MODE_LINEAR              = 1,
    D2D1_2DAFFINETRANSFORM_INTERPOLATION_MODE_CUBIC               = 2,
    D2D1_2DAFFINETRANSFORM_INTERPOLATION_MODE_MULTI_SAMPLE_LINEAR = 3,
    D2D1_2DAFFINETRANSFORM_INTERPOLATION_MODE_ANISOTROPIC         = 4,
    D2D1_2DAFFINETRANSFORM_INTERPOLATION_MODE_HIGH_QUALITY_CUBIC  = 5,
    D2D1_2DAFFINETRANSFORM_INTERPOLATION_MODE_FORCE_DWORD         = 0xffffffff
} D2D1_2DAFFINETRANSFORM_INTERPOLATION_MODE;

typedef enum D2D1_BLEND_PROP {
    D2D1_BLEND_PROP_MODE = 0,
    D2D1_BLEND_PROP_FORCE_DWORD = 0xffffffff
} D2D1_BLEND_PROP;

typedef enum D2D1_BLEND_MODE {
    D2D1_BLEND_MODE_MULTIPLY      = 0,
    D2D1_BLEND_MODE_SCREEN        = 1,
    D2D1_BLEND_MODE_DARKEN        = 2,
    D2D1_BLEND_MODE_LIGHTEN       = 3,
    D2D1_BLEND_MODE_DISSOLVE      = 4,
    D2D1_BLEND_MODE_COLOR_BURN    = 5,
    D2D1_BLEND_MODE_LINEAR_BURN   = 6,
    D2D1_BLEND_MODE_DARKER_COLOR  = 7,
    D2D1_BLEND_MODE_LIGHTER_COLOR = 8,
    D2D1_BLEND_MODE_COLOR_DODGE   = 9,
    D2D1_BLEND_MODE_LINEAR_DODGE  = 10,
    D2D1_BLEND_MODE_OVERLAY       = 11,
    D2D1_BLEND_MODE_SOFT_LIGHT    = 12,
    D2D1_BLEND_MODE_HARD_LIGHT    = 13,
    D2D1_BLEND_MODE_VIVID_LIGHT   = 14,
    D2D1_BLEND_MODE_LINEAR_LIGHT  = 15,
    D2D1_BLEND_MODE_PIN_LIGHT     = 16,
    D2D1_BLEND_MODE_HARD_MIX      = 17,
    D2D1_BLEND_MODE_DIFFERENCE    = 18,
    D2D1_BLEND_MODE_EXCLUSION     = 19,
    D2D1_BLEND_MODE_HUE           = 20,
    D2D1_BLEND_MODE_SATURATION    = 21,
    D2D1_BLEND_MODE_COLOR         = 22,
    D2D1_BLEND_MODE_LUMINOSITY    = 23,
    D2D1_BLEND_MODE_SUBTRACT      = 24,
    D2D1_BLEND_MODE_DIVISION      = 25,
    D2D1_BLEND_MODE_FORCE_DWORD   = 0xffffffff
} D2D1_BLEND_MODE;

typedef enum D2D1_MORPHOLOGY_PROP {
    D2D1_MORPHOLOGY_PROP_MODE   = 0,
    D2D1_MORPHOLOGY_PROP_WIDTH  = 1,
    D2D1_MORPHOLOGY_PROP_HEIGHT = 2,
    D2D1_MORPHOLOGY_PROP_FORCE_DWORD = 0xffffffff
} D2D1_MORPHOLOGY_PROP;

typedef enum D2D1_FLOOD_PROP {
    D2D1_FLOOD_PROP_COLOR = 0,
    D2D1_FLOOD_PROP_FORCE_DWORD = 0xffffffff
} D2D1_FLOOD_PROP;

typedef enum D2D1_MORPHOLOGY_MODE {
    D2D1_MORPHOLOGY_MODE_ERODE  = 0,
    D2D1_MORPHOLOGY_MODE_DILATE = 1,
    D2D1_MORPHOLOGY_MODE_FORCE_DWORD = 0xffffffff
} D2D1_MORPHOLOGY_MODE;

typedef enum D2D1_TURBULENCE_NOISE {
    D2D1_TURBULENCE_NOISE_FRACTAL_SUM = 0,
    D2D1_TURBULENCE_NOISE_TURBULENCE  = 1,
    D2D1_TURBULENCE_NOISE_FORCE_DWORD = 0xffffffff
} D2D1_TURBULENCE_NOISE;

typedef enum D2D1_DISPLACEMENTMAP_PROP {
    D2D1_DISPLACEMENTMAP_PROP_SCALE            = 0,
    D2D1_DISPLACEMENTMAP_PROP_X_CHANNEL_SELECT = 1,
    D2D1_DISPLACEMENTMAP_PROP_Y_CHANNEL_SELECT = 2,
    D2D1_DISPLACEMENTMAP_PROP_FORCE_DWORD      = 0xffffffff
} D2D1_DISPLACEMENTMAP_PROP;

typedef enum D2D1_TURBULENCE_PROP {
    D2D1_TURBULENCE_PROP_OFFSET         = 0,
    D2D1_TURBULENCE_PROP_SIZE           = 1,
    D2D1_TURBULENCE_PROP_BASE_FREQUENCY = 2,
    D2D1_TURBULENCE_PROP_NUM_OCTAVES    = 3,
    D2D1_TURBULENCE_PROP_SEED           = 4,
    D2D1_TURBULENCE_PROP_NOISE          = 5,
    D2D1_TURBULENCE_PROP_STITCHABLE     = 6,
    D2D1_TURBULENCE_PROP_FORCE_DWORD    = 0xffffffff
} D2D1_TURBULENCE_PROP;

typedef enum D2D1_ARITHMETICCOMPOSITE_PROP {
    D2D1_ARITHMETICCOMPOSITE_PROP_COEFFICIENTS = 0,
    D2D1_ARITHMETICCOMPOSITE_PROP_CLAMP_OUTPUT = 1,
    D2D1_ARITHMETICCOMPOSITE_PROP_FORCE_DWORD  = 0xffffffff
} D2D1_ARITHMETICCOMPOSITE_PROP;

typedef enum D2D1_CROP_PROP {
    D2D1_CROP_PROP_RECT        = 0,
    D2D1_CROP_PROP_BORDER_MODE = 1,
    D2D1_CROP_PROP_FORCE_DWORD = 0xffffffff
} D2D1_CROP_PROP;

typedef enum D2D1_GAUSSIANBLUR_PROP {
    D2D1_GAUSSIANBLUR_PROP_STANDARD_DEVIATION = 0,
    D2D1_GAUSSIANBLUR_PROP_OPTIMIZATION       = 1,
    D2D1_GAUSSIANBLUR_PROP_BORDER_MODE        = 2,
    D2D1_GAUSSIANBLUR_PROP_FORCE_DWORD        = 0xffffffff
} D2D1_GAUSSIANBLUR_PROP;

typedef enum D2D1_DIRECTIONALBLUR_PROP {
    D2D1_DIRECTIONALBLUR_PROP_STANDARD_DEVIATION = 0,
    D2D1_DIRECTIONALBLUR_PROP_ANGLE              = 1,
    D2D1_DIRECTIONALBLUR_PROP_OPTIMIZATION       = 2,
    D2D1_DIRECTIONALBLUR_PROP_BORDER_MODE        = 3,
    D2D1_DIRECTIONALBLUR_PROP_FORCE_DWORD        = 0xffffffff
} D2D1_DIRECTIONALBLUR_PROP;

typedef enum D2D1_SPOTDIFFUSE_PROP {
    D2D1_SPOTDIFFUSE_PROP_LIGHT_POSITION      = 0,
    D2D1_SPOTDIFFUSE_PROP_POINTS_AT           = 1,
    D2D1_SPOTDIFFUSE_PROP_FOCUS               = 2,
    D2D1_SPOTDIFFUSE_PROP_LIMITING_CONE_ANGLE = 3,
    D2D1_SPOTDIFFUSE_PROP_DIFFUSE_CONSTANT    = 4,
    D2D1_SPOTDIFFUSE_PROP_SURFACE_SCALE       = 5,
    D2D1_SPOTDIFFUSE_PROP_COLOR               = 6,
    D2D1_SPOTDIFFUSE_PROP_KERNEL_UNIT_LENGTH  = 7,
    D2D1_SPOTDIFFUSE_PROP_SCALE_MODE          = 8,
    D2D1_SPOTDIFFUSE_PROP_FORCE_DWORD         = 0xffffffff
} D2D1_SPOTDIFFUSE_PROP;

typedef enum D2D1_BORDER_PROP {
    D2D1_BORDER_PROP_EDGE_MODE_X = 0,
    D2D1_BORDER_PROP_EDGE_MODE_Y = 1,
    D2D1_BORDER_PROP_FORCE_DWORD = 0xffffffff
} D2D1_BORDER_PROP;

typedef enum D2D1_POINTDIFFUSE_PROP {
    D2D1_POINTDIFFUSE_PROP_LIGHT_POSITION     = 0,
    D2D1_POINTDIFFUSE_PROP_DIFFUSE_CONSTANT   = 1,
    D2D1_POINTDIFFUSE_PROP_SURFACE_SCALE      = 2,
    D2D1_POINTDIFFUSE_PROP_COLOR              = 3,
    D2D1_POINTDIFFUSE_PROP_KERNEL_UNIT_LENGTH = 4,
    D2D1_POINTDIFFUSE_PROP_SCALE_MODE         = 5,
    D2D1_POINTDIFFUSE_PROP_FORCE_DWORD        = 0xffffffff
} D2D1_POINTDIFFUSE_PROP;

typedef enum D2D1_TABLETRANSFER_PROP {
    D2D1_TABLETRANSFER_PROP_RED_TABLE     = 0,
    D2D1_TABLETRANSFER_PROP_RED_DISABLE   = 1,
    D2D1_TABLETRANSFER_PROP_GREEN_TABLE   = 2,
    D2D1_TABLETRANSFER_PROP_GREEN_DISABLE = 3,
    D2D1_TABLETRANSFER_PROP_BLUE_TABLE    = 4,
    D2D1_TABLETRANSFER_PROP_BLUE_DISABLE  = 5,
    D2D1_TABLETRANSFER_PROP_ALPHA_TABLE   = 6,
    D2D1_TABLETRANSFER_PROP_ALPHA_DISABLE = 7,
    D2D1_TABLETRANSFER_PROP_CLAMP_OUTPUT  = 8,
    D2D1_TABLETRANSFER_PROP_FORCE_DWORD   = 0xffffffff
} D2D1_TABLETRANSFER_PROP;

typedef enum D2D1_DISCRETETRANSFER_PROP {
    D2D1_DISCRETETRANSFER_PROP_RED_TABLE     = 0,
    D2D1_DISCRETETRANSFER_PROP_RED_DISABLE   = 1,
    D2D1_DISCRETETRANSFER_PROP_GREEN_TABLE   = 2,
    D2D1_DISCRETETRANSFER_PROP_GREEN_DISABLE = 3,
    D2D1_DISCRETETRANSFER_PROP_BLUE_TABLE    = 4,
    D2D1_DISCRETETRANSFER_PROP_BLUE_DISABLE  = 5,
    D2D1_DISCRETETRANSFER_PROP_ALPHA_TABLE   = 6,
    D2D1_DISCRETETRANSFER_PROP_ALPHA_DISABLE = 7,
    D2D1_DISCRETETRANSFER_PROP_CLAMP_OUTPUT  = 8,
    D2D1_DISCRETETRANSFER_PROP_FORCE_DWORD   = 0xffffffff
} D2D1_DISCRETETRANSFER_PROP;

typedef enum D2D1_LINEARTRANSFER_PROP {
    D2D1_LINEARTRANSFER_PROP_RED_Y_INTERCEPT   = 0,
    D2D1_LINEARTRANSFER_PROP_RED_SLOPE         = 1,
    D2D1_LINEARTRANSFER_PROP_RED_DISABLE       = 2,
    D2D1_LINEARTRANSFER_PROP_GREEN_Y_INTERCEPT = 3,
    D2D1_LINEARTRANSFER_PROP_GREEN_SLOPE       = 4,
    D2D1_LINEARTRANSFER_PROP_GREEN_DISABLE     = 5,
    D2D1_LINEARTRANSFER_PROP_BLUE_Y_INTERCEPT  = 6,
    D2D1_LINEARTRANSFER_PROP_BLUE_SLOPE        = 7,
    D2D1_LINEARTRANSFER_PROP_BLUE_DISABLE      = 8,
    D2D1_LINEARTRANSFER_PROP_ALPHA_Y_INTERCEPT = 9,
    D2D1_LINEARTRANSFER_PROP_ALPHA_SLOPE       = 10,
    D2D1_LINEARTRANSFER_PROP_ALPHA_DISABLE     = 11,
    D2D1_LINEARTRANSFER_PROP_CLAMP_OUTPUT      = 12,
    D2D1_LINEARTRANSFER_PROP_FORCE_DWORD       = 0xffffffff
} D2D1_LINEARTRANSFER_PROP;

typedef enum D2D1_GAMMATRANSFER_PROP {
    D2D1_GAMMATRANSFER_PROP_RED_AMPLITUDE   = 0,
    D2D1_GAMMATRANSFER_PROP_RED_EXPONENT    = 1,
    D2D1_GAMMATRANSFER_PROP_RED_OFFSET      = 2,
    D2D1_GAMMATRANSFER_PROP_RED_DISABLE     = 3,
    D2D1_GAMMATRANSFER_PROP_GREEN_AMPLITUDE = 4,
    D2D1_GAMMATRANSFER_PROP_GREEN_EXPONENT  = 5,
    D2D1_GAMMATRANSFER_PROP_GREEN_OFFSET    = 6,
    D2D1_GAMMATRANSFER_PROP_GREEN_DISABLE   = 7,
    D2D1_GAMMATRANSFER_PROP_BLUE_AMPLITUDE  = 8,
    D2D1_GAMMATRANSFER_PROP_BLUE_EXPONENT   = 9,
    D2D1_GAMMATRANSFER_PROP_BLUE_OFFSET     = 10,
    D2D1_GAMMATRANSFER_PROP_BLUE_DISABLE    = 11,
    D2D1_GAMMATRANSFER_PROP_ALPHA_AMPLITUDE = 12,
    D2D1_GAMMATRANSFER_PROP_ALPHA_EXPONENT  = 13,
    D2D1_GAMMATRANSFER_PROP_ALPHA_OFFSET    = 14,
    D2D1_GAMMATRANSFER_PROP_ALPHA_DISABLE   = 15,
    D2D1_GAMMATRANSFER_PROP_CLAMP_OUTPUT    = 16,
    D2D1_GAMMATRANSFER_PROP_FORCE_DWORD     = 0xffffffff
} D2D1_GAMMATRANSFER_PROP;

typedef enum D2D1_CONVOLVEMATRIX_PROP {
    D2D1_CONVOLVEMATRIX_PROP_KERNEL_UNIT_LENGTH = 0,
    D2D1_CONVOLVEMATRIX_PROP_SCALE_MODE         = 1,
    D2D1_CONVOLVEMATRIX_PROP_KERNEL_SIZE_X      = 2,
    D2D1_CONVOLVEMATRIX_PROP_KERNEL_SIZE_Y      = 3,
    D2D1_CONVOLVEMATRIX_PROP_KERNEL_MATRIX      = 4,
    D2D1_CONVOLVEMATRIX_PROP_DIVISOR            = 5,
    D2D1_CONVOLVEMATRIX_PROP_BIAS               = 6,
    D2D1_CONVOLVEMATRIX_PROP_KERNEL_OFFSET      = 7,
    D2D1_CONVOLVEMATRIX_PROP_PRESERVE_ALPHA     = 8,
    D2D1_CONVOLVEMATRIX_PROP_BORDER_MODE        = 9,
    D2D1_CONVOLVEMATRIX_PROP_CLAMP_OUTPUT       = 10,
    D2D1_CONVOLVEMATRIX_PROP_FORCE_DWORD        = 0xffffffff
} D2D1_CONVOLVEMATRIX_PROP;

typedef enum D2D1_BORDER_EDGE_MODE {
    D2D1_BORDER_EDGE_MODE_CLAMP       = 0,
    D2D1_BORDER_EDGE_MODE_WRAP        = 1,
    D2D1_BORDER_EDGE_MODE_MIRROR      = 2,
    D2D1_BORDER_EDGE_MODE_FORCE_DWORD = 0xffffffff
} D2D1_BORDER_EDGE_MODE;

typedef enum D2D1_DISTANTDIFFUSE_PROP {
    D2D1_DISTANTDIFFUSE_PROP_AZIMUTH            = 0,
    D2D1_DISTANTDIFFUSE_PROP_ELEVATION          = 1,
    D2D1_DISTANTDIFFUSE_PROP_DIFFUSE_CONSTANT   = 2,
    D2D1_DISTANTDIFFUSE_PROP_SURFACE_SCALE      = 3,
    D2D1_DISTANTDIFFUSE_PROP_COLOR              = 4,
    D2D1_DISTANTDIFFUSE_PROP_KERNEL_UNIT_LENGTH = 5,
    D2D1_DISTANTDIFFUSE_PROP_SCALE_MODE         = 6,
    D2D1_DISTANTDIFFUSE_PROP_FORCE_DWORD        = 0xffffffff
} D2D1_DISTANTDIFFUSE_PROP;

typedef enum D2D1_POINTSPECULAR_PROP {
    D2D1_POINTSPECULAR_PROP_LIGHT_POSITION     = 0,
    D2D1_POINTSPECULAR_PROP_SPECULAR_EXPONENT  = 1,
    D2D1_POINTSPECULAR_PROP_SPECULAR_CONSTANT  = 2,
    D2D1_POINTSPECULAR_PROP_SURFACE_SCALE      = 3,
    D2D1_POINTSPECULAR_PROP_COLOR              = 4,
    D2D1_POINTSPECULAR_PROP_KERNEL_UNIT_LENGTH = 5,
    D2D1_POINTSPECULAR_PROP_SCALE_MODE         = 6,
    D2D1_POINTSPECULAR_PROP_FORCE_DWORD        = 0xffffffff
} D2D1_POINTSPECULAR_PROP;

typedef enum D2D1_SPOTSPECULAR_PROP {
    D2D1_SPOTSPECULAR_PROP_LIGHT_POSITION      = 0,
    D2D1_SPOTSPECULAR_PROP_POINTS_AT           = 1,
    D2D1_SPOTSPECULAR_PROP_FOCUS               = 2,
    D2D1_SPOTSPECULAR_PROP_LIMITING_CONE_ANGLE = 3,
    D2D1_SPOTSPECULAR_PROP_SPECULAR_EXPONENT   = 4,
    D2D1_SPOTSPECULAR_PROP_SPECULAR_CONSTANT   = 5,
    D2D1_SPOTSPECULAR_PROP_SURFACE_SCALE       = 6,
    D2D1_SPOTSPECULAR_PROP_COLOR               = 7,
    D2D1_SPOTSPECULAR_PROP_KERNEL_UNIT_LENGTH  = 8,
    D2D1_SPOTSPECULAR_PROP_SCALE_MODE          = 9,
    D2D1_SPOTSPECULAR_PROP_FORCE_DWORD         = 0xffffffff
} D2D1_SPOTSPECULAR_PROP;

typedef enum D2D1_DISTANTSPECULAR_PROP {
    D2D1_DISTANTSPECULAR_PROP_AZIMUTH            = 0,
    D2D1_DISTANTSPECULAR_PROP_ELEVATION          = 1,
    D2D1_DISTANTSPECULAR_PROP_SPECULAR_EXPONENT  = 2,
    D2D1_DISTANTSPECULAR_PROP_SPECULAR_CONSTANT  = 3,
    D2D1_DISTANTSPECULAR_PROP_SURFACE_SCALE      = 4,
    D2D1_DISTANTSPECULAR_PROP_COLOR              = 5,
    D2D1_DISTANTSPECULAR_PROP_KERNEL_UNIT_LENGTH = 6,
    D2D1_DISTANTSPECULAR_PROP_SCALE_MODE         = 7,
    D2D1_DISTANTSPECULAR_PROP_FORCE_DWORD        = 0xffffffff
} D2D1_DISTANTSPECULAR_PROP;

typedef enum D2D1_TILE_PROP {
    D2D1_TILE_PROP_RECT = 0,
    D2D1_TILE_PROP_FORCE_DWORD = 0xffffffff
} D2D1_TILE_PROP;

#endif
#endif /* _D2D1_EFFECTS_ */
