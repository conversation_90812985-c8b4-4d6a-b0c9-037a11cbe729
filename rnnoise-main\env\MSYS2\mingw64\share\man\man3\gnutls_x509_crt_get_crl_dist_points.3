.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_crl_dist_points" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_crl_dist_points \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_crl_dist_points(gnutls_x509_crt_t " cert ", unsigned int " seq ", void * " san ", size_t * " san_size ", unsigned int * " reason_flags ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "unsigned int seq" 12
specifies the sequence number of the distribution point (0 for the first one, 1 for the second etc.)
.IP "void * san" 12
is the place where the distribution point will be copied to
.IP "size_t * san_size" 12
holds the size of ret.
.IP "unsigned int * reason_flags" 12
Revocation reasons. An ORed sequence of flags from \fBgnutls_x509_crl_reason_flags_t\fP.
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical (may be null)
.SH "DESCRIPTION"
This function retrieves the CRL distribution points (*********),
contained in the given certificate in the X509v3 Certificate
Extensions.
.SH "RETURNS"
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP and updates  \fIret_size\fP if
 \fIret_size\fP is not enough to hold the distribution point, or the
type of the distribution point if everything was ok. The type is
one of the enumerated \fBgnutls_x509_subject_alt_name_t\fP.  If the
certificate does not have an Alternative name with the specified
sequence number then \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is
returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
