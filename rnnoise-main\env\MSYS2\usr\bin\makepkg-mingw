#!/usr/bin/env bash
#   makepkg-mingw - wrapper for makepkg to build mingw-w64 packages under  MSYS2
#
#   Copyright (c) 2013 <PERSON><PERSON> <<EMAIL>>
#
#   This program is free software; you can redistribute it and/or modify
#   it under the terms of the GNU General Public License as published by
#   the Free Software Foundation; either version 2 of the License, or
#   (at your option) any later version.
#
#   This program is distributed in the hope that it will be useful,
#   but WITHOUT ANY WARRANTY; without even the implied warranty of
#   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#   GNU General Public License for more details.
#
#   You should have received a copy of the GNU General Public License
#   along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

set -e

plain() {
  local mesg=$1; shift
  printf "${BOLD}    ${mesg}${ALL_OFF}\n" "$@" >&2
}

print_warning() {
  local mesg=$1; shift
  printf "${YELLOW}=> WARNING:${ALL_OFF}${BOLD} ${mesg}${ALL_OFF}\n" "$@" >&2
}

print_msg1() {
  local mesg=$1; shift
  printf "${GREEN}==> ${ALL_OFF}${BOLD} ${mesg}${ALL_OFF}\n" "$@" >&2
}

print_msg2() {
  local mesg=$1; shift
  printf "${BLUE}  ->${ALL_OFF}${BOLD} ${mesg}${ALL_OFF}\n" "$@" >&2
}

print_error() {
  local mesg=$1; shift
  printf "${RED}==> ERROR:${ALL_OFF}${BOLD} ${mesg}${ALL_OFF}\n" "$@" >&2
}

if /usr/bin/tput setaf 0 &>/dev/null; then
  ALL_OFF="$(/usr/bin/tput sgr0)"
  BOLD="$(/usr/bin/tput bold)"
  BLUE="${BOLD}$(/usr/bin/tput setaf 4)"
  GREEN="${BOLD}$(/usr/bin/tput setaf 2)"
  RED="${BOLD}$(/usr/bin/tput setaf 1)"
  YELLOW="${BOLD}$(/usr/bin/tput setaf 3)"
else
  ALL_OFF="\e[1;0m"
  BOLD="\e[1;1m"
  BLUE="${BOLD}\e[1;34m"
  GREEN="${BOLD}\e[1;32m"
  RED="${BOLD}\e[1;31m"
  YELLOW="${BOLD}\e[1;33m"
fi

readonly ALL_OFF BOLD BLUE GREEN RED YELLOW

# For certain flags skip our processing and just forward to makepkg
for _arg in "$@"; do
  if [ "${_arg}" = "--help" ] || [ "${_arg}" = "-h" ]; then
    /usr/bin/makepkg "$@"
    echo -e "makepkg-mingw:\n"
    echo '  $MINGW_ARCH      Space separated list of environments to build for.'
    echo '                   Defaults to the active environment.'
    exit 0
  fi
  if [ "${_arg}" = "--version" ] || [ "${_arg}" = "-V" ]; then
    /usr/bin/makepkg "$@"
    exit 0
  fi
done

# For backwards compatibility
if [[ -z "${MINGW_ARCH}" ]] && [[ -n "${MINGW_INSTALLS}" ]]; then
  print_warning "MINGW_INSTALLS is deprecated, use MINGW_ARCH instead"
  MINGW_ARCH="${MINGW_INSTALLS}";
fi

# Validate or set MINGW_ARCH
declare -a MINGW_ARCH_ALLOWED
for _conf in /etc/makepkg_mingw.d/*.conf; do
  MINGW_ARCH_ALLOWED+=("$(basename "$_conf" .conf)")
done

MINGW_ARCH="${MINGW_ARCH,,}"
if [[ -z "$MINGW_ARCH" ]]; then
  # In case MINGW_ARCH isn't set we default to MSYSTEM, or error out
  if [[ " ${MINGW_ARCH_ALLOWED[*]} " = *" ${MSYSTEM,,} "* ]]; then
    MINGW_ARCH="${MSYSTEM,,}"
  else
    print_warning 'MINGW_ARCH not set and not called from a MINGW environment, defaulting to mingw64. This will fail in the future!'
    MINGW_ARCH='mingw64'
  fi
else
  # Make sure MINGW_ARCH is valid
  for _mingw in ${MINGW_ARCH}; do
    if [[ ! " ${MINGW_ARCH_ALLOWED[*]} " = *" ${_mingw} "* ]]; then
      print_error "MINGW_ARCH: '${_mingw}' unknown, possible values: ${MINGW_ARCH_ALLOWED[*]}"
      exit 1
    fi
  done
fi
print_msg1 "MINGW_ARCH: ${MINGW_ARCH}"

for _mingw in ${MINGW_ARCH}; do
  print_msg2 "Building ${_mingw}..."

  MSYSTEM="${_mingw^^}" \
  CHERE_INVOKING=1 \
    bash -leo pipefail -c "/usr/bin/makepkg --config /etc/makepkg_mingw.conf \"\$@\"" bash "$@"

done

exit 0
