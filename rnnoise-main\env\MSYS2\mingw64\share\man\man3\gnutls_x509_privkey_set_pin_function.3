.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_set_pin_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_set_pin_function \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void gnutls_x509_privkey_set_pin_function(gnutls_x509_privkey_t " privkey ", gnutls_pin_callback_t " fn ", void * " userdata ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t privkey" 12
The certificate structure
.IP "gnutls_pin_callback_t fn" 12
the callback
.IP "void * userdata" 12
data associated with the callback
.SH "DESCRIPTION"
This function will set a callback function to be used when
it is required to access a protected object. This function overrides 
the global function set using \fBgnutls_pkcs11_set_pin_function()\fP.

Note that this callback is used when decrypting a key.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
