<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_sleep</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_sleep - delay execution for a specified number of milliseconds</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/crypto.h&gt;

void OSSL_sleep(uint64_t millis);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>OSSL_sleep() is a convenience function to delay execution of the calling thread for (at least) <i>millis</i> milliseconds. The delay is not guaranteed; it may be affected by system activity, by the time spent processing the call, limitation on the underlying system call parameter size or by system timer granularity.</p>

<p>In particular on Windows the maximum amount of time it will sleep is 49 days and on systems where the regular sleep(3) is used as the underlying system call the maximum sleep time is about 136 years.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_sleep() does not return any value.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>OSSL_sleep() was added in OpenSSL 3.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2022-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


