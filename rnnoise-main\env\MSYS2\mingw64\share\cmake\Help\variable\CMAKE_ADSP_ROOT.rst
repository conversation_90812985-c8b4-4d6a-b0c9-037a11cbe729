CMAKE_ADSP_ROOT
---------------

.. versionadded:: 3.24

When :ref:`Cross Compiling for ADSP SHARC/Blackfin`,
this variable holds the absolute path to the latest CCES or VDSP++ install.
The directory is expected to contain the ``cc21k.exe`` and ``ccblkfn.exe`` compilers.
This will be set automatically if a default install of CCES or VDSP++ can be found.

See also the :envvar:`ADSP_ROOT` environment variable.
