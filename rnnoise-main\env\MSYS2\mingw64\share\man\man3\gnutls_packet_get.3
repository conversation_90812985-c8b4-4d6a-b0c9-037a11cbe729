.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_packet_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_packet_get \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_packet_get(gnutls_packet_t " packet ", gnutls_datum_t * " data ", unsigned char * " sequence ");"
.SH ARGUMENTS
.IP "gnutls_packet_t packet" 12
is a \fBgnutls_packet_t\fP type.
.IP "gnutls_datum_t * data" 12
will contain the data present in the  \fIpacket\fP structure (may be \fBNULL\fP)
.IP "unsigned char * sequence" 12
the 8\-bytes of the packet sequence number (may be \fBNULL\fP)
.SH "DESCRIPTION"
This function returns the data and sequence number associated with
the received packet.
.SH "SINCE"
3.3.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
