.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_ocsp_status_request_mem" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_ocsp_status_request_mem \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_ocsp_status_request_mem(gnutls_certificate_credentials_t " sc ", const gnutls_datum_t * " resp_data ", unsigned " idx ", gnutls_x509_crt_fmt_t " fmt ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t sc" 12
is a credentials structure.
.IP "const gnutls_datum_t * resp_data" 12
a memory buffer holding an OCSP response
.IP "unsigned idx" 12
is a certificate index as returned by \fBgnutls_certificate_set_key()\fP and friends
.IP "gnutls_x509_crt_fmt_t fmt" 12
is PEM or DER
.SH "DESCRIPTION"
This function sets the OCSP responses to be sent to the
peer for the certificate chain specified by  \fIidx\fP . When  \fIfmt\fP is set
to PEM, multiple responses can be loaded.
.SH "NOTE"
the ability to set multiple OCSP responses per credential
structure via the index  \fIidx\fP was added in version 3.5.6. To keep
backwards compatibility, it requires using \fBgnutls_certificate_set_flags()\fP
with the \fBGNUTLS_CERTIFICATE_API_V2\fP flag to make the set certificate
functions return an index usable by this function.

This function must be called after setting any certificates, and
cannot be used for certificates that are provided via a callback \-\-
that is when \fBgnutls_certificate_set_retrieve_function()\fP is used.

This function can be called multiple times when multiple responses which
apply to the certificate chain are available.
If the response provided does not match any certificates present
in the chain, the code \fBGNUTLS_E_OCSP_MISMATCH_WITH_CERTS\fP is returned.
If the response is already expired at the time of loading the code
\fBGNUTLS_E_EXPIRED\fP is returned.
.SH "RETURNS"
On success, the number of loaded responses is returned,
otherwise a negative error code.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
