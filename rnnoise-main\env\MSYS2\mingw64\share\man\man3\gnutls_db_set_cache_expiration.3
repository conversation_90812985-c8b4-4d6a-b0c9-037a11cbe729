.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_db_set_cache_expiration" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_db_set_cache_expiration \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_db_set_cache_expiration(gnutls_session_t " session ", int " seconds ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "int seconds" 12
is the number of seconds.
.SH "DESCRIPTION"
Set the expiration time for resumed sessions. The default is 21600
(6 hours) at the time of writing.

The maximum value that can be set using this function is 604800
(7 days).
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
