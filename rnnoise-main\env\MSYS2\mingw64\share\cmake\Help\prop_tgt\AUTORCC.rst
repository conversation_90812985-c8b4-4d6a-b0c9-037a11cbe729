AUTORCC
-------

Should the target be processed with auto-rcc (for Qt projects).

``AUTORCC`` is a boolean specifying whether <PERSON><PERSON><PERSON> will handle
the Qt ``rcc`` code generator automatically, i.e. without having to use
commands like :module:`QT4_ADD_RESOURCES() <FindQt4>`, `qt5_add_resources()`_,
etc.  Currently, Qt versions 4 to 6 are supported.

.. _`qt5_add_resources()`: https://doc.qt.io/qt-5/qtcore-cmake-qt5-add-resources.html

When this property is ``ON``, CMake will handle ``.qrc`` files added
as target sources at build time and invoke ``rcc`` accordingly.
This property is initialized by the value of the :variable:`CMAKE_AUTORCC`
variable if it is set when a target is created.

By default ``AUTORCC`` is processed by a
:command:`custom command <add_custom_command>`.
If the ``.qrc`` file is :prop_sf:`GENERATED`, a
:command:`custom target <add_custom_target>` is used instead.

When there are multiple ``.qrc`` files with the same name, <PERSON><PERSON><PERSON> will
generate unspecified unique output file names for ``rcc``.  Therefore, if
``Q_INIT_RESOURCE()`` or ``Q_CLEANUP_RESOURCE()`` need to be used, the
``.qrc`` file name must be unique.


Modifiers
^^^^^^^^^

:prop_tgt:`AUTORCC_EXECUTABLE`:
The ``rcc`` executable will be detected automatically, but can be forced to
a certain binary by setting this target property.

:prop_tgt:`AUTORCC_OPTIONS`:
Additional command line options for ``rcc`` can be set via this target
property.  The corresponding :prop_sf:`AUTORCC_OPTIONS` source file property
can be used to specify options to be applied only to a specific ``.qrc`` file.

:prop_sf:`SKIP_AUTORCC`:
``.qrc`` files can be excluded from ``AUTORCC`` processing by
setting this source file property.

:prop_sf:`SKIP_AUTOGEN`:
Source files can be excluded from :prop_tgt:`AUTOMOC`,
:prop_tgt:`AUTOUIC` and ``AUTORCC`` processing by
setting this source file property.

:prop_gbl:`AUTOGEN_SOURCE_GROUP`:
This global property can be used to group files generated by
:prop_tgt:`AUTOMOC` or ``AUTORCC`` together in an IDE, e.g.  in MSVS.

:prop_gbl:`AUTOGEN_TARGETS_FOLDER`:
This global property can be used to group :prop_tgt:`AUTOMOC`,
:prop_tgt:`AUTOUIC` and ``AUTORCC`` targets together in an IDE,
e.g.  in MSVS.

:variable:`CMAKE_GLOBAL_AUTORCC_TARGET`:
A global ``autorcc`` target that depends on all ``AUTORCC`` targets
in the project will be generated when this variable is ``ON``.

See the :manual:`cmake-qt(7)` manual for more information on using CMake
with Qt.
