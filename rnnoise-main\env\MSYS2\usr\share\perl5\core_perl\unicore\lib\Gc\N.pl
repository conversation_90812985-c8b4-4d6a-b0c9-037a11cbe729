# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V274
48
58
178
180
185
186
188
191
1632
1642
1776
1786
1984
1994
2406
2416
2534
2544
2548
2554
2662
2672
2790
2800
2918
2928
2930
2936
3046
3059
3174
3184
3192
3199
3302
3312
3416
3423
3430
3449
3558
3568
3664
3674
3792
3802
3872
3892
4160
4170
4240
4250
4969
4989
5870
5873
6112
6122
6128
6138
6160
6170
6470
6480
6608
6619
6784
6794
6800
6810
6992
7002
7088
7098
7232
7242
7248
7258
8304
8305
8308
8314
8320
8330
8528
8579
8581
8586
9312
9372
9450
9472
10102
10132
11517
11518
12295
12296
12321
12330
12344
12347
12690
12694
12832
12842
12872
12880
12881
12896
12928
12938
12977
12992
42528
42538
42726
42736
43056
43062
43216
43226
43264
43274
43472
43482
43504
43514
43600
43610
44016
44026
65296
65306
65799
65844
65856
65913
65930
65932
66273
66300
66336
66340
66369
66370
66378
66379
66513
66518
66720
66730
67672
67680
67705
67712
67751
67760
67835
67840
67862
67868
68028
68030
68032
68048
68050
68096
68160
68169
68221
68223
68253
68256
68331
68336
68440
68448
68472
68480
68521
68528
68858
68864
68912
68922
69216
69247
69405
69415
69457
69461
69573
69580
69714
69744
69872
69882
69942
69952
70096
70106
70113
70133
70384
70394
70736
70746
70864
70874
71248
71258
71360
71370
71472
71484
71904
71923
72016
72026
72784
72813
73040
73050
73120
73130
73552
73562
73664
73685
74752
74863
92768
92778
92864
92874
93008
93018
93019
93026
93824
93847
119488
119508
119520
119540
119648
119673
120782
120832
123200
123210
123632
123642
124144
124154
125127
125136
125264
125274
126065
126124
126125
126128
126129
126133
126209
126254
126255
126270
127232
127245
130032
130042
END
