/* -*- mode: C; buffer-read-only: t -*-
 *
 *    embed.h
 *
 *    Copyright (C) 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001,
 *    2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013,
 *    2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022
 *    by <PERSON> and others
 *
 *    You may distribute under the terms of either the GNU General Public
 *    License or the Artistic License, as specified in the README file.
 *
 * !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
 * This file is built by regen/embed.pl from embed.fnc, intrpvar.h,
 * perlvars.h, regen/opcodes, regen/embed.pl, regen/embed_lib.pl and
 * regen/HeaderParser.pm.
 * Any changes made here will be lost!
 *
 * Edit those files and run 'make regen_headers' to effect changes.
 */

/* (Doing namespace management portably in C is really gross.) */

/* By defining PERL_NO_SHORT_NAMES (not done by default) the short forms
 * (like warn instead of Perl_warn) for the API are not defined.
 * Not defining the short forms is a good thing for cleaner embedding.
 * BEWARE that a bunch of macros don't have long names, so either must be
 * added or don't use them if you define this symbol */

#if !defined(MULTIPLICITY)
/* undefined symbols, point them back at the usual ones */
# define Perl_croak_nocontext                   Perl_croak
# define Perl_deb_nocontext                     Perl_deb
# define Perl_die_nocontext                     Perl_die
# define Perl_form_nocontext                    Perl_form
# define Perl_load_module_nocontext             Perl_load_module
# define Perl_mess_nocontext                    Perl_mess
# define Perl_newSVpvf_nocontext                Perl_newSVpvf
# define Perl_sv_catpvf_nocontext               Perl_sv_catpvf
# define Perl_sv_catpvf_mg_nocontext            Perl_sv_catpvf_mg
# define Perl_sv_setpvf_nocontext               Perl_sv_setpvf
# define Perl_sv_setpvf_mg_nocontext            Perl_sv_setpvf_mg
# define Perl_warn_nocontext                    Perl_warn
# define Perl_warner_nocontext                  Perl_warner
#endif /* !defined(MULTIPLICITY) */
#if !defined(PERL_CORE)
/* Compatibility stubs.  Compile extensions with -DPERL_NOCOMPAT to
 * disable them.
 */
# define sv_setptrobj(rv,ptr,name)              sv_setref_iv(rv,name,PTR2IV(ptr))
# define sv_setptrref(rv,ptr)                   sv_setref_iv(rv,NULL,PTR2IV(ptr))
# if !defined(PERL_NOCOMPAT)

/* Compatibility for various misnamed functions.  All functions
   in the API that begin with "perl_" (not "Perl_") take an explicit
   interpreter context pointer.
   The following are not like that, but since they had a "perl_"
   prefix in previous versions, we provide compatibility macros.
 */
#   define perl_atexit(a,b)                     call_atexit(a,b)
#   define perl_call_argv(a,b,c)                call_argv(a,b,c)
#   define perl_call_method(a,b)                call_method(a,b)
#   define perl_call_pv(a,b)                    call_pv(a,b)
#   define perl_call_sv(a,b)                    call_sv(a,b)
#   define perl_eval_pv(a,b)                    eval_pv(a,b)
#   define perl_eval_sv(a,b)                    eval_sv(a,b)
#   define perl_get_av(a,b)                     get_av(a,b)
#   define perl_get_cv(a,b)                     get_cv(a,b)
#   define perl_get_hv(a,b)                     get_hv(a,b)
#   define perl_get_sv(a,b)                     get_sv(a,b)
#   define perl_init_i18nl10n(a)                init_i18nl10n(a)
#   define perl_require_pv(a)                   require_pv(a)

/* varargs functions can't be handled with CPP macros. :-(
   This provides a set of compatibility functions that don't take
   an extra argument but grab the context pointer using the macro
   dTHX.
 */

#   if defined(MULTIPLICITY) && !defined(PERL_NO_SHORT_NAMES)
#     define croak                              Perl_croak_nocontext
#     define deb                                Perl_deb_nocontext
#     define die                                Perl_die_nocontext
#     define form                               Perl_form_nocontext
#     define load_module                        Perl_load_module_nocontext
#     define mess                               Perl_mess_nocontext
#     define newSVpvf                           Perl_newSVpvf_nocontext
#     define sv_catpvf                          Perl_sv_catpvf_nocontext
#     define sv_catpvf_mg                       Perl_sv_catpvf_mg_nocontext
#     define sv_setpvf                          Perl_sv_setpvf_nocontext
#     define sv_setpvf_mg                       Perl_sv_setpvf_mg_nocontext
#     define warn                               Perl_warn_nocontext
#     define warner                             Perl_warner_nocontext
#   endif /* defined(MULTIPLICITY) && !defined(PERL_NO_SHORT_NAMES) */
# endif /* !defined(PERL_NOCOMPAT) */
#endif /* !defined(PERL_CORE) */
#if !defined(PERL_NO_SHORT_NAMES)

/* Hide global symbols */

# define Gv_AMupdate(a,b)                       Perl_Gv_AMupdate(aTHX_ a,b)
# define SvAMAGIC_off                           Perl_SvAMAGIC_off
# define SvAMAGIC_on                            Perl_SvAMAGIC_on
# define SvGETMAGIC(a)                          Perl_SvGETMAGIC(aTHX_ a)
# define SvIV(a)                                Perl_SvIV(aTHX_ a)
# define SvIV_nomg(a)                           Perl_SvIV_nomg(aTHX_ a)
# define SvNV(a)                                Perl_SvNV(aTHX_ a)
# define SvNV_nomg(a)                           Perl_SvNV_nomg(aTHX_ a)
# define SvPVXtrue(a)                           Perl_SvPVXtrue(aTHX_ a)
# define SvREFCNT_dec_ret_NULL(a)               Perl_SvREFCNT_dec_ret_NULL(aTHX_ a)
# define SvTRUE(a)                              Perl_SvTRUE(aTHX_ a)
# define SvTRUE_NN(a)                           Perl_SvTRUE_NN(aTHX_ a)
# define SvTRUE_common(a,b)                     Perl_SvTRUE_common(aTHX_ a,b)
# define SvTRUE_nomg(a)                         Perl_SvTRUE_nomg(aTHX_ a)
# define SvUV(a)                                Perl_SvUV(aTHX_ a)
# define SvUV_nomg(a)                           Perl_SvUV_nomg(aTHX_ a)
# define _force_out_malformed_utf8_message(a,b,c,d) Perl__force_out_malformed_utf8_message(aTHX_ a,b,c,d)
# define _is_in_locale_category(a,b)            Perl__is_in_locale_category(aTHX_ a,b)
# define _is_uni_FOO(a,b)                       Perl__is_uni_FOO(aTHX_ a,b)
# define _is_uni_perl_idcont(a)                 Perl__is_uni_perl_idcont(aTHX_ a)
# define _is_uni_perl_idstart(a)                Perl__is_uni_perl_idstart(aTHX_ a)
# define _is_utf8_FOO(a,b,c)                    Perl__is_utf8_FOO(aTHX_ a,b,c)
# define _is_utf8_perl_idcont(a,b)              Perl__is_utf8_perl_idcont(aTHX_ a,b)
# define _is_utf8_perl_idstart(a,b)             Perl__is_utf8_perl_idstart(aTHX_ a,b)
# define _to_uni_fold_flags(a,b,c,d)            Perl__to_uni_fold_flags(aTHX_ a,b,c,d)
# define _to_utf8_fold_flags(a,b,c,d,e)         Perl__to_utf8_fold_flags(aTHX_ a,b,c,d,e)
# define _to_utf8_lower_flags(a,b,c,d,e)        Perl__to_utf8_lower_flags(aTHX_ a,b,c,d,e)
# define _to_utf8_title_flags(a,b,c,d,e)        Perl__to_utf8_title_flags(aTHX_ a,b,c,d,e)
# define _to_utf8_upper_flags(a,b,c,d,e)        Perl__to_utf8_upper_flags(aTHX_ a,b,c,d,e)
# define _utf8n_to_uvchr_msgs_helper            Perl__utf8n_to_uvchr_msgs_helper
# define amagic_call(a,b,c,d)                   Perl_amagic_call(aTHX_ a,b,c,d)
# define amagic_deref_call(a,b)                 Perl_amagic_deref_call(aTHX_ a,b)
# define apply_attrs_string(a,b,c,d)            Perl_apply_attrs_string(aTHX_ a,b,c,d)
# define apply_builtin_cv_attributes(a,b)       Perl_apply_builtin_cv_attributes(aTHX_ a,b)
# define atfork_lock                            Perl_atfork_lock
# define atfork_unlock                          Perl_atfork_unlock
# define av_clear(a)                            Perl_av_clear(aTHX_ a)
# define av_count(a)                            Perl_av_count(aTHX_ a)
# define av_delete(a,b,c)                       Perl_av_delete(aTHX_ a,b,c)
# define av_dump(a)                             Perl_av_dump(aTHX_ a)
# define av_exists(a,b)                         Perl_av_exists(aTHX_ a,b)
# define av_extend(a,b)                         Perl_av_extend(aTHX_ a,b)
# define av_fetch(a,b,c)                        Perl_av_fetch(aTHX_ a,b,c)
# define av_fetch_simple(a,b,c)                 Perl_av_fetch_simple(aTHX_ a,b,c)
# define av_fill(a,b)                           Perl_av_fill(aTHX_ a,b)
# define av_len(a)                              Perl_av_len(aTHX_ a)
# define av_make(a,b)                           Perl_av_make(aTHX_ a,b)
# define av_new_alloc(a,b)                      Perl_av_new_alloc(aTHX_ a,b)
# define av_pop(a)                              Perl_av_pop(aTHX_ a)
# define av_push(a,b)                           Perl_av_push(aTHX_ a,b)
# define av_push_simple(a,b)                    Perl_av_push_simple(aTHX_ a,b)
# define av_shift(a)                            Perl_av_shift(aTHX_ a)
# define av_store(a,b,c)                        Perl_av_store(aTHX_ a,b,c)
# define av_store_simple(a,b,c)                 Perl_av_store_simple(aTHX_ a,b,c)
# define av_undef(a)                            Perl_av_undef(aTHX_ a)
# define av_unshift(a,b)                        Perl_av_unshift(aTHX_ a,b)
# define block_end(a,b)                         Perl_block_end(aTHX_ a,b)
# define block_gimme()                          Perl_block_gimme(aTHX)
# define block_start(a)                         Perl_block_start(aTHX_ a)
# define bytes_cmp_utf8(a,b,c,d)                Perl_bytes_cmp_utf8(aTHX_ a,b,c,d)
# define bytes_from_utf8_loc                    Perl_bytes_from_utf8_loc
# define bytes_to_utf8(a,b)                     Perl_bytes_to_utf8(aTHX_ a,b)
# define call_argv(a,b,c)                       Perl_call_argv(aTHX_ a,b,c)
# define call_atexit(a,b)                       Perl_call_atexit(aTHX_ a,b)
# define call_list(a,b)                         Perl_call_list(aTHX_ a,b)
# define call_method(a,b)                       Perl_call_method(aTHX_ a,b)
# define call_pv(a,b)                           Perl_call_pv(aTHX_ a,b)
# define call_sv(a,b)                           Perl_call_sv(aTHX_ a,b)
# define caller_cx(a,b)                         Perl_caller_cx(aTHX_ a,b)
# define cast_i32                               Perl_cast_i32
# define cast_iv                                Perl_cast_iv
# define cast_ulong                             Perl_cast_ulong
# define cast_uv                                Perl_cast_uv
# define ck_entersub_args_list(a)               Perl_ck_entersub_args_list(aTHX_ a)
# define ck_entersub_args_proto(a,b,c)          Perl_ck_entersub_args_proto(aTHX_ a,b,c)
# define ck_entersub_args_proto_or_list(a,b,c)  Perl_ck_entersub_args_proto_or_list(aTHX_ a,b,c)
# define clear_defarray(a,b)                    Perl_clear_defarray(aTHX_ a,b)
# define cop_fetch_label(a,b,c)                 Perl_cop_fetch_label(aTHX_ a,b,c)
# define cop_store_label(a,b,c,d)               Perl_cop_store_label(aTHX_ a,b,c,d)
# define croak_memory_wrap                      Perl_croak_memory_wrap
# define croak_no_modify                        Perl_croak_no_modify
# define croak_sv(a)                            Perl_croak_sv(aTHX_ a)
# define croak_xs_usage                         Perl_croak_xs_usage
# define csighandler1                           Perl_csighandler1
# define csighandler3                           Perl_csighandler3
# define cv_clone(a)                            Perl_cv_clone(aTHX_ a)
# define cv_const_sv                            Perl_cv_const_sv
# define cv_get_call_checker(a,b,c)             Perl_cv_get_call_checker(aTHX_ a,b,c)
# define cv_get_call_checker_flags(a,b,c,d,e)   Perl_cv_get_call_checker_flags(aTHX_ a,b,c,d,e)
# define cv_name(a,b,c)                         Perl_cv_name(aTHX_ a,b,c)
# define cv_set_call_checker(a,b,c)             Perl_cv_set_call_checker(aTHX_ a,b,c)
# define cv_set_call_checker_flags(a,b,c,d)     Perl_cv_set_call_checker_flags(aTHX_ a,b,c,d)
# define cv_undef(a)                            Perl_cv_undef(aTHX_ a)
# define cx_dump(a)                             Perl_cx_dump(aTHX_ a)
# define cxinc()                                Perl_cxinc(aTHX)
# define debop(a)                               Perl_debop(aTHX_ a)
# define debprofdump()                          Perl_debprofdump(aTHX)
# define debstack()                             Perl_debstack(aTHX)
# define debstackptrs()                         Perl_debstackptrs(aTHX)
# define delimcpy                               Perl_delimcpy
# define despatch_signals()                     Perl_despatch_signals(aTHX)
# define die_sv(a)                              Perl_die_sv(aTHX_ a)
# define do_close(a,b)                          Perl_do_close(aTHX_ a,b)
# define do_gv_dump(a,b,c,d)                    Perl_do_gv_dump(aTHX_ a,b,c,d)
# define do_gvgv_dump(a,b,c,d)                  Perl_do_gvgv_dump(aTHX_ a,b,c,d)
# define do_hv_dump(a,b,c,d)                    Perl_do_hv_dump(aTHX_ a,b,c,d)
# define do_join(a,b,c,d)                       Perl_do_join(aTHX_ a,b,c,d)
# define do_magic_dump(a,b,c,d,e,f,g)           Perl_do_magic_dump(aTHX_ a,b,c,d,e,f,g)
# define do_op_dump(a,b,c)                      Perl_do_op_dump(aTHX_ a,b,c)
# define do_openn(a,b,c,d,e,f,g,h,i)            Perl_do_openn(aTHX_ a,b,c,d,e,f,g,h,i)
# define do_pmop_dump(a,b,c)                    Perl_do_pmop_dump(aTHX_ a,b,c)
# define do_sprintf(a,b,c)                      Perl_do_sprintf(aTHX_ a,b,c)
# define do_sv_dump(a,b,c,d,e,f,g)              Perl_do_sv_dump(aTHX_ a,b,c,d,e,f,g)
# define doing_taint                            Perl_doing_taint
# define doref(a,b,c)                           Perl_doref(aTHX_ a,b,c)
# define dounwind(a)                            Perl_dounwind(aTHX_ a)
# define dowantarray()                          Perl_dowantarray(aTHX)
# define dump_all()                             Perl_dump_all(aTHX)
# define dump_eval()                            Perl_dump_eval(aTHX)
# define dump_form(a)                           Perl_dump_form(aTHX_ a)
# define dump_packsubs(a)                       Perl_dump_packsubs(aTHX_ a)
# define dump_sub(a)                            Perl_dump_sub(aTHX_ a)
# define dump_vindent(a,b,c,d)                  Perl_dump_vindent(aTHX_ a,b,c,d)
# define eval_pv(a,b)                           Perl_eval_pv(aTHX_ a,b)
# define eval_sv(a,b)                           Perl_eval_sv(aTHX_ a,b)
# define fbm_compile(a,b)                       Perl_fbm_compile(aTHX_ a,b)
# define fbm_instr(a,b,c,d)                     Perl_fbm_instr(aTHX_ a,b,c,d)
# define filter_add(a,b)                        Perl_filter_add(aTHX_ a,b)
# define filter_del(a)                          Perl_filter_del(aTHX_ a)
# define filter_read(a,b,c)                     Perl_filter_read(aTHX_ a,b,c)
# define find_runcv(a)                          Perl_find_runcv(aTHX_ a)
# define find_rundefsv()                        Perl_find_rundefsv(aTHX)
# define foldEQ(a,b,c)                          Perl_foldEQ(aTHX_ a,b,c)
# define foldEQ_latin1(a,b,c)                   Perl_foldEQ_latin1(aTHX_ a,b,c)
# define foldEQ_locale(a,b,c)                   Perl_foldEQ_locale(aTHX_ a,b,c)
# define foldEQ_utf8_flags(a,b,c,d,e,f,g,h,i)   Perl_foldEQ_utf8_flags(aTHX_ a,b,c,d,e,f,g,h,i)
# define forbid_outofblock_ops(a,b)             Perl_forbid_outofblock_ops(aTHX_ a,b)
# define free_tmps()                            Perl_free_tmps(aTHX)
# define get_av(a,b)                            Perl_get_av(aTHX_ a,b)
# define get_cv(a,b)                            Perl_get_cv(aTHX_ a,b)
# define get_cvn_flags(a,b,c)                   Perl_get_cvn_flags(aTHX_ a,b,c)
# define get_hv(a,b)                            Perl_get_hv(aTHX_ a,b)
# define get_op_descs()                         Perl_get_op_descs(aTHX)
# define get_op_names()                         Perl_get_op_names(aTHX)
# define get_ppaddr()                           Perl_get_ppaddr(aTHX)
# define get_sv(a,b)                            Perl_get_sv(aTHX_ a,b)
# define get_vtbl(a)                            Perl_get_vtbl(aTHX_ a)
# define getcwd_sv(a)                           Perl_getcwd_sv(aTHX_ a)
# define gp_free(a)                             Perl_gp_free(aTHX_ a)
# define gp_ref(a)                              Perl_gp_ref(aTHX_ a)
# define grok_atoUV                             Perl_grok_atoUV
# define grok_bin_oct_hex(a,b,c,d,e,f,g)        Perl_grok_bin_oct_hex(aTHX_ a,b,c,d,e,f,g)
# define grok_infnan(a,b)                       Perl_grok_infnan(aTHX_ a,b)
# define grok_number(a,b,c)                     Perl_grok_number(aTHX_ a,b,c)
# define grok_number_flags(a,b,c,d)             Perl_grok_number_flags(aTHX_ a,b,c,d)
# define grok_numeric_radix(a,b)                Perl_grok_numeric_radix(aTHX_ a,b)
# define gv_add_by_type(a,b)                    Perl_gv_add_by_type(aTHX_ a,b)
# define gv_autoload_pv(a,b,c)                  Perl_gv_autoload_pv(aTHX_ a,b,c)
# define gv_autoload_pvn(a,b,c,d)               Perl_gv_autoload_pvn(aTHX_ a,b,c,d)
# define gv_autoload_sv(a,b,c)                  Perl_gv_autoload_sv(aTHX_ a,b,c)
# define gv_check(a)                            Perl_gv_check(aTHX_ a)
# define gv_const_sv(a)                         Perl_gv_const_sv(aTHX_ a)
# define gv_dump(a)                             Perl_gv_dump(aTHX_ a)
# define gv_efullname4(a,b,c,d)                 Perl_gv_efullname4(aTHX_ a,b,c,d)
# define gv_fetchfile(a)                        Perl_gv_fetchfile(aTHX_ a)
# define gv_fetchfile_flags(a,b,c)              Perl_gv_fetchfile_flags(aTHX_ a,b,c)
# define gv_fetchmeth_pv(a,b,c,d)               Perl_gv_fetchmeth_pv(aTHX_ a,b,c,d)
# define gv_fetchmeth_pv_autoload(a,b,c,d)      Perl_gv_fetchmeth_pv_autoload(aTHX_ a,b,c,d)
# define gv_fetchmeth_pvn(a,b,c,d,e)            Perl_gv_fetchmeth_pvn(aTHX_ a,b,c,d,e)
# define gv_fetchmeth_pvn_autoload(a,b,c,d,e)   Perl_gv_fetchmeth_pvn_autoload(aTHX_ a,b,c,d,e)
# define gv_fetchmeth_sv(a,b,c,d)               Perl_gv_fetchmeth_sv(aTHX_ a,b,c,d)
# define gv_fetchmeth_sv_autoload(a,b,c,d)      Perl_gv_fetchmeth_sv_autoload(aTHX_ a,b,c,d)
# define gv_fetchmethod_autoload(a,b,c)         Perl_gv_fetchmethod_autoload(aTHX_ a,b,c)
# define gv_fetchmethod_pv_flags(a,b,c)         Perl_gv_fetchmethod_pv_flags(aTHX_ a,b,c)
# define gv_fetchmethod_pvn_flags(a,b,c,d)      Perl_gv_fetchmethod_pvn_flags(aTHX_ a,b,c,d)
# define gv_fetchmethod_sv_flags(a,b,c)         Perl_gv_fetchmethod_sv_flags(aTHX_ a,b,c)
# define gv_fetchpv(a,b,c)                      Perl_gv_fetchpv(aTHX_ a,b,c)
# define gv_fetchpvn_flags(a,b,c,d)             Perl_gv_fetchpvn_flags(aTHX_ a,b,c,d)
# define gv_fetchsv(a,b,c)                      Perl_gv_fetchsv(aTHX_ a,b,c)
# define gv_fullname4(a,b,c,d)                  Perl_gv_fullname4(aTHX_ a,b,c,d)
# define gv_handler(a,b)                        Perl_gv_handler(aTHX_ a,b)
# define gv_init_pv(a,b,c,d)                    Perl_gv_init_pv(aTHX_ a,b,c,d)
# define gv_init_pvn(a,b,c,d,e)                 Perl_gv_init_pvn(aTHX_ a,b,c,d,e)
# define gv_init_sv(a,b,c,d)                    Perl_gv_init_sv(aTHX_ a,b,c,d)
# define gv_name_set(a,b,c,d)                   Perl_gv_name_set(aTHX_ a,b,c,d)
# define gv_stashpv(a,b)                        Perl_gv_stashpv(aTHX_ a,b)
# define gv_stashpvn(a,b,c)                     Perl_gv_stashpvn(aTHX_ a,b,c)
# define gv_stashsv(a,b)                        Perl_gv_stashsv(aTHX_ a,b)
# define hv_bucket_ratio(a)                     Perl_hv_bucket_ratio(aTHX_ a)
# define hv_clear(a)                            Perl_hv_clear(aTHX_ a)
# define hv_clear_placeholders(a)               Perl_hv_clear_placeholders(aTHX_ a)
# define hv_common(a,b,c,d,e,f,g,h)             Perl_hv_common(aTHX_ a,b,c,d,e,f,g,h)
# define hv_common_key_len(a,b,c,d,e,f)         Perl_hv_common_key_len(aTHX_ a,b,c,d,e,f)
# define hv_copy_hints_hv(a)                    Perl_hv_copy_hints_hv(aTHX_ a)
# define hv_delayfree_ent(a,b)                  Perl_hv_delayfree_ent(aTHX_ a,b)
# define hv_dump(a)                             Perl_hv_dump(aTHX_ a)
# define hv_free_ent(a,b)                       Perl_hv_free_ent(aTHX_ a,b)
# define hv_iterinit(a)                         Perl_hv_iterinit(aTHX_ a)
# define hv_iterkey(a,b)                        Perl_hv_iterkey(aTHX_ a,b)
# define hv_iterkeysv(a)                        Perl_hv_iterkeysv(aTHX_ a)
# define hv_iternext_flags(a,b)                 Perl_hv_iternext_flags(aTHX_ a,b)
# define hv_iternextsv(a,b,c)                   Perl_hv_iternextsv(aTHX_ a,b,c)
# define hv_iterval(a,b)                        Perl_hv_iterval(aTHX_ a,b)
# define hv_ksplit(a,b)                         Perl_hv_ksplit(aTHX_ a,b)
# define hv_name_set(a,b,c,d)                   Perl_hv_name_set(aTHX_ a,b,c,d)
# define hv_rand_set(a,b)                       Perl_hv_rand_set(aTHX_ a,b)
# define hv_scalar(a)                           Perl_hv_scalar(aTHX_ a)
# define init_i18nl10n(a)                       Perl_init_i18nl10n(aTHX_ a)
# define init_stacks()                          Perl_init_stacks(aTHX)
# define init_tm(a)                             Perl_init_tm(aTHX_ a)
# define intro_my()                             Perl_intro_my(aTHX)
# define isC9_STRICT_UTF8_CHAR                  Perl_isC9_STRICT_UTF8_CHAR
# define isSTRICT_UTF8_CHAR                     Perl_isSTRICT_UTF8_CHAR
# define isUTF8_CHAR                            Perl_isUTF8_CHAR
# define isUTF8_CHAR_flags                      Perl_isUTF8_CHAR_flags
# define is_c9strict_utf8_string_loclen         Perl_is_c9strict_utf8_string_loclen
# define is_lvalue_sub()                        Perl_is_lvalue_sub(aTHX)
# define is_safe_syscall(a,b,c,d)               Perl_is_safe_syscall(aTHX_ a,b,c,d)
# define is_strict_utf8_string_loclen           Perl_is_strict_utf8_string_loclen
# define is_utf8_FF_helper_                     Perl_is_utf8_FF_helper_
# define is_utf8_char_helper_                   Perl_is_utf8_char_helper_
# define is_utf8_fixed_width_buf_loclen_flags   Perl_is_utf8_fixed_width_buf_loclen_flags
# define is_utf8_invariant_string_loc           Perl_is_utf8_invariant_string_loc
# define is_utf8_string_flags                   Perl_is_utf8_string_flags
# define is_utf8_string_loclen                  Perl_is_utf8_string_loclen
# define is_utf8_string_loclen_flags            Perl_is_utf8_string_loclen_flags
# define is_utf8_valid_partial_char_flags       Perl_is_utf8_valid_partial_char_flags
# define isinfnan                               Perl_isinfnan
# define leave_adjust_stacks(a,b,c,d)           Perl_leave_adjust_stacks(aTHX_ a,b,c,d)
# define leave_scope(a)                         Perl_leave_scope(aTHX_ a)
# define lex_bufutf8()                          Perl_lex_bufutf8(aTHX)
# define lex_discard_to(a)                      Perl_lex_discard_to(aTHX_ a)
# define lex_grow_linestr(a)                    Perl_lex_grow_linestr(aTHX_ a)
# define lex_next_chunk(a)                      Perl_lex_next_chunk(aTHX_ a)
# define lex_peek_unichar(a)                    Perl_lex_peek_unichar(aTHX_ a)
# define lex_read_space(a)                      Perl_lex_read_space(aTHX_ a)
# define lex_read_to(a)                         Perl_lex_read_to(aTHX_ a)
# define lex_read_unichar(a)                    Perl_lex_read_unichar(aTHX_ a)
# define lex_start(a,b,c)                       Perl_lex_start(aTHX_ a,b,c)
# define lex_stuff_pv(a,b)                      Perl_lex_stuff_pv(aTHX_ a,b)
# define lex_stuff_pvn(a,b,c)                   Perl_lex_stuff_pvn(aTHX_ a,b,c)
# define lex_stuff_sv(a,b)                      Perl_lex_stuff_sv(aTHX_ a,b)
# define lex_unstuff(a)                         Perl_lex_unstuff(aTHX_ a)
# define looks_like_number(a)                   Perl_looks_like_number(aTHX_ a)
# define lsbit_pos32                            Perl_lsbit_pos32
# define magic_dump(a)                          Perl_magic_dump(aTHX_ a)
# define markstack_grow()                       Perl_markstack_grow(aTHX)
# define mess_sv(a,b)                           Perl_mess_sv(aTHX_ a,b)
# define mg_clear(a)                            Perl_mg_clear(aTHX_ a)
# define mg_copy(a,b,c,d)                       Perl_mg_copy(aTHX_ a,b,c,d)
# define mg_find                                Perl_mg_find
# define mg_findext                             Perl_mg_findext
# define mg_free(a)                             Perl_mg_free(aTHX_ a)
# define mg_free_type(a,b)                      Perl_mg_free_type(aTHX_ a,b)
# define mg_freeext(a,b,c)                      Perl_mg_freeext(aTHX_ a,b,c)
# define mg_get(a)                              Perl_mg_get(aTHX_ a)
# define mg_magical                             Perl_mg_magical
# define mg_set(a)                              Perl_mg_set(aTHX_ a)
# define mg_size(a)                             Perl_mg_size(aTHX_ a)
# define mini_mktime                            Perl_mini_mktime
# define moreswitches(a)                        Perl_moreswitches(aTHX_ a)
# define mortal_destructor_sv(a,b)              Perl_mortal_destructor_sv(aTHX_ a,b)
# define mortal_getenv                          Perl_mortal_getenv
# define mortal_svfunc_x(a,b)                   Perl_mortal_svfunc_x(aTHX_ a,b)
# define mro_get_linear_isa(a)                  Perl_mro_get_linear_isa(aTHX_ a)
# define mro_method_changed_in(a)               Perl_mro_method_changed_in(aTHX_ a)
# define msbit_pos32                            Perl_msbit_pos32
# define my_atof(a)                             Perl_my_atof(aTHX_ a)
# define my_atof3(a,b,c)                        Perl_my_atof3(aTHX_ a,b,c)
# define my_dirfd                               Perl_my_dirfd
# define my_exit(a)                             Perl_my_exit(aTHX_ a)
# define my_failure_exit()                      Perl_my_failure_exit(aTHX)
# define my_fflush_all()                        Perl_my_fflush_all(aTHX)
# define my_fork                                Perl_my_fork
# define my_popen_list(a,b,c)                   Perl_my_popen_list(aTHX_ a,b,c)
# define my_setenv(a,b)                         Perl_my_setenv(aTHX_ a,b)
# define my_socketpair                          Perl_my_socketpair
# define my_strftime(a,b,c,d,e,f,g,h,i,j)       Perl_my_strftime(aTHX_ a,b,c,d,e,f,g,h,i,j)
# define my_strtod                              Perl_my_strtod
# define newANONATTRSUB(a,b,c,d)                Perl_newANONATTRSUB(aTHX_ a,b,c,d)
# define newANONHASH(a)                         Perl_newANONHASH(aTHX_ a)
# define newANONLIST(a)                         Perl_newANONLIST(aTHX_ a)
# define newANONSUB(a,b,c)                      Perl_newANONSUB(aTHX_ a,b,c)
# define newARGDEFELEMOP(a,b,c)                 Perl_newARGDEFELEMOP(aTHX_ a,b,c)
# define newASSIGNOP(a,b,c,d)                   Perl_newASSIGNOP(aTHX_ a,b,c,d)
# define newAVREF(a)                            Perl_newAVREF(aTHX_ a)
# define newAVav(a)                             Perl_newAVav(aTHX_ a)
# define newAVhv(a)                             Perl_newAVhv(aTHX_ a)
# define newBINOP(a,b,c,d)                      Perl_newBINOP(aTHX_ a,b,c,d)
# define newCONDOP(a,b,c,d)                     Perl_newCONDOP(aTHX_ a,b,c,d)
# define newCONSTSUB(a,b,c)                     Perl_newCONSTSUB(aTHX_ a,b,c)
# define newCONSTSUB_flags(a,b,c,d,e)           Perl_newCONSTSUB_flags(aTHX_ a,b,c,d,e)
# define newCVREF(a,b)                          Perl_newCVREF(aTHX_ a,b)
# define newDEFEROP(a,b)                        Perl_newDEFEROP(aTHX_ a,b)
# define newDEFSVOP()                           Perl_newDEFSVOP(aTHX)
# define newFORM(a,b,c)                         Perl_newFORM(aTHX_ a,b,c)
# define newFOROP(a,b,c,d,e)                    Perl_newFOROP(aTHX_ a,b,c,d,e)
# define newGIVENOP(a,b,c)                      Perl_newGIVENOP(aTHX_ a,b,c)
# define newGVOP(a,b,c)                         Perl_newGVOP(aTHX_ a,b,c)
# define newGVREF(a,b)                          Perl_newGVREF(aTHX_ a,b)
# define newGVgen_flags(a,b)                    Perl_newGVgen_flags(aTHX_ a,b)
# define newHVREF(a)                            Perl_newHVREF(aTHX_ a)
# define newHVhv(a)                             Perl_newHVhv(aTHX_ a)
# define newLISTOP(a,b,c,d)                     Perl_newLISTOP(aTHX_ a,b,c,d)
# define newLOGOP(a,b,c,d)                      Perl_newLOGOP(aTHX_ a,b,c,d)
# define newLOOPEX(a,b)                         Perl_newLOOPEX(aTHX_ a,b)
# define newLOOPOP(a,b,c,d)                     Perl_newLOOPOP(aTHX_ a,b,c,d)
# define newMETHOP(a,b,c)                       Perl_newMETHOP(aTHX_ a,b,c)
# define newMETHOP_named(a,b,c)                 Perl_newMETHOP_named(aTHX_ a,b,c)
# define newMYSUB(a,b,c,d,e)                    Perl_newMYSUB(aTHX_ a,b,c,d,e)
# define newNULLLIST()                          Perl_newNULLLIST(aTHX)
# define newOP(a,b)                             Perl_newOP(aTHX_ a,b)
# define newPADNAMELIST                         Perl_newPADNAMELIST
# define newPADNAMEouter                        Perl_newPADNAMEouter
# define newPADNAMEpvn                          Perl_newPADNAMEpvn
# define newPADxVOP(a,b,c)                      Perl_newPADxVOP(aTHX_ a,b,c)
# define newPMOP(a,b)                           Perl_newPMOP(aTHX_ a,b)
# define newPROG(a)                             Perl_newPROG(aTHX_ a)
# define newPVOP(a,b,c)                         Perl_newPVOP(aTHX_ a,b,c)
# define newRANGE(a,b,c)                        Perl_newRANGE(aTHX_ a,b,c)
# define newRV(a)                               Perl_newRV(aTHX_ a)
# define newRV_noinc(a)                         Perl_newRV_noinc(aTHX_ a)
# define newSLICEOP(a,b,c)                      Perl_newSLICEOP(aTHX_ a,b,c)
# define newSTATEOP(a,b,c)                      Perl_newSTATEOP(aTHX_ a,b,c)
# define newSV(a)                               Perl_newSV(aTHX_ a)
# define newSVOP(a,b,c)                         Perl_newSVOP(aTHX_ a,b,c)
# define newSVREF(a)                            Perl_newSVREF(aTHX_ a)
# define newSV_false()                          Perl_newSV_false(aTHX)
# define newSV_true()                           Perl_newSV_true(aTHX)
# define newSV_type(a)                          Perl_newSV_type(aTHX_ a)
# define newSV_type_mortal(a)                   Perl_newSV_type_mortal(aTHX_ a)
# define newSVbool(a)                           Perl_newSVbool(aTHX_ a)
# define newSVhek(a)                            Perl_newSVhek(aTHX_ a)
# define newSVhek_mortal(a)                     Perl_newSVhek_mortal(aTHX_ a)
# define newSViv(a)                             Perl_newSViv(aTHX_ a)
# define newSVnv(a)                             Perl_newSVnv(aTHX_ a)
# define newSVpv(a,b)                           Perl_newSVpv(aTHX_ a,b)
# define newSVpv_share(a,b)                     Perl_newSVpv_share(aTHX_ a,b)
# define newSVpvn(a,b)                          Perl_newSVpvn(aTHX_ a,b)
# define newSVpvn_flags(a,b,c)                  Perl_newSVpvn_flags(aTHX_ a,b,c)
# define newSVpvn_share(a,b,c)                  Perl_newSVpvn_share(aTHX_ a,b,c)
# define newSVrv(a,b)                           Perl_newSVrv(aTHX_ a,b)
# define newSVsv_flags(a,b)                     Perl_newSVsv_flags(aTHX_ a,b)
# define newSVuv(a)                             Perl_newSVuv(aTHX_ a)
# define newTRYCATCHOP(a,b,c,d)                 Perl_newTRYCATCHOP(aTHX_ a,b,c,d)
# define newUNOP(a,b,c)                         Perl_newUNOP(aTHX_ a,b,c)
# define newUNOP_AUX(a,b,c,d)                   Perl_newUNOP_AUX(aTHX_ a,b,c,d)
# define newWHENOP(a,b)                         Perl_newWHENOP(aTHX_ a,b)
# define newWHILEOP(a,b,c,d,e,f,g)              Perl_newWHILEOP(aTHX_ a,b,c,d,e,f,g)
# define newXS(a,b,c)                           Perl_newXS(aTHX_ a,b,c)
# define newXS_flags(a,b,c,d,e)                 Perl_newXS_flags(aTHX_ a,b,c,d,e)
# define new_stackinfo(a,b)                     Perl_new_stackinfo(aTHX_ a,b)
# define new_version(a)                         Perl_new_version(aTHX_ a)
# define nothreadhook()                         Perl_nothreadhook(aTHX)
# define op_append_elem(a,b,c)                  Perl_op_append_elem(aTHX_ a,b,c)
# define op_append_list(a,b,c)                  Perl_op_append_list(aTHX_ a,b,c)
# define op_class(a)                            Perl_op_class(aTHX_ a)
# define op_contextualize(a,b)                  Perl_op_contextualize(aTHX_ a,b)
# define op_convert_list(a,b,c)                 Perl_op_convert_list(aTHX_ a,b,c)
# define op_dump(a)                             Perl_op_dump(aTHX_ a)
# define op_force_list(a)                       Perl_op_force_list(aTHX_ a)
# define op_free(a)                             Perl_op_free(aTHX_ a)
# define op_linklist(a)                         Perl_op_linklist(aTHX_ a)
# define op_null(a)                             Perl_op_null(aTHX_ a)
# define op_parent                              Perl_op_parent
# define op_prepend_elem(a,b,c)                 Perl_op_prepend_elem(aTHX_ a,b,c)
# define op_refcnt_lock()                       Perl_op_refcnt_lock(aTHX)
# define op_refcnt_unlock()                     Perl_op_refcnt_unlock(aTHX)
# define op_scope(a)                            Perl_op_scope(aTHX_ a)
# define op_sibling_splice                      Perl_op_sibling_splice
# define op_wrap_finally(a,b)                   Perl_op_wrap_finally(aTHX_ a,b)
# define packlist(a,b,c,d,e)                    Perl_packlist(aTHX_ a,b,c,d,e)
# define pad_add_anon(a,b)                      Perl_pad_add_anon(aTHX_ a,b)
# define pad_add_name_pv(a,b,c,d)               Perl_pad_add_name_pv(aTHX_ a,b,c,d)
# define pad_add_name_pvn(a,b,c,d,e)            Perl_pad_add_name_pvn(aTHX_ a,b,c,d,e)
# define pad_add_name_sv(a,b,c,d)               Perl_pad_add_name_sv(aTHX_ a,b,c,d)
# define pad_alloc(a,b)                         Perl_pad_alloc(aTHX_ a,b)
# define pad_findmy_pv(a,b)                     Perl_pad_findmy_pv(aTHX_ a,b)
# define pad_findmy_pvn(a,b,c)                  Perl_pad_findmy_pvn(aTHX_ a,b,c)
# define pad_findmy_sv(a,b)                     Perl_pad_findmy_sv(aTHX_ a,b)
# define pad_new(a)                             Perl_pad_new(aTHX_ a)
# define pad_tidy(a)                            Perl_pad_tidy(aTHX_ a)
# define padnamelist_fetch                      Perl_padnamelist_fetch
# define padnamelist_store(a,b,c)               Perl_padnamelist_store(aTHX_ a,b,c)
# define parse_arithexpr(a)                     Perl_parse_arithexpr(aTHX_ a)
# define parse_barestmt(a)                      Perl_parse_barestmt(aTHX_ a)
# define parse_block(a)                         Perl_parse_block(aTHX_ a)
# define parse_fullexpr(a)                      Perl_parse_fullexpr(aTHX_ a)
# define parse_fullstmt(a)                      Perl_parse_fullstmt(aTHX_ a)
# define parse_label(a)                         Perl_parse_label(aTHX_ a)
# define parse_listexpr(a)                      Perl_parse_listexpr(aTHX_ a)
# define parse_stmtseq(a)                       Perl_parse_stmtseq(aTHX_ a)
# define parse_subsignature(a)                  Perl_parse_subsignature(aTHX_ a)
# define parse_termexpr(a)                      Perl_parse_termexpr(aTHX_ a)
# define perly_sighandler                       Perl_perly_sighandler
# define pmop_dump(a)                           Perl_pmop_dump(aTHX_ a)
# define pop_scope()                            Perl_pop_scope(aTHX)
# define pregcomp(a,b)                          Perl_pregcomp(aTHX_ a,b)
# define pregexec(a,b,c,d,e,f,g)                Perl_pregexec(aTHX_ a,b,c,d,e,f,g)
# define pregfree(a)                            Perl_pregfree(aTHX_ a)
# define pregfree2(a)                           Perl_pregfree2(aTHX_ a)
# define prescan_version(a,b,c,d,e,f,g)         Perl_prescan_version(aTHX_ a,b,c,d,e,f,g)
# define ptr_table_fetch(a,b)                   Perl_ptr_table_fetch(aTHX_ a,b)
# define ptr_table_free(a)                      Perl_ptr_table_free(aTHX_ a)
# define ptr_table_new()                        Perl_ptr_table_new(aTHX)
# define ptr_table_split(a)                     Perl_ptr_table_split(aTHX_ a)
# define ptr_table_store(a,b,c)                 Perl_ptr_table_store(aTHX_ a,b,c)
# define push_scope()                           Perl_push_scope(aTHX)
# define pv_display(a,b,c,d,e)                  Perl_pv_display(aTHX_ a,b,c,d,e)
# define pv_escape(a,b,c,d,e,f)                 Perl_pv_escape(aTHX_ a,b,c,d,e,f)
# define pv_pretty(a,b,c,d,e,f,g)               Perl_pv_pretty(aTHX_ a,b,c,d,e,f,g)
# define pv_uni_display(a,b,c,d,e)              Perl_pv_uni_display(aTHX_ a,b,c,d,e)
# define rcpv_copy(a)                           Perl_rcpv_copy(aTHX_ a)
# define rcpv_free(a)                           Perl_rcpv_free(aTHX_ a)
# define rcpv_new(a,b,c)                        Perl_rcpv_new(aTHX_ a,b,c)
# define re_compile(a,b)                        Perl_re_compile(aTHX_ a,b)
# define re_intuit_start(a,b,c,d,e,f,g)         Perl_re_intuit_start(aTHX_ a,b,c,d,e,f,g)
# define re_intuit_string(a)                    Perl_re_intuit_string(aTHX_ a)
# define reentrant_free()                       Perl_reentrant_free(aTHX)
# define reentrant_init()                       Perl_reentrant_init(aTHX)
# define reentrant_retry                        Perl_reentrant_retry
# define reentrant_size()                       Perl_reentrant_size(aTHX)
# define reg_named_buff_all(a,b)                Perl_reg_named_buff_all(aTHX_ a,b)
# define reg_named_buff_exists(a,b,c)           Perl_reg_named_buff_exists(aTHX_ a,b,c)
# define reg_named_buff_fetch(a,b,c)            Perl_reg_named_buff_fetch(aTHX_ a,b,c)
# define reg_named_buff_firstkey(a,b)           Perl_reg_named_buff_firstkey(aTHX_ a,b)
# define reg_named_buff_nextkey(a,b)            Perl_reg_named_buff_nextkey(aTHX_ a,b)
# define reg_named_buff_scalar(a,b)             Perl_reg_named_buff_scalar(aTHX_ a,b)
# define regdump(a)                             Perl_regdump(aTHX_ a)
# define regexec_flags(a,b,c,d,e,f,g,h)         Perl_regexec_flags(aTHX_ a,b,c,d,e,f,g,h)
# define regfree_internal(a)                    Perl_regfree_internal(aTHX_ a)
# define reginitcolors()                        Perl_reginitcolors(aTHX)
# define repeatcpy                              Perl_repeatcpy
# define require_pv(a)                          Perl_require_pv(aTHX_ a)
# define rninstr                                Perl_rninstr
# define rsignal(a,b)                           Perl_rsignal(aTHX_ a,b)
# define rsignal_state(a)                       Perl_rsignal_state(aTHX_ a)
# define runops_debug()                         Perl_runops_debug(aTHX)
# define runops_standard()                      Perl_runops_standard(aTHX)
# define rv2cv_op_cv(a,b)                       Perl_rv2cv_op_cv(aTHX_ a,b)
# define safesyscalloc                          Perl_safesyscalloc
# define safesysfree                            Perl_safesysfree
# define safesysmalloc                          Perl_safesysmalloc
# define safesysrealloc                         Perl_safesysrealloc
# define save_I16(a)                            Perl_save_I16(aTHX_ a)
# define save_I32(a)                            Perl_save_I32(aTHX_ a)
# define save_I8(a)                             Perl_save_I8(aTHX_ a)
# define save_adelete(a,b)                      Perl_save_adelete(aTHX_ a,b)
# define save_aelem_flags(a,b,c,d)              Perl_save_aelem_flags(aTHX_ a,b,c,d)
# define save_alloc(a,b)                        Perl_save_alloc(aTHX_ a,b)
# define save_aptr(a)                           Perl_save_aptr(aTHX_ a)
# define save_ary(a)                            Perl_save_ary(aTHX_ a)
# define save_bool(a)                           Perl_save_bool(aTHX_ a)
# define save_clearsv(a)                        Perl_save_clearsv(aTHX_ a)
# define save_delete(a,b,c)                     Perl_save_delete(aTHX_ a,b,c)
# define save_destructor(a,b)                   Perl_save_destructor(aTHX_ a,b)
# define save_destructor_x(a,b)                 Perl_save_destructor_x(aTHX_ a,b)
# define save_freercpv(a)                       Perl_save_freercpv(aTHX_ a)
# define save_generic_pvref(a)                  Perl_save_generic_pvref(aTHX_ a)
# define save_generic_svref(a)                  Perl_save_generic_svref(aTHX_ a)
# define save_gp(a,b)                           Perl_save_gp(aTHX_ a,b)
# define save_hash(a)                           Perl_save_hash(aTHX_ a)
# define save_hdelete(a,b)                      Perl_save_hdelete(aTHX_ a,b)
# define save_helem_flags(a,b,c,d)              Perl_save_helem_flags(aTHX_ a,b,c,d)
# define save_hints()                           Perl_save_hints(aTHX)
# define save_hptr(a)                           Perl_save_hptr(aTHX_ a)
# define save_int(a)                            Perl_save_int(aTHX_ a)
# define save_item(a)                           Perl_save_item(aTHX_ a)
# define save_iv(a)                             Perl_save_iv(aTHX_ a)
# define save_padsv_and_mortalize(a)            Perl_save_padsv_and_mortalize(aTHX_ a)
# define save_pptr(a)                           Perl_save_pptr(aTHX_ a)
# define save_pushi32ptr(a,b,c)                 Perl_save_pushi32ptr(aTHX_ a,b,c)
# define save_pushptr(a,b)                      Perl_save_pushptr(aTHX_ a,b)
# define save_pushptrptr(a,b,c)                 Perl_save_pushptrptr(aTHX_ a,b,c)
# define save_rcpv(a)                           Perl_save_rcpv(aTHX_ a)
# define save_re_context()                      Perl_save_re_context(aTHX)
# define save_scalar(a)                         Perl_save_scalar(aTHX_ a)
# define save_set_svflags(a,b,c)                Perl_save_set_svflags(aTHX_ a,b,c)
# define save_shared_pvref(a)                   Perl_save_shared_pvref(aTHX_ a)
# define save_sptr(a)                           Perl_save_sptr(aTHX_ a)
# define save_svref(a)                          Perl_save_svref(aTHX_ a)
# define save_vptr(a)                           Perl_save_vptr(aTHX_ a)
# define savepv(a)                              Perl_savepv(aTHX_ a)
# define savepvn(a,b)                           Perl_savepvn(aTHX_ a,b)
# define savesharedpv(a)                        Perl_savesharedpv(aTHX_ a)
# define savesharedpvn(a,b)                     Perl_savesharedpvn(aTHX_ a,b)
# define savesharedsvpv(a)                      Perl_savesharedsvpv(aTHX_ a)
# define savestack_grow()                       Perl_savestack_grow(aTHX)
# define savestack_grow_cnt(a)                  Perl_savestack_grow_cnt(aTHX_ a)
# define savesvpv(a)                            Perl_savesvpv(aTHX_ a)
# define scan_bin(a,b,c)                        Perl_scan_bin(aTHX_ a,b,c)
# define scan_hex(a,b,c)                        Perl_scan_hex(aTHX_ a,b,c)
# define scan_num(a,b)                          Perl_scan_num(aTHX_ a,b)
# define scan_oct(a,b,c)                        Perl_scan_oct(aTHX_ a,b,c)
# define scan_version(a,b,c)                    Perl_scan_version(aTHX_ a,b,c)
# define scan_vstring(a,b,c)                    Perl_scan_vstring(aTHX_ a,b,c)
# define seed()                                 Perl_seed(aTHX)
# define set_context                            Perl_set_context
# define setdefout(a)                           Perl_setdefout(aTHX_ a)
# define share_hek(a,b,c)                       Perl_share_hek(aTHX_ a,b,c)
# define single_1bit_pos32                      Perl_single_1bit_pos32
# define sortsv(a,b,c)                          Perl_sortsv(aTHX_ a,b,c)
# define sortsv_flags(a,b,c,d)                  Perl_sortsv_flags(aTHX_ a,b,c,d)
# define stack_grow(a,b,c)                      Perl_stack_grow(aTHX_ a,b,c)
# define start_subparse(a,b)                    Perl_start_subparse(aTHX_ a,b)
# define str_to_version(a)                      Perl_str_to_version(aTHX_ a)
# define suspend_compcv(a)                      Perl_suspend_compcv(aTHX_ a)
# define sv_2bool_flags(a,b)                    Perl_sv_2bool_flags(aTHX_ a,b)
# define sv_2cv(a,b,c,d)                        Perl_sv_2cv(aTHX_ a,b,c,d)
# define sv_2io(a)                              Perl_sv_2io(aTHX_ a)
# define sv_2iv_flags(a,b)                      Perl_sv_2iv_flags(aTHX_ a,b)
# define sv_2mortal(a)                          Perl_sv_2mortal(aTHX_ a)
# define sv_2nv_flags(a,b)                      Perl_sv_2nv_flags(aTHX_ a,b)
# define sv_2pv_flags(a,b,c)                    Perl_sv_2pv_flags(aTHX_ a,b,c)
# define sv_2pvbyte_flags(a,b,c)                Perl_sv_2pvbyte_flags(aTHX_ a,b,c)
# define sv_2pvutf8_flags(a,b,c)                Perl_sv_2pvutf8_flags(aTHX_ a,b,c)
# define sv_2uv_flags(a,b)                      Perl_sv_2uv_flags(aTHX_ a,b)
# define sv_backoff                             Perl_sv_backoff
# define sv_bless(a,b)                          Perl_sv_bless(aTHX_ a,b)
# define sv_cat_decode(a,b,c,d,e,f)             Perl_sv_cat_decode(aTHX_ a,b,c,d,e,f)
# define sv_catpv(a,b)                          Perl_sv_catpv(aTHX_ a,b)
# define sv_catpv_flags(a,b,c)                  Perl_sv_catpv_flags(aTHX_ a,b,c)
# define sv_catpv_mg(a,b)                       Perl_sv_catpv_mg(aTHX_ a,b)
# define sv_catpvn_flags(a,b,c,d)               Perl_sv_catpvn_flags(aTHX_ a,b,c,d)
# define sv_catsv_flags(a,b,c)                  Perl_sv_catsv_flags(aTHX_ a,b,c)
# define sv_chop(a,b)                           Perl_sv_chop(aTHX_ a,b)
# define sv_clear(a)                            Perl_sv_clear(aTHX_ a)
# define sv_cmp_flags(a,b,c)                    Perl_sv_cmp_flags(aTHX_ a,b,c)
# define sv_cmp_locale_flags(a,b,c)             Perl_sv_cmp_locale_flags(aTHX_ a,b,c)
# define sv_copypv_flags(a,b,c)                 Perl_sv_copypv_flags(aTHX_ a,b,c)
# define sv_dec(a)                              Perl_sv_dec(aTHX_ a)
# define sv_dec_nomg(a)                         Perl_sv_dec_nomg(aTHX_ a)
# define sv_derived_from(a,b)                   Perl_sv_derived_from(aTHX_ a,b)
# define sv_derived_from_hv(a,b)                Perl_sv_derived_from_hv(aTHX_ a,b)
# define sv_derived_from_pv(a,b,c)              Perl_sv_derived_from_pv(aTHX_ a,b,c)
# define sv_derived_from_pvn(a,b,c,d)           Perl_sv_derived_from_pvn(aTHX_ a,b,c,d)
# define sv_derived_from_sv(a,b,c)              Perl_sv_derived_from_sv(aTHX_ a,b,c)
# define sv_destroyable(a)                      Perl_sv_destroyable(aTHX_ a)
# define sv_does(a,b)                           Perl_sv_does(aTHX_ a,b)
# define sv_does_pv(a,b,c)                      Perl_sv_does_pv(aTHX_ a,b,c)
# define sv_does_pvn(a,b,c,d)                   Perl_sv_does_pvn(aTHX_ a,b,c,d)
# define sv_does_sv(a,b,c)                      Perl_sv_does_sv(aTHX_ a,b,c)
# define sv_dump(a)                             Perl_sv_dump(aTHX_ a)
# define sv_dump_depth(a,b)                     Perl_sv_dump_depth(aTHX_ a,b)
# define sv_eq_flags(a,b,c)                     Perl_sv_eq_flags(aTHX_ a,b,c)
# define sv_force_normal_flags(a,b)             Perl_sv_force_normal_flags(aTHX_ a,b)
# define sv_free(a)                             Perl_sv_free(aTHX_ a)
# define sv_get_backrefs                        Perl_sv_get_backrefs
# define sv_gets(a,b,c)                         Perl_sv_gets(aTHX_ a,b,c)
# define sv_grow(a,b)                           Perl_sv_grow(aTHX_ a,b)
# define sv_grow_fresh(a,b)                     Perl_sv_grow_fresh(aTHX_ a,b)
# define sv_inc(a)                              Perl_sv_inc(aTHX_ a)
# define sv_inc_nomg(a)                         Perl_sv_inc_nomg(aTHX_ a)
# define sv_insert_flags(a,b,c,d,e,f)           Perl_sv_insert_flags(aTHX_ a,b,c,d,e,f)
# define sv_isa(a,b)                            Perl_sv_isa(aTHX_ a,b)
# define sv_isa_sv(a,b)                         Perl_sv_isa_sv(aTHX_ a,b)
# define sv_isobject(a)                         Perl_sv_isobject(aTHX_ a)
# define sv_len(a)                              Perl_sv_len(aTHX_ a)
# define sv_len_utf8(a)                         Perl_sv_len_utf8(aTHX_ a)
# define sv_len_utf8_nomg(a)                    Perl_sv_len_utf8_nomg(aTHX_ a)
# define sv_magic(a,b,c,d,e)                    Perl_sv_magic(aTHX_ a,b,c,d,e)
# define sv_magicext(a,b,c,d,e,f)               Perl_sv_magicext(aTHX_ a,b,c,d,e,f)
# define sv_mortalcopy_flags(a,b)               Perl_sv_mortalcopy_flags(aTHX_ a,b)
# define sv_newmortal()                         Perl_sv_newmortal(aTHX)
# define sv_newref(a)                           Perl_sv_newref(aTHX_ a)
# define sv_nosharing(a)                        Perl_sv_nosharing(aTHX_ a)
# define sv_numeq_flags(a,b,c)                  Perl_sv_numeq_flags(aTHX_ a,b,c)
# define sv_peek(a)                             Perl_sv_peek(aTHX_ a)
# define sv_pos_b2u(a,b)                        Perl_sv_pos_b2u(aTHX_ a,b)
# define sv_pos_b2u_flags(a,b,c)                Perl_sv_pos_b2u_flags(aTHX_ a,b,c)
# define sv_pos_u2b(a,b,c)                      Perl_sv_pos_u2b(aTHX_ a,b,c)
# define sv_pos_u2b_flags(a,b,c,d)              Perl_sv_pos_u2b_flags(aTHX_ a,b,c,d)
# define sv_pvbyten_force(a,b)                  Perl_sv_pvbyten_force(aTHX_ a,b)
# define sv_pvn_force_flags(a,b,c)              Perl_sv_pvn_force_flags(aTHX_ a,b,c)
# define sv_pvutf8n_force(a,b)                  Perl_sv_pvutf8n_force(aTHX_ a,b)
# define sv_recode_to_utf8(a,b)                 Perl_sv_recode_to_utf8(aTHX_ a,b)
# define sv_ref(a,b,c)                          Perl_sv_ref(aTHX_ a,b,c)
# define sv_reftype(a,b)                        Perl_sv_reftype(aTHX_ a,b)
# define sv_replace(a,b)                        Perl_sv_replace(aTHX_ a,b)
# define sv_report_used()                       Perl_sv_report_used(aTHX)
# define sv_reset(a,b)                          Perl_sv_reset(aTHX_ a,b)
# define sv_rvunweaken(a)                       Perl_sv_rvunweaken(aTHX_ a)
# define sv_rvweaken(a)                         Perl_sv_rvweaken(aTHX_ a)
# define sv_set_bool(a,b)                       Perl_sv_set_bool(aTHX_ a,b)
# define sv_set_false(a)                        Perl_sv_set_false(aTHX_ a)
# define sv_set_true(a)                         Perl_sv_set_true(aTHX_ a)
# define sv_set_undef(a)                        Perl_sv_set_undef(aTHX_ a)
# define sv_setiv(a,b)                          Perl_sv_setiv(aTHX_ a,b)
# define sv_setiv_mg(a,b)                       Perl_sv_setiv_mg(aTHX_ a,b)
# define sv_setnv(a,b)                          Perl_sv_setnv(aTHX_ a,b)
# define sv_setnv_mg(a,b)                       Perl_sv_setnv_mg(aTHX_ a,b)
# define sv_setpv(a,b)                          Perl_sv_setpv(aTHX_ a,b)
# define sv_setpv_bufsize(a,b,c)                Perl_sv_setpv_bufsize(aTHX_ a,b,c)
# define sv_setpv_freshbuf(a)                   Perl_sv_setpv_freshbuf(aTHX_ a)
# define sv_setpv_mg(a,b)                       Perl_sv_setpv_mg(aTHX_ a,b)
# define sv_setpvn(a,b,c)                       Perl_sv_setpvn(aTHX_ a,b,c)
# define sv_setpvn_fresh(a,b,c)                 Perl_sv_setpvn_fresh(aTHX_ a,b,c)
# define sv_setpvn_mg(a,b,c)                    Perl_sv_setpvn_mg(aTHX_ a,b,c)
# define sv_setref_iv(a,b,c)                    Perl_sv_setref_iv(aTHX_ a,b,c)
# define sv_setref_nv(a,b,c)                    Perl_sv_setref_nv(aTHX_ a,b,c)
# define sv_setref_pv(a,b,c)                    Perl_sv_setref_pv(aTHX_ a,b,c)
# define sv_setref_pvn(a,b,c,d)                 Perl_sv_setref_pvn(aTHX_ a,b,c,d)
# define sv_setref_uv(a,b,c)                    Perl_sv_setref_uv(aTHX_ a,b,c)
# define sv_setrv_inc(a,b)                      Perl_sv_setrv_inc(aTHX_ a,b)
# define sv_setrv_inc_mg(a,b)                   Perl_sv_setrv_inc_mg(aTHX_ a,b)
# define sv_setrv_noinc(a,b)                    Perl_sv_setrv_noinc(aTHX_ a,b)
# define sv_setrv_noinc_mg(a,b)                 Perl_sv_setrv_noinc_mg(aTHX_ a,b)
# define sv_setsv_flags(a,b,c)                  Perl_sv_setsv_flags(aTHX_ a,b,c)
# define sv_setsv_mg(a,b)                       Perl_sv_setsv_mg(aTHX_ a,b)
# define sv_setuv(a,b)                          Perl_sv_setuv(aTHX_ a,b)
# define sv_setuv_mg(a,b)                       Perl_sv_setuv_mg(aTHX_ a,b)
# define sv_streq_flags(a,b,c)                  Perl_sv_streq_flags(aTHX_ a,b,c)
# define sv_string_from_errnum(a,b)             Perl_sv_string_from_errnum(aTHX_ a,b)
# define sv_tainted(a)                          Perl_sv_tainted(aTHX_ a)
# define sv_true(a)                             Perl_sv_true(aTHX_ a)
# define sv_uni_display(a,b,c,d)                Perl_sv_uni_display(aTHX_ a,b,c,d)
# define sv_unmagic(a,b)                        Perl_sv_unmagic(aTHX_ a,b)
# define sv_unmagicext(a,b,c)                   Perl_sv_unmagicext(aTHX_ a,b,c)
# define sv_unref_flags(a,b)                    Perl_sv_unref_flags(aTHX_ a,b)
# define sv_untaint(a)                          Perl_sv_untaint(aTHX_ a)
# define sv_upgrade(a,b)                        Perl_sv_upgrade(aTHX_ a,b)
# define sv_usepvn_flags(a,b,c,d)               Perl_sv_usepvn_flags(aTHX_ a,b,c,d)
# define sv_utf8_decode(a)                      Perl_sv_utf8_decode(aTHX_ a)
# define sv_utf8_downgrade_flags(a,b,c)         Perl_sv_utf8_downgrade_flags(aTHX_ a,b,c)
# define sv_utf8_encode(a)                      Perl_sv_utf8_encode(aTHX_ a)
# define sv_utf8_upgrade_flags_grow(a,b,c)      Perl_sv_utf8_upgrade_flags_grow(aTHX_ a,b,c)
# define sv_vcatpvf(a,b,c)                      Perl_sv_vcatpvf(aTHX_ a,b,c)
# define sv_vcatpvf_mg(a,b,c)                   Perl_sv_vcatpvf_mg(aTHX_ a,b,c)
# define sv_vcatpvfn(a,b,c,d,e,f,g)             Perl_sv_vcatpvfn(aTHX_ a,b,c,d,e,f,g)
# define sv_vcatpvfn_flags(a,b,c,d,e,f,g,h)     Perl_sv_vcatpvfn_flags(aTHX_ a,b,c,d,e,f,g,h)
# define sv_vsetpvf(a,b,c)                      Perl_sv_vsetpvf(aTHX_ a,b,c)
# define sv_vsetpvf_mg(a,b,c)                   Perl_sv_vsetpvf_mg(aTHX_ a,b,c)
# define sv_vsetpvfn(a,b,c,d,e,f,g)             Perl_sv_vsetpvfn(aTHX_ a,b,c,d,e,f,g)
# define switch_to_global_locale()              Perl_switch_to_global_locale(aTHX)
# define sync_locale()                          Perl_sync_locale(aTHX)
# define taint_env()                            Perl_taint_env(aTHX)
# define taint_proper(a,b)                      Perl_taint_proper(aTHX_ a,b)
# define thread_locale_init()                   Perl_thread_locale_init(aTHX)
# define thread_locale_term()                   Perl_thread_locale_term(aTHX)
# define to_uni_lower(a,b,c)                    Perl_to_uni_lower(aTHX_ a,b,c)
# define to_uni_title(a,b,c)                    Perl_to_uni_title(aTHX_ a,b,c)
# define to_uni_upper(a,b,c)                    Perl_to_uni_upper(aTHX_ a,b,c)
# define unpackstring(a,b,c,d,e)                Perl_unpackstring(aTHX_ a,b,c,d,e)
# define unshare_hek(a)                         Perl_unshare_hek(aTHX_ a)
# define unsharepvn(a,b,c)                      Perl_unsharepvn(aTHX_ a,b,c)
# define upg_version(a,b)                       Perl_upg_version(aTHX_ a,b)
# define utf8_distance(a,b)                     Perl_utf8_distance(aTHX_ a,b)
# define utf8_hop                               Perl_utf8_hop
# define utf8_hop_back                          Perl_utf8_hop_back
# define utf8_hop_forward                       Perl_utf8_hop_forward
# define utf8_hop_safe                          Perl_utf8_hop_safe
# define utf8_length(a,b)                       Perl_utf8_length(aTHX_ a,b)
# define utf8_to_bytes(a,b)                     Perl_utf8_to_bytes(aTHX_ a,b)
# define utf8_to_uvchr_buf_helper(a,b,c)        Perl_utf8_to_uvchr_buf_helper(aTHX_ a,b,c)
# define utf8n_to_uvchr_msgs                    Perl_utf8n_to_uvchr_msgs
# define uvoffuni_to_utf8_flags_msgs(a,b,c,d)   Perl_uvoffuni_to_utf8_flags_msgs(aTHX_ a,b,c,d)
# define uvuni_to_utf8(a,b)                     Perl_uvuni_to_utf8(aTHX_ a,b)
# define valid_utf8_to_uvchr                    Perl_valid_utf8_to_uvchr
# define vcmp(a,b)                              Perl_vcmp(aTHX_ a,b)
# define vcroak(a,b)                            Perl_vcroak(aTHX_ a,b)
# define vdeb(a,b)                              Perl_vdeb(aTHX_ a,b)
# define vform(a,b)                             Perl_vform(aTHX_ a,b)
# define vload_module(a,b,c,d)                  Perl_vload_module(aTHX_ a,b,c,d)
# define vmess(a,b)                             Perl_vmess(aTHX_ a,b)
# define vnewSVpvf(a,b)                         Perl_vnewSVpvf(aTHX_ a,b)
# define vnormal(a)                             Perl_vnormal(aTHX_ a)
# define vnumify(a)                             Perl_vnumify(aTHX_ a)
# define vstringify(a)                          Perl_vstringify(aTHX_ a)
# define vverify(a)                             Perl_vverify(aTHX_ a)
# define vwarn(a,b)                             Perl_vwarn(aTHX_ a,b)
# define vwarner(a,b,c)                         Perl_vwarner(aTHX_ a,b,c)
# define warn_sv(a)                             Perl_warn_sv(aTHX_ a)
# define whichsig_pv(a)                         Perl_whichsig_pv(aTHX_ a)
# define whichsig_pvn(a,b)                      Perl_whichsig_pvn(aTHX_ a,b)
# define whichsig_sv(a)                         Perl_whichsig_sv(aTHX_ a)
# define wrap_infix_plugin(a,b)                 Perl_wrap_infix_plugin(aTHX_ a,b)
# define wrap_keyword_plugin(a,b)               Perl_wrap_keyword_plugin(aTHX_ a,b)
# define wrap_op_checker(a,b,c)                 Perl_wrap_op_checker(aTHX_ a,b,c)

# if defined(DEBUGGING)
#   define pad_setsv(a,b)                       Perl_pad_setsv(aTHX_ a,b)
#   define pad_sv(a)                            Perl_pad_sv(aTHX_ a)
# endif
# if !defined(EBCDIC)
#   define variant_byte_number                  Perl_variant_byte_number
# endif
# if defined(F_FREESP) && !defined(HAS_CHSIZE) && !defined(HAS_TRUNCATE)
#   define my_chsize(a,b)                       Perl_my_chsize(aTHX_ a,b)
# endif
# if !defined(HAS_STRLCAT)
#   define my_strlcat                           Perl_my_strlcat
# endif
# if !defined(HAS_STRLCPY)
#   define my_strlcpy                           Perl_my_strlcpy
# endif
# if !defined(HAS_STRNLEN)
#   define my_strnlen                           Perl_my_strnlen
# endif
# if defined(HAVE_INTERP_INTERN)
#   define sys_intern_clear()                   Perl_sys_intern_clear(aTHX)
#   define sys_intern_init()                    Perl_sys_intern_init(aTHX)
#   if defined(USE_ITHREADS)
#     define sys_intern_dup(a,b)                Perl_sys_intern_dup(aTHX_ a,b)
#   endif
# endif
# if defined(MULTIPLICITY)
#   define croak_nocontext                      Perl_croak_nocontext
#   define deb_nocontext                        Perl_deb_nocontext
#   define die_nocontext                        Perl_die_nocontext
#   define form_nocontext                       Perl_form_nocontext
#   define load_module_nocontext                Perl_load_module_nocontext
#   define mess_nocontext                       Perl_mess_nocontext
#   define newSVpvf_nocontext                   Perl_newSVpvf_nocontext
#   define sv_catpvf_mg_nocontext               Perl_sv_catpvf_mg_nocontext
#   define sv_catpvf_nocontext                  Perl_sv_catpvf_nocontext
#   define sv_setpvf_mg_nocontext               Perl_sv_setpvf_mg_nocontext
#   define sv_setpvf_nocontext                  Perl_sv_setpvf_nocontext
#   define warn_nocontext                       Perl_warn_nocontext
#   define warner_nocontext                     Perl_warner_nocontext
# endif /* defined(MULTIPLICITY) */
# if !defined(MULTIPLICITY) || defined(PERL_CORE)
#   define ck_warner(a,...)                     Perl_ck_warner(aTHX_ a,__VA_ARGS__)
#   define ck_warner_d(a,...)                   Perl_ck_warner_d(aTHX_ a,__VA_ARGS__)
#   define croak(...)                           Perl_croak(aTHX_ __VA_ARGS__)
#   define deb(...)                             Perl_deb(aTHX_ __VA_ARGS__)
#   define die(...)                             Perl_die(aTHX_ __VA_ARGS__)
#   define dump_indent(a,b,...)                 Perl_dump_indent(aTHX_ a,b,__VA_ARGS__)
#   define form(...)                            Perl_form(aTHX_ __VA_ARGS__)
#   define load_module(a,b,...)                 Perl_load_module(aTHX_ a,b,__VA_ARGS__)
#   define mess(...)                            Perl_mess(aTHX_ __VA_ARGS__)
#   define newSVpvf(...)                        Perl_newSVpvf(aTHX_ __VA_ARGS__)
#   define sv_catpvf(a,...)                     Perl_sv_catpvf(aTHX_ a,__VA_ARGS__)
#   define sv_catpvf_mg(a,...)                  Perl_sv_catpvf_mg(aTHX_ a,__VA_ARGS__)
#   define sv_setpvf(a,...)                     Perl_sv_setpvf(aTHX_ a,__VA_ARGS__)
#   define sv_setpvf_mg(a,...)                  Perl_sv_setpvf_mg(aTHX_ a,__VA_ARGS__)
#   define warn(...)                            Perl_warn(aTHX_ __VA_ARGS__)
#   define warner(a,...)                        Perl_warner(aTHX_ a,__VA_ARGS__)
# endif /* !defined(MULTIPLICITY) || defined(PERL_CORE) */
# if defined(MYMALLOC)
#   define dump_mstats(a)                       Perl_dump_mstats(aTHX_ a)
#   define get_mstats(a,b,c)                    Perl_get_mstats(aTHX_ a,b,c)
#   if defined(PERL_CORE)
#     define malloc_good_size                   Perl_malloc_good_size
#     define malloced_size                      Perl_malloced_size
#   endif
# endif
# if !defined(NO_MATHOMS)
#   define sv_nolocking(a)                      Perl_sv_nolocking(aTHX_ a)
#   define sv_nounlocking(a)                    Perl_sv_nounlocking(aTHX_ a)
#   define utf8_to_uvchr(a,b)                   Perl_utf8_to_uvchr(aTHX_ a,b)
#   define utf8_to_uvuni(a,b)                   Perl_utf8_to_uvuni(aTHX_ a,b)
#   define utf8n_to_uvuni(a,b,c,d)              Perl_utf8n_to_uvuni(aTHX_ a,b,c,d)
# endif
# if defined(PERL_CORE)
#   define PerlLIO_dup2_cloexec(a,b)            Perl_PerlLIO_dup2_cloexec(aTHX_ a,b)
#   define PerlLIO_dup_cloexec(a)               Perl_PerlLIO_dup_cloexec(aTHX_ a)
#   define PerlLIO_open3_cloexec(a,b,c)         Perl_PerlLIO_open3_cloexec(aTHX_ a,b,c)
#   define PerlLIO_open_cloexec(a,b)            Perl_PerlLIO_open_cloexec(aTHX_ a,b)
#   define Slab_Alloc(a)                        Perl_Slab_Alloc(aTHX_ a)
#   define Slab_Free(a)                         Perl_Slab_Free(aTHX_ a)
#   define _warn_problematic_locale             Perl__warn_problematic_locale
#   define abort_execution(a,b)                 Perl_abort_execution(aTHX_ a,b)
#   define alloc_LOGOP(a,b,c)                   Perl_alloc_LOGOP(aTHX_ a,b,c)
#   define allocmy(a,b,c)                       Perl_allocmy(aTHX_ a,b,c)
#   define amagic_applies(a,b,c)                Perl_amagic_applies(aTHX_ a,b,c)
#   define amagic_is_enabled(a)                 Perl_amagic_is_enabled(aTHX_ a)
#   define apply(a,b,c)                         Perl_apply(aTHX_ a,b,c)
#   define av_extend_guts(a,b,c,d,e)            Perl_av_extend_guts(aTHX_ a,b,c,d,e)
#   define av_nonelem(a,b)                      Perl_av_nonelem(aTHX_ a,b)
#   define bind_match(a,b,c)                    Perl_bind_match(aTHX_ a,b,c)
#   define boot_core_PerlIO()                   Perl_boot_core_PerlIO(aTHX)
#   define boot_core_UNIVERSAL()                Perl_boot_core_UNIVERSAL(aTHX)
#   define boot_core_builtin()                  Perl_boot_core_builtin(aTHX)
#   define boot_core_mro()                      Perl_boot_core_mro(aTHX)
#   define build_infix_plugin(a,b,c)            Perl_build_infix_plugin(aTHX_ a,b,c)
#   define cando(a,b,c)                         Perl_cando(aTHX_ a,b,c)
#   define check_utf8_print(a,b)                Perl_check_utf8_print(aTHX_ a,b)
#   define closest_cop(a,b,c,d)                 Perl_closest_cop(aTHX_ a,b,c,d)
#   define cmpchain_extend(a,b,c)               Perl_cmpchain_extend(aTHX_ a,b,c)
#   define cmpchain_finish(a)                   Perl_cmpchain_finish(aTHX_ a)
#   define cmpchain_start(a,b,c)                Perl_cmpchain_start(aTHX_ a,b,c)
#   define core_prototype(a,b,c,d)              Perl_core_prototype(aTHX_ a,b,c,d)
#   define coresub_op(a,b,c)                    Perl_coresub_op(aTHX_ a,b,c)
#   define create_eval_scope(a,b)               Perl_create_eval_scope(aTHX_ a,b)
#   define croak_caller                         Perl_croak_caller
#   define croak_no_mem                         Perl_croak_no_mem
#   define croak_popstack                       Perl_croak_popstack
#   define custom_op_get_field(a,b)             Perl_custom_op_get_field(aTHX_ a,b)
#   define cv_clone_into(a,b)                   Perl_cv_clone_into(aTHX_ a,b)
#   define cv_const_sv_or_av                    Perl_cv_const_sv_or_av
#   define cv_forget_slab(a)                    Perl_cv_forget_slab(aTHX_ a)
#   define cv_undef_flags(a,b)                  Perl_cv_undef_flags(aTHX_ a,b)
#   define cvgv_set(a,b)                        Perl_cvgv_set(aTHX_ a,b)
#   define cvstash_set(a,b)                     Perl_cvstash_set(aTHX_ a,b)
#   define deb_stack_all()                      Perl_deb_stack_all(aTHX)
#   define debug_hash_seed(a)                   Perl_debug_hash_seed(aTHX_ a)
#   define defelem_target(a,b)                  Perl_defelem_target(aTHX_ a,b)
#   define delete_eval_scope()                  Perl_delete_eval_scope(aTHX)
#   define die_unwind(a)                        Perl_die_unwind(aTHX_ a)
#   define do_aexec5(a,b,c,d,e)                 Perl_do_aexec5(aTHX_ a,b,c,d,e)
#   define do_dump_pad(a,b,c,d)                 Perl_do_dump_pad(aTHX_ a,b,c,d)
#   define do_eof(a)                            Perl_do_eof(aTHX_ a)
#   define do_ncmp(a,b)                         Perl_do_ncmp(aTHX_ a,b)
#   define do_open6(a,b,c,d,e,f)                Perl_do_open6(aTHX_ a,b,c,d,e,f)
#   define do_open_raw(a,b,c,d,e,f)             Perl_do_open_raw(aTHX_ a,b,c,d,e,f)
#   define do_print(a,b)                        Perl_do_print(aTHX_ a,b)
#   define do_readline()                        Perl_do_readline(aTHX)
#   define do_seek(a,b,c)                       Perl_do_seek(aTHX_ a,b,c)
#   define do_sysseek(a,b,c)                    Perl_do_sysseek(aTHX_ a,b,c)
#   define do_tell(a)                           Perl_do_tell(aTHX_ a)
#   define do_trans(a)                          Perl_do_trans(aTHX_ a)
#   define do_vecget(a,b,c)                     Perl_do_vecget(aTHX_ a,b,c)
#   define do_vecset(a)                         Perl_do_vecset(aTHX_ a)
#   define do_vop(a,b,c,d)                      Perl_do_vop(aTHX_ a,b,c,d)
#   define dofile(a,b)                          Perl_dofile(aTHX_ a,b)
#   define dump_all_perl(a)                     Perl_dump_all_perl(aTHX_ a)
#   define dump_packsubs_perl(a,b)              Perl_dump_packsubs_perl(aTHX_ a,b)
#   define dump_sub_perl(a,b)                   Perl_dump_sub_perl(aTHX_ a,b)
#   define find_lexical_cv(a)                   Perl_find_lexical_cv(aTHX_ a)
#   define find_runcv_where(a,b,c)              Perl_find_runcv_where(aTHX_ a,b,c)
#   define find_script(a,b,c,d)                 Perl_find_script(aTHX_ a,b,c,d)
#   define force_locale_unlock                  Perl_force_locale_unlock
#   define free_tied_hv_pool()                  Perl_free_tied_hv_pool(aTHX)
#   define get_extended_os_errno                Perl_get_extended_os_errno
#   define get_hash_seed(a)                     Perl_get_hash_seed(aTHX_ a)
#   define get_no_modify()                      Perl_get_no_modify(aTHX)
#   define get_opargs()                         Perl_get_opargs(aTHX)
#   define gv_override(a,b)                     Perl_gv_override(aTHX_ a,b)
#   define gv_setref(a,b)                       Perl_gv_setref(aTHX_ a,b)
#   define gv_try_downgrade(a)                  Perl_gv_try_downgrade(aTHX_ a)
#   define hv_ename_add(a,b,c,d)                Perl_hv_ename_add(aTHX_ a,b,c,d)
#   define hv_ename_delete(a,b,c,d)             Perl_hv_ename_delete(aTHX_ a,b,c,d)
#   define hv_pushkv(a,b)                       Perl_hv_pushkv(aTHX_ a,b)
#   define init_argv_symbols(a,b)               Perl_init_argv_symbols(aTHX_ a,b)
#   define init_constants()                     Perl_init_constants(aTHX)
#   define init_debugger()                      Perl_init_debugger(aTHX)
#   define init_named_cv(a,b)                   Perl_init_named_cv(aTHX_ a,b)
#   define init_uniprops()                      Perl_init_uniprops(aTHX)
#   define invert(a)                            Perl_invert(aTHX_ a)
#   define invmap_dump(a,b)                     Perl_invmap_dump(aTHX_ a,b)
#   define io_close(a,b,c,d)                    Perl_io_close(aTHX_ a,b,c,d)
#   define isinfnansv(a)                        Perl_isinfnansv(aTHX_ a)
#   define jmaybe(a)                            Perl_jmaybe(aTHX_ a)
#   define keyword(a,b,c)                       Perl_keyword(aTHX_ a,b,c)
#   define list(a)                              Perl_list(aTHX_ a)
#   define localize(a,b)                        Perl_localize(aTHX_ a,b)
#   define magic_clear_all_env(a,b)             Perl_magic_clear_all_env(aTHX_ a,b)
#   define magic_cleararylen_p(a,b)             Perl_magic_cleararylen_p(aTHX_ a,b)
#   define magic_clearenv(a,b)                  Perl_magic_clearenv(aTHX_ a,b)
#   define magic_clearhint(a,b)                 Perl_magic_clearhint(aTHX_ a,b)
#   define magic_clearhints(a,b)                Perl_magic_clearhints(aTHX_ a,b)
#   define magic_clearhook(a,b)                 Perl_magic_clearhook(aTHX_ a,b)
#   define magic_clearhookall(a,b)              Perl_magic_clearhookall(aTHX_ a,b)
#   define magic_clearisa(a,b)                  Perl_magic_clearisa(aTHX_ a,b)
#   define magic_clearpack(a,b)                 Perl_magic_clearpack(aTHX_ a,b)
#   define magic_clearsig(a,b)                  Perl_magic_clearsig(aTHX_ a,b)
#   define magic_copycallchecker(a,b,c,d,e)     Perl_magic_copycallchecker(aTHX_ a,b,c,d,e)
#   define magic_existspack(a,b)                Perl_magic_existspack(aTHX_ a,b)
#   define magic_freearylen_p(a,b)              Perl_magic_freearylen_p(aTHX_ a,b)
#   define magic_freedestruct(a,b)              Perl_magic_freedestruct(aTHX_ a,b)
#   define magic_freemglob(a,b)                 Perl_magic_freemglob(aTHX_ a,b)
#   define magic_freeovrld(a,b)                 Perl_magic_freeovrld(aTHX_ a,b)
#   define magic_freeutf8(a,b)                  Perl_magic_freeutf8(aTHX_ a,b)
#   define magic_get(a,b)                       Perl_magic_get(aTHX_ a,b)
#   define magic_getarylen(a,b)                 Perl_magic_getarylen(aTHX_ a,b)
#   define magic_getdebugvar(a,b)               Perl_magic_getdebugvar(aTHX_ a,b)
#   define magic_getdefelem(a,b)                Perl_magic_getdefelem(aTHX_ a,b)
#   define magic_getnkeys(a,b)                  Perl_magic_getnkeys(aTHX_ a,b)
#   define magic_getpack(a,b)                   Perl_magic_getpack(aTHX_ a,b)
#   define magic_getpos(a,b)                    Perl_magic_getpos(aTHX_ a,b)
#   define magic_getsig(a,b)                    Perl_magic_getsig(aTHX_ a,b)
#   define magic_getsubstr(a,b)                 Perl_magic_getsubstr(aTHX_ a,b)
#   define magic_gettaint(a,b)                  Perl_magic_gettaint(aTHX_ a,b)
#   define magic_getuvar(a,b)                   Perl_magic_getuvar(aTHX_ a,b)
#   define magic_getvec(a,b)                    Perl_magic_getvec(aTHX_ a,b)
#   define magic_killbackrefs(a,b)              Perl_magic_killbackrefs(aTHX_ a,b)
#   define magic_nextpack(a,b,c)                Perl_magic_nextpack(aTHX_ a,b,c)
#   define magic_regdata_cnt(a,b)               Perl_magic_regdata_cnt(aTHX_ a,b)
#   define magic_regdatum_get(a,b)              Perl_magic_regdatum_get(aTHX_ a,b)
#   define magic_scalarpack(a,b)                Perl_magic_scalarpack(aTHX_ a,b)
#   define magic_set(a,b)                       Perl_magic_set(aTHX_ a,b)
#   define magic_set_all_env(a,b)               Perl_magic_set_all_env(aTHX_ a,b)
#   define magic_setarylen(a,b)                 Perl_magic_setarylen(aTHX_ a,b)
#   define magic_setdbline(a,b)                 Perl_magic_setdbline(aTHX_ a,b)
#   define magic_setdebugvar(a,b)               Perl_magic_setdebugvar(aTHX_ a,b)
#   define magic_setdefelem(a,b)                Perl_magic_setdefelem(aTHX_ a,b)
#   define magic_setenv(a,b)                    Perl_magic_setenv(aTHX_ a,b)
#   define magic_sethint(a,b)                   Perl_magic_sethint(aTHX_ a,b)
#   define magic_sethook(a,b)                   Perl_magic_sethook(aTHX_ a,b)
#   define magic_sethookall(a,b)                Perl_magic_sethookall(aTHX_ a,b)
#   define magic_setisa(a,b)                    Perl_magic_setisa(aTHX_ a,b)
#   define magic_setlvref(a,b)                  Perl_magic_setlvref(aTHX_ a,b)
#   define magic_setmglob(a,b)                  Perl_magic_setmglob(aTHX_ a,b)
#   define magic_setnkeys(a,b)                  Perl_magic_setnkeys(aTHX_ a,b)
#   define magic_setnonelem(a,b)                Perl_magic_setnonelem(aTHX_ a,b)
#   define magic_setpack(a,b)                   Perl_magic_setpack(aTHX_ a,b)
#   define magic_setpos(a,b)                    Perl_magic_setpos(aTHX_ a,b)
#   define magic_setregexp(a,b)                 Perl_magic_setregexp(aTHX_ a,b)
#   define magic_setsig(a,b)                    Perl_magic_setsig(aTHX_ a,b)
#   define magic_setsigall(a,b)                 Perl_magic_setsigall(aTHX_ a,b)
#   define magic_setsubstr(a,b)                 Perl_magic_setsubstr(aTHX_ a,b)
#   define magic_settaint(a,b)                  Perl_magic_settaint(aTHX_ a,b)
#   define magic_setutf8(a,b)                   Perl_magic_setutf8(aTHX_ a,b)
#   define magic_setuvar(a,b)                   Perl_magic_setuvar(aTHX_ a,b)
#   define magic_setvec(a,b)                    Perl_magic_setvec(aTHX_ a,b)
#   define magic_sizepack(a,b)                  Perl_magic_sizepack(aTHX_ a,b)
#   define magic_wipepack(a,b)                  Perl_magic_wipepack(aTHX_ a,b)
#   define mg_localize(a,b,c)                   Perl_mg_localize(aTHX_ a,b,c)
#   define mode_from_discipline(a,b)            Perl_mode_from_discipline(aTHX_ a,b)
#   define mro_isa_changed_in(a)                Perl_mro_isa_changed_in(aTHX_ a)
#   define mro_package_moved(a,b,c,d)           Perl_mro_package_moved(aTHX_ a,b,c,d)
#   define my_attrs(a,b)                        Perl_my_attrs(aTHX_ a,b)
#   define my_clearenv()                        Perl_my_clearenv(aTHX)
#   define my_lstat_flags(a)                    Perl_my_lstat_flags(aTHX_ a)
#   define my_stat_flags(a)                     Perl_my_stat_flags(aTHX_ a)
#   define my_strerror(a,b)                     Perl_my_strerror(aTHX_ a,b)
#   define my_unexec()                          Perl_my_unexec(aTHX)
#   define newATTRSUB_x(a,b,c,d,e,f)            Perl_newATTRSUB_x(aTHX_ a,b,c,d,e,f)
#   define newSTUB(a,b)                         Perl_newSTUB(aTHX_ a,b)
#   define newSVavdefelem(a,b,c)                Perl_newSVavdefelem(aTHX_ a,b,c)
#   define newXS_deffile(a,b)                   Perl_newXS_deffile(aTHX_ a,b)
#   define newXS_len_flags(a,b,c,d,e,f,g)       Perl_newXS_len_flags(aTHX_ a,b,c,d,e,f,g)
#   define nextargv(a,b)                        Perl_nextargv(aTHX_ a,b)
#   define no_bareword_filehandle(a)            Perl_no_bareword_filehandle(aTHX_ a)
#   define noperl_die                           Perl_noperl_die
#   define notify_parser_that_changed_to_utf8() Perl_notify_parser_that_changed_to_utf8(aTHX)
#   define oopsAV(a)                            Perl_oopsAV(aTHX_ a)
#   define oopsHV(a)                            Perl_oopsHV(aTHX_ a)
#   define op_unscope(a)                        Perl_op_unscope(aTHX_ a)
#   define package(a)                           Perl_package(aTHX_ a)
#   define package_version(a)                   Perl_package_version(aTHX_ a)
#   define pad_add_weakref(a)                   Perl_pad_add_weakref(aTHX_ a)
#   define pad_block_start(a)                   Perl_pad_block_start(aTHX_ a)
#   define pad_fixup_inner_anons(a,b,c)         Perl_pad_fixup_inner_anons(aTHX_ a,b,c)
#   define pad_free(a)                          Perl_pad_free(aTHX_ a)
#   define pad_leavemy()                        Perl_pad_leavemy(aTHX)
#   define pad_push(a,b)                        Perl_pad_push(aTHX_ a,b)
#   define pad_swipe(a,b)                       Perl_pad_swipe(aTHX_ a,b)
#   define padlist_store(a,b,c)                 Perl_padlist_store(aTHX_ a,b,c)
#   define parse_unicode_opts(a)                Perl_parse_unicode_opts(aTHX_ a)
#   define parser_free(a)                       Perl_parser_free(aTHX_ a)
#   define peep(a)                              Perl_peep(aTHX_ a)
#   define pmruntime(a,b,c,d,e)                 Perl_pmruntime(aTHX_ a,b,c,d,e)
#   define re_op_compile(a,b,c,d,e,f,g,h)       Perl_re_op_compile(aTHX_ a,b,c,d,e,f,g,h)
#   define refcounted_he_chain_2hv(a,b)         Perl_refcounted_he_chain_2hv(aTHX_ a,b)
#   define refcounted_he_fetch_pv(a,b,c,d)      Perl_refcounted_he_fetch_pv(aTHX_ a,b,c,d)
#   define refcounted_he_fetch_pvn(a,b,c,d,e)   Perl_refcounted_he_fetch_pvn(aTHX_ a,b,c,d,e)
#   define refcounted_he_fetch_sv(a,b,c,d)      Perl_refcounted_he_fetch_sv(aTHX_ a,b,c,d)
#   define refcounted_he_free(a)                Perl_refcounted_he_free(aTHX_ a)
#   define refcounted_he_inc(a)                 Perl_refcounted_he_inc(aTHX_ a)
#   define refcounted_he_new_pv(a,b,c,d,e)      Perl_refcounted_he_new_pv(aTHX_ a,b,c,d,e)
#   define refcounted_he_new_pvn(a,b,c,d,e,f)   Perl_refcounted_he_new_pvn(aTHX_ a,b,c,d,e,f)
#   define refcounted_he_new_sv(a,b,c,d,e)      Perl_refcounted_he_new_sv(aTHX_ a,b,c,d,e)
#   define report_evil_fh(a)                    Perl_report_evil_fh(aTHX_ a)
#   define report_wrongway_fh(a,b)              Perl_report_wrongway_fh(aTHX_ a,b)
#   define rpeep(a)                             Perl_rpeep(aTHX_ a)
#   define rsignal_restore(a,b)                 Perl_rsignal_restore(aTHX_ a,b)
#   define rsignal_save(a,b,c)                  Perl_rsignal_save(aTHX_ a,b,c)
#   define rxres_save(a,b)                      Perl_rxres_save(aTHX_ a,b)
#   define save_strlen(a)                       Perl_save_strlen(aTHX_ a)
#   define sawparens(a)                         Perl_sawparens(aTHX_ a)
#   define scalar(a)                            Perl_scalar(aTHX_ a)
#   define scalarvoid(a)                        Perl_scalarvoid(aTHX_ a)
#   define set_caret_X()                        Perl_set_caret_X(aTHX)
#   define set_numeric_standard()               Perl_set_numeric_standard(aTHX)
#   define set_numeric_underlying()             Perl_set_numeric_underlying(aTHX)
#   define setfd_cloexec                        Perl_setfd_cloexec
#   define setfd_cloexec_for_nonsysfd(a)        Perl_setfd_cloexec_for_nonsysfd(aTHX_ a)
#   define setfd_cloexec_or_inhexec_by_sysfdness(a) Perl_setfd_cloexec_or_inhexec_by_sysfdness(aTHX_ a)
#   define setfd_inhexec                        Perl_setfd_inhexec
#   define setfd_inhexec_for_sysfd(a)           Perl_setfd_inhexec_for_sysfd(aTHX_ a)
#   define sighandler1                          Perl_sighandler1
#   define sighandler3                          Perl_sighandler3
#   define sub_crush_depth(a)                   Perl_sub_crush_depth(aTHX_ a)
#   define sv_2num(a)                           Perl_sv_2num(aTHX_ a)
#   define sv_clean_all()                       Perl_sv_clean_all(aTHX)
#   define sv_clean_objs()                      Perl_sv_clean_objs(aTHX)
#   define sv_del_backref(a,b)                  Perl_sv_del_backref(aTHX_ a,b)
#   define sv_free_arenas()                     Perl_sv_free_arenas(aTHX)
#   define sv_pvbyten_force_wrapper(a,b,c)      Perl_sv_pvbyten_force_wrapper(aTHX_ a,b,c)
#   define sv_pvutf8n_force_wrapper(a,b,c)      Perl_sv_pvutf8n_force_wrapper(aTHX_ a,b,c)
#   define sv_resetpvn(a,b,c)                   Perl_sv_resetpvn(aTHX_ a,b,c)
#   define sv_sethek(a,b)                       Perl_sv_sethek(aTHX_ a,b)
#   define tmps_grow_p(a)                       Perl_tmps_grow_p(aTHX_ a)
#   define utilize(a,b,c,d,e)                   Perl_utilize(aTHX_ a,b,c,d,e)
#   define vivify_ref(a,b)                      Perl_vivify_ref(aTHX_ a,b)
#   define wait4pid(a,b,c)                      Perl_wait4pid(aTHX_ a,b,c)
#   define watch(a)                             Perl_watch(aTHX_ a)
#   define write_to_stderr(a)                   Perl_write_to_stderr(aTHX_ a)
#   define xs_boot_epilog(a)                    Perl_xs_boot_epilog(aTHX_ a)
#   define yyerror(a)                           Perl_yyerror(aTHX_ a)
#   define yyerror_pv(a,b)                      Perl_yyerror_pv(aTHX_ a,b)
#   define yyerror_pvn(a,b,c)                   Perl_yyerror_pvn(aTHX_ a,b,c)
#   define yyparse(a)                           Perl_yyparse(aTHX_ a)
#   define yyquit()                             Perl_yyquit(aTHX)
#   define yyunlex()                            Perl_yyunlex(aTHX)
#   define opslab_force_free(a)                 Perl_opslab_force_free(aTHX_ a)
#   define opslab_free(a)                       Perl_opslab_free(aTHX_ a)
#   define opslab_free_nopad(a)                 Perl_opslab_free_nopad(aTHX_ a)
#   define parser_free_nexttoke_ops(a,b)        Perl_parser_free_nexttoke_ops(aTHX_ a,b)
#   define should_warn_nl                       S_should_warn_nl
#   if defined(DEBUGGING)
#     define get_debug_opts(a,b)                Perl_get_debug_opts(aTHX_ a,b)
#     define set_padlist                        Perl_set_padlist
#   endif
#   if defined(DEBUG_LEAKING_SCALARS_FORK_DUMP)
#     define dump_sv_child(a)                   Perl_dump_sv_child(aTHX_ a)
#   endif
#   if !defined(HAS_GETENV_LEN)
#     define getenv_len(a,b)                    Perl_getenv_len(aTHX_ a,b)
#   endif
#   if defined(HAS_MSG) || defined(HAS_SEM) || defined(HAS_SHM)
#     define do_ipcctl(a,b,c)                   Perl_do_ipcctl(aTHX_ a,b,c)
#     define do_ipcget(a,b,c)                   Perl_do_ipcget(aTHX_ a,b,c)
#     define do_msgrcv(a,b)                     Perl_do_msgrcv(aTHX_ a,b)
#     define do_msgsnd(a,b)                     Perl_do_msgsnd(aTHX_ a,b)
#     define do_semop(a,b)                      Perl_do_semop(aTHX_ a,b)
#     define do_shmio(a,b,c)                    Perl_do_shmio(aTHX_ a,b,c)
#   endif
#   if defined(HAS_PIPE)
#     define PerlProc_pipe_cloexec(a)           Perl_PerlProc_pipe_cloexec(aTHX_ a)
#   endif
#   if !defined(HAS_RENAME)
#     define same_dirent(a,b)                   Perl_same_dirent(aTHX_ a,b)
#   endif
#   if defined(HAS_SOCKET)
#     define PerlSock_accept_cloexec(a,b,c)     Perl_PerlSock_accept_cloexec(aTHX_ a,b,c)
#     define PerlSock_socket_cloexec(a,b,c)     Perl_PerlSock_socket_cloexec(aTHX_ a,b,c)
#   endif
#   if   defined(HAS_SOCKETPAIR) ||                                     \
       ( defined(AF_INET) && defined(HAS_SOCKET) && defined(PF_INET) && \
         defined(SOCK_DGRAM) )
#     define PerlSock_socketpair_cloexec(a,b,c,d) Perl_PerlSock_socketpair_cloexec(aTHX_ a,b,c,d)
#   endif
#   if defined(_MSC_VER)
#     define magic_regdatum_set(a,b)            Perl_magic_regdatum_set(aTHX_ a,b)
#   else
#     define magic_regdatum_set(a,b)            Perl_magic_regdatum_set(aTHX_ a,b)
#   endif
#   if !defined(MULTIPLICITY) || defined(PERL_CORE)
#     define tied_method(a,b,c,d,e,...)         Perl_tied_method(aTHX_ a,b,c,d,e,__VA_ARGS__)
#     if defined(PERL_IN_REGCOMP_C)
#       define re_croak(a,...)                  S_re_croak(aTHX_ a,__VA_ARGS__)
#     endif
#   endif
#   if defined(PERL_DEBUG_READONLY_COW)
#     define sv_buf_to_ro(a)                    Perl_sv_buf_to_ro(aTHX_ a)
#   endif
#   if defined(PERL_DEBUG_READONLY_OPS)
#     define Slab_to_ro(a)                      Perl_Slab_to_ro(aTHX_ a)
#     define Slab_to_rw(a)                      Perl_Slab_to_rw(aTHX_ a)
#   endif
#   if !defined(PERL_DEFAULT_DO_EXEC3_IMPLEMENTATION)
#     define do_exec(a)                         Perl_do_exec(aTHX_ a)
#   endif
#   if defined(PERL_IN_AV_C)
#     define get_aux_mg(a)                      S_get_aux_mg(aTHX_ a)
#   endif
#   if defined(PERL_IN_DEB_C)
#     define deb_stack_n(a,b,c,d,e)             S_deb_stack_n(aTHX_ a,b,c,d,e)
#   endif
#   if defined(PERL_IN_DOIO_C)
#     define argvout_final(a,b,c)               S_argvout_final(aTHX_ a,b,c)
#     define exec_failed(a,b,c)                 S_exec_failed(aTHX_ a,b,c)
#     define ingroup(a,b)                       S_ingroup(aTHX_ a,b)
#     define openn_cleanup(a,b,c,d,e,f,g,h,i,j,k,l,m) S_openn_cleanup(aTHX_ a,b,c,d,e,f,g,h,i,j,k,l,m)
#     define openn_setup(a,b,c,d,e,f)           S_openn_setup(aTHX_ a,b,c,d,e,f)
#   endif
#   if defined(PERL_IN_DOOP_C)
#     define do_trans_complex(a,b)              S_do_trans_complex(aTHX_ a,b)
#     define do_trans_count(a,b)                S_do_trans_count(aTHX_ a,b)
#     define do_trans_count_invmap(a,b)         S_do_trans_count_invmap(aTHX_ a,b)
#     define do_trans_invmap(a,b)               S_do_trans_invmap(aTHX_ a,b)
#     define do_trans_simple(a,b)               S_do_trans_simple(aTHX_ a,b)
#   endif
#   if defined(PERL_IN_DUMP_C)
#     define deb_curcv(a)                       S_deb_curcv(aTHX_ a)
#     define debprof(a)                         S_debprof(aTHX_ a)
#     define pm_description(a)                  S_pm_description(aTHX_ a)
#     define sequence_num(a)                    S_sequence_num(aTHX_ a)
#   endif
#   if defined(PERL_IN_GLOBALS_C) || defined(PERL_IN_OP_C) || \
       defined(PERL_IN_PEEP_C)
#     define ck_anoncode(a)                     Perl_ck_anoncode(aTHX_ a)
#     define ck_backtick(a)                     Perl_ck_backtick(aTHX_ a)
#     define ck_bitop(a)                        Perl_ck_bitop(aTHX_ a)
#     define ck_cmp(a)                          Perl_ck_cmp(aTHX_ a)
#     define ck_concat(a)                       Perl_ck_concat(aTHX_ a)
#     define ck_defined(a)                      Perl_ck_defined(aTHX_ a)
#     define ck_delete(a)                       Perl_ck_delete(aTHX_ a)
#     define ck_each(a)                         Perl_ck_each(aTHX_ a)
#     define ck_eof(a)                          Perl_ck_eof(aTHX_ a)
#     define ck_eval(a)                         Perl_ck_eval(aTHX_ a)
#     define ck_exec(a)                         Perl_ck_exec(aTHX_ a)
#     define ck_exists(a)                       Perl_ck_exists(aTHX_ a)
#     define ck_ftst(a)                         Perl_ck_ftst(aTHX_ a)
#     define ck_fun(a)                          Perl_ck_fun(aTHX_ a)
#     define ck_glob(a)                         Perl_ck_glob(aTHX_ a)
#     define ck_grep(a)                         Perl_ck_grep(aTHX_ a)
#     define ck_helemexistsor(a)                Perl_ck_helemexistsor(aTHX_ a)
#     define ck_index(a)                        Perl_ck_index(aTHX_ a)
#     define ck_isa(a)                          Perl_ck_isa(aTHX_ a)
#     define ck_join(a)                         Perl_ck_join(aTHX_ a)
#     define ck_length(a)                       Perl_ck_length(aTHX_ a)
#     define ck_lfun(a)                         Perl_ck_lfun(aTHX_ a)
#     define ck_listiob(a)                      Perl_ck_listiob(aTHX_ a)
#     define ck_match(a)                        Perl_ck_match(aTHX_ a)
#     define ck_method(a)                       Perl_ck_method(aTHX_ a)
#     define ck_null(a)                         Perl_ck_null(aTHX_ a)
#     define ck_open(a)                         Perl_ck_open(aTHX_ a)
#     define ck_prototype(a)                    Perl_ck_prototype(aTHX_ a)
#     define ck_readline(a)                     Perl_ck_readline(aTHX_ a)
#     define ck_refassign(a)                    Perl_ck_refassign(aTHX_ a)
#     define ck_repeat(a)                       Perl_ck_repeat(aTHX_ a)
#     define ck_require(a)                      Perl_ck_require(aTHX_ a)
#     define ck_return(a)                       Perl_ck_return(aTHX_ a)
#     define ck_rfun(a)                         Perl_ck_rfun(aTHX_ a)
#     define ck_rvconst(a)                      Perl_ck_rvconst(aTHX_ a)
#     define ck_sassign(a)                      Perl_ck_sassign(aTHX_ a)
#     define ck_select(a)                       Perl_ck_select(aTHX_ a)
#     define ck_shift(a)                        Perl_ck_shift(aTHX_ a)
#     define ck_smartmatch(a)                   Perl_ck_smartmatch(aTHX_ a)
#     define ck_sort(a)                         Perl_ck_sort(aTHX_ a)
#     define ck_spair(a)                        Perl_ck_spair(aTHX_ a)
#     define ck_split(a)                        Perl_ck_split(aTHX_ a)
#     define ck_stringify(a)                    Perl_ck_stringify(aTHX_ a)
#     define ck_subr(a)                         Perl_ck_subr(aTHX_ a)
#     define ck_substr(a)                       Perl_ck_substr(aTHX_ a)
#     define ck_svconst(a)                      Perl_ck_svconst(aTHX_ a)
#     define ck_tell(a)                         Perl_ck_tell(aTHX_ a)
#     define ck_trunc(a)                        Perl_ck_trunc(aTHX_ a)
#     define ck_trycatch(a)                     Perl_ck_trycatch(aTHX_ a)
#   endif /* defined(PERL_IN_GLOBALS_C) || defined(PERL_IN_OP_C) ||
             defined(PERL_IN_PEEP_C) */
#   if defined(PERL_IN_GV_C)
#     define find_default_stash(a,b,c,d,e,f)    S_find_default_stash(aTHX_ a,b,c,d,e,f)
#     define gv_fetchmeth_internal(a,b,c,d,e,f) S_gv_fetchmeth_internal(aTHX_ a,b,c,d,e,f)
#     define gv_init_svtype(a,b)                S_gv_init_svtype(aTHX_ a,b)
#     define gv_is_in_main(a,b,c)               S_gv_is_in_main(aTHX_ a,b,c)
#     define gv_magicalize(a,b,c,d,e)           S_gv_magicalize(aTHX_ a,b,c,d,e)
#     define gv_magicalize_isa(a)               S_gv_magicalize_isa(aTHX_ a)
#     define gv_stashpvn_internal(a,b,c)        S_gv_stashpvn_internal(aTHX_ a,b,c)
#     define maybe_multimagic_gv(a,b,c)         S_maybe_multimagic_gv(aTHX_ a,b,c)
#     define parse_gv_stash_name(a,b,c,d,e,f,g,h) S_parse_gv_stash_name(aTHX_ a,b,c,d,e,f,g,h)
#     define require_tie_mod(a,b,c,d,e)         S_require_tie_mod(aTHX_ a,b,c,d,e)
#   endif /* defined(PERL_IN_GV_C) */
#   if defined(PERL_IN_HV_C)
#     define clear_placeholders(a,b)            S_clear_placeholders(aTHX_ a,b)
#     define hsplit(a,b,c)                      S_hsplit(aTHX_ a,b,c)
#     define hv_auxinit(a)                      S_hv_auxinit(aTHX_ a)
#     define hv_delete_common(a,b,c,d,e,f,g)    S_hv_delete_common(aTHX_ a,b,c,d,e,f,g)
#     define hv_free_ent_ret(a)                 S_hv_free_ent_ret(aTHX_ a)
#     define hv_free_entries(a)                 S_hv_free_entries(aTHX_ a)
#     define hv_magic_check                     S_hv_magic_check
#     define hv_notallowed(a,b,c,d)             S_hv_notallowed(aTHX_ a,b,c,d)
#     define refcounted_he_value(a)             S_refcounted_he_value(aTHX_ a)
#     define save_hek_flags                     S_save_hek_flags
#     define share_hek_flags(a,b,c,d)           S_share_hek_flags(aTHX_ a,b,c,d)
#     define unshare_hek_or_pvn(a,b,c,d)        S_unshare_hek_or_pvn(aTHX_ a,b,c,d)
#     if !defined(PURIFY)
#       define new_he()                         S_new_he(aTHX)
#     endif
#   endif /* defined(PERL_IN_HV_C) */
#   if defined(PERL_IN_LOCALE_C)
#     define get_locale_string_utf8ness_i(a,b,c,d) S_get_locale_string_utf8ness_i(aTHX_ a,b,c,d)
#     define is_locale_utf8(a)                  S_is_locale_utf8(aTHX_ a)
#     if defined(HAS_LOCALECONV)
#       define my_localeconv(a)                 S_my_localeconv(aTHX_ a)
#       define populate_hash_from_localeconv(a,b,c,d,e) S_populate_hash_from_localeconv(aTHX_ a,b,c,d,e)
#     endif
#     if defined(USE_LOCALE)
#       define get_category_index               S_get_category_index
#       define get_category_index_nowarn        S_get_category_index_nowarn
#       define mortalized_pv_copy(a)            S_mortalized_pv_copy(aTHX_ a)
#       define new_LC_ALL(a,b)                  S_new_LC_ALL(aTHX_ a,b)
#       define save_to_buffer                   S_save_to_buffer
#       define setlocale_failure_panic_i(a,b,c,d,e) S_setlocale_failure_panic_i(aTHX_ a,b,c,d,e)
#       define stdize_locale(a,b,c,d,e)         S_stdize_locale(aTHX_ a,b,c,d,e)
#       if defined(DEBUGGING)
#         define my_setlocale_debug_string_i(a,b,c,d) S_my_setlocale_debug_string_i(aTHX_ a,b,c,d)
#       endif
#       if defined(HAS_NL_LANGINFO) || defined(HAS_NL_LANGINFO_L)
#         define my_langinfo_i(a,b,c,d,e,f)     S_my_langinfo_i(aTHX_ a,b,c,d,e,f)
#       else
#         define my_langinfo_i(a,b,c,d,e,f)     S_my_langinfo_i(aTHX_ a,b,c,d,e,f)
#       endif
#       if defined(USE_LOCALE_COLLATE)
#         define new_collate(a,b)               S_new_collate(aTHX_ a,b)
#         if defined(DEBUGGING)
#           define print_collxfrm_input_and_return(a,b,c,d,e) S_print_collxfrm_input_and_return(aTHX_ a,b,c,d,e)
#         endif
#       endif
#       if defined(USE_LOCALE_CTYPE)
#         define is_codeset_name_UTF8           S_is_codeset_name_UTF8
#         define new_ctype(a,b)                 S_new_ctype(aTHX_ a,b)
#       endif
#       if defined(USE_LOCALE_NUMERIC)
#         define new_numeric(a,b)               S_new_numeric(aTHX_ a,b)
#       endif
#       if defined(USE_PERL_SWITCH_LOCALE_CONTEXT) || defined(DEBUGGING)
#         define get_LC_ALL_display()           S_get_LC_ALL_display(aTHX)
#       endif
#       if defined(USE_POSIX_2008_LOCALE)
#         define emulate_setlocale_i(a,b,c,d)   S_emulate_setlocale_i(aTHX_ a,b,c,d)
#         define my_querylocale_i(a)            S_my_querylocale_i(aTHX_ a)
#         define setlocale_from_aggregate_LC_ALL(a,b) S_setlocale_from_aggregate_LC_ALL(aTHX_ a,b)
#         define use_curlocale_scratch()        S_use_curlocale_scratch(aTHX)
#         if defined(USE_QUERYLOCALE)
#           define calculate_LC_ALL(a)          S_calculate_LC_ALL(aTHX_ a)
#         else
#           define update_PL_curlocales_i(a,b,c) S_update_PL_curlocales_i(aTHX_ a,b,c)
#         endif
#       elif  defined(USE_LOCALE_THREADS) &&                  \
             !defined(USE_THREAD_SAFE_LOCALE) &&              \
             !defined(USE_THREAD_SAFE_LOCALE_EMULATION) /* &&
             !defined(USE_POSIX_2008_LOCALE) */
#         define less_dicey_setlocale_r(a,b)    S_less_dicey_setlocale_r(aTHX_ a,b)
#         define less_dicey_void_setlocale_i(a,b,c) S_less_dicey_void_setlocale_i(aTHX_ a,b,c)
#         if 0
#           define less_dicey_bool_setlocale_r(a,b) S_less_dicey_bool_setlocale_r(aTHX_ a,b)
#         endif
#       endif
#       if !(  defined(USE_POSIX_2008_LOCALE) && defined(USE_QUERYLOCALE) ) && \
            ( !defined(LC_ALL) || defined(USE_POSIX_2008_LOCALE) ||            \
               defined(WIN32) )
#         define calculate_LC_ALL(a)            S_calculate_LC_ALL(aTHX_ a)
#       endif
#       if defined(WIN32)
#         define Win_byte_string_to_wstring     S_Win_byte_string_to_wstring
#         define Win_wstring_to_byte_string     S_Win_wstring_to_byte_string
#         define win32_setlocale(a,b)           S_win32_setlocale(aTHX_ a,b)
#         define wrap_wsetlocale(a,b)           S_wrap_wsetlocale(aTHX_ a,b)
#       endif
#       if   defined(WIN32) || \
           ( defined(USE_POSIX_2008_LOCALE) && !defined(USE_QUERYLOCALE) )
#         define find_locale_from_environment(a) S_find_locale_from_environment(aTHX_ a)
#       endif
#     endif /* defined(USE_LOCALE) */
#     if defined(USE_POSIX_2008_LOCALE) || defined(DEBUGGING)
#       define get_displayable_string(a,b,c)    S_get_displayable_string(aTHX_ a,b,c)
#     endif
#   endif /* defined(PERL_IN_LOCALE_C) */
#   if defined(PERL_IN_MALLOC_C)
#     define adjust_size_and_find_bucket        S_adjust_size_and_find_bucket
#   endif
#   if defined(PERL_IN_MG_C)
#     define fixup_errno_string(a)              S_fixup_errno_string(aTHX_ a)
#     define magic_methcall1(a,b,c,d,e,f)       S_magic_methcall1(aTHX_ a,b,c,d,e,f)
#     define magic_methpack(a,b,c)              S_magic_methpack(aTHX_ a,b,c)
#     define restore_magic(a)                   S_restore_magic(aTHX_ a)
#     define save_magic_flags(a,b,c)            S_save_magic_flags(aTHX_ a,b,c)
#     define unwind_handler_stack(a)            S_unwind_handler_stack(aTHX_ a)
#   endif
#   if defined(PERL_IN_MG_C) || defined(PERL_IN_PP_C)
#     define translate_substr_offsets           Perl_translate_substr_offsets
#   endif
#   if defined(PERL_IN_MRO_C)
#     define mro_clean_isarev(a,b,c,d,e,f)      S_mro_clean_isarev(aTHX_ a,b,c,d,e,f)
#     define mro_gather_and_rename(a,b,c,d,e)   S_mro_gather_and_rename(aTHX_ a,b,c,d,e)
#     define mro_get_linear_isa_dfs(a,b)        S_mro_get_linear_isa_dfs(aTHX_ a,b)
#   endif
#   if defined(PERL_IN_NUMERIC_C)
#     define output_non_portable(a)             S_output_non_portable(aTHX_ a)
#   endif
#   if defined(PERL_IN_OP_C)
#     define apply_attrs(a,b,c)                 S_apply_attrs(aTHX_ a,b,c)
#     define apply_attrs_my(a,b,c,d)            S_apply_attrs_my(aTHX_ a,b,c,d)
#     define assignment_type(a)                 S_assignment_type(aTHX_ a)
#     define bad_type_gv(a,b,c,d)               S_bad_type_gv(aTHX_ a,b,c,d)
#     define bad_type_pv(a,b,c,d)               S_bad_type_pv(aTHX_ a,b,c,d)
#     define clear_special_blocks(a,b,c)        S_clear_special_blocks(aTHX_ a,b,c)
#     define cop_free(a)                        S_cop_free(aTHX_ a)
#     define dup_attrlist(a)                    S_dup_attrlist(aTHX_ a)
#     define find_and_forget_pmops(a)           S_find_and_forget_pmops(aTHX_ a)
#     define fold_constants(a)                  S_fold_constants(aTHX_ a)
#     define force_list(a,b)                    S_force_list(aTHX_ a,b)
#     define forget_pmop(a)                     S_forget_pmop(aTHX_ a)
#     define gen_constant_list(a)               S_gen_constant_list(aTHX_ a)
#     define inplace_aassign(a)                 S_inplace_aassign(aTHX_ a)
#     define is_handle_constructor              S_is_handle_constructor
#     define listkids(a)                        S_listkids(aTHX_ a)
#     define looks_like_bool(a)                 S_looks_like_bool(aTHX_ a)
#     define modkids(a,b)                       S_modkids(aTHX_ a,b)
#     define move_proto_attr(a,b,c,d)           S_move_proto_attr(aTHX_ a,b,c,d)
#     define my_kid(a,b,c)                      S_my_kid(aTHX_ a,b,c)
#     define newGIVWHENOP(a,b,c,d,e)            S_newGIVWHENOP(aTHX_ a,b,c,d,e)
#     define newMETHOP_internal(a,b,c,d)        S_newMETHOP_internal(aTHX_ a,b,c,d)
#     define new_logop(a,b,c,d)                 S_new_logop(aTHX_ a,b,c,d)
#     define no_fh_allowed(a)                   S_no_fh_allowed(aTHX_ a)
#     define op_integerize(a)                   S_op_integerize(aTHX_ a)
#     define op_std_init(a)                     S_op_std_init(aTHX_ a)
#     define pmtrans(a,b,c)                     S_pmtrans(aTHX_ a,b,c)
#     define process_special_blocks(a,b,c,d)    S_process_special_blocks(aTHX_ a,b,c,d)
#     define ref_array_or_hash(a)               S_ref_array_or_hash(aTHX_ a)
#     define refkids(a,b)                       S_refkids(aTHX_ a,b)
#     define scalar_mod_type                    S_scalar_mod_type
#     define scalarboolean(a)                   S_scalarboolean(aTHX_ a)
#     define scalarkids(a)                      S_scalarkids(aTHX_ a)
#     define search_const(a)                    S_search_const(aTHX_ a)
#     define simplify_sort(a)                   S_simplify_sort(aTHX_ a)
#     define too_few_arguments_pv(a,b,c)        S_too_few_arguments_pv(aTHX_ a,b,c)
#     define too_many_arguments_pv(a,b,c)       S_too_many_arguments_pv(aTHX_ a,b,c)
#     define voidnonfinal(a)                    S_voidnonfinal(aTHX_ a)
#   endif /* defined(PERL_IN_OP_C) */
#   if defined(PERL_IN_OP_C) || defined(PERL_IN_PAD_C)
#     define PadnameIN_SCOPE                    S_PadnameIN_SCOPE
#   endif
#   if defined(PERL_IN_OP_C) || defined(PERL_IN_PEEP_C)
#     define check_hash_fields_and_hekify(a,b,c) Perl_check_hash_fields_and_hekify(aTHX_ a,b,c)
#     define no_bareword_allowed(a)             Perl_no_bareword_allowed(aTHX_ a)
#     define op_prune_chain_head                Perl_op_prune_chain_head
#     define op_varname(a)                      Perl_op_varname(aTHX_ a)
#     define warn_elem_scalar_context(a,b,c,d)  Perl_warn_elem_scalar_context(aTHX_ a,b,c,d)
#   endif
#   if defined(PERL_IN_OP_C) || defined(PERL_IN_SV_C)
#     define report_redefined_cv(a,b,c)         Perl_report_redefined_cv(aTHX_ a,b,c)
#     define varname(a,b,c,d,e,f)               Perl_varname(aTHX_ a,b,c,d,e,f)
#   endif
#   if defined(PERL_IN_PAD_C)
#     define pad_alloc_name(a,b,c,d)            S_pad_alloc_name(aTHX_ a,b,c,d)
#     define pad_check_dup(a,b,c)               S_pad_check_dup(aTHX_ a,b,c)
#     define pad_findlex(a,b,c,d,e,f,g,h,i)     S_pad_findlex(aTHX_ a,b,c,d,e,f,g,h,i)
#     define pad_reset()                        S_pad_reset(aTHX)
#     if defined(DEBUGGING)
#       define cv_dump(a,b)                     S_cv_dump(aTHX_ a,b)
#     endif
#   endif
#   if defined(PERL_IN_PEEP_C)
#     define finalize_op(a)                     S_finalize_op(aTHX_ a)
#     define optimize_op(a)                     S_optimize_op(aTHX_ a)
#     define traverse_op_tree(a,b)              S_traverse_op_tree(aTHX_ a,b)
#   endif
#   if defined(PERL_IN_PERL_C)
#     define find_beginning(a,b)                S_find_beginning(aTHX_ a,b)
#     define forbid_setid(a,b)                  S_forbid_setid(aTHX_ a,b)
#     define incpush(a,b,c)                     S_incpush(aTHX_ a,b,c)
#     define incpush_use_sep(a,b,c)             S_incpush_use_sep(aTHX_ a,b,c)
#     define init_ids()                         S_init_ids(aTHX)
#     define init_interp()                      S_init_interp(aTHX)
#     define init_main_stash()                  S_init_main_stash(aTHX)
#     define init_perllib()                     S_init_perllib(aTHX)
#     define init_postdump_symbols(a,b,c)       S_init_postdump_symbols(aTHX_ a,b,c)
#     define init_predump_symbols()             S_init_predump_symbols(aTHX)
#     define mayberelocate(a,b,c)               S_mayberelocate(aTHX_ a,b,c)
#     define minus_v()                          S_minus_v(aTHX)
#     define my_exit_jump()                     S_my_exit_jump(aTHX)
#     define nuke_stacks()                      S_nuke_stacks(aTHX)
#     define open_script(a,b,c)                 S_open_script(aTHX_ a,b,c)
#     define parse_body(a,b)                    S_parse_body(aTHX_ a,b)
#     define run_body(a)                        S_run_body(aTHX_ a)
#     define usage()                            S_usage(aTHX)
#     if !defined(PERL_IS_MINIPERL)
#       define incpush_if_exists(a,b,c)         S_incpush_if_exists(aTHX_ a,b,c)
#     endif
#   endif /* defined(PERL_IN_PERL_C) */
#   if defined(PERL_IN_PP_C)
#     define do_chomp(a,b,c)                    S_do_chomp(aTHX_ a,b,c)
#     define do_delete_local()                  S_do_delete_local(aTHX)
#     define refto(a)                           S_refto(aTHX_ a)
#   endif
#   if defined(PERL_IN_PP_C) || defined(PERL_IN_PP_HOT_C)
#     define lossless_NV_to_IV                  S_lossless_NV_to_IV
#   endif
#   if defined(PERL_IN_PP_C) || defined(PERL_IN_UTF8_C)
#     define _to_upper_title_latin1(a,b,c,d)    Perl__to_upper_title_latin1(aTHX_ a,b,c,d)
#   endif
#   if defined(PERL_IN_PP_CTL_C)
#     define check_type_and_open(a)             S_check_type_and_open(aTHX_ a)
#     define destroy_matcher(a)                 S_destroy_matcher(aTHX_ a)
#     define do_smartmatch(a,b,c)               S_do_smartmatch(aTHX_ a,b,c)
#     define docatch(a)                         S_docatch(aTHX_ a)
#     define doeval_compile(a,b,c,d)            S_doeval_compile(aTHX_ a,b,c,d)
#     define dofindlabel(a,b,c,d,e,f)           S_dofindlabel(aTHX_ a,b,c,d,e,f)
#     define doparseform(a)                     S_doparseform(aTHX_ a)
#     define dopoptoeval(a)                     S_dopoptoeval(aTHX_ a)
#     define dopoptogivenfor(a)                 S_dopoptogivenfor(aTHX_ a)
#     define dopoptolabel(a,b,c)                S_dopoptolabel(aTHX_ a,b,c)
#     define dopoptoloop(a)                     S_dopoptoloop(aTHX_ a)
#     define dopoptosub_at(a,b)                 S_dopoptosub_at(aTHX_ a,b)
#     define dopoptowhen(a)                     S_dopoptowhen(aTHX_ a)
#     define make_matcher(a)                    S_make_matcher(aTHX_ a)
#     define matcher_matches_sv(a,b)            S_matcher_matches_sv(aTHX_ a,b)
#     define num_overflow                       S_num_overflow
#     define path_is_searchable                 S_path_is_searchable
#     define run_user_filter(a,b,c)             S_run_user_filter(aTHX_ a,b,c)
#     define rxres_free(a)                      S_rxres_free(aTHX_ a)
#     define rxres_restore(a,b)                 S_rxres_restore(aTHX_ a,b)
#     define save_lines(a,b)                    S_save_lines(aTHX_ a,b)
#     if !defined(PERL_DISABLE_PMC)
#       define doopen_pm(a)                     S_doopen_pm(aTHX_ a)
#     endif
#   endif /* defined(PERL_IN_PP_CTL_C) */
#   if defined(PERL_IN_PP_CTL_C) || defined(PERL_IN_UTIL_C)
#     define invoke_exception_hook(a,b)         Perl_invoke_exception_hook(aTHX_ a,b)
#   endif
#   if defined(PERL_IN_PP_HOT_C)
#     define do_oddball(a,b)                    S_do_oddball(aTHX_ a,b)
#     define opmethod_stash(a)                  S_opmethod_stash(aTHX_ a)
#     define should_we_output_Debug_r(a)        S_should_we_output_Debug_r(aTHX_ a)
#   endif
#   if defined(PERL_IN_PP_PACK_C)
#     define div128(a,b)                        S_div128(aTHX_ a,b)
#     define first_symbol                       S_first_symbol
#     define get_num(a,b)                       S_get_num(aTHX_ a,b)
#     define group_end(a,b,c)                   S_group_end(aTHX_ a,b,c)
#     define is_an_int(a,b)                     S_is_an_int(aTHX_ a,b)
#     define measure_struct(a)                  S_measure_struct(aTHX_ a)
#     define mul128(a,b)                        S_mul128(aTHX_ a,b)
#     define my_bytes_to_utf8                   S_my_bytes_to_utf8
#     define need_utf8                          S_need_utf8
#     define next_symbol(a)                     S_next_symbol(aTHX_ a)
#     define pack_rec(a,b,c,d)                  S_pack_rec(aTHX_ a,b,c,d)
#     define sv_exp_grow(a,b)                   S_sv_exp_grow(aTHX_ a,b)
#     define unpack_rec(a,b,c,d,e)              S_unpack_rec(aTHX_ a,b,c,d,e)
#   endif /* defined(PERL_IN_PP_PACK_C) */
#   if defined(PERL_IN_PP_SORT_C)
#     define amagic_cmp(a,b)                    S_amagic_cmp(aTHX_ a,b)
#     define amagic_cmp_desc(a,b)               S_amagic_cmp_desc(aTHX_ a,b)
#     define amagic_i_ncmp(a,b)                 S_amagic_i_ncmp(aTHX_ a,b)
#     define amagic_i_ncmp_desc(a,b)            S_amagic_i_ncmp_desc(aTHX_ a,b)
#     define amagic_ncmp(a,b)                   S_amagic_ncmp(aTHX_ a,b)
#     define amagic_ncmp_desc(a,b)              S_amagic_ncmp_desc(aTHX_ a,b)
#     define cmp_desc(a,b)                      S_cmp_desc(aTHX_ a,b)
#     define sortcv(a,b)                        S_sortcv(aTHX_ a,b)
#     define sortcv_stacked(a,b)                S_sortcv_stacked(aTHX_ a,b)
#     define sortcv_xsub(a,b)                   S_sortcv_xsub(aTHX_ a,b)
#     define sortsv_flags_impl(a,b,c,d)         S_sortsv_flags_impl(aTHX_ a,b,c,d)
#     define sv_i_ncmp(a,b)                     S_sv_i_ncmp(aTHX_ a,b)
#     define sv_i_ncmp_desc(a,b)                S_sv_i_ncmp_desc(aTHX_ a,b)
#     define sv_ncmp(a,b)                       S_sv_ncmp(aTHX_ a,b)
#     define sv_ncmp_desc(a,b)                  S_sv_ncmp_desc(aTHX_ a,b)
#     if defined(USE_LOCALE_COLLATE)
#       define amagic_cmp_locale(a,b)           S_amagic_cmp_locale(aTHX_ a,b)
#       define amagic_cmp_locale_desc(a,b)      S_amagic_cmp_locale_desc(aTHX_ a,b)
#       define cmp_locale_desc(a,b)             S_cmp_locale_desc(aTHX_ a,b)
#     endif
#   endif /* defined(PERL_IN_PP_SORT_C) */
#   if defined(PERL_IN_PP_SYS_C)
#     define doform(a,b,c)                      S_doform(aTHX_ a,b,c)
#     define space_join_names_mortal(a)         S_space_join_names_mortal(aTHX_ a)
#     if !defined(HAS_MKDIR) || !defined(HAS_RMDIR)
#       define dooneliner(a,b)                  S_dooneliner(aTHX_ a,b)
#     endif
#   endif
#   if defined(PERL_IN_REGCOMP_INVLIST_C) && !defined(PERL_EXT_RE_BUILD)
#     define initialize_invlist_guts(a,b)       S_initialize_invlist_guts(aTHX_ a,b)
#   endif
#   if defined(PERL_IN_SCOPE_C)
#     define save_pushptri32ptr(a,b,c,d)        S_save_pushptri32ptr(aTHX_ a,b,c,d)
#     define save_scalar_at(a,b)                S_save_scalar_at(aTHX_ a,b)
#   endif
#   if defined(PERL_IN_TOKE_C)
#     define ao(a)                              S_ao(aTHX_ a)
#     define check_uni()                        S_check_uni(aTHX)
#     define checkcomma(a,b,c)                  S_checkcomma(aTHX_ a,b,c)
#     define filter_gets(a,b)                   S_filter_gets(aTHX_ a,b)
#     define find_in_my_stash(a,b)              S_find_in_my_stash(aTHX_ a,b)
#     define force_ident(a,b)                   S_force_ident(aTHX_ a,b)
#     define force_ident_maybe_lex(a)           S_force_ident_maybe_lex(aTHX_ a)
#     define force_next(a)                      S_force_next(aTHX_ a)
#     define force_strict_version(a)            S_force_strict_version(aTHX_ a)
#     define force_version(a,b)                 S_force_version(aTHX_ a,b)
#     define force_word(a,b,c,d)                S_force_word(aTHX_ a,b,c,d)
#     define get_and_check_backslash_N_name_wrapper(a,b) S_get_and_check_backslash_N_name_wrapper(aTHX_ a,b)
#     define incline(a,b)                       S_incline(aTHX_ a,b)
#     define intuit_method(a,b,c)               S_intuit_method(aTHX_ a,b,c)
#     define intuit_more(a,b)                   S_intuit_more(aTHX_ a,b)
#     define lop(a,b,c)                         S_lop(aTHX_ a,b,c)
#     define missingterm(a,b)                   S_missingterm(aTHX_ a,b)
#     define no_op(a,b)                         S_no_op(aTHX_ a,b)
#     define parse_ident(a,b,c,d,e,f,g)         S_parse_ident(aTHX_ a,b,c,d,e,f,g)
#     define pending_ident()                    S_pending_ident(aTHX)
#     define scan_const(a)                      S_scan_const(aTHX_ a)
#     define scan_formline(a)                   S_scan_formline(aTHX_ a)
#     define scan_heredoc(a)                    S_scan_heredoc(aTHX_ a)
#     define scan_ident(a,b,c,d)                S_scan_ident(aTHX_ a,b,c,d)
#     define scan_inputsymbol(a)                S_scan_inputsymbol(aTHX_ a)
#     define scan_pat(a,b)                      S_scan_pat(aTHX_ a,b)
#     define scan_subst(a)                      S_scan_subst(aTHX_ a)
#     define scan_trans(a)                      S_scan_trans(aTHX_ a)
#     define sublex_done()                      S_sublex_done(aTHX)
#     define sublex_push()                      S_sublex_push(aTHX)
#     define sublex_start()                     S_sublex_start(aTHX)
#     define swallow_bom(a)                     S_swallow_bom(aTHX_ a)
#     define tokenize_use(a,b)                  S_tokenize_use(aTHX_ a,b)
#     define tokeq(a)                           S_tokeq(aTHX_ a)
#     define update_debugger_info(a,b,c)        S_update_debugger_info(aTHX_ a,b,c)
#     define yywarn(a,b)                        S_yywarn(aTHX_ a,b)
#     if defined(DEBUGGING)
#       define printbuf(a,b)                    S_printbuf(aTHX_ a,b)
#       define tokereport(a,b)                  S_tokereport(aTHX_ a,b)
#     endif
#     if defined(PERL_CR_FILTER)
#       define cr_textfilter(a,b,c)             S_cr_textfilter(aTHX_ a,b,c)
#       define strip_return(a)                  S_strip_return(aTHX_ a)
#     endif
#     if !defined(PERL_NO_UTF16_FILTER)
#       define add_utf16_textfilter(a,b)        S_add_utf16_textfilter(aTHX_ a,b)
#       define utf16_textfilter(a,b,c)          S_utf16_textfilter(aTHX_ a,b,c)
#     endif
#   endif /* defined(PERL_IN_TOKE_C) */
#   if defined(PERL_IN_UNIVERSAL_C)
#     define isa_lookup(a,b,c,d,e)              S_isa_lookup(aTHX_ a,b,c,d,e)
#     define sv_derived_from_svpvn(a,b,c,d,e)   S_sv_derived_from_svpvn(aTHX_ a,b,c,d,e)
#   endif
#   if defined(PERL_IN_UTF8_C)
#     define _to_utf8_case(a,b,c,d,e,f,g,h,i)   S__to_utf8_case(aTHX_ a,b,c,d,e,f,g,h,i)
#     define check_locale_boundary_crossing(a,b,c,d) S_check_locale_boundary_crossing(aTHX_ a,b,c,d)
#     define does_utf8_overflow                 S_does_utf8_overflow
#     define isFF_overlong                      S_isFF_overlong
#     define is_utf8_common(a,b,c)              S_is_utf8_common(aTHX_ a,b,c)
#     define is_utf8_overlong                   S_is_utf8_overlong
#     define new_msg_hv(a,b,c)                  S_new_msg_hv(aTHX_ a,b,c)
#     define to_case_cp_list(a,b,c,d,e,f,g,h)   S_to_case_cp_list(aTHX_ a,b,c,d,e,f,g,h)
#     define to_lower_latin1                    S_to_lower_latin1
#     define turkic_fc(a,b,c,d)                 S_turkic_fc(aTHX_ a,b,c,d)
#     define turkic_lc(a,b,c,d)                 S_turkic_lc(aTHX_ a,b,c,d)
#     define turkic_uc(a,b,c,d)                 S_turkic_uc(aTHX_ a,b,c,d)
#     define unexpected_non_continuation_text(a,b,c,d) S_unexpected_non_continuation_text(aTHX_ a,b,c,d)
#     if 0
#       define warn_on_first_deprecated_use(a,b,c,d,e,f) S_warn_on_first_deprecated_use(aTHX_ a,b,c,d,e,f)
#     endif
#   endif /* defined(PERL_IN_UTF8_C) */
#   if defined(PERL_IN_UTIL_C)
#     define ckwarn_common(a)                   S_ckwarn_common(aTHX_ a)
#     define mess_alloc()                       S_mess_alloc(aTHX)
#     define ptr_hash                           S_ptr_hash
#     define with_queued_errors(a)              S_with_queued_errors(aTHX_ a)
#     if defined(PERL_MEM_LOG) && !defined(PERL_MEM_LOG_NOIMPL)
#       define mem_log_common                   S_mem_log_common
#     endif
#     if defined(PERL_USES_PL_PIDSTATUS)
#       define pidgone(a,b)                     S_pidgone(aTHX_ a,b)
#     endif
#   endif /* defined(PERL_IN_UTIL_C) */
#   if defined(PERL_USE_3ARG_SIGHANDLER)
#     define sighandler                         Perl_sighandler
#   else
#     define sighandler                         Perl_sighandler
#   endif
#   if defined(USE_C_BACKTRACE)
#     define get_c_backtrace(a,b)               Perl_get_c_backtrace(aTHX_ a,b)
#   endif
#   if defined(USE_ITHREADS)
#     define mro_meta_dup(a,b)                  Perl_mro_meta_dup(aTHX_ a,b)
#     define padlist_dup(a,b)                   Perl_padlist_dup(aTHX_ a,b)
#     define padname_dup(a,b)                   Perl_padname_dup(aTHX_ a,b)
#     define padnamelist_dup(a,b)               Perl_padnamelist_dup(aTHX_ a,b)
#     if !defined(PERL_IMPLICIT_SYS)
#       define PerlEnv_putenv(a)                S_PerlEnv_putenv(aTHX_ a)
#     endif
#     if defined(PERL_IN_OP_C) || defined(PERL_IN_PEEP_C)
#       define op_relocate_sv(a,b)              Perl_op_relocate_sv(aTHX_ a,b)
#     endif
#   endif /* defined(USE_ITHREADS) */
#   if defined(USE_LOCALE_COLLATE)
#     define magic_freecollxfrm(a,b)            Perl_magic_freecollxfrm(aTHX_ a,b)
#     define magic_setcollxfrm(a,b)             Perl_magic_setcollxfrm(aTHX_ a,b)
#   endif
#   if defined(USE_PERLIO)
#     define PerlIO_restore_errno(a)            Perl_PerlIO_restore_errno(aTHX_ a)
#     define PerlIO_save_errno(a)               Perl_PerlIO_save_errno(aTHX_ a)
#   endif
#   if defined(USE_QUADMATH)
#     define quadmath_format_needed             Perl_quadmath_format_needed
#     define quadmath_format_valid              Perl_quadmath_format_valid
#   endif
#   if defined(WIN32)
#     define get_win32_message_utf8ness(a)      Perl_get_win32_message_utf8ness(aTHX_ a)
#   else
#     define do_exec3(a,b,c)                    Perl_do_exec3(aTHX_ a,b,c)
#   endif
# endif /* defined(PERL_CORE) */
# if defined(PERL_CORE) || defined(PERL_EXT)
#   define _byte_dump_string(a,b,c)             Perl__byte_dump_string(aTHX_ a,b,c)
#   define _inverse_folds(a,b,c)                Perl__inverse_folds(aTHX_ a,b,c)
#   define append_utf8_from_native_byte         Perl_append_utf8_from_native_byte
#   define av_reify(a)                          Perl_av_reify(aTHX_ a)
#   define cntrl_to_mnemonic                    Perl_cntrl_to_mnemonic
#   define current_re_engine()                  Perl_current_re_engine(aTHX)
#   define cv_ckproto_len_flags(a,b,c,d,e)      Perl_cv_ckproto_len_flags(aTHX_ a,b,c,d,e)
#   define delimcpy_no_escape                   Perl_delimcpy_no_escape
#   define do_uniprop_match                     Perl_do_uniprop_match
#   define get_and_check_backslash_N_name(a,b,c,d) Perl_get_and_check_backslash_N_name(aTHX_ a,b,c,d)
#   define get_deprecated_property_msg          Perl_get_deprecated_property_msg
#   define get_prop_definition(a)               Perl_get_prop_definition(aTHX_ a)
#   define get_prop_values                      Perl_get_prop_values
#   define load_charnames(a,b,c,d)              Perl_load_charnames(aTHX_ a,b,c,d)
#   define mbtowc_(a,b,c)                       Perl_mbtowc_(aTHX_ a,b,c)
#   define mg_find_mglob(a)                     Perl_mg_find_mglob(aTHX_ a)
#   define multiconcat_stringify(a)             Perl_multiconcat_stringify(aTHX_ a)
#   define multideref_stringify(a,b)            Perl_multideref_stringify(aTHX_ a,b)
#   define my_strftime8_temp(a,b,c,d,e,f,g,h,i,j,k) Perl_my_strftime8_temp(aTHX_ a,b,c,d,e,f,g,h,i,j,k)
#   define op_clear(a)                          Perl_op_clear(aTHX_ a)
#   define qerror(a)                            Perl_qerror(aTHX_ a)
#   define reg_named_buff(a,b,c,d)              Perl_reg_named_buff(aTHX_ a,b,c,d)
#   define reg_named_buff_iter(a,b,c)           Perl_reg_named_buff_iter(aTHX_ a,b,c)
#   define reg_numbered_buff_fetch(a,b,c)       Perl_reg_numbered_buff_fetch(aTHX_ a,b,c)
#   define reg_numbered_buff_fetch_flags(a,b,c,d) Perl_reg_numbered_buff_fetch_flags(aTHX_ a,b,c,d)
#   define reg_numbered_buff_length(a,b,c)      Perl_reg_numbered_buff_length(aTHX_ a,b,c)
#   define reg_numbered_buff_store(a,b,c)       Perl_reg_numbered_buff_store(aTHX_ a,b,c)
#   define reg_qr_package(a)                    Perl_reg_qr_package(aTHX_ a)
#   define reg_temp_copy(a,b)                   Perl_reg_temp_copy(aTHX_ a,b)
#   define report_uninit(a)                     Perl_report_uninit(aTHX_ a)
#   define scan_str(a,b,c,d,e)                  Perl_scan_str(aTHX_ a,b,c,d,e)
#   define scan_word(a,b,c,d,e)                 Perl_scan_word(aTHX_ a,b,c,d,e)
#   define scan_word6(a,b,c,d,e,f)              Perl_scan_word6(aTHX_ a,b,c,d,e,f)
#   define skipspace_flags(a,b)                 Perl_skipspace_flags(aTHX_ a,b)
#   define sv_magicext_mglob(a)                 Perl_sv_magicext_mglob(aTHX_ a)
#   define sv_only_taint_gmagic                 Perl_sv_only_taint_gmagic
#   define utf16_to_utf8_base(a,b,c,d,e,f)      Perl_utf16_to_utf8_base(aTHX_ a,b,c,d,e,f)
#   define utf8_to_utf16_base(a,b,c,d,e,f)      Perl_utf8_to_utf16_base(aTHX_ a,b,c,d,e,f)
#   define validate_proto(a,b,c,d)              Perl_validate_proto(aTHX_ a,b,c,d)
#   define vivify_defelem(a)                    Perl_vivify_defelem(aTHX_ a)
#   define yylex()                              Perl_yylex(aTHX)
#   define isSCRIPT_RUN(a,b,c)                  Perl_isSCRIPT_RUN(aTHX_ a,b,c)
#   define is_utf8_non_invariant_string         Perl_is_utf8_non_invariant_string
#   define sv_or_pv_pos_u2b(a,b,c,d)            S_sv_or_pv_pos_u2b(aTHX_ a,b,c,d)
#   define variant_under_utf8_count             S_variant_under_utf8_count
#   if !defined(HAS_MEMRCHR)
#     define my_memrchr                         S_my_memrchr
#   endif
#   if defined(PERL_ANY_COW)
#     define sv_setsv_cow(a,b)                  Perl_sv_setsv_cow(aTHX_ a,b)
#   endif
#   if defined(PERL_IN_DOOP_C)    || defined(PERL_IN_OP_C)        || \
       defined(PERL_IN_PP_C)      || defined(PERL_IN_REGCOMP_ANY) || \
       defined(PERL_IN_REGEXEC_C) || defined(PERL_IN_TOKE_C)      || \
       defined(PERL_IN_UTF8_C)
#     define _invlist_contains_cp               S__invlist_contains_cp
#     define _invlist_len                       S__invlist_len
#     define _invlist_search                    Perl__invlist_search
#     define get_invlist_offset_addr            S_get_invlist_offset_addr
#     define invlist_array                      S_invlist_array
#     define is_invlist                         S_is_invlist
#   endif
#   if defined(PERL_IN_DOOP_C) || defined(PERL_IN_OP_C) || \
       defined(PERL_IN_REGCOMP_ANY)
#     define add_cp_to_invlist(a,b)             S_add_cp_to_invlist(aTHX_ a,b)
#     define invlist_extend(a,b)                S_invlist_extend(aTHX_ a,b)
#     define invlist_highest                    S_invlist_highest
#     define invlist_set_len(a,b,c)             S_invlist_set_len(aTHX_ a,b,c)
#   endif
#   if defined(PERL_IN_DOOP_C)      || defined(PERL_IN_OP_C) || \
       defined(PERL_IN_REGCOMP_ANY) || defined(PERL_IN_UTF8_C)
#     define _add_range_to_invlist(a,b,c)       Perl__add_range_to_invlist(aTHX_ a,b,c)
#     define _invlist_intersection_maybe_complement_2nd(a,b,c,d) Perl__invlist_intersection_maybe_complement_2nd(aTHX_ a,b,c,d)
#     define _invlist_invert(a)                 Perl__invlist_invert(aTHX_ a)
#     define _invlist_union_maybe_complement_2nd(a,b,c,d) Perl__invlist_union_maybe_complement_2nd(aTHX_ a,b,c,d)
#     define _new_invlist(a)                    Perl__new_invlist(aTHX_ a)
#     define _setup_canned_invlist(a,b,c)       Perl__setup_canned_invlist(aTHX_ a,b,c)
#   endif
#   if defined(PERL_IN_DQUOTE_C) || defined(PERL_IN_REGCOMP_C) || \
       defined(PERL_IN_TOKE_C)
#     define form_alien_digit_msg(a,b,c,d,e,f)  Perl_form_alien_digit_msg(aTHX_ a,b,c,d,e,f)
#     define grok_bslash_c(a,b,c,d)             Perl_grok_bslash_c(aTHX_ a,b,c,d)
#     define grok_bslash_o(a,b,c,d,e,f,g,h)     Perl_grok_bslash_o(aTHX_ a,b,c,d,e,f,g,h)
#     define grok_bslash_x(a,b,c,d,e,f,g,h)     Perl_grok_bslash_x(aTHX_ a,b,c,d,e,f,g,h)
#   endif
#   if defined(PERL_IN_DQUOTE_C) || defined(PERL_IN_REGCOMP_C) || \
       defined(PERL_IN_TOKE_C)   || defined(PERL_IN_UTF8_C)
#     define form_cp_too_large_msg(a,b,c,d)     Perl_form_cp_too_large_msg(aTHX_ a,b,c,d)
#   endif
#   if defined(PERL_IN_DUMP_C) || defined(PERL_IN_OP_C) || \
       defined(PERL_IN_REGCOMP_ANY)
#     define _invlist_dump(a,b,c,d)             Perl__invlist_dump(aTHX_ a,b,c,d)
#   endif
#   if defined(PERL_IN_GV_C) || defined(PERL_IN_UNIVERSAL_C)
#     define gv_stashsvpvn_cached(a,b,c,d)      Perl_gv_stashsvpvn_cached(aTHX_ a,b,c,d)
#   endif
#   if defined(PERL_IN_OP_C) || defined(PERL_IN_REGCOMP_ANY)
#     define get_invlist_iter_addr              S_get_invlist_iter_addr
#     define invlist_iterfinish                 S_invlist_iterfinish
#     define invlist_iterinit                   S_invlist_iterinit
#     define invlist_iternext                   S_invlist_iternext
#   endif
#   if defined(PERL_IN_PERL_C) || defined(PERL_IN_REGCOMP_ANY) || \
       defined(PERL_IN_UTF8_C)
#     define _invlistEQ(a,b,c)                  Perl__invlistEQ(aTHX_ a,b,c)
#     define _new_invlist_C_array(a)            Perl__new_invlist_C_array(aTHX_ a)
#   endif
#   if defined(PERL_IN_PP_C)   || defined(PERL_IN_REGCOMP_ANY) || \
       defined(PERL_IN_TOKE_C) || defined(PERL_IN_UNIVERSAL_C)
#     define get_regex_charset_name             S_get_regex_charset_name
#   endif
#   if defined(PERL_IN_REGCOMP_ANY)
#     define add_above_Latin1_folds(a,b,c)      Perl_add_above_Latin1_folds(aTHX_ a,b,c)
#     define construct_ahocorasick_from_trie(a,b,c) Perl_construct_ahocorasick_from_trie(aTHX_ a,b,c)
#     define get_ANYOFHbbm_contents(a)          Perl_get_ANYOFHbbm_contents(aTHX_ a)
#     define get_ANYOFM_contents(a)             Perl_get_ANYOFM_contents(aTHX_ a)
#     define invlist_contents(a,b)              S_invlist_contents(aTHX_ a,b)
#     define invlist_highest_range_start        S_invlist_highest_range_start
#     define invlist_is_iterating               S_invlist_is_iterating
#     define invlist_lowest                     S_invlist_lowest
#     define is_ssc_worth_it                    Perl_is_ssc_worth_it
#     define join_exact(a,b,c,d,e,f,g)          Perl_join_exact(aTHX_ a,b,c,d,e,f,g)
#     define make_trie(a,b,c,d,e,f,g,h)         Perl_make_trie(aTHX_ a,b,c,d,e,f,g,h)
#     define populate_anyof_bitmap_from_invlist(a,b) Perl_populate_anyof_bitmap_from_invlist(aTHX_ a,b)
#     define reg_add_data                       Perl_reg_add_data
#     define scan_commit(a,b,c,d)               Perl_scan_commit(aTHX_ a,b,c,d)
#     define set_ANYOF_arg(a,b,c,d,e)           Perl_set_ANYOF_arg(aTHX_ a,b,c,d,e)
#     define ssc_finalize(a,b)                  Perl_ssc_finalize(aTHX_ a,b)
#     define ssc_init(a,b)                      Perl_ssc_init(aTHX_ a,b)
#     define study_chunk(a,b,c,d,e,f,g,h,i,j,k,l) Perl_study_chunk(aTHX_ a,b,c,d,e,f,g,h,i,j,k,l)
#     if defined(PERL_IN_REGCOMP_TRIE_C) && defined(DEBUGGING)
#       define dump_trie(a,b,c,d)               S_dump_trie(aTHX_ a,b,c,d)
#       define dump_trie_interim_list(a,b,c,d,e) S_dump_trie_interim_list(aTHX_ a,b,c,d,e)
#       define dump_trie_interim_table(a,b,c,d,e) S_dump_trie_interim_table(aTHX_ a,b,c,d,e)
#     endif
#   endif /* defined(PERL_IN_REGCOMP_ANY) */
#   if defined(PERL_IN_REGCOMP_ANY) || defined(PERL_IN_SV_C)
#     define invlist_clone(a,b)                 Perl_invlist_clone(aTHX_ a,b)
#   endif
#   if defined(PERL_IN_REGCOMP_C)
#     define add_multi_match(a,b,c)             S_add_multi_match(aTHX_ a,b,c)
#     define change_engine_size(a,b)            S_change_engine_size(aTHX_ a,b)
#     define compile_wildcard(a,b,c)            S_compile_wildcard(aTHX_ a,b,c)
#     define compute_EXACTish                   S_compute_EXACTish
#     define edit_distance                      S_edit_distance
#     define execute_wildcard(a,b,c,d,e,f,g)    S_execute_wildcard(aTHX_ a,b,c,d,e,f,g)
#     define find_first_differing_byte_pos      S_find_first_differing_byte_pos
#     define get_quantifier_value(a,b,c)        S_get_quantifier_value(aTHX_ a,b,c)
#     define grok_bslash_N(a,b,c,d,e,f,g)       S_grok_bslash_N(aTHX_ a,b,c,d,e,f,g)
#     define handle_named_backref(a,b,c,d)      S_handle_named_backref(aTHX_ a,b,c,d)
#     define handle_names_wildcard(a,b,c,d)     S_handle_names_wildcard(aTHX_ a,b,c,d)
#     define handle_possible_posix(a,b,c,d,e)   S_handle_possible_posix(aTHX_ a,b,c,d,e)
#     define handle_regex_sets(a,b,c,d)         S_handle_regex_sets(aTHX_ a,b,c,d)
#     define handle_user_defined_property(a,b,c,d,e,f,g,h,i,j) S_handle_user_defined_property(aTHX_ a,b,c,d,e,f,g,h,i,j)
#     define nextchar(a)                        S_nextchar(aTHX_ a)
#     define optimize_regclass(a,b,c,d,e,f,g,h,i,j) S_optimize_regclass(aTHX_ a,b,c,d,e,f,g,h,i,j)
#     define output_posix_warnings(a,b)         S_output_posix_warnings(aTHX_ a,b)
#     define parse_lparen_question_flags(a)     S_parse_lparen_question_flags(aTHX_ a)
#     define parse_uniprop_string(a,b,c,d,e,f,g,h,i,j) S_parse_uniprop_string(aTHX_ a,b,c,d,e,f,g,h,i,j)
#     define reg(a,b,c,d)                       S_reg(aTHX_ a,b,c,d)
#     define reg1node(a,b,c)                    S_reg1node(aTHX_ a,b,c)
#     define reg2node(a,b,c,d)                  S_reg2node(aTHX_ a,b,c,d)
#     define reg_la_NOTHING(a,b,c)              S_reg_la_NOTHING(aTHX_ a,b,c)
#     define reg_la_OPFAIL(a,b,c)               S_reg_la_OPFAIL(aTHX_ a,b,c)
#     define reg_node(a,b)                      S_reg_node(aTHX_ a,b)
#     define reg_scan_name(a,b)                 S_reg_scan_name(aTHX_ a,b)
#     define reg_skipcomment                    S_reg_skipcomment
#     define regatom(a,b,c)                     S_regatom(aTHX_ a,b,c)
#     define regbranch(a,b,c,d)                 S_regbranch(aTHX_ a,b,c,d)
#     define regclass(a,b,c,d,e,f,g,h,i)        S_regclass(aTHX_ a,b,c,d,e,f,g,h,i)
#     define regex_set_precedence               S_regex_set_precedence
#     define reginsert(a,b,c,d)                 S_reginsert(aTHX_ a,b,c,d)
#     define regnode_guts(a,b)                  S_regnode_guts(aTHX_ a,b)
#     define regpiece(a,b,c)                    S_regpiece(aTHX_ a,b,c)
#     define regpnode(a,b,c)                    S_regpnode(aTHX_ a,b,c)
#     define regtail(a,b,c,d)                   S_regtail(aTHX_ a,b,c,d)
#     define set_regex_pv(a,b)                  S_set_regex_pv(aTHX_ a,b)
#     define skip_to_be_ignored_text(a,b,c)     S_skip_to_be_ignored_text(aTHX_ a,b,c)
#     if defined(DEBUGGING)
#       define regnode_guts_debug(a,b,c)        S_regnode_guts_debug(aTHX_ a,b,c)
#       define regtail_study(a,b,c,d)           S_regtail_study(aTHX_ a,b,c,d)
#       if defined(ENABLE_REGEX_SETS_DEBUGGING)
#         define dump_regex_sets_structures(a,b,c,d) S_dump_regex_sets_structures(aTHX_ a,b,c,d)
#       endif
#     endif
#   endif /* defined(PERL_IN_REGCOMP_C) */
#   if defined(PERL_IN_REGCOMP_C) || defined(PERL_IN_REGCOMP_INVLIST_C)
#     define populate_bitmap_from_invlist(a,b,c,d) Perl_populate_bitmap_from_invlist(aTHX_ a,b,c,d)
#     define populate_invlist_from_bitmap(a,b,c,d) Perl_populate_invlist_from_bitmap(aTHX_ a,b,c,d)
#   endif
#   if defined(PERL_IN_REGCOMP_C) || defined(PERL_IN_REGEXEC_C) || \
       defined(PERL_IN_TOKE_C)
#     define is_grapheme(a,b,c,d)               Perl_is_grapheme(aTHX_ a,b,c,d)
#   endif
#   if defined(PERL_IN_REGCOMP_C) || defined(PERL_IN_REGEXEC_C) || \
       defined(PERL_IN_UTF8_C)
#     define _to_fold_latin1                    Perl__to_fold_latin1
#   endif
#   if defined(PERL_IN_REGCOMP_C) || defined(PERL_IN_TOKE_C)
#     define regcurly                           Perl_regcurly
#   endif
#   if defined(PERL_IN_REGCOMP_DEBUG_C) && defined(DEBUGGING)
#     define put_charclass_bitmap_innards(a,b,c,d,e,f,g) S_put_charclass_bitmap_innards(aTHX_ a,b,c,d,e,f,g)
#     define put_charclass_bitmap_innards_common(a,b,c,d,e,f) S_put_charclass_bitmap_innards_common(aTHX_ a,b,c,d,e,f)
#     define put_charclass_bitmap_innards_invlist(a,b) S_put_charclass_bitmap_innards_invlist(aTHX_ a,b)
#     define put_code_point(a,b)                S_put_code_point(aTHX_ a,b)
#     define put_range(a,b,c,d)                 S_put_range(aTHX_ a,b,c,d)
#     define regdump_extflags(a,b)              S_regdump_extflags(aTHX_ a,b)
#     define regdump_intflags(a,b)              S_regdump_intflags(aTHX_ a,b)
#   endif
#   if defined(PERL_IN_REGCOMP_INVLIST_C) && !defined(PERL_EXT_RE_BUILD)
#     define _append_range_to_invlist(a,b,c)    S__append_range_to_invlist(aTHX_ a,b,c)
#     define _invlist_array_init                S__invlist_array_init
#     define get_invlist_previous_index_addr    S_get_invlist_previous_index_addr
#     define invlist_clear(a)                   S_invlist_clear(aTHX_ a)
#     define invlist_max                        S_invlist_max
#     define invlist_previous_index             S_invlist_previous_index
#     define invlist_replace_list_destroys_src(a,b) S_invlist_replace_list_destroys_src(aTHX_ a,b)
#     define invlist_set_previous_index         S_invlist_set_previous_index
#     define invlist_trim                       S_invlist_trim
#   endif /*  defined(PERL_IN_REGCOMP_INVLIST_C) &&
             !defined(PERL_EXT_RE_BUILD) */
#   if defined(PERL_IN_REGCOMP_STUDY_C)
#     define get_ANYOF_cp_list_for_ssc(a,b)     S_get_ANYOF_cp_list_for_ssc(aTHX_ a,b)
#     define make_exactf_invlist(a,b)           S_make_exactf_invlist(aTHX_ a,b)
#     define rck_elide_nothing(a)               S_rck_elide_nothing(aTHX_ a)
#     define ssc_add_range(a,b,c)               S_ssc_add_range(aTHX_ a,b,c)
#     define ssc_and(a,b,c)                     S_ssc_and(aTHX_ a,b,c)
#     define ssc_anything(a)                    S_ssc_anything(aTHX_ a)
#     define ssc_clear_locale                   S_ssc_clear_locale
#     define ssc_cp_and(a,b)                    S_ssc_cp_and(aTHX_ a,b)
#     define ssc_intersection(a,b,c)            S_ssc_intersection(aTHX_ a,b,c)
#     define ssc_is_anything                    S_ssc_is_anything
#     define ssc_is_cp_posixl_init              S_ssc_is_cp_posixl_init
#     define ssc_or(a,b,c)                      S_ssc_or(aTHX_ a,b,c)
#     define ssc_union(a,b,c)                   S_ssc_union(aTHX_ a,b,c)
#     define unwind_scan_frames(a)              S_unwind_scan_frames(aTHX_ a)
#   endif /* defined(PERL_IN_REGCOMP_STUDY_C) */
#   if defined(PERL_IN_REGEXEC_C)
#     define advance_one_LB(a,b,c)              S_advance_one_LB(aTHX_ a,b,c)
#     define advance_one_SB(a,b,c)              S_advance_one_SB(aTHX_ a,b,c)
#     define advance_one_WB(a,b,c,d)            S_advance_one_WB(aTHX_ a,b,c,d)
#     define backup_one_GCB(a,b,c)              S_backup_one_GCB(aTHX_ a,b,c)
#     define backup_one_LB(a,b,c)               S_backup_one_LB(aTHX_ a,b,c)
#     define backup_one_SB(a,b,c)               S_backup_one_SB(aTHX_ a,b,c)
#     define backup_one_WB(a,b,c,d)             S_backup_one_WB(aTHX_ a,b,c,d)
#     define capture_clear(a,b,c,d)             S_capture_clear(aTHX_ a,b,c,d comma_aDEPTH)
#     define find_byclass(a,b,c,d,e)            S_find_byclass(aTHX_ a,b,c,d,e)
#     define find_next_masked                   S_find_next_masked
#     define find_span_end                      S_find_span_end
#     define find_span_end_mask                 S_find_span_end_mask
#     define foldEQ_latin1_s2_folded(a,b,c)     S_foldEQ_latin1_s2_folded(aTHX_ a,b,c)
#     define isFOO_lc(a,b)                      S_isFOO_lc(aTHX_ a,b)
#     define isFOO_utf8_lc(a,b,c)               S_isFOO_utf8_lc(aTHX_ a,b,c)
#     define isGCB(a,b,c,d,e)                   S_isGCB(aTHX_ a,b,c,d,e)
#     define isLB(a,b,c,d,e,f)                  S_isLB(aTHX_ a,b,c,d,e,f)
#     define isSB(a,b,c,d,e,f)                  S_isSB(aTHX_ a,b,c,d,e,f)
#     define isWB(a,b,c,d,e,f,g)                S_isWB(aTHX_ a,b,c,d,e,f,g)
#     define reg_check_named_buff_matched       S_reg_check_named_buff_matched
#     define regcp_restore(a,b,c)               S_regcp_restore(aTHX_ a,b,c comma_aDEPTH)
#     define regcppop(a,b)                      S_regcppop(aTHX_ a,b comma_aDEPTH)
#     define regcppush(a,b,c)                   S_regcppush(aTHX_ a,b,c comma_aDEPTH)
#     define reghop3                            S_reghop3
#     define reghop4                            S_reghop4
#     define reghopmaybe3                       S_reghopmaybe3
#     define reginclass(a,b,c,d,e)              S_reginclass(aTHX_ a,b,c,d,e)
#     define regmatch(a,b,c)                    S_regmatch(aTHX_ a,b,c)
#     define regrepeat(a,b,c,d,e,f)             S_regrepeat(aTHX_ a,b,c,d,e,f comma_aDEPTH)
#     define regtry(a,b)                        S_regtry(aTHX_ a,b)
#     define to_byte_substr(a)                  S_to_byte_substr(aTHX_ a)
#     define to_utf8_substr(a)                  S_to_utf8_substr(aTHX_ a)
#     define unwind_paren(a,b,c)                S_unwind_paren(aTHX_ a,b,c comma_aDEPTH)
#     if defined(DEBUGGING)
#       define debug_start_match(a,b,c,d,e)     S_debug_start_match(aTHX_ a,b,c,d,e)
#       define dump_exec_pos(a,b,c,d,e,f,g)     S_dump_exec_pos(aTHX_ a,b,c,d,e,f,g)
#       if !defined(MULTIPLICITY) || defined(PERL_CORE)
#         define re_exec_indentf(a,...)         Perl_re_exec_indentf(aTHX_ a,__VA_ARGS__)
#       endif
#     endif
#   endif /* defined(PERL_IN_REGEXEC_C) */
# endif /* defined(PERL_CORE) || defined(PERL_EXT) */
# if defined(PERL_CORE) || defined(PERL_USE_VOLATILE_API)
#   define finalize_optree(a)                   Perl_finalize_optree(aTHX_ a)
#   define optimize_optree(a)                   Perl_optimize_optree(aTHX_ a)
# endif
# if !defined(PERL_IMPLICIT_SYS)
#   define my_pclose(a)                         Perl_my_pclose(aTHX_ a)
#   define my_popen(a,b)                        Perl_my_popen(aTHX_ a,b)
# endif
# if defined(PERL_IN_CLASS_C) || defined(PERL_IN_OP_C)    || \
     defined(PERL_IN_PAD_C)   || defined(PERL_IN_PERLY_C) || \
     defined(PERL_IN_TOKE_C)
#   define class_add_ADJUST(a,b)                Perl_class_add_ADJUST(aTHX_ a,b)
#   define class_add_field(a,b)                 Perl_class_add_field(aTHX_ a,b)
#   define class_apply_attributes(a,b)          Perl_class_apply_attributes(aTHX_ a,b)
#   define class_apply_field_attributes(a,b)    Perl_class_apply_field_attributes(aTHX_ a,b)
#   define class_prepare_initfield_parse()      Perl_class_prepare_initfield_parse(aTHX)
#   define class_prepare_method_parse(a)        Perl_class_prepare_method_parse(aTHX_ a)
#   define class_seal_stash(a)                  Perl_class_seal_stash(aTHX_ a)
#   define class_set_field_defop(a,b,c)         Perl_class_set_field_defop(aTHX_ a,b,c)
#   define class_setup_stash(a)                 Perl_class_setup_stash(aTHX_ a)
#   define class_wrap_method_body(a)            Perl_class_wrap_method_body(aTHX_ a)
#   define croak_kw_unless_class(a)             Perl_croak_kw_unless_class(aTHX_ a)
# endif /* defined(PERL_IN_CLASS_C) || defined(PERL_IN_OP_C)    ||
           defined(PERL_IN_PAD_C)   || defined(PERL_IN_PERLY_C) ||
           defined(PERL_IN_TOKE_C) */
# if defined(PERL_IN_REGEX_ENGINE)
#   define check_regnode_after(a,b)             Perl_check_regnode_after(aTHX_ a,b)
#   define regnext(a)                           Perl_regnext(aTHX_ a)
#   define regnode_after(a,b)                   Perl_regnode_after(aTHX_ a,b)
#   if defined(DEBUGGING)
#     if ( !defined(MULTIPLICITY) || defined(PERL_CORE) ) && \
         (  defined(PERL_CORE)    || defined(PERL_EXT) )
#       define re_indentf(a,...)                Perl_re_indentf(aTHX_ a,__VA_ARGS__)
#       define re_printf(...)                   Perl_re_printf(aTHX_ __VA_ARGS__)
#     endif
#     if defined(PERL_CORE) || defined(PERL_EXT)
#       define debug_peep(a,b,c,d,e)            Perl_debug_peep(aTHX_ a,b,c,d,e)
#       define debug_show_study_flags(a,b,c)    Perl_debug_show_study_flags(aTHX_ a,b,c)
#       define debug_studydata(a,b,c,d,e,f,g)   Perl_debug_studydata(aTHX_ a,b,c,d,e,f,g)
#       define dumpuntil(a,b,c,d,e,f,g,h)       Perl_dumpuntil(aTHX_ a,b,c,d,e,f,g,h)
#       define regprop(a,b,c,d,e)               Perl_regprop(aTHX_ a,b,c,d,e)
#     endif
#   endif /* defined(DEBUGGING) */
#   if defined(PERL_EXT_RE_BUILD)
#     if defined(PERL_CORE) || defined(PERL_EXT)
#       define get_re_gclass_aux_data(a,b,c,d,e,f) Perl_get_re_gclass_aux_data(aTHX_ a,b,c,d,e,f)
#     endif
#   elif defined(PERL_CORE) || defined(PERL_EXT)
#     define get_regclass_aux_data(a,b,c,d,e,f) Perl_get_regclass_aux_data(aTHX_ a,b,c,d,e,f)
#   endif
# endif /* defined(PERL_IN_REGEX_ENGINE) */
# if defined(PERL_IN_SV_C)
#   define more_sv()                            Perl_more_sv(aTHX)
#   if defined(PERL_CORE)
#     define F0convert                          S_F0convert
#     define anonymise_cv_maybe(a,b)            S_anonymise_cv_maybe(aTHX_ a,b)
#     define assert_uft8_cache_coherent(a,b,c,d) S_assert_uft8_cache_coherent(aTHX_ a,b,c,d)
#     define curse(a,b)                         S_curse(aTHX_ a,b)
#     define expect_number(a)                   S_expect_number(aTHX_ a)
#     define find_array_subscript(a,b)          S_find_array_subscript(aTHX_ a,b)
#     define find_hash_subscript(a,b)           S_find_hash_subscript(aTHX_ a,b)
#     define find_uninit_var(a,b,c,d)           S_find_uninit_var(aTHX_ a,b,c,d)
#     define glob_2number(a)                    S_glob_2number(aTHX_ a)
#     define glob_assign_glob(a,b,c)            S_glob_assign_glob(aTHX_ a,b,c)
#     define not_a_number(a)                    S_not_a_number(aTHX_ a)
#     define not_incrementable(a)               S_not_incrementable(aTHX_ a)
#     define ptr_table_find                     S_ptr_table_find
#     define sv_2iuv_common(a)                  S_sv_2iuv_common(aTHX_ a)
#     define sv_add_arena(a,b,c)                S_sv_add_arena(aTHX_ a,b,c)
#     define sv_display(a,b,c)                  S_sv_display(aTHX_ a,b,c)
#     define sv_pos_b2u_midway(a,b,c,d)         S_sv_pos_b2u_midway(aTHX_ a,b,c,d)
#     define sv_pos_u2b_cached(a,b,c,d,e,f,g)   S_sv_pos_u2b_cached(aTHX_ a,b,c,d,e,f,g)
#     define sv_pos_u2b_forwards                S_sv_pos_u2b_forwards
#     define sv_pos_u2b_midway                  S_sv_pos_u2b_midway
#     define sv_unglob(a,b)                     S_sv_unglob(aTHX_ a,b)
#     define uiv_2buf                           S_uiv_2buf
#     define utf8_mg_len_cache_update(a,b,c)    S_utf8_mg_len_cache_update(aTHX_ a,b,c)
#     define utf8_mg_pos_cache_update(a,b,c,d,e) S_utf8_mg_pos_cache_update(aTHX_ a,b,c,d,e)
#     define visit(a,b,c)                       S_visit(aTHX_ a,b,c)
#     if defined(DEBUGGING)
#       define del_sv(a)                        S_del_sv(aTHX_ a)
#     endif
#     if !defined(NV_PRESERVES_UV)
#       if defined(DEBUGGING)
#         define sv_2iuv_non_preserve(a,b)      S_sv_2iuv_non_preserve(aTHX_ a,b)
#       else
#         define sv_2iuv_non_preserve(a)        S_sv_2iuv_non_preserve(aTHX_ a)
#       endif
#     endif
#     if defined(PERL_DEBUG_READONLY_COW)
#       define sv_buf_to_rw(a)                  S_sv_buf_to_rw(aTHX_ a)
#     endif
#     if defined(USE_ITHREADS)
#       define sv_dup_common(a,b)               S_sv_dup_common(aTHX_ a,b)
#       define sv_dup_hvaux(a,b,c)              S_sv_dup_hvaux(aTHX_ a,b,c)
#       define sv_dup_inc_multiple(a,b,c,d)     S_sv_dup_inc_multiple(aTHX_ a,b,c,d)
#       define unreferenced_to_tmp_stack(a)     S_unreferenced_to_tmp_stack(aTHX_ a)
#     endif
#   endif /* defined(PERL_CORE) */
# endif /* defined(PERL_IN_SV_C) */
# if defined(PERL_MEM_LOG)
#   define mem_log_alloc                        Perl_mem_log_alloc
#   define mem_log_del_sv                       Perl_mem_log_del_sv
#   define mem_log_free                         Perl_mem_log_free
#   define mem_log_new_sv                       Perl_mem_log_new_sv
#   define mem_log_realloc                      Perl_mem_log_realloc
# endif
# if !defined(PERL_NO_INLINE_FUNCTIONS)
#   define cx_popblock(a)                       Perl_cx_popblock(aTHX_ a)
#   define cx_popeval(a)                        Perl_cx_popeval(aTHX_ a)
#   define cx_popformat(a)                      Perl_cx_popformat(aTHX_ a)
#   define cx_popgiven(a)                       Perl_cx_popgiven(aTHX_ a)
#   define cx_poploop(a)                        Perl_cx_poploop(aTHX_ a)
#   define cx_popsub(a)                         Perl_cx_popsub(aTHX_ a)
#   define cx_popsub_args(a)                    Perl_cx_popsub_args(aTHX_ a)
#   define cx_popsub_common(a)                  Perl_cx_popsub_common(aTHX_ a)
#   define cx_popwhen(a)                        Perl_cx_popwhen(aTHX_ a)
#   define cx_pushblock(a,b,c,d)                Perl_cx_pushblock(aTHX_ a,b,c,d)
#   define cx_pusheval(a,b,c)                   Perl_cx_pusheval(aTHX_ a,b,c)
#   define cx_pushformat(a,b,c,d)               Perl_cx_pushformat(aTHX_ a,b,c,d)
#   define cx_pushgiven(a,b)                    Perl_cx_pushgiven(aTHX_ a,b)
#   define cx_pushloop_for(a,b,c)               Perl_cx_pushloop_for(aTHX_ a,b,c)
#   define cx_pushloop_plain(a)                 Perl_cx_pushloop_plain(aTHX_ a)
#   define cx_pushsub(a,b,c,d)                  Perl_cx_pushsub(aTHX_ a,b,c,d)
#   define cx_pushtry(a,b)                      Perl_cx_pushtry(aTHX_ a,b)
#   define cx_pushwhen(a)                       Perl_cx_pushwhen(aTHX_ a)
#   define cx_topblock(a)                       Perl_cx_topblock(aTHX_ a)
#   define gimme_V()                            Perl_gimme_V(aTHX)
# endif /* !defined(PERL_NO_INLINE_FUNCTIONS) */
# if defined(PERL_USE_3ARG_SIGHANDLER)
#   define csighandler                          Perl_csighandler
# else
#   define csighandler                          Perl_csighandler
# endif
# if defined(U64TYPE)
#   define lsbit_pos64                          Perl_lsbit_pos64
#   define msbit_pos64                          Perl_msbit_pos64
#   define single_1bit_pos64                    Perl_single_1bit_pos64
# endif
# if defined(UNLINK_ALL_VERSIONS)
#   define unlnk(a)                             Perl_unlnk(aTHX_ a)
# endif
# if defined(USE_C_BACKTRACE)
#   define dump_c_backtrace(a,b,c)              Perl_dump_c_backtrace(aTHX_ a,b,c)
#   define get_c_backtrace_dump(a,b)            Perl_get_c_backtrace_dump(aTHX_ a,b)
# endif
# if defined(USE_ITHREADS)
#   define alloccopstash(a)                     Perl_alloccopstash(aTHX_ a)
#   define any_dup(a,b)                         Perl_any_dup(aTHX_ a,b)
#   define cop_file_avn(a)                      Perl_cop_file_avn(aTHX_ a)
#   define cx_dup(a,b,c,d)                      Perl_cx_dup(aTHX_ a,b,c,d)
#   define dirp_dup(a,b)                        Perl_dirp_dup(aTHX_ a,b)
#   define fp_dup(a,b,c)                        Perl_fp_dup(aTHX_ a,b,c)
#   define gp_dup(a,b)                          Perl_gp_dup(aTHX_ a,b)
#   define he_dup(a,b,c)                        Perl_he_dup(aTHX_ a,b,c)
#   define hek_dup(a,b)                         Perl_hek_dup(aTHX_ a,b)
#   define mg_dup(a,b)                          Perl_mg_dup(aTHX_ a,b)
#   define newPADOP(a,b,c)                      Perl_newPADOP(aTHX_ a,b,c)
#   define parser_dup(a,b)                      Perl_parser_dup(aTHX_ a,b)
#   define re_dup_guts(a,b,c)                   Perl_re_dup_guts(aTHX_ a,b,c)
#   define regdupe_internal(a,b)                Perl_regdupe_internal(aTHX_ a,b)
#   define rvpv_dup(a,b,c)                      Perl_rvpv_dup(aTHX_ a,b,c)
#   define si_dup(a,b)                          Perl_si_dup(aTHX_ a,b)
#   define ss_dup(a,b)                          Perl_ss_dup(aTHX_ a,b)
#   define sv_dup(a,b)                          Perl_sv_dup(aTHX_ a,b)
#   define sv_dup_inc(a,b)                      Perl_sv_dup_inc(aTHX_ a,b)
# endif /* defined(USE_ITHREADS) */
# if defined(USE_LOCALE_COLLATE)
#   define sv_collxfrm_flags(a,b,c)             Perl_sv_collxfrm_flags(aTHX_ a,b,c)
#   if ( defined(PERL_CORE)        || defined(PERL_EXT) ) &&        \
       ( defined(PERL_IN_LOCALE_C) || defined(PERL_IN_MATHOMS_C) || \
         defined(PERL_IN_SV_C) )
#     define mem_collxfrm_(a,b,c,d)             Perl_mem_collxfrm_(aTHX_ a,b,c,d)
#   endif
# endif
# if defined(USE_PERLIO)
#   define PerlIO_clearerr(a)                   Perl_PerlIO_clearerr(aTHX_ a)
#   define PerlIO_close(a)                      Perl_PerlIO_close(aTHX_ a)
#   define PerlIO_eof(a)                        Perl_PerlIO_eof(aTHX_ a)
#   define PerlIO_error(a)                      Perl_PerlIO_error(aTHX_ a)
#   define PerlIO_fileno(a)                     Perl_PerlIO_fileno(aTHX_ a)
#   define PerlIO_fill(a)                       Perl_PerlIO_fill(aTHX_ a)
#   define PerlIO_flush(a)                      Perl_PerlIO_flush(aTHX_ a)
#   define PerlIO_get_base(a)                   Perl_PerlIO_get_base(aTHX_ a)
#   define PerlIO_get_bufsiz(a)                 Perl_PerlIO_get_bufsiz(aTHX_ a)
#   define PerlIO_get_cnt(a)                    Perl_PerlIO_get_cnt(aTHX_ a)
#   define PerlIO_get_ptr(a)                    Perl_PerlIO_get_ptr(aTHX_ a)
#   define PerlIO_read(a,b,c)                   Perl_PerlIO_read(aTHX_ a,b,c)
#   define PerlIO_seek(a,b,c)                   Perl_PerlIO_seek(aTHX_ a,b,c)
#   define PerlIO_set_cnt(a,b)                  Perl_PerlIO_set_cnt(aTHX_ a,b)
#   define PerlIO_set_ptrcnt(a,b,c)             Perl_PerlIO_set_ptrcnt(aTHX_ a,b,c)
#   define PerlIO_setlinebuf(a)                 Perl_PerlIO_setlinebuf(aTHX_ a)
#   define PerlIO_stderr()                      Perl_PerlIO_stderr(aTHX)
#   define PerlIO_stdin()                       Perl_PerlIO_stdin(aTHX)
#   define PerlIO_stdout()                      Perl_PerlIO_stdout(aTHX)
#   define PerlIO_tell(a)                       Perl_PerlIO_tell(aTHX_ a)
#   define PerlIO_unread(a,b,c)                 Perl_PerlIO_unread(aTHX_ a,b,c)
#   define PerlIO_write(a,b,c)                  Perl_PerlIO_write(aTHX_ a,b,c)
# endif /* defined(USE_PERLIO) */
# if defined(VMS) || defined(WIN32)
#   define do_aspawn(a,b,c)                     Perl_do_aspawn(aTHX_ a,b,c)
#   define do_spawn(a)                          Perl_do_spawn(aTHX_ a)
#   define do_spawn_nowait(a)                   Perl_do_spawn_nowait(aTHX_ a)
# endif
# if defined(WIN32)
#   define get_context                          Perl_get_context
# else
#   define get_context                          Perl_get_context
# endif
#endif /* !defined(PERL_NO_SHORT_NAMES) */

/* ex: set ro ft=c: */
