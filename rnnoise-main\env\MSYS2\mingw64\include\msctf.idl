/*
 * Copyright 2008 <PERSON><PERSON>, <PERSON><PERSON>eavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifndef DO_NO_IMPORTS
import "oaidl.idl";
import "comcat.idl";
import "textstor.idl";
import "ctfutb.idl";
#endif

cpp_quote("#define TF_E_LOCKED                MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0500)")
cpp_quote("#define TF_E_STACKFULL             MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0501)")
cpp_quote("#define TF_E_NOTOWNEDRANGE         MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0502)")
cpp_quote("#define TF_E_NOPROVIDER            MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0503)")
cpp_quote("#define TF_E_DISCONNECTED          MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0504)")
cpp_quote("#define TF_E_INVALIDVIEW           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0505)")
cpp_quote("#define TF_E_ALREADY_EXISTS        MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0506)")
cpp_quote("#define TF_E_RANGE_NOT_COVERED     MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0507)")
cpp_quote("#define TF_E_COMPOSITION_REJECTED  MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0508)")
cpp_quote("#define TF_E_EMPTYCONTEXT          MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0509)")
cpp_quote("#define TF_E_INVALIDPOS            MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0200)")
cpp_quote("#define TF_E_NOLOCK                MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0201)")
cpp_quote("#define TF_E_NOOBJECT              MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0202)")
cpp_quote("#define TF_E_NOSERVICE             MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0203)")
cpp_quote("#define TF_E_NOINTERFACE           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0204)")
cpp_quote("#define TF_E_NOSELECTION           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0205)")
cpp_quote("#define TF_E_NOLAYOUT              MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0206)")
cpp_quote("#define TF_E_INVALIDPOINT          MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0207)")
cpp_quote("#define TF_E_SYNCHRONOUS           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0208)")
cpp_quote("#define TF_E_READONLY              MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0209)")
cpp_quote("#define TF_E_FORMAT                MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x020a)")
cpp_quote("#define TF_S_ASYNC                 MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0x0300)")

cpp_quote("HRESULT WINAPI TF_CreateThreadMgr(ITfThreadMgr **pptim);")
cpp_quote("HRESULT WINAPI TF_GetThreadMgr(ITfThreadMgr **pptim);")
cpp_quote("HRESULT WINAPI TF_CreateInputProcessorProfiles(ITfInputProcessorProfiles **ppipr);")
cpp_quote("HRESULT WINAPI TF_CreateLangBarMgr(ITfLangBarMgr **pppbm);")
cpp_quote("HRESULT WINAPI TF_CreateLangBarItemMgr(ITfLangBarItemMgr **pplbim);")
cpp_quote("HRESULT WINAPI TF_InvalidAssemblyListCacheIfExist(void);")

cpp_quote("EXTERN_C const GUID GUID_PROP_TEXTOWNER;")
cpp_quote("DEFINE_GUID(GUID_PROP_ATTRIBUTE,0x34b45670,0x7526,0x11d2,0xa1,0x47,0x00,0x10,0x5a,0x27,0x99,0xb5);")
cpp_quote("EXTERN_C const GUID GUID_PROP_LANGID;")
cpp_quote("EXTERN_C const GUID GUID_PROP_READING;")
cpp_quote("EXTERN_C const GUID GUID_PROP_COMPOSING;")

cpp_quote("EXTERN_C const CLSID CLSID_TF_ThreadMgr;")
cpp_quote("EXTERN_C const CLSID CLSID_TF_InputProcessorProfiles;")
cpp_quote("EXTERN_C const CLSID CLSID_TF_LangBarMgr;")
cpp_quote("EXTERN_C const CLSID CLSID_TF_CategoryMgr;")
cpp_quote("EXTERN_C const CLSID CLSID_TF_DisplayAttributeMgr;")

/* GUIDs for Compartments */
cpp_quote("EXTERN_C const GUID GUID_COMPARTMENT_KEYBOARD_DISABLED;")
cpp_quote("EXTERN_C const GUID GUID_COMPARTMENT_KEYBOARD_OPENCLOSE;")
cpp_quote("EXTERN_C const GUID GUID_COMPARTMENT_HANDWRITING_OPENCLOSE;")
cpp_quote("EXTERN_C const GUID GUID_COMPARTMENT_SPEECH_DISABLED;")
cpp_quote("EXTERN_C const GUID GUID_COMPARTMENT_SPEECH_OPENCLOSE;")
cpp_quote("EXTERN_C const GUID GUID_COMPARTMENT_SPEECH_GLOBALSTATE;")
cpp_quote("EXTERN_C const GUID GUID_COMPARTMENT_PERSISTMENUENABLED;")
cpp_quote("EXTERN_C const GUID GUID_COMPARTMENT_EMPTYCONTEXT;")
cpp_quote("EXTERN_C const GUID GUID_COMPARTMENT_TIPUISTATUS;")

/* GUIDs for Categories */
cpp_quote("EXTERN_C const GUID GUID_TFCAT_TIP_KEYBOARD;")
cpp_quote("EXTERN_C const GUID GUID_TFCAT_TIP_SPEECH;")
cpp_quote("EXTERN_C const GUID GUID_TFCAT_TIP_HANDWRITING;")
cpp_quote("EXTERN_C const GUID GUID_TFCAT_DISPLAYATTRIBUTEPROVIDER;")

typedef [uuid(7213778c-7bb0-4270-b050-6189ee594e97)]  DWORD TfEditCookie;
typedef [uuid(de403c21-89fd-4f85-8b87-64584d063fbc)] DWORD TfClientId;
typedef [uuid(88a9c478-f3ec-4763-8345-cd9250443f8d)] DWORD TfGuidAtom;

cpp_quote("#define TF_MOD_ALT                   0x0001")
cpp_quote("#define TF_MOD_CONTROL               0x0002")
cpp_quote("#define TF_MOD_SHIFT                 0x0004")
cpp_quote("#define TF_MOD_RALT                  0x0008")
cpp_quote("#define TF_MOD_RCONTROL              0x0010")
cpp_quote("#define TF_MOD_RSHIFT                0x0020")
cpp_quote("#define TF_MOD_LALT                  0x0040")
cpp_quote("#define TF_MOD_LCONTROL              0x0080")
cpp_quote("#define TF_MOD_LSHIFT                0x0100")
cpp_quote("#define TF_MOD_ON_KEYUP              0x0200")
cpp_quote("#define TF_MOD_IGNORE_ALL_MODIFIER   0x0400")

cpp_quote("#define TF_PROFILETYPE_INPUTPROCESSOR  0x0001")
cpp_quote("#define TF_PROFILETYPE_KEYBOARDLAYOUT  0x0002")

cpp_quote("#define TF_IPSINK_FLAG_ACTIVE 0x0001")

cpp_quote("#define TF_TMAE_NOACTIVATETIP            0x00000001")
cpp_quote("#define TF_TMAE_SECUREMODE               0x00000002")
cpp_quote("#define TF_TMAE_UIELEMENTENABLEDONLY     0x00000004")
cpp_quote("#define TF_TMAE_COMLESS                  0x00000008")
cpp_quote("#define TF_TMAE_WOW16                    0x00000010")
cpp_quote("#define TF_TMAE_NOACTIVATEKEYBOARDLAYOUT 0x00000020")
cpp_quote("#define TF_TMAE_CONSOLE                  0x00000040")

cpp_quote("#define TF_TMF_NOACTIVATETIP             TF_TMAE_NOACTIVATETIP")
cpp_quote("#define TF_TMF_SECUREMODE                TF_TMAE_SECUREMODE")
cpp_quote("#define TF_TMF_UIELEMENTENABLEDONLY      TF_TMAE_UIELEMENTENABLEDONLY")
cpp_quote("#define TF_TMF_COMLESS                   TF_TMAE_COMLESS")
cpp_quote("#define TF_TMF_WOW16                     TF_TMAE_WOW16")
cpp_quote("#define TF_TMF_CONSOLE                   TF_TMAE_CONSOLE")
cpp_quote("#define TF_TMF_IMMERSIVEMODE             0x40000000")
cpp_quote("#define TF_TMF_ACTIVATED                 0x80000000")

interface ITfDocumentMgr;
interface ITfContext;
interface IEnumTfDocumentMgrs;
interface IEnumTfContexts;
interface ITfCompartmentMgr;
interface ITfEditSession;
interface ITfRange;
interface ITfProperty;
interface ITfReadOnlyProperty;
interface IEnumTfLanguageProfiles;
interface ITfCompositionView;
interface ITfKeyEventSink;
interface ITfPersistentPropertyLoaderACP;
interface ITfRangeACP;

cpp_quote("#if 0")
typedef [uuid(4f5d560f-5ab5-4dde-8c4d-404592857ab0)] UINT_PTR HKL;
cpp_quote("#endif")

typedef [uuid(e26d9e1d-691e-4f29-90d7-338dcf1f8cef)] struct TF_PERSISTENT_PROPERTY_HEADER_ACP
{
    GUID guidType;
    LONG ichStart;
    LONG cch;
    ULONG cb;
    DWORD dwPrivate;
    CLSID clsidTIP;
} TF_PERSISTENT_PROPERTY_HEADER_ACP;

typedef [uuid(e1b5808d-1e46-4c19-84dc-68c5f5978cc8)] struct TF_LANGUAGEPROFILE
{
    CLSID clsid;
    LANGID langid;
    GUID catid;
    BOOL fActive;
    GUID guidProfile;
} TF_LANGUAGEPROFILE;

typedef [uuid(77c12f95-b783-450d-879f-1cd2362c6521)] struct TF_PRESERVEDKEY
{
    UINT uVKey;
    UINT uModifiers;
} TF_PRESERVEDKEY;

typedef [uuid(5a886226-ae9a-489b-b991-2b1e25ee59a9)]  enum { TF_ANCHOR_START = 0, TF_ANCHOR_END = 1 } TfAnchor;

typedef [uuid(d678c645-eb6a-45c9-b4ee-0f3e3a991348)] struct TF_PROPERTYVAL
{
    GUID guidId;
    VARIANT varValue;
} TF_PROPERTYVAL;

[
    object,
    uuid(101d6610-0990-11d3-8df0-00105a2799b5),
    pointer_default(unique)
]
interface ITfFunctionProvider : IUnknown
{
    HRESULT GetType(
        [out] GUID *guid);

    HRESULT GetDescription(
        [out] BSTR *desc);

    HRESULT GetFunction(
        [in] REFGUID guid,
        [in] REFIID riid,
        [out, iid_is(riid)] IUnknown **func);
}

[
    object,
    uuid(e4b24db0-0990-11d3-8df0-00105a2799b5),
    pointer_default(unique)
]
interface IEnumTfFunctionProviders : IUnknown
{
    HRESULT Clone(
        [out] IEnumTfFunctionProviders **ret);

    HRESULT Next(
        [in] ULONG count,
        [out, size_is(count), length_is(*fetched)] ITfFunctionProvider **prov,
        [out] ULONG *fetched);

    HRESULT Reset();

    HRESULT Skip(
        [in] ULONG count);
}

[
    object,
    uuid(aa80e801-2021-11d2-93e0-0060b067b86e),
    pointer_default(unique)
]
interface ITfThreadMgr: IUnknown
{
    HRESULT Activate(
        [out] TfClientId *ptid);

    HRESULT Deactivate();

    HRESULT CreateDocumentMgr(
        [out] ITfDocumentMgr **ppdim);

    HRESULT EnumDocumentMgrs(
        [out] IEnumTfDocumentMgrs **ppEnum);

    HRESULT GetFocus(
        [out] ITfDocumentMgr **ppdimFocus);

    HRESULT SetFocus(
        [in] ITfDocumentMgr *pdimFocus);

    HRESULT AssociateFocus(
        [in] HWND hwnd,
        [in, unique] ITfDocumentMgr *pdimNew,
        [out] ITfDocumentMgr **ppdimPrev);

    HRESULT IsThreadFocus(
        [out] BOOL *pfThreadFocus);

    HRESULT GetFunctionProvider(
        [in] REFCLSID clsid,
        [out] ITfFunctionProvider **ppFuncProv);

    HRESULT EnumFunctionProviders(
        [out] IEnumTfFunctionProviders **ppEnum);

    HRESULT GetGlobalCompartment(
        [out] ITfCompartmentMgr **ppCompMgr);
}

[
    object,
    uuid(3e90ade3-7594-4cb0-bb58-69628f5f458c),
    pointer_default(unique)
]
interface ITfThreadMgrEx : ITfThreadMgr
{
    HRESULT ActivateEx(
        [out] TfClientId *id,
        [in] DWORD flags);
    HRESULT GetActiveFlags(
        [out] DWORD *flags);
}

[
    object,
    uuid(d7540241-f9a1-4364-befc-dbcd2c4395b7),
    pointer_default(unique)
]
interface ITfCompositionView : IUnknown
{
    HRESULT GetOwnerClsid([out] CLSID *pclsid);

    HRESULT GetRange([out] ITfRange **ppRange);
}

[
    object,
    uuid(aa80e7f4-2021-11d2-93e0-0060b067b86e),
    pointer_default(unique)
]
interface ITfDocumentMgr: IUnknown
{
    HRESULT CreateContext(
        [in] TfClientId tidOwner,
        [in] DWORD dwFlags,
        [in, unique] IUnknown *punk,
        [out] ITfContext **ppic,
        [out] TfEditCookie *pecTextStore);

    HRESULT Push(
        [in] ITfContext *pic);

    const DWORD TF_POPF_ALL = 0x0001;

    HRESULT Pop(
        [in] DWORD dwFlags);

    HRESULT GetTop(
        [out] ITfContext **ppic);

    HRESULT GetBase(
        [out] ITfContext **ppic);

    HRESULT EnumContexts(
        [out] IEnumTfContexts **ppEnum);
}

[
    object,
    uuid(2433bf8e-0f9b-435c-ba2c-180611978c30),
    pointer_default(unique)
]
interface ITfContextView : IUnknown
{
    HRESULT GetRangeFromPoint(
        [in] TfEditCookie cookie,
        [in] const POINT *pt,
        [in] DWORD flags,
        [out] ITfRange **range);

    HRESULT GetTextExt(
        [in] TfEditCookie cookie,
        [in] ITfRange *range,
        [out] RECT *rect,
        [out] BOOL *clipped);

    HRESULT GetScreenExt(
        [out] RECT *rect);

    HRESULT GetWnd(
        [out] HWND *hwnd);
}

[
    object,
    uuid(f0c0f8dd-cf38-44e1-bb0f-68cf0d551c78),
    pointer_default(unique)
]
interface IEnumTfContextViews : IUnknown
{
    HRESULT Clone(
        [out] IEnumTfContextViews **ret);

    HRESULT Next(
        [in] ULONG count,
        [out, size_is(count), length_is(*fetched)] ITfContextView **views,
        [out] ULONG *fetched);

    HRESULT Reset();

    HRESULT Skip(
        [in] ULONG count);
}

[
    object,
    uuid(19188cb0-aca9-11d2-afc5-00105a2799b5),
    pointer_default(unique)
]
interface IEnumTfProperties : IUnknown
{
    HRESULT Clone(
        [out] IEnumTfProperties **ret);

    HRESULT Next(
        [in] ULONG count,
        [out, size_is(count), length_is(*fetched)] ITfProperty **props,
        [out] ULONG *fetched);

    HRESULT Reset();

    HRESULT Skip(
        [in] ULONG count);
}

[
    object,
    uuid(8ed8981b-7c10-4d7d-9fb3-ab72e9c75f72),
    pointer_default(unique)
]
interface IEnumTfPropertyValue : IUnknown
{
    HRESULT Clone(
        [out] IEnumTfPropertyValue **property_value);

    HRESULT Next(
        [in] ULONG count,
        [out, size_is(count), length_is(*fetched)] TF_PROPERTYVAL *values,
        [out] ULONG *fetched);

    HRESULT Reset();

    HRESULT Skip(
        [in] ULONG count);
}

[
    object,
    uuid(463a506d-6992-49d2-9b88-93d55e70bb16),
    pointer_default(unique)
]
interface ITfRangeBackup : IUnknown
{
    HRESULT Restore(
        [in] TfEditCookie cookie,
        [in] ITfRange *range);
}

[
    object,
    uuid(aa80e901-2021-11d2-93e0-0060b067b86e),
    pointer_default(unique)
]
interface ITextStoreACPServices : IUnknown
{
    HRESULT Serialize(
        [in] ITfProperty *prop,
        [in] ITfRange *range,
        [out] TF_PERSISTENT_PROPERTY_HEADER_ACP *header,
        [in] IStream *stream);

    HRESULT Unserialize(
        [in] ITfProperty *prop,
        [in] const TF_PERSISTENT_PROPERTY_HEADER_ACP *header,
        [in] IStream *stream,
        [in] ITfPersistentPropertyLoaderACP *loader);

    HRESULT ForceLoadProperty(
        [in] ITfProperty *prop);

    HRESULT CreateRange(
        [in] LONG start,
        [in] LONG end,
        [out] ITfRangeACP **range);
}

[
    object,
    uuid(aa80e7fd-2021-11d2-93e0-0060b067b86e),
    pointer_default(unique)
]
interface ITfContext : IUnknown
{
    const DWORD TF_ES_ASYNCDONTCARE = 0x0;
    const DWORD TF_ES_SYNC          = 0x1;
    const DWORD TF_ES_READ          = 0x2;
    const DWORD TF_ES_READWRITE     = 0x6;
    const DWORD TF_ES_ASYNC         = 0x8;

    HRESULT RequestEditSession(
        [in] TfClientId tid,
        [in] ITfEditSession *pes,
        [in] DWORD dwFlags,
        [out] HRESULT *phrSession);

    HRESULT InWriteSession(
        [in] TfClientId tid,
        [out] BOOL *pfWriteSession);

    typedef [uuid(1690be9b-d3e9-49f6-8d8b-51b905af4c43)] enum { TF_AE_NONE, TF_AE_START, TF_AE_END } TfActiveSelEnd;

    typedef [uuid(36ae42a4-6989-4bdc-b48a-6137b7bf2e42)] struct TF_SELECTIONSTYLE
    {
        TfActiveSelEnd ase;
        BOOL fInterimChar;
    } TF_SELECTIONSTYLE;

    typedef [uuid(75eb22f2-b0bf-46a8-8006-975a3b6efcf1)] struct TF_SELECTION
    {
        ITfRange *range;
        TF_SELECTIONSTYLE style;
    } TF_SELECTION;

    const ULONG TF_DEFAULT_SELECTION =  TS_DEFAULT_SELECTION;

    HRESULT GetSelection(
        [in] TfEditCookie ec,
        [in] ULONG ulIndex,
        [in] ULONG ulCount,
        [out, size_is(ulCount), length_is(*pcFetched)] TF_SELECTION *pSelection,
        [out] ULONG *pcFetched);

    HRESULT SetSelection(
        [in] TfEditCookie ec,
        [in] ULONG ulCount,
        [in, size_is(ulCount)] const TF_SELECTION *pSelection);

    HRESULT GetStart(
        [in] TfEditCookie ec,
        [out] ITfRange **ppStart);

    HRESULT GetEnd(
        [in] TfEditCookie ec,
        [out] ITfRange **ppEnd);

    typedef [uuid(bc7d979a-846a-444d-afef-0a9bfa82b961)] TS_STATUS TF_STATUS;
    const DWORD TF_SD_READONLY    = TS_SD_READONLY;
    const DWORD TF_SD_LOADING     = TS_SD_LOADING;
    const DWORD TF_SS_DISJOINTSEL = TS_SS_DISJOINTSEL;
    const DWORD TF_SS_REGIONS     = TS_SS_REGIONS;
    const DWORD TF_SS_TRANSITORY  = TS_SS_TRANSITORY;


    HRESULT GetActiveView(
        [out] ITfContextView **ppView);

    HRESULT EnumViews(
        [out] IEnumTfContextViews **ppEnum);

    HRESULT GetStatus(
        [out] TF_STATUS *pdcs);

    HRESULT GetProperty(
        [in] REFGUID guidProp,
        [out] ITfProperty **ppProp);

    HRESULT GetAppProperty(
        [in] REFGUID guidProp,
        [out] ITfReadOnlyProperty **ppProp);

    HRESULT TrackProperties(
        [in, size_is(cProp)] const GUID **prgProp,
        [in] ULONG cProp,
        [in, size_is(cAppProp)] const GUID **prgAppProp,
        [in] ULONG cAppProp,
        [out] ITfReadOnlyProperty **ppProperty);

    HRESULT EnumProperties(
        [out] IEnumTfProperties **ppEnum);

    HRESULT GetDocumentMgr(
        [out] ITfDocumentMgr **ppDm);

    HRESULT CreateRangeBackup(
        [in] TfEditCookie ec,
        [in] ITfRange *pRange,
        [out] ITfRangeBackup **ppBackup);
}

const DWORD TF_INVALID_COOKIE = 0xffffffff;

[
  object,
  uuid(4ea48a35-60ae-446f-8fd6-e6a8d82459f7),
  pointer_default(unique)
]
interface ITfSource : IUnknown
{
    HRESULT AdviseSink(
        [in] REFIID riid,
        [in, iid_is(riid)] IUnknown *punk,
        [out] DWORD *pdwCookie);

    HRESULT UnadviseSink(
        [in] DWORD dwCookie);
}

[
  object,
  local,
  uuid(1F02B6C5-7842-4EE6-8A0B-9A24183A95CA),
  pointer_default(unique)
]
interface ITfInputProcessorProfiles : IUnknown
{
    HRESULT Register(
        [in] REFCLSID rclsid);

    HRESULT Unregister(
        [in] REFCLSID rclsid);

    HRESULT AddLanguageProfile(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfile,
        [in, size_is(cchDesc)] const WCHAR *pchDesc,
        [in] ULONG cchDesc,
        [in, size_is(cchFile)] const WCHAR *pchIconFile,
        [in] ULONG cchFile,
        [in] ULONG uIconIndex);

    HRESULT RemoveLanguageProfile(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfile);

    HRESULT EnumInputProcessorInfo(
        [out] IEnumGUID **ppEnum);

    HRESULT GetDefaultLanguageProfile(
        [in] LANGID langid,
        [in] REFGUID catid,
        [out] CLSID *pclsid,
        [out] GUID *pguidProfile);

    HRESULT SetDefaultLanguageProfile(
        [in] LANGID langid,
        [in] REFCLSID rclsid,
        [in] REFGUID guidProfiles);

    HRESULT ActivateLanguageProfile(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfiles);

    HRESULT GetActiveLanguageProfile(
        [in] REFCLSID rclsid,
        [out] LANGID *plangid,
        [out] GUID *pguidProfile);

    HRESULT GetLanguageProfileDescription(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfile,
        [out] BSTR *pbstrProfile);

    HRESULT GetCurrentLanguage(
        [out] LANGID *plangid);

    HRESULT ChangeCurrentLanguage(
        [in] LANGID langid);

    HRESULT GetLanguageList(
        [out] LANGID **ppLangId,
        [out] ULONG *pulCount);

    HRESULT EnumLanguageProfiles(
        [in] LANGID langid,
        [out] IEnumTfLanguageProfiles **ppEnum);

    HRESULT EnableLanguageProfile(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfile,
        [in] BOOL fEnable);

    HRESULT IsEnabledLanguageProfile(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfile,
        [out] BOOL *pfEnable);

    HRESULT EnableLanguageProfileByDefault(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfile,
        [in] BOOL fEnable);

    HRESULT SubstituteKeyboardLayout(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfile,
        [in] HKL hKL);
}

typedef [uuid(44d2825a-10e5-43b2-877f-6cb2f43b7e7e)]
struct TF_INPUTPROCESSORPROFILE {
    DWORD dwProfileType;
    LANGID langid;
    CLSID clsid;
    GUID guidProfile;
    GUID catid;
    HKL hklSubstitute;
    DWORD dwCaps;
    HKL hkl;
    DWORD dwFlags;
} TF_INPUTPROCESSORPROFILE;

[
    object,
    uuid(71c6e74d-0f28-11d8-a82a-00065b84435c),
    pointer_default(unique)
]
interface IEnumTfInputProcessorProfiles : IUnknown
{
    HRESULT Clone(
        [out] IEnumTfInputProcessorProfiles **ppEnum);

    HRESULT Next(
        [in] ULONG ulCount,
        [out, size_is(ulCount), length_is(*pcFetch)] TF_INPUTPROCESSORPROFILE *pProfile,
        [out] ULONG *pcFetch);

    HRESULT Reset();

    HRESULT Skip(
        [in] ULONG ulCount);
}

[
    object,
    uuid(71c6e74c-0f28-11d8-a82a-00065b84435c),
    pointer_default(unique)
]
interface ITfInputProcessorProfileMgr : IUnknown
{
    HRESULT ActivateProfile(
        [in] DWORD dwProfileType,
        [in] LANGID langid,
        [in] REFCLSID clsid,
        [in] REFGUID guidProfile,
        [in] HKL hkl,
        [in] DWORD dwFlags);

    HRESULT DeactivateProfile(
        [in] DWORD dwProfileType,
        [in] LANGID langid,
        [in] REFCLSID clsid,
        [in] REFGUID guidProfile,
        [in] HKL hkl,
        [in] DWORD dwFlags);

    HRESULT GetProfile(
        [in] DWORD dwProfileType,
        [in] LANGID langid,
        [in] REFCLSID clsid,
        [in] REFGUID guidProfile,
        [in] HKL hkl,
        [out] TF_INPUTPROCESSORPROFILE *pProfile);


    HRESULT EnumProfiles(
        [in] LANGID langid,
        [out] IEnumTfInputProcessorProfiles **ppEnum);

    HRESULT ReleaseInputProcessor(
        [in] REFCLSID rclsid,
        [in] DWORD dwFlags);

    HRESULT RegisterProfile(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfile,
        [in, size_is(cchDesc)] const WCHAR *pchDesc,
        [in] ULONG cchDesc,
        [in, size_is(cchFile)] const WCHAR *pchIconFile,
        [in] ULONG cchFile,
        [in] ULONG uIconIndex,
        [in] HKL hklsubstitute,
        [in] DWORD dwPreferredLayout,
        [in] BOOL bEnabledByDefault,
        [in] DWORD dwFlags);

    HRESULT UnregisterProfile(
        [in] REFCLSID rclsid,
        [in] LANGID langid,
        [in] REFGUID guidProfile,
        [in] DWORD dwFlags);

    HRESULT GetActiveProfile(
        [in] REFGUID catid,
        [out] TF_INPUTPROCESSORPROFILE *pProfile);
}

typedef [uuid(c4cc07f1-80cc-4a7b-bc54-98512782cbe3)]
enum {
    TF_LS_NONE      = 0,
    TF_LS_SOLID     = 1,
    TF_LS_DOT       = 2,
    TF_LS_DASH      = 3,
    TF_LS_SQUIGGLE  = 4
} TF_DA_LINESTYLE;

typedef [uuid(d9b92e21-084a-401b-9c64-1e6dad91a1ab)]
enum {
    TF_CT_NONE      = 0,
    TF_CT_SYSCOLOR  = 1,
    TF_CT_COLORREF  = 2
} TF_DA_COLORTYPE;

typedef [uuid(90d0cb5e-6520-4a0f-b47c-c39bd955f0d6)]
struct TF_DA_COLOR {
    TF_DA_COLORTYPE type;
    [switch_type(TF_DA_COLORTYPE), switch_is(type)]
    union {
        [case(TF_CT_SYSCOLOR)] int nIndex;
        [case(TF_CT_COLORREF)] COLORREF cr;
    };
} TF_DA_COLOR;

typedef [uuid(33d2fe4b-6c24-4f67-8d75-3bc1819e4126)]
enum {
    TF_ATTR_INPUT                = 0,
    TF_ATTR_TARGET_CONVERTED     = 1,
    TF_ATTR_CONVERTED            = 2,
    TF_ATTR_TARGET_NOTCONVERTED  = 3,
    TF_ATTR_INPUT_ERROR          = 4,
    TF_ATTR_FIXEDCONVERTED       = 5,
    TF_ATTR_OTHER                = -1
} TF_DA_ATTR_INFO;

typedef [uuid(1bf1c305-419b-4182-a4d2-9bfadc3f021f)]
struct TF_DISPLAYATTRIBUTE {
    TF_DA_COLOR      crText;
    TF_DA_COLOR      crBk;
    TF_DA_LINESTYLE  lsStyle;
    BOOL             fBoldLine;
    TF_DA_COLOR      crLine;
    TF_DA_ATTR_INFO  bAttr;
} TF_DISPLAYATTRIBUTE;

[
    object,
    uuid(70528852-2f26-4aea-8c96-************),
    pointer_default(unique)
]
interface ITfDisplayAttributeInfo : IUnknown
{
    HRESULT GetGUID([out] GUID *pguid);

    HRESULT GetDescription([out] BSTR *pbstrDesc);

    HRESULT GetAttributeInfo([out] TF_DISPLAYATTRIBUTE *pda);

    HRESULT SetAttributeInfo([in] const TF_DISPLAYATTRIBUTE *pda);

    HRESULT Reset();
}

[
    object,
    uuid(7cef04d7-cb75-4e80-a7ab-5f5bc7d332de),
    pointer_default(unique)
]
interface IEnumTfDisplayAttributeInfo : IUnknown
{
    HRESULT Clone([out] IEnumTfDisplayAttributeInfo **ppEnum);

    HRESULT Next(
            [in] ULONG ulCount,
            [out, size_is(ulCount), length_is(*pcFetched)] ITfDisplayAttributeInfo **rgInfo,
            [out] ULONG *pcFetched);

    HRESULT Reset();

    HRESULT Skip([in] ULONG ulCount);
}

[
    object,
    local,
    uuid(8ded7393-5db1-475c-9e71-a39111b0ff67),
    pointer_default(unique)
]
interface ITfDisplayAttributeMgr : IUnknown
{
    HRESULT OnUpdateInfo();

    HRESULT EnumDisplayAttributeInfo([out] IEnumTfDisplayAttributeInfo **ppEnum);

    HRESULT GetDisplayAttributeInfo(
            [in] REFGUID guid,
            [out] ITfDisplayAttributeInfo **ppInfo,
            [out] CLSID *pclsidOwner);

}

[
  object,
  local,
  uuid(c3acefb5-f69d-4905-938f-fcadcf4be830),
  pointer_default(unique)
]
interface ITfCategoryMgr : IUnknown
{
    HRESULT RegisterCategory([in] REFCLSID rclsid,
                             [in] REFGUID rcatid,
                             [in] REFGUID rguid);

    HRESULT UnregisterCategory([in] REFCLSID rclsid,
                               [in] REFGUID rcatid,
                               [in] REFGUID rguid);

    HRESULT EnumCategoriesInItem([in] REFGUID rguid,
                                 [out] IEnumGUID **ppEnum);

    HRESULT EnumItemsInCategory([in] REFGUID rcatid,
                                [out] IEnumGUID **ppEnum);

    HRESULT FindClosestCategory([in] REFGUID rguid,
                                [out] GUID *pcatid,
                                [in, size_is(ulCount)] const GUID **ppcatidList,
                                [in] ULONG ulCount);

    HRESULT RegisterGUIDDescription([in] REFCLSID rclsid,
                                    [in] REFGUID rguid,
                                    [in, size_is(cch)] const WCHAR *pchDesc,
                                    [in] ULONG cch);

    HRESULT UnregisterGUIDDescription([in] REFCLSID rclsid,
                                      [in] REFGUID rguid);

    HRESULT GetGUIDDescription([in] REFGUID rguid,
                               [out] BSTR *pbstrDesc);

    HRESULT RegisterGUIDDWORD([in] REFCLSID rclsid,
                              [in] REFGUID rguid,
                              [in] DWORD dw);

    HRESULT UnregisterGUIDDWORD([in] REFCLSID rclsid,
                                [in] REFGUID rguid);

    HRESULT GetGUIDDWORD([in] REFGUID rguid,
                         [out] DWORD *pdw);

    HRESULT RegisterGUID([in] REFGUID rguid,
                         [out] TfGuidAtom *pguidatom);

    HRESULT GetGUID([in] TfGuidAtom guidatom,
                    [out] GUID *pguid);

    HRESULT IsEqualTfGuidAtom([in] TfGuidAtom guidatom,
                              [in] REFGUID rguid,
                              [out] BOOL *pfEqual);
}

[
    object,
    uuid(f99d3f40-8e32-11d2-bf46-00105a2799b5),
    pointer_default(unique)
]
interface IEnumTfRanges : IUnknown
{
    HRESULT Clone([out] IEnumTfRanges **ppEnum);

    HRESULT Next(
            [in] ULONG ulCount,
            [out, size_is(ulCount), length_is(*pcFetched)] ITfRange **ppRange,
            [out] ULONG *pcFetched);

    HRESULT Reset();

    HRESULT Skip(ULONG ulCount);
}

[
    object,
    uuid(42d4d099-7c1a-4a89-b836-6c6f22160df0),
    pointer_default(unique)
]
interface ITfEditRecord : IUnknown
{
    const DWORD TF_GTP_INCL_TEXT = 0x1;

    HRESULT GetSelectionStatus(
        [out] BOOL *changed);

    HRESULT GetTextAndPropertyUpdates(
        [in] DWORD flags,
        [in, size_is(count)] const GUID **props,
        [in] ULONG count,
        [out] IEnumTfRanges **ret);
}

[
  object,
  uuid(8127d409-ccd3-4683-967a-b43d5b482bf7),
  pointer_default(unique)
]
interface ITfTextEditSink : IUnknown
{
    HRESULT OnEndEdit(
        [in] ITfContext *pic,
        [in] TfEditCookie ecReadOnly,
        [in] ITfEditRecord *pEditRecord);
}

[
    object,
    uuid(5F20AA40-B57A-4F34-96AB-3576F377CC79),
    pointer_default(unique)
]
interface ITfContextOwnerCompositionSink : IUnknown
{
    HRESULT OnStartComposition(
        [in] ITfCompositionView *pComposition,
        [out] BOOL *pfOk);

    HRESULT OnUpdateComposition(
        [in] ITfCompositionView *pComposition,
        [in] ITfRange *pRangeNew);

    HRESULT OnEndComposition(
        [in] ITfCompositionView *pComposition);
}

[
    object,
    uuid(b246cb75-a93e-4652-bf8c-b3fe0cfd7e57),
    pointer_default(unique)
]
interface ITfActiveLanguageProfileNotifySink : IUnknown
{
    HRESULT OnActivated(
        [in] REFCLSID clsid,
        [in] REFGUID guidProfile,
        [in] BOOL fActivated);
}

[
  object,
  uuid(3d61bf11-ac5f-42c8-a4cb-931bcc28c744),
  pointer_default(unique)
]
interface IEnumTfLanguageProfiles : IUnknown
{
    HRESULT Clone(
        [out] IEnumTfLanguageProfiles **ppEnum);

    HRESULT Next(
        [in] ULONG ulCount,
        [out, size_is(ulCount), length_is(*pcFetch)] TF_LANGUAGEPROFILE *pProfile,
        [out] ULONG *pcFetch);

    HRESULT Reset();

    HRESULT Skip(
        [in] ULONG ulCount);
}

[
  object,
  local,
  uuid(aa80e7f7-2021-11d2-93e0-0060b067b86e),
  pointer_default(unique)
]
interface ITfTextInputProcessor : IUnknown
{
    HRESULT Activate(
        [in] ITfThreadMgr *ptim,
        [in] TfClientId tid);

    HRESULT Deactivate();
}

[
  object,
  uuid(aa80e80e-2021-11d2-93e0-0060b067b86e),
  pointer_default(unique)
]
interface ITfThreadMgrEventSink : IUnknown
{
    HRESULT OnInitDocumentMgr(
        [in] ITfDocumentMgr *pdim);

    HRESULT OnUninitDocumentMgr(
        [in] ITfDocumentMgr *pdim);

    HRESULT OnSetFocus(
        [in] ITfDocumentMgr *pdimFocus,
        [in] ITfDocumentMgr *pdimPrevFocus);

    HRESULT OnPushContext(
        [in] ITfContext *pic);

    HRESULT OnPopContext(
        [in] ITfContext *pic);
}

[
  object,
  local,
  uuid(aa80e7f0-2021-11d2-93e0-0060b067b86e),
  pointer_default(unique)
]
interface ITfKeystrokeMgr : IUnknown
{
    HRESULT AdviseKeyEventSink(
        [in] TfClientId tid,
        [in] ITfKeyEventSink *pSink,
        [in] BOOL fForeground);

    HRESULT UnadviseKeyEventSink(
        [in] TfClientId tid);

    HRESULT GetForeground(
        [out] CLSID *pclsid);

    HRESULT TestKeyDown(
        [in] WPARAM wParam,
        [in] LPARAM lParam,
        [out] BOOL *pfEaten);

    HRESULT TestKeyUp(
        [in] WPARAM wParam,
        [in] LPARAM lParam,
        [out] BOOL *pfEaten);

    HRESULT KeyDown(
        [in] WPARAM wParam,
        [in] LPARAM lParam,
        [out] BOOL *pfEaten);

    HRESULT KeyUp(
        [in] WPARAM wParam,
        [in] LPARAM lParam,
        [out] BOOL *pfEaten);

    HRESULT GetPreservedKey(
        [in] ITfContext *pic,
        [in] const TF_PRESERVEDKEY *pprekey,
        [out] GUID *pguid);

    HRESULT IsPreservedKey(
        [in] REFGUID rguid,
        [in] const TF_PRESERVEDKEY *pprekey,
        [out] BOOL *pfRegistered);

    HRESULT PreserveKey(
        [in] TfClientId tid,
        [in] REFGUID rguid,
        [in] const TF_PRESERVEDKEY *prekey,
        [in, size_is(cchDesc)] const WCHAR *pchDesc,
        [in] ULONG cchDesc);

    HRESULT UnpreserveKey(
        [in] REFGUID rguid,
        [in] const TF_PRESERVEDKEY *pprekey);

    HRESULT SetPreservedKeyDescription(
        [in] REFGUID rguid,
        [in, size_is(cchDesc)] const WCHAR *pchDesc,
        [in] ULONG cchDesc);

    HRESULT GetPreservedKeyDescription(
        [in] REFGUID rguid,
        [out] BSTR *pbstrDesc);

    HRESULT SimulatePreservedKey(
        [in] ITfContext *pic,
        [in] REFGUID rguid,
        [out] BOOL *pfEaten);
}

[
  object,
  local,
  uuid(aa80e7f5-2021-11d2-93e0-0060b067b86e),
  pointer_default(unique)
]
interface ITfKeyEventSink : IUnknown
{
    HRESULT OnSetFocus(
        [in] BOOL fForeground);

    HRESULT OnTestKeyDown(
        [in] ITfContext *pic,
        [in] WPARAM wParam,
        [in] LPARAM lParam,
        [out] BOOL *pfEaten);

    HRESULT OnTestKeyUp(
        [in] ITfContext *pic,
        [in] WPARAM wParam,
        [in] LPARAM lParam,
        [out] BOOL *pfEaten);

    HRESULT OnKeyDown(
        [in] ITfContext *pic,
        [in] WPARAM wParam,
        [in] LPARAM lParam,
        [out] BOOL *pfEaten);

    HRESULT OnKeyUp(
        [in] ITfContext *pic,
        [in] WPARAM wParam,
        [in] LPARAM lParam,
        [out] BOOL *pfEaten);

    HRESULT OnPreservedKey(
        [in] ITfContext *pic,
        [in] REFGUID rguid,
        [out] BOOL *pfEaten);
}

[
  object,
  local,
  uuid(1cd4c13b-1c36-4191-a70a-7f3e611f367d),
  pointer_default(unique)
]
interface ITfKeyTraceEventSink : IUnknown
{
    HRESULT OnKeyTraceDown(
        [in] WPARAM wParam,
        [in] LPARAM lParam);

    HRESULT OnKeyTraceUp(
        [in] WPARAM wParam,
        [in] LPARAM lParam);
}

[
  object,
  local,
  uuid(ea1ea136-19df-11d7-a6d2-00065b84435c),
  pointer_default(unique)
]
interface ITfUIElementSink : IUnknown
{
    HRESULT BeginUIElement(
        [in] DWORD id,
        [in, out] BOOL *show);

    HRESULT UpdateUIElement(
        [in] DWORD id);

    HRESULT EndUIElement(
        [in] DWORD id);
}

[
  object,
  local,
  uuid(8f1b8ad8-0b6b-4874-90c5-bd76011e8f7c),
  pointer_default(unique)
]
interface ITfMessagePump : IUnknown
{
    HRESULT PeekMessageA(
        [out] LPMSG pMsg,
        [in] HWND hwnd,
        [in] UINT wMsgFilterMin,
        [in] UINT wMsgFilterMax,
        [in] UINT wRemoveMsg,
        [out] BOOL *pfResult);

    HRESULT GetMessageA(
        [out] LPMSG pMsg,
        [in] HWND hwnd,
        [in] UINT wMsgFilterMin,
        [in] UINT wMsgFilterMax,
        [out] BOOL *pfResult);

    HRESULT PeekMessageW(
        [out] LPMSG pMsg,
        [in] HWND hwnd,
        [in] UINT wMsgFilterMin,
        [in] UINT wMsgFilterMax,
        [in] UINT wRemoveMsg,
        [out] BOOL *pfResult);

    HRESULT GetMessageW(
        [out] LPMSG pMsg,
        [in] HWND hwnd,
        [in] UINT wMsgFilterMin,
        [in] UINT wMsgFilterMax,
        [out] BOOL *pfResult);
}

[
  object,
  local,
  uuid(d60a7b49-1b9f-4be2-b702-47e9dc05dec3),
  pointer_default(unique)
]
interface ITfClientId : IUnknown
{
    HRESULT GetClientId(
        [in] REFCLSID rclsid,
        [out] TfClientId *ptid);
}

[
  object,
  uuid(43c9fe15-f494-4c17-9de2-b8a4ac350aa8),
  pointer_default(unique)
]
interface ITfLanguageProfileNotifySink : IUnknown
{
    HRESULT OnLanguageChange(
        [in] LANGID langid,
        [out] BOOL *pfAccept);

    HRESULT OnLanguageChanged();
}

[
  object,
  uuid(aa80e803-2021-11d2-93e0-0060b067b86e),
  pointer_default(unique)
]
interface ITfEditSession : IUnknown
{
    HRESULT DoEditSession(
        [in] TfEditCookie ec);
}

[
  object,
  uuid(aa80e7ff-2021-11d2-93e0-0060b067b86e),
  pointer_default(unique)
]
interface ITfRange : IUnknown
{
    const WCHAR TF_CHAR_EMBEDDED     = TS_CHAR_EMBEDDED;

    typedef [uuid(cf610f06-2882-46f6-abe5-298568b664c4)] enum { TF_GRAVITY_BACKWARD = 0, TF_GRAVITY_FORWARD = 1 } TfGravity;

    typedef [uuid(1e512533-bbdc-4530-9a8e-a1dc0af67468)] enum { TF_SD_BACKWARD = 0, TF_SD_FORWARD = 1 } TfShiftDir;

    const DWORD TF_HF_OBJECT         = 1;
    const DWORD TF_TF_MOVESTART      = 1;
    const DWORD TF_TF_IGNOREEND      = 2;
    const DWORD TF_ST_CORRECTION     = 1;
    const DWORD TF_IE_CORRECTION     = 1;

    typedef [uuid(49930d51-7d93-448c-a48c-fea5dac192b1)] struct  TF_HALTCOND
    {
        ITfRange *pHaltRange;
        TfAnchor aHaltPos;
        DWORD dwFlags;
    } TF_HALTCOND;

    HRESULT GetText(
        [in] TfEditCookie ec,
        [in] DWORD dwFlags,
        [out, size_is(cchMax), length_is(*pcch)] WCHAR *pchText,
        [in] ULONG cchMax,
        [out] ULONG *pcch);

    HRESULT SetText(
        [in] TfEditCookie ec,
        [in] DWORD dwFlags,
        [in, size_is(cch), unique] const WCHAR *pchText,
        [in] LONG cch);

    HRESULT GetFormattedText(
        [in] TfEditCookie ec,
        [out] IDataObject **ppDataObject);

    HRESULT GetEmbedded(
        [in] TfEditCookie ec,
        [in] REFGUID rguidService,
        [in] REFIID riid,
        [out, iid_is(riid)] IUnknown **ppunk);

    HRESULT InsertEmbedded(
        [in] TfEditCookie ec,
        [in] DWORD dwFlags,
        [in] IDataObject *pDataObject);

    HRESULT ShiftStart(
        [in] TfEditCookie ec,
        [in] LONG cchReq,
        [out] LONG *pcch,
        [in, unique] const TF_HALTCOND *pHalt);

    HRESULT ShiftEnd(
        [in] TfEditCookie ec,
        [in] LONG cchReq,
        [out] LONG *pcch,
        [in, unique] const TF_HALTCOND *pHalt);

    HRESULT ShiftStartToRange(
        [in] TfEditCookie ec,
        [in] ITfRange *pRange,
        [in] TfAnchor aPos);

    HRESULT ShiftEndToRange(
        [in] TfEditCookie ec,
        [in] ITfRange *pRange,
        [in] TfAnchor aPos);

    HRESULT ShiftStartRegion(
        [in] TfEditCookie ec,
        [in] TfShiftDir dir,
        [out] BOOL *pfNoRegion);

    HRESULT ShiftEndRegion(
        [in] TfEditCookie ec,
        [in] TfShiftDir dir,
        [out] BOOL *pfNoRegion);

    HRESULT IsEmpty(
        [in] TfEditCookie ec,
        [out] BOOL *pfEmpty);

    HRESULT Collapse(
        [in] TfEditCookie ec,
        [in] TfAnchor aPos);

    HRESULT IsEqualStart(
        [in] TfEditCookie ec,
        [in] ITfRange *pWith,
        [in] TfAnchor aPos,
        [out] BOOL *pfEqual);

    HRESULT IsEqualEnd(
        [in] TfEditCookie ec,
        [in] ITfRange *pWith,
        [in] TfAnchor aPos,
        [out] BOOL *pfEqual);

    HRESULT CompareStart(
        [in] TfEditCookie ec,
        [in] ITfRange *pWith,
        [in] TfAnchor aPos,
        [out] LONG *plResult);

    HRESULT CompareEnd(
        [in] TfEditCookie ec,
        [in] ITfRange *pWith,
        [in] TfAnchor aPos,
        [out] LONG *plResult);

    HRESULT AdjustForInsert(
        [in] TfEditCookie ec,
        [in] ULONG cchInsert,
        [out] BOOL *pfInsertOk);

    HRESULT GetGravity(
        [out] TfGravity *pgStart,
        [out] TfGravity *pgEnd);

    HRESULT SetGravity(
        [in] TfEditCookie ec,
        [in] TfGravity gStart,
        [in] TfGravity gEnd);

    HRESULT Clone(
        [out] ITfRange **ppClone);

    HRESULT GetContext(
        [out] ITfContext **ppContext);
}

[
    object,
    uuid(057a6296-029b-4154-b79a-0d461d4ea94c),
    pointer_default(unique)
]
interface ITfRangeACP : ITfRange
{
    HRESULT GetExtent([out] LONG *pacpAnchor,
                      [out] LONG *pcch);

    HRESULT SetExtent([in] LONG acpAnchor,
                      [in] LONG cch);
}

[
    object,
    uuid(55ce16ba-3014-41c1-9ceb-fade1446ac6c),
    pointer_default(unique)
]
interface ITfInsertAtSelection : IUnknown
{
    const DWORD TF_IAS_NOQUERY   = 0x1;
    const DWORD TF_IAS_QUERYONLY = 0x2;
    const DWORD TF_IAS_NO_DEFAULT_COMPOSITION = 0x80000000;

    HRESULT InsertTextAtSelection(
        [in] TfEditCookie ec,
        [in] DWORD dwFlags,
        [in, size_is(cch)] const WCHAR *pchText,
        [in] LONG cch,
        [out] ITfRange **ppRange);

    HRESULT InsertEmbeddedAtSelection(
        [in] TfEditCookie ec,
        [in] DWORD dwFlags,
        [in] IDataObject *pDataObject,
        [out] ITfRange **ppRange);
}

[
    object,
    uuid(6834b120-88cb-11d2-bf45-00105a2799b5),
    pointer_default(unique)
]
interface ITfPropertyStore : IUnknown
{
    const DWORD TF_TU_CORRECTION = 0x1;

    HRESULT GetType([out] GUID *pguid);

    HRESULT GetDataType([out] DWORD *pdwReserved);

    HRESULT GetData([out] VARIANT *pvarValue);

    HRESULT OnTextUpdated(
            [in] DWORD dwFlags,
            [in] ITfRange *pRangeNew,
            [out] BOOL *pfAccept);

    HRESULT Shrink(
            [in] ITfRange *pRangeNew,
            [out] BOOL *pfFree);

    HRESULT Divide(
            [in] ITfRange *pRangeThis,
            [in] ITfRange *pRangeNew,
            [out] ITfPropertyStore **ppPropStore);

    HRESULT Clone(
            [out] ITfPropertyStore **pPropStore);

    HRESULT GetPropertyRangeCreator(
            [out] CLSID *pclsid);

    HRESULT Serialize(
            [in] IStream *pStream,
            [out] ULONG *pcb);
}

[
    object,
    uuid(5efd22Ba-7838-46cb-88e2-cadb14124f8f),
    pointer_default(unique)
]
interface IEnumITfCompositionView : IUnknown
{
    HRESULT Clone([out] IEnumITfCompositionView **ppEnum);

    HRESULT Next(
            [in] ULONG ulCount,
            [out, size_is(ulCount), length_is(*pcFetched)] ITfCompositionView **rgCompositionView,
            [out] ULONG *pcFetched);

    HRESULT Reset();

    HRESULT Skip([in] ULONG ulCount);
}

[
    object,
    uuid(20168d64-5a8f-4a5a-b7bd-cfa29f4D0fd9),
    pointer_default(unique)
]
interface ITfComposition : IUnknown
{
    HRESULT GetRange([out] ITfRange **ppRange);

    HRESULT ShiftStart(
            [in] TfEditCookie ecWrite,
            [in] ITfRange *pNewStart);

    HRESULT ShiftEnd(
            [in] TfEditCookie ecWrite,
            [in] ITfRange *pNewEnd);

    HRESULT EndComposition([in] TfEditCookie ecWrite);
}

[
    object,
    uuid(a781718c-579a-4b15-a280-32b8577acc5e),
    pointer_default(unique)
]
interface ITfCompositionSink : IUnknown
{
    HRESULT OnCompositionTerminated(
            [in] TfEditCookie ecWrite,
            [in] ITfComposition *pComposition);
}

[
    object,
    uuid(d40C8aae-aC92-4fc7-9a11-0ee0e23aa39b),
    pointer_default(unique)
]
interface ITfContextComposition : IUnknown
{
    HRESULT StartComposition(
            [in] TfEditCookie ecWrite,
            [in] ITfRange *pCompositionRange,
            [in] ITfCompositionSink *pSink,
            [out] ITfComposition **ppComposition);

    HRESULT EnumCompositions([out] IEnumITfCompositionView **ppEnum);

    HRESULT FindComposition(
            [in] TfEditCookie ecRead,
            [in] ITfRange *pTestRange,
            [out] IEnumITfCompositionView **ppEnum);

    HRESULT TakeOwnership(
            [in] TfEditCookie ecWrite,
            [in] ITfCompositionView *pComposition,
            [in] ITfCompositionSink *pSink,
            [out] ITfComposition **ppComposition);
}

[
    object,
    uuid(86462810-593b-4916-9764-19c08e9ce110),
    pointer_default(unique)
]
interface ITfContextOwnerCompositionServices : ITfContextComposition
{
    HRESULT TerminateComposition([in] ITfCompositionView *pComposition);
}

[
    object,
    uuid(4ef89150-0807-11d3-8df0-00105a2799b5),
    pointer_default(unique)
]
interface ITfPersistentPropertyLoaderACP : IUnknown
{
    HRESULT LoadProperty(
            [in] const TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,
            [out] IStream **ppStream);
}

[
    object,
    uuid(b23eb630-3e1c-11d3-a745-0050040ab407),
    pointer_default(unique)
]
interface ITfContextOwnerServices : IUnknown
{
    HRESULT OnLayoutChange();

    HRESULT OnStatusChange([in] DWORD dwFlags);

    HRESULT OnAttributeChange([in] REFGUID rguidAttribute);

    HRESULT Serialize(
            [in] ITfProperty *pProp,
            [in] ITfRange *pRange,
            [out] TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,
            [in] IStream *pStream);

    HRESULT Unserialize(
            [in] ITfProperty *pProp,
            [in] const TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,
            [in] IStream *pStream,
            [in] ITfPersistentPropertyLoaderACP *pLoader);

    HRESULT ForceLoadProperty([in] ITfProperty *pProp);

    HRESULT CreateRange(
            [in] LONG acpStart,
            [in] LONG acpEnd,
            [out] ITfRangeACP **ppRange);
}

[
    object,
    uuid(17d49a3d-f8b8-4b2f-b254-52319dd64c53),
    pointer_default(unique)
]
interface ITfReadOnlyProperty : IUnknown
{
    HRESULT GetType([out] GUID *pguid);

    HRESULT EnumRanges(
            [in] TfEditCookie ec,
            [out] IEnumTfRanges **ppEnum,
            [in] ITfRange *pTargetRange);

    HRESULT GetValue(
            [in] TfEditCookie ec,
            [in] ITfRange *pRange,
            [out] VARIANT *pvarValue);

    HRESULT GetContext([out] ITfContext **ppContext);
}

[
    object,
    uuid(e2449660-9542-11d2-bf46-00105a2799b5),
    pointer_default(unique)
]
interface ITfProperty : ITfReadOnlyProperty
{
    HRESULT FindRange(
            [in] TfEditCookie ec,
            [in] ITfRange *pRange,
            [out] ITfRange **ppRange,
            [in] TfAnchor aPos);

    HRESULT SetValueStore(
            [in] TfEditCookie ec,
            [in] ITfRange *pRange,
            [in] ITfPropertyStore *pPropStore);

    HRESULT SetValue(
            [in] TfEditCookie ec,
            [in] ITfRange *pRange,
            [in] const VARIANT *pvarValue);

    HRESULT Clear(
            [in] TfEditCookie ec,
            [in] ITfRange *pRange);
}

[
  object,
  uuid(bb08f7a9-607a-4384-8623-056892b64371),
  pointer_default(unique)
]
interface ITfCompartment : IUnknown
{
    HRESULT SetValue(
        [in] TfClientId tid,
        [in] const VARIANT *pvarValue);

    HRESULT GetValue(
        [out] VARIANT *pvarValue);
}

[
  object,
  uuid(7dcf57ac-18ad-438b-824d-979bffb74b7c),
  pointer_default(unique)
]
interface ITfCompartmentMgr : IUnknown
{
    HRESULT GetCompartment(
        [in] REFGUID rguid,
        [out] ITfCompartment **ppcomp);

    HRESULT ClearCompartment(
        [in] TfClientId tid,
        [in] REFGUID rguid);

    HRESULT EnumCompartments(
        [out] IEnumGUID **ppEnum);
}

[
  object,
  uuid(743abd5f-f26d-48df-8cc5-238492419b64),
  pointer_default(unique)
]
interface ITfCompartmentEventSink : IUnknown
{
    HRESULT OnChange(
        [in] REFGUID rguid);
}

[
  object,
  uuid(8f1a7ea6-1654-4502-a86e-b2902344d507),
  pointer_default(unique)
]
interface IEnumTfContexts : IUnknown
{
    HRESULT Clone(
        [out] IEnumTfContexts **ppEnum);

    HRESULT Next(
        [in] ULONG ulCount,
        [out, size_is(ulCount), length_is(*pcFetched)] ITfContext **rgContext,
        [out] ULONG *pcFetched);

    HRESULT Reset();

    HRESULT Skip(
        [in] ULONG ulCount);
}

[
  object,
  uuid(aa80e808-2021-11d2-93e0-0060b067b86e),
  pointer_default(unique)
]
interface IEnumTfDocumentMgrs : IUnknown
{
    HRESULT Clone(
        [out] IEnumTfDocumentMgrs **ppEnum);

    HRESULT Next(
        [in] ULONG ulCount,
        [out, size_is(ulCount), length_is(*pcFetched)] ITfDocumentMgr **rgDocumentMgr,
        [out] ULONG *pcFetched);

    HRESULT Reset();

    HRESULT Skip(
        [in] ULONG ulCount);
}

[
    object,
    local,
    uuid(ea1ea137-19df-11d7-a6d2-00065b84435c),
    pointer_default(unique)
]
interface ITfUIElement : IUnknown
{
    HRESULT GetDescription(
        [out] BSTR *description);

    HRESULT GetGUID(
        [out] GUID *guid);

    HRESULT Show(
        [in] BOOL show);

    HRESULT IsShown(
        [out] BOOL *show);
}

[
    object,
    local,
    uuid(887aa91e-acba-4931-84da-3c5208cf543f),
    pointer_default(unique)
]
interface IEnumTfUIElements : IUnknown
{
    HRESULT Clone(
        [out] IEnumTfUIElements **enum_elements);

    HRESULT Next(
        [in] ULONG count,
        [out, size_is(count), length_is(fetched)] ITfUIElement **element,
        [out] ULONG *fetched);

    HRESULT Reset();

    HRESULT Skip(
        [in] ULONG count);
}

[
    object,
    local,
    uuid(ea1ea135-19df-11d7-a6d2-00065b84435c),
    pointer_default(unique)
]
interface ITfUIElementMgr : IUnknown
{
    HRESULT BeginUIElement(
        [in] ITfUIElement *element,
        [in, out] BOOL *show,
        [out] DWORD *id);

    HRESULT UpdateUIElement(
        [in] DWORD id);

    HRESULT EndUIElement(
        [in] DWORD id);

    HRESULT GetUIElement(
        [in] DWORD id,
        [out] ITfUIElement **element);

    HRESULT EnumUIElements(
        [out] IEnumTfUIElements **enum_elements);
}

[
  object,
  uuid(73131f9c-56a9-49dd-b0ee-d046633f7528),
  pointer_default(unique)
]
interface ITfSourceSingle : IUnknown
{
    HRESULT AdviseSingleSink(
        [in] TfClientId tid,
        [in] REFIID riid,
        [in, iid_is(riid)] IUnknown *punk);

    HRESULT UnadviseSingleSink(
        [in] TfClientId tid,
        [in] REFIID riid);
}

[
  object,
  local,
  uuid(c0f1db0c-3a20-405c-a303-96b6010a885f),
  pointer_default(unique)
]
interface ITfThreadFocusSink : IUnknown
{
    HRESULT OnSetThreadFocus();

    HRESULT OnKillThreadFocus();
}

[
  object,
  uuid(71c6e74e-0f28-11d8-a82a-00065b84435c),
  pointer_default(unique)
]
interface ITfInputProcessorProfileActivationSink : IUnknown
{
    HRESULT OnActivated(
        [in] DWORD dwProfileType,
        [in] LANGID langid,
        [in] REFCLSID clsid,
        [in] REFGUID catid,
        [in] REFGUID guidProfile,
        [in] HKL hkl,
        [in] DWORD dwFlags);
}

[
    object,
    local,
    uuid(a1adaaa2-3a24-449d-ac96-5183e7f5c217),
    pointer_default(unique)
]
interface ITfMouseSink : IUnknown
{
    HRESULT OnMouseEvent(
        [in] ULONG uEdge,
        [in] ULONG uQuadrant,
        [in] DWORD dwBtnStatus,
        [out] BOOL *pfEaten);
}

[
    object,
    uuid(09d146cd-a544-4132-925b-7afa8ef322d0),
    pointer_default(unique)
]
interface ITfMouseTracker : IUnknown
{
    HRESULT AdviseMouseSink(
        [in] ITfRange *range,
        [in] ITfMouseSink *pSink,
        [out] DWORD *pdwCookie);

    HRESULT UnadviseMouseSink([in] DWORD dwCookie);
}

[
    object,
    uuid(3bdd78e2-c16e-47fd-b883-ce6facc1a208),
    pointer_default(unique)
]
interface ITfMouseTrackerACP : IUnknown
{
    HRESULT AdviseMouseSink(
        [in] ITfRangeACP *range,
        [in] ITfMouseSink *pSink,
        [out] DWORD *pdwCookie);

    HRESULT UnadviseMouseSink([in] DWORD dwCookie);
}

[
    object,
    local,
    uuid(a615096f-1c57-4813-8a15-55ee6e5a839c),
    pointer_default(unique)
]
interface ITfTransitoryExtensionSink : IUnknown
{
    HRESULT OnTransitoryExtensionUpdated(
        [in] ITfContext *pic,
        [in] TfEditCookie ecReadOnly,
        [in] ITfRange *pResultRange,
        [in] ITfRange *pCompositionRange,
        [out] BOOL *pfDeleteResultRange);
}
