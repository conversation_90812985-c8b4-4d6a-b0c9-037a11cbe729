.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dh_get_group" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dh_get_group \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_dh_get_group(gnutls_session_t " session ", gnutls_datum_t * " raw_gen ", gnutls_datum_t * " raw_prime ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.IP "gnutls_datum_t * raw_gen" 12
will hold the generator.
.IP "gnutls_datum_t * raw_prime" 12
will hold the prime.
.SH "DESCRIPTION"
This function will return the group parameters used in the last
<PERSON><PERSON>ie\-<PERSON><PERSON> key exchange with the peer.  These are the prime and
the generator used.  This function should be used for both
anonymous and ephemeral Diffie\-Hellman.  The output parameters must
be freed with \fBgnutls_free()\fP.

Note, that the prime and generator are exported as non\-negative
integers and may include a leading zero byte.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
