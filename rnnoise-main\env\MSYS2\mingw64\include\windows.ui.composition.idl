/*
 * Copyright (C) 2023 Mo<PERSON>ad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.foundation.numerics.idl";
/* import "windows.graphics.idl"; */
import "windows.graphics.directx.idl";
import "windows.graphics.effects.idl";
import "windows.system.idl";
import "windows.ui.idl";
import "windows.ui.core.idl";

namespace Windows.UI.Composition {
    typedef enum CompositionBackfaceVisibility CompositionBackfaceVisibility;
    typedef enum CompositionBatchTypes CompositionBatchTypes;
    typedef enum CompositionBitmapInterpolationMode CompositionBitmapInterpolationMode;
    typedef enum CompositionBorderMode CompositionBorderMode;
    typedef enum CompositionColorSpace CompositionColorSpace;
    typedef enum CompositionCompositeMode CompositionCompositeMode;
    typedef enum CompositionEffectFactoryLoadStatus CompositionEffectFactoryLoadStatus;
    typedef enum CompositionGetValueStatus CompositionGetValueStatus;
    typedef enum CompositionStretch CompositionStretch;

    interface IAnimationObject;
    interface IColorKeyFrameAnimation;
    interface ICompositionAnimation;
    interface ICompositionAnimation2;
    interface ICompositionAnimation3;
    interface ICompositionAnimation4;
    interface ICompositionAnimationBase;
    interface ICompositionAnimationFactory;
    interface ICompositionBatchCompletedEventArgs;
    interface ICompositionBrush;
    interface ICompositionBrushFactory;
    interface ICompositionClip;
    interface ICompositionClip2;
    interface ICompositionClipFactory;
    interface ICompositionColorBrush;
    interface ICompositionCommitBatch;
    interface ICompositionDrawingSurface;
    interface ICompositionDrawingSurface2;
    interface ICompositionDrawingSurfaceFactory;
    interface ICompositionEasingFunction;
    interface ICompositionEasingFunctionFactory;
    interface ICompositionEasingFunctionStatics;
    interface ICompositionEffectBrush;
    interface ICompositionEffectFactory;
    interface ICompositionGraphicsDevice;
    interface ICompositionGraphicsDevice2;
    interface ICompositionGraphicsDevice3;
    interface ICompositionGraphicsDevice4;
    interface ICompositionObject;
    interface ICompositionObject2;
    interface ICompositionObject3;
    interface ICompositionObject4;
    interface ICompositionObjectFactory;
    interface ICompositionObjectStatics;
    interface ICompositionPropertySet;
    interface ICompositionPropertySet2;
    interface ICompositionScopedBatch;
    interface ICompositionSurface;
    interface ICompositionSurfaceBrush;
    interface ICompositionSurfaceBrush2;
    interface ICompositionSurfaceBrush3;
    interface ICompositionTarget;
    interface ICompositionTargetFactory;
    interface ICompositor;
    interface ICompositor2;
    interface ICompositor3;
    interface ICompositor4;
    interface ICompositor5;
    interface ICompositor6;
    interface ICompositor7;
    interface ICompositorStatics;
    interface ICompositorWithBlurredWallpaperBackdropBrush;
    interface ICompositorWithProjectedShadow;
    interface ICompositorWithRadialGradient;
    interface ICompositorWithVisualSurface;
    interface IContainerVisual;
    interface IContainerVisualFactory;
    interface ICubicBezierEasingFunction;
    interface IExpressionAnimation;
    interface IInsetClip;
    interface ILinearEasingFunction;
    interface IQuaternionKeyFrameAnimation;
    interface IRenderingDeviceReplacedEventArgs;
    interface IScalarKeyFrameAnimation;
    interface ISpriteVisual;
    interface ISpriteVisual2;
    interface IVector2KeyFrameAnimation;
    interface IVector3KeyFrameAnimation;
    interface IVector4KeyFrameAnimation;
    interface IVisual;
    interface IVisual2;
    interface IVisual3;
    interface IVisual4;
    interface IVisualCollection;
    interface IVisualFactory;

    runtimeclass ColorKeyFrameAnimation;
    runtimeclass CompositionAnimation;
    runtimeclass CompositionBatchCompletedEventArgs;
    runtimeclass CompositionBrush;
    runtimeclass CompositionClip;
    runtimeclass CompositionColorBrush;
    runtimeclass CompositionCommitBatch;
    runtimeclass CompositionDrawingSurface;
    runtimeclass CompositionEasingFunction;
    runtimeclass CompositionEffectBrush;
    runtimeclass CompositionEffectFactory;
    runtimeclass CompositionGraphicsDevice;
    runtimeclass CompositionObject;
    runtimeclass CompositionPropertySet;
    runtimeclass CompositionScopedBatch;
    runtimeclass CompositionSurfaceBrush;
    runtimeclass CompositionTarget;
    runtimeclass Compositor;
    runtimeclass ContainerVisual;
    runtimeclass CubicBezierEasingFunction;
    runtimeclass ExpressionAnimation;
    runtimeclass InsetClip;
    runtimeclass LinearEasingFunction;
    runtimeclass KeyFrameAnimation;
    runtimeclass QuaternionKeyFrameAnimation;
    runtimeclass RenderingDeviceReplacedEventArgs;
    runtimeclass ScalarKeyFrameAnimation;
    runtimeclass SpriteVisual;
    runtimeclass Vector2KeyFrameAnimation;
    runtimeclass Vector3KeyFrameAnimation;
    runtimeclass Vector4KeyFrameAnimation;
    runtimeclass Visual;
    runtimeclass VisualCollection;

    declare {
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.UI.Composition.ICompositionSurface *>;
        interface Windows.Foundation.IAsyncOperation<Windows.UI.Composition.ICompositionSurface *>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Composition.CompositionAnimation *>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Composition.Visual *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Composition.CompositionAnimation *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Composition.Visual *>;
        interface Windows.Foundation.TypedEventHandler<IInspectable *, Windows.UI.Composition.CompositionBatchCompletedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Composition.CompositionGraphicsDevice *, Windows.UI.Composition.RenderingDeviceReplacedEventArgs *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0)
    ]
    enum CompositionBackfaceVisibility
    {
        Inherit = 0,
        Visible = 1,
        Hidden  = 2,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        flags
    ]
    enum CompositionBatchTypes
    {
        None              = 0x0,
        Animation         = 0x1,
        Effect            = 0x2,
        [contract(Windows.Foundation.UniversalApiContract, 7.0)]
        InfiniteAnimation = 0x4,
        [contract(Windows.Foundation.UniversalApiContract, 7.0)]
        AllAnimations     = 0x5,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0)
    ]
    enum CompositionBitmapInterpolationMode
    {
        NearestNeighbor                = 0,
        Linear                         = 1,
        [contract(Windows.Foundation.UniversalApiContract, 8.0)]
        MagLinearMinLinearMipLinear    = 2,
        [contract(Windows.Foundation.UniversalApiContract, 8.0)]
        MagLinearMinLinearMipNearest   = 3,
        [contract(Windows.Foundation.UniversalApiContract, 8.0)]
        MagLinearMinNearestMipLinear   = 4,
        [contract(Windows.Foundation.UniversalApiContract, 8.0)]
        MagLinearMinNearestMipNearest  = 5,
        [contract(Windows.Foundation.UniversalApiContract, 8.0)]
        MagNearestMinLinearMipLinear   = 6,
        [contract(Windows.Foundation.UniversalApiContract, 8.0)]
        MagNearestMinLinearMipNearest  = 7,
        [contract(Windows.Foundation.UniversalApiContract, 8.0)]
        MagNearestMinNearestMipLinear  = 8,
        [contract(Windows.Foundation.UniversalApiContract, 8.0)]
        MagNearestMinNearestMipNearest = 9,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0)
    ]
    enum CompositionBorderMode
    {
        Inherit = 0,
        Soft    = 1,
        Hard    = 2,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0)
    ]
    enum CompositionColorSpace
    {
        Auto      = 0,
        Hsl       = 1,
        Rgb       = 2,
        [contract(Windows.Foundation.UniversalApiContract, 5.0)]
        HslLinear = 3,
        [contract(Windows.Foundation.UniversalApiContract, 5.0)]
        RgbLinear = 4,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0)
    ]
    enum CompositionCompositeMode
    {
        Inherit           = 0,
        SourceOver        = 1,
        DestinationInvert = 2,
        [contract(Windows.Foundation.UniversalApiContract, 3.0)]
        MinBlend          = 3,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0)
    ]
    enum CompositionEffectFactoryLoadStatus
    {
        Success          = 0,
        EffectTooComplex = 1,
        Pending          = 2,
        Other            = -1,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0)
    ]
    enum CompositionGetValueStatus
    {
        Succeeded    = 0,
        TypeMismatch = 1,
        NotFound     = 2,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0)
    ]
    enum CompositionStretch
    {
        None          = 0,
        Fill          = 1,
        Uniform       = 2,
        UniformToFill = 3,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.ColorKeyFrameAnimation),
        uuid(93adb5e9-8e05-4593-84a3-dca152781e56)
    ]
    interface IColorKeyFrameAnimation : IInspectable
    {
        [propget] HRESULT InterpolationColorSpace([out, retval] Windows.UI.Composition.CompositionColorSpace *value);
        [propput] HRESULT InterpolationColorSpace([in] Windows.UI.Composition.CompositionColorSpace value);
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrame([in] FLOAT key, [in] Windows.UI.Color value);
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrameWithEasingFunction(
            [in] FLOAT key, [in] Windows.UI.Color value,
            [in] Windows.UI.Composition.CompositionEasingFunction *function
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionAnimation),
        uuid(464c4c2c-1caa-4061-9b40-e13fde1503ca)
    ]
    interface ICompositionAnimation : IInspectable
    {
        HRESULT ClearAllParameters();
        HRESULT ClearParameter([in] HSTRING key);
        HRESULT SetColorParameter([in] HSTRING key, [in] Windows.UI.Color value);
        HRESULT SetMatrix3x2Parameter([in] HSTRING key, [in] Windows.Foundation.Numerics.Matrix3x2 value);
        HRESULT SetMatrix4x4Parameter([in] HSTRING key, [in] Windows.Foundation.Numerics.Matrix4x4 value);
        HRESULT SetQuaternionParameter([in] HSTRING key, [in] Windows.Foundation.Numerics.Quaternion value);
        HRESULT SetReferenceParameter([in] HSTRING key, [in] Windows.UI.Composition.CompositionObject *object);
        HRESULT SetScalarParameter([in] HSTRING key, [in] FLOAT value);
        HRESULT SetVector2Parameter([in] HSTRING key, [in] Windows.Foundation.Numerics.Vector2 value);
        HRESULT SetVector3Parameter([in] HSTRING key, [in] Windows.Foundation.Numerics.Vector3 value);
        HRESULT SetVector4Parameter([in] HSTRING key, [in] Windows.Foundation.Numerics.Vector4 value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        uuid(1c2c2999-e818-48d3-a6dd-d78c82f8ace9)
    ]
    interface ICompositionAnimationBase : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionAnimation),
        uuid(10f6c4fb-6e51-4c25-bbd3-586a9bec3ef4)
    ]
    interface ICompositionAnimationFactory : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionObject),
        uuid(bcb4ad45-7609-4550-934f-16002a68fded)
    ]
    interface ICompositionObject : IInspectable
    {
        [propget] HRESULT Compositor([out, retval] Windows.UI.Composition.Compositor **value);
        [propget] HRESULT Dispatcher([out, retval] Windows.UI.Core.CoreDispatcher **value);
        [propget] HRESULT Properties([out, retval] Windows.UI.Composition.CompositionPropertySet **value);
        HRESULT StartAnimation([in] HSTRING name, [in] Windows.UI.Composition.CompositionAnimation *animation);
        HRESULT StopAnimation([in] HSTRING name);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionObject),
        uuid(51205c5e-558a-4f2a-8d39-37bfe1e20ddd)
    ]
    interface ICompositionObjectFactory : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionBatchCompletedEventArgs),
        uuid(0d00dad0-9464-450a-a562-2e2698b0a812)
    ]
    interface ICompositionBatchCompletedEventArgs : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionBrush),
        uuid(ab0d7608-30c0-40e9-b568-b60a6bd1fb46)
    ]
    interface ICompositionBrush : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionBrush),
        uuid(da53fb4c-4650-47c4-ad76-765379607ed6)
    ]
    interface ICompositionBrushFactory : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionClip),
        uuid(1ccd2a52-cfc7-4ace-9983-146bb8eb6a3c)
    ]
    interface ICompositionClip : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionClip),
        uuid(b9484caf-20c7-4aed-ac4a-9c78ba1302cf)
    ]
    interface ICompositionClipFactory : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionColorBrush),
        uuid(2b264c5e-bf35-4831-8642-cf70c20fff2f)
    ]
    interface ICompositionColorBrush : IInspectable
    {
        [propget] HRESULT Color([out, retval] Windows.UI.Color *value);
        [propput] HRESULT Color([in] Windows.UI.Color value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionCommitBatch),
        uuid(0d00dad0-ca07-4400-8c8e-cb5db08559cc)
    ]
    interface ICompositionCommitBatch : IInspectable
    {
        [propget] HRESULT IsActive([out, retval] boolean *value);
        [propget] HRESULT IsEnded([out, retval] boolean *value);
        [eventadd] HRESULT Completed(
            [in] Windows.Foundation.TypedEventHandler<IInspectable *, Windows.UI.Composition.CompositionBatchCompletedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *token
        );
        [eventremove] HRESULT Completed([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionDrawingSurface),
        uuid(a166c300-fad0-4d11-9e67-e433162ff49e)
    ]
    interface ICompositionDrawingSurface : IInspectable
    {
        [propget] HRESULT AlphaMode([out, retval] Windows.Graphics.DirectX.DirectXAlphaMode *value);
        [propget] HRESULT PixelFormat([out, retval] Windows.Graphics.DirectX.DirectXPixelFormat *value);
        [propget] HRESULT Size([out, retval] Windows.Foundation.Size *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Composition.CompositionDrawingSurface),
        uuid(9497b00a-312d-46b9-9db3-412fd79464c8)
    ]
    interface ICompositionDrawingSurfaceFactory : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionEasingFunction),
        uuid(5145e356-bf79-4ea8-8cc2-6b5b472e6c9a)
    ]
    interface ICompositionEasingFunction : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionEasingFunction),
        uuid(60840774-3da0-4949-8200-7206c00190a0)
    ]
    interface ICompositionEasingFunctionFactory : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionEffectBrush),
        uuid(bf7f795e-83cc-44bf-a447-3e3c071789ec)
    ]
    interface ICompositionEffectBrush : IInspectable
    {
        HRESULT GetSourceParameter([in] HSTRING name, [out, retval] Windows.UI.Composition.CompositionBrush **result);
        HRESULT SetSourceParameter([in] HSTRING name, [in] Windows.UI.Composition.CompositionBrush *source);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionEffectFactory),
        uuid(be5624af-ba7e-4510-9850-41c0b4ff74df)
    ]
    interface ICompositionEffectFactory : IInspectable
    {
        HRESULT CreateBrush([out, retval] Windows.UI.Composition.CompositionEffectBrush **result);
        [propget] HRESULT ExtendedError([out, retval] HRESULT *value);
        [propget] HRESULT LoadStatus([out, retval] Windows.UI.Composition.CompositionEffectFactoryLoadStatus *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionGraphicsDevice),
        uuid(fb22c6e1-80a2-4667-9936-dbeaf6eefe95)
    ]
    interface ICompositionGraphicsDevice : IInspectable
    {
        HRESULT CreateDrawingSurface(
            [in] Windows.Foundation.Size pixels, [in] Windows.Graphics.DirectX.DirectXPixelFormat format,
            [in] Windows.Graphics.DirectX.DirectXAlphaMode mode, [out, retval] Windows.UI.Composition.CompositionDrawingSurface **result
        );
        [eventadd] HRESULT RenderingDeviceReplaced(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.Composition.CompositionGraphicsDevice *, Windows.UI.Composition.RenderingDeviceReplacedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *token
        );
        [eventremove] HRESULT RenderingDeviceReplaced([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionPropertySet),
        uuid(c9d6d202-5f67-4453-9117-9eadd430d3c2)
    ]
    interface ICompositionPropertySet : IInspectable
    {
        HRESULT InsertColor([in] HSTRING name, [in] Windows.UI.Color value);
        HRESULT InsertMatrix3x2([in] HSTRING name, [in] Windows.Foundation.Numerics.Matrix3x2 value);
        HRESULT InsertMatrix4x4([in] HSTRING name, [in] Windows.Foundation.Numerics.Matrix4x4 value);
        HRESULT InsertQuaternion([in] HSTRING name, [in] Windows.Foundation.Numerics.Quaternion value);
        HRESULT InsertScalar([in] HSTRING name, [in] FLOAT value);
        HRESULT InsertVector2([in] HSTRING name, [in] Windows.Foundation.Numerics.Vector2 value);
        HRESULT InsertVector3([in] HSTRING name, [in] Windows.Foundation.Numerics.Vector3 value);
        HRESULT InsertVector4([in] HSTRING name, [in] Windows.Foundation.Numerics.Vector4 value);
        HRESULT TryGetColor([in] HSTRING name, [out] Windows.UI.Color *value, [out, retval] Windows.UI.Composition.CompositionGetValueStatus *result);
        HRESULT TryGetMatrix3x2(
            [in] HSTRING name, [out] Windows.Foundation.Numerics.Matrix3x2 *value,
            [out, retval] Windows.UI.Composition.CompositionGetValueStatus *result
        );
        HRESULT TryGetMatrix4x4(
            [in] HSTRING name, [out] Windows.Foundation.Numerics.Matrix4x4 *value,
            [out, retval] Windows.UI.Composition.CompositionGetValueStatus *result
        );
        HRESULT TryGetQuaternion(
            [in] HSTRING name, [out] Windows.Foundation.Numerics.Quaternion *value,
            [out, retval] Windows.UI.Composition.CompositionGetValueStatus *result
        );
        HRESULT TryGetScalar([in] HSTRING name, [out] FLOAT *value, [out, retval] Windows.UI.Composition.CompositionGetValueStatus *result);
        HRESULT TryGetVector2(
            [in] HSTRING name, [out] Windows.Foundation.Numerics.Vector2 *value,
            [out, retval] Windows.UI.Composition.CompositionGetValueStatus *result
        );
        HRESULT TryGetVector3(
            [in] HSTRING name, [out] Windows.Foundation.Numerics.Vector3 *value,
            [out, retval] Windows.UI.Composition.CompositionGetValueStatus *result
        );
        HRESULT TryGetVector4(
            [in] HSTRING name, [out] Windows.Foundation.Numerics.Vector4 *value,
            [out, retval] Windows.UI.Composition.CompositionGetValueStatus *result
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionScopedBatch),
        uuid(0d00dad0-fb07-46fd-8c72-6280d1a3d1dd)
    ]
    interface ICompositionScopedBatch : IInspectable
    {
        [propget] HRESULT IsActive([out, retval] boolean *value);
        [propget] HRESULT IsEnded([out, retval] boolean *value);
        HRESULT End();
        HRESULT Resume();
        HRESULT Suspend();
        [eventadd] HRESULT Completed(
            [in] Windows.Foundation.TypedEventHandler<IInspectable *, Windows.UI.Composition.CompositionBatchCompletedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *token
        );
        [eventremove] HRESULT Completed([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        uuid(1527540d-42c7-47a6-a408-668f79a90dfb)
    ]
    interface ICompositionSurface : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionSurfaceBrush),
        uuid(ad016d79-1e4c-4c0d-9c29-83338c87c162)
    ]
    interface ICompositionSurfaceBrush : IInspectable
    {
        [propget] HRESULT BitmapInterpolationMode([out, retval] Windows.UI.Composition.CompositionBitmapInterpolationMode *value);
        [propput] HRESULT BitmapInterpolationMode([in] Windows.UI.Composition.CompositionBitmapInterpolationMode value);
        [propget] HRESULT HorizontalAlignmentRatio([out, retval] FLOAT *value);
        [propput] HRESULT HorizontalAlignmentRatio([in] FLOAT value);
        [propget] HRESULT Stretch([out, retval] Windows.UI.Composition.CompositionStretch *value);
        [propput] HRESULT Stretch([in] Windows.UI.Composition.CompositionStretch value);
        [propget] HRESULT Surface([out, retval] Windows.UI.Composition.ICompositionSurface **value);
        [propput] HRESULT Surface([in] Windows.UI.Composition.ICompositionSurface *value);
        [propget] HRESULT VerticalAlignmentRatio([out, retval] FLOAT *value);
        [propput] HRESULT VerticalAlignmentRatio([in] FLOAT value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CompositionTarget),
        uuid(a1bea8ba-d726-4663-8129-6b5e7927ffa6)
    ]
    interface ICompositionTarget : IInspectable
    {
        [propget] HRESULT Root([out, retval] Windows.UI.Composition.Visual **value);
        [propput] HRESULT Root([in] Windows.UI.Composition.Visual *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        exclusiveto(Windows.UI.Composition.CompositionTarget),
        uuid(93cd9d2b-8516-4b14-a8ce-f49e2119ec42)
    ]
    interface ICompositionTargetFactory : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.Compositor),
        uuid(b403ca50-7f8c-4e83-985f-cc45060036d8)
    ]
    interface ICompositor : IInspectable
    {
        HRESULT CreateColorKeyFrameAnimation([out, retval] Windows.UI.Composition.ColorKeyFrameAnimation **result);
        [overload("CreateColorBrush")] HRESULT CreateColorBrush([out, retval] Windows.UI.Composition.CompositionColorBrush **result);
        [overload("CreateColorBrush")] HRESULT CreateColorBrushWithColor(
            [in] Windows.UI.Color color, [out, retval] Windows.UI.Composition.CompositionColorBrush **result
        );
        HRESULT CreateContainerVisual([out, retval] Windows.UI.Composition.ContainerVisual **result);
        HRESULT CreateCubicBezierEasingFunction(
            [in] Windows.Foundation.Numerics.Vector2 point1, [in] Windows.Foundation.Numerics.Vector2 point2,
            [out, retval] Windows.UI.Composition.CubicBezierEasingFunction **result
        );
        [overload("CreateEffectFactory")] HRESULT CreateEffectFactory(
            [in] Windows.Graphics.Effects.IGraphicsEffect *effect, [out, retval] Windows.UI.Composition.CompositionEffectFactory **result
        );
        [overload("CreateEffectFactory")] HRESULT CreateEffectFactoryWithProperties(
            [in] Windows.Graphics.Effects.IGraphicsEffect *effect, [in] Windows.Foundation.Collections.IIterable<HSTRING> *animatable,
            [out, retval] Windows.UI.Composition.CompositionEffectFactory **result
        );
        [overload("CreateExpressionAnimation")] HRESULT CreateExpressionAnimation([out, retval] Windows.UI.Composition.ExpressionAnimation **result);
        [overload("CreateExpressionAnimation")] HRESULT CreateExpressionAnimationWithExpression(
            [in] HSTRING expression, [out, retval] Windows.UI.Composition.ExpressionAnimation **result
        );
        [overload("CreateInsetClip")] HRESULT CreateInsetClip([out, retval] Windows.UI.Composition.InsetClip **result);
        [overload("CreateInsetClip")] HRESULT CreateInsetClipWithInsets(
            [in] FLOAT left, [in] FLOAT top, [in] FLOAT right, [in] FLOAT bottom,
            [out, retval] Windows.UI.Composition.InsetClip **result
        );
        HRESULT CreateLinearEasingFunction([out, retval] Windows.UI.Composition.LinearEasingFunction **result);
        HRESULT CreatePropertySet([out, retval] Windows.UI.Composition.CompositionPropertySet **result);
        HRESULT CreateQuaternionKeyFrameAnimation([out, retval] Windows.UI.Composition.QuaternionKeyFrameAnimation **result);
        HRESULT CreateScalarKeyFrameAnimation([out, retval] Windows.UI.Composition.ScalarKeyFrameAnimation **result);
        HRESULT CreateScopedBatch(
            [in] Windows.UI.Composition.CompositionBatchTypes type, [out, retval] Windows.UI.Composition.CompositionScopedBatch **result
        );
        HRESULT CreateSpriteVisual([out, retval] Windows.UI.Composition.SpriteVisual **result);
        [overload("CreateSurfaceBrush")] HRESULT CreateSurfaceBrush([out, retval] Windows.UI.Composition.CompositionSurfaceBrush **result);
        [overload("CreateSurfaceBrush")] HRESULT CreateSurfaceBrushWithSurface(
            [in] Windows.UI.Composition.ICompositionSurface *surface, [out, retval] Windows.UI.Composition.CompositionSurfaceBrush **result
        );
        HRESULT CreateTargetForCurrentView([out, retval] Windows.UI.Composition.CompositionTarget **result);
        HRESULT CreateVector2KeyFrameAnimation([out, retval] Windows.UI.Composition.Vector2KeyFrameAnimation **result);
        HRESULT CreateVector3KeyFrameAnimation([out, retval] Windows.UI.Composition.Vector3KeyFrameAnimation **result);
        HRESULT CreateVector4KeyFrameAnimation([out, retval] Windows.UI.Composition.Vector4KeyFrameAnimation **result);
        HRESULT GetCommitBatch(
            [in] Windows.UI.Composition.CompositionBatchTypes type, [out, retval] Windows.UI.Composition.CompositionCommitBatch **result
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.ContainerVisual),
        uuid(02f6bc74-ed20-4773-afe6-d49b4a93db32)
    ]
    interface IContainerVisual : IInspectable
    {
        [propget] HRESULT Children([out, retval] Windows.UI.Composition.VisualCollection **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.ContainerVisual),
        uuid(0363a65b-c7da-4d9a-95f4-69b5c8df670b)
    ]
    interface IContainerVisualFactory : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.CubicBezierEasingFunction),
        uuid(32350666-c1e8-44f9-96b8-c98acf0ae698)
    ]
    interface ICubicBezierEasingFunction : IInspectable
    {
        [propget] HRESULT ControlPoint1([out, retval] Windows.Foundation.Numerics.Vector2 *value);
        [propget] HRESULT ControlPoint2([out, retval] Windows.Foundation.Numerics.Vector2 *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.ExpressionAnimation),
        uuid(6acc5431-7d3d-4bf3-abb6-f44bdc4888c1)
    ]
    interface IExpressionAnimation : IInspectable
    {
        [propget] HRESULT Expression([out, retval] HSTRING *value);
        [propput] HRESULT Expression([in] HSTRING value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.InsetClip),
        uuid(1e73e647-84c7-477a-b474-5880e0442e15)
    ]
    interface IInsetClip : IInspectable
    {
        [propget] HRESULT BottomInset([out, retval] FLOAT *value);
        [propput] HRESULT BottomInset([in] FLOAT value);
        [propget] HRESULT LeftInset([out, retval] FLOAT *value);
        [propput] HRESULT LeftInset([in] FLOAT value);
        [propget] HRESULT RightInset([out, retval] FLOAT *value);
        [propput] HRESULT RightInset([in] FLOAT value);
        [propget] HRESULT TopInset([out, retval] FLOAT *value);
        [propput] HRESULT TopInset([in] FLOAT value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.LinearEasingFunction),
        uuid(9400975a-c7a6-46b3-acf7-1a268a0a117d)
    ]
    interface ILinearEasingFunction : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.QuaternionKeyFrameAnimation),
        uuid(404e5835-ecf6-4240-8520-671279cf36bc)
    ]
    interface IQuaternionKeyFrameAnimation : IInspectable
    {
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrame([in] FLOAT key, [in] Windows.Foundation.Numerics.Quaternion value);
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrameWithEasingFunction(
            [in] FLOAT key, [in] Windows.Foundation.Numerics.Quaternion value,
            [in] Windows.UI.Composition.CompositionEasingFunction *function
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.RenderingDeviceReplacedEventArgs),
        uuid(3a31ac7d-28bf-4e7a-8524-71679d480f38)
    ]
    interface IRenderingDeviceReplacedEventArgs : IInspectable
    {
        [propget] HRESULT GraphicsDevice([out, retval] Windows.UI.Composition.CompositionGraphicsDevice **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.ScalarKeyFrameAnimation),
        uuid(ae288fa9-252c-4b95-a725-bf85e38000a1)
    ]
    interface IScalarKeyFrameAnimation : IInspectable
    {
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrame([in] FLOAT key, [in] FLOAT value);
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrameWithEasingFunction(
            [in] FLOAT key, [in] FLOAT value,
            [in] Windows.UI.Composition.CompositionEasingFunction *function
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.SpriteVisual),
        uuid(08e05581-1ad1-4f97-9757-402d76e4233b)
    ]
    interface ISpriteVisual : IInspectable
    {
        [propget] HRESULT Brush([out, retval] Windows.UI.Composition.CompositionBrush **value);
        [propput] HRESULT Brush([in] Windows.UI.Composition.CompositionBrush *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.Vector2KeyFrameAnimation),
        uuid(df414515-4e29-4f11-b55e-bf2a6eb36294)
    ]
    interface IVector2KeyFrameAnimation : IInspectable
    {
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrame([in] FLOAT key, [in] Windows.Foundation.Numerics.Vector2 value);
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrameWithEasingFunction(
            [in] FLOAT key, [in] Windows.Foundation.Numerics.Vector2 value,
            [in] Windows.UI.Composition.CompositionEasingFunction *function
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.Vector3KeyFrameAnimation),
        uuid(c8039daa-a281-43c2-a73d-b68e3c533c40)
    ]
    interface IVector3KeyFrameAnimation : IInspectable
    {
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrame([in] FLOAT key, [in] Windows.Foundation.Numerics.Vector3 value);
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrameWithEasingFunction(
            [in] FLOAT key, [in] Windows.Foundation.Numerics.Vector3 value,
            [in] Windows.UI.Composition.CompositionEasingFunction *function
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.Vector4KeyFrameAnimation),
        uuid(2457945b-addd-4385-9606-b6a3d5e4e1b9)
    ]
    interface IVector4KeyFrameAnimation : IInspectable
    {
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrame([in] FLOAT key, [in] Windows.Foundation.Numerics.Vector4 value);
        [overload("InsertKeyFrame")] HRESULT InsertKeyFrameWithEasingFunction(
            [in] FLOAT key, [in] Windows.Foundation.Numerics.Vector4 value,
            [in] Windows.UI.Composition.CompositionEasingFunction *function
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.Visual),
        uuid(117e202d-a859-4c89-873b-c2aa566788e3)
    ]
    interface IVisual : IInspectable
    {
        [propget] HRESULT AnchorPoint([out, retval] Windows.Foundation.Numerics.Vector2 *value);
        [propput] HRESULT AnchorPoint([in] Windows.Foundation.Numerics.Vector2 value);
        [propget] HRESULT BackfaceVisibility([out, retval] Windows.UI.Composition.CompositionBackfaceVisibility *value);
        [propput] HRESULT BackfaceVisibility([in] Windows.UI.Composition.CompositionBackfaceVisibility value);
        [propget] HRESULT BorderMode([out, retval] Windows.UI.Composition.CompositionBorderMode *value);
        [propput] HRESULT BorderMode([in] Windows.UI.Composition.CompositionBorderMode value);
        [propget] HRESULT CenterPoint([out, retval] Windows.Foundation.Numerics.Vector3 *value);
        [propput] HRESULT CenterPoint([in] Windows.Foundation.Numerics.Vector3 value);
        [propget] HRESULT Clip([out, retval] Windows.UI.Composition.CompositionClip **value);
        [propput] HRESULT Clip([in] Windows.UI.Composition.CompositionClip *value);
        [propget] HRESULT CompositeMode([out, retval] Windows.UI.Composition.CompositionCompositeMode *value);
        [propput] HRESULT CompositeMode([in] Windows.UI.Composition.CompositionCompositeMode value);
        [propget] HRESULT IsVisible([out, retval] boolean *value);
        [propput] HRESULT IsVisible([in] boolean value);
        [propget] HRESULT Offset([out, retval] Windows.Foundation.Numerics.Vector3 *value);
        [propput] HRESULT Offset([in] Windows.Foundation.Numerics.Vector3 value);
        [propget] HRESULT Opacity([out, retval] FLOAT *value);
        [propput] HRESULT Opacity([in] FLOAT value);
        [propget] HRESULT Orientation([out, retval] Windows.Foundation.Numerics.Quaternion *value);
        [propput] HRESULT Orientation([in] Windows.Foundation.Numerics.Quaternion value);
        [propget] HRESULT Parent([out, retval] Windows.UI.Composition.ContainerVisual **value);
        [propget] HRESULT RotationAngle([out, retval] FLOAT *value);
        [propput] HRESULT RotationAngle([in] FLOAT value);
        [propget] HRESULT RotationAngleInDegrees([out, retval] FLOAT *value);
        [propput] HRESULT RotationAngleInDegrees([in] FLOAT value);
        [propget] HRESULT RotationAxis([out, retval] Windows.Foundation.Numerics.Vector3 *value);
        [propput] HRESULT RotationAxis([in] Windows.Foundation.Numerics.Vector3 value);
        [propget] HRESULT Scale([out, retval] Windows.Foundation.Numerics.Vector3 *value);
        [propput] HRESULT Scale([in] Windows.Foundation.Numerics.Vector3 value);
        [propget] HRESULT Size([out, retval] Windows.Foundation.Numerics.Vector2 *value);
        [propput] HRESULT Size([in] Windows.Foundation.Numerics.Vector2 value);
        [propget] HRESULT TransformMatrix([out, retval] Windows.Foundation.Numerics.Matrix4x4 *value);
        [propput] HRESULT TransformMatrix([in] Windows.Foundation.Numerics.Matrix4x4 value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.Visual),
        uuid(ad0ff93e-b502-4eb5-87b4-9a38a71d0137)
    ]
    interface IVisualFactory : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.UI.Composition.VisualCollection),
        uuid(8b745505-fd3e-4a98-84a8-e949468c6bcb)
    ]
    interface IVisualCollection : IInspectable
    {
        [propget] HRESULT Count([out, retval] INT32 *value);
        HRESULT InsertAbove([in] Windows.UI.Composition.Visual *child, [in] Windows.UI.Composition.Visual *sibling);
        HRESULT InsertAtBottom([in] Windows.UI.Composition.Visual *child);
        HRESULT InsertAtTop([in] Windows.UI.Composition.Visual *child);
        HRESULT InsertBelow([in] Windows.UI.Composition.Visual *child, [in] Windows.UI.Composition.Visual *sibling);
        HRESULT Remove([in] Windows.UI.Composition.Visual *child);
        HRESULT RemoveAll();
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass ColorKeyFrameAnimation : Windows.UI.Composition.KeyFrameAnimation
    {
        [default] interface Windows.UI.Composition.IColorKeyFrameAnimation;
    }

    [
        composable(Windows.UI.Composition.ICompositionAnimationFactory, public, Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionAnimation : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionAnimation;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Composition.ICompositionAnimation2;
        [contract(Windows.Foundation.UniversalApiContract, 5.0)] interface Windows.UI.Composition.ICompositionAnimation3;
        [contract(Windows.Foundation.UniversalApiContract, 7.0)] interface Windows.UI.Composition.ICompositionAnimation4;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Composition.ICompositionAnimationBase;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionBatchCompletedEventArgs : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionBatchCompletedEventArgs;
    }

    [
        composable(Windows.UI.Composition.ICompositionBrushFactory, public, Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionBrush : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionBrush;
    }

    [
        composable(Windows.UI.Composition.ICompositionClipFactory, public, Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionClip : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionClip;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Composition.ICompositionClip2;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionColorBrush : Windows.UI.Composition.CompositionBrush
    {
        [default] interface Windows.UI.Composition.ICompositionColorBrush;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionCommitBatch : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionCommitBatch;
    }

    [
        composable(Windows.UI.Composition.ICompositionDrawingSurfaceFactory, public, Windows.Foundation.UniversalApiContract, 4.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionDrawingSurface : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionDrawingSurface;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.Composition.ICompositionDrawingSurface2;
        interface Windows.UI.Composition.ICompositionSurface;
    }

    [
        composable(Windows.UI.Composition.ICompositionEasingFunctionFactory, public, Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        static(Windows.UI.Composition.ICompositionEasingFunctionStatics, Windows.Foundation.UniversalApiContract, 12.0),
        threading(both)
    ]
    runtimeclass CompositionEasingFunction : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionEasingFunction;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionEffectBrush : Windows.UI.Composition.CompositionBrush
    {
        [default] interface Windows.UI.Composition.ICompositionEffectBrush;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionEffectFactory : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionEffectFactory;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionGraphicsDevice : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionGraphicsDevice;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.Composition.ICompositionGraphicsDevice2;
        [contract(Windows.Foundation.UniversalApiContract, 8.0)] interface Windows.UI.Composition.ICompositionGraphicsDevice3;
        [contract(Windows.Foundation.UniversalApiContract, 12.0)] interface Windows.UI.Composition.ICompositionGraphicsDevice4;
    }

    [
        composable(Windows.UI.Composition.ICompositionObjectFactory, public, Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        static(Windows.UI.Composition.ICompositionObjectStatics, Windows.Foundation.UniversalApiContract, 7.0),
        threading(both)
    ]
    runtimeclass CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionObject;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Composition.ICompositionObject2;
        [contract(Windows.Foundation.UniversalApiContract, 5.0)] interface Windows.UI.Composition.ICompositionObject3;
        [contract(Windows.Foundation.UniversalApiContract, 6.0)] interface Windows.UI.Composition.ICompositionObject4;
        interface Windows.Foundation.IClosable;
        [contract(Windows.Foundation.UniversalApiContract, 7.0)] interface Windows.UI.Composition.IAnimationObject;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionPropertySet : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionPropertySet;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Composition.ICompositionPropertySet2;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionScopedBatch : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionScopedBatch;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionSurfaceBrush : Windows.UI.Composition.CompositionBrush
    {
        [default] interface Windows.UI.Composition.ICompositionSurfaceBrush;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Composition.ICompositionSurfaceBrush2;
        [contract(Windows.Foundation.UniversalApiContract, 8.0)] interface Windows.UI.Composition.ICompositionSurfaceBrush3;
    }

    [
        composable(Windows.UI.Composition.ICompositionTargetFactory, public, Windows.Foundation.UniversalApiContract, 5.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CompositionTarget : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.ICompositionTarget;
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        static(Windows.UI.Composition.ICompositorStatics, Windows.Foundation.UniversalApiContract, 6.0),
        threading(both)
    ]
    runtimeclass Compositor
    {
        [default] interface Windows.UI.Composition.ICompositor;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Composition.ICompositor2;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.Composition.ICompositor3;
        [contract(Windows.Foundation.UniversalApiContract, 5.0)] interface Windows.UI.Composition.ICompositor4;
        [contract(Windows.Foundation.UniversalApiContract, 6.0)] interface Windows.UI.Composition.ICompositor5;
        [contract(Windows.Foundation.UniversalApiContract, 7.0)] interface Windows.UI.Composition.ICompositor6;
        [contract(Windows.Foundation.UniversalApiContract, 8.0)] interface Windows.UI.Composition.ICompositorWithProjectedShadow;
        [contract(Windows.Foundation.UniversalApiContract, 8.0)] interface Windows.UI.Composition.ICompositorWithRadialGradient;
        [contract(Windows.Foundation.UniversalApiContract, 8.0)] interface Windows.UI.Composition.ICompositorWithVisualSurface;
        [contract(Windows.Foundation.UniversalApiContract, 12.0)] interface Windows.UI.Composition.ICompositor7;
        [contract(Windows.Foundation.UniversalApiContract, 13.0)] interface Windows.UI.Composition.ICompositorWithBlurredWallpaperBackdropBrush;
        interface Windows.Foundation.IClosable;
    }

    [
        composable(Windows.UI.Composition.IContainerVisualFactory, public, Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass ContainerVisual : Windows.UI.Composition.Visual
    {
        [default] interface Windows.UI.Composition.IContainerVisual;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CubicBezierEasingFunction : Windows.UI.Composition.CompositionEasingFunction
    {
        [default] interface Windows.UI.Composition.ICubicBezierEasingFunction;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass ExpressionAnimation : Windows.UI.Composition.CompositionAnimation
    {
        [default] interface Windows.UI.Composition.IExpressionAnimation;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass InsetClip : Windows.UI.Composition.CompositionClip
    {
        [default] interface Windows.UI.Composition.IInsetClip;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass LinearEasingFunction : Windows.UI.Composition.CompositionEasingFunction
    {
        [default] interface Windows.UI.Composition.ILinearEasingFunction;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass QuaternionKeyFrameAnimation : Windows.UI.Composition.KeyFrameAnimation
    {
        [default] interface Windows.UI.Composition.IQuaternionKeyFrameAnimation;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass RenderingDeviceReplacedEventArgs : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.IRenderingDeviceReplacedEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass ScalarKeyFrameAnimation : Windows.UI.Composition.KeyFrameAnimation
    {
        [default] interface Windows.UI.Composition.IScalarKeyFrameAnimation;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass SpriteVisual : Windows.UI.Composition.ContainerVisual
    {
        [default] interface Windows.UI.Composition.ISpriteVisual;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Composition.ISpriteVisual2;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass Vector2KeyFrameAnimation : Windows.UI.Composition.KeyFrameAnimation
    {
        [default] interface Windows.UI.Composition.IVector2KeyFrameAnimation;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass Vector3KeyFrameAnimation : Windows.UI.Composition.KeyFrameAnimation
    {
        [default] interface Windows.UI.Composition.IVector3KeyFrameAnimation;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass Vector4KeyFrameAnimation : Windows.UI.Composition.KeyFrameAnimation
    {
        [default] interface Windows.UI.Composition.IVector4KeyFrameAnimation;
    }

    [
        composable(Windows.UI.Composition.IVisualFactory, public, Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass Visual : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.IVisual;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.Composition.IVisual2;
        [contract(Windows.Foundation.UniversalApiContract, 11.0)] interface Windows.UI.Composition.IVisual3;
        [contract(Windows.Foundation.UniversalApiContract, 12.0)] interface Windows.UI.Composition.IVisual4;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass VisualCollection : Windows.UI.Composition.CompositionObject
    {
        [default] interface Windows.UI.Composition.IVisualCollection;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Composition.Visual *>;
    }
}
