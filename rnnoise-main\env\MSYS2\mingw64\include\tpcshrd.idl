/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

import "wtypes.idl";

cpp_quote("#define TABLET_DISABLE_PRESSANDHOLD        0x00000001")
cpp_quote("#define TABLET_DISABLE_PENTAPFEEDBACK      0x00000008")
cpp_quote("#define TABLET_DISABLE_PENBARRELFEEDBACK   0x00000010")
cpp_quote("#define TABLET_DISABLE_TOUCHUIFORCEON      0x00000100")
cpp_quote("#define TABLET_DISABLE_TOUCHUIFORCEOFF     0x00000200")
cpp_quote("#define TABLET_DISABLE_TOUCHSWITCH         0x00008000")
cpp_quote("#define TABLET_DISABLE_FLICKS              0x00010000")
cpp_quote("#define TABLET_ENABLE_FLICKSONCONTEXT      0x00020000")
cpp_quote("#define TABLET_ENABLE_FLICKLEARNINGMODE    0x00040000")
cpp_quote("#define TABLET_DISABLE_SMOOTHSCROLLING     0x00080000")
cpp_quote("#define TABLET_DISABLE_FLICKFALLBACKKEYS   0x00100000")
cpp_quote("#define TABLET_ENABLE_MULTITOUCHDATA       0x01000000")

cpp_quote("#define WM_TABLET_QUERYSYSTEMGESTURESTATUS 0x02CC")

cpp_quote("#define IP_CURSOR_DOWN  0x1")
cpp_quote("#define IP_INVERTED     0x2")
cpp_quote("#define IP_MARGIN       0x4")

typedef DWORD CURSOR_ID;
typedef USHORT SYSTEM_EVENT;
typedef DWORD TABLET_CONTEXT_ID;

cpp_quote("#ifndef _XFORM_")
cpp_quote("#define _XFORM_")
typedef struct tagXFORM {
    float eM11;
    float eM12;
    float eM21;
    float eM22;
    float eDx;
    float eDy;
} XFORM;
cpp_quote("#endif")
