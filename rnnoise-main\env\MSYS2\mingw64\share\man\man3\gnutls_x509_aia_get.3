.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_aia_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_aia_get \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_aia_get(gnutls_x509_aia_t " aia ", unsigned int " seq ", gnutls_datum_t * " oid ", unsigned * " san_type ", gnutls_datum_t * " san ");"
.SH ARGUMENTS
.IP "gnutls_x509_aia_t aia" 12
The authority info access
.IP "unsigned int seq" 12
specifies the sequence number of the access descriptor (0 for the first one, 1 for the second etc.)
.IP "gnutls_datum_t * oid" 12
the type of available data; to be treated as constant.
.IP "unsigned * san_type" 12
Will hold the type of the name of \fBgnutls_subject_alt_names_t\fP (may be null).
.IP "gnutls_datum_t * san" 12
the access location name; to be treated as constant (may be null).
.SH "DESCRIPTION"
This function reads from the Authority Information Access type.

The  \fIseq\fP input parameter is used to indicate which member of the
sequence the caller is interested in.  The first member is 0, the
second member 1 and so on.  When the  \fIseq\fP value is out of bounds,
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned.

Typically  \fIoid\fP is \fBGNUTLS_OID_AD_CAISSUERS\fP or \fBGNUTLS_OID_AD_OCSP\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
