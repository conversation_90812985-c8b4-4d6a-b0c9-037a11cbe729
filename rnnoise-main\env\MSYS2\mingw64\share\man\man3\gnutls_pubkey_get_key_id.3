.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_get_key_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_get_key_id \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_get_key_id(gnutls_pubkey_t " key ", unsigned int " flags ", unsigned char * " output_data ", size_t * " output_data_size ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
Holds the public key
.IP "unsigned int flags" 12
should be one of the flags from \fBgnutls_keyid_flags_t\fP
.IP "unsigned char * output_data" 12
will contain the key ID
.IP "size_t * output_data_size" 12
holds the size of output_data (and will be
replaced by the actual size of parameters)
.SH "DESCRIPTION"
This function will return a unique ID that depends on the public
key parameters. This ID can be used in checking whether a
certificate corresponds to the given public key.

If the buffer provided is not long enough to hold the output, then
*output_data_size is updated and \fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP will
be returned.  The output will normally be a SHA\-1 hash output,
which is 20 bytes.
.SH "RETURNS"
In case of failure a negative error code will be
returned, and 0 on success.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
