.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_free_client_credentials" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_free_client_credentials \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_psk_free_client_credentials(gnutls_psk_client_credentials_t " sc ");"
.SH ARGUMENTS
.IP "gnutls_psk_client_credentials_t sc" 12
is a \fBgnutls_psk_client_credentials_t\fP type.
.SH "DESCRIPTION"
Free a gnutls_psk_client_credentials_t structure.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
