.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_verify_hash2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_verify_hash2 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_verify_hash2(gnutls_pubkey_t " key ", gnutls_sign_algorithm_t " algo ", unsigned int " flags ", const gnutls_datum_t * " hash ", const gnutls_datum_t * " signature ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
Holds the public key
.IP "gnutls_sign_algorithm_t algo" 12
The signature algorithm used
.IP "unsigned int flags" 12
Zero or an OR list of \fBgnutls_certificate_verify_flags\fP
.IP "const gnutls_datum_t * hash" 12
holds the hash digest to be verified
.IP "const gnutls_datum_t * signature" 12
contains the signature
.SH "DESCRIPTION"
This function will verify the given signed digest, using the
parameters from the public key. Note that unlike \fBgnutls_privkey_sign_hash()\fP,
this function accepts a signature algorithm instead of a digest algorithm.
You can use \fBgnutls_pk_to_sign()\fP to get the appropriate value.
.SH "RETURNS"
In case of a verification failure \fBGNUTLS_E_PK_SIG_VERIFY_FAILED\fP 
is returned, and zero or positive code on success. For known to be insecure
signatures this function will return \fBGNUTLS_E_INSUFFICIENT_SECURITY\fP unless
the flag \fBGNUTLS_VERIFY_ALLOW_BROKEN\fP is specified.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
