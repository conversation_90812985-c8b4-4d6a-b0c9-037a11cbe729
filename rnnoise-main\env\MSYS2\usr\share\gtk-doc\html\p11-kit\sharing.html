<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Proxy Module: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="index.html" title="p11-kit">
<link rel="prev" href="sharing-managed.html" title="Managed modules">
<link rel="next" href="remoting.html" title="Remoting / Forwarding">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><img src="up-insensitive.png" width="16" height="16" border="0"></td>
<td><a accesskey="p" href="sharing-managed.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="remoting.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="chapter">
<div class="titlepage"><div><div><h1 class="title">
<a name="sharing"></a>Proxy Module</h1></div></div></div>
<p>When an application is aware of the fact that coordination
	is necessary between multiple consumers of a PKCS#11 module, and wants
	to load standard configured PKCS#11 modules, it can link to
	<code class="literal">p11-kit</code> and use the functions there to provide this
	functionality.</p>
<p>However most current consumers of PKCS#11 are ignorant of
	this problem, and do not link to p11-kit. In order to solve this
	multiple initialization problem for all applications,
	<code class="literal">p11-kit</code> provides a proxy compatibility
	module.</p>
<p>This proxy module acts like a normal PKCS#11 module, but
	internally loads a preconfigured set of PKCS#11 modules and
	manages their features as described earlier. Each slot in the configured modules
	is exposed as a slot of the <code class="literal">p11-kit</code> proxy module. The proxy
	module is then used as a normal PKCS#11 module would be. It can be loaded by
	crypto libraries like NSS and behaves as expected.</p>
<p>The <code class="literal">C_GetFunctionList</code>,
	<code class="literal">C_GetInterfaceList</code> and <code class="literal">C_GetInterface</code>
	exported entry points of the proxy module returns a new managed PKCS#11 module
	each time it is called. These managed instances are released when the proxy
	module is unloaded.</p>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>