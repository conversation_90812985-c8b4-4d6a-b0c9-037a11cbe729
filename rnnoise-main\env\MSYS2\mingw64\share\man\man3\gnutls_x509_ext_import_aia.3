.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_ext_import_aia" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_ext_import_aia \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_ext_import_aia(const gnutls_datum_t * " ext ", gnutls_x509_aia_t " aia ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * ext" 12
The DER\-encoded extension data
.IP "gnutls_x509_aia_t aia" 12
The authority info access
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function extracts the Authority Information Access (AIA)
extension from the provided DER\-encoded data; see RFC 5280 section ******* 
for more information on the extension.  The
AIA extension holds a sequence of AccessDescription (AD) data.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
