<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_ERR_STATE_save</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_ERR_STATE_new, OSSL_ERR_STATE_save, OSSL_ERR_STATE_save_to_mark, OSSL_ERR_STATE_restore, OSSL_ERR_STATE_free - saving and restoring error state</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/err.h&gt;

ERR_STATE *OSSL_ERR_STATE_new(void);
void OSSL_ERR_STATE_save(ERR_STATE *es);
void OSSL_ERR_STATE_save_to_mark(ERR_STATE *es);
void OSSL_ERR_STATE_restore(const ERR_STATE *es);
void OSSL_ERR_STATE_free(ERR_STATE *es);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions save and restore the error state from the thread local error state to a preallocated error state structure.</p>

<p>OSSL_ERR_STATE_new() allocates an empty error state structure to be used when saving and restoring thread error state.</p>

<p>OSSL_ERR_STATE_save() saves the thread error state to <i>es</i>. It subsequently clears the thread error state. Any previously saved state in <i>es</i> is cleared prior to saving the new state.</p>

<p>OSSL_ERR_STATE_save_to_mark() is similar to OSSL_ERR_STATE_save() but only saves ERR entries up to the most recent mark on the ERR stack. These entries are moved to <i>es</i> and removed from the thread error state. However, the most recent marked ERR and any ERR state before it remains part of the thread error state and is not moved to the ERR_STATE. The mark is not cleared and must be cleared explicitly after a call to this function using <a href="../man3/ERR_pop_to_mark.html">ERR_pop_to_mark(3)</a> or <a href="../man3/ERR_clear_last_mark.html">ERR_clear_last_mark(3)</a>. (Since a call to OSSL_ERR_STATE_save_to_mark() leaves the marked ERR as the top error, either of these functions will have the same effect.) If there is no marked ERR in the thread local error state, all ERR entries are copied and the effect is the same as for a call to OSSL_ERR_STATE_save().</p>

<p>OSSL_ERR_STATE_restore() adds all the error entries from the saved state <i>es</i> to the thread error state. Existing entries in the thread error state are not affected if there is enough space for all the added entries. Any allocated data in the saved error entries is duplicated on adding to the thread state.</p>

<p>OSSL_ERR_STATE_free() frees the saved error state <i>es</i>. If the argument is NULL, nothing is done.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_ERR_STATE_new() returns a pointer to the allocated ERR_STATE structure or NULL on error.</p>

<p>OSSL_ERR_STATE_save(), OSSL_ERR_STATE_save_to_mark(), OSSL_ERR_STATE_restore(), OSSL_ERR_STATE_free() do not return any values.</p>

<h1 id="NOTES">NOTES</h1>

<p>OSSL_ERR_STATE_save() and OSSL_ERR_STATE_save_to_mark() cannot fail as it takes over any allocated data from the thread error state.</p>

<p>OSSL_ERR_STATE_restore() is a best effort function. The only failure that can happen during its operation is when memory allocation fails. Because it manipulates the thread error state it avoids raising memory errors on such failure. At worst the restored error entries will be missing the auxiliary error data.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_raise.html">ERR_raise(3)</a>, <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/ERR_clear_error.html">ERR_clear_error(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>All of these functions were added in OpenSSL 3.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2023-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


