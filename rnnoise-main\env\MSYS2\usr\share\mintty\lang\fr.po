# Traductions françaises du paquet mintty.
# Copyright (C) 2017 THE mintty'S COPYRIGHT HOLDER
# This file is distributed under the same license as the mintty package.
# <PERSON> <<EMAIL>>, 2017-2023.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2025-03-22 07:33+0100\n"
"PO-Revision-Date: 2023-01-26 14:20+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: French <<EMAIL>>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 2.0.9\n"

#: charset.c:228 charset.c:239 winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "(Default)"
msgstr "(défaut)"

#: charset.c:250
msgid "(OEM codepage)"
msgstr "(codage OEM)"

#: charset.c:254
msgid "(ANSI codepage)"
msgstr "(codage ANSI)"

#: child.c:96
msgid "There are no available terminals"
msgstr "Aucun terminal disponible"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "Erreur : impossible d’ouvrir le fichier de trace"

#: child.c:334
msgid "Error: Could not fork child process"
msgstr "Erreur : impossible de créer un process fils"

#: child.c:336
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr ""
"Il peut être nécessaire de rebaser les DLL, voir « rebaseall / rebase --"
"help »"

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:426
msgid "Failed to run '%s': %s"
msgstr "Impossible de lancer « %s » : %s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:578
msgid "%s: Exit %i"
msgstr "%s : sortie %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:585
msgid "TERMINATED"
msgstr "TERMINÉ"

#: child.c:1232
msgid "Error: Could not fork child daemon"
msgstr "Erreur : impossible de créer un process démon fils"

#. __ Setting false for Boolean options (localization optional)
#: config.c:655
msgid "no"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:657
msgid "yes"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:659
msgid "false"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:661
msgid "true"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:663
msgid "off"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:665
msgid "on"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:694
msgid "stretch"
msgstr "étendre"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:696
msgid "align"
msgstr "aligner"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:698
msgid "middle"
msgstr "milieu"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:700
msgid "full"
msgstr "complet"

#. __ %s: unknown option name
#: config.c:866
msgid "Ignoring unknown option '%s'"
msgstr "Option inconnue « %s » ignorée"

#: config.c:914 config.c:943
msgid "Internal error: too many options"
msgstr "Erreur interne : trop d’options"

#: config.c:930
msgid "Internal error: too many options/comments"
msgstr "Erreur interne : trop d’options/commentaires"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1100
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "Value inconnue ignorée « %s » pour l’option « %s »"

#. __ %s: option name
#: config.c:1112
msgid "Ignoring option '%s' with missing value"
msgstr "Option « %s » avec des valeurs manquantes ignorée"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1791
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"Impossible d’enregistrer les options dans « %s » :\n"
"%s."

#: config.c:2172
msgid "◇ None (printing disabled) ◇"
msgstr "◇ Aucune (pas d’impression) ◇"

#: config.c:2174
msgid "◆ Default printer ◆"
msgstr "◆ Imprimante par défaut ◆"

#. __ UI localization disabled
#: config.c:2283
msgid "– None –"
msgstr "– Aucun –"

#. __ UI localization: use Windows desktop setting
#: config.c:2285
msgid "@ Windows language @"
msgstr "@ Langue système @"

#. __ UI localization: use environment variable setting (LANGUAGE, LC_*)
#: config.c:2287
msgid "* Locale environm. *"
msgstr "* Locale environ. *"

#. __ UI localization: use mintty configuration setting (Text - Locale)
#: config.c:2289
msgid "= cfg. Text Locale ="
msgstr "= Langue caractères ="

#: config.c:2394
msgid "simple beep"
msgstr "bip simple"

#: config.c:2395
msgid "no beep"
msgstr "pas de son"

#: config.c:2396
msgid "Default Beep"
msgstr "Bip par défaut"

#: config.c:2397
msgid "Critical Stop"
msgstr "Arrêt critique"

#: config.c:2398
msgid "Question"
msgstr "Question"

#: config.c:2399
msgid "Exclamation"
msgstr "Exclamation"

#: config.c:2400
msgid "Asterisk"
msgstr "Astérisque"

#: config.c:2443
msgid "◇ None (system sound) ◇"
msgstr "◇ Aucun (son système) ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2874 config.c:3425
msgid "◇ None ◇"
msgstr "◇ Aucun ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2877
msgid "downloaded / give me a name!"
msgstr "téléchargé / donnez-moi un nom !"

#: config.c:2983
msgid "Could not load web theme"
msgstr "Impossible de télécharger le thème"

#: config.c:3040
msgid "Cannot write theme file"
msgstr "Impossible d’écrire le fichier de thème"

#: config.c:3045
msgid "Cannot store theme file"
msgstr "Impossible d’enregistrer le fichier de thème"

#. __ Options - Text:
#: config.c:3502 config.c:3840 config.c:3939
msgid "as font"
msgstr "comme fonte"

#. __ Options - Text:
#: config.c:3503 config.c:3845 config.c:3944
msgid "as colour"
msgstr "comme couleur"

#: config.c:3504
msgid "as font & as colour"
msgstr "fonte & couleur"

#. __ Options - Text:
#: config.c:3505 config.c:3850 config.c:3949
msgid "xterm"
msgstr "xterm"

#. __ Dialog button - show About text
#: config.c:3653
msgid "About..."
msgstr "À propos …"

#. __ Dialog button - save changes
#: config.c:3656
msgid "Save"
msgstr "Enregistrer"

#. __ Dialog button - cancel
#: config.c:3660 winctrls.c:1277 windialog.c:895
msgid "Cancel"
msgstr "Annuler"

#. __ Dialog button - apply changes
#: config.c:3664
msgid "Apply"
msgstr "Appliquer"

#. __ Dialog button - take notice
#: config.c:3668 windialog.c:892
msgid "I see"
msgstr "Bien vu"

#. __ Dialog button - confirm action
#: config.c:3670 winctrls.c:1276 windialog.c:894
msgid "OK"
msgstr "Ok"

#. __ Options - Looks: treeview label
#: config.c:3677 config.c:3708 config.c:3767
msgid "Looks"
msgstr "Apparence"

#. __ Options - Looks: panel title
#: config.c:3679
msgid "Looks in Terminal"
msgstr "Apparence"

#. __ Options - Looks: section title
#: config.c:3681
msgid "Colours"
msgstr "Couleurs"

#. __ Options - Looks:
#: config.c:3685
msgid "&Foreground..."
msgstr "A&vant-plan…"

#. __ Options - Looks:
#: config.c:3689
msgid "&Background..."
msgstr "A&rrière-plan…"

#. __ Options - Looks:
#: config.c:3693
msgid "&Cursor..."
msgstr "&Curseur…"

#. __ Options - Looks:
#: config.c:3697
msgid "&Theme"
msgstr "&Thème"

#. __ Options - Looks: name of web service
#: config.c:3702
msgid "Color Scheme Designer"
msgstr "Modèle de couleur"

#. __ Options - Looks: store colour scheme
#: config.c:3705 winctrls.c:484
msgid "Store"
msgstr "Enreg."

#. __ Options - Looks: section title
#: config.c:3710
msgid "Transparency"
msgstr "Transparence"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3716 config.c:4098 config.c:4261 config.c:4459
msgid "&Off"
msgstr "A&rrêt"

#. __ Options - Looks: transparency
#: config.c:3718
msgid "&Low"
msgstr "&Basse"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3720
msgid "&Med."
msgstr "&Moy."

#. __ Options - Looks: transparency
#: config.c:3722
msgid "&Medium"
msgstr "&Moyen"

#. __ Options - Looks: transparency
#: config.c:3724
msgid "&High"
msgstr "&Haute"

#. __ Options - Looks: transparency
#: config.c:3726
msgid "Gla&ss"
msgstr "&Verre"

#. __ Options - Looks: transparency
#: config.c:3733 config.c:3745 config.c:3752
msgid "Opa&que when focused"
msgstr "Opa&que au focus"

#. __ Options - Looks: transparency
#: config.c:3738
msgid "Blu&r"
msgstr "&Flou"

#: config.c:3759
msgid "◄"
msgstr "◄"

#: config.c:3762
msgid "►"
msgstr "►"

#. __ Options - Looks: section title
#: config.c:3769
msgid "Cursor"
msgstr "Curseur"

#. __ Options - Looks: cursor type
#: config.c:3774
msgid "Li&ne"
msgstr "Lig&ne"

#. __ Options - Looks: cursor type
#: config.c:3776
msgid "Bloc&k"
msgstr "B&loc"

#. __ Options - Looks: cursor type
#: config.c:3779
msgid "Bo&x"
msgstr "Bloc"

#. __ Options - Looks: cursor type
#: config.c:3782
msgid "&Underscore"
msgstr "&Tiret bas"

#. __ Options - Looks: cursor feature
#: config.c:3787
msgid "Blinkin&g"
msgstr "Cli&gnotant"

#. __ Options - Text: treeview label
#: config.c:3794 config.c:3819 config.c:3834 config.c:3883 config.c:3933
#: config.c:3958 config.c:3980 config.c:3993 config.c:4001
msgid "Text"
msgstr "Texte"

#. __ Options - Text: panel title
#: config.c:3796
msgid "Text and Font properties"
msgstr "Propriétés de la police de caractères"

#. __ Options - Text: section title
#: config.c:3798
msgid "Font"
msgstr "Police de caractères"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3806 winctrls.c:1287
msgid "Font st&yle:"
msgstr "&Style de police :"

#. __ Font chooser:
#: config.c:3811 winctrls.c:1289
msgid "&Size:"
msgstr "&Taille :"

#. __ Options - Text:
#: config.c:3823 config.c:3902
msgid "Sho&w bold as font"
msgstr "&Gras comme fonte"

#. __ Options - Text:
#: config.c:3828 config.c:3907
msgid "Show &bold as colour"
msgstr "G&ras comme couleur"

#. __ Options - Text:
#: config.c:3836 config.c:3857 config.c:3935 config.c:3962
msgid "Show bold"
msgstr "Gras"

#. __ Options - Text:
#: config.c:3864 config.c:3912 config.c:3968
msgid "&Allow blinking"
msgstr "C&lignotement"

#. __ Options - Text:
#: config.c:3869 config.c:3973
msgid "Show dim as font"
msgstr "Pâle comme fonte"

#. __ Options - Text:
#: config.c:3887 config.c:3920 config.c:3955
msgid "Font smoothing"
msgstr "Lissage des caractères"

#. __ Options - Text:
#: config.c:3890 config.c:3923 config.c:4149 config.c:4188 config.c:4342
#: config.c:4355
msgid "&Default"
msgstr "&Défaut"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3892 config.c:3925 config.c:4147 config.c:4186 config.c:4340
#: config.c:4353 config.c:4440
msgid "&None"
msgstr "Aucu&ne"

#. __ Options - Text:
#: config.c:3894 config.c:3927 config.c:4148 config.c:4187 config.c:4341
#: config.c:4354
msgid "&Partial"
msgstr "&Partiel"

#. __ Options - Text:
#: config.c:3896 config.c:3929 config.c:4150 config.c:4189 config.c:4343
#: config.c:4356
msgid "&Full"
msgstr "&Complet"

#: config.c:3983
msgid "&Locale"
msgstr "&Langue"

#: config.c:3986
msgid "&Character set"
msgstr "&Jeu de caractères"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3997 config.c:4003
msgid "Emojis"
msgstr "Émoticône"

#. __ Options - Text - Emojis:
#: config.c:4007
msgid "Style"
msgstr "Style"

#. __ Options - Text - Emojis:
#: config.c:4012
msgid "Placement"
msgstr "Placement"

#. __ Options - Keys: treeview label
#: config.c:4020 config.c:4050 config.c:4085 config.c:4103
msgid "Keys"
msgstr "Clavier"

#. __ Options - Keys: panel title
#: config.c:4022
msgid "Keyboard features"
msgstr "Clavier"

#. __ Options - Keys:
#: config.c:4026
msgid "&Backarrow sends ^H"
msgstr "&Efface émet ^H"

#. __ Options - Keys:
#: config.c:4031
msgid "&Delete sends DEL"
msgstr "&Suppr émet DEL"

#. __ Options - Keys:
#: config.c:4036
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "Ctrl+AltGauche émet Alt&Gr"

#. __ Options - Keys:
#: config.c:4041
msgid "AltGr is also Alt"
msgstr "AltGr émet également Alt"

#. __ Options - Keys:
#: config.c:4046
msgid "&Esc/Enter reset IME to alphanumeric"
msgstr "&Esc/Enter réinitiale IME"

#. __ Options - Keys: section title
#: config.c:4052
msgid "Shortcuts"
msgstr "Raccourcis"

#. __ Options - Keys:
#: config.c:4055
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "Cop&ier et coller (Ctrl/Maj+Inser)"

#. __ Options - Keys:
#: config.c:4060
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "&Menu et plein écran (Alt+Espace/Entrée)"

#. __ Options - Keys:
#: config.c:4065
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "&Change de fenêtre (Ctrl+[Maj+]Tab)"

#. __ Options - Keys:
#: config.c:4070
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "&Zoom (Ctrl+plus/moins/zéro)"

#. __ Options - Keys:
#: config.c:4075
msgid "&Alt+Fn shortcuts"
msgstr "Raccourcis &Alt+Fn"

#. __ Options - Keys:
#: config.c:4080
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "Raccourcis &Ctrl+Maj+lettre"

#. __ Options - Keys: section title
#: config.c:4087 config.c:4105
msgid "Compose key"
msgstr "Touche compose"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:4092 config.c:4253 config.c:4272 config.c:4451 config.c:4470
msgid "&Shift"
msgstr "&Maj"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:4094 config.c:4255 config.c:4280 config.c:4453 config.c:4478
msgid "&Ctrl"
msgstr "&Ctrl"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4096 config.c:4257 config.c:4276 config.c:4455 config.c:4474
msgid "&Alt"
msgstr "&Alt"

#. __ Options - Mouse: treeview label
#: config.c:4112 config.c:4201 config.c:4233
msgid "Mouse"
msgstr "Souris"

#. __ Options - Mouse: panel title
#: config.c:4114
msgid "Mouse functions"
msgstr "Souris"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4122 config.c:4155 config.c:4171 config.c:4319
msgid "Cop&y on select"
msgstr "Copier sur &sélection"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4127 config.c:4160 config.c:4324 wininput.c:685
msgid "Copy with TABs"
msgstr "Copier (texte) avec &tabulations"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4132 config.c:4165 config.c:4177 config.c:4329
msgid "Copy as &rich text"
msgstr "Copier en &Rich Text"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4138 config.c:4145 config.c:4184 config.c:4338 config.c:4351
msgid "Copy as &HTML"
msgstr "Copier en &HTML"

#. __ Options - Mouse:
#: config.c:4197
msgid "Clic&ks place command line cursor"
msgstr "Cli&c positionne le curseur"

#. __ Options - Mouse: section title
#: config.c:4203
msgid "Click actions"
msgstr "Actions du cllic"

#. __ Options - Mouse:
#: config.c:4206
msgid "Right mouse button"
msgstr "Bouton droit"

#. __ Options - Mouse:
#: config.c:4209 config.c:4223
msgid "&Paste"
msgstr "&Coller"

#. __ Options - Mouse:
#: config.c:4211 config.c:4225
msgid "E&xtend"
msgstr "É&tendre"

#. __ Options - Mouse:
#: config.c:4213
msgid "&Menu"
msgstr "&Menu"

#. __ Options - Mouse:
#: config.c:4215 config.c:4229
msgid "Ente&r"
msgstr "Ent&ée"

#. __ Options - Mouse:
#: config.c:4220
msgid "Middle mouse button"
msgstr "Bouton du milieu"

#. __ Options - Mouse:
#: config.c:4227
msgid "&Nothing"
msgstr "&Rien"

#. __ Options - Mouse: section title
#: config.c:4235
msgid "Application mouse mode"
msgstr "Souris en mode appli."

#. __ Options - Mouse:
#: config.c:4238
msgid "Default click target"
msgstr "&Cible du clic"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4241
msgid "&Window"
msgstr "&Fenêtre"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4243
msgid "&Application"
msgstr "A&pplication"

#. __ Options - Mouse:
#: config.c:4250 config.c:4268
msgid "Modifier for overriding default"
msgstr "Modifieur"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4259 config.c:4284 config.c:4457 config.c:4482
msgid "&Win"
msgstr "&Win"

#. __ Options - Modifier - Super:
#: config.c:4288 config.c:4486
msgid "&Sup"
msgstr "&Sup"

#. __ Options - Modifier - Hyper:
#: config.c:4292 config.c:4490
msgid "&Hyp"
msgstr "&Hyp"

#. __ Options - Selection: treeview label
#: config.c:4302 config.c:4313 config.c:4377
msgid "Selection"
msgstr "Sélection"

#. __ Options - Selection: panel title
#: config.c:4304
msgid "Selection and clipboard"
msgstr "Sélection et presse-papier"

#. __ Options - Selection:
#: config.c:4308
msgid "Clear selection on input"
msgstr "Effacer la sélection sur saisie"

#. __ Options - Selection: section title
#: config.c:4315
msgid "Clipboard"
msgstr "Presse-papier"

#. __ Options - Selection:
#: config.c:4365
msgid "Trim space from selection"
msgstr "Enlever les espaces de la sélection"

#. __ Options - Selection:
#: config.c:4371
msgid "Allow setting selection"
msgstr "Activer la sélection"

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4379 config.c:4401 config.c:4426 config.c:4499
msgid "Window"
msgstr "Fenêtre"

#. __ Options - Selection: clock position of info popup for text size
#: config.c:4384
msgid "Show size while selecting (0..12)"
msgstr "Montrer la taille pendant la sélection (0..12)"

#. __ Options - Selection:
#: config.c:4391
msgid "Suspend output while selecting"
msgstr "Suspendre l'affichage pendant la sélection"

#. __ Options - Window: panel title
#: config.c:4403
msgid "Window properties"
msgstr "Fenêtre"

#. __ Options - Window: section title
#: config.c:4405
msgid "Default size"
msgstr "Taille par défaut"

#. __ Options - Window:
#: config.c:4409
msgid "Colu&mns"
msgstr "Colon&nes"

#. __ Options - Window:
#: config.c:4413
msgid "Ro&ws"
msgstr "Li&gnes"

#. __ Options - Window:
#: config.c:4417
msgid "C&urrent size"
msgstr "Taille a&ctuelle"

#. __ Options - Window:
#: config.c:4422
msgid "Re&wrap on resize"
msgstr "Réaligner après re&dimensionnement"

#. __ Options - Window:
#: config.c:4430
msgid "Scroll&back lines"
msgstr "&Lignes défilement ar."

#. __ Options - Window:
#: config.c:4435
msgid "Scrollbar"
msgstr "Barre de défilement"

#. __ Options - Window: scrollbar
#: config.c:4438
msgid "&Left"
msgstr "&Gauche"

#. __ Options - Window: scrollbar
#: config.c:4442
msgid "&Right"
msgstr "&Droite"

#. __ Options - Window:
#: config.c:4448 config.c:4466
msgid "Modifier for scrolling"
msgstr "Modifieur pour défilement"

#. __ Options - Window:
#: config.c:4495
msgid "&PgUp and PgDn scroll without modifier"
msgstr "Défilement par &PgAr et PgAv "

#. __ Options - Window: section title
#: config.c:4501
msgid "UI language"
msgstr "Langue"

#. __ Options - Terminal: treeview label
#: config.c:4511 config.c:4524 config.c:4585 config.c:4599
msgid "Terminal"
msgstr "Terminal"

#. __ Options - Terminal: panel title
#: config.c:4513
msgid "Terminal features"
msgstr "Terminal"

#. __ Options - Terminal:
#: config.c:4517
msgid "&Type"
msgstr "&Type"

#. __ Options - Terminal: answerback string for ^E request
#: config.c:4521
msgid "&Answerback"
msgstr "&Réponse"

#. __ Options - Terminal: section title
#: config.c:4526
msgid "Bell"
msgstr "Son"

#. __ Options - Terminal: bell
#: config.c:4533
msgid "► &Play"
msgstr "► &Lire"

#. __ Options - Terminal: bell
#: config.c:4539
msgid "&Wave"
msgstr "&Fichier"

#. __ Options - Terminal: bell
#: config.c:4561 config.c:4574
msgid "&Flash"
msgstr "&Flash"

#. __ Options - Terminal: bell
#: config.c:4563 config.c:4578
msgid "&Highlight in taskbar"
msgstr "Flash &tâche"

#. __ Options - Terminal: bell
#: config.c:4565 config.c:4582
msgid "&Popup"
msgstr "&Popup"

#. __ Options - Terminal: section title
#: config.c:4587
msgid "Printer"
msgstr "Imprimante"

#. __ Options - Terminal:
#: config.c:4602
msgid "Prompt about running processes on &close"
msgstr "&Demander si process en cours à la fermeture"

#. __ Options - Terminal:
#. __ Context menu:
#: config.c:4607 wininput.c:581
msgid "Status Line"
msgstr "Ligne d'état"

#: textprint.c:44 textprint.c:127
msgid "[Printing...] "
msgstr "[Impression…] "

#. __ Options - Text: font chooser activation button
#: winctrls.c:935
msgid "&Select..."
msgstr "&Sélect.…"

#. __ Font chooser: title bar label
#: winctrls.c:1281
msgid "Font "
msgstr "Police de caractères "

#. __ Font chooser: button
#: winctrls.c:1283
msgid "&Apply"
msgstr "&Appliquer"

#. __ Font chooser:
#: winctrls.c:1285
msgid "&Font:"
msgstr "&Police :"

#. __ Font chooser:
#: winctrls.c:1291
msgid "Sample"
msgstr "Échantillon"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1295 winctrls.c:1554 winctrls.c:1719
msgid "Ferqœm’4€"
msgstr "AàBbCçÉéYz"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1312
msgid "Sc&ript:"
msgstr "Sc&ript :"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1314
msgid "<A>Show more fonts</A>"
msgstr "<A>Autres polices</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1319
msgid "Colour "
msgstr "Couleur "

#. __ Colour chooser:
#: winctrls.c:1332 winctrls.c:1344
msgid "B&asic colours:"
msgstr "Couleurs de b&ase :"

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Custom colours:"
msgstr "Couleurs &personnalisées :"

#. __ Colour chooser:
#: winctrls.c:1360
msgid "De&fine Custom Colours >>"
msgstr "&Définir les couleurs personnalisées >>"

#. __ Colour chooser:
#: winctrls.c:1363
msgid "Colour"
msgstr "Couleur"

#. __ Colour chooser:
#: winctrls.c:1365
msgid "|S&olid"
msgstr "|&Unie"

#. __ Colour chooser:
#: winctrls.c:1367
msgid "&Hue:"
msgstr "&Teinte :"

#. __ Colour chooser:
#: winctrls.c:1370
msgid "&Sat:"
msgstr "&Satur. :"

#. __ Colour chooser:
#: winctrls.c:1372
msgid "&Lum:"
msgstr "&Lum. :"

#. __ Colour chooser:
#: winctrls.c:1374
msgid "&Red:"
msgstr "&Rouge :"

#. __ Colour chooser:
#: winctrls.c:1376
msgid "&Green:"
msgstr "&Vert :"

#. __ Colour chooser:
#: winctrls.c:1378
msgid "&Blue:"
msgstr "&Bleu :"

#. __ Colour chooser:
#: winctrls.c:1381
msgid "A&dd to Custom Colours"
msgstr "A&jouter aux couleurs personnalisées"

#. __ Options: dialog title
#: windialog.c:266 windialog.c:839
msgid "Options"
msgstr "Options"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:268
msgid "available"
msgstr "disponible"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:783
msgid "100"
msgstr "100"

#: windialog.c:924 windialog.c:951
msgid "Error"
msgstr "Erreur"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:300
msgid "Session switcher"
msgstr "Choisir une session"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:320
msgid "Session launcher"
msgstr "Lanceur de session"

#: wininput.c:429 wininput.c:435
msgid "Ctrl+"
msgstr "Ctrl+"

#: wininput.c:430 wininput.c:436
msgid "Alt+"
msgstr "Alt+"

#: wininput.c:431 wininput.c:437
msgid "Shift+"
msgstr "Maj+"

#. __ System menu:
#: wininput.c:462
msgid "&Restore"
msgstr "&Restaurer"

#. __ System menu:
#: wininput.c:464
msgid "&Move"
msgstr "&Déplacer"

#. __ System menu:
#: wininput.c:466
msgid "&Size"
msgstr "&Taille"

#. __ System menu:
#: wininput.c:468
msgid "Mi&nimize"
msgstr "Di&minuer"

#. __ System menu:
#: wininput.c:470
msgid "Ma&ximize"
msgstr "Ma&ximiser"

#. __ System menu:
#: wininput.c:472
msgid "&Close"
msgstr "&Fermer"

#. __ System menu:
#: wininput.c:477
msgid "New &Window"
msgstr "Nouvelle &fenêtre"

#. __ System menu:
#: wininput.c:483
msgid "New &Tab"
msgstr "Nouvel &onglet"

#. __ Context menu:
#: wininput.c:490
msgid "&Copy"
msgstr "&Copier"

#. __ Context menu:
#: wininput.c:509
msgid "&Paste "
msgstr "Co&ller "

#. __ Context menu:
#: wininput.c:514
msgid "Copy → Paste"
msgstr "Copier → Coller"

#. __ Context menu:
#: wininput.c:519
msgid "S&earch"
msgstr "Ch&ercher"

#. __ Context menu:
#: wininput.c:526
msgid "&Log to File"
msgstr "Enregistrer fichier"

#. __ Context menu:
#: wininput.c:532
msgid "Character &Info"
msgstr "&Info de caractères"

#. __ Context menu:
#: wininput.c:538
msgid "VT220 Keyboard"
msgstr "Clavier VT220"

#. __ Context menu:
#: wininput.c:543
msgid "&Reset"
msgstr "&Reset"

#. __ Context menu:
#: wininput.c:551
msgid "&Default Size"
msgstr "Taille &défaut"

#. __ Context menu:
#: wininput.c:561
msgid "Scroll&bar"
msgstr "Barre de défilement"

#. __ Context menu:
#: wininput.c:567
msgid "&Full Screen"
msgstr "&Plein écran"

#. __ Context menu:
#: wininput.c:573
msgid "Flip &Screen"
msgstr "Basculer l'&écran"

#. __ System menu:
#: wininput.c:591 wininput.c:763
msgid "Copy &Title"
msgstr "&Copier le titre"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:593 wininput.c:747 wininput.c:765
msgid "&Options..."
msgstr "&Options…"

#. __ Context menu:
#: wininput.c:678
msgid "Ope&n"
msgstr "Ouvr&ir"

#. __ Context menu:
#: wininput.c:683
msgid "Copy as text"
msgstr "Copier en text"

#. __ Context menu:
#: wininput.c:687
msgid "Copy as RTF"
msgstr "Copier en RTF"

#. __ Context menu:
#: wininput.c:689
msgid "Copy as HTML text"
msgstr "Copier en texte HTML"

#. __ Context menu:
#: wininput.c:691
msgid "Copy as HTML"
msgstr "Copier en HTML"

#. __ Context menu:
#: wininput.c:693
msgid "Copy as HTML full"
msgstr "Copier en HTML complet"

#. __ Context menu:
#: wininput.c:700
msgid "Select &All"
msgstr "Tout &sélectionner"

#. __ Context menu:
#: wininput.c:702
msgid "Save as &Image"
msgstr "Enregistrer comme &image"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:714
msgid "HTML Screen Dump"
msgstr "Copie écran en HTML"

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:722
msgid "Clear Scrollback"
msgstr "Purger le défilement"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:733
msgid "Send Break"
msgstr "Émet Break"

#. __ Context menu, user commands
#: wininput.c:835
msgid "User commands"
msgstr "Commandes utilisateur"

#: wininput.c:1503 wininput.c:1524 wininput.c:1526 wininput.c:1528
#: wininput.c:1565
msgid "[NO SCROLL] "
msgstr "[SANS DÉFILEMENT] "

#: wininput.c:1516 wininput.c:1525 wininput.c:1530 wininput.c:1586
msgid "[SCROLL MODE] "
msgstr "[MODE DÉFILEMENT] "

# msgstr "Arrêter le défilement"
#: winmain.c:3845
msgid "Processes are running in session:"
msgstr "Processus en cours dans la session :"

#: winmain.c:3846
msgid "Close anyway?"
msgstr "Forcer la fermeture ?"

#: winmain.c:3870
msgid "Reset terminal?"
msgstr "Réinitialiser le terminal ?"

#: winmain.c:4100
msgid "Try '--help' for more information"
msgstr "Essayez « --help » pour plus d’information"

#: winmain.c:4108
msgid "Could not load icon"
msgstr "Impossible de charger l’icône"

#: winmain.c:6402
msgid "Usage:"
msgstr "Utilisation :"

#: winmain.c:6403
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[OPTION]... [ PROGRAMME [ARG]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:6406
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"Démarre une nouvelle session de terminal en utilisant le programme spécifié "
"ou le shell de l’utilisateur.\n"
"Si un tiret est donné comme nom de programme, alors invoque le shell comme "
"shell de login.\n"
"\n"
"Options :\n"
"  -c, --config FICHIER  Charge le fichier de configuration spécifié (cf. -C "
"ou -o FichierTheme)\n"
"  -e, --exec ...        Traite le reste des arguments comme la commande à "
"exécuter\n"
"  -h, --hold never|start|error|always  Garde la fenêtre ouvert après la fin "
"de la commande\n"
"  -p, --position X,Y    Ouvre la fenêtre aux coordonnées spécifiées\n"
"  -p, --position center|left|right|top|bottom  Ouvre la fenêtre à une "
"position spéciale\n"
"  -p, --position @N     Ouvre la fenêtre sur l’écran n° N\n"
"  -s, --size COLS,LIGNES  Configure la taille de la fenêtre en nombre de "
"caractères (également COLSxLIGNES)\n"
"  -s, --size maxwidth|maxheight  Configure la taille max de la fenêtre aux "
"dimensions spécifiées\n"
"  -t, --title TITRE     Configure le titre de la fenêtre (par défault : nom "
"de la commande invoquée) (cf. -T)\n"
"  -w, --window normal|min|max|full|hide  Configure l’état initial de la "
"fenêtre\n"
"  -i, --icon FICHIER[,IX]  Configure  l’icône de la fenêtre depuis un "
"fichier, avec un index en option\n"
"  -l, --log FICHIER|-   Enregistrer la sortie dans un fichier ou la sortie "
"standard\n"
"      --nobidi|--nortl  Inhiber bidi (support écriture droite-gauche)\n"
"  -o, --option OPT=VAL  Configurer/surcharger une option du fichier de "
"configuration avec la valeur spécifiée\n"
"  -B, --Border frame|void  Utiliser des bordures fines ou pas de bordure\n"
"  -R, --Report s|o      Reporter la position de la fenêtre (court/long) "
"après la sortie\n"
"      --nopin           Indiquer que cette instance ne peut pas être "
"épinglée à la barre des tâches\n"
"  -D, --daemon          Démarrer une nouvelle instance avec la touche de "
"raccourci Windows\n"
"  -H, --help            Afficher l’aide et quitter\n"
"  -V, --version         Afficher la version et quitter\n"
"Voir la page de manuel pour les autres options de la ligne de commande et la "
"configuration.\n"

#: winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "WSL distribution '%s' not found"
msgstr "Distribution WSL « %s » non trouvée"

#: winmain.c:6731
msgid "Duplicate option '%s'"
msgstr "Option « %s » en double"

#: winmain.c:6739 winmain.c:6830
msgid "Unknown option '%s'"
msgstr "Option inconnue « %s »"

#: winmain.c:6741
msgid "Option '%s' requires an argument"
msgstr "L’option « %s » requiert un argument"

#: winmain.c:6768
msgid "Syntax error in position argument '%s'"
msgstr "Erreur de syntaxe pour l’argument de position « %s »"

#: winmain.c:6779
msgid "Syntax error in size argument '%s'"
msgstr "Erreur de syntaxe pour l’argument de taille « %s »"

#: winmain.c:6939
msgid "Syntax error in geometry argument '%s'"
msgstr "Erreur de syntaxe pour l’argument de taille et position « %s »"

#: winmain.c:7038
msgid "Mintty could not detach from caller, starting anyway"
msgstr "Mintty ne peut se détacher de l’appelant, on démarre quand même"

#: winmain.c:7387
msgid "Using default title due to invalid characters in program name"
msgstr ""
"Utilisation du titre par défaut à cause d’un caractère invalide dans le nom "
"du programme"

#: winsearch.c:232
msgid "◀"
msgstr "◀"

#: winsearch.c:233
msgid "▶"
msgstr "▶"

#: winsearch.c:234
msgid "X"
msgstr "X"

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:165
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "Interlignage : %d, Gras : %s, Souligné : %s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:167
msgid "font"
msgstr "police"

#. __ Options - Text: font properties: value affected by option
#: wintext.c:169
msgid "manual"
msgstr "manuel"

#: wintext.c:544
msgid "Font not found, using system substitute"
msgstr "Police non trouvée, utilisation de la police système"

#: wintext.c:559
msgid "Font has limited support for character ranges"
msgstr "La police a un jeu de caractères limité"

#: wintext.c:702
msgid "Font installation corrupt, using system substitute"
msgstr "Police corrompue, utilisation de la police système"

#: wintext.c:756
msgid "Font does not support system locale"
msgstr "Police non supportée pour la langue système"

#: appinfo.h:64
msgid "There is no warranty, to the extent permitted by law."
msgstr "Sans garantie aucune dans les limites permises par la loi."

#. __ %s: WEBSITE (URL)
#: appinfo.h:69
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"Traductions © 2023 Éric Lassauge\n"
"Reportez les bugs ou les demandes d’amélioration via le gestionnaire de "
"problèmes de la page du projet mintty\n"
"à l’adresse « %s ».\n"
"Voir également le wiki pour d’autres astuces, remerciements et crédits."
