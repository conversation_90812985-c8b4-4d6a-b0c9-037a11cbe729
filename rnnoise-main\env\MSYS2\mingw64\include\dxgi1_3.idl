/*
 * Copyright 2017 <PERSON><PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "dxgi1_2.idl";

typedef struct DXGI_MATRIX_3X2_F
{
    float _11;
    float _12;
    float _21;
    float _22;
    float _31;
    float _32;
} DXGI_MATRIX_3X2_F;

typedef struct DXGI_DECODE_SWAP_CHAIN_DESC
{
    UINT Flags;
} DXGI_DECODE_SWAP_CHAIN_DESC;

typedef enum DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS
{
    DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAG_NOMINAL_RANGE = 0x1,
    DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAG_BT709 = 0x2,
    DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAG_xvYCC = 0x4,
} DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS;

typedef enum DXGI_FRAME_PRESENTATION_MODE
{
    DXGI_FRAME_PRESENTATION_MODE_COMPOSED = 0,
    DXGI_FRAME_PRESENTATION_MODE_OVERLAY = 1,
    DXGI_FRAME_PRESENTATION_MODE_NONE = 2,
    DXGI_FRAME_PRESENTATION_MODE_COMPOSITION_FAILURE = 3,
} DXGI_FRAME_PRESENTATION_MODE;

typedef struct DXGI_FRAME_STATISTICS_MEDIA
{
    UINT PresentCount;
    UINT PresentRefreshCount;
    UINT SyncRefreshCount;
    LARGE_INTEGER SyncQPCTime;
    LARGE_INTEGER SyncGPUTime;
    DXGI_FRAME_PRESENTATION_MODE CompositionMode;
    UINT ApprovedPresentDuration;
} DXGI_FRAME_STATISTICS_MEDIA;

typedef enum DXGI_OVERLAY_SUPPORT_FLAG
{
    DXGI_OVERLAY_SUPPORT_FLAG_DIRECT = 0x1,
    DXGI_OVERLAY_SUPPORT_FLAG_SCALING = 0x2,
} DXGI_OVERLAY_SUPPORT_FLAG;

[
    object,
    uuid(6007896c-3244-4afd-bf18-a6d3beda5023),
    local,
    pointer_default(unique)
]
interface IDXGIDevice3 : IDXGIDevice2
{
    void Trim();
}

[
    object,
    uuid(a8be2ac4-199f-4946-b331-79599fb98de7),
    local,
    pointer_default(unique)
]
interface IDXGISwapChain2 : IDXGISwapChain1
{
    HRESULT SetSourceSize(UINT width, UINT height);

    HRESULT GetSourceSize(
        [out] UINT *width,
        [out] UINT *height
    );

    HRESULT SetMaximumFrameLatency(UINT max_latency);

    HRESULT GetMaximumFrameLatency(
        [out] UINT *max_latency
    );

    HANDLE  GetFrameLatencyWaitableObject();

    HRESULT SetMatrixTransform(const DXGI_MATRIX_3X2_F *matrix);

    HRESULT GetMatrixTransform(
        [out] DXGI_MATRIX_3X2_F *matrix
    );
}

[
    object,
    uuid(595e39d1-2724-4663-99b1-da969de28364),
    local,
    pointer_default(unique)
]
interface IDXGIOutput2 : IDXGIOutput1
{
    BOOL SupportsOverlays();
}

[
    object,
    uuid(25483823-cd46-4c7d-86ca-47aa95b837bd),
    local,
    pointer_default(unique)
]
interface IDXGIFactory3 : IDXGIFactory2
{
    UINT GetCreationFlags();
}

[
    object,
    uuid(2633066b-4514-4c7a-8fd8-12ea98059d18),
    local,
    pointer_default(unique)
]
interface IDXGIDecodeSwapChain : IUnknown
{
    HRESULT PresentBuffer(UINT buffer_to_present, UINT sync_interval, UINT flags);

    HRESULT SetSourceRect(const RECT *rect);

    HRESULT SetTargetRect(const RECT *rect);

    HRESULT SetDestSize(UINT width, UINT height);

    HRESULT GetSourceRect(
        [out] RECT *rect
    );

    HRESULT GetTargetRect(
        [out] RECT *rect
    );

    HRESULT GetDestSize(
        [out] UINT *width,
        [out] UINT *height
    );

    HRESULT SetColorSpace(DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS colorspace);

    DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS GetColorSpace();
}

[
    object,
    uuid(41e7d1f2-a591-4f7b-a2e5-fa9c843e1c12),
    local,
    pointer_default(unique)
]
interface IDXGIFactoryMedia : IUnknown
{
    HRESULT CreateSwapChainForCompositionSurfaceHandle(
        [in] IUnknown *device,
        [in] HANDLE surface,
        [in] const DXGI_SWAP_CHAIN_DESC1 *desc,
        [in] IDXGIOutput *restrict_to_output,
        [out] IDXGISwapChain1 **swapchain
    );

    HRESULT CreateDecodeSwapChainForCompositionSurfaceHandle(
        [in] IUnknown *device,
        [in] HANDLE surface,
        [in] DXGI_DECODE_SWAP_CHAIN_DESC *desc,
        [in] IDXGIResource *yuv_decode_buffers,
        [in] IDXGIOutput *restrict_to_output,
        [out] IDXGIDecodeSwapChain **swapchain
    );
}

[
    object,
    uuid(dd95b90b-f05f-4f6a-bd65-25bfb264bd84),
    local,
    pointer_default(unique)
]
interface IDXGISwapChainMedia : IUnknown
{
    HRESULT GetFrameStatisticsMedia(
        [out] DXGI_FRAME_STATISTICS_MEDIA *stats
    );

    HRESULT SetPresentDuration(UINT duration);

    HRESULT CheckPresentDurationSupport(
        UINT desired_present_duration,
        [out] UINT *closest_smaller_present_duration,
        [out] UINT *closest_larger_present_duration
    );
}

[
    object,
    uuid(8a6bb301-7e7e-41F4-a8e0-5b32f7f99b18),
    local,
    pointer_default(unique)
]
interface IDXGIOutput3 : IDXGIOutput2
{
    HRESULT CheckOverlaySupport(
        [in] DXGI_FORMAT enum_format,
        [out] IUnknown *concerned_device,
        [out] UINT *flags
    );
}

const UINT DXGI_CREATE_FACTORY_DEBUG = 0x1;

[local] HRESULT __stdcall CreateDXGIFactory2(UINT flags, REFIID iid, void **factory);

[local] HRESULT __stdcall DXGIGetDebugInterface1(UINT flags, REFIID iid, void **debug);
