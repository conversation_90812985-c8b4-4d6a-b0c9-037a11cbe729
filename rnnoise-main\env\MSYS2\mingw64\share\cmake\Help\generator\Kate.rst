Kate
----

.. deprecated:: 3.27

  Support for :ref:`Extra Generators` is deprecated and will be removed from
  a future version of CMake.  IDEs may use the :manual:`cmake-file-api(7)`
  to view CMake-generated project build trees.

Generates Kate project files.

A project file for <PERSON> will be created in the top directory in the top level
build directory.
To use it in <PERSON>, the Project plugin must be enabled.
The project file is loaded in Kate by opening the
``ProjectName.kateproject`` file in the editor.
If the Kate Build-plugin is enabled, all targets generated by CMake are
available for building.

This "extra" generator may be specified as:

``Kate - MinGW Makefiles``
 Generate with :generator:`MinGW Makefiles`.

``Kate - NMake Makefiles``
 Generate with :generator:`NMake Makefiles`.

``Kate - Ninja``
 Generate with :generator:`Ninja`.

``Kate - Ninja Multi-Config``
 Generate with :generator:`Ninja Multi-Config`.

``Kate - Unix Makefiles``
 Generate with :generator:`Unix Makefiles`.
