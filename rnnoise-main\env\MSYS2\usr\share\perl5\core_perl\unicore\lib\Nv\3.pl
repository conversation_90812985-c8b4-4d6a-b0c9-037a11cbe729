# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V274
51
52
179
180
1635
1636
1779
1780
1987
1988
2409
2410
2537
2538
2665
2666
2793
2794
2921
2922
3049
3050
3177
3178
3195
3196
3198
3199
3305
3306
3433
3434
3561
3562
3667
3668
3795
3796
3875
3876
4163
4164
4243
4244
4971
4972
6115
6116
6131
6132
6163
6164
6473
6474
6611
6612
6787
6788
6803
6804
6995
6996
7091
7092
7235
7236
7251
7252
8323
8324
8546
8547
8562
8563
9314
9315
9334
9335
9354
9355
9463
9464
10104
10105
10114
10115
10124
10125
12323
12324
12692
12693
12834
12835
12930
12931
19977
19978
20200
20201
21441
21445
24334
24335
42531
42532
42728
42729
43219
43220
43267
43268
43475
43476
43507
43508
43603
43604
44019
44020
63851
63852
65299
65300
65801
65802
66275
66276
66723
66724
67674
67675
67707
67708
67753
67754
67867
67868
68034
68035
68162
68163
68442
68443
68474
68475
68523
68524
68915
68916
69218
69219
69407
69408
69575
69576
69716
69717
69737
69738
69875
69876
69945
69946
70099
70100
70115
70116
70387
70388
70739
70740
70867
70868
71251
71252
71363
71364
71475
71476
71907
71908
72019
72020
72787
72788
72796
72797
73043
73044
73123
73124
73555
73556
74753
74754
74760
74761
74775
74776
74784
74785
74788
74790
74798
74800
74806
74808
74810
74812
74827
74828
74833
74834
74839
74840
92771
92772
92867
92868
93011
93012
93827
93828
93846
93847
119491
119492
119523
119524
119650
119651
119668
119669
120785
120786
120795
120796
120805
120806
120815
120816
120825
120826
123203
123204
123635
123636
124147
124148
125129
125130
125267
125268
126067
126068
126117
126118
126211
126212
126256
126257
127236
127237
130035
130036
133885
133886
133913
133914
141720
141721
146203
146204
END
