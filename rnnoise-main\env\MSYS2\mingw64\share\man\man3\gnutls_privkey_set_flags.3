.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_set_flags" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_set_flags \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "void gnutls_privkey_set_flags(gnutls_privkey_t " key ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
A key of type \fBgnutls_privkey_t\fP
.IP "unsigned int flags" 12
flags from the \fBgnutls_privkey_flags\fP
.SH "DESCRIPTION"
This function will set flags for the specified private key, after
it is generated. Currently this is useful for the \fBGNUTLS_PRIVKEY_FLAG_EXPORT_COMPAT\fP
to allow exporting a "provable" private key in backwards compatible way.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
