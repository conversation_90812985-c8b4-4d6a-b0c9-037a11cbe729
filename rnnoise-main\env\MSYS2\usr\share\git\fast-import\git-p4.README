The git-p4 script moved to the top-level of the git source directory.

Invoke it as any other git command, like "git p4 clone", for instance.

Note that the top-level git-p4.py script is now the source.  It is
built using make to git-p4, which will be installed.

Windows users can copy the git-p4.py source script directly, possibly
invoking it through a batch file called "git-p4.bat" in the same folder.
It should contain just one line:

    @python "%~d0%~p0git-p4.py" %*
