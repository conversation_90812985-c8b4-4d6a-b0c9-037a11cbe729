.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hmac_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hmac_init \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_hmac_init(gnutls_hmac_hd_t * " dig ", gnutls_mac_algorithm_t " algorithm ", const void * " key ", size_t " keylen ");"
.SH ARGUMENTS
.IP "gnutls_hmac_hd_t * dig" 12
is a \fBgnutls_hmac_hd_t\fP type
.IP "gnutls_mac_algorithm_t algorithm" 12
the HMAC algorithm to use
.IP "const void * key" 12
the key to be used for encryption
.IP "size_t keylen" 12
the length of the key
.SH "DESCRIPTION"
This function will initialize an context that can be used to
produce a Message Authentication Code (MAC) of data.  This will
effectively use the current crypto backend in use by gnutls or the
cryptographic accelerator in use.

Note that despite the name of this function, it can be used
for other MAC algorithms than HMAC.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
