## Syntax highlighting for Javascript.

syntax javascript "\.m?js$"
magic "JavaScript source"
comment "//"

# Declarational stuff.
color green "\<(async|class|const|extends|function|let|this|typeof|var|void)\>"
# Flow control and special keywords.
color brightyellow "\<(do|while|if|else|switch|case|default|for|each|in|of|with)\>"
color brightyellow "\<(await|export|import|throw|try|catch|finally|new|delete)\>"
# "Exit" points.
color magenta "\<(break|continue|return|yield)\>"

# Octal/decimal and hexadecimal numbers.
color cyan "\<([0-9]+|0x[[:xdigit:]]+)\>"
# Special values.
color cyan "\<(true|false|null|undefined)\>"

# Strings.
color brightmagenta ""([^"\]|\\.)*"|'([^'\]|\\.)*'|`([^`\]|\\.)*`"
# Comments.
color brightblue "(^|[[:blank:]])//.*"
color brightblue start="/\*" end="\*/"

# Trailing whitespace.
color ,green "[[:space:]]+$"
