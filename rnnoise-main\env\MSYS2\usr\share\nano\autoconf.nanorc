## Syntax highlighting for Autoconf.

## Original author:  <PERSON><PERSON>
## License:  GPL version 3 or newer

syntax autoconf "\.(ac|m4)$"
comment "#"

# Keywords:
color yellow "\<(if|test|then|elif|else|fi|case|esac|ifelse|for|in|do|done)\>"
color yellow "=|!=|&&|\|\|"

# Macros:
color cyan "\<[[:upper:][:digit:]_]+\>"

# Version numbers:
color red "\<[-_.0-9]+\>"

# Strings:
color red ""[^"]*"|'[^']*'"

# Backticks:
color green "`[^`]*`"

# Error lines:
color brightred "^[[:blank:]]*\*\*\*.*"

# Brackets:
color magenta "\[|\]|\(|\)"

# Comments:
color blue "^[[:blank:]]*#.*|\<dnl.*"

# Trailing whitespace:
color ,green "[[:space:]]+$"
