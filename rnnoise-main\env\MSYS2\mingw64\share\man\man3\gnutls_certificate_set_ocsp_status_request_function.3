.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_ocsp_status_request_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_ocsp_status_request_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_certificate_set_ocsp_status_request_function(gnutls_certificate_credentials_t " sc ", gnutls_status_request_ocsp_func " ocsp_func ", void * " ptr ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t sc" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "gnutls_status_request_ocsp_func ocsp_func" 12
function pointer to OCSP status request callback.
.IP "void * ptr" 12
opaque pointer passed to callback function
.SH "DESCRIPTION"
This function is to be used by server to register a callback to
handle OCSP status requests from the client.  The callback will be
invoked if the client supplied a status\-request OCSP extension.
The callback function prototype is:

typedef int (*gnutls_status_request_ocsp_func)
(gnutls_session_t session, void *ptr, gnutls_datum_t *ocsp_response);

The callback will be invoked if the client requests an OCSP certificate
status.  The callback may return \fBGNUTLS_E_NO_CERTIFICATE_STATUS\fP, if
there is no recent OCSP response. If the callback returns \fBGNUTLS_E_SUCCESS\fP,
it is expected to have the  \fIocsp_response\fP field set with a valid (DER\-encoded)
OCSP response. The response must be a value allocated using \fBgnutls_malloc()\fP,
and will be deinitialized by the caller.

It is possible to set a specific callback for each provided certificate
using \fBgnutls_certificate_set_ocsp_status_request_function2()\fP.
.SH "SINCE"
3.1.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
