Format of keyserver colon listings
==================================

<PERSON> <<EMAIL>>

The machine readable response begins with an optional information
line:

info:<version>:<count>

<version> = this is the version of this protocol.  Currently, this is
	    the number 1.

<count> = the number of keys returned in this response.  Note this is
	  the number of keys, and not the number of lines returned.
	  It should match the number of "pub:" lines returned.

If this optional line is not included, or the version information is
not supplied, the version number is assumed to be 1.

The key listings are made up of several lines per key.  The first line
is for the primary key:

pub:<fingerprint>:<algo>:<keylen>:<creationdate>:<expirationdate>:<flags>

<fingerprint> = this is either the fingerprint or the keyid of the
                key.  Either the 16-digit or 8-digit keyids are
                acceptable, but obviously the fingerprint is best.
                Since it is not possible to calculate the keyid from a
                V3 key fingerprint, for V3 keys this should be either
                the 16-digit or 8-digit keyid only.

<algo> = the algorithm number from RFC-2440.  (i.e. 1==RSA, 17==DSA,
         etc).

<keylen> = the key length (i.e. 1024, 2048, 4096, etc.)

<creationdate> = creation date of the key in standard RFC-2440 form
	         (i.e. number of seconds since 1/1/1970 UTC time)

<expirationdate> = expiration date of the key in standard RFC-2440
	         form (i.e. number of seconds since 1/1/1970 UTC time)

<flags> = letter codes to indicate details of the key, if any.  Flags
	  may be in any order.

	  r == revoked
	  d == disabled
	  e == expired

Following the "pub" line are one or more "uid" lines to indicate user
IDs on the key:

uid:<escaped uid string>:<creationdate>:<expirationdate>:<flags>

<escaped uid string> == the user ID string, with HTTP %-escaping for
			anything that isn't 7-bit safe as well as for
			the ":" character.  Any other characters may
			be escaped, as desired.

creationdate, expirationdate, and flags mean the same here as before.
The information is taken from the self-sig, if any, and applies to the
user ID in question, and not to the key as a whole.

Details:

* All characters except for the <escaped uid string> are
  case-insensitive.

* Obviously, on a keyserver without integrated crypto, many of the
  items given here are not fully trustworthy until the key is
  downloaded and signatures checked.  For example, the information
  that a key is flagged "r" for revoked should be treated as
  untrustworthy information until the key is checked on the client
  side.

* Empty fields are allowed.  For example, a key with no expiration
  date would have the <expirationdate> field empty.  Also, a keyserver
  that does not track a particular piece of information may leave that
  field empty as well.  I expect that the creation and expiration
  dates for user IDs will be left empty in current keyservers.  Colons
  for empty fields on the end of each line may be left off, if
  desired.
