------------------------------------------------------------------------
-- dqPlus.decTest -- decQuad 0+x                                      --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decQuads.
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check
dqpls001 plus       +7.50  -> 7.50

-- Infinities
dqpls011 plus  Infinity    -> Infinity
dqpls012 plus  -Infinity   -> -Infinity

-- NaNs, 0 payload
ddqls021 plus         NaN  -> NaN
ddqls022 plus        -NaN  -> -NaN
ddqls023 plus        sNaN  -> NaN  Invalid_operation
ddqls024 plus       -sNaN  -> -NaN Invalid_operation

-- NaNs, non-0 payload
ddqls031 plus       NaN13  -> NaN13
ddqls032 plus      -NaN13  -> -NaN13
ddqls033 plus      sNaN13  -> NaN13   Invalid_operation
ddqls034 plus     -sNaN13  -> -NaN13  Invalid_operation
ddqls035 plus       NaN70  -> NaN70
ddqls036 plus      -NaN70  -> -NaN70
ddqls037 plus      sNaN101 -> NaN101  Invalid_operation
ddqls038 plus     -sNaN101 -> -NaN101 Invalid_operation

-- finites
dqpls101 plus          7   -> 7
dqpls102 plus         -7   -> -7
dqpls103 plus         75   -> 75
dqpls104 plus        -75   -> -75
dqpls105 plus       7.50   -> 7.50
dqpls106 plus      -7.50   -> -7.50
dqpls107 plus       7.500  -> 7.500
dqpls108 plus      -7.500  -> -7.500

-- zeros
dqpls111 plus          0   -> 0
dqpls112 plus         -0   -> 0
dqpls113 plus       0E+4   -> 0E+4
dqpls114 plus      -0E+4   -> 0E+4
dqpls115 plus     0.0000   -> 0.0000
dqpls116 plus    -0.0000   -> 0.0000
dqpls117 plus      0E-141  -> 0E-141
dqpls118 plus     -0E-141  -> 0E-141

-- full coefficients, alternating bits
dqpls121 plus   2682682682682682682682682682682682    ->  2682682682682682682682682682682682
dqpls122 plus  -2682682682682682682682682682682682    -> -2682682682682682682682682682682682
dqpls123 plus   1341341341341341341341341341341341    ->  1341341341341341341341341341341341
dqpls124 plus  -1341341341341341341341341341341341    -> -1341341341341341341341341341341341

-- Nmax, Nmin, Ntiny
dqpls131 plus  9.999999999999999999999999999999999E+6144   ->  9.999999999999999999999999999999999E+6144
dqpls132 plus  1E-6143                                     ->  1E-6143
dqpls133 plus  1.000000000000000000000000000000000E-6143   ->  1.000000000000000000000000000000000E-6143
dqpls134 plus  1E-6176                                     ->  1E-6176 Subnormal

dqpls135 plus  -1E-6176                                    -> -1E-6176 Subnormal
dqpls136 plus  -1.000000000000000000000000000000000E-6143  -> -1.000000000000000000000000000000000E-6143
dqpls137 plus  -1E-6143                                    -> -1E-6143
dqpls138 plus  -9.999999999999999999999999999999999E+6144  -> -9.999999999999999999999999999999999E+6144
