.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_credentials_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_credentials_get \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_credentials_get(gnutls_session_t " session ", gnutls_credentials_type_t " type ", void ** " cred ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_credentials_type_t type" 12
is the type of the credentials to return
.IP "void ** cred" 12
will contain the credentials.
.SH "DESCRIPTION"
Returns the previously provided credentials structures.

For \fBGNUTLS_CRD_ANON\fP,  \fIcred\fP will be
\fBgnutls_anon_client_credentials_t\fP in case of a client.  In case of
a server it should be \fBgnutls_anon_server_credentials_t\fP.

For \fBGNUTLS_CRD_SRP\fP,  \fIcred\fP will be \fBgnutls_srp_client_credentials_t\fP
in case of a client, and \fBgnutls_srp_server_credentials_t\fP, in case
of a server.

For \fBGNUTLS_CRD_PSK\fP,  \fIcred\fP will be \fBgnutls_psk_client_credentials_t\fP
in case of a client, and \fBgnutls_psk_server_credentials_t\fP, in case
of a server.

For \fBGNUTLS_CRD_CERTIFICATE\fP,  \fIcred\fP will be
\fBgnutls_certificate_credentials_t\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "SINCE"
3.3.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
