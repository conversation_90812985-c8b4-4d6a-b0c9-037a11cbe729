_ipcmk_module()
{
	local cur prev
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-M'|'--shmem')
			COMPREPLY=( $(compgen -W "size" -- $cur) )
			return 0
			;;
		'-S'|'--semaphore')
			COMPREPLY=( $(compgen -W "number" -- $cur) )
			return 0
			;;
		'-p'|'--mode')
			COMPREPLY=( $(compgen -W "mode" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	COMPREPLY=( $(compgen -W "--shmem --semaphore --queue --mode --help --version" -- $cur) )
	return 0
}
complete -F _ipcmk_module ipcmk
