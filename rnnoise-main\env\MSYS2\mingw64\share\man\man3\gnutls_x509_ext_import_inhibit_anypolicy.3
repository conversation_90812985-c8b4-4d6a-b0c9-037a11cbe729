.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_ext_import_inhibit_anypolicy" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_ext_import_inhibit_anypolicy \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_ext_import_inhibit_anypolicy(const gnutls_datum_t * " ext ", unsigned int * " skipcerts ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * ext" 12
the DER encoded extension data
.IP "unsigned int * skipcerts" 12
will hold the number of certificates after which anypolicy is no longer acceptable.
.SH "DESCRIPTION"
This function will return certificate's value of SkipCerts,
by reading the DER data of the Inhibit anyPolicy X.509 extension (*********).

The  \fIskipcerts\fP value is the number of additional certificates that
may appear in the path before the anyPolicy (\fBGNUTLS_X509_OID_POLICY_ANY\fP)
is no longer acceptable.
.SH "RETURNS"
zero, or a negative error code in case of
parsing error.  If the certificate does not contain the Inhibit anyPolicy
extension \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be
returned.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
