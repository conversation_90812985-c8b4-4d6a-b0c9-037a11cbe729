.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_export" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_export \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_export(gnutls_ocsp_resp_const_t " resp ", gnutls_datum_t * " data ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
Holds the OCSP response
.IP "gnutls_datum_t * data" 12
newly allocate buffer holding DER encoded OCSP response
.SH "DESCRIPTION"
This function will export the OCSP response to DER format.
.SH "RETURNS"
In case of failure a negative error code will be
returned, and 0 on success.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
