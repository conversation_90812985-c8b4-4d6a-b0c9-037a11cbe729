<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Cygwin Utilities</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using.html" title="Chapter&#160;3.&#160;Using Cygwin"><link rel="prev" href="using-cygserver.html" title="Cygserver"><link rel="next" href="chattr.html" title="chattr"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Cygwin Utilities</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="using-cygserver.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;3.&#160;Using Cygwin</th><td width="20%" align="right">&#160;<a accesskey="n" href="chattr.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="using-utils"></a>Cygwin Utilities</h2></div></div></div><p>Cygwin comes with a number of command-line utilities that are used to
    manage the UNIX emulation portion of the Cygwin environment. While many of
    these reflect their UNIX counterparts, each was written specifically for
    Cygwin. You may use the long or short option names interchangeably; for
    example, <code class="literal">--help</code> and <code class="literal">-h</code> function
    identically. All of the Cygwin command-line utilities support the
    <code class="literal">--help</code> and <code class="literal">--version</code> options. </p></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="using-cygserver.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="chattr.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Cygserver&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;chattr</td></tr></table></div></body></html>
