/* -*- mode: C; buffer-read-only: t -*-
   !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
   This file is built by regen/feature.pl.
   Any changes made here will be lost!
 */


#ifndef PERL_FEATURE_H_
#define PERL_FEATURE_H_

#if defined(PERL_CORE) || defined (PERL_EXT)

#define HINT_FEATURE_SHIFT	26

#define FEATURE_BAREWORD_FILEHANDLES_BIT    0x0001
#define FEATURE_BITWISE_BIT                 0x0002
#define FEATURE_CLASS_BIT                   0x0004
#define FEATURE___SUB___BIT                 0x0008
#define FEATURE_MYREF_BIT                   0x0010
#define FEATURE_DEFER_BIT                   0x0020
#define FEATURE_EVALBYTES_BIT               0x0040
#define FEATURE_MORE_DELIMS_BIT             0x0080
#define FEATURE_FC_BIT                      0x0100
#define FEATURE_INDIRECT_BIT                0x0200
#define FEATURE_ISA_BIT                     0x0400
#define FEATURE_MODULE_TRUE_BIT             0x0800
#define FEATURE_MULTIDIMENSIONAL_BIT        0x1000
#define FEATURE_POSTDEREF_QQ_BIT            0x2000
#define FEATURE_REFALIASING_BIT             0x4000
#define FEATURE_SAY_BIT                     0x8000
#define FEATURE_SIGNATURES_BIT              0x10000
#define FEATURE_STATE_BIT                   0x20000
#define FEATURE_SWITCH_BIT                  0x40000
#define FEATURE_TRY_BIT                     0x80000
#define FEATURE_UNIEVAL_BIT                 0x100000
#define FEATURE_UNICODE_BIT                 0x200000

#define FEATURE_BUNDLE_DEFAULT	0
#define FEATURE_BUNDLE_510	1
#define FEATURE_BUNDLE_511	2
#define FEATURE_BUNDLE_515	3
#define FEATURE_BUNDLE_523	4
#define FEATURE_BUNDLE_527	5
#define FEATURE_BUNDLE_535	6
#define FEATURE_BUNDLE_537	7
#define FEATURE_BUNDLE_CUSTOM	(HINT_FEATURE_MASK >> HINT_FEATURE_SHIFT)

/* this is preserved for testing and asserts */
#define OLD_CURRENT_HINTS \
    (PL_curcop == &PL_compiling ? PL_hints : PL_curcop->cop_hints)
/* this is the same thing, but simpler (no if) as PL_hints expands
   to PL_compiling.cop_hints */
#define CURRENT_HINTS \
    PL_curcop->cop_hints
#define CURRENT_FEATURE_BUNDLE \
    ((CURRENT_HINTS & HINT_FEATURE_MASK) >> HINT_FEATURE_SHIFT)

#define FEATURE_IS_ENABLED_MASK(mask)                   \
  ((CURRENT_HINTS & HINT_LOCALIZE_HH)                \
    ? (PL_curcop->cop_features & (mask)) : FALSE)

/* The longest string we pass in.  */
#define MAX_FEATURE_LEN (sizeof("bareword_filehandles")-1)

#define FEATURE_FC_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_515 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_FC_BIT)) \
    )

#define FEATURE_ISA_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_535 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_ISA_BIT)) \
    )

#define FEATURE_SAY_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_510 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_SAY_BIT)) \
    )

#define FEATURE_TRY_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_TRY_BIT) \
    )

#define FEATURE_CLASS_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_CLASS_BIT) \
    )

#define FEATURE_DEFER_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_DEFER_BIT) \
    )

#define FEATURE_STATE_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_510 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_STATE_BIT)) \
    )

#define FEATURE_SWITCH_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_510 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_527) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_SWITCH_BIT)) \
    )

#define FEATURE_BITWISE_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_527 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_BITWISE_BIT)) \
    )

#define FEATURE_INDIRECT_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_527 \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_INDIRECT_BIT)) \
    )

#define FEATURE_EVALBYTES_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_515 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_EVALBYTES_BIT)) \
    )

#define FEATURE_SIGNATURES_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_535 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_SIGNATURES_BIT)) \
    )

#define FEATURE___SUB___IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_515 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE___SUB___BIT)) \
    )

#define FEATURE_MODULE_TRUE_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_537 \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_MODULE_TRUE_BIT)) \
    )

#define FEATURE_REFALIASING_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_REFALIASING_BIT) \
    )

#define FEATURE_POSTDEREF_QQ_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_523 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_POSTDEREF_QQ_BIT)) \
    )

#define FEATURE_UNIEVAL_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_515 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_UNIEVAL_BIT)) \
    )

#define FEATURE_MYREF_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_MYREF_BIT) \
    )

#define FEATURE_UNICODE_IS_ENABLED \
    ( \
	(CURRENT_FEATURE_BUNDLE >= FEATURE_BUNDLE_511 && \
	 CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_537) \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_UNICODE_BIT)) \
    )

#define FEATURE_MULTIDIMENSIONAL_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_527 \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_MULTIDIMENSIONAL_BIT)) \
    )

#define FEATURE_BAREWORD_FILEHANDLES_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE <= FEATURE_BUNDLE_535 \
     || (CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_BAREWORD_FILEHANDLES_BIT)) \
    )

#define FEATURE_MORE_DELIMS_IS_ENABLED \
    ( \
	CURRENT_FEATURE_BUNDLE == FEATURE_BUNDLE_CUSTOM && \
	 FEATURE_IS_ENABLED_MASK(FEATURE_MORE_DELIMS_BIT) \
    )


#define SAVEFEATUREBITS() SAVEI32(PL_compiling.cop_features)

#define CLEARFEATUREBITS() (PL_compiling.cop_features = 0)

#define STOREFEATUREBITSHH(hh) \
  (hv_stores((hh), "feature/bits", newSVuv(PL_compiling.cop_features)))

#define FETCHFEATUREBITSHH(hh)                              \
  STMT_START {                                              \
      SV **fbsv = hv_fetchs((hh), "feature/bits", FALSE);   \
      PL_compiling.cop_features = fbsv ? SvUV(*fbsv) : 0;   \
  } STMT_END

#endif /* PERL_CORE or PERL_EXT */

#ifdef PERL_IN_OP_C
PERL_STATIC_INLINE void
S_enable_feature_bundle(pTHX_ SV *ver)
{
    SV *comp_ver = sv_newmortal();
    PL_hints = (PL_hints &~ HINT_FEATURE_MASK)
	     | (
		  (sv_setnv(comp_ver, 5.037),
		   vcmp(ver, upg_version(comp_ver, FALSE)) >= 0)
			? FEATURE_BUNDLE_537 :
		  (sv_setnv(comp_ver, 5.035),
		   vcmp(ver, upg_version(comp_ver, FALSE)) >= 0)
			? FEATURE_BUNDLE_535 :
		  (sv_setnv(comp_ver, 5.027),
		   vcmp(ver, upg_version(comp_ver, FALSE)) >= 0)
			? FEATURE_BUNDLE_527 :
		  (sv_setnv(comp_ver, 5.023),
		   vcmp(ver, upg_version(comp_ver, FALSE)) >= 0)
			? FEATURE_BUNDLE_523 :
		  (sv_setnv(comp_ver, 5.015),
		   vcmp(ver, upg_version(comp_ver, FALSE)) >= 0)
			? FEATURE_BUNDLE_515 :
		  (sv_setnv(comp_ver, 5.011),
		   vcmp(ver, upg_version(comp_ver, FALSE)) >= 0)
			? FEATURE_BUNDLE_511 :
		  (sv_setnv(comp_ver, 5.009005),
		   vcmp(ver, upg_version(comp_ver, FALSE)) >= 0)
			? FEATURE_BUNDLE_510 :
			  FEATURE_BUNDLE_DEFAULT
	       ) << HINT_FEATURE_SHIFT;
    /* special case */
    assert(PL_curcop == &PL_compiling);
    if (FEATURE_UNICODE_IS_ENABLED) PL_hints |=  HINT_UNI_8_BIT;
    else			    PL_hints &= ~HINT_UNI_8_BIT;
}
#endif /* PERL_IN_OP_C */

#ifdef PERL_IN_MG_C

#define magic_sethint_feature(keysv, keypv, keylen, valsv, valbool) \
    S_magic_sethint_feature(aTHX_ (keysv), (keypv), (keylen), (valsv), (valbool))
PERL_STATIC_INLINE void
S_magic_sethint_feature(pTHX_ SV *keysv, const char *keypv, STRLEN keylen,
                        SV *valsv, bool valbool) {
    if (keysv)
      keypv = SvPV_const(keysv, keylen);

    if (memBEGINs(keypv, keylen, "feature_")) {
        const char *subf = keypv + (sizeof("feature_")-1);
        U32 mask = 0;
        switch (*subf) {
        case '_':
            if (keylen == sizeof("feature___SUB__")-1
                 && memcmp(subf+1, "_SUB__", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE___SUB___BIT;
                break;
            }
            return;

        case 'b':
            if (keylen == sizeof("feature_bareword_filehandles")-1
                 && memcmp(subf+1, "areword_filehandles", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_BAREWORD_FILEHANDLES_BIT;
                break;
            }
            else if (keylen == sizeof("feature_bitwise")-1
                 && memcmp(subf+1, "itwise", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_BITWISE_BIT;
                break;
            }
            return;

        case 'c':
            if (keylen == sizeof("feature_class")-1
                 && memcmp(subf+1, "lass", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_CLASS_BIT;
                break;
            }
            return;

        case 'd':
            if (keylen == sizeof("feature_defer")-1
                 && memcmp(subf+1, "efer", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_DEFER_BIT;
                break;
            }
            return;

        case 'e':
            if (keylen == sizeof("feature_evalbytes")-1
                 && memcmp(subf+1, "valbytes", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_EVALBYTES_BIT;
                break;
            }
            return;

        case 'f':
            if (keylen == sizeof("feature_fc")-1
                 && memcmp(subf+1, "c", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_FC_BIT;
                break;
            }
            return;

        case 'i':
            if (keylen == sizeof("feature_indirect")-1
                 && memcmp(subf+1, "ndirect", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_INDIRECT_BIT;
                break;
            }
            else if (keylen == sizeof("feature_isa")-1
                 && memcmp(subf+1, "sa", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_ISA_BIT;
                break;
            }
            return;

        case 'm':
            if (keylen == sizeof("feature_module_true")-1
                 && memcmp(subf+1, "odule_true", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_MODULE_TRUE_BIT;
                break;
            }
            else if (keylen == sizeof("feature_more_delims")-1
                 && memcmp(subf+1, "ore_delims", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_MORE_DELIMS_BIT;
                break;
            }
            else if (keylen == sizeof("feature_multidimensional")-1
                 && memcmp(subf+1, "ultidimensional", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_MULTIDIMENSIONAL_BIT;
                break;
            }
            else if (keylen == sizeof("feature_myref")-1
                 && memcmp(subf+1, "yref", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_MYREF_BIT;
                break;
            }
            return;

        case 'p':
            if (keylen == sizeof("feature_postderef_qq")-1
                 && memcmp(subf+1, "ostderef_qq", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_POSTDEREF_QQ_BIT;
                break;
            }
            return;

        case 'r':
            if (keylen == sizeof("feature_refaliasing")-1
                 && memcmp(subf+1, "efaliasing", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_REFALIASING_BIT;
                break;
            }
            return;

        case 's':
            if (keylen == sizeof("feature_say")-1
                 && memcmp(subf+1, "ay", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_SAY_BIT;
                break;
            }
            else if (keylen == sizeof("feature_signatures")-1
                 && memcmp(subf+1, "ignatures", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_SIGNATURES_BIT;
                break;
            }
            else if (keylen == sizeof("feature_state")-1
                 && memcmp(subf+1, "tate", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_STATE_BIT;
                break;
            }
            else if (keylen == sizeof("feature_switch")-1
                 && memcmp(subf+1, "witch", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_SWITCH_BIT;
                break;
            }
            return;

        case 't':
            if (keylen == sizeof("feature_try")-1
                 && memcmp(subf+1, "ry", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_TRY_BIT;
                break;
            }
            return;

        case 'u':
            if (keylen == sizeof("feature_unicode")-1
                 && memcmp(subf+1, "nicode", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_UNICODE_BIT;
                break;
            }
            else if (keylen == sizeof("feature_unieval")-1
                 && memcmp(subf+1, "nieval", keylen - sizeof("feature_")) == 0) {
                mask = FEATURE_UNIEVAL_BIT;
                break;
            }
            return;

        default:
            return;
        }
        if (valsv ? SvTRUE(valsv) : valbool)
            PL_compiling.cop_features |= mask;
        else
            PL_compiling.cop_features &= ~mask;
    }
}
#endif /* PERL_IN_MG_C */

#endif /* PERL_FEATURE_H_ */

/* ex: set ro ft=c: */
