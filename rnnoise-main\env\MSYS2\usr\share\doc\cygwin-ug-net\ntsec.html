<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>POSIX accounts, permission, and security</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using.html" title="Chapter&#160;3.&#160;Using Cygwin"><link rel="prev" href="using-cygwinenv.html" title="The CYGWIN environment variable"><link rel="next" href="using-cygserver.html" title="Cygserver"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">POSIX accounts, permission, and security</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="using-cygwinenv.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;3.&#160;Using Cygwin</th><td width="20%" align="right">&#160;<a accesskey="n" href="using-cygserver.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ntsec"></a>POSIX accounts, permission, and security</h2></div></div></div><p>This section discusses how the Windows security model is
utilized in Cygwin to implement POSIX account information, POSIX-like
permissions, and how the Windows authentication model is used to allow
cygwin applications to switch users in a POSIX-like fashion.</p><p>The setting of POSIX-like file and directory permissions is
controlled by the <a class="link" href="using.html#mount-table" title="The Cygwin Mount Table">mount</a> option
<code class="literal">(no)acl</code> which is set to <code class="literal">acl</code> by
default.</p><p>We start with a short overview.  Note that this overview must
be necessarily short.  If you want to learn more about the Windows security
model, see the <a class="ulink" href="http://msdn.microsoft.com/en-us/library/aa374860(VS.85).aspx" target="_top">Access Control</a> article in MSDN documentation.</p><p>POSIX concepts and in particular the POSIX security model are not
discussed here, but assumed to be understood by the reader.  If you
don't know the POSIX security model, search the web for beginner
documentation.</p><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ntsec-common"></a>Brief overview of Windows security</h3></div></div></div><p>In the Windows security model, almost any "object" is securable.
"Objects" are files, processes, threads, semaphores, etc.</p><p>Every object has a data structure attached, called a "security
descriptor" (SD).  The SD contains all information necessary to control
who can access an object, and to determine what they are allowed to do
to or with it.  The SD of an object consists of five parts:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Flags which control several aspects of this SD. This is
not discussed here.</p></li><li class="listitem"><p>The SID of the object owner.</p></li><li class="listitem"><p>The SID of the object owner group.</p></li><li class="listitem"><p>A list of "Access Control Entries" (ACE), called the
"Discretionary Access Control List" (DACL).</p></li><li class="listitem"><p>Another list of ACEs, called the "Security Access Control List"
(SACL), which doesn't matter for our purpose.  We ignore it here.</p></li></ul></div><p>Every ACE contains a so-called "Security IDentifier" (SID) and
other stuff which is explained a bit later.  Let's talk about the SID first.
</p><p>A SID is a unique identifier for users, groups, computers and
Active Directory (AD) domains.  SIDs are basically comparable to POSIX
user ids (UIDs) and group ids (GIDs), but are more complicated because
they are unique across multiple machines or domains.  A SID is a
structure of multiple numerical values.  There's a convenient convention
to type SIDs, as a string of numerical fields separated by hyphen
characters.  Here's an example:</p><p>SID of a machine "foo":</p><pre class="screen">
  S-1-5-21-*********-**********-*********
</pre><p>SID of a user "johndoe" of the system "foo":</p><pre class="screen">
  S-1-5-21-*********-**********-*********-1023
</pre><p>The first field is always "S", which is just a notational convention
to show that this is a SID.  The second field is the version number of
the SID structure, So far there exists only one version of SIDs, so this
field is always 1.  The third and fourth fields represent the "authority"
which can be thought of as a type or category of SIDs.  There are a
couple of builtin accounts and accounts with very special meaning which
have certain well known values in these third and fourth fields.
However, computer and domain SIDs always start with "S-1-5-21".  The
next three fields, all 32 bit values, represent the unique 96 bit
identifier of the computer system.  This is a hopefully unique value all
over the world, but in practice it's sufficient if the computer SIDs are
unique within a single Windows network.</p><p>As you can see in the above example, SIDs of users (and groups)
are identical to the computer SID, except for an additional part, the
so-called "relative identifier" (RID).  So the SID of a user is always 
uniquely attached to the system on which the account has been generated.</p><p>It's a bit different in domains.  The domain has its own SID, and
that SID is identical to the SID of the first domain controller, on
which the domain is created.  Domain user SIDs look exactly like the
computer user SIDs, the leading part is just the domain SID and the RID
is created when the user is created.</p><p>Ok, consider you created a new domain "bar" on some new domain
controller and you would like to create a domain account "johndoe":</p><p>SID of a domain "bar.local":</p><pre class="screen">
  S-1-5-21-*********-**********-*********
</pre><p>SID of a user "johndoe" in the domain "bar.local":</p><pre class="screen">
  S-1-5-21-*********-**********-*********-1207
</pre><p>So you now have two accounts called johndoe, one account
created on the machine "foo", one created in the domain "bar.local".
Both have different SIDs and not even the RID is the same.  How do
the systems know it's the same account?  After all, the name is
the same, right?  The answer is, these accounts are <span class="bold"><strong>not</strong></span> identical.  All machines on the network will
treat these SIDs as identifying two separate accounts.  One is
"FOO\johndoe", the other one is "BAR\johndoe" or "<EMAIL>".
Different SID, different account.  Full stop.  </p><p>Starting with Cygwin 1.7.34, Cygwin uses an automatic, internal
translation from Windows SID to POSIX UID/GID.  This mechanism, which is
the preferred method for the SID&lt;=&gt;UID/GID mapping, is described in
detail in <a class="xref" href="ntsec.html#ntsec-mapping" title="Mapping Windows accounts to POSIX accounts">the section called &#8220;Mapping Windows accounts to POSIX accounts&#8221;</a>.</p><p>Prior to Cygwin 1.7.34, the last part of the SID, the so called
"Relative IDentifier" (RID), was by default used as UID and/or GID
when you created the <code class="filename">/etc/passwd</code> and
<code class="filename">/etc/group</code> files using the
<span class="command"><strong><a class="link" href="mkpasswd.html" title="mkpasswd">mkpasswd</a></strong></span> and
<span class="command"><strong><a class="link" href="mkgroup.html" title="mkgroup">mkgroup</a></strong></span> tools.
These tools as well as reading accounts from <code class="filename">/etc/passwd</code>
and <code class="filename">/etc/group</code> files is still present in recent
versions of Cygwin, but you should switch to the aforementioned
automatic translation, unless you have very specific needs. Again,
see <a class="xref" href="ntsec.html#ntsec-mapping" title="Mapping Windows accounts to POSIX accounts">the section called &#8220;Mapping Windows accounts to POSIX accounts&#8221;</a> for the details.</p><p>Do you still remember the SIDs with special meaning?  In offical
notation they are called "well-known SIDs".  For example, POSIX has no GID
for the group of "all users" or "world" or "others".  The last three rwx
bits in a unix-style permission value just represent the permissions for
"everyone who is not the owner or is member of the owning group".
Windows has a SID for these poor souls, the "Everyone" SID.  Other
well-known SIDs represent circumstances under which a process is
running, rather than actual users or groups.  Here are a few examples
for well-known SIDs:</p><pre class="screen">
Everyone                        S-1-1-0    Simply everyone...
Batch                           S-1-5-3    Processes started via the task
					   scheduler are member of this group.
Interactive			S-1-5-4    Only processes of users which are
					   logged in via an interactive
					   session are members here.
Authenticated Users             S-1-5-11   Users which have gone through
                                           the authentication process and
					   survived.  Anonymously accessing
					   users are not incuded here.
SYSTEM                          S-1-5-18   A special account which has all
					   kinds of dangerous rights, sort of
					   an uber-root account.
</pre><p>For a full list please refer to the MSDN document <a class="ulink" href="http://msdn.microsoft.com/en-us/library/aa379649.aspx" target="_top">Well-known
SIDs</a>.  The Cygwin package called "csih" provides a tool,
/usr/lib/csih/getAccountName.exe, which can be used to print the
(possibly localized) name for the various well-known SIDS.</p><p>Naturally, well-known SIDs are the same on each machine, so they are
not unique to a machine or domain.  They have the same meaning across
the Windows network.</p><p>Additionally, there are a couple of well-known builtin groups,
which have the same SID on every machine and which have certain user
rights by default:</p><pre class="screen">
administrators                  S-1-5-32-544
users                           S-1-5-32-545
guests                          S-1-5-32-546
...
</pre><p>For instance, every account is usually member in the "Users"
group.  All administrator accounts are member of the "Administrators"
group.  That's all about it as far as single machines are involved.  In
a domain environment it's a bit more tricky.  Since these SIDs are not
unique to a machine, every domain user and every domain group can be a
member of these well known groups.  Consider the domain group "Domain
Admins".  This group is by default in the "Administrators" group.  Let's
assume the above computer called "foo" is a member machine of the domain
"bar.local".  If you stick the user "BAR\johndoe" into the group "Domain
Admins", this guy will automatically be a member of the administrators
group on "foo" when logging on to "foo".  Neat, isn't it?</p><p>Back to ACE and ACL.  POSIX is able to create three different
permissions, the permissions for the owner, for the group and for the
world.  In contrast the Windows ACL has a potentially infinite number of
members... as long as they fit into 64K.  Every member is an ACE.
ACE consist of three parts:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>The type of the ACE (allow ACE or deny ACE).</p></li><li class="listitem"><p>Permission bits, 32 of them.</p></li><li class="listitem"><p>The SID for which the permissions are allowed or denied.</p></li></ul></div><p>The two (for us) important types of ACEs are the "access allowed
ACE" and the "access denied ACE".  As the names imply, the allow ACE
tells the system to allow the given permissions to the SID, the deny ACE
results in denying the specific permission bits.</p><p>The possible permissions on objects are more detailed than in
POSIX.  For example, the permission to delete an object is different
from the permission to change object data, and even changing object data
can be separated into different permission bits for different kind of
data.  But there's a problem with the definition of a "correct" ACL
which disallows mapping of certain POSIX permissions cleanly.  See
<a class="xref" href="ntsec.html#ntsec-files" title="File permissions">the section called &#8220;File permissions&#8221;</a>.</p><p>POSIX is able to create only three different permissions?  Not quite.
Newer operating systems and file systems on POSIX systems also provide
access control lists.  Two different APIs exist for accessing these
ACLs, the Solaris API and the POSIX API.  Cygwin implements the original
Solaris API to access Windows ACLs in a Unixy way.  Online man pages for the
Solaris ACL API can be found on
<a class="ulink" href="http://docs.oracle.com" target="_top">http://docs.oracle.com</a>.
For an overview see <a class="ulink" href="http://docs.oracle.com/cd/E23824_01/html/821-1474/acl-5.html#scrolltoc" target="_top">acl(5)</a>.</p></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ntsec-mapping"></a>Mapping Windows accounts to POSIX accounts</h3></div></div></div><p>
For as long as Cygwin has existed, it has stored user and group information in
<code class="filename">/etc/passwd</code> and <code class="filename">/etc/group</code> files. 
Under the assumption that these files would never be too large, the first
process in a process tree, as well as every execing process within the tree
would parse them into structures in memory.  Thus every Cygwin process would
contain an expanded copy of the full information from
<code class="filename">/etc/passwd</code> and <code class="filename">/etc/group</code>.
</p><p>
This approach has a few downsides.  One of them is that the idea that these
files will always be small, is flawed.  Another one is that reading the entire
file is most of the time entirely useless, since most processes only
need information on their own user and the primary group.  Last but not
least, the passwd and group files have to be maintained separately from
the already existing Windows user databases, the local SAM and Active
Directory.
</p><p>
On the other hand, we have to have this mapping between Windows SIDs and
POSIX uid/gid values, so we need a mechanism to convert SIDs to uid/gid
values and vice versa.
</p><p>
Microsoft "Services for UNIX" (SFU) (deprecated since Windows 8/Server 2012)
never used passwd/group files.  Rather, SFU used a fixed, computational mapping
between SIDs and POSIX uid/gid which even has Active Directory support.  It
allows us to generate uid/gid values from SIDs and vice versa.  The mechanism is
documented, albeit in a confusing way and spread over multiple MSDN articles.
</p><p>
Starting with Cygwin 1.7.34, Cygwin utilizes an approach inspired by the
mapping method as implemented by SFU, with a few differences for backward
compatibility and to handle some border cases differently.
</p><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-mapping-how"></a>Mapping Windows SIDs to POSIX uid/gid values</h4></div></div></div><p>
The following description assumes you're comfortable with the concept of
Windows SIDs and RIDs.  For a brief introduction, see
<a class="xref" href="ntsec.html#ntsec-common" title="Brief overview of Windows security">the section called &#8220;Brief overview of Windows security&#8221;</a>.
</p><p>
Cygwin's mapping between SIDs and uid/gid values works in two ways.
</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Read <code class="filename">/etc/passwd<code class="filename"> and
</code>/etc/group</code> files if they exist, just as in the olden
days, mainly for backward compatibility.</p></li><li class="listitem"><p>If no files are present, or if an entry is missing in the files,
ask Windows.</p></li></ul></div><p>
At least, that's the default behaviour now.  It will be configurable
using a file <code class="filename">/etc/nsswitch.conf</code>, which is discussed in
<a class="xref" href="ntsec.html#ntsec-mapping-nsswitch" title="The /etc/nsswitch.conf file">the section called &#8220;The <code class="filename">/etc/nsswitch.conf</code> file&#8221;</a>.  Let's explore the default
for now.
</p><p>
If the passwd or group files are present, they will be scanned on demand as
soon as a mapping from SIDs to uid/gid or account names is required.  The new
mechanism will never read the entire file into memory, but only scan for
the requested entry and cache this one in memory.
</p><p>
If no entry is found, or no passwd or group file was present, Cygwin
will ask the OS.
</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>
If the first process in a Cygwin process tree determines that no
<code class="filename">/etc/passwd</code> or <code class="filename">/etc/group</code> file is
present, no other process in the entire process tree will try to read the files
later on.  This is done for self-preservation.  It's rather bad if the uid
or gid of a user changes during the lifetime of a process tree.
</p><p>
For the same reason, if you delete the <code class="filename">/etc/passwd</code>
or <code class="filename">/etc/group</code> file, this will be ignored.  The passwd
and group records read from the files will persist in memory until either a
new <code class="filename">/etc/passwd</code> or <code class="filename">/etc/group</code>
is created, or you exit all processes in the current process tree.
</p><p>
See the note in <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch" title="The /etc/nsswitch.conf file">the section called &#8220;The <code class="filename">/etc/nsswitch.conf</code> file&#8221;</a> for some
comprehensive examples.
</p></div><p>
So if we've drawn a blank reading the files, we're going to ask the OS.
First thing, we ask the local machine for the SID or the username.  The
OS functions
<a class="ulink" href="http://msdn.microsoft.com/en-us/library/windows/desktop/aa379166%28v=vs.85%29.aspx" target="_top">LookupAccountSid</a>
and
<a class="ulink" href="http://msdn.microsoft.com/en-us/library/windows/desktop/aa379159%28v=vs.85%29.aspx" target="_top">LookupAccountName</a>
are pretty intelligent.  They have all the stuff built in to ask for any
account of the local machine, the Active Directory domain of the machine,
the Global Catalog of the forest of the domain, as well as any trusted
domain of our forest for the information.  One OS call and we're
practically done...
</p><p>
Except, the calls only return the mapping between SID, account name and the
account's domain.  We don't have a mapping to POSIX uid/gid and we're missing
information on the user's home dir and login shell.
</p><p>
Let's discuss the SID&lt;=&gt;uid/gid mapping first.  Here's how it works.
</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>
<a class="ulink" href="http://msdn.microsoft.com/en-us/library/aa379649.aspx" target="_top">Well-known
SIDs</a>
in the NT_AUTHORITY domain of the S-1-5-RID type, or aliases of the
S-1-5-32-RID type are mapped to the uid/gid value RID.  Examples:
</p><pre class="screen">
  "SYSTEM" S-1-5-18                    &lt;=&gt; uid/gid: 18
  "Users"  S-1-5-32-545                &lt;=&gt; uid/gid: 545
</pre></li><li class="listitem"><p>
Other well-known SIDs in the NT_AUTHORITY domain (S-1-5-X-RID):
</p><pre class="screen">
  S-1-5-X-RID                          &lt;=&gt; uid/gid: 0x1000 * X + RID
</pre><p>Example:</p><pre class="screen">
  "NTLM Authentication" S-1-5-64-10    &lt;=&gt; uid/gid: 0x4000A == 262154
</pre></li><li class="listitem"><p>
Other well-known SIDs:
</p><pre class="screen">
  S-1-X-Y                              &lt;=&gt; uid/gid: 0x10000 + 0x100 * X + Y
</pre><p>Example:</p><pre class="screen">
  "LOCAL" S-1-2-0                      &lt;=&gt; uid/gid: 0x10200 == 66048
  "Creator Group" S-1-3-1              &lt;=&gt; uid/gid: 0x10301 == 66305
</pre></li><li class="listitem"><p>
Logon SIDs: The LogonSid of the current user's session is converted to the
fixed uid 0xfff == 4095 and named "CurrentSession".  Any other LogonSid is
converted to the fixed uid 0xffe == 4094 and named "OtherSession".
</p></li><li class="listitem"><p>
Mandatory Labels:
</p><pre class="screen">
  S-1-16-RID                           &lt;=&gt; uid/gid: 0x60000 + RID
</pre><p>Example:</p><pre class="screen">
  "Medium Mandatory Level" S-1-16-8192 &lt;=&gt; uid/gid: 0x62000 == 401408
</pre></li><li class="listitem"><p>
Accounts from the local machine's user DB (SAM):
</p><pre class="screen">
  S-1-5-21-X-Y-Z-RID                   &lt;=&gt; uid/gid: 0x30000 + RID
</pre><p>Example:</p><pre class="screen">
  "Administrator" S-1-5-21-X-Y-Z-500   &lt;=&gt; uid/gid: 0x301f4 == 197108
</pre></li><li class="listitem"><p>
Accounts from the machine's primary domain:
</p><pre class="screen">
  S-1-5-21-X-Y-Z-RID                   &lt;=&gt; uid/gid: 0x100000 + RID
</pre><p>Example:</p><pre class="screen">
  "Domain Users" S-1-5-21-X-Y-Z-513    &lt;=&gt; uid/gid: 0x100201 == 1049089
</pre></li><li class="listitem"><p>
Accounts from a trusted domain of the machine's primary domain:
</p><pre class="screen">
  S-1-5-21-X-Y-Z-RID                   &lt;=&gt; uid/gid: trustPosixOffset(domain) + RID
</pre><p>
<code class="literal">trustPosixOffset</code>?  This needs a bit of explanation.  This
value exists in Windows domains already since before Active Directory days.
What happens is this.  If you create a domain trust between two domains, a
trustedDomain entry will be added to both databases.  It describes how
<span class="emphasis"><em>this</em></span> domain trusts the <span class="emphasis"><em>other</em></span> domain.
One attribute of a trust is a 32 bit value called
<code class="literal">trustPosixOffset</code>  For each new trust,
<code class="literal">trustPosixOffset</code> will get some automatic value.  In recent
AD domain implementations, the first trusted domain will get
<code class="literal">trustPosixOffset</code> set to 0x80000000.  Following domains will
get lower values.  Unfortunately the domain admins are allowed to set the
<code class="literal">trustPosixOffset</code> value for each trusted domain to some
arbitrary 32 bit value, no matter what the other
<code class="literal">trustPosixOffset</code> are set to, thus allowing any kind of
collisions between the <code class="literal">trustPosixOffset</code> values of domains.
That's not exactly helpful, but as the user of this value, we have to
<span class="emphasis"><em>trust</em></span> the domain admins to set
<code class="literal">trustPosixOffset</code> to sensible values, or to keep it at the
system chosen defaults.
</p><p>
So, for the first (or only) trusted domain of your domain, the automatic offset
is 0x80000000.  An example for a user of that trusted domain is
</p><pre class="screen">
  S-1-5-21-X-Y-Z-1234                  &lt;=&gt; uid/gid 0x800004d2 == 2147484882
</pre><p>
There's one problem with this approach.  Assuming you're running in the context
of a local SAM user on a domain member machine.  Local users don't have the
right to fetch this kind of domain information from the DC, they'll get
permission denied.  In this case Cygwin will fake a sensible
<code class="literal">trustPosixOffset</code> value.
</p><p>
Another problem is if the AD administrators chose an unreasonably small
<code class="literal">trustPosixOffset</code> value.  Anything below the hexadecimal
value 0x100000 (the POSIX offset of the primary domain) is bound to produce
collisions with system accounts as well as local accounts.  The right thing
to do in this case is to notify your administrator of the problem and to ask
for moving the offset to a more reasonable value.  However, to reduce the
probability for collisions, Cygwin overrides this offset with a sensible
fixed replacement offset.
</p></li><li class="listitem"><p>
Local accounts from another machine in the network:
</p><p>
There's no SID&lt;=&gt;uid/gid mapping implemented for this case.  The problem
is, there's no way to generate a bijective mapping.  There's no central place
which keeps an analogue of the <code class="literal">trustPosixOffset</code>, and there's
the additional problem that the
<a class="ulink" href="http://msdn.microsoft.com/en-us/library/windows/desktop/aa379166%28v=vs.85%29.aspx" target="_top">LookupAccountSid</a>
and
<a class="ulink" href="http://msdn.microsoft.com/en-us/library/windows/desktop/aa379159%28v=vs.85%29.aspx" target="_top">LookupAccountName</a>
functions cannnot resolve the SIDs, unless they know the name of the machine
this SID comes from.  And even then it will probably suffer a
<code class="literal">Permission denied</code> error when trying to ask the machine
for its local account.
</p></li></ul></div><p>
Now we have a semi-bijective mapping between SIDs and POSIX uid/gid values,
but given that we have potentially users and groups in different domains having
the same name, how do we uniquely distinguish between them by name?  Well, we
can do that by making their names unique in a per-machine way.  Dependent on
the domain membership of the account, and dependent of the machine being a
domain member or not, the user and group names will be generated using a domain
prefix and a separator character between domain and account name.
The  separator character is the plus sign, <code class="literal">+</code>.
</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>
Well-known and builtin accounts will be named as in Windows:
</p><pre class="screen">
  "SYSTEM", "LOCAL", "Medium Mandatory Level", ...
</pre></li><li class="listitem"><p>
If the machine is not a domain member machine, only local accounts can be resolved
into names, so for ease of use, just the account names are used as Cygwin
user/group names:
</p><pre class="screen">
  "corinna", "bigfoot", "None", ...
</pre></li><li class="listitem"><p>
If the machine is a domain member machine, all accounts from the primary domain
of the machine are mapped to Cygwin names without domain prefix:
</p><pre class="screen">
  "corinna", "bigfoot", "Domain Users", ...
</pre><p>
while accounts from other domains are prepended by their domain:
</p><pre class="screen">
  "DOMAIN1+corinna", "DOMAIN2+bigfoot", "DOMAIN3+Domain Users", ...
</pre></li><li class="listitem"><p>
Local machine accounts of a domain member machine get a Cygwin user name the
same way as accounts from another domain:  The local machine name gets
prepended:
</p><pre class="screen">
  "MYMACHINE+corinna", "MYMACHINE+bigfoot", "MYMACHINE+None", ...
</pre></li><li class="listitem"><p>
If LookupAccountSid fails, Cygwin checks the accounts against the known trusted
domains.  If the account is from one of the trusted domains, an artificial
account name is created.  It consists of the domain name, and a special name
created from the account RID:
</p><pre class="screen">
  "MY_DOM+User(1234)", "MY_DOM+Group(5678)"
</pre><p>
Otherwise we know nothing about this SID, so it will be mapped to the
fake accounts <code class="literal">Unknown+User</code>/<code class="literal">Unknown+Group</code>
with uid/gid -1.
</p></li></ul></div></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-mapping-caching"></a>Caching account information</h4></div></div></div><p>
The information fetched from the Windows account database or the
<code class="filename">/etc/passwd</code> and <code class="filename">/etc/group</code> files is
cached by the process.  The cached information is inherited by Cygwin child
processes.  A Cygwin process invoked from a Windows command, such as CMD.exe,
will start a new Cygwin process tree and the caching starts from scratch
(unless <span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> is
running, but read on).
</p><p>
While usually working fine, this has some drawbacks.  Consider a shell calling
<span class="command"><strong>id</strong></span>.  <span class="command"><strong>id</strong></span> fetches all group information
from the current token and caches them.  Unfortunately <span class="command"><strong>id</strong></span>
doesn't start any child processes, so the information is lost as soon as
<span class="command"><strong>id</strong></span> exits.
</p><p>
But there's another caching mechanism available.  If
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> is running
it will provide passwd and group entry caching for all processes in every Cygwin
process tree started after
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span>.  So, if
you start a Cygwin Terminal and
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> is running
at the time, <span class="command"><strong>mintty</strong></span>, the shell, and all child processes will
use <span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> caching.
If you start a Cygwin Terminal and
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> is not
running at the time, none of the processes started inside this terminal window
will use <span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span>
caching.
</p><p>
The advantage of
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> caching is
that it's system-wide and, as long as
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> is running,
unforgetful.  Every Cygwin process on the system will have the
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> cache at
its service.  Additionally, all information requested from
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> once, will
be cached inside the process itself and, again, propagated to child processes.
</p><p>
If you automatically start Cygwin processes as Windows services at system
startup, you may wish to consider starting
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> first in
order to take advantage of this system-wide caching.  To assure that
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span> has started
prior to starting <span class="command"><strong>cygsshd</strong></span> or other Cygwin services, you may
wish to create service startup dependencies.
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">Cygserver</a></strong></span> should
probably wait for Windows TCPIP and AFD services before it starts, and then
other Cygwin process should start after
<span class="command"><strong><a class="link" href="using-cygserver.html" title="Cygserver">cygserver</a></strong></span>.  Example
Windows commands to accomplish this (after the services already exist) are
shown below.  You will need an administrative prompt to run the
<span class="command"><strong>sc config</strong></span> commands.
</p><pre class="screen">
  # Delay Cygserver until TCPIP and AFD have started
  # Note the (odd) required space character after "depend="

  sc config cygserver depend= tcpip/afd

  # Delay sshd until after Cygserver has started
  # Again note the (odd) required space character after "depend="

  sc config cygsshd depend= cygserver

  # View the Cygserver service details

  sc qc cygserver
</pre><p>
Note that this <span class="command"><strong>sc config</strong></span> command
<span class="emphasis"><em>replaces</em></span> any existing dependencies.  The above changes
will not impact the running instance, only future instances.
</p><pre class="screen">
  # To remove all dependencies from the cygserver service

  sc config cygserver depend= /
</pre></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-mapping-passwdinfo"></a>Cygwin user names, home dirs, login shells</h4></div></div></div><p>
Obviously, if you don't maintain <code class="filename">passwd</code> and
<code class="filename">group</code> files, you need to have a way to maintain the other
fields of a passwd entry as well.  A couple of things come to mind:
</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>
You want to use a Cygwin username different from your Windows username.
</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>
This is only supported via <code class="filename">/etc/passwd</code>.  A Cygwin
username maintained in the Windows user databases would require very costly
(read: slow) search operations.
</p></div></li><li class="listitem"><p>
You want to change the primary group of a user.  For AD accounts this is
not supported.  The primary group of a user is always the Windows
primary group set in Active Directory and can't be changed.  For SAM
accounts, you can add the primary group to the SAM
<code class="literal">description</code> field of the user.  See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-desc" title="The desc schema">the section called &#8220;The <code class="literal">desc</code> schema&#8221;</a> for more info.
</p></li><li class="listitem"><p>
You want a home dir different from the default
<code class="filename">/home/<USER>/code>.
</p></li><li class="listitem"><p>
You want to specify a different login shell than <code class="filename">/bin/bash</code>.
</p></li><li class="listitem"><p>
You want to add specific content to the pw_gecos field.
</p></li></ul></div><p>
For simple needs you can create <code class="filename">/etc/passwd</code> and/or
<code class="filename">/etc/group</code> files with entries for your account
and tweak that.
</p><p>
For bigger installations, maintaining per-client files is rather troublesome.
Also, no two environments are the same, so the needs are pretty different.
Therefore Cygwin supports configuring how to fetch home directory,
login shell, and gecos information in /etc/nsswitch.conf.  See the next
section for detailed information how to configure Cygwin's account handling.
</p></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-mapping-nsswitch"></a>The <code class="filename">/etc/nsswitch.conf</code> file</h4></div></div></div><p>
On Linux and some other UNIXy OSes, we have a file called
<a class="ulink" href="http://linux.die.net/man/5/nsswitch.conf" target="_top">/etc/nsswitch.conf</a>.
Among other things, it determines how passwd and group entries are generated.
That's what Cygwin now provides as well.
</p><p>
The <code class="filename">/etc/nsswitch.conf</code> file is optional.  If you don't
have one, Cygwin uses sensible defaults.
</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>
The <code class="filename">/etc/nsswitch.conf</code> file is read exactly once by
the first process of a Cygwin process tree.  If there was no
<code class="filename">/etc/nsswitch.conf</code> file when this first process started,
then no other process in the running Cygwin process tree will try to read the
file.
</p><p>
If you create or change <code class="filename">/etc/nsswitch.conf</code>, you have to
restart all Cygwin processes that need to see the change.  If the process
you want to see the change is a child of another process, you need to restart
all of that process's parents, too.
</p><p>
For example, if you run <span class="command"><strong>vim</strong></span> inside the default Cygwin
Terminal, <span class="command"><strong>vim</strong></span> is a child of your shell, which is a child
of <span class="command"><strong>mintty</strong></span>.  If you edit
<code class="filename">/etc/nsswitch.conf</code> in that <span class="command"><strong>vim</strong></span>
instance, your shell won't immediately see the change, nor will
<span class="command"><strong>vim</strong></span> if you restart it from that same shell instance.
This is because both are getting their nsswitch information from their
ancestor, <span class="command"><strong>mintty</strong></span>.  You have to start a fresh terminal
window for the change to take effect.
</p><p>
By contrast, if you leave that Cygwin Terminal window open after making the
change to <code class="filename">/etc/nsswitch.conf</code>, then restart a Cygwin
service like <span class="command"><strong>cron</strong></span>, <span class="command"><strong>cron</strong></span> will see the
change, because it is not a child of <span class="command"><strong>mintty</strong></span> or any other
Cygwin process. (Technically, it is a child of <span class="command"><strong>cygrunsrv</strong></span>,
but that instance also restarts when you restart the service.)
</p><p>
The reason we point all this out is that the requirements for restarting
things are not quite as stringent as when you replace
<code class="filename">cygwin1.dll</code>. If you have three process trees, you have
three independent copies of the nsswitch information.  If you start a fresh
process tree, it will see the changes.  As long as any process in an existing
process tree remains running, all processes in that tree will continue to use
the old information.
</p></div><p>
So, what settings can we perform with <code class="filename">/etc/nsswitch.conf</code>?
Let's start with an example <code class="filename">/etc/nsswitch.conf</code> file
set up to all default values:
</p><pre class="screen">
  # /etc/nsswitch.conf
  passwd: files db
  group:  files db
  db_enum:  cache builtin
  db_home:  /home/<USER>
  db_shell: /bin/bash
  db_gecos: &lt;empty&gt;
</pre><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-syntax"></a>The <code class="filename">/etc/nsswitch.conf</code> syntax</h5></div></div></div><p>
The first line, starting with a hash <code class="literal">#</code> is a comment.
The hash character starts a comment, just as in shell scripts.  Everything
up to the end of the line is ignored.  So this:
</p><pre class="screen">
  foo:  bar # baz
</pre><p>
means, set "foo" to value "bar", ignore everything after the hash.
</p><p>
The other lines define the available settings.  The first word up to a
colon is a keyword.  Note that the colon <span class="emphasis"><em>must</em></span> follow
immediately after the keyword.  This is a valid line:
</p><pre class="screen">
  foo: bar
</pre><p>
This is not valid:
</p><pre class="screen">
  foo  :  bar
</pre><p>
Apart from this restriction, the remainder of the line can have as
many spaces and TABs as you like.
</p><p>
When the same keyword occurs multiple times, the last one wins, as if the
previous ones were ignored.
</p></div><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-pwdgrp"></a>The <code class="literal">passwd:</code> and <code class="literal">group:</code> settings</h5></div></div></div><p>
The two lines starting with the keywords <code class="literal">passwd:</code> and
<code class="literal">group:</code> define where Cygwin gets its passwd and group
information from.  <code class="literal">files</code> means, fetch the information
from the corresponding file in the /etc directory.  <code class="literal">db</code>
means, fetch the information from the Windows account databases, the SAM
for local accounts, Active Directory for domain account.  Examples:
</p><pre class="screen">
  passwd: files
</pre><p>
Read passwd entries only from /etc/passwd.
</p><pre class="screen">
  group: db
</pre><p>
Read group entries only from SAM/AD.
</p><pre class="screen">
  group: files # db
</pre><p>
Read group entries only from <code class="filename">/etc/group</code>
(<code class="literal">db</code> is only a comment).
</p><pre class="screen">
  passwd: files db
</pre><p>
Read passwd entries from <code class="filename">/etc/passwd</code>.  If a user account
isn't found, try to find it in SAM or AD.  This is the default for both,
passwd and group information.
</p><pre class="screen">
  group: db files
</pre><p>
This is a valid entry, but the order will be ignored by Cygwin.  If both
settings, <code class="literal">files</code> and <code class="literal">db</code> are specified,
Cygwin will always try the files first, then the db.
</p><p>
<code class="literal">passwd:</code> and <code class="literal">group:</code> are the two basic
settings defining where to get the account information from.  The following
settings starting with <code class="literal">db_</code> define certain aspects of the
Windows account database search and how to generate <code class="literal">passwd</code>
and <code class="literal">group</code> information from the database.
</p></div><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-enum"></a>The <code class="literal">db_enum:</code> setting</h5></div></div></div><p>
<code class="literal">db_enum:</code> defines the depth of a database search, if an
application calls one of the enumeration functions
<a class="ulink" href="http://linux.die.net/man/3/getpwent" target="_top">getpwent</a>
or <a class="ulink" href="http://linux.die.net/man/3/getgrent" target="_top">getgrent</a>.
The problem with these functions is, they neither allow to define how many
entries will be enumerated when calling them in a loop, nor do they
allow to add some filter criteria.  They were designed back in the days,
when only <code class="filename">/etc/passwd</code> and <code class="filename">/etc/group</code>
files existed and the number of user accounts on a typical UNIX system was
seldomly a three-digit number.
</p><p>
These days, with user and group databases sometimes going in the
six-digit range, they are a potential burden.  For that reason, Cygwin
does not enumerate all user or group accounts by default, but rather
just a very small list, consisting only of the accounts cached in memory
by the current process, as well as a handful of predefined builtin
accounts.
</p><p>
<code class="literal">db_enum:</code> allows to specify the accounts to enumerate in a
fine-grained manner.  It takes a list of sources as argument:
</p><pre class="screen">
  db_enum:  source1 source2 ...
</pre><p>
The recognized sources are the following:
</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">none</code></span></dt><dd>No output from
         <code class="function">getpwent</code>/<code class="function">getgrent</code>
	 at all.</dd><dt><span class="term"><code class="literal">all</code></span></dt><dd>The opposite.  Enumerates accounts from all known sources,
	      including all trusted domains.</dd><dt><span class="term"><code class="literal">cache</code></span></dt><dd>Enumerate all accounts currently cached in memory.</dd><dt><span class="term"><code class="literal">builtin</code></span></dt><dd>Enumerate the predefined builtin accounts for backward
	      compatibility.  These are five passwd accounts (SYSTEM,
	      LocalService, NetworkService, Administrators, TrustedInstaller)
	      and two group accounts (SYSTEM and TrustedInstaller).</dd><dt><span class="term"><code class="literal">files</code></span></dt><dd>Enumerate the accounts from <code class="filename">/etc/passwd</code> or
	      <code class="filename">/etc/group</code>.</dd><dt><span class="term"><code class="literal">local</code></span></dt><dd>Enumerate all accounts from the local SAM.</dd><dt><span class="term"><code class="literal">primary</code></span></dt><dd>Enumerate all accounts from the primary domain.</dd><dt><span class="term"><code class="literal">alltrusted</code></span></dt><dd>Enumerate all accounts from all trusted domains.</dd><dt><span class="term"><code class="literal">some.domain</code></span></dt><dd>Enumerate all accounts from the trusted domain some.domain.  The
	      trusted domain can be given as Netbios flat name (MY_DOMAIN) or
	      as dns domain name (my_domain.corp).  In contrast to the
	      aforementioned fixed source keywords, distinct domain names are
	      caseinsensitive.  Only domains which are actually trusted domains
	      are enumerated.  Unknown domains are simply ignored.</dd></dl></div><p>
Please note that <code class="function">getpwent</code>/<code class="function">getgrent</code>
do <span class="emphasis"><em>not</em></span> test if an account was already listed from another
source, so an account can easily show up twice or three times.  Such a test
would be rather tricky, nor does the Linux implementation perform such test.
Here are a few examples for <code class="filename">/etc/nsswitch.conf</code>:
</p><pre class="screen">
  db_enum: none
</pre><p>
No output from <code class="function">getpwent</code>/<code class="function">getgrent</code>
at all.  The first call to the function immediately returns a NULL pointer.
</p><pre class="screen">
  db_enum: cache files
</pre><p>
Enumerate all accounts cached by the current process, plus all entries
from either the /etc/passwd or /etc/group file.
</p><pre class="screen">
  db_enum: cache local primary
</pre><p>
Enumerate all accounts cached by the current process, all accounts from the SAM
of the local machine, and all accounts from the primary domain of the machine.
</p><pre class="screen">
  db_enum: local primary alltrusted
</pre><p>
Enumerate the accounts from the machine's SAM, from the primary domain of the
machine, and from all trusted domains.
</p><pre class="screen">
  db_enum: primary domain1.corp sub.domain.corp domain2.net
</pre><p>
Enumerate the accounts from the primary domain and from the domains
domain1.corp, sub.domain.corp and domain2.net.
</p><pre class="screen">
  db_enum: all
</pre><p>
Enumerate everything and the kitchen sink.
</p></div><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-passwd"></a>Settings defining how to create the <code class="literal">passwd</code> entry</h5></div></div></div><p>
<code class="filename">/etc/nsswitch.conf</code> supports three settings to configure
where to get the pw_dir, pw_shell, and pw_gecos content of a
<code class="literal">passwd</code> entry from:
</p><pre class="screen">
  db_home: schema...     # Define how to fetch the pw_dir entry.
  db_shell: schema...    # Define how to fetch the pw_shell entry.
  db_gecos: schema...    # Define how to fetch the pw_gecos entry.
</pre>

"schema..." is a list of up to four space-separated schemata:

<pre class="screen">
  db_FOO: schema1 schema2 ...
</pre><p>
When generating a passwd entry, Cygwin tries the schemata in order.  If
the first schema returns an empty string, it skips to the second, and so
on.  Schemata only supported on AD are silently skipped for SAM accounts
and on non-AD machines.
</p><p>
Five schemata are predefined, two schemata are variable.  The predefined
schemata are the following:
</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">windows</code></span></dt><dd>Utilizes typical Windows settings.  Supported for AD and SAM
	      accounts.</dd><dt><span class="term"><code class="literal">cygwin</code></span></dt><dd>Utilizes the cygwinUser AD schema extension.  This schema
	      extension is available via a schema extension file
	      <code class="filename">/usr/share/cygwin/cygwin.ldif</code>.
	      See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-cygwin" title="The cygwin schema">the section called &#8220;The <code class="literal">cygwin</code> schema&#8221;</a> for
	      more information.</dd><dt><span class="term"><code class="literal">unix</code></span></dt><dd>Utilizes the posixAccount schema attributes per
	      <a class="ulink" href="https://tools.ietf.org/html/rfc2307" target="_top">RFC 2307</a>.
	      The posixAccount schema is available by default in Windows
	      Server, but typically only utilized when installing the
	      Active Directory "Server for NIS" feature (which is deprecated
	      since Server 2012 R2).
	      See also <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-posix" title="The unix schema">the section called &#8220;The <code class="literal">unix</code> schema&#8221;</a>.
	      </dd><dt><span class="term"><code class="literal">desc</code></span></dt><dd>Utilizes XML-style attributes in the description attribute.
	      Supported for AD and SAM accounts.
	      See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-desc" title="The desc schema">the section called &#8220;The <code class="literal">desc</code> schema&#8221;</a>
	      for a more detailed description.</dd><dt><span class="term"><code class="literal">env</code></span></dt><dd>Utilizes the user's environment.  This schema is only supported
	      for setting the home directory yet.
	      See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-home" title="The db_home setting">the section called &#8220;The <code class="literal">db_home</code> setting&#8221;</a> for
	      the description.</dd></dl></div><p>
The variable schemata are as follows.  Note that the leading characters
(<code class="literal">@</code> and <code class="literal">/</code>) are an integral part of the
definition.
</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">@ad_attribute</code></span></dt><dd><code class="literal">ad_attribute</code> is any arbitrary AD attribute
	      name which should (ideally) be available in the User class or
	      in any attached auxiliary class.  It's always treated as a
	      single string argument.  Only the first string of a multi-string
	      attributes will be read.</dd><dt><span class="term"><code class="literal">/path</code></span></dt><dd>An arbitrary string, typically a path.  The leading slash is
	      required.  Given that a single, fixed path for all users
	      only makes marginal sense, the /path schema supports wildcards.
	      A wildcard is a per-cent (<code class="literal">%</code>) character,
	      followed by another character giving the meaning.  The supported
	      wildcard characters are:
	      
	      <div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">%u</code></span></dt><dd>The Cygwin username (that's lowercase
			    <code class="literal">u</code>).</dd><dt><span class="term"><code class="literal">%U</code></span></dt><dd>The Windows username (that's uppercase
			    <code class="literal">U</code>).</dd><dt><span class="term"><code class="literal">%D</code></span></dt><dd>Windows domain in NetBIOS style.</dd><dt><span class="term"><code class="literal">%H</code></span></dt><dd>Windows home directory in POSIX style.  Note that,
			    for the <code class="literal">db_home:</code> setting, this
			    only makes sense right after the preceeding slash,
			    as in
<pre class="screen">
db_home:  /%H/cygwin
</pre></dd><dt><span class="term"><code class="literal">%_</code></span></dt><dd>Since space and TAB characters are used to separate
			    the schemata, a space in the filename has to be
			    given as <code class="literal">%_</code> (that's an
			    underscore).</dd><dt><span class="term"><code class="literal">%%</code></span></dt><dd>A per-cent character.</dd></dl></div><p>Any other <code class="literal">%X</code> expression is treated as if
	      the character <code class="literal">X</code> has been given alone.</p></dd></dl></div><p>
The exact meaning of a schema depends on the setting it's used for.  The
following sections explain the settings in detail.
</p></div><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-home"></a>The <code class="literal">db_home</code> setting</h5></div></div></div><p>
The <code class="literal">db_home:</code> setting defines how Cygwin fetches the user's
home directory, or, more precise, the content of the <code class="literal">pw_dir</code>
member of the user's passwd entry.  The following list describes the meaning
of each schema when used with <code class="literal">db_home:</code>
</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">windows</code></span></dt><dd>The user's home directory is set to the same directory which is
	      used as Windows home directory.  This is the
<code class="literal">homeDirectory</code> AD attribute.
	      For SAM accounts, this is equivalent to the "Home folder" setting
	      in SAM.  If both attributes are unset, Cygwin falls back to the
	      user's local profile directory, typically something along the
	      lines of <code class="filename">C:\Users\<USER>\HOMEPATH</code> and
	      <code class="literal">USERPROFILE</code>, in that order).  This is faster
	      than the <code class="literal">windows</code> schema at the
	      expense of determining only the current user's home directory
	      correctly.  This schema is skipped for any other account.
	      </dd><dt><span class="term"><code class="literal">@ad_attribute</code></span></dt><dd>AD only: The user's home directory is set to the path given
	      in the <code class="literal">ad_attribute</code> attribute.  The path
	      can be given as Windows or POSIX path.</dd><dt><span class="term"><code class="literal">/path</code></span></dt><dd>The user's home directory is set to the given POSIX path.
	      Remember the wildcards described in
	      <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-passwd" title="Settings defining how to create the passwd entry">the section called &#8220;Settings defining how to create the <code class="literal">passwd</code> entry&#8221;</a>.</dd><dt><span class="term">Fallback</span></dt><dd>If none of the schemes given for <code class="literal">db_home:</code>
	      define a non-empty directory, the user's home directory is set to
	      <code class="filename">/home/<USER>/code>.</dd></dl></div><p>
<code class="literal">db_home:</code> defines no default schemata.  If this setting is not
present in <code class="filename">/etc/nsswitch.conf</code>, the aforementioned fallback
takes over, which is equivalent to a <code class="filename">/etc/nsswitch.conf</code>
settting of
</p><pre class="screen">
  db_home: /home/<USER>
</pre></div><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-shell"></a>The <code class="literal">db_shell</code> setting</h5></div></div></div><p>
The <code class="literal">db_shell:</code> setting defines how Cygwin fetches the user's
login shell, the content of the <code class="literal">pw_shell</code> member of the
user's passwd entry.  The following list describes the meaning of each schema
when used with <code class="literal">db_shell:</code>
</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">windows</code></span></dt><dd>The <code class="literal">windows</code> schema is ignored for now.
	      The logical choice would be CMD, but that introduces some
	      problems, for instance the fact that CMD has no concept of
	      running as <code class="literal">login shell</code>.  This may change
	      in future.</dd><dt><span class="term"><code class="literal">cygwin</code></span></dt><dd>AD only: The user's home directory is set to the POSIX path given
	      in the <code class="literal">cygwinShell</code> attribute from the
	      <code class="literal">cygwinUser</code> auxiliary class.
	      See also <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-cygwin" title="The cygwin schema">the section called &#8220;The <code class="literal">cygwin</code> schema&#8221;</a>.
	      </dd><dt><span class="term"><code class="literal">unix</code></span></dt><dd>AD only: The user's login shell is set to the POSIX path given
	      in the <code class="literal">loginShell</code> attribute from the
	      <code class="literal">posixAccount</code> auxiliary class.
	      See also <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-posix" title="The unix schema">the section called &#8220;The <code class="literal">unix</code> schema&#8221;</a>.
	      </dd><dt><span class="term"><code class="literal">desc</code></span></dt><dd>The user's login shell is set to the POSIX path given in the
	      shell="..." XML-alike setting in the user's
	      <code class="literal">description</code> attribute in SAM or AD.
	      See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-desc" title="The desc schema">the section called &#8220;The <code class="literal">desc</code> schema&#8221;</a>
	      for a detailed description.</dd><dt><span class="term"><code class="literal">@ad_attribute</code></span></dt><dd>AD only: The user's login shell is set to the path given
	      in the <code class="literal">ad_attribute</code> attribute.  The path
	      can be given as Windows or POSIX path.</dd><dt><span class="term"><code class="literal">/path</code></span></dt><dd>The user's login shell is set to the given POSIX path.
	      Albeit not being as important here, the wildcards described in
	      <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-passwd" title="Settings defining how to create the passwd entry">the section called &#8220;Settings defining how to create the <code class="literal">passwd</code> entry&#8221;</a>
	      are also available for specifying a login shell path.</dd><dt><span class="term">Fallback</span></dt><dd>If none of the schemes given for <code class="literal">db_shell:</code>
	      define a non-empty pathname, the user's login shell is set to
	      <code class="filename">/bin/bash</code>.</dd></dl></div><p>
<code class="literal">db_shell:</code> defines no default schemata.  If this setting is
not present in <code class="filename">/etc/nsswitch.conf</code>, the aforementioned
fallback takes over, which is equivalent to a
<code class="filename">/etc/nsswitch.conf</code> settting of
</p><pre class="screen">
  db_shell: /bin/bash
</pre></div><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-gecos"></a>The <code class="literal">db_gecos</code> setting</h5></div></div></div><p>
The <code class="literal">db_gecos:</code> setting defines how to fetch additional
content for the <code class="literal">pw_gecos</code> member of the user's passwd entry.
There's always a fixed, Cygwin-specific part in the <code class="literal">pw_gecos</code>
field for identifying the account.  However, an administrator might want to
add informative content like, for instance, the user's full name.  That's
what the <code class="literal">db_gecos:</code> setting is for.
The following list describes the meaning of each schema when used with
<code class="literal">db_gecos:</code>
</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">windows</code></span></dt><dd>Add the AD <code class="literal">displayName</code> attribute or, for
	      SAM accounts, the "Full name" entry to the
	      <code class="literal">pw_gecos</code> field.</dd><dt><span class="term"><code class="literal">cygwin</code></span></dt><dd>AD only: The content of the <code class="literal">cygwinGecos</code>
	      attribute from the <code class="literal">cygwinUser</code> auxiliary class
	      is added to <code class="literal">pw_gecos</code>.
	      See also <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-cygwin" title="The cygwin schema">the section called &#8220;The <code class="literal">cygwin</code> schema&#8221;</a>.
	      </dd><dt><span class="term"><code class="literal">unix</code></span></dt><dd>AD only: The content of the <code class="literal">gecos</code> attribute
	      from the <code class="literal">posixAccount</code> auxiliary class
	      is added to <code class="literal">pw_gecos</code>.
	      See also <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-posix" title="The unix schema">the section called &#8220;The <code class="literal">unix</code> schema&#8221;</a>.
	      </dd><dt><span class="term"><code class="literal">desc</code></span></dt><dd>The content of the gecos="..." XML-alike setting in the user's
	      <code class="literal">description</code> attribute in SAM or AD is added
	      to <code class="literal">pw_gecos</code>.
	      See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-desc" title="The desc schema">the section called &#8220;The <code class="literal">desc</code> schema&#8221;</a>
	      for a detailed description.</dd><dt><span class="term"><code class="literal">@ad_attribute</code></span></dt><dd>AD only: The content of the <code class="literal">ad_attribute</code>
	      attribute is added to <code class="literal">pw_gecos</code>.</dd><dt><span class="term"><code class="literal">/path</code></span></dt><dd>The string following the slash is added to
	      <code class="literal">pw_gecos</code>.  Here, the wildcards described in
	      <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-passwd" title="Settings defining how to create the passwd entry">the section called &#8220;Settings defining how to create the <code class="literal">passwd</code> entry&#8221;</a>
	      may come in handy.</dd><dt><span class="term">Fallback</span></dt><dd>If none of the schemes given for <code class="literal">db_gecos:</code>
	      define a non-empty string, nothing is added to
	      <code class="literal">pw_gecos</code>.</dd></dl></div><p>
<code class="literal">db_gecos:</code> defines no default schemata.
</p></div><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-cygwin"></a>The <code class="literal">cygwin</code> schema</h5></div></div></div><p>
The <code class="literal">cygwin</code> schema is based on a Cygwin-specific Active
Directory schema extension.  Using this schema extension allows to maintain
Cygwin-specific settings entirely within AD, without colliding with any other
schema.
</p><p>
The cygwin schema extension is available in a default Cygwin installation
in the file <code class="filename">/usr/share/cygwin/cygwin.ldif</code>.  To install
the schema extension, you have to be schema admin, and you have to run the
<span class="command"><strong>ldifde</strong></span> command on the schema master.  The installation
itself is rather simple.  Assuming you're schema admin and running a shell
with administrative privileges:
</p><pre class="screen">
  $ cd /usr/share/cygwin
  $ ldifde -i -f cygwin.ldif -k -c "CN=schema,CN=Configuration,DC=X" #schemaNamingContext
</pre><p>
Afterwards, the auxiliary class <code class="literal">cygwinUser</code> is attached to
the class <code class="literal">User</code>, and the auxiliary class
<code class="literal">cygwinGroup</code> is attached to the class
<code class="literal">Group</code>.  The new attributes can be immediately edited
using <span class="command"><strong>ADSI Edit</strong></span>.
</p><p>
At the time of writing the following attributes are utilized by Cygwin:
</p><div class="segmentedlist"><table border="0"><thead><tr class="segtitle"></tr></thead><tbody><tr class="seglistitem"><td class="seg"><code class="literal">cygwinHome</code></td><td class="seg">Used as Cygwin home directory with <code class="literal">db_home: cygwin</code>.
	 See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-home" title="The db_home setting">the section called &#8220;The <code class="literal">db_home</code> setting&#8221;</a>.</td></tr><tr class="seglistitem"><td class="seg"><code class="literal">cygwinShell</code></td><td class="seg">Used as Cygwin login shell with <code class="literal">db_shell: cygwin</code>.
	 See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-shell" title="The db_shell setting">the section called &#8220;The <code class="literal">db_shell</code> setting&#8221;</a>.</td></tr><tr class="seglistitem"><td class="seg"><code class="literal">cygwinGecos</code></td><td class="seg">Content will be added to the pw_gecos field with
	 <code class="literal">db_gecos: cygwin</code>.
	 See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-gecos" title="The db_gecos setting">the section called &#8220;The <code class="literal">db_gecos</code> setting&#8221;</a>.</td></tr></tbody></table></div></div><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-posix"></a>The <code class="literal">unix</code> schema</h5></div></div></div><p>
The <code class="literal">unix</code> schema utilizes the 
<code class="literal">posixAccount</code> attribute extension.  This is one of two
schema extensions which are connected to AD accounts, available by default.
They are usually <span class="bold"><strong>not set</strong></span>, unless used by
the Active Directory <code class="literal">Server for NIS</code> feature (deprecated
since Server 2012 R2).

Two schemata are interesting for Cygwin, <code class="literal">posixAccount</code>,
connected to user accounts, and <code class="literal">posixGroup</code>, connected
to group accounts.  Both follow the description of RFC 2307,
<a class="ulink" href="https://tools.ietf.org/html/rfc2307" target="_top">an Approach for Using LDAP as
a Network Information Service</a>.
The user attributes utilized by Cygwin are:
</p><div class="segmentedlist"><table border="0"><thead><tr class="segtitle"></tr></thead><tbody><tr class="seglistitem"><td class="seg"><code class="literal">unixHomeDirectory</code></td><td class="seg">Used as Cygwin home directory with <code class="literal">db_home: unix</code>.
	 See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-home" title="The db_home setting">the section called &#8220;The <code class="literal">db_home</code> setting&#8221;</a>.</td></tr><tr class="seglistitem"><td class="seg"><code class="literal">loginShell</code></td><td class="seg">Used as Cygwin login shell with <code class="literal">db_shell: unix</code>.
	 See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-shell" title="The db_shell setting">the section called &#8220;The <code class="literal">db_shell</code> setting&#8221;</a>.</td></tr><tr class="seglistitem"><td class="seg"><code class="literal">gecos</code></td><td class="seg">Content will be added to the pw_gecos field with
	 <code class="literal">db_gecos: unix</code>.
	 See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-gecos" title="The db_gecos setting">the section called &#8220;The <code class="literal">db_gecos</code> setting&#8221;</a>.</td></tr><tr class="seglistitem"><td class="seg"><code class="literal">uidNumber</code></td><td class="seg">See <a class="xref" href="ntsec.html#ntsec-mapping-nfs" title="NFS account mapping">the section called &#8220;NFS account mapping&#8221;</a> and
	 <a class="xref" href="ntsec.html#ntsec-mapping-samba" title="Samba account mapping">the section called &#8220;Samba account mapping&#8221;</a>.</td></tr></tbody></table></div><p>
The group attributes utilized by Cygwin are:
</p><div class="segmentedlist"><table border="0"><thead><tr class="segtitle"></tr></thead><tbody><tr class="seglistitem"><td class="seg"><code class="literal">gidNumber</code></td><td class="seg">See <a class="xref" href="ntsec.html#ntsec-mapping-nfs" title="NFS account mapping">the section called &#8220;NFS account mapping&#8221;</a> and
	 <a class="xref" href="ntsec.html#ntsec-mapping-samba" title="Samba account mapping">the section called &#8220;Samba account mapping&#8221;</a>.</td></tr></tbody></table></div><p>
Apart from power shell scripting or inventing new CLI tools, these attributes
can be changed using the <code class="literal">Attribute Editor</code> tab in the user
properties dialog of the <code class="literal">Active Directory Users and Computers</code>
MMC snap-in.  Alternatively, if the <code class="literal">Server for NIS</code>
administration feature has been installed, there will be a
<code class="literal">UNIX Attributes</code> tab which contains the required fields,
except for the gecos field.  Last resort is <span class="command"><strong>ADSI Edit</strong></span>.
</p></div><div class="sect4"><div class="titlepage"><div><div><h5 class="title"><a name="ntsec-mapping-nsswitch-desc"></a>The <code class="literal">desc</code> schema</h5></div></div></div><p>
When using user accounts from the local account database, the SAM, there
are only a very limited number of settings available.  In contrast to
Active Directory there's no way to add fields to a user's entry.  You have
to make do with the fields available.  The method to utilize the
<code class="literal">description</code> field has been mainly introduced for those
accounts, usually the only ones a home user has.  However, for symmetry,
and because there may be a reason to use this in an AD environment, this
schema is also supported for AD users.
</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>
The presentation of local user account settings on Windows is confusing,
to say the least.  The <code class="literal">description</code> field is not visible at
all in the user settings available via the <code class="literal">User Accounts</code>
control settings.  And while it's called <code class="literal">Description</code> in the
<code class="literal">Local Users and Groups</code> MMC snap-in (available, for instance,
via the <code class="literal">Computer Management</code> GUI), in the command
line tool <span class="command"><strong>net user</strong></span> the same field is called
<code class="literal">comment</code>.  The latter is especially confusing for
AD admins, because the <code class="literal">comment</code> attribute in AD is called
<code class="literal">usercomment</code> on the command line.  Confused?  Never mind,
you're not the only one...
</p></div><p>
Fortunately you can utilize the <code class="literal">description</code> field even if
you're running a "home edition" of Windows, by using the command line.  The
<span class="command"><strong>net user</strong></span> command allows to set all values in the SAM, even
if the GUI is crippled.
</p><p>
A Cygwin SAM comment entry looks like this:
</p><pre class="screen">
&lt;cygwin key="value" key="value" [...] /&gt;
</pre><p>
The supported keys are:
</p><div class="segmentedlist"><table border="0"><thead><tr class="segtitle"></tr></thead><tbody><tr class="seglistitem"><td class="seg"><code class="literal">home="value"</code></td><td class="seg">Sets the Cygwin home dir to value.</td></tr><tr class="seglistitem"><td class="seg"><code class="literal">shell="value"</code></td><td class="seg">Sets the Cygwin login shell to value.</td></tr><tr class="seglistitem"><td class="seg"><code class="literal">gecos="value"</code></td><td class="seg">Adds the string value to the user's gecos field.</td></tr></tbody></table></div><p>
The next two settings are only supported for SAM accounts.
</p><div class="segmentedlist"><table border="0"><thead><tr class="segtitle"></tr></thead><tbody><tr class="seglistitem"><td class="seg"><code class="literal">group="value"</code></td><td class="seg">Sets the Cygwin primary group of the account to value, provided that
    	 the user <span class="emphasis"><em>is</em></span> already a member of that group.
	 This allows to override the default <code class="literal">None</code> primary
	 group for local accounts.  One nice idea here is, for instance,
	 group="Users".</td></tr><tr class="seglistitem"><td class="seg"><code class="literal">unix="value"</code></td><td class="seg">Sets the NFS/Samba uid of the user to the decimal value.
         See <a class="xref" href="ntsec.html#ntsec-mapping-nfs" title="NFS account mapping">the section called &#8220;NFS account mapping&#8221;</a> and
	 <a class="xref" href="ntsec.html#ntsec-mapping-samba" title="Samba account mapping">the section called &#8220;Samba account mapping&#8221;</a>.</td></tr></tbody></table></div><p>
The &lt;cygwin .../&gt; string can start at any point in the comment, but
you have to follow the rules:
</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem">
It starts with "&lt;cygwin " and ends with "/&gt;".
</li><li class="listitem">
The "cygwin" string and the key names have to be lowercase.
</li><li class="listitem">
No spaces between key and "value", just the equal sign.
</li><li class="listitem">
The value must be placed within double quotes and it must not contain a double
quote itself.  The double quotes are required for the decimal values as well!
</li></ul></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>
There's also a length restriction imposed by Windows.  The
<code class="literal">description</code> entry has a maximum length of 1023 characters.
</p></div><p>
CMD example:
</p><pre class="screen">
net user corinna /comment:"&lt;cygwin home=\"/home/<USER>"/&gt;"
</pre><p>
Bash example (use single quotes):
</p><pre class="screen">
net user corinna /comment:'&lt;cygwin home="/home/<USER>"/&gt;'
</pre><p>
For changing group comments, use the `net localgroup' command.  The supported
key/value pair for SAM groups are:
</p><div class="segmentedlist"><table border="0"><thead><tr class="segtitle"></tr></thead><tbody><tr class="seglistitem"><td class="seg"><code class="literal">unix="value"</code></td><td class="seg">Sets the NFS/Samba gid of the group to the decimal value.
         See <a class="xref" href="ntsec.html#ntsec-mapping-nfs" title="NFS account mapping">the section called &#8220;NFS account mapping&#8221;</a> and
	 <a class="xref" href="ntsec.html#ntsec-mapping-samba" title="Samba account mapping">the section called &#8220;Samba account mapping&#8221;</a>.</td></tr></tbody></table></div></div></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-mapping-nfs"></a>NFS account mapping</h4></div></div></div><p>
Microsoft's NFS client does not map the uid/gid values on the NFS shares
to SIDs.  There's no such thing as a (fake) security descriptor returned
to the application.  Rather, via an undocumented API an application can
fetch <a class="ulink" href="https://tools.ietf.org/html/rfc1813" target="_top">RFC 1813</a>
compatible NFSv3 stat information from the share.  This is what Cygwin is
using to show stat information for files on NFS shares.
</p><p>
The problem is, while all other information in this stat record, like
timestamps, file size etc., can be used by Cygwin, Cygwin had no way to
map the values of the st_uid and st_gid members to a Windows SID for a
long time.  So it just faked the file owner info and claimed that it's
you.
</p><p>
However, SFU has, over time, developed multiple methods to map UNIX
uid/gid values on NFS shares to Windows SIDs.  You'll find the full
documentation of the mapping methods in
<a class="ulink" href="http://blogs.technet.com/b/filecab/archive/2012/10/09/nfs-identity-mapping-in-windows-server-2012.aspx" target="_top">NFS Identity Mapping in Windows Server 2012</a>
</p><p>
Cygwin now utilizes the
<a class="ulink" href="https://tools.ietf.org/html/rfc2307" target="_top">RFC 2307</a>
mapping for this purpose.  This is most of the time provided by an AD domain,
but it could also be a standalone LDAP mapping server.  Per
<a class="ulink" href="https://tools.ietf.org/html/rfc2307" target="_top">RFC 2307</a>, the uid is
in the attribute <code class="literal">uidNumber</code>.  For groups, the gid is in the
<code class="literal">gidNumber</code> attribute.
See <a class="xref" href="ntsec.html#ntsec-mapping-nsswitch-posix" title="The unix schema">the section called &#8220;The <code class="literal">unix</code> schema&#8221;</a>.
</p><p>
When Cygwin stat()s files on an NFS share, it asks the mapping server via
LDAP in two different ways, depending on the role of the mapping server.
</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem">
If the server is an AD domain controller, it asks for an account with
<code class="literal">uidNumber</code> attribute == <code class="literal">st_uid</code> field of
the stat record returned by NFS.  If an account matches, AD returns the
Windows SID, so we have an immediate mapping from UNIX uid to a Windows SID,
if the user account has a valid <code class="literal">uidNumber</code> attribute.  For
groups, the method is the same, just that Cygwin asks for a group with
<code class="literal">gidNumber</code> attribute == <code class="literal">st_gid</code> field of the
stat record.
</li><li class="listitem">
If the server is a standalone LDAP mapping server Cygwin asks for the
same <code class="literal">uidNumber</code>/<code class="literal">gidNumber</code> attributes, but
it can't expect that the LDAP server knows anything about Windows SIDs.
Rather, the mapping server returns the account name.  Cygwin then asks the
DC for an account with this name, and if that succeeds, we have a mapping
between UNIX uid/gid and Windows SIDs.
</li></ul></div><p>
The mapping will be cached for the lifetime of the process, and inherited
by child processes.
</p></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-mapping-samba"></a>Samba account mapping</h4></div></div></div><p>
A fully set up Samba file server with domain integration is running winbindd to
map Windows SIDs to artificially created UNIX uids and gids, and this mapping is
transparent within the domain, so Cygwin doesn't have to do anything special.
</p><p>
However, setting up winbindd isn't for everybody, and it fails to map
Windows accounts to already existing UNIX users or groups.  In contrast
to NFS, Samba returns security descriptors, but unmapped UNIX accounts
get special SIDs:
</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem">
A UNIX user account with uid X is mapped to the Windows SID S-1-22-1-X.
</li><li class="listitem">
A UNIX group account with gid X is mapped to SID S-1-22-2-X.
</li></ul></div><p>
As you can see, even though we have SIDs, they just reflect the actual
uid/gid values on the UNIX box in the RID value.  It's only marginally
different from the NFS method, so why not just use the same method as
for NFS?
</p><p>
That's what Cygwin will do.  If it encounters a S-1-22-x-y SID, it
will perform the same
<a class="ulink" href="https://tools.ietf.org/html/rfc2307" target="_top">RFC 2307</a>
mapping as for NFS shares.
</p><p>
For home users without any Windows domain or LDAP server per
<a class="ulink" href="https://tools.ietf.org/html/rfc2307" target="_top">RFC 2307</a>,
but with a Linux machine running Samba, just add this information to
your SAM account.  Assuming the uid of your Linux user account is 505
and the gid of your primary group is, say, 100, just add the values to
your SAM user and group accounts.  The following example assumes you
didn't already add something else to the comment field.
</p><p>
To your user's SAM comment (remember: called <code class="literal">Description</code>
in the GUI),
add:
</p><pre class="screen">
  &lt;cygwin group="Users" unix="505"/&gt;
</pre><p>
To the <code class="literal">Users</code> group SAM comment add:
</p><pre class="screen">
  &lt;cygwin unix="100"/&gt;
</pre><p>
This should be sufficient to work on your Samba share and to see
all files owned by your Linux user account as your files.
</p></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ntsec-files"></a>File permissions</h3></div></div></div><p>On NTFS and if the <code class="literal">noacl</code> mount option is not
specified for a mount point, Cygwin sets file permissions as on POSIX
systems.  Basically this is done by defining a Security Descriptor with the
matching owner and group SIDs, and a DACL which contains ACEs for the owner,
the group and for "Everyone", which represents what POSIX calls "others".</p><p>There's just one problem when trying to map the POSIX permission model
onto the Windows permission model.</p><p>There's a leak in the definition of a "correct" ACL which disallows a
certain POSIX permission setting.  The official documentation explains in short
the following:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>The requested permissions are checked against all
ACEs of the user as well as all groups the user is member of.  The
permissions given in these user and groups access allowed ACEs are
accumulated and the resulting set is the set of permissions of that
user given for that object.</p></li><li class="listitem"><p>The order of ACEs is important. The system reads them in
sequence until either any single requested permission is denied or all
requested permissions are granted.  Reading stops when this condition is
met.  Later ACEs are not taken into account.</p></li><li class="listitem"><p>All access denied ACEs <span class="bold"><strong>should</strong></span> precede any access allowed ACE.  ACLs
following this rule are called "canonical".</p></li></ul></div><p>Note that the last rule is a preference or a definition of
correctness.  It's not an absolute requirement.  All Windows kernels
will correctly deal with the ACL regardless of the order of allow and
deny ACEs.  The second rule is not modified to get the ACEs in the
preferred order.</p><p>Unfortunately, the security tab in the file properties dialog of
the Windows Explorer will pop up a warning stating "The permissions on
... are incorrectly ordered, which may cause some entries to be
ineffective."  Pressing the Cancel button of the properties dialog
fortunately leaves the sort order unchanged, but pressing OK will cause
Explorer to canonicalize the order of the ACEs, thereby invalidating
POSIX compatibility.</p><p>Canonical ACLs are unable to reflect each possible combination
of POSIX permissions. Example:</p><pre class="screen">
rw-r-xrw-
</pre><p>Ok, so here's the first try to create a matching ACL, assuming
the Windows permissions only have three bits, as their POSIX counterpart:
</p><pre class="screen">
UserAllow:   110
GroupAllow:  101
OthersAllow: 110
</pre><p>Hmm, because of the accumulation of allow rights the user may
execute because the group may execute.</p><p>Second try:</p><pre class="screen">
UserDeny:    001
GroupAllow:  101
OthersAllow: 110
</pre><p>Now the user may read and write but not execute. Better? No!
Unfortunately the group may write now because others may write.</p><p>Third try:</p><pre class="screen">
UserDeny:    001
GroupDeny:   010
GroupAllow:  001
OthersAllow: 110
</pre><p>Now the group may not write as intended but unfortunately the user may
not write anymore, either. How should this problem be solved? According to
the canonical order a UserAllow has to follow the GroupDeny but it's
easy to see that this can never be solved that way.</p><p>The only chance:</p><pre class="screen">
UserDeny:    001
UserAllow:   010
GroupDeny:   010
GroupAllow:  001
OthersAllow: 110
</pre><p>Again: This works on all supported versions of Windows.  Only the GUIs
aren't able (or willing) to deal with that order.</p></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ntsec-setuid-overview"></a>Switching the user context</h3></div></div></div><p>Windows users have been accustomed to the "Switch User" feature, which
switches the entire desktop to another user while leaving the original user's
desktop "suspended".  Another Windows feature is the "Run as..." context menu
entry, which allows you to start an application using another user account
when right-clicking on applications and shortcuts.</p><p>On POSIX systems, this operation can be performed by processes
running under the privileged user accounts (usually the "root" user
account) on a per-process basis.  This is called "switching the user
context" for that process, and is performed using the POSIX
<span class="command"><strong>setuid</strong></span> and <span class="command"><strong>seteuid</strong></span> system
calls.</p><p>While this sort of feature is available on Windows as well,
Windows does not support the concept of these calls in a simple fashion.
Switching the user context in Windows is generally a tricky process with
lots of "behind the scenes" magic involved.</p><p>Windows uses so-called `access tokens' to identify a user and its
permissions.  Usually the access token is created at logon time and then
it's attached to the starting process.  Every new process within a session
inherits the access token from its parent process.  Every thread can
get its own access token, which allows, for instance, to define threads
with restricted permissions.</p><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-logonuser"></a>Switching the user context with password authentication</h4></div></div></div><p>To switch the user context, the process has to request such an access
token for the new user.  This is typically done by calling the Win32 API
function <span class="command"><strong>LogonUser</strong></span> with the user name and the user's
cleartext password as arguments.  If the user exists and the password was
specified correctly, the access token is returned and either used in
<span class="command"><strong>ImpersonateLoggedOnUser</strong></span> to change the user context of
the current thread, or in <span class="command"><strong>CreateProcessAsUser</strong></span> to
change the user context of a spawned child process.</p><p>Later versions of Windows define new functions in this context and
there are also functions to manipulate existing access tokens (usually
only to restrict them).  Windows Vista also adds subtokens which are
attached to other access tokens which plays an important role in the UAC
(User Access Control) facility of Vista and later.  However, none of
these extensions to the original concept are important for this
documentation.</p><p>Back to this logon with password, how can this be used to
implement <span class="command"><strong>set(e)uid</strong></span>?  Well, it requires modification
of the calling application.  Two Cygwin functions have been introduced
to support porting <span class="command"><strong>setuid</strong></span> applications which only
require login with passwords.  You only give Cygwin the right access
token and then you can call <span class="command"><strong>seteuid</strong></span> or
<span class="command"><strong>setuid</strong></span> as usual in POSIX applications.  Porting such
a <span class="command"><strong>setuid</strong></span> application is illustrated by a short
example:</p><pre class="screen">

/* First include all needed cygwin stuff. */
#ifdef __CYGWIN__
#include &lt;windows.h&gt;
#include &lt;sys/cygwin.h&gt;
#endif

[...]

  struct passwd *user_pwd_entry = getpwnam (username);
  char *cleartext_password = getpass ("Password:");

[...]

#ifdef __CYGWIN__
  /* Patch the typical password test. */
  {
    HANDLE token;

    /* Try to get the access token from Windows. */
    token = cygwin_logon_user (user_pwd_entry, cleartext_password);
    if (token == INVALID_HANDLE_VALUE)
       error_exit;
    /* Inform Cygwin about the new impersonation token. */
    cygwin_set_impersonation_token (token);
    /* Cygwin is now able, to switch to that user context by setuid or seteuid calls. */
  }
#else
    /* Use standard method on non-Cygwin systems. */
    hashed_password = crypt (cleartext_password, salt);
    if (!user_pwd_entry ||
        strcmp (hashed_password, user_pwd_entry-&gt;pw_passwd))
      error_exit;
#endif /* CYGWIN */

[...]

  /* Everything else remains the same! */

  setegid (user_pwd_entry-&gt;pw_gid);
  seteuid (user_pwd_entry-&gt;pw_uid);
  execl ("/bin/sh", ...);


</pre></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-nopasswd1"></a>Switching the user context without password, Method 1: Kerberos/MsV1_0 S4U authentication</h4></div></div></div><p>An unfortunate aspect of the implementation of
<span class="command"><strong>set(e)uid</strong></span> is the fact that the calling process
requires the password of the user to switch to.  Applications such as
<span class="command"><strong>sshd</strong></span> wishing to switch the user context after a
successful public key authentication, or the <span class="command"><strong>cron</strong></span>
application which, again, wants to switch the user without any authentication
are stuck here.  But there are other ways to get new user tokens.</p><p>Starting with Cygwin 3.0, Cygwin tries to create a token by using
<code class="literal">Windows S4U authentication</code> by default.  For a quick
description, see
<a class="ulink" href="https://blogs.msdn.microsoft.com/winsdk/2015/08/28/logon-as-a-user-without-a-password/" target="_top">this blog posting</a>.  Cygwin versions prior
to 3.0 tried to creat a user token from scratch using an officially
undocumented function <span class="command"><strong>NtCreateToken</strong></span> which
is now disabled.</p><p>So we just start the services which have to switch the user context
(<span class="command"><strong>cygsshd</strong></span>, <span class="command"><strong>inetd</strong></span>, <span class="command"><strong>cron</strong></span>,
...) as Windows services under the SYSTEM (or LocalSystem in the GUI)
account and everything just works.  Unfortunately that's too simple.
Using <code class="literal">S4U</code> has a drawback.</p><p>Annoyingly, you don't have the usual comfortable access
to network shares.  The reason is that the token has been created
without knowing the password.  The password are your credentials
necessary for network access.  Thus, if you logon with a password, the
password is stored hidden as "token credentials" within the access token
and used as default logon to access network resources.  Since these
credentials are missing from the token created with <code class="literal">S4U</code>
or <span class="command"><strong>NtCreateToken</strong></span>, you only can access network shares
from the new user's process tree by using explicit authentication, on
the command line for instance:</p><pre class="screen">
bash$ net use '\\server\share' /user:DOMAIN\my_user my_users_password
</pre><p>Note that, on some systems, you can't even define a drive letter
to access the share, and under some circumstances the drive letter you
choose collides with a drive letter already used in another session.
Therefore it's better to get used to accessing these shares using the UNC
path as in</p><pre class="screen">
bash$ grep foo //server/share/foofile
</pre></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-nopasswd3"></a>Switching the user context without password, Method 2: With password</h4></div></div></div><p>Not being able to access network shares without having to specify
a cleartext password on the command line or in a script is a harsh problem
for automated logons for testing purposes and similar stuff.</p><p>Fortunately there is a solution, but it has its own drawbacks.
But, first things first, how does it work?  The title of this section
says it all.  Instead of trying to logon without password, we just logon
with password.  The password gets stored two-way encrypted in a hidden,
obfuscated area of the registry, the LSA private registry area.  This
part of the registry contains, for instance, the passwords of the Windows
services which run under some non-default user account.</p><p>So what we do is to utilize this registry area for the purpose of
<span class="command"><strong>set(e)uid</strong></span>.  The Cygwin command <span class="command"><strong><a class="link" href="passwd.html" title="passwd">passwd</a> -R</strong></span> allows a user to specify
his/her password for storage in this registry area.  When this user
tries to login using ssh with public key authentication, Cygwin's
<span class="command"><strong>set(e)uid</strong></span> examines the LSA private registry area and
searches for a Cygwin specific key which contains the password.  If it
finds it, it calls <span class="command"><strong>LogonUser</strong></span> under the hood, using
this password.  If that works, <span class="command"><strong>LogonUser</strong></span> returns an
access token with all credentials necessary for network access.</p><p>For good measure, and since this way to implement
<span class="command"><strong>set(e)uid</strong></span> is not only used by Cygwin but also by
Microsoft's SFU (Services for Unix), we also look for a key stored by
SFU (using the SFU command <span class="command"><strong>regpwd</strong></span>) and use that if it's
available.</p><p>We got it.  A full access token with its own logon session, with
all network credentials.  Hmm, that's heaven...</p><p>Back on earth, what about the drawbacks?</p><p>First, adding a password to the LSA private registry area
requires administrative access.  So calling <span class="command"><strong>passwd -R</strong></span>
as a normal user will fail!  Cygwin provides a workaround for
this.  If <span class="command"><strong>cygserver</strong></span> is started as a service running
under the SYSTEM account (which is the default way to run
<span class="command"><strong>cygserver</strong></span>) you can use <span class="command"><strong>passwd -R</strong></span>
as normal, non-privileged user as well.</p><p>Second, as aforementioned, the password is two-way encrypted in a
hidden, obfuscated registry area.  Only SYSTEM has access to this area
for listing purposes, so, even as an administrator, you can't examine
this area with regedit.  Right?  No.  Every administrator can start
regedit as SYSTEM user, the Internet is your friend here.</p><p>Additionally, if an administrator knows under which name
the private key is stored (which is well-known since the algorithms
used to create the Cygwin and SFU keys are no secret), every administrator
can access the password of all keys stored this way in the registry.</p><p>Conclusion: If your system is used exclusively by you, and if
you're also the only administrator of your system, and if your system is
adequately locked down to prevent malicious access, you can safely use
this method.  If your machine is part of a network which has
dedicated administrators, and you're not one of these administrators,
but you (think you) can trust your administrators, you can probably
safely use this method.</p><p>In all other cases, don't use this method.  You have been warned.</p></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ntsec-setuid-impl"></a>Switching the user context, how does it all fit together?</h4></div></div></div><p>Now we learned about three different ways to switch the user
context using the <span class="command"><strong>set(e)uid</strong></span> system call, but
how does <span class="command"><strong>set(e)uid</strong></span> really work?  Which method does it
use now?</p><p>The answer is, all three of them.  So here's a brief overview
what <span class="command"><strong>set(e)uid</strong></span> does under the hood:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>When <span class="command"><strong>set(e)uid</strong></span> is called, it tests if the
user context had been switched by an earlier call already, and if the
new user account is the privileged user account under which the process
had been started originally.  If so, it just switches to the original
access token of the process it had been started with.</p></li><li class="listitem"><p>
Next, it tests if an access token has been stored by an earlier call
to <span class="command"><strong>cygwin_set_impersonation_token</strong></span>.  If so, it tests
if that token matches the requested user account.  If so, the stored
token is used for the user context switch.</p><p>
If not, there's no predefined token which can just be used for
the user context switch, so we have to create a new token.  The order
is as follows.</p></li><li class="listitem"><p>Check if the user has stored the logon password in the LSA
private registry area, either under a Cygwin key, or under a SFU key.
If so, use this to call <span class="command"><strong>LogonUser</strong></span>.  If this
succeeds, we use the resulting token for the user context switch.</p></li><li class="listitem"><p>Otherwise, use the default <code class="literal">S4U</code> authentication
to create a token.</p></li><li class="listitem"><p>Older systems, like WOW64 under Windows 7 64 bit, don't support
<code class="literal">S4U</code> authentication for local machine accounts.  On
these systems Cygwin falls back to an old and otherwise deprecated
method to create a user token from scratch.  The underlying system call
is undocumented and has an unfortunate requirement: We have to create a
special account with dangerous permissions to perform this action.
Therefore this is only enabled on affected systems.</p></li><li class="listitem"><p>If all of the above fails, our process has insufficient privileges
to switch the user context at all, so <span class="command"><strong>set(e)uid</strong></span>
fails and returns -1, setting errno to EPERM.</p></li></ul></div></div></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="using-cygwinenv.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="using-cygserver.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">The <code class="envar">CYGWIN</code> environment
variable&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Cygserver</td></tr></table></div></body></html>
