.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_get_crt_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_get_crt_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_get_crt_raw(gnutls_pkcs7_t " pkcs7 ", unsigned " indx ", void * " certificate ", size_t * " certificate_size ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_t pkcs7" 12
should contain a gnutls_pkcs7_t type
.IP "unsigned indx" 12
contains the index of the certificate to extract
.IP "void * certificate" 12
the contents of the certificate will be copied
there (may be null)
.IP "size_t * certificate_size" 12
should hold the size of the certificate
.SH "DESCRIPTION"
This function will return a certificate of the PKCS7 or RFC2630
certificate set.

After the last certificate has been read
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be returned.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.  If the provided buffer is not long enough,
then  \fIcertificate_size\fP is updated and
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
