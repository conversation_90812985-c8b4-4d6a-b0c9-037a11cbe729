# Automatically generated from regenerate_commands_perl_info.pl

package Texinfo::Commands;

our %block_commands = (
  "ifclear"                 => 'conditional',
  "ifcommanddefined"        => 'conditional',
  "ifcommandnotdefined"     => 'conditional',
  "ifdocbook"               => 'conditional',
  "ifhtml"                  => 'conditional',
  "ifinfo"                  => 'conditional',
  "iflatex"                 => 'conditional',
  "ifnotdocbook"            => 'conditional',
  "ifnothtml"               => 'conditional',
  "ifnotinfo"               => 'conditional',
  "ifnotlatex"              => 'conditional',
  "ifnotplaintext"          => 'conditional',
  "ifnottex"                => 'conditional',
  "ifnotxml"                => 'conditional',
  "ifplaintext"             => 'conditional',
  "ifset"                   => 'conditional',
  "iftex"                   => 'conditional',
  "ifxml"                   => 'conditional',
  "defblock"                => 'def',
  "defcv"                   => 'def',
  "deffn"                   => 'def',
  "defivar"                 => 'def',
  "defmac"                  => 'def',
  "defmethod"               => 'def',
  "defop"                   => 'def',
  "defopt"                  => 'def',
  "defspec"                 => 'def',
  "deftp"                   => 'def',
  "deftypecv"               => 'def',
  "deftypefn"               => 'def',
  "deftypefun"              => 'def',
  "deftypeivar"             => 'def',
  "deftypemethod"           => 'def',
  "deftypeop"               => 'def',
  "deftypevar"              => 'def',
  "deftypevr"               => 'def',
  "defun"                   => 'def',
  "defvar"                  => 'def',
  "defvr"                   => 'def',
  "float"                   => 'float',
  "docbook"                 => 'format_raw',
  "html"                    => 'format_raw',
  "latex"                   => 'format_raw',
  "tex"                     => 'format_raw',
  "xml"                     => 'format_raw',
  "enumerate"               => 'item_container',
  "itemize"                 => 'item_container',
  "ftable"                  => 'item_line',
  "table"                   => 'item_line',
  "vtable"                  => 'item_line',
  "displaymath"             => 'math',
  "detailmenu"              => 'menu',
  "direntry"                => 'menu',
  "menu"                    => 'menu',
  "multitable"              => 'multitable',
  "cartouche"               => 'other',
  "flushleft"               => 'other',
  "flushright"              => 'other',
  "group"                   => 'other',
  "indentedblock"           => 'other',
  "nodedescriptionblock"    => 'other',
  "raggedright"             => 'other',
  "smallindentedblock"      => 'other',
  "display"                 => 'preformatted',
  "example"                 => 'preformatted',
  "format"                  => 'preformatted',
  "lisp"                    => 'preformatted',
  "smalldisplay"            => 'preformatted',
  "smallexample"            => 'preformatted',
  "smallformat"             => 'preformatted',
  "smalllisp"               => 'preformatted',
  "quotation"               => 'quotation',
  "smallquotation"          => 'quotation',
  "ignore"                  => 'raw',
  "linemacro"               => 'raw',
  "macro"                   => 'raw',
  "rmacro"                  => 'raw',
  "verbatim"                => 'raw',
  "copying"                 => 'region',
  "documentdescription"     => 'region',
  "titlepage"               => 'region',
);

our %brace_commands = (
  "'"                       => 'accent',
  ","                       => 'accent',
  "="                       => 'accent',
  "H"                       => 'accent',
  "\""                      => 'accent',
  "^"                       => 'accent',
  "`"                       => 'accent',
  "dotaccent"               => 'accent',
  "dotless"                 => 'accent',
  "ogonek"                  => 'accent',
  "ringaccent"              => 'accent',
  "tieaccent"               => 'accent',
  "u"                       => 'accent',
  "ubaraccent"              => 'accent',
  "udotaccent"              => 'accent',
  "v"                       => 'accent',
  "~"                       => 'accent',
  "U"                       => 'arguments',
  "abbr"                    => 'arguments',
  "acronym"                 => 'arguments',
  "anchor"                  => 'arguments',
  "email"                   => 'arguments',
  "errormsg"                => 'arguments',
  "hyphenation"             => 'arguments',
  "image"                   => 'arguments',
  "inforef"                 => 'arguments',
  "link"                    => 'arguments',
  "pxref"                   => 'arguments',
  "ref"                     => 'arguments',
  "seealso"                 => 'arguments',
  "seeentry"                => 'arguments',
  "sortas"                  => 'arguments',
  "uref"                    => 'arguments',
  "url"                     => 'arguments',
  "xref"                    => 'arguments',
  "caption"                 => 'context',
  "footnote"                => 'context',
  "math"                    => 'context',
  "shortcaption"            => 'context',
  "inlinefmt"               => 'inline',
  "inlinefmtifelse"         => 'inline',
  "inlineifclear"           => 'inline',
  "inlineifset"             => 'inline',
  "inlineraw"               => 'inline',
  "AA"                      => 'noarg',
  "AE"                      => 'noarg',
  "DH"                      => 'noarg',
  "L"                       => 'noarg',
  "LaTeX"                   => 'noarg',
  "O"                       => 'noarg',
  "OE"                      => 'noarg',
  "TH"                      => 'noarg',
  "TeX"                     => 'noarg',
  "aa"                      => 'noarg',
  "ae"                      => 'noarg',
  "ampchar"                 => 'noarg',
  "arrow"                   => 'noarg',
  "atchar"                  => 'noarg',
  "backslashchar"           => 'noarg',
  "bullet"                  => 'noarg',
  "click"                   => 'noarg',
  "comma"                   => 'noarg',
  "copyright"               => 'noarg',
  "dh"                      => 'noarg',
  "dots"                    => 'noarg',
  "enddots"                 => 'noarg',
  "equiv"                   => 'noarg',
  "error"                   => 'noarg',
  "euro"                    => 'noarg',
  "exclamdown"              => 'noarg',
  "expansion"               => 'noarg',
  "geq"                     => 'noarg',
  "guillemetleft"           => 'noarg',
  "guillemetright"          => 'noarg',
  "guillemotleft"           => 'noarg',
  "guillemotright"          => 'noarg',
  "guilsinglleft"           => 'noarg',
  "guilsinglright"          => 'noarg',
  "hashchar"                => 'noarg',
  "l"                       => 'noarg',
  "lbracechar"              => 'noarg',
  "leq"                     => 'noarg',
  "minus"                   => 'noarg',
  "o"                       => 'noarg',
  "oe"                      => 'noarg',
  "ordf"                    => 'noarg',
  "ordm"                    => 'noarg',
  "point"                   => 'noarg',
  "pounds"                  => 'noarg',
  "print"                   => 'noarg',
  "questiondown"            => 'noarg',
  "quotedblbase"            => 'noarg',
  "quotedblleft"            => 'noarg',
  "quotedblright"           => 'noarg',
  "quoteleft"               => 'noarg',
  "quoteright"              => 'noarg',
  "quotesinglbase"          => 'noarg',
  "rbracechar"              => 'noarg',
  "registeredsymbol"        => 'noarg',
  "result"                  => 'noarg',
  "ss"                      => 'noarg',
  "textdegree"              => 'noarg',
  "th"                      => 'noarg',
  "tie"                     => 'noarg',
  "today"                   => 'noarg',
  "dmn"                     => 'other',
  "titlefont"               => 'other',
  "txiinternalvalue"        => 'special',
  "value"                   => 'special',
  "verb"                    => 'special',
  "w"                       => 'special',
  "code"                    => 'style_code',
  "command"                 => 'style_code',
  "env"                     => 'style_code',
  "file"                    => 'style_code',
  "indicateurl"             => 'style_code',
  "kbd"                     => 'style_code',
  "key"                     => 'style_code',
  "option"                  => 'style_code',
  "samp"                    => 'style_code',
  "t"                       => 'style_code',
  "r"                       => 'style_no_code',
  "asis"                    => 'style_other',
  "b"                       => 'style_other',
  "cite"                    => 'style_other',
  "clicksequence"           => 'style_other',
  "definfoenclose_command"  => 'style_other',
  "dfn"                     => 'style_other',
  "emph"                    => 'style_other',
  "headitemfont"            => 'style_other',
  "i"                       => 'style_other',
  "sansserif"               => 'style_other',
  "sc"                      => 'style_other',
  "slanted"                 => 'style_other',
  "strong"                  => 'style_other',
  "sub"                     => 'style_other',
  "sup"                     => 'style_other',
  "var"                     => 'style_other',
);

our %line_commands = (
  "appendix"                => 'line',
  "appendixsec"             => 'line',
  "appendixsection"         => 'line',
  "appendixsubsec"          => 'line',
  "appendixsubsubsec"       => 'line',
  "author"                  => 'line',
  "center"                  => 'line',
  "centerchap"              => 'line',
  "chapheading"             => 'line',
  "chapter"                 => 'line',
  "cindex"                  => 'line',
  "cpindex"                 => 'line',
  "defcvx"                  => 'line',
  "deffnx"                  => 'line',
  "defivarx"                => 'line',
  "defline"                 => 'line',
  "defmacx"                 => 'line',
  "defmethodx"              => 'line',
  "defoptx"                 => 'line',
  "defopx"                  => 'line',
  "defspecx"                => 'line',
  "deftpx"                  => 'line',
  "deftypecvx"              => 'line',
  "deftypefnx"              => 'line',
  "deftypefunx"             => 'line',
  "deftypeivarx"            => 'line',
  "deftypeline"             => 'line',
  "deftypemethodx"          => 'line',
  "deftypeopx"              => 'line',
  "deftypevarx"             => 'line',
  "deftypevrx"              => 'line',
  "defunx"                  => 'line',
  "defvarx"                 => 'line',
  "defvrx"                  => 'line',
  "dircategory"             => 'line',
  "evenfooting"             => 'line',
  "evenheading"             => 'line',
  "everyfooting"            => 'line',
  "everyheading"            => 'line',
  "exdent"                  => 'line',
  "findex"                  => 'line',
  "fnindex"                 => 'line',
  "heading"                 => 'line',
  "index_entry_command"     => 'line',
  "item_LINE"               => 'line',
  "itemx"                   => 'line',
  "kindex"                  => 'line',
  "kyindex"                 => 'line',
  "listoffloats"            => 'line',
  "majorheading"            => 'line',
  "node"                    => 'line',
  "nodedescription"         => 'line',
  "oddfooting"              => 'line',
  "oddheading"              => 'line',
  "pagesizes"               => 'line',
  "part"                    => 'line',
  "pgindex"                 => 'line',
  "pindex"                  => 'line',
  "section"                 => 'line',
  "settitle"                => 'line',
  "shorttitlepage"          => 'line',
  "subentry"                => 'line',
  "subheading"              => 'line',
  "subsection"              => 'line',
  "subsubheading"           => 'line',
  "subsubsection"           => 'line',
  "subtitle"                => 'line',
  "tindex"                  => 'line',
  "title"                   => 'line',
  "top"                     => 'line',
  "tpindex"                 => 'line',
  "unnumbered"              => 'line',
  "unnumberedsec"           => 'line',
  "unnumberedsubsec"        => 'line',
  "unnumberedsubsubsec"     => 'line',
  "vindex"                  => 'line',
  "vrindex"                 => 'line',
  "afivepaper"              => 'lineraw',
  "afourlatex"              => 'lineraw',
  "afourpaper"              => 'lineraw',
  "afourwide"               => 'lineraw',
  "bsixpaper"               => 'lineraw',
  "bye"                     => 'lineraw',
  "c"                       => 'lineraw',
  "clear"                   => 'lineraw',
  "clickstyle"              => 'lineraw',
  "comment"                 => 'lineraw',
  "contents"                => 'lineraw',
  "finalout"                => 'lineraw',
  "insertcopying"           => 'lineraw',
  "lowersections"           => 'lineraw',
  "novalidate"              => 'lineraw',
  "page"                    => 'lineraw',
  "raisesections"           => 'lineraw',
  "set"                     => 'lineraw',
  "shortcontents"           => 'lineraw',
  "smallbook"               => 'lineraw',
  "summarycontents"         => 'lineraw',
  "unmacro"                 => 'lineraw',
  "vskip"                   => 'lineraw',
  "alias"                   => 'specific',
  "allowcodebreaks"         => 'specific',
  "codequotebacktick"       => 'specific',
  "codequoteundirected"     => 'specific',
  "columnfractions"         => 'specific',
  "defcodeindex"            => 'specific',
  "defindex"                => 'specific',
  "definfoenclose"          => 'specific',
  "deftypefnnewline"        => 'specific',
  "evenfootingmarks"        => 'specific',
  "evenheadingmarks"        => 'specific',
  "everyfootingmarks"       => 'specific',
  "everyheadingmarks"       => 'specific',
  "exampleindent"           => 'specific',
  "firstparagraphindent"    => 'specific',
  "fonttextsize"            => 'specific',
  "footnotestyle"           => 'specific',
  "frenchspacing"           => 'specific',
  "headings"                => 'specific',
  "kbdinputstyle"           => 'specific',
  "microtype"               => 'specific',
  "need"                    => 'specific',
  "oddfootingmarks"         => 'specific',
  "oddheadingmarks"         => 'specific',
  "paragraphindent"         => 'specific',
  "printindex"              => 'specific',
  "setchapternewpage"       => 'specific',
  "sp"                      => 'specific',
  "syncodeindex"            => 'specific',
  "synindex"                => 'specific',
  "urefbreakstyle"          => 'specific',
  "xrefautomaticsectiontitle" => 'specific',
  "documentencoding"        => 'text',
  "documentlanguage"        => 'text',
  "end"                     => 'text',
  "include"                 => 'text',
  "setfilename"             => 'text',
  "verbatiminclude"         => 'text',
);

our %nobrace_commands = (
  "refill"                  => 'other',
  "thischapter"             => 'other',
  "thischaptername"         => 'other',
  "thischapternum"          => 'other',
  "thisfile"                => 'other',
  "thispage"                => 'other',
  "thissection"             => 'other',
  "thissectionname"         => 'other',
  "thissectionnum"          => 'other',
  "thistitle"               => 'other',
  "headitem"                => 'skipspace',
  "indent"                  => 'skipspace',
  "item"                    => 'skipspace',
  "noindent"                => 'skipspace',
  "tab"                     => 'skipspace',
  "!"                       => 'symbol',
  "&"                       => 'symbol',
  "*"                       => 'symbol',
  "-"                       => 'symbol',
  "."                       => 'symbol',
  "/"                       => 'symbol',
  ":"                       => 'symbol',
  "?"                       => 'symbol',
  "@"                       => 'symbol',
  "\\"                      => 'symbol',
  "\n"                      => 'symbol',
  "\t"                      => 'symbol',
  "\x20"                    => 'symbol',
  "{"                       => 'symbol',
  "|"                       => 'symbol',
  "}"                       => 'symbol',
);


# flag hashes
our %accent_commands = (
  "'"                       => 1,
  ","                       => 1,
  "="                       => 1,
  "H"                       => 1,
  "\""                      => 1,
  "^"                       => 1,
  "`"                       => 1,
  "dotaccent"               => 1,
  "dotless"                 => 1,
  "ogonek"                  => 1,
  "ringaccent"              => 1,
  "tieaccent"               => 1,
  "u"                       => 1,
  "ubaraccent"              => 1,
  "udotaccent"              => 1,
  "v"                       => 1,
  "~"                       => 1,
);

our %appendix_commands = (
  "appendix"                => 1,
  "appendixsec"             => 1,
  "appendixsection"         => 1,
  "appendixsubsec"          => 1,
  "appendixsubsubsec"       => 1,
);

our %blockitem_commands = (
  "enumerate"               => 1,
  "ftable"                  => 1,
  "itemize"                 => 1,
  "multitable"              => 1,
  "table"                   => 1,
  "vtable"                  => 1,
);

our %brace_code_commands = (
  "code"                    => 1,
  "command"                 => 1,
  "env"                     => 1,
  "file"                    => 1,
  "indicateurl"             => 1,
  "kbd"                     => 1,
  "key"                     => 1,
  "option"                  => 1,
  "samp"                    => 1,
  "t"                       => 1,
  "verb"                    => 1,
);

our %close_paragraph_commands = (
  "caption"                 => 1,
  "cartouche"               => 1,
  "center"                  => 1,
  "chapheading"             => 1,
  "contents"                => 1,
  "copying"                 => 1,
  "defblock"                => 1,
  "defcv"                   => 1,
  "defcvx"                  => 1,
  "deffn"                   => 1,
  "deffnx"                  => 1,
  "defivar"                 => 1,
  "defivarx"                => 1,
  "defline"                 => 1,
  "defmac"                  => 1,
  "defmacx"                 => 1,
  "defmethod"               => 1,
  "defmethodx"              => 1,
  "defop"                   => 1,
  "defopt"                  => 1,
  "defoptx"                 => 1,
  "defopx"                  => 1,
  "defspec"                 => 1,
  "defspecx"                => 1,
  "deftp"                   => 1,
  "deftpx"                  => 1,
  "deftypecv"               => 1,
  "deftypecvx"              => 1,
  "deftypefn"               => 1,
  "deftypefnx"              => 1,
  "deftypefun"              => 1,
  "deftypefunx"             => 1,
  "deftypeivar"             => 1,
  "deftypeivarx"            => 1,
  "deftypeline"             => 1,
  "deftypemethod"           => 1,
  "deftypemethodx"          => 1,
  "deftypeop"               => 1,
  "deftypeopx"              => 1,
  "deftypevar"              => 1,
  "deftypevarx"             => 1,
  "deftypevr"               => 1,
  "deftypevrx"              => 1,
  "defun"                   => 1,
  "defunx"                  => 1,
  "defvar"                  => 1,
  "defvarx"                 => 1,
  "defvr"                   => 1,
  "defvrx"                  => 1,
  "detailmenu"              => 1,
  "dircategory"             => 1,
  "direntry"                => 1,
  "display"                 => 1,
  "displaymath"             => 1,
  "documentdescription"     => 1,
  "enumerate"               => 1,
  "example"                 => 1,
  "exdent"                  => 1,
  "float"                   => 1,
  "flushleft"               => 1,
  "flushright"              => 1,
  "format"                  => 1,
  "ftable"                  => 1,
  "group"                   => 1,
  "heading"                 => 1,
  "headitem"                => 1,
  "indentedblock"           => 1,
  "insertcopying"           => 1,
  "item"                    => 1,
  "item_LINE"               => 1,
  "itemize"                 => 1,
  "itemx"                   => 1,
  "lisp"                    => 1,
  "listoffloats"            => 1,
  "majorheading"            => 1,
  "menu"                    => 1,
  "multitable"              => 1,
  "need"                    => 1,
  "nodedescription"         => 1,
  "nodedescriptionblock"    => 1,
  "page"                    => 1,
  "printindex"              => 1,
  "quotation"               => 1,
  "raggedright"             => 1,
  "setfilename"             => 1,
  "shortcaption"            => 1,
  "shortcontents"           => 1,
  "smalldisplay"            => 1,
  "smallexample"            => 1,
  "smallformat"             => 1,
  "smallindentedblock"      => 1,
  "smalllisp"               => 1,
  "smallquotation"          => 1,
  "sp"                      => 1,
  "subheading"              => 1,
  "subsubheading"           => 1,
  "summarycontents"         => 1,
  "tab"                     => 1,
  "table"                   => 1,
  "titlefont"               => 1,
  "titlepage"               => 1,
  "verbatim"                => 1,
  "verbatiminclude"         => 1,
  "vtable"                  => 1,
);

our %contain_basic_inline_commands = (
  "abbr"                    => 1,
  "acronym"                 => 1,
  "anchor"                  => 1,
  "appendix"                => 1,
  "appendixsec"             => 1,
  "appendixsection"         => 1,
  "appendixsubsec"          => 1,
  "appendixsubsubsec"       => 1,
  "author"                  => 1,
  "centerchap"              => 1,
  "chapheading"             => 1,
  "chapter"                 => 1,
  "cindex"                  => 1,
  "cpindex"                 => 1,
  "defcv"                   => 1,
  "defcvx"                  => 1,
  "deffn"                   => 1,
  "deffnx"                  => 1,
  "defivar"                 => 1,
  "defivarx"                => 1,
  "defmac"                  => 1,
  "defmacx"                 => 1,
  "defmethod"               => 1,
  "defmethodx"              => 1,
  "defop"                   => 1,
  "defopt"                  => 1,
  "defoptx"                 => 1,
  "defopx"                  => 1,
  "defspec"                 => 1,
  "defspecx"                => 1,
  "deftp"                   => 1,
  "deftpx"                  => 1,
  "deftypecv"               => 1,
  "deftypecvx"              => 1,
  "deftypefn"               => 1,
  "deftypefnx"              => 1,
  "deftypefun"              => 1,
  "deftypefunx"             => 1,
  "deftypeivar"             => 1,
  "deftypeivarx"            => 1,
  "deftypemethod"           => 1,
  "deftypemethodx"          => 1,
  "deftypeop"               => 1,
  "deftypeopx"              => 1,
  "deftypevar"              => 1,
  "deftypevarx"             => 1,
  "deftypevr"               => 1,
  "deftypevrx"              => 1,
  "defun"                   => 1,
  "defunx"                  => 1,
  "defvar"                  => 1,
  "defvarx"                 => 1,
  "defvr"                   => 1,
  "defvrx"                  => 1,
  "dircategory"             => 1,
  "email"                   => 1,
  "enumerate"               => 1,
  "errormsg"                => 1,
  "evenfooting"             => 1,
  "evenheading"             => 1,
  "everyfooting"            => 1,
  "everyheading"            => 1,
  "example"                 => 1,
  "findex"                  => 1,
  "float"                   => 1,
  "fnindex"                 => 1,
  "ftable"                  => 1,
  "heading"                 => 1,
  "image"                   => 1,
  "index_entry_command"     => 1,
  "inforef"                 => 1,
  "itemize"                 => 1,
  "kindex"                  => 1,
  "kyindex"                 => 1,
  "link"                    => 1,
  "listoffloats"            => 1,
  "majorheading"            => 1,
  "math"                    => 1,
  "multitable"              => 1,
  "node"                    => 1,
  "nodedescription"         => 1,
  "oddfooting"              => 1,
  "oddheading"              => 1,
  "part"                    => 1,
  "pgindex"                 => 1,
  "pindex"                  => 1,
  "pxref"                   => 1,
  "quotation"               => 1,
  "ref"                     => 1,
  "section"                 => 1,
  "seealso"                 => 1,
  "seeentry"                => 1,
  "settitle"                => 1,
  "shortcaption"            => 1,
  "shorttitlepage"          => 1,
  "smallquotation"          => 1,
  "subentry"                => 1,
  "subheading"              => 1,
  "subsection"              => 1,
  "subsubheading"           => 1,
  "subsubsection"           => 1,
  "subtitle"                => 1,
  "table"                   => 1,
  "tindex"                  => 1,
  "title"                   => 1,
  "titlefont"               => 1,
  "top"                     => 1,
  "tpindex"                 => 1,
  "unnumbered"              => 1,
  "unnumberedsec"           => 1,
  "unnumberedsubsec"        => 1,
  "unnumberedsubsubsec"     => 1,
  "uref"                    => 1,
  "url"                     => 1,
  "vindex"                  => 1,
  "vrindex"                 => 1,
  "vtable"                  => 1,
  "w"                       => 1,
  "xref"                    => 1,
);

our %contain_plain_text_commands = (
  "'"                       => 1,
  ","                       => 1,
  "="                       => 1,
  "H"                       => 1,
  "\""                      => 1,
  "^"                       => 1,
  "`"                       => 1,
  "alias"                   => 1,
  "allowcodebreaks"         => 1,
  "codequotebacktick"       => 1,
  "codequoteundirected"     => 1,
  "columnfractions"         => 1,
  "defcodeindex"            => 1,
  "defindex"                => 1,
  "definfoenclose"          => 1,
  "deftypefnnewline"        => 1,
  "dmn"                     => 1,
  "documentencoding"        => 1,
  "documentlanguage"        => 1,
  "dotaccent"               => 1,
  "dotless"                 => 1,
  "end"                     => 1,
  "evenfootingmarks"        => 1,
  "evenheadingmarks"        => 1,
  "everyfootingmarks"       => 1,
  "everyheadingmarks"       => 1,
  "exampleindent"           => 1,
  "firstparagraphindent"    => 1,
  "fonttextsize"            => 1,
  "footnotestyle"           => 1,
  "frenchspacing"           => 1,
  "headings"                => 1,
  "hyphenation"             => 1,
  "include"                 => 1,
  "kbdinputstyle"           => 1,
  "key"                     => 1,
  "microtype"               => 1,
  "need"                    => 1,
  "oddfootingmarks"         => 1,
  "oddheadingmarks"         => 1,
  "ogonek"                  => 1,
  "pagesizes"               => 1,
  "paragraphindent"         => 1,
  "printindex"              => 1,
  "ringaccent"              => 1,
  "setchapternewpage"       => 1,
  "setfilename"             => 1,
  "sortas"                  => 1,
  "sp"                      => 1,
  "syncodeindex"            => 1,
  "synindex"                => 1,
  "tieaccent"               => 1,
  "u"                       => 1,
  "ubaraccent"              => 1,
  "udotaccent"              => 1,
  "urefbreakstyle"          => 1,
  "v"                       => 1,
  "verbatiminclude"         => 1,
  "xrefautomaticsectiontitle" => 1,
  "~"                       => 1,
);

our %def_commands = (
  "defcv"                   => 1,
  "defcvx"                  => 1,
  "deffn"                   => 1,
  "deffnx"                  => 1,
  "defivar"                 => 1,
  "defivarx"                => 1,
  "defline"                 => 1,
  "defmac"                  => 1,
  "defmacx"                 => 1,
  "defmethod"               => 1,
  "defmethodx"              => 1,
  "defop"                   => 1,
  "defopt"                  => 1,
  "defoptx"                 => 1,
  "defopx"                  => 1,
  "defspec"                 => 1,
  "defspecx"                => 1,
  "deftp"                   => 1,
  "deftpx"                  => 1,
  "deftypecv"               => 1,
  "deftypecvx"              => 1,
  "deftypefn"               => 1,
  "deftypefnx"              => 1,
  "deftypefun"              => 1,
  "deftypefunx"             => 1,
  "deftypeivar"             => 1,
  "deftypeivarx"            => 1,
  "deftypeline"             => 1,
  "deftypemethod"           => 1,
  "deftypemethodx"          => 1,
  "deftypeop"               => 1,
  "deftypeopx"              => 1,
  "deftypevar"              => 1,
  "deftypevarx"             => 1,
  "deftypevr"               => 1,
  "deftypevrx"              => 1,
  "defun"                   => 1,
  "defunx"                  => 1,
  "defvar"                  => 1,
  "defvarx"                 => 1,
  "defvr"                   => 1,
  "defvrx"                  => 1,
);

our %def_alias_commands = (
  "defivar"                 => 1,
  "defivarx"                => 1,
  "defmac"                  => 1,
  "defmacx"                 => 1,
  "defmethod"               => 1,
  "defmethodx"              => 1,
  "defopt"                  => 1,
  "defoptx"                 => 1,
  "defspec"                 => 1,
  "defspecx"                => 1,
  "deftypefun"              => 1,
  "deftypefunx"             => 1,
  "deftypeivar"             => 1,
  "deftypeivarx"            => 1,
  "deftypemethod"           => 1,
  "deftypemethodx"          => 1,
  "deftypevar"              => 1,
  "deftypevarx"             => 1,
  "defun"                   => 1,
  "defunx"                  => 1,
  "defvar"                  => 1,
  "defvarx"                 => 1,
);

our %deprecated_commands = (
  "centerchap"              => 1,
  "definfoenclose"          => 1,
  "inforef"                 => 1,
  "refill"                  => 1,
);

our %explained_commands = (
  "abbr"                    => 1,
  "acronym"                 => 1,
);

our %formattable_line_commands = (
  "insertcopying"           => 1,
  "listoffloats"            => 1,
  "need"                    => 1,
  "printindex"              => 1,
  "sp"                      => 1,
  "verbatiminclude"         => 1,
  "vskip"                   => 1,
);

our %formatted_line_commands = (
  "appendix"                => 1,
  "appendixsec"             => 1,
  "appendixsection"         => 1,
  "appendixsubsec"          => 1,
  "appendixsubsubsec"       => 1,
  "author"                  => 1,
  "center"                  => 1,
  "centerchap"              => 1,
  "chapheading"             => 1,
  "chapter"                 => 1,
  "exdent"                  => 1,
  "heading"                 => 1,
  "item_LINE"               => 1,
  "itemx"                   => 1,
  "majorheading"            => 1,
  "node"                    => 1,
  "page"                    => 1,
  "part"                    => 1,
  "section"                 => 1,
  "subheading"              => 1,
  "subsection"              => 1,
  "subsubheading"           => 1,
  "subsubsection"           => 1,
  "subtitle"                => 1,
  "title"                   => 1,
  "top"                     => 1,
  "unnumbered"              => 1,
  "unnumberedsec"           => 1,
  "unnumberedsubsec"        => 1,
  "unnumberedsubsubsec"     => 1,
);

our %formatted_nobrace_commands = (
  "!"                       => 1,
  "&"                       => 1,
  "*"                       => 1,
  "-"                       => 1,
  "."                       => 1,
  "/"                       => 1,
  ":"                       => 1,
  "?"                       => 1,
  "@"                       => 1,
  "\\"                      => 1,
  "\n"                      => 1,
  "\t"                      => 1,
  "\x20"                    => 1,
  "headitem"                => 1,
  "item"                    => 1,
  "tab"                     => 1,
  "{"                       => 1,
  "}"                       => 1,
);

our %global_commands = (
  "allowcodebreaks"         => 1,
  "author"                  => 1,
  "chapheading"             => 1,
  "clickstyle"              => 1,
  "codequotebacktick"       => 1,
  "codequoteundirected"     => 1,
  "contents"                => 1,
  "deftypefnnewline"        => 1,
  "detailmenu"              => 1,
  "documentencoding"        => 1,
  "documentlanguage"        => 1,
  "exampleindent"           => 1,
  "firstparagraphindent"    => 1,
  "float"                   => 1,
  "footnote"                => 1,
  "frenchspacing"           => 1,
  "heading"                 => 1,
  "headings"                => 1,
  "hyphenation"             => 1,
  "insertcopying"           => 1,
  "kbdinputstyle"           => 1,
  "listoffloats"            => 1,
  "majorheading"            => 1,
  "microtype"               => 1,
  "paragraphindent"         => 1,
  "part"                    => 1,
  "printindex"              => 1,
  "shortcontents"           => 1,
  "subheading"              => 1,
  "subsubheading"           => 1,
  "subtitle"                => 1,
  "summarycontents"         => 1,
  "titlefont"               => 1,
  "urefbreakstyle"          => 1,
  "xrefautomaticsectiontitle" => 1,
);

our %global_unique_commands = (
  "afivepaper"              => 1,
  "afourlatex"              => 1,
  "afourpaper"              => 1,
  "afourwide"               => 1,
  "bsixpaper"               => 1,
  "copying"                 => 1,
  "documentdescription"     => 1,
  "evenfooting"             => 1,
  "evenfootingmarks"        => 1,
  "evenheading"             => 1,
  "evenheadingmarks"        => 1,
  "everyfooting"            => 1,
  "everyfootingmarks"       => 1,
  "everyheading"            => 1,
  "everyheadingmarks"       => 1,
  "fonttextsize"            => 1,
  "footnotestyle"           => 1,
  "novalidate"              => 1,
  "oddfooting"              => 1,
  "oddfootingmarks"         => 1,
  "oddheading"              => 1,
  "oddheadingmarks"         => 1,
  "pagesizes"               => 1,
  "setchapternewpage"       => 1,
  "setfilename"             => 1,
  "settitle"                => 1,
  "shorttitlepage"          => 1,
  "smallbook"               => 1,
  "title"                   => 1,
  "titlepage"               => 1,
  "top"                     => 1,
);

our %heading_spec_commands = (
  "evenfooting"             => 1,
  "evenheading"             => 1,
  "everyfooting"            => 1,
  "everyheading"            => 1,
  "oddfooting"              => 1,
  "oddheading"              => 1,
);

our %in_heading_spec_commands = (
  "thischapter"             => 1,
  "thischaptername"         => 1,
  "thischapternum"          => 1,
  "thisfile"                => 1,
  "thispage"                => 1,
  "thissection"             => 1,
  "thissectionname"         => 1,
  "thissectionnum"          => 1,
  "thistitle"               => 1,
  "|"                       => 1,
);

our %in_index_commands = (
  "seealso"                 => 1,
  "seeentry"                => 1,
  "sortas"                  => 1,
  "subentry"                => 1,
);

our %index_entry_command_commands = (
  "cindex"                  => 1,
  "cpindex"                 => 1,
  "findex"                  => 1,
  "fnindex"                 => 1,
  "index_entry_command"     => 1,
  "kindex"                  => 1,
  "kyindex"                 => 1,
  "pgindex"                 => 1,
  "pindex"                  => 1,
  "tindex"                  => 1,
  "tpindex"                 => 1,
  "vindex"                  => 1,
  "vrindex"                 => 1,
);

our %inline_conditional_commands = (
  "inlineifclear"           => 1,
  "inlineifset"             => 1,
);

our %inline_format_commands = (
  "inlinefmt"               => 1,
  "inlinefmtifelse"         => 1,
  "inlineraw"               => 1,
);

our %internal_commands = (
  "definfoenclose_command"  => 1,
  "index_entry_command"     => 1,
  "txiinternalvalue"        => 1,
);

our %letter_no_arg_commands = (
  "AA"                      => 1,
  "AE"                      => 1,
  "DH"                      => 1,
  "L"                       => 1,
  "O"                       => 1,
  "OE"                      => 1,
  "TH"                      => 1,
  "aa"                      => 1,
  "ae"                      => 1,
  "dh"                      => 1,
  "l"                       => 1,
  "o"                       => 1,
  "oe"                      => 1,
  "ss"                      => 1,
  "th"                      => 1,
);

our %math_commands = (
  "displaymath"             => 1,
  "math"                    => 1,
);

our %no_paragraph_commands = (
  "*"                       => 1,
  "afivepaper"              => 1,
  "afourlatex"              => 1,
  "afourpaper"              => 1,
  "afourwide"               => 1,
  "alias"                   => 1,
  "allowcodebreaks"         => 1,
  "anchor"                  => 1,
  "appendix"                => 1,
  "appendixsec"             => 1,
  "appendixsection"         => 1,
  "appendixsubsec"          => 1,
  "appendixsubsubsec"       => 1,
  "author"                  => 1,
  "bsixpaper"               => 1,
  "bye"                     => 1,
  "c"                       => 1,
  "caption"                 => 1,
  "cartouche"               => 1,
  "center"                  => 1,
  "centerchap"              => 1,
  "chapheading"             => 1,
  "chapter"                 => 1,
  "cindex"                  => 1,
  "clear"                   => 1,
  "clickstyle"              => 1,
  "codequotebacktick"       => 1,
  "codequoteundirected"     => 1,
  "columnfractions"         => 1,
  "comment"                 => 1,
  "contents"                => 1,
  "copying"                 => 1,
  "cpindex"                 => 1,
  "defblock"                => 1,
  "defcodeindex"            => 1,
  "defcv"                   => 1,
  "defcvx"                  => 1,
  "deffn"                   => 1,
  "deffnx"                  => 1,
  "defindex"                => 1,
  "definfoenclose"          => 1,
  "defivar"                 => 1,
  "defivarx"                => 1,
  "defline"                 => 1,
  "defmac"                  => 1,
  "defmacx"                 => 1,
  "defmethod"               => 1,
  "defmethodx"              => 1,
  "defop"                   => 1,
  "defopt"                  => 1,
  "defoptx"                 => 1,
  "defopx"                  => 1,
  "defspec"                 => 1,
  "defspecx"                => 1,
  "deftp"                   => 1,
  "deftpx"                  => 1,
  "deftypecv"               => 1,
  "deftypecvx"              => 1,
  "deftypefn"               => 1,
  "deftypefnnewline"        => 1,
  "deftypefnx"              => 1,
  "deftypefun"              => 1,
  "deftypefunx"             => 1,
  "deftypeivar"             => 1,
  "deftypeivarx"            => 1,
  "deftypeline"             => 1,
  "deftypemethod"           => 1,
  "deftypemethodx"          => 1,
  "deftypeop"               => 1,
  "deftypeopx"              => 1,
  "deftypevar"              => 1,
  "deftypevarx"             => 1,
  "deftypevr"               => 1,
  "deftypevrx"              => 1,
  "defun"                   => 1,
  "defunx"                  => 1,
  "defvar"                  => 1,
  "defvarx"                 => 1,
  "defvr"                   => 1,
  "defvrx"                  => 1,
  "detailmenu"              => 1,
  "dircategory"             => 1,
  "direntry"                => 1,
  "display"                 => 1,
  "displaymath"             => 1,
  "docbook"                 => 1,
  "documentdescription"     => 1,
  "documentencoding"        => 1,
  "documentlanguage"        => 1,
  "end"                     => 1,
  "enumerate"               => 1,
  "errormsg"                => 1,
  "evenfooting"             => 1,
  "evenfootingmarks"        => 1,
  "evenheading"             => 1,
  "evenheadingmarks"        => 1,
  "everyfooting"            => 1,
  "everyfootingmarks"       => 1,
  "everyheading"            => 1,
  "everyheadingmarks"       => 1,
  "example"                 => 1,
  "exampleindent"           => 1,
  "exdent"                  => 1,
  "finalout"                => 1,
  "findex"                  => 1,
  "firstparagraphindent"    => 1,
  "float"                   => 1,
  "flushleft"               => 1,
  "flushright"              => 1,
  "fnindex"                 => 1,
  "fonttextsize"            => 1,
  "footnotestyle"           => 1,
  "format"                  => 1,
  "frenchspacing"           => 1,
  "ftable"                  => 1,
  "group"                   => 1,
  "heading"                 => 1,
  "headings"                => 1,
  "headitem"                => 1,
  "html"                    => 1,
  "hyphenation"             => 1,
  "ifclear"                 => 1,
  "ifcommanddefined"        => 1,
  "ifcommandnotdefined"     => 1,
  "ifdocbook"               => 1,
  "ifhtml"                  => 1,
  "ifinfo"                  => 1,
  "iflatex"                 => 1,
  "ifnotdocbook"            => 1,
  "ifnothtml"               => 1,
  "ifnotinfo"               => 1,
  "ifnotlatex"              => 1,
  "ifnotplaintext"          => 1,
  "ifnottex"                => 1,
  "ifnotxml"                => 1,
  "ifplaintext"             => 1,
  "ifset"                   => 1,
  "iftex"                   => 1,
  "ifxml"                   => 1,
  "ignore"                  => 1,
  "image"                   => 1,
  "include"                 => 1,
  "indent"                  => 1,
  "indentedblock"           => 1,
  "index_entry_command"     => 1,
  "index_entry_command"     => 1,
  "insertcopying"           => 1,
  "item"                    => 1,
  "item_LINE"               => 1,
  "itemize"                 => 1,
  "itemx"                   => 1,
  "kbdinputstyle"           => 1,
  "kindex"                  => 1,
  "kyindex"                 => 1,
  "latex"                   => 1,
  "linemacro"               => 1,
  "lisp"                    => 1,
  "listoffloats"            => 1,
  "lowersections"           => 1,
  "macro"                   => 1,
  "majorheading"            => 1,
  "menu"                    => 1,
  "microtype"               => 1,
  "multitable"              => 1,
  "need"                    => 1,
  "node"                    => 1,
  "nodedescription"         => 1,
  "nodedescriptionblock"    => 1,
  "noindent"                => 1,
  "novalidate"              => 1,
  "oddfooting"              => 1,
  "oddfootingmarks"         => 1,
  "oddheading"              => 1,
  "oddheadingmarks"         => 1,
  "page"                    => 1,
  "pagesizes"               => 1,
  "paragraphindent"         => 1,
  "part"                    => 1,
  "pgindex"                 => 1,
  "pindex"                  => 1,
  "printindex"              => 1,
  "quotation"               => 1,
  "raggedright"             => 1,
  "raisesections"           => 1,
  "refill"                  => 1,
  "rmacro"                  => 1,
  "section"                 => 1,
  "set"                     => 1,
  "setchapternewpage"       => 1,
  "setfilename"             => 1,
  "settitle"                => 1,
  "shortcaption"            => 1,
  "shortcontents"           => 1,
  "shorttitlepage"          => 1,
  "smallbook"               => 1,
  "smalldisplay"            => 1,
  "smallexample"            => 1,
  "smallformat"             => 1,
  "smallindentedblock"      => 1,
  "smalllisp"               => 1,
  "smallquotation"          => 1,
  "sp"                      => 1,
  "subentry"                => 1,
  "subheading"              => 1,
  "subsection"              => 1,
  "subsubheading"           => 1,
  "subsubsection"           => 1,
  "subtitle"                => 1,
  "summarycontents"         => 1,
  "syncodeindex"            => 1,
  "synindex"                => 1,
  "tab"                     => 1,
  "table"                   => 1,
  "tex"                     => 1,
  "tindex"                  => 1,
  "title"                   => 1,
  "titlefont"               => 1,
  "titlepage"               => 1,
  "top"                     => 1,
  "tpindex"                 => 1,
  "unmacro"                 => 1,
  "unnumbered"              => 1,
  "unnumberedsec"           => 1,
  "unnumberedsubsec"        => 1,
  "unnumberedsubsubsec"     => 1,
  "urefbreakstyle"          => 1,
  "verbatim"                => 1,
  "verbatiminclude"         => 1,
  "vindex"                  => 1,
  "vrindex"                 => 1,
  "vskip"                   => 1,
  "vtable"                  => 1,
  "xml"                     => 1,
  "xrefautomaticsectiontitle" => 1,
);

our %non_formatted_block_commands = (
  "ignore"                  => 1,
  "linemacro"               => 1,
  "macro"                   => 1,
  "nodedescriptionblock"    => 1,
  "rmacro"                  => 1,
);

our %non_formatted_brace_commands = (
  "anchor"                  => 1,
  "caption"                 => 1,
  "errormsg"                => 1,
  "hyphenation"             => 1,
  "shortcaption"            => 1,
  "sortas"                  => 1,
);

our %preamble_commands = (
  "*"                       => 1,
  "afivepaper"              => 1,
  "afourlatex"              => 1,
  "afourpaper"              => 1,
  "afourwide"               => 1,
  "alias"                   => 1,
  "allowcodebreaks"         => 1,
  "bsixpaper"               => 1,
  "bye"                     => 1,
  "c"                       => 1,
  "clear"                   => 1,
  "clickstyle"              => 1,
  "codequotebacktick"       => 1,
  "codequoteundirected"     => 1,
  "columnfractions"         => 1,
  "comment"                 => 1,
  "contents"                => 1,
  "copying"                 => 1,
  "defcodeindex"            => 1,
  "defindex"                => 1,
  "definfoenclose"          => 1,
  "deftypefnnewline"        => 1,
  "dircategory"             => 1,
  "direntry"                => 1,
  "docbook"                 => 1,
  "documentdescription"     => 1,
  "documentencoding"        => 1,
  "documentlanguage"        => 1,
  "end"                     => 1,
  "errormsg"                => 1,
  "evenfooting"             => 1,
  "evenfootingmarks"        => 1,
  "evenheading"             => 1,
  "evenheadingmarks"        => 1,
  "everyfooting"            => 1,
  "everyfootingmarks"       => 1,
  "everyheading"            => 1,
  "everyheadingmarks"       => 1,
  "exampleindent"           => 1,
  "finalout"                => 1,
  "firstparagraphindent"    => 1,
  "fonttextsize"            => 1,
  "footnotestyle"           => 1,
  "frenchspacing"           => 1,
  "headings"                => 1,
  "html"                    => 1,
  "hyphenation"             => 1,
  "ignore"                  => 1,
  "include"                 => 1,
  "indent"                  => 1,
  "inlinefmt"               => 1,
  "inlinefmtifelse"         => 1,
  "inlineifclear"           => 1,
  "inlineifset"             => 1,
  "inlineraw"               => 1,
  "kbdinputstyle"           => 1,
  "latex"                   => 1,
  "linemacro"               => 1,
  "lowersections"           => 1,
  "macro"                   => 1,
  "microtype"               => 1,
  "noindent"                => 1,
  "novalidate"              => 1,
  "oddfooting"              => 1,
  "oddfootingmarks"         => 1,
  "oddheading"              => 1,
  "oddheadingmarks"         => 1,
  "pagesizes"               => 1,
  "paragraphindent"         => 1,
  "raisesections"           => 1,
  "refill"                  => 1,
  "rmacro"                  => 1,
  "set"                     => 1,
  "setchapternewpage"       => 1,
  "setfilename"             => 1,
  "settitle"                => 1,
  "shortcontents"           => 1,
  "shorttitlepage"          => 1,
  "smallbook"               => 1,
  "summarycontents"         => 1,
  "syncodeindex"            => 1,
  "synindex"                => 1,
  "tex"                     => 1,
  "titlepage"               => 1,
  "unmacro"                 => 1,
  "urefbreakstyle"          => 1,
  "xml"                     => 1,
  "xrefautomaticsectiontitle" => 1,
);

our %preformatted_commands = (
  "display"                 => 1,
  "example"                 => 1,
  "format"                  => 1,
  "lisp"                    => 1,
  "smalldisplay"            => 1,
  "smallexample"            => 1,
  "smallformat"             => 1,
  "smalllisp"               => 1,
);

our %preformatted_code_commands = (
  "example"                 => 1,
  "lisp"                    => 1,
  "smallexample"            => 1,
  "smalllisp"               => 1,
);

our %ref_commands = (
  "inforef"                 => 1,
  "link"                    => 1,
  "pxref"                   => 1,
  "ref"                     => 1,
  "xref"                    => 1,
);

our %root_commands = (
  "appendix"                => 1,
  "appendixsec"             => 1,
  "appendixsection"         => 1,
  "appendixsubsec"          => 1,
  "appendixsubsubsec"       => 1,
  "centerchap"              => 1,
  "chapter"                 => 1,
  "node"                    => 1,
  "part"                    => 1,
  "section"                 => 1,
  "subsection"              => 1,
  "subsubsection"           => 1,
  "top"                     => 1,
  "unnumbered"              => 1,
  "unnumberedsec"           => 1,
  "unnumberedsubsec"        => 1,
  "unnumberedsubsubsec"     => 1,
);

our %sectioning_heading_commands = (
  "appendix"                => 1,
  "appendixsec"             => 1,
  "appendixsection"         => 1,
  "appendixsubsec"          => 1,
  "appendixsubsubsec"       => 1,
  "centerchap"              => 1,
  "chapheading"             => 1,
  "chapter"                 => 1,
  "heading"                 => 1,
  "majorheading"            => 1,
  "part"                    => 1,
  "section"                 => 1,
  "subheading"              => 1,
  "subsection"              => 1,
  "subsubheading"           => 1,
  "subsubsection"           => 1,
  "top"                     => 1,
  "unnumbered"              => 1,
  "unnumberedsec"           => 1,
  "unnumberedsubsec"        => 1,
  "unnumberedsubsubsec"     => 1,
);

our %unnumbered_commands = (
  "centerchap"              => 1,
  "part"                    => 1,
  "top"                     => 1,
  "unnumbered"              => 1,
  "unnumberedsec"           => 1,
  "unnumberedsubsec"        => 1,
  "unnumberedsubsubsec"     => 1,
);

our %variadic_commands = (
  "example"                 => 1,
);


# @-commands max number of arguments.  Not set for all commands,
# in general it only matters if > 1, as commands with 0 args
# are in specific categories, and default handling of commands
# ignore commas as argument delimiter, which corresponds to commands
# with 1 argument.  Mostly used in Parser.
our %commands_args_number = (
  "U"                       => 1,
  "abbr"                    => 2,
  "acronym"                 => 2,
  "alias"                   => 2,
  "allowcodebreaks"         => 1,
  "anchor"                  => 1,
  "c"                       => 1,
  "cartouche"               => 1,
  "clear"                   => 1,
  "clickstyle"              => 1,
  "codequotebacktick"       => 1,
  "codequoteundirected"     => 1,
  "comment"                 => 1,
  "defcodeindex"            => 1,
  "defindex"                => 1,
  "definfoenclose"          => 3,
  "deftypefnnewline"        => 1,
  "dmn"                     => 1,
  "email"                   => 2,
  "enumerate"               => 1,
  "errormsg"                => 1,
  "evenfootingmarks"        => 1,
  "evenheadingmarks"        => 1,
  "everyfootingmarks"       => 1,
  "everyheadingmarks"       => 1,
  "exampleindent"           => 1,
  "firstparagraphindent"    => 1,
  "float"                   => 2,
  "fonttextsize"            => 1,
  "footnotestyle"           => 1,
  "frenchspacing"           => 1,
  "ftable"                  => 1,
  "headings"                => 1,
  "hyphenation"             => 1,
  "ifclear"                 => 1,
  "ifcommanddefined"        => 1,
  "ifcommandnotdefined"     => 1,
  "ifset"                   => 1,
  "image"                   => 5,
  "inforef"                 => 3,
  "inlinefmt"               => 2,
  "inlinefmtifelse"         => 3,
  "inlineifclear"           => 2,
  "inlineifset"             => 2,
  "inlineraw"               => 2,
  "itemize"                 => 1,
  "kbdinputstyle"           => 1,
  "link"                    => 3,
  "microtype"               => 1,
  "need"                    => 1,
  "node"                    => 4,
  "oddfootingmarks"         => 1,
  "oddheadingmarks"         => 1,
  "paragraphindent"         => 1,
  "printindex"              => 1,
  "pxref"                   => 5,
  "quotation"               => 1,
  "ref"                     => 5,
  "seealso"                 => 1,
  "seeentry"                => 1,
  "set"                     => 2,
  "setchapternewpage"       => 1,
  "smallquotation"          => 1,
  "sortas"                  => 1,
  "sp"                      => 1,
  "syncodeindex"            => 2,
  "synindex"                => 2,
  "table"                   => 1,
  "titlefont"               => 1,
  "unmacro"                 => 1,
  "uref"                    => 3,
  "urefbreakstyle"          => 1,
  "url"                     => 3,
  "vskip"                   => 1,
  "vtable"                  => 1,
  "xref"                    => 5,
  "xrefautomaticsectiontitle" => 1,
);


# indices
our %index_names = (
'cp' => {'in_code' => 0},
'fn' => {'in_code' => 1},
'ky' => {'in_code' => 1},
'pg' => {'in_code' => 1},
'tp' => {'in_code' => 1},
'vr' => {'in_code' => 1},
);

foreach my $index (keys(%index_names)) {
  $index_names{$index}->{"name"} = $index;
}

our %default_index_commands;
foreach my $index_name (keys (%index_names)) {
  my $one_letter_prefix = substr($index_name, 0, 1);
  foreach my $prefix ($index_name, $one_letter_prefix) {
    $default_index_commands{$prefix."index"} = $index_name;
  }
}
1;
