<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_CIPHER-RC2</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Algorithm-Names">Algorithm Names</a></li>
      <li><a href="#Parameters">Parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_CIPHER-RC2 - The RC2 EVP_CIPHER implementations</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for RC2 symmetric encryption using the <b>EVP_CIPHER</b> API.</p>

<h2 id="Algorithm-Names">Algorithm Names</h2>

<p>The following algorithms are available in the legacy provider:</p>

<dl>

<dt id="RC2-CBC-RC2-or-RC2-128">&quot;RC2-CBC&quot;, &quot;RC2&quot; or &quot;RC2-128&quot;</dt>
<dd>

</dd>
<dt id="RC2-40-CBC-or-RC2-40">&quot;RC2-40-CBC&quot; or &quot;RC2-40&quot;</dt>
<dd>

</dd>
<dt id="RC2-64-CBC-or-RC2-64">&quot;RC2-64-CBC&quot; or &quot;RC2-64&quot;</dt>
<dd>

</dd>
<dt id="RC2-ECB">&quot;RC2-ECB&quot;</dt>
<dd>

</dd>
<dt id="RC2-CFB">&quot;RC2-CFB&quot;</dt>
<dd>

</dd>
<dt id="RC2-OFB">&quot;RC2-OFB&quot;</dt>
<dd>

</dd>
</dl>

<h2 id="Parameters">Parameters</h2>

<p>This implementation supports the parameters described in <a href="../man3/EVP_EncryptInit.html">&quot;PARAMETERS&quot; in EVP_EncryptInit(3)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider-cipher.html">provider-cipher(7)</a>, <a href="../man7/OSSL_PROVIDER-legacy.html">OSSL_PROVIDER-legacy(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


