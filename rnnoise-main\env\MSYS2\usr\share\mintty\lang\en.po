# re-localize system-localized items (system menu, chooser dialogs)
# to English; use built-in defaults for msgs not listed here

#: wininput.c:151
msgid "&<PERSON>ore"
msgstr "&Restore"

#: wininput.c:152
msgid "&Move"
msgstr "&Move"

#: wininput.c:153
msgid "&Size"
msgstr "&Size"

#: wininput.c:154
msgid "Mi&nimize"
msgstr "Mi&nimize"

#: wininput.c:155
msgid "Ma&ximize"
msgstr "Ma&ximize"

#: wininput.c:156
msgid "&Close"
msgstr "&Close"

#. __ take notice
#: windialog.c:517
msgid "I see"
msgstr "I see"

#. __ confirm action
#: windialog.c:520
msgid "OK"
msgstr "OK"

#: winctrls.c:910
msgid "&Apply"
msgstr "&Apply"

#: config.c:1879 winctrls.c:906 windialog.c:521
msgid "Cancel"
msgstr "Cancel"

#: winctrls.c:909
msgid "Font "
msgstr "Font"

#: winctrls.c:911
msgid "&Font:"
msgstr "&Font:"

#: winctrls.c:912
msgid "Font st&yle:"
msgstr "Font st&yle:"

#: winctrls.c:913
msgid "&Size:"
msgstr "&Size:"

#: winctrls.c:914
msgid "Sample"
msgstr "Sample"

#. __ font chooser text sample ("AaBbYyZz" by default)
#: winctrls.c:916
msgid "Ferqœm’4€"
msgstr "Cwm fjord bank glyphs vext quiz œ’4€"

#. __ this field is only shown with OldFontMenu=true
#: winctrls.c:933
msgid "Sc&ript:"
msgstr "Sc&ript:"

#. __ this field is only shown with OldFontMenu=true
#: winctrls.c:935
msgid "<A>Show more fonts</A>"
msgstr "<A>Show more fonts</A>"

#: winctrls.c:939
msgid "Colour "
msgstr "Colour"

#: winctrls.c:952
msgid "Colour"
msgstr "Colour"

#: winctrls.c:947
msgid "B&asic colours:"
msgstr "B&asic colours:"

#: winctrls.c:949
msgid "&Custom colours:"
msgstr "&Custom colours:"

#: winctrls.c:951
msgid "De&fine Custom Colours >>"
msgstr "De&fine Custom Colours >>"

#: winctrls.c:953
msgid "|S&olid"
msgstr "|S&olid"

#: winctrls.c:954
msgid "&Hue:"
msgstr "&Hue:"

#: winctrls.c:955
msgid "&Sat:"
msgstr "&Sat:"

#: winctrls.c:956
msgid "&Lum:"
msgstr "&Lum:"

#: winctrls.c:957
msgid "&Red:"
msgstr "&Red:"

#: winctrls.c:958
msgid "&Green:"
msgstr "&Green:"

#: winctrls.c:959
msgid "&Blue:"
msgstr "&Blue:"

#: winctrls.c:960
msgid "A&dd to Custom Colours"
msgstr "A&dd to Custom Colours"

