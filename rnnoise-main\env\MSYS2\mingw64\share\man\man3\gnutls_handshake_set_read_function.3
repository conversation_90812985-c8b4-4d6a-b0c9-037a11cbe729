.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake_set_read_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake_set_read_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_handshake_set_read_function(gnutls_session_t " session ", gnutls_handshake_read_func " func ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is \fBgnutls_session_t\fP type
.IP "gnutls_handshake_read_func func" 12
is the function to be called
.SH "DESCRIPTION"
This function will set a callback to be called when a handshake
message is being sent.
.SH "SINCE"
3.7.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
