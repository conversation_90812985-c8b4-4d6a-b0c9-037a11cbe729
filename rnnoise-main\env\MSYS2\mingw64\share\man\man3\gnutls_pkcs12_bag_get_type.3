.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs12_bag_get_type" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs12_bag_get_type \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs12.h>
.sp
.BI "int gnutls_pkcs12_bag_get_type(gnutls_pkcs12_bag_t " bag ", unsigned " indx ");"
.SH ARGUMENTS
.IP "gnutls_pkcs12_bag_t bag" 12
The bag
.IP "unsigned indx" 12
The element of the bag to get the type
.SH "DESCRIPTION"
This function will return the bag's type.
.SH "RETURNS"
On error a negative error value or one of the \fBgnutls_pkcs12_bag_type_t\fP enumerations.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
