.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hmac_get_len" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hmac_get_len \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "unsigned gnutls_hmac_get_len(gnutls_mac_algorithm_t " algorithm ");"
.SH ARGUMENTS
.IP "gnutls_mac_algorithm_t algorithm" 12
the hmac algorithm to use
.SH "DESCRIPTION"
This function will return the length of the output data
of the given hmac algorithm.
.SH "RETURNS"
The length or zero on error.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
