include(Compiler/XLClang)
__compiler_xlclang(CXX)

set(CMAKE_CXX_COMPILE_OPTIONS_EXPLICIT_LANGUAGE -x c++)

if (CMAKE_CXX_COMPILER_VERSION VERSION_GREATER_EQUAL 13.1.1)
  set(CMAKE_CXX98_STANDARD_COMPILE_OPTION  "")
  set(CMAKE_CXX98_EXTENSION_COMPILE_OPTION "")
  set(CMAKE_CXX98_STANDARD__HAS_FULL_SUPPORT ON)
  set(CMAKE_CXX11_STANDARD_COMPILE_OPTION  "-qlanglvl=extended0x")
  set(CMAKE_CXX11_EXTENSION_COMPILE_OPTION "-qlanglvl=extended0x")
  set(CMAKE_CXX11_STANDARD__HAS_FULL_SUPPORT ON)

  set(CMAKE_CXX_STANDARD_LATEST 11)

  if (CMAKE_CXX_COMPILER_VERSION VERSION_GREATER_EQUAL 13.1.2)
    set(CMAKE_CXX11_STANDARD_COMPILE_OPTION  "-std=c++11")
    set(CMAKE_CXX11_EXTENSION_COMPILE_OPTION "-std=gnu++11")
    set(CMAKE_CXX14_STANDARD_COMPILE_OPTION  "-std=c++1y")
    set(CMAKE_CXX14_EXTENSION_COMPILE_OPTION "-std=gnu++1y")
    set(CMAKE_CXX14_STANDARD__HAS_FULL_SUPPORT ON)

    set(CMAKE_CXX_STANDARD_LATEST 14)
  endif ()
  if (CMAKE_CXX_COMPILER_VERSION VERSION_GREATER_EQUAL 16.1.0)
    set(CMAKE_CXX14_STANDARD_COMPILE_OPTION  "-std=c++14")
    set(CMAKE_CXX14_EXTENSION_COMPILE_OPTION "-std=gnu++14")
  endif()
endif()

__compiler_check_default_language_standard(CXX 13.1.1 98)

set(CMAKE_CXX_COMPILE_OBJECT
  "<CMAKE_CXX_COMPILER> -x c++ <DEFINES> <INCLUDES> <FLAGS> -o <OBJECT> -c <SOURCE>")
