.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hmac_fast" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hmac_fast \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_hmac_fast(gnutls_mac_algorithm_t " algorithm ", const void * " key ", size_t " keylen ", const void * " ptext ", size_t " ptext_len ", void * " digest ");"
.SH ARGUMENTS
.IP "gnutls_mac_algorithm_t algorithm" 12
the hash algorithm to use
.IP "const void * key" 12
the key to use
.IP "size_t keylen" 12
the length of the key
.IP "const void * ptext" 12
the data to hash
.IP "size_t ptext_len" 12
the length of data to hash
.IP "void * digest" 12
is the output value of the hash
.SH "DESCRIPTION"
This convenience function will hash the given data and return output
on a single call. Note, this call will not work for MAC algorithms
that require nonce (like UMAC or GMAC).
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
