.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_query_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_query_data \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "int dane_query_data(dane_query_t " q ", unsigned int " idx ", unsigned int * " usage ", unsigned int * " type ", unsigned int * " match ", gnutls_datum_t * " data ");"
.SH ARGUMENTS
.IP "dane_query_t q" 12
The query result structure
.IP "unsigned int idx" 12
The index of the query response.
.IP "unsigned int * usage" 12
The certificate usage (see \fBdane_cert_usage_t\fP)
.IP "unsigned int * type" 12
The certificate type (see \fBdane_cert_type_t\fP)
.IP "unsigned int * match" 12
The DANE matching type (see \fBdane_match_type_t\fP)
.IP "gnutls_datum_t * data" 12
The DANE data.
.SH "DESCRIPTION"
This function will provide the DANE data from the query
response.
.SH "RETURNS"
On success, \fBDANE_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
