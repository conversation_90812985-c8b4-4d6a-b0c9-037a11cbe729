.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_set_server_credentials_file" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_set_server_credentials_file \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_psk_set_server_credentials_file(gnutls_psk_server_credentials_t " res ", const char * " password_file ");"
.SH ARGUMENTS
.IP "gnutls_psk_server_credentials_t res" 12
is a \fBgnutls_psk_server_credentials_t\fP type.
.IP "const char * password_file" 12
is the PSK password file (passwd.psk)
.SH "DESCRIPTION"
This function sets the password file, in a
\fBgnutls_psk_server_credentials_t\fP type.  This password file
holds usernames and keys and will be used for PSK authentication.

Each entry in the file consists of a username, followed by a colon
(':') and a hex\-encoded key.  If the username contains a colon or
any other special character, it can be hex\-encoded preceded by a
'#'.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
