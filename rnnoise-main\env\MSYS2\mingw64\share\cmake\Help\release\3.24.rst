CMake 3.24 Release Notes
************************

.. only:: html

  .. contents::

Changes made since CMake 3.23 include the following.

New Features
============

Presets
-------

* :manual:`cmake-presets(7)` files now support schema version ``5``.

* :manual:`cmake-presets(7)` files now support a ``${pathListSep}`` macro,
  which expands to ``:`` or ``;`` based on the platform.

* :manual:`cmake-presets(7)` files gained support for specifying a
  ``testOutputTruncation`` field in test presets, which specifies the
  truncation mode once the maximum test output size has been reached.

Generators
----------

* The :generator:`Green Hills MULTI` generator now generates build
  rules to re-run CMake if any CMake files are updated.

* The :ref:`Visual Studio Generators` now support ``SYSTEM`` headers
  when using VS 2019 Update 11 or later.

Command-Line
------------

* :manual:`cmake(1)` gained the :option:`--fresh <cmake --fresh>` command-line
  option to remove any existing ``CMakeCache.txt`` file and associated ``CMakeFiles/``
  directory, when configuring a build tree, thus starting a new configuration
  as if the build tree were freshly created.

* :manual:`cmake(1)` gained the
  :option:`--compile-no-warning-as-error <cmake --compile-no-warning-as-error>`
  command-line option which causes the effects of the
  :prop_tgt:`COMPILE_WARNING_AS_ERROR` target property and
  :variable:`CMAKE_COMPILE_WARNING_AS_ERROR` variable to be ignored.

* The :manual:`cmake(1)` :option:`--trace=json-v1 <cmake --trace>` trace
  format gained fields ``global_frame`` and ``line_end``.

* The :manual:`cmake(1)` :option:`-E <cmake -E>` commands :option:`cat <cmake-E cat>`
  and :option:`env <cmake-E env>` learned to respect a double dash
  (:option:`-- <cmake-E_env -->`) argument that acts as a delimiter indicating
  the end of options. Any following arguments are treated as operands/positional
  arguments, even if they begin with a dash ``-`` character.

* The :manual:`cmake(1)` :option:`-E tar <cmake-E tar>` command gained the
  :option:`--touch <cmake-E_tar --touch>` option
  to keep the current local timestamp instead of extracting file timestamps
  from the archive.

Compilers
---------

* LLVM's `flang`_ Fortran compiler is now supported on some platforms,
  with compiler id ``LLVMFlang``.

.. _`flang`: https://github.com/llvm/llvm-project/tree/main/flang

* ADSP compiler support (SHARC and Blackfin) now covers both CCES and
  VDSP++ installations, with required configuration now done in the
  compiler module itself rather than the ``Generic-ADSP`` platform module.

Platforms
---------

* A dedicated ``ADSP`` platform has been added
  to replace the existing ``Generic-ADSP`` implementation.
  This features automatic detection of the latest CCES/VDSP++ install
  and compiler selection (``cc21k`` vs. ``ccblkfn``)
  based off of the :variable:`CMAKE_SYSTEM_PROCESSOR` variable.

Commands
--------

* The :command:`cmake_host_system_information` command, on Windows,
  gained a ``QUERY WINDOWS_REGISTRY`` mode.
  See its :ref:`Query Windows registry` section.

* The :command:`cmake_language` command gained a new
  ``SET_DEPENDENCY_PROVIDER`` sub-command.  When a dependency provider is set,
  calls to :command:`find_package` and :command:`FetchContent_MakeAvailable`
  can be redirected through a custom command, which can choose to fulfill the
  request directly, modify how the request is processed, or leave it to be
  fulfilled by the built-in implementation.  See :ref:`dependency_providers`.

* The :command:`file(DOWNLOAD)` command gained options ``RANGE_START`` and
  ``RANGE_END`` to specify a range of bytes to download.  This can be
  useful for downloading parts of big binary files.

* The :command:`find_file`, :command:`find_path`, :command:`find_library`,
  :command:`find_program`, and :command:`find_package` commands gained the
  ``NO_CMAKE_INSTALL_PREFIX`` option to control searching
  :variable:`CMAKE_INSTALL_PREFIX`.

* The :command:`find_file`, :command:`find_path`, :command:`find_library`,
  :command:`find_program`, and :command:`find_package` commands gained the
  ability to specify which Windows Registry views must be queried.

* The :command:`find_package` command gained a ``GLOBAL`` option that
  allows for the promotion of imported targets to global scope for the
  duration of the :command:`find_package` call.

* The :command:`if` command gained the capability to compare paths by
  using the ``PATH_EQUAL`` operator.  See policy :policy:`CMP0139`.

Variables
---------

* The :variable:`CMAKE_COLOR_DIAGNOSTICS` variable was added to control
  color diagnostics generated by compilers.  This variable also controls
  color build system messages with :ref:`Makefile Generators`, replacing
  :variable:`CMAKE_COLOR_MAKEFILE`.

  The :envvar:`CMAKE_COLOR_DIAGNOSTICS` environment variable was added to set
  a default value for :variable:`CMAKE_COLOR_DIAGNOSTICS`.

* The :variable:`CMAKE_COMPILE_WARNING_AS_ERROR` variable and corresponding
  :prop_tgt:`COMPILE_WARNING_AS_ERROR` target property were added to enable
  compilation with a compiler-specific flag to treat warnings as errors,
  such as ``-Werror``.

* The :variable:`CMAKE_CUDA_ARCHITECTURES` variable and associated
  :prop_tgt:`CUDA_ARCHITECTURES` target property now support the
  special ``native`` value to compile for the architectures(s)
  of the host's GPU(s).

* The :variable:`CMAKE_FIND_PACKAGE_TARGETS_GLOBAL` variable was added to
  toggle behavior of the :command:`find_package` command's new ``GLOBAL``
  option.

* The :variable:`CMAKE_FIND_USE_INSTALL_PREFIX` variable was added to toggle
  behavior of the :command:`find_file`, :command:`find_library`,
  :command:`find_path`, :command:`find_package`, and :command:`find_program`
  commands' new ``NO_CMAKE_INSTALL_PREFIX`` option.

* The :variable:`CMAKE_PROJECT_TOP_LEVEL_INCLUDES` variable was added to allow
  injecting custom code at the site of the first :command:`project` call,
  after the host and target platform details have been determined.

* The :variable:`CMAKE_TRY_COMPILE_NO_PLATFORM_VARIABLES` variable
  was added to tell the :command:`try_compile` command not to
  pass any platform variables to the test project.

* The :variable:`CMAKE_VERIFY_INTERFACE_HEADER_SETS` variable and
  corresponding :prop_tgt:`VERIFY_INTERFACE_HEADER_SETS` target property
  were added to enable build rules that verify all headers in header sets
  can be used on their own.

* The :variable:`CMAKE_VS_NO_COMPILE_BATCHING` variable and corresponding
  :prop_tgt:`VS_NO_COMPILE_BATCHING` target property were added to
  tell :ref:`Visual Studio Generators` whether to disable compiler
  parallelism and call the compiler with one source file at a time.

* The :variable:`CMAKE_WATCOM_RUNTIME_LIBRARY` variable and
  :prop_tgt:`WATCOM_RUNTIME_LIBRARY` target property were introduced to
  select the runtime library used by compilers targeting the Watcom ABI.
  See policy :policy:`CMP0136`.

* The :variable:`CMAKE_XCODE_XCCONFIG` variable and corresponding
  :prop_tgt:`XCODE_XCCONFIG` target property were added to tell
  the :generator:`Xcode` generator to handle ``xcconfig`` files.

Properties
----------

* The :prop_tgt:`INTERFACE_LINK_LIBRARIES_DIRECT` and
  :prop_tgt:`INTERFACE_LINK_LIBRARIES_DIRECT_EXCLUDE` target properties
  were added to express usage requirements affecting a consumer's
  direct link dependencies.

* The :prop_tgt:`INTERFACE_HEADER_SETS_TO_VERIFY` target property was
  added to specify which header sets should be verified by
  :prop_tgt:`VERIFY_INTERFACE_HEADER_SETS`.

* The :prop_tgt:`LINK_LIBRARIES` target property now supports
  the :genex:`$<LINK_ONLY:...>` generator expression.
  See policy :policy:`CMP0131`.

* The :prop_tgt:`VS_DOTNET_STARTUP_OBJECT` target property was added to
  tell :ref:`Visual Studio Generators` which startup class shall be used
  when the program or project is executed. This is necessary when more
  than one ``static void Main(string[])`` function signature is available
  in a managed .NET project.

Modules
-------

* The :module:`ExternalProject` module :command:`ExternalProject_Add`
  command gained a new ``DOWNLOAD_EXTRACT_TIMESTAMP`` option for
  controlling whether the timestamps of extracted contents are set to
  match those in the archive when the ``URL`` download method is used.
  Policy :policy:`CMP0135` was added to enable the option by default.

* The :module:`FetchContent` module and the :command:`find_package` command
  now support integration capabilities:

  * :command:`FetchContent_MakeAvailable` can now try to satisfy a dependency
    by calling :command:`find_package` first.  A new
    :variable:`FETCHCONTENT_TRY_FIND_PACKAGE_MODE` variable controls whether
    this is done by default for all dependencies, is opt-in per dependency,
    or is disabled entirely.

  * :command:`find_package` can be re-routed to call
    :command:`FetchContent_MakeAvailable` instead.  A new read-only
    :variable:`CMAKE_FIND_PACKAGE_REDIRECTS_DIR` variable points to a
    directory where config package files can be located to facilitate these
    re-routed calls.

* The :module:`FindJNI` module now provides imported targets.

* The :module:`FindMatlab` module :command:`matlab_add_mex` function
  gained a ``NO_IMPLICIT_LINK_TO_MATLAB_LIBRARIES`` option to disable
  automatic linking of MATLAB libraries.

* The :module:`FindVulkan` module now supports components to select which
  VulkanSDK tool and libraries to find in addition to the Vulkan SDK headers
  and library.

* The :module:`FindZLIB` gained a new ``ZLIB_USE_STATIC_LIBS`` variable to
  search only for static libraries.

Generator Expressions
---------------------

* The :genex:`LINK_LIBRARY` generator expression was added to manage how
  libraries are specified during the link step.
  The :variable:`CMAKE_<LANG>_LINK_LIBRARY_USING_<FEATURE>` and
  :variable:`CMAKE_LINK_LIBRARY_USING_<FEATURE>` variables are used to define
  features usable by the :genex:`LINK_LIBRARY` generator expression.
  Moreover, the :prop_tgt:`LINK_LIBRARY_OVERRIDE` and
  :prop_tgt:`LINK_LIBRARY_OVERRIDE_<LIBRARY>` target properties are
  available to resolve incompatible features.

  The :genex:`LINK_LIBRARY` generator expression can link frameworks in
  various ways when targeting ``Apple`` platforms.
  The following features were added:

  * ``FRAMEWORK``
  * ``NEEDED_FRAMEWORK``
  * ``REEXPORT_FRAMEWORK``
  * ``WEAK_FRAMEWORK``

  The :genex:`LINK_LIBRARY` generator expression can link libraries in
  various ways when targeting ``Apple`` platforms.
  The following features were added:

  * ``NEEDED_LIBRARY``
  * ``REEXPORT_LIBRARY``
  * ``WEAK_LIBRARY``

  The :genex:`LINK_LIBRARY` generator expression gained the feature
  ``WHOLE_ARCHIVE`` to force load of all members in a static library.
  This feature is supported on the following target platforms:

  * all ``Apple`` variants
  * ``Linux``
  * all ``BSD`` variants
  * ``SunOS``
  * ``Windows``
  * ``CYGWIN``
  * ``MSYS``

* The :genex:`LINK_GROUP` generator expression was added to manage the
  grouping of libraries during the link step.  The
  :variable:`CMAKE_<LANG>_LINK_GROUP_USING_<FEATURE>` and
  :variable:`CMAKE_LINK_GROUP_USING_<FEATURE>` variables are used to define
  features usable with the :genex:`LINK_GROUP` generator expression.
  This release defines the ``RESCAN`` feature, which can be used to handle
  circular references among static libraries when using toolchains for
  Linux, BSD, SunOS and GNU toolchains for Windows.

* The :genex:`PATH` generator expression was added to manage paths.

* The :genex:`PATH_EQUAL` generator expression was added to manage path
  comparisons.

* The :genex:`TARGET_BUNDLE_DIR_NAME` generator expression
  was added to evaluate to the name of the bundle directory
  for a given bundle target.

CTest
-----

* :manual:`ctest(1)` gained a
  :option:`--test-output-truncation <ctest --test-output-truncation>` option
  (and corresponding :variable:`CTEST_CUSTOM_TEST_OUTPUT_TRUNCATION` variable)
  to specify the truncation mode once the maximum test output size has been
  reached. Possible values are ``tail`` (default), ``middle`` or ``head``.

CPack
-----

* The :cpack_gen:`CPack WIX Generator` gained a new variable,
  :variable:`CPACK_WIX_ARCHITECTURE`, to specify the installer architecture
  in order to support computers running Windows for ARM.

* CPack now supports the :variable:`CPACK_THREADS` option for ``zstd``
  compression when compiled with libarchive 3.6 or higher.  It is
  supported by official CMake binaries available on `cmake.org`_.

Deprecated and Removed Features
===============================

* The :module:`CPack` module no longer enables the SLA by default in the
  :cpack_gen:`CPack DragNDrop Generator`.  See policy :policy:`CMP0133`
  and the :variable:`CPACK_DMG_SLA_USE_RESOURCE_FILE_LICENSE` variable.

* The deprecated :cpack_gen:`CPack PackageMaker Generator` has been removed.

* The :module:`FindGLUT` module no longer provides the undocumented
  ``GLUT_LIBRARY`` and ``GLUT_INCLUDE_PATH`` result variables.

* The :module:`FindVulkan` module no longer silently ignores unknown
  components requested by a ``find_package(Vulkan REQUIRED ...)`` call.
  With the addition of support for components, requests for unknown
  components now produce an error.

Other Changes
=============

* CMake no longer sets environment variables like :envvar:`CC`, :envvar:`CXX`,
  etc. when enabling the corresponding language during the first CMake run in
  a build directory.  See policy :policy:`CMP0132`.

* The :module:`CheckIPOSupported` module :command:`check_ipo_supported`
  command now uses the caller's :variable:`CMAKE_<LANG>_FLAGS`
  and :variable:`CMAKE_<LANG>_FLAGS_<CONFIG>` values.
  See policy :policy:`CMP0138`.

* The :generator:`MSYS Makefiles` and :generator:`MinGW Makefiles`
  generators, when a compiler is not explicitly specified, now select
  the first compiler (of any name) found in directories listed by the
  ``PATH`` environment variable.

* The :command:`try_compile` command
  :ref:`whole-project <Try Compiling Whole Projects>` signature
  now propagates platform variables.  See policy :policy:`CMP0137`.

* The :command:`while` command now diagnoses errors during condition
  evaluation.  See policy :policy:`CMP0130`.

* The precompiled macOS binaries provided on `cmake.org`_ no longer attach a
  SLA to the ``.dmg`` packages.  This was removed because macOS 12 deprecated
  the tools used to attach ``.dmg`` resources.

* A precompiled Windows ``arm64`` binary is now provided on `cmake.org`_.

.. _`cmake.org`: https://cmake.org/download/

Updates
=======

Changes made since CMake 3.24.0 include the following.

3.24.1, 3.24.2
--------------

* These versions made no changes to documented features or interfaces.
  Some implementation updates were made to support ecosystem changes
  and/or fix regressions.

3.24.3
------

* The ``LLVMFlang`` Fortran compiler support added in 3.24.0 has been extended:

  * It now supports mixed-language linking between Fortran and C or CXX.
  * It now supports the GNU ABI (MinGW) on Windows, but not yet the MSVC ABI.

* Some implementation updates were made to support ecosystem changes
  and/or fix regressions.

3.24.4
------

* This version made no changes to documented features or interfaces.
  Some implementation updates were made to support ecosystem changes
  and/or fix regressions.
