<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_whirlpool</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_whirlpool - WHIRLPOOL For EVP</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const EVP_MD *EVP_whirlpool(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>WHIRLPOOL is a cryptographic hash function standardized in ISO/IEC 10118-3:2004 designed by Vincent Rijmen and Paulo S. L. M. Barreto. This implementation is only available with the legacy provider.</p>

<dl>

<dt id="EVP_whirlpool">EVP_whirlpool()</dt>
<dd>

<p>The WHIRLPOOL algorithm that produces a message digest of 512-bits from a given input.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>Developers should be aware of the negative performance implications of calling this function multiple times and should consider using <a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a> with <a href="../man7/EVP_MD-WHIRLPOOL.html">EVP_MD-WHIRLPOOL(7)</a> instead. See <a href="../man7/crypto.html">&quot;Performance&quot; in crypto(7)</a> for further information.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return a <b>EVP_MD</b> structure that contains the implementation of the message digest. See <a href="../man3/EVP_MD_meth_new.html">EVP_MD_meth_new(3)</a> for details of the <b>EVP_MD</b> structure.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>ISO/IEC 10118-3:2004.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man7/provider.html">provider(7)</a>, <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


