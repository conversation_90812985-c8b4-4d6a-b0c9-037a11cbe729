# help.it.txt - Italian GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
E compito tuo assegnare un valore; questo valore non sarà mai esportato a
terzi. Ci serve per implementare il web-of-trust; non ha nulla a che fare
con il web-of-certificates (creato implicitamente).
.

.gpg.edit_ownertrust.set_ultimate.okay
Per costruire il Web-Of-Trust, GnuPG ha bisogno di sapere quali chiavi sono
definitivamente affidabili - di solito quelle per cui hai accesso alla chiave
segreta.
Rispondi "sì" per impostare questa chiave come definitivamente affidabile

.

.gpg.untrusted_key.override
Se vuoi usare comunque questa chiave non fidata, rispondi "si".
.

.gpg.pklist.user_id.enter
Inserisci l'user ID del destinatario a cui vuoi mandare il messaggio.
.

.#gpg.keygen.algo
# fixme: Please translate and remove the hash mark from the key line.
Select the algorithm to use.

DSA (aka DSS) is the Digital Signature Algorithm and can only be used
for signatures.

Elgamal is an encrypt-only algorithm.

RSA may be used for signatures or encryption.

The first (primary) key must always be a key which is capable of signing.
.

.gpg.keygen.algo.rsa_se
In generale non è una buona idea usare la stessa chiave per le firme e la
cifratura. Questo algoritmo dovrebbe solo essere usato in determinati campi.
Per favore consulta prima il tuo esperto di sicurezza.
.

.gpg.keygen.size
Inserisci le dimensioni della chiave
.

.gpg.keygen.size.huge.okay
Rispondi "si" o "no"
.

.gpg.keygen.size.large.okay
Rispondi "si" o "no"
.

.gpg.keygen.valid
Inserisci il valore richiesto come indicato dal prompt.
È possibile inserire una data in formato ISO (YYYY-MM-DD) ma non avrai un
messaggio di errore corretto: il sistema cerca di interpretare il valore
dato come un intervallo.
.

.gpg.keygen.valid.okay
Rispondi "si" o "no"
.

.gpg.keygen.name
Inserisci il nome del proprietario della chiave
.

.gpg.keygen.email
Inserisci un indirizzo di email opzionale (ma fortemente suggerito)
.

.gpg.keygen.comment
Inserisci un commento opzionale
.

.gpg.keygen.userid.cmd
N  per cambiare il nome.
C  per cambiare il commento.
E  per cambiare l'indirizzo di email.
O  per continuare con la generazione della chiave.
Q  per abbandonare il processo di generazione della chiave.
.

.gpg.keygen.sub.okay
Rispondi "si" (o "y") se va bene generare la subchiave.
.

.gpg.sign_uid.okay
Rispondi "si" o "no"
.

.gpg.sign_uid.class
Quando firmi l'user ID di una chiave dovresti prima verificare che questa
appartiene alla persona indicata nell'user ID. È utile agli altri sapere
con quanta attenzione lo hai verificato.

"0" significa che non fai particolari affermazioni sull'attenzione con cui
    hai ferificato la chiave.

"1" significa che credi che la chiave sia posseduta dalla persona che dice di
    possederla, ma non hai o non hai potuto verificare per niente la chiave.

"2" significa che hai fatto una verifica superficiale della chiave. Per esempio
    potrebbe significare che hai verificato l'impronta digitale e confrontato
    l'user ID della chiave con un documento di identità con fotografia.

"3" significa che hai fatto una verifica approfondita della chiave. Per esempio
    potrebbe significare che hai verificato di persona l'impronta digitale con
    il possessore della chiave e hai controllato, per esempio per mezzo di
    un documento di identità con fotografia difficile da falsificare (come
    un passaporto), che il nome del proprietario della chiave corrisponde a
    quello nell'user ID della chiave, e per finire che hai verificato
    (scambiando dei messaggi) che l'indirizzo di email sulla chiave appartiene
    al proprietario.

Nota che gli esempi indicati per i livelli 2 e 3 sono *solo* esempi. Alla fine
sta a te decidere cosa significano "superficiale" e "approfondita" quando
firmi chiavi di altri.

Se non sai cosa rispondere, rispondi "0".
.

.gpg.change_passwd.empty.okay
Rispondi "si" o "no"
.

.gpg.keyedit.save.okay
Rispondi "si" o "no"
.

.gpg.keyedit.cancel.okay
Rispondi "si" o "no"
.

.#gpg.keyedit.sign_all.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if you want to sign ALL the user IDs
.

.gpg.keyedit.remove.uid.okay
Rispondi "si" se vuoi davvero cancellare questo user ID.
Tutti i certificati saranno persi!
.

.gpg.keyedit.remove.subkey.okay
Rispondi "si" se va bene cancellare la subchiave
.

.gpg.keyedit.delsig.valid
Questa è una firma valida per la chiave. Normalmente non vorresti cancellare
questa firma perchè può essere importante per stabilire una connessione di
fiducia alla chiave o a un'altra chiave certificata da questa chiave.
.

.gpg.keyedit.delsig.unknown
Questa firma non può essere verificata perchè non hai la chiave corrispondente.
Dovresti rimandare la sua cancellazione finchè non saprai quale chiave è stata
usata perchè questa chiave potrebbe stabilire una connessione di fiducia
attraverso una chiave già certificata.
.

.gpg.keyedit.delsig.invalid
La firma non è valida. Ha senso rimuoverla dal tuo portachiavi.
.

.gpg.keyedit.delsig.selfsig
Questa è una firma che collega l'user id alla chiave. Solitamente non è una
buona idea rimuovere questo tipo di firma. In realtà GnuPG potrebbe non essere
più in grado di usare questa chiave. Quindi fallo solo se questa autofirma non
è valida per qualche ragione e ne è disponibile un'altra.
.

.gpg.keyedit.updpref.okay
Cambia le preferenze di tutti gli user ID (o solo di quelli selezionati) con
la lista di preferenze corrente. L'orario di tutte le autofirme coinvolte
sarà aumentato di un secondo.

.

.gpg.passphrase.enter
Inserisci la passphrase, cioè una frase segreta 

.

.gpg.passphrase.repeat
Ripeti l'ultima passphrase per essere sicuro di cosa hai scritto.
.

.gpg.detached_signature.filename
Inserisci il nome del file a cui si riferisce la firma.
.

.gpg.openfile.overwrite.okay
Rispondi "si" se va bene sovrascrivere il file.
.

.gpg.openfile.askoutname
Inserisci il nuovo nome del file. Se premi INVIO sarà usato il nome
predefinito (quello indicato tra parentesi).
.

.gpg.ask_revocation_reason.code
Dovresti specificare un motivo per questa certificazione. A seconda del
contesto hai la possibilità di scegliere tra questa lista:
  "Key has been compromised"
      Usa questo se hai un motivo per credere che una persona non autorizzata
      abbia avuto accesso alla tua chiave segreta.
  "Key is superseded"
      Usa questo se hai sostituito questa chiave con una più recente.
  "Key is no longer used"
      Usa questo se hai mandato in pensione questa chiave.
  "User ID is no longer valid"
      Usa questo per affermare che l'user ID non dovrebbe più essere usato;
      solitamente è usato per indicare un indirizzo di email non valido.

.

.gpg.ask_revocation_reason.text
Se vuoi, puoi digitare un testo che descrive perché hai emesso
questo certificato di revoca. Per favore sii conciso.
Una riga vuota termina il testo.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
