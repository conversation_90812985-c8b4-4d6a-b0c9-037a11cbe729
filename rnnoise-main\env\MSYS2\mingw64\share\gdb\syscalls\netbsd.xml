<?xml version="1.0"?> <!-- THIS FILE IS GENERATED -*- buffer-read-only: t -*-  -->
<!-- vi:set ro: -->
<!-- Copyright (C) 2020-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-syscalls.dtd">

<!-- This file was generated using the following file:

     /usr/src/sys/sys/syscall.h

     The file mentioned above belongs to the NetBSD Kernel.  -->

<syscalls_info>
  <syscall name="exit" number="1"/>
  <syscall name="fork" number="2"/>
  <syscall name="read" number="3"/>
  <syscall name="write" number="4"/>
  <syscall name="open" number="5"/>
  <syscall name="close" number="6"/>
  <syscall name="compat_50_wait4" number="7"/>
  <syscall name="compat_43_ocreat" number="8"/>
  <syscall name="link" number="9"/>
  <syscall name="unlink" number="10"/>
  <syscall name="execv" number="11"/>
  <syscall name="chdir" number="12"/>
  <syscall name="fchdir" number="13"/>
  <syscall name="compat_50_mknod" number="14"/>
  <syscall name="chmod" number="15"/>
  <syscall name="chown" number="16"/>
  <syscall name="break" number="17"/>
  <syscall name="compat_20_getfsstat" number="18"/>
  <syscall name="compat_43_olseek" number="19"/>
  <syscall name="getpid" number="20"/>
  <syscall name="compat_40_mount" number="21"/>
  <syscall name="unmount" number="22"/>
  <syscall name="setuid" number="23"/>
  <syscall name="getuid" number="24"/>
  <syscall name="geteuid" number="25"/>
  <syscall name="ptrace" number="26"/>
  <syscall name="recvmsg" number="27"/>
  <syscall name="sendmsg" number="28"/>
  <syscall name="recvfrom" number="29"/>
  <syscall name="accept" number="30"/>
  <syscall name="getpeername" number="31"/>
  <syscall name="getsockname" number="32"/>
  <syscall name="access" number="33"/>
  <syscall name="chflags" number="34"/>
  <syscall name="fchflags" number="35"/>
  <syscall name="sync" number="36"/>
  <syscall name="kill" number="37"/>
  <syscall name="compat_43_stat43" number="38"/>
  <syscall name="getppid" number="39"/>
  <syscall name="compat_43_lstat43" number="40"/>
  <syscall name="dup" number="41"/>
  <syscall name="pipe" number="42"/>
  <syscall name="getegid" number="43"/>
  <syscall name="profil" number="44"/>
  <syscall name="ktrace" number="45"/>
  <syscall name="compat_13_sigaction13" number="46"/>
  <syscall name="getgid" number="47"/>
  <syscall name="compat_13_sigprocmask13" number="48"/>
  <syscall name="__getlogin" number="49"/>
  <syscall name="__setlogin" number="50"/>
  <syscall name="acct" number="51"/>
  <syscall name="compat_13_sigpending13" number="52"/>
  <syscall name="compat_13_sigaltstack13" number="53"/>
  <syscall name="ioctl" number="54"/>
  <syscall name="compat_12_oreboot" number="55"/>
  <syscall name="revoke" number="56"/>
  <syscall name="symlink" number="57"/>
  <syscall name="readlink" number="58"/>
  <syscall name="execve" number="59"/>
  <syscall name="umask" number="60"/>
  <syscall name="chroot" number="61"/>
  <syscall name="compat_43_fstat43" number="62"/>
  <syscall name="compat_43_ogetkerninfo" number="63"/>
  <syscall name="compat_43_ogetpagesize" number="64"/>
  <syscall name="compat_12_msync" number="65"/>
  <syscall name="vfork" number="66"/>
  <syscall name="vread" number="67"/>
  <syscall name="vwrite" number="68"/>
  <syscall name="sbrk" number="69"/>
  <syscall name="sstk" number="70"/>
  <syscall name="compat_43_ommap" number="71"/>
  <syscall name="vadvise" number="72"/>
  <syscall name="munmap" number="73"/>
  <syscall name="mprotect" number="74"/>
  <syscall name="madvise" number="75"/>
  <syscall name="vhangup" number="76"/>
  <syscall name="vlimit" number="77"/>
  <syscall name="mincore" number="78"/>
  <syscall name="getgroups" number="79"/>
  <syscall name="setgroups" number="80"/>
  <syscall name="getpgrp" number="81"/>
  <syscall name="setpgid" number="82"/>
  <syscall name="compat_50_setitimer" number="83"/>
  <syscall name="compat_43_owait" number="84"/>
  <syscall name="compat_12_oswapon" number="85"/>
  <syscall name="compat_50_getitimer" number="86"/>
  <syscall name="compat_43_ogethostname" number="87"/>
  <syscall name="compat_43_osethostname" number="88"/>
  <syscall name="compat_43_ogetdtablesize" number="89"/>
  <syscall name="dup2" number="90"/>
  <syscall name="getrandom" number="91"/>
  <syscall name="fcntl" number="92"/>
  <syscall name="compat_50_select" number="93"/>
  <syscall name="fsync" number="95"/>
  <syscall name="setpriority" number="96"/>
  <syscall name="compat_30_socket" number="97"/>
  <syscall name="connect" number="98"/>
  <syscall name="compat_43_oaccept" number="99"/>
  <syscall name="getpriority" number="100"/>
  <syscall name="compat_43_osend" number="101"/>
  <syscall name="compat_43_orecv" number="102"/>
  <syscall name="compat_13_sigreturn13" number="103"/>
  <syscall name="bind" number="104"/>
  <syscall name="setsockopt" number="105"/>
  <syscall name="listen" number="106"/>
  <syscall name="vtimes" number="107"/>
  <syscall name="compat_43_osigvec" number="108"/>
  <syscall name="compat_43_osigblock" number="109"/>
  <syscall name="compat_43_osigsetmask" number="110"/>
  <syscall name="compat_13_sigsuspend13" number="111"/>
  <syscall name="compat_43_osigstack" number="112"/>
  <syscall name="compat_43_orecvmsg" number="113"/>
  <syscall name="compat_43_osendmsg" number="114"/>
  <syscall name="vtrace" number="115"/>
  <syscall name="compat_50_gettimeofday" number="116"/>
  <syscall name="compat_50_getrusage" number="117"/>
  <syscall name="getsockopt" number="118"/>
  <syscall name="resuba" number="119"/>
  <syscall name="readv" number="120"/>
  <syscall name="writev" number="121"/>
  <syscall name="compat_50_settimeofday" number="122"/>
  <syscall name="fchown" number="123"/>
  <syscall name="fchmod" number="124"/>
  <syscall name="compat_43_orecvfrom" number="125"/>
  <syscall name="setreuid" number="126"/>
  <syscall name="setregid" number="127"/>
  <syscall name="rename" number="128"/>
  <syscall name="compat_43_otruncate" number="129"/>
  <syscall name="compat_43_oftruncate" number="130"/>
  <syscall name="flock" number="131"/>
  <syscall name="mkfifo" number="132"/>
  <syscall name="sendto" number="133"/>
  <syscall name="shutdown" number="134"/>
  <syscall name="socketpair" number="135"/>
  <syscall name="mkdir" number="136"/>
  <syscall name="rmdir" number="137"/>
  <syscall name="compat_50_utimes" number="138"/>
  <syscall name="compat_50_adjtime" number="140"/>
  <syscall name="compat_43_ogetpeername" number="141"/>
  <syscall name="compat_43_ogethostid" number="142"/>
  <syscall name="compat_43_osethostid" number="143"/>
  <syscall name="compat_43_ogetrlimit" number="144"/>
  <syscall name="compat_43_osetrlimit" number="145"/>
  <syscall name="compat_43_okillpg" number="146"/>
  <syscall name="setsid" number="147"/>
  <syscall name="compat_50_quotactl" number="148"/>
  <syscall name="compat_43_oquota" number="149"/>
  <syscall name="compat_43_ogetsockname" number="150"/>
  <syscall name="nfssvc" number="155"/>
  <syscall name="compat_43_ogetdirentries" number="156"/>
  <syscall name="compat_20_statfs" number="157"/>
  <syscall name="compat_20_fstatfs" number="158"/>
  <syscall name="compat_30_getfh" number="161"/>
  <syscall name="compat_09_ogetdomainname" number="162"/>
  <syscall name="compat_09_osetdomainname" number="163"/>
  <syscall name="compat_09_ouname" number="164"/>
  <syscall name="sysarch" number="165"/>
  <syscall name="__futex" number="166"/>
  <syscall name="__futex_set_robust_list" number="167"/>
  <syscall name="__futex_get_robust_list" number="168"/>
  <syscall name="compat_10_osemsys" number="169"/>
  <syscall name="compat_10_omsgsys" number="170"/>
  <syscall name="compat_10_oshmsys" number="171"/>
  <syscall name="pread" number="173"/>
  <syscall name="pwrite" number="174"/>
  <syscall name="compat_30_ntp_gettime" number="175"/>
  <syscall name="ntp_adjtime" number="176"/>
  <syscall name="timerfd_create" number="177"/>
  <syscall name="timerfd_settime" number="178"/>
  <syscall name="timerfd_gettime" number="179"/>
  <syscall name="setgid" number="181"/>
  <syscall name="setegid" number="182"/>
  <syscall name="seteuid" number="183"/>
  <syscall name="lfs_bmapv" number="184"/>
  <syscall name="lfs_markv" number="185"/>
  <syscall name="lfs_segclean" number="186"/>
  <syscall name="compat_50_lfs_segwait" number="187"/>
  <syscall name="compat_12_stat12" number="188"/>
  <syscall name="compat_12_fstat12" number="189"/>
  <syscall name="compat_12_lstat12" number="190"/>
  <syscall name="pathconf" number="191"/>
  <syscall name="fpathconf" number="192"/>
  <syscall name="getsockopt2" number="193"/>
  <syscall name="getrlimit" number="194"/>
  <syscall name="setrlimit" number="195"/>
  <syscall name="compat_12_getdirentries" number="196"/>
  <syscall name="mmap" number="197"/>
  <syscall name="lseek" number="199"/>
  <syscall name="truncate" number="200"/>
  <syscall name="ftruncate" number="201"/>
  <syscall name="__sysctl" number="202"/>
  <syscall name="mlock" number="203"/>
  <syscall name="munlock" number="204"/>
  <syscall name="undelete" number="205"/>
  <syscall name="compat_50_futimes" number="206"/>
  <syscall name="getpgid" number="207"/>
  <syscall name="reboot" number="208"/>
  <syscall name="poll" number="209"/>
  <syscall name="afssys" number="210"/>
  <syscall name="compat_14___semctl" number="220"/>
  <syscall name="semget" number="221"/>
  <syscall name="semop" number="222"/>
  <syscall name="semconfig" number="223"/>
  <syscall name="compat_14_msgctl" number="224"/>
  <syscall name="msgget" number="225"/>
  <syscall name="msgsnd" number="226"/>
  <syscall name="msgrcv" number="227"/>
  <syscall name="shmat" number="228"/>
  <syscall name="compat_14_shmctl" number="229"/>
  <syscall name="shmdt" number="230"/>
  <syscall name="shmget" number="231"/>
  <syscall name="compat_50_clock_gettime" number="232"/>
  <syscall name="compat_50_clock_settime" number="233"/>
  <syscall name="compat_50_clock_getres" number="234"/>
  <syscall name="timer_create" number="235"/>
  <syscall name="timer_delete" number="236"/>
  <syscall name="compat_50_timer_settime" number="237"/>
  <syscall name="compat_50_timer_gettime" number="238"/>
  <syscall name="timer_getoverrun" number="239"/>
  <syscall name="compat_50_nanosleep" number="240"/>
  <syscall name="fdatasync" number="241"/>
  <syscall name="mlockall" number="242"/>
  <syscall name="munlockall" number="243"/>
  <syscall name="compat_50___sigtimedwait" number="244"/>
  <syscall name="sigqueueinfo" number="245"/>
  <syscall name="modctl" number="246"/>
  <syscall name="_ksem_init" number="247"/>
  <syscall name="_ksem_open" number="248"/>
  <syscall name="_ksem_unlink" number="249"/>
  <syscall name="_ksem_close" number="250"/>
  <syscall name="_ksem_post" number="251"/>
  <syscall name="_ksem_wait" number="252"/>
  <syscall name="_ksem_trywait" number="253"/>
  <syscall name="_ksem_getvalue" number="254"/>
  <syscall name="_ksem_destroy" number="255"/>
  <syscall name="_ksem_timedwait" number="256"/>
  <syscall name="mq_open" number="257"/>
  <syscall name="mq_close" number="258"/>
  <syscall name="mq_unlink" number="259"/>
  <syscall name="mq_getattr" number="260"/>
  <syscall name="mq_setattr" number="261"/>
  <syscall name="mq_notify" number="262"/>
  <syscall name="mq_send" number="263"/>
  <syscall name="mq_receive" number="264"/>
  <syscall name="compat_50_mq_timedsend" number="265"/>
  <syscall name="compat_50_mq_timedreceive" number="266"/>
  <syscall name="eventfd" number="267"/>
  <syscall name="__posix_rename" number="270"/>
  <syscall name="swapctl" number="271"/>
  <syscall name="compat_30_getdents" number="272"/>
  <syscall name="minherit" number="273"/>
  <syscall name="lchmod" number="274"/>
  <syscall name="lchown" number="275"/>
  <syscall name="compat_50_lutimes" number="276"/>
  <syscall name="__msync13" number="277"/>
  <syscall name="compat_30___stat13" number="278"/>
  <syscall name="compat_30___fstat13" number="279"/>
  <syscall name="compat_30___lstat13" number="280"/>
  <syscall name="__sigaltstack14" number="281"/>
  <syscall name="__vfork14" number="282"/>
  <syscall name="__posix_chown" number="283"/>
  <syscall name="__posix_fchown" number="284"/>
  <syscall name="__posix_lchown" number="285"/>
  <syscall name="getsid" number="286"/>
  <syscall name="__clone" number="287"/>
  <syscall name="fktrace" number="288"/>
  <syscall name="preadv" number="289"/>
  <syscall name="pwritev" number="290"/>
  <syscall name="compat_16___sigaction14" number="291"/>
  <syscall name="__sigpending14" number="292"/>
  <syscall name="__sigprocmask14" number="293"/>
  <syscall name="__sigsuspend14" number="294"/>
  <syscall name="compat_16___sigreturn14" number="295"/>
  <syscall name="__getcwd" number="296"/>
  <syscall name="fchroot" number="297"/>
  <syscall name="compat_30_fhopen" number="298"/>
  <syscall name="compat_30_fhstat" number="299"/>
  <syscall name="compat_20_fhstatfs" number="300"/>
  <syscall name="compat_50_____semctl13" number="301"/>
  <syscall name="compat_50___msgctl13" number="302"/>
  <syscall name="compat_50___shmctl13" number="303"/>
  <syscall name="lchflags" number="304"/>
  <syscall name="issetugid" number="305"/>
  <syscall name="utrace" number="306"/>
  <syscall name="getcontext" number="307"/>
  <syscall name="setcontext" number="308"/>
  <syscall name="_lwp_create" number="309"/>
  <syscall name="_lwp_exit" number="310"/>
  <syscall name="_lwp_self" number="311"/>
  <syscall name="_lwp_wait" number="312"/>
  <syscall name="_lwp_suspend" number="313"/>
  <syscall name="_lwp_continue" number="314"/>
  <syscall name="_lwp_wakeup" number="315"/>
  <syscall name="_lwp_getprivate" number="316"/>
  <syscall name="_lwp_setprivate" number="317"/>
  <syscall name="_lwp_kill" number="318"/>
  <syscall name="_lwp_detach" number="319"/>
  <syscall name="compat_50__lwp_park" number="320"/>
  <syscall name="_lwp_unpark" number="321"/>
  <syscall name="_lwp_unpark_all" number="322"/>
  <syscall name="_lwp_setname" number="323"/>
  <syscall name="_lwp_getname" number="324"/>
  <syscall name="_lwp_ctl" number="325"/>
  <syscall name="compat_60_sa_register" number="330"/>
  <syscall name="compat_60_sa_stacks" number="331"/>
  <syscall name="compat_60_sa_enable" number="332"/>
  <syscall name="compat_60_sa_setconcurrency" number="333"/>
  <syscall name="compat_60_sa_yield" number="334"/>
  <syscall name="compat_60_sa_preempt" number="335"/>
  <syscall name="sys_sa_unblockyield" number="336"/>
  <syscall name="__sigaction_sigtramp" number="340"/>
  <syscall name="sys_pmc_get_info" number="341"/>
  <syscall name="sys_pmc_control" number="342"/>
  <syscall name="rasctl" number="343"/>
  <syscall name="kqueue" number="344"/>
  <syscall name="compat_50_kevent" number="345"/>
  <syscall name="_sched_setparam" number="346"/>
  <syscall name="_sched_getparam" number="347"/>
  <syscall name="_sched_setaffinity" number="348"/>
  <syscall name="_sched_getaffinity" number="349"/>
  <syscall name="sched_yield" number="350"/>
  <syscall name="_sched_protect" number="351"/>
  <syscall name="fsync_range" number="354"/>
  <syscall name="uuidgen" number="355"/>
  <syscall name="compat_90_getvfsstat" number="356"/>
  <syscall name="compat_90_statvfs1" number="357"/>
  <syscall name="compat_90_fstatvfs1" number="358"/>
  <syscall name="compat_30_fhstatvfs1" number="359"/>
  <syscall name="extattrctl" number="360"/>
  <syscall name="extattr_set_file" number="361"/>
  <syscall name="extattr_get_file" number="362"/>
  <syscall name="extattr_delete_file" number="363"/>
  <syscall name="extattr_set_fd" number="364"/>
  <syscall name="extattr_get_fd" number="365"/>
  <syscall name="extattr_delete_fd" number="366"/>
  <syscall name="extattr_set_link" number="367"/>
  <syscall name="extattr_get_link" number="368"/>
  <syscall name="extattr_delete_link" number="369"/>
  <syscall name="extattr_list_fd" number="370"/>
  <syscall name="extattr_list_file" number="371"/>
  <syscall name="extattr_list_link" number="372"/>
  <syscall name="compat_50_pselect" number="373"/>
  <syscall name="compat_50_pollts" number="374"/>
  <syscall name="setxattr" number="375"/>
  <syscall name="lsetxattr" number="376"/>
  <syscall name="fsetxattr" number="377"/>
  <syscall name="getxattr" number="378"/>
  <syscall name="lgetxattr" number="379"/>
  <syscall name="fgetxattr" number="380"/>
  <syscall name="listxattr" number="381"/>
  <syscall name="llistxattr" number="382"/>
  <syscall name="flistxattr" number="383"/>
  <syscall name="removexattr" number="384"/>
  <syscall name="lremovexattr" number="385"/>
  <syscall name="fremovexattr" number="386"/>
  <syscall name="compat_50___stat30" number="387"/>
  <syscall name="compat_50___fstat30" number="388"/>
  <syscall name="compat_50___lstat30" number="389"/>
  <syscall name="__getdents30" number="390"/>
  <syscall name="compat_30___fhstat30" number="392"/>
  <syscall name="compat_50___ntp_gettime30" number="393"/>
  <syscall name="__socket30" number="394"/>
  <syscall name="__getfh30" number="395"/>
  <syscall name="__fhopen40" number="396"/>
  <syscall name="compat_90_fhstatvfs1" number="397"/>
  <syscall name="compat_50___fhstat40" number="398"/>
  <syscall name="aio_cancel" number="399"/>
  <syscall name="aio_error" number="400"/>
  <syscall name="aio_fsync" number="401"/>
  <syscall name="aio_read" number="402"/>
  <syscall name="aio_return" number="403"/>
  <syscall name="compat_50_aio_suspend" number="404"/>
  <syscall name="aio_write" number="405"/>
  <syscall name="lio_listio" number="406"/>
  <syscall name="__mount50" number="410"/>
  <syscall name="mremap" number="411"/>
  <syscall name="pset_create" number="412"/>
  <syscall name="pset_destroy" number="413"/>
  <syscall name="pset_assign" number="414"/>
  <syscall name="_pset_bind" number="415"/>
  <syscall name="__posix_fadvise50" number="416"/>
  <syscall name="__select50" number="417"/>
  <syscall name="__gettimeofday50" number="418"/>
  <syscall name="__settimeofday50" number="419"/>
  <syscall name="__utimes50" number="420"/>
  <syscall name="__adjtime50" number="421"/>
  <syscall name="__lfs_segwait50" number="422"/>
  <syscall name="__futimes50" number="423"/>
  <syscall name="__lutimes50" number="424"/>
  <syscall name="__setitimer50" number="425"/>
  <syscall name="__getitimer50" number="426"/>
  <syscall name="__clock_gettime50" number="427"/>
  <syscall name="__clock_settime50" number="428"/>
  <syscall name="__clock_getres50" number="429"/>
  <syscall name="__nanosleep50" number="430"/>
  <syscall name="____sigtimedwait50" number="431"/>
  <syscall name="__mq_timedsend50" number="432"/>
  <syscall name="__mq_timedreceive50" number="433"/>
  <syscall name="compat_60__lwp_park" number="434"/>
  <syscall name="compat_100___kevent50" number="435"/>
  <syscall name="__pselect50" number="436"/>
  <syscall name="__pollts50" number="437"/>
  <syscall name="__aio_suspend50" number="438"/>
  <syscall name="__stat50" number="439"/>
  <syscall name="__fstat50" number="440"/>
  <syscall name="__lstat50" number="441"/>
  <syscall name="____semctl50" number="442"/>
  <syscall name="__shmctl50" number="443"/>
  <syscall name="__msgctl50" number="444"/>
  <syscall name="__getrusage50" number="445"/>
  <syscall name="__timer_settime50" number="446"/>
  <syscall name="__timer_gettime50" number="447"/>
  <syscall name="__ntp_gettime50" number="448"/>
  <syscall name="__wait450" number="449"/>
  <syscall name="__mknod50" number="450"/>
  <syscall name="__fhstat50" number="451"/>
  <syscall name="pipe2" number="453"/>
  <syscall name="dup3" number="454"/>
  <syscall name="kqueue1" number="455"/>
  <syscall name="paccept" number="456"/>
  <syscall name="linkat" number="457"/>
  <syscall name="renameat" number="458"/>
  <syscall name="mkfifoat" number="459"/>
  <syscall name="mknodat" number="460"/>
  <syscall name="mkdirat" number="461"/>
  <syscall name="faccessat" number="462"/>
  <syscall name="fchmodat" number="463"/>
  <syscall name="fchownat" number="464"/>
  <syscall name="fexecve" number="465"/>
  <syscall name="fstatat" number="466"/>
  <syscall name="utimensat" number="467"/>
  <syscall name="openat" number="468"/>
  <syscall name="readlinkat" number="469"/>
  <syscall name="symlinkat" number="470"/>
  <syscall name="unlinkat" number="471"/>
  <syscall name="futimens" number="472"/>
  <syscall name="__quotactl" number="473"/>
  <syscall name="posix_spawn" number="474"/>
  <syscall name="recvmmsg" number="475"/>
  <syscall name="sendmmsg" number="476"/>
  <syscall name="clock_nanosleep" number="477"/>
  <syscall name="___lwp_park60" number="478"/>
  <syscall name="posix_fallocate" number="479"/>
  <syscall name="fdiscard" number="480"/>
  <syscall name="wait6" number="481"/>
  <syscall name="clock_getcpuclockid2" number="482"/>
  <syscall name="__getvfsstat90" number="483"/>
  <syscall name="__statvfs190" number="484"/>
  <syscall name="__fstatvfs190" number="485"/>
  <syscall name="__fhstatvfs190" number="486"/>
  <syscall name="__acl_get_link" number="487"/>
  <syscall name="__acl_set_link" number="488"/>
  <syscall name="__acl_delete_link" number="489"/>
  <syscall name="__acl_aclcheck_link" number="490"/>
  <syscall name="__acl_get_file" number="491"/>
  <syscall name="__acl_set_file" number="492"/>
  <syscall name="__acl_get_fd" number="493"/>
  <syscall name="__acl_set_fd" number="494"/>
  <syscall name="__acl_delete_file" number="495"/>
  <syscall name="__acl_delete_fd" number="496"/>
  <syscall name="__acl_aclcheck_file" number="497"/>
  <syscall name="__acl_aclcheck_fd" number="498"/>
  <syscall name="lpathconf" number="499"/>
  <syscall name="memfd_create" number="500"/>
  <syscall name="__kevent100" number="501"/>
  <syscall name="epoll_create1" number="502"/>
  <syscall name="epoll_ctl" number="503"/>
  <syscall name="epoll_pwait2" number="504"/>
</syscalls_info>
