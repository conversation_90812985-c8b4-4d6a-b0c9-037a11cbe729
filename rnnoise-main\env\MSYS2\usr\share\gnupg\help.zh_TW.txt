# help.zh_TW.txt - zh_TW GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
#
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


# Note that this help file needs to be UTF-8 encoded.  When looking
# for a help item, GnuPG scans the help files in the following order
# (assuming a GNU or Unix system):
#
#    /etc/gnupg/help.LL_TT.txt
#    /etc/gnupg/help.LL.txt
#    /etc/gnupg/help.txt
#    /usr/share/gnupg/help.LL_TT.txt
#    /usr/share/gnupg/help.LL.txt
#    /usr/share/gnupg/help.txt
#
# Here LL_TT denotes the full name of the current locale with the
# territory (.e.g. "de_DE"), LL denotes just the locale name
# (e.g. "de").  The first matching item is returned.  To put a dot or
# a hash mark at the beginning of a help text line, it needs to be
# prefixed with ". ".  A single dot may be used to terminated ahelp
# entry.

.#pinentry.qualitybar.tooltip
# [remove the hash mark from the key to enable this text]
# This entry is just an example on how to customize the tooltip shown
# when hovering over the quality bar of the pinentry.  We don't
# install this text so that the hardcoded translation takes
# precedence.  An administrator should write up a short help to tell
# the users about the configured passphrase constraints and save that
# to /etc/gnupg/help.txt.  The help text should not be longer than
# about 800 characters.
本指示條顯示上方輸入的密語的強度。

本處若顯示為紅色，代表該密語強度不足，不能被 GnuPG 接受。請與管理者詢
問關於密語強度的細節。
.


.gnupg.agent-problem
# There was a problem accessing or starting the agent.
無法連接執行中的 Gpg-Agent，或與執行中的 Gpg-Agent 通訊時出現異常。

本系統使用稱為 Gpg-Agent 的背景程式，以代理處理私鑰或詢問密文。該代理
程式會在使用者登入時執行，並持續執行直到使用者登出為止。若沒有代理程
式，系統會在執行時嘗試使用一個功能受限的代理程式，該版本的代理程式可能
會導致一些問題。

您可能需要詢問管理者如何解決這個問題，作為臨時方案，您可以嘗試登出後重
新登入，並檢查問題是否能改善。即使以上方法有用，也請告知系統管理者，因
為這代表軟體可能存在瑕疵。
.


.gnupg.dirmngr-problem
# There was a problen accessing the dirmngr.
無法連接到執行中的 Dirmngr，或與執行中的 Dirmngr 通訊時出現異常。

為了查詢憑證吊銷列表 (CRL)、執行 OCSP 金鑰驗證、與 LDAP 伺服器查詢金
鑰，本系統會使用一個外部工具稱為 Dirmngr。Dirmngr 通常會以系統服務（常
駐程式）的形式執行，使用者無須注意。若此處出現問題，作為臨時方案，系統
每次在接到請求時會執行內建版本的 Dirmngr，因而導致效能上的限制。

若您遇上這個問題，您可以與系統管理者詢問如何處理。作為暫代方案，您可以
關閉在 gpgsm 的設定中關閉 CRL 的檢查。
.


.gpg.edit_ownertrust.value
# The help identies prefixed with "gpg." used to be hard coded in gpg
# but may now be overridden by help texts from this file.
您可以自由決定是否要設定本值，該值不會輸出到任何第三方。本值只是為了實
作信任網路而設，與（隱式建立的）憑證網路沒有關係。
.

.gpg.edit_ownertrust.set_ultimate.okay
要建構信任網絡, GnuPG 需要知道哪些是徹底信任的金鑰——通常是指您可以存取
到私鑰的金鑰。若要設定為徹底信任的金鑰，請回答 "yes"。


.gpg.untrusted_key.override
如果您仍要使用這把不受信任的金鑰, 請回答 "yes"。
.

.gpg.pklist.user_id.enter
請輸入本訊息接收者的使用者 ID。
.

.gpg.keygen.algo
請選擇要使用的金鑰演算法。

DSA（又稱 DSS）是數位簽章演算法 (Digital Signature Algorithm)，
只能用於簽署。

Elgamal 是只能用於加密的演算法。

RSA 可以作為簽署及加密使用。

第一把（主要的）金鑰必須要是能用於簽署的金鑰。
.

.gpg.keygen.algo.rsa_se
通常來說用同一把金鑰簽署及加密並不是個好主意。這個演算法應該只能用於特
定的領域。請先聯絡您的安全專家。
.

.gpg.keygen.cardkey
請從本卡片選擇要使用的金鑰。

本清單依序顯示選擇索引、鑰柄（十六進位字串）、卡片特定的金鑰參照、該
金鑰的演算法，括號內會顯示金鑰的用途（cert 證書、sign 簽名、auth 認證
、encr 加密）。若能得知預設的金鑰用途，會以星號標示。
.

.gpg.keygen.flags
切換金鑰的功能。

金鑰可以擁有哪些功能，受限於使用的金鑰演算法。

若要快速設定金鑰的功能，您可以輸入 '=' 後接以下字元組合的字串：
's' 簽名、'e' 加密、'a' 認證。無效的字元跟無法設定的功能會被忽略。本子
選單會在使用此快速設定的方法後立即關閉。
.


.gpg.keygen.size
請輸入金鑰的大小。

建議的金鑰大小通常是良好的選擇。

若您要使用較大的金鑰大小，例如 4096 位元，請再三考慮是否合理。
您可以參考以下網址：https://www.xkcd.com/538/。
.

.gpg.keygen.size.huge.okay
請回答 "yes" 或 "no"。
.


.gpg.keygen.size.large.okay
請回答 "yes" 或 "no"。
.


.gpg.keygen.valid
請輸入提示裡要求的值。
輸入 ISO 日期格式 (YYYY-MM-DD) 雖然會被接受，但是因為系統會解析成區
間，因此不會得到正常的錯誤回應。

.

.gpg.keygen.valid.okay
請回答 "yes" 或 "no"。
.


.gpg.keygen.name
請輸入金鑰持有人的名字。
字元 "<" 跟 ">" 不會被接受。
例：Heinrich Heine
.


.gpg.keygen.email
請輸入 Email 地址（非必要，但強烈建議使用）。
例：<EMAIL>
.

.gpg.keygen.comment
請輸入註釋（非必要）。
字元 "(" 跟 ")" 不會被接受。
通常不需要寫入註釋。
.


.gpg.keygen.userid.cmd
# (Keep a leading empty line)

N  修改姓名。
C  修改註釋。
E  修改 Email 地址。
O  繼續產生金鑰。
Q  中止產生金鑰。
.

.gpg.keygen.sub.okay
如果要產生子鑰的話, 請回答 "yes" (或 "y")。
.

.gpg.sign_uid.okay
請回答 "yes" 或 "no"。
.

.gpg.sign_uid.class
當您在某把金鑰上簽署某個使用者 ID，您首先必須先驗證那把金鑰確實屬於該使用者
ID 上所代表的人。可以讓其他人知道您驗證該金鑰的詳細程度。

"0" 表示您不特別聲明您驗證那把金鑰的詳細程度。

"1" 表示您相信這把金鑰屬於那個主張是主人的人，但是您不能，或是完全沒有驗證
    那把金鑰。這對「人格上」的驗證，亦即簽署化名使用者的金鑰來說是有用的。

"2" 表示您做了快速的金鑰驗證。例如，這能表示您驗證了這把金鑰的指紋並將使用
    者 ID 與相片證件對照。

"3" 表示您做了仔細的金鑰驗證。例如，這能表示您當面向金鑰持有人驗證了金鑰指
    紋，並透過附帶照片而難以偽造的文件（像是護照）確認了金鑰持有人的姓名與
    金鑰上的使用者 ID 一致，最後您（透過 Email 往來）驗證了金鑰上的 Email
    位址確實屬於金鑰持有人。

請注意上述關於等級 2 和 3 的範例 *只是* 範例而已。到頭來還是得由您來決定當
您簽署其他的金鑰時，「快速」與「仔細」的意義。

若您不知道正確回答為何，請回答 "0"。
.

.gpg.change_passwd.empty.okay
請回答 "yes" 或 "no"。
.


.gpg.keyedit.save.okay
請回答 "yes" 或 "no"。
.


.gpg.keyedit.cancel.okay
請回答 "yes" 或 "no"。
.

.gpg.keyedit.sign_all.okay
如果您想要簽署 *所有* 使用者 ID 的話請回答 "yes"。
.

.gpg.keyedit.remove.uid.okay
如果您真的想要刪除這個使用者 ID 的話請回答 "yes"。
所有的憑證也會因此刪除！
.

.gpg.keyedit.remove.subkey.okay
如果要刪除這把子鑰的話請回答 "yes"。
.

.gpg.keyedit.delsig.valid
這是一份在這把金鑰上有效的簽章；通常您不會想要刪除這份簽章，因為
建立與該金鑰（或與由該金鑰信任的其他金鑰）的信任連結，會是一件重
要的事情。
.

.gpg.keyedit.delsig.unknown
因為您沒有持有對應的金鑰，這份簽章無法被檢驗。

您應該在知道哪一把金鑰被用來簽署前，暫緩刪除簽章的動作；因為這把
來簽署的金鑰可能透過其他已經驗證的金鑰建立了信任連結。
.

.gpg.keyedit.delsig.invalid
這份簽章無效。把它從您的鑰匙圈裡移除是合理的。
.

.gpg.keyedit.delsig.selfsig
這是一份和這個金鑰使用者 ID 相繫的簽章。通常把這樣的簽章移除不會
是個好主意。實際上 GnuPG 可能從此就不能再使用這把金鑰了。所以只
有在這把金鑰的第一個自我簽章因某些原因無效，而第二個還可用的情況
下才這麼做。
.

.gpg.keyedit.updpref.okay
變更所有 (或只有被選取的那幾個) 使用者 ID 的偏好成現用的偏好清單。
所有受到影響的自我簽章的時間戳記都會增加一秒鐘。
.


.gpg.passphrase.enter
# (keep a leading empty line)

請輸入密語; 密語代表為一個秘密的句子。
.


.gpg.passphrase.repeat
請重新輸入先前的密語, 以確定您到底輸入了什麼。
.

.gpg.detached_signature.filename
請給定簽章所要套用的檔案名稱。
.

.gpg.openfile.overwrite.okay
# openfile.c (overwrite_filep)
若要覆寫這個檔案的話，請回答 "yes"。
.

.gpg.openfile.askoutname
# openfile.c (ask_outfile_name)
請輸入一個新的檔名。若直接按下 Enter 則會使用預設的檔案 (顯示在括號
中)。
.

.gpg.ask_revocation_reason.code
# revoke.c (ask_revocation_reason)
您應該為這份憑證指定一個原因。根據情境不同，您能夠從這個清單中選擇一
個：
  「金鑰已經被洩漏了」
      使用此選項，若您相信有某個未經許可的人取得了您的私鑰。
  「金鑰被代換了」
      使用此選項，如果您把您的金鑰換成新的了。
  「金鑰不再被使用了」
      使用此選項，如果您已經撤回了這把金鑰。
  「使用者 ID 不再有效了」
      使用此選項，如果這個使用者 ID 已不再使用；這通常用來表示某
      個 Email 地址已經無效。

.

.gpg.ask_revocation_reason.text
# revoke.c (ask_revocation_reason)
您也可以輸入文字來描述為甚麼發佈這份撤銷憑證的理由。請讓這段文字
保持簡明扼要。
請輸入一個空行以結束文字。
.

.gpg.tofu.conflict
# tofu.c
TOFU 偵測到其他的金鑰持有一樣（或是非常類似）的 Email 地址。可能是該使
用者建立了新的金鑰，若為如此，您可以安全地信任該金鑰（但請與該使用者確
認）。但也有可能是偽造的金鑰，或是有中間人攻擊 (MitM) 正在進行中，若為
如此，您應該要標記該不良金鑰，該金鑰將會不受信任。標記一個金鑰為「不受
信任」代表該金鑰的簽名也會被當成不良簽章，且使用該金鑰加密也會出現警
告。若您無法確定，並且不能檢查該金鑰，您應選擇「接受一次」或「拒絕一
次」。
.

.gpgsm.root-cert-not-trusted
# This text gets displayed by the audit log if
# a root certificates was not trusted.
根憑證（信任的基點）不受信任。根據設定，您可能已被要求需要設定根憑證
為信任憑證，或是您需要手動告知 GnuPG 信任該憑證。信任憑證的設定在
GnuPG 的家目錄下的 trustlist.txt。若有疑慮，請詢問系統管理者是否該信
任此憑證。


.gpgsm.crl-problem
# This text is displayed by the audit log for problems with
# the CRL or OCSP checking.
根據您的設定，在取得憑證吊銷列表 (CRL) 或是透過 OCSP 檢查憑證狀態時
出現問題。該問題的產生有很多因素，請詳閱說明文件以找出可能解法。


# Local variables:
# mode: default-generic
# coding: utf-8
# End:
