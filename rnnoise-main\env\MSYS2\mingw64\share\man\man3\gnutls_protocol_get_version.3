.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_protocol_get_version" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_protocol_get_version \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_protocol_t gnutls_protocol_get_version(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
Get TLS version, a \fBgnutls_protocol_t\fP value.
.SH "RETURNS"
The version of the currently used protocol.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
