.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_equals2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_equals2 \- This function compares a gnutls_x509_crt_t cert with DER data
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "unsigned gnutls_x509_crt_equals2(gnutls_x509_crt_t " cert1 ", const gnutls_datum_t * " der ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert1" 12
The first certificate
.IP "const gnutls_datum_t * der" 12
A DER encoded certificate
.SH "DESCRIPTION"
This function will compare an X.509 certificate structures, with DER
encoded certificate data.
.SH "RETURNS"
On equality non\-zero is returned, otherwise zero.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
