set ::msgcat::header "Project-Id-Version: gitk\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2016-05-06 15:35+0000\nLast-Translator: Vasco Almeida <<EMAIL>>\nLanguage-Team: Portuguese\nLanguage: pt\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Virtaal 0.7.1\n"
::msgcat::mcset pt_pt "Couldn't get list of unmerged files:" "N\u00e3o foi poss\u00edvel obter lista de ficheiros n\u00e3o integrados:"
::msgcat::mcset pt_pt "Color words" "Colorir palavras"
::msgcat::mcset pt_pt "Markup words" "Marcar palavras"
::msgcat::mcset pt_pt "Error parsing revisions:" "Erro ao analisar revis\u00f5es:"
::msgcat::mcset pt_pt "Error executing --argscmd command:" "Erro ao executar o comando de --argscmd:"
::msgcat::mcset pt_pt "No files selected: --merge specified but no files are unmerged." "Nenhum ficheiro selecionado: --merge especificado mas n\u00e3o h\u00e1 ficheiros por integrar."
::msgcat::mcset pt_pt "No files selected: --merge specified but no unmerged files are within file limit." "Nenhum ficheiro selecionado: --merge especificado mas n\u00e3o h\u00e1 ficheiros por integrar ao n\u00edvel de ficheiro."
::msgcat::mcset pt_pt "Error executing git log:" "Erro ao executar git log:"
::msgcat::mcset pt_pt "Reading" "A ler"
::msgcat::mcset pt_pt "Reading commits..." "A ler commits..."
::msgcat::mcset pt_pt "No commits selected" "Nenhum commit selecionado"
::msgcat::mcset pt_pt "Command line" "Linha de comandos"
::msgcat::mcset pt_pt "Can't parse git log output:" "N\u00e3o \u00e9 poss\u00edvel analisar a sa\u00edda de git log:"
::msgcat::mcset pt_pt "No commit information available" "N\u00e3o h\u00e1 informa\u00e7\u00e3o dispon\u00edvel sobre o commit"
::msgcat::mcset pt_pt "OK" "OK"
::msgcat::mcset pt_pt "Cancel" "Cancelar"
::msgcat::mcset pt_pt "&Update" "At&ualizar"
::msgcat::mcset pt_pt "&Reload" "&Recarregar"
::msgcat::mcset pt_pt "Reread re&ferences" "Reler re&fer\u00eancias"
::msgcat::mcset pt_pt "&List references" "&Listar refer\u00eancias"
::msgcat::mcset pt_pt "Start git &gui" "Iniciar git &gui"
::msgcat::mcset pt_pt "&Quit" "&Sair"
::msgcat::mcset pt_pt "&File" "&Ficheiro"
::msgcat::mcset pt_pt "&Preferences" "&Prefer\u00eancias"
::msgcat::mcset pt_pt "&Edit" "&Editar"
::msgcat::mcset pt_pt "&New view..." "&Nova vista..."
::msgcat::mcset pt_pt "&Edit view..." "&Editar vista..."
::msgcat::mcset pt_pt "&Delete view" "Elimina&r vista"
::msgcat::mcset pt_pt "&All files" "&Todos os ficheiros"
::msgcat::mcset pt_pt "&View" "&Ver"
::msgcat::mcset pt_pt "&About gitk" "&Sobre gitk"
::msgcat::mcset pt_pt "&Key bindings" "&Atalhos"
::msgcat::mcset pt_pt "&Help" "&Ajuda"
::msgcat::mcset pt_pt "SHA1 ID:" "ID SHA1:"
::msgcat::mcset pt_pt "Row" "Linha"
::msgcat::mcset pt_pt "Find" "Procurar"
::msgcat::mcset pt_pt "commit" "commit"
::msgcat::mcset pt_pt "containing:" "contendo:"
::msgcat::mcset pt_pt "touching paths:" "altera os caminhos:"
::msgcat::mcset pt_pt "adding/removing string:" "adiciona/remove a cadeia:"
::msgcat::mcset pt_pt "changing lines matching:" "altera linhas com:"
::msgcat::mcset pt_pt "Exact" "Exato"
::msgcat::mcset pt_pt "IgnCase" "IgnMai\u00fasculas"
::msgcat::mcset pt_pt "Regexp" "Expr. regular"
::msgcat::mcset pt_pt "All fields" "Todos os campos"
::msgcat::mcset pt_pt "Headline" "Cabe\u00e7alho"
::msgcat::mcset pt_pt "Comments" "Coment\u00e1rios"
::msgcat::mcset pt_pt "Author" "Autor"
::msgcat::mcset pt_pt "Committer" "Committer"
::msgcat::mcset pt_pt "Search" "Pesquisar"
::msgcat::mcset pt_pt "Diff" "Diff"
::msgcat::mcset pt_pt "Old version" "Vers\u00e3o antiga"
::msgcat::mcset pt_pt "New version" "Vers\u00e3o nova"
::msgcat::mcset pt_pt "Lines of context" "Linhas de contexto"
::msgcat::mcset pt_pt "Ignore space change" "Ignorar espa\u00e7os"
::msgcat::mcset pt_pt "Line diff" "Diff de linha"
::msgcat::mcset pt_pt "Patch" "Patch"
::msgcat::mcset pt_pt "Tree" "\u00c1rvore"
::msgcat::mcset pt_pt "Diff this -> selected" "Diff este -> sele\u00e7\u00e3o"
::msgcat::mcset pt_pt "Diff selected -> this" "Diff sele\u00e7\u00e3o -> este"
::msgcat::mcset pt_pt "Make patch" "Gerar patch"
::msgcat::mcset pt_pt "Create tag" "Criar tag"
::msgcat::mcset pt_pt "Copy commit summary" "Copiar sum\u00e1rio do commit"
::msgcat::mcset pt_pt "Write commit to file" "Escrever commit num ficheiro"
::msgcat::mcset pt_pt "Create new branch" "Criar novo ramo"
::msgcat::mcset pt_pt "Cherry-pick this commit" "Efetuar cherry-pick deste commit"
::msgcat::mcset pt_pt "Reset HEAD branch to here" "Repor ramo HEAD para aqui"
::msgcat::mcset pt_pt "Mark this commit" "Marcar este commit"
::msgcat::mcset pt_pt "Return to mark" "Voltar \u00e0 marca"
::msgcat::mcset pt_pt "Find descendant of this and mark" "Encontrar descendeste deste e da marca"
::msgcat::mcset pt_pt "Compare with marked commit" "Comparar com o commit marcado"
::msgcat::mcset pt_pt "Diff this -> marked commit" "Diff este -> commit marcado"
::msgcat::mcset pt_pt "Diff marked commit -> this" "Diff commit marcado -> este"
::msgcat::mcset pt_pt "Revert this commit" "Reverter este commit"
::msgcat::mcset pt_pt "Check out this branch" "Extrair este ramo"
::msgcat::mcset pt_pt "Remove this branch" "Remover este ramo"
::msgcat::mcset pt_pt "Copy branch name" "Copiar nome do ramo"
::msgcat::mcset pt_pt "Highlight this too" "Real\u00e7ar este tamb\u00e9m"
::msgcat::mcset pt_pt "Highlight this only" "Real\u00e7ar apenas este"
::msgcat::mcset pt_pt "External diff" "Diff externo"
::msgcat::mcset pt_pt "Blame parent commit" "Culpar commit pai"
::msgcat::mcset pt_pt "Copy path" "Copiar caminho"
::msgcat::mcset pt_pt "Show origin of this line" "Mostrar origem deste ficheiro"
::msgcat::mcset pt_pt "Run git gui blame on this line" "Executar git gui blame sobre esta linha"
::msgcat::mcset pt_pt "About gitk" "Sobre gitk"
::msgcat::mcset pt_pt "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk - um visualizador de commits do git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse e redistribua sob os termos da GNU General Public License"
::msgcat::mcset pt_pt "Close" "Fechar"
::msgcat::mcset pt_pt "Gitk key bindings" "Atalhos do gitk"
::msgcat::mcset pt_pt "Gitk key bindings:" "Atalhos do gitk:"
::msgcat::mcset pt_pt "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Sair"
::msgcat::mcset pt_pt "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009Fechar janela"
::msgcat::mcset pt_pt "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009Mover para o primeiro commit"
::msgcat::mcset pt_pt "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009Mover para o \u00faltimo commit"
::msgcat::mcset pt_pt "<Up>, p, k\u0009Move up one commit" "<Cima>, p, k\u0009Mover para o commit acima"
::msgcat::mcset pt_pt "<Down>, n, j\u0009Move down one commit" "<Baixo>, n, j\u0009Mover para o commit abaixo"
::msgcat::mcset pt_pt "<Left>, z, h\u0009Go back in history list" "<Esquerda>, z, h\u0009Recuar no hist\u00f3rico"
::msgcat::mcset pt_pt "<Right>, x, l\u0009Go forward in history list" "<Direita>, x, l\u0009Avan\u00e7ar no hist\u00f3rico"
::msgcat::mcset pt_pt "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n>\u0009Ir para o n-\u00e9simo pai do commit atual no hist\u00f3rico"
::msgcat::mcset pt_pt "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009Mover a lista de commits uma p\u00e1gina para cima"
::msgcat::mcset pt_pt "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009Mover a lista de commits uma p\u00e1gina para baixo"
::msgcat::mcset pt_pt "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009Deslocar para o topo da lista"
::msgcat::mcset pt_pt "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009Deslocar para o fim da lista"
::msgcat::mcset pt_pt "<%s-Up>\u0009Scroll commit list up one line" "<%s-Cima>\u0009Deslocar a lista de commits uma linha para cima"
::msgcat::mcset pt_pt "<%s-Down>\u0009Scroll commit list down one line" "<%s-Baixo>\u0009Deslocar a lista de commits uma linha para baixo"
::msgcat::mcset pt_pt "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009Deslocar a lista de commits uma p\u00e1gina para cima"
::msgcat::mcset pt_pt "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009Deslocar a lista de commits uma p\u00e1gina para baixo"
::msgcat::mcset pt_pt "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Cima>\u0009Procurar para tr\u00e1s (para cima, commits posteriores)"
::msgcat::mcset pt_pt "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Baixo>\u0009Procurar para a frente (para baixo, commits anteriores)"
::msgcat::mcset pt_pt "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009Deslocar vista diff uma p\u00e1gina para cima"
::msgcat::mcset pt_pt "<Backspace>\u0009Scroll diff view up one page" "<Retrocesso>\u0009Deslocar vista diff uma p\u00e1gina para cima"
::msgcat::mcset pt_pt "<Space>\u0009\u0009Scroll diff view down one page" "<Espa\u00e7o>\u0009Deslocar vista diff uma p\u00e1gina para baixo"
::msgcat::mcset pt_pt "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009Deslocar vista diff 18 linhas para cima"
::msgcat::mcset pt_pt "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009Deslocar vista diff 18 linhas para baixo"
::msgcat::mcset pt_pt "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009Procurar"
::msgcat::mcset pt_pt "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009Mover para a ocorr\u00eancia seguinte"
::msgcat::mcset pt_pt "<Return>\u0009Move to next find hit" "<Return>\u0009Mover para a ocorr\u00eancia seguinte"
::msgcat::mcset pt_pt "g\u0009\u0009Go to commit" "g\u0009\u0009Ir para o commit"
::msgcat::mcset pt_pt "/\u0009\u0009Focus the search box" "/\u0009\u0009Focar a caixa de pesquisa"
::msgcat::mcset pt_pt "?\u0009\u0009Move to previous find hit" "?\u0009\u0009Mover para a ocorr\u00eancia anterior"
::msgcat::mcset pt_pt "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009Deslocar vista diff para o ficheiro seguinte"
::msgcat::mcset pt_pt "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009Procurar pela ocorr\u00eancia seguinte na vista diff"
::msgcat::mcset pt_pt "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009Procurar pela ocorr\u00eancia anterior na vista diff"
::msgcat::mcset pt_pt "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009Aumentar o tamanho da letra"
::msgcat::mcset pt_pt "<%s-plus>\u0009Increase font size" "<%s-mais>\u0009Aumentar o tamanho da letra"
::msgcat::mcset pt_pt "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009Diminuir o tamanho da letra"
::msgcat::mcset pt_pt "<%s-minus>\u0009Decrease font size" "<%s-menos>\u0009Diminuir o tamanho da letra"
::msgcat::mcset pt_pt "<F5>\u0009\u0009Update" "<F5>\u0009\u0009Atualizar"
::msgcat::mcset pt_pt "Error creating temporary directory %s:" "Erro ao criar ficheiro tempor\u00e1rio %s:"
::msgcat::mcset pt_pt "Error getting \"%s\" from %s:" "Erro ao obter \"%s\" de %s:"
::msgcat::mcset pt_pt "command failed:" "o comando falhou:"
::msgcat::mcset pt_pt "No such commit" "Commit inexistente"
::msgcat::mcset pt_pt "git gui blame: command failed:" "git gui blame: o comando falhou:"
::msgcat::mcset pt_pt "Couldn't read merge head: %s" "N\u00e3o foi poss\u00edvel ler a cabe\u00e7a de integra\u00e7\u00e3o: %s"
::msgcat::mcset pt_pt "Error reading index: %s" "Erro ao ler o \u00edndice: %s"
::msgcat::mcset pt_pt "Couldn't start git blame: %s" "N\u00e3o foi poss\u00edvel iniciar git blame: %s"
::msgcat::mcset pt_pt "Searching" "A procurar"
::msgcat::mcset pt_pt "Error running git blame: %s" "Erro ao executar git blame: %s"
::msgcat::mcset pt_pt "That line comes from commit %s,  which is not in this view" "Essa linha prov\u00e9m do commit %s, que n\u00e3o est\u00e1 nesta vista"
::msgcat::mcset pt_pt "External diff viewer failed:" "Visualizador diff externo falhou:"
::msgcat::mcset pt_pt "All files" "Todos os ficheiros"
::msgcat::mcset pt_pt "View" "Vista"
::msgcat::mcset pt_pt "Gitk view definition" "Defini\u00e7\u00e3o de vistas do gitk"
::msgcat::mcset pt_pt "Remember this view" "Recordar esta vista"
::msgcat::mcset pt_pt "References (space separated list):" "Refer\u00eancias (lista separada por espa\u00e7o):"
::msgcat::mcset pt_pt "Branches & tags:" "Ramos e tags:"
::msgcat::mcset pt_pt "All refs" "Todas as refer\u00eancias"
::msgcat::mcset pt_pt "All (local) branches" "Todos os ramos (locais)"
::msgcat::mcset pt_pt "All tags" "Todas as tags"
::msgcat::mcset pt_pt "All remote-tracking branches" "Todos os ramos remotos de monitoriza\u00e7\u00e3o"
::msgcat::mcset pt_pt "Commit Info (regular expressions):" "Informa\u00e7\u00e3o Sobre o Commit (express\u00f5es regulares):"
::msgcat::mcset pt_pt "Author:" "Autor:"
::msgcat::mcset pt_pt "Committer:" "Committer:"
::msgcat::mcset pt_pt "Commit Message:" "Mensagem de Commit:"
::msgcat::mcset pt_pt "Matches all Commit Info criteria" "Corresponde a todos os crit\u00e9rios da Informa\u00e7\u00e3o Sobre o Commit"
::msgcat::mcset pt_pt "Matches no Commit Info criteria" "N\u00e3o corresponde a nenhum crit\u00e9rio da Informa\u00e7\u00e3o Sobre o Commit"
::msgcat::mcset pt_pt "Changes to Files:" "Altera\u00e7\u00f5es nos Ficheiros:"
::msgcat::mcset pt_pt "Fixed String" "Cadeia Fixa"
::msgcat::mcset pt_pt "Regular Expression" "Express\u00e3o Regular"
::msgcat::mcset pt_pt "Search string:" "Procurar pela cadeia:"
::msgcat::mcset pt_pt "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "Datas de Commit (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):"
::msgcat::mcset pt_pt "Since:" "Desde:"
::msgcat::mcset pt_pt "Until:" "At\u00e9:"
::msgcat::mcset pt_pt "Limit and/or skip a number of revisions (positive integer):" "Limitar e/ou ignorar um n\u00famero de revis\u00f5es (inteiro positivo):"
::msgcat::mcset pt_pt "Number to show:" "N\u00famero a mostrar:"
::msgcat::mcset pt_pt "Number to skip:" "N\u00famero a ignorar:"
::msgcat::mcset pt_pt "Miscellaneous options:" "Op\u00e7\u00f5es diversas:"
::msgcat::mcset pt_pt "Strictly sort by date" "Ordenar estritamente pela data"
::msgcat::mcset pt_pt "Mark branch sides" "Marcar lado dos ramos"
::msgcat::mcset pt_pt "Limit to first parent" "Restringir ao primeiro pai"
::msgcat::mcset pt_pt "Simple history" "Hist\u00f3rico simples"
::msgcat::mcset pt_pt "Additional arguments to git log:" "Argumentos adicionais ao git log:"
::msgcat::mcset pt_pt "Enter files and directories to include, one per line:" "Introduza ficheiros e diret\u00f3rios para incluir, um por linha:"
::msgcat::mcset pt_pt "Command to generate more commits to include:" "Comando para gerar mais commits para incluir:"
::msgcat::mcset pt_pt "Gitk: edit view" "Gitk: editar vista"
::msgcat::mcset pt_pt "-- criteria for selecting revisions" "-- crit\u00e9rio para selecionar revis\u00f5es"
::msgcat::mcset pt_pt "View Name" "Nome da Vista"
::msgcat::mcset pt_pt "Apply (F5)" "Aplicar (F5)"
::msgcat::mcset pt_pt "Error in commit selection arguments:" "Erro nos argumentos de sele\u00e7\u00e3o de commits:"
::msgcat::mcset pt_pt "None" "Nenhum"
::msgcat::mcset pt_pt "Descendant" "Descendente"
::msgcat::mcset pt_pt "Not descendant" "N\u00e3o descendente"
::msgcat::mcset pt_pt "Ancestor" "Antecessor"
::msgcat::mcset pt_pt "Not ancestor" "N\u00e3o antecessor"
::msgcat::mcset pt_pt "Local changes checked in to index but not committed" "Altera\u00e7\u00f5es locais preparadas no \u00edndice mas n\u00e3o submetidas"
::msgcat::mcset pt_pt "Local uncommitted changes, not checked in to index" "Altera\u00e7\u00f5es locais n\u00e3o submetidas, n\u00e3o preparadas no \u00edndice"
::msgcat::mcset pt_pt "and many more" "e muitos mais"
::msgcat::mcset pt_pt "many" "muitos"
::msgcat::mcset pt_pt "Tags:" "Tags:"
::msgcat::mcset pt_pt "Parent" "Pai"
::msgcat::mcset pt_pt "Child" "Filho"
::msgcat::mcset pt_pt "Branch" "Ramo"
::msgcat::mcset pt_pt "Follows" "Sucede"
::msgcat::mcset pt_pt "Precedes" "Precede"
::msgcat::mcset pt_pt "Error getting diffs: %s" "Erro ao obter diferen\u00e7as: %s"
::msgcat::mcset pt_pt "Goto:" "Ir para:"
::msgcat::mcset pt_pt "Short SHA1 id %s is ambiguous" "O id SHA1 abreviado %s \u00e9 amb\u00edguo"
::msgcat::mcset pt_pt "Revision %s is not known" "A revis\u00e3o %s n\u00e3o \u00e9 conhecida"
::msgcat::mcset pt_pt "SHA1 id %s is not known" "O id SHA1 %s n\u00e3o \u00e9 conhecido"
::msgcat::mcset pt_pt "Revision %s is not in the current view" "A revis\u00e3o %s n\u00e3o se encontra na vista atual"
::msgcat::mcset pt_pt "Date" "Data"
::msgcat::mcset pt_pt "Children" "Filhos"
::msgcat::mcset pt_pt "Reset %s branch to here" "Repor o ramo %s para aqui"
::msgcat::mcset pt_pt "Detached head: can't reset" "Cabe\u00e7a destacada: n\u00e3o \u00e9 poss\u00edvel repor"
::msgcat::mcset pt_pt "Skipping merge commit " "A ignorar commit de integra\u00e7\u00e3o "
::msgcat::mcset pt_pt "Error getting patch ID for " "Erro ao obter ID de patch de "
::msgcat::mcset pt_pt " - stopping\n" " - a interromper\n"
::msgcat::mcset pt_pt "Commit " "Commit "
::msgcat::mcset pt_pt " is the same patch as\n       " " \u00e9 o mesmo patch que\n       "
::msgcat::mcset pt_pt " differs from\n       " " difere de\n       "
::msgcat::mcset pt_pt "Diff of commits:\n\n" "Diferen\u00e7a dos commits:\n\n"
::msgcat::mcset pt_pt " has %s children - stopping\n" " tem %s filhos - a interromper\n"
::msgcat::mcset pt_pt "Error writing commit to file: %s" "Erro ao escrever commit no ficheiro: %s"
::msgcat::mcset pt_pt "Error diffing commits: %s" "Erro ao calcular as diferen\u00e7as dos commits: %s"
::msgcat::mcset pt_pt "Top" "Topo"
::msgcat::mcset pt_pt "From" "De"
::msgcat::mcset pt_pt "To" "Para"
::msgcat::mcset pt_pt "Generate patch" "Gerar patch"
::msgcat::mcset pt_pt "From:" "De:"
::msgcat::mcset pt_pt "To:" "Para:"
::msgcat::mcset pt_pt "Reverse" "Reverter"
::msgcat::mcset pt_pt "Output file:" "Ficheiro de sa\u00edda:"
::msgcat::mcset pt_pt "Generate" "Gerar"
::msgcat::mcset pt_pt "Error creating patch:" "Erro ao criar patch:"
::msgcat::mcset pt_pt "ID:" "ID:"
::msgcat::mcset pt_pt "Tag name:" "Nome da tag:"
::msgcat::mcset pt_pt "Tag message is optional" "A mensagem da tag \u00e9 opcional"
::msgcat::mcset pt_pt "Tag message:" "Mensagem da tag:"
::msgcat::mcset pt_pt "Create" "Criar"
::msgcat::mcset pt_pt "No tag name specified" "Nenhum nome de tag especificado"
::msgcat::mcset pt_pt "Tag \"%s\" already exists" "A tag \"%s\" j\u00e1 existe"
::msgcat::mcset pt_pt "Error creating tag:" "Erro ao criar tag:"
::msgcat::mcset pt_pt "Command:" "Comando:"
::msgcat::mcset pt_pt "Write" "Escrever"
::msgcat::mcset pt_pt "Error writing commit:" "Erro ao escrever commit:"
::msgcat::mcset pt_pt "Name:" "Nome:"
::msgcat::mcset pt_pt "Please specify a name for the new branch" "Especifique um nome para o novo ramo"
::msgcat::mcset pt_pt "Branch '%s' already exists. Overwrite?" "O ramo '%s' j\u00e1 existe. Substitu\u00ed-lo?"
::msgcat::mcset pt_pt "Commit %s is already included in branch %s -- really re-apply it?" "O commit %s j\u00e1 est\u00e1 inclu\u00eddo no ramo %s -- reaplic\u00e1-lo mesmo assim?"
::msgcat::mcset pt_pt "Cherry-picking" "A efetuar cherry-pick"
::msgcat::mcset pt_pt "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "Falha ao efetuar cherry-pick devido a altera\u00e7\u00f5es locais no ficheiro '%s'.\nSubmeta, empilhe ou reponha as altera\u00e7\u00f5es e tente de novo."
::msgcat::mcset pt_pt "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "Falha ao efetuar cherry-pick devido a conflito de integra\u00e7\u00e3o.\nDeseja executar git citool para resolv\u00ea-lo?"
::msgcat::mcset pt_pt "No changes committed" "N\u00e3o foi submetida nenhum altera\u00e7\u00e3o"
::msgcat::mcset pt_pt "Commit %s is not included in branch %s -- really revert it?" "O commit %s n\u00e3o est\u00e1 inclu\u00eddo no ramo %s -- revert\u00ea-lo mesmo assim?"
::msgcat::mcset pt_pt "Reverting" "A reverter"
::msgcat::mcset pt_pt "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "Falha ao reverter devido a altera\u00e7\u00f5es locais nos seguintes ficheiros:%s Submeta, empilhe ou reponha as altera\u00e7\u00f5es e tente de novo."
::msgcat::mcset pt_pt "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "Falha ao reverter devido a conflito de integra\u00e7\u00e3o.\nDeseja executar git citool para resolv\u00ea-lo?"
::msgcat::mcset pt_pt "Confirm reset" "Confirmar reposi\u00e7\u00e3o"
::msgcat::mcset pt_pt "Reset branch %s to %s?" "Repor o ramo %s para %s?"
::msgcat::mcset pt_pt "Reset type:" "Tipo de reposi\u00e7\u00e3o:"
::msgcat::mcset pt_pt "Soft: Leave working tree and index untouched" "Suave: Deixar a \u00e1rvore de trabalho e o \u00edndice intactos"
::msgcat::mcset pt_pt "Mixed: Leave working tree untouched, reset index" "Misto: Deixar a \u00e1rvore de trabalho intacta, repor \u00edndice"
::msgcat::mcset pt_pt "Hard: Reset working tree and index\n(discard ALL local changes)" "Forte: Repor \u00e1rvore de trabalho e \u00edndice\n(descartar TODAS as altera\u00e7\u00f5es locais)"
::msgcat::mcset pt_pt "Resetting" "A repor"
::msgcat::mcset pt_pt "Checking out" "A extrair"
::msgcat::mcset pt_pt "Cannot delete the currently checked-out branch" "N\u00e3o \u00e9 poss\u00edvel eliminar o ramo atual extra\u00eddo"
::msgcat::mcset pt_pt "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "Os commits no ramo %s n\u00e3o est\u00e3o presentes em mais nenhum ramo.\nEliminar o ramo %s mesmo assim?"
::msgcat::mcset pt_pt "Tags and heads: %s" "Tags e cabe\u00e7as: %s"
::msgcat::mcset pt_pt "Filter" "Filtrar"
::msgcat::mcset pt_pt "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "Erro ao ler informa\u00e7\u00e3o de topologia do commit; a informa\u00e7\u00e3o do ramo e da tag precedente/seguinte ficar\u00e1 incompleta."
::msgcat::mcset pt_pt "Tag" "Tag"
::msgcat::mcset pt_pt "Id" "Id"
::msgcat::mcset pt_pt "Gitk font chooser" "Escolha de tipo de letra do gitk"
::msgcat::mcset pt_pt "B" "B"
::msgcat::mcset pt_pt "I" "I"
::msgcat::mcset pt_pt "Commit list display options" "Op\u00e7\u00f5es de visualiza\u00e7\u00e3o da lista de commits"
::msgcat::mcset pt_pt "Maximum graph width (lines)" "Largura m\u00e1xima do gr\u00e1fico (linhas)"
::msgcat::mcset pt_pt "Maximum graph width (% of pane)" "Largura m\u00e1xima do gr\u00e1fico (% do painel)"
::msgcat::mcset pt_pt "Show local changes" "Mostrar altera\u00e7\u00f5es locais"
::msgcat::mcset pt_pt "Auto-select SHA1 (length)" "Selecionar automaticamente SHA1 (largura)"
::msgcat::mcset pt_pt "Hide remote refs" "Ocultar refer\u00eancias remotas"
::msgcat::mcset pt_pt "Diff display options" "Op\u00e7\u00f5es de visualiza\u00e7\u00e3o de diferen\u00e7as"
::msgcat::mcset pt_pt "Tab spacing" "Espa\u00e7amento da tabula\u00e7\u00e3o"
::msgcat::mcset pt_pt "Display nearby tags/heads" "Mostrar tags/cabe\u00e7as pr\u00f3ximas"
::msgcat::mcset pt_pt "Maximum # tags/heads to show" "N\u00ba m\u00e1ximo de tags/cabe\u00e7as a mostrar"
::msgcat::mcset pt_pt "Limit diffs to listed paths" "Limitar diferen\u00e7as aos caminhos listados"
::msgcat::mcset pt_pt "Support per-file encodings" "Suportar codifica\u00e7\u00e3o por cada ficheiro"
::msgcat::mcset pt_pt "External diff tool" "Ferramenta diff externa"
::msgcat::mcset pt_pt "Choose..." "Escolher..."
::msgcat::mcset pt_pt "General options" "Op\u00e7\u00f5es gerais"
::msgcat::mcset pt_pt "Use themed widgets" "Usar widgets com estilo"
::msgcat::mcset pt_pt "(change requires restart)" "(altera\u00e7\u00e3o exige reiniciar)"
::msgcat::mcset pt_pt "(currently unavailable)" "(n\u00e3o dispon\u00edvel de momento)"
::msgcat::mcset pt_pt "Colors: press to choose" "Cores: pressione para escolher"
::msgcat::mcset pt_pt "Interface" "Interface"
::msgcat::mcset pt_pt "interface" "interface"
::msgcat::mcset pt_pt "Background" "Fundo"
::msgcat::mcset pt_pt "background" "fundo"
::msgcat::mcset pt_pt "Foreground" "Primeiro plano"
::msgcat::mcset pt_pt "foreground" "primeiro plano"
::msgcat::mcset pt_pt "Diff: old lines" "Diff: linhas antigas"
::msgcat::mcset pt_pt "diff old lines" "diff linhas antigas"
::msgcat::mcset pt_pt "Diff: new lines" "Diff: linhas novas"
::msgcat::mcset pt_pt "diff new lines" "diff linhas novas"
::msgcat::mcset pt_pt "Diff: hunk header" "Diff: cabe\u00e7alho do excerto"
::msgcat::mcset pt_pt "diff hunk header" "diff cabe\u00e7alho do excerto"
::msgcat::mcset pt_pt "Marked line bg" "Fundo da linha marcada"
::msgcat::mcset pt_pt "marked line background" "fundo da linha marcada"
::msgcat::mcset pt_pt "Select bg" "Selecionar fundo"
::msgcat::mcset pt_pt "Fonts: press to choose" "Tipo de letra: pressione para escolher"
::msgcat::mcset pt_pt "Main font" "Tipo de letra principal"
::msgcat::mcset pt_pt "Diff display font" "Tipo de letra ao mostrar diferen\u00e7as"
::msgcat::mcset pt_pt "User interface font" "Tipo de letra da interface de utilizador"
::msgcat::mcset pt_pt "Gitk preferences" "Prefer\u00eancias do gitk"
::msgcat::mcset pt_pt "General" "Geral"
::msgcat::mcset pt_pt "Colors" "Cores"
::msgcat::mcset pt_pt "Fonts" "Tipos de letra"
::msgcat::mcset pt_pt "Gitk: choose color for %s" "Gitk: escolher cor de %s"
::msgcat::mcset pt_pt "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "N\u00e3o \u00e9 poss\u00edvel executar o gitk com esta vers\u00e3o do Tcl/Tk.\nO gitk requer pelo menos Tcl/Tk 8.4."
::msgcat::mcset pt_pt "Cannot find a git repository here." "N\u00e3o foi encontrado nenhum reposit\u00f3rio git aqui."
::msgcat::mcset pt_pt "Ambiguous argument '%s': both revision and filename" "Argumento '%s' amb\u00edguo: pode ser uma revis\u00e3o ou um ficheiro"
::msgcat::mcset pt_pt "Bad arguments to gitk:" "Argumentos do gitk incorretos:"
