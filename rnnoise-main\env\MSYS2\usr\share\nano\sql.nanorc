## Syntax highlighting for (Postgres) SQL scripts.

## Original author:  <PERSON><PERSON>

syntax sql "\.sql$"
comment "-- "

# Types.
icolor #0a4 "\<(integer|int[248]|(big|small)?(int|serial)|decimal|numeric|real|double precision)\>"
icolor #0a4 "\<(bit( varying)?|boolean|bytea|enum|money|tsquery|tsvector|uuid|xml)\>"
icolor #0a4 "\<(char|varchar|character( varying)?|text|cidr|inet|macaddr8?)\>"
icolor #0a4 "\<(date|interval|time(stamp)?( with(out)? time zone)?)\>"
icolor #0a4 "\<(box|circle|line|lseg|path|point|polygon)\>"
icolor #0a4 "\<(date|int[48]|num|ts|tstz)range\>"

# SQL keywords.
icolor #48c "\<(ABORT|AGGREGATE|ALL|ALTER|ANALYZE|AND|ANY|AS|BEGIN|BETWEEN|CAST|CHECKPOINT)\>"
icolor #48c "\<(CLOSE|CLUSTER|COLLATION|COMMENT|COMMIT|CONSTRAINTS?|CONVERSION|COPY|CREATE)\>"
icolor #48c "\<(DATABASE|DEALLOCATE|DECLARE|DEFAULT|DELETE|DISCARD|DISTINCT|DO|DOMAIN|DROP)\>"
icolor #48c "\<(END|EXCEPT|EXECUTE|EXISTS|EXPLAIN|EXTENSION|FALSE|FETCH|FOREIGN (DATA WRAPPER|SCHEMA|TABLE))\>"
icolor #48c "\<(FROM|FUNCTION|GRANT|GROUP( BY)?|HAVING|IN|INDEX|INSERT|INTERSECT|INTO|IS)\>"
icolor #48c "\<(NATURAL )?(INNER |OUTER |CROSS |(LEFT|RIGHT|FULL)( OUTER)? )?JOIN\>"
icolor #48c "\<((PRIMARY|FOREIGN) KEY|LANGUAGE|LOAD|LIKE|LOCK|MOVE|NOT|NOTIFY|NULL)\>"
icolor #48c "\<(ON|ONLY|OPERATOR( CLASS| FAMILY)?|OR|ORDER BY|PREPARED?|PRIVILEGES|PROCEDURE)\>"
icolor #48c "\<(REFERENCES|RELEASE|REPLACE|RESET|RETURN(S|ING)|REVOKE|ROLE|ROLLBACK|RULE|SAVEPOINT|SCHEMA)\>"
icolor #48c "\<(SELECT|SEQUENCE|SERVER|SESSION AUTHORIZATION|SET|SHOW|SIMILAR|START|SYSTEM)\>"
icolor #48c "\<(TABLE(SPACE)?|TEXT SEARCH (CONFIGURATION|DICTIONARY|PARSER|TEMPLATE))\>"
icolor #48c "\<(TO|TRANSACTION|(EVENT )?TRIGGER|TRUE|TRUNCATE|TYPE|UNION|UNIQUE|UPDATE|USER( MAPPING)?|USING)\>"
icolor #48c "\<(VACUUM|VALUES|((REFRESH )?MATERIALIZED )?VIEW|WHERE|WITH)\>"

# Flow control.
icolor #cc0 "\<(CASE|WHEN|IF|THEN|ELSE|ELSE?IF|LOOP|CONTINUE|EXIT)\>"
icolor #cc0 "\<(FOR|FOREACH|IN|WHILE|END (CASE|IF|LOOP))\>"
icolor #a4a "\<(RAISE|EXCEPTION|NOTICE|RETURN)\>"

# Quoted identifiers.
color normal ""[[:alpha:]_][[:alnum:]_$]*""

# Strings.
color #4c0 "'([^']|\\')*'"

# Trailing whitespace.
color ,green "[[:space:]]+$"

# Comments.
color italic,#c44 "(--|//).*"
color italic,#c44 start="/\*" end="\*/"

# PostgreSQL default schemas.
color grey "\<(pg_catalog|public)\>"

# PostgreSQL PLs.
color #c08 "\<(SQL|pl(java|perlu?|pgsql|py|pythonu?|r|ruby|scheme|sh|tcl))\>"
