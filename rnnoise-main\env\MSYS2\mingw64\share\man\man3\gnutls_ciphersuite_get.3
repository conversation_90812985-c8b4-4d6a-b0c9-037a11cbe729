.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ciphersuite_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ciphersuite_get \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_ciphersuite_get(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
Get the canonical name of negotiated TLS ciphersuite.  The names
returned by this function match the IANA registry, with one
exception:

TLS_DHE_DSS_RC4_128_SHA { 0x00, 0x66 }

which is reserved for compatibility.

To get a detailed description of the current ciphersuite, it is
recommended to use \fBgnutls_session_get_desc()\fP.
.SH "RETURNS"
a string that contains the canonical name of a TLS ciphersuite,
or \fBNULL\fP if the handshake is not completed.
.SH "SINCE"
3.7.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
