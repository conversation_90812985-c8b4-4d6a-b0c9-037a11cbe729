_more_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-n'|'--lines')
			COMPREPLY=( $(compgen -W "number" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="
				--silent
				--logical
				--no-pause
				--print-over
				--clean-print
				--squeeze
				--plain
				--lines
				--help
				--version
			"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
		+*)
			OPTS="+number +/pattern"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac

	local IFS=$'\n'
	compopt -o filenames
	COMPREPLY=( $(compgen -f -- $cur) )
	return 0
}
complete -F _more_module more
