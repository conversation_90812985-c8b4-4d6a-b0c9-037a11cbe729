/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
COLORMGMTDLGORD DIALOG DISCARDABLE 0,0,350,190
STYLE DS_MODALFRAME | DS_CONTEXTHELP | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_VISIBLE
CAPTION "Color Management"
FONT 8,"MS Shell Dlg"
BEGIN
    CONTROL "",IDC_STATIC,"Static",SS_ETCHEDHORZ,170,83,11,1
    CONTROL "",IDC_STATIC,"Static",SS_ETCHEDHORZ,170,113,11,1
    CONTROL "",IDC_STATIC,"Static",SS_ETCHEDVERT,179,83,1,32
    CONTROL "",IDC_STATIC,"Static",SS_ETCHEDHORZ,180,97,11,1
    CONTROL "",IDC_STATIC,"St<PERSON>",SS_ETCHEDHORZ,170,143,21,1
    CONTROL "&Basic color management: Specify how colors appear on your monitor and printer.",rad1,"Button",BS_AU<PERSON>RA<PERSON>OBUTTON | WS_TABSTOP | WS_GROUP,23,40,310,10
    CONTROL "&Proofing: See colors on your monitor and/or printer as they would appear on another device.",rad2,"Button",BS_AUTORADIOBUTTON | WS_TABSTOP,23,51,310,10
    LTEXT "&Monitor Profile: (",stc1,30,68,148,8
    COMBOBOX cmb1,30,78,138,69,CBS_DROPDOWNLIST |
                    CBS_SORT | WS_VSCROLL | WS_TABSTOP | WS_GROUP
    LTEXT "Pri&nter Profile: (",stc2,30,98,148,8
    COMBOBOX cmb2,30,108,138,69,CBS_DROPDOWNLIST |
                    CBS_SORT | WS_VSCROLL | WS_TABSTOP | WS_GROUP
    LTEXT "&Rendering Intent:",stc3,192,82,138,8
    COMBOBOX cmb3,192,92,138,69,CBS_DROPDOWNLIST |
                    WS_VSCROLL | WS_TABSTOP | WS_GROUP
    LTEXT "Em&ulated device profile:",stc4,30,128,140,8
    COMBOBOX cmb4,30,138,138,69,CBS_DROPDOWNLIST |
                    CBS_SORT | WS_VSCROLL | WS_TABSTOP | WS_GROUP
    LTEXT "(Match intent is default for proofing)",stc6,192,107,144,8
    LTEXT "Ren&dering Intent:",stc5,192,128,138,8
    COMBOBOX cmb5,192,138,138,69,CBS_DROPDOWNLIST |
                    WS_VSCROLL | WS_TABSTOP | WS_GROUP
    LTEXT "Image, graphic, or text generated by:",stc7,8,7,120,8
    EDITTEXT edt1,128,5,214,12,ES_READONLY | NOT WS_TABSTOP | WS_GROUP
    DEFPUSHBUTTON "OK",IDOK,156,171,60,14
    PUSHBUTTON "Cancel",IDCANCEL,219,171,60,14
    PUSHBUTTON "&Apply",psh1,282,171,60,14
    CONTROL "",IDC_STATIC,"Static",SS_ETCHEDFRAME,8,29,334,134
    CONTROL "&Enable Color Management",chx1,"Button",BS_AUTOCHECKBOX | WS_TABSTOP | WS_GROUP,13,25,96,10

END
