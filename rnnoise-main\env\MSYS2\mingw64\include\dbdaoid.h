/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
DEFINE_OLEGUID(LIBID_CJETSQLHELP,0x00025f01,0,0);

#define DEFINE_DAOGUID(name,l) DEFINE_GUID(name,l,0,0x10,0x80,0,0,0xAA,0,0x6D,0x2E,0xA4)

DEFINE_OLEGUID(LIBID_CDAO,0x00025e01,0,0);
DEFINE_OLEGUID(LIBID_CDAO25,0x00025e04,0,0);
DEFINE_DAOGUID(IID_IDAOStdObject,0x0000000A);
DEFINE_DAOGUID(IID_IDAOStdCollection,0x0000000C);
DEFINE_DAOGUID(CLSID_CDAODBEngine,0x00000010);
DEFINE_DAOGUID(CLSID_CDAOPrivDBEngine,0x00000011);
DEFINE_DAOGUID(CLSID_CDAOConnection,0x00000012);
DEFINE_DAOGUID(CLSID_CDAOTableDef,0x00000013);
DEFINE_DAOGUID(CLSID_CDAOField,0x00000014);
DEFINE_DAOGUID(CLSID_CDAOIndex,0x00000015);
DEFINE_DAOGUID(CLSID_CDAOGroup,0x00000016);
DEFINE_DAOGUID(CLSID_CDAOUser,0x00000017);
DEFINE_DAOGUID(CLSID_CDAOQueryDef,0x00000018);
DEFINE_DAOGUID(CLSID_CDAORelation,0x00000019);
DEFINE_DAOGUID(IID_IDAODBEngine,0x00000020);
DEFINE_DAOGUID(IID_IDAODBEngineW,0x00000021);
DEFINE_DAOGUID(IID_IDAOError,0x00000022);
DEFINE_DAOGUID(IID_IDAOErrorW,0x00000023);
DEFINE_DAOGUID(IID_IDAOErrors,0x00000024);
DEFINE_DAOGUID(IID_IDAOErrorsW,0x00000025);
DEFINE_DAOGUID(IID_IDAOProperty,0x00000026);
DEFINE_DAOGUID(IID_IDAOPropertyW,0x00000027);
DEFINE_DAOGUID(IID_IDAOProperties,0x00000028);
DEFINE_DAOGUID(IID_IDAOPropertiesW,0x00000029);
DEFINE_DAOGUID(IID_IDAORecordset,0x00000030);
DEFINE_DAOGUID(IID_IDAORecordsetW,0x00000031);
DEFINE_DAOGUID(IID_IDAORecordsets,0x00000032);
DEFINE_DAOGUID(IID_IDAORecordsetsW,0x00000033);
DEFINE_OLEGUID(IID_ICDAORecordset,0x00025e31,0,0);
DEFINE_DAOGUID(IID_IDAOWorkspace,0x00000038);
DEFINE_DAOGUID(IID_IDAOWorkspaceW,0x00000039);
DEFINE_DAOGUID(IID_IDAOWorkspaces,0x0000003A);
DEFINE_DAOGUID(IID_IDAOWorkspacesW,0x0000003B);
DEFINE_DAOGUID(IID_IDAOConnection,0x00000040);
DEFINE_DAOGUID(IID_IDAOConnectionW,0x00000041);
DEFINE_DAOGUID(IID_IDAOConnections,0x00000042);
DEFINE_DAOGUID(IID_IDAOConnectionsW,0x00000043);
DEFINE_DAOGUID(IID_IDAOTableDef,0x00000048);
DEFINE_DAOGUID(IID_IDAOTableDefW,0x00000049);
DEFINE_DAOGUID(IID_IDAOTableDefs,0x0000004A);
DEFINE_DAOGUID(IID_IDAOTableDefsW,0x0000004B);
DEFINE_DAOGUID(IID_IDAOField,0x00000050);
DEFINE_DAOGUID(IID_IDAOFieldW,0x00000051);
DEFINE_DAOGUID(IID_IDAOFields,0x00000052);
DEFINE_DAOGUID(IID_IDAOFieldsW,0x00000053);
DEFINE_DAOGUID(IID_IDAOIndex,0x00000058);
DEFINE_DAOGUID(IID_IDAOIndexW,0x00000059);
DEFINE_DAOGUID(IID_IDAOIndexes,0x0000005A);
DEFINE_DAOGUID(IID_IDAOIndexesW,0x0000005B);
DEFINE_DAOGUID(IID_IDAOIndexFields,0x0000005C);
DEFINE_DAOGUID(IID_IDAOIndexFieldsW,0x0000005D);
DEFINE_DAOGUID(IID_IDAOGroup,0x00000060);
DEFINE_DAOGUID(IID_IDAOGroupW,0x00000061);
DEFINE_DAOGUID(IID_IDAOGroups,0x00000062);
DEFINE_DAOGUID(IID_IDAOGroupsW,0x00000063);
DEFINE_DAOGUID(IID_IDAOUser,0x00000068);
DEFINE_DAOGUID(IID_IDAOUserW,0x00000069);
DEFINE_DAOGUID(IID_IDAOUsers,0x0000006A);
DEFINE_DAOGUID(IID_IDAOUsersW,0x0000006B);
DEFINE_DAOGUID(IID_IDAODatabase,0x00000070);
DEFINE_DAOGUID(IID_IDAODatabaseW,0x00000071);
DEFINE_DAOGUID(IID_IDAODatabases,0x00000072);
DEFINE_DAOGUID(IID_IDAODatabasesW,0x00000073);
DEFINE_DAOGUID(IID_IDAOQueryDef,0x00000078);
DEFINE_DAOGUID(IID_IDAOQueryDefW,0x00000079);
DEFINE_DAOGUID(IID_IDAOQueryDefs,0x0000007A);
DEFINE_DAOGUID(IID_IDAOQueryDefsW,0x0000007B);
DEFINE_DAOGUID(IID_IDAOParameter,0x00000080);
DEFINE_DAOGUID(IID_IDAOParameterW,0x00000081);
DEFINE_DAOGUID(IID_IDAOParameters,0x00000082);
DEFINE_DAOGUID(IID_IDAOParametersW,0x00000083);
DEFINE_DAOGUID(IID_IDAORelation,0x00000088);
DEFINE_DAOGUID(IID_IDAORelationW,0x00000089);
DEFINE_DAOGUID(IID_IDAORelations,0x0000008A);
DEFINE_DAOGUID(IID_IDAORelationsW,0x0000008B);
DEFINE_DAOGUID(IID_IDAOContainer,0x00000090);
DEFINE_DAOGUID(IID_IDAOContainerW,0x00000091);
DEFINE_DAOGUID(IID_IDAOContainers,0x00000092);
DEFINE_DAOGUID(IID_IDAOContainersW,0x00000093);
DEFINE_DAOGUID(IID_IDAODocument,0x00000098);
DEFINE_DAOGUID(IID_IDAODocumentW,0x00000099);
DEFINE_DAOGUID(IID_IDAODocuments,0x0000009A);
DEFINE_DAOGUID(IID_IDAODocumentsW,0x0000009B);
DEFINE_DAOGUID(IID_IDAOCollection,0x000000A0);
DEFINE_DAOGUID(IID_IDAODynaCollection,0x000000A2);
DEFINE_DAOGUID(IID_IDAOQueryCP,0x000000B0);
DEFINE_DAOGUID(IID_IDAOQueryAS,0x000000B2);
DEFINE_OLEGUID(LIBID30_CDAO,0x00025e01,0,0);
DEFINE_OLEGUID(LIBID30_CDAO25,0x00025e04,0,0);
DEFINE_OLEGUID(IID30_IDAOStdObject,0x00025e02,0,0);
DEFINE_OLEGUID(IID30_IDAOStdCollection,0x00025e03,0,0);
DEFINE_OLEGUID(CLSID30_CDAODBEngine,0x00025e15,0,0);
DEFINE_OLEGUID(IID30_IDAODBEngine,0x00025e16,0,0);
DEFINE_OLEGUID(IID30_IDAODBEngineW,0x00025e17,0,0);
DEFINE_OLEGUID(IID30_ICDAOJETDBEngine,0x00025e18,0,0);
DEFINE_OLEGUID(CLSID30_CDAOPrivDBEngine,0x00025e19,0,0);
DEFINE_OLEGUID(IID30_IDAOError,0x00025e1d,0,0);
DEFINE_OLEGUID(IID30_IDAOErrorW,0x00025e1e,0,0);
DEFINE_OLEGUID(IID30_IDAOErrors,0x00025e1f,0,0);
DEFINE_OLEGUID(IID30_IDAOErrorsW,0x00025e20,0,0);
DEFINE_OLEGUID(IID30_ICDAOJETError,0x00025e21,0,0);
DEFINE_OLEGUID(IID30_IDAOProperty,0x00025e25,0,0);
DEFINE_OLEGUID(IID30_IDAOPropertyW,0x00025e26,0,0);
DEFINE_OLEGUID(IID30_IDAOProperties,0x00025e27,0,0);
DEFINE_OLEGUID(IID30_IDAOPropertiesW,0x00025e28,0,0);
DEFINE_OLEGUID(IID30_IDAORecordset,0x00025e2d,0,0);
DEFINE_OLEGUID(IID30_IDAORecordsetW,0x00025e2e,0,0);
DEFINE_OLEGUID(IID30_IDAORecordsets,0x00025e2f,0,0);
DEFINE_OLEGUID(IID30_IDAORecordsetsW,0x00025e30,0,0);
DEFINE_OLEGUID(IID30_ICDAORecordset,0x00025e31,0,0);
DEFINE_OLEGUID(IID30_ICDAOJETRecordset,0x00025e32,0,0);
DEFINE_OLEGUID(IID30_IDAOSnapshot,0x00025e33,0,0);
DEFINE_OLEGUID(IID30_IDAOTable,0x00025e34,0,0);
DEFINE_OLEGUID(IID30_IDAODynaset,0x00025e35,0,0);
DEFINE_OLEGUID(IID30_IDAOWorkspace,0x00025e3a,0,0);
DEFINE_OLEGUID(IID30_IDAOWorkspaceW,0x00025e3b,0,0);
DEFINE_OLEGUID(IID30_IDAOWorkspaces,0x00025e3c,0,0);
DEFINE_OLEGUID(IID30_IDAOWorkspacesW,0x00025e3d,0,0);
DEFINE_OLEGUID(IID30_ICDAOJETWorkspace,0x00025e3e,0,0);
DEFINE_OLEGUID(CLSID30_CDAOConnection,0x00025eb1,0,0);
DEFINE_OLEGUID(IID30_IDAOConnection,0x00025eb2,0,0);
DEFINE_OLEGUID(IID30_IDAOConnectionW,0x00025eb3,0,0);
DEFINE_OLEGUID(IID30_IDAOConnections,0x00025eb4,0,0);
DEFINE_OLEGUID(IID30_IDAOConnectionsW,0x00025eb5,0,0);
DEFINE_OLEGUID(CLSID30_CDAOTableDef,0x00025e43,0,0);
DEFINE_OLEGUID(IID30_IDAOTableDef,0x00025e44,0,0);
DEFINE_OLEGUID(IID30_IDAOTableDefW,0x00025e45,0,0);
DEFINE_OLEGUID(IID30_IDAOTableDefs,0x00025e46,0,0);
DEFINE_OLEGUID(IID30_IDAOTableDefsW,0x00025e47,0,0);
DEFINE_OLEGUID(CLSID30_CDAOField,0x00025e4c,0,0);
DEFINE_OLEGUID(IID30_IDAOField,0x00025e4d,0,0);
DEFINE_OLEGUID(IID30_IDAOFieldW,0x00025e4e,0,0);
DEFINE_OLEGUID(IID30_IDAOFields,0x00025e4f,0,0);
DEFINE_OLEGUID(IID30_IDAOFieldsW,0x00025e50,0,0);
DEFINE_OLEGUID(IID30_ICDAOJETField,0x00025e49,0,0);
DEFINE_OLEGUID(CLSID30_CDAOIndex,0x00025e55,0,0);
DEFINE_OLEGUID(IID30_IDAOIndex,0x00025e56,0,0);
DEFINE_OLEGUID(IID30_IDAOIndexW,0x00025ed7,0,0);
DEFINE_OLEGUID(IID30_IDAOIndexes,0x00025e58,0,0);
DEFINE_OLEGUID(IID30_IDAOIndexesW,0x00025e59,0,0);
DEFINE_OLEGUID(IID30_IDAOIndexFields,0x00025e5a,0,0);
DEFINE_OLEGUID(IID30_IDAOIndexFieldsW,0x00025e5b,0,0);
DEFINE_OLEGUID(CLSID30_CDAOGroup,0x00025e5f,0,0);
DEFINE_OLEGUID(IID30_IDAOGroup,0x00025e60,0,0);
DEFINE_OLEGUID(IID30_IDAOGroupW,0x00025e61,0,0);
DEFINE_OLEGUID(IID30_IDAOGroups,0x00025e62,0,0);
DEFINE_OLEGUID(IID30_IDAOGroupsW,0x00025e63,0,0);
DEFINE_OLEGUID(CLSID30_CDAOUser,0x00025e68,0,0);
DEFINE_OLEGUID(IID30_IDAOUser,0x00025e69,0,0);
DEFINE_OLEGUID(IID30_IDAOUserW,0x00025e6a,0,0);
DEFINE_OLEGUID(IID30_IDAOUsers,0x00025e6b,0,0);
DEFINE_OLEGUID(IID30_IDAOUsersW,0x00025e6c,0,0);
DEFINE_OLEGUID(IID30_IDAODatabase,0x00025e71,0,0);
DEFINE_OLEGUID(IID30_IDAODatabaseW,0x00025e72,0,0);
DEFINE_OLEGUID(IID30_IDAODatabases,0x00025e73,0,0);
DEFINE_OLEGUID(IID30_IDAODatabasesW,0x00025e74,0,0);
DEFINE_OLEGUID(IID30_ICDAOJETDatabase,0x00025e75,0,0);
DEFINE_OLEGUID(CLSID30_CDAOQueryDef,0x00025e7a,0,0);
DEFINE_OLEGUID(IID30_IDAOQueryDef,0x00025e7b,0,0);
DEFINE_OLEGUID(IID30_IDAOQueryDefW,0x00025e7c,0,0);
DEFINE_OLEGUID(IID30_IDAOQueryDefs,0x00025e7d,0,0);
DEFINE_OLEGUID(IID30_IDAOQueryDefsW,0x00025e7e,0,0);
DEFINE_OLEGUID(IID30_IDAOParameter,0x00025e83,0,0);
DEFINE_OLEGUID(IID30_IDAOParameterW,0x00025e84,0,0);
DEFINE_OLEGUID(IID30_IDAOParameters,0x00025e85,0,0);
DEFINE_OLEGUID(IID30_IDAOParametersW,0x00025e86,0,0);
DEFINE_OLEGUID(CLSID30_CDAORelation,0x00025e8b,0,0);
DEFINE_OLEGUID(IID30_IDAORelation,0x00025e8c,0,0);
DEFINE_OLEGUID(IID30_IDAORelationW,0x00025e8d,0,0);
DEFINE_OLEGUID(IID30_IDAORelations,0x00025e8e,0,0);
DEFINE_OLEGUID(IID30_IDAORelationsW,0x00025e8f,0,0);
DEFINE_OLEGUID(IID30_IDAOContainer,0x00025e94,0,0);
DEFINE_OLEGUID(IID30_IDAOContainerW,0x00025e95,0,0);
DEFINE_OLEGUID(IID30_IDAOContainers,0x00025e96,0,0);
DEFINE_OLEGUID(IID30_IDAOContainersW,0x00025e97,0,0);
DEFINE_OLEGUID(IID30_IDAODocument,0x00025e9c,0,0);
DEFINE_OLEGUID(IID30_IDAODocumentW,0x00025e9d,0,0);
DEFINE_OLEGUID(IID30_IDAODocuments,0x00025e9e,0,0);
DEFINE_OLEGUID(IID30_IDAODocumentsW,0x00025e9f,0,0);
DEFINE_OLEGUID(IID30_IDAOCollection,0x00025ea4,0,0);
DEFINE_OLEGUID(IID30_IDAODynaCollection,0x00025ea5,0,0);
DEFINE_OLEGUID(IID30_IDAOQueryCP,0x00025eaa,0,0);
DEFINE_OLEGUID(IID30_IDAOQueryAS,0x00025eab,0,0);
