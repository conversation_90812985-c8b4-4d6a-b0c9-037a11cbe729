.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_force_valid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_force_valid \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_session_force_valid(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
Clears the invalid flag in a session. That means
that sessions were corrupt or invalid data were received 
can be re\-used. Use only when debugging or experimenting
with the TLS protocol. Should not be used in typical
applications.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
