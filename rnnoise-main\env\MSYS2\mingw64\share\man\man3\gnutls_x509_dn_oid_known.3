.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_dn_oid_known" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_dn_oid_known \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_dn_oid_known(const char * " oid ");"
.SH ARGUMENTS
.IP "const char * oid" 12
holds an Object Identifier in a null terminated string
.SH "DESCRIPTION"
This function will inform about known DN OIDs. This is useful since
functions like \fBgnutls_x509_crt_set_dn_by_oid()\fP use the information
on known OIDs to properly encode their input. Object Identifiers
that are not known are not encoded by these functions, and their
input is stored directly into the ASN.1 structure. In that case of
unknown OIDs, you have the responsibility of DER encoding your
data.
.SH "RETURNS"
1 on known OIDs and 0 otherwise.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
