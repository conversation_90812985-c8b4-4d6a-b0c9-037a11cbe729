.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dh_set_prime_bits" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dh_set_prime_bits \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_dh_set_prime_bits(gnutls_session_t " session ", unsigned int " bits ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int bits" 12
is the number of bits
.SH "DESCRIPTION"
This function sets the number of bits, for use in a Di<PERSON>ie\-<PERSON><PERSON>
key exchange.  This is used both in DH ephemeral and DH anonymous
cipher suites.  This will set the minimum size of the prime that
will be used for the handshake.

In the client side it sets the minimum accepted number of bits.  If
a server sends a prime with less bits than that
\fBGNUTLS_E_DH_PRIME_UNACCEPTABLE\fP will be returned by the handshake.

Note that this function will warn via the audit log for value that
are believed to be weak.

The function has no effect in server side.

Note that since 3.1.7 this function is deprecated. The minimum
number of bits is set by the priority string level.
Also this function must be called after \fBgnutls_priority_set_direct()\fP
or the set value may be overridden by the selected priority options.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
