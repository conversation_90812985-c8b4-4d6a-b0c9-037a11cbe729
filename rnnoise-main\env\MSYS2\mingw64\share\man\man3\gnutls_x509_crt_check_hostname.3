.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_check_hostname" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_check_hostname \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "unsigned gnutls_x509_crt_check_hostname(gnutls_x509_crt_t " cert ", const char * " hostname ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain an gnutls_x509_crt_t type
.IP "const char * hostname" 12
A null terminated string that contains a DNS name
.SH "DESCRIPTION"
This function will check if the given certificate's subject matches
the given hostname.  This is a basic implementation of the matching
described in RFC6125, and takes into account wildcards,
and the DNSName/IPAddress subject alternative name PKIX extension.

For details see also \fBgnutls_x509_crt_check_hostname2()\fP.
.SH "RETURNS"
non\-zero for a successful match, and zero on failure.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
