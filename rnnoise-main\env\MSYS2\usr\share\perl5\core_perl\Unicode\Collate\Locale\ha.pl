+{
   locale_version => 1.31,
# b-hook, d-hook, k-hook, y-hook don't require tailoring
   entry => <<'ENTRY', # for DUCET v13.0.0
0073 0068 ; [.21D3.0020.0002] # <LATIN SMALL LETTER S, LATIN SMALL LETTER H>
0053 0068 ; [.21D3.0020.0007] # <LATIN CAPITAL LETTER S, LATIN SMALL LETTER H>
0053 0048 ; [.21D3.0020.0008] # <LATIN CAPITAL LETTER S, LATIN CAPITAL LETTER H>
0074 0073 ; [.21F8.0020.0002] # <LATIN SMALL LETTER T, LATIN SMALL LETTER S>
0054 0073 ; [.21F8.0020.0007] # <LATIN CAPITAL LETTER T, LATIN SMALL LETTER S>
0054 0053 ; [.21F8.0020.0008] # <LATIN CAPITAL LETTER T, LATIN CAPITAL LETTER S>
02BC 0079 ; [.227C.0020.0003] # <MODIFIER LETTER APOSTROPHE, LATIN SMALL LETTER Y>
02BC 0059 ; [.227C.0020.0009] # <MODIFIER LETTER APOSTROPHE, LATIN CAPITAL LETTER Y>
0027 0079 ; [.227C.0020.0004] # <APOSTROPHE, LATIN SMALL LETTER Y>
0027 0059 ; [.227C.0020.000A] # <APOSTROPHE, LATIN CAPITAL LETTER Y>
ENTRY
};
