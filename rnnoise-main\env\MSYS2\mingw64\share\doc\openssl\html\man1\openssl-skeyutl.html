<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-skeyutl</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-skeyutl - opaque symmetric keys routines</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>skeyutl</b> [<b>-help</b>] [<b>-cipher</b> <i>cipher</i>] [<b>-skeymgmt</b> <i>skeymgmt</i>] [<b>-skeyopt</b> <i>opt</i>:<i>value</i>] [<b>-genkey</b>] [<b>-provider</b> <i>name</i>] [<b>-provider-path</b> <i>path</i>] [<b>-provparam</b> <i>[name:]key=value</i>] [<b>-propquery</b> <i>propq</i>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Not all cipher implementations support keys as raw bytes. E.g. PKCS#11 tokens can store them internally without any option to get the raw byte representation.</p>

<p>This tool is designed for managing opaque symmetric keys.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="cipher-cipher"><b>-cipher</b> <i>cipher</i></dt>
<dd>

<p>The cipher to generate the key for.</p>

</dd>
<dt id="skeymgmt-skeymgmt"><b>-skeymgmt</b> <i>skeymgmt</i></dt>
<dd>

<p>Some providers may support opaque symmetric keys objects. To use them, we need to know the <i>skeymgmt</i>. If not specified, the name of the cipher will be used.</p>

<p>To find out the name of the suitable symmetric key management, please refer to the output of the <code>openssl list -skey-managers</code> command.</p>

</dd>
<dt id="skeyopt-opt:value"><b>-skeyopt</b> <i>opt</i>:<i>value</i></dt>
<dd>

<p>To obtain an existing opaque symmetric key or to generate a new one, key options are specified as opt:value. These options can&#39;t be used together with any options implying raw key either directly or indirectly.</p>

</dd>
<dt id="genkey"><b>-genkey</b></dt>
<dd>

<p>Generate a new opaque key object.</p>

</dd>
<dt id="provider-name"><b>-provider</b> <i>name</i></dt>
<dd>

</dd>
<dt id="provider-path-path"><b>-provider-path</b> <i>path</i></dt>
<dd>

</dd>
<dt id="provparam-name:-key-value"><b>-provparam</b> <i>[name:]key=value</i></dt>
<dd>

</dd>
<dt id="propquery-propq"><b>-propquery</b> <i>propq</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Provider Options&quot; in openssl(1)</a>, <a href="../man7/provider.html">provider(7)</a>, and <a href="../man7/property.html">property(7)</a>.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl-enc.html">openssl-enc(1)</a>, <a href="../man3/EVP_SKEY.html">EVP_SKEY(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>skeyutl</b> command was added in OpenSSL 3.5.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2025 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


