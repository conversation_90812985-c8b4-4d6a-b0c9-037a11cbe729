cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file <PERSON><PERSON><PERSON><PERSON>IMER within this package.")
cpp_quote(" */")
cpp_quote("")

#ifndef LF_FACESIZE
#define LF_FACESIZE 32
#endif
#ifndef STYLE_DESCRIPTION_SIZE
#define STYLE_DESCRIPTION_SIZE 32
#endif
#ifndef IMEMENUITEM_STRING_SIZE
#define IMEMENUITEM_STRING_SIZE 80
#endif

#ifndef DO_NO_IMPORTS
import "unknwn.idl";
#endif

interface IEnumRegisterWordA;
interface IEnumRegisterWordW;

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

[uuid (4955dd30-B159-11d0-8fcf-00aa006bcc59), lcid (0x0000), version (0.1)]
library ActiveIMM {
  importlib ("stdole2.tlb");
  cpp_quote("#include <imm.h>")

cpp_quote("#if 0")
  typedef WORD LANGID;
  typedef struct {
    LPSTR lpReading;
    LPSTR lpWord;
  } REGISTERWORDA;

  typedef struct {
    LPWSTR lpReading;
    LPWSTR lpWord;
  } REGISTERWORDW;

  typedef struct {
    LONG lfHeight;
    LONG lfWidth;
    LONG lfEscapement;
    LONG lfOrientation;
    LONG lfWeight;
    BYTE lfItalic;
    BYTE lfUnderline;
    BYTE lfStrikeOut;
    BYTE lfCharSet;
    BYTE lfOutPrecision;
    BYTE lfClipPrecision;
    BYTE lfQuality;
    BYTE lfPitchAndFamily;
    CHAR lfFaceName[LF_FACESIZE];
  } LOGFONTA;

  typedef struct {
    LONG lfHeight;
    LONG lfWidth;
    LONG lfEscapement;
    LONG lfOrientation;
    LONG lfWeight;
    BYTE lfItalic;
    BYTE lfUnderline;
    BYTE lfStrikeOut;
    BYTE lfCharSet;
    BYTE lfOutPrecision;
    BYTE lfClipPrecision;
    BYTE lfQuality;
    BYTE lfPitchAndFamily;
    WCHAR lfFaceName[LF_FACESIZE];
  } LOGFONTW;

  typedef DWORD HIMC;
  typedef DWORD HIMCC;

  typedef struct {
    DWORD dwIndex;
    DWORD dwStyle;
    POINT ptCurrentPos;
    RECT rcArea;
  } CANDIDATEFORM;

  typedef struct {
    DWORD dwStyle;
    POINT ptCurrentPos;
    RECT rcArea;
  } COMPOSITIONFORM;

  typedef struct {
    DWORD dwSize;
    DWORD dwStyle;
    DWORD dwCount;
    DWORD dwSelection;
    DWORD dwPageStart;
    DWORD dwPageSize;
    DWORD dwOffset[1];
  } CANDIDATELIST;

  typedef struct {
    DWORD dwStyle;
    CHAR szDescription[STYLE_DESCRIPTION_SIZE];
  } STYLEBUFA;

  typedef struct {
    DWORD dwStyle;
    WCHAR szDescription[STYLE_DESCRIPTION_SIZE];
  } STYLEBUFW;

  typedef WORD ATOM;

  typedef struct {
    UINT cbSize;
    UINT fType;
    UINT fState;
    UINT wID;
    HBITMAP hbmpChecked;
    HBITMAP hbmpUnchecked;
    DWORD dwItemData;
    CHAR szString[IMEMENUITEM_STRING_SIZE];
    HBITMAP hbmpItem;
  } IMEMENUITEMINFOA;

typedef struct {
    UINT cbSize;
    UINT fType;
    UINT fState;
    UINT wID;
    HBITMAP hbmpChecked;
    HBITMAP hbmpUnchecked;
    DWORD dwItemData;
    WCHAR szString[IMEMENUITEM_STRING_SIZE];
    HBITMAP hbmpItem;
  } IMEMENUITEMINFOW;
cpp_quote("#endif")

  cpp_quote("#ifndef _DDKIMM_H_")

  typedef struct {
    HWND hWnd;
    BOOL fOpen;
    POINT ptStatusWndPos;
    POINT ptSoftKbdPos;
    DWORD fdwConversion;
    DWORD fdwSentence;
    union {
      LOGFONTA A;
      LOGFONTW W;
    } lfFont;
    COMPOSITIONFORM cfCompForm;
    CANDIDATEFORM cfCandForm[4];
    HIMCC hCompStr;
    HIMCC hCandInfo;
    HIMCC hGuideLine;
    HIMCC hPrivate;
    DWORD dwNumMsgBuf;
    HIMCC hMsgBuf;
    DWORD fdwInit;
    DWORD dwReserve[3];
  } INPUTCONTEXT;

  typedef struct {
    DWORD dwPrivateDataSize;
    DWORD fdwProperty;
    DWORD fdwConversionCaps;
    DWORD fdwSentenceCaps;
    DWORD fdwUICaps;
    DWORD fdwSCSCaps;
    DWORD fdwSelectCaps;
  } IMEINFO;
  cpp_quote("#endif")

  [object, uuid (09b5eab0-f997-11d1-93d4-0060b067b86e), pointer_default (unique)]
  interface IEnumInputContext : IUnknown {
    HRESULT Clone ([out] IEnumInputContext **ppEnum);
    HRESULT Next ([in] ULONG ulCount,[out] HIMC *rgInputContext,[out] ULONG *pcFetched);
    HRESULT Reset ();
    HRESULT Skip ([in] ULONG ulCount);
  };

  [object, uuid (b3458082-bd00-11d1-939b-0060b067b86e), pointer_default (unique)]
  interface IActiveIMMRegistrar : IUnknown {
    HRESULT RegisterIME ([in] REFCLSID rclsid,[in] LANGID lgid,[in] LPCWSTR pszIconFile,[in] LPCWSTR pszDesc);
    HRESULT UnregisterIME ([in] REFCLSID rclsid);
  };

  [object, uuid (b5cf2cfa-8aeb-11d1-9364-0060b067b86e), pointer_default (unique)]
  interface IActiveIMMMessagePumpOwner : IUnknown {
    HRESULT Start ();
    HRESULT End ();
    HRESULT OnTranslateMessage ([in] const MSG *pMsg);
    HRESULT Pause ([out] DWORD *pdwCookie);
    HRESULT Resume ([in] DWORD dwCookie);
  }

  [object, uuid (08c0e040-62d1-11d1-9326-0060b067b86e), pointer_default (unique)]
  interface IActiveIMMApp : IUnknown {
    HRESULT AssociateContext ([in] HWND hWnd,[in] HIMC hIME,[out] HIMC *phPrev);
    HRESULT ConfigureIMEA ([in] HKL hKL,[in] HWND hWnd,[in] DWORD dwMode,[in] REGISTERWORDA *pData);
    HRESULT ConfigureIMEW ([in] HKL hKL,[in] HWND hWnd,[in] DWORD dwMode,[in] REGISTERWORDW *pData);
    HRESULT CreateContext ([out] HIMC *phIMC);
    HRESULT DestroyContext ([in] HIMC hIME);
    HRESULT EnumRegisterWordA ([in] HKL hKL,[in] LPSTR szReading,[in] DWORD dwStyle,[in] LPSTR szRegister,[in] LPVOID pData,[out] IEnumRegisterWordA **pEnum);
    HRESULT EnumRegisterWordW ([in] HKL hKL,[in] LPWSTR szReading,[in] DWORD dwStyle,[in] LPWSTR szRegister,[in] LPVOID pData,[out] IEnumRegisterWordW **pEnum);
    HRESULT EscapeA ([in] HKL hKL,[in] HIMC hIMC,[in] UINT uEscape,[in, out] LPVOID pData,[out] LRESULT *plResult);
    HRESULT EscapeW ([in] HKL hKL,[in] HIMC hIMC,[in] UINT uEscape,[in, out] LPVOID pData,[out] LRESULT *plResult);
    HRESULT GetCandidateListA ([in] HIMC hIMC,[in] DWORD dwIndex,[in] UINT uBufLen,[out] CANDIDATELIST *pCandList,[out] UINT *puCopied);
    HRESULT GetCandidateListW ([in] HIMC hIMC,[in] DWORD dwIndex,[in] UINT uBufLen,[out] CANDIDATELIST *pCandList,[out] UINT *puCopied);
    HRESULT GetCandidateListCountA ([in] HIMC hIMC,[out] DWORD *pdwListSize,[out] DWORD *pdwBufLen);
    HRESULT GetCandidateListCountW ([in] HIMC hIMC,[out] DWORD *pdwListSize,[out] DWORD *pdwBufLen);
    HRESULT GetCandidateWindow ([in] HIMC hIMC,[in] DWORD dwIndex,[out] CANDIDATEFORM *pCandidate);
    HRESULT GetCompositionFontA ([in] HIMC hIMC,[out] LOGFONTA *plf);
    HRESULT GetCompositionFontW ([in] HIMC hIMC,[out] LOGFONTW *plf);
    HRESULT GetCompositionStringA ([in] HIMC hIMC,[in] DWORD dwIndex,[in] DWORD dwBufLen,[out] LONG *plCopied,[out] LPVOID pBuf);
    HRESULT GetCompositionStringW ([in] HIMC hIMC,[in] DWORD dwIndex,[in] DWORD dwBufLen,[out] LONG *plCopied,[out] LPVOID pBuf);
    HRESULT GetCompositionWindow ([in] HIMC hIMC,[out] COMPOSITIONFORM *pCompForm);
    HRESULT GetContext ([in] HWND hWnd,[out] HIMC *phIMC);
    HRESULT GetConversionListA ([in] HKL hKL,[in] HIMC hIMC,[in] LPSTR pSrc,[in] UINT uBufLen,[in] UINT uFlag,[out] CANDIDATELIST *pDst,[out] UINT *puCopied);
    HRESULT GetConversionListW ([in] HKL hKL,[in] HIMC hIMC,[in] LPWSTR pSrc,[in] UINT uBufLen,[in] UINT uFlag,[out] CANDIDATELIST *pDst,[out] UINT *puCopied);
    HRESULT GetConversionStatus ([in] HIMC hIMC,[out] DWORD *pfdwConversion,[out] DWORD *pfdwSentence);
    HRESULT GetDefaultIMEWnd ([in] HWND hWnd,[out] HWND *phDefWnd);
    HRESULT GetDescriptionA ([in] HKL hKL,[in] UINT uBufLen,[out] LPSTR szDescription,[out] UINT *puCopied);
    HRESULT GetDescriptionW ([in] HKL hKL,[in] UINT uBufLen,[out] LPWSTR szDescription,[out] UINT *puCopied);
    HRESULT GetGuideLineA ([in] HIMC hIMC,[in] DWORD dwIndex,[in] DWORD dwBufLen,[out] LPSTR pBuf,[out] DWORD *pdwResult);
    HRESULT GetGuideLineW ([in] HIMC hIMC,[in] DWORD dwIndex,[in] DWORD dwBufLen,[out] LPWSTR pBuf,[out] DWORD *pdwResult);
    HRESULT GetIMEFileNameA ([in] HKL hKL,[in] UINT uBufLen,[out] LPSTR szFileName,[out] UINT *puCopied);
    HRESULT GetIMEFileNameW ([in] HKL hKL,[in] UINT uBufLen,[out] LPWSTR szFileName,[out] UINT *puCopied);
    HRESULT GetOpenStatus ([in] HIMC hIMC);
    HRESULT GetProperty ([in] HKL hKL,[in] DWORD fdwIndex,[out] DWORD *pdwProperty);
    HRESULT GetRegisterWordStyleA ([in] HKL hKL,[in] UINT nItem,[out] STYLEBUFA *pStyleBuf,[out] UINT *puCopied);
    HRESULT GetRegisterWordStyleW ([in] HKL hKL,[in] UINT nItem,[out] STYLEBUFW *pStyleBuf,[out] UINT *puCopied);
    HRESULT GetStatusWindowPos ([in] HIMC hIMC,[out] POINT *pptPos);
    HRESULT GetVirtualKey ([in] HWND hWnd,[out] UINT *puVirtualKey);
    HRESULT InstallIMEA ([in] LPSTR szIMEFileName,[in] LPSTR szLayoutText,[out] HKL *phKL);
    HRESULT InstallIMEW ([in] LPWSTR szIMEFileName,[in] LPWSTR szLayoutText,[out] HKL *phKL);
    HRESULT IsIME ([in] HKL hKL);
    HRESULT IsUIMessageA ([in] HWND hWndIME,[in] UINT msg,[in] WPARAM wParam,[in] LPARAM lParam);
    HRESULT IsUIMessageW ([in] HWND hWndIME,[in] UINT msg,[in] WPARAM wParam,[in] LPARAM lParam);
    HRESULT NotifyIME ([in] HIMC hIMC,[in] DWORD dwAction,[in] DWORD dwIndex,[in] DWORD dwValue);
    HRESULT RegisterWordA ([in] HKL hKL,[in] LPSTR szReading,[in] DWORD dwStyle,[in] LPSTR szRegister);
    HRESULT RegisterWordW ([in] HKL hKL,[in] LPWSTR szReading,[in] DWORD dwStyle,[in] LPWSTR szRegister);
    HRESULT ReleaseContext ([in] HWND hWnd,[in] HIMC hIMC);
    HRESULT SetCandidateWindow ([in] HIMC hIMC,[in] CANDIDATEFORM *pCandidate);
    HRESULT SetCompositionFontA ([in] HIMC hIMC,[in] LOGFONTA *plf);
    HRESULT SetCompositionFontW ([in] HIMC hIMC,[in] LOGFONTW *plf);
    HRESULT SetCompositionStringA ([in] HIMC hIMC,[in] DWORD dwIndex,[in] LPVOID pComp,[in] DWORD dwCompLen,[in] LPVOID pRead,[in] DWORD dwReadLen);
    HRESULT SetCompositionStringW ([in] HIMC hIMC,[in] DWORD dwIndex,[in] LPVOID pComp,[in] DWORD dwCompLen,[in] LPVOID pRead,[in] DWORD dwReadLen);
    HRESULT SetCompositionWindow ([in] HIMC hIMC,[in] COMPOSITIONFORM *pCompForm);
    HRESULT SetConversionStatus ([in] HIMC hIMC,[in] DWORD fdwConversion,[in] DWORD fdwSentence);
    HRESULT SetOpenStatus ([in] HIMC hIMC,[in] BOOL fOpen);
    HRESULT SetStatusWindowPos ([in] HIMC hIMC,[in] POINT *pptPos);
    HRESULT SimulateHotKey ([in] HWND hWnd,[in] DWORD dwHotKeyID);
    HRESULT UnregisterWordA ([in] HKL hKL,[in] LPSTR szReading,[in] DWORD dwStyle,[in] LPSTR szUnregister);
    HRESULT UnregisterWordW ([in] HKL hKL,[in] LPWSTR szReading,[in] DWORD dwStyle,[in] LPWSTR szUnregister);
    HRESULT Activate ([in] BOOL fRestoreLayout);
    HRESULT Deactivate ();
    HRESULT OnDefWindowProc ([in] HWND hWnd,[in] UINT Msg,[in] WPARAM wParam,[in] LPARAM lParam,[out] LRESULT *plResult);
    HRESULT FilterClientWindows ([in] ATOM *aaClassList,[in] UINT uSize);
    HRESULT GetCodePageA ([in] HKL hKL,[out] UINT *uCodePage);
    HRESULT GetLangId ([in] HKL hKL,[out] LANGID *plid);
    HRESULT AssociateContextEx ([in] HWND hWnd,[in] HIMC hIMC,[in] DWORD dwFlags);
    HRESULT DisableIME ([in] DWORD idThread);
    HRESULT GetImeMenuItemsA ([in] HIMC hIMC,[in] DWORD dwFlags,[in] DWORD dwType,[in] IMEMENUITEMINFOA *pImeParentMenu,[out] IMEMENUITEMINFOA *pImeMenu,[in] DWORD dwSize,[out] DWORD *pdwResult);
    HRESULT GetImeMenuItemsW ([in] HIMC hIMC,[in] DWORD dwFlags,[in] DWORD dwType,[in] IMEMENUITEMINFOW *pImeParentMenu,[out] IMEMENUITEMINFOW *pImeMenu,[in] DWORD dwSize,[out] DWORD *pdwResult);
    HRESULT EnumInputContext ([in] DWORD idThread,[out] IEnumInputContext **ppEnum);
  };

  [object, uuid (08c03411-F96B-11d0-A475-00aa006bcc59), pointer_default (unique)]
  interface IActiveIMMIME : IUnknown {
    HRESULT AssociateContext ([in] HWND hWnd,[in] HIMC hIME,[out] HIMC *phPrev);
    HRESULT ConfigureIMEA ([in] HKL hKL,[in] HWND hWnd,[in] DWORD dwMode,[in] REGISTERWORDA *pData);
    HRESULT ConfigureIMEW ([in] HKL hKL,[in] HWND hWnd,[in] DWORD dwMode,[in] REGISTERWORDW *pData);
    HRESULT CreateContext ([out] HIMC *phIMC);
    HRESULT DestroyContext ([in] HIMC hIME);
    HRESULT EnumRegisterWordA ([in] HKL hKL,[in] LPSTR szReading,[in] DWORD dwStyle,[in] LPSTR szRegister,[in] LPVOID pData,[out] IEnumRegisterWordA **pEnum);
    HRESULT EnumRegisterWordW ([in] HKL hKL,[in] LPWSTR szReading,[in] DWORD dwStyle,[in] LPWSTR szRegister,[in] LPVOID pData,[out] IEnumRegisterWordW **pEnum);
    HRESULT EscapeA ([in] HKL hKL,[in] HIMC hIMC,[in] UINT uEscape,[in, out] LPVOID pData,[out] LRESULT *plResult);
    HRESULT EscapeW ([in] HKL hKL,[in] HIMC hIMC,[in] UINT uEscape,[in, out] LPVOID pData,[out] LRESULT *plResult);
    HRESULT GetCandidateListA ([in] HIMC hIMC,[in] DWORD dwIndex,[in] UINT uBufLen,[out] CANDIDATELIST *pCandList,[out] UINT *puCopied);
    HRESULT GetCandidateListW ([in] HIMC hIMC,[in] DWORD dwIndex,[in] UINT uBufLen,[out] CANDIDATELIST *pCandList,[out] UINT *puCopied);
    HRESULT GetCandidateListCountA ([in] HIMC hIMC,[out] DWORD *pdwListSize,[out] DWORD *pdwBufLen);
    HRESULT GetCandidateListCountW ([in] HIMC hIMC,[out] DWORD *pdwListSize,[out] DWORD *pdwBufLen);
    HRESULT GetCandidateWindow ([in] HIMC hIMC,[in] DWORD dwIndex,[out] CANDIDATEFORM *pCandidate);
    HRESULT GetCompositionFontA ([in] HIMC hIMC,[out] LOGFONTA *plf);
    HRESULT GetCompositionFontW ([in] HIMC hIMC,[out] LOGFONTW *plf);
    HRESULT GetCompositionStringA ([in] HIMC hIMC,[in] DWORD dwIndex,[in] DWORD dwBufLen,[out] LONG *plCopied,[out] LPVOID pBuf);
    HRESULT GetCompositionStringW ([in] HIMC hIMC,[in] DWORD dwIndex,[in] DWORD dwBufLen,[out] LONG *plCopied,[out] LPVOID pBuf);
    HRESULT GetCompositionWindow ([in] HIMC hIMC,[out] COMPOSITIONFORM *pCompForm);
    HRESULT GetContext ([in] HWND hWnd,[out] HIMC *phIMC);
    HRESULT GetConversionListA ([in] HKL hKL,[in] HIMC hIMC,[in] LPSTR pSrc,[in] UINT uBufLen,[in] UINT uFlag,[out] CANDIDATELIST *pDst,[out] UINT *puCopied);
    HRESULT GetConversionListW ([in] HKL hKL,[in] HIMC hIMC,[in] LPWSTR pSrc,[in] UINT uBufLen,[in] UINT uFlag,[out] CANDIDATELIST *pDst,[out] UINT *puCopied);
    HRESULT GetConversionStatus ([in] HIMC hIMC,[out] DWORD *pfdwConversion,[out] DWORD *pfdwSentence);
    HRESULT GetDefaultIMEWnd ([in] HWND hWnd,[out] HWND *phDefWnd);
    HRESULT GetDescriptionA ([in] HKL hKL,[in] UINT uBufLen,[out] LPSTR szDescription,[out] UINT *puCopied);
    HRESULT GetDescriptionW ([in] HKL hKL,[in] UINT uBufLen,[out] LPWSTR szDescription,[out] UINT *puCopied);
    HRESULT GetGuideLineA ([in] HIMC hIMC,[in] DWORD dwIndex,[in] DWORD dwBufLen,[out] LPSTR pBuf,[out] DWORD *pdwResult);
    HRESULT GetGuideLineW ([in] HIMC hIMC,[in] DWORD dwIndex,[in] DWORD dwBufLen,[out] LPWSTR pBuf,[out] DWORD *pdwResult);
    HRESULT GetIMEFileNameA ([in] HKL hKL,[in] UINT uBufLen,[out] LPSTR szFileName,[out] UINT *puCopied);
    HRESULT GetIMEFileNameW ([in] HKL hKL,[in] UINT uBufLen,[out] LPWSTR szFileName,[out] UINT *puCopied);
    HRESULT GetOpenStatus ([in] HIMC hIMC);
    HRESULT GetProperty ([in] HKL hKL,[in] DWORD fdwIndex,[out] DWORD *pdwProperty);
    HRESULT GetRegisterWordStyleA ([in] HKL hKL,[in] UINT nItem,[out] STYLEBUFA *pStyleBuf,[out] UINT *puCopied);
    HRESULT GetRegisterWordStyleW ([in] HKL hKL,[in] UINT nItem,[out] STYLEBUFW *pStyleBuf,[out] UINT *puCopied);
    HRESULT GetStatusWindowPos ([in] HIMC hIMC,[out] POINT *pptPos);
    HRESULT GetVirtualKey ([in] HWND hWnd,[out] UINT *puVirtualKey);
    HRESULT InstallIMEA ([in] LPSTR szIMEFileName,[in] LPSTR szLayoutText,[out] HKL *phKL);
    HRESULT InstallIMEW ([in] LPWSTR szIMEFileName,[in] LPWSTR szLayoutText,[out] HKL *phKL);
    HRESULT IsIME ([in] HKL hKL);
    HRESULT IsUIMessageA ([in] HWND hWndIME,[in] UINT msg,[in] WPARAM wParam,[in] LPARAM lParam);
    HRESULT IsUIMessageW ([in] HWND hWndIME,[in] UINT msg,[in] WPARAM wParam,[in] LPARAM lParam);
    HRESULT NotifyIME ([in] HIMC hIMC,[in] DWORD dwAction,[in] DWORD dwIndex,[in] DWORD dwValue);
    HRESULT RegisterWordA ([in] HKL hKL,[in] LPSTR szReading,[in] DWORD dwStyle,[in] LPSTR szRegister);
    HRESULT RegisterWordW ([in] HKL hKL,[in] LPWSTR szReading,[in] DWORD dwStyle,[in] LPWSTR szRegister);
    HRESULT ReleaseContext ([in] HWND hWnd,[in] HIMC hIMC);
    HRESULT SetCandidateWindow ([in] HIMC hIMC,[in] CANDIDATEFORM *pCandidate);
    HRESULT SetCompositionFontA ([in] HIMC hIMC,[in] LOGFONTA *plf);
    HRESULT SetCompositionFontW ([in] HIMC hIMC,[in] LOGFONTW *plf);
    HRESULT SetCompositionStringA ([in] HIMC hIMC,[in] DWORD dwIndex,[in] LPVOID pComp,[in] DWORD dwCompLen,[in] LPVOID pRead,[in] DWORD dwReadLen);
    HRESULT SetCompositionStringW ([in] HIMC hIMC,[in] DWORD dwIndex,[in] LPVOID pComp,[in] DWORD dwCompLen,[in] LPVOID pRead,[in] DWORD dwReadLen);
    HRESULT SetCompositionWindow ([in] HIMC hIMC,[in] COMPOSITIONFORM *pCompForm);
    HRESULT SetConversionStatus ([in] HIMC hIMC,[in] DWORD fdwConversion,[in] DWORD fdwSentence);
    HRESULT SetOpenStatus ([in] HIMC hIMC,[in] BOOL fOpen);
    HRESULT SetStatusWindowPos ([in] HIMC hIMC,[in] POINT *pptPos);
    HRESULT SimulateHotKey ([in] HWND hWnd,[in] DWORD dwHotKeyID);
    HRESULT UnregisterWordA ([in] HKL hKL,[in] LPSTR szReading,[in] DWORD dwStyle,[in] LPSTR szUnregister);
    HRESULT UnregisterWordW ([in] HKL hKL,[in] LPWSTR szReading,[in] DWORD dwStyle,[in] LPWSTR szUnregister);
    HRESULT GenerateMessage ([in] HIMC hIMC);
    HRESULT LockIMC ([in] HIMC hIMC,[out] INPUTCONTEXT **ppIMC);
    HRESULT UnlockIMC ([in] HIMC hIMC);
    HRESULT GetIMCLockCount ([in] HIMC hIMC,[out] DWORD *pdwLockCount);
    HRESULT CreateIMCC ([in] DWORD dwSize,[out] HIMCC *phIMCC);
    HRESULT DestroyIMCC ([in] HIMCC hIMCC);
    HRESULT LockIMCC ([in] HIMCC hIMCC,[out] void **ppv);
    HRESULT UnlockIMCC ([in] HIMCC hIMCC);
    HRESULT ReSizeIMCC ([in] HIMCC hIMCC,[in] DWORD dwSize,[out] HIMCC *phIMCC);
    HRESULT GetIMCCSize ([in] HIMCC hIMCC,[out] DWORD *pdwSize);
    HRESULT GetIMCCLockCount ([in] HIMCC hIMCC,[out] DWORD *pdwLockCount);
    HRESULT GetHotKey ([in] DWORD dwHotKeyID,[out] UINT *puModifiers,[out] UINT *puVKey,[out] HKL *phKL);
    HRESULT SetHotKey ([in] DWORD dwHotKeyID,[in] UINT uModifiers,[in] UINT uVKey,[in] HKL hKL);
    HRESULT CreateSoftKeyboard ([in] UINT uType,[in] HWND hOwner,[in] int x,[in] int y,[out] HWND *phSoftKbdWnd);
    HRESULT DestroySoftKeyboard ([in] HWND hSoftKbdWnd);
    HRESULT ShowSoftKeyboard ([in] HWND hSoftKbdWnd,[in] int nCmdShow);
    HRESULT GetCodePageA ([in] HKL hKL,[out] UINT *uCodePage);
    HRESULT GetLangId ([in] HKL hKL,[out] LANGID *plid);
    HRESULT KeybdEvent ([in] LANGID lgidIME,[in] BYTE bVk,[in] BYTE bScan,[in] DWORD dwFlags,[in] DWORD dwExtraInfo);
    HRESULT LockModal ();
    HRESULT UnlockModal ();
    HRESULT AssociateContextEx ([in] HWND hWnd,[in] HIMC hIMC,[in] DWORD dwFlags);
    HRESULT DisableIME ([in] DWORD idThread);
    HRESULT GetImeMenuItemsA ([in] HIMC hIMC,[in] DWORD dwFlags,[in] DWORD dwType,[in] IMEMENUITEMINFOA *pImeParentMenu,[out] IMEMENUITEMINFOA *pImeMenu,[in] DWORD dwSize,[out] DWORD *pdwResult);
    HRESULT GetImeMenuItemsW ([in] HIMC hIMC,[in] DWORD dwFlags,[in] DWORD dwType,[in] IMEMENUITEMINFOW *pImeParentMenu,[out] IMEMENUITEMINFOW *pImeMenu,[in] DWORD dwSize,[out] DWORD *pdwResult);
    HRESULT EnumInputContext ([in] DWORD idThread,[out] IEnumInputContext **ppEnum);
    HRESULT RequestMessageA ([in] HIMC hIMC,[in] WPARAM wParam,[in] LPARAM lParam,[out] LRESULT *plResult);
    HRESULT RequestMessageW ([in] HIMC hIMC,[in] WPARAM wParam,[in] LPARAM lParam,[out] LRESULT *plResult);
    HRESULT SendIMCA ([in] HWND hWnd,[in] UINT uMsg,[in] WPARAM wParam,[in] LPARAM lParam,[out] LRESULT *plResult);
    HRESULT SendIMCW ([in] HWND hWnd,[in] UINT uMsg,[in] WPARAM wParam,[in] LPARAM lParam,[out] LRESULT *plResult);
    HRESULT IsSleeping ();
  };

  [object, uuid (6fe20962-D077-11d0-8fe7-00aa006bcc59), pointer_default (unique)]
  interface IActiveIME : IUnknown {
    HRESULT Inquire ([in] DWORD dwSystemInfoFlags,[out] IMEINFO *pIMEInfo,[out] LPWSTR szWndClass,[out] DWORD *pdwPrivate);
    HRESULT ConversionList ([in] HIMC hIMC,[in] LPWSTR szSource,[in] UINT uFlag,[in] UINT uBufLen,[out] CANDIDATELIST *pDest,[out] UINT *puCopied);
    HRESULT Configure ([in] HKL hKL,[in] HWND hWnd,[in] DWORD dwMode,[in] REGISTERWORDW *pRegisterWord);
    HRESULT Destroy ([in] UINT uReserved);
    HRESULT Escape ([in] HIMC hIMC,[in] UINT uEscape,[in, out] void *pData,[out] LRESULT *plResult);
    HRESULT SetActiveContext ([in] HIMC hIMC,[in] BOOL fFlag);
    HRESULT ProcessKey ([in] HIMC hIMC,[in] UINT uVirKey,[in] DWORD lParam,[in] BYTE *pbKeyState);
    HRESULT Notify ([in] HIMC hIMC,[in] DWORD dwAction,[in] DWORD dwIndex,[in] DWORD dwValue);
    HRESULT Select ([in] HIMC hIMC,[in] BOOL fSelect);
    HRESULT SetCompositionString ([in] HIMC hIMC,[in] DWORD dwIndex,[in] void *pComp,[in] DWORD dwCompLen,[in] void *pRead,[in] DWORD dwReadLen);
    HRESULT ToAsciiEx ([in] UINT uVirKey,[in] UINT uScanCode,[in] BYTE *pbKeyState,[in] UINT fuState,[in] HIMC hIMC,[out] DWORD *pdwTransBuf,[out] UINT *puSize);
    HRESULT RegisterWord ([in] LPWSTR szReading,[in] DWORD dwStyle,[in] LPWSTR szString);
    HRESULT UnregisterWord ([in] LPWSTR szReading,[in] DWORD dwStyle,[in] LPWSTR szString);
    HRESULT GetRegisterWordStyle ([in] UINT nItem,[out] STYLEBUFW *pStyleBuf,[out] UINT *puBufSize);
    HRESULT EnumRegisterWord ([in] LPWSTR szReading,[in] DWORD dwStyle,[in] LPWSTR szRegister,[in] LPVOID pData,[out] IEnumRegisterWordW **ppEnum);
    HRESULT GetCodePageA ([out] UINT *uCodePage);
    HRESULT GetLangId ([out] LANGID *plid);
  };

  [object, uuid (e1c4bf0e-2d53-11d2-93e1-0060b067b86e), pointer_default (unique)]
  interface IActiveIME2 : IActiveIME {
    HRESULT Sleep ();
    HRESULT Unsleep ([in] BOOL fDead);
  };

  [object, uuid (08c03412-F96B-11d0-A475-00aa006bcc59), pointer_default (unique)]
  interface IEnumRegisterWordA : IUnknown {
    HRESULT Clone ([out] IEnumRegisterWordA **ppEnum);
    HRESULT Next ([in] ULONG ulCount,[out] REGISTERWORDA *rgRegisterWord,[out] ULONG *pcFetched);
    HRESULT Reset ();
    HRESULT Skip ([in] ULONG ulCount);
  };

  [object, uuid (4955dd31-B159-11d0-8fcf-00aa006bcc59), pointer_default (unique)]
  interface IEnumRegisterWordW : IUnknown {
    HRESULT Clone ([out] IEnumRegisterWordW **ppEnum);
    HRESULT Next ([in] ULONG ulCount,[out] REGISTERWORDW *rgRegisterWord,[out] ULONG *pcFetched);
    HRESULT Reset ();
    HRESULT Skip ([in] ULONG ulCount);
  };

  [uuid (4955dd33-B159-11d0-8fcf-00aa006bcc59)]
  coclass CActiveIMM {
    [default] interface IActiveIMMApp;
    interface IActiveIMMIME;
    interface IActiveIMMRegistrar;
    interface IActiveIMMMessagePumpOwner;
  };
}
cpp_quote("#endif")
