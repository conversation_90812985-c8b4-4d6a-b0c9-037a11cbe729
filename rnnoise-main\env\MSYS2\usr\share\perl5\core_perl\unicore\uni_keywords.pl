# -*- mode: Perl; buffer-read-only: t -*-
# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is built by regen/mk_invlists.pl from Unicode::UCD.
# Any changes made here will be lost!

%Unicode::UCD::uni_prop_ptrs_indices = (
    '_perl_any_folds' => 1222,
    '_perl_charname_begin' => 1223,
    '_perl_charname_continue' => 1224,
    '_perl_folds_to_multi_char' => 1225,
    '_perl_idcont' => 1226,
    '_perl_idstart' => 1227,
    '_perl_is_in_multi_char_fold' => 1228,
    '_perl_nchar' => 1229,
    '_perl_patws' => 1230,
    '_perl_problematic_locale_foldeds_start' => 1231,
    '_perl_problematic_locale_folds' => 1232,
    '_perl_quotemeta' => 1233,
    '_perl_surrogate' => 1234,
    'adlm' => 1,
    'aegeannumbers' => 2,
    'age=10' => 3,
    'age=11' => 4,
    'age=12' => 5,
    'age=12.1' => 6,
    'age=13' => 7,
    'age=14' => 8,
    'age=15' => 9,
    'age=2' => 10,
    'age=2.1' => 11,
    'age=3' => 12,
    'age=3.1' => 13,
    'age=3.2' => 14,
    'age=4' => 15,
    'age=4.1' => 16,
    'age=5' => 17,
    'age=5.1' => 18,
    'age=5.2' => 19,
    'age=6' => 20,
    'age=6.1' => 21,
    'age=6.2' => 22,
    'age=6.3' => 23,
    'age=7' => 24,
    'age=8' => 25,
    'age=9' => 26,
    'aghb' => 27,
    'ahex' => 997,
    'ahom' => 28,
    'alchemical' => 29,
    'all' => 30,
    'alnum' => 1195,
    'alpha' => 1196,
    'alphabetic' => 1196,
    'alphabeticpf' => 31,
    'ancientgreekmusic' => 32,
    'ancientgreeknumbers' => 33,
    'ancientsymbols' => 34,
    'any' => 35,
    'arab' => 36,
    'arabicexta' => 37,
    'arabicextb' => 38,
    'arabicextc' => 39,
    'arabicmath' => 40,
    'arabicpfa' => 41,
    'arabicpfb' => 42,
    'arabicsup' => 43,
    'armi' => 44,
    'armn' => 45,
    'arrows' => 46,
    'ascii' => 47,
    'asciihexdigit' => 997,
    'assigned' => 48,
    'avst' => 49,
    'bali' => 50,
    'bamu' => 51,
    'bamumsup' => 52,
    'basiclatin' => 47,
    'bass' => 53,
    'batk' => 54,
    'bc=al' => 55,
    'bc=an' => 56,
    'bc=b' => 57,
    'bc=bn' => 58,
    'bc=cs' => 59,
    'bc=en' => 60,
    'bc=es' => 61,
    'bc=et' => 62,
    'bc=fsi' => 63,
    'bc=l' => 64,
    'bc=lre' => 65,
    'bc=lri' => 66,
    'bc=lro' => 67,
    'bc=nsm' => 68,
    'bc=on' => 69,
    'bc=pdf' => 70,
    'bc=pdi' => 71,
    'bc=r' => 72,
    'bc=rle' => 73,
    'bc=rli' => 74,
    'bc=rlo' => 75,
    'bc=s' => 76,
    'bc=ws' => 77,
    'beng' => 78,
    'bhks' => 79,
    'bidic' => 80,
    'bidim' => 81,
    'blank' => 1197,
    'blockelements' => 82,
    'bopo' => 83,
    'bopomofoext' => 84,
    'boxdrawing' => 85,
    'bpt=c' => 86,
    'bpt=n' => 87,
    'bpt=o' => 88,
    'brah' => 89,
    'brai' => 90,
    'bugi' => 91,
    'buhd' => 92,
    'byzantinemusic' => 93,
    'c' => 94,
    'cakm' => 95,
    'cans' => 96,
    'cari' => 97,
    'cased' => 98,
    'casedletter' => 99,
    'cc' => 1198,
    'ccc=0' => 100,
    'ccc=1' => 101,
    'ccc=10' => 102,
    'ccc=103' => 103,
    'ccc=107' => 104,
    'ccc=11' => 105,
    'ccc=118' => 106,
    'ccc=12' => 107,
    'ccc=122' => 108,
    'ccc=129' => 109,
    'ccc=13' => 110,
    'ccc=130' => 111,
    'ccc=132' => 112,
    'ccc=14' => 113,
    'ccc=15' => 114,
    'ccc=16' => 115,
    'ccc=17' => 116,
    'ccc=18' => 117,
    'ccc=19' => 118,
    'ccc=20' => 119,
    'ccc=202' => 120,
    'ccc=21' => 121,
    'ccc=214' => 122,
    'ccc=216' => 123,
    'ccc=22' => 124,
    'ccc=23' => 125,
    'ccc=24' => 126,
    'ccc=25' => 127,
    'ccc=26' => 128,
    'ccc=27' => 129,
    'ccc=28' => 130,
    'ccc=29' => 131,
    'ccc=30' => 132,
    'ccc=31' => 133,
    'ccc=32' => 134,
    'ccc=33' => 135,
    'ccc=34' => 136,
    'ccc=35' => 137,
    'ccc=36' => 138,
    'ccc=6' => 139,
    'ccc=7' => 140,
    'ccc=8' => 141,
    'ccc=84' => 142,
    'ccc=9' => 143,
    'ccc=91' => 144,
    'ccc=a' => 145,
    'ccc=al' => 146,
    'ccc=ar' => 147,
    'ccc=b' => 148,
    'ccc=bl' => 149,
    'ccc=br' => 150,
    'ccc=da' => 151,
    'ccc=db' => 152,
    'ccc=is' => 153,
    'ccc=l' => 154,
    'ccc=r' => 155,
    'ce' => 156,
    'cf' => 157,
    'cham' => 158,
    'cher' => 159,
    'cherokeesup' => 160,
    'chesssymbols' => 161,
    'chrs' => 162,
    'ci' => 163,
    'cjk' => 164,
    'cjkcompat' => 165,
    'cjkcompatforms' => 166,
    'cjkcompatideographs' => 167,
    'cjkcompatideographssup' => 168,
    'cjkexta' => 169,
    'cjkextb' => 170,
    'cjkextc' => 171,
    'cjkextd' => 172,
    'cjkexte' => 173,
    'cjkextf' => 174,
    'cjkextg' => 175,
    'cjkexth' => 176,
    'cjkradicalssup' => 177,
    'cjkstrokes' => 178,
    'cjksymbols' => 179,
    'cn' => 180,
    'cntrl' => 1198,
    'co' => 181,
    'compatjamo' => 182,
    'compex' => 183,
    'control' => 1198,
    'controlpictures' => 184,
    'copt' => 185,
    'copticepactnumbers' => 186,
    'countingrod' => 187,
    'cpmn' => 188,
    'cprt' => 189,
    'cuneiformnumbers' => 190,
    'currencysymbols' => 191,
    'cwcf' => 192,
    'cwcm' => 193,
    'cwkcf' => 194,
    'cwl' => 195,
    'cwt' => 196,
    'cwu' => 197,
    'cypriotsyllabary' => 198,
    'cyrillicexta' => 199,
    'cyrillicextb' => 200,
    'cyrillicextc' => 201,
    'cyrillicextd' => 202,
    'cyrillicsup' => 203,
    'cyrl' => 204,
    'dash' => 205,
    'decimalnumber' => 1199,
    'dep' => 206,
    'deva' => 207,
    'devanagariext' => 208,
    'devanagariexta' => 209,
    'di' => 210,
    'dia' => 211,
    'diacriticals' => 212,
    'diacriticalsext' => 213,
    'diacriticalsforsymbols' => 214,
    'diacriticalssup' => 215,
    'diak' => 216,
    'digit' => 1199,
    'dingbats' => 217,
    'dogr' => 218,
    'domino' => 219,
    'dsrt' => 220,
    'dt=can' => 221,
    'dt=com' => 222,
    'dt=enc' => 223,
    'dt=fin' => 224,
    'dt=font' => 225,
    'dt=fra' => 226,
    'dt=init' => 227,
    'dt=iso' => 228,
    'dt=med' => 229,
    'dt=nar' => 230,
    'dt=nb' => 231,
    'dt=noncanon' => 232,
    'dt=none' => 233,
    'dt=sml' => 234,
    'dt=sqr' => 235,
    'dt=sub' => 236,
    'dt=sup' => 237,
    'dt=vert' => 238,
    'dupl' => 239,
    'ea=a' => 241,
    'ea=f' => 242,
    'ea=h' => 243,
    'ea=n' => 244,
    'ea=na' => 245,
    'ea=w' => 246,
    'earlydynasticcuneiform' => 240,
    'ebase' => 247,
    'ecomp' => 248,
    'egyp' => 249,
    'egyptianhieroglyphformatcontrols' => 250,
    'elba' => 251,
    'elym' => 252,
    'emod' => 253,
    'emoji' => 254,
    'emoticons' => 255,
    'enclosedalphanum' => 256,
    'enclosedalphanumsup' => 257,
    'enclosedcjk' => 258,
    'enclosedideographicsup' => 259,
    'epres' => 260,
    'ethi' => 261,
    'ethiopicext' => 262,
    'ethiopicexta' => 263,
    'ethiopicextb' => 264,
    'ethiopicsup' => 265,
    'ext' => 266,
    'extpict' => 267,
    'gcb=cn' => 268,
    'gcb=ex' => 269,
    'gcb=l' => 270,
    'gcb=pp' => 271,
    'gcb=sm' => 272,
    'gcb=t' => 273,
    'gcb=v' => 274,
    'gcb=xx' => 275,
    'geometricshapes' => 276,
    'geometricshapesext' => 277,
    'geor' => 278,
    'georgianext' => 279,
    'georgiansup' => 280,
    'glag' => 281,
    'glagoliticsup' => 282,
    'gong' => 283,
    'gonm' => 284,
    'goth' => 285,
    'gran' => 286,
    'graph' => 1200,
    'grbase' => 287,
    'greekext' => 288,
    'grek' => 289,
    'grext' => 290,
    'gujr' => 291,
    'guru' => 292,
    'halfandfullforms' => 293,
    'halfmarks' => 294,
    'han' => 295,
    'hang' => 296,
    'hano' => 297,
    'hatr' => 298,
    'hebr' => 299,
    'hex' => 1207,
    'hexdigit' => 1207,
    'highpusurrogates' => 300,
    'highsurrogates' => 301,
    'hira' => 302,
    'hluw' => 303,
    'hmng' => 304,
    'hmnp' => 305,
    'horizspace' => 1197,
    'hst=na' => 306,
    'hung' => 307,
    'hyphen_perl_aux' => 308,
    'idc' => 309,
    'identifierstatus=allowed' => 310,
    'identifierstatus=restricted' => 311,
    'identifiertype=defaultignorable' => 312,
    'identifiertype=exclusion' => 313,
    'identifiertype=inclusion' => 314,
    'identifiertype=limiteduse' => 315,
    'identifiertype=notcharacter' => 316,
    'identifiertype=notnfkc' => 317,
    'identifiertype=notxid' => 318,
    'identifiertype=obsolete' => 319,
    'identifiertype=recommended' => 320,
    'identifiertype=technical' => 321,
    'identifiertype=uncommonuse' => 322,
    'ideo' => 323,
    'ideographicsymbols' => 324,
    'ids' => 325,
    'idsb' => 326,
    'idst' => 327,
    'in=1.1' => 534,
    'in=10' => 527,
    'in=11' => 528,
    'in=12' => 529,
    'in=12.1' => 530,
    'in=13' => 531,
    'in=14' => 532,
    'in=15' => 533,
    'in=2' => 535,
    'in=2.1' => 536,
    'in=3' => 537,
    'in=3.1' => 538,
    'in=3.2' => 539,
    'in=4' => 540,
    'in=4.1' => 541,
    'in=5' => 542,
    'in=5.1' => 543,
    'in=5.2' => 544,
    'in=6' => 545,
    'in=6.1' => 546,
    'in=6.2' => 547,
    'in=6.3' => 548,
    'in=7' => 549,
    'in=8' => 550,
    'in=9' => 551,
    'in=na' => 552,
    'inadlam' => 328,
    'inahom' => 329,
    'inanatolianhieroglyphs' => 330,
    'inarabic' => 331,
    'inarmenian' => 332,
    'inavestan' => 333,
    'inbalinese' => 334,
    'inbamum' => 335,
    'inbassavah' => 336,
    'inbatak' => 337,
    'inbengali' => 338,
    'inbhaiksuki' => 339,
    'inbopomofo' => 340,
    'inbrahmi' => 341,
    'inbuginese' => 342,
    'inbuhid' => 343,
    'incarian' => 344,
    'incaucasianalbanian' => 345,
    'inchakma' => 346,
    'incham' => 347,
    'incherokee' => 348,
    'inchorasmian' => 349,
    'incoptic' => 350,
    'incuneiform' => 351,
    'incyprominoan' => 352,
    'incyrillic' => 353,
    'indevanagari' => 354,
    'indicnumberforms' => 355,
    'indicsiyaqnumbers' => 356,
    'indivesakuru' => 357,
    'indogra' => 358,
    'induployan' => 359,
    'inegyptianhieroglyphs' => 360,
    'inelbasan' => 361,
    'inelymaic' => 362,
    'inethiopic' => 363,
    'ingeorgian' => 364,
    'inglagolitic' => 365,
    'ingothic' => 366,
    'ingrantha' => 367,
    'ingreek' => 368,
    'ingujarati' => 369,
    'ingunjalagondi' => 370,
    'ingurmukhi' => 371,
    'inhangul' => 372,
    'inhanifirohingya' => 373,
    'inhanunoo' => 374,
    'inhatran' => 375,
    'inhebrew' => 376,
    'inhiragana' => 377,
    'inidc' => 378,
    'inimperialaramaic' => 379,
    'ininscriptionalpahlavi' => 380,
    'ininscriptionalparthian' => 381,
    'injavanese' => 382,
    'inkaithi' => 383,
    'inkannada' => 384,
    'inkatakana' => 385,
    'inkawi' => 386,
    'inkharoshthi' => 387,
    'inkhitansmallscript' => 388,
    'inkhmer' => 389,
    'inkhojki' => 390,
    'inkhudawadi' => 391,
    'inlao' => 392,
    'inlepcha' => 393,
    'inlimbu' => 394,
    'inlineara' => 395,
    'inlisu' => 396,
    'inlycian' => 397,
    'inlydian' => 398,
    'inmahajani' => 399,
    'inmakasar' => 400,
    'inmalayalam' => 401,
    'inmandaic' => 402,
    'inmanichaean' => 403,
    'inmarchen' => 404,
    'inmasaramgondi' => 405,
    'inmedefaidrin' => 406,
    'inmeeteimayek' => 407,
    'inmendekikakui' => 408,
    'inmeroiticcursive' => 409,
    'inmiao' => 410,
    'inmodi' => 411,
    'inmongolian' => 412,
    'inmro' => 413,
    'inmultani' => 414,
    'inmyanmar' => 415,
    'innabataean' => 416,
    'innagmundari' => 417,
    'innandinagari' => 418,
    'innewa' => 419,
    'innewtailue' => 420,
    'innko' => 421,
    'innushu' => 422,
    'innyiakengpuachuehmong' => 423,
    'inogham' => 424,
    'inoldhungarian' => 425,
    'inolditalic' => 426,
    'inoldpermic' => 427,
    'inoldpersian' => 428,
    'inoldsogdian' => 429,
    'inoldturkic' => 430,
    'inolduyghur' => 431,
    'inoriya' => 432,
    'inosage' => 433,
    'inosmanya' => 434,
    'inpahawhhmong' => 435,
    'inpaucinhau' => 436,
    'inpc=bottom' => 437,
    'inpc=bottomandleft' => 438,
    'inpc=bottomandright' => 439,
    'inpc=left' => 440,
    'inpc=leftandright' => 441,
    'inpc=na' => 442,
    'inpc=overstruck' => 443,
    'inpc=right' => 444,
    'inpc=top' => 445,
    'inpc=topandbottom' => 446,
    'inpc=topandbottomandleft' => 447,
    'inpc=topandbottomandright' => 448,
    'inpc=topandleft' => 449,
    'inpc=topandleftandright' => 450,
    'inpc=topandright' => 451,
    'inphagspa' => 452,
    'inphoenician' => 453,
    'inpsalterpahlavi' => 454,
    'inpunctuation' => 455,
    'inrejang' => 456,
    'inrunic' => 457,
    'insamaritan' => 458,
    'insaurashtra' => 459,
    'insc=avagraha' => 460,
    'insc=bindu' => 461,
    'insc=brahmijoiningnumber' => 462,
    'insc=cantillationmark' => 463,
    'insc=consonant' => 464,
    'insc=consonantdead' => 465,
    'insc=consonantfinal' => 466,
    'insc=consonantheadletter' => 467,
    'insc=consonantinitialpostfixed' => 468,
    'insc=consonantkiller' => 469,
    'insc=consonantmedial' => 470,
    'insc=consonantplaceholder' => 471,
    'insc=consonantprecedingrepha' => 472,
    'insc=consonantprefixed' => 473,
    'insc=consonantsubjoined' => 474,
    'insc=consonantsucceedingrepha' => 475,
    'insc=consonantwithstacker' => 476,
    'insc=geminationmark' => 477,
    'insc=invisiblestacker' => 478,
    'insc=modifyingletter' => 479,
    'insc=nonjoiner' => 480,
    'insc=nukta' => 481,
    'insc=number' => 482,
    'insc=numberjoiner' => 483,
    'insc=other' => 484,
    'insc=purekiller' => 485,
    'insc=registershifter' => 486,
    'insc=syllablemodifier' => 487,
    'insc=toneletter' => 488,
    'insc=tonemark' => 489,
    'insc=virama' => 490,
    'insc=visarga' => 491,
    'insc=vowel' => 492,
    'insc=voweldependent' => 493,
    'insc=vowelindependent' => 494,
    'insiddham' => 495,
    'insinhala' => 496,
    'insogdian' => 497,
    'insorasompeng' => 498,
    'insoyombo' => 499,
    'insundanese' => 500,
    'insylotinagri' => 501,
    'insyriac' => 502,
    'intagalog' => 503,
    'intagbanwa' => 504,
    'intaile' => 505,
    'intaitham' => 506,
    'intaiviet' => 507,
    'intakri' => 508,
    'intamil' => 509,
    'intangsa' => 510,
    'intangut' => 511,
    'intelugu' => 512,
    'inthaana' => 513,
    'inthai' => 514,
    'intibetan' => 515,
    'intifinagh' => 516,
    'intirhuta' => 517,
    'intoto' => 518,
    'inugaritic' => 519,
    'invai' => 520,
    'invithkuqi' => 521,
    'invs' => 522,
    'inwancho' => 523,
    'inwarangciti' => 524,
    'inyezidi' => 525,
    'inzanabazarsquare' => 526,
    'ipaext' => 553,
    'ital' => 554,
    'jamo' => 555,
    'jamoexta' => 556,
    'jamoextb' => 557,
    'java' => 558,
    'jg=africanfeh' => 559,
    'jg=africannoon' => 560,
    'jg=africanqaf' => 561,
    'jg=ain' => 562,
    'jg=alaph' => 563,
    'jg=alef' => 564,
    'jg=beh' => 565,
    'jg=beth' => 566,
    'jg=burushaskiyehbarree' => 567,
    'jg=dal' => 568,
    'jg=dalathrish' => 569,
    'jg=e' => 570,
    'jg=farsiyeh' => 571,
    'jg=fe' => 572,
    'jg=feh' => 573,
    'jg=finalsemkath' => 574,
    'jg=gaf' => 575,
    'jg=gamal' => 576,
    'jg=hah' => 577,
    'jg=hamzaonhehgoal' => 578,
    'jg=hanifirohingyakinnaya' => 579,
    'jg=hanifirohingyapa' => 580,
    'jg=he' => 581,
    'jg=heh' => 582,
    'jg=hehgoal' => 583,
    'jg=heth' => 584,
    'jg=kaf' => 585,
    'jg=kaph' => 586,
    'jg=khaph' => 587,
    'jg=knottedheh' => 588,
    'jg=lam' => 589,
    'jg=lamadh' => 590,
    'jg=malayalambha' => 591,
    'jg=malayalamja' => 592,
    'jg=malayalamlla' => 593,
    'jg=malayalamllla' => 594,
    'jg=malayalamnga' => 595,
    'jg=malayalamnna' => 596,
    'jg=malayalamnnna' => 597,
    'jg=malayalamnya' => 598,
    'jg=malayalamra' => 599,
    'jg=malayalamssa' => 600,
    'jg=malayalamtta' => 601,
    'jg=manichaeanaleph' => 602,
    'jg=manichaeanayin' => 603,
    'jg=manichaeanbeth' => 604,
    'jg=manichaeandaleth' => 605,
    'jg=manichaeandhamedh' => 606,
    'jg=manichaeanfive' => 607,
    'jg=manichaeangimel' => 608,
    'jg=manichaeanheth' => 609,
    'jg=manichaeanhundred' => 610,
    'jg=manichaeankaph' => 611,
    'jg=manichaeanlamedh' => 612,
    'jg=manichaeanmem' => 613,
    'jg=manichaeannun' => 614,
    'jg=manichaeanone' => 615,
    'jg=manichaeanpe' => 616,
    'jg=manichaeanqoph' => 617,
    'jg=manichaeanresh' => 618,
    'jg=manichaeansadhe' => 619,
    'jg=manichaeansamekh' => 620,
    'jg=manichaeantaw' => 621,
    'jg=manichaeanten' => 622,
    'jg=manichaeanteth' => 623,
    'jg=manichaeanthamedh' => 624,
    'jg=manichaeantwenty' => 625,
    'jg=manichaeanwaw' => 626,
    'jg=manichaeanyodh' => 627,
    'jg=manichaeanzayin' => 628,
    'jg=meem' => 629,
    'jg=mim' => 630,
    'jg=nojoininggroup' => 631,
    'jg=noon' => 632,
    'jg=nun' => 633,
    'jg=nya' => 634,
    'jg=pe' => 635,
    'jg=qaf' => 636,
    'jg=qaph' => 637,
    'jg=reh' => 638,
    'jg=reversedpe' => 639,
    'jg=rohingyayeh' => 640,
    'jg=sad' => 641,
    'jg=sadhe' => 642,
    'jg=seen' => 643,
    'jg=semkath' => 644,
    'jg=shin' => 645,
    'jg=straightwaw' => 646,
    'jg=swashkaf' => 647,
    'jg=syriacwaw' => 648,
    'jg=tah' => 649,
    'jg=taw' => 650,
    'jg=tehmarbuta' => 651,
    'jg=teth' => 652,
    'jg=thinyeh' => 653,
    'jg=verticaltail' => 654,
    'jg=waw' => 655,
    'jg=yeh' => 656,
    'jg=yehbarree' => 657,
    'jg=yehwithtail' => 658,
    'jg=yudh' => 659,
    'jg=yudhhe' => 660,
    'jg=zain' => 661,
    'jg=zhain' => 662,
    'joinc' => 663,
    'jt=c' => 664,
    'jt=d' => 665,
    'jt=l' => 666,
    'jt=r' => 667,
    'jt=t' => 668,
    'jt=u' => 669,
    'kaktoviknumerals' => 670,
    'kali' => 671,
    'kana' => 672,
    'kanaexta' => 673,
    'kanaextb' => 674,
    'kanasup' => 675,
    'kanbun' => 676,
    'kangxi' => 677,
    'katakanaext' => 678,
    'kawi' => 679,
    'khar' => 680,
    'khmersymbols' => 681,
    'khmr' => 682,
    'khoj' => 683,
    'kits' => 684,
    'knda' => 685,
    'kthi' => 686,
    'l' => 687,
    'l_' => 99,
    'l_amp_' => 99,
    'lana' => 688,
    'lao' => 689,
    'latin1' => 690,
    'latinexta' => 691,
    'latinextadditional' => 692,
    'latinextb' => 693,
    'latinextc' => 694,
    'latinextd' => 695,
    'latinexte' => 696,
    'latinextf' => 697,
    'latinextg' => 698,
    'latn' => 699,
    'lb=ai' => 700,
    'lb=al' => 701,
    'lb=b2' => 702,
    'lb=ba' => 703,
    'lb=bb' => 704,
    'lb=bk' => 705,
    'lb=cb' => 706,
    'lb=cj' => 707,
    'lb=cl' => 708,
    'lb=cm' => 709,
    'lb=cp' => 710,
    'lb=cr' => 711,
    'lb=ex' => 712,
    'lb=gl' => 713,
    'lb=h2' => 714,
    'lb=h3' => 715,
    'lb=hl' => 716,
    'lb=hy' => 717,
    'lb=id' => 718,
    'lb=in' => 719,
    'lb=is' => 720,
    'lb=lf' => 721,
    'lb=nl' => 722,
    'lb=ns' => 723,
    'lb=nu' => 724,
    'lb=op' => 725,
    'lb=po' => 726,
    'lb=pr' => 727,
    'lb=qu' => 728,
    'lb=sa' => 729,
    'lb=sg_perl_aux' => 730,
    'lb=sp' => 731,
    'lb=sy' => 732,
    'lb=wj' => 733,
    'lb=xx' => 734,
    'lb=zw' => 735,
    'lb=zwj' => 736,
    'lc' => 99,
    'lepc' => 737,
    'letterlikesymbols' => 738,
    'limb' => 739,
    'lina' => 740,
    'linb' => 741,
    'linearbideograms' => 742,
    'linearbsyllabary' => 743,
    'lisu' => 744,
    'lisusup' => 745,
    'll' => 749,
    'lm' => 746,
    'lo' => 747,
    'loe' => 748,
    'lower' => 1201,
    'lowercase' => 1201,
    'lowercaseletter' => 749,
    'lowsurrogates' => 750,
    'lt' => 1154,
    'lu' => 1163,
    'lyci' => 751,
    'lydi' => 752,
    'm' => 753,
    'mahj' => 754,
    'mahjong' => 755,
    'maka' => 756,
    'mand' => 757,
    'mani' => 758,
    'marc' => 759,
    'math' => 760,
    'mathalphanum' => 761,
    'mathoperators' => 762,
    'mayannumerals' => 763,
    'mc' => 764,
    'me' => 765,
    'medf' => 766,
    'meeteimayekext' => 767,
    'mend' => 768,
    'merc' => 769,
    'mero' => 770,
    'miao' => 771,
    'miscarrows' => 772,
    'miscmathsymbolsa' => 773,
    'miscmathsymbolsb' => 774,
    'miscpictographs' => 775,
    'miscsymbols' => 776,
    'misctechnical' => 777,
    'mlym' => 778,
    'mn' => 779,
    'modi' => 780,
    'modifierletters' => 781,
    'modifiertoneletters' => 782,
    'mong' => 783,
    'mongoliansup' => 784,
    'mro' => 785,
    'mtei' => 786,
    'mult' => 787,
    'music' => 788,
    'myanmarexta' => 789,
    'myanmarextb' => 790,
    'mymr' => 791,
    'n' => 792,
    'nagm' => 793,
    'nand' => 794,
    'narb' => 795,
    'nb' => 796,
    'nbat' => 797,
    'nd' => 1199,
    'newa' => 798,
    'nfcqc=m' => 799,
    'nfcqc=y' => 800,
    'nfdqc=y' => 801,
    'nfkcqc=n' => 802,
    'nfkcqc=y' => 803,
    'nfkdqc=n' => 804,
    'nko' => 805,
    'nl' => 806,
    'no' => 807,
    'nshu' => 808,
    'nt=di' => 809,
    'nt=nu' => 810,
    'numberforms' => 811,
    'nv=0' => 812,
    'nv=1' => 813,
    'nv=1/10' => 838,
    'nv=1/12' => 839,
    'nv=1/16' => 840,
    'nv=1/160' => 841,
    'nv=1/2' => 842,
    'nv=1/20' => 843,
    'nv=1/3' => 844,
    'nv=1/32' => 845,
    'nv=1/320' => 846,
    'nv=1/4' => 847,
    'nv=1/40' => 848,
    'nv=1/5' => 849,
    'nv=1/6' => 850,
    'nv=1/64' => 851,
    'nv=1/7' => 852,
    'nv=1/8' => 853,
    'nv=1/80' => 854,
    'nv=1/9' => 855,
    'nv=10' => 814,
    'nv=100' => 815,
    'nv=1000' => 816,
    'nv=10000' => 817,
    'nv=100000' => 818,
    'nv=1000000' => 819,
    'nv=10000000' => 820,
    'nv=100000000' => 821,
    'nv=10000000000' => 822,
    'nv=1000000000000' => 823,
    'nv=11' => 824,
    'nv=11/12' => 825,
    'nv=11/2' => 826,
    'nv=12' => 827,
    'nv=13' => 828,
    'nv=13/2' => 829,
    'nv=14' => 830,
    'nv=15' => 831,
    'nv=15/2' => 832,
    'nv=16' => 833,
    'nv=17' => 834,
    'nv=17/2' => 835,
    'nv=18' => 836,
    'nv=19' => 837,
    'nv=2' => 856,
    'nv=2/3' => 873,
    'nv=2/5' => 874,
    'nv=20' => 857,
    'nv=200' => 858,
    'nv=2000' => 859,
    'nv=20000' => 860,
    'nv=200000' => 861,
    'nv=20000000' => 862,
    'nv=21' => 863,
    'nv=216000' => 864,
    'nv=22' => 865,
    'nv=23' => 866,
    'nv=24' => 867,
    'nv=25' => 868,
    'nv=26' => 869,
    'nv=27' => 870,
    'nv=28' => 871,
    'nv=29' => 872,
    'nv=3' => 875,
    'nv=3/16' => 890,
    'nv=3/2' => 891,
    'nv=3/20' => 892,
    'nv=3/4' => 893,
    'nv=3/5' => 894,
    'nv=3/64' => 895,
    'nv=3/8' => 896,
    'nv=3/80' => 897,
    'nv=30' => 876,
    'nv=300' => 877,
    'nv=3000' => 878,
    'nv=30000' => 879,
    'nv=300000' => 880,
    'nv=31' => 881,
    'nv=32' => 882,
    'nv=33' => 883,
    'nv=34' => 884,
    'nv=35' => 885,
    'nv=36' => 886,
    'nv=37' => 887,
    'nv=38' => 888,
    'nv=39' => 889,
    'nv=4' => 898,
    'nv=4/5' => 914,
    'nv=40' => 899,
    'nv=400' => 900,
    'nv=4000' => 901,
    'nv=40000' => 902,
    'nv=400000' => 903,
    'nv=41' => 904,
    'nv=42' => 905,
    'nv=43' => 906,
    'nv=432000' => 907,
    'nv=44' => 908,
    'nv=45' => 909,
    'nv=46' => 910,
    'nv=47' => 911,
    'nv=48' => 912,
    'nv=49' => 913,
    'nv=5' => 915,
    'nv=5/12' => 921,
    'nv=5/2' => 922,
    'nv=5/6' => 923,
    'nv=5/8' => 924,
    'nv=50' => 916,
    'nv=500' => 917,
    'nv=5000' => 918,
    'nv=50000' => 919,
    'nv=500000' => 920,
    'nv=6' => 925,
    'nv=60' => 926,
    'nv=600' => 927,
    'nv=6000' => 928,
    'nv=60000' => 929,
    'nv=600000' => 930,
    'nv=7' => 931,
    'nv=7/12' => 937,
    'nv=7/2' => 938,
    'nv=7/8' => 939,
    'nv=70' => 932,
    'nv=700' => 933,
    'nv=7000' => 934,
    'nv=70000' => 935,
    'nv=700000' => 936,
    'nv=8' => 940,
    'nv=80' => 941,
    'nv=800' => 942,
    'nv=8000' => 943,
    'nv=80000' => 944,
    'nv=800000' => 945,
    'nv=9' => 946,
    'nv=9/2' => 952,
    'nv=90' => 947,
    'nv=900' => 948,
    'nv=9000' => 949,
    'nv=90000' => 950,
    'nv=900000' => 951,
    'nv=_minus_1/2' => 954,
    'nv=nan' => 953,
    'ocr' => 955,
    'ogam' => 956,
    'olck' => 957,
    'orkh' => 958,
    'ornamentaldingbats' => 959,
    'orya' => 960,
    'osge' => 961,
    'osma' => 962,
    'ottomansiyaqnumbers' => 963,
    'ougr' => 964,
    'p' => 965,
    'palm' => 966,
    'patsyn' => 967,
    'pauc' => 968,
    'pc' => 969,
    'pcm' => 970,
    'pd' => 971,
    'pe' => 972,
    'perlspace' => 994,
    'perlword' => 996,
    'perm' => 973,
    'pf' => 974,
    'phag' => 975,
    'phaistos' => 976,
    'phli' => 977,
    'phlp' => 978,
    'phnx' => 979,
    'phoneticext' => 980,
    'phoneticextsup' => 981,
    'pi' => 982,
    'playingcards' => 983,
    'po' => 984,
    'posixalnum' => 985,
    'posixalpha' => 986,
    'posixblank' => 987,
    'posixcntrl' => 988,
    'posixdigit' => 989,
    'posixgraph' => 990,
    'posixlower' => 991,
    'posixprint' => 992,
    'posixpunct' => 993,
    'posixspace' => 994,
    'posixupper' => 995,
    'posixword' => 996,
    'posixxdigit' => 997,
    'print' => 1202,
    'prti' => 998,
    'ps' => 999,
    'pua' => 1000,
    'qaai' => 1001,
    'qmark' => 1002,
    'radical' => 1003,
    'ri' => 1004,
    'rjng' => 1005,
    'rohg' => 1006,
    'rumi' => 1007,
    'runr' => 1008,
    's' => 1009,
    'samr' => 1010,
    'sarb' => 1011,
    'saur' => 1012,
    'sb=at' => 1013,
    'sb=cl' => 1014,
    'sb=ex' => 1015,
    'sb=fo' => 1016,
    'sb=le' => 1017,
    'sb=lo' => 1018,
    'sb=nu' => 1019,
    'sb=sc' => 1020,
    'sb=se' => 1021,
    'sb=sp' => 1022,
    'sb=st' => 1023,
    'sb=up' => 1024,
    'sb=xx' => 1025,
    'sc' => 1026,
    'sc=adlm' => 1027,
    'sc=arab' => 1028,
    'sc=beng' => 1029,
    'sc=bopo' => 1030,
    'sc=bugi' => 1031,
    'sc=buhd' => 1032,
    'sc=cakm' => 1033,
    'sc=copt' => 1034,
    'sc=cpmn' => 1035,
    'sc=cprt' => 1036,
    'sc=cyrl' => 1037,
    'sc=deva' => 1038,
    'sc=dogr' => 1039,
    'sc=dupl' => 1040,
    'sc=geor' => 1041,
    'sc=glag' => 1042,
    'sc=gong' => 1043,
    'sc=gonm' => 1044,
    'sc=gran' => 1045,
    'sc=grek' => 1046,
    'sc=gujr' => 1047,
    'sc=guru' => 1048,
    'sc=han' => 1049,
    'sc=hang' => 1050,
    'sc=hano' => 1051,
    'sc=hira' => 1052,
    'sc=java' => 1053,
    'sc=kali' => 1054,
    'sc=kana' => 1055,
    'sc=khoj' => 1056,
    'sc=knda' => 1057,
    'sc=kthi' => 1058,
    'sc=latn' => 1059,
    'sc=limb' => 1060,
    'sc=lina' => 1061,
    'sc=linb' => 1062,
    'sc=mahj' => 1063,
    'sc=mand' => 1064,
    'sc=mani' => 1065,
    'sc=mlym' => 1066,
    'sc=modi' => 1067,
    'sc=mong' => 1068,
    'sc=mult' => 1069,
    'sc=mymr' => 1070,
    'sc=nand' => 1071,
    'sc=nko' => 1072,
    'sc=orya' => 1073,
    'sc=ougr' => 1074,
    'sc=perm' => 1075,
    'sc=phag' => 1076,
    'sc=phlp' => 1077,
    'sc=qaai' => 1078,
    'sc=rohg' => 1079,
    'sc=shrd' => 1080,
    'sc=sind' => 1081,
    'sc=sinh' => 1082,
    'sc=sogd' => 1083,
    'sc=sylo' => 1084,
    'sc=syrc' => 1085,
    'sc=tagb' => 1086,
    'sc=takr' => 1087,
    'sc=tale' => 1088,
    'sc=taml' => 1089,
    'sc=telu' => 1090,
    'sc=tglg' => 1091,
    'sc=thaa' => 1092,
    'sc=tirh' => 1093,
    'sc=yezi' => 1094,
    'sc=yi' => 1095,
    'sc=zyyy' => 1096,
    'sd' => 1097,
    'sgnw' => 1098,
    'shaw' => 1099,
    'shorthandformatcontrols' => 1100,
    'shrd' => 1101,
    'sidd' => 1102,
    'sind' => 1103,
    'sinh' => 1104,
    'sinhalaarchaicnumbers' => 1105,
    'sk' => 1106,
    'sm' => 1107,
    'smallforms' => 1108,
    'smallkanaext' => 1109,
    'so' => 1110,
    'sogd' => 1111,
    'sogo' => 1112,
    'sora' => 1113,
    'soyo' => 1114,
    'space' => 1204,
    'spaceperl' => 1204,
    'specials' => 1115,
    'sterm' => 1116,
    'sund' => 1117,
    'sundanesesup' => 1118,
    'suparrowsa' => 1119,
    'suparrowsb' => 1120,
    'suparrowsc' => 1121,
    'superandsub' => 1122,
    'supmathoperators' => 1123,
    'suppuaa' => 1124,
    'suppuab' => 1125,
    'suppunctuation' => 1126,
    'supsymbolsandpictographs' => 1127,
    'suttonsignwriting' => 1128,
    'sylo' => 1129,
    'symbolsandpictographsexta' => 1130,
    'symbolsforlegacycomputing' => 1131,
    'syrc' => 1132,
    'syriacsup' => 1133,
    'tagb' => 1134,
    'tags' => 1135,
    'taixuanjing' => 1136,
    'takr' => 1137,
    'tale' => 1138,
    'talu' => 1139,
    'tamilsup' => 1140,
    'taml' => 1141,
    'tang' => 1142,
    'tangutcomponents' => 1143,
    'tangutsup' => 1144,
    'tavt' => 1145,
    'telu' => 1146,
    'term' => 1147,
    'tfng' => 1148,
    'tglg' => 1149,
    'thaa' => 1150,
    'thai' => 1151,
    'tibt' => 1152,
    'tirh' => 1153,
    'title' => 1154,
    'titlecase' => 1154,
    'titlecaseletter' => 1154,
    'tnsa' => 1155,
    'toto' => 1156,
    'transportandmap' => 1157,
    'ucas' => 1158,
    'ucasext' => 1159,
    'ucasexta' => 1160,
    'ugar' => 1161,
    'uideo' => 1162,
    'upper' => 1205,
    'uppercase' => 1205,
    'uppercaseletter' => 1163,
    'vai' => 1164,
    'vedicext' => 1165,
    'verticalforms' => 1166,
    'vertspace' => 1167,
    'vith' => 1168,
    'vo=r' => 1169,
    'vo=tr' => 1170,
    'vo=tu' => 1171,
    'vo=u' => 1172,
    'vs' => 1173,
    'vssup' => 1174,
    'wara' => 1175,
    'wb=dq' => 1176,
    'wb=eb' => 1177,
    'wb=ex' => 1178,
    'wb=extend' => 1179,
    'wb=fo' => 1180,
    'wb=ka' => 1181,
    'wb=le' => 1182,
    'wb=mb' => 1183,
    'wb=ml' => 1184,
    'wb=mn' => 1185,
    'wb=nl' => 1186,
    'wb=nu' => 1187,
    'wb=sq' => 1188,
    'wb=wsegspace' => 1189,
    'wb=xx' => 1190,
    'wcho' => 1191,
    'whitespace' => 1204,
    'word' => 1206,
    'wspace' => 1204,
    'xdigit' => 1207,
    'xidc' => 1192,
    'xids' => 1193,
    'xpeo' => 1194,
    'xperlspace' => 1204,
    'xposixalnum' => 1195,
    'xposixalpha' => 1196,
    'xposixblank' => 1197,
    'xposixcntrl' => 1198,
    'xposixdigit' => 1199,
    'xposixgraph' => 1200,
    'xposixlower' => 1201,
    'xposixprint' => 1202,
    'xposixpunct' => 1203,
    'xposixspace' => 1204,
    'xposixupper' => 1205,
    'xposixword' => 1206,
    'xposixxdigit' => 1207,
    'xsux' => 1208,
    'yezi' => 1209,
    'yi' => 1210,
    'yijing' => 1211,
    'yiradicals' => 1212,
    'yisyllables' => 1213,
    'z' => 1214,
    'zanb' => 1215,
    'zl' => 1216,
    'znamennymusic' => 1217,
    'zp' => 1218,
    'zs' => 1219,
    'zyyy' => 1220,
    'zzzz' => 1221,
);

1;

# Generated from:
# 688d673ec947f7ccf898b4eae9848139d4d33676b688dee54f449f8bf9d3bbd2 lib/Unicode/UCD.pm
# eb840f36e0a7446293578c684a54c6d83d249abde7bdd4dfa89794af1d7fe9e9 lib/unicore/ArabicShaping.txt
# 333ae1e99db0504ca8a046a07dc45b5e7aa91869c685e6bf955ebe674804827a lib/unicore/BidiBrackets.txt
# b4b9e1d87d8ea273613880de9d2b2f0b0b696244b42152bfa0a3106e7d983a20 lib/unicore/BidiMirroring.txt
# 529dc5d0f6386d52f2f56e004bbfab48ce2d587eea9d38ba546c4052491bd820 lib/unicore/Blocks.txt
# cdd49e55eae3bbf1f0a3f6580c974a0263cb86a6a08daa10fbf705b4808a56f7 lib/unicore/CaseFolding.txt
# 3b019c0a33c3140cbc920c078f4f9af2680ba4f71869c8d4de5190667c70b6a3 lib/unicore/CompositionExclusions.txt
# 7570877e0fa197c45338f7c41a02636da4e14c8dba6a3611a01cd30bf329d5ca lib/unicore/DAge.txt
# d367290bc0867e6b484c68370530bdd1a08b6b32404601b8c7accaf83e05628d lib/unicore/DCoreProperties.txt
# d5687a48c95c7d6e1ec59cb29c0f2e8b052018eb069a4371b7368d0561e12a29 lib/unicore/DNormalizationProps.txt
# 743e7bc435c04ab1a8459710b1c3cad56eedced5b806b4659b6e69b85d0adf2a lib/unicore/EastAsianWidth.txt
# f2e04bae8c856fad3a16353a99d4cc2de6c72770260379f5e4974a97548aad2a lib/unicore/EquivalentUnifiedIdeograph.txt
# 9a3ab36d36a22bdb84de7a17b17e9b9c242134f0080f0a8b4b28d209465a8fc8 lib/unicore/HangulSyllableType.txt
# 790bc9595795c0e0a3860a21a7f97157a134b61a4fc4ab03c7d315d07c9a6eb7 lib/unicore/IdStatus.txt
# 71d3ed8f15cd5d8cd00cdebe62015ff26356462774b261b4a2b83d3bf46b1639 lib/unicore/IdType.txt
# 0ce56c1294da405c0a0a0071582ac839fd229bbf97bdd260462ee571309d4ec4 lib/unicore/IndicPositionalCategory.txt
# ffae561a51b47ddbbe267fdd8505ac3776b85b2932268809127acee84200b573 lib/unicore/IndicSyllabicCategory.txt
# 14733bcb6731ae0c07485bf59a41cb3db08785a50bd2b46b836b4341eab7ee46 lib/unicore/Jamo.txt
# 012bca868e2c4e59a5a10a7546baf0c6fb1b2ef458c277f054915c8a49d292bf lib/unicore/LineBreak.txt
# 3e39509e8fae3e5d50ba73759d0b97194501d14a9c63107a6372a46b38be18e8 lib/unicore/NameAliases.txt
# 1d5202155f14841973aa540b1625f4befbde185ac77ce5aceaaaa0501a68bd66 lib/unicore/NamedSequences.txt
# fb9ac8cc154a80cad6caac9897af55a4e75176af6f4e2bb6edc2bf8b1d57f326 lib/unicore/NormTest.txt
# e05c0a2811d113dae4abd832884199a3ea8d187ee1b872d8240a788a96540bfd lib/unicore/PropList.txt
# 13a7666843abea5c6b7eb8c057c57ab9bb2ba96cfc936e204224dd67d71cafad lib/unicore/PropValueAliases.txt
# e4935149af407fa455901832b710bccb63d2453e46d09190e234d019bcfbba45 lib/unicore/PropertyAliases.txt
# 7e07313d9d0bee42220c476b64485995130ae30917bbcf7780b602d677d7e33f lib/unicore/ScriptExtensions.txt
# cca85d830f46aece2e7c1459ef1249993dca8f2e46d51e869255be140d7ea4b0 lib/unicore/Scripts.txt
# 78b29c64b5840d25c11a9f31b665ee551b8a499eca6c70d770fcad7dd710f494 lib/unicore/SpecialCasing.txt
# 806e9aed65037197f1ec85e12be6e8cd870fc5608b4de0fffd990f689f376a73 lib/unicore/UnicodeData.txt
# ca6d332f485a6f5f452b29b4a74146af0f2c17b7577aa4c821d597210f70611a lib/unicore/VerticalOrientation.txt
# 0d2080d0def294a4b7660801cc03ddfe5866ff300c789c2cc1b50fd7802b2d97 lib/unicore/auxiliary/GCBTest.txt
# 5a0f8748575432f8ff95e1dd5bfaa27bda1a844809e17d6939ee912bba6568a1 lib/unicore/auxiliary/GraphemeBreakProperty.txt
# 371bde4052aa593b108684ae292d8ea2dbb93c19990e0cdf416fa7239557aac3 lib/unicore/auxiliary/LBTest.txt
# f62279d8fd10935ba0cf0d8417a1dcbe7ab0d4e62f59c17e02cbe40f580c4162 lib/unicore/auxiliary/SBTest.txt
# 61e4ba975b0a5bc1a76ee931b94914395d7289ef624e3c0d4d6b9460ee387bea lib/unicore/auxiliary/SentenceBreakProperty.txt
# 2a676130c71194245e7c74a837e58330f202600d8ddcf4518129dd476f26e18e lib/unicore/auxiliary/WBTest.txt
# 5188a56e91593467c2e912601ebc78750e6adc9b04541b8c5becb5441e388ce2 lib/unicore/auxiliary/WordBreakProperty.txt
# 29071dba22c72c27783a73016afb8ffaeb025866740791f9c2d0b55cc45a3470 lib/unicore/emoji/emoji.txt
# 4841f2090c2dbc592d3ce43bb74c2191b3da50fb9a0d00274f1448c202851b02 lib/unicore/extracted/DBidiClass.txt
# f10a35451429137f7348825f22d624b6390c526ead3d8e756d2af9e5ed5b2b67 lib/unicore/extracted/DBinaryProperties.txt
# ca54f6360cd288ad92113415bf1f77749015abe11cbd6798d21f7fa81f04205d lib/unicore/extracted/DCombiningClass.txt
# db059ce45e3cec49bfda56e262fa658b3a5561b1648de266c818d2a08a85b78a lib/unicore/extracted/DDecompositionType.txt
# d62e6950f086e53f47c593a38342621f8838f48c49a1de070cf83d3959bd1688 lib/unicore/extracted/DEastAsianWidth.txt
# fe29a45c0882500e591140aaa5c4f5067e6a5d746806148af34400c48b9c06f9 lib/unicore/extracted/DGeneralCategory.txt
# e13ca1344b16023aa38c6ada39f9658536fc6bb7c3c24d579f0bc316a4f4f1e0 lib/unicore/extracted/DJoinGroup.txt
# c4870b11e2b8b7d0eb70b99ce85608e5c28a399efa316cca97238a58ae160e5e lib/unicore/extracted/DJoinType.txt
# 3f4f32ed2a577344a508114527e721d7a8b633d32f38945d47fe0c743650c585 lib/unicore/extracted/DLineBreak.txt
# 710abf2d581ac9c57f244c0834f9d9969d9781e0396adccd330eaae658ac7d6b lib/unicore/extracted/DNumType.txt
# 6bd30f385f3baf3ab5d5308c111a81de87bea5f494ba0ba69e8ab45263b8c34d lib/unicore/extracted/DNumValues.txt
# f7265069b38ba9a0675a18600e241b1ec6fc8c55fd806fe4c13bc5d8cb0dc508 lib/unicore/mktables
# 55d90fdc3f902e5c0b16b3378f9eaa36e970a1c09723c33de7d47d0370044012 lib/unicore/version
# 0a6b5ab33bb1026531f816efe81aea1a8ffcd34a27cbea37dd6a70a63d73c844 regen/charset_translations.pl
# c7ff8e0d207d3538c7feb4a1a152b159e5e902d20293b303569ea8323e84633e regen/mk_PL_charclass.pl
# cdbafee25193032242e77f2a6332b731d8392ce342fa616dbabc2c14c7b44eb6 regen/mk_invlists.pl
# ex: set ro ft=perl:
