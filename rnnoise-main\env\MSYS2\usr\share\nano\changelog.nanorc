## Syntax highlighting for Changelogs.

## Original author:  <PERSON><PERSON>
## License:  GPL version 3 or newer

syntax changelog "Change[Ll]og[^/]*$"

# Author lines.
color green "^(19|20).*"
# Dates.
color red "^(19|20)[[:digit:]-]{8}"
# Email addresses.
color yellow "<[^>]*@[^>]*>"

# Command-line options.
color cyan "[[:blank:]]-[[:alpha:]$]|--[[:lower:]8-]+"
# Bug and patch numbers.
color cyan "(BZ|bug|patch) #[[:digit:]]+|PR [[:alnum:]]+/[[:digit:]]+"
# Probable constants, for variety.
color brightred "\<[[:upper:]_][[:upper:][:digit:]_]+\>"
# Key sequences.
color brightblue "\^[[:upper:]\^_]|\<M-.|\<F1?[[:digit:]]|(\^|M-)Space"

# Changed files.
color magenta start="^(	| {8})\* " end="(:( |$)|^$)"

# Release markers.
color brightblue "^(GNU )?nano[- ][[:digit:]]\.[[:digit:]]\.[^ ]+"

# Trailing whitespace.
color ,green "[[:space:]]+$"
