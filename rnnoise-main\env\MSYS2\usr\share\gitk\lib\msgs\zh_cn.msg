set ::msgcat::header "Project-Id-Version: Git Chinese Localization Project\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2017-03-11 02:27+0800\nLast-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\nLanguage-Team: Chinese\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nLanguage: zh_CN\n"
::msgcat::mcset zh_cn "Couldn't get list of unmerged files:" "\u4e0d\u80fd\u83b7\u53d6\u672a\u5408\u5e76\u6587\u4ef6\u5217\u8868\uff1a"
::msgcat::mcset zh_cn "Color words" "\u7740\u8272\u663e\u793a\u5dee\u5f02"
::msgcat::mcset zh_cn "Markup words" "\u6807\u8bb0\u663e\u793a\u5dee\u5f02"
::msgcat::mcset zh_cn "Error parsing revisions:" "\u89e3\u6790\u7248\u672c\u9519\u8bef\uff1a"
::msgcat::mcset zh_cn "Error executing --argscmd command:" "\u8fd0\u884c --argscmd\u547d\u4ee4\u51fa\u9519"
::msgcat::mcset zh_cn "No files selected: --merge specified but no files are unmerged." "\u6ca1\u6709\u9009\u4e2d\u6587\u4ef6\uff1a--\u6307\u5b9amerge\u53c2\u6570\u4f46\u6ca1\u6709\u672a\u5408\u5e76\u7684\u6587\u4ef6\u3002"
::msgcat::mcset zh_cn "No files selected: --merge specified but no unmerged files are within file limit." "\u6ca1\u6709\u9009\u4e2d\u6587\u4ef6\uff1a--\u6307\u5b9amerge\u53c2\u6570\u4f46\u6ca1\u6709\u672a\u5408\u5e76\u7684\u6587\u4ef6\u5728\u6587\u4ef6\u4e2d"
::msgcat::mcset zh_cn "Error executing git log:" "\u6267\u884cgit log\u547d\u4ee4\u51fa\u9519\uff1a"
::msgcat::mcset zh_cn "Reading" "\u8bfb\u53d6\u4e2d"
::msgcat::mcset zh_cn "Reading commits..." "\u63d0\u4ea4\u8bb0\u5f55\u8bfb\u53d6\u4e2d..."
::msgcat::mcset zh_cn "No commits selected" "\u672a\u9009\u4e2d\u4efb\u4f55\u63d0\u4ea4"
::msgcat::mcset zh_cn "Command line" "\u547d\u4ee4\u884c"
::msgcat::mcset zh_cn "Can't parse git log output:" "\u4e0d\u80fd\u89e3\u6790git log\u8f93\u51fa\uff1a"
::msgcat::mcset zh_cn "No commit information available" "\u65e0\u53ef\u7528\u63d0\u4ea4\u4fe1\u606f"
::msgcat::mcset zh_cn "OK" "\u786e\u5b9a"
::msgcat::mcset zh_cn "Cancel" "\u53d6\u6d88"
::msgcat::mcset zh_cn "&Update" "\u66f4\u65b0"
::msgcat::mcset zh_cn "&Reload" "\u91cd\u65b0\u52a0\u8f7d"
::msgcat::mcset zh_cn "Reread re&ferences" "\u91cd\u65b0\u8bfb\u53d6\u5f15\u7528"
::msgcat::mcset zh_cn "&List references" "\u5217\u51fa\u5f15\u7528(\u5206\u652f\u4ee5\u53catag)"
::msgcat::mcset zh_cn "Start git &gui" "\u542f\u52a8git gui\u5ba2\u6237\u7aef"
::msgcat::mcset zh_cn "&Quit" "\u9000\u51fa"
::msgcat::mcset zh_cn "&File" "\u6587\u4ef6"
::msgcat::mcset zh_cn "&Preferences" "\u504f\u597d\u8bbe\u7f6e"
::msgcat::mcset zh_cn "&Edit" "\u7f16\u8f91"
::msgcat::mcset zh_cn "&New view..." "\u65b0\u89c6\u56fe..."
::msgcat::mcset zh_cn "&Edit view..." "\u7f16\u8f91\u89c6\u56fe..."
::msgcat::mcset zh_cn "&Delete view" "\u5220\u9664\u89c6\u56fe"
::msgcat::mcset zh_cn "&All files" "\u6240\u6709\u6587\u4ef6"
::msgcat::mcset zh_cn "&View" "\u89c6\u56fe"
::msgcat::mcset zh_cn "&About gitk" "\u5173\u4e8egitk"
::msgcat::mcset zh_cn "&Key bindings" "\u5feb\u6377\u952e"
::msgcat::mcset zh_cn "&Help" "\u5e2e\u52a9"
::msgcat::mcset zh_cn "SHA1 ID:" "SHA1 ID\uff1a"
::msgcat::mcset zh_cn "Row" "\u884c"
::msgcat::mcset zh_cn "Find" "\u67e5\u627e"
::msgcat::mcset zh_cn "commit" "\u63d0\u4ea4"
::msgcat::mcset zh_cn "containing:" "\u5305\u542b\uff1a"
::msgcat::mcset zh_cn "touching paths:" "\u5f71\u54cd\u8def\u5f84\uff1a"
::msgcat::mcset zh_cn "adding/removing string:" "\u589e\u52a0/\u5220\u9664\u5b57\u7b26\u4e32\uff1a"
::msgcat::mcset zh_cn "changing lines matching:" "\u6539\u53d8\u884c\u5339\u914d\uff1a"
::msgcat::mcset zh_cn "Exact" "\u7cbe\u786e\u5339\u914d"
::msgcat::mcset zh_cn "IgnCase" "\u5ffd\u7565\u5927\u5c0f\u5199"
::msgcat::mcset zh_cn "Regexp" "\u6b63\u5219"
::msgcat::mcset zh_cn "All fields" "\u6240\u6709\u5b57\u6bb5"
::msgcat::mcset zh_cn "Headline" "\u6807\u9898"
::msgcat::mcset zh_cn "Comments" "\u63d0\u4ea4\u6ce8\u91ca"
::msgcat::mcset zh_cn "Author" "\u4f5c\u8005"
::msgcat::mcset zh_cn "Committer" "\u63d0\u4ea4\u8005"
::msgcat::mcset zh_cn "Search" "\u641c\u7d22"
::msgcat::mcset zh_cn "Diff" "\u5dee\u5f02"
::msgcat::mcset zh_cn "Old version" "\u8001\u7248\u672c"
::msgcat::mcset zh_cn "New version" "\u65b0\u7248\u672c"
::msgcat::mcset zh_cn "Lines of context" "Diff\u4e0a\u4e0b\u6587\u663e\u793a\u884c\u6570"
::msgcat::mcset zh_cn "Ignore space change" "\u5ffd\u7565\u7a7a\u683c\u4fee\u6539"
::msgcat::mcset zh_cn "Line diff" "\u6309\u884c\u663e\u793a\u5dee\u5f02"
::msgcat::mcset zh_cn "Patch" "\u8865\u4e01"
::msgcat::mcset zh_cn "Tree" "\u6811"
::msgcat::mcset zh_cn "Diff this -> selected" "\u6bd4\u8f83\u4ece\u5f53\u524d\u63d0\u4ea4\u5230\u9009\u4e2d\u63d0\u4ea4\u7684\u5dee\u5f02"
::msgcat::mcset zh_cn "Diff selected -> this" "\u6bd4\u8f83\u4ece\u9009\u4e2d\u63d0\u4ea4\u5230\u5f53\u524d\u63d0\u4ea4\u7684\u5dee\u5f02"
::msgcat::mcset zh_cn "Make patch" "\u5236\u4f5c\u8865\u4e01"
::msgcat::mcset zh_cn "Create tag" "\u521b\u5efatag"
::msgcat::mcset zh_cn "Copy commit summary" "\u590d\u5236\u63d0\u4ea4\u6458\u8981"
::msgcat::mcset zh_cn "Write commit to file" "\u5199\u5165\u63d0\u4ea4\u5230\u6587\u4ef6"
::msgcat::mcset zh_cn "Create new branch" "\u521b\u5efa\u65b0\u5206\u652f"
::msgcat::mcset zh_cn "Cherry-pick this commit" "\u5728\u6b64\u63d0\u4ea4\u8fd0\u7528\u8865\u4e01(cherry-pick)\u547d\u4ee4"
::msgcat::mcset zh_cn "Reset HEAD branch to here" "\u5c06\u5206\u652f\u5934(HEAD)\u91cd\u7f6e\u5230\u6b64\u5904"
::msgcat::mcset zh_cn "Mark this commit" "\u6807\u8bb0\u6b64\u63d0\u4ea4"
::msgcat::mcset zh_cn "Return to mark" "\u8fd4\u56de\u5230\u6807\u8bb0"
::msgcat::mcset zh_cn "Find descendant of this and mark" "\u67e5\u627e\u672c\u6b21\u63d0\u4ea4\u7684\u5b50\u63d0\u4ea4\u5e76\u6807\u8bb0"
::msgcat::mcset zh_cn "Compare with marked commit" "\u548c\u5df2\u6807\u8bb0\u7684\u63d0\u4ea4\u4f5c\u6bd4\u8f83"
::msgcat::mcset zh_cn "Diff this -> marked commit" "\u6bd4\u8f83\u4ece\u5f53\u524d\u63d0\u4ea4\u5230\u5df2\u6807\u8bb0\u63d0\u4ea4\u7684\u5dee\u5f02"
::msgcat::mcset zh_cn "Diff marked commit -> this" "\u6bd4\u8f83\u4ece\u5df2\u6807\u8bb0\u63d0\u4ea4\u5230\u5f53\u524d\u63d0\u4ea4\u7684\u5dee\u5f02"
::msgcat::mcset zh_cn "Revert this commit" "\u64a4\u9500(revert)\u6b64\u63d0\u4ea4"
::msgcat::mcset zh_cn "Check out this branch" "\u68c0\u51fa(checkout)\u6b64\u5206\u652f"
::msgcat::mcset zh_cn "Rename this branch" "\u91cd\u547d\u540d(Rename)\u6b64\u5206\u652f"
::msgcat::mcset zh_cn "Remove this branch" "\u5220\u9664(Remove)\u6b64\u5206\u652f"
::msgcat::mcset zh_cn "Copy branch name" "\u590d\u5236\u5206\u652f\u540d\u79f0"
::msgcat::mcset zh_cn "Highlight this too" "\u9ad8\u4eae\u6b64\u5904"
::msgcat::mcset zh_cn "Highlight this only" "\u53ea\u9ad8\u4eae\u6b64\u5904"
::msgcat::mcset zh_cn "External diff" "\u5916\u90e8diff"
::msgcat::mcset zh_cn "Blame parent commit" "Blame\u7236\u63d0\u4ea4"
::msgcat::mcset zh_cn "Copy path" "\u590d\u5236\u8def\u5f84"
::msgcat::mcset zh_cn "Show origin of this line" "\u663e\u793a\u6b64\u884c\u539f\u59cb\u63d0\u4ea4"
::msgcat::mcset zh_cn "Run git gui blame on this line" "\u5728\u6b64\u884c\u8fd0\u884cgit gui\u5ba2\u6237\u7aef\u7684blame"
::msgcat::mcset zh_cn "About gitk" "\u5173\u4e8egitk"
::msgcat::mcset zh_cn "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk \u2014 \u4e00\u4e2agit\u7684\u63d0\u4ea4\u67e5\u770b\u5668\n\n\u00a9 2005-2016 Paul Mackerras\n\n\u5728GNU\u8bb8\u53ef\u8bc1\u4e0b\u4f7f\u7528\u4ee5\u53ca\u5206\u53d1"
::msgcat::mcset zh_cn "Close" "\u5173\u95ed"
::msgcat::mcset zh_cn "Gitk key bindings" "Gitk\u5feb\u6377\u952e"
::msgcat::mcset zh_cn "Gitk key bindings:" "Gitk\u5feb\u6377\u952e\uff1a"
::msgcat::mcset zh_cn "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009\u9000\u51fa"
::msgcat::mcset zh_cn "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009\u5173\u95ed\u7a97\u53e3"
::msgcat::mcset zh_cn "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009\u79fb\u52a8\u5230\u7b2c\u4e00\u6b21\u63d0\u4ea4"
::msgcat::mcset zh_cn "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009\u79fb\u52a8\u5230\u6700\u540e\u4e00\u6b21\u63d0\u4ea4"
::msgcat::mcset zh_cn "<Up>, p, k\u0009Move up one commit" "<Up>, p, k\u0009\u79fb\u52a8\u5230\u4e0a\u4e00\u6b21\u63d0\u4ea4"
::msgcat::mcset zh_cn "<Down>, n, j\u0009Move down one commit" "<Down>, n, j\u0009\u79fb\u52a8\u5230\u4e0b\u4e00\u6b21\u63d0\u4ea4"
::msgcat::mcset zh_cn "<Left>, z, h\u0009Go back in history list" "<Left>, z, h\u0009\u5386\u53f2\u5217\u8868\u7684\u4e0a\u4e00\u9879"
::msgcat::mcset zh_cn "<Right>, x, l\u0009Go forward in history list" "<Right>, x, l\u0009\u5386\u53f2\u5217\u8868\u7684\u4e0b\u4e00\u9879"
::msgcat::mcset zh_cn "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n>\u0009\u5728\u5386\u53f2\u5217\u8868\u4e2d\u524d\u5f80\u672c\u6b21\u63d0\u4ea4\u7684\u7b2cn\u4e2a\u7236\u63d0\u4ea4"
::msgcat::mcset zh_cn "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009\u4e0a\u4e00\u9875\u63d0\u4ea4\u5217\u8868"
::msgcat::mcset zh_cn "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009\u4e0b\u4e00\u9875\u63d0\u4ea4\u5217\u8868"
::msgcat::mcset zh_cn "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009\u6eda\u52a8\u5230\u63d0\u4ea4\u5217\u8868\u9876\u90e8"
::msgcat::mcset zh_cn "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009\u6eda\u52a8\u5230\u63d0\u4ea4\u5217\u8868\u5e95\u90e8"
::msgcat::mcset zh_cn "<%s-Up>\u0009Scroll commit list up one line" "<%s-Up>\u0009\u5411\u4e0a\u6eda\u52a8\u4e00\u884c\u63d0\u4ea4\u5217\u8868"
::msgcat::mcset zh_cn "<%s-Down>\u0009Scroll commit list down one line" "<%s-Down>\u0009\u5411\u4e0b\u6eda\u52a8\u4e00\u884c\u63d0\u4ea4\u5217\u8868"
::msgcat::mcset zh_cn "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009\u5411\u4e0a\u6eda\u52a8\u4e00\u9875\u63d0\u4ea4\u5217\u8868"
::msgcat::mcset zh_cn "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009\u5411\u4e0b\u6eda\u52a8\u4e00\u9875\u63d0\u4ea4\u5217\u8868"
::msgcat::mcset zh_cn "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Up>\u0009\u5411\u540e\u67e5\u627e(\u5411\u4e0a\u7684\uff0c\u66f4\u665a\u7684\u63d0\u4ea4)"
::msgcat::mcset zh_cn "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Down>\u0009\u5411\u524d\u67e5\u627e(\u5411\u4e0b\u7684\uff0c\u66f4\u65e9\u7684\u63d0\u4ea4)"
::msgcat::mcset zh_cn "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009\u5411\u4e0a\u6eda\u52a8diff\u89c6\u56fe\u4e00\u9875"
::msgcat::mcset zh_cn "<Backspace>\u0009Scroll diff view up one page" "<Backspace>\u0009\u5411\u4e0a\u6eda\u52a8diff\u89c6\u56fe\u4e00\u9875"
::msgcat::mcset zh_cn "<Space>\u0009\u0009Scroll diff view down one page" "<Space>\u0009\u0009\u5411\u4e0b\u6eda\u52a8diff\u89c6\u56fe\u4e00\u9875"
::msgcat::mcset zh_cn "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009\u5411\u4e0a\u6eda\u52a8diff\u89c6\u56fe18\u884c"
::msgcat::mcset zh_cn "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009\u5411\u4e0b\u6eda\u52a8diff\u89c6\u56fe18\u884c"
::msgcat::mcset zh_cn "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009\u67e5\u627e"
::msgcat::mcset zh_cn "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009\u79fb\u52a8\u5230\u4e0b\u4e00\u6b21\u67e5\u627e\u547d\u4e2d"
::msgcat::mcset zh_cn "<Return>\u0009Move to next find hit" "<Return>\u0009\u0009\u79fb\u52a8\u5230\u4e0b\u4e00\u6b21\u67e5\u627e\u547d\u4e2d"
::msgcat::mcset zh_cn "g\u0009\u0009Go to commit" "g\u0009\u0009\u8f6c\u5230\u63d0\u4ea4"
::msgcat::mcset zh_cn "/\u0009\u0009Focus the search box" "/\u0009\u0009\u9009\u4e2d\u641c\u7d22\u6846"
::msgcat::mcset zh_cn "?\u0009\u0009Move to previous find hit" "?\u0009\u0009\u79fb\u52a8\u5230\u4e0a\u4e00\u6b21\u67e5\u627e\u547d\u4e2d"
::msgcat::mcset zh_cn "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009\u6eda\u52a8diff\u89c6\u56fe\u5230\u4e0b\u4e00\u4e2a\u6587\u4ef6"
::msgcat::mcset zh_cn "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009\u5728diff\u89c6\u56fe\u4e2d\u67e5\u627e\u4e0b\u4e00\u6b64\u547d\u4e2d"
::msgcat::mcset zh_cn "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009\u5728diff\u89c6\u56fe\u4e2d\u67e5\u627e\u4e0a\u4e00\u6b21\u547d\u4e2d"
::msgcat::mcset zh_cn "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009\u589e\u5927\u5b57\u4f53\u5927\u5c0f"
::msgcat::mcset zh_cn "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009\u589e\u5927\u5b57\u4f53\u5927\u5c0f"
::msgcat::mcset zh_cn "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009\u51cf\u5c0f\u5b57\u4f53\u5927\u5c0f"
::msgcat::mcset zh_cn "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009\u51cf\u5c0f\u5b57\u4f53\u5927\u5c0f"
::msgcat::mcset zh_cn "<F5>\u0009\u0009Update" "<F5>\u0009\u0009\u66f4\u65b0"
::msgcat::mcset zh_cn "Error creating temporary directory %s:" "\u521b\u5efa\u4e34\u65f6\u76ee\u5f55\u51fa\u9519%s\uff1a"
::msgcat::mcset zh_cn "Error getting \"%s\" from %s:" "\u4ece%s\u83b7\u53d6\"%s\"\u51fa\u9519\uff1a"
::msgcat::mcset zh_cn "command failed:" "\u6267\u884c\u547d\u4ee4\u5931\u8d25\uff1a"
::msgcat::mcset zh_cn "No such commit" "\u65e0\u6b64\u63d0\u4ea4"
::msgcat::mcset zh_cn "git gui blame: command failed:" "git gui blame\uff1a\u6267\u884c\u547d\u4ee4\u5931\u8d25\uff1a"
::msgcat::mcset zh_cn "Couldn't read merge head: %s" "\u4e0d\u80fd\u8bfb\u53d6\u5408\u5e76\u5934(merge head)\uff1a%s"
::msgcat::mcset zh_cn "Error reading index: %s" "\u8bfb\u53d6\u7d22\u5f15\u51fa\u9519\uff1a%s"
::msgcat::mcset zh_cn "Couldn't start git blame: %s" "\u4e0d\u80fd\u6267\u884cgit blame\uff1a%s"
::msgcat::mcset zh_cn "Searching" "\u641c\u7d22\u4e2d"
::msgcat::mcset zh_cn "Error running git blame: %s" "\u8fd0\u884cgit blame\u51fa\u9519\uff1a%s"
::msgcat::mcset zh_cn "That line comes from commit %s,  which is not in this view" "\u6b64\u884c\u6765\u81ea\u63d0\u4ea4%s\uff0c\u4e0d\u5728\u6b64\u89c6\u56fe\u4e2d"
::msgcat::mcset zh_cn "External diff viewer failed:" "\u5916\u90e8diff\u67e5\u770b\u5668\u5931\u8d25\uff1a"
::msgcat::mcset zh_cn "All files" "\u6240\u6709\u6587\u4ef6"
::msgcat::mcset zh_cn "View" "\u89c6\u56fe"
::msgcat::mcset zh_cn "Gitk view definition" "Gitk\u89c6\u56fe\u5b9a\u4e49"
::msgcat::mcset zh_cn "Remember this view" "\u8bb0\u4f4f\u6b64\u89c6\u56fe"
::msgcat::mcset zh_cn "References (space separated list):" "\u5f15\u7528(\u7a7a\u683c\u5207\u5206\u7684\u5217\u8868)\uff1a"
::msgcat::mcset zh_cn "Branches & tags:" "\u5206\u652f\u548ctags"
::msgcat::mcset zh_cn "All refs" "\u6240\u6709\u5f15\u7528"
::msgcat::mcset zh_cn "All (local) branches" "\u6240\u6709(\u672c\u5730)\u5206\u652f"
::msgcat::mcset zh_cn "All tags" "\u6240\u6709tag"
::msgcat::mcset zh_cn "All remote-tracking branches" "\u6240\u6709\u8fdc\u7a0b\u8ddf\u8e2a\u5206\u652f"
::msgcat::mcset zh_cn "Commit Info (regular expressions):" "\u63d0\u4ea4\u4fe1\u606f (\u6b63\u5219\u8868\u8fbe\u5f0f)\uff1a"
::msgcat::mcset zh_cn "Author:" "\u4f5c\u8005\uff1a"
::msgcat::mcset zh_cn "Committer:" "\u63d0\u4ea4\u8005\uff1a"
::msgcat::mcset zh_cn "Commit Message:" "\u63d0\u4ea4\u4fe1\u606f\uff1a"
::msgcat::mcset zh_cn "Matches all Commit Info criteria" "\u5339\u914d\u6240\u6709\u63d0\u4ea4\u4fe1\u606f\u6807\u51c6"
::msgcat::mcset zh_cn "Matches no Commit Info criteria" "\u5339\u914d\u65e0\u63d0\u4ea4\u4fe1\u606f\u6807\u51c6"
::msgcat::mcset zh_cn "Changes to Files:" "\u6587\u4ef6\u4fee\u6539\u5217\u8868\uff1a"
::msgcat::mcset zh_cn "Fixed String" "\u56fa\u5b9a\u5b57\u7b26\u4e32"
::msgcat::mcset zh_cn "Regular Expression" "\u6b63\u5219\u8868\u8fbe\u5f0f\uff1a"
::msgcat::mcset zh_cn "Search string:" "\u641c\u7d22\u5b57\u7b26\u4e32\uff1a"
::msgcat::mcset zh_cn "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "\u63d0\u4ea4\u65e5\u671f (\"2\u661f\u671f\u4e4b\u524d\", \"2009-03-17 15:27:38\", \"5\u6708 17, 2009 15:27:38\"):"
::msgcat::mcset zh_cn "Since:" "\u81ea\uff1a"
::msgcat::mcset zh_cn "Until:" "\u5230\uff1a"
::msgcat::mcset zh_cn "Limit and/or skip a number of revisions (positive integer):" "\u9650\u5236 \u4e14/\u6216 \u8df3\u8fc7\u4e00\u5b9a\u6570\u91cf\u7684\u7248\u672c(\u6b63\u6574\u6570)\uff1a"
::msgcat::mcset zh_cn "Number to show:" "\u663e\u793a\u6570\u91cf\uff1a"
::msgcat::mcset zh_cn "Number to skip:" "\u8df3\u8fc7\u6570\u91cf\uff1a"
::msgcat::mcset zh_cn "Miscellaneous options:" "\u5176\u4ed6\u9009\u9879\uff1a"
::msgcat::mcset zh_cn "Strictly sort by date" "\u4e25\u683c\u6309\u65e5\u671f\u6574\u7406"
::msgcat::mcset zh_cn "Mark branch sides" "\u6807\u8bb0\u5206\u652f\u8fb9\u754c"
::msgcat::mcset zh_cn "Limit to first parent" "\u9650\u5236\u5230\u7b2c\u4e00\u4e2a\u7236\u63d0\u4ea4"
::msgcat::mcset zh_cn "Simple history" "\u7b80\u6613\u5386\u53f2"
::msgcat::mcset zh_cn "Additional arguments to git log:" "git log\u547d\u4ee4\u7684\u989d\u5916\u53c2\u6570\uff1a"
::msgcat::mcset zh_cn "Enter files and directories to include, one per line:" "\u8f93\u5165\u6587\u4ef6\u548c\u6587\u4ef6\u5939\u6765\u5f15\u7528\uff0c\u6bcf\u884c\u4e00\u4e2a\uff1a"
::msgcat::mcset zh_cn "Command to generate more commits to include:" "\u547d\u4ee4\u4ea7\u751f\u66f4\u591a\u7684\u63d0\u4ea4\u6765\u5f15\u7528\uff1a"
::msgcat::mcset zh_cn "Gitk: edit view" "Gitk: \u7f16\u8f91\u89c6\u56fe"
::msgcat::mcset zh_cn "-- criteria for selecting revisions" "-- \u7528\u6765\u9009\u62e9\u7248\u672c\u7684\u89c4\u5219"
::msgcat::mcset zh_cn "View Name" "\u89c6\u56fe\u540d\u79f0"
::msgcat::mcset zh_cn "Apply (F5)" "\u5e94\u7528(F5)"
::msgcat::mcset zh_cn "Error in commit selection arguments:" "\u63d0\u4ea4\u9009\u62e9\u53c2\u6570\u9519\u8bef\uff1a"
::msgcat::mcset zh_cn "None" "\u65e0"
::msgcat::mcset zh_cn "Descendant" "\u5b50\u63d0\u4ea4"
::msgcat::mcset zh_cn "Not descendant" "\u975e\u5b50\u63d0\u4ea4"
::msgcat::mcset zh_cn "Ancestor" "\u7236\u63d0\u4ea4"
::msgcat::mcset zh_cn "Not ancestor" "\u975e\u7236\u63d0\u4ea4"
::msgcat::mcset zh_cn "Local changes checked in to index but not committed" "\u5df2\u6dfb\u52a0\u5230\u7d22\u5f15\u4f46\u672a\u63d0\u4ea4\u7684\u4fee\u6539"
::msgcat::mcset zh_cn "Local uncommitted changes, not checked in to index" "\u672a\u6dfb\u52a0\u5230\u7d22\u5f15\u4e14\u672a\u63d0\u4ea4\u7684\u4fee\u6539"
::msgcat::mcset zh_cn "and many more" "\u66f4\u591a"
::msgcat::mcset zh_cn "many" "\u5f88\u591a"
::msgcat::mcset zh_cn "Tags:" "Tags:"
::msgcat::mcset zh_cn "Parent" "\u7236\u8282\u70b9"
::msgcat::mcset zh_cn "Child" "\u5b50\u8282\u70b9"
::msgcat::mcset zh_cn "Branch" "\u5206\u652f"
::msgcat::mcset zh_cn "Follows" "\u4e4b\u540e\u7684tag"
::msgcat::mcset zh_cn "Precedes" "\u4e4b\u524d\u7684tag"
::msgcat::mcset zh_cn "Error getting diffs: %s" "\u83b7\u53d6\u5dee\u5f02\u9519\u8bef\uff1a%s"
::msgcat::mcset zh_cn "Goto:" "\u8f6c\u5230\uff1a"
::msgcat::mcset zh_cn "Short SHA1 id %s is ambiguous" "\u77ed\u683c\u5f0f\u7684SHA1\u63d0\u4ea4\u53f7%s\u4e0d\u660e\u786e\u3001\u6709\u6b67\u4e49"
::msgcat::mcset zh_cn "Revision %s is not known" "\u7248\u672c%s\u672a\u77e5"
::msgcat::mcset zh_cn "SHA1 id %s is not known" "\u63d0\u4ea4\u53f7(SHA1 id)%s\u672a\u77e5"
::msgcat::mcset zh_cn "Revision %s is not in the current view" "\u7248\u672c%s\u4e0d\u5728\u5f53\u524d\u89c6\u56fe\u4e2d"
::msgcat::mcset zh_cn "Date" "\u65e5\u671f"
::msgcat::mcset zh_cn "Children" "\u5b50\u8282\u70b9"
::msgcat::mcset zh_cn "Reset %s branch to here" "\u91cd\u7f6e\u5206\u652f%s\u5230\u6b64\u5904"
::msgcat::mcset zh_cn "Detached head: can't reset" "\u5206\u79bb\u7684\u5934(head)\uff1a\u4e0d\u80fd\u91cd\u7f6e(reset)"
::msgcat::mcset zh_cn "Skipping merge commit " "\u8df3\u8fc7\u5408\u5e76\u63d0\u4ea4"
::msgcat::mcset zh_cn "Error getting patch ID for " "\u83b7\u53d6\u8865\u4e01ID\u51fa\u9519"
::msgcat::mcset zh_cn " - stopping\n" " \u2014 \u505c\u6b62\u4e2d\n"
::msgcat::mcset zh_cn "Commit " "\u63d0\u4ea4"
::msgcat::mcset zh_cn " is the same patch as\n       " " \u662f\u76f8\u540c\u7684\u8865\u4e01(patch)\n       "
::msgcat::mcset zh_cn " differs from\n       " " \u5dee\u5f02\u6765\u81ea\n       "
::msgcat::mcset zh_cn "Diff of commits:\n\n" "\u63d0\u4ea4\u7684\u5dee\u5f02(Diff)\uff1a\n\n"
::msgcat::mcset zh_cn " has %s children - stopping\n" "\u6709%s\u5b50\u8282\u70b9 \u2014 \u505c\u6b62\u4e2d\n"
::msgcat::mcset zh_cn "Error writing commit to file: %s" "\u5199\u5165\u63d0\u4ea4\u5230\u6587\u4ef6\u51fa\u9519\uff1a%s"
::msgcat::mcset zh_cn "Error diffing commits: %s" "\u6bd4\u8f83\u63d0\u4ea4\u5dee\u5f02\u51fa\u9519\uff1a%s"
::msgcat::mcset zh_cn "Top" "\u9876\u90e8"
::msgcat::mcset zh_cn "From" "\u4ece"
::msgcat::mcset zh_cn "To" "\u5230"
::msgcat::mcset zh_cn "Generate patch" "\u751f\u6210\u8865\u4e01(patch)"
::msgcat::mcset zh_cn "From:" "\u4ece\uff1a"
::msgcat::mcset zh_cn "To:" "\u5230\uff1a"
::msgcat::mcset zh_cn "Reverse" "\u53cd\u5411(Reverse)"
::msgcat::mcset zh_cn "Output file:" "\u8f93\u51fa\u6587\u4ef6\uff1a"
::msgcat::mcset zh_cn "Generate" "\u751f\u6210"
::msgcat::mcset zh_cn "Error creating patch:" "\u521b\u5efa\u8865\u4e01(patch)\u51fa\u9519\uff1a"
::msgcat::mcset zh_cn "ID:" "ID:"
::msgcat::mcset zh_cn "Tag name:" "Tag\u540d\u79f0\uff1a"
::msgcat::mcset zh_cn "Tag message is optional" "Tag\u4fe1\u606f\u662f\u53ef\u9009\u7684"
::msgcat::mcset zh_cn "Tag message:" "Tag\u4fe1\u606f\uff1a"
::msgcat::mcset zh_cn "Create" "\u521b\u5efa"
::msgcat::mcset zh_cn "No tag name specified" "\u672a\u6307\u5b9atag\u540d\u79f0"
::msgcat::mcset zh_cn "Tag \"%s\" already exists" "Tag\"%s\"\u5df2\u7ecf\u5b58\u5728"
::msgcat::mcset zh_cn "Error creating tag:" "\u521b\u5efatag\u51fa\u9519\uff1a"
::msgcat::mcset zh_cn "Command:" "\u547d\u4ee4\uff1a"
::msgcat::mcset zh_cn "Write" "\u5199\u5165"
::msgcat::mcset zh_cn "Error writing commit:" "\u5199\u5165\u63d0\u4ea4\u51fa\u9519\uff1a"
::msgcat::mcset zh_cn "Create branch" "\u521b\u5efa\u5206\u652f"
::msgcat::mcset zh_cn "Rename branch %s" "\u91cd\u547d\u540d\u5206\u652f%s"
::msgcat::mcset zh_cn "Rename" "\u91cd\u547d\u540d"
::msgcat::mcset zh_cn "Name:" "\u540d\u79f0\uff1a"
::msgcat::mcset zh_cn "Please specify a name for the new branch" "\u8bf7\u6307\u5b9a\u65b0\u5206\u652f\u7684\u540d\u79f0"
::msgcat::mcset zh_cn "Branch '%s' already exists. Overwrite?" "\u5206\u652f\"%s\"\u5df2\u7ecf\u5b58\u5728\u3002\u8986\u76d6\u5b83\uff1f"
::msgcat::mcset zh_cn "Please specify a new name for the branch" "\u8bf7\u91cd\u65b0\u6307\u5b9a\u65b0\u5206\u652f\u7684\u540d\u79f0"
::msgcat::mcset zh_cn "Commit %s is already included in branch %s -- really re-apply it?" "\u63d0\u4ea4%s\u5df2\u7ecf\u5b58\u5728\u4e8e\u5206\u652f%s\u3002\u786e\u5b9a\u91cd\u65b0\u5e94\u7528\u5b83\uff1f"
::msgcat::mcset zh_cn "Cherry-picking" "\u6253\u8865\u4e01\u4e2d(Cherry-picking)"
::msgcat::mcset zh_cn "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "\u6253\u8865\u4e01(Cherry-pick)\u5931\u8d25\uff0c\u56e0\u4e3a\u672c\u5730\u4fee\u6539\u4e86\u6587\u4ef6\"%s\"\u3002\n\u8bf7\u63d0\u4ea4(commit)\u3001\u91cd\u7f6e(reset)\u6216\u6682\u5b58(stash)\u4fee\u6539\u540e\u91cd\u8bd5\u3002"
::msgcat::mcset zh_cn "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "\u6253\u8865\u4e01(Cherry-pick)\u5931\u8d25\u56e0\u4e3a\u5408\u5e76\u51b2\u7a81\u3002\n\u4f60\u662f\u5426\u5e0c\u671b\u8fd0\u884cgit citool \u6765\u89e3\u51b3\u51b2\u7a81\uff1f"
::msgcat::mcset zh_cn "No changes committed" "\u65e0\u5df2\u7ecf\u63d0\u4ea4\u7684\u4fee\u6539"
::msgcat::mcset zh_cn "Commit %s is not included in branch %s -- really revert it?" "\u63d0\u4ea4%s\u4e0d\u5305\u542b\u5728\u5206\u652f%s\u4e2d\uff0c\u786e\u8ba4\u56de\u6eda(revert)\u5b83\uff1f"
::msgcat::mcset zh_cn "Reverting" "\u56de\u6eda\u4e2d(Reverting)"
::msgcat::mcset zh_cn "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "\u56de\u6eda(revert)\u5931\u8d25\uff0c\u56e0\u4e3a\u5982\u4e0b\u7684\u672c\u5730\u6587\u4ef6\u4fee\u6539\uff1a%s\n\u8bf7\u63d0\u4ea4(commit)\u3001\u91cd\u7f6e(reset)\u6216\u8005\u6682\u5b58(stash)\u6539\u53d8\u540e\u91cd\u8bd5\u3002"
::msgcat::mcset zh_cn "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "\u56de\u6eda(revert)\u5931\u8d25\uff0c\u56e0\u4e3a\u5408\u5e76\u51b2\u7a81\u3002\n\u4f60\u662f\u5426\u5e0c\u671b\u8fd0\u884cgit citool\u6765\u89e3\u51b3\u51b2\u7a81\uff1f"
::msgcat::mcset zh_cn "Confirm reset" "\u786e\u8ba4\u91cd\u7f6e(reset)"
::msgcat::mcset zh_cn "Reset branch %s to %s?" "\u91cd\u7f6e(reset)\u5206\u652f%s\u5230%s\uff1f"
::msgcat::mcset zh_cn "Reset type:" "\u91cd\u7f6e(reset)\u7c7b\u578b\uff1a"
::msgcat::mcset zh_cn "Soft: Leave working tree and index untouched" "\u8f6f\u6027\uff1a\u79bb\u5f00\u5de5\u4f5c\u6811\uff0c\u7d22\u5f15\u672a\u6539\u53d8"
::msgcat::mcset zh_cn "Mixed: Leave working tree untouched, reset index" "\u6df7\u5408\uff1a\u79bb\u5f00\u5de5\u4f5c\u6811(\u672a\u6539\u53d8)\uff0c\u7d22\u5f15\u91cd\u7f6e"
::msgcat::mcset zh_cn "Hard: Reset working tree and index\n(discard ALL local changes)" "\u786c\u6027\uff1a\u91cd\u7f6e\u5de5\u4f5c\u6811\u548c\u7d22\u5f15\n(\u4e22\u5f03\u6240\u6709\u7684\u672c\u5730\u4fee\u6539)"
::msgcat::mcset zh_cn "Resetting" "\u91cd\u7f6e\u4e2d(Resetting)"
::msgcat::mcset zh_cn "A local branch named %s exists already" "\u672c\u5730\u5206\u652f%s\u5df2\u7ecf\u5b58\u5728"
::msgcat::mcset zh_cn "Checking out" "\u68c0\u51fa\u4e2d(Checking out)"
::msgcat::mcset zh_cn "Cannot delete the currently checked-out branch" "\u4e0d\u80fd\u5220\u9664\u5f53\u524d\u68c0\u51fa(checkout)\u5206\u652f"
::msgcat::mcset zh_cn "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "\u5728\u5206\u652f%s\u4e0a\u7684\u63d0\u4ea4\u4e0d\u5728\u5176\u4ed6\u4efb\u4f55\u5206\u652f\u4e0a\u3002\n\u786e\u8ba4\u5220\u9664\u5206\u652f%s\uff1f"
::msgcat::mcset zh_cn "Tags and heads: %s" "Tags\u548c\u5934\u6307\u9488(heads)\uff1a%s"
::msgcat::mcset zh_cn "Filter" "\u8fc7\u6ee4\u5668"
::msgcat::mcset zh_cn "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "\u8bfb\u53d6\u63d0\u4ea4\u62d3\u6251\u4fe1\u606f\u51fa\u9519\uff1b\u5206\u652f\u548c\u4e4b\u524d/\u4e4b\u540e\u7684tag\u4fe1\u606f\u5c06\u4e0d\u80fd\u5b8c\u6210\u3002"
::msgcat::mcset zh_cn "Tag" "\u6807\u7b7e(Tag)"
::msgcat::mcset zh_cn "Id" "Id"
::msgcat::mcset zh_cn "Gitk font chooser" "Gitk\u5b57\u4f53\u9009\u62e9"
::msgcat::mcset zh_cn "B" "\u7c97\u4f53"
::msgcat::mcset zh_cn "I" "\u659c\u4f53"
::msgcat::mcset zh_cn "Commit list display options" "\u63d0\u4ea4\u5217\u8868\u5c55\u793a\u9009\u9879"
::msgcat::mcset zh_cn "Maximum graph width (lines)" "\u6700\u5927\u56fe\u5bbd\u5ea6(\u884c\u6570)"
::msgcat::mcset zh_cn "Maximum graph width (% of pane)" "\u6700\u5927\u56fe\u5bbd\u5ea6(%\u7a97\u53e3\u767e\u5206\u6bd4)"
::msgcat::mcset zh_cn "Show local changes" "\u663e\u793a\u672c\u5730\u4fee\u6539"
::msgcat::mcset zh_cn "Auto-select SHA1 (length)" "\u81ea\u52a8\u9009\u62e9SHA1(\u957f\u5ea6)"
::msgcat::mcset zh_cn "Hide remote refs" "\u9690\u85cf\u8fdc\u7a0b\u5f15\u7528"
::msgcat::mcset zh_cn "Diff display options" "\u5dee\u5f02(Diff)\u5c55\u793a\u9009\u9879"
::msgcat::mcset zh_cn "Tab spacing" "\u5236\u8868\u7b26\u5bbd\u5ea6"
::msgcat::mcset zh_cn "Display nearby tags/heads" "\u663e\u793a\u4e34\u8fd1\u7684tags/heads"
::msgcat::mcset zh_cn "Maximum # tags/heads to show" "\u6700\u5927tags/heads\u5c55\u793a\u6570\u91cf"
::msgcat::mcset zh_cn "Limit diffs to listed paths" "diff\u4e2d\u5217\u51fa\u6587\u4ef6\u9650\u5236"
::msgcat::mcset zh_cn "Support per-file encodings" "\u5355\u72ec\u6587\u4ef6\u7f16\u7801\u652f\u6301"
::msgcat::mcset zh_cn "External diff tool" "\u5916\u90e8\u5dee\u5f02(diff)\u5de5\u5177"
::msgcat::mcset zh_cn "Choose..." "\u9009\u62e9..."
::msgcat::mcset zh_cn "General options" "\u5e38\u89c4\u9009\u9879"
::msgcat::mcset zh_cn "Use themed widgets" "\u4f7f\u7528\u4e3b\u9898\u5c0f\u90e8\u4ef6"
::msgcat::mcset zh_cn "(change requires restart)" "(\u9700\u91cd\u542f\u751f\u6548)"
::msgcat::mcset zh_cn "(currently unavailable)" "(\u5f53\u524d\u4e0d\u53ef\u7528)"
::msgcat::mcset zh_cn "Colors: press to choose" "\u989c\u8272\uff1a\u70b9\u51fb\u6765\u9009\u62e9"
::msgcat::mcset zh_cn "Interface" "\u754c\u9762"
::msgcat::mcset zh_cn "interface" "\u754c\u9762"
::msgcat::mcset zh_cn "Background" "\u80cc\u666f"
::msgcat::mcset zh_cn "background" "\u80cc\u666f"
::msgcat::mcset zh_cn "Foreground" "\u524d\u666f"
::msgcat::mcset zh_cn "foreground" "\u524d\u666f"
::msgcat::mcset zh_cn "Diff: old lines" "\u5dee\u5f02(Diff)\uff1a\u8001\u4ee3\u7801\u884c"
::msgcat::mcset zh_cn "diff old lines" "\u5dee\u5f02(diff)\u8001\u4ee3\u7801\u884c"
::msgcat::mcset zh_cn "Diff: new lines" "\u5dee\u5f02(Diff)\uff1a\u65b0\u4ee3\u7801\u884c"
::msgcat::mcset zh_cn "diff new lines" "\u5dee\u5f02(diff)\u65b0\u4ee3\u7801\u884c"
::msgcat::mcset zh_cn "Diff: hunk header" "\u5dee\u5f02(Diff)\uff1a\u8865\u4e01\u7247\u6bb5\u5934\u4fe1\u606f"
::msgcat::mcset zh_cn "diff hunk header" "\u5dee\u5f02(diff)\u8865\u4e01\u7247\u6bb5\u5934\u4fe1\u606f"
::msgcat::mcset zh_cn "Marked line bg" "\u5df2\u6807\u8bb0\u4ee3\u7801\u884c\u80cc\u666f"
::msgcat::mcset zh_cn "marked line background" "\u5df2\u6807\u8bb0\u4ee3\u7801\u884c\u80cc\u666f"
::msgcat::mcset zh_cn "Select bg" "\u9009\u62e9\u80cc\u666f"
::msgcat::mcset zh_cn "Fonts: press to choose" "\u5b57\u4f53\uff1a\u70b9\u51fb\u6765\u9009\u62e9"
::msgcat::mcset zh_cn "Main font" "\u4e3b\u5b57\u4f53"
::msgcat::mcset zh_cn "Diff display font" "\u5dee\u5f02(Diff)\u663e\u793a\u5b57\u4f53"
::msgcat::mcset zh_cn "User interface font" "\u7528\u6237\u754c\u9762\u5b57\u4f53"
::msgcat::mcset zh_cn "Gitk preferences" "Gitk\u504f\u597d\u8bbe\u7f6e"
::msgcat::mcset zh_cn "General" "\u5e38\u89c4"
::msgcat::mcset zh_cn "Colors" "\u989c\u8272"
::msgcat::mcset zh_cn "Fonts" "\u5b57\u4f53"
::msgcat::mcset zh_cn "Gitk: choose color for %s" "Gitk\uff1a\u9009\u62e9\u989c\u8272\u7528\u4e8e%s"
::msgcat::mcset zh_cn "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "\u5bf9\u4e0d\u8d77\uff0cgitk\u4e0d\u80fd\u8fd0\u884c\u5728\u5f53\u524d\u7248\u672c\u7684Tcl/Tk\u4e2d\u3002\nGitk\u8fd0\u884c\u9700\u8981\u6700\u4f4e\u7248\u672c\u4e3aTcl/Tk8.4\u3002"
::msgcat::mcset zh_cn "Cannot find a git repository here." "\u5728\u6b64\u4f4d\u7f6e\u672a\u53d1\u73b0git\u4ed3\u5e93\u3002"
::msgcat::mcset zh_cn "Ambiguous argument '%s': both revision and filename" "\u4e0d\u660e\u786e\u6709\u6b67\u4e49\u7684\u53c2\u6570\"%s\"\uff1a\u7248\u672c\u548c\u6587\u4ef6\u540d\u79f0"
::msgcat::mcset zh_cn "Bad arguments to gitk:" "\u8fd0\u884cgitk\u53c2\u6570\u9519\u8bef\uff1a"
