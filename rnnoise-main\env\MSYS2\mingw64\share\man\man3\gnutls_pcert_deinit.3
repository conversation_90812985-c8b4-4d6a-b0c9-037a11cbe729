.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pcert_deinit" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pcert_deinit \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "void gnutls_pcert_deinit(gnutls_pcert_st * " pcert ");"
.SH ARGUMENTS
.IP "gnutls_pcert_st * pcert" 12
The structure to be deinitialized
.SH "DESCRIPTION"
This function will deinitialize a pcert structure.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
