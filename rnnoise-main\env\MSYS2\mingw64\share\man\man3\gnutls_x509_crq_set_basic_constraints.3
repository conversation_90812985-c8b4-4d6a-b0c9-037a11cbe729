.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_set_basic_constraints" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_set_basic_constraints \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_set_basic_constraints(gnutls_x509_crq_t " crq ", unsigned int " ca ", int " pathLenConstraint ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
a certificate request of type \fBgnutls_x509_crq_t\fP
.IP "unsigned int ca" 12
true(1) or false(0) depending on the Certificate authority status.
.IP "int pathLenConstraint" 12
non\-negative error codes indicate maximum length of path,
and negative error codes indicate that the pathLenConstraints field should
not be present.
.SH "DESCRIPTION"
This function will set the basicConstraints certificate extension.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
