.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pcert_import_x509_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pcert_import_x509_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pcert_import_x509_raw(gnutls_pcert_st * " pcert ", const gnutls_datum_t * " cert ", gnutls_x509_crt_fmt_t " format ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pcert_st * pcert" 12
The pcert structure
.IP "const gnutls_datum_t * cert" 12
The raw certificate to be imported
.IP "gnutls_x509_crt_fmt_t format" 12
The format of the certificate
.IP "unsigned int flags" 12
zero for now
.SH "DESCRIPTION"
This convenience function will import the given certificate to a
\fBgnutls_pcert_st\fP structure. The structure must be deinitialized
afterwards using \fBgnutls_pcert_deinit()\fP;
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
