CMP0037
-------

.. |REMOVED_IN_CMAKE_VERSION| replace:: 4.0
.. include:: REMOVED_PROLOGUE.txt

Target names should not be reserved and should match a validity pattern.

CMake 2.8.12 and lower allowed creating targets using :command:`add_library`,
:command:`add_executable` and :command:`add_custom_target` with unrestricted
choice for the target name.  Newer cmake features such
as :manual:`cmake-generator-expressions(7)` and some
diagnostics expect target names to match a restricted pattern.

Target names may contain upper and lower case letters, numbers, the underscore
character (``_``), dot(``.``), plus(``+``) and minus(``-``).
As a special case, ``ALIAS`` and ``IMPORTED`` targets may contain
two consecutive colons.

Target names reserved by one or more CMake generators are not allowed.
Among others these include ``all``, ``clean``, ``help``, and ``install``.

Target names associated with optional features, such as ``test`` and
``package``, may also be reserved.  CMake 3.10 and below always reserve them.
CMake 3.11 and above reserve them only when the corresponding feature is
enabled (e.g. by including the :module:`CTest` or :module:`CPack` modules).

The ``OLD`` behavior for this policy is to allow creating targets with
reserved names or which do not match the validity pattern.
The ``NEW`` behavior for this policy is to report an error
if an add_* command is used with an invalid target name.

.. |INTRODUCED_IN_CMAKE_VERSION| replace:: 3.0
.. |WARNED_OR_DID_NOT_WARN| replace:: warned
.. include:: REMOVED_EPILOGUE.txt
