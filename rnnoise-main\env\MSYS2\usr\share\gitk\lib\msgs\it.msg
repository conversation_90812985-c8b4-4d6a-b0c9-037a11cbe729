set ::msgcat::header "Project-Id-Version: gitk\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2010-01-28 18:41+0100\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Italian\nLanguage: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset it "Couldn't get list of unmerged files:" "Impossibile ottenere l'elenco dei file in attesa di fusione:"
::msgcat::mcset it "Error parsing revisions:" "Errore nella lettura delle revisioni:"
::msgcat::mcset it "Error executing --argscmd command:" "Errore nell'esecuzione del comando specificato con --argscmd:"
::msgcat::mcset it "No files selected: --merge specified but no files are unmerged." "Nessun file selezionato: \u00e8 stata specificata l'opzione --merge ma non ci sono file in attesa di fusione."
::msgcat::mcset it "No files selected: --merge specified but no unmerged files are within file limit." "Nessun file selezionato: \u00e8 stata specificata l'opzione --merge ma i file specificati non sono in attesa di fusione."
::msgcat::mcset it "Error executing git log:" "Errore nell'esecuzione di git log:"
::msgcat::mcset it "Reading" "Lettura in corso"
::msgcat::mcset it "Reading commits..." "Lettura delle revisioni in corso..."
::msgcat::mcset it "No commits selected" "Nessuna revisione selezionata"
::msgcat::mcset it "Command line" "Linea di comando"
::msgcat::mcset it "Can't parse git log output:" "Impossibile elaborare i dati di git log:"
::msgcat::mcset it "No commit information available" "Nessuna informazione disponibile sulle revisioni"
::msgcat::mcset it "OK" "OK"
::msgcat::mcset it "Cancel" "Annulla"
::msgcat::mcset it "&Update" "Aggiorna"
::msgcat::mcset it "&Reload" "Ricarica"
::msgcat::mcset it "Reread re&ferences" "Rileggi riferimenti"
::msgcat::mcset it "&List references" "Elenca riferimenti"
::msgcat::mcset it "Start git &gui" "Avvia git gui"
::msgcat::mcset it "&Quit" "Esci"
::msgcat::mcset it "&File" "&File"
::msgcat::mcset it "&Preferences" "Preferenze"
::msgcat::mcset it "&Edit" "Modifica"
::msgcat::mcset it "&New view..." "Nuova vista..."
::msgcat::mcset it "&Edit view..." "Modifica vista..."
::msgcat::mcset it "&Delete view" "Elimina vista"
::msgcat::mcset it "&All files" "Tutti i file"
::msgcat::mcset it "&View" "Vista"
::msgcat::mcset it "&About gitk" "Informazioni su gitk"
::msgcat::mcset it "&Key bindings" "Scorciatoie da tastiera"
::msgcat::mcset it "&Help" "Aiuto"
::msgcat::mcset it "SHA1 ID:" "SHA1 ID:"
::msgcat::mcset it "Row" "Riga"
::msgcat::mcset it "Find" "Trova"
::msgcat::mcset it "commit" "revisione"
::msgcat::mcset it "containing:" "contenente:"
::msgcat::mcset it "touching paths:" "che riguarda i percorsi:"
::msgcat::mcset it "adding/removing string:" "che aggiunge/rimuove la stringa:"
::msgcat::mcset it "Exact" "Esatto"
::msgcat::mcset it "All fields" "Tutti i campi"
::msgcat::mcset it "Headline" "Titolo"
::msgcat::mcset it "Comments" "Commenti"
::msgcat::mcset it "Author" "Autore"
::msgcat::mcset it "Committer" "Revisione creata da"
::msgcat::mcset it "Search" "Cerca"
::msgcat::mcset it "Old version" "Vecchia versione"
::msgcat::mcset it "New version" "Nuova versione"
::msgcat::mcset it "Lines of context" "Linee di contesto"
::msgcat::mcset it "Ignore space change" "Ignora modifiche agli spazi"
::msgcat::mcset it "Patch" "Modifiche"
::msgcat::mcset it "Tree" "Directory"
::msgcat::mcset it "Diff this -> selected" "Diff questo -> selezionato"
::msgcat::mcset it "Diff selected -> this" "Diff selezionato -> questo"
::msgcat::mcset it "Make patch" "Crea patch"
::msgcat::mcset it "Create tag" "Crea etichetta"
::msgcat::mcset it "Write commit to file" "Scrivi revisione in un file"
::msgcat::mcset it "Create new branch" "Crea un nuovo ramo"
::msgcat::mcset it "Cherry-pick this commit" "Porta questa revisione in cima al ramo attuale"
::msgcat::mcset it "Reset HEAD branch to here" "Aggiorna il ramo HEAD a questa revisione"
::msgcat::mcset it "Mark this commit" "Segna questa revisione"
::msgcat::mcset it "Return to mark" "Torna alla revisione segnata"
::msgcat::mcset it "Find descendant of this and mark" "Trova il discendente di questa revisione e di quella segnata"
::msgcat::mcset it "Compare with marked commit" "Confronta con la revisione segnata"
::msgcat::mcset it "Check out this branch" "Attiva questo ramo"
::msgcat::mcset it "Remove this branch" "Elimina questo ramo"
::msgcat::mcset it "Highlight this too" "Evidenzia anche questo"
::msgcat::mcset it "Highlight this only" "Evidenzia solo questo"
::msgcat::mcset it "External diff" "Visualizza differenze in un altro programma"
::msgcat::mcset it "Blame parent commit" "Annota la revisione precedente"
::msgcat::mcset it "Show origin of this line" "Mostra la provenienza di questa riga"
::msgcat::mcset it "Run git gui blame on this line" "Esegui git gui blame su questa riga"
::msgcat::mcset it "Close" "Chiudi"
::msgcat::mcset it "Gitk key bindings" "Scorciatoie da tastiera di Gitk"
::msgcat::mcset it "Gitk key bindings:" "Scorciatoie da tastiera di Gitk:"
::msgcat::mcset it "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Esci"
::msgcat::mcset it "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009Vai alla prima revisione"
::msgcat::mcset it "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009Vai all'ultima revisione"
::msgcat::mcset it "<Right>, x, l\u0009Go forward in history list" "<Destra>, x, l\u0009Vai avanti nella cronologia"
::msgcat::mcset it "<PageUp>\u0009Move up one page in commit list" "<PaginaSu>\u0009Vai pi\u00f9 in alto di una pagina nella lista delle revisioni"
::msgcat::mcset it "<PageDown>\u0009Move down one page in commit list" "<PaginaGi\u00f9>\u0009Vai pi\u00f9 in basso di una pagina nella lista delle revisioni"
::msgcat::mcset it "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009Scorri alla cima della lista delle revisioni"
::msgcat::mcset it "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009Scorri alla fine della lista delle revisioni"
::msgcat::mcset it "<%s-Up>\u0009Scroll commit list up one line" "<%s-Su>\u0009Scorri la lista delle revisioni in alto di una riga"
::msgcat::mcset it "<%s-Down>\u0009Scroll commit list down one line" "<%s-Gi\u00f9>\u0009Scorri la lista delle revisioni in basso di una riga"
::msgcat::mcset it "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PaginaSu>\u0009Scorri la lista delle revisioni in alto di una pagina"
::msgcat::mcset it "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PaginaGi\u00f9>\u0009Scorri la lista delle revisioni in basso di una pagina"
::msgcat::mcset it "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Su>\u0009Trova all'indietro (verso l'alto, revisioni successive)"
::msgcat::mcset it "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Gi\u00f9>\u0009Trova in avanti (verso il basso, revisioni precedenti)"
::msgcat::mcset it "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009Scorri la vista delle differenze in alto di una pagina"
::msgcat::mcset it "<Backspace>\u0009Scroll diff view up one page" "<Backspace>\u0009Scorri la vista delle differenze in alto di una pagina"
::msgcat::mcset it "<Space>\u0009\u0009Scroll diff view down one page" "<Spazio>\u0009\u0009Scorri la vista delle differenze in basso di una pagina"
::msgcat::mcset it "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009Scorri la vista delle differenze in alto di 18 linee"
::msgcat::mcset it "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009Scorri la vista delle differenze in basso di 18 linee"
::msgcat::mcset it "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009Trova"
::msgcat::mcset it "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009Trova in avanti"
::msgcat::mcset it "<Return>\u0009Move to next find hit" "<Invio>\u0009Trova in avanti"
::msgcat::mcset it "/\u0009\u0009Focus the search box" "/\u0009\u0009Cursore nel box di ricerca"
::msgcat::mcset it "?\u0009\u0009Move to previous find hit" "?\u0009\u0009Trova all'indietro"
::msgcat::mcset it "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009Scorri la vista delle differenze al file successivo"
::msgcat::mcset it "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009Cerca in avanti nella vista delle differenze"
::msgcat::mcset it "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009Cerca all'indietro nella vista delle differenze"
::msgcat::mcset it "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009Aumenta dimensione carattere"
::msgcat::mcset it "<%s-plus>\u0009Increase font size" "<%s-pi\u00f9>\u0009Aumenta dimensione carattere"
::msgcat::mcset it "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009Diminuisci dimensione carattere"
::msgcat::mcset it "<%s-minus>\u0009Decrease font size" "<%s-meno>\u0009Diminuisci dimensione carattere"
::msgcat::mcset it "<F5>\u0009\u0009Update" "<F5>\u0009\u0009Aggiorna"
::msgcat::mcset it "Error creating temporary directory %s:" "Errore durante la creazione della directory temporanea %s:"
::msgcat::mcset it "Error getting \"%s\" from %s:" "Errore nella lettura di \"%s\" da %s:"
::msgcat::mcset it "command failed:" "impossibile eseguire il comando:"
::msgcat::mcset it "No such commit" "Revisione inesistente"
::msgcat::mcset it "git gui blame: command failed:" "git gui blame: impossibile eseguire il comando:"
::msgcat::mcset it "Couldn't read merge head: %s" "Impossibile leggere merge head: %s"
::msgcat::mcset it "Error reading index: %s" "Errore nella lettura dell'indice: %s"
::msgcat::mcset it "Couldn't start git blame: %s" "Impossibile eseguire git blame: %s"
::msgcat::mcset it "Searching" "Ricerca in corso"
::msgcat::mcset it "Error running git blame: %s" "Errore nell'esecuzione di git blame: %s"
::msgcat::mcset it "That line comes from commit %s,  which is not in this view" "Quella riga proviene dalla revisione %s, non presente in questa vista"
::msgcat::mcset it "External diff viewer failed:" "Impossibile eseguire il visualizzatore di differenze:"
::msgcat::mcset it "Gitk view definition" "Scelta vista Gitk"
::msgcat::mcset it "Remember this view" "Ricorda questa vista"
::msgcat::mcset it "References (space separated list):" "Riferimenti (lista di elementi separati da spazi)"
::msgcat::mcset it "Branches & tags:" "Rami ed etichette"
::msgcat::mcset it "All refs" "Tutti i riferimenti"
::msgcat::mcset it "All (local) branches" "Tutti i rami (locali)"
::msgcat::mcset it "All tags" "Tutte le etichette"
::msgcat::mcset it "All remote-tracking branches" "Tutti i rami remoti"
::msgcat::mcset it "Commit Info (regular expressions):" "Informazioni sulla revisione (espressioni regolari):"
::msgcat::mcset it "Author:" "Autore:"
::msgcat::mcset it "Committer:" "Revisione creata da:"
::msgcat::mcset it "Commit Message:" "Messaggio di revisione:"
::msgcat::mcset it "Matches all Commit Info criteria" "Risponde a tutti i criteri di ricerca sulle revisioni"
::msgcat::mcset it "Changes to Files:" "Modifiche ai file:"
::msgcat::mcset it "Fixed String" "Stringa fissa"
::msgcat::mcset it "Regular Expression" "Espressione regolare"
::msgcat::mcset it "Search string:" "Cerca stringa:"
::msgcat::mcset it "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "Date di revisione (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):"
::msgcat::mcset it "Since:" "Da:"
::msgcat::mcset it "Until:" "A:"
::msgcat::mcset it "Limit and/or skip a number of revisions (positive integer):" "Limita e/o salta N revisioni (intero positivo):"
::msgcat::mcset it "Number to show:" "Numero di revisioni da mostrare:"
::msgcat::mcset it "Number to skip:" "Numero di revisioni da saltare:"
::msgcat::mcset it "Miscellaneous options:" "Altre opzioni:"
::msgcat::mcset it "Strictly sort by date" "Ordina solo per data"
::msgcat::mcset it "Mark branch sides" "Segna i lati del ramo"
::msgcat::mcset it "Limit to first parent" "Limita al primo genitore"
::msgcat::mcset it "Simple history" "Cronologia semplificata"
::msgcat::mcset it "Additional arguments to git log:" "Ulteriori argomenti da passare a git log:"
::msgcat::mcset it "Enter files and directories to include, one per line:" "Inserire file e directory da includere, uno per riga:"
::msgcat::mcset it "Command to generate more commits to include:" "Comando che genera altre revisioni da visualizzare:"
::msgcat::mcset it "Gitk: edit view" "Gitk: modifica vista"
::msgcat::mcset it "-- criteria for selecting revisions" "-- criteri per la scelta delle revisioni"
::msgcat::mcset it "View Name" "Nome vista"
::msgcat::mcset it "Apply (F5)" "Applica (F5)"
::msgcat::mcset it "Error in commit selection arguments:" "Errore negli argomenti di selezione delle revisioni:"
::msgcat::mcset it "None" "Nessuno"
::msgcat::mcset it "Descendant" "Discendente"
::msgcat::mcset it "Not descendant" "Non discendente"
::msgcat::mcset it "Ancestor" "Ascendente"
::msgcat::mcset it "Not ancestor" "Non ascendente"
::msgcat::mcset it "Local changes checked in to index but not committed" "Modifiche locali presenti nell'indice ma non nell'archivio"
::msgcat::mcset it "Local uncommitted changes, not checked in to index" "Modifiche locali non presenti n\u00e9 nell'archivio n\u00e9 nell'indice"
::msgcat::mcset it "many" "molti"
::msgcat::mcset it "Tags:" "Etichette:"
::msgcat::mcset it "Parent" "Genitore"
::msgcat::mcset it "Child" "Figlio"
::msgcat::mcset it "Branch" "Ramo"
::msgcat::mcset it "Follows" "Segue"
::msgcat::mcset it "Precedes" "Precede"
::msgcat::mcset it "Error getting diffs: %s" "Errore nella lettura delle differenze:"
::msgcat::mcset it "Goto:" "Vai a:"
::msgcat::mcset it "Short SHA1 id %s is ambiguous" "La SHA1 id abbreviata %s \u00e8 ambigua"
::msgcat::mcset it "Revision %s is not known" "La revisione %s \u00e8 sconosciuta"
::msgcat::mcset it "SHA1 id %s is not known" "La SHA1 id %s \u00e8 sconosciuta"
::msgcat::mcset it "Revision %s is not in the current view" "La revisione %s non \u00e8 presente nella vista attuale"
::msgcat::mcset it "Date" "Data"
::msgcat::mcset it "Children" "Figli"
::msgcat::mcset it "Reset %s branch to here" "Aggiorna il ramo %s a questa revisione"
::msgcat::mcset it "Detached head: can't reset" "Nessun ramo attivo: reset impossibile"
::msgcat::mcset it "Skipping merge commit " "Salto la revisione di fusione "
::msgcat::mcset it "Error getting patch ID for " "Errore nella identificazione della patch per "
::msgcat::mcset it " - stopping\n" " - fine\n"
::msgcat::mcset it "Commit " "La revisione "
::msgcat::mcset it " is the same patch as\n       " " ha le stesse differenze di\n       "
::msgcat::mcset it " differs from\n       " " \u00e8 diversa da\n       "
::msgcat::mcset it "Diff of commits:\n\n" "Differenze tra le revisioni:\n\n"
::msgcat::mcset it " has %s children - stopping\n" " ha %s figli - fine\n"
::msgcat::mcset it "Error writing commit to file: %s" "Errore nella scrittura della revisione nel file: %s"
::msgcat::mcset it "Error diffing commits: %s" "Errore nelle differenze tra le revisioni: %s"
::msgcat::mcset it "Top" "Inizio"
::msgcat::mcset it "From" "Da"
::msgcat::mcset it "To" "A"
::msgcat::mcset it "Generate patch" "Genera patch"
::msgcat::mcset it "From:" "Da:"
::msgcat::mcset it "To:" "A:"
::msgcat::mcset it "Reverse" "Inverti"
::msgcat::mcset it "Output file:" "Scrivi sul file:"
::msgcat::mcset it "Generate" "Genera"
::msgcat::mcset it "Error creating patch:" "Errore nella creazione della patch:"
::msgcat::mcset it "ID:" "ID:"
::msgcat::mcset it "Tag name:" "Nome etichetta:"
::msgcat::mcset it "Tag message is optional" "Il messaggio dell'etichetta \u00e8 opzionale"
::msgcat::mcset it "Tag message:" "Messaggio dell'etichetta:"
::msgcat::mcset it "Create" "Crea"
::msgcat::mcset it "No tag name specified" "Nessuna etichetta specificata"
::msgcat::mcset it "Tag \"%s\" already exists" "L'etichetta \"%s\" esiste gi\u00e0"
::msgcat::mcset it "Error creating tag:" "Errore nella creazione dell'etichetta:"
::msgcat::mcset it "Command:" "Comando:"
::msgcat::mcset it "Write" "Scrivi"
::msgcat::mcset it "Error writing commit:" "Errore nella scrittura della revisione:"
::msgcat::mcset it "Name:" "Nome:"
::msgcat::mcset it "Please specify a name for the new branch" "Specificare un nome per il nuovo ramo"
::msgcat::mcset it "Branch '%s' already exists. Overwrite?" "Il ramo '%s' esiste gi\u00e0. Sovrascrivere?"
::msgcat::mcset it "Commit %s is already included in branch %s -- really re-apply it?" "La revisione %s \u00e8 gi\u00e0 inclusa nel ramo %s -- applicarla di nuovo?"
::msgcat::mcset it "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "Impossibile eseguire cherry-pick perch\u00e9 il file '%s' \u00e8 stato modificato nella directory di lavoro.\nPrima di riprovare, bisogna creare una nuova revisione, annullare le modifiche o usare 'git stash'."
::msgcat::mcset it "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "Impossibile eseguire cherry-pick a causa di un conflitto nella fusione.\nVuoi avviare git citool per risolverlo?"
::msgcat::mcset it "No changes committed" "Nessuna modifica archiviata"
::msgcat::mcset it "Confirm reset" "Conferma git reset"
::msgcat::mcset it "Reset branch %s to %s?" "Aggiornare il ramo %s a %s?"
::msgcat::mcset it "Reset type:" "Tipo di aggiornamento:"
::msgcat::mcset it "Soft: Leave working tree and index untouched" "Soft: Lascia la direcory di lavoro e l'indice come sono"
::msgcat::mcset it "Mixed: Leave working tree untouched, reset index" "Mixed: Lascia la directory di lavoro come \u00e8, aggiorna l'indice"
::msgcat::mcset it "Hard: Reset working tree and index\n(discard ALL local changes)" "Hard: Aggiorna la directory di lavoro e l'indice\n(abbandona TUTTE le modifiche locali)"
::msgcat::mcset it "Resetting" "git reset in corso"
::msgcat::mcset it "Checking out" "Attivazione in corso"
::msgcat::mcset it "Cannot delete the currently checked-out branch" "Impossibile cancellare il ramo attualmente attivo"
::msgcat::mcset it "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "Le revisioni nel ramo %s non sono presenti su altri rami.\nCancellare il ramo %s?"
::msgcat::mcset it "Tags and heads: %s" "Etichette e rami: %s"
::msgcat::mcset it "Filter" "Filtro"
::msgcat::mcset it "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "Errore nella lettura della topologia delle revisioni: le informazioni sul ramo e le etichette precedenti e seguenti saranno incomplete."
::msgcat::mcset it "Tag" "Etichetta"
::msgcat::mcset it "Id" "Id"
::msgcat::mcset it "Gitk font chooser" "Scelta caratteri gitk"
::msgcat::mcset it "B" "B"
::msgcat::mcset it "I" "I"
::msgcat::mcset it "Commit list display options" "Opzioni visualizzazione dell'elenco revisioni"
::msgcat::mcset it "Maximum graph width (lines)" "Larghezza massima del grafico (in linee)"
::msgcat::mcset it "Maximum graph width (% of pane)" "Larghezza massima del grafico (% del pannello)"
::msgcat::mcset it "Show local changes" "Mostra modifiche locali"
::msgcat::mcset it "Hide remote refs" "Nascondi i riferimenti remoti"
::msgcat::mcset it "Diff display options" "Opzioni di visualizzazione delle differenze"
::msgcat::mcset it "Tab spacing" "Spaziatura tabulazioni"
::msgcat::mcset it "Limit diffs to listed paths" "Limita le differenze ai percorsi elencati"
::msgcat::mcset it "Support per-file encodings" "Attiva codifica file per file"
::msgcat::mcset it "External diff tool" "Visualizzatore di differenze"
::msgcat::mcset it "Choose..." "Scegli..."
::msgcat::mcset it "General options" "Opzioni generali"
::msgcat::mcset it "Use themed widgets" "Utilizza interfaccia a tema"
::msgcat::mcset it "(change requires restart)" "(una modifica richiede il riavvio)"
::msgcat::mcset it "(currently unavailable)" "(momentaneamente non disponibile)"
::msgcat::mcset it "Colors: press to choose" "Colori: premere per scegliere"
::msgcat::mcset it "Interface" "Interfaccia"
::msgcat::mcset it "interface" "interfaccia"
::msgcat::mcset it "Background" "Sfondo"
::msgcat::mcset it "background" "sfondo"
::msgcat::mcset it "Foreground" "Primo piano"
::msgcat::mcset it "foreground" "primo piano"
::msgcat::mcset it "Diff: old lines" "Diff: vecchie linee"
::msgcat::mcset it "diff old lines" "vecchie linee"
::msgcat::mcset it "Diff: new lines" "Diff: nuove linee"
::msgcat::mcset it "diff new lines" "nuove linee"
::msgcat::mcset it "Diff: hunk header" "Diff: intestazione della sezione"
::msgcat::mcset it "diff hunk header" "intestazione della sezione"
::msgcat::mcset it "Marked line bg" "Sfondo riga selezionata"
::msgcat::mcset it "marked line background" "sfondo riga selezionata"
::msgcat::mcset it "Select bg" "Sfondo"
::msgcat::mcset it "Fonts: press to choose" "Carattere: premere per scegliere"
::msgcat::mcset it "Main font" "Carattere principale"
::msgcat::mcset it "Diff display font" "Carattere per differenze"
::msgcat::mcset it "User interface font" "Carattere per interfaccia utente"
::msgcat::mcset it "Gitk preferences" "Preferenze gitk"
::msgcat::mcset it "Gitk: choose color for %s" "Gitk: scegliere un colore per %s"
::msgcat::mcset it "Cannot find a git repository here." "Archivio git non trovato."
::msgcat::mcset it "Ambiguous argument '%s': both revision and filename" "Argomento ambiguo: '%s' \u00e8 sia revisione che nome di file"
::msgcat::mcset it "Bad arguments to gitk:" "Gitk: argomenti errati:"
