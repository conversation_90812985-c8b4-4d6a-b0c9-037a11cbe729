.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_verify_data2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_verify_data2 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_verify_data2(gnutls_x509_crt_t " crt ", gnutls_sign_algorithm_t " algo ", unsigned int " flags ", const gnutls_datum_t * " data ", const gnutls_datum_t * " signature ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
Holds the certificate to verify with
.IP "gnutls_sign_algorithm_t algo" 12
The signature algorithm used
.IP "unsigned int flags" 12
Zero or an OR list of \fBgnutls_certificate_verify_flags\fP
.IP "const gnutls_datum_t * data" 12
holds the signed data
.IP "const gnutls_datum_t * signature" 12
contains the signature
.SH "DESCRIPTION"
This function will verify the given signed data, using the
parameters from the certificate.
.SH "RETURNS"
In case of a verification failure \fBGNUTLS_E_PK_SIG_VERIFY_FAILED\fP
is returned, \fBGNUTLS_E_EXPIRED\fP or \fBGNUTLS_E_NOT_YET_ACTIVATED\fP on expired
or not yet activated certificate and zero or positive code on success.

Note that since GnuTLS 3.5.6 this function introduces checks in the
end certificate ( \fIcrt\fP ), including time checks and key usage checks.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
