<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(NASMBeforeTargets)' == '' and '$(NASMAfterTargets)' == '' and '$(ConfigurationType)' != 'Makefile'">
    <NASMBeforeTargets>Midl</NASMBeforeTargets>
    <NASMAfterTargets>CustomBuild</NASMAfterTargets>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <NASM>
      <OutputFormat>$(IntDir)%(FileName).obj</OutputFormat>
      <Outputswitch>0</Outputswitch>
      <CompilerNasm>@CMAKE_ASM_NASM_COMPILER@</CompilerNasm>
      <PackAlignmentBoundary>0</PackAlignmentBoundary>
      <CommandLineTemplate>"%(CompilerNasm)" [AllOptions] [AdditionalOptions] "%(FullPath)"</CommandLineTemplate>
      <ExecutionDescription>Assembling %(Filename)%(Extension)</ExecutionDescription>
    </NASM>
  </ItemDefinitionGroup>
</Project>
