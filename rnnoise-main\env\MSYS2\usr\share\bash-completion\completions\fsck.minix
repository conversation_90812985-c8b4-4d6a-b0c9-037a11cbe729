_fsck.minix_module()
{
	local cur OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	case $cur in
		-*)
			OPTS="--list --auto --repair --verbose --super --uncleared --force --help --version"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	compopt -o bashdefault -o default
	COMPREPLY=( $(compgen -W "$(lsblk -pnro name)" -- $cur) )
	return 0
}
complete -F _fsck.minix_module fsck.minix
