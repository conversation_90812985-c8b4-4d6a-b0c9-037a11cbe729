.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_sign_get_pk_algorithm" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_sign_get_pk_algorithm \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_pk_algorithm_t gnutls_sign_get_pk_algorithm(gnutls_sign_algorithm_t " sign ");"
.SH ARGUMENTS
.IP "gnutls_sign_algorithm_t sign" 12
is a signature algorithm
.SH "DESCRIPTION"
This function returns the public key algorithm corresponding to
the given signature algorithms. Note that there may be multiple
public key algorithms supporting a particular signature type;
when dealing with such algorithms use instead \fBgnutls_sign_supports_pk_algorithm()\fP.
.SH "SINCE"
3.1.1
.SH "RETURNS"
return a \fBgnutls_pk_algorithm_t\fP value, or \fBGNUTLS_PK_UNKNOWN\fP on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
