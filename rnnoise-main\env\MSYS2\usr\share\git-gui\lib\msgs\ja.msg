set ::msgcat::header "Project-Id-Version: git-gui\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2019-10-13 23:20+0900\nLast-Translator: KIDAN<PERSON> Akito <<EMAIL>>\nLanguage-Team: Japanese\nLanguage: ja\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset ja "Invalid font specified in %s:" "%s \u306b\u7121\u52b9\u306a\u30d5\u30a9\u30f3\u30c8\u304c\u6307\u5b9a\u3055\u308c\u3066\u3044\u307e\u3059:"
::msgcat::mcset ja "Main Font" "\u4e3b\u30d5\u30a9\u30f3\u30c8"
::msgcat::mcset ja "Diff/Console Font" "diff/\u30b3\u30f3\u30bd\u30fc\u30eb\u30fb\u30d5\u30a9\u30f3\u30c8"
::msgcat::mcset ja "git-gui: fatal error" "git-gui: \u81f4\u547d\u7684\u306a\u30a8\u30e9\u30fc"
::msgcat::mcset ja "Cannot find git in PATH." "PATH \u4e2d\u306b git \u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "Cannot parse Git version string:" "Git \u30d0\u30fc\u30b8\u30e7\u30f3\u540d\u304c\u7406\u89e3\u3067\u304d\u307e\u305b\u3093:"
::msgcat::mcset ja "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "Git \u306e\u30d0\u30fc\u30b8\u30e7\u30f3\u304c\u78ba\u8a8d\u3067\u304d\u307e\u305b\u3093\u3002\n\n%s \u306f\u30d0\u30fc\u30b8\u30e7\u30f3 '%s' \u3068\u306e\u3053\u3068\u3067\u3059\u3002\n\n%s \u306f\u6700\u4f4e\u3067\u3082 1.5.0 \u304b\u305d\u308c\u4ee5\u964d\u306e Git \u304c\u5fc5\u8981\u3067\u3059\n\n'%s' \u306f\u30d0\u30fc\u30b8\u30e7\u30f3 1.5.0 \u3068\u601d\u3063\u3066\u826f\u3044\u3067\u3059\u304b\uff1f\n"
::msgcat::mcset ja "Git directory not found:" "Git \u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093:"
::msgcat::mcset ja "Cannot move to top of working directory:" "\u4f5c\u696d\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u306e\u6700\u4e0a\u4f4d\u306b\u79fb\u52d5\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "Cannot use bare repository:" "\u88f8\u306e\u30ea\u30dd\u30b8\u30c8\u30ea\u306f\u4f7f\u3048\u307e\u305b\u3093:"
::msgcat::mcset ja "No working directory" "\u4f5c\u696d\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u304c\u3042\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "Refreshing file status..." "\u30d5\u30a1\u30a4\u30eb\u72b6\u614b\u3092\u66f4\u65b0\u3057\u3066\u3044\u307e\u3059\u2026"
::msgcat::mcset ja "Scanning for modified files ..." "\u5909\u66f4\u3055\u308c\u305f\u30d5\u30a1\u30a4\u30eb\u3092\u30b9\u30ad\u30e3\u30f3\u3057\u3066\u3044\u307e\u3059\u2026"
::msgcat::mcset ja "Calling prepare-commit-msg hook..." "prepare-commit-msg \u30d5\u30c3\u30af\u3092\u5b9f\u884c\u4e2d\u30fb\u30fb\u30fb"
::msgcat::mcset ja "Commit declined by prepare-commit-msg hook." "prepare-commit-msg \u30d5\u30c3\u30af\u304c\u30b3\u30df\u30c3\u30c8\u3092\u62d2\u5426\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Ready." "\u6e96\u5099\u5b8c\u4e86"
::msgcat::mcset ja "Display limit (gui.maxfilesdisplayed = %s) reached, not showing all %s files." "\u8868\u793a\u53ef\u80fd\u306a\u9650\u754c (gui.maxfilesdisplayed = %s) \u306b\u9054\u3057\u305f\u3081\u3001\u5168\u4f53\u3067%s\u500b\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u8868\u793a\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "Unmodified" "\u5909\u66f4\u7121\u3057"
::msgcat::mcset ja "Modified, not staged" "\u5909\u66f4\u3042\u308a\u3001\u30b3\u30df\u30c3\u30c8\u672a\u4e88\u5b9a"
::msgcat::mcset ja "Staged for commit" "\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u6e08"
::msgcat::mcset ja "Portions staged for commit" "\u90e8\u5206\u7684\u306b\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u6e08"
::msgcat::mcset ja "Staged for commit, missing" "\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u6e08\u3001\u30d5\u30a1\u30a4\u30eb\u7121\u3057"
::msgcat::mcset ja "File type changed, not staged" "\u30d5\u30a1\u30a4\u30eb\u578b\u5909\u66f4\u3001\u30b3\u30df\u30c3\u30c8\u672a\u4e88\u5b9a"
::msgcat::mcset ja "File type changed, old type staged for commit" "\u30d5\u30a1\u30a4\u30eb\u578b\u5909\u66f4\u3001\u65e7\u578b\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u6e08"
::msgcat::mcset ja "File type changed, staged" "\u30d5\u30a1\u30a4\u30eb\u578b\u5909\u66f4\u3001\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u6e08"
::msgcat::mcset ja "File type change staged, modification not staged" "\u30d5\u30a1\u30a4\u30eb\u578b\u5909\u66f4\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u6e08\u3001\u5909\u66f4\u30b3\u30df\u30c3\u30c8\u672a\u4e88\u5b9a"
::msgcat::mcset ja "File type change staged, file missing" "\u30d5\u30a1\u30a4\u30eb\u578b\u5909\u66f4\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u6e08\u3001\u30d5\u30a1\u30a4\u30eb\u7121\u3057"
::msgcat::mcset ja "Untracked, not staged" "\u7ba1\u7406\u5916\u3001\u30b3\u30df\u30c3\u30c8\u672a\u4e88\u5b9a"
::msgcat::mcset ja "Missing" "\u30d5\u30a1\u30a4\u30eb\u7121\u3057"
::msgcat::mcset ja "Staged for removal" "\u524a\u9664\u4e88\u5b9a\u6e08"
::msgcat::mcset ja "Staged for removal, still present" "\u524a\u9664\u4e88\u5b9a\u6e08\u3001\u30d5\u30a1\u30a4\u30eb\u672a\u524a\u9664"
::msgcat::mcset ja "Requires merge resolution" "\u8981\u30de\u30fc\u30b8\u89e3\u6c7a"
::msgcat::mcset ja "Starting gitk... please wait..." "gitk \u3092\u8d77\u52d5\u4e2d\u2026\u304a\u5f85\u3061\u4e0b\u3055\u3044\u2026"
::msgcat::mcset ja "Couldn't find gitk in PATH" "PATH \u4e2d\u306b gitk \u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "Couldn't find git gui in PATH" "PATH \u4e2d\u306b git gui \u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "Repository" "\u30ea\u30dd\u30b8\u30c8\u30ea"
::msgcat::mcset ja "Edit" "\u7de8\u96c6"
::msgcat::mcset ja "Branch" "\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Commit@@noun" "\u30b3\u30df\u30c3\u30c8"
::msgcat::mcset ja "Merge" "\u30de\u30fc\u30b8"
::msgcat::mcset ja "Remote" "\u30ea\u30e2\u30fc\u30c8"
::msgcat::mcset ja "Tools" "\u30c4\u30fc\u30eb"
::msgcat::mcset ja "Explore Working Copy" "\u30ef\u30fc\u30ad\u30f3\u30b0\u30b3\u30d4\u30fc\u3092\u30d6\u30e9\u30a6\u30ba"
::msgcat::mcset ja "Browse Current Branch's Files" "\u73fe\u5728\u306e\u30d6\u30e9\u30f3\u30c1\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u898b\u308b"
::msgcat::mcset ja "Browse Branch Files..." "\u30d6\u30e9\u30f3\u30c1\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u898b\u308b\u2026"
::msgcat::mcset ja "Visualize Current Branch's History" "\u73fe\u5728\u306e\u30d6\u30e9\u30f3\u30c1\u306e\u5c65\u6b74\u3092\u898b\u308b"
::msgcat::mcset ja "Visualize All Branch History" "\u5168\u3066\u306e\u30d6\u30e9\u30f3\u30c1\u306e\u5c65\u6b74\u3092\u898b\u308b"
::msgcat::mcset ja "Browse %s's Files" "\u30d6\u30e9\u30f3\u30c1 %s \u306e\u30d5\u30a1\u30a4\u30eb\u3092\u898b\u308b"
::msgcat::mcset ja "Visualize %s's History" "\u30d6\u30e9\u30f3\u30c1 %s \u306e\u5c65\u6b74\u3092\u898b\u308b"
::msgcat::mcset ja "Database Statistics" "\u30c7\u30fc\u30bf\u30d9\u30fc\u30b9\u7d71\u8a08"
::msgcat::mcset ja "Compress Database" "\u30c7\u30fc\u30bf\u30d9\u30fc\u30b9\u5727\u7e2e"
::msgcat::mcset ja "Verify Database" "\u30c7\u30fc\u30bf\u30d9\u30fc\u30b9\u691c\u8a3c"
::msgcat::mcset ja "Create Desktop Icon" "\u30c7\u30b9\u30af\u30c8\u30c3\u30d7\u30fb\u30a2\u30a4\u30b3\u30f3\u3092\u4f5c\u308b"
::msgcat::mcset ja "Quit" "\u7d42\u4e86"
::msgcat::mcset ja "Undo" "\u5143\u306b\u623b\u3059"
::msgcat::mcset ja "Redo" "\u3084\u308a\u76f4\u3057"
::msgcat::mcset ja "Cut" "\u5207\u308a\u53d6\u308a"
::msgcat::mcset ja "Copy" "\u30b3\u30d4\u30fc"
::msgcat::mcset ja "Paste" "\u8cbc\u308a\u4ed8\u3051"
::msgcat::mcset ja "Delete" "\u524a\u9664"
::msgcat::mcset ja "Select All" "\u5168\u3066\u9078\u629e"
::msgcat::mcset ja "Create..." "\u4f5c\u6210\u2026"
::msgcat::mcset ja "Checkout..." "\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8"
::msgcat::mcset ja "Rename..." "\u540d\u524d\u5909\u66f4\u2026"
::msgcat::mcset ja "Delete..." "\u524a\u9664\u2026"
::msgcat::mcset ja "Reset..." "\u30ea\u30bb\u30c3\u30c8\u2026"
::msgcat::mcset ja "Done" "\u5b8c\u4e86"
::msgcat::mcset ja "Commit@@verb" "\u30b3\u30df\u30c3\u30c8"
::msgcat::mcset ja "New Commit" "\u65b0\u898f\u30b3\u30df\u30c3\u30c8"
::msgcat::mcset ja "Amend Last Commit" "\u6700\u65b0\u30b3\u30df\u30c3\u30c8\u3092\u8a02\u6b63"
::msgcat::mcset ja "Rescan" "\u518d\u30b9\u30ad\u30e3\u30f3"
::msgcat::mcset ja "Stage To Commit" "\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u3059\u308b"
::msgcat::mcset ja "Stage Changed Files To Commit" "\u5909\u66f4\u3055\u308c\u305f\u30d5\u30a1\u30a4\u30eb\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a"
::msgcat::mcset ja "Unstage From Commit" "\u30b3\u30df\u30c3\u30c8\u304b\u3089\u964d\u308d\u3059"
::msgcat::mcset ja "Revert Changes" "\u5909\u66f4\u3092\u5143\u306b\u623b\u3059"
::msgcat::mcset ja "Show Less Context" "\u6587\u8108\u3092\u5c11\u306a\u304f"
::msgcat::mcset ja "Show More Context" "\u6587\u8108\u3092\u591a\u304f"
::msgcat::mcset ja "Sign Off" "\u7f72\u540d"
::msgcat::mcset ja "Local Merge..." "\u30ed\u30fc\u30ab\u30eb\u30fb\u30de\u30fc\u30b8\u2026"
::msgcat::mcset ja "Abort Merge..." "\u30de\u30fc\u30b8\u4e2d\u6b62\u2026"
::msgcat::mcset ja "Add..." "\u8ffd\u52a0"
::msgcat::mcset ja "Push..." "\u30d7\u30c3\u30b7\u30e5\u2026"
::msgcat::mcset ja "Delete Branch..." "\u30d6\u30e9\u30f3\u30c1\u524a\u9664..."
::msgcat::mcset ja "Options..." "\u30aa\u30d7\u30b7\u30e7\u30f3\u2026"
::msgcat::mcset ja "Remove..." "\u524a\u9664..."
::msgcat::mcset ja "Help" "\u30d8\u30eb\u30d7"
::msgcat::mcset ja "About %s" "%s \u306b\u3064\u3044\u3066"
::msgcat::mcset ja "Online Documentation" "\u30aa\u30f3\u30e9\u30a4\u30f3\u30fb\u30c9\u30ad\u30e5\u30e1\u30f3\u30c8"
::msgcat::mcset ja "Show SSH Key" "SSH \u30ad\u30fc\u3092\u8868\u793a"
::msgcat::mcset ja "Usage" "\u4f7f\u3044\u65b9"
::msgcat::mcset ja "Error" "\u30a8\u30e9\u30fc"
::msgcat::mcset ja "fatal: cannot stat path %s: No such file or directory" "\u81f4\u547d\u7684: \u30d1\u30b9 %s \u304c stat \u3067\u304d\u307e\u305b\u3093\u3002\u305d\u306e\u3088\u3046\u306a\u30d5\u30a1\u30a4\u30eb\u3084\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u306f\u3042\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "Current Branch:" "\u73fe\u5728\u306e\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Staged Changes (Will Commit)" "\u30b9\u30c6\u30fc\u30b8\u30f3\u30b0\u3055\u308c\u305f\uff08\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u6e08\u306e\uff09\u5909\u66f4"
::msgcat::mcset ja "Unstaged Changes" "\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u5165\u3063\u3066\u3044\u306a\u3044\u5909\u66f4"
::msgcat::mcset ja "Stage Changed" "\u5909\u66f4\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u5165\u308c\u308b"
::msgcat::mcset ja "Push" "\u30d7\u30c3\u30b7\u30e5"
::msgcat::mcset ja "Initial Commit Message:" "\u6700\u521d\u306e\u30b3\u30df\u30c3\u30c8\u30e1\u30c3\u30bb\u30fc\u30b8:"
::msgcat::mcset ja "Amended Commit Message:" "\u8a02\u6b63\u3057\u305f\u30b3\u30df\u30c3\u30c8\u30e1\u30c3\u30bb\u30fc\u30b8:"
::msgcat::mcset ja "Amended Initial Commit Message:" "\u8a02\u6b63\u3057\u305f\u6700\u521d\u306e\u30b3\u30df\u30c3\u30c8\u30e1\u30c3\u30bb\u30fc\u30b8:"
::msgcat::mcset ja "Amended Merge Commit Message:" "\u8a02\u6b63\u3057\u305f\u30de\u30fc\u30b8\u30b3\u30df\u30c3\u30c8\u30e1\u30c3\u30bb\u30fc\u30b8:"
::msgcat::mcset ja "Merge Commit Message:" "\u30de\u30fc\u30b8\u30b3\u30df\u30c3\u30c8\u30e1\u30c3\u30bb\u30fc\u30b8:"
::msgcat::mcset ja "Commit Message:" "\u30b3\u30df\u30c3\u30c8\u30e1\u30c3\u30bb\u30fc\u30b8:"
::msgcat::mcset ja "Copy All" "\u5168\u3066\u30b3\u30d4\u30fc"
::msgcat::mcset ja "File:" "\u30d5\u30a1\u30a4\u30eb:"
::msgcat::mcset ja "Refresh" "\u518d\u8aad\u307f\u8fbc\u307f"
::msgcat::mcset ja "Decrease Font Size" "\u30d5\u30a9\u30f3\u30c8\u3092\u5c0f\u3055\u304f"
::msgcat::mcset ja "Increase Font Size" "\u30d5\u30a9\u30f3\u30c8\u3092\u5927\u304d\u304f"
::msgcat::mcset ja "Encoding" "\u30a8\u30f3\u30b3\u30fc\u30c7\u30a3\u30f3\u30b0"
::msgcat::mcset ja "Apply/Reverse Hunk" "\u30d1\u30c3\u30c1\u3092\u9069\u7528/\u53d6\u308a\u6d88\u3059"
::msgcat::mcset ja "Apply/Reverse Line" "\u30d1\u30c3\u30c1\u884c\u3092\u9069\u7528/\u53d6\u308a\u6d88\u3059"
::msgcat::mcset ja "Run Merge Tool" "\u30de\u30fc\u30b8\u30c4\u30fc\u30eb\u3092\u8d77\u52d5"
::msgcat::mcset ja "Use Remote Version" "\u30ea\u30e2\u30fc\u30c8\u306e\u65b9\u3092\u63a1\u7528"
::msgcat::mcset ja "Use Local Version" "\u30ed\u30fc\u30ab\u30eb\u306e\u65b9\u3092\u63a1\u7528"
::msgcat::mcset ja "Revert To Base" "\u30d9\u30fc\u30b9\u7248\u3092\u63a1\u7528"
::msgcat::mcset ja "Visualize These Changes In The Submodule" "\u30b5\u30d6\u30e2\u30b8\u30e5\u30fc\u30eb\u5185\u306e\u3053\u308c\u3089\u306e\u5909\u66f4\u3092\u898b\u308b"
::msgcat::mcset ja "Visualize Current Branch History In The Submodule" "\u30b5\u30d6\u30e2\u30b8\u30e5\u30fc\u30eb\u5185\u3067\u73fe\u5728\u306e\u30d6\u30e9\u30f3\u30c1\u306e\u5c65\u6b74\u3092\u898b\u308b"
::msgcat::mcset ja "Visualize All Branch History In The Submodule" "\u30b5\u30d6\u30e2\u30b8\u30e5\u30fc\u30eb\u5185\u3067\u5168\u3066\u306e\u30d6\u30e9\u30f3\u30c1\u306e\u5c65\u6b74\u3092\u898b\u308b"
::msgcat::mcset ja "Start git gui In The Submodule" "\u30b5\u30d6\u30e2\u30b8\u30e5\u30fc\u30eb\u5185\u3067git gui\u3092\u8d77\u52d5\u3059\u308b"
::msgcat::mcset ja "Unstage Hunk From Commit" "\u30d1\u30c3\u30c1\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u304b\u3089\u5916\u3059"
::msgcat::mcset ja "Unstage Lines From Commit" "\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u304b\u3089\u884c\u3092\u5916\u3059"
::msgcat::mcset ja "Unstage Line From Commit" "\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u304b\u3089\u884c\u3092\u5916\u3059"
::msgcat::mcset ja "Stage Hunk For Commit" "\u30d1\u30c3\u30c1\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u52a0\u3048\u308b"
::msgcat::mcset ja "Stage Lines For Commit" "\u30d1\u30c3\u30c1\u884c\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u52a0\u3048\u308b"
::msgcat::mcset ja "Stage Line For Commit" "\u30d1\u30c3\u30c1\u884c\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u52a0\u3048\u308b"
::msgcat::mcset ja "Initializing..." "\u521d\u671f\u5316\u3057\u3066\u3044\u307e\u3059\u2026"
::msgcat::mcset ja "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "\u74b0\u5883\u306b\u554f\u984c\u304c\u3042\u308b\u53ef\u80fd\u6027\u304c\u3042\u308a\u307e\u3059\n\n\u4ee5\u4e0b\u306e\u74b0\u5883\u5909\u6570\u306f %s \u304c\u8d77\u52d5\u3059\u308b Git \u30b5\u30d6\u30d7\u30ed\u30bb\u30b9\u306b\u3088\u3063\u3066\u7121\u8996\u3055\u308c\u308b\u3067\u3057\u3087\u3046:\n\n"
::msgcat::mcset ja "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\n\u3053\u308c\u306f Cygwin \u3067\u914d\u5e03\u3055\u308c\u3066\u3044\u308b Tcl \u30d0\u30a4\u30ca\u30ea\u306b\n\u95a2\u3057\u3066\u306e\u65e2\u77e5\u306e\u554f\u984c\u306b\u3088\u308a\u307e\u3059"
::msgcat::mcset ja "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\n\u500b\u4eba\u7684\u306a ~/.gitconfig \u30d5\u30a1\u30a4\u30eb\u5185\u3067 user.name \u3068 user.email \u306e\u5024\u3092\u8a2d\u5b9a\n\u3059\u308b\u306e\u304c\u3001%s \u306e\u826f\u3044\u4ee3\u7528\u3068\u306a\u308a\u307e\u3059\n"
::msgcat::mcset ja "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "\u8a02\u6b63\u4e2d\u306b\u306f\u30de\u30fc\u30b8\u3067\u304d\u307e\u305b\u3093\u3002\n\n\u8a02\u6b63\u51e6\u7406\u3092\u5b8c\u4e86\u3059\u308b\u307e\u3067\u306f\u65b0\u305f\u306b\u30de\u30fc\u30b8\u3092\u958b\u59cb\u3067\u304d\u307e\u305b\u3093\u3002\n"
::msgcat::mcset ja "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "\u6700\u5f8c\u306b\u30b9\u30ad\u30e3\u30f3\u3057\u305f\u72b6\u614b\u306f\u30ea\u30dd\u30b8\u30c8\u30ea\u306e\u72b6\u614b\u3068\u5408\u81f4\u3057\u307e\u305b\u3093\u3002\n\n\u6700\u5f8c\u306b\u30b9\u30ad\u30e3\u30f3\u3057\u3066\u4ee5\u5f8c\u3001\u5225\u306e Git \u30d7\u30ed\u30b0\u30e9\u30e0\u304c\u30ea\u30dd\u30b8\u30c8\u30ea\u3092\u5909\u66f4\u3057\u3066\u3044\u307e\u3059\u3002\u30de\u30fc\u30b8\u3092\u958b\u59cb\u3059\u308b\u524d\u306b\u3001\u518d\u30b9\u30ad\u30e3\u30f3\u304c\u5fc5\u8981\u3067\u3059\u3002\n\n\u81ea\u52d5\u7684\u306b\u518d\u30b9\u30ad\u30e3\u30f3\u3092\u958b\u59cb\u3057\u307e\u3059\u3002\n"
::msgcat::mcset ja "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "\u885d\u7a81\u306e\u3042\u3063\u305f\u30de\u30fc\u30b8\u306e\u9014\u4e2d\u3067\u3059\u3002\n\n\u30d5\u30a1\u30a4\u30eb %s \u306b\u306f\u30de\u30fc\u30b8\u4e2d\u306e\u885d\u7a81\u304c\u6b8b\u3063\u3066\u3044\u307e\u3059\u3002\n\n\u3053\u306e\u30d5\u30a1\u30a4\u30eb\u306e\u885d\u7a81\u3092\u89e3\u6c7a\u3057\u3001\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u52a0\u3048\u3066\u3001\u30b3\u30df\u30c3\u30c8\u3059\u308b\u3053\u3068\u3067\u30de\u30fc\u30b8\u3092\u5b8c\u4e86\u3057\u307e\u3059\u3002\u305d\u3046\u3084\u3063\u3066\u59cb\u3081\u3066\u3001\u65b0\u305f\u306a\u30de\u30fc\u30b8\u3092\u958b\u59cb\u3067\u304d\u308b\u3088\u3046\u306b\u306a\u308a\u307e\u3059\u3002\n"
::msgcat::mcset ja "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "\u5909\u66f4\u306e\u9014\u4e2d\u3067\u3059\u3002\n\n\u30d5\u30a1\u30a4\u30eb %s \u306f\u5909\u66f4\u4e2d\u3067\u3059\u3002\n\n\u73fe\u5728\u306e\u30b3\u30df\u30c3\u30c8\u3092\u5b8c\u4e86\u3057\u3066\u304b\u3089\u30de\u30fc\u30b8\u3092\u958b\u59cb\u3057\u3066\u4e0b\u3055\u3044\u3002\u305d\u3046\u3059\u308b\u65b9\u304c\u30de\u30fc\u30b8\u306b\u5931\u6557\u3057\u305f\u3068\u304d\u306e\u56de\u5fa9\u304c\u697d\u3067\u3059\u3002\n"
::msgcat::mcset ja "%s of %s" "%2\$s \u306e %1\$s \u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Merging %s and %s..." "%s \u3068 %s \u3092\u30de\u30fc\u30b8\u4e2d\u30fb\u30fb\u30fb"
::msgcat::mcset ja "Merge completed successfully." "\u30de\u30fc\u30b8\u304c\u5b8c\u4e86\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Merge failed.  Conflict resolution is required." "\u30de\u30fc\u30b8\u304c\u5931\u6557\u3057\u307e\u3057\u305f\u3002\u885d\u7a81\u306e\u89e3\u6c7a\u304c\u5fc5\u8981\u3067\u3059\u3002"
::msgcat::mcset ja "Merge Into %s" "%s \u306b\u30de\u30fc\u30b8"
::msgcat::mcset ja "Visualize" "\u53ef\u8996\u5316"
::msgcat::mcset ja "Cancel" "\u4e2d\u6b62"
::msgcat::mcset ja "Revision To Merge" "\u30de\u30fc\u30b8\u3059\u308b\u30ea\u30d3\u30b8\u30e7\u30f3"
::msgcat::mcset ja "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "\u8a02\u6b63\u4e2d\u306b\u306f\u4e2d\u6b62\u3067\u304d\u307e\u305b\u3093\u3002\n\n\u307e\u305a\u4eca\u306e\u30b3\u30df\u30c3\u30c8\u8a02\u6b63\u3092\u5b8c\u4e86\u3055\u305b\u3066\u4e0b\u3055\u3044\u3002\n"
::msgcat::mcset ja "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "\u30de\u30fc\u30b8\u3092\u4e2d\u65ad\u3057\u307e\u3059\u304b\uff1f\n\n\u73fe\u5728\u306e\u30de\u30fc\u30b8\u3092\u4e2d\u65ad\u3059\u308b\u3068\u3001\u30b3\u30df\u30c3\u30c8\u3057\u3066\u3044\u306a\u3044\u5168\u3066\u306e\u5909\u66f4\u304c\u5931\u308f\u308c\u307e\u3059\u3002\n\n\u30de\u30fc\u30b8\u3092\u4e2d\u65ad\u3057\u3066\u3088\u308d\u3057\u3044\u3067\u3059\u304b\uff1f"
::msgcat::mcset ja "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "\u5909\u66f4\u70b9\u3092\u30ea\u30bb\u30c3\u30c8\u3057\u307e\u3059\u304b\uff1f\n\n\u5909\u66f4\u70b9\u3092\u30ea\u30bb\u30c3\u30c8\u3059\u308b\u3068\u3001\u30b3\u30df\u30c3\u30c8\u3057\u3066\u3044\u306a\u3044\u5168\u3066\u306e\u5909\u66f4\u304c\u5931\u308f\u308c\u307e\u3059\u3002\n\n\u30ea\u30bb\u30c3\u30c8\u3057\u3066\u3088\u308d\u3057\u3044\u3067\u3059\u304b\uff1f"
::msgcat::mcset ja "Aborting" "\u4e2d\u65ad\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "files reset" "\u30ea\u30bb\u30c3\u30c8\u3057\u305f\u30d5\u30a1\u30a4\u30eb"
::msgcat::mcset ja "Abort failed." "\u4e2d\u65ad\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002"
::msgcat::mcset ja "Abort completed.  Ready." "\u4e2d\u65ad\u5b8c\u4e86\u3002"
::msgcat::mcset ja "error" "\u30a8\u30e9\u30fc"
::msgcat::mcset ja "warning" "\u8b66\u544a"
::msgcat::mcset ja "You must correct the above errors before committing." "\u30b3\u30df\u30c3\u30c8\u3059\u308b\u524d\u306b\u3001\u4ee5\u4e0a\u306e\u30a8\u30e9\u30fc\u3092\u4fee\u6b63\u3057\u3066\u4e0b\u3055\u3044"
::msgcat::mcset ja "Invalid date from Git: %s" "Git \u304b\u3089\u51fa\u305f\u7121\u52b9\u306a\u65e5\u4ed8: %s"
::msgcat::mcset ja "Default" "\u30c7\u30d5\u30a9\u30fc\u30eb\u30c8"
::msgcat::mcset ja "System (%s)" "\u30b7\u30b9\u30c6\u30e0 (%s)"
::msgcat::mcset ja "Other" "\u305d\u306e\u4ed6"
::msgcat::mcset ja "Delete Branch Remotely" "\u30ea\u30e2\u30fc\u30c8\u30d6\u30e9\u30f3\u30c1\u524a\u9664"
::msgcat::mcset ja "From Repository" "\u5143\u306e\u30ea\u30dd\u30b8\u30c8\u30ea"
::msgcat::mcset ja "Remote:" "\u30ea\u30e2\u30fc\u30c8:"
::msgcat::mcset ja "Arbitrary Location:" "\u4efb\u610f\u306e\u4f4d\u7f6e:"
::msgcat::mcset ja "Branches" "\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Delete Only If" "\u6761\u4ef6\u4ed8\u3067\u524a\u9664"
::msgcat::mcset ja "Merged Into:" "\u30de\u30fc\u30b8\u5148:"
::msgcat::mcset ja "Always (Do not perform merge checks)" "\u7121\u6761\u4ef6\uff08\u30de\u30fc\u30b8\u691c\u67fb\u3092\u3057\u306a\u3044\uff09"
::msgcat::mcset ja "A branch is required for 'Merged Into'." "'\u30de\u30fc\u30b8\u5148' \u306b\u306f\u30d6\u30e9\u30f3\u30c1\u304c\u5fc5\u8981\u3067\u3059\u3002"
::msgcat::mcset ja "The following branches are not completely merged into %s:\n\n - %s" "\u4ee5\u4e0b\u306e\u30d6\u30e9\u30f3\u30c1\u306f %s \u306b\u5b8c\u5168\u306b\u30de\u30fc\u30b8\u3055\u308c\u3066\u3044\u307e\u305b\u3093:\n\n - %s"
::msgcat::mcset ja "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "\u5fc5\u8981\u306a\u30b3\u30df\u30c3\u30c8\u304c\u4e0d\u8db3\u3057\u3066\u3044\u308b\u305f\u3081\u306b\u3001\u30de\u30fc\u30b8\u691c\u67fb\u304c\u5931\u6557\u3057\u307e\u3057\u305f\u3002\u307e\u305a %s \u304b\u3089\u30d5\u30a7\u30c3\u30c1\u3057\u3066\u4e0b\u3055\u3044\u3002"
::msgcat::mcset ja "Please select one or more branches to delete." "\u524a\u9664\u3059\u308b\u30d6\u30e9\u30f3\u30c1\u3092\u9078\u629e\u3057\u3066\u4e0b\u3055\u3044\u3002"
::msgcat::mcset ja "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "\u524a\u9664\u3057\u305f\u30d6\u30e9\u30f3\u30c1\u3092\u56de\u5fa9\u3059\u308b\u306e\u306f\u56f0\u96e3\u3067\u3059\u3002\n\n\u9078\u629e\u3057\u305f\u30d6\u30e9\u30f3\u30c1\u3092\u524a\u9664\u3057\u3066\u826f\u3044\u3067\u3059\u304b\uff1f"
::msgcat::mcset ja "Deleting branches from %s" "%s \u304b\u3089\u30d6\u30e9\u30f3\u30c1\u3092\u524a\u9664\u3057\u3066\u3044\u307e\u3059\u3002"
::msgcat::mcset ja "No repository selected." "\u30ea\u30dd\u30b8\u30c8\u30ea\u304c\u9078\u629e\u3055\u308c\u3066\u3044\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Scanning %s..." "%s \u3092\u30b9\u30ad\u30e3\u30f3\u3057\u3066\u3044\u307e\u3059\u2026"
::msgcat::mcset ja "Delete Branch" "\u30d6\u30e9\u30f3\u30c1\u524a\u9664"
::msgcat::mcset ja "Delete Local Branch" "\u30ed\u30fc\u30ab\u30eb\u30fb\u30d6\u30e9\u30f3\u30c1\u3092\u524a\u9664"
::msgcat::mcset ja "Local Branches" "\u30ed\u30fc\u30ab\u30eb\u30fb\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Delete Only If Merged Into" "\u30de\u30fc\u30b8\u6e08\u307f\u306e\u6642\u306e\u307f\u524a\u9664"
::msgcat::mcset ja "The following branches are not completely merged into %s:" "\u4ee5\u4e0b\u306e\u30d6\u30e9\u30f3\u30c1\u306f %s \u306b\u5b8c\u5168\u306b\u30de\u30fc\u30b8\u3055\u308c\u3066\u3044\u307e\u305b\u3093:"
::msgcat::mcset ja "Failed to delete branches:\n%s" "\u4ee5\u4e0b\u306e\u30d6\u30e9\u30f3\u30c1\u3092\u524a\u9664\u3067\u304d\u307e\u305b\u3093:\n%s"
::msgcat::mcset ja "This Detached Checkout" "\u5206\u96e2\u3055\u308c\u305f\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8"
::msgcat::mcset ja "Revision Expression:" "\u30ea\u30d3\u30b8\u30e7\u30f3\u5f0f:"
::msgcat::mcset ja "Local Branch" "\u30ed\u30fc\u30ab\u30eb\u30fb\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Tracking Branch" "\u30c8\u30e9\u30c3\u30ad\u30f3\u30b0\u30fb\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Tag" "\u30bf\u30b0"
::msgcat::mcset ja "Invalid revision: %s" "\u7121\u52b9\u306a\u30ea\u30d3\u30b8\u30e7\u30f3: %s"
::msgcat::mcset ja "No revision selected." "\u30ea\u30d3\u30b8\u30e7\u30f3\u304c\u672a\u9078\u629e\u3067\u3059\u3002"
::msgcat::mcset ja "Revision expression is empty." "\u30ea\u30d3\u30b8\u30e7\u30f3\u5f0f\u304c\u7a7a\u3067\u3059\u3002"
::msgcat::mcset ja "Updated" "\u66f4\u65b0\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "URL" "URL"
::msgcat::mcset ja "Working... please wait..." "\u5b9f\u884c\u4e2d\u2026\u304a\u5f85\u3061\u4e0b\u3055\u3044\u2026"
::msgcat::mcset ja "Close" "\u9589\u3058\u308b"
::msgcat::mcset ja "Success" "\u6210\u529f"
::msgcat::mcset ja "Error: Command Failed" "\u30a8\u30e9\u30fc: \u30b3\u30de\u30f3\u30c9\u304c\u5931\u6557\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Fetching %s from %s" "%2\$s \u304b\u3089 %1\$s \u3092\u30d5\u30a7\u30c3\u30c1\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "fatal: Cannot resolve %s" "\u81f4\u547d\u7684\u30a8\u30e9\u30fc: %s \u3092\u89e3\u6c7a\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "Branch '%s' does not exist." "\u30d6\u30e9\u30f3\u30c1'%s'\u306f\u5b58\u5728\u3057\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Failed to configure simplified git-pull for '%s'." "'%s' \u306b\u7c21\u6613 git-pull \u3092\u8a2d\u5b9a\u3067\u304d\u307e\u305b\u3093\u3067\u3057\u305f"
::msgcat::mcset ja "Branch '%s' already exists." "'%s'\u3068\u3044\u3046\u30d6\u30e9\u30f3\u30c1\u306f\u65e2\u306b\u5b58\u5728\u3057\u307e\u3059\u3002"
::msgcat::mcset ja "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "\u30d6\u30e9\u30f3\u30c1 '%s' \u306f\u65e2\u306b\u5b58\u5728\u3057\u307e\u3059\u3002\n\n%s \u306b\u65e9\u9001\u308a\u3067\u304d\u307e\u305b\u3093\u3002\n\u30de\u30fc\u30b8\u304c\u5fc5\u8981\u3067\u3059\u3002"
::msgcat::mcset ja "Merge strategy '%s' not supported." "'%s' \u30de\u30fc\u30b8\u6226\u7565\u306f\u30b5\u30dd\u30fc\u30c8\u3055\u308c\u3066\u3044\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Failed to update '%s'." "'%s' \u306e\u66f4\u65b0\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002"
::msgcat::mcset ja "Staging area (index) is already locked." "\u30a4\u30f3\u30c7\u30c3\u30af\u30b9\u306f\u65e2\u306b\u30ed\u30c3\u30af\u3055\u308c\u3066\u3044\u307e\u3059\u3002"
::msgcat::mcset ja "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "\u6700\u5f8c\u306b\u30b9\u30ad\u30e3\u30f3\u3057\u305f\u72b6\u614b\u306f\u30ea\u30dd\u30b8\u30c8\u30ea\u306e\u72b6\u614b\u3068\u5408\u81f4\u3057\u307e\u305b\u3093\u3002\n\n\u6700\u5f8c\u306b\u30b9\u30ad\u30e3\u30f3\u3057\u3066\u4ee5\u5f8c\u3001\u5225\u306e Git \u30d7\u30ed\u30b0\u30e9\u30e0\u304c\u30ea\u30dd\u30b8\u30c8\u30ea\u3092\u5909\u66f4\u3057\u3066\u3044\u307e\u3059\u3002\u73fe\u5728\u306e\u30d6\u30e9\u30f3\u30c1\u3092\u5909\u66f4\u3059\u308b\u524d\u306b\u3001\u518d\u30b9\u30ad\u30e3\u30f3\u304c\u5fc5\u8981\u3067\u3059\u3002\n\n\u81ea\u52d5\u7684\u306b\u518d\u30b9\u30ad\u30e3\u30f3\u3092\u958b\u59cb\u3057\u307e\u3059\u3002\n"
::msgcat::mcset ja "Updating working directory to '%s'..." "\u4f5c\u696d\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u3092 '%s' \u306b\u66f4\u65b0\u3057\u3066\u3044\u307e\u3059\u2026"
::msgcat::mcset ja "files checked out" "\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8\u3055\u308c\u305f\u30d5\u30a1\u30a4\u30eb"
::msgcat::mcset ja "Aborted checkout of '%s' (file level merging is required)." "'%s' \u306e\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8\u3092\u4e2d\u6b62\u3057\u307e\u3057\u305f\uff08\u30d5\u30a1\u30a4\u30eb\u6bce\u306e\u30de\u30fc\u30b8\u304c\u5fc5\u8981\u3067\u3059\uff09\u3002"
::msgcat::mcset ja "File level merge required." "\u30d5\u30a1\u30a4\u30eb\u6bce\u306e\u30de\u30fc\u30b8\u304c\u5fc5\u8981\u3067\u3059\u3002"
::msgcat::mcset ja "Staying on branch '%s'." "\u30d6\u30e9\u30f3\u30c1 '%s' \u306b\u6ede\u307e\u308a\u307e\u3059\u3002"
::msgcat::mcset ja "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "\u30ed\u30fc\u30ab\u30eb\u30fb\u30d6\u30e9\u30f3\u30c1\u304b\u3089\u96e2\u308c\u307e\u3059\u3002\n\n\u30d6\u30e9\u30f3\u30c1\u4e0a\u306b\u6ede\u307e\u308a\u305f\u3044\u3068\u304d\u306f\u3001\u3053\u306e\u300c\u5206\u96e2\u3055\u308c\u305f\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8\u300d\u304b\u3089\u65b0\u898f\u30d6\u30e9\u30f3\u30c1\u3092\u958b\u59cb\u3057\u3066\u304f\u3060\u3055\u3044\u3002"
::msgcat::mcset ja "Checked out '%s'." "'%s' \u3092\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Resetting '%s' to '%s' will lose the following commits:" "'%s' \u3092 '%s' \u306b\u30ea\u30bb\u30c3\u30c8\u3059\u308b\u3068\u3001\u4ee5\u4e0b\u306e\u30b3\u30df\u30c3\u30c8\u304c\u5931\u306a\u308f\u308c\u307e\u3059:"
::msgcat::mcset ja "Recovering lost commits may not be easy." "\u5931\u306a\u308f\u308c\u305f\u30b3\u30df\u30c3\u30c8\u3092\u56de\u5fa9\u3059\u308b\u306e\u306f\u7c21\u5358\u3067\u306f\u3042\u308a\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Reset '%s'?" "'%s' \u3092\u30ea\u30bb\u30c3\u30c8\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Reset" "\u30ea\u30bb\u30c3\u30c8"
::msgcat::mcset ja "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "\u73fe\u5728\u306e\u30d6\u30e9\u30f3\u30c1\u3092\u8a2d\u5b9a\u3067\u304d\u307e\u305b\u3093\u3002\n\n\u4f5c\u696d\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u306f\u90e8\u5206\u7684\u306b\u3057\u304b\u5207\u308a\u66ff\u308f\u3063\u3066\u3044\u307e\u305b\u3093\u3002\u30d5\u30a1\u30a4\u30eb\u306e\u66f4\u65b0\u306b\u306f\u6210\u529f\u3057\u307e\u3057\u305f\u304c\u3001 Git \u306e\u5185\u90e8\u30c7\u30fc\u30bf\u3092\u66f4\u65b0\u3067\u304d\u307e\u305b\u3093\u3067\u3057\u305f\u3002\n\u8d77\u3053\u308b\u306f\u305a\u306e\u306a\u3044\u30a8\u30e9\u30fc\u3067\u3059\u3002\u3042\u304d\u3089\u3081\u3066 %s \u3092\u7d42\u4e86\u3057\u307e\u3059\u3002"
::msgcat::mcset ja "File Viewer" "\u30d5\u30a1\u30a4\u30eb\u30d4\u30e5\u30fc\u30ef"
::msgcat::mcset ja "Commit:" "\u30b3\u30df\u30c3\u30c8:"
::msgcat::mcset ja "Copy Commit" "\u30b3\u30df\u30c3\u30c8\u3092\u30b3\u30d4\u30fc"
::msgcat::mcset ja "Find Text..." "\u30c6\u30ad\u30b9\u30c8\u3092\u691c\u7d22"
::msgcat::mcset ja "Goto Line..." "\u6307\u5b9a\u884c\u306b\u79fb\u52d5\u2026"
::msgcat::mcset ja "Do Full Copy Detection" "\u30b3\u30d4\u30fc\u691c\u77e5"
::msgcat::mcset ja "Show History Context" "\u6587\u8108\u3092\u898b\u305b\u308b"
::msgcat::mcset ja "Blame Parent Commit" "\u89aa\u30b3\u30df\u30c3\u30c8\u3092\u6ce8\u91c8"
::msgcat::mcset ja "Reading %s..." "%s \u3092\u8aad\u3093\u3067\u3044\u307e\u3059\u2026"
::msgcat::mcset ja "Loading copy/move tracking annotations..." "\u30b3\u30d4\u30fc\u30fb\u79fb\u52d5\u8ffd\u8de1\u30c7\u30fc\u30bf\u3092\u8aad\u3093\u3067\u3044\u307e\u3059\u2026"
::msgcat::mcset ja "lines annotated" "\u884c\u3092\u6ce8\u91c8\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Loading original location annotations..." "\u5143\u4f4d\u7f6e\u884c\u306e\u6ce8\u91c8\u30c7\u30fc\u30bf\u3092\u8aad\u3093\u3067\u3044\u307e\u3059\u2026"
::msgcat::mcset ja "Annotation complete." "\u6ce8\u91c8\u5b8c\u4e86\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Busy" "\u5b9f\u884c\u4e2d"
::msgcat::mcset ja "Annotation process is already running." "\u3059\u3067\u306b blame \u30d7\u30ed\u30bb\u30b9\u3092\u5b9f\u884c\u4e2d\u3067\u3059\u3002"
::msgcat::mcset ja "Running thorough copy detection..." "\u30b3\u30d4\u30fc\u691c\u77e5\u3092\u5b9f\u884c\u4e2d\u2026"
::msgcat::mcset ja "Loading annotation..." "\u6ce8\u91c8\u3092\u8aad\u307f\u8fbc\u3093\u3067\u3044\u307e\u3059\u2026"
::msgcat::mcset ja "Author:" "\u4f5c\u8005:"
::msgcat::mcset ja "Committer:" "\u30b3\u30df\u30c3\u30c8\u8005:"
::msgcat::mcset ja "Original File:" "\u5143\u30d5\u30a1\u30a4\u30eb"
::msgcat::mcset ja "Cannot find HEAD commit:" "HEAD \u30b3\u30df\u30c3\u30c8\u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "Cannot find parent commit:" "\u89aa\u30b3\u30df\u30c3\u30c8\u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093:"
::msgcat::mcset ja "Unable to display parent" "\u89aa\u3092\u8868\u793a\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "Error loading diff:" "diff \u3092\u8aad\u3080\u969b\u306e\u30a8\u30e9\u30fc\u3067\u3059:"
::msgcat::mcset ja "Originally By:" "\u539f\u4f5c\u8005:"
::msgcat::mcset ja "In File:" "\u30d5\u30a1\u30a4\u30eb:"
::msgcat::mcset ja "Copied Or Moved Here By:" "\u8907\u5199\u30fb\u79fb\u52d5\u8005:"
::msgcat::mcset ja "git-gui - a graphical user interface for Git." "Git \u306e\u30b0\u30e9\u30d5\u30a3\u30ab\u30ebUI git-gui"
::msgcat::mcset ja "Git Gui" "Git GUI"
::msgcat::mcset ja "Create New Repository" "\u65b0\u3057\u3044\u30ea\u30dd\u30b8\u30c8\u30ea\u3092\u4f5c\u308b"
::msgcat::mcset ja "New..." "\u65b0\u898f\u2026"
::msgcat::mcset ja "Clone Existing Repository" "\u65e2\u5b58\u30ea\u30dd\u30b8\u30c8\u30ea\u3092\u8907\u88fd\u3059\u308b"
::msgcat::mcset ja "Clone..." "\u8907\u88fd\u2026"
::msgcat::mcset ja "Open Existing Repository" "\u65e2\u5b58\u30ea\u30dd\u30b8\u30c8\u30ea\u3092\u958b\u304f"
::msgcat::mcset ja "Open..." "\u958b\u304f\u2026"
::msgcat::mcset ja "Recent Repositories" "\u6700\u8fd1\u4f7f\u3063\u305f\u30ea\u30dd\u30b8\u30c8\u30ea"
::msgcat::mcset ja "Open Recent Repository:" "\u6700\u8fd1\u4f7f\u3063\u305f\u30ea\u30dd\u30b8\u30c8\u30ea\u3092\u958b\u304f"
::msgcat::mcset ja "Failed to create repository %s:" "\u30ea\u30dd\u30b8\u30c8\u30ea %s \u3092\u4f5c\u88fd\u3067\u304d\u307e\u305b\u3093:"
::msgcat::mcset ja "Create" "\u4f5c\u6210"
::msgcat::mcset ja "Directory:" "\u30c7\u30a3\u30ec\u30af\u30c8\u30ea:"
::msgcat::mcset ja "Browse" "\u30d6\u30e9\u30a6\u30ba"
::msgcat::mcset ja "Git Repository" "GIT \u30ea\u30dd\u30b8\u30c8\u30ea"
::msgcat::mcset ja "Directory %s already exists." "\u30c7\u30a3\u30ec\u30af\u30c8\u30ea '%s' \u306f\u65e2\u306b\u5b58\u5728\u3057\u307e\u3059\u3002"
::msgcat::mcset ja "File %s already exists." "\u30d5\u30a1\u30a4\u30eb '%s' \u306f\u65e2\u306b\u5b58\u5728\u3057\u307e\u3059\u3002"
::msgcat::mcset ja "Clone" "\u8907\u88fd"
::msgcat::mcset ja "Source Location:" "\u30bd\u30fc\u30b9\u306e\u4f4d\u7f6e"
::msgcat::mcset ja "Target Directory:" "\u5148\u30c7\u30a3\u30ec\u30af\u30c8\u30ea:"
::msgcat::mcset ja "Clone Type:" "\u8907\u88fd\u65b9\u5f0f:"
::msgcat::mcset ja "Standard (Fast, Semi-Redundant, Hardlinks)" "\u6a19\u6e96(\u9ad8\u901f\u30fb\u4e2d\u5197\u9577\u5ea6\u30fb\u30cf\u30fc\u30c9\u30ea\u30f3\u30af)"
::msgcat::mcset ja "Full Copy (Slower, Redundant Backup)" "\u5168\u8907\u5199(\u4f4e\u901f\u30fb\u5197\u9577\u30d0\u30c3\u30af\u30a2\u30c3\u30d7)"
::msgcat::mcset ja "Shared (Fastest, Not Recommended, No Backup)" "\u5171\u6709(\u6700\u9ad8\u901f\u30fb\u975e\u63a8\u5968\u30fb\u30d0\u30c3\u30af\u30a2\u30c3\u30d7\u7121\u3057)"
::msgcat::mcset ja "Recursively clone submodules too" "\u30b5\u30d6\u30e2\u30b8\u30e5\u30fc\u30eb\u3082\u518d\u5e30\u7684\u306b\u8907\u88fd\u3059\u308b"
::msgcat::mcset ja "Not a Git repository: %s" "Git \u30ea\u30dd\u30b8\u30c8\u30ea\u3067\u306f\u3042\u308a\u307e\u305b\u3093: %s"
::msgcat::mcset ja "Standard only available for local repository." "\u6a19\u6e96\u65b9\u5f0f\u306f\u540c\u4e00\u8a08\u7b97\u6a5f\u4e0a\u306e\u30ea\u30dd\u30b8\u30c8\u30ea\u306b\u306e\u307f\u4f7f\u3048\u307e\u3059\u3002"
::msgcat::mcset ja "Shared only available for local repository." "\u5171\u6709\u65b9\u5f0f\u306f\u540c\u4e00\u8a08\u7b97\u6a5f\u4e0a\u306e\u30ea\u30dd\u30b8\u30c8\u30ea\u306b\u306e\u307f\u4f7f\u3048\u307e\u3059\u3002"
::msgcat::mcset ja "Location %s already exists." "'%s' \u306f\u65e2\u306b\u5b58\u5728\u3057\u307e\u3059\u3002"
::msgcat::mcset ja "Failed to configure origin" "origin \u3092\u8a2d\u5b9a\u3067\u304d\u307e\u305b\u3093\u3067\u3057\u305f"
::msgcat::mcset ja "Counting objects" "\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u3092\u6570\u3048\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "buckets" "\u30d0\u30b1\u30c4"
::msgcat::mcset ja "Unable to copy objects/info/alternates: %s" "objects/info/alternates \u3092\u8907\u5199\u3067\u304d\u307e\u305b\u3093: %s"
::msgcat::mcset ja "Nothing to clone from %s." "%s \u304b\u3089\u8907\u88fd\u3059\u308b\u5185\u5bb9\u306f\u3042\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "The 'master' branch has not been initialized." "'master' \u30d6\u30e9\u30f3\u30c1\u304c\u521d\u671f\u5316\u3055\u308c\u3066\u3044\u307e\u305b\u3093"
::msgcat::mcset ja "Hardlinks are unavailable.  Falling back to copying." "\u30cf\u30fc\u30c9\u30ea\u30f3\u30af\u304c\u4f5c\u308c\u306a\u3044\u306e\u3067\u3001\u30b3\u30d4\u30fc\u3057\u307e\u3059"
::msgcat::mcset ja "Cloning from %s" "%s \u304b\u3089\u8907\u88fd\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "Copying objects" "\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u3092\u8907\u5199\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "KiB" "KiB"
::msgcat::mcset ja "Unable to copy object: %s" "\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u3092\u8907\u5199\u3067\u304d\u307e\u305b\u3093: %s"
::msgcat::mcset ja "Linking objects" "\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u3092\u9023\u7d50\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "objects" "\u30aa\u30d6\u30b8\u30a7\u30af\u30c8"
::msgcat::mcset ja "Unable to hardlink object: %s" "\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u3092\u30cf\u30fc\u30c9\u30ea\u30f3\u30af\u3067\u304d\u307e\u305b\u3093: %s"
::msgcat::mcset ja "Cannot fetch branches and objects.  See console output for details." "\u30d6\u30e9\u30f3\u30c1\u3084\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u3092\u53d6\u5f97\u3067\u304d\u307e\u305b\u3093\u3002\u30b3\u30f3\u30bd\u30fc\u30eb\u51fa\u529b\u3092\u898b\u3066\u4e0b\u3055\u3044"
::msgcat::mcset ja "Cannot fetch tags.  See console output for details." "\u30bf\u30b0\u3092\u53d6\u5f97\u3067\u304d\u307e\u305b\u3093\u3002\u30b3\u30f3\u30bd\u30fc\u30eb\u51fa\u529b\u3092\u898b\u3066\u4e0b\u3055\u3044"
::msgcat::mcset ja "Cannot determine HEAD.  See console output for details." "HEAD \u3092\u78ba\u5b9a\u3067\u304d\u307e\u305b\u3093\u3002\u30b3\u30f3\u30bd\u30fc\u30eb\u51fa\u529b\u3092\u898b\u3066\u4e0b\u3055\u3044"
::msgcat::mcset ja "Unable to cleanup %s" "%s \u3092\u6383\u9664\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "Clone failed." "\u8907\u5199\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002"
::msgcat::mcset ja "No default branch obtained." "\u30c7\u30d5\u30a9\u30fc\u30eb\u30c8\u30fb\u30d6\u30e9\u30f3\u30c1\u304c\u53d6\u5f97\u3055\u308c\u307e\u305b\u3093\u3067\u3057\u305f"
::msgcat::mcset ja "Cannot resolve %s as a commit." "%s \u3092\u30b3\u30df\u30c3\u30c8\u3068\u3057\u3066\u89e3\u91c8\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "Creating working directory" "\u4f5c\u696d\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u3092\u4f5c\u6210\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "files" "\u30d5\u30a1\u30a4\u30eb"
::msgcat::mcset ja "Cannot clone submodules." "\u30b5\u30d6\u30e2\u30b8\u30e5\u30fc\u30eb\u304c\u8907\u88fd\u3067\u304d\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Cloning submodules" "\u30b5\u30d6\u30e2\u30b8\u30e5\u30fc\u30eb\u3092\u8907\u88fd\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "Initial file checkout failed." "\u521d\u671f\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8\u306b\u5931\u6557\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Open" "\u958b\u304f"
::msgcat::mcset ja "Repository:" "\u30ea\u30dd\u30b8\u30c8\u30ea:"
::msgcat::mcset ja "Failed to open repository %s:" "\u30ea\u30dd\u30b8\u30c8\u30ea %s \u3092\u958b\u3051\u307e\u305b\u3093:"
::msgcat::mcset ja "Rename Branch" "\u30d6\u30e9\u30f3\u30c1\u306e\u540d\u524d\u5909\u66f4"
::msgcat::mcset ja "Rename" "\u540d\u524d\u5909\u66f4"
::msgcat::mcset ja "Branch:" "\u30d6\u30e9\u30f3\u30c1:"
::msgcat::mcset ja "New Name:" "\u65b0\u3057\u3044\u540d\u524d:"
::msgcat::mcset ja "Please select a branch to rename." "\u540d\u524d\u3092\u5909\u66f4\u3059\u308b\u30d6\u30e9\u30f3\u30c1\u3092\u9078\u3093\u3067\u4e0b\u3055\u3044\u3002"
::msgcat::mcset ja "Please supply a branch name." "\u30d6\u30e9\u30f3\u30c1\u540d\u3092\u6307\u5b9a\u3057\u3066\u4e0b\u3055\u3044\u3002"
::msgcat::mcset ja "'%s' is not an acceptable branch name." "'%s' \u306f\u30d6\u30e9\u30f3\u30c1\u540d\u306b\u4f7f\u3048\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Failed to rename '%s'." "'%s'\u306e\u540d\u524d\u5909\u66f4\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002"
::msgcat::mcset ja "Cannot write shortcut:" "\u30b7\u30e7\u30fc\u30c8\u30ab\u30c3\u30c8\u304c\u66f8\u3051\u307e\u305b\u3093:"
::msgcat::mcset ja "Cannot write icon:" "\u30a2\u30a4\u30b3\u30f3\u304c\u66f8\u3051\u307e\u305b\u3093:"
::msgcat::mcset ja "Find:" "\u691c\u7d22:"
::msgcat::mcset ja "Next" "\u6b21"
::msgcat::mcset ja "Prev" "\u524d"
::msgcat::mcset ja "RegExp" "\u6b63\u898f\u8868\u73fe"
::msgcat::mcset ja "Case" "\u5927\u6587\u5b57\u5c0f\u6587\u5b57\u3092\u533a\u5225"
::msgcat::mcset ja "%s ... %*i of %*i %s (%3i%%)" "%1\$s ... %4\$*i %6\$s \u4e2d\u306e %2\$*i (%7\$3i%%)"
::msgcat::mcset ja "Add Tool" "\u30c4\u30fc\u30eb\u306e\u8ffd\u52a0"
::msgcat::mcset ja "Add New Tool Command" "\u65b0\u898f\u30c4\u30fc\u30eb\u30b3\u30de\u30f3\u30c9\u306e\u8ffd\u52a0"
::msgcat::mcset ja "Add globally" "\u5168\u4f53\u306b\u8ffd\u52a0"
::msgcat::mcset ja "Add" "\u8ffd\u52a0"
::msgcat::mcset ja "Tool Details" "\u30c4\u30fc\u30eb\u306e\u8a73\u7d30"
::msgcat::mcset ja "Use '/' separators to create a submenu tree:" "'/' \u3067\u30b5\u30d6\u30e1\u30cb\u30e5\u30fc\u3092\u533a\u5207\u308a\u307e\u3059:"
::msgcat::mcset ja "Name:" "\u540d\u524d:"
::msgcat::mcset ja "Command:" "\u30b3\u30de\u30f3\u30c9:"
::msgcat::mcset ja "Show a dialog before running" "\u8d77\u52d5\u3059\u308b\u524d\u306b\u30c0\u30a4\u30a2\u30ed\u30b0\u3092\u8868\u793a"
::msgcat::mcset ja "Ask the user to select a revision (sets \$REVISION)" "\u30e6\u30fc\u30b6\u306b\u30b3\u30df\u30c3\u30c8\u3092\u4e00\u3064\u9078\u3070\u305b\u308b (\$REVISION \u306b\u30bb\u30c3\u30c8\u3057\u307e\u3059)"
::msgcat::mcset ja "Ask the user for additional arguments (sets \$ARGS)" "\u30e6\u30fc\u30b6\u306b\u4ed6\u306e\u5f15\u6570\u3092\u8ffd\u52a0\u3055\u305b\u308b (\$ARGS \u306b\u30bb\u30c3\u30c8\u3057\u307e\u3059)"
::msgcat::mcset ja "Don't show the command output window" "\u30b3\u30de\u30f3\u30c9\u304b\u3089\u306e\u51fa\u529b\u30a6\u30a3\u30f3\u30c9\u30a6\u3092\u898b\u305b\u306a\u3044"
::msgcat::mcset ja "Run only if a diff is selected (\$FILENAME not empty)" "\u30d1\u30c3\u30c1\u304c\u9078\u3070\u308c\u3066\u3044\u308b\u3068\u304d\u3060\u3051\u52d5\u304b\u3059(\$FILENAME \u304c\u7a7a\u3067\u306a\u3044)"
::msgcat::mcset ja "Please supply a name for the tool." "\u30c4\u30fc\u30eb\u540d\u3092\u6307\u5b9a\u3057\u3066\u4e0b\u3055\u3044\u3002"
::msgcat::mcset ja "Tool '%s' already exists." "\u30c4\u30fc\u30eb '%s' \u306f\u65e2\u306b\u5b58\u5728\u3057\u307e\u3059\u3002"
::msgcat::mcset ja "Could not add tool:\n%s" "\u30c4\u30fc\u30eb\u3092\u8ffd\u52a0\u3067\u304d\u307e\u305b\u3093:\n%s"
::msgcat::mcset ja "Remove Tool" "\u30c4\u30fc\u30eb\u306e\u524a\u9664"
::msgcat::mcset ja "Remove Tool Commands" "\u30c4\u30fc\u30eb\u30b3\u30de\u30f3\u30c9\u306e\u524a\u9664"
::msgcat::mcset ja "Remove" "\u524a\u9664"
::msgcat::mcset ja "(Blue denotes repository-local tools)" "(\u9752\u8272\u306f\u30ed\u30fc\u30ab\u30eb\u30ec\u30dd\u30b8\u30c8\u30ea\u306e\u30c4\u30fc\u30eb\u3067\u3059)"
::msgcat::mcset ja "Run Command: %s" "\u30b3\u30de\u30f3\u30c9\u3092\u8d77\u52d5: %s"
::msgcat::mcset ja "Arguments" "\u5f15\u6570"
::msgcat::mcset ja "Revision" "\u30ea\u30d3\u30b8\u30e7\u30f3"
::msgcat::mcset ja "OK" "OK"
::msgcat::mcset ja "Running %s requires a selected file." "\u30d5\u30a1\u30a4\u30eb\u3092\u9078\u629e\u3057\u3066\u304b\u3089 %s \u3092\u8d77\u52d5\u3057\u3066\u304f\u3060\u3055\u3044\u3002"
::msgcat::mcset ja "Are you sure you want to run %1\$s on file \"%2\$s\"?" "\u672c\u5f53\u306b\u30d5\u30a1\u30a4\u30eb \"%2\$s\"\u3067 %1\$s \u3092\u8d77\u52d5\u3057\u307e\u3059\u304b?"
::msgcat::mcset ja "Are you sure you want to run %s?" "\u672c\u5f53\u306b %s \u3092\u8d77\u52d5\u3057\u307e\u3059\u304b?"
::msgcat::mcset ja "Tool: %s" "\u30c4\u30fc\u30eb: %s"
::msgcat::mcset ja "Running: %s" "\u5b9f\u884c\u4e2d: %s"
::msgcat::mcset ja "Tool completed successfully: %s" "\u30c4\u30fc\u30eb\u304c\u5b8c\u4e86\u3057\u307e\u3057\u305f: %s"
::msgcat::mcset ja "Tool failed: %s" "\u30c4\u30fc\u30eb\u304c\u5931\u6557\u3057\u307e\u3057\u305f: %s"
::msgcat::mcset ja "Force resolution to the base version?" "\u5171\u901a\u306e\u7248\u3092\u4f7f\u3044\u307e\u3059\u304b?"
::msgcat::mcset ja "Force resolution to this branch?" "\u81ea\u5206\u306e\u5074\u306e\u7248\u3092\u4f7f\u3044\u307e\u3059\u304b?"
::msgcat::mcset ja "Force resolution to the other branch?" "\u76f8\u624b\u5236\u306e\u7248\u3092\u4f7f\u3044\u307e\u3059\u304b?"
::msgcat::mcset ja "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "\u7af6\u5408\u3059\u308b\u5909\u66f4\u70b9\u3060\u3051\u304c\u8868\u793a\u3055\u308c\u3066\u3044\u308b\u3053\u3068\u306b\u6ce8\u610f\u3057\u3066\u304f\u3060\u3055\u3044\u3002\n\n%s \u306f\u4e0a\u66f8\u304d\u3055\u308c\u307e\u3059\u3002\n\n\u3084\u308a\u76f4\u3059\u306b\u306f\u30de\u30fc\u30b8\u5168\u4f53\u3092\u3084\u308a\u76f4\u3057\u3066\u304f\u3060\u3055\u3044\u3002"
::msgcat::mcset ja "File %s seems to have unresolved conflicts, still stage?" "\u30d5\u30a1\u30a4\u30eb %s \u306b\u306f\u89e3\u6c7a\u3057\u3066\u3044\u306a\u3044\u7af6\u5408\u90e8\u5206\u304c\u307e\u3060\u3042\u308b\u3088\u3046\u3067\u3059\u304c\u3001\u3044\u3044\u3067\u3059\u304b?"
::msgcat::mcset ja "Adding resolution for %s" "%s \u3078\u306e\u89e3\u6c7a\u3092\u30b9\u30c6\u30fc\u30b8\u3057\u307e\u3059"
::msgcat::mcset ja "Cannot resolve deletion or link conflicts using a tool" "\u30c4\u30fc\u30eb\u3067\u306f\u524a\u9664\u3084\u30ea\u30f3\u30af\u7af6\u5408\u306f\u6271\u3048\u307e\u305b\u3093"
::msgcat::mcset ja "Conflict file does not exist" "\u7af6\u5408\u30d5\u30a1\u30a4\u30eb\u306f\u5b58\u5728\u3057\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Not a GUI merge tool: '%s'" "GUI \u30de\u30fc\u30b8\u30c4\u30fc\u30eb\u3067\u306f\u3042\u308a\u307e\u305b\u3093: %s"
::msgcat::mcset ja "Unsupported merge tool '%s'" "\u30de\u30fc\u30b8\u30c4\u30fc\u30eb '%s' \u306f\u30b5\u30dd\u30fc\u30c8\u3057\u3066\u3044\u307e\u305b\u3093"
::msgcat::mcset ja "Merge tool is already running, terminate it?" "\u30de\u30fc\u30b8\u30c4\u30fc\u30eb\u306f\u3059\u3067\u306b\u8d77\u52d5\u3057\u3066\u3044\u307e\u3059\u3002\u7d42\u4e86\u3057\u307e\u3059\u304b?"
::msgcat::mcset ja "Error retrieving versions:\n%s" "\u7248\u306e\u53d6\u308a\u51fa\u3057\u6642\u306b\u30a8\u30e9\u30fc\u304c\u51fa\u307e\u3057\u305f:\n%s"
::msgcat::mcset ja "Could not start the merge tool:\n\n%s" "\u30de\u30fc\u30b8\u30c4\u30fc\u30eb\u304c\u8d77\u52d5\u3067\u304d\u307e\u305b\u3093:\n\n%s"
::msgcat::mcset ja "Running merge tool..." "\u30de\u30fc\u30b8\u30c4\u30fc\u30eb\u3092\u5b9f\u884c\u3057\u3066\u3044\u307e\u3059..."
::msgcat::mcset ja "Merge tool failed." "\u30de\u30fc\u30b8\u30c4\u30fc\u30eb\u304c\u5931\u6557\u3057\u307e\u3057\u305f\u3002"
::msgcat::mcset ja "Invalid global encoding '%s'" "\u5168\u4f53\u30a8\u30f3\u30b3\u30fc\u30c7\u30a3\u30f3\u30b0\u306b \u7121\u52b9\u306a %s \u304c\u6307\u5b9a\u3055\u308c\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "Invalid repo encoding '%s'" "\u30ea\u30dd\u30b8\u30c8\u30ea\u30a8\u30f3\u30b3\u30fc\u30c7\u30a3\u30f3\u30b0\u306b \u7121\u52b9\u306a %s \u304c\u6307\u5b9a\u3055\u308c\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "Restore Defaults" "\u65e2\u5b9a\u5024\u306b\u623b\u3059"
::msgcat::mcset ja "Save" "\u4fdd\u5b58"
::msgcat::mcset ja "%s Repository" "%s \u30ea\u30dd\u30b8\u30c8\u30ea"
::msgcat::mcset ja "Global (All Repositories)" "\u5927\u57df\uff08\u5168\u3066\u306e\u30ea\u30dd\u30b8\u30c8\u30ea\uff09"
::msgcat::mcset ja "User Name" "\u30e6\u30fc\u30b6\u540d"
::msgcat::mcset ja "Email Address" "\u96fb\u5b50\u30e1\u30fc\u30eb\u30a2\u30c9\u30ec\u30b9"
::msgcat::mcset ja "Summarize Merge Commits" "\u30de\u30fc\u30b8\u30b3\u30df\u30c3\u30c8\u306e\u8981\u7d04"
::msgcat::mcset ja "Merge Verbosity" "\u30de\u30fc\u30b8\u306e\u5197\u9577\u5ea6"
::msgcat::mcset ja "Show Diffstat After Merge" "\u30de\u30fc\u30b8\u5f8c\u306b diffstat \u3092\u8868\u793a"
::msgcat::mcset ja "Use Merge Tool" "\u30de\u30fc\u30b8\u30c4\u30fc\u30eb\u3092\u4f7f\u7528"
::msgcat::mcset ja "Trust File Modification Timestamps" "\u30d5\u30a1\u30a4\u30eb\u5909\u66f4\u6642\u523b\u3092\u4fe1\u983c\u3059\u308b"
::msgcat::mcset ja "Prune Tracking Branches During Fetch" "\u30d5\u30a7\u30c3\u30c1\u4e2d\u306b\u30c8\u30e9\u30c3\u30ad\u30f3\u30b0\u30d6\u30e9\u30f3\u30c1\u3092\u5208\u308b"
::msgcat::mcset ja "Match Tracking Branches" "\u30c8\u30e9\u30c3\u30ad\u30f3\u30b0\u30d6\u30e9\u30f3\u30c1\u3092\u5408\u308f\u305b\u308b"
::msgcat::mcset ja "Use Textconv For Diffs and Blames" "diff \u3068\u6ce8\u91c8\u306b textconv \u3092\u4f7f\u3046"
::msgcat::mcset ja "Blame Copy Only On Changed Files" "\u5909\u66f4\u3055\u308c\u305f\u30d5\u30a1\u30a4\u30eb\u306e\u307f\u30b3\u30d4\u30fc\u691c\u77e5\u3092\u884c\u306a\u3046"
::msgcat::mcset ja "Maximum Length of Recent Repositories List" "\u6700\u8fd1\u4f7f\u3063\u305f\u30ea\u30dd\u30b8\u30c8\u30ea\u4e00\u89a7\u306e\u4e0a\u9650"
::msgcat::mcset ja "Minimum Letters To Blame Copy On" "\u30b3\u30d4\u30fc\u3092\u691c\u77e5\u3059\u308b\u6700\u5c11\u6587\u5b57\u6570"
::msgcat::mcset ja "Blame History Context Radius (days)" "\u6ce8\u91c8\u3059\u308b\u5c65\u6b74\u534a\u5f84\uff08\u65e5\u6570\uff09"
::msgcat::mcset ja "Number of Diff Context Lines" "diff \u306e\u6587\u8108\u884c\u6570"
::msgcat::mcset ja "Additional Diff Parameters" "diff \u306e\u8ffd\u52a0\u5f15\u6570"
::msgcat::mcset ja "Commit Message Text Width" "\u30b3\u30df\u30c3\u30c8\u30e1\u30c3\u30bb\u30fc\u30b8\u306e\u30c6\u30ad\u30b9\u30c8\u5e45"
::msgcat::mcset ja "New Branch Name Template" "\u65b0\u3057\u3044\u30d6\u30e9\u30f3\u30c1\u540d\u306e\u30c6\u30f3\u30d7\u30ec\u30fc\u30c8"
::msgcat::mcset ja "Default File Contents Encoding" "\u30d5\u30a1\u30a4\u30eb\u5185\u5bb9\u306e\u30c7\u30d5\u30a9\u30fc\u30eb\u30c8\u30a8\u30f3\u30b3\u30fc\u30c7\u30a3\u30f3\u30b0"
::msgcat::mcset ja "Warn before committing to a detached head" "\u5206\u96e2 HEAD \u306e\u30b3\u30df\u30c3\u30c8\u524d\u306b\u8b66\u544a\u3059\u308b"
::msgcat::mcset ja "Staging of untracked files" "\u7ba1\u7406\u5916\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u3059\u308b"
::msgcat::mcset ja "Show untracked files" "\u7ba1\u7406\u5916\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u8868\u793a\u3059\u308b"
::msgcat::mcset ja "Tab spacing" "\u30bf\u30d6\u5e45"
::msgcat::mcset ja "Change" "\u5909\u66f4"
::msgcat::mcset ja "Spelling Dictionary:" "\u30b9\u30da\u30eb\u30c1\u30a7\u30c3\u30af\u8f9e\u66f8"
::msgcat::mcset ja "Change Font" "\u30d5\u30a9\u30f3\u30c8\u3092\u5909\u66f4"
::msgcat::mcset ja "Choose %s" "%s \u3092\u9078\u629e"
::msgcat::mcset ja "pt." "\u30dd\u30a4\u30f3\u30c8"
::msgcat::mcset ja "Preferences" "\u8a2d\u5b9a"
::msgcat::mcset ja "Options" "\u30aa\u30d7\u30b7\u30e7\u30f3"
::msgcat::mcset ja "Failed to completely save options:" "\u5b8c\u5168\u306b\u30aa\u30d7\u30b7\u30e7\u30f3\u3092\u4fdd\u5b58\u3067\u304d\u307e\u305b\u3093:"
::msgcat::mcset ja "Number of loose objects" "\u3070\u3089\u3070\u3089\u306a\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u306e\u6570"
::msgcat::mcset ja "Disk space used by loose objects" "\u3070\u3089\u3070\u3089\u306a\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u306e\u4f7f\u7528\u3059\u308b\u30c7\u30a3\u30b9\u30af\u91cf"
::msgcat::mcset ja "Number of packed objects" "\u30d1\u30c3\u30af\u3055\u308c\u305f\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u306e\u6570"
::msgcat::mcset ja "Number of packs" "\u30d1\u30c3\u30af\u306e\u6570"
::msgcat::mcset ja "Disk space used by packed objects" "\u30d1\u30c3\u30af\u3055\u308c\u305f\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u306e\u4f7f\u7528\u3059\u308b\u30c7\u30a3\u30b9\u30af\u91cf"
::msgcat::mcset ja "Packed objects waiting for pruning" "\u30d1\u30c3\u30af\u306b\u5b58\u5728\u3059\u308b\u306e\u3067\u6368\u3066\u3066\u826f\u3044\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u306e\u6570"
::msgcat::mcset ja "Garbage files" "\u30b4\u30df\u30d5\u30a1\u30a4\u30eb"
::msgcat::mcset ja "Compressing the object database" "\u30c7\u30fc\u30bf\u30d9\u30fc\u30b9\u5727\u7e2e"
::msgcat::mcset ja "Verifying the object database with fsck-objects" "fsck-objects \u3067\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u30fb\u30c7\u30fc\u30bf\u30d9\u30fc\u30b9\u3092\u691c\u8a3c\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "\u3053\u306e\u30ea\u30dd\u30b8\u30c8\u30ea\u306b\u306f\u304a\u304a\u3088\u305d %i \u500b\u306e\u500b\u5225\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u304c\u3042\u308a\u307e\u3059\n\n\u6700\u9069\u306a\u6027\u80fd\u3092\u4fdd\u3064\u305f\u3081\u306b\u3001\u30c7\u30fc\u30bf\u30d9\u30fc\u30b9\u3092\u5727\u7e2e\u3059\u308b\u3053\u3068\u3092\u63a8\u5968\u3057\u307e\u3059\n\n\u30c7\u30fc\u30bf\u30d9\u30fc\u30b9\u3092\u5727\u7e2e\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "fetch %s" "%s \u3092\u53d6\u5f97"
::msgcat::mcset ja "Fetching new changes from %s" "%s \u304b\u3089\u65b0\u3057\u3044\u5909\u66f4\u3092\u30d5\u30a7\u30c3\u30c1\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "remote prune %s" "\u30ea\u30e2\u30fc\u30c8\u5208\u8fbc %s"
::msgcat::mcset ja "Pruning tracking branches deleted from %s" "%s \u304b\u3089\u524a\u9664\u3055\u308c\u305f\u30c8\u30e9\u30c3\u30ad\u30f3\u30b0\u30fb\u30d6\u30e9\u30f3\u30c1\u3092\u5208\u3063\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "fetch all remotes" "\u3059\u3079\u3066\u306e\u30ea\u30e2\u30fc\u30c8\u3092\u53d6\u5f97"
::msgcat::mcset ja "Fetching new changes from all remotes" "\u3059\u3079\u3066\u306e\u30ea\u30e2\u30fc\u30c8\u304b\u3089\u65b0\u3057\u3044\u5909\u66f4\u3092\u30d5\u30a7\u30c3\u30c1\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "remote prune all remotes" "\u30ea\u30e2\u30fc\u30c8\u5208\u8fbc \u3059\u3079\u3066\u306e\u30ea\u30e2\u30fc\u30c8"
::msgcat::mcset ja "Pruning tracking branches deleted from all remotes" "\u3059\u3079\u3066\u306e\u30ea\u30e2\u30fc\u30c8\u304b\u3089\u524a\u9664\u3055\u308c\u305f\u30c8\u30e9\u30c3\u30ad\u30f3\u30b0\u30fb\u30d6\u30e9\u30f3\u30c1\u3092\u5208\u3063\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "push %s" "%s \u3092\u30d7\u30c3\u30b7\u30e5"
::msgcat::mcset ja "Pushing changes to %s" "%s \u3078\u5909\u66f4\u3092\u30d7\u30c3\u30b7\u30e5\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "Mirroring to %s" "%s \u3078\u30df\u30e9\u30fc\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "Pushing %s %s to %s" "%3\$s \u3078 %1\$s %2\$s \u3092\u30d7\u30c3\u30b7\u30e5\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "Push Branches" "\u30d6\u30e9\u30f3\u30c1\u3092\u30d7\u30c3\u30b7\u30e5"
::msgcat::mcset ja "Source Branches" "\u5143\u306e\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Destination Repository" "\u9001\u308a\u5148\u30ea\u30dd\u30b8\u30c8\u30ea"
::msgcat::mcset ja "Transfer Options" "\u901a\u4fe1\u30aa\u30d7\u30b7\u30e7\u30f3"
::msgcat::mcset ja "Force overwrite existing branch (may discard changes)" "\u65e2\u5b58\u30d6\u30e9\u30f3\u30c1\u3092\u4e0a\u66f8\u304d(\u5909\u66f4\u3092\u7834\u68c4\u3059\u308b\u53ef\u80fd\u6027\u304c\u3042\u308a\u307e\u3059)"
::msgcat::mcset ja "Use thin pack (for slow network connections)" "Thin Pack \u3092\u4f7f\u3046\uff08\u9045\u3044\u30cd\u30c3\u30c8\u30ef\u30fc\u30af\u63a5\u7d9a\uff09"
::msgcat::mcset ja "Include tags" "\u30bf\u30b0\u3092\u542b\u3081\u308b"
::msgcat::mcset ja "Select" "\u9078\u629e"
::msgcat::mcset ja "Font Family" "\u30d5\u30a9\u30f3\u30c8\u30fb\u30d5\u30a1\u30df\u30ea\u30fc"
::msgcat::mcset ja "Font Size" "\u30d5\u30a9\u30f3\u30c8\u306e\u5927\u304d\u3055"
::msgcat::mcset ja "Font Example" "\u30d5\u30a9\u30f3\u30c8\u30fb\u30b5\u30f3\u30d7\u30eb"
::msgcat::mcset ja "This is example text.\nIf you like this text, it can be your font." "\u3053\u308c\u306f\u30b5\u30f3\u30d7\u30eb\u6587\u3067\u3059\u3002\n\u3053\u306e\u30d5\u30a9\u30f3\u30c8\u304c\u6c17\u306b\u5165\u308c\u3070\u304a\u4f7f\u3044\u306b\u306a\u308c\u307e\u3059\u3002"
::msgcat::mcset ja "Push to" "\u30d7\u30c3\u30b7\u30e5\u5148"
::msgcat::mcset ja "Remove Remote" "\u30ea\u30e2\u30fc\u30c8\u3092\u524a\u9664"
::msgcat::mcset ja "Prune from" "\u304b\u3089\u5208\u8fbc\u3080\u2026"
::msgcat::mcset ja "Fetch from" "\u53d6\u5f97\u5143"
::msgcat::mcset ja "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "\u5909\u66f4\u304c\u3042\u308a\u307e\u305b\u3093\u3002\n\n%s \u306b\u306f\u5909\u66f4\u304c\u3042\u308a\u307e\u305b\u3093\u3002\n\n\u3053\u306e\u30d5\u30a1\u30a4\u30eb\u306e\u5909\u66f4\u6642\u523b\u306f\u4ed6\u306e\u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u306b\u3088\u3063\u3066\u66f4\u65b0\u3055\u308c\u3066\u3044\u307e\u3059\u304c\u30d5\u30a1\u30a4\u30eb\u5185\u5bb9\u306b\u306f\u5909\u66f4\u304c\u3042\u308a\u307e\u305b\u3093\u3002\n\n\u540c\u69d8\u306a\u72b6\u614b\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u63a2\u3059\u305f\u3081\u306b\u3001\u81ea\u52d5\u7684\u306b\u518d\u30b9\u30ad\u30e3\u30f3\u3092\u958b\u59cb\u3057\u307e\u3059\u3002"
::msgcat::mcset ja "Loading diff of %s..." "%s \u306e\u5909\u66f4\u70b9\u3092\u30ed\u30fc\u30c9\u4e2d\u2026"
::msgcat::mcset ja "LOCAL: deleted\nREMOTE:\n" "LOCAL: \u524a\u9664\nRemote:\n"
::msgcat::mcset ja "REMOTE: deleted\nLOCAL:\n" "REMOTE: \u524a\u9664\nLOCAL:\n"
::msgcat::mcset ja "LOCAL:\n" "LOCAL:\n"
::msgcat::mcset ja "REMOTE:\n" "REMOTE\n"
::msgcat::mcset ja "Unable to display %s" "%s \u3092\u8868\u793a\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "Error loading file:" "\u30d5\u30a1\u30a4\u30eb\u3092\u8aad\u3080\u969b\u306e\u30a8\u30e9\u30fc\u3067\u3059:"
::msgcat::mcset ja "Git Repository (subproject)" "Git \u30ea\u30dd\u30b8\u30c8\u30ea(\u30b5\u30d6\u30d7\u30ed\u30b8\u30a7\u30af\u30c8)"
::msgcat::mcset ja "* Binary file (not showing content)." "* \u30d0\u30a4\u30ca\u30ea\u30d5\u30a1\u30a4\u30eb(\u5185\u5bb9\u306f\u8868\u793a\u3057\u307e\u305b\u3093)"
::msgcat::mcset ja "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* \u7ba1\u7406\u5916\u306e\u30d5\u30a1\u30a4\u30eb\u306e\u5927\u304d\u3055\u306f %d \u30d0\u30a4\u30c8\u3067\u3059\u3002\n* \u6700\u521d\u306e %d \u30d0\u30a4\u30c8\u3060\u3051\u8868\u793a\u3057\u3066\u3044\u307e\u3059\u3002\n"
::msgcat::mcset ja "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n\n* %s \u306f\u7ba1\u7406\u5916\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u3053\u3053\u3067\u5207\u308a\u304a\u3068\u3057\u307e\u3057\u305f\u3002\n* \u5168\u4f53\u3092\u898b\u308b\u306b\u306f\u5916\u90e8\u30a8\u30c7\u30a3\u30bf\u3092\u4f7f\u3063\u3066\u304f\u3060\u3055\u3044\u3002\n"
::msgcat::mcset ja "Failed to unstage selected hunk." "\u9078\u629e\u3055\u308c\u305f\u30d1\u30c3\u30c1\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u304b\u3089\u5916\u305b\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Failed to stage selected hunk." "\u9078\u629e\u3055\u308c\u305f\u30d1\u30c3\u30c1\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u52a0\u3048\u3089\u308c\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Failed to unstage selected line." "\u9078\u629e\u3055\u308c\u305f\u30d1\u30c3\u30c1\u884c\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u304b\u3089\u5916\u305b\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Failed to stage selected line." "\u9078\u629e\u3055\u308c\u305f\u30d1\u30c3\u30c1\u884c\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u52a0\u3048\u3089\u308c\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Checkout Branch" "\u30d6\u30e9\u30f3\u30c1\u3092\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8"
::msgcat::mcset ja "Checkout" "\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8"
::msgcat::mcset ja "Fetch Tracking Branch" "\u30c8\u30e9\u30c3\u30ad\u30f3\u30b0\u30fb\u30d6\u30e9\u30f3\u30c1\u3092\u30d5\u30a7\u30c3\u30c1"
::msgcat::mcset ja "Detach From Local Branch" "\u30ed\u30fc\u30ab\u30eb\u30fb\u30d6\u30e9\u30f3\u30c1\u304b\u3089\u524a\u9664"
::msgcat::mcset ja "Unable to unlock the index." "\u30a4\u30f3\u30c7\u30c3\u30af\u30b9\u3092\u30ed\u30c3\u30af\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "Index Error" "\u7d22\u5f15\u30a8\u30e9\u30fc"
::msgcat::mcset ja "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "GIT \u30a4\u30f3\u30c7\u30c3\u30af\u30b9\u306e\u66f4\u65b0\u304c\u5931\u6557\u3057\u307e\u3057\u305f\u3002git-gui \u3068\u540c\u671f\u3092\u3068\u308b\u305f\u3081\u306b\u518d\u30b9\u30ad\u30e3\u30f3\u3057\u307e\u3059\u3002"
::msgcat::mcset ja "Continue" "\u7d9a\u884c"
::msgcat::mcset ja "Unlock Index" "\u30a4\u30f3\u30c7\u30c3\u30af\u30b9\u306e\u30ed\u30c3\u30af\u89e3\u9664"
::msgcat::mcset ja "Unstaging %s from commit" "\u30b3\u30df\u30c3\u30c8\u304b\u3089 '%s' \u3092\u964d\u308d\u3059"
::msgcat::mcset ja "Ready to commit." "\u30b3\u30df\u30c3\u30c8\u6e96\u5099\u5b8c\u4e86"
::msgcat::mcset ja "Adding %s" "\u30b3\u30df\u30c3\u30c8\u306b %s \u3092\u52a0\u3048\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "Stage %d untracked files?" "\u7ba1\u7406\u5916\u306e %d \u30d5\u30a1\u30a4\u30eb\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u3068\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Revert changes in file %s?" "\u30d5\u30a1\u30a4\u30eb %s \u306b\u3057\u305f\u5909\u66f4\u3092\u5143\u306b\u623b\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Revert changes in these %i files?" "\u3053\u308c\u3089 %i \u500b\u306e\u30d5\u30a1\u30a4\u30eb\u306b\u3057\u305f\u5909\u66f4\u3092\u5143\u306b\u623b\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Any unstaged changes will be permanently lost by the revert." "\u5909\u66f4\u3092\u5143\u306b\u623b\u3059\u3068\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u3057\u3066\u3044\u306a\u3044\u5909\u66f4\u306f\u5168\u3066\u5931\u308f\u308c\u307e\u3059\u3002"
::msgcat::mcset ja "Do Nothing" "\u4f55\u3082\u3057\u306a\u3044"
::msgcat::mcset ja "Reverting selected files" "\u9078\u629e\u3055\u308c\u305f\u30d5\u30a1\u30a4\u30eb\u306b\u3057\u305f\u5909\u66f4\u3092\u5143\u306b\u623b\u3057\u307e\u3059"
::msgcat::mcset ja "Reverting %s" "%s \u306b\u3057\u305f\u5909\u66f4\u3092\u5143\u306b\u623b\u3057\u307e\u3059"
::msgcat::mcset ja "No keys found." "\u30ad\u30fc\u304c\u3042\u308a\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Found a public key in: %s" "\u516c\u958b\u9375\u304c\u3042\u308a\u307e\u3057\u305f: %s"
::msgcat::mcset ja "Generate Key" "\u9375\u3092\u751f\u6210"
::msgcat::mcset ja "Copy To Clipboard" "\u30af\u30ea\u30c3\u30d7\u30dc\u30fc\u30c9\u306b\u30b3\u30d4\u30fc"
::msgcat::mcset ja "Your OpenSSH Public Key" "\u3042\u306a\u305f\u306e OpenSSH \u516c\u958b\u9375"
::msgcat::mcset ja "Generating..." "\u751f\u6210\u4e2d..."
::msgcat::mcset ja "Could not start ssh-keygen:\n\n%s" "ssh-keygen \u3092\u8d77\u52d5\u3067\u304d\u307e\u305b\u3093:\n\n%s"
::msgcat::mcset ja "Generation failed." "\u751f\u6210\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002"
::msgcat::mcset ja "Generation succeeded, but no keys found." "\u751f\u6210\u306b\u306f\u6210\u529f\u3057\u307e\u3057\u305f\u304c\u3001\u9375\u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Your key is in: %s" "\u3042\u306a\u305f\u306e\u9375\u306f %s \u306b\u3042\u308a\u307e\u3059"
::msgcat::mcset ja "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "\u8a02\u6b63\u3059\u308b\u30b3\u30df\u30c3\u30c8\u304c\u305d\u3082\u305d\u3082\u3042\u308a\u307e\u305b\u3093\u3002\n\n\u3053\u308c\u304b\u3089\u4f5c\u308b\u306e\u306f\u6700\u521d\u306e\u30b3\u30df\u30c3\u30c8\u3067\u3059\u3002\u305d\u306e\u524d\u306b\u306f\u307e\u3060\u8a02\u6b63\u3059\u308b\u3088\u3046\u306a\u30b3\u30df\u30c3\u30c8\u306f\u3042\u308a\u307e\u305b\u3093\u3002\n"
::msgcat::mcset ja "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "\u30de\u30fc\u30b8\u4e2d\u306b\u30b3\u30df\u30c3\u30c8\u306e\u8a02\u6b63\u306f\u3067\u304d\u307e\u305b\u3093\u3002\n\n\u73fe\u5728\u306f\u307e\u3060\u30de\u30fc\u30b8\u306e\u9014\u4e2d\u3067\u3059\u3002\u5148\u306b\u3053\u306e\u30de\u30fc\u30b8\u3092\u4e2d\u6b62\u3057\u306a\u3044\u3068\u3001\u524d\u306e\u30b3\u30df\u30c3\u30c8\u306e\u8a02\u6b63\u306f\u3067\u304d\u307e\u305b\u3093\n"
::msgcat::mcset ja "Error loading commit data for amend:" "\u8a02\u6b63\u3059\u308b\u30b3\u30df\u30c3\u30c8\u306e\u30c7\u30fc\u30bf\u3092\u8aad\u3081\u307e\u305b\u3093:"
::msgcat::mcset ja "Unable to obtain your identity:" "\u30e6\u30fc\u30b6\u306e\u6b63\u4f53\u3092\u78ba\u8a8d\u3067\u304d\u307e\u305b\u3093:"
::msgcat::mcset ja "Invalid GIT_COMMITTER_IDENT:" "GIT_COMMITTER_IDENT \u304c\u7121\u52b9\u3067\u3059:"
::msgcat::mcset ja "warning: Tcl does not support encoding '%s'." "\u8b66\u544a: Tcl \u306f\u30a8\u30f3\u30b3\u30fc\u30c7\u30a3\u30f3\u30b0 '%s' \u3092\u30b5\u30dd\u30fc\u30c8\u3057\u3066\u3044\u307e\u305b\u3093"
::msgcat::mcset ja "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "\u6700\u5f8c\u306b\u30b9\u30ad\u30e3\u30f3\u3057\u305f\u72b6\u614b\u306f\u30ea\u30dd\u30b8\u30c8\u30ea\u306e\u72b6\u614b\u3068\u5408\u81f4\u3057\u307e\u305b\u3093\u3002\n\n\u6700\u5f8c\u306b\u30b9\u30ad\u30e3\u30f3\u3057\u3066\u4ee5\u5f8c\u3001\u5225\u306e Git \u30d7\u30ed\u30b0\u30e9\u30e0\u304c\u30ea\u30dd\u30b8\u30c8\u30ea\u3092\u5909\u66f4\u3057\u3066\u3044\u307e\u3059\u3002\u65b0\u3057\u304f\u30b3\u30df\u30c3\u30c8\u3059\u308b\u524d\u306b\u3001\u518d\u30b9\u30ad\u30e3\u30f3\u304c\u5fc5\u8981\u3067\u3059\u3002\n\n\u81ea\u52d5\u7684\u306b\u518d\u30b9\u30ad\u30e3\u30f3\u3092\u958b\u59cb\u3057\u307e\u3059\u3002\n"
::msgcat::mcset ja "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "\u30de\u30fc\u30b8\u3057\u3066\u3044\u306a\u3044\u30d5\u30a1\u30a4\u30eb\u306f\u30b3\u30df\u30c3\u30c8\u3067\u304d\u307e\u305b\u3093\u3002\n\n\u30d5\u30a1\u30a4\u30eb %s \u306b\u306f\u30de\u30fc\u30b8\u885d\u7a81\u304c\u6b8b\u3063\u3066\u3044\u307e\u3059\u3002\u307e\u305a\u89e3\u6c7a\u3057\u3066\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u52a0\u3048\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002\n"
::msgcat::mcset ja "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "\u4e0d\u660e\u306a\u30d5\u30a1\u30a4\u30eb\u72b6\u614b %s \u3067\u3059\u3002\n\n\u30d5\u30a1\u30a4\u30eb %s \u306f\u672c\u30d7\u30ed\u30b0\u30e9\u30e0\u3067\u306f\u30b3\u30df\u30c3\u30c8\u3067\u304d\u307e\u305b\u3093\u3002\n"
::msgcat::mcset ja "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "\u30b3\u30df\u30c3\u30c8\u3059\u308b\u5909\u66f4\u304c\u3042\u308a\u307e\u305b\u3093\u3002\n\n\u6700\u4f4e\u4e00\u3064\u306e\u5909\u66f4\u3092\u30b3\u30df\u30c3\u30c8\u4e88\u5b9a\u306b\u52a0\u3048\u3066\u304b\u3089\u30b3\u30df\u30c3\u30c8\u3057\u3066\u4e0b\u3055\u3044\u3002\n"
::msgcat::mcset ja "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "\u30b3\u30df\u30c3\u30c8\u30fb\u30e1\u30c3\u30bb\u30fc\u30b8\u3092\u5165\u529b\u3057\u3066\u4e0b\u3055\u3044\u3002\n\n\u6b63\u3057\u3044\u30b3\u30df\u30c3\u30c8\u30fb\u30e1\u30c3\u30bb\u30fc\u30b8\u306f:\n\n- \u7b2c\uff11\u884c: \u4f55\u3092\u3057\u305f\u304b\u3001\u3092\uff11\u884c\u3067\u8981\u7d04\u3002\n- \u7b2c\uff12\u884c: \u7a7a\u767d\n- \u6b8b\u308a\u306e\u884c: \u306a\u305c\u3001\u3053\u306e\u5909\u66f4\u304c\u826f\u3044\u5909\u66f4\u304b\u3001\u306e\u8aac\u660e\u3002\n"
::msgcat::mcset ja "Calling pre-commit hook..." "\u30b3\u30df\u30c3\u30c8\u524d\u30d5\u30c3\u30af\u3092\u5b9f\u884c\u4e2d\u30fb\u30fb\u30fb"
::msgcat::mcset ja "Commit declined by pre-commit hook." "\u30b3\u30df\u30c3\u30c8\u524d\u30d5\u30c3\u30af\u304c\u30b3\u30df\u30c3\u30c8\u3092\u62d2\u5426\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "You are about to commit on a detached head. This is a potentially dangerous thing to do because if you switch to another branch you will lose your changes and it can be difficult to retrieve them later from the reflog. You should probably cancel this commit and create a new branch to continue.\n \n Do you really want to proceed with your Commit?" "\u5206\u96e2 HEAD \u3067\u306e\u5909\u66f4\u3092\u30b3\u30df\u30c3\u30c8\u3057\u3088\u3046\u3068\u3057\u3066\u3044\u307e\u3059\u3002\u3053\u308c\u306f\u6f5c\u5728\u7684\u306b\u5371\u967a\u306a\u884c\u70ba\u3067\u3001\u7406\u7531\u306f\u5225\u306e\u30d6\u30e9\u30f3\u30c1\u3078\u306e\u5207\u308a\u66ff\u3048\u3067\u5909\u66f4\u304c\u6d88\u5931\u3057\u3001reflog \u304b\u3089\u306e\u4e8b\u5f8c\u5fa9\u65e7\u3082\u56f0\u96e3\u3068\u306a\u308b\u305f\u3081\u3067\u3059\u3002\u304a\u305d\u3089\u304f\u3053\u306e\u30b3\u30df\u30c3\u30c8\u306f\u30ad\u30e3\u30f3\u30bb\u30eb\u3057\u65b0\u3057\u304f\u4f5c\u6210\u3057\u305f\u30d6\u30e9\u30f3\u30c1\u3067\u884c\u3046\u3079\u304d\u3067\u3059\u3002\n\n \u672c\u5f53\u306b\u30b3\u30df\u30c3\u30c8\u3092\u7d9a\u884c\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Calling commit-msg hook..." "\u30b3\u30df\u30c3\u30c8\u30fb\u30e1\u30c3\u30bb\u30fc\u30b8\u30fb\u30d5\u30c3\u30af\u3092\u5b9f\u884c\u4e2d\u30fb\u30fb\u30fb"
::msgcat::mcset ja "Commit declined by commit-msg hook." "\u30b3\u30df\u30c3\u30c8\u30fb\u30e1\u30c3\u30bb\u30fc\u30b8\u30fb\u30d5\u30c3\u30af\u304c\u30b3\u30df\u30c3\u30c8\u3092\u62d2\u5426\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Committing changes..." "\u5909\u66f4\u70b9\u3092\u30b3\u30df\u30c3\u30c8\u4e2d\u30fb\u30fb\u30fb"
::msgcat::mcset ja "write-tree failed:" "write-tree \u304c\u5931\u6557\u3057\u307e\u3057\u305f:"
::msgcat::mcset ja "Commit failed." "\u30b3\u30df\u30c3\u30c8\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002"
::msgcat::mcset ja "Commit %s appears to be corrupt" "\u30b3\u30df\u30c3\u30c8 %s \u306f\u58ca\u308c\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "\u30b3\u30df\u30c3\u30c8\u3059\u308b\u5909\u66f4\u304c\u3042\u308a\u307e\u305b\u3093\u3002\n\n\u30de\u30fc\u30b8\u3067\u306a\u304f\u3001\u307e\u305f\u3001\u4e00\u3064\u3082\u5909\u66f4\u70b9\u304c\u3042\u308a\u307e\u305b\u3093\u3002\n\n\u81ea\u52d5\u7684\u306b\u518d\u30b9\u30ad\u30e3\u30f3\u3092\u958b\u59cb\u3057\u307e\u3059\u3002\n"
::msgcat::mcset ja "No changes to commit." "\u30b3\u30df\u30c3\u30c8\u3059\u308b\u5909\u66f4\u304c\u3042\u308a\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "commit-tree failed:" "commit-tree \u304c\u5931\u6557\u3057\u307e\u3057\u305f:"
::msgcat::mcset ja "update-ref failed:" "update-ref \u304c\u5931\u6557\u3057\u307e\u3057\u305f:"
::msgcat::mcset ja "Created commit %s: %s" "\u30b3\u30df\u30c3\u30c8 %s \u3092\u4f5c\u6210\u3057\u307e\u3057\u305f: %s"
::msgcat::mcset ja "Starting..." "\u8d77\u52d5\u4e2d\u2026"
::msgcat::mcset ja "File Browser" "\u30d5\u30a1\u30a4\u30eb\u30fb\u30d6\u30e9\u30a6\u30b6"
::msgcat::mcset ja "Loading %s..." "%s \u3092\u30ed\u30fc\u30c9\u4e2d\u2026"
::msgcat::mcset ja "\[Up To Parent\]" "\[\u4e0a\u4f4d\u30d5\u30a9\u30eb\u30c0\u3078\]"
::msgcat::mcset ja "Browse Branch Files" "\u73fe\u5728\u306e\u30d6\u30e9\u30f3\u30c1\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u898b\u308b"
::msgcat::mcset ja "Add Remote" "\u30ea\u30e2\u30fc\u30c8\u3092\u8ffd\u52a0"
::msgcat::mcset ja "Add New Remote" "\u30ea\u30e2\u30fc\u30c8\u3092\u65b0\u898f\u306b\u8ffd\u52a0"
::msgcat::mcset ja "Remote Details" "\u30ea\u30e2\u30fc\u30c8\u306e\u8a73\u7d30"
::msgcat::mcset ja "Location:" "\u5834\u6240:"
::msgcat::mcset ja "Further Action" "\u305d\u306e\u4ed6\u306e\u52d5\u4f5c"
::msgcat::mcset ja "Fetch Immediately" "\u5373\u5ea7\u306b\u53d6\u5f97"
::msgcat::mcset ja "Initialize Remote Repository and Push" "\u30ea\u30e2\u30fc\u30c8\u30ec\u30dd\u30b8\u30c8\u30ea\u3092\u521d\u671f\u5316\u3057\u3066\u30d7\u30c3\u30b7\u30e5"
::msgcat::mcset ja "Do Nothing Else Now" "\u4f55\u3082\u3057\u306a\u3044"
::msgcat::mcset ja "Please supply a remote name." "\u30ea\u30e2\u30fc\u30c8\u540d\u3092\u6307\u5b9a\u3057\u3066\u4e0b\u3055\u3044\u3002"
::msgcat::mcset ja "'%s' is not an acceptable remote name." "'%s' \u306f\u30ea\u30e2\u30fc\u30c8\u540d\u306b\u4f7f\u3048\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Failed to add remote '%s' of location '%s'." "\u5834\u6240 '%2\$s' \u306e\u30ea\u30e2\u30fc\u30c8 '%1\$s'\u306e\u540d\u524d\u5909\u66f4\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002"
::msgcat::mcset ja "Fetching the %s" "%s \u304b\u3089\u30d5\u30a7\u30c3\u30c1\u3057\u3066\u3044\u307e\u3059"
::msgcat::mcset ja "Do not know how to initialize repository at location '%s'." "\u30ea\u30dd\u30b8\u30c8\u30ea '%s' \u3092\u521d\u671f\u5316\u3067\u304d\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Setting up the %s (at %s)" "%2\$s \u306b\u3042\u308b %1\$s \u3092\u30bb\u30c3\u30c8\u30a2\u30c3\u30d7\u3057\u307e\u3059"
::msgcat::mcset ja "Goto Line:" "\u884c\u756a\u53f7"
::msgcat::mcset ja "Go" "\u79fb\u52d5"
::msgcat::mcset ja "Create Branch" "\u30d6\u30e9\u30f3\u30c1\u3092\u4f5c\u6210"
::msgcat::mcset ja "Create New Branch" "\u30d6\u30e9\u30f3\u30c1\u3092\u65b0\u898f\u4f5c\u6210"
::msgcat::mcset ja "Branch Name" "\u30d6\u30e9\u30f3\u30c1\u540d"
::msgcat::mcset ja "Match Tracking Branch Name" "\u30c8\u30e9\u30c3\u30ad\u30f3\u30b0\u30fb\u30d6\u30e9\u30f3\u30c1\u540d\u3092\u5408\u308f\u305b\u308b"
::msgcat::mcset ja "Starting Revision" "\u521d\u671f\u30ea\u30d3\u30b8\u30e7\u30f3"
::msgcat::mcset ja "Update Existing Branch:" "\u65e2\u5b58\u306e\u30d6\u30e9\u30f3\u30c1\u3092\u66f4\u65b0:"
::msgcat::mcset ja "No" "\u3044\u3044\u3048"
::msgcat::mcset ja "Fast Forward Only" "\u65e9\u9001\u308a\u306e\u307f"
::msgcat::mcset ja "Checkout After Creation" "\u4f5c\u6210\u3057\u3066\u3059\u3050\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8"
::msgcat::mcset ja "Please select a tracking branch." "\u30c8\u30e9\u30c3\u30ad\u30f3\u30b0\u30fb\u30d6\u30e9\u30f3\u30c1\u3092\u9078\u629e\u3057\u3066\u4e0b\u3055\u3044\u3002"
::msgcat::mcset ja "Tracking branch %s is not a branch in the remote repository." "\u30c8\u30e9\u30c3\u30ad\u30f3\u30b0\u30fb\u30d6\u30e9\u30f3\u30c1 %s \u306f\u30ea\u30e2\u30fc\u30c8\u30ea\u30dd\u30b8\u30c8\u30ea\u306e\u30d6\u30e9\u30f3\u30c1\u3067\u306f\u3042\u308a\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Unsupported spell checker" "\u30b5\u30dd\u30fc\u30c8\u3055\u308c\u3066\u3044\u306a\u3044\u30b9\u30da\u30eb\u30c1\u30a7\u30c3\u30ab\u30fc\u3067\u3059"
::msgcat::mcset ja "Spell checking is unavailable" "\u30b9\u30da\u30eb\u30c1\u30a7\u30c3\u30af\u6a5f\u80fd\u306f\u4f7f\u3048\u307e\u305b\u3093"
::msgcat::mcset ja "Invalid spell checking configuration" "\u30b9\u30da\u30eb\u30c1\u30a7\u30c3\u30af\u306e\u8a2d\u5b9a\u304c\u4e0d\u6b63\u3067\u3059"
::msgcat::mcset ja "Reverting dictionary to %s." "\u8f9e\u66f8\u3092 %s \u306b\u5dfb\u304d\u623b\u3057\u307e\u3059"
::msgcat::mcset ja "Spell checker silently failed on startup" "\u30b9\u30da\u30eb\u30c1\u30a7\u30c3\u30ab\u30fc\u306e\u8d77\u52d5\u306b\u5931\u6557\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Unrecognized spell checker" "\u30b9\u30da\u30eb\u30c1\u30a7\u30c3\u30ab\u30fc\u304c\u5224\u5225\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "No Suggestions" "\u63d0\u6848\u306a\u3057"
::msgcat::mcset ja "Unexpected EOF from spell checker" "\u30b9\u30da\u30eb\u30c1\u30a7\u30c3\u30ab\u30fc\u304c\u4e88\u60f3\u5916\u306e EOF \u3092\u8fd4\u3057\u307e\u3057\u305f"
::msgcat::mcset ja "Spell Checker Failed" "\u30b9\u30da\u30eb\u30c1\u30a7\u30c3\u30af\u5931\u6557"
