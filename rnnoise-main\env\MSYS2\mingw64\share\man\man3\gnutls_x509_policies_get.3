.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_policies_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_policies_get \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_policies_get(gnutls_x509_policies_t " policies ", unsigned int " seq ", struct gnutls_x509_policy_st * " policy ");"
.SH ARGUMENTS
.IP "gnutls_x509_policies_t policies" 12
The policies
.IP "unsigned int seq" 12
The index of the name to get
.IP "struct gnutls_x509_policy_st * policy" 12
Will hold the policy
.SH "DESCRIPTION"
This function will return a specific policy as stored in
the  \fIpolicies\fP type. The returned values should be treated as constant
and valid for the lifetime of  \fIpolicies\fP .

The any policy OID is available as the \fBGNUTLS_X509_OID_POLICY_ANY\fP macro.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
if the index is out of bounds, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
