.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_verify_session_crt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_verify_session_crt \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "int dane_verify_session_crt(dane_state_t " s ", gnutls_session_t " session ", const char * " hostname ", const char * " proto ", unsigned int " port ", unsigned int " sflags ", unsigned int " vflags ", unsigned int * " verify ");"
.SH ARGUMENTS
.IP "dane_state_t s" 12
A DANE state structure (may be NULL)
.IP "gnutls_session_t session" 12
A gnutls session
.IP "const char * hostname" 12
The hostname associated with the chain
.IP "const char * proto" 12
The protocol of the service connecting (e.g. tcp)
.IP "unsigned int port" 12
The port of the service connecting (e.g. 443)
.IP "unsigned int sflags" 12
Flags for the initialization of  \fIs\fP (if NULL)
.IP "unsigned int vflags" 12
Verification flags; an OR'ed list of \fBdane_verify_flags_t\fP.
.IP "unsigned int * verify" 12
An OR'ed list of \fBdane_verify_status_t\fP.
.SH "DESCRIPTION"
This function will verify session's certificate chain against the
CA constrains and/or the certificate available via DANE.
See \fBdane_verify_crt()\fP for more information.

This will not verify the chain for validity; unless the DANE
verification is restricted to end certificates, this must be
be performed separately using \fBgnutls_certificate_verify_peers3()\fP.
.SH "RETURNS"
a negative error code on error and \fBDANE_E_SUCCESS\fP (0)
when the DANE entries were successfully parsed, irrespective of
whether they were verified (see  \fIverify\fP for that information). If
no usable entries were encountered \fBDANE_E_REQUESTED_DATA_NOT_AVAILABLE\fP
will be returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
