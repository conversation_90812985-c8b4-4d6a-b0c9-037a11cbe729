.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_init \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_init(unsigned int " flags ", const char * " deprecated_config_file ");"
.SH ARGUMENTS
.IP "unsigned int flags" 12
An ORed sequence of \fBGNUTLS_PKCS11_FLAG_\fP*
.IP "const char * deprecated_config_file" 12
either NULL or the location of a deprecated
configuration file
.SH "DESCRIPTION"
This function will initialize the PKCS 11 subsystem in gnutls. It will
read configuration files if \fBGNUTLS_PKCS11_FLAG_AUTO\fP is used or allow
you to independently load PKCS 11 modules using \fBgnutls_pkcs11_add_provider()\fP
if \fBGNUTLS_PKCS11_FLAG_MANUAL\fP is specified.

You don't need to call this function since GnuTLS 3.3.0 because it is being called
during the first request PKCS 11 operation. That call will assume the \fBGNUTLS_PKCS11_FLAG_AUTO\fP
flag. If another flags are required then it must be called independently
prior to any PKCS 11 operation.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
