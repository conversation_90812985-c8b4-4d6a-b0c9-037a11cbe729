<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>PKCS#11 Configuration: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="index.html" title="p11-kit">
<link rel="prev" href="index.html" title="p11-kit">
<link rel="next" href="config-example.html" title="Example">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><img src="up-insensitive.png" width="16" height="16" border="0"></td>
<td><a accesskey="p" href="index.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="config-example.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="chapter">
<div class="titlepage"><div><div><h1 class="title">
<a name="config"></a>PKCS#11 Configuration</h1></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="config.html#config-introduction">Consistent configuration</a></span></dt>
<dt><span class="section"><a href="config-example.html">Example</a></span></dt>
<dt><span class="section"><a href="config-files.html">Configuration Files</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="config-introduction"></a>Consistent configuration</h2></div></div></div>
<p>In order for multiple applications on the user's desktop to use
			PKCS#11 modules in a consistent manner, there must be a configuration
			or registry to specify which modules to load and how to use them. The
			PKCS#11 specification does not specify such a configuration standard.
		</p>
<p>Because of the multi-library module initialization problem, use of
			PKCS#11 modules must be coordinated within an application. p11-kit
			provides that coordination. Since coordination is required, it follows
			that p11-kit can also implement a consistent module configuration.
		</p>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>