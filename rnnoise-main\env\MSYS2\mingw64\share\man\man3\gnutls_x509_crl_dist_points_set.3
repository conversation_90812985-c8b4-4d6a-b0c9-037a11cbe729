.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_dist_points_set" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_dist_points_set \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_crl_dist_points_set(gnutls_x509_crl_dist_points_t " cdp ", gnutls_x509_subject_alt_name_t " type ", const gnutls_datum_t * " san ", unsigned int " reasons ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_dist_points_t cdp" 12
The CRL distribution points
.IP "gnutls_x509_subject_alt_name_t type" 12
The type of the name (of \fBgnutls_subject_alt_names_t\fP)
.IP "const gnutls_datum_t * san" 12
The point name data
.IP "unsigned int reasons" 12
Revocation reasons. An ORed sequence of flags from \fBgnutls_x509_crl_reason_flags_t\fP.
.SH "DESCRIPTION"
This function will store the specified CRL distribution point value
the  \fIcdp\fP type.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0), otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
