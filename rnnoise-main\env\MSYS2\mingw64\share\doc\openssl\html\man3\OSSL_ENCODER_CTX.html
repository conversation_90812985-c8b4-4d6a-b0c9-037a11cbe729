<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_ENCODER_CTX</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Functions">Functions</a></li>
      <li><a href="#Constructor">Constructor</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_ENCODER_CTX, OSSL_ENCODER_CTX_new, OSSL_ENCODER_settable_ctx_params, OSSL_ENCODER_CTX_set_params, OSSL_ENCODER_CTX_free, OSSL_ENCODER_CTX_set_selection, OSSL_ENCODER_CTX_set_output_type, OSSL_ENCODER_CTX_set_output_structure, OSSL_ENCODER_CTX_add_encoder, OSSL_ENCODER_CTX_add_extra, OSSL_ENCODER_CTX_get_num_encoders, OSSL_ENCODER_INSTANCE, OSSL_ENCODER_INSTANCE_get_encoder, OSSL_ENCODER_INSTANCE_get_encoder_ctx, OSSL_ENCODER_INSTANCE_get_output_type, OSSL_ENCODER_INSTANCE_get_output_structure, OSSL_ENCODER_CONSTRUCT, OSSL_ENCODER_CLEANUP, OSSL_ENCODER_CTX_set_construct, OSSL_ENCODER_CTX_set_construct_data, OSSL_ENCODER_CTX_set_cleanup - Encoder context routines</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/encoder.h&gt;

typedef struct ossl_encoder_ctx_st OSSL_ENCODER_CTX;

OSSL_ENCODER_CTX *OSSL_ENCODER_CTX_new();
const OSSL_PARAM *OSSL_ENCODER_settable_ctx_params(OSSL_ENCODER *encoder);
int OSSL_ENCODER_CTX_set_params(OSSL_ENCODER_CTX *ctx,
                                const OSSL_PARAM params[]);
void OSSL_ENCODER_CTX_free(OSSL_ENCODER_CTX *ctx);

int OSSL_ENCODER_CTX_set_selection(OSSL_ENCODER_CTX *ctx, int selection);
int OSSL_ENCODER_CTX_set_output_type(OSSL_ENCODER_CTX *ctx,
                                     const char *output_type);
int OSSL_ENCODER_CTX_set_output_structure(OSSL_ENCODER_CTX *ctx,
                                          const char *output_structure);

int OSSL_ENCODER_CTX_add_encoder(OSSL_ENCODER_CTX *ctx, OSSL_ENCODER *encoder);
int OSSL_ENCODER_CTX_add_extra(OSSL_ENCODER_CTX *ctx,
                               OSSL_LIB_CTX *libctx, const char *propq);
int OSSL_ENCODER_CTX_get_num_encoders(OSSL_ENCODER_CTX *ctx);

typedef struct ossl_encoder_instance_st OSSL_ENCODER_INSTANCE;
OSSL_ENCODER *
OSSL_ENCODER_INSTANCE_get_encoder(OSSL_ENCODER_INSTANCE *encoder_inst);
void *
OSSL_ENCODER_INSTANCE_get_encoder_ctx(OSSL_ENCODER_INSTANCE *encoder_inst);
const char *
OSSL_ENCODER_INSTANCE_get_output_type(OSSL_ENCODER_INSTANCE *encoder_inst);
const char *
OSSL_ENCODER_INSTANCE_get_output_structure(OSSL_ENCODER_INSTANCE *encoder_inst);

typedef const void *OSSL_ENCODER_CONSTRUCT(OSSL_ENCODER_INSTANCE *encoder_inst,
                                           void *construct_data);
typedef void OSSL_ENCODER_CLEANUP(void *construct_data);

int OSSL_ENCODER_CTX_set_construct(OSSL_ENCODER_CTX *ctx,
                                   OSSL_ENCODER_CONSTRUCT *construct);
int OSSL_ENCODER_CTX_set_construct_data(OSSL_ENCODER_CTX *ctx,
                                        void *construct_data);
int OSSL_ENCODER_CTX_set_cleanup(OSSL_ENCODER_CTX *ctx,
                                 OSSL_ENCODER_CLEANUP *cleanup);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Encoding an input object to the desired encoding may be done with a chain of encoder implementations, which means that the output from one encoder may be the input for the next in the chain. The <b>OSSL_ENCODER_CTX</b> holds all the data about these encoders. This allows having generic format encoders such as DER to PEM, as well as more specialized encoders like RSA to DER.</p>

<p>The final output type must be given, and a chain of encoders must end with an implementation that produces that output type.</p>

<p>At the beginning of the encoding process, a constructor provided by the caller is called to ensure that there is an appropriate provider-side object to start with. The constructor is set with OSSL_ENCODER_CTX_set_construct().</p>

<p><b>OSSL_ENCODER_INSTANCE</b> is an opaque structure that contains data about the encoder that is going to be used, and that may be useful for the constructor. There are some functions to extract data from this type, described in <a href="#Constructor">&quot;Constructor&quot;</a> below.</p>

<h2 id="Functions">Functions</h2>

<p>OSSL_ENCODER_CTX_new() creates a <b>OSSL_ENCODER_CTX</b>.</p>

<p>OSSL_ENCODER_settable_ctx_params() returns an <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array of parameter descriptors.</p>

<p>OSSL_ENCODER_CTX_set_params() attempts to set parameters specified with an <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array <i>params</i>. Parameters that the implementation doesn&#39;t recognise should be ignored.</p>

<p>OSSL_ENCODER_CTX_free() frees the given context <i>ctx</i>. If the argument is NULL, nothing is done.</p>

<p>OSSL_ENCODER_CTX_add_encoder() populates the <b>OSSL_ENCODER_CTX</b> <i>ctx</i> with a encoder, to be used to encode an input object.</p>

<p>OSSL_ENCODER_CTX_add_extra() finds encoders that further encodes output from already added encoders, and adds them as well. This is used to build encoder chains.</p>

<p>OSSL_ENCODER_CTX_set_output_type() sets the ending output type. This must be specified, and determines if a complete encoder chain is available.</p>

<p>OSSL_ENCODER_CTX_set_output_structure() sets the desired output structure. This may be used to determines what encoder implementations may be used. Depending on the type of object being encoded, the output structure may not be relevant.</p>

<p>OSSL_ENCODER_CTX_get_num_encoders() gets the number of encoders currently added to the context <i>ctx</i>.</p>

<p>OSSL_ENCODER_CTX_set_construct() sets the constructor <i>construct</i>.</p>

<p>OSSL_ENCODER_CTX_set_construct_data() sets the constructor data that is passed to the constructor every time it&#39;s called.</p>

<p>OSSL_ENCODER_CTX_set_cleanup() sets the constructor data <i>cleanup</i> function. This is called by <a href="../man3/OSSL_ENCODER_CTX_free.html">OSSL_ENCODER_CTX_free(3)</a>.</p>

<h2 id="Constructor">Constructor</h2>

<p>A <b>OSSL_ENCODER_CONSTRUCT</b> gets the following arguments:</p>

<dl>

<dt id="encoder_inst"><i>encoder_inst</i></dt>
<dd>

<p>The <b>OSSL_ENCODER_INSTANCE</b> for the encoder from which the constructor gets its data.</p>

</dd>
<dt id="construct_data"><i>construct_data</i></dt>
<dd>

<p>The pointer that was set with OSSL_ENCODE_CTX_set_construct_data().</p>

</dd>
</dl>

<p>The constructor is expected to return a valid (non-NULL) pointer to a provider-native object that can be used as first input of an encoding chain, or NULL to indicate that an error has occurred.</p>

<p>These utility functions may be used by a constructor:</p>

<p>OSSL_ENCODER_INSTANCE_get_encoder() can be used to get the encoder implementation of the encoder instance <i>encoder_inst</i>.</p>

<p>OSSL_ENCODER_INSTANCE_get_encoder_ctx() can be used to get the encoder implementation&#39;s provider context of the encoder instance <i>encoder_inst</i>.</p>

<p>OSSL_ENCODER_INSTANCE_get_output_type() can be used to get the output type for the encoder implementation of the encoder instance <i>encoder_inst</i>. This will never be NULL.</p>

<p>OSSL_ENCODER_INSTANCE_get_output_structure() can be used to get the output structure for the encoder implementation of the encoder instance <i>encoder_inst</i>. This may be NULL.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_ENCODER_CTX_new() returns a pointer to a <b>OSSL_ENCODER_CTX</b>, or NULL if the context structure couldn&#39;t be allocated.</p>

<p>OSSL_ENCODER_settable_ctx_params() returns an <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array, or NULL if none is available.</p>

<p>OSSL_ENCODER_CTX_set_params() returns 1 if all recognised parameters were valid, or 0 if one of them was invalid or caused some other failure in the implementation.</p>

<p>OSSL_ENCODER_CTX_add_encoder(), OSSL_ENCODER_CTX_add_extra(), OSSL_ENCODER_CTX_set_construct(), OSSL_ENCODER_CTX_set_construct_data() and OSSL_ENCODER_CTX_set_cleanup() return 1 on success, or 0 on failure.</p>

<p>OSSL_ENCODER_CTX_get_num_encoders() returns the current number of encoders. It returns 0 if <i>ctx</i> is NULL.</p>

<p>OSSL_ENCODER_INSTANCE_get_encoder() returns an <b>OSSL_ENCODER</b> pointer on success, or NULL on failure.</p>

<p>OSSL_ENCODER_INSTANCE_get_encoder_ctx() returns a provider context pointer on success, or NULL on failure.</p>

<p>OSSL_ENCODER_INSTANCE_get_output_type() returns a string with the name of the input type, if relevant. NULL is a valid returned value.</p>

<p>OSSL_ENCODER_INSTANCE_get_output_type() returns a string with the name of the output type.</p>

<p>OSSL_ENCODER_INSTANCE_get_output_structure() returns a string with the name of the output structure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider.html">provider(7)</a>, <a href="../man3/OSSL_ENCODER.html">OSSL_ENCODER(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The functions described here were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


