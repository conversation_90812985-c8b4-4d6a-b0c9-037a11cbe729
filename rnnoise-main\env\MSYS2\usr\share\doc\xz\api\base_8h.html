<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/base.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('base_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">base.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Data types and functions used in many places in liblzma API.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__allocator.html">lzma_allocator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Custom functions for memory handling.  <a href="structlzma__allocator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__stream.html">lzma_stream</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Passing data to and from liblzma.  <a href="structlzma__stream.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:af31f0c8b6f14359cd082b9559f7f3e01" id="r_af31f0c8b6f14359cd082b9559f7f3e01"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af31f0c8b6f14359cd082b9559f7f3e01">LZMA_STREAM_INIT</a></td></tr>
<tr class="memdesc:af31f0c8b6f14359cd082b9559f7f3e01"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization for <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a>.  <br /></td></tr>
<tr class="separator:af31f0c8b6f14359cd082b9559f7f3e01"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:abbc819c74b484c846825ae1388a50a59" id="r_abbc819c74b484c846825ae1388a50a59"><td class="memItemLeft" align="right" valign="top">typedef unsigned char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abbc819c74b484c846825ae1388a50a59">lzma_bool</a></td></tr>
<tr class="memdesc:abbc819c74b484c846825ae1388a50a59"><td class="mdescLeft">&#160;</td><td class="mdescRight">Boolean.  <br /></td></tr>
<tr class="separator:abbc819c74b484c846825ae1388a50a59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1a60127c640135687a5bcc232cec906" id="r_ab1a60127c640135687a5bcc232cec906"><td class="memItemLeft" align="right" valign="top">typedef struct lzma_internal_s&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab1a60127c640135687a5bcc232cec906">lzma_internal</a></td></tr>
<tr class="memdesc:ab1a60127c640135687a5bcc232cec906"><td class="mdescLeft">&#160;</td><td class="mdescRight">Internal data structure.  <br /></td></tr>
<tr class="separator:ab1a60127c640135687a5bcc232cec906"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a05805a07754b2aa22f7d443eb7ece41a" id="r_a05805a07754b2aa22f7d443eb7ece41a"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a05805a07754b2aa22f7d443eb7ece41a">lzma_reserved_enum</a> { <b>LZMA_RESERVED_ENUM</b> = 0
 }</td></tr>
<tr class="memdesc:a05805a07754b2aa22f7d443eb7ece41a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type of reserved enumeration variable in structures.  <a href="#a05805a07754b2aa22f7d443eb7ece41a">More...</a><br /></td></tr>
<tr class="separator:a05805a07754b2aa22f7d443eb7ece41a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8494e0457e1463d6d2b6836018d87b6e" id="r_a8494e0457e1463d6d2b6836018d87b6e"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> { <br />
&#160;&#160;<a class="el" href="#a8494e0457e1463d6d2b6836018d87b6eac003781ccb81bbd5578e29abed8a8cfe">LZMA_OK</a> = 0
, <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6ea91ecc6fab14c13ad36224afbcb4e55c4">LZMA_STREAM_END</a> = 1
, <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6eaa720d30092d504d7d138a320db1905ef">LZMA_NO_CHECK</a> = 2
, <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6ea989f393a1772d85bf545a9da48fc7ac2">LZMA_UNSUPPORTED_CHECK</a> = 3
, <br />
&#160;&#160;<a class="el" href="#a8494e0457e1463d6d2b6836018d87b6eaa5b648c18da0f584f621cfdf7fef1bdb">LZMA_GET_CHECK</a> = 4
, <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6ea567e1464feca03900a5425fb45b2f5b6">LZMA_MEM_ERROR</a> = 5
, <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6eaa1d705effe6026f32c0fe9756b6326bc">LZMA_MEMLIMIT_ERROR</a> = 6
, <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6ea63b7a58949854eb9307f8e351358d56c">LZMA_FORMAT_ERROR</a> = 7
, <br />
&#160;&#160;<a class="el" href="#a8494e0457e1463d6d2b6836018d87b6eaa9ff6dfee36b7aba4fae60706d37425f">LZMA_OPTIONS_ERROR</a> = 8
, <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6ea3aa72729a844790e39b4e1101a731dfb">LZMA_DATA_ERROR</a> = 9
, <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6ea9ca0ecb62459bdc84d6af47d16b23ae5">LZMA_BUF_ERROR</a> = 10
, <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6ea2dac8d451cb38da8550653d0d7be4ec2">LZMA_PROG_ERROR</a> = 11
, <br />
&#160;&#160;<a class="el" href="#a8494e0457e1463d6d2b6836018d87b6ea6cf28e5345851f13bd798a4eab8cc939">LZMA_SEEK_NEEDED</a> = 12
, <b>LZMA_RET_INTERNAL1</b> = 101
, <b>LZMA_RET_INTERNAL2</b> = 102
, <b>LZMA_RET_INTERNAL3</b> = 103
, <br />
&#160;&#160;<b>LZMA_RET_INTERNAL4</b> = 104
, <b>LZMA_RET_INTERNAL5</b> = 105
, <b>LZMA_RET_INTERNAL6</b> = 106
, <b>LZMA_RET_INTERNAL7</b> = 107
, <br />
&#160;&#160;<b>LZMA_RET_INTERNAL8</b> = 108
<br />
 }</td></tr>
<tr class="memdesc:a8494e0457e1463d6d2b6836018d87b6e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return values used by several functions in liblzma.  <a href="#a8494e0457e1463d6d2b6836018d87b6e">More...</a><br /></td></tr>
<tr class="separator:a8494e0457e1463d6d2b6836018d87b6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa92efcbf3cecfcac79c81fc645fce77e" id="r_aa92efcbf3cecfcac79c81fc645fce77e"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77e">lzma_action</a> { <br />
&#160;&#160;<a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77ea868472b76492afcaef54020a481890b1">LZMA_RUN</a> = 0
, <a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77ea14d75152afcda85d215e877fdd9c4170">LZMA_SYNC_FLUSH</a> = 1
, <a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77eaab46f0d7c721f1ec377e9575eab2586f">LZMA_FULL_FLUSH</a> = 2
, <a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77eaf7bf60e3555a4d10ffad3ecc3d2e01f1">LZMA_FULL_BARRIER</a> = 4
, <br />
&#160;&#160;<a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77ea7d24fb3c6c144d13bcb091195b8ebec1">LZMA_FINISH</a> = 3
<br />
 }</td></tr>
<tr class="memdesc:aa92efcbf3cecfcac79c81fc645fce77e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The 'action' argument for <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>  <a href="#aa92efcbf3cecfcac79c81fc645fce77e">More...</a><br /></td></tr>
<tr class="separator:aa92efcbf3cecfcac79c81fc645fce77e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a28cc09bc422d5ba1e0187c9f2af5d957" id="r_a28cc09bc422d5ba1e0187c9f2af5d957"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957">lzma_code</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, <a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77e">lzma_action</a> action) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a28cc09bc422d5ba1e0187c9f2af5d957"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encode or decode data.  <br /></td></tr>
<tr class="separator:a28cc09bc422d5ba1e0187c9f2af5d957"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a854ff37464ae1225febf14db1af43308" id="r_a854ff37464ae1225febf14db1af43308"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a854ff37464ae1225febf14db1af43308">lzma_end</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm) lzma_nothrow</td></tr>
<tr class="memdesc:a854ff37464ae1225febf14db1af43308"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free memory allocated for the coder data structures.  <br /></td></tr>
<tr class="separator:a854ff37464ae1225febf14db1af43308"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6447cd68eeecbd6b88f21daeb8ce751" id="r_ab6447cd68eeecbd6b88f21daeb8ce751"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab6447cd68eeecbd6b88f21daeb8ce751">lzma_get_progress</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, uint64_t *progress_in, uint64_t *progress_out) lzma_nothrow</td></tr>
<tr class="memdesc:ab6447cd68eeecbd6b88f21daeb8ce751"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get progress information.  <br /></td></tr>
<tr class="separator:ab6447cd68eeecbd6b88f21daeb8ce751"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a418b210cf206782a73cd9de7dc27f670" id="r_a418b210cf206782a73cd9de7dc27f670"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a418b210cf206782a73cd9de7dc27f670">lzma_memusage</a> (const <a class="el" href="structlzma__stream.html">lzma_stream</a> *strm) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a418b210cf206782a73cd9de7dc27f670"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the memory usage of decoder filter chain.  <br /></td></tr>
<tr class="separator:a418b210cf206782a73cd9de7dc27f670"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac871bc2ead5d482c6d6b3d51bfec365c" id="r_ac871bc2ead5d482c6d6b3d51bfec365c"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac871bc2ead5d482c6d6b3d51bfec365c">lzma_memlimit_get</a> (const <a class="el" href="structlzma__stream.html">lzma_stream</a> *strm) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:ac871bc2ead5d482c6d6b3d51bfec365c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the current memory usage limit.  <br /></td></tr>
<tr class="separator:ac871bc2ead5d482c6d6b3d51bfec365c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc49d4cf75b73128a167df3407505f7b" id="r_afc49d4cf75b73128a167df3407505f7b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afc49d4cf75b73128a167df3407505f7b">lzma_memlimit_set</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, uint64_t memlimit) lzma_nothrow</td></tr>
<tr class="memdesc:afc49d4cf75b73128a167df3407505f7b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the memory usage limit.  <br /></td></tr>
<tr class="separator:afc49d4cf75b73128a167df3407505f7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Data types and functions used in many places in liblzma API. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="af31f0c8b6f14359cd082b9559f7f3e01" name="af31f0c8b6f14359cd082b9559f7f3e01"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af31f0c8b6f14359cd082b9559f7f3e01">&#9670;&#160;</a></span>LZMA_STREAM_INIT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_STREAM_INIT</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    { NULL, 0, 0, NULL, 0, 0, NULL, NULL, \</div>
<div class="line">    NULL, NULL, NULL, NULL, 0, 0, 0, 0, \</div>
<div class="line">    LZMA_RESERVED_ENUM, LZMA_RESERVED_ENUM }</div>
</div><!-- fragment -->
<p>Initialization for <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a>. </p>
<p>When you declare an instance of <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a>, you can immediately initialize it so that initialization functions know that no memory has been allocated yet: </p><pre class="fragment">lzma_stream strm = LZMA_STREAM_INIT;
</pre><p>If you need to initialize a dynamically allocated <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a>, you can use memset(strm_pointer, 0, sizeof(lzma_stream)). Strictly speaking, this violates the C standard since NULL may have different internal representation than zero, but it should be portable enough in practice. Anyway, for maximum portability, you can use something like this: </p><pre class="fragment">lzma_stream tmp = LZMA_STREAM_INIT;
*strm = tmp;
</pre> 
</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="abbc819c74b484c846825ae1388a50a59" name="abbc819c74b484c846825ae1388a50a59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abbc819c74b484c846825ae1388a50a59">&#9670;&#160;</a></span>lzma_bool</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef unsigned char <a class="el" href="#abbc819c74b484c846825ae1388a50a59">lzma_bool</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Boolean. </p>
<p>This is here because C89 doesn't have stdbool.h. To set a value for variables having type <a class="el" href="#abbc819c74b484c846825ae1388a50a59" title="Boolean.">lzma_bool</a>, you can use</p><ul>
<li>C99's 'true' and 'false' from stdbool.h;</li>
<li>C++'s internal 'true' and 'false'; or</li>
<li>integers one (true) and zero (false). </li>
</ul>

</div>
</div>
<a id="ab1a60127c640135687a5bcc232cec906" name="ab1a60127c640135687a5bcc232cec906"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab1a60127c640135687a5bcc232cec906">&#9670;&#160;</a></span>lzma_internal</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct lzma_internal_s <a class="el" href="#ab1a60127c640135687a5bcc232cec906">lzma_internal</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Internal data structure. </p>
<p>The contents of this structure is not visible outside the library. </p>

</div>
</div>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="a05805a07754b2aa22f7d443eb7ece41a" name="a05805a07754b2aa22f7d443eb7ece41a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a05805a07754b2aa22f7d443eb7ece41a">&#9670;&#160;</a></span>lzma_reserved_enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a05805a07754b2aa22f7d443eb7ece41a">lzma_reserved_enum</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Type of reserved enumeration variable in structures. </p>
<p>To avoid breaking library ABI when new features are added, several structures contain extra variables that may be used in future. Since sizeof(enum) can be different than sizeof(int), and sizeof(enum) may even vary depending on the range of enumeration constants, we specify a separate type to be used for reserved enumeration variables. All enumeration constants in liblzma API will be non-negative and less than 128, which should guarantee that the ABI won't break even when new constants are added to existing enumerations. </p>

</div>
</div>
<a id="a8494e0457e1463d6d2b6836018d87b6e" name="a8494e0457e1463d6d2b6836018d87b6e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8494e0457e1463d6d2b6836018d87b6e">&#9670;&#160;</a></span>lzma_ret</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return values used by several functions in liblzma. </p>
<p>Check the descriptions of specific functions to find out which return values they can return. With some functions the return values may have more specific meanings than described here; those differences are described per-function basis. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6eac003781ccb81bbd5578e29abed8a8cfe" name="a8494e0457e1463d6d2b6836018d87b6eac003781ccb81bbd5578e29abed8a8cfe"></a>LZMA_OK&#160;</td><td class="fielddoc"><p>Operation completed successfully. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6ea91ecc6fab14c13ad36224afbcb4e55c4" name="a8494e0457e1463d6d2b6836018d87b6ea91ecc6fab14c13ad36224afbcb4e55c4"></a>LZMA_STREAM_END&#160;</td><td class="fielddoc"><p>End of stream was reached. </p>
<p>In encoder, LZMA_SYNC_FLUSH, LZMA_FULL_FLUSH, or LZMA_FINISH was finished. In decoder, this indicates that all the data was successfully decoded.</p>
<p>In all cases, when LZMA_STREAM_END is returned, the last output bytes should be picked from strm-&gt;next_out. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6eaa720d30092d504d7d138a320db1905ef" name="a8494e0457e1463d6d2b6836018d87b6eaa720d30092d504d7d138a320db1905ef"></a>LZMA_NO_CHECK&#160;</td><td class="fielddoc"><p>Input stream has no integrity check. </p>
<p>This return value can be returned only if the LZMA_TELL_NO_CHECK flag was used when initializing the decoder. LZMA_NO_CHECK is just a warning, and the decoding can be continued normally.</p>
<p>It is possible to call <a class="el" href="check_8h.html#a8d7c3ffabfd024485f03fa209536c746" title="Get the type of the integrity check.">lzma_get_check()</a> immediately after lzma_code has returned LZMA_NO_CHECK. The result will naturally be LZMA_CHECK_NONE, but the possibility to call <a class="el" href="check_8h.html#a8d7c3ffabfd024485f03fa209536c746" title="Get the type of the integrity check.">lzma_get_check()</a> may be convenient in some applications. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6ea989f393a1772d85bf545a9da48fc7ac2" name="a8494e0457e1463d6d2b6836018d87b6ea989f393a1772d85bf545a9da48fc7ac2"></a>LZMA_UNSUPPORTED_CHECK&#160;</td><td class="fielddoc"><p>Cannot calculate the integrity check. </p>
<p>The usage of this return value is different in encoders and decoders.</p>
<p>Encoders can return this value only from the initialization function. If initialization fails with this value, the encoding cannot be done, because there's no way to produce output with the correct integrity check.</p>
<p>Decoders can return this value only from <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> and only if the LZMA_TELL_UNSUPPORTED_CHECK flag was used when initializing the decoder. The decoding can still be continued normally even if the check type is unsupported, but naturally the check will not be validated, and possible errors may go undetected.</p>
<p>With decoder, it is possible to call <a class="el" href="check_8h.html#a8d7c3ffabfd024485f03fa209536c746" title="Get the type of the integrity check.">lzma_get_check()</a> immediately after <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> has returned LZMA_UNSUPPORTED_CHECK. This way it is possible to find out what the unsupported Check ID was. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6eaa5b648c18da0f584f621cfdf7fef1bdb" name="a8494e0457e1463d6d2b6836018d87b6eaa5b648c18da0f584f621cfdf7fef1bdb"></a>LZMA_GET_CHECK&#160;</td><td class="fielddoc"><p>Integrity check type is now available. </p>
<p>This value can be returned only by the <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> function and only if the decoder was initialized with the LZMA_TELL_ANY_CHECK flag. LZMA_GET_CHECK tells the application that it may now call <a class="el" href="check_8h.html#a8d7c3ffabfd024485f03fa209536c746" title="Get the type of the integrity check.">lzma_get_check()</a> to find out the Check ID. This can be used, for example, to implement a decoder that accepts only files that have strong enough integrity check. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6ea567e1464feca03900a5425fb45b2f5b6" name="a8494e0457e1463d6d2b6836018d87b6ea567e1464feca03900a5425fb45b2f5b6"></a>LZMA_MEM_ERROR&#160;</td><td class="fielddoc"><p>Cannot allocate memory. </p>
<p>Memory allocation failed, or the size of the allocation would be greater than SIZE_MAX.</p>
<p>Due to internal implementation reasons, the coding cannot be continued even if more memory were made available after LZMA_MEM_ERROR. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6eaa1d705effe6026f32c0fe9756b6326bc" name="a8494e0457e1463d6d2b6836018d87b6eaa1d705effe6026f32c0fe9756b6326bc"></a>LZMA_MEMLIMIT_ERROR&#160;</td><td class="fielddoc"><p>Memory usage limit was reached. </p>
<p>Decoder would need more memory than allowed by the specified memory usage limit. To continue decoding, the memory usage limit has to be increased with <a class="el" href="#afc49d4cf75b73128a167df3407505f7b" title="Set the memory usage limit.">lzma_memlimit_set()</a>.</p>
<p>liblzma 5.2.6 and earlier had a bug in single-threaded .xz decoder (<a class="el" href="container_8h.html#a02b7683ef98d8049788961370a8b28c0" title="Initialize .xz Stream decoder.">lzma_stream_decoder()</a>) which made it impossible to continue decoding after LZMA_MEMLIMIT_ERROR even if the limit was increased using <a class="el" href="#afc49d4cf75b73128a167df3407505f7b" title="Set the memory usage limit.">lzma_memlimit_set()</a>. Other decoders worked correctly. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6ea63b7a58949854eb9307f8e351358d56c" name="a8494e0457e1463d6d2b6836018d87b6ea63b7a58949854eb9307f8e351358d56c"></a>LZMA_FORMAT_ERROR&#160;</td><td class="fielddoc"><p>File format not recognized. </p>
<p>The decoder did not recognize the input as supported file format. This error can occur, for example, when trying to decode .lzma format file with lzma_stream_decoder, because lzma_stream_decoder accepts only the .xz format. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6eaa9ff6dfee36b7aba4fae60706d37425f" name="a8494e0457e1463d6d2b6836018d87b6eaa9ff6dfee36b7aba4fae60706d37425f"></a>LZMA_OPTIONS_ERROR&#160;</td><td class="fielddoc"><p>Invalid or unsupported options. </p>
<p>Invalid or unsupported options, for example</p><ul>
<li>unsupported filter(s) or filter options; or</li>
<li>reserved bits set in headers (decoder only).</li>
</ul>
<p>Rebuilding liblzma with more features enabled, or upgrading to a newer version of liblzma may help. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6ea3aa72729a844790e39b4e1101a731dfb" name="a8494e0457e1463d6d2b6836018d87b6ea3aa72729a844790e39b4e1101a731dfb"></a>LZMA_DATA_ERROR&#160;</td><td class="fielddoc"><p>Data is corrupt. </p>
<p>The usage of this return value is different in encoders and decoders. In both encoder and decoder, the coding cannot continue after this error.</p>
<p>Encoders return this if size limits of the target file format would be exceeded. These limits are huge, thus getting this error from an encoder is mostly theoretical. For example, the maximum compressed and uncompressed size of a .xz Stream is roughly 8 EiB (2^63 bytes).</p>
<p>Decoders return this error if the input data is corrupt. This can mean, for example, invalid CRC32 in headers or invalid check of uncompressed data. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6ea9ca0ecb62459bdc84d6af47d16b23ae5" name="a8494e0457e1463d6d2b6836018d87b6ea9ca0ecb62459bdc84d6af47d16b23ae5"></a>LZMA_BUF_ERROR&#160;</td><td class="fielddoc"><p>No progress is possible. </p>
<p>This error code is returned when the coder cannot consume any new input and produce any new output. The most common reason for this error is that the input stream being decoded is truncated or corrupt.</p>
<p>This error is not fatal. Coding can be continued normally by providing more input and/or more output space, if possible.</p>
<p>Typically the first call to <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> that can do no progress returns LZMA_OK instead of LZMA_BUF_ERROR. Only the second consecutive call doing no progress will return LZMA_BUF_ERROR. This is intentional.</p>
<p>With zlib, Z_BUF_ERROR may be returned even if the application is doing nothing wrong, so apps will need to handle Z_BUF_ERROR specially. The above hack guarantees that liblzma never returns LZMA_BUF_ERROR to properly written applications unless the input file is truncated or corrupt. This should simplify the applications a little. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6ea2dac8d451cb38da8550653d0d7be4ec2" name="a8494e0457e1463d6d2b6836018d87b6ea2dac8d451cb38da8550653d0d7be4ec2"></a>LZMA_PROG_ERROR&#160;</td><td class="fielddoc"><p>Programming error. </p>
<p>This indicates that the arguments given to the function are invalid or the internal state of the decoder is corrupt.</p><ul>
<li>Function arguments are invalid or the structures pointed by the argument pointers are invalid e.g. if strm-&gt;next_out has been set to NULL and strm-&gt;avail_out &gt; 0 when calling <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>.</li>
<li>lzma_* functions have been called in wrong order e.g. <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> was called right after <a class="el" href="#a854ff37464ae1225febf14db1af43308" title="Free memory allocated for the coder data structures.">lzma_end()</a>.</li>
<li>If errors occur randomly, the reason might be flaky hardware.</li>
</ul>
<p>If you think that your code is correct, this error code can be a sign of a bug in liblzma. See the documentation how to report bugs. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8494e0457e1463d6d2b6836018d87b6ea6cf28e5345851f13bd798a4eab8cc939" name="a8494e0457e1463d6d2b6836018d87b6ea6cf28e5345851f13bd798a4eab8cc939"></a>LZMA_SEEK_NEEDED&#160;</td><td class="fielddoc"><p>Request to change the input file position. </p>
<p>Some coders can do random access in the input file. The initialization functions of these coders take the file size as an argument. No other coders can return LZMA_SEEK_NEEDED.</p>
<p>When this value is returned, the application must seek to the file position given in <a class="el" href="structlzma__stream.html#af7c43a61f3dfeb0b9c8487b7f275054e" title="New seek input position for LZMA_SEEK_NEEDED.">lzma_stream.seek_pos</a>. This value is guaranteed to never exceed the file size that was specified at the coder initialization.</p>
<p>After seeking the application should read new input and pass it normally via <a class="el" href="structlzma__stream.html#a72fdc738c793f07a5c29715aa57802cf">lzma_stream.next_in</a> and .avail_in. </p>
</td></tr>
</table>

</div>
</div>
<a id="aa92efcbf3cecfcac79c81fc645fce77e" name="aa92efcbf3cecfcac79c81fc645fce77e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa92efcbf3cecfcac79c81fc645fce77e">&#9670;&#160;</a></span>lzma_action</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77e">lzma_action</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The 'action' argument for <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> </p>
<p>After the first use of LZMA_SYNC_FLUSH, LZMA_FULL_FLUSH, LZMA_FULL_BARRIER, or LZMA_FINISH, the same 'action' must be used until <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> returns LZMA_STREAM_END. Also, the amount of input (that is, strm-&gt;avail_in) must not be modified by the application until <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> returns LZMA_STREAM_END. Changing the 'action' or modifying the amount of input will make <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> return LZMA_PROG_ERROR. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="aa92efcbf3cecfcac79c81fc645fce77ea868472b76492afcaef54020a481890b1" name="aa92efcbf3cecfcac79c81fc645fce77ea868472b76492afcaef54020a481890b1"></a>LZMA_RUN&#160;</td><td class="fielddoc"><p>Continue coding. </p>
<p>Encoder: Encode as much input as possible. Some internal buffering will probably be done (depends on the filter chain in use), which causes latency: the input used won't usually be decodeable from the output of the same <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> call.</p>
<p>Decoder: Decode as much input as possible and produce as much output as possible. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa92efcbf3cecfcac79c81fc645fce77ea14d75152afcda85d215e877fdd9c4170" name="aa92efcbf3cecfcac79c81fc645fce77ea14d75152afcda85d215e877fdd9c4170"></a>LZMA_SYNC_FLUSH&#160;</td><td class="fielddoc"><p>Make all the input available at output. </p>
<p>Normally the encoder introduces some latency. LZMA_SYNC_FLUSH forces all the buffered data to be available at output without resetting the internal state of the encoder. This way it is possible to use compressed stream for example for communication over network.</p>
<p>Only some filters support LZMA_SYNC_FLUSH. Trying to use LZMA_SYNC_FLUSH with filters that don't support it will make <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> return LZMA_OPTIONS_ERROR. For example, LZMA1 doesn't support LZMA_SYNC_FLUSH but LZMA2 does.</p>
<p>Using LZMA_SYNC_FLUSH very often can dramatically reduce the compression ratio. With some filters (for example, LZMA2), fine-tuning the compression options may help mitigate this problem significantly (for example, match finder with LZMA2).</p>
<p>Decoders don't support LZMA_SYNC_FLUSH. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa92efcbf3cecfcac79c81fc645fce77eaab46f0d7c721f1ec377e9575eab2586f" name="aa92efcbf3cecfcac79c81fc645fce77eaab46f0d7c721f1ec377e9575eab2586f"></a>LZMA_FULL_FLUSH&#160;</td><td class="fielddoc"><p>Finish encoding of the current Block. </p>
<p>All the input data going to the current Block must have been given to the encoder (the last bytes can still be pending in *next_in). Call <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> with LZMA_FULL_FLUSH until it returns LZMA_STREAM_END. Then continue normally with LZMA_RUN or finish the Stream with LZMA_FINISH.</p>
<p>This action is currently supported only by Stream encoder and easy encoder (which uses Stream encoder). If there is no unfinished Block, no empty Block is created. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa92efcbf3cecfcac79c81fc645fce77eaf7bf60e3555a4d10ffad3ecc3d2e01f1" name="aa92efcbf3cecfcac79c81fc645fce77eaf7bf60e3555a4d10ffad3ecc3d2e01f1"></a>LZMA_FULL_BARRIER&#160;</td><td class="fielddoc"><p>Finish encoding of the current Block. </p>
<p>This is like LZMA_FULL_FLUSH except that this doesn't necessarily wait until all the input has been made available via the output buffer. That is, <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> might return LZMA_STREAM_END as soon as all the input has been consumed (avail_in == 0).</p>
<p>LZMA_FULL_BARRIER is useful with a threaded encoder if one wants to split the .xz Stream into Blocks at specific offsets but doesn't care if the output isn't flushed immediately. Using LZMA_FULL_BARRIER allows keeping the threads busy while LZMA_FULL_FLUSH would make <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> wait until all the threads have finished until more data could be passed to the encoder.</p>
<p>With a <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> initialized with the single-threaded <a class="el" href="container_8h.html#a1a97aec94c9fedd7646cfa51c4f4cd52" title="Initialize .xz Stream encoder using a custom filter chain.">lzma_stream_encoder()</a> or <a class="el" href="container_8h.html#acbdad999c544872f0f5d242f0d1a4ed4" title="Initialize .xz Stream encoder using a preset number.">lzma_easy_encoder()</a>, LZMA_FULL_BARRIER is an alias for LZMA_FULL_FLUSH. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa92efcbf3cecfcac79c81fc645fce77ea7d24fb3c6c144d13bcb091195b8ebec1" name="aa92efcbf3cecfcac79c81fc645fce77ea7d24fb3c6c144d13bcb091195b8ebec1"></a>LZMA_FINISH&#160;</td><td class="fielddoc"><p>Finish the coding operation. </p>
<p>All the input data must have been given to the encoder (the last bytes can still be pending in next_in). Call <a class="el" href="#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> with LZMA_FINISH until it returns LZMA_STREAM_END. Once LZMA_FINISH has been used, the amount of input must no longer be changed by the application.</p>
<p>When decoding, using LZMA_FINISH is optional unless the LZMA_CONCATENATED flag was used when the decoder was initialized. When LZMA_CONCATENATED was not used, the only effect of LZMA_FINISH is that the amount of input must not be changed just like in the encoder. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a28cc09bc422d5ba1e0187c9f2af5d957" name="a28cc09bc422d5ba1e0187c9f2af5d957"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a28cc09bc422d5ba1e0187c9f2af5d957">&#9670;&#160;</a></span>lzma_code()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_code </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77e">lzma_action</a></td>          <td class="paramname"><span class="paramname"><em>action</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Encode or decode data. </p>
<p>Once the <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> has been successfully initialized (e.g. with <a class="el" href="container_8h.html#a1a97aec94c9fedd7646cfa51c4f4cd52" title="Initialize .xz Stream encoder using a custom filter chain.">lzma_stream_encoder()</a>), the actual encoding or decoding is done using this function. The application has to update strm-&gt;next_in, strm-&gt;avail_in, strm-&gt;next_out, and strm-&gt;avail_out to pass input to and get output from liblzma.</p>
<p>See the description of the coder-specific initialization function to find out what 'action' values are supported by the coder.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">action</td><td>Action for this function to take. Must be a valid <a class="el" href="#aa92efcbf3cecfcac79c81fc645fce77e" title="The &#39;action&#39; argument for lzma_code()">lzma_action</a> enum value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Any valid <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a>. See the <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> enum description for more information. </dd></dl>

</div>
</div>
<a id="a854ff37464ae1225febf14db1af43308" name="a854ff37464ae1225febf14db1af43308"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a854ff37464ae1225febf14db1af43308">&#9670;&#160;</a></span>lzma_end()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void lzma_end </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Free memory allocated for the coder data structures. </p>
<p>After lzma_end(strm), strm-&gt;internal is guaranteed to be NULL. No other members of the <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> structure are touched.</p>
<dl class="section note"><dt>Note</dt><dd>zlib indicates an error if application end()s unfinished stream structure. liblzma doesn't do this, and assumes that application knows what it is doing.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ab6447cd68eeecbd6b88f21daeb8ce751" name="ab6447cd68eeecbd6b88f21daeb8ce751"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab6447cd68eeecbd6b88f21daeb8ce751">&#9670;&#160;</a></span>lzma_get_progress()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void lzma_get_progress </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *</td>          <td class="paramname"><span class="paramname"><em>progress_in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *</td>          <td class="paramname"><span class="paramname"><em>progress_out</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get progress information. </p>
<p>In single-threaded mode, applications can get progress information from strm-&gt;total_in and strm-&gt;total_out. In multi-threaded mode this is less useful because a significant amount of both input and output data gets buffered internally by liblzma. This makes total_in and total_out give misleading information and also makes the progress indicator updates non-smooth.</p>
<p>This function gives realistic progress information also in multi-threaded mode by taking into account the progress made by each thread. In single-threaded mode *progress_in and *progress_out are set to strm-&gt;total_in and strm-&gt;total_out, respectively.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">progress_in</td><td>Pointer to the number of input bytes processed. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">progress_out</td><td>Pointer to the number of output bytes processed. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a418b210cf206782a73cd9de7dc27f670" name="a418b210cf206782a73cd9de7dc27f670"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a418b210cf206782a73cd9de7dc27f670">&#9670;&#160;</a></span>lzma_memusage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_memusage </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the memory usage of decoder filter chain. </p>
<p>This function is currently supported only when *strm has been initialized with a function that takes a memlimit argument. With other functions, you should use e.g. <a class="el" href="filter_8h.html#a730f9391e85a5979bcd1b32643ae7176" title="Calculate approximate memory requirements for raw encoder.">lzma_raw_encoder_memusage()</a> or <a class="el" href="filter_8h.html#a58511249ae9206d7de7c5d1f05842297" title="Calculate approximate memory requirements for raw decoder.">lzma_raw_decoder_memusage()</a> to estimate the memory requirements.</p>
<p>This function is useful e.g. after LZMA_MEMLIMIT_ERROR to find out how big the memory usage limit should have been to decode the input. Note that this may give misleading information if decoding .xz Streams that have multiple Blocks, because each Block can have different memory requirements.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>How much memory is currently allocated for the filter decoders. If no filter chain is currently allocated, some non-zero value is still returned, which is less than or equal to what any filter chain would indicate as its memory requirement.</dd></dl>
<p>If this function isn't supported by *strm or some other error occurs, zero is returned. </p>

</div>
</div>
<a id="ac871bc2ead5d482c6d6b3d51bfec365c" name="ac871bc2ead5d482c6d6b3d51bfec365c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac871bc2ead5d482c6d6b3d51bfec365c">&#9670;&#160;</a></span>lzma_memlimit_get()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_memlimit_get </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the current memory usage limit. </p>
<p>This function is supported only when *strm has been initialized with a function that takes a memlimit argument.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>On success, the current memory usage limit is returned (always non-zero). On error, zero is returned. </dd></dl>

</div>
</div>
<a id="afc49d4cf75b73128a167df3407505f7b" name="afc49d4cf75b73128a167df3407505f7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afc49d4cf75b73128a167df3407505f7b">&#9670;&#160;</a></span>lzma_memlimit_set()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_memlimit_set </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t</td>          <td class="paramname"><span class="paramname"><em>memlimit</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set the memory usage limit. </p>
<p>This function is supported only when *strm has been initialized with a function that takes a memlimit argument.</p>
<p>liblzma 5.2.3 and earlier has a bug where memlimit value of 0 causes this function to do nothing (leaving the limit unchanged) and still return LZMA_OK. Later versions treat 0 as if 1 had been specified (so <a class="el" href="#ac871bc2ead5d482c6d6b3d51bfec365c" title="Get the current memory usage limit.">lzma_memlimit_get()</a> will return 1 even if you specify 0 here).</p>
<p>liblzma 5.2.6 and earlier had a bug in single-threaded .xz decoder (<a class="el" href="container_8h.html#a02b7683ef98d8049788961370a8b28c0" title="Initialize .xz Stream decoder.">lzma_stream_decoder()</a>) which made it impossible to continue decoding after LZMA_MEMLIMIT_ERROR even if the limit was increased using <a class="el" href="#afc49d4cf75b73128a167df3407505f7b" title="Set the memory usage limit.">lzma_memlimit_set()</a>. Other decoders worked correctly.</p>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: New memory usage limit successfully set.</li>
<li>LZMA_MEMLIMIT_ERROR: The new limit is too small. The limit was not changed.</li>
<li>LZMA_PROG_ERROR: Invalid arguments, e.g. *strm doesn't support memory usage limit. </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li><li class="navelem"><a class="el" href="base_8h.html">base.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
