set ::msgcat::header "Project-Id-Version: git-gui master\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2024-12-22 15:44+0100\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Bulgarian <<EMAIL>>\nLanguage: bg\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=2; plural=(n != 1);\n"
::msgcat::mcset bg "Invalid font specified in %s:" "\u0423\u043a\u0430\u0437\u0430\u043d \u0435 \u043d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u0435\u043d \u0448\u0440\u0438\u0444\u0442 \u0432 \u201e%s\u201c:"
::msgcat::mcset bg "Main Font" "\u041e\u0441\u043d\u043e\u0432\u0435\u043d \u0448\u0440\u0438\u0444\u0442"
::msgcat::mcset bg "Diff/Console Font" "\u0428\u0440\u0438\u0444\u0442 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435/\u043a\u043e\u043d\u0437\u043e\u043b\u0430\u0442\u0430"
::msgcat::mcset bg "git-gui: fatal error" "git-gui: \u0444\u0430\u0442\u0430\u043b\u043d\u0430 \u0433\u0440\u0435\u0448\u043a\u0430"
::msgcat::mcset bg "Cannot find git in PATH." "\u041a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 git \u043b\u0438\u043f\u0441\u0432\u0430 \u0432 \u043f\u044a\u0442\u044f (PATH)."
::msgcat::mcset bg "Cannot parse Git version string:" "\u041d\u0438\u0437\u044a\u0442 \u0441 \u0432\u0435\u0440\u0441\u0438\u044f\u0442\u0430 \u043d\u0430 Git \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0430\u043d\u0430\u043b\u0438\u0437\u0438\u0440\u0430:"
::msgcat::mcset bg "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "\u0412\u0435\u0440\u0441\u0438\u044f\u0442\u0430 \u043d\u0430 Git \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0438.\n\n\u0412\u0435\u0440\u0441\u0438\u044f\u0442\u0430 \u043d\u0430 \u201e%s\u201c \u0438\u0437\u0433\u043b\u0435\u0436\u0434\u0430, \u0447\u0435 \u0435 \u201e%s\u201c.\n\n\u201e%s\u201c \u0438\u0437\u0438\u0441\u043a\u0432\u0430 Git, \u0432\u0435\u0440\u0441\u0438\u044f \u043f\u043e\u043d\u0435 1.5.0.\n\n\u0414\u0430 \u0441\u0435 \u043f\u0440\u0438\u0435\u043c\u0435 \u043b\u0438, \u0447\u0435 \u201e%s\u201c \u0435 \u0432\u0435\u0440\u0441\u0438\u044f \u201e1.5.0\u201c?\n"
::msgcat::mcset bg "Git directory not found:" "\u0414\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f\u0442\u0430 \u043d\u0430 Git \u043d\u0435 \u0435 \u043e\u0442\u043a\u0440\u0438\u0442\u0430:"
::msgcat::mcset bg "Cannot move to top of working directory:" "\u041d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043f\u0440\u0435\u043c\u0438\u043d\u0435 \u043a\u044a\u043c \u0440\u043e\u0434\u0438\u0442\u0435\u043b\u0441\u043a\u0430\u0442\u0430 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f."
::msgcat::mcset bg "Cannot use bare repository:" "\u0413\u043e\u043b\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0438\u0437\u043f\u043e\u043b\u0437\u0432\u0430:"
::msgcat::mcset bg "No working directory" "\u0420\u0430\u0431\u043e\u0442\u043d\u0430\u0442\u0430 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f \u043b\u0438\u043f\u0441\u0432\u0430"
::msgcat::mcset bg "Refreshing file status..." "\u041e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u0441\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435\u0442\u043e \u043d\u0430 \u0444\u0430\u0439\u043b\u0430\u2026"
::msgcat::mcset bg "Scanning for modified files ..." "\u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u0437\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0435\u043d\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u2026"
::msgcat::mcset bg "Calling prepare-commit-msg hook..." "\u041a\u0443\u043a\u0430\u0442\u0430 \u201eprepare-commit-msg\u201c \u0441\u0435 \u0438\u0437\u043f\u044a\u043b\u043d\u044f\u0432\u0430 \u0432 \u043c\u043e\u043c\u0435\u043d\u0442\u0430\u2026"
::msgcat::mcset bg "Commit declined by prepare-commit-msg hook." "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u0435 \u043e\u0442\u0445\u0432\u044a\u0440\u043b\u0435\u043d\u043e \u043e\u0442 \u043a\u0443\u043a\u0430\u0442\u0430 \u201eprepare-commit-msg\u201c."
::msgcat::mcset bg "Ready." "\u0413\u043e\u0442\u043e\u0432\u043e."
::msgcat::mcset bg "Display limit (gui.maxfilesdisplayed = %s) reached, not showing all %s files." "\u0414\u043e\u0441\u0442\u0438\u0433\u043d\u0430\u0442 \u0435 \u043c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u043d\u0438\u044f\u0442 \u0440\u0430\u0437\u043c\u0435\u0440 \u043d\u0430 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0437\u0430 \u0438\u0437\u0432\u0435\u0436\u0434\u0430\u043d\u0435(gui.maxfilesdisplayed = %s), \u0441\u044a\u043e\u0442\u0432\u0435\u0442\u043d\u043e \u043d\u0435 \u0441\u0430 \u043f\u043e\u043a\u0430\u0437\u0430\u043d\u0438 \u0432\u0441\u0438\u0447\u043a\u0438 %s \u0444\u0430\u0439\u043b\u0430."
::msgcat::mcset bg "Unmodified" "\u041d\u0435\u043f\u0440\u043e\u043c\u0435\u043d\u0435\u043d"
::msgcat::mcset bg "Modified, not staged" "\u041f\u0440\u043e\u043c\u0435\u043d\u0435\u043d, \u043d\u043e \u043d\u0435 \u0435 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset bg "Staged for commit" "\u0412 \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Portions staged for commit" "\u0427\u0430\u0441\u0442\u0438 \u0441\u0430 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Staged for commit, missing" "\u0412 \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435, \u043d\u043e \u043b\u0438\u043f\u0441\u0432\u0430"
::msgcat::mcset bg "File type changed, not staged" "\u0412\u0438\u0434\u044a\u0442 \u043d\u0430 \u0444\u0430\u0439\u043b\u0430 \u0435 \u0441\u043c\u0435\u043d\u0435\u043d, \u043d\u043e \u043d\u0435 \u0435 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset bg "File type changed, old type staged for commit" "\u0412\u0438\u0434\u044a\u0442 \u043d\u0430 \u0444\u0430\u0439\u043b\u0430 \u0435 \u0441\u043c\u0435\u043d\u0435\u043d, \u043d\u043e \u043d\u043e\u0432\u0438\u044f\u0442 \u0432\u0438\u0434 \u043d\u0435 \u0435 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset bg "File type changed, staged" "\u0412\u0438\u0434\u044a\u0442 \u043d\u0430 \u0444\u0430\u0439\u043b\u0430 \u0435 \u0441\u043c\u0435\u043d\u0435\u043d \u0438 \u0435 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset bg "File type change staged, modification not staged" "\u0412\u0438\u0434\u044a\u0442 \u043d\u0430 \u0444\u0430\u0439\u043b\u0430 \u0435 \u0441\u043c\u0435\u043d\u0435\u043d \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430, \u043d\u043e \u043d\u0435 \u0438 \u0441\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435\u0442\u043e"
::msgcat::mcset bg "File type change staged, file missing" "\u0412\u0438\u0434\u044a\u0442 \u043d\u0430 \u0444\u0430\u0439\u043b\u0430 \u0435 \u0441\u043c\u0435\u043d\u0435\u043d \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430, \u043d\u043e \u0444\u0430\u0439\u043b\u044a\u0442 \u043b\u0438\u043f\u0441\u0432\u0430"
::msgcat::mcset bg "Untracked, not staged" "\u041d\u0435\u0441\u043b\u0435\u0434\u0435\u043d"
::msgcat::mcset bg "Missing" "\u041b\u0438\u043f\u0441\u0432\u0430\u0449"
::msgcat::mcset bg "Staged for removal" "\u0412 \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0437\u0430 \u0438\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Staged for removal, still present" "\u0412 \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0437\u0430 \u0438\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435, \u043d\u043e \u043e\u0449\u0435 \u0433\u043e \u0438\u043c\u0430"
::msgcat::mcset bg "Requires merge resolution" "\u0418\u0437\u0438\u0441\u043a\u0432\u0430 \u043a\u043e\u0440\u0438\u0433\u0438\u0440\u0430\u043d\u0435 \u043f\u0440\u0438 \u0441\u043b\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Couldn't find gitk in PATH" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u201egitk\u201c \u043b\u0438\u043f\u0441\u0432\u0430 \u0432 \u043f\u044a\u0442\u0438\u0449\u0430\u0442\u0430, \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438 \u043e\u0442 \u043f\u0440\u043e\u043c\u0435\u043d\u043b\u0438\u0432\u0430\u0442\u0430 PATH."
::msgcat::mcset bg "Starting %s... please wait..." "\u0421\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c\u2026, \u0438\u0437\u0447\u0430\u043a\u0430\u0439\u0442\u0435\u2026"
::msgcat::mcset bg "Couldn't find git gui in PATH" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u201egit gui\u201c \u043b\u0438\u043f\u0441\u0432\u0430 \u0432 \u043f\u044a\u0442\u0438\u0449\u0430\u0442\u0430, \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438 \u043e\u0442 \u043f\u0440\u043e\u043c\u0435\u043d\u043b\u0438\u0432\u0430\u0442\u0430 PATH."
::msgcat::mcset bg "Repository" "\u0425\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Edit" "\u0420\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435"
::msgcat::mcset bg "Branch" "\u041a\u043b\u043e\u043d"
::msgcat::mcset bg "Commit@@noun" "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Merge" "\u0421\u043b\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Remote" "\u041e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Tools" "\u041a\u043e\u043c\u0430\u043d\u0434\u0438"
::msgcat::mcset bg "Explore Working Copy" "\u0420\u0430\u0437\u0433\u043b\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0440\u0430\u0431\u043e\u0442\u043d\u043e\u0442\u043e \u043a\u043e\u043f\u0438\u0435"
::msgcat::mcset bg "Git Bash" "Bash \u0437\u0430 Git"
::msgcat::mcset bg "Browse Current Branch's Files" "\u0420\u0430\u0437\u0433\u043b\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435 \u0432 \u0442\u0435\u043a\u0443\u0449\u0438\u044f \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Browse Branch Files..." "\u0420\u0430\u0437\u0433\u043b\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0442\u0435\u043a\u0443\u0449\u0438\u044f \u043a\u043b\u043e\u043d\u2026"
::msgcat::mcset bg "Visualize Current Branch's History" "\u0412\u0438\u0437\u0443\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u044f \u043d\u0430 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430 \u043d\u0430 \u0442\u0435\u043a\u0443\u0449\u0438\u044f \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Visualize All Branch History" "\u0412\u0438\u0437\u0443\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u044f \u043d\u0430 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430 \u043d\u0430 \u0432\u0441\u0438\u0447\u043a\u0438 \u043a\u043b\u043e\u043d\u043e\u0432\u0435"
::msgcat::mcset bg "Browse %s's Files" "\u0420\u0430\u0437\u0433\u043b\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435 \u0432 \u201e%s\u201c"
::msgcat::mcset bg "Visualize %s's History" "\u0412\u0438\u0437\u0443\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u044f \u043d\u0430 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430 \u043d\u0430 \u201e%s\u201c"
::msgcat::mcset bg "Database Statistics" "\u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043a\u0430 \u043d\u0430 \u0431\u0430\u0437\u0430\u0442\u0430 \u043e\u0442 \u0434\u0430\u043d\u043d\u0438"
::msgcat::mcset bg "Compress Database" "\u041a\u043e\u043c\u043f\u0440\u0435\u0441\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0431\u0430\u0437\u0430\u0442\u0430 \u043e\u0442 \u0434\u0430\u043d\u043d\u0438"
::msgcat::mcset bg "Verify Database" "\u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u0431\u0430\u0437\u0430\u0442\u0430 \u043e\u0442 \u0434\u0430\u043d\u043d\u0438"
::msgcat::mcset bg "Create Desktop Icon" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0438\u043a\u043e\u043d\u0430 \u043d\u0430 \u0440\u0430\u0431\u043e\u0442\u043d\u0438\u044f \u043f\u043b\u043e\u0442"
::msgcat::mcset bg "Quit" "\u0421\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430"
::msgcat::mcset bg "Undo" "\u041e\u0442\u043c\u044f\u043d\u0430"
::msgcat::mcset bg "Redo" "\u041f\u043e\u0432\u0442\u043e\u0440\u0435\u043d\u0438\u0435"
::msgcat::mcset bg "Cut" "\u041e\u0442\u0440\u044f\u0437\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Copy" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435"
::msgcat::mcset bg "Paste" "\u041f\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435"
::msgcat::mcset bg "Delete" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Select All" "\u0418\u0437\u0431\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0432\u0441\u0438\u0447\u043a\u043e"
::msgcat::mcset bg "Create..." "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Checkout..." "\u0418\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435\u2026"
::msgcat::mcset bg "Rename..." "\u041f\u0440\u0435\u0438\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Delete..." "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Reset..." "\u041e\u0442\u043c\u044f\u043d\u0430 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435\u2026"
::msgcat::mcset bg "Done" "\u0413\u043e\u0442\u043e\u0432\u043e"
::msgcat::mcset bg "Commit@@verb" "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Amend Last Commit" "\u041f\u043e\u043f\u0440\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Rescan" "\u041e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Stage To Commit" "\u041a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Stage Changed Files To Commit" "\u0412\u0441\u0438\u0447\u043a\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0435\u043d\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Unstage From Commit" "\u0418\u0437\u0432\u0430\u0436\u0434\u0430\u043d\u0435 \u043e\u0442 \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Revert Changes" "\u0412\u0440\u044a\u0449\u0430\u043d\u0435 \u043d\u0430 \u043e\u0440\u0438\u0433\u0438\u043d\u0430\u043b\u0430"
::msgcat::mcset bg "Show Less Context" "\u041f\u043e-\u043c\u0430\u043b\u043a\u043e \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442"
::msgcat::mcset bg "Show More Context" "\u041f\u043e\u0432\u0435\u0447\u0435 \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442"
::msgcat::mcset bg "Sign Off" "\u041f\u043e\u0434\u043f\u0438\u0441\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Local Merge..." "\u041b\u043e\u043a\u0430\u043b\u043d\u043e \u0441\u043b\u0438\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Abort Merge..." "\u041f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Add..." "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435\u2026"
::msgcat::mcset bg "Push..." "\u0418\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Delete Branch..." "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d\u2026"
::msgcat::mcset bg "Options..." "\u041e\u043f\u0446\u0438\u0438\u2026"
::msgcat::mcset bg "Remove..." "\u041f\u0440\u0435\u043c\u0430\u0445\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Help" "\u041f\u043e\u043c\u043e\u0449"
::msgcat::mcset bg "About %s" "\u041e\u0442\u043d\u043e\u0441\u043d\u043e \u201e%s\u201c"
::msgcat::mcset bg "Online Documentation" "\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u044f \u0432 \u0418\u043d\u0442\u0435\u0440\u043d\u0435\u0442"
::msgcat::mcset bg "Show SSH Key" "\u041f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u044e\u0447\u0430 \u0437\u0430 SSH"
::msgcat::mcset bg "usage:" "\u0443\u043f\u043e\u0442\u0440\u0435\u0431\u0430:"
::msgcat::mcset bg "Usage" "\u0423\u043f\u043e\u0442\u0440\u0435\u0431\u0430"
::msgcat::mcset bg "Error" "\u0413\u0440\u0435\u0448\u043a\u0430"
::msgcat::mcset bg "fatal: cannot stat path %s: No such file or directory" "\u0424\u0410\u0422\u0410\u041b\u041d\u0410 \u0413\u0420\u0415\u0428\u041a\u0410: \u043f\u044a\u0442\u044f\u0442 \u201e%s\u201c \u043b\u0438\u043f\u0441\u0432\u0430: \u0442\u0430\u043a\u044a\u0432 \u0444\u0430\u0439\u043b \u0438\u043b\u0438 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f \u043d\u044f\u043c\u0430"
::msgcat::mcset bg "Current Branch:" "\u0422\u0435\u043a\u0443\u0449 \u043a\u043b\u043e\u043d:"
::msgcat::mcset bg "Unstaged Changes" "\u041f\u0440\u043e\u043c\u0435\u043d\u0438 \u0438\u0437\u0432\u044a\u043d \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset bg "Staged Changes (Will Commit)" "\u041f\u0440\u043e\u043c\u0435\u043d\u0438 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430 (\u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435)"
::msgcat::mcset bg "Stage Changed" "\u0418\u043d\u0434\u0435\u043a\u0441\u044a\u0442 \u0435 \u043f\u0440\u043e\u043c\u0435\u043d\u0435\u043d"
::msgcat::mcset bg "Push" "\u0418\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Initial Commit Message:" "\u041f\u044a\u0440\u0432\u043e\u043d\u0430\u0447\u0430\u043b\u043d\u043e \u0441\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Amended Commit Message:" "\u041f\u043e\u043f\u0440\u0430\u0432\u0435\u043d\u043e \u0441\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Amended Initial Commit Message:" "\u041f\u043e\u043f\u0440\u0430\u0432\u0435\u043d\u043e \u043f\u044a\u0440\u0432\u043e\u043d\u0430\u0447\u0430\u043b\u043d\u043e \u0441\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Amended Merge Commit Message:" "\u041f\u043e\u043f\u0440\u0430\u0432\u0435\u043d\u043e \u0441\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u0441\u044a\u0441 \u0441\u043b\u0438\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Merge Commit Message:" "\u0421\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u0441\u044a\u0441 \u0441\u043b\u0438\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Commit Message:" "\u0421\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Copy All" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0432\u0441\u0438\u0447\u043a\u043e"
::msgcat::mcset bg "File:" "\u0424\u0430\u0439\u043b:"
::msgcat::mcset bg "Open" "\u041e\u0442\u0432\u0430\u0440\u044f\u043d\u0435"
::msgcat::mcset bg "Refresh" "\u041e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Decrease Font Size" "\u041f\u043e-\u0434\u0440\u0435\u0431\u0435\u043d \u0448\u0440\u0438\u0444\u0442"
::msgcat::mcset bg "Increase Font Size" "\u041f\u043e-\u0435\u0434\u044a\u0440 \u0448\u0440\u0438\u0444\u0442"
::msgcat::mcset bg "Encoding" "\u041a\u043e\u0434\u0438\u0440\u0430\u043d\u0435"
::msgcat::mcset bg "Apply/Reverse Hunk" "\u041f\u0440\u0438\u043b\u0430\u0433\u0430\u043d\u0435/\u0432\u0440\u044a\u0449\u0430\u043d\u0435 \u043d\u0430 \u043f\u0430\u0440\u0447\u0435"
::msgcat::mcset bg "Apply/Reverse Line" "\u041f\u0440\u0438\u043b\u0430\u0433\u0430\u043d\u0435/\u0432\u0440\u044a\u0449\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434"
::msgcat::mcset bg "Revert Hunk" "\u0412\u0440\u044a\u0449\u0430\u043d\u0435 \u043d\u0430 \u043f\u0430\u0440\u0447\u0435"
::msgcat::mcset bg "Revert Line" "\u0412\u0440\u044a\u0449\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434"
::msgcat::mcset bg "Undo Last Revert" "\u041e\u0442\u043c\u044f\u043d\u0430 \u043d\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u043e\u0442\u043e \u0432\u0440\u044a\u0449\u0430\u043d\u0435"
::msgcat::mcset bg "Run Merge Tool" "\u0418\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Use Remote Version" "\u0412\u0435\u0440\u0441\u0438\u044f \u043e\u0442 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e\u0442\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Use Local Version" "\u041b\u043e\u043a\u0430\u043b\u043d\u0430 \u0432\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset bg "Revert To Base" "\u0412\u0440\u044a\u0449\u0430\u043d\u0435 \u043a\u044a\u043c \u0440\u043e\u0434\u0438\u0442\u0435\u043b\u0441\u043a\u0430\u0442\u0430 \u0432\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset bg "Visualize These Changes In The Submodule" "\u0412\u0438\u0437\u0443\u0430\u043b\u0438\u0437\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u0432 \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0430"
::msgcat::mcset bg "Visualize Current Branch History In The Submodule" "\u0412\u0438\u0437\u0443\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u044f \u043d\u0430 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430 \u043d\u0430 \u0442\u0435\u043a\u0443\u0449\u0438\u044f \u043a\u043b\u043e\u043d \u0432 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430 \u0437\u0430 \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0430"
::msgcat::mcset bg "Visualize All Branch History In The Submodule" "\u0412\u0438\u0437\u0443\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u044f \u043d\u0430 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430 \u043d\u0430 \u0432\u0441\u0438\u0447\u043a\u0438 \u043a\u043b\u043e\u043d\u0438 \u0432 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430 \u0437\u0430 \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0430"
::msgcat::mcset bg "Start git gui In The Submodule" "\u0421\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u201egit gui\u201c \u0437\u0430 \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0430"
::msgcat::mcset bg "Unstage Hunk From Commit" "\u0418\u0437\u0432\u0430\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u043f\u0430\u0440\u0447\u0435\u0442\u043e \u043e\u0442 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Unstage Lines From Commit" "\u0418\u0437\u0432\u0430\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434\u043e\u0432\u0435\u0442\u0435 \u043e\u0442 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Revert Lines" "\u0412\u0440\u044a\u0449\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434\u043e\u0432\u0435\u0442\u0435"
::msgcat::mcset bg "Unstage Line From Commit" "\u0418\u0437\u0432\u0430\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434\u0430 \u043e\u0442 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Stage Hunk For Commit" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043f\u0430\u0440\u0447\u0435\u0442\u043e \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Stage Lines For Commit" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434\u043e\u0432\u0435\u0442\u0435 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Stage Line For Commit" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434\u0430 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Initializing..." "\u0418\u043d\u0438\u0446\u0438\u0430\u043b\u0438\u0437\u0438\u0440\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "\u0412\u044a\u0437\u043c\u043e\u0436\u043d\u043e \u0435 \u0434\u0430 \u0438\u043c\u0430 \u043f\u0440\u043e\u0431\u043b\u0435\u043c \u0441\u044a\u0441 \u0441\u0440\u0435\u0434\u0430\u0442\u0430.\n\n\u041d\u0430\u0439-\u0432\u0435\u0440\u043e\u044f\u0442\u043d\u043e \u0441\u043b\u0435\u0434\u043d\u0438\u0442\u0435 \u043f\u0440\u043e\u043c\u0435\u043d\u043b\u0438\u0432\u0438 \u043d\u044f\u043c\u0430 \u0434\u0430 \u0441\u0435\n\u0432\u0437\u0435\u043c\u0430\u0442 \u043f\u043e\u0434 \u0432\u043d\u0438\u043c\u0430\u043d\u0438\u0435 \u043e\u0442 \u043f\u043e\u0434\u043f\u0440\u043e\u0446\u0435\u0441\u0438\u0442\u0435 \u043d\u0430 Git\n\u043e\u0442 %s:\n\n"
::msgcat::mcset bg "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\n\u0422\u043e\u0432\u0430 \u0435 \u043f\u043e\u0437\u043d\u0430\u0442 \u043f\u0440\u043e\u0431\u043b\u0435\u043c \u0438 \u0441\u0435 \u0434\u044a\u043b\u0436\u0438 \u043d\u0430\n\u0432\u0435\u0440\u0441\u0438\u044f\u0442\u0430 \u043d\u0430 Tcl \u0432\u043a\u043b\u044e\u0447\u0435\u043d\u0430 \u0432 Cygwin."
::msgcat::mcset bg "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\n\u0414\u043e\u0431\u044a\u0440 \u0437\u0430\u043c\u0435\u0441\u0442\u0438\u0442\u0435\u043b \u043d\u0430 \u201e%s\u201c\n\u0435 \u0434\u0430 \u043f\u043e\u0441\u0442\u0430\u0432\u0438\u0442\u0435 \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438\u0442\u0435 \u201euser.name\u201c \u0438\n\u201euser.email\u201c \u0432 \u043b\u0438\u0447\u043d\u0438\u044f \u0441\u0438 \u0444\u0430\u0439\u043b \u201e~/.gitconfig\u201c.\n"
::msgcat::mcset bg "Unsupported spell checker" "\u0422\u0430\u0437\u0438 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430 \u0437\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430 \u043d\u0435 \u0441\u0435 \u043f\u043e\u0434\u0434\u044a\u0440\u0436\u0430"
::msgcat::mcset bg "Spell checking is unavailable" "\u041b\u0438\u043f\u0441\u0432\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430 \u0437\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430"
::msgcat::mcset bg "Invalid spell checking configuration" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u043d\u0438 \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043d\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430\u0442\u0430 \u043d\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430"
::msgcat::mcset bg "Reverting dictionary to %s." "\u041f\u043e\u043b\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0447\u043d\u0438\u043a \u0437\u0430 \u0435\u0437\u0438\u043a \u201e%s\u201c."
::msgcat::mcset bg "Spell checker silently failed on startup" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u0437\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441 \u0434\u0430\u0436\u0435 \u043d\u0435 \u0441\u0442\u0430\u0440\u0442\u0438\u0440\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e."
::msgcat::mcset bg "Unrecognized spell checker" "\u041d\u0435\u043f\u043e\u0437\u043d\u0430\u0442\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430 \u0437\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430"
::msgcat::mcset bg "No Suggestions" "\u041d\u044f\u043c\u0430 \u043f\u0440\u0435\u0434\u043b\u043e\u0436\u0435\u043d\u0438\u044f"
::msgcat::mcset bg "Unexpected EOF from spell checker" "\u041d\u0435\u043e\u0447\u0430\u043a\u0432\u0430\u043d \u043a\u0440\u0430\u0439 \u043d\u0430 \u0444\u0430\u0439\u043b \u043e\u0442 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u0437\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430"
::msgcat::mcset bg "Spell Checker Failed" "\u0413\u0440\u0435\u0448\u043a\u0430 \u0432 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u0437\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430"
::msgcat::mcset bg "fetch %s" "\u0434\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u201e%s\u201c"
::msgcat::mcset bg "Fetching new changes from %s" "\u0414\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u043e\u0442 \u201e%s\u201c"
::msgcat::mcset bg "remote prune %s" "\u043e\u043a\u0430\u0441\u0442\u0440\u044f\u043d\u0435 \u043d\u0430 \u0441\u043b\u0435\u0434\u044f\u0449\u0438\u0442\u0435 \u043a\u043b\u043e\u043d\u0438 \u043a\u044a\u043c \u201e%s\u201c"
::msgcat::mcset bg "Pruning tracking branches deleted from %s" "\u041e\u043a\u0430\u0441\u0442\u0440\u044f\u043d\u0435 \u043d\u0430 \u0441\u043b\u0435\u0434\u044f\u0449\u0438\u0442\u0435 \u043a\u043b\u043e\u043d\u0438 \u043d\u0430 \u0438\u0437\u0442\u0440\u0438\u0442\u0438\u0442\u0435 \u043a\u043b\u043e\u043d\u0438 \u043e\u0442 \u201e%s\u201c"
::msgcat::mcset bg "fetch all remotes" "\u0434\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435 \u043e\u0442 \u0432\u0441\u0438\u0447\u043a\u0438 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u0438"
::msgcat::mcset bg "Fetching new changes from all remotes" "\u0414\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u043e\u0442 \u0432\u0441\u0438\u0447\u043a\u0438 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u0438 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0430"
::msgcat::mcset bg "remote prune all remotes" "\u043e\u043a\u0430\u0441\u0442\u0440\u044f\u043d\u0435 \u043d\u0430 \u0441\u043b\u0435\u0434\u044f\u0449\u0438\u0442\u0435 \u0438\u0437\u0442\u0440\u0438\u0442\u0438"
::msgcat::mcset bg "Pruning tracking branches deleted from all remotes" "\u041e\u043a\u0430\u0441\u0442\u0440\u044f\u043d\u0435 \u043d\u0430 \u0441\u043b\u0435\u0434\u044f\u0449\u0438\u0442\u0435 \u043a\u043b\u043e\u043d\u0438 \u043d\u0430 \u0438\u0437\u0442\u0440\u0438\u0442\u0438\u0442\u0435 \u043a\u043b\u043e\u043d\u0438 \u043e\u0442 \u0432\u0441\u0438\u0447\u043a\u0438 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u0438 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0430"
::msgcat::mcset bg "push %s" "\u0438\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c"
::msgcat::mcset bg "Pushing changes to %s" "\u0418\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u043a\u044a\u043c \u201e%s\u201c"
::msgcat::mcset bg "Mirroring to %s" "\u0418\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 \u0432\u0441\u0438\u0447\u043a\u043e \u043a\u044a\u043c \u201e%s\u201c"
::msgcat::mcset bg "Pushing %s %s to %s" "\u0418\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 %s \u201e%s\u201c \u043a\u044a\u043c \u201e%s\u201c"
::msgcat::mcset bg "Push Branches" "\u041a\u043b\u043e\u043d\u0438 \u0437\u0430 \u0438\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Cancel" "\u041e\u0442\u043a\u0430\u0437\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Source Branches" "\u041a\u043b\u043e\u043d\u0438-\u0438\u0437\u0442\u043e\u0447\u043d\u0438\u0446\u0438"
::msgcat::mcset bg "Destination Repository" "\u0426\u0435\u043b\u0435\u0432\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Remote:" "\u041e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435:"
::msgcat::mcset bg "Arbitrary Location:" "\u041f\u0440\u043e\u0438\u0437\u0432\u043e\u043b\u043d\u043e \u043c\u0435\u0441\u0442\u043e\u043f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435:"
::msgcat::mcset bg "Transfer Options" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043f\u0440\u0438 \u043f\u0440\u0435\u043d\u0430\u0441\u044f\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Force overwrite existing branch (may discard changes)" "\u0418\u0437\u0440\u0438\u0447\u043d\u043e \u043f\u0440\u0435\u0437\u0430\u043f\u0438\u0441\u0432\u0430\u043d\u0435 \u043d\u0430 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430\u0449 \u043a\u043b\u043e\u043d (\u043d\u044f\u043a\u043e\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0437\u0430\u0433\u0443\u0431\u044f\u0442)"
::msgcat::mcset bg "Use thin pack (for slow network connections)" "\u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u043d\u0430 \u043a\u043e\u043c\u043f\u0440\u0435\u0441\u0438\u044f (\u0437\u0430 \u0431\u0430\u0432\u043d\u0438 \u043c\u0440\u0435\u0436\u043e\u0432\u0438 \u0432\u0440\u044a\u0437\u043a\u0438)"
::msgcat::mcset bg "Include tags" "\u0412\u043a\u043b\u044e\u0447\u0432\u0430\u043d\u0435 \u043d\u0430 \u0435\u0442\u0438\u043a\u0435\u0442\u0438\u0442\u0435"
::msgcat::mcset bg "%s (%s): Push" "%s (%s): \u0418\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Fetching %s from %s" "\u0414\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u201e%s\u201c \u043e\u0442 \u201e%s\u201c"
::msgcat::mcset bg "fatal: Cannot resolve %s" "\u0444\u0430\u0442\u0430\u043b\u043d\u0430 \u0433\u0440\u0435\u0448\u043a\u0430: \u201e%s\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043e\u0442\u043a\u0440\u0438\u0435"
::msgcat::mcset bg "Close" "\u0417\u0430\u0442\u0432\u0430\u0440\u044f\u043d\u0435"
::msgcat::mcset bg "Branch '%s' does not exist." "\u041a\u043b\u043e\u043d\u044a\u0442 \u201e%s\u201c \u043d\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430."
::msgcat::mcset bg "Failed to configure simplified git-pull for '%s'." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u0432\u0430\u043d\u0435 \u043d\u0430 \u043e\u043f\u0440\u043e\u0441\u0442\u0435\u043d git-pull \u0437\u0430 \u201e%s\u201c."
::msgcat::mcset bg "Branch '%s' already exists." "\u041a\u043b\u043e\u043d\u044a\u0442 \u201e%s\u201c \u0432\u0435\u0447\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430."
::msgcat::mcset bg "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "\u041a\u043b\u043e\u043d\u044a\u0442 \u201e%s\u201c \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430.\n\n\u0422\u043e\u0439 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0441\u043b\u0435\u0435 \u0442\u0440\u0438\u0432\u0438\u0430\u043b\u043d\u043e \u0434\u043e \u201e%s\u201c.\n\u041d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u043e \u0435 \u0441\u043b\u0438\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "Merge strategy '%s' not supported." "\u0421\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u044f \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435 \u201e%s\u201c \u043d\u0435 \u0441\u0435 \u043f\u043e\u0434\u0434\u044a\u0440\u0436\u0430."
::msgcat::mcset bg "Failed to update '%s'." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c."
::msgcat::mcset bg "Staging area (index) is already locked." "\u0418\u043d\u0434\u0435\u043a\u0441\u044a\u0442 \u0432\u0435\u0447\u0435 \u0435 \u0437\u0430\u043a\u043b\u044e\u0447\u0435\u043d."
::msgcat::mcset bg "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "\u0421\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435\u0442\u043e \u043f\u0440\u0438 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0430\u0442\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0435 \u043e\u0442\u0433\u043e\u0432\u0430\u0440\u044f \u043d\u0430 \u0441\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435\u0442\u043e \u043d\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e.\n\n\u041d\u044f\u043a\u043e\u0439 \u0434\u0440\u0443\u0433 \u043f\u0440\u043e\u0446\u0435\u0441 \u0437\u0430 Git \u0435 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u043b \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e \u043c\u0435\u0436\u0434\u0443\u0432\u0440\u0435\u043c\u0435\u043d\u043d\u043e. \u0421\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435\u0442\u043e \u0442\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0441\u0435 \u043f\u0440\u043e\u0432\u0435\u0440\u0438, \u043f\u0440\u0435\u0434\u0438 \u0434\u0430 \u0441\u0435 \u043f\u0440\u0435\u043c\u0438\u043d\u0435 \u043a\u044a\u043c \u043d\u043e\u0432 \u043a\u043b\u043e\u043d.\n\n\u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u043d\u043e \u0449\u0435 \u0437\u0430\u043f\u043e\u0447\u043d\u0435 \u043d\u043e\u0432\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430.\n"
::msgcat::mcset bg "Updating working directory to '%s'..." "\u0420\u0430\u0431\u043e\u0442\u043d\u0430\u0442\u0430 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f \u0441\u0435 \u043f\u0440\u0438\u0432\u0435\u0436\u0434\u0430 \u043a\u044a\u043c \u201e%s\u201c\u2026"
::msgcat::mcset bg "files checked out" "\u0444\u0430\u0439\u043b\u0430 \u0441\u0430 \u0438\u0437\u0442\u0435\u0433\u043b\u0435\u043d\u0438"
::msgcat::mcset bg "Aborted checkout of '%s' (file level merging is required)." "\u041f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u0438\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435\u0442\u043e \u043d\u0430 \u201e%s\u201c (\u043d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u043e \u0435 \u043f\u043e\u0444\u0430\u0439\u043b\u043e\u0432\u043e \u0441\u043b\u0438\u0432\u0430\u043d\u0435)."
::msgcat::mcset bg "File level merge required." "\u041d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u043e \u0435 \u043f\u043e\u0444\u0430\u0439\u043b\u043e\u0432\u043e \u0441\u043b\u0438\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "Staying on branch '%s'." "\u041e\u0441\u0442\u0430\u0432\u0430\u043d\u0435 \u0432\u044a\u0440\u0445\u0443 \u043a\u043b\u043e\u043d\u0430 \u201e%s\u201c."
::msgcat::mcset bg "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "\u0412\u0435\u0447\u0435 \u043d\u0435 \u0441\u0442\u0435 \u043d\u0430 \u043b\u043e\u043a\u0430\u043b\u0435\u043d \u043a\u043b\u043e\u043d.\n\n\u0410\u043a\u043e \u0438\u0441\u043a\u0430\u0442\u0435 \u0434\u0430 \u0441\u0442\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d, \u0441\u044a\u0437\u0434\u0430\u0439\u0442\u0435 \u0431\u0430\u0437\u0438\u0440\u0430\u043d \u043d\u0430 \u201e\u0422\u043e\u0432\u0430 \u043d\u0435\u0441\u0432\u044a\u0440\u0437\u0430\u043d\u043e \u0438\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435\u201c."
::msgcat::mcset bg "Checked out '%s'." "\u201e%s\u201c \u0435 \u0438\u0437\u0442\u0435\u0433\u043b\u0435\u043d."
::msgcat::mcset bg "Resetting '%s' to '%s' will lose the following commits:" "\u0417\u0430\u043d\u0443\u043b\u044f\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u201e%s\u201c \u043a\u044a\u043c \u201e%s\u201c \u0449\u0435 \u0434\u043e\u0432\u0435\u0434\u0435 \u0434\u043e \u0437\u0430\u0433\u0443\u0431\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u043b\u0435\u0434\u043d\u0438\u0442\u0435 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f:"
::msgcat::mcset bg "Recovering lost commits may not be easy." "\u0412\u044a\u0437\u0441\u0442\u0430\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0437\u0430\u0433\u0443\u0431\u0435\u043d\u0438\u0442\u0435 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f \u043c\u043e\u0436\u0435 \u0434\u0430 \u0435 \u0442\u0440\u0443\u0434\u043d\u043e."
::msgcat::mcset bg "Reset '%s'?" "\u0417\u0430\u043d\u0443\u043b\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c?"
::msgcat::mcset bg "Visualize" "\u0412\u0438\u0437\u0443\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u044f"
::msgcat::mcset bg "Reset" "\u041e\u0442\u043d\u0430\u0447\u0430\u043b\u043e"
::msgcat::mcset bg "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0437\u0430\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u0442\u0435\u043a\u0443\u0449\u0438\u044f \u043a\u043b\u043e\u043d.\n\n\u0420\u0430\u0431\u043e\u0442\u043d\u0430\u0442\u0430 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f \u0435 \u0441\u0430\u043c\u043e \u0447\u0430\u0441\u0442\u0438\u0447\u043d\u043e \u043e\u0431\u043d\u043e\u0432\u0435\u043d\u0430: \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435 \u0441\u0430 \u043e\u0431\u043d\u043e\u0432\u0435\u043d\u0438 \u0443\u0441\u043f\u0435\u0448\u043d\u043e, \u043d\u043e \u043d\u044f\u043a\u043e\u0439 \u043e\u0442 \u0432\u044a\u0442\u0440\u0435\u0448\u043d\u0438\u0442\u0435, \u0441\u043b\u0443\u0436\u0435\u0431\u043d\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u043d\u0430 Git \u043d\u0435 \u0435 \u0431\u0438\u043b.\n\n\u0422\u043e\u0432\u0430 \u0441\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u0435 \u0430\u0432\u0430\u0440\u0438\u0439\u043d\u043e \u0438 \u043d\u0435 \u0442\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0441\u0435 \u0441\u043b\u0443\u0447\u0432\u0430. \u041f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u201e%s\u201c \u0449\u0435 \u043f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u0438 \u0440\u0430\u0431\u043e\u0442\u0430."
::msgcat::mcset bg "%s (%s): Add Remote" "%s (%s): \u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Add New Remote" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Add" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435"
::msgcat::mcset bg "Remote Details" "\u0414\u0430\u043d\u043d\u0438 \u0437\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e\u0442\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Name:" "\u0418\u043c\u0435:"
::msgcat::mcset bg "Location:" "\u041c\u0435\u0441\u0442\u043e\u043f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435:"
::msgcat::mcset bg "Further Action" "\u0421\u043b\u0435\u0434\u0432\u0430\u0449\u043e \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435"
::msgcat::mcset bg "Fetch Immediately" "\u041d\u0435\u0437\u0430\u0431\u0430\u0432\u043d\u043e \u0434\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435"
::msgcat::mcset bg "Initialize Remote Repository and Push" "\u0418\u043d\u0438\u0446\u0438\u0430\u043b\u0438\u0437\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e\u0442\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u0438 \u0438\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435"
::msgcat::mcset bg "Do Nothing Else Now" "\u0414\u0430 \u043d\u0435 \u0441\u0435 \u043f\u0440\u0430\u0432\u0438 \u043d\u0438\u0449\u043e"
::msgcat::mcset bg "Please supply a remote name." "\u0417\u0430\u0434\u0430\u0439\u0442\u0435 \u0438\u043c\u0435 \u0437\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e\u0442\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435."
::msgcat::mcset bg "'%s' is not an acceptable remote name." "\u041e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043a\u0430\u0437\u0432\u0430 \u201e%s\u201c."
::msgcat::mcset bg "Failed to add remote '%s' of location '%s'." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0434\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e\u0442\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u201e%s\u201c \u043e\u0442 \u0430\u0434\u0440\u0435\u0441 \u201e%s\u201c."
::msgcat::mcset bg "Fetching the %s" "\u0414\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u201e%s\u201c"
::msgcat::mcset bg "Do not know how to initialize repository at location '%s'." "\u0425\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e \u0441 \u043c\u0435\u0441\u0442\u043e\u043f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u201e%s\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0438\u043d\u0438\u0446\u0438\u0430\u043b\u0438\u0437\u0438\u0440\u0430."
::msgcat::mcset bg "Setting up the %s (at %s)" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u201e%s\u201c (\u0441 \u0430\u0434\u0440\u0435\u0441 \u201e%s\u201c)"
::msgcat::mcset bg "Starting..." "\u0421\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "%s (%s): File Browser" "%s (%s): \u0424\u0430\u0439\u043b\u043e\u0432 \u0431\u0440\u0430\u0443\u0437\u044a\u0440"
::msgcat::mcset bg "Loading %s..." "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c\u2026"
::msgcat::mcset bg "\[Up To Parent\]" "\[\u041a\u044a\u043c \u0440\u043e\u0434\u0438\u0442\u0435\u043b\u044f\]"
::msgcat::mcset bg "%s (%s): Browse Branch Files" "%s (%s): \u0420\u0430\u0437\u0433\u043b\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435 \u0432 \u043a\u043b\u043e\u043d\u0430"
::msgcat::mcset bg "Browse Branch Files" "\u0420\u0430\u0437\u0433\u043b\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435 \u0432 \u043a\u043b\u043e\u043d\u0430"
::msgcat::mcset bg "Browse" "\u0420\u0430\u0437\u0433\u043b\u0435\u0436\u0434\u0430\u043d\u0435"
::msgcat::mcset bg "Revision" "\u0412\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset bg "Unable to unlock the index." "\u0418\u043d\u0434\u0435\u043a\u0441\u044a\u0442 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043e\u0442\u043a\u043b\u044e\u0447\u0438."
::msgcat::mcset bg "Index Error" "\u0413\u0440\u0435\u0448\u043a\u0430 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset bg "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u043d\u0430 Git. \u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u043d\u043e \u0449\u0435 \u0437\u0430\u043f\u043e\u0447\u043d\u0435 \u043d\u043e\u0432\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u0437\u0430 \u0441\u0438\u043d\u0445\u0440\u043e\u043d\u0438\u0437\u0438\u0440\u0430\u043d\u0435\u0442\u043e \u043d\u0430 git-gui."
::msgcat::mcset bg "Continue" "\u041f\u0440\u043e\u0434\u044a\u043b\u0436\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Unlock Index" "\u041e\u0442\u043a\u043b\u044e\u0447\u0432\u0430\u043d\u0435 \u043d\u0430 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset bg "files" "\u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "Unstaging selected files from commit" "\u0418\u0437\u0432\u0430\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0438\u0437\u0431\u0440\u0430\u043d\u0438\u0442\u0435 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u043e\u0442 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Unstaging %s from commit" "\u0418\u0437\u0432\u0430\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c \u043e\u0442 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Ready to commit." "\u0413\u043e\u0442\u043e\u0432\u043d\u043e\u0441\u0442 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "Adding selected files" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0438\u0437\u0431\u0440\u0430\u043d\u0438\u0442\u0435 \u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "Adding %s" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u201e%s\u201c"
::msgcat::mcset bg "Stage %d untracked files?" "\u0414\u0430 \u0441\u0435 \u0434\u043e\u0431\u0430\u0432\u044f\u0442 \u043b\u0438 %d \u043d\u0435\u0441\u043b\u0435\u0434\u0435\u043d\u0438 \u0444\u0430\u0439\u043b\u0430 \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430?"
::msgcat::mcset bg "Adding all changed files" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0432\u0441\u0438\u0447\u043a\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0435\u043d\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "Revert changes in file %s?" "\u0414\u0430 \u0441\u0435 \u043c\u0430\u0445\u043d\u0430\u0442 \u043b\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u0432\u044a\u0432 \u0444\u0430\u0439\u043b\u0430 \u201e%s\u201c?"
::msgcat::mcset bg "Revert changes in these %i files?" "\u0414\u0430 \u0441\u0435 \u043c\u0430\u0445\u043d\u0430\u0442 \u043b\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u0432 \u0442\u0435\u0437\u0438 %i \u0444\u0430\u0439\u043b\u0430?"
::msgcat::mcset bg "Any unstaged changes will be permanently lost by the revert." "\u0412\u0441\u0438\u0447\u043a\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438, \u043a\u043e\u0438\u0442\u043e \u043d\u0435 \u0441\u0430 \u0431\u0438\u043b\u0438 \u0434\u043e\u0431\u0430\u0432\u0435\u043d\u0438 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0430, \u0449\u0435 \u0441\u0435 \u0437\u0430\u0433\u0443\u0431\u044f\u0442 \u0431\u0435\u0437\u0432\u044a\u0437\u0432\u0440\u0430\u0442\u043d\u043e."
::msgcat::mcset bg "Do Nothing" "\u041d\u0438\u0449\u043e \u0434\u0430 \u043d\u0435 \u0441\u0435 \u043f\u0440\u0430\u0432\u0438"
::msgcat::mcset bg "Delete untracked file %s?" "\u0414\u0430 \u0441\u0435 \u0438\u0437\u0442\u0440\u0438\u0435 \u043b\u0438 \u043d\u0435\u0441\u043b\u0435\u0434\u0435\u043d\u0438\u044f\u0442 \u0444\u0430\u0439\u043b \u201e%s\u201c?"
::msgcat::mcset bg "Delete these %i untracked files?" "\u0414\u0430 \u0441\u0435 \u0438\u0437\u0442\u0440\u0438\u044f\u0442 \u043b\u0438 \u0442\u0435\u0437\u0438 %d \u043d\u0435\u0441\u043b\u0435\u0434\u0435\u043d\u0438 \u0444\u0430\u0439\u043b\u0430?"
::msgcat::mcset bg "Files will be permanently deleted." "\u0424\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435 \u0449\u0435 \u0441\u0435 \u0438\u0437\u0442\u0440\u0438\u044f\u0442 \u043e\u043a\u043e\u043d\u0447\u0430\u0442\u0435\u043b\u043d\u043e."
::msgcat::mcset bg "Delete Files" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "Deleting" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Encountered errors deleting files:\n" "\u0413\u0440\u0435\u0448\u043a\u0438 \u043f\u0440\u0438 \u0438\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435:\n"
::msgcat::mcset bg "None of the %d selected files could be deleted." "\u041d\u0438\u043a\u043e\u0439 \u043e\u0442 \u0438\u0437\u0431\u0440\u0430\u043d\u0438\u0442\u0435 %d \u0444\u0430\u0439\u043b\u0430 \u043d\u0435 \u0431\u0435 \u0438\u0437\u0442\u0440\u0438\u0442."
::msgcat::mcset bg "%d of the %d selected files could not be deleted." "%d \u043e\u0442 \u0438\u0437\u0431\u0440\u0430\u043d\u0438\u0442\u0435 %d \u0444\u0430\u0439\u043b\u0430 \u043d\u0435 \u0431\u044f\u0445\u0430 \u0438\u0437\u0442\u0440\u0438\u0442\u0438."
::msgcat::mcset bg "Reverting selected files" "\u041c\u0430\u0445\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u0432 \u0438\u0437\u0431\u0440\u0430\u043d\u0438\u0442\u0435 \u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "Reverting %s" "\u041c\u0430\u0445\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u0432 \u201e%s\u201c"
::msgcat::mcset bg "%s (%s): Checkout Branch" "%s (%s): \u041a\u043b\u043e\u043d \u0437\u0430 \u0438\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435"
::msgcat::mcset bg "Checkout Branch" "\u041a\u043b\u043e\u043d \u0437\u0430 \u0438\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435"
::msgcat::mcset bg "Checkout" "\u0418\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435"
::msgcat::mcset bg "Options" "\u041e\u043f\u0446\u0438\u0438"
::msgcat::mcset bg "Fetch Tracking Branch" "\u0418\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u043e\u0442 \u0441\u043b\u0435\u0434\u0435\u043d\u0438\u044f \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Detach From Local Branch" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043e\u0442 \u043b\u043e\u043a\u0430\u043b\u043d\u0438\u044f \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "%s ... %*i of %*i %s (%3i%%)" "%s\u2026 %*i \u043e\u0442 \u043e\u0431\u0449\u043e %*i %s (%3i%%)"
::msgcat::mcset bg "Push to" "\u0418\u0437\u0442\u043b\u0430\u0441\u043a\u0432\u0430\u043d\u0435 \u043a\u044a\u043c"
::msgcat::mcset bg "Remove Remote" "\u041f\u0440\u0435\u043c\u0430\u0445\u0432\u0430\u043d\u0435 \u043d\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Prune from" "\u041e\u043a\u0430\u0441\u0442\u0440\u044f\u043d\u0435 \u043e\u0442"
::msgcat::mcset bg "Fetch from" "\u0414\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435 \u043e\u0442"
::msgcat::mcset bg "All" "\u0412\u0441\u0438\u0447\u043a\u0438"
::msgcat::mcset bg "%s (%s): Rename Branch" "%s (%s): \u041f\u0440\u0435\u0438\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Rename Branch" "\u041f\u0440\u0435\u0438\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Rename" "\u041f\u0440\u0435\u0438\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Branch:" "\u041a\u043b\u043e\u043d:"
::msgcat::mcset bg "New Name:" "\u041d\u043e\u0432\u043e \u0438\u043c\u0435:"
::msgcat::mcset bg "Please select a branch to rename." "\u0418\u0437\u0431\u0435\u0440\u0435\u0442\u0435 \u043a\u043b\u043e\u043d \u0437\u0430 \u043f\u0440\u0435\u0438\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "Please supply a branch name." "\u0414\u0430\u0439\u0442\u0435 \u0438\u043c\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d\u0430."
::msgcat::mcset bg "'%s' is not an acceptable branch name." "\u201e%s\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0438\u0437\u043f\u043e\u043b\u0437\u0432\u0430 \u0437\u0430 \u0438\u043c\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d."
::msgcat::mcset bg "Failed to rename '%s'." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043f\u0440\u0435\u0438\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c."
::msgcat::mcset bg "Select" "\u0418\u0437\u0431\u043e\u0440"
::msgcat::mcset bg "Font Family" "\u0428\u0440\u0438\u0444\u0442"
::msgcat::mcset bg "Font Size" "\u0420\u0430\u0437\u043c\u0435\u0440"
::msgcat::mcset bg "Font Example" "\u041c\u043e\u0441\u0442\u0440\u0430"
::msgcat::mcset bg "This is example text.\nIf you like this text, it can be your font." "\u0422\u043e\u0432\u0430 \u0435 \u043f\u0440\u0438\u043c\u0435\u0440\u0435\u043d \u0442\u0435\u043a\u0441\u0442.\n\u0410\u043a\u043e \u0432\u0438 \u0445\u0430\u0440\u0435\u0441\u0432\u0430 \u043a\u0430\u043a \u0438\u0437\u0433\u043b\u0435\u0436\u0434\u0430, \u0438\u0437\u0431\u0435\u0440\u0435\u0442\u0435 \u0448\u0440\u0438\u0444\u0442\u0430."
::msgcat::mcset bg "Invalid global encoding '%s'" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u043d\u043e \u0433\u043b\u043e\u0431\u0430\u043b\u043d\u043e \u043a\u043e\u0434\u0438\u0440\u0430\u043d\u0435 \u201e%s\u201c"
::msgcat::mcset bg "Invalid repo encoding '%s'" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u043d\u043e \u043a\u043e\u0434\u0438\u0440\u0430\u043d\u0435 \u201e%s\u201c \u043d\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e"
::msgcat::mcset bg "Restore Defaults" "\u0421\u0442\u0430\u043d\u0434\u0430\u0440\u0442\u043d\u0438 \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438"
::msgcat::mcset bg "Save" "\u0417\u0430\u043f\u0430\u0437\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "%s Repository" "\u0425\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u201e%s\u201c"
::msgcat::mcset bg "Global (All Repositories)" "\u0413\u043b\u043e\u0431\u0430\u043b\u043d\u043e (\u0437\u0430 \u0432\u0441\u0438\u0447\u043a\u0438 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0430)"
::msgcat::mcset bg "User Name" "\u041f\u043e\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043b\u0441\u043a\u043e \u0438\u043c\u0435"
::msgcat::mcset bg "Email Address" "\u0410\u0434\u0440\u0435\u0441 \u043d\u0430 \u0435-\u043f\u043e\u0449\u0430"
::msgcat::mcset bg "Summarize Merge Commits" "\u041e\u0431\u043e\u0431\u0449\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430 \u043f\u0440\u0438 \u0441\u043b\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Merge Verbosity" "\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u043e\u0441\u0442\u0438 \u043f\u0440\u0438 \u0441\u043b\u0438\u0432\u0430\u043d\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "Show Diffstat After Merge" "\u0418\u0437\u0432\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043a\u0430 \u0441\u043b\u0435\u0434 \u0441\u043b\u0438\u0432\u0430\u043d\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "Use Merge Tool" "\u0418\u0437\u043f\u043e\u043b\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Trust File Modification Timestamps" "\u0414\u043e\u0432\u0435\u0440\u0438\u0435 \u0432\u044a\u0432 \u0432\u0440\u0435\u043c\u0435\u0442\u043e \u043d\u0430 \u043f\u0440\u043e\u043c\u044f\u043d\u0430 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435"
::msgcat::mcset bg "Prune Tracking Branches During Fetch" "\u041e\u043a\u0430\u0441\u0442\u0440\u044f\u043d\u0435 \u043d\u0430 \u0441\u043b\u0435\u0434\u044f\u0449\u0438\u0442\u0435 \u043a\u043b\u043e\u043d\u043e\u0432\u0435 \u043f\u0440\u0438 \u0434\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435"
::msgcat::mcset bg "Match Tracking Branches" "\u041d\u0430\u043f\u0430\u0441\u0432\u0430\u043d\u0435 \u043d\u0430 \u0441\u043b\u0435\u0434\u044f\u0449\u0438\u0442\u0435 \u043a\u043b\u043e\u043d\u043e\u0432\u0435"
::msgcat::mcset bg "Use Textconv For Diffs and Blames" "\u0418\u0437\u043f\u043e\u043b\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u201etextconv\u201c \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435 \u0438 \u0430\u043d\u043e\u0442\u0438\u0440\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Blame Copy Only On Changed Files" "\u0410\u043d\u043e\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043f\u0438\u0435\u0442\u043e \u0441\u0430\u043c\u043e \u043f\u043e \u043f\u0440\u043e\u043c\u0435\u043d\u0435\u043d\u0438\u0442\u0435 \u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "Maximum Length of Recent Repositories List" "\u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u0435\u043d \u0431\u0440\u043e\u0439 \u043d\u0430 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u201e\u0421\u043a\u043e\u0440\u043e \u043f\u043e\u043b\u0437\u0432\u0430\u043d\u0438\u201c \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0430"
::msgcat::mcset bg "Minimum Letters To Blame Copy On" "\u041c\u0438\u043d\u0438\u043c\u0430\u043b\u0435\u043d \u0431\u0440\u043e\u0439 \u0437\u043d\u0430\u0446\u0438 \u0437\u0430 \u0430\u043d\u043e\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043f\u0438\u0435\u0442\u043e"
::msgcat::mcset bg "Blame History Context Radius (days)" "\u0418\u0441\u0442\u043e\u0440\u0438\u0447\u0435\u0441\u043a\u0438 \u043e\u0431\u0445\u0432\u0430\u0442 \u0437\u0430 \u0430\u043d\u043e\u0442\u0438\u0440\u0430\u043d\u0435 \u0432 \u0434\u043d\u0438"
::msgcat::mcset bg "Number of Diff Context Lines" "\u0411\u0440\u043e\u0439 \u0440\u0435\u0434\u043e\u0432\u0435 \u0437\u0430 \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442\u0430 \u043d\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435"
::msgcat::mcset bg "Additional Diff Parameters" "\u0410\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u0438 \u043a\u044a\u043c \u043a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435"
::msgcat::mcset bg "Commit Message Text Width" "\u0428\u0438\u0440\u043e\u0447\u0438\u043d\u0430 \u043d\u0430 \u0442\u0435\u043a\u0441\u0442\u0430 \u043d\u0430 \u0441\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435\u0442\u043e \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "New Branch Name Template" "\u0428\u0430\u0431\u043b\u043e\u043d \u0437\u0430 \u0438\u043c\u0435\u0442\u043e \u043d\u0430 \u043d\u043e\u0432\u0438\u0442\u0435 \u043a\u043b\u043e\u043d\u0438"
::msgcat::mcset bg "Default File Contents Encoding" "\u041a\u043e\u0434\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435"
::msgcat::mcset bg "Warn before committing to a detached head" "\u041f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0430\u0432\u0430\u043d\u0435 \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u043a\u044a\u043c \u043d\u0435\u0441\u0432\u044a\u0440\u0437\u0430\u043d \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b"
::msgcat::mcset bg "Staging of untracked files" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043d\u0435\u0441\u043b\u0435\u0434\u0435\u043d\u0438\u0442\u0435 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset bg "Show untracked files" "\u041f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043d\u0435\u0441\u043b\u0435\u0434\u0435\u043d\u0438\u0442\u0435 \u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "Tab spacing" "\u0428\u0438\u0440\u0438\u043d\u0430 \u043d\u0430 \u0442\u0430\u0431\u0443\u043b\u0430\u0446\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "%s:" "%s:"
::msgcat::mcset bg "Change" "\u0421\u043c\u044f\u043d\u0430"
::msgcat::mcset bg "Spelling Dictionary:" "\u041f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0435\u043d \u0440\u0435\u0447\u043d\u0438\u043a:"
::msgcat::mcset bg "Change Font" "\u0421\u043c\u044f\u043d\u0430 \u043d\u0430 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset bg "Choose %s" "\u0418\u0437\u0431\u043e\u0440 \u043d\u0430 \u201e%s\u201c"
::msgcat::mcset bg "pt." "\u0442\u0447\u043a."
::msgcat::mcset bg "Preferences" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438"
::msgcat::mcset bg "Failed to completely save options:" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0437\u0430\u043f\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438\u0442\u0435:"
::msgcat::mcset bg "Default" "\u0421\u0442\u0430\u043d\u0434\u0430\u0440\u0442\u043d\u043e\u0442\u043e"
::msgcat::mcset bg "System (%s)" "\u0421\u0438\u0441\u0442\u0435\u043c\u043d\u043e\u0442\u043e (%s)"
::msgcat::mcset bg "Other" "\u0414\u0440\u0443\u0433\u043e"
::msgcat::mcset bg "Running %s requires a selected file." "\u0417\u0430 \u0438\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435\u0442\u043e \u043d\u0430 \u201e%s\u201c \u0442\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0438\u0437\u0431\u0435\u0440\u0435\u0442\u0435 \u0444\u0430\u0439\u043b."
::msgcat::mcset bg "Are you sure you want to run %1\$s on file \"%2\$s\"?" "\u0421\u0438\u0433\u0443\u0440\u043d\u0438 \u043b\u0438 \u0441\u0442\u0435, \u0447\u0435 \u0438\u0441\u043a\u0430\u0442\u0435 \u0434\u0430 \u0438\u0437\u043f\u044a\u043b\u043d\u0438\u0442\u0435 \u201e%1\$s\u201c \u0432\u044a\u0440\u0445\u0443 \u0444\u0430\u0439\u043b\u0430 \u201e%2\$s\u201c?"
::msgcat::mcset bg "Are you sure you want to run %s?" "\u0421\u0438\u0433\u0443\u0440\u043d\u0438 \u043b\u0438 \u0441\u0442\u0435, \u0447\u0435 \u0438\u0441\u043a\u0430\u0442\u0435 \u0434\u0430 \u0438\u0437\u043f\u044a\u043b\u043d\u0438\u0442\u0435 \u201e%s\u201c?"
::msgcat::mcset bg "Tool: %s" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430: %s"
::msgcat::mcset bg "Running: %s" "\u0418\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435: %s"
::msgcat::mcset bg "Tool completed successfully: %s" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u0437\u0430\u0432\u044a\u0440\u0448\u0438 \u0443\u0441\u043f\u0435\u0448\u043d\u043e: %s"
::msgcat::mcset bg "Tool failed: %s" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u0432\u044a\u0440\u043d\u0430 \u0433\u0440\u0435\u0448\u043a\u0430: %s"
::msgcat::mcset bg "Force resolution to the base version?" "\u0414\u0430 \u0441\u0435 \u0438\u0437\u043f\u043e\u043b\u0437\u0432\u0430 \u0431\u0430\u0437\u043e\u0432\u0430\u0442\u0430 \u0432\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset bg "Force resolution to this branch?" "\u0414\u0430 \u0441\u0435 \u0438\u0437\u043f\u043e\u043b\u0437\u0432\u0430 \u0432\u0435\u0440\u0441\u0438\u044f\u0442\u0430 \u043e\u0442 \u0442\u043e\u0437\u0438 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Force resolution to the other branch?" "\u0414\u0430 \u0441\u0435 \u0438\u0437\u043f\u043e\u043b\u0437\u0432\u0430 \u0432\u0435\u0440\u0441\u0438\u044f\u0442\u0430 \u043e\u0442 \u0434\u0440\u0443\u0433\u0438\u044f \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "\u0420\u0430\u0437\u043b\u0438\u043a\u0430\u0442\u0430 \u043f\u043e\u043a\u0430\u0437\u0432\u0430 \u0441\u0430\u043c\u043e \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435 \u0441 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442.\n\n\u0424\u0430\u0439\u043b\u044a\u0442 \u201e%s\u201c \u0449\u0435 \u0441\u0435 \u043f\u0440\u0435\u0437\u0430\u043f\u0438\u0448\u0435.\n\n\u0422\u0430\u0437\u0438 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044f \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043e\u0442\u043c\u0435\u043d\u0438 \u0441\u0430\u043c\u043e \u0447\u0440\u0435\u0437 \u0437\u0430\u043f\u043e\u0447\u0432\u0430\u043d\u0435 \u043d\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430\u043d\u043e\u0432\u043e."
::msgcat::mcset bg "File %s seems to have unresolved conflicts, still stage?" "\u0418\u0437\u0433\u043b\u0435\u0436\u0434\u0430, \u0447\u0435 \u0432\u0441\u0435 \u043e\u0449\u0435 \u0438\u043c\u0430 \u043d\u0435\u043a\u043e\u0440\u0438\u0433\u0438\u0440\u0430\u043d\u0438 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438 \u0432\u044a\u0432 \u0444\u0430\u0439\u043b\u0430 \u201e%s\u201c. \u0414\u0430 \u0441\u0435 \u0434\u043e\u0431\u0430\u0432\u0438 \u043b\u0438 \u0444\u0430\u0439\u043b\u044a\u0442 \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430?"
::msgcat::mcset bg "Adding resolution for %s" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043a\u043e\u0440\u0435\u043a\u0446\u0438\u044f \u043d\u0430 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438\u0442\u0435 \u0432 \u201e%s\u201c"
::msgcat::mcset bg "Cannot resolve deletion or link conflicts using a tool" "\u041a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438\u0442\u0435 \u043f\u0440\u0438 \u0441\u0438\u043c\u0432\u043e\u043b\u043d\u0438 \u0432\u0440\u044a\u0437\u043a\u0438 \u0438\u043b\u0438 \u0438\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043a\u043e\u0440\u0438\u0433\u0438\u0440\u0430\u0442 \u0441 \u0432\u044a\u043d\u0448\u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430."
::msgcat::mcset bg "Conflict file does not exist" "\u0424\u0430\u0439\u043b\u044a\u0442, \u0432 \u043a\u043e\u0439\u0442\u043e \u0435 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u044a\u0442, \u043d\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430"
::msgcat::mcset bg "Not a GUI merge tool: '%s'" "\u0422\u043e\u0432\u0430 \u043d\u0435 \u0435 \u0433\u0440\u0430\u0444\u0438\u0447\u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435: \u201e%s\u201c"
::msgcat::mcset bg "Unsupported merge tool '%s'" "\u041d\u0435\u043f\u043e\u0434\u0434\u044a\u0440\u0436\u0430\u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435: \u201e%s\u201c"
::msgcat::mcset bg "Merge tool is already running, terminate it?" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435 \u0432\u0435\u0447\u0435 \u0435 \u0441\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u043d\u0430. \u0414\u0430 \u0441\u0435 \u0438\u0437\u043a\u043b\u044e\u0447\u0438 \u043b\u0438?"
::msgcat::mcset bg "Error retrieving versions:\n%s" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0438\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435\u0442\u043e \u043d\u0430 \u0432\u0435\u0440\u0441\u0438\u0438:\n%s"
::msgcat::mcset bg "Could not start the merge tool:\n\n%s" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0441\u0442\u0430\u0440\u0442\u0438\u0440\u0430:\n\n%s"
::msgcat::mcset bg "Running merge tool..." "\u0421\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Merge tool failed." "\u0413\u0440\u0435\u0448\u043a\u0430 \u0432 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "%s (%s): Add Tool" "%s (%s): \u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430"
::msgcat::mcset bg "Add New Tool Command" "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430"
::msgcat::mcset bg "Add globally" "\u0413\u043b\u043e\u0431\u0430\u043b\u043d\u043e \u0434\u043e\u0431\u0430\u0432\u044f\u043d\u0435"
::msgcat::mcset bg "Tool Details" "\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u043e\u0441\u0442\u0438 \u0437\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430"
::msgcat::mcset bg "Use '/' separators to create a submenu tree:" "\u0417\u0430 \u0441\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u043e\u0434\u043c\u0435\u043d\u044e\u0442\u0430 \u0438\u0437\u043f\u043e\u043b\u0437\u0432\u0430\u0439\u0442\u0435 \u0437\u043d\u0430\u043a\u0430 \u201e/\u201c \u0437\u0430 \u0440\u0430\u0437\u0434\u0435\u043b\u0438\u0442\u0435\u043b:"
::msgcat::mcset bg "Command:" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430:"
::msgcat::mcset bg "Show a dialog before running" "\u041f\u0440\u0435\u0434\u0438 \u0438\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u0434\u0430 \u0441\u0435 \u0438\u0437\u0432\u0435\u0436\u0434\u0430 \u0434\u0438\u0430\u043b\u043e\u0433\u043e\u0432 \u043f\u0440\u043e\u0437\u043e\u0440\u0435\u0446"
::msgcat::mcset bg "Ask the user to select a revision (sets \$REVISION)" "\u041f\u043e\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043b\u044f\u0442 \u0434\u0430 \u0443\u043a\u0430\u0436\u0435 \u0432\u0435\u0440\u0441\u0438\u044f (\u0437\u0430\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u043b\u0438\u0432\u0430\u0442\u0430 \$REVISION)"
::msgcat::mcset bg "Ask the user for additional arguments (sets \$ARGS)" "\u041f\u043e\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043b\u044f\u0442 \u0434\u0430 \u0443\u043a\u0430\u0436\u0435 \u0434\u043e\u043f\u044a\u043b\u043d\u0438\u0442\u0435\u043b\u043d\u0438 \u0430\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u0438 (\u0437\u0430\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u043b\u0438\u0432\u0430\u0442\u0430 \$ARGS)"
::msgcat::mcset bg "Don't show the command output window" "\u0411\u0435\u0437 \u043f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u0437\u043e\u0440\u0435\u0446 \u0441 \u0438\u0437\u0445\u043e\u0434\u0430 \u043e\u0442 \u043a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430"
::msgcat::mcset bg "Run only if a diff is selected (\$FILENAME not empty)" "\u0421\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u043d\u0435 \u0441\u0430\u043c\u043e \u0441\u043b\u0435\u0434 \u0438\u0437\u0431\u043e\u0440 \u043d\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0430 (\u043f\u0440\u043e\u043c\u0435\u043d\u043b\u0438\u0432\u0430\u0442\u0430 \$FILENAME \u043d\u0435 \u0435 \u043f\u0440\u0430\u0437\u043d\u0430)"
::msgcat::mcset bg "Please supply a name for the tool." "\u0417\u0430\u0434\u0430\u0439\u0442\u0435 \u0438\u043c\u0435 \u0437\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430."
::msgcat::mcset bg "Tool '%s' already exists." "\u041a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u201e%s\u201c \u0432\u0435\u0447\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430."
::msgcat::mcset bg "Could not add tool:\n%s" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0434\u043e\u0431\u0430\u0432\u0438:\n%s"
::msgcat::mcset bg "%s (%s): Remove Tool" "%s (%s): \u041f\u0440\u0435\u043c\u0430\u0445\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430"
::msgcat::mcset bg "Remove Tool Commands" "\u041f\u0440\u0435\u043c\u0430\u0445\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0438"
::msgcat::mcset bg "Remove" "\u041f\u0440\u0435\u043c\u0430\u0445\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "(Blue denotes repository-local tools)" "(\u043a\u043e\u043c\u0430\u043d\u0434\u0438\u0442\u0435 \u043a\u044a\u043c \u043b\u043e\u043a\u0430\u043b\u043d\u043e\u0442\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u0441\u0430 \u043e\u0431\u043e\u0437\u043d\u0430\u0447\u0435\u043d\u0438 \u0432 \u0441\u0438\u043d\u044c\u043e)"
::msgcat::mcset bg "%s (%s):" "%s (%s):"
::msgcat::mcset bg "Run Command: %s" "\u0418\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u043d\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u201e%s\u201c"
::msgcat::mcset bg "Arguments" "\u0410\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u0438"
::msgcat::mcset bg "OK" "\u0414\u043e\u0431\u0440\u0435"
::msgcat::mcset bg "Find:" "\u0422\u044a\u0440\u0441\u0435\u043d\u0435:"
::msgcat::mcset bg "Next" "\u0421\u043b\u0435\u0434\u0432\u0430\u0449\u0430 \u043f\u043e\u044f\u0432\u0430"
::msgcat::mcset bg "Prev" "\u041f\u0440\u0435\u0434\u0438\u0448\u043d\u0430 \u043f\u043e\u044f\u0432\u0430"
::msgcat::mcset bg "RegExp" "\u0420\u0435\u0433\u0418\u0437\u0440"
::msgcat::mcset bg "Case" "\u0413\u043b\u0430\u0432\u043d\u0438/\u041c\u0430\u043b\u043a\u0438"
::msgcat::mcset bg "%s (%s): Create Desktop Icon" "%s (%s): \u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0438\u043a\u043e\u043d\u0430 \u043d\u0430 \u0440\u0430\u0431\u043e\u0442\u043d\u0438\u044f \u043f\u043b\u043e\u0442"
::msgcat::mcset bg "Cannot write shortcut:" "\u041a\u043b\u0430\u0432\u0438\u0448\u043d\u0430\u0442\u0430 \u043a\u043e\u043c\u0431\u0438\u043d\u0430\u0446\u0438\u044f \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0437\u0430\u043f\u0430\u0437\u0438:"
::msgcat::mcset bg "Cannot write icon:" "\u0418\u043a\u043e\u043d\u0430\u0442\u0430 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0437\u0430\u043f\u0430\u0437\u0438:"
::msgcat::mcset bg "%s (%s): Delete Branch Remotely" "%s (%s): \u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u0438\u044f \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Delete Branch Remotely" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u0438\u044f \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "From Repository" "\u041e\u0442 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Branches" "\u041a\u043b\u043e\u043d\u0438"
::msgcat::mcset bg "Delete Only If" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435, \u0441\u0430\u043c\u043e \u0430\u043a\u043e"
::msgcat::mcset bg "Merged Into:" "\u0421\u043b\u044f\u0442 \u0432:"
::msgcat::mcset bg "Always (Do not perform merge checks)" "\u0412\u0438\u043d\u0430\u0433\u0438 (\u0431\u0435\u0437 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435)"
::msgcat::mcset bg "A branch is required for 'Merged Into'." "\u0417\u0430 \u0434\u0430\u043d\u043d\u0438\u0442\u0435 \u201e\u0421\u043b\u044f\u0442 \u0432\u201c \u0435 \u043d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u043e \u0434\u0430 \u0437\u0430\u0434\u0430\u0434\u0435\u0442\u0435 \u043a\u043b\u043e\u043d."
::msgcat::mcset bg "The following branches are not completely merged into %s:\n\n - %s" "\u0421\u043b\u0435\u0434\u043d\u0438\u0442\u0435 \u043a\u043b\u043e\u043d\u0438 \u043d\u0435 \u0441\u0430 \u0441\u043b\u0435\u0442\u0438 \u043d\u0430\u043f\u044a\u043b\u043d\u043e \u0432 \u201e%s\u201c:\n\n \u25cf %s"
::msgcat::mcset bg "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "\u041f\u043e\u043d\u0435 \u0435\u0434\u043d\u0430 \u043e\u0442 \u043f\u0440\u043e\u0431\u0438\u0442\u0435 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435 \u0435 \u043d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u0430, \u0437\u0430\u0449\u043e\u0442\u043e \u043d\u0435 \u0441\u0442\u0435 \u0434\u043e\u0441\u0442\u0430\u0432\u0438\u043b\u0438 \u0432\u0441\u0438\u0447\u043a\u0438 \u043d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f. \u041f\u0440\u043e\u0431\u0432\u0430\u0439\u0442\u0435 \u043f\u044a\u0440\u0432\u043e \u0434\u0430 \u0434\u043e\u0441\u0442\u0430\u0432\u0438\u0442\u0435 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430 \u043e\u0442 \u201e%s\u201c."
::msgcat::mcset bg "Please select one or more branches to delete." "\u0418\u0437\u0431\u0435\u0440\u0435\u0442\u0435 \u043f\u043e\u043d\u0435 \u0435\u0434\u0438\u043d \u043a\u043b\u043e\u043d \u0437\u0430 \u0438\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "\u0412\u044a\u0437\u0441\u0442\u0430\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0438\u0437\u0442\u0440\u0438\u0442\u0438 \u043a\u043b\u043e\u043d\u0438 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0435 \u0442\u0440\u0443\u0434\u043d\u043e.\n\n\u0421\u0438\u0433\u0443\u0440\u043d\u0438 \u043b\u0438 \u0441\u0442\u0435, \u0447\u0435 \u0438\u0441\u043a\u0430\u0442\u0435 \u0434\u0430 \u0442\u0440\u0438\u0435\u0442\u0435?"
::msgcat::mcset bg "Deleting branches from %s" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d\u0438 \u043e\u0442 \u201e%s\u201c"
::msgcat::mcset bg "No repository selected." "\u041d\u0435 \u0435 \u0438\u0437\u0431\u0440\u0430\u043d\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435."
::msgcat::mcset bg "Scanning %s..." "\u041f\u0440\u0435\u0442\u044a\u0440\u0441\u0432\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c\u2026"
::msgcat::mcset bg "Git Gui" "\u0413\u041f\u0418 \u043d\u0430 Git"
::msgcat::mcset bg "Create New Repository" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043d\u043e\u0432\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "New..." "\u041d\u043e\u0432\u043e\u2026"
::msgcat::mcset bg "Clone Existing Repository" "\u041a\u043b\u043e\u043d\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430\u0449\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Clone..." "\u041a\u043b\u043e\u043d\u0438\u0440\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Open Existing Repository" "\u041e\u0442\u0432\u0430\u0440\u044f\u043d\u0435 \u043d\u0430 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430\u0449\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435"
::msgcat::mcset bg "Open..." "\u041e\u0442\u0432\u0430\u0440\u044f\u043d\u0435\u2026"
::msgcat::mcset bg "Recent Repositories" "\u0421\u043a\u043e\u0440\u043e \u043f\u043e\u043b\u0437\u0432\u0430\u043d\u0438"
::msgcat::mcset bg "Open Recent Repository:" "\u041e\u0442\u0432\u0430\u0440\u044f\u043d\u0435 \u043d\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u043f\u043e\u043b\u0437\u0432\u0430\u043d\u043e \u043d\u0430\u0441\u043a\u043e\u0440\u043e:"
::msgcat::mcset bg "Failed to create repository %s:" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0441\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e \u201e%s\u201c:"
::msgcat::mcset bg "Create" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Directory:" "\u0414\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f:"
::msgcat::mcset bg "Git Repository" "\u0425\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u043d\u0430 Git"
::msgcat::mcset bg "Directory %s already exists." "\u0412\u0435\u0447\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f \u201e%s\u201c."
::msgcat::mcset bg "File %s already exists." "\u0412\u0435\u0447\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430 \u0444\u0430\u0439\u043b \u201e%s\u201c."
::msgcat::mcset bg "Clone" "\u041a\u043b\u043e\u043d\u0438\u0440\u0430\u043d\u0435"
::msgcat::mcset bg "Source Location:" "\u0410\u0434\u0440\u0435\u0441 \u043d\u0430 \u0438\u0437\u0442\u043e\u0447\u043d\u0438\u043a\u0430:"
::msgcat::mcset bg "Target Directory:" "\u0426\u0435\u043b\u0435\u0432\u0430 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f:"
::msgcat::mcset bg "Clone Type:" "\u0412\u0438\u0434 \u043a\u043b\u043e\u043d\u0438\u0440\u0430\u043d\u0435:"
::msgcat::mcset bg "Standard (Fast, Semi-Redundant, Hardlinks)" "\u0421\u0442\u0430\u043d\u0434\u0430\u0440\u0442\u043d\u043e (\u0431\u044a\u0440\u0437\u043e, \u0447\u0430\u0441\u0442\u0438\u0447\u043d\u043e \u0441\u043f\u043e\u0434\u0435\u043b\u044f\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435, \u0442\u0432\u044a\u0440\u0434\u0438 \u0432\u0440\u044a\u0437\u043a\u0438)"
::msgcat::mcset bg "Full Copy (Slower, Redundant Backup)" "\u041f\u044a\u043b\u043d\u043e (\u0431\u0430\u0432\u043d\u043e, \u043f\u044a\u043b\u043d\u043e\u0446\u0435\u043d\u043d\u043e \u0440\u0435\u0437\u0435\u0440\u0432\u043d\u043e \u043a\u043e\u043f\u0438\u0435)"
::msgcat::mcset bg "Shared (Fastest, Not Recommended, No Backup)" "\u0421\u043f\u043e\u0434\u0435\u043b\u0435\u043d\u043e (\u043d\u0430\u0439-\u0431\u044a\u0440\u0437\u043e, \u043d\u0435 \u0441\u0435 \u043f\u0440\u0435\u043f\u043e\u0440\u044a\u0447\u0432\u0430, \u043d\u0435 \u043f\u0440\u0430\u0432\u0438 \u0440\u0435\u0437\u0435\u0440\u0432\u043d\u043e \u043a\u043e\u043f\u0438\u0435)"
::msgcat::mcset bg "Recursively clone submodules too" "\u0420\u0435\u043a\u0443\u0440\u0441\u0438\u0432\u043d\u043e \u043a\u043b\u043e\u043d\u0438\u0440\u0430\u043d\u0435 \u0438 \u043d\u0430 \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0438\u0442\u0435"
::msgcat::mcset bg "Not a Git repository: %s" "\u0422\u043e\u0432\u0430 \u043d\u0435 \u0435 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u043d\u0430 Git: %s"
::msgcat::mcset bg "Standard only available for local repository." "\u0421\u0430\u043c\u043e \u043b\u043e\u043a\u0430\u043b\u043d\u0438 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0430 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043a\u043b\u043e\u043d\u0438\u0440\u0430\u0442 \u0441\u0442\u0430\u043d\u0434\u0430\u0440\u0442\u043d\u043e"
::msgcat::mcset bg "Shared only available for local repository." "\u0421\u0430\u043c\u043e \u043b\u043e\u043a\u0430\u043b\u043d\u0438 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0430 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043a\u043b\u043e\u043d\u0438\u0440\u0430\u0442 \u0441\u043f\u043e\u0434\u0435\u043b\u0435\u043d\u043e"
::msgcat::mcset bg "Location %s already exists." "\u041c\u0435\u0441\u0442\u043e\u043f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435\u0442\u043e \u201e%s\u201c \u0432\u0435\u0447\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430."
::msgcat::mcset bg "Failed to configure origin" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u0432\u0430\u043d\u0435 \u043d\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e-\u0438\u0437\u0442\u043e\u0447\u043d\u0438\u043a"
::msgcat::mcset bg "Counting objects" "\u041f\u0440\u0435\u0431\u0440\u043e\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u043e\u0431\u0435\u043a\u0442\u0438"
::msgcat::mcset bg "buckets" "\u043a\u043b\u0435\u0442\u043a\u0438"
::msgcat::mcset bg "Unable to copy objects/info/alternates: %s" "\u041e\u0431\u0435\u043a\u0442\u0438\u0442\u0435/\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f\u0442\u0430/\u0421\u0438\u043d\u043e\u043d\u0438\u043c\u0438\u0442\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043a\u043e\u043f\u0438\u0440\u0430\u0442: %s"
::msgcat::mcset bg "Nothing to clone from %s." "\u041d\u044f\u043c\u0430 \u043a\u0430\u043a\u0432\u043e \u0434\u0430 \u0441\u0435 \u043a\u043b\u043e\u043d\u0438\u0440\u0430 \u043e\u0442 \u201e%s\u201c."
::msgcat::mcset bg "The 'master' branch has not been initialized." "\u041e\u0441\u043d\u043e\u0432\u043d\u0438\u044f\u0442 \u043a\u043b\u043e\u043d \u2014 \u201emaster\u201c \u043d\u0435 \u0435 \u0438\u043d\u0438\u0446\u0438\u0430\u043b\u0438\u0437\u0438\u0440\u0430\u043d."
::msgcat::mcset bg "Hardlinks are unavailable.  Falling back to copying." "\u041d\u0435 \u0441\u0435 \u043f\u043e\u0434\u0434\u044a\u0440\u0436\u0430\u0442 \u0442\u0432\u044a\u0440\u0434\u0438 \u0432\u0440\u044a\u0437\u043a\u0438. \u041f\u0440\u0435\u043c\u0438\u043d\u0430\u0432\u0430 \u0441\u0435 \u043a\u044a\u043c \u043a\u043e\u043f\u0438\u0440\u0430\u043d\u0435."
::msgcat::mcset bg "Cloning from %s" "\u041a\u043b\u043e\u043d\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c"
::msgcat::mcset bg "Copying objects" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043e\u0431\u0435\u043a\u0442\u0438"
::msgcat::mcset bg "KiB" "KiB"
::msgcat::mcset bg "Unable to copy object: %s" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043e\u0431\u0435\u043a\u0442: %s"
::msgcat::mcset bg "Linking objects" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u0432\u0440\u044a\u0437\u043a\u0438 \u043a\u044a\u043c \u043e\u0431\u0435\u043a\u0442\u0438\u0442\u0435"
::msgcat::mcset bg "objects" "\u043e\u0431\u0435\u043a\u0442\u0438"
::msgcat::mcset bg "Unable to hardlink object: %s" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0441\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u0442\u0432\u044a\u0440\u0434\u0430 \u0432\u0440\u044a\u0437\u043a\u0430 \u043a\u044a\u043c \u043e\u0431\u0435\u043a\u0442: %s"
::msgcat::mcset bg "Cannot fetch branches and objects.  See console output for details." "\u041a\u043b\u043e\u043d\u0438\u0442\u0435 \u0438 \u043e\u0431\u0435\u043a\u0442\u0438\u0442\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0438\u0437\u0442\u0435\u0433\u043b\u044f\u0442. \u0417\u0430 \u043f\u043e\u0432\u0435\u0447\u0435 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043f\u043e\u0433\u043b\u0435\u0434\u043d\u0435\u0442\u0435 \u0438\u0437\u0445\u043e\u0434\u0430 \u043d\u0430 \u043a\u043e\u043d\u0437\u043e\u043b\u0430\u0442\u0430."
::msgcat::mcset bg "Cannot fetch tags.  See console output for details." "\u0415\u0442\u0438\u043a\u0435\u0442\u0438\u0442\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0438\u0437\u0442\u0435\u0433\u043b\u044f\u0442. \u0417\u0430 \u043f\u043e\u0432\u0435\u0447\u0435 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043f\u043e\u0433\u043b\u0435\u0434\u043d\u0435\u0442\u0435 \u0438\u0437\u0445\u043e\u0434\u0430 \u043d\u0430 \u043a\u043e\u043d\u0437\u043e\u043b\u0430\u0442\u0430."
::msgcat::mcset bg "Cannot determine HEAD.  See console output for details." "\u0412\u044a\u0440\u0445\u044a\u0442 \u201eHEAD\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0438. \u0417\u0430 \u043f\u043e\u0432\u0435\u0447\u0435 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043f\u043e\u0433\u043b\u0435\u0434\u043d\u0435\u0442\u0435 \u0438\u0437\u0445\u043e\u0434\u0430 \u043d\u0430 \u043a\u043e\u043d\u0437\u043e\u043b\u0430\u0442\u0430."
::msgcat::mcset bg "Unable to cleanup %s" "\u201e%s\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0438\u0437\u0447\u0438\u0441\u0442\u0438"
::msgcat::mcset bg "Clone failed." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043a\u043b\u043e\u043d\u0438\u0440\u0430\u043d\u0435."
::msgcat::mcset bg "No default branch obtained." "\u041d\u0435 \u0435 \u043f\u043e\u043b\u0443\u0447\u0435\u043d \u043a\u043b\u043e\u043d \u043f\u043e \u043f\u043e\u0434\u0440\u0430\u0437\u0431\u0438\u0440\u0430\u043d\u0435."
::msgcat::mcset bg "Cannot resolve %s as a commit." "\u041d\u044f\u043c\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u043e\u0442\u0433\u043e\u0432\u0430\u0440\u044f\u0449\u043e \u043d\u0430 \u201e%s\u201c."
::msgcat::mcset bg "Creating working directory" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u0440\u0430\u0431\u043e\u0442\u043d\u0430\u0442\u0430 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f"
::msgcat::mcset bg "Initial file checkout failed." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043f\u044a\u0440\u0432\u043e\u043d\u0430\u0447\u0430\u043b\u043d\u043e \u0438\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435."
::msgcat::mcset bg "Cloning submodules" "\u041a\u043b\u043e\u043d\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0438"
::msgcat::mcset bg "Cannot clone submodules." "\u041f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0438\u0442\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043a\u043b\u043e\u043d\u0438\u0440\u0430\u0442."
::msgcat::mcset bg "Repository:" "\u0425\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435:"
::msgcat::mcset bg "Failed to open repository %s:" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043e\u0442\u0432\u0430\u0440\u044f\u043d\u0435 \u043d\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e \u201e%s\u201c:"
::msgcat::mcset bg "git-gui - a graphical user interface for Git." "git-gui \u2014 \u0433\u0440\u0430\u0444\u0438\u0447\u0435\u043d \u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441 \u0437\u0430 Git."
::msgcat::mcset bg "%s (%s): File Viewer" "%s (%s): \u041f\u0440\u0435\u0433\u043b\u0435\u0434 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "Commit:" "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Copy Commit" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Find Text..." "\u0422\u044a\u0440\u0441\u0435\u043d\u0435 \u043d\u0430 \u0442\u0435\u043a\u0441\u0442\u2026"
::msgcat::mcset bg "Goto Line..." "\u041a\u044a\u043c \u0440\u0435\u0434\u2026"
::msgcat::mcset bg "Do Full Copy Detection" "\u041f\u044a\u043b\u043d\u043e \u0442\u044a\u0440\u0441\u0435\u043d\u0435 \u043d\u0430 \u043a\u043e\u043f\u0438\u0440\u0430\u043d\u0435"
::msgcat::mcset bg "Show History Context" "\u041f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442\u0430 \u043e\u0442 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "Blame Parent Commit" "\u0410\u043d\u043e\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0440\u043e\u0434\u0438\u0442\u0435\u043b\u0441\u043a\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Reading %s..." "\u0427\u0435\u0442\u0435 \u0441\u0435 \u201e%s\u201c\u2026"
::msgcat::mcset bg "Loading copy/move tracking annotations..." "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0430\u043d\u043e\u0442\u0430\u0446\u0438\u0438\u0442\u0435 \u0437\u0430 \u043f\u0440\u043e\u0441\u043b\u0435\u0434\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043f\u0438\u0440\u0430\u043d\u0435\u0442\u043e/\u043f\u0440\u0435\u043c\u0435\u0441\u0442\u0432\u0430\u043d\u0435\u0442\u043e\u2026"
::msgcat::mcset bg "lines annotated" "\u0440\u0435\u0434\u0430 \u0430\u043d\u043e\u0442\u0438\u0440\u0430\u043d\u0438"
::msgcat::mcset bg "Loading original location annotations..." "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0430\u043d\u043e\u0442\u0430\u0446\u0438\u0438\u0442\u0435 \u0437\u0430 \u043f\u044a\u0440\u0432\u043e\u043d\u0430\u0447\u0430\u043b\u043d\u043e\u0442\u043e \u043c\u0435\u0441\u0442\u043e\u043f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435\u2026"
::msgcat::mcset bg "Annotation complete." "\u0410\u043d\u043e\u0442\u0438\u0440\u0430\u043d\u0435\u0442\u043e \u0437\u0430\u0432\u044a\u0440\u0448\u0438."
::msgcat::mcset bg "Busy" "\u041e\u043f\u0435\u0440\u0430\u0446\u0438\u044f\u0442\u0430 \u043d\u0435 \u0435 \u0437\u0430\u0432\u044a\u0440\u0448\u0438\u043b\u0430"
::msgcat::mcset bg "Annotation process is already running." "\u0412 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u0442\u0435\u0447\u0435 \u043f\u0440\u043e\u0446\u0435\u0441 \u043d\u0430 \u0430\u043d\u043e\u0442\u0438\u0440\u0430\u043d\u0435."
::msgcat::mcset bg "Running thorough copy detection..." "\u0418\u0437\u043f\u044a\u043b\u043d\u044f\u0432\u0430 \u0441\u0435 \u0446\u044f\u043b\u043e\u0441\u0442\u0435\u043d \u043f\u0440\u043e\u0446\u0435\u0441 \u043d\u0430 \u043e\u0442\u043a\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043f\u0438\u0440\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Loading annotation..." "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0430\u043d\u043e\u0442\u0430\u0446\u0438\u0438\u2026"
::msgcat::mcset bg "Author:" "\u0410\u0432\u0442\u043e\u0440:"
::msgcat::mcset bg "Committer:" "\u041f\u043e\u0434\u0430\u043b:"
::msgcat::mcset bg "Original File:" "\u041f\u044a\u0440\u0432\u043e\u043d\u0430\u0447\u0430\u043b\u0435\u043d \u0444\u0430\u0439\u043b:"
::msgcat::mcset bg "Cannot find HEAD commit:" "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u0437\u0430 \u0432\u0440\u044a\u0445 \u201eHEAD\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043e\u0442\u043a\u0440\u0438\u0435:"
::msgcat::mcset bg "Cannot find parent commit:" "\u0420\u043e\u0434\u0438\u0442\u0435\u043b\u0441\u043a\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043e\u0442\u043a\u0440\u0438\u0435"
::msgcat::mcset bg "Unable to display parent" "\u0420\u043e\u0434\u0438\u0442\u0435\u043b\u044f\u0442 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043f\u043e\u043a\u0430\u0436\u0435"
::msgcat::mcset bg "Error loading diff:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0430:"
::msgcat::mcset bg "Originally By:" "\u041f\u044a\u0440\u0432\u043e\u043d\u0430\u0447\u0430\u043b\u043d\u043e \u043e\u0442:"
::msgcat::mcset bg "In File:" "\u0412\u044a\u0432 \u0444\u0430\u0439\u043b:"
::msgcat::mcset bg "Copied Or Moved Here By:" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u043e \u0438\u043b\u0438 \u043f\u0440\u0435\u043c\u0435\u0441\u0442\u0435\u043d\u043e \u0442\u0443\u043a \u043e\u0442:"
::msgcat::mcset bg "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "\u041d\u0435 \u0441\u0430 \u043e\u0442\u043a\u0440\u0438\u0442\u0438 \u0440\u0430\u0437\u043b\u0438\u043a\u0438.\n\n\u041d\u044f\u043c\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0432 \u201e%s\u201c.\n\n\u0412\u0440\u0435\u043c\u0435\u0442\u043e \u043d\u0430 \u043f\u0440\u043e\u043c\u044f\u043d\u0430 \u043d\u0430 \u0444\u0430\u0439\u043b\u0430 \u0435 \u0431\u0438\u043b \u0437\u0430\u0434\u0430\u0434\u0435\u043d \u043e\u0442 \u0434\u0440\u0443\u0433\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430, \u043d\u043e \u0441\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435\u0442\u043e \u043c\u0443 \u043d\u0435 \u0435 \u043f\u0440\u043e\u043c\u0435\u043d\u0435\u043d\u043e.\n\n\u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u043d\u043e \u0449\u0435 \u0437\u0430\u043f\u043e\u0447\u043d\u0435 \u043d\u043e\u0432\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u0434\u0430\u043b\u0438 \u043d\u044f\u043c\u0430 \u0434\u0440\u0443\u0433\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u0432 \u0442\u043e\u0432\u0430 \u0441\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435."
::msgcat::mcset bg "Loading diff of %s..." "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435 \u0432 \u201e%s\u201c\u2026"
::msgcat::mcset bg "LOCAL: deleted\nREMOTE:\n" "\u041b\u041e\u041a\u0410\u041b\u041d\u041e: \u0438\u0437\u0442\u0440\u0438\u0442\n\u041e\u0422\u0414\u0410\u041b\u0415\u0427\u0415\u041d\u041e:\n"
::msgcat::mcset bg "REMOTE: deleted\nLOCAL:\n" "\u041e\u0422\u0414\u0410\u041b\u0415\u0427\u0415\u041d\u041e: \u0438\u0437\u0442\u0440\u0438\u0442\n\u041b\u041e\u041a\u0410\u041b\u041d\u041e:\n"
::msgcat::mcset bg "LOCAL:\n" "\u041b\u041e\u041a\u0410\u041b\u041d\u041e:\n"
::msgcat::mcset bg "REMOTE:\n" "\u041e\u0422\u0414\u0410\u041b\u0415\u0427\u0415\u041d\u041e:\n"
::msgcat::mcset bg "Unable to display %s" "\u0424\u0430\u0439\u043b\u044a\u0442 \u201e%s\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043f\u043e\u043a\u0430\u0436\u0435"
::msgcat::mcset bg "Error loading file:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b:"
::msgcat::mcset bg "Git Repository (subproject)" "\u0425\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u043d\u0430 Git (\u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b)"
::msgcat::mcset bg "* Binary file (not showing content)." "\u25cf \u0414\u0432\u043e\u0438\u0447\u0435\u043d \u0444\u0430\u0439\u043b (\u0441\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435\u0442\u043e \u043d\u0435 \u0441\u0435 \u043f\u043e\u043a\u0430\u0437\u0432\u0430)."
::msgcat::mcset bg "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "\u25cf \u041d\u0435\u0441\u043b\u0435\u0434\u0435\u043d\u0438\u044f\u0442 \u0444\u0430\u0439\u043b \u0435 %d \u0431\u0430\u0439\u0442\u0430.\n\u25cf \u041f\u043e\u043a\u0430\u0437\u0432\u0430\u0442 \u0441\u0435 \u0441\u0430\u043c\u043e \u043f\u044a\u0440\u0432\u0438\u0442\u0435 %d \u0431\u0430\u0439\u0442\u0430.\n"
::msgcat::mcset bg "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n\u25cf \u041d\u0435\u0441\u043b\u0435\u0434\u0435\u043d\u0438\u044f\u0442 \u0444\u0430\u0439\u043b \u0435 \u043e\u0442\u0440\u044f\u0437\u0430\u043d \u0434\u043e\u0442\u0443\u043a \u043e\u0442 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u201e%s\u201c.\n\u25cf \u0418\u0437\u043f\u043e\u043b\u0437\u0432\u0430\u0439\u0442\u0435 \u0432\u044a\u043d\u0448\u0435\u043d \u0440\u0435\u0434\u0430\u043a\u0442\u043e\u0440, \u0437\u0430 \u0434\u0430 \u0432\u0438\u0434\u0438\u0442\u0435 \u0446\u0435\u043b\u0438\u044f \u0444\u0430\u0439\u043b.\n"
::msgcat::mcset bg "Failed to unstage selected hunk." "\u0418\u0437\u0431\u0440\u0430\u043d\u043e\u0442\u043e \u043f\u0430\u0440\u0447\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0438\u0437\u0432\u0430\u0434\u0438 \u043e\u0442 \u0438\u043d\u0434\u0435\u043a\u0441\u0430."
::msgcat::mcset bg "Failed to revert selected hunk." "\u0418\u0437\u0431\u0440\u0430\u043d\u043e\u0442\u043e \u043f\u0430\u0440\u0447\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0432\u044a\u0440\u043d\u0435."
::msgcat::mcset bg "Failed to stage selected hunk." "\u0418\u0437\u0431\u0440\u0430\u043d\u043e\u0442\u043e \u043f\u0430\u0440\u0447\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0434\u043e\u0431\u0430\u0432\u0438 \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430."
::msgcat::mcset bg "Failed to unstage selected line." "\u0418\u0437\u0431\u0440\u0430\u043d\u0438\u044f\u0442 \u0440\u0435\u0434 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0438\u0437\u0432\u0430\u0434\u0438 \u043e\u0442 \u0438\u043d\u0434\u0435\u043a\u0441\u0430."
::msgcat::mcset bg "Failed to revert selected line." "\u0418\u0437\u0431\u0440\u0430\u043d\u0438\u044f\u0442 \u0440\u0435\u0434 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0432\u044a\u0440\u043d\u0435."
::msgcat::mcset bg "Failed to stage selected line." "\u0418\u0437\u0431\u0440\u0430\u043d\u0438\u044f\u0442 \u0440\u0435\u0434 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0434\u043e\u0431\u0430\u0432\u0438 \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430."
::msgcat::mcset bg "Failed to undo last revert." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u0430 \u043e\u0442\u043c\u044f\u043d\u0430 \u043d\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u043e\u0442\u043e \u0432\u0440\u044a\u0449\u0430\u043d\u0435."
::msgcat::mcset bg "No keys found." "\u041d\u0435 \u0441\u0430 \u043e\u0442\u043a\u0440\u0438\u0442\u0438 \u043a\u043b\u044e\u0447\u043e\u0432\u0435."
::msgcat::mcset bg "Found a public key in: %s" "\u041e\u0442\u043a\u0440\u0438\u0442 \u0435 \u043f\u0443\u0431\u043b\u0438\u0447\u0435\u043d \u043a\u043b\u044e\u0447 \u0432 \u201e%s\u201c"
::msgcat::mcset bg "Generate Key" "\u0413\u0435\u043d\u0435\u0440\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u044e\u0447"
::msgcat::mcset bg "Copy To Clipboard" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043a\u044a\u043c \u0441\u0438\u0441\u0442\u0435\u043c\u043d\u0438\u044f \u0431\u0443\u0444\u0435\u0440"
::msgcat::mcset bg "Your OpenSSH Public Key" "\u041f\u0443\u0431\u043b\u0438\u0447\u043d\u0438\u044f\u0442 \u0432\u0438 \u043a\u043b\u044e\u0447 \u0437\u0430 OpenSSH"
::msgcat::mcset bg "Generating..." "\u0413\u0435\u043d\u0435\u0440\u0438\u0440\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Could not start ssh-keygen:\n\n%s" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u201essh-keygen\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0441\u0442\u0430\u0440\u0442\u0438\u0440\u0430:\n\n%s"
::msgcat::mcset bg "Generation failed." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0433\u0435\u043d\u0435\u0440\u0438\u0440\u0430\u043d\u0435."
::msgcat::mcset bg "Generation succeeded, but no keys found." "\u0413\u0435\u043d\u0435\u0440\u0438\u0440\u0430\u043d\u0435\u0442\u043e \u0437\u0430\u0432\u044a\u0440\u0448\u0438 \u0443\u0441\u043f\u0435\u0448\u043d\u043e, \u0430 \u043d\u0435 \u0441\u0430 \u043d\u0430\u043c\u0435\u0440\u0435\u043d\u0438 \u043a\u043b\u044e\u0447\u043e\u0432\u0435."
::msgcat::mcset bg "Your key is in: %s" "\u041a\u043b\u044e\u0447\u044a\u0442 \u0432\u0438 \u0435 \u0432 \u201e%s\u201c"
::msgcat::mcset bg "%s (%s): Create Branch" "%s (%s): \u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Create New Branch" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043d\u043e\u0432 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Branch Name" "\u0418\u043c\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d\u0430"
::msgcat::mcset bg "Match Tracking Branch Name" "\u0421\u044a\u0432\u043f\u0430\u0434\u0430\u043d\u0435 \u043f\u043e \u0438\u043c\u0435\u0442\u043e \u043d\u0430 \u0441\u043b\u0435\u0434\u0435\u043d\u0438\u044f \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Starting Revision" "\u041d\u0430\u0447\u0430\u043b\u043d\u0430 \u0432\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset bg "Update Existing Branch:" "\u041e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430\u0449 \u043a\u043b\u043e\u043d:"
::msgcat::mcset bg "No" "\u041d\u0435"
::msgcat::mcset bg "Fast Forward Only" "\u0421\u0430\u043c\u043e \u0442\u0440\u0438\u0432\u0438\u0430\u043b\u043d\u043e \u043f\u0440\u0435\u0432\u044a\u0440\u0442\u0430\u0449\u043e \u0441\u043b\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Checkout After Creation" "\u041f\u0440\u0435\u043c\u0438\u043d\u0430\u0432\u0430\u043d\u0435 \u043a\u044a\u043c \u043a\u043b\u043e\u043d\u0430 \u0441\u043b\u0435\u0434 \u0441\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043c\u0443"
::msgcat::mcset bg "Please select a tracking branch." "\u0418\u0437\u0431\u0435\u0440\u0435\u0442\u0435 \u043a\u043b\u043e\u043d \u0437\u0430 \u0441\u043b\u0435\u0434\u0435\u043d\u0438."
::msgcat::mcset bg "Tracking branch %s is not a branch in the remote repository." "\u0421\u043b\u0435\u0434\u044f\u0449\u0438\u044f\u0442 \u043a\u043b\u043e\u043d \u2014 \u201e%s\u201c, \u043d\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430 \u0432 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u043e\u0442\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435."
::msgcat::mcset bg "Working... please wait..." "\u0412 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u0441\u0435 \u0438\u0437\u0432\u044a\u0440\u0448\u0432\u0430 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435, \u0438\u0437\u0447\u0430\u043a\u0430\u0439\u0442\u0435\u2026"
::msgcat::mcset bg "Success" "\u0423\u0441\u043f\u0435\u0445"
::msgcat::mcset bg "Error: Command Failed" "\u0413\u0440\u0435\u0448\u043a\u0430: \u043d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0438\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u043d\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430"
::msgcat::mcset bg "Goto Line:" "\u041a\u044a\u043c \u0440\u0435\u0434:"
::msgcat::mcset bg "Go" "\u041a\u044a\u043c"
::msgcat::mcset bg "This Detached Checkout" "\u0422\u043e\u0432\u0430 \u043d\u0435\u0441\u0432\u044a\u0440\u0437\u0430\u043d\u043e \u0438\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435"
::msgcat::mcset bg "Revision Expression:" "\u0418\u0437\u0440\u0430\u0437 \u0437\u0430 \u0432\u0435\u0440\u0441\u0438\u044f:"
::msgcat::mcset bg "Local Branch" "\u041b\u043e\u043a\u0430\u043b\u0435\u043d \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Tracking Branch" "\u0421\u043b\u0435\u0434\u044f\u0449 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Tag" "\u0415\u0442\u0438\u043a\u0435\u0442"
::msgcat::mcset bg "Invalid revision: %s" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u043d\u0430 \u0432\u0435\u0440\u0441\u0438\u044f: %s"
::msgcat::mcset bg "No revision selected." "\u041d\u0435 \u0435 \u0438\u0437\u0431\u0440\u0430\u043d\u0430 \u0432\u0435\u0440\u0441\u0438\u044f."
::msgcat::mcset bg "Revision expression is empty." "\u0418\u0437\u0440\u0430\u0437\u044a\u0442 \u0437\u0430 \u0432\u0435\u0440\u0441\u0438\u044f \u0435 \u043f\u0440\u0430\u0437\u0435\u043d."
::msgcat::mcset bg "Updated" "\u041e\u0431\u043d\u043e\u0432\u0435\u043d"
::msgcat::mcset bg "URL" "\u0410\u0434\u0440\u0435\u0441"
::msgcat::mcset bg "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "\u041d\u044f\u043c\u0430 \u043a\u0430\u043a\u0432\u043e \u0434\u0430 \u0441\u0435 \u043f\u043e\u043f\u0440\u0430\u0432\u0438.\n\n\u0429\u0435 \u0441\u044a\u0437\u0434\u0430\u0434\u0435\u0442\u0435 \u043f\u044a\u0440\u0432\u043e\u043d\u0430\u0447\u0430\u043b\u043d\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435. \u041f\u0440\u0435\u0434\u0438 \u043d\u0435\u0433\u043e \u043d\u044f\u043c\u0430 \u0434\u0440\u0443\u0433\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f, \u043a\u043e\u0438\u0442\u043e \u0434\u0430 \u043f\u043e\u043f\u0440\u0430\u0432\u0438\u0442\u0435.\n"
::msgcat::mcset bg "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "\u041f\u043e \u0432\u0440\u0435\u043c\u0435 \u043d\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u043f\u043e\u043f\u0440\u0430\u0432\u044f\u0442\u0435.\n\n\u0412 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u0432\u0441\u0435 \u043e\u0449\u0435 \u043d\u0435 \u0441\u0442\u0435 \u0437\u0430\u0432\u044a\u0440\u0448\u0438\u043b\u0438 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044f \u043f\u043e \u0441\u043b\u0438\u0432\u0430\u043d\u0435. \u041d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u043f\u043e\u043f\u0440\u0430\u0432\u0438\u0442\u0435 \u043f\u0440\u0435\u0434\u0438\u0448\u043d\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435, \u043e\u0441\u0432\u0435\u043d \u0430\u043a\u043e \u043f\u044a\u0440\u0432\u043e \u043d\u0435 \u043f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u0435 \u0442\u0435\u043a\u0443\u0449\u043e\u0442\u043e \u0441\u043b\u0438\u0432\u0430\u043d\u0435.\n"
::msgcat::mcset bg "Error loading commit data for amend:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0434\u0430\u043d\u043d\u0438\u0442\u0435 \u043e\u0442 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435, \u043a\u043e\u0438\u0442\u043e \u0434\u0430 \u0441\u0435 \u043f\u043e\u043f\u0440\u0430\u0432\u044f\u0442:"
::msgcat::mcset bg "Unable to obtain your identity:" "\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0446\u0438\u044f\u0442\u0430 \u0432\u0438 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0438:"
::msgcat::mcset bg "Invalid GIT_COMMITTER_IDENT:" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u043d\u043e \u043f\u043e\u043b\u0435 \u201eGIT_COMMITTER_IDENT\u201c:"
::msgcat::mcset bg "warning: Tcl does not support encoding '%s'." "\u043f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0435\u043d\u0438\u0435: Tcl \u043d\u0435 \u043f\u043e\u0434\u0434\u044a\u0440\u0436\u0430 \u043a\u043e\u0434\u0438\u0440\u0430\u043d\u0435\u0442\u043e \u201e%s\u201c."
::msgcat::mcset bg "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "\u0421\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435\u0442\u043e \u043f\u0440\u0438 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0430\u0442\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0435 \u043e\u0442\u0433\u043e\u0432\u0430\u0440\u044f \u043d\u0430 \u0441\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435\u0442\u043e \u043d\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e.\n\n\u041d\u044f\u043a\u043e\u0439 \u0434\u0440\u0443\u0433 \u043f\u0440\u043e\u0446\u0435\u0441 \u0437\u0430 Git \u0435 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u043b \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e \u043c\u0435\u0436\u0434\u0443\u0432\u0440\u0435\u043c\u0435\u043d\u043d\u043e. \u0421\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435\u0442\u043e \u0442\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0441\u0435 \u043f\u0440\u043e\u0432\u0435\u0440\u0438 \u043f\u0440\u0435\u0434\u0438 \u043d\u043e\u0432\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435.\n\n\u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u043d\u043e \u0449\u0435 \u0437\u0430\u043f\u043e\u0447\u043d\u0435 \u043d\u043e\u0432\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430.\n"
::msgcat::mcset bg "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "\u041d\u0435\u0441\u043b\u0435\u0442\u0438\u0442\u0435 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043f\u043e\u0434\u0430\u0434\u0430\u0442.\n\n\u0412\u044a\u0432 \u0444\u0430\u0439\u043b\u0430 \u201e%s\u201c \u0438\u043c\u0430 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438 \u043f\u0440\u0438 \u0441\u043b\u0438\u0432\u0430\u043d\u0435. \u0417\u0430 \u0434\u0430 \u0433\u043e \u043f\u043e\u0434\u0430\u0434\u0435\u0442\u0435, \u0442\u0440\u044f\u0431\u0432\u0430 \u043f\u044a\u0440\u0432\u043e \u0434\u0430 \u043a\u043e\u0440\u0438\u0433\u0438\u0440\u0430\u0442\u0435 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438\u0442\u0435 \u0438 \u0434\u0430 \u0434\u043e\u0431\u0430\u0432\u0438\u0442\u0435 \u0444\u0430\u0439\u043b\u0430 \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435.\n"
::msgcat::mcset bg "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "\u041d\u0435\u043f\u043e\u0437\u043d\u0430\u0442\u043e \u0441\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b \u201e%s\u201c.\n\n\u0424\u0430\u0439\u043b\u044a\u0442 \u201e%s\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043f\u043e\u0434\u0430\u0434\u0435 \u0447\u0440\u0435\u0437 \u0442\u0435\u043a\u0443\u0449\u0430\u0442\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430.\n"
::msgcat::mcset bg "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "\u041d\u044f\u043c\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435.\n\n\u0422\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0434\u043e\u0431\u0430\u0432\u0438\u0442\u0435 \u043f\u043e\u043d\u0435 \u0435\u0434\u0438\u043d \u0444\u0430\u0439\u043b \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430, \u0437\u0430 \u0434\u0430 \u043f\u043e\u0434\u0430\u0434\u0435\u0442\u0435.\n"
::msgcat::mcset bg "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "\u0417\u0430\u0434\u0430\u0439\u0442\u0435 \u0434\u043e\u0431\u0440\u043e \u0441\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435.\n\n\u0418\u0437\u043f\u043e\u043b\u0437\u0432\u0430\u0439\u0442\u0435 \u0441\u043b\u0435\u0434\u043d\u0438\u044f \u0444\u043e\u0440\u043c\u0430\u0442:\n\n\u25cf \u041f\u044a\u0440\u0432\u0438 \u0440\u0435\u0434: \u043e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u0432 \u0435\u0434\u043d\u043e \u0438\u0437\u0440\u0435\u0447\u0435\u043d\u0438\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u044f\u043d\u0430\u0442\u0430.\n\u25cf \u0412\u0442\u043e\u0440\u0438 \u0440\u0435\u0434: \u043f\u0440\u0430\u0437\u0435\u043d.\n\u25cf \u041e\u0441\u0442\u0430\u043d\u0430\u043b\u0438\u0442\u0435 \u0440\u0435\u0434\u043e\u0432\u0435: \u043e\u043f\u0438\u0448\u0435\u0442\u0435 \u0437\u0430\u0449\u043e \u0441\u0435 \u043d\u0430\u043b\u0430\u0433\u0430 \u0442\u0430\u0437\u0438 \u043f\u0440\u043e\u043c\u044f\u043d\u0430.\n"
::msgcat::mcset bg "Calling pre-commit hook..." "\u0418\u0437\u043f\u044a\u043b\u043d\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u0443\u043a\u0430\u0442\u0430 \u043f\u0440\u0435\u0434\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Commit declined by pre-commit hook." "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u0435 \u043e\u0442\u0445\u0432\u044a\u0440\u043b\u0435\u043d\u043e \u043e\u0442 \u043a\u0443\u043a\u0430\u0442\u0430 \u043f\u0440\u0435\u0434\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "You are about to commit on a detached head. This is a potentially dangerous thing to do because if you switch to another branch you will lose your changes and it can be difficult to retrieve them later from the reflog. You should probably cancel this commit and create a new branch to continue.\n \n Do you really want to proceed with your Commit?" "\u0429\u0435 \u043f\u043e\u0434\u0430\u0434\u0435\u0442\u0435 \u043a\u044a\u043c \u043d\u0435\u0441\u0432\u044a\u0440\u0437\u0430\u043d, \u043e\u0442\u0434\u0435\u043b\u0450\u043d \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b \u201eHEAD\u201c. \u0422\u043e\u0432\u0430 \u0435 \u043e\u043f\u0430\u0441\u043d\u043e, \u0437\u0430\u0449\u043e\u0442\u043e \u043f\u0440\u0438 \u043f\u0440\u0435\u043c\u0438\u043d\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043a\u044a\u043c \u043a\u043b\u043e\u043d \u0449\u0435 \u0437\u0430\u0433\u0443\u0431\u0438\u0442\u0435 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u0441\u0438, \u043a\u0430\u0442\u043e \u0435\u0434\u0438\u043d\u0441\u0442\u0432\u0435\u043d\u0438\u044f\u0442 \u043d\u0430\u0447\u0438\u043d \u0434\u0430 \u0433\u0438 \u0432\u044a\u0440\u043d\u0435\u0442\u0435 \u0449\u0435 \u0435 \u0447\u0440\u0435\u0437 \u0436\u0443\u0440\u043d\u0430\u043b\u0430 \u043d\u0430 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u0438\u0442\u0435 (reflog). \u041d\u0430\u0439-\u0432\u0435\u0440\u043e\u044f\u0442\u043d\u043e \u0442\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u043d\u0435 \u043f\u0440\u0430\u0432\u0438\u0442\u0435 \u0442\u043e\u0432\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435, \u0430 \u0434\u0430 \u0441\u044a\u0437\u0434\u0430\u0434\u0435\u0442\u0435 \u043d\u043e\u0432 \u043a\u043b\u043e\u043d, \u043f\u0440\u0435\u0434\u0438 \u0434\u0430 \u043f\u0440\u043e\u0434\u044a\u043b\u0436\u0438\u0442\u0435.\n \n\u0421\u0438\u0433\u0443\u0440\u043d\u0438 \u043b\u0438 \u0441\u0442\u0435, \u0447\u0435 \u0438\u0441\u043a\u0430\u0442\u0435 \u0434\u0430 \u0438\u0437\u0432\u044a\u0440\u0448\u0438\u0442\u0435 \u0442\u0435\u043a\u0443\u0449\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435?"
::msgcat::mcset bg "Calling commit-msg hook..." "\u0418\u0437\u043f\u044a\u043b\u043d\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u0443\u043a\u0430\u0442\u0430 \u0437\u0430 \u0441\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435\u0442\u043e \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u2026"
::msgcat::mcset bg "Commit declined by commit-msg hook." "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u0435 \u043e\u0442\u0445\u0432\u044a\u0440\u043b\u0435\u043d\u043e \u043e\u0442 \u043a\u0443\u043a\u0430\u0442\u0430 \u0437\u0430 \u0441\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435\u0442\u043e \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "Committing changes..." "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435\u2026"
::msgcat::mcset bg "write-tree failed:" "\u043d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0437\u0430\u043f\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u0434\u044a\u0440\u0432\u043e\u0442\u043e (write-tree):"
::msgcat::mcset bg "Commit failed." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "Commit %s appears to be corrupt" "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u201e%s\u201c \u0438\u0437\u0433\u043b\u0435\u0436\u0434\u0430 \u043f\u043e\u0432\u0440\u0435\u0434\u0435\u043d\u043e"
::msgcat::mcset bg "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "\u041d\u044f\u043c\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435.\n\n\u0412 \u0442\u043e\u0432\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0435 \u0441\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u044f\u043d\u0438 \u043d\u0438\u043a\u0430\u043a\u0432\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435, \u0430 \u0438 \u043d\u0435 \u0435 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u0441\u044a\u0441 \u0441\u043b\u0438\u0432\u0430\u043d\u0435.\n\n\u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u043d\u043e \u0449\u0435 \u0437\u0430\u043f\u043e\u0447\u043d\u0435 \u043d\u043e\u0432\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430.\n"
::msgcat::mcset bg "No changes to commit." "\u041d\u044f\u043c\u0430 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "commit-tree failed:" "\u043d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u0434\u044a\u0440\u0432\u043e\u0442\u043e (commit-tree):"
::msgcat::mcset bg "update-ref failed:" "\u043d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u0438\u0442\u0435 (update-ref):"
::msgcat::mcset bg "Created commit %s: %s" "\u0423\u0441\u043f\u0435\u0448\u043d\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 %s: %s"
::msgcat::mcset bg "%s (%s): Delete Branch" "%s (%s): \u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Delete Local Branch" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043b\u043e\u043a\u0430\u043b\u0435\u043d \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Local Branches" "\u041b\u043e\u043a\u0430\u043b\u043d\u0438 \u043a\u043b\u043e\u043d\u0438"
::msgcat::mcset bg "Delete Only If Merged Into" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435, \u0441\u0430\u043c\u043e \u0430\u043a\u043e \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435 \u0441\u0430 \u0441\u043b\u0435\u0442\u0438 \u0438 \u0434\u0440\u0443\u0433\u0430\u0434\u0435"
::msgcat::mcset bg "The following branches are not completely merged into %s:" "\u041d\u0435 \u0432\u0441\u0438\u0447\u043a\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0432 \u043a\u043b\u043e\u043d\u0438\u0442\u0435 \u0441\u0430 \u0441\u043b\u0435\u0442\u0438 \u0432 \u201e%s\u201c:"
::msgcat::mcset bg " - %s:" " \u2014 \u201e%s:\u201c"
::msgcat::mcset bg "Failed to delete branches:\n%s" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0442\u0440\u0438\u0435\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d\u0438:\n%s"
::msgcat::mcset bg "Invalid date from Git: %s" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u043d\u0438 \u0434\u0430\u043d\u043d\u0438 \u043e\u0442 Git: %s"
::msgcat::mcset bg "Number of loose objects" "\u0411\u0440\u043e\u0439 \u043d\u0435\u043f\u0430\u043a\u0435\u0442\u0438\u0440\u0430\u043d\u0438 \u043e\u0431\u0435\u043a\u0442\u0438"
::msgcat::mcset bg "Disk space used by loose objects" "\u0414\u0438\u0441\u043a\u043e\u0432\u043e \u043f\u0440\u043e\u0441\u0442\u0440\u0430\u043d\u0441\u0442\u0432\u043e \u0437\u0430\u0435\u0442\u043e \u043e\u0442 \u043d\u0435\u043f\u0430\u043a\u0435\u0442\u0438\u0440\u0430\u043d\u0438 \u043e\u0431\u0435\u043a\u0442\u0438"
::msgcat::mcset bg "Number of packed objects" "\u0411\u0440\u043e\u0439 \u043f\u0430\u043a\u0435\u0442\u0438\u0440\u0430\u043d\u0438 \u043e\u0431\u0435\u043a\u0442\u0438"
::msgcat::mcset bg "Number of packs" "\u0411\u0440\u043e\u0439 \u043f\u0430\u043a\u0435\u0442\u0438"
::msgcat::mcset bg "Disk space used by packed objects" "\u0414\u0438\u0441\u043a\u043e\u0432\u043e \u043f\u0440\u043e\u0441\u0442\u0440\u0430\u043d\u0441\u0442\u0432\u043e \u0437\u0430\u0435\u0442\u043e \u043e\u0442 \u043f\u0430\u043a\u0435\u0442\u0438\u0440\u0430\u043d\u0438 \u043e\u0431\u0435\u043a\u0442\u0438"
::msgcat::mcset bg "Packed objects waiting for pruning" "\u041f\u0430\u043a\u0435\u0442\u0438\u0440\u0430\u043d\u0438 \u043e\u0431\u0435\u043a\u0442\u0438 \u0437\u0430 \u043e\u043a\u0430\u0441\u0442\u0440\u044f\u043d\u0435"
::msgcat::mcset bg "Garbage files" "\u0424\u0430\u0439\u043b\u043e\u0432\u0435 \u0437\u0430 \u0431\u043e\u043a\u043b\u0443\u043a\u0430"
::msgcat::mcset bg "%s (%s): Database Statistics" "%s (%s): \u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043a\u0430 \u043d\u0430 \u0431\u0430\u0437\u0430\u0442\u0430 \u043e\u0442 \u0434\u0430\u043d\u043d\u0438"
::msgcat::mcset bg "Compressing the object database" "\u041a\u043e\u043c\u043f\u0440\u0435\u0441\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0431\u0430\u0437\u0430\u0442\u0430 \u0441 \u0434\u0430\u043d\u043d\u0438 \u0437\u0430 \u043e\u0431\u0435\u043a\u0442\u0438\u0442\u0435"
::msgcat::mcset bg "Verifying the object database with fsck-objects" "\u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u0431\u0430\u0437\u0430\u0442\u0430 \u0441 \u0434\u0430\u043d\u043d\u0438 \u0437\u0430 \u043e\u0431\u0435\u043a\u0442\u0438\u0442\u0435 \u0441 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430 \u201efsck-objects\u201c"
::msgcat::mcset bg "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "\u0412 \u0442\u043e\u0432\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u0432 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u0438\u043c\u0430 \u043a\u044a\u043c %i \u043d\u0435\u043f\u0430\u043a\u0435\u0442\u0438\u0440\u0430\u043d\u0438 \u043e\u0431\u0435\u043a\u0442\u0438.\n\n\u0417\u0430 \u0434\u043e\u0431\u0440\u0430 \u043f\u0440\u043e\u0438\u0437\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u043d\u043e\u0441\u0442 \u0441\u0435 \u043f\u0440\u0435\u043f\u043e\u0440\u044a\u0447\u0432\u0430 \u0434\u0430 \u043a\u043e\u043c\u043f\u0440\u0435\u0441\u0438\u0440\u0430\u0442\u0435 \u0431\u0430\u0437\u0430\u0442\u0430 \u0441 \u0434\u0430\u043d\u043d\u0438 \u0437\u0430 \u043e\u0431\u0435\u043a\u0442\u0438\u0442\u0435.\n\n\u0414\u0430 \u0441\u0435 \u0437\u0430\u043f\u043e\u0447\u043d\u0435 \u043b\u0438 \u043a\u043e\u043c\u043f\u0440\u0435\u0441\u0438\u0440\u0430\u043d\u0435\u0442\u043e?"
::msgcat::mcset bg "%s: error" "%s: \u0433\u0440\u0435\u0448\u043a\u0430"
::msgcat::mcset bg "%s: warning" "%s: \u043f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0435\u043d\u0438\u0435"
::msgcat::mcset bg "%s hook failed:" "%s: \u0433\u0440\u0435\u0448\u043a\u0430 \u043e\u0442 \u043a\u0443\u043a\u0430\u0442\u0430"
::msgcat::mcset bg "You must correct the above errors before committing." "\u041f\u0440\u0435\u0434\u0438 \u0434\u0430 \u043c\u043e\u0436\u0435\u0442\u0435 \u0434\u0430 \u043f\u043e\u0434\u0430\u0434\u0435\u0442\u0435, \u043a\u043e\u0440\u0438\u0433\u0438\u0440\u0430\u0439\u0442\u0435 \u0433\u043e\u0440\u043d\u0438\u0442\u0435 \u0433\u0440\u0435\u0448\u043a\u0438."
::msgcat::mcset bg "%s (%s): error" "%s (%s): \u0433\u0440\u0435\u0448\u043a\u0430"
::msgcat::mcset bg "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "\u041f\u043e \u0432\u0440\u0435\u043c\u0435 \u043d\u0430 \u043f\u043e\u043f\u0440\u0430\u0432\u044f\u043d\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435.\n\n\u0422\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0437\u0430\u0432\u044a\u0440\u0448\u0438\u0442\u0435 \u043f\u043e\u043f\u0440\u0430\u0432\u044f\u043d\u0435\u0442\u043e \u043d\u0430 \u0442\u0435\u043a\u0443\u0449\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435, \u043f\u0440\u0435\u0434\u0438 \u0434\u0430 \u0437\u0430\u043f\u043e\u0447\u043d\u0435\u0442\u0435 \u0441\u043b\u0438\u0432\u0430\u043d\u0435.\n"
::msgcat::mcset bg "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "\u041f\u043e\u0441\u043b\u0435\u0434\u043d\u043e \u0443\u0441\u0442\u0430\u043d\u043e\u0432\u0435\u043d\u043e\u0442\u043e \u0441\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u043d\u0435 \u043e\u0442\u0433\u043e\u0432\u0430\u0440\u044f \u043d\u0430 \u0442\u043e\u0432\u0430 \u0432 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e.\n\n\u041d\u044f\u043a\u043e\u0439 \u0434\u0440\u0443\u0433 \u043f\u0440\u043e\u0446\u0435\u0441 \u0437\u0430 Git \u0435 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u043b \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435\u0442\u043e \u043c\u0435\u0436\u0434\u0443\u0432\u0440\u0435\u043c\u0435\u043d\u043d\u043e. \u0421\u044a\u0441\u0442\u043e\u044f\u043d\u0438\u0435\u0442\u043e \u0442\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0441\u0435 \u043f\u0440\u043e\u0432\u0435\u0440\u0438, \u043f\u0440\u0435\u0434\u0438 \u0434\u0430 \u0441\u0435 \u0438\u0437\u0432\u044a\u0440\u0448\u0438 \u0441\u043b\u0438\u0432\u0430\u043d\u0435.\n\n\u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u043d\u043e \u0449\u0435 \u0437\u0430\u043f\u043e\u0447\u043d\u0435 \u043d\u043e\u0432\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430.\n\n"
::msgcat::mcset bg "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "\u0412 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u0442\u0435\u0447\u0435 \u0441\u043b\u0438\u0432\u0430\u043d\u0435, \u043d\u043e \u0438\u043c\u0430 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438.\n\n\u041f\u043e\u0433\u043b\u0435\u0434\u043d\u0435\u0442\u0435 \u0444\u0430\u0439\u043b\u0430 \u201e%s\u201c.\n\n\u0422\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u043a\u043e\u0440\u0438\u0433\u0438\u0440\u0430\u0442\u0435 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438\u0442\u0435 \u0432 \u043d\u0435\u0433\u043e, \u0434\u0430 \u0433\u043e \u0434\u043e\u0431\u0430\u0432\u0438\u0442\u0435 \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u0438 \u0434\u0430 \u0437\u0430\u0432\u044a\u0440\u0448\u0438\u0442\u0435 \u0442\u0435\u043a\u0443\u0449\u043e\u0442\u043e \u0441\u043b\u0438\u0432\u0430\u043d\u0435 \u0447\u0440\u0435\u0437 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435. \u0427\u0430\u043a \u0442\u043e\u0433\u0430\u0432\u0430 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0437\u0430\u043f\u043e\u0447\u043d\u0435\u0442\u0435 \u043d\u043e\u0432\u043e \u0441\u043b\u0438\u0432\u0430\u043d\u0435.\n"
::msgcat::mcset bg "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "\u0412 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u0442\u0435\u0447\u0435 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435.\n\n\u0424\u0430\u0439\u043b\u044a\u0442 \u201e%s\u201c \u0435 \u043f\u0440\u043e\u043c\u0435\u043d\u0435\u043d.\n\n\u0422\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0437\u0430\u0432\u044a\u0440\u0448\u0438\u0442\u0435 \u0442\u0435\u043a\u0443\u0449\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435, \u043f\u0440\u0435\u0434\u0438 \u0434\u0430 \u0437\u0430\u043f\u043e\u0447\u043d\u0435\u0442\u0435 \u0441\u043b\u0438\u0432\u0430\u043d\u0435. \u0422\u0430\u043a\u0430 \u0449\u0435 \u043c\u043e\u0436\u0435\u0442\u0435 \u043b\u0435\u0441\u043d\u043e \u0434\u0430 \u043f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u0435 \u0441\u043b\u0438\u0432\u0430\u043d\u0435\u0442\u043e, \u0430\u043a\u043e \u0432\u044a\u0437\u043d\u0438\u043a\u043d\u0435 \u043d\u0443\u0436\u0434\u0430.\n"
::msgcat::mcset bg "%s of %s" "%s \u043e\u0442 \u043e\u0431\u0449\u043e %s"
::msgcat::mcset bg "Merging %s and %s..." "\u0421\u043b\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u201e%s\u201c \u0438 \u201e%s\u201c\u2026"
::msgcat::mcset bg "Merge completed successfully." "\u0421\u043b\u0438\u0432\u0430\u043d\u0435\u0442\u043e \u0437\u0430\u0432\u044a\u0440\u0448\u0438 \u0443\u0441\u043f\u0435\u0448\u043d\u043e."
::msgcat::mcset bg "Merge failed.  Conflict resolution is required." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0441\u043b\u0438\u0432\u0430\u043d\u0435 \u2014 \u0438\u043c\u0430 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438 \u0437\u0430 \u043a\u043e\u0440\u0438\u0433\u0438\u0440\u0430\u043d\u0435."
::msgcat::mcset bg "%s (%s): Merge" "%s (%s): \u0421\u043b\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Merge Into %s" "\u0421\u043b\u0438\u0432\u0430\u043d\u0435 \u0432 \u201e%s\u201c"
::msgcat::mcset bg "Revision To Merge" "\u0412\u0435\u0440\u0441\u0438\u044f \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "\u041f\u043e\u043f\u0440\u0430\u0432\u044f\u043d\u0435\u0442\u043e \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u0438.\n\n\u0422\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0437\u0430\u0432\u044a\u0440\u0448\u0438\u0442\u0435 \u043f\u043e\u043f\u0440\u0430\u0432\u043a\u0430\u0442\u0430 \u043d\u0430 \u0442\u043e\u0432\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435.\n"
::msgcat::mcset bg "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "\u0414\u0430 \u0441\u0435 \u043f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u0438 \u043b\u0438 \u0441\u043b\u0438\u0432\u0430\u043d\u0435\u0442\u043e?\n\n\u0412 \u0442\u0430\u043a\u044a\u0432 \u0441\u043b\u0443\u0447\u0430\u0439 \u25cf\u0412\u0421\u0418\u0427\u041a\u0418\u25cf \u043d\u0435\u043f\u043e\u0434\u0430\u0434\u0435\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0449\u0435 \u0441\u0435 \u0437\u0430\u0433\u0443\u0431\u044f\u0442 \u0431\u0435\u0437\u0432\u044a\u0437\u0432\u0440\u0430\u0442\u043d\u043e.\n\n\u041d\u0430\u0438\u0441\u0442\u0438\u043d\u0430 \u043b\u0438 \u0434\u0430 \u0441\u0435 \u043f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u0438 \u0441\u043b\u0438\u0432\u0430\u043d\u0435\u0442\u043e?"
::msgcat::mcset bg "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "\u0414\u0430 \u0441\u0435 \u0437\u0430\u043d\u0443\u043b\u044f\u0442 \u043b\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435?\n\n\u0412 \u0442\u0430\u043a\u044a\u0432 \u0441\u043b\u0443\u0447\u0430\u0439 \u25cf\u0412\u0421\u0418\u0427\u041a\u0418\u25cf \u043d\u0435\u043f\u043e\u0434\u0430\u0434\u0435\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0449\u0435 \u0441\u0435 \u0437\u0430\u0433\u0443\u0431\u044f\u0442 \u0431\u0435\u0437\u0432\u044a\u0437\u0432\u0440\u0430\u0442\u043d\u043e.\n\n\u041d\u0430\u0438\u0441\u0442\u0438\u043d\u0430 \u043b\u0438 \u0434\u0430 \u0441\u0435 \u0437\u0430\u043d\u0443\u043b\u044f\u0442 \u043f\u0440\u043e\u043c\u0435\u043d\u0438\u0442\u0435?"
::msgcat::mcset bg "Aborting" "\u041f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "files reset" "\u0444\u0430\u0439\u043b\u0430 \u0441\u044a\u0441 \u0437\u0430\u043d\u0443\u043b\u0435\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438"
::msgcat::mcset bg "Abort failed." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435."
::msgcat::mcset bg "Abort completed.  Ready." "\u0423\u0441\u043f\u0435\u0448\u043d\u043e \u043f\u0440\u0435\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435. \u0413\u043e\u0442\u043e\u0432\u043d\u043e\u0441\u0442 \u0437\u0430 \u0441\u043b\u0435\u0434\u0432\u0430\u0449\u043e \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435."
