.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_aead_cipher_encryptv2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_aead_cipher_encryptv2 \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_aead_cipher_encryptv2(gnutls_aead_cipher_hd_t " handle ", const void * " nonce ", size_t " nonce_len ", const giovec_t * " auth_iov ", int " auth_iovcnt ", const giovec_t * " iov ", int " iovcnt ", void * " tag ", size_t * " tag_size ");"
.SH ARGUMENTS
.IP "gnutls_aead_cipher_hd_t handle" 12
is a \fBgnutls_aead_cipher_hd_t\fP type.
.IP "const void * nonce" 12
the nonce to set
.IP "size_t nonce_len" 12
The length of the nonce
.IP "const giovec_t * auth_iov" 12
additional data to be authenticated
.IP "int auth_iovcnt" 12
The number of buffers in  \fIauth_iov\fP 
.IP "const giovec_t * iov" 12
the data to be encrypted
.IP "int iovcnt" 12
The number of buffers in  \fIiov\fP 
.IP "void * tag" 12
The authentication tag
.IP "size_t * tag_size" 12
The size of the tag to use (use zero for the default)
.SH "DESCRIPTION"
This is similar to \fBgnutls_aead_cipher_encrypt()\fP, but it performs
in\-place encryption on the provided data buffers.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
3.6.10
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
