.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pcert_list_import_x509_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pcert_list_import_x509_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pcert_list_import_x509_raw(gnutls_pcert_st * " pcert_list ", unsigned int * " pcert_list_size ", const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " format ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pcert_st * pcert_list" 12
The structures to store the certificates; must not contain initialized \fBgnutls_pcert_st\fP structures.
.IP "unsigned int * pcert_list_size" 12
Initially must hold the maximum number of certs. It will be updated with the number of certs available.
.IP "const gnutls_datum_t * data" 12
The certificates.
.IP "gnutls_x509_crt_fmt_t format" 12
One of DER or PEM.
.IP "unsigned int flags" 12
must be (0) or an OR'd sequence of gnutls_certificate_import_flags.
.SH "DESCRIPTION"
This function will import the provided DER or PEM encoded certificates to an
already allocated set of \fBgnutls_pcert_st\fP structures. The structures must
be deinitialized afterwards using \fBgnutls_pcert_deinit()\fP.  \fIpcert_list\fP should contain space for at least  \fIpcert_list_size\fP elements.

If the Certificate is PEM encoded it should have a header of "X509
CERTIFICATE", or "CERTIFICATE".
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value; if the  \fIpcert\fP list doesn't have enough space
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP will be returned.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
