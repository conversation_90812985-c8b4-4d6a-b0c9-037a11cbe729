<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>setmetamode</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="setfacl.html" title="setfacl"><link rel="next" href="ssp.html" title="ssp"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">setmetamode</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="setfacl.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="ssp.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="setmetamode"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>setmetamode &#8212; Get or set keyboard meta mode</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">setmetamode</code>  [ metabit  |   escprefix ]</p></div><div class="cmdsynopsis"><p><code class="command">setmetamode</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="setmetamode-options"></a><h2>Options</h2><pre class="screen">
  Without argument, it shows the current meta key mode.

  metabit|meta|bit     The meta key sets the top bit of the character.
  escprefix|esc|prefix The meta key sends an escape prefix.

Other options:

  -h, --help           This text
  -V, --version        Print program version and exit
</pre></div><div class="refsect1"><a name="setmetamode-desc"></a><h2>Description</h2><p><span class="command"><strong>setmetamode</strong></span> can be used to determine and set the
      key code sent by the meta (aka <code class="literal">Alt</code>) key.</p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="setfacl.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="ssp.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">setfacl&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;ssp</td></tr></table></div></body></html>
