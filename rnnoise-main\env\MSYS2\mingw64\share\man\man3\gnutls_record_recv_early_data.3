.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_recv_early_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_recv_early_data \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_recv_early_data(gnutls_session_t " session ", void * " data ", size_t " data_size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "void * data" 12
the buffer that the data will be read into
.IP "size_t data_size" 12
the number of requested bytes
.SH "DESCRIPTION"
This function can be used by a server to retrieve data sent early
in the handshake processes when resuming a session.  This is used
to implement a zero\-roundtrip (0\-RTT) mode.  It has the same
semantics as \fBgnutls_record_recv()\fP.

This function can be called either in a handshake hook, or after
the handshake is complete.
.SH "RETURNS"
The number of bytes received and zero when early data
reading is complete.  A negative error code is returned in case of
an error.  If no early data is received during the handshake, this
function returns \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP.  The
number of bytes received might be less than the requested
 \fIdata_size\fP .
.SH "SINCE"
3.6.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
