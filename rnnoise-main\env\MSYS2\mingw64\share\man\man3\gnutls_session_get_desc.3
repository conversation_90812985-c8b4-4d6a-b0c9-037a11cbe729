.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_get_desc" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_get_desc \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "char * gnutls_session_get_desc(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.SH "DESCRIPTION"
This function returns a string describing the current session.
The string is null terminated and allocated using \fBgnutls_malloc()\fP.

If initial negotiation is not complete when this function is called,
\fBNULL\fP will be returned.
.SH "RETURNS"
a description of the protocols and algorithms in the current session.
.SH "SINCE"
3.1.10
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
