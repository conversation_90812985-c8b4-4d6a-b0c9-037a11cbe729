.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_export_rsa_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_export_rsa_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_export_rsa_raw(gnutls_pubkey_t " key ", gnutls_datum_t * " m ", gnutls_datum_t * " e ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
Holds the certificate
.IP "gnutls_datum_t * m" 12
will hold the modulus (may be \fBNULL\fP)
.IP "gnutls_datum_t * e" 12
will hold the public exponent (may be \fBNULL\fP)
.SH "DESCRIPTION"
This function will export the RSA public key's parameters found in
the given structure.  The new parameters will be allocated using
\fBgnutls_malloc()\fP and will be stored in the appropriate datum.

This function allows for \fBNULL\fP parameters since 3.4.1.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
