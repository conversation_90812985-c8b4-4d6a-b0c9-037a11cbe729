+{
   locale_version => 1.31,
   entry => <<'ENTRY', # for DUCET v13.0.0
0063 0068 ; [.1FD7.0020.0002] # <LATIN SMALL LETTER C, LATIN SMALL LETTER H>
0043 0068 ; [.1FD7.0020.0007] # <LATIN CAPITAL LETTER C, LATIN SMALL LETTER H>
0043 0048 ; [.1FD7.0020.0008] # <LATIN CAPITAL LETTER C, LATIN CAPITAL LETTER H>
0064 0064 ; [.1FEC.0020.0002] # <LATIN SMALL LETTER D, LATIN SMALL LETTER D>
0044 0064 ; [.1FEC.0020.0007] # <LATIN CAPITAL LETTER D, LATIN SMALL LETTER D>
0044 0044 ; [.1FEC.0020.0008] # <LATIN CAPITAL LETTER D, LATIN CAPITAL LETTER D>
0066 0066 ; [.2043.0020.0002] # <LATIN SMALL LETTER F, LATIN SMALL LETTER F>
0046 0066 ; [.2043.0020.0007] # <LATIN CAPITAL LETTER F, LATIN SMALL LETTER F>
0046 0046 ; [.2043.0020.0008] # <LATIN CAPITAL LETTER F, LATIN CAPITAL LETTER F>
006E 0067 ; [.2052.0020.0002] # <LATIN SMALL LETTER N, LATIN SMALL LETTER G>
004E 0067 ; [.2052.0020.0007] # <LATIN CAPITAL LETTER N, LATIN SMALL LETTER G>
004E 0047 ; [.2052.0020.0008] # <LATIN CAPITAL LETTER N, LATIN CAPITAL LETTER G>
006C 006C ; [.20D7.0020.0002] # <LATIN SMALL LETTER L, LATIN SMALL LETTER L>
004C 006C ; [.20D7.0020.0007] # <LATIN CAPITAL LETTER L, LATIN SMALL LETTER L>
004C 004C ; [.20D7.0020.0008] # <LATIN CAPITAL LETTER L, LATIN CAPITAL LETTER L>
0070 0068 ; [.216C.0020.0002] # <LATIN SMALL LETTER P, LATIN SMALL LETTER H>
0050 0068 ; [.216C.0020.0007] # <LATIN CAPITAL LETTER P, LATIN SMALL LETTER H>
0050 0048 ; [.216C.0020.0008] # <LATIN CAPITAL LETTER P, LATIN CAPITAL LETTER H>
0072 0068 ; [.2194.0020.0002] # <LATIN SMALL LETTER R, LATIN SMALL LETTER H>
0052 0068 ; [.2194.0020.0007] # <LATIN CAPITAL LETTER R, LATIN SMALL LETTER H>
0052 0048 ; [.2194.0020.0008] # <LATIN CAPITAL LETTER R, LATIN CAPITAL LETTER H>
0074 0068 ; [.21F8.0020.0002] # <LATIN SMALL LETTER T, LATIN SMALL LETTER H>
0054 0068 ; [.21F8.0020.0007] # <LATIN CAPITAL LETTER T, LATIN SMALL LETTER H>
0054 0048 ; [.21F8.0020.0008] # <LATIN CAPITAL LETTER T, LATIN CAPITAL LETTER H>
ENTRY
};
