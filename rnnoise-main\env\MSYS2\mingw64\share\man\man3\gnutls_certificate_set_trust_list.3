.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_trust_list" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_trust_list \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void gnutls_certificate_set_trust_list(gnutls_certificate_credentials_t " res ", gnutls_x509_trust_list_t " tlist ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "gnutls_x509_trust_list_t tlist" 12
is a \fBgnutls_x509_trust_list_t\fP type
.IP "unsigned flags" 12
must be zero
.SH "DESCRIPTION"
This function sets a trust list in the gnutls_certificate_credentials_t type.

Note that the  \fItlist\fP will become part of the credentials
structure and must not be deallocated. It will be automatically deallocated
when the  \fIres\fP structure is deinitialized.
.SH "SINCE"
3.2.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
