.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srp_base64_decode2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srp_base64_decode2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srp_base64_decode2(const gnutls_datum_t * " b64_data ", gnutls_datum_t * " result ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * b64_data" 12
contains the encoded data
.IP "gnutls_datum_t * result" 12
the place where decoded data lie
.SH "DESCRIPTION"
This function will decode the given encoded data. The decoded data
will be allocated, and stored into result.  It will decode using
the base64 algorithm as used in libsrp.

You should use \fBgnutls_free()\fP to free the returned data.

Warning!  This base64 encoding is not the "standard" encoding, so
do not use it for non\-SRP purposes.
.SH "RETURNS"
0 on success, or an error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
