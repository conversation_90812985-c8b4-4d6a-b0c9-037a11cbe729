/* -*- mode: C; buffer-read-only: t -*-
 * !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
 * This file is built by regen/ebcdic.pl.
 * Any changes made here will be lost!
 */


#ifndef PERL_EBCDIC_TABLES_H_   /* Guard against nested #includes */
#define PERL_EBCDIC_TABLES_H_   1

/* This file contains definitions for various tables used in EBCDIC handling.
 * More info is in utfebcdic.h
 *
 * Some of the tables are adapted from
 *      https://bjoern.hoehrmann.de/utf-8/decoder/dfa/
 * which requires this copyright notice:

Copyright (c) 2008-2009 B<PERSON>ern <PERSON>rman<PERSON> <<EMAIL>>

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
of the Software, and to permit persons to whom the Software is furnished to do
so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

*/

#if 'A' == 193 /* EBCDIC 1047 */ \
     && '\\' == 224 && '[' == 173 && ']' == 189 && '{' == 192 && '}' == 208 \
     && '^' == 95 && '~' == 161 && '!' == 90 && '#' == 123 && '|' == 79 \
     && '$' == 91 && '@' == 124 && '`' == 121 && '\n' == 21

/* Index is ASCII platform code point; value is EBCDIC 1047 equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_a2e[256];
#  else
    EXTCONST U8 PL_a2e[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x37,0x2D,0x2E,0x2F,0x16,0x05,0x15,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x3C,0x3D,0x32,0x26,0x18,0x19,0x3F,0x27,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x40,0x5A,0x7F,0x7B,0x5B,0x6C,0x50,0x7D,0x4D,0x5D,0x5C,0x4E,0x6B,0x60,0x4B,0x61,
/*3_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0x7A,0x5E,0x4C,0x7E,0x6E,0x6F,
/*4_*/0x7C,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,
/*5_*/0xD7,0xD8,0xD9,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xAD,0xE0,0xBD,0x5F,0x6D,
/*6_*/0x79,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x91,0x92,0x93,0x94,0x95,0x96,
/*7_*/0x97,0x98,0x99,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xC0,0x4F,0xD0,0xA1,0x07,
/*8_*/0x20,0x21,0x22,0x23,0x24,0x25,0x06,0x17,0x28,0x29,0x2A,0x2B,0x2C,0x09,0x0A,0x1B,
/*9_*/0x30,0x31,0x1A,0x33,0x34,0x35,0x36,0x08,0x38,0x39,0x3A,0x3B,0x04,0x14,0x3E,0xFF,
/*A_*/0x41,0xAA,0x4A,0xB1,0x9F,0xB2,0x6A,0xB5,0xBB,0xB4,0x9A,0x8A,0xB0,0xCA,0xAF,0xBC,
/*B_*/0x90,0x8F,0xEA,0xFA,0xBE,0xA0,0xB6,0xB3,0x9D,0xDA,0x9B,0x8B,0xB7,0xB8,0xB9,0xAB,
/*C_*/0x64,0x65,0x62,0x66,0x63,0x67,0x9E,0x68,0x74,0x71,0x72,0x73,0x78,0x75,0x76,0x77,
/*D_*/0xAC,0x69,0xED,0xEE,0xEB,0xEF,0xEC,0xBF,0x80,0xFD,0xFE,0xFB,0xFC,0xBA,0xAE,0x59,
/*E_*/0x44,0x45,0x42,0x46,0x43,0x47,0x9C,0x48,0x54,0x51,0x52,0x53,0x58,0x55,0x56,0x57,
/*F_*/0x8C,0x49,0xCD,0xCE,0xCB,0xCF,0xCC,0xE1,0x70,0xDD,0xDE,0xDB,0xDC,0x8D,0x8E,0xDF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 1047 code point; value is ASCII platform equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_e2a[256];
#  else
    EXTCONST U8 PL_e2a[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x9C,0x09,0x86,0x7F,0x97,0x8D,0x8E,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x9D,0x0A,0x08,0x87,0x18,0x19,0x92,0x8F,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x80,0x81,0x82,0x83,0x84,0x85,0x17,0x1B,0x88,0x89,0x8A,0x8B,0x8C,0x05,0x06,0x07,
/*3_*/0x90,0x91,0x16,0x93,0x94,0x95,0x96,0x04,0x98,0x99,0x9A,0x9B,0x14,0x15,0x9E,0x1A,
/*4_*/0x20,0xA0,0xE2,0xE4,0xE0,0xE1,0xE3,0xE5,0xE7,0xF1,0xA2,0x2E,0x3C,0x28,0x2B,0x7C,
/*5_*/0x26,0xE9,0xEA,0xEB,0xE8,0xED,0xEE,0xEF,0xEC,0xDF,0x21,0x24,0x2A,0x29,0x3B,0x5E,
/*6_*/0x2D,0x2F,0xC2,0xC4,0xC0,0xC1,0xC3,0xC5,0xC7,0xD1,0xA6,0x2C,0x25,0x5F,0x3E,0x3F,
/*7_*/0xF8,0xC9,0xCA,0xCB,0xC8,0xCD,0xCE,0xCF,0xCC,0x60,0x3A,0x23,0x40,0x27,0x3D,0x22,
/*8_*/0xD8,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0xAB,0xBB,0xF0,0xFD,0xFE,0xB1,
/*9_*/0xB0,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,0x70,0x71,0x72,0xAA,0xBA,0xE6,0xB8,0xC6,0xA4,
/*A_*/0xB5,0x7E,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7A,0xA1,0xBF,0xD0,0x5B,0xDE,0xAE,
/*B_*/0xAC,0xA3,0xA5,0xB7,0xA9,0xA7,0xB6,0xBC,0xBD,0xBE,0xDD,0xA8,0xAF,0x5D,0xB4,0xD7,
/*C_*/0x7B,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0xAD,0xF4,0xF6,0xF2,0xF3,0xF5,
/*D_*/0x7D,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,0x50,0x51,0x52,0xB9,0xFB,0xFC,0xF9,0xFA,0xFF,
/*E_*/0x5C,0xF7,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5A,0xB2,0xD4,0xD6,0xD2,0xD3,0xD5,
/*F_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0xB3,0xDB,0xDC,0xD9,0xDA,0x9F
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* (Confusingly named) Index is EBCDIC 1047 I8 byte; value is
 * EBCDIC 1047 UTF-EBCDIC equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_utf2e[256];
#  else
    EXTCONST U8 PL_utf2e[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x37,0x2D,0x2E,0x2F,0x16,0x05,0x15,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x3C,0x3D,0x32,0x26,0x18,0x19,0x3F,0x27,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x40,0x5A,0x7F,0x7B,0x5B,0x6C,0x50,0x7D,0x4D,0x5D,0x5C,0x4E,0x6B,0x60,0x4B,0x61,
/*3_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0x7A,0x5E,0x4C,0x7E,0x6E,0x6F,
/*4_*/0x7C,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,
/*5_*/0xD7,0xD8,0xD9,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xAD,0xE0,0xBD,0x5F,0x6D,
/*6_*/0x79,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x91,0x92,0x93,0x94,0x95,0x96,
/*7_*/0x97,0x98,0x99,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xC0,0x4F,0xD0,0xA1,0x07,
/*8_*/0x20,0x21,0x22,0x23,0x24,0x25,0x06,0x17,0x28,0x29,0x2A,0x2B,0x2C,0x09,0x0A,0x1B,
/*9_*/0x30,0x31,0x1A,0x33,0x34,0x35,0x36,0x08,0x38,0x39,0x3A,0x3B,0x04,0x14,0x3E,0xFF,
/*A_*/0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4A,0x51,0x52,0x53,0x54,0x55,0x56,
/*B_*/0x57,0x58,0x59,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6A,0x70,0x71,0x72,0x73,
/*C_*/0x74,0x75,0x76,0x77,0x78,0x80,0x8A,0x8B,0x8C,0x8D,0x8E,0x8F,0x90,0x9A,0x9B,0x9C,
/*D_*/0x9D,0x9E,0x9F,0xA0,0xAA,0xAB,0xAC,0xAE,0xAF,0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,
/*E_*/0xB7,0xB8,0xB9,0xBA,0xBB,0xBC,0xBE,0xBF,0xCA,0xCB,0xCC,0xCD,0xCE,0xCF,0xDA,0xDB,
/*F_*/0xDC,0xDD,0xDE,0xDF,0xE1,0xEA,0xEB,0xEC,0xED,0xEE,0xEF,0xFA,0xFB,0xFC,0xFD,0xFE
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* (Confusingly named) Index is EBCDIC 1047 UTF-EBCDIC byte; value is
 * EBCDIC 1047 I8 equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_e2utf[256];
#  else
    EXTCONST U8 PL_e2utf[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x9C,0x09,0x86,0x7F,0x97,0x8D,0x8E,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x9D,0x0A,0x08,0x87,0x18,0x19,0x92,0x8F,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x80,0x81,0x82,0x83,0x84,0x85,0x17,0x1B,0x88,0x89,0x8A,0x8B,0x8C,0x05,0x06,0x07,
/*3_*/0x90,0x91,0x16,0x93,0x94,0x95,0x96,0x04,0x98,0x99,0x9A,0x9B,0x14,0x15,0x9E,0x1A,
/*4_*/0x20,0xA0,0xA1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0x2E,0x3C,0x28,0x2B,0x7C,
/*5_*/0x26,0xAA,0xAB,0xAC,0xAD,0xAE,0xAF,0xB0,0xB1,0xB2,0x21,0x24,0x2A,0x29,0x3B,0x5E,
/*6_*/0x2D,0x2F,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0xBA,0xBB,0x2C,0x25,0x5F,0x3E,0x3F,
/*7_*/0xBC,0xBD,0xBE,0xBF,0xC0,0xC1,0xC2,0xC3,0xC4,0x60,0x3A,0x23,0x40,0x27,0x3D,0x22,
/*8_*/0xC5,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0xC6,0xC7,0xC8,0xC9,0xCA,0xCB,
/*9_*/0xCC,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,0x70,0x71,0x72,0xCD,0xCE,0xCF,0xD0,0xD1,0xD2,
/*A_*/0xD3,0x7E,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7A,0xD4,0xD5,0xD6,0x5B,0xD7,0xD8,
/*B_*/0xD9,0xDA,0xDB,0xDC,0xDD,0xDE,0xDF,0xE0,0xE1,0xE2,0xE3,0xE4,0xE5,0x5D,0xE6,0xE7,
/*C_*/0x7B,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0xE8,0xE9,0xEA,0xEB,0xEC,0xED,
/*D_*/0x7D,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,0x50,0x51,0x52,0xEE,0xEF,0xF0,0xF1,0xF2,0xF3,
/*E_*/0x5C,0xF4,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5A,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,
/*F_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0xFB,0xFC,0xFD,0xFE,0xFF,0x9F
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 1047 UTF-EBCDIC byte; value is UTF8SKIP for start bytes
 * (including for overlongs); 1 for continuation.  Adapted from the shadow
 * flags table in tr16.  The entries marked 9 in tr16 are continuation bytes
 * and are marked as length 1 here so that we can recover. */
#  ifndef DOINIT
    EXTCONST U8 PL_utf8skip[256];
#  else
    EXTCONST U8 PL_utf8skip[256] = {
/*     _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
/*0_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*1_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*2_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*3_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*4_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*5_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*6_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*7_*/  1,  1,  1,  1,  2,  2,  2,  2,  2,  1,  1,  1,  1,  1,  1,  1,
/*8_*/  2,  1,  1,  1,  1,  1,  1,  1,  1,  1,  2,  2,  2,  2,  2,  2,
/*9_*/  2,  1,  1,  1,  1,  1,  1,  1,  1,  1,  2,  2,  2,  2,  2,  2,
/*A_*/  2,  1,  1,  1,  1,  1,  1,  1,  1,  1,  2,  2,  2,  1,  2,  2,
/*B_*/  2,  2,  2,  2,  2,  2,  2,  3,  3,  3,  3,  3,  3,  1,  3,  3,
/*C_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  3,  3,  3,  3,  3,  3,
/*D_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  3,  3,  4,  4,  4,  4,
/*E_*/  1,  4,  1,  1,  1,  1,  1,  1,  1,  1,  4,  4,  4,  5,  5,  5,
/*F_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  5,  6,  6,  7, 14,  1
/*     _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
};
#  endif

/* Index is EBCDIC 1047 code point; value is its lowercase equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_latin1_lc[256];
#  else
    EXTCONST U8 PL_latin1_lc[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,
/*3_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0x3E,0x3F,
/*4_*/0x40,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,
/*5_*/0x50,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5A,0x5B,0x5C,0x5D,0x5E,0x5F,
/*6_*/0x60,0x61,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,
/*7_*/0x70,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x79,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F,
/*8_*/0x70,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8A,0x8B,0x8C,0x8D,0x8E,0x8F,
/*9_*/0x90,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0x9A,0x9B,0x9C,0x9D,0x9C,0x9F,
/*A_*/0xA0,0xA1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xAA,0xAB,0x8C,0xAD,0x8E,0xAF,
/*B_*/0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0x8D,0xBB,0xBC,0xBD,0xBE,0xBF,
/*C_*/0xC0,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0xCA,0xCB,0xCC,0xCD,0xCE,0xCF,
/*D_*/0xD0,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0xDA,0xDB,0xDC,0xDD,0xDE,0xDF,
/*E_*/0xE0,0xE1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xEA,0xCB,0xCC,0xCD,0xCE,0xCF,
/*F_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,0xDB,0xDC,0xDD,0xDE,0xFF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 1047 code point; value is its uppercase equivalent.
 * The 'mod' in the name means that codepoints whose uppercase is above 255 or
 * longer than 1 character map to LATIN SMALL LETTER Y WITH DIARESIS */
#  ifndef DOINIT
    EXTCONST U8 PL_mod_latin1_uc[256];
#  else
    EXTCONST U8 PL_mod_latin1_uc[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,
/*3_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0x3E,0x3F,
/*4_*/0x40,0x41,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,
/*5_*/0x50,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0xDF,0x5A,0x5B,0x5C,0x5D,0x5E,0x5F,
/*6_*/0x60,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,
/*7_*/0x80,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F,
/*8_*/0x80,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0x8A,0x8B,0xAC,0xBA,0xAE,0x8F,
/*9_*/0x90,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,0xD7,0xD8,0xD9,0x9A,0x9B,0x9E,0x9D,0x9E,0x9F,
/*A_*/0xDF,0xA1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xAA,0xAB,0xAC,0xAD,0xAE,0xAF,
/*B_*/0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0xBA,0xBB,0xBC,0xBD,0xBE,0xBF,
/*C_*/0xC0,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0xCA,0xEB,0xEC,0xED,0xEE,0xEF,
/*D_*/0xD0,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,0xD7,0xD8,0xD9,0xDA,0xFB,0xFC,0xFD,0xFE,0xDF,
/*E_*/0xE0,0xE1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xEA,0xEB,0xEC,0xED,0xEE,0xEF,
/*F_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,0xFB,0xFC,0xFD,0xFE,0xFF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 1047 code point; For A-Z, value is a-z; for a-z, value
 * is A-Z; all other code points map to themselves */
#  ifndef DOINIT
    EXTCONST U8 PL_fold[256];
#  else
    EXTCONST U8 PL_fold[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,
/*3_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0x3E,0x3F,
/*4_*/0x40,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,
/*5_*/0x50,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5A,0x5B,0x5C,0x5D,0x5E,0x5F,
/*6_*/0x60,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,
/*7_*/0x70,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F,
/*8_*/0x80,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0x8A,0x8B,0x8C,0x8D,0x8E,0x8F,
/*9_*/0x90,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,0xD7,0xD8,0xD9,0x9A,0x9B,0x9C,0x9D,0x9E,0x9F,
/*A_*/0xA0,0xA1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xAA,0xAB,0xAC,0xAD,0xAE,0xAF,
/*B_*/0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0xBA,0xBB,0xBC,0xBD,0xBE,0xBF,
/*C_*/0xC0,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0xCA,0xCB,0xCC,0xCD,0xCE,0xCF,
/*D_*/0xD0,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0xDA,0xDB,0xDC,0xDD,0xDE,0xDF,
/*E_*/0xE0,0xE1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xEA,0xEB,0xEC,0xED,0xEE,0xEF,
/*F_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,0xFB,0xFC,0xFD,0xFE,0xFF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 1047 code point; value is its other fold-pair equivalent
 * (A => a; a => A, etc) in the 0-255 range.  If no such equivalent, value is
 * the code point itself */
#  ifndef DOINIT
    EXTCONST U8 PL_fold_latin1[256];
#  else
    EXTCONST U8 PL_fold_latin1[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,
/*3_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0x3E,0x3F,
/*4_*/0x40,0x41,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,
/*5_*/0x50,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x59,0x5A,0x5B,0x5C,0x5D,0x5E,0x5F,
/*6_*/0x60,0x61,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,
/*7_*/0x80,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x79,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F,
/*8_*/0x70,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0x8A,0x8B,0xAC,0xBA,0xAE,0x8F,
/*9_*/0x90,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,0xD7,0xD8,0xD9,0x9A,0x9B,0x9E,0x9D,0x9C,0x9F,
/*A_*/0xA0,0xA1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xAA,0xAB,0x8C,0xAD,0x8E,0xAF,
/*B_*/0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0x8D,0xBB,0xBC,0xBD,0xBE,0xBF,
/*C_*/0xC0,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0xCA,0xEB,0xEC,0xED,0xEE,0xEF,
/*D_*/0xD0,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0xDA,0xFB,0xFC,0xFD,0xFE,0xDF,
/*E_*/0xE0,0xE1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xEA,0xCB,0xCC,0xCD,0xCE,0xCF,
/*F_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,0xDB,0xDC,0xDD,0xDE,0xFF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif


/* The table below is adapted from
 *      https://bjoern.hoehrmann.de/utf-8/decoder/dfa/
 * See copyright notice at the beginning of this file.
 */

#  ifndef DOINIT
    EXTCONST U8 PL_extended_utf8_dfa_tab[416];
#  else
    EXTCONST U8 PL_extended_utf8_dfa_tab[416] = {
/*         _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
/*0_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*1_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*2_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*3_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*4_    */  0,  7,  7,  8,  8,  9,  9,  9,  9, 10, 10,  0,  0,  0,  0,  0,
/*5_    */  0, 10, 10, 10, 10, 10, 10, 11, 11, 11,  0,  0,  0,  0,  0,  0,
/*6_    */  0,  0, 11, 11, 11, 11, 11, 11, 11, 11, 11,  0,  0,  0,  0,  0,
/*7_    */ 11, 11, 11, 11,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,  0,
/*8_    */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*9_    */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*A_    */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  0,  2,  2,
/*B_    */  2,  2,  2,  2,  2,  2,  2,  1,  3,  3,  3,  3,  3,  0,  3,  3,
/*C_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  3,  3,  3,  3,
/*D_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3, 12,  4,  4,  4,
/*E_    */  0,  4,  0,  0,  0,  0,  0,  0,  0,  0,  4,  4,  4, 13,  5,  5,
/*F_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  5, 14,  6, 15,  1,  0,
/*N0=  0*/  0,  1, 16, 32, 48, 64, 80,  1,  1,  1,  1,  1, 96,112,128,144,
/*N1= 16*/  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  1,  1,  1,  1,
/*N2= 32*/  1,  1,  1,  1,  1,  1,  1, 16, 16, 16, 16, 16,  1,  1,  1,  1,
/*N3= 48*/  1,  1,  1,  1,  1,  1,  1, 32, 32, 32, 32, 32,  1,  1,  1,  1,
/*N4= 64*/  1,  1,  1,  1,  1,  1,  1, 48, 48, 48, 48, 48,  1,  1,  1,  1,
/*N5= 80*/  1,  1,  1,  1,  1,  1,  1, 64, 64, 64, 64, 64,  1,  1,  1,  1,
/*N6= 96*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 32,  1,  1,  1,  1,
/*N7=112*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 48, 48,  1,  1,  1,  1,
/*N8=128*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 64, 64, 64,  1,  1,  1,  1,
/*N9=144*/  1,  1,  1,  1,  1,  1,  1,  1, 80, 80, 80, 80,  1,  1,  1,  1
/*          0   1   2   3   4   5   6   7   8   9  10  11  12  13  14 15*/
};
#  endif


/* The table below is adapted from
 *      https://bjoern.hoehrmann.de/utf-8/decoder/dfa/
 * See copyright notice at the beginning of this file.
 */

#  ifndef DOINIT
    EXTCONST U16 PL_strict_utf8_dfa_tab[624];
#  else
    EXTCONST U16 PL_strict_utf8_dfa_tab[624] = {
/*          _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
/*0_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*1_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*2_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*3_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*4_     */  0, 10, 11, 12, 12, 12, 12, 12, 12, 13, 14,  0,  0,  0,  0,  0,
/*5_     */  0, 13, 14, 13, 14, 15, 16, 17, 18, 17,  0,  0,  0,  0,  0,  0,
/*6_     */  0,  0, 18, 17, 18, 19, 20, 17, 18, 17, 18,  0,  0,  0,  0,  0,
/*7_     */ 17, 18, 21, 22,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,  0,
/*8_     */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*9_     */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*A_     */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  0,  2,  2,
/*B_     */  2,  2,  2,  2,  2,  2,  2,  1,  3,  3,  3,  3,  3,  0,  3,  3,
/*C_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  3,  3,  3,  3,
/*D_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  8,  6,  4,  5,
/*E_     */  0,  4,  0,  0,  0,  0,  0,  0,  0,  0,  5,  4,  5,  9,  7,  1,
/*F_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,  1,  1,  1,  1,  0,
/*N0 =  0*/  0,  1, 23, 46, 69,138,115,184, 92,161,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*N1 = 23*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*N2 = 46*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23,
/*N3 = 69*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46,
/*N4 = 92*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46,
/*N5 =115*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46, 46, 46, 46,  1,  1, 46,207,
/*N6 =138*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46,276,
/*N7 =161*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 69,322, 69,322, 69,322, 69,322, 69,322,
/*N8 =184*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 69,322,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*N9 =207*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 23, 23, 23, 23, 23,230,253, 23, 23, 23, 23, 23,299,
/*N10=230*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,  0,  1,  1,  1,  1,  1,  1,
/*N11=253*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,
/*N12=276*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23,299,
/*N13=299*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,  1,
/*N14=322*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46,345,
/*N15=345*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23,299
/*           0   1   2   3   4   5   6   7   8   9  10  11  12  13  14  15  16  17  18  19  20  21 22*/
};
#  endif


/* The table below is adapted from
 *      https://bjoern.hoehrmann.de/utf-8/decoder/dfa/
 * See copyright notice at the beginning of this file.
 */

#  ifndef DOINIT
    EXTCONST U8 PL_c9_utf8_dfa_tab[368];
#  else
    EXTCONST U8 PL_c9_utf8_dfa_tab[368] = {
/*        _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
/*0_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*1_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*2_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*3_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*4_   */  0,  9,  9, 10, 10, 10, 10, 10, 10, 11, 11,  0,  0,  0,  0,  0,
/*5_   */  0, 11, 11, 11, 11, 11, 11, 12, 12, 12,  0,  0,  0,  0,  0,  0,
/*6_   */  0,  0, 12, 12, 12, 13, 13, 12, 12, 12, 12,  0,  0,  0,  0,  0,
/*7_   */ 12, 12, 12, 12,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,  0,
/*8_   */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*9_   */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*A_   */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  0,  2,  2,
/*B_   */  2,  2,  2,  2,  2,  2,  2,  1,  3,  3,  3,  3,  3,  0,  3,  3,
/*C_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  3,  3,  3,  3,
/*D_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  6,  5,  4,  4,
/*E_   */  0,  4,  0,  0,  0,  0,  0,  0,  0,  0,  4,  4,  4,  8,  7,  1,
/*F_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,  1,  1,  1,  1,  0,
/*N0= 0*/  0,  1, 14, 28, 42, 70, 56, 98, 84,  1,  1,  1,  1,  1,
/*N1=14*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,
/*N2=28*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 14, 14, 14, 14, 14,
/*N3=42*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 28, 28, 28, 28, 28,
/*N4=56*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 28, 28,
/*N5=70*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 28, 28, 28, 28,  1,
/*N6=84*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 42, 42, 42,
/*N7=98*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 42,  1,  1,  1,  1
/*         0   1   2   3   4   5   6   7   8   9  10  11  12 13*/
};
#  endif

#endif	/* EBCDIC 1047 */

#if 'A' == 193 /* EBCDIC 037 */ \
     && '\\' == 224 && '[' == 186 && ']' == 187 && '{' == 192 && '}' == 208 \
     && '^' == 176 && '~' == 161 && '!' == 90 && '#' == 123 && '|' == 79 \
     && '$' == 91 && '@' == 124 && '`' == 121 && '\n' == 37

/* Index is ASCII platform code point; value is EBCDIC 037 equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_a2e[256];
#  else
    EXTCONST U8 PL_a2e[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x37,0x2D,0x2E,0x2F,0x16,0x05,0x25,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x3C,0x3D,0x32,0x26,0x18,0x19,0x3F,0x27,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x40,0x5A,0x7F,0x7B,0x5B,0x6C,0x50,0x7D,0x4D,0x5D,0x5C,0x4E,0x6B,0x60,0x4B,0x61,
/*3_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0x7A,0x5E,0x4C,0x7E,0x6E,0x6F,
/*4_*/0x7C,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,
/*5_*/0xD7,0xD8,0xD9,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xBA,0xE0,0xBB,0xB0,0x6D,
/*6_*/0x79,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x91,0x92,0x93,0x94,0x95,0x96,
/*7_*/0x97,0x98,0x99,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xC0,0x4F,0xD0,0xA1,0x07,
/*8_*/0x20,0x21,0x22,0x23,0x24,0x15,0x06,0x17,0x28,0x29,0x2A,0x2B,0x2C,0x09,0x0A,0x1B,
/*9_*/0x30,0x31,0x1A,0x33,0x34,0x35,0x36,0x08,0x38,0x39,0x3A,0x3B,0x04,0x14,0x3E,0xFF,
/*A_*/0x41,0xAA,0x4A,0xB1,0x9F,0xB2,0x6A,0xB5,0xBD,0xB4,0x9A,0x8A,0x5F,0xCA,0xAF,0xBC,
/*B_*/0x90,0x8F,0xEA,0xFA,0xBE,0xA0,0xB6,0xB3,0x9D,0xDA,0x9B,0x8B,0xB7,0xB8,0xB9,0xAB,
/*C_*/0x64,0x65,0x62,0x66,0x63,0x67,0x9E,0x68,0x74,0x71,0x72,0x73,0x78,0x75,0x76,0x77,
/*D_*/0xAC,0x69,0xED,0xEE,0xEB,0xEF,0xEC,0xBF,0x80,0xFD,0xFE,0xFB,0xFC,0xAD,0xAE,0x59,
/*E_*/0x44,0x45,0x42,0x46,0x43,0x47,0x9C,0x48,0x54,0x51,0x52,0x53,0x58,0x55,0x56,0x57,
/*F_*/0x8C,0x49,0xCD,0xCE,0xCB,0xCF,0xCC,0xE1,0x70,0xDD,0xDE,0xDB,0xDC,0x8D,0x8E,0xDF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 037 code point; value is ASCII platform equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_e2a[256];
#  else
    EXTCONST U8 PL_e2a[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x9C,0x09,0x86,0x7F,0x97,0x8D,0x8E,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x9D,0x85,0x08,0x87,0x18,0x19,0x92,0x8F,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x80,0x81,0x82,0x83,0x84,0x0A,0x17,0x1B,0x88,0x89,0x8A,0x8B,0x8C,0x05,0x06,0x07,
/*3_*/0x90,0x91,0x16,0x93,0x94,0x95,0x96,0x04,0x98,0x99,0x9A,0x9B,0x14,0x15,0x9E,0x1A,
/*4_*/0x20,0xA0,0xE2,0xE4,0xE0,0xE1,0xE3,0xE5,0xE7,0xF1,0xA2,0x2E,0x3C,0x28,0x2B,0x7C,
/*5_*/0x26,0xE9,0xEA,0xEB,0xE8,0xED,0xEE,0xEF,0xEC,0xDF,0x21,0x24,0x2A,0x29,0x3B,0xAC,
/*6_*/0x2D,0x2F,0xC2,0xC4,0xC0,0xC1,0xC3,0xC5,0xC7,0xD1,0xA6,0x2C,0x25,0x5F,0x3E,0x3F,
/*7_*/0xF8,0xC9,0xCA,0xCB,0xC8,0xCD,0xCE,0xCF,0xCC,0x60,0x3A,0x23,0x40,0x27,0x3D,0x22,
/*8_*/0xD8,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0xAB,0xBB,0xF0,0xFD,0xFE,0xB1,
/*9_*/0xB0,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,0x70,0x71,0x72,0xAA,0xBA,0xE6,0xB8,0xC6,0xA4,
/*A_*/0xB5,0x7E,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7A,0xA1,0xBF,0xD0,0xDD,0xDE,0xAE,
/*B_*/0x5E,0xA3,0xA5,0xB7,0xA9,0xA7,0xB6,0xBC,0xBD,0xBE,0x5B,0x5D,0xAF,0xA8,0xB4,0xD7,
/*C_*/0x7B,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0xAD,0xF4,0xF6,0xF2,0xF3,0xF5,
/*D_*/0x7D,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,0x50,0x51,0x52,0xB9,0xFB,0xFC,0xF9,0xFA,0xFF,
/*E_*/0x5C,0xF7,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5A,0xB2,0xD4,0xD6,0xD2,0xD3,0xD5,
/*F_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0xB3,0xDB,0xDC,0xD9,0xDA,0x9F
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* (Confusingly named) Index is EBCDIC 037 I8 byte; value is
 * EBCDIC 037 UTF-EBCDIC equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_utf2e[256];
#  else
    EXTCONST U8 PL_utf2e[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x37,0x2D,0x2E,0x2F,0x16,0x05,0x25,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x3C,0x3D,0x32,0x26,0x18,0x19,0x3F,0x27,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x40,0x5A,0x7F,0x7B,0x5B,0x6C,0x50,0x7D,0x4D,0x5D,0x5C,0x4E,0x6B,0x60,0x4B,0x61,
/*3_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0x7A,0x5E,0x4C,0x7E,0x6E,0x6F,
/*4_*/0x7C,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,
/*5_*/0xD7,0xD8,0xD9,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xBA,0xE0,0xBB,0xB0,0x6D,
/*6_*/0x79,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x91,0x92,0x93,0x94,0x95,0x96,
/*7_*/0x97,0x98,0x99,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xC0,0x4F,0xD0,0xA1,0x07,
/*8_*/0x20,0x21,0x22,0x23,0x24,0x15,0x06,0x17,0x28,0x29,0x2A,0x2B,0x2C,0x09,0x0A,0x1B,
/*9_*/0x30,0x31,0x1A,0x33,0x34,0x35,0x36,0x08,0x38,0x39,0x3A,0x3B,0x04,0x14,0x3E,0xFF,
/*A_*/0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4A,0x51,0x52,0x53,0x54,0x55,0x56,
/*B_*/0x57,0x58,0x59,0x5F,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6A,0x70,0x71,0x72,
/*C_*/0x73,0x74,0x75,0x76,0x77,0x78,0x80,0x8A,0x8B,0x8C,0x8D,0x8E,0x8F,0x90,0x9A,0x9B,
/*D_*/0x9C,0x9D,0x9E,0x9F,0xA0,0xAA,0xAB,0xAC,0xAD,0xAE,0xAF,0xB1,0xB2,0xB3,0xB4,0xB5,
/*E_*/0xB6,0xB7,0xB8,0xB9,0xBC,0xBD,0xBE,0xBF,0xCA,0xCB,0xCC,0xCD,0xCE,0xCF,0xDA,0xDB,
/*F_*/0xDC,0xDD,0xDE,0xDF,0xE1,0xEA,0xEB,0xEC,0xED,0xEE,0xEF,0xFA,0xFB,0xFC,0xFD,0xFE
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* (Confusingly named) Index is EBCDIC 037 UTF-EBCDIC byte; value is
 * EBCDIC 037 I8 equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_e2utf[256];
#  else
    EXTCONST U8 PL_e2utf[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x9C,0x09,0x86,0x7F,0x97,0x8D,0x8E,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x9D,0x85,0x08,0x87,0x18,0x19,0x92,0x8F,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x80,0x81,0x82,0x83,0x84,0x0A,0x17,0x1B,0x88,0x89,0x8A,0x8B,0x8C,0x05,0x06,0x07,
/*3_*/0x90,0x91,0x16,0x93,0x94,0x95,0x96,0x04,0x98,0x99,0x9A,0x9B,0x14,0x15,0x9E,0x1A,
/*4_*/0x20,0xA0,0xA1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0x2E,0x3C,0x28,0x2B,0x7C,
/*5_*/0x26,0xAA,0xAB,0xAC,0xAD,0xAE,0xAF,0xB0,0xB1,0xB2,0x21,0x24,0x2A,0x29,0x3B,0xB3,
/*6_*/0x2D,0x2F,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0xBA,0xBB,0xBC,0x2C,0x25,0x5F,0x3E,0x3F,
/*7_*/0xBD,0xBE,0xBF,0xC0,0xC1,0xC2,0xC3,0xC4,0xC5,0x60,0x3A,0x23,0x40,0x27,0x3D,0x22,
/*8_*/0xC6,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0xC7,0xC8,0xC9,0xCA,0xCB,0xCC,
/*9_*/0xCD,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,0x70,0x71,0x72,0xCE,0xCF,0xD0,0xD1,0xD2,0xD3,
/*A_*/0xD4,0x7E,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7A,0xD5,0xD6,0xD7,0xD8,0xD9,0xDA,
/*B_*/0x5E,0xDB,0xDC,0xDD,0xDE,0xDF,0xE0,0xE1,0xE2,0xE3,0x5B,0x5D,0xE4,0xE5,0xE6,0xE7,
/*C_*/0x7B,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0xE8,0xE9,0xEA,0xEB,0xEC,0xED,
/*D_*/0x7D,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,0x50,0x51,0x52,0xEE,0xEF,0xF0,0xF1,0xF2,0xF3,
/*E_*/0x5C,0xF4,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5A,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,
/*F_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0xFB,0xFC,0xFD,0xFE,0xFF,0x9F
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 037 UTF-EBCDIC byte; value is UTF8SKIP for start bytes
 * (including for overlongs); 1 for continuation.  Adapted from the shadow
 * flags table in tr16.  The entries marked 9 in tr16 are continuation bytes
 * and are marked as length 1 here so that we can recover. */
#  ifndef DOINIT
    EXTCONST U8 PL_utf8skip[256];
#  else
    EXTCONST U8 PL_utf8skip[256] = {
/*     _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
/*0_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*1_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*2_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*3_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*4_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*5_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*6_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*7_*/  1,  1,  1,  2,  2,  2,  2,  2,  2,  1,  1,  1,  1,  1,  1,  1,
/*8_*/  2,  1,  1,  1,  1,  1,  1,  1,  1,  1,  2,  2,  2,  2,  2,  2,
/*9_*/  2,  1,  1,  1,  1,  1,  1,  1,  1,  1,  2,  2,  2,  2,  2,  2,
/*A_*/  2,  1,  1,  1,  1,  1,  1,  1,  1,  1,  2,  2,  2,  2,  2,  2,
/*B_*/  1,  2,  2,  2,  2,  2,  3,  3,  3,  3,  1,  1,  3,  3,  3,  3,
/*C_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  3,  3,  3,  3,  3,  3,
/*D_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  3,  3,  4,  4,  4,  4,
/*E_*/  1,  4,  1,  1,  1,  1,  1,  1,  1,  1,  4,  4,  4,  5,  5,  5,
/*F_*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  5,  6,  6,  7, 14,  1
/*     _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
};
#  endif

/* Index is EBCDIC 037 code point; value is its lowercase equivalent */
#  ifndef DOINIT
    EXTCONST U8 PL_latin1_lc[256];
#  else
    EXTCONST U8 PL_latin1_lc[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,
/*3_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0x3E,0x3F,
/*4_*/0x40,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,
/*5_*/0x50,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5A,0x5B,0x5C,0x5D,0x5E,0x5F,
/*6_*/0x60,0x61,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,
/*7_*/0x70,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x79,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F,
/*8_*/0x70,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8A,0x8B,0x8C,0x8D,0x8E,0x8F,
/*9_*/0x90,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0x9A,0x9B,0x9C,0x9D,0x9C,0x9F,
/*A_*/0xA0,0xA1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xAA,0xAB,0x8C,0x8D,0x8E,0xAF,
/*B_*/0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0xBA,0xBB,0xBC,0xBD,0xBE,0xBF,
/*C_*/0xC0,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0xCA,0xCB,0xCC,0xCD,0xCE,0xCF,
/*D_*/0xD0,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0xDA,0xDB,0xDC,0xDD,0xDE,0xDF,
/*E_*/0xE0,0xE1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xEA,0xCB,0xCC,0xCD,0xCE,0xCF,
/*F_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,0xDB,0xDC,0xDD,0xDE,0xFF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 037 code point; value is its uppercase equivalent.
 * The 'mod' in the name means that codepoints whose uppercase is above 255 or
 * longer than 1 character map to LATIN SMALL LETTER Y WITH DIARESIS */
#  ifndef DOINIT
    EXTCONST U8 PL_mod_latin1_uc[256];
#  else
    EXTCONST U8 PL_mod_latin1_uc[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,
/*3_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0x3E,0x3F,
/*4_*/0x40,0x41,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,
/*5_*/0x50,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0xDF,0x5A,0x5B,0x5C,0x5D,0x5E,0x5F,
/*6_*/0x60,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,
/*7_*/0x80,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F,
/*8_*/0x80,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0x8A,0x8B,0xAC,0xAD,0xAE,0x8F,
/*9_*/0x90,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,0xD7,0xD8,0xD9,0x9A,0x9B,0x9E,0x9D,0x9E,0x9F,
/*A_*/0xDF,0xA1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xAA,0xAB,0xAC,0xAD,0xAE,0xAF,
/*B_*/0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0xBA,0xBB,0xBC,0xBD,0xBE,0xBF,
/*C_*/0xC0,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0xCA,0xEB,0xEC,0xED,0xEE,0xEF,
/*D_*/0xD0,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,0xD7,0xD8,0xD9,0xDA,0xFB,0xFC,0xFD,0xFE,0xDF,
/*E_*/0xE0,0xE1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xEA,0xEB,0xEC,0xED,0xEE,0xEF,
/*F_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,0xFB,0xFC,0xFD,0xFE,0xFF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 037 code point; For A-Z, value is a-z; for a-z, value
 * is A-Z; all other code points map to themselves */
#  ifndef DOINIT
    EXTCONST U8 PL_fold[256];
#  else
    EXTCONST U8 PL_fold[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,
/*3_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0x3E,0x3F,
/*4_*/0x40,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,
/*5_*/0x50,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5A,0x5B,0x5C,0x5D,0x5E,0x5F,
/*6_*/0x60,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,
/*7_*/0x70,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F,
/*8_*/0x80,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0x8A,0x8B,0x8C,0x8D,0x8E,0x8F,
/*9_*/0x90,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,0xD7,0xD8,0xD9,0x9A,0x9B,0x9C,0x9D,0x9E,0x9F,
/*A_*/0xA0,0xA1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xAA,0xAB,0xAC,0xAD,0xAE,0xAF,
/*B_*/0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0xBA,0xBB,0xBC,0xBD,0xBE,0xBF,
/*C_*/0xC0,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0xCA,0xCB,0xCC,0xCD,0xCE,0xCF,
/*D_*/0xD0,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0xDA,0xDB,0xDC,0xDD,0xDE,0xDF,
/*E_*/0xE0,0xE1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xEA,0xEB,0xEC,0xED,0xEE,0xEF,
/*F_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,0xFB,0xFC,0xFD,0xFE,0xFF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif

/* Index is EBCDIC 037 code point; value is its other fold-pair equivalent
 * (A => a; a => A, etc) in the 0-255 range.  If no such equivalent, value is
 * the code point itself */
#  ifndef DOINIT
    EXTCONST U8 PL_fold_latin1[256];
#  else
    EXTCONST U8 PL_fold_latin1[256] = {
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
/*0_*/0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,
/*1_*/0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,
/*2_*/0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,
/*3_*/0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0x3E,0x3F,
/*4_*/0x40,0x41,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,
/*5_*/0x50,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x59,0x5A,0x5B,0x5C,0x5D,0x5E,0x5F,
/*6_*/0x60,0x61,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,
/*7_*/0x80,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x79,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F,
/*8_*/0x70,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0x8A,0x8B,0xAC,0xAD,0xAE,0x8F,
/*9_*/0x90,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,0xD7,0xD8,0xD9,0x9A,0x9B,0x9E,0x9D,0x9C,0x9F,
/*A_*/0xA0,0xA1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xAA,0xAB,0x8C,0x8D,0x8E,0xAF,
/*B_*/0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0xBA,0xBB,0xBC,0xBD,0xBE,0xBF,
/*C_*/0xC0,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0xCA,0xEB,0xEC,0xED,0xEE,0xEF,
/*D_*/0xD0,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0xDA,0xFB,0xFC,0xFD,0xFE,0xDF,
/*E_*/0xE0,0xE1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xEA,0xCB,0xCC,0xCD,0xCE,0xCF,
/*F_*/0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0xF7,0xF8,0xF9,0xFA,0xDB,0xDC,0xDD,0xDE,0xFF
/*      _0   _1   _2   _3   _4   _5   _6   _7   _8   _9   _A   _B   _C   _D   _E  _F*/
};
#  endif


/* The table below is adapted from
 *      https://bjoern.hoehrmann.de/utf-8/decoder/dfa/
 * See copyright notice at the beginning of this file.
 */

#  ifndef DOINIT
    EXTCONST U8 PL_extended_utf8_dfa_tab[416];
#  else
    EXTCONST U8 PL_extended_utf8_dfa_tab[416] = {
/*         _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
/*0_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*1_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*2_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*3_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*4_    */  0,  7,  7,  8,  8,  9,  9,  9,  9, 10, 10,  0,  0,  0,  0,  0,
/*5_    */  0, 10, 10, 10, 10, 10, 10, 11, 11, 11,  0,  0,  0,  0,  0, 11,
/*6_    */  0,  0, 11, 11, 11, 11, 11, 11, 11, 11, 11,  0,  0,  0,  0,  0,
/*7_    */ 11, 11, 11,  1,  1,  1,  1,  1,  2,  0,  0,  0,  0,  0,  0,  0,
/*8_    */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*9_    */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*A_    */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*B_    */  0,  2,  2,  2,  2,  2,  1,  3,  3,  3,  0,  0,  3,  3,  3,  3,
/*C_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  3,  3,  3,  3,
/*D_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3, 12,  4,  4,  4,
/*E_    */  0,  4,  0,  0,  0,  0,  0,  0,  0,  0,  4,  4,  4, 13,  5,  5,
/*F_    */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  5, 14,  6, 15,  1,  0,
/*N0=  0*/  0,  1, 16, 32, 48, 64, 80,  1,  1,  1,  1,  1, 96,112,128,144,
/*N1= 16*/  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  1,  1,  1,  1,
/*N2= 32*/  1,  1,  1,  1,  1,  1,  1, 16, 16, 16, 16, 16,  1,  1,  1,  1,
/*N3= 48*/  1,  1,  1,  1,  1,  1,  1, 32, 32, 32, 32, 32,  1,  1,  1,  1,
/*N4= 64*/  1,  1,  1,  1,  1,  1,  1, 48, 48, 48, 48, 48,  1,  1,  1,  1,
/*N5= 80*/  1,  1,  1,  1,  1,  1,  1, 64, 64, 64, 64, 64,  1,  1,  1,  1,
/*N6= 96*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 32,  1,  1,  1,  1,
/*N7=112*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 48, 48,  1,  1,  1,  1,
/*N8=128*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 64, 64, 64,  1,  1,  1,  1,
/*N9=144*/  1,  1,  1,  1,  1,  1,  1,  1, 80, 80, 80, 80,  1,  1,  1,  1
/*          0   1   2   3   4   5   6   7   8   9  10  11  12  13  14 15*/
};
#  endif


/* The table below is adapted from
 *      https://bjoern.hoehrmann.de/utf-8/decoder/dfa/
 * See copyright notice at the beginning of this file.
 */

#  ifndef DOINIT
    EXTCONST U16 PL_strict_utf8_dfa_tab[624];
#  else
    EXTCONST U16 PL_strict_utf8_dfa_tab[624] = {
/*          _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
/*0_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*1_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*2_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*3_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*4_     */  0, 10, 11, 12, 12, 12, 12, 12, 12, 13, 14,  0,  0,  0,  0,  0,
/*5_     */  0, 13, 14, 13, 14, 15, 16, 17, 18, 17,  0,  0,  0,  0,  0, 18,
/*6_     */  0,  0, 17, 18, 19, 20, 17, 18, 17, 18, 17,  0,  0,  0,  0,  0,
/*7_     */ 18, 21, 22,  1,  1,  1,  1,  1,  2,  0,  0,  0,  0,  0,  0,  0,
/*8_     */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*9_     */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*A_     */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*B_     */  0,  2,  2,  2,  2,  2,  1,  3,  3,  3,  0,  0,  3,  3,  3,  3,
/*C_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  3,  3,  3,  3,
/*D_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  8,  6,  4,  5,
/*E_     */  0,  4,  0,  0,  0,  0,  0,  0,  0,  0,  5,  4,  5,  9,  7,  1,
/*F_     */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,  1,  1,  1,  1,  0,
/*N0 =  0*/  0,  1, 23, 46, 69,138,115,184, 92,161,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*N1 = 23*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*N2 = 46*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23,
/*N3 = 69*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46,
/*N4 = 92*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46,
/*N5 =115*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46, 46, 46, 46,  1,  1, 46,207,
/*N6 =138*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46,276,
/*N7 =161*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 69,322, 69,322, 69,322, 69,322, 69,322,
/*N8 =184*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 69,322,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
/*N9 =207*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 23, 23, 23, 23, 23,230,253, 23, 23, 23, 23, 23,299,
/*N10=230*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,  0,  1,  1,  1,  1,  1,  1,
/*N11=253*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,
/*N12=276*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23,299,
/*N13=299*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,  1,
/*N14=322*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46,345,
/*N15=345*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23,299
/*           0   1   2   3   4   5   6   7   8   9  10  11  12  13  14  15  16  17  18  19  20  21 22*/
};
#  endif


/* The table below is adapted from
 *      https://bjoern.hoehrmann.de/utf-8/decoder/dfa/
 * See copyright notice at the beginning of this file.
 */

#  ifndef DOINIT
    EXTCONST U8 PL_c9_utf8_dfa_tab[368];
#  else
    EXTCONST U8 PL_c9_utf8_dfa_tab[368] = {
/*        _0  _1  _2  _3  _4  _5  _6  _7  _8  _9  _A  _B  _C  _D  _E _F*/
/*0_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*1_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*2_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*3_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
/*4_   */  0,  9,  9, 10, 10, 10, 10, 10, 10, 11, 11,  0,  0,  0,  0,  0,
/*5_   */  0, 11, 11, 11, 11, 11, 11, 12, 12, 12,  0,  0,  0,  0,  0, 12,
/*6_   */  0,  0, 12, 12, 13, 13, 12, 12, 12, 12, 12,  0,  0,  0,  0,  0,
/*7_   */ 12, 12, 12,  1,  1,  1,  1,  1,  2,  0,  0,  0,  0,  0,  0,  0,
/*8_   */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*9_   */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*A_   */  2,  0,  0,  0,  0,  0,  0,  0,  0,  0,  2,  2,  2,  2,  2,  2,
/*B_   */  0,  2,  2,  2,  2,  2,  1,  3,  3,  3,  0,  0,  3,  3,  3,  3,
/*C_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  3,  3,  3,  3,
/*D_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  3,  3,  6,  5,  4,  4,
/*E_   */  0,  4,  0,  0,  0,  0,  0,  0,  0,  0,  4,  4,  4,  8,  7,  1,
/*F_   */  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,  1,  1,  1,  1,  0,
/*N0= 0*/  0,  1, 14, 28, 42, 70, 56, 98, 84,  1,  1,  1,  1,  1,
/*N1=14*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  0,  0,  0,  0,  0,
/*N2=28*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 14, 14, 14, 14, 14,
/*N3=42*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 28, 28, 28, 28, 28,
/*N4=56*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 28, 28,
/*N5=70*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 28, 28, 28, 28,  1,
/*N6=84*/  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1, 42, 42, 42,
/*N7=98*/  1,  1,  1,  1,  1,  1,  1,  1,  1, 42,  1,  1,  1,  1
/*         0   1   2   3   4   5   6   7   8   9  10  11  12 13*/
};
#  endif

#endif	/* EBCDIC 037 */

#endif /* PERL_EBCDIC_TABLES_H_ */

/* ex: set ro ft=c: */
