.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_import_pkcs8" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_import_pkcs8 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_import_pkcs8(gnutls_x509_privkey_t " key ", const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " format ", const char * " password ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
The data to store the parsed key
.IP "const gnutls_datum_t * data" 12
The DER or PEM encoded key.
.IP "gnutls_x509_crt_fmt_t format" 12
One of DER or PEM
.IP "const char * password" 12
the password to decrypt the key (if it is encrypted).
.IP "unsigned int flags" 12
0 if encrypted or GNUTLS_PKCS_PLAIN if not encrypted.
.SH "DESCRIPTION"
This function will convert the given DER or PEM encoded PKCS8 2.0
encrypted key to the native gnutls_x509_privkey_t format. The
output will be stored in  \fIkey\fP .  Both RSA and DSA keys can be
imported, and flags can only be used to indicate an unencrypted
key.

The  \fIpassword\fP can be either ASCII or UTF\-8 in the default PBES2
encryption schemas, or ASCII for the PKCS12 schemas.

If the Certificate is PEM encoded it should have a header of
"ENCRYPTED PRIVATE KEY", or "PRIVATE KEY". You only need to
specify the flags if the key is DER encoded, since in that case
the encryption status cannot be auto\-detected.

If the \fBGNUTLS_PKCS_PLAIN\fP flag is specified and the supplied data
are encrypted then \fBGNUTLS_E_DECRYPTION_FAILED\fP is returned.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
