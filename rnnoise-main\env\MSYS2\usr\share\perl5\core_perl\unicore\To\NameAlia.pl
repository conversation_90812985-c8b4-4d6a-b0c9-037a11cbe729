# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.



# The name this table is to be known by, with the format of the mappings in
# the main body of the table, and what all code points missing from this file
# map to.
$Unicode::UCD::SwashInfo{'ToNameAlias'}{'format'} = 's'; # string
$Unicode::UCD::SwashInfo{'ToNameAlias'}{'missing'} = ''; # code point maps to the empty string

return <<'END';
0		NULL: control
0		NUL: abbreviation
1		START OF HEADING: control
1		SOH: abbreviation
2		START OF TEXT: control
2		STX: abbreviation
3		END OF TEXT: control
3		ETX: abbreviation
4		END OF TRANSMISSION: control
4		EOT: abbreviation
5		ENQUIRY: control
5		ENQ: abbreviation
6		ACKNOWLEDGE: control
6		ACK: abbreviation
7		ALERT: control
7		BEL: abbreviation
8		BACKSPACE: control
8		BS: abbreviation
9		CHARACTER TABULATION: control
9		HORIZONTAL TABULATION: control
9		HT: abbreviation
9		TAB: abbreviation
A		LINE FEED: control
A		NEW LINE: control
A		END OF LINE: control
A		LF: abbreviation
A		NL: abbreviation
A		EOL: abbreviation
B		LINE TABULATION: control
B		VERTICAL TABULATION: control
B		VT: abbreviation
C		FORM FEED: control
C		FF: abbreviation
D		CARRIAGE RETURN: control
D		CR: abbreviation
E		SHIFT OUT: control
E		LOCKING-SHIFT ONE: control
E		SO: abbreviation
F		SHIFT IN: control
F		LOCKING-SHIFT ZERO: control
F		SI: abbreviation
10		DATA LINK ESCAPE: control
10		DLE: abbreviation
11		DEVICE CONTROL ONE: control
11		DC1: abbreviation
12		DEVICE CONTROL TWO: control
12		DC2: abbreviation
13		DEVICE CONTROL THREE: control
13		DC3: abbreviation
14		DEVICE CONTROL FOUR: control
14		DC4: abbreviation
15		NEGATIVE ACKNOWLEDGE: control
15		NAK: abbreviation
16		SYNCHRONOUS IDLE: control
16		SYN: abbreviation
17		END OF TRANSMISSION BLOCK: control
17		ETB: abbreviation
18		CANCEL: control
18		CAN: abbreviation
19		END OF MEDIUM: control
19		EOM: abbreviation
19		EM: abbreviation
1A		SUBSTITUTE: control
1A		SUB: abbreviation
1B		ESCAPE: control
1B		ESC: abbreviation
1C		INFORMATION SEPARATOR FOUR: control
1C		FILE SEPARATOR: control
1C		FS: abbreviation
1D		INFORMATION SEPARATOR THREE: control
1D		GROUP SEPARATOR: control
1D		GS: abbreviation
1E		INFORMATION SEPARATOR TWO: control
1E		RECORD SEPARATOR: control
1E		RS: abbreviation
1F		INFORMATION SEPARATOR ONE: control
1F		UNIT SEPARATOR: control
1F		US: abbreviation
20		SP: abbreviation
7F		DELETE: control
7F		DEL: abbreviation
80		PADDING CHARACTER: figment
80		PAD: abbreviation
81		HIGH OCTET PRESET: figment
81		HOP: abbreviation
82		BREAK PERMITTED HERE: control
82		BPH: abbreviation
83		NO BREAK HERE: control
83		NBH: abbreviation
84		INDEX: control
84		IND: abbreviation
85		NEXT LINE: control
85		NEL: abbreviation
86		START OF SELECTED AREA: control
86		SSA: abbreviation
87		END OF SELECTED AREA: control
87		ESA: abbreviation
88		CHARACTER TABULATION SET: control
88		HORIZONTAL TABULATION SET: control
88		HTS: abbreviation
89		CHARACTER TABULATION WITH JUSTIFICATION: control
89		HORIZONTAL TABULATION WITH JUSTIFICATION: control
89		HTJ: abbreviation
8A		LINE TABULATION SET: control
8A		VERTICAL TABULATION SET: control
8A		VTS: abbreviation
8B		PARTIAL LINE FORWARD: control
8B		PARTIAL LINE DOWN: control
8B		PLD: abbreviation
8C		PARTIAL LINE BACKWARD: control
8C		PARTIAL LINE UP: control
8C		PLU: abbreviation
8D		REVERSE LINE FEED: control
8D		REVERSE INDEX: control
8D		RI: abbreviation
8E		SINGLE SHIFT TWO: control
8E		SINGLE-SHIFT-2: control
8E		SS2: abbreviation
8F		SINGLE SHIFT THREE: control
8F		SINGLE-SHIFT-3: control
8F		SS3: abbreviation
90		DEVICE CONTROL STRING: control
90		DCS: abbreviation
91		PRIVATE USE ONE: control
91		PRIVATE USE-1: control
91		PU1: abbreviation
92		PRIVATE USE TWO: control
92		PRIVATE USE-2: control
92		PU2: abbreviation
93		SET TRANSMIT STATE: control
93		STS: abbreviation
94		CANCEL CHARACTER: control
94		CCH: abbreviation
95		MESSAGE WAITING: control
95		MW: abbreviation
96		START OF GUARDED AREA: control
96		START OF PROTECTED AREA: control
96		SPA: abbreviation
97		END OF GUARDED AREA: control
97		END OF PROTECTED AREA: control
97		EPA: abbreviation
98		START OF STRING: control
98		SOS: abbreviation
99		SINGLE GRAPHIC CHARACTER INTRODUCER: figment
99		SGC: abbreviation
9A		SINGLE CHARACTER INTRODUCER: control
9A		SCI: abbreviation
9B		CONTROL SEQUENCE INTRODUCER: control
9B		CSI: abbreviation
9C		STRING TERMINATOR: control
9C		ST: abbreviation
9D		OPERATING SYSTEM COMMAND: control
9D		OSC: abbreviation
9E		PRIVACY MESSAGE: control
9E		PM: abbreviation
9F		APPLICATION PROGRAM COMMAND: control
9F		APC: abbreviation
A0		NBSP: abbreviation
AD		SHY: abbreviation
1A2		LATIN CAPITAL LETTER GHA: correction
1A3		LATIN SMALL LETTER GHA: correction
34F		CGJ: abbreviation
616		ARABIC SMALL HIGH LIGATURE ALEF WITH YEH BARREE: correction
61C		ALM: abbreviation
709		SYRIAC SUBLINEAR COLON SKEWED LEFT: correction
CDE		KANNADA LETTER LLLA: correction
E9D		LAO LETTER FO FON: correction
E9F		LAO LETTER FO FAY: correction
EA3		LAO LETTER RO: correction
EA5		LAO LETTER LO: correction
FD0		TIBETAN MARK BKA- SHOG GI MGO RGYAN: correction
11EC		HANGUL JONGSEONG YESIEUNG-KIYEOK: correction
11ED		HANGUL JONGSEONG YESIEUNG-SSANGKIYEOK: correction
11EE		HANGUL JONGSEONG SSANGYESIEUNG: correction
11EF		HANGUL JONGSEONG YESIEUNG-KHIEUKH: correction
180B		FVS1: abbreviation
180C		FVS2: abbreviation
180D		FVS3: abbreviation
180E		MVS: abbreviation
180F		FVS4: abbreviation
1BBD		SUNDANESE LETTER ARCHAIC I: correction
200B		ZWSP: abbreviation
200C		ZWNJ: abbreviation
200D		ZWJ: abbreviation
200E		LRM: abbreviation
200F		RLM: abbreviation
202A		LRE: abbreviation
202B		RLE: abbreviation
202C		PDF: abbreviation
202D		LRO: abbreviation
202E		RLO: abbreviation
202F		NNBSP: abbreviation
205F		MMSP: abbreviation
2060		WJ: abbreviation
2066		LRI: abbreviation
2067		RLI: abbreviation
2068		FSI: abbreviation
2069		PDI: abbreviation
2118		WEIERSTRASS ELLIPTIC FUNCTION: correction
2448		MICR ON US SYMBOL: correction
2449		MICR DASH SYMBOL: correction
2B7A		LEFTWARDS TRIANGLE-HEADED ARROW WITH DOUBLE VERTICAL STROKE: correction
2B7C		RIGHTWARDS TRIANGLE-HEADED ARROW WITH DOUBLE VERTICAL STROKE: correction
A015		YI SYLLABLE ITERATION MARK: correction
AA6E		MYANMAR LETTER KHAMTI LLA: correction
FE00		VS1: abbreviation
FE01		VS2: abbreviation
FE02		VS3: abbreviation
FE03		VS4: abbreviation
FE04		VS5: abbreviation
FE05		VS6: abbreviation
FE06		VS7: abbreviation
FE07		VS8: abbreviation
FE08		VS9: abbreviation
FE09		VS10: abbreviation
FE0A		VS11: abbreviation
FE0B		VS12: abbreviation
FE0C		VS13: abbreviation
FE0D		VS14: abbreviation
FE0E		VS15: abbreviation
FE0F		VS16: abbreviation
FE18		PRESENTATION FORM FOR VERTICAL RIGHT WHITE LENTICULAR BRACKET: correction
FEFF		BYTE ORDER MARK: alternate
FEFF		BOM: abbreviation
FEFF		ZWNBSP: abbreviation
122D4		CUNEIFORM SIGN NU11 TENU: correction
122D5		CUNEIFORM SIGN NU11 OVER NU11 BUR OVER BUR: correction
16E56		MEDEFAIDRIN CAPITAL LETTER H: correction
16E57		MEDEFAIDRIN CAPITAL LETTER NG: correction
16E76		MEDEFAIDRIN SMALL LETTER H: correction
16E77		MEDEFAIDRIN SMALL LETTER NG: correction
1B001		HENTAIGANA LETTER E-1: correction
1D0C5		BYZANTINE MUSICAL SYMBOL FTHORA SKLIRON CHROMA VASIS: correction
E0100		VS17: abbreviation
E0101		VS18: abbreviation
E0102		VS19: abbreviation
E0103		VS20: abbreviation
E0104		VS21: abbreviation
E0105		VS22: abbreviation
E0106		VS23: abbreviation
E0107		VS24: abbreviation
E0108		VS25: abbreviation
E0109		VS26: abbreviation
E010A		VS27: abbreviation
E010B		VS28: abbreviation
E010C		VS29: abbreviation
E010D		VS30: abbreviation
E010E		VS31: abbreviation
E010F		VS32: abbreviation
E0110		VS33: abbreviation
E0111		VS34: abbreviation
E0112		VS35: abbreviation
E0113		VS36: abbreviation
E0114		VS37: abbreviation
E0115		VS38: abbreviation
E0116		VS39: abbreviation
E0117		VS40: abbreviation
E0118		VS41: abbreviation
E0119		VS42: abbreviation
E011A		VS43: abbreviation
E011B		VS44: abbreviation
E011C		VS45: abbreviation
E011D		VS46: abbreviation
E011E		VS47: abbreviation
E011F		VS48: abbreviation
E0120		VS49: abbreviation
E0121		VS50: abbreviation
E0122		VS51: abbreviation
E0123		VS52: abbreviation
E0124		VS53: abbreviation
E0125		VS54: abbreviation
E0126		VS55: abbreviation
E0127		VS56: abbreviation
E0128		VS57: abbreviation
E0129		VS58: abbreviation
E012A		VS59: abbreviation
E012B		VS60: abbreviation
E012C		VS61: abbreviation
E012D		VS62: abbreviation
E012E		VS63: abbreviation
E012F		VS64: abbreviation
E0130		VS65: abbreviation
E0131		VS66: abbreviation
E0132		VS67: abbreviation
E0133		VS68: abbreviation
E0134		VS69: abbreviation
E0135		VS70: abbreviation
E0136		VS71: abbreviation
E0137		VS72: abbreviation
E0138		VS73: abbreviation
E0139		VS74: abbreviation
E013A		VS75: abbreviation
E013B		VS76: abbreviation
E013C		VS77: abbreviation
E013D		VS78: abbreviation
E013E		VS79: abbreviation
E013F		VS80: abbreviation
E0140		VS81: abbreviation
E0141		VS82: abbreviation
E0142		VS83: abbreviation
E0143		VS84: abbreviation
E0144		VS85: abbreviation
E0145		VS86: abbreviation
E0146		VS87: abbreviation
E0147		VS88: abbreviation
E0148		VS89: abbreviation
E0149		VS90: abbreviation
E014A		VS91: abbreviation
E014B		VS92: abbreviation
E014C		VS93: abbreviation
E014D		VS94: abbreviation
E014E		VS95: abbreviation
E014F		VS96: abbreviation
E0150		VS97: abbreviation
E0151		VS98: abbreviation
E0152		VS99: abbreviation
E0153		VS100: abbreviation
E0154		VS101: abbreviation
E0155		VS102: abbreviation
E0156		VS103: abbreviation
E0157		VS104: abbreviation
E0158		VS105: abbreviation
E0159		VS106: abbreviation
E015A		VS107: abbreviation
E015B		VS108: abbreviation
E015C		VS109: abbreviation
E015D		VS110: abbreviation
E015E		VS111: abbreviation
E015F		VS112: abbreviation
E0160		VS113: abbreviation
E0161		VS114: abbreviation
E0162		VS115: abbreviation
E0163		VS116: abbreviation
E0164		VS117: abbreviation
E0165		VS118: abbreviation
E0166		VS119: abbreviation
E0167		VS120: abbreviation
E0168		VS121: abbreviation
E0169		VS122: abbreviation
E016A		VS123: abbreviation
E016B		VS124: abbreviation
E016C		VS125: abbreviation
E016D		VS126: abbreviation
E016E		VS127: abbreviation
E016F		VS128: abbreviation
E0170		VS129: abbreviation
E0171		VS130: abbreviation
E0172		VS131: abbreviation
E0173		VS132: abbreviation
E0174		VS133: abbreviation
E0175		VS134: abbreviation
E0176		VS135: abbreviation
E0177		VS136: abbreviation
E0178		VS137: abbreviation
E0179		VS138: abbreviation
E017A		VS139: abbreviation
E017B		VS140: abbreviation
E017C		VS141: abbreviation
E017D		VS142: abbreviation
E017E		VS143: abbreviation
E017F		VS144: abbreviation
E0180		VS145: abbreviation
E0181		VS146: abbreviation
E0182		VS147: abbreviation
E0183		VS148: abbreviation
E0184		VS149: abbreviation
E0185		VS150: abbreviation
E0186		VS151: abbreviation
E0187		VS152: abbreviation
E0188		VS153: abbreviation
E0189		VS154: abbreviation
E018A		VS155: abbreviation
E018B		VS156: abbreviation
E018C		VS157: abbreviation
E018D		VS158: abbreviation
E018E		VS159: abbreviation
E018F		VS160: abbreviation
E0190		VS161: abbreviation
E0191		VS162: abbreviation
E0192		VS163: abbreviation
E0193		VS164: abbreviation
E0194		VS165: abbreviation
E0195		VS166: abbreviation
E0196		VS167: abbreviation
E0197		VS168: abbreviation
E0198		VS169: abbreviation
E0199		VS170: abbreviation
E019A		VS171: abbreviation
E019B		VS172: abbreviation
E019C		VS173: abbreviation
E019D		VS174: abbreviation
E019E		VS175: abbreviation
E019F		VS176: abbreviation
E01A0		VS177: abbreviation
E01A1		VS178: abbreviation
E01A2		VS179: abbreviation
E01A3		VS180: abbreviation
E01A4		VS181: abbreviation
E01A5		VS182: abbreviation
E01A6		VS183: abbreviation
E01A7		VS184: abbreviation
E01A8		VS185: abbreviation
E01A9		VS186: abbreviation
E01AA		VS187: abbreviation
E01AB		VS188: abbreviation
E01AC		VS189: abbreviation
E01AD		VS190: abbreviation
E01AE		VS191: abbreviation
E01AF		VS192: abbreviation
E01B0		VS193: abbreviation
E01B1		VS194: abbreviation
E01B2		VS195: abbreviation
E01B3		VS196: abbreviation
E01B4		VS197: abbreviation
E01B5		VS198: abbreviation
E01B6		VS199: abbreviation
E01B7		VS200: abbreviation
E01B8		VS201: abbreviation
E01B9		VS202: abbreviation
E01BA		VS203: abbreviation
E01BB		VS204: abbreviation
E01BC		VS205: abbreviation
E01BD		VS206: abbreviation
E01BE		VS207: abbreviation
E01BF		VS208: abbreviation
E01C0		VS209: abbreviation
E01C1		VS210: abbreviation
E01C2		VS211: abbreviation
E01C3		VS212: abbreviation
E01C4		VS213: abbreviation
E01C5		VS214: abbreviation
E01C6		VS215: abbreviation
E01C7		VS216: abbreviation
E01C8		VS217: abbreviation
E01C9		VS218: abbreviation
E01CA		VS219: abbreviation
E01CB		VS220: abbreviation
E01CC		VS221: abbreviation
E01CD		VS222: abbreviation
E01CE		VS223: abbreviation
E01CF		VS224: abbreviation
E01D0		VS225: abbreviation
E01D1		VS226: abbreviation
E01D2		VS227: abbreviation
E01D3		VS228: abbreviation
E01D4		VS229: abbreviation
E01D5		VS230: abbreviation
E01D6		VS231: abbreviation
E01D7		VS232: abbreviation
E01D8		VS233: abbreviation
E01D9		VS234: abbreviation
E01DA		VS235: abbreviation
E01DB		VS236: abbreviation
E01DC		VS237: abbreviation
E01DD		VS238: abbreviation
E01DE		VS239: abbreviation
E01DF		VS240: abbreviation
E01E0		VS241: abbreviation
E01E1		VS242: abbreviation
E01E2		VS243: abbreviation
E01E3		VS244: abbreviation
E01E4		VS245: abbreviation
E01E5		VS246: abbreviation
E01E6		VS247: abbreviation
E01E7		VS248: abbreviation
E01E8		VS249: abbreviation
E01E9		VS250: abbreviation
E01EA		VS251: abbreviation
E01EB		VS252: abbreviation
E01EC		VS253: abbreviation
E01ED		VS254: abbreviation
E01EE		VS255: abbreviation
E01EF		VS256: abbreviation
END
