<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>dos2unix 7.5.2 - Convertidor de archivos de texto de formato DOS/Mac a Unix y viceversa</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:<EMAIL>" />
</head>

<body>



<ul id="index">
  <li><a href="#NOMBRE">NOMBRE</a></li>
  <li><a href="#SINOPSIS">SINOPSIS</a></li>
  <li><a href="#DESCRIPCIN">DESCRIPCI&Oacute;N</a></li>
  <li><a href="#PARMETROS">PAR&Aacute;METROS</a></li>
  <li><a href="#MODO-MAC">MODO MAC</a></li>
  <li><a href="#MODOS-DE-CONVERSIN">MODOS DE CONVERSI&Oacute;N</a></li>
  <li><a href="#UNICODE">UNICODE</a>
    <ul>
      <li><a href="#Codificaciones">Codificaciones</a></li>
      <li><a href="#Conversion">Conversion</a></li>
      <li><a href="#Marca-de-orden-de-bytes">Marca de orden de bytes</a></li>
      <li><a href="#Unicode-file-names-on-Windows">Unicode file names on Windows</a></li>
      <li><a href="#Ejemplos-Unicode">Ejemplos Unicode</a></li>
    </ul>
  </li>
  <li><a href="#GB18030">GB18030</a></li>
  <li><a href="#EJEMPLOS">EJEMPLOS</a></li>
  <li><a href="#CONVERSIN-RECURSIVA">CONVERSI&Oacute;N RECURSIVA</a></li>
  <li><a href="#INTERNACIONALIZACIN">INTERNACIONALIZACI&Oacute;N</a></li>
  <li><a href="#VALOR-DE-RETORNO">VALOR DE RETORNO</a></li>
  <li><a href="#ESTNDARES">EST&Aacute;NDARES</a></li>
  <li><a href="#AUTORES">AUTORES</a></li>
  <li><a href="#VASE-TAMBIN">V&Eacute;ASE TAMBI&Eacute;N</a></li>
</ul>

<h1 id="NOMBRE">NOMBRE</h1>

<p>dos2unix - Convertidor de archivos de texto de formato DOS/Mac a Unix y viceversa</p>

<h1 id="SINOPSIS">SINOPSIS</h1>

<pre><code>dos2unix [par&aacute;metros] [ARCHIVO ...] [-n ARCHIVO_DE_ENTRADA ARCHIVO_DE_SALIDA ...]
unix2dos [par&aacute;metros] [ARCHIVO ...] [-n ARCHIVO_DE_ENTRADA ARCHIVO_DE_SALIDA ...]</code></pre>

<h1 id="DESCRIPCIN">DESCRIPCI&Oacute;N</h1>

<p>El paquete Dos2unix incluye las herramientas <code>dos2unix</code> y <code>unix2dos</code> para convertir archivos de texto plano en formato DOS o Mac a formato Unix y viceversa.</p>

<p>En archivos de texto DOS/Windows, un salto de l&iacute;nea, tambi&eacute;n conocido como nueva l&iacute;nea, es una combinaci&oacute;n de dos caracteres: un retorno de carro (CR) seguido por un salto de l&iacute;nea (LF). En archivos de texto Unix, un salto de l&iacute;nea es solamente un car&aacute;cter: el salto de l&iacute;nea (LF). En archivos de texto Mac, antes de Mac OS X, un salto de l&iacute;nea era s&oacute;lo un car&aacute;cter retorno de carro (CR). Actualmente, Mac OS usa el estilo Unix de saltos de l&iacute;nea (LF).</p>

<p>Adem&aacute;s de saltos de l&iacute;nea, Dos2unix puede tambi&eacute;n convertir la codificaci&oacute;n de archivos. Unas cuantas p&aacute;ginas de c&oacute;digos DOS pueden ser convertidas a Unix Latin-1. Y archivos Unicode de Windows (UTF-16) pueden ser convertidos a archivos Unicode de Unix (UTF-8).</p>

<p>Los archivos binarios son ignorados autom&aacute;ticamente, a menos que se fuerce su conversi&oacute;n.</p>

<p>Los archivos no regulares, tales como directorios y FIFO, son ignorados autom&aacute;ticamente.</p>

<p>Los enlaces simb&oacute;licos y sus destinos no son modificados por defecto. Los enlaces simb&oacute;licos pueden opcionalmente ser reemplazados, o la salida puede ser escrita al destino simb&oacute;lico del enlace. En Windows no est&aacute; soportada la escritura a enlaces simb&oacute;licos.</p>

<p>Dos2unix was modelled after dos2unix under SunOS/Solaris. There is one important difference with the original SunOS/Solaris version. This version does by default in-place conversion (old file mode), while the original SunOS/Solaris version only supports paired conversion (new file mode). See also options <code>-o</code> and <code>-n</code>. Another difference is that the SunOS/Solaris version uses by default <i>iso</i> mode conversion while this version uses by default <i>ascii</i> mode conversion.</p>

<h1 id="PARMETROS">PAR&Aacute;METROS</h1>

<dl>

<dt id="pod"><b>--</b></dt>
<dd>

<p>Todos los par&aacute;metros siguientes son tratados como nombres de archivo. Use este par&aacute;metro si desea convertir archivos cuyos nombres inician con un gui&oacute;n. Por ejemplo para convertir un archivoo llamado &quot;-foo&quot;, use este comando:</p>

<pre><code>dos2unix -- -foo</code></pre>

<p>O en modo de archivo nuevo:</p>

<pre><code>dos2unix -n -- -foo out.txt</code></pre>

</dd>
<dt id="allow-chown"><b>--allow-chown</b></dt>
<dd>

<p>Allow file ownership change in old file mode.</p>

<p>When this option is used, the conversion will not be aborted when the user and/or group ownership of the original file can&#39;t be preserved in old file mode. Conversion will continue and the converted file will get the same new ownership as if it was converted in new file mode. See also options <code>-o</code> and <code>-n</code>. This option is only available if dos2unix has support for preserving the user and group ownership of files.</p>

</dd>
<dt id="ascii"><b>-ascii</b></dt>
<dd>

<p>Default conversion mode. See also section CONVERSION MODES.</p>

</dd>
<dt id="iso"><b>-iso</b></dt>
<dd>

<p>Conversi&oacute;n entre el conjunto de caracteres DOS e ISO-8859-1. V&eacute;ase tambi&eacute;n la secci&oacute;n MODOS DE CONVERSI&Oacute;N.</p>

</dd>
<dt id="pod-1252"><b>-1252</b></dt>
<dd>

<p>Usa la p&aacute;gina de c&oacute;digos Windows 1252 (Europa Occidental).</p>

</dd>
<dt id="pod-437"><b>-437</b></dt>
<dd>

<p>Usa la p&aacute;gina de c&oacute;digos DOS 437 (EE. UU.). Est&aacute; es la p&aacute;gina de c&oacute;digos usada por defecto para conversi&oacute;n ISO.</p>

</dd>
<dt id="pod-850"><b>-850</b></dt>
<dd>

<p>Usa la p&aacute;gina de c&oacute;digos DOS 850 (Europa Occidental).</p>

</dd>
<dt id="pod-860"><b>-860</b></dt>
<dd>

<p>Usa la p&aacute;gina de c&oacute;digos DOS 860 (Portugu&eacute;s).</p>

</dd>
<dt id="pod-863"><b>-863</b></dt>
<dd>

<p>Usa la p&aacute;gina de c&oacute;digos DOS 863 (Francocanadiense).</p>

</dd>
<dt id="pod-865"><b>-865</b></dt>
<dd>

<p>Usa la p&aacute;gina de c&oacute;digos DOS 865 (N&oacute;rdico).</p>

</dd>
<dt id="pod-7"><b>-7</b></dt>
<dd>

<p>Convierte caracteres de 8 bits al espacio de 7 bits.</p>

</dd>
<dt id="b---keep-bom"><b>-b, --keep-bom</b></dt>
<dd>

<p>Mantiene la Marca de Orden de Byte (BOM). Cuando el archivo de entrada tiene BOM, escribe BOM en el archivo de salida. Este es el comportamiento por defecto en la conversi&oacute;n a saltos de l&iacute;nea DOS. Vea tambi&eacute;n la opci&oacute;n <code>-r</code>.</p>

</dd>
<dt id="c---convmode-CONVMODE"><b>-c, --convmode CONVMODE</b></dt>
<dd>

<p>Establece el modo de conversi&oacute;n, Donde CONVMODE puede ser: <i>ascii</i>, <i>7bit</i>, <i>iso</i>, <i>mac</i> siendo ascii el valor por defecto.</p>

</dd>
<dt id="D---display-enc-ENCODING"><b>-D, --display-enc ENCODING</b></dt>
<dd>

<p>Set encoding of displayed text. Where ENCODING is one of: <i>ansi</i>, <i>unicode</i>, <i>unicodebom</i>, <i>utf8</i>, <i>utf8bom</i> with ansi being the default.</p>

<p>This option is only available in dos2unix for Windows with Unicode file name support. This option has no effect on the actual file names read and written, only on how they are displayed.</p>

<p>There are several methods for displaying text in a Windows console based on the encoding of the text. They all have their own advantages and disadvantages.</p>

<dl>

<dt id="ansi"><b>ansi</b></dt>
<dd>

<p>Dos2unix&#39;s default method is to use ANSI encoded text. The advantage is that it is backwards compatible. It works with raster and TrueType fonts. In some regions you may need to change the active DOS OEM code page to the Windows system ANSI code page using the <code>chcp</code> command, because dos2unix uses the Windows system code page.</p>

<p>The disadvantage of ansi is that international file names with characters not inside the system default code page are not displayed properly. You will see a question mark, or a wrong symbol instead. When you don&#39;t work with foreign file names this method is OK.</p>

</dd>
<dt id="unicode-unicodebom"><b>unicode, unicodebom</b></dt>
<dd>

<p>The advantage of unicode (the Windows name for UTF-16) encoding is that text is usually properly displayed. There is no need to change the active code page. You may need to set the console&#39;s font to a TrueType font to have international characters displayed properly. When a character is not included in the TrueType font you usually see a small square, sometimes with a question mark in it.</p>

<p>When you use the ConEmu console all text is displayed properly, because ConEmu automatically selects a good font.</p>

<p>The disadvantage of unicode is that it is not compatible with ASCII. The output is not easy to handle when you redirect it to another program.</p>

<p>When method <code>unicodebom</code> is used the Unicode text will be preceded with a BOM (Byte Order Mark). A BOM is required for correct redirection or piping in PowerShell.</p>

</dd>
<dt id="utf8-utf8bom"><b>utf8, utf8bom</b></dt>
<dd>

<p>The advantage of utf8 is that it is compatible with ASCII. You need to set the console&#39;s font to a TrueType font. With a TrueType font the text is displayed similar as with the <code>unicode</code> encoding.</p>

<p>The disadvantage is that when you use the default raster font all non-ASCII characters are displayed wrong. Not only unicode file names, but also translated messages become unreadable. On Windows configured for an East-Asian region you may see a lot of flickering of the console when the messages are displayed.</p>

<p>In a ConEmu console the utf8 encoding method works well.</p>

<p>When method <code>utf8bom</code> is used the UTF-8 text will be preceded with a BOM (Byte Order Mark). A BOM is required for correct redirection or piping in PowerShell.</p>

</dd>
</dl>

<p>The default encoding can be changed with environment variable DOS2UNIX_DISPLAY_ENC by setting it to <code>unicode</code>, <code>unicodebom</code>, <code>utf8</code>, or <code>utf8bom</code>.</p>

</dd>
<dt id="e---add-eol"><b>-e, --add-eol</b></dt>
<dd>

<p>Add a line break to the last line if there isn&#39;t one. This works for every conversion.</p>

<p>A file converted from DOS to Unix format may lack a line break on the last line. There are text editors that write text files without a line break on the last line. Some Unix programs have problems processing these files, because the POSIX standard defines that every line in a text file must end with a terminating newline character. For instance concatenating files may not give the expected result.</p>

</dd>
<dt id="f---force"><b>-f, --force</b></dt>
<dd>

<p>Fuerza la conversi&oacute;n de archivos binarios.</p>

</dd>
<dt id="gb---gb18030"><b>-gb, --gb18030</b></dt>
<dd>

<p>En Windows los archivos UTF-16 se convierten por defecto a UTF-8, sin tener en cuenta la configuraci&oacute;n local. Use esta opci&oacute;n para convertir archivos UTF-16 a GB18030. Esta opci&oacute;n s&oacute;lo est&aacute; disponible en Windows.l V&eacute;ase tambi&eacute;n la secci&oacute;n GB18030.</p>

</dd>
<dt id="h---help"><b>-h, --help</b></dt>
<dd>

<p>Despiega la ayuda y termina el programa.</p>

</dd>
<dt id="i-MARCAS---info-MARCAS-ARCHIVO"><b>-i[MARCAS], --info[= MARCAS] ARCHIVO ...</b></dt>
<dd>

<p>Muestra la informaci&oacute;n del archivo. No se realiza ninguna conversi&oacute;n.</p>

<p>Se muestra la siguiente informaci&oacute;n, en este orden: n&uacute;mero de saltos de l&iacute;nea DOS, n&uacute;mero de saltos de l&iacute;nea Unix, n&uacute;mero de saltos de l&iacute;nea Mac, Marca de Orden de Byte, de texto o binario, nombre del archivo.</p>

<p>Ejemplo de salida:</p>

<pre><code> 6       0       0  no_bom    text    dos.txt
 0       6       0  no_bom    text    unix.txt
 0       0       6  no_bom    text    mac.txt
 6       6       6  no_bom    text    mixed.txt
50       0       0  UTF-16LE  text    utf16le.txt
 0      50       0  no_bom    text    utf8unix.txt
50       0       0  UTF-8     text    utf8dos.txt
 2     418     219  no_bom    binary  dos2unix.exe</code></pre>

<p>Note that sometimes a binary file can be mistaken for a text file. See also option <code>-s</code>.</p>

<p>If in addition option <code>-e</code> or <code>--add-eol</code> is used also the type of the line break of the last line is printed, or <code>noeol</code> if there is none.</p>

<p>Ejemplo de salida:</p>

<pre><code>6       0       0  no_bom    text   dos     dos.txt
0       6       0  no_bom    text   unix    unix.txt
0       0       6  no_bom    text   mac     mac.txt
1       0       0  no_bom    text   noeol   noeol_dos.txt</code></pre>

<p>Se pueden utilizar marcas extras opcionales para modificar la salida. Se pueden a&ntilde;adir una o m&aacute;s marcas.</p>

<dl>

<dt id="pod0"><b>0</b></dt>
<dd>

<p>Print the file information lines followed by a null character instead of a newline character. This enables correct interpretation of file names with spaces or quotes when flag c is used. Use this flag in combination with xargs(1) option <code>-0</code> or <code>--null</code>.</p>

</dd>
<dt id="d"><b>d</b></dt>
<dd>

<p>Muestra el n&uacute;mero de saltos de l&iacute;nea DOS.</p>

</dd>
<dt id="u"><b>u</b></dt>
<dd>

<p>Muestra el n&uacute;mero de saltos de l&iacute;nea Unix.</p>

</dd>
<dt id="m"><b>m</b></dt>
<dd>

<p>Muestra el n&uacute;mero de saltos de l&iacute;nea Mac.</p>

</dd>
<dt id="b"><b>b</b></dt>
<dd>

<p>Muestra la Marca de Orden de Byte.</p>

</dd>
<dt id="t"><b>t</b></dt>
<dd>

<p>Muestra si el archivo es de texto o binario.</p>

</dd>
<dt id="e"><b>e</b></dt>
<dd>

<p>Print the type of the line break of the last line, or <code>noeol</code> if there is none.</p>

</dd>
<dt id="c"><b>c</b></dt>
<dd>

<p>Muestra s&oacute;lo los archivos que pueden ser convertidos.</p>

<p>Con la marca <code>c</code> dos2unix s&oacute;lo mostrar&aacute; los archivos que contengan saltos de l&iacute;nea DOS, unix2dos s&oacute;lo mostrar&aacute; los nombres de archivo que tengan saltos de l&iacute;nea Unix.</p>

<p>If in addition option <code>-e</code> or <code>--add-eol</code> is used also the files that lack a line break on the last line will be printed.</p>

</dd>
<dt id="h"><b>h</b></dt>
<dd>

<p>Print a header.</p>

</dd>
<dt id="p"><b>p</b></dt>
<dd>

<p>Show file names without path.</p>

</dd>
</dl>

<p>Ejemplos:</p>

<p>Muestra informaci&oacute;n para todos los archivos *.txt:</p>

<pre><code>dos2unix -i *.txt</code></pre>

<p>Muestra s&oacute;lo el n&uacute;mero de saltos de l&iacute;nea de DOS y de Unix:</p>

<pre><code>dos2unix -idu *.txt</code></pre>

<p>Muestra s&oacute;lo la Marca de Orden de Byte.</p>

<pre><code>dos2unix --info=b *.txt</code></pre>

<p>Muestra los archivos que tienen saltos de l&iacute;nea DOS:</p>

<pre><code>dos2unix -ic *.txt</code></pre>

<p>Muestra los archivos que tienen saltos de l&iacute;nea Unix:</p>

<pre><code>unix2dos -ic *.txt</code></pre>

<p>List the files that have DOS line breaks or lack a line break on the last line:</p>

<pre><code>dos2unix -e -ic *.txt</code></pre>

<p>Convert only files that have DOS line breaks and leave the other files untouched:</p>

<pre><code>dos2unix -ic0 *.txt | xargs -0 dos2unix</code></pre>

<p>Find text files that have DOS line breaks:</p>

<pre><code>find -name &#39;*.txt&#39; -print0 | xargs -0 dos2unix -ic</code></pre>

</dd>
<dt id="k---keepdate"><b>-k, --keepdate</b></dt>
<dd>

<p>Mantiene la fecha del archivo de salida igual a la del archivo de entrada.</p>

</dd>
<dt id="L---license"><b>-L, --license</b></dt>
<dd>

<p>Muestra la licencia del programa.</p>

</dd>
<dt id="l---newline"><b>-l, --newline</b></dt>
<dd>

<p>A&ntilde;ade salto de l&iacute;nea adicional.</p>

<p><b>dos2unix</b>: S&oacute;lo los saltos de l&iacute;nea DOS son cambiados por dos saltos de l&iacute;nea Unix. En modo Mac s&oacute;lo los saltos de l&iacute;nea Mac son cambiados por dos saltos de l&iacute;nea Unix.</p>

<p><b>unix2dos</b>: S&oacute;lo los saltos de l&iacute;nea Unix son cambiados por dos saltos de l&iacute;nea DOS. En modo Mac los saltos de l&iacute;nea Unix son cambiados por dos saltos de l&iacute;nea Mac.</p>

</dd>
<dt id="m---add-bom"><b>-m, --add-bom</b></dt>
<dd>

<p>Escribe una Marca de Orden de Bytes (BOM) en el archivo de salida. Por defecto se escribe una BOM UTF-8.</p>

<p>Cuando el archivo de entrada es UTF-16 y se usa la opci&oacute;n <code>-u</code>, se escribir&aacute; un BOM UTF-16.</p>

<p>No utilice esta opci&oacute;n cuando la codificaci&oacute;n de salida sea distinta de UTF-8, UTF-16 o GB18030. V&eacute;ase tambi&eacute;n la secci&oacute;n UNICODE.</p>

</dd>
<dt id="n---newfile-ARCHIVO_DE_ENTRADA-ARCHIVO_DE_SALIDA"><b>-n, --newfile ARCHIVO_DE_ENTRADA ARCHIVO_DE_SALIDA ...</b></dt>
<dd>

<p>Modo de archivo nuevo. Convierte el archivo ARCHIVO_DE_ENTRADA y escribe la salida al archivo ARCHIVO_DE_SALIDA. Los nombres de archivo deben ser dados en pares y los comodines <i>no</i> deben ser usados o <i>perder&aacute;</i> sus archivos.</p>

<p>La persona que inicia la conversi&oacute;n en el modo de archivo nuevo (emparejado) ser&aacute; el propietario del archivo convertido. Los permisos de lectura/escritura del archivo nuevo ser&aacute;n los permisos del archivo original menos la umask(1) de la persona que ejecute la conversi&oacute;n.</p>

</dd>
<dt id="no-allow-chown"><b>--no-allow-chown</b></dt>
<dd>

<p>Don&#39;t allow file ownership change in old file mode (default).</p>

<p>Abort conversion when the user and/or group ownership of the original file can&#39;t be preserved in old file mode. See also options <code>-o</code> and <code>-n</code>. This option is only available if dos2unix has support for preserving the user and group ownership of files.</p>

</dd>
<dt id="no-add-eol"><b>--no-add-eol</b></dt>
<dd>

<p>Do not add a line break to the last line if there isn&#39;t one.</p>

</dd>
<dt id="O---to-stdout"><b>-O, --to-stdout</b></dt>
<dd>

<p>Write to standard output, like a Unix filter. Use option <code>-o</code> to go back to old file (in-place) mode.</p>

<p>Combined with option <code>-e</code> files can be properly concatenated. No merged last and first lines, and no Unicode byte order marks in the middle of the concatenated file. Example:</p>

<pre><code>dos2unix -e -O file1.txt file2.txt &gt; output.txt</code></pre>

</dd>
<dt id="o---oldfile-FILE"><b>-o, --oldfile FILE ...</b></dt>
<dd>

<p>Modo de archivo antiguo. Convierte el archivo ARCHIVO y lo sobrescribe con la salida. El programa por defecto se ejecuta en este modo. Se pueden emplear comodines.</p>

<p>En modo de archivo antiguo (in situ), el archivo convertido tiene el mismo propietario, grupo y permisos de lectura/escritura que el archivo original. Lo mismo aplica cuando el archivo es convertido por otro usuario que tiene permiso de lectura en el archivo (p.e. usuario root). La conversi&oacute;n ser&aacute; abortada cuando no sea posible preservar los valores originales. Cambiar el propietario implicar&iacute;a que el propietario original ya no podr&aacute; leer el archivo. Cambiar el grupo podr&iacute;a ser un riesgo de seguridad, ya que el archivo podr&iacute;a ser accesible a personas inadecuadas. La preservaci&oacute;n del propietario, grupo, y permisos de lectura/escritura s&oacute;lo est&aacute; soportada bajo Unix.</p>

<p>To check if dos2unix has support for preserving the user and group ownership of files type <code>dos2unix -V</code>.</p>

<p>Conversion is always done via a temporary file. When an error occurs halfway the conversion, the temporary file is deleted and the original file stays intact. When the conversion is successful, the original file is replaced with the temporary file. You may have write permission on the original file, but no permission to put the same user and/or group ownership properties on the temporary file as the original file has. This means you are not able to preserve the user and/or group ownership of the original file. In this case you can use option <code>--allow-chown</code> to continue with the conversion:</p>

<pre><code>dos2unix --allow-chown foo.txt</code></pre>

<p>Another option is to use new file mode:</p>

<pre><code>dos2unix -n foo.txt foo.txt</code></pre>

<p>The advantage of the <code>--allow-chown</code> option is that you can use wildcards, and the ownership properties will be preserved when possible.</p>

</dd>
<dt id="q---quiet"><b>-q, --quiet</b></dt>
<dd>

<p>Modo silencioso. Suprime todas las advertencias y mensajes. El valor retornado es cero. Excepto cuando se emplean par&aacute;metros incorrectos.</p>

</dd>
<dt id="r---remove-bom"><b>-r, --remove-bom</b></dt>
<dd>

<p>Elimina la Marca de Orden de Byte (BOM). No escribe el BOM en el archivo de salida. Este es el comportamiento por defecto al convertir a saltos de l&iacute;nea Unix. Vea tambi&eacute;n la opci&oacute;n <code>-b</code>.</p>

</dd>
<dt id="s---safe"><b>-s, --safe</b></dt>
<dd>

<p>Ignora los archivos binarios (por defecto).</p>

<p>The skipping of binary files is done to avoid accidental mistakes. Be aware that the detection of binary files is not 100% foolproof. Input files are scanned for binary symbols which are typically not found in text files. It is possible that a binary file contains only normal text characters. Such a binary file will mistakenly be seen as a text file.</p>

</dd>
<dt id="u---keep-utf16"><b>-u, --keep-utf16</b></dt>
<dd>

<p>Mantiene la codificaci&oacute;n original UTF-16 en el archivo de entrada. El archivo de salida se escribir&aacute; con la misma codificaci&oacute;n UTF-16, little o big endian, como el archivo de entrada. Esto impide la transformaci&oacute;n a UTF-8. En consecuencia se escribir&aacute; un BOM UTF-16. Esta opci&oacute;n se puede desactivar con la opci&oacute;n <code>-ascii</code>.</p>

</dd>
<dt id="ul---assume-utf16le"><b>-ul, --assume-utf16le</b></dt>
<dd>

<p>Se asume que el formato de archivo de entrada es UTF-16LE.</p>

<p>Cuando existe una Marca de Orden de Bytes (BOM) en el archivo de entrada, la BOM tiene prioridad sobre esta opci&oacute;n.</p>

<p>Cuando se hace una suposici&oacute;n incorrecta (el archivo de entrada no estaba en formato UTF-16LE) y la conversi&oacute;n tiene &eacute;xito, obtendr&aacute; un archivo UTF-8 de salida con el texto err&oacute;neo. La conversi&oacute;n err&oacute;nea puede deshacerse con iconv(1) convirtiendo el archivo UTF-8 de salida de vuelta a UTF-16LE. Esto restaurar&aacute; el archivo original.</p>

<p>El supuesto de UTF-16LE funciona como un <i>modo de conversi&oacute;n</i>. Al cambiar al modo por defecto <i>ascii</i> el supuesto UTF-16LE es deshabilitado.</p>

</dd>
<dt id="ub---assume-utf16be"><b>-ub, --assume-utf16be</b></dt>
<dd>

<p>Se asume que el formato del archivo de entrada es UTF-16BE.</p>

<p>Esta opci&oacute;n funciona igual que la opci&oacute;n <code>-ul</code>.</p>

</dd>
<dt id="v---verbose"><b>-v, --verbose</b></dt>
<dd>

<p>Mostrar mensajes detallados. Se muestra informaci&oacute;n extra acerca de Marcas de Orden de Bytes (BOM) y el n&uacute;mero de saltos de l&iacute;nea convertidos.</p>

</dd>
<dt id="F---follow-symlink"><b>-F, --follow-symlink</b></dt>
<dd>

<p>Sigue los enlaces simb&oacute;licos y convierte los destinos.</p>

</dd>
<dt id="R---replace-symlink"><b>-R, --replace-symlink</b></dt>
<dd>

<p>Reemplaza los enlaces simb&oacute;licos con los archivos convertidos (los archivos destino originales no se alteran).</p>

</dd>
<dt id="S---skip-symlink"><b>-S, --skip-symlink</b></dt>
<dd>

<p>No altera los enlaces simb&oacute;licos ni sus destinos (por defecto).</p>

</dd>
<dt id="V---version"><b>-V, --version</b></dt>
<dd>

<p>Despiega la informaci&oacute;n de la versi&oacute;n y termina el programa.</p>

</dd>
</dl>

<h1 id="MODO-MAC">MODO MAC</h1>

<p>By default line breaks are converted from DOS to Unix and vice versa. Mac line breaks are not converted.</p>

<p>En modo Mac los saltos de l&iacute;nea son convertidos de Mac a Unix y viceversa. Los saltos de l&iacute;nea DOS no son modificados.</p>

<p>Para ejecutar en modo Mac use el modificador <code>-c mac</code> o use los comandos <code>mac2unix</code> o <code>unix2mac</code>.</p>

<h1 id="MODOS-DE-CONVERSIN">MODOS DE CONVERSI&Oacute;N</h1>

<dl>

<dt id="ascii1"><b>ascii</b></dt>
<dd>

<p>This is the default conversion mode. This mode is for converting ASCII and ASCII-compatible encoded files, like UTF-8. Enabling <b>ascii</b> mode disables <b>7bit</b> and <b>iso</b> mode.</p>

<p>If dos2unix has UTF-16 support, UTF-16 encoded files are converted to the current locale character encoding on POSIX systems and to UTF-8 on Windows. Enabling <b>ascii</b> mode disables the option to keep UTF-16 encoding (<code>-u</code>) and the options to assume UTF-16 input (<code>-ul</code> and <code>-ub</code>). To see if dos2unix has UTF-16 support type <code>dos2unix -V</code>. See also section UNICODE.</p>

</dd>
<dt id="bit"><b>7bit</b></dt>
<dd>

<p>En este modo todos los caracteres no ASCII de 8 bits (con valores de 128 a 255) son convertidos al espacio de 7 bits.</p>

</dd>
<dt id="iso1"><b>iso</b></dt>
<dd>

<p>Los caracteres son convertidos entre un conjunto de caracteres DOS (p&aacute;gina de c&oacute;digos) y el conjunto de caracteres ISO-8859-1 (Lat&iacute;n-1) de Unix. Los caracteres DOS sin equivalente ISO-8859-1, para los cuales la conversi&oacute;n es imposible, son convertidos en un punto. Lo mismo se aplica para caracteres ISO-8859-1 sin contraparte DOS.</p>

<p>Cuando s&oacute;lo se emplea el par&aacute;metro <code>-iso</code>, dos2unix intentar&aacute; determinar la p&aacute;gina de c&oacute;digos activa. Cuando esto no sea posible, dos2unix utilizar&aacute; la p&aacute;gina de c&oacute;digos 437 por defecto, la cual es empleada principalmente en EE. UU. Para forzar una p&aacute;gina de c&oacute;digos espec&iacute;fica emplee los par&aacute;metros <code>-437</code> (EE. UU.), <code>-850</code> (Europa Occidental), <code>-860</code> (Portugu&eacute;s), <code>-863</code> (Francocanadiense), o <code>-865</code> (N&oacute;rdico). La p&aacute;gina de c&oacute;digos Windows 1252 (Europa Occidental) tambi&eacute;n est&aacute; soportada con el par&aacute;metro <code>-1252</code>. Para acceder a otras p&aacute;ginas de c&oacute;digos use dos2unix en combinaci&oacute;n con iconv(1). Iconv puede convertir entre una larga lista de codificaciones de caracteres.</p>

<p>No use la conversi&oacute;n ISO en archivos de texto Unicode. Esto corromper&iacute;a los archivos codificados como UTF-8.</p>

<p>Algunos ejemplos:</p>

<p>Convierte de la p&aacute;gina de c&oacute;digos por defecto de DOS a Lat&iacute;n-1 de Unix:</p>

<pre><code>dos2unix -iso -n in.txt out.txt</code></pre>

<p>Convierte de DOS CP850 a Unix Lat&iacute;n-1:</p>

<pre><code>dos2unix -850 -n in.txt out.txt</code></pre>

<p>Convierte de Windows CP1252 a Unix Latin-1:</p>

<pre><code>dos2unix -1252 -n in.txt out.txt</code></pre>

<p>Convierte de Windows CP1252 a Unix UTF-8 (Unicode).</p>

<pre><code>iconv -f CP1252 -t UTF-8 in.txt | dos2unix &gt; out.txt</code></pre>

<p>Convierte de Unix Latin-1 a la p&aacute;gina de c&oacute;digos por defecto de DOS:</p>

<pre><code>unix2dos -iso -n in.txt out.txt</code></pre>

<p>Convierte de Unix Latin-1 a DOS CP850:</p>

<pre><code>unix2dos -850 -n in.txt out.txt</code></pre>

<p>Convierte de Unix Latin-1 a Windows CP1252.</p>

<pre><code>unix2dos -1252 -n in.txt out.txt</code></pre>

<p>Convierte de Unix UTF-8 (Unicode) a Windows CP1252:</p>

<pre><code>unix2dos &lt; in.txt | iconv -f UTF-8 -t CP1252 &gt; out.txt</code></pre>

<p>V&eacute;ase tambi&eacute;n <a href="http://czyborra.com/charsets/codepages.html">http://czyborra.com/charsets/codepages.html</a> y <a href="http://czyborra.com/charsets/iso8859.html">http://czyborra.com/charsets/iso8859.html</a>.</p>

</dd>
</dl>

<h1 id="UNICODE">UNICODE</h1>

<h2 id="Codificaciones">Codificaciones</h2>

<p>Existen diferentes codificaciones Unicode. En Unix y Linux los archivos Unicode son codificados com&uacute;nmente en UTF-8. En Windows los archivos de texto Unicode pueden estar codificados en UTF-8, UTF-16, o UTF-16 big endian, pero en general son codificados en formato UTF-16.</p>

<h2 id="Conversion">Conversion</h2>

<p>Unicode text files can have DOS, Unix or Mac line breaks, like ASCII text files.</p>

<p>Todas las versiones de dos2unix y unix2dos pueden convertir archivos codificados como UTF-8, debido a que UTF-8 fue dise&ntilde;ado para retro-compatibilidad con ASCII.</p>

<p>Dos2unix y unix2dos con soporte Unicode UTF-16, pueden leer archivos de texto codificados como UTF-16 little y big endian. Para ver si dos2unix fue compilado con soporte UTF-16 escriba <code>dos2unix -V</code>.</p>

<p>En Unix/Linux los archivos codificados con UTF-16 se convierten a la codificaci&oacute;n de caracteres local. Use el comando locale(1) para averiguar la codificaci&oacute;n de caracteres local. Cuando no se puede hacer la conversi&oacute;n se obtendr&aacute; un error de conversi&oacute;n y se omitir&aacute; el archivo.</p>

<p>En Windows los archivos UTF-16 se convierten por defecto a UTF-8. Los archivos de texto forrajeados con UTF-8 est&aacute;n soportados tanto en Windows como en Unix/Linux.</p>

<p>Las codificaciones UTF-16 y UTF-8 son totalmente compatibles, no se perder&aacute; ning&uacute;n texto en la conversi&oacute;n. Cuando ocurre un error de conversi&oacute;n de UTF-16 a UTF-8, por ejemplo cuando el archivo de entrada UTF-16 contiene un error, se omitir&aacute; el archivo.</p>

<p>Cuando se usa la opci&oacute;n <code>-u</code>, el archivo de salida se escribir&aacute; en la misma codificaci&oacute;n UTF-16 que el archivo de entrada. La opci&oacute;n <code>-u</code> previene la conversi&oacute;n a UTF-8.</p>

<p>Dos2unix y unix2dos no tienen la opci&oacute;n de convertir archivos UTF-8 a UTF-16.</p>

<p>La conversi&oacute;n en modos ISO y 7-bit no funciona en archivos UTF-16.</p>

<h2 id="Marca-de-orden-de-bytes">Marca de orden de bytes</h2>

<p>On Windows Unicode text files typically have a Byte Order Mark (BOM), because many Windows programs (including Notepad) add BOMs by default. See also <a href="https://en.wikipedia.org/wiki/Byte_order_mark">https://en.wikipedia.org/wiki/Byte_order_mark</a>.</p>

<p>En Unix los archivos Unicode no suelen tener BOM. Se supone que los archivos de texto son codificados en la codificaci&oacute;n local de caracteres.</p>

<p>Dos2unix s&oacute;lo puede detectar si un archivo est&aacute; en formato UTF-16 si el archivo tiene una BOM. Cuando un archivo UTF-16 no tiene una BOM, dos2unix tratar&aacute; el archivo como un archivo binario.</p>

<p>Use la opci&oacute;n <code>-ul</code> o <code>-ub</code> para convertir un archivo UTF-16 sin BOM.</p>

<p>Dos2Unix, por defecto, no escribe BOM en el archivo de salida. Con la opci&oacute;n <code>-b</code> Dos2unix escribe el BOM cuando el archivo de entrada tiene BOM.</p>

<p>Unix2dos escribe BOM en el archivo de salida cuando el archivo de entrada tiene BOM. Use la opci&oacute;n <code>-r</code> para eliminar la BOM.</p>

<p>Dos2unix y unix2dos escriben siempre BOM cuando se usa la opci&oacute;n <code>-m</code>.</p>

<h2 id="Unicode-file-names-on-Windows">Unicode file names on Windows</h2>

<p>Dos2unix has optional support for reading and writing Unicode file names in the Windows Command Prompt. That means that dos2unix can open files that have characters in the name that are not part of the default system ANSI code page. To see if dos2unix for Windows was built with Unicode file name support type <code>dos2unix -V</code>.</p>

<p>There are some issues with displaying Unicode file names in a Windows console. See option <code>-D</code>, <code>--display-enc</code>. The file names may be displayed wrongly in the console, but the files will be written with the correct name.</p>

<h2 id="Ejemplos-Unicode">Ejemplos Unicode</h2>

<p>Convertir de Windows UTF-16 (con una BOM) a Unix UTF-8:</p>

<pre><code>dos2unix -n in.txt out.txt</code></pre>

<p>Convertir de Windows UTF-16LE (sin una BOM) a Unix UTF-8:</p>

<pre><code>dos2unix -ul -n in.txt out.txt</code></pre>

<p>Convertir de Unix UTF-8 a Windows UTF-8 sin una BOM:</p>

<pre><code>unix2dos -m -n in.txt out.txt</code></pre>

<p>Convertir de Unix UTF-8 a Windows UTF-16:</p>

<pre><code>unix2dos &lt; in.txt | iconv -f UTF-8 -t UTF-16 &gt; out.txt</code></pre>

<h1 id="GB18030">GB18030</h1>

<p>GB18030 is a Chinese government standard. A mandatory subset of the GB18030 standard is officially required for all software products sold in China. See also <a href="https://en.wikipedia.org/wiki/GB_18030">https://en.wikipedia.org/wiki/GB_18030</a>.</p>

<p>GB18030 es totalmente compatible con Unicode y puede considerarse como formato de transformaci&oacute;n Unicode. Como ocurre con UTF-8, GB18030 es compatible con ASCII. GB18030 tambi&eacute;n es compatible con la p&aacute;gina de c&oacute;digos de Windows 936, tambi&eacute;n conocida como GBK.</p>

<p>En Unix/Linux los archivos UTF-16 se convierten a GB18030 cuando la codificaci&oacute;n local se establece en GB18030. Tenga en cuenta que esto s&oacute;lo funcionar&aacute; si la configuraci&oacute;n local es soportada por el sistema. Utilice <code>locale -a</code> para obtener el listado de configuraciones regionales admitidas.</p>

<p>Use la opci&oacute;n <code>-ul</code> o <code>-ub</code> para convertir un archivo UTF-16 sin BOM.</p>

<p>Los archivos codificados como GB18030 pueden tener una Marca de Orden de Bytes, como ocurre con los archivos Unicode.</p>

<h1 id="EJEMPLOS">EJEMPLOS</h1>

<p>Lee la entrada desde &#39;stdin&#39; y escribe la salida a &#39;stdout&#39;:</p>

<pre><code>dos2unix &lt; a.txt
cat a.txt | dos2unix</code></pre>

<p>Convierte y reemplaza a.txt. Convierte y reemplaza b.txt:</p>

<pre><code>dos2unix a.txt b.txt
dos2unix -o a.txt b.txt</code></pre>

<p>Convierte y reemplaza a.txt empleando modo de conversi&oacute;n ascii:</p>

<pre><code>dos2unix a.txt</code></pre>

<p>Convierte y reemplaza a.txt empleando modo de conversi&oacute;n ascii, convierte y reemplaza b.txt empleando modo de conversi&oacute;n de 7bits:</p>

<pre><code>dos2unix a.txt -c 7bit b.txt
dos2unix -c ascii a.txt -c 7bit b.txt
dos2unix -ascii a.txt -7 b.txt</code></pre>

<p>Convierte a.txt del formato de Mac a Unix:</p>

<pre><code>dos2unix -c mac a.txt
mac2unix a.txt</code></pre>

<p>Convierte a.txt del formato de Unix a Mac:</p>

<pre><code>unix2dos -c mac a.txt
unix2mac a.txt</code></pre>

<p>Convierte y reemplaza a.txt manteniendo la fecha del archivo original:</p>

<pre><code>dos2unix -k a.txt
dos2unix -k -o a.txt</code></pre>

<p>Convierte a.txt y escribe la salida en e.txt:</p>

<pre><code>dos2unix -n a.txt e.txt</code></pre>

<p>Convierte a.txt y escribe la salida en e.txt, manteniendo la fecha de e.txt igual a la de a.txt:</p>

<pre><code>dos2unix -k -n a.txt e.txt</code></pre>

<p>Convierte y reemplaza a.txt, convierte b.txt y escribe en e.txt:</p>

<pre><code>dos2unix a.txt -n b.txt e.txt
dos2unix -o a.txt -n b.txt e.txt</code></pre>

<p>Convierte c.txt y escribe en e.txt, convierte y reemplaza a.txt, convierte y reemplaza b.txt, convierte d.txt y escribe en f.txt:</p>

<pre><code>dos2unix -n c.txt e.txt -o a.txt b.txt -n d.txt f.txt</code></pre>

<h1 id="CONVERSIN-RECURSIVA">CONVERSI&Oacute;N RECURSIVA</h1>

<p>In a Unix shell the find(1) and xargs(1) commands can be used to run dos2unix recursively over all text files in a directory tree. For instance to convert all .txt files in the directory tree under the current directory type:</p>

<pre><code>find . -name &#39;*.txt&#39; -print0 |xargs -0 dos2unix</code></pre>

<p>The find(1) option <code>-print0</code> and corresponding xargs(1) option <code>-0</code> are needed when there are files with spaces or quotes in the name. Otherwise these options can be omitted. Another option is to use find(1) with the <code>-exec</code> option:</p>

<pre><code>find . -name &#39;*.txt&#39; -exec dos2unix {} \;</code></pre>

<p>In a Windows Command Prompt the following command can be used:</p>

<pre><code>for /R %G in (*.txt) do dos2unix &quot;%G&quot;</code></pre>

<p>PowerShell users can use the following command in Windows PowerShell:</p>

<pre><code>get-childitem -path . -filter &#39;*.txt&#39; -recurse | foreach-object {dos2unix $_.Fullname}</code></pre>

<h1 id="INTERNACIONALIZACIN">INTERNACIONALIZACI&Oacute;N</h1>

<dl>

<dt id="LANG"><b>LANG</b></dt>
<dd>

<p>El idioma principal se selecciona con la variable de entorno LANG. La variable LANG consiste de varias partes. La primer parte es el c&oacute;digo del idioma en min&uacute;sculas. La segunda es opcional y es el c&oacute;digo del pa&iacute;s en may&uacute;sculas, precedido por un gui&oacute;n bajo. Existe tambi&eacute;n una tercera parte opcional: la codificaci&oacute;n de caracteres, precedida por un punto. Unos cuantos ejemplos para int&eacute;rpretes de comandos tipo POSIX est&aacute;ndar:</p>

<pre><code>export LANG=nl               Neerland&eacute;s
export LANG=nl_NL            Neerland&eacute;s, Pa&iacute;ses Bajos
export LANG=nl_BE            Neerland&eacute;s, B&eacute;lgica
export LANG=es_ES            Espa&ntilde;ol, Espa&ntilde;a
export LANG=es_MX            Espa&ntilde;ol, M&eacute;xico
export LANG=en_US.iso88591   Ingles, EE. UU., codificaci&oacute;n Lat&iacute;n-1
export LANG=en_GB.UTF-8      Ingles, Reino Unido, codificaci&oacute;n UTF-8</code></pre>

<p>For a complete list of language and country codes see the gettext manual: <a href="https://www.gnu.org/software/gettext/manual/html_node/Usual-Language-Codes.html">https://www.gnu.org/software/gettext/manual/html_node/Usual-Language-Codes.html</a></p>

<p>En sistemas Unix puede emplear el comando locale(1) para obtener informaci&oacute;n espec&iacute;fica de locale.</p>

</dd>
<dt id="LANGUAGE"><b>LANGUAGE</b></dt>
<dd>

<p>With the LANGUAGE environment variable you can specify a priority list of languages, separated by colons. Dos2unix gives preference to LANGUAGE over LANG. For instance, first Dutch and then German: <code>LANGUAGE=nl:de</code>. You have to first enable localization, by setting LANG (or LC_ALL) to a value other than &quot;C&quot;, before you can use a language priority list through the LANGUAGE variable. See also the gettext manual: <a href="https://www.gnu.org/software/gettext/manual/html_node/The-LANGUAGE-variable.html">https://www.gnu.org/software/gettext/manual/html_node/The-LANGUAGE-variable.html</a></p>

<p>Si selecciona un idioma que no est&aacute; disponible el programa funcionar&aacute; en ingles.</p>

</dd>
<dt id="DOS2UNIX_LOCALEDIR"><b>DOS2UNIX_LOCALEDIR</b></dt>
<dd>

<p>Con la variable de entorno DOS2UNIX_LOCALEDIR el LOCALEDIR asignado durante la compilaci&oacute;n puede ser modificado. LOCALEDIR es usado para encontrar los archivos de idioma. El valor por defecto de GNU es <code>/usr/local/share/locale</code>. El par&aacute;metro <b>--version</b> mostrar&aacute; el LOCALEDIR en uso.</p>

<p>Ejemplo (int&eacute;rprete de comandos POSIX):</p>

<pre><code>export DOS2UNIX_LOCALEDIR=$HOME/share/locale</code></pre>

</dd>
</dl>

<h1 id="VALOR-DE-RETORNO">VALOR DE RETORNO</h1>

<p>Se regresa cero cuando el programa termina exitosamente. Cuando ocurre un error del sistema se regresar&aacute; el &uacute;ltimo n&uacute;mero de error del sistema. Para otros errores se regresa 1.</p>

<p>El valor de retorno es siempre cero en modo silencioso, excepto cuando se emplean par&aacute;metros incorrectos.</p>

<h1 id="ESTNDARES">EST&Aacute;NDARES</h1>

<p><a href="https://en.wikipedia.org/wiki/Text_file">https://en.wikipedia.org/wiki/Text_file</a></p>

<p><a href="https://en.wikipedia.org/wiki/Carriage_return">https://en.wikipedia.org/wiki/Carriage_return</a></p>

<p><a href="https://en.wikipedia.org/wiki/Newline">https://en.wikipedia.org/wiki/Newline</a></p>

<p><a href="https://en.wikipedia.org/wiki/Unicode">https://en.wikipedia.org/wiki/Unicode</a></p>

<h1 id="AUTORES">AUTORES</h1>

<p>Benjamin Lin - &lt;<EMAIL>&gt;, Bernd Johannes Wuebben (mac2unix mode) - &lt;<EMAIL>&gt;, Christian Wurll (add extra newline) - &lt;<EMAIL>&gt;, Erwin Waterlander - &lt;<EMAIL>&gt; (maintainer)</p>

<p>Project page: <a href="https://waterlan.home.xs4all.nl/dos2unix.html">https://waterlan.home.xs4all.nl/dos2unix.html</a></p>

<p>SourceForge page: <a href="https://sourceforge.net/projects/dos2unix/">https://sourceforge.net/projects/dos2unix/</a></p>

<h1 id="VASE-TAMBIN">V&Eacute;ASE TAMBI&Eacute;N</h1>

<p>file(1) find(1) iconv(1) locale(1) xargs(1)</p>


</body>

</html>


