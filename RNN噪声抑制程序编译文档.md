# RNN噪声抑制程序编译文档

## 项目概述
本文档记录了如何在Windows环境下编译RNN噪声抑制程序（rnn_gao_new.exe）的完整流程。

## 环境要求
- Windows 10/11 操作系统
- MSYS2环境（已包含在项目的env文件夹中）
- 足够的磁盘空间（约300MB用于编译工具）

## 项目结构
```
d:\RNN\
├── noisy.wav                    # 输入的含噪声音频文件
└── rnnoise-main\
    ├── main.c                   # 主程序源代码
    ├── CMakeLists.txt           # CMake构建配置文件
    ├── Makefile                 # Make构建文件
    ├── bin\                     # 编译输出目录
    │   ├── rnn_gao_new.exe     # 编译生成的可执行文件
    │   ├── noisy.wav           # 输入音频文件
    │   └── out.wav             # 处理后的输出音频文件
    ├── src\                     # 源代码目录
    ├── include\                 # 头文件目录
    └── env\                     # 环境工具目录
        └── MSYS2\               # MSYS2环境
```

## 编译流程详解

### 第一步：启动MSYS2环境
```bash
# 从项目根目录启动MSYS2 MinGW64环境
rnnoise-main\env\MSYS2\msys2_shell.cmd -defterm -here -no-start -mingw64
```

**说明**：
- `-defterm`: 使用默认终端
- `-here`: 在当前目录启动
- `-no-start`: 不启动新窗口
- `-mingw64`: 使用MinGW64环境

### 第二步：安装编译工具
```bash
# 安装CMake和GCC编译器
pacman -S mingw-w64-x86_64-cmake mingw-w64-x86_64-gcc
```

**下载内容**：
- `mingw-w64-x86_64-cmake-4.0.3-1-any` (约9.2MB)
- `mingw-w64-x86_64-gcc` (重新安装/更新)

**安装位置**：
- CMake: `/mingw64/bin/cmake`
- GCC: `/mingw64/bin/gcc`

### 第三步：修复CMake配置文件
原始的CMakeLists.txt使用了过旧的CMake版本要求，需要更新：

```cmake
# 修改前
cmake_minimum_required(VERSION 2.8)

# 修改后  
cmake_minimum_required(VERSION 3.5)
```

### 第四步：清理旧的构建文件
```bash
# 进入项目目录
cd rnnoise-main

# 删除旧的CMake缓存文件
rm -rf CMakeCache.txt CMakeFiles
```

### 第五步：生成构建文件
```bash
# 使用CMake生成Ninja构建文件
cmake .
```

**输出信息**：
```
-- Building for: Ninja
-- The C compiler identification is GNU 15.1.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/cc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Configuring done (1.6s)
-- Generating done (0.0s)
-- Build files have been written to: D:/RNN/rnnoise-main
```

### 第六步：编译程序
```bash
# 使用Ninja进行编译
ninja
```

**编译过程**：
```
[0/10] Building C object src/CMakeFiles/rnnLib.dir/celt_lpc.c.obj
[1/10] Building C object src/CMakeFiles/rnnLib.dir/kiss_fft.c.obj
[2/10] Building C object src/CMakeFiles/rnnLib.dir/rnn_data.c.obj
[3/10] Building C object CMakeFiles/rnn_gao_new.dir/main.c.obj
[4/10] Building C object src/CMakeFiles/rnnLib.dir/rnn.c.obj
[5/10] Building C object src/CMakeFiles/rnnLib.dir/denoise.c.obj
[6/10] Linking C static library src\librnnLib.a
[7/10] Linking C executable bin\rnn_gao_new.exe
```

**生成文件**：
- `bin\rnn_gao_new.exe` (527,572 字节) - 主要可执行文件
- `src\librnnLib.a` - 静态链接库

## 程序使用方法

### 基本语法
```bash
./rnn_gao_new.exe <输入音频文件> <输出音频文件>
```

### 实际使用示例
```bash
# 进入bin目录
cd bin

# 运行噪声抑制程序
./rnn_gao_new.exe noisy.wav out.wav
```

**程序输出**：
```
Start doing noise supreesion
Finished RNNnoise Noise Supression
```

### 处理结果
- **输入文件**: `noisy.wav` (288,044 字节)
- **输出文件**: `out.wav` (143,724 字节)
- **处理效果**: 使用RNN深度学习模型进行噪声抑制

## 技术细节

### 编译器信息
- **编译器**: GCC 15.1.0 (MinGW-W64)
- **构建系统**: CMake 4.0.3 + Ninja
- **C标准**: C11
- **目标平台**: Windows x86_64

### 依赖库
程序使用了以下核心组件：
- `rnnoise` - RNN噪声抑制核心算法
- `dr_wav` - WAV文件读写库
- `dr_mp3` - MP3文件读写库（支持）
- 数学库 (`-lm`)

### 关键参数
- **帧大小**: 160 samples
- **采样处理**: 每帧160个样本
- **音频格式**: 支持WAV和MP3格式
- **输出格式**: 16位PCM WAV文件

## 故障排除

### 常见问题及解决方案

1. **"无法执行二进制文件"错误**
   - 原因：使用了不兼容的预编译二进制文件
   - 解决：重新编译生成适合当前环境的exe文件

2. **CMake版本错误**
   - 原因：CMakeLists.txt中版本要求过低
   - 解决：更新`cmake_minimum_required(VERSION 3.5)`

3. **找不到cmake命令**
   - 原因：cmake未安装或路径不正确
   - 解决：使用pacman安装mingw-w64-x86_64-cmake

4. **编译工具缺失**
   - 原因：缺少GCC编译器
   - 解决：安装mingw-w64-x86_64-gcc

## 总结
通过以上步骤，成功在Windows环境下使用MSYS2编译了RNN噪声抑制程序。整个过程包括环境配置、工具安装、源码修复、构建配置和最终编译，生成了可用的rnn_gao_new.exe可执行文件。
