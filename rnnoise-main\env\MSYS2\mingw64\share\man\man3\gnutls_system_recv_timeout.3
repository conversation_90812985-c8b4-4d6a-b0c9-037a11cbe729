.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_system_recv_timeout" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_system_recv_timeout \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_system_recv_timeout(gnutls_transport_ptr_t " ptr ", unsigned int " ms ");"
.SH ARGUMENTS
.IP "gnutls_transport_ptr_t ptr" 12
A file descriptor (wrapped in a gnutls_transport_ptr_t pointer)
.IP "unsigned int ms" 12
The number of milliseconds to wait.
.SH "DESCRIPTION"
Wait for data to be received from the provided socket ( \fIptr\fP ) within a
timeout period in milliseconds, using \fBselect()\fP on the provided  \fIptr\fP .

This function is provided as a helper for constructing custom
callbacks for \fBgnutls_transport_set_pull_timeout_function()\fP,
which can be used if you rely on socket file descriptors.

Returns \-1 on error, 0 on timeout, positive value if data are available for reading.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
