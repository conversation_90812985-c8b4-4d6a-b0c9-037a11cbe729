CMake 3.21 Release Notes
************************

.. only:: html

  .. contents::

Changes made since CMake 3.20 include the following.

New Features
============

Presets
-------

* :manual:`cmake-presets(7)` gained support for specifying the install prefix
  in a configure preset.

* :manual:`cmake-presets(7)` gained support for conditional enabling of presets.

* :manual:`cmake-presets(7)` gained support for a ``${hostSystemName}`` macro.

* :manual:`cmake-presets(7)` gained support for omitting the ``generator`` and
  ``binaryDir`` fields.

Generators
----------

* The :generator:`Visual Studio 17 2022` generator was added.

* The :ref:`Makefile Generators` and the :generator:`Ninja` generator
  learned to add linker launcher tools along with the linker for ``C``,
  ``CXX``, ``OBJC``, and ``OBJCXX`` languages.
  See the :variable:`CMAKE_<LANG>_LINKER_LAUNCHER` variable
  and :prop_tgt:`<LANG>_LINKER_LAUNCHER` target property for details.

Languages
---------

* <PERSON><PERSON><PERSON> learned to support ``HIP`` as a first-class language that can be
  enabled via the :command:`project` and :command:`enable_language` commands.

* :prop_tgt:`C_STANDARD`, :prop_tgt:`OBJC_STANDARD`, and the
  :manual:`Compile Features <cmake-compile-features(7)>` functionality gained
  support for C17 and C23.

* Source file extensions ``.ixx`` and ``.cppm`` are now treated as C++.

Command-Line
------------

* :manual:`cmake(1)` gained the :option:`--install-prefix <cmake --install-prefix>`
  command-line option to specify the location of the install prefix.

* :manual:`cmake(1)` gained the :option:`--toolchain <cmake --toolchain>`
  command-line option to specify a toolchain file.

* :manual:`cmake(1)` :option:`-E capabilities <cmake-E capabilities>` output,
  for some generators, may now contain a ``supportedPlatforms`` field listing
  platforms known to be supported in :variable:`CMAKE_GENERATOR_PLATFORM`.

* Messages printed to a terminal now may be colored by message type.

Compilers
---------

* The Fujitsu compiler is now supported using compiler id ``Fujitsu``
  in traditional (``Trad``) mode, and compiler id ``FujitsuClang``
  in ``Clang`` mode.

Platforms
---------

* CMake now supports the MSYS runtime environment, much like CYGWIN.

File-Based API
--------------

* The :manual:`cmake-file-api(7)` "codemodel" version 2 ``version`` field
  has been updated to 2.3.

* The :manual:`cmake-file-api(7)` "codemodel" version 2 gained a
  new "directory" object containing directory-level information.
  This includes a list of installers generated by the :command:`install`
  command.

Commands
--------

* The :command:`add_custom_command` command ``DEPFILE`` option:

  * may now use
    :manual:`generator expressions <cmake-generator-expressions(7)>`,

  * is now supported by :ref:`Visual Studio Generators` for VS 2012
    and above, and

  * is now supported by the :generator:`Xcode` generator.

* The :command:`add_custom_command(TARGET)` command
  (for :ref:`Build Events <add_custom_command(TARGET)>`)
  gained support for resolving target-dependent generator expressions.

* The :command:`build_command` command gained a ``PARALLEL_LEVEL`` option.

* The :command:`file(COPY_FILE)` command was added to copy a single file.

* The :command:`file(GET_RUNTIME_DEPENDENCIES)` command gained new
  ``POST_INCLUDE_FILES`` and ``POST_EXCLUDE_FILES`` arguments.

* The :command:`file(REAL_PATH)` command gained the option ``EXPAND_TILDE`` to
  replace any leading tilde with the path to the user's home directory.

* The :command:`file(RENAME)` command learned to optionally capture
  failure in a result variable.  It also gained a ``NO_REPLACE``
  option to fail if the destination exists.

* The :command:`install` command gained a new ``IMPORTED_RUNTIME_ARTIFACTS``
  mode, which can be used to install the runtime artifacts of imported targets.

* The :command:`install` command gained a new ``RUNTIME_DEPENDENCY_SET`` mode,
  which can be used to install runtime dependencies using
  :command:`file(GET_RUNTIME_DEPENDENCIES)`.

* The :command:`install(TARGETS)` command gained new ``RUNTIME_DEPENDENCIES``
  and ``RUNTIME_DEPENDENCY_SET`` arguments, which can be used to install
  runtime dependencies using :command:`file(GET_RUNTIME_DEPENDENCIES)`.

* The :command:`install(SCRIPT|CODE)` command
  supports a new option ``ALL_COMPONENTS`` which allows
  the corresponding code to run for every component of
  a per component installation.

* The :command:`project` command now sets variables
  :variable:`PROJECT_IS_TOP_LEVEL` and :variable:`<PROJECT-NAME>_IS_TOP_LEVEL`
  to indicate whether it was called in a top-level ``CMakeLists.txt`` file.

Variables
---------

* The :envvar:`CMAKE_TOOLCHAIN_FILE` environment variable was added to
  provide a default value for the :variable:`CMAKE_TOOLCHAIN_FILE` variable.

Properties
----------

* The :prop_dir:`IMPORTED_TARGETS` directory property was added to
  get a list of :ref:`Imported Targets` created in the current
  directory.

* The :prop_tgt:`XCODE_EMBED_APP_EXTENSIONS <XCODE_EMBED_<type>>` target property
  was added to tell the :generator:`Xcode` generator to embed app extensions
  such as iMessage sticker packs.
  Aspects of the embedding can be customized with the
  :prop_tgt:`XCODE_EMBED_APP_EXTENSIONS_PATH <XCODE_EMBED_<type>>`,
  :prop_tgt:`XCODE_EMBED_APP_EXTENSIONS_CODE_SIGN_ON_COPY <XCODE_EMBED_<type>_CODE_SIGN_ON_COPY>` and
  :prop_tgt:`XCODE_EMBED_APP_EXTENSIONS_REMOVE_HEADERS_ON_COPY <XCODE_EMBED_<type>_REMOVE_HEADERS_ON_COPY>`
  properties.

Modules
-------

* The :module:`FindBLAS` and :module:`FindLAPACK` modules learned to support
  the serial ``Fujitsu_SSL2`` and parallel ``Fujitsu_SSL2BLAMP`` libraries.

* The :module:`FindDevIL` module now provides imported targets.

* The :module:`FindIconv` module now has version support.

* The :module:`FindIntl` module now has version support.

* The :module:`FindMPI` module learned to support ``Fujitsu`` and
  ``FujitsuClang`` in both host and cross compiling modes.

* The :module:`FindMsys` module was added to find MSYS installations.
  Like :module:`FindCygwin`, it is used automatically by some other
  find modules to locate UNIX-style tools on Windows.

* The :module:`FindOpenMP` module learned to support ``Fujitsu`` and
  ``FujitsuClang``.

* The :module:`FindVulkan` module gained imported targets
  ``Vulkan::Headers`` and ``Vulkan::glslangValidator``.

* The :module:`UseJava` module command ``add_jar`` gained a ``RESOURCES``
  option to allow explicit naming of resources with non-optional namespace.

* The :module:`UseSWIG` module use now standard library naming conventions
  for the ``CSharp`` language. See policy :policy:`CMP0122`.

* The :module:`UseSWIG` module now supports using the ``swig`` tool to
  generate implicit dependencies with the :generator:`Xcode` generator.

Generator Expressions
---------------------

* A new :genex:`TARGET_RUNTIME_DLLS` generator expression was added.

CTest
-----

* :manual:`ctest(1)` gained documentation for its ability to capture
  :ref:`Additional Test Measurements`.

* :manual:`ctest(1)` learned to recognize files attached to a test at run time.
  Previously it was only possible to attach files to tests at configure time
  by using the :prop_test:`ATTACHED_FILES` or
  :prop_test:`ATTACHED_FILES_ON_FAIL` test properties.
  See :ref:`Additional Test Measurements` for more information.

* :manual:`ctest(1)` gained a :option:`--output-junit <ctest --output-junit>`
  option to write test results to a JUnit XML file.

* The :command:`ctest_build` command gained a ``PARALLEL_LEVEL`` option.

CPack
-----

* The :cpack_gen:`CPack DragNDrop Generator` gained option
  :variable:`CPACK_DMG_FILESYSTEM` to control the ``.dmg`` filesystem.

* The :cpack_gen:`CPack IFW Generator` now supports hyphens in names
  given to :command:`cpack_ifw_configure_component` or
  :command:`cpack_ifw_configure_component_group` as ``DEPENDS`` or
  ``DEPENDENCIES`` arguments.  This requires QtIFW 3.1 or later.

* The :cpack_gen:`CPack NSIS Generator` gained a new
  :variable:`CPACK_NSIS_EXECUTABLE` variable to specify the ``makensis``
  executable to use instead of the default one.

* The :variable:`CPACK_CUSTOM_INSTALL_VARIABLES` variable was added to set
  variables in ``cmake_install.cmake`` script invocations made by CPack.

Deprecated and Removed Features
===============================

* Undocumented :variable:`CMAKE_SYSTEM_NAME` version-stripping behavior has
  been removed entirely. If it is set by a ``-D`` flag or by a
  :manual:`toolchain file <cmake-toolchains(7)>`, it is left unaltered,
  even if it still contains a version number.
  Similar :variable:`CMAKE_HOST_SYSTEM_NAME` version-stripping behavior,
  also undocumented, has been moved earlier, before :command:`project` or
  :command:`enable_language` is called.

* ``ARMClang`` cpu/arch compile and link flags are no longer added
  automatically based on the :variable:`CMAKE_SYSTEM_PROCESSOR`
  variable or the undocumented ``CMAKE_SYSTEM_ARCH`` variable.
  They must be specified explicitly.  See policy :policy:`CMP0123`.

Other Changes
=============

* The :command:`find_file`, :command:`find_path`, :command:`find_program`,
  and :command:`find_library` commands handle cache variables in the same way
  regardless how they are defined. See policy :policy:`CMP0125` for details.

* The :command:`find_file`, :command:`find_path`, :command:`find_program`,
  and :command:`find_library` commands gained the option ``NO_CACHE`` to store
  find result in normal variable.

* The :command:`foreach` command now isolates loop variables in the loop scope.
  See policy :policy:`CMP0124` for details.

* The :command:`list` command's ``GET``, ``INSERT``, ``SUBLIST``, and
  ``REMOVE_AT`` subcommands now error with invalid (i.e., non-integer) values
  are given as any of their index arguments based on the setting of policy
  :policy:`CMP0121`.

* The :command:`set(CACHE)` command no longer removes a normal variable
  of the same name, if any. See policy :policy:`CMP0126`.

* :command:`target_link_libraries` calls referencing object libraries
  via the :genex:`TARGET_OBJECTS` generator expression now place the
  object files before all libraries on the link line, regardless of
  their specified order.  See documentation on
  :ref:`Linking Object Libraries via \$\<TARGET_OBJECTS\>` for details.

* The :ref:`Ninja Generators` now pass source files and include directories
  to the compiler using absolute paths.  This makes diagnostic messages and
  debug symbols more consistent, and matches the :ref:`Makefile Generators`.

* The :generator:`NMake Makefiles` generator now encodes the generated
  makefiles as UTF-8 with a BOM when using ``nmake`` from VS 9 or above.

* The :ref:`Visual Studio Generators` for VS 2010 and above now place
  per-source preprocessor definitions after target-wide preprocssor
  definitions.  This makes VS consistent with the :ref:`Ninja Generators`
  and the :ref:`Makefile Generators`.

* The precompiled binaries provided on
  `cmake.org <https://cmake.org/download/>`_ now support
  ``liblzma`` multi-threading.  See the :variable:`CPACK_THREADS` and
  :variable:`CPACK_ARCHIVE_THREADS` variables.

Updates
=======

Changes made since CMake 3.21.0 include the following.

3.21.1
------

* The :generator:`Visual Studio 17 2022` generator is now based on
  "Visual Studio 2022 Preview 2".  Previously it was based on "Preview 1.1".

3.21.2
------

* ``CUDA`` targets with :prop_tgt:`CUDA_SEPARABLE_COMPILATION` enabled are now
  correctly generated in non-root directories.

* The :generator:`Visual Studio 17 2022` generator is now based on
  "Visual Studio 2022 Preview 3.1".  Previously it was based on "Preview 2".

3.21.3
------

* The :generator:`Visual Studio 17 2022` generator is now based on
  "Visual Studio 2022 Preview 4".  Previously it was based on "Preview 3.1".

* The AMD ROCm Platform ``hipcc`` compiler was identified by CMake 3.21.0
  through 3.21.2 as a distinct compiler with id ``ROCMClang``.  This has
  been removed because it caused regressions.  Instead:

  * ``hipcc`` may no longer be used as a ``HIP`` compiler because it
    interferes with flags CMake needs to pass to Clang.  Use Clang directly.

  * ``hipcc`` may once again be used as a ``CXX`` compiler, and is treated as
    whatever compiler it selects underneath, as CMake 3.20 and below did.

3.21.4
------

* The :generator:`Visual Studio 17 2022` generator is now based on the
  "Visual Studio 2022" release candidates.  Previously it was based on
  preview versions.

3.21.5, 3.21.6, 3.21.7
----------------------

These versions made no changes to documented features or interfaces.
Some implementation updates were made to support ecosystem changes
and/or fix regressions.
