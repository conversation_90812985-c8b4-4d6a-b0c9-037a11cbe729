.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_flags" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_flags \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void gnutls_x509_crt_set_flags(gnutls_x509_crt_t " cert ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
A type \fBgnutls_x509_crt_t\fP
.IP "unsigned int flags" 12
flags from the \fBgnutls_x509_crt_flags\fP
.SH "DESCRIPTION"
This function will set flags for the specified certificate.
Currently this is useful for the \fBGNUTLS_X509_CRT_FLAG_IGNORE_SANITY\fP
which allows importing certificates even if they have known issues.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
