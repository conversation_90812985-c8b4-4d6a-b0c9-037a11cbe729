/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

import "oaidl.idl";
import "ocidl.idl";

cpp_quote("")
interface IWSDXMLContext;

enum {
  OpNone,
  OpEndOfTable,
  OpBeginElement_,
  OpBeginAnyElement,
  OpEndElement,
  OpElement_,
  OpAnyElement,
  OpAnyElements,
  OpAnyText,
  OpAttribute_,
  OpBeginChoice,
  OpEndChoice,
  OpBeginSequence,
  OpEndSequence,
  OpBeginAll,
  OpEndAll,
  OpAnything,
  OpAnyNumber,
  OpOneOrMore,
  OpOptional,
  OpFormatBool_,
  OpFormatInt8_,
  OpFormatInt16_,
  OpFormatInt32_,
  OpFormatInt64_,
  OpFormatUInt8_,
  OpFormatUInt16_,
  OpFormatUInt32_,
  OpFormatUInt64_,
  OpFormatUnicodeString_,
  OpFormatDom_,
  OpFormatStruct_,
  OpFormatUri_,
  OpFormatUuidUri_,
  OpFormatName_,
  OpFormatListInsertTail_,
  OpFormatType_,
  OpFormatDynamicType_,
  OpFormatLookupType_,
  OpFormatDuration_,
  OpFormatDateTime_,
  OpFormatFloat_,
  OpFormatDouble_,
  OpProcess_,
  OpQualifiedAttribute_,
  OpFormatXMLDeclaration_,
  OpFormatMax
};

typedef struct _WSDXML_NAMESPACE WSDXML_NAMESPACE;
typedef const WSDXML_NAMESPACE *PCWSDXML_NAMESPACE;
typedef struct _WSDXML_NAME WSDXML_NAME;
typedef struct _WSDXML_TYPE WSDXML_TYPE;
typedef const WSDXML_TYPE *PCWSDXML_TYPE;
typedef struct _WSDXML_PREFIX_MAPPING WSDXML_PREFIX_MAPPING;
typedef struct _WSDXML_ATTRIBUTE WSDXML_ATTRIBUTE;
typedef struct _WSDXML_NODE WSDXML_NODE;
typedef struct _WSDXML_ELEMENT WSDXML_ELEMENT;
typedef struct _WSDXML_TEXT WSDXML_TEXT;
typedef struct _WSDXML_ELEMENT_LIST WSDXML_ELEMENT_LIST;

typedef struct _WSD_DATETIME {
  BOOL isPositive;
  ULONG year;
  UCHAR month;
  UCHAR day;
  UCHAR hour;
  UCHAR minute;
  UCHAR second;
  UINT millisecond;
  BOOL TZIsLocal;
  BOOL TZIsPositive;
  UCHAR TZHour;
  UCHAR TZMinute;
} WSD_DATETIME;

typedef struct _WSD_DURATION {
  BOOL isPositive;
  ULONG year;
  ULONG month;
  ULONG day;
  ULONG hour;
  ULONG minute;
  ULONG second;
  ULONG millisecond;
} WSD_DURATION;

cpp_quote("")
cpp_quote("#define WSDXML_TYPE_ENCODING(tidx,lno) ((((DWORD) lno) << 28) | tidx)")
cpp_quote("#define WSDXML_NAMESPACE_ENCODING(nsidx, lno) ((((WORD) lno) << 12) | nsidx)")
cpp_quote("#define WSDXML_NAME_ENCODING(nidx, nsenc) ((((DWORD) nsenc) << 16) | nidx)")
cpp_quote("")
cpp_quote("#define OFFSET(type,field) ((DWORD_PTR) (&((type *)0)->field))")
cpp_quote("#define BYTE0(n) (BYTE) (((DWORD) n) & 0xff)")
cpp_quote("#define BYTE1(n) (BYTE) ((((DWORD) n) >> 8) & 0xff)")
cpp_quote("#define BYTE2(n) (BYTE) ((((DWORD) n) >> 16) & 0xff)")
cpp_quote("#define BYTE3(n) (BYTE)((((DWORD) n) >> 24) & 0xff)")
cpp_quote("#define BYTES(n) BYTE0(n), BYTE1(n), BYTE2(n), BYTE3(n)")
cpp_quote("")
cpp_quote("#define OpAttribute(name) OpAttribute_, BYTES(name)")
cpp_quote("#define OpBeginElement(name) OpBeginElement_, BYTES(name)")
cpp_quote("#define OpElement(name) OpElement_, BYTES(name)")
cpp_quote("#define OpFormatBool(type,field,isptr) OpFormatBool_, BYTES(isptr), BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatDateTime(type,field) OpFormatDateTime_, BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatDom(type,field) OpFormatDom_, BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatDouble(type,field,isptr) OpFormatDouble_, BYTES(isptr),BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatDuration(type,field) OpFormatDuration_, BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatDynamicType(name,type,field) OpFormatDynamicType_, BYTES(name),BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatFloat(type,field,isptr) OpFormatFloat_, BYTES(isptr),BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatInt8(type,field,isptr) OpFormatInt8_, BYTES(isptr), BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatInt16(type,field,isptr) OpFormatInt16_, BYTES(isptr), BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatInt32(type,field,isptr) OpFormatInt32_, BYTES(isptr), BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatInt64(type,field,isptr) OpFormatInt64_, BYTES(isptr), BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatListInsertTail(s,type,field) OpFormatListInsertTail_,BYTES(sizeof(s)),BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatLookupType(uriField,type,field) OpFormatLookupType_, BYTES(OFFSET(type,uriField)),BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatName(type,field) OpFormatName_, BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatPointerToStruct(sType) OpFormatStruct_, BYTES(sizeof(sType)),BYTES(0)")
cpp_quote("#define OpFormatStruct(sType,type,field) OpFormatStruct_, BYTES(sizeof(sType)),BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatType(table,type,field) OpFormatType_, BYTES(table),BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatUInt8(type,field,isptr) OpFormatUInt8_, BYTES(isptr), BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatUInt16(type,field,isptr) OpFormatUInt16_, BYTES(isptr), BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatUInt32(type,field,isptr) OpFormatUInt32_, BYTES(isptr), BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatUInt64(type,field,isptr) OpFormatUInt64_, BYTES(isptr), BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatUnicodeString(type,field) OpFormatUnicodeString_, BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatUri(type,field) OpFormatUri_, BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatUuidUri(type,field,isptr) OpFormatUuidUri_, BYTES(isptr),BYTES(OFFSET(type,field))")
cpp_quote("#define OpFormatXMLDeclaration(type,field) OpFormatXMLDeclaration_, BYTES(OFFSET(type,field))")
cpp_quote("#define OpProcess(type,field) OpProcess_, BYTES(OFFSET(type,field))")
cpp_quote("#define OpQualifiedAttribute(name) OpQualifiedAttribute_, BYTES(name)")

[object, local, restricted, uuid (75d8f3ee-3e5a-43b4-a15a-bcf6887460c0), pointer_default (unique)]
interface IWSDXMLContext : IUnknown {
  HRESULT AddNamespace ([in] LPCWSTR pszUri,[in] LPCWSTR pszSuggestedPrefix,[out] WSDXML_NAMESPACE **ppNamespace);
  HRESULT AddNameToNamespace ([in] LPCWSTR pszUri,[in] LPCWSTR pszName,[out] WSDXML_NAME **ppName);
  HRESULT SetNamespaces ([in, size_is (wNamespacesCount)] const PCWSDXML_NAMESPACE *pNamespaces,[in] WORD wNamespacesCount,[in] BYTE bLayerNumber);
  HRESULT SetTypes ([in, size_is (dwTypesCount)] const PCWSDXML_TYPE *pTypes,[in] DWORD dwTypesCount,[in] BYTE bLayerNumber);
};

cpp_quote("HRESULT WINAPI WSDXMLGetNameFromBuiltinNamespace(LPCWSTR pszNamespace, LPCWSTR pszName, WSDXML_NAME **ppName);")
cpp_quote("HRESULT WINAPI WSDXMLCreateContext(IWSDXMLContext **ppContext);")

cpp_quote("#endif")
