.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_db_check_entry_time" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_db_check_entry_time \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "time_t gnutls_db_check_entry_time(gnutls_datum_t * " entry ");"
.SH ARGUMENTS
.IP "gnutls_datum_t * entry" 12
is a pointer to a \fBgnutls_datum_t\fP type.
.SH "DESCRIPTION"
This function returns the time that this entry was active.
It can be used for database entry expiration.
.SH "RETURNS"
The time this entry was created, or zero on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
