.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_import_gost_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_import_gost_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_import_gost_raw(gnutls_privkey_t " key ", gnutls_ecc_curve_t " curve ", gnutls_digest_algorithm_t " digest ", gnutls_gost_paramset_t " paramset ", const gnutls_datum_t * " x ", const gnutls_datum_t * " y ", const gnutls_datum_t * " k ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
The key
.IP "gnutls_ecc_curve_t curve" 12
holds the curve
.IP "gnutls_digest_algorithm_t digest" 12
holds the digest
.IP "gnutls_gost_paramset_t paramset" 12
holds the GOST parameter set ID
.IP "const gnutls_datum_t * x" 12
holds the x\-coordinate
.IP "const gnutls_datum_t * y" 12
holds the y\-coordinate
.IP "const gnutls_datum_t * k" 12
holds the k (private key)
.SH "DESCRIPTION"
This function will convert the given GOST private key's parameters to the
native \fBgnutls_privkey_t\fP format.  The output will be stored
in  \fIkey\fP .   \fIdigest\fP should be one of GNUTLS_DIG_GOSR_94,
GNUTLS_DIG_STREEBOG_256 or GNUTLS_DIG_STREEBOG_512.  If  \fIparamset\fP is set to
GNUTLS_GOST_PARAMSET_UNKNOWN default one will be selected depending on
 \fIdigest\fP .
.SH "NOTE"
parameters should be stored with least significant byte first. On
version 3.6.3 big\-endian format was used incorrectly.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
