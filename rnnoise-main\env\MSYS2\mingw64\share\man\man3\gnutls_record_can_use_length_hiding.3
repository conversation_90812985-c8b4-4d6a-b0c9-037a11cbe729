.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_can_use_length_hiding" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_can_use_length_hiding \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_record_can_use_length_hiding(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
If the session supports length\-hiding padding, you can
invoke \fBgnutls_record_send_range()\fP to send a message whose
length is hidden in the given range. If the session does not
support length hiding padding, you can use the standard
\fBgnutls_record_send()\fP function, or \fBgnutls_record_send_range()\fP
making sure that the range is the same as the length of the
message you are trying to send.
.SH "RETURNS"
true (1) if the current session supports length\-hiding
padding, false (0) if the current session does not.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
