# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the mintty package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2025-03-22 07:33+0100\n"
"PO-Revision-Date: 2018-06-24 00:19-0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.6\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: charset.c:228 charset.c:239 winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "(Default)"
msgstr "(Padrão)"

#: charset.c:250
msgid "(OEM codepage)"
msgstr "(Página de código OEM)"

#: charset.c:254
msgid "(ANSI codepage)"
msgstr "(Página de código ANSI)"

#: child.c:96
msgid "There are no available terminals"
msgstr "Não há terminais disponíveis"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "Erro: Não foi possível abrir o arquivo de log"

#: child.c:334
msgid "Error: Could not fork child process"
msgstr "Erro: Não foi possível bifurcar o processo filho"

#: child.c:336
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr ""
"Pode ser necessário um rebasing do DLL; veja 'rebaseall / rebase --help'"

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:426
msgid "Failed to run '%s': %s"
msgstr "Falha ao executar '%s': %s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:578
msgid "%s: Exit %i"
msgstr "%s: Saiu com código %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:585
msgid "TERMINATED"
msgstr "FINALIZADO"

#: child.c:1232
msgid "Error: Could not fork child daemon"
msgstr "Erro: Não foi possível bifurcar o daemon filho"

#. __ Setting false for Boolean options (localization optional)
#: config.c:655
msgid "no"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:657
msgid "yes"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:659
msgid "false"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:661
msgid "true"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:663
msgid "off"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:665
msgid "on"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:694
msgid "stretch"
msgstr "esticar"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:696
msgid "align"
msgstr "alinhar"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:698
msgid "middle"
msgstr "meio"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:700
msgid "full"
msgstr "completa"

#. __ %s: unknown option name
#: config.c:866
msgid "Ignoring unknown option '%s'"
msgstr "Ignorando opção desconhecida '%s'"

#: config.c:914 config.c:943
msgid "Internal error: too many options"
msgstr "Erro interno: opções em excesso"

#: config.c:930
msgid "Internal error: too many options/comments"
msgstr "Erro interno: opções/comentários em excesso"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1100
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "Ignorando valor inválido '%s' para a opção '%s'"

#. __ %s: option name
#: config.c:1112
msgid "Ignoring option '%s' with missing value"
msgstr "Ignorando opção '%s' com valor faltando"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1791
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"Não foi possível salvar as opções em '%s':\n"
"%s."

#: config.c:2172
msgid "◇ None (printing disabled) ◇"
msgstr "◇ Nenhuma (impressão desativada) ◇"

#: config.c:2174
msgid "◆ Default printer ◆"
msgstr "◆ Impressora padrão ◆"

#. __ UI localization disabled
#: config.c:2283
msgid "– None –"
msgstr "– Nenhuma –"

#. __ UI localization: use Windows desktop setting
#: config.c:2285
msgid "@ Windows language @"
msgstr "@ Idioma do Windows @"

#. __ UI localization: use environment variable setting (LANGUAGE, LC_*)
#: config.c:2287
msgid "* Locale environm. *"
msgstr "* Variável amb. de locale *"

#. __ UI localization: use mintty configuration setting (Text - Locale)
#: config.c:2289
msgid "= cfg. Text Locale ="
msgstr "= Cfg. de locale em Texto ="

#: config.c:2394
msgid "simple beep"
msgstr "Bipe simples"

#: config.c:2395
msgid "no beep"
msgstr "Sem bipe"

#: config.c:2396
msgid "Default Beep"
msgstr "Bipe padrão"

#: config.c:2397
msgid "Critical Stop"
msgstr "Parada crítica"

#: config.c:2398
msgid "Question"
msgstr "Pergunta"

#: config.c:2399
msgid "Exclamation"
msgstr "Exclamação"

#: config.c:2400
msgid "Asterisk"
msgstr "Asterisco"

#: config.c:2443
msgid "◇ None (system sound) ◇"
msgstr "◇ Nenhum (som do sistema) ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2874 config.c:3425
msgid "◇ None ◇"
msgstr "◇ Nenhum ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2877
msgid "downloaded / give me a name!"
msgstr "baixado / me dê um nome!"

#: config.c:2983
msgid "Could not load web theme"
msgstr "Não foi possível carregar o tema web"

#: config.c:3040
msgid "Cannot write theme file"
msgstr "Não é possível gravar o tema web"

#: config.c:3045
msgid "Cannot store theme file"
msgstr "Não é possível salvar o tema web"

#. __ Options - Text:
#: config.c:3502 config.c:3840 config.c:3939
msgid "as font"
msgstr "como fonte"

#. __ Options - Text:
#: config.c:3503 config.c:3845 config.c:3944
msgid "as colour"
msgstr "como cor"

#: config.c:3504
msgid "as font & as colour"
msgstr "como fonte & cor"

#. __ Options - Text:
#: config.c:3505 config.c:3850 config.c:3949
msgid "xterm"
msgstr ""

#. __ Dialog button - show About text
#: config.c:3653
msgid "About..."
msgstr "Sobre..."

#. __ Dialog button - save changes
#: config.c:3656
msgid "Save"
msgstr "Salvar"

#. __ Dialog button - cancel
#: config.c:3660 winctrls.c:1277 windialog.c:895
msgid "Cancel"
msgstr "Cancelar"

#. __ Dialog button - apply changes
#: config.c:3664
msgid "Apply"
msgstr "Aplicar"

#. __ Dialog button - take notice
#: config.c:3668 windialog.c:892
msgid "I see"
msgstr "Entendi"

#. __ Dialog button - confirm action
#: config.c:3670 winctrls.c:1276 windialog.c:894
msgid "OK"
msgstr "OK"

#. __ Options - Looks: treeview label
#: config.c:3677 config.c:3708 config.c:3767
msgid "Looks"
msgstr "Aparência"

#. __ Options - Looks: panel title
#: config.c:3679
msgid "Looks in Terminal"
msgstr "Aparência no terminal"

#. __ Options - Looks: section title
#: config.c:3681
msgid "Colours"
msgstr "Cores"

#. __ Options - Looks:
#: config.c:3685
msgid "&Foreground..."
msgstr "&Prim. plano..."

#. __ Options - Looks:
#: config.c:3689
msgid "&Background..."
msgstr "&Fundo..."

#. __ Options - Looks:
#: config.c:3693
msgid "&Cursor..."
msgstr "&Cursor..."

#. __ Options - Looks:
#: config.c:3697
msgid "&Theme"
msgstr "&Tema"

#. __ Options - Looks: name of web service
#: config.c:3702
msgid "Color Scheme Designer"
msgstr "Criar esquema de cores"

#. __ Options - Looks: store colour scheme
#: config.c:3705 winctrls.c:484
msgid "Store"
msgstr "Salvar"

#. __ Options - Looks: section title
#: config.c:3710
msgid "Transparency"
msgstr "Transparência"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3716 config.c:4098 config.c:4261 config.c:4459
msgid "&Off"
msgstr "&Desat."

#. __ Options - Looks: transparency
#: config.c:3718
msgid "&Low"
msgstr "&Baixa"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3720
msgid "&Med."
msgstr "&Méd."

#. __ Options - Looks: transparency
#: config.c:3722
msgid "&Medium"
msgstr "&Média"

#. __ Options - Looks: transparency
#: config.c:3724
msgid "&High"
msgstr "&Alta"

#. __ Options - Looks: transparency
#: config.c:3726
msgid "Gla&ss"
msgstr "&Vidro"

#. __ Options - Looks: transparency
#: config.c:3733 config.c:3745 config.c:3752
msgid "Opa&que when focused"
msgstr "&Opaca quando em primeiro plano"

#. __ Options - Looks: transparency
#: config.c:3738
msgid "Blu&r"
msgstr "&Desfoque"

#: config.c:3759
msgid "◄"
msgstr ""

#: config.c:3762
msgid "►"
msgstr ""

#. __ Options - Looks: section title
#: config.c:3769
msgid "Cursor"
msgstr "Cursor"

#. __ Options - Looks: cursor type
#: config.c:3774
msgid "Li&ne"
msgstr "L&inha"

#. __ Options - Looks: cursor type
#: config.c:3776
msgid "Bloc&k"
msgstr "Blo&co"

#. __ Options - Looks: cursor type
#: config.c:3779
msgid "Bo&x"
msgstr ""

#. __ Options - Looks: cursor type
#: config.c:3782
msgid "&Underscore"
msgstr "&Sublinhado"

#. __ Options - Looks: cursor feature
#: config.c:3787
msgid "Blinkin&g"
msgstr "&Piscar"

#. __ Options - Text: treeview label
#: config.c:3794 config.c:3819 config.c:3834 config.c:3883 config.c:3933
#: config.c:3958 config.c:3980 config.c:3993 config.c:4001
msgid "Text"
msgstr "Texto"

#. __ Options - Text: panel title
#: config.c:3796
msgid "Text and Font properties"
msgstr "Propriedades de texto e fonte"

#. __ Options - Text: section title
#: config.c:3798
msgid "Font"
msgstr "Fonte"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3806 winctrls.c:1287
msgid "Font st&yle:"
msgstr "E&stilo de fonte:"

#. __ Font chooser:
#: config.c:3811 winctrls.c:1289
msgid "&Size:"
msgstr "&Tamanho:"

#. __ Options - Text:
#: config.c:3823 config.c:3902
msgid "Sho&w bold as font"
msgstr "Most. n&eg. como fonte"

#. __ Options - Text:
#: config.c:3828 config.c:3907
msgid "Show &bold as colour"
msgstr "Most. ne&g. como cor"

#. __ Options - Text:
#: config.c:3836 config.c:3857 config.c:3935 config.c:3962
msgid "Show bold"
msgstr "Most. negrita"

#. __ Options - Text:
#: config.c:3864 config.c:3912 config.c:3968
msgid "&Allow blinking"
msgstr "Pe&rmitir piscar"

#. __ Options - Text:
#: config.c:3869 config.c:3973
msgid "Show dim as font"
msgstr "Most. pálido como fonte"

#. __ Options - Text:
#: config.c:3887 config.c:3920 config.c:3955
msgid "Font smoothing"
msgstr "Suavização da fonte"

#. __ Options - Text:
#: config.c:3890 config.c:3923 config.c:4149 config.c:4188 config.c:4342
#: config.c:4355
msgid "&Default"
msgstr "&Padrão"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3892 config.c:3925 config.c:4147 config.c:4186 config.c:4340
#: config.c:4353 config.c:4440
msgid "&None"
msgstr "&Desat."

#. __ Options - Text:
#: config.c:3894 config.c:3927 config.c:4148 config.c:4187 config.c:4341
#: config.c:4354
msgid "&Partial"
msgstr "&Parcial"

#. __ Options - Text:
#: config.c:3896 config.c:3929 config.c:4150 config.c:4189 config.c:4343
#: config.c:4356
msgid "&Full"
msgstr "&Total"

#: config.c:3983
msgid "&Locale"
msgstr "&Locale"

#: config.c:3986
msgid "&Character set"
msgstr "&Conjunto de caracteres"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3997 config.c:4003
msgid "Emojis"
msgstr ""

#. __ Options - Text - Emojis:
#: config.c:4007
msgid "Style"
msgstr "Estilo"

#. __ Options - Text - Emojis:
#: config.c:4012
msgid "Placement"
msgstr "Localização"

#. __ Options - Keys: treeview label
#: config.c:4020 config.c:4050 config.c:4085 config.c:4103
msgid "Keys"
msgstr "Teclado"

#. __ Options - Keys: panel title
#: config.c:4022
msgid "Keyboard features"
msgstr "Funcionalidades do teclado"

#. __ Options - Keys:
#: config.c:4026
msgid "&Backarrow sends ^H"
msgstr "&Backspace envia ^H"

#. __ Options - Keys:
#: config.c:4031
msgid "&Delete sends DEL"
msgstr "&Delete envia DEL"

#. __ Options - Keys:
#: config.c:4036
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "Ctrl+AltEsq. vira Alt&Gr"

#. __ Options - Keys:
#: config.c:4041
msgid "AltGr is also Alt"
msgstr "AltGr vira também Alt"

#. __ Options - Keys:
#: config.c:4046
msgid "&Esc/Enter reset IME to alphanumeric"
msgstr ""

#. __ Options - Keys: section title
#: config.c:4052
msgid "Shortcuts"
msgstr "Atalhos"

#. __ Options - Keys:
#: config.c:4055
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "C&opiar e colar (Ctrl/Shift+Ins)"

#. __ Options - Keys:
#: config.c:4060
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "&Menu e tela cheia (Alt+Space/Enter)"

#. __ Options - Keys:
#: config.c:4065
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "&Trocar janela (Ctrl+[Shift+]Tab)"

#. __ Options - Keys:
#: config.c:4070
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "&Zoom (Ctrl+mais/menos/zero)"

#. __ Options - Keys:
#: config.c:4075
msgid "&Alt+Fn shortcuts"
msgstr "Atalhos &Alt+Fn"

#. __ Options - Keys:
#: config.c:4080
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "Atalhos &Ctrl+Shift+letra"

#. __ Options - Keys: section title
#: config.c:4087 config.c:4105
msgid "Compose key"
msgstr "Tecla de composição"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:4092 config.c:4253 config.c:4272 config.c:4451 config.c:4470
msgid "&Shift"
msgstr "&Shift"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:4094 config.c:4255 config.c:4280 config.c:4453 config.c:4478
msgid "&Ctrl"
msgstr "Ct&rl"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4096 config.c:4257 config.c:4276 config.c:4455 config.c:4474
msgid "&Alt"
msgstr "A&lt"

#. __ Options - Mouse: treeview label
#: config.c:4112 config.c:4201 config.c:4233
msgid "Mouse"
msgstr "Mouse"

#. __ Options - Mouse: panel title
#: config.c:4114
msgid "Mouse functions"
msgstr "Funções do mouse"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4122 config.c:4155 config.c:4171 config.c:4319
msgid "Cop&y on select"
msgstr "&Copiar ao selecionar"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4127 config.c:4160 config.c:4324 wininput.c:685
msgid "Copy with TABs"
msgstr "Copiar com &TABs"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4132 config.c:4165 config.c:4177 config.c:4329
msgid "Copy as &rich text"
msgstr "Cop. como texto &rico"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4138 config.c:4145 config.c:4184 config.c:4338 config.c:4351
msgid "Copy as &HTML"
msgstr "Copiar como &HTML"

#. __ Options - Mouse:
#: config.c:4197
msgid "Clic&ks place command line cursor"
msgstr "Cliques &posicionam o cursor na linha de com."

#. __ Options - Mouse: section title
#: config.c:4203
msgid "Click actions"
msgstr "Ações de clique"

#. __ Options - Mouse:
#: config.c:4206
msgid "Right mouse button"
msgstr "Botão direito do mouse"

#. __ Options - Mouse:
#: config.c:4209 config.c:4223
msgid "&Paste"
msgstr "C&olar"

#. __ Options - Mouse:
#: config.c:4211 config.c:4225
msgid "E&xtend"
msgstr "E&stender"

#. __ Options - Mouse:
#: config.c:4213
msgid "&Menu"
msgstr "&Menu"

#. __ Options - Mouse:
#: config.c:4215 config.c:4229
msgid "Ente&r"
msgstr "Ente&r"

#. __ Options - Mouse:
#: config.c:4220
msgid "Middle mouse button"
msgstr "Botão do meio do mouse"

#. __ Options - Mouse:
#: config.c:4227
msgid "&Nothing"
msgstr "&Nada"

#. __ Options - Mouse: section title
#: config.c:4235
msgid "Application mouse mode"
msgstr "Modo de mouse de aplicativo"

#. __ Options - Mouse:
#: config.c:4238
msgid "Default click target"
msgstr "Alvo padrão do clique"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4241
msgid "&Window"
msgstr "&Janela"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4243
msgid "&Application"
msgstr "&Aplicativo"

#. __ Options - Mouse:
#: config.c:4250 config.c:4268
msgid "Modifier for overriding default"
msgstr "Modificador para sobrescrever o padrão"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4259 config.c:4284 config.c:4457 config.c:4482
msgid "&Win"
msgstr ""

#. __ Options - Modifier - Super:
#: config.c:4288 config.c:4486
msgid "&Sup"
msgstr ""

#. __ Options - Modifier - Hyper:
#: config.c:4292 config.c:4490
msgid "&Hyp"
msgstr ""

#. __ Options - Selection: treeview label
#: config.c:4302 config.c:4313 config.c:4377
msgid "Selection"
msgstr "Seleção"

#. __ Options - Selection: panel title
#: config.c:4304
msgid "Selection and clipboard"
msgstr "Seleção e área de transferência"

#. __ Options - Selection:
#: config.c:4308
msgid "Clear selection on input"
msgstr "Limpar a seleção na entrada"

#. __ Options - Selection: section title
#: config.c:4315
msgid "Clipboard"
msgstr "Área de transferência"

#. __ Options - Selection:
#: config.c:4365
msgid "Trim space from selection"
msgstr "Aparar espaço da seleção"

#. __ Options - Selection:
#: config.c:4371
msgid "Allow setting selection"
msgstr "Permitir seleção de configuração"

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4379 config.c:4401 config.c:4426 config.c:4499
msgid "Window"
msgstr "Janela"

#. __ Options - Selection: clock position of info popup for text size
#: config.c:4384
msgid "Show size while selecting (0..12)"
msgstr "Mostrar tamanho ao selecionar (0..12)"

#. __ Options - Selection:
#: config.c:4391
msgid "Suspend output while selecting"
msgstr "Suspender saída durante a seleção"

#. __ Options - Window: panel title
#: config.c:4403
msgid "Window properties"
msgstr "Propriedades da janela"

#. __ Options - Window: section title
#: config.c:4405
msgid "Default size"
msgstr "Tamanho padrão"

#. __ Options - Window:
#: config.c:4409
msgid "Colu&mns"
msgstr "Col&unas"

#. __ Options - Window:
#: config.c:4413
msgid "Ro&ws"
msgstr "&Linhas"

#. __ Options - Window:
#: config.c:4417
msgid "C&urrent size"
msgstr "&Tam. atual"

#. __ Options - Window:
#: config.c:4422
msgid "Re&wrap on resize"
msgstr "Reajustar ao redimensionar"

#. __ Options - Window:
#: config.c:4430
msgid "Scroll&back lines"
msgstr "Linhas de &rol."

#. __ Options - Window:
#: config.c:4435
msgid "Scrollbar"
msgstr "Barra de rolagem"

#. __ Options - Window: scrollbar
#: config.c:4438
msgid "&Left"
msgstr "&Esquerda"

#. __ Options - Window: scrollbar
#: config.c:4442
msgid "&Right"
msgstr "&Direita"

#. __ Options - Window:
#: config.c:4448 config.c:4466
msgid "Modifier for scrolling"
msgstr "Modificador para rolagem"

#. __ Options - Window:
#: config.c:4495
msgid "&PgUp and PgDn scroll without modifier"
msgstr "&PgUp e PgDn rolam sem modificador"

#. __ Options - Window: section title
#: config.c:4501
msgid "UI language"
msgstr "Idioma da interface"

#. __ Options - Terminal: treeview label
#: config.c:4511 config.c:4524 config.c:4585 config.c:4599
msgid "Terminal"
msgstr "Terminal"

#. __ Options - Terminal: panel title
#: config.c:4513
msgid "Terminal features"
msgstr "Recursos do terminal"

#. __ Options - Terminal:
#: config.c:4517
msgid "&Type"
msgstr "&Tipo"

#. __ Options - Terminal: answerback string for ^E request
#: config.c:4521
msgid "&Answerback"
msgstr "&Answerback"

#. __ Options - Terminal: section title
#: config.c:4526
msgid "Bell"
msgstr "Aviso"

#. __ Options - Terminal: bell
#: config.c:4533
msgid "► &Play"
msgstr "► T&ocar"

#. __ Options - Terminal: bell
#: config.c:4539
msgid "&Wave"
msgstr "&Wave"

#. __ Options - Terminal: bell
#: config.c:4561 config.c:4574
msgid "&Flash"
msgstr "&Piscar"

#. __ Options - Terminal: bell
#: config.c:4563 config.c:4578
msgid "&Highlight in taskbar"
msgstr "&Destac. na bar. de tar."

#. __ Options - Terminal: bell
#: config.c:4565 config.c:4582
msgid "&Popup"
msgstr "&Saltar"

#. __ Options - Terminal: section title
#: config.c:4587
msgid "Printer"
msgstr "Impressora"

#. __ Options - Terminal:
#: config.c:4602
msgid "Prompt about running processes on &close"
msgstr "Confirmar &fechar com processos ativos"

#. __ Options - Terminal:
#. __ Context menu:
#: config.c:4607 wininput.c:581
msgid "Status Line"
msgstr ""

#: textprint.c:44 textprint.c:127
msgid "[Printing...] "
msgstr "[Imprimindo...] "

#. __ Options - Text: font chooser activation button
#: winctrls.c:935
msgid "&Select..."
msgstr "Selec&..."

#. __ Font chooser: title bar label
#: winctrls.c:1281
msgid "Font "
msgstr "Fonte "

#. __ Font chooser: button
#: winctrls.c:1283
msgid "&Apply"
msgstr "&Aplicar"

#. __ Font chooser:
#: winctrls.c:1285
msgid "&Font:"
msgstr "&Fonte:"

#. __ Font chooser:
#: winctrls.c:1291
msgid "Sample"
msgstr "Exemplo"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1295 winctrls.c:1554 winctrls.c:1719
msgid "Ferqœm’4€"
msgstr "AaBbYyZz"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1312
msgid "Sc&ript:"
msgstr "Sc&ript:"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1314
msgid "<A>Show more fonts</A>"
msgstr "<A>Mostrar mais fontes</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1319
msgid "Colour "
msgstr "Cor "

#. __ Colour chooser:
#: winctrls.c:1332 winctrls.c:1344
msgid "B&asic colours:"
msgstr "Cores &básicas:"

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Custom colours:"
msgstr "Cores &personalizadas:"

#. __ Colour chooser:
#: winctrls.c:1360
msgid "De&fine Custom Colours >>"
msgstr "De&finir cores personalizadas >>"

#. __ Colour chooser:
#: winctrls.c:1363
msgid "Colour"
msgstr "Cor"

#. __ Colour chooser:
#: winctrls.c:1365
msgid "|S&olid"
msgstr "|Só&lida"

#. __ Colour chooser:
#: winctrls.c:1367
msgid "&Hue:"
msgstr "&Hue:"

#. __ Colour chooser:
#: winctrls.c:1370
msgid "&Sat:"
msgstr "&Sat:"

#. __ Colour chooser:
#: winctrls.c:1372
msgid "&Lum:"
msgstr "&Lum:"

#. __ Colour chooser:
#: winctrls.c:1374
msgid "&Red:"
msgstr "&Vermelho:"

#. __ Colour chooser:
#: winctrls.c:1376
msgid "&Green:"
msgstr "V&erde:"

#. __ Colour chooser:
#: winctrls.c:1378
msgid "&Blue:"
msgstr "&Azul:"

#. __ Colour chooser:
#: winctrls.c:1381
msgid "A&dd to Custom Colours"
msgstr "A&dicionar às cores personalizadas"

#. __ Options: dialog title
#: windialog.c:266 windialog.c:839
msgid "Options"
msgstr "Opções"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:268
msgid "available"
msgstr "disponível"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:783
msgid "100"
msgstr "100"

#: windialog.c:924 windialog.c:951
msgid "Error"
msgstr "Erro"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:300
msgid "Session switcher"
msgstr "Alternar sessão"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:320
msgid "Session launcher"
msgstr "Iniciar sessão"

#: wininput.c:429 wininput.c:435
msgid "Ctrl+"
msgstr "Ctrl+"

#: wininput.c:430 wininput.c:436
msgid "Alt+"
msgstr "Alt+"

#: wininput.c:431 wininput.c:437
msgid "Shift+"
msgstr "Shift+"

#. __ System menu:
#: wininput.c:462
msgid "&Restore"
msgstr "&Restaurar"

#. __ System menu:
#: wininput.c:464
msgid "&Move"
msgstr "Mo&ver"

#. __ System menu:
#: wininput.c:466
msgid "&Size"
msgstr "&Tamanho"

#. __ System menu:
#: wininput.c:468
msgid "Mi&nimize"
msgstr "Mi&nimizar"

#. __ System menu:
#: wininput.c:470
msgid "Ma&ximize"
msgstr "Ma&ximizar"

#. __ System menu:
#: wininput.c:472
msgid "&Close"
msgstr "&Fechar"

#. __ System menu:
#: wininput.c:477
msgid "New &Window"
msgstr "Nova &janela"

#. __ System menu:
#: wininput.c:483
msgid "New &Tab"
msgstr "Nova &aba"

#. __ Context menu:
#: wininput.c:490
msgid "&Copy"
msgstr "&Copiar"

#. __ Context menu:
#: wininput.c:509
msgid "&Paste "
msgstr "Co&lar "

#. __ Context menu:
#: wininput.c:514
msgid "Copy → Paste"
msgstr "Copiar → Colar"

#. __ Context menu:
#: wininput.c:519
msgid "S&earch"
msgstr "P&rocurar"

#. __ Context menu:
#: wininput.c:526
msgid "&Log to File"
msgstr "&Registrar em arquivo"

#. __ Context menu:
#: wininput.c:532
msgid "Character &Info"
msgstr "In&formações do caractere"

#. __ Context menu:
#: wininput.c:538
msgid "VT220 Keyboard"
msgstr "Teclado VT220"

#. __ Context menu:
#: wininput.c:543
msgid "&Reset"
msgstr "R&edefinir"

#. __ Context menu:
#: wininput.c:551
msgid "&Default Size"
msgstr "&Tamanho padrão"

#. __ Context menu:
#: wininput.c:561
msgid "Scroll&bar"
msgstr "&Barra de rolagem"

#. __ Context menu:
#: wininput.c:567
msgid "&Full Screen"
msgstr "Tela &cheia"

#. __ Context menu:
#: wininput.c:573
msgid "Flip &Screen"
msgstr "Al&ternar tela"

#. __ System menu:
#: wininput.c:591 wininput.c:763
msgid "Copy &Title"
msgstr "Copiar tít&ulo"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:593 wininput.c:747 wininput.c:765
msgid "&Options..."
msgstr "O&pções..."

#. __ Context menu:
#: wininput.c:678
msgid "Ope&n"
msgstr "A&brir"

#. __ Context menu:
#: wininput.c:683
msgid "Copy as text"
msgstr "Copiar como texto"

#. __ Context menu:
#: wininput.c:687
msgid "Copy as RTF"
msgstr "Copiar como RTF"

#. __ Context menu:
#: wininput.c:689
msgid "Copy as HTML text"
msgstr "Cop. como HTML texto"

#. __ Context menu:
#: wininput.c:691
msgid "Copy as HTML"
msgstr "Cop. como HTML"

#. __ Context menu:
#: wininput.c:693
msgid "Copy as HTML full"
msgstr "Cop. como HTML total"

#. __ Context menu:
#: wininput.c:700
msgid "Select &All"
msgstr "Se&lecionar tudo"

#. __ Context menu:
#: wininput.c:702
msgid "Save as &Image"
msgstr "Salvar como &imagem"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:714
msgid "HTML Screen Dump"
msgstr "Salvar tela como HTML"

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:722
msgid "Clear Scrollback"
msgstr "Limpar tela"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:733
msgid "Send Break"
msgstr "Enviar break"

#. __ Context menu, user commands
#: wininput.c:835
msgid "User commands"
msgstr "Comandos do usuário"

#: wininput.c:1503 wininput.c:1524 wininput.c:1526 wininput.c:1528
#: wininput.c:1565
msgid "[NO SCROLL] "
msgstr ""

#: wininput.c:1516 wininput.c:1525 wininput.c:1530 wininput.c:1586
msgid "[SCROLL MODE] "
msgstr "[MODO de SCROLL] "

#: winmain.c:3845
msgid "Processes are running in session:"
msgstr "Processos rodando na sessão:"

#: winmain.c:3846
msgid "Close anyway?"
msgstr "Fechar mesmo assim?"

#: winmain.c:3870
msgid "Reset terminal?"
msgstr "Redefinir terminal?"

#: winmain.c:4100
msgid "Try '--help' for more information"
msgstr "Tente '--help' para mais informações"

#: winmain.c:4108
msgid "Could not load icon"
msgstr "Não foi possível carregar o ícone"

#: winmain.c:6402
msgid "Usage:"
msgstr "Uso:"

#: winmain.c:6403
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[OPÇÃO]... [ PROGRAMA [ARG]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:6406
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"Inicia uma nova sessão de terminal executando o programa especificado na "
"shell do usuário.\n"
"Se um hífen foi fornecido ao invés de um programa, invoca a shell como uma "
"shell de login.\n"
"\n"
"Opções:\n"
"  -c, --config ARQUIVO     Carrega o arquivo de conf. especificado (cf. -C "
"ou -o ThemeFile)\n"
"  -e, --exec ...        Trata os argumentos seguintes como o comando a ser "
"executado\n"
"  -h, --hold never|start|error|always  Mantém a janela aberta após o comando "
"terminar\n"
"  -p, --position X,Y    Abre a janela nas coordenadas especificadas\n"
"  -p, --position center|left|right|top|bottom  Abre a janela em uma posição "
"especial\n"
"  -p, --position @N     Abre a janela no monitor N\n"
"  -s, --size COLS,LINHAS  Define o tamanho da tela em caracteres (tbm. dá "
"com COLSxLINHAS)\n"
"  -s, --size largmax|altmax  Define o tam. máx. da tela nas dimensões "
"especificadas\n"
"  -t, --title TÍTULO     Define o título da janela (padrão: o comando "
"invocado) (cf. -T)\n"
"  -w, --window normal|min|max|full|hide  Define o estado inicial da janela\n"
"  -i, --icon ARQUIVO[,IND]  Carrega o ícone da janela de um arquivo, opcion. "
"com o índice\n"
"  -l, --log ARQUIVO|-      Registra a saída em um arquivo ou no stdout\n"
"      --nobidi|--nortl  Desativa o bidi (suporte a esquerda-para-direita)\n"
"  -o, --option OPC=VAL  Define/sobrescreve a opção no arq. de config. com o "
"valor dado\n"
"  -B, --Border frame|void  Usar borda fina/nenhuma borda na janela\n"
"  -R, --Report s|o      Informar posição da janela (curta/longa) após a "
"saída\n"
"      --nopin           Impede que essa instância seja afixável na barra de "
"tarefas\n"
"  -D, --daemon          Inicia uma nova instância com tecla de atalho "
"Windows\n"
"  -H, --help            Mostra a ajuda e encerra\n"
"  -V, --version         Mostra informações de versão e encerra\n"
"Veja a página do manual para mais opções de linha de comando e "
"configurações.\n"

#: winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "WSL distribution '%s' not found"
msgstr "Distribuição WSL '%s' não encontrada"

#: winmain.c:6731
msgid "Duplicate option '%s'"
msgstr "Opção repetida: '%s'"

#: winmain.c:6739 winmain.c:6830
msgid "Unknown option '%s'"
msgstr "Opção desconhecida: '%s'"

#: winmain.c:6741
msgid "Option '%s' requires an argument"
msgstr "A opção '%s' precisa de um argumento"

#: winmain.c:6768
msgid "Syntax error in position argument '%s'"
msgstr "Erro de sintaxe no argumento de posição '%s'"

#: winmain.c:6779
msgid "Syntax error in size argument '%s'"
msgstr "Erro de sintaxe no argumento de tamanho '%s'"

#: winmain.c:6939
msgid "Syntax error in geometry argument '%s'"
msgstr "Erro de sintaxe no argumento de geometria '%s'"

#: winmain.c:7038
msgid "Mintty could not detach from caller, starting anyway"
msgstr "O Mintty não pôde se desanexar do chamador, iniciando mesmo assim"

#: winmain.c:7387
msgid "Using default title due to invalid characters in program name"
msgstr "Usando título padrão devido a caracteres inválidos no nome do programa"

#: winsearch.c:232
msgid "◀"
msgstr "◀"

#: winsearch.c:233
msgid "▶"
msgstr "▶"

#: winsearch.c:234
msgid "X"
msgstr "X"

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:165
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "Distância: %d, Negrito: %s, Sublinhado: %s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:167
msgid "font"
msgstr "fonte"

#. __ Options - Text: font properties: value affected by option
#: wintext.c:169
msgid "manual"
msgstr "manual"

#: wintext.c:544
msgid "Font not found, using system substitute"
msgstr "Fonte não encontrada. Usando substituta do sistema."

#: wintext.c:559
msgid "Font has limited support for character ranges"
msgstr "A fonte tem suporte limitado a intervalos de caracteres"

#: wintext.c:702
msgid "Font installation corrupt, using system substitute"
msgstr "Instalação da fonte está corrompida. Usando substituta do sistema."

#: wintext.c:756
msgid "Font does not support system locale"
msgstr "A fonte é incompatível com o idioma do sistema"

#: appinfo.h:64
msgid "There is no warranty, to the extent permitted by law."
msgstr "Não há garantia, na medida permitida por lei."

#. __ %s: WEBSITE (URL)
#: appinfo.h:69
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"Informe bugs ou sugira melhorias no gerenciador de issues na página do "
"projeto do Mintty no endereço\n"
"%s.\n"
"Veja por lá também a Wiki do projeto para maiores dicas, agradecimentos e "
"créditos."
