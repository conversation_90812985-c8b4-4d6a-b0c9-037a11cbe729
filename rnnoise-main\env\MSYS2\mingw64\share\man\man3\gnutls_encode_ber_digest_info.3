.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_encode_ber_digest_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_encode_ber_digest_info \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_encode_ber_digest_info(gnutls_digest_algorithm_t " hash ", const gnutls_datum_t * " digest ", gnutls_datum_t * " output ");"
.SH ARGUMENTS
.IP "gnutls_digest_algorithm_t hash" 12
the hash algorithm that was used to get the digest
.IP "const gnutls_datum_t * digest" 12
must contain the digest data
.IP "gnutls_datum_t * output" 12
will contain the allocated DigestInfo BER encoded data
.SH "DESCRIPTION"
This function will encode the provided digest data, and its
algorithm into an RSA PKCS\fB1\fP 1.5 DigestInfo structure. 
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
