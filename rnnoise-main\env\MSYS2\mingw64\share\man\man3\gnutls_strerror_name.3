.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_strerror_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_strerror_name \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_strerror_name(int " error ");"
.SH ARGUMENTS
.IP "int error" 12
is an error returned by a gnutls function.
.SH "DESCRIPTION"
Return the GnuTLS error code define as a string.  For example,
gnutls_strerror_name (GNUTLS_E_DH_PRIME_UNACCEPTABLE) will return
the string "GNUTLS_E_DH_PRIME_UNACCEPTABLE".
.SH "RETURNS"
A string corresponding to the symbol name of the error
code.
.SH "SINCE"
2.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
