.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_issuer_unique_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_issuer_unique_id \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_issuer_unique_id(gnutls_x509_crt_t " crt ", char * " buf ", size_t * " buf_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
Holds the certificate
.IP "char * buf" 12
user allocated memory buffer, will hold the unique id
.IP "size_t * buf_size" 12
size of user allocated memory buffer (on input), will hold
actual size of the unique ID on return.
.SH "DESCRIPTION"
This function will extract the issuerUniqueID value (if present) for
the given certificate.

If the user allocated memory buffer is not large enough to hold the
full subjectUniqueID, then a GNUTLS_E_SHORT_MEMORY_BUFFER error will be
returned, and buf_size will be set to the actual length.

This function had a bug prior to 3.4.8 that prevented the setting
of \fBNULL\fP  \fIbuf\fP to discover the  \fIbuf_size\fP . To use this function safely
with the older versions the  \fIbuf\fP must be a valid buffer that can hold
at least a single byte if  \fIbuf_size\fP is zero.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
