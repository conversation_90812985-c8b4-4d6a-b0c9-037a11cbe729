<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_CIPHER-CAST</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Algorithm-Names">Algorithm Names</a></li>
      <li><a href="#Parameters">Parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_CIPHER-CAST - The CAST EVP_CIPHER implementations</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for CAST symmetric encryption using the <b>EVP_CIPHER</b> API.</p>

<h2 id="Algorithm-Names">Algorithm Names</h2>

<p>The following algorithms are available in the legacy provider:</p>

<dl>

<dt id="CAST-128-CBC-CAST-192-CBC-and-CAST-256-CBC">&quot;CAST-128-CBC&quot;, &quot;CAST-192-CBC&quot; and &quot;CAST-256-CBC&quot;</dt>
<dd>

</dd>
<dt id="CAST-128-CFB-CAST-192-CFB-CAST-256-CFB">&quot;CAST-128-CFB&quot;, &quot;CAST-192-CFB&quot;, &quot;CAST-256-CFB&quot;</dt>
<dd>

</dd>
<dt id="CAST-128-ECB-CAST-192-ECB-and-CAST-256-ECB">&quot;CAST-128-ECB&quot;, &quot;CAST-192-ECB&quot; and &quot;CAST-256-ECB&quot;</dt>
<dd>

</dd>
<dt id="CAST-192-OFB-CAST-128-OFB-and-CAST-256-OFB">&quot;CAST-192-OFB&quot;, &quot;CAST-128-OFB&quot; and &quot;CAST-256-OFB&quot;</dt>
<dd>

</dd>
</dl>

<h2 id="Parameters">Parameters</h2>

<p>This implementation supports the parameters described in <a href="../man3/EVP_EncryptInit.html">&quot;PARAMETERS&quot; in EVP_EncryptInit(3)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider-cipher.html">provider-cipher(7)</a>, <a href="../man7/OSSL_PROVIDER-legacy.html">OSSL_PROVIDER-legacy(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


