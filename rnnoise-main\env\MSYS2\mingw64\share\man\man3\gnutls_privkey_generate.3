.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_generate" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_generate \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_generate(gnutls_privkey_t " pkey ", gnutls_pk_algorithm_t " algo ", unsigned int " bits ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t pkey" 12
An initialized private key
.IP "gnutls_pk_algorithm_t algo" 12
is one of the algorithms in \fBgnutls_pk_algorithm_t\fP.
.IP "unsigned int bits" 12
the size of the parameters to generate
.IP "unsigned int flags" 12
Must be zero or flags from \fBgnutls_privkey_flags_t\fP.
.SH "DESCRIPTION"
This function will generate a random private key. Note that this
function must be called on an initialized private key.

The flag \fBGNUTLS_PRIVKEY_FLAG_PROVABLE\fP
instructs the key generation process to use algorithms like <PERSON><PERSON>\<PERSON><PERSON>
(from FIPS PUB186\-4) which generate provable parameters out of a seed
for RSA and DSA keys. See \fBgnutls_privkey_generate2()\fP for more
information.

Note that when generating an elliptic curve key, the curve
can be substituted in the place of the bits parameter using the
\fBGNUTLS_CURVE_TO_BITS()\fP macro. The input to the macro is any curve from
\fBgnutls_ecc_curve_t\fP.

For DSA keys, if the subgroup size needs to be specified check
the \fBGNUTLS_SUBGROUP_TO_BITS()\fP macro.

It is recommended to do not set the number of  \fIbits\fP directly, use \fBgnutls_sec_param_to_pk_bits()\fP instead .

See also \fBgnutls_privkey_generate2()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
