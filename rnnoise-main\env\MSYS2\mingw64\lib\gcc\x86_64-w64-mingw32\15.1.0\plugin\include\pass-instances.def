/* This file is auto-generated by gen-pass-instances.awk
   from passes.def.  */
/* Description of pass structure
   Copyright (C) 1987-2025 Free Software Foundation, Inc.

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

/* Note that this file is processed by a simple parser:
   'gen-pass-instances.awk', so carefully verify the generated
   'pass-instances.def' if you deviate from the syntax otherwise used in
   here.  */


 /* All passes needed to lower the function into shape optimizers can
    operate on.  These passes are always run first on the function, but
    backend might produce already lowered functions that are not processed
    by these passes.  */
  INSERT_PASSES_AFTER (all_lowering_passes)
  NEXT_PASS (pass_warn_unused_result, 1);
  NEXT_PASS (pass_diagnose_omp_blocks, 1);
  NEXT_PASS (pass_diagnose_tm_blocks, 1);
  NEXT_PASS (pass_omp_oacc_kernels_decompose, 1);
  NEXT_PASS (pass_lower_omp, 1);
  NEXT_PASS (pass_lower_cf, 1);
  NEXT_PASS (pass_lower_tm, 1);
  NEXT_PASS (pass_refactor_eh, 1);
  NEXT_PASS (pass_lower_eh, 1);
  NEXT_PASS (pass_coroutine_lower_builtins, 1);
  NEXT_PASS (pass_build_cfg, 1);
  NEXT_PASS (pass_warn_function_return, 1);
  NEXT_PASS (pass_coroutine_early_expand_ifns, 1);
  NEXT_PASS (pass_expand_omp, 1);
  NEXT_PASS (pass_build_cgraph_edges, 1);
  TERMINATE_PASS_LIST (all_lowering_passes)

  /* Interprocedural optimization passes.  */
  INSERT_PASSES_AFTER (all_small_ipa_passes)
  NEXT_PASS (pass_ipa_free_lang_data, 1);
  NEXT_PASS (pass_ipa_function_and_variable_visibility, 1);
  NEXT_PASS (pass_ipa_strub_mode, 1);
  NEXT_PASS (pass_build_ssa_passes, 1);
  PUSH_INSERT_PASSES_WITHIN (pass_build_ssa_passes, 1)
      NEXT_PASS (pass_fixup_cfg, 1);
      NEXT_PASS (pass_build_ssa, 1);
      NEXT_PASS_WITH_ARG (pass_walloca, 1, /*strict_mode_p=*/true);
      NEXT_PASS (pass_warn_printf, 1);
      NEXT_PASS (pass_warn_nonnull_compare, 1);
      NEXT_PASS (pass_early_warn_uninitialized, 1);
      NEXT_PASS_WITH_ARG (pass_warn_access, 1, /*early=*/true);
      NEXT_PASS (pass_ubsan, 1);
      NEXT_PASS (pass_nothrow, 1);
      NEXT_PASS (pass_rebuild_cgraph_edges, 1);
  POP_INSERT_PASSES ()

  NEXT_PASS (pass_local_optimization_passes, 1);
  PUSH_INSERT_PASSES_WITHIN (pass_local_optimization_passes, 1)
      NEXT_PASS (pass_fixup_cfg, 2);
      NEXT_PASS (pass_rebuild_cgraph_edges, 2);
      NEXT_PASS (pass_local_fn_summary, 1);
      NEXT_PASS (pass_early_inline, 1);
      NEXT_PASS (pass_warn_recursion, 1);
      NEXT_PASS (pass_all_early_optimizations, 1);
      PUSH_INSERT_PASSES_WITHIN (pass_all_early_optimizations, 1)
	  NEXT_PASS (pass_remove_cgraph_callee_edges, 1);
	  NEXT_PASS (pass_early_object_sizes, 1);
	  /* Don't record nonzero bits before IPA to avoid
	     using too much memory.  */
	  NEXT_PASS_WITH_ARG (pass_ccp, 1, false /* nonzero_p */);
	  /* After CCP we rewrite no longer addressed locals into SSA
	     form if possible.  */
	  NEXT_PASS_WITH_ARG (pass_forwprop, 1, /*last=*/false);
          NEXT_PASS_WITH_ARG (pass_early_thread_jumps, 1, /*first=*/true);
	  NEXT_PASS (pass_sra_early, 1);
	  /* pass_build_ealias is a dummy pass that ensures that we
	     execute TODO_rebuild_alias at this point.  */
	  NEXT_PASS (pass_build_ealias, 1);
	  /* Do phiprop before FRE so we optimize std::min and std::max well.  */
	  NEXT_PASS (pass_phiprop, 1);
	  NEXT_PASS_WITH_ARG (pass_fre, 1, true /* may_iterate */);
	  NEXT_PASS (pass_early_vrp, 1);
	  NEXT_PASS (pass_merge_phi, 1);
          NEXT_PASS (pass_dse, 1);
	  NEXT_PASS_WITH_ARGS (pass_cd_dce, 1, false /* update_address_taken_p */, true /* remove_unused_locals */);
	  NEXT_PASS_WITH_ARG (pass_phiopt, 1, true /* early_p */);
	  NEXT_PASS (pass_tail_recursion, 1);
	  NEXT_PASS (pass_if_to_switch, 1);
	  NEXT_PASS (pass_convert_switch, 1);
	  NEXT_PASS (pass_cleanup_eh, 1);
	  NEXT_PASS (pass_sccopy, 1);
	  NEXT_PASS (pass_profile, 1);
	  NEXT_PASS (pass_local_pure_const, 1);
	  NEXT_PASS (pass_modref, 1);
	  /* Split functions creates parts that are not run through
	     early optimizations again.  It is thus good idea to do this
	      late.  */
	  NEXT_PASS (pass_split_functions, 1);
	  NEXT_PASS_WITH_ARG (pass_strip_predict_hints, 1, true /* early_p */);
      POP_INSERT_PASSES ()
      NEXT_PASS (pass_release_ssa_names, 1);
      NEXT_PASS (pass_rebuild_cgraph_edges, 3);
      NEXT_PASS (pass_local_fn_summary, 2);
  POP_INSERT_PASSES ()

  NEXT_PASS (pass_ipa_remove_symbols, 1);
  NEXT_PASS (pass_ipa_strub, 1);
  NEXT_PASS (pass_ipa_oacc, 1);
  PUSH_INSERT_PASSES_WITHIN (pass_ipa_oacc, 1)
      NEXT_PASS (pass_ipa_pta, 1);
      /* Pass group that runs when the function is an offloaded function
	 containing oacc kernels loops.	 */
      NEXT_PASS (pass_ipa_oacc_kernels, 1);
      PUSH_INSERT_PASSES_WITHIN (pass_ipa_oacc_kernels, 1)
	  NEXT_PASS (pass_oacc_kernels, 1);
	  PUSH_INSERT_PASSES_WITHIN (pass_oacc_kernels, 1)
	      NEXT_PASS (pass_ch, 1);
	      NEXT_PASS_WITH_ARG (pass_fre, 2, true /* may_iterate */);
	      /* We use pass_lim to rewrite in-memory iteration and reduction
		 variable accesses in loops into local variables accesses.  */
	      NEXT_PASS (pass_lim, 1);
	      NEXT_PASS_WITH_ARG (pass_dominator, 1, false /* may_peel_loop_headers_p */);
	      NEXT_PASS (pass_dce, 1);
	      NEXT_PASS_WITH_ARG (pass_parallelize_loops, 1, true /* oacc_kernels_p */);
	      NEXT_PASS (pass_expand_omp_ssa, 1);
	      NEXT_PASS (pass_rebuild_cgraph_edges, 4);
	  POP_INSERT_PASSES ()
      POP_INSERT_PASSES ()
  POP_INSERT_PASSES ()

  NEXT_PASS (pass_target_clone, 1);
  NEXT_PASS (pass_ipa_auto_profile, 1);
  NEXT_PASS (pass_ipa_tree_profile, 1);
  PUSH_INSERT_PASSES_WITHIN (pass_ipa_tree_profile, 1)
      NEXT_PASS (pass_feedback_split_functions, 1);
  POP_INSERT_PASSES ()
  NEXT_PASS_WITH_ARG (pass_ipa_free_fn_summary, 1, true /* small_p */);
  NEXT_PASS (pass_ipa_increase_alignment, 1);
  NEXT_PASS (pass_ipa_tm, 1);
  NEXT_PASS (pass_ipa_lower_emutls, 1);
  TERMINATE_PASS_LIST (all_small_ipa_passes)

  INSERT_PASSES_AFTER (all_regular_ipa_passes)
  NEXT_PASS (pass_analyzer, 1);
  NEXT_PASS (pass_ipa_odr, 1);
  NEXT_PASS (pass_ipa_whole_program_visibility, 1);
  NEXT_PASS (pass_ipa_profile, 1);
  NEXT_PASS (pass_ipa_icf, 1);
  NEXT_PASS (pass_ipa_devirt, 1);
  NEXT_PASS (pass_ipa_cdtor_merge, 1);
  NEXT_PASS (pass_ipa_cp, 1);
  NEXT_PASS (pass_ipa_sra, 1);
  NEXT_PASS (pass_ipa_fn_summary, 1);
  NEXT_PASS (pass_ipa_inline, 1);
  NEXT_PASS (pass_ipa_locality_cloning, 1);
  NEXT_PASS (pass_ipa_pure_const, 1);
  NEXT_PASS (pass_ipa_modref, 1);
  NEXT_PASS_WITH_ARG (pass_ipa_free_fn_summary, 2, false /* small_p */);
  NEXT_PASS (pass_ipa_reference, 1);
  /* This pass needs to be scheduled after any IP code duplication.   */
  NEXT_PASS (pass_ipa_single_use, 1);
  /* Comdat privatization come last, as direct references to comdat local
     symbols are not allowed outside of the comdat group.  Privatizing early
     would result in missed optimizations due to this restriction.  */
  NEXT_PASS (pass_ipa_comdats, 1);
  TERMINATE_PASS_LIST (all_regular_ipa_passes)

  /* Simple IPA passes executed after the regular passes.  In WHOPR mode the
     passes are executed after partitioning and thus see just parts of the
     compiled unit.  */
  INSERT_PASSES_AFTER (all_late_ipa_passes)
  NEXT_PASS (pass_ipa_pta, 2);
  NEXT_PASS (pass_omp_simd_clone, 1);
  TERMINATE_PASS_LIST (all_late_ipa_passes)

  /* These passes are run after IPA passes on every function that is being
     output to the assembler file.  */
  INSERT_PASSES_AFTER (all_passes)
  NEXT_PASS (pass_fixup_cfg, 3);
  NEXT_PASS (pass_lower_eh_dispatch, 1);
  NEXT_PASS (pass_oacc_loop_designation, 1);
  NEXT_PASS (pass_omp_oacc_neuter_broadcast, 1);
  NEXT_PASS (pass_oacc_device_lower, 1);
  NEXT_PASS (pass_omp_device_lower, 1);
  NEXT_PASS (pass_omp_target_link, 1);
  NEXT_PASS (pass_adjust_alignment, 1);
  NEXT_PASS (pass_harden_control_flow_redundancy, 1);
  NEXT_PASS (pass_all_optimizations, 1);
  PUSH_INSERT_PASSES_WITHIN (pass_all_optimizations, 1)
      NEXT_PASS (pass_remove_cgraph_callee_edges, 2);
      /* Initial scalar cleanups before alias computation.
	 They ensure memory accesses are not indirect wherever possible.  */
      NEXT_PASS_WITH_ARG (pass_strip_predict_hints, 2, false /* early_p */);
      NEXT_PASS_WITH_ARG (pass_ccp, 2, true /* nonzero_p */);
      /* After CCP we rewrite no longer addressed locals into SSA
	 form if possible.  */
      NEXT_PASS (pass_object_sizes, 1);
      NEXT_PASS (pass_post_ipa_warn, 1);
      /* Must run before loop unrolling.  */
      NEXT_PASS_WITH_ARG (pass_warn_access, 2, /*early=*/true);
      /* Profile count may overflow as a result of inlinining very large
         loop nests.  This pass should run before any late pass that makes
	 use of profile.  */
      NEXT_PASS (pass_rebuild_frequencies, 1);
      NEXT_PASS (pass_complete_unrolli, 1);
      NEXT_PASS (pass_backprop, 1);
      NEXT_PASS (pass_phiprop, 2);
      NEXT_PASS_WITH_ARG (pass_forwprop, 2, /*last=*/false);
      /* pass_build_alias is a dummy pass that ensures that we
	 execute TODO_rebuild_alias at this point.  */
      NEXT_PASS (pass_build_alias, 1);
      NEXT_PASS (pass_return_slot, 1);
      NEXT_PASS_WITH_ARG (pass_fre, 3, true /* may_iterate */);
      NEXT_PASS (pass_merge_phi, 2);
      NEXT_PASS_WITH_ARG (pass_thread_jumps_full, 1, /*first=*/true);
      NEXT_PASS_WITH_ARG (pass_vrp, 1, false /* final_p*/);
      NEXT_PASS (pass_array_bounds, 1);
      NEXT_PASS (pass_dse, 2);
      NEXT_PASS_WITH_ARGS (pass_dce, 2, false /* update_address_taken_p */, true /* remove_unused_locals */);
      NEXT_PASS (pass_stdarg, 1);
      NEXT_PASS (pass_call_cdce, 1);
      NEXT_PASS (pass_cselim, 1);
      NEXT_PASS (pass_copy_prop, 1);
      NEXT_PASS (pass_tree_ifcombine, 1);
      NEXT_PASS (pass_merge_phi, 3);
      NEXT_PASS_WITH_ARG (pass_phiopt, 2, false /* early_p */);
      NEXT_PASS (pass_tail_recursion, 2);
      NEXT_PASS (pass_ch, 2);
      NEXT_PASS (pass_lower_complex, 1);
      NEXT_PASS (pass_lower_bitint, 1);
      NEXT_PASS (pass_sra, 1);
      /* The dom pass will also resolve all __builtin_constant_p calls
         that are still there to 0.  This has to be done after some
	 propagations have already run, but before some more dead code
	 is removed, and this place fits nicely.  Remember this when
	 trying to move or duplicate pass_dominator somewhere earlier.  */
      NEXT_PASS_WITH_ARG (pass_thread_jumps, 1, /*first=*/true);
      NEXT_PASS_WITH_ARG (pass_dominator, 2, true /* may_peel_loop_headers_p */);
      /* Threading can leave many const/copy propagations in the IL.
	 Clean them up.  Failure to do so well can lead to false
	 positives from warnings for erroneous code.  */
      NEXT_PASS (pass_copy_prop, 2);
      /* Identify paths that should never be executed in a conforming
	 program and isolate those paths.  */
      NEXT_PASS (pass_isolate_erroneous_paths, 1);
      NEXT_PASS_WITH_ARG (pass_reassoc, 1, true /* early_p */);
      NEXT_PASS (pass_dce, 3);
      NEXT_PASS_WITH_ARG (pass_forwprop, 3, /*last=*/false);
      NEXT_PASS_WITH_ARG (pass_phiopt, 3, false /* early_p */);
      NEXT_PASS_WITH_ARG (pass_ccp, 3, true /* nonzero_p */);
      /* After CCP we rewrite no longer addressed locals into SSA
	 form if possible.  */
      NEXT_PASS (pass_expand_pow, 1);
      NEXT_PASS (pass_optimize_bswap, 1);
      NEXT_PASS (pass_laddress, 1);
      NEXT_PASS (pass_lim, 2);
      NEXT_PASS_WITH_ARG (pass_walloca, 2, false);
      NEXT_PASS (pass_pre, 1);
      NEXT_PASS_WITH_ARG (pass_sink_code, 1, false /* unsplit edges */);
      NEXT_PASS (pass_sancov, 1);
      NEXT_PASS (pass_asan, 1);
      NEXT_PASS (pass_tsan, 1);
      NEXT_PASS_WITH_ARG (pass_dse, 3, true /* use DR analysis */);
      NEXT_PASS_WITH_ARGS (pass_dce, 4, false /* update_address_taken_p */, false /* remove_unused_locals */);
      /* Pass group that runs when 1) enabled, 2) there are loops
	 in the function.  Make sure to run pass_fix_loops before
	 to discover/remove loops before running the gate function
	 of pass_tree_loop.  */
      NEXT_PASS (pass_fix_loops, 1);
      NEXT_PASS (pass_tree_loop, 1);
      PUSH_INSERT_PASSES_WITHIN (pass_tree_loop, 1)
          /* Before loop_init we rewrite no longer addressed locals into SSA
	     form if possible.  */
	  NEXT_PASS (pass_tree_loop_init, 1);
	  NEXT_PASS (pass_tree_unswitch, 1);
	  NEXT_PASS (pass_loop_split, 1);
	  NEXT_PASS (pass_scev_cprop, 1);
	  NEXT_PASS (pass_loop_versioning, 1);
	  NEXT_PASS (pass_loop_jam, 1);
	  /* All unswitching, final value replacement and splitting can expose
	     empty loops.  Remove them now.  */
	  NEXT_PASS_WITH_ARG (pass_cd_dce, 2, false /* update_address_taken_p */);
	  NEXT_PASS (pass_iv_canon, 1);
	  NEXT_PASS (pass_loop_distribution, 1);
	  NEXT_PASS (pass_crc_optimization, 1);
	  NEXT_PASS (pass_linterchange, 1);
	  NEXT_PASS (pass_copy_prop, 3);
	  NEXT_PASS (pass_graphite, 1);
	  PUSH_INSERT_PASSES_WITHIN (pass_graphite, 1)
	      NEXT_PASS (pass_graphite_transforms, 1);
	      NEXT_PASS (pass_lim, 3);
	      NEXT_PASS (pass_copy_prop, 4);
	      NEXT_PASS (pass_dce, 5);
	  POP_INSERT_PASSES ()
	  NEXT_PASS_WITH_ARG (pass_parallelize_loops, 2, false /* oacc_kernels_p */);
	  NEXT_PASS (pass_expand_omp_ssa, 2);
	  NEXT_PASS (pass_ch_vect, 1);
	  NEXT_PASS (pass_if_conversion, 1);
	  /* pass_vectorize must immediately follow pass_if_conversion.
	     Please do not add any other passes in between.  */
	  NEXT_PASS (pass_vectorize, 1);
	  PUSH_INSERT_PASSES_WITHIN (pass_vectorize, 1)
	      NEXT_PASS (pass_dce, 6);
	  POP_INSERT_PASSES ()
	  NEXT_PASS (pass_predcom, 1);
	  NEXT_PASS (pass_complete_unroll, 1);
	  NEXT_PASS (pass_pre_slp_scalar_cleanup, 1);
	  PUSH_INSERT_PASSES_WITHIN (pass_pre_slp_scalar_cleanup, 1)
	      NEXT_PASS_WITH_ARG (pass_fre, 4, false /* may_iterate */);
	      NEXT_PASS (pass_dse, 4);
	  POP_INSERT_PASSES ()
	  NEXT_PASS (pass_slp_vectorize, 1);
	  NEXT_PASS (pass_loop_prefetch, 1);
	  /* Run IVOPTs after the last pass that uses data-reference analysis
	     as that doesn't handle TARGET_MEM_REFs.  */
	  NEXT_PASS (pass_iv_optimize, 1);
	  NEXT_PASS (pass_lim, 4);
	  NEXT_PASS (pass_tree_loop_done, 1);
      POP_INSERT_PASSES ()
      /* Pass group that runs when pass_tree_loop is disabled or there
         are no loops in the function.  */
      NEXT_PASS (pass_tree_no_loop, 1);
      PUSH_INSERT_PASSES_WITHIN (pass_tree_no_loop, 1)
	  NEXT_PASS (pass_slp_vectorize, 2);
      POP_INSERT_PASSES ()
      NEXT_PASS (pass_simduid_cleanup, 1);
      NEXT_PASS (pass_lower_vector_ssa, 1);
      NEXT_PASS (pass_lower_switch, 1);
      NEXT_PASS (pass_cse_sincos, 1);
      NEXT_PASS (pass_cse_reciprocals, 1);
      NEXT_PASS_WITH_ARG (pass_reassoc, 2, false /* early_p */);
      NEXT_PASS (pass_strength_reduction, 1);
      NEXT_PASS (pass_split_paths, 1);
      NEXT_PASS (pass_tracer, 1);
      NEXT_PASS_WITH_ARG (pass_fre, 5, false /* may_iterate */);
      /* After late FRE we rewrite no longer addressed locals into SSA
         form if possible.  */
      NEXT_PASS_WITH_ARG (pass_thread_jumps, 2, /*first=*/false);
      NEXT_PASS_WITH_ARG (pass_dominator, 3, false /* may_peel_loop_headers_p */);
      NEXT_PASS (pass_strlen, 1);
      NEXT_PASS_WITH_ARG (pass_thread_jumps_full, 2, /*first=*/false);
      NEXT_PASS_WITH_ARG (pass_vrp, 2, true /* final_p */);
      /* Run CCP to compute alignment and nonzero bits.  */
      NEXT_PASS_WITH_ARG (pass_ccp, 4, true /* nonzero_p */);
      NEXT_PASS (pass_warn_restrict, 1);
      NEXT_PASS (pass_dse, 5);
      NEXT_PASS_WITH_ARGS (pass_dce, 7, true /* update_address_taken_p */, true /* remove_unused_locals */);
      /* After late DCE we rewrite no longer addressed locals into SSA
	 form if possible.  */
      NEXT_PASS_WITH_ARG (pass_forwprop, 4, /*last=*/true);
      NEXT_PASS_WITH_ARG (pass_sink_code, 2, true /* unsplit edges */);
      NEXT_PASS_WITH_ARG (pass_phiopt, 4, false /* early_p */);
      NEXT_PASS (pass_fold_builtins, 1);
      NEXT_PASS (pass_optimize_widening_mul, 1);
      NEXT_PASS (pass_store_merging, 1);
      /* If DCE is not run before checking for uninitialized uses,
	 we may get false warnings (e.g., testsuite/gcc.dg/uninit-5.c).
	 However, this also causes us to misdiagnose cases that should be
	 real warnings (e.g., testsuite/gcc.dg/pr18501.c).  */
      NEXT_PASS_WITH_ARG (pass_cd_dce, 3, false /* update_address_taken_p */);
      NEXT_PASS (pass_sccopy, 2);
      NEXT_PASS (pass_tail_calls, 1);
      /* Split critical edges before late uninit warning to reduce the
         number of false positives from it.  */
      NEXT_PASS (pass_split_crit_edges, 1);
      NEXT_PASS (pass_late_warn_uninitialized, 1);
      NEXT_PASS (pass_local_pure_const, 2);
      NEXT_PASS (pass_modref, 2);
      /* uncprop replaces constants by SSA names.  This makes analysis harder
	 and thus it should be run last.  */
      NEXT_PASS (pass_uncprop, 1);
  POP_INSERT_PASSES ()
  NEXT_PASS (pass_all_optimizations_g, 1);
  PUSH_INSERT_PASSES_WITHIN (pass_all_optimizations_g, 1)
      /* The idea is that with -Og we do not perform any IPA optimization
	 so post-IPA work should be restricted to semantically required
	 passes and all optimization work is done early.  */
      NEXT_PASS (pass_remove_cgraph_callee_edges, 3);
      NEXT_PASS_WITH_ARG (pass_strip_predict_hints, 3, false /* early_p */);
      /* Lower remaining pieces of GIMPLE.  */
      NEXT_PASS (pass_lower_complex, 2);
      NEXT_PASS (pass_lower_bitint, 2);
      NEXT_PASS (pass_lower_vector_ssa, 2);
      NEXT_PASS (pass_lower_switch, 2);
      /* Perform simple scalar cleanup which is constant/copy propagation.  */
      NEXT_PASS_WITH_ARG (pass_ccp, 5, true /* nonzero_p */);
      NEXT_PASS (pass_post_ipa_warn, 2);
      NEXT_PASS (pass_object_sizes, 2);
      /* Fold remaining builtins.  */
      NEXT_PASS (pass_fold_builtins, 2);
      NEXT_PASS (pass_strlen, 2);
      /* Copy propagation also copy-propagates constants, this is necessary
         to forward object-size and builtin folding results properly.  */
      NEXT_PASS (pass_copy_prop, 5);
      NEXT_PASS (pass_dce, 8);
      /* Profile count may overflow as a result of inlinining very large
         loop nests.  This pass should run before any late pass that makes
	 use of profile.  */
      NEXT_PASS (pass_rebuild_frequencies, 2);
      NEXT_PASS (pass_sancov, 2);
      NEXT_PASS (pass_asan, 2);
      NEXT_PASS (pass_tsan, 2);
      /* ???  We do want some kind of loop invariant motion, but we possibly
         need to adjust LIM to be more friendly towards preserving accurate
	 debug information here.  */
      /* Split critical edges before late uninit warning to reduce the
         number of false positives from it.  */
      NEXT_PASS (pass_split_crit_edges, 2);
      NEXT_PASS (pass_late_warn_uninitialized, 2);
      /* uncprop replaces constants by SSA names.  This makes analysis harder
	 and thus it should be run last.  */
      NEXT_PASS (pass_uncprop, 2);
  POP_INSERT_PASSES ()
  NEXT_PASS (pass_assumptions, 1);
  NEXT_PASS (pass_tm_init, 1);
  PUSH_INSERT_PASSES_WITHIN (pass_tm_init, 1)
      NEXT_PASS (pass_tm_mark, 1);
      NEXT_PASS (pass_tm_memopt, 1);
      NEXT_PASS (pass_tm_edges, 1);
  POP_INSERT_PASSES ()
  NEXT_PASS (pass_simduid_cleanup, 2);
  NEXT_PASS (pass_vtable_verify, 1);
  NEXT_PASS (pass_lower_vaarg, 1);
  NEXT_PASS (pass_lower_vector, 1);
  NEXT_PASS (pass_lower_complex_O0, 1);
  NEXT_PASS (pass_lower_bitint_O0, 1);
  NEXT_PASS (pass_sancov_O0, 1);
  NEXT_PASS (pass_lower_switch_O0, 1);
  NEXT_PASS (pass_asan_O0, 1);
  NEXT_PASS (pass_tsan_O0, 1);
  NEXT_PASS (pass_sanopt, 1);
  NEXT_PASS (pass_cleanup_eh, 2);
  NEXT_PASS (pass_musttail, 1);
  NEXT_PASS (pass_lower_resx, 1);
  NEXT_PASS (pass_nrv, 1);
  NEXT_PASS (pass_gimple_isel, 1);
  NEXT_PASS (pass_harden_conditional_branches, 1);
  NEXT_PASS (pass_harden_compares, 1);
  NEXT_PASS_WITH_ARG (pass_warn_access, 3, /*early=*/false);
  NEXT_PASS (pass_cleanup_cfg_post_optimizing, 1);
  NEXT_PASS (pass_warn_function_noreturn, 1);

  NEXT_PASS (pass_expand, 1);

  NEXT_PASS (pass_rest_of_compilation, 1);
  PUSH_INSERT_PASSES_WITHIN (pass_rest_of_compilation, 1)
      NEXT_PASS (pass_instantiate_virtual_regs, 1);
      NEXT_PASS (pass_into_cfg_layout_mode, 1);
      NEXT_PASS (pass_jump, 1);
      NEXT_PASS (pass_lower_subreg, 1);
      NEXT_PASS (pass_df_initialize_opt, 1);
      NEXT_PASS (pass_cse, 1);
      NEXT_PASS (pass_rtl_fwprop, 1);
      NEXT_PASS (pass_rtl_cprop, 1);
      NEXT_PASS (pass_rtl_pre, 1);
      NEXT_PASS (pass_rtl_hoist, 1);
      NEXT_PASS (pass_hardreg_pre, 1);
      NEXT_PASS (pass_rtl_cprop, 2);
      NEXT_PASS (pass_rtl_store_motion, 1);
      NEXT_PASS (pass_cse_after_global_opts, 1);
      NEXT_PASS (pass_rtl_ifcvt, 1);
  NEXT_PASS (pass_apx_nf_convert, 1);
      NEXT_PASS (pass_reginfo_init, 1);
      /* Perform loop optimizations.  It might be better to do them a bit
	 sooner, but we want the profile feedback to work more
	 efficiently.  */
      NEXT_PASS (pass_loop2, 1);
      PUSH_INSERT_PASSES_WITHIN (pass_loop2, 1)
	  NEXT_PASS (pass_rtl_loop_init, 1);
	  NEXT_PASS (pass_rtl_move_loop_invariants, 1);
	  NEXT_PASS (pass_rtl_unroll_loops, 1);
	  NEXT_PASS (pass_rtl_doloop, 1);
	  NEXT_PASS (pass_rtl_loop_done, 1);
      POP_INSERT_PASSES ()
      NEXT_PASS (pass_lower_subreg2, 1);
      NEXT_PASS (pass_web, 1);
      NEXT_PASS (pass_rtl_cprop, 3);
  NEXT_PASS_WITH_ARG (pass_stv, 1, true /* timode_p */);
      NEXT_PASS (pass_cse2, 1);
      NEXT_PASS (pass_rtl_dse1, 1);
      NEXT_PASS (pass_rtl_fwprop_addr, 1);
      NEXT_PASS (pass_inc_dec, 1);
      NEXT_PASS (pass_initialize_regs, 1);
      NEXT_PASS (pass_ud_rtl_dce, 1);
      NEXT_PASS (pass_ext_dce, 1);
      NEXT_PASS (pass_combine, 1);
      NEXT_PASS (pass_late_combine, 1);
  NEXT_PASS (pass_remove_partial_avx_dependency, 1);
  NEXT_PASS_WITH_ARG (pass_stv, 2, false /* timode_p */);
      NEXT_PASS (pass_if_after_combine, 1);
      NEXT_PASS (pass_jump_after_combine, 1);
      NEXT_PASS (pass_partition_blocks, 1);
      NEXT_PASS (pass_outof_cfg_layout_mode, 1);
      NEXT_PASS (pass_split_all_insns, 1);
      NEXT_PASS (pass_lower_subreg3, 1);
      NEXT_PASS (pass_df_initialize_no_opt, 1);
      NEXT_PASS (pass_stack_ptr_mod, 1);
      NEXT_PASS (pass_mode_switching, 1);
      NEXT_PASS (pass_match_asm_constraints, 1);
      NEXT_PASS (pass_sms, 1);
      NEXT_PASS (pass_live_range_shrinkage, 1);
      NEXT_PASS (pass_sched, 1);
      NEXT_PASS (pass_rtl_avoid_store_forwarding, 1);
      NEXT_PASS (pass_early_remat, 1);
      NEXT_PASS (pass_ira, 1);
      NEXT_PASS (pass_reload, 1);
      /* In the following, some passes are tied to 'pass_postreload' and others
	 to 'pass_late_compilation'.  The difference is that the latter also
	 run for 'targetm.no_register_allocation' targets.  */
      NEXT_PASS (pass_postreload, 1);
      PUSH_INSERT_PASSES_WITHIN (pass_postreload, 1)
	  NEXT_PASS (pass_postreload_cse, 1);
  NEXT_PASS (pass_insert_vzeroupper, 1);
	  NEXT_PASS (pass_late_combine, 2);
	  NEXT_PASS (pass_gcse2, 1);
	  NEXT_PASS (pass_split_after_reload, 1);
	  NEXT_PASS (pass_ree, 1);
	  NEXT_PASS (pass_compare_elim_after_reload, 1);
	  NEXT_PASS (pass_thread_prologue_and_epilogue, 1);
	  NEXT_PASS (pass_rtl_dse2, 1);
	  NEXT_PASS (pass_stack_adjustments, 1);
	  NEXT_PASS (pass_jump2, 1);
	  NEXT_PASS (pass_duplicate_computed_gotos, 1);
	  NEXT_PASS (pass_sched_fusion, 1);
	  NEXT_PASS (pass_peephole2, 1);
	  NEXT_PASS (pass_if_after_reload, 1);
	  NEXT_PASS (pass_regrename, 1);
	  NEXT_PASS (pass_fold_mem_offsets, 1);
	  NEXT_PASS (pass_cprop_hardreg, 1);
	  NEXT_PASS (pass_fast_rtl_dce, 1);
	  NEXT_PASS (pass_reorder_blocks, 1);
	  NEXT_PASS (pass_leaf_regs, 1);
	  NEXT_PASS (pass_split_before_sched2, 1);
	  NEXT_PASS (pass_sched2, 1);
	  NEXT_PASS (pass_stack_regs, 1);
	  PUSH_INSERT_PASSES_WITHIN (pass_stack_regs, 1)
	      NEXT_PASS (pass_split_before_regstack, 1);
	      NEXT_PASS (pass_stack_regs_run, 1);
	  POP_INSERT_PASSES ()
      POP_INSERT_PASSES ()
      NEXT_PASS (pass_late_thread_prologue_and_epilogue, 1);
      /* No target-independent code motion is allowed beyond this point,
         excepting the legacy delayed-branch pass.  */
      NEXT_PASS (pass_late_compilation, 1);
      PUSH_INSERT_PASSES_WITHIN (pass_late_compilation, 1)
	  NEXT_PASS (pass_zero_call_used_regs, 1);
	  NEXT_PASS (pass_compute_alignments, 1);
	  NEXT_PASS (pass_variable_tracking, 1);
	  NEXT_PASS (pass_free_cfg, 1);
	  NEXT_PASS (pass_machine_reorg, 1);
	  NEXT_PASS (pass_cleanup_barriers, 1);
	  NEXT_PASS (pass_delay_slots, 1);
	  NEXT_PASS (pass_split_for_shorten_branches, 1);
	  NEXT_PASS (pass_convert_to_eh_region_ranges, 1);
  NEXT_PASS (pass_insert_endbr_and_patchable_area, 1);
  NEXT_PASS (pass_align_tight_loops, 1);
	  NEXT_PASS (pass_shorten_branches, 1);
	  NEXT_PASS (pass_set_nothrow_function_flags, 1);
	  NEXT_PASS (pass_dwarf2_frame, 1);
	  NEXT_PASS (pass_final, 1);
      POP_INSERT_PASSES ()
      NEXT_PASS (pass_df_finish, 1);
  POP_INSERT_PASSES ()
  NEXT_PASS (pass_clean_state, 1);
  TERMINATE_PASS_LIST (all_passes)
/* Description of target passes for IA-32 
   Copyright (C) 2016-2025 Free Software Foundation, Inc.

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

/*
   Macros that can be used in this file:
 */

  /* Run the 64-bit STV pass before the CSE pass so that CONST0_RTX and
     CONSTM1_RTX generated by the STV pass can be CSEed.  */

  /* pass_align_tight_loops must be after pass_insert_endbr_and_patchable_area.
     PR116174.  */

#undef INSERT_PASSES_AFTER
#undef PUSH_INSERT_PASSES_WITHIN
#undef POP_INSERT_PASSES
#undef NEXT_PASS
#undef NEXT_PASS_WITH_ARG
#undef NEXT_PASS_WITH_ARGS
#undef TERMINATE_PASS_LIST
