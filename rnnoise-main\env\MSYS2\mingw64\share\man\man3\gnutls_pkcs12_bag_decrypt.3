.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs12_bag_decrypt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs12_bag_decrypt \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs12.h>
.sp
.BI "int gnutls_pkcs12_bag_decrypt(gnutls_pkcs12_bag_t " bag ", const char * " pass ");"
.SH ARGUMENTS
.IP "gnutls_pkcs12_bag_t bag" 12
The bag
.IP "const char * pass" 12
The password used for encryption, must be ASCII.
.SH "DESCRIPTION"
This function will decrypt the given encrypted bag and return 0 on
success.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
