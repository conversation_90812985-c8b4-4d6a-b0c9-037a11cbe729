------------------------------------------------------------------------
-- log10.decTest -- decimal logarithm in base 10                      --
-- Copyright (c) IBM Corporation, 2005, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- This emphasises the testing of notable cases, as they will often
-- have unusual paths (especially the 10**n results).

extended:    1
precision:   16
rounding:    half_even
maxExponent: 384
minexponent: -383

-- examples in specification
precision:   9
logxs000 log10  0                 -> -Infinity
logxs001 log10  0.001             -> -3
logxs002 log10  1                 ->  0
logxs003 log10  2                 ->  0.301029996         Inexact Rounded
logxs004 log10  10                ->  1
logxs005 log10  70                ->  1.84509804          Inexact Rounded
logxs006 log10 +Infinity          ->  Infinity


-- basics (examples in specification, etc.)
precision:   16
logx0000 log10  0                 -> -Infinity
logx0001 log10  7E-1000           -> -999.1549019599857   Inexact Rounded
logx0002 log10  1.1E-9            -> -8.958607314841775   Inexact Rounded
logx0003 log10  0.0007            -> -3.154901959985743   Inexact Rounded
logx0004 log10  0.11              -> -0.9586073148417750  Inexact Rounded
logx0005 log10  0.7               -> -0.1549019599857432  Inexact Rounded
logx0006 log10  1                 ->  0
logx0007 log10  1.5               ->  0.1760912590556812  Inexact Rounded
logx0008 log10  2                 ->  0.3010299956639812  Inexact Rounded
logx0009 log10  2.718281828459045 ->  0.4342944819032518  Inexact Rounded
logx0010 log10  2.718281828459046 ->  0.4342944819032519  Inexact Rounded
logx0011 log10  2.718281828459047 ->  0.4342944819032521  Inexact Rounded
logx0012 log10  7                 ->  0.8450980400142568  Inexact Rounded
logx0013 log10  10                ->  1
logx0014 log10  10.5              ->  1.021189299069938   Inexact Rounded
logx0015 log10  11                ->  1.041392685158225   Inexact Rounded
logx0016 log10  70                ->  1.845098040014257   Inexact Rounded
logx0017 log10  9999              ->  3.999956568380192   Inexact Rounded
logx0018 log10  1.21E6            ->  6.082785370316450   Inexact Rounded
logx0019 log10  1.1E+9            ->  9.041392685158225   Inexact Rounded
logx0020 log10  7E+1000           ->  1000.845098040014   Inexact Rounded
logx0021 log10 +Infinity          ->  Infinity

-- notable cases
-- negatives
logx0031 log10 -1E-9              -> NaN Invalid_operation
logx0032 log10 -0.0007            -> NaN Invalid_operation
logx0033 log10 -0.1               -> NaN Invalid_operation
logx0034 log10 -0.7               -> NaN Invalid_operation
logx0035 log10 -1                 -> NaN Invalid_operation
logx0036 log10 -1.5               -> NaN Invalid_operation
logx0037 log10 -2                 -> NaN Invalid_operation
logx0038 log10 -10.5              -> NaN Invalid_operation
logx0039 log10 -10.5              -> NaN Invalid_operation
logx0040 log10 -9999              -> NaN Invalid_operation
logx0041 log10 -10                -> NaN Invalid_operation
logx0042 log10 -0                 -> -Infinity
logx0043 log10 -0E+17             -> -Infinity
logx0044 log10 -0E-17             -> -Infinity
-- other zeros
logx0051 log10  0                 -> -Infinity
logx0052 log10  0E+17             -> -Infinity
logx0053 log10  0E-17             -> -Infinity
-- infinities
logx0055 log10 -Infinity          -> NaN Invalid_operation
logx0056 log10 +Infinity          -> Infinity
-- ones
logx0061 log10  1                 ->   0
logx0062 log10  1.0               ->   0
logx0063 log10  1.000000000000000 ->   0
logx0064 log10  1.000000000000000000 ->   0

-- notable cases -- exact powers of 10
logx1100 log10 1             -> 0
logx1101 log10 10            -> 1
logx1102 log10 100           -> 2
logx1103 log10 1000          -> 3
logx1104 log10 10000         -> 4
logx1105 log10 100000        -> 5
logx1106 log10 1000000       -> 6
logx1107 log10 10000000      -> 7
logx1108 log10 100000000     -> 8
logx1109 log10 1000000000    -> 9
logx1110 log10 10000000000   -> 10
logx1111 log10 100000000000  -> 11
logx1112 log10 1000000000000 -> 12
logx1113 log10 0.00000000001 -> -11
logx1114 log10 0.0000000001 -> -10
logx1115 log10 0.000000001 -> -9
logx1116 log10 0.00000001 -> -8
logx1117 log10 0.0000001 -> -7
logx1118 log10 0.000001 -> -6
logx1119 log10 0.00001 -> -5
logx1120 log10 0.0001 -> -4
logx1121 log10 0.001 -> -3
logx1122 log10 0.01 -> -2
logx1123 log10 0.1 -> -1
logx1124 log10 1E-99  -> -99
logx1125 log10 1E-100 -> -100
logx1126 log10 1E-383 -> -383

-- check normally exact cases round properly
precision: 1
logx1141 log10 10000000000   -> 1E+1         Rounded
logx1142 log10 1000000000000 -> 1E+1 Inexact Rounded
logx1143 log10 1E+100        -> 1E+2         Rounded
logx1144 log10 1E+123        -> 1E+2 Inexact Rounded
logx1145 log10 1E+126        -> 1E+2 Inexact Rounded
logx1146 log10 1E+916        -> 9E+2 Inexact Rounded
logx1147 log10 1E+999        -> 1E+3 Inexact Rounded

precision: 2
logx1151 log10 10000000000   -> 10
logx1152 log10 1000000000000 -> 12
logx1153 log10 1E+100        -> 1.0E+2         Rounded
logx1154 log10 1E+123        -> 1.2E+2 Inexact Rounded
logx1155 log10 1E+126        -> 1.3E+2 Inexact Rounded
logx1156 log10 1E+916        -> 9.2E+2 Inexact Rounded
logx1157 log10 1E+999        -> 1.0E+3 Inexact Rounded
-- some half-way point rounds, other cases, and negatives
logx1158 log10 1E+125        -> 1.2E+2 Inexact Rounded
logx1159 log10 1E+135        -> 1.4E+2 Inexact Rounded
logx1160 log10 1E+129        -> 1.3E+2 Inexact Rounded
logx1161 log10 1E+131        -> 1.3E+2 Inexact Rounded
logx1162 log10 1E-123        -> -1.2E+2 Inexact Rounded
logx1163 log10 1E-126        -> -1.3E+2 Inexact Rounded
logx1164 log10 1E-916        -> -9.2E+2 Inexact Rounded
logx1165 log10 1E-999        -> -1.0E+3 Inexact Rounded
logx1166 log10 1E-125        -> -1.2E+2 Inexact Rounded
logx1167 log10 1E-135        -> -1.4E+2 Inexact Rounded
logx1168 log10 1E-129        -> -1.3E+2 Inexact Rounded
logx1169 log10 1E-131        -> -1.3E+2 Inexact Rounded

precision: 3
logx1171 log10 10000000000   -> 10
logx1172 log10 1000000000000 -> 12
logx1173 log10 1E+100        -> 100
logx1174 log10 1E+123        -> 123
logx1175 log10 1E+126        -> 126
logx1176 log10 1E+916        -> 916
logx1177 log10 1E+999        -> 999

-- log10(2) .. tests both ln(2) and ln(10) constants, too
precision: 50
logx1201 log10 2     -> 0.30102999566398119521373889472449302676818988146211 Inexact Rounded
logx1202 log10 2.000 -> 0.30102999566398119521373889472449302676818988146211 Inexact Rounded
logx1203 log10 0.2E1 -> 0.30102999566398119521373889472449302676818988146211 Inexact Rounded
precision: 49
logx1204 log10 2 -> 0.3010299956639811952137388947244930267681898814621 Inexact Rounded
precision: 48
logx1205 log10 2 -> 0.301029995663981195213738894724493026768189881462  Inexact Rounded
precision: 47
logx1206 log10 2 -> 0.30102999566398119521373889472449302676818988146   Inexact Rounded
precision: 46
logx1207 log10 2 -> 0.3010299956639811952137388947244930267681898815    Inexact Rounded
precision: 45
logx1208 log10 2 -> 0.301029995663981195213738894724493026768189881     Inexact Rounded
precision: 44
logx1209 log10 2 -> 0.30102999566398119521373889472449302676818988      Inexact Rounded
precision: 43
logx1210 log10 2 -> 0.3010299956639811952137388947244930267681899       Inexact Rounded
precision: 42
logx1211 log10 2 -> 0.301029995663981195213738894724493026768190        Inexact Rounded
precision: 41
logx1212 log10 2 -> 0.30102999566398119521373889472449302676819         Inexact Rounded
precision: 40
logx1213 log10 2 -> 0.3010299956639811952137388947244930267682          Inexact Rounded
precision: 39
logx1214 log10 2 -> 0.301029995663981195213738894724493026768           Inexact Rounded
precision: 38
logx1215 log10 2 -> 0.30102999566398119521373889472449302677            Inexact Rounded
precision: 37
logx1216 log10 2 -> 0.3010299956639811952137388947244930268             Inexact Rounded
precision: 36
logx1217 log10 2 -> 0.301029995663981195213738894724493027              Inexact Rounded
precision: 35
logx1218 log10 2 -> 0.30102999566398119521373889472449303               Inexact Rounded
precision: 34
logx1219 log10 2 -> 0.3010299956639811952137388947244930                Inexact Rounded
precision: 33
logx1220 log10 2 -> 0.301029995663981195213738894724493                 Inexact Rounded
precision: 32
logx1221 log10 2 -> 0.30102999566398119521373889472449                  Inexact Rounded
precision: 31
logx1222 log10 2 -> 0.3010299956639811952137388947245                   Inexact Rounded
precision: 30
logx1223 log10 2 -> 0.301029995663981195213738894724                    Inexact Rounded
precision: 29
logx1224 log10 2 -> 0.30102999566398119521373889472                     Inexact Rounded
precision: 28
logx1225 log10 2 -> 0.3010299956639811952137388947                      Inexact Rounded
precision: 27
logx1226 log10 2 -> 0.301029995663981195213738895                       Inexact Rounded
precision: 26
logx1227 log10 2 -> 0.30102999566398119521373889                        Inexact Rounded
precision: 25
logx1228 log10 2 -> 0.3010299956639811952137389                         Inexact Rounded
precision: 24
logx1229 log10 2 -> 0.301029995663981195213739                          Inexact Rounded
precision: 23
logx1230 log10 2 -> 0.30102999566398119521374                           Inexact Rounded
precision: 22
logx1231 log10 2 -> 0.3010299956639811952137                            Inexact Rounded
precision: 21
logx1232 log10 2 -> 0.301029995663981195214                             Inexact Rounded
precision: 20
logx1233 log10 2 -> 0.30102999566398119521                              Inexact Rounded
precision: 19
logx1234 log10 2 -> 0.3010299956639811952                               Inexact Rounded
precision: 18
logx1235 log10 2 -> 0.301029995663981195                                Inexact Rounded
precision: 17
logx1236 log10 2 -> 0.30102999566398120                                 Inexact Rounded
precision: 16
logx1237 log10 2 -> 0.3010299956639812                                  Inexact Rounded
precision: 15
logx1238 log10 2 -> 0.301029995663981                                   Inexact Rounded
precision: 14
logx1239 log10 2 -> 0.30102999566398                                    Inexact Rounded
precision: 13
logx1240 log10 2 -> 0.3010299956640                                     Inexact Rounded
precision: 12
logx1241 log10 2 -> 0.301029995664                                      Inexact Rounded
precision: 11
logx1242 log10 2 -> 0.30102999566                                       Inexact Rounded
precision: 10
logx1243 log10 2 -> 0.3010299957                                        Inexact Rounded
precision:  9
logx1244 log10 2 -> 0.301029996                                         Inexact Rounded
precision:  8
logx1245 log10 2 -> 0.30103000                                          Inexact Rounded
precision:  7
logx1246 log10 2 -> 0.3010300                                           Inexact Rounded
precision:  6
logx1247 log10 2 -> 0.301030                                            Inexact Rounded
precision:  5
logx1248 log10 2 -> 0.30103                                             Inexact Rounded
precision:  4
logx1249 log10 2 -> 0.3010                                              Inexact Rounded
precision:  3
logx1250 log10 2 -> 0.301                                               Inexact Rounded
precision:  2
logx1251 log10 2 -> 0.30                                                Inexact Rounded
precision:  1
logx1252 log10 2 -> 0.3                                                 Inexact Rounded

maxExponent: 384
minExponent: -383
precision:   16
rounding:    half_even

-- More close-to-e, etc., tests
precision:   34
logx1301 log10 2.718281828459045235360287471352661  -> 0.4342944819032518276511289189166048 Inexact Rounded
logx1302 log10 2.718281828459045235360287471352662  -> 0.4342944819032518276511289189166050 Inexact Rounded
logx1303 log10 2.718281828459045235360287471352663  -> 0.4342944819032518276511289189166052 Inexact Rounded
logx1304 log10 0.99999999999999999999999999999999   -> -4.342944819032518276511289189166073E-33 Inexact Rounded
logx1305 log10 0.999999999999999999999999999999999  -> -4.342944819032518276511289189166053E-34 Inexact Rounded
logx1306 log10 0.9999999999999999999999999999999999 -> -4.342944819032518276511289189166051E-35 Inexact Rounded
logx1307 log10 1.000000000000000000000000000000000  -> 0
logx1308 log10 1.0000000000000000000000000000000001 -> 4.342944819032518276511289189166051E-35 Inexact Rounded
logx1309 log10 1.000000000000000000000000000000001  -> 4.342944819032518276511289189166049E-34 Inexact Rounded
logx1310 log10 1.00000000000000000000000000000001   -> 4.342944819032518276511289189166029E-33 Inexact Rounded
-- lower p
precision:    7
logx1320 log10 0.999999    -> -4.342947E-7  Inexact Rounded
logx1321 log10 0.9999999   -> -4.342945E-8  Inexact Rounded
logx1322 log10 0.99999999  -> -4.342945E-9  Inexact Rounded
logx1323 log10 0.999999999 -> -4.342945E-10 Inexact Rounded
logx1324 log10 1.00000000  ->  0
logx1325 log10 1.00000001  ->  4.342945E-9  Inexact Rounded
logx1326 log10 1.0000001   ->  4.342945E-8  Inexact Rounded
logx1327 log10 1.000001    ->  4.342943E-7  Inexact Rounded

-- near 10^3
precision:   9
logx1331 log10  999.9999998  -> 3.00000000 Inexact Rounded
logx1332 log10  999.9999999  -> 3.00000000 Inexact Rounded
logx1333 log10 1000.000000   -> 3
logx1334 log10 1000.000001   -> 3.00000000 Inexact Rounded
logx1335 log10 1000.000002   -> 3.00000000 Inexact Rounded
precision: 16
logx1341 log10  999.9999998  -> 2.999999999913141 Inexact Rounded
logx1342 log10  999.9999999  -> 2.999999999956571 Inexact Rounded
logx1343 log10 1000.000000   -> 3
logx1344 log10 1000.000001   -> 3.000000000434294 Inexact Rounded
logx1345 log10 1000.000002   -> 3.000000000868589 Inexact Rounded

-- suggestions from Ilan Nehama
logx1400 log10 10E-3    -> -2
logx1401 log10 10E-2    -> -1
logx1402 log10 100E-2   ->  0
logx1403 log10 1000E-2  ->  1
logx1404 log10 10000E-2 ->  2
logx1405 log10 10E-1    ->  0
logx1406 log10 100E-1   ->  1
logx1407 log10 1000E-1  ->  2
logx1408 log10 10000E-1 ->  3
logx1409 log10 10E0     ->  1
logx1410 log10 100E0    ->  2
logx1411 log10 1000E0   ->  3
logx1412 log10 10000E0  ->  4
logx1413 log10 10E1     ->  2
logx1414 log10 100E1    ->  3
logx1415 log10 1000E1   ->  4
logx1416 log10 10000E1  ->  5
logx1417 log10 10E2     ->  3
logx1418 log10 100E2    ->  4
logx1419 log10 1000E2   ->  5
logx1420 log10 10000E2  ->  6

-- Randoms
-- P=50, within 0-9999
Precision: 50
logx2501 log10 0.00035448001667968141775891246991912655961163345904 ->  -3.4504082425411775290864053318247274944685586188505 Inexact Rounded
logx2502 log10 70.636455726424311228255338637935330826995136597644  ->   1.8490288998408492045793070255302335558140975719247 Inexact Rounded
logx2503 log10 0.00000000000000233550362473821889060812804063040169 -> -14.631619454343834858023578299142866557717904223667 Inexact Rounded
logx2504 log10 97.783628621523244679901260358286898958832135433764  ->   1.9902661493224219517897657964362571690592734407330 Inexact Rounded
logx2505 log10 0062.2377135315858392802612812022807838599572017342  ->   1.7940536293085066199287632725026837018486533544141 Inexact Rounded
logx2506 log10 6.3767634652071053619977602804724129652981747879532  ->   0.80460030789825961615100163576080761326857374098644 Inexact Rounded
logx2507 log10 63.297088981313278529306533814195068850532666658798  ->   1.8013837373724427092417170149098614410849353839673 Inexact Rounded
logx2508 log10 0.00000077239693316881797717820110898167721602299187 ->  -6.1121594592718550613773886241951966264826760310047 Inexact Rounded
logx2509 log10 0.00000003953580359780185534830572461922527831395002 ->  -7.4030094293833847136252547069905477213541787177561 Inexact Rounded
logx2510 log10 754.62905817369989169188998111527272688791544577204  ->   2.8777335243761300047758534304371912099958057545416 Inexact Rounded
logx2511 log10 0.00000048360378410241428936607147056283282849158312 ->  -6.3155103095309353457604038397980091650760346334512 Inexact Rounded
logx2512 log10 0.00007509037583645612577196104591672080542932166089 ->  -4.1244157219700166314012344705538088030592896111026 Inexact Rounded
logx2513 log10 0.00000000000705475944638915053419839063567898092064 -> -11.151517790256466048553810002525868198178167950377 Inexact Rounded
logx2514 log10 9.6210300460497657917445410947099633479609165120661  ->   0.98322157093260978206633922877716078683518617768411 Inexact Rounded
logx2515 log10 0.00000000050150361386555527496607245976120864985611 ->  -9.2997259330798261040411086835563234390934934629340 Inexact Rounded
logx2516 log10 098.24754029731994125797723545333677604490074810751  ->   1.9923216862874337077795278629351060819105679670633 Inexact Rounded
logx2517 log10 7.5091998150046994320441463854301624742491015752980  ->   0.87559366078005924080766469158763499725414024128781 Inexact Rounded
logx2518 log10 0.00000000000079540571273330075193668596942268542425 -> -12.099411294165176028817305108475326325006250936963 Inexact Rounded
logx2519 log10 0.00000042395034799555215782907515074134154915491701 ->  -6.3726850039125381134069450802108893075604464135297 Inexact Rounded
logx2520 log10 56.683376304674355481905023145238799909301732694982  ->   1.7534557107853480435703421826077606250636580091754 Inexact Rounded
logx2521 log10 48.734033811444195070807606721517169810438049581227  ->   1.6878323602741065190942654710049433808208291564049 Inexact Rounded
logx2522 log10 0.00074830310930046865009851706989430228561880221063 ->  -3.1259224502209974082223667712016445572431791920618 Inexact Rounded
logx2523 log10 36.677348885111593384020836720396262497122708598359  ->   1.5643979364260796086754530282302605477567469395425 Inexact Rounded
logx2524 log10 0.00000000000000004495678560480432858812419145833744 -> -16.347204748239740510014320630363244015916029619561 Inexact Rounded
logx2525 log10 9509.5854013650642799374159131940108748594774307104  ->   3.9781615829916326741100166519726824430945406302661 Inexact Rounded
logx2526 log10 0.07834891268689177014044454793608715276615743819097 ->  -1.1059670262197643147805517398621288897669876996348 Inexact Rounded
logx2527 log10 0.00000029584529880706128444454688454999032801904794 ->  -6.5289353275814043710076526920566721570375026917206 Inexact Rounded
logx2528 log10 3.0713496544497618098794332787772186176981011904294  ->   0.48732926103896828546424341029492468100431414072994 Inexact Rounded
logx2529 log10 352.66392670788816474407442785460803833927136413943  ->   2.5473610388199562714709836398243933320284077008314 Inexact Rounded
logx2530 log10 0.00304743125181876267210516527361742185617091801650 ->  -2.5160660830163981967774124745311497447050056400207 Inexact Rounded
logx2531 log10 0.00000076120535894952136499250364604538117729437183 ->  -6.1184981629047051532448413863950776496652483019415 Inexact Rounded
logx2532 log10 769.88795978534353052965286195053735007473187735815  ->   2.8864275277862652709986498581064117950288798222100 Inexact Rounded
logx2533 log10 0.00000000000000041297494808612226304619570016336188 -> -15.384076292745415917510668454361868659468669804710 Inexact Rounded
logx2534 log10 860.88864595714426940247940960258558876903741966974  ->   2.9349469800554277915920278090647283233440859155176 Inexact Rounded
logx2535 log10 5839.0328812994787235900178587371051096898683972444  ->   3.7663409208972392569269125539438874737147906238543 Inexact Rounded
logx2536 log10 0.00000028532710151284840471670497112821201598377841 ->  -6.5446569753514027675878879843238065488490618159490 Inexact Rounded
logx2537 log10 0.00000000000000009734490059931638483445631835651581 -> -16.011686794011271135978633880864278692254243106931 Inexact Rounded
logx2538 log10 5.8610949526439529489252302463450302981511714144330  ->   0.76797875722452549281028552067645732490929361952278 Inexact Rounded
logx2539 log10 6.6282432221115923372151148990137179611977576327206  ->   0.82139843639227213211012044000785757267155736071361 Inexact Rounded
logx2540 log10 0.00000000001994071862386846626954819923923344413454 -> -10.700259194632339980266559224447212260115021637626 Inexact Rounded

-- P=34, within 0-9999
Precision: 34
logx2201 log10 1.522513203889714179088327328864183  -> 0.1825610677098896250496651330492109 Inexact Rounded
logx2202 log10 0.171123774769717316154080888930404  -> -0.7666896483548462582461898092764408 Inexact Rounded
logx2203 log10 0.0000000997467236251714283104963838 -> -7.001101360652518274271569010312115 Inexact Rounded
logx2204 log10 0.0008856103624122479769647543468633 -> -3.052757310476070891830490327138190 Inexact Rounded
logx2205 log10 1.938274868738032930709498221236758  -> 0.2874153648259449520201536171714594 Inexact Rounded
logx2206 log10 479.5667847823826713082613445010097  -> 2.680849095850361068709165157286435 Inexact Rounded
logx2207 log10 8856.136599178820202141823157336804  -> 3.947244306584767101480454261950559 Inexact Rounded
logx2208 log10 0.0000911026318801903982642871344858 -> -4.040469076434979398438617464033826 Inexact Rounded
logx2209 log10 0.0000000000017271112650427414732630 -> -11.76267968314038748995178212654921 Inexact Rounded
logx2210 log10 6.962605370078885647639503548229695  -> 0.8427717807200322352686396925992250 Inexact Rounded
logx2211 log10 0.3354804428992793132855923541692781 -> -0.4743327923012159170967636070844834 Inexact Rounded
logx2212 log10 2.079864257474859008252165836663504  -> 0.3180349916198059046812506741388856 Inexact Rounded
logx2213 log10 2805.479529292939499220276986621988  -> 3.448007104139974344565978780624744 Inexact Rounded
logx2214 log10 66.45731133034187374557028537213949  -> 1.822542767005644041661520936223086 Inexact Rounded
logx2215 log10 0.0000001206521261762681738274822835 -> -6.918465020390216969561494755767318 Inexact Rounded
logx2216 log10 0.0000000001884891916264401160472381 -> -9.724713548119065386091933007528633 Inexact Rounded
logx2217 log10 0.0000015467279551726326581314582759 -> -5.810586065070435383755759514608738 Inexact Rounded
logx2218 log10 0.0090776316728068586744633914135952 -> -2.042027442843745884503280954390114 Inexact Rounded
logx2219 log10 0.0000000000024541106528713393740030 -> -11.61010585935635713090119156069479 Inexact Rounded
logx2220 log10 14.12936879385863410081087750645856  -> 1.150122760895466989841057385742662 Inexact Rounded
logx2221 log10 0.0000036912481831392922922647231392 -> -5.432826753789892283556211380824203 Inexact Rounded
logx2222 log10 0.0000000004067477525420424270138734 -> -9.390674838050073122857868012475060 Inexact Rounded
logx2223 log10 7080.122562705399744969319589806194  -> 3.850040775747103318724330047546916 Inexact Rounded
logx2224 log10 261.3491411363679209175524790255725  -> 2.417221077227536319655699517530855 Inexact Rounded
logx2225 log10 003.9945581449915240094728380041494  -> 0.6014687471531988260823066997845691 Inexact Rounded
logx2226 log10 0.0000000000583549164588495206767840 -> -10.23392254834182677023231713519341 Inexact Rounded
logx2227 log10 9567.961832607240278342761088487484  -> 3.980819434211107631569386147016368 Inexact Rounded
logx2228 log10 06.26592979160342972777219828867033  -> 0.7969855243966221408595024012574729 Inexact Rounded
logx2229 log10 0.0000000000589847046598067273287319 -> -10.22926059078206218717755253582907 Inexact Rounded
logx2230 log10 567.9388648235589204769442863724997  -> 2.754301589058313576472380262907638 Inexact Rounded
logx2231 log10 039.7790325480037778918162264883415  -> 1.599654216592019199639285308997886 Inexact Rounded
logx2232 log10 0.0000000005123951921894162149817207 -> -9.290394953898862694847327137242690 Inexact Rounded
logx2233 log10 0.0000000000038500999723636904276723 -> -11.41452799337924056186867324854691 Inexact Rounded
logx2234 log10 0.0006726500658977759825616537935864 -> -3.172210810922768725687671849421792 Inexact Rounded
logx2235 log10 260.2400250475967528429943779126507  -> 2.415374092073799204236801383070064 Inexact Rounded
logx2236 log10 0.0000000006101942339385102585042548 -> -9.214531900562046557191261226632509 Inexact Rounded
logx2237 log10 0.0000000010846867501382746760066557 -> -8.964695664883282406359874242387236 Inexact Rounded
logx2238 log10 60.24078375568814769010333711509928  -> 1.779890613567084253168373266648922 Inexact Rounded
logx2239 log10 0.0012058738711757669337600252986093 -> -2.918698115012605915753728220896010 Inexact Rounded
logx2240 log10 230.9450930197841600611503095185600  -> 2.363508739056822846742942599628966 Inexact Rounded

-- P=16, within 0-999
Precision: 16
logx2101 log10 0.0072067119605184 -> -2.142262835573038 Inexact Rounded
logx2102 log10 503.6828482226624  -> 2.702157162195652 Inexact Rounded
logx2103 log10 64.96074447821815  -> 1.812650993464174 Inexact Rounded
logx2104 log10 48.75408597467246  -> 1.688011018842600 Inexact Rounded
logx2105 log10 0.0329009839269587 -> -1.482791113975280 Inexact Rounded
logx2106 log10 223.5320415060633  -> 2.349339784523410 Inexact Rounded
logx2107 log10 73.12765002292194  -> 1.864081617476268 Inexact Rounded
logx2108 log10 487.3749378358509  -> 2.687863192802252 Inexact Rounded
logx2109 log10 0.0000019671987621 -> -5.706151757557926 Inexact Rounded
logx2110 log10 0.0570680660609784 -> -1.243606844697873 Inexact Rounded
logx2111 log10 33.10311638788998  -> 1.519868880976773 Inexact Rounded
logx2112 log10 0.0687382699187077 -> -1.162801402868185 Inexact Rounded
logx2113 log10 258.9416193626484  -> 2.413201859654145 Inexact Rounded
logx2114 log10 0.0005306100136736 -> -3.275224558269725 Inexact Rounded
logx2115 log10 65.78490393408572  -> 1.818126244825109 Inexact Rounded
logx2116 log10 504.2328842073510  -> 2.702631165346958 Inexact Rounded
logx2117 log10 9.417432755815027  -> 0.9739325278524503 Inexact Rounded
logx2118 log10 006.7054835355498  -> 0.8264301004947640 Inexact Rounded
logx2119 log10 0.0917012272363915 -> -1.037624852133399 Inexact Rounded
logx2120 log10 5.959404385244921  -> 0.7752028561953401 Inexact Rounded
logx2121 log10 0.0001209759148486 -> -3.917301084968903 Inexact Rounded
logx2122 log10 0.0004706112139838 -> -3.327337728428039 Inexact Rounded
logx2123 log10 0.0069700457377046 -> -2.156764372035771 Inexact Rounded
logx2124 log10 0.5155584569852619 -> -0.2877220847805025 Inexact Rounded
logx2125 log10 88.06005885607414  -> 1.944778971389913 Inexact Rounded
logx2126 log10 0.0448240038219866 -> -1.348489353509709 Inexact Rounded
logx2127 log10 3.419622484059565  -> 0.5339781639101145 Inexact Rounded
logx2128 log10 5.171123353858721  -> 0.7135848977142854 Inexact Rounded
logx2129 log10 0.0002133188319807 -> -3.670970802945872 Inexact Rounded
logx2130 log10 46.21086703136966  -> 1.664744117045149 Inexact Rounded
logx2131 log10 0.0000631053714415 -> -4.199933672639880 Inexact Rounded
logx2132 log10 78.66019196870698  -> 1.895755001962469 Inexact Rounded
logx2133 log10 0.0007152278351188 -> -3.145555592082297 Inexact Rounded
logx2134 log10 45.52509819928536  -> 1.658250891256892 Inexact Rounded
logx2135 log10 0.0000703227795740 -> -4.152903971697183 Inexact Rounded
logx2136 log10 26.24438641426669  -> 1.419036423550599 Inexact Rounded
logx2137 log10 0.0000044654829535 -> -5.350131564166817 Inexact Rounded
logx2138 log10 0.7360702733062529 -> -0.1330807211893611 Inexact Rounded
logx2139 log10 8.417059176469655  -> 0.9251603805112778 Inexact Rounded
logx2140 log10 0.0002926570767968 -> -3.533640969664818 Inexact Rounded

-- P=7, within 0-99
Precision: 7
logx2001 log10 57.26089  -> 1.757858 Inexact Rounded
logx2002 log10 0.0575421 -> -1.240014 Inexact Rounded
logx2003 log10 0.5918465 -> -0.2277909 Inexact Rounded
logx2004 log10 0.0068776 -> -2.162563 Inexact Rounded
logx2005 log10 0.0066833 -> -2.175009 Inexact Rounded
logx2006 log10 9.926963  -> 0.9968164 Inexact Rounded
logx2007 log10 0.0041852 -> -2.378284 Inexact Rounded
logx2008 log10 84.15412  -> 1.925075 Inexact Rounded
logx2009 log10 2.466856  -> 0.3921438 Inexact Rounded
logx2010 log10 0.0058047 -> -2.236220 Inexact Rounded
logx2011 log10 9.885154  -> 0.9949834 Inexact Rounded
logx2012 log10 0.6667654 -> -0.1760269 Inexact Rounded
logx2013 log10 34.65736  -> 1.539795 Inexact Rounded
logx2014 log10 0.0026884 -> -2.570506 Inexact Rounded
logx2015 log10 0.0432767 -> -1.363746 Inexact Rounded
logx2016 log10 66.01407  -> 1.819637 Inexact Rounded
logx2017 log10 0.0070572 -> -2.151368 Inexact Rounded
logx2018 log10 0.0731613 -> -1.135719 Inexact Rounded
logx2019 log10 9.838983  -> 0.9929502 Inexact Rounded
logx2020 log10 15.89696  -> 1.201314 Inexact Rounded
logx2021 log10 8.459247  -> 0.9273317 Inexact Rounded
logx2022 log10 0.0010873 -> -2.963651 Inexact Rounded
logx2023 log10 0.6498619 -> -0.1871789 Inexact Rounded
logx2024 log10 0.0847008 -> -1.072112 Inexact Rounded
logx2025 log10 0.0075489 -> -2.122116 Inexact Rounded
logx2026 log10 51.11152  -> 1.708519 Inexact Rounded
logx2027 log10 0.7233866 -> -0.1406295 Inexact Rounded
logx2028 log10 2.254721  -> 0.3530928 Inexact Rounded
logx2029 log10 6.568444  -> 0.8174625 Inexact Rounded
logx2030 log10 83.72639  -> 1.922862 Inexact Rounded
logx2031 log10 6.720585  -> 0.8274071 Inexact Rounded
logx2032 log10 87.90366  -> 1.944007 Inexact Rounded
logx2033 log10 0.0433324 -> -1.363187 Inexact Rounded
logx2034 log10 34.63912  -> 1.539567 Inexact Rounded
logx2035 log10 0.8089059 -> -0.09210200 Inexact Rounded
logx2036 log10 7.793405  -> 0.8917272 Inexact Rounded
logx2037 log10 0.0041757 -> -2.379271 Inexact Rounded
logx2038 log10 7.135417  -> 0.8534194 Inexact Rounded
logx2039 log10 12.49570  -> 1.096761 Inexact Rounded
logx2040 log10 6.356276  -> 0.8032027 Inexact Rounded

--------
maxExponent: 384
minExponent: -383
precision:   16
rounding:    half_even

-- special values
logx820  log10   Infinity ->   Infinity
logx821  log10   0        ->  -Infinity
logx822  log10   NaN      ->   NaN
logx823  log10   sNaN     ->   NaN     Invalid_operation
-- propagating NaNs
logx824  log10   sNaN123  ->   NaN123  Invalid_operation
logx825  log10   -sNaN321 ->  -NaN321  Invalid_operation
logx826  log10   NaN456   ->   NaN456
logx827  log10   -NaN654  ->  -NaN654
logx828  log10   NaN1     ->   NaN1


-- Invalid operations due to restrictions
-- [next two probably skipped by most test harnesses]
precision: 100000000
logx901  log10 1 ->  NaN            Invalid_context
precision:  99999999
logx902  log10 0 ->  NaN            Invalid_context

precision: 9
maxExponent:   1000000
minExponent:   -999999
logx903  log10 1   ->  NaN            Invalid_context
maxExponent:    999999
minExponent:   -999999
logx904  log10 0 ->  -Infinity
maxExponent:    999999
minExponent:  -1000000
logx905  log10 1   ->  NaN            Invalid_context
maxExponent:    999999
minExponent:   -999998
logx906  log10 0 ->  -Infinity

-- Null test
logx900  log10 #   -> NaN Invalid_operation


