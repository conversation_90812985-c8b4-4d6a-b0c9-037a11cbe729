.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_policy" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_policy \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_policy(gnutls_x509_crt_t " crt ", const struct gnutls_x509_policy_st * " policy ", unsigned int " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "const struct gnutls_x509_policy_st * policy" 12
A pointer to a policy
.IP "unsigned int critical" 12
use non\-zero if the extension is marked as critical
.SH "DESCRIPTION"
This function will set the certificate policy extension (*********).
Multiple calls to this function append a new policy.

Note the maximum text size for the qualifier \fBGNUTLS_X509_QUALIFIER_NOTICE\fP
is 200 characters. This function will fail with \fBGNUTLS_E_INVALID_REQUEST\fP
if this is exceeded.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
