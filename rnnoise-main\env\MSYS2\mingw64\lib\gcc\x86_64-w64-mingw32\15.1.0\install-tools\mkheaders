#!/bin/sh

# Copyright (C) 2002, 2007, 2009 Free Software Foundation, Inc.

#This file is part of GCC.

#GCC is free software; you can redistribute it and/or modify it under
#the terms of the GNU General Public License as published by the Free
#Software Foundation; either version 3, or (at your option) any later
#version.

#GCC is distributed in the hope that it will be useful, but WITHOUT
#ANY WARRANTY; without even the implied warranty of MERCHA<PERSON>ABILITY or
#FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
#for more details.

#You should have received a copy of the GNU General Public License
#along with GCC; see the file COPYING3.  If not see
#<http://www.gnu.org/licenses/>.

# Basic information
target=x86_64-w64-mingw32
target_noncanonical=x86_64-w64-mingw32
version=15.1.0

VERBOSE=0
while [ x$1 = x-v ] ; do
    shift
    VERBOSE=`expr $VERBOSE + 1`
done
export VERBOSE

if [ x$1 = x--help ] ; then
    echo "Usage: mkheaders [options] [prefix [isysroot]]"
    echo "Options:"
    echo "  -v        Print more output (may be repeated for even more output)"
    echo "  --help    This help"
    echo "  --version Print version information"
    exit 0
fi

if [ x$1 = x--version ] ; then
    echo "mkheaders (GCC) version $version"
    echo "Copyright 2002, 2007, 2009 Free Software Foundation, Inc."
    echo "This program is free software; you may redistribute it under the"
    echo "terms of the GNU General Public License.  This program has"
    echo "absolutely no warranty."
    exit 0
fi

# Common prefix for installation directories.
if [ x$1 != x ] ; then
  prefix=$1
  shift
else
  prefix=/mingw64
fi

# Allow for alternate isysroot in which to find headers
if [ x$1 != x ] ; then
  isysroot=$1
  shift
else
  isysroot=
fi

# Directory in which to put host dependent programs and libraries
exec_prefix=${prefix}
# Directory in which to put the directories used by the compiler.
libdir=${exec_prefix}/lib
libexecdir=/mingw64/lib
# Directory in which the compiler finds libraries, etc.
libsubdir=${libdir}/gcc/${target_noncanonical}/${version}
# Directory in which the compiler finds executables
libexecsubdir=${libexecdir}/gcc/${target_noncanonical}/${version}

itoolsdir=${libexecsubdir}/install-tools
itoolsdatadir=${libsubdir}/install-tools
incdir=${libsubdir}/include-fixed
mkinstalldirs="${itoolsdir}/mkinstalldirs"

cd ${itoolsdir}
rm -rf ${incdir}/*

for shell in $CONFIG_SHELL $SHELL /bin/sh /bin/sh ""; do
  if { test -x $shell || test -x $shell.exe; } \
  && $shell $mkinstalldirs > /dev/null 2>&1; then
    mkinstalldirs="$shell $mkinstalldirs"
    break
  elif test x$shell = x; then
    if $mkinstalldirs > /dev/null 2>&1; then
      break
    elif test ! -f $mkinstalldirs; then
      echo mkheaders: could not find $mkinstalldirs >&2
      exit 1
    else
      echo mkheaders: please rerun with CONFIG_SHELL set to a working Bourne shell >&2
      exit 1
    fi
  fi
done

for ml in `cat ${itoolsdatadir}/fixinc_list`; do
  sysroot_headers_suffix=`echo ${ml} | sed -e 's/;.*$//'`
  multi_dir=`echo ${ml} | sed -e 's/^[^;]*;//'`
  subincdir=${incdir}${multi_dir}
  ${mkinstalldirs} ${subincdir}
  . ${itoolsdatadir}/mkheaders.conf
  if [ x${STMP_FIXINC} != x ] ; then
	TARGET_MACHINE="${target}" target_canonical="${target}" \
	    MACRO_LIST="${itoolsdatadir}/macro_list" \
	    $shell ./fixinc.sh ${subincdir} \
	    ${isysroot}${SYSTEM_HEADER_DIR} ${OTHER_FIXINCLUDES_DIRS}
	rm -f ${subincdir}/syslimits.h
	if [ -f ${subincdir}/limits.h ]; then
	  mv ${subincdir}/limits.h ${subincdir}/syslimits.h
	else
	  cp ${itoolsdatadir}/gsyslimits.h ${subincdir}/syslimits.h
	fi
  fi

  cp ${itoolsdatadir}/include${multi_dir}/limits.h ${subincdir}/limits.h
done
