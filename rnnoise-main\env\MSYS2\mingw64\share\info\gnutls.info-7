This is gnutls.info, produced by makeinfo version 7.1 from gnutls.texi.

This manual is last updated 28 January 2025 for version 3.8.9 of GnuTLS.

Copyright © 2001-2025 Free Software Foundation, Inc.\\ Copyright ©
2001-2025 <PERSON><PERSON>

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.3 or any later version published by the Free Software
     Foundation; with no Invariant Sections, no Front-Cover Texts, and
     no Back-Cover Texts.  A copy of the license is included in the
     section entitled "GNU Free Documentation License".
INFO-DIR-SECTION Software libraries
START-INFO-DIR-ENTRY
* GnuTLS: (gnutls).		GNU Transport Layer Security Library.
END-INFO-DIR-ENTRY

INFO-DIR-SECTION System Administration
START-INFO-DIR-ENTRY
* certtool: (gnutls)certtool Invocation.	Manipulate certificates and keys.
* gnutls-serv: (gnutls)gnutls-serv Invocation.	GnuTLS test server.
* gnutls-cli: (gnutls)gnutls-cli Invocation.	GnuTLS test client.
* gnutls-cli-debug: (gnutls)gnutls-cli-debug Invocation.	GnuTLS debug client.
* psktool: (gnutls)psktool Invocation.	Simple TLS-Pre-Shared-Keys manager.
* srptool: (gnutls)srptool Invocation.	Simple SRP password tool.
END-INFO-DIR-ENTRY


File: gnutls.info,  Node: Bibliography,  Next: Function and Data Index,  Prev: Copying Information,  Up: Top

Bibliography
************

[CBCATT]
     Bodo Moeller, "Security of CBC Ciphersuites in SSL/TLS: Problems
     and Countermeasures", 2002, available from
     <https://www.openssl.org/~bodo/tls-cbc.txt>.

[GPGH]
     Mike Ashley, "The GNU Privacy Handbook", 2002, available from
     <https://www.gnupg.org/gph/en/manual.pdf>.

[GUTPKI]
     Peter Gutmann, "Everything you never wanted to know about PKI but
     were forced to find out", Available from
     <https://www.cs.auckland.ac.nz/~pgut001/>.

[PRNGATTACKS]
     John Kelsey and Bruce Schneier, "Cryptanalytic Attacks on
     Pseudorandom Number Generators", Available from
     <https://www.schneier.com/academic/paperfiles/paper-prngs.pdf>.

[KEYPIN]
     Chris Evans and Chris Palmer, "Public Key Pinning Extension for
     HTTP", Available from
     <https://tools.ietf.org/html/draft-ietf-websec-key-pinning-01>.

[NISTSP80057]
     NIST Special Publication 800-57, "Recommendation for Key Management
     - Part 1: General (Revised)", March 2007, available from
     <https://csrc.nist.gov/publications/nistpubs/800-57/sp800-57-Part1-revised2_Mar08-2007.pdf>.

[RFC7413]
     Y. Cheng and J. Chu and S. Radhakrishnan and A. Jain, "TCP Fast
     Open", December 2014, Available from
     <https://www.ietf.org/rfc/rfc7413.txt>.

[RFC7918]
     A. Langley, N. Modadugu, B. Moeller, "Transport Layer Security
     (TLS) False Start", August 2016, Available from
     <https://www.ietf.org/rfc/rfc7918.txt>.

[RFC6125]
     Peter Saint-Andre and Jeff Hodges, "Representation and Verification
     of Domain-Based Application Service Identity within Internet Public
     Key Infrastructure Using X.509 (PKIX) Certificates in the Context
     of Transport Layer Security (TLS)", March 2011, Available from
     <https://www.ietf.org/rfc/rfc6125.txt>.

[RFC7685]
     Adam Langley, "A Transport Layer Security (TLS) ClientHello Padding
     Extension", October 2015, Available from
     <https://www.ietf.org/rfc/rfc7685.txt>.

[RFC7613]
     Peter Saint-Andre and Alexey Melnikov, "Preparation, Enforcement,
     and Comparison of Internationalized Strings Representing Usernames
     and Passwords", August 2015, Available from
     <https://www.ietf.org/rfc/rfc7613.txt>.

[RFC2246]
     Tim Dierks and Christopher Allen, "The TLS Protocol Version 1.0",
     January 1999, Available from
     <https://www.ietf.org/rfc/rfc2246.txt>.

[RFC6083]
     M. Tuexen and R. Seggelmann and E. Rescorla, "Datagram Transport
     Layer Security (DTLS) for Stream Control Transmission Protocol
     (SCTP)", January 2011, Available from
     <https://www.ietf.org/rfc/rfc6083.txt>.

[RFC4418]
     Ted Krovetz, "UMAC: Message Authentication Code using Universal
     Hashing", March 2006, Available from
     <https://www.ietf.org/rfc/rfc4418.txt>.

[RFC4680]
     S. Santesson, "TLS Handshake Message for Supplemental Data",
     September 2006, Available from
     <https://www.ietf.org/rfc/rfc4680.txt>.

[RFC7633]
     P. Hallam-Baker, "X.509v3 Transport Layer Security (TLS) Feature
     Extension", October 2015, Available from
     <https://www.ietf.org/rfc/rfc7633.txt>.

[RFC7919]
     D. Gillmor, "Negotiated Finite Field Diffie-Hellman Ephemeral
     Parameters for Transport Layer Security (TLS)", August 2016,
     Available from <https://www.ietf.org/rfc/rfc7919.txt>.

[RFC4514]
     Kurt D. Zeilenga, "Lightweight Directory Access Protocol (LDAP):
     String Representation of Distinguished Names", June 2006, Available
     from <https://www.ietf.org/rfc/rfc4513.txt>.

[RFC4346]
     Tim Dierks and Eric Rescorla, "The TLS Protocol Version 1.1", Match
     2006, Available from <https://www.ietf.org/rfc/rfc4346.txt>.

[RFC4347]
     Eric Rescorla and Nagendra Modadugu, "Datagram Transport Layer
     Security", April 2006, Available from
     <https://www.ietf.org/rfc/rfc4347.txt>.

[RFC5246]
     Tim Dierks and Eric Rescorla, "The TLS Protocol Version 1.2",
     August 2008, Available from <https://www.ietf.org/rfc/rfc5246.txt>.

[RFC2440]
     Jon Callas, Lutz Donnerhacke, Hal Finney and Rodney Thayer,
     "OpenPGP Message Format", November 1998, Available from
     <https://www.ietf.org/rfc/rfc2440.txt>.

[RFC4880]
     Jon Callas, Lutz Donnerhacke, Hal Finney, David Shaw and Rodney
     Thayer, "OpenPGP Message Format", November 2007, Available from
     <https://www.ietf.org/rfc/rfc4880.txt>.

[RFC4211]
     J. Schaad, "Internet X.509 Public Key Infrastructure Certificate
     Request Message Format (CRMF)", September 2005, Available from
     <https://www.ietf.org/rfc/rfc4211.txt>.

[RFC2817]
     Rohit Khare and Scott Lawrence, "Upgrading to TLS Within HTTP/1.1",
     May 2000, Available from <https://www.ietf.org/rfc/rfc2817.txt>

[RFC2818]
     Eric Rescorla, "HTTP Over TLS", May 2000, Available from
     <https://www.ietf/rfc/rfc2818.txt>.

[RFC2945]
     Tom Wu, "The SRP Authentication and Key Exchange System", September
     2000, Available from <https://www.ietf.org/rfc/rfc2945.txt>.

[RFC7301]
     S. Friedl, A. Popov, A. Langley, E. Stephan, "Transport Layer
     Security (TLS) Application-Layer Protocol Negotiation Extension",
     July 2014, Available from <https://www.ietf.org/rfc/rfc7301.txt>.

[RFC2986]
     Magnus Nystrom and Burt Kaliski, "PKCS 10 v1.7: Certification
     Request Syntax Specification", November 2000, Available from
     <https://www.ietf.org/rfc/rfc2986.txt>.

[PKIX]
     D. Cooper, S. Santesson, S. Farrel, S. Boeyen, R. Housley, W. Polk,
     "Internet X.509 Public Key Infrastructure Certificate and
     Certificate Revocation List (CRL) Profile", May 2008, available
     from <https://www.ietf.org/rfc/rfc5280.txt>.

[RFC3749]
     Scott Hollenbeck, "Transport Layer Security Protocol Compression
     Methods", May 2004, available from
     <https://www.ietf.org/rfc/rfc3749.txt>.

[RFC3820]
     Steven Tuecke, Von Welch, Doug Engert, Laura Pearlman, and Mary
     Thompson, "Internet X.509 Public Key Infrastructure (PKI) Proxy
     Certificate Profile", June 2004, available from
     <https://www.ietf.org/rfc/rfc3820>.

[RFC6520]
     R. Seggelmann, M. Tuexen, and M. Williams, "Transport Layer
     Security (TLS) and Datagram Transport Layer Security (DTLS)
     Heartbeat Extension", February 2012, available from
     <https://www.ietf.org/rfc/rfc6520>.

[RFC5746]
     E. Rescorla, M. Ray, S. Dispensa, and N. Oskov, "Transport Layer
     Security (TLS) Renegotiation Indication Extension", February 2010,
     available from <https://www.ietf.org/rfc/rfc5746>.

[RFC5280]
     D. Cooper, S. Santesson, S. Farrell, S. Boeyen, R. Housley, and W.
     Polk, "Internet X.509 Public Key Infrastructure Certificate and
     Certificate Revocation List (CRL) Profile", May 2008, available
     from <https://www.ietf.org/rfc/rfc5280>.

[TLSTKT]
     Joseph Salowey, Hao Zhou, Pasi Eronen, Hannes Tschofenig,
     "Transport Layer Security (TLS) Session Resumption without
     Server-Side State", January 2008, available from
     <https://www.ietf.org/rfc/rfc5077>.

[PKCS12]
     RSA Laboratories, "PKCS 12 v1.0: Personal Information Exchange
     Syntax", June 1999, Available from <https://www.rsa.com>.

[PKCS11]
     RSA Laboratories, "PKCS #11 Base Functionality v2.30: Cryptoki –
     Draft 4", July 2009, Available from <https://www.rsa.com>.

[RESCORLA]
     Eric Rescorla, "SSL and TLS: Designing and Building Secure
     Systems", 2001

[SELKEY]
     Arjen Lenstra and Eric Verheul, "Selecting Cryptographic Key
     Sizes", 2003, available from
     <https://www.win.tue.nl/~klenstra/key.pdf>.

[SSL3]
     Alan Freier, Philip Karlton and Paul Kocher, "The Secure Sockets
     Layer (SSL) Protocol Version 3.0", August 2011, Available from
     <https://www.ietf.org/rfc/rfc6101.txt>.

[STEVENS]
     Richard Stevens, "UNIX Network Programming, Volume 1", Prentice
     Hall PTR, January 1998

[TLSEXT]
     Simon Blake-Wilson, Magnus Nystrom, David Hopwood, Jan Mikkelsen
     and Tim Wright, "Transport Layer Security (TLS) Extensions", June
     2003, Available from <https://www.ietf.org/rfc/rfc3546.txt>.

[TLSPGP]
     Nikos Mavrogiannopoulos, "Using OpenPGP keys for TLS
     authentication", January 2011.  Available from
     <https://www.ietf.org/rfc/rfc6091.txt>.

[TLSSRP]
     David Taylor, Trevor Perrin, Tom Wu and Nikos Mavrogiannopoulos,
     "Using SRP for TLS Authentication", November 2007.  Available from
     <https://www.ietf.org/rfc/rfc5054.txt>.

[TLSPSK]
     Pasi Eronen and Hannes Tschofenig, "Pre-shared key Ciphersuites for
     TLS", December 2005, Available from
     <https://www.ietf.org/rfc/rfc4279.txt>.

[TOMSRP]
     Tom Wu, "The Stanford SRP Authentication Project", Available at
     <https://srp.stanford.edu/>.

[WEGER]
     Arjen Lenstra and Xiaoyun Wang and Benne de Weger, "Colliding X.509
     Certificates", Cryptology ePrint Archive, Report 2005/067,
     Available at <https://eprint.iacr.org/>.

[ECRYPT]
     European Network of Excellence in Cryptology II, "ECRYPT II Yearly
     Report on Algorithms and Keysizes (2009-2010)", Available at
     <https://www.ecrypt.eu.org/documents/D.SPA.13.pdf>.

[RFC5056]
     N. Williams, "On the Use of Channel Bindings to Secure Channels",
     November 2007, available from <https://www.ietf.org/rfc/rfc5056>.

[RFC5764]
     D. McGrew, E. Rescorla, "Datagram Transport Layer Security (DTLS)
     Extension to Establish Keys for the Secure Real-time Transport
     Protocol (SRTP)On the Use of Channel Bindings to Secure Channels",
     May 2010, available from <https://www.ietf.org/rfc/rfc5764>.

[RFC5929]
     J. Altman, N. Williams, L. Zhu, "Channel Bindings for TLS", July
     2010, available from <https://www.ietf.org/rfc/rfc5929>.

[PKCS11URI]
     J. Pechanec, D. Moffat, "The PKCS#11 URI Scheme", April 2015,
     available from <https://www.ietf.org/rfc/rfc7512>.

[TPMURI]
     C. Latze, N. Mavrogiannopoulos, "The TPMKEY URI Scheme", January
     2013, Work in progress, available from
     <https://tools.ietf.org/html/draft-mavrogiannopoulos-tpmuri-01>.

[ANDERSON]
     R. J. Anderson, "Security Engineering: A Guide to Building
     Dependable Distributed Systems", John Wiley \& Sons, Inc., 2001.

[RFC4821]
     M. Mathis, J. Heffner, "Packetization Layer Path MTU Discovery",
     March 2007, available from <https://www.ietf.org/rfc/rfc4821.txt>.

[RFC2560]
     M. Myers et al, "X.509 Internet Public Key Infrastructure Online
     Certificate Status Protocol - OCSP", June 1999, Available from
     <https://www.ietf.org/rfc/rfc2560.txt>.

[RIVESTCRL]
     R. L. Rivest, "Can We Eliminate Certificate Revocation Lists?",
     Proceedings of Financial Cryptography '98; Springer Lecture Notes
     in Computer Science No.  1465 (Rafael Hirschfeld, ed.), February
     1998), pages 178-183, available from
     <https://people.csail.mit.edu/rivest/Rivest-CanWeEliminateCertificateRevocationLists.pdf>.

[RFC9266]
     S. Whited, "Channel Bindings for TLS 1.3", July 2022, available
     from <https://www.ietf.org/rfc/rfc9266.txt>.


File: gnutls.info,  Node: Function and Data Index,  Next: Concept Index,  Prev: Bibliography,  Up: Top

Function and Data Index
***********************

 [index ]
* Menu:

* dane_cert_type_name:                   DANE API.           (line   13)
* dane_cert_usage_name:                  DANE API.           (line   24)
* dane_match_type_name:                  DANE API.           (line   36)
* dane_query_data:                       DANE API.           (line   47)
* dane_query_deinit:                     DANE API.           (line   70)
* dane_query_entries:                    DANE API.           (line   78)
* dane_query_status:                     DANE API.           (line   88)
* dane_query_tlsa:                       DANE API.           (line   99)
* dane_query_to_raw_tlsa:                DANE API.           (line  120)
* dane_raw_tlsa:                         DANE API.           (line  155)
* dane_state_deinit:                     DANE API.           (line  185)
* dane_state_init:                       DANE API.           (line  193)
* dane_state_set_dlv_file:               DANE API.           (line  208)
* dane_strerror:                         DANE API.           (line  220)
* dane_verification_status_print:        DANE API.           (line  234)
* dane_verify_crt:                       Certificate verification.
                                                             (line  181)
* dane_verify_crt <1>:                   DANE API.           (line  253)
* dane_verify_crt_raw:                   DANE API.           (line  307)
* dane_verify_session_crt:               DANE API.           (line  343)
* gnutls_aead_cipher_decrypt:            Cryptographic API.  (line   16)
* gnutls_aead_cipher_decryptv2:          Cryptographic API.  (line   56)
* gnutls_aead_cipher_deinit:             Cryptographic API.  (line   88)
* gnutls_aead_cipher_encrypt:            Cryptographic API.  (line  100)
* gnutls_aead_cipher_encryptv:           Symmetric algorithms.
                                                             (line  149)
* gnutls_aead_cipher_encryptv <1>:       Cryptographic API.  (line  136)
* gnutls_aead_cipher_encryptv2:          Cryptographic API.  (line  172)
* gnutls_aead_cipher_init:               Cryptographic API.  (line  204)
* gnutls_aead_cipher_set_key:            Cryptographic API.  (line  225)
* gnutls_alert_get:                      Handling alerts.    (line   18)
* gnutls_alert_get <1>:                  Core TLS API.       (line   11)
* gnutls_alert_get_name:                 Handling alerts.    (line   31)
* gnutls_alert_get_name <1>:             Core TLS API.       (line   27)
* gnutls_alert_get_strname:              Core TLS API.       (line   40)
* gnutls_alert_send:                     Handling alerts.    (line   45)
* gnutls_alert_send <1>:                 Core TLS API.       (line   54)
* gnutls_alert_send_appropriate:         Core TLS API.       (line   78)
* gnutls_alert_set_read_function:        Core TLS API.       (line  104)
* gnutls_alpn_get_selected_protocol:     Core TLS API.       (line  118)
* gnutls_alpn_set_protocols:             Core TLS API.       (line  139)
* gnutls_anon_allocate_client_credentials: Core TLS API.     (line  165)
* gnutls_anon_allocate_server_credentials: Core TLS API.     (line  176)
* gnutls_anon_free_client_credentials:   Core TLS API.       (line  187)
* gnutls_anon_free_server_credentials:   Core TLS API.       (line  196)
* gnutls_anon_set_params_function:       Core TLS API.       (line  205)
* gnutls_anon_set_server_dh_params:      Core TLS API.       (line  223)
* gnutls_anon_set_server_known_dh_params: Core TLS API.      (line  241)
* gnutls_anon_set_server_params_function: Core TLS API.      (line  265)
* gnutls_anti_replay_deinit:             Core TLS API.       (line  283)
* gnutls_anti_replay_enable:             Core TLS API.       (line  295)
* gnutls_anti_replay_init:               Core TLS API.       (line  308)
* gnutls_anti_replay_set_add_function:   Core TLS API.       (line  324)
* gnutls_anti_replay_set_ptr:            Core TLS API.       (line  351)
* gnutls_anti_replay_set_window:         Core TLS API.       (line  363)
* gnutls_auth_client_get_type:           Core TLS API.       (line  383)
* gnutls_auth_get_type:                  Core TLS API.       (line  400)
* gnutls_auth_server_get_type:           Core TLS API.       (line  421)
* gnutls_base64_decode2:                 Core TLS API.       (line  438)
* gnutls_base64_encode2:                 Core TLS API.       (line  457)
* gnutls_buffer_append_data:             Core TLS API.       (line  477)
* gnutls_bye:                            Data transfer and termination.
                                                             (line  155)
* gnutls_bye <1>:                        Core TLS API.       (line  495)
* gnutls_certificate_activation_time_peers: Core TLS API.    (line  531)
* gnutls_certificate_allocate_credentials: Core TLS API.     (line  545)
* gnutls_certificate_client_get_request_status: Core TLS API.
                                                             (line  556)
* gnutls_certificate_expiration_time_peers: Core TLS API.    (line  569)
* gnutls_certificate_free_ca_names:      Core TLS API.       (line  583)
* gnutls_certificate_free_cas:           Core TLS API.       (line  600)
* gnutls_certificate_free_credentials:   Core TLS API.       (line  611)
* gnutls_certificate_free_crls:          Core TLS API.       (line  624)
* gnutls_certificate_free_keys:          Core TLS API.       (line  634)
* gnutls_certificate_get_crt_raw:        Core TLS API.       (line  646)
* gnutls_certificate_get_issuer:         Core TLS API.       (line  673)
* gnutls_certificate_get_ocsp_expiration: OCSP stapling.     (line   46)
* gnutls_certificate_get_ocsp_expiration <1>: Core TLS API.  (line  703)
* gnutls_certificate_get_ours:           Core TLS API.       (line  735)
* gnutls_certificate_get_peers:          Core TLS API.       (line  756)
* gnutls_certificate_get_peers_subkey_id: Core TLS API.      (line  781)
* gnutls_certificate_get_trust_list:     X509 certificate API.
                                                             (line   12)
* gnutls_certificate_get_verify_flags:   Core TLS API.       (line  796)
* gnutls_certificate_get_x509_crt:       Core TLS API.       (line  810)
* gnutls_certificate_get_x509_key:       Core TLS API.       (line  851)
* gnutls_certificate_send_x509_rdn_sequence: Certificate credentials.
                                                             (line  192)
* gnutls_certificate_send_x509_rdn_sequence <1>: Core TLS API.
                                                             (line  887)
* gnutls_certificate_server_set_request: Certificate credentials.
                                                             (line  177)
* gnutls_certificate_server_set_request <1>: Core TLS API.   (line  905)
* gnutls_certificate_set_dh_params:      Core TLS API.       (line  923)
* gnutls_certificate_set_flags:          Core TLS API.       (line  944)
* gnutls_certificate_set_key:            Certificate credentials.
                                                             (line   69)
* gnutls_certificate_set_key <1>:        Abstract key API.   (line   12)
* gnutls_certificate_set_known_dh_params: Core TLS API.      (line  959)
* gnutls_certificate_set_ocsp_status_request_file: Core TLS API.
                                                             (line  983)
* gnutls_certificate_set_ocsp_status_request_file2: Core TLS API.
                                                             (line 1026)
* gnutls_certificate_set_ocsp_status_request_function: Core TLS API.
                                                             (line 1063)
* gnutls_certificate_set_ocsp_status_request_function2: Core TLS API.
                                                             (line 1098)
* gnutls_certificate_set_ocsp_status_request_mem: Core TLS API.
                                                             (line 1144)
* gnutls_certificate_set_params_function: Core TLS API.      (line 1186)
* gnutls_certificate_set_pin_function:   Certificate credentials.
                                                             (line   45)
* gnutls_certificate_set_pin_function <1>: Core TLS API.     (line 1205)
* gnutls_certificate_set_rawpk_key_file: Core TLS API.       (line 1226)
* gnutls_certificate_set_rawpk_key_mem:  Core TLS API.       (line 1300)
* gnutls_certificate_set_retrieve_function: Core TLS API.    (line 1371)
* gnutls_certificate_set_retrieve_function2: Abstract key API.
                                                             (line   63)
* gnutls_certificate_set_retrieve_function3: Abstract key API.
                                                             (line  117)
* gnutls_certificate_set_trust_list:     X509 certificate API.
                                                             (line   30)
* gnutls_certificate_set_verify_flags:   Core TLS API.       (line 1421)
* gnutls_certificate_set_verify_function: Core TLS API.      (line 1434)
* gnutls_certificate_set_verify_limits:  Core TLS API.       (line 1463)
* gnutls_certificate_set_x509_crl:       Core TLS API.       (line 1482)
* gnutls_certificate_set_x509_crl_file:  Core TLS API.       (line 1506)
* gnutls_certificate_set_x509_crl_mem:   Core TLS API.       (line 1528)
* gnutls_certificate_set_x509_key:       Core TLS API.       (line 1550)
* gnutls_certificate_set_x509_key_file:  Core TLS API.       (line 1590)
* gnutls_certificate_set_x509_key_file2: Core TLS API.       (line 1642)
* gnutls_certificate_set_x509_key_mem:   Core TLS API.       (line 1699)
* gnutls_certificate_set_x509_key_mem2:  Core TLS API.       (line 1740)
* gnutls_certificate_set_x509_simple_pkcs12_file: Core TLS API.
                                                             (line 1786)
* gnutls_certificate_set_x509_simple_pkcs12_mem: Core TLS API.
                                                             (line 1834)
* gnutls_certificate_set_x509_system_trust: Certificate credentials.
                                                             (line  232)
* gnutls_certificate_set_x509_system_trust <1>: Core TLS API.
                                                             (line 1884)
* gnutls_certificate_set_x509_trust:     Core TLS API.       (line 1902)
* gnutls_certificate_set_x509_trust_dir: Core TLS API.       (line 1929)
* gnutls_certificate_set_x509_trust_file: Core TLS API.      (line 1951)
* gnutls_certificate_set_x509_trust_mem: Core TLS API.       (line 1981)
* gnutls_certificate_type_get:           Core TLS API.       (line 2006)
* gnutls_certificate_type_get_id:        Core TLS API.       (line 2059)
* gnutls_certificate_type_get_name:      Core TLS API.       (line 2071)
* gnutls_certificate_type_get2:          Core TLS API.       (line 2027)
* gnutls_certificate_type_list:          Core TLS API.       (line 2083)
* gnutls_certificate_verification_profile_get_id: X509 certificate API.
                                                             (line   51)
* gnutls_certificate_verification_profile_get_name: X509 certificate API.
                                                             (line   65)
* gnutls_certificate_verification_status_print: Core TLS API.
                                                             (line 2094)
* gnutls_certificate_verify_flags:       Verifying a certificate in the context of TLS session.
                                                             (line    6)
* gnutls_certificate_verify_flags <1>:   Certificate verification.
                                                             (line    6)
* gnutls_certificate_verify_peers:       Core TLS API.       (line 2119)
* gnutls_certificate_verify_peers2:      Core TLS API.       (line 2180)
* gnutls_certificate_verify_peers3:      Core TLS API.       (line 2222)
* gnutls_check_version:                  Core TLS API.       (line 2273)
* gnutls_cipher_add_auth:                Cryptographic API.  (line  241)
* gnutls_cipher_decrypt:                 Cryptographic API.  (line  260)
* gnutls_cipher_decrypt2:                Cryptographic API.  (line  282)
* gnutls_cipher_decrypt3:                Cryptographic API.  (line  311)
* gnutls_cipher_deinit:                  Cryptographic API.  (line  338)
* gnutls_cipher_encrypt:                 Cryptographic API.  (line  349)
* gnutls_cipher_encrypt2:                Cryptographic API.  (line  367)
* gnutls_cipher_encrypt3:                Cryptographic API.  (line  392)
* gnutls_cipher_get:                     Core TLS API.       (line 2292)
* gnutls_cipher_get_block_size:          Cryptographic API.  (line  427)
* gnutls_cipher_get_id:                  Core TLS API.       (line 2304)
* gnutls_cipher_get_iv_size:             Cryptographic API.  (line  438)
* gnutls_cipher_get_key_size:            Core TLS API.       (line 2316)
* gnutls_cipher_get_name:                Core TLS API.       (line 2328)
* gnutls_cipher_get_tag_size:            Cryptographic API.  (line  453)
* gnutls_cipher_init:                    Cryptographic API.  (line  467)
* gnutls_cipher_list:                    Core TLS API.       (line 2340)
* gnutls_cipher_set_iv:                  Cryptographic API.  (line  490)
* gnutls_cipher_suite_get_name:          Core TLS API.       (line 2356)
* gnutls_cipher_suite_info:              Core TLS API.       (line 2379)
* gnutls_cipher_tag:                     Cryptographic API.  (line  506)
* gnutls_ciphersuite_get:                Core TLS API.       (line 2409)
* gnutls_compress_certificate_get_selected_method: Core TLS API.
                                                             (line 2432)
* gnutls_compress_certificate_set_methods: Core TLS API.     (line 2449)
* gnutls_compression_get:                Compatibility API.  (line   13)
* gnutls_compression_get_id:             Compatibility API.  (line   25)
* gnutls_compression_get_name:           Compatibility API.  (line   37)
* gnutls_compression_list:               Compatibility API.  (line   49)
* gnutls_credentials_clear:              Core TLS API.       (line 2490)
* gnutls_credentials_get:                Core TLS API.       (line 2498)
* gnutls_credentials_set:                Session initialization.
                                                             (line  163)
* gnutls_credentials_set <1>:            Core TLS API.       (line 2531)
* gnutls_crypto_register_aead_cipher:    Overriding algorithms.
                                                             (line   58)
* gnutls_crypto_register_aead_cipher <1>: Cryptographic API. (line  524)
* gnutls_crypto_register_cipher:         Overriding algorithms.
                                                             (line   13)
* gnutls_crypto_register_cipher <1>:     Cryptographic API.  (line  569)
* gnutls_crypto_register_digest:         Overriding algorithms.
                                                             (line  139)
* gnutls_crypto_register_digest <1>:     Cryptographic API.  (line  617)
* gnutls_crypto_register_mac:            Overriding algorithms.
                                                             (line  100)
* gnutls_crypto_register_mac <1>:        Cryptographic API.  (line  653)
* gnutls_db_check_entry:                 Core TLS API.       (line 2566)
* gnutls_db_check_entry_expire_time:     Core TLS API.       (line 2582)
* gnutls_db_check_entry_time:            Core TLS API.       (line 2596)
* gnutls_db_get_default_cache_expiration: Core TLS API.      (line 2607)
* gnutls_db_get_ptr:                     Core TLS API.       (line 2615)
* gnutls_db_remove_session:              Core TLS API.       (line 2626)
* gnutls_db_set_cache_expiration:        Core TLS API.       (line 2640)
* gnutls_db_set_ptr:                     Core TLS API.       (line 2655)
* gnutls_db_set_remove_function:         Core TLS API.       (line 2667)
* gnutls_db_set_retrieve_function:       Core TLS API.       (line 2682)
* gnutls_db_set_store_function:          Core TLS API.       (line 2702)
* gnutls_decode_ber_digest_info:         Cryptographic API.  (line  695)
* gnutls_decode_gost_rs_value:           Cryptographic API.  (line  718)
* gnutls_decode_rs_value:                Cryptographic API.  (line  743)
* gnutls_deinit:                         Data transfer and termination.
                                                             (line  188)
* gnutls_deinit <1>:                     Core TLS API.       (line 2717)
* gnutls_dh_get_group:                   Core TLS API.       (line 2727)
* gnutls_dh_get_peers_public_bits:       Core TLS API.       (line 2750)
* gnutls_dh_get_prime_bits:              Core TLS API.       (line 2764)
* gnutls_dh_get_pubkey:                  Core TLS API.       (line 2780)
* gnutls_dh_get_secret_bits:             Core TLS API.       (line 2800)
* gnutls_dh_params_cpy:                  Core TLS API.       (line 2813)
* gnutls_dh_params_deinit:               Core TLS API.       (line 2828)
* gnutls_dh_params_export_pkcs3:         Core TLS API.       (line 2861)
* gnutls_dh_params_export_raw:           Core TLS API.       (line 2888)
* gnutls_dh_params_export2_pkcs3:        Core TLS API.       (line 2837)
* gnutls_dh_params_generate2:            Core TLS API.       (line 2910)
* gnutls_dh_params_import_dsa:           Core TLS API.       (line 2940)
* gnutls_dh_params_import_pkcs3:         Core TLS API.       (line 2955)
* gnutls_dh_params_import_raw:           Core TLS API.       (line 2977)
* gnutls_dh_params_import_raw2:          Core TLS API.       (line 2996)
* gnutls_dh_params_import_raw3:          Core TLS API.       (line 3017)
* gnutls_dh_params_init:                 Core TLS API.       (line 3038)
* gnutls_dh_set_prime_bits:              Core TLS API.       (line 3049)
* gnutls_digest_get_id:                  Core TLS API.       (line 3077)
* gnutls_digest_get_name:                Core TLS API.       (line 3090)
* gnutls_digest_get_oid:                 Core TLS API.       (line 3102)
* gnutls_digest_list:                    Core TLS API.       (line 3117)
* gnutls_digest_set_secure:              Core TLS API.       (line 3131)
* gnutls_dtls_cookie_send:               Datagram TLS API.   (line   11)
* gnutls_dtls_cookie_verify:             Datagram TLS API.   (line   45)
* gnutls_dtls_get_data_mtu:              Datagram TLS API.   (line   74)
* gnutls_dtls_get_mtu:                   Datagram TLS API.   (line   89)
* gnutls_dtls_get_timeout:               Setting up the transport layer.
                                                             (line  193)
* gnutls_dtls_get_timeout <1>:           Datagram TLS API.   (line  104)
* gnutls_dtls_prestate_set:              Datagram TLS API.   (line  121)
* gnutls_dtls_set_data_mtu:              Datagram TLS API.   (line  139)
* gnutls_dtls_set_mtu:                   Datagram TLS API.   (line  165)
* gnutls_dtls_set_timeouts:              Datagram TLS API.   (line  182)
* gnutls_early_cipher_get:               Core TLS API.       (line 3147)
* gnutls_early_prf_hash_get:             Core TLS API.       (line 3161)
* gnutls_ecc_curve_get:                  Core TLS API.       (line 3176)
* gnutls_ecc_curve_get_id:               Core TLS API.       (line 3190)
* gnutls_ecc_curve_get_name:             Core TLS API.       (line 3204)
* gnutls_ecc_curve_get_oid:              Core TLS API.       (line 3218)
* gnutls_ecc_curve_get_pk:               Core TLS API.       (line 3232)
* gnutls_ecc_curve_get_size:             Core TLS API.       (line 3244)
* gnutls_ecc_curve_list:                 Core TLS API.       (line 3254)
* gnutls_ecc_curve_set_enabled:          Core TLS API.       (line 3266)
* gnutls_encode_ber_digest_info:         Cryptographic API.  (line  767)
* gnutls_encode_gost_rs_value:           Cryptographic API.  (line  787)
* gnutls_encode_rs_value:                Cryptographic API.  (line  810)
* gnutls_error_is_fatal:                 Data transfer and termination.
                                                             (line   82)
* gnutls_error_is_fatal <1>:             Core TLS API.       (line 3291)
* gnutls_error_to_alert:                 Handling alerts.    (line   66)
* gnutls_error_to_alert <1>:             Core TLS API.       (line 3311)
* gnutls_est_record_overhead_size:       Core TLS API.       (line 3330)
* gnutls_ext_get_current_msg:            Core TLS API.       (line 3357)
* gnutls_ext_get_data:                   Core TLS API.       (line 3375)
* gnutls_ext_get_name:                   Core TLS API.       (line 3394)
* gnutls_ext_get_name2:                  Core TLS API.       (line 3405)
* gnutls_ext_raw_parse:                  Core TLS API.       (line 3422)
* gnutls_ext_register:                   Core TLS API.       (line 3453)
* gnutls_ext_set_data:                   Core TLS API.       (line 3500)
* gnutls_fingerprint:                    Core TLS API.       (line 3517)
* gnutls_fips140_context_deinit:         Core TLS API.       (line 3544)
* gnutls_fips140_context_init:           Core TLS API.       (line 3555)
* gnutls_fips140_get_operation_state:    FIPS140-2 mode.     (line  137)
* gnutls_fips140_get_operation_state <1>: Core TLS API.      (line 3568)
* gnutls_fips140_mode_enabled:           Core TLS API.       (line 3582)
* gnutls_fips140_pop_context:            Core TLS API.       (line 3600)
* gnutls_fips140_push_context:           Core TLS API.       (line 3619)
* gnutls_fips140_run_self_tests:         Core TLS API.       (line 3642)
* gnutls_fips140_set_mode:               Core TLS API.       (line 3660)
* gnutls_get_library_config:             Core TLS API.       (line 3686)
* gnutls_get_system_config_file:         System-wide configuration of the library.
                                                             (line   24)
* gnutls_get_system_config_file <1>:     Core TLS API.       (line 3715)
* gnutls_global_deinit:                  Core TLS API.       (line 3727)
* gnutls_global_init:                    Core TLS API.       (line 3740)
* gnutls_global_set_audit_log_function:  Debugging and auditing.
                                                             (line   64)
* gnutls_global_set_audit_log_function <1>: Core TLS API.    (line 3769)
* gnutls_global_set_log_function:        Core TLS API.       (line 3788)
* gnutls_global_set_log_level:           Core TLS API.       (line 3803)
* gnutls_global_set_mem_functions:       Compatibility API.  (line   60)
* gnutls_global_set_mutex:               Core TLS API.       (line 3816)
* gnutls_global_set_time_function:       Core TLS API.       (line 3845)
* gnutls_gost_paramset_get_name:         Core TLS API.       (line 3859)
* gnutls_gost_paramset_get_oid:          Core TLS API.       (line 3873)
* gnutls_group_get:                      Core TLS API.       (line 3887)
* gnutls_group_get_id:                   Core TLS API.       (line 3900)
* gnutls_group_get_name:                 Core TLS API.       (line 3913)
* gnutls_group_list:                     Core TLS API.       (line 3926)
* gnutls_handshake:                      TLS handshake.      (line   10)
* gnutls_handshake <1>:                  Core TLS API.       (line 3940)
* gnutls_handshake_description_get_name: Core TLS API.       (line 3983)
* gnutls_handshake_get_last_in:          Core TLS API.       (line 3995)
* gnutls_handshake_get_last_out:         Core TLS API.       (line 4012)
* gnutls_handshake_set_hook_function:    Virtual hosts and credentials.
                                                             (line   56)
* gnutls_handshake_set_hook_function <1>: Core TLS API.      (line 4029)
* gnutls_handshake_set_max_packet_length: Core TLS API.      (line 4066)
* gnutls_handshake_set_post_client_hello_function: Core TLS API.
                                                             (line 4087)
* gnutls_handshake_set_private_extensions: Core TLS API.     (line 4118)
* gnutls_handshake_set_random:           Core TLS API.       (line 4137)
* gnutls_handshake_set_read_function:    Core TLS API.       (line 4159)
* gnutls_handshake_set_secret_function:  Core TLS API.       (line 4173)
* gnutls_handshake_set_timeout:          TLS handshake.      (line   50)
* gnutls_handshake_set_timeout <1>:      Core TLS API.       (line 4187)
* gnutls_handshake_write:                Core TLS API.       (line 4207)
* gnutls_hash:                           Cryptographic API.  (line  831)
* gnutls_hash_copy:                      Cryptographic API.  (line  849)
* gnutls_hash_deinit:                    Cryptographic API.  (line  868)
* gnutls_hash_fast:                      Cryptographic API.  (line  882)
* gnutls_hash_get_len:                   Cryptographic API.  (line  902)
* gnutls_hash_init:                      Cryptographic API.  (line  916)
* gnutls_hash_output:                    Cryptographic API.  (line  934)
* gnutls_hash_squeeze:                   Cryptographic API.  (line  949)
* gnutls_heartbeat_allowed:              Core TLS API.       (line 4228)
* gnutls_heartbeat_enable:               Core TLS API.       (line 4245)
* gnutls_heartbeat_get_timeout:          Core TLS API.       (line 4269)
* gnutls_heartbeat_ping:                 Core TLS API.       (line 4285)
* gnutls_heartbeat_pong:                 Core TLS API.       (line 4317)
* gnutls_heartbeat_set_timeouts:         Core TLS API.       (line 4333)
* gnutls_hex_decode:                     Core TLS API.       (line 4378)
* gnutls_hex_decode2:                    Core TLS API.       (line 4400)
* gnutls_hex_encode:                     Core TLS API.       (line 4415)
* gnutls_hex_encode2:                    Core TLS API.       (line 4434)
* gnutls_hex2bin:                        Core TLS API.       (line 4355)
* gnutls_hkdf_expand:                    Cryptographic API.  (line  975)
* gnutls_hkdf_extract:                   Cryptographic API.  (line  999)
* gnutls_hmac:                           Cryptographic API.  (line 1020)
* gnutls_hmac_copy:                      Cryptographic API.  (line 1038)
* gnutls_hmac_deinit:                    Cryptographic API.  (line 1056)
* gnutls_hmac_fast:                      Cryptographic API.  (line 1070)
* gnutls_hmac_get_key_size:              Cryptographic API.  (line 1096)
* gnutls_hmac_get_len:                   Cryptographic API.  (line 1111)
* gnutls_hmac_init:                      Cryptographic API.  (line 1125)
* gnutls_hmac_output:                    Cryptographic API.  (line 1151)
* gnutls_hmac_set_nonce:                 Cryptographic API.  (line 1165)
* gnutls_idna_map:                       Core TLS API.       (line 4452)
* gnutls_idna_reverse_map:               Core TLS API.       (line 4483)
* gnutls_init:                           Session initialization.
                                                             (line   14)
* gnutls_init <1>:                       Core TLS API.       (line 4509)
* gnutls_key_generate:                   Core TLS API.       (line 4541)
* gnutls_kx_get:                         Core TLS API.       (line 4558)
* gnutls_kx_get_id:                      Core TLS API.       (line 4575)
* gnutls_kx_get_name:                    Core TLS API.       (line 4587)
* gnutls_kx_list:                        Core TLS API.       (line 4599)
* gnutls_load_file:                      Core TLS API.       (line 4611)
* gnutls_mac_get:                        Core TLS API.       (line 4634)
* gnutls_mac_get_id:                     Core TLS API.       (line 4646)
* gnutls_mac_get_key_size:               Core TLS API.       (line 4659)
* gnutls_mac_get_name:                   Core TLS API.       (line 4671)
* gnutls_mac_get_nonce_size:             Cryptographic API.  (line 1180)
* gnutls_mac_list:                       Core TLS API.       (line 4683)
* gnutls_memcmp:                         Core TLS API.       (line 4695)
* gnutls_memset:                         Core TLS API.       (line 4716)
* gnutls_ocsp_req_add_cert:              OCSP API.           (line   12)
* gnutls_ocsp_req_add_cert_id:           OCSP API.           (line   36)
* gnutls_ocsp_req_deinit:                OCSP API.           (line   69)
* gnutls_ocsp_req_export:                OCSP API.           (line   77)
* gnutls_ocsp_req_get_cert_id:           OCSP API.           (line   91)
* gnutls_ocsp_req_get_extension:         OCSP API.           (line  131)
* gnutls_ocsp_req_get_nonce:             OCSP API.           (line  168)
* gnutls_ocsp_req_get_version:           OCSP API.           (line  187)
* gnutls_ocsp_req_import:                OCSP API.           (line  200)
* gnutls_ocsp_req_init:                  OCSP API.           (line  216)
* gnutls_ocsp_req_print:                 OCSP API.           (line  227)
* gnutls_ocsp_req_randomize_nonce:       OCSP API.           (line  250)
* gnutls_ocsp_req_set_extension:         OCSP API.           (line  263)
* gnutls_ocsp_req_set_nonce:             OCSP API.           (line  284)
* gnutls_ocsp_resp_check_crt:            OCSP API.           (line  302)
* gnutls_ocsp_resp_deinit:               OCSP API.           (line  322)
* gnutls_ocsp_resp_export:               OCSP API.           (line  330)
* gnutls_ocsp_resp_export2:              OCSP API.           (line  344)
* gnutls_ocsp_resp_get_certs:            OCSP API.           (line  363)
* gnutls_ocsp_resp_get_extension:        OCSP API.           (line  390)
* gnutls_ocsp_resp_get_nonce:            OCSP API.           (line  427)
* gnutls_ocsp_resp_get_produced:         OCSP API.           (line  447)
* gnutls_ocsp_resp_get_responder:        OCSP API.           (line  459)
* gnutls_ocsp_resp_get_responder_raw_id: OCSP API.           (line  517)
* gnutls_ocsp_resp_get_responder2:       OCSP API.           (line  486)
* gnutls_ocsp_resp_get_response:         OCSP API.           (line  541)
* gnutls_ocsp_resp_get_signature:        OCSP API.           (line  571)
* gnutls_ocsp_resp_get_signature_algorithm: OCSP API.        (line  585)
* gnutls_ocsp_resp_get_single:           OCSP certificate status checking.
                                                             (line  157)
* gnutls_ocsp_resp_get_single <1>:       OCSP API.           (line  599)
* gnutls_ocsp_resp_get_status:           OCSP API.           (line  650)
* gnutls_ocsp_resp_get_version:          OCSP API.           (line  663)
* gnutls_ocsp_resp_import:               OCSP API.           (line  676)
* gnutls_ocsp_resp_import2:              OCSP API.           (line  692)
* gnutls_ocsp_resp_init:                 OCSP API.           (line  712)
* gnutls_ocsp_resp_list_import2:         OCSP API.           (line  723)
* gnutls_ocsp_resp_print:                OCSP API.           (line  757)
* gnutls_ocsp_resp_verify:               OCSP API.           (line  780)
* gnutls_ocsp_resp_verify_direct:        OCSP API.           (line  818)
* gnutls_ocsp_status_request_enable_client: Core TLS API.    (line 4731)
* gnutls_ocsp_status_request_get:        Core TLS API.       (line 4759)
* gnutls_ocsp_status_request_get2:       Core TLS API.       (line 4778)
* gnutls_ocsp_status_request_is_checked: Core TLS API.       (line 4804)
* gnutls_oid_to_digest:                  Core TLS API.       (line 4838)
* gnutls_oid_to_ecc_curve:               Core TLS API.       (line 4853)
* gnutls_oid_to_gost_paramset:           Core TLS API.       (line 4865)
* gnutls_oid_to_mac:                     Core TLS API.       (line 4880)
* gnutls_oid_to_pk:                      Core TLS API.       (line 4895)
* gnutls_oid_to_sign:                    Core TLS API.       (line 4909)
* gnutls_openpgp_privkey_sign_hash:      Compatibility API.  (line   95)
* gnutls_openpgp_send_cert:              Core TLS API.       (line 4924)
* gnutls_packet_deinit:                  Core TLS API.       (line 4937)
* gnutls_packet_get:                     Core TLS API.       (line 4948)
* gnutls_pbkdf2:                         Cryptographic API.  (line 1193)
* gnutls_pcert_deinit:                   Abstract key API.   (line  176)
* gnutls_pcert_export_openpgp:           Abstract key API.   (line  186)
* gnutls_pcert_export_x509:              Abstract key API.   (line  201)
* gnutls_pcert_import_openpgp:           Abstract key API.   (line  221)
* gnutls_pcert_import_openpgp_raw:       Abstract key API.   (line  238)
* gnutls_pcert_import_rawpk:             Abstract key API.   (line  260)
* gnutls_pcert_import_rawpk_raw:         Abstract key API.   (line  284)
* gnutls_pcert_import_x509:              Abstract key API.   (line  318)
* gnutls_pcert_import_x509_list:         Abstract key API.   (line  338)
* gnutls_pcert_import_x509_raw:          Abstract key API.   (line  370)
* gnutls_pcert_list_import_x509_file:    Abstract key API.   (line  393)
* gnutls_pcert_list_import_x509_raw:     Abstract key API.   (line  430)
* gnutls_pem_base64_decode:              Core TLS API.       (line 4966)
* gnutls_pem_base64_decode2:             Core TLS API.       (line 4990)
* gnutls_pem_base64_encode:              Core TLS API.       (line 5018)
* gnutls_pem_base64_encode2:             Core TLS API.       (line 5041)
* gnutls_perror:                         Core TLS API.       (line 5069)
* gnutls_pk_algorithm_get_name:          Core TLS API.       (line 5078)
* gnutls_pk_bits_to_sec_param:           Selecting cryptographic key sizes.
                                                             (line   91)
* gnutls_pk_bits_to_sec_param <1>:       Core TLS API.       (line 5090)
* gnutls_pk_get_id:                      Core TLS API.       (line 5107)
* gnutls_pk_get_name:                    Core TLS API.       (line 5122)
* gnutls_pk_get_oid:                     Core TLS API.       (line 5136)
* gnutls_pk_list:                        Core TLS API.       (line 5151)
* gnutls_pk_to_sign:                     Core TLS API.       (line 5165)
* gnutls_pkcs_schema_get_name:           X509 certificate API.
                                                             (line  119)
* gnutls_pkcs_schema_get_oid:            X509 certificate API.
                                                             (line  134)
* gnutls_pkcs11_add_provider:            PKCS11 Manual Initialization.
                                                             (line   13)
* gnutls_pkcs11_add_provider <1>:        PKCS 11 API.        (line   12)
* gnutls_pkcs11_copy_attached_extension: PKCS 11 API.        (line   35)
* gnutls_pkcs11_copy_pubkey:             PKCS 11 API.        (line   62)
* gnutls_pkcs11_copy_secret_key:         PKCS 11 API.        (line   94)
* gnutls_pkcs11_copy_x509_crt:           PKCS 11 API.        (line  119)
* gnutls_pkcs11_copy_x509_crt2:          Writing objects.    (line   41)
* gnutls_pkcs11_copy_x509_crt2 <1>:      PKCS 11 API.        (line  141)
* gnutls_pkcs11_copy_x509_privkey:       PKCS 11 API.        (line  170)
* gnutls_pkcs11_copy_x509_privkey2:      Writing objects.    (line   13)
* gnutls_pkcs11_copy_x509_privkey2 <1>:  PKCS 11 API.        (line  197)
* gnutls_pkcs11_crt_is_known:            PKCS 11 API.        (line  228)
* gnutls_pkcs11_deinit:                  PKCS 11 API.        (line  259)
* gnutls_pkcs11_delete_url:              Writing objects.    (line   67)
* gnutls_pkcs11_delete_url <1>:          PKCS 11 API.        (line  270)
* gnutls_pkcs11_get_pin_function:        PKCS 11 API.        (line  287)
* gnutls_pkcs11_get_raw_issuer:          PKCS 11 API.        (line  301)
* gnutls_pkcs11_get_raw_issuer_by_dn:    PKCS 11 API.        (line  327)
* gnutls_pkcs11_get_raw_issuer_by_subject_key_id: PKCS 11 API.
                                                             (line  356)
* gnutls_pkcs11_init:                    PKCS11 Manual Initialization.
                                                             (line   38)
* gnutls_pkcs11_init <1>:                PKCS 11 API.        (line  388)
* gnutls_pkcs11_obj_deinit:              PKCS 11 API.        (line  415)
* gnutls_pkcs11_obj_export:              PKCS 11 API.        (line  425)
* gnutls_pkcs11_obj_export_url:          PKCS 11 API.        (line  492)
* gnutls_pkcs11_obj_export2:             PKCS 11 API.        (line  450)
* gnutls_pkcs11_obj_export3:             PKCS 11 API.        (line  470)
* gnutls_pkcs11_obj_flags_get_str:       PKCS 11 API.        (line  510)
* gnutls_pkcs11_obj_get_exts:            PKCS 11 API.        (line  526)
* gnutls_pkcs11_obj_get_flags:           PKCS 11 API.        (line  554)
* gnutls_pkcs11_obj_get_info:            Reading objects.    (line   28)
* gnutls_pkcs11_obj_get_info <1>:        PKCS 11 API.        (line  572)
* gnutls_pkcs11_obj_get_ptr:             PKCS11 Low Level Access.
                                                             (line   31)
* gnutls_pkcs11_obj_get_ptr <1>:         PKCS 11 API.        (line  606)
* gnutls_pkcs11_obj_get_type:            PKCS 11 API.        (line  633)
* gnutls_pkcs11_obj_import_url:          PKCS 11 API.        (line  647)
* gnutls_pkcs11_obj_init:                PKCS 11 API.        (line  673)
* gnutls_pkcs11_obj_list_import_url3:    PKCS 11 API.        (line  686)
* gnutls_pkcs11_obj_list_import_url4:    PKCS 11 API.        (line  733)
* gnutls_pkcs11_obj_set_info:            PKCS 11 API.        (line  775)
* gnutls_pkcs11_obj_set_pin_function:    PKCS 11 API.        (line  800)
* gnutls_pkcs11_privkey_cpy:             PKCS 11 API.        (line  818)
* gnutls_pkcs11_privkey_deinit:          PKCS 11 API.        (line  835)
* gnutls_pkcs11_privkey_export_pubkey:   PKCS 11 API.        (line  844)
* gnutls_pkcs11_privkey_export_url:      PKCS 11 API.        (line  869)
* gnutls_pkcs11_privkey_generate:        PKCS 11 API.        (line  886)
* gnutls_pkcs11_privkey_generate2:       PKCS 11 API.        (line  911)
* gnutls_pkcs11_privkey_generate3:       PKCS 11 API.        (line  947)
* gnutls_pkcs11_privkey_get_info:        PKCS 11 API.        (line  991)
* gnutls_pkcs11_privkey_get_pk_algorithm: PKCS 11 API.       (line 1014)
* gnutls_pkcs11_privkey_import_url:      PKCS 11 API.        (line 1030)
* gnutls_pkcs11_privkey_init:            PKCS 11 API.        (line 1050)
* gnutls_pkcs11_privkey_set_pin_function: PKCS 11 API.       (line 1068)
* gnutls_pkcs11_privkey_status:          PKCS 11 API.        (line 1086)
* gnutls_pkcs11_reinit:                  PKCS 11 API.        (line 1100)
* gnutls_pkcs11_set_pin_function:        PKCS 11 API.        (line 1117)
* gnutls_pkcs11_set_token_function:      PKCS 11 API.        (line 1132)
* gnutls_pkcs11_token_check_mechanism:   PKCS 11 API.        (line 1146)
* gnutls_pkcs11_token_get_flags:         PKCS 11 API.        (line 1171)
* gnutls_pkcs11_token_get_info:          PKCS 11 API.        (line 1191)
* gnutls_pkcs11_token_get_mechanism:     PKCS 11 API.        (line 1220)
* gnutls_pkcs11_token_get_ptr:           PKCS11 Low Level Access.
                                                             (line   12)
* gnutls_pkcs11_token_get_ptr <1>:       PKCS 11 API.        (line 1240)
* gnutls_pkcs11_token_get_random:        PKCS 11 API.        (line 1262)
* gnutls_pkcs11_token_get_url:           PKCS 11 API.        (line 1280)
* gnutls_pkcs11_token_init:              PKCS 11 API.        (line 1300)
* gnutls_pkcs11_token_set_pin:           PKCS 11 API.        (line 1319)
* gnutls_pkcs11_type_get_name:           PKCS 11 API.        (line 1342)
* gnutls_pkcs12_bag_decrypt:             PKCS 12 API.        (line   12)
* gnutls_pkcs12_bag_deinit:              PKCS 12 API.        (line   27)
* gnutls_pkcs12_bag_enc_info:            PKCS 12 API.        (line   35)
* gnutls_pkcs12_bag_encrypt:             PKCS 12 API.        (line   71)
* gnutls_pkcs12_bag_get_count:           PKCS 12 API.        (line   88)
* gnutls_pkcs12_bag_get_data:            PKCS 12 API.        (line  100)
* gnutls_pkcs12_bag_get_friendly_name:   PKCS 12 API.        (line  118)
* gnutls_pkcs12_bag_get_key_id:          PKCS 12 API.        (line  137)
* gnutls_pkcs12_bag_get_type:            PKCS 12 API.        (line  156)
* gnutls_pkcs12_bag_init:                PKCS 12 API.        (line  170)
* gnutls_pkcs12_bag_set_crl:             PKCS 12 API.        (line  183)
* gnutls_pkcs12_bag_set_crt:             PKCS 12 API.        (line  198)
* gnutls_pkcs12_bag_set_data:            PKCS 12 API.        (line  213)
* gnutls_pkcs12_bag_set_friendly_name:   PKCS 12 API.        (line  230)
* gnutls_pkcs12_bag_set_key_id:          PKCS 12 API.        (line  250)
* gnutls_pkcs12_bag_set_privkey:         PKCS 12 API.        (line  270)
* gnutls_pkcs12_deinit:                  PKCS 12 API.        (line  291)
* gnutls_pkcs12_export:                  PKCS 12 API.        (line  299)
* gnutls_pkcs12_export2:                 PKCS 12 API.        (line  327)
* gnutls_pkcs12_generate_mac:            PKCS 12 API.        (line  351)
* gnutls_pkcs12_generate_mac2:           PKCS 12 API.        (line  365)
* gnutls_pkcs12_generate_mac3:           PKCS 12 API.        (line  381)
* gnutls_pkcs12_get_bag:                 PKCS 12 API.        (line  403)
* gnutls_pkcs12_import:                  PKCS 12 API.        (line  423)
* gnutls_pkcs12_init:                    PKCS 12 API.        (line  446)
* gnutls_pkcs12_mac_info:                PKCS 12 API.        (line  459)
* gnutls_pkcs12_set_bag:                 PKCS 12 API.        (line  492)
* gnutls_pkcs12_simple_parse:            Managing encrypted keys.
                                                             (line  160)
* gnutls_pkcs12_simple_parse <1>:        PKCS 12 API.        (line  506)
* gnutls_pkcs12_verify_mac:              PKCS 12 API.        (line  573)
* gnutls_pkcs7_add_attr:                 PKCS 7 API.         (line   12)
* gnutls_pkcs7_attrs_deinit:             PKCS 7 API.         (line   36)
* gnutls_pkcs7_deinit:                   PKCS 7 API.         (line   46)
* gnutls_pkcs7_delete_crl:               PKCS 7 API.         (line   54)
* gnutls_pkcs7_delete_crt:               PKCS 7 API.         (line   69)
* gnutls_pkcs7_export:                   PKCS 7 API.         (line   84)
* gnutls_pkcs7_export2:                  PKCS 7 API.         (line  111)
* gnutls_pkcs7_get_attr:                 PKCS 7 API.         (line  134)
* gnutls_pkcs7_get_crl_count:            PKCS 7 API.         (line  161)
* gnutls_pkcs7_get_crl_raw:              PKCS 7 API.         (line  173)
* gnutls_pkcs7_get_crl_raw2:             PKCS 7 API.         (line  194)
* gnutls_pkcs7_get_crt_count:            PKCS 7 API.         (line  214)
* gnutls_pkcs7_get_crt_raw:              PKCS 7 API.         (line  226)
* gnutls_pkcs7_get_crt_raw2:             PKCS 7 API.         (line  251)
* gnutls_pkcs7_get_embedded_data:        PKCS 7 API.         (line  276)
* gnutls_pkcs7_get_embedded_data_oid:    PKCS 7 API.         (line  303)
* gnutls_pkcs7_get_signature_count:      PKCS 7 API.         (line  319)
* gnutls_pkcs7_get_signature_info:       PKCS 7 API.         (line  334)
* gnutls_pkcs7_import:                   PKCS 7 API.         (line  355)
* gnutls_pkcs7_init:                     PKCS 7 API.         (line  377)
* gnutls_pkcs7_print:                    PKCS 7 API.         (line  390)
* gnutls_pkcs7_print_signature_info:     PKCS 7 API.         (line  413)
* gnutls_pkcs7_set_crl:                  PKCS 7 API.         (line  439)
* gnutls_pkcs7_set_crl_raw:              PKCS 7 API.         (line  454)
* gnutls_pkcs7_set_crt:                  PKCS 7 API.         (line  468)
* gnutls_pkcs7_set_crt_raw:              PKCS 7 API.         (line  484)
* gnutls_pkcs7_sign:                     Cryptographic Message Syntax / PKCS7.
                                                             (line   35)
* gnutls_pkcs7_sign <1>:                 PKCS 7 API.         (line  499)
* gnutls_pkcs7_signature_info_deinit:    PKCS 7 API.         (line  540)
* gnutls_pkcs7_verify:                   PKCS 7 API.         (line  552)
* gnutls_pkcs7_verify_direct:            PKCS 7 API.         (line  585)
* gnutls_pkcs8_info:                     X509 certificate API.
                                                             (line   79)
* gnutls_prf:                            Core TLS API.       (line 5180)
* gnutls_prf_early:                      Core TLS API.       (line 5230)
* gnutls_prf_hash_get:                   Core TLS API.       (line 5275)
* gnutls_prf_raw:                        Core TLS API.       (line 5292)
* gnutls_prf_rfc5705:                    Deriving keys for other applications/protocols.
                                                             (line   16)
* gnutls_prf_rfc5705 <1>:                Core TLS API.       (line 5337)
* gnutls_priority_certificate_type_list: Core TLS API.       (line 5384)
* gnutls_priority_certificate_type_list2: Core TLS API.      (line 5405)
* gnutls_priority_cipher_list:           Core TLS API.       (line 5425)
* gnutls_priority_compression_list:      Compatibility API.  (line  111)
* gnutls_priority_deinit:                Core TLS API.       (line 5440)
* gnutls_priority_ecc_curve_list:        Core TLS API.       (line 5449)
* gnutls_priority_get_cipher_suite_index: Core TLS API.      (line 5467)
* gnutls_priority_group_list:            Core TLS API.       (line 5492)
* gnutls_priority_init:                  Core TLS API.       (line 5507)
* gnutls_priority_init2:                 Core TLS API.       (line 5535)
* gnutls_priority_kx_list:               Core TLS API.       (line 5643)
* gnutls_priority_mac_list:              Core TLS API.       (line 5659)
* gnutls_priority_protocol_list:         Core TLS API.       (line 5674)
* gnutls_priority_set:                   Core TLS API.       (line 5690)
* gnutls_priority_set_direct:            Core TLS API.       (line 5708)
* gnutls_priority_sign_list:             Core TLS API.       (line 5732)
* gnutls_priority_string_list:           Core TLS API.       (line 5748)
* gnutls_privkey_decrypt_data:           Operations.         (line  144)
* gnutls_privkey_decrypt_data <1>:       Abstract key API.   (line  465)
* gnutls_privkey_decrypt_data2:          Abstract key API.   (line  488)
* gnutls_privkey_deinit:                 Abstract key API.   (line  515)
* gnutls_privkey_derive_secret:          Abstract key API.   (line  525)
* gnutls_privkey_export_dh_raw:          Abstract key API.   (line  551)
* gnutls_privkey_export_dsa_raw:         Abstract key API.   (line  581)
* gnutls_privkey_export_dsa_raw2:        Abstract key API.   (line  608)
* gnutls_privkey_export_ecc_raw:         Abstract key API.   (line  637)
* gnutls_privkey_export_ecc_raw2:        Abstract key API.   (line  665)
* gnutls_privkey_export_gost_raw2:       Abstract key API.   (line  695)
* gnutls_privkey_export_openpgp:         Abstract key API.   (line  731)
* gnutls_privkey_export_pkcs11:          Abstract key API.   (line  746)
* gnutls_privkey_export_rsa_raw:         Abstract key API.   (line  765)
* gnutls_privkey_export_rsa_raw2:        Abstract key API.   (line  800)
* gnutls_privkey_export_x509:            Abstract key API.   (line  836)
* gnutls_privkey_generate:               Abstract key API.   (line  856)
* gnutls_privkey_generate2:              Public key algorithms.
                                                             (line   43)
* gnutls_privkey_generate2 <1>:          Abstract key API.   (line  897)
* gnutls_privkey_get_pk_algorithm:       Abstract key API.   (line  948)
* gnutls_privkey_get_seed:               Abstract key API.   (line  967)
* gnutls_privkey_get_spki:               Abstract key API.   (line  992)
* gnutls_privkey_get_type:               Abstract key API.   (line 1012)
* gnutls_privkey_import_dh_raw:          Abstract key API.   (line 1027)
* gnutls_privkey_import_dsa_raw:         Abstract key API.   (line 1050)
* gnutls_privkey_import_ecc_raw:         Abstract key API.   (line 1076)
* gnutls_privkey_import_ext:             Abstract key API.   (line 1104)
* gnutls_privkey_import_ext2:            Abstract key API.   (line 1139)
* gnutls_privkey_import_ext3:            Abstract key API.   (line 1178)
* gnutls_privkey_import_ext4:            Abstract private keys.
                                                             (line   50)
* gnutls_privkey_import_ext4 <1>:        Abstract key API.   (line 1221)
* gnutls_privkey_import_gost_raw:        Abstract key API.   (line 1286)
* gnutls_privkey_import_openpgp:         Abstract key API.   (line 1322)
* gnutls_privkey_import_openpgp_raw:     Abstract key API.   (line 1339)
* gnutls_privkey_import_pkcs11:          Abstract key API.   (line 1362)
* gnutls_privkey_import_pkcs11_url:      Abstract key API.   (line 1388)
* gnutls_privkey_import_rsa_raw:         Abstract key API.   (line 1405)
* gnutls_privkey_import_tpm_raw:         Abstract key API.   (line 1438)
* gnutls_privkey_import_tpm_url:         Using keys.         (line   16)
* gnutls_privkey_import_tpm_url <1>:     Abstract key API.   (line 1468)
* gnutls_privkey_import_url:             Abstract private keys.
                                                             (line   24)
* gnutls_privkey_import_url <1>:         Abstract key API.   (line 1499)
* gnutls_privkey_import_x509:            Abstract key API.   (line 1519)
* gnutls_privkey_import_x509_raw:        Managing encrypted keys.
                                                             (line   27)
* gnutls_privkey_import_x509_raw <1>:    Abstract key API.   (line 1545)
* gnutls_privkey_init:                   Abstract key API.   (line 1572)
* gnutls_privkey_set_flags:              Abstract key API.   (line 1595)
* gnutls_privkey_set_pin_function:       Abstract key API.   (line 1611)
* gnutls_privkey_set_spki:               Abstract key API.   (line 1631)
* gnutls_privkey_sign_data:              Operations.         (line   80)
* gnutls_privkey_sign_data <1>:          Abstract key API.   (line 1651)
* gnutls_privkey_sign_data2:             Abstract key API.   (line 1682)
* gnutls_privkey_sign_hash:              Operations.         (line  108)
* gnutls_privkey_sign_hash <1>:          Abstract key API.   (line 1710)
* gnutls_privkey_sign_hash2:             Abstract key API.   (line 1749)
* gnutls_privkey_status:                 Abstract key API.   (line 1784)
* gnutls_privkey_verify_params:          Abstract key API.   (line 1800)
* gnutls_privkey_verify_seed:            Abstract key API.   (line 1813)
* gnutls_protocol_get_id:                Core TLS API.       (line 5768)
* gnutls_protocol_get_name:              Core TLS API.       (line 5780)
* gnutls_protocol_get_version:           Core TLS API.       (line 5792)
* gnutls_protocol_list:                  Core TLS API.       (line 5803)
* gnutls_protocol_set_enabled:           Core TLS API.       (line 5815)
* gnutls_psk_allocate_client_credentials: Core TLS API.      (line 5840)
* gnutls_psk_allocate_server_credentials: Core TLS API.      (line 5852)
* gnutls_psk_client_get_hint:            Core TLS API.       (line 5864)
* gnutls_psk_format_imported_identity:   Core TLS API.       (line 5883)
* gnutls_psk_free_client_credentials:    Core TLS API.       (line 5912)
* gnutls_psk_free_server_credentials:    Core TLS API.       (line 5921)
* gnutls_psk_server_get_username:        Core TLS API.       (line 5930)
* gnutls_psk_server_get_username2:       Core TLS API.       (line 5950)
* gnutls_psk_set_client_credentials:     Core TLS API.       (line 5971)
* gnutls_psk_set_client_credentials_function: PSK credentials.
                                                             (line   22)
* gnutls_psk_set_client_credentials_function <1>: Core TLS API.
                                                             (line 6020)
* gnutls_psk_set_client_credentials_function2: Core TLS API. (line 6045)
* gnutls_psk_set_client_credentials_function3: Core TLS API. (line 6074)
* gnutls_psk_set_client_credentials2:    Core TLS API.       (line 5997)
* gnutls_psk_set_params_function:        Core TLS API.       (line 6107)
* gnutls_psk_set_server_credentials_file: PSK credentials.   (line   59)
* gnutls_psk_set_server_credentials_file <1>: Core TLS API.  (line 6125)
* gnutls_psk_set_server_credentials_function: Core TLS API.  (line 6147)
* gnutls_psk_set_server_credentials_function2: Core TLS API. (line 6172)
* gnutls_psk_set_server_credentials_function3: Core TLS API. (line 6201)
* gnutls_psk_set_server_credentials_hint: Core TLS API.      (line 6232)
* gnutls_psk_set_server_dh_params:       Core TLS API.       (line 6251)
* gnutls_psk_set_server_known_dh_params: Core TLS API.       (line 6269)
* gnutls_psk_set_server_params_function: Core TLS API.       (line 6293)
* gnutls_pubkey_deinit:                  Abstract key API.   (line 1837)
* gnutls_pubkey_encrypt_data:            Operations.         (line   60)
* gnutls_pubkey_encrypt_data <1>:        Abstract key API.   (line 1847)
* gnutls_pubkey_export:                  Abstract key API.   (line 1870)
* gnutls_pubkey_export_dh_raw:           Abstract key API.   (line 1926)
* gnutls_pubkey_export_dsa_raw:          Abstract key API.   (line 1955)
* gnutls_pubkey_export_dsa_raw2:         Abstract key API.   (line 1982)
* gnutls_pubkey_export_ecc_raw:          Abstract key API.   (line 2011)
* gnutls_pubkey_export_ecc_raw2:         Abstract key API.   (line 2039)
* gnutls_pubkey_export_ecc_x962:         Abstract key API.   (line 2069)
* gnutls_pubkey_export_gost_raw2:        Abstract key API.   (line 2089)
* gnutls_pubkey_export_rsa_raw:          Abstract key API.   (line 2123)
* gnutls_pubkey_export_rsa_raw2:         Abstract key API.   (line 2145)
* gnutls_pubkey_export2:                 Abstract public keys.
                                                             (line   24)
* gnutls_pubkey_export2 <1>:             Abstract key API.   (line 1901)
* gnutls_pubkey_get_key_id:              Abstract key API.   (line 2169)
* gnutls_pubkey_get_key_usage:           Abstract key API.   (line 2198)
* gnutls_pubkey_get_openpgp_key_id:      Abstract key API.   (line 2215)
* gnutls_pubkey_get_pk_algorithm:        Abstract key API.   (line 2238)
* gnutls_pubkey_get_preferred_hash_algorithm: Abstract key API.
                                                             (line 2257)
* gnutls_pubkey_get_spki:                Abstract key API.   (line 2285)
* gnutls_pubkey_import:                  Abstract key API.   (line 2305)
* gnutls_pubkey_import_dh_raw:           Abstract key API.   (line 2326)
* gnutls_pubkey_import_dsa_raw:          Abstract key API.   (line 2346)
* gnutls_pubkey_import_ecc_raw:          Abstract key API.   (line 2371)
* gnutls_pubkey_import_ecc_x962:         Abstract key API.   (line 2396)
* gnutls_pubkey_import_gost_raw:         Abstract key API.   (line 2416)
* gnutls_pubkey_import_openpgp:          Abstract key API.   (line 2450)
* gnutls_pubkey_import_openpgp_raw:      Abstract key API.   (line 2467)
* gnutls_pubkey_import_pkcs11:           Abstract key API.   (line 2490)
* gnutls_pubkey_import_privkey:          Abstract key API.   (line 2509)
* gnutls_pubkey_import_rsa_raw:          Abstract key API.   (line 2533)
* gnutls_pubkey_import_tpm_raw:          Abstract key API.   (line 2553)
* gnutls_pubkey_import_tpm_url:          Using keys.         (line   44)
* gnutls_pubkey_import_tpm_url <1>:      Abstract key API.   (line 2580)
* gnutls_pubkey_import_url:              Abstract key API.   (line 2608)
* gnutls_pubkey_import_x509:             Abstract key API.   (line 2626)
* gnutls_pubkey_import_x509_crq:         Abstract key API.   (line 2645)
* gnutls_pubkey_import_x509_raw:         Abstract key API.   (line 2664)
* gnutls_pubkey_init:                    Abstract key API.   (line 2686)
* gnutls_pubkey_print:                   Abstract key API.   (line 2699)
* gnutls_pubkey_set_key_usage:           Abstract key API.   (line 2724)
* gnutls_pubkey_set_pin_function:        Abstract key API.   (line 2742)
* gnutls_pubkey_set_spki:                Abstract key API.   (line 2762)
* gnutls_pubkey_verify_data2:            Operations.         (line    9)
* gnutls_pubkey_verify_data2 <1>:        Abstract key API.   (line 2782)
* gnutls_pubkey_verify_hash2:            Operations.         (line   33)
* gnutls_pubkey_verify_hash2 <1>:        Abstract key API.   (line 2809)
* gnutls_pubkey_verify_params:           Abstract key API.   (line 2839)
* gnutls_random_art:                     Core TLS API.       (line 6311)
* gnutls_range_split:                    Core TLS API.       (line 6338)
* gnutls_reauth:                         Core TLS API.       (line 6364)
* gnutls_record_can_use_length_hiding:   Core TLS API.       (line 6410)
* gnutls_record_check_corked:            Core TLS API.       (line 6428)
* gnutls_record_check_pending:           Data transfer and termination.
                                                             (line  138)
* gnutls_record_check_pending <1>:       Core TLS API.       (line 6442)
* gnutls_record_cork:                    Buffered data transfer.
                                                             (line   12)
* gnutls_record_cork <1>:                Core TLS API.       (line 6455)
* gnutls_record_disable_padding:         Core TLS API.       (line 6469)
* gnutls_record_discard_queued:          Core TLS API.       (line 6484)
* gnutls_record_get_direction:           Asynchronous operation.
                                                             (line   65)
* gnutls_record_get_direction <1>:       Core TLS API.       (line 6503)
* gnutls_record_get_discarded:           Datagram TLS API.   (line  209)
* gnutls_record_get_max_early_data_size: Core TLS API.       (line 6526)
* gnutls_record_get_max_size:            Core TLS API.       (line 6542)
* gnutls_record_get_state:               Core TLS API.       (line 6554)
* gnutls_record_overhead_size:           Core TLS API.       (line 6585)
* gnutls_record_recv:                    Data transfer and termination.
                                                             (line   53)
* gnutls_record_recv <1>:                Core TLS API.       (line 6598)
* gnutls_record_recv_early_data:         Core TLS API.       (line 6630)
* gnutls_record_recv_packet:             Core TLS API.       (line 6658)
* gnutls_record_recv_seq:                Data transfer and termination.
                                                             (line  108)
* gnutls_record_recv_seq <1>:            Core TLS API.       (line 6682)
* gnutls_record_send:                    Data transfer and termination.
                                                             (line   12)
* gnutls_record_send <1>:                Core TLS API.       (line 6709)
* gnutls_record_send_early_data:         Core TLS API.       (line 6786)
* gnutls_record_send_file:               Core TLS API.       (line 6814)
* gnutls_record_send_range:              Core TLS API.       (line 6843)
* gnutls_record_send2:                   On Record Padding.  (line   23)
* gnutls_record_send2 <1>:               Core TLS API.       (line 6753)
* gnutls_record_set_max_early_data_size: Core TLS API.       (line 6872)
* gnutls_record_set_max_recv_size:       Core TLS API.       (line 6891)
* gnutls_record_set_max_size:            Core TLS API.       (line 6913)
* gnutls_record_set_state:               Core TLS API.       (line 6942)
* gnutls_record_set_timeout:             Core TLS API.       (line 6963)
* gnutls_record_uncork:                  Buffered data transfer.
                                                             (line   23)
* gnutls_record_uncork <1>:              Core TLS API.       (line 6982)
* gnutls_register_custom_url:            Application-specific keys.
                                                             (line   69)
* gnutls_register_custom_url <1>:        Abstract key API.   (line 2852)
* gnutls_rehandshake:                    TLS 1.2 re-authentication.
                                                             (line   70)
* gnutls_rehandshake <1>:                Core TLS API.       (line 7007)
* gnutls_rnd:                            Random number generation.
                                                             (line   21)
* gnutls_rnd <1>:                        Cryptographic API.  (line 1218)
* gnutls_rnd_refresh:                    Cryptographic API.  (line 1240)
* gnutls_safe_renegotiation_status:      TLS 1.2 re-authentication.
                                                             (line   44)
* gnutls_safe_renegotiation_status <1>:  Core TLS API.       (line 7047)
* gnutls_sec_param_get_name:             Core TLS API.       (line 7062)
* gnutls_sec_param_to_pk_bits:           Selecting cryptographic key sizes.
                                                             (line   75)
* gnutls_sec_param_to_pk_bits <1>:       Core TLS API.       (line 7076)
* gnutls_sec_param_to_symmetric_bits:    Core TLS API.       (line 7095)
* gnutls_server_name_get:                Core TLS API.       (line 7109)
* gnutls_server_name_set:                Core TLS API.       (line 7148)
* gnutls_session_channel_binding:        Core TLS API.       (line 7179)
* gnutls_session_enable_compatibility_mode: Core TLS API.    (line 7200)
* gnutls_session_etm_status:             Core TLS API.       (line 7220)
* gnutls_session_ext_master_secret_status: Core TLS API.     (line 7233)
* gnutls_session_ext_register:           Core TLS API.       (line 7247)
* gnutls_session_force_valid:            Core TLS API.       (line 7303)
* gnutls_session_get_data:               Core TLS API.       (line 7314)
* gnutls_session_get_data2:              Core TLS API.       (line 7334)
* gnutls_session_get_desc:               Core TLS API.       (line 7382)
* gnutls_session_get_flags:              Core TLS API.       (line 7399)
* gnutls_session_get_id:                 Core TLS API.       (line 7418)
* gnutls_session_get_id2:                Session resumption. (line   49)
* gnutls_session_get_id2 <1>:            Core TLS API.       (line 7452)
* gnutls_session_get_keylog_function:    Core TLS API.       (line 7485)
* gnutls_session_get_master_secret:      Core TLS API.       (line 7499)
* gnutls_session_get_ptr:                Core TLS API.       (line 7515)
* gnutls_session_get_random:             Core TLS API.       (line 7527)
* gnutls_session_get_verify_cert_status: Core TLS API.       (line 7547)
* gnutls_session_is_resumed:             Session resumption. (line   40)
* gnutls_session_is_resumed <1>:         Core TLS API.       (line 7567)
* gnutls_session_key_update:             Core TLS API.       (line 7579)
* gnutls_session_resumption_requested:   Session resumption. (line  150)
* gnutls_session_resumption_requested <1>: Core TLS API.     (line 7606)
* gnutls_session_set_data:               Core TLS API.       (line 7619)
* gnutls_session_set_id:                 Core TLS API.       (line 7642)
* gnutls_session_set_keylog_function:    Core TLS API.       (line 7663)
* gnutls_session_set_premaster:          Core TLS API.       (line 7677)
* gnutls_session_set_ptr:                Core TLS API.       (line 7712)
* gnutls_session_set_verify_cert:        Certificate credentials.
                                                             (line  267)
* gnutls_session_set_verify_cert <1>:    Core TLS API.       (line 7725)
* gnutls_session_set_verify_cert2:       Core TLS API.       (line 7758)
* gnutls_session_set_verify_function:    Core TLS API.       (line 7790)
* gnutls_session_set_verify_output_function: X509 certificate API.
                                                             (line  149)
* gnutls_session_supplemental_register:  Core TLS API.       (line 7819)
* gnutls_session_ticket_enable_client:   Core TLS API.       (line 7855)
* gnutls_session_ticket_enable_server:   Session resumption. (line  117)
* gnutls_session_ticket_enable_server <1>: Core TLS API.     (line 7871)
* gnutls_session_ticket_key_generate:    Session resumption. (line  137)
* gnutls_session_ticket_key_generate <1>: Core TLS API.      (line 7894)
* gnutls_session_ticket_send:            Session resumption. (line  170)
* gnutls_session_ticket_send <1>:        Core TLS API.       (line 7910)
* gnutls_set_default_priority:           Core TLS API.       (line 7928)
* gnutls_set_default_priority_append:    Core TLS API.       (line 7954)
* gnutls_sign_algorithm_get:             Core TLS API.       (line 7990)
* gnutls_sign_algorithm_get_client:      Core TLS API.       (line 8004)
* gnutls_sign_algorithm_get_requested:   Core TLS API.       (line 8019)
* gnutls_sign_get_hash_algorithm:        Core TLS API.       (line 8046)
* gnutls_sign_get_id:                    Core TLS API.       (line 8061)
* gnutls_sign_get_name:                  Core TLS API.       (line 8073)
* gnutls_sign_get_oid:                   Core TLS API.       (line 8085)
* gnutls_sign_get_pk_algorithm:          Core TLS API.       (line 8099)
* gnutls_sign_is_secure:                 Core TLS API.       (line 8117)
* gnutls_sign_is_secure2:                Core TLS API.       (line 8127)
* gnutls_sign_list:                      Core TLS API.       (line 8139)
* gnutls_sign_set_secure:                Core TLS API.       (line 8150)
* gnutls_sign_set_secure_for_certs:      Core TLS API.       (line 8179)
* gnutls_sign_supports_pk_algorithm:     Core TLS API.       (line 8209)
* gnutls_srp_allocate_client_credentials: Core TLS API.      (line 8227)
* gnutls_srp_allocate_server_credentials: Core TLS API.      (line 8239)
* gnutls_srp_base64_decode:              Core TLS API.       (line 8251)
* gnutls_srp_base64_decode2:             Core TLS API.       (line 8273)
* gnutls_srp_base64_encode:              Core TLS API.       (line 8293)
* gnutls_srp_base64_encode2:             Core TLS API.       (line 8315)
* gnutls_srp_free_client_credentials:    Core TLS API.       (line 8336)
* gnutls_srp_free_server_credentials:    Core TLS API.       (line 8345)
* gnutls_srp_server_get_username:        Core TLS API.       (line 8354)
* gnutls_srp_set_client_credentials:     Core TLS API.       (line 8367)
* gnutls_srp_set_client_credentials_function: SRP credentials.
                                                             (line   19)
* gnutls_srp_set_client_credentials_function <1>: Core TLS API.
                                                             (line 8390)
* gnutls_srp_set_prime_bits:             Core TLS API.       (line 8423)
* gnutls_srp_set_server_credentials_file: SRP credentials.   (line   56)
* gnutls_srp_set_server_credentials_file <1>: Core TLS API.  (line 8444)
* gnutls_srp_set_server_credentials_function: SRP credentials.
                                                             (line   72)
* gnutls_srp_set_server_credentials_function <1>: Core TLS API.
                                                             (line 8463)
* gnutls_srp_set_server_fake_salt_seed:  Core TLS API.       (line 8501)
* gnutls_srp_verifier:                   Authentication using SRP.
                                                             (line   45)
* gnutls_srp_verifier <1>:               Core TLS API.       (line 8538)
* gnutls_srtp_get_keys:                  SRTP.               (line   35)
* gnutls_srtp_get_keys <1>:              Core TLS API.       (line 8567)
* gnutls_srtp_get_mki:                   Core TLS API.       (line 8605)
* gnutls_srtp_get_profile_id:            Core TLS API.       (line 8623)
* gnutls_srtp_get_profile_name:          Core TLS API.       (line 8639)
* gnutls_srtp_get_selected_profile:      Core TLS API.       (line 8654)
* gnutls_srtp_set_mki:                   Core TLS API.       (line 8670)
* gnutls_srtp_set_profile:               Core TLS API.       (line 8687)
* gnutls_srtp_set_profile_direct:        Core TLS API.       (line 8704)
* gnutls_store_commitment:               Certificate verification.
                                                             (line  115)
* gnutls_store_commitment <1>:           Core TLS API.       (line 8725)
* gnutls_store_pubkey:                   Certificate verification.
                                                             (line   64)
* gnutls_store_pubkey <1>:               Core TLS API.       (line 8765)
* gnutls_strerror:                       Core TLS API.       (line 8814)
* gnutls_strerror_name:                  Core TLS API.       (line 8828)
* gnutls_subject_alt_names_deinit:       X509 certificate API.
                                                             (line  178)
* gnutls_subject_alt_names_get:          X509 certificate API.
                                                             (line  189)
* gnutls_subject_alt_names_init:         X509 certificate API.
                                                             (line  218)
* gnutls_subject_alt_names_set:          X509 certificate API.
                                                             (line  232)
* gnutls_supplemental_get_name:          Core TLS API.       (line 8843)
* gnutls_supplemental_recv:              Core TLS API.       (line 8856)
* gnutls_supplemental_register:          Core TLS API.       (line 8871)
* gnutls_supplemental_send:              Core TLS API.       (line 8902)
* gnutls_system_key_add_x509:            Abstract key API.   (line 2878)
* gnutls_system_key_delete:              Abstract key API.   (line 2904)
* gnutls_system_key_iter_deinit:         Abstract key API.   (line 2920)
* gnutls_system_key_iter_get_info:       Application-specific keys.
                                                             (line   20)
* gnutls_system_key_iter_get_info <1>:   Abstract key API.   (line 2931)
* gnutls_system_recv_timeout:            Core TLS API.       (line 8916)
* gnutls_tdb_deinit:                     Core TLS API.       (line 8939)
* gnutls_tdb_init:                       Core TLS API.       (line 8948)
* gnutls_tdb_set_store_commitment_func:  Core TLS API.       (line 8959)
* gnutls_tdb_set_store_func:             Core TLS API.       (line 8979)
* gnutls_tdb_set_verify_func:            Core TLS API.       (line 8998)
* gnutls_tpm_get_registered:             TPM API.            (line   12)
* gnutls_tpm_key_list_deinit:            TPM API.            (line   27)
* gnutls_tpm_key_list_get_url:           TPM API.            (line   38)
* gnutls_tpm_privkey_delete:             Key generation.     (line   52)
* gnutls_tpm_privkey_delete <1>:         Using keys.         (line   82)
* gnutls_tpm_privkey_delete <2>:         TPM API.            (line   60)
* gnutls_tpm_privkey_generate:           Key generation.     (line    9)
* gnutls_tpm_privkey_generate <1>:       TPM API.            (line   76)
* gnutls_transport_get_int:              Core TLS API.       (line 9020)
* gnutls_transport_get_int2:             Core TLS API.       (line 9034)
* gnutls_transport_get_ptr:              Core TLS API.       (line 9051)
* gnutls_transport_get_ptr2:             Core TLS API.       (line 9064)
* gnutls_transport_is_ktls_enabled:      Core TLS API.       (line 9080)
* gnutls_transport_set_errno:            Setting up the transport layer.
                                                             (line  116)
* gnutls_transport_set_errno <1>:        Core TLS API.       (line 9094)
* gnutls_transport_set_errno_function:   Core TLS API.       (line 9117)
* gnutls_transport_set_fastopen:         Reducing round-trips.
                                                             (line   22)
* gnutls_transport_set_fastopen <1>:     Socket specific API.
                                                             (line   11)
* gnutls_transport_set_int:              Core TLS API.       (line 9135)
* gnutls_transport_set_int2:             Core TLS API.       (line 9153)
* gnutls_transport_set_ptr:              Core TLS API.       (line 9175)
* gnutls_transport_set_ptr2:             Core TLS API.       (line 9188)
* gnutls_transport_set_pull_function:    Setting up the transport layer.
                                                             (line   56)
* gnutls_transport_set_pull_function <1>: Core TLS API.      (line 9205)
* gnutls_transport_set_pull_timeout_function: Setting up the transport layer.
                                                             (line   71)
* gnutls_transport_set_pull_timeout_function <1>: Setting up the transport layer.
                                                             (line  156)
* gnutls_transport_set_pull_timeout_function <2>: Core TLS API.
                                                             (line 9223)
* gnutls_transport_set_push_function:    Setting up the transport layer.
                                                             (line   23)
* gnutls_transport_set_push_function <1>: Core TLS API.      (line 9263)
* gnutls_transport_set_vec_push_function: Setting up the transport layer.
                                                             (line   40)
* gnutls_transport_set_vec_push_function <1>: Core TLS API.  (line 9283)
* gnutls_url_is_supported:               Abstract public keys.
                                                             (line   57)
* gnutls_url_is_supported <1>:           Core TLS API.       (line 9302)
* gnutls_utf8_password_normalize:        Core TLS API.       (line 9316)
* gnutls_verify_stored_pubkey:           Certificate verification.
                                                             (line   18)
* gnutls_verify_stored_pubkey <1>:       Core TLS API.       (line 9341)
* gnutls_x509_aia_deinit:                X509 certificate API.
                                                             (line  259)
* gnutls_x509_aia_get:                   X509 certificate API.
                                                             (line  269)
* gnutls_x509_aia_init:                  X509 certificate API.
                                                             (line  303)
* gnutls_x509_aia_set:                   X509 certificate API.
                                                             (line  316)
* gnutls_x509_aki_deinit:                X509 certificate API.
                                                             (line  343)
* gnutls_x509_aki_get_cert_issuer:       X509 certificate API.
                                                             (line  353)
* gnutls_x509_aki_get_id:                X509 certificate API.
                                                             (line  385)
* gnutls_x509_aki_init:                  X509 certificate API.
                                                             (line  403)
* gnutls_x509_aki_set_cert_issuer:       X509 certificate API.
                                                             (line  416)
* gnutls_x509_aki_set_id:                X509 certificate API.
                                                             (line  448)
* gnutls_x509_cidr_to_rfc5280:           X509 certificate API.
                                                             (line  465)
* gnutls_x509_crl_check_issuer:          X509 certificate API.
                                                             (line  490)
* gnutls_x509_crl_deinit:                X509 certificate API.
                                                             (line  505)
* gnutls_x509_crl_dist_points_deinit:    X509 certificate API.
                                                             (line  513)
* gnutls_x509_crl_dist_points_get:       X509 certificate API.
                                                             (line  524)
* gnutls_x509_crl_dist_points_init:      X509 certificate API.
                                                             (line  550)
* gnutls_x509_crl_dist_points_set:       X509 certificate API.
                                                             (line  564)
* gnutls_x509_crl_export:                X509 certificate API.
                                                             (line  588)
* gnutls_x509_crl_export2:               X509 certificate API.
                                                             (line  614)
* gnutls_x509_crl_get_authority_key_gn_serial: X509 certificate API.
                                                             (line  637)
* gnutls_x509_crl_get_authority_key_id:  X509 certificate API.
                                                             (line  674)
* gnutls_x509_crl_get_crt_count:         X509 certificate API.
                                                             (line  702)
* gnutls_x509_crl_get_crt_serial:        PKIX certificate revocation lists.
                                                             (line   55)
* gnutls_x509_crl_get_crt_serial <1>:    X509 certificate API.
                                                             (line  714)
* gnutls_x509_crl_get_dn_oid:            X509 certificate API.
                                                             (line  740)
* gnutls_x509_crl_get_extension_data:    X509 certificate API.
                                                             (line  763)
* gnutls_x509_crl_get_extension_data2:   X509 certificate API.
                                                             (line  793)
* gnutls_x509_crl_get_extension_info:    X509 certificate API.
                                                             (line  816)
* gnutls_x509_crl_get_extension_oid:     X509 certificate API.
                                                             (line  850)
* gnutls_x509_crl_get_issuer_dn:         X509 certificate API.
                                                             (line  875)
* gnutls_x509_crl_get_issuer_dn_by_oid:  X509 certificate API.
                                                             (line  949)
* gnutls_x509_crl_get_issuer_dn2:        X509 certificate API.
                                                             (line  900)
* gnutls_x509_crl_get_issuer_dn3:        X509 certificate API.
                                                             (line  923)
* gnutls_x509_crl_get_next_update:       X509 certificate API.
                                                             (line  985)
* gnutls_x509_crl_get_number:            X509 certificate API.
                                                             (line  999)
* gnutls_x509_crl_get_raw_issuer_dn:     X509 certificate API.
                                                             (line 1021)
* gnutls_x509_crl_get_signature:         X509 certificate API.
                                                             (line 1037)
* gnutls_x509_crl_get_signature_algorithm: X509 certificate API.
                                                             (line 1054)
* gnutls_x509_crl_get_signature_oid:     X509 certificate API.
                                                             (line 1070)
* gnutls_x509_crl_get_this_update:       X509 certificate API.
                                                             (line 1090)
* gnutls_x509_crl_get_version:           X509 certificate API.
                                                             (line 1101)
* gnutls_x509_crl_import:                X509 certificate API.
                                                             (line 1111)
* gnutls_x509_crl_init:                  X509 certificate API.
                                                             (line 1131)
* gnutls_x509_crl_iter_crt_serial:       X509 certificate API.
                                                             (line 1146)
* gnutls_x509_crl_iter_deinit:           X509 certificate API.
                                                             (line 1179)
* gnutls_x509_crl_list_import:           X509 certificate API.
                                                             (line 1188)
* gnutls_x509_crl_list_import2:          X509 certificate API.
                                                             (line 1219)
* gnutls_x509_crl_print:                 X509 certificate API.
                                                             (line 1248)
* gnutls_x509_crl_privkey_sign:          PKIX certificate revocation lists.
                                                             (line  132)
* gnutls_x509_crl_privkey_sign <1>:      Abstract key API.   (line 2969)
* gnutls_x509_crl_set_authority_key_id:  X509 certificate API.
                                                             (line 1268)
* gnutls_x509_crl_set_crt:               X509 certificate API.
                                                             (line 1289)
* gnutls_x509_crl_set_crt_serial:        X509 certificate API.
                                                             (line 1307)
* gnutls_x509_crl_set_next_update:       X509 certificate API.
                                                             (line 1327)
* gnutls_x509_crl_set_number:            X509 certificate API.
                                                             (line 1346)
* gnutls_x509_crl_set_this_update:       X509 certificate API.
                                                             (line 1366)
* gnutls_x509_crl_set_version:           X509 certificate API.
                                                             (line 1380)
* gnutls_x509_crl_sign:                  X509 certificate API.
                                                             (line 1396)
* gnutls_x509_crl_sign2:                 PKIX certificate revocation lists.
                                                             (line  101)
* gnutls_x509_crl_sign2 <1>:             X509 certificate API.
                                                             (line 1415)
* gnutls_x509_crl_verify:                X509 certificate API.
                                                             (line 1449)
* gnutls_x509_crq_deinit:                X509 certificate API.
                                                             (line 1482)
* gnutls_x509_crq_export:                X509 certificate API.
                                                             (line 1491)
* gnutls_x509_crq_export2:               X509 certificate API.
                                                             (line 1519)
* gnutls_x509_crq_get_attribute_by_oid:  X509 certificate API.
                                                             (line 1543)
* gnutls_x509_crq_get_attribute_data:    X509 certificate API.
                                                             (line 1572)
* gnutls_x509_crq_get_attribute_info:    X509 certificate API.
                                                             (line 1601)
* gnutls_x509_crq_get_basic_constraints: X509 certificate API.
                                                             (line 1632)
* gnutls_x509_crq_get_challenge_password: X509 certificate API.
                                                             (line 1662)
* gnutls_x509_crq_get_dn:                X509 certificate API.
                                                             (line 1680)
* gnutls_x509_crq_get_dn_by_oid:         X509 certificate API.
                                                             (line 1755)
* gnutls_x509_crq_get_dn_oid:            X509 certificate API.
                                                             (line 1789)
* gnutls_x509_crq_get_dn2:               X509 certificate API.
                                                             (line 1704)
* gnutls_x509_crq_get_dn3:               X509 certificate API.
                                                             (line 1728)
* gnutls_x509_crq_get_extension_by_oid:  X509 certificate API.
                                                             (line 1809)
* gnutls_x509_crq_get_extension_by_oid2: X509 certificate API.
                                                             (line 1839)
* gnutls_x509_crq_get_extension_data:    X509 certificate API.
                                                             (line 1867)
* gnutls_x509_crq_get_extension_data2:   X509 certificate API.
                                                             (line 1897)
* gnutls_x509_crq_get_extension_info:    X509 certificate API.
                                                             (line 1922)
* gnutls_x509_crq_get_key_id:            X509 certificate API.
                                                             (line 1956)
* gnutls_x509_crq_get_key_purpose_oid:   X509 certificate API.
                                                             (line 1985)
* gnutls_x509_crq_get_key_rsa_raw:       X509 certificate API.
                                                             (line 2013)
* gnutls_x509_crq_get_key_usage:         X509 certificate API.
                                                             (line 2033)
* gnutls_x509_crq_get_pk_algorithm:      X509 certificate API.
                                                             (line 2059)
* gnutls_x509_crq_get_pk_oid:            X509 certificate API.
                                                             (line 2079)
* gnutls_x509_crq_get_private_key_usage_period: X509 certificate API.
                                                             (line 2098)
* gnutls_x509_crq_get_signature_algorithm: X509 certificate API.
                                                             (line 2119)
* gnutls_x509_crq_get_signature_oid:     X509 certificate API.
                                                             (line 2138)
* gnutls_x509_crq_get_spki:              X509 certificate API.
                                                             (line 2158)
* gnutls_x509_crq_get_subject_alt_name:  X509 certificate API.
                                                             (line 2174)
* gnutls_x509_crq_get_subject_alt_othername_oid: X509 certificate API.
                                                             (line 2211)
* gnutls_x509_crq_get_tlsfeatures:       X509 certificate API.
                                                             (line 2247)
* gnutls_x509_crq_get_version:           X509 certificate API.
                                                             (line 2279)
* gnutls_x509_crq_import:                X509 certificate API.
                                                             (line 2291)
* gnutls_x509_crq_init:                  X509 certificate API.
                                                             (line 2312)
* gnutls_x509_crq_print:                 X509 certificate API.
                                                             (line 2324)
* gnutls_x509_crq_privkey_sign:          Abstract key API.   (line 3005)
* gnutls_x509_crq_set_attribute_by_oid:  X509 certificate API.
                                                             (line 2346)
* gnutls_x509_crq_set_basic_constraints: X509 certificate API.
                                                             (line 2371)
* gnutls_x509_crq_set_challenge_password: X509 certificate API.
                                                             (line 2393)
* gnutls_x509_crq_set_dn:                X509 certificate API.
                                                             (line 2408)
* gnutls_x509_crq_set_dn_by_oid:         X509 certificate API.
                                                             (line 2426)
* gnutls_x509_crq_set_extension_by_oid:  X509 certificate API.
                                                             (line 2455)
* gnutls_x509_crq_set_key:               PKCS 10 certificate requests.
                                                             (line   31)
* gnutls_x509_crq_set_key <1>:           X509 certificate API.
                                                             (line 2479)
* gnutls_x509_crq_set_key_purpose_oid:   X509 certificate API.
                                                             (line 2494)
* gnutls_x509_crq_set_key_rsa_raw:       X509 certificate API.
                                                             (line 2516)
* gnutls_x509_crq_set_key_usage:         X509 certificate API.
                                                             (line 2535)
* gnutls_x509_crq_set_private_key_usage_period: X509 certificate API.
                                                             (line 2551)
* gnutls_x509_crq_set_pubkey:            Operations.         (line  168)
* gnutls_x509_crq_set_pubkey <1>:        Abstract key API.   (line 3042)
* gnutls_x509_crq_set_spki:              X509 certificate API.
                                                             (line 2568)
* gnutls_x509_crq_set_subject_alt_name:  X509 certificate API.
                                                             (line 2592)
* gnutls_x509_crq_set_subject_alt_othername: X509 certificate API.
                                                             (line 2617)
* gnutls_x509_crq_set_tlsfeatures:       X509 certificate API.
                                                             (line 2645)
* gnutls_x509_crq_set_version:           X509 certificate API.
                                                             (line 2663)
* gnutls_x509_crq_sign:                  X509 certificate API.
                                                             (line 2678)
* gnutls_x509_crq_sign2:                 PKCS 10 certificate requests.
                                                             (line   43)
* gnutls_x509_crq_sign2 <1>:             X509 certificate API.
                                                             (line 2699)
* gnutls_x509_crq_verify:                X509 certificate API.
                                                             (line 2734)
* gnutls_x509_crt_check_email:           X509 certificate API.
                                                             (line 2753)
* gnutls_x509_crt_check_hostname:        X509 certificate API.
                                                             (line 2770)
* gnutls_x509_crt_check_hostname2:       X509 certificate API.
                                                             (line 2788)
* gnutls_x509_crt_check_ip:              X509 certificate API.
                                                             (line 2829)
* gnutls_x509_crt_check_issuer:          X509 certificate API.
                                                             (line 2849)
* gnutls_x509_crt_check_key_purpose:     X509 certificate API.
                                                             (line 2868)
* gnutls_x509_crt_check_revocation:      X509 certificate API.
                                                             (line 2889)
* gnutls_x509_crt_cpy_crl_dist_points:   X509 certificate API.
                                                             (line 2907)
* gnutls_x509_crt_deinit:                X509 certificate API.
                                                             (line 2923)
* gnutls_x509_crt_equals:                X509 certificate API.
                                                             (line 2931)
* gnutls_x509_crt_equals2:               X509 certificate API.
                                                             (line 2946)
* gnutls_x509_crt_export:                X509 certificate API.
                                                             (line 2962)
* gnutls_x509_crt_export2:               X509 certificate API.
                                                             (line 2989)
* gnutls_x509_crt_get_activation_time:   X509 certificate API.
                                                             (line 3011)
* gnutls_x509_crt_get_authority_info_access: X509 certificate API.
                                                             (line 3023)
* gnutls_x509_crt_get_authority_key_gn_serial: X509 certificate API.
                                                             (line 3101)
* gnutls_x509_crt_get_authority_key_id:  X509 certificate API.
                                                             (line 3140)
* gnutls_x509_crt_get_basic_constraints: X509 certificate API.
                                                             (line 3167)
* gnutls_x509_crt_get_ca_status:         X509 certificate API.
                                                             (line 3195)
* gnutls_x509_crt_get_crl_dist_points:   X509 certificate API.
                                                             (line 3218)
* gnutls_x509_crt_get_dn:                X509 certificate API.
                                                             (line 3251)
* gnutls_x509_crt_get_dn_by_oid:         X509 certificate API.
                                                             (line 3329)
* gnutls_x509_crt_get_dn_oid:            X509 certificate API.
                                                             (line 3368)
* gnutls_x509_crt_get_dn2:               X.509 distinguished names.
                                                             (line   14)
* gnutls_x509_crt_get_dn2 <1>:           X509 certificate API.
                                                             (line 3280)
* gnutls_x509_crt_get_dn3:               X509 certificate API.
                                                             (line 3303)
* gnutls_x509_crt_get_expiration_time:   X509 certificate API.
                                                             (line 3394)
* gnutls_x509_crt_get_extension_by_oid:  X509 certificate API.
                                                             (line 3406)
* gnutls_x509_crt_get_extension_by_oid2: X509 certificate API.
                                                             (line 3434)
* gnutls_x509_crt_get_extension_data:    X509 certificate API.
                                                             (line 3462)
* gnutls_x509_crt_get_extension_data2:   X509 certificate API.
                                                             (line 3490)
* gnutls_x509_crt_get_extension_info:    X509 certificate API.
                                                             (line 3513)
* gnutls_x509_crt_get_extension_oid:     X509 certificate API.
                                                             (line 3547)
* gnutls_x509_crt_get_fingerprint:       X509 certificate API.
                                                             (line 3573)
* gnutls_x509_crt_get_inhibit_anypolicy: X509 certificate API.
                                                             (line 3597)
* gnutls_x509_crt_get_issuer:            X509 certificate API.
                                                             (line 3624)
* gnutls_x509_crt_get_issuer_alt_name:   X509 certificate API.
                                                             (line 3642)
* gnutls_x509_crt_get_issuer_alt_name2:  X509 certificate API.
                                                             (line 3685)
* gnutls_x509_crt_get_issuer_alt_othername_oid: X509 certificate API.
                                                             (line 3723)
* gnutls_x509_crt_get_issuer_dn:         X509 certificate API.
                                                             (line 3763)
* gnutls_x509_crt_get_issuer_dn_by_oid:  X509 certificate API.
                                                             (line 3839)
* gnutls_x509_crt_get_issuer_dn_oid:     X509 certificate API.
                                                             (line 3878)
* gnutls_x509_crt_get_issuer_dn2:        X509 certificate API.
                                                             (line 3790)
* gnutls_x509_crt_get_issuer_dn3:        X509 certificate API.
                                                             (line 3813)
* gnutls_x509_crt_get_issuer_unique_id:  X509 certificate API.
                                                             (line 3904)
* gnutls_x509_crt_get_key_id:            X.509 public and private keys.
                                                             (line   13)
* gnutls_x509_crt_get_key_id <1>:        X509 certificate API.
                                                             (line 3933)
* gnutls_x509_crt_get_key_purpose_oid:   X509 certificate API.
                                                             (line 3960)
* gnutls_x509_crt_get_key_usage:         X509 certificate API.
                                                             (line 3990)
* gnutls_x509_crt_get_name_constraints:  X509 certificate API.
                                                             (line 4013)
* gnutls_x509_crt_get_pk_algorithm:      X509 certificate API.
                                                             (line 4048)
* gnutls_x509_crt_get_pk_dsa_raw:        X509 certificate API.
                                                             (line 4070)
* gnutls_x509_crt_get_pk_ecc_raw:        X509 certificate API.
                                                             (line 4093)
* gnutls_x509_crt_get_pk_gost_raw:       X509 certificate API.
                                                             (line 4119)
* gnutls_x509_crt_get_pk_oid:            X509 certificate API.
                                                             (line 4147)
* gnutls_x509_crt_get_pk_rsa_raw:        X509 certificate API.
                                                             (line 4166)
* gnutls_x509_crt_get_policy:            X509 certificate API.
                                                             (line 4184)
* gnutls_x509_crt_get_preferred_hash_algorithm: Compatibility API.
                                                             (line  127)
* gnutls_x509_crt_get_private_key_usage_period: X509 certificate API.
                                                             (line 4211)
* gnutls_x509_crt_get_proxy:             X509 certificate API.
                                                             (line 4233)
* gnutls_x509_crt_get_raw_dn:            X509 certificate API.
                                                             (line 4260)
* gnutls_x509_crt_get_raw_issuer_dn:     X509 certificate API.
                                                             (line 4277)
* gnutls_x509_crt_get_serial:            X509 certificate API.
                                                             (line 4293)
* gnutls_x509_crt_get_signature:         X509 certificate API.
                                                             (line 4313)
* gnutls_x509_crt_get_signature_algorithm: X509 certificate API.
                                                             (line 4330)
* gnutls_x509_crt_get_signature_oid:     X509 certificate API.
                                                             (line 4347)
* gnutls_x509_crt_get_spki:              X509 certificate API.
                                                             (line 4367)
* gnutls_x509_crt_get_subject:           X509 certificate API.
                                                             (line 4383)
* gnutls_x509_crt_get_subject_alt_name:  X509 certificate API.
                                                             (line 4401)
* gnutls_x509_crt_get_subject_alt_name2: X509 certificate API.
                                                             (line 4441)
* gnutls_x509_crt_get_subject_alt_othername_oid: X509 certificate API.
                                                             (line 4477)
* gnutls_x509_crt_get_subject_key_id:    X509 certificate API.
                                                             (line 4515)
* gnutls_x509_crt_get_subject_unique_id: X509 certificate API.
                                                             (line 4537)
* gnutls_x509_crt_get_tlsfeatures:       X509 certificate API.
                                                             (line 4564)
* gnutls_x509_crt_get_version:           X509 certificate API.
                                                             (line 4596)
* gnutls_x509_crt_import:                X509 certificate API.
                                                             (line 4607)
* gnutls_x509_crt_import_pkcs11:         PKCS 11 API.        (line 1358)
* gnutls_x509_crt_import_url:            X509 certificate API.
                                                             (line 4628)
* gnutls_x509_crt_init:                  X509 certificate API.
                                                             (line 4652)
* gnutls_x509_crt_list_import:           X509 certificate API.
                                                             (line 4663)
* gnutls_x509_crt_list_import_pkcs11:    PKCS 11 API.        (line 1375)
* gnutls_x509_crt_list_import_url:       X509 certificate API.
                                                             (line 4731)
* gnutls_x509_crt_list_import2:          X509 certificate API.
                                                             (line 4699)
* gnutls_x509_crt_list_verify:           X509 certificate API.
                                                             (line 4766)
* gnutls_x509_crt_print:                 X509 certificate API.
                                                             (line 4806)
* gnutls_x509_crt_privkey_sign:          Abstract key API.   (line 3059)
* gnutls_x509_crt_set_activation_time:   X509 certificate API.
                                                             (line 4831)
* gnutls_x509_crt_set_authority_info_access: X509 certificate API.
                                                             (line 4846)
* gnutls_x509_crt_set_authority_key_id:  X509 certificate API.
                                                             (line 4872)
* gnutls_x509_crt_set_basic_constraints: X509 certificate API.
                                                             (line 4890)
* gnutls_x509_crt_set_ca_status:         X509 certificate API.
                                                             (line 4910)
* gnutls_x509_crt_set_crl_dist_points:   X509 certificate API.
                                                             (line 4927)
* gnutls_x509_crt_set_crl_dist_points2:  X509 certificate API.
                                                             (line 4947)
* gnutls_x509_crt_set_crq:               X509 certificate API.
                                                             (line 4972)
* gnutls_x509_crt_set_crq_extension_by_oid: X509 certificate API.
                                                             (line 4992)
* gnutls_x509_crt_set_crq_extensions:    X509 certificate API.
                                                             (line 5014)
* gnutls_x509_crt_set_dn:                X509 certificate API.
                                                             (line 5031)
* gnutls_x509_crt_set_dn_by_oid:         X509 certificate API.
                                                             (line 5054)
* gnutls_x509_crt_set_expiration_time:   X509 certificate API.
                                                             (line 5083)
* gnutls_x509_crt_set_extension_by_oid:  X509 certificate API.
                                                             (line 5099)
* gnutls_x509_crt_set_flags:             X509 certificate API.
                                                             (line 5123)
* gnutls_x509_crt_set_inhibit_anypolicy: X509 certificate API.
                                                             (line 5139)
* gnutls_x509_crt_set_issuer_alt_name:   X509 certificate API.
                                                             (line 5154)
* gnutls_x509_crt_set_issuer_alt_othername: X509 certificate API.
                                                             (line 5184)
* gnutls_x509_crt_set_issuer_dn:         X509 certificate API.
                                                             (line 5215)
* gnutls_x509_crt_set_issuer_dn_by_oid:  X509 certificate API.
                                                             (line 5233)
* gnutls_x509_crt_set_issuer_unique_id:  X509 certificate API.
                                                             (line 5266)
* gnutls_x509_crt_set_key:               X509 certificate API.
                                                             (line 5285)
* gnutls_x509_crt_set_key_purpose_oid:   X509 certificate API.
                                                             (line 5303)
* gnutls_x509_crt_set_key_usage:         X509 certificate API.
                                                             (line 5323)
* gnutls_x509_crt_set_name_constraints:  X509 certificate API.
                                                             (line 5337)
* gnutls_x509_crt_set_pin_function:      X509 certificate API.
                                                             (line 5358)
* gnutls_x509_crt_set_policy:            X509 certificate API.
                                                             (line 5378)
* gnutls_x509_crt_set_private_key_usage_period: X509 certificate API.
                                                             (line 5402)
* gnutls_x509_crt_set_proxy:             X509 certificate API.
                                                             (line 5419)
* gnutls_x509_crt_set_proxy_dn:          X509 certificate API.
                                                             (line 5442)
* gnutls_x509_crt_set_pubkey:            Operations.         (line  182)
* gnutls_x509_crt_set_pubkey <1>:        Abstract key API.   (line 3093)
* gnutls_x509_crt_set_serial:            X509 certificate API.
                                                             (line 5468)
* gnutls_x509_crt_set_spki:              X509 certificate API.
                                                             (line 5494)
* gnutls_x509_crt_set_subject_alt_name:  X509 certificate API.
                                                             (line 5518)
* gnutls_x509_crt_set_subject_alt_othername: X509 certificate API.
                                                             (line 5551)
* gnutls_x509_crt_set_subject_alternative_name: X509 certificate API.
                                                             (line 5582)
* gnutls_x509_crt_set_subject_key_id:    X509 certificate API.
                                                             (line 5606)
* gnutls_x509_crt_set_subject_unique_id: X509 certificate API.
                                                             (line 5623)
* gnutls_x509_crt_set_tlsfeatures:       X509 certificate API.
                                                             (line 5642)
* gnutls_x509_crt_set_version:           X509 certificate API.
                                                             (line 5660)
* gnutls_x509_crt_sign:                  X509 certificate API.
                                                             (line 5682)
* gnutls_x509_crt_sign2:                 X509 certificate API.
                                                             (line 5701)
* gnutls_x509_crt_verify:                X509 certificate API.
                                                             (line 5735)
* gnutls_x509_crt_verify_data2:          X509 certificate API.
                                                             (line 5762)
* gnutls_x509_ct_sct_get:                X509 certificate API.
                                                             (line 5792)
* gnutls_x509_ct_sct_get_version:        X509 certificate API.
                                                             (line 5823)
* gnutls_x509_dn_deinit:                 X509 certificate API.
                                                             (line 5845)
* gnutls_x509_dn_export:                 X509 certificate API.
                                                             (line 5856)
* gnutls_x509_dn_export2:                X509 certificate API.
                                                             (line 5883)
* gnutls_x509_dn_get_rdn_ava:            X.509 distinguished names.
                                                             (line   53)
* gnutls_x509_dn_get_rdn_ava <1>:        X509 certificate API.
                                                             (line 5906)
* gnutls_x509_dn_get_str:                X509 certificate API.
                                                             (line 5937)
* gnutls_x509_dn_get_str2:               X509 certificate API.
                                                             (line 5956)
* gnutls_x509_dn_import:                 X509 certificate API.
                                                             (line 5981)
* gnutls_x509_dn_init:                   X509 certificate API.
                                                             (line 6000)
* gnutls_x509_dn_oid_known:              X509 certificate API.
                                                             (line 6016)
* gnutls_x509_dn_oid_name:               X509 certificate API.
                                                             (line 6032)
* gnutls_x509_dn_set_str:                X509 certificate API.
                                                             (line 6049)
* gnutls_x509_ext_ct_export_scts:        X509 certificate API.
                                                             (line 6069)
* gnutls_x509_ext_ct_import_scts:        X509 certificate API.
                                                             (line 6087)
* gnutls_x509_ext_ct_scts_deinit:        X509 certificate API.
                                                             (line 6109)
* gnutls_x509_ext_ct_scts_init:          X509 certificate API.
                                                             (line 6119)
* gnutls_x509_ext_deinit:                X509 certificate API.
                                                             (line 6131)
* gnutls_x509_ext_export_aia:            X509 certificate API.
                                                             (line 6141)
* gnutls_x509_ext_export_authority_key_id: X509 certificate API.
                                                             (line 6160)
* gnutls_x509_ext_export_basic_constraints: X509 certificate API.
                                                             (line 6179)
* gnutls_x509_ext_export_crl_dist_points: X509 certificate API.
                                                             (line 6200)
* gnutls_x509_ext_export_inhibit_anypolicy: X509 certificate API.
                                                             (line 6220)
* gnutls_x509_ext_export_key_purposes:   X509 certificate API.
                                                             (line 6240)
* gnutls_x509_ext_export_key_usage:      X509 certificate API.
                                                             (line 6259)
* gnutls_x509_ext_export_name_constraints: X509 certificate API.
                                                             (line 6278)
* gnutls_x509_ext_export_policies:       X509 certificate API.
                                                             (line 6297)
* gnutls_x509_ext_export_private_key_usage_period: X509 certificate API.
                                                             (line 6317)
* gnutls_x509_ext_export_proxy:          X509 certificate API.
                                                             (line 6338)
* gnutls_x509_ext_export_subject_alt_names: X509 certificate API.
                                                             (line 6367)
* gnutls_x509_ext_export_subject_key_id: X509 certificate API.
                                                             (line 6386)
* gnutls_x509_ext_export_tlsfeatures:    X509 certificate API.
                                                             (line 6405)
* gnutls_x509_ext_import_aia:            X509 certificate API.
                                                             (line 6424)
* gnutls_x509_ext_import_authority_key_id: X509 certificate API.
                                                             (line 6445)
* gnutls_x509_ext_import_basic_constraints: X509 certificate API.
                                                             (line 6466)
* gnutls_x509_ext_import_crl_dist_points: X509 certificate API.
                                                             (line 6485)
* gnutls_x509_ext_import_inhibit_anypolicy: X509 certificate API.
                                                             (line 6505)
* gnutls_x509_ext_import_key_purposes:   X509 certificate API.
                                                             (line 6529)
* gnutls_x509_ext_import_key_usage:      X509 certificate API.
                                                             (line 6550)
* gnutls_x509_ext_import_name_constraints: X509 certificate API.
                                                             (line 6574)
* gnutls_x509_ext_import_policies:       X509 certificate API.
                                                             (line 6607)
* gnutls_x509_ext_import_private_key_usage_period: X509 certificate API.
                                                             (line 6626)
* gnutls_x509_ext_import_proxy:          X509 certificate API.
                                                             (line 6646)
* gnutls_x509_ext_import_subject_alt_names: X509 certificate API.
                                                             (line 6675)
* gnutls_x509_ext_import_subject_key_id: X509 certificate API.
                                                             (line 6699)
* gnutls_x509_ext_import_tlsfeatures:    X509 certificate API.
                                                             (line 6718)
* gnutls_x509_ext_print:                 X509 certificate API.
                                                             (line 6744)
* gnutls_x509_key_purpose_deinit:        X509 certificate API.
                                                             (line 6766)
* gnutls_x509_key_purpose_get:           X509 certificate API.
                                                             (line 6777)
* gnutls_x509_key_purpose_init:          X509 certificate API.
                                                             (line 6800)
* gnutls_x509_key_purpose_set:           X509 certificate API.
                                                             (line 6814)
* gnutls_x509_name_constraints_add_excluded: X509 certificate API.
                                                             (line 6830)
* gnutls_x509_name_constraints_add_permitted: X509 certificate API.
                                                             (line 6856)
* gnutls_x509_name_constraints_check:    X509 certificate API.
                                                             (line 6881)
* gnutls_x509_name_constraints_check_crt: X509 certificate API.
                                                             (line 6906)
* gnutls_x509_name_constraints_deinit:   X509 certificate API.
                                                             (line 6931)
* gnutls_x509_name_constraints_get_excluded: X509 certificate API.
                                                             (line 6942)
* gnutls_x509_name_constraints_get_permitted: X509 certificate API.
                                                             (line 6972)
* gnutls_x509_name_constraints_init:     X509 certificate API.
                                                             (line 7002)
* gnutls_x509_othername_to_virtual:      X509 certificate API.
                                                             (line 7016)
* gnutls_x509_policies_deinit:           X509 certificate API.
                                                             (line 7038)
* gnutls_x509_policies_get:              X509 certificate API.
                                                             (line 7049)
* gnutls_x509_policies_init:             X509 certificate API.
                                                             (line 7074)
* gnutls_x509_policies_set:              X509 certificate API.
                                                             (line 7088)
* gnutls_x509_policy_release:            X509 certificate API.
                                                             (line 7105)
* gnutls_x509_privkey_cpy:               X509 certificate API.
                                                             (line 7118)
* gnutls_x509_privkey_deinit:            X509 certificate API.
                                                             (line 7133)
* gnutls_x509_privkey_export:            X509 certificate API.
                                                             (line 7142)
* gnutls_x509_privkey_export_dsa_raw:    X509 certificate API.
                                                             (line 7233)
* gnutls_x509_privkey_export_ecc_raw:    X509 certificate API.
                                                             (line 7259)
* gnutls_x509_privkey_export_gost_raw:   X509 certificate API.
                                                             (line 7287)
* gnutls_x509_privkey_export_pkcs8:      X509 certificate API.
                                                             (line 7322)
* gnutls_x509_privkey_export_rsa_raw:    X509 certificate API.
                                                             (line 7361)
* gnutls_x509_privkey_export_rsa_raw2:   X509 certificate API.
                                                             (line 7389)
* gnutls_x509_privkey_export2:           X509 certificate API.
                                                             (line 7171)
* gnutls_x509_privkey_export2_pkcs8:     X509 certificate API.
                                                             (line 7197)
* gnutls_x509_privkey_fix:               X509 certificate API.
                                                             (line 7424)
* gnutls_x509_privkey_generate:          X509 certificate API.
                                                             (line 7436)
* gnutls_x509_privkey_generate2:         X509 certificate API.
                                                             (line 7476)
* gnutls_x509_privkey_get_key_id:        X509 certificate API.
                                                             (line 7528)
* gnutls_x509_privkey_get_pk_algorithm:  X509 certificate API.
                                                             (line 7555)
* gnutls_x509_privkey_get_pk_algorithm2: X509 certificate API.
                                                             (line 7568)
* gnutls_x509_privkey_get_seed:          X509 certificate API.
                                                             (line 7583)
* gnutls_x509_privkey_get_spki:          X509 certificate API.
                                                             (line 7608)
* gnutls_x509_privkey_import:            X509 certificate API.
                                                             (line 7624)
* gnutls_x509_privkey_import_dh_raw:     X509 certificate API.
                                                             (line 7676)
* gnutls_x509_privkey_import_dsa_raw:    X509 certificate API.
                                                             (line 7697)
* gnutls_x509_privkey_import_ecc_raw:    X509 certificate API.
                                                             (line 7723)
* gnutls_x509_privkey_import_gost_raw:   X509 certificate API.
                                                             (line 7750)
* gnutls_x509_privkey_import_openssl:    Managing encrypted keys.
                                                             (line  249)
* gnutls_x509_privkey_import_openssl <1>: X509 certificate API.
                                                             (line 7787)
* gnutls_x509_privkey_import_pkcs8:      X509 certificate API.
                                                             (line 7811)
* gnutls_x509_privkey_import_rsa_raw:    X509 certificate API.
                                                             (line 7848)
* gnutls_x509_privkey_import_rsa_raw2:   X509 certificate API.
                                                             (line 7877)
* gnutls_x509_privkey_import2:           Managing encrypted keys.
                                                             (line   51)
* gnutls_x509_privkey_import2 <1>:       X509 certificate API.
                                                             (line 7646)
* gnutls_x509_privkey_init:              X509 certificate API.
                                                             (line 7911)
* gnutls_x509_privkey_sec_param:         X509 certificate API.
                                                             (line 7922)
* gnutls_x509_privkey_set_flags:         X509 certificate API.
                                                             (line 7937)
* gnutls_x509_privkey_set_pin_function:  X509 certificate API.
                                                             (line 7953)
* gnutls_x509_privkey_set_spki:          X509 certificate API.
                                                             (line 7973)
* gnutls_x509_privkey_sign_data:         X509 certificate API.
                                                             (line 7989)
* gnutls_x509_privkey_sign_hash:         Compatibility API.  (line  154)
* gnutls_x509_privkey_verify_params:     X509 certificate API.
                                                             (line 8024)
* gnutls_x509_privkey_verify_seed:       X509 certificate API.
                                                             (line 8036)
* gnutls_x509_rdn_get:                   X509 certificate API.
                                                             (line 8061)
* gnutls_x509_rdn_get_by_oid:            X509 certificate API.
                                                             (line 8108)
* gnutls_x509_rdn_get_oid:               X509 certificate API.
                                                             (line 8136)
* gnutls_x509_rdn_get2:                  X509 certificate API.
                                                             (line 8084)
* gnutls_x509_spki_deinit:               X509 certificate API.
                                                             (line 8159)
* gnutls_x509_spki_get_rsa_oaep_params:  X509 certificate API.
                                                             (line 8169)
* gnutls_x509_spki_get_rsa_pss_params:   X509 certificate API.
                                                             (line 8189)
* gnutls_x509_spki_init:                 X509 certificate API.
                                                             (line 8209)
* gnutls_x509_spki_set_rsa_oaep_params:  X509 certificate API.
                                                             (line 8224)
* gnutls_x509_spki_set_rsa_pss_params:   X509 certificate API.
                                                             (line 8244)
* gnutls_x509_tlsfeatures_add:           X509 certificate API.
                                                             (line 8261)
* gnutls_x509_tlsfeatures_check_crt:     X509 certificate API.
                                                             (line 8278)
* gnutls_x509_tlsfeatures_deinit:        X509 certificate API.
                                                             (line 8297)
* gnutls_x509_tlsfeatures_get:           X509 certificate API.
                                                             (line 8309)
* gnutls_x509_tlsfeatures_init:          X509 certificate API.
                                                             (line 8329)
* gnutls_x509_trust_list_add_cas:        Verifying X.509 certificate paths.
                                                             (line    9)
* gnutls_x509_trust_list_add_cas <1>:    X509 certificate API.
                                                             (line 8344)
* gnutls_x509_trust_list_add_crls:       Verifying X.509 certificate paths.
                                                             (line   73)
* gnutls_x509_trust_list_add_crls <1>:   X509 certificate API.
                                                             (line 8379)
* gnutls_x509_trust_list_add_named_crt:  Verifying X.509 certificate paths.
                                                             (line   41)
* gnutls_x509_trust_list_add_named_crt <1>: X509 certificate API.
                                                             (line 8419)
* gnutls_x509_trust_list_add_system_trust: Verifying X.509 certificate paths.
                                                             (line  293)
* gnutls_x509_trust_list_add_system_trust <1>: X509 certificate API.
                                                             (line 8454)
* gnutls_x509_trust_list_add_trust_dir:  X509 certificate API.
                                                             (line 8478)
* gnutls_x509_trust_list_add_trust_file: Verifying X.509 certificate paths.
                                                             (line  238)
* gnutls_x509_trust_list_add_trust_file <1>: X509 certificate API.
                                                             (line 8505)
* gnutls_x509_trust_list_add_trust_mem:  Verifying X.509 certificate paths.
                                                             (line  266)
* gnutls_x509_trust_list_add_trust_mem <1>: X509 certificate API.
                                                             (line 8536)
* gnutls_x509_trust_list_deinit:         X509 certificate API.
                                                             (line 8566)
* gnutls_x509_trust_list_get_issuer:     X509 certificate API.
                                                             (line 8583)
* gnutls_x509_trust_list_get_issuer_by_dn: X509 certificate API.
                                                             (line 8613)
* gnutls_x509_trust_list_get_issuer_by_subject_key_id: X509 certificate API.
                                                             (line 8637)
* gnutls_x509_trust_list_get_ptr:        X509 certificate API.
                                                             (line 8664)
* gnutls_x509_trust_list_init:           X509 certificate API.
                                                             (line 8680)
* gnutls_x509_trust_list_iter_deinit:    X509 certificate API.
                                                             (line 8697)
* gnutls_x509_trust_list_iter_get_ca:    X509 certificate API.
                                                             (line 8708)
* gnutls_x509_trust_list_remove_cas:     X509 certificate API.
                                                             (line 8740)
* gnutls_x509_trust_list_remove_trust_file: X509 certificate API.
                                                             (line 8765)
* gnutls_x509_trust_list_remove_trust_mem: X509 certificate API.
                                                             (line 8787)
* gnutls_x509_trust_list_set_getissuer_function: X509 certificate API.
                                                             (line 8808)
* gnutls_x509_trust_list_set_ptr:        X509 certificate API.
                                                             (line 8850)
* gnutls_x509_trust_list_verify_crt:     Verifying X.509 certificate paths.
                                                             (line  110)
* gnutls_x509_trust_list_verify_crt <1>: X509 certificate API.
                                                             (line 8866)
* gnutls_x509_trust_list_verify_crt2:    Verifying X.509 certificate paths.
                                                             (line  140)
* gnutls_x509_trust_list_verify_crt2 <1>: X509 certificate API.
                                                             (line 8899)
* gnutls_x509_trust_list_verify_named_crt: Verifying X.509 certificate paths.
                                                             (line  201)
* gnutls_x509_trust_list_verify_named_crt <1>: X509 certificate API.
                                                             (line 8963)


File: gnutls.info,  Node: Concept Index,  Prev: Function and Data Index,  Up: Top

Concept Index
*************

 [index ]
* Menu:

* abstract types:                        Abstract key types.  (line   6)
* alert protocol:                        The TLS Alert Protocol.
                                                              (line   6)
* ALPN:                                  Application Layer Protocol Negotiation (ALPN).
                                                              (line   6)
* anonymous authentication:              Anonymous authentication.
                                                              (line   6)
* API reference:                         API reference.       (line   6)
* Application Layer Protocol Negotiation: Application Layer Protocol Negotiation (ALPN).
                                                              (line   6)
* Application-specific keys:             Application-specific keys.
                                                              (line   6)
* authentication methods:                Authentication methods.
                                                              (line   6)
* bad_record_mac:                        On Record Padding.   (line   6)
* callback functions:                    Callback functions.  (line   6)
* certificate authentication:            Certificate authentication.
                                                              (line   6)
* certificate authentication <1>:        More on certificate authentication.
                                                              (line   6)
* certificate requests:                  PKCS 10 certificate requests.
                                                              (line   6)
* certificate revocation lists:          PKIX certificate revocation lists.
                                                              (line   6)
* certificate status:                    OCSP certificate status checking.
                                                              (line   6)
* certificate status <1>:                OCSP stapling.       (line   6)
* Certificate status request:            OCSP status request. (line   6)
* Certificate verification:              Advanced certificate verification.
                                                              (line   6)
* certification:                         Certification.       (line   6)
* certtool:                              certtool Invocation. (line   6)
* certtool help:                         certtool Invocation. (line  17)
* channel bindings:                      Channel Bindings.    (line   6)
* ciphersuites:                          Supported ciphersuites.
                                                              (line   6)
* client certificate authentication:     Client Authentication.
                                                              (line   6)
* CMS:                                   Cryptographic Message Syntax / PKCS7.
                                                              (line   6)
* compression algorithms:                Compression algorithms and the record layer.
                                                              (line   6)
* contributing:                          Contributing.        (line   6)
* credentials:                           Virtual hosts and credentials.
                                                              (line   6)
* CRL:                                   PKIX certificate revocation lists.
                                                              (line   6)
* cryptographic message syntax:          Cryptographic Message Syntax / PKCS7.
                                                              (line   6)
* DANE:                                  Verifying a certificate using DANE.
                                                              (line   6)
* DANE <1>:                              Certificate verification.
                                                              (line   6)
* danetool:                              danetool Invocation. (line   6)
* danetool help:                         danetool Invocation. (line  11)
* deriving keys:                         Deriving keys for other applications/protocols.
                                                              (line   6)
* digital signatures:                    Digital signatures.  (line   6)
* DNSSEC:                                Verifying a certificate using DANE.
                                                              (line   6)
* DNSSEC <1>:                            Certificate verification.
                                                              (line   6)
* download:                              Downloading and installing.
                                                              (line   6)
* Encrypted keys:                        Managing encrypted keys.
                                                              (line   6)
* error codes:                           Error codes.         (line   6)
* example programs:                      GnuTLS application examples.
                                                              (line   6)
* examples:                              GnuTLS application examples.
                                                              (line   6)
* exporting keying material:             Deriving keys for other applications/protocols.
                                                              (line   6)
* False Start:                           False Start.         (line   6)
* FDL, GNU Free Documentation License:   Copying Information. (line   6)
* file signing:                          Cryptographic Message Syntax / PKCS7.
                                                              (line   6)
* fork:                                  Sessions and fork.   (line   6)
* generating parameters:                 Parameter generation.
                                                              (line   6)
* giovec_t:                              Common types.        (line   6)
* gnutls_datum_t:                        Common types.        (line   6)
* gnutls-cli:                            gnutls-cli Invocation.
                                                              (line   6)
* gnutls-cli help:                       gnutls-cli Invocation.
                                                              (line  13)
* gnutls-cli-debug:                      gnutls-cli-debug Invocation.
                                                              (line   6)
* gnutls-cli-debug help:                 gnutls-cli-debug Invocation.
                                                              (line  16)
* gnutls-serv:                           gnutls-serv Invocation.
                                                              (line   6)
* gnutls-serv help:                      gnutls-serv Invocation.
                                                              (line  11)
* hacking:                               Contributing.        (line   6)
* handshake protocol:                    The TLS Handshake Protocol.
                                                              (line   6)
* hardware security modules:             Smart cards and HSMs.
                                                              (line   6)
* hardware tokens:                       Smart cards and HSMs.
                                                              (line   6)
* hash functions:                        Hash and MAC functions.
                                                              (line   6)
* heartbeat:                             HeartBeat.           (line   6)
* HMAC functions:                        Hash and MAC functions.
                                                              (line   6)
* installation:                          Downloading and installing.
                                                              (line   6)
* installation <1>:                      Installing for a software distribution.
                                                              (line   6)
* internal architecture:                 Internal architecture of GnuTLS.
                                                              (line   6)
* isolated mode:                         Running in a sandbox.
                                                              (line   6)
* key extraction:                        Deriving keys for other applications/protocols.
                                                              (line   6)
* Key pinning:                           Verifying a certificate using trust on first use authentication.
                                                              (line   6)
* Key pinning <1>:                       Certificate verification.
                                                              (line   6)
* key sizes:                             Selecting cryptographic key sizes.
                                                              (line   6)
* keying material exporters:             Deriving keys for other applications/protocols.
                                                              (line   6)
* MAC functions:                         Hash and MAC functions.
                                                              (line   6)
* maximum fragment length:               Maximum fragment length negotiation.
                                                              (line   6)
* OCSP:                                  OCSP certificate status checking.
                                                              (line   6)
* OCSP Functions:                        OCSP API.            (line   6)
* OCSP stapling:                         OCSP stapling.       (line   6)
* OCSP status request:                   OCSP status request. (line   6)
* ocsptool:                              ocsptool Invocation. (line   6)
* ocsptool help:                         ocsptool Invocation. (line  18)
* Online Certificate Status Protocol:    OCSP certificate status checking.
                                                              (line   6)
* Online Certificate Status Protocol <1>: OCSP stapling.      (line   6)
* OpenPGP certificates:                  OpenPGP certificates.
                                                              (line   6)
* OpenSSL:                               Compatibility with the OpenSSL library.
                                                              (line   6)
* OpenSSL encrypted keys:                Managing encrypted keys.
                                                              (line 243)
* overriding algorithms:                 Overriding algorithms.
                                                              (line   6)
* p11tool:                               p11tool Invocation.  (line   6)
* p11tool help:                          p11tool Invocation.  (line  21)
* parameter generation:                  Parameter generation.
                                                              (line   6)
* PCT:                                   On SSL 2 and older protocols.
                                                              (line  37)
* PKCS #10:                              PKCS 10 certificate requests.
                                                              (line   6)
* PKCS #11 tokens:                       Smart cards and HSMs.
                                                              (line   6)
* PKCS #12:                              Managing encrypted keys.
                                                              (line 137)
* PKCS #7:                               Cryptographic Message Syntax / PKCS7.
                                                              (line   6)
* PKCS #8:                               Managing encrypted keys.
                                                              (line  86)
* post-handshake authentication:         TLS 1.3 re-authentication and re-key.
                                                              (line   6)
* Priority strings:                      Priority Strings.    (line   6)
* PSK authentication:                    Authentication using PSK.
                                                              (line   6)
* psktool:                               psktool Invocation.  (line   6)
* psktool help:                          psktool Invocation.  (line  12)
* public key algorithms:                 Public key algorithms.
                                                              (line   6)
* public key algorithms <1>:             Cryptographic Message Syntax / PKCS7.
                                                              (line   6)
* random numbers:                        Random number generation.
                                                              (line   6)
* Raw public-keys:                       Raw public-keys.     (line   6)
* re-authentication:                     TLS 1.2 re-authentication.
                                                              (line   6)
* re-authentication <1>:                 TLS 1.3 re-authentication and re-key.
                                                              (line   6)
* re-key:                                TLS 1.3 re-authentication and re-key.
                                                              (line   6)
* re-negotiation:                        TLS 1.2 re-authentication.
                                                              (line   6)
* re-negotiation <1>:                    TLS 1.3 re-authentication and re-key.
                                                              (line   6)
* record padding:                        On Record Padding.   (line   6)
* record protocol:                       The TLS record protocol.
                                                              (line   6)
* renegotiation:                         Safe renegotiation.  (line   6)
* reporting bugs:                        Bug Reports.         (line   6)
* resuming sessions:                     Resuming Sessions.   (line   6)
* resuming sessions <1>:                 Session resumption.  (line   6)
* safe renegotiation:                    Safe renegotiation.  (line   6)
* seccomp:                               Running in a sandbox.
                                                              (line   6)
* Secure RTP:                            SRTP.                (line   6)
* server name indication:                Server name indication.
                                                              (line   6)
* session resumption:                    Resuming Sessions.   (line   6)
* session resumption <1>:                Session resumption.  (line   6)
* session tickets:                       Session tickets.     (line   6)
* Smart card example:                    Client using a smart card with TLS.
                                                              (line   6)
* smart cards:                           Smart cards and HSMs.
                                                              (line   6)
* SRP authentication:                    Authentication using SRP.
                                                              (line   6)
* srptool:                               srptool Invocation.  (line   6)
* srptool help:                          srptool Invocation.  (line  19)
* SRTP:                                  SRTP.                (line   6)
* SSH-style authentication:              Verifying a certificate using trust on first use authentication.
                                                              (line   6)
* SSH-style authentication <1>:          Certificate verification.
                                                              (line   6)
* SSL 2:                                 On SSL 2 and older protocols.
                                                              (line   6)
* Supplemental data:                     Extensions and Supplemental Data.
                                                              (line   6)
* symmetric algorithms:                  Symmetric algorithms.
                                                              (line   6)
* symmetric cryptography:                Symmetric algorithms.
                                                              (line   6)
* symmetric encryption algorithms:       Encryption algorithms used in the record layer.
                                                              (line   6)
* System-specific keys:                  Application-specific keys.
                                                              (line   5)
* System-wide configuration:             System-wide configuration of the library.
                                                              (line   6)
* thread safety:                         Thread safety.       (line   6)
* tickets:                               Session tickets.     (line   6)
* TLS extensions:                        TLS Extensions.      (line   6)
* TLS extensions <1>:                    Maximum fragment length negotiation.
                                                              (line   6)
* TLS extensions <2>:                    Server name indication.
                                                              (line   6)
* TLS extensions <3>:                    Session tickets.     (line   6)
* TLS extensions <4>:                    HeartBeat.           (line   6)
* TLS False Start:                       False Start.         (line   6)
* TLS layers:                            TLS layers.          (line   6)
* TPM:                                   Trusted Platform Module.
                                                              (line   6)
* tpmtool:                               tpmtool Invocation.  (line   6)
* tpmtool help:                          tpmtool Invocation.  (line  11)
* transport layer:                       The transport layer. (line   6)
* transport protocol:                    The transport layer. (line   6)
* Trust on first use:                    Verifying a certificate using trust on first use authentication.
                                                              (line   6)
* Trust on first use <1>:                Certificate verification.
                                                              (line   6)
* trusted platform module:               Trusted Platform Module.
                                                              (line   6)
* upgrading:                             Upgrading from previous versions.
                                                              (line   6)
* verifying certificate paths:           Verifying X.509 certificate paths.
                                                              (line   6)
* verifying certificate paths <1>:       Verifying a certificate in the context of TLS session.
                                                              (line   6)
* verifying certificate paths <2>:       Verifying a certificate using trust on first use authentication.
                                                              (line   6)
* verifying certificate paths <3>:       Verifying a certificate using DANE.
                                                              (line   6)
* verifying certificate with pkcs11:     Verification using PKCS11.
                                                              (line   6)
* virtual hosts:                         Virtual hosts and credentials.
                                                              (line   6)
* X.509 certificate name:                X.509 certificate names.
                                                              (line   6)
* X.509 certificates:                    X.509 certificates.  (line   6)
* X.509 distinguished name:              X.509 distinguished names.
                                                              (line   6)
* X.509 extensions:                      X.509 extensions.    (line   6)
* X.509 Functions:                       X509 certificate API.
                                                              (line   6)

