.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_free_keys" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_free_keys \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_certificate_free_keys(gnutls_certificate_credentials_t " sc ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t sc" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.SH "DESCRIPTION"
This function will delete all the keys and the certificates associated
with the given credentials. This function must not be called when a
TLS negotiation that uses the credentials is in progress.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
