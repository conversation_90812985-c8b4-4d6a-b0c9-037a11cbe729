.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_dn_by_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_dn_by_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_dn_by_oid(gnutls_x509_crt_t " crt ", const char * " oid ", unsigned int " raw_flag ", const void * " name ", unsigned int " sizeof_name ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "const char * oid" 12
holds an Object Identifier in a null terminated string
.IP "unsigned int raw_flag" 12
must be 0, or 1 if the data are DER encoded
.IP "const void * name" 12
a pointer to the name
.IP "unsigned int sizeof_name" 12
holds the size of  \fIname\fP 
.SH "DESCRIPTION"
This function will set the part of the name of the Certificate
subject, specified by the given OID. The input string should be
ASCII or UTF\-8 encoded.

Some helper macros with popular OIDs can be found in gnutls/x509.h
With this function you can only set the known OIDs. You can test
for known OIDs using \fBgnutls_x509_dn_oid_known()\fP. For OIDs that are
not known (by gnutls) you should properly DER encode your data,
and call this function with  \fIraw_flag\fP set.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
