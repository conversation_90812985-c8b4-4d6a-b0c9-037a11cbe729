DEPLOYMENT_ADDITIONAL_FILES
---------------------------

.. versionadded:: 3.13

Set the WinCE project ``AdditionalFiles`` in ``DeploymentTool`` in ``.vcproj``
files generated by the :ref:`Visual Studio Generators`.
This is useful when you want to debug on remote WinCE device.
Specify additional files that will be copied to the device.
For example:

.. code-block:: cmake

  set_property(TARGET ${TARGET} PROPERTY
    DEPLOYMENT_ADDITIONAL_FILES "english.lng|local_folder|remote_folder|0"
    "german.lng|local_folder|remote_folder|0")

produces::

  <DeploymentTool AdditionalFiles="english.lng|local_folder|remote_folder|0;german.lng|local_folder|remote_folder|0" ... />
