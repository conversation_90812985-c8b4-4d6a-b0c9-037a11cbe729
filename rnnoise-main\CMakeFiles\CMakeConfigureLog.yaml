
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/cc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        D:/RNN/rnnoise-main/CMakeFiles/4.0.3/CompilerIdC/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/RNN/rnnoise-main/CMakeFiles/CMakeScratch/TryCompile-3v81gz"
      binary: "D:/RNN/rnnoise-main/CMakeFiles/CMakeScratch/TryCompile-3v81gz"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/RNN/rnnoise-main/CMakeFiles/CMakeScratch/TryCompile-3v81gz'
        
        Run Build Command(s): D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/ninja.exe -v cmTC_c523d
        [1/2] D:\\RNN\\rnnoise-main\\env\\MSYS2\\mingw64\\bin\\cc.exe   -v -o CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj -c D:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=D:\\RNN\\rnnoise-main\\env\\MSYS2\\mingw64\\bin\\cc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (Rev8, Built by MSYS2 project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c523d.dir/'
         D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -D_REENTRANT D:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_c523d.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -o D:\\RNN\\rnnoise-main\\env\\MSYS2\\tmp\\ccDBRN70.s
        GNU C23 (Rev8, Built by MSYS2 project) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.3.0, MPFR version 4.2.2, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring duplicate directory "D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring nonexistent directory "D:/M/msys64/mingw64/include"
        ignoring nonexistent directory "/mingw64/include"
        ignoring duplicate directory "D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring nonexistent directory "D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "D:/M/msys64/mingw64/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include
         D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
        End of search list.
        Compiler executable checksum: 4c5da8c951b237779f1a74b21e87fb74
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c523d.dir/'
         D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj D:\\RNN\\rnnoise-main\\env\\MSYS2\\tmp\\ccDBRN70.s
        GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45
        COMPILER_PATH=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../\x0d
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.'\x0d
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\RNN\\rnnoise-main\\env\\MSYS2\\mingw64\\bin\\cc.exe  -v -Wl,-v CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj -o cmTC_c523d.exe -Wl,--out-implib,libcmTC_c523d.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=D:\\RNN\\rnnoise-main\\env\\MSYS2\\mingw64\\bin\\cc.exe
        COLLECT_LTO_WRAPPER=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (Rev8, Built by MSYS2 project) 
        COMPILER_PATH=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c523d.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_c523d.'
         D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=D:\\RNN\\rnnoise-main\\env\\MSYS2\\tmp\\ccXrj1d2.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_c523d.exe D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_c523d.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=D:\\RNN\\rnnoise-main\\env\\MSYS2\\tmp\\ccXrj1d2.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_c523d.exe D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_c523d.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o\x0d
        GNU ld (GNU Binutils) 2.45\x0d
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c523d.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_c523d.'\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
          add: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        end of search list found
        collapse include dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/include]
        collapse include dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        implicit include dirs: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include;D:/RNN/rnnoise-main/env/MSYS2/mingw64/include;D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/RNN/rnnoise-main/CMakeFiles/CMakeScratch/TryCompile-3v81gz']
        ignore line: []
        ignore line: [Run Build Command(s): D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/ninja.exe -v cmTC_c523d]
        ignore line: [[1/2] D:\\RNN\\rnnoise-main\\env\\MSYS2\\mingw64\\bin\\cc.exe   -v -o CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj -c D:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\RNN\\rnnoise-main\\env\\MSYS2\\mingw64\\bin\\cc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (Rev8  Built by MSYS2 project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c523d.dir/']
        ignore line: [ D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -D_REENTRANT D:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_c523d.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -o D:\\RNN\\rnnoise-main\\env\\MSYS2\\tmp\\ccDBRN70.s]
        ignore line: [GNU C23 (Rev8  Built by MSYS2 project) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.3.0  MPFR version 4.2.2  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring duplicate directory "D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring nonexistent directory "D:/M/msys64/mingw64/include"]
        ignore line: [ignoring nonexistent directory "/mingw64/include"]
        ignore line: [ignoring duplicate directory "D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "D:/M/msys64/mingw64/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
        ignore line: [ D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 4c5da8c951b237779f1a74b21e87fb74]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c523d.dir/']
        ignore line: [ D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj D:\\RNN\\rnnoise-main\\env\\MSYS2\\tmp\\ccDBRN70.s]
        ignore line: [GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45]
        ignore line: [COMPILER_PATH=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../\x0d]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.'\x0d]
        ignore line: [[2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\RNN\\rnnoise-main\\env\\MSYS2\\mingw64\\bin\\cc.exe  -v -Wl -v CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj -o cmTC_c523d.exe -Wl --out-implib libcmTC_c523d.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\RNN\\rnnoise-main\\env\\MSYS2\\mingw64\\bin\\cc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (Rev8  Built by MSYS2 project) ]
        ignore line: [COMPILER_PATH=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c523d.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_c523d.']
        link line: [ D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=D:\\RNN\\rnnoise-main\\env\\MSYS2\\tmp\\ccXrj1d2.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_c523d.exe D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_c523d.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=D:\\RNN\\rnnoise-main\\env\\MSYS2\\tmp\\ccXrj1d2.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_c523d.exe] ==> ignore
          arg [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o] ==> obj [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o]
          arg [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc] ==> dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc]
          arg [-LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_c523d.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o] ==> obj [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o]
          arg [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        ignore line: [collect2 version 15.1.0]
        ignore line: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=D:\\RNN\\rnnoise-main\\env\\MSYS2\\tmp\\ccXrj1d2.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_c523d.exe D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v CMakeFiles/cmTC_c523d.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_c523d.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o\x0d]
        linker tool for 'C': D:/RNN/rnnoise-main/env/MSYS2/mingw64/x86_64-w64-mingw32/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/crt2.o]
        collapse obj [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/default-manifest.o]
        collapse obj [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc]
        collapse library dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib]
        collapse library dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/RNN/rnnoise-main/env/MSYS2/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32]
        implicit objs: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/crt2.o;D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/default-manifest.o;D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib/gcc;D:/RNN/rnnoise-main/env/MSYS2/mingw64/x86_64-w64-mingw32/lib;D:/RNN/rnnoise-main/env/MSYS2/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "env/MSYS2/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "D:/RNN/rnnoise-main/env/MSYS2/mingw64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.45
...
