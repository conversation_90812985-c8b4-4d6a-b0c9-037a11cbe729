.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake_write" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake_write \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_handshake_write(gnutls_session_t " session ", gnutls_record_encryption_level_t " level ", const void * " data ", size_t " data_size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_record_encryption_level_t level" 12
the current encryption level for reading a handshake message
.IP "const void * data" 12
the (const) handshake data to be processed
.IP "size_t data_size" 12
the size of data
.SH "DESCRIPTION"
This function processes a handshake message in the encryption level
specified with  \fIlevel\fP . Prior to calling this function, a handshake
read callback must be set on  \fIsession\fP . Use
\fBgnutls_handshake_set_read_function()\fP to do this.
.SH "SINCE"
3.7.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
