.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_othername_to_virtual" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_othername_to_virtual \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_othername_to_virtual(const char * " oid ", const gnutls_datum_t * " othername ", unsigned int * " virt_type ", gnutls_datum_t * " virt ");"
.SH ARGUMENTS
.IP "const char * oid" 12
The othername object identifier
.IP "const gnutls_datum_t * othername" 12
The othername data
.IP "unsigned int * virt_type" 12
GNUTLS_SAN_OTHERNAME_XXX
.IP "gnutls_datum_t * virt" 12
allocated printable data
.SH "DESCRIPTION"
This function will parse and convert the othername data to a virtual
type supported by gnutls.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.3.8
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
