# common.conf - common defaults for all components.
#
# This file may provide defaults as well as options which needs to be
# synchronized between components.  As usual this file is read from
# the system wide config directory (e.g. /etc/gnupg/common.conf) as
# well as from the home directory (e.g. ~/.gnupg/common.conf).


# Uncomment to enable the use of the keybox daemon (keyboxd) by gpg
# and gpgsm.
#use-keyboxd

# For testing ist is somethimes useful to use a different binary
# of keybox.  This option can be used to speicify this.
#keyboxd-program /foo/bar/keyboxd

# For the daemons (gpg-agent, scdaemon, dirmngr, keyboxd) it is often
# useful to define a shared logging destination.  This is either the
# standard logging socket (socket://) or a tcp server (tcp://ip:port).
# If a file name is given the name of the component is internally
# appended.
#log-file socket://
