.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_authority_info_access" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_authority_info_access \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_authority_info_access(gnutls_x509_crt_t " crt ", int " what ", gnutls_datum_t * " data ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
Holds the certificate
.IP "int what" 12
what data to get, a \fBgnutls_info_access_what_t\fP type.
.IP "gnutls_datum_t * data" 12
output data to be freed with \fBgnutls_free()\fP.
.SH "DESCRIPTION"
This function sets the Authority Information Access (AIA)
extension, see RFC 5280 section ******* for more information.  

The type of data stored in  \fIdata\fP is specified via  \fIwhat\fP which
should be \fBgnutls_info_access_what_t\fP values.

If  \fIwhat\fP is \fBGNUTLS_IA_OCSP_URI\fP,  \fIdata\fP will hold the OCSP URI.
If  \fIwhat\fP is \fBGNUTLS_IA_CAISSUERS_URI\fP,  \fIdata\fP will hold the caIssuers
URI.  
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
