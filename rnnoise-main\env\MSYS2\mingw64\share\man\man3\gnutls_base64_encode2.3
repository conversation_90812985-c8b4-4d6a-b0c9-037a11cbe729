.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_base64_encode2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_base64_encode2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_base64_encode2(const gnutls_datum_t * " data ", gnutls_datum_t * " result ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * data" 12
contains the raw data
.IP "gnutls_datum_t * result" 12
will hold the newly allocated encoded data
.SH "DESCRIPTION"
This function will convert the given data to printable data, using
the base64 encoding. This function will allocate the required
memory to hold the encoded data.

You should use \fBgnutls_free()\fP to free the returned data.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
