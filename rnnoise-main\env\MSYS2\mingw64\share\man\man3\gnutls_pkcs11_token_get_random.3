.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_token_get_random" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_token_get_random \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_token_get_random(const char * " token_url ", void * " rnddata ", size_t " len ");"
.SH ARGUMENTS
.IP "const char * token_url" 12
A PKCS \fB11\fP URL specifying a token
.IP "void * rnddata" 12
A pointer to the memory area to be filled with random data
.IP "size_t len" 12
The number of bytes of randomness to request
.SH "DESCRIPTION"
This function will get random data from the given token.
It will store rnddata and fill the memory pointed to by rnddata with
len random bytes from the token.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
