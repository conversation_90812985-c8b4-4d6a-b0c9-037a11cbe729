#!/usr/bin/bash
#
# updpkgsums - update source checksums in-place in PKGBUILDs
#
# Copyright (C) 2012-2013 <PERSON> <<EMAIL>>
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License
# as published by the Free Software Foundation; either version 2
# of the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

shopt -s extglob

declare -r myname='updpkgsums'
declare -r myver='1.10.6'

LIBRARY=${LIBRARY:-'/usr/share/makepkg'}

# Import libmakepkg
# shellcheck source=/dev/null
source "$LIBRARY"/util/schema.sh

usage() {
	cat <<EOF
${myname} v${myver}

Update checksums of a PKGBUILD file.

Usage: ${myname} [options] [build file]

Options:
  -h, --help     display this help message and exit
  -V, --version  display version information and exit

These options can be passed to makepkg:
  -m, --nocolor  do not colorize output
EOF
}

version() {
	printf "%s %s\n" "$myname" "$myver"
	echo 'Copyright (C) 2012-2013 Dave Reisner <<EMAIL>>'
}

die() {
	# shellcheck disable=SC2059
	printf "==> ERROR: $1\n" "${@:2}" >&2
	exit 1
}

MAKEPKG_OPTS=()
buildfile='PKGBUILD'

while (( "$#" )); do
	case "$1" in
		-m|--nocolor) MAKEPKG_OPTS+=("$1"); shift ;;
		-h|--help) usage; exit ;;
		-V|--version) version; exit ;;
		*) buildfile="$1"; break 2 ;;
	esac
done

if [[ ! -f $buildfile ]]; then
	die "%s not found or is not a file" "$buildfile"
fi

# Resolve any symlinks to avoid replacing the symlink with a file. But, we
# have to do this portably -- readlink's flags are inconsistent across OSes.
while [[ -L $buildfile ]]; do
	buildfile=$(readlink "$buildfile")
	if [[ $buildfile = */* ]]; then
		cd "${buildfile%/*}" || exit
		buildfile=${buildfile##*/}
	fi
done

# cd into the directory with the build file. This avoids creating random src/
# directories scattered about the filesystem, and avoids cases where we might
# not be able to write in the $PWD.
if [[ $buildfile = */* ]]; then
	cd "${buildfile%/*}" || exit
	buildfile=${buildfile##*/}
fi

# Check $PWD/ for permission to unlink the $buildfile and write a new one
if [[ ! -w . ]]; then
	die "No write permission in '%s'" "$PWD"
fi

# Generate the new sums
BUILDDIR=$(mktemp -d  "${TMPDIR:-/tmp}/updpkgsums.XXXXXX")
export BUILDDIR
newbuildfile=$(mktemp "${TMPDIR:-/tmp}/updpkgsums.XXXXXX")

trap 'rm -rf "$BUILDDIR" "$newbuildfile"' EXIT
# shellcheck disable=SC2154 # known_hash_algos imported via libmakepkg
sumtypes=$(IFS='|'; echo "${known_hash_algos[*]}")
newsums=$(MINGW_PACKAGE_PREFIX=mingw-w64-i686 makepkg -g -p "$buildfile" "${MAKEPKG_OPTS[@]}") || die 'Failed to generate new checksums'

if [[ -z $newsums ]]; then
	die "$buildfile does not contain sources to update"
fi

awk -v sumtypes="$sumtypes" -v newsums="$newsums" '
	$0 ~"^[[:blank:]]*(" sumtypes ")sums(_[^=]+)?\\+?=", $0 ~ "\\)[[:blank:]]*(#.*)?$" {
		if (!w) {
			print newsums
			w++
		}
		next
	}

	1
	END { if (!w) print newsums }
' "$buildfile" > "$newbuildfile" || die 'Failed to write new PKGBUILD'

# Rewrite the original buildfile. Use cat instead of mv/cp to preserve
# permissions implicitly.
if ! cat -- "$newbuildfile" >"$buildfile"; then
	die "Failed to update %s. The file has not been modified." "$buildfile"
fi
