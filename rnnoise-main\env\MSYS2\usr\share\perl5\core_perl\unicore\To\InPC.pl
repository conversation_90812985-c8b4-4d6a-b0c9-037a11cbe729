# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.



# The name this table is to be known by, with the format of the mappings in
# the main body of the table, and what all code points missing from this file
# map to.
$Unicode::UCD::SwashInfo{'ToInPC'}{'format'} = 's'; # string
$Unicode::UCD::SwashInfo{'ToInPC'}{'missing'} = 'NA';

return <<'END';
900	902	Top
903		Right
93A		Top
93B		Right
93C		Bottom
93E		Right
93F		Left
940		Right
941	944	Bottom
945	948	Top
949	94C	Right
94D		Bottom
94E		Left
94F		Right
951		Top
952		Bottom
955		Top
956	957	Bottom
962	963	Bottom
981		Top
982	983	Right
9BC		Bottom
9BE		Right
9BF		Left
9C0		Right
9C1	9C4	Bottom
9C7	9C8	Left
9CB	9CC	Left_And_Right
9CD		Bottom
9D7		Right
9E2	9E3	Bottom
9FE		Top
A01	A02	Top
A03		Right
A3C		Bottom
A3E		Right
A3F		Left
A40		Right
A41	A42	Bottom
A47	A48	Top
A4B	A4C	Top
A4D		Bottom
A51		Bottom
A70	A71	Top
A75		Bottom
A81	A82	Top
A83		Right
ABC		Bottom
ABE		Right
ABF		Left
AC0		Right
AC1	AC4	Bottom
AC5		Top
AC7	AC8	Top
AC9		Top_And_Right
ACB	ACC	Right
ACD		Bottom
AE2	AE3	Bottom
AFA	AFF	Top
B01		Top
B02	B03	Right
B3C		Bottom
B3E		Right
B3F		Top
B40		Right
B41	B44	Bottom
B47		Left
B48		Top_And_Left
B4B		Left_And_Right
B4C		Top_And_Left_And_Right
B4D		Bottom
B55	B56	Top
B57		Top_And_Right
B62	B63	Bottom
B82		Top
BBE	BBF	Right
BC0		Top
BC1	BC2	Right
BC6	BC8	Left
BCA	BCC	Left_And_Right
BCD		Top
BD7		Right
C00		Top
C01	C03	Right
C04		Top
C3C		Bottom
C3E	C40	Top
C41	C44	Right
C46	C47	Top
C48		Top_And_Bottom
C4A	C4D	Top
C55		Top
C56		Bottom
C62	C63	Bottom
C81		Top
C82	C83	Right
CBC		Bottom
CBE		Right
CBF		Top
CC0		Top_And_Right
CC1	CC4	Right
CC6		Top
CC7	CC8	Top_And_Right
CCA	CCB	Top_And_Right
CCC	CCD	Top
CD5	CD6	Right
CE2	CE3	Bottom
CF3		Right
D00	D01	Top
D02	D03	Right
D3B	D3C	Top
D3E	D42	Right
D43	D44	Bottom
D46	D48	Left
D4A	D4C	Left_And_Right
D4D	D4E	Top
D57		Right
D62	D63	Bottom
D81		Top
D82	D83	Right
DCA		Top
DCF	DD1	Right
DD2	DD3	Top
DD4		Bottom
DD6		Bottom
DD8		Right
DD9		Left
DDA		Top_And_Left
DDB		Left
DDC		Left_And_Right
DDD		Top_And_Left_And_Right
DDE		Left_And_Right
DDF		Right
DF2	DF3	Right
E30		Right
E31		Top
E32	E33	Right
E34	E37	Top
E38	E3A	Bottom
E40	E44	Visual_Order_Left
E45		Right
E47	E4E	Top
EB0		Right
EB1		Top
EB2	EB3	Right
EB4	EB7	Top
EB8	EBA	Bottom
EBB		Top
EBC		Bottom
EC0	EC4	Visual_Order_Left
EC8	ECE	Top
F18	F19	Bottom
F35		Bottom
F37		Bottom
F39		Top
F3E		Right
F3F		Left
F71		Bottom
F72		Top
F73		Top_And_Bottom
F74	F75	Bottom
F76	F79	Top_And_Bottom
F7A	F7E	Top
F7F		Right
F80		Top
F81		Top_And_Bottom
F82	F83	Top
F84		Bottom
F86	F87	Top
F8D	F97	Bottom
F99	FBC	Bottom
FC6		Bottom
102B	102C	Right
102D	102E	Top
102F	1030	Bottom
1031		Left
1032	1036	Top
1037		Bottom
1038		Right
103A		Top
103B		Right
103C		Top_And_Bottom_And_Left
103D	103E	Bottom
1056	1057	Right
1058	1059	Bottom
105E	1060	Bottom
1062	1064	Right
1067	106D	Right
1071	1074	Top
1082		Bottom
1083		Right
1084		Left
1085	1086	Top
1087	108C	Right
108D		Bottom
108F		Right
109A	109C	Right
109D		Top
1712		Top
1713	1714	Bottom
1715		Right
1732		Top
1733		Bottom
1734		Right
1752		Top
1753		Bottom
1772		Top
1773		Bottom
17B6		Right
17B7	17BA	Top
17BB	17BD	Bottom
17BE		Top_And_Left
17BF		Top_And_Left_And_Right
17C0		Left_And_Right
17C1	17C3	Left
17C4	17C5	Left_And_Right
17C6		Top
17C7	17C8	Right
17C9	17D1	Top
17D3		Top
17DD		Top
1920	1921	Top
1922		Bottom
1923	1924	Right
1925	1926	Top_And_Right
1927	1928	Top
1929	192B	Right
1930	1931	Right
1932		Bottom
1933	1938	Right
1939		Bottom
193A		Top
193B		Bottom
19B0	19B4	Right
19B5	19B7	Visual_Order_Left
19B8	19B9	Right
19BA		Visual_Order_Left
19BB	19C0	Right
19C8	19C9	Right
1A17		Top
1A18		Bottom
1A19		Left
1A1A		Right
1A1B		Top
1A55		Left
1A56		Bottom
1A57		Right
1A58	1A5A	Top
1A5B	1A5E	Bottom
1A61		Right
1A62		Top
1A63	1A64	Right
1A65	1A68	Top
1A69	1A6A	Bottom
1A6B		Top
1A6C		Bottom
1A6D		Right
1A6E	1A72	Left
1A73	1A7C	Top
1A7F		Bottom
1B00	1B03	Top
1B04		Right
1B34		Top
1B35		Right
1B36	1B37	Top
1B38	1B3A	Bottom
1B3B		Bottom_And_Right
1B3C		Top_And_Bottom
1B3D		Top_And_Bottom_And_Right
1B3E	1B3F	Left
1B40	1B41	Left_And_Right
1B42		Top
1B43		Top_And_Right
1B44		Right
1B6B		Top
1B6C		Bottom
1B6D	1B73	Top
1B80	1B81	Top
1B82		Right
1BA1		Right
1BA2	1BA3	Bottom
1BA4		Top
1BA5		Bottom
1BA6		Left
1BA7		Right
1BA8	1BA9	Top
1BAA		Right
1BAC	1BAD	Bottom
1BE6		Top
1BE7		Right
1BE8	1BE9	Top
1BEA	1BEC	Right
1BED		Top
1BEE		Right
1BEF	1BF1	Top
1BF2	1BF3	Right
1C24	1C26	Right
1C27	1C28	Left
1C29		Top_And_Left
1C2A	1C2B	Right
1C2C		Bottom
1C2D	1C33	Top
1C34	1C35	Left
1C36		Top
1C37		Bottom
1CD0	1CD2	Top
1CD4		Overstruck
1CD5	1CD9	Bottom
1CDA	1CDB	Top
1CDC	1CDF	Bottom
1CE0		Top
1CE1		Right
1CE2	1CE8	Overstruck
1CED		Bottom
1CF4		Top
1CF7		Right
1DFB		Top
20F0		Top
A802		Top
A806		Top
A80B		Top
A823	A824	Right
A825		Bottom
A826		Top
A827		Right
A82C		Bottom
A880	A881	Right
A8B4	A8C3	Right
A8C4		Bottom
A8C5		Top
A8E0	A8F1	Top
A8FF		Top
A926	A92A	Top
A92B	A92D	Bottom
A947	A949	Bottom
A94A		Top
A94B	A94E	Bottom
A94F	A951	Top
A952	A953	Right
A980	A982	Top
A983		Right
A9B3		Top
A9B4	A9B5	Right
A9B6	A9B7	Top
A9B8	A9B9	Bottom
A9BA	A9BB	Left
A9BC		Top
A9BD		Bottom
A9BE		Bottom_And_Right
A9BF		Bottom_And_Left
A9C0		Bottom_And_Right
A9E5		Top
AA29	AA2C	Top
AA2D		Bottom
AA2E		Top
AA2F	AA30	Left
AA31		Top
AA32		Bottom
AA33		Right
AA34		Left
AA35	AA36	Bottom
AA43		Top
AA4C		Top
AA4D		Right
AA7B		Right
AA7C		Top
AA7D		Right
AAB0		Top
AAB1		Right
AAB2	AAB3	Top
AAB4		Bottom
AAB5	AAB6	Visual_Order_Left
AAB7	AAB8	Top
AAB9		Visual_Order_Left
AABA		Right
AABB	AABC	Visual_Order_Left
AABD		Right
AABE	AABF	Top
AAC1		Top
AAEB		Left
AAEC		Bottom
AAED		Top
AAEE		Left
AAEF		Right
AAF5		Right
ABE3	ABE4	Right
ABE5		Top
ABE6	ABE7	Right
ABE8		Bottom
ABE9	ABEA	Right
ABEC		Right
ABED		Bottom
10A01		Overstruck
10A02	10A03	Bottom
10A05		Top
10A06		Overstruck
10A0C	10A0E	Bottom
10A0F		Top
10A38		Top
10A39	10A3A	Bottom
11000		Right
11001		Top
11002		Right
11038	1103B	Top
1103C	11041	Bottom
11042	11046	Top
11070		Top
11073	11074	Top
11080	11081	Top
11082		Right
110B0		Right
110B1		Left
110B2		Right
110B3	110B4	Bottom
110B5	110B6	Top
110B7	110B8	Right
110B9	110BA	Bottom
110C2		Bottom
11100	11102	Top
11127	11129	Top
1112A	1112B	Bottom
1112C		Left
1112D		Top
1112E	1112F	Top_And_Bottom
11130		Top
11131	11132	Bottom
11134		Top
11145	11146	Right
11173		Bottom
11180	11181	Top
11182		Right
111B3		Right
111B4		Left
111B5		Right
111B6	111BB	Bottom
111BC	111BE	Top
111BF		Top_And_Right
111C0		Right
111C2	111C3	Top
111C9	111CA	Bottom
111CB		Top
111CC		Bottom
111CE		Left
111CF		Top
1122C	1122E	Right
1122F		Bottom
11230	11231	Top
11232	11233	Top_And_Right
11234		Top
11235		Right
11236	11237	Top
1123E		Top
11241		Bottom
112DF		Top
112E0		Right
112E1		Left
112E2		Right
112E3	112E4	Bottom
112E5	112E8	Top
112E9	112EA	Bottom
11300	11301	Top
11302	11303	Right
1133B	1133C	Bottom
1133E	1133F	Right
11340		Top
11341	11344	Right
11347	11348	Left
1134B	1134C	Left_And_Right
1134D		Right
11357		Right
11362	11363	Right
11366	1136C	Top
11370	11374	Top
11435		Right
11436		Left
11437		Right
11438	1143D	Bottom
1143E	1143F	Top
11440	11441	Right
11442		Bottom
11443	11444	Top
11445		Right
11446		Bottom
1145E		Top
114B0		Right
114B1		Left
114B2		Right
114B3	114B8	Bottom
114B9		Left
114BA		Top
114BB		Top_And_Left
114BC		Left_And_Right
114BD		Right
114BE		Left_And_Right
114BF	114C0	Top
114C1		Right
114C2	114C3	Bottom
115AF		Right
115B0		Left
115B1		Right
115B2	115B5	Bottom
115B8		Left
115B9		Top_And_Left
115BA		Left_And_Right
115BB		Top_And_Left_And_Right
115BC	115BD	Top
115BE		Right
115BF	115C0	Bottom
115DC	115DD	Bottom
11630	11632	Right
11633	11638	Bottom
11639	1163A	Top
1163B	1163C	Right
1163D		Top
1163E		Right
1163F		Bottom
11640		Top
116AB		Top
116AC		Right
116AD		Top
116AE		Left
116AF		Right
116B0	116B1	Bottom
116B2	116B5	Top
116B6		Right
116B7		Bottom
1171D		Bottom
1171E		Top_And_Bottom_And_Left
1171F		Top
11720	11721	Right
11722	11723	Top
11724	11725	Bottom
11726		Left
11727		Top
11728		Bottom
11729	1172B	Top
1182C		Right
1182D		Left
1182E		Right
1182F	11832	Bottom
11833	11837	Top
11838		Right
11839	1183A	Bottom
11930	11934	Right
11935		Left
11937		Left
11938		Left_And_Right
1193B	1193C	Top
1193D		Right
1193F		Top
11940		Right
11941		Top
11942		Bottom_And_Right
11943		Bottom
119D1		Right
119D2		Left
119D3		Right
119D4	119D7	Bottom
119DA	119DB	Top
119DC	119DF	Right
119E0		Bottom
119E4		Left
11A01		Top
11A02	11A03	Bottom
11A04	11A09	Top
11A0A		Bottom
11A33	11A34	Bottom
11A35	11A38	Top
11A39		Right
11A3A		Top
11A3B	11A3E	Bottom
11A51		Top
11A52	11A53	Bottom
11A54	11A56	Top
11A57	11A58	Right
11A59	11A5B	Bottom
11A84	11A89	Top
11A8A	11A95	Bottom
11A96		Top
11A97		Right
11A98		Top
11C2F		Right
11C30	11C31	Top
11C32	11C36	Bottom
11C38	11C3D	Top
11C3E		Right
11C3F		Bottom
11C92	11CA7	Bottom
11CA9		Right
11CAA	11CB0	Bottom
11CB1		Left
11CB2		Bottom
11CB3		Top
11CB4		Right
11CB5	11CB6	Top
11D31	11D35	Top
11D36		Bottom
11D3A		Top
11D3C	11D3D	Top
11D3F	11D41	Top
11D42		Bottom
11D43		Top
11D44		Bottom
11D46		Right
11D47		Bottom
11D8A	11D8E	Right
11D90	11D91	Top
11D93	11D94	Right
11D95		Top
11D96		Right
11EF3		Top
11EF4		Bottom
11EF5		Left
11EF6		Right
11F00	11F02	Top
11F03		Right
11F34	11F35	Right
11F36	11F37	Top
11F38	11F3A	Bottom
11F3E	11F3F	Left
11F40		Top
11F41		Right
END
