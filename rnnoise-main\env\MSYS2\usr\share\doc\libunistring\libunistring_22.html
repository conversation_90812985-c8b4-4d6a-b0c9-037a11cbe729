<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html401/loose.dtd">
<html>
<!-- Created on October, 16 2024 by texi2html 1.78a -->
<!--
Written by: <PERSON> <<EMAIL>> (original author)
            <PERSON>  <<EMAIL>>
            <PERSON> <o<PERSON><EMAIL>>
            and many others.
Maintained by: Many creative people.
Send bugs and suggestions to <<EMAIL>>

-->
<head>
<title>GNU libunistring: Index: U</title>

<meta name="description" content="GNU libunistring: Index: U">
<meta name="keywords" content="GNU libunistring: Index: U">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="texi2html 1.78a">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
pre.display {font-family: serif}
pre.format {font-family: serif}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: serif; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: serif; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.roman {font-family:serif; font-weight:normal;}
span.sansserif {font-family:sans-serif; font-weight:normal;}
ul.toc {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">

<table cellpadding="1" cellspacing="1" border="0">
<tr><td valign="middle" align="left">[<a href="libunistring_21.html#SEC94" title="Beginning of this chapter or previous chapter"> &lt;&lt; </a>]</td>
<td valign="middle" align="left">[<a href="libunistring_23.html#INDEX1" title="Next chapter"> &gt;&gt; </a>]</td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Top" title="Cover (top) of document">Top</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Contents" title="Table of contents">Contents</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_21.html#SEC94" title="Index">Index</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_abt.html#SEC_About" title="About (help)"> ? </a>]</td>
</tr></table>

<hr size="2">
<a name="INDEX0"></a>
<h1 class="unnumbered"> Index: U </h1>
<table><tr><th valign="top">Jump to: &nbsp; </th><td><a href="libunistring_21.html#SEC94_0" class="summary-letter"><b>A</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_1" class="summary-letter"><b>B</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_2" class="summary-letter"><b>C</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_3" class="summary-letter"><b>D</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_4" class="summary-letter"><b>E</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_5" class="summary-letter"><b>F</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_6" class="summary-letter"><b>G</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_7" class="summary-letter"><b>H</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_8" class="summary-letter"><b>I</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_9" class="summary-letter"><b>J</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_10" class="summary-letter"><b>L</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_11" class="summary-letter"><b>M</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_12" class="summary-letter"><b>N</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_13" class="summary-letter"><b>O</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_14" class="summary-letter"><b>P</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_15" class="summary-letter"><b>R</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_16" class="summary-letter"><b>S</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_17" class="summary-letter"><b>T</b></a>
 &nbsp; 
<a href="#INDEX0_0" class="summary-letter"><b>U</b></a>
 &nbsp; 
<a href="libunistring_23.html#INDEX1_0" class="summary-letter"><b>V</b></a>
 &nbsp; 
<a href="libunistring_23.html#INDEX1_1" class="summary-letter"><b>W</b></a>
 &nbsp; 
</td></tr></table>
<table border="0" class="index-cp">
<tr><td></td><th align="left">Index Entry</th><th align="left"> Section</th></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="INDEX0_0">U</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX201"><code>u16_asnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX200"><code>u16_asprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX956"><code>u16_casecmp</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX968"><code>u16_casecoll</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX950"><code>u16_casefold</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX964"><code>u16_casexfrm</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX927"><code>u16_casing_prefix_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX930"><code>u16_casing_prefixes_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX935"><code>u16_casing_suffix_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX938"><code>u16_casing_suffixes_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX21"><code>u16_check</code></a></td><td valign="top"><a href="libunistring_4.html#SEC11">4.1 Elementary string checks</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX63"><code>u16_chr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC18">4.3.5 Searching for a character in a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX57"><code>u16_cmp</code></a></td><td valign="top"><a href="libunistring_4.html#SEC17">4.3.4 Comparing Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX60"><code>u16_cmp2</code></a></td><td valign="top"><a href="libunistring_4.html#SEC17">4.3.4 Comparing Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX157"><code>u16_conv_from_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX160"><code>u16_conv_to_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX45"><code>u16_cpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX69"><code>u16_cpy_alloc</code></a></td><td valign="top"><a href="libunistring_4.html#SEC20">4.4 Elementary string functions with memory allocation</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX953"><code>u16_ct_casefold</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX944"><code>u16_ct_tolower</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX947"><code>u16_ct_totitle</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX941"><code>u16_ct_toupper</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX143"><code>u16_endswith</code></a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX797"><code>u16_grapheme_breaks</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX791"><code>u16_grapheme_next</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX794"><code>u16_grapheme_prev</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX984"><code>u16_is_cased</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX981"><code>u16_is_casefolded</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX975"><code>u16_is_lowercase</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX978"><code>u16_is_titlecase</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX972"><code>u16_is_uppercase</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX30"><code>u16_mblen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX66"><code>u16_mbsnlen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC19">4.3.6 Counting the characters in a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX33"><code>u16_mbtouc</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX36"><code>u16_mbtouc_unsafe</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX39"><code>u16_mbtoucr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX51"><code>u16_move</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX79"><code>u16_next</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX892"><code>u16_normalize</code></a></td><td valign="top"><a href="libunistring_13.html#SEC66">13.3 Normalization of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX895"><code>u16_normcmp</code></a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX903"><code>u16_normcoll</code></a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX900"><code>u16_normxfrm</code></a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX48"><code>u16_pcpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#IDX855"><code>u16_possible_linebreaks</code></a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX82"><code>u16_prev</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX54"><code>u16_set</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX199"><code>u16_snprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX198"><code>u16_sprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX140"><code>u16_startswith</code></a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX94"><code>u16_stpcpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX100"><code>u16_stpncpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX103"><code>u16_strcat</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX122"><code>u16_strchr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX109"><code>u16_strcmp</code></a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX113"><code>u16_strcoll</code></a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX163"><code>u16_strconv_from_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX169"><code>u16_strconv_from_locale</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX166"><code>u16_strconv_to_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX172"><code>u16_strconv_to_locale</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX91"><code>u16_strcpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX128"><code>u16_strcspn</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX119"><code>u16_strdup</code></a></td><td valign="top"><a href="libunistring_4.html#SEC26">4.5.5 Duplicating a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX85"><code>u16_strlen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC23">4.5.2 Length of a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX72"><code>u16_strmblen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX76"><code>u16_strmbtouc</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX106"><code>u16_strncat</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX116"><code>u16_strncmp</code></a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX97"><code>u16_strncpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX88"><code>u16_strnlen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC23">4.5.2 Length of a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX134"><code>u16_strpbrk</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX125"><code>u16_strrchr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX131"><code>u16_strspn</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX137"><code>u16_strstr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX146"><code>u16_strtok</code></a></td><td valign="top"><a href="libunistring_4.html#SEC29">4.5.8 Tokenizing a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX788"><code>u16_strwidth</code></a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX26"><code>u16_to_u32</code></a></td><td valign="top"><a href="libunistring_4.html#SEC12">4.2 Elementary string conversions</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX25"><code>u16_to_u8</code></a></td><td valign="top"><a href="libunistring_4.html#SEC12">4.2 Elementary string conversions</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX919"><code>u16_tolower</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX922"><code>u16_totitle</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX916"><code>u16_toupper</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX209"><code>u16_u16_asnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX208"><code>u16_u16_asprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX207"><code>u16_u16_snprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX206"><code>u16_u16_sprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX213"><code>u16_u16_vasnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX212"><code>u16_u16_vasprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX211"><code>u16_u16_vsnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX210"><code>u16_u16_vsprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX42"><code>u16_uctomb</code></a></td><td valign="top"><a href="libunistring_4.html#SEC15">4.3.2 Creating Unicode strings one character at a time</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX205"><code>u16_vasnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX204"><code>u16_vasprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX203"><code>u16_vsnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX202"><code>u16_vsprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX785"><code>u16_width</code></a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#IDX859"><code>u16_width_linebreaks</code></a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_11.html#IDX822"><code>u16_wordbreaks</code></a></td><td valign="top"><a href="libunistring_11.html#SEC60">11.1 Word breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX217"><code>u32_asnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX216"><code>u32_asprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX957"><code>u32_casecmp</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX969"><code>u32_casecoll</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX951"><code>u32_casefold</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX965"><code>u32_casexfrm</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX928"><code>u32_casing_prefix_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX931"><code>u32_casing_prefixes_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX936"><code>u32_casing_suffix_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX939"><code>u32_casing_suffixes_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX22"><code>u32_check</code></a></td><td valign="top"><a href="libunistring_4.html#SEC11">4.1 Elementary string checks</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX64"><code>u32_chr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC18">4.3.5 Searching for a character in a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX58"><code>u32_cmp</code></a></td><td valign="top"><a href="libunistring_4.html#SEC17">4.3.4 Comparing Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX61"><code>u32_cmp2</code></a></td><td valign="top"><a href="libunistring_4.html#SEC17">4.3.4 Comparing Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX158"><code>u32_conv_from_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX161"><code>u32_conv_to_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX46"><code>u32_cpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX70"><code>u32_cpy_alloc</code></a></td><td valign="top"><a href="libunistring_4.html#SEC20">4.4 Elementary string functions with memory allocation</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX954"><code>u32_ct_casefold</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX945"><code>u32_ct_tolower</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX948"><code>u32_ct_totitle</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX942"><code>u32_ct_toupper</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX144"><code>u32_endswith</code></a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX798"><code>u32_grapheme_breaks</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX792"><code>u32_grapheme_next</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX795"><code>u32_grapheme_prev</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX985"><code>u32_is_cased</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX982"><code>u32_is_casefolded</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX976"><code>u32_is_lowercase</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX979"><code>u32_is_titlecase</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX973"><code>u32_is_uppercase</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX31"><code>u32_mblen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX67"><code>u32_mbsnlen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC19">4.3.6 Counting the characters in a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX34"><code>u32_mbtouc</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX37"><code>u32_mbtouc_unsafe</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX40"><code>u32_mbtoucr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX52"><code>u32_move</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX80"><code>u32_next</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX893"><code>u32_normalize</code></a></td><td valign="top"><a href="libunistring_13.html#SEC66">13.3 Normalization of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX896"><code>u32_normcmp</code></a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX904"><code>u32_normcoll</code></a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX901"><code>u32_normxfrm</code></a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX49"><code>u32_pcpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#IDX856"><code>u32_possible_linebreaks</code></a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX83"><code>u32_prev</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX55"><code>u32_set</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX215"><code>u32_snprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX214"><code>u32_sprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX141"><code>u32_startswith</code></a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX95"><code>u32_stpcpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX101"><code>u32_stpncpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX104"><code>u32_strcat</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX123"><code>u32_strchr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX110"><code>u32_strcmp</code></a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX114"><code>u32_strcoll</code></a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX164"><code>u32_strconv_from_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX170"><code>u32_strconv_from_locale</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX167"><code>u32_strconv_to_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX173"><code>u32_strconv_to_locale</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX92"><code>u32_strcpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX129"><code>u32_strcspn</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX120"><code>u32_strdup</code></a></td><td valign="top"><a href="libunistring_4.html#SEC26">4.5.5 Duplicating a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX86"><code>u32_strlen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC23">4.5.2 Length of a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX73"><code>u32_strmblen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX77"><code>u32_strmbtouc</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX107"><code>u32_strncat</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX117"><code>u32_strncmp</code></a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX98"><code>u32_strncpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX89"><code>u32_strnlen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC23">4.5.2 Length of a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX135"><code>u32_strpbrk</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX126"><code>u32_strrchr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX132"><code>u32_strspn</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX138"><code>u32_strstr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX147"><code>u32_strtok</code></a></td><td valign="top"><a href="libunistring_4.html#SEC29">4.5.8 Tokenizing a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX789"><code>u32_strwidth</code></a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX28"><code>u32_to_u16</code></a></td><td valign="top"><a href="libunistring_4.html#SEC12">4.2 Elementary string conversions</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX27"><code>u32_to_u8</code></a></td><td valign="top"><a href="libunistring_4.html#SEC12">4.2 Elementary string conversions</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX920"><code>u32_tolower</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX923"><code>u32_totitle</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX917"><code>u32_toupper</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX225"><code>u32_u32_asnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX224"><code>u32_u32_asprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX223"><code>u32_u32_snprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX222"><code>u32_u32_sprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX229"><code>u32_u32_vasnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX228"><code>u32_u32_vasprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX227"><code>u32_u32_vsnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX226"><code>u32_u32_vsprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX43"><code>u32_uctomb</code></a></td><td valign="top"><a href="libunistring_4.html#SEC15">4.3.2 Creating Unicode strings one character at a time</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX221"><code>u32_vasnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX220"><code>u32_vasprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX219"><code>u32_vsnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX218"><code>u32_vsprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX786"><code>u32_width</code></a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#IDX860"><code>u32_width_linebreaks</code></a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_11.html#IDX823"><code>u32_wordbreaks</code></a></td><td valign="top"><a href="libunistring_11.html#SEC60">11.1 Word breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX185"><code>u8_asnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX184"><code>u8_asprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX955"><code>u8_casecmp</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX967"><code>u8_casecoll</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX949"><code>u8_casefold</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX963"><code>u8_casexfrm</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX926"><code>u8_casing_prefix_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX929"><code>u8_casing_prefixes_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX934"><code>u8_casing_suffix_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX937"><code>u8_casing_suffixes_context</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX20"><code>u8_check</code></a></td><td valign="top"><a href="libunistring_4.html#SEC11">4.1 Elementary string checks</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX62"><code>u8_chr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC18">4.3.5 Searching for a character in a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX56"><code>u8_cmp</code></a></td><td valign="top"><a href="libunistring_4.html#SEC17">4.3.4 Comparing Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX59"><code>u8_cmp2</code></a></td><td valign="top"><a href="libunistring_4.html#SEC17">4.3.4 Comparing Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX156"><code>u8_conv_from_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX159"><code>u8_conv_to_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX44"><code>u8_cpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX68"><code>u8_cpy_alloc</code></a></td><td valign="top"><a href="libunistring_4.html#SEC20">4.4 Elementary string functions with memory allocation</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX952"><code>u8_ct_casefold</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX943"><code>u8_ct_tolower</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX946"><code>u8_ct_totitle</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX940"><code>u8_ct_toupper</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX142"><code>u8_endswith</code></a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX796"><code>u8_grapheme_breaks</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX790"><code>u8_grapheme_next</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX793"><code>u8_grapheme_prev</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX983"><code>u8_is_cased</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX980"><code>u8_is_casefolded</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX974"><code>u8_is_lowercase</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX977"><code>u8_is_titlecase</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX971"><code>u8_is_uppercase</code></a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX29"><code>u8_mblen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX65"><code>u8_mbsnlen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC19">4.3.6 Counting the characters in a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX32"><code>u8_mbtouc</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX35"><code>u8_mbtouc_unsafe</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX38"><code>u8_mbtoucr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX50"><code>u8_move</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX78"><code>u8_next</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX891"><code>u8_normalize</code></a></td><td valign="top"><a href="libunistring_13.html#SEC66">13.3 Normalization of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX894"><code>u8_normcmp</code></a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX902"><code>u8_normcoll</code></a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX899"><code>u8_normxfrm</code></a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX47"><code>u8_pcpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#IDX854"><code>u8_possible_linebreaks</code></a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX81"><code>u8_prev</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX53"><code>u8_set</code></a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX183"><code>u8_snprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX182"><code>u8_sprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX139"><code>u8_startswith</code></a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX93"><code>u8_stpcpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX99"><code>u8_stpncpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX102"><code>u8_strcat</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX121"><code>u8_strchr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX108"><code>u8_strcmp</code></a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX112"><code>u8_strcoll</code></a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX162"><code>u8_strconv_from_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX168"><code>u8_strconv_from_locale</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX165"><code>u8_strconv_to_encoding</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX171"><code>u8_strconv_to_locale</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX90"><code>u8_strcpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX127"><code>u8_strcspn</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX118"><code>u8_strdup</code></a></td><td valign="top"><a href="libunistring_4.html#SEC26">4.5.5 Duplicating a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX84"><code>u8_strlen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC23">4.5.2 Length of a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX71"><code>u8_strmblen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX75"><code>u8_strmbtouc</code></a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX105"><code>u8_strncat</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX115"><code>u8_strncmp</code></a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX96"><code>u8_strncpy</code></a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX87"><code>u8_strnlen</code></a></td><td valign="top"><a href="libunistring_4.html#SEC23">4.5.2 Length of a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX133"><code>u8_strpbrk</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX124"><code>u8_strrchr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX130"><code>u8_strspn</code></a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX136"><code>u8_strstr</code></a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX145"><code>u8_strtok</code></a></td><td valign="top"><a href="libunistring_4.html#SEC29">4.5.8 Tokenizing a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX787"><code>u8_strwidth</code></a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX23"><code>u8_to_u16</code></a></td><td valign="top"><a href="libunistring_4.html#SEC12">4.2 Elementary string conversions</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX24"><code>u8_to_u32</code></a></td><td valign="top"><a href="libunistring_4.html#SEC12">4.2 Elementary string conversions</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX918"><code>u8_tolower</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX921"><code>u8_totitle</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX915"><code>u8_toupper</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX193"><code>u8_u8_asnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX192"><code>u8_u8_asprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX191"><code>u8_u8_snprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX190"><code>u8_u8_sprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX197"><code>u8_u8_vasnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX196"><code>u8_u8_vasprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX195"><code>u8_u8_vsnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX194"><code>u8_u8_vsprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX41"><code>u8_uctomb</code></a></td><td valign="top"><a href="libunistring_4.html#SEC15">4.3.2 Creating Unicode strings one character at a time</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX189"><code>u8_vasnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX188"><code>u8_vasprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX187"><code>u8_vsnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX186"><code>u8_vsprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX784"><code>u8_width</code></a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#IDX858"><code>u8_width_linebreaks</code></a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_11.html#IDX821"><code>u8_wordbreaks</code></a></td><td valign="top"><a href="libunistring_11.html#SEC60">11.1 Word breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX756"><code>uc_all_blocks</code></a></td><td valign="top"><a href="libunistring_8.html#SEC52">8.12 Blocks</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX751"><code>uc_all_scripts</code></a></td><td valign="top"><a href="libunistring_8.html#SEC51">8.11 Scripts</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX413"><code>uc_bidi_category</code></a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX411"><code>uc_bidi_category_byname</code></a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX408"><code>uc_bidi_category_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX412"><code>uc_bidi_class</code></a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX410"><code>uc_bidi_class_byname</code></a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX409"><code>uc_bidi_class_long_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX407"><code>uc_bidi_class_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX754"><code>uc_block</code></a></td><td valign="top"><a href="libunistring_8.html#SEC52">8.12 Blocks</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX752"><code>uc_block_t</code></a></td><td valign="top"><a href="libunistring_8.html#SEC52">8.12 Blocks</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX764"><code>uc_c_ident_category</code></a></td><td valign="top"><a href="libunistring_8.html#SEC53">8.13 ISO C and Java syntax</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX881"><code>uc_canonical_decomposition</code></a></td><td valign="top"><a href="libunistring_13.html#SEC64">13.1 Decomposition of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX383"><code>uc_combining_class</code></a></td><td valign="top"><a href="libunistring_8.html#SEC37">8.2 Canonical combining class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX382"><code>uc_combining_class_byname</code></a></td><td valign="top"><a href="libunistring_8.html#SEC37">8.2 Canonical combining class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX381"><code>uc_combining_class_long_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC37">8.2 Canonical combining class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX380"><code>uc_combining_class_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC37">8.2 Canonical combining class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX882"><code>uc_composition</code></a></td><td valign="top"><a href="libunistring_13.html#SEC65">13.2 Composition of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX416"><code>uc_decimal_value</code></a></td><td valign="top"><a href="libunistring_8.html#SEC39">8.4 Decimal digit value</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX880"><code>uc_decomposition</code></a></td><td valign="top"><a href="libunistring_13.html#SEC64">13.1 Decomposition of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX417"><code>uc_digit_value</code></a></td><td valign="top"><a href="libunistring_8.html#SEC40">8.5 Digit value</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX418"><code>uc_fraction_t</code></a></td><td valign="top"><a href="libunistring_8.html#SEC41">8.6 Numeric value</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX319"><code>uc_general_category</code></a></td><td valign="top"><a href="libunistring_8.html#SEC35">8.1.1 The object oriented API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX314"><code>uc_general_category_and</code></a></td><td valign="top"><a href="libunistring_8.html#SEC35">8.1.1 The object oriented API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX315"><code>uc_general_category_and_not</code></a></td><td valign="top"><a href="libunistring_8.html#SEC35">8.1.1 The object oriented API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX318"><code>uc_general_category_byname</code></a></td><td valign="top"><a href="libunistring_8.html#SEC35">8.1.1 The object oriented API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX317"><code>uc_general_category_long_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC35">8.1.1 The object oriented API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX316"><code>uc_general_category_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC35">8.1.1 The object oriented API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX313"><code>uc_general_category_or</code></a></td><td valign="top"><a href="libunistring_8.html#SEC35">8.1.1 The object oriented API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX236"><code>uc_general_category_t</code></a></td><td valign="top"><a href="libunistring_8.html#SEC35">8.1.1 The object oriented API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX800"><code>uc_grapheme_breaks</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX819"><code>uc_graphemeclusterbreak_property</code></a></td><td valign="top"><a href="libunistring_10.html#SEC58">10.2 Grapheme cluster break property</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX745"><code>uc_indic_conjunct_break</code></a></td><td valign="top"><a href="libunistring_8.html#SEC50">8.10.1 Indic conjunct break</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX744"><code>uc_indic_conjunct_break_byname</code></a></td><td valign="top"><a href="libunistring_8.html#SEC50">8.10.1 Indic conjunct break</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX743"><code>uc_indic_conjunct_break_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC50">8.10.1 Indic conjunct break</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX767"><code>uc_is_alnum</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX768"><code>uc_is_alpha</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX415"><code>uc_is_bidi_category</code></a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX414"><code>uc_is_bidi_class</code></a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX778"><code>uc_is_blank</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX755"><code>uc_is_block</code></a></td><td valign="top"><a href="libunistring_8.html#SEC52">8.12 Blocks</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX757"><code>uc_is_c_whitespace</code></a></td><td valign="top"><a href="libunistring_8.html#SEC53">8.13 ISO C and Java syntax</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX769"><code>uc_is_cntrl</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX770"><code>uc_is_digit</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX320"><code>uc_is_general_category</code></a></td><td valign="top"><a href="libunistring_8.html#SEC35">8.1.1 The object oriented API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX359"><code>uc_is_general_category_withtable</code></a></td><td valign="top"><a href="libunistring_8.html#SEC36">8.1.2 The bit mask API for general category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX771"><code>uc_is_graph</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX820"><code>uc_is_grapheme_break</code></a></td><td valign="top"><a href="libunistring_10.html#SEC58">10.2 Grapheme cluster break property</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX758"><code>uc_is_java_whitespace</code></a></td><td valign="top"><a href="libunistring_8.html#SEC53">8.13 ISO C and Java syntax</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX772"><code>uc_is_lower</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX773"><code>uc_is_print</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX640"><code>uc_is_property</code></a></td><td valign="top"><a href="libunistring_8.html#SEC47">8.9.1 Properties as objects &ndash; the object oriented API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX642"><code>uc_is_property_alphabetic</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX699"><code>uc_is_property_ascii_hex_digit</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX688"><code>uc_is_property_bidi_arabic_digit</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX684"><code>uc_is_property_bidi_arabic_right_to_left</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX690"><code>uc_is_property_bidi_block_separator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX694"><code>uc_is_property_bidi_boundary_neutral</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX689"><code>uc_is_property_bidi_common_separator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX681"><code>uc_is_property_bidi_control</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX696"><code>uc_is_property_bidi_embedding_or_override</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX686"><code>uc_is_property_bidi_eur_num_separator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX687"><code>uc_is_property_bidi_eur_num_terminator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX685"><code>uc_is_property_bidi_european_digit</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX683"><code>uc_is_property_bidi_hebrew_right_to_left</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX682"><code>uc_is_property_bidi_left_to_right</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX693"><code>uc_is_property_bidi_non_spacing_mark</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX697"><code>uc_is_property_bidi_other_neutral</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX695"><code>uc_is_property_bidi_pdf</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX691"><code>uc_is_property_bidi_segment_separator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX692"><code>uc_is_property_bidi_whitespace</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX658"><code>uc_is_property_case_ignorable</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX657"><code>uc_is_property_cased</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX662"><code>uc_is_property_changes_when_casefolded</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX663"><code>uc_is_property_changes_when_casemapped</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX659"><code>uc_is_property_changes_when_lowercased</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX661"><code>uc_is_property_changes_when_titlecased</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX660"><code>uc_is_property_changes_when_uppercased</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX731"><code>uc_is_property_combining</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX732"><code>uc_is_property_composite</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX726"><code>uc_is_property_currency_symbol</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX718"><code>uc_is_property_dash</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX733"><code>uc_is_property_decimal_digit</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX645"><code>uc_is_property_default_ignorable_code_point</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX647"><code>uc_is_property_deprecated</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX735"><code>uc_is_property_diacritic</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX706"><code>uc_is_property_emoji</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX710"><code>uc_is_property_emoji_component</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX708"><code>uc_is_property_emoji_modifier</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX709"><code>uc_is_property_emoji_modifier_base</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX707"><code>uc_is_property_emoji_presentation</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX711"><code>uc_is_property_extended_pictographic</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX736"><code>uc_is_property_extender</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX716"><code>uc_is_property_format_control</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX676"><code>uc_is_property_grapheme_base</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX677"><code>uc_is_property_grapheme_extend</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX679"><code>uc_is_property_grapheme_link</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX698"><code>uc_is_property_hex_digit</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX719"><code>uc_is_property_hyphen</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX672"><code>uc_is_property_id_compat_math_continue</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX671"><code>uc_is_property_id_compat_math_start</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX667"><code>uc_is_property_id_continue</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX665"><code>uc_is_property_id_start</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX700"><code>uc_is_property_ideographic</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX704"><code>uc_is_property_ids_binary_operator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX705"><code>uc_is_property_ids_trinary_operator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX703"><code>uc_is_property_ids_unary_operator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX737"><code>uc_is_property_ignorable_control</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX715"><code>uc_is_property_iso_control</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX675"><code>uc_is_property_join_control</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX730"><code>uc_is_property_left_of_pair</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX721"><code>uc_is_property_line_separator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX648"><code>uc_is_property_logical_order_exception</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX654"><code>uc_is_property_lowercase</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX727"><code>uc_is_property_math</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX680"><code>uc_is_property_modifier_combining_mark</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX714"><code>uc_is_property_non_break</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX644"><code>uc_is_property_not_a_character</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX734"><code>uc_is_property_numeric</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX643"><code>uc_is_property_other_alphabetic</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX646"><code>uc_is_property_other_default_ignorable_code_point</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX678"><code>uc_is_property_other_grapheme_extend</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX668"><code>uc_is_property_other_id_continue</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX666"><code>uc_is_property_other_id_start</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX655"><code>uc_is_property_other_lowercase</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX728"><code>uc_is_property_other_math</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX653"><code>uc_is_property_other_uppercase</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX729"><code>uc_is_property_paired_punctuation</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX722"><code>uc_is_property_paragraph_separator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX674"><code>uc_is_property_pattern_syntax</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX673"><code>uc_is_property_pattern_white_space</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX717"><code>uc_is_property_prepended_concatenation_mark</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX650"><code>uc_is_property_private_use</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX720"><code>uc_is_property_punctuation</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX723"><code>uc_is_property_quotation_mark</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX702"><code>uc_is_property_radical</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX738"><code>uc_is_property_regional_indicator</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX724"><code>uc_is_property_sentence_terminal</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX664"><code>uc_is_property_soft_dotted</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX713"><code>uc_is_property_space</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX725"><code>uc_is_property_terminal_punctuation</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX656"><code>uc_is_property_titlecase</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX651"><code>uc_is_property_unassigned_code_value</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX701"><code>uc_is_property_unified_ideograph</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX652"><code>uc_is_property_uppercase</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX649"><code>uc_is_property_variation_selector</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX641"><code>uc_is_property_white_space</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX670"><code>uc_is_property_xid_continue</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX669"><code>uc_is_property_xid_start</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX712"><code>uc_is_property_zero_width</code></a></td><td valign="top"><a href="libunistring_8.html#SEC48">8.9.2 Properties as functions &ndash; the functional API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX774"><code>uc_is_punct</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX750"><code>uc_is_script</code></a></td><td valign="top"><a href="libunistring_8.html#SEC51">8.11 Scripts</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX775"><code>uc_is_space</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX776"><code>uc_is_upper</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX777"><code>uc_is_xdigit</code></a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX766"><code>uc_java_ident_category</code></a></td><td valign="top"><a href="libunistring_8.html#SEC53">8.13 ISO C and Java syntax</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX538"><code>uc_joining_group</code></a></td><td valign="top"><a href="libunistring_8.html#SEC45">8.8.2 Joining group of Arabic characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX537"><code>uc_joining_group_byname</code></a></td><td valign="top"><a href="libunistring_8.html#SEC45">8.8.2 Joining group of Arabic characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX536"><code>uc_joining_group_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC45">8.8.2 Joining group of Arabic characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX430"><code>uc_joining_type</code></a></td><td valign="top"><a href="libunistring_8.html#SEC44">8.8.1 Joining type of Arabic characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX429"><code>uc_joining_type_byname</code></a></td><td valign="top"><a href="libunistring_8.html#SEC44">8.8.1 Joining type of Arabic characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX428"><code>uc_joining_type_long_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC44">8.8.1 Joining type of Arabic characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX427"><code>uc_joining_type_name</code></a></td><td valign="top"><a href="libunistring_8.html#SEC44">8.8.1 Joining type of Arabic characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX914"><code>uc_locale_language</code></a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX420"><code>uc_mirror_char</code></a></td><td valign="top"><a href="libunistring_8.html#SEC42">8.7 Mirrored character</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX419"><code>uc_numeric_value</code></a></td><td valign="top"><a href="libunistring_8.html#SEC41">8.6 Numeric value</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX638"><code>uc_property_byname</code></a></td><td valign="top"><a href="libunistring_8.html#SEC47">8.9.1 Properties as objects &ndash; the object oriented API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX639"><code>uc_property_is_valid</code></a></td><td valign="top"><a href="libunistring_8.html#SEC47">8.9.1 Properties as objects &ndash; the object oriented API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX539"><code>uc_property_t</code></a></td><td valign="top"><a href="libunistring_8.html#SEC47">8.9.1 Properties as objects &ndash; the object oriented API</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX748"><code>uc_script</code></a></td><td valign="top"><a href="libunistring_8.html#SEC51">8.11 Scripts</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX749"><code>uc_script_byname</code></a></td><td valign="top"><a href="libunistring_8.html#SEC51">8.11 Scripts</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX746"><code>uc_script_t</code></a></td><td valign="top"><a href="libunistring_8.html#SEC51">8.11 Scripts</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX911"><code>uc_tolower</code></a></td><td valign="top"><a href="libunistring_14.html#SEC70">14.1 Case mappings of characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX912"><code>uc_totitle</code></a></td><td valign="top"><a href="libunistring_14.html#SEC70">14.1 Case mappings of characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX910"><code>uc_toupper</code></a></td><td valign="top"><a href="libunistring_14.html#SEC70">14.1 Case mappings of characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX783"><code>uc_width</code></a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_11.html#IDX847"><code>uc_wordbreak_property</code></a></td><td valign="top"><a href="libunistring_11.html#SEC61">11.2 Word break property</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX6">UCS-4</a></td><td valign="top"><a href="libunistring_1.html#SEC2">1.1 Unicode</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_3.html#IDX19"><code>ucs4_t</code></a></td><td valign="top"><a href="libunistring_3.html#SEC9">3. Elementary types <code>&lt;unitypes.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_3.html#IDX17"><code>uint16_t</code></a></td><td valign="top"><a href="libunistring_3.html#SEC9">3. Elementary types <code>&lt;unitypes.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_3.html#IDX18"><code>uint32_t</code></a></td><td valign="top"><a href="libunistring_3.html#SEC9">3. Elementary types <code>&lt;unitypes.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_3.html#IDX16"><code>uint8_t</code></a></td><td valign="top"><a href="libunistring_3.html#SEC9">3. Elementary types <code>&lt;unitypes.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX177"><code>ulc_asnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX176"><code>ulc_asprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX958"><code>ulc_casecmp</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX970"><code>ulc_casecoll</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX966"><code>ulc_casexfrm</code></a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX230"><code>ulc_fprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#IDX799"><code>ulc_grapheme_breaks</code></a></td><td valign="top"><a href="libunistring_10.html#SEC57">10.1 Grapheme cluster breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#IDX857"><code>ulc_possible_linebreaks</code></a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX175"><code>ulc_snprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX174"><code>ulc_sprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX181"><code>ulc_vasnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX180"><code>ulc_vasprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX231"><code>ulc_vfprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX179"><code>ulc_vsnprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#IDX178"><code>ulc_vsprintf</code></a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#IDX861"><code>ulc_width_linebreaks</code></a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_11.html#IDX824"><code>ulc_wordbreaks</code></a></td><td valign="top"><a href="libunistring_11.html#SEC60">11.1 Word breaks in a string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#SEC2">Unicode</a></td><td valign="top"><a href="libunistring_1.html#SEC2">1.1 Unicode</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC38">Unicode character, bidi class</a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC38">Unicode character, bidirectional category</a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX753">Unicode character, block</a></td><td valign="top"><a href="libunistring_8.html#SEC52">8.12 Blocks</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC37">Unicode character, canonical combining class</a></td><td valign="top"><a href="libunistring_8.html#SEC37">8.2 Canonical combining class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#SEC70">Unicode character, case mappings</a></td><td valign="top"><a href="libunistring_14.html#SEC70">14.1 Case mappings of characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC34">Unicode character, classification</a></td><td valign="top"><a href="libunistring_8.html#SEC34">8.1 General category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC54">Unicode character, classification like in C</a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC34">Unicode character, general category</a></td><td valign="top"><a href="libunistring_8.html#SEC34">8.1 General category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC42">Unicode character, mirroring</a></td><td valign="top"><a href="libunistring_8.html#SEC42">8.7 Mirrored character</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_7.html#SEC32">Unicode character, name</a></td><td valign="top"><a href="libunistring_7.html#SEC32">7. Names of Unicode characters <code>&lt;uniname.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC46">Unicode character, properties</a></td><td valign="top"><a href="libunistring_8.html#SEC46">8.9 Properties</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX747">Unicode character, script</a></td><td valign="top"><a href="libunistring_8.html#SEC51">8.11 Scripts</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX763">Unicode character, validity in C identifiers</a></td><td valign="top"><a href="libunistring_8.html#SEC53">8.13 ISO C and Java syntax</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#IDX765">Unicode character, validity in Java identifiers</a></td><td valign="top"><a href="libunistring_8.html#SEC53">8.13 ISO C and Java syntax</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC39">Unicode character, value</a></td><td valign="top"><a href="libunistring_8.html#SEC39">8.4 Decimal digit value</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC40">Unicode character, value</a></td><td valign="top"><a href="libunistring_8.html#SEC40">8.5 Digit value</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC41">Unicode character, value</a></td><td valign="top"><a href="libunistring_8.html#SEC41">8.6 Numeric value</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX780">Unicode character, width</a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_7.html#IDX233"><code>unicode_character_name</code></a></td><td valign="top"><a href="libunistring_7.html#SEC32">7. Names of Unicode characters <code>&lt;uniname.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_7.html#IDX234"><code>unicode_name_character</code></a></td><td valign="top"><a href="libunistring_7.html#SEC32">7. Names of Unicode characters <code>&lt;uniname.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX890"><code>uninorm_decomposing_form</code></a></td><td valign="top"><a href="libunistring_13.html#SEC66">13.3 Normalization of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX906"><code>uninorm_filter_create</code></a></td><td valign="top"><a href="libunistring_13.html#SEC68">13.5 Normalization of streams of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX908"><code>uninorm_filter_flush</code></a></td><td valign="top"><a href="libunistring_13.html#SEC68">13.5 Normalization of streams of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX909"><code>uninorm_filter_free</code></a></td><td valign="top"><a href="libunistring_13.html#SEC68">13.5 Normalization of streams of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX907"><code>uninorm_filter_write</code></a></td><td valign="top"><a href="libunistring_13.html#SEC68">13.5 Normalization of streams of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX888"><code>uninorm_is_compat_decomposing</code></a></td><td valign="top"><a href="libunistring_13.html#SEC66">13.3 Normalization of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX889"><code>uninorm_is_composing</code></a></td><td valign="top"><a href="libunistring_13.html#SEC66">13.3 Normalization of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX883"><code>uninorm_t</code></a></td><td valign="top"><a href="libunistring_13.html#SEC66">13.3 Normalization of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#SEC71">uppercasing</a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX1">use cases</a></td><td valign="top"><a href="libunistring_1.html#SEC1">1. Introduction</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX4">UTF-16</a></td><td valign="top"><a href="libunistring_1.html#SEC2">1.1 Unicode</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX12">UTF-16, strings</a></td><td valign="top"><a href="libunistring_1.html#SEC7">1.6 Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX5">UTF-32</a></td><td valign="top"><a href="libunistring_1.html#SEC2">1.1 Unicode</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX13">UTF-32, strings</a></td><td valign="top"><a href="libunistring_1.html#SEC7">1.6 Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX3">UTF-8</a></td><td valign="top"><a href="libunistring_1.html#SEC2">1.1 Unicode</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX11">UTF-8, strings</a></td><td valign="top"><a href="libunistring_1.html#SEC7">1.6 Unicode strings</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
</table>
<table><tr><th valign="top">Jump to: &nbsp; </th><td><a href="libunistring_21.html#SEC94_0" class="summary-letter"><b>A</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_1" class="summary-letter"><b>B</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_2" class="summary-letter"><b>C</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_3" class="summary-letter"><b>D</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_4" class="summary-letter"><b>E</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_5" class="summary-letter"><b>F</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_6" class="summary-letter"><b>G</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_7" class="summary-letter"><b>H</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_8" class="summary-letter"><b>I</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_9" class="summary-letter"><b>J</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_10" class="summary-letter"><b>L</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_11" class="summary-letter"><b>M</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_12" class="summary-letter"><b>N</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_13" class="summary-letter"><b>O</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_14" class="summary-letter"><b>P</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_15" class="summary-letter"><b>R</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_16" class="summary-letter"><b>S</b></a>
 &nbsp; 
<a href="libunistring_21.html#SEC94_17" class="summary-letter"><b>T</b></a>
 &nbsp; 
<a href="#INDEX0_0" class="summary-letter"><b>U</b></a>
 &nbsp; 
<a href="libunistring_23.html#INDEX1_0" class="summary-letter"><b>V</b></a>
 &nbsp; 
<a href="libunistring_23.html#INDEX1_1" class="summary-letter"><b>W</b></a>
 &nbsp; 
</td></tr></table>
<hr size="6">
<table cellpadding="1" cellspacing="1" border="0">
<tr><td valign="middle" align="left">[<a href="libunistring_21.html#SEC94" title="Beginning of this chapter or previous chapter"> &lt;&lt; </a>]</td>
<td valign="middle" align="left">[<a href="libunistring_23.html#INDEX1" title="Next chapter"> &gt;&gt; </a>]</td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Top" title="Cover (top) of document">Top</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Contents" title="Table of contents">Contents</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_21.html#SEC94" title="Index">Index</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_abt.html#SEC_About" title="About (help)"> ? </a>]</td>
</tr></table>
<p>
 <font size="-1">
  This document was generated by <em>Bruno Haible</em> on <em>October, 16 2024</em> using <a href="https://www.nongnu.org/texi2html/"><em>texi2html 1.78a</em></a>.
 </font>
 <br>

</p>
</body>
</html>
