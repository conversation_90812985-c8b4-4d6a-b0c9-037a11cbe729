<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_KDF-SCRYPT</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
      <li><a href="#Supported-parameters">Supported parameters</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_KDF-SCRYPT - The scrypt EVP_KDF implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing the <b>scrypt</b> password-based KDF through the <b>EVP_KDF</b> API.</p>

<p>The EVP_KDF-SCRYPT algorithm implements the scrypt password-based key derivation function, as described in RFC 7914. It is memory-hard in the sense that it deliberately requires a significant amount of RAM for efficient computation. The intention of this is to render brute forcing of passwords on systems that lack large amounts of main memory (such as GPUs or ASICs) computationally infeasible.</p>

<p>scrypt provides three work factors that can be customized: N, r and p. N, which has to be a positive power of two, is the general work factor and scales CPU time in an approximately linear fashion. r is the block size of the internally used hash function and p is the parallelization factor. Both r and p need to be greater than zero. The amount of RAM that scrypt requires for its computation is roughly (128 * N * r * p) bytes.</p>

<p>In the original paper of Colin Percival (&quot;Stronger Key Derivation via Sequential Memory-Hard Functions&quot;, 2009), the suggested values that give a computation time of less than 5 seconds on a 2.5 GHz Intel Core 2 Duo are N = 2^20 = 1048576, r = 8, p = 1. Consequently, the required amount of memory for this computation is roughly 1 GiB. On a more recent CPU (Intel i7-5930K at 3.5 GHz), this computation takes about 3 seconds. When N, r or p are not specified, they default to 1048576, 8, and 1, respectively. The maximum amount of RAM that may be used by scrypt defaults to 1025 MiB.</p>

<h2 id="Identity">Identity</h2>

<p>&quot;SCRYPT&quot; is the name for this implementation; it can be used with the EVP_KDF_fetch() function.</p>

<h2 id="Supported-parameters">Supported parameters</h2>

<p>The supported parameters are:</p>

<dl>

<dt id="pass-OSSL_KDF_PARAM_PASSWORD-octet-string">&quot;pass&quot; (<b>OSSL_KDF_PARAM_PASSWORD</b>) &lt;octet string&gt;</dt>
<dd>

</dd>
<dt id="salt-OSSL_KDF_PARAM_SALT-octet-string">&quot;salt&quot; (<b>OSSL_KDF_PARAM_SALT</b>) &lt;octet string&gt;</dt>
<dd>

<p>These parameters work as described in <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a>.</p>

</dd>
<dt id="n-OSSL_KDF_PARAM_SCRYPT_N-unsigned-integer">&quot;n&quot; (<b>OSSL_KDF_PARAM_SCRYPT_N</b>) &lt;unsigned integer&gt;</dt>
<dd>

</dd>
<dt id="r-OSSL_KDF_PARAM_SCRYPT_R-unsigned-integer">&quot;r&quot; (<b>OSSL_KDF_PARAM_SCRYPT_R</b>) &lt;unsigned integer&gt;</dt>
<dd>

</dd>
<dt id="p-OSSL_KDF_PARAM_SCRYPT_P-unsigned-integer">&quot;p&quot; (<b>OSSL_KDF_PARAM_SCRYPT_P</b>) &lt;unsigned integer&gt;</dt>
<dd>

</dd>
<dt id="maxmem_bytes-OSSL_KDF_PARAM_SCRYPT_MAXMEM-unsigned-integer">&quot;maxmem_bytes&quot; (<b>OSSL_KDF_PARAM_SCRYPT_MAXMEM</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>These parameters configure the scrypt work factors N, r, maxmem and p. Both N and maxmem_bytes are parameters of type <b>uint64_t</b>. Both r and p are parameters of type <b>uint32_t</b>.</p>

</dd>
<dt id="properties-OSSL_KDF_PARAM_PROPERTIES-UTF8-string">&quot;properties&quot; (<b>OSSL_KDF_PARAM_PROPERTIES</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>This can be used to set the property query string when fetching the fixed digest internally. NULL is used if this value is not set.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>A context for scrypt can be obtained by calling:</p>

<pre><code>EVP_KDF *kdf = EVP_KDF_fetch(NULL, &quot;SCRYPT&quot;, NULL);
EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);</code></pre>

<p>The output length of an scrypt key derivation is specified via the &quot;keylen&quot; parameter to the <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a> function.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>This example derives a 64-byte long test vector using scrypt with the password &quot;password&quot;, salt &quot;NaCl&quot; and N = 1024, r = 8, p = 16.</p>

<pre><code>EVP_KDF *kdf;
EVP_KDF_CTX *kctx;
unsigned char out[64];
OSSL_PARAM params[6], *p = params;

kdf = EVP_KDF_fetch(NULL, &quot;SCRYPT&quot;, NULL);
kctx = EVP_KDF_CTX_new(kdf);
EVP_KDF_free(kdf);

*p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_PASSWORD,
                                         &quot;password&quot;, (size_t)8);
*p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
                                         &quot;NaCl&quot;, (size_t)4);
*p++ = OSSL_PARAM_construct_uint64(OSSL_KDF_PARAM_SCRYPT_N, (uint64_t)1024);
*p++ = OSSL_PARAM_construct_uint32(OSSL_KDF_PARAM_SCRYPT_R, (uint32_t)8);
*p++ = OSSL_PARAM_construct_uint32(OSSL_KDF_PARAM_SCRYPT_P, (uint32_t)16);
*p = OSSL_PARAM_construct_end();
if (EVP_KDF_derive(kctx, out, sizeof(out), params) &lt;= 0) {
    error(&quot;EVP_KDF_derive&quot;);
}

{
    const unsigned char expected[sizeof(out)] = {
        0xfd, 0xba, 0xbe, 0x1c, 0x9d, 0x34, 0x72, 0x00,
        0x78, 0x56, 0xe7, 0x19, 0x0d, 0x01, 0xe9, 0xfe,
        0x7c, 0x6a, 0xd7, 0xcb, 0xc8, 0x23, 0x78, 0x30,
        0xe7, 0x73, 0x76, 0x63, 0x4b, 0x37, 0x31, 0x62,
        0x2e, 0xaf, 0x30, 0xd9, 0x2e, 0x22, 0xa3, 0x88,
        0x6f, 0xf1, 0x09, 0x27, 0x9d, 0x98, 0x30, 0xda,
        0xc7, 0x27, 0xaf, 0xb9, 0x4a, 0x83, 0xee, 0x6d,
        0x83, 0x60, 0xcb, 0xdf, 0xa2, 0xcc, 0x06, 0x40
    };

    assert(!memcmp(out, expected, sizeof(out)));
}

EVP_KDF_CTX_free(kctx);</code></pre>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>RFC 7914</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_KDF.html">EVP_KDF(3)</a>, <a href="../man3/EVP_KDF_CTX_new.html">EVP_KDF_CTX_new(3)</a>, <a href="../man3/EVP_KDF_CTX_free.html">EVP_KDF_CTX_free(3)</a>, <a href="../man3/EVP_KDF_CTX_set_params.html">EVP_KDF_CTX_set_params(3)</a>, <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a>, <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


