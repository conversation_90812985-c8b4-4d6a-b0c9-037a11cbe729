.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_recv" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_recv \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_recv(gnutls_session_t " session ", void * " data ", size_t " data_size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "void * data" 12
the buffer that the data will be read into
.IP "size_t data_size" 12
the number of requested bytes
.SH "DESCRIPTION"
This function has the similar semantics with \fBrecv()\fP.  The only
difference is that it accepts a GnuTLS session, and uses different
error codes.
In the special case that the peer requests a renegotiation, the
caller will receive an error code of \fBGNUTLS_E_REHANDSHAKE\fP.  In case
of a client, this message may be simply ignored, replied with an alert
\fBGNUTLS_A_NO_RENEGOTIATION\fP, or replied with a new handshake,
depending on the client's will. A server receiving this error code
can only initiate a new handshake or terminate the session.

If \fBEINTR\fP is returned by the internal pull function (the default
is \fBrecv()\fP) then \fBGNUTLS_E_INTERRUPTED\fP will be returned.  If
\fBGNUTLS_E_INTERRUPTED\fP or \fBGNUTLS_E_AGAIN\fP is returned, you must
call this function again to get the data.  See also
\fBgnutls_record_get_direction()\fP.
.SH "RETURNS"
The number of bytes received and zero on EOF (for stream
connections).  A negative error code is returned in case of an error.
The number of bytes received might be less than the requested  \fIdata_size\fP .
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
