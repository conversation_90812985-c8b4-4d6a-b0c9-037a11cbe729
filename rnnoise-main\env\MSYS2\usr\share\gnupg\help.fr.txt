# help.fr.txt - fr GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
C'est à vous d'assigner une valeur ici; cette valeur ne sera jamais
envoyée à une tierce personne. Nous en avons besoin pour créer le réseau
de confiance (web-of-trust); cela n'a rien à voir avec le réseau des
certificats (créé implicitement)
.

.gpg.edit_ownertrust.set_ultimate.okay
Pour mettre en place le Réseau de confiance (Web of Trust), GnuPG a
besoin de savoir en quelles clés votre confiance est ultime - ce sont
en général les clés dont vous avez accès à la clé secrète. Répondez
"oui" pour indiquer que votre confiance en cette clé est ultime

.

.gpg.untrusted_key.override
Si vous voulez utiliser cette clé peu sûre quand-même, répondez «oui».
.

.gpg.pklist.user_id.enter
Entrez le nom d'utilisateur de la personne à qui vous voulez envoyer
le message.
.

.gpg.keygen.algo
Sélectionnez l'algorithme à utiliser.

DSA (connu également sous le nom de DSS) est un algorithme de signature
digitale et ne peut être utilisé que pour des signatures.

Elgamal est un algorithme pour le chiffrement seul.

RSA peut être utilisé pour les signatures et le chiffrement.

La première clé (clé principale) doit toujours être une clé capable
de signer.
.

.gpg.keygen.algo.rsa_se
En général ce n'est pas une bonne idée d'utiliser la même clé pour
signer et pour chiffrer. Cet algorithme ne doit être utilisé que
pour certains domaines.
Consultez votre expert en sécurité d'abord.
.

.gpg.keygen.size
Entrez la taille de la clé
.

.gpg.keygen.size.huge.okay
Répondez «oui» ou «non»
.

.gpg.keygen.size.large.okay
Répondez «oui» ou «non»
.

.gpg.keygen.valid
Entrez la valeur demandée comme indiqué dans la ligne de commande.
On peut entrer une date ISO (AAAA-MM-JJ) mais le résultat d'erreur sera
mauvais - le système essaierait d'interpréter la valeur donnée comme un
intervalle.
.

.gpg.keygen.valid.okay
Répondez «oui» ou «non»
.

.gpg.keygen.name
Entrez le nom du propriétaire de la clé
.

.gpg.keygen.email
entrez une adresse e-mail optionnelle mais hautement recommandée
.

.gpg.keygen.comment
Entrez un commentaire optionnel
.

.gpg.keygen.userid.cmd
N pour changer le nom.
C pour changer le commentaire.
E pour changer l'adresse e-mail.
O pour continuer à générer la clé.
Q pour arrêter de générer de clé.
.

.gpg.keygen.sub.okay
Répondez «oui» (ou simplement «o») pour générer la sous-clé
.

.gpg.sign_uid.okay
Répondez «oui» ou «non»
.

.gpg.sign_uid.class
Quand vous signez un nom d'utilisateur d'une clé, vous devriez d'abord
vérifier que la clé appartient à la personne nommée. Il est utile que
les autres personnes sachent avec quel soin vous l'avez vérifié.

"0" signifie que vous n'avez pas d'opinon.

"1" signifie que vous croyez que la clé appartient à la personne qui
dit la posséder mais vous n'avez pas pu vérifier du tout la clé.
C'est utile lorsque vous signez la clé d'un pseudonyme.

"2" signifie que vous avez un peu vérifié la clé. Par exemple, cela
pourrait être un vérification de l'empreinte et du nom de
l'utilisateur avec la photo.

"3" signifie que vous avez complètement vérifié la clé. Par exemple,
cela pourrait être une vérification de l'empreinte, du nom de
l'utilisateur avec un document difficile à contrefaire (comme un
passeport) et de son adresse e-mail (vérifié par un échange de
courrier électronique).

Notez bien que les exemples donnés ci-dessus pour les niveaux 2 et
3 ne sont *que* des exemples.
C'est à vous de décider quelle valeur mettre quand vous signez
les clés des autres personnes.

Si vous ne savez pas quelle réponse est la bonne, répondez "0".
.

.gpg.change_passwd.empty.okay
Répondez «oui» ou «non»
.

.gpg.keyedit.save.okay
Répondez «oui» ou «non»
.

.gpg.keyedit.cancel.okay
Répondez «oui» ou «non»
.

.gpg.keyedit.sign_all.okay
Répondez «oui» si vous voulez signer TOUS les noms d'utilisateurs
.

.gpg.keyedit.remove.uid.okay
Répondez «oui» si vous voulez vraiment supprimer ce nom
d'utilisateur. Tous les certificats seront alors perdus en même temps !
.

.gpg.keyedit.remove.subkey.okay
Répondez «oui» s'il faut vraiment supprimer la sous-clé
.

.gpg.keyedit.delsig.valid
C'est une signature valide dans la clé; vous n'avez pas normalement
intérêt à supprimer cette signature car elle peut être importante pour
établir une connection de confiance vers la clé ou une autre clé certifiée
par celle-là.
.

.gpg.keyedit.delsig.unknown
Cette signature ne peut pas être vérifiée parce que vous n'avez pas la
clé correspondante. Vous devriez remettre sa supression jusqu'à ce que
vous soyez sûr de quelle clé a été utilisée car cette clé de signature
peut établir une connection de confiance vers une autre clé déjà certifiée.
.

.gpg.keyedit.delsig.invalid
Cette signature n'est pas valide. Vous devriez la supprimer de votre
porte-clés.
.

.gpg.keyedit.delsig.selfsig
Cette signature relie le nom d'utilisateur à la clé. Habituellement
enlever une telle signature n'est pas une bonne idée. En fait GnuPG peut
ne plus être capable d'utiliser cette clé. Donc faites ceci uniquement si
cette auto-signature est invalide pour une certaine raison et si une autre
est disponible.
.

.gpg.keyedit.updpref.okay
Changer les préférences de tous les noms d'utilisateurs (ou juste
ceux qui sont sélectionnés) vers la liste actuelle. La date de toutes
les auto-signatures affectées seront avancées d'une seconde.

.

.gpg.passphrase.enter
Entrez le mot de passe ; c'est une phrase secrète 

.

.gpg.passphrase.repeat
Répétez la dernière phrase de passe pour être sûr de ce que vous
avez tapé.
.

.gpg.detached_signature.filename
Donnez le nom du fichier auquel la signature se rapporte
.

.gpg.openfile.overwrite.okay
Répondez «oui» s'il faut vraiment réécrire le fichier
.

.gpg.openfile.askoutname
Entrez le nouveau nom de fichier. Si vous tapez simplement ENTRÉE le
fichier par défaut (indiqué entre crochets) sera utilisé.
.

.gpg.ask_revocation_reason.code
Vous devriez donner une raison pour la certification. Selon le contexte
vous pouvez choisir dans cette liste:
  «La clé a été compromise»
      Utilisez cette option si vous avez une raison de croire que des
      personnes ont pu accéder à votre clé secrète sans autorisation.
  «La clé a été remplacée»
      Utilisez cette option si vous avez remplacé la clé par une nouvelle.
  «La clé n'est plus utilisée»
      Utilisez cette option si cette clé n'a plus d'utilité.
  «Le nom d'utilisateur n'est plus valide»
      Utilisez cette option si le nom d'utilisateur ne doit plus être
      utilisé. Cela sert généralement à indiquer qu'une adresse e-mail
      est invalide.

.

.gpg.ask_revocation_reason.text
Si vous le désirez, vous pouvez entrer un texte qui explique pourquoi vous
avez émis ce certificat de révocation. Essayez de garder ce texte concis.
Une ligne vide délimite la fin du texte.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
