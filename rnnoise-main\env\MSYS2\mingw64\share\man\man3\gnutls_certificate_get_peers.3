.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_peers" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_peers \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const gnutls_datum_t * gnutls_certificate_get_peers(gnutls_session_t " session ", unsigned int * " list_size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.IP "unsigned int * list_size" 12
is the length of the certificate list (may be \fBNULL\fP)
.SH "DESCRIPTION"
Get the peer's raw certificate (chain) as sent by the peer.  These
certificates are in raw format (DER encoded for X.509).  In case of
a X.509 then a certificate list may be present.  The list
is provided as sent by the server; the server must send as first
certificate in the list its own certificate, following the
issuer's certificate, then the issuer's issuer etc. However, there
are servers which violate this principle and thus on certain
occasions this may be an unsorted list.

In resumed sessions, this function will return the peer's certificate
list as used in the first/original session.
.SH "RETURNS"
a pointer to a \fBgnutls_datum_t\fP containing the peer's
certificates, or \fBNULL\fP in case of an error or if no certificate
was used.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
