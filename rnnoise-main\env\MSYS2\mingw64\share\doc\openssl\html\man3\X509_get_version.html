<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_get_version</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_get_version, X509_set_version, X509_REQ_get_version, X509_REQ_set_version, X509_ACERT_get_version, X509_ACERT_set_version, X509_CRL_get_version, X509_CRL_set_version - get or set certificate, certificate request or CRL version</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

long X509_get_version(const X509 *x);
int X509_set_version(X509 *x, long version);

long X509_REQ_get_version(const X509_REQ *req);
int X509_REQ_set_version(X509_REQ *x, long version);

long X509_CRL_get_version(const X509_CRL *crl);
int X509_CRL_set_version(X509_CRL *x, long version);

#include &lt;openssl/x509_acert.h&gt;

int X509_ACERT_set_version(X509_ACERT *x, long version);
long X509_ACERT_get_version(const X509_ACERT *x);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_get_version() returns the numerical value of the version field of certificate <i>x</i>. These correspond to the constants <b>X509_VERSION_1</b>, <b>X509_VERSION_2</b>, and <b>X509_VERSION_3</b>. Note: the values of these constants are defined by standards (X.509 et al) to be one less than the certificate version. So <b>X509_VERSION_3</b> has value 2 and <b>X509_VERSION_1</b> has value 0.</p>

<p>X509_set_version() sets the numerical value of the version field of certificate <i>x</i> to <i>version</i>.</p>

<p>Similarly X509_REQ_get_version(), X509_REQ_set_version(), X509_ACERT_get_version(), X509_ACERT_set_version(), X509_CRL_get_version() and X509_CRL_set_version() get and set the version number of certificate requests and CRLs. They use constants <b>X509_REQ_VERSION_1</b>, <b>X509_ACERT_VERSION_2</b>, <b>X509_CRL_VERSION_1</b>, and <b>X509_CRL_VERSION_2</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The version field of certificates, certificate requests and CRLs has a DEFAULT value of <b>v1(0)</b> meaning the field should be omitted for version 1. This is handled transparently by these functions.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_get_version(), X509_REQ_get_version() and X509_CRL_get_version() return the numerical value of the version field.</p>

<p>X509_set_version(), X509_REQ_set_version() and X509_CRL_set_version() return 1 for success and 0 for failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/d2i_X509.html">d2i_X509(3)</a>, <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/X509_CRL_get0_by_serial.html">X509_CRL_get0_by_serial(3)</a>, <a href="../man3/X509_get0_signature.html">X509_get0_signature(3)</a>, <a href="../man3/X509_get_ext_d2i.html">X509_get_ext_d2i(3)</a>, <a href="../man3/X509_get_extension_flags.html">X509_get_extension_flags(3)</a>, <a href="../man3/X509_get_pubkey.html">X509_get_pubkey(3)</a>, <a href="../man3/X509_get_subject_name.html">X509_get_subject_name(3)</a>, <a href="../man3/X509_NAME_add_entry_by_txt.html">X509_NAME_add_entry_by_txt(3)</a>, <a href="../man3/X509_NAME_ENTRY_get_object.html">X509_NAME_ENTRY_get_object(3)</a>, <a href="../man3/X509_NAME_get_index_by_NID.html">X509_NAME_get_index_by_NID(3)</a>, <a href="../man3/X509_NAME_print_ex.html">X509_NAME_print_ex(3)</a>, <a href="../man3/X509_new.html">X509_new(3)</a>, <a href="../man3/X509_sign.html">X509_sign(3)</a>, <a href="../man3/X509V3_get_d2i.html">X509V3_get_d2i(3)</a>, <a href="../man3/X509_verify_cert.html">X509_verify_cert(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>X509_get_version(), X509_REQ_get_version() and X509_CRL_get_version() are functions in OpenSSL 1.1.0, in previous versions they were macros.</p>

<p>X509_ACERT_get_version(), X509_ACERT_set_version() were added in OpenSSL 3.4.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


