<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('functions.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="contents">
<div class="textblock">Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:</div>

<h3><a id="index_a" name="index_a"></a>- a -</h3><ul>
<li>alloc&#160;:&#160;<a class="el" href="structlzma__allocator.html#aba5c4369af94cc9943423b49171462ec">lzma_allocator</a></li>
<li>allocator&#160;:&#160;<a class="el" href="structlzma__stream.html#a4eb2f3e87e32cc4bea613898b0bd353f">lzma_stream</a></li>
<li>avail_in&#160;:&#160;<a class="el" href="structlzma__stream.html#abb680ecea31910cbda1d7a6ad4f191c0">lzma_stream</a></li>
<li>avail_out&#160;:&#160;<a class="el" href="structlzma__stream.html#a5ff28ea4e39148723c19f59811627904">lzma_stream</a></li>
</ul>


<h3><a id="index_b" name="index_b"></a>- b -</h3><ul>
<li>backward_size&#160;:&#160;<a class="el" href="structlzma__stream__flags.html#aaa65ed7a55a098f829f04dba25d0f212">lzma_stream_flags</a></li>
<li>block_count&#160;:&#160;<a class="el" href="structlzma__index__iter.html#abc6ee9be23e54f31aed07382c8caaf7c">lzma_index_iter</a></li>
<li>block_size&#160;:&#160;<a class="el" href="structlzma__mt.html#a20cdc7865266ccb88da36a6e68f84d15">lzma_mt</a></li>
</ul>


<h3><a id="index_c" name="index_c"></a>- c -</h3><ul>
<li>check&#160;:&#160;<a class="el" href="structlzma__block.html#a80cd9d3025991db4a476ce7588f853e6">lzma_block</a>, <a class="el" href="structlzma__mt.html#ae38846e8aca5b20d2a86a2364283b730">lzma_mt</a>, <a class="el" href="structlzma__stream__flags.html#ab1052ea7047c8d67f127f33278166647">lzma_stream_flags</a></li>
<li>compressed_file_offset&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a26436e75d4c2b5dd8d1de24140d8003e">lzma_index_iter</a></li>
<li>compressed_offset&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a426705df8dde4b094a42f91ea20a46ac">lzma_index_iter</a></li>
<li>compressed_size&#160;:&#160;<a class="el" href="structlzma__block.html#a8383d489c9ffea8af390669a105c74e5">lzma_block</a>, <a class="el" href="structlzma__index__iter.html#a6e73b1f37e3fcf1e9491e4a53b2c52c7">lzma_index_iter</a></li>
<li>compressed_stream_offset&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a578bba553c43dc59a5e4032d4f6c89a3">lzma_index_iter</a></li>
</ul>


<h3><a id="index_d" name="index_d"></a>- d -</h3><ul>
<li>depth&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#a4226f686e8c9f6288595fe23d0e15713">lzma_options_lzma</a></li>
<li>dict_size&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#aeb3f86002405a1191af86def46fca5ad">lzma_options_lzma</a></li>
<li>dist&#160;:&#160;<a class="el" href="structlzma__options__delta.html#a31b4b0b5a2462cb9433c2663b8a62790">lzma_options_delta</a></li>
</ul>


<h3><a id="index_e" name="index_e"></a>- e -</h3><ul>
<li>ext_flags&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#ade251d13ef46bcacb4e052b83693878c">lzma_options_lzma</a></li>
<li>ext_size_high&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#ae5b3c2375c43ddfacf093980385fb9e3">lzma_options_lzma</a></li>
<li>ext_size_low&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#a971da8385dcebd01e60235afb3b717f9">lzma_options_lzma</a></li>
</ul>


<h3><a id="index_f" name="index_f"></a>- f -</h3><ul>
<li>filters&#160;:&#160;<a class="el" href="structlzma__block.html#a5900e517e6e0a473a3184074ae7defd1">lzma_block</a>, <a class="el" href="structlzma__mt.html#ad43a62ef2178c76405e5be0ece7a98b4">lzma_mt</a></li>
<li>flags&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a223a046bcf09077a6e720967682deeae">lzma_index_iter</a>, <a class="el" href="structlzma__mt.html#a1c2fe028f547bf58b48b5199557d9a9f">lzma_mt</a></li>
<li>free&#160;:&#160;<a class="el" href="structlzma__allocator.html#a3726deffd08393934263c04660208009">lzma_allocator</a></li>
</ul>


<h3><a id="index_h" name="index_h"></a>- h -</h3><ul>
<li>header_size&#160;:&#160;<a class="el" href="structlzma__block.html#a6689c4f7524b2c05772a2d6151138610">lzma_block</a></li>
</ul>


<h3><a id="index_i" name="index_i"></a>- i -</h3><ul>
<li>id&#160;:&#160;<a class="el" href="structlzma__filter.html#aef1d9709759f39e61db77547b2326929">lzma_filter</a></li>
<li>ignore_check&#160;:&#160;<a class="el" href="structlzma__block.html#a516ac9cc63bc1a4fadd9fbfc189a206b">lzma_block</a></li>
<li>internal&#160;:&#160;<a class="el" href="structlzma__stream.html#a209da54c2fb5dea40ad011c8408300d0">lzma_stream</a></li>
</ul>


<h3><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>lc&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#a95f6188e5b5f05c50ec463a315df3585">lzma_options_lzma</a></li>
<li>lp&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#a0fe9c54e808fce3090b6994d95fe41fe">lzma_options_lzma</a></li>
</ul>


<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>memlimit_stop&#160;:&#160;<a class="el" href="structlzma__mt.html#ab5e0b530d4c572c7a2361aabbad656aa">lzma_mt</a></li>
<li>memlimit_threading&#160;:&#160;<a class="el" href="structlzma__mt.html#a5a7fb0c7c2db350e09e77477bc3c9509">lzma_mt</a></li>
<li>mf&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#aa99612cd52259093007f33513882dcd0">lzma_options_lzma</a></li>
<li>mode&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#a1d711df9bda046fd3899abf21fa250d5">lzma_options_lzma</a></li>
</ul>


<h3><a id="index_n" name="index_n"></a>- n -</h3><ul>
<li>next_in&#160;:&#160;<a class="el" href="structlzma__stream.html#a72fdc738c793f07a5c29715aa57802cf">lzma_stream</a></li>
<li>next_out&#160;:&#160;<a class="el" href="structlzma__stream.html#a14ee64ed636ddcb775edf87e2b9f42ec">lzma_stream</a></li>
<li>nice_len&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#a0352ea7f8b6a43b745a44f6cb4e2d263">lzma_options_lzma</a></li>
<li>number&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a115a307dbc778a9de296376dc39c7b23">lzma_index_iter</a></li>
<li>number_in_file&#160;:&#160;<a class="el" href="structlzma__index__iter.html#abe5333de53562189012d5ed084c0ef98">lzma_index_iter</a></li>
<li>number_in_stream&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a35a752d344ff5d35d2a858a20bd6e5e8">lzma_index_iter</a></li>
</ul>


<h3><a id="index_o" name="index_o"></a>- o -</h3><ul>
<li>opaque&#160;:&#160;<a class="el" href="structlzma__allocator.html#aab293a5007a93299cc97ee8b5fb81268">lzma_allocator</a></li>
<li>options&#160;:&#160;<a class="el" href="structlzma__filter.html#a10dfbaa1601793657d12320bef933ee6">lzma_filter</a></li>
</ul>


<h3><a id="index_p" name="index_p"></a>- p -</h3><ul>
<li>padding&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a48cfc856f283fe00b0df37402e012818">lzma_index_iter</a></li>
<li>pb&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#acae107b3d3e9d0d4fe16103be22f4408">lzma_options_lzma</a></li>
<li>preset&#160;:&#160;<a class="el" href="structlzma__mt.html#ab3883b5644752cdd15f01387d58dd050">lzma_mt</a></li>
<li>preset_dict&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#a16a58c1ee3ec18c820d5cb03dde3739a">lzma_options_lzma</a></li>
<li>preset_dict_size&#160;:&#160;<a class="el" href="structlzma__options__lzma.html#a655ad4cce9e4dac9cf2a5c8daaa629e0">lzma_options_lzma</a></li>
</ul>


<h3><a id="index_r" name="index_r"></a>- r -</h3><ul>
<li>raw_check&#160;:&#160;<a class="el" href="structlzma__block.html#a25e9bf1bb1699017694b18ca24f965d2">lzma_block</a></li>
</ul>


<h3><a id="index_s" name="index_s"></a>- s -</h3><ul>
<li>seek_pos&#160;:&#160;<a class="el" href="structlzma__stream.html#af7c43a61f3dfeb0b9c8487b7f275054e">lzma_stream</a></li>
<li>start_offset&#160;:&#160;<a class="el" href="structlzma__options__bcj.html#a3f5a3c62cd82ce89433684f12ed096ac">lzma_options_bcj</a></li>
</ul>


<h3><a id="index_t" name="index_t"></a>- t -</h3><ul>
<li>threads&#160;:&#160;<a class="el" href="structlzma__mt.html#a881761f858dbda33c697e74acde0be70">lzma_mt</a></li>
<li>timeout&#160;:&#160;<a class="el" href="structlzma__mt.html#a298992bf7d2154d8dd814560219d10c2">lzma_mt</a></li>
<li>total_in&#160;:&#160;<a class="el" href="structlzma__stream.html#a1a411e1755d6185756caefabc3932c7b">lzma_stream</a></li>
<li>total_out&#160;:&#160;<a class="el" href="structlzma__stream.html#a80d703ffdfd7661e344fe7b61ff737fa">lzma_stream</a></li>
<li>total_size&#160;:&#160;<a class="el" href="structlzma__index__iter.html#ae164ca3d7492dcf5883769c38baac30e">lzma_index_iter</a></li>
<li>type&#160;:&#160;<a class="el" href="structlzma__options__delta.html#af3f1ece7f8c472f4a794953b414c7cd7">lzma_options_delta</a></li>
</ul>


<h3><a id="index_u" name="index_u"></a>- u -</h3><ul>
<li>uncompressed_file_offset&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a2f3ecf341b5dc043e9673759b8ff47b9">lzma_index_iter</a></li>
<li>uncompressed_offset&#160;:&#160;<a class="el" href="structlzma__index__iter.html#abd374b748b4a42e122b90841709609bc">lzma_index_iter</a></li>
<li>uncompressed_size&#160;:&#160;<a class="el" href="structlzma__block.html#a17362d38d1946dd16a9686557ec19a94">lzma_block</a>, <a class="el" href="structlzma__index__iter.html#aafc48408ed40060a84ecd66bae5e1b23">lzma_index_iter</a></li>
<li>uncompressed_stream_offset&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a0fc4959fab08e1a6a4902c728c735a99">lzma_index_iter</a></li>
<li>unpadded_size&#160;:&#160;<a class="el" href="structlzma__index__iter.html#a9f4e405b9884be08e3a35bc06e3e15df">lzma_index_iter</a></li>
</ul>


<h3><a id="index_v" name="index_v"></a>- v -</h3><ul>
<li>version&#160;:&#160;<a class="el" href="structlzma__block.html#ac3936a5b0ec3f9b8f9c7ad68e7d149a5">lzma_block</a>, <a class="el" href="structlzma__stream__flags.html#a61e9151869d5b77c868aaa4958e74d10">lzma_stream_flags</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
