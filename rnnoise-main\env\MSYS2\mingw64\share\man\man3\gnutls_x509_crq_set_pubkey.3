.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_set_pubkey" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_set_pubkey \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_x509_crq_set_pubkey(gnutls_x509_crq_t " crq ", gnutls_pubkey_t " key ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "gnutls_pubkey_t key" 12
holds a public key
.SH "DESCRIPTION"
This function will set the public parameters from the given public
key to the request. The  \fIkey\fP can be deallocated after that.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
