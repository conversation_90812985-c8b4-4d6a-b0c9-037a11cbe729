set(CMAKE_C_COMPILER "@CMAKE_C_COMPILER@")
set(CMAKE_C_COMPILER_ARG1 "@CMAKE_C_COMPILER_ARG1@")
set(CMAKE_C_COMPILER_ID "@CMAKE_C_COMPILER_ID@")
set(CMAKE_C_COMPILER_VERSION "@CMAKE_C_COMPILER_VERSION@")
set(CMAKE_C_COMPILER_VERSION_INTERNAL "@CMAKE_C_COMPILER_VERSION_INTERNAL@")
set(CMAKE_C_COMPILER_WRAPPER "@CMAKE_C_COMPILER_WRAPPER@")
set(CMAKE_C_STANDARD_COMPUTED_DEFAULT "@CMAKE_C_STANDARD_COMPUTED_DEFAULT@")
set(CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT "@CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT@")
set(CMAKE_C_STANDARD_LATEST "@CMAKE_C_STANDARD_LATEST@")
set(CMAKE_C_COMPILE_FEATURES "@CMAKE_C_COMPILE_FEATURES@")
set(CMAKE_C90_COMPILE_FEATURES "@CMAKE_C90_COMPILE_FEATURES@")
set(CMAKE_C99_COMPILE_FEATURES "@CMAKE_C99_COMPILE_FEATURES@")
set(CMAKE_C11_COMPILE_FEATURES "@CMAKE_C11_COMPILE_FEATURES@")
set(CMAKE_C17_COMPILE_FEATURES "@CMAKE_C17_COMPILE_FEATURES@")
set(CMAKE_C23_COMPILE_FEATURES "@CMAKE_C23_COMPILE_FEATURES@")

set(CMAKE_C_PLATFORM_ID "@CMAKE_C_PLATFORM_ID@")
set(CMAKE_C_SIMULATE_ID "@CMAKE_C_SIMULATE_ID@")
set(CMAKE_C_COMPILER_FRONTEND_VARIANT "@CMAKE_C_COMPILER_FRONTEND_VARIANT@")
set(CMAKE_C_COMPILER_APPLE_SYSROOT "@CMAKE_C_COMPILER_APPLE_SYSROOT@")
set(CMAKE_C_SIMULATE_VERSION "@CMAKE_C_SIMULATE_VERSION@")
@_SET_CMAKE_C_COMPILER_ARCHITECTURE_ID@
@_SET_CMAKE_C_COMPILER_SYSROOT@
@SET_MSVC_C_ARCHITECTURE_ID@
@SET_CMAKE_XCODE_ARCHS@
set(CMAKE_AR "@CMAKE_AR@")
set(CMAKE_C_COMPILER_AR "@CMAKE_C_COMPILER_AR@")
set(CMAKE_RANLIB "@CMAKE_RANLIB@")
set(CMAKE_C_COMPILER_RANLIB "@CMAKE_C_COMPILER_RANLIB@")
set(CMAKE_LINKER "@CMAKE_LINKER@")
set(CMAKE_LINKER_LINK "@CMAKE_LINKER_LINK@")
set(CMAKE_LINKER_LLD "@CMAKE_LINKER_LLD@")
set(CMAKE_C_COMPILER_LINKER "@CMAKE_C_COMPILER_LINKER@")
set(CMAKE_C_COMPILER_LINKER_ID "@CMAKE_C_COMPILER_LINKER_ID@")
set(CMAKE_C_COMPILER_LINKER_VERSION @CMAKE_C_COMPILER_LINKER_VERSION@)
set(CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT @CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT@)
set(CMAKE_MT "@CMAKE_MT@")
set(CMAKE_TAPI "@CMAKE_TAPI@")
set(CMAKE_COMPILER_IS_GNUCC @CMAKE_COMPILER_IS_GNUCC@)
set(CMAKE_C_COMPILER_LOADED 1)
set(CMAKE_C_COMPILER_WORKS @CMAKE_C_COMPILER_WORKS@)
set(CMAKE_C_ABI_COMPILED @CMAKE_C_ABI_COMPILED@)

set(CMAKE_C_COMPILER_ENV_VAR "CC")

set(CMAKE_C_COMPILER_ID_RUN 1)
set(CMAKE_C_SOURCE_FILE_EXTENSIONS c;m)
set(CMAKE_C_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_C_LINKER_PREFERENCE 10)
set(CMAKE_C_LINKER_DEPFILE_SUPPORTED @CMAKE_C_LINKER_DEPFILE_SUPPORTED@)
set(CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED @CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED@)
set(CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED @CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED@)

# Save compiler ABI information.
set(CMAKE_C_SIZEOF_DATA_PTR "@CMAKE_C_SIZEOF_DATA_PTR@")
set(CMAKE_C_COMPILER_ABI "@CMAKE_C_COMPILER_ABI@")
set(CMAKE_C_BYTE_ORDER "@CMAKE_C_BYTE_ORDER@")
set(CMAKE_C_LIBRARY_ARCHITECTURE "@CMAKE_C_LIBRARY_ARCHITECTURE@")

if(CMAKE_C_SIZEOF_DATA_PTR)
  set(CMAKE_SIZEOF_VOID_P "${CMAKE_C_SIZEOF_DATA_PTR}")
endif()

if(CMAKE_C_COMPILER_ABI)
  set(CMAKE_INTERNAL_PLATFORM_ABI "${CMAKE_C_COMPILER_ABI}")
endif()

if(CMAKE_C_LIBRARY_ARCHITECTURE)
  set(CMAKE_LIBRARY_ARCHITECTURE "@CMAKE_C_LIBRARY_ARCHITECTURE@")
endif()

set(CMAKE_C_CL_SHOWINCLUDES_PREFIX "@CMAKE_C_CL_SHOWINCLUDES_PREFIX@")
if(CMAKE_C_CL_SHOWINCLUDES_PREFIX)
  set(CMAKE_CL_SHOWINCLUDES_PREFIX "${CMAKE_C_CL_SHOWINCLUDES_PREFIX}")
endif()

@CMAKE_C_COMPILER_CUSTOM_CODE@
@CMAKE_C_SYSROOT_FLAG_CODE@
@CMAKE_C_OSX_DEPLOYMENT_TARGET_FLAG_CODE@

set(CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES "@CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES@")
set(CMAKE_C_IMPLICIT_LINK_LIBRARIES "@CMAKE_C_IMPLICIT_LINK_LIBRARIES@")
set(CMAKE_C_IMPLICIT_LINK_DIRECTORIES "@CMAKE_C_IMPLICIT_LINK_DIRECTORIES@")
set(CMAKE_C_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES "@CMAKE_C_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES@")
