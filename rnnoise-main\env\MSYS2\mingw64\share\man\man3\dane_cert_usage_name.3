.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_cert_usage_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_cert_usage_name \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "const char * dane_cert_usage_name(dane_cert_usage_t " usage ");"
.SH ARGUMENTS
.IP "dane_cert_usage_t usage" 12
is a DANE certificate usage
.SH "DESCRIPTION"
Convert a \fBdane_cert_usage_t\fP value to a string.
.SH "RETURNS"
a string that contains the name of the specified
type, or \fBNULL\fP.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
