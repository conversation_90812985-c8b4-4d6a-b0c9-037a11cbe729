.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dh_get_pubkey" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dh_get_pubkey \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_dh_get_pubkey(gnutls_session_t " session ", gnutls_datum_t * " raw_key ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.IP "gnutls_datum_t * raw_key" 12
will hold the public key.
.SH "DESCRIPTION"
This function will return the peer's public key used in the last
Di<PERSON>ie\-<PERSON><PERSON> key exchange.  This function should be used for both
anonymous and ephemeral Diffie\-Hellman.  The output parameters must
be freed with \fBgnutls_free()\fP.

Note, that public key is exported as non\-negative
integer and may include a leading zero byte.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
