%NAME%
mingw-w64-x86_64-cmake

%VERSION%
4.0.3-1

%BASE%
mingw-w64-cmake

%DESC%
A cross-platform open-source make system (mingw-w64)

%URL%
https://www.cmake.org/

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/54197e6a/15843812793)

%SIZE%
51005769

%LICENSE%
spdx:MIT

%VALIDATION%
sha256
pgp

%DEPENDS%
mingw-w64-x86_64-cc-libs
mingw-w64-x86_64-cppdap
mingw-w64-x86_64-curl
mingw-w64-x86_64-expat
mingw-w64-x86_64-jsoncpp
mingw-w64-x86_64-libarchive
mingw-w64-x86_64-libuv
mingw-w64-x86_64-ninja
mingw-w64-x86_64-pkgconf
mingw-w64-x86_64-rhash
mingw-w64-x86_64-zlib

%OPTDEPENDS%
mingw-w64-x86_64-emacs: for cmake emacs mode

%XDATA%
pkgtype=split

