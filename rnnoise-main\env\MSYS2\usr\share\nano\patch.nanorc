## Syntax highlighting for patch and diff files.

## Original author:  <PERSON>
## License:  GPL version 3 or newer

syntax patch "\.(patch|diff|debdiff|rej)$"
magic "diff output"
# There is no official support for comments in patch files.
comment ""

# Added lines.
color brightgreen "^\+.*"
# Show trailing whitespace only on added lines.
color ,green "[[:space:]]+$"
# Context lines.
color brightblue "^ .*"
# Deleted lines.
color brightred "^-.*"

# Header lines.
color magenta "^(Index:|diff)[[:blank:]].*"
# File names and dates.
color red "^---.*"
color green "^\+\+\+.*"
# Line numbers.
color brightyellow "^@@.*"

# Statistics.
color cyan start="^---$" end="^$"
