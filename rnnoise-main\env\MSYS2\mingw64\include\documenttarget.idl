cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file DISCLAIMER within this package.")
cpp_quote(" */")

import "oaidl.idl";
import "ocidl.idl";

cpp_quote("")
cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("DEFINE_GUID (ID_DOCUMENTPACKAGETARGET_MSXPS, 0x9cae40a8, 0xded1, 0x41c9, 0xa9, 0xfd, 0xd7, 0x35, 0xef, 0x33, 0xae, 0xda);")
cpp_quote("DEFINE_GUID (ID_DOCUMENTPACKAGETARGET_OPENXPS, 0x0056bb72, 0x8c9c, 0x4612, 0xbd, 0x0f, 0x93, 0x01, 0x2a, 0x87, 0x09, 0x9d);")
cpp_quote("")
cpp_quote("#if NTDDI_VERSION >= 0x06010000")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (1b8efec4-3019-4c27-964e-************)]
interface IPrintDocumentPackageTarget : IUnknown {
  HRESULT GetPackageTargetTypes ([out] UINT32 *targetCount,[out, size_is (,*targetCount)] GUID **targetTypes);
  HRESULT GetPackageTarget ([in] REFGUID guidTargetType,[in] REFIID riid,[out, iid_is (riid)] void **ppvTarget);
  HRESULT Cancel ();
};
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
typedef [v1_enum] enum PrintDocumentPackageCompletion {
  PrintDocumentPackageCompletion_InProgress = 0,
  PrintDocumentPackageCompletion_Completed,
  PrintDocumentPackageCompletion_Canceled,
  PrintDocumentPackageCompletion_Failed
} PrintDocumentPackageCompletion;

typedef struct {
  UINT32 JobId;
  INT32 CurrentDocument;
  INT32 CurrentPage;
  INT32 CurrentPageTotal;
  PrintDocumentPackageCompletion Completion;
  HRESULT PackageStatus;
} PrintDocumentPackageStatus;

[object, uuid (ed90c8ad-5c34-4d05-a1ec-0e8a9b3ad7af), dual, nonextensible]
interface IPrintDocumentPackageStatusEvent : IDispatch {
  [id (1)] HRESULT PackageStatusUpdated ([in] PrintDocumentPackageStatus *packageStatus);
};

[object, uuid (d2959bf7-b31b-4a3d-9600-712eb1335ba4)]
interface IPrintDocumentPackageTargetFactory : IUnknown {
  HRESULT CreateDocumentPackageTargetForPrintJob ([in, string] LPCWSTR printerName,[in, string] LPCWSTR jobName,[in] IStream *jobOutputStream,[in] IStream *jobPrintTicketStream,[out] IPrintDocumentPackageTarget **docPackageTarget);
};
cpp_quote("#endif")

[uuid (410d76f7-8bb5-4a7d-9d37-9c71b1b14d14), version (1.0)]
library PrintDocumentTargetLib {
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
  interface IPrintDocumentPackageTarget;
cpp_quote("#endif")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
  interface IPrintDocumentPackageStatusEvent;
  interface IPrintDocumentPackageTargetFactory;

  [uuid (4842669e-9947-46ea-8ba2-d8cce432c2ca), noncreatable]
  coclass PrintDocumentPackageTarget {
    [default] interface IPrintDocumentPackageTarget;
    [source] dispinterface IPrintDocumentPackageStatusEvent;
  };

  [uuid (348ef17d-6c81-4982-92b4-ee188a43867a)]
  coclass PrintDocumentPackageTargetFactory {
    [default] interface IPrintDocumentPackageTargetFactory;
  };
  cpp_quote("#endif")
};

cpp_quote("#endif")
