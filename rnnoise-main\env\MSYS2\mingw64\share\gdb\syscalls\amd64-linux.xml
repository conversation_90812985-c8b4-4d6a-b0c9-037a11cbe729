<?xml version="1.0"?>
<!DOCTYPE syscalls_info SYSTEM "gdb-syscalls.dtd">
<!-- Copyright (C) 2009-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->
<!-- This file was generated using the following file:

     arch/x86/entry/syscalls/syscall_64.tbl

     The file mentioned above belongs to the Linux Kernel.  -->
<syscalls_info>
  <syscall name="read" number="0" groups="descriptor"/>
  <syscall name="write" number="1" groups="descriptor"/>
  <syscall name="open" number="2" groups="descriptor,file"/>
  <syscall name="close" number="3" groups="descriptor"/>
  <syscall name="stat" number="4" groups="file"/>
  <syscall name="fstat" number="5" groups="descriptor"/>
  <syscall name="lstat" number="6" groups="file"/>
  <syscall name="poll" number="7" groups="descriptor"/>
  <syscall name="lseek" number="8" groups="descriptor"/>
  <syscall name="mmap" number="9" groups="descriptor,memory"/>
  <syscall name="mprotect" number="10" groups="memory"/>
  <syscall name="munmap" number="11" groups="memory"/>
  <syscall name="brk" number="12" groups="memory"/>
  <syscall name="rt_sigaction" number="13" groups="signal"/>
  <syscall name="rt_sigprocmask" number="14" groups="signal"/>
  <syscall name="rt_sigreturn" number="15" groups="signal"/>
  <syscall name="ioctl" number="16" groups="descriptor"/>
  <syscall name="pread64" number="17" groups="descriptor"/>
  <syscall name="pwrite64" number="18" groups="descriptor"/>
  <syscall name="readv" number="19" groups="descriptor"/>
  <syscall name="writev" number="20" groups="descriptor"/>
  <syscall name="access" number="21" groups="file"/>
  <syscall name="pipe" number="22" groups="descriptor"/>
  <syscall name="select" number="23" groups="descriptor"/>
  <syscall name="sched_yield" number="24"/>
  <syscall name="mremap" number="25" groups="memory"/>
  <syscall name="msync" number="26" groups="memory"/>
  <syscall name="mincore" number="27" groups="memory"/>
  <syscall name="madvise" number="28" groups="memory"/>
  <syscall name="shmget" number="29" groups="ipc"/>
  <syscall name="shmat" number="30" groups="ipc,memory"/>
  <syscall name="shmctl" number="31" groups="ipc"/>
  <syscall name="dup" number="32" groups="descriptor"/>
  <syscall name="dup2" number="33" groups="descriptor"/>
  <syscall name="pause" number="34" groups="signal"/>
  <syscall name="nanosleep" number="35"/>
  <syscall name="getitimer" number="36"/>
  <syscall name="alarm" number="37"/>
  <syscall name="setitimer" number="38"/>
  <syscall name="getpid" number="39"/>
  <syscall name="sendfile" number="40" groups="descriptor,network"/>
  <syscall name="socket" number="41" groups="network"/>
  <syscall name="connect" number="42" groups="network"/>
  <syscall name="accept" number="43" groups="network"/>
  <syscall name="sendto" number="44" groups="network"/>
  <syscall name="recvfrom" number="45" groups="network"/>
  <syscall name="sendmsg" number="46" groups="network"/>
  <syscall name="recvmsg" number="47" groups="network"/>
  <syscall name="shutdown" number="48" groups="network"/>
  <syscall name="bind" number="49" groups="network"/>
  <syscall name="listen" number="50" groups="network"/>
  <syscall name="getsockname" number="51" groups="network"/>
  <syscall name="getpeername" number="52" groups="network"/>
  <syscall name="socketpair" number="53" groups="network"/>
  <syscall name="setsockopt" number="54" groups="network"/>
  <syscall name="getsockopt" number="55" groups="network"/>
  <syscall name="clone" number="56" groups="process"/>
  <syscall name="fork" number="57" groups="process"/>
  <syscall name="vfork" number="58" groups="process"/>
  <syscall name="execve" number="59" groups="file,process"/>
  <syscall name="exit" number="60" groups="process"/>
  <syscall name="wait4" number="61" groups="process"/>
  <syscall name="kill" number="62" groups="signal,process"/>
  <syscall name="uname" number="63"/>
  <syscall name="semget" number="64" groups="ipc"/>
  <syscall name="semop" number="65" groups="ipc"/>
  <syscall name="semctl" number="66" groups="ipc"/>
  <syscall name="shmdt" number="67" groups="ipc,memory"/>
  <syscall name="msgget" number="68" groups="ipc"/>
  <syscall name="msgsnd" number="69" groups="ipc"/>
  <syscall name="msgrcv" number="70" groups="ipc"/>
  <syscall name="msgctl" number="71" groups="ipc"/>
  <syscall name="fcntl" number="72" groups="descriptor"/>
  <syscall name="flock" number="73" groups="descriptor"/>
  <syscall name="fsync" number="74" groups="descriptor"/>
  <syscall name="fdatasync" number="75" groups="descriptor"/>
  <syscall name="truncate" number="76" groups="file"/>
  <syscall name="ftruncate" number="77" groups="descriptor"/>
  <syscall name="getdents" number="78" groups="descriptor"/>
  <syscall name="getcwd" number="79" groups="file"/>
  <syscall name="chdir" number="80" groups="file"/>
  <syscall name="fchdir" number="81" groups="descriptor"/>
  <syscall name="rename" number="82" groups="file"/>
  <syscall name="mkdir" number="83" groups="file"/>
  <syscall name="rmdir" number="84" groups="file"/>
  <syscall name="creat" number="85" groups="descriptor,file"/>
  <syscall name="link" number="86" groups="file"/>
  <syscall name="unlink" number="87" groups="file"/>
  <syscall name="symlink" number="88" groups="file"/>
  <syscall name="readlink" number="89" groups="file"/>
  <syscall name="chmod" number="90" groups="file"/>
  <syscall name="fchmod" number="91" groups="descriptor"/>
  <syscall name="chown" number="92" groups="file"/>
  <syscall name="fchown" number="93" groups="descriptor"/>
  <syscall name="lchown" number="94" groups="file"/>
  <syscall name="umask" number="95"/>
  <syscall name="gettimeofday" number="96"/>
  <syscall name="getrlimit" number="97"/>
  <syscall name="getrusage" number="98"/>
  <syscall name="sysinfo" number="99"/>
  <syscall name="times" number="100"/>
  <syscall name="ptrace" number="101"/>
  <syscall name="getuid" number="102"/>
  <syscall name="syslog" number="103"/>
  <syscall name="getgid" number="104"/>
  <syscall name="setuid" number="105"/>
  <syscall name="setgid" number="106"/>
  <syscall name="geteuid" number="107"/>
  <syscall name="getegid" number="108"/>
  <syscall name="setpgid" number="109"/>
  <syscall name="getppid" number="110"/>
  <syscall name="getpgrp" number="111"/>
  <syscall name="setsid" number="112"/>
  <syscall name="setreuid" number="113"/>
  <syscall name="setregid" number="114"/>
  <syscall name="getgroups" number="115"/>
  <syscall name="setgroups" number="116"/>
  <syscall name="setresuid" number="117"/>
  <syscall name="getresuid" number="118"/>
  <syscall name="setresgid" number="119"/>
  <syscall name="getresgid" number="120"/>
  <syscall name="getpgid" number="121"/>
  <syscall name="setfsuid" number="122"/>
  <syscall name="setfsgid" number="123"/>
  <syscall name="getsid" number="124"/>
  <syscall name="capget" number="125"/>
  <syscall name="capset" number="126"/>
  <syscall name="rt_sigpending" number="127" groups="signal"/>
  <syscall name="rt_sigtimedwait" number="128" groups="signal"/>
  <syscall name="rt_sigqueueinfo" number="129" groups="signal,process"/>
  <syscall name="rt_sigsuspend" number="130" groups="signal"/>
  <syscall name="sigaltstack" number="131" groups="signal"/>
  <syscall name="utime" number="132" groups="file"/>
  <syscall name="mknod" number="133" groups="file"/>
  <syscall name="uselib" number="134" groups="file"/>
  <syscall name="personality" number="135"/>
  <syscall name="ustat" number="136"/>
  <syscall name="statfs" number="137" groups="file"/>
  <syscall name="fstatfs" number="138" groups="descriptor"/>
  <syscall name="sysfs" number="139"/>
  <syscall name="getpriority" number="140"/>
  <syscall name="setpriority" number="141"/>
  <syscall name="sched_setparam" number="142"/>
  <syscall name="sched_getparam" number="143"/>
  <syscall name="sched_setscheduler" number="144"/>
  <syscall name="sched_getscheduler" number="145"/>
  <syscall name="sched_get_priority_max" number="146"/>
  <syscall name="sched_get_priority_min" number="147"/>
  <syscall name="sched_rr_get_interval" number="148"/>
  <syscall name="mlock" number="149" groups="memory"/>
  <syscall name="munlock" number="150" groups="memory"/>
  <syscall name="mlockall" number="151" groups="memory"/>
  <syscall name="munlockall" number="152" groups="memory"/>
  <syscall name="vhangup" number="153"/>
  <syscall name="modify_ldt" number="154"/>
  <syscall name="pivot_root" number="155" groups="file"/>
  <syscall name="_sysctl" number="156"/>
  <syscall name="prctl" number="157"/>
  <syscall name="arch_prctl" number="158"/>
  <syscall name="adjtimex" number="159"/>
  <syscall name="setrlimit" number="160"/>
  <syscall name="chroot" number="161" groups="file"/>
  <syscall name="sync" number="162"/>
  <syscall name="acct" number="163" groups="file"/>
  <syscall name="settimeofday" number="164"/>
  <syscall name="mount" number="165" groups="file"/>
  <syscall name="umount2" number="166" groups="file"/>
  <syscall name="swapon" number="167" groups="file"/>
  <syscall name="swapoff" number="168" groups="file"/>
  <syscall name="reboot" number="169"/>
  <syscall name="sethostname" number="170"/>
  <syscall name="setdomainname" number="171"/>
  <syscall name="iopl" number="172"/>
  <syscall name="ioperm" number="173"/>
  <syscall name="create_module" number="174"/>
  <syscall name="init_module" number="175"/>
  <syscall name="delete_module" number="176"/>
  <syscall name="get_kernel_syms" number="177"/>
  <syscall name="query_module" number="178"/>
  <syscall name="quotactl" number="179" groups="file"/>
  <syscall name="nfsservctl" number="180"/>
  <syscall name="getpmsg" number="181" groups="network"/>
  <syscall name="putpmsg" number="182" groups="network"/>
  <syscall name="afs_syscall" number="183"/>
  <syscall name="tuxcall" number="184"/>
  <syscall name="security" number="185"/>
  <syscall name="gettid" number="186"/>
  <syscall name="readahead" number="187" groups="descriptor"/>
  <syscall name="setxattr" number="188" groups="file"/>
  <syscall name="lsetxattr" number="189" groups="file"/>
  <syscall name="fsetxattr" number="190" groups="descriptor"/>
  <syscall name="getxattr" number="191" groups="file"/>
  <syscall name="lgetxattr" number="192" groups="file"/>
  <syscall name="fgetxattr" number="193" groups="descriptor"/>
  <syscall name="listxattr" number="194" groups="file"/>
  <syscall name="llistxattr" number="195" groups="file"/>
  <syscall name="flistxattr" number="196" groups="descriptor"/>
  <syscall name="removexattr" number="197" groups="file"/>
  <syscall name="lremovexattr" number="198" groups="file"/>
  <syscall name="fremovexattr" number="199" groups="descriptor"/>
  <syscall name="tkill" number="200" groups="signal,process"/>
  <syscall name="time" number="201"/>
  <syscall name="futex" number="202"/>
  <syscall name="sched_setaffinity" number="203"/>
  <syscall name="sched_getaffinity" number="204"/>
  <syscall name="set_thread_area" number="205"/>
  <syscall name="io_setup" number="206" groups="memory"/>
  <syscall name="io_destroy" number="207" groups="memory"/>
  <syscall name="io_getevents" number="208"/>
  <syscall name="io_submit" number="209"/>
  <syscall name="io_cancel" number="210"/>
  <syscall name="get_thread_area" number="211"/>
  <syscall name="lookup_dcookie" number="212"/>
  <syscall name="epoll_create" number="213" groups="descriptor"/>
  <syscall name="epoll_ctl_old" number="214"/>
  <syscall name="epoll_wait_old" number="215"/>
  <syscall name="remap_file_pages" number="216" groups="memory"/>
  <syscall name="getdents64" number="217" groups="descriptor"/>
  <syscall name="set_tid_address" number="218"/>
  <syscall name="restart_syscall" number="219"/>
  <syscall name="semtimedop" number="220" groups="ipc"/>
  <syscall name="fadvise64" number="221" groups="descriptor"/>
  <syscall name="timer_create" number="222"/>
  <syscall name="timer_settime" number="223"/>
  <syscall name="timer_gettime" number="224"/>
  <syscall name="timer_getoverrun" number="225"/>
  <syscall name="timer_delete" number="226"/>
  <syscall name="clock_settime" number="227"/>
  <syscall name="clock_gettime" number="228"/>
  <syscall name="clock_getres" number="229"/>
  <syscall name="clock_nanosleep" number="230"/>
  <syscall name="exit_group" number="231" groups="process"/>
  <syscall name="epoll_wait" number="232" groups="descriptor"/>
  <syscall name="epoll_ctl" number="233" groups="descriptor"/>
  <syscall name="tgkill" number="234" groups="signal,process"/>
  <syscall name="utimes" number="235" groups="file"/>
  <syscall name="vserver" number="236"/>
  <syscall name="mbind" number="237" groups="memory"/>
  <syscall name="set_mempolicy" number="238" groups="memory"/>
  <syscall name="get_mempolicy" number="239" groups="memory"/>
  <syscall name="mq_open" number="240" groups="descriptor"/>
  <syscall name="mq_unlink" number="241"/>
  <syscall name="mq_timedsend" number="242" groups="descriptor"/>
  <syscall name="mq_timedreceive" number="243" groups="descriptor"/>
  <syscall name="mq_notify" number="244" groups="descriptor"/>
  <syscall name="mq_getsetattr" number="245" groups="descriptor"/>
  <syscall name="kexec_load" number="246"/>
  <syscall name="waitid" number="247" groups="process"/>
  <syscall name="add_key" number="248"/>
  <syscall name="request_key" number="249"/>
  <syscall name="keyctl" number="250"/>
  <syscall name="ioprio_set" number="251"/>
  <syscall name="ioprio_get" number="252"/>
  <syscall name="inotify_init" number="253" groups="descriptor"/>
  <syscall name="inotify_add_watch" number="254" groups="descriptor,file"/>
  <syscall name="inotify_rm_watch" number="255" groups="descriptor"/>
  <syscall name="migrate_pages" number="256" groups="memory"/>
  <syscall name="openat" number="257" groups="descriptor,file"/>
  <syscall name="mkdirat" number="258" groups="descriptor,file"/>
  <syscall name="mknodat" number="259" groups="descriptor,file"/>
  <syscall name="fchownat" number="260" groups="descriptor,file"/>
  <syscall name="futimesat" number="261" groups="descriptor,file"/>
  <syscall name="newfstatat" number="262" groups="descriptor,file"/>
  <syscall name="unlinkat" number="263" groups="descriptor,file"/>
  <syscall name="renameat" number="264" groups="descriptor,file"/>
  <syscall name="linkat" number="265" groups="descriptor,file"/>
  <syscall name="symlinkat" number="266" groups="descriptor,file"/>
  <syscall name="readlinkat" number="267" groups="descriptor,file"/>
  <syscall name="fchmodat" number="268" groups="descriptor,file"/>
  <syscall name="faccessat" number="269" groups="descriptor,file"/>
  <syscall name="pselect6" number="270" groups="descriptor"/>
  <syscall name="ppoll" number="271" groups="descriptor"/>
  <syscall name="unshare" number="272"/>
  <syscall name="set_robust_list" number="273"/>
  <syscall name="get_robust_list" number="274"/>
  <syscall name="splice" number="275" groups="descriptor"/>
  <syscall name="tee" number="276" groups="descriptor"/>
  <syscall name="sync_file_range" number="277" groups="descriptor"/>
  <syscall name="vmsplice" number="278" groups="descriptor"/>
  <syscall name="move_pages" number="279" groups="memory"/>
  <syscall name="utimensat" number="280" groups="descriptor,file"/>
  <syscall name="epoll_pwait" number="281" groups="descriptor"/>
  <syscall name="signalfd" number="282" groups="descriptor,signal"/>
  <syscall name="timerfd_create" number="283" groups="descriptor"/>
  <syscall name="eventfd" number="284" groups="descriptor"/>
  <syscall name="fallocate" number="285" groups="descriptor"/>
  <syscall name="timerfd_settime" number="286" groups="descriptor"/>
  <syscall name="timerfd_gettime" number="287" groups="descriptor"/>
  <syscall name="accept4" number="288" groups="network"/>
  <syscall name="signalfd4" number="289" groups="descriptor,signal"/>
  <syscall name="eventfd2" number="290" groups="descriptor"/>
  <syscall name="epoll_create1" number="291" groups="descriptor"/>
  <syscall name="dup3" number="292" groups="descriptor"/>
  <syscall name="pipe2" number="293" groups="descriptor"/>
  <syscall name="inotify_init1" number="294" groups="descriptor"/>
  <syscall name="preadv" number="295" groups="descriptor"/>
  <syscall name="pwritev" number="296" groups="descriptor"/>
  <syscall name="rt_tgsigqueueinfo" number="297" groups="process,signal"/>
  <syscall name="perf_event_open" number="298" groups="descriptor"/>
  <syscall name="recvmmsg" number="299" groups="network"/>
  <syscall name="fanotify_init" number="300" groups="descriptor"/>
  <syscall name="fanotify_mark" number="301" groups="descriptor,file"/>
  <syscall name="prlimit64" number="302"/>
  <syscall name="name_to_handle_at" number="303" groups="descriptor,file"/>
  <syscall name="open_by_handle_at" number="304" groups="descriptor"/>
  <syscall name="clock_adjtime" number="305"/>
  <syscall name="syncfs" number="306" groups="descriptor"/>
  <syscall name="sendmmsg" number="307" groups="network"/>
  <syscall name="setns" number="308" groups="descriptor"/>
  <syscall name="getcpu" number="309"/>
  <syscall name="process_vm_readv" number="310"/>
  <syscall name="process_vm_writev" number="311"/>
  <syscall name="kcmp" number="312"/>
  <syscall name="finit_module" number="313" groups="descriptor"/>
  <syscall name="sched_setattr" number="314"/>
  <syscall name="sched_getattr" number="315"/>
  <syscall name="renameat2" number="316" groups="descriptor,file"/>
  <syscall name="seccomp" number="317"/>
  <syscall name="getrandom" number="318"/>
  <syscall name="memfd_create" number="319" groups="descriptor"/>
  <syscall name="kexec_file_load" number="320" groups="descriptor"/>
  <syscall name="bpf" number="321" groups="descriptor"/>
  <syscall name="execveat" number="322" groups="descriptor,file,process"/>
  <syscall name="userfaultfd" number="323" groups="descriptor"/>
  <syscall name="membarrier" number="324"/>
  <syscall name="mlock2" number="325" groups="memory"/>
  <syscall name="copy_file_range" number="326" groups="descriptor"/>
  <syscall name="preadv2" number="327" groups="descriptor"/>
  <syscall name="pwritev2" number="328" groups="descriptor"/>
  <syscall name="pkey_mprotect" number="329" groups="memory"/>
  <syscall name="pkey_alloc" number="330"/>
  <syscall name="pkey_free" number="331"/>
  <syscall name="statx" number="332" groups="descriptor,file"/>
  <syscall name="io_pgetevents" number="333"/>
  <syscall name="rseq" number="334"/>
  <syscall name="uretprobe" number="335"/>
  <syscall name="pidfd_send_signal" number="424" groups="descriptor,signal,process"/>
  <syscall name="io_uring_setup" number="425" groups="descriptor"/>
  <syscall name="io_uring_enter" number="426" groups="descriptor,signal"/>
  <syscall name="io_uring_register" number="427" groups="descriptor,memory"/>
  <syscall name="open_tree" number="428" groups="descriptor,file"/>
  <syscall name="move_mount" number="429" groups="descriptor,file"/>
  <syscall name="fsopen" number="430" groups="descriptor"/>
  <syscall name="fsconfig" number="431" groups="descriptor,file"/>
  <syscall name="fsmount" number="432" groups="descriptor"/>
  <syscall name="fspick" number="433" groups="descriptor,file"/>
  <syscall name="pidfd_open" number="434" groups="descriptor"/>
  <syscall name="clone3" number="435" groups="process"/>
  <syscall name="close_range" number="436"/>
  <syscall name="openat2" number="437" groups="descriptor,file"/>
  <syscall name="pidfd_getfd" number="438" groups="descriptor"/>
  <syscall name="faccessat2" number="439" groups="descriptor,file"/>
  <syscall name="process_madvise" number="440" groups="descriptor"/>
  <syscall name="epoll_pwait2" number="441" groups="descriptor"/>
  <syscall name="mount_setattr" number="442" groups="descriptor,file"/>
  <syscall name="quotactl_fd" number="443" groups="descriptor"/>
  <syscall name="landlock_create_ruleset" number="444" groups="descriptor"/>
  <syscall name="landlock_add_rule" number="445" groups="descriptor"/>
  <syscall name="landlock_restrict_self" number="446" groups="descriptor"/>
  <syscall name="memfd_secret" number="447" groups="descriptor"/>
  <syscall name="process_mrelease" number="448" groups="descriptor"/>
  <syscall name="futex_waitv" number="449"/>
  <syscall name="set_mempolicy_home_node" number="450" groups="memory"/>
  <syscall name="cachestat" number="451" groups="descriptor"/>
  <syscall name="fchmodat2" number="452" groups="descriptor,file"/>
  <syscall name="map_shadow_stack" number="453" groups="memory"/>
  <syscall name="futex_wake" number="454"/>
  <syscall name="futex_wait" number="455"/>
  <syscall name="futex_requeue" number="456"/>
  <syscall name="statmount" number="457"/>
  <syscall name="listmount" number="458"/>
  <syscall name="lsm_get_self_attr" number="459"/>
  <syscall name="lsm_set_self_attr" number="460"/>
  <syscall name="lsm_list_modules" number="461"/>
  <syscall name="mseal" number="462" groups="memory"/>
  <syscall name="setxattrat" number="463"/>
  <syscall name="getxattrat" number="464"/>
  <syscall name="listxattrat" number="465"/>
  <syscall name="removexattrat" number="466"/>
</syscalls_info>
