<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Testing and Code Coverage: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="devel.html" title="Building, Packaging, and Contributing to p11-kit">
<link rel="prev" href="devel-building-style.html" title="Coding Style">
<link rel="next" href="devel-debugging.html" title="Debugging Tips">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="devel.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="devel-building-style.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="devel-debugging.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="devel-testing"></a>Testing and Code Coverage</h2></div></div></div>
<p>As a general rule changes to p11-kit should have a tests exercising
		that change. Use the <code class="literal">make check</code> command to run all
		the tests. If you run it from a subdirectory only the tests in that
		directory will be run.</p>
<p>To check for memory errors or memory leaks, run <code class="literal">make memcheck</code>
		or <code class="literal">make leakcheck</code> respectively. This requires valgrind
		be installed.</p>
<p>Build p11-kit with the <code class="option">--enable-coverage</code> configure
		option to build code coverage support.</p>
<p>Once you've done that you can either use <code class="literal">make coverage</code>
		to build code coverage information. Alternatively (and this is usually
		easier) you can use
		<a class="ulink" href="http://stef.thewalter.net/2012/12/git-coverage-useful-code-coverage.html" target="_top">
			<code class="literal">git coverage</code></a> to easily check whether
		you've tested the lines changed by a patch.</p>
<p>A code coverage report is
			<a class="ulink" href="https://coveralls.io/github/p11-glue/p11-kit" target="_top">available online</a></p>.
	</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>