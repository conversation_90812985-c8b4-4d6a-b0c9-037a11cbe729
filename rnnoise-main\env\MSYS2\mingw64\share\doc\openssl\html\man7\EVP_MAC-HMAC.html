<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_MAC-HMAC</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
      <li><a href="#Supported-parameters">Supported parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_MAC-HMAC - The HMAC EVP_MAC implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing HMAC MACs through the <b>EVP_MAC</b> API.</p>

<p>This implementation uses EVP_MD functions to get access to the underlying digest.</p>

<h2 id="Identity">Identity</h2>

<p>This implementation is identified with this name and properties, to be used with EVP_MAC_fetch():</p>

<dl>

<dt id="HMAC-provider-default-or-provider-fips">&quot;HMAC&quot;, &quot;provider=default&quot; or &quot;provider=fips&quot;</dt>
<dd>

</dd>
</dl>

<h2 id="Supported-parameters">Supported parameters</h2>

<p>The general description of these parameters can be found in <a href="../man3/EVP_MAC.html">&quot;PARAMETERS&quot; in EVP_MAC(3)</a>.</p>

<p>The following parameters can be set with EVP_MAC_CTX_set_params():</p>

<dl>

<dt id="key-OSSL_MAC_PARAM_KEY-octet-string">&quot;key&quot; (<b>OSSL_MAC_PARAM_KEY</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the MAC key. Setting this parameter is identical to passing a <i>key</i> to <a href="../man3/EVP_MAC_init.html">EVP_MAC_init(3)</a>.</p>

</dd>
<dt id="digest-OSSL_MAC_PARAM_DIGEST-UTF8-string">&quot;digest&quot; (<b>OSSL_MAC_PARAM_DIGEST</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Sets the name of the underlying digest to be used.</p>

</dd>
<dt id="properties-OSSL_MAC_PARAM_PROPERTIES-UTF8-string">&quot;properties&quot; (<b>OSSL_MAC_PARAM_PROPERTIES</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Sets the properties to be queried when trying to fetch the underlying digest. This must be given together with the digest naming parameter (&quot;digest&quot;, or <b>OSSL_MAC_PARAM_DIGEST</b>) to be considered valid.</p>

</dd>
<dt id="digest-noinit-OSSL_MAC_PARAM_DIGEST_NOINIT-integer">&quot;digest-noinit&quot; (<b>OSSL_MAC_PARAM_DIGEST_NOINIT</b>) &lt;integer&gt;</dt>
<dd>

<p>A flag to set the MAC digest to not initialise the implementation specific data. The value 0 or 1 is expected. This option is deprecated and will be removed in a future release. It may be set but is currently ignored</p>

</dd>
<dt id="digest-oneshot-OSSL_MAC_PARAM_DIGEST_ONESHOT-integer">&quot;digest-oneshot&quot; (<b>OSSL_MAC_PARAM_DIGEST_ONESHOT</b>) &lt;integer&gt;</dt>
<dd>

<p>A flag to set the MAC digest to be a one-shot operation. The value 0 or 1 is expected. This option is deprecated and will be removed in a future release. It may be set but is currently ignored.</p>

</dd>
<dt id="tls-data-size-OSSL_MAC_PARAM_TLS_DATA_SIZE-unsigned-integer">&quot;tls-data-size&quot; (<b>OSSL_MAC_PARAM_TLS_DATA_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

</dd>
<dt id="key-check-OSSL_MAC_PARAM_FIPS_KEY_CHECK-integer">&quot;key-check&quot; (<b>OSSL_MAC_PARAM_FIPS_KEY_CHECK</b>) &lt;integer&gt;</dt>
<dd>

<p>See <a href="../man7/provider-mac.html">&quot;Mac Parameters&quot; in provider-mac(7)</a>.</p>

</dd>
</dl>

<p>The following parameters can be retrieved with EVP_MAC_CTX_get_params():</p>

<dl>

<dt id="size-OSSL_MAC_PARAM_SIZE-unsigned-integer">&quot;size&quot; (<b>OSSL_MAC_PARAM_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>The &quot;size&quot; parameter can also be retrieved with EVP_MAC_CTX_get_mac_size(). The length of the &quot;size&quot; parameter is equal to that of an <b>unsigned int</b>.</p>

</dd>
<dt id="block-size-OSSL_MAC_PARAM_BLOCK_SIZE-unsigned-integer">&quot;block-size&quot; (<b>OSSL_MAC_PARAM_BLOCK_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Gets the MAC block size. The &quot;block-size&quot; parameter can also be retrieved with EVP_MAC_CTX_get_block_size().</p>

</dd>
<dt id="fips-indicator-OSSL_KDF_PARAM_FIPS_APPROVED_INDICATOR-integer">&quot;fips-indicator&quot; (<b>OSSL_KDF_PARAM_FIPS_APPROVED_INDICATOR</b>) &lt;integer&gt;</dt>
<dd>

<p>See <a href="../man7/provider-mac.html">&quot;Mac Parameters&quot; in provider-mac(7)</a>.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_MAC_CTX_get_params.html">EVP_MAC_CTX_get_params(3)</a>, <a href="../man3/EVP_MAC_CTX_set_params.html">EVP_MAC_CTX_set_params(3)</a>, <a href="../man3/EVP_MAC.html">&quot;PARAMETERS&quot; in EVP_MAC(3)</a>, <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a>, <a href="../man3/HMAC.html">HMAC(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


