version: ?.??

extended: 1
rounding: half_even

-- testing folddown and clamping
maxexponent: 9
minexponent: -9
precision: 6
clamp: 1
extr0000 apply 1E+11 -> Infinity Overflow Inexact Rounded
extr0001 apply 1E+10 -> Infinity Overflow Inexact Rounded
extr0002 apply 1E+9 -> 1.00000E+9 Clamped
extr0003 apply 1E+8 -> 1.0000E+8 Clamped
extr0004 apply 1E+7 -> 1.000E+7 Clamped
extr0005 apply 1E+6 -> 1.00E+6 Clamped
extr0006 apply 1E+5 -> 1.0E+5 Clamped
extr0007 apply 1E+4 -> 1E+4
extr0008 apply 1E+3 -> 1E+3
extr0009 apply 1E+2 -> 1E+2
extr0010 apply 1E+1 -> 1E+1
extr0011 apply 1 -> 1
extr0012 apply 1E-1 -> 0.1
extr0013 apply 1E-2 -> 0.01
extr0014 apply 1E-3 -> 0.001
extr0015 apply 1E-4 -> 0.0001
extr0016 apply 1E-5 -> 0.00001
extr0017 apply 1E-6 -> 0.000001
extr0018 apply 1E-7 -> 1E-7
extr0019 apply 1E-8 -> 1E-8
extr0020 apply 1E-9 -> 1E-9
extr0021 apply 1E-10 -> 1E-10 Subnormal
extr0022 apply 1E-11 -> 1E-11 Subnormal
extr0023 apply 1E-12 -> 1E-12 Subnormal
extr0024 apply 1E-13 -> 1E-13 Subnormal
extr0025 apply 1E-14 -> 1E-14 Subnormal
extr0026 apply 1E-15 -> 0E-14 Inexact Rounded Subnormal Underflow Clamped
extr0027 apply 1E-16 -> 0E-14 Inexact Rounded Subnormal Underflow Clamped
clamp: 0

-- large precision, small minimum and maximum exponent; in this case
-- it's possible that folddown is required on a subnormal result
maxexponent: 9
minexponent: -9
precision: 24
clamp: 1
extr0100 apply 1E+11 -> Infinity Overflow Inexact Rounded
extr0101 apply 1E+10 -> Infinity Overflow Inexact Rounded
extr0102 apply 1E+9 -> 1000000000.00000000000000 Clamped
extr0103 apply 1E+8 -> 100000000.00000000000000 Clamped
extr0104 apply 1E+7 -> 10000000.00000000000000 Clamped
extr0105 apply 1E+6 -> 1000000.00000000000000 Clamped
extr0106 apply 1E+5 -> 100000.00000000000000 Clamped
extr0107 apply 1E+4 -> 10000.00000000000000 Clamped
extr0108 apply 1E+3 -> 1000.00000000000000 Clamped
extr0109 apply 1E+2 -> 100.00000000000000 Clamped
extr0110 apply 1E+1 -> 10.00000000000000 Clamped
extr0111 apply 1 ->    1.00000000000000 Clamped
extr0112 apply 1E-1 -> 0.10000000000000 Clamped
extr0113 apply 1E-2 -> 0.01000000000000 Clamped
extr0114 apply 1E-3 -> 0.00100000000000 Clamped
extr0115 apply 1E-4 -> 0.00010000000000 Clamped
extr0116 apply 1E-5 -> 0.00001000000000 Clamped
extr0117 apply 1E-6 -> 0.00000100000000 Clamped
extr0118 apply 1E-7 -> 1.0000000E-7 Clamped
extr0119 apply 1E-8 -> 1.000000E-8 Clamped
extr0120 apply 1E-9 -> 1.00000E-9 Clamped
extr0121 apply 1E-10 -> 1.0000E-10 Subnormal Clamped
extr0122 apply 1E-11 -> 1.000E-11 Subnormal Clamped
extr0123 apply 1E-12 -> 1.00E-12 Subnormal Clamped
extr0124 apply 1E-13 -> 1.0E-13 Subnormal Clamped
extr0125 apply 1E-14 -> 1E-14 Subnormal
extr0126 apply 1E-15 -> 1E-15 Subnormal
extr0127 apply 1E-16 -> 1E-16 Subnormal
extr0128 apply 1E-17 -> 1E-17 Subnormal
extr0129 apply 1E-18 -> 1E-18 Subnormal
extr0130 apply 1E-19 -> 1E-19 Subnormal
extr0131 apply 1E-20 -> 1E-20 Subnormal
extr0132 apply 1E-21 -> 1E-21 Subnormal
extr0133 apply 1E-22 -> 1E-22 Subnormal
extr0134 apply 1E-23 -> 1E-23 Subnormal
extr0135 apply 1E-24 -> 1E-24 Subnormal
extr0136 apply 1E-25 -> 1E-25 Subnormal
extr0137 apply 1E-26 -> 1E-26 Subnormal
extr0138 apply 1E-27 -> 1E-27 Subnormal
extr0139 apply 1E-28 -> 1E-28 Subnormal
extr0140 apply 1E-29 -> 1E-29 Subnormal
extr0141 apply 1E-30 -> 1E-30 Subnormal
extr0142 apply 1E-31 -> 1E-31 Subnormal
extr0143 apply 1E-32 -> 1E-32 Subnormal
extr0144 apply 1E-33 -> 0E-32 Inexact Rounded Subnormal Underflow Clamped
extr0145 apply 1E-34 -> 0E-32 Inexact Rounded Subnormal Underflow Clamped
clamp: 0

-- some buggy addition cases from Python 2.5.x
maxexponent: 999
minexponent: -999
precision: 6
extr1000 add 0E+1000 0E+2000 -> 0E+999 Clamped
extr1001 add 0E+1004 0E+1001 -> 0E+999 Clamped
clamp: 1
extr1002 add 0E+1000 0E+1000 -> 0E+994 Clamped
clamp: 0
extr1003 add 0E+1000 0E-1005 -> 0E-1004 Clamped
extr1004 add 0E-1006 0 -> 0E-1004 Clamped
extr1005 add 1E+1000 -1E+1000 -> 0E+999 Clamped
extr1006 add -3.1E+1004 3.1E+1004 -> 0E+999 Clamped
clamp: 1
extr1007 add 1E+998 -1E+998 -> 0E+994 Clamped
clamp: 0
extr1008 add 2E-1005 -2E-1005 -> 0E-1004 Clamped
extr1009 add -3.1E-1005 3.1E-1005 -> 0E-1004 Clamped

precision: 3
extr1010 add 99949.9 0.200000 -> 1.00E+5 Inexact Rounded
extr1011 add 99949.9 0.100000 -> 1.00E+5 Inexact Rounded
extr1012 add 99849.9 0.200000 -> 9.99E+4 Inexact Rounded
extr1013 add 99849.9 0.100000 -> 9.98E+4 Inexact Rounded
extr1014 add 1.0149 0.00011 -> 1.02 Inexact Rounded
extr1015 add 1.0149 0.00010 -> 1.02 Inexact Rounded
extr1016 add 1.0149 0.00009 -> 1.01 Inexact Rounded
extr1017 add 1.0049 0.00011 -> 1.01 Inexact Rounded
extr1018 add 1.0049 0.00010 -> 1.00 Inexact Rounded
extr1019 add 1.0049 0.00009 -> 1.00 Inexact Rounded
rounding: down
extr1020 add 99999.9 0.200000 -> 1.00E+5 Inexact Rounded
extr1021 add 99999.8 0.200000 -> 1.00E+5 Rounded
extr1022 add 99999.7 0.200000 -> 9.99E+4 Inexact Rounded
rounding: half_even

-- a bug in _rescale caused the following to fail in Python 2.5.1
maxexponent: 999
minexponent: -999
precision: 6
extr1100 add 0E+1000 1E+1000 -> Infinity Overflow Inexact Rounded
extr1101 remainder 1E+1000 2E+1000 -> Infinity Overflow Inexact Rounded

-- tests for scaleb in case where input precision > context precision.
-- Result should be rounded.  (This isn't totally clear from the
-- specification, but the treatment of underflow in the testcases
-- suggests that rounding should occur in general.  Furthermore, it's
-- the way that the reference implementation behaves.)
maxexponent: 999
minexponent: -999
precision: 3
extr1200 scaleb 1234 1 -> 1.23E+4 Inexact Rounded
extr1201 scaleb 5678 0 -> 5.68E+3 Inexact Rounded
extr1202 scaleb -9105 -1 -> -910 Inexact Rounded

-- Invalid operation from 0 * infinity in fma
-- takes precedence over a third-argument sNaN
extr1300 fma 0 Inf sNaN123 -> NaN Invalid_operation
extr1301 fma Inf 0 sNaN456 -> NaN Invalid_operation
extr1302 fma 0E123 -Inf sNaN789 -> NaN Invalid_operation
extr1302 fma -Inf 0E-456 sNaN148 -> NaN Invalid_operation

-- max/min/max_mag/min_mag bug in 2.5.2/2.6/3.0: max(NaN, finite) gave
-- incorrect answers when the finite number required rounding; similarly
-- for the other three functions
maxexponent: 999
minexponent: -999
precision: 6
rounding: half_even
extr1400 max NaN 1234567 -> 1.23457E+6 Inexact Rounded
extr1401 max 3141590E-123 NaN1729 -> 3.14159E-117 Rounded
extr1402 max -7.654321 -NaN -> -7.65432 Inexact Rounded
extr1410 min -NaN -765432.1 -> -765432 Inexact Rounded
extr1411 min 3141592 NaN -> 3.14159E+6 Inexact Rounded
extr1420 max_mag 0.1111111 -NaN123 -> 0.111111 Inexact Rounded
extr1421 max_mag NaN999999999 0.001234567 -> 0.00123457 Inexact Rounded
extr1430 min_mag 9181716151 -NaN -> 9.18172E+9 Inexact Rounded
extr1431 min_mag NaN4 1.818180E100 -> 1.81818E+100 Rounded

-- Issue #6794: when comparing NaNs using compare_total, payloads
-- should be compared as though positive integers; not
-- lexicographically as strings.
extr1500 comparetotal NaN123 NaN45 -> 1
extr1501 comparetotal sNaN123 sNaN45 -> 1
extr1502 comparetotal -NaN123 -NaN45 -> -1
extr1503 comparetotal -sNaN123 -sNaN45 -> -1
extr1504 comparetotal NaN45 NaN123 -> -1
extr1505 comparetotal sNaN45 sNaN123 -> -1
extr1506 comparetotal -NaN45 -NaN123 -> 1
extr1507 comparetotal -sNaN45 -sNaN123 -> 1

extr1510 comparetotal -sNaN63450748854172416 -sNaN911993 -> -1
extr1511 comparetotmag NaN1222222222222 -NaN999999 -> 1

-- Issue #7233: rotate and scale should truncate an argument
-- of length greater than the current precision.
precision: 4
extr1600 rotate 1234567 -5 -> NaN Invalid_operation
extr1601 rotate 1234567 -4 -> 4567
extr1602 rotate 1234567 -3 -> 5674
extr1603 rotate 1234567 -2 -> 6745
extr1604 rotate 1234567 -1 -> 7456
extr1605 rotate 1234567 0 -> 4567
extr1606 rotate 1234567 1 -> 5674
extr1607 rotate 1234567 2 -> 6745
extr1608 rotate 1234567 3 -> 7456
extr1609 rotate 1234567 4 -> 4567
extr1610 rotate 1234567 5 -> NaN Invalid_operation

extr1650 shift 1234567 -5 -> NaN Invalid_operation
extr1651 shift 1234567 -4 -> 0
extr1652 shift 1234567 -3 -> 4
extr1653 shift 1234567 -2 -> 45
extr1654 shift 1234567 -1 -> 456
extr1655 shift 1234567 0 -> 4567
extr1656 shift 1234567 1 -> 5670
extr1657 shift 1234567 2 -> 6700
extr1658 shift 1234567 3 -> 7000
extr1659 shift 1234567 4 -> 0
extr1660 shift 1234567 5 -> NaN Invalid_operation

-- Cases where the power function was impossibly slow to determine that the
-- result is inexact.  Thanks Stefan Krah for identifying this problem.
precision: 16
maxExponent: 999999999
minExponent: -999999999
extr1700 power 10 1e-999999999 -> 1.000000000000000 Inexact Rounded
extr1701 power 100.0 -557.71e-742888888 -> 1.000000000000000 Inexact Rounded
extr1702 power 10 1e-100 -> 1.000000000000000 Inexact Rounded

-- Another one (see issue #12080).  Thanks again to Stefan Krah.
extr1703 power 4 -1.2e-999999999 -> 1.000000000000000 Inexact Rounded

-- A couple of interesting exact cases for power.  Note that the specification
-- requires these to be reported as Inexact.
extr1710 power 1e375 56e-3 -> 1.000000000000000E+21 Inexact Rounded
extr1711 power 10000 0.75 -> 1000.000000000000 Inexact Rounded
extr1712 power 1e-24 0.875 -> 1.000000000000000E-21 Inexact Rounded

-- Some more exact cases, exercising power with negative second argument.
extr1720 power 400 -0.5 -> 0.05000000000000000 Inexact Rounded
extr1721 power 4096 -0.75 -> 0.001953125000000000 Inexact Rounded
extr1722 power 625e4 -0.25 -> 0.02000000000000000 Inexact Rounded

-- Nonexact cases, to exercise some of the early exit conditions from
-- _power_exact.
extr1730 power 2048 -0.75 -> 0.003284751622084822 Inexact Rounded


-- Tests for the is_* boolean operations
precision: 9
maxExponent: 999
minExponent: -999

bool0000 iscanonical 0E-2000 -> 1
bool0001 iscanonical -0E-2000 -> 1
bool0002 iscanonical 0E-1008 -> 1
bool0003 iscanonical -0E-1008 -> 1
bool0004 iscanonical 0E-1007 -> 1
bool0005 iscanonical -0E-1007 -> 1
bool0006 iscanonical 0E-1006 -> 1
bool0007 iscanonical -0E-1006 -> 1
bool0008 iscanonical 0E-1000 -> 1
bool0009 iscanonical -0E-1000 -> 1
bool0010 iscanonical 0E-999 -> 1
bool0011 iscanonical -0E-999 -> 1
bool0012 iscanonical 0E-998 -> 1
bool0013 iscanonical -0E-998 -> 1
bool0014 iscanonical 0E-100 -> 1
bool0015 iscanonical -0E-100 -> 1
bool0016 iscanonical 0.000000 -> 1
bool0017 iscanonical -0.000000 -> 1
bool0018 iscanonical 0.000 -> 1
bool0019 iscanonical -0.000 -> 1
bool0020 iscanonical 0.00 -> 1
bool0021 iscanonical -0.00 -> 1
bool0022 iscanonical 0.0 -> 1
bool0023 iscanonical -0.0 -> 1
bool0024 iscanonical 0 -> 1
bool0025 iscanonical -0 -> 1
bool0026 iscanonical 0E+1 -> 1
bool0027 iscanonical -0E+1 -> 1
bool0028 iscanonical 0E+2 -> 1
bool0029 iscanonical -0E+2 -> 1
bool0030 iscanonical 0E+3 -> 1
bool0031 iscanonical -0E+3 -> 1
bool0032 iscanonical 0E+6 -> 1
bool0033 iscanonical -0E+6 -> 1
bool0034 iscanonical 0E+100 -> 1
bool0035 iscanonical -0E+100 -> 1
bool0036 iscanonical 0E+990 -> 1
bool0037 iscanonical -0E+990 -> 1
bool0038 iscanonical 0E+991 -> 1
bool0039 iscanonical -0E+991 -> 1
bool0040 iscanonical 0E+992 -> 1
bool0041 iscanonical -0E+992 -> 1
bool0042 iscanonical 0E+998 -> 1
bool0043 iscanonical -0E+998 -> 1
bool0044 iscanonical 0E+999 -> 1
bool0045 iscanonical -0E+999 -> 1
bool0046 iscanonical 0E+1000 -> 1
bool0047 iscanonical -0E+1000 -> 1
bool0048 iscanonical 0E+2000 -> 1
bool0049 iscanonical -0E+2000 -> 1
bool0050 iscanonical 1E-2000 -> 1
bool0051 iscanonical -1E-2000 -> 1
bool0052 iscanonical 1E-1008 -> 1
bool0053 iscanonical -1E-1008 -> 1
bool0054 iscanonical 1E-1007 -> 1
bool0055 iscanonical -1E-1007 -> 1
bool0056 iscanonical 1E-1006 -> 1
bool0057 iscanonical -1E-1006 -> 1
bool0058 iscanonical 1E-1000 -> 1
bool0059 iscanonical -1E-1000 -> 1
bool0060 iscanonical 1E-999 -> 1
bool0061 iscanonical -1E-999 -> 1
bool0062 iscanonical 1E-998 -> 1
bool0063 iscanonical -1E-998 -> 1
bool0064 iscanonical 1E-100 -> 1
bool0065 iscanonical -1E-100 -> 1
bool0066 iscanonical 0.000001 -> 1
bool0067 iscanonical -0.000001 -> 1
bool0068 iscanonical 0.001 -> 1
bool0069 iscanonical -0.001 -> 1
bool0070 iscanonical 0.01 -> 1
bool0071 iscanonical -0.01 -> 1
bool0072 iscanonical 0.1 -> 1
bool0073 iscanonical -0.1 -> 1
bool0074 iscanonical 1 -> 1
bool0075 iscanonical -1 -> 1
bool0076 iscanonical 1E+1 -> 1
bool0077 iscanonical -1E+1 -> 1
bool0078 iscanonical 1E+2 -> 1
bool0079 iscanonical -1E+2 -> 1
bool0080 iscanonical 1E+3 -> 1
bool0081 iscanonical -1E+3 -> 1
bool0082 iscanonical 1E+6 -> 1
bool0083 iscanonical -1E+6 -> 1
bool0084 iscanonical 1E+100 -> 1
bool0085 iscanonical -1E+100 -> 1
bool0086 iscanonical 1E+990 -> 1
bool0087 iscanonical -1E+990 -> 1
bool0088 iscanonical 1E+991 -> 1
bool0089 iscanonical -1E+991 -> 1
bool0090 iscanonical 1E+992 -> 1
bool0091 iscanonical -1E+992 -> 1
bool0092 iscanonical 1E+998 -> 1
bool0093 iscanonical -1E+998 -> 1
bool0094 iscanonical 1E+999 -> 1
bool0095 iscanonical -1E+999 -> 1
bool0096 iscanonical 1E+1000 -> 1
bool0097 iscanonical -1E+1000 -> 1
bool0098 iscanonical 1E+2000 -> 1
bool0099 iscanonical -1E+2000 -> 1
bool0100 iscanonical 9E-2000 -> 1
bool0101 iscanonical -9E-2000 -> 1
bool0102 iscanonical 9E-1008 -> 1
bool0103 iscanonical -9E-1008 -> 1
bool0104 iscanonical 9E-1007 -> 1
bool0105 iscanonical -9E-1007 -> 1
bool0106 iscanonical 9E-1006 -> 1
bool0107 iscanonical -9E-1006 -> 1
bool0108 iscanonical 9E-1000 -> 1
bool0109 iscanonical -9E-1000 -> 1
bool0110 iscanonical 9E-999 -> 1
bool0111 iscanonical -9E-999 -> 1
bool0112 iscanonical 9E-998 -> 1
bool0113 iscanonical -9E-998 -> 1
bool0114 iscanonical 9E-100 -> 1
bool0115 iscanonical -9E-100 -> 1
bool0116 iscanonical 0.000009 -> 1
bool0117 iscanonical -0.000009 -> 1
bool0118 iscanonical 0.009 -> 1
bool0119 iscanonical -0.009 -> 1
bool0120 iscanonical 0.09 -> 1
bool0121 iscanonical -0.09 -> 1
bool0122 iscanonical 0.9 -> 1
bool0123 iscanonical -0.9 -> 1
bool0124 iscanonical 9 -> 1
bool0125 iscanonical -9 -> 1
bool0126 iscanonical 9E+1 -> 1
bool0127 iscanonical -9E+1 -> 1
bool0128 iscanonical 9E+2 -> 1
bool0129 iscanonical -9E+2 -> 1
bool0130 iscanonical 9E+3 -> 1
bool0131 iscanonical -9E+3 -> 1
bool0132 iscanonical 9E+6 -> 1
bool0133 iscanonical -9E+6 -> 1
bool0134 iscanonical 9E+100 -> 1
bool0135 iscanonical -9E+100 -> 1
bool0136 iscanonical 9E+990 -> 1
bool0137 iscanonical -9E+990 -> 1
bool0138 iscanonical 9E+991 -> 1
bool0139 iscanonical -9E+991 -> 1
bool0140 iscanonical 9E+992 -> 1
bool0141 iscanonical -9E+992 -> 1
bool0142 iscanonical 9E+998 -> 1
bool0143 iscanonical -9E+998 -> 1
bool0144 iscanonical 9E+999 -> 1
bool0145 iscanonical -9E+999 -> 1
bool0146 iscanonical 9E+1000 -> 1
bool0147 iscanonical -9E+1000 -> 1
bool0148 iscanonical 9E+2000 -> 1
bool0149 iscanonical -9E+2000 -> 1
bool0150 iscanonical 9.99999999E-2000 -> 1
bool0151 iscanonical -9.99999999E-2000 -> 1
bool0152 iscanonical 9.99999999E-1008 -> 1
bool0153 iscanonical -9.99999999E-1008 -> 1
bool0154 iscanonical 9.99999999E-1007 -> 1
bool0155 iscanonical -9.99999999E-1007 -> 1
bool0156 iscanonical 9.99999999E-1006 -> 1
bool0157 iscanonical -9.99999999E-1006 -> 1
bool0158 iscanonical 9.99999999E-1000 -> 1
bool0159 iscanonical -9.99999999E-1000 -> 1
bool0160 iscanonical 9.99999999E-999 -> 1
bool0161 iscanonical -9.99999999E-999 -> 1
bool0162 iscanonical 9.99999999E-998 -> 1
bool0163 iscanonical -9.99999999E-998 -> 1
bool0164 iscanonical 9.99999999E-100 -> 1
bool0165 iscanonical -9.99999999E-100 -> 1
bool0166 iscanonical 0.00000999999999 -> 1
bool0167 iscanonical -0.00000999999999 -> 1
bool0168 iscanonical 0.00999999999 -> 1
bool0169 iscanonical -0.00999999999 -> 1
bool0170 iscanonical 0.0999999999 -> 1
bool0171 iscanonical -0.0999999999 -> 1
bool0172 iscanonical 0.999999999 -> 1
bool0173 iscanonical -0.999999999 -> 1
bool0174 iscanonical 9.99999999 -> 1
bool0175 iscanonical -9.99999999 -> 1
bool0176 iscanonical 99.9999999 -> 1
bool0177 iscanonical -99.9999999 -> 1
bool0178 iscanonical 999.999999 -> 1
bool0179 iscanonical -999.999999 -> 1
bool0180 iscanonical 9999.99999 -> 1
bool0181 iscanonical -9999.99999 -> 1
bool0182 iscanonical 9999999.99 -> 1
bool0183 iscanonical -9999999.99 -> 1
bool0184 iscanonical 9.99999999E+100 -> 1
bool0185 iscanonical -9.99999999E+100 -> 1
bool0186 iscanonical 9.99999999E+990 -> 1
bool0187 iscanonical -9.99999999E+990 -> 1
bool0188 iscanonical 9.99999999E+991 -> 1
bool0189 iscanonical -9.99999999E+991 -> 1
bool0190 iscanonical 9.99999999E+992 -> 1
bool0191 iscanonical -9.99999999E+992 -> 1
bool0192 iscanonical 9.99999999E+998 -> 1
bool0193 iscanonical -9.99999999E+998 -> 1
bool0194 iscanonical 9.99999999E+999 -> 1
bool0195 iscanonical -9.99999999E+999 -> 1
bool0196 iscanonical 9.99999999E+1000 -> 1
bool0197 iscanonical -9.99999999E+1000 -> 1
bool0198 iscanonical 9.99999999E+2000 -> 1
bool0199 iscanonical -9.99999999E+2000 -> 1
bool0200 iscanonical Infinity -> 1
bool0201 iscanonical -Infinity -> 1
bool0202 iscanonical NaN -> 1
bool0203 iscanonical -NaN -> 1
bool0204 iscanonical NaN123 -> 1
bool0205 iscanonical -NaN123 -> 1
bool0206 iscanonical sNaN -> 1
bool0207 iscanonical -sNaN -> 1
bool0208 iscanonical sNaN123 -> 1
bool0209 iscanonical -sNaN123 -> 1
bool0210 isfinite 0E-2000 -> 1
bool0211 isfinite -0E-2000 -> 1
bool0212 isfinite 0E-1008 -> 1
bool0213 isfinite -0E-1008 -> 1
bool0214 isfinite 0E-1007 -> 1
bool0215 isfinite -0E-1007 -> 1
bool0216 isfinite 0E-1006 -> 1
bool0217 isfinite -0E-1006 -> 1
bool0218 isfinite 0E-1000 -> 1
bool0219 isfinite -0E-1000 -> 1
bool0220 isfinite 0E-999 -> 1
bool0221 isfinite -0E-999 -> 1
bool0222 isfinite 0E-998 -> 1
bool0223 isfinite -0E-998 -> 1
bool0224 isfinite 0E-100 -> 1
bool0225 isfinite -0E-100 -> 1
bool0226 isfinite 0.000000 -> 1
bool0227 isfinite -0.000000 -> 1
bool0228 isfinite 0.000 -> 1
bool0229 isfinite -0.000 -> 1
bool0230 isfinite 0.00 -> 1
bool0231 isfinite -0.00 -> 1
bool0232 isfinite 0.0 -> 1
bool0233 isfinite -0.0 -> 1
bool0234 isfinite 0 -> 1
bool0235 isfinite -0 -> 1
bool0236 isfinite 0E+1 -> 1
bool0237 isfinite -0E+1 -> 1
bool0238 isfinite 0E+2 -> 1
bool0239 isfinite -0E+2 -> 1
bool0240 isfinite 0E+3 -> 1
bool0241 isfinite -0E+3 -> 1
bool0242 isfinite 0E+6 -> 1
bool0243 isfinite -0E+6 -> 1
bool0244 isfinite 0E+100 -> 1
bool0245 isfinite -0E+100 -> 1
bool0246 isfinite 0E+990 -> 1
bool0247 isfinite -0E+990 -> 1
bool0248 isfinite 0E+991 -> 1
bool0249 isfinite -0E+991 -> 1
bool0250 isfinite 0E+992 -> 1
bool0251 isfinite -0E+992 -> 1
bool0252 isfinite 0E+998 -> 1
bool0253 isfinite -0E+998 -> 1
bool0254 isfinite 0E+999 -> 1
bool0255 isfinite -0E+999 -> 1
bool0256 isfinite 0E+1000 -> 1
bool0257 isfinite -0E+1000 -> 1
bool0258 isfinite 0E+2000 -> 1
bool0259 isfinite -0E+2000 -> 1
bool0260 isfinite 1E-2000 -> 1
bool0261 isfinite -1E-2000 -> 1
bool0262 isfinite 1E-1008 -> 1
bool0263 isfinite -1E-1008 -> 1
bool0264 isfinite 1E-1007 -> 1
bool0265 isfinite -1E-1007 -> 1
bool0266 isfinite 1E-1006 -> 1
bool0267 isfinite -1E-1006 -> 1
bool0268 isfinite 1E-1000 -> 1
bool0269 isfinite -1E-1000 -> 1
bool0270 isfinite 1E-999 -> 1
bool0271 isfinite -1E-999 -> 1
bool0272 isfinite 1E-998 -> 1
bool0273 isfinite -1E-998 -> 1
bool0274 isfinite 1E-100 -> 1
bool0275 isfinite -1E-100 -> 1
bool0276 isfinite 0.000001 -> 1
bool0277 isfinite -0.000001 -> 1
bool0278 isfinite 0.001 -> 1
bool0279 isfinite -0.001 -> 1
bool0280 isfinite 0.01 -> 1
bool0281 isfinite -0.01 -> 1
bool0282 isfinite 0.1 -> 1
bool0283 isfinite -0.1 -> 1
bool0284 isfinite 1 -> 1
bool0285 isfinite -1 -> 1
bool0286 isfinite 1E+1 -> 1
bool0287 isfinite -1E+1 -> 1
bool0288 isfinite 1E+2 -> 1
bool0289 isfinite -1E+2 -> 1
bool0290 isfinite 1E+3 -> 1
bool0291 isfinite -1E+3 -> 1
bool0292 isfinite 1E+6 -> 1
bool0293 isfinite -1E+6 -> 1
bool0294 isfinite 1E+100 -> 1
bool0295 isfinite -1E+100 -> 1
bool0296 isfinite 1E+990 -> 1
bool0297 isfinite -1E+990 -> 1
bool0298 isfinite 1E+991 -> 1
bool0299 isfinite -1E+991 -> 1
bool0300 isfinite 1E+992 -> 1
bool0301 isfinite -1E+992 -> 1
bool0302 isfinite 1E+998 -> 1
bool0303 isfinite -1E+998 -> 1
bool0304 isfinite 1E+999 -> 1
bool0305 isfinite -1E+999 -> 1
bool0306 isfinite 1E+1000 -> 1
bool0307 isfinite -1E+1000 -> 1
bool0308 isfinite 1E+2000 -> 1
bool0309 isfinite -1E+2000 -> 1
bool0310 isfinite 9E-2000 -> 1
bool0311 isfinite -9E-2000 -> 1
bool0312 isfinite 9E-1008 -> 1
bool0313 isfinite -9E-1008 -> 1
bool0314 isfinite 9E-1007 -> 1
bool0315 isfinite -9E-1007 -> 1
bool0316 isfinite 9E-1006 -> 1
bool0317 isfinite -9E-1006 -> 1
bool0318 isfinite 9E-1000 -> 1
bool0319 isfinite -9E-1000 -> 1
bool0320 isfinite 9E-999 -> 1
bool0321 isfinite -9E-999 -> 1
bool0322 isfinite 9E-998 -> 1
bool0323 isfinite -9E-998 -> 1
bool0324 isfinite 9E-100 -> 1
bool0325 isfinite -9E-100 -> 1
bool0326 isfinite 0.000009 -> 1
bool0327 isfinite -0.000009 -> 1
bool0328 isfinite 0.009 -> 1
bool0329 isfinite -0.009 -> 1
bool0330 isfinite 0.09 -> 1
bool0331 isfinite -0.09 -> 1
bool0332 isfinite 0.9 -> 1
bool0333 isfinite -0.9 -> 1
bool0334 isfinite 9 -> 1
bool0335 isfinite -9 -> 1
bool0336 isfinite 9E+1 -> 1
bool0337 isfinite -9E+1 -> 1
bool0338 isfinite 9E+2 -> 1
bool0339 isfinite -9E+2 -> 1
bool0340 isfinite 9E+3 -> 1
bool0341 isfinite -9E+3 -> 1
bool0342 isfinite 9E+6 -> 1
bool0343 isfinite -9E+6 -> 1
bool0344 isfinite 9E+100 -> 1
bool0345 isfinite -9E+100 -> 1
bool0346 isfinite 9E+990 -> 1
bool0347 isfinite -9E+990 -> 1
bool0348 isfinite 9E+991 -> 1
bool0349 isfinite -9E+991 -> 1
bool0350 isfinite 9E+992 -> 1
bool0351 isfinite -9E+992 -> 1
bool0352 isfinite 9E+998 -> 1
bool0353 isfinite -9E+998 -> 1
bool0354 isfinite 9E+999 -> 1
bool0355 isfinite -9E+999 -> 1
bool0356 isfinite 9E+1000 -> 1
bool0357 isfinite -9E+1000 -> 1
bool0358 isfinite 9E+2000 -> 1
bool0359 isfinite -9E+2000 -> 1
bool0360 isfinite 9.99999999E-2000 -> 1
bool0361 isfinite -9.99999999E-2000 -> 1
bool0362 isfinite 9.99999999E-1008 -> 1
bool0363 isfinite -9.99999999E-1008 -> 1
bool0364 isfinite 9.99999999E-1007 -> 1
bool0365 isfinite -9.99999999E-1007 -> 1
bool0366 isfinite 9.99999999E-1006 -> 1
bool0367 isfinite -9.99999999E-1006 -> 1
bool0368 isfinite 9.99999999E-1000 -> 1
bool0369 isfinite -9.99999999E-1000 -> 1
bool0370 isfinite 9.99999999E-999 -> 1
bool0371 isfinite -9.99999999E-999 -> 1
bool0372 isfinite 9.99999999E-998 -> 1
bool0373 isfinite -9.99999999E-998 -> 1
bool0374 isfinite 9.99999999E-100 -> 1
bool0375 isfinite -9.99999999E-100 -> 1
bool0376 isfinite 0.00000999999999 -> 1
bool0377 isfinite -0.00000999999999 -> 1
bool0378 isfinite 0.00999999999 -> 1
bool0379 isfinite -0.00999999999 -> 1
bool0380 isfinite 0.0999999999 -> 1
bool0381 isfinite -0.0999999999 -> 1
bool0382 isfinite 0.999999999 -> 1
bool0383 isfinite -0.999999999 -> 1
bool0384 isfinite 9.99999999 -> 1
bool0385 isfinite -9.99999999 -> 1
bool0386 isfinite 99.9999999 -> 1
bool0387 isfinite -99.9999999 -> 1
bool0388 isfinite 999.999999 -> 1
bool0389 isfinite -999.999999 -> 1
bool0390 isfinite 9999.99999 -> 1
bool0391 isfinite -9999.99999 -> 1
bool0392 isfinite 9999999.99 -> 1
bool0393 isfinite -9999999.99 -> 1
bool0394 isfinite 9.99999999E+100 -> 1
bool0395 isfinite -9.99999999E+100 -> 1
bool0396 isfinite 9.99999999E+990 -> 1
bool0397 isfinite -9.99999999E+990 -> 1
bool0398 isfinite 9.99999999E+991 -> 1
bool0399 isfinite -9.99999999E+991 -> 1
bool0400 isfinite 9.99999999E+992 -> 1
bool0401 isfinite -9.99999999E+992 -> 1
bool0402 isfinite 9.99999999E+998 -> 1
bool0403 isfinite -9.99999999E+998 -> 1
bool0404 isfinite 9.99999999E+999 -> 1
bool0405 isfinite -9.99999999E+999 -> 1
bool0406 isfinite 9.99999999E+1000 -> 1
bool0407 isfinite -9.99999999E+1000 -> 1
bool0408 isfinite 9.99999999E+2000 -> 1
bool0409 isfinite -9.99999999E+2000 -> 1
bool0410 isfinite Infinity -> 0
bool0411 isfinite -Infinity -> 0
bool0412 isfinite NaN -> 0
bool0413 isfinite -NaN -> 0
bool0414 isfinite NaN123 -> 0
bool0415 isfinite -NaN123 -> 0
bool0416 isfinite sNaN -> 0
bool0417 isfinite -sNaN -> 0
bool0418 isfinite sNaN123 -> 0
bool0419 isfinite -sNaN123 -> 0
bool0420 isinfinite 0E-2000 -> 0
bool0421 isinfinite -0E-2000 -> 0
bool0422 isinfinite 0E-1008 -> 0
bool0423 isinfinite -0E-1008 -> 0
bool0424 isinfinite 0E-1007 -> 0
bool0425 isinfinite -0E-1007 -> 0
bool0426 isinfinite 0E-1006 -> 0
bool0427 isinfinite -0E-1006 -> 0
bool0428 isinfinite 0E-1000 -> 0
bool0429 isinfinite -0E-1000 -> 0
bool0430 isinfinite 0E-999 -> 0
bool0431 isinfinite -0E-999 -> 0
bool0432 isinfinite 0E-998 -> 0
bool0433 isinfinite -0E-998 -> 0
bool0434 isinfinite 0E-100 -> 0
bool0435 isinfinite -0E-100 -> 0
bool0436 isinfinite 0.000000 -> 0
bool0437 isinfinite -0.000000 -> 0
bool0438 isinfinite 0.000 -> 0
bool0439 isinfinite -0.000 -> 0
bool0440 isinfinite 0.00 -> 0
bool0441 isinfinite -0.00 -> 0
bool0442 isinfinite 0.0 -> 0
bool0443 isinfinite -0.0 -> 0
bool0444 isinfinite 0 -> 0
bool0445 isinfinite -0 -> 0
bool0446 isinfinite 0E+1 -> 0
bool0447 isinfinite -0E+1 -> 0
bool0448 isinfinite 0E+2 -> 0
bool0449 isinfinite -0E+2 -> 0
bool0450 isinfinite 0E+3 -> 0
bool0451 isinfinite -0E+3 -> 0
bool0452 isinfinite 0E+6 -> 0
bool0453 isinfinite -0E+6 -> 0
bool0454 isinfinite 0E+100 -> 0
bool0455 isinfinite -0E+100 -> 0
bool0456 isinfinite 0E+990 -> 0
bool0457 isinfinite -0E+990 -> 0
bool0458 isinfinite 0E+991 -> 0
bool0459 isinfinite -0E+991 -> 0
bool0460 isinfinite 0E+992 -> 0
bool0461 isinfinite -0E+992 -> 0
bool0462 isinfinite 0E+998 -> 0
bool0463 isinfinite -0E+998 -> 0
bool0464 isinfinite 0E+999 -> 0
bool0465 isinfinite -0E+999 -> 0
bool0466 isinfinite 0E+1000 -> 0
bool0467 isinfinite -0E+1000 -> 0
bool0468 isinfinite 0E+2000 -> 0
bool0469 isinfinite -0E+2000 -> 0
bool0470 isinfinite 1E-2000 -> 0
bool0471 isinfinite -1E-2000 -> 0
bool0472 isinfinite 1E-1008 -> 0
bool0473 isinfinite -1E-1008 -> 0
bool0474 isinfinite 1E-1007 -> 0
bool0475 isinfinite -1E-1007 -> 0
bool0476 isinfinite 1E-1006 -> 0
bool0477 isinfinite -1E-1006 -> 0
bool0478 isinfinite 1E-1000 -> 0
bool0479 isinfinite -1E-1000 -> 0
bool0480 isinfinite 1E-999 -> 0
bool0481 isinfinite -1E-999 -> 0
bool0482 isinfinite 1E-998 -> 0
bool0483 isinfinite -1E-998 -> 0
bool0484 isinfinite 1E-100 -> 0
bool0485 isinfinite -1E-100 -> 0
bool0486 isinfinite 0.000001 -> 0
bool0487 isinfinite -0.000001 -> 0
bool0488 isinfinite 0.001 -> 0
bool0489 isinfinite -0.001 -> 0
bool0490 isinfinite 0.01 -> 0
bool0491 isinfinite -0.01 -> 0
bool0492 isinfinite 0.1 -> 0
bool0493 isinfinite -0.1 -> 0
bool0494 isinfinite 1 -> 0
bool0495 isinfinite -1 -> 0
bool0496 isinfinite 1E+1 -> 0
bool0497 isinfinite -1E+1 -> 0
bool0498 isinfinite 1E+2 -> 0
bool0499 isinfinite -1E+2 -> 0
bool0500 isinfinite 1E+3 -> 0
bool0501 isinfinite -1E+3 -> 0
bool0502 isinfinite 1E+6 -> 0
bool0503 isinfinite -1E+6 -> 0
bool0504 isinfinite 1E+100 -> 0
bool0505 isinfinite -1E+100 -> 0
bool0506 isinfinite 1E+990 -> 0
bool0507 isinfinite -1E+990 -> 0
bool0508 isinfinite 1E+991 -> 0
bool0509 isinfinite -1E+991 -> 0
bool0510 isinfinite 1E+992 -> 0
bool0511 isinfinite -1E+992 -> 0
bool0512 isinfinite 1E+998 -> 0
bool0513 isinfinite -1E+998 -> 0
bool0514 isinfinite 1E+999 -> 0
bool0515 isinfinite -1E+999 -> 0
bool0516 isinfinite 1E+1000 -> 0
bool0517 isinfinite -1E+1000 -> 0
bool0518 isinfinite 1E+2000 -> 0
bool0519 isinfinite -1E+2000 -> 0
bool0520 isinfinite 9E-2000 -> 0
bool0521 isinfinite -9E-2000 -> 0
bool0522 isinfinite 9E-1008 -> 0
bool0523 isinfinite -9E-1008 -> 0
bool0524 isinfinite 9E-1007 -> 0
bool0525 isinfinite -9E-1007 -> 0
bool0526 isinfinite 9E-1006 -> 0
bool0527 isinfinite -9E-1006 -> 0
bool0528 isinfinite 9E-1000 -> 0
bool0529 isinfinite -9E-1000 -> 0
bool0530 isinfinite 9E-999 -> 0
bool0531 isinfinite -9E-999 -> 0
bool0532 isinfinite 9E-998 -> 0
bool0533 isinfinite -9E-998 -> 0
bool0534 isinfinite 9E-100 -> 0
bool0535 isinfinite -9E-100 -> 0
bool0536 isinfinite 0.000009 -> 0
bool0537 isinfinite -0.000009 -> 0
bool0538 isinfinite 0.009 -> 0
bool0539 isinfinite -0.009 -> 0
bool0540 isinfinite 0.09 -> 0
bool0541 isinfinite -0.09 -> 0
bool0542 isinfinite 0.9 -> 0
bool0543 isinfinite -0.9 -> 0
bool0544 isinfinite 9 -> 0
bool0545 isinfinite -9 -> 0
bool0546 isinfinite 9E+1 -> 0
bool0547 isinfinite -9E+1 -> 0
bool0548 isinfinite 9E+2 -> 0
bool0549 isinfinite -9E+2 -> 0
bool0550 isinfinite 9E+3 -> 0
bool0551 isinfinite -9E+3 -> 0
bool0552 isinfinite 9E+6 -> 0
bool0553 isinfinite -9E+6 -> 0
bool0554 isinfinite 9E+100 -> 0
bool0555 isinfinite -9E+100 -> 0
bool0556 isinfinite 9E+990 -> 0
bool0557 isinfinite -9E+990 -> 0
bool0558 isinfinite 9E+991 -> 0
bool0559 isinfinite -9E+991 -> 0
bool0560 isinfinite 9E+992 -> 0
bool0561 isinfinite -9E+992 -> 0
bool0562 isinfinite 9E+998 -> 0
bool0563 isinfinite -9E+998 -> 0
bool0564 isinfinite 9E+999 -> 0
bool0565 isinfinite -9E+999 -> 0
bool0566 isinfinite 9E+1000 -> 0
bool0567 isinfinite -9E+1000 -> 0
bool0568 isinfinite 9E+2000 -> 0
bool0569 isinfinite -9E+2000 -> 0
bool0570 isinfinite 9.99999999E-2000 -> 0
bool0571 isinfinite -9.99999999E-2000 -> 0
bool0572 isinfinite 9.99999999E-1008 -> 0
bool0573 isinfinite -9.99999999E-1008 -> 0
bool0574 isinfinite 9.99999999E-1007 -> 0
bool0575 isinfinite -9.99999999E-1007 -> 0
bool0576 isinfinite 9.99999999E-1006 -> 0
bool0577 isinfinite -9.99999999E-1006 -> 0
bool0578 isinfinite 9.99999999E-1000 -> 0
bool0579 isinfinite -9.99999999E-1000 -> 0
bool0580 isinfinite 9.99999999E-999 -> 0
bool0581 isinfinite -9.99999999E-999 -> 0
bool0582 isinfinite 9.99999999E-998 -> 0
bool0583 isinfinite -9.99999999E-998 -> 0
bool0584 isinfinite 9.99999999E-100 -> 0
bool0585 isinfinite -9.99999999E-100 -> 0
bool0586 isinfinite 0.00000999999999 -> 0
bool0587 isinfinite -0.00000999999999 -> 0
bool0588 isinfinite 0.00999999999 -> 0
bool0589 isinfinite -0.00999999999 -> 0
bool0590 isinfinite 0.0999999999 -> 0
bool0591 isinfinite -0.0999999999 -> 0
bool0592 isinfinite 0.999999999 -> 0
bool0593 isinfinite -0.999999999 -> 0
bool0594 isinfinite 9.99999999 -> 0
bool0595 isinfinite -9.99999999 -> 0
bool0596 isinfinite 99.9999999 -> 0
bool0597 isinfinite -99.9999999 -> 0
bool0598 isinfinite 999.999999 -> 0
bool0599 isinfinite -999.999999 -> 0
bool0600 isinfinite 9999.99999 -> 0
bool0601 isinfinite -9999.99999 -> 0
bool0602 isinfinite 9999999.99 -> 0
bool0603 isinfinite -9999999.99 -> 0
bool0604 isinfinite 9.99999999E+100 -> 0
bool0605 isinfinite -9.99999999E+100 -> 0
bool0606 isinfinite 9.99999999E+990 -> 0
bool0607 isinfinite -9.99999999E+990 -> 0
bool0608 isinfinite 9.99999999E+991 -> 0
bool0609 isinfinite -9.99999999E+991 -> 0
bool0610 isinfinite 9.99999999E+992 -> 0
bool0611 isinfinite -9.99999999E+992 -> 0
bool0612 isinfinite 9.99999999E+998 -> 0
bool0613 isinfinite -9.99999999E+998 -> 0
bool0614 isinfinite 9.99999999E+999 -> 0
bool0615 isinfinite -9.99999999E+999 -> 0
bool0616 isinfinite 9.99999999E+1000 -> 0
bool0617 isinfinite -9.99999999E+1000 -> 0
bool0618 isinfinite 9.99999999E+2000 -> 0
bool0619 isinfinite -9.99999999E+2000 -> 0
bool0620 isinfinite Infinity -> 1
bool0621 isinfinite -Infinity -> 1
bool0622 isinfinite NaN -> 0
bool0623 isinfinite -NaN -> 0
bool0624 isinfinite NaN123 -> 0
bool0625 isinfinite -NaN123 -> 0
bool0626 isinfinite sNaN -> 0
bool0627 isinfinite -sNaN -> 0
bool0628 isinfinite sNaN123 -> 0
bool0629 isinfinite -sNaN123 -> 0
bool0630 isnan 0E-2000 -> 0
bool0631 isnan -0E-2000 -> 0
bool0632 isnan 0E-1008 -> 0
bool0633 isnan -0E-1008 -> 0
bool0634 isnan 0E-1007 -> 0
bool0635 isnan -0E-1007 -> 0
bool0636 isnan 0E-1006 -> 0
bool0637 isnan -0E-1006 -> 0
bool0638 isnan 0E-1000 -> 0
bool0639 isnan -0E-1000 -> 0
bool0640 isnan 0E-999 -> 0
bool0641 isnan -0E-999 -> 0
bool0642 isnan 0E-998 -> 0
bool0643 isnan -0E-998 -> 0
bool0644 isnan 0E-100 -> 0
bool0645 isnan -0E-100 -> 0
bool0646 isnan 0.000000 -> 0
bool0647 isnan -0.000000 -> 0
bool0648 isnan 0.000 -> 0
bool0649 isnan -0.000 -> 0
bool0650 isnan 0.00 -> 0
bool0651 isnan -0.00 -> 0
bool0652 isnan 0.0 -> 0
bool0653 isnan -0.0 -> 0
bool0654 isnan 0 -> 0
bool0655 isnan -0 -> 0
bool0656 isnan 0E+1 -> 0
bool0657 isnan -0E+1 -> 0
bool0658 isnan 0E+2 -> 0
bool0659 isnan -0E+2 -> 0
bool0660 isnan 0E+3 -> 0
bool0661 isnan -0E+3 -> 0
bool0662 isnan 0E+6 -> 0
bool0663 isnan -0E+6 -> 0
bool0664 isnan 0E+100 -> 0
bool0665 isnan -0E+100 -> 0
bool0666 isnan 0E+990 -> 0
bool0667 isnan -0E+990 -> 0
bool0668 isnan 0E+991 -> 0
bool0669 isnan -0E+991 -> 0
bool0670 isnan 0E+992 -> 0
bool0671 isnan -0E+992 -> 0
bool0672 isnan 0E+998 -> 0
bool0673 isnan -0E+998 -> 0
bool0674 isnan 0E+999 -> 0
bool0675 isnan -0E+999 -> 0
bool0676 isnan 0E+1000 -> 0
bool0677 isnan -0E+1000 -> 0
bool0678 isnan 0E+2000 -> 0
bool0679 isnan -0E+2000 -> 0
bool0680 isnan 1E-2000 -> 0
bool0681 isnan -1E-2000 -> 0
bool0682 isnan 1E-1008 -> 0
bool0683 isnan -1E-1008 -> 0
bool0684 isnan 1E-1007 -> 0
bool0685 isnan -1E-1007 -> 0
bool0686 isnan 1E-1006 -> 0
bool0687 isnan -1E-1006 -> 0
bool0688 isnan 1E-1000 -> 0
bool0689 isnan -1E-1000 -> 0
bool0690 isnan 1E-999 -> 0
bool0691 isnan -1E-999 -> 0
bool0692 isnan 1E-998 -> 0
bool0693 isnan -1E-998 -> 0
bool0694 isnan 1E-100 -> 0
bool0695 isnan -1E-100 -> 0
bool0696 isnan 0.000001 -> 0
bool0697 isnan -0.000001 -> 0
bool0698 isnan 0.001 -> 0
bool0699 isnan -0.001 -> 0
bool0700 isnan 0.01 -> 0
bool0701 isnan -0.01 -> 0
bool0702 isnan 0.1 -> 0
bool0703 isnan -0.1 -> 0
bool0704 isnan 1 -> 0
bool0705 isnan -1 -> 0
bool0706 isnan 1E+1 -> 0
bool0707 isnan -1E+1 -> 0
bool0708 isnan 1E+2 -> 0
bool0709 isnan -1E+2 -> 0
bool0710 isnan 1E+3 -> 0
bool0711 isnan -1E+3 -> 0
bool0712 isnan 1E+6 -> 0
bool0713 isnan -1E+6 -> 0
bool0714 isnan 1E+100 -> 0
bool0715 isnan -1E+100 -> 0
bool0716 isnan 1E+990 -> 0
bool0717 isnan -1E+990 -> 0
bool0718 isnan 1E+991 -> 0
bool0719 isnan -1E+991 -> 0
bool0720 isnan 1E+992 -> 0
bool0721 isnan -1E+992 -> 0
bool0722 isnan 1E+998 -> 0
bool0723 isnan -1E+998 -> 0
bool0724 isnan 1E+999 -> 0
bool0725 isnan -1E+999 -> 0
bool0726 isnan 1E+1000 -> 0
bool0727 isnan -1E+1000 -> 0
bool0728 isnan 1E+2000 -> 0
bool0729 isnan -1E+2000 -> 0
bool0730 isnan 9E-2000 -> 0
bool0731 isnan -9E-2000 -> 0
bool0732 isnan 9E-1008 -> 0
bool0733 isnan -9E-1008 -> 0
bool0734 isnan 9E-1007 -> 0
bool0735 isnan -9E-1007 -> 0
bool0736 isnan 9E-1006 -> 0
bool0737 isnan -9E-1006 -> 0
bool0738 isnan 9E-1000 -> 0
bool0739 isnan -9E-1000 -> 0
bool0740 isnan 9E-999 -> 0
bool0741 isnan -9E-999 -> 0
bool0742 isnan 9E-998 -> 0
bool0743 isnan -9E-998 -> 0
bool0744 isnan 9E-100 -> 0
bool0745 isnan -9E-100 -> 0
bool0746 isnan 0.000009 -> 0
bool0747 isnan -0.000009 -> 0
bool0748 isnan 0.009 -> 0
bool0749 isnan -0.009 -> 0
bool0750 isnan 0.09 -> 0
bool0751 isnan -0.09 -> 0
bool0752 isnan 0.9 -> 0
bool0753 isnan -0.9 -> 0
bool0754 isnan 9 -> 0
bool0755 isnan -9 -> 0
bool0756 isnan 9E+1 -> 0
bool0757 isnan -9E+1 -> 0
bool0758 isnan 9E+2 -> 0
bool0759 isnan -9E+2 -> 0
bool0760 isnan 9E+3 -> 0
bool0761 isnan -9E+3 -> 0
bool0762 isnan 9E+6 -> 0
bool0763 isnan -9E+6 -> 0
bool0764 isnan 9E+100 -> 0
bool0765 isnan -9E+100 -> 0
bool0766 isnan 9E+990 -> 0
bool0767 isnan -9E+990 -> 0
bool0768 isnan 9E+991 -> 0
bool0769 isnan -9E+991 -> 0
bool0770 isnan 9E+992 -> 0
bool0771 isnan -9E+992 -> 0
bool0772 isnan 9E+998 -> 0
bool0773 isnan -9E+998 -> 0
bool0774 isnan 9E+999 -> 0
bool0775 isnan -9E+999 -> 0
bool0776 isnan 9E+1000 -> 0
bool0777 isnan -9E+1000 -> 0
bool0778 isnan 9E+2000 -> 0
bool0779 isnan -9E+2000 -> 0
bool0780 isnan 9.99999999E-2000 -> 0
bool0781 isnan -9.99999999E-2000 -> 0
bool0782 isnan 9.99999999E-1008 -> 0
bool0783 isnan -9.99999999E-1008 -> 0
bool0784 isnan 9.99999999E-1007 -> 0
bool0785 isnan -9.99999999E-1007 -> 0
bool0786 isnan 9.99999999E-1006 -> 0
bool0787 isnan -9.99999999E-1006 -> 0
bool0788 isnan 9.99999999E-1000 -> 0
bool0789 isnan -9.99999999E-1000 -> 0
bool0790 isnan 9.99999999E-999 -> 0
bool0791 isnan -9.99999999E-999 -> 0
bool0792 isnan 9.99999999E-998 -> 0
bool0793 isnan -9.99999999E-998 -> 0
bool0794 isnan 9.99999999E-100 -> 0
bool0795 isnan -9.99999999E-100 -> 0
bool0796 isnan 0.00000999999999 -> 0
bool0797 isnan -0.00000999999999 -> 0
bool0798 isnan 0.00999999999 -> 0
bool0799 isnan -0.00999999999 -> 0
bool0800 isnan 0.0999999999 -> 0
bool0801 isnan -0.0999999999 -> 0
bool0802 isnan 0.999999999 -> 0
bool0803 isnan -0.999999999 -> 0
bool0804 isnan 9.99999999 -> 0
bool0805 isnan -9.99999999 -> 0
bool0806 isnan 99.9999999 -> 0
bool0807 isnan -99.9999999 -> 0
bool0808 isnan 999.999999 -> 0
bool0809 isnan -999.999999 -> 0
bool0810 isnan 9999.99999 -> 0
bool0811 isnan -9999.99999 -> 0
bool0812 isnan 9999999.99 -> 0
bool0813 isnan -9999999.99 -> 0
bool0814 isnan 9.99999999E+100 -> 0
bool0815 isnan -9.99999999E+100 -> 0
bool0816 isnan 9.99999999E+990 -> 0
bool0817 isnan -9.99999999E+990 -> 0
bool0818 isnan 9.99999999E+991 -> 0
bool0819 isnan -9.99999999E+991 -> 0
bool0820 isnan 9.99999999E+992 -> 0
bool0821 isnan -9.99999999E+992 -> 0
bool0822 isnan 9.99999999E+998 -> 0
bool0823 isnan -9.99999999E+998 -> 0
bool0824 isnan 9.99999999E+999 -> 0
bool0825 isnan -9.99999999E+999 -> 0
bool0826 isnan 9.99999999E+1000 -> 0
bool0827 isnan -9.99999999E+1000 -> 0
bool0828 isnan 9.99999999E+2000 -> 0
bool0829 isnan -9.99999999E+2000 -> 0
bool0830 isnan Infinity -> 0
bool0831 isnan -Infinity -> 0
bool0832 isnan NaN -> 1
bool0833 isnan -NaN -> 1
bool0834 isnan NaN123 -> 1
bool0835 isnan -NaN123 -> 1
bool0836 isnan sNaN -> 1
bool0837 isnan -sNaN -> 1
bool0838 isnan sNaN123 -> 1
bool0839 isnan -sNaN123 -> 1
bool0840 isnormal 0E-2000 -> 0
bool0841 isnormal -0E-2000 -> 0
bool0842 isnormal 0E-1008 -> 0
bool0843 isnormal -0E-1008 -> 0
bool0844 isnormal 0E-1007 -> 0
bool0845 isnormal -0E-1007 -> 0
bool0846 isnormal 0E-1006 -> 0
bool0847 isnormal -0E-1006 -> 0
bool0848 isnormal 0E-1000 -> 0
bool0849 isnormal -0E-1000 -> 0
bool0850 isnormal 0E-999 -> 0
bool0851 isnormal -0E-999 -> 0
bool0852 isnormal 0E-998 -> 0
bool0853 isnormal -0E-998 -> 0
bool0854 isnormal 0E-100 -> 0
bool0855 isnormal -0E-100 -> 0
bool0856 isnormal 0.000000 -> 0
bool0857 isnormal -0.000000 -> 0
bool0858 isnormal 0.000 -> 0
bool0859 isnormal -0.000 -> 0
bool0860 isnormal 0.00 -> 0
bool0861 isnormal -0.00 -> 0
bool0862 isnormal 0.0 -> 0
bool0863 isnormal -0.0 -> 0
bool0864 isnormal 0 -> 0
bool0865 isnormal -0 -> 0
bool0866 isnormal 0E+1 -> 0
bool0867 isnormal -0E+1 -> 0
bool0868 isnormal 0E+2 -> 0
bool0869 isnormal -0E+2 -> 0
bool0870 isnormal 0E+3 -> 0
bool0871 isnormal -0E+3 -> 0
bool0872 isnormal 0E+6 -> 0
bool0873 isnormal -0E+6 -> 0
bool0874 isnormal 0E+100 -> 0
bool0875 isnormal -0E+100 -> 0
bool0876 isnormal 0E+990 -> 0
bool0877 isnormal -0E+990 -> 0
bool0878 isnormal 0E+991 -> 0
bool0879 isnormal -0E+991 -> 0
bool0880 isnormal 0E+992 -> 0
bool0881 isnormal -0E+992 -> 0
bool0882 isnormal 0E+998 -> 0
bool0883 isnormal -0E+998 -> 0
bool0884 isnormal 0E+999 -> 0
bool0885 isnormal -0E+999 -> 0
bool0886 isnormal 0E+1000 -> 0
bool0887 isnormal -0E+1000 -> 0
bool0888 isnormal 0E+2000 -> 0
bool0889 isnormal -0E+2000 -> 0
bool0890 isnormal 1E-2000 -> 0
bool0891 isnormal -1E-2000 -> 0
bool0892 isnormal 1E-1008 -> 0
bool0893 isnormal -1E-1008 -> 0
bool0894 isnormal 1E-1007 -> 0
bool0895 isnormal -1E-1007 -> 0
bool0896 isnormal 1E-1006 -> 0
bool0897 isnormal -1E-1006 -> 0
bool0898 isnormal 1E-1000 -> 0
bool0899 isnormal -1E-1000 -> 0
bool0900 isnormal 1E-999 -> 1
bool0901 isnormal -1E-999 -> 1
bool0902 isnormal 1E-998 -> 1
bool0903 isnormal -1E-998 -> 1
bool0904 isnormal 1E-100 -> 1
bool0905 isnormal -1E-100 -> 1
bool0906 isnormal 0.000001 -> 1
bool0907 isnormal -0.000001 -> 1
bool0908 isnormal 0.001 -> 1
bool0909 isnormal -0.001 -> 1
bool0910 isnormal 0.01 -> 1
bool0911 isnormal -0.01 -> 1
bool0912 isnormal 0.1 -> 1
bool0913 isnormal -0.1 -> 1
bool0914 isnormal 1 -> 1
bool0915 isnormal -1 -> 1
bool0916 isnormal 1E+1 -> 1
bool0917 isnormal -1E+1 -> 1
bool0918 isnormal 1E+2 -> 1
bool0919 isnormal -1E+2 -> 1
bool0920 isnormal 1E+3 -> 1
bool0921 isnormal -1E+3 -> 1
bool0922 isnormal 1E+6 -> 1
bool0923 isnormal -1E+6 -> 1
bool0924 isnormal 1E+100 -> 1
bool0925 isnormal -1E+100 -> 1
bool0926 isnormal 1E+990 -> 1
bool0927 isnormal -1E+990 -> 1
bool0928 isnormal 1E+991 -> 1
bool0929 isnormal -1E+991 -> 1
bool0930 isnormal 1E+992 -> 1
bool0931 isnormal -1E+992 -> 1
bool0932 isnormal 1E+998 -> 1
bool0933 isnormal -1E+998 -> 1
bool0934 isnormal 1E+999 -> 1
bool0935 isnormal -1E+999 -> 1
bool0936 isnormal 1E+1000 -> 1
bool0937 isnormal -1E+1000 -> 1
bool0938 isnormal 1E+2000 -> 1
bool0939 isnormal -1E+2000 -> 1
bool0940 isnormal 9E-2000 -> 0
bool0941 isnormal -9E-2000 -> 0
bool0942 isnormal 9E-1008 -> 0
bool0943 isnormal -9E-1008 -> 0
bool0944 isnormal 9E-1007 -> 0
bool0945 isnormal -9E-1007 -> 0
bool0946 isnormal 9E-1006 -> 0
bool0947 isnormal -9E-1006 -> 0
bool0948 isnormal 9E-1000 -> 0
bool0949 isnormal -9E-1000 -> 0
bool0950 isnormal 9E-999 -> 1
bool0951 isnormal -9E-999 -> 1
bool0952 isnormal 9E-998 -> 1
bool0953 isnormal -9E-998 -> 1
bool0954 isnormal 9E-100 -> 1
bool0955 isnormal -9E-100 -> 1
bool0956 isnormal 0.000009 -> 1
bool0957 isnormal -0.000009 -> 1
bool0958 isnormal 0.009 -> 1
bool0959 isnormal -0.009 -> 1
bool0960 isnormal 0.09 -> 1
bool0961 isnormal -0.09 -> 1
bool0962 isnormal 0.9 -> 1
bool0963 isnormal -0.9 -> 1
bool0964 isnormal 9 -> 1
bool0965 isnormal -9 -> 1
bool0966 isnormal 9E+1 -> 1
bool0967 isnormal -9E+1 -> 1
bool0968 isnormal 9E+2 -> 1
bool0969 isnormal -9E+2 -> 1
bool0970 isnormal 9E+3 -> 1
bool0971 isnormal -9E+3 -> 1
bool0972 isnormal 9E+6 -> 1
bool0973 isnormal -9E+6 -> 1
bool0974 isnormal 9E+100 -> 1
bool0975 isnormal -9E+100 -> 1
bool0976 isnormal 9E+990 -> 1
bool0977 isnormal -9E+990 -> 1
bool0978 isnormal 9E+991 -> 1
bool0979 isnormal -9E+991 -> 1
bool0980 isnormal 9E+992 -> 1
bool0981 isnormal -9E+992 -> 1
bool0982 isnormal 9E+998 -> 1
bool0983 isnormal -9E+998 -> 1
bool0984 isnormal 9E+999 -> 1
bool0985 isnormal -9E+999 -> 1
bool0986 isnormal 9E+1000 -> 1
bool0987 isnormal -9E+1000 -> 1
bool0988 isnormal 9E+2000 -> 1
bool0989 isnormal -9E+2000 -> 1
bool0990 isnormal 9.99999999E-2000 -> 0
bool0991 isnormal -9.99999999E-2000 -> 0
bool0992 isnormal 9.99999999E-1008 -> 0
bool0993 isnormal -9.99999999E-1008 -> 0
bool0994 isnormal 9.99999999E-1007 -> 0
bool0995 isnormal -9.99999999E-1007 -> 0
bool0996 isnormal 9.99999999E-1006 -> 0
bool0997 isnormal -9.99999999E-1006 -> 0
bool0998 isnormal 9.99999999E-1000 -> 0
bool0999 isnormal -9.99999999E-1000 -> 0
bool1000 isnormal 9.99999999E-999 -> 1
bool1001 isnormal -9.99999999E-999 -> 1
bool1002 isnormal 9.99999999E-998 -> 1
bool1003 isnormal -9.99999999E-998 -> 1
bool1004 isnormal 9.99999999E-100 -> 1
bool1005 isnormal -9.99999999E-100 -> 1
bool1006 isnormal 0.00000999999999 -> 1
bool1007 isnormal -0.00000999999999 -> 1
bool1008 isnormal 0.00999999999 -> 1
bool1009 isnormal -0.00999999999 -> 1
bool1010 isnormal 0.0999999999 -> 1
bool1011 isnormal -0.0999999999 -> 1
bool1012 isnormal 0.999999999 -> 1
bool1013 isnormal -0.999999999 -> 1
bool1014 isnormal 9.99999999 -> 1
bool1015 isnormal -9.99999999 -> 1
bool1016 isnormal 99.9999999 -> 1
bool1017 isnormal -99.9999999 -> 1
bool1018 isnormal 999.999999 -> 1
bool1019 isnormal -999.999999 -> 1
bool1020 isnormal 9999.99999 -> 1
bool1021 isnormal -9999.99999 -> 1
bool1022 isnormal 9999999.99 -> 1
bool1023 isnormal -9999999.99 -> 1
bool1024 isnormal 9.99999999E+100 -> 1
bool1025 isnormal -9.99999999E+100 -> 1
bool1026 isnormal 9.99999999E+990 -> 1
bool1027 isnormal -9.99999999E+990 -> 1
bool1028 isnormal 9.99999999E+991 -> 1
bool1029 isnormal -9.99999999E+991 -> 1
bool1030 isnormal 9.99999999E+992 -> 1
bool1031 isnormal -9.99999999E+992 -> 1
bool1032 isnormal 9.99999999E+998 -> 1
bool1033 isnormal -9.99999999E+998 -> 1
bool1034 isnormal 9.99999999E+999 -> 1
bool1035 isnormal -9.99999999E+999 -> 1
bool1036 isnormal 9.99999999E+1000 -> 1
bool1037 isnormal -9.99999999E+1000 -> 1
bool1038 isnormal 9.99999999E+2000 -> 1
bool1039 isnormal -9.99999999E+2000 -> 1
bool1040 isnormal Infinity -> 0
bool1041 isnormal -Infinity -> 0
bool1042 isnormal NaN -> 0
bool1043 isnormal -NaN -> 0
bool1044 isnormal NaN123 -> 0
bool1045 isnormal -NaN123 -> 0
bool1046 isnormal sNaN -> 0
bool1047 isnormal -sNaN -> 0
bool1048 isnormal sNaN123 -> 0
bool1049 isnormal -sNaN123 -> 0
bool1050 isqnan 0E-2000 -> 0
bool1051 isqnan -0E-2000 -> 0
bool1052 isqnan 0E-1008 -> 0
bool1053 isqnan -0E-1008 -> 0
bool1054 isqnan 0E-1007 -> 0
bool1055 isqnan -0E-1007 -> 0
bool1056 isqnan 0E-1006 -> 0
bool1057 isqnan -0E-1006 -> 0
bool1058 isqnan 0E-1000 -> 0
bool1059 isqnan -0E-1000 -> 0
bool1060 isqnan 0E-999 -> 0
bool1061 isqnan -0E-999 -> 0
bool1062 isqnan 0E-998 -> 0
bool1063 isqnan -0E-998 -> 0
bool1064 isqnan 0E-100 -> 0
bool1065 isqnan -0E-100 -> 0
bool1066 isqnan 0.000000 -> 0
bool1067 isqnan -0.000000 -> 0
bool1068 isqnan 0.000 -> 0
bool1069 isqnan -0.000 -> 0
bool1070 isqnan 0.00 -> 0
bool1071 isqnan -0.00 -> 0
bool1072 isqnan 0.0 -> 0
bool1073 isqnan -0.0 -> 0
bool1074 isqnan 0 -> 0
bool1075 isqnan -0 -> 0
bool1076 isqnan 0E+1 -> 0
bool1077 isqnan -0E+1 -> 0
bool1078 isqnan 0E+2 -> 0
bool1079 isqnan -0E+2 -> 0
bool1080 isqnan 0E+3 -> 0
bool1081 isqnan -0E+3 -> 0
bool1082 isqnan 0E+6 -> 0
bool1083 isqnan -0E+6 -> 0
bool1084 isqnan 0E+100 -> 0
bool1085 isqnan -0E+100 -> 0
bool1086 isqnan 0E+990 -> 0
bool1087 isqnan -0E+990 -> 0
bool1088 isqnan 0E+991 -> 0
bool1089 isqnan -0E+991 -> 0
bool1090 isqnan 0E+992 -> 0
bool1091 isqnan -0E+992 -> 0
bool1092 isqnan 0E+998 -> 0
bool1093 isqnan -0E+998 -> 0
bool1094 isqnan 0E+999 -> 0
bool1095 isqnan -0E+999 -> 0
bool1096 isqnan 0E+1000 -> 0
bool1097 isqnan -0E+1000 -> 0
bool1098 isqnan 0E+2000 -> 0
bool1099 isqnan -0E+2000 -> 0
bool1100 isqnan 1E-2000 -> 0
bool1101 isqnan -1E-2000 -> 0
bool1102 isqnan 1E-1008 -> 0
bool1103 isqnan -1E-1008 -> 0
bool1104 isqnan 1E-1007 -> 0
bool1105 isqnan -1E-1007 -> 0
bool1106 isqnan 1E-1006 -> 0
bool1107 isqnan -1E-1006 -> 0
bool1108 isqnan 1E-1000 -> 0
bool1109 isqnan -1E-1000 -> 0
bool1110 isqnan 1E-999 -> 0
bool1111 isqnan -1E-999 -> 0
bool1112 isqnan 1E-998 -> 0
bool1113 isqnan -1E-998 -> 0
bool1114 isqnan 1E-100 -> 0
bool1115 isqnan -1E-100 -> 0
bool1116 isqnan 0.000001 -> 0
bool1117 isqnan -0.000001 -> 0
bool1118 isqnan 0.001 -> 0
bool1119 isqnan -0.001 -> 0
bool1120 isqnan 0.01 -> 0
bool1121 isqnan -0.01 -> 0
bool1122 isqnan 0.1 -> 0
bool1123 isqnan -0.1 -> 0
bool1124 isqnan 1 -> 0
bool1125 isqnan -1 -> 0
bool1126 isqnan 1E+1 -> 0
bool1127 isqnan -1E+1 -> 0
bool1128 isqnan 1E+2 -> 0
bool1129 isqnan -1E+2 -> 0
bool1130 isqnan 1E+3 -> 0
bool1131 isqnan -1E+3 -> 0
bool1132 isqnan 1E+6 -> 0
bool1133 isqnan -1E+6 -> 0
bool1134 isqnan 1E+100 -> 0
bool1135 isqnan -1E+100 -> 0
bool1136 isqnan 1E+990 -> 0
bool1137 isqnan -1E+990 -> 0
bool1138 isqnan 1E+991 -> 0
bool1139 isqnan -1E+991 -> 0
bool1140 isqnan 1E+992 -> 0
bool1141 isqnan -1E+992 -> 0
bool1142 isqnan 1E+998 -> 0
bool1143 isqnan -1E+998 -> 0
bool1144 isqnan 1E+999 -> 0
bool1145 isqnan -1E+999 -> 0
bool1146 isqnan 1E+1000 -> 0
bool1147 isqnan -1E+1000 -> 0
bool1148 isqnan 1E+2000 -> 0
bool1149 isqnan -1E+2000 -> 0
bool1150 isqnan 9E-2000 -> 0
bool1151 isqnan -9E-2000 -> 0
bool1152 isqnan 9E-1008 -> 0
bool1153 isqnan -9E-1008 -> 0
bool1154 isqnan 9E-1007 -> 0
bool1155 isqnan -9E-1007 -> 0
bool1156 isqnan 9E-1006 -> 0
bool1157 isqnan -9E-1006 -> 0
bool1158 isqnan 9E-1000 -> 0
bool1159 isqnan -9E-1000 -> 0
bool1160 isqnan 9E-999 -> 0
bool1161 isqnan -9E-999 -> 0
bool1162 isqnan 9E-998 -> 0
bool1163 isqnan -9E-998 -> 0
bool1164 isqnan 9E-100 -> 0
bool1165 isqnan -9E-100 -> 0
bool1166 isqnan 0.000009 -> 0
bool1167 isqnan -0.000009 -> 0
bool1168 isqnan 0.009 -> 0
bool1169 isqnan -0.009 -> 0
bool1170 isqnan 0.09 -> 0
bool1171 isqnan -0.09 -> 0
bool1172 isqnan 0.9 -> 0
bool1173 isqnan -0.9 -> 0
bool1174 isqnan 9 -> 0
bool1175 isqnan -9 -> 0
bool1176 isqnan 9E+1 -> 0
bool1177 isqnan -9E+1 -> 0
bool1178 isqnan 9E+2 -> 0
bool1179 isqnan -9E+2 -> 0
bool1180 isqnan 9E+3 -> 0
bool1181 isqnan -9E+3 -> 0
bool1182 isqnan 9E+6 -> 0
bool1183 isqnan -9E+6 -> 0
bool1184 isqnan 9E+100 -> 0
bool1185 isqnan -9E+100 -> 0
bool1186 isqnan 9E+990 -> 0
bool1187 isqnan -9E+990 -> 0
bool1188 isqnan 9E+991 -> 0
bool1189 isqnan -9E+991 -> 0
bool1190 isqnan 9E+992 -> 0
bool1191 isqnan -9E+992 -> 0
bool1192 isqnan 9E+998 -> 0
bool1193 isqnan -9E+998 -> 0
bool1194 isqnan 9E+999 -> 0
bool1195 isqnan -9E+999 -> 0
bool1196 isqnan 9E+1000 -> 0
bool1197 isqnan -9E+1000 -> 0
bool1198 isqnan 9E+2000 -> 0
bool1199 isqnan -9E+2000 -> 0
bool1200 isqnan 9.99999999E-2000 -> 0
bool1201 isqnan -9.99999999E-2000 -> 0
bool1202 isqnan 9.99999999E-1008 -> 0
bool1203 isqnan -9.99999999E-1008 -> 0
bool1204 isqnan 9.99999999E-1007 -> 0
bool1205 isqnan -9.99999999E-1007 -> 0
bool1206 isqnan 9.99999999E-1006 -> 0
bool1207 isqnan -9.99999999E-1006 -> 0
bool1208 isqnan 9.99999999E-1000 -> 0
bool1209 isqnan -9.99999999E-1000 -> 0
bool1210 isqnan 9.99999999E-999 -> 0
bool1211 isqnan -9.99999999E-999 -> 0
bool1212 isqnan 9.99999999E-998 -> 0
bool1213 isqnan -9.99999999E-998 -> 0
bool1214 isqnan 9.99999999E-100 -> 0
bool1215 isqnan -9.99999999E-100 -> 0
bool1216 isqnan 0.00000999999999 -> 0
bool1217 isqnan -0.00000999999999 -> 0
bool1218 isqnan 0.00999999999 -> 0
bool1219 isqnan -0.00999999999 -> 0
bool1220 isqnan 0.0999999999 -> 0
bool1221 isqnan -0.0999999999 -> 0
bool1222 isqnan 0.999999999 -> 0
bool1223 isqnan -0.999999999 -> 0
bool1224 isqnan 9.99999999 -> 0
bool1225 isqnan -9.99999999 -> 0
bool1226 isqnan 99.9999999 -> 0
bool1227 isqnan -99.9999999 -> 0
bool1228 isqnan 999.999999 -> 0
bool1229 isqnan -999.999999 -> 0
bool1230 isqnan 9999.99999 -> 0
bool1231 isqnan -9999.99999 -> 0
bool1232 isqnan 9999999.99 -> 0
bool1233 isqnan -9999999.99 -> 0
bool1234 isqnan 9.99999999E+100 -> 0
bool1235 isqnan -9.99999999E+100 -> 0
bool1236 isqnan 9.99999999E+990 -> 0
bool1237 isqnan -9.99999999E+990 -> 0
bool1238 isqnan 9.99999999E+991 -> 0
bool1239 isqnan -9.99999999E+991 -> 0
bool1240 isqnan 9.99999999E+992 -> 0
bool1241 isqnan -9.99999999E+992 -> 0
bool1242 isqnan 9.99999999E+998 -> 0
bool1243 isqnan -9.99999999E+998 -> 0
bool1244 isqnan 9.99999999E+999 -> 0
bool1245 isqnan -9.99999999E+999 -> 0
bool1246 isqnan 9.99999999E+1000 -> 0
bool1247 isqnan -9.99999999E+1000 -> 0
bool1248 isqnan 9.99999999E+2000 -> 0
bool1249 isqnan -9.99999999E+2000 -> 0
bool1250 isqnan Infinity -> 0
bool1251 isqnan -Infinity -> 0
bool1252 isqnan NaN -> 1
bool1253 isqnan -NaN -> 1
bool1254 isqnan NaN123 -> 1
bool1255 isqnan -NaN123 -> 1
bool1256 isqnan sNaN -> 0
bool1257 isqnan -sNaN -> 0
bool1258 isqnan sNaN123 -> 0
bool1259 isqnan -sNaN123 -> 0
bool1260 issigned 0E-2000 -> 0
bool1261 issigned -0E-2000 -> 1
bool1262 issigned 0E-1008 -> 0
bool1263 issigned -0E-1008 -> 1
bool1264 issigned 0E-1007 -> 0
bool1265 issigned -0E-1007 -> 1
bool1266 issigned 0E-1006 -> 0
bool1267 issigned -0E-1006 -> 1
bool1268 issigned 0E-1000 -> 0
bool1269 issigned -0E-1000 -> 1
bool1270 issigned 0E-999 -> 0
bool1271 issigned -0E-999 -> 1
bool1272 issigned 0E-998 -> 0
bool1273 issigned -0E-998 -> 1
bool1274 issigned 0E-100 -> 0
bool1275 issigned -0E-100 -> 1
bool1276 issigned 0.000000 -> 0
bool1277 issigned -0.000000 -> 1
bool1278 issigned 0.000 -> 0
bool1279 issigned -0.000 -> 1
bool1280 issigned 0.00 -> 0
bool1281 issigned -0.00 -> 1
bool1282 issigned 0.0 -> 0
bool1283 issigned -0.0 -> 1
bool1284 issigned 0 -> 0
bool1285 issigned -0 -> 1
bool1286 issigned 0E+1 -> 0
bool1287 issigned -0E+1 -> 1
bool1288 issigned 0E+2 -> 0
bool1289 issigned -0E+2 -> 1
bool1290 issigned 0E+3 -> 0
bool1291 issigned -0E+3 -> 1
bool1292 issigned 0E+6 -> 0
bool1293 issigned -0E+6 -> 1
bool1294 issigned 0E+100 -> 0
bool1295 issigned -0E+100 -> 1
bool1296 issigned 0E+990 -> 0
bool1297 issigned -0E+990 -> 1
bool1298 issigned 0E+991 -> 0
bool1299 issigned -0E+991 -> 1
bool1300 issigned 0E+992 -> 0
bool1301 issigned -0E+992 -> 1
bool1302 issigned 0E+998 -> 0
bool1303 issigned -0E+998 -> 1
bool1304 issigned 0E+999 -> 0
bool1305 issigned -0E+999 -> 1
bool1306 issigned 0E+1000 -> 0
bool1307 issigned -0E+1000 -> 1
bool1308 issigned 0E+2000 -> 0
bool1309 issigned -0E+2000 -> 1
bool1310 issigned 1E-2000 -> 0
bool1311 issigned -1E-2000 -> 1
bool1312 issigned 1E-1008 -> 0
bool1313 issigned -1E-1008 -> 1
bool1314 issigned 1E-1007 -> 0
bool1315 issigned -1E-1007 -> 1
bool1316 issigned 1E-1006 -> 0
bool1317 issigned -1E-1006 -> 1
bool1318 issigned 1E-1000 -> 0
bool1319 issigned -1E-1000 -> 1
bool1320 issigned 1E-999 -> 0
bool1321 issigned -1E-999 -> 1
bool1322 issigned 1E-998 -> 0
bool1323 issigned -1E-998 -> 1
bool1324 issigned 1E-100 -> 0
bool1325 issigned -1E-100 -> 1
bool1326 issigned 0.000001 -> 0
bool1327 issigned -0.000001 -> 1
bool1328 issigned 0.001 -> 0
bool1329 issigned -0.001 -> 1
bool1330 issigned 0.01 -> 0
bool1331 issigned -0.01 -> 1
bool1332 issigned 0.1 -> 0
bool1333 issigned -0.1 -> 1
bool1334 issigned 1 -> 0
bool1335 issigned -1 -> 1
bool1336 issigned 1E+1 -> 0
bool1337 issigned -1E+1 -> 1
bool1338 issigned 1E+2 -> 0
bool1339 issigned -1E+2 -> 1
bool1340 issigned 1E+3 -> 0
bool1341 issigned -1E+3 -> 1
bool1342 issigned 1E+6 -> 0
bool1343 issigned -1E+6 -> 1
bool1344 issigned 1E+100 -> 0
bool1345 issigned -1E+100 -> 1
bool1346 issigned 1E+990 -> 0
bool1347 issigned -1E+990 -> 1
bool1348 issigned 1E+991 -> 0
bool1349 issigned -1E+991 -> 1
bool1350 issigned 1E+992 -> 0
bool1351 issigned -1E+992 -> 1
bool1352 issigned 1E+998 -> 0
bool1353 issigned -1E+998 -> 1
bool1354 issigned 1E+999 -> 0
bool1355 issigned -1E+999 -> 1
bool1356 issigned 1E+1000 -> 0
bool1357 issigned -1E+1000 -> 1
bool1358 issigned 1E+2000 -> 0
bool1359 issigned -1E+2000 -> 1
bool1360 issigned 9E-2000 -> 0
bool1361 issigned -9E-2000 -> 1
bool1362 issigned 9E-1008 -> 0
bool1363 issigned -9E-1008 -> 1
bool1364 issigned 9E-1007 -> 0
bool1365 issigned -9E-1007 -> 1
bool1366 issigned 9E-1006 -> 0
bool1367 issigned -9E-1006 -> 1
bool1368 issigned 9E-1000 -> 0
bool1369 issigned -9E-1000 -> 1
bool1370 issigned 9E-999 -> 0
bool1371 issigned -9E-999 -> 1
bool1372 issigned 9E-998 -> 0
bool1373 issigned -9E-998 -> 1
bool1374 issigned 9E-100 -> 0
bool1375 issigned -9E-100 -> 1
bool1376 issigned 0.000009 -> 0
bool1377 issigned -0.000009 -> 1
bool1378 issigned 0.009 -> 0
bool1379 issigned -0.009 -> 1
bool1380 issigned 0.09 -> 0
bool1381 issigned -0.09 -> 1
bool1382 issigned 0.9 -> 0
bool1383 issigned -0.9 -> 1
bool1384 issigned 9 -> 0
bool1385 issigned -9 -> 1
bool1386 issigned 9E+1 -> 0
bool1387 issigned -9E+1 -> 1
bool1388 issigned 9E+2 -> 0
bool1389 issigned -9E+2 -> 1
bool1390 issigned 9E+3 -> 0
bool1391 issigned -9E+3 -> 1
bool1392 issigned 9E+6 -> 0
bool1393 issigned -9E+6 -> 1
bool1394 issigned 9E+100 -> 0
bool1395 issigned -9E+100 -> 1
bool1396 issigned 9E+990 -> 0
bool1397 issigned -9E+990 -> 1
bool1398 issigned 9E+991 -> 0
bool1399 issigned -9E+991 -> 1
bool1400 issigned 9E+992 -> 0
bool1401 issigned -9E+992 -> 1
bool1402 issigned 9E+998 -> 0
bool1403 issigned -9E+998 -> 1
bool1404 issigned 9E+999 -> 0
bool1405 issigned -9E+999 -> 1
bool1406 issigned 9E+1000 -> 0
bool1407 issigned -9E+1000 -> 1
bool1408 issigned 9E+2000 -> 0
bool1409 issigned -9E+2000 -> 1
bool1410 issigned 9.99999999E-2000 -> 0
bool1411 issigned -9.99999999E-2000 -> 1
bool1412 issigned 9.99999999E-1008 -> 0
bool1413 issigned -9.99999999E-1008 -> 1
bool1414 issigned 9.99999999E-1007 -> 0
bool1415 issigned -9.99999999E-1007 -> 1
bool1416 issigned 9.99999999E-1006 -> 0
bool1417 issigned -9.99999999E-1006 -> 1
bool1418 issigned 9.99999999E-1000 -> 0
bool1419 issigned -9.99999999E-1000 -> 1
bool1420 issigned 9.99999999E-999 -> 0
bool1421 issigned -9.99999999E-999 -> 1
bool1422 issigned 9.99999999E-998 -> 0
bool1423 issigned -9.99999999E-998 -> 1
bool1424 issigned 9.99999999E-100 -> 0
bool1425 issigned -9.99999999E-100 -> 1
bool1426 issigned 0.00000999999999 -> 0
bool1427 issigned -0.00000999999999 -> 1
bool1428 issigned 0.00999999999 -> 0
bool1429 issigned -0.00999999999 -> 1
bool1430 issigned 0.0999999999 -> 0
bool1431 issigned -0.0999999999 -> 1
bool1432 issigned 0.999999999 -> 0
bool1433 issigned -0.999999999 -> 1
bool1434 issigned 9.99999999 -> 0
bool1435 issigned -9.99999999 -> 1
bool1436 issigned 99.9999999 -> 0
bool1437 issigned -99.9999999 -> 1
bool1438 issigned 999.999999 -> 0
bool1439 issigned -999.999999 -> 1
bool1440 issigned 9999.99999 -> 0
bool1441 issigned -9999.99999 -> 1
bool1442 issigned 9999999.99 -> 0
bool1443 issigned -9999999.99 -> 1
bool1444 issigned 9.99999999E+100 -> 0
bool1445 issigned -9.99999999E+100 -> 1
bool1446 issigned 9.99999999E+990 -> 0
bool1447 issigned -9.99999999E+990 -> 1
bool1448 issigned 9.99999999E+991 -> 0
bool1449 issigned -9.99999999E+991 -> 1
bool1450 issigned 9.99999999E+992 -> 0
bool1451 issigned -9.99999999E+992 -> 1
bool1452 issigned 9.99999999E+998 -> 0
bool1453 issigned -9.99999999E+998 -> 1
bool1454 issigned 9.99999999E+999 -> 0
bool1455 issigned -9.99999999E+999 -> 1
bool1456 issigned 9.99999999E+1000 -> 0
bool1457 issigned -9.99999999E+1000 -> 1
bool1458 issigned 9.99999999E+2000 -> 0
bool1459 issigned -9.99999999E+2000 -> 1
bool1460 issigned Infinity -> 0
bool1461 issigned -Infinity -> 1
bool1462 issigned NaN -> 0
bool1463 issigned -NaN -> 1
bool1464 issigned NaN123 -> 0
bool1465 issigned -NaN123 -> 1
bool1466 issigned sNaN -> 0
bool1467 issigned -sNaN -> 1
bool1468 issigned sNaN123 -> 0
bool1469 issigned -sNaN123 -> 1
bool1470 issnan 0E-2000 -> 0
bool1471 issnan -0E-2000 -> 0
bool1472 issnan 0E-1008 -> 0
bool1473 issnan -0E-1008 -> 0
bool1474 issnan 0E-1007 -> 0
bool1475 issnan -0E-1007 -> 0
bool1476 issnan 0E-1006 -> 0
bool1477 issnan -0E-1006 -> 0
bool1478 issnan 0E-1000 -> 0
bool1479 issnan -0E-1000 -> 0
bool1480 issnan 0E-999 -> 0
bool1481 issnan -0E-999 -> 0
bool1482 issnan 0E-998 -> 0
bool1483 issnan -0E-998 -> 0
bool1484 issnan 0E-100 -> 0
bool1485 issnan -0E-100 -> 0
bool1486 issnan 0.000000 -> 0
bool1487 issnan -0.000000 -> 0
bool1488 issnan 0.000 -> 0
bool1489 issnan -0.000 -> 0
bool1490 issnan 0.00 -> 0
bool1491 issnan -0.00 -> 0
bool1492 issnan 0.0 -> 0
bool1493 issnan -0.0 -> 0
bool1494 issnan 0 -> 0
bool1495 issnan -0 -> 0
bool1496 issnan 0E+1 -> 0
bool1497 issnan -0E+1 -> 0
bool1498 issnan 0E+2 -> 0
bool1499 issnan -0E+2 -> 0
bool1500 issnan 0E+3 -> 0
bool1501 issnan -0E+3 -> 0
bool1502 issnan 0E+6 -> 0
bool1503 issnan -0E+6 -> 0
bool1504 issnan 0E+100 -> 0
bool1505 issnan -0E+100 -> 0
bool1506 issnan 0E+990 -> 0
bool1507 issnan -0E+990 -> 0
bool1508 issnan 0E+991 -> 0
bool1509 issnan -0E+991 -> 0
bool1510 issnan 0E+992 -> 0
bool1511 issnan -0E+992 -> 0
bool1512 issnan 0E+998 -> 0
bool1513 issnan -0E+998 -> 0
bool1514 issnan 0E+999 -> 0
bool1515 issnan -0E+999 -> 0
bool1516 issnan 0E+1000 -> 0
bool1517 issnan -0E+1000 -> 0
bool1518 issnan 0E+2000 -> 0
bool1519 issnan -0E+2000 -> 0
bool1520 issnan 1E-2000 -> 0
bool1521 issnan -1E-2000 -> 0
bool1522 issnan 1E-1008 -> 0
bool1523 issnan -1E-1008 -> 0
bool1524 issnan 1E-1007 -> 0
bool1525 issnan -1E-1007 -> 0
bool1526 issnan 1E-1006 -> 0
bool1527 issnan -1E-1006 -> 0
bool1528 issnan 1E-1000 -> 0
bool1529 issnan -1E-1000 -> 0
bool1530 issnan 1E-999 -> 0
bool1531 issnan -1E-999 -> 0
bool1532 issnan 1E-998 -> 0
bool1533 issnan -1E-998 -> 0
bool1534 issnan 1E-100 -> 0
bool1535 issnan -1E-100 -> 0
bool1536 issnan 0.000001 -> 0
bool1537 issnan -0.000001 -> 0
bool1538 issnan 0.001 -> 0
bool1539 issnan -0.001 -> 0
bool1540 issnan 0.01 -> 0
bool1541 issnan -0.01 -> 0
bool1542 issnan 0.1 -> 0
bool1543 issnan -0.1 -> 0
bool1544 issnan 1 -> 0
bool1545 issnan -1 -> 0
bool1546 issnan 1E+1 -> 0
bool1547 issnan -1E+1 -> 0
bool1548 issnan 1E+2 -> 0
bool1549 issnan -1E+2 -> 0
bool1550 issnan 1E+3 -> 0
bool1551 issnan -1E+3 -> 0
bool1552 issnan 1E+6 -> 0
bool1553 issnan -1E+6 -> 0
bool1554 issnan 1E+100 -> 0
bool1555 issnan -1E+100 -> 0
bool1556 issnan 1E+990 -> 0
bool1557 issnan -1E+990 -> 0
bool1558 issnan 1E+991 -> 0
bool1559 issnan -1E+991 -> 0
bool1560 issnan 1E+992 -> 0
bool1561 issnan -1E+992 -> 0
bool1562 issnan 1E+998 -> 0
bool1563 issnan -1E+998 -> 0
bool1564 issnan 1E+999 -> 0
bool1565 issnan -1E+999 -> 0
bool1566 issnan 1E+1000 -> 0
bool1567 issnan -1E+1000 -> 0
bool1568 issnan 1E+2000 -> 0
bool1569 issnan -1E+2000 -> 0
bool1570 issnan 9E-2000 -> 0
bool1571 issnan -9E-2000 -> 0
bool1572 issnan 9E-1008 -> 0
bool1573 issnan -9E-1008 -> 0
bool1574 issnan 9E-1007 -> 0
bool1575 issnan -9E-1007 -> 0
bool1576 issnan 9E-1006 -> 0
bool1577 issnan -9E-1006 -> 0
bool1578 issnan 9E-1000 -> 0
bool1579 issnan -9E-1000 -> 0
bool1580 issnan 9E-999 -> 0
bool1581 issnan -9E-999 -> 0
bool1582 issnan 9E-998 -> 0
bool1583 issnan -9E-998 -> 0
bool1584 issnan 9E-100 -> 0
bool1585 issnan -9E-100 -> 0
bool1586 issnan 0.000009 -> 0
bool1587 issnan -0.000009 -> 0
bool1588 issnan 0.009 -> 0
bool1589 issnan -0.009 -> 0
bool1590 issnan 0.09 -> 0
bool1591 issnan -0.09 -> 0
bool1592 issnan 0.9 -> 0
bool1593 issnan -0.9 -> 0
bool1594 issnan 9 -> 0
bool1595 issnan -9 -> 0
bool1596 issnan 9E+1 -> 0
bool1597 issnan -9E+1 -> 0
bool1598 issnan 9E+2 -> 0
bool1599 issnan -9E+2 -> 0
bool1600 issnan 9E+3 -> 0
bool1601 issnan -9E+3 -> 0
bool1602 issnan 9E+6 -> 0
bool1603 issnan -9E+6 -> 0
bool1604 issnan 9E+100 -> 0
bool1605 issnan -9E+100 -> 0
bool1606 issnan 9E+990 -> 0
bool1607 issnan -9E+990 -> 0
bool1608 issnan 9E+991 -> 0
bool1609 issnan -9E+991 -> 0
bool1610 issnan 9E+992 -> 0
bool1611 issnan -9E+992 -> 0
bool1612 issnan 9E+998 -> 0
bool1613 issnan -9E+998 -> 0
bool1614 issnan 9E+999 -> 0
bool1615 issnan -9E+999 -> 0
bool1616 issnan 9E+1000 -> 0
bool1617 issnan -9E+1000 -> 0
bool1618 issnan 9E+2000 -> 0
bool1619 issnan -9E+2000 -> 0
bool1620 issnan 9.99999999E-2000 -> 0
bool1621 issnan -9.99999999E-2000 -> 0
bool1622 issnan 9.99999999E-1008 -> 0
bool1623 issnan -9.99999999E-1008 -> 0
bool1624 issnan 9.99999999E-1007 -> 0
bool1625 issnan -9.99999999E-1007 -> 0
bool1626 issnan 9.99999999E-1006 -> 0
bool1627 issnan -9.99999999E-1006 -> 0
bool1628 issnan 9.99999999E-1000 -> 0
bool1629 issnan -9.99999999E-1000 -> 0
bool1630 issnan 9.99999999E-999 -> 0
bool1631 issnan -9.99999999E-999 -> 0
bool1632 issnan 9.99999999E-998 -> 0
bool1633 issnan -9.99999999E-998 -> 0
bool1634 issnan 9.99999999E-100 -> 0
bool1635 issnan -9.99999999E-100 -> 0
bool1636 issnan 0.00000999999999 -> 0
bool1637 issnan -0.00000999999999 -> 0
bool1638 issnan 0.00999999999 -> 0
bool1639 issnan -0.00999999999 -> 0
bool1640 issnan 0.0999999999 -> 0
bool1641 issnan -0.0999999999 -> 0
bool1642 issnan 0.999999999 -> 0
bool1643 issnan -0.999999999 -> 0
bool1644 issnan 9.99999999 -> 0
bool1645 issnan -9.99999999 -> 0
bool1646 issnan 99.9999999 -> 0
bool1647 issnan -99.9999999 -> 0
bool1648 issnan 999.999999 -> 0
bool1649 issnan -999.999999 -> 0
bool1650 issnan 9999.99999 -> 0
bool1651 issnan -9999.99999 -> 0
bool1652 issnan 9999999.99 -> 0
bool1653 issnan -9999999.99 -> 0
bool1654 issnan 9.99999999E+100 -> 0
bool1655 issnan -9.99999999E+100 -> 0
bool1656 issnan 9.99999999E+990 -> 0
bool1657 issnan -9.99999999E+990 -> 0
bool1658 issnan 9.99999999E+991 -> 0
bool1659 issnan -9.99999999E+991 -> 0
bool1660 issnan 9.99999999E+992 -> 0
bool1661 issnan -9.99999999E+992 -> 0
bool1662 issnan 9.99999999E+998 -> 0
bool1663 issnan -9.99999999E+998 -> 0
bool1664 issnan 9.99999999E+999 -> 0
bool1665 issnan -9.99999999E+999 -> 0
bool1666 issnan 9.99999999E+1000 -> 0
bool1667 issnan -9.99999999E+1000 -> 0
bool1668 issnan 9.99999999E+2000 -> 0
bool1669 issnan -9.99999999E+2000 -> 0
bool1670 issnan Infinity -> 0
bool1671 issnan -Infinity -> 0
bool1672 issnan NaN -> 0
bool1673 issnan -NaN -> 0
bool1674 issnan NaN123 -> 0
bool1675 issnan -NaN123 -> 0
bool1676 issnan sNaN -> 1
bool1677 issnan -sNaN -> 1
bool1678 issnan sNaN123 -> 1
bool1679 issnan -sNaN123 -> 1
bool1680 issubnormal 0E-2000 -> 0
bool1681 issubnormal -0E-2000 -> 0
bool1682 issubnormal 0E-1008 -> 0
bool1683 issubnormal -0E-1008 -> 0
bool1684 issubnormal 0E-1007 -> 0
bool1685 issubnormal -0E-1007 -> 0
bool1686 issubnormal 0E-1006 -> 0
bool1687 issubnormal -0E-1006 -> 0
bool1688 issubnormal 0E-1000 -> 0
bool1689 issubnormal -0E-1000 -> 0
bool1690 issubnormal 0E-999 -> 0
bool1691 issubnormal -0E-999 -> 0
bool1692 issubnormal 0E-998 -> 0
bool1693 issubnormal -0E-998 -> 0
bool1694 issubnormal 0E-100 -> 0
bool1695 issubnormal -0E-100 -> 0
bool1696 issubnormal 0.000000 -> 0
bool1697 issubnormal -0.000000 -> 0
bool1698 issubnormal 0.000 -> 0
bool1699 issubnormal -0.000 -> 0
bool1700 issubnormal 0.00 -> 0
bool1701 issubnormal -0.00 -> 0
bool1702 issubnormal 0.0 -> 0
bool1703 issubnormal -0.0 -> 0
bool1704 issubnormal 0 -> 0
bool1705 issubnormal -0 -> 0
bool1706 issubnormal 0E+1 -> 0
bool1707 issubnormal -0E+1 -> 0
bool1708 issubnormal 0E+2 -> 0
bool1709 issubnormal -0E+2 -> 0
bool1710 issubnormal 0E+3 -> 0
bool1711 issubnormal -0E+3 -> 0
bool1712 issubnormal 0E+6 -> 0
bool1713 issubnormal -0E+6 -> 0
bool1714 issubnormal 0E+100 -> 0
bool1715 issubnormal -0E+100 -> 0
bool1716 issubnormal 0E+990 -> 0
bool1717 issubnormal -0E+990 -> 0
bool1718 issubnormal 0E+991 -> 0
bool1719 issubnormal -0E+991 -> 0
bool1720 issubnormal 0E+992 -> 0
bool1721 issubnormal -0E+992 -> 0
bool1722 issubnormal 0E+998 -> 0
bool1723 issubnormal -0E+998 -> 0
bool1724 issubnormal 0E+999 -> 0
bool1725 issubnormal -0E+999 -> 0
bool1726 issubnormal 0E+1000 -> 0
bool1727 issubnormal -0E+1000 -> 0
bool1728 issubnormal 0E+2000 -> 0
bool1729 issubnormal -0E+2000 -> 0
bool1730 issubnormal 1E-2000 -> 1
bool1731 issubnormal -1E-2000 -> 1
bool1732 issubnormal 1E-1008 -> 1
bool1733 issubnormal -1E-1008 -> 1
bool1734 issubnormal 1E-1007 -> 1
bool1735 issubnormal -1E-1007 -> 1
bool1736 issubnormal 1E-1006 -> 1
bool1737 issubnormal -1E-1006 -> 1
bool1738 issubnormal 1E-1000 -> 1
bool1739 issubnormal -1E-1000 -> 1
bool1740 issubnormal 1E-999 -> 0
bool1741 issubnormal -1E-999 -> 0
bool1742 issubnormal 1E-998 -> 0
bool1743 issubnormal -1E-998 -> 0
bool1744 issubnormal 1E-100 -> 0
bool1745 issubnormal -1E-100 -> 0
bool1746 issubnormal 0.000001 -> 0
bool1747 issubnormal -0.000001 -> 0
bool1748 issubnormal 0.001 -> 0
bool1749 issubnormal -0.001 -> 0
bool1750 issubnormal 0.01 -> 0
bool1751 issubnormal -0.01 -> 0
bool1752 issubnormal 0.1 -> 0
bool1753 issubnormal -0.1 -> 0
bool1754 issubnormal 1 -> 0
bool1755 issubnormal -1 -> 0
bool1756 issubnormal 1E+1 -> 0
bool1757 issubnormal -1E+1 -> 0
bool1758 issubnormal 1E+2 -> 0
bool1759 issubnormal -1E+2 -> 0
bool1760 issubnormal 1E+3 -> 0
bool1761 issubnormal -1E+3 -> 0
bool1762 issubnormal 1E+6 -> 0
bool1763 issubnormal -1E+6 -> 0
bool1764 issubnormal 1E+100 -> 0
bool1765 issubnormal -1E+100 -> 0
bool1766 issubnormal 1E+990 -> 0
bool1767 issubnormal -1E+990 -> 0
bool1768 issubnormal 1E+991 -> 0
bool1769 issubnormal -1E+991 -> 0
bool1770 issubnormal 1E+992 -> 0
bool1771 issubnormal -1E+992 -> 0
bool1772 issubnormal 1E+998 -> 0
bool1773 issubnormal -1E+998 -> 0
bool1774 issubnormal 1E+999 -> 0
bool1775 issubnormal -1E+999 -> 0
bool1776 issubnormal 1E+1000 -> 0
bool1777 issubnormal -1E+1000 -> 0
bool1778 issubnormal 1E+2000 -> 0
bool1779 issubnormal -1E+2000 -> 0
bool1780 issubnormal 9E-2000 -> 1
bool1781 issubnormal -9E-2000 -> 1
bool1782 issubnormal 9E-1008 -> 1
bool1783 issubnormal -9E-1008 -> 1
bool1784 issubnormal 9E-1007 -> 1
bool1785 issubnormal -9E-1007 -> 1
bool1786 issubnormal 9E-1006 -> 1
bool1787 issubnormal -9E-1006 -> 1
bool1788 issubnormal 9E-1000 -> 1
bool1789 issubnormal -9E-1000 -> 1
bool1790 issubnormal 9E-999 -> 0
bool1791 issubnormal -9E-999 -> 0
bool1792 issubnormal 9E-998 -> 0
bool1793 issubnormal -9E-998 -> 0
bool1794 issubnormal 9E-100 -> 0
bool1795 issubnormal -9E-100 -> 0
bool1796 issubnormal 0.000009 -> 0
bool1797 issubnormal -0.000009 -> 0
bool1798 issubnormal 0.009 -> 0
bool1799 issubnormal -0.009 -> 0
bool1800 issubnormal 0.09 -> 0
bool1801 issubnormal -0.09 -> 0
bool1802 issubnormal 0.9 -> 0
bool1803 issubnormal -0.9 -> 0
bool1804 issubnormal 9 -> 0
bool1805 issubnormal -9 -> 0
bool1806 issubnormal 9E+1 -> 0
bool1807 issubnormal -9E+1 -> 0
bool1808 issubnormal 9E+2 -> 0
bool1809 issubnormal -9E+2 -> 0
bool1810 issubnormal 9E+3 -> 0
bool1811 issubnormal -9E+3 -> 0
bool1812 issubnormal 9E+6 -> 0
bool1813 issubnormal -9E+6 -> 0
bool1814 issubnormal 9E+100 -> 0
bool1815 issubnormal -9E+100 -> 0
bool1816 issubnormal 9E+990 -> 0
bool1817 issubnormal -9E+990 -> 0
bool1818 issubnormal 9E+991 -> 0
bool1819 issubnormal -9E+991 -> 0
bool1820 issubnormal 9E+992 -> 0
bool1821 issubnormal -9E+992 -> 0
bool1822 issubnormal 9E+998 -> 0
bool1823 issubnormal -9E+998 -> 0
bool1824 issubnormal 9E+999 -> 0
bool1825 issubnormal -9E+999 -> 0
bool1826 issubnormal 9E+1000 -> 0
bool1827 issubnormal -9E+1000 -> 0
bool1828 issubnormal 9E+2000 -> 0
bool1829 issubnormal -9E+2000 -> 0
bool1830 issubnormal 9.99999999E-2000 -> 1
bool1831 issubnormal -9.99999999E-2000 -> 1
bool1832 issubnormal 9.99999999E-1008 -> 1
bool1833 issubnormal -9.99999999E-1008 -> 1
bool1834 issubnormal 9.99999999E-1007 -> 1
bool1835 issubnormal -9.99999999E-1007 -> 1
bool1836 issubnormal 9.99999999E-1006 -> 1
bool1837 issubnormal -9.99999999E-1006 -> 1
bool1838 issubnormal 9.99999999E-1000 -> 1
bool1839 issubnormal -9.99999999E-1000 -> 1
bool1840 issubnormal 9.99999999E-999 -> 0
bool1841 issubnormal -9.99999999E-999 -> 0
bool1842 issubnormal 9.99999999E-998 -> 0
bool1843 issubnormal -9.99999999E-998 -> 0
bool1844 issubnormal 9.99999999E-100 -> 0
bool1845 issubnormal -9.99999999E-100 -> 0
bool1846 issubnormal 0.00000999999999 -> 0
bool1847 issubnormal -0.00000999999999 -> 0
bool1848 issubnormal 0.00999999999 -> 0
bool1849 issubnormal -0.00999999999 -> 0
bool1850 issubnormal 0.0999999999 -> 0
bool1851 issubnormal -0.0999999999 -> 0
bool1852 issubnormal 0.999999999 -> 0
bool1853 issubnormal -0.999999999 -> 0
bool1854 issubnormal 9.99999999 -> 0
bool1855 issubnormal -9.99999999 -> 0
bool1856 issubnormal 99.9999999 -> 0
bool1857 issubnormal -99.9999999 -> 0
bool1858 issubnormal 999.999999 -> 0
bool1859 issubnormal -999.999999 -> 0
bool1860 issubnormal 9999.99999 -> 0
bool1861 issubnormal -9999.99999 -> 0
bool1862 issubnormal 9999999.99 -> 0
bool1863 issubnormal -9999999.99 -> 0
bool1864 issubnormal 9.99999999E+100 -> 0
bool1865 issubnormal -9.99999999E+100 -> 0
bool1866 issubnormal 9.99999999E+990 -> 0
bool1867 issubnormal -9.99999999E+990 -> 0
bool1868 issubnormal 9.99999999E+991 -> 0
bool1869 issubnormal -9.99999999E+991 -> 0
bool1870 issubnormal 9.99999999E+992 -> 0
bool1871 issubnormal -9.99999999E+992 -> 0
bool1872 issubnormal 9.99999999E+998 -> 0
bool1873 issubnormal -9.99999999E+998 -> 0
bool1874 issubnormal 9.99999999E+999 -> 0
bool1875 issubnormal -9.99999999E+999 -> 0
bool1876 issubnormal 9.99999999E+1000 -> 0
bool1877 issubnormal -9.99999999E+1000 -> 0
bool1878 issubnormal 9.99999999E+2000 -> 0
bool1879 issubnormal -9.99999999E+2000 -> 0
bool1880 issubnormal Infinity -> 0
bool1881 issubnormal -Infinity -> 0
bool1882 issubnormal NaN -> 0
bool1883 issubnormal -NaN -> 0
bool1884 issubnormal NaN123 -> 0
bool1885 issubnormal -NaN123 -> 0
bool1886 issubnormal sNaN -> 0
bool1887 issubnormal -sNaN -> 0
bool1888 issubnormal sNaN123 -> 0
bool1889 issubnormal -sNaN123 -> 0
bool1890 iszero 0E-2000 -> 1
bool1891 iszero -0E-2000 -> 1
bool1892 iszero 0E-1008 -> 1
bool1893 iszero -0E-1008 -> 1
bool1894 iszero 0E-1007 -> 1
bool1895 iszero -0E-1007 -> 1
bool1896 iszero 0E-1006 -> 1
bool1897 iszero -0E-1006 -> 1
bool1898 iszero 0E-1000 -> 1
bool1899 iszero -0E-1000 -> 1
bool1900 iszero 0E-999 -> 1
bool1901 iszero -0E-999 -> 1
bool1902 iszero 0E-998 -> 1
bool1903 iszero -0E-998 -> 1
bool1904 iszero 0E-100 -> 1
bool1905 iszero -0E-100 -> 1
bool1906 iszero 0.000000 -> 1
bool1907 iszero -0.000000 -> 1
bool1908 iszero 0.000 -> 1
bool1909 iszero -0.000 -> 1
bool1910 iszero 0.00 -> 1
bool1911 iszero -0.00 -> 1
bool1912 iszero 0.0 -> 1
bool1913 iszero -0.0 -> 1
bool1914 iszero 0 -> 1
bool1915 iszero -0 -> 1
bool1916 iszero 0E+1 -> 1
bool1917 iszero -0E+1 -> 1
bool1918 iszero 0E+2 -> 1
bool1919 iszero -0E+2 -> 1
bool1920 iszero 0E+3 -> 1
bool1921 iszero -0E+3 -> 1
bool1922 iszero 0E+6 -> 1
bool1923 iszero -0E+6 -> 1
bool1924 iszero 0E+100 -> 1
bool1925 iszero -0E+100 -> 1
bool1926 iszero 0E+990 -> 1
bool1927 iszero -0E+990 -> 1
bool1928 iszero 0E+991 -> 1
bool1929 iszero -0E+991 -> 1
bool1930 iszero 0E+992 -> 1
bool1931 iszero -0E+992 -> 1
bool1932 iszero 0E+998 -> 1
bool1933 iszero -0E+998 -> 1
bool1934 iszero 0E+999 -> 1
bool1935 iszero -0E+999 -> 1
bool1936 iszero 0E+1000 -> 1
bool1937 iszero -0E+1000 -> 1
bool1938 iszero 0E+2000 -> 1
bool1939 iszero -0E+2000 -> 1
bool1940 iszero 1E-2000 -> 0
bool1941 iszero -1E-2000 -> 0
bool1942 iszero 1E-1008 -> 0
bool1943 iszero -1E-1008 -> 0
bool1944 iszero 1E-1007 -> 0
bool1945 iszero -1E-1007 -> 0
bool1946 iszero 1E-1006 -> 0
bool1947 iszero -1E-1006 -> 0
bool1948 iszero 1E-1000 -> 0
bool1949 iszero -1E-1000 -> 0
bool1950 iszero 1E-999 -> 0
bool1951 iszero -1E-999 -> 0
bool1952 iszero 1E-998 -> 0
bool1953 iszero -1E-998 -> 0
bool1954 iszero 1E-100 -> 0
bool1955 iszero -1E-100 -> 0
bool1956 iszero 0.000001 -> 0
bool1957 iszero -0.000001 -> 0
bool1958 iszero 0.001 -> 0
bool1959 iszero -0.001 -> 0
bool1960 iszero 0.01 -> 0
bool1961 iszero -0.01 -> 0
bool1962 iszero 0.1 -> 0
bool1963 iszero -0.1 -> 0
bool1964 iszero 1 -> 0
bool1965 iszero -1 -> 0
bool1966 iszero 1E+1 -> 0
bool1967 iszero -1E+1 -> 0
bool1968 iszero 1E+2 -> 0
bool1969 iszero -1E+2 -> 0
bool1970 iszero 1E+3 -> 0
bool1971 iszero -1E+3 -> 0
bool1972 iszero 1E+6 -> 0
bool1973 iszero -1E+6 -> 0
bool1974 iszero 1E+100 -> 0
bool1975 iszero -1E+100 -> 0
bool1976 iszero 1E+990 -> 0
bool1977 iszero -1E+990 -> 0
bool1978 iszero 1E+991 -> 0
bool1979 iszero -1E+991 -> 0
bool1980 iszero 1E+992 -> 0
bool1981 iszero -1E+992 -> 0
bool1982 iszero 1E+998 -> 0
bool1983 iszero -1E+998 -> 0
bool1984 iszero 1E+999 -> 0
bool1985 iszero -1E+999 -> 0
bool1986 iszero 1E+1000 -> 0
bool1987 iszero -1E+1000 -> 0
bool1988 iszero 1E+2000 -> 0
bool1989 iszero -1E+2000 -> 0
bool1990 iszero 9E-2000 -> 0
bool1991 iszero -9E-2000 -> 0
bool1992 iszero 9E-1008 -> 0
bool1993 iszero -9E-1008 -> 0
bool1994 iszero 9E-1007 -> 0
bool1995 iszero -9E-1007 -> 0
bool1996 iszero 9E-1006 -> 0
bool1997 iszero -9E-1006 -> 0
bool1998 iszero 9E-1000 -> 0
bool1999 iszero -9E-1000 -> 0
bool2000 iszero 9E-999 -> 0
bool2001 iszero -9E-999 -> 0
bool2002 iszero 9E-998 -> 0
bool2003 iszero -9E-998 -> 0
bool2004 iszero 9E-100 -> 0
bool2005 iszero -9E-100 -> 0
bool2006 iszero 0.000009 -> 0
bool2007 iszero -0.000009 -> 0
bool2008 iszero 0.009 -> 0
bool2009 iszero -0.009 -> 0
bool2010 iszero 0.09 -> 0
bool2011 iszero -0.09 -> 0
bool2012 iszero 0.9 -> 0
bool2013 iszero -0.9 -> 0
bool2014 iszero 9 -> 0
bool2015 iszero -9 -> 0
bool2016 iszero 9E+1 -> 0
bool2017 iszero -9E+1 -> 0
bool2018 iszero 9E+2 -> 0
bool2019 iszero -9E+2 -> 0
bool2020 iszero 9E+3 -> 0
bool2021 iszero -9E+3 -> 0
bool2022 iszero 9E+6 -> 0
bool2023 iszero -9E+6 -> 0
bool2024 iszero 9E+100 -> 0
bool2025 iszero -9E+100 -> 0
bool2026 iszero 9E+990 -> 0
bool2027 iszero -9E+990 -> 0
bool2028 iszero 9E+991 -> 0
bool2029 iszero -9E+991 -> 0
bool2030 iszero 9E+992 -> 0
bool2031 iszero -9E+992 -> 0
bool2032 iszero 9E+998 -> 0
bool2033 iszero -9E+998 -> 0
bool2034 iszero 9E+999 -> 0
bool2035 iszero -9E+999 -> 0
bool2036 iszero 9E+1000 -> 0
bool2037 iszero -9E+1000 -> 0
bool2038 iszero 9E+2000 -> 0
bool2039 iszero -9E+2000 -> 0
bool2040 iszero 9.99999999E-2000 -> 0
bool2041 iszero -9.99999999E-2000 -> 0
bool2042 iszero 9.99999999E-1008 -> 0
bool2043 iszero -9.99999999E-1008 -> 0
bool2044 iszero 9.99999999E-1007 -> 0
bool2045 iszero -9.99999999E-1007 -> 0
bool2046 iszero 9.99999999E-1006 -> 0
bool2047 iszero -9.99999999E-1006 -> 0
bool2048 iszero 9.99999999E-1000 -> 0
bool2049 iszero -9.99999999E-1000 -> 0
bool2050 iszero 9.99999999E-999 -> 0
bool2051 iszero -9.99999999E-999 -> 0
bool2052 iszero 9.99999999E-998 -> 0
bool2053 iszero -9.99999999E-998 -> 0
bool2054 iszero 9.99999999E-100 -> 0
bool2055 iszero -9.99999999E-100 -> 0
bool2056 iszero 0.00000999999999 -> 0
bool2057 iszero -0.00000999999999 -> 0
bool2058 iszero 0.00999999999 -> 0
bool2059 iszero -0.00999999999 -> 0
bool2060 iszero 0.0999999999 -> 0
bool2061 iszero -0.0999999999 -> 0
bool2062 iszero 0.999999999 -> 0
bool2063 iszero -0.999999999 -> 0
bool2064 iszero 9.99999999 -> 0
bool2065 iszero -9.99999999 -> 0
bool2066 iszero 99.9999999 -> 0
bool2067 iszero -99.9999999 -> 0
bool2068 iszero 999.999999 -> 0
bool2069 iszero -999.999999 -> 0
bool2070 iszero 9999.99999 -> 0
bool2071 iszero -9999.99999 -> 0
bool2072 iszero 9999999.99 -> 0
bool2073 iszero -9999999.99 -> 0
bool2074 iszero 9.99999999E+100 -> 0
bool2075 iszero -9.99999999E+100 -> 0
bool2076 iszero 9.99999999E+990 -> 0
bool2077 iszero -9.99999999E+990 -> 0
bool2078 iszero 9.99999999E+991 -> 0
bool2079 iszero -9.99999999E+991 -> 0
bool2080 iszero 9.99999999E+992 -> 0
bool2081 iszero -9.99999999E+992 -> 0
bool2082 iszero 9.99999999E+998 -> 0
bool2083 iszero -9.99999999E+998 -> 0
bool2084 iszero 9.99999999E+999 -> 0
bool2085 iszero -9.99999999E+999 -> 0
bool2086 iszero 9.99999999E+1000 -> 0
bool2087 iszero -9.99999999E+1000 -> 0
bool2088 iszero 9.99999999E+2000 -> 0
bool2089 iszero -9.99999999E+2000 -> 0
bool2090 iszero Infinity -> 0
bool2091 iszero -Infinity -> 0
bool2092 iszero NaN -> 0
bool2093 iszero -NaN -> 0
bool2094 iszero NaN123 -> 0
bool2095 iszero -NaN123 -> 0
bool2096 iszero sNaN -> 0
bool2097 iszero -sNaN -> 0
bool2098 iszero sNaN123 -> 0
bool2099 iszero -sNaN123 -> 0

------------------------------------------------------------------------
-- The following tests (pwmx0 through pwmx440) are for the            --
-- three-argument version of power:                                   --
--                                                                    --
--   pow(x, y, z) := x**y % z                                         --
--                                                                    --
-- Note that the three-argument version of power is *not* part of     --
-- the IBM General Decimal Arithmetic specification.  Questions       --
-- about it, or about these testcases, should go to one of the        --
-- Python decimal authors.                                            --
------------------------------------------------------------------------

extended: 1
precision: 9
rounding: down
maxExponent: 999
minExponent: -999

-- Small numbers
-- Note that power(0, 0, m) is an error for any m
pwmx0 power 0 -0 1 -> NaN Invalid_operation
pwmx1 power 0 -0 2 -> NaN Invalid_operation
pwmx2 power 0 -0 3 -> NaN Invalid_operation
pwmx3 power 0 -0 4 -> NaN Invalid_operation
pwmx4 power 0 -0 -1 -> NaN Invalid_operation
pwmx5 power 0 -0 -2 -> NaN Invalid_operation
pwmx6 power 0 0 1 -> NaN Invalid_operation
pwmx7 power 0 0 2 -> NaN Invalid_operation
pwmx8 power 0 0 3 -> NaN Invalid_operation
pwmx9 power 0 0 4 -> NaN Invalid_operation
pwmx10 power 0 0 -1 -> NaN Invalid_operation
pwmx11 power 0 0 -2 -> NaN Invalid_operation
pwmx12 power 0 1 1 -> 0
pwmx13 power 0 1 2 -> 0
pwmx14 power 0 1 3 -> 0
pwmx15 power 0 1 4 -> 0
pwmx16 power 0 1 -1 -> 0
pwmx17 power 0 1 -2 -> 0
pwmx18 power 0 2 1 -> 0
pwmx19 power 0 2 2 -> 0
pwmx20 power 0 2 3 -> 0
pwmx21 power 0 2 4 -> 0
pwmx22 power 0 2 -1 -> 0
pwmx23 power 0 2 -2 -> 0
pwmx24 power 0 3 1 -> 0
pwmx25 power 0 3 2 -> 0
pwmx26 power 0 3 3 -> 0
pwmx27 power 0 3 4 -> 0
pwmx28 power 0 3 -1 -> 0
pwmx29 power 0 3 -2 -> 0
pwmx30 power 0 4 1 -> 0
pwmx31 power 0 4 2 -> 0
pwmx32 power 0 4 3 -> 0
pwmx33 power 0 4 4 -> 0
pwmx34 power 0 4 -1 -> 0
pwmx35 power 0 4 -2 -> 0
pwmx36 power 0 5 1 -> 0
pwmx37 power 0 5 2 -> 0
pwmx38 power 0 5 3 -> 0
pwmx39 power 0 5 4 -> 0
pwmx40 power 0 5 -1 -> 0
pwmx41 power 0 5 -2 -> 0
pwmx42 power 1 -0 1 -> 0
pwmx43 power 1 -0 2 -> 1
pwmx44 power 1 -0 3 -> 1
pwmx45 power 1 -0 4 -> 1
pwmx46 power 1 -0 -1 -> 0
pwmx47 power 1 -0 -2 -> 1
pwmx48 power 1 0 1 -> 0
pwmx49 power 1 0 2 -> 1
pwmx50 power 1 0 3 -> 1
pwmx51 power 1 0 4 -> 1
pwmx52 power 1 0 -1 -> 0
pwmx53 power 1 0 -2 -> 1
pwmx54 power 1 1 1 -> 0
pwmx55 power 1 1 2 -> 1
pwmx56 power 1 1 3 -> 1
pwmx57 power 1 1 4 -> 1
pwmx58 power 1 1 -1 -> 0
pwmx59 power 1 1 -2 -> 1
pwmx60 power 1 2 1 -> 0
pwmx61 power 1 2 2 -> 1
pwmx62 power 1 2 3 -> 1
pwmx63 power 1 2 4 -> 1
pwmx64 power 1 2 -1 -> 0
pwmx65 power 1 2 -2 -> 1
pwmx66 power 1 3 1 -> 0
pwmx67 power 1 3 2 -> 1
pwmx68 power 1 3 3 -> 1
pwmx69 power 1 3 4 -> 1
pwmx70 power 1 3 -1 -> 0
pwmx71 power 1 3 -2 -> 1
pwmx72 power 1 4 1 -> 0
pwmx73 power 1 4 2 -> 1
pwmx74 power 1 4 3 -> 1
pwmx75 power 1 4 4 -> 1
pwmx76 power 1 4 -1 -> 0
pwmx77 power 1 4 -2 -> 1
pwmx78 power 1 5 1 -> 0
pwmx79 power 1 5 2 -> 1
pwmx80 power 1 5 3 -> 1
pwmx81 power 1 5 4 -> 1
pwmx82 power 1 5 -1 -> 0
pwmx83 power 1 5 -2 -> 1
pwmx84 power 2 -0 1 -> 0
pwmx85 power 2 -0 2 -> 1
pwmx86 power 2 -0 3 -> 1
pwmx87 power 2 -0 4 -> 1
pwmx88 power 2 -0 -1 -> 0
pwmx89 power 2 -0 -2 -> 1
pwmx90 power 2 0 1 -> 0
pwmx91 power 2 0 2 -> 1
pwmx92 power 2 0 3 -> 1
pwmx93 power 2 0 4 -> 1
pwmx94 power 2 0 -1 -> 0
pwmx95 power 2 0 -2 -> 1
pwmx96 power 2 1 1 -> 0
pwmx97 power 2 1 2 -> 0
pwmx98 power 2 1 3 -> 2
pwmx99 power 2 1 4 -> 2
pwmx100 power 2 1 -1 -> 0
pwmx101 power 2 1 -2 -> 0
pwmx102 power 2 2 1 -> 0
pwmx103 power 2 2 2 -> 0
pwmx104 power 2 2 3 -> 1
pwmx105 power 2 2 4 -> 0
pwmx106 power 2 2 -1 -> 0
pwmx107 power 2 2 -2 -> 0
pwmx108 power 2 3 1 -> 0
pwmx109 power 2 3 2 -> 0
pwmx110 power 2 3 3 -> 2
pwmx111 power 2 3 4 -> 0
pwmx112 power 2 3 -1 -> 0
pwmx113 power 2 3 -2 -> 0
pwmx114 power 2 4 1 -> 0
pwmx115 power 2 4 2 -> 0
pwmx116 power 2 4 3 -> 1
pwmx117 power 2 4 4 -> 0
pwmx118 power 2 4 -1 -> 0
pwmx119 power 2 4 -2 -> 0
pwmx120 power 2 5 1 -> 0
pwmx121 power 2 5 2 -> 0
pwmx122 power 2 5 3 -> 2
pwmx123 power 2 5 4 -> 0
pwmx124 power 2 5 -1 -> 0
pwmx125 power 2 5 -2 -> 0
pwmx126 power 3 -0 1 -> 0
pwmx127 power 3 -0 2 -> 1
pwmx128 power 3 -0 3 -> 1
pwmx129 power 3 -0 4 -> 1
pwmx130 power 3 -0 -1 -> 0
pwmx131 power 3 -0 -2 -> 1
pwmx132 power 3 0 1 -> 0
pwmx133 power 3 0 2 -> 1
pwmx134 power 3 0 3 -> 1
pwmx135 power 3 0 4 -> 1
pwmx136 power 3 0 -1 -> 0
pwmx137 power 3 0 -2 -> 1
pwmx138 power 3 1 1 -> 0
pwmx139 power 3 1 2 -> 1
pwmx140 power 3 1 3 -> 0
pwmx141 power 3 1 4 -> 3
pwmx142 power 3 1 -1 -> 0
pwmx143 power 3 1 -2 -> 1
pwmx144 power 3 2 1 -> 0
pwmx145 power 3 2 2 -> 1
pwmx146 power 3 2 3 -> 0
pwmx147 power 3 2 4 -> 1
pwmx148 power 3 2 -1 -> 0
pwmx149 power 3 2 -2 -> 1
pwmx150 power 3 3 1 -> 0
pwmx151 power 3 3 2 -> 1
pwmx152 power 3 3 3 -> 0
pwmx153 power 3 3 4 -> 3
pwmx154 power 3 3 -1 -> 0
pwmx155 power 3 3 -2 -> 1
pwmx156 power 3 4 1 -> 0
pwmx157 power 3 4 2 -> 1
pwmx158 power 3 4 3 -> 0
pwmx159 power 3 4 4 -> 1
pwmx160 power 3 4 -1 -> 0
pwmx161 power 3 4 -2 -> 1
pwmx162 power 3 5 1 -> 0
pwmx163 power 3 5 2 -> 1
pwmx164 power 3 5 3 -> 0
pwmx165 power 3 5 4 -> 3
pwmx166 power 3 5 -1 -> 0
pwmx167 power 3 5 -2 -> 1
pwmx168 power -0 -0 1 -> NaN Invalid_operation
pwmx169 power -0 -0 2 -> NaN Invalid_operation
pwmx170 power -0 -0 3 -> NaN Invalid_operation
pwmx171 power -0 -0 4 -> NaN Invalid_operation
pwmx172 power -0 -0 -1 -> NaN Invalid_operation
pwmx173 power -0 -0 -2 -> NaN Invalid_operation
pwmx174 power -0 0 1 -> NaN Invalid_operation
pwmx175 power -0 0 2 -> NaN Invalid_operation
pwmx176 power -0 0 3 -> NaN Invalid_operation
pwmx177 power -0 0 4 -> NaN Invalid_operation
pwmx178 power -0 0 -1 -> NaN Invalid_operation
pwmx179 power -0 0 -2 -> NaN Invalid_operation
pwmx180 power -0 1 1 -> -0
pwmx181 power -0 1 2 -> -0
pwmx182 power -0 1 3 -> -0
pwmx183 power -0 1 4 -> -0
pwmx184 power -0 1 -1 -> -0
pwmx185 power -0 1 -2 -> -0
pwmx186 power -0 2 1 -> 0
pwmx187 power -0 2 2 -> 0
pwmx188 power -0 2 3 -> 0
pwmx189 power -0 2 4 -> 0
pwmx190 power -0 2 -1 -> 0
pwmx191 power -0 2 -2 -> 0
pwmx192 power -0 3 1 -> -0
pwmx193 power -0 3 2 -> -0
pwmx194 power -0 3 3 -> -0
pwmx195 power -0 3 4 -> -0
pwmx196 power -0 3 -1 -> -0
pwmx197 power -0 3 -2 -> -0
pwmx198 power -0 4 1 -> 0
pwmx199 power -0 4 2 -> 0
pwmx200 power -0 4 3 -> 0
pwmx201 power -0 4 4 -> 0
pwmx202 power -0 4 -1 -> 0
pwmx203 power -0 4 -2 -> 0
pwmx204 power -0 5 1 -> -0
pwmx205 power -0 5 2 -> -0
pwmx206 power -0 5 3 -> -0
pwmx207 power -0 5 4 -> -0
pwmx208 power -0 5 -1 -> -0
pwmx209 power -0 5 -2 -> -0
pwmx210 power -1 -0 1 -> 0
pwmx211 power -1 -0 2 -> 1
pwmx212 power -1 -0 3 -> 1
pwmx213 power -1 -0 4 -> 1
pwmx214 power -1 -0 -1 -> 0
pwmx215 power -1 -0 -2 -> 1
pwmx216 power -1 0 1 -> 0
pwmx217 power -1 0 2 -> 1
pwmx218 power -1 0 3 -> 1
pwmx219 power -1 0 4 -> 1
pwmx220 power -1 0 -1 -> 0
pwmx221 power -1 0 -2 -> 1
pwmx222 power -1 1 1 -> -0
pwmx223 power -1 1 2 -> -1
pwmx224 power -1 1 3 -> -1
pwmx225 power -1 1 4 -> -1
pwmx226 power -1 1 -1 -> -0
pwmx227 power -1 1 -2 -> -1
pwmx228 power -1 2 1 -> 0
pwmx229 power -1 2 2 -> 1
pwmx230 power -1 2 3 -> 1
pwmx231 power -1 2 4 -> 1
pwmx232 power -1 2 -1 -> 0
pwmx233 power -1 2 -2 -> 1
pwmx234 power -1 3 1 -> -0
pwmx235 power -1 3 2 -> -1
pwmx236 power -1 3 3 -> -1
pwmx237 power -1 3 4 -> -1
pwmx238 power -1 3 -1 -> -0
pwmx239 power -1 3 -2 -> -1
pwmx240 power -1 4 1 -> 0
pwmx241 power -1 4 2 -> 1
pwmx242 power -1 4 3 -> 1
pwmx243 power -1 4 4 -> 1
pwmx244 power -1 4 -1 -> 0
pwmx245 power -1 4 -2 -> 1
pwmx246 power -1 5 1 -> -0
pwmx247 power -1 5 2 -> -1
pwmx248 power -1 5 3 -> -1
pwmx249 power -1 5 4 -> -1
pwmx250 power -1 5 -1 -> -0
pwmx251 power -1 5 -2 -> -1

-- Randomly chosen larger values
pwmx252 power 0 4 7 -> 0
pwmx253 power -4 5 -9 -> -7
pwmx254 power -5 4 -9 -> 4
pwmx255 power -50 29 2 -> -0
pwmx256 power -1 83 3 -> -1
pwmx257 power -55 65 -75 -> -25
pwmx258 power -613 151 -302 -> -9
pwmx259 power 551 23 -35 -> 31
pwmx260 power 51 142 942 -> 9
pwmx261 power 6886 9204 -6091 -> 5034
pwmx262 power 3057 5890 -3 -> 0
pwmx263 power 56 4438 5365 -> 521
pwmx264 power 96237 35669 -46669 -> 30717
pwmx265 power 40011 34375 -57611 -> 625
pwmx266 power 44317 38493 -12196 -> 11081
pwmx267 power -282368 895633 -235870 -> -220928
pwmx268 power 77328 852553 -405529 -> 129173
pwmx269 power -929659 855713 650348 -> -90803
pwmx270 power 907057 6574309 4924768 -> 3018257
pwmx271 power -2887757 3198492 -5864352 -> 3440113
pwmx272 power -247310 657371 -7415739 -> -1301840
pwmx273 power -8399046 45334087 -22395020 -> -18515896
pwmx274 power 79621397 4850236 1486555 -> 928706
pwmx275 power 96012251 27971901 69609031 -> 50028729
pwmx276 power -907335481 74127986 582330017 -> 51527187
pwmx277 power -141192960 821063826 -260877928 -> 112318560
pwmx278 power -501711702 934355994 82135143 -> 66586995
pwmx279 power -9256358075 8900900138 -467222031 -> 95800246
pwmx280 power -7031964291 1751257483 -935334498 -> -607626609
pwmx281 power 8494314971 8740197252 107522491 -> 17373655
pwmx282 power 88306216890 87477374166 -23498076 -> 15129528
pwmx283 power -33939432478 7170196239 22133583 -> -11017036
pwmx284 power 19466222767 30410710614 305752056 -> 191509537
pwmx285 power -864942494008 370558899638 346688856 -> 56956768
pwmx286 power -525406225603 345700226898 237163621 -> 56789534
pwmx287 power 464612215955 312474621651 -329485700 -> 1853975
pwmx288 power -1664283031244 3774474669855 919022867 -> -516034520
pwmx289 power -3472438506913 7407327549995 -451206854 -> -74594761
pwmx290 power -4223662152949 6891069279069 499843503 -> -80135290
pwmx291 power -44022119276816 8168266170326 569679509 -> 375734475
pwmx292 power -66195891207902 12532690555875 -243262129 -> -113186833
pwmx293 power -69039911263164 52726605857673 360625196 -> -268662748
pwmx294 power -299010116699208 885092589359231 -731310123 -> -104103765
pwmx295 power -202495776299758 501159122943145 -686234870 -> -135511878
pwmx296 power -595411478087676 836269270472481 -214614901 -> -183440819
pwmx297 power -139555381056229 1324808520020507 -228944738 -> -218991473
pwmx298 power 7846356250770543 1798045051036814 -101028985 -> 7805179
pwmx299 power -4298015862709415 604966944844209 880212893 -> -87408671
pwmx300 power -37384897538910893 76022206995659295 -930512842 -> -697757157
pwmx301 power 82166659028005443 23375408251767704 817270700 -> 770697001
pwmx302 power 97420301198165641 72213282983416924 947519716 -> 610711721
pwmx303 power 913382043453243607 449681707248500262 211135545 -> 79544899
pwmx304 power -313823613418052171 534579409610142937 -943062968 -> -446001379
pwmx305 power -928106516894494093 760020177330116509 -50043994 -> -46010575
pwmx306 power 4692146601679439796 4565354511806767804 -667339075 -> 480272081
pwmx307 power 9722256633509177930 7276568791860505790 792675321 -> 182879752
pwmx308 power 8689899484830064228 429082967129615261 -844555637 -> 270374557

-- All inputs must be integers
pwmx309 power 2.1 3 1 -> NaN Invalid_operation
pwmx310 power 0.4 1 5 -> NaN Invalid_operation
pwmx311 power 2 3.1 5 -> NaN Invalid_operation
pwmx312 power 13 -1.2 10 -> NaN Invalid_operation
pwmx313 power 2 3 5.1 -> NaN Invalid_operation

-- Second argument must be nonnegative (-0 is okay)
pwmx314 power 2 -3 5 -> NaN Invalid_operation
pwmx315 power 7 -1 1 -> NaN Invalid_operation
pwmx316 power 0 -2 6 -> NaN Invalid_operation

-- Third argument must be nonzero
pwmx317 power 13 1003 0 -> NaN Invalid_operation
pwmx318 power 1 0 0E+987 -> NaN Invalid_operation
pwmx319 power 0 2 -0 -> NaN Invalid_operation

-- Integers are fine, no matter how they're expressed
pwmx320 power 13.0 117.00 1E+2 -> 33
pwmx321 power -2E+3 1.1E+10 -12323 -> 4811
pwmx322 power 20 0E-300 143 -> 1
pwmx323 power -20 -0E+1005 1179 -> 1
pwmx324 power 0E-1001 17 5.6E+4 -> 0

-- Modulus must not exceed precision
pwmx325 power 0 1 1234567890 -> NaN Invalid_operation
pwmx326 power 1 0 1000000000 -> NaN Invalid_operation
pwmx327 power -23 5 -1000000000 -> NaN Invalid_operation
pwmx328 power 41557 213 -999999999 -> 47650456
pwmx329 power -2134 199 999999997 -> -946957912

-- Huge base shouldn't present any problems
pwmx330 power 1.23E+123456791 10123898 17291065 -> 5674045

-- Large exponent, may be slow
-- (if second argument is 1En then expect O(n) running time)
pwmx331 power 1000288896 9.87E+12347 93379908 -> 43224924

-- Triple NaN propagation (adapted from examples in fma.decTest)
pwmx400 power  NaN2  NaN3  NaN5   ->  NaN2
pwmx401 power  1     NaN3  NaN5   ->  NaN3
pwmx402 power  1     1     NaN5   ->  NaN5
pwmx403 power  sNaN1 sNaN2 sNaN3  ->  NaN1 Invalid_operation
pwmx404 power  1     sNaN2 sNaN3  ->  NaN2 Invalid_operation
pwmx405 power  1     1     sNaN3  ->  NaN3 Invalid_operation
pwmx406 power  sNaN1 sNaN2 sNaN3  ->  NaN1 Invalid_operation
pwmx407 power  NaN7  sNaN2 sNaN3  ->  NaN2 Invalid_operation
pwmx408 power  NaN7  NaN5  sNaN3  ->  NaN3 Invalid_operation

-- Infinities not allowed
pwmx410 power Inf 1 1 -> NaN Invalid_operation
pwmx411 power 1 Inf 1 -> NaN Invalid_operation
pwmx412 power 1 1 Inf -> NaN Invalid_operation
pwmx413 power -Inf 1 1 -> NaN Invalid_operation
pwmx414 power 1 -Inf 1 -> NaN Invalid_operation
pwmx415 power 1 1 -Inf -> NaN Invalid_operation

-- Just for fun: 1729 is a Carmichael number
pwmx420 power 0 1728 1729 -> 0
pwmx421 power 1 1728 1729 -> 1
pwmx422 power 2 1728 1729 -> 1
pwmx423 power 3 1728 1729 -> 1
pwmx424 power 4 1728 1729 -> 1
pwmx425 power 5 1728 1729 -> 1
pwmx426 power 6 1728 1729 -> 1
pwmx427 power 7 1728 1729 -> 742
pwmx428 power 8 1728 1729 -> 1
pwmx429 power 9 1728 1729 -> 1
pwmx430 power 10 1728 1729 -> 1
pwmx431 power 11 1728 1729 -> 1
pwmx432 power 12 1728 1729 -> 1
pwmx433 power 13 1728 1729 -> 533
pwmx434 power 14 1728 1729 -> 742
pwmx435 power 15 1728 1729 -> 1
pwmx436 power 16 1728 1729 -> 1
pwmx437 power 17 1728 1729 -> 1
pwmx438 power 18 1728 1729 -> 1
pwmx439 power 19 1728 1729 -> 456
pwmx440 power 20 1728 1729 -> 1

-- plus and minus zero in various rounding modes (see issue 11131)
extended: 1
precision: 9
maxexponent: 384
minexponent: -383

rounding: half_even
plux1000 plus 0.0 -> 0.0
plux1001 plus -0.0 -> 0.0
minx1000 minus 0.0 -> 0.0
minx1001 minus -0.0 -> 0.0
absx1000 abs 0.0 -> 0.0
absx1001 abs -0.0 -> 0.0

rounding: half_up
plux1010 plus 0.0 -> 0.0
minx1010 minus 0.0 -> 0.0
plux1011 plus -0.0 -> 0.0
minx1011 minus -0.0 -> 0.0
absx1010 abs 0.0 -> 0.0
absx1011 abs -0.0 -> 0.0

rounding: ceiling
plux1020 plus 0.0 -> 0.0
minx1020 minus 0.0 -> 0.0
plux1021 plus -0.0 -> 0.0
minx1021 minus -0.0 -> 0.0
absx1020 abs 0.0 -> 0.0
absx1021 abs -0.0 -> 0.0

rounding: floor
plux1030 plus 0.0 -> 0.0
minx1030 minus 0.0 -> -0.0
plux1031 plus -0.0 -> -0.0
minx1031 minus -0.0 -> 0.0
absx1030 abs 0.0 -> 0.0
absx1031 abs -0.0 -> 0.0

rounding: down
plux1040 plus 0.0 -> 0.0
minx1040 minus 0.0 -> 0.0
plux1041 plus -0.0 -> 0.0
minx1041 minus -0.0 -> 0.0
absx1040 abs 0.0 -> 0.0
absx1041 abs -0.0 -> 0.0

rounding: up
plux1050 plus 0.0 -> 0.0
minx1050 minus 0.0 -> 0.0
plux1051 plus -0.0 -> 0.0
minx1051 minus -0.0 -> 0.0
absx1050 abs 0.0 -> 0.0
absx1051 abs -0.0 -> 0.0

rounding: half_down
plux1060 plus 0.0 -> 0.0
minx1060 minus 0.0 -> 0.0
plux1061 plus -0.0 -> 0.0
minx1061 minus -0.0 -> 0.0
absx1060 abs 0.0 -> 0.0
absx1061 abs -0.0 -> 0.0

rounding: 05up
plux1070 plus 0.0 -> 0.0
minx1070 minus 0.0 -> 0.0
plux1071 plus -0.0 -> 0.0
minx1071 minus -0.0 -> 0.0
absx1070 abs 0.0 -> 0.0
absx1071 abs -0.0 -> 0.0
