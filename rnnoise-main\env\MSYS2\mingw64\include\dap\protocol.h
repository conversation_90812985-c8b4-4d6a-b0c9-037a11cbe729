// Copyright 2019 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Generated with protocol_gen.go -- do not edit this file.
//   go run scripts/protocol_gen/protocol_gen.go
//
// DAP version 1.65.0

#ifndef dap_protocol_h
#define dap_protocol_h

#include "optional.h"
#include "typeinfo.h"
#include "typeof.h"
#include "variant.h"

#include <string>
#include <type_traits>
#include <vector>

namespace dap {

struct Request {};
struct Response {};
struct Event {};

// Response to `attach` request. This is just an acknowledgement, so no body
// field is required.
struct AttachResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(AttachResponse);

// The `attach` request is sent from the client to the debug adapter to attach
// to a debuggee that is already running. Since attaching is debugger/runtime
// specific, the arguments for this request are not part of this specification.
struct AttachRequest : public Request {
  using Response = AttachResponse;
  // Arbitrary data from the previous, restarted session.
  // The data is sent as the `restart` attribute of the `terminated` event.
  // The client should leave the data intact.
  optional<variant<array<any>, boolean, integer, null, number, object, string>>
      restart;
};

DAP_DECLARE_STRUCT_TYPEINFO(AttachRequest);

// Names of checksum algorithms that may be supported by a debug adapter.
//
// Must be one of the following enumeration values:
// 'MD5', 'SHA1', 'SHA256', 'timestamp'
using ChecksumAlgorithm = string;

// The checksum of an item calculated by the specified algorithm.
struct Checksum {
  // The algorithm used to calculate this checksum.
  ChecksumAlgorithm algorithm = "MD5";
  // Value of the checksum, encoded as a hexadecimal value.
  string checksum;
};

DAP_DECLARE_STRUCT_TYPEINFO(Checksum);

// A `Source` is a descriptor for source code.
// It is returned from the debug adapter as part of a `StackFrame` and it is
// used by clients when specifying breakpoints.
struct Source {
  // Additional data that a debug adapter might want to loop through the client.
  // The client should leave the data intact and persist it across sessions. The
  // client should not interpret the data.
  optional<variant<array<any>, boolean, integer, null, number, object, string>>
      adapterData;
  // The checksums associated with this file.
  optional<array<Checksum>> checksums;
  // The short name of the source. Every source returned from the debug adapter
  // has a name. When sending a source to the debug adapter this name is
  // optional.
  optional<string> name;
  // The origin of this source. For example, 'internal module', 'inlined content
  // from source map', etc.
  optional<string> origin;
  // The path of the source to be shown in the UI.
  // It is only used to locate and load the content of the source if no
  // `sourceReference` is specified (or its value is 0).
  optional<string> path;
  // A hint for how to present the source in the UI.
  // A value of `deemphasize` can be used to indicate that the source is not
  // available or that it is skipped on stepping.
  //
  // Must be one of the following enumeration values:
  // 'normal', 'emphasize', 'deemphasize'
  optional<string> presentationHint;
  // If the value > 0 the contents of the source must be retrieved through the
  // `source` request (even if a path is specified). Since a `sourceReference`
  // is only valid for a session, it can not be used to persist a source. The
  // value should be less than or equal to 2147483647 (2^31-1).
  optional<integer> sourceReference;
  // A list of sources that are related to this source. These may be the source
  // that generated this source.
  optional<array<Source>> sources;
};

DAP_DECLARE_STRUCT_TYPEINFO(Source);

// Information about a breakpoint created in `setBreakpoints`,
// `setFunctionBreakpoints`, `setInstructionBreakpoints`, or
// `setDataBreakpoints` requests.
struct Breakpoint {
  // Start position of the source range covered by the breakpoint. It is
  // measured in UTF-16 code units and the client capability `columnsStartAt1`
  // determines whether it is 0- or 1-based.
  optional<integer> column;
  // End position of the source range covered by the breakpoint. It is measured
  // in UTF-16 code units and the client capability `columnsStartAt1` determines
  // whether it is 0- or 1-based. If no end line is given, then the end column
  // is assumed to be in the start line.
  optional<integer> endColumn;
  // The end line of the actual range covered by the breakpoint.
  optional<integer> endLine;
  // The identifier for the breakpoint. It is needed if breakpoint events are
  // used to update or remove breakpoints.
  optional<integer> id;
  // A memory reference to where the breakpoint is set.
  optional<string> instructionReference;
  // The start line of the actual range covered by the breakpoint.
  optional<integer> line;
  // A message about the state of the breakpoint.
  // This is shown to the user and can be used to explain why a breakpoint could
  // not be verified.
  optional<string> message;
  // The offset from the instruction reference.
  // This can be negative.
  optional<integer> offset;
  // A machine-readable explanation of why a breakpoint may not be verified. If
  // a breakpoint is verified or a specific reason is not known, the adapter
  // should omit this property. Possible values include:
  //
  // - `pending`: Indicates a breakpoint might be verified in the future, but
  // the adapter cannot verify it in the current state.
  //  - `failed`: Indicates a breakpoint was not able to be verified, and the
  //  adapter does not believe it can be verified without intervention.
  //
  // Must be one of the following enumeration values:
  // 'pending', 'failed'
  optional<string> reason;
  // The source where the breakpoint is located.
  optional<Source> source;
  // If true, the breakpoint could be set (but not necessarily at the desired
  // location).
  boolean verified;
};

DAP_DECLARE_STRUCT_TYPEINFO(Breakpoint);

// The event indicates that some information about a breakpoint has changed.
struct BreakpointEvent : public Event {
  // The `id` attribute is used to find the target breakpoint, the other
  // attributes are used as the new values.
  Breakpoint breakpoint;
  // The reason for the event.
  //
  // May be one of the following enumeration values:
  // 'changed', 'new', 'removed'
  string reason;
};

DAP_DECLARE_STRUCT_TYPEINFO(BreakpointEvent);

// Properties of a breakpoint location returned from the `breakpointLocations`
// request.
struct BreakpointLocation {
  // The start position of a breakpoint location. Position is measured in UTF-16
  // code units and the client capability `columnsStartAt1` determines whether
  // it is 0- or 1-based.
  optional<integer> column;
  // The end position of a breakpoint location (if the location covers a range).
  // Position is measured in UTF-16 code units and the client capability
  // `columnsStartAt1` determines whether it is 0- or 1-based.
  optional<integer> endColumn;
  // The end line of breakpoint location if the location covers a range.
  optional<integer> endLine;
  // Start line of breakpoint location.
  integer line;
};

DAP_DECLARE_STRUCT_TYPEINFO(BreakpointLocation);

// Response to `breakpointLocations` request.
// Contains possible locations for source breakpoints.
struct BreakpointLocationsResponse : public Response {
  // Sorted set of possible breakpoint locations.
  array<BreakpointLocation> breakpoints;
};

DAP_DECLARE_STRUCT_TYPEINFO(BreakpointLocationsResponse);

// The `breakpointLocations` request returns all possible locations for source
// breakpoints in a given range. Clients should only call this request if the
// corresponding capability `supportsBreakpointLocationsRequest` is true.
struct BreakpointLocationsRequest : public Request {
  using Response = BreakpointLocationsResponse;
  // Start position within `line` to search possible breakpoint locations in. It
  // is measured in UTF-16 code units and the client capability
  // `columnsStartAt1` determines whether it is 0- or 1-based. If no column is
  // given, the first position in the start line is assumed.
  optional<integer> column;
  // End position within `endLine` to search possible breakpoint locations in.
  // It is measured in UTF-16 code units and the client capability
  // `columnsStartAt1` determines whether it is 0- or 1-based. If no end column
  // is given, the last position in the end line is assumed.
  optional<integer> endColumn;
  // End line of range to search possible breakpoint locations in. If no end
  // line is given, then the end line is assumed to be the start line.
  optional<integer> endLine;
  // Start line of range to search possible breakpoint locations in. If only the
  // line is specified, the request returns all possible locations in that line.
  integer line;
  // The source location of the breakpoints; either `source.path` or
  // `source.sourceReference` must be specified.
  Source source;
};

DAP_DECLARE_STRUCT_TYPEINFO(BreakpointLocationsRequest);

// Response to `cancel` request. This is just an acknowledgement, so no body
// field is required.
struct CancelResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(CancelResponse);

// The `cancel` request is used by the client in two situations:
// - to indicate that it is no longer interested in the result produced by a
// specific request issued earlier
// - to cancel a progress sequence.
// Clients should only call this request if the corresponding capability
// `supportsCancelRequest` is true. This request has a hint characteristic: a
// debug adapter can only be expected to make a 'best effort' in honoring this
// request but there are no guarantees. The `cancel` request may return an error
// if it could not cancel an operation but a client should refrain from
// presenting this error to end users. The request that got cancelled still
// needs to send a response back. This can either be a normal result (`success`
// attribute true) or an error response (`success` attribute false and the
// `message` set to `cancelled`). Returning partial results from a cancelled
// request is possible but please note that a client has no generic way for
// detecting that a response is partial or not. The progress that got cancelled
// still needs to send a `progressEnd` event back.
//  A client should not assume that progress just got cancelled after sending
//  the `cancel` request.
struct CancelRequest : public Request {
  using Response = CancelResponse;
  // The ID (attribute `progressId`) of the progress to cancel. If missing no
  // progress is cancelled. Both a `requestId` and a `progressId` can be
  // specified in one request.
  optional<string> progressId;
  // The ID (attribute `seq`) of the request to cancel. If missing no request is
  // cancelled. Both a `requestId` and a `progressId` can be specified in one
  // request.
  optional<integer> requestId;
};

DAP_DECLARE_STRUCT_TYPEINFO(CancelRequest);

// A `ColumnDescriptor` specifies what module attribute to show in a column of
// the modules view, how to format it, and what the column's label should be. It
// is only used if the underlying UI actually supports this level of
// customization.
struct ColumnDescriptor {
  // Name of the attribute rendered in this column.
  string attributeName;
  // Format to use for the rendered values in this column. TBD how the format
  // strings looks like.
  optional<string> format;
  // Header UI label of column.
  string label;
  // Datatype of values in this column. Defaults to `string` if not specified.
  //
  // Must be one of the following enumeration values:
  // 'string', 'number', 'boolean', 'unixTimestampUTC'
  optional<string> type;
  // Width of this column in characters (hint only).
  optional<integer> width;
};

DAP_DECLARE_STRUCT_TYPEINFO(ColumnDescriptor);

// Describes one or more type of breakpoint a `BreakpointMode` applies to. This
// is a non-exhaustive enumeration and may expand as future breakpoint types are
// added.
using BreakpointModeApplicability = string;

// A `BreakpointMode` is provided as a option when setting breakpoints on
// sources or instructions.
struct BreakpointMode {
  // Describes one or more type of breakpoint this mode applies to.
  array<BreakpointModeApplicability> appliesTo;
  // A help text providing additional information about the breakpoint mode.
  // This string is typically shown as a hover and can be translated.
  optional<string> description;
  // The name of the breakpoint mode. This is shown in the UI.
  string label;
  // The internal ID of the mode. This value is passed to the `setBreakpoints`
  // request.
  string mode;
};

DAP_DECLARE_STRUCT_TYPEINFO(BreakpointMode);

// An `ExceptionBreakpointsFilter` is shown in the UI as an filter option for
// configuring how exceptions are dealt with.
struct ExceptionBreakpointsFilter {
  // A help text providing information about the condition. This string is shown
  // as the placeholder text for a text box and can be translated.
  optional<string> conditionDescription;
  // Initial value of the filter option. If not specified a value false is
  // assumed.
  optional<boolean> def;
  // A help text providing additional information about the exception filter.
  // This string is typically shown as a hover and can be translated.
  optional<string> description;
  // The internal ID of the filter option. This value is passed to the
  // `setExceptionBreakpoints` request.
  string filter;
  // The name of the filter option. This is shown in the UI.
  string label;
  // Controls whether a condition can be specified for this filter option. If
  // false or missing, a condition can not be set.
  optional<boolean> supportsCondition;
};

DAP_DECLARE_STRUCT_TYPEINFO(ExceptionBreakpointsFilter);

// Information about the capabilities of a debug adapter.
struct Capabilities {
  // The set of additional module information exposed by the debug adapter.
  optional<array<ColumnDescriptor>> additionalModuleColumns;
  // Modes of breakpoints supported by the debug adapter, such as 'hardware' or
  // 'software'. If present, the client may allow the user to select a mode and
  // include it in its `setBreakpoints` request.
  //
  // Clients may present the first applicable mode in this array as the
  // 'default' mode in gestures that set breakpoints.
  optional<array<BreakpointMode>> breakpointModes;
  // The set of characters that should trigger completion in a REPL. If not
  // specified, the UI should assume the `.` character.
  optional<array<string>> completionTriggerCharacters;
  // Available exception filter options for the `setExceptionBreakpoints`
  // request.
  optional<array<ExceptionBreakpointsFilter>> exceptionBreakpointFilters;
  // The debug adapter supports the `suspendDebuggee` attribute on the
  // `disconnect` request.
  optional<boolean> supportSuspendDebuggee;
  // The debug adapter supports the `terminateDebuggee` attribute on the
  // `disconnect` request.
  optional<boolean> supportTerminateDebuggee;
  // Checksum algorithms supported by the debug adapter.
  optional<array<ChecksumAlgorithm>> supportedChecksumAlgorithms;
  // The debug adapter supports the `breakpointLocations` request.
  optional<boolean> supportsBreakpointLocationsRequest;
  // The debug adapter supports the `cancel` request.
  optional<boolean> supportsCancelRequest;
  // The debug adapter supports the `clipboard` context value in the `evaluate`
  // request.
  optional<boolean> supportsClipboardContext;
  // The debug adapter supports the `completions` request.
  optional<boolean> supportsCompletionsRequest;
  // The debug adapter supports conditional breakpoints.
  optional<boolean> supportsConditionalBreakpoints;
  // The debug adapter supports the `configurationDone` request.
  optional<boolean> supportsConfigurationDoneRequest;
  // The debug adapter supports data breakpoints.
  optional<boolean> supportsDataBreakpoints;
  // The debug adapter supports the delayed loading of parts of the stack, which
  // requires that both the `startFrame` and `levels` arguments and the
  // `totalFrames` result of the `stackTrace` request are supported.
  optional<boolean> supportsDelayedStackTraceLoading;
  // The debug adapter supports the `disassemble` request.
  optional<boolean> supportsDisassembleRequest;
  // The debug adapter supports a (side effect free) `evaluate` request for data
  // hovers.
  optional<boolean> supportsEvaluateForHovers;
  // The debug adapter supports `filterOptions` as an argument on the
  // `setExceptionBreakpoints` request.
  optional<boolean> supportsExceptionFilterOptions;
  // The debug adapter supports the `exceptionInfo` request.
  optional<boolean> supportsExceptionInfoRequest;
  // The debug adapter supports `exceptionOptions` on the
  // `setExceptionBreakpoints` request.
  optional<boolean> supportsExceptionOptions;
  // The debug adapter supports function breakpoints.
  optional<boolean> supportsFunctionBreakpoints;
  // The debug adapter supports the `gotoTargets` request.
  optional<boolean> supportsGotoTargetsRequest;
  // The debug adapter supports breakpoints that break execution after a
  // specified number of hits.
  optional<boolean> supportsHitConditionalBreakpoints;
  // The debug adapter supports adding breakpoints based on instruction
  // references.
  optional<boolean> supportsInstructionBreakpoints;
  // The debug adapter supports the `loadedSources` request.
  optional<boolean> supportsLoadedSourcesRequest;
  // The debug adapter supports log points by interpreting the `logMessage`
  // attribute of the `SourceBreakpoint`.
  optional<boolean> supportsLogPoints;
  // The debug adapter supports the `modules` request.
  optional<boolean> supportsModulesRequest;
  // The debug adapter supports the `readMemory` request.
  optional<boolean> supportsReadMemoryRequest;
  // The debug adapter supports restarting a frame.
  optional<boolean> supportsRestartFrame;
  // The debug adapter supports the `restart` request. In this case a client
  // should not implement `restart` by terminating and relaunching the adapter
  // but by calling the `restart` request.
  optional<boolean> supportsRestartRequest;
  // The debug adapter supports the `setExpression` request.
  optional<boolean> supportsSetExpression;
  // The debug adapter supports setting a variable to a value.
  optional<boolean> supportsSetVariable;
  // The debug adapter supports the `singleThread` property on the execution
  // requests (`continue`, `next`, `stepIn`, `stepOut`, `reverseContinue`,
  // `stepBack`).
  optional<boolean> supportsSingleThreadExecutionRequests;
  // The debug adapter supports stepping back via the `stepBack` and
  // `reverseContinue` requests.
  optional<boolean> supportsStepBack;
  // The debug adapter supports the `stepInTargets` request.
  optional<boolean> supportsStepInTargetsRequest;
  // The debug adapter supports stepping granularities (argument `granularity`)
  // for the stepping requests.
  optional<boolean> supportsSteppingGranularity;
  // The debug adapter supports the `terminate` request.
  optional<boolean> supportsTerminateRequest;
  // The debug adapter supports the `terminateThreads` request.
  optional<boolean> supportsTerminateThreadsRequest;
  // The debug adapter supports a `format` attribute on the `stackTrace`,
  // `variables`, and `evaluate` requests.
  optional<boolean> supportsValueFormattingOptions;
  // The debug adapter supports the `writeMemory` request.
  optional<boolean> supportsWriteMemoryRequest;
};

DAP_DECLARE_STRUCT_TYPEINFO(Capabilities);

// The event indicates that one or more capabilities have changed.
// Since the capabilities are dependent on the client and its UI, it might not
// be possible to change that at random times (or too late). Consequently this
// event has a hint characteristic: a client can only be expected to make a
// 'best effort' in honoring individual capabilities but there are no
// guarantees. Only changed capabilities need to be included, all other
// capabilities keep their values.
struct CapabilitiesEvent : public Event {
  // The set of updated capabilities.
  Capabilities capabilities;
};

DAP_DECLARE_STRUCT_TYPEINFO(CapabilitiesEvent);

// Some predefined types for the CompletionItem. Please note that not all
// clients have specific icons for all of them.
//
// Must be one of the following enumeration values:
// 'method', 'function', 'constructor', 'field', 'variable', 'class',
// 'interface', 'module', 'property', 'unit', 'value', 'enum', 'keyword',
// 'snippet', 'text', 'color', 'file', 'reference', 'customcolor'
using CompletionItemType = string;

// `CompletionItems` are the suggestions returned from the `completions`
// request.
struct CompletionItem {
  // A human-readable string with additional information about this item, like
  // type or symbol information.
  optional<string> detail;
  // The label of this completion item. By default this is also the text that is
  // inserted when selecting this completion.
  string label;
  // Length determines how many characters are overwritten by the completion
  // text and it is measured in UTF-16 code units. If missing the value 0 is
  // assumed which results in the completion text being inserted.
  optional<integer> length;
  // Determines the length of the new selection after the text has been inserted
  // (or replaced) and it is measured in UTF-16 code units. The selection can
  // not extend beyond the bounds of the completion text. If omitted the length
  // is assumed to be 0.
  optional<integer> selectionLength;
  // Determines the start of the new selection after the text has been inserted
  // (or replaced). `selectionStart` is measured in UTF-16 code units and must
  // be in the range 0 and length of the completion text. If omitted the
  // selection starts at the end of the completion text.
  optional<integer> selectionStart;
  // A string that should be used when comparing this item with other items. If
  // not returned or an empty string, the `label` is used instead.
  optional<string> sortText;
  // Start position (within the `text` attribute of the `completions` request)
  // where the completion text is added. The position is measured in UTF-16 code
  // units and the client capability `columnsStartAt1` determines whether it is
  // 0- or 1-based. If the start position is omitted the text is added at the
  // location specified by the `column` attribute of the `completions` request.
  optional<integer> start;
  // If text is returned and not an empty string, then it is inserted instead of
  // the label.
  optional<string> text;
  // The item's type. Typically the client uses this information to render the
  // item in the UI with an icon.
  optional<CompletionItemType> type;
};

DAP_DECLARE_STRUCT_TYPEINFO(CompletionItem);

// Response to `completions` request.
struct CompletionsResponse : public Response {
  // The possible completions for .
  array<CompletionItem> targets;
};

DAP_DECLARE_STRUCT_TYPEINFO(CompletionsResponse);

// Returns a list of possible completions for a given caret position and text.
// Clients should only call this request if the corresponding capability
// `supportsCompletionsRequest` is true.
struct CompletionsRequest : public Request {
  using Response = CompletionsResponse;
  // The position within `text` for which to determine the completion proposals.
  // It is measured in UTF-16 code units and the client capability
  // `columnsStartAt1` determines whether it is 0- or 1-based.
  integer column;
  // Returns completions in the scope of this stack frame. If not specified, the
  // completions are returned for the global scope.
  optional<integer> frameId;
  // A line for which to determine the completion proposals. If missing the
  // first line of the text is assumed.
  optional<integer> line;
  // One or more source lines. Typically this is the text users have typed into
  // the debug console before they asked for completion.
  string text;
};

DAP_DECLARE_STRUCT_TYPEINFO(CompletionsRequest);

// Response to `configurationDone` request. This is just an acknowledgement, so
// no body field is required.
struct ConfigurationDoneResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(ConfigurationDoneResponse);

// This request indicates that the client has finished initialization of the
// debug adapter. So it is the last request in the sequence of configuration
// requests (which was started by the `initialized` event). Clients should only
// call this request if the corresponding capability
// `supportsConfigurationDoneRequest` is true.
struct ConfigurationDoneRequest : public Request {
  using Response = ConfigurationDoneResponse;
};

DAP_DECLARE_STRUCT_TYPEINFO(ConfigurationDoneRequest);

// Response to `continue` request.
struct ContinueResponse : public Response {
  // The value true (or a missing property) signals to the client that all
  // threads have been resumed. The value false indicates that not all threads
  // were resumed.
  optional<boolean> allThreadsContinued;
};

DAP_DECLARE_STRUCT_TYPEINFO(ContinueResponse);

// The request resumes execution of all threads. If the debug adapter supports
// single thread execution (see capability
// `supportsSingleThreadExecutionRequests`), setting the `singleThread` argument
// to true resumes only the specified thread. If not all threads were resumed,
// the `allThreadsContinued` attribute of the response should be set to false.
struct ContinueRequest : public Request {
  using Response = ContinueResponse;
  // If this flag is true, execution is resumed only for the thread with given
  // `threadId`.
  optional<boolean> singleThread;
  // Specifies the active thread. If the debug adapter supports single thread
  // execution (see `supportsSingleThreadExecutionRequests`) and the argument
  // `singleThread` is true, only the thread with this ID is resumed.
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ContinueRequest);

// The event indicates that the execution of the debuggee has continued.
// Please note: a debug adapter is not expected to send this event in response
// to a request that implies that execution continues, e.g. `launch` or
// `continue`. It is only necessary to send a `continued` event if there was no
// previous request that implied this.
struct ContinuedEvent : public Event {
  // If `allThreadsContinued` is true, a debug adapter can announce that all
  // threads have continued.
  optional<boolean> allThreadsContinued;
  // The thread which was continued.
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ContinuedEvent);

// This enumeration defines all possible access types for data breakpoints.
//
// Must be one of the following enumeration values:
// 'read', 'write', 'readWrite'
using DataBreakpointAccessType = string;

// Response to `dataBreakpointInfo` request.
struct DataBreakpointInfoResponse : public Response {
  // Attribute lists the available access types for a potential data breakpoint.
  // A UI client could surface this information.
  optional<array<DataBreakpointAccessType>> accessTypes;
  // Attribute indicates that a potential data breakpoint could be persisted
  // across sessions.
  optional<boolean> canPersist;
  // An identifier for the data on which a data breakpoint can be registered
  // with the `setDataBreakpoints` request or null if no data breakpoint is
  // available. If a `variablesReference` or `frameId` is passed, the `dataId`
  // is valid in the current suspended state, otherwise it's valid indefinitely.
  // See 'Lifetime of Object References' in the Overview section for details.
  // Breakpoints set using the `dataId` in the `setDataBreakpoints` request may
  // outlive the lifetime of the associated `dataId`.
  variant<string, null> dataId;
  // UI string that describes on what data the breakpoint is set on or why a
  // data breakpoint is not available.
  string description;
};

DAP_DECLARE_STRUCT_TYPEINFO(DataBreakpointInfoResponse);

// Obtains information on a possible data breakpoint that could be set on an
// expression or variable. Clients should only call this request if the
// corresponding capability `supportsDataBreakpoints` is true.
struct DataBreakpointInfoRequest : public Request {
  using Response = DataBreakpointInfoResponse;
  // When `name` is an expression, evaluate it in the scope of this stack frame.
  // If not specified, the expression is evaluated in the global scope. When
  // `variablesReference` is specified, this property has no effect.
  optional<integer> frameId;
  // The mode of the desired breakpoint. If defined, this must be one of the
  // `breakpointModes` the debug adapter advertised in its `Capabilities`.
  optional<string> mode;
  // The name of the variable's child to obtain data breakpoint information for.
  // If `variablesReference` isn't specified, this can be an expression.
  string name;
  // Reference to the variable container if the data breakpoint is requested for
  // a child of the container. The `variablesReference` must have been obtained
  // in the current suspended state. See 'Lifetime of Object References' in the
  // Overview section for details.
  optional<integer> variablesReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(DataBreakpointInfoRequest);

// Represents a single disassembled instruction.
struct DisassembledInstruction {
  // The address of the instruction. Treated as a hex value if prefixed with
  // `0x`, or as a decimal value otherwise.
  string address;
  // The column within the line that corresponds to this instruction, if any.
  optional<integer> column;
  // The end column of the range that corresponds to this instruction, if any.
  optional<integer> endColumn;
  // The end line of the range that corresponds to this instruction, if any.
  optional<integer> endLine;
  // Text representing the instruction and its operands, in an
  // implementation-defined format.
  string instruction;
  // Raw bytes representing the instruction and its operands, in an
  // implementation-defined format.
  optional<string> instructionBytes;
  // The line within the source location that corresponds to this instruction,
  // if any.
  optional<integer> line;
  // Source location that corresponds to this instruction, if any.
  // Should always be set (if available) on the first instruction returned,
  // but can be omitted afterwards if this instruction maps to the same source
  // file as the previous instruction.
  optional<Source> location;
  // A hint for how to present the instruction in the UI.
  //
  // A value of `invalid` may be used to indicate this instruction is 'filler'
  // and cannot be reached by the program. For example, unreadable memory
  // addresses may be presented is 'invalid.'
  //
  // Must be one of the following enumeration values:
  // 'normal', 'invalid'
  optional<string> presentationHint;
  // Name of the symbol that corresponds with the location of this instruction,
  // if any.
  optional<string> symbol;
};

DAP_DECLARE_STRUCT_TYPEINFO(DisassembledInstruction);

// Response to `disassemble` request.
struct DisassembleResponse : public Response {
  // The list of disassembled instructions.
  array<DisassembledInstruction> instructions;
};

DAP_DECLARE_STRUCT_TYPEINFO(DisassembleResponse);

// Disassembles code stored at the provided location.
// Clients should only call this request if the corresponding capability
// `supportsDisassembleRequest` is true.
struct DisassembleRequest : public Request {
  using Response = DisassembleResponse;
  // Number of instructions to disassemble starting at the specified location
  // and offset. An adapter must return exactly this number of instructions -
  // any unavailable instructions should be replaced with an
  // implementation-defined 'invalid instruction' value.
  integer instructionCount;
  // Offset (in instructions) to be applied after the byte offset (if any)
  // before disassembling. Can be negative.
  optional<integer> instructionOffset;
  // Memory reference to the base location containing the instructions to
  // disassemble.
  string memoryReference;
  // Offset (in bytes) to be applied to the reference location before
  // disassembling. Can be negative.
  optional<integer> offset;
  // If true, the adapter should attempt to resolve memory addresses and other
  // values to symbolic names.
  optional<boolean> resolveSymbols;
};

DAP_DECLARE_STRUCT_TYPEINFO(DisassembleRequest);

// Response to `disconnect` request. This is just an acknowledgement, so no body
// field is required.
struct DisconnectResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(DisconnectResponse);

// The `disconnect` request asks the debug adapter to disconnect from the
// debuggee (thus ending the debug session) and then to shut down itself (the
// debug adapter). In addition, the debug adapter must terminate the debuggee if
// it was started with the `launch` request. If an `attach` request was used to
// connect to the debuggee, then the debug adapter must not terminate the
// debuggee. This implicit behavior of when to terminate the debuggee can be
// overridden with the `terminateDebuggee` argument (which is only supported by
// a debug adapter if the corresponding capability `supportTerminateDebuggee` is
// true).
struct DisconnectRequest : public Request {
  using Response = DisconnectResponse;
  // A value of true indicates that this `disconnect` request is part of a
  // restart sequence.
  optional<boolean> restart;
  // Indicates whether the debuggee should stay suspended when the debugger is
  // disconnected. If unspecified, the debuggee should resume execution. The
  // attribute is only honored by a debug adapter if the corresponding
  // capability `supportSuspendDebuggee` is true.
  optional<boolean> suspendDebuggee;
  // Indicates whether the debuggee should be terminated when the debugger is
  // disconnected. If unspecified, the debug adapter is free to do whatever it
  // thinks is best. The attribute is only honored by a debug adapter if the
  // corresponding capability `supportTerminateDebuggee` is true.
  optional<boolean> terminateDebuggee;
};

DAP_DECLARE_STRUCT_TYPEINFO(DisconnectRequest);

// A structured message object. Used to return errors from requests.
struct Message {
  // A format string for the message. Embedded variables have the form `{name}`.
  // If variable name starts with an underscore character, the variable does not
  // contain user data (PII) and can be safely used for telemetry purposes.
  string format;
  // Unique (within a debug adapter implementation) identifier for the message.
  // The purpose of these error IDs is to help extension authors that have the
  // requirement that every user visible error message needs a corresponding
  // error number, so that users or customer support can find information about
  // the specific error more easily.
  integer id;
  // If true send to telemetry.
  optional<boolean> sendTelemetry;
  // If true show user.
  optional<boolean> showUser;
  // A url where additional information about this message can be found.
  optional<string> url;
  // A label that is presented to the user as the UI for opening the url.
  optional<string> urlLabel;
  // An object used as a dictionary for looking up the variables in the format
  // string.
  optional<object> variables;
};

DAP_DECLARE_STRUCT_TYPEINFO(Message);

// On error (whenever `success` is false), the body can provide more details.
struct ErrorResponse : public Response {
  // A structured error message.
  optional<Message> error;
};

DAP_DECLARE_STRUCT_TYPEINFO(ErrorResponse);

// Properties of a variable that can be used to determine how to render the
// variable in the UI.
struct VariablePresentationHint {
  // Set of attributes represented as an array of strings. Before introducing
  // additional values, try to use the listed values.
  optional<array<string>> attributes;
  // The kind of variable. Before introducing additional values, try to use the
  // listed values.
  //
  // May be one of the following enumeration values:
  // 'property', 'method', 'class', 'data', 'event', 'baseClass', 'innerClass',
  // 'interface', 'mostDerivedClass', 'virtual', 'dataBreakpoint'
  optional<string> kind;
  // If true, clients can present the variable with a UI that supports a
  // specific gesture to trigger its evaluation. This mechanism can be used for
  // properties that require executing code when retrieving their value and
  // where the code execution can be expensive and/or produce side-effects. A
  // typical example are properties based on a getter function. Please note that
  // in addition to the `lazy` flag, the variable's `variablesReference` is
  // expected to refer to a variable that will provide the value through another
  // `variable` request.
  optional<boolean> lazy;
  // Visibility of variable. Before introducing additional values, try to use
  // the listed values.
  //
  // May be one of the following enumeration values:
  // 'public', 'private', 'protected', 'internal', 'final'
  optional<string> visibility;
};

DAP_DECLARE_STRUCT_TYPEINFO(VariablePresentationHint);

// Response to `evaluate` request.
struct EvaluateResponse : public Response {
  // The number of indexed child variables.
  // The client can use this information to present the variables in a paged UI
  // and fetch them in chunks. The value should be less than or equal to
  // 2147483647 (2^31-1).
  optional<integer> indexedVariables;
  // A memory reference to a location appropriate for this result.
  // For pointer type eval results, this is generally a reference to the memory
  // address contained in the pointer. This attribute may be returned by a debug
  // adapter if corresponding capability `supportsMemoryReferences` is true.
  optional<string> memoryReference;
  // The number of named child variables.
  // The client can use this information to present the variables in a paged UI
  // and fetch them in chunks. The value should be less than or equal to
  // 2147483647 (2^31-1).
  optional<integer> namedVariables;
  // Properties of an evaluate result that can be used to determine how to
  // render the result in the UI.
  optional<VariablePresentationHint> presentationHint;
  // The result of the evaluate request.
  string result;
  // The type of the evaluate result.
  // This attribute should only be returned by a debug adapter if the
  // corresponding capability `supportsVariableType` is true.
  optional<string> type;
  // If `variablesReference` is > 0, the evaluate result is structured and its
  // children can be retrieved by passing `variablesReference` to the
  // `variables` request as long as execution remains suspended. See 'Lifetime
  // of Object References' in the Overview section for details.
  integer variablesReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(EvaluateResponse);

// Provides formatting information for a value.
struct ValueFormat {
  // Display the value in hex.
  optional<boolean> hex;
};

DAP_DECLARE_STRUCT_TYPEINFO(ValueFormat);

// Evaluates the given expression in the context of the topmost stack frame.
// The expression has access to any variables and arguments that are in scope.
struct EvaluateRequest : public Request {
  using Response = EvaluateResponse;
  // The context in which the evaluate request is used.
  //
  // May be one of the following enumeration values:
  // 'watch', 'repl', 'hover', 'clipboard', 'variables'
  optional<string> context;
  // The expression to evaluate.
  string expression;
  // Specifies details on how to format the result.
  // The attribute is only honored by a debug adapter if the corresponding
  // capability `supportsValueFormattingOptions` is true.
  optional<ValueFormat> format;
  // Evaluate the expression in the scope of this stack frame. If not specified,
  // the expression is evaluated in the global scope.
  optional<integer> frameId;
};

DAP_DECLARE_STRUCT_TYPEINFO(EvaluateRequest);

// This enumeration defines all possible conditions when a thrown exception
// should result in a break. never: never breaks, always: always breaks,
// unhandled: breaks when exception unhandled,
// userUnhandled: breaks if the exception is not handled by user code.
//
// Must be one of the following enumeration values:
// 'never', 'always', 'unhandled', 'userUnhandled'
using ExceptionBreakMode = string;

// Detailed information about an exception that has occurred.
struct ExceptionDetails {
  // An expression that can be evaluated in the current scope to obtain the
  // exception object.
  optional<string> evaluateName;
  // Fully-qualified type name of the exception object.
  optional<string> fullTypeName;
  // Details of the exception contained by this exception, if any.
  optional<array<ExceptionDetails>> innerException;
  // Message contained in the exception.
  optional<string> message;
  // Stack trace at the time the exception was thrown.
  optional<string> stackTrace;
  // Short type name of the exception object.
  optional<string> typeName;
};

DAP_DECLARE_STRUCT_TYPEINFO(ExceptionDetails);

// Response to `exceptionInfo` request.
struct ExceptionInfoResponse : public Response {
  // Mode that caused the exception notification to be raised.
  ExceptionBreakMode breakMode = "never";
  // Descriptive text for the exception.
  optional<string> description;
  // Detailed information about the exception.
  optional<ExceptionDetails> details;
  // ID of the exception that was thrown.
  string exceptionId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ExceptionInfoResponse);

// Retrieves the details of the exception that caused this event to be raised.
// Clients should only call this request if the corresponding capability
// `supportsExceptionInfoRequest` is true.
struct ExceptionInfoRequest : public Request {
  using Response = ExceptionInfoResponse;
  // Thread for which exception information should be retrieved.
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ExceptionInfoRequest);

// The event indicates that the debuggee has exited and returns its exit code.
struct ExitedEvent : public Event {
  // The exit code returned from the debuggee.
  integer exitCode;
};

DAP_DECLARE_STRUCT_TYPEINFO(ExitedEvent);

// Response to `goto` request. This is just an acknowledgement, so no body field
// is required.
struct GotoResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(GotoResponse);

// The request sets the location where the debuggee will continue to run.
// This makes it possible to skip the execution of code or to execute code
// again. The code between the current location and the goto target is not
// executed but skipped. The debug adapter first sends the response and then a
// `stopped` event with reason `goto`. Clients should only call this request if
// the corresponding capability `supportsGotoTargetsRequest` is true (because
// only then goto targets exist that can be passed as arguments).
struct GotoRequest : public Request {
  using Response = GotoResponse;
  // The location where the debuggee will continue to run.
  integer targetId;
  // Set the goto target for this thread.
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(GotoRequest);

// A `GotoTarget` describes a code location that can be used as a target in the
// `goto` request. The possible goto targets can be determined via the
// `gotoTargets` request.
struct GotoTarget {
  // The column of the goto target.
  optional<integer> column;
  // The end column of the range covered by the goto target.
  optional<integer> endColumn;
  // The end line of the range covered by the goto target.
  optional<integer> endLine;
  // Unique identifier for a goto target. This is used in the `goto` request.
  integer id;
  // A memory reference for the instruction pointer value represented by this
  // target.
  optional<string> instructionPointerReference;
  // The name of the goto target (shown in the UI).
  string label;
  // The line of the goto target.
  integer line;
};

DAP_DECLARE_STRUCT_TYPEINFO(GotoTarget);

// Response to `gotoTargets` request.
struct GotoTargetsResponse : public Response {
  // The possible goto targets of the specified location.
  array<GotoTarget> targets;
};

DAP_DECLARE_STRUCT_TYPEINFO(GotoTargetsResponse);

// This request retrieves the possible goto targets for the specified source
// location. These targets can be used in the `goto` request. Clients should
// only call this request if the corresponding capability
// `supportsGotoTargetsRequest` is true.
struct GotoTargetsRequest : public Request {
  using Response = GotoTargetsResponse;
  // The position within `line` for which the goto targets are determined. It is
  // measured in UTF-16 code units and the client capability `columnsStartAt1`
  // determines whether it is 0- or 1-based.
  optional<integer> column;
  // The line location for which the goto targets are determined.
  integer line;
  // The source location for which the goto targets are determined.
  Source source;
};

DAP_DECLARE_STRUCT_TYPEINFO(GotoTargetsRequest);

// Response to `initialize` request.
struct InitializeResponse : public Response {
  // The set of additional module information exposed by the debug adapter.
  optional<array<ColumnDescriptor>> additionalModuleColumns;
  // Modes of breakpoints supported by the debug adapter, such as 'hardware' or
  // 'software'. If present, the client may allow the user to select a mode and
  // include it in its `setBreakpoints` request.
  //
  // Clients may present the first applicable mode in this array as the
  // 'default' mode in gestures that set breakpoints.
  optional<array<BreakpointMode>> breakpointModes;
  // The set of characters that should trigger completion in a REPL. If not
  // specified, the UI should assume the `.` character.
  optional<array<string>> completionTriggerCharacters;
  // Available exception filter options for the `setExceptionBreakpoints`
  // request.
  optional<array<ExceptionBreakpointsFilter>> exceptionBreakpointFilters;
  // The debug adapter supports the `suspendDebuggee` attribute on the
  // `disconnect` request.
  optional<boolean> supportSuspendDebuggee;
  // The debug adapter supports the `terminateDebuggee` attribute on the
  // `disconnect` request.
  optional<boolean> supportTerminateDebuggee;
  // Checksum algorithms supported by the debug adapter.
  optional<array<ChecksumAlgorithm>> supportedChecksumAlgorithms;
  // The debug adapter supports the `breakpointLocations` request.
  optional<boolean> supportsBreakpointLocationsRequest;
  // The debug adapter supports the `cancel` request.
  optional<boolean> supportsCancelRequest;
  // The debug adapter supports the `clipboard` context value in the `evaluate`
  // request.
  optional<boolean> supportsClipboardContext;
  // The debug adapter supports the `completions` request.
  optional<boolean> supportsCompletionsRequest;
  // The debug adapter supports conditional breakpoints.
  optional<boolean> supportsConditionalBreakpoints;
  // The debug adapter supports the `configurationDone` request.
  optional<boolean> supportsConfigurationDoneRequest;
  // The debug adapter supports data breakpoints.
  optional<boolean> supportsDataBreakpoints;
  // The debug adapter supports the delayed loading of parts of the stack, which
  // requires that both the `startFrame` and `levels` arguments and the
  // `totalFrames` result of the `stackTrace` request are supported.
  optional<boolean> supportsDelayedStackTraceLoading;
  // The debug adapter supports the `disassemble` request.
  optional<boolean> supportsDisassembleRequest;
  // The debug adapter supports a (side effect free) `evaluate` request for data
  // hovers.
  optional<boolean> supportsEvaluateForHovers;
  // The debug adapter supports `filterOptions` as an argument on the
  // `setExceptionBreakpoints` request.
  optional<boolean> supportsExceptionFilterOptions;
  // The debug adapter supports the `exceptionInfo` request.
  optional<boolean> supportsExceptionInfoRequest;
  // The debug adapter supports `exceptionOptions` on the
  // `setExceptionBreakpoints` request.
  optional<boolean> supportsExceptionOptions;
  // The debug adapter supports function breakpoints.
  optional<boolean> supportsFunctionBreakpoints;
  // The debug adapter supports the `gotoTargets` request.
  optional<boolean> supportsGotoTargetsRequest;
  // The debug adapter supports breakpoints that break execution after a
  // specified number of hits.
  optional<boolean> supportsHitConditionalBreakpoints;
  // The debug adapter supports adding breakpoints based on instruction
  // references.
  optional<boolean> supportsInstructionBreakpoints;
  // The debug adapter supports the `loadedSources` request.
  optional<boolean> supportsLoadedSourcesRequest;
  // The debug adapter supports log points by interpreting the `logMessage`
  // attribute of the `SourceBreakpoint`.
  optional<boolean> supportsLogPoints;
  // The debug adapter supports the `modules` request.
  optional<boolean> supportsModulesRequest;
  // The debug adapter supports the `readMemory` request.
  optional<boolean> supportsReadMemoryRequest;
  // The debug adapter supports restarting a frame.
  optional<boolean> supportsRestartFrame;
  // The debug adapter supports the `restart` request. In this case a client
  // should not implement `restart` by terminating and relaunching the adapter
  // but by calling the `restart` request.
  optional<boolean> supportsRestartRequest;
  // The debug adapter supports the `setExpression` request.
  optional<boolean> supportsSetExpression;
  // The debug adapter supports setting a variable to a value.
  optional<boolean> supportsSetVariable;
  // The debug adapter supports the `singleThread` property on the execution
  // requests (`continue`, `next`, `stepIn`, `stepOut`, `reverseContinue`,
  // `stepBack`).
  optional<boolean> supportsSingleThreadExecutionRequests;
  // The debug adapter supports stepping back via the `stepBack` and
  // `reverseContinue` requests.
  optional<boolean> supportsStepBack;
  // The debug adapter supports the `stepInTargets` request.
  optional<boolean> supportsStepInTargetsRequest;
  // The debug adapter supports stepping granularities (argument `granularity`)
  // for the stepping requests.
  optional<boolean> supportsSteppingGranularity;
  // The debug adapter supports the `terminate` request.
  optional<boolean> supportsTerminateRequest;
  // The debug adapter supports the `terminateThreads` request.
  optional<boolean> supportsTerminateThreadsRequest;
  // The debug adapter supports a `format` attribute on the `stackTrace`,
  // `variables`, and `evaluate` requests.
  optional<boolean> supportsValueFormattingOptions;
  // The debug adapter supports the `writeMemory` request.
  optional<boolean> supportsWriteMemoryRequest;
};

DAP_DECLARE_STRUCT_TYPEINFO(InitializeResponse);

// The `initialize` request is sent as the first request from the client to the
// debug adapter in order to configure it with client capabilities and to
// retrieve capabilities from the debug adapter. Until the debug adapter has
// responded with an `initialize` response, the client must not send any
// additional requests or events to the debug adapter. In addition the debug
// adapter is not allowed to send any requests or events to the client until it
// has responded with an `initialize` response. The `initialize` request may
// only be sent once.
struct InitializeRequest : public Request {
  using Response = InitializeResponse;
  // The ID of the debug adapter.
  string adapterID;
  // The ID of the client using this adapter.
  optional<string> clientID;
  // The human-readable name of the client using this adapter.
  optional<string> clientName;
  // If true all column numbers are 1-based (default).
  optional<boolean> columnsStartAt1;
  // If true all line numbers are 1-based (default).
  optional<boolean> linesStartAt1;
  // The ISO-639 locale of the client using this adapter, e.g. en-US or de-CH.
  optional<string> locale;
  // Determines in what format paths are specified. The default is `path`, which
  // is the native format.
  //
  // May be one of the following enumeration values:
  // 'path', 'uri'
  optional<string> pathFormat;
  // Client supports the `argsCanBeInterpretedByShell` attribute on the
  // `runInTerminal` request.
  optional<boolean> supportsArgsCanBeInterpretedByShell;
  // Client supports the `invalidated` event.
  optional<boolean> supportsInvalidatedEvent;
  // Client supports the `memory` event.
  optional<boolean> supportsMemoryEvent;
  // Client supports memory references.
  optional<boolean> supportsMemoryReferences;
  // Client supports progress reporting.
  optional<boolean> supportsProgressReporting;
  // Client supports the `runInTerminal` request.
  optional<boolean> supportsRunInTerminalRequest;
  // Client supports the `startDebugging` request.
  optional<boolean> supportsStartDebuggingRequest;
  // Client supports the paging of variables.
  optional<boolean> supportsVariablePaging;
  // Client supports the `type` attribute for variables.
  optional<boolean> supportsVariableType;
};

DAP_DECLARE_STRUCT_TYPEINFO(InitializeRequest);

// This event indicates that the debug adapter is ready to accept configuration
// requests (e.g. `setBreakpoints`, `setExceptionBreakpoints`). A debug adapter
// is expected to send this event when it is ready to accept configuration
// requests (but not before the `initialize` request has finished). The sequence
// of events/requests is as follows:
// - adapters sends `initialized` event (after the `initialize` request has
// returned)
// - client sends zero or more `setBreakpoints` requests
// - client sends one `setFunctionBreakpoints` request (if corresponding
// capability `supportsFunctionBreakpoints` is true)
// - client sends a `setExceptionBreakpoints` request if one or more
// `exceptionBreakpointFilters` have been defined (or if
// `supportsConfigurationDoneRequest` is not true)
// - client sends other future configuration requests
// - client sends one `configurationDone` request to indicate the end of the
// configuration.
struct InitializedEvent : public Event {};

DAP_DECLARE_STRUCT_TYPEINFO(InitializedEvent);

// Logical areas that can be invalidated by the `invalidated` event.
using InvalidatedAreas = string;

// This event signals that some state in the debug adapter has changed and
// requires that the client needs to re-render the data snapshot previously
// requested. Debug adapters do not have to emit this event for runtime changes
// like stopped or thread events because in that case the client refetches the
// new state anyway. But the event can be used for example to refresh the UI
// after rendering formatting has changed in the debug adapter. This event
// should only be sent if the corresponding capability
// `supportsInvalidatedEvent` is true.
struct InvalidatedEvent : public Event {
  // Set of logical areas that got invalidated. This property has a hint
  // characteristic: a client can only be expected to make a 'best effort' in
  // honoring the areas but there are no guarantees. If this property is
  // missing, empty, or if values are not understood, the client should assume a
  // single value `all`.
  optional<array<InvalidatedAreas>> areas;
  // If specified, the client only needs to refetch data related to this stack
  // frame (and the `threadId` is ignored).
  optional<integer> stackFrameId;
  // If specified, the client only needs to refetch data related to this thread.
  optional<integer> threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(InvalidatedEvent);

// Response to `launch` request. This is just an acknowledgement, so no body
// field is required.
struct LaunchResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(LaunchResponse);

// This launch request is sent from the client to the debug adapter to start the
// debuggee with or without debugging (if `noDebug` is true). Since launching is
// debugger/runtime specific, the arguments for this request are not part of
// this specification.
struct LaunchRequest : public Request {
  using Response = LaunchResponse;
  // Arbitrary data from the previous, restarted session.
  // The data is sent as the `restart` attribute of the `terminated` event.
  // The client should leave the data intact.
  optional<variant<array<any>, boolean, integer, null, number, object, string>>
      restart;
  // If true, the launch request should launch the program without enabling
  // debugging.
  optional<boolean> noDebug;
};

DAP_DECLARE_STRUCT_TYPEINFO(LaunchRequest);

// The event indicates that some source has been added, changed, or removed from
// the set of all loaded sources.
struct LoadedSourceEvent : public Event {
  // The reason for the event.
  //
  // Must be one of the following enumeration values:
  // 'new', 'changed', 'removed'
  string reason = "new";
  // The new, changed, or removed source.
  Source source;
};

DAP_DECLARE_STRUCT_TYPEINFO(LoadedSourceEvent);

// Response to `loadedSources` request.
struct LoadedSourcesResponse : public Response {
  // Set of loaded sources.
  array<Source> sources;
};

DAP_DECLARE_STRUCT_TYPEINFO(LoadedSourcesResponse);

// Retrieves the set of all sources currently loaded by the debugged process.
// Clients should only call this request if the corresponding capability
// `supportsLoadedSourcesRequest` is true.
struct LoadedSourcesRequest : public Request {
  using Response = LoadedSourcesResponse;
};

DAP_DECLARE_STRUCT_TYPEINFO(LoadedSourcesRequest);

// This event indicates that some memory range has been updated. It should only
// be sent if the corresponding capability `supportsMemoryEvent` is true.
// Clients typically react to the event by re-issuing a `readMemory` request if
// they show the memory identified by the `memoryReference` and if the updated
// memory range overlaps the displayed range. Clients should not make
// assumptions how individual memory references relate to each other, so they
// should not assume that they are part of a single continuous address range and
// might overlap. Debug adapters can use this event to indicate that the
// contents of a memory range has changed due to some other request like
// `setVariable` or `setExpression`. Debug adapters are not expected to emit
// this event for each and every memory change of a running program, because
// that information is typically not available from debuggers and it would flood
// clients with too many events.
struct MemoryEvent : public Event {
  // Number of bytes updated.
  integer count;
  // Memory reference of a memory range that has been updated.
  string memoryReference;
  // Starting offset in bytes where memory has been updated. Can be negative.
  integer offset;
};

DAP_DECLARE_STRUCT_TYPEINFO(MemoryEvent);

// A Module object represents a row in the modules view.
// The `id` attribute identifies a module in the modules view and is used in a
// `module` event for identifying a module for adding, updating or deleting. The
// `name` attribute is used to minimally render the module in the UI.
//
// Additional attributes can be added to the module. They show up in the module
// view if they have a corresponding `ColumnDescriptor`.
//
// To avoid an unnecessary proliferation of additional attributes with similar
// semantics but different names, we recommend to re-use attributes from the
// 'recommended' list below first, and only introduce new attributes if nothing
// appropriate could be found.
struct Module {
  // Address range covered by this module.
  optional<string> addressRange;
  // Module created or modified, encoded as a RFC 3339 timestamp.
  optional<string> dateTimeStamp;
  // Unique identifier for the module.
  variant<integer, string> id;
  // True if the module is optimized.
  optional<boolean> isOptimized;
  // True if the module is considered 'user code' by a debugger that supports
  // 'Just My Code'.
  optional<boolean> isUserCode;
  // A name of the module.
  string name;
  // Logical full path to the module. The exact definition is implementation
  // defined, but usually this would be a full path to the on-disk file for the
  // module.
  optional<string> path;
  // Logical full path to the symbol file. The exact definition is
  // implementation defined.
  optional<string> symbolFilePath;
  // User-understandable description of if symbols were found for the module
  // (ex: 'Symbols Loaded', 'Symbols not found', etc.)
  optional<string> symbolStatus;
  // Version of Module.
  optional<string> version;
};

DAP_DECLARE_STRUCT_TYPEINFO(Module);

// The event indicates that some information about a module has changed.
struct ModuleEvent : public Event {
  // The new, changed, or removed module. In case of `removed` only the module
  // id is used.
  Module module;
  // The reason for the event.
  //
  // Must be one of the following enumeration values:
  // 'new', 'changed', 'removed'
  string reason = "new";
};

DAP_DECLARE_STRUCT_TYPEINFO(ModuleEvent);

// Response to `modules` request.
struct ModulesResponse : public Response {
  // All modules or range of modules.
  array<Module> modules;
  // The total number of modules available.
  optional<integer> totalModules;
};

DAP_DECLARE_STRUCT_TYPEINFO(ModulesResponse);

// Modules can be retrieved from the debug adapter with this request which can
// either return all modules or a range of modules to support paging. Clients
// should only call this request if the corresponding capability
// `supportsModulesRequest` is true.
struct ModulesRequest : public Request {
  using Response = ModulesResponse;
  // The number of modules to return. If `moduleCount` is not specified or 0,
  // all modules are returned.
  optional<integer> moduleCount;
  // The index of the first module to return; if omitted modules start at 0.
  optional<integer> startModule;
};

DAP_DECLARE_STRUCT_TYPEINFO(ModulesRequest);

// Response to `next` request. This is just an acknowledgement, so no body field
// is required.
struct NextResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(NextResponse);

// The granularity of one 'step' in the stepping requests `next`, `stepIn`,
// `stepOut`, and `stepBack`.
//
// Must be one of the following enumeration values:
// 'statement', 'line', 'instruction'
using SteppingGranularity = string;

// The request executes one step (in the given granularity) for the specified
// thread and allows all other threads to run freely by resuming them. If the
// debug adapter supports single thread execution (see capability
// `supportsSingleThreadExecutionRequests`), setting the `singleThread` argument
// to true prevents other suspended threads from resuming. The debug adapter
// first sends the response and then a `stopped` event (with reason `step`)
// after the step has completed.
struct NextRequest : public Request {
  using Response = NextResponse;
  // Stepping granularity. If no granularity is specified, a granularity of
  // `statement` is assumed.
  optional<SteppingGranularity> granularity;
  // If this flag is true, all other suspended threads are not resumed.
  optional<boolean> singleThread;
  // Specifies the thread for which to resume execution for one step (of the
  // given granularity).
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(NextRequest);

// The event indicates that the target has produced some output.
struct OutputEvent : public Event {
  // The output category. If not specified or if the category is not understood
  // by the client, `console` is assumed.
  //
  // May be one of the following enumeration values:
  // 'console', 'important', 'stdout', 'stderr', 'telemetry'
  optional<string> category;
  // The position in `line` where the output was produced. It is measured in
  // UTF-16 code units and the client capability `columnsStartAt1` determines
  // whether it is 0- or 1-based.
  optional<integer> column;
  // Additional data to report. For the `telemetry` category the data is sent to
  // telemetry, for the other categories the data is shown in JSON format.
  optional<variant<array<any>, boolean, integer, null, number, object, string>>
      data;
  // Support for keeping an output log organized by grouping related messages.
  //
  // Must be one of the following enumeration values:
  // 'start', 'startCollapsed', 'end'
  optional<string> group;
  // The source location's line where the output was produced.
  optional<integer> line;
  // The output to report.
  string output;
  // The source location where the output was produced.
  optional<Source> source;
  // If an attribute `variablesReference` exists and its value is > 0, the
  // output contains objects which can be retrieved by passing
  // `variablesReference` to the `variables` request as long as execution
  // remains suspended. See 'Lifetime of Object References' in the Overview
  // section for details.
  optional<integer> variablesReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(OutputEvent);

// Response to `pause` request. This is just an acknowledgement, so no body
// field is required.
struct PauseResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(PauseResponse);

// The request suspends the debuggee.
// The debug adapter first sends the response and then a `stopped` event (with
// reason `pause`) after the thread has been paused successfully.
struct PauseRequest : public Request {
  using Response = PauseResponse;
  // Pause execution for this thread.
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(PauseRequest);

// The event indicates that the debugger has begun debugging a new process.
// Either one that it has launched, or one that it has attached to.
struct ProcessEvent : public Event {
  // If true, the process is running on the same computer as the debug adapter.
  optional<boolean> isLocalProcess;
  // The logical name of the process. This is usually the full path to process's
  // executable file. Example: /home/<USER>/myproj/program.js.
  string name;
  // The size of a pointer or address for this process, in bits. This value may
  // be used by clients when formatting addresses for display.
  optional<integer> pointerSize;
  // Describes how the debug engine started debugging this process.
  //
  // Must be one of the following enumeration values:
  // 'launch', 'attach', 'attachForSuspendedLaunch'
  optional<string> startMethod;
  // The system process id of the debugged process. This property is missing for
  // non-system processes.
  optional<integer> systemProcessId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ProcessEvent);

// The event signals the end of the progress reporting with a final message.
// This event should only be sent if the corresponding capability
// `supportsProgressReporting` is true.
struct ProgressEndEvent : public Event {
  // More detailed progress message. If omitted, the previous message (if any)
  // is used.
  optional<string> message;
  // The ID that was introduced in the initial `ProgressStartEvent`.
  string progressId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ProgressEndEvent);

// The event signals that a long running operation is about to start and
// provides additional information for the client to set up a corresponding
// progress and cancellation UI. The client is free to delay the showing of the
// UI in order to reduce flicker. This event should only be sent if the
// corresponding capability `supportsProgressReporting` is true.
struct ProgressStartEvent : public Event {
  // If true, the request that reports progress may be cancelled with a `cancel`
  // request. So this property basically controls whether the client should use
  // UX that supports cancellation. Clients that don't support cancellation are
  // allowed to ignore the setting.
  optional<boolean> cancellable;
  // More detailed progress message.
  optional<string> message;
  // Progress percentage to display (value range: 0 to 100). If omitted no
  // percentage is shown.
  optional<number> percentage;
  // An ID that can be used in subsequent `progressUpdate` and `progressEnd`
  // events to make them refer to the same progress reporting. IDs must be
  // unique within a debug session.
  string progressId;
  // The request ID that this progress report is related to. If specified a
  // debug adapter is expected to emit progress events for the long running
  // request until the request has been either completed or cancelled. If the
  // request ID is omitted, the progress report is assumed to be related to some
  // general activity of the debug adapter.
  optional<integer> requestId;
  // Short title of the progress reporting. Shown in the UI to describe the long
  // running operation.
  string title;
};

DAP_DECLARE_STRUCT_TYPEINFO(ProgressStartEvent);

// The event signals that the progress reporting needs to be updated with a new
// message and/or percentage. The client does not have to update the UI
// immediately, but the clients needs to keep track of the message and/or
// percentage values. This event should only be sent if the corresponding
// capability `supportsProgressReporting` is true.
struct ProgressUpdateEvent : public Event {
  // More detailed progress message. If omitted, the previous message (if any)
  // is used.
  optional<string> message;
  // Progress percentage to display (value range: 0 to 100). If omitted no
  // percentage is shown.
  optional<number> percentage;
  // The ID that was introduced in the initial `progressStart` event.
  string progressId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ProgressUpdateEvent);

// Response to `readMemory` request.
struct ReadMemoryResponse : public Response {
  // The address of the first byte of data returned.
  // Treated as a hex value if prefixed with `0x`, or as a decimal value
  // otherwise.
  string address;
  // The bytes read from memory, encoded using base64. If the decoded length of
  // `data` is less than the requested `count` in the original `readMemory`
  // request, and `unreadableBytes` is zero or omitted, then the client should
  // assume it's reached the end of readable memory.
  optional<string> data;
  // The number of unreadable bytes encountered after the last successfully read
  // byte. This can be used to determine the number of bytes that should be
  // skipped before a subsequent `readMemory` request succeeds.
  optional<integer> unreadableBytes;
};

DAP_DECLARE_STRUCT_TYPEINFO(ReadMemoryResponse);

// Reads bytes from memory at the provided location.
// Clients should only call this request if the corresponding capability
// `supportsReadMemoryRequest` is true.
struct ReadMemoryRequest : public Request {
  using Response = ReadMemoryResponse;
  // Number of bytes to read at the specified location and offset.
  integer count;
  // Memory reference to the base location from which data should be read.
  string memoryReference;
  // Offset (in bytes) to be applied to the reference location before reading
  // data. Can be negative.
  optional<integer> offset;
};

DAP_DECLARE_STRUCT_TYPEINFO(ReadMemoryRequest);

// Response to `restartFrame` request. This is just an acknowledgement, so no
// body field is required.
struct RestartFrameResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(RestartFrameResponse);

// The request restarts execution of the specified stack frame.
// The debug adapter first sends the response and then a `stopped` event (with
// reason `restart`) after the restart has completed. Clients should only call
// this request if the corresponding capability `supportsRestartFrame` is true.
struct RestartFrameRequest : public Request {
  using Response = RestartFrameResponse;
  // Restart the stack frame identified by `frameId`. The `frameId` must have
  // been obtained in the current suspended state. See 'Lifetime of Object
  // References' in the Overview section for details.
  integer frameId;
};

DAP_DECLARE_STRUCT_TYPEINFO(RestartFrameRequest);

// Response to `restart` request. This is just an acknowledgement, so no body
// field is required.
struct RestartResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(RestartResponse);

// Restarts a debug session. Clients should only call this request if the
// corresponding capability `supportsRestartRequest` is true. If the capability
// is missing or has the value false, a typical client emulates `restart` by
// terminating the debug adapter first and then launching it anew.
struct RestartRequest : public Request {
  using Response = RestartResponse;
  // The latest version of the `launch` or `attach` configuration.
  optional<object> arguments;
};

DAP_DECLARE_STRUCT_TYPEINFO(RestartRequest);

// Response to `reverseContinue` request. This is just an acknowledgement, so no
// body field is required.
struct ReverseContinueResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(ReverseContinueResponse);

// The request resumes backward execution of all threads. If the debug adapter
// supports single thread execution (see capability
// `supportsSingleThreadExecutionRequests`), setting the `singleThread` argument
// to true resumes only the specified thread. If not all threads were resumed,
// the `allThreadsContinued` attribute of the response should be set to false.
// Clients should only call this request if the corresponding capability
// `supportsStepBack` is true.
struct ReverseContinueRequest : public Request {
  using Response = ReverseContinueResponse;
  // If this flag is true, backward execution is resumed only for the thread
  // with given `threadId`.
  optional<boolean> singleThread;
  // Specifies the active thread. If the debug adapter supports single thread
  // execution (see `supportsSingleThreadExecutionRequests`) and the
  // `singleThread` argument is true, only the thread with this ID is resumed.
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ReverseContinueRequest);

// Response to `runInTerminal` request.
struct RunInTerminalResponse : public Response {
  // The process ID. The value should be less than or equal to 2147483647
  // (2^31-1).
  optional<integer> processId;
  // The process ID of the terminal shell. The value should be less than or
  // equal to 2147483647 (2^31-1).
  optional<integer> shellProcessId;
};

DAP_DECLARE_STRUCT_TYPEINFO(RunInTerminalResponse);

// This request is sent from the debug adapter to the client to run a command in
// a terminal. This is typically used to launch the debuggee in a terminal
// provided by the client. This request should only be called if the
// corresponding client capability `supportsRunInTerminalRequest` is true.
// Client implementations of `runInTerminal` are free to run the command however
// they choose including issuing the command to a command line interpreter (aka
// 'shell'). Argument strings passed to the `runInTerminal` request must arrive
// verbatim in the command to be run. As a consequence, clients which use a
// shell are responsible for escaping any special shell characters in the
// argument strings to prevent them from being interpreted (and modified) by the
// shell. Some users may wish to take advantage of shell processing in the
// argument strings. For clients which implement `runInTerminal` using an
// intermediary shell, the `argsCanBeInterpretedByShell` property can be set to
// true. In this case the client is requested not to escape any special shell
// characters in the argument strings.
struct RunInTerminalRequest : public Request {
  using Response = RunInTerminalResponse;
  // List of arguments. The first argument is the command to run.
  array<string> args;
  // This property should only be set if the corresponding capability
  // `supportsArgsCanBeInterpretedByShell` is true. If the client uses an
  // intermediary shell to launch the application, then the client must not
  // attempt to escape characters with special meanings for the shell. The user
  // is fully responsible for escaping as needed and that arguments using
  // special characters may not be portable across shells.
  optional<boolean> argsCanBeInterpretedByShell;
  // Working directory for the command. For non-empty, valid paths this
  // typically results in execution of a change directory command.
  string cwd;
  // Environment key-value pairs that are added to or removed from the default
  // environment.
  optional<object> env;
  // What kind of terminal to launch. Defaults to `integrated` if not specified.
  //
  // Must be one of the following enumeration values:
  // 'integrated', 'external'
  optional<string> kind;
  // Title of the terminal.
  optional<string> title;
};

DAP_DECLARE_STRUCT_TYPEINFO(RunInTerminalRequest);

// A `Scope` is a named container for variables. Optionally a scope can map to a
// source or a range within a source.
struct Scope {
  // Start position of the range covered by the scope. It is measured in UTF-16
  // code units and the client capability `columnsStartAt1` determines whether
  // it is 0- or 1-based.
  optional<integer> column;
  // End position of the range covered by the scope. It is measured in UTF-16
  // code units and the client capability `columnsStartAt1` determines whether
  // it is 0- or 1-based.
  optional<integer> endColumn;
  // The end line of the range covered by this scope.
  optional<integer> endLine;
  // If true, the number of variables in this scope is large or expensive to
  // retrieve.
  boolean expensive;
  // The number of indexed variables in this scope.
  // The client can use this information to present the variables in a paged UI
  // and fetch them in chunks.
  optional<integer> indexedVariables;
  // The start line of the range covered by this scope.
  optional<integer> line;
  // Name of the scope such as 'Arguments', 'Locals', or 'Registers'. This
  // string is shown in the UI as is and can be translated.
  string name;
  // The number of named variables in this scope.
  // The client can use this information to present the variables in a paged UI
  // and fetch them in chunks.
  optional<integer> namedVariables;
  // A hint for how to present this scope in the UI. If this attribute is
  // missing, the scope is shown with a generic UI.
  //
  // May be one of the following enumeration values:
  // 'arguments', 'locals', 'registers'
  optional<string> presentationHint;
  // The source for this scope.
  optional<Source> source;
  // The variables of this scope can be retrieved by passing the value of
  // `variablesReference` to the `variables` request as long as execution
  // remains suspended. See 'Lifetime of Object References' in the Overview
  // section for details.
  integer variablesReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(Scope);

// Response to `scopes` request.
struct ScopesResponse : public Response {
  // The scopes of the stack frame. If the array has length zero, there are no
  // scopes available.
  array<Scope> scopes;
};

DAP_DECLARE_STRUCT_TYPEINFO(ScopesResponse);

// The request returns the variable scopes for a given stack frame ID.
struct ScopesRequest : public Request {
  using Response = ScopesResponse;
  // Retrieve the scopes for the stack frame identified by `frameId`. The
  // `frameId` must have been obtained in the current suspended state. See
  // 'Lifetime of Object References' in the Overview section for details.
  integer frameId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ScopesRequest);

// Response to `setBreakpoints` request.
// Returned is information about each breakpoint created by this request.
// This includes the actual code location and whether the breakpoint could be
// verified. The breakpoints returned are in the same order as the elements of
// the `breakpoints` (or the deprecated `lines`) array in the arguments.
struct SetBreakpointsResponse : public Response {
  // Information about the breakpoints.
  // The array elements are in the same order as the elements of the
  // `breakpoints` (or the deprecated `lines`) array in the arguments.
  array<Breakpoint> breakpoints;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetBreakpointsResponse);

// Properties of a breakpoint or logpoint passed to the `setBreakpoints`
// request.
struct SourceBreakpoint {
  // Start position within source line of the breakpoint or logpoint. It is
  // measured in UTF-16 code units and the client capability `columnsStartAt1`
  // determines whether it is 0- or 1-based.
  optional<integer> column;
  // The expression for conditional breakpoints.
  // It is only honored by a debug adapter if the corresponding capability
  // `supportsConditionalBreakpoints` is true.
  optional<string> condition;
  // The expression that controls how many hits of the breakpoint are ignored.
  // The debug adapter is expected to interpret the expression as needed.
  // The attribute is only honored by a debug adapter if the corresponding
  // capability `supportsHitConditionalBreakpoints` is true. If both this
  // property and `condition` are specified, `hitCondition` should be evaluated
  // only if the `condition` is met, and the debug adapter should stop only if
  // both conditions are met.
  optional<string> hitCondition;
  // The source line of the breakpoint or logpoint.
  integer line;
  // If this attribute exists and is non-empty, the debug adapter must not
  // 'break' (stop) but log the message instead. Expressions within `{}` are
  // interpolated. The attribute is only honored by a debug adapter if the
  // corresponding capability `supportsLogPoints` is true. If either
  // `hitCondition` or `condition` is specified, then the message should only be
  // logged if those conditions are met.
  optional<string> logMessage;
  // The mode of this breakpoint. If defined, this must be one of the
  // `breakpointModes` the debug adapter advertised in its `Capabilities`.
  optional<string> mode;
};

DAP_DECLARE_STRUCT_TYPEINFO(SourceBreakpoint);

// Sets multiple breakpoints for a single source and clears all previous
// breakpoints in that source. To clear all breakpoint for a source, specify an
// empty array. When a breakpoint is hit, a `stopped` event (with reason
// `breakpoint`) is generated.
struct SetBreakpointsRequest : public Request {
  using Response = SetBreakpointsResponse;
  // The code locations of the breakpoints.
  optional<array<SourceBreakpoint>> breakpoints;
  // Deprecated: The code locations of the breakpoints.
  optional<array<integer>> lines;
  // The source location of the breakpoints; either `source.path` or
  // `source.sourceReference` must be specified.
  Source source;
  // A value of true indicates that the underlying source has been modified
  // which results in new breakpoint locations.
  optional<boolean> sourceModified;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetBreakpointsRequest);

// Response to `setDataBreakpoints` request.
// Returned is information about each breakpoint created by this request.
struct SetDataBreakpointsResponse : public Response {
  // Information about the data breakpoints. The array elements correspond to
  // the elements of the input argument `breakpoints` array.
  array<Breakpoint> breakpoints;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetDataBreakpointsResponse);

// Properties of a data breakpoint passed to the `setDataBreakpoints` request.
struct DataBreakpoint {
  // The access type of the data.
  optional<DataBreakpointAccessType> accessType;
  // An expression for conditional breakpoints.
  optional<string> condition;
  // An id representing the data. This id is returned from the
  // `dataBreakpointInfo` request.
  string dataId;
  // An expression that controls how many hits of the breakpoint are ignored.
  // The debug adapter is expected to interpret the expression as needed.
  optional<string> hitCondition;
};

DAP_DECLARE_STRUCT_TYPEINFO(DataBreakpoint);

// Replaces all existing data breakpoints with new data breakpoints.
// To clear all data breakpoints, specify an empty array.
// When a data breakpoint is hit, a `stopped` event (with reason `data
// breakpoint`) is generated. Clients should only call this request if the
// corresponding capability `supportsDataBreakpoints` is true.
struct SetDataBreakpointsRequest : public Request {
  using Response = SetDataBreakpointsResponse;
  // The contents of this array replaces all existing data breakpoints. An empty
  // array clears all data breakpoints.
  array<DataBreakpoint> breakpoints;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetDataBreakpointsRequest);

// Response to `setExceptionBreakpoints` request.
// The response contains an array of `Breakpoint` objects with information about
// each exception breakpoint or filter. The `Breakpoint` objects are in the same
// order as the elements of the `filters`, `filterOptions`, `exceptionOptions`
// arrays given as arguments. If both `filters` and `filterOptions` are given,
// the returned array must start with `filters` information first, followed by
// `filterOptions` information. The `verified` property of a `Breakpoint` object
// signals whether the exception breakpoint or filter could be successfully
// created and whether the condition is valid. In case of an error the `message`
// property explains the problem. The `id` property can be used to introduce a
// unique ID for the exception breakpoint or filter so that it can be updated
// subsequently by sending breakpoint events. For backward compatibility both
// the `breakpoints` array and the enclosing `body` are optional. If these
// elements are missing a client is not able to show problems for individual
// exception breakpoints or filters.
struct SetExceptionBreakpointsResponse : public Response {
  // Information about the exception breakpoints or filters.
  // The breakpoints returned are in the same order as the elements of the
  // `filters`, `filterOptions`, `exceptionOptions` arrays in the arguments. If
  // both `filters` and `filterOptions` are given, the returned array must start
  // with `filters` information first, followed by `filterOptions` information.
  optional<array<Breakpoint>> breakpoints;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetExceptionBreakpointsResponse);

// An `ExceptionPathSegment` represents a segment in a path that is used to
// match leafs or nodes in a tree of exceptions. If a segment consists of more
// than one name, it matches the names provided if `negate` is false or missing,
// or it matches anything except the names provided if `negate` is true.
struct ExceptionPathSegment {
  // Depending on the value of `negate` the names that should match or not
  // match.
  array<string> names;
  // If false or missing this segment matches the names provided, otherwise it
  // matches anything except the names provided.
  optional<boolean> negate;
};

DAP_DECLARE_STRUCT_TYPEINFO(ExceptionPathSegment);

// An `ExceptionOptions` assigns configuration options to a set of exceptions.
struct ExceptionOptions {
  // Condition when a thrown exception should result in a break.
  ExceptionBreakMode breakMode = "never";
  // A path that selects a single or multiple exceptions in a tree. If `path` is
  // missing, the whole tree is selected. By convention the first segment of the
  // path is a category that is used to group exceptions in the UI.
  optional<array<ExceptionPathSegment>> path;
};

DAP_DECLARE_STRUCT_TYPEINFO(ExceptionOptions);

// An `ExceptionFilterOptions` is used to specify an exception filter together
// with a condition for the `setExceptionBreakpoints` request.
struct ExceptionFilterOptions {
  // An expression for conditional exceptions.
  // The exception breaks into the debugger if the result of the condition is
  // true.
  optional<string> condition;
  // ID of an exception filter returned by the `exceptionBreakpointFilters`
  // capability.
  string filterId;
  // The mode of this exception breakpoint. If defined, this must be one of the
  // `breakpointModes` the debug adapter advertised in its `Capabilities`.
  optional<string> mode;
};

DAP_DECLARE_STRUCT_TYPEINFO(ExceptionFilterOptions);

// The request configures the debugger's response to thrown exceptions. Each of
// the `filters`, `filterOptions`, and `exceptionOptions` in the request are
// independent configurations to a debug adapter indicating a kind of exception
// to catch. An exception thrown in a program should result in a `stopped` event
// from the debug adapter (with reason `exception`) if any of the configured
// filters match. Clients should only call this request if the corresponding
// capability `exceptionBreakpointFilters` returns one or more filters.
struct SetExceptionBreakpointsRequest : public Request {
  using Response = SetExceptionBreakpointsResponse;
  // Configuration options for selected exceptions.
  // The attribute is only honored by a debug adapter if the corresponding
  // capability `supportsExceptionOptions` is true.
  optional<array<ExceptionOptions>> exceptionOptions;
  // Set of exception filters and their options. The set of all possible
  // exception filters is defined by the `exceptionBreakpointFilters`
  // capability. This attribute is only honored by a debug adapter if the
  // corresponding capability `supportsExceptionFilterOptions` is true. The
  // `filter` and `filterOptions` sets are additive.
  optional<array<ExceptionFilterOptions>> filterOptions;
  // Set of exception filters specified by their ID. The set of all possible
  // exception filters is defined by the `exceptionBreakpointFilters`
  // capability. The `filter` and `filterOptions` sets are additive.
  array<string> filters;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetExceptionBreakpointsRequest);

// Response to `setExpression` request.
struct SetExpressionResponse : public Response {
  // The number of indexed child variables.
  // The client can use this information to present the variables in a paged UI
  // and fetch them in chunks. The value should be less than or equal to
  // 2147483647 (2^31-1).
  optional<integer> indexedVariables;
  // A memory reference to a location appropriate for this result.
  // For pointer type eval results, this is generally a reference to the memory
  // address contained in the pointer. This attribute may be returned by a debug
  // adapter if corresponding capability `supportsMemoryReferences` is true.
  optional<string> memoryReference;
  // The number of named child variables.
  // The client can use this information to present the variables in a paged UI
  // and fetch them in chunks. The value should be less than or equal to
  // 2147483647 (2^31-1).
  optional<integer> namedVariables;
  // Properties of a value that can be used to determine how to render the
  // result in the UI.
  optional<VariablePresentationHint> presentationHint;
  // The type of the value.
  // This attribute should only be returned by a debug adapter if the
  // corresponding capability `supportsVariableType` is true.
  optional<string> type;
  // The new value of the expression.
  string value;
  // If `variablesReference` is > 0, the evaluate result is structured and its
  // children can be retrieved by passing `variablesReference` to the
  // `variables` request as long as execution remains suspended. See 'Lifetime
  // of Object References' in the Overview section for details.
  optional<integer> variablesReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetExpressionResponse);

// Evaluates the given `value` expression and assigns it to the `expression`
// which must be a modifiable l-value. The expressions have access to any
// variables and arguments that are in scope of the specified frame. Clients
// should only call this request if the corresponding capability
// `supportsSetExpression` is true. If a debug adapter implements both
// `setExpression` and `setVariable`, a client uses `setExpression` if the
// variable has an `evaluateName` property.
struct SetExpressionRequest : public Request {
  using Response = SetExpressionResponse;
  // The l-value expression to assign to.
  string expression;
  // Specifies how the resulting value should be formatted.
  optional<ValueFormat> format;
  // Evaluate the expressions in the scope of this stack frame. If not
  // specified, the expressions are evaluated in the global scope.
  optional<integer> frameId;
  // The value expression to assign to the l-value expression.
  string value;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetExpressionRequest);

// Response to `setFunctionBreakpoints` request.
// Returned is information about each breakpoint created by this request.
struct SetFunctionBreakpointsResponse : public Response {
  // Information about the breakpoints. The array elements correspond to the
  // elements of the `breakpoints` array.
  array<Breakpoint> breakpoints;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetFunctionBreakpointsResponse);

// Properties of a breakpoint passed to the `setFunctionBreakpoints` request.
struct FunctionBreakpoint {
  // An expression for conditional breakpoints.
  // It is only honored by a debug adapter if the corresponding capability
  // `supportsConditionalBreakpoints` is true.
  optional<string> condition;
  // An expression that controls how many hits of the breakpoint are ignored.
  // The debug adapter is expected to interpret the expression as needed.
  // The attribute is only honored by a debug adapter if the corresponding
  // capability `supportsHitConditionalBreakpoints` is true.
  optional<string> hitCondition;
  // The name of the function.
  string name;
};

DAP_DECLARE_STRUCT_TYPEINFO(FunctionBreakpoint);

// Replaces all existing function breakpoints with new function breakpoints.
// To clear all function breakpoints, specify an empty array.
// When a function breakpoint is hit, a `stopped` event (with reason `function
// breakpoint`) is generated. Clients should only call this request if the
// corresponding capability `supportsFunctionBreakpoints` is true.
struct SetFunctionBreakpointsRequest : public Request {
  using Response = SetFunctionBreakpointsResponse;
  // The function names of the breakpoints.
  array<FunctionBreakpoint> breakpoints;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetFunctionBreakpointsRequest);

// Response to `setInstructionBreakpoints` request
struct SetInstructionBreakpointsResponse : public Response {
  // Information about the breakpoints. The array elements correspond to the
  // elements of the `breakpoints` array.
  array<Breakpoint> breakpoints;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetInstructionBreakpointsResponse);

// Properties of a breakpoint passed to the `setInstructionBreakpoints` request
struct InstructionBreakpoint {
  // An expression for conditional breakpoints.
  // It is only honored by a debug adapter if the corresponding capability
  // `supportsConditionalBreakpoints` is true.
  optional<string> condition;
  // An expression that controls how many hits of the breakpoint are ignored.
  // The debug adapter is expected to interpret the expression as needed.
  // The attribute is only honored by a debug adapter if the corresponding
  // capability `supportsHitConditionalBreakpoints` is true.
  optional<string> hitCondition;
  // The instruction reference of the breakpoint.
  // This should be a memory or instruction pointer reference from an
  // `EvaluateResponse`, `Variable`, `StackFrame`, `GotoTarget`, or
  // `Breakpoint`.
  string instructionReference;
  // The mode of this breakpoint. If defined, this must be one of the
  // `breakpointModes` the debug adapter advertised in its `Capabilities`.
  optional<string> mode;
  // The offset from the instruction reference in bytes.
  // This can be negative.
  optional<integer> offset;
};

DAP_DECLARE_STRUCT_TYPEINFO(InstructionBreakpoint);

// Replaces all existing instruction breakpoints. Typically, instruction
// breakpoints would be set from a disassembly window. To clear all instruction
// breakpoints, specify an empty array. When an instruction breakpoint is hit, a
// `stopped` event (with reason `instruction breakpoint`) is generated. Clients
// should only call this request if the corresponding capability
// `supportsInstructionBreakpoints` is true.
struct SetInstructionBreakpointsRequest : public Request {
  using Response = SetInstructionBreakpointsResponse;
  // The instruction references of the breakpoints
  array<InstructionBreakpoint> breakpoints;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetInstructionBreakpointsRequest);

// Response to `setVariable` request.
struct SetVariableResponse : public Response {
  // The number of indexed child variables.
  // The client can use this information to present the variables in a paged UI
  // and fetch them in chunks. The value should be less than or equal to
  // 2147483647 (2^31-1).
  optional<integer> indexedVariables;
  // A memory reference to a location appropriate for this result.
  // For pointer type eval results, this is generally a reference to the memory
  // address contained in the pointer. This attribute may be returned by a debug
  // adapter if corresponding capability `supportsMemoryReferences` is true.
  optional<string> memoryReference;
  // The number of named child variables.
  // The client can use this information to present the variables in a paged UI
  // and fetch them in chunks. The value should be less than or equal to
  // 2147483647 (2^31-1).
  optional<integer> namedVariables;
  // The type of the new value. Typically shown in the UI when hovering over the
  // value.
  optional<string> type;
  // The new value of the variable.
  string value;
  // If `variablesReference` is > 0, the new value is structured and its
  // children can be retrieved by passing `variablesReference` to the
  // `variables` request as long as execution remains suspended. See 'Lifetime
  // of Object References' in the Overview section for details.
  optional<integer> variablesReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetVariableResponse);

// Set the variable with the given name in the variable container to a new
// value. Clients should only call this request if the corresponding capability
// `supportsSetVariable` is true. If a debug adapter implements both
// `setVariable` and `setExpression`, a client will only use `setExpression` if
// the variable has an `evaluateName` property.
struct SetVariableRequest : public Request {
  using Response = SetVariableResponse;
  // Specifies details on how to format the response value.
  optional<ValueFormat> format;
  // The name of the variable in the container.
  string name;
  // The value of the variable.
  string value;
  // The reference of the variable container. The `variablesReference` must have
  // been obtained in the current suspended state. See 'Lifetime of Object
  // References' in the Overview section for details.
  integer variablesReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(SetVariableRequest);

// Response to `source` request.
struct SourceResponse : public Response {
  // Content of the source reference.
  string content;
  // Content type (MIME type) of the source.
  optional<string> mimeType;
};

DAP_DECLARE_STRUCT_TYPEINFO(SourceResponse);

// The request retrieves the source code for a given source reference.
struct SourceRequest : public Request {
  using Response = SourceResponse;
  // Specifies the source content to load. Either `source.path` or
  // `source.sourceReference` must be specified.
  optional<Source> source;
  // The reference to the source. This is the same as `source.sourceReference`.
  // This is provided for backward compatibility since old clients do not
  // understand the `source` attribute.
  integer sourceReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(SourceRequest);

// A Stackframe contains the source location.
struct StackFrame {
  // Indicates whether this frame can be restarted with the `restart` request.
  // Clients should only use this if the debug adapter supports the `restart`
  // request and the corresponding capability `supportsRestartRequest` is true.
  // If a debug adapter has this capability, then `canRestart` defaults to
  // `true` if the property is absent.
  optional<boolean> canRestart;
  // Start position of the range covered by the stack frame. It is measured in
  // UTF-16 code units and the client capability `columnsStartAt1` determines
  // whether it is 0- or 1-based. If attribute `source` is missing or doesn't
  // exist, `column` is 0 and should be ignored by the client.
  integer column;
  // End position of the range covered by the stack frame. It is measured in
  // UTF-16 code units and the client capability `columnsStartAt1` determines
  // whether it is 0- or 1-based.
  optional<integer> endColumn;
  // The end line of the range covered by the stack frame.
  optional<integer> endLine;
  // An identifier for the stack frame. It must be unique across all threads.
  // This id can be used to retrieve the scopes of the frame with the `scopes`
  // request or to restart the execution of a stack frame.
  integer id;
  // A memory reference for the current instruction pointer in this frame.
  optional<string> instructionPointerReference;
  // The line within the source of the frame. If the source attribute is missing
  // or doesn't exist, `line` is 0 and should be ignored by the client.
  integer line;
  // The module associated with this frame, if any.
  optional<variant<integer, string>> moduleId;
  // The name of the stack frame, typically a method name.
  string name;
  // A hint for how to present this frame in the UI.
  // A value of `label` can be used to indicate that the frame is an artificial
  // frame that is used as a visual label or separator. A value of `subtle` can
  // be used to change the appearance of a frame in a 'subtle' way.
  //
  // Must be one of the following enumeration values:
  // 'normal', 'label', 'subtle'
  optional<string> presentationHint;
  // The source of the frame.
  optional<Source> source;
};

DAP_DECLARE_STRUCT_TYPEINFO(StackFrame);

// Response to `stackTrace` request.
struct StackTraceResponse : public Response {
  // The frames of the stack frame. If the array has length zero, there are no
  // stack frames available. This means that there is no location information
  // available.
  array<StackFrame> stackFrames;
  // The total number of frames available in the stack. If omitted or if
  // `totalFrames` is larger than the available frames, a client is expected to
  // request frames until a request returns less frames than requested (which
  // indicates the end of the stack). Returning monotonically increasing
  // `totalFrames` values for subsequent requests can be used to enforce paging
  // in the client.
  optional<integer> totalFrames;
};

DAP_DECLARE_STRUCT_TYPEINFO(StackTraceResponse);

// Provides formatting information for a stack frame.
struct StackFrameFormat : public ValueFormat {
  // Includes all stack frames, including those the debug adapter might
  // otherwise hide.
  optional<boolean> includeAll;
  // Displays the line number of the stack frame.
  optional<boolean> line;
  // Displays the module of the stack frame.
  optional<boolean> module;
  // Displays the names of parameters for the stack frame.
  optional<boolean> parameterNames;
  // Displays the types of parameters for the stack frame.
  optional<boolean> parameterTypes;
  // Displays the values of parameters for the stack frame.
  optional<boolean> parameterValues;
  // Displays parameters for the stack frame.
  optional<boolean> parameters;
};

DAP_DECLARE_STRUCT_TYPEINFO(StackFrameFormat);

// The request returns a stacktrace from the current execution state of a given
// thread. A client can request all stack frames by omitting the startFrame and
// levels arguments. For performance-conscious clients and if the corresponding
// capability `supportsDelayedStackTraceLoading` is true, stack frames can be
// retrieved in a piecemeal way with the `startFrame` and `levels` arguments.
// The response of the `stackTrace` request may contain a `totalFrames` property
// that hints at the total number of frames in the stack. If a client needs this
// total number upfront, it can issue a request for a single (first) frame and
// depending on the value of `totalFrames` decide how to proceed. In any case a
// client should be prepared to receive fewer frames than requested, which is an
// indication that the end of the stack has been reached.
struct StackTraceRequest : public Request {
  using Response = StackTraceResponse;
  // Specifies details on how to format the stack frames.
  // The attribute is only honored by a debug adapter if the corresponding
  // capability `supportsValueFormattingOptions` is true.
  optional<StackFrameFormat> format;
  // The maximum number of frames to return. If levels is not specified or 0,
  // all frames are returned.
  optional<integer> levels;
  // The index of the first frame to return; if omitted frames start at 0.
  optional<integer> startFrame;
  // Retrieve the stacktrace for this thread.
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(StackTraceRequest);

// Response to `startDebugging` request. This is just an acknowledgement, so no
// body field is required.
struct StartDebuggingResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(StartDebuggingResponse);

// This request is sent from the debug adapter to the client to start a new
// debug session of the same type as the caller. This request should only be
// sent if the corresponding client capability `supportsStartDebuggingRequest`
// is true. A client implementation of `startDebugging` should start a new debug
// session (of the same type as the caller) in the same way that the caller's
// session was started. If the client supports hierarchical debug sessions, the
// newly created session can be treated as a child of the caller session.
struct StartDebuggingRequest : public Request {
  using Response = StartDebuggingResponse;
  // Arguments passed to the new debug session. The arguments must only contain
  // properties understood by the `launch` or `attach` requests of the debug
  // adapter and they must not contain any client-specific properties (e.g.
  // `type`) or client-specific features (e.g. substitutable 'variables').
  object configuration;
  // Indicates whether the new debug session should be started with a `launch`
  // or `attach` request.
  //
  // Must be one of the following enumeration values:
  // 'launch', 'attach'
  string request = "launch";
};

DAP_DECLARE_STRUCT_TYPEINFO(StartDebuggingRequest);

// Response to `stepBack` request. This is just an acknowledgement, so no body
// field is required.
struct StepBackResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(StepBackResponse);

// The request executes one backward step (in the given granularity) for the
// specified thread and allows all other threads to run backward freely by
// resuming them. If the debug adapter supports single thread execution (see
// capability `supportsSingleThreadExecutionRequests`), setting the
// `singleThread` argument to true prevents other suspended threads from
// resuming. The debug adapter first sends the response and then a `stopped`
// event (with reason `step`) after the step has completed. Clients should only
// call this request if the corresponding capability `supportsStepBack` is true.
struct StepBackRequest : public Request {
  using Response = StepBackResponse;
  // Stepping granularity to step. If no granularity is specified, a granularity
  // of `statement` is assumed.
  optional<SteppingGranularity> granularity;
  // If this flag is true, all other suspended threads are not resumed.
  optional<boolean> singleThread;
  // Specifies the thread for which to resume execution for one step backwards
  // (of the given granularity).
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(StepBackRequest);

// Response to `stepIn` request. This is just an acknowledgement, so no body
// field is required.
struct StepInResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(StepInResponse);

// The request resumes the given thread to step into a function/method and
// allows all other threads to run freely by resuming them. If the debug adapter
// supports single thread execution (see capability
// `supportsSingleThreadExecutionRequests`), setting the `singleThread` argument
// to true prevents other suspended threads from resuming. If the request cannot
// step into a target, `stepIn` behaves like the `next` request. The debug
// adapter first sends the response and then a `stopped` event (with reason
// `step`) after the step has completed. If there are multiple function/method
// calls (or other targets) on the source line, the argument `targetId` can be
// used to control into which target the `stepIn` should occur. The list of
// possible targets for a given source line can be retrieved via the
// `stepInTargets` request.
struct StepInRequest : public Request {
  using Response = StepInResponse;
  // Stepping granularity. If no granularity is specified, a granularity of
  // `statement` is assumed.
  optional<SteppingGranularity> granularity;
  // If this flag is true, all other suspended threads are not resumed.
  optional<boolean> singleThread;
  // Id of the target to step into.
  optional<integer> targetId;
  // Specifies the thread for which to resume execution for one step-into (of
  // the given granularity).
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(StepInRequest);

// A `StepInTarget` can be used in the `stepIn` request and determines into
// which single target the `stepIn` request should step.
struct StepInTarget {
  // Start position of the range covered by the step in target. It is measured
  // in UTF-16 code units and the client capability `columnsStartAt1` determines
  // whether it is 0- or 1-based.
  optional<integer> column;
  // End position of the range covered by the step in target. It is measured in
  // UTF-16 code units and the client capability `columnsStartAt1` determines
  // whether it is 0- or 1-based.
  optional<integer> endColumn;
  // The end line of the range covered by the step-in target.
  optional<integer> endLine;
  // Unique identifier for a step-in target.
  integer id;
  // The name of the step-in target (shown in the UI).
  string label;
  // The line of the step-in target.
  optional<integer> line;
};

DAP_DECLARE_STRUCT_TYPEINFO(StepInTarget);

// Response to `stepInTargets` request.
struct StepInTargetsResponse : public Response {
  // The possible step-in targets of the specified source location.
  array<StepInTarget> targets;
};

DAP_DECLARE_STRUCT_TYPEINFO(StepInTargetsResponse);

// This request retrieves the possible step-in targets for the specified stack
// frame. These targets can be used in the `stepIn` request. Clients should only
// call this request if the corresponding capability
// `supportsStepInTargetsRequest` is true.
struct StepInTargetsRequest : public Request {
  using Response = StepInTargetsResponse;
  // The stack frame for which to retrieve the possible step-in targets.
  integer frameId;
};

DAP_DECLARE_STRUCT_TYPEINFO(StepInTargetsRequest);

// Response to `stepOut` request. This is just an acknowledgement, so no body
// field is required.
struct StepOutResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(StepOutResponse);

// The request resumes the given thread to step out (return) from a
// function/method and allows all other threads to run freely by resuming them.
// If the debug adapter supports single thread execution (see capability
// `supportsSingleThreadExecutionRequests`), setting the `singleThread` argument
// to true prevents other suspended threads from resuming. The debug adapter
// first sends the response and then a `stopped` event (with reason `step`)
// after the step has completed.
struct StepOutRequest : public Request {
  using Response = StepOutResponse;
  // Stepping granularity. If no granularity is specified, a granularity of
  // `statement` is assumed.
  optional<SteppingGranularity> granularity;
  // If this flag is true, all other suspended threads are not resumed.
  optional<boolean> singleThread;
  // Specifies the thread for which to resume execution for one step-out (of the
  // given granularity).
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(StepOutRequest);

// The event indicates that the execution of the debuggee has stopped due to
// some condition. This can be caused by a breakpoint previously set, a stepping
// request has completed, by executing a debugger statement etc.
struct StoppedEvent : public Event {
  // If `allThreadsStopped` is true, a debug adapter can announce that all
  // threads have stopped.
  // - The client should use this information to enable that all threads can be
  // expanded to access their stacktraces.
  // - If the attribute is missing or false, only the thread with the given
  // `threadId` can be expanded.
  optional<boolean> allThreadsStopped;
  // The full reason for the event, e.g. 'Paused on exception'. This string is
  // shown in the UI as is and can be translated.
  optional<string> description;
  // Ids of the breakpoints that triggered the event. In most cases there is
  // only a single breakpoint but here are some examples for multiple
  // breakpoints:
  // - Different types of breakpoints map to the same location.
  // - Multiple source breakpoints get collapsed to the same instruction by the
  // compiler/runtime.
  // - Multiple function breakpoints with different function names map to the
  // same location.
  optional<array<integer>> hitBreakpointIds;
  // A value of true hints to the client that this event should not change the
  // focus.
  optional<boolean> preserveFocusHint;
  // The reason for the event.
  // For backward compatibility this string is shown in the UI if the
  // `description` attribute is missing (but it must not be translated).
  //
  // May be one of the following enumeration values:
  // 'step', 'breakpoint', 'exception', 'pause', 'entry', 'goto', 'function
  // breakpoint', 'data breakpoint', 'instruction breakpoint'
  string reason;
  // Additional information. E.g. if reason is `exception`, text contains the
  // exception name. This string is shown in the UI.
  optional<string> text;
  // The thread which was stopped.
  optional<integer> threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(StoppedEvent);

// Response to `terminate` request. This is just an acknowledgement, so no body
// field is required.
struct TerminateResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(TerminateResponse);

// The `terminate` request is sent from the client to the debug adapter in order
// to shut down the debuggee gracefully. Clients should only call this request
// if the capability `supportsTerminateRequest` is true. Typically a debug
// adapter implements `terminate` by sending a software signal which the
// debuggee intercepts in order to clean things up properly before terminating
// itself. Please note that this request does not directly affect the state of
// the debug session: if the debuggee decides to veto the graceful shutdown for
// any reason by not terminating itself, then the debug session just continues.
// Clients can surface the `terminate` request as an explicit command or they
// can integrate it into a two stage Stop command that first sends `terminate`
// to request a graceful shutdown, and if that fails uses `disconnect` for a
// forceful shutdown.
struct TerminateRequest : public Request {
  using Response = TerminateResponse;
  // A value of true indicates that this `terminate` request is part of a
  // restart sequence.
  optional<boolean> restart;
};

DAP_DECLARE_STRUCT_TYPEINFO(TerminateRequest);

// Response to `terminateThreads` request. This is just an acknowledgement, no
// body field is required.
struct TerminateThreadsResponse : public Response {};

DAP_DECLARE_STRUCT_TYPEINFO(TerminateThreadsResponse);

// The request terminates the threads with the given ids.
// Clients should only call this request if the corresponding capability
// `supportsTerminateThreadsRequest` is true.
struct TerminateThreadsRequest : public Request {
  using Response = TerminateThreadsResponse;
  // Ids of threads to be terminated.
  optional<array<integer>> threadIds;
};

DAP_DECLARE_STRUCT_TYPEINFO(TerminateThreadsRequest);

// The event indicates that debugging of the debuggee has terminated. This does
// **not** mean that the debuggee itself has exited.
struct TerminatedEvent : public Event {
  // A debug adapter may set `restart` to true (or to an arbitrary object) to
  // request that the client restarts the session. The value is not interpreted
  // by the client and passed unmodified as an attribute `__restart` to the
  // `launch` and `attach` requests.
  optional<variant<array<any>, boolean, integer, null, number, object, string>>
      restart;
};

DAP_DECLARE_STRUCT_TYPEINFO(TerminatedEvent);

// The event indicates that a thread has started or exited.
struct ThreadEvent : public Event {
  // The reason for the event.
  //
  // May be one of the following enumeration values:
  // 'started', 'exited'
  string reason;
  // The identifier of the thread.
  integer threadId;
};

DAP_DECLARE_STRUCT_TYPEINFO(ThreadEvent);

// A Thread
struct Thread {
  // Unique identifier for the thread.
  integer id;
  // The name of the thread.
  string name;
};

DAP_DECLARE_STRUCT_TYPEINFO(Thread);

// Response to `threads` request.
struct ThreadsResponse : public Response {
  // All threads.
  array<Thread> threads;
};

DAP_DECLARE_STRUCT_TYPEINFO(ThreadsResponse);

// The request retrieves a list of all threads.
struct ThreadsRequest : public Request {
  using Response = ThreadsResponse;
};

DAP_DECLARE_STRUCT_TYPEINFO(ThreadsRequest);

// A Variable is a name/value pair.
// The `type` attribute is shown if space permits or when hovering over the
// variable's name. The `kind` attribute is used to render additional properties
// of the variable, e.g. different icons can be used to indicate that a variable
// is public or private. If the value is structured (has children), a handle is
// provided to retrieve the children with the `variables` request. If the number
// of named or indexed children is large, the numbers should be returned via the
// `namedVariables` and `indexedVariables` attributes. The client can use this
// information to present the children in a paged UI and fetch them in chunks.
struct Variable {
  // The evaluatable name of this variable which can be passed to the `evaluate`
  // request to fetch the variable's value.
  optional<string> evaluateName;
  // The number of indexed child variables.
  // The client can use this information to present the children in a paged UI
  // and fetch them in chunks.
  optional<integer> indexedVariables;
  // A memory reference associated with this variable.
  // For pointer type variables, this is generally a reference to the memory
  // address contained in the pointer. For executable data, this reference may
  // later be used in a `disassemble` request. This attribute may be returned by
  // a debug adapter if corresponding capability `supportsMemoryReferences` is
  // true.
  optional<string> memoryReference;
  // The variable's name.
  string name;
  // The number of named child variables.
  // The client can use this information to present the children in a paged UI
  // and fetch them in chunks.
  optional<integer> namedVariables;
  // Properties of a variable that can be used to determine how to render the
  // variable in the UI.
  optional<VariablePresentationHint> presentationHint;
  // The type of the variable's value. Typically shown in the UI when hovering
  // over the value. This attribute should only be returned by a debug adapter
  // if the corresponding capability `supportsVariableType` is true.
  optional<string> type;
  // The variable's value.
  // This can be a multi-line text, e.g. for a function the body of a function.
  // For structured variables (which do not have a simple value), it is
  // recommended to provide a one-line representation of the structured object.
  // This helps to identify the structured object in the collapsed state when
  // its children are not yet visible. An empty string can be used if no value
  // should be shown in the UI.
  string value;
  // If `variablesReference` is > 0, the variable is structured and its children
  // can be retrieved by passing `variablesReference` to the `variables` request
  // as long as execution remains suspended. See 'Lifetime of Object References'
  // in the Overview section for details.
  integer variablesReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(Variable);

// Response to `variables` request.
struct VariablesResponse : public Response {
  // All (or a range) of variables for the given variable reference.
  array<Variable> variables;
};

DAP_DECLARE_STRUCT_TYPEINFO(VariablesResponse);

// Retrieves all child variables for the given variable reference.
// A filter can be used to limit the fetched children to either named or indexed
// children.
struct VariablesRequest : public Request {
  using Response = VariablesResponse;
  // The number of variables to return. If count is missing or 0, all variables
  // are returned. The attribute is only honored by a debug adapter if the
  // corresponding capability `supportsVariablePaging` is true.
  optional<integer> count;
  // Filter to limit the child variables to either named or indexed. If omitted,
  // both types are fetched.
  //
  // Must be one of the following enumeration values:
  // 'indexed', 'named'
  optional<string> filter;
  // Specifies details on how to format the Variable values.
  // The attribute is only honored by a debug adapter if the corresponding
  // capability `supportsValueFormattingOptions` is true.
  optional<ValueFormat> format;
  // The index of the first variable to return; if omitted children start at 0.
  // The attribute is only honored by a debug adapter if the corresponding
  // capability `supportsVariablePaging` is true.
  optional<integer> start;
  // The variable for which to retrieve its children. The `variablesReference`
  // must have been obtained in the current suspended state. See 'Lifetime of
  // Object References' in the Overview section for details.
  integer variablesReference;
};

DAP_DECLARE_STRUCT_TYPEINFO(VariablesRequest);

// Response to `writeMemory` request.
struct WriteMemoryResponse : public Response {
  // Property that should be returned when `allowPartial` is true to indicate
  // the number of bytes starting from address that were successfully written.
  optional<integer> bytesWritten;
  // Property that should be returned when `allowPartial` is true to indicate
  // the offset of the first byte of data successfully written. Can be negative.
  optional<integer> offset;
};

DAP_DECLARE_STRUCT_TYPEINFO(WriteMemoryResponse);

// Writes bytes to memory at the provided location.
// Clients should only call this request if the corresponding capability
// `supportsWriteMemoryRequest` is true.
struct WriteMemoryRequest : public Request {
  using Response = WriteMemoryResponse;
  // Property to control partial writes. If true, the debug adapter should
  // attempt to write memory even if the entire memory region is not writable.
  // In such a case the debug adapter should stop after hitting the first byte
  // of memory that cannot be written and return the number of bytes written in
  // the response via the `offset` and `bytesWritten` properties. If false or
  // missing, a debug adapter should attempt to verify the region is writable
  // before writing, and fail the response if it is not.
  optional<boolean> allowPartial;
  // Bytes to write, encoded using base64.
  string data;
  // Memory reference to the base location to which data should be written.
  string memoryReference;
  // Offset (in bytes) to be applied to the reference location before writing
  // data. Can be negative.
  optional<integer> offset;
};

DAP_DECLARE_STRUCT_TYPEINFO(WriteMemoryRequest);

}  // namespace dap

#endif  // dap_protocol_h
