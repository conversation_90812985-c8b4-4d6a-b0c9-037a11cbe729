------------------------------------------------------------------------
-- ddRotate.decTest -- rotate a decDouble coefficient left or right   --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- Sanity check
ddrot001 rotate                 0    0  ->  0
ddrot002 rotate                 0    2  ->  0
ddrot003 rotate                 1    2  ->  100
ddrot004 rotate                 1   15  ->  1000000000000000
ddrot005 rotate                 1   16  ->  1
ddrot006 rotate                 1   -1  ->  1000000000000000
ddrot007 rotate                 0   -2  ->  0
ddrot008 rotate  1234567890123456   -1  ->  6123456789012345
ddrot009 rotate  1234567890123456   -15 ->  2345678901234561
ddrot010 rotate  1234567890123456   -16 ->  1234567890123456
ddrot011 rotate  9934567890123456   -15 ->  9345678901234569
ddrot012 rotate  9934567890123456   -16 ->  9934567890123456

-- rhs must be an integer
ddrot015 rotate        1    1.5    -> NaN Invalid_operation
ddrot016 rotate        1    1.0    -> NaN Invalid_operation
ddrot017 rotate        1    0.1    -> NaN Invalid_operation
ddrot018 rotate        1    0.0    -> NaN Invalid_operation
ddrot019 rotate        1    1E+1   -> NaN Invalid_operation
ddrot020 rotate        1    1E+99  -> NaN Invalid_operation
ddrot021 rotate        1    Inf    -> NaN Invalid_operation
ddrot022 rotate        1    -Inf   -> NaN Invalid_operation
-- and |rhs| <= precision
ddrot025 rotate        1    -1000  -> NaN Invalid_operation
ddrot026 rotate        1    -17    -> NaN Invalid_operation
ddrot027 rotate        1     17    -> NaN Invalid_operation
ddrot028 rotate        1     1000  -> NaN Invalid_operation

-- full pattern
ddrot030 rotate  1234567890123456         -16  -> 1234567890123456
ddrot031 rotate  1234567890123456         -15  -> 2345678901234561
ddrot032 rotate  1234567890123456         -14  -> 3456789012345612
ddrot033 rotate  1234567890123456         -13  -> 4567890123456123
ddrot034 rotate  1234567890123456         -12  -> 5678901234561234
ddrot035 rotate  1234567890123456         -11  -> 6789012345612345
ddrot036 rotate  1234567890123456         -10  -> 7890123456123456
ddrot037 rotate  1234567890123456         -9   -> 8901234561234567
ddrot038 rotate  1234567890123456         -8   -> 9012345612345678
ddrot039 rotate  1234567890123456         -7   ->  123456123456789
ddrot040 rotate  1234567890123456         -6   -> 1234561234567890
ddrot041 rotate  1234567890123456         -5   -> 2345612345678901
ddrot042 rotate  1234567890123456         -4   -> 3456123456789012
ddrot043 rotate  1234567890123456         -3   -> 4561234567890123
ddrot044 rotate  1234567890123456         -2   -> 5612345678901234
ddrot045 rotate  1234567890123456         -1   -> 6123456789012345
ddrot046 rotate  1234567890123456         -0   -> 1234567890123456

ddrot047 rotate  1234567890123456         +0   -> 1234567890123456
ddrot048 rotate  1234567890123456         +1   -> 2345678901234561
ddrot049 rotate  1234567890123456         +2   -> 3456789012345612
ddrot050 rotate  1234567890123456         +3   -> 4567890123456123
ddrot051 rotate  1234567890123456         +4   -> 5678901234561234
ddrot052 rotate  1234567890123456         +5   -> 6789012345612345
ddrot053 rotate  1234567890123456         +6   -> 7890123456123456
ddrot054 rotate  1234567890123456         +7   -> 8901234561234567
ddrot055 rotate  1234567890123456         +8   -> 9012345612345678
ddrot056 rotate  1234567890123456         +9   ->  123456123456789
ddrot057 rotate  1234567890123456         +10  -> 1234561234567890
ddrot058 rotate  1234567890123456         +11  -> 2345612345678901
ddrot059 rotate  1234567890123456         +12  -> 3456123456789012
ddrot060 rotate  1234567890123456         +13  -> 4561234567890123
ddrot061 rotate  1234567890123456         +14  -> 5612345678901234
ddrot062 rotate  1234567890123456         +15  -> 6123456789012345
ddrot063 rotate  1234567890123456         +16  -> 1234567890123456

-- zeros
ddrot070 rotate  0E-10              +9   ->   0E-10
ddrot071 rotate  0E-10              -9   ->   0E-10
ddrot072 rotate  0.000              +9   ->   0.000
ddrot073 rotate  0.000              -9   ->   0.000
ddrot074 rotate  0E+10              +9   ->   0E+10
ddrot075 rotate  0E+10              -9   ->   0E+10
ddrot076 rotate -0E-10              +9   ->  -0E-10
ddrot077 rotate -0E-10              -9   ->  -0E-10
ddrot078 rotate -0.000              +9   ->  -0.000
ddrot079 rotate -0.000              -9   ->  -0.000
ddrot080 rotate -0E+10              +9   ->  -0E+10
ddrot081 rotate -0E+10              -9   ->  -0E+10

-- Nmax, Nmin, Ntiny
ddrot141 rotate  9.999999999999999E+384     -1  -> 9.999999999999999E+384
ddrot142 rotate  9.999999999999999E+384     -15 -> 9.999999999999999E+384
ddrot143 rotate  9.999999999999999E+384      1  -> 9.999999999999999E+384
ddrot144 rotate  9.999999999999999E+384      15 -> 9.999999999999999E+384
ddrot145 rotate  1E-383                     -1  -> 1.000000000000000E-368
ddrot146 rotate  1E-383                     -15 -> 1.0E-382
ddrot147 rotate  1E-383                      1  -> 1.0E-382
ddrot148 rotate  1E-383                      15 -> 1.000000000000000E-368
ddrot151 rotate  1.000000000000000E-383     -1  -> 1.00000000000000E-384
ddrot152 rotate  1.000000000000000E-383     -15 -> 1E-398
ddrot153 rotate  1.000000000000000E-383      1  -> 1E-398
ddrot154 rotate  1.000000000000000E-383      15 -> 1.00000000000000E-384
ddrot155 rotate  9.000000000000000E-383     -1  -> 9.00000000000000E-384
ddrot156 rotate  9.000000000000000E-383     -15 -> 9E-398
ddrot157 rotate  9.000000000000000E-383      1  -> 9E-398
ddrot158 rotate  9.000000000000000E-383      15 -> 9.00000000000000E-384
ddrot160 rotate  1E-398                     -1  -> 1.000000000000000E-383
ddrot161 rotate  1E-398                     -15 -> 1.0E-397
ddrot162 rotate  1E-398                      1  -> 1.0E-397
ddrot163 rotate  1E-398                      15 -> 1.000000000000000E-383
--  negatives
ddrot171 rotate -9.999999999999999E+384     -1  -> -9.999999999999999E+384
ddrot172 rotate -9.999999999999999E+384     -15 -> -9.999999999999999E+384
ddrot173 rotate -9.999999999999999E+384      1  -> -9.999999999999999E+384
ddrot174 rotate -9.999999999999999E+384      15 -> -9.999999999999999E+384
ddrot175 rotate -1E-383                     -1  -> -1.000000000000000E-368
ddrot176 rotate -1E-383                     -15 -> -1.0E-382
ddrot177 rotate -1E-383                      1  -> -1.0E-382
ddrot178 rotate -1E-383                      15 -> -1.000000000000000E-368
ddrot181 rotate -1.000000000000000E-383     -1  -> -1.00000000000000E-384
ddrot182 rotate -1.000000000000000E-383     -15 -> -1E-398
ddrot183 rotate -1.000000000000000E-383      1  -> -1E-398
ddrot184 rotate -1.000000000000000E-383      15 -> -1.00000000000000E-384
ddrot185 rotate -9.000000000000000E-383     -1  -> -9.00000000000000E-384
ddrot186 rotate -9.000000000000000E-383     -15 -> -9E-398
ddrot187 rotate -9.000000000000000E-383      1  -> -9E-398
ddrot188 rotate -9.000000000000000E-383      15 -> -9.00000000000000E-384
ddrot190 rotate -1E-398                     -1  -> -1.000000000000000E-383
ddrot191 rotate -1E-398                     -15 -> -1.0E-397
ddrot192 rotate -1E-398                      1  -> -1.0E-397
ddrot193 rotate -1E-398                      15 -> -1.000000000000000E-383

-- more negatives (of sanities)
ddrot201 rotate                -0    0  -> -0
ddrot202 rotate                -0    2  -> -0
ddrot203 rotate                -1    2  -> -100
ddrot204 rotate                -1   15  -> -1000000000000000
ddrot205 rotate                -1   16  -> -1
ddrot206 rotate                -1   -1  -> -1000000000000000
ddrot207 rotate                -0   -2  -> -0
ddrot208 rotate -1234567890123456   -1  -> -6123456789012345
ddrot209 rotate -1234567890123456   -15 -> -2345678901234561
ddrot210 rotate -1234567890123456   -16 -> -1234567890123456
ddrot211 rotate -9934567890123456   -15 -> -9345678901234569
ddrot212 rotate -9934567890123456   -16 -> -9934567890123456


-- Specials; NaNs are handled as usual
ddrot781 rotate -Inf  -8     -> -Infinity
ddrot782 rotate -Inf  -1     -> -Infinity
ddrot783 rotate -Inf  -0     -> -Infinity
ddrot784 rotate -Inf   0     -> -Infinity
ddrot785 rotate -Inf   1     -> -Infinity
ddrot786 rotate -Inf   8     -> -Infinity
ddrot787 rotate -1000 -Inf   -> NaN Invalid_operation
ddrot788 rotate -Inf  -Inf   -> NaN Invalid_operation
ddrot789 rotate -1    -Inf   -> NaN Invalid_operation
ddrot790 rotate -0    -Inf   -> NaN Invalid_operation
ddrot791 rotate  0    -Inf   -> NaN Invalid_operation
ddrot792 rotate  1    -Inf   -> NaN Invalid_operation
ddrot793 rotate  1000 -Inf   -> NaN Invalid_operation
ddrot794 rotate  Inf  -Inf   -> NaN Invalid_operation

ddrot800 rotate  Inf  -Inf   -> NaN Invalid_operation
ddrot801 rotate  Inf  -8     -> Infinity
ddrot802 rotate  Inf  -1     -> Infinity
ddrot803 rotate  Inf  -0     -> Infinity
ddrot804 rotate  Inf   0     -> Infinity
ddrot805 rotate  Inf   1     -> Infinity
ddrot806 rotate  Inf   8     -> Infinity
ddrot807 rotate  Inf   Inf   -> NaN Invalid_operation
ddrot808 rotate -1000  Inf   -> NaN Invalid_operation
ddrot809 rotate -Inf   Inf   -> NaN Invalid_operation
ddrot810 rotate -1     Inf   -> NaN Invalid_operation
ddrot811 rotate -0     Inf   -> NaN Invalid_operation
ddrot812 rotate  0     Inf   -> NaN Invalid_operation
ddrot813 rotate  1     Inf   -> NaN Invalid_operation
ddrot814 rotate  1000  Inf   -> NaN Invalid_operation
ddrot815 rotate  Inf   Inf   -> NaN Invalid_operation

ddrot821 rotate  NaN -Inf    ->  NaN
ddrot822 rotate  NaN -1000   ->  NaN
ddrot823 rotate  NaN -1      ->  NaN
ddrot824 rotate  NaN -0      ->  NaN
ddrot825 rotate  NaN  0      ->  NaN
ddrot826 rotate  NaN  1      ->  NaN
ddrot827 rotate  NaN  1000   ->  NaN
ddrot828 rotate  NaN  Inf    ->  NaN
ddrot829 rotate  NaN  NaN    ->  NaN
ddrot830 rotate -Inf  NaN    ->  NaN
ddrot831 rotate -1000 NaN    ->  NaN
ddrot832 rotate -1    NaN    ->  NaN
ddrot833 rotate -0    NaN    ->  NaN
ddrot834 rotate  0    NaN    ->  NaN
ddrot835 rotate  1    NaN    ->  NaN
ddrot836 rotate  1000 NaN    ->  NaN
ddrot837 rotate  Inf  NaN    ->  NaN

ddrot841 rotate  sNaN -Inf   ->  NaN  Invalid_operation
ddrot842 rotate  sNaN -1000  ->  NaN  Invalid_operation
ddrot843 rotate  sNaN -1     ->  NaN  Invalid_operation
ddrot844 rotate  sNaN -0     ->  NaN  Invalid_operation
ddrot845 rotate  sNaN  0     ->  NaN  Invalid_operation
ddrot846 rotate  sNaN  1     ->  NaN  Invalid_operation
ddrot847 rotate  sNaN  1000  ->  NaN  Invalid_operation
ddrot848 rotate  sNaN  NaN   ->  NaN  Invalid_operation
ddrot849 rotate  sNaN sNaN   ->  NaN  Invalid_operation
ddrot850 rotate  NaN  sNaN   ->  NaN  Invalid_operation
ddrot851 rotate -Inf  sNaN   ->  NaN  Invalid_operation
ddrot852 rotate -1000 sNaN   ->  NaN  Invalid_operation
ddrot853 rotate -1    sNaN   ->  NaN  Invalid_operation
ddrot854 rotate -0    sNaN   ->  NaN  Invalid_operation
ddrot855 rotate  0    sNaN   ->  NaN  Invalid_operation
ddrot856 rotate  1    sNaN   ->  NaN  Invalid_operation
ddrot857 rotate  1000 sNaN   ->  NaN  Invalid_operation
ddrot858 rotate  Inf  sNaN   ->  NaN  Invalid_operation
ddrot859 rotate  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
ddrot861 rotate  NaN1   -Inf    ->  NaN1
ddrot862 rotate +NaN2   -1000   ->  NaN2
ddrot863 rotate  NaN3    1000   ->  NaN3
ddrot864 rotate  NaN4    Inf    ->  NaN4
ddrot865 rotate  NaN5   +NaN6   ->  NaN5
ddrot866 rotate -Inf     NaN7   ->  NaN7
ddrot867 rotate -1000    NaN8   ->  NaN8
ddrot868 rotate  1000    NaN9   ->  NaN9
ddrot869 rotate  Inf    +NaN10  ->  NaN10
ddrot871 rotate  sNaN11  -Inf   ->  NaN11  Invalid_operation
ddrot872 rotate  sNaN12  -1000  ->  NaN12  Invalid_operation
ddrot873 rotate  sNaN13   1000  ->  NaN13  Invalid_operation
ddrot874 rotate  sNaN14   NaN17 ->  NaN14  Invalid_operation
ddrot875 rotate  sNaN15  sNaN18 ->  NaN15  Invalid_operation
ddrot876 rotate  NaN16   sNaN19 ->  NaN19  Invalid_operation
ddrot877 rotate -Inf    +sNaN20 ->  NaN20  Invalid_operation
ddrot878 rotate -1000    sNaN21 ->  NaN21  Invalid_operation
ddrot879 rotate  1000    sNaN22 ->  NaN22  Invalid_operation
ddrot880 rotate  Inf     sNaN23 ->  NaN23  Invalid_operation
ddrot881 rotate +NaN25  +sNaN24 ->  NaN24  Invalid_operation
ddrot882 rotate -NaN26    NaN28 -> -NaN26
ddrot883 rotate -sNaN27  sNaN29 -> -NaN27  Invalid_operation
ddrot884 rotate  1000    -NaN30 -> -NaN30
ddrot885 rotate  1000   -sNaN31 -> -NaN31  Invalid_operation
