.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_get_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_get_info \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_obj_get_info(gnutls_pkcs11_obj_t " obj ", gnutls_pkcs11_obj_info_t " itype ", void * " output ", size_t * " output_size ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_t obj" 12
should contain a \fBgnutls_pkcs11_obj_t\fP type
.IP "gnutls_pkcs11_obj_info_t itype" 12
Denotes the type of information requested
.IP "void * output" 12
where output will be stored
.IP "size_t * output_size" 12
contains the maximum size of the output buffer and will be
overwritten with the actual size.
.SH "DESCRIPTION"
This function will return information about the PKCS11 certificate
such as the label, id as well as token information where the key is
stored.

When output is text, a null terminated string is written to  \fIoutput\fP and its
string length is written to  \fIoutput_size\fP (without null terminator). If the
buffer is too small,  \fIoutput_size\fP will contain the expected buffer size
(with null terminator for text) and return \fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP.

In versions previously to 3.6.0 this function included the null terminator
to  \fIoutput_size\fP . After 3.6.0 the output size doesn't include the terminator character.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success or a negative error code on error.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
