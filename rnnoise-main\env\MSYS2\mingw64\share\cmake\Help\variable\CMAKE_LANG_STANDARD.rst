CMAKE_<LANG>_STANDARD
---------------------

The variations are:

* :variable:`CMAKE_C_STANDARD`
* :variable:`CMAKE_CXX_STANDARD`
* :variable:`CMAKE_CUDA_STANDARD`
* :variable:`CMAKE_HIP_STANDARD`
* :variable:`CMAKE_OBJC_STANDARD`
* :variable:`CMAKE_OBJCXX_STANDARD`

Default values for :prop_tgt:`<LANG>_STANDARD` target properties if set when a
target is created.

For supported CMake versions see the respective pages.

See the :manual:`cmake-compile-features(7)` manual for information on
compile features and a list of supported compilers.
