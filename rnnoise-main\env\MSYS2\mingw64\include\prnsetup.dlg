/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#include <dlgs.h>

PRINTDLGORD DIALOG DISCARDABLE 32,32,288,186
STYLE DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION | WS_SYSMENU |
      DS_CONTEXTHELP | DS_3DLOOK
CAPTION "Print"
FONT 8,"MS Shell Dlg"
BEGIN
    GROUPBOX "Printer",grp4,8,4,272,84,WS_GROUP
    LTEXT "&Name:",stc6,16,20,36,8
    COMBOBOX cmb4,52,18,152,152,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    PUSHBUTTON "&Properties...",psh2,212,17,60,14,WS_GROUP
    LTEXT "Status:",stc8,16,36,36,10,SS_NOPREFIX
    LTEXT "",stc12,52,36,224,10,SS_NOPREFIX | SS_LEFTNOWORDWRAP
    LTEXT "Type:",stc7,16,48,36,10,SS_NOPREFIX
    LTEXT "",stc11,52,48,224,10,SS_NOPREFIX | SS_LEFTNOWORDWRAP
    LTEXT "Where:",stc10,16,60,36,10,SS_NOPREFIX
    LTEXT "",stc14,52,60,224,10,SS_NOPREFIX | SS_LEFTNOWORDWRAP
    LTEXT "Comment:",stc9,16,72,36,10,SS_NOPREFIX
    LTEXT "",stc13,52,72,156,10,SS_NOPREFIX | SS_LEFTNOWORDWRAP
    AUTOCHECKBOX "Print to fi&le",chx1,212,72,64,12,WS_GROUP
    GROUPBOX "Print range",grp1,8,92,144,64,WS_GROUP
    RADIOBUTTON "&All",rad1,16,106,64,12,WS_GROUP
    RADIOBUTTON "Pa&ges",rad3,16,122,36,12
    RADIOBUTTON "&Selection",rad2,16,138,64,12
    RTEXT "&from:",stc2,52,124,20,8
    EDITTEXT edt1,74,122,26,12,WS_GROUP | ES_NUMBER
    RTEXT "&to:",stc3,102,124,16,8
    EDITTEXT edt2,120,122,26,12,WS_GROUP | ES_NUMBER
    GROUPBOX "Copies",grp2,160,92,120,64,WS_GROUP
    LTEXT "Number of &copies:",stc5,168,108,68,8
    EDITTEXT edt3,240,106,32,12,WS_GROUP | ES_NUMBER
    ICON "",ico3,162,124,76,28,WS_GROUP | SS_CENTERIMAGE
    AUTOCHECKBOX "C&ollate",chx2,240,130,36,12,WS_GROUP
    DEFPUSHBUTTON "OK",IDOK,180,164,48,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,232,164,48,14
    PUSHBUTTON "&Help",pshHelp,8,164,48,14
END

PRNSETUPDLGORD DIALOG DISCARDABLE 32,32,288,178
STYLE DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION | WS_SYSMENU |
      DS_CONTEXTHELP | DS_3DLOOK
CAPTION "Print Setup"
FONT 8,"MS Shell Dlg"
BEGIN
    GROUPBOX "Printer",grp4,8,4,272,84,WS_GROUP
    LTEXT "&Name:",stc6,16,20,36,8
    COMBOBOX cmb1,52,18,152,152,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    PUSHBUTTON "&Properties...",psh2,212,17,60,14,WS_GROUP
    LTEXT "Status:",stc8,16,36,36,10,SS_NOPREFIX
    LTEXT "",stc12,52,36,224,10,SS_NOPREFIX | SS_LEFTNOWORDWRAP
    LTEXT "Type:",stc7,16,48,36,10,SS_NOPREFIX
    LTEXT "",stc11,52,48,224,10,SS_NOPREFIX | SS_LEFTNOWORDWRAP
    LTEXT "Where:",stc10,16,60,36,10,SS_NOPREFIX
    LTEXT "",stc14,52,60,224,10,SS_NOPREFIX | SS_LEFTNOWORDWRAP
    LTEXT "Comment:",stc9,16,72,36,10,SS_NOPREFIX
    LTEXT "",stc13,52,72,224,10,SS_NOPREFIX | SS_LEFTNOWORDWRAP
    GROUPBOX "Paper",grp2,8,92,164,56,WS_GROUP
    LTEXT "Si&ze:",stc2,16,108,36,8
    COMBOBOX cmb2,52,106,112,112,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    LTEXT "&Source:",stc3,16,128,36,8
    COMBOBOX cmb3,52,126,112,112,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    GROUPBOX "Orientation",grp1,180,92,100,56,WS_GROUP
    ICON "",ico1,190,112,25,28,WS_GROUP
    RADIOBUTTON "P&ortrait",rad1,224,106,52,12,WS_GROUP | WS_TABSTOP
    RADIOBUTTON "L&andscape",rad2,224,126,52,12
    DEFPUSHBUTTON "OK",IDOK,180,156,48,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,232,156,48,14
    PUSHBUTTON "&Help",pshHelp,8,156,48,14
END

PAGESETUPDLGORD DIALOG DISCARDABLE 32,32,240,240
STYLE DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION | WS_SYSMENU |
      DS_CONTEXTHELP | DS_3DLOOK
CAPTION "Page Setup"
FONT 8,"MS Shell Dlg"
BEGIN
    CONTROL "",rct1,"Static",SS_WHITERECT | WS_GROUP,80,8,80,80
    CONTROL "",rct2,"Static",SS_GRAYRECT | WS_GROUP,160,12,4,80
    CONTROL "",rct3,"Static",SS_GRAYRECT | WS_GROUP,84,88,80,4
    GROUPBOX "Paper",grp2,8,96,224,56,WS_GROUP
    LTEXT "Si&ze:",stc2,16,112,36,8
    COMBOBOX cmb2,64,110,160,160,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    LTEXT "&Source:",stc3,16,132,36,8
    COMBOBOX cmb3,64,130,160,160,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    GROUPBOX "Orientation",grp1,8,156,64,56,WS_GROUP
    RADIOBUTTON "P&ortrait",rad1,16,170,52,12,WS_GROUP | WS_TABSTOP
    RADIOBUTTON "L&andscape",rad2,16,190,52,12
    GROUPBOX "Margins",grp4,80,156,152,56,WS_GROUP
    LTEXT "&Left:",stc15,88,172,32,8
    EDITTEXT edt4,118,170,32,12,WS_GROUP
    LTEXT "&Right:",stc16,164,172,32,8
    EDITTEXT edt6,194,170,32,12,WS_GROUP
    LTEXT "&Top:",stc17,88,192,32,8
    EDITTEXT edt5,118,190,32,12,WS_GROUP
    LTEXT "&Bottom:",stc18,164,192,32,8
    EDITTEXT edt7,194,190,32,12,WS_GROUP
    DEFPUSHBUTTON "OK",IDOK,80,220,48,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,132,220,48,14
    PUSHBUTTON "&Printer...",psh3,184,220,48,14
    PUSHBUTTON "&Help",pshHelp,8,220,48,14
END

PRINTDLGEXORD DIALOG DISCARDABLE 0,0,280,75
STYLE DS_CONTROL | DS_3DLOOK | WS_CHILD
FONT 8,"MS Shell Dlg"
BEGIN
    GROUPBOX "Page Range",grp1,0,0,149,73,WS_GROUP

    RADIOBUTTON "A&ll",rad1,7,11,58,10,WS_GROUP
    RADIOBUTTON "Selec&tion",rad2,7,24,49,10
    RADIOBUTTON "C&urrent Page",rad3,58,24,58,10
    RADIOBUTTON "Pa&ges:",rad4,7,38,38,10
    EDITTEXT edt1,58,37,86,12,WS_GROUP | ES_AUTOHSCROLL
    LTEXT "Enter page numbers and/or page ranges separated by commas.  For example, 1,5-12",stc1,6,52,139,18,NOT WS_VISIBLE | WS_DISABLED
    LTEXT "Enter either a single page number or a single page range.  For example, 5-12",stc2,6,52,139,18,NOT WS_VISIBLE | WS_DISABLED

    GROUPBOX "",grp2,159,0,118,73,WS_GROUP

    LTEXT "Number of &copies:",stc3,164,13,61,10,WS_GROUP
    EDITTEXT edt2,232,11,25,12,ES_NUMBER | WS_GROUP

    AUTOCHECKBOX "C&ollate",chx1,166,38,35,10,WS_GROUP
    ICON "",ico1,206,33,67,36,WS_GROUP | SS_CENTERIMAGE
END
