<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_logon_user</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="func-cygwin-login.html" title="Helper functions to change user context"><link rel="prev" href="func-cygwin-login.html" title="Helper functions to change user context"><link rel="next" href="func-cygwin-set-impersonation-token.html" title="cygwin_set_impersonation_token"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_logon_user</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="func-cygwin-login.html">Prev</a>&#160;</td><th width="60%" align="center">Helper functions to change user context</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-set-impersonation-token.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-logon_user"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_logon_user</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">HANDLE
<b class="fsfunc">cygwin_logon_user</b>(</code>const struct passwd *<var class="pdparam">passwd_entry</var>, const char *<var class="pdparam">password</var><code>)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-logon_user-desc"></a><h2>Description</h2><p>Given a pointer to a passwd entry of a user and a cleartext password,
returns a HANDLE to an impersonation token for this user which can be used
in a subsequent call to <code class="function">cygwin_set_impersonation_token</code>
to impersonate that user.  This function can only be called from a process
which has the required NT user rights to perform a logon.</p></div><div class="refsect1"><a name="func-cygwin-logon_user-also"></a><h2>See also</h2><p>See also the chapter
<a class="ulink" href="../cygwin-ug-net/ntsec.html#ntsec-setuid-overview" target="_top">Switching the user context</a>
in the Cygwin User's guide.</p><p>See also <a class="link" href="func-cygwin-set-impersonation-token.html" title="cygwin_set_impersonation_token">cygwin_set_impersonation_token</a></p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="func-cygwin-login.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="func-cygwin-login.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-set-impersonation-token.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Helper functions to change user context&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;cygwin_set_impersonation_token</td></tr></table></div></body></html>
