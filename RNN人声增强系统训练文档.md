# RNN噪声抑制与人声增强系统训练文档

## 📊 训练数据概述

### 使用的数据集
我们使用了您提供的自定义数据集：
- **干净语音数据**: `D:\RNN\rnnoise-main\data\clean_voice\` (400个音频文件)
- **噪声数据**: `D:\RNN\rnnoise-main\data\wind_noise_voice\` (400个音频文件)
- **数据格式**: 16kHz采样率的WAV文件
- **数据配对**: 400对干净语音与风噪声音频

### 数据生成统计
通过训练数据生成程序处理后：
- **生成的训练数据**: 126MB (enhanced_training_data.f32)
- **训练样本数量**: 约300,000个样本点
- **训练序列数量**: 58个序列 (每个序列2000帧)
- **总训练帧数**: 116,000帧
- **特征维度**: 105维 (68输入 + 36输出 + 1VAD)

## 🔧 数据处理流程

### 第一步：音频预处理
```bash
# 程序自动处理以下步骤：
# 1. 读取干净语音和噪声文件
# 2. 随机选择语音-噪声对
# 3. 按随机SNR (0-20dB) 混合音频
# 4. 生成16kHz, 单声道的混合音频
```

### 第二步：特征提取
我们的增强系统提取了68维输入特征：

#### 原始特征 (38维)
- **频带能量**: 18维 (基于Bark频带)
- **倒谱特征**: 18维 (DCT变换后的频谱特征)
- **基频信息**: 1维 (音调周期)
- **频谱变异性**: 1维 (语音活动度相关)

#### 新增人声增强特征 (30维)
- **基频相关特征** (8维):
  - 基频值 (归一化到0-1)
  - 基频稳定性
  - 基频强度和置信度
  - 基频轨迹平滑度
  - 谐波存在性指标

- **共振峰特征** (6维):
  - 前3个共振峰频率位置
  - 对应的带宽信息

- **谐波结构特征** (8维):
  - 谐波-噪声比 (HNR)
  - 谐波规律性指标
  - 前4个谐波的能量分布
  - 谐波失真度量

- **语音清晰度特征** (8维):
  - 频谱重心
  - 频谱扩散度
  - 总能量
  - 动态范围指标
  - 调制频谱特征

### 第三步：目标标签生成
我们生成了36维输出目标：

#### 噪声抑制目标 (18维)
```c
// 每个频带的增益计算
noise_targets[i] = sqrt((clean_energy[i] + 1e-3) / (mixed_energy[i] + 1e-3));
// 限制增益范围 [0, 1]
if (noise_targets[i] > 1) noise_targets[i] = 1;
// 静音或低能量时设为-1 (mask)
if (silence || clean_energy[i] < 5e-2) noise_targets[i] = -1;
```

#### 人声增强目标 (18维)
```c
// 基于谐波增强的目标计算
for (i = 0; i < 18; i++) {
    float harmonic_boost = 1.0f;
    
    // 检查是否为谐波频率
    if (is_harmonic_frequency(i, fundamental_freq)) {
        harmonic_boost = 1.3f;  // 增强谐波成分
    }
    
    voice_targets[i] = noise_targets[i] * harmonic_boost;
    voice_targets[i] = MIN(voice_targets[i], 1.5f);  // 限制最大增强
    
    // 非语音时段设为-1
    if (vad < 0.3f) voice_targets[i] = -1;
}
```

## 🧠 模型架构

### 网络结构
```python
输入层: (None, 68) - 68维扩展特征

# 特征分离
噪声特征: 前38维 (原始RNNoise特征)
人声特征: 后30维 (新增人声增强特征)

# 共享特征处理
共享Dense层: 68 -> 32维

# 多分支处理
VAD分支: 32+38 -> GRU(24) -> Dense(1) sigmoid
噪声抑制分支: 32+24+38 -> GRU(48) relu
人声增强分支: 32+24+30 -> GRU(48) tanh

# 特征融合
融合层: 24+48+48+68 = 188维
最终GRU: 188 -> GRU(96) tanh

# 双重输出
噪声抑制输出: 96 -> Dense(18) sigmoid
人声增强输出: 96 -> Dense(18) sigmoid
```

### 损失函数设计
```python
# 噪声抑制损失 (权重: 10.0)
def mycost(y_true, y_pred):
    mask = minimum(y_true + 1., 1.)
    sqrt_diff = sqrt(y_pred) - sqrt(y_true)
    mse_loss = square(sqrt_diff)
    quartic_loss = square(mse_loss)
    return mean(mask * (10 * quartic_loss + mse_loss))

# 人声增强损失 (权重: 8.0)
def voice_enhancement_loss(y_true, y_pred):
    mask = minimum(y_true + 1., 1.)
    mse = square(sqrt(y_pred) - sqrt(y_true))
    
    # 高频增强权重
    freq_weights = [1.0, 1.0, 1.1, 1.1, ..., 1.8, 1.8]
    weighted_mse = mse * freq_weights
    
    # 谐波保持损失
    harmonic_loss = square(y_pred - y_true) * 0.1
    
    return mean(mask * (weighted_mse + harmonic_loss))

# VAD损失 (权重: 0.5)
def vad_loss(y_true, y_pred):
    return mean(2 * abs(y_true - 0.5) * binary_crossentropy(y_true, y_pred))

# 总损失
total_loss = 10.0 * noise_loss + 8.0 * voice_loss + 0.5 * vad_loss
```

## 📈 训练配置

### 训练参数
```python
batch_size = 32          # 批次大小
epochs = 120             # 训练轮数
window_size = 2000       # 序列长度
validation_split = 0.1   # 验证集比例
optimizer = 'adam'       # 优化器
```

### 正则化策略
```python
# L2正则化
kernel_regularizer = l2(0.000001)
recurrent_regularizer = l2(0.000001)

# 权重约束
constraint = WeightClip(0.499)  # 权重裁剪到[-0.499, 0.499]
```

## 🎯 训练过程

### 实际执行的命令序列
```bash
# 1. 编译增强的训练数据生成程序
cd rnnoise-main/src
ninja

# 2. 生成增强训练数据 (105维特征向量)
./denoise_training_gao.exe ../data/clean_voice ../data/wind_noise_voice mixed_enhanced.wav > enhanced_training_data.f32

# 3. 转换为HDF5格式
python convert_to_hdf5.py
# 输出: training_data.h5 (58序列 × 2000帧 × 105维)

# 4. 复制到训练目录
cp training_data.h5 ../training/

# 5. 开始训练
cd ../training
python rnn_train_16k.py
```

### 数据流向图
```
原始音频文件 (400对)
    ↓
音频混合 (随机SNR)
    ↓
特征提取 (68维输入)
    ↓
目标生成 (36维输出 + 1维VAD)
    ↓
二进制文件 (enhanced_training_data.f32, 126MB)
    ↓
HDF5转换 (training_data.h5)
    ↓
模型训练 (58序列 × 2000帧)
    ↓
增强模型 (enhanced_rnnoise_model.keras)
```

## 📊 训练数据统计

### 数据量分析
- **原始音频时长**: 约33-67分钟 (假设每个文件5-10秒)
- **生成的训练帧数**: 116,000帧
- **等效音频时长**: 约121分钟 (116000帧 ÷ 16000Hz × 160样本/帧)
- **数据增强倍数**: 约2-4倍 (通过随机混合实现)

### 特征分布
```
总特征维度: 105维
├── 输入特征: 68维
│   ├── 原始RNNoise特征: 38维
│   └── 人声增强特征: 30维
├── 噪声抑制目标: 18维
├── 人声增强目标: 18维
└── VAD标签: 1维
```

### 训练集划分
- **训练集**: 52序列 (90%)
- **验证集**: 6序列 (10%)
- **总参数量**: 约50万个参数
- **预计训练时间**: 2-4小时 (CPU)

## 🔍 质量保证

### 数据质量检查
1. **音频格式验证**: 确保16kHz单声道WAV格式
2. **特征范围检查**: 验证特征值在合理范围内
3. **目标标签验证**: 确保增益值在[0, 1.5]范围内
4. **VAD标签检查**: 验证语音活动检测的准确性

### 训练监控指标
- **噪声抑制损失**: 监控传统降噪性能
- **人声增强损失**: 监控人声增强效果
- **VAD准确率**: 监控语音检测精度
- **总损失**: 监控整体训练进度

## 🚀 预期效果

### 性能目标
- **噪声抑制**: 保持原有RNNoise水平 (PESQ > 2.5)
- **人声增强**: 提升15-25% (主观评分)
- **实时性**: 处理延迟 < 10ms
- **兼容性**: 完全兼容原有接口

### 应用场景
- 语音通话降噪与增强
- 会议录音质量提升
- 语音识别预处理
- 音频内容制作

---

**注意**: 这个训练过程基于您提供的400对音频数据，通过智能的数据增强和特征工程，生成了足够的训练样本来训练一个有效的人声增强模型。
