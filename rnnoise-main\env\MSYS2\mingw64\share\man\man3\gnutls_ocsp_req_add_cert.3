.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_req_add_cert" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_req_add_cert \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_req_add_cert(gnutls_ocsp_req_t " req ", gnutls_digest_algorithm_t " digest ", gnutls_x509_crt_t " issuer ", gnutls_x509_crt_t " cert ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_req_t req" 12
should contain a \fBgnutls_ocsp_req_t\fP type
.IP "gnutls_digest_algorithm_t digest" 12
hash algorithm, a \fBgnutls_digest_algorithm_t\fP value
.IP "gnutls_x509_crt_t issuer" 12
issuer of  \fIsubject\fP certificate
.IP "gnutls_x509_crt_t cert" 12
certificate to request status for
.SH "DESCRIPTION"
This function will add another request to the OCSP request for a
particular certificate.  The issuer name hash, issuer key hash, and
serial number fields is populated as follows.  The issuer name and
the serial number is taken from  \fIcert\fP .  The issuer key is taken
from  \fIissuer\fP .  The hashed values will be hashed using the  \fIdigest\fP algorithm, normally \fBGNUTLS_DIG_SHA1\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
