<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html401/loose.dtd">
<html>
<!-- Created on October, 16 2024 by texi2html 1.78a -->
<!--
Written by: <PERSON> <<EMAIL>> (original author)
            <PERSON>  <<EMAIL>>
            <PERSON> <o<PERSON><EMAIL>>
            and many others.
Maintained by: Many creative people.
Send bugs and suggestions to <<EMAIL>>

-->
<head>
<title>GNU libunistring: 13. Normalization forms (composition and decomposition) &lt;uninorm.h&gt;</title>

<meta name="description" content="GNU libunistring: 13. Normalization forms (composition and decomposition) &lt;uninorm.h&gt;">
<meta name="keywords" content="GNU libunistring: 13. Normalization forms (composition and decomposition) &lt;uninorm.h&gt;">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="texi2html 1.78a">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
pre.display {font-family: serif}
pre.format {font-family: serif}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: serif; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: serif; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.roman {font-family:serif; font-weight:normal;}
span.sansserif {font-family:sans-serif; font-weight:normal;}
ul.toc {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">

<table cellpadding="1" cellspacing="1" border="0">
<tr><td valign="middle" align="left">[<a href="libunistring_12.html#SEC62" title="Beginning of this chapter or previous chapter"> &lt;&lt; </a>]</td>
<td valign="middle" align="left">[<a href="libunistring_14.html#SEC69" title="Next chapter"> &gt;&gt; </a>]</td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Top" title="Cover (top) of document">Top</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Contents" title="Table of contents">Contents</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_21.html#SEC94" title="Index">Index</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_abt.html#SEC_About" title="About (help)"> ? </a>]</td>
</tr></table>

<hr size="2">
<a name="uninorm_002eh"></a>
<a name="SEC63"></a>
<h1 class="chapter"> <a href="libunistring_toc.html#TOC63">13. Normalization forms (composition and decomposition) <code>&lt;uninorm.h&gt;</code></a> </h1>

<p>This include file defines functions for transforming Unicode strings to one
of the four normal forms, known as NFC, NFD, NKFC, NFKD.  These
transformations involve decomposition and &mdash; for NFC and NFKC &mdash; composition
of Unicode characters.
</p>

<hr size="6">
<a name="Decomposition-of-characters"></a>
<a name="SEC64"></a>
<h2 class="section"> <a href="libunistring_toc.html#TOC64">13.1 Decomposition of Unicode characters</a> </h2>

<p>The following enumerated values are the possible types of decomposition of a
Unicode character.
</p>
<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_CANONICAL</b>
<a name="IDX862"></a>
</dt>
<dd><p>Denotes canonical decomposition.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_FONT</b>
<a name="IDX863"></a>
</dt>
<dd><p>UCD marker: <code>&lt;font&gt;</code>.  Denotes a font variant (e.g. a blackletter form).
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_NOBREAK</b>
<a name="IDX864"></a>
</dt>
<dd><p>UCD marker: <code>&lt;noBreak&gt;</code>.
Denotes a no-break version of a space or hyphen.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_INITIAL</b>
<a name="IDX865"></a>
</dt>
<dd><p>UCD marker: <code>&lt;initial&gt;</code>.
Denotes an initial presentation form (Arabic).
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_MEDIAL</b>
<a name="IDX866"></a>
</dt>
<dd><p>UCD marker: <code>&lt;medial&gt;</code>.
Denotes a medial presentation form (Arabic).
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_FINAL</b>
<a name="IDX867"></a>
</dt>
<dd><p>UCD marker: <code>&lt;final&gt;</code>.
Denotes a final presentation form (Arabic).
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_ISOLATED</b>
<a name="IDX868"></a>
</dt>
<dd><p>UCD marker: <code>&lt;isolated&gt;</code>.
Denotes an isolated presentation form (Arabic).
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_CIRCLE</b>
<a name="IDX869"></a>
</dt>
<dd><p>UCD marker: <code>&lt;circle&gt;</code>.
Denotes an encircled form.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_SUPER</b>
<a name="IDX870"></a>
</dt>
<dd><p>UCD marker: <code>&lt;super&gt;</code>.
Denotes a superscript form.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_SUB</b>
<a name="IDX871"></a>
</dt>
<dd><p>UCD marker: <code>&lt;sub&gt;</code>.
Denotes a subscript form.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_VERTICAL</b>
<a name="IDX872"></a>
</dt>
<dd><p>UCD marker: <code>&lt;vertical&gt;</code>.
Denotes a vertical layout presentation form.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_WIDE</b>
<a name="IDX873"></a>
</dt>
<dd><p>UCD marker: <code>&lt;wide&gt;</code>.
Denotes a wide (or zenkaku) compatibility character.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_NARROW</b>
<a name="IDX874"></a>
</dt>
<dd><p>UCD marker: <code>&lt;narrow&gt;</code>.
Denotes a narrow (or hankaku) compatibility character.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_SMALL</b>
<a name="IDX875"></a>
</dt>
<dd><p>UCD marker: <code>&lt;small&gt;</code>.
Denotes a small variant form (CNS compatibility).
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_SQUARE</b>
<a name="IDX876"></a>
</dt>
<dd><p>UCD marker: <code>&lt;square&gt;</code>.
Denotes a CJK squared font variant.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_FRACTION</b>
<a name="IDX877"></a>
</dt>
<dd><p>UCD marker: <code>&lt;fraction&gt;</code>.
Denotes a vulgar fraction form.
</p></dd></dl>

<dl>
<dt><u>Constant:</u> int <b>UC_DECOMP_COMPAT</b>
<a name="IDX878"></a>
</dt>
<dd><p>UCD marker: <code>&lt;compat&gt;</code>.
Denotes an otherwise unspecified compatibility character.
</p></dd></dl>

<p>The following constant denotes the maximum size of decomposition of a single
Unicode character.
</p>
<dl>
<dt><u>Macro:</u> unsigned int <b>UC_DECOMPOSITION_MAX_LENGTH</b>
<a name="IDX879"></a>
</dt>
<dd><p>This macro expands to a constant that is the required size of buffer passed to
the <code>uc_decomposition</code> and <code>uc_canonical_decomposition</code> functions.
</p></dd></dl>

<p>The following functions decompose a Unicode character.
</p>
<dl>
<dt><u>Function:</u> int <b>uc_decomposition</b><i> (ucs4_t&nbsp;<var>uc</var>, int&nbsp;*<var>decomp_tag</var>, ucs4_t&nbsp;*<var>decomposition</var>)</i>
<a name="IDX880"></a>
</dt>
<dd><p>Returns the character decomposition mapping of the Unicode character <var>uc</var>.
<var>decomposition</var> must point to an array of at least
<code>UC_DECOMPOSITION_MAX_LENGTH</code> <code>ucs_t</code> elements.
</p>
<p>When a decomposition exists, <code><var>decomposition</var>[0..<var>n</var>-1]</code> and
<code>*<var>decomp_tag</var></code> are filled and <var>n</var> is returned.  Otherwise -1 is
returned.
</p></dd></dl>

<dl>
<dt><u>Function:</u> int <b>uc_canonical_decomposition</b><i> (ucs4_t&nbsp;<var>uc</var>, ucs4_t&nbsp;*<var>decomposition</var>)</i>
<a name="IDX881"></a>
</dt>
<dd><p>Returns the canonical character decomposition mapping of the Unicode character
<var>uc</var>.  <var>decomposition</var> must point to an array of at least
<code>UC_DECOMPOSITION_MAX_LENGTH</code> <code>ucs_t</code> elements.
</p>
<p>When a decomposition exists, <code><var>decomposition</var>[0..<var>n</var>-1]</code> is filled
and <var>n</var> is returned.  Otherwise -1 is returned.
</p>
<p>Note: This function returns the (simple) &ldquo;canonical decomposition&rdquo; of
<var>uc</var>.  If you want the &ldquo;full canonical decomposition&rdquo; of <var>uc</var>,
that is, the recursive application of &ldquo;canonical decomposition&rdquo;, use the
function <code>u*_normalize</code> with argument <code>UNINORM_NFD</code> instead.
</p></dd></dl>

<hr size="6">
<a name="Composition-of-characters"></a>
<a name="SEC65"></a>
<h2 class="section"> <a href="libunistring_toc.html#TOC65">13.2 Composition of Unicode characters</a> </h2>

<p>The following function composes a Unicode character from two Unicode
characters.
</p>
<dl>
<dt><u>Function:</u> ucs4_t <b>uc_composition</b><i> (ucs4_t&nbsp;<var>uc1</var>, ucs4_t&nbsp;<var>uc2</var>)</i>
<a name="IDX882"></a>
</dt>
<dd><p>Attempts to combine the Unicode characters <var>uc1</var>, <var>uc2</var>.
<var>uc1</var> is known to have canonical combining class 0.
</p>
<p>Returns the combination of <var>uc1</var> and <var>uc2</var>, if it exists.
Returns 0 otherwise.
</p>
<p>Not all decompositions can be recombined using this function.  See the Unicode
file &lsquo;<tt>CompositionExclusions.txt</tt>&rsquo; for details.
</p></dd></dl>

<hr size="6">
<a name="Normalization-of-strings"></a>
<a name="SEC66"></a>
<h2 class="section"> <a href="libunistring_toc.html#TOC66">13.3 Normalization of strings</a> </h2>

<p>The Unicode standard defines four normalization forms for Unicode strings.
The following type is used to denote a normalization form.
</p>
<dl>
<dt><u>Type:</u> <b>uninorm_t</b>
<a name="IDX883"></a>
</dt>
<dd><p>An object of type <code>uninorm_t</code> denotes a Unicode normalization form.
This is a scalar type; its values can be compared with <code>==</code>.
</p></dd></dl>

<p>The following constants denote the four normalization forms.
</p>
<dl>
<dt><u>Macro:</u> uninorm_t <b>UNINORM_NFD</b>
<a name="IDX884"></a>
</dt>
<dd><p>Denotes Normalization form D: canonical decomposition.
</p></dd></dl>

<dl>
<dt><u>Macro:</u> uninorm_t <b>UNINORM_NFC</b>
<a name="IDX885"></a>
</dt>
<dd><p>Normalization form C: canonical decomposition, then canonical composition.
</p></dd></dl>

<dl>
<dt><u>Macro:</u> uninorm_t <b>UNINORM_NFKD</b>
<a name="IDX886"></a>
</dt>
<dd><p>Normalization form KD: compatibility decomposition.
</p></dd></dl>

<dl>
<dt><u>Macro:</u> uninorm_t <b>UNINORM_NFKC</b>
<a name="IDX887"></a>
</dt>
<dd><p>Normalization form KC: compatibility decomposition, then canonical composition.
</p></dd></dl>

<p>The following functions operate on <code>uninorm_t</code> objects.
</p>
<dl>
<dt><u>Function:</u> bool <b>uninorm_is_compat_decomposing</b><i> (uninorm_t&nbsp;<var>nf</var>)</i>
<a name="IDX888"></a>
</dt>
<dd><p>Tests whether the normalization form <var>nf</var> does compatibility decomposition.
</p></dd></dl>

<dl>
<dt><u>Function:</u> bool <b>uninorm_is_composing</b><i> (uninorm_t&nbsp;<var>nf</var>)</i>
<a name="IDX889"></a>
</dt>
<dd><p>Tests whether the normalization form <var>nf</var> includes canonical composition.
</p></dd></dl>

<dl>
<dt><u>Function:</u> uninorm_t <b>uninorm_decomposing_form</b><i> (uninorm_t&nbsp;<var>nf</var>)</i>
<a name="IDX890"></a>
</dt>
<dd><p>Returns the decomposing variant of the normalization form <var>nf</var>.
This maps NFC,NFD → NFD and NFKC,NFKD → NFKD.
</p></dd></dl>

<p>The following functions apply a Unicode normalization form to a Unicode string.
</p>
<dl>
<dt><u>Function:</u> uint8_t * <b>u8_normalize</b><i> (uninorm_t&nbsp;<var>nf</var>, const&nbsp;uint8_t&nbsp;*<var>s</var>, size_t&nbsp;<var>n</var>, uint8_t&nbsp;*<var>resultbuf</var>, size_t&nbsp;*<var>lengthp</var>)</i>
<a name="IDX891"></a>
</dt>
<dt><u>Function:</u> uint16_t * <b>u16_normalize</b><i> (uninorm_t&nbsp;<var>nf</var>, const&nbsp;uint16_t&nbsp;*<var>s</var>, size_t&nbsp;<var>n</var>, uint16_t&nbsp;*<var>resultbuf</var>, size_t&nbsp;*<var>lengthp</var>)</i>
<a name="IDX892"></a>
</dt>
<dt><u>Function:</u> uint32_t * <b>u32_normalize</b><i> (uninorm_t&nbsp;<var>nf</var>, const&nbsp;uint32_t&nbsp;*<var>s</var>, size_t&nbsp;<var>n</var>, uint32_t&nbsp;*<var>resultbuf</var>, size_t&nbsp;*<var>lengthp</var>)</i>
<a name="IDX893"></a>
</dt>
<dd><p>Returns the specified normalization form of a string.
</p>
<p>The <var>resultbuf</var> and <var>lengthp</var> arguments are as described in
chapter <a href="libunistring_2.html#SEC8">Conventions</a>.
</p></dd></dl>

<hr size="6">
<a name="Normalizing-comparisons"></a>
<a name="SEC67"></a>
<h2 class="section"> <a href="libunistring_toc.html#TOC67">13.4 Normalizing comparisons</a> </h2>

<p>The following functions compare Unicode string, ignoring differences in
normalization.
</p>
<dl>
<dt><u>Function:</u> int <b>u8_normcmp</b><i> (const&nbsp;uint8_t&nbsp;*<var>s1</var>, size_t&nbsp;<var>n1</var>, const&nbsp;uint8_t&nbsp;*<var>s2</var>, size_t&nbsp;<var>n2</var>, uninorm_t&nbsp;<var>nf</var>, int&nbsp;*<var>resultp</var>)</i>
<a name="IDX894"></a>
</dt>
<dt><u>Function:</u> int <b>u16_normcmp</b><i> (const&nbsp;uint16_t&nbsp;*<var>s1</var>, size_t&nbsp;<var>n1</var>, const&nbsp;uint16_t&nbsp;*<var>s2</var>, size_t&nbsp;<var>n2</var>, uninorm_t&nbsp;<var>nf</var>, int&nbsp;*<var>resultp</var>)</i>
<a name="IDX895"></a>
</dt>
<dt><u>Function:</u> int <b>u32_normcmp</b><i> (const&nbsp;uint32_t&nbsp;*<var>s1</var>, size_t&nbsp;<var>n1</var>, const&nbsp;uint32_t&nbsp;*<var>s2</var>, size_t&nbsp;<var>n2</var>, uninorm_t&nbsp;<var>nf</var>, int&nbsp;*<var>resultp</var>)</i>
<a name="IDX896"></a>
</dt>
<dd><p>Compares <var>s1</var> and <var>s2</var>, ignoring differences in normalization.
</p>
<p><var>nf</var> must be either <code>UNINORM_NFD</code> or <code>UNINORM_NFKD</code>.
</p>
<p>If successful, sets <code>*<var>resultp</var></code> to -1 if <var>s1</var> &lt; <var>s2</var>,
0 if <var>s1</var> = <var>s2</var>, 1 if <var>s1</var> &gt; <var>s2</var>, and returns 0.
Upon failure, returns -1 with <code>errno</code> set.
</p></dd></dl>

<a name="IDX897"></a>
<a name="IDX898"></a>
<dl>
<dt><u>Function:</u> char * <b>u8_normxfrm</b><i> (const&nbsp;uint8_t&nbsp;*<var>s</var>, size_t&nbsp;<var>n</var>, uninorm_t&nbsp;<var>nf</var>, char&nbsp;*<var>resultbuf</var>, size_t&nbsp;*<var>lengthp</var>)</i>
<a name="IDX899"></a>
</dt>
<dt><u>Function:</u> char * <b>u16_normxfrm</b><i> (const&nbsp;uint16_t&nbsp;*<var>s</var>, size_t&nbsp;<var>n</var>, uninorm_t&nbsp;<var>nf</var>, char&nbsp;*<var>resultbuf</var>, size_t&nbsp;*<var>lengthp</var>)</i>
<a name="IDX900"></a>
</dt>
<dt><u>Function:</u> char * <b>u32_normxfrm</b><i> (const&nbsp;uint32_t&nbsp;*<var>s</var>, size_t&nbsp;<var>n</var>, uninorm_t&nbsp;<var>nf</var>, char&nbsp;*<var>resultbuf</var>, size_t&nbsp;*<var>lengthp</var>)</i>
<a name="IDX901"></a>
</dt>
<dd><p>Converts the string <var>s</var> of length <var>n</var> to a NUL-terminated byte
sequence, in such a way that comparing <code>u8_normxfrm (<var>s1</var>)</code> and
<code>u8_normxfrm (<var>s2</var>)</code> with the <code>u8_cmp2</code> function is equivalent to
comparing <var>s1</var> and <var>s2</var> with the <code>u8_normcoll</code> function.
</p>
<p><var>nf</var> must be either <code>UNINORM_NFC</code> or <code>UNINORM_NFKC</code>.
</p>
<p>The <var>resultbuf</var> and <var>lengthp</var> arguments are as described in
chapter <a href="libunistring_2.html#SEC8">Conventions</a>.
</p></dd></dl>

<dl>
<dt><u>Function:</u> int <b>u8_normcoll</b><i> (const&nbsp;uint8_t&nbsp;*<var>s1</var>, size_t&nbsp;<var>n1</var>, const&nbsp;uint8_t&nbsp;*<var>s2</var>, size_t&nbsp;<var>n2</var>, uninorm_t&nbsp;<var>nf</var>, int&nbsp;*<var>resultp</var>)</i>
<a name="IDX902"></a>
</dt>
<dt><u>Function:</u> int <b>u16_normcoll</b><i> (const&nbsp;uint16_t&nbsp;*<var>s1</var>, size_t&nbsp;<var>n1</var>, const&nbsp;uint16_t&nbsp;*<var>s2</var>, size_t&nbsp;<var>n2</var>, uninorm_t&nbsp;<var>nf</var>, int&nbsp;*<var>resultp</var>)</i>
<a name="IDX903"></a>
</dt>
<dt><u>Function:</u> int <b>u32_normcoll</b><i> (const&nbsp;uint32_t&nbsp;*<var>s1</var>, size_t&nbsp;<var>n1</var>, const&nbsp;uint32_t&nbsp;*<var>s2</var>, size_t&nbsp;<var>n2</var>, uninorm_t&nbsp;<var>nf</var>, int&nbsp;*<var>resultp</var>)</i>
<a name="IDX904"></a>
</dt>
<dd><p>Compares <var>s1</var> and <var>s2</var>, ignoring differences in normalization, using
the collation rules of the current locale.
</p>
<p><var>nf</var> must be either <code>UNINORM_NFC</code> or <code>UNINORM_NFKC</code>.
</p>
<p>If successful, sets <code>*<var>resultp</var></code> to -1 if <var>s1</var> &lt; <var>s2</var>,
0 if <var>s1</var> = <var>s2</var>, 1 if <var>s1</var> &gt; <var>s2</var>, and returns 0.
Upon failure, returns -1 with <code>errno</code> set.
</p></dd></dl>

<hr size="6">
<a name="Normalization-of-streams"></a>
<a name="SEC68"></a>
<h2 class="section"> <a href="libunistring_toc.html#TOC68">13.5 Normalization of streams of Unicode characters</a> </h2>

<p>A &ldquo;stream of Unicode characters&rdquo; is essentially a function that accepts an
<code>ucs4_t</code> argument repeatedly, optionally combined with a function that
&ldquo;flushes&rdquo; the stream.
</p>
<dl>
<dt><u>Type:</u> <b>struct uninorm_filter</b>
<a name="IDX905"></a>
</dt>
<dd><p>This is the data type of a stream of Unicode characters that normalizes its
input according to a given normalization form and passes the normalized
character sequence to the encapsulated stream of Unicode characters.
</p></dd></dl>

<dl>
<dt><u>Function:</u> struct uninorm_filter * <b>uninorm_filter_create</b><i> (uninorm_t&nbsp;<var>nf</var>, int&nbsp;(*<var>stream_func</var>)&nbsp;(void&nbsp;*<var>stream_data</var>, ucs4_t&nbsp;<var>uc</var>), void&nbsp;*<var>stream_data</var>)</i>
<a name="IDX906"></a>
</dt>
<dd><p>Creates and returns a normalization filter for Unicode characters.
</p>
<p>The pair (<var>stream_func</var>, <var>stream_data</var>) is the encapsulated stream.
<code><var>stream_func</var> (<var>stream_data</var>, <var>uc</var>)</code> receives the Unicode
character <var>uc</var> and returns 0 if successful, or -1 with <code>errno</code> set
upon failure.
</p>
<p>Returns the new filter, or NULL with <code>errno</code> set upon failure.
</p></dd></dl>

<dl>
<dt><u>Function:</u> int <b>uninorm_filter_write</b><i> (struct&nbsp;uninorm_filter&nbsp;*<var>filter</var>, ucs4_t&nbsp;<var>uc</var>)</i>
<a name="IDX907"></a>
</dt>
<dd><p>Stuffs a Unicode character into a normalizing filter.
Returns 0 if successful, or -1 with <code>errno</code> set upon failure.
</p></dd></dl>

<dl>
<dt><u>Function:</u> int <b>uninorm_filter_flush</b><i> (struct&nbsp;uninorm_filter&nbsp;*<var>filter</var>)</i>
<a name="IDX908"></a>
</dt>
<dd><p>Brings data buffered in the filter to its destination, the encapsulated stream.
</p>
<p>Returns 0 if successful, or -1 with <code>errno</code> set upon failure.
</p>
<p>Note! If after calling this function, additional characters are written
into the filter, the resulting character sequence in the encapsulated stream
will not necessarily be normalized.
</p></dd></dl>

<dl>
<dt><u>Function:</u> int <b>uninorm_filter_free</b><i> (struct&nbsp;uninorm_filter&nbsp;*<var>filter</var>)</i>
<a name="IDX909"></a>
</dt>
<dd><p>Brings data buffered in the filter to its destination, the encapsulated stream,
then closes and frees the filter.
</p>
<p>Returns 0 if successful, or -1 with <code>errno</code> set upon failure.
</p></dd></dl>
<hr size="6">
<table cellpadding="1" cellspacing="1" border="0">
<tr><td valign="middle" align="left">[<a href="#SEC63" title="Beginning of this chapter or previous chapter"> &lt;&lt; </a>]</td>
<td valign="middle" align="left">[<a href="libunistring_14.html#SEC69" title="Next chapter"> &gt;&gt; </a>]</td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Top" title="Cover (top) of document">Top</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Contents" title="Table of contents">Contents</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_21.html#SEC94" title="Index">Index</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_abt.html#SEC_About" title="About (help)"> ? </a>]</td>
</tr></table>
<p>
 <font size="-1">
  This document was generated by <em>Bruno Haible</em> on <em>October, 16 2024</em> using <a href="https://www.nongnu.org/texi2html/"><em>texi2html 1.78a</em></a>.
 </font>
 <br>

</p>
</body>
</html>
