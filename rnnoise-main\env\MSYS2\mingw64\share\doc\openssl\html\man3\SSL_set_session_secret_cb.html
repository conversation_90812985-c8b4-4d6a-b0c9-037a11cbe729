<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_set_session_secret_cb</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_set_session_secret_cb, tls_session_secret_cb_fn - set the session secret callback</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

typedef int (*tls_session_secret_cb_fn)(SSL *s, void *secret, int *secret_len,
                                        STACK_OF(SSL_CIPHER) *peer_ciphers,
                                        const SSL_CIPHER **cipher, void *arg);

int SSL_set_session_secret_cb(SSL *s,
                              tls_session_secret_cb_fn session_secret_cb,
                              void *arg);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_set_session_secret_cb() sets the session secret callback to be used (<i>session_secret_cb</i>), and an optional argument (<i>arg</i>) to be passed to that callback when it is called. This is only useful for an implementation of EAP-FAST (RFC4851). The presence of the callback also modifies the internal OpenSSL TLS state machine to match the modified TLS behaviour as described in RFC4851. Therefore this callback should not be used except when implementing EAP-FAST.</p>

<p>The callback is expected to set the master secret to be used by filling in the data pointed to by <i>*secret</i>. The size of the secret buffer is initially available in <i>*secret_len</i> and may be updated by the callback (but must not be larger than the initial value).</p>

<p>On the server side the set of ciphersuites offered by the peer is provided in the <i>peer_ciphers</i> stack. Optionally the callback may select the preferred ciphersuite by setting it in <i>*cipher</i>.</p>

<p>On the client side the <i>peer_ciphers</i> stack will always be NULL. The callback may specify the preferred cipher in <i>*cipher</i> and this will be associated with the <b>SSL_SESSION</b> - but it does not affect the ciphersuite selected by the server.</p>

<p>The callback is also supplied with an additional argument in <i>arg</i> which is the argument that was provided to the original SSL_set_session_secret_cb() call.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_set_session_secret_cb() returns 1 on success and 0 on failure.</p>

<p>If the callback returns 1 then this indicates it has successfully set the secret. A return value of 0 indicates that the secret has not been set. On the client this will cause an immediate abort of the handshake.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_get_session.html">SSL_get_session(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


