<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Coding Style: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="devel.html" title="Building, Packaging, and Contributing to p11-kit">
<link rel="prev" href="devel-building.html" title="Compiling p11-kit from Source">
<link rel="next" href="devel-testing.html" title="Testing and Code Coverage">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="devel.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="devel-building.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="devel-testing.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="devel-building-style"></a>Coding Style</h2></div></div></div>
<p>We use a code style similar to the linux kernel. Use tabs
		to indent and spaces to align/wrap beyond the indentation level.</p>
<p>We don't try to guarantee completely robust and problem free
		behavior in cases where the caller or system isn't behaving. We
		consider these to be outside of our control:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p>Broken input from callers. We use preconditions
			to check input and immediately return. We don't try to provide
			error codes for all the various ways callers can screw
			around.</p></li>
<li class="listitem">
<p>Out of memory. It is pretty much impossible to handle out
			of memory errors correctly. Handling them alongside other errors
			is naive and broken. We don't try to guarantee library state
			(such as locks or memory leaks) when memory allocation fails.</p>
<p>We do check the results from all memory allocations, but
			treat them as unexpected conditions. As a nod to the behavior
			of callers of this library, we don't abort on memory allocation
			failures. We use preconditions with somewhat sane results.</p>
<p>Exception: when reading files or allocating potentially
			unbounded amounts of memory, we should respond robustly to memory
			allocation failures.</p>
</li>
</ul></div>
<p>These unexpected conditions indicate a bug either in p11-kit or
		in the system. All bets are off once this occurs.</p>
<p>Use the <code class="literal">return_val_xxx()</code> precondition macros to
		check for unexpected conditions.</p>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>