<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_options_bcj Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structlzma__options__bcj.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_options_bcj Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Options for BCJ filters.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;bcj.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a3f5a3c62cd82ce89433684f12ed096ac" id="r_a3f5a3c62cd82ce89433684f12ed096ac"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3f5a3c62cd82ce89433684f12ed096ac">start_offset</a></td></tr>
<tr class="memdesc:a3f5a3c62cd82ce89433684f12ed096ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Start offset for conversions.  <br /></td></tr>
<tr class="separator:a3f5a3c62cd82ce89433684f12ed096ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Options for BCJ filters. </p>
<p>The BCJ filters never change the size of the data. Specifying options for them is optional: if pointer to options is NULL, default value is used. You probably never need to specify options to BCJ filters, so just set the options pointer to NULL and be happy.</p>
<p>If options with non-default values have been specified when encoding, the same options must also be specified when decoding.</p>
<dl class="section note"><dt>Note</dt><dd>At the moment, none of the BCJ filters support LZMA_SYNC_FLUSH. If LZMA_SYNC_FLUSH is specified, LZMA_OPTIONS_ERROR will be returned. If there is need, partial support for LZMA_SYNC_FLUSH can be added in future. Partial means that flushing would be possible only at offsets that are multiple of 2, 4, or 16 depending on the filter, except x86 which cannot be made to support LZMA_SYNC_FLUSH predictably. </dd></dl>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a3f5a3c62cd82ce89433684f12ed096ac" name="a3f5a3c62cd82ce89433684f12ed096ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3f5a3c62cd82ce89433684f12ed096ac">&#9670;&#160;</a></span>start_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_bcj::start_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Start offset for conversions. </p>
<p>This setting is useful only when the same filter is used <em>separately</em> for multiple sections of the same executable file, and the sections contain cross-section branch/call/jump instructions. In that case it is beneficial to set the start offset of the non-first sections so that the relative addresses of the cross-section branch/call/jump instructions will use the same absolute addresses as in the first section.</p>
<p>When the pointer to options is NULL, the default value (zero) is used. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="bcj_8h.html">bcj.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structlzma__options__bcj.html">lzma_options_bcj</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
