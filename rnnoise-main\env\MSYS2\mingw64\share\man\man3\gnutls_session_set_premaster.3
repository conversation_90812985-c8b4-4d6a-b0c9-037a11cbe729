.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_set_premaster" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_set_premaster \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_set_premaster(gnutls_session_t " session ", unsigned int " entity ", gnutls_protocol_t " version ", gnutls_kx_algorithm_t " kx ", gnutls_cipher_algorithm_t " cipher ", gnutls_mac_algorithm_t " mac ", gnutls_compression_method_t " comp ", const gnutls_datum_t * " master ", const gnutls_datum_t * " session_id ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int entity" 12
GNUTLS_SERVER or GNUTLS_CLIENT
.IP "gnutls_protocol_t version" 12
the TLS protocol version
.IP "gnutls_kx_algorithm_t kx" 12
the key exchange method
.IP "gnutls_cipher_algorithm_t cipher" 12
the cipher
.IP "gnutls_mac_algorithm_t mac" 12
the MAC algorithm
.IP "gnutls_compression_method_t comp" 12
the compression method (ignored)
.IP "const gnutls_datum_t * master" 12
the master key to use
.IP "const gnutls_datum_t * session_id" 12
the session identifier
.SH "DESCRIPTION"
This function sets the premaster secret in a session. This is
a function intended for exceptional uses. Do not use this
function unless you are implementing a legacy protocol.
Use \fBgnutls_session_set_data()\fP instead.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
