# help.txt - English GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
#
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


# Note that this help file needs to be UTF-8 encoded.  When looking
# for a help item, GnuPG scans the help files in the following order
# (assuming a GNU or Unix system):
#
#    /etc/gnupg/help.LL_TT.txt
#    /etc/gnupg/help.LL.txt
#    /etc/gnupg/help.txt
#    /usr/share/gnupg/help.LL_TT.txt
#    /usr/share/gnupg/help.LL.txt
#    /usr/share/gnupg/help.txt
#
# Here LL_TT denotes the full name of the current locale with the
# territory (.e.g. "de_DE"), LL denotes just the locale name
# (e.g. "de").  The first matching item is returned.  To put a dot or
# a hash mark at the beginning of a help text line, it needs to be
# prefixed with ". ".  A single dot may be used to terminated a help
# entry.

.#pinentry.qualitybar.tooltip
# [remove the hash mark from the key to enable this text]
# This entry is just an example on how to customize the tooltip shown
# when hovering over the quality bar of the pinentry.  We don't
# install this text so that the hardcoded translation takes
# precedence.  An administrator should write up a short help to tell
# the users about the configured passphrase constraints and save that
# to /etc/gnupg/help.txt.  The help text should not be longer than
# about 800 characters.
This bar indicates the quality of the passphrase entered above.

As long as the bar is shown in red, GnuPG considers the passphrase too
weak to accept.  Please ask your administrator for details about the
configured passphrase constraints.
.


.#pinentry.constraints.hint.short
# [remove the hash mark from the key to enable this hint]
# This entry is used by some pinentries to display a hint about
# enabled passphrase constraints.  These constraints are configurable
# and the admin may give a hint about them by using this help entry.
Please use letters and digits.
.


.#pinentry.constraints.hint.long
# [remove the hash mark from the key to enable this hint]
# This entry is used by some pinentries to show a tooltip with more
# information about the configured passphrase constraints.
Please use letters and digits.
Extra constraints are enforced, for example
the use of common car number plates.
.


.#pinentry.formatted_passphrase.hint",
# [remove the hash mark from the key to enable this hint]
# If this entry is not set a standard text is shown
Note: The blanks are not part of the passphrase.
.


.gnupg.agent-problem
# There was a problem accessing or starting the agent.
It was either not possible to connect to a running Gpg-Agent or a
communication problem with a running agent occurred.

The system uses a background process, called Gpg-Agent, for processing
private keys and to ask for passphrases.  The agent is usually started
when the user logs in and runs as long the user is logged in. In case
that no agent is available, the system tries to start one on the fly
but that version of the agent is somewhat limited in functionality and
thus may lead to little problems.

You probably need to ask your administrator on how to solve the
problem.  As a workaround you might try to log out and in to your
session and see whether this helps.  If this helps please tell the
administrator anyway because this indicates a bug in the software.
.


.gnupg.dirmngr-problem
# There was a problen accessing the dirmngr.
It was either not possible to connect to a running Dirmngr or a
communication problem with a running Dirmngr occurred.

To lookup certificate revocation lists (CRLs), performing OCSP
validation and to lookup keys through LDAP servers, the system uses an
external service program named Dirmngr.  The Dirmngr is usually running
as a system service (daemon) and does not need any attention by the
user.  In case of problems the system might start its own copy of the
Dirmngr on a per request base; this is a workaround and yields limited
performance.

If you encounter this problem, you should ask your system
administrator how to proceed.  As an interim solution you may try to
disable CRL checking in gpgsm's configuration.
.


.gpg.edit_ownertrust.value
# The help identies prefixed with "gpg." used to be hard coded in gpg
# but may now be overridden by help texts from this file.
It's up to you to assign a value here; this value will never be exported
to any 3rd party.  We need it to implement the web-of-trust; it has nothing
to do with the (implicitly created) web-of-certificates.
.

.gpg.edit_ownertrust.set_ultimate.okay
To build the Web-of-Trust, GnuPG needs to know which keys are
ultimately trusted - those are usually the keys for which you have
access to the secret key.  Answer "yes" to set this key to
ultimately trusted.


.gpg.untrusted_key.override
If you want to use this untrusted key anyway, answer "yes".
.

.gpg.pklist.user_id.enter
Enter the user ID of the addressee to whom you want to send the message.
.

.gpg.keygen.algo
Select the algorithm to use.

DSA (aka DSS) is the Digital Signature Algorithm and can only be used
for signatures.

Elgamal is an encrypt-only algorithm.

RSA may be used for signatures or encryption.

The first (primary) key must always be a key which is capable of signing.
.


.gpg.keygen.algo.rsa_se
In general it is not a good idea to use the same key for signing and
encryption.  This algorithm should only be used in certain domains.
Please consult your security expert first.
.

.gpg.keygen.cardkey
Select which key from the card shall be used.

The listing shows the selection index, the keygrip (a string of hex
digits), the card specific key reference, the algorithm used for this
key, and in parentheses the usage of the key (cert, sign, auth, encr).
If known the standard usage for a key is marked with an asterisk.
.


.gpg.keygen.keygrip
Enter the keygrip of the key to add.

The keygrip is a string of 40 hex digits that identifies a key.  It
must belong to a secret key or a secret subkey stored in your keyring.
.


.gpg.keygen.flags
Toggle the capabilities of the key.

It is only possible to toggle those capabilities which are possible
for the selected algorithm.

To quickly set the capabilities all at once it is possible to enter a
'=' as first character followed by a list of letters indicating the
capability to set: 's' for signing, 'e' for encryption, and 'a' for
authentication.  Invalid letters and impossible capabilities are
ignored.  This submenu is immediately closed after using this
shortcut.
.


.gpg.keygen.size
Enter the size of the key.

The suggested default is usually a good choice.

If you want to use a large key size, for example 4096 bit, please
think again whether it really makes sense for you.  You may want
to view the web page https://www.xkcd.com/538/ .
.

.gpg.keygen.size.huge.okay
Answer "yes" or "no".
.


.gpg.keygen.size.large.okay
Answer "yes" or "no".
.


.gpg.keygen.valid
Enter the required value as shown in the prompt.
It is possible to enter an ISO date (YYYY-MM-DD) but you won't
get a good error response - instead the system tries to interpret
the given value as an interval.
.

.gpg.keygen.valid.okay
Answer "yes" or "no".
.


.gpg.keygen.name
Enter the name of the key holder.
The characters "<" and ">" are not allowed.
Example: Heinrich Heine
.


.gpg.keygen.email
Please enter an optional but highly suggested email address.
Example: <EMAIL>
.

.gpg.keygen.comment
Please enter an optional comment.
The characters "(" and ")" are not allowed.
In general there is no need for a comment.
.


.gpg.keygen.userid.cmd
# (Keep a leading empty line)

N  to change the name.
C  to change the comment.
E  to change the email address.
O  to continue with key generation.
Q  to quit the key generation.
.

.gpg.keygen.sub.okay
Answer "yes" (or just "y") if it is okay to generate the sub key.
.

.gpg.sign_uid.okay
Answer "yes" or "no".
.

.gpg.sign_uid.class
When you sign a user ID on a key, you should first verify that the key
belongs to the person named in the user ID.  It is useful for others to
know how carefully you verified this.

"0" means you make no particular claim as to how carefully you verified the
    key.

"1" means you believe the key is owned by the person who claims to own it
    but you could not, or did not verify the key at all.  This is useful for
    a "persona" verification, where you sign the key of a pseudonymous user.

"2" means you did casual verification of the key.  For example, this could
    mean that you verified the key fingerprint and checked the user ID on the
    key against a photo ID.

"3" means you did extensive verification of the key.  For example, this could
    mean that you verified the key fingerprint with the owner of the key in
    person, and that you checked, by means of a hard to forge document with a
    photo ID (such as a passport) that the name of the key owner matches the
    name in the user ID on the key, and finally that you verified (by exchange
    of email) that the email address on the key belongs to the key owner.

Note that the examples given above for levels 2 and 3 are *only* examples.
In the end, it is up to you to decide just what "casual" and "extensive"
mean to you when you sign other keys.

If you don't know what the right answer is, answer "0".
.

.gpg.change_passwd.empty.okay
Answer "yes" or "no".
.


.gpg.keyedit.save.okay
Answer "yes" or "no".
.


.gpg.keyedit.cancel.okay
Answer "yes" or "no".
.

.gpg.keyedit.sign_all.okay
Answer "yes" if you want to sign ALL the user IDs.
.

.gpg.keyedit.remove.uid.okay
Answer "yes" if you really want to delete this user ID.
All certificates are then also lost!
.

.gpg.keyedit.remove.subkey.okay
Answer "yes" if it is okay to delete the subkey.
.


.gpg.keyedit.delsig.valid
This is a valid signature on the key; you normally don't want
to delete this signature because it may be important to establish a
trust connection to the key or another key certified by this key.
.

.gpg.keyedit.delsig.unknown
This signature can't be checked because you don't have the
corresponding key.  You should postpone its deletion until you
know which key was used because this signing key might establish
a trust connection through another already certified key.
.

.gpg.keyedit.delsig.invalid
The signature is not valid.  It does make sense to remove it from
your keyring.
.

.gpg.keyedit.delsig.selfsig
This is a signature which binds the user ID to the key. It is
usually not a good idea to remove such a signature.  Actually
GnuPG might not be able to use this key anymore.  So do this
only if this self-signature is for some reason not valid and
a second one is available.
.

.gpg.keyedit.updpref.okay
Change the preferences of all user IDs (or just of the selected ones)
to the current list of preferences.  The timestamp of all affected
self-signatures will be advanced by one second.
.


.gpg.passphrase.enter
# (keep a leading empty line)

Please enter the passphrase; this is a secret sentence.
.


.gpg.passphrase.repeat
Please repeat the last passphrase, so you are sure what you typed in.
.

.gpg.detached_signature.filename
Give the name of the file to which the signature applies.
.

.gpg.openfile.overwrite.okay
# openfile.c (overwrite_filep)
Answer "yes" if it is okay to overwrite the file.
.

.gpg.openfile.askoutname
# openfile.c (ask_outfile_name)
Please enter a new filename.  If you just hit RETURN the default
file (which is shown in brackets) will be used.
.

.gpg.ask_revocation_reason.code
# revoke.c (ask_revocation_reason)
You should specify a reason for the revocation.  Depending on the
context you have the ability to choose from this list:
  "Key has been compromised"
      Use this if you have a reason to believe that unauthorized persons
      got access to your secret key.
  "Key is superseded"
      Use this if you have replaced this key with a newer one.
  "Key is no longer used"
      Use this if you have retired this key.
  "User ID is no longer valid"
      Use this to state that the user ID should not longer be used;
      this is normally used to mark an email address invalid.
.

.gpg.ask_revocation_reason.text
# revoke.c (ask_revocation_reason)
If you like, you can enter a text describing why you issue this
revocation certificate.  Please keep this text concise.
An empty line ends the text.
.

.gpg.tofu.conflict
# tofu.c
TOFU has detected another key with the same (or a very similar) email
address.  It might be that the user created a new key.  In this case,
you can safely trust the new key (but, confirm this by asking the
person).  However, it could also be that the key is a forgery or there
is an active Man-in-the-Middle (MitM) attack.  In this case, you
should mark the key as being bad, so that it is untrusted.  Marking a
key as being untrusted means that any signatures will be considered
bad and attempts to encrypt to the key will be flagged.  If you are
unsure and can't currently check, you should select either accept once
or reject once.
.

.gpgsm.root-cert-not-trusted
# This text gets displayed by the audit log if
# a root certificates was not trusted.
The root certificate (the trust-anchor) is not trusted.  Depending on
the configuration you may have been prompted to mark that root
certificate as trusted or you need to manually tell GnuPG to trust that
certificate.  Trusted certificates are configured in the file
trustlist.txt in GnuPG's home directory.  If you are in doubt, ask
your system administrator whether you should trust this certificate.


.gpgsm.crl-problem
# This text is displayed by the audit log for problems with
# the CRL or OCSP checking.
Depending on your configuration a problem retrieving the CRL or
performing an OCSP check occurred.  There are a great variety of
reasons why this did not work.  Check the manual for possible
solutions.


# Local variables:
# mode: default-generic
# coding: utf-8
# End:
