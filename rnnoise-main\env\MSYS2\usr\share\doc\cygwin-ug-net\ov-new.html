<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>What's new and what changed in Cygwin</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="overview.html" title="Chapter&#160;1.&#160;Cygwin Overview"><link rel="prev" href="highlights.html" title="Highlights of Cygwin Functionality"><link rel="next" href="setup-net.html" title="Chapter&#160;2.&#160;Setting Up Cygwin"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">What's new and what changed in Cygwin</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="highlights.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;1.&#160;Cygwin Overview</th><td width="20%" align="right">&#160;<a accesskey="n" href="setup-net.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ov-new"></a>What's new and what changed in Cygwin</h2></div></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new3.6"></a>What's new and what changed in 3.6</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New API calls: tcgetwinsize, tcsetwinsize.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API call: getlocalename_l.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API calls: fdclosedir, posix_getdents.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API call: setproctitle.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API call: timespec_get.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API calls: tcgetwinsize, tcsetwinsize.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API call: posix_close.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New POSIX-defined entry points posix_spawn_file_actions_addchdir and
posix_spawn_file_actions_addfchdir.  These are the same as the already
exported posix_spawn_file_actions_addchdir_np and
posix_spawn_file_actions_addfchdir_np.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add FS_IOC_[GS]ETFLAGS FS_PINNED_FL and FS_UNPINNED_FL flags to handle
Windows attributes FILE_ATTRIBUTE_PINNED and FILE_ATTRIBUTE_UNPINNED.
Add matching 'p' and 'u' mode bits in chattr(1) and lsattr(1).
</p></li><li class="listitem" style="list-style-type: disc"><p>
New libaio.a provided for projects checking for POSIX aio support by
looking for this library at configure time.
</p></li><li class="listitem" style="list-style-type: disc"><p>
cygpath -r option allows to generate all Windows paths with root-local
path prefix \\?\.
</p></li><li class="listitem" style="list-style-type: disc"><p>
ps -f now prints the commandline rather than the full path to the
executable.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Drop workarounds for connecting with NT4 and Samba &lt; 3.0.22 shares.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Now that SMBv1 is ultimately deprecated and not installed by default on
latest Windows versions, use Network Discovery (i. e. WSD, "Web Service
Discovery") for enumerating network servers in //, just like Windows
Explorer.
</p></li><li class="listitem" style="list-style-type: disc"><p>
If "server" is given as FQDN, and if "server" is an NFS server, ls
//server now also enumerates NFS shares.  If "server" is given as a flat
name, only SMB shares are enumerated.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Expose WebDav shares, //tsclient (Microsoft Terminal Services) shares as
well as //wsl$ (Plan 9 Network Provider) shares, i. e., WSL installation
root dirs.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Redesign pipe handling to minimize toggling blocking mode.  The
query_hdl stuff is no longer needed in new implementation.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Now using AVX/AVX2/AVX-512 instructions in signal handler does not break
their context.
</p></li><li class="listitem" style="list-style-type: disc"><p>
nice(2), setpriority(2) and sched_setparam(2) now fail with EACCES or
EPERM if Windows would silently set a lower priority
(HIGH_PRIORITY_CLASS instead of REALTIME_PRIORITY_CLASS) due to missing
administrator privileges.
</p></li><li class="listitem" style="list-style-type: disc"><p>
nice(2) now returns the new nice value instead of 0 on success and sets
errno to EPERM instead of EACCES on failure.  This confirms to POSIX and
Linux (glibc &gt;= 2.2.4) behavior.
</p></li><li class="listitem" style="list-style-type: disc"><p>
sched_setscheduler(2) now emulates changes between SCHED_OTHER,
SCHED_BATCH, SCHED_IDLE, SCHED_FIFO and SCHED_RR.  If SCHED_OTHER or
SCHED_BATCH is selected, the Windows priority is set according to the
nice value where SCHED_BATCH sets a one step lower priority.  If
SCHED_IDLE is selected, the nice value is preserved and the Windows
priority is set to IDLE_PRIORITY_CLASS.  If SCHED_FIFO or SCHED_RR is
selected, the nice value is preserved and the Windows priority is set
according to the realtime priority.  If the SCHED_RESET_ON_FORK flag is
set, SCHED_FIFO and SCHED_RR are reset to SCHED_OTHER and negative nice
values are reset to zero in each child process created with fork(2).
Note: Windows does not offer alternative scheduling policies so this
could only emulate API behavior.
</p></li><li class="listitem" style="list-style-type: disc"><p>
If SCHED_FIFO or SCHED_RR is selected, the /proc/&lt;PID&gt;/stat field '(18)
priority' is now set to the negated sched_policy minus one.  If
SCHED_IDLE is selected, this field is set to 39.  The '(19) nice' field
is now set to the originally requested nice value.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Raise maximum pid from 65536 to 4194304 to account for scenarios with
lots of CPUs and lots of tasks.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow mmap with MAP_FIXED to succeed on an address range contained in
the chunk of an existing anonymous mapping, provided the
MAP_SHARED/MAP_PRIVATE flags agree and MAP_NORESERVE is not set for
either mapping.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Fix a long-standing hang issue when running on ARM64 under emulation.
This was due to a thread being terminated while the emulation was
holding an internal lock.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add a host machine tag to uname(2)'s sysname field.  This echoes what
used to be done with -WOW64 (when that was supported), but now with
-ARM64 when running on an ARM64 host under emulation.  The Cygwin DLL's
own architecture continues to be reported in the machine field.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Escape special characters in /proc/&lt;PID&gt;/mount*.  This allows the
contents to be parsed consistently, and matches what is done on Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Expose all Windows volume mount points via getmntent(3).  This also
exposes them via /proc/&gt;PID&gt;/mount*.  A change in behavior from
previous Cygwin versions is that volumes whose root is mounted
explicitly in Cygwin will now also show up as mounted under the cygdrive
prefix, whereas before that entry would have been suppressed.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new3.5"></a>What's new and what changed in 3.5</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Drop support for Windows 7, Windows 8, Server 2008 R2 and Server 2012.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Console devices (/dev/consN) are now accessible by processes attached to other
consoles or ptys. Thanks to this new feature, GNU screen and tmux now work in
the console.
</p></li><li class="listitem" style="list-style-type: disc"><p>
newgrp(1) tool.
</p></li><li class="listitem" style="list-style-type: disc"><p>
cygcheck has new options searching for available packages in the
cygwin distro, as well as getting extended info on available and
installed packages.
</p></li><li class="listitem" style="list-style-type: disc"><p>
fnmatch(3) and glob(3) now support named character classes, equivalence
class expressions, and collating symbols in the search pattern, i.e.,
[:alnum:], [=a=], [.aa.].
</p></li><li class="listitem" style="list-style-type: disc"><p>
Introduce /dev/disk directory with various by-* subdirectories which
provide symlinks to disk and partition raw devices:
  </p><pre class="screen">
  by-drive/DRIVE_LETTER -&gt;  ../../sdXN
  by-label/VOLUME_LABEL -&gt;  ../../sdXN
  by-id/BUSTYPE-[VENDOR_]PRODUCT_[SERIAL|0xHASH][-partN] -&gt; ../../sdX[N]
  by-partuuid/MBR_SERIAL-OFFSET -&gt; ../../sdXN
  by-partuuid/GPT_GUID -&gt; ../../sdXN
  by-uuid/VOLUME_SERIAL -&gt; ../../sdXN
  by-voluuid/MBR_SERIAL-OFFSET -&gt; ../../sdXN
  by-voluuid/VOLUME_GUID -&gt; ../../sdXN
  </pre><p>
The subdirectories by-drive and by-voluuid are Cygwin specific.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Introduce /proc/codesets and /proc/locales with information on supported
codesets and locales for all interested parties.  Locale(1) opens these
files and uses the info for printing locale info like any other process
could do.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add support for GB18030 codeset.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add support for lseek flags SEEK_DATA and SEEK_HOLE, a GNU extension.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API calls: posix_spawn_file_actions_addchdir_np,
posix_spawn_file_actions_addfchdir_np.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API calls: c8rtomb, c16rtomb, c32rtomb, mbrtoc8, mbrtoc16, mbrtoc32.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API call: close_range (available on FreeBSD and Linux).
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API call: fallocate (Linux-specific).
</p></li><li class="listitem" style="list-style-type: disc"><p>
posix_spawnp no longer falls back to starting the shell for unrecognized
files as execvp.  For the reasoning, see
https://www.austingroupbugs.net/view.php?id=1674
</p></li><li class="listitem" style="list-style-type: disc"><p>
FIFOs now also work on NFS filesystems.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Enable automatic sparsifying of files on SSDs, independent of the
"sparse" mount mode.
</p></li><li class="listitem" style="list-style-type: disc"><p>
When RLIMIT_CORE is more than 1MB, a core dump file which can be loaded by gdb
is now written on a fatal error. Otherwise, if it's greater than zero, a text
format .stackdump file is written, as previously.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The default RLIMIT_CORE is now 0, disabling the generation of core dump or
stackdump files. Use e.g. <code class="code">ulimit -c unlimited</code> or <code class="code">ulimit -c
1024</code> to enable them again.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new3.4"></a>What's new and what changed in 3.4</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Drop support for Vista and Server 2008.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Drop support for 32 bit Windows and WOW64.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow to run with full ASLR enabled and enable on Cygwin DLL by default.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Remove any special handling for the .com filename suffix. It has to
be used always explicitely.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add code to handle setrlimit(RLIMIT_AS).
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add code to handle signal masks in /proc/&lt;PID&gt;/status.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The CYGWIN=pipe_byte option is now set by default, so that pipes are
opened in byte mode rather than message mode.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Handle UDP_SEGMENT and UDP_GRO socket options.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The stdio input functions no longer try again to read after EOF.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The default values of FD_SETSIZE and NOFILE are now 1024 and 3200,
respectively.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new3.3"></a>What's new and what changed in 3.3</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
An IP-sampling profiler named 'profiler' has been added.  It can be used
to profile any Cygwin program along with any DLLs loaded.
</p></li><li class="listitem" style="list-style-type: disc"><p>
A new tool 'gmondump' has been added.  It can dump the raw information
of any "gmon.out" file created by profiler, ssp, or use of the gcc/g++
option '-pg'.  (Continue using gprof to get symbolic profile displays.)
</p></li><li class="listitem" style="list-style-type: disc"><p>
New GNU-specific APIs, slated to become part of the next POSIX standard:
pthread_cond_clockwait, pthread_mutex_clocklock, pthread_rwlock_clockrdlock,
pthread_rwlock_clockwrlock, sem_clockwait.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New Solaris-specific APIs, slated to become part of the next POSIX standard:
sig2str, str2sig.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The speed argument to cfsetspeed(3) can now be a numerical baud rate
rather than a Bnnn constant, as on Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The internal implementation of pipes has been overhauled; this should
result in improved performance.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new3.2"></a>What's new and what changed in 3.2</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Revamped pseudo console support.  Conditionally activating it only when
a non-cygwin application is run.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New C11 threads API: call_once, cnd_broadcast, cnd_destroy, cnd_init,
cnd_signal, cnd_timedwait, cnd_wait, mtx_destroy, mtx_init, mtx_lock,
mtx_timedlock, mtx_trylock, mtx_unlock, thrd_create, thrd_current,
thrd_detach, thrd_equal, thrd_exit, thrd_join, thrd_sleep, thrd_yield,
tss_create, tss_delete, tss_get, tss_set.
</p></li><li class="listitem" style="list-style-type: disc"><p>
In cygwin console, new thread which handles special keys/signals such
as Ctrl-Z (VSUSP), Ctrl-\ (VQUIT), Ctrl-S (VSTOP), Ctrl-Q (VSTART) and
SIGWINCH has been introduced. There have been a long standing issue
that these keys/signals are handled only when app calls read() or
select(). Now, these work even if app does not call read() or select().
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow ~5000 child processes per process on 64 bit, ~1200 child processes
per process on 32 bit.  So far, only 256 child processes per process were
supported.
</p></li><li class="listitem" style="list-style-type: disc"><p>
A few FAQ updates.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Have tmpfile(3) make use of Win32 FILE_ATTRIBUTE_TEMPORARY via open(2)
flag O_TMPFILE.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Utilize Windows 10 1809 FILE_DISPOSITION_IGNORE_READONLY_ATTRIBUTE flag
to allow simpler unlink of files with DOS readonly flags set.
</p></li><li class="listitem" style="list-style-type: disc"><p>
fchmodat(2) now has limited support for the AT_SYMLINK_NOFOLLOW flag.
</p></li><li class="listitem" style="list-style-type: disc"><p>
getdtablesize(3), sysconf(_SC_OPEN_MAX), and getrlimit(RLIMIT_NOFILE)
now return the true limit on the number of open descriptors, 3200.
Previously they returned the current size of Cygwin's internal file
descriptor table, which can grow dynamically.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Cygwin now recognizes native Windows AF_UNIX sockets (as regular
files, not as socket files).  This allows tools like 'ls' and 'rm' to
work.
</p></li><li class="listitem" style="list-style-type: disc"><p>
facl(2) now fails with EBADF on a file opened with O_PATH.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow to start Windows Store executables via their "app execution
aliases".  Handle these aliases (which are special reparse points)
as symlinks to the actual executables.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The speed argument to cfsetspeed(3) can now be a numerical baud rate
rather than a Bnnn constant, as on Linux.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new3.1"></a>What's new and what changed in 3.1</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
FIFOs can now be opened multiple times for writing.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add 24 bit color support using xterm compatibility mode in Windows 10
1703 or later.  Add fake 24 bit color support for legacy console, which
uses the nearest color from 16 system colors.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support pseudo console in PTY. Pseudo console is a new feature
in Windows 10 1809, which provides console APIs on virtual
terminal. With this patch, native console applications can work
in PTYs such as mintty, ssh, gnu screen or tmux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Eliminate a header file name collision with &lt;X11/XLocale.h&gt; on
case insensitive filesystems by reverting &lt;xlocale.h&gt; back to
&lt;sys/_locale.h&gt;.
</p></li><li class="listitem" style="list-style-type: disc"><p>
If a SA_SIGINFO signal handler changes the ucontext_t pointed to by the
third parameter, follow it after returning from the handler.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: sched_getaffinity, sched_setaffinity, pthread_getaffinity_np,
pthread_setaffinity_np, plus CPU_SET macros.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: dbm_clearerr, dbm_close, dbm_delete, dbm_dirfno, dbm_error,
dbm_fetch, dbm_firstkey, dbm_nextkey, dbm_open, dbm_store.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow times(2) to have a NULL argument, as on Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Improve /proc/cpuinfo output and align more closely with Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow symlinks to be opened with O_PATH | O_NOFOLLOW.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow the pathname argument to readlinkat(2) to be an empty string,
provided the dirfd argument refers to a symlink opened with O_PATH |
O_NOFOLLOW.  The readlinkat call then operates on that symlink.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support the Linux-specific AT_EMPTY_PATH flag for fchownat(2) and
fstatat(2).
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow AF_LOCAL sockets to be opened with O_PATH.  If that flag is not
set, or if an attempt is made to open a different type of socket, the
errno is now EOPNOTSUPP instead of ENXIO.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The new locale modifier @cjksingle allows enforcing of single-width
character property for usually double-widthed characters.  This will
be supported by upcoming mintty releases.  For the reasoning, see
https://gitlab.freedesktop.org/terminal-wg/specifications/issues/9.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support WSL symlinks.  Create those by default now.
</p></li><li class="listitem" style="list-style-type: disc"><p>
FIFOs can now be opened multiple times for reading.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support more IPPROTO_TCP socket options: TCP_FASTOPEN, TCP_KEEPIDLE,
TCP_KEEPCNT, TCP_KEEPINTVL, TCP_QUICKACK, TCP_USER_TIMEOUT.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new3.0"></a>What's new and what changed in 3.0</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Updated era and message locale data, in particular add era data for
the new Tenn&#333;.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support for CLOCK_REALTIME_COARSE, CLOCK_MONOTONIC_COARSE,
CLOCK_MONOTONIC_RAW, CLOCK_BOOTTIME, CLOCK_REALTIME_ALARM,
CLOCK_BOOTTIME_ALARM clocks.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support for case sensitive directories via chattr(1).  This feature requires
Windows 10 1803 or later and WSL installed.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New file ioctls's FS_IOC_GETFLAGS and FS_IOC_SETFLAGS.  The actual inode
flags are Cygwin-specific.  This allows to set or reset DOS attributes,
file sparseness, FS level encryption and compression, as well as
directory case sensitivity programatically.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New tools chattr(1) and lsattr(1) to utilize setting and viewing the
aforementioned new ioctl's on the command line.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support for exFAT.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support Linux-specific open(2) flag O_PATH.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support Linux-specific linkat(2) flag AT_EMPTY_PATH.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support overrun counter for posix timers (via timer_getoverrun() or
siginfo_t::si_overrun).
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: secure_getenv, signalfd, timerfd_create, timerfd_gettime,
timerfd_settime, timer_getoverrun.
</p></li><li class="listitem" style="list-style-type: disc"><p>
clock_nanosleep, pthread_condattr_setclock and timer_create now support
all clocks, except CLOCK_PROCESS_CPUTIME_ID and CLOCK_THREAD_CPUTIME_ID.
</p></li><li class="listitem" style="list-style-type: disc"><p>
clock_setres is a no-op now.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Use the new POSIX unlink semantics on NTFS starting with Windows 10
1709.  Deleting an in-use file now actually removes the file, rather
than moving it to the recycler bin.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Use the new POSIX rename semantics on NTFS starting with Windows 10
1809.  Renaming a file to another in-use file now actually removes the
other file, rather than moving it to the recycler bin.
</p></li><li class="listitem" style="list-style-type: disc"><p>
open(..., O_TMPFILE) now moves the file to the trash bin immediately,
to free the parent directory.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Wctype functions updated to Unicode 11.0.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Remove matherr, and SVID and X/Open math library configurations.
Default math library configuration is now IEEE.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Improve uname(2) for newly built applications.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Kerberos/MSV1_0 S4U authentication replaces two old methods:
Creating a token from scratch and Cygwin LSA authentication package.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Cygwin PIDs have been decoupled from Windows PID.  Cygwin PIDs are now
incrementally dealt in the range from 2 up to 65535, POSIX-like.
Native Windows processes not started by Cygwin processes are mapped
into the range beyond 65535.
</p></li><li class="listitem" style="list-style-type: disc"><p>
fork(2) now is able to recover from when an in-use executable/dll is
removed or replaced during process runtime.  This feature is disabled by
default and limited to exes/dlls on the same NTFS partition as the Cygwin
installation.  For more information and how to enable, please refer to
<a class="xref" href="highlights.html#ov-hi-process" title="Process Creation">the section called &#8220;Process Creation&#8221;</a>.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Improve /proc/&lt;PID&gt;/cmdline and /proc/&lt;PID&gt;/stat handling to allow
all processes access to basic process information of foreign processes.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.11"></a>What's new and what changed in 2.11</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
SO_RCVTIMEO and SO_SNDTIMEO socket options are now honored.
</p></li><li class="listitem" style="list-style-type: disc"><p>
/proc/cpuinfo now reports L3 cache size on Intel CPUs.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: clearenv, pthread_tryjoin_np, pthread_timedjoin_np, sched_getcpu.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs for POSIX Asynchronous I/O: aio_cancel, aio_error, aio_fsync,
aio_read, aio_return, aio_suspend, aio_write, lio_listio.
New Header: &lt;aio.h&gt;.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Drop denormal-operand exception from FE_ALL_EXCEPT, as on Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Rename the --file option of setfacl(1) to --set-file, as on Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support Unicode 10.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Following glibc, `union wait' has now been removed.  Applications
using union wait and these macros will have to migrate to the
POSIX-specified int status type.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.10"></a>What's new and what changed in 2.10</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New open(2) flags O_TMPFILE and O_NOATIME.
</p></li><li class="listitem" style="list-style-type: disc"><p>
scanf/wscanf now handle the POSIX %m modifier.
</p></li><li class="listitem" style="list-style-type: disc"><p>
scanf now handles the %l[ conversion.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Improved hostprogs compatibility for cross-compiling the Linux kernel.
New headers: &lt;asm/bitsperlong.h&gt;, &lt;asm/posix_types.h&gt;.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Built-in implementation of Stack Smashing Protection compiler feature.
New APIs: __stack_chk_fail, __stack_chk_guard.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Built-in implementation of _FORTIFY_SOURCE guards for functions in
&lt;stdio.h&gt;, &lt;stdlib.h&gt;, &lt;string.h&gt;, &lt;strings.h&gt;,
&lt;unistd.h&gt;, &lt;wchar.h&gt;, &lt;sys/poll.h&gt;, and &lt;sys/socket.h&gt;.
New APIs:  __chk_fail, __gets_chk, __memcpy_chk, __memmove_chk, __mempcpy_chk,
__memset_chk, __snprintf_chk, __sprintf_chk, __stpcpy_chk, __stpncpy_chk,
__strcat_chk, __strcpy_chk, __strncat_chk, __strncpy_chk, __vsnprintf_chk,
__vsprintf_chk.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Built-in implementation of POSIX.1-2001 message catalog support.
New APIs: catclose, catgets, catopen.  New tool: gencat.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: sigtimedwait, wmempcpy.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.9"></a>What's new and what changed in 2.9</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New APIs: explicit_bzero, strnstr.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: pthread_mutex_timedwait, pthread_rwlock_timedrdlock,
pthread_rwlock_timedwrlock.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: renameat2.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Improved implementation of &lt;elf.h&gt;.
</p></li><li class="listitem" style="list-style-type: disc"><p>
strptime(3) supports %s (seconds since Epoch) now, analogue to strftime.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.8"></a>What's new and what changed in 2.8</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New API: timingsafe_bcmp, timingsafe_memcmp
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: dladdr
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: getloadavg
</p></li><li class="listitem" style="list-style-type: disc"><p>
Cygcheck and strace now always generate output with Unix LF line endings,
rather than with DOS/Windows CR LF line endings.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Fork now preserves the load order of unrelated dlopen'd modules.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Pthread_cond_wait now acts like Linux and BSD: Resume waiting for the
condition variable as if it was not interrupted, rather than returning 0.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The internal &lt;sys/_locale.h&gt; header was renamed to &lt;xlocale.h&gt; for
source compatibility with other systems.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Try harder supporting Netapp drives.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.7"></a>What's new and what changed in 2.7</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Support for /proc/&lt;PID&gt;/environ.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: getentropy, getrandom.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.6"></a>What's new and what changed in 2.6</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Support for POSIX-1.2008 locale objects and per-thread locales.
</p><p>
New API per POSIX-1.2008: newlocale, freelocale, duplocale, uselocale,
nl_langinfo_l, isalnum_l, isalpha_l, isblank_l, iscntrl_l, isdigit_l,
isgraph_l, islower_l, isprint_l, ispunct_l, isspace_l, isupper_l, iswalnum_l,
iswalpha_l, iswblank_l, iswcntrl_l, iswctype_l, iswdigit_l, iswgraph_l,
iswlower_l, iswprint_l, iswpunct_l, iswspace_l, iswupper_l, iswxdigit_l,
isxdigit_l, tolower_l, toupper_l, towctrans_l, towlower_l, towupper_l,
wctrans_l, wctype_l, strcasecmp_l, strcoll_l, strerror_l, strfmon_l,
strftime_l, strncasecmp_l, strxfrm_l, wcscasecmp_l, wcscoll_l, wcstrncasecmp_l,
wcstrxfrm_l.
</p><p>
New API, GNU extensions: isascii_l, toascii_l, strptime_l, strtod_l, strtof_l,
strtol_l, strtold_l, strtoll_l, strtoul_l, strtoull_l, wcsftime_l, wcstod_l,
wcstof_l, wcstol_l, wcstold_l, wcstoll_l, wcstoul_l, wcstoull_l.
</p></li><li class="listitem" style="list-style-type: disc"><p>
locale(1) now supports a -i/--input option to fetch the current input locale
(this is basically equivalent to the current keyboard layout setting).
</p></li><li class="listitem" style="list-style-type: disc"><p>
Drop support for Windows XP and Windows Server 2003/2003 R2.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Drop support for very old SUNWNFS filesystem.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Raise number of supported partitions per disk (for raw access) to 63.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Further header file improvements in terms of feature test macros.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add a workaround for filesystems not supporting the FileAllInformation
info class.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support AzureAD accounts.
</p></li><li class="listitem" style="list-style-type: disc"><p>
"nobody" account support for WinFSP.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: pthread_getname_np, pthread_setname_np.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.5"></a>What's new and what changed in 2.5</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Full set of POSIX.1e ACL API functions now implemented.
New APIs: acl_add_perm, acl_calc_mask, acl_clear_perms, acl_copy_entry,
acl_copy_ext, acl_copy_int, acl_create_entry, acl_delete_def_file,
acl_delete_entry, acl_delete_perm, acl_dup, acl_free, acl_from_text,
acl_get_entry, acl_get_fd, acl_get_file, acl_get_permset, acl_get_qualifier,
acl_get_tag_type, acl_init, acl_set_fd, acl_set_file, acl_set_permset,
acl_set_qualifier, acl_set_tag_type, acl_size, acl_to_text, acl_valid.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Most libacl extensions now implemented, too:
New APIs: acl_check, acl_cmp, acl_entries, acl_equiv_mode, acl_error,
acl_extended_fd, acl_extended_file, acl_extended_file_nofollow,
acl_from_mode, acl_get_perm, acl_to_any_text.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Including &lt;sys/acl.h&gt; now *only* includes the POSIX ACL API.  To include
the old Solaris API, include &lt;cygwin/acl.h&gt;.
</p></li><li class="listitem" style="list-style-type: disc"><p>
First implementation of pthread_barrier/pthread_barrierattr functions.
New APIs: pthread_barrierattr_init, pthread_barrierattr_setpshared,
pthread_barrierattr_getpshared, pthread_barrierattr_destroy,
pthread_barrier_init, pthread_barrier_destroy, pthread_barrier_wait.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Enabled console reports requested by escape sequences: Requesting primary
and secondary device attributes, requesting cursor position report;
see https://cygwin.com/ml/cygwin-patches/2012-q3/msg00019.html
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: clog10, clog10f, nexttoward, nexttowardf, nexttowardl.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add missing long double functions.  New APIs:
acoshl, acosl, asinhl, asinl, atan2l, atanhl, atanl, cacoshl, cacosl, cargl,
casinhl, casinl, catanhl, catanl, ccoshl, ccosl, ceill, cexpl, clog10l,
clogl, conjl, copysignl, coshl, cosl, cpowl, cprojl, csinhl, csinl, csqrtl,
ctanhl, ctanl, dreml, erfcl, erfl, exp10l, exp2l, expl, expm1l, fabsl,
fdiml, floorl, fmal, fmaxl, fminl, fmodl, frexpl, ilogbl, isinfl, isnanl,
ldexpl, lgammal, lgammal_r, llroundl, log10l, log1pl, log2l, logbl, logl,
lroundl, modfl, nearbyintl, nextafterl, pow10l, powl, remainderl, remquol,
roundl, scalbl, scalblnl, scalbnl, sincosl, sinhl, sinl, tanhl, tanl,
tgammal, truncl.
</p></li><li class="listitem" style="list-style-type: disc"><p>
In calls to chmod treat ACLs with extra ACEs *only* for Admins and
SYSTEM like a trivial ACL.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Bump POSIX option macros to POSIX.1-2008.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Profiling data, specifically pc sampling, now covers all threads of a
program and not just the main thread.  Environment variable GMON_OUT_PREFIX
enables multiple gmon.out files to preserve profiling data after fork or
from multiple program runs.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Feature test macros, which control which symbols are exposed in the standard
headers, have been completely overhauled for compatibility with glibc.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The isinf, isinff, and isinfl functions are signed, returning -1 for
negative infinity for compatibility with glibc.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.4"></a>What's new and what changed in 2.4</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New, unified implementation of POSIX permission and ACL handling.  The
new ACLs now store the POSIX ACL MASK/CLASS_OBJ permission mask, and
they allow to inherit the S_ISGID bit.  ACL inheritance now really
works as desired, in a limited, but theoretically equivalent fashion
even for non-Cygwin processes.</p><p>To accommodate standard Windows ACLs, the POSIX permissions of
the owner and all other users in the ACL are computed using the Windows
AuthZ API.  This may slow down the computation of POSIX permissions
noticably in some circumstances, but is generally more correct.
The new code also ignores SYSTEM and Administrators group permissions
when computing the MASK/CLASS_OBJ permission mask on old ACLs, and it
doesn't deny access to SYSTEM and Administrators group based on the
value of MASK/CLASS_OBJ when creating the new ACLs.</p><p>The new code now handles the S_ISGID bit on directories as on Linux:
Setting S_ISGID on a directory causes new files and subdirs created
within to inherit its group, rather than the primary group of the user
who created the file.  This only works for files and directories
created by Cygwin processes.
</p></li><li class="listitem" style="list-style-type: disc"><p>
cygpath has a new -U option, which creates cygdrive paths using the
unambiguous /proc/cygdrive prefix.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: rpmatch.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Align setfacl(1) usage a bit closer to the usage on Linux.  Rename -d option
to -x, --substitute to --set.  Add --no-mask and --mask options.  Allow to
use the -b and -k option combined to allow reducing an ACL to only reflect
standard POSIX permissions.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Fix (numeric and monetary) decimal point and thousands separator in
fa_IR and ps_AF locales to be aligned with Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
utmpname/utmpxname are now defined as int functions as on Linux.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.3"></a>What's new and what changed in 2.3</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
strftime(3) supports %s (seconds since Epoch) now.
</p></li><li class="listitem" style="list-style-type: disc"><p>
posix_madvise(POSIX_MADV_WILLNEED) now utilizes OS functionality available
starting with Windows 8/Server 2012.
</p><p>
posix_madvise(POSIX_MADV_DONTNEED) now utilizes OS functionality available
starting with Windows 8.1/Server 2012R2.
</p></li><li class="listitem" style="list-style-type: disc"><p>
sysconf() now supports returning CPU cache information:
  </p><pre class="screen">
  _SC_LEVEL1_ICACHE_SIZE, _SC_LEVEL1_ICACHE_ASSOC, _SC_LEVEL1_ICACHE_LINESIZE,
  _SC_LEVEL1_DCACHE_SIZE, _SC_LEVEL1_DCACHE_ASSOC, _SC_LEVEL1_DCACHE_LINESIZE,
  _SC_LEVEL2_CACHE_SIZE, _SC_LEVEL2_CACHE_ASSOC, _SC_LEVEL2_CACHE_LINESIZE,
  _SC_LEVEL3_CACHE_SIZE, _SC_LEVEL3_CACHE_ASSOC, _SC_LEVEL3_CACHE_LINESIZE,
  _SC_LEVEL4_CACHE_SIZE, _SC_LEVEL4_CACHE_ASSOC, _SC_LEVEL4_CACHE_LINESIZE
  </pre><p>
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: aligned_alloc, at_quick_exit, quick_exit.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add support for Parallels Desktop FS (prlfs).
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.2"></a>What's new and what changed in 2.2</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New APIs: getcontext, setcontext, makecontext, swapcontext.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New functions: sigsetjmp, siglongjmp.
</p><p>
These were only available as macros up to now, but POSIX requires that
siglongjmp has to be available as function.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.1"></a>What's new and what changed in 2.1</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Handle pthread stacksizes as in GLibc:  Default to RLIMIT_STACK resource.
Allow to set RLIMIT_STACK via setrlimit.  Default RLIMIT_STACK to value
from executable header as described on the MSDN website
<a class="ulink" href="https://msdn.microsoft.com/en-us/library/windows/desktop/ms686774.aspx" target="_top">Thread Stack Size</a>
Default stacksize to 2 Megs in case RLIMIT_STACK is set to RLIM_INFINITY.
</p></li><li class="listitem" style="list-style-type: disc"><p>
First cut of an implementation to allow signal handlers running on an
alternate signal stack. 
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API sigaltstack, plus definitions for SA_ONSTACK, SS_ONSTACK,
SS_DISABLE, MINSIGSTKSZ, SIGSTKSZ.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: sethostname.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new2.0"></a>What's new and what changed in 2.0</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
basename(3) now comes in two flavors, POSIX and GNU.  The POSIX version is
the default.  You get the GNU version after

</p><pre class="screen">
  #define _GNU_SOURCE
  #include &lt;string.h&gt;
</pre><p>
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.35"></a>What's new and what changed in 1.7.35</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Performance improvements of the new account DB handling.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Since 1.7.34, chmod does not always affect the POSIX permission mask as
returned by stat(2) or printed by ls(1), due to the improved POSIX ACL
handling.  However, that's still far from perfect, so, as a temporary
workaround, chmod now checks if secondary groups and users in the ACL
have more permissions than the primary group.  If so, the permissions of
the secondary users and groups will be reduced according to the mask
given by the new primary group permissions.  I.e, chmod 600 will remove
all permissions from the primary group as well as all secondary user and
group entries in the ACL.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Change handling of group permissions if owner SID == group SID.  Now the
group permissions don't mirror the user permissions anymore, thus
leading to less hassle with security-conscious applications.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow group SID to be the same as owner SID for "Microsoft Accounts".
Those have the group in their user token set to the owner SID by
default.  Drop the workaround to change their primary group to "Users".
It's not required anymore due to the aforementioned changes.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Change getfacl long options from --all to --access and from --dir to
--default, along the lines of the Linux getfacl tool.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Don't raise a SIGSYS signal in the XSI IPC functions if cygserver is not
running.  Just return -1 with errno set to ENOSYS.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: cabsl, cimagl, creall, finitel, hypotl, sqrtl.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: issetugid.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.34"></a>What's new and what changed in 1.7.34</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Cygwin can now generate passwd/group entries directly from Windows user
databases (local SAM or Active Directory), thus allowing to run Cygwin
without having to create /etc/passwd and /etc/group files.  Introduce
/etc/nsswitch.conf file to configure passwd/group handling.
</p><p>
For bordercase which require to use /etc/passwd and /etc/group files,
change mkpasswd/mkgroup to generate passwd/group entries compatible with
the entries read from SAM/AD.
</p><p>For a description of this exciting new feature see
<a class="xref" href="ntsec.html" title="POSIX accounts, permission, and security">the section called &#8220;POSIX accounts, permission, and security&#8221;</a>.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add -b/--remove-all option to setfacl to reduce the ACL to only the entries
representing POSIX permission bits.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add -k/--remove-default option to setfacl to remove all default ACL entries
from an ACL.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add restore action to regtool.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Make gethostbyname2 handle numeric host addresses as well as the
reserved domain names "localhost" and "invalid" per RFC 6761.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Revamp Solaris ACL implementation to more closely work like POSIX ACLs
are supposed to work.  Finally implement a CLASS_OBJ emulation.  Update
getfacl(1)/setfacl(1) accordingly.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The xdr functions are no longer exported for newly built executables.
Use libtirpc-devel instead.
</p></li><li class="listitem" style="list-style-type: disc"><p>
32 bit only: Change default values for socket buffer size to raise
performance on 10Gb networks.
</p></li><li class="listitem" style="list-style-type: disc"><p>
When spawning a process under another user account, merge the user's
default Windows environment into the new process' environment.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: qsort_r, __bsd_qsort_r.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: wcstold.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: __fbufsize, __flbf, __fpending, __freadable, __freading,
__fsetlocking, __fwritable, __fwriting.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: clearerr_unlocked, feof_unlocked, ferror_unlocked, fflush_unlocked,
fgetc_unlocked, fgets_unlocked, fgetwc_unlocked, fgetws_unlocked,
fileno_unlocked, fputc_unlocked, fputs_unlocked, fputwc_unlocked,
fputws_unlocked, fread_unlocked, fwrite_unlocked, getwc_unlocked,
getwchar_unlocked, putwc_unlocked, putwchar_unlocked.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: sockatmark.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.33"></a>What's new and what changed in 1.7.33</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
/proc/cygdrive is a new symlink pointing to the current cygdrive prefix.
This can be utilized in scripts to access paths via cygdrive prefix,
even if the cygdrive prefix has been changed by the user.
</p></li><li class="listitem" style="list-style-type: disc"><p>
/proc/partitions now prints the windows mount points the device is
mounted on.  This allows to recognize the underlying Windows devices of
the Cygwin raw device names.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: quotactl, designed after the Linux/BSD function, but severely
restricted:  Windows only supports user block quotas on NTFS, no group
quotas, no inode quotas, no time constraints.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: ffsl, ffsll (glibc extensions).
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: stime (SVr4).
</p></li><li class="listitem" style="list-style-type: disc"><p>
Provide Cygwin documentation (PDFs and HTML) for offline usage in
<code class="filename">/usr/share/doc/cygwin-${version}</code>.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New internal exception handling based on SEH on 64 bit Cygwin.
</p></li><li class="listitem" style="list-style-type: disc"><p>
When exec'ing applications, check if $PATH exists and is non-empty.  If
not, add PATH variable with Cygwin installation directory as content to
Windows environment to allow loading of Cygwin system DLLs.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Disable CYGWIN "dosfilewarning" option by default.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Improve various header files for C++- and standards-compliance.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Doug Lea malloc implementation update from 2.8.3 to the latest 2.8.6.
</p></li><li class="listitem" style="list-style-type: disc"><p>
atexit(3) is now exported as statically linked function from libcygwin.a.
This allows reliable access to the DSO handle of the caller for newly
built executables.  The former atexit entry point into the DLL remains
for backward compatibility only.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.32"></a>What's new and what changed in 1.7.32</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Export __cxa_atexit and __cxa_finalize to allow C++ standards-compliant
destructor handling in libstdc++ and g++ compiled code.

Please note that, in order to benefit from this new feature, C++ code
must be recompiled with the upcoming gcc 4.8.3-3 release which will
enable the -fuse-cxa-atexit flag by default, and that C++ applications
using this feature will not run on older Cygwin releases.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support more recent CPU flags in /proc/cpuinfo.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.31"></a>What's new and what changed in 1.7.31</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Improve performance of send(2), sendto(2), sendmsg(2) when using small
input buffers.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The default pthread_mutex type is now PTHREAD_MUTEX_NORMAL, rather than
PTHREAD_MUTEX_ERRORCHECK, just as on Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Align pthread_attr stack functions more closely to Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Mark pthread_attr_getstackaddr and pthread_attr_setstackaddr as deprecated,
as on Linux.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.29"></a>What's new and what changed in 1.7.29</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Allow quoting of arguments to the CYGWIN environment variable, i.e.,
set CYGWIN=error_start="c:\bin\someprogram -T"
</p></li><li class="listitem" style="list-style-type: disc"><p>
Console screen clearing works more like xterm or mintty.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.28"></a>What's new and what changed in 1.7.28</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
popen now supports the Glibc 'e' flag to set the FD_CLOEXEC flag on the pipe
in a thread-safe way.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New netinet/ip6.h header.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Switch to BSD FILE stream fopen/exit semantics, as in all BSD variants
and Linux/GLibc:  Don't fflush/lseek a FILE stream on fclose and exit,
if it only has been read from.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.27"></a>What's new and what changed in 1.7.27</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Don't create native symlinks with target paths having long path prefixes
"\\?\" if the target path is shorter than MAX_PATH characters.  This works
around a Windows 8.1 bug:  The ShellExecuteW fails if the lpFile parameter
points to a native NTFS symlink with a target path prefixed with "\\?\".
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.26"></a>What's new and what changed in 1.7.26</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
getaddrinfo now supports glibc-specific International Domain Name (IDN)
extension flags: AI_IDN, AI_CANONIDN, AI_IDN_ALLOW_UNASSIGNED,
AI_IDN_USE_STD3_ASCII_RULES.
</p></li><li class="listitem" style="list-style-type: disc"><p>
getnameinfo now supports glibc-specific International Domain Name (IDN)
extension flags: NI_IDN, NI_IDN_ALLOW_UNASSIGNED, NI_IDN_USE_STD3_ASCII_RULES.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Slightly improve randomness of /dev/random emulation.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow to use advisory locking on any device.  POSIX fcntl and lockf locking
works with any device, BSD flock locking only with devices backed by an OS
handle.  Right now this excludes console windows on pre Windows 8, as well as
almost all virtual files under /proc from BSD flock locking.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The header /usr/include/exceptions.h, containing implementation details for
32 bit Windows' exception handling only, has been removed.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Preliminary, experimental support of the posix_spawn family of functions.
New associated header /usr/include/spawn.h.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.25"></a>What's new and what changed in 1.7.25</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Change magic number associated with process information block so that 32-bit
Cygwin processes don't try to interpret 64-bit information and vice-versa.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Redefine content of mtget tape info struct to allow fetching the number of
partitions on a tape.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.24"></a>What's new and what changed in 1.7.24</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Allow application override of posix_memalign.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.23"></a>What's new and what changed in 1.7.23</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Added CYGWIN environment variable keyword "wincmdln" which causes Cygwin to
send the full windows command line to any subprocesses.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.22"></a>What's new and what changed in 1.7.22</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Support for /dev/mem, /dev/kmem and /dev/port removed, since OS support
was limited to 32 bit Windows XP only.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Added cygwin GetCommandLine wrappers which will allow Cygwin programs to
(appear to) use the Windows command line functions.
</p></li><li class="listitem" style="list-style-type: disc"><p>
regcomp(3) now allows character values &gt;= 0x80 if the current codeset is
ASCII (default codeset in the "C"/"POSIX" locale).  This allows patterns
containing arbitrary byte values as GLibc's regcomp.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.21"></a>What's new and what changed in 1.7.21</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New API: rawmemchr.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.19"></a>What's new and what changed in 1.7.19</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Drop support for Windows 2000 and Windows XP pre-SP3.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add support for building a 64 bit version of Cygwin on x86_64 natively.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add support for creating native NTFS symlinks starting with Windows Vista
by setting the CYGWIN=winsymlinks:native or CYGWIN=winsymlinks:nativestrict
option.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add support for AFS filesystem.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Preliminary support for mandatory locking via fcntl/flock/lockf, using Windows
locking semantics.  New F_LCK_MANDATORY fcntl command.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: __b64_ntop, __b64_pton, arc4random, arc4random_addrandom,
arc4random_buf, arc4random_stir, arc4random_uniform.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.18"></a>What's new and what changed in 1.7.18</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>Added Windows console cursor appearance support.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
  Show/Hide Cursor mode (DECTCEM): "ESC[?25h" / "ESC[?25l"
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Set cursor style (DECSCUSR): "ESC[n q" (note the space before the q);
  where n is 0, 1, 2 for block cursor, 3, 4 for underline cursor (all
  disregarding blinking mode), or &gt; 4 to set the cursor height to a
  percentage of the cell height.
  </p></li></ul></div></li><li class="listitem" style="list-style-type: disc"><p>
For performance reasons, Cygwin does not try to create sparse files
automatically anymore, unless you use the new "sparse" mount option.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: cfsetspeed.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.17"></a>What's new and what changed in 1.7.17</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Support the "e" flag to fopen(3).  This is a Glibc extension which
allows to fopen the file with the O_CLOEXEC flag set.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support the "x" flag to fopen(3).  This is a Glibc/C11 extension which
allows to open the file with the O_EXCL flag set.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.16"></a>What's new and what changed in 1.7.16</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New API: getmntent_r, memrchr.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Recognize ReFS filesystem.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.15"></a>What's new and what changed in 1.7.15</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
CYGWIN=pipe_byte option now forces the opening of pipes in byte mode rather than message mode.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.14"></a>What's new and what changed in 1.7.14</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Add mouse reporting modes 1005, 1006 and 1015 to console window.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.13"></a>What's new and what changed in 1.7.13</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
mkpasswd and mkgroup now try to print an entry for the TrustedInstaller
account existing since Windows Vista/Server 2008.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Terminal typeahead when switching from canonical to non-canonical mode
is now properly flushed.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.12"></a>What's new and what changed in 1.7.12</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Cygwin now automatically populates the /dev directory with all existing
POSIX devices.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add virtual /proc/PID/mountinfo file.
</p></li><li class="listitem" style="list-style-type: disc"><p>
flock now additionally supports the following scenario, which requires
  to propagate locks to the parent process:
   </p><pre class="screen">
    (
      flock -n 9 || exit 1
      # ... commands executed under lock ...
    } 9&gt;/var/lock/mylockfile
   </pre><p>
  Only propagation to the direct parent process is supported so far,
  not to grand parents or sibling processes.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add a "detect_bloda" setting for the CYGWIN environment variable to help
finding potential BLODAs.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.11"></a>What's new and what changed in 1.7.11</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New <span class="command"><strong>pldd</strong></span> command for listing DLLs loaded by a process.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: scandirat.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Change the way remote shares mapped to drive letters are recognized when
creating the cygdrive directory.  If Windows claims the drive is
unavailable, don't show it in the cygdrive directory listing.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Raise default stacksize of pthreads from 512K to 1 Meg.  It can still be
changed using the pthread_attr_setstacksize call.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.10"></a>What's new and what changed in 1.7.10</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Drop support for Windows NT4.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The CYGWIN environment variable options "envcache", "strip_title", "title",
"tty", and "upcaseenv" have been removed.
</p></li><li class="listitem" style="list-style-type: disc"><p>
If the executable (and the system) is large address aware, the application heap
will be placed in the large memory area.  The <span class="command"><strong>peflags</strong></span> tool
from the <code class="literal">rebase</code> package can be used to set the large
address awareness flag in the executable file header.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The registry setting "heap_chunk_in_mb" has been removed, in favor of a new
per-executable setting in the executable file header which can be set using the
<span class="command"><strong>peflags</strong></span> tool.  See <a class="xref" href="setup-maxmem.html" title="Changing Cygwin's Maximum Memory">the section called &#8220;Changing Cygwin's Maximum Memory&#8221;</a>
for more information.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The CYGWIN=tty mode using pipes to communicate with the console in a pseudo
tty-like mode has been removed.  Either just use the normal Windows console
as is, or use a terminal application like <span class="command"><strong>mintty</strong></span>.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New <span class="command"><strong>getconf</strong></span> command for querying confstr(3), pathconf(3),
sysconf(3), and limits.h configuration.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New <span class="command"><strong>tzset</strong></span> utility to generate a POSIX-compatible TZ
environment variable from the Windows timezone settings.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The passwd command now allows an administrator to use the -R command for
other user accounts:  passwd -R username.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Pthread spinlocks.  New APIs: pthread_spin_destroy, pthread_spin_init,
pthread_spin_lock, pthread_spin_trylock, pthread_spin_unlock.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Pthread stack address management.  New APIs: pthread_attr_getstack,
pthread_attr_getstackaddr, pthread_attr_getguardsize, pthread_attr_setstack,
pthread_attr_setstackaddr, pthread_attr_setguardsize, pthread_getattr_np.
</p></li><li class="listitem" style="list-style-type: disc"><p>
POSIX Clock Selection option.  New APIs: clock_nanosleep,
pthread_condattr_getclock, pthread_condattr_setclock.
</p></li><li class="listitem" style="list-style-type: disc"><p>
clock_gettime(3) and clock_getres(3) accept per-process and per-thread CPU-time
clocks, including CLOCK_PROCESS_CPUTIME_ID and CLOCK_THREAD_CPUTIME_ID.
New APIs: clock_getcpuclockid, pthread_getcpuclockid.
</p></li><li class="listitem" style="list-style-type: disc"><p>
GNU/glibc error.h error reporting functions.  New APIs: error, error_at_line.
New exports: error_message_count, error_one_per_line, error_print_progname.
Also, perror and strerror_r no longer clobber strerror storage.
</p></li><li class="listitem" style="list-style-type: disc"><p>
C99 &lt;tgmath.h&gt; type-generic macros.
</p></li><li class="listitem" style="list-style-type: disc"><p>
/proc/loadavg now shows the number of currently running processes and the
total number of processes.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Added /proc/devices and /proc/misc, which lists supported device types and
their device numbers.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Added /proc/swaps, which shows the location and size of Windows paging file(s).
</p></li><li class="listitem" style="list-style-type: disc"><p>
Added /proc/sysvipc/msg, /proc/sysvipc/sem, and /proc/sysvipc/shm which
provide information about System V IPC message queues, semaphores, and
shared memory.
</p></li><li class="listitem" style="list-style-type: disc"><p>
/proc/version now shows the username of whomever compiled the Cygwin DLL
as well as the version of GCC used when compiling.
</p></li><li class="listitem" style="list-style-type: disc"><p>
dlopen now supports the Glibc-specific RTLD_NODELETE and RTLD_NOOPEN flags.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The printf(3) and wprintf(3) families of functions now handle the %m
conversion flag.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Other new API: clock_settime, __fpurge, getgrouplist, get_current_dir_name,
getpt, ppoll, psiginfo, psignal, ptsname_r, sys_siglist, pthread_setschedprio,
pthread_sigqueue, sysinfo.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.9"></a>What's new and what changed in 1.7.9</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New API: strchrnul.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.8"></a>What's new and what changed in 1.7.8</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Drop support for Windows NT4 prior to Service Pack 4.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Reinstantiate Cygwin's ability to delete an empty directory which is the
current working directory of the same or another process.  Same for any
other empty directory which has been opened by the same or another process.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Cygwin now ships the C standard library fenv.h header file, and implements the
related APIs (including GNU/glibc extensions): feclearexcept, fedisableexcept,
feenableexcept, fegetenv, fegetexcept, fegetexceptflag, fegetprec, fegetround,
feholdexcept, feraiseexcept, fesetenv, fesetexceptflag, fesetprec, fesetround,
fetestexcept, feupdateenv, and predefines both default and no-mask FP
environments.  See the
<a class="ulink" href="http://www.gnu.org/software/libc/manual/html_node/Arithmetic.html" target="_top">
GNU C Library manual</a> for full details of this functionality.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support for the C99 complex functions, except for the "long double"
implementations.  New APIs: cacos, cacosf, cacosh, cacoshf, carg, cargf, casin,
casinf, casinh, casinhf, catan, catanf, catanh, catanhf, ccos, ccosf, ccosh,
ccoshf, cexp, cexpf, cimag, cimagf, clog, clogf, conj, conjf, cpow, cpowf,
cproj, cprojf, creal, crealf, csin, csinf, csinh, csinhf, csqrt, csqrtf, ctan,
ctanf, ctanh, ctanhf.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Fix the width of "CJK Ambiguous Width" characters to 1 for singlebyte charsets
and 2 for East Asian multibyte charsets. (For UTF-8, it remains dependent on
the specified language, and the "@cjknarrow" locale modifier can still be used
to force width 1.)
</p></li><li class="listitem" style="list-style-type: disc"><p>
The strerror_r interface now has two flavors; if _GNU_SOURCE is
defined, it retains the previous behavior of returning char *
(but the result is now guaranteed to be NUL-terminated); otherwise
it now obeys POSIX semantics of returning int.
</p></li><li class="listitem" style="list-style-type: disc"><p>
/proc/sys now allows unfiltered access to the native NT namespace.  Access
restrictions still apply.  Direct device access via /proc/sys is not yet
supported.  File system access via block devices works.  For instance
(note the trailing slash!)
</p><pre class="screen">
bash$ cd /proc/sys/Device/HarddiskVolumeShadowCopy1/
</pre><p>
</p></li><li class="listitem" style="list-style-type: disc"><p>
Other new APIs: llround, llroundf, madvise, pthread_yield.
Export program_invocation_name, program_invocation_short_name.
Support TIOCGPGRP, TIOCSPGRP ioctls.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.7"></a>What's new and what changed in 1.7.7</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Partially revert the 1.7.6 change to set the Win32 current working directory
(CWD) always to an invalid directory, since it breaks backward compatibility
too much.  The Cygwin CWD and the Win32 CWD are now kept in sync again, unless
the Cygwin CWD is not usable as Win32 CWD.  See the reworked
<a class="xref" href="using.html#pathnames-win32-api" title="Using the Win32 file API in Cygwin applications">the section called &#8220;Using the Win32 file API in Cygwin applications&#8221;</a> for details.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Make sure to follow the Microsoft security advisory concerning DLL hijacking.
See the <a class="ulink" href="http://www.microsoft.com/technet/security/advisory/2269637.mspx" target="_top">Microsoft Security Advisory (2269637) "Insecure Library Loading Could Allow Remote Code Execution"</a> for details.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow to link against -lbinmode instead of /lib/binmode.o.  Same for
-ltextmode, -ltextreadmode and -lautomode. 
See <a class="xref" href="using-textbinary.html#textbin-devel" title="Programming">the section called &#8220;Programming&#8221;</a> for details.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.6"></a>What's new and what changed in 1.7.6</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Add new mount options "dos" and "ihash" to allow overriding Cygwin default
behaviour on broken filesystems not recognized by Cygwin.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add new mount option "bind" to allow remounting parts of the POSIX file
hirarchy somewhere else.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Ttys and ptys are handled as securable objects using file-like permissions
and owner/group information.  <span class="command"><strong>chmod</strong></span> and
<span class="command"><strong>chown</strong></span> now work on ttys/ptys.  A new mechanism is used
to propagate pty handles safely to other processes, which does not require
to use Cygserver.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Pass on coresize settings made with setrlimit(2).  This allows shells to
disable creating stackdump files in child processes via
</p><pre class="screen">ulimit -c 0</pre><p> in bash or </p><pre class="screen">limit coredumpsize 0</pre><p>
in tcsh.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Locale categories contain all localization strings additionally as wide-char
strings.  locale(1) prints these values just as on Linux.  nl_langinfo(3)
allows to fetch them.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New interfaces mkostemp(3) and mkostemps(3) are added.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New virtual file /proc/filesystems.
</p></li><li class="listitem" style="list-style-type: disc"><p>
clock_gettime(3) and clock_getres(3) accept CLOCK_MONOTONIC.
</p></li><li class="listitem" style="list-style-type: disc"><p>
DEPRECATED with 1.7.7: Cygwin handles the current working directory entirely
on its own.  The Win32 current working directory is set to an invalid path to
be out of the way.  [...]
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.5"></a>What's new and what changed in 1.7.5</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Support for DEC Backarrow Key Mode escape sequences (ESC [ ? 67 h, ESC [ ? 67 l)
in Windows console.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.3"></a>What's new and what changed in 1.7.3</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Support for GB2312/EUC-CN.  These charsets are implemented as aliases to GBK.
GB2312 is now the default charset name for the locales zh_CN and zh_SG, just
as on Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Modification and access timestamps of devices reflect the current time.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.2"></a>What's new and what changed in 1.7.2</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>Localization support has been much improved.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
  Cygwin now handles locales using the underlying Windows locale
  support.  The locale must exist in Windows to be recognized.
  Locale aliases from the file /usr/share/locale/locale.alias are also
  allowed, as long as their replacement is supported by the underlying Windows.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  New tool "locale" to fetch locale information and default locales based on
  the Windows default settings as well as lists of all supported locales
  and character sets.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Default charset for locales without explicit charset is now chosen
  from a list of Linux-compatible charsets.
  </p><p>
  For instance: en_US -&gt; ISO-8859-1, ja_JP -&gt; EUC-JP, zh_TW -&gt; Big5.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Added support for the charsets GEORGIAN-PS, PT154, and TIS-620.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Support for the various locale modifiers to switch charsets as on Linux.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Default charset in the "C" or "POSIX" locale has been changed back
  from UTF-8 to ASCII, to avoid problems with applications
  expecting a singlebyte charset in the "C"/"POSIX" locale.  Still use
  UTF-8 internally for filename conversion in this case.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  LC_COLLATE, LC_MONETARY, LC_NUMERIC, and LC_TIME localization is
  enabled via Windows locale support.  LC_MESSAGES is enabled via a big
  table with localized strings.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  fnmatch(3), regcomp(3), regexec(3) calls are now multibyte-aware.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  printf(3), wprintf(3) families of functions now handle the grouping
  flag, the apostrophe <code class="literal">'</code>, per POSIX-1.2008.  The
  integer portion of the result of a decimal conversion (%i, %d, %u, %f,
  %F, %g, %G) will be formatted with thousands' grouping characters.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  strftime(3), wcsftime(3), and strptime(3) now handle the E and O format
  modifiers to print/scan alternative date and time representations or to
  use alternative digits in locales which support this.  Additionally these
  functions now also support the padding modifiers '0' and '+', as well as
  a field width per POSIX-1.2008.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  New strfmon(3) call.
  </p></li></ul></div></li><li class="listitem" style="list-style-type: disc"><p>
Support open(2) flags O_CLOEXEC and O_TTY_INIT flags.  Support fcntl
flag F_DUPFD_CLOEXEC.  Support socket flags SOCK_CLOEXEC and SOCK_NONBLOCK.
Add new Linux-compatible API calls accept4(2), dup3(2), and pipe2(2).
Support the signal SIGPWR.
</p></li><li class="listitem" style="list-style-type: disc"><p>Enhanced Windows console support.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
  The console's backspace keycode can be changed using 'stty erase'.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Function keys send distinguished escape sequences compatible with rxvt.
  Keypad keys send distinguished escape sequences, xterm-style.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Support of combining Alt and AltGr modifiers in console window
  (compatible with xterm and mintty), so that e.g. Alt-@ sends ESC @
  also on keyboards where @ is mapped to an AltGr combination.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Report mouse wheel scroll events in mouse reporting mode 1000 (note:
  this doesn't seem to work on all systems, assumedly due to driver
  interworking issues).
  Add mouse reporting mode 1002 to report mouse drag movement.
  Add mouse reporting mode 1003 to report any mouse movement.
  Add focus event reporting (mode 1004), compatible with xterm and mintty.
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Add escape sequences for not bold (22), not invisible (28), not
  blinking (25) (compatible with xterm and mintty).
  </p></li><li class="listitem" style="list-style-type: disc"><p>
  Support VT100 line drawing graphics mode in console window (compatible
  with xterm and mintty).
  </p></li></ul></div></li><li class="listitem" style="list-style-type: disc"><p>
Handle native DOS paths always as if mounted with "posix=0,noacl".
</p></li><li class="listitem" style="list-style-type: disc"><p>
Handle UNC paths starting with slashes identical to /cygdrive paths.
In other words, use the /cygdrive mount flags for these paths as well.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Recognize NWFS filesystem and workaround broken OS call.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New support for eXtensible Data Record (XDR) encoding and decoding,
as defined by RFCs 1014, 1832, and 4506.  The XDR protocol and
functions are useful for cross-platfrom data exchange, and are
commonly used as the core data interchange format for Remote
Procedure Call (RPC) and NFS.
</p></li></ul></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="ov-new1.7.1"></a>What's new and what changed from 1.5 to 1.7</h3></div></div></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ov-new1.7-os"></a>OS related changes</h4></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Windows 95, 98 and Me are not supported anymore.  The new Cygwin 1.7 DLL
will not run on any of these systems.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add support for Windows 7 and Windows Server 2008 R2.
</p></li></ul></div></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ov-new1.7-file"></a>File Access related changes</h4></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Mount points are no longer stored in the registry.  Use /etc/fstab and
/etc/fstab.d/$USER instead.  Mount points created with mount(1) are only
local to the current session and disappear when the last Cygwin process
in the session exits.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Cygwin creates the mount points for /, /usr/bin, and /usr/lib
automatically from it's own position on the disk.  They don't have to be
specified in /etc/fstab.
</p></li><li class="listitem" style="list-style-type: disc"><p>
If a filename cannot be represented in the current character set, the
character will be converted to a sequence Ctrl-X + UTF-8 representation
of the character.  This allows to access all files, even those not
having a valid representation of their filename in the current character
set.  To always have a valid string, use the UTF-8 charset by
setting the environment variable $LANG, $LC_ALL, or $LC_CTYPE to a valid
POSIX value, such as "en_US.UTF-8".
</p></li><li class="listitem" style="list-style-type: disc"><p>
PATH_MAX is now 4096.  Internally, path names can be as long as the
underlying OS can handle (32K).
</p></li><li class="listitem" style="list-style-type: disc"><p>
struct dirent now supports d_type, filled out with DT_REG or DT_DIR.
All other file types return as DT_UNKNOWN for performance reasons.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The CYGWIN environment variable options "ntsec" and "smbntsec" have been
replaced by the per-mount option "acl"/"noacl".
</p></li><li class="listitem" style="list-style-type: disc"><p>
The CYGWIN environment variable option "ntea" has been removed without
substitute.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The CYGWIN environment variable option "check_case" has been removed in
favor of real case-sensitivity on file systems supporting it.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Creating filenames with special DOS characters '"', '*', ':', '&lt;',
'&gt;', '|' is supported.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Creating files with special DOS device filename components ("aux",
"nul", "prn") is supported.
</p></li><li class="listitem" style="list-style-type: disc"><p>
File names are case sensitive if the OS and the underlying file system
supports it.  Works on NTFS and NFS.  Does not work on FAT and Samba
shares.  Requires to change a registry key (see the User's Guide).  Can
be switched off on a per-mount basis.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Due to the above changes, managed mounts have been removed.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Incoming DOS paths are always handled case-insensitive and get no POSIX
permission, as if they are mounted with noacl,posix=0 mount flags.
</p></li><li class="listitem" style="list-style-type: disc"><p>
unlink(2) and rmdir(2) try very hard to remove files/directories even if
they are currently accessed or locked.  This is done by utilizing the
hidden recycle bin directories and marking the files for deletion.
</p></li><li class="listitem" style="list-style-type: disc"><p>
rename(2) rewritten to be more POSIX conformant.
</p></li><li class="listitem" style="list-style-type: disc"><p>
access(2) now performs checks using the real user ID, as required by
POSIX; the old behavior of querying based on effective user ID is
available through the new faccessat(2) and euidaccess(2) APIs.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add st_birthtim member to struct stat.
</p></li><li class="listitem" style="list-style-type: disc"><p>
File locking is now advisory, not mandatory anymore.  The fcntl(2) and
the new lockf(2) APIs create and maintain locks with POSIX semantics,
the flock(2) API creates and maintains locks with BSD semantics.  POSIX
and BSD locks are independent of each other.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Implement atomic O_APPEND mode.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New open(2) flags O_DIRECTORY, O_EXEC and O_SEARCH.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Make the "plain file with SYSTEM attribute set" style symlink default
again when creating symlinks.  Only create Windows shortcut style
symlinks if CYGWIN=winsymlinks is set in the environment.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Symlinks now use UTF-16 encoding for the target filename for better
internationalization support.  Cygwin 1.7 can read all old style
symlinks, but the new style is not compatible with older Cygwin
releases.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Handle NTFS native symlinks available since Vista/2008 as symlinks (but
don't create Vista/2008 symlinks due to unfortunate OS restrictions).
</p></li><li class="listitem" style="list-style-type: disc"><p>
Recognize NFS shares and handle them using native mechanisms.  Recognize
and create real symlinks on NFS shares.  Get correct stat(2) information
and set real mode bits on open(2), mkdir(2) and chmod(2).
</p></li><li class="listitem" style="list-style-type: disc"><p>
Recognize MVFS and workaround problems manipulating metadata and handling
DOS attributes.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Recognize Netapp DataOnTap drives and fix inode number handling.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Recognize Samba version beginning with Samba 3.0.28a using the new
extended version information negotiated with the Samba developers.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Stop faking hardlinks by copying the file on filesystems which don't
support hardlinks natively (FAT, FAT32, etc.).  Just return an error
instead, just like Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
List servers of all accessible domains and workgroups in // instead of
just the servers in the own domain/workgroup.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support Linux-like extended attributes ([fl]getxattr, [fl]listxattr,
[fl]setxattr, [fl]removexattr).
</p></li><li class="listitem" style="list-style-type: disc"><p>
New file conversion API for conversion from Win32 to POSIX path and vice
versa (cygwin_conv_path, cygwin_create_path, cygwin_conv_path_list).
</p></li><li class="listitem" style="list-style-type: disc"><p>
New openat family of functions: openat, faccessat, fchmodat, fchownat,
fstatat, futimesat, linkat, mkdirat, mkfifoat, mknodat, readlinkat,
renameat, symlinkat, unlinkat.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Other new APIs: posix_fadvise, posix_fallocate, funopen, fopencookie,
open_memstream, open_wmemstream, fmemopen, fdopendir, fpurge, mkstemps,
eaccess, euidaccess, canonicalize_file_name, fexecve, execvpe.
</p></li></ul></div></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ov-new1.7-net"></a>Network related changes</h4></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New implementation for blocking sockets and select on sockets which is
supposed to allow POSIX-compatible sharing of sockets between threads
and processes.
</p></li><li class="listitem" style="list-style-type: disc"><p>
send/sendto/sendmsg now send data in 64K chunks to circumvent an
internal buffer problem in WinSock (KB 201213).
</p></li><li class="listitem" style="list-style-type: disc"><p>
New send/recv option MSG_DONTWAIT.
</p></li><li class="listitem" style="list-style-type: disc"><p>
IPv6 support.  New APIs getaddrinfo, getnameinfo, freeaddrinfo,
gai_strerror, in6addr_any, in6addr_loopback.  On IPv6-less systems,
replacement functions are available for IPv4.  On systems with IPv6
enabled, the underlying WinSock functions are used.  While I tried hard
to get the functionality as POSIXy as possible, keep in mind that a
*fully* conformant implementation of getaddrinfo and other stuff is only
available starting with Windows Vista/2008.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Resolver functions (res_init, res_query, res_search, res_querydomain,
res_mkquery, res_send, dn_comp, dn_expand) are now part of Cygwin.
Applications don't have to link against minires anymore.  Actually, this
*is* the former libminires.a.
</p></li><li class="listitem" style="list-style-type: disc"><p>
rcmd is now implemented inside of Cygwin, instead of calling the WinSock
function.  This allows rsh(1) usage on Vista/2008 and later, which
dropped this function from WinSock.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Define multicast structures in netinet/in.h.  Note that fully conformant
multicast support is only available beginning with Vista/2008.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Improve get_ifconf.  Redefine struct ifreq and subsequent datastructures
to be able to keep more information.  Support SIOCGIFINDEX,
SIOCGIFDSTADDR and the Cygwin specific SIOCGIFFRNDLYNAM.  Support real
interface flags on systems supporting them.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Other new APIs: bindresvport, bindresvport_sa, gethostbyname2,
iruserok_sa, rcmd_af, rresvport_af.  getifaddrs, freeifaddrs,
if_nametoindex, if_indextoname, if_nameindex, if_freenameindex.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add /proc/net/if_inet6.
</p></li></ul></div></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ov-new1.7-device"></a>Device related changes</h4></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Reworked pipe implementation which uses overlapped IO to create more
reliable interruptible pipes and fifos.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The CYGWIN environment variable option "binmode" has been removed.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Improved fifo handling by using native Windows named pipes.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Detect when a stdin/stdout which looks like a pipe is really a tty.
Among other things, this allows a debugged application to recognize that
it is using the same tty as the debugger.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support UTF-8 in console window.
</p></li><li class="listitem" style="list-style-type: disc"><p>
In the console window the backspace key now emits DEL (0x7f) instead of
BS (0x08), Alt-Backspace emits ESC-DEL (0x1b,0x7f) instead of DEL
(0x7f), same as the Linux console and xterm.  Control-Space now emits an
ASCII NUL (0x0) character.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support up to 64 serial interfaces using /dev/ttyS0 - /dev/ttyS63.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support up to 128 raw disk drives /dev/sda - /dev/sddx.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API: cfmakeraw, get_avphys_pages, get_nprocs, get_nprocs_conf,
get_phys_pages, posix_openpt.
</p></li></ul></div></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ov-new1.7-posix"></a>Other POSIX related changes</h4></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
A lot of character sets are supported now via a call to setlocale().
The setting of the environment variables $LANG, $LC_ALL or $LC_CTYPE
will be used.  For instance, setting $LANG to "de_DE.ISO-8859-15" before
starting a Cygwin session will use the ISO-8859-15 character set in the
entire session.  The default locale in the absence of one of the
aforementioned environment variables is "C.UTF-8".
</p><p>
The full list of supported character sets: "ASCII", "ISO-8859-x" with x
in 1-16, except 12, "UTF-8", Windows codepages "CPxxx", with xxx in
(437, 720, 737, 775, 850, 852, 855, 857, 858, 862, 866, 874, 1125, 1250,
1251, 1252, 1253, 1254, 1255, 1256, 1257, 1258), "KOI8-R", "KOI8-U",
"SJIS", "GBK", "eucJP", "eucKR", and "Big5".
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow multiple concurrent read locks per thread for pthread_rwlock_t.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Implement pthread_kill(thread, 0) as per POSIX.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New API for POSIX IPC: Named semaphores: sem_open, sem_close,
sem_unlink.  Message queues: mq_open, mq_getattr, mq_setattr, mq_notify,
mq_send, mq_timedsend, mq_receive, mq_timedreceive, mq_close, mq_unlink.
Shared memory: shm_open, shm_unlink.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Only declare expected functions in &lt;strings.h&gt;, don't include
&lt;string.h&gt; from here.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Support for WCONTINUED, WIFCONTINUED() added to waitpid and wait4.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New APIs: _Exit, confstr, insque, remque, sys_sigabbrev, posix_madvise,
posix_memalign, reallocf, exp10, exp10f, pow10, pow10f, lrint, lrintf,
rint, rintf, llrint, llrintf, llrintl, lrintl, rintl, mbsnrtowcs,
strcasestr, stpcpy, stpncpy, wcpcpy, wcpncpy, wcsnlen, wcsnrtombs,
wcsftime, wcstod, wcstof, wcstoimax, wcstok, wcstol, wcstoll, wcstoul,
wcstoull, wcstoumax, wcsxfrm, wcscasecmp, wcsncasecmp, fgetwc, fgetws,
fputwc, fputws, fwide, getwc, getwchar, putwc, putwchar, ungetwc,
asnprintf, dprintf, vasnprintf, vdprintf, wprintf, fwprintf, swprintf,
vwprintf, vfwprintf, vswprintf, wscanf, fwscanf, swscanf, vwscanf,
vfwscanf, vswscanf.
</p></li></ul></div></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ov-new1.7-sec"></a>Security related changes</h4></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
Getting a domain user's groups is hopefully more bulletproof now.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Cygwin now comes with a real LSA authentication package.  This must be
manually installed by a privileged user using the /bin/cyglsa-config
script.  The advantages and disadvantages are noted in
https://cygwin.com/ml/cygwin-developers/2006-11/msg00000.html
</p></li><li class="listitem" style="list-style-type: disc"><p>
Cygwin now allows storage and use of user passwords in a hidden area of
the registry.  This is tried first when Cygwin is called by privileged
processes to switch the user context.  This allows, for instance, ssh
public key sessions with full network credentials to access shares on
other machines.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New options have been added to the mkpasswd and mkgroup tools to ease
use in multi-machine and multi-domain environments.  The existing
options have a slightly changed behaviour.
</p></li></ul></div></div><div class="sect3"><div class="titlepage"><div><div><h4 class="title"><a name="ov-new1.7-misc"></a>Miscellaneous</h4></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>
New ldd utility, similar to Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
New link libraries libdl.a, libresolv.a, librt.a.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Fallout from the long path names: If the current working directory is
longer than 260 bytes, or if the current working directory is a virtual
path (like /proc, /cygdrive, //server), don't call native Win32 programs
since they don't understand these paths.
</p></li><li class="listitem" style="list-style-type: disc"><p>
On the first usage of a DOS path (C:\foo, \\foo\bar), the Cygwin DLL
emits a scary warning that DOS paths shouldn't be used.  This warning
may be disabled via the new CYGWIN=nodosfilewarning setting.
</p></li><li class="listitem" style="list-style-type: disc"><p>
The CYGWIN environment variable option "server" has been removed.
Cygwin automatically uses cygserver if it's available.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Allow environment of arbitrary size instead of a maximum of 32K.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Don't force uppercase environment when started from a non-Cygwin
process.  Except for certain Windows and POSIX variables which are
always uppercased, preserve environment case.  Switch back to old
behaviour with the new CYGWIN=upcaseenv setting.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Detect and report a missing DLL on process startup.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add /proc/registry32 and /proc/registry64 paths to access 32 bit and 64
bit registry on 64 bit systems.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add the ability to distinguish registry keys and registry values with
the same name in the same registry subtree.  The key is called "foo" and
the value will be called "foo%val" in this case.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Align /proc/cpuinfo more closly to Linux content.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Add /proc/$PID/mounts entries and a symlink /proc/mounts pointing to
/proc/self/mounts as on Linux.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Optimized strstr and memmem implementation.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Remove backwards compatibility with old signal masks.  (Some *very* old
programs which use signal masks may no longer work correctly).
</p></li><li class="listitem" style="list-style-type: disc"><p>
Cygwin now exports wrapper functions for libstdc++ operators new and
delete, to support the toolchain in implementing full C++ standards
conformance when working with shared libraries.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Different Cygwin installations in different paths can be run in parallel
without knowing of each other.  The path of the Cygwin DLL used in a
process is a key used when creating IPC objects.  So different Cygwin
DLLs are running in different namespaces.
</p></li><li class="listitem" style="list-style-type: disc"><p>
Each Cygwin DLL stores its path and installation key in the registry.
This allows troubleshooting of problems which could be a result of
having multiple concurrent Cygwin installations.
</p></li></ul></div></div></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="highlights.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="overview.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="setup-net.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Highlights of Cygwin Functionality&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Chapter&#160;2.&#160;Setting Up Cygwin</td></tr></table></div></body></html>
