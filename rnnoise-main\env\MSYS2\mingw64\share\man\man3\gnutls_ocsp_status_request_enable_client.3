.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_status_request_enable_client" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_status_request_enable_client \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_ocsp_status_request_enable_client(gnutls_session_t " session ", gnutls_datum_t * " responder_id ", size_t " responder_id_size ", gnutls_datum_t * " extensions ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_datum_t * responder_id" 12
ignored, must be \fBNULL\fP
.IP "size_t responder_id_size" 12
ignored, must be zero
.IP "gnutls_datum_t * extensions" 12
ignored, must be \fBNULL\fP
.SH "DESCRIPTION"
This function is to be used by clients to request OCSP response
from the server, using the "status_request" TLS extension.  Only
OCSP status type is supported.

Previous versions of GnuTLS supported setting  \fIresponder_id\fP and
 \fIextensions\fP fields, but due to the difficult semantics of the
parameter usage, and other issues, this support was removed
since 3.6.0 and these parameters must be set to \fBNULL\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "SINCE"
3.1.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
