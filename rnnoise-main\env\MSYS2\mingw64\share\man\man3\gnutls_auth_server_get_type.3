.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_auth_server_get_type" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_auth_server_get_type \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_credentials_type_t gnutls_auth_server_get_type(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
Returns the type of credentials that were used for server authentication.
The returned information is to be used to distinguish the function used
to access authentication data.

Note that on resumed sessions, this function returns the schema
used in the original session authentication.
.SH "RETURNS"
The type of credentials for the server authentication
schema, a \fBgnutls_credentials_type_t\fP type.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
