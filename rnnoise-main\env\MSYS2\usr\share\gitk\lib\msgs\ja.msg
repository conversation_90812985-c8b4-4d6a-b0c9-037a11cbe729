set ::msgcat::header "Project-Id-Version: gitk\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2015-11-12 13:00+0900\nLast-Translator: <PERSON><PERSON> <<EMAIL>>\nLanguage-Team: Japanese\nLanguage: ja\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=1; plural=0;\n"
::msgcat::mcset ja "Couldn't get list of unmerged files:" "\u30de\u30fc\u30b8\u3055\u308c\u3066\u3044\u306a\u3044\u30d5\u30a1\u30a4\u30eb\u306e\u30ea\u30b9\u30c8\u3092\u53d6\u5f97\u3067\u304d\u307e\u305b\u3093:"
::msgcat::mcset ja "Color words" "\u5909\u66f4\u3092\u7740\u8272"
::msgcat::mcset ja "Markup words" "\u5909\u66f4\u3092\u30de\u30fc\u30af\u30a2\u30c3\u30d7"
::msgcat::mcset ja "Error parsing revisions:" "\u30ea\u30d3\u30b8\u30e7\u30f3\u89e3\u6790\u30a8\u30e9\u30fc:"
::msgcat::mcset ja "Error executing --argscmd command:" "--argscmd \u30b3\u30de\u30f3\u30c9\u5b9f\u884c\u30a8\u30e9\u30fc:"
::msgcat::mcset ja "No files selected: --merge specified but no files are unmerged." "\u30d5\u30a1\u30a4\u30eb\u672a\u9078\u629e: --merge \u304c\u6307\u5b9a\u3055\u308c\u307e\u3057\u305f\u304c\u3001\u30de\u30fc\u30b8\u3055\u308c\u3066\u3044\u306a\u3044\u30d5\u30a1\u30a4\u30eb\u306f\u3042\u308a\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "No files selected: --merge specified but no unmerged files are within file limit." "\u30d5\u30a1\u30a4\u30eb\u672a\u9078\u629e: --merge \u304c\u6307\u5b9a\u3055\u308c\u307e\u3057\u305f\u304c\u3001\u30d5\u30a1\u30a4\u30eb\u5236\u9650\u5185\u306b\u30de\u30fc\u30b8\u3055\u308c\u3066\u3044\u306a\u3044\u30d5\u30a1\u30a4\u30eb\u306f\u3042\u308a\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Error executing git log:" "git log \u5b9f\u884c\u30a8\u30e9\u30fc:"
::msgcat::mcset ja "Reading" "\u8aad\u307f\u8fbc\u307f\u4e2d"
::msgcat::mcset ja "Reading commits..." "\u30b3\u30df\u30c3\u30c8\u8aad\u307f\u8fbc\u307f\u4e2d..."
::msgcat::mcset ja "No commits selected" "\u30b3\u30df\u30c3\u30c8\u304c\u9078\u629e\u3055\u308c\u3066\u3044\u307e\u305b\u3093"
::msgcat::mcset ja "Command line" "\u30b3\u30de\u30f3\u30c9\u884c"
::msgcat::mcset ja "Can't parse git log output:" "git log \u306e\u51fa\u529b\u3092\u89e3\u6790\u3067\u304d\u307e\u305b\u3093:"
::msgcat::mcset ja "No commit information available" "\u6709\u52b9\u306a\u30b3\u30df\u30c3\u30c8\u306e\u60c5\u5831\u304c\u3042\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "OK" "OK"
::msgcat::mcset ja "Cancel" "\u30ad\u30e3\u30f3\u30bb\u30eb"
::msgcat::mcset ja "&Update" "\u66f4\u65b0(&U)"
::msgcat::mcset ja "&Reload" "\u30ea\u30ed\u30fc\u30c9(&R)"
::msgcat::mcset ja "Reread re&ferences" "\u30ea\u30d5\u30a1\u30ec\u30f3\u30b9\u3092\u518d\u8aad\u307f\u8fbc\u307f(&F)"
::msgcat::mcset ja "&List references" "\u30ea\u30d5\u30a1\u30ec\u30f3\u30b9\u30ea\u30b9\u30c8\u3092\u8868\u793a(&L)"
::msgcat::mcset ja "Start git &gui" "git gui \u306e\u958b\u59cb(&G)"
::msgcat::mcset ja "&Quit" "\u7d42\u4e86(&Q)"
::msgcat::mcset ja "&File" "\u30d5\u30a1\u30a4\u30eb(&F)"
::msgcat::mcset ja "&Preferences" "\u8a2d\u5b9a(&P)"
::msgcat::mcset ja "&Edit" "\u7de8\u96c6(&E)"
::msgcat::mcset ja "&New view..." "\u65b0\u898f\u30d3\u30e5\u30fc(&N)..."
::msgcat::mcset ja "&Edit view..." "\u30d3\u30e5\u30fc\u7de8\u96c6(&E)..."
::msgcat::mcset ja "&Delete view" "\u30d3\u30e5\u30fc\u524a\u9664(&D)"
::msgcat::mcset ja "&All files" "\u5168\u3066\u306e\u30d5\u30a1\u30a4\u30eb(&A)"
::msgcat::mcset ja "&View" "\u30d3\u30e5\u30fc(&V)"
::msgcat::mcset ja "&About gitk" "gitk \u306b\u3064\u3044\u3066(&A)"
::msgcat::mcset ja "&Key bindings" "\u30ad\u30fc\u30d0\u30a4\u30f3\u30c7\u30a3\u30f3\u30b0(&K)"
::msgcat::mcset ja "&Help" "\u30d8\u30eb\u30d7(&H)"
::msgcat::mcset ja "SHA1 ID:" "SHA1 ID:"
::msgcat::mcset ja "Row" "\u884c"
::msgcat::mcset ja "Find" "\u691c\u7d22"
::msgcat::mcset ja "commit" "\u30b3\u30df\u30c3\u30c8"
::msgcat::mcset ja "containing:" "\u542b\u3080:"
::msgcat::mcset ja "touching paths:" "\u30d1\u30b9\u306e\u4e00\u90e8:"
::msgcat::mcset ja "adding/removing string:" "\u8ffd\u52a0/\u9664\u53bb\u3055\u308c\u308b\u6587\u5b57\u5217:"
::msgcat::mcset ja "changing lines matching:" "\u5909\u66f4\u3055\u308c\u308b\u6587\u5b57\u5217"
::msgcat::mcset ja "Exact" "\u82f1\u5b57\u306e\u5927\u5c0f\u3092\u533a\u5225\u3059\u308b"
::msgcat::mcset ja "IgnCase" "\u82f1\u5b57\u306e\u5927\u5c0f\u3092\u533a\u5225\u3057\u306a\u3044"
::msgcat::mcset ja "Regexp" "\u6b63\u898f\u8868\u73fe"
::msgcat::mcset ja "All fields" "\u5168\u3066\u306e\u9805\u76ee"
::msgcat::mcset ja "Headline" "\u30d8\u30c3\u30c9\u30e9\u30a4\u30f3"
::msgcat::mcset ja "Comments" "\u30b3\u30e1\u30f3\u30c8"
::msgcat::mcset ja "Author" "\u4f5c\u8005"
::msgcat::mcset ja "Committer" "\u30b3\u30df\u30c3\u30c8\u8005"
::msgcat::mcset ja "Search" "\u691c\u7d22"
::msgcat::mcset ja "Diff" "Diff"
::msgcat::mcset ja "Old version" "\u65e7\u30d0\u30fc\u30b8\u30e7\u30f3"
::msgcat::mcset ja "New version" "\u65b0\u30d0\u30fc\u30b8\u30e7\u30f3"
::msgcat::mcset ja "Lines of context" "\u6587\u8108\u884c\u6570"
::msgcat::mcset ja "Ignore space change" "\u7a7a\u767d\u306e\u9055\u3044\u3092\u7121\u8996"
::msgcat::mcset ja "Line diff" "\u884c\u6bce\u306ediff"
::msgcat::mcset ja "Patch" "\u30d1\u30c3\u30c1"
::msgcat::mcset ja "Tree" "\u30c4\u30ea\u30fc"
::msgcat::mcset ja "Diff this -> selected" "\u3053\u308c\u3068\u9078\u629e\u3057\u305f\u30b3\u30df\u30c3\u30c8\u306ediff\u3092\u898b\u308b"
::msgcat::mcset ja "Diff selected -> this" "\u9078\u629e\u3057\u305f\u30b3\u30df\u30c3\u30c8\u3068\u3053\u308c\u306ediff\u3092\u898b\u308b"
::msgcat::mcset ja "Make patch" "\u30d1\u30c3\u30c1\u4f5c\u6210"
::msgcat::mcset ja "Create tag" "\u30bf\u30b0\u751f\u6210"
::msgcat::mcset ja "Copy commit summary" "\u30b3\u30df\u30c3\u30c8\u306e\u8981\u7d04\u3092\u30b3\u30d4\u30fc\u3059\u308b"
::msgcat::mcset ja "Write commit to file" "\u30b3\u30df\u30c3\u30c8\u3092\u30d5\u30a1\u30a4\u30eb\u306b\u66f8\u304d\u51fa\u3059"
::msgcat::mcset ja "Create new branch" "\u65b0\u898f\u30d6\u30e9\u30f3\u30c1\u751f\u6210"
::msgcat::mcset ja "Cherry-pick this commit" "\u3053\u306e\u30b3\u30df\u30c3\u30c8\u3092\u30c1\u30a7\u30ea\u30fc\u30d4\u30c3\u30af\u3059\u308b"
::msgcat::mcset ja "Reset HEAD branch to here" "\u30d6\u30e9\u30f3\u30c1\u306eHEAD\u3092\u3053\u3053\u306b\u30ea\u30bb\u30c3\u30c8\u3059\u308b"
::msgcat::mcset ja "Mark this commit" "\u3053\u306e\u30b3\u30df\u30c3\u30c8\u306b\u30de\u30fc\u30af\u3092\u3064\u3051\u308b"
::msgcat::mcset ja "Return to mark" "\u30de\u30fc\u30af\u3092\u4ed8\u3051\u305f\u6240\u306b\u623b\u308b"
::msgcat::mcset ja "Find descendant of this and mark" "\u3053\u308c\u3068\u30de\u30fc\u30af\u3092\u3064\u3051\u305f\u6240\u3068\u306e\u5b50\u5b6b\u3092\u898b\u3064\u3051\u308b"
::msgcat::mcset ja "Compare with marked commit" "\u30de\u30fc\u30af\u3092\u4ed8\u3051\u305f\u30b3\u30df\u30c3\u30c8\u3068\u6bd4\u8f03\u3059\u308b"
::msgcat::mcset ja "Diff this -> marked commit" "\u3053\u308c\u3068\u30de\u30fc\u30af\u3092\u4ed8\u3051\u305f\u30b3\u30df\u30c3\u30c8\u306ediff\u3092\u898b\u308b"
::msgcat::mcset ja "Diff marked commit -> this" "\u30de\u30fc\u30af\u3092\u4ed8\u3051\u305f\u30b3\u30df\u30c3\u30c8\u3068\u3053\u308c\u306ediff\u3092\u898b\u308b"
::msgcat::mcset ja "Revert this commit" "\u3053\u306e\u30b3\u30df\u30c3\u30c8\u3092\u64a4\u56de\u3059\u308b"
::msgcat::mcset ja "Check out this branch" "\u3053\u306e\u30d6\u30e9\u30f3\u30c1\u3092\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8\u3059\u308b"
::msgcat::mcset ja "Remove this branch" "\u3053\u306e\u30d6\u30e9\u30f3\u30c1\u3092\u9664\u53bb\u3059\u308b"
::msgcat::mcset ja "Copy branch name" "\u30d6\u30e9\u30f3\u30c1\u540d\u3092\u30b3\u30d4\u30fc\u3059\u308b"
::msgcat::mcset ja "Highlight this too" "\u3053\u308c\u3082\u30cf\u30a4\u30e9\u30a4\u30c8\u3055\u305b\u308b"
::msgcat::mcset ja "Highlight this only" "\u3053\u308c\u3060\u3051\u3092\u30cf\u30a4\u30e9\u30a4\u30c8\u3055\u305b\u308b"
::msgcat::mcset ja "External diff" "\u5916\u90e8diff\u30c4\u30fc\u30eb"
::msgcat::mcset ja "Blame parent commit" "\u89aa\u30b3\u30df\u30c3\u30c8\u304b\u3089 blame \u3092\u304b\u3051\u308b"
::msgcat::mcset ja "Copy path" "\u30d1\u30b9\u540d\u3092\u30b3\u30d4\u30fc\u3059\u308b"
::msgcat::mcset ja "Show origin of this line" "\u3053\u306e\u884c\u306e\u51fa\u81ea\u3092\u8868\u793a\u3059\u308b"
::msgcat::mcset ja "Run git gui blame on this line" "\u3053\u306e\u884c\u306b git gui \u3067 blame \u3092\u304b\u3051\u308b"
::msgcat::mcset ja "About gitk" "gitk \u306b\u3064\u3044\u3066"
::msgcat::mcset ja "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk - git\u30b3\u30df\u30c3\u30c8\u30d3\u30e5\u30fc\u30a2\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\n\u4f7f\u7528\u304a\u3088\u3073\u518d\u914d\u5e03\u306f GNU General Public License \u306b\u5f93\u3063\u3066\u304f\u3060\u3055\u3044"
::msgcat::mcset ja "Close" "\u9589\u3058\u308b"
::msgcat::mcset ja "Gitk key bindings" "Gitk \u30ad\u30fc\u30d0\u30a4\u30f3\u30c7\u30a3\u30f3\u30b0"
::msgcat::mcset ja "Gitk key bindings:" "Gitk \u30ad\u30fc\u30d0\u30a4\u30f3\u30c7\u30a3\u30f3\u30b0:"
::msgcat::mcset ja "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009\u7d42\u4e86"
::msgcat::mcset ja "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009\u30a6\u30a3\u30f3\u30c9\u30a6\u3092\u9589\u3058\u308b"
::msgcat::mcset ja "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009\u6700\u521d\u306e\u30b3\u30df\u30c3\u30c8\u306b\u79fb\u52d5"
::msgcat::mcset ja "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009\u6700\u5f8c\u306e\u30b3\u30df\u30c3\u30c8\u306b\u79fb\u52d5"
::msgcat::mcset ja "<Up>, p, k\u0009Move up one commit" "<Up>, p, k\u0009\u4e00\u3064\u4e0a\u306e\u30b3\u30df\u30c3\u30c8\u306b\u79fb\u52d5"
::msgcat::mcset ja "<Down>, n, j\u0009Move down one commit" "<Down>, n, j\u0009\u4e00\u3064\u4e0b\u306e\u30b3\u30df\u30c3\u30c8\u306b\u79fb\u52d5"
::msgcat::mcset ja "<Left>, z, h\u0009Go back in history list" "<Left>, z, h\u0009\u5c65\u6b74\u306e\u524d\u306b\u623b\u308b"
::msgcat::mcset ja "<Right>, x, l\u0009Go forward in history list" "<Right>, x, l\u0009\u5c65\u6b74\u306e\u6b21\u3078\u9032\u3080"
::msgcat::mcset ja "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n(\u6570\u5b57)>\u0009\u5c65\u6b74\u4e0a\u3067\u73fe\u5728\u306e\u30b3\u30df\u30c3\u30c8\u306e\u89aa\u30b3\u30df\u30c3\u30c8\u306e\u5185\u306en(\u6570\u5b57)\u756a\u76ee\u306e\u30b3\u30df\u30c3\u30c8\u3078\u79fb\u52d5"
::msgcat::mcset ja "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009\u30b3\u30df\u30c3\u30c8\u30ea\u30b9\u30c8\u306e\u4e00\u3064\u4e0a\u306e\u30da\u30fc\u30b8\u306b\u79fb\u52d5"
::msgcat::mcset ja "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009\u30b3\u30df\u30c3\u30c8\u30ea\u30b9\u30c8\u306e\u4e00\u3064\u4e0b\u306e\u30da\u30fc\u30b8\u306b\u79fb\u52d5"
::msgcat::mcset ja "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009\u30b3\u30df\u30c3\u30c8\u30ea\u30b9\u30c8\u306e\u4e00\u756a\u4e0a\u306b\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009\u30b3\u30df\u30c3\u30c8\u30ea\u30b9\u30c8\u306e\u4e00\u756a\u4e0b\u306b\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<%s-Up>\u0009Scroll commit list up one line" "<%s-Up>\u0009\u30b3\u30df\u30c3\u30c8\u30ea\u30b9\u30c8\u306e\u4e00\u3064\u4e0b\u306e\u884c\u306b\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<%s-Down>\u0009Scroll commit list down one line" "<%s-Down>\u0009\u30b3\u30df\u30c3\u30c8\u30ea\u30b9\u30c8\u306e\u4e00\u3064\u4e0b\u306e\u884c\u306b\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009\u30b3\u30df\u30c3\u30c8\u30ea\u30b9\u30c8\u306e\u4e0a\u306e\u30da\u30fc\u30b8\u306b\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009\u30b3\u30df\u30c3\u30c8\u30ea\u30b9\u30c8\u306e\u4e0b\u306e\u30da\u30fc\u30b8\u306b\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Up>\u0009\u5f8c\u65b9\u3092\u691c\u7d22 (\u4e0a\u65b9\u306e\u30fb\u65b0\u3057\u3044\u30b3\u30df\u30c3\u30c8)"
::msgcat::mcset ja "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Down>\u0009\u524d\u65b9\u3092\u691c\u7d22\uff08\u4e0b\u65b9\u306e\u30fb\u53e4\u3044\u30b3\u30df\u30c3\u30c8\uff09"
::msgcat::mcset ja "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009diff\u753b\u9762\u3092\u4e0a\u306e\u30da\u30fc\u30b8\u306b\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<Backspace>\u0009Scroll diff view up one page" "<Backspace>\u0009diff\u753b\u9762\u3092\u4e0a\u306e\u30da\u30fc\u30b8\u306b\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<Space>\u0009\u0009Scroll diff view down one page" "<Space>\u0009\u0009diff\u753b\u9762\u3092\u4e0b\u306e\u30da\u30fc\u30b8\u306b\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009diff\u753b\u9762\u3092\u4e0a\u306b18\u884c\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009diff\u753b\u9762\u3092\u4e0b\u306b18\u884c\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009\u691c\u7d22"
::msgcat::mcset ja "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009\u6b21\u3092\u691c\u7d22\u3057\u3066\u79fb\u52d5"
::msgcat::mcset ja "<Return>\u0009Move to next find hit" "<Return>\u0009\u6b21\u3092\u691c\u7d22\u3057\u3066\u79fb\u52d5"
::msgcat::mcset ja "g\u0009\u0009Go to commit" "g\u0009\u0009\u6307\u5b9a\u3057\u3066\u30b3\u30df\u30c3\u30c8\u306b\u79fb\u52d5"
::msgcat::mcset ja "/\u0009\u0009Focus the search box" "/\u0009\u0009\u691c\u7d22\u30dc\u30c3\u30af\u30b9\u306b\u30d5\u30a9\u30fc\u30ab\u30b9"
::msgcat::mcset ja "?\u0009\u0009Move to previous find hit" "?\u0009\u0009\u524d\u3092\u691c\u7d22\u3057\u3066\u79fb\u52d5"
::msgcat::mcset ja "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009\u6b21\u306e\u30d5\u30a1\u30a4\u30eb\u306bdiff\u753b\u9762\u3092\u30b9\u30af\u30ed\u30fc\u30eb\u3059\u308b"
::msgcat::mcset ja "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009diff\u753b\u9762\u306e\u6b21\u3092\u691c\u7d22"
::msgcat::mcset ja "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009diff\u753b\u9762\u306e\u524d\u3092\u691c\u7d22"
::msgcat::mcset ja "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009\u6587\u5b57\u30b5\u30a4\u30ba\u3092\u62e1\u5927"
::msgcat::mcset ja "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009\u6587\u5b57\u30b5\u30a4\u30ba\u3092\u62e1\u5927"
::msgcat::mcset ja "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009\u6587\u5b57\u30b5\u30a4\u30ba\u3092\u7e2e\u5c0f"
::msgcat::mcset ja "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009\u6587\u5b57\u30b5\u30a4\u30ba\u3092\u7e2e\u5c0f"
::msgcat::mcset ja "<F5>\u0009\u0009Update" "<F5>\u0009\u0009\u66f4\u65b0"
::msgcat::mcset ja "Error creating temporary directory %s:" "\u4e00\u6642\u30c7\u30a3\u30ec\u30af\u30c8\u30ea %s \u751f\u6210\u6642\u30a8\u30e9\u30fc:"
::msgcat::mcset ja "Error getting \"%s\" from %s:" "\"%s\" \u306e\u30a8\u30e9\u30fc\u304c %s \u306b\u767a\u751f:"
::msgcat::mcset ja "command failed:" "\u30b3\u30de\u30f3\u30c9\u5931\u6557:"
::msgcat::mcset ja "No such commit" "\u305d\u306e\u3088\u3046\u306a\u30b3\u30df\u30c3\u30c8\u306f\u3042\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "git gui blame: command failed:" "git gui blame: \u30b3\u30de\u30f3\u30c9\u5931\u6557:"
::msgcat::mcset ja "Couldn't read merge head: %s" "\u30de\u30fc\u30b8\u3059\u308b HEAD \u3092\u8aad\u307f\u8fbc\u3081\u307e\u305b\u3093: %s"
::msgcat::mcset ja "Error reading index: %s" "\u30a4\u30f3\u30c7\u30c3\u30af\u30b9\u8aad\u307f\u8fbc\u307f\u30a8\u30e9\u30fc: %s"
::msgcat::mcset ja "Couldn't start git blame: %s" "git blame \u3092\u59cb\u3081\u3089\u308c\u307e\u305b\u3093: %s"
::msgcat::mcset ja "Searching" "\u691c\u7d22\u4e2d"
::msgcat::mcset ja "Error running git blame: %s" "git blame \u5b9f\u884c\u30a8\u30e9\u30fc: %s"
::msgcat::mcset ja "That line comes from commit %s,  which is not in this view" "\u30b3\u30df\u30c3\u30c8 %s \u306b\u7531\u6765\u3059\u308b\u305d\u306e\u884c\u306f\u3001\u3053\u306e\u30d3\u30e5\u30fc\u306b\u8868\u793a\u3055\u308c\u3066\u3044\u307e\u305b\u3093"
::msgcat::mcset ja "External diff viewer failed:" "\u5916\u90e8diff\u30d3\u30e5\u30fc\u30a2\u304c\u5931\u6557:"
::msgcat::mcset ja "All files" "\u5168\u3066\u306e\u30d5\u30a1\u30a4\u30eb"
::msgcat::mcset ja "View" "\u30d3\u30e5\u30fc"
::msgcat::mcset ja "Gitk view definition" "Gitk \u30d3\u30e5\u30fc\u5b9a\u7fa9"
::msgcat::mcset ja "Remember this view" "\u3053\u306e\u30d3\u30e5\u30fc\u3092\u8a18\u61b6\u3059\u308b"
::msgcat::mcset ja "References (space separated list):" "\u30ea\u30d5\u30a1\u30ec\u30f3\u30b9\uff08\u30b9\u30da\u30fc\u30b9\u533a\u5207\u308a\u306e\u30ea\u30b9\u30c8\uff09:"
::msgcat::mcset ja "Branches & tags:" "\u30d6\u30e9\u30f3\u30c1\uff06\u30bf\u30b0:"
::msgcat::mcset ja "All refs" "\u5168\u3066\u306e\u30ea\u30d5\u30a1\u30ec\u30f3\u30b9"
::msgcat::mcset ja "All (local) branches" "\u5168\u3066\u306e\uff08\u30ed\u30fc\u30ab\u30eb\u306a\uff09\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "All tags" "\u5168\u3066\u306e\u30bf\u30b0"
::msgcat::mcset ja "All remote-tracking branches" "\u5168\u3066\u306e\u30ea\u30e2\u30fc\u30c8\u8ffd\u8de1\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Commit Info (regular expressions):" "\u30b3\u30df\u30c3\u30c8\u60c5\u5831\uff08\u6b63\u898f\u8868\u73fe\uff09:"
::msgcat::mcset ja "Author:" "\u4f5c\u8005:"
::msgcat::mcset ja "Committer:" "\u30b3\u30df\u30c3\u30c8\u8005:"
::msgcat::mcset ja "Commit Message:" "\u30b3\u30df\u30c3\u30c8\u30e1\u30c3\u30bb\u30fc\u30b8:"
::msgcat::mcset ja "Matches all Commit Info criteria" "\u30b3\u30df\u30c3\u30c8\u60c5\u5831\u306e\u5168\u3066\u306e\u6761\u4ef6\u306b\u4e00\u81f4"
::msgcat::mcset ja "Matches no Commit Info criteria" "\u30b3\u30df\u30c3\u30c8\u60c5\u5831\u306e\u5168\u3066\u306e\u6761\u4ef6\u306b\u4e0d\u4e00\u81f4"
::msgcat::mcset ja "Changes to Files:" "\u5909\u66f4\u3057\u305f\u30d5\u30a1\u30a4\u30eb:"
::msgcat::mcset ja "Fixed String" "\u56fa\u5b9a\u6587\u5b57\u5217"
::msgcat::mcset ja "Regular Expression" "\u6b63\u898f\u8868\u73fe"
::msgcat::mcset ja "Search string:" "\u691c\u7d22\u6587\u5b57\u5217:"
::msgcat::mcset ja "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "\u30b3\u30df\u30c3\u30c8\u65e5\u6642 (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):"
::msgcat::mcset ja "Since:" "\u671f\u9593\u306e\u59cb\u3081:"
::msgcat::mcset ja "Until:" "\u671f\u9593\u306e\u7d42\u308f\u308a:"
::msgcat::mcset ja "Limit and/or skip a number of revisions (positive integer):" "\u5236\u9650\u30fb\u7701\u7565\u3059\u308b\u30ea\u30d3\u30b8\u30e7\u30f3\u306e\u6570\uff08\u6b63\u306e\u6574\u6570\uff09:"
::msgcat::mcset ja "Number to show:" "\u8868\u793a\u3059\u308b\u6570:"
::msgcat::mcset ja "Number to skip:" "\u7701\u7565\u3059\u308b\u6570:"
::msgcat::mcset ja "Miscellaneous options:" "\u305d\u306e\u4ed6\u306e\u30aa\u30d7\u30b7\u30e7\u30f3:"
::msgcat::mcset ja "Strictly sort by date" "\u53b3\u5bc6\u306b\u65e5\u4ed8\u9806\u3067\u4e26\u3073\u66ff\u3048"
::msgcat::mcset ja "Mark branch sides" "\u5074\u679d\u30de\u30fc\u30af"
::msgcat::mcset ja "Limit to first parent" "\u6700\u521d\u306e\u89aa\u306b\u5236\u9650"
::msgcat::mcset ja "Simple history" "\u7c21\u6613\u306a\u5c65\u6b74"
::msgcat::mcset ja "Additional arguments to git log:" "git log \u3078\u306e\u8ffd\u52a0\u306e\u5f15\u6570:"
::msgcat::mcset ja "Enter files and directories to include, one per line:" "\u542b\u307e\u308c\u308b\u30d5\u30a1\u30a4\u30eb\u30fb\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u3092\u4e00\u884c\u3054\u3068\u306b\u5165\u529b:"
::msgcat::mcset ja "Command to generate more commits to include:" "\u30b3\u30df\u30c3\u30c8\u8ffd\u52a0\u30b3\u30de\u30f3\u30c9:"
::msgcat::mcset ja "Gitk: edit view" "Gitk: \u30d3\u30e5\u30fc\u7de8\u96c6"
::msgcat::mcset ja "-- criteria for selecting revisions" "\u2015 \u30ea\u30d3\u30b8\u30e7\u30f3\u306e\u9078\u629e\u6761\u4ef6"
::msgcat::mcset ja "View Name" "\u30d3\u30e5\u30fc\u540d:"
::msgcat::mcset ja "Apply (F5)" "\u9069\u7528 (F5)"
::msgcat::mcset ja "Error in commit selection arguments:" "\u30b3\u30df\u30c3\u30c8\u9078\u629e\u5f15\u6570\u306e\u30a8\u30e9\u30fc:"
::msgcat::mcset ja "None" "\u7121\u3057"
::msgcat::mcset ja "Descendant" "\u5b50\u5b6b"
::msgcat::mcset ja "Not descendant" "\u975e\u5b50\u5b6b"
::msgcat::mcset ja "Ancestor" "\u7956\u5148"
::msgcat::mcset ja "Not ancestor" "\u975e\u7956\u5148"
::msgcat::mcset ja "Local changes checked in to index but not committed" "\u30b9\u30c6\u30fc\u30b8\u3055\u308c\u305f\u3001\u30b3\u30df\u30c3\u30c8\u524d\u306e\u30ed\u30fc\u30ab\u30eb\u306a\u5909\u66f4"
::msgcat::mcset ja "Local uncommitted changes, not checked in to index" "\u30b9\u30c6\u30fc\u30b8\u3055\u308c\u3066\u3044\u306a\u3044\u3001\u30b3\u30df\u30c3\u30c8\u524d\u306e\u30ed\u30fc\u30ab\u30eb\u306a\u5909\u66f4"
::msgcat::mcset ja "and many more" "\u4ed6\u591a\u6570"
::msgcat::mcset ja "many" "\u591a\u6570"
::msgcat::mcset ja "Tags:" "\u30bf\u30b0:"
::msgcat::mcset ja "Parent" "\u89aa"
::msgcat::mcset ja "Child" "\u5b50"
::msgcat::mcset ja "Branch" "\u30d6\u30e9\u30f3\u30c1"
::msgcat::mcset ja "Follows" "\u4e0b\u4f4d"
::msgcat::mcset ja "Precedes" "\u4e0a\u4f4d"
::msgcat::mcset ja "Error getting diffs: %s" "diff\u53d6\u5f97\u30a8\u30e9\u30fc: %s"
::msgcat::mcset ja "Goto:" "Goto:"
::msgcat::mcset ja "Short SHA1 id %s is ambiguous" "%s \u3092\u542b\u3080 SHA1 ID \u306f\u8907\u6570\u5b58\u5728\u3057\u307e\u3059"
::msgcat::mcset ja "Revision %s is not known" "\u30ea\u30d3\u30b8\u30e7\u30f3 %s \u306f\u4e0d\u660e\u3067\u3059"
::msgcat::mcset ja "SHA1 id %s is not known" "SHA1 id %s \u306f\u4e0d\u660e\u3067\u3059"
::msgcat::mcset ja "Revision %s is not in the current view" "\u30ea\u30d3\u30b8\u30e7\u30f3 %s \u306f\u73fe\u5728\u306e\u30d3\u30e5\u30fc\u306b\u306f\u3042\u308a\u307e\u305b\u3093"
::msgcat::mcset ja "Date" "\u65e5\u4ed8"
::msgcat::mcset ja "Children" "\u5b50"
::msgcat::mcset ja "Reset %s branch to here" "%s \u30d6\u30e9\u30f3\u30c1\u3092\u3053\u3053\u306b\u30ea\u30bb\u30c3\u30c8\u3059\u308b"
::msgcat::mcset ja "Detached head: can't reset" "\u5207\u308a\u96e2\u3055\u308c\u305fHEAD: \u30ea\u30bb\u30c3\u30c8\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "Skipping merge commit " "\u30b3\u30df\u30c3\u30c8\u30de\u30fc\u30b8\u3092\u30b9\u30ad\u30c3\u30d7: "
::msgcat::mcset ja "Error getting patch ID for " "\u30d1\u30c3\u30c1\u53d6\u5f97\u30a8\u30e9\u30fc: ID "
::msgcat::mcset ja " - stopping\n" " - \u505c\u6b62\n"
::msgcat::mcset ja "Commit " "\u30b3\u30df\u30c3\u30c8 "
::msgcat::mcset ja " is the same patch as\n       " " \u306f\u4e0b\u8a18\u306e\u30d1\u30c3\u30c1\u3068\u540c\u7b49\n       "
::msgcat::mcset ja " differs from\n       " " \u4e0b\u8a18\u304b\u3089\u306ediff\n       "
::msgcat::mcset ja "Diff of commits:\n\n" "\u30b3\u30df\u30c3\u30c8\u306ediff:\n\n"
::msgcat::mcset ja " has %s children - stopping\n" " \u306b\u306f %s \u306e\u5b50\u304c\u3042\u308a\u307e\u3059 - \u505c\u6b62\n"
::msgcat::mcset ja "Error writing commit to file: %s" "\u30d5\u30a1\u30a4\u30eb\u3078\u306e\u30b3\u30df\u30c3\u30c8\u66f8\u304d\u51fa\u3057\u30a8\u30e9\u30fc: %s"
::msgcat::mcset ja "Error diffing commits: %s" "\u30b3\u30df\u30c3\u30c8\u306ediff\u5b9f\u884c\u30a8\u30e9\u30fc: %s"
::msgcat::mcset ja "Top" "Top"
::msgcat::mcset ja "From" "From"
::msgcat::mcset ja "To" "To"
::msgcat::mcset ja "Generate patch" "\u30d1\u30c3\u30c1\u751f\u6210"
::msgcat::mcset ja "From:" "From:"
::msgcat::mcset ja "To:" "To:"
::msgcat::mcset ja "Reverse" "\u9006"
::msgcat::mcset ja "Output file:" "\u51fa\u529b\u30d5\u30a1\u30a4\u30eb:"
::msgcat::mcset ja "Generate" "\u751f\u6210"
::msgcat::mcset ja "Error creating patch:" "\u30d1\u30c3\u30c1\u751f\u6210\u30a8\u30e9\u30fc:"
::msgcat::mcset ja "ID:" "ID:"
::msgcat::mcset ja "Tag name:" "\u30bf\u30b0\u540d:"
::msgcat::mcset ja "Tag message is optional" "\u30bf\u30b0\u30e1\u30c3\u30bb\u30fc\u30b8\u3092\u4ed8\u3051\u308b\u4e8b\u3082\u51fa\u6765\u307e\u3059"
::msgcat::mcset ja "Tag message:" "\u30bf\u30b0\u30e1\u30c3\u30bb\u30fc\u30b8:"
::msgcat::mcset ja "Create" "\u751f\u6210"
::msgcat::mcset ja "No tag name specified" "\u30bf\u30b0\u306e\u540d\u79f0\u304c\u6307\u5b9a\u3055\u308c\u3066\u3044\u307e\u305b\u3093"
::msgcat::mcset ja "Tag \"%s\" already exists" "\u30bf\u30b0 \"%s\" \u306f\u65e2\u306b\u5b58\u5728\u3057\u307e\u3059"
::msgcat::mcset ja "Error creating tag:" "\u30bf\u30b0\u751f\u6210\u30a8\u30e9\u30fc:"
::msgcat::mcset ja "Command:" "\u30b3\u30de\u30f3\u30c9:"
::msgcat::mcset ja "Write" "\u66f8\u304d\u51fa\u3057"
::msgcat::mcset ja "Error writing commit:" "\u30b3\u30df\u30c3\u30c8\u66f8\u304d\u51fa\u3057\u30a8\u30e9\u30fc:"
::msgcat::mcset ja "Name:" "\u540d\u524d:"
::msgcat::mcset ja "Please specify a name for the new branch" "\u65b0\u3057\u3044\u30d6\u30e9\u30f3\u30c1\u306e\u540d\u524d\u3092\u6307\u5b9a\u3057\u3066\u304f\u3060\u3055\u3044"
::msgcat::mcset ja "Branch '%s' already exists. Overwrite?" "\u30d6\u30e9\u30f3\u30c1 '%s' \u306f\u65e2\u306b\u5b58\u5728\u3057\u307e\u3059\u3002\u4e0a\u66f8\u304d\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Commit %s is already included in branch %s -- really re-apply it?" "\u30b3\u30df\u30c3\u30c8 %s \u306f\u65e2\u306b\u30d6\u30e9\u30f3\u30c1 %s \u306b\u542b\u307e\u308c\u3066\u3044\u307e\u3059 \u2015 \u672c\u5f53\u306b\u3053\u308c\u3092\u518d\u9069\u7528\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Cherry-picking" "\u30c1\u30a7\u30ea\u30fc\u30d4\u30c3\u30af\u4e2d"
::msgcat::mcset ja "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "\u30d5\u30a1\u30a4\u30eb '%s' \u306e\u30ed\u30fc\u30ab\u30eb\u306a\u5909\u66f4\u306e\u305f\u3081\u306b\u30c1\u30a7\u30ea\u30fc\u30d4\u30c3\u30af\u306f\u5931\u6557\u3057\u307e\u3057\u305f\u3002\n\u3042\u306a\u305f\u306e\u5909\u66f4\u306b commit, reset, stash \u306e\u3044\u305a\u308c\u304b\u3092\u884c\u3063\u3066\u304b\u3089\u3084\u308a\u76f4\u3057\u3066\u304f\u3060\u3055\u3044\u3002"
::msgcat::mcset ja "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "\u30de\u30fc\u30b8\u306e\u885d\u7a81\u306b\u3088\u3063\u3066\u30c1\u30a7\u30ea\u30fc\u30d4\u30c3\u30af\u306f\u5931\u6557\u3057\u307e\u3057\u305f\u3002\n\u3053\u306e\u89e3\u6c7a\u306e\u305f\u3081\u306b git citool \u3092\u5b9f\u884c\u3057\u305f\u3044\u3067\u3059\u304b\uff1f"
::msgcat::mcset ja "No changes committed" "\u4f55\u306e\u5909\u66f4\u3082\u30b3\u30df\u30c3\u30c8\u3055\u308c\u3066\u3044\u307e\u305b\u3093"
::msgcat::mcset ja "Commit %s is not included in branch %s -- really revert it?" "\u30b3\u30df\u30c3\u30c8 %s \u306f\u65e2\u306b\u30d6\u30e9\u30f3\u30c1 %s \u306b\u542b\u307e\u308c\u3066\u3044\u307e\u3059 \u2015 \u672c\u5f53\u306b\u3053\u308c\u3092\u64a4\u56de\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Reverting" "\u64a4\u56de\u4e2d"
::msgcat::mcset ja "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "\u30d5\u30a1\u30a4\u30eb '%s' \u306e\u30ed\u30fc\u30ab\u30eb\u306a\u5909\u66f4\u306e\u305f\u3081\u306b\u64a4\u56de\u306f\u5931\u6557\u3057\u307e\u3057\u305f\u3002 \u3042\u306a\u305f\u306e\u5909\u66f4\u306b commit, reset, stash \u306e\u3044\u305a\u308c\u304b\u3092\u884c\u3063\u3066\u304b\u3089\u3084\u308a\u76f4\u3057\u3066\u304f\u3060\u3055\u3044\u3002"
::msgcat::mcset ja "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "\u30de\u30fc\u30b8\u306e\u885d\u7a81\u306b\u3088\u3063\u3066\u64a4\u56de\u306f\u5931\u6557\u3057\u307e\u3057\u305f\u3002\n\u3053\u306e\u89e3\u6c7a\u306e\u305f\u3081\u306b git citool \u3092\u5b9f\u884c\u3057\u305f\u3044\u3067\u3059\u304b\uff1f"
::msgcat::mcset ja "Confirm reset" "\u78ba\u8a8d\u3092\u53d6\u308a\u6d88\u3059"
::msgcat::mcset ja "Reset branch %s to %s?" "\u30d6\u30e9\u30f3\u30c1 %s \u3092 %s \u306b\u30ea\u30bb\u30c3\u30c8\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Reset type:" "Reset \u30bf\u30a4\u30d7:"
::msgcat::mcset ja "Soft: Leave working tree and index untouched" "Soft: \u4f5c\u696d\u30c4\u30ea\u30fc\u3082\u30a4\u30f3\u30c7\u30c3\u30af\u30b9\u3082\u305d\u306e\u307e\u307e\u306b\u3059\u308b"
::msgcat::mcset ja "Mixed: Leave working tree untouched, reset index" "Mixed: \u4f5c\u696d\u30c4\u30ea\u30fc\u3092\u305d\u306e\u307e\u307e\u306b\u3057\u3066\u3001\u30a4\u30f3\u30c7\u30c3\u30af\u30b9\u3092\u30ea\u30bb\u30c3\u30c8"
::msgcat::mcset ja "Hard: Reset working tree and index\n(discard ALL local changes)" "Hard: \u4f5c\u696d\u30c4\u30ea\u30fc\u3084\u30a4\u30f3\u30c7\u30c3\u30af\u30b9\u3092\u30ea\u30bb\u30c3\u30c8\n\uff08\u300c\u5168\u3066\u306e\u300d\u30ed\u30fc\u30ab\u30eb\u306a\u5909\u66f4\u3092\u7834\u68c4\uff09"
::msgcat::mcset ja "Resetting" "\u30ea\u30bb\u30c3\u30c8\u4e2d"
::msgcat::mcset ja "Checking out" "\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8"
::msgcat::mcset ja "Cannot delete the currently checked-out branch" "\u73fe\u5728\u30c1\u30a7\u30c3\u30af\u30a2\u30a6\u30c8\u3055\u308c\u3066\u3044\u308b\u30d6\u30e9\u30f3\u30c1\u3092\u524a\u9664\u3059\u308b\u3053\u3068\u306f\u3067\u304d\u307e\u305b\u3093"
::msgcat::mcset ja "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "\u30d6\u30e9\u30f3\u30c1 %s \u306b\u306f\u4ed6\u306e\u30d6\u30e9\u30f3\u30c1\u306b\u5b58\u5728\u3057\u306a\u3044\u30b3\u30df\u30c3\u30c8\u304c\u3042\u308a\u307e\u3059\u3002\n\u672c\u5f53\u306b\u30d6\u30e9\u30f3\u30c1 %s \u3092\u524a\u9664\u3057\u307e\u3059\u304b\uff1f"
::msgcat::mcset ja "Tags and heads: %s" "\u30bf\u30b0\u3068HEAD: %s"
::msgcat::mcset ja "Filter" "\u30d5\u30a3\u30eb\u30bf\u30fc"
::msgcat::mcset ja "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "\u30b3\u30df\u30c3\u30c8\u69cb\u9020\u60c5\u5831\u8aad\u307f\u8fbc\u307f\u30a8\u30e9\u30fc; \u30d6\u30e9\u30f3\u30c1\u53ca\u3073\u4e0a\u4f4d/\u4e0b\u4f4d\u306e\u30bf\u30b0\u60c5\u5831\u304c\u4e0d\u5b8c\u5168\u3067\u3042\u308b\u3088\u3046\u3067\u3059\u3002"
::msgcat::mcset ja "Tag" "\u30bf\u30b0"
::msgcat::mcset ja "Id" "ID"
::msgcat::mcset ja "Gitk font chooser" "Gitk \u30d5\u30a9\u30f3\u30c8\u9078\u629e"
::msgcat::mcset ja "B" "B"
::msgcat::mcset ja "I" "I"
::msgcat::mcset ja "Commit list display options" "\u30b3\u30df\u30c3\u30c8\u30ea\u30b9\u30c8\u8868\u793a\u30aa\u30d7\u30b7\u30e7\u30f3"
::msgcat::mcset ja "Maximum graph width (lines)" "\u6700\u5927\u30b0\u30e9\u30d5\u5e45\uff08\u7dda\u306e\u672c\u6570\uff09"
::msgcat::mcset ja "Maximum graph width (% of pane)" "\u6700\u5927\u30b0\u30e9\u30d5\u5e45\uff08\u30da\u30a4\u30f3\u306b\u5bfe\u3059\u308b\uff05\uff09"
::msgcat::mcset ja "Show local changes" "\u30ed\u30fc\u30ab\u30eb\u306a\u5909\u66f4\u3092\u8868\u793a"
::msgcat::mcset ja "Auto-select SHA1 (length)" "SHA1 \u306e\u81ea\u52d5\u9078\u629e (\u9078\u629e\u6587\u5b57\u6570\u6307\u5b9a)"
::msgcat::mcset ja "Hide remote refs" "\u30ea\u30e2\u30fc\u30c8\u30ea\u30d5\u30a1\u30ec\u30f3\u30b9\u3092\u96a0\u3059"
::msgcat::mcset ja "Diff display options" "diff\u8868\u793a\u30aa\u30d7\u30b7\u30e7\u30f3"
::msgcat::mcset ja "Tab spacing" "\u30bf\u30d6\u7a7a\u767d\u5e45"
::msgcat::mcset ja "Display nearby tags/heads" "\u8fd1\u304f\u306e \u30bf\u30b0/head \u3092\u8868\u793a\u3059\u308b"
::msgcat::mcset ja "Maximum # tags/heads to show" "\u30bf\u30b0/head \u306e\u6700\u5927\u8868\u793a\u6570"
::msgcat::mcset ja "Limit diffs to listed paths" "diff \u3092\u30ea\u30b9\u30c8\u306e\u30d1\u30b9\u306b\u5236\u9650"
::msgcat::mcset ja "Support per-file encodings" "\u30d5\u30a1\u30a4\u30eb\u3054\u3068\u306e\u30a8\u30f3\u30b3\u30fc\u30c7\u30a3\u30f3\u30b0\u306e\u30b5\u30dd\u30fc\u30c8"
::msgcat::mcset ja "External diff tool" "\u5916\u90e8diff\u30c4\u30fc\u30eb"
::msgcat::mcset ja "Choose..." "\u9078\u629e..."
::msgcat::mcset ja "General options" "\u5168\u4f53\u8a2d\u5b9a"
::msgcat::mcset ja "Use themed widgets" "\u30c6\u30fc\u30de\u30a6\u30a3\u30b8\u30a7\u30c3\u30c8\u3092\u4f7f\u7528\u3059\u308b"
::msgcat::mcset ja "(change requires restart)" "(\u5909\u66f4\u306b\u306f\u518d\u8d77\u52d5\u304c\u5fc5\u8981\u3067\u3059)"
::msgcat::mcset ja "(currently unavailable)" "(\u73fe\u5728\u306f\u4f7f\u7528\u51fa\u6765\u307e\u305b\u3093)"
::msgcat::mcset ja "Colors: press to choose" "\u8272: \u30dc\u30bf\u30f3\u3092\u62bc\u3057\u3066\u9078\u629e"
::msgcat::mcset ja "Interface" "\u30a4\u30f3\u30bf\u30fc\u30d5\u30a7\u30a4\u30b9"
::msgcat::mcset ja "interface" "\u30a4\u30f3\u30bf\u30fc\u30d5\u30a7\u30a4\u30b9"
::msgcat::mcset ja "Background" "\u80cc\u666f"
::msgcat::mcset ja "background" "\u80cc\u666f"
::msgcat::mcset ja "Foreground" "\u524d\u666f"
::msgcat::mcset ja "foreground" "\u524d\u666f"
::msgcat::mcset ja "Diff: old lines" "Diff: \u65e7\u30d0\u30fc\u30b8\u30e7\u30f3"
::msgcat::mcset ja "diff old lines" "diff \u65e7\u30d0\u30fc\u30b8\u30e7\u30f3"
::msgcat::mcset ja "Diff: new lines" "Diff: \u65b0\u30d0\u30fc\u30b8\u30e7\u30f3"
::msgcat::mcset ja "diff new lines" "diff \u65b0\u30d0\u30fc\u30b8\u30e7\u30f3"
::msgcat::mcset ja "Diff: hunk header" "Diff: hunk\u30d8\u30c3\u30c0"
::msgcat::mcset ja "diff hunk header" "diff hunk\u30d8\u30c3\u30c0"
::msgcat::mcset ja "Marked line bg" "\u30de\u30fc\u30af\u884c\u306e\u80cc\u666f"
::msgcat::mcset ja "marked line background" "\u30de\u30fc\u30af\u884c\u306e\u80cc\u666f"
::msgcat::mcset ja "Select bg" "\u9078\u629e\u306e\u80cc\u666f"
::msgcat::mcset ja "Fonts: press to choose" "\u30d5\u30a9\u30f3\u30c8: \u30dc\u30bf\u30f3\u3092\u62bc\u3057\u3066\u9078\u629e"
::msgcat::mcset ja "Main font" "\u4e3b\u30d5\u30a9\u30f3\u30c8"
::msgcat::mcset ja "Diff display font" "Diff\u8868\u793a\u7528\u30d5\u30a9\u30f3\u30c8"
::msgcat::mcset ja "User interface font" "UI\u7528\u30d5\u30a9\u30f3\u30c8"
::msgcat::mcset ja "Gitk preferences" "Gitk \u8a2d\u5b9a"
::msgcat::mcset ja "General" "\u4e00\u822c"
::msgcat::mcset ja "Colors" "\u8272"
::msgcat::mcset ja "Fonts" "\u30d5\u30a9\u30f3\u30c8"
::msgcat::mcset ja "Gitk: choose color for %s" "Gitk: \u300c%s\u300d \u306e\u8272\u3092\u9078\u629e"
::msgcat::mcset ja "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "\u7533\u3057\u8a33\u3042\u308a\u307e\u305b\u3093\u304c\u3001\u3053\u306e\u30d0\u30fc\u30b8\u30e7\u30f3\u306e Tcl/Tk \u3067\u306f gitk \u3092\u5b9f\u884c\u51fa\u6765\u307e\u305b\u3093\u3002\nGitk\u306e\u5b9f\u884c\u306b\u306f Tcl/Tk 8.4 \u4ee5\u4e0a\u304c\u5fc5\u8981\u3067\u3059\u3002"
::msgcat::mcset ja "Cannot find a git repository here." "\u3053\u3053\u306b\u306fgit\u30ea\u30dd\u30b8\u30c8\u30ea\u304c\u3042\u308a\u307e\u305b\u3093\u3002"
::msgcat::mcset ja "Ambiguous argument '%s': both revision and filename" "\u3042\u3044\u307e\u3044\u306a\u5f15\u6570 '%s': \u30ea\u30d3\u30b8\u30e7\u30f3\u3068\u30d5\u30a1\u30a4\u30eb\u540d\u306e\u4e21\u65b9\u306b\u89e3\u91c8\u3067\u304d\u307e\u3059"
::msgcat::mcset ja "Bad arguments to gitk:" "gitk\u3078\u306e\u4e0d\u6b63\u306a\u5f15\u6570:"
