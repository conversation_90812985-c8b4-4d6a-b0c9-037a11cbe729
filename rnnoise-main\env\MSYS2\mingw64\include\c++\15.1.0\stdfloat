// <stdfloat> -*- C++ -*-

// Copyright (C) 2022-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/stdfloat
 *  This is a Standard C++ Library header.
 */

#ifndef _GLIBCXX_STDFLOAT
#define _GLIBCXX_STDFLOAT 1

#if __cplusplus > 202002L
#include <bits/c++config.h>

namespace std
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  #ifdef __STDCPP_FLOAT16_T__
  using float16_t = _Float16;
  #endif

  #ifdef __STDCPP_FLOAT32_T__
  using float32_t = _Float32;
  #endif

  #ifdef __STDCPP_FLOAT64_T__
  using float64_t = _Float64;
  #endif

  #ifdef __STDCPP_FLOAT128_T__
  using float128_t = _Float128;
  #endif

  #ifdef __STDCPP_BFLOAT16_T__
  using bfloat16_t = __gnu_cxx::__bfloat16_t;
  #endif

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace std
#endif // C++23
#endif // _GLIBCXX_STDFLOAT
