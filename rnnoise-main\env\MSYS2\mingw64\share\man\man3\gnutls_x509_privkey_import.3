.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_import" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_import \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_import(gnutls_x509_privkey_t " key ", const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " format ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
The data to store the parsed key
.IP "const gnutls_datum_t * data" 12
The DER or PEM encoded certificate.
.IP "gnutls_x509_crt_fmt_t format" 12
One of DER or PEM
.SH "DESCRIPTION"
This function will convert the given DER or PEM encoded key to the
native \fBgnutls_x509_privkey_t\fP format. The output will be stored in
 \fIkey\fP .

If the key is PEM encoded it should have a header that contains "PRIVATE
KEY". Note that this function falls back to PKCS \fB8\fP decoding without
password, if the default format fails to import.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
