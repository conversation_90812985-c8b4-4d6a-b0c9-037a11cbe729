/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#define categoryHeader __MSABI_LONG(0x00000001)
#define categoryUnused __MSABI_LONG(0x00000002)
#define categoryGeneral __MSABI_LONG(0x00000003)
#define CDO_E_UNCAUGHT_EXCEPTION __MSABI_LONG(0x80040201)
#define CDO_E_NOT_OPENED __MSABI_LONG(0x80040202)
#define CDO_E_UNSUPPORTED_DATASOURCE __MSABI_LONG(0x80040203)
#define CDO_E_INVALID_PROPERTYNAME __MSABI_LONG(0x80040204)
#define CDO_E_PROP_UNSUPPORTED __MSABI_LONG(0x80040205)
#define CDO_E_INACTIVE __MSABI_LONG(0x80040206)
#define CDO_E_NO_SUPPORT_FOR_OBJECTS __MSABI_LONG(0x80040207)
#define CDO_E_NOT_AVAILABLE __MSABI_LONG(0x80040208)
#define CDO_E_NO_DEFAULT_DROP_DIR __MSABI_LONG(0x80040209)
#define CDO_E_SMTP_SERVER_REQUIRED __MSABI_LONG(0x8004020A)
#define CDO_E_NNTP_SERVER_REQUIRED __MSABI_LONG(0x8004020B)
#define CDO_E_RECIPIENT_MISSING __MSABI_LONG(0x8004020C)
#define CDO_E_FROM_MISSING __MSABI_LONG(0x8004020D)
#define CDO_E_SENDER_REJECTED __MSABI_LONG(0x8004020E)
#define CDO_E_RECIPIENTS_REJECTED __MSABI_LONG(0x8004020F)
#define CDO_E_NNTP_POST_FAILED __MSABI_LONG(0x80040210)
#define CDO_E_SMTP_SEND_FAILED __MSABI_LONG(0x80040211)
#define CDO_E_CONNECTION_DROPPED __MSABI_LONG(0x80040212)
#define CDO_E_FAILED_TO_CONNECT __MSABI_LONG(0x80040213)
#define CDO_E_INVALID_POST __MSABI_LONG(0x80040214)
#define CDO_E_AUTHENTICATION_FAILURE __MSABI_LONG(0x80040215)
#define CDO_E_INVALID_CONTENT_TYPE __MSABI_LONG(0x80040216)
#define CDO_E_LOGON_FAILURE __MSABI_LONG(0x80040217)
#define CDO_E_HTTP_NOT_FOUND __MSABI_LONG(0x80040218)
#define CDO_E_HTTP_FORBIDDEN __MSABI_LONG(0x80040219)
#define CDO_E_HTTP_FAILED __MSABI_LONG(0x8004021A)
#define CDO_E_MULTIPART_NO_DATA __MSABI_LONG(0x8004021B)
#define CDO_E_INVALID_ENCODING_FOR_MULTIPART __MSABI_LONG(0x8004021C)
#define CDO_E_UNSAFE_OPERATION __MSABI_LONG(0x8004021D)
#define CDO_E_PROP_NOT_FOUND __MSABI_LONG(0x8004021E)
#define CDO_E_INVALID_SEND_OPTION __MSABI_LONG(0x80040220)
#define CDO_E_INVALID_POST_OPTION __MSABI_LONG(0x80040221)
#define CDO_E_NO_PICKUP_DIR __MSABI_LONG(0x80040222)
#define CDO_E_NOT_ALL_DELETED __MSABI_LONG(0x80040223)
#define CDO_E_NO_METHOD __MSABI_LONG(0x80040224)
#define CDO_E_PROP_READONLY __MSABI_LONG(0x80040227)
#define CDO_E_PROP_CANNOT_DELETE __MSABI_LONG(0x80040228)
#define CDO_E_BAD_DATA __MSABI_LONG(0x80040229)
#define CDO_E_PROP_NONHEADER __MSABI_LONG(0x8004022A)
#define CDO_E_INVALID_CHARSET __MSABI_LONG(0x8004022B)
#define CDO_E_ADOSTREAM_NOT_BOUND __MSABI_LONG(0x8004022C)
#define CDO_E_CONTENTPROPXML_NOT_FOUND __MSABI_LONG(0x8004022D)
#define CDO_E_CONTENTPROPXML_WRONG_CHARSET __MSABI_LONG(0x8004022E)
#define CDO_E_CONTENTPROPXML_PARSE_FAILED __MSABI_LONG(0x8004022F)
#define CDO_E_CONTENTPROPXML_CONVERT_FAILED __MSABI_LONG(0x80040230)
#define CDO_E_NO_DIRECTORIES_SPECIFIED __MSABI_LONG(0x80040231)
#define CDO_E_DIRECTORIES_UNREACHABLE __MSABI_LONG(0x80040232)
#define CDO_E_BAD_SENDER __MSABI_LONG(0x80040233)
#define CDO_E_SELF_BINDING __MSABI_LONG(0x80040234)
#define CDO_E_BAD_ATTENDEE_DATA __MSABI_LONG(0x80040235)
#define CDO_E_ARGUMENT1 __MSABI_LONG(0x80044000)
#define CDO_E_ARGUMENT2 __MSABI_LONG(0x80044001)
#define CDO_E_ARGUMENT3 __MSABI_LONG(0x80044002)
#define CDO_E_ARGUMENT4 __MSABI_LONG(0x80044003)
#define CDO_E_ARGUMENT5 __MSABI_LONG(0x80044004)
#define CDO_E_NOT_FOUND __MSABI_LONG(0x800CCE05)
#define CDO_E_INVALID_ENCODING_TYPE __MSABI_LONG(0x800CCE1D)
#define IDS_ORIGINAL_MESSAGE __MSABI_LONG(0x00011000)
#define IDS_FROM __MSABI_LONG(0x00011001)
#define IDS_SENT __MSABI_LONG(0x00011002)
#define IDS_POSTED_AT __MSABI_LONG(0x00011003)
#define IDS_TO __MSABI_LONG(0x00011004)
#define IDS_CC __MSABI_LONG(0x00011005)
#define IDS_POSTED_TO __MSABI_LONG(0x00011006)
#define IDS_CONVERSATION __MSABI_LONG(0x00011007)
#define IDS_SUBJECT __MSABI_LONG(0x00011008)
#define IDS_IMPORTANCE __MSABI_LONG(0x00011009)
#define IDS_ON_BEHALF_OF __MSABI_LONG(0x0001100A)
#define IDS_FW __MSABI_LONG(0x0001100B)
#define IDS_RE __MSABI_LONG(0x0001100C)
#define IDS_CODEPAGE __MSABI_LONG(0x0001100D)

#ifdef CDOSVR
#define IDS_CalendarFolder __MSABI_LONG(0x0001100E)
#define IDS_ContactsFolder __MSABI_LONG(0x0001100F)
#define IDS_DraftsFolder __MSABI_LONG(0x00011010)
#define IDS_JournalFolder __MSABI_LONG(0x00011011)
#define IDS_NotesFolder __MSABI_LONG(0x00011012)
#define IDS_TasksFolder __MSABI_LONG(0x00011013)
#endif

#define IDS_NewFolder __MSABI_LONG(0x00011014)
#define IDS_Location __MSABI_LONG(0x00011015)
#define IDS_StartTime __MSABI_LONG(0x00011016)
#define IDS_EndTime __MSABI_LONG(0x00011017)
#define IDS_TimeZone __MSABI_LONG(0x00011018)
#define IDS_LocalTime __MSABI_LONG(0x00011019)
#define IDS_Organizer __MSABI_LONG(0x0001101A)
#define IDS_ApptType __MSABI_LONG(0x0001101B)
#define IDS_SingleAppt __MSABI_LONG(0x0001101C)
#define IDS_SingleMtg __MSABI_LONG(0x0001101D)
#define IDS_RecurAppt __MSABI_LONG(0x0001101E)
#define IDS_RecurMtg __MSABI_LONG(0x0001101F)
#define IDS_Universal __MSABI_LONG(0x00011100)
#define IDS_Greenwich __MSABI_LONG(0x00011101)
#define IDS_Sarajevo __MSABI_LONG(0x00011102)
#define IDS_Paris __MSABI_LONG(0x00011103)
#define IDS_Berlin __MSABI_LONG(0x00011104)
#define IDS_EasternEurope __MSABI_LONG(0x00011105)
#define IDS_Prague __MSABI_LONG(0x00011106)
#define IDS_Athens __MSABI_LONG(0x00011107)
#define IDS_Brasilia __MSABI_LONG(0x00011108)
#define IDS_Atlantic __MSABI_LONG(0x00011109)
#define IDS_Eastern __MSABI_LONG(0x0001110A)
#define IDS_Central __MSABI_LONG(0x0001110B)
#define IDS_Mountain __MSABI_LONG(0x0001110C)
#define IDS_Pacific __MSABI_LONG(0x0001110D)
#define IDS_Alaska __MSABI_LONG(0x0001110E)
#define IDS_Hawaii __MSABI_LONG(0x0001110F)
#define IDS_Midway __MSABI_LONG(0x00011110)
#define IDS_Wellington __MSABI_LONG(0x00011111)
#define IDS_Brisbane __MSABI_LONG(0x00011112)
#define IDS_Adelaide __MSABI_LONG(0x00011113)
#define IDS_Tokyo __MSABI_LONG(0x00011114)
#define IDS_Singapore __MSABI_LONG(0x00011115)
#define IDS_Bangkok __MSABI_LONG(0x00011116)
#define IDS_Bombay __MSABI_LONG(0x00011117)
#define IDS_AbuDhabi __MSABI_LONG(0x00011118)
#define IDS_Tehran __MSABI_LONG(0x00011119)
#define IDS_Baghdad __MSABI_LONG(0x0001111A)
#define IDS_Israel __MSABI_LONG(0x0001111B)
#define IDS_Newfoundland __MSABI_LONG(0x0001111C)
#define IDS_Azores __MSABI_LONG(0x0001111D)
#define IDS_MidAtlantic __MSABI_LONG(0x0001111E)
#define IDS_Monrovia __MSABI_LONG(0x0001111F)
#define IDS_BuenosAires __MSABI_LONG(0x00011120)
#define IDS_Caracas __MSABI_LONG(0x00011121)
#define IDS_Indiana __MSABI_LONG(0x00011122)
#define IDS_Bogota __MSABI_LONG(0x00011123)
#define IDS_Saskatchewan __MSABI_LONG(0x00011124)
#define IDS_Mexico __MSABI_LONG(0x00011125)
#define IDS_Arizona __MSABI_LONG(0x00011126)
#define IDS_Eniwetok __MSABI_LONG(0x00011127)
#define IDS_Fiji __MSABI_LONG(0x00011128)
#define IDS_Magadan __MSABI_LONG(0x00011129)
#define IDS_Hobart __MSABI_LONG(0x0001112A)
#define IDS_Guam __MSABI_LONG(0x0001112B)
#define IDS_Darwin __MSABI_LONG(0x0001112C)
#define IDS_Beijing __MSABI_LONG(0x0001112D)
#define IDS_Almaty __MSABI_LONG(0x0001112E)
#define IDS_Islamabad __MSABI_LONG(0x0001112F)
#define IDS_Kabul __MSABI_LONG(0x00011130)
#define IDS_Cairo __MSABI_LONG(0x00011131)
#define IDS_Harare __MSABI_LONG(0x00011132)
#define IDS_Moscow __MSABI_LONG(0x00011133)
#define IDS_CapeVerde __MSABI_LONG(0x00011134)
#define IDS_Caucasus __MSABI_LONG(0x00011135)
#define IDS_CentralAmerica __MSABI_LONG(0x00011136)
#define IDS_EastAfrica __MSABI_LONG(0x00011137)
#define IDS_Melbourne __MSABI_LONG(0x00011138)
#define IDS_Ekaterinburg __MSABI_LONG(0x00011139)
#define IDS_Helsinki __MSABI_LONG(0x0001113A)
#define IDS_Greenland __MSABI_LONG(0x0001113B)
#define IDS_Rangoon __MSABI_LONG(0x0001113C)
#define IDS_Nepal __MSABI_LONG(0x0001113D)
#define IDS_Irkutsk __MSABI_LONG(0x0001113E)
#define IDS_Krasnoyarsk __MSABI_LONG(0x0001113F)
#define IDS_Santiago __MSABI_LONG(0x00011140)
#define IDS_SriLanka __MSABI_LONG(0x00011141)
#define IDS_Tonga __MSABI_LONG(0x00011142)
#define IDS_Vladivostok __MSABI_LONG(0x00011143)
#define IDS_WestCentralAfrica __MSABI_LONG(0x00011144)
#define IDS_Yakutsk __MSABI_LONG(0x00011145)
#define IDS_Dhaka __MSABI_LONG(0x00011146)
#define IDS_Seoul __MSABI_LONG(0x00011147)
#define IDS_Perth __MSABI_LONG(0x00011148)
#define IDS_Arab __MSABI_LONG(0x00011149)
#define IDS_Taipei __MSABI_LONG(0x0001114A)
#define IDS_Sydney2000 __MSABI_LONG(0x0001114B)

#ifdef CDOSVR
#define evtMethodCalled __MSABI_LONG(0x00032000)
#define evtMethodReturning __MSABI_LONG(0x00032001)
#define evtIsAborting __MSABI_LONG(0xC0032002)
#define evtExpansionInitialized __MSABI_LONG(0x00032003)
#define evtExpansionUnInitialized __MSABI_LONG(0x00032004)
#define evtExpansionInitializeFailed __MSABI_LONG(0xC0032005)
#define evtExpansionRegisterFailed __MSABI_LONG(0xC0032006)
#define evtExpansionMessageSaveChangesFailed __MSABI_LONG(0xC0032007)
#define evtExpansionMessageDeleteFailed __MSABI_LONG(0xC0032008)
#define evtExpansionFolderSaveChangesFailed __MSABI_LONG(0xC0032009)
#define evtExpansionTooManyInstancesPerDay __MSABI_LONG(0x8003200A)
#define evtMailboxCreateTotalFailure __MSABI_LONG(0xC003200B)
#define evtMailboxCreatePartialFailure __MSABI_LONG(0xC003200C)
#define evtUninitImplRestFailed __MSABI_LONG(0xC003200D)
#define evtExpandSavingAppt __MSABI_LONG(0xC003200E)
#define evtExpandDeletingAppt __MSABI_LONG(0xC003200F)
#define evtExpandQuery __MSABI_LONG(0xC0032010)
#define evtExpandFolderSetProps __MSABI_LONG(0xC0032011)
#define evtRegistryFailure __MSABI_LONG(0xC0032012)
#define evtExpStat __MSABI_LONG(0xC0032013)
#define evtDumpFcn __MSABI_LONG(0xC0032014)
#define evtSaveDeleteFailFBUpdate __MSABI_LONG(0xC0032015)
#define evtProcessingQueryCallback __MSABI_LONG(0xC0032016)
#define evtMailboxLocalizeTotalFailure __MSABI_LONG(0xC0032017)
#define evtMailboxLocalizePartialFailure __MSABI_LONG(0xC0032018)
#define evtExpandMaster __MSABI_LONG(0xC0032019)
#define evtExpansionInit __MSABI_LONG(0xC003201A)
#define evtFBGenerateMsg __MSABI_LONG(0xC003201B)
#define evtExpansionInstExpiryInPublicMDB __MSABI_LONG(0x8003201C)
#define evtUnhandledExceptionInitialization __MSABI_LONG(0xC003201D)
#define evtUnhandledExceptionShutdown __MSABI_LONG(0xC003201E)
#define evtUnhandledExceptionInitializationMDB __MSABI_LONG(0xC003201F)
#define evtUnhandledExceptionShutdownMDB __MSABI_LONG(0xC0032020)
#define evtUnhandledExceptionMsgSaveChanges __MSABI_LONG(0xC0032021)
#define evtUnhandledExceptionDelete __MSABI_LONG(0xC0032022)
#define evtUnhandledExceptionQuery __MSABI_LONG(0xC0032023)
#define evtUnhandledExceptionFolderSaveChanges __MSABI_LONG(0xC0032024)
#define evtCorruptedCalendar __MSABI_LONG(0xC0032025)
#define evtRebuildCalendar __MSABI_LONG(0x80032026)
#define evtCheckPrimaryCalendar __MSABI_LONG(0x80032027)
#define evtExpandMasterPF __MSABI_LONG(0xC0032028)
#define evtCorruptedPFCalendar __MSABI_LONG(0xC0032029)
#define evtRebuildPFCalendar __MSABI_LONG(0x8003202A)
#define evtMovingMailboxCallbackFailed __MSABI_LONG(0x8003202B)
#endif
