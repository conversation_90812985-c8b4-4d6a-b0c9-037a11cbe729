.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_get_library_config" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_get_library_config \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const gnutls_library_config_st * gnutls_get_library_config( " void ");"
.SH ARGUMENTS
.IP " void" 12
.SH "DESCRIPTION"

Returns the library configuration as key value pairs.
Currently defined keys are:

\- fips\-module\-name: the name of the FIPS140 module

\- fips\-module\-version: the version of the FIPS140 module

\- libgnutls\-soname: the SONAME of the library itself

\- libnettle\-soname: the library SONAME of linked libnettle

\- libhogweed\-soname: the library SONAME of linked libhogweed

\- libgmp\-soname: the library SONAME of linked libgmp

\- hardware\-features: enabled hardware support features

\- tls\-features: enabled TLS protocol features
.SH "RETURNS"
a NUL\-terminated \fBgnutls_library_config_st\fP array
.SH "SINCE"
3.7.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
