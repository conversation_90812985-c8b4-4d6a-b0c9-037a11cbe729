+{
   locale_version => 1.31,
   entry => <<'ENTRY', # for DUCET v13.0.0
0063 0068 ; [.1FD7.0020.0002] # <LATIN SMALL LETTER C, LATIN SMALL LETTER H>
0063 0048 ; [.1FD7.0020.0007][.0000.0000.0002] # <LATIN SMALL LETTER C, LATIN CAPITAL LETTER H>
0043 0068 ; [.1FD7.0020.0007][.0000.0000.0008] # <LATIN CAPITAL LETTER C, LATIN SMALL LETTER H>
0043 0048 ; [.1FD7.0020.0008] # <LATIN CAPITAL LETTER C, LATIN CAPITAL LETTER H>
006C 006C ; [.20D7.0020.0002][.0000.0000.0001] # <LATIN SMALL LETTER L, LATIN SMALL LETTER L>
006C 00B7 006C ; [.20D7.0020.0002][.0000.0000.0007] # <LATIN SMALL LETTER L, MIDDLE DOT, LATIN SMALL LETTER L>
006C 004C ; [.20D7.0020.0007][.0000.0000.0002][.0000.0000.0001] # <LATIN SMALL LETTER L, LATIN CAPITAL LETTER L>
006C 00B7 004C ; [.20D7.0020.0007][.0000.0000.0002][.0000.0000.0007] # <LATIN SMALL LETTER L, MIDDLE DOT, LATIN CAPITAL LETTER L>
004C 006C ; [.20D7.0020.0007][.0000.0000.0008][.0000.0000.0001] # <LATIN CAPITAL LETTER L, LATIN SMALL LETTER L>
004C 00B7 006C ; [.20D7.0020.0007][.0000.0000.0008][.0000.0000.0007] # <LATIN CAPITAL LETTER L, MIDDLE DOT, LATIN SMALL LETTER L>
004C 004C ; [.20D7.0020.0008][.0000.0000.0001] # <LATIN CAPITAL LETTER L, LATIN CAPITAL LETTER L>
004C 00B7 004C ; [.20D7.0020.0008][.0000.0000.0007] # <LATIN CAPITAL LETTER L, MIDDLE DOT, LATIN CAPITAL LETTER L>
ENTRY
};
