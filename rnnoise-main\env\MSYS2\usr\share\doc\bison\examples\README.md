This directory contains examples of Bison grammar files, sorted per
language.

Several of them come from the documentation, which should be installed
together with Bison.  The URLs are provided for convenience.

These examples come with a README and a Makefile.  Not only can they be used
to toy with <PERSON><PERSON>, they can also be starting points for your own grammars.

Please, be sure to read the C examples before looking at the other
languages, as these examples are simpler.

<!---

Local Variables:
fill-column: 76
ispell-dictionary: "american"
End:

Copyright (C) 2018-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, with no Front-Cover Texts, and with no Back-Cover
Texts.  A copy of the license is included in the "GNU Free
Documentation License" file as part of this distribution.
--->
