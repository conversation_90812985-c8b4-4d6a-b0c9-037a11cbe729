.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_get_seed" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_get_seed \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_get_seed(gnutls_x509_privkey_t " key ", gnutls_digest_algorithm_t * " digest ", void * " seed ", size_t * " seed_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
should contain a \fBgnutls_x509_privkey_t\fP type
.IP "gnutls_digest_algorithm_t * digest" 12
if non\-NULL it will contain the digest algorithm used for key generation (if applicable)
.IP "void * seed" 12
where seed will be copied to
.IP "size_t * seed_size" 12
originally holds the size of  \fIseed\fP , will be updated with actual size
.SH "DESCRIPTION"
This function will return the seed that was used to generate the
given private key. That function will succeed only if the key was generated
as a provable key.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
