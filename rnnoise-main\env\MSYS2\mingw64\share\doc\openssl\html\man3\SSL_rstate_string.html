<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_rstate_string</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_rstate_string, SSL_rstate_string_long - get textual description of state of an SSL object during read operation</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

const char *SSL_rstate_string(SSL *ssl);
const char *SSL_rstate_string_long(SSL *ssl);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_rstate_string() returns a 2 letter string indicating the current read state of the SSL object <b>ssl</b>.</p>

<p>SSL_rstate_string_long() returns a string indicating the current read state of the SSL object <b>ssl</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>When performing a read operation, the SSL/TLS engine must parse the record, consisting of header and body. When working in a blocking environment, SSL_rstate_string[_long]() should always return &quot;RD&quot;/&quot;read done&quot;.</p>

<p>This function should only seldom be needed in applications.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_rstate_string() and SSL_rstate_string_long() can return the following values:</p>

<dl>

<dt id="RH-read-header">&quot;RH&quot;/&quot;read header&quot;</dt>
<dd>

<p>The header of the record is being evaluated.</p>

</dd>
<dt id="RB-read-body">&quot;RB&quot;/&quot;read body&quot;</dt>
<dd>

<p>The body of the record is being evaluated.</p>

</dd>
<dt id="unknown-unknown">&quot;unknown&quot;/&quot;unknown&quot;</dt>
<dd>

<p>The read state is unknown. This should never happen.</p>

</dd>
</dl>

<p>When used with QUIC SSL objects, these functions always return &quot;RH&quot;/&quot;read header&quot; in normal conditions.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2001-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


