<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>mkgroup</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="minidumper.html" title="minidumper"><link rel="next" href="mkpasswd.html" title="mkpasswd"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">mkgroup</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="minidumper.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="mkpasswd.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="mkgroup"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>mkgroup &#8212; Write /etc/group-like output to stdout</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">mkgroup</code>  [
	    -l  |   -L  
	   [<em class="replaceable"><code>MACHINE</code></em>]
	] [-d [<em class="replaceable"><code>DOMAIN</code></em>]] [-c] [-S <em class="replaceable"><code>CHAR</code></em>] [-o <em class="replaceable"><code>OFFSET</code></em>] [-g <em class="replaceable"><code>GROUPNAME</code></em>] [-b] [-U  <em class="replaceable"><code>GROUPLIST</code></em>]</p></div><div class="cmdsynopsis"><p><code class="command">mkgroup</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="mkgroup-options"></a><h2>Options</h2><pre class="screen">
   -l,--local [machine]    Print local group accounts of \"machine\",
                           from local machine if no machine specified.
                           Automatically adding machine prefix for local
                           machine depends on settings in /etc/nsswitch.conf.
   -L,--Local [machine]    Ditto, but generate groupname with machine prefix.
   -d,--domain [domain]    Print domain groups,
                           from current domain if no domain specified.
   -c,--current            Print current group.
   -S,--separator char     For -L use character char as domain\\group
                           separator in groupname instead of default '+'.
   -o,--id-offset offset   Change the default offset (0x10000) added to gids
   -g,--group groupname    Only return information for the specified group.
                           One of -l, -d must be specified, too.
   -b,--no-builtin         Don't print BUILTIN groups.
   -U,--unix grouplist     Print UNIX groups when using -l on a UNIX Samba
                           server.  Grouplist is a comma-separated list of
                           groupnames or gid ranges (root,-25,50-100).
                           Enumerating large ranges can take a long time!
   -h,--help               Print this message.
   -v,--version            Print version information and exit.

Default is to print local groups on stand-alone machines, plus domain
groups on domain controllers and domain member machines.
</pre></div><div class="refsect1"><a name="mkgroup-desc"></a><h2>Description</h2><p>Don't use this command to generate a local /etc/group file, unless you
     really need one.  See the Cygwin User's Guide for more information.
     </p><p>The <span class="command"><strong>mkgroup</strong></span> program can be used to create a local
      <code class="filename">/etc/group</code> file.  Cygwin doesn't need this file,
      because it reads group information from the Windows account databases,
      but you can add an <code class="filename">/etc/group</code> file for instance, if
      your machine is often disconnected from its domain controller.
      </p><p>Note that this information is static, in contrast to the information
      automatically gathered by Cygwin from the Windows account databases. If
      you change the group information on your system, you'll need to regenerate
      the group file for it to have the new information.</p><p>By default, the information generated by <span class="command"><strong>mkgroup</strong></span>
      is equivalent to the information generated by Cygwin itself.  The
      <code class="literal">-d</code> and <code class="literal">-l/-L</code> options allow you to
      specify where the information comes from, some domain, or the local SAM
      of a machine. Note that you can only enumerate accounts from trusted
      domains.  Any non-trusted domain will be ignored.  Access-restrictions
      of your current account apply.  The <code class="literal">-l/-L</code> when used
      with a machine name, tries to contact that machine to enumerate local
      groups of other machines, typically outside of domains.  This scenario
      cannot be covered by Cygwin's account automatism.  If you want to use
      the <code class="literal">-L</code> option, but you don't like the default
      domain/group separator from <code class="filename">/etc/nsswitch.conf</code>,
      you can specify another separator using the <code class="literal">-S</code> option,
      for instance:</p><div class="example"><a name="utils-mkgroup-ex"></a><p class="title"><b>Example&#160;3.9.&#160;Setting up group entry for current user with different
        domain/group separator</b></p><div class="example-contents"><pre class="screen">
<code class="prompt">$</code> <strong class="userinput"><code>mkgroup -L server1 -S= &gt; /etc/group</code></strong>
</pre></div></div><br class="example-break"><p>For very simple needs, an entry for the current user's group can be
      created by using the option <code class="literal">-c</code>.</p><p>The <code class="literal">-o</code> option allows for (unlikely) special cases
      with multiple machines where the GIDs might match otherwise. The
      <code class="literal">-g</code> option only prints the information for one group.
      The <code class="literal">-U</code> option allows you to enumerate the standard
      UNIX groups on a Samba machine. It's used together with <code class="literal">-l
      samba-server</code> or <code class="literal">-L samba-server</code>. The normal
      UNIX groups are usually not enumerated, but they can show up as a group
      in <span class="command"><strong>ls -l</strong></span> output. </p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="minidumper.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="mkpasswd.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">minidumper&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;mkpasswd</td></tr></table></div></body></html>
