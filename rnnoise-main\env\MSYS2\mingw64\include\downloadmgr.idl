/*
 * Copyright 2005 <PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifndef DO_NO_IMPORTS
import "unknwn.idl";
import "ocidl.idl";
import "oleidl.idl";
import "oaidl.idl";
#endif

[
    object,
    uuid(988934A4-064B-11D3-BB80-00104B35E7F9),
    pointer_default(unique),
    local
]
interface IDownloadManager : IUnknown
{
    HRESULT Download(
        [in] IMoniker *pmk,
        [in] IBindCtx *pbc,
        [in] DWORD dwBindVerb,
        [in] LONG grfBINDF,
        [in] BINDINFO *pBindInfo,
        [in] LPCOLESTR pszHeaders,
        [in] LPCOLESTR pszRedir,
        [in] UINT uiCP);
}
