<!-- Creator     : groff version 1.22.3 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>ICONV_OPEN</title>

</head>
<body>

<h1 align="center">ICONV_OPEN</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#RETURN VALUE">RETURN VALUE</a><br>
<a href="#ERRORS">ERRORS</a><br>
<a href="#CONFORMING TO">CONFORMING TO</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">iconv_open
&minus; allocate descriptor for character set conversion</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>#include
&lt;iconv.h&gt;</b></p>

<p style="margin-left:11%; margin-top: 1em"><b>iconv_t
iconv_open (const char*</b> <i>tocode</i><b>, const
char*</b> <i>fromcode</i><b>);</b></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The
<b>iconv_open</b> function allocates a conversion descriptor
suitable for converting byte sequences from character
encoding <i>fromcode</i> to character encoding
<i>tocode</i>.</p>

<p style="margin-left:11%; margin-top: 1em">The values
permitted for <i>fromcode</i> and <i>tocode</i> and the
supported combinations are system dependent. For the
libiconv library, the following encodings are supported, in
all combinations. <br>
European languages</p>

<p style="margin-left:22%;">ASCII,
ISO&minus;8859&minus;{1,2,3,4,5,7,9,10,13,14,15,16},
KOI8&minus;R, KOI8&minus;U, KOI8&minus;RU,
CP{1250,1251,1252,1253,1254,1257}, CP{850,866,1131},
Mac{Roman,CentralEurope,Iceland,Croatian,Romania},
Mac{Cyrillic,Ukraine,Greek,Turkish}, Macintosh</p>

<p style="margin-left:11%;">Semitic languages</p>

<p style="margin-left:22%;">ISO&minus;8859&minus;{6,8},
CP{1255,1256}, CP862, Mac{Hebrew,Arabic}</p>

<p style="margin-left:11%;">Japanese</p>

<p style="margin-left:22%;">EUC&minus;JP, SHIFT_JIS, CP932,
ISO&minus;2022&minus;JP, ISO&minus;2022&minus;JP&minus;2,
ISO&minus;2022&minus;JP&minus;1,
ISO-2022&minus;JP&minus;MS</p>

<p style="margin-left:11%;">Chinese</p>

<p style="margin-left:22%;">EUC&minus;CN, HZ, GBK, CP936,
GB18030, GB18030:2022, EUC&minus;TW, BIG5, CP950,
BIG5&minus;HKSCS, BIG5&minus;HKSCS:2004,
BIG5&minus;HKSCS:2001, BIG5&minus;HKSCS:1999,
ISO&minus;2022&minus;CN,
ISO&minus;2022&minus;CN&minus;EXT</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p>Korean</p></td>
<td width="2%"></td>
<td width="50%">


<p>EUC&minus;KR, CP949, ISO&minus;2022&minus;KR, JOHAB</p></td>
<td width="28%">
</td></tr>
</table>

<p style="margin-left:11%;">Armenian</p>

<p style="margin-left:22%;">ARMSCII&minus;8</p>

<p style="margin-left:11%;">Georgian</p>

<p style="margin-left:22%;">Georgian&minus;Academy,
Georgian&minus;PS</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p>Tajik</p></td>
<td width="2%"></td>
<td width="35%">


<p>KOI8&minus;T</p></td>
<td width="43%">
</td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p>Kazakh</p></td>
<td width="2%"></td>
<td width="35%">


<p>PT154, RK1048</p></td>
<td width="43%">
</td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p>Thai</p></td>
<td width="2%"></td>
<td width="35%">


<p>TIS&minus;620, CP874, MacThai</p></td>
<td width="43%">
</td></tr>
</table>

<p style="margin-left:11%;">Laotian</p>

<p style="margin-left:22%;">MuleLao&minus;1, CP1133</p>

<p style="margin-left:11%;">Vietnamese</p>

<p style="margin-left:22%;">VISCII, TCVN, CP1258</p>

<p style="margin-left:11%;">Platform specifics</p>

<p style="margin-left:22%;">HP&minus;ROMAN8, NEXTSTEP</p>

<p style="margin-left:11%;">Full Unicode</p>

<p style="margin-left:22%;">UTF&minus;8 <br>
UCS&minus;2, UCS&minus;2BE, UCS&minus;2LE <br>
UCS&minus;4, UCS&minus;4BE, UCS&minus;4LE <br>
UTF&minus;16, UTF&minus;16BE, UTF&minus;16LE <br>
UTF&minus;32, UTF&minus;32BE, UTF&minus;32LE <br>
UTF&minus;7 <br>
C99, JAVA</p>

<p style="margin-left:11%;">Full Unicode, in terms of
<b>uint16_t</b> or <b>uint32_t</b></p>

<p style="margin-left:22%;">(with machine dependent
endianness and alignment) <br>
UCS&minus;2&minus;INTERNAL, UCS&minus;4&minus;INTERNAL</p>

<p style="margin-left:11%;">Locale dependent, in terms of
<b>char</b> or <b>wchar_t</b></p>

<p style="margin-left:22%;">(with machine dependent
endianness and alignment, and with semantics depending on
the OS and the current LC_CTYPE locale facet) <br>
char, wchar_t</p>

<p style="margin-left:11%; margin-top: 1em">When configured
with the option
<b>&minus;&minus;enable&minus;extra&minus;encodings</b>, it
also provides support for a few extra encodings: <br>
European languages</p>


<p style="margin-left:22%;">CP{437,737,775,852,853,855,857,858,860,861,863,865,869,1125}</p>

<p style="margin-left:11%;">Semitic languages</p>

<p style="margin-left:22%;">CP864</p>

<p style="margin-left:11%;">Japanese</p>

<p style="margin-left:22%;">EUC&minus;JISX0213,
Shift_JISX0213, ISO&minus;2022&minus;JP&minus;3</p>

<p style="margin-left:11%;">Chinese</p>

<p style="margin-left:22%;">BIG5&minus;2003
(experimental)</p>

<p style="margin-left:11%;">Turkmen</p>

<p style="margin-left:22%;">TDS565</p>

<p style="margin-left:11%;">Platform specifics</p>

<p style="margin-left:22%;">ATARIST,
RISCOS&minus;LATIN1</p>

<p style="margin-left:11%;">EBCDIC compatible (not ASCII
compatible, very rarely used)</p>

<p style="margin-left:22%;">European languages: <br>

IBM-{037,273,277,278,280,282,284,285,297,423,500,870,871,875,880},
<br>

IBM-{905,924,1025,1026,1047,1112,1122,1123,1140,1141,1142,1143},
<br>

IBM-{1144,1145,1146,1147,1148,1149,1153,1154,1155,1156,1157,1158},
<br>
IBM-{1165,1166,4971} <br>
Semitic languages: <br>
IBM-{424,425,12712,16804} <br>
Persian: <br>
IBM-1097 <br>
Thai: <br>
IBM-{838,1160} <br>
Laotian: <br>
IBM-1132 <br>
Vietnamese: <br>
IBM-{1130,1164} <br>
Indic languages: <br>
IBM-1137</p>

<p style="margin-left:11%; margin-top: 1em">The empty
encoding name &quot;&quot; is equivalent to
&quot;char&quot;: it denotes the locale dependent character
encoding.</p>

<p style="margin-left:11%; margin-top: 1em">When the string
&quot;//TRANSLIT&quot; is appended to <i>tocode</i>,
transliteration is activated. This means that when a
character cannot be represented in the target character set,
it can be approximated through one or several characters
that look similar to the original character.</p>

<p style="margin-left:11%; margin-top: 1em">When the string
&quot;//IGNORE&quot; is appended to <i>tocode</i>, invalid
multibyte sequences in the input and characters that cannot
be represented in the target character set will be silently
discarded.</p>

<p style="margin-left:11%; margin-top: 1em">When the string
&quot;//NON_IDENTICAL_DISCARD&quot; is appended to
<i>tocode</i>, characters that cannot be represented in the
target character set will be silently discarded.</p>

<p style="margin-left:11%; margin-top: 1em">The resulting
conversion descriptor can be used with <b>iconv</b> any
number of times. It remains valid until deallocated using
<b>iconv_close</b>.</p>

<p style="margin-left:11%; margin-top: 1em">A conversion
descriptor contains a conversion state. After creation using
<b>iconv_open</b>, the state is in the initial state. Using
<b>iconv</b> modifies the descriptor&rsquo;s conversion
state. (This implies that a conversion descriptor can not be
used in multiple threads simultaneously.) To bring the state
back to the initial state, use <b>iconv</b> with NULL as
<i>inbuf</i> argument.</p>

<h2>RETURN VALUE
<a name="RETURN VALUE"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The
<b>iconv_open</b> function returns a freshly allocated
conversion descriptor. In case of error, it sets
<b>errno</b> and returns (iconv_t)(&minus;1).</p>

<h2>ERRORS
<a name="ERRORS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The following
error can occur, among others:</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p><b>EINVAL</b></p></td>
<td width="2%"></td>
<td width="78%">


<p>The conversion from <i>fromcode</i> to <i>tocode</i> is
not supported by the implementation.</p></td></tr>
</table>

<h2>CONFORMING TO
<a name="CONFORMING TO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">POSIX:2024</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><b>iconv</b>(3)
<b>iconvctl</b>(3) <b>iconv_close</b>(3)</p>
<hr>
</body>
</html>
