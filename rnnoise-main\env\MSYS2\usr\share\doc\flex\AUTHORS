
In 2001, <PERSON> took over as maintainer of flex.

<PERSON> is a co-author of the current version of flex. He has
contributed a large number of new features, fixed a large number of
outstanding bugs and has made significant contributions to the flex
documentation.

<PERSON> has contributed several bug fixes to the flex codebase.

<PERSON><PERSON> wrote flex with the help of many ideas and much
inspiration from <PERSON>.  Original version by <PERSON><PERSON>.

The fast table representation is a partial implementation of a design
done by <PERSON>.  The implementation was done by <PERSON> and
<PERSON><PERSON>.
