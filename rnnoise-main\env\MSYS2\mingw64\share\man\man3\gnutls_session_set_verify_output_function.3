.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_set_verify_output_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_set_verify_output_function \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void gnutls_session_set_verify_output_function(gnutls_session_t " session ", gnutls_verify_output_function * " func ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_x509_trust_list_t\fP type.
.IP "gnutls_verify_output_function * func" 12
is the callback function
.SH "DESCRIPTION"
This function sets a callback to be called when the peer's certificate
chain has to be verified and full path to the trusted root has to be
printed.

The callback's function prototype is defined in `x509.h':
int (*callback)(
gnutls_x509_crt_t cert,
gnutls_x509_crt_t issuer,
gnutls_x509_crl_t crl,
unsigned int verification_output);

If the callback function is provided then gnut<PERSON> will call it, in the
certificate verification procedure.
To verify the certificate chain and print its path uptp the trusted root,
functions such as \fBgnutls_certificate_verify_peers()\fP,
\fBgnutls_x509_trust_list_verify_crt()\fP, and \fBgnutls_x509_trust_list_verify_crt2()\fP
can be used. The callback is set in \fB_gnutls_verify_crt_status()\fP and
\fB_gnutls_pkcs11_verify_crt_status()\fP.
.SH "SINCE"
3.7.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
