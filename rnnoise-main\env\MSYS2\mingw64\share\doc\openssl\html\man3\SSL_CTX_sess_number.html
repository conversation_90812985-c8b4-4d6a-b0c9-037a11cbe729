<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_sess_number</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CTX_sess_number, SSL_CTX_sess_connect, SSL_CTX_sess_connect_good, SSL_CTX_sess_connect_renegotiate, SSL_CTX_sess_accept, SSL_CTX_sess_accept_good, SSL_CTX_sess_accept_renegotiate, SSL_CTX_sess_hits, SSL_CTX_sess_cb_hits, SSL_CTX_sess_misses, SSL_CTX_sess_timeouts, SSL_CTX_sess_cache_full - obtain session cache statistics</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

long SSL_CTX_sess_number(SSL_CTX *ctx);
long SSL_CTX_sess_connect(SSL_CTX *ctx);
long SSL_CTX_sess_connect_good(SSL_CTX *ctx);
long SSL_CTX_sess_connect_renegotiate(SSL_CTX *ctx);
long SSL_CTX_sess_accept(SSL_CTX *ctx);
long SSL_CTX_sess_accept_good(SSL_CTX *ctx);
long SSL_CTX_sess_accept_renegotiate(SSL_CTX *ctx);
long SSL_CTX_sess_hits(SSL_CTX *ctx);
long SSL_CTX_sess_cb_hits(SSL_CTX *ctx);
long SSL_CTX_sess_misses(SSL_CTX *ctx);
long SSL_CTX_sess_timeouts(SSL_CTX *ctx);
long SSL_CTX_sess_cache_full(SSL_CTX *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_CTX_sess_number() returns the current number of sessions in the internal session cache.</p>

<p>SSL_CTX_sess_connect() returns the number of started SSL/TLS handshakes in client mode.</p>

<p>SSL_CTX_sess_connect_good() returns the number of successfully established SSL/TLS sessions in client mode.</p>

<p>SSL_CTX_sess_connect_renegotiate() returns the number of started renegotiations in client mode.</p>

<p>SSL_CTX_sess_accept() returns the number of started SSL/TLS handshakes in server mode.</p>

<p>SSL_CTX_sess_accept_good() returns the number of successfully established SSL/TLS sessions in server mode.</p>

<p>SSL_CTX_sess_accept_renegotiate() returns the number of started renegotiations in server mode.</p>

<p>SSL_CTX_sess_hits() returns the number of successfully reused sessions. In client mode a session set with <a href="../man3/SSL_set_session.html">SSL_set_session(3)</a> successfully reused is counted as a hit. In server mode a session successfully retrieved from internal or external cache is counted as a hit.</p>

<p>SSL_CTX_sess_cb_hits() returns the number of successfully retrieved sessions from the external session cache in server mode.</p>

<p>SSL_CTX_sess_misses() returns the number of sessions proposed by clients that were not found in the internal session cache in server mode.</p>

<p>SSL_CTX_sess_timeouts() returns the number of sessions proposed by clients and either found in the internal or external session cache in server mode, but that were invalid due to timeout. These sessions are not included in the SSL_CTX_sess_hits() count.</p>

<p>SSL_CTX_sess_cache_full() returns the number of sessions that were removed because the maximum session cache size was exceeded.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The functions return the values indicated in the DESCRIPTION section.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_set_session.html">SSL_set_session(3)</a>, <a href="../man3/SSL_CTX_set_session_cache_mode.html">SSL_CTX_set_session_cache_mode(3)</a> <a href="../man3/SSL_CTX_sess_set_cache_size.html">SSL_CTX_sess_set_cache_size(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


