<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_allocator Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structlzma__allocator.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_allocator Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Custom functions for memory handling.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;base.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aba5c4369af94cc9943423b49171462ec" id="r_aba5c4369af94cc9943423b49171462ec"><td class="memItemLeft" align="right" valign="top">void *(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aba5c4369af94cc9943423b49171462ec">alloc</a> )(void *<a class="el" href="#aab293a5007a93299cc97ee8b5fb81268">opaque</a>, size_t nmemb, size_t size)</td></tr>
<tr class="memdesc:aba5c4369af94cc9943423b49171462ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to a custom memory allocation function.  <br /></td></tr>
<tr class="separator:aba5c4369af94cc9943423b49171462ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3726deffd08393934263c04660208009" id="r_a3726deffd08393934263c04660208009"><td class="memItemLeft" align="right" valign="top">void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3726deffd08393934263c04660208009">free</a> )(void *<a class="el" href="#aab293a5007a93299cc97ee8b5fb81268">opaque</a>, void *ptr)</td></tr>
<tr class="memdesc:a3726deffd08393934263c04660208009"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to a custom memory freeing function.  <br /></td></tr>
<tr class="separator:a3726deffd08393934263c04660208009"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab293a5007a93299cc97ee8b5fb81268" id="r_aab293a5007a93299cc97ee8b5fb81268"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aab293a5007a93299cc97ee8b5fb81268">opaque</a></td></tr>
<tr class="memdesc:aab293a5007a93299cc97ee8b5fb81268"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer passed to .<a class="el" href="#aba5c4369af94cc9943423b49171462ec" title="Pointer to a custom memory allocation function.">alloc()</a> and .<a class="el" href="#a3726deffd08393934263c04660208009" title="Pointer to a custom memory freeing function.">free()</a>  <br /></td></tr>
<tr class="separator:aab293a5007a93299cc97ee8b5fb81268"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Custom functions for memory handling. </p>
<p>A pointer to <a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> may be passed via <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> structure to liblzma, and some advanced functions take a pointer to <a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> as a separate function argument. The library will use the functions specified in <a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for memory handling instead of the default malloc() and <a class="el" href="#a3726deffd08393934263c04660208009" title="Pointer to a custom memory freeing function.">free()</a>. C++ users should note that the custom memory handling functions must not throw exceptions.</p>
<p>Single-threaded mode only: liblzma doesn't make an internal copy of <a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a>. Thus, it is OK to change these function pointers in the middle of the coding process, but obviously it must be done carefully to make sure that the replacement 'free' can deallocate memory allocated by the earlier 'alloc' function(s).</p>
<p>Multithreaded mode: liblzma might internally store pointers to the <a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> given via the <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> structure. The application must not change the allocator pointer in <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> or the contents of the pointed <a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> structure until <a class="el" href="base_8h.html#a854ff37464ae1225febf14db1af43308" title="Free memory allocated for the coder data structures.">lzma_end()</a> has been used to free the memory associated with that <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a>. The allocation functions might be called simultaneously from multiple threads, and thus they must be thread safe. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aba5c4369af94cc9943423b49171462ec" name="aba5c4369af94cc9943423b49171462ec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba5c4369af94cc9943423b49171462ec">&#9670;&#160;</a></span>alloc</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void *(* lzma_allocator::alloc) (void *<a class="el" href="#aab293a5007a93299cc97ee8b5fb81268">opaque</a>, size_t nmemb, size_t size)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to a custom memory allocation function. </p>
<p>If you don't want a custom allocator, but still want custom <a class="el" href="#a3726deffd08393934263c04660208009" title="Pointer to a custom memory freeing function.">free()</a>, set this to NULL and liblzma will use the standard malloc().</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">opaque</td><td><a class="el" href="#aab293a5007a93299cc97ee8b5fb81268" title="Pointer passed to .alloc() and .free()">lzma_allocator.opaque</a> (see below) </td></tr>
    <tr><td class="paramname">nmemb</td><td>Number of elements like in calloc(). liblzma will always set nmemb to 1, so it is safe to ignore nmemb in a custom allocator if you like. The nmemb argument exists only for compatibility with zlib and libbzip2. </td></tr>
    <tr><td class="paramname">size</td><td>Size of an element in bytes. liblzma never sets this to zero.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Pointer to the beginning of a memory block of 'size' bytes, or NULL if allocation fails for some reason. When allocation fails, functions of liblzma return LZMA_MEM_ERROR.</dd></dl>
<p>The allocator should not waste time zeroing the allocated buffers. This is not only about speed, but also memory usage, since the operating system kernel doesn't necessarily allocate the requested memory in physical memory until it is actually used. With small input files, liblzma may actually need only a fraction of the memory that it requested for allocation.</p>
<dl class="section note"><dt>Note</dt><dd>LZMA_MEM_ERROR is also used when the size of the allocation would be greater than SIZE_MAX. Thus, don't assume that the custom allocator must have returned NULL if some function from liblzma returns LZMA_MEM_ERROR. </dd></dl>

</div>
</div>
<a id="a3726deffd08393934263c04660208009" name="a3726deffd08393934263c04660208009"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3726deffd08393934263c04660208009">&#9670;&#160;</a></span>free</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void(* lzma_allocator::free) (void *<a class="el" href="#aab293a5007a93299cc97ee8b5fb81268">opaque</a>, void *ptr)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to a custom memory freeing function. </p>
<p>If you don't want a custom freeing function, but still want a custom allocator, set this to NULL and liblzma will use the standard <a class="el" href="#a3726deffd08393934263c04660208009" title="Pointer to a custom memory freeing function.">free()</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">opaque</td><td><a class="el" href="#aab293a5007a93299cc97ee8b5fb81268" title="Pointer passed to .alloc() and .free()">lzma_allocator.opaque</a> (see below) </td></tr>
    <tr><td class="paramname">ptr</td><td>Pointer returned by <a class="el" href="#aba5c4369af94cc9943423b49171462ec" title="Pointer to a custom memory allocation function.">lzma_allocator.alloc()</a>, or when it is set to NULL, a pointer returned by the standard malloc(). </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aab293a5007a93299cc97ee8b5fb81268" name="aab293a5007a93299cc97ee8b5fb81268"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab293a5007a93299cc97ee8b5fb81268">&#9670;&#160;</a></span>opaque</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* lzma_allocator::opaque</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer passed to .<a class="el" href="#aba5c4369af94cc9943423b49171462ec" title="Pointer to a custom memory allocation function.">alloc()</a> and .<a class="el" href="#a3726deffd08393934263c04660208009" title="Pointer to a custom memory freeing function.">free()</a> </p>
<p>opaque is passed as the first argument to <a class="el" href="#aba5c4369af94cc9943423b49171462ec" title="Pointer to a custom memory allocation function.">lzma_allocator.alloc()</a> and <a class="el" href="#a3726deffd08393934263c04660208009" title="Pointer to a custom memory freeing function.">lzma_allocator.free()</a>. This intended to ease implementing custom memory allocation functions for use with liblzma.</p>
<p>If you don't need this, you should set this to NULL. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="base_8h.html">base.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structlzma__allocator.html">lzma_allocator</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
