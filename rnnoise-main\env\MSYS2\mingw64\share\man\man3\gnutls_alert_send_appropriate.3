.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_alert_send_appropriate" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_alert_send_appropriate \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_alert_send_appropriate(gnutls_session_t " session ", int " err ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "int err" 12
is an error code returned by another GnuTLS function
.SH "DESCRIPTION"
Sends an alert to the peer depending on the error code returned by
a gnutls function. This function will call \fBgnutls_error_to_alert()\fP
to determine the appropriate alert to send.

This function may also return \fBGNUTLS_E_AGAIN\fP, or
\fBGNUTLS_E_INTERRUPTED\fP.

This function historically was always sending an alert to the
peer, even if  \fIerr\fP was inappropriate to respond with an alert
(e.g., \fBGNUTLS_E_SUCCESS\fP). Since 3.6.6 this function returns
success without transmitting any data on error codes that
should not result to an alert.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
