.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_compress_certificate_set_methods" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_compress_certificate_set_methods \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_compress_certificate_set_methods(gnutls_session_t " session ", const gnutls_compression_method_t * " methods ", size_t " methods_len ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const gnutls_compression_method_t * methods" 12
is a list of supported compression methods.
.IP "size_t methods_len" 12
number of compression methods in  \fImethods\fP 
.SH "DESCRIPTION"
This function sets the supported compression methods for certificate compression
for the given session. The list of supported compression methods will be used
for a) requesting the compression of peer's certificate and b) selecting the
method to compress the local certificate before sending it to the peer.
The order of compression methods inside the list does matter as the method
that appears earlier in the list will be preferred before the later ones.
Note that even if you set the list of supported compression methods, the
compression might not be used if the peer does not support any of your chosen
compression methods.

The list of supported compression methods must meet the following criteria:
Argument  \fImethods\fP must be an array of valid compression methods of type
\fBgnutls_compression_method_t\fP. Argument  \fImethods_len\fP must contain the number of
compression methods stored in the  \fImethods\fP array and must be within range <1, 127>.
The length constraints are defined by \fBMIN_COMPRESS_CERTIFICATE_METHODS\fP
and \fBMAX_COMPRESS_CERTIFICATE_METHODS\fP macros located in the header file
compress_certificate.h.

If either  \fImethods\fP or  \fImethods_len\fP is equal to 0, current list of supported
compression methods will be unset.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.

Since 3.7.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
