// <cstdint> -*- C++ -*-

// Copyright (C) 2007-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/cstdint
 *  This is a Standard C++ Library header.
 */

#ifndef _GLIBCXX_CSTDINT
#define _GLIBCXX_CSTDINT 1

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#if __cplusplus < 201103L
# include <bits/c++0x_warning.h>
#else

#include <bits/c++config.h>

#if ! _GLIBCXX_HOSTED && __has_include(<stdint-gcc.h>)
// For --disable-hosted-libstdcxx we want GCC's own stdint-gcc.h header
// even when -ffreestanding isn't used.
# include <stdint-gcc.h>
#elif __has_include(<stdint.h>)
# include <stdint.h>
#endif

namespace std
{
#ifdef _GLIBCXX_USE_C99_STDINT
  using ::int8_t;
  using ::int16_t;
  using ::int32_t;
  using ::int64_t;

  using ::int_fast8_t;
  using ::int_fast16_t;
  using ::int_fast32_t;
  using ::int_fast64_t;

  using ::int_least8_t;
  using ::int_least16_t;
  using ::int_least32_t;
  using ::int_least64_t;

  using ::intmax_t;
  using ::intptr_t;
  
  using ::uint8_t;
  using ::uint16_t;
  using ::uint32_t;
  using ::uint64_t;

  using ::uint_fast8_t;
  using ::uint_fast16_t;
  using ::uint_fast32_t;
  using ::uint_fast64_t;

  using ::uint_least8_t;
  using ::uint_least16_t;
  using ::uint_least32_t;
  using ::uint_least64_t;

  using ::uintmax_t;
  using ::uintptr_t;
#else // !_GLIBCXX_USE_C99_STDINT

  using intmax_t = __INTMAX_TYPE__;
  using uintmax_t = __UINTMAX_TYPE__;

#ifdef __INT8_TYPE__
  using int8_t = __INT8_TYPE__;
#endif
#ifdef __INT16_TYPE__
  using int16_t = __INT16_TYPE__;
#endif
#ifdef __INT32_TYPE__
  using int32_t = __INT32_TYPE__;
#endif
#ifdef __INT64_TYPE__
  using int64_t = __INT64_TYPE__;
#endif

  using int_least8_t = __INT_LEAST8_TYPE__;
  using int_least16_t = __INT_LEAST16_TYPE__;
  using int_least32_t = __INT_LEAST32_TYPE__;
  using int_least64_t = __INT_LEAST64_TYPE__;
  using int_fast8_t = __INT_FAST8_TYPE__;
  using int_fast16_t = __INT_FAST16_TYPE__;
  using int_fast32_t = __INT_FAST32_TYPE__;
  using int_fast64_t = __INT_FAST64_TYPE__;

#ifdef __INTPTR_TYPE__
  using intptr_t = __INTPTR_TYPE__;
#endif

#ifdef __UINT8_TYPE__
  using uint8_t = __UINT8_TYPE__;
#endif
#ifdef __UINT16_TYPE__
  using uint16_t = __UINT16_TYPE__;
#endif
#ifdef __UINT32_TYPE__
  using uint32_t = __UINT32_TYPE__;
#endif
#ifdef __UINT64_TYPE__
  using uint64_t = __UINT64_TYPE__;
#endif
  using uint_least8_t = __UINT_LEAST8_TYPE__;
  using uint_least16_t = __UINT_LEAST16_TYPE__;
  using uint_least32_t = __UINT_LEAST32_TYPE__;
  using uint_least64_t = __UINT_LEAST64_TYPE__;
  using uint_fast8_t = __UINT_FAST8_TYPE__;
  using uint_fast16_t = __UINT_FAST16_TYPE__;
  using uint_fast32_t = __UINT_FAST32_TYPE__;
  using uint_fast64_t = __UINT_FAST64_TYPE__;
#ifdef __UINTPTR_TYPE__
  using uintptr_t = __UINTPTR_TYPE__;
#endif

#endif // _GLIBCXX_USE_C99_STDINT
} // namespace std

#endif // C++11

#endif // _GLIBCXX_CSTDINT
