/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

import "inspectable.idl";

cpp_quote("#include <winapifamily.h>")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN10_RS1)")

cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

[
  uuid(75cf2c57-**************-f0b409e916af)
]
interface IInputPaneInterop : IInspectable
{
  HRESULT GetForWindow(
    [in] HWND app_window,
    [in] REFIID riid,
    [out, retval, iid_is(riid)] void **input_pane);
}

cpp_quote("#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */")

cpp_quote("#endif /* (NTDDI_VERSION >= NTDDI_WIN10_RS1) */")
