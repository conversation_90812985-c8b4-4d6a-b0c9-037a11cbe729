.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_get_embedded_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_get_embedded_data \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_get_embedded_data(gnutls_pkcs7_t " pkcs7 ", unsigned " flags ", gnutls_datum_t * " data ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_t pkcs7" 12
should contain a gnutls_pkcs7_t type
.IP "unsigned flags" 12
must be zero or \fBGNUTLS_PKCS7_EDATA_GET_RAW\fP
.IP "gnutls_datum_t * data" 12
will hold the embedded data in the provided structure
.SH "DESCRIPTION"
This function will return the data embedded in the signature of
the PKCS7 structure. If no data are available then
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be returned.

The returned data must be de\-allocated using \fBgnutls_free()\fP.

Note, that this function returns the exact same data that are
authenticated. If the \fBGNUTLS_PKCS7_EDATA_GET_RAW\fP flag is provided,
the returned data will be including the wrapping tag/value as
they are encoded in the structure.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.8
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
