# "kohlrausch" colour theme for mintty
#
# Copyright (C) 2022 <PERSON>.
# Licensed under the terms of the MIT License.
#
# This is a variant of the "helmholtz" theme that has been designed for use
# with a dark text colour and light background, by darkening the foreground
# variants of the normal colours and brightening the background ones.
#
# To level the colours, their luminance was calculated as follows:
# - Gamma-expand the sRGB components by dividing each by 255 and applying the
#   sRGB transfer function (which approximately means applying exponent 2.2).
# - Combine the components with formula Y = 0.2126 R + 0.7152 G + 0.0722 B.
# - Gamma-compress with the reverse transfer function (approximately: apply
#   exponent 1/2.2).
#
# https://en.wikipedia.org/wiki/SRGB
#
# The colours were then adjusted to achieve equal luminance. However, that does
# not actually yield colours with equal apparent brightness. This is due to the
# "Helmholtz-Kohlrausch effect", which means that some saturated colours appear
# brighter than others to the human eye. The theme is named after that.
#
# https://en.wikipedia.org/wiki/He<PERSON>holtz-<PERSON>hlrausch_effect
#
# There doesn't appear to be a generally valid model for compensating for the
# effect, as it depends on observer, display device, and viewing environment.
# Hence, correction factors were determined experimentally, ranging from 1.0
# for yellow to 1.45 for red, and the colour components adjusted accordingly.
# Of course, that is a highly subjective process ...

ForegroundColour=0,0,0
BackgroundColour=255,255,255
CursorColour=0,0,0

Black=0,0,0
Red=162,30,41;212,44,58
Green=21,126,0;28,168,0
Yellow=144,120,0;192,160,0
Blue=0,32,192;0,93,255
Magenta=134,54,150;177,72,198
Cyan=0,126,115;0,168,154
White=191,191,191

BoldBlack=96,96,96;127,127,127
BoldRed=255,118,118
BoldGreen=0,242,0
BoldYellow=242,242,0
BoldBlue=125,151,255
BoldMagenta=255,112,255
BoldCyan=0,240,240
BoldWhite=255,255,255
