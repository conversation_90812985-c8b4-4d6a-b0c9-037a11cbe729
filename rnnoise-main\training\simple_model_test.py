#!/usr/bin/env python3
"""
简单的模型测试脚本
验证增强RNN模型的基本功能
"""

import tensorflow as tf
import numpy as np
import os

# 设置CPU运行
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

def test_model_basic():
    """基本模型测试"""
    print("=== 增强RNN模型基本测试 ===")
    
    model_path = '../models_enhanced/stable_20250802_124711/enhanced_rnnoise_stable.keras'
    
    if not os.path.exists(model_path):
        print(f"❌ 找不到模型文件: {model_path}")
        return False
    
    try:
        # 定义自定义损失函数
        def stable_mse_loss(y_true, y_pred):
            return tf.reduce_mean(tf.square(y_true - y_pred))
        
        # 加载模型
        print("加载模型...")
        model = tf.keras.models.load_model(model_path, custom_objects={
            'stable_mse_loss': stable_mse_loss
        })
        
        print("✅ 模型加载成功")
        
        # 显示模型信息
        print("\n模型结构:")
        model.summary()
        
        # 创建测试输入
        print("\n创建测试输入...")
        test_input = np.random.randn(1, 100, 68).astype(np.float32)  # (batch, sequence, features)
        print(f"测试输入形状: {test_input.shape}")
        
        # 运行预测
        print("\n运行预测...")
        predictions = model.predict(test_input, verbose=0)
        
        print(f"预测输出数量: {len(predictions)}")
        for i, pred in enumerate(predictions):
            print(f"  输出 {i+1} 形状: {pred.shape}")
            print(f"  输出 {i+1} 范围: [{np.min(pred):.6f}, {np.max(pred):.6f}]")
            print(f"  输出 {i+1} 是否有NaN: {np.any(np.isnan(pred))}")
            print(f"  输出 {i+1} 是否有Inf: {np.any(np.isinf(pred))}")
        
        # 测试不同输入大小
        print("\n测试不同序列长度:")
        for seq_len in [1, 10, 50, 200]:
            test_seq = np.random.randn(1, seq_len, 68).astype(np.float32)
            try:
                pred = model.predict(test_seq, verbose=0)
                print(f"  序列长度 {seq_len}: ✅ 成功")
            except Exception as e:
                print(f"  序列长度 {seq_len}: ❌ 失败 - {e}")
        
        # 测试边界情况
        print("\n测试边界情况:")
        boundary_tests = [
            ("零输入", np.zeros((1, 10, 68))),
            ("全1输入", np.ones((1, 10, 68))),
            ("大值输入", np.ones((1, 10, 68)) * 10),
            ("负值输入", -np.ones((1, 10, 68))),
            ("随机小值", np.random.randn(1, 10, 68) * 0.01)
        ]
        
        for name, test_data in boundary_tests:
            try:
                pred = model.predict(test_data, verbose=0)
                has_nan = any(np.any(np.isnan(p)) for p in pred)
                has_inf = any(np.any(np.isinf(p)) for p in pred)
                
                if has_nan or has_inf:
                    print(f"  {name}: ⚠️ 包含NaN/Inf")
                else:
                    print(f"  {name}: ✅ 正常")
                    
            except Exception as e:
                print(f"  {name}: ❌ 错误 - {e}")
        
        print("\n🎉 基本测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_working_model():
    """创建一个简单可工作的模型用于验证"""
    print("\n=== 创建简单验证模型 ===")
    
    # 创建简单模型
    inputs = tf.keras.Input(shape=(None, 68), name='inputs')
    
    # 简单的处理层
    x = tf.keras.layers.Dense(32, activation='tanh')(inputs)
    x = tf.keras.layers.GRU(32, return_sequences=True)(x)
    
    # 三个输出
    noise_output = tf.keras.layers.Dense(18, activation='sigmoid', name='noise_output')(x)
    voice_output = tf.keras.layers.Dense(18, activation='sigmoid', name='voice_output')(x)
    vad_output = tf.keras.layers.Dense(1, activation='sigmoid', name='vad_output')(x)
    
    model = tf.keras.Model(inputs=inputs, outputs=[noise_output, voice_output, vad_output])
    
    # 编译模型
    model.compile(
        optimizer='adam',
        loss=['mse', 'mse', 'binary_crossentropy'],
        loss_weights=[1.0, 1.0, 0.1]
    )
    
    print("✅ 简单模型创建成功")
    model.summary()
    
    # 测试简单模型
    test_input = np.random.randn(2, 50, 68).astype(np.float32)
    pred = model.predict(test_input, verbose=0)
    
    print(f"\n简单模型测试:")
    for i, p in enumerate(pred):
        print(f"  输出 {i+1}: 形状 {p.shape}, 范围 [{np.min(p):.3f}, {np.max(p):.3f}]")
    
    # 保存简单模型
    simple_model_path = '../models_enhanced/simple_working_model.keras'
    model.save(simple_model_path)
    print(f"✅ 简单模型已保存: {simple_model_path}")
    
    return model

def compare_models():
    """比较训练的模型和简单模型"""
    print("\n=== 模型对比 ===")
    
    # 测试输入
    test_input = np.random.randn(1, 20, 68).astype(np.float32)
    
    # 测试训练的模型
    trained_model_path = '../models_enhanced/stable_20250802_124711/enhanced_rnnoise_stable.keras'
    if os.path.exists(trained_model_path):
        try:
            def stable_mse_loss(y_true, y_pred):
                return tf.reduce_mean(tf.square(y_true - y_pred))
            
            trained_model = tf.keras.models.load_model(trained_model_path, custom_objects={
                'stable_mse_loss': stable_mse_loss
            })
            
            trained_pred = trained_model.predict(test_input, verbose=0)
            print("训练模型预测:")
            for i, p in enumerate(trained_pred):
                has_nan = np.any(np.isnan(p))
                has_inf = np.any(np.isinf(p))
                print(f"  输出 {i+1}: NaN={has_nan}, Inf={has_inf}, 范围=[{np.min(p):.6f}, {np.max(p):.6f}]")
                
        except Exception as e:
            print(f"训练模型测试失败: {e}")
    
    # 测试简单模型
    simple_model_path = '../models_enhanced/simple_working_model.keras'
    if os.path.exists(simple_model_path):
        try:
            simple_model = tf.keras.models.load_model(simple_model_path)
            simple_pred = simple_model.predict(test_input, verbose=0)
            print("简单模型预测:")
            for i, p in enumerate(simple_pred):
                print(f"  输出 {i+1}: 范围=[{np.min(p):.3f}, {np.max(p):.3f}]")
                
        except Exception as e:
            print(f"简单模型测试失败: {e}")

def main():
    """主函数"""
    print("增强RNN模型测试程序")
    print("=" * 50)
    
    # 基本测试
    success = test_model_basic()
    
    # 创建简单模型作为对比
    create_simple_working_model()
    
    # 模型对比
    compare_models()
    
    # 总结
    print("\n" + "=" * 50)
    if success:
        print("✅ 模型基本功能正常")
        print("📝 建议:")
        print("  1. 检查训练数据质量")
        print("  2. 调整损失函数和学习率")
        print("  3. 增加数值稳定性措施")
    else:
        print("❌ 模型存在问题")
        print("📝 建议:")
        print("  1. 重新训练模型")
        print("  2. 使用简单模型架构")
        print("  3. 检查数据预处理")
    
    print("\n模型文件位置:")
    print("  训练模型: ../models_enhanced/stable_20250802_124711/enhanced_rnnoise_stable.keras")
    print("  简单模型: ../models_enhanced/simple_working_model.keras")

if __name__ == '__main__':
    main()
