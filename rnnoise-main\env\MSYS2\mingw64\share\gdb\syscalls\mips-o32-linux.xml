<?xml version="1.0"?>
<!DOCTYPE syscalls_info SYSTEM "gdb-syscalls.dtd">
<!-- Copyright (C) 2011-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->
<!-- This file was generated using the following file:

     arch/mips/kernel/syscalls/syscall_o32.tbl

     The file mentioned above belongs to the Linux Kernel.  -->
<syscalls_info>
  <syscall name="syscall" number="4000"/>
  <syscall name="exit" number="4001" groups="process"/>
  <syscall name="fork" number="4002" groups="process"/>
  <syscall name="read" number="4003" groups="descriptor"/>
  <syscall name="write" number="4004" groups="descriptor"/>
  <syscall name="open" number="4005" groups="descriptor,file"/>
  <syscall name="close" number="4006" groups="descriptor"/>
  <syscall name="waitpid" number="4007" groups="process"/>
  <syscall name="creat" number="4008" groups="descriptor,file"/>
  <syscall name="link" number="4009" groups="file"/>
  <syscall name="unlink" number="4010" groups="file"/>
  <syscall name="execve" number="4011" groups="file,process"/>
  <syscall name="chdir" number="4012" groups="file"/>
  <syscall name="time" number="4013"/>
  <syscall name="mknod" number="4014" groups="file"/>
  <syscall name="chmod" number="4015" groups="file"/>
  <syscall name="lchown" number="4016" groups="file"/>
  <syscall name="break" number="4017" groups="memory"/>
  <syscall name="lseek" number="4019" groups="descriptor"/>
  <syscall name="getpid" number="4020"/>
  <syscall name="mount" number="4021" groups="file"/>
  <syscall name="umount" number="4022" groups="file"/>
  <syscall name="setuid" number="4023"/>
  <syscall name="getuid" number="4024"/>
  <syscall name="stime" number="4025"/>
  <syscall name="ptrace" number="4026"/>
  <syscall name="alarm" number="4027"/>
  <syscall name="pause" number="4029" groups="signal"/>
  <syscall name="utime" number="4030" groups="file"/>
  <syscall name="stty" number="4031"/>
  <syscall name="gtty" number="4032"/>
  <syscall name="access" number="4033" groups="file"/>
  <syscall name="nice" number="4034"/>
  <syscall name="ftime" number="4035"/>
  <syscall name="sync" number="4036"/>
  <syscall name="kill" number="4037" groups="signal,process"/>
  <syscall name="rename" number="4038" groups="file"/>
  <syscall name="mkdir" number="4039" groups="file"/>
  <syscall name="rmdir" number="4040" groups="file"/>
  <syscall name="dup" number="4041" groups="descriptor"/>
  <syscall name="pipe" number="4042" groups="descriptor"/>
  <syscall name="times" number="4043"/>
  <syscall name="prof" number="4044"/>
  <syscall name="brk" number="4045" groups="memory"/>
  <syscall name="setgid" number="4046"/>
  <syscall name="getgid" number="4047"/>
  <syscall name="signal" number="4048" groups="signal"/>
  <syscall name="geteuid" number="4049"/>
  <syscall name="getegid" number="4050"/>
  <syscall name="acct" number="4051" groups="file"/>
  <syscall name="umount2" number="4052" groups="file"/>
  <syscall name="lock" number="4053"/>
  <syscall name="ioctl" number="4054" groups="descriptor"/>
  <syscall name="fcntl" number="4055" groups="descriptor"/>
  <syscall name="mpx" number="4056"/>
  <syscall name="setpgid" number="4057"/>
  <syscall name="ulimit" number="4058"/>
  <syscall name="umask" number="4060"/>
  <syscall name="chroot" number="4061" groups="file"/>
  <syscall name="ustat" number="4062"/>
  <syscall name="dup2" number="4063" groups="descriptor"/>
  <syscall name="getppid" number="4064"/>
  <syscall name="getpgrp" number="4065"/>
  <syscall name="setsid" number="4066"/>
  <syscall name="sigaction" number="4067" groups="signal"/>
  <syscall name="sgetmask" number="4068" groups="signal"/>
  <syscall name="ssetmask" number="4069" groups="signal"/>
  <syscall name="setreuid" number="4070"/>
  <syscall name="setregid" number="4071"/>
  <syscall name="sigsuspend" number="4072" groups="signal"/>
  <syscall name="sigpending" number="4073" groups="signal"/>
  <syscall name="sethostname" number="4074"/>
  <syscall name="setrlimit" number="4075"/>
  <syscall name="getrlimit" number="4076"/>
  <syscall name="getrusage" number="4077"/>
  <syscall name="gettimeofday" number="4078"/>
  <syscall name="settimeofday" number="4079"/>
  <syscall name="getgroups" number="4080"/>
  <syscall name="setgroups" number="4081"/>
  <syscall name="symlink" number="4083" groups="file"/>
  <syscall name="readlink" number="4085" groups="file"/>
  <syscall name="uselib" number="4086" groups="file"/>
  <syscall name="swapon" number="4087" groups="file"/>
  <syscall name="reboot" number="4088"/>
  <syscall name="readdir" number="4089" groups="descriptor"/>
  <syscall name="mmap" number="4090" groups="descriptor,memory"/>
  <syscall name="munmap" number="4091" groups="memory"/>
  <syscall name="truncate" number="4092" groups="file"/>
  <syscall name="ftruncate" number="4093" groups="descriptor"/>
  <syscall name="fchmod" number="4094" groups="descriptor"/>
  <syscall name="fchown" number="4095" groups="descriptor"/>
  <syscall name="getpriority" number="4096"/>
  <syscall name="setpriority" number="4097"/>
  <syscall name="profil" number="4098"/>
  <syscall name="statfs" number="4099" groups="file"/>
  <syscall name="fstatfs" number="4100" groups="descriptor"/>
  <syscall name="ioperm" number="4101"/>
  <syscall name="socketcall" number="4102" groups="descriptor"/>
  <syscall name="syslog" number="4103"/>
  <syscall name="setitimer" number="4104"/>
  <syscall name="getitimer" number="4105"/>
  <syscall name="stat" number="4106" groups="file"/>
  <syscall name="lstat" number="4107" groups="file"/>
  <syscall name="fstat" number="4108" groups="descriptor"/>
  <syscall name="iopl" number="4110"/>
  <syscall name="vhangup" number="4111"/>
  <syscall name="idle" number="4112"/>
  <syscall name="vm86" number="4113"/>
  <syscall name="wait4" number="4114" groups="process"/>
  <syscall name="swapoff" number="4115" groups="file"/>
  <syscall name="sysinfo" number="4116"/>
  <syscall name="ipc" number="4117" groups="ipc"/>
  <syscall name="fsync" number="4118" groups="descriptor"/>
  <syscall name="sigreturn" number="4119" groups="signal"/>
  <syscall name="clone" number="4120" groups="process"/>
  <syscall name="setdomainname" number="4121"/>
  <syscall name="uname" number="4122"/>
  <syscall name="modify_ldt" number="4123"/>
  <syscall name="adjtimex" number="4124"/>
  <syscall name="mprotect" number="4125" groups="memory"/>
  <syscall name="sigprocmask" number="4126" groups="signal"/>
  <syscall name="create_module" number="4127"/>
  <syscall name="init_module" number="4128"/>
  <syscall name="delete_module" number="4129"/>
  <syscall name="get_kernel_syms" number="4130"/>
  <syscall name="quotactl" number="4131" groups="file"/>
  <syscall name="getpgid" number="4132"/>
  <syscall name="fchdir" number="4133" groups="descriptor"/>
  <syscall name="bdflush" number="4134"/>
  <syscall name="sysfs" number="4135"/>
  <syscall name="personality" number="4136"/>
  <syscall name="afs_syscall" number="4137"/>
  <syscall name="setfsuid" number="4138"/>
  <syscall name="setfsgid" number="4139"/>
  <syscall name="_llseek" number="4140" groups="descriptor"/>
  <syscall name="getdents" number="4141" groups="descriptor"/>
  <syscall name="_newselect" number="4142" groups="descriptor"/>
  <syscall name="flock" number="4143" groups="descriptor"/>
  <syscall name="msync" number="4144" groups="memory"/>
  <syscall name="readv" number="4145" groups="descriptor"/>
  <syscall name="writev" number="4146" groups="descriptor"/>
  <syscall name="cacheflush" number="4147" groups="memory"/>
  <syscall name="cachectl" number="4148"/>
  <syscall name="sysmips" number="4149"/>
  <syscall name="getsid" number="4151"/>
  <syscall name="fdatasync" number="4152" groups="descriptor"/>
  <syscall name="_sysctl" number="4153"/>
  <syscall name="mlock" number="4154" groups="memory"/>
  <syscall name="munlock" number="4155" groups="memory"/>
  <syscall name="mlockall" number="4156" groups="memory"/>
  <syscall name="munlockall" number="4157" groups="memory"/>
  <syscall name="sched_setparam" number="4158"/>
  <syscall name="sched_getparam" number="4159"/>
  <syscall name="sched_setscheduler" number="4160"/>
  <syscall name="sched_getscheduler" number="4161"/>
  <syscall name="sched_yield" number="4162"/>
  <syscall name="sched_get_priority_max" number="4163"/>
  <syscall name="sched_get_priority_min" number="4164"/>
  <syscall name="sched_rr_get_interval" number="4165"/>
  <syscall name="nanosleep" number="4166"/>
  <syscall name="mremap" number="4167" groups="memory"/>
  <syscall name="accept" number="4168" groups="network"/>
  <syscall name="bind" number="4169" groups="network"/>
  <syscall name="connect" number="4170" groups="network"/>
  <syscall name="getpeername" number="4171" groups="network"/>
  <syscall name="getsockname" number="4172" groups="network"/>
  <syscall name="getsockopt" number="4173" groups="network"/>
  <syscall name="listen" number="4174" groups="network"/>
  <syscall name="recv" number="4175" groups="network"/>
  <syscall name="recvfrom" number="4176" groups="network"/>
  <syscall name="recvmsg" number="4177" groups="network"/>
  <syscall name="send" number="4178" groups="network"/>
  <syscall name="sendmsg" number="4179" groups="network"/>
  <syscall name="sendto" number="4180" groups="network"/>
  <syscall name="setsockopt" number="4181" groups="network"/>
  <syscall name="shutdown" number="4182" groups="network"/>
  <syscall name="socket" number="4183" groups="network"/>
  <syscall name="socketpair" number="4184" groups="network"/>
  <syscall name="setresuid" number="4185"/>
  <syscall name="getresuid" number="4186"/>
  <syscall name="query_module" number="4187"/>
  <syscall name="poll" number="4188" groups="descriptor"/>
  <syscall name="nfsservctl" number="4189"/>
  <syscall name="setresgid" number="4190"/>
  <syscall name="getresgid" number="4191"/>
  <syscall name="prctl" number="4192"/>
  <syscall name="rt_sigreturn" number="4193" groups="signal"/>
  <syscall name="rt_sigaction" number="4194" groups="signal"/>
  <syscall name="rt_sigprocmask" number="4195" groups="signal"/>
  <syscall name="rt_sigpending" number="4196" groups="signal"/>
  <syscall name="rt_sigtimedwait" number="4197" groups="signal"/>
  <syscall name="rt_sigqueueinfo" number="4198" groups="signal,process"/>
  <syscall name="rt_sigsuspend" number="4199" groups="signal"/>
  <syscall name="pread64" number="4200" groups="descriptor"/>
  <syscall name="pwrite64" number="4201" groups="descriptor"/>
  <syscall name="chown" number="4202" groups="file"/>
  <syscall name="getcwd" number="4203" groups="file"/>
  <syscall name="capget" number="4204"/>
  <syscall name="capset" number="4205"/>
  <syscall name="sigaltstack" number="4206" groups="signal"/>
  <syscall name="sendfile" number="4207" groups="descriptor,network"/>
  <syscall name="getpmsg" number="4208" groups="network"/>
  <syscall name="putpmsg" number="4209" groups="network"/>
  <syscall name="mmap2" number="4210" groups="descriptor,memory"/>
  <syscall name="truncate64" number="4211" groups="file"/>
  <syscall name="ftruncate64" number="4212" groups="descriptor"/>
  <syscall name="stat64" number="4213" groups="file"/>
  <syscall name="lstat64" number="4214" groups="file"/>
  <syscall name="fstat64" number="4215" groups="descriptor"/>
  <syscall name="pivot_root" number="4216" groups="file"/>
  <syscall name="mincore" number="4217" groups="memory"/>
  <syscall name="madvise" number="4218" groups="memory"/>
  <syscall name="getdents64" number="4219" groups="descriptor"/>
  <syscall name="fcntl64" number="4220" groups="descriptor"/>
  <syscall name="gettid" number="4222"/>
  <syscall name="readahead" number="4223" groups="descriptor"/>
  <syscall name="setxattr" number="4224" groups="file"/>
  <syscall name="lsetxattr" number="4225" groups="file"/>
  <syscall name="fsetxattr" number="4226" groups="descriptor"/>
  <syscall name="getxattr" number="4227" groups="file"/>
  <syscall name="lgetxattr" number="4228" groups="file"/>
  <syscall name="fgetxattr" number="4229" groups="descriptor"/>
  <syscall name="listxattr" number="4230" groups="file"/>
  <syscall name="llistxattr" number="4231" groups="file"/>
  <syscall name="flistxattr" number="4232" groups="descriptor"/>
  <syscall name="removexattr" number="4233" groups="file"/>
  <syscall name="lremovexattr" number="4234" groups="file"/>
  <syscall name="fremovexattr" number="4235" groups="descriptor"/>
  <syscall name="tkill" number="4236" groups="signal,process"/>
  <syscall name="sendfile64" number="4237" groups="descriptor,network"/>
  <syscall name="futex" number="4238"/>
  <syscall name="sched_setaffinity" number="4239"/>
  <syscall name="sched_getaffinity" number="4240"/>
  <syscall name="io_setup" number="4241" groups="memory"/>
  <syscall name="io_destroy" number="4242" groups="memory"/>
  <syscall name="io_getevents" number="4243"/>
  <syscall name="io_submit" number="4244"/>
  <syscall name="io_cancel" number="4245"/>
  <syscall name="exit_group" number="4246" groups="process"/>
  <syscall name="lookup_dcookie" number="4247"/>
  <syscall name="epoll_create" number="4248" groups="descriptor"/>
  <syscall name="epoll_ctl" number="4249" groups="descriptor"/>
  <syscall name="epoll_wait" number="4250" groups="descriptor"/>
  <syscall name="remap_file_pages" number="4251" groups="memory"/>
  <syscall name="set_tid_address" number="4252"/>
  <syscall name="restart_syscall" number="4253"/>
  <syscall name="fadvise64" number="4254" groups="descriptor"/>
  <syscall name="statfs64" number="4255" groups="file"/>
  <syscall name="fstatfs64" number="4256" groups="descriptor"/>
  <syscall name="timer_create" number="4257"/>
  <syscall name="timer_settime" number="4258"/>
  <syscall name="timer_gettime" number="4259"/>
  <syscall name="timer_getoverrun" number="4260"/>
  <syscall name="timer_delete" number="4261"/>
  <syscall name="clock_settime" number="4262"/>
  <syscall name="clock_gettime" number="4263"/>
  <syscall name="clock_getres" number="4264"/>
  <syscall name="clock_nanosleep" number="4265"/>
  <syscall name="tgkill" number="4266" groups="signal,process"/>
  <syscall name="utimes" number="4267" groups="file"/>
  <syscall name="mbind" number="4268" groups="memory"/>
  <syscall name="get_mempolicy" number="4269" groups="memory"/>
  <syscall name="set_mempolicy" number="4270" groups="memory"/>
  <syscall name="mq_open" number="4271" groups="descriptor"/>
  <syscall name="mq_unlink" number="4272"/>
  <syscall name="mq_timedsend" number="4273" groups="descriptor"/>
  <syscall name="mq_timedreceive" number="4274" groups="descriptor"/>
  <syscall name="mq_notify" number="4275" groups="descriptor"/>
  <syscall name="mq_getsetattr" number="4276" groups="descriptor"/>
  <syscall name="vserver" number="4277"/>
  <syscall name="waitid" number="4278" groups="process"/>
  <syscall name="add_key" number="4280"/>
  <syscall name="request_key" number="4281"/>
  <syscall name="keyctl" number="4282"/>
  <syscall name="set_thread_area" number="4283"/>
  <syscall name="inotify_init" number="4284" groups="descriptor"/>
  <syscall name="inotify_add_watch" number="4285" groups="descriptor,file"/>
  <syscall name="inotify_rm_watch" number="4286" groups="descriptor"/>
  <syscall name="migrate_pages" number="4287" groups="memory"/>
  <syscall name="openat" number="4288" groups="descriptor,file"/>
  <syscall name="mkdirat" number="4289" groups="descriptor,file"/>
  <syscall name="mknodat" number="4290" groups="descriptor,file"/>
  <syscall name="fchownat" number="4291" groups="descriptor,file"/>
  <syscall name="futimesat" number="4292" groups="descriptor,file"/>
  <syscall name="fstatat64" number="4293" groups="descriptor,file"/>
  <syscall name="unlinkat" number="4294" groups="descriptor,file"/>
  <syscall name="renameat" number="4295" groups="descriptor,file"/>
  <syscall name="linkat" number="4296" groups="descriptor,file"/>
  <syscall name="symlinkat" number="4297" groups="descriptor,file"/>
  <syscall name="readlinkat" number="4298" groups="descriptor,file"/>
  <syscall name="fchmodat" number="4299" groups="descriptor,file"/>
  <syscall name="faccessat" number="4300" groups="descriptor,file"/>
  <syscall name="pselect6" number="4301" groups="descriptor"/>
  <syscall name="ppoll" number="4302" groups="descriptor"/>
  <syscall name="unshare" number="4303"/>
  <syscall name="splice" number="4304" groups="descriptor"/>
  <syscall name="sync_file_range" number="4305" groups="descriptor"/>
  <syscall name="tee" number="4306" groups="descriptor"/>
  <syscall name="vmsplice" number="4307" groups="descriptor"/>
  <syscall name="move_pages" number="4308" groups="memory"/>
  <syscall name="set_robust_list" number="4309"/>
  <syscall name="get_robust_list" number="4310"/>
  <syscall name="kexec_load" number="4311"/>
  <syscall name="getcpu" number="4312"/>
  <syscall name="epoll_pwait" number="4313" groups="descriptor"/>
  <syscall name="ioprio_set" number="4314"/>
  <syscall name="ioprio_get" number="4315"/>
  <syscall name="utimensat" number="4316" groups="descriptor,file"/>
  <syscall name="signalfd" number="4317" groups="descriptor,signal"/>
  <syscall name="timerfd" number="4318" groups="descriptor"/>
  <syscall name="eventfd" number="4319" groups="descriptor"/>
  <syscall name="fallocate" number="4320" groups="descriptor"/>
  <syscall name="timerfd_create" number="4321" groups="descriptor"/>
  <syscall name="timerfd_gettime" number="4322" groups="descriptor"/>
  <syscall name="timerfd_settime" number="4323" groups="descriptor"/>
  <syscall name="signalfd4" number="4324" groups="descriptor,signal"/>
  <syscall name="eventfd2" number="4325" groups="descriptor"/>
  <syscall name="epoll_create1" number="4326" groups="descriptor"/>
  <syscall name="dup3" number="4327" groups="descriptor"/>
  <syscall name="pipe2" number="4328" groups="descriptor"/>
  <syscall name="inotify_init1" number="4329" groups="descriptor"/>
  <syscall name="preadv" number="4330" groups="descriptor"/>
  <syscall name="pwritev" number="4331" groups="descriptor"/>
  <syscall name="rt_tgsigqueueinfo" number="4332" groups="process,signal"/>
  <syscall name="perf_event_open" number="4333" groups="descriptor"/>
  <syscall name="accept4" number="4334" groups="network"/>
  <syscall name="recvmmsg" number="4335" groups="network"/>
  <syscall name="fanotify_init" number="4336" groups="descriptor"/>
  <syscall name="fanotify_mark" number="4337" groups="descriptor,file"/>
  <syscall name="prlimit64" number="4338"/>
  <syscall name="name_to_handle_at" number="4339" groups="descriptor,file"/>
  <syscall name="open_by_handle_at" number="4340" groups="descriptor"/>
  <syscall name="clock_adjtime" number="4341"/>
  <syscall name="syncfs" number="4342" groups="descriptor"/>
  <syscall name="sendmmsg" number="4343" groups="network"/>
  <syscall name="setns" number="4344" groups="descriptor"/>
  <syscall name="process_vm_readv" number="4345"/>
  <syscall name="process_vm_writev" number="4346"/>
  <syscall name="kcmp" number="4347"/>
  <syscall name="finit_module" number="4348" groups="descriptor"/>
  <syscall name="sched_setattr" number="4349"/>
  <syscall name="sched_getattr" number="4350"/>
  <syscall name="renameat2" number="4351" groups="descriptor,file"/>
  <syscall name="seccomp" number="4352"/>
  <syscall name="getrandom" number="4353"/>
  <syscall name="memfd_create" number="4354" groups="descriptor"/>
  <syscall name="bpf" number="4355" groups="descriptor"/>
  <syscall name="execveat" number="4356" groups="descriptor,file,process"/>
  <syscall name="userfaultfd" number="4357" groups="descriptor"/>
  <syscall name="membarrier" number="4358"/>
  <syscall name="mlock2" number="4359" groups="memory"/>
  <syscall name="copy_file_range" number="4360" groups="descriptor"/>
  <syscall name="preadv2" number="4361" groups="descriptor"/>
  <syscall name="pwritev2" number="4362" groups="descriptor"/>
  <syscall name="pkey_mprotect" number="4363" groups="memory"/>
  <syscall name="pkey_alloc" number="4364"/>
  <syscall name="pkey_free" number="4365"/>
  <syscall name="statx" number="4366" groups="descriptor,file"/>
  <syscall name="rseq" number="4367"/>
  <syscall name="io_pgetevents" number="4368"/>
  <syscall name="semget" number="4393" groups="ipc"/>
  <syscall name="semctl" number="4394" groups="ipc"/>
  <syscall name="shmget" number="4395" groups="ipc"/>
  <syscall name="shmctl" number="4396" groups="ipc"/>
  <syscall name="shmat" number="4397" groups="ipc,memory"/>
  <syscall name="shmdt" number="4398" groups="ipc,memory"/>
  <syscall name="msgget" number="4399" groups="ipc"/>
  <syscall name="msgsnd" number="4400" groups="ipc"/>
  <syscall name="msgrcv" number="4401" groups="ipc"/>
  <syscall name="msgctl" number="4402" groups="ipc"/>
  <syscall name="clock_gettime64" number="4403"/>
  <syscall name="clock_settime64" number="4404"/>
  <syscall name="clock_adjtime64" number="4405"/>
  <syscall name="clock_getres_time64" number="4406"/>
  <syscall name="clock_nanosleep_time64" number="4407"/>
  <syscall name="timer_gettime64" number="4408"/>
  <syscall name="timer_settime64" number="4409"/>
  <syscall name="timerfd_gettime64" number="4410" groups="descriptor"/>
  <syscall name="timerfd_settime64" number="4411" groups="descriptor"/>
  <syscall name="utimensat_time64" number="4412" groups="descriptor,file"/>
  <syscall name="pselect6_time64" number="4413" groups="descriptor"/>
  <syscall name="ppoll_time64" number="4414" groups="descriptor"/>
  <syscall name="io_pgetevents_time64" number="4416"/>
  <syscall name="recvmmsg_time64" number="4417" groups="network"/>
  <syscall name="mq_timedsend_time64" number="4418" groups="descriptor"/>
  <syscall name="mq_timedreceive_time64" number="4419" groups="descriptor"/>
  <syscall name="semtimedop_time64" number="4420" groups="ipc"/>
  <syscall name="rt_sigtimedwait_time64" number="4421" groups="signal"/>
  <syscall name="futex_time64" number="4422"/>
  <syscall name="sched_rr_get_interval_time64" number="4423"/>
  <syscall name="pidfd_send_signal" number="4424" groups="descriptor,signal,process"/>
  <syscall name="io_uring_setup" number="4425" groups="descriptor"/>
  <syscall name="io_uring_enter" number="4426" groups="descriptor,signal"/>
  <syscall name="io_uring_register" number="4427" groups="descriptor,memory"/>
  <syscall name="open_tree" number="4428" groups="descriptor,file"/>
  <syscall name="move_mount" number="4429" groups="descriptor,file"/>
  <syscall name="fsopen" number="4430" groups="descriptor"/>
  <syscall name="fsconfig" number="4431" groups="descriptor,file"/>
  <syscall name="fsmount" number="4432" groups="descriptor"/>
  <syscall name="fspick" number="4433" groups="descriptor,file"/>
  <syscall name="pidfd_open" number="4434" groups="descriptor"/>
  <syscall name="clone3" number="4435" groups="process"/>
  <syscall name="close_range" number="4436"/>
  <syscall name="openat2" number="4437" groups="descriptor,file"/>
  <syscall name="pidfd_getfd" number="4438" groups="descriptor"/>
  <syscall name="faccessat2" number="4439" groups="descriptor,file"/>
  <syscall name="process_madvise" number="4440" groups="descriptor"/>
  <syscall name="epoll_pwait2" number="4441" groups="descriptor"/>
  <syscall name="mount_setattr" number="4442" groups="descriptor,file"/>
  <syscall name="quotactl_fd" number="4443" groups="descriptor"/>
  <syscall name="landlock_create_ruleset" number="4444" groups="descriptor"/>
  <syscall name="landlock_add_rule" number="4445" groups="descriptor"/>
  <syscall name="landlock_restrict_self" number="4446" groups="descriptor"/>
  <syscall name="process_mrelease" number="4448" groups="descriptor"/>
  <syscall name="futex_waitv" number="4449"/>
  <syscall name="set_mempolicy_home_node" number="4450" groups="memory"/>
  <syscall name="cachestat" number="4451" groups="descriptor"/>
  <syscall name="fchmodat2" number="4452" groups="descriptor,file"/>
  <syscall name="map_shadow_stack" number="4453" groups="memory"/>
  <syscall name="futex_wake" number="4454"/>
  <syscall name="futex_wait" number="4455"/>
  <syscall name="futex_requeue" number="4456"/>
  <syscall name="statmount" number="4457"/>
  <syscall name="listmount" number="4458"/>
  <syscall name="lsm_get_self_attr" number="4459"/>
  <syscall name="lsm_set_self_attr" number="4460"/>
  <syscall name="lsm_list_modules" number="4461"/>
  <syscall name="mseal" number="4462" groups="memory"/>
  <syscall name="setxattrat" number="4463"/>
  <syscall name="getxattrat" number="4464"/>
  <syscall name="listxattrat" number="4465"/>
  <syscall name="removexattrat" number="4466"/>
</syscalls_info>
