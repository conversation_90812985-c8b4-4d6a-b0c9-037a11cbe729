_scriptlive_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-c'|'--command')
			compopt -o bashdefault
			COMPREPLY=( $(compgen -c -- $cur) )
			return 0
			;;
		'-d'|'--divisor'|'-m'|'--maxdelay')
			COMPREPLY=( $(compgen -W "digit" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--timing
				--log-in
				--log-io
				--log-timing
				--command
				--divisor
				--maxdelay
				--version
				--help"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	local IFS=$'\n'
	compopt -o filenames
	COMPREPLY=( $(compgen -f -- $cur) )
	return 0
}
complete -F _scriptlive_module scriptlive
