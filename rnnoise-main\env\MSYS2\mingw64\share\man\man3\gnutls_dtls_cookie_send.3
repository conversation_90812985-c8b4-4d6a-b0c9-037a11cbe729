.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dtls_cookie_send" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dtls_cookie_send \- API function
.SH SYNOPSIS
.B #include <gnutls/dtls.h>
.sp
.BI "int gnutls_dtls_cookie_send(gnutls_datum_t * " key ", void * " client_data ", size_t " client_data_size ", gnutls_dtls_prestate_st * " prestate ", gnutls_transport_ptr_t " ptr ", gnutls_push_func " push_func ");"
.SH ARGUMENTS
.IP "gnutls_datum_t * key" 12
is a random key to be used at cookie generation
.IP "void * client_data" 12
contains data identifying the client (i.e. address)
.IP "size_t client_data_size" 12
The size of client's data
.IP "gnutls_dtls_prestate_st * prestate" 12
The previous cookie returned by \fBgnutls_dtls_cookie_verify()\fP
.IP "gnutls_transport_ptr_t ptr" 12
A transport pointer to be used by  \fIpush_func\fP 
.IP "gnutls_push_func push_func" 12
A function that will be used to reply
.SH "DESCRIPTION"
This function can be used to prevent denial of service
attacks to a DTLS server by requiring the client to
reply using a cookie sent by this function. That way
it can be ensured that a client we allocated resources
for (i.e. \fBgnutls_session_t\fP) is the one that the 
original incoming packet was originated from.

This function must be called at the first incoming packet,
prior to allocating any resources and must be succeeded
by \fBgnutls_dtls_cookie_verify()\fP.
.SH "RETURNS"
the number of bytes sent, or a negative error code.  
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
