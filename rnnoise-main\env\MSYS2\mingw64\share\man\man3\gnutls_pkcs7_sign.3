.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_sign" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_sign \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_sign(gnutls_pkcs7_t " pkcs7 ", gnutls_x509_crt_t " signer ", gnutls_privkey_t " signer_key ", const gnutls_datum_t * " data ", gnutls_pkcs7_attrs_t " signed_attrs ", gnutls_pkcs7_attrs_t " unsigned_attrs ", gnutls_digest_algorithm_t " dig ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_t pkcs7" 12
should contain a \fBgnutls_pkcs7_t\fP type
.IP "gnutls_x509_crt_t signer" 12
the certificate to sign the structure
.IP "gnutls_privkey_t signer_key" 12
the key to sign the structure
.IP "const gnutls_datum_t * data" 12
The data to be signed or \fBNULL\fP if the data are already embedded
.IP "gnutls_pkcs7_attrs_t signed_attrs" 12
Any additional attributes to be included in the signed ones (or \fBNULL\fP)
.IP "gnutls_pkcs7_attrs_t unsigned_attrs" 12
Any additional attributes to be included in the unsigned ones (or \fBNULL\fP)
.IP "gnutls_digest_algorithm_t dig" 12
The digest algorithm to use for signing
.IP "unsigned flags" 12
Should be zero or one of \fBGNUTLS_PKCS7\fP flags
.SH "DESCRIPTION"
This function will add a signature in the provided PKCS \fB7\fP structure
for the provided data. Multiple signatures can be made with different
signers.

The available flags are:
\fBGNUTLS_PKCS7_EMBED_DATA\fP, \fBGNUTLS_PKCS7_INCLUDE_TIME\fP, \fBGNUTLS_PKCS7_INCLUDE_CERT\fP,
and \fBGNUTLS_PKCS7_WRITE_SPKI\fP. They are explained in the \fBgnutls_pkcs7_sign_flags\fP
definition.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
