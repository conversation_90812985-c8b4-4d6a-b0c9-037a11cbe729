<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_CIPHER_CTX_get_cipher_data</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_CIPHER_CTX_get_cipher_data, EVP_CIPHER_CTX_set_cipher_data - Routines to inspect and modify EVP_CIPHER_CTX objects</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

void *EVP_CIPHER_CTX_get_cipher_data(const EVP_CIPHER_CTX *ctx);
void *EVP_CIPHER_CTX_set_cipher_data(EVP_CIPHER_CTX *ctx, void *cipher_data);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP_CIPHER_CTX_get_cipher_data() function returns a pointer to the cipher data relevant to EVP_CIPHER_CTX. The contents of this data is specific to the particular implementation of the cipher. For example this data can be used by engines to store engine specific information. The data is automatically allocated and freed by OpenSSL, so applications and engines should not normally free this directly (but see below).</p>

<p>The EVP_CIPHER_CTX_set_cipher_data() function allows an application or engine to replace the cipher data with new data. A pointer to any existing cipher data is returned from this function. If the old data is no longer required then it should be freed through a call to OPENSSL_free().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The EVP_CIPHER_CTX_get_cipher_data() function returns a pointer to the current cipher data for the EVP_CIPHER_CTX.</p>

<p>The EVP_CIPHER_CTX_set_cipher_data() function returns a pointer to the old cipher data for the EVP_CIPHER_CTX.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The EVP_CIPHER_CTX_get_cipher_data() and EVP_CIPHER_CTX_set_cipher_data() functions were added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


