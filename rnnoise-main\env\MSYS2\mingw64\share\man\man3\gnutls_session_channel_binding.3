.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_channel_binding" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_channel_binding \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_channel_binding(gnutls_session_t " session ", gnutls_channel_binding_t " cbtype ", gnutls_datum_t * " cb ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_channel_binding_t cbtype" 12
an \fBgnutls_channel_binding_t\fP enumeration type
.IP "gnutls_datum_t * cb" 12
output buffer array with data
.SH "DESCRIPTION"
Extract given channel binding data of the  \fIcbtype\fP (e.g.,
\fBGNUTLS_CB_TLS_UNIQUE\fP) type.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success,
\fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP if the  \fIcbtype\fP is unsupported,
\fBGNUTLS_E_CHANNEL_BINDING_NOT_AVAILABLE\fP if the data is not
currently available, or an error code.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
