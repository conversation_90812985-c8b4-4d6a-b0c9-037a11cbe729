.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_compression_get_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_compression_get_id \- API function
.SH SYNOPSIS
.B #include <gnutls/compat.h>
.sp
.BI "gnutls_compression_method_t gnutls_compression_get_id(const char * " name ");"
.SH ARGUMENTS
.IP "const char * name" 12
is a compression method name
.SH "DESCRIPTION"
The names are compared in a case insensitive way.
.SH "RETURNS"
an id of the specified in a string compression method, or
\fBGNUTLS_COMP_UNKNOWN\fP on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
