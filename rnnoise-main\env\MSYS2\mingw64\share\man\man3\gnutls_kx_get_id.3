.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_kx_get_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_kx_get_id \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_kx_algorithm_t gnutls_kx_get_id(const char * " name ");"
.SH ARGUMENTS
.IP "const char * name" 12
is a KX name
.SH "DESCRIPTION"
Convert a string to a \fBgnutls_kx_algorithm_t\fP value.  The names are
compared in a case insensitive way.
.SH "RETURNS"
an id of the specified KX algorithm, or \fBGNUTLS_KX_UNKNOWN\fP
on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
