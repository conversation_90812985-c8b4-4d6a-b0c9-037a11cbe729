.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srp_set_server_credentials_file" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srp_set_server_credentials_file \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srp_set_server_credentials_file(gnutls_srp_server_credentials_t " res ", const char * " password_file ", const char * " password_conf_file ");"
.SH ARGUMENTS
.IP "gnutls_srp_server_credentials_t res" 12
is a \fBgnutls_srp_server_credentials_t\fP type.
.IP "const char * password_file" 12
is the SRP password file (tpasswd)
.IP "const char * password_conf_file" 12
is the SRP password conf file (tpasswd.conf)
.SH "DESCRIPTION"
This function sets the password files, in a
\fBgnutls_srp_server_credentials_t\fP type.  Those password files
hold usernames and verifiers and will be used for SRP
authentication.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, or an
error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
