/* This file is part of the KDE project
   Copyright (C) 2007 Shane King

   This program is free software; you can redistribute it and/or
   modify it under the terms of the GNU Library General Public
   License as published by the Free Software Foundation; either
   version 2 of the License, or (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   <PERSON><PERSON><PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
   Library General Public License for more details.

   You should have received a copy of the GNU Library General Public License
   along with this program; see the file COPYING.  If not, write to
   the Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor,
   Boston, MA 02110-1301, USA.
*/

import "unknwn.idl";

cpp_quote("DEFINE_GUID(CLSID_DMOWrapperFilter,  0x94297043, 0xbd82, 0x4dfd, 0xb0, 0xde, 0x81, 0x77, 0x73, 0x9c, 0x6d, 0x20);")

[
    object,
    uuid(52d6f586-9f0f-4824-8fc8-e32ca04930c2)
]
interface IDMOWrapperFilter : IUnknown
{
    HRESULT Init(
      REFCLSID clsidDMO,
      REFCLSID catDMO
    );
}
