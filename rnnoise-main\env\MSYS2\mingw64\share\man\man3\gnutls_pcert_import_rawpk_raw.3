.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pcert_import_rawpk_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pcert_import_rawpk_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pcert_import_rawpk_raw(gnutls_pcert_st * " pcert ", const gnutls_datum_t * " rawpubkey ", gnutls_x509_crt_fmt_t " format ", unsigned int " key_usage ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pcert_st * pcert" 12
The pcert structure to import the data into.
.IP "const gnutls_datum_t * rawpubkey" 12
The raw public\-key in \fBgnutls_datum_t\fP format to be imported.
.IP "gnutls_x509_crt_fmt_t format" 12
The format of the raw public\-key. DER or PEM.
.IP "unsigned int key_usage" 12
An ORed sequence of \fBGNUTLS_KEY_\fP* flags.
.IP "unsigned int flags" 12
zero for now
.SH "DESCRIPTION"
This convenience function will import (i.e. convert) the given raw
public key  \fIrawpubkey\fP into a \fBgnutls_pcert_st\fP structure. The structure
must be deinitialized afterwards using \fBgnutls_pcert_deinit()\fP.
Note that the caller is responsible for freeing  \fIrawpubkey\fP . All necessary
values will be copied into  \fIpcert\fP .

Key usage (as defined by X.509 extension (*********)) can be explicitly
set because there is no certificate structure around the key to define
this value. See for more info \fBgnutls_x509_crt_get_key_usage()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.6.6
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
