Authors of MPFR (in chronological order of initial contribution):

<PERSON> author
<PERSON><PERSON><PERSON>       Original version of mul_ui.c, gmp_op.c
<PERSON>         Main author
<PERSON><PERSON><PERSON>            Original version of agm.c and log.c
<PERSON><PERSON><PERSON>           Original version of zeta.c
<PERSON>        Original version of exp3.c, const_pi.c, sincos.c
<PERSON><PERSON>          acos.c, asin.c, atan.c and early gamma.c
<PERSON>         Main author
<PERSON>yperbolic and inverse hyperbolic functions, base-2
                        and base-10 exponential and logarithm, factorial
<PERSON>        Rewritten get_str.c
<PERSON><PERSON><PERSON> Me<PERSON>er         Error function (erf.c)
<PERSON>       Main author
<PERSON>          Original version of sum.c
<PERSON>ction mpfr_get_ld_2exp
<PERSON> author
<PERSON><PERSON><PERSON><PERSON>      Original version of ai.c
<PERSON>          mpfr_nrandom and mpfr_erandom functions
<PERSON><PERSON>       New version of mpfr_const_euler
<PERSON><PERSON>       MPFRbench program
Jianyang Pan            Formally proven code for mpfr_add1sp1

The main authors are included in the MPFR mailing-list <<EMAIL>>.
This is the preferred way to contact us. For further information, please
look at the MPFR web page <https://www.mpfr.org/>.
