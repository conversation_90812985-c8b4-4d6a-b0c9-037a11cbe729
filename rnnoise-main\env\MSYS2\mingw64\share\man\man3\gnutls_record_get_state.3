.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_get_state" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_get_state \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_record_get_state(gnutls_session_t " session ", unsigned " read ", gnutls_datum_t * " mac_key ", gnutls_datum_t * " IV ", gnutls_datum_t * " cipher_key ", unsigned char [8] " seq_number ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type
.IP "unsigned read" 12
if non\-zero the read parameters are returned, otherwise the write
.IP "gnutls_datum_t * mac_key" 12
the key used for MAC (if a MAC is used)
.IP "gnutls_datum_t * IV" 12
the initialization vector or nonce used
.IP "gnutls_datum_t * cipher_key" 12
the cipher key
.IP "unsigned char [8] seq_number" 12
A 64\-bit sequence number
.SH "DESCRIPTION"
This function will return the parameters of the current record state.
These are only useful to be provided to an external off\-loading device
or subsystem. The returned values should be considered constant
and valid for the lifetime of the session.

In that case, to sync the state back you must call \fBgnutls_record_set_state()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.

Since 3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
