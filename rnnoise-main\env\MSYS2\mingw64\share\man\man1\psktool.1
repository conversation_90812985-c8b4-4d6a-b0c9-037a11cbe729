.de1 NOP
.  it 1 an-trap
.  if \\n[.$] \,\\$*\/
..
.ie t \
.ds B-Font [CB]
.ds I-Font [CI]
.ds R-Font [CR]
.el \
.ds B-Font B
.ds I-Font I
.ds R-Font R
.TH psktool 1 "08 Feb 2025" "3.8.9" "User Commands"
.SH NAME
\f\*[B-Font]psktool\fP
\- GnuTLS PSK tool
.SH SYNOPSIS
\f\*[B-Font]psktool\fP
.\" Mixture of short (flag) options and long options
[\f\*[B-Font]\-flags\f[]]
[\f\*[B-Font]\-flag\f[] [\f\*[I-Font]value\f[]]]
[\f\*[B-Font]\-\-option-name\f[][[=| ]\f\*[I-Font]value\f[]]]
.sp \n(Ppu
.ne 2

All arguments must be options.
.sp \n(Ppu
.ne 2
.SH "DESCRIPTION"
Program  that generates random keys for use with TLS-PSK. The
keys are stored in hexadecimal format in a key file.
.sp
.SH "OPTIONS"
.TP
.NOP \f\*[B-Font]\-d\f[] \f\*[I-Font]num\f[], \f\*[B-Font]\-\-debug\f[]=\f\*[I-Font]num\f[]
Enable debugging.
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 9999
.fi
.in -4
.sp
Specifies the debug level.
.TP
.NOP \f\*[B-Font]\-s\f[] \f\*[I-Font]num\f[], \f\*[B-Font]\-\-keysize\f[]=\f\*[I-Font]num\f[]
Specify the key size in bytes (default is 32-bytes or 256-bits).
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 512
.fi
.in -4
.sp
.TP
.NOP \f\*[B-Font]\-u\f[] \f\*[I-Font]str\f[], \f\*[B-Font]\-\-username\f[]=\f\*[I-Font]str\f[]
Specify the username to use.
.sp
.TP
.NOP \f\*[B-Font]\-p\f[] \f\*[I-Font]str\f[], \f\*[B-Font]\-\-pskfile\f[]=\f\*[I-Font]str\f[]
Specify a pre-shared key file.
.sp
This option will specify the pre\-shared key file to store the generated keys.
.TP
.NOP \f\*[B-Font]\-\-passwd\f[]
This is an alias for the \fI--pskfile\fR option.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-v\f[] \f\*[I-Font]arg\f[], \f\*[B-Font]\-\-version\f[]=\f\*[I-Font]arg\f[]
Output version of program and exit.  The default mode is `v', a simple
version.  The `c' mode will print copyright information and `n' will
print the full copyright notice.
.TP
.NOP \f\*[B-Font]\-h\f[], \f\*[B-Font]\-\-help\f[]
Display usage information and exit.
.TP
.NOP \f\*[B-Font]\-!\f[], \f\*[B-Font]\-\-more\-help\f[]
Pass the extended usage information through a pager.

.sp
.SH EXAMPLES
To add a user 'psk_identity' in \fIkeys.psk\fP for use with GnuTLS run:
.br
.in +4
.nf
$ ./psktool \-u psk_identity \-p keys.psk
Generating a random key for user 'psk_identity'
Key stored to keys.psk
$ cat keys.psk
psk_identity:88f3824b3e5659f52d00e959bacab954b6540344
$
.in -4
.fi
.sp
This command will create \fIkeys.psk\fP if it does not exist
and will add user 'psk_identity'.
.SH "EXIT STATUS"
One of the following exit values will be returned:
.TP
.NOP 0 " (EXIT_SUCCESS)"
Successful program execution.
.TP
.NOP 1 " (EXIT_FAILURE)"
The operation failed or the command syntax was not valid.
.PP
.SH "SEE ALSO"
gnutls\-cli\-debug (1), gnutls\-serv (1), srptool (1), certtool (1)
.SH "AUTHORS"

.SH "COPYRIGHT"
Copyright (C) 2020-2023 Free Software Foundation, and others all rights reserved.
This program is released under the terms of
the GNU General Public License, version 3 or later
.
.SH "BUGS"
Please send bug reports to: <EMAIL>
