set ::msgcat::header "Project-Id-Version: Git Russian Localization Project\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2020-11-05 11:20+0000\nLast-Translator: <PERSON><PERSON> <<EMAIL>>\nLanguage-Team: Russian (http://www.transifex.com/djm00n/git-po-ru/language/ru/)\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nLanguage: ru\nPlural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"
::msgcat::mcset ru "Invalid font specified in %s:" "\u0412 %s \u0443\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d \u043d\u0435\u0432\u0435\u0440\u043d\u044b\u0439 \u0448\u0440\u0438\u0444\u0442:"
::msgcat::mcset ru "Main Font" "\u0428\u0440\u0438\u0444\u0442 \u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441\u0430"
::msgcat::mcset ru "Diff/Console Font" "\u0428\u0440\u0438\u0444\u0442 \u043a\u043e\u043d\u0441\u043e\u043b\u0438 \u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 (diff)"
::msgcat::mcset ru "git-gui: fatal error" "git-gui: \u043a\u0440\u0438\u0442\u0438\u0447\u0435\u0441\u043a\u0430\u044f \u043e\u0448\u0438\u0431\u043a\u0430"
::msgcat::mcset ru "Cannot find git in PATH." "git \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d \u0432 PATH."
::msgcat::mcset ru "Cannot parse Git version string:" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0440\u0430\u0441\u043f\u043e\u0437\u043d\u0430\u0442\u044c \u0441\u0442\u0440\u043e\u043a\u0443 \u0432\u0435\u0440\u0441\u0438\u0438 Git: "
::msgcat::mcset ru "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0438\u0442\u044c \u0432\u0435\u0440\u0441\u0438\u044e Git\n\n%s \u0443\u043a\u0430\u0437\u044b\u0432\u0430\u0435\u0442 \u043d\u0430 \u0432\u0435\u0440\u0441\u0438\u044e \u00ab%s\u00bb.\n\n\u0434\u043b\u044f %s \u0442\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044f \u0432\u0435\u0440\u0441\u0438\u044f Git, \u043d\u0430\u0447\u0438\u043d\u0430\u044f \u0441 1.5.0\n\n\u041f\u0440\u0435\u0434\u043f\u043e\u043b\u043e\u0436\u0438\u0442\u044c, \u0447\u0442\u043e \u00ab%s\u00bb \u0438 \u0435\u0441\u0442\u044c \u0432\u0435\u0440\u0441\u0438\u044f 1.5.0?\n"
::msgcat::mcset ru "Git directory not found:" "\u041a\u0430\u0442\u0430\u043b\u043e\u0433 Git \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d:"
::msgcat::mcset ru "Cannot move to top of working directory:" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u043f\u0435\u0440\u0435\u0439\u0442\u0438 \u043a \u043a\u043e\u0440\u043d\u044e \u0440\u0430\u0431\u043e\u0447\u0435\u0433\u043e \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0430 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f: "
::msgcat::mcset ru "Cannot use bare repository:" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f \u0431\u0435\u0437 \u0440\u0430\u0431\u043e\u0447\u0435\u0433\u043e \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0430:"
::msgcat::mcset ru "No working directory" "\u041e\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u0435\u0442 \u0440\u0430\u0431\u043e\u0447\u0438\u0439 \u043a\u0430\u0442\u0430\u043b\u043e\u0433"
::msgcat::mcset ru "Refreshing file status..." "\u041e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u0438 \u043e \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u2026"
::msgcat::mcset ru "Scanning for modified files ..." "\u041f\u043e\u0438\u0441\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u043d\u044b\u0445 \u0444\u0430\u0439\u043b\u043e\u0432\u2026"
::msgcat::mcset ru "Calling prepare-commit-msg hook..." "\u0412\u044b\u0437\u043e\u0432 \u043f\u0435\u0440\u0435\u0445\u0432\u0430\u0442\u0447\u0438\u043a\u0430 prepare-commit-msg\u2026"
::msgcat::mcset ru "Commit declined by prepare-commit-msg hook." "\u041a\u043e\u043c\u043c\u0438\u0442 \u043f\u0440\u0435\u0440\u0432\u0430\u043d \u043f\u0435\u0440\u0435\u0445\u0432\u0430\u0442\u0447\u0438\u043a\u043e\u043c prepare-commit-msg."
::msgcat::mcset ru "Ready." "\u0413\u043e\u0442\u043e\u0432\u043e."
::msgcat::mcset ru "Display limit (gui.maxfilesdisplayed = %s) reached, not showing all %s files." "\u041b\u0438\u043c\u0438\u0442 \u043e\u0442\u043e\u0431\u0440\u0430\u0436\u0430\u0435\u043c\u044b\u0445 \u0444\u0430\u0439\u043b\u043e\u0432 \u0434\u043e\u0441\u0442\u0438\u0433\u043d\u0443\u0442 (gui.maxfilesdisplayed = %s), \u043d\u0435 \u0432\u0441\u0435 %s \u0444\u0430\u0439\u043b\u044b \u043f\u043e\u043a\u0430\u0437\u0430\u043d\u044b."
::msgcat::mcset ru "Unmodified" "\u041d\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u043e"
::msgcat::mcset ru "Modified, not staged" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u043e, \u043d\u0435 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435"
::msgcat::mcset ru "Staged for commit" "\u0412 \u0438\u043d\u0434\u0435\u043a\u0441\u0435 \u0434\u043b\u044f \u043a\u043e\u043c\u043c\u0438\u0442\u0430"
::msgcat::mcset ru "Portions staged for commit" "\u0427\u0430\u0441\u0442\u0438, \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435 \u0434\u043b\u044f \u043a\u043e\u043c\u043c\u0438\u0442\u0430"
::msgcat::mcset ru "Staged for commit, missing" "\u0412 \u0438\u043d\u0434\u0435\u043a\u0441\u0435 \u0434\u043b\u044f \u043a\u043e\u043c\u043c\u0438\u0442\u0430, \u043e\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u0435\u0442"
::msgcat::mcset ru "File type changed, not staged" "\u0422\u0438\u043f \u0444\u0430\u0439\u043b\u0430 \u0438\u0437\u043c\u0435\u043d\u0451\u043d, \u043d\u0435 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435"
::msgcat::mcset ru "File type changed, old type staged for commit" "\u0422\u0438\u043f \u0444\u0430\u0439\u043b\u0430 \u0438\u0437\u043c\u0435\u043d\u0451\u043d, \u0441\u0442\u0430\u0440\u044b\u0439 \u0442\u0438\u043f \u0444\u0430\u0439\u043b\u0430 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435"
::msgcat::mcset ru "File type changed, staged" "\u0422\u0438\u043f \u0444\u0430\u0439\u043b\u0430 \u0438\u0437\u043c\u0435\u043d\u0451\u043d, \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435"
::msgcat::mcset ru "File type change staged, modification not staged" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435 \u0442\u0438\u043f\u0430 \u0444\u0430\u0439\u043b\u0430 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435, \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435 \u043d\u0435 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435"
::msgcat::mcset ru "File type change staged, file missing" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435 \u0442\u0438\u043f\u0430 \u0444\u0430\u0439\u043b\u0430 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435, \u0444\u0430\u0439\u043b \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d"
::msgcat::mcset ru "Untracked, not staged" "\u041d\u0435 \u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u0442\u0441\u044f, \u043d\u0435 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435"
::msgcat::mcset ru "Missing" "\u041e\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u0435\u0442"
::msgcat::mcset ru "Staged for removal" "\u0412 \u0438\u043d\u0434\u0435\u043a\u0441\u0435 \u0434\u043b\u044f \u0443\u0434\u0430\u043b\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Staged for removal, still present" "\u0412 \u0438\u043d\u0434\u0435\u043a\u0441\u0435 \u0434\u043b\u044f \u0443\u0434\u0430\u043b\u0435\u043d\u0438\u044f, \u0435\u0449\u0435 \u043d\u0435 \u0443\u0434\u0430\u043b\u0435\u043d\u043e"
::msgcat::mcset ru "Requires merge resolution" "\u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044f \u0440\u0430\u0437\u0440\u0435\u0448\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0430 \u043f\u0440\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u0438"
::msgcat::mcset ru "Couldn't find gitk in PATH" "gitk \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d \u0432 PATH."
::msgcat::mcset ru "Starting %s... please wait..." "\u0417\u0430\u043f\u0443\u0441\u043a\u0430\u0435\u0442\u0441\u044f %s\u2026 \u041f\u043e\u0434\u043e\u0436\u0434\u0438\u0442\u0435, \u043f\u043e\u0436\u0430\u043b\u0443\u0439\u0441\u0442\u0430\u2026"
::msgcat::mcset ru "Couldn't find git gui in PATH" "git gui \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d \u0432 PATH."
::msgcat::mcset ru "Repository" "\u0420\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439"
::msgcat::mcset ru "Edit" "\u041f\u0440\u0430\u0432\u043a\u0430"
::msgcat::mcset ru "Branch" "\u0412\u0435\u0442\u043a\u0430"
::msgcat::mcset ru "Commit@@noun" "\u041a\u043e\u043c\u043c\u0438\u0442"
::msgcat::mcset ru "Merge" "\u0421\u043b\u0438\u044f\u043d\u0438\u0435"
::msgcat::mcset ru "Remote" "\u0412\u043d\u0435\u0448\u043d\u0438\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0438"
::msgcat::mcset ru "Tools" "\u0412\u0441\u043f\u043e\u043c\u043e\u0433\u0430\u0442\u0435\u043b\u044c\u043d\u044b\u0435 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438"
::msgcat::mcset ru "Explore Working Copy" "\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0440\u0430\u0431\u043e\u0447\u0435\u0433\u043e \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0430"
::msgcat::mcset ru "Git Bash" "Git Bash"
::msgcat::mcset ru "Browse Current Branch's Files" "\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0435\u0442\u044c \u0444\u0430\u0439\u043b\u044b \u0442\u0435\u043a\u0443\u0449\u0435\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Browse Branch Files..." "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0444\u0430\u0439\u043b\u044b \u0432\u0435\u0442\u043a\u0438\u2026"
::msgcat::mcset ru "Visualize Current Branch's History" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0438\u0441\u0442\u043e\u0440\u0438\u044e \u0442\u0435\u043a\u0443\u0449\u0435\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Visualize All Branch History" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0438\u0441\u0442\u043e\u0440\u0438\u044e \u0432\u0441\u0435\u0445 \u0432\u0435\u0442\u043e\u043a"
::msgcat::mcset ru "Browse %s's Files" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0444\u0430\u0439\u043b\u044b \u0432\u0435\u0442\u043a\u0438 %s"
::msgcat::mcset ru "Visualize %s's History" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0438\u0441\u0442\u043e\u0440\u0438\u044e \u0432\u0435\u0442\u043a\u0438 %s"
::msgcat::mcset ru "Database Statistics" "\u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043a\u0430 \u0431\u0430\u0437\u044b \u0434\u0430\u043d\u043d\u044b\u0445"
::msgcat::mcset ru "Compress Database" "\u0421\u0436\u0430\u0442\u044c \u0431\u0430\u0437\u0443 \u0434\u0430\u043d\u043d\u044b\u0445"
::msgcat::mcset ru "Verify Database" "\u041f\u0440\u043e\u0432\u0435\u0440\u0438\u0442\u044c \u0431\u0430\u0437\u0443 \u0434\u0430\u043d\u043d\u044b\u0445"
::msgcat::mcset ru "Create Desktop Icon" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c \u044f\u0440\u043b\u044b\u043a \u043d\u0430 \u0440\u0430\u0431\u043e\u0447\u0435\u043c \u0441\u0442\u043e\u043b\u0435"
::msgcat::mcset ru "Quit" "\u0412\u044b\u0445\u043e\u0434"
::msgcat::mcset ru "Undo" "\u041e\u0442\u043c\u0435\u043d\u0438\u0442\u044c"
::msgcat::mcset ru "Redo" "\u041f\u043e\u0432\u0442\u043e\u0440\u0438\u0442\u044c"
::msgcat::mcset ru "Cut" "\u0412\u044b\u0440\u0435\u0437\u0430\u0442\u044c"
::msgcat::mcset ru "Copy" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c"
::msgcat::mcset ru "Paste" "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044c"
::msgcat::mcset ru "Delete" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c"
::msgcat::mcset ru "Select All" "\u0412\u044b\u0434\u0435\u043b\u0438\u0442\u044c \u0432\u0441\u0451"
::msgcat::mcset ru "Create..." "\u0421\u043e\u0437\u0434\u0430\u0442\u044c\u2026"
::msgcat::mcset ru "Checkout..." "\u041f\u0435\u0440\u0435\u0439\u0442\u0438\u2026"
::msgcat::mcset ru "Rename..." "\u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u0442\u044c\u2026"
::msgcat::mcset ru "Delete..." "\u0423\u0434\u0430\u043b\u0438\u0442\u044c\u2026"
::msgcat::mcset ru "Reset..." "\u0421\u0431\u0440\u043e\u0441\u0438\u0442\u044c\u2026"
::msgcat::mcset ru "Done" "\u0417\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u043e"
::msgcat::mcset ru "Commit@@verb" "\u0417\u0430\u043a\u043e\u043c\u043c\u0438\u0442\u0438\u0442\u044c"
::msgcat::mcset ru "Amend Last Commit" "\u0418\u0441\u043f\u0440\u0430\u0432\u0438\u0442\u044c \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0439 \u043a\u043e\u043c\u043c\u0438\u0442"
::msgcat::mcset ru "Rescan" "\u041f\u0435\u0440\u0435\u0447\u0438\u0442\u0430\u0442\u044c"
::msgcat::mcset ru "Stage To Commit" "\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u0432 \u0438\u043d\u0434\u0435\u043a\u0441"
::msgcat::mcset ru "Stage Changed Files To Commit" "\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0451\u043d\u043d\u044b\u0435 \u0444\u0430\u0439\u043b\u044b \u0432 \u0438\u043d\u0434\u0435\u043a\u0441"
::msgcat::mcset ru "Unstage From Commit" "\u0423\u0431\u0440\u0430\u0442\u044c \u0438\u0437 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset ru "Revert Changes" "\u041e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Show Less Context" "\u041c\u0435\u043d\u044c\u0448\u0435 \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442\u0430"
::msgcat::mcset ru "Show More Context" "\u0411\u043e\u043b\u044c\u0448\u0435 \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442\u0430"
::msgcat::mcset ru "Sign Off" "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044c Signed-off-by"
::msgcat::mcset ru "Local Merge..." "\u041b\u043e\u043a\u0430\u043b\u044c\u043d\u043e\u0435 \u0441\u043b\u0438\u044f\u043d\u0438\u0435\u2026"
::msgcat::mcset ru "Abort Merge..." "\u041f\u0440\u0435\u0440\u0432\u0430\u0442\u044c \u0441\u043b\u0438\u044f\u043d\u0438\u0435\u2026"
::msgcat::mcset ru "Add..." "\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c\u2026"
::msgcat::mcset ru "Push..." "\u041e\u0442\u043f\u0440\u0430\u0432\u0438\u0442\u044c\u2026"
::msgcat::mcset ru "Delete Branch..." "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u0432\u0435\u0442\u043a\u0443\u2026"
::msgcat::mcset ru "Options..." "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438\u2026"
::msgcat::mcset ru "Remove..." "\u0423\u0434\u0430\u043b\u0438\u0442\u044c\u2026"
::msgcat::mcset ru "Help" "\u0421\u043f\u0440\u0430\u0432\u043a\u0430"
::msgcat::mcset ru "About %s" "\u041e %s"
::msgcat::mcset ru "Online Documentation" "\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u044f \u0432 \u0438\u043d\u0442\u0435\u0440\u043d\u0435\u0442\u0435"
::msgcat::mcset ru "Show SSH Key" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u043a\u043b\u044e\u0447 SSH"
::msgcat::mcset ru "usage:" "\u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u0435:"
::msgcat::mcset ru "Usage" "\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u0435"
::msgcat::mcset ru "Error" "\u041e\u0448\u0438\u0431\u043a\u0430"
::msgcat::mcset ru "fatal: cannot stat path %s: No such file or directory" "\u043a\u0440\u0438\u0442\u0438\u0447\u0435\u0441\u043a\u0430\u044f \u043e\u0448\u0438\u0431\u043a\u0430: %s: \u043d\u0435\u0442 \u0442\u0430\u043a\u043e\u0433\u043e \u0444\u0430\u0439\u043b\u0430 \u0438\u043b\u0438 \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0430"
::msgcat::mcset ru "Current Branch:" "\u0422\u0435\u043a\u0443\u0449\u0430\u044f \u0432\u0435\u0442\u043a\u0430:"
::msgcat::mcset ru "Unstaged Changes" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u043e (\u043d\u0435 \u0431\u0443\u0434\u0435\u0442 \u0441\u043e\u0445\u0440\u0430\u043d\u0435\u043d\u043e)"
::msgcat::mcset ru "Staged Changes (Will Commit)" "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435 (\u0431\u0443\u0434\u0443\u0442 \u0437\u0430\u043a\u043e\u043c\u043c\u0438\u0447\u0435\u043d\u044b)"
::msgcat::mcset ru "Stage Changed" "\u0418\u043d\u0434\u0435\u043a\u0441\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0432\u0441\u0451"
::msgcat::mcset ru "Push" "\u041e\u0442\u043f\u0440\u0430\u0432\u0438\u0442\u044c"
::msgcat::mcset ru "Initial Commit Message:" "\u0421\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0435\u0440\u0432\u043e\u0433\u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0430:"
::msgcat::mcset ru "Amended Commit Message:" "\u0421\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u043d\u043e\u0433\u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0430:"
::msgcat::mcset ru "Amended Initial Commit Message:" "\u0421\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u043d\u043e\u0433\u043e \u043f\u0435\u0440\u0432\u043e\u0433\u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0430:"
::msgcat::mcset ru "Amended Merge Commit Message:" "\u0421\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u043d\u043e\u0433\u043e \u0441\u043b\u0438\u044f\u043d\u0438\u044f:"
::msgcat::mcset ru "Merge Commit Message:" "\u0421\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u0441\u043b\u0438\u044f\u043d\u0438\u044f:"
::msgcat::mcset ru "Commit Message:" "\u0421\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u0430:"
::msgcat::mcset ru "Copy All" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0432\u0441\u0435"
::msgcat::mcset ru "File:" "\u0424\u0430\u0439\u043b:"
::msgcat::mcset ru "Open" "\u041e\u0442\u043a\u0440\u044b\u0442\u044c"
::msgcat::mcset ru "Refresh" "\u041e\u0431\u043d\u043e\u0432\u0438\u0442\u044c"
::msgcat::mcset ru "Decrease Font Size" "\u0423\u043c\u0435\u043d\u044c\u0448\u0438\u0442\u044c \u0440\u0430\u0437\u043c\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset ru "Increase Font Size" "\u0423\u0432\u0435\u043b\u0438\u0447\u0438\u0442\u044c \u0440\u0430\u0437\u043c\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset ru "Encoding" "\u041a\u043e\u0434\u0438\u0440\u043e\u0432\u043a\u0430"
::msgcat::mcset ru "Apply/Reverse Hunk" "\u041f\u0440\u0438\u043c\u0435\u043d\u0438\u0442\u044c/\u0423\u0431\u0440\u0430\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435"
::msgcat::mcset ru "Apply/Reverse Line" "\u041f\u0440\u0438\u043c\u0435\u043d\u0438\u0442\u044c/\u0423\u0431\u0440\u0430\u0442\u044c \u0441\u0442\u0440\u043e\u043a\u0443"
::msgcat::mcset ru "Revert Hunk" "\u041e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0431\u043b\u043e\u043a\u0430"
::msgcat::mcset ru "Revert Line" "\u041e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0441\u0442\u0440\u043e\u043a\u0438"
::msgcat::mcset ru "Undo Last Revert" "\u041e\u0442\u043c\u0435\u043d\u0438\u0442\u044c \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0435 \u043e\u0431\u0440\u0430\u0449\u0435\u043d\u0438\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "Run Merge Tool" "\u0417\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0443 \u0441\u043b\u0438\u044f\u043d\u0438\u044f"
::msgcat::mcset ru "Use Remote Version" "\u0412\u0437\u044f\u0442\u044c \u0432\u043d\u0435\u0448\u043d\u044e\u044e \u0432\u0435\u0440\u0441\u0438\u044e"
::msgcat::mcset ru "Use Local Version" "\u0412\u0437\u044f\u0442\u044c \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u0443\u044e \u0432\u0435\u0440\u0441\u0438\u044e"
::msgcat::mcset ru "Revert To Base" "\u041e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Visualize These Changes In The Submodule" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u044d\u0442\u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u044f"
::msgcat::mcset ru "Visualize Current Branch History In The Submodule" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0438\u0441\u0442\u043e\u0440\u0438\u044e \u0442\u0435\u043a\u0443\u0449\u0435\u0439 \u0432\u0435\u0442\u043a\u0438 \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u044f"
::msgcat::mcset ru "Visualize All Branch History In The Submodule" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0438\u0441\u0442\u043e\u0440\u0438\u044e \u0432\u0441\u0435\u0445 \u0432\u0435\u0442\u043e\u043a \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u044f"
::msgcat::mcset ru "Start git gui In The Submodule" "\u0417\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c git gui \u0432 \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0435"
::msgcat::mcset ru "Unstage Hunk From Commit" "\u0423\u0431\u0440\u0430\u0442\u044c \u0431\u043b\u043e\u043a \u0438\u0437 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset ru "Unstage Lines From Commit" "\u0423\u0431\u0440\u0430\u0442\u044c \u0441\u0442\u0440\u043e\u043a\u0438 \u0438\u0437 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset ru "Revert Lines" "\u041e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0441\u0442\u0440\u043e\u043a"
::msgcat::mcset ru "Unstage Line From Commit" "\u0423\u0431\u0440\u0430\u0442\u044c \u0441\u0442\u0440\u043e\u043a\u0443 \u0438\u0437 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset ru "Stage Hunk For Commit" "\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u0431\u043b\u043e\u043a \u0432 \u0438\u043d\u0434\u0435\u043a\u0441"
::msgcat::mcset ru "Stage Lines For Commit" "\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u0441\u0442\u0440\u043e\u043a\u0438 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441"
::msgcat::mcset ru "Stage Line For Commit" "\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u0441\u0442\u0440\u043e\u043a\u0443 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441"
::msgcat::mcset ru "Initializing..." "\u0418\u043d\u0438\u0446\u0438\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u044f\u2026"
::msgcat::mcset ru "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "\u0412\u043e\u0437\u043c\u043e\u0436\u043d\u044b \u043e\u0448\u0438\u0431\u043a\u0438 \u0432 \u043f\u0435\u0440\u0435\u043c\u0435\u043d\u043d\u044b\u0445 \u043e\u043a\u0440\u0443\u0436\u0435\u043d\u0438\u044f.\n\n\u041f\u0435\u0440\u0435\u043c\u0435\u043d\u043d\u044b\u0435 \u043e\u043a\u0440\u0443\u0436\u0435\u043d\u0438\u044f, \u043a\u043e\u0442\u043e\u0440\u044b\u0435 \u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e\n\u0431\u0443\u0434\u0443\u0442 \u043f\u0440\u043e\u0438\u0433\u043d\u043e\u0440\u0438\u0440\u043e\u0432\u0430\u043d\u044b \u043a\u043e\u043c\u0430\u043d\u0434\u0430\u043c\u0438 Git,\n\u0437\u0430\u043f\u0443\u0449\u0435\u043d\u043d\u044b\u043c\u0438 \u0438\u0437 %s\n\n"
::msgcat::mcset ru "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\n\u042d\u0442\u043e \u0438\u0437\u0432\u0435\u0441\u0442\u043d\u0430\u044f \u043f\u0440\u043e\u0431\u043b\u0435\u043c\u0430 \u0441 Tcl,\n\u0440\u0430\u0441\u043f\u0440\u043e\u0441\u0442\u0440\u0430\u043d\u044f\u0435\u043c\u044b\u043c Cygwin."
::msgcat::mcset ru "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\n\u0412\u043c\u0435\u0441\u0442\u043e \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u044f %s \u043c\u043e\u0436\u043d\u043e\n\u0441\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u0437\u043d\u0430\u0447\u0435\u043d\u0438\u044f user.name \u0438\nuser.email \u0432 \u0412\u0430\u0448\u0435\u043c \u043f\u0435\u0440\u0441\u043e\u043d\u0430\u043b\u044c\u043d\u043e\u043c\n\u0444\u0430\u0439\u043b\u0435 ~/.gitconfig.\n"
::msgcat::mcset ru "Unsupported spell checker" "\u041d\u0435\u043f\u043e\u0434\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0435\u043c\u0430\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430\u043d\u0438\u044f"
::msgcat::mcset ru "Spell checking is unavailable" "\u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430\u043d\u0438\u044f \u043d\u0435 \u0434\u043e\u0441\u0442\u0443\u043f\u043d\u0430"
::msgcat::mcset ru "Invalid spell checking configuration" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u044c\u043d\u0430\u044f \u043a\u043e\u043d\u0444\u0438\u0433\u0443\u0440\u0430\u0446\u0438\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430\u043d\u0438\u044f"
::msgcat::mcset ru "Reverting dictionary to %s." "\u0421\u043b\u043e\u0432\u0430\u0440\u044c \u0432\u0435\u0440\u043d\u0443\u0442 \u043a %s."
::msgcat::mcset ru "Spell checker silently failed on startup" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430\u043d\u0438\u044f \u043d\u0435 \u0441\u043c\u043e\u0433\u043b\u0430 \u0437\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c\u0441\u044f"
::msgcat::mcset ru "Unrecognized spell checker" "\u041d\u0435\u0440\u0430\u0441\u043f\u043e\u0437\u043d\u0430\u043d\u043d\u0430\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430\u043d\u0438\u044f"
::msgcat::mcset ru "No Suggestions" "\u0418\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u0438\u0439 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d\u043e"
::msgcat::mcset ru "Unexpected EOF from spell checker" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430\u043d\u0438\u044f \u043f\u0440\u0435\u0440\u0432\u0430\u043b\u0430 \u043f\u0435\u0440\u0435\u0434\u0430\u0447\u0443 \u0434\u0430\u043d\u043d\u044b\u0445"
::msgcat::mcset ru "Spell Checker Failed" "\u041e\u0448\u0438\u0431\u043a\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430\u043d\u0438\u044f"
::msgcat::mcset ru "fetch %s" "\u0438\u0437\u0432\u043b\u0435\u0447\u0435\u043d\u0438\u0435 %s"
::msgcat::mcset ru "Fetching new changes from %s" "\u0418\u0437\u0432\u043b\u0435\u0447\u0435\u043d\u0438\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0438\u0437 %s "
::msgcat::mcset ru "remote prune %s" "\u0447\u0438\u0441\u0442\u043a\u0430 \u0432\u043d\u0435\u0448\u043d\u0435\u0433\u043e %s"
::msgcat::mcset ru "Pruning tracking branches deleted from %s" "\u0427\u0438\u0441\u0442\u043a\u0430 \u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0445 \u0432\u0435\u0442\u043e\u043a, \u0443\u0434\u0430\u043b\u0451\u043d\u043d\u044b\u0445 \u0438\u0437 %s"
::msgcat::mcset ru "fetch all remotes" "\u0438\u0437\u0432\u043b\u0435\u0447\u044c \u0441\u043e \u0432\u0441\u0435\u0445 \u0432\u043d\u0435\u0448\u043d\u0438\u0445 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0435\u0432"
::msgcat::mcset ru "Fetching new changes from all remotes" "\u041f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0441\u043e \u0432\u0441\u0435\u0445 \u0432\u043d\u0435\u0448\u043d\u0438\u0445 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0435\u0432"
::msgcat::mcset ru "remote prune all remotes" "\u0447\u0438\u0441\u0442\u043a\u0430 \u0432\u0441\u0435\u0445 \u0432\u043d\u0435\u0448\u043d\u0438\u0445 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0435\u0432"
::msgcat::mcset ru "Pruning tracking branches deleted from all remotes" "\u0427\u0438\u0441\u0442\u043a\u0430 \u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0445 \u0432\u0435\u0442\u043e\u043a, \u0443\u0434\u0430\u043b\u0451\u043d\u043d\u044b\u0445 \u0441\u043e \u0432\u0441\u0435\u0445 \u0432\u043d\u0435\u0448\u043d\u0438\u0445 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0435\u0432"
::msgcat::mcset ru "push %s" "\u043e\u0442\u043f\u0440\u0430\u0432\u0438\u0442\u044c %s"
::msgcat::mcset ru "Pushing changes to %s" "\u041e\u0442\u043f\u0440\u0430\u0432\u043a\u0430 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0432 %s "
::msgcat::mcset ru "Mirroring to %s" "\u0422\u043e\u0447\u043d\u043e\u0435 \u043a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u0432 %s"
::msgcat::mcset ru "Pushing %s %s to %s" "\u041e\u0442\u043f\u0440\u0430\u0432\u043a\u0430 %s %s \u0432 %s"
::msgcat::mcset ru "Push Branches" "\u041e\u0442\u043f\u0440\u0430\u0432\u0438\u0442\u044c \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Cancel" "\u041e\u0442\u043c\u0435\u043d\u0430"
::msgcat::mcset ru "Source Branches" "\u0418\u0441\u0445\u043e\u0434\u043d\u044b\u0435 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Destination Repository" "\u0420\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 \u043d\u0430\u0437\u043d\u0430\u0447\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Remote:" "\u0432\u043d\u0435\u0448\u043d\u0438\u0439:"
::msgcat::mcset ru "Arbitrary Location:" "\u0423\u043a\u0430\u0437\u0430\u043d\u043d\u043e\u0435 \u043f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435:"
::msgcat::mcset ru "Transfer Options" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043e\u0442\u043f\u0440\u0430\u0432\u043a\u0438"
::msgcat::mcset ru "Force overwrite existing branch (may discard changes)" "\u041f\u0440\u0438\u043d\u0443\u0434\u0438\u0442\u0435\u043b\u044c\u043d\u043e \u043f\u0435\u0440\u0435\u0437\u0430\u043f\u0438\u0441\u0430\u0442\u044c \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044e\u0449\u0443\u044e \u0432\u0435\u0442\u043a\u0443 (\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u0430 \u043f\u043e\u0442\u0435\u0440\u044f \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439)"
::msgcat::mcset ru "Use thin pack (for slow network connections)" "\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c thin pack (\u0434\u043b\u044f \u043c\u0435\u0434\u043b\u0435\u043d\u043d\u044b\u0445 \u0441\u0435\u0442\u0435\u0432\u044b\u0445 \u043f\u043e\u0434\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0439)"
::msgcat::mcset ru "Include tags" "\u041f\u0435\u0440\u0435\u0434\u0430\u0442\u044c \u043c\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "%s (%s): Push" "%s (%s): \u041e\u0442\u043f\u0440\u0430\u0432\u043a\u0430"
::msgcat::mcset ru "Fetching %s from %s" "\u0418\u0437\u0432\u043b\u0435\u0447\u0435\u043d\u0438\u0435 %s \u0438\u0437 %s "
::msgcat::mcset ru "fatal: Cannot resolve %s" "\u043a\u0440\u0438\u0442\u0438\u0447\u0435\u0441\u043a\u0430\u044f \u043e\u0448\u0438\u0431\u043a\u0430: \u043d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0440\u0430\u0437\u0440\u0435\u0448\u0438\u0442\u044c %s"
::msgcat::mcset ru "Close" "\u0417\u0430\u043a\u0440\u044b\u0442\u044c"
::msgcat::mcset ru "Branch '%s' does not exist." "\u0412\u0435\u0442\u043a\u0430 \u00ab%s\u00bb \u043d\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442."
::msgcat::mcset ru "Failed to configure simplified git-pull for '%s'." "\u041e\u0448\u0438\u0431\u043a\u0430 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f \u0443\u043f\u0440\u043e\u0449\u0451\u043d\u043d\u043e\u0439 \u043a\u043e\u043d\u0444\u0438\u0433\u0443\u0440\u0430\u0446\u0438\u0438 git pull \u0434\u043b\u044f \u00ab%s\u00bb."
::msgcat::mcset ru "Branch '%s' already exists." "\u0412\u0435\u0442\u043a\u0430 \u00ab%s\u00bb \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442."
::msgcat::mcset ru "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "\u0412\u0435\u0442\u043a\u0430 \u00ab%s\u00bb \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442.\n\n\u041e\u043d\u0430 \u043d\u0435 \u043c\u043e\u0436\u0435\u0442 \u0431\u044b\u0442\u044c \u043f\u0435\u0440\u0435\u043c\u043e\u0442\u0430\u043d\u0430 \u0432\u043f\u0435\u0440\u0435\u0434 \u043a %s.\n\u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044f \u0441\u043b\u0438\u044f\u043d\u0438\u0435."
::msgcat::mcset ru "Merge strategy '%s' not supported." "\u041d\u0435\u0438\u0437\u0432\u0435\u0441\u0442\u043d\u0430\u044f \u0441\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u044f \u0441\u043b\u0438\u044f\u043d\u0438\u044f \u00ab%s\u00bb."
::msgcat::mcset ru "Failed to update '%s'." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043e\u0431\u043d\u043e\u0432\u0438\u0442\u044c \u00ab%s\u00bb."
::msgcat::mcset ru "Staging area (index) is already locked." "\u0420\u0430\u0431\u043e\u0447\u0430\u044f \u043e\u0431\u043b\u0430\u0441\u0442\u044c \u0437\u0430\u0431\u043b\u043e\u043a\u0438\u0440\u043e\u0432\u0430\u043d\u0430 \u0434\u0440\u0443\u0433\u0438\u043c \u043f\u0440\u043e\u0446\u0435\u0441\u0441\u043e\u043c."
::msgcat::mcset ru "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "\u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0435 \u043f\u0440\u043e\u0447\u0438\u0442\u0430\u043d\u043d\u043e\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f \u043d\u0435 \u0441\u043e\u043e\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0443\u0435\u0442 \u0442\u0435\u043a\u0443\u0449\u0435\u043c\u0443.\n\n\u0421 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 \u0431\u044b\u043b \u0438\u0437\u043c\u0435\u043d\u0435\u043d \u0434\u0440\u0443\u0433\u043e\u0439 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u043e\u0439 Git. \u041d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u043e \u043f\u0435\u0440\u0435\u0447\u0438\u0442\u0430\u0442\u044c \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439, \u043f\u0440\u0435\u0436\u0434\u0435 \u0447\u0435\u043c \u0442\u0435\u043a\u0443\u0449\u0430\u044f \u0432\u0435\u0442\u043a\u0430 \u043c\u043e\u0436\u0435\u0442 \u0431\u044b\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0430.\n\n\u042d\u0442\u043e \u0431\u0443\u0434\u0435\u0442 \u0441\u0434\u0435\u043b\u0430\u043d\u043e \u0441\u0435\u0439\u0447\u0430\u0441 \u0430\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438.\n"
::msgcat::mcset ru "Updating working directory to '%s'..." "\u041e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u0440\u0430\u0431\u043e\u0447\u0435\u0433\u043e \u043a\u0430\u0442\u0430\u043b\u043e\u0433\u0430 \u0438\u0437 \u00ab%s\u00bb\u2026"
::msgcat::mcset ru "files checked out" "\u0444\u0430\u0439\u043b\u044b \u0438\u0437\u0432\u043b\u0435\u0447\u0435\u043d\u044b"
::msgcat::mcset ru "Aborted checkout of '%s' (file level merging is required)." "\u041f\u0440\u0435\u0440\u0432\u0430\u043d \u043f\u0435\u0440\u0435\u0445\u043e\u0434 \u043d\u0430 \u00ab%s\u00bb (\u0442\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044f \u0441\u043b\u0438\u044f\u043d\u0438\u0435 \u0441\u043e\u0434\u0435\u0440\u0436\u0438\u043c\u043e\u0433\u043e \u0444\u0430\u0439\u043b\u043e\u0432)"
::msgcat::mcset ru "File level merge required." "\u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044f \u0441\u043b\u0438\u044f\u043d\u0438\u0435 \u0441\u043e\u0434\u0435\u0440\u0436\u0430\u043d\u0438\u044f \u0444\u0430\u0439\u043b\u043e\u0432."
::msgcat::mcset ru "Staying on branch '%s'." "\u0412\u0435\u0442\u043a\u0430 \u00ab%s\u00bb \u043e\u0441\u0442\u0430\u0451\u0442\u0441\u044f \u0442\u0435\u043a\u0443\u0449\u0435\u0439."
::msgcat::mcset ru "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "\u0412\u044b \u0431\u043e\u043b\u0435\u0435 \u043d\u0435 \u043d\u0430\u0445\u043e\u0434\u0438\u0442\u0435\u0441\u044c \u043d\u0430 \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u043e\u0439 \u0432\u0435\u0442\u043a\u0435.\n\n\u0415\u0441\u043b\u0438 \u0432\u044b \u0445\u043e\u0442\u0438\u0442\u0435 \u0441\u043d\u043e\u0432\u0430 \u0432\u0435\u0440\u043d\u0443\u0442\u044c\u0441\u044f \u043a \u043a\u0430\u043a\u043e\u0439-\u043d\u0438\u0431\u0443\u0434\u044c \u0432\u0435\u0442\u043a\u0435, \u0441\u043e\u0437\u0434\u0430\u0439\u0442\u0435 \u0435\u0451 \u0441\u0435\u0439\u0447\u0430\u0441, \u043d\u0430\u0447\u0438\u043d\u0430\u044f \u0441 \u00ab\u0422\u0435\u043a\u0443\u0449\u0435\u0433\u043e \u043e\u0442\u0441\u043e\u0435\u0434\u0438\u043d\u0435\u043d\u043d\u043e\u0433\u043e \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u044f\u00bb."
::msgcat::mcset ru "Checked out '%s'." "\u0412\u044b\u043f\u043e\u043b\u043d\u0435\u043d \u043f\u0435\u0440\u0435\u0445\u043e\u0434 \u043d\u0430 \u00ab%s\u00bb."
::msgcat::mcset ru "Resetting '%s' to '%s' will lose the following commits:" "\u0421\u0431\u0440\u043e\u0441 \u00ab%s\u00bb  \u043d\u0430 \u00ab%s\u00bb \u043f\u0440\u0438\u0432\u0435\u0434\u0435\u0442 \u043a \u043f\u043e\u0442\u0435\u0440\u0435 \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0438\u0445 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u0432:"
::msgcat::mcset ru "Recovering lost commits may not be easy." "\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u044c \u043f\u043e\u0442\u0435\u0440\u044f\u043d\u043d\u044b\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u044b \u0431\u0443\u0434\u0435\u0442 \u0441\u043b\u043e\u0436\u043d\u043e."
::msgcat::mcset ru "Reset '%s'?" "\u0421\u0431\u0440\u043e\u0441\u0438\u0442\u044c \u00ab%s\u00bb?"
::msgcat::mcset ru "Visualize" "\u041d\u0430\u0433\u043b\u044f\u0434\u043d\u043e"
::msgcat::mcset ru "Reset" "\u0421\u0431\u0440\u043e\u0441"
::msgcat::mcset ru "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0443\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u044c \u0442\u0435\u043a\u0443\u0449\u0443\u044e \u0432\u0435\u0442\u043a\u0443.\n\n\u0412\u0430\u0448 \u0440\u0430\u0431\u043e\u0447\u0438\u0439 \u043a\u0430\u0442\u0430\u043b\u043e\u0433 \u043e\u0431\u043d\u043e\u0432\u043b\u0451\u043d \u0442\u043e\u043b\u044c\u043a\u043e \u0447\u0430\u0441\u0442\u0438\u0447\u043d\u043e. \u0411\u044b\u043b\u0438 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u044b \u0432\u0441\u0435 \u0444\u0430\u0439\u043b\u044b \u043a\u0440\u043e\u043c\u0435 \u0441\u043b\u0443\u0436\u0435\u0431\u043d\u044b\u0445 \u0444\u0430\u0439\u043b\u043e\u0432 Git. \n\n\u042d\u0442\u043e\u0433\u043e \u043d\u0435 \u0434\u043e\u043b\u0436\u043d\u043e \u0431\u044b\u043b\u043e \u043f\u0440\u043e\u0438\u0437\u043e\u0439\u0442\u0438. %s \u0437\u0430\u0432\u0435\u0440\u0448\u0430\u0435\u0442\u0441\u044f."
::msgcat::mcset ru "%s (%s): Add Remote" "%s (%s): \u0414\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u0432\u043d\u0435\u0448\u043d\u0435\u0433\u043e \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f"
::msgcat::mcset ru "Add New Remote" "\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u0432\u043d\u0435\u0448\u043d\u0438\u0439 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439"
::msgcat::mcset ru "Add" "\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c"
::msgcat::mcset ru "Remote Details" "\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u0432\u043d\u0435\u0448\u043d\u0435\u043c \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0438"
::msgcat::mcset ru "Name:" "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435:"
::msgcat::mcset ru "Location:" "\u041f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435:"
::msgcat::mcset ru "Further Action" "\u0421\u043b\u0435\u0434\u0443\u044e\u0449\u0430\u044f \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044f"
::msgcat::mcset ru "Fetch Immediately" "\u0421\u0440\u0430\u0437\u0443 \u0438\u0437\u0432\u043b\u0435\u0447\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Initialize Remote Repository and Push" "\u0418\u043d\u0438\u0446\u0438\u0430\u043b\u0438\u0437\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0432\u043d\u0435\u0448\u043d\u0438\u0439 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 \u0438 \u043e\u0442\u043f\u0440\u0430\u0432\u0438\u0442\u044c"
::msgcat::mcset ru "Do Nothing Else Now" "\u0411\u043e\u043b\u044c\u0448\u0435 \u043d\u0438\u0447\u0435\u0433\u043e \u043d\u0435 \u0434\u0435\u043b\u0430\u0442\u044c"
::msgcat::mcset ru "Please supply a remote name." "\u0423\u043a\u0430\u0436\u0438\u0442\u0435 \u043d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u0432\u043d\u0435\u0448\u043d\u0435\u0433\u043e \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f."
::msgcat::mcset ru "'%s' is not an acceptable remote name." "\u00ab%s\u00bb \u043d\u0435 \u044f\u0432\u043b\u044f\u0435\u0442\u0441\u044f \u0434\u043e\u043f\u0443\u0441\u0442\u0438\u043c\u044b\u043c \u0438\u043c\u0435\u043d\u0435\u043c \u0432\u043d\u0435\u0448\u043d\u0435\u0433\u043e \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f."
::msgcat::mcset ru "Failed to add remote '%s' of location '%s'." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0434\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u00ab%s\u00bb \u0438\u0437 \u00ab%s\u00bb. "
::msgcat::mcset ru "Fetching the %s" "\u0418\u0437\u0432\u043b\u0435\u0447\u0435\u043d\u0438\u0435 %s"
::msgcat::mcset ru "Do not know how to initialize repository at location '%s'." "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0438\u043d\u0438\u0446\u0438\u0430\u043b\u0438\u0437\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 \u0432 \u00ab%s\u00bb."
::msgcat::mcset ru "Setting up the %s (at %s)" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0430 %s (\u0432 %s)"
::msgcat::mcset ru "Starting..." "\u0417\u0430\u043f\u0443\u0441\u043a\u2026"
::msgcat::mcset ru "%s (%s): File Browser" "%s (%s): \u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430 \u0444\u0430\u0439\u043b\u043e\u0432"
::msgcat::mcset ru "Loading %s..." "\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430 %s\u2026"
::msgcat::mcset ru "\[Up To Parent\]" "\[\u041d\u0430 \u0443\u0440\u043e\u0432\u0435\u043d\u044c \u0432\u044b\u0448\u0435\]"
::msgcat::mcset ru "%s (%s): Browse Branch Files" "%s (%s): \u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0444\u0430\u0439\u043b\u043e\u0432 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Browse Branch Files" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0444\u0430\u0439\u043b\u044b \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Browse" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c"
::msgcat::mcset ru "Revision" "\u0412\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset ru "Unable to unlock the index." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0440\u0430\u0437\u0431\u043b\u043e\u043a\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0438\u043d\u0434\u0435\u043a\u0441"
::msgcat::mcset ru "Index Error" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441\u0435"
::msgcat::mcset ru "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043e\u0431\u043d\u043e\u0432\u0438\u0442\u044c \u0438\u043d\u0434\u0435\u043a\u0441 Git. \u0421\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f \u0431\u0443\u0434\u0435\u0442 \u043f\u0435\u0440\u0435\u0447\u0438\u0442\u0430\u043d\u043e \u0430\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438."
::msgcat::mcset ru "Continue" "\u041f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u044c"
::msgcat::mcset ru "Unlock Index" "\u0420\u0430\u0437\u0431\u043b\u043e\u043a\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0438\u043d\u0434\u0435\u043a\u0441"
::msgcat::mcset ru "files" "\u0444\u0430\u0439\u043b\u043e\u0432"
::msgcat::mcset ru "Unstaging selected files from commit" "\u0423\u0431\u043e\u0440\u043a\u0430 \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u044b\u0445 \u0444\u0430\u0439\u043b\u043e\u0432 \u0438\u0437 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset ru "Unstaging %s from commit" "\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435 %s \u0438\u0437 \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset ru "Ready to commit." "\u0413\u043e\u0442\u043e\u0432 \u0434\u043b\u044f \u043a\u043e\u043c\u043c\u0438\u0442\u0430."
::msgcat::mcset ru "Adding selected files" "\u0414\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u044b\u0445 \u0444\u0430\u0439\u043b\u043e\u0432"
::msgcat::mcset ru "Adding %s" "\u0414\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u0438\u0435 %s\u2026"
::msgcat::mcset ru "Stage %d untracked files?" "\u041f\u0440\u043e\u0438\u043d\u0434\u0435\u043a\u0441\u0438\u0440\u043e\u0432\u0430\u0442\u044c %d \u043d\u0435\u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0435 \u0444\u0430\u0439\u043b\u0430?"
::msgcat::mcset ru "Adding all changed files" "\u0414\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u0432\u0441\u0435\u0445 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u043d\u044b\u0445 \u0444\u0430\u0439\u043b\u043e\u0432"
::msgcat::mcset ru "Revert changes in file %s?" "\u041e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432 \u0444\u0430\u0439\u043b\u0435 %s?"
::msgcat::mcset ru "Revert changes in these %i files?" "\u041e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432 %i \u0444\u0430\u0439\u043b\u0435(-\u0430\u0445)?"
::msgcat::mcset ru "Any unstaged changes will be permanently lost by the revert." "\u041b\u044e\u0431\u044b\u0435 \u043d\u0435\u043f\u0440\u043e\u0438\u043d\u0434\u0435\u043a\u0441\u0438\u0440\u043e\u0432\u0430\u043d\u043d\u044b\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f, \u0431\u0443\u0434\u0443\u0442 \u043f\u043e\u0442\u0435\u0440\u044f\u043d\u044b \u043f\u0440\u0438 \u043e\u0431\u0440\u0430\u0449\u0435\u043d\u0438\u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439."
::msgcat::mcset ru "Do Nothing" "\u041d\u0438\u0447\u0435\u0433\u043e \u043d\u0435 \u0434\u0435\u043b\u0430\u0442\u044c"
::msgcat::mcset ru "Delete untracked file %s?" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u043d\u0435\u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0439 \u0444\u0430\u0439\u043b %s?"
::msgcat::mcset ru "Delete these %i untracked files?" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c %i \u043d\u0435\u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0435 \u0444\u0430\u0439\u043b\u0430?"
::msgcat::mcset ru "Files will be permanently deleted." "\u0424\u0430\u0439\u043b\u044b \u0431\u0443\u0434\u0443\u0442 \u0443\u0434\u0430\u043b\u0435\u043d\u044b \u043d\u0430\u0432\u0441\u0435\u0433\u0434\u0430."
::msgcat::mcset ru "Delete Files" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u0444\u0430\u0439\u043b\u044b"
::msgcat::mcset ru "Deleting" "\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435"
::msgcat::mcset ru "Encountered errors deleting files:\n" "\u0412\u043e\u0437\u043d\u0438\u043a\u0448\u0438\u0435 \u043e\u0448\u0438\u0431\u043a\u0438 \u043f\u0440\u0438 \u0443\u0434\u0430\u043b\u0435\u043d\u0438\u0438 \u0444\u0430\u0439\u043b\u043e\u0432:\n"
::msgcat::mcset ru "None of the %d selected files could be deleted." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0443\u0434\u0430\u043b\u0438\u0442\u044c \u043d\u0438 \u043e\u0434\u0438\u043d \u0438\u0437 \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u044b\u0445 %d \u0444\u0430\u0439\u043b\u043e\u0432."
::msgcat::mcset ru "%d of the %d selected files could not be deleted." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0443\u0434\u0430\u043b\u0438\u0442\u044c %d \u0438\u0437 \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u044b\u0445  %d \u0444\u0430\u0439\u043b\u043e\u0432."
::msgcat::mcset ru "Reverting selected files" "\u041e\u0431\u0440\u0430\u0449\u0435\u043d\u0438\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0432 \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u044b\u0445 \u0444\u0430\u0439\u043b\u0430\u0445"
::msgcat::mcset ru "Reverting %s" "\u041e\u0431\u0440\u0430\u0449\u0435\u043d\u0438\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0432 %s"
::msgcat::mcset ru "%s (%s): Checkout Branch" "%s (%s): \u041f\u0435\u0440\u0435\u0445\u043e\u0434 \u043d\u0430 \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Checkout Branch" "\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Checkout" "\u041f\u0435\u0440\u0435\u0439\u0442\u0438"
::msgcat::mcset ru "Options" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438"
::msgcat::mcset ru "Fetch Tracking Branch" "\u0418\u0437\u0432\u043b\u0435\u0447\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0438\u0437 \u0432\u043d\u0435\u0448\u043d\u0435\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Detach From Local Branch" "\u041e\u0442\u0441\u043e\u0435\u0434\u0438\u043d\u0438\u0442\u044c \u043e\u0442 \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u043e\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "%s ... %*i of %*i %s (%3i%%)" "%s \u2026 %*i \u0438\u0437 %*i %s (%3i%%)"
::msgcat::mcset ru "Push to" "\u041e\u0442\u043f\u0440\u0430\u0432\u0438\u0442\u044c"
::msgcat::mcset ru "Remove Remote" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u0441\u0441\u044b\u043b\u043a\u0443 \u043d\u0430 \u0432\u043d\u0435\u0448\u043d\u0438\u0439 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439"
::msgcat::mcset ru "Prune from" "\u0427\u0438\u0441\u0442\u043a\u0430"
::msgcat::mcset ru "Fetch from" "\u0418\u0437\u0432\u043b\u0435\u0447\u0435\u043d\u0438\u0435 \u0438\u0437"
::msgcat::mcset ru "All" "\u0412\u0441\u0435"
::msgcat::mcset ru "%s (%s): Rename Branch" "%s (%s): \u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u0442\u044c \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Rename Branch" "\u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Rename" "\u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u0442\u044c"
::msgcat::mcset ru "Branch:" "\u0412\u0435\u0442\u043a\u0430:"
::msgcat::mcset ru "New Name:" "\u041d\u043e\u0432\u043e\u0435 \u043d\u0430\u0437\u0432\u0430\u043d\u0438\u0435:"
::msgcat::mcset ru "Please select a branch to rename." "\u0423\u043a\u0430\u0436\u0438\u0442\u0435 \u0432\u0435\u0442\u043a\u0443 \u0434\u043b\u044f \u043f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u044f."
::msgcat::mcset ru "Please supply a branch name." "\u0423\u043a\u0430\u0436\u0438\u0442\u0435 \u0438\u043c\u044f \u0432\u0435\u0442\u043a\u0438."
::msgcat::mcset ru "'%s' is not an acceptable branch name." "\u041d\u0435\u0434\u043e\u043f\u0443\u0441\u0442\u0438\u043c\u043e\u0435 \u0438\u043c\u044f \u0432\u0435\u0442\u043a\u0438 \u00ab%s\u00bb."
::msgcat::mcset ru "Failed to rename '%s'." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u0442\u044c \u00ab%s\u00bb. "
::msgcat::mcset ru "Select" "\u0412\u044b\u0431\u0440\u0430\u0442\u044c"
::msgcat::mcset ru "Font Family" "\u0428\u0440\u0438\u0444\u0442"
::msgcat::mcset ru "Font Size" "\u0420\u0430\u0437\u043c\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset ru "Font Example" "\u041f\u0440\u0438\u043c\u0435\u0440 \u0442\u0435\u043a\u0441\u0442\u0430"
::msgcat::mcset ru "This is example text.\nIf you like this text, it can be your font." "\u042d\u0442\u043e \u043f\u0440\u0438\u043c\u0435\u0440 \u0442\u0435\u043a\u0441\u0442\u0430.\n\u0415\u0441\u043b\u0438 \u0412\u0430\u043c \u043d\u0440\u0430\u0432\u0438\u0442\u0441\u044f \u044d\u0442\u043e\u0442 \u0442\u0435\u043a\u0441\u0442, \u044d\u0442\u043e \u043c\u043e\u0436\u0435\u0442 \u0431\u044b\u0442\u044c \u0412\u0430\u0448 \u0448\u0440\u0438\u0444\u0442."
::msgcat::mcset ru "Invalid global encoding '%s'" "\u041d\u0435\u0432\u0435\u0440\u043d\u0430\u044f \u0433\u043b\u043e\u0431\u0430\u043b\u044c\u043d\u0430\u044f \u043a\u043e\u0434\u0438\u0440\u043e\u0432\u043a\u0430 \u00ab%s\u00bb"
::msgcat::mcset ru "Invalid repo encoding '%s'" "\u041d\u0435\u0432\u0435\u0440\u043d\u0430\u044f \u043a\u043e\u0434\u0438\u0440\u043e\u0432\u043a\u0430 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f \u00ab%s\u00bb"
::msgcat::mcset ru "Restore Defaults" "\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u044c \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043f\u043e \u0443\u043c\u043e\u043b\u0447\u0430\u043d\u0438\u044e"
::msgcat::mcset ru "Save" "\u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c"
::msgcat::mcset ru "%s Repository" "\u0414\u043b\u044f \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f %s"
::msgcat::mcset ru "Global (All Repositories)" "\u041e\u0431\u0449\u0438\u0435 (\u0434\u043b\u044f \u0432\u0441\u0435\u0445 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0435\u0432)"
::msgcat::mcset ru "User Name" "\u0418\u043c\u044f \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044f"
::msgcat::mcset ru "Email Address" "\u0410\u0434\u0440\u0435\u0441 \u044d\u043b\u0435\u043a\u0442\u0440\u043e\u043d\u043d\u043e\u0439 \u043f\u043e\u0447\u0442\u044b"
::msgcat::mcset ru "Summarize Merge Commits" "\u0421\u0443\u043c\u043c\u0430\u0440\u043d\u043e\u0435 \u0441\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u0438"
::msgcat::mcset ru "Merge Verbosity" "\u0423\u0440\u043e\u0432\u0435\u043d\u044c \u0434\u0435\u0442\u0430\u043b\u044c\u043d\u043e\u0441\u0442\u0438 \u0441\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0439 \u043f\u0440\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u0438"
::msgcat::mcset ru "Show Diffstat After Merge" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u043e\u0442\u0447\u0435\u0442 \u043e\u0431 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f\u0445 \u043f\u043e\u0441\u043b\u0435 \u0441\u043b\u0438\u044f\u043d\u0438\u044f"
::msgcat::mcset ru "Use Merge Tool" "\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u0434\u043b\u044f \u0441\u043b\u0438\u044f\u043d\u0438\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0443"
::msgcat::mcset ru "Trust File Modification Timestamps" "\u0414\u043e\u0432\u0435\u0440\u044f\u0442\u044c \u0432\u0440\u0435\u043c\u0435\u043d\u0438 \u043c\u043e\u0434\u0438\u0444\u0438\u043a\u0430\u0446\u0438\u0438 \u0444\u0430\u0439\u043b\u0430"
::msgcat::mcset ru "Prune Tracking Branches During Fetch" "\u0427\u0438\u0441\u0442\u043a\u0430 \u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0445 \u0432\u0435\u0442\u043e\u043a \u043f\u0440\u0438 \u0438\u0437\u0432\u043b\u0435\u0447\u0435\u043d\u0438\u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439"
::msgcat::mcset ru "Match Tracking Branches" "\u0422\u0430\u043a\u043e\u0435 \u0436\u0435 \u0438\u043c\u044f, \u043a\u0430\u043a \u0438 \u0443 \u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u043e\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Use Textconv For Diffs and Blames" "\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c Textconv \u0434\u043b\u044f \u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430 \u0440\u0430\u0437\u043b\u0438\u0447\u0438\u0439 \u0438 \u0430\u0432\u0442\u043e\u0440\u0441\u0442\u0432\u0430"
::msgcat::mcset ru "Blame Copy Only On Changed Files" "\u041f\u043e\u0438\u0441\u043a \u043a\u043e\u043f\u0438\u0439 \u0442\u043e\u043b\u044c\u043a\u043e \u0432 \u0438\u0437\u043c\u0435\u043d\u0451\u043d\u043d\u044b\u0445 \u0444\u0430\u0439\u043b\u0430\u0445"
::msgcat::mcset ru "Maximum Length of Recent Repositories List" "\u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u044c\u043d\u0430\u044f \u0434\u043b\u0438\u043d\u043d\u0430 \u0441\u043f\u0438\u0441\u043a\u0430 \u043d\u0435\u0434\u0430\u0432\u043d\u0438\u0445 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0435\u0432"
::msgcat::mcset ru "Minimum Letters To Blame Copy On" "\u041c\u0438\u043d\u0438\u043c\u0430\u043b\u044c\u043d\u043e\u0435 \u043a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u0441\u0438\u043c\u0432\u043e\u043b\u043e\u0432 \u0434\u043b\u044f \u043f\u043e\u0438\u0441\u043a\u0430 \u043a\u043e\u043f\u0438\u0439"
::msgcat::mcset ru "Blame History Context Radius (days)" "\u0420\u0430\u0434\u0438\u0443\u0441 \u0438\u0441\u0442\u043e\u0440\u0438\u0447\u0435\u0441\u043a\u043e\u0433\u043e \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442\u0430 (\u0432 \u0434\u043d\u044f\u0445)"
::msgcat::mcset ru "Number of Diff Context Lines" "\u0427\u0438\u0441\u043b\u043e \u0441\u0442\u0440\u043e\u043a \u0432 \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442\u0435 diff"
::msgcat::mcset ru "Additional Diff Parameters" "\u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u044b\u0435 \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u044b \u0434\u043b\u044f diff"
::msgcat::mcset ru "Commit Message Text Width" "\u0428\u0438\u0440\u0438\u043d\u0430 \u0442\u0435\u043a\u0441\u0442\u0430 \u0441\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u044f \u043a\u043e\u043c\u043c\u0438\u0442\u0430"
::msgcat::mcset ru "New Branch Name Template" "\u0428\u0430\u0431\u043b\u043e\u043d \u0434\u043b\u044f \u0438\u043c\u0435\u043d\u0438 \u043d\u043e\u0432\u043e\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Default File Contents Encoding" "\u041a\u043e\u0434\u0438\u0440\u043e\u0432\u043a\u0430 \u0441\u043e\u0434\u0435\u0440\u0436\u0430\u043d\u0438\u044f \u0444\u0430\u0439\u043b\u0430 \u043f\u043e \u0443\u043c\u043e\u043b\u0447\u0430\u043d\u0438\u044e"
::msgcat::mcset ru "Warn before committing to a detached head" "\u041f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0430\u0442\u044c \u043f\u0435\u0440\u0435\u0434 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u043c \u0432 \u043e\u0442\u0434\u0435\u043b\u0451\u043d\u043d\u044b\u0439 HEAD"
::msgcat::mcset ru "Staging of untracked files" "\u0418\u043d\u0434\u0435\u043a\u0441\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043d\u0435\u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0445 \u0444\u0430\u0439\u043b\u043e\u0432"
::msgcat::mcset ru "Show untracked files" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u043d\u0435\u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0435 \u0444\u0430\u0439\u043b\u044b"
::msgcat::mcset ru "Tab spacing" "\u0428\u0438\u0440\u0438\u043d\u0430 \u0442\u0430\u0431\u0443\u043b\u044f\u0446\u0438\u0438"
::msgcat::mcset ru "%s:" "%s:"
::msgcat::mcset ru "Change" "\u0418\u0437\u043c\u0435\u043d\u0438\u0442\u044c"
::msgcat::mcset ru "Spelling Dictionary:" "\u0421\u043b\u043e\u0432\u0430\u0440\u044c \u0434\u043b\u044f \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430\u043d\u0438\u044f:"
::msgcat::mcset ru "Change Font" "\u0418\u0437\u043c\u0435\u043d\u0438\u0442\u044c"
::msgcat::mcset ru "Choose %s" "\u0412\u044b\u0431\u0435\u0440\u0438\u0442\u0435 %s"
::msgcat::mcset ru "pt." "\u043f."
::msgcat::mcset ru "Preferences" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438"
::msgcat::mcset ru "Failed to completely save options:" "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043f\u043e\u043b\u043d\u043e\u0441\u0442\u044c\u044e \u0441\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438:"
::msgcat::mcset ru "Default" "\u041f\u043e \u0443\u043c\u043e\u043b\u0447\u0430\u043d\u0438\u044e"
::msgcat::mcset ru "System (%s)" "\u0421\u0438\u0441\u0442\u0435\u043c\u043d\u0430\u044f (%s)"
::msgcat::mcset ru "Other" "\u0414\u0440\u0443\u0433\u0430\u044f"
::msgcat::mcset ru "Running %s requires a selected file." "\u0417\u0430\u043f\u0443\u0441\u043a %s \u0442\u0440\u0435\u0431\u0443\u0435\u0442 \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u043e\u0433\u043e \u0444\u0430\u0439\u043b\u0430."
::msgcat::mcset ru "Are you sure you want to run %1\$s on file \"%2\$s\"?" "\u0412\u044b \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0442\u0435\u043b\u044c\u043d\u043e \u0445\u043e\u0442\u0438\u0442\u0435 \u0432\u044b\u043f\u043e\u043b\u043d\u0438\u0442\u044c %1\$s \u043d\u0430 \u00ab%2\$s\u00bb?"
::msgcat::mcset ru "Are you sure you want to run %s?" "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u0442\u0435\u043b\u044c\u043d\u043e \u0437\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c %s?"
::msgcat::mcset ru "Tool: %s" "\u0412\u0441\u043f\u043e\u043c\u043e\u0433\u0430\u0442\u0435\u043b\u044c\u043d\u0430\u044f \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044f: %s"
::msgcat::mcset ru "Running: %s" "\u0412\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u0435: %s"
::msgcat::mcset ru "Tool completed successfully: %s" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 %s \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u043b\u0430\u0441\u044c \u0443\u0441\u043f\u0435\u0448\u043d\u043e."
::msgcat::mcset ru "Tool failed: %s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b: %s"
::msgcat::mcset ru "Force resolution to the base version?" "\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u0431\u0430\u0437\u043e\u0432\u0443\u044e \u0432\u0435\u0440\u0441\u0438\u044e \u0434\u043b\u044f \u0440\u0430\u0437\u0440\u0435\u0448\u0435\u043d\u0438\u044f \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0430?"
::msgcat::mcset ru "Force resolution to this branch?" "\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u0432\u0435\u0440\u0441\u0438\u044e \u0438\u0437 \u044d\u0442\u043e\u0439 \u0432\u0435\u0442\u043a\u0438 \u0434\u043b\u044f \u0440\u0430\u0437\u0440\u0435\u0448\u0435\u043d\u0438\u044f \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0430?"
::msgcat::mcset ru "Force resolution to the other branch?" "\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u0432\u0435\u0440\u0441\u0438\u044e \u0438\u0437 \u0434\u0440\u0443\u0433\u043e\u0439 \u0432\u0435\u0442\u043a\u0438 \u0434\u043b\u044f \u0440\u0430\u0437\u0440\u0435\u0448\u0435\u043d\u0438\u044f \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0430?"
::msgcat::mcset ru "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "\u0412\u043d\u0438\u043c\u0430\u043d\u0438\u0435! \u0421\u043f\u0438\u0441\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0435\u0442 \u0442\u043e\u043b\u044c\u043a\u043e \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0443\u044e\u0449\u0438\u0435 \u043e\u0442\u043b\u0438\u0447\u0438\u044f.\n\n%s \u0431\u0443\u0434\u0435\u0442 \u043f\u0435\u0440\u0435\u043f\u0438\u0441\u0430\u043d.\n\n\u042d\u0442\u043e \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435 \u043c\u043e\u0436\u043d\u043e \u043e\u0442\u043c\u0435\u043d\u0438\u0442\u044c \u0442\u043e\u043b\u044c\u043a\u043e \u043f\u0435\u0440\u0435\u0437\u0430\u043f\u0443\u0441\u043a\u043e\u043c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u044f."
::msgcat::mcset ru "File %s seems to have unresolved conflicts, still stage?" "\u041f\u043e\u0445\u043e\u0436\u0435, \u0447\u0442\u043e \u0444\u0430\u0439\u043b %s \u0441\u043e\u0434\u0435\u0440\u0436\u0438\u0442 \u043d\u0435\u0440\u0430\u0437\u0440\u0435\u0448\u0435\u043d\u043d\u044b\u0435 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u044b. \u041f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u044c \u0438\u043d\u0434\u0435\u043a\u0441\u0430\u0446\u0438\u044e?"
::msgcat::mcset ru "Adding resolution for %s" "\u0414\u043e\u0431\u0430\u0432\u043b\u044f\u044e \u0440\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442 \u0440\u0430\u0437\u0440\u0435\u0448\u0435\u043d\u0438\u044f \u0434\u043b\u044f %s"
::msgcat::mcset ru "Cannot resolve deletion or link conflicts using a tool" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u0441\u043b\u0438\u044f\u043d\u0438\u044f \u043d\u0435 \u043e\u0431\u0440\u0430\u0431\u0430\u0442\u044b\u0432\u0430\u0435\u0442 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u044b \u0441 \u0443\u0434\u0430\u043b\u0435\u043d\u0438\u0435\u043c \u0438\u043b\u0438 \u0443\u0447\u0430\u0441\u0442\u0438\u0435\u043c \u0441\u0441\u044b\u043b\u043e\u043a"
::msgcat::mcset ru "Conflict file does not exist" "\u041a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0443\u044e\u0449\u0438\u0439 \u0444\u0430\u0439\u043b \u043d\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442"
::msgcat::mcset ru "Not a GUI merge tool: '%s'" "\u00ab%s\u00bb \u043d\u0435 \u044f\u0432\u043b\u044f\u0435\u0442\u0441\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u043e\u0439 \u0441\u043b\u0438\u044f\u043d\u0438\u044f"
::msgcat::mcset ru "Unsupported merge tool '%s'" "\u041d\u0435\u043f\u043e\u0434\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0435\u043c\u0430\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u0441\u043b\u0438\u044f\u043d\u0438\u044f \u00ab%s\u00bb"
::msgcat::mcset ru "Merge tool is already running, terminate it?" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 \u0441\u043b\u0438\u044f\u043d\u0438\u044f \u0443\u0436\u0435 \u0440\u0430\u0431\u043e\u0442\u0430\u0435\u0442. \u041f\u0440\u0435\u0440\u0432\u0430\u0442\u044c?"
::msgcat::mcset ru "Error retrieving versions:\n%s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u043f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u044f \u0432\u0435\u0440\u0441\u0438\u0439:\n%s"
::msgcat::mcset ru "Could not start the merge tool:\n\n%s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0437\u0430\u043f\u0443\u0441\u043a\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b \u0441\u043b\u0438\u044f\u043d\u0438\u044f:\n\n%s"
::msgcat::mcset ru "Running merge tool..." "\u0417\u0430\u043f\u0443\u0441\u043a \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b \u0441\u043b\u0438\u044f\u043d\u0438\u044f\u2026"
::msgcat::mcset ru "Merge tool failed." "\u041e\u0448\u0438\u0431\u043a\u0430 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b \u0441\u043b\u0438\u044f\u043d\u0438\u044f."
::msgcat::mcset ru "%s (%s): Add Tool" "%s (%s): \u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u0438\u043d\u0441\u0442\u0440\u0443\u043c\u0435\u043d\u0442"
::msgcat::mcset ru "Add New Tool Command" "\u041d\u043e\u0432\u0430\u044f \u0432\u0441\u043f\u043e\u043c\u043e\u0433\u0430\u0442\u0435\u043b\u044c\u043d\u0430\u044f \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044f"
::msgcat::mcset ru "Add globally" "\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u0434\u043b\u044f \u0432\u0441\u0435\u0445 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0435\u0432"
::msgcat::mcset ru "Tool Details" "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u0432\u0441\u043f\u043e\u043c\u043e\u0433\u0430\u0442\u0435\u043b\u044c\u043d\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438"
::msgcat::mcset ru "Use '/' separators to create a submenu tree:" "\u0418\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u0439\u0442\u0435 \u00ab/\u00bb \u0434\u043b\u044f \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f \u043f\u043e\u0434\u043c\u0435\u043d\u044e"
::msgcat::mcset ru "Command:" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430:"
::msgcat::mcset ru "Show a dialog before running" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0434\u0438\u0430\u043b\u043e\u0433 \u043f\u0435\u0440\u0435\u0434 \u0437\u0430\u043f\u0443\u0441\u043a\u043e\u043c"
::msgcat::mcset ru "Ask the user to select a revision (sets \$REVISION)" "\u0417\u0430\u043f\u0440\u043e\u0441 \u043d\u0430 \u0432\u044b\u0431\u043e\u0440 \u0432\u0435\u0440\u0441\u0438\u0438 (\u0443\u0441\u0442\u0430\u043d\u0430\u0432\u043b\u0438\u0432\u0430\u0435\u0442 \$REVISION)"
::msgcat::mcset ru "Ask the user for additional arguments (sets \$ARGS)" "\u0417\u0430\u043f\u0440\u043e\u0441 \u0434\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u044b\u0445 \u0430\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u043e\u0432 (\u0443\u0441\u0442\u0430\u043d\u0430\u0432\u043b\u0438\u0432\u0430\u0435\u0442 \$ARGS)"
::msgcat::mcset ru "Don't show the command output window" "\u041d\u0435 \u043f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u043e\u043a\u043d\u043e \u0432\u044b\u0432\u043e\u0434\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u044b"
::msgcat::mcset ru "Run only if a diff is selected (\$FILENAME not empty)" "\u0417\u0430\u043f\u0443\u0441\u043a \u0442\u043e\u043b\u044c\u043a\u043e \u0435\u0441\u043b\u0438 \u043f\u043e\u043a\u0430\u0437\u0430\u043d \u0441\u043f\u0438\u0441\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 (\$FILENAME \u043d\u0435 \u043f\u0443\u0441\u0442\u043e)"
::msgcat::mcset ru "Please supply a name for the tool." "\u0423\u043a\u0430\u0436\u0438\u0442\u0435 \u043d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u0432\u0441\u043f\u043e\u043c\u043e\u0433\u0430\u0442\u0435\u043b\u044c\u043d\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438."
::msgcat::mcset ru "Tool '%s' already exists." "\u0412\u0441\u043f\u043e\u043c\u043e\u0433\u0430\u0442\u0435\u043b\u044c\u043d\u0430\u044f \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044f \u00ab%s\u00bb \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442."
::msgcat::mcset ru "Could not add tool:\n%s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0434\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u0438\u044f \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b:\n%s"
::msgcat::mcset ru "%s (%s): Remove Tool" "%s (%s): \u0423\u0434\u0430\u043b\u0438\u0442\u044c \u0438\u043d\u0441\u0442\u0440\u0443\u043c\u0435\u043d\u0442"
::msgcat::mcset ru "Remove Tool Commands" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u043a\u043e\u043c\u0430\u043d\u0434\u044b \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b"
::msgcat::mcset ru "Remove" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c"
::msgcat::mcset ru "(Blue denotes repository-local tools)" "(\u0421\u0438\u043d\u0438\u043c \u0432\u044b\u0434\u0435\u043b\u0435\u043d\u044b \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u044b\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044e)"
::msgcat::mcset ru "%s (%s):" "%s (%s):"
::msgcat::mcset ru "Run Command: %s" "\u0417\u0430\u043f\u0443\u0441\u043a \u043a\u043e\u043c\u0430\u043d\u0434\u044b: %s"
::msgcat::mcset ru "Arguments" "\u0410\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u044b"
::msgcat::mcset ru "OK" "OK"
::msgcat::mcset ru "Find:" "\u041f\u043e\u0438\u0441\u043a:"
::msgcat::mcset ru "Next" "\u0414\u0430\u043b\u044c\u0448\u0435"
::msgcat::mcset ru "Prev" "\u041e\u0431\u0440\u0430\u0442\u043d\u043e"
::msgcat::mcset ru "RegExp" "\u0420\u0435\u0433\u0443\u043b\u044f\u0440\u043d\u044b\u0435 \u0432\u044b\u0440\u0430\u0436\u0435\u043d\u0438\u044f"
::msgcat::mcset ru "Case" "\u0423\u0447\u0451\u0442 \u0440\u0435\u0433\u0438\u0441\u0442\u0440\u0430"
::msgcat::mcset ru "%s (%s): Create Desktop Icon" "%s (%s): \u0421\u043e\u0437\u0434\u0430\u0442\u044c \u044f\u0440\u043b\u044b\u043a \u043d\u0430 \u0440\u0430\u0431\u043e\u0447\u0435\u043c \u0441\u0442\u043e\u043b\u0435"
::msgcat::mcset ru "Cannot write shortcut:" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0437\u0430\u043f\u0438\u0441\u0430\u0442\u044c \u0441\u0441\u044b\u043b\u043a\u0443:"
::msgcat::mcset ru "Cannot write icon:" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0437\u0430\u043f\u0438\u0441\u0430\u0442\u044c \u0437\u043d\u0430\u0447\u043e\u043a:"
::msgcat::mcset ru "%s (%s): Delete Branch Remotely" "%s (%s): \u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435 \u0432\u043d\u0435\u0448\u043d\u0435\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Delete Branch Remotely" "\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435 \u0432\u0435\u0442\u043a\u0438 \u0432\u043e \u0432\u043d\u0435\u0448\u043d\u0435\u043c \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0438"
::msgcat::mcset ru "From Repository" "\u0418\u0437 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f"
::msgcat::mcset ru "Branches" "\u0412\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Delete Only If" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u0442\u043e\u043b\u044c\u043a\u043e \u0432 \u0441\u043b\u0443\u0447\u0430\u0435, \u0435\u0441\u043b\u0438"
::msgcat::mcset ru "Merged Into:" "\u0421\u043b\u0438\u044f\u043d\u0438\u0435 \u0441:"
::msgcat::mcset ru "Always (Do not perform merge checks)" "\u0412\u0441\u0435\u0433\u0434\u0430 (\u043d\u0435 \u0432\u044b\u043f\u043e\u043b\u043d\u044f\u0442\u044c \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0443 \u043d\u0430 \u0441\u043b\u0438\u044f\u043d\u0438\u0435)"
::msgcat::mcset ru "A branch is required for 'Merged Into'." "\u0414\u043b\u044f \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u00ab\u0421\u043b\u0438\u044f\u043d\u0438\u0435 \u0441\u00bb \u0442\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044f \u0443\u043a\u0430\u0437\u0430\u0442\u044c \u0432\u0435\u0442\u043a\u0443."
::msgcat::mcset ru "The following branches are not completely merged into %s:\n\n - %s" "\u0421\u043b\u0435\u0434\u0443\u044e\u0449\u0438\u0435 \u0432\u0435\u0442\u043a\u0438 \u043c\u043e\u0433\u0443\u0442 \u0431\u044b\u0442\u044c \u043e\u0431\u044a\u0435\u0434\u0438\u043d\u0435\u043d\u044b \u0441 %s \u043f\u0440\u0438 \u043f\u043e\u043c\u043e\u0449\u0438 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u044f:\n\n - %s"
::msgcat::mcset ru "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "\u041d\u0435\u043a\u043e\u0442\u043e\u0440\u044b\u0435 \u0442\u0435\u0441\u0442\u044b \u043d\u0430 \u0441\u043b\u0438\u044f\u043d\u0438\u0435 \u043d\u0435 \u043f\u0440\u043e\u0448\u043b\u0438, \u043f\u043e\u0442\u043e\u043c\u0443 \u0447\u0442\u043e \u0432\u044b \u043d\u0435 \u0438\u0437\u0432\u043b\u0435\u043a\u043b\u0438 \u043d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u044b\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u044b. \u041f\u043e\u043f\u044b\u0442\u0430\u0439\u0442\u0435\u0441\u044c \u0438\u0437\u0432\u043b\u0435\u0447\u044c \u0438\u0445 \u0438\u0437 %s."
::msgcat::mcset ru "Please select one or more branches to delete." "\u0423\u043a\u0430\u0436\u0438\u0442\u0435 \u043e\u0434\u043d\u0443 \u0438\u043b\u0438 \u043d\u0435\u0441\u043a\u043e\u043b\u044c\u043a\u043e \u0432\u0435\u0442\u043e\u043a \u0434\u043b\u044f \u0443\u0434\u0430\u043b\u0435\u043d\u0438\u044f."
::msgcat::mcset ru "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u044c \u0443\u0434\u0430\u043b\u0435\u043d\u043d\u044b\u0435 \u0432\u0435\u0442\u043a\u0438 \u0441\u043b\u043e\u0436\u043d\u043e.\n\n\u041f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u044c?"
::msgcat::mcset ru "Deleting branches from %s" "\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435 \u0432\u0435\u0442\u043e\u043a \u0438\u0437 %s"
::msgcat::mcset ru "No repository selected." "\u041d\u0435 \u0443\u043a\u0430\u0437\u0430\u043d \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439."
::msgcat::mcset ru "Scanning %s..." "\u041f\u0435\u0440\u0435\u0447\u0438\u0442\u044b\u0432\u0430\u043d\u0438\u0435 %s\u2026"
::msgcat::mcset ru "Git Gui" "Git Gui"
::msgcat::mcset ru "Create New Repository" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c \u043d\u043e\u0432\u044b\u0439 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439"
::msgcat::mcset ru "New..." "\u041d\u043e\u0432\u044b\u0439\u2026"
::msgcat::mcset ru "Clone Existing Repository" "\u0421\u043a\u043b\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044e\u0449\u0438\u0439 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439"
::msgcat::mcset ru "Clone..." "\u041a\u043b\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u0442\u044c\u2026"
::msgcat::mcset ru "Open Existing Repository" "\u0412\u044b\u0431\u0440\u0430\u0442\u044c \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044e\u0449\u0438\u0439 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439"
::msgcat::mcset ru "Open..." "\u041e\u0442\u043a\u0440\u044b\u0442\u044c\u2026"
::msgcat::mcset ru "Recent Repositories" "\u041d\u0435\u0434\u0430\u0432\u043d\u0438\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0438"
::msgcat::mcset ru "Open Recent Repository:" "\u041e\u0442\u043a\u0440\u044b\u0442\u044c \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0439 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439"
::msgcat::mcset ru "Failed to create repository %s:" "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0441\u043e\u0437\u0434\u0430\u0442\u044c \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 %s:"
::msgcat::mcset ru "Create" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c"
::msgcat::mcset ru "Directory:" "\u041a\u0430\u0442\u0430\u043b\u043e\u0433:"
::msgcat::mcset ru "Git Repository" "\u0420\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439"
::msgcat::mcset ru "Directory %s already exists." "\u041a\u0430\u0442\u0430\u043b\u043e\u0433 '%s' \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442."
::msgcat::mcset ru "File %s already exists." "\u0424\u0430\u0439\u043b '%s' \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442."
::msgcat::mcset ru "Clone" "\u0421\u043a\u043b\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u0442\u044c"
::msgcat::mcset ru "Source Location:" "\u0418\u0441\u0445\u043e\u0434\u043d\u043e\u0435 \u043f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435:"
::msgcat::mcset ru "Target Directory:" "\u041a\u0430\u0442\u0430\u043b\u043e\u0433 \u043d\u0430\u0437\u043d\u0430\u0447\u0435\u043d\u0438\u044f:"
::msgcat::mcset ru "Clone Type:" "\u0422\u0438\u043f \u043a\u043b\u043e\u043d\u0430:"
::msgcat::mcset ru "Standard (Fast, Semi-Redundant, Hardlinks)" "\u0421\u0442\u0430\u043d\u0434\u0430\u0440\u0442\u043d\u044b\u0439 (\u0411\u044b\u0441\u0442\u0440\u044b\u0439, \u043f\u043e\u043b\u0443\u0438\u0437\u0431\u044b\u0442\u043e\u0447\u043d\u044b\u0439, \u00ab\u0436\u0435\u0441\u0442\u043a\u0438\u0435\u00bb \u0441\u0441\u044b\u043b\u043a\u0438)"
::msgcat::mcset ru "Full Copy (Slower, Redundant Backup)" "\u041f\u043e\u043b\u043d\u0430\u044f \u043a\u043e\u043f\u0438\u044f (\u041c\u0435\u0434\u043b\u0435\u043d\u043d\u044b\u0439, \u0441\u043e\u0437\u0434\u0430\u0435\u0442 \u0440\u0435\u0437\u0435\u0440\u0432\u043d\u0443\u044e \u043a\u043e\u043f\u0438\u044e)"
::msgcat::mcset ru "Shared (Fastest, Not Recommended, No Backup)" "\u041e\u0431\u0449\u0438\u0439 (\u0421\u0430\u043c\u044b\u0439 \u0431\u044b\u0441\u0442\u0440\u044b\u0439, \u043d\u0435 \u0440\u0435\u043a\u043e\u043c\u0435\u043d\u0434\u0443\u0435\u0442\u0441\u044f, \u0431\u0435\u0437 \u0440\u0435\u0437\u0435\u0440\u0432\u043d\u043e\u0439 \u043a\u043e\u043f\u0438\u0438)"
::msgcat::mcset ru "Recursively clone submodules too" "\u0422\u0430\u043a\u0436\u0435 \u0440\u0435\u043a\u0443\u0440\u0441\u0438\u0432\u043d\u043e \u043a\u043b\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0438"
::msgcat::mcset ru "Not a Git repository: %s" "\u041a\u0430\u0442\u0430\u043b\u043e\u0433 \u043d\u0435 \u044f\u0432\u043b\u044f\u0435\u0442\u0441\u044f \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0435\u043c Git: %s"
::msgcat::mcset ru "Standard only available for local repository." "\u0421\u0442\u0430\u043d\u0434\u0430\u0440\u0442\u043d\u044b\u0439 \u043a\u043b\u043e\u043d \u0432\u043e\u0437\u043c\u043e\u0436\u0435\u043d \u0442\u043e\u043b\u044c\u043a\u043e \u0434\u043b\u044f \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u043e\u0433\u043e \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f."
::msgcat::mcset ru "Shared only available for local repository." "\u041e\u0431\u0449\u0438\u0439 \u043a\u043b\u043e\u043d \u0432\u043e\u0437\u043c\u043e\u0436\u0435\u043d \u0442\u043e\u043b\u044c\u043a\u043e \u0434\u043b\u044f \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u043e\u0433\u043e \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f."
::msgcat::mcset ru "Location %s already exists." "\u041f\u0443\u0442\u044c %s \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442."
::msgcat::mcset ru "Failed to configure origin" "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0441\u043a\u043e\u043d\u0444\u0438\u0433\u0443\u0440\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0438\u0441\u0445\u043e\u0434\u043d\u044b\u0439 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439"
::msgcat::mcset ru "Counting objects" "\u041f\u043e\u0434\u0441\u0447\u0451\u0442 \u043e\u0431\u044a\u0435\u043a\u0442\u043e\u0432"
::msgcat::mcset ru "buckets" "\u0431\u043b\u043e\u043a\u0438"
::msgcat::mcset ru "Unable to copy objects/info/alternates: %s" "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0441\u043a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c objects/info/alternates: %s"
::msgcat::mcset ru "Nothing to clone from %s." "\u041d\u0435\u0447\u0435\u0433\u043e \u043a\u043b\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0441 %s."
::msgcat::mcset ru "The 'master' branch has not been initialized." "\u041d\u0435 \u0438\u043d\u0438\u0446\u0438\u0430\u043b\u0438\u0437\u0438\u0440\u043e\u0432\u0430\u043d\u0430 \u0432\u0435\u0442\u043a\u0430 \u00abmaster\u00bb."
::msgcat::mcset ru "Hardlinks are unavailable.  Falling back to copying." "\u0416\u0435\u0441\u0442\u043a\u0438\u0435 \u0441\u0441\u044b\u043b\u043a\u0438 \u043d\u0435\u0434\u043e\u0441\u0442\u0443\u043f\u043d\u044b. \u0411\u0443\u0434\u0435\u0442 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u043e \u043a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435."
::msgcat::mcset ru "Cloning from %s" "\u041a\u043b\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u0438\u0437 %s"
::msgcat::mcset ru "Copying objects" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043e\u0431\u044a\u0435\u043a\u0442\u043e\u0432"
::msgcat::mcset ru "KiB" "\u041a\u0411"
::msgcat::mcset ru "Unable to copy object: %s" "\u041d\u0435 \u043c\u043e\u0433\u0443 \u0441\u043a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043e\u0431\u044a\u0435\u043a\u0442: %s"
::msgcat::mcset ru "Linking objects" "\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435 \u0441\u0441\u044b\u043b\u043e\u043a \u043d\u0430 objects"
::msgcat::mcset ru "objects" "\u043e\u0431\u044a\u0435\u043a\u0442\u044b"
::msgcat::mcset ru "Unable to hardlink object: %s" "\u041d\u0435 \u043c\u043e\u0433\u0443 \u0441\u043e\u0437\u0434\u0430\u0442\u044c \u00ab\u0436\u0435\u0441\u0442\u043a\u0443\u044e \u0441\u0441\u044b\u043b\u043a\u0443\u00bb \u043d\u0430 \u043e\u0431\u044a\u0435\u043a\u0442: %s"
::msgcat::mcset ru "Cannot fetch branches and objects.  See console output for details." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0438\u0437\u0432\u043b\u0435\u0447\u044c \u0432\u0435\u0442\u043a\u0438 \u0438 \u043e\u0431\u044a\u0435\u043a\u0442\u044b. \u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u0430\u044f \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043d\u0430 \u043a\u043e\u043d\u0441\u043e\u043b\u0438."
::msgcat::mcset ru "Cannot fetch tags.  See console output for details." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0438\u0437\u0432\u043b\u0435\u0447\u044c \u043c\u0435\u0442\u043a\u0438. \u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u0430\u044f \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043d\u0430 \u043a\u043e\u043d\u0441\u043e\u043b\u0438."
::msgcat::mcset ru "Cannot determine HEAD.  See console output for details." "\u041d\u0435 \u043c\u043e\u0433\u0443 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0438\u0442\u044c HEAD. \u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u0430\u044f \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043d\u0430 \u043a\u043e\u043d\u0441\u043e\u043b\u0438."
::msgcat::mcset ru "Unable to cleanup %s" "\u041d\u0435 \u043c\u043e\u0433\u0443 \u043e\u0447\u0438\u0441\u0442\u0438\u0442\u044c %s"
::msgcat::mcset ru "Clone failed." "\u041a\u043b\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c."
::msgcat::mcset ru "No default branch obtained." "\u0412\u0435\u0442\u043a\u0430 \u043f\u043e \u0443\u043c\u043e\u043b\u0447\u0430\u043d\u0438\u044e \u043d\u0435 \u0431\u044b\u043b\u0430 \u043f\u043e\u043b\u0443\u0447\u0435\u043d\u0430."
::msgcat::mcset ru "Cannot resolve %s as a commit." "\u041d\u0435 \u043c\u043e\u0433\u0443 \u0440\u0430\u0441\u043f\u043e\u0437\u043d\u0430\u0442\u044c %s \u043a\u0430\u043a \u043a\u043e\u043c\u043c\u0438\u0442."
::msgcat::mcset ru "Creating working directory" "\u0421\u043e\u0437\u0434\u0430\u044e \u0440\u0430\u0431\u043e\u0447\u0438\u0439 \u043a\u0430\u0442\u0430\u043b\u043e\u0433"
::msgcat::mcset ru "Initial file checkout failed." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043f\u043e\u043b\u0443\u0447\u0438\u0442\u044c \u043d\u0430\u0447\u0430\u043b\u044c\u043d\u043e\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u0444\u0430\u0439\u043b\u043e\u0432 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f."
::msgcat::mcset ru "Cloning submodules" "\u041a\u043b\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0435\u0439"
::msgcat::mcset ru "Cannot clone submodules." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043a\u043b\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043f\u043e\u0434\u043c\u043e\u0434\u0443\u043b\u0438."
::msgcat::mcset ru "Repository:" "\u0420\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439:"
::msgcat::mcset ru "Failed to open repository %s:" "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043e\u0442\u043a\u0440\u044b\u0442\u044c \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 %s:"
::msgcat::mcset ru "git-gui - a graphical user interface for Git." "git-gui - \u0433\u0440\u0430\u0444\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044c\u0441\u043a\u0438\u0439 \u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441 \u043a Git."
::msgcat::mcset ru "%s (%s): File Viewer" "%s (%s): \u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0444\u0430\u0439\u043b\u0430"
::msgcat::mcset ru "Commit:" "\u041a\u043e\u043c\u043c\u0438\u0442:"
::msgcat::mcset ru "Copy Commit" "\u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c SHA-1"
::msgcat::mcset ru "Find Text..." "\u041d\u0430\u0439\u0442\u0438 \u0442\u0435\u043a\u0441\u0442\u2026"
::msgcat::mcset ru "Goto Line..." "\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u0441\u0442\u0440\u043e\u043a\u0443\u2026"
::msgcat::mcset ru "Do Full Copy Detection" "\u041f\u0440\u043e\u0432\u0435\u0441\u0442\u0438 \u043f\u043e\u043b\u043d\u044b\u0439 \u043f\u043e\u0438\u0441\u043a \u043a\u043e\u043f\u0438\u0439"
::msgcat::mcset ru "Show History Context" "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u0438\u0441\u0442\u043e\u0440\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442"
::msgcat::mcset ru "Blame Parent Commit" "\u0410\u0432\u0442\u043e\u0440\u044b \u0440\u043e\u0434\u0438\u0442\u0435\u043b\u044c\u0441\u043a\u043e\u0433\u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0430"
::msgcat::mcset ru "Reading %s..." "\u0427\u0442\u0435\u043d\u0438\u0435 %s\u2026"
::msgcat::mcset ru "Loading copy/move tracking annotations..." "\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430 \u0430\u043d\u043d\u043e\u0442\u0430\u0446\u0438\u0438 \u043a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0439/\u043f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0439\u2026"
::msgcat::mcset ru "lines annotated" "\u0441\u0442\u0440\u043e\u043a \u043f\u0440\u043e\u043a\u043e\u043c\u043c\u0435\u043d\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u043e"
::msgcat::mcset ru "Loading original location annotations..." "\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430 \u0430\u043d\u043d\u043e\u0442\u0430\u0446\u0438\u0439 \u043f\u0435\u0440\u0432\u043e\u043d\u0430\u0447\u0430\u043b\u044c\u043d\u043e\u0433\u043e \u043f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u044f \u043e\u0431\u044a\u0435\u043a\u0442\u0430\u2026"
::msgcat::mcset ru "Annotation complete." "\u0410\u043d\u043d\u043e\u0442\u0430\u0446\u0438\u044f \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u0430."
::msgcat::mcset ru "Busy" "\u0417\u0430\u043d\u044f\u0442"
::msgcat::mcset ru "Annotation process is already running." "\u0410\u043d\u043d\u043e\u0442\u0430\u0446\u0438\u044f \u0443\u0436\u0435 \u0437\u0430\u043f\u0443\u0449\u0435\u043d\u0430"
::msgcat::mcset ru "Running thorough copy detection..." "\u0412\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u0435 \u043f\u043e\u043b\u043d\u043e\u0433\u043e \u043f\u043e\u0438\u0441\u043a\u0430 \u043a\u043e\u043f\u0438\u0439\u2026"
::msgcat::mcset ru "Loading annotation..." "\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430 \u0430\u043d\u043d\u043e\u0442\u0430\u0446\u0438\u0438\u2026"
::msgcat::mcset ru "Author:" "\u0410\u0432\u0442\u043e\u0440:"
::msgcat::mcset ru "Committer:" "\u041a\u043e\u043c\u043c\u0438\u0442\u0435\u0440:"
::msgcat::mcset ru "Original File:" "\u0418\u0441\u0445\u043e\u0434\u043d\u044b\u0439 \u0444\u0430\u0439\u043b:"
::msgcat::mcset ru "Cannot find HEAD commit:" "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043d\u0430\u0439\u0442\u0438 \u0442\u0435\u043a\u0443\u0449\u0435\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435:"
::msgcat::mcset ru "Cannot find parent commit:" "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043d\u0430\u0439\u0442\u0438 \u0440\u043e\u0434\u0438\u0442\u0435\u043b\u044c\u0441\u043a\u043e\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435:"
::msgcat::mcset ru "Unable to display parent" "\u041d\u0435 \u043c\u043e\u0433\u0443 \u043f\u043e\u043a\u0430\u0437\u0430\u0442\u044c \u043f\u0440\u0435\u0434\u043a\u0430"
::msgcat::mcset ru "Error loading diff:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043a\u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439:"
::msgcat::mcset ru "Originally By:" "\u0418\u0441\u0442\u043e\u0447\u043d\u0438\u043a:"
::msgcat::mcset ru "In File:" "\u0424\u0430\u0439\u043b:"
::msgcat::mcset ru "Copied Or Moved Here By:" "\u0421\u043a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d\u043e/\u043f\u0435\u0440\u0435\u043c\u0435\u0449\u0435\u043d\u043e \u0432:"
::msgcat::mcset ru "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043d\u0435 \u043e\u0431\u043d\u0430\u0440\u0443\u0436\u0435\u043d\u043e.\n\n\u0432 %s \u043e\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044e\u0442 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f.\n\n\u0414\u0430\u0442\u0430 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0444\u0430\u0439\u043b\u0430 \u0431\u044b\u043b\u0430 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0430 \u0434\u0440\u0443\u0433\u043e\u0439 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u043e\u0439, \u043d\u043e \u0441\u043e\u0434\u0435\u0440\u0436\u0438\u043c\u043e\u0435 \u0444\u0430\u0439\u043b\u0430 \u043e\u0441\u0442\u0430\u043b\u043e\u0441\u044c \u043f\u0440\u0435\u0436\u043d\u0438\u043c.\n\n\u0421\u0435\u0439\u0447\u0430\u0441 \u0431\u0443\u0434\u0435\u0442 \u0437\u0430\u043f\u0443\u0449\u0435\u043d\u043e \u043f\u0435\u0440\u0435\u0447\u0438\u0442\u044b\u0432\u0430\u043d\u0438\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f, \u0447\u0442\u043e\u0431\u044b \u043d\u0430\u0439\u0442\u0438 \u043f\u043e\u0434\u043e\u0431\u043d\u044b\u0435 \u0444\u0430\u0439\u043b\u044b."
::msgcat::mcset ru "Loading diff of %s..." "\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 %s\u2026"
::msgcat::mcset ru "LOCAL: deleted\nREMOTE:\n" "\u041b\u041e\u041a\u0410\u041b\u042c\u041d\u041e: \u0443\u0434\u0430\u043b\u0451\u043d\n\u0412\u041d\u0415\u0428\u041d\u0418\u0419:\n"
::msgcat::mcset ru "REMOTE: deleted\nLOCAL:\n" "\u0412\u041d\u0415\u0428\u041d\u0418\u0419: \u0443\u0434\u0430\u043b\u0451\u043d\n\u041b\u041e\u041a\u0410\u041b\u042c\u041d\u041e:\n"
::msgcat::mcset ru "LOCAL:\n" "\u041b\u041e\u041a\u0410\u041b\u042c\u041d\u041e:\n"
::msgcat::mcset ru "REMOTE:\n" "\u0412\u041d\u0415\u0428\u041d\u0418\u0419:\n"
::msgcat::mcset ru "Unable to display %s" "\u041d\u0435 \u043c\u043e\u0433\u0443 \u043f\u043e\u043a\u0430\u0437\u0430\u0442\u044c %s"
::msgcat::mcset ru "Error loading file:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043a\u0438 \u0444\u0430\u0439\u043b\u0430:"
::msgcat::mcset ru "Git Repository (subproject)" "\u0420\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 Git (\u043f\u043e\u0434\u043f\u0440\u043e\u0435\u043a\u0442)"
::msgcat::mcset ru "* Binary file (not showing content)." "* \u0414\u0432\u043e\u0438\u0447\u043d\u044b\u0439 \u0444\u0430\u0439\u043b (\u0441\u043e\u0434\u0435\u0440\u0436\u0438\u043c\u043e\u0435 \u043d\u0435 \u043f\u043e\u043a\u0430\u0437\u0430\u043d\u043e)"
::msgcat::mcset ru "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* \u0420\u0430\u0437\u043c\u0435\u0440 \u043d\u0435\u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u043e\u0433\u043e \u0444\u0430\u0439\u043b\u0430 %d \u0431\u0430\u0439\u0442.\n* \u041f\u043e\u043a\u0430\u0437\u0430\u043d\u043e \u043f\u0435\u0440\u0432\u044b\u0445 %d \u0431\u0430\u0439\u0442.\n"
::msgcat::mcset ru "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* \u041d\u0435\u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u044b\u0439 \u0444\u0430\u0439\u043b \u043e\u0431\u0440\u0435\u0437\u0430\u043d: %s.\n* \u0427\u0442\u043e\u0431\u044b \u0443\u0432\u0438\u0434\u0435\u0442\u044c \u0432\u0435\u0441\u044c \u0444\u0430\u0439\u043b, \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u0439\u0442\u0435 \u0432\u043d\u0435\u0448\u043d\u0438\u0439 \u0440\u0435\u0434\u0430\u043a\u0442\u043e\u0440.\n"
::msgcat::mcset ru "Failed to unstage selected hunk." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0438\u0441\u043a\u043b\u044e\u0447\u0438\u0442\u044c \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u0443\u044e \u0447\u0430\u0441\u0442\u044c."
::msgcat::mcset ru "Failed to revert selected hunk." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u043e\u0433\u043e \u0431\u043b\u043e\u043a\u0430."
::msgcat::mcset ru "Failed to stage selected hunk." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043f\u0440\u043e\u0438\u043d\u0434\u0435\u043a\u0441\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u044b\u0439 \u0431\u043b\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439."
::msgcat::mcset ru "Failed to unstage selected line." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0438\u0441\u043a\u043b\u044e\u0447\u0438\u0442\u044c \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u0443\u044e \u0441\u0442\u0440\u043e\u043a\u0443."
::msgcat::mcset ru "Failed to revert selected line." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043e\u0431\u0440\u0430\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432\u044b\u0431\u0440\u0430\u043d\u043e\u0439 \u0441\u0442\u0440\u043e\u043a\u0438."
::msgcat::mcset ru "Failed to stage selected line." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043f\u0440\u043e\u0438\u043d\u0434\u0435\u043a\u0441\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0432\u044b\u0431\u0440\u0430\u043d\u043d\u0443\u044e \u0441\u0442\u0440\u043e\u043a\u0443."
::msgcat::mcset ru "Failed to undo last revert." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u043e\u0442\u043c\u0435\u043d\u0438\u0442\u044c \u043f\u043e\u0441\u043b\u0435\u043d\u0434\u043d\u0435\u0435 \u043e\u0431\u0440\u0430\u0449\u0435\u043d\u0438\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439."
::msgcat::mcset ru "No keys found." "\u041a\u043b\u044e\u0447 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d"
::msgcat::mcset ru "Found a public key in: %s" "\u041f\u0443\u0431\u043b\u0438\u0447\u043d\u044b\u0439 \u043a\u043b\u044e\u0447 \u0438\u0437 %s"
::msgcat::mcset ru "Generate Key" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c \u043a\u043b\u044e\u0447"
::msgcat::mcset ru "Copy To Clipboard" "\u0421\u043a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0432 \u0431\u0443\u0444\u0435\u0440 \u043e\u0431\u043c\u0435\u043d\u0430"
::msgcat::mcset ru "Your OpenSSH Public Key" "\u0412\u0430\u0448 \u043f\u0443\u0431\u043b\u0438\u0447\u043d\u044b\u0439 \u043a\u043b\u044e\u0447 OpenSSH"
::msgcat::mcset ru "Generating..." "\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435\u2026"
::msgcat::mcset ru "Could not start ssh-keygen:\n\n%s" "\u041e\u0448\u0438\u0431\u043a\u0430 \u0437\u0430\u043f\u0443\u0441\u043a\u0430 ssh-keygen:\n\n%s"
::msgcat::mcset ru "Generation failed." "\u041a\u043b\u044e\u0447 \u043d\u0435 \u0441\u043e\u0437\u0434\u0430\u043d."
::msgcat::mcset ru "Generation succeeded, but no keys found." "\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435 \u043a\u043b\u044e\u0447\u0430 \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u043b\u043e\u0441\u044c, \u043d\u043e \u0440\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442 \u043d\u0435 \u0431\u044b\u043b \u043d\u0430\u0439\u0434\u0435\u043d"
::msgcat::mcset ru "Your key is in: %s" "\u0412\u0430\u0448 \u043a\u043b\u044e\u0447 \u043d\u0430\u0445\u043e\u0434\u0438\u0442\u0441\u044f \u0432: %s"
::msgcat::mcset ru "%s (%s): Create Branch" "%s (%s): \u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Create New Branch" "\u0421\u043e\u0437\u0434\u0430\u0442\u044c \u043d\u043e\u0432\u0443\u044e \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Branch Name" "\u0418\u043c\u044f \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Match Tracking Branch Name" "\u0421\u043e\u043e\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u043e\u0432\u0430\u0442\u044c \u0438\u043c\u0435\u043d\u0438 \u043e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u043e\u0439 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Starting Revision" "\u041d\u0430\u0447\u0430\u043b\u044c\u043d\u0430\u044f \u0432\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset ru "Update Existing Branch:" "\u041e\u0431\u043d\u043e\u0432\u0438\u0442\u044c \u0438\u043c\u0435\u044e\u0449\u0443\u044e\u0441\u044f \u0432\u0435\u0442\u043a\u0443:"
::msgcat::mcset ru "No" "\u041d\u0435\u0442"
::msgcat::mcset ru "Fast Forward Only" "\u0422\u043e\u043b\u044c\u043a\u043e Fast Forward"
::msgcat::mcset ru "Checkout After Creation" "\u041f\u043e\u0441\u043b\u0435 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f \u0441\u0434\u0435\u043b\u0430\u0442\u044c \u0442\u0435\u043a\u0443\u0449\u0435\u0439"
::msgcat::mcset ru "Please select a tracking branch." "\u0423\u043a\u0430\u0436\u0438\u0442\u0435 \u043e\u0442\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u0443\u044e \u0432\u0435\u0442\u043a\u0443."
::msgcat::mcset ru "Tracking branch %s is not a branch in the remote repository." "\u041e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u0430\u044f \u0432\u0435\u0442\u043a\u0430 %s \u043d\u0435 \u044f\u0432\u043b\u044f\u0435\u0442\u0441\u044f \u0432\u0435\u0442\u043a\u043e\u0439 \u043d\u0430 \u0432\u043d\u0435\u0448\u043d\u0435\u043c \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0438."
::msgcat::mcset ru "Working... please wait..." "\u0412 \u043f\u0440\u043e\u0446\u0435\u0441\u0441\u0435\u2026 \u043f\u043e\u0436\u0430\u043b\u0443\u0439\u0441\u0442\u0430, \u0436\u0434\u0438\u0442\u0435\u2026"
::msgcat::mcset ru "Success" "\u041f\u0440\u043e\u0446\u0435\u0441\u0441 \u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d"
::msgcat::mcset ru "Error: Command Failed" "\u041e\u0448\u0438\u0431\u043a\u0430: \u043d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0432\u044b\u043f\u043e\u043b\u043d\u0438\u0442\u044c \u043a\u043e\u043c\u0430\u043d\u0434\u0443"
::msgcat::mcset ru "Goto Line:" "\u041f\u0435\u0440\u0435\u0439\u0442\u0438 \u043d\u0430 \u0441\u0442\u0440\u043e\u043a\u0443:"
::msgcat::mcset ru "Go" "\u041f\u0435\u0440\u0435\u0439\u0442\u0438"
::msgcat::mcset ru "This Detached Checkout" "\u0422\u0435\u043a\u0443\u0449\u0435\u0435 \u043e\u0442\u0441\u043e\u0435\u0434\u0438\u043d\u0435\u043d\u043d\u043e\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435"
::msgcat::mcset ru "Revision Expression:" "\u0412\u044b\u0440\u0430\u0436\u0435\u043d\u0438\u0435 \u0434\u043b\u044f \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438\u044f \u0432\u0435\u0440\u0441\u0438\u0438:"
::msgcat::mcset ru "Local Branch" "\u041b\u043e\u043a\u0430\u043b\u044c\u043d\u0430\u044f \u0432\u0435\u0442\u043a\u0430:"
::msgcat::mcset ru "Tracking Branch" "\u041e\u0442\u0441\u043b\u0435\u0436\u0438\u0432\u0430\u0435\u043c\u0430\u044f \u0432\u0435\u0442\u043a\u0430"
::msgcat::mcset ru "Tag" "\u041c\u0435\u0442\u043a\u0430"
::msgcat::mcset ru "Invalid revision: %s" "\u041d\u0435\u0432\u0435\u0440\u043d\u0430\u044f \u0432\u0435\u0440\u0441\u0438\u044f: %s"
::msgcat::mcset ru "No revision selected." "\u0412\u0435\u0440\u0441\u0438\u044f \u043d\u0435 \u0443\u043a\u0430\u0437\u0430\u043d\u0430."
::msgcat::mcset ru "Revision expression is empty." "\u041f\u0443\u0441\u0442\u043e\u0435 \u0432\u044b\u0440\u0430\u0436\u0435\u043d\u0438\u0435 \u0434\u043b\u044f \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438\u044f \u0432\u0435\u0440\u0441\u0438\u0438."
::msgcat::mcset ru "Updated" "\u041e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u043e"
::msgcat::mcset ru "URL" "\u0421\u0441\u044b\u043b\u043a\u0430"
::msgcat::mcset ru "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "\u041e\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u0435\u0442 \u043a\u043e\u043c\u043c\u0438\u0442\u044b \u0434\u043b\u044f \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u0438\u044f.\n\n\u0412\u044b \u0441\u043e\u0437\u0434\u0430\u0435\u0442\u0435 \u043d\u0430\u0447\u0430\u043b\u044c\u043d\u044b\u0439 \u043a\u043e\u043c\u043c\u0438\u0442, \u0437\u0434\u0435\u0441\u044c \u0435\u0449\u0435 \u043d\u0435\u0447\u0435\u0433\u043e \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u044f\u0442\u044c.\n"
::msgcat::mcset ru "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0438\u0441\u043f\u0440\u0430\u0432\u0438\u0442\u044c \u043a\u043e\u043c\u043c\u0438\u0442 \u0432\u043e \u0432\u0440\u0435\u043c\u044f \u0441\u043b\u0438\u044f\u043d\u0438\u044f.\n\n\u0422\u0435\u043a\u0443\u0449\u0435\u0435 \u0441\u043b\u0438\u044f\u043d\u0438\u0435 \u043d\u0435 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u043e. \u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0438\u0441\u043f\u0440\u0430\u0432\u0438\u0442\u044c \u043f\u0440\u0435\u0434\u044b\u0434\u0443\u0438\u0439 \u043a\u043e\u043c\u043c\u0438\u0442, \u043d\u0435 \u043f\u0440\u0435\u0440\u044b\u0432\u0430\u044f \u044d\u0442\u0443 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044e.\n"
::msgcat::mcset ru "Error loading commit data for amend:" "\u041e\u0448\u0438\u0431\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u0433\u0440\u0443\u0437\u043a\u0435 \u0434\u0430\u043d\u043d\u044b\u0445 \u0434\u043b\u044f \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u0438\u044f \u043a\u043e\u043c\u043c\u0438\u0442\u0430:"
::msgcat::mcset ru "Unable to obtain your identity:" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u043f\u043e\u043b\u0443\u0447\u0438\u0442\u044c \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044e \u043e\u0431 \u0430\u0432\u0442\u043e\u0440\u0441\u0442\u0432\u0435:"
::msgcat::mcset ru "Invalid GIT_COMMITTER_IDENT:" "\u041d\u0435\u0434\u043e\u043f\u0443\u0441\u0442\u0438\u043c\u044b\u0439 GIT_COMMITTER_IDENT:"
::msgcat::mcset ru "warning: Tcl does not support encoding '%s'." "\u043f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0435\u043d\u0438\u0435: Tcl \u043d\u0435 \u043f\u043e\u0434\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0435\u0442 \u043a\u043e\u0434\u0438\u0440\u043e\u0432\u043a\u0443 \u00ab%s\u00bb."
::msgcat::mcset ru "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "\u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0435 \u043f\u0440\u043e\u0447\u0438\u0442\u0430\u043d\u043d\u043e\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f \u043d\u0435 \u0441\u043e\u043e\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0443\u0435\u0442 \u0442\u0435\u043a\u0443\u0449\u0435\u043c\u0443.\n\n\u0421 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 \u0431\u044b\u043b \u0438\u0437\u043c\u0435\u043d\u0435\u043d \u0434\u0440\u0443\u0433\u043e\u0439 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u043e\u0439 Git. \u041d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u043e \u043f\u0435\u0440\u0435\u0447\u0438\u0442\u0430\u0442\u044c \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439, \u043f\u0440\u0435\u0436\u0434\u0435 \u0447\u0435\u043c \u0438\u0437\u043c\u0435\u043d\u044f\u0442\u044c \u0442\u0435\u043a\u0443\u0449\u0443\u044e \u0432\u0435\u0442\u0432\u044c. \n\n\u042d\u0442\u043e \u0431\u0443\u0434\u0435\u0442 \u0441\u0434\u0435\u043b\u0430\u043d\u043e \u0441\u0435\u0439\u0447\u0430\u0441 \u0430\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438.\n"
::msgcat::mcset ru "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "\u041d\u0435\u043b\u044c\u0437\u044f \u0432\u044b\u043f\u043e\u043b\u043d\u0438\u0442\u044c \u043a\u043e\u043c\u043c\u0438\u0442 \u0441 \u043d\u0435\u0437\u0430\u0432\u0435\u0440\u0448\u0451\u043d\u043d\u043e\u0439 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0435\u0439 \u0441\u043b\u0438\u044f\u043d\u0438\u044f.\n\n\u0414\u043b\u044f \u0444\u0430\u0439\u043b\u0430 %s \u0432\u043e\u0437\u043d\u0438\u043a \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442 \u0441\u043b\u0438\u044f\u043d\u0438\u044f. \u0420\u0430\u0437\u0440\u0435\u0448\u0438\u0442\u0435 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442 \u0438 \u0434\u043e\u0431\u0430\u0432\u044c\u0442\u0435 \u0438\u0445 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441 \u043f\u0435\u0440\u0435\u0434 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u0435\u043c \u043a\u043e\u043c\u043c\u0438\u0442\u0430.\n"
::msgcat::mcset ru "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "\u041e\u0431\u043d\u0430\u0440\u0443\u0436\u0435\u043d\u043e \u043d\u0435\u0438\u0437\u0432\u0435\u0441\u0442\u043d\u043e\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u0444\u0430\u0439\u043b\u0430 %s.\n\n\u0424\u0430\u0439\u043b %s \u043d\u0435 \u043c\u043e\u0436\u0435\u0442 \u0431\u044b\u0442\u044c \u0437\u0430\u043a\u043e\u043c\u043c\u0438\u0447\u0435\u043d \u044d\u0442\u043e\u0439 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u043e\u0439.\n"
::msgcat::mcset ru "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "\u041e\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044e\u0442 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0434\u043b\u044f \u0441\u043e\u0445\u0440\u0430\u043d\u0435\u043d\u0438\u044f.\n\n\u0414\u043e\u0431\u0430\u0432\u044c\u0442\u0435 \u0432 \u0438\u043d\u0434\u0435\u043a\u0441 \u0445\u043e\u0442\u044f \u0431\u044b \u043e\u0434\u0438\u043d \u0444\u0430\u0439\u043b \u043f\u0435\u0440\u0435\u0434 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u0435\u043c \u043a\u043e\u043c\u043c\u0438\u0442\u0430.\n"
::msgcat::mcset ru "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "\u0423\u043a\u0430\u0436\u0438\u0442\u0435 \u0441\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u0430.\n\n\u0420\u0435\u043a\u043e\u043c\u0435\u043d\u0434\u0443\u0435\u0442\u0441\u044f \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0438\u0439 \u0444\u043e\u0440\u043c\u0430\u0442 \u0441\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u044f:\n\n- \u0432 \u043f\u0435\u0440\u0432\u043e\u0439 \u0441\u0442\u0440\u043e\u043a\u0435 \u043a\u0440\u0430\u0442\u043a\u043e\u0435 \u043e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u0441\u0434\u0435\u043b\u0430\u043d\u043d\u044b\u0445 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439\n- \u0432\u0442\u043e\u0440\u0430\u044f \u0441\u0442\u0440\u043e\u043a\u0430 \u043f\u0443\u0441\u0442\u0430\u044f\n- \u0432 \u043e\u0441\u0442\u0430\u0432\u0448\u0438\u0445\u0441\u044f \u0441\u0442\u0440\u043e\u043a\u0430\u0445 \u043e\u043f\u0438\u0448\u0438\u0442\u0435, \u0447\u0442\u043e \u0434\u0430\u044e\u0442 \u0432\u0430\u0448\u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f\n"
::msgcat::mcset ru "Calling pre-commit hook..." "\u0412\u044b\u0437\u043e\u0432 \u043f\u0435\u0440\u0435\u0445\u0432\u0430\u0442\u0447\u0438\u043a\u0430 pre-commit\u2026"
::msgcat::mcset ru "Commit declined by pre-commit hook." "\u041a\u043e\u043c\u043c\u0438\u0442 \u043f\u0440\u0435\u0440\u0432\u0430\u043d \u043f\u0435\u0440\u0435\u0432\u0430\u0442\u0447\u0438\u043a\u043e\u043c pre-commit."
::msgcat::mcset ru "You are about to commit on a detached head. This is a potentially dangerous thing to do because if you switch to another branch you will lose your changes and it can be difficult to retrieve them later from the reflog. You should probably cancel this commit and create a new branch to continue.\n \n Do you really want to proceed with your Commit?" "\u0412\u044b \u0441\u043e\u0431\u0438\u0440\u0430\u0435\u0442\u0435\u0441\u044c \u0441\u0434\u0435\u043b\u0430\u0442\u044c \u043a\u043e\u043c\u043c\u0438\u0442 \u0432 \u043e\u0442\u0434\u0435\u043b\u0451\u043d\u043d\u044b\u0439 HEAD. \u042d\u0442\u043e \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435 \u043f\u043e\u0442\u0435\u043d\u0446\u0438\u0430\u043b\u044c\u043d\u043e \u043e\u043f\u0430\u0441\u043d\u043e, \u0442\u0430\u043a \u043a\u0430\u043a \u0435\u0441\u043b\u0438 \u0432\u044b \u043f\u0435\u0440\u0435\u043a\u043b\u044e\u0447\u0438\u0442\u0435\u0441\u044c \u043d\u0430 \u0434\u0440\u0443\u0433\u0443\u044e \u0432\u0435\u0442\u043a\u0443 \u043f\u043e\u0441\u043b\u0435 \u044d\u0442\u043e\u0433\u043e, \u0442\u043e \u0432\u044b \u043f\u043e\u0442\u0435\u0440\u044f\u0435\u0442\u0435 \u0441\u0432\u043e\u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0438 \u0438\u0445 \u0441\u043b\u043e\u0436\u043d\u043e \u0431\u0443\u0434\u0435\u0442 \u043f\u043e\u0442\u043e\u043c \u043d\u0430\u0439\u0442\u0438 \u0441 \u043f\u043e\u043c\u043e\u0449\u044c\u044e \u0436\u0443\u0440\u043d\u0430\u043b\u0430 \u0441\u0441\u044b\u043b\u043e\u043a (reflog). \u0412\u0430\u043c \u0441\u043a\u043e\u0440\u0435\u0435 \u0432\u0441\u0435\u0433\u043e \u0441\u043b\u0435\u0434\u0443\u0435\u0442 \u043e\u0442\u043c\u0435\u043d\u0438\u0442\u044c \u044d\u0442\u043e\u0442 \u043a\u043e\u043c\u043c\u0438\u0442 \u0438 \u0441\u043e\u0437\u0434\u0430\u0442\u044c \u043d\u043e\u0432\u0443\u044e \u0432\u0435\u0442\u043a\u0443 \u0434\u043e \u043f\u0440\u043e\u0434\u043e\u043b\u0436\u0435\u043d\u0438\u044f.\n \n \u0412\u044b \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0442\u0435\u043b\u044c\u043d\u043e \u0445\u043e\u0442\u0438\u0442\u0435 \u043f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u044c \u0438 \u0441\u043e\u0437\u0434\u0430\u0442\u044c \u043a\u043e\u043c\u043c\u0438\u0442?"
::msgcat::mcset ru "Calling commit-msg hook..." "\u0412\u044b\u0437\u043e\u0432 \u043f\u0435\u0440\u0435\u0445\u0432\u0430\u0442\u0447\u0438\u043a\u0430 commit-msg\u2026"
::msgcat::mcset ru "Commit declined by commit-msg hook." "\u041a\u043e\u043c\u043c\u0438\u0442 \u043f\u0440\u0435\u0440\u0432\u0430\u043d \u043f\u0435\u0440\u0435\u0432\u0430\u0442\u0447\u0438\u043a\u043e\u043c commit-msg"
::msgcat::mcset ru "Committing changes..." "\u041a\u043e\u043c\u043c\u0438\u0442 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439\u2026"
::msgcat::mcset ru "write-tree failed:" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 write-tree \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u043b\u0430\u0441\u044c \u0441 \u043e\u0448\u0438\u0431\u043a\u043e\u0439:"
::msgcat::mcset ru "Commit failed." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0437\u0430\u043a\u043e\u043c\u043c\u0438\u0442\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f."
::msgcat::mcset ru "Commit %s appears to be corrupt" "\u041a\u043e\u043c\u043c\u0438\u0442 %s \u043f\u043e\u0445\u043e\u0436\u0435 \u043f\u043e\u0432\u0440\u0435\u0436\u0434\u0435\u043d"
::msgcat::mcset ru "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "\u041d\u0435\u0442 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0434\u043b\u044f \u043a\u043e\u043c\u043c\u0438\u0442\u0430.\n\n\u041d\u0438 \u043e\u0434\u0438\u043d \u0444\u0430\u0439\u043b \u043d\u0435 \u0431\u044b\u043b \u0438\u0437\u043c\u0435\u043d\u0435\u043d \u0438 \u043d\u0435 \u0431\u044b\u043b\u043e \u0441\u043b\u0438\u044f\u043d\u0438\u044f.\n\n\u0421\u0435\u0439\u0447\u0430\u0441 \u0430\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438 \u0437\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u0441\u044f \u043f\u0435\u0440\u0435\u0447\u0438\u0442\u044b\u0432\u0430\u043d\u0438\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f.\n"
::msgcat::mcset ru "No changes to commit." "\u041d\u0435\u0442 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0434\u043b\u044f \u043a\u043e\u043c\u043c\u0438\u0442\u0430."
::msgcat::mcset ru "commit-tree failed:" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 commit-tree \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u043b\u0430\u0441\u044c \u0441 \u043e\u0448\u0438\u0431\u043a\u043e\u0439:"
::msgcat::mcset ru "update-ref failed:" "\u041f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0430 update-ref \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u043b\u0430\u0441\u044c \u0441 \u043e\u0448\u0438\u0431\u043a\u043e\u0439:"
::msgcat::mcset ru "Created commit %s: %s" "\u0421\u043e\u0437\u0434\u0430\u043d \u043a\u043e\u043c\u043c\u0438\u0442 %s: %s "
::msgcat::mcset ru "%s (%s): Delete Branch" "%s (%s): \u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Delete Local Branch" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u0443\u044e \u0432\u0435\u0442\u043a\u0443"
::msgcat::mcset ru "Local Branches" "\u041b\u043e\u043a\u0430\u043b\u044c\u043d\u044b\u0435 \u0432\u0435\u0442\u043a\u0438"
::msgcat::mcset ru "Delete Only If Merged Into" "\u0423\u0434\u0430\u043b\u0438\u0442\u044c \u0442\u043e\u043b\u044c\u043a\u043e \u0432 \u0441\u043b\u0443\u0447\u0430\u0435, \u0435\u0441\u043b\u0438 \u0431\u044b\u043b\u043e \u0441\u043b\u0438\u044f\u043d\u0438\u0435 \u0441"
::msgcat::mcset ru "The following branches are not completely merged into %s:" "\u0412\u0435\u0442\u043a\u0438, \u043a\u043e\u0442\u043e\u0440\u044b\u0435 \u043d\u0435 \u043f\u043e\u043b\u043d\u043e\u0441\u0442\u044c\u044e \u0441\u043b\u0438\u0432\u0430\u044e\u0442\u0441\u044f \u0441 %s:"
::msgcat::mcset ru " - %s:" " \u2014 %s:"
::msgcat::mcset ru "Failed to delete branches:\n%s" "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0443\u0434\u0430\u043b\u0438\u0442\u044c \u0432\u0435\u0442\u043a\u0438:\n%s"
::msgcat::mcset ru "Invalid date from Git: %s" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u044c\u043d\u0430\u044f \u0434\u0430\u0442\u0430 \u0432 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0438: %s"
::msgcat::mcset ru "Number of loose objects" "\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u043d\u0435\u0441\u0432\u044f\u0437\u0430\u043d\u043d\u044b\u0445 \u043e\u0431\u044a\u0435\u043a\u0442\u043e\u0432"
::msgcat::mcset ru "Disk space used by loose objects" "\u041e\u0431\u044a\u0435\u043c \u0434\u0438\u0441\u043a\u043e\u0432\u043e\u0433\u043e \u043f\u0440\u043e\u0441\u0442\u0440\u0430\u043d\u0441\u0442\u0432\u0430, \u0437\u0430\u043d\u044f\u0442\u044b\u0439 \u043d\u0435\u0441\u0432\u044f\u0437\u0430\u043d\u043d\u044b\u043c\u0438 \u043e\u0431\u044a\u0435\u043a\u0442\u0430\u043c\u0438"
::msgcat::mcset ru "Number of packed objects" "\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u0443\u043f\u0430\u043a\u043e\u0432\u0430\u043d\u043d\u044b\u0445 \u043e\u0431\u044a\u0435\u043a\u0442\u043e\u0432"
::msgcat::mcset ru "Number of packs" "\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e pack-\u0444\u0430\u0439\u043b\u043e\u0432"
::msgcat::mcset ru "Disk space used by packed objects" "\u041e\u0431\u044a\u0435\u043c \u0434\u0438\u0441\u043a\u043e\u0432\u043e\u0433\u043e \u043f\u0440\u043e\u0441\u0442\u0440\u0430\u043d\u0441\u0442\u0432\u0430, \u0437\u0430\u043d\u044f\u0442\u044b\u0439 \u0443\u043f\u0430\u043a\u043e\u0432\u0430\u043d\u043d\u044b\u043c\u0438 \u043e\u0431\u044a\u0435\u043a\u0442\u0430\u043c\u0438"
::msgcat::mcset ru "Packed objects waiting for pruning" "\u041d\u0435\u0441\u0432\u044f\u0437\u0430\u043d\u043d\u044b\u0435 \u043e\u0431\u044a\u0435\u043a\u0442\u044b, \u043a\u043e\u0442\u043e\u0440\u044b\u0435 \u043c\u043e\u0436\u043d\u043e \u0443\u0434\u0430\u043b\u0438\u0442\u044c"
::msgcat::mcset ru "Garbage files" "\u041c\u0443\u0441\u043e\u0440"
::msgcat::mcset ru "%s (%s): Database Statistics" "%s (%s): \u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043a\u0430 \u0431\u0430\u0437\u044b \u0434\u0430\u043d\u043d\u044b\u0445"
::msgcat::mcset ru "Compressing the object database" "\u0421\u0436\u0430\u0442\u0438\u0435 \u0431\u0430\u0437\u044b \u043e\u0431\u044a\u0435\u043a\u0442\u043e\u0432"
::msgcat::mcset ru "Verifying the object database with fsck-objects" "\u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u0431\u0430\u0437\u044b \u043e\u0431\u044a\u0435\u043a\u0442\u043e\u0432 \u043f\u0440\u0438 \u043f\u043e\u043c\u043e\u0449\u0438 fsck"
::msgcat::mcset ru "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "\u042d\u0442\u043e\u0442 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 \u0441\u0435\u0439\u0447\u0430\u0441 \u0441\u043e\u0434\u0435\u0440\u0436\u0438\u0442 \u043f\u0440\u0438\u043c\u0435\u0440\u043d\u043e %i \u0441\u0432\u043e\u0431\u043e\u0434\u043d\u044b\u0445 \u043e\u0431\u044a\u0435\u043a\u0442\u043e\u0432\n\n\u0414\u043b\u044f \u043b\u0443\u0447\u0448\u0435\u0439 \u043f\u0440\u043e\u0438\u0437\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u044c\u043d\u043e\u0441\u0442\u0438 \u0440\u0435\u043a\u043e\u043c\u0435\u043d\u0434\u0443\u0435\u0442\u0441\u044f \u0441\u0436\u0430\u0442\u044c \u0431\u0430\u0437\u0443 \u0434\u0430\u043d\u043d\u044b\u0445.\n\n\u0421\u0436\u0430\u0442\u044c \u0431\u0430\u0437\u0443 \u0434\u0430\u043d\u043d\u044b\u0445 \u0441\u0435\u0439\u0447\u0430\u0441?"
::msgcat::mcset ru "%s: error" "%s: \u043e\u0448\u0438\u0431\u043a\u0430"
::msgcat::mcset ru "%s: warning" "%s: \u043f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0435\u043d\u0438\u0435"
::msgcat::mcset ru "%s hook failed:" "\u043e\u0448\u0438\u0431\u043a\u0430 \u043f\u0435\u0440\u0435\u0445\u0432\u0430\u0442\u0447\u0438\u043a\u0430 %s:"
::msgcat::mcset ru "You must correct the above errors before committing." "\u041f\u0435\u0440\u0435\u0434 \u043a\u043e\u043c\u043c\u0438\u0442\u043e\u043c, \u0438\u0441\u043f\u0440\u0430\u0432\u044c\u0442\u0435 \u0432\u044b\u0448\u0435\u0443\u043a\u0430\u0437\u0430\u043d\u043d\u044b\u0435 \u043e\u0448\u0438\u0431\u043a\u0438."
::msgcat::mcset ru "%s (%s): error" "%s (%s): \u043e\u0448\u0438\u0431\u043a\u0430"
::msgcat::mcset ru "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0432\u044b\u043f\u043e\u043b\u043d\u0438\u0442\u044c \u0441\u043b\u0438\u044f\u043d\u0438\u0435 \u0432\u043e \u0432\u0440\u0435\u043c\u044f \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u0438\u044f.\n\n\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0435 \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u0434\u0430\u043d\u043d\u043e\u0433\u043e \u043a\u043e\u043c\u043c\u0438\u0442\u0430 \u043f\u0435\u0440\u0435\u0434 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u0435\u043c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u044f.\n"
::msgcat::mcset ru "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "\u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0435 \u043f\u0440\u043e\u0447\u0438\u0442\u0430\u043d\u043d\u043e\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u044f \u043d\u0435 \u0441\u043e\u043e\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0443\u0435\u0442 \u0442\u0435\u043a\u0443\u0449\u0435\u043c\u0443.\n\n\u0421 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439 \u0431\u044b\u043b \u0438\u0437\u043c\u0435\u043d\u0435\u043d \u0434\u0440\u0443\u0433\u043e\u0439 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u043e\u0439 Git. \u041d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u043e \u043f\u0435\u0440\u0435\u0447\u0438\u0442\u0430\u0442\u044c \u0440\u0435\u043f\u043e\u0437\u0438\u0442\u043e\u0440\u0438\u0439, \u043f\u0440\u0435\u0436\u0434\u0435 \u0447\u0435\u043c \u0441\u043b\u0438\u044f\u043d\u0438\u0435 \u043c\u043e\u0436\u0435\u0442 \u0431\u044b\u0442\u044c \u0441\u0434\u0435\u043b\u0430\u043d\u043e.\n\n\u042d\u0442\u043e \u0431\u0443\u0434\u0435\u0442 \u0441\u0434\u0435\u043b\u0430\u043d\u043e \u0441\u0435\u0439\u0447\u0430\u0441 \u0430\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438.\n"
::msgcat::mcset ru "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "\u041f\u0440\u0435\u0434\u044b\u0434\u0443\u0449\u0435\u0435 \u0441\u043b\u0438\u044f\u043d\u0438\u0435 \u043d\u0435 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u043e \u0438\u0437-\u0437\u0430 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0430.\n\n\u0414\u043b\u044f \u0444\u0430\u0439\u043b\u0430 %s \u0432\u043e\u0437\u043d\u0438\u043a \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442 \u0441\u043b\u0438\u044f\u043d\u0438\u044f.\n\n\u0420\u0430\u0437\u0440\u0435\u0448\u0438\u0442\u0435 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442, \u0434\u043e\u0431\u0430\u0432\u044c\u0442\u0435 \u0444\u0430\u0439\u043b \u0432 \u0438\u043d\u0434\u0435\u043a\u0441 \u0438 \u0437\u0430\u043a\u043e\u043c\u043c\u0438\u0442\u044c\u0442\u0435. \u0422\u043e\u043b\u044c\u043a\u043e \u043f\u043e\u0441\u043b\u0435 \u044d\u0442\u043e\u0433\u043e \u043c\u043e\u0436\u043d\u043e \u043d\u0430\u0447\u0430\u0442\u044c \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0435\u0435 \u0441\u043b\u0438\u044f\u043d\u0438\u0435.\n"
::msgcat::mcset ru "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "\u0412\u044b \u043d\u0430\u0445\u043e\u0434\u0438\u0442\u0435\u0441\u044c \u0432 \u043f\u0440\u043e\u0446\u0435\u0441\u0441\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439.\n\n\u0424\u0430\u0439\u043b %s \u0438\u0437\u043c\u0435\u043d\u0451\u043d.\n\n\u0412\u044b \u0434\u043e\u043b\u0436\u043d\u044b \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044c \u0442\u0435\u043a\u0443\u0449\u0438\u0439 \u043a\u043e\u043c\u043c\u0438\u0442 \u043f\u0435\u0440\u0435\u0434 \u043d\u0430\u0447\u0430\u043b\u043e\u043c \u0441\u043b\u0438\u044f\u043d\u0438\u044f. \u0412 \u0441\u043b\u0443\u0447\u0430\u0435 \u043d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u043e\u0441\u0442\u0438, \u044d\u0442\u043e \u043f\u043e\u0437\u0432\u043e\u043b\u0438\u0442 \u043f\u0440\u0435\u0440\u0432\u0430\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044e \u0441\u043b\u0438\u044f\u043d\u0438\u044f.\n"
::msgcat::mcset ru "%s of %s" "%s \u0438\u0437 %s"
::msgcat::mcset ru "Merging %s and %s..." "\u0421\u043b\u0438\u044f\u043d\u0438\u0435 %s \u0438 %s\u2026"
::msgcat::mcset ru "Merge completed successfully." "\u0421\u043b\u0438\u044f\u043d\u0438\u0435 \u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u043e."
::msgcat::mcset ru "Merge failed.  Conflict resolution is required." "\u041d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044c \u0441\u043b\u0438\u044f\u043d\u0438\u0435. \u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044f \u0440\u0430\u0437\u0440\u0435\u0448\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0430."
::msgcat::mcset ru "%s (%s): Merge" "%s (%s): \u0421\u043b\u0438\u044f\u043d\u0438\u0435"
::msgcat::mcset ru "Merge Into %s" "\u0421\u043b\u0438\u044f\u043d\u0438\u0435 \u0441 %s"
::msgcat::mcset ru "Revision To Merge" "\u0412\u0435\u0440\u0441\u0438\u044f, \u0441 \u043a\u043e\u0442\u043e\u0440\u043e\u0439 \u043f\u0440\u043e\u0432\u0435\u0441\u0442\u0438 \u0441\u043b\u0438\u044f\u043d\u0438\u0435"
::msgcat::mcset ru "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u043f\u0440\u0435\u0440\u0432\u0430\u0442\u044c \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u0438\u0435.\n\n\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0435 \u0442\u0435\u043a\u0443\u0449\u0435\u0435 \u0438\u0441\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u043a\u043e\u043c\u043c\u0438\u0442\u0430.\n"
::msgcat::mcset ru "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "\u041f\u0440\u0435\u0440\u0432\u0430\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u044e \u0441\u043b\u0438\u044f\u043d\u0438\u044f?\n\n\u041f\u0440\u0435\u0440\u044b\u0432\u0430\u043d\u0438\u0435 \u0442\u0435\u043a\u0443\u0449\u0435\u0433\u043e \u0441\u043b\u0438\u044f\u043d\u0438\u044f \u043f\u0440\u0438\u0432\u0435\u0434\u0435\u0442 \u043a \u043f\u043e\u0442\u0435\u0440\u0435 *\u0412\u0421\u0415\u0425* \u043d\u0435\u0441\u043e\u0445\u0440\u0430\u043d\u0435\u043d\u043d\u044b\u0445 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439.\n\n\u041f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u044c?"
::msgcat::mcset ru "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "\u0421\u0431\u0440\u043e\u0441\u0438\u0442\u044c \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f?\n\n\u0421\u0431\u0440\u043e\u0441 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043f\u0440\u0438\u0432\u0435\u0434\u0435\u0442 \u043a \u043f\u043e\u0442\u0435\u0440\u0435 *\u0412\u0421\u0415\u0425* \u043d\u0435\u0441\u043e\u0445\u0440\u0430\u043d\u0435\u043d\u043d\u044b\u0445 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439.\n\n\u041f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u044c?"
::msgcat::mcset ru "Aborting" "\u041f\u0440\u0435\u0440\u044b\u0432\u0430\u044e"
::msgcat::mcset ru "files reset" "\u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432 \u0444\u0430\u0439\u043b\u0430\u0445 \u043e\u0442\u043c\u0435\u043d\u0435\u043d\u044b"
::msgcat::mcset ru "Abort failed." "\u041f\u0440\u0435\u0440\u0432\u0430\u0442\u044c \u043d\u0435 \u0443\u0434\u0430\u043b\u043e\u0441\u044c."
::msgcat::mcset ru "Abort completed.  Ready." "\u041f\u0440\u0435\u0440\u0432\u0430\u043d\u043e."
