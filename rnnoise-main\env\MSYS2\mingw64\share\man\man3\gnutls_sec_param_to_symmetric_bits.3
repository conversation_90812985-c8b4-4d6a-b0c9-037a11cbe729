.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_sec_param_to_symmetric_bits" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_sec_param_to_symmetric_bits \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned int gnutls_sec_param_to_symmetric_bits(gnutls_sec_param_t " param ");"
.SH ARGUMENTS
.IP "gnutls_sec_param_t param" 12
is a security parameter
.SH "DESCRIPTION"
This function will return the number of bits that correspond to
symmetric cipher strength for the given security parameter.
.SH "RETURNS"
The number of bits, or (0).
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
