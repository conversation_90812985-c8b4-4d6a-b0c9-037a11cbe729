.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hmac_get_key_size" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hmac_get_key_size \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "unsigned gnutls_hmac_get_key_size(gnutls_mac_algorithm_t " algorithm ");"
.SH ARGUMENTS
.IP "gnutls_mac_algorithm_t algorithm" 12
the mac algorithm to use
.SH "DESCRIPTION"
This function will return the size of the key to be used with this
algorithm. On the algorithms which may accept arbitrary key sizes,
the returned size is the MAC key size used in the TLS protocol.
.SH "RETURNS"
The key size or zero on error.
.SH "SINCE"
3.6.12
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
