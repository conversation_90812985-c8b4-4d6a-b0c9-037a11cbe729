.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dh_params_generate2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dh_params_generate2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_dh_params_generate2(gnutls_dh_params_t " dparams ", unsigned int " bits ");"
.SH ARGUMENTS
.IP "gnutls_dh_params_t dparams" 12
The parameters
.IP "unsigned int bits" 12
is the prime's number of bits
.SH "DESCRIPTION"
This function will generate a new pair of prime and generator for use in
the <PERSON><PERSON><PERSON>\-<PERSON><PERSON> key exchange. This may take long time.

It is recommended not to set the number of bits directly, but 
use \fBgnutls_sec_param_to_pk_bits()\fP instead.
Also note that the DH parameters are only useful to servers.
Since clients use the parameters sent by the server, it's of
no use to call this in client side.

The parameters generated are of the DSA form. It also is possible
to generate provable parameters (following the Shawe\-Taylor
algorithm), using \fBgnutls_x509_privkey_generate2()\fP with DSA option
and the \fBGNUTLS_PRIVKEY_FLAG_PROVABLE\fP flag set. These can the
be imported with \fBgnutls_dh_params_import_dsa()\fP.

It is no longer recommended for applications to generate parameters.
See the "Parameter generation" section in the manual.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
