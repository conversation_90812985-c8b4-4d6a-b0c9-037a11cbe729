VS_DOTNET_REFERENCE_<refname>
-----------------------------

.. versionadded:: 3.8

Visual Studio managed project .NET reference with name ``<refname>``
and hint path.

Adds one .NET reference to generated Visual Studio project. The
reference will have the name ``<refname>`` and will point to the
assembly given as value of the property.

See also :prop_tgt:`VS_DOTNET_REFERENCES` and
:prop_tgt:`VS_DOTNET_REFERENCES_COPY_LOCAL`
