.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hex_encode" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hex_encode \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_hex_encode(const gnutls_datum_t * " data ", char * " result ", size_t * " result_size ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * data" 12
contain the raw data
.IP "char * result" 12
the place where hex data will be copied
.IP "size_t * result_size" 12
holds the size of the result
.SH "DESCRIPTION"
This function will convert the given data to printable data, using
the hex encoding, as used in the PSK password files.

Note that the size of the result includes the null terminator.
.SH "RETURNS"
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if the buffer given is not
long enough, or 0 on success.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
