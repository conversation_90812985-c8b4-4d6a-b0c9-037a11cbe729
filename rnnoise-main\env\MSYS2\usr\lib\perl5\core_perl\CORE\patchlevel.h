/*    patchlevel.h
 *
 *    Copyright (C) 1993, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
 *    2003, 2004, 2005, 2006, 2007, 2008, 2009, by <PERSON> and others
 *
 *    You may distribute under the terms of either the GNU General Public
 *    License or the Artistic License, as specified in the README file.
 *
 */

/*
=for apidoc AmDnU|U8|PERL_REVISION
The major number component of the perl interpreter currently being compiled or
executing.  This has been C<5> from 1993 into 2020.

Instead use one of the version comparison macros.  See C<L</PERL_VERSION_EQ>>.

=for apidoc AmDnU|U8|PERL_VERSION
The minor number component of the perl interpreter currently being compiled or
executing.  Between 1993 into 2020, this has ranged from 0 to 33.

Instead use one of the version comparison macros.  See C<L</PERL_VERSION_EQ>>.

=for apidoc AmDnU|U8|PERL_SUBVERSION
The micro number component of the perl interpreter currently being compiled or
executing.  In stable releases this gives the dot release number for
maintenance updates.  In development releases this gives a tag for a snapshot
of the status at various points in the development cycle.

Instead use one of the version comparison macros.  See C<L</PERL_VERSION_EQ>>.

=cut
*/

#ifndef __PATCHLEVEL_H_INCLUDED__

/* do not adjust the whitespace! Configure expects the numbers to be
 * exactly on the third column */

#define PERL_REVISION	5		/* age */
#define PERL_VERSION	38		/* epoch */
#define PERL_SUBVERSION	4		/* generation */

/* The following numbers describe the earliest compatible version of
   Perl ("compatibility" here being defined as sufficient binary/API
   compatibility to run XS code built with the older version).
   Normally this should not change across maintenance releases.

   Note that this only refers to an out-of-the-box build.  Many non-default
   options such as usemultiplicity tend to break binary compatibility
   more often.

   This is used by Configure et al to figure out
   PERL_INC_VERSION_LIST, which lists version libraries
   to include in @INC.  See INSTALL for how this works.

   Porting/bump-perl-version will automatically set these to the version of perl
   to be released for blead releases, and to 5.X.0 for maint releases. Manually
   changing them should not be necessary.
*/
#define PERL_API_REVISION	5
#define PERL_API_VERSION	38
#define PERL_API_SUBVERSION	0
/*
   XXX Note:  The selection of non-default Configure options, such
   as -Duselonglong may invalidate these settings.  Currently, Configure
   does not adequately test for this.   A.D.  Jan 13, 2000
*/

#define __PATCHLEVEL_H_INCLUDED__
#endif

/*
        local_patches -- list of locally applied less-than-subversion patches.
        If you're distributing such a patch, please give it a name and a
        one-line description, placed just before the last NULL in the array
        below.  If your patch fixes a bug in the perlbug database, please
        mention the bugid.  If your patch *IS* dependent on a prior patch,
        please place your applied patch line after its dependencies. This
        will help tracking of patch dependencies.

        Please either use 'diff --unified=0' if your diff supports
        that or edit the hunk of the diff output which adds your patch
        to this list, to remove context lines which would give patch
        problems. For instance, if the original context diff is

           *** patchlevel.h.orig	<date here>
           --- patchlevel.h	<date here>
           *** 38,43 ***
           --- 38,44 ---
                ,"FOO1235 - some patch"
                ,"BAR3141 - another patch"
                ,"BAZ2718 - and another patch"
           + 	,"MINE001 - my new patch"
                ,NULL
             };

        please change it to
           *** patchlevel.h.orig	<date here>
           --- patchlevel.h	<date here>
           *** 41,43 ***
           --- 41,44 ---
           + 	,"MINE001 - my new patch"
                ,NULL
             };

        (Note changes to line numbers as well as removal of context lines.)
        This will prevent patch from choking if someone has previously
        applied different patches than you.

        History has shown that nobody distributes patches that also
        modify patchlevel.h. Do it yourself. The following perl
        program can be used to add a comment to patchlevel.h:

#!perl
die "Usage: perl -x patchlevel.h comment ..." unless @ARGV;
open PLIN, "<", "patchlevel.h" or die "Couldn't open patchlevel.h : $!";
open PLOUT, ">", "patchlevel.new" or die "Couldn't write on patchlevel.new : $!";
my $seen=0;
while (<PLIN>) {
    if (/^(\s+),NULL/ and $seen) {
       my $pre = $1;
       while (my $c = shift @ARGV){
            $c =~ s|\\|\\\\|g;
            $c =~ s|"|\\"|g;
            print PLOUT qq{$pre,"$c"\n};
       }
    }
    $seen++ if /local_patches\[\]/;
    print PLOUT;
}
close PLOUT or die "Couldn't close filehandle writing to patchlevel.new : $!";
close PLIN or die "Couldn't close filehandle reading from patchlevel.h : $!";
close DATA; # needed to allow unlink to work win32.
unlink "patchlevel.bak" or warn "Couldn't unlink patchlevel.bak : $!"
  if -e "patchlevel.bak";
rename "patchlevel.h", "patchlevel.bak" or
  die "Couldn't rename patchlevel.h to patchlevel.bak : $!";
rename "patchlevel.new", "patchlevel.h" or
  die "Couldn't rename patchlevel.new to patchlevel.h : $!";
__END__

Please keep empty lines below so that context diffs of this file do
not ever collect the lines belonging to local_patches() into the same
hunk.

 */

#if !defined(PERL_PATCHLEVEL_H_IMPLICIT) && !defined(LOCAL_PATCH_COUNT)
#  if defined(PERL_IS_MINIPERL)
#    define PERL_PATCHNUM "UNKNOWN-miniperl"
#    define PERL_GIT_UNPUSHED_COMMITS /*leave-this-comment*/
#  elif defined(PERL_MICRO)
#    define PERL_PATCHNUM "UNKNOWN-microperl"
#    define PERL_GIT_UNPUSHED_COMMITS /*leave-this-comment*/
#  else
#    include "git_version.h"
#  endif
static const char * const local_patches[] = {
        NULL
#ifdef PERL_GIT_UNCOMMITTED_CHANGES
        ,"uncommitted-changes"
#endif
        PERL_GIT_UNPUSHED_COMMITS    	/* do not remove this line */
        ,NULL
};



/* Initial space prevents this variable from being inserted in config.sh  */
#  define LOCAL_PATCH_COUNT     \
        ((int)(C_ARRAY_LENGTH(local_patches)-2))

/* the old terms of reference, add them only when explicitly included */
#define PATCHLEVEL		PERL_VERSION
#undef  SUBVERSION		/* OS/390 has a SUBVERSION in a system header */
#define SUBVERSION		PERL_SUBVERSION
#endif
