.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_privkey_generate" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_privkey_generate \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_privkey_generate(const char * " url ", gnutls_pk_algorithm_t " pk ", unsigned int " bits ", const char * " label ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * url" 12
a token URL
.IP "gnutls_pk_algorithm_t pk" 12
the public key algorithm
.IP "unsigned int bits" 12
the security bits
.IP "const char * label" 12
a label
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will generate a private key in the specified
by the  \fIurl\fP token. The private key will be generate within
the token and will not be exportable.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
