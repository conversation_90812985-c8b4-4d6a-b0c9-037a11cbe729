@echo off
echo Building Enhanced RNNoise Executables...
echo ========================================

REM 设置环境
set MSYS2_PATH=%CD%\env\MSYS2
set PATH=%MSYS2_PATH%\mingw64\bin;%PATH%

REM 创建bin目录
if not exist bin mkdir bin

echo.
echo Compiling model verification program...
gcc -o bin\model_verify.exe src\model_verify.c src\enhanced_rnn_weights.c -Iinclude -lm -static-libgcc
if %ERRORLEVEL% EQU 0 (
    echo ✓ model_verify.exe compiled successfully
) else (
    echo ✗ Failed to compile model_verify.exe
)

echo.
echo Compiling simple model test...
gcc -o bin\simple_test.exe src\simple_model_test.c -Iinclude -lm -static-libgcc
if %ERRORLEVEL% EQU 0 (
    echo ✓ simple_test.exe compiled successfully
) else (
    echo ✗ Failed to compile simple_test.exe
)

echo.
echo Compiling enhanced inference test...
gcc -o bin\enhanced_inference.exe src\enhanced_inference_test.c src\enhanced_rnn_weights.c -Iinclude -lm -static-libgcc
if %ERRORLEVEL% EQU 0 (
    echo ✓ enhanced_inference.exe compiled successfully
) else (
    echo ✗ Failed to compile enhanced_inference.exe
)

echo.
echo Listing compiled executables:
dir bin\*.exe

echo.
echo Build completed!
echo Executables are available in the bin\ directory.
pause
