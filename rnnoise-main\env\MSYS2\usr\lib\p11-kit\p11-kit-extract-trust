#!/bin/sh

#set -vx

# At this time, while this script is trivial, we ignore any parameters given.
# However, for backwards compatibility reasons, future versions of this script must 
# support the syntax "update-ca-trust extract" trigger the generation of output 
# files in $DEST.

DEST=/etc/pki/ca-trust/extracted

# OpenSSL PEM bundle that includes trust flags
# (BEGIN TRUSTED CERTIFICATE)
/usr/bin/p11-kit extract --format=openssl-bundle --filter=certificates --overwrite --comment $DEST/openssl/ca-bundle.trust.crt
/usr/bin/p11-kit extract --format=pem-bundle --filter=ca-anchors --overwrite --comment --purpose server-auth $DEST/pem/tls-ca-bundle.pem
/usr/bin/p11-kit extract --format=pem-bundle --filter=ca-anchors --overwrite --comment --purpose email $DEST/pem/email-ca-bundle.pem
/usr/bin/p11-kit extract --format=pem-bundle --filter=ca-anchors --overwrite --comment --purpose code-signing $DEST/pem/objsign-ca-bundle.pem
/usr/bin/p11-kit extract --format=java-cacerts --filter=ca-anchors --overwrite --purpose server-auth $DEST/java/cacerts

# The usual symbolic links are not present to keep these file in sync in MSYS2, so copying is necessary
mkdir -p /usr/ssl/certs
cp -f $DEST/pem/tls-ca-bundle.pem /usr/ssl/certs/ca-bundle.crt
cp -f $DEST/pem/tls-ca-bundle.pem /usr/ssl/cert.pem
cp -f $DEST/openssl/ca-bundle.trust.crt /usr/ssl/certs/ca-bundle.trust.crt
