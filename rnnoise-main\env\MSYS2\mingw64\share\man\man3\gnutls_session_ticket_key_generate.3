.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_ticket_key_generate" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_ticket_key_generate \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_ticket_key_generate(gnutls_datum_t * " key ");"
.SH ARGUMENTS
.IP "gnutls_datum_t * key" 12
is a pointer to a \fBgnutls_datum_t\fP which will contain a newly
created key.
.SH "DESCRIPTION"
Generate a random key to encrypt security parameters within
SessionTicket.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, or an
error code.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
