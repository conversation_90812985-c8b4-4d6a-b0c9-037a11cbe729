CMAKE_ANDROID_ARCH_ABI
----------------------

.. versionadded:: 3.7

When :ref:`Cross Compiling for Android`, this variable specifies the
target architecture and ABI to be used.  Valid values are:

* ``arm64-v8a``
* ``armeabi-v7a``
* ``armeabi-v6``
* ``armeabi``
* ``mips``
* ``mips64``
* ``x86``
* ``x86_64``

See also the :variable:`CMAKE_ANDROID_ARM_MODE` and
:variable:`CMAKE_ANDROID_ARM_NEON` variables.
