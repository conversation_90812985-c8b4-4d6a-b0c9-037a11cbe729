<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/index_hash.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('index__hash_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">index_hash.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Validate Index by using a hash function.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a2db9f438838c8ff72a8a6fd3fc856f8c" id="r_a2db9f438838c8ff72a8a6fd3fc856f8c"><td class="memItemLeft" align="right" valign="top"><a id="a2db9f438838c8ff72a8a6fd3fc856f8c" name="a2db9f438838c8ff72a8a6fd3fc856f8c"></a>
typedef struct lzma_index_hash_s&#160;</td><td class="memItemRight" valign="bottom"><b>lzma_index_hash</b></td></tr>
<tr class="memdesc:a2db9f438838c8ff72a8a6fd3fc856f8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Opaque data type to hold the Index hash. <br /></td></tr>
<tr class="separator:a2db9f438838c8ff72a8a6fd3fc856f8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:aaafae4967a4a266d97dc34a98bfcabfb" id="r_aaafae4967a4a266d97dc34a98bfcabfb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaafae4967a4a266d97dc34a98bfcabfb">lzma_index_hash_init</a> (<a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *index_hash, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:aaafae4967a4a266d97dc34a98bfcabfb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Allocate and initialize a new <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure.  <br /></td></tr>
<tr class="separator:aaafae4967a4a266d97dc34a98bfcabfb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7dacb41b9ec1c8df5d33dfdae97743b3" id="r_a7dacb41b9ec1c8df5d33dfdae97743b3"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7dacb41b9ec1c8df5d33dfdae97743b3">lzma_index_hash_end</a> (<a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *index_hash, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow</td></tr>
<tr class="memdesc:a7dacb41b9ec1c8df5d33dfdae97743b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Deallocate <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure.  <br /></td></tr>
<tr class="separator:a7dacb41b9ec1c8df5d33dfdae97743b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2bdbe4f0b5fa2fadb7528447feaaa97f" id="r_a2bdbe4f0b5fa2fadb7528447feaaa97f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2bdbe4f0b5fa2fadb7528447feaaa97f">lzma_index_hash_append</a> (<a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *index_hash, <a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> unpadded_size, <a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> uncompressed_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a2bdbe4f0b5fa2fadb7528447feaaa97f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a new Record to an Index hash.  <br /></td></tr>
<tr class="separator:a2bdbe4f0b5fa2fadb7528447feaaa97f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a891eb955284c9117155f92eb0ddba44c" id="r_a891eb955284c9117155f92eb0ddba44c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a891eb955284c9117155f92eb0ddba44c">lzma_index_hash_decode</a> (<a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *index_hash, const uint8_t *in, size_t *in_pos, size_t in_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a891eb955284c9117155f92eb0ddba44c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode and validate the Index field.  <br /></td></tr>
<tr class="separator:a891eb955284c9117155f92eb0ddba44c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0f8ab3b57b117f9547866156755c917f" id="r_a0f8ab3b57b117f9547866156755c917f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0f8ab3b57b117f9547866156755c917f">lzma_index_hash_size</a> (const <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *index_hash) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a0f8ab3b57b117f9547866156755c917f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the size of the Index field as bytes.  <br /></td></tr>
<tr class="separator:a0f8ab3b57b117f9547866156755c917f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Validate Index by using a hash function. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead.</dd></dl>
<p>Hashing makes it possible to use constant amount of memory to validate Index of arbitrary size. </p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="aaafae4967a4a266d97dc34a98bfcabfb" name="aaafae4967a4a266d97dc34a98bfcabfb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaafae4967a4a266d97dc34a98bfcabfb">&#9670;&#160;</a></span>lzma_index_hash_init()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> * lzma_index_hash_init </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *</td>          <td class="paramname"><span class="paramname"><em>index_hash</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Allocate and initialize a new <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure. </p>
<p>If index_hash is NULL, this function allocates and initializes a new <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure and returns a pointer to it. If allocation fails, NULL is returned.</p>
<p>If index_hash is non-NULL, this function reinitializes the <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure and returns the same pointer. In this case, return value cannot be NULL or a different pointer than the index_hash that was given as an argument.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index_hash</td><td>Pointer to a <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure or NULL. </td></tr>
    <tr><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free().</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Initialized <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure on success or NULL on failure. </dd></dl>

</div>
</div>
<a id="a7dacb41b9ec1c8df5d33dfdae97743b3" name="a7dacb41b9ec1c8df5d33dfdae97743b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7dacb41b9ec1c8df5d33dfdae97743b3">&#9670;&#160;</a></span>lzma_index_hash_end()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void lzma_index_hash_end </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *</td>          <td class="paramname"><span class="paramname"><em>index_hash</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Deallocate <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index_hash</td><td>Pointer to a <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure to free. </td></tr>
    <tr><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a2bdbe4f0b5fa2fadb7528447feaaa97f" name="a2bdbe4f0b5fa2fadb7528447feaaa97f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2bdbe4f0b5fa2fadb7528447feaaa97f">&#9670;&#160;</a></span>lzma_index_hash_append()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_hash_append </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *</td>          <td class="paramname"><span class="paramname"><em>index_hash</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td>          <td class="paramname"><span class="paramname"><em>unpadded_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td>          <td class="paramname"><span class="paramname"><em>uncompressed_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Add a new Record to an Index hash. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index_hash</td><td>Pointer to a <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure </td></tr>
    <tr><td class="paramname">unpadded_size</td><td>Unpadded Size of a Block </td></tr>
    <tr><td class="paramname">uncompressed_size</td><td>Uncompressed Size of a Block</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_DATA_ERROR: Compressed or uncompressed size of the Stream or size of the Index field would grow too big.</li>
<li>LZMA_PROG_ERROR: Invalid arguments or this function is being used when <a class="el" href="#a891eb955284c9117155f92eb0ddba44c" title="Decode and validate the Index field.">lzma_index_hash_decode()</a> has already been used. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a891eb955284c9117155f92eb0ddba44c" name="a891eb955284c9117155f92eb0ddba44c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a891eb955284c9117155f92eb0ddba44c">&#9670;&#160;</a></span>lzma_index_hash_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_hash_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *</td>          <td class="paramname"><span class="paramname"><em>index_hash</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>in_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Decode and validate the Index field. </p>
<p>After telling the sizes of all Blocks with <a class="el" href="#a2bdbe4f0b5fa2fadb7528447feaaa97f" title="Add a new Record to an Index hash.">lzma_index_hash_append()</a>, the actual Index field is decoded with this function. Specifically, once decoding of the Index field has been started, no more Records can be added using <a class="el" href="#a2bdbe4f0b5fa2fadb7528447feaaa97f" title="Add a new Record to an Index hash.">lzma_index_hash_append()</a>.</p>
<p>This function doesn't use <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> structure to pass the input data. Instead, the input buffer is specified using three arguments. This is because it matches better the internal APIs of liblzma.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">index_hash</td><td>Pointer to a <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Pointer to the beginning of the input buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">in_pos</td><td>in[*in_pos] is the next byte to process </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>in[in_size] is the first byte not to process</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: So far good, but more input is needed.</li>
<li>LZMA_STREAM_END: Index decoded successfully and it matches the Records given with <a class="el" href="#a2bdbe4f0b5fa2fadb7528447feaaa97f" title="Add a new Record to an Index hash.">lzma_index_hash_append()</a>.</li>
<li>LZMA_DATA_ERROR: Index is corrupt or doesn't match the information given with <a class="el" href="#a2bdbe4f0b5fa2fadb7528447feaaa97f" title="Add a new Record to an Index hash.">lzma_index_hash_append()</a>.</li>
<li>LZMA_BUF_ERROR: Cannot progress because *in_pos &gt;= in_size.</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a0f8ab3b57b117f9547866156755c917f" name="a0f8ab3b57b117f9547866156755c917f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0f8ab3b57b117f9547866156755c917f">&#9670;&#160;</a></span>lzma_index_hash_size()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_hash_size </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c">lzma_index_hash</a> *</td>          <td class="paramname"><span class="paramname"><em>index_hash</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the size of the Index field as bytes. </p>
<p>This is needed to verify the Backward Size field in the Stream Footer.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index_hash</td><td>Pointer to a <a class="el" href="#a2db9f438838c8ff72a8a6fd3fc856f8c" title="Opaque data type to hold the Index hash.">lzma_index_hash</a> structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Size of the Index field in bytes. </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li><li class="navelem"><a class="el" href="index__hash_8h.html">index_hash.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
