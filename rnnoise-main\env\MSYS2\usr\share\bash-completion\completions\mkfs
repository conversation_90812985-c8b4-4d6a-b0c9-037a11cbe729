_mkfs_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-t'|'--type')
			FSTYPES=$(for I in /usr/bin/mkfs.*; do if [ -e $I ]; then echo ${I##*mkfs.}; fi; done)
			COMPREPLY=( $(compgen -W "$FSTYPES" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS='--type --verbose --help --version'
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	compopt -o bashdefault -o default
	COMPREPLY=( $(compgen -W "$(lsblk -pnro name)" -- $cur) )
	return 0
}
complete -F _mkfs_module mkfs
