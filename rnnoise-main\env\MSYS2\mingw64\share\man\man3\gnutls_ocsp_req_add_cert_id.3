.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_req_add_cert_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_req_add_cert_id \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_req_add_cert_id(gnutls_ocsp_req_t " req ", gnutls_digest_algorithm_t " digest ", const gnutls_datum_t * " issuer_name_hash ", const gnutls_datum_t * " issuer_key_hash ", const gnutls_datum_t * " serial_number ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_req_t req" 12
should contain a \fBgnutls_ocsp_req_t\fP type
.IP "gnutls_digest_algorithm_t digest" 12
hash algorithm, a \fBgnutls_digest_algorithm_t\fP value
.IP "const gnutls_datum_t * issuer_name_hash" 12
hash of issuer's DN
.IP "const gnutls_datum_t * issuer_key_hash" 12
hash of issuer's public key
.IP "const gnutls_datum_t * serial_number" 12
serial number of certificate to check
.SH "DESCRIPTION"
This function will add another request to the OCSP request for a
particular certificate having the issuer name hash of
 \fIissuer_name_hash\fP and issuer key hash of  \fIissuer_key_hash\fP (both
hashed using  \fIdigest\fP ) and serial number  \fIserial_number\fP .

The information needed corresponds to the CertID structure:

<informalexample><programlisting>
CertID	  ::=     SEQUENCE {
hashAlgorithm       AlgorithmIdentifier,
issuerNameHash      OCTET STRING, \-\- Hash of Issuer's DN
issuerKeyHash       OCTET STRING, \-\- Hash of Issuers public key
serialNumber	CertificateSerialNumber }
</programlisting></informalexample>
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
