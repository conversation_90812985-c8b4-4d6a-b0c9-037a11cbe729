CMake 3.26 Release Notes
************************

.. only:: html

  .. contents::

Changes made since CMake 3.25 include the following.

New Features
============

Languages
---------

* The ``ASM_MARMASM`` language was added to support the
  Microsoft ARM assembler language.

Command-Line
------------

* The :option:`cmake -E copy <cmake-E copy>` command-line tool now
  supports a ``-t`` argument.

* The :option:`cmake -E copy_directory_if_different
  <cmake-E copy_directory_if_different>` command-line tool was added.

Configure Log
-------------

* <PERSON><PERSON><PERSON> now writes a YAML log of configure-time checks to
  ``CMakeFiles/CMakeConfigureLog.yaml`` under the top of the build tree.
  See the :manual:`cmake-configure-log(7)` manual.

File-Based API
--------------

* The :manual:`cmake-file-api(7)` "codemodel" version 2 ``version`` field has
  been updated to 2.5.

* The :manual:`cmake-file-api(7)` "codemodel" version 2 "target" object
  gained a new ``fileSets`` field and associated ``fileSetIndex``
  field to ``sources`` objects.

* The :manual:`cmake-file-api(7)` gained a new "configureLog" object kind
  that enables stable access to the :manual:`cmake-configure-log(7)`.

Commands
--------

* The :command:`add_custom_command` and :command:`add_custom_target` commands
  now support :manual:`generator expressions <cmake-generator-expressions(7)>`
  in their ``COMMENT`` option.

* The :command:`message` command gained a ``CONFIGURE_LOG`` mode to
  record an entry in the :manual:`cmake-configure-log(7)`.

* The :command:`string(TIMESTAMP)` and :command:`file(TIMESTAMP)` commands
  now support the ``%z`` and ``%Z`` specifiers for the time zone.

* The :command:`try_compile` and :command:`try_run` commands gained
  a ``LOG_DESCRIPTION`` option specifying text to be recorded in the
  :manual:`cmake-configure-log(7)`.

* The :command:`try_compile` and :command:`try_run` commands gained a
  ``NO_LOG`` option to skip recording a :manual:`cmake-configure-log(7)`
  entry.

Variables
---------

* The :variable:`CMAKE_<LANG>_COMPILER_FRONTEND_VARIANT` variable is now
  set for ``GNU``, ``MSVC``, and ``AppleClang`` compilers that have only
  one frontend variant.

* A :variable:`CMAKE_VS_VERSION_BUILD_NUMBER` variable is now set by
  :ref:`Visual Studio Generators` for VS 2017 and above to report the
  four-component Visual Studio version number.

Properties
----------

* The :prop_tgt:`<LANG>_CLANG_TIDY_EXPORT_FIXES_DIR` target property was
  added to allow the ``clang-tidy`` tool to export its suggested fixes to a
  set of ``.yaml`` files. A new
  :variable:`CMAKE_<LANG>_CLANG_TIDY_EXPORT_FIXES_DIR` variable was created to
  initialize this property.

* The :prop_tgt:`XCODE_EMBED_EXTENSIONKIT_EXTENSIONS <XCODE_EMBED_<type>>`
  target property was added to tell the :generator:`Xcode` generator to embed
  ExtensionKit-based extensions such as extensions using the Background
  Assets framework.  Aspects of the embedding can be customized with:

  * :prop_tgt:`XCODE_EMBED_EXTENSIONKIT_EXTENSIONS_PATH <XCODE_EMBED_<type>>`
  * :prop_tgt:`XCODE_EMBED_EXTENSIONKIT_EXTENSIONS_CODE_SIGN_ON_COPY <XCODE_EMBED_<type>_CODE_SIGN_ON_COPY>`
  * :prop_tgt:`XCODE_EMBED_EXTENSIONKIT_EXTENSIONS_REMOVE_HEADERS_ON_COPY <XCODE_EMBED_<type>_REMOVE_HEADERS_ON_COPY>`

Modules
-------

* The :module:`ExternalProject` module's :command:`ExternalProject_Add` command
  gained an ``INSTALL_BYPRODUCTS`` option to specify files generated by the
  ``install`` step.

* The :module:`FindCUDAToolkit` module:

  * gained support for the ``sbsa-linux`` cross compilation target, and

  * now provides an imported target for ``nvrtc_static``, if found.

* The :module:`FindImageMagick` module now provides imported targets.

* The :module:`FindPython3` and :module:`FindPython` modules gained
  support for the `Stable Application Binary Interface`_.

* The :module:`UseSWIG` module gained support for the ``perl5`` language.

.. _`Stable Application Binary Interface`: https://docs.python.org/3/c-api/stable.html

Generator Expressions
---------------------

* The :genex:`$<BUILD_LOCAL_INTERFACE:...>` generator expression was added to
  prevent usage requirements from being exported to dependent projects.

CTest
-----

* The :envvar:`CTEST_NO_TESTS_ACTION` environment variable was added to
  provide a default value for the
  :option:`--no-tests=\<action\> <ctest --no-tests>` command line
  argument of :manual:`ctest(1)`.

Deprecated and Removed Features
===============================

* The ``CMakeFiles/CMakeOutput.log`` and ``CMakeFiles/CMakeError.log``
  files are no longer populated by CMake's built-in modules.
  :manual:`cmake(1)` no longer suggests looking at them after a
  ``CMake Error`` occurs.  Information previously logged to those
  files is instead logged to the :manual:`cmake-configure-log(7)`.

* On CYGWIN, the undocumented ``CMAKE_LEGACY_CYGWIN_WIN32`` mode for
  compatibility with CMake versions older than 2.8.4 has been removed.

Other Changes
=============

* :ref:`Language Standard Flags`, such as ``-std=c++11``, when generated due
  to :command:`target_compile_features` or :variable:`CMAKE_<LANG>_STANDARD`,
  are now placed before flags added by :command:`target_compile_options`,
  rather than after them.

* For all ``COMPILE_DEFINITIONS`` properties, any leading ``-D`` on an item
  is removed whether or not it was specified by a generator expression.

* The ``compile_commands.json`` database enabled by
  :variable:`CMAKE_EXPORT_COMPILE_COMMANDS` now provides the ``output``
  field in the compile commands objects.  This allows multi-config
  generators, such as the :generator:`Ninja Multi-Config` generator,
  to provide the compile commands for all configurations.

* The :prop_gbl:`USE_FOLDERS` global property is treated as ``ON`` by default.
  See policy :policy:`CMP0143`.

* The top-level :command:`project` call will now emit an author warning if the
  documented command order in relation to :command:`cmake_minimum_required` is
  not respected.

* The :option:`cmake --trace` option now follows :command:`try_compile` and
  :command:`try_run` invocations.

Updates
=======

Changes made since CMake 3.26.0 include the following.

3.26.1, 3.26.2, 3.26.3, 3.26.4, 3.26.5
--------------------------------------

* These versions made no changes to documented features or interfaces.
  Some implementation updates were made to support ecosystem changes
  and/or fix regressions.

3.26.6
------

* The :manual:`cmake-file-api(7)` "codemodel" version 2 "target" object
  ``fileSets`` field was introduced by CMake 3.26.0 with entries of its
  ``baseDirectories`` member incorrectly expressed as absolute paths
  even if they are inside the top-level source directory.  This has
  been fixed.  Clients must be updated to expect relative paths under
  the top-level source directory.
