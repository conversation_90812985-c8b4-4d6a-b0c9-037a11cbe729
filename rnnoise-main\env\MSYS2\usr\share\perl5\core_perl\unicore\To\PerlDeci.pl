# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


# The mappings must be modified to get the correct values by adding the code
# point ordinal number to each one that is numeric.

# The name this table is to be known by, with the format of the mappings in
# the main body of the table, and what all code points missing from this file
# map to.
$Unicode::UCD::SwashInfo{'ToPerlDecimalDigit'}{'format'} = 'a'; # some entries need adjustment
$Unicode::UCD::SwashInfo{'ToPerlDecimalDigit'}{'missing'} = ''; # code point maps to the empty string

return <<'END';
30	39	0
660	669	0
6F0	6F9	0
7C0	7C9	0
966	96F	0
9E6	9EF	0
A66	A6F	0
AE6	AEF	0
B66	B6F	0
BE6	BEF	0
C66	C6F	0
CE6	CEF	0
D66	D6F	0
DE6	DEF	0
E50	E59	0
ED0	ED9	0
F20	F29	0
1040	1049	0
1090	1099	0
17E0	17E9	0
1810	1819	0
1946	194F	0
19D0	19D9	0
1A80	1A89	0
1A90	1A99	0
1B50	1B59	0
1BB0	1BB9	0
1C40	1C49	0
1C50	1C59	0
A620	A629	0
A8D0	A8D9	0
A900	A909	0
A9D0	A9D9	0
A9F0	A9F9	0
AA50	AA59	0
ABF0	ABF9	0
FF10	FF19	0
104A0	104A9	0
10D30	10D39	0
11066	1106F	0
110F0	110F9	0
11136	1113F	0
111D0	111D9	0
112F0	112F9	0
11450	11459	0
114D0	114D9	0
11650	11659	0
116C0	116C9	0
11730	11739	0
118E0	118E9	0
11950	11959	0
11C50	11C59	0
11D50	11D59	0
11DA0	11DA9	0
11F50	11F59	0
16A60	16A69	0
16AC0	16AC9	0
16B50	16B59	0
1D7CE	1D7D7	0
1D7D8	1D7E1	0
1D7E2	1D7EB	0
1D7EC	1D7F5	0
1D7F6	1D7FF	0
1E140	1E149	0
1E2F0	1E2F9	0
1E4F0	1E4F9	0
1E950	1E959	0
1FBF0	1FBF9	0
END
