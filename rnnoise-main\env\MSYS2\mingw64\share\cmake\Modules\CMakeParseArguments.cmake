# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file LICENSE.rst or https://cmake.org/licensing for details.

#[=======================================================================[.rst:
CMakeParseArguments
-------------------

.. deprecated:: 3.5

This module once implemented the :command:`cmake_parse_arguments` command
that is now implemented natively by CMake.  It is now an empty placeholder
for compatibility with projects that include it to get the command from
CMake 3.4 and lower.
#]=======================================================================]
