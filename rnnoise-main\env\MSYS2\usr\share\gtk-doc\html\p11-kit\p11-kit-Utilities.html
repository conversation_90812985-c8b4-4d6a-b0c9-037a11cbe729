<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Utilities: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="reference.html" title="API Reference">
<link rel="prev" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">
<link rel="next" href="p11-kit-Future.html" title="Future">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts">
<a href="#" class="shortcut">Top</a><span id="nav_description">  <span class="dim">|</span> 
                  <a href="#p11-kit-Utilities.description" class="shortcut">Description</a></span>
</td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="reference.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="p11-kit-PIN-Callbacks.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="p11-kit-Future.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="refentry">
<a name="p11-kit-Utilities"></a><div class="titlepage"></div>
<div class="refnamediv"><table width="100%"><tr>
<td valign="top">
<h2><span class="refentrytitle"><a name="p11-kit-Utilities.top_of_page"></a>Utilities</span></h2>
<p>Utilities — PKCS#11 utilities</p>
</td>
<td class="gallery_image" valign="top" align="right"></td>
</tr></table></div>
<div class="refsect1">
<a name="p11-kit-Utilities.functions"></a><h2>Functions</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="functions_proto_type">
<col class="functions_proto_name">
</colgroup>
<tbody>
<tr>
<td class="function_type">const <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Utilities.html#p11-kit-strerror" title="p11_kit_strerror ()">p11_kit_strerror</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">const <span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Utilities.html#p11-kit-message" title="p11_kit_message ()">p11_kit_message</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Utilities.html#p11-kit-space-strdup" title="p11_kit_space_strdup ()">p11_kit_space_strdup</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">size_t</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Utilities.html#p11-kit-space-strlen" title="p11_kit_space_strlen ()">p11_kit_space_strlen</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Utilities.html#p11-kit-be-quiet" title="p11_kit_be_quiet ()">p11_kit_be_quiet</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Utilities.html#p11-kit-be-loud" title="p11_kit_be_loud ()">p11_kit_be_loud</a> <span class="c_punctuation">()</span>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-Utilities.description"></a><h2>Description</h2>
<p>Utility functions for working with PKCS#11.</p>
</div>
<div class="refsect1">
<a name="p11-kit-Utilities.functions_details"></a><h2>Functions</h2>
<div class="refsect2">
<a name="p11-kit-strerror"></a><h3>p11_kit_strerror ()</h3>
<pre class="programlisting">const <span class="returnvalue">char</span> *
p11_kit_strerror (<em class="parameter"><code><span class="type">CK_RV</span> rv</code></em>);</pre>
<p>Get a message for a PKCS#11 return value or error code. Do not
pass CKR_OK or other such non errors to this function.</p>
<div class="refsect3">
<a name="p11-kit-strerror.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>rv</p></td>
<td class="parameter_description"><p>The code to get a message for.</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-strerror.returns"></a><h4>Returns</h4>
<p> The user readable and localized message.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-message"></a><h3>p11_kit_message ()</h3>
<pre class="programlisting">const <span class="returnvalue">char</span> *
p11_kit_message (<em class="parameter"><code><span class="type">void</span></code></em>);</pre>
<p>Gets the failure message for a recently called p11-kit function, which
returned a failure code on this thread. Not all functions set this message.
Each function that does so, will note it in its documentation.</p>
<p>If the most recent p11-kit function did not fail, then this will return NULL.
The string is owned by the p11-kit library and is only valid on the same
thread that the failed function executed on.</p>
<div class="refsect3">
<a name="p11-kit-message.returns"></a><h4>Returns</h4>
<p> The last failure message, or <code class="literal">NULL</code>.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-space-strdup"></a><h3>p11_kit_space_strdup ()</h3>
<pre class="programlisting"><span class="returnvalue">char</span> *
p11_kit_space_strdup (<em class="parameter"><code>const unsigned <span class="type">char</span> *string</code></em>,
                      <em class="parameter"><code><span class="type">size_t</span> max_length</code></em>);</pre>
<p>In PKCS#11 structures many strings are encoded in a strange way. The string
is placed in a fixed length buffer and then padded with spaces.</p>
<p>This function copies the space padded string into a normal null-terminated
string. The result is owned by the caller.</p>
<div class="informalexample">
  <table class="listing_frame" border="0" cellpadding="0" cellspacing="0">
    <tbody>
      <tr>
        <td class="listing_lines" align="right"><pre>1
2
3
4</pre></td>
        <td class="listing_code"><pre class="programlisting"><span class="n">CK_INFO</span><span class="w"> </span><span class="n">info</span><span class="p">;</span>
<span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">description</span><span class="p">;</span>
<span class="w">   </span><span class="p">...</span>
<span class="n">description</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">p11_kit_space_strdup</span><span class="w"> </span><span class="p">(</span><span class="n">info</span><span class="o">-&gt;</span><span class="n">libraryDescription</span><span class="p">,</span><span class="w"> </span><span class="k">sizeof</span><span class="w"> </span><span class="p">(</span><span class="n">info</span><span class="o">-&gt;</span><span class="n">libraryDescription</span><span class="p">));</span></pre></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="refsect3">
<a name="p11-kit-space-strdup.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>string</p></td>
<td class="parameter_description"><p>Pointer to string block</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>max_length</p></td>
<td class="parameter_description"><p>Maximum length of string block</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-space-strdup.returns"></a><h4>Returns</h4>
<p> The newly allocated string, or <code class="literal">NULL</code> if memory could not be allocated.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-space-strlen"></a><h3>p11_kit_space_strlen ()</h3>
<pre class="programlisting"><span class="returnvalue">size_t</span>
p11_kit_space_strlen (<em class="parameter"><code>const unsigned <span class="type">char</span> *string</code></em>,
                      <em class="parameter"><code><span class="type">size_t</span> max_length</code></em>);</pre>
<p>In PKCS#11 structures many strings are encoded in a strange way. The string
is placed in a fixed length buffer and then padded with spaces.</p>
<p>This function determines the actual length of the string. Since the string
is not null-terminated you need to pass in the size of buffer as max_length.
The string will never be longer than this buffer.</p>
<div class="informalexample">
  <table class="listing_frame" border="0" cellpadding="0" cellspacing="0">
    <tbody>
      <tr>
        <td class="listing_lines" align="right"><pre>1
2
3
4</pre></td>
        <td class="listing_code"><pre class="programlisting"><span class="n">CK_INFO</span><span class="w"> </span><span class="n">info</span><span class="p">;</span>
<span class="kt">size_t</span><span class="w"> </span><span class="n">length</span><span class="p">;</span>
<span class="w">   </span><span class="p">...</span>
<span class="n">length</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">p11_kit_space_strlen</span><span class="w"> </span><span class="p">(</span><span class="n">info</span><span class="o">-&gt;</span><span class="n">libraryDescription</span><span class="p">,</span><span class="w"> </span><span class="k">sizeof</span><span class="w"> </span><span class="p">(</span><span class="n">info</span><span class="o">-&gt;</span><span class="n">libraryDescription</span><span class="p">));</span></pre></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="refsect3">
<a name="p11-kit-space-strlen.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>string</p></td>
<td class="parameter_description"><p>Pointer to string block</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>max_length</p></td>
<td class="parameter_description"><p>Maximum length of string block</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-space-strlen.returns"></a><h4>Returns</h4>
<p> The length of the space padded string.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-be-quiet"></a><h3>p11_kit_be_quiet ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_be_quiet (<em class="parameter"><code><span class="type">void</span></code></em>);</pre>
<p>Once this function is called, the p11-kit library will no longer print
failure or warning messages to stderr.</p>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-be-loud"></a><h3>p11_kit_be_loud ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_be_loud (<em class="parameter"><code><span class="type">void</span></code></em>);</pre>
<p>Tell the p11-kit library will print failure or warning messages to stderr.
This is the default behavior, but can be changed using <a class="link" href="p11-kit-Utilities.html#p11-kit-be-quiet" title="p11_kit_be_quiet ()"><code class="function">p11_kit_be_quiet()</code></a>.</p>
</div>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>