.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake_set_post_client_hello_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake_set_post_client_hello_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_handshake_set_post_client_hello_function(gnutls_session_t " session ", gnutls_handshake_simple_hook_func " func ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_handshake_simple_hook_func func" 12
is the function to be called
.SH "DESCRIPTION"
This function will set a callback to be called after the client
hello has been received (callback valid in server side only). This
allows the server to adjust settings based on received extensions.

Those settings could be ciphersuites, requesting certificate, or
anything else except for version negotiation (this is done before
the hello message is parsed).

This callback must return 0 on success or a gnutls error code to
terminate the handshake.

Since GnuTLS 3.3.5 the callback is
allowed to return \fBGNUTLS_E_AGAIN\fP or \fBGNUTLS_E_INTERRUPTED\fP to
put the handshake on hold. In that case \fBgnutls_handshake()\fP
will return \fBGNUTLS_E_INTERRUPTED\fP and can be resumed when needed.
.SH "WARNING"
You should not use this function to terminate the
handshake based on client input unless you know what you are
doing. Before the handshake is finished there is no way to know if
there is a man\-in\-the\-middle attack being performed.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
