CMAKE_C_COMPILE_FEATURES
------------------------

.. versionadded:: 3.1

List of features known to the C compiler

These features are known to be available for use with the C compiler. This
list is a subset of the features listed in the
:prop_gbl:`CMAKE_C_KNOWN_FEATURES` global property.

See the :manual:`cmake-compile-features(7)` manual for information on
compile features and a list of supported compilers.
