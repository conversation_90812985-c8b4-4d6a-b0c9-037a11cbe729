/*
 * Background Intelligent Transfer Service (BITS) 2.0 interface
 *
 * Copyright 2015 <PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 *
 */

#ifndef DO_NO_IMPORTS
import "bits.idl";
import "bits1_5.idl";
#endif

cpp_quote("#define BG_COPY_FILE_OWNER 1")
cpp_quote("#define BG_COPY_FILE_GROUP 2")
cpp_quote("#define BG_COPY_FILE_DACL  4")
cpp_quote("#define BG_COPY_FILE_SACL  8")
cpp_quote("#define BG_COPY_FILE_ALL   15")

cpp_quote("#define BG_LENGTH_TO_EOF (UINT64)(-1)")

cpp_quote("#ifndef _BG_FILE_RANGE_DEFINED")
cpp_quote("#define _BG_FILE_RANGE_DEFINED")
typedef struct _BG_FILE_RANGE
{
    UINT64 InitialOffset;
    UINT64 Length;
} BG_FILE_RANGE;
cpp_quote("#endif")

[
    uuid(443c8934-90ff-48ed-bcde-26f5c7450042),
    odl
]
interface IBackgroundCopyJob3 : IBackgroundCopyJob2
{
    HRESULT ReplaceRemotePrefix(
        [in] LPCWSTR OldPrefix,
        [in] LPCWSTR NewPrefix);

    HRESULT AddFileWithRanges(
        [in] LPCWSTR RemoteUrl,
        [in] LPCWSTR LocalName,
        [in] DWORD RangeCount,
        [in, size_is(RangeCount)] BG_FILE_RANGE Ranges[]);

    HRESULT SetFileACLFlags(
        [in] DWORD Flags);

    HRESULT GetFileACLFlags(
        [out, ref] DWORD *Flags);
}

[
    uuid(83e81b93-0873-474d-8a8c-f2018b1a939c),
    odl
]
interface IBackgroundCopyFile2 : IBackgroundCopyFile
{
    HRESULT GetFileRanges(
        [in, out, unique] DWORD *RangeCount,
        [out, size_is(, *RangeCount)] BG_FILE_RANGE **Ranges);

    HRESULT SetRemoteName(
        LPCWSTR Val);
}

[
    uuid(2289a9af-dc96-486e-b268-89c9e3397c3d),
    version(1.0)
]
library BackgroundCopyManager2_0
{
    [
        uuid(6d18ad12-bde3-4393-b311-099c346e6df9)
    ]
    coclass BackgroundCopyManager2_0
    {
        [default] interface IBackgroundCopyManager;
    };

    interface IBackgroundCopyCallback;
    interface IBackgroundCopyJob3;
    interface IBackgroundCopyFile2;
}

cpp_quote("#include \"bits2_5.h\"")
