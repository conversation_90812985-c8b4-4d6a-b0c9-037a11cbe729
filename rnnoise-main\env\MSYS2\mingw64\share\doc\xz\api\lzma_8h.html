<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('lzma_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">lzma.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>The public API of liblzma data compression library.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;stddef.h&gt;</code><br />
<code>#include &lt;inttypes.h&gt;</code><br />
<code>#include &lt;limits.h&gt;</code><br />
<code>#include &quot;<a class="el" href="version_8h.html">lzma/version.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="base_8h.html">lzma/base.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="vli_8h.html">lzma/vli.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="check_8h.html">lzma/check.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="filter_8h.html">lzma/filter.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="bcj_8h.html">lzma/bcj.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="delta_8h.html">lzma/delta.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="lzma12_8h.html">lzma/lzma12.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="container_8h.html">lzma/container.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="stream__flags_8h.html">lzma/stream_flags.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="block_8h.html">lzma/block.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="index_8h.html">lzma/index.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="index__hash_8h.html">lzma/index_hash.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="hardware_8h.html">lzma/hardware.h</a>&quot;</code><br />
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:acecee981dc3f28418f54035e20d7e578" id="r_acecee981dc3f28418f54035e20d7e578"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acecee981dc3f28418f54035e20d7e578">UINT32_C</a>(n)</td></tr>
<tr class="separator:acecee981dc3f28418f54035e20d7e578"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6548785b283106d4d7bfc7a4ef87fc4" id="r_ac6548785b283106d4d7bfc7a4ef87fc4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac6548785b283106d4d7bfc7a4ef87fc4">UINT64_C</a>(n)</td></tr>
<tr class="separator:ac6548785b283106d4d7bfc7a4ef87fc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5eb23180f7cc12b7d6c04a8ec067fdd" id="r_ab5eb23180f7cc12b7d6c04a8ec067fdd"><td class="memItemLeft" align="right" valign="top"><a id="ab5eb23180f7cc12b7d6c04a8ec067fdd" name="ab5eb23180f7cc12b7d6c04a8ec067fdd"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>UINT32_MAX</b>&#160;&#160;&#160;(UINT32_C(4294967295))</td></tr>
<tr class="separator:ab5eb23180f7cc12b7d6c04a8ec067fdd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30654b4b67d97c42ca3f9b6052dda916" id="r_a30654b4b67d97c42ca3f9b6052dda916"><td class="memItemLeft" align="right" valign="top"><a id="a30654b4b67d97c42ca3f9b6052dda916" name="a30654b4b67d97c42ca3f9b6052dda916"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>UINT64_MAX</b>&#160;&#160;&#160;(UINT64_C(18446744073709551615))</td></tr>
<tr class="separator:a30654b4b67d97c42ca3f9b6052dda916"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a55854429aba444dbb0bd8325fed4eece" id="r_a55854429aba444dbb0bd8325fed4eece"><td class="memItemLeft" align="right" valign="top"><a id="a55854429aba444dbb0bd8325fed4eece" name="a55854429aba444dbb0bd8325fed4eece"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>lzma_nothrow</b></td></tr>
<tr class="separator:a55854429aba444dbb0bd8325fed4eece"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbc570ea8654f0c96699e38249ac8032" id="r_adbc570ea8654f0c96699e38249ac8032"><td class="memItemLeft" align="right" valign="top"><a id="adbc570ea8654f0c96699e38249ac8032" name="adbc570ea8654f0c96699e38249ac8032"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>lzma_attr_pure</b>&#160;&#160;&#160;lzma_attribute((__pure__))</td></tr>
<tr class="separator:adbc570ea8654f0c96699e38249ac8032"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aaafb664f89a525ff22530d61704556" id="r_a0aaafb664f89a525ff22530d61704556"><td class="memItemLeft" align="right" valign="top"><a id="a0aaafb664f89a525ff22530d61704556" name="a0aaafb664f89a525ff22530d61704556"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>lzma_attr_const</b>&#160;&#160;&#160;lzma_attribute((__const__))</td></tr>
<tr class="separator:a0aaafb664f89a525ff22530d61704556"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af15e147fcd7986ec4dd82660aec2b695" id="r_af15e147fcd7986ec4dd82660aec2b695"><td class="memItemLeft" align="right" valign="top"><a id="af15e147fcd7986ec4dd82660aec2b695" name="af15e147fcd7986ec4dd82660aec2b695"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>lzma_attr_warn_unused_result</b>&#160;&#160;&#160;		lzma_attribute((__warn_unused_result__))</td></tr>
<tr class="separator:af15e147fcd7986ec4dd82660aec2b695"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af828f75941ade54f8379bb847da37349" id="r_af828f75941ade54f8379bb847da37349"><td class="memItemLeft" align="right" valign="top"><a id="af828f75941ade54f8379bb847da37349" name="af828f75941ade54f8379bb847da37349"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_H_INTERNAL</b>&#160;&#160;&#160;1</td></tr>
<tr class="separator:af828f75941ade54f8379bb847da37349"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The public API of liblzma data compression library. </p>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="acecee981dc3f28418f54035e20d7e578" name="acecee981dc3f28418f54035e20d7e578"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acecee981dc3f28418f54035e20d7e578">&#9670;&#160;</a></span>UINT32_C</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define UINT32_C</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>n</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">n ## U</div>
</div><!-- fragment -->
</div>
</div>
<a id="ac6548785b283106d4d7bfc7a4ef87fc4" name="ac6548785b283106d4d7bfc7a4ef87fc4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac6548785b283106d4d7bfc7a4ef87fc4">&#9670;&#160;</a></span>UINT64_C</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define UINT64_C</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>n</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">n ## UL</div>
</div><!-- fragment -->
</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="lzma_8h.html">lzma.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
