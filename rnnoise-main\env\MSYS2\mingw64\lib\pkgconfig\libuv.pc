prefix=/mingw64
exec_prefix=${prefix}
libdir=/mingw64/lib
includedir=/mingw64/include
LIBUV_STATIC=-L${libdir} -l:libuv.a -lpsapi -luser32 -ladvapi32 -liphlpapi -luserenv -lws2_32 -ldbghelp -lole32 -lshell32

Name: libuv
Version: 1.51.0
Description: multi-platform support library with a focus on asynchronous I/O.
URL: http://libuv.org/

Libs: -L${libdir} -luv -lpsapi -luser32 -ladvapi32 -liphlpapi -luserenv -lws2_32 -ldbghelp -lole32 -lshell32
Cflags: -I${includedir}
