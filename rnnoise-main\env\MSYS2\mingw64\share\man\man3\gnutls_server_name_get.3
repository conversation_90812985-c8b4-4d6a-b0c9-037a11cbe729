.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_server_name_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_server_name_get \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_server_name_get(gnutls_session_t " session ", void * " data ", size_t * " data_length ", unsigned int * " type ", unsigned int " indx ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "void * data" 12
will hold the data
.IP "size_t * data_length" 12
will hold the data length. Must hold the maximum size of data.
.IP "unsigned int * type" 12
will hold the server name indicator type
.IP "unsigned int indx" 12
is the index of the server_name
.SH "DESCRIPTION"
This function will allow you to get the name indication (if any), a
client has sent.  The name indication may be any of the enumeration
gnutls_server_name_type_t.

If  \fItype\fP is GNUTLS_NAME_DNS, then this function is to be used by
servers that support virtual hosting, and the data will be a null
terminated IDNA ACE string (prior to GnuTLS 3.4.0 it was a UTF\-8 string).

If  \fIdata\fP has not enough size to hold the server name
GNUTLS_E_SHORT_MEMORY_BUFFER is returned, and  \fIdata_length\fP will
hold the required size.

 \fIindx\fP is used to retrieve more than one server names (if sent by
the client).  The first server name has an index of 0, the second 1
and so on.  If no name with the given index exists
GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE is returned.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, on UTF\-8
decoding error \fBGNUTLS_E_IDNA_ERROR\fP is returned, otherwise a negative
error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
