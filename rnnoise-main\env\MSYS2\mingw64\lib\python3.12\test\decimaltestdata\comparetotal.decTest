------------------------------------------------------------------------
-- comparetotal.decTest -- decimal comparison using total ordering    --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- Note that we cannot assume add/subtract tests cover paths adequately,
-- here, because the code might be quite different (comparison cannot
-- overflow or underflow, so actual subtractions are not necessary).
-- Similarly, comparetotal will have some radically different paths
-- than compare.

extended:    1
precision:   16
rounding:    half_up
maxExponent: 384
minExponent: -383

-- sanity checks
cotx001 comparetotal  -2  -2  -> 0
cotx002 comparetotal  -2  -1  -> -1
cotx003 comparetotal  -2   0  -> -1
cotx004 comparetotal  -2   1  -> -1
cotx005 comparetotal  -2   2  -> -1
cotx006 comparetotal  -1  -2  -> 1
cotx007 comparetotal  -1  -1  -> 0
cotx008 comparetotal  -1   0  -> -1
cotx009 comparetotal  -1   1  -> -1
cotx010 comparetotal  -1   2  -> -1
cotx011 comparetotal   0  -2  -> 1
cotx012 comparetotal   0  -1  -> 1
cotx013 comparetotal   0   0  -> 0
cotx014 comparetotal   0   1  -> -1
cotx015 comparetotal   0   2  -> -1
cotx016 comparetotal   1  -2  -> 1
cotx017 comparetotal   1  -1  -> 1
cotx018 comparetotal   1   0  -> 1
cotx019 comparetotal   1   1  -> 0
cotx020 comparetotal   1   2  -> -1
cotx021 comparetotal   2  -2  -> 1
cotx022 comparetotal   2  -1  -> 1
cotx023 comparetotal   2   0  -> 1
cotx025 comparetotal   2   1  -> 1
cotx026 comparetotal   2   2  -> 0

cotx031 comparetotal  -20  -20  -> 0
cotx032 comparetotal  -20  -10  -> -1
cotx033 comparetotal  -20   00  -> -1
cotx034 comparetotal  -20   10  -> -1
cotx035 comparetotal  -20   20  -> -1
cotx036 comparetotal  -10  -20  -> 1
cotx037 comparetotal  -10  -10  -> 0
cotx038 comparetotal  -10   00  -> -1
cotx039 comparetotal  -10   10  -> -1
cotx040 comparetotal  -10   20  -> -1
cotx041 comparetotal   00  -20  -> 1
cotx042 comparetotal   00  -10  -> 1
cotx043 comparetotal   00   00  -> 0
cotx044 comparetotal   00   10  -> -1
cotx045 comparetotal   00   20  -> -1
cotx046 comparetotal   10  -20  -> 1
cotx047 comparetotal   10  -10  -> 1
cotx048 comparetotal   10   00  -> 1
cotx049 comparetotal   10   10  -> 0
cotx050 comparetotal   10   20  -> -1
cotx051 comparetotal   20  -20  -> 1
cotx052 comparetotal   20  -10  -> 1
cotx053 comparetotal   20   00  -> 1
cotx055 comparetotal   20   10  -> 1
cotx056 comparetotal   20   20  -> 0

cotx061 comparetotal  -2.0  -2.0  -> 0
cotx062 comparetotal  -2.0  -1.0  -> -1
cotx063 comparetotal  -2.0   0.0  -> -1
cotx064 comparetotal  -2.0   1.0  -> -1
cotx065 comparetotal  -2.0   2.0  -> -1
cotx066 comparetotal  -1.0  -2.0  -> 1
cotx067 comparetotal  -1.0  -1.0  -> 0
cotx068 comparetotal  -1.0   0.0  -> -1
cotx069 comparetotal  -1.0   1.0  -> -1
cotx070 comparetotal  -1.0   2.0  -> -1
cotx071 comparetotal   0.0  -2.0  -> 1
cotx072 comparetotal   0.0  -1.0  -> 1
cotx073 comparetotal   0.0   0.0  -> 0
cotx074 comparetotal   0.0   1.0  -> -1
cotx075 comparetotal   0.0   2.0  -> -1
cotx076 comparetotal   1.0  -2.0  -> 1
cotx077 comparetotal   1.0  -1.0  -> 1
cotx078 comparetotal   1.0   0.0  -> 1
cotx079 comparetotal   1.0   1.0  -> 0
cotx080 comparetotal   1.0   2.0  -> -1
cotx081 comparetotal   2.0  -2.0  -> 1
cotx082 comparetotal   2.0  -1.0  -> 1
cotx083 comparetotal   2.0   0.0  -> 1
cotx085 comparetotal   2.0   1.0  -> 1
cotx086 comparetotal   2.0   2.0  -> 0

-- now some cases which might overflow if subtract were used
maxexponent: 999999999
minexponent: -999999999
cotx090 comparetotal  9.99999999E+999999999 9.99999999E+999999999  -> 0
cotx091 comparetotal -9.99999999E+999999999 9.99999999E+999999999  -> -1
cotx092 comparetotal  9.99999999E+999999999 -9.99999999E+999999999 -> 1
cotx093 comparetotal -9.99999999E+999999999 -9.99999999E+999999999 -> 0

-- Examples
cotx094 comparetotal  12.73  127.9  -> -1
cotx095 comparetotal  -127   12     -> -1
cotx096 comparetotal  12.30  12.3   -> -1
cotx097 comparetotal  12.30  12.30  ->  0
cotx098 comparetotal  12.3   12.300 ->  1
cotx099 comparetotal  12.3   NaN    -> -1

-- some differing length/exponent cases
-- in this first group, compare would compare all equal
cotx100 comparetotal   7.0    7.0    -> 0
cotx101 comparetotal   7.0    7      -> -1
cotx102 comparetotal   7      7.0    -> 1
cotx103 comparetotal   7E+0   7.0    -> 1
cotx104 comparetotal   70E-1  7.0    -> 0
cotx105 comparetotal   0.7E+1 7      -> 0
cotx106 comparetotal   70E-1  7      -> -1
cotx107 comparetotal   7.0    7E+0   -> -1
cotx108 comparetotal   7.0    70E-1  -> 0
cotx109 comparetotal   7      0.7E+1 -> 0
cotx110 comparetotal   7      70E-1  -> 1

cotx120 comparetotal   8.0    7.0    -> 1
cotx121 comparetotal   8.0    7      -> 1
cotx122 comparetotal   8      7.0    -> 1
cotx123 comparetotal   8E+0   7.0    -> 1
cotx124 comparetotal   80E-1  7.0    -> 1
cotx125 comparetotal   0.8E+1 7      -> 1
cotx126 comparetotal   80E-1  7      -> 1
cotx127 comparetotal   8.0    7E+0   -> 1
cotx128 comparetotal   8.0    70E-1  -> 1
cotx129 comparetotal   8      0.7E+1  -> 1
cotx130 comparetotal   8      70E-1  -> 1

cotx140 comparetotal   8.0    9.0    -> -1
cotx141 comparetotal   8.0    9      -> -1
cotx142 comparetotal   8      9.0    -> -1
cotx143 comparetotal   8E+0   9.0    -> -1
cotx144 comparetotal   80E-1  9.0    -> -1
cotx145 comparetotal   0.8E+1 9      -> -1
cotx146 comparetotal   80E-1  9      -> -1
cotx147 comparetotal   8.0    9E+0   -> -1
cotx148 comparetotal   8.0    90E-1  -> -1
cotx149 comparetotal   8      0.9E+1 -> -1
cotx150 comparetotal   8      90E-1  -> -1

-- and again, with sign changes -+ ..
cotx200 comparetotal  -7.0    7.0    -> -1
cotx201 comparetotal  -7.0    7      -> -1
cotx202 comparetotal  -7      7.0    -> -1
cotx203 comparetotal  -7E+0   7.0    -> -1
cotx204 comparetotal  -70E-1  7.0    -> -1
cotx205 comparetotal  -0.7E+1 7      -> -1
cotx206 comparetotal  -70E-1  7      -> -1
cotx207 comparetotal  -7.0    7E+0   -> -1
cotx208 comparetotal  -7.0    70E-1  -> -1
cotx209 comparetotal  -7      0.7E+1 -> -1
cotx210 comparetotal  -7      70E-1  -> -1

cotx220 comparetotal  -8.0    7.0    -> -1
cotx221 comparetotal  -8.0    7      -> -1
cotx222 comparetotal  -8      7.0    -> -1
cotx223 comparetotal  -8E+0   7.0    -> -1
cotx224 comparetotal  -80E-1  7.0    -> -1
cotx225 comparetotal  -0.8E+1 7      -> -1
cotx226 comparetotal  -80E-1  7      -> -1
cotx227 comparetotal  -8.0    7E+0   -> -1
cotx228 comparetotal  -8.0    70E-1  -> -1
cotx229 comparetotal  -8      0.7E+1 -> -1
cotx230 comparetotal  -8      70E-1  -> -1

cotx240 comparetotal  -8.0    9.0    -> -1
cotx241 comparetotal  -8.0    9      -> -1
cotx242 comparetotal  -8      9.0    -> -1
cotx243 comparetotal  -8E+0   9.0    -> -1
cotx244 comparetotal  -80E-1  9.0    -> -1
cotx245 comparetotal  -0.8E+1 9      -> -1
cotx246 comparetotal  -80E-1  9      -> -1
cotx247 comparetotal  -8.0    9E+0   -> -1
cotx248 comparetotal  -8.0    90E-1  -> -1
cotx249 comparetotal  -8      0.9E+1 -> -1
cotx250 comparetotal  -8      90E-1  -> -1

-- and again, with sign changes +- ..
cotx300 comparetotal   7.0    -7.0    -> 1
cotx301 comparetotal   7.0    -7      -> 1
cotx302 comparetotal   7      -7.0    -> 1
cotx303 comparetotal   7E+0   -7.0    -> 1
cotx304 comparetotal   70E-1  -7.0    -> 1
cotx305 comparetotal   .7E+1  -7      -> 1
cotx306 comparetotal   70E-1  -7      -> 1
cotx307 comparetotal   7.0    -7E+0   -> 1
cotx308 comparetotal   7.0    -70E-1  -> 1
cotx309 comparetotal   7      -.7E+1  -> 1
cotx310 comparetotal   7      -70E-1  -> 1

cotx320 comparetotal   8.0    -7.0    -> 1
cotx321 comparetotal   8.0    -7      -> 1
cotx322 comparetotal   8      -7.0    -> 1
cotx323 comparetotal   8E+0   -7.0    -> 1
cotx324 comparetotal   80E-1  -7.0    -> 1
cotx325 comparetotal   .8E+1  -7      -> 1
cotx326 comparetotal   80E-1  -7      -> 1
cotx327 comparetotal   8.0    -7E+0   -> 1
cotx328 comparetotal   8.0    -70E-1  -> 1
cotx329 comparetotal   8      -.7E+1  -> 1
cotx330 comparetotal   8      -70E-1  -> 1

cotx340 comparetotal   8.0    -9.0    -> 1
cotx341 comparetotal   8.0    -9      -> 1
cotx342 comparetotal   8      -9.0    -> 1
cotx343 comparetotal   8E+0   -9.0    -> 1
cotx344 comparetotal   80E-1  -9.0    -> 1
cotx345 comparetotal   .8E+1  -9      -> 1
cotx346 comparetotal   80E-1  -9      -> 1
cotx347 comparetotal   8.0    -9E+0   -> 1
cotx348 comparetotal   8.0    -90E-1  -> 1
cotx349 comparetotal   8      -.9E+1  -> 1
cotx350 comparetotal   8      -90E-1  -> 1

-- and again, with sign changes -- ..
cotx400 comparetotal   -7.0    -7.0    -> 0
cotx401 comparetotal   -7.0    -7      -> 1
cotx402 comparetotal   -7      -7.0    -> -1
cotx403 comparetotal   -7E+0   -7.0    -> -1
cotx404 comparetotal   -70E-1  -7.0    -> 0
cotx405 comparetotal   -.7E+1  -7      -> 0
cotx406 comparetotal   -70E-1  -7      -> 1
cotx407 comparetotal   -7.0    -7E+0   -> 1
cotx408 comparetotal   -7.0    -70E-1  -> 0
cotx409 comparetotal   -7      -.7E+1  -> 0
cotx410 comparetotal   -7      -70E-1  -> -1

cotx420 comparetotal   -8.0    -7.0    -> -1
cotx421 comparetotal   -8.0    -7      -> -1
cotx422 comparetotal   -8      -7.0    -> -1
cotx423 comparetotal   -8E+0   -7.0    -> -1
cotx424 comparetotal   -80E-1  -7.0    -> -1
cotx425 comparetotal   -.8E+1  -7      -> -1
cotx426 comparetotal   -80E-1  -7      -> -1
cotx427 comparetotal   -8.0    -7E+0   -> -1
cotx428 comparetotal   -8.0    -70E-1  -> -1
cotx429 comparetotal   -8      -.7E+1  -> -1
cotx430 comparetotal   -8      -70E-1  -> -1

cotx440 comparetotal   -8.0    -9.0    -> 1
cotx441 comparetotal   -8.0    -9      -> 1
cotx442 comparetotal   -8      -9.0    -> 1
cotx443 comparetotal   -8E+0   -9.0    -> 1
cotx444 comparetotal   -80E-1  -9.0    -> 1
cotx445 comparetotal   -.8E+1  -9      -> 1
cotx446 comparetotal   -80E-1  -9      -> 1
cotx447 comparetotal   -8.0    -9E+0   -> 1
cotx448 comparetotal   -8.0    -90E-1  -> 1
cotx449 comparetotal   -8      -.9E+1  -> 1
cotx450 comparetotal   -8      -90E-1  -> 1


-- testcases that subtract to lots of zeros at boundaries [pgr]
precision: 40
cotx470 comparetotal 123.4560000000000000E789 123.456E789 -> -1
cotx471 comparetotal 123.456000000000000E-89 123.456E-89 -> -1
cotx472 comparetotal 123.45600000000000E789 123.456E789 -> -1
cotx473 comparetotal 123.4560000000000E-89 123.456E-89 -> -1
cotx474 comparetotal 123.456000000000E789 123.456E789 -> -1
cotx475 comparetotal 123.45600000000E-89 123.456E-89 -> -1
cotx476 comparetotal 123.4560000000E789 123.456E789 -> -1
cotx477 comparetotal 123.456000000E-89 123.456E-89 -> -1
cotx478 comparetotal 123.45600000E789 123.456E789 -> -1
cotx479 comparetotal 123.4560000E-89 123.456E-89 -> -1
cotx480 comparetotal 123.456000E789 123.456E789 -> -1
cotx481 comparetotal 123.45600E-89 123.456E-89 -> -1
cotx482 comparetotal 123.4560E789 123.456E789 -> -1
cotx483 comparetotal 123.456E-89 123.456E-89 -> 0
cotx484 comparetotal 123.456E-89 123.4560000000000000E-89 -> 1
cotx485 comparetotal 123.456E789 123.456000000000000E789 -> 1
cotx486 comparetotal 123.456E-89 123.45600000000000E-89 -> 1
cotx487 comparetotal 123.456E789 123.4560000000000E789 -> 1
cotx488 comparetotal 123.456E-89 123.456000000000E-89 -> 1
cotx489 comparetotal 123.456E789 123.45600000000E789 -> 1
cotx490 comparetotal 123.456E-89 123.4560000000E-89 -> 1
cotx491 comparetotal 123.456E789 123.456000000E789 -> 1
cotx492 comparetotal 123.456E-89 123.45600000E-89 -> 1
cotx493 comparetotal 123.456E789 123.4560000E789 -> 1
cotx494 comparetotal 123.456E-89 123.456000E-89 -> 1
cotx495 comparetotal 123.456E789 123.45600E789 -> 1
cotx496 comparetotal 123.456E-89 123.4560E-89 -> 1
cotx497 comparetotal 123.456E789 123.456E789 -> 0

-- wide-ranging, around precision; signs equal
precision: 9
cotx500 comparetotal    1     1E-15    -> 1
cotx501 comparetotal    1     1E-14    -> 1
cotx502 comparetotal    1     1E-13    -> 1
cotx503 comparetotal    1     1E-12    -> 1
cotx504 comparetotal    1     1E-11    -> 1
cotx505 comparetotal    1     1E-10    -> 1
cotx506 comparetotal    1     1E-9     -> 1
cotx507 comparetotal    1     1E-8     -> 1
cotx508 comparetotal    1     1E-7     -> 1
cotx509 comparetotal    1     1E-6     -> 1
cotx510 comparetotal    1     1E-5     -> 1
cotx511 comparetotal    1     1E-4     -> 1
cotx512 comparetotal    1     1E-3     -> 1
cotx513 comparetotal    1     1E-2     -> 1
cotx514 comparetotal    1     1E-1     -> 1
cotx515 comparetotal    1     1E-0     -> 0
cotx516 comparetotal    1     1E+1     -> -1
cotx517 comparetotal    1     1E+2     -> -1
cotx518 comparetotal    1     1E+3     -> -1
cotx519 comparetotal    1     1E+4     -> -1
cotx521 comparetotal    1     1E+5     -> -1
cotx522 comparetotal    1     1E+6     -> -1
cotx523 comparetotal    1     1E+7     -> -1
cotx524 comparetotal    1     1E+8     -> -1
cotx525 comparetotal    1     1E+9     -> -1
cotx526 comparetotal    1     1E+10    -> -1
cotx527 comparetotal    1     1E+11    -> -1
cotx528 comparetotal    1     1E+12    -> -1
cotx529 comparetotal    1     1E+13    -> -1
cotx530 comparetotal    1     1E+14    -> -1
cotx531 comparetotal    1     1E+15    -> -1
-- LR swap
cotx540 comparetotal    1E-15  1       -> -1
cotx541 comparetotal    1E-14  1       -> -1
cotx542 comparetotal    1E-13  1       -> -1
cotx543 comparetotal    1E-12  1       -> -1
cotx544 comparetotal    1E-11  1       -> -1
cotx545 comparetotal    1E-10  1       -> -1
cotx546 comparetotal    1E-9   1       -> -1
cotx547 comparetotal    1E-8   1       -> -1
cotx548 comparetotal    1E-7   1       -> -1
cotx549 comparetotal    1E-6   1       -> -1
cotx550 comparetotal    1E-5   1       -> -1
cotx551 comparetotal    1E-4   1       -> -1
cotx552 comparetotal    1E-3   1       -> -1
cotx553 comparetotal    1E-2   1       -> -1
cotx554 comparetotal    1E-1   1       -> -1
cotx555 comparetotal    1E-0   1       ->  0
cotx556 comparetotal    1E+1   1       ->  1
cotx557 comparetotal    1E+2   1       ->  1
cotx558 comparetotal    1E+3   1       ->  1
cotx559 comparetotal    1E+4   1       ->  1
cotx561 comparetotal    1E+5   1       ->  1
cotx562 comparetotal    1E+6   1       ->  1
cotx563 comparetotal    1E+7   1       ->  1
cotx564 comparetotal    1E+8   1       ->  1
cotx565 comparetotal    1E+9   1       ->  1
cotx566 comparetotal    1E+10  1       ->  1
cotx567 comparetotal    1E+11  1       ->  1
cotx568 comparetotal    1E+12  1       ->  1
cotx569 comparetotal    1E+13  1       ->  1
cotx570 comparetotal    1E+14  1       ->  1
cotx571 comparetotal    1E+15  1       ->  1
-- similar with an useful coefficient, one side only
cotx580 comparetotal  0.000000987654321     1E-15    -> 1
cotx581 comparetotal  0.000000987654321     1E-14    -> 1
cotx582 comparetotal  0.000000987654321     1E-13    -> 1
cotx583 comparetotal  0.000000987654321     1E-12    -> 1
cotx584 comparetotal  0.000000987654321     1E-11    -> 1
cotx585 comparetotal  0.000000987654321     1E-10    -> 1
cotx586 comparetotal  0.000000987654321     1E-9     -> 1
cotx587 comparetotal  0.000000987654321     1E-8     -> 1
cotx588 comparetotal  0.000000987654321     1E-7     -> 1
cotx589 comparetotal  0.000000987654321     1E-6     -> -1
cotx590 comparetotal  0.000000987654321     1E-5     -> -1
cotx591 comparetotal  0.000000987654321     1E-4     -> -1
cotx592 comparetotal  0.000000987654321     1E-3     -> -1
cotx593 comparetotal  0.000000987654321     1E-2     -> -1
cotx594 comparetotal  0.000000987654321     1E-1     -> -1
cotx595 comparetotal  0.000000987654321     1E-0     -> -1
cotx596 comparetotal  0.000000987654321     1E+1     -> -1
cotx597 comparetotal  0.000000987654321     1E+2     -> -1
cotx598 comparetotal  0.000000987654321     1E+3     -> -1
cotx599 comparetotal  0.000000987654321     1E+4     -> -1

-- check some unit-y traps
precision: 20
cotx600 comparetotal   12            12.2345 -> -1
cotx601 comparetotal   12.0          12.2345 -> -1
cotx602 comparetotal   12.00         12.2345 -> -1
cotx603 comparetotal   12.000        12.2345 -> -1
cotx604 comparetotal   12.0000       12.2345 -> -1
cotx605 comparetotal   12.00000      12.2345 -> -1
cotx606 comparetotal   12.000000     12.2345 -> -1
cotx607 comparetotal   12.0000000    12.2345 -> -1
cotx608 comparetotal   12.00000000   12.2345 -> -1
cotx609 comparetotal   12.000000000  12.2345 -> -1
cotx610 comparetotal   12.1234 12            ->  1
cotx611 comparetotal   12.1234 12.0          ->  1
cotx612 comparetotal   12.1234 12.00         ->  1
cotx613 comparetotal   12.1234 12.000        ->  1
cotx614 comparetotal   12.1234 12.0000       ->  1
cotx615 comparetotal   12.1234 12.00000      ->  1
cotx616 comparetotal   12.1234 12.000000     ->  1
cotx617 comparetotal   12.1234 12.0000000    ->  1
cotx618 comparetotal   12.1234 12.00000000   ->  1
cotx619 comparetotal   12.1234 12.000000000  ->  1
cotx620 comparetotal  -12           -12.2345 ->  1
cotx621 comparetotal  -12.0         -12.2345 ->  1
cotx622 comparetotal  -12.00        -12.2345 ->  1
cotx623 comparetotal  -12.000       -12.2345 ->  1
cotx624 comparetotal  -12.0000      -12.2345 ->  1
cotx625 comparetotal  -12.00000     -12.2345 ->  1
cotx626 comparetotal  -12.000000    -12.2345 ->  1
cotx627 comparetotal  -12.0000000   -12.2345 ->  1
cotx628 comparetotal  -12.00000000  -12.2345 ->  1
cotx629 comparetotal  -12.000000000 -12.2345 ->  1
cotx630 comparetotal  -12.1234 -12           -> -1
cotx631 comparetotal  -12.1234 -12.0         -> -1
cotx632 comparetotal  -12.1234 -12.00        -> -1
cotx633 comparetotal  -12.1234 -12.000       -> -1
cotx634 comparetotal  -12.1234 -12.0000      -> -1
cotx635 comparetotal  -12.1234 -12.00000     -> -1
cotx636 comparetotal  -12.1234 -12.000000    -> -1
cotx637 comparetotal  -12.1234 -12.0000000   -> -1
cotx638 comparetotal  -12.1234 -12.00000000  -> -1
cotx639 comparetotal  -12.1234 -12.000000000 -> -1
precision: 9

-- extended zeros
cotx640 comparetotal   0     0   -> 0
cotx641 comparetotal   0    -0   -> 1
cotx642 comparetotal   0    -0.0 -> 1
cotx643 comparetotal   0     0.0 -> 1
cotx644 comparetotal  -0     0   -> -1
cotx645 comparetotal  -0    -0   -> 0
cotx646 comparetotal  -0    -0.0 -> -1
cotx647 comparetotal  -0     0.0 -> -1
cotx648 comparetotal   0.0   0   -> -1
cotx649 comparetotal   0.0  -0   -> 1
cotx650 comparetotal   0.0  -0.0 -> 1
cotx651 comparetotal   0.0   0.0 -> 0
cotx652 comparetotal  -0.0   0   -> -1
cotx653 comparetotal  -0.0  -0   -> 1
cotx654 comparetotal  -0.0  -0.0 -> 0
cotx655 comparetotal  -0.0   0.0 -> -1

cotx656 comparetotal  -0E1   0.0 -> -1
cotx657 comparetotal  -0E2   0.0 -> -1
cotx658 comparetotal   0E1   0.0 -> 1
cotx659 comparetotal   0E2   0.0 -> 1
cotx660 comparetotal  -0E1   0   -> -1
cotx661 comparetotal  -0E2   0   -> -1
cotx662 comparetotal   0E1   0   -> 1
cotx663 comparetotal   0E2   0   -> 1
cotx664 comparetotal  -0E1  -0E1 -> 0
cotx665 comparetotal  -0E2  -0E1 -> -1
cotx666 comparetotal   0E1  -0E1 -> 1
cotx667 comparetotal   0E2  -0E1 -> 1
cotx668 comparetotal  -0E1  -0E2 -> 1
cotx669 comparetotal  -0E2  -0E2 -> 0
cotx670 comparetotal   0E1  -0E2 -> 1
cotx671 comparetotal   0E2  -0E2 -> 1
cotx672 comparetotal  -0E1   0E1 -> -1
cotx673 comparetotal  -0E2   0E1 -> -1
cotx674 comparetotal   0E1   0E1 -> 0
cotx675 comparetotal   0E2   0E1 -> 1
cotx676 comparetotal  -0E1   0E2 -> -1
cotx677 comparetotal  -0E2   0E2 -> -1
cotx678 comparetotal   0E1   0E2 -> -1
cotx679 comparetotal   0E2   0E2 -> 0

-- trailing zeros; unit-y
precision: 20
cotx680 comparetotal   12    12           -> 0
cotx681 comparetotal   12    12.0         -> 1
cotx682 comparetotal   12    12.00        -> 1
cotx683 comparetotal   12    12.000       -> 1
cotx684 comparetotal   12    12.0000      -> 1
cotx685 comparetotal   12    12.00000     -> 1
cotx686 comparetotal   12    12.000000    -> 1
cotx687 comparetotal   12    12.0000000   -> 1
cotx688 comparetotal   12    12.00000000  -> 1
cotx689 comparetotal   12    12.000000000 -> 1
cotx690 comparetotal   12              12 -> 0
cotx691 comparetotal   12.0            12 -> -1
cotx692 comparetotal   12.00           12 -> -1
cotx693 comparetotal   12.000          12 -> -1
cotx694 comparetotal   12.0000         12 -> -1
cotx695 comparetotal   12.00000        12 -> -1
cotx696 comparetotal   12.000000       12 -> -1
cotx697 comparetotal   12.0000000      12 -> -1
cotx698 comparetotal   12.00000000     12 -> -1
cotx699 comparetotal   12.000000000    12 -> -1

-- long operand checks
maxexponent: 999
minexponent: -999
precision: 9
cotx701 comparetotal 12345678000  1 ->  1
cotx702 comparetotal 1 12345678000  -> -1
cotx703 comparetotal 1234567800   1 ->  1
cotx704 comparetotal 1 1234567800   -> -1
cotx705 comparetotal 1234567890   1 ->  1
cotx706 comparetotal 1 1234567890   -> -1
cotx707 comparetotal 1234567891   1 ->  1
cotx708 comparetotal 1 1234567891   -> -1
cotx709 comparetotal 12345678901  1 ->  1
cotx710 comparetotal 1 12345678901  -> -1
cotx711 comparetotal 1234567896   1 ->  1
cotx712 comparetotal 1 1234567896   -> -1
cotx713 comparetotal -1234567891  1 -> -1
cotx714 comparetotal 1 -1234567891  ->  1
cotx715 comparetotal -12345678901 1 -> -1
cotx716 comparetotal 1 -12345678901 ->  1
cotx717 comparetotal -1234567896  1 -> -1
cotx718 comparetotal 1 -1234567896  ->  1

precision: 15
-- same with plenty of precision
cotx721 comparetotal 12345678000 1 -> 1
cotx722 comparetotal 1 12345678000 -> -1
cotx723 comparetotal 1234567800  1 -> 1
cotx724 comparetotal 1 1234567800  -> -1
cotx725 comparetotal 1234567890  1 -> 1
cotx726 comparetotal 1 1234567890  -> -1
cotx727 comparetotal 1234567891  1 -> 1
cotx728 comparetotal 1 1234567891  -> -1
cotx729 comparetotal 12345678901 1 -> 1
cotx730 comparetotal 1 12345678901 -> -1
cotx731 comparetotal 1234567896  1 -> 1
cotx732 comparetotal 1 1234567896  -> -1

-- residue cases
precision: 5
cotx740 comparetotal  1  0.9999999  -> 1
cotx741 comparetotal  1  0.999999   -> 1
cotx742 comparetotal  1  0.99999    -> 1
cotx743 comparetotal  1  1.0000     -> 1
cotx744 comparetotal  1  1.00001    -> -1
cotx745 comparetotal  1  1.000001   -> -1
cotx746 comparetotal  1  1.0000001  -> -1
cotx750 comparetotal  0.9999999  1  -> -1
cotx751 comparetotal  0.999999   1  -> -1
cotx752 comparetotal  0.99999    1  -> -1
cotx753 comparetotal  1.0000     1  -> -1
cotx754 comparetotal  1.00001    1  -> 1
cotx755 comparetotal  1.000001   1  -> 1
cotx756 comparetotal  1.0000001  1  -> 1

-- a selection of longies
cotx760 comparetotal -36852134.84194296250843579428931 -5830629.8347085025808756560357940 -> -1
cotx761 comparetotal -36852134.84194296250843579428931 -36852134.84194296250843579428931  -> 0
cotx762 comparetotal -36852134.94194296250843579428931 -36852134.84194296250843579428931  -> -1
cotx763 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
-- precisions above or below the difference should have no effect
precision:   11
cotx764 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:   10
cotx765 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    9
cotx766 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    8
cotx767 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    7
cotx768 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    6
cotx769 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    5
cotx770 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    4
cotx771 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    3
cotx772 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    2
cotx773 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    1
cotx774 comparetotal -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1

-- Specials
precision:   9
cotx780 comparetotal  Inf  -Inf   ->  1
cotx781 comparetotal  Inf  -1000  ->  1
cotx782 comparetotal  Inf  -1     ->  1
cotx783 comparetotal  Inf  -0     ->  1
cotx784 comparetotal  Inf   0     ->  1
cotx785 comparetotal  Inf   1     ->  1
cotx786 comparetotal  Inf   1000  ->  1
cotx787 comparetotal  Inf   Inf   ->  0
cotx788 comparetotal -1000  Inf   -> -1
cotx789 comparetotal -Inf   Inf   -> -1
cotx790 comparetotal -1     Inf   -> -1
cotx791 comparetotal -0     Inf   -> -1
cotx792 comparetotal  0     Inf   -> -1
cotx793 comparetotal  1     Inf   -> -1
cotx794 comparetotal  1000  Inf   -> -1
cotx795 comparetotal  Inf   Inf   ->  0

cotx800 comparetotal -Inf  -Inf   ->  0
cotx801 comparetotal -Inf  -1000  -> -1
cotx802 comparetotal -Inf  -1     -> -1
cotx803 comparetotal -Inf  -0     -> -1
cotx804 comparetotal -Inf   0     -> -1
cotx805 comparetotal -Inf   1     -> -1
cotx806 comparetotal -Inf   1000  -> -1
cotx807 comparetotal -Inf   Inf   -> -1
cotx808 comparetotal -Inf  -Inf   ->  0
cotx809 comparetotal -1000 -Inf   ->  1
cotx810 comparetotal -1    -Inf   ->  1
cotx811 comparetotal -0    -Inf   ->  1
cotx812 comparetotal  0    -Inf   ->  1
cotx813 comparetotal  1    -Inf   ->  1
cotx814 comparetotal  1000 -Inf   ->  1
cotx815 comparetotal  Inf  -Inf   ->  1

cotx821 comparetotal  NaN -Inf    ->  1
cotx822 comparetotal  NaN -1000   ->  1
cotx823 comparetotal  NaN -1      ->  1
cotx824 comparetotal  NaN -0      ->  1
cotx825 comparetotal  NaN  0      ->  1
cotx826 comparetotal  NaN  1      ->  1
cotx827 comparetotal  NaN  1000   ->  1
cotx828 comparetotal  NaN  Inf    ->  1
cotx829 comparetotal  NaN  NaN    ->  0
cotx830 comparetotal -Inf  NaN    ->  -1
cotx831 comparetotal -1000 NaN    ->  -1
cotx832 comparetotal -1    NaN    ->  -1
cotx833 comparetotal -0    NaN    ->  -1
cotx834 comparetotal  0    NaN    ->  -1
cotx835 comparetotal  1    NaN    ->  -1
cotx836 comparetotal  1000 NaN    ->  -1
cotx837 comparetotal  Inf  NaN    ->  -1
cotx838 comparetotal -NaN -NaN    ->  0
cotx839 comparetotal +NaN -NaN    ->  1
cotx840 comparetotal -NaN +NaN    ->  -1

cotx841 comparetotal  sNaN -sNaN  ->  1
cotx842 comparetotal  sNaN -NaN   ->  1
cotx843 comparetotal  sNaN -Inf   ->  1
cotx844 comparetotal  sNaN -1000  ->  1
cotx845 comparetotal  sNaN -1     ->  1
cotx846 comparetotal  sNaN -0     ->  1
cotx847 comparetotal  sNaN  0     ->  1
cotx848 comparetotal  sNaN  1     ->  1
cotx849 comparetotal  sNaN  1000  ->  1
cotx850 comparetotal  sNaN  NaN   ->  -1
cotx851 comparetotal  sNaN sNaN   ->  0

cotx852 comparetotal -sNaN sNaN   ->  -1
cotx853 comparetotal -NaN  sNaN   ->  -1
cotx854 comparetotal -Inf  sNaN   ->  -1
cotx855 comparetotal -1000 sNaN   ->  -1
cotx856 comparetotal -1    sNaN   ->  -1
cotx857 comparetotal -0    sNaN   ->  -1
cotx858 comparetotal  0    sNaN   ->  -1
cotx859 comparetotal  1    sNaN   ->  -1
cotx860 comparetotal  1000 sNaN   ->  -1
cotx861 comparetotal  Inf  sNaN   ->  -1
cotx862 comparetotal  NaN  sNaN   ->  1
cotx863 comparetotal  sNaN sNaN   ->  0

cotx871 comparetotal  -sNaN -sNaN  ->  0
cotx872 comparetotal  -sNaN -NaN   ->  1
cotx873 comparetotal  -sNaN -Inf   ->  -1
cotx874 comparetotal  -sNaN -1000  ->  -1
cotx875 comparetotal  -sNaN -1     ->  -1
cotx876 comparetotal  -sNaN -0     ->  -1
cotx877 comparetotal  -sNaN  0     ->  -1
cotx878 comparetotal  -sNaN  1     ->  -1
cotx879 comparetotal  -sNaN  1000  ->  -1
cotx880 comparetotal  -sNaN  NaN   ->  -1
cotx881 comparetotal  -sNaN sNaN   ->  -1

cotx882 comparetotal -sNaN -sNaN   ->  0
cotx883 comparetotal -NaN  -sNaN   ->  -1
cotx884 comparetotal -Inf  -sNaN   ->  1
cotx885 comparetotal -1000 -sNaN   ->  1
cotx886 comparetotal -1    -sNaN   ->  1
cotx887 comparetotal -0    -sNaN   ->  1
cotx888 comparetotal  0    -sNaN   ->  1
cotx889 comparetotal  1    -sNaN   ->  1
cotx890 comparetotal  1000 -sNaN   ->  1
cotx891 comparetotal  Inf  -sNaN   ->  1
cotx892 comparetotal  NaN  -sNaN   ->  1
cotx893 comparetotal  sNaN -sNaN   ->  1

-- NaNs with payload
cotx960 comparetotal  NaN9 -Inf   ->  1
cotx961 comparetotal  NaN8  999   ->  1
cotx962 comparetotal  NaN77 Inf   ->  1
cotx963 comparetotal -NaN67 NaN5  ->  -1
cotx964 comparetotal -Inf  -NaN4  ->  1
cotx965 comparetotal -999  -NaN33 ->  1
cotx966 comparetotal  Inf   NaN2  ->  -1

cotx970 comparetotal -NaN41 -NaN42 -> 1
cotx971 comparetotal +NaN41 -NaN42 -> 1
cotx972 comparetotal -NaN41 +NaN42 -> -1
cotx973 comparetotal +NaN41 +NaN42 -> -1
cotx974 comparetotal -NaN42 -NaN01 -> -1
cotx975 comparetotal +NaN42 -NaN01 ->  1
cotx976 comparetotal -NaN42 +NaN01 -> -1
cotx977 comparetotal +NaN42 +NaN01 ->  1

cotx980 comparetotal -sNaN771 -sNaN772 -> 1
cotx981 comparetotal +sNaN771 -sNaN772 -> 1
cotx982 comparetotal -sNaN771 +sNaN772 -> -1
cotx983 comparetotal +sNaN771 +sNaN772 -> -1
cotx984 comparetotal -sNaN772 -sNaN771 -> -1
cotx985 comparetotal +sNaN772 -sNaN771 ->  1
cotx986 comparetotal -sNaN772 +sNaN771 -> -1
cotx987 comparetotal +sNaN772 +sNaN771 ->  1

cotx991 comparetotal -sNaN99 -Inf    -> -1
cotx992 comparetotal  sNaN98 -11     ->  1
cotx993 comparetotal  sNaN97  NaN    -> -1
cotx994 comparetotal  sNaN16 sNaN94  -> -1
cotx995 comparetotal  NaN85  sNaN83  ->  1
cotx996 comparetotal -Inf    sNaN92  -> -1
cotx997 comparetotal  088    sNaN81  -> -1
cotx998 comparetotal  Inf    sNaN90  -> -1
cotx999 comparetotal  NaN   -sNaN89  ->  1

-- overflow and underflow tests .. subnormal results now allowed
maxExponent: 999999999
minexponent: -999999999
cotx1080 comparetotal +1.23456789012345E-0 9E+999999999 -> -1
cotx1081 comparetotal 9E+999999999 +1.23456789012345E-0 ->  1
cotx1082 comparetotal +0.100 9E-999999999               ->  1
cotx1083 comparetotal 9E-999999999 +0.100               -> -1
cotx1085 comparetotal -1.23456789012345E-0 9E+999999999 -> -1
cotx1086 comparetotal 9E+999999999 -1.23456789012345E-0 ->  1
cotx1087 comparetotal -0.100 9E-999999999               -> -1
cotx1088 comparetotal 9E-999999999 -0.100               ->  1

cotx1089 comparetotal 1e-599999999 1e-400000001   -> -1
cotx1090 comparetotal 1e-599999999 1e-400000000   -> -1
cotx1091 comparetotal 1e-600000000 1e-400000000   -> -1
cotx1092 comparetotal 9e-999999998 0.01           -> -1
cotx1093 comparetotal 9e-999999998 0.1            -> -1
cotx1094 comparetotal 0.01 9e-999999998           ->  1
cotx1095 comparetotal 1e599999999 1e400000001     ->  1
cotx1096 comparetotal 1e599999999 1e400000000     ->  1
cotx1097 comparetotal 1e600000000 1e400000000     ->  1
cotx1098 comparetotal 9e999999998 100             ->  1
cotx1099 comparetotal 9e999999998 10              ->  1
cotx1100 comparetotal 100  9e999999998            -> -1
-- signs
cotx1101 comparetotal  1e+777777777  1e+411111111 ->  1
cotx1102 comparetotal  1e+777777777 -1e+411111111 ->  1
cotx1103 comparetotal -1e+777777777  1e+411111111 -> -1
cotx1104 comparetotal -1e+777777777 -1e+411111111 -> -1
cotx1105 comparetotal  1e-777777777  1e-411111111 -> -1
cotx1106 comparetotal  1e-777777777 -1e-411111111 ->  1
cotx1107 comparetotal -1e-777777777  1e-411111111 -> -1
cotx1108 comparetotal -1e-777777777 -1e-411111111 ->  1

-- spread zeros
cotx1110 comparetotal   0E-383  0       -> -1
cotx1111 comparetotal   0E-383 -0       ->  1
cotx1112 comparetotal  -0E-383  0       -> -1
cotx1113 comparetotal  -0E-383 -0       ->  1
cotx1114 comparetotal   0E-383  0E+384  -> -1
cotx1115 comparetotal   0E-383 -0E+384  ->  1
cotx1116 comparetotal  -0E-383  0E+384  -> -1
cotx1117 comparetotal  -0E-383 -0E+384  ->  1
cotx1118 comparetotal   0       0E+384  -> -1
cotx1119 comparetotal   0      -0E+384  ->  1
cotx1120 comparetotal  -0       0E+384  -> -1
cotx1121 comparetotal  -0      -0E+384  ->  1

cotx1130 comparetotal   0E+384  0       ->  1
cotx1131 comparetotal   0E+384 -0       ->  1
cotx1132 comparetotal  -0E+384  0       -> -1
cotx1133 comparetotal  -0E+384 -0       -> -1
cotx1134 comparetotal   0E+384  0E-383  ->  1
cotx1135 comparetotal   0E+384 -0E-383  ->  1
cotx1136 comparetotal  -0E+384  0E-383  -> -1
cotx1137 comparetotal  -0E+384 -0E-383  -> -1
cotx1138 comparetotal   0       0E-383  ->  1
cotx1139 comparetotal   0      -0E-383  ->  1
cotx1140 comparetotal  -0       0E-383  -> -1
cotx1141 comparetotal  -0      -0E-383  -> -1

-- Null tests
cotx9990 comparetotal 10  # -> NaN Invalid_operation
cotx9991 comparetotal  # 10 -> NaN Invalid_operation
