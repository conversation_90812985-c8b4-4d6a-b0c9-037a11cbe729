## Syntax highlighting for AWK scripts.

## Original author:  <PERSON><PERSON>

syntax awk "\.awk$"
header "^#!.*awk"
magic "awk script"
comment "#"

# Records.
color brightred "\$[[:alnum:]_!@#$*?-]+"
# Awk-set variables.
color red "\<(ARGC|ARGIND|ARGV|BINMODE|CONVFMT|ENVIRON|ERRNO|FIELDWIDTHS)\>"
color red "\<(FILENAME|FNR|FS|IGNORECASE|LINT|NF|NR|OFMT|OFS|ORS)\>"
color red "\<(PROCINFO|RS|RT|RSTART|RLENGTH|SUBSEP|TEXTDOMAIN)\>"
# Function declarations and special patterns.
color brightgreen "\<(function|extension|BEGIN|END)\>"
# Operators and brackets/braces/slashes/parentheses.
color green "[][!%&()*+/:;<=>?\^{|}~-]"
# Flow control.
color brightyellow "\<(for|if|while|do|else|in|delete|exit)\>"
color brightyellow "\<(break|continue|return)\>"
# I/O statements.
color brightgreen "\<(close|fflush|getline|next|nextfile|print|printf|system)\>"
# Standard functions.
color magenta "\<(atan2|cos|exp|int|log|rand|sin|sqrt|srand)\>"
color magenta "\<(asort|asorti|gensub|gsub|index|length|match)\>"
color magenta "\<(split|sprintf|strtonum|sub|substr|tolower|toupper)\>"
color magenta "\<(mktime|strftime|systime)\>"
color magenta "\<(and|compl|lshift|or|rshift|xor)\>"
color magenta "\<(bindtextdomain|dcgettext|dcngettext)\>"

# Strings.
color brightyellow ""([^"\]|\\.)*""
# Escape sequences.
color brick "\\([abfnrtv\/"]|[0-7]{1,3}|x[[:xdigit:]]{1,2}|u[[:xdigit:]]{1,8})"
# Comments.
color brightblue "(^|[[:blank:]])#.*"

# Trailing whitespace.
color ,green "[[:space:]]+$"
