.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_get_pk_algorithm" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_get_pk_algorithm \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_get_pk_algorithm(gnutls_privkey_t " key ", unsigned int * " bits ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
should contain a \fBgnutls_privkey_t\fP type
.IP "unsigned int * bits" 12
If set will return the number of bits of the parameters (may be NULL)
.SH "DESCRIPTION"
This function will return the public key algorithm of a private
key and if possible will return a number of bits that indicates
the security parameter of the key.
.SH "RETURNS"
a member of the \fBgnutls_pk_algorithm_t\fP enumeration on
success, or a negative error code on error.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
