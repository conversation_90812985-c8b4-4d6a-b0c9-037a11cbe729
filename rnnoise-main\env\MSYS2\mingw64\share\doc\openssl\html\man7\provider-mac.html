<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>provider-mac</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Context-Management-Functions">Context Management Functions</a></li>
      <li><a href="#Encryption-Decryption-Functions">Encryption/Decryption Functions</a></li>
      <li><a href="#Mac-Parameters">Mac Parameters</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>provider-mac - The mac library &lt;-&gt; provider functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/core_dispatch.h&gt;
#include &lt;openssl/core_names.h&gt;

/*
 * None of these are actual functions, but are displayed like this for
 * the function signatures for functions that are offered as function
 * pointers in OSSL_DISPATCH arrays.
 */

/* Context management */
void *OSSL_FUNC_mac_newctx(void *provctx);
void OSSL_FUNC_mac_freectx(void *mctx);
void *OSSL_FUNC_mac_dupctx(void *src);

/* Encryption/decryption */
int OSSL_FUNC_mac_init(void *mctx, unsigned char *key, size_t keylen,
                       const OSSL_PARAM params[]);
int OSSL_FUNC_mac_init_skey(void *mctx, const void *key, const OSSL_PARAM params[]);
int OSSL_FUNC_mac_update(void *mctx, const unsigned char *in, size_t inl);
int OSSL_FUNC_mac_final(void *mctx, unsigned char *out, size_t *outl, size_t outsize);

/* MAC parameter descriptors */
const OSSL_PARAM *OSSL_FUNC_mac_gettable_params(void *provctx);
const OSSL_PARAM *OSSL_FUNC_mac_gettable_ctx_params(void *mctx, void *provctx);
const OSSL_PARAM *OSSL_FUNC_mac_settable_ctx_params(void *mctx, void *provctx);

/* MAC parameters */
int OSSL_FUNC_mac_get_params(OSSL_PARAM params[]);
int OSSL_FUNC_mac_get_ctx_params(void *mctx, OSSL_PARAM params[]);
int OSSL_FUNC_mac_set_ctx_params(void *mctx, const OSSL_PARAM params[]);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This documentation is primarily aimed at provider authors. See <a href="../man7/provider.html">provider(7)</a> for further information.</p>

<p>The MAC operation enables providers to implement mac algorithms and make them available to applications via the API functions <a href="../man3/EVP_MAC_init.html">EVP_MAC_init(3)</a>, <a href="../man3/EVP_MAC_update.html">EVP_MAC_update(3)</a> and <a href="../man3/EVP_MAC_final.html">EVP_MAC_final(3)</a>.</p>

<p>All &quot;functions&quot; mentioned here are passed as function pointers between <i>libcrypto</i> and the provider in <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays via <a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a> arrays that are returned by the provider&#39;s provider_query_operation() function (see <a href="../man7/provider-base.html">&quot;Provider Functions&quot; in provider-base(7)</a>).</p>

<p>All these &quot;functions&quot; have a corresponding function type definition named <b>OSSL_FUNC_{name}_fn</b>, and a helper function to retrieve the function pointer from an <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> element named <b>OSSL_FUNC_{name}</b>. For example, the &quot;function&quot; OSSL_FUNC_mac_newctx() has these:</p>

<pre><code>typedef void *(OSSL_FUNC_mac_newctx_fn)(void *provctx);
static ossl_inline OSSL_FUNC_mac_newctx_fn
    OSSL_FUNC_mac_newctx(const OSSL_DISPATCH *opf);</code></pre>

<p><a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays are indexed by numbers that are provided as macros in <a href="../man7/openssl-core_dispatch.h.html">openssl-core_dispatch.h(7)</a>, as follows:</p>

<pre><code>OSSL_FUNC_mac_newctx               OSSL_FUNC_MAC_NEWCTX
OSSL_FUNC_mac_freectx              OSSL_FUNC_MAC_FREECTX
OSSL_FUNC_mac_dupctx               OSSL_FUNC_MAC_DUPCTX

OSSL_FUNC_mac_init                 OSSL_FUNC_MAC_INIT
OSSL_FUNC_mac_init_skey            OSSL_FUNC_MAC_INIT_SKEY
OSSL_FUNC_mac_update               OSSL_FUNC_MAC_UPDATE
OSSL_FUNC_mac_final                OSSL_FUNC_MAC_FINAL

OSSL_FUNC_mac_get_params           OSSL_FUNC_MAC_GET_PARAMS
OSSL_FUNC_mac_get_ctx_params       OSSL_FUNC_MAC_GET_CTX_PARAMS
OSSL_FUNC_mac_set_ctx_params       OSSL_FUNC_MAC_SET_CTX_PARAMS

OSSL_FUNC_mac_gettable_params      OSSL_FUNC_MAC_GETTABLE_PARAMS
OSSL_FUNC_mac_gettable_ctx_params  OSSL_FUNC_MAC_GETTABLE_CTX_PARAMS
OSSL_FUNC_mac_settable_ctx_params  OSSL_FUNC_MAC_SETTABLE_CTX_PARAMS</code></pre>

<p>A mac algorithm implementation may not implement all of these functions. In order to be a consistent set of functions, at least the following functions must be implemented: OSSL_FUNC_mac_newctx(), OSSL_FUNC_mac_freectx(), at least one of OSSL_FUNC_mac_init() or OSSL_FUNC_mac_init_skey(), OSSL_FUNC_mac_update(), OSSL_FUNC_mac_final(). All other functions are optional.</p>

<h2 id="Context-Management-Functions">Context Management Functions</h2>

<p>OSSL_FUNC_mac_newctx() should create and return a pointer to a provider side structure for holding context information during a mac operation. A pointer to this context will be passed back in a number of the other mac operation function calls. The parameter <i>provctx</i> is the provider context generated during provider initialisation (see <a href="../man7/provider.html">provider(7)</a>).</p>

<p>OSSL_FUNC_mac_freectx() is passed a pointer to the provider side mac context in the <i>mctx</i> parameter. If it receives NULL as <i>mctx</i> value, it should not do anything other than return. This function should free any resources associated with that context.</p>

<p>OSSL_FUNC_mac_dupctx() should duplicate the provider side mac context in the <i>mctx</i> parameter and return the duplicate copy.</p>

<h2 id="Encryption-Decryption-Functions">Encryption/Decryption Functions</h2>

<p>OSSL_FUNC_mac_init() initialises a mac operation given a newly created provider side mac context in the <i>mctx</i> parameter. The <i>params</i> are set before setting the MAC <i>key</i> of <i>keylen</i> bytes.</p>

<p>OSSL_FUNC_mac_init_skey() is similar but uses an opaque provider-specific object to initialize the MAC context.</p>

<p>OSSL_FUNC_mac_update() is called to supply data for MAC computation of a previously initialised mac operation. The <i>mctx</i> parameter contains a pointer to a previously initialised provider side context. OSSL_FUNC_mac_update() may be called multiple times for a single mac operation.</p>

<p>OSSL_FUNC_mac_final() completes the MAC computation started through previous OSSL_FUNC_mac_init() and OSSL_FUNC_mac_update() calls. The <i>mctx</i> parameter contains a pointer to the provider side context. The resulting MAC should be written to <i>out</i> and the amount of data written to <i>*outl</i>, which should not exceed <i>outsize</i> bytes. The same expectations apply to <i>outsize</i> as documented for <a href="../man3/EVP_MAC_final.html">EVP_MAC_final(3)</a>.</p>

<h2 id="Mac-Parameters">Mac Parameters</h2>

<p>See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for further details on the parameters structure used by these functions.</p>

<p>OSSL_FUNC_mac_get_params() gets details of parameter values associated with the provider algorithm and stores them in <i>params</i>.</p>

<p>OSSL_FUNC_mac_set_ctx_params() sets mac parameters associated with the given provider side mac context <i>mctx</i> to <i>params</i>. Any parameter settings are additional to any that were previously set. Passing NULL for <i>params</i> should return true.</p>

<p>OSSL_FUNC_mac_get_ctx_params() gets details of currently set parameter values associated with the given provider side mac context <i>mctx</i> and stores them in <i>params</i>. Passing NULL for <i>params</i> should return true.</p>

<p>OSSL_FUNC_mac_gettable_params(), OSSL_FUNC_mac_gettable_ctx_params(), and OSSL_FUNC_mac_settable_ctx_params() all return constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> arrays as descriptors of the parameters that OSSL_FUNC_mac_get_params(), OSSL_FUNC_mac_get_ctx_params(), and OSSL_FUNC_mac_set_ctx_params() can handle, respectively. OSSL_FUNC_mac_gettable_ctx_params() and OSSL_FUNC_mac_settable_ctx_params() will return the parameters associated with the provider side context <i>mctx</i> in its current state if it is not NULL. Otherwise, they return the parameters associated with the provider side algorithm <i>provctx</i>.</p>

<p>All MAC implementations are expected to handle the following parameters:</p>

<dl>

<dt id="with-OSSL_FUNC_set_ctx_params">with OSSL_FUNC_set_ctx_params():</dt>
<dd>

<dl>

<dt id="key-OSSL_MAC_PARAM_KEY-octet-string">&quot;key&quot; (<b>OSSL_MAC_PARAM_KEY</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the key in the associated MAC ctx. This is identical to passing a <i>key</i> argument to the OSSL_FUNC_mac_init() function.</p>

</dd>
</dl>

</dd>
<dt id="with-OSSL_FUNC_get_params">with OSSL_FUNC_get_params():</dt>
<dd>

<dl>

<dt id="size-OSSL_MAC_PARAM_SIZE-integer">&quot;size&quot; (<b>OSSL_MAC_PARAM_SIZE</b>) &lt;integer&gt;</dt>
<dd>

<p>Can be used to get the default MAC size (which might be the only allowable MAC size for the implementation).</p>

<p>Note that some implementations allow setting the size that the resulting MAC should have as well, see the documentation of the implementation.</p>

</dd>
</dl>

<dl>

<dt id="size-OSSL_MAC_PARAM_BLOCK_SIZE-integer">&quot;size&quot; (<b>OSSL_MAC_PARAM_BLOCK_SIZE</b>) &lt;integer&gt;</dt>
<dd>

<p>Can be used to get the MAC block size (if supported by the algorithm).</p>

</dd>
</dl>

</dd>
</dl>

<p>The OpenSSL FIPS provider may support the following parameters:</p>

<dl>

<dt id="fips-indicator-OSSL_MAC_PARAM_FIPS_APPROVED_INDICATOR-int">&quot;fips-indicator&quot; (<b>OSSL_MAC_PARAM_FIPS_APPROVED_INDICATOR</b>) &lt;int&gt;</dt>
<dd>

<p>A getter that returns 1 if the operation is FIPS approved, or 0 otherwise. This may be used after calling the final function. It may return 0 if either &quot;no-short-mac&quot; or &quot;key-check&quot; are set to 0.</p>

</dd>
<dt id="no-short-mac-OSSL_MAC_PARAM_FIPS_NO_SHORT_MAC-integer">&quot;no-short-mac&quot; (<b>OSSL_MAC_PARAM_FIPS_NO_SHORT_MAC</b>) &lt;integer&gt;</dt>
<dd>

<p>If required this parameter should be set early via an init function. The default value of 1 causes an error when too short MAC output is asked for. Setting this to 0 will ignore the error and set the approved &quot;fips-indicator&quot; to 0. This option breaks FIPS compliance if it causes the approved &quot;fips-indicator&quot; to return 0.</p>

</dd>
<dt id="key-check-OSSL_MAC_PARAM_FIPS_KEY_CHECK-integer">&quot;key-check&quot; (<b>OSSL_MAC_PARAM_FIPS_KEY_CHECK</b>) &lt;integer&gt;</dt>
<dd>

<p>If required this parameter should be set before OSSL_FUNC_mac_init. The default value of 1 causes an error when small key sizes are asked for. Setting this to 0 will ignore the error and set the approved &quot;fips-indicator&quot; to 0. This option breaks FIPS compliance if it causes the approved &quot;fips-indicator&quot; to return 0.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>The MAC life-cycle is described in <a href="../man7/life_cycle-rand.html">life_cycle-rand(7)</a>. Providers should ensure that the various transitions listed there are supported. At some point the EVP layer will begin enforcing the listed transitions.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_FUNC_mac_newctx() and OSSL_FUNC_mac_dupctx() should return the newly created provider side mac context, or NULL on failure.</p>

<p>OSSL_FUNC_mac_init(), OSSL_FUNC_mac_init_skey(), OSSL_FUNC_mac_update(), OSSL_FUNC_mac_final(), OSSL_FUNC_mac_get_params(), OSSL_FUNC_mac_get_ctx_params() and OSSL_FUNC_mac_set_ctx_params() should return 1 for success or 0 on error.</p>

<p>OSSL_FUNC_mac_gettable_params(), OSSL_FUNC_mac_gettable_ctx_params() and OSSL_FUNC_mac_settable_ctx_params() should return a constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array, or NULL if none is offered.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider.html">provider(7)</a>, <a href="../man7/EVP_MAC-BLAKE2.html">EVP_MAC-BLAKE2(7)</a>, <a href="../man7/EVP_MAC-CMAC.html">EVP_MAC-CMAC(7)</a>, <a href="../man7/EVP_MAC-GMAC.html">EVP_MAC-GMAC(7)</a>, <a href="../man7/EVP_MAC-HMAC.html">EVP_MAC-HMAC(7)</a>, <a href="../man7/EVP_MAC-KMAC.html">EVP_MAC-KMAC(7)</a>, <a href="../man7/EVP_MAC-Poly1305.html">EVP_MAC-Poly1305(7)</a>, <a href="../man7/EVP_MAC-Siphash.html">EVP_MAC-Siphash(7)</a>, <a href="../man7/life_cycle-mac.html">life_cycle-mac(7)</a>, <a href="../man3/EVP_MAC.html">EVP_MAC(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The provider MAC interface was introduced in OpenSSL 3.0. The parameters &quot;no-short-mac&quot; and &quot;fips-indicator&quot; were added in OpenSSL 3.4.</p>

<p>The function OSSL_FUNC_mac_init_skey() was introduced in OpenSSL 3.5.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2025 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


