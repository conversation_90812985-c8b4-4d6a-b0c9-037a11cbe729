.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ext_raw_parse" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ext_raw_parse \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_ext_raw_parse(void * " ctx ", gnutls_ext_raw_process_func " cb ", const gnutls_datum_t * " data ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "void * ctx" 12
a pointer to pass to callback function
.IP "gnutls_ext_raw_process_func cb" 12
callback function to process each extension found
.IP "const gnutls_datum_t * data" 12
TLS extension data
.IP "unsigned int flags" 12
should be zero or \fBGNUTLS_EXT_RAW_FLAG_TLS_CLIENT_HELLO\fP or \fBGNUTLS_EXT_RAW_FLAG_DTLS_CLIENT_HELLO\fP
.SH "DESCRIPTION"
This function iterates through the TLS extensions as passed in
 \fIdata\fP , passing the individual extension data to callback. The
 \fIdata\fP must conform to Extension extensions<0..2^16\-1> format.

If flags is \fBGNUTLS_EXT_RAW_TLS_FLAG_CLIENT_HELLO\fP then this function
will parse the extension data from the position, as if the packet in
 \fIdata\fP is a client hello (without record or handshake headers) \-
as provided by \fBgnutls_handshake_set_hook_function()\fP.

The return value of the callback will be propagated.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code. On unknown
flags it returns \fBGNUTLS_E_INVALID_REQUEST\fP.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
