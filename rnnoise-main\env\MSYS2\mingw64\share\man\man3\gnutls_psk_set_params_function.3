.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_set_params_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_set_params_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_psk_set_params_function(gnutls_psk_server_credentials_t " res ", gnutls_params_function * " func ");"
.SH ARGUMENTS
.IP "gnutls_psk_server_credentials_t res" 12
is a gnutls_psk_server_credentials_t type
.IP "gnutls_params_function * func" 12
is the function to be called
.SH "DESCRIPTION"
This function will set a callback in order for the server to get
the Di<PERSON>ie\-<PERSON>man or RSA parameters for PSK authentication.  The
callback should return \fBGNUTLS_E_SUCCESS\fP (0) on success.
.SH "DEPRECATED"
This function is unnecessary and discouraged on GnuTLS 3.6.0
or later. Since 3.6.0, DH parameters are negotiated
following RFC7919.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
