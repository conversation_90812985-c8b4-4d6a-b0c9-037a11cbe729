.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_token_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_token_init \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_token_init(const char * " token_url ", const char * " so_pin ", const char * " label ");"
.SH ARGUMENTS
.IP "const char * token_url" 12
A PKCS \fB11\fP URL specifying a token
.IP "const char * so_pin" 12
Security Officer's PIN
.IP "const char * label" 12
A name to be used for the token
.SH "DESCRIPTION"
This function will initialize (format) a token. If the token is
at a factory defaults state the security officer's PIN given will be
set to be the default. Otherwise it should match the officer's PIN.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
