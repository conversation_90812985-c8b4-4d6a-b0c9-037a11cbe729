.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_verify" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_verify \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_verify(gnutls_x509_crl_t " crl ", const gnutls_x509_crt_t * " trusted_cas ", unsigned " tcas_size ", unsigned int " flags ", unsigned int * " verify ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
is the crl to be verified
.IP "const gnutls_x509_crt_t * trusted_cas" 12
is a certificate list that is considered to be trusted one
.IP "unsigned tcas_size" 12
holds the number of CA certificates in CA_list
.IP "unsigned int flags" 12
Flags that may be used to change the verification algorithm. Use OR of the gnutls_certificate_verify_flags enumerations.
.IP "unsigned int * verify" 12
will hold the crl verification output.
.SH "DESCRIPTION"
This function will try to verify the given crl and return its verification status.
See \fBgnutls_x509_crt_list_verify()\fP for a detailed description of
return values. Note that since GnuTLS 3.1.4 this function includes
the time checks.

Note that value in  \fIverify\fP is set only when the return value of this 
function is success (i.e, failure to trust a CRL a certificate does not imply 
a negative return value).

Before GnuTLS 3.5.7 this function would return zero or a positive
number on success.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0), otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
