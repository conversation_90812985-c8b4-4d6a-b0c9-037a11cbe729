COMPILE_PDB_OUTPUT_DIRECTORY_<CONFIG>
-------------------------------------

.. versionadded:: 3.1

Per-configuration output directory for the MS debug symbol ``.pdb`` file
generated by the compiler while building source files.

This is a per-configuration version of
:prop_tgt:`COMPILE_PDB_OUTPUT_DIRECTORY`,
but multi-configuration generators (Visual Studio, Xcode) do NOT append a
per-configuration subdirectory to the specified directory.  This
property is initialized by the value of the
:variable:`CMAKE_COMPILE_PDB_OUTPUT_DIRECTORY_<CONFIG>` variable
if it is set when a target is created.

.. |PDB_XXX| replace:: :prop_tgt:`PDB_OUTPUT_DIRECTORY_<CONFIG>`
.. include:: COMPILE_PDB_NOTE.txt
