.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_rdn_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_rdn_get \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_rdn_get(const gnutls_datum_t * " idn ", char * " buf ", size_t * " buf_size ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * idn" 12
should contain a DER encoded RDN sequence
.IP "char * buf" 12
a pointer to a structure to hold the peer's name
.IP "size_t * buf_size" 12
holds the size of  \fIbuf\fP 
.SH "DESCRIPTION"
This function will return the name of the given RDN sequence.  The
name will be in the form "C=xxxx,O=yyyy,CN=zzzz" as described in
RFC4514.

This function does not output a fully RFC4514 compliant string, if
that is required see \fBgnutls_x509_rdn_get2()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, or
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP is returned and * \fIbuf_size\fP is
updated if the provided buffer is not long enough, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
