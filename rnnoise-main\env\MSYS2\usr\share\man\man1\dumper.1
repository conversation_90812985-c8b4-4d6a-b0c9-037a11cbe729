'\" t
.\"     Title: dumper
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/18/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "DUMPER" "1" "06/18/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dumper \- Dump core from WIN32PID to FILENAME\&.core
.SH "SYNOPSIS"
.HP \w'\fBdumper\fR\ 'u
\fBdumper\fR [\-n] [\-d] [\-q] \fIFILENAME\fR \fIWIN32PID\fR
.HP \w'\fBdumper\fR\ 'u
\fBdumper\fR \-h | \-V 
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
\-n, \-\-nokill   don\*(Aqt terminate the dumped process
\-d, \-\-verbose  be verbose while dumping
\-h, \-\-help     output help information and exit
\-q, \-\-quiet    be quiet while dumping (default)
\-V, \-\-version  output version information and exit
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
The
\fBdumper\fR
utility can be used to create a core dump of running Windows process\&. This core dump can be later loaded into
\fBgdb\fR
and analyzed\&.
.PP
If the core file size limit is set to unlimited (e\&.g\&.
\fBulimit \-c unlimited\fR) and an
error_start
executable hasn\*(Aqt been configured in the
CYGWIN
environment variable, Cygwin will automatically run
\fBdumper\fR
when a fatal error occurs\&.
.PP
\fBdumper\fR
can be also be started from the command line to create a core dump of any running process\&.
.PP
For historical reasons, unless the
\-n
option is given, after the core dump is created and when the
\fBdumper\fR
exits, the target process is also terminated\&.
.PP
To save space in the core dump,
\fBdumper\fR
doesn\*(Aqt write those portions of the target process\*(Aqs memory space that were loaded from executable and dll files and are unchanged (e\&.g\&. program code)\&. Instead,
\fBdumper\fR
saves paths to the files which contain that data\&. When a core dump is loaded into gdb, it uses these paths to load the appropriate files\&. That means that if you create a core dump on one machine and try to debug it on another, you\*(Aqll need to place identical copies of the executable and dlls in the same directories as on the machine where the core dump was created\&.
.SH "NOTES"
.PP
A Cygwin "core dump file" is an ELF file containing the mutable parts of the process memory and special note sections which capture the process, thread and loaded module context needed to recreate the process image in a debugger\&.
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
