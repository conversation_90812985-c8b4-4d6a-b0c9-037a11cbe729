set ::msgcat::header "Project-Id-Version: git-gui\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2015-10-20 14:20+0200\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: German\nLanguage: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset de "Couldn't get list of unmerged files:" "Liste der nicht zusammengef\u00fchrten Dateien nicht gefunden:"
::msgcat::mcset de "Color words" "W\u00f6rter einf\u00e4rben"
::msgcat::mcset de "Markup words" "W\u00f6rter kennzeichnen"
::msgcat::mcset de "Error parsing revisions:" "Fehler beim Laden der Versionen:"
::msgcat::mcset de "Error executing --argscmd command:" "Fehler beim Ausf\u00fchren des --argscmd-Kommandos:"
::msgcat::mcset de "No files selected: --merge specified but no files are unmerged." "Keine Dateien ausgew\u00e4hlt: Es wurde --merge angegeben, aber es existieren keine nicht zusammengef\u00fchrten Dateien."
::msgcat::mcset de "No files selected: --merge specified but no unmerged files are within file limit." "Keine Dateien ausgew\u00e4hlt: Es wurde --merge angegeben, aber es sind keine nicht zusammengef\u00fchrten Dateien in der Dateiauswahl."
::msgcat::mcset de "Error executing git log:" "Fehler beim Ausf\u00fchren von \u00bbgit log\u00ab:"
::msgcat::mcset de "Reading" "Lesen"
::msgcat::mcset de "Reading commits..." "Versionen werden gelesen ..."
::msgcat::mcset de "No commits selected" "Keine Versionen ausgew\u00e4hlt"
::msgcat::mcset de "Command line" "Kommandozeile"
::msgcat::mcset de "Can't parse git log output:" "Ausgabe von \u00bbgit log\u00ab kann nicht erkannt werden:"
::msgcat::mcset de "No commit information available" "Keine Versionsinformation verf\u00fcgbar"
::msgcat::mcset de "OK" "Ok"
::msgcat::mcset de "Cancel" "Abbrechen"
::msgcat::mcset de "&Update" "&Aktualisieren"
::msgcat::mcset de "&Reload" "&Neu laden"
::msgcat::mcset de "Reread re&ferences" "&Zweige neu laden"
::msgcat::mcset de "&List references" "Zweige/Markierungen auf&listen"
::msgcat::mcset de "Start git &gui" "\u00bbgit &gui\u00ab starten"
::msgcat::mcset de "&Quit" "&Beenden"
::msgcat::mcset de "&File" "&Datei"
::msgcat::mcset de "&Preferences" "&Einstellungen"
::msgcat::mcset de "&Edit" "&Bearbeiten"
::msgcat::mcset de "&New view..." "&Neue Ansicht ..."
::msgcat::mcset de "&Edit view..." "Ansicht &bearbeiten ..."
::msgcat::mcset de "&Delete view" "Ansicht &entfernen"
::msgcat::mcset de "&All files" "&Alle Dateien"
::msgcat::mcset de "&View" "&Ansicht"
::msgcat::mcset de "&About gitk" "\u00dcber &gitk"
::msgcat::mcset de "&Key bindings" "&Tastenk\u00fcrzel"
::msgcat::mcset de "&Help" "&Hilfe"
::msgcat::mcset de "SHA1 ID:" "SHA1 ID:"
::msgcat::mcset de "Row" "Zeile"
::msgcat::mcset de "Find" "Suche"
::msgcat::mcset de "commit" "Version nach"
::msgcat::mcset de "containing:" "Beschreibung:"
::msgcat::mcset de "touching paths:" "Dateien:"
::msgcat::mcset de "adding/removing string:" "\u00c4nderungen:"
::msgcat::mcset de "changing lines matching:" "Ge\u00e4nderte Zeilen entsprechen:"
::msgcat::mcset de "Exact" "Exakt"
::msgcat::mcset de "IgnCase" "Kein Gro\u00df/Klein"
::msgcat::mcset de "Regexp" "Regexp"
::msgcat::mcset de "All fields" "Alle Felder"
::msgcat::mcset de "Headline" "\u00dcberschrift"
::msgcat::mcset de "Comments" "Beschreibung"
::msgcat::mcset de "Author" "Autor"
::msgcat::mcset de "Committer" "Eintragender"
::msgcat::mcset de "Search" "Suchen"
::msgcat::mcset de "Diff" "Vergleich"
::msgcat::mcset de "Old version" "Alte Version"
::msgcat::mcset de "New version" "Neue Version"
::msgcat::mcset de "Lines of context" "Kontextzeilen"
::msgcat::mcset de "Ignore space change" "Leerzeichen\u00e4nderungen ignorieren"
::msgcat::mcset de "Line diff" "Zeilenunterschied"
::msgcat::mcset de "Patch" "Patch"
::msgcat::mcset de "Tree" "Baum"
::msgcat::mcset de "Diff this -> selected" "Vergleich: diese -> gew\u00e4hlte"
::msgcat::mcset de "Diff selected -> this" "Vergleich: gew\u00e4hlte -> diese"
::msgcat::mcset de "Make patch" "Patch erstellen"
::msgcat::mcset de "Create tag" "Markierung erstellen"
::msgcat::mcset de "Write commit to file" "Version in Datei schreiben"
::msgcat::mcset de "Create new branch" "Neuen Zweig erstellen"
::msgcat::mcset de "Cherry-pick this commit" "Diese Version pfl\u00fccken"
::msgcat::mcset de "Reset HEAD branch to here" "HEAD-Zweig auf diese Version zur\u00fccksetzen"
::msgcat::mcset de "Mark this commit" "Lesezeichen setzen"
::msgcat::mcset de "Return to mark" "Zum Lesezeichen"
::msgcat::mcset de "Find descendant of this and mark" "Abk\u00f6mmling von Lesezeichen und dieser Version finden"
::msgcat::mcset de "Compare with marked commit" "Mit Lesezeichen vergleichen"
::msgcat::mcset de "Diff this -> marked commit" "Vergleich: diese -> gew\u00e4hlte Version"
::msgcat::mcset de "Diff marked commit -> this" "Vergleich: gew\u00e4hlte -> diese Version"
::msgcat::mcset de "Revert this commit" "Version umkehren"
::msgcat::mcset de "Check out this branch" "Auf diesen Zweig umstellen"
::msgcat::mcset de "Remove this branch" "Zweig l\u00f6schen"
::msgcat::mcset de "Copy branch name" "Zweigname kopieren"
::msgcat::mcset de "Highlight this too" "Diesen auch hervorheben"
::msgcat::mcset de "Highlight this only" "Nur diesen hervorheben"
::msgcat::mcset de "External diff" "Externes Diff-Programm"
::msgcat::mcset de "Blame parent commit" "Annotieren der Elternversion"
::msgcat::mcset de "Copy path" "Pfad kopieren"
::msgcat::mcset de "Show origin of this line" "Herkunft dieser Zeile anzeigen"
::msgcat::mcset de "Run git gui blame on this line" "Diese Zeile annotieren (\u00bbgit gui blame\u00ab)"
::msgcat::mcset de "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk - eine Visualisierung der Git-Historie\n\nCopyright \\u00a9 2005-2016 Paul Mackerras\n\nBenutzung und Weiterverbreitung gem\u00e4\u00df den Bedingungen der GNU General Public License"
::msgcat::mcset de "Close" "Schlie\u00dfen"
::msgcat::mcset de "Gitk key bindings" "Gitk-Tastaturbelegung"
::msgcat::mcset de "Gitk key bindings:" "Gitk-Tastaturbelegung:"
::msgcat::mcset de "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Beenden"
::msgcat::mcset de "<%s-W>\u0009\u0009Close window" "<%s-F>\u0009\u0009Fenster schlie\u00dfen"
::msgcat::mcset de "<Home>\u0009\u0009Move to first commit" "<Pos1>\u0009\u0009Zur neuesten Version springen"
::msgcat::mcset de "<End>\u0009\u0009Move to last commit" "<Ende>\u0009\u0009Zur \u00e4ltesten Version springen"
::msgcat::mcset de "<Up>, p, k\u0009Move up one commit" "<Hoch>, p, k\u0009N\u00e4chste neuere Version"
::msgcat::mcset de "<Down>, n, j\u0009Move down one commit" "<Runter>, n, j\u0009N\u00e4chste \u00e4ltere Version"
::msgcat::mcset de "<Left>, z, h\u0009Go back in history list" "<Links>, z, h\u0009Eine Version zur\u00fcckgehen"
::msgcat::mcset de "<Right>, x, l\u0009Go forward in history list" "<Rechts>, x, l\u0009Eine Version weitergehen"
::msgcat::mcset de "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n>\u0009Zu n-ter Elternversion in Versionshistorie springen"
::msgcat::mcset de "<PageUp>\u0009Move up one page in commit list" "<BildHoch>\u0009Eine Seite nach oben bl\u00e4ttern"
::msgcat::mcset de "<PageDown>\u0009Move down one page in commit list" "<BildRunter>\u0009Eine Seite nach unten bl\u00e4ttern"
::msgcat::mcset de "<%s-Home>\u0009Scroll to top of commit list" "<%s-Pos1>\u0009Zum oberen Ende der Versionsliste bl\u00e4ttern"
::msgcat::mcset de "<%s-End>\u0009Scroll to bottom of commit list" "<%s-Ende>\u0009Zum unteren Ende der Versionsliste bl\u00e4ttern"
::msgcat::mcset de "<%s-Up>\u0009Scroll commit list up one line" "<%s-Hoch>\u0009Versionsliste eine Zeile nach oben bl\u00e4ttern"
::msgcat::mcset de "<%s-Down>\u0009Scroll commit list down one line" "<%s-Runter>\u0009Versionsliste eine Zeile nach unten bl\u00e4ttern"
::msgcat::mcset de "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-BildHoch>\u0009Versionsliste eine Seite nach oben bl\u00e4ttern"
::msgcat::mcset de "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-BildRunter>\u0009Versionsliste eine Seite nach unten bl\u00e4ttern"
::msgcat::mcset de "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Umschalt-Hoch>\u0009R\u00fcckw\u00e4rts suchen (nach oben; neuere Versionen)"
::msgcat::mcset de "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Umschalt-Runter> Suchen (nach unten; \u00e4ltere Versionen)"
::msgcat::mcset de "<Delete>, b\u0009Scroll diff view up one page" "<Entf>, b\u0009\u0009Vergleich eine Seite nach oben bl\u00e4ttern"
::msgcat::mcset de "<Backspace>\u0009Scroll diff view up one page" "<L\u00f6schtaste>\u0009Vergleich eine Seite nach oben bl\u00e4ttern"
::msgcat::mcset de "<Space>\u0009\u0009Scroll diff view down one page" "<Leertaste>\u0009Vergleich eine Seite nach unten bl\u00e4ttern"
::msgcat::mcset de "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009Vergleich um 18 Zeilen nach oben bl\u00e4ttern"
::msgcat::mcset de "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009Vergleich um 18 Zeilen nach unten bl\u00e4ttern"
::msgcat::mcset de "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009Suchen"
::msgcat::mcset de "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009Weitersuchen"
::msgcat::mcset de "<Return>\u0009Move to next find hit" "<Eingabetaste>\u0009Weitersuchen"
::msgcat::mcset de "g\u0009\u0009Go to commit" "g\u0009\u0009Zu Version springen"
::msgcat::mcset de "/\u0009\u0009Focus the search box" "/\u0009\u0009Tastaturfokus ins Suchfeld"
::msgcat::mcset de "?\u0009\u0009Move to previous find hit" "?\u0009\u0009R\u00fcckw\u00e4rts weitersuchen"
::msgcat::mcset de "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009Vergleich zur n\u00e4chsten Datei bl\u00e4ttern"
::msgcat::mcset de "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009Weitersuchen im Vergleich"
::msgcat::mcset de "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009R\u00fcckw\u00e4rts weitersuchen im Vergleich"
::msgcat::mcset de "<%s-KP+>\u0009Increase font size" "<%s-Nummerblock-Plus>\u0009Schrift vergr\u00f6\u00dfern"
::msgcat::mcset de "<%s-plus>\u0009Increase font size" "<%s-Plus>\u0009Schrift vergr\u00f6\u00dfern"
::msgcat::mcset de "<%s-KP->\u0009Decrease font size" "<%s-Nummernblock-Minus> Schrift verkleinern"
::msgcat::mcset de "<%s-minus>\u0009Decrease font size" "<%s-Minus>\u0009Schrift verkleinern"
::msgcat::mcset de "<F5>\u0009\u0009Update" "<F5>\u0009\u0009Aktualisieren"
::msgcat::mcset de "Error creating temporary directory %s:" "Fehler beim Erzeugen des tempor\u00e4ren Verzeichnisses \u00bb%s\u00ab:"
::msgcat::mcset de "Error getting \"%s\" from %s:" "Fehler beim Holen von \u00bb%s\u00ab von \u00bb%s\u00ab:"
::msgcat::mcset de "command failed:" "Kommando fehlgeschlagen:"
::msgcat::mcset de "No such commit" "Version nicht gefunden"
::msgcat::mcset de "git gui blame: command failed:" "git gui blame: Kommando fehlgeschlagen:"
::msgcat::mcset de "Couldn't read merge head: %s" "Zusammenf\u00fchrungs-Spitze konnte nicht gelesen werden: %s"
::msgcat::mcset de "Error reading index: %s" "Fehler beim Lesen der Bereitstellung (\u00bbindex\u00ab): %s"
::msgcat::mcset de "Couldn't start git blame: %s" "\u00bbgit blame\u00ab konnte nicht gestartet werden: %s"
::msgcat::mcset de "Searching" "Suchen"
::msgcat::mcset de "Error running git blame: %s" "Fehler beim Ausf\u00fchren von \u00bbgit blame\u00ab: %s"
::msgcat::mcset de "That line comes from commit %s,  which is not in this view" "Diese Zeile stammt aus Version %s, die nicht in dieser Ansicht gezeigt wird"
::msgcat::mcset de "External diff viewer failed:" "Externes Diff-Programm fehlgeschlagen:"
::msgcat::mcset de "Gitk view definition" "Gitk-Ansichten"
::msgcat::mcset de "Remember this view" "Diese Ansicht speichern"
::msgcat::mcset de "References (space separated list):" "Zweige/Markierungen (durch Leerzeichen getrennte Liste):"
::msgcat::mcset de "Branches & tags:" "Zweige/Markierungen:"
::msgcat::mcset de "All refs" "Alle Markierungen und Zweige"
::msgcat::mcset de "All (local) branches" "Alle (lokalen) Zweige"
::msgcat::mcset de "All tags" "Alle Markierungen"
::msgcat::mcset de "All remote-tracking branches" "Alle \u00dcbernahmezweige"
::msgcat::mcset de "Commit Info (regular expressions):" "Versionsinformationen (regul\u00e4re Ausdr\u00fccke):"
::msgcat::mcset de "Author:" "Autor:"
::msgcat::mcset de "Committer:" "Eintragender:"
::msgcat::mcset de "Commit Message:" "Versionsbeschreibung:"
::msgcat::mcset de "Matches all Commit Info criteria" "Alle Versionsinformationen-Kriterien erf\u00fcllen"
::msgcat::mcset de "Matches no Commit Info criteria" "keine Versionsinformationen-Kriterien erf\u00fcllen"
::msgcat::mcset de "Changes to Files:" "Dateien:"
::msgcat::mcset de "Fixed String" "Zeichenkette"
::msgcat::mcset de "Regular Expression" "Regul\u00e4rer Ausdruck"
::msgcat::mcset de "Search string:" "Suchausdruck:"
::msgcat::mcset de "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "Datum (\u00bb2 weeks ago\u00ab, \u00bb2009-03-17 15:27:38\u00ab, \u00bbMarch 17, 2009 15:27:38\u00ab)"
::msgcat::mcset de "Since:" "Von:"
::msgcat::mcset de "Until:" "Bis:"
::msgcat::mcset de "Limit and/or skip a number of revisions (positive integer):" "Versionsanzahl begrenzen oder einige \u00fcberspringen (ganzzahliger Wert):"
::msgcat::mcset de "Number to show:" "Anzeigen:"
::msgcat::mcset de "Number to skip:" "\u00dcberspringen:"
::msgcat::mcset de "Miscellaneous options:" "Sonstiges:"
::msgcat::mcset de "Strictly sort by date" "Streng nach Datum sortieren"
::msgcat::mcset de "Mark branch sides" "Zweig-Seiten markieren"
::msgcat::mcset de "Limit to first parent" "Auf erste Elternversion beschr\u00e4nken"
::msgcat::mcset de "Simple history" "Einfache Historie"
::msgcat::mcset de "Additional arguments to git log:" "Zus\u00e4tzliche Argumente f\u00fcr \u00bbgit log\u00ab:"
::msgcat::mcset de "Enter files and directories to include, one per line:" "Folgende Dateien und Verzeichnisse anzeigen (eine pro Zeile):"
::msgcat::mcset de "Command to generate more commits to include:" "Versionsliste durch folgendes Kommando erzeugen lassen:"
::msgcat::mcset de "Gitk: edit view" "Gitk: Ansicht bearbeiten"
::msgcat::mcset de "-- criteria for selecting revisions" "-- Auswahl der angezeigten Versionen"
::msgcat::mcset de "View Name" "Ansichtsname"
::msgcat::mcset de "Apply (F5)" "Anwenden (F5)"
::msgcat::mcset de "Error in commit selection arguments:" "Fehler in den ausgew\u00e4hlten Versionen:"
::msgcat::mcset de "None" "Keine"
::msgcat::mcset de "Descendant" "Abk\u00f6mmling"
::msgcat::mcset de "Not descendant" "Kein Abk\u00f6mmling"
::msgcat::mcset de "Ancestor" "Vorg\u00e4nger"
::msgcat::mcset de "Not ancestor" "Kein Vorg\u00e4nger"
::msgcat::mcset de "Local changes checked in to index but not committed" "Lokale \u00c4nderungen bereitgestellt, aber nicht eingetragen"
::msgcat::mcset de "Local uncommitted changes, not checked in to index" "Lokale \u00c4nderungen, nicht bereitgestellt"
::msgcat::mcset de "and many more" "und weitere"
::msgcat::mcset de "many" "viele"
::msgcat::mcset de "Tags:" "Markierungen:"
::msgcat::mcset de "Parent" "Eltern"
::msgcat::mcset de "Child" "Kind"
::msgcat::mcset de "Branch" "Zweig"
::msgcat::mcset de "Follows" "Folgt auf"
::msgcat::mcset de "Precedes" "Vorg\u00e4nger von"
::msgcat::mcset de "Error getting diffs: %s" "Fehler beim Laden des Vergleichs: %s"
::msgcat::mcset de "Goto:" "Gehe zu:"
::msgcat::mcset de "Short SHA1 id %s is ambiguous" "Kurzer SHA1-Hashwert \u00bb%s\u00ab ist mehrdeutig"
::msgcat::mcset de "Revision %s is not known" "Version \u00bb%s\u00ab ist unbekannt"
::msgcat::mcset de "SHA1 id %s is not known" "SHA1-Hashwert \u00bb%s\u00ab ist unbekannt"
::msgcat::mcset de "Revision %s is not in the current view" "Version \u00bb%s\u00ab wird in der aktuellen Ansicht nicht angezeigt"
::msgcat::mcset de "Date" "Datum"
::msgcat::mcset de "Children" "Kinder"
::msgcat::mcset de "Reset %s branch to here" "Zweig \u00bb%s\u00ab hierher zur\u00fccksetzen"
::msgcat::mcset de "Detached head: can't reset" "Zweigspitze ist abgetrennt: Zur\u00fccksetzen nicht m\u00f6glich"
::msgcat::mcset de "Skipping merge commit " "\u00dcberspringe Zusammenf\u00fchrungs-Version "
::msgcat::mcset de "Error getting patch ID for " "Fehler beim Holen der Patch-ID f\u00fcr "
::msgcat::mcset de " - stopping\n" " - Abbruch.\n"
::msgcat::mcset de "Commit " "Version "
::msgcat::mcset de " is the same patch as\n       " " ist das gleiche Patch wie\n       "
::msgcat::mcset de " differs from\n       " " ist unterschiedlich von\n       "
::msgcat::mcset de "Diff of commits:\n\n" "Vergleich der Versionen:\n\n"
::msgcat::mcset de " has %s children - stopping\n" " hat %s Kinder. Abbruch\n"
::msgcat::mcset de "Error writing commit to file: %s" "Fehler beim Schreiben der Version in Datei: %s"
::msgcat::mcset de "Error diffing commits: %s" "Fehler beim Vergleichen der Versionen: %s"
::msgcat::mcset de "Top" "Oben"
::msgcat::mcset de "From" "Von"
::msgcat::mcset de "To" "bis"
::msgcat::mcset de "Generate patch" "Patch erstellen"
::msgcat::mcset de "From:" "Von:"
::msgcat::mcset de "To:" "bis:"
::msgcat::mcset de "Reverse" "Umgekehrt"
::msgcat::mcset de "Output file:" "Ausgabedatei:"
::msgcat::mcset de "Generate" "Erzeugen"
::msgcat::mcset de "Error creating patch:" "Fehler beim Erzeugen des Patches:"
::msgcat::mcset de "ID:" "ID:"
::msgcat::mcset de "Tag name:" "Markierungsname:"
::msgcat::mcset de "Tag message is optional" "Eine Markierungsbeschreibung ist optional"
::msgcat::mcset de "Tag message:" "Markierungsbeschreibung:"
::msgcat::mcset de "Create" "Erstellen"
::msgcat::mcset de "No tag name specified" "Kein Markierungsname angegeben"
::msgcat::mcset de "Tag \"%s\" already exists" "Markierung \u00bb%s\u00ab existiert bereits."
::msgcat::mcset de "Error creating tag:" "Fehler beim Erstellen der Markierung:"
::msgcat::mcset de "Command:" "Kommando:"
::msgcat::mcset de "Write" "Schreiben"
::msgcat::mcset de "Error writing commit:" "Fehler beim Schreiben der Version:"
::msgcat::mcset de "Name:" "Name:"
::msgcat::mcset de "Please specify a name for the new branch" "Bitte geben Sie einen Namen f\u00fcr den neuen Zweig an."
::msgcat::mcset de "Branch '%s' already exists. Overwrite?" "Zweig \u00bb%s\u00ab existiert bereits. Soll er \u00fcberschrieben werden?"
::msgcat::mcset de "Commit %s is already included in branch %s -- really re-apply it?" "Version \u00bb%s\u00ab ist bereits im Zweig \u00bb%s\u00ab enthalten -- trotzdem erneut eintragen?"
::msgcat::mcset de "Cherry-picking" "Version pfl\u00fccken"
::msgcat::mcset de "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "Pfl\u00fccken fehlgeschlagen, da noch lokale \u00c4nderungen in Datei \u00bb%s\u00ab\nvorliegen. Bitte diese \u00c4nderungen eintragen, zur\u00fccksetzen oder\nzwischenspeichern (\u00bbgit stash\u00ab) und dann erneut versuchen."
::msgcat::mcset de "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "Pfl\u00fccken fehlgeschlagen, da ein Zusammenf\u00fchrungs-Konflikt aufgetreten\nist. Soll das Zusammenf\u00fchrungs-Werkzeug (\u00bbgit citool\u00ab) aufgerufen\nwerden, um diesen Konflikt aufzul\u00f6sen?"
::msgcat::mcset de "No changes committed" "Keine \u00c4nderungen eingetragen"
::msgcat::mcset de "Commit %s is not included in branch %s -- really revert it?" "Version \u00bb%s\u00ab ist nicht im Zweig \u00bb%s\u00ab enthalten -- trotzdem umkehren?"
::msgcat::mcset de "Reverting" "Umkehren"
::msgcat::mcset de "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "Umkehren fehlgeschlagen, da noch lokale \u00c4nderungen in Datei \u00bb%s\u00ab\nvorliegen. Bitte diese \u00c4nderungen eintragen, zur\u00fccksetzen oder\nzwischenspeichern (\u00bbgit stash\u00ab) und dann erneut versuchen."
::msgcat::mcset de "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "Umkehren fehlgeschlagen, da ein Zusammenf\u00fchrungs-Konflikt aufgetreten\nist. Soll das Zusammenf\u00fchrungs-Werkzeug (\u00bbgit citool\u00ab) aufgerufen\nwerden, um diesen Konflikt aufzul\u00f6sen?"
::msgcat::mcset de "Confirm reset" "Zur\u00fccksetzen best\u00e4tigen"
::msgcat::mcset de "Reset branch %s to %s?" "Zweig \u00bb%s\u00ab auf \u00bb%s\u00ab zur\u00fccksetzen?"
::msgcat::mcset de "Reset type:" "Art des Zur\u00fccksetzens:"
::msgcat::mcset de "Soft: Leave working tree and index untouched" "Harmlos: Arbeitskopie und Bereitstellung unver\u00e4ndert"
::msgcat::mcset de "Mixed: Leave working tree untouched, reset index" "Gemischt: Arbeitskopie unver\u00e4ndert,\nBereitstellung zur\u00fcckgesetzt"
::msgcat::mcset de "Hard: Reset working tree and index\n(discard ALL local changes)" "Hart: Arbeitskopie und Bereitstellung\n(Alle lokalen \u00c4nderungen werden gel\u00f6scht)"
::msgcat::mcset de "Resetting" "Zur\u00fccksetzen"
::msgcat::mcset de "Checking out" "Umstellen"
::msgcat::mcset de "Cannot delete the currently checked-out branch" "Der Zweig, auf den die Arbeitskopie momentan umgestellt ist, kann nicht gel\u00f6scht werden."
::msgcat::mcset de "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "Die Versionen auf Zweig \u00bb%s\u00ab existieren auf keinem anderen Zweig.\nZweig \u00bb%s\u00ab trotzdem l\u00f6schen?"
::msgcat::mcset de "Tags and heads: %s" "Markierungen und Zweige: %s"
::msgcat::mcset de "Filter" "Filtern"
::msgcat::mcset de "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "Fehler beim Lesen der Strukturinformationen; Zweige und Informationen zu Vorg\u00e4nger/Nachfolger werden unvollst\u00e4ndig sein."
::msgcat::mcset de "Tag" "Markierung"
::msgcat::mcset de "Id" "Id"
::msgcat::mcset de "Gitk font chooser" "Gitk-Schriften w\u00e4hlen"
::msgcat::mcset de "B" "F"
::msgcat::mcset de "I" "K"
::msgcat::mcset de "Commit list display options" "Anzeige der Versionsliste"
::msgcat::mcset de "Maximum graph width (lines)" "Maximale Graphenbreite (Zeilen)"
::msgcat::mcset de "Maximum graph width (% of pane)" "Maximale Graphenbreite (% des Fensters)"
::msgcat::mcset de "Show local changes" "Lokale \u00c4nderungen anzeigen"
::msgcat::mcset de "Auto-select SHA1 (length)" "SHA1-Hashwert (L\u00e4nge) automatisch ausw\u00e4hlen"
::msgcat::mcset de "Hide remote refs" "Entfernte Zweige/Markierungen ausblenden"
::msgcat::mcset de "Diff display options" "Anzeige des Vergleichs"
::msgcat::mcset de "Tab spacing" "Tabulatorbreite"
::msgcat::mcset de "Display nearby tags/heads" "Naheliegende Markierungen/Zweigspitzen anzeigen"
::msgcat::mcset de "Maximum # tags/heads to show" "Maximale Anzahl anzuzeigender Markierungen/Zweigspitzen"
::msgcat::mcset de "Limit diffs to listed paths" "Vergleich nur f\u00fcr angezeigte Pfade"
::msgcat::mcset de "Support per-file encodings" "Zeichenkodierung pro Datei ermitteln"
::msgcat::mcset de "External diff tool" "Externes Diff-Programm"
::msgcat::mcset de "Choose..." "W\u00e4hlen ..."
::msgcat::mcset de "General options" "Allgemeine Optionen"
::msgcat::mcset de "Use themed widgets" "Aussehen der Benutzeroberfl\u00e4che durch Thema bestimmen"
::msgcat::mcset de "(change requires restart)" "(\u00c4nderungen werden erst nach Neustart wirksam)"
::msgcat::mcset de "(currently unavailable)" "(Momentan nicht verf\u00fcgbar)"
::msgcat::mcset de "Colors: press to choose" "Farben: Klicken zum W\u00e4hlen"
::msgcat::mcset de "Interface" "Benutzeroberfl\u00e4che"
::msgcat::mcset de "interface" "Benutzeroberfl\u00e4che"
::msgcat::mcset de "Background" "Hintergrund"
::msgcat::mcset de "background" "Hintergrund"
::msgcat::mcset de "Foreground" "Vordergrund"
::msgcat::mcset de "foreground" "Vordergrund"
::msgcat::mcset de "Diff: old lines" "Vergleich: Alte Zeilen"
::msgcat::mcset de "diff old lines" "Vergleich - Alte Zeilen"
::msgcat::mcset de "Diff: new lines" "Vergleich: Neue Zeilen"
::msgcat::mcset de "diff new lines" "Vergleich - Neue Zeilen"
::msgcat::mcset de "Diff: hunk header" "Vergleich: \u00c4nderungstitel"
::msgcat::mcset de "diff hunk header" "Vergleich - \u00c4nderungstitel"
::msgcat::mcset de "Marked line bg" "Hintergrund f\u00fcr markierte Zeile"
::msgcat::mcset de "marked line background" "Hintergrund f\u00fcr markierte Zeile"
::msgcat::mcset de "Select bg" "Hintergrundfarbe ausw\u00e4hlen"
::msgcat::mcset de "Fonts: press to choose" "Schriftart: Klicken zum W\u00e4hlen"
::msgcat::mcset de "Main font" "Programmschriftart"
::msgcat::mcset de "Diff display font" "Schriftart f\u00fcr Vergleich"
::msgcat::mcset de "User interface font" "Beschriftungen"
::msgcat::mcset de "Gitk preferences" "Gitk-Einstellungen"
::msgcat::mcset de "General" "Allgemein"
::msgcat::mcset de "Colors" "Farben"
::msgcat::mcset de "Fonts" "Schriftarten"
::msgcat::mcset de "Gitk: choose color for %s" "Gitk: Farbe w\u00e4hlen f\u00fcr %s"
::msgcat::mcset de "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "Entschuldigung, gitk kann nicht mit dieser Tcl/Tk Version ausgef\u00fchrt werden.\n Gitk erfordert mindestens Tcl/Tk 8.4."
::msgcat::mcset de "Cannot find a git repository here." "Kein Git-Projektarchiv gefunden."
::msgcat::mcset de "Ambiguous argument '%s': both revision and filename" "Mehrdeutige Angabe \u00bb%s\u00ab: Sowohl Version als auch Dateiname existiert."
::msgcat::mcset de "Bad arguments to gitk:" "Falsche Kommandozeilen-Parameter f\u00fcr gitk:"
