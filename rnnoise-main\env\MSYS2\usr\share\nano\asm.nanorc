## Syntax highlighting for assembler.

## Original author:  <PERSON>
## License:  GPL version 3 or newer

syntax asm "\.(S|s|asm)$"
magic "assembler source"
comment "//"

color red "\<[[:alpha:]_]{2,}\>"
color brightgreen "\.(data|subsection|text)\>"
color green "\.(align|file|globl|global|hidden|section|size|type|weak)\>"
color brightyellow "\.(ascii|asciz|byte|double|float|hword|int|long|short|single|struct|word)\>"
color brightred "^[[:blank:]]*[[:alnum:]_.]*:"
color brightcyan "^[[:blank:]]*#[[:blank:]]*(define|undef|include|ifn?def|endif|elif|else|if|warning|error)\>"

# Strings and names of included files.
color brightyellow ""([^"\]|\\.)*"|<[^= 	]*>"

# Comments.
color brightblue "//.*"
color brightblue start="/\*" end="\*/"

# Trailing whitespace.
color ,green "[[:space:]]+$"
