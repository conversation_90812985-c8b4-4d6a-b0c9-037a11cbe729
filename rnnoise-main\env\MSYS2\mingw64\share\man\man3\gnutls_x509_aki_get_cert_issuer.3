.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_aki_get_cert_issuer" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_aki_get_cert_issuer \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_aki_get_cert_issuer(gnutls_x509_aki_t " aki ", unsigned int " seq ", unsigned int * " san_type ", gnutls_datum_t * " san ", gnutls_datum_t * " othername_oid ", gnutls_datum_t * " serial ");"
.SH ARGUMENTS
.IP "gnutls_x509_aki_t aki" 12
The authority key ID
.IP "unsigned int seq" 12
The index of the name to get
.IP "unsigned int * san_type" 12
Will hold the type of the name (of \fBgnutls_subject_alt_names_t\fP)
.IP "gnutls_datum_t * san" 12
The alternative name data
.IP "gnutls_datum_t * othername_oid" 12
The object identifier if  \fIsan_type\fP is \fBGNUTLS_SAN_OTHERNAME\fP
.IP "gnutls_datum_t * serial" 12
The authorityCertSerialNumber number
.SH "DESCRIPTION"
This function will return a specific authorityCertIssuer name as stored in
the  \fIaki\fP type, as well as the authorityCertSerialNumber. All the returned
values should be treated as constant, and may be set to \fBNULL\fP when are not required.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
if the index is out of bounds, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
