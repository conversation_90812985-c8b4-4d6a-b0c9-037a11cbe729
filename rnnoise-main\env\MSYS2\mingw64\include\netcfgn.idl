/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

cpp_quote("#include <winapifamily.h>")

cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

#ifndef DO_NO_IMPORTS
import "unknwn.idl";
import "wtypes.idl";
import "netcfgx.idl";
#endif

cpp_quote("")
[local, object, uuid(8d84bd35-e227-11d2-b700-00a0c98a6a85), pointer_default(unique)]
interface INetCfgPnpReconfigCallback : IUnknown {
  typedef [v1_enum] enum tagNCPNP_RECONFIG_LAYER {
    NCRL_NDIS = 1,
    NCRL_TDI = 2
  } NCPNP_RECONFIG_LAYER;

  HRESULT SendPnpReconfig ([in] NCPNP_RECONFIG_LAYER Layer, [in] <PERSON><PERSON><PERSON><PERSON> pszwUpper, [in] LPCWSTR pszwLower, [in] PVOID pvData, [in] DWORD dwSizeOfData);
};

cpp_quote("")
[local, object, uuid(932238df-bea1-11d0-9298-00c04fc99dcf), pointer_default(unique)]
interface INetCfgComponentControl : IUnknown {
  HRESULT Initialize ([in] INetCfgComponent *pIComp, [in] INetCfg *pINetCfg, [in] WINBOOL fInstalling);
  HRESULT ApplyRegistryChanges ();
  HRESULT ApplyPnpChanges ([in] INetCfgPnpReconfigCallback *pICallback);
  HRESULT CancelChanges ();
};

cpp_quote("")
[local, object, uuid(932238e3-bea1-11d0-9298-00c04fc99dcf), pointer_default(unique)]
interface INetCfgComponentSetup : IUnknown {
  typedef [v1_enum] enum tagNETWORK_INSTALL_TIME {
    NSF_PRIMARYINSTALL = 0x00000001,
    NSF_POSTSYSINSTALL = 0x00000002
  } NETWORK_INSTALL_TIME;

  typedef [v1_enum] enum tagNETWORK_UPGRADE_TYPE {
    NSF_WIN16_UPGRADE = 0x00000010,
    NSF_WIN95_UPGRADE = 0x00000020,
    NSF_WINNT_WKS_UPGRADE = 0x00000040,
    NSF_WINNT_SVR_UPGRADE = 0x00000080,
    NSF_WINNT_SBS_UPGRADE = 0x00000100,
    NSF_COMPONENT_UPDATE = 0x00000200
  } NETWORK_UPGRADE_TYPE;

  HRESULT Install ([in] DWORD dwSetupFlags);
  HRESULT Upgrade ([in] DWORD dwSetupFlags, [in] DWORD dwUpgradeFomBuildNo );
  HRESULT ReadAnswerFile ([in] LPCWSTR pszwAnswerFile, [in] LPCWSTR pszwAnswerSections);
  HRESULT Removing ();
};

cpp_quote("")
[local, object, uuid(932238e0-bea1-11d0-9298-00c04fc99dcf), pointer_default(unique)]
interface INetCfgComponentPropertyUi : IUnknown {
  typedef [v1_enum] enum tagDEFAULT_PAGES {
    DPP_ADVANCED = 1,
  } DEFAULT_PAGES;

  HRESULT QueryPropertyUi ([in] IUnknown *pUnkReserved);
  HRESULT SetContext ([in] IUnknown *pUnkReserved);
  HRESULT MergePropPages ([in, out] DWORD *pdwDefPages, [out] BYTE **pahpspPrivate, [out] UINT *pcPages, [in] HWND hwndParent, [in] LPCWSTR *pszStartPage);
  HRESULT ValidateProperties ([in] HWND hwndSheet);
  HRESULT ApplyProperties ();
  HRESULT CancelProperties ();
};

cpp_quote("")
[local, object, uuid(932238e1-bea1-11d0-9298-00c04fc99dcf), pointer_default(unique)]
interface INetCfgComponentNotifyBinding : IUnknown {
  typedef [v1_enum] enum tagBIND_FLAGS1 {
    NCN_ADD = 0x00000001,
    NCN_REMOVE = 0x00000002,
    NCN_UPDATE = 0x00000004,
    NCN_ENABLE = 0x00000010,
    NCN_DISABLE = 0x00000020,
    NCN_BINDING_PATH = 0x00000100,
    NCN_PROPERTYCHANGE = 0x00000200,
    NCN_NET = 0x00010000,
    NCN_NETTRANS = 0x00020000,
    NCN_NETCLIENT = 0x00040000,
    NCN_NETSERVICE = 0x00080000
  } BIND_FLAGS1;

  HRESULT QueryBindingPath ([in] DWORD dwChangeFlag, [in] INetCfgBindingPath *pIPath);
  HRESULT NotifyBindingPath ([in] DWORD dwChangeFlag, [in] INetCfgBindingPath *pIPath);
};

cpp_quote("")
[local, object, uuid(932238e2-bea1-11d0-9298-00c04fc99dcf), pointer_default(unique)]
interface INetCfgComponentNotifyGlobal : IUnknown {
  HRESULT GetSupportedNotifications ([out] DWORD *dwNotifications);
  HRESULT SysQueryBindingPath ([in] DWORD dwChangeFlag, [in] INetCfgBindingPath *pIPath);
  HRESULT SysNotifyBindingPath ([in] DWORD dwChangeFlag, [in] INetCfgBindingPath *pIPath);
  HRESULT SysNotifyComponent ([in] DWORD dwChangeFlag, [in] INetCfgComponent *pIComp);
};

cpp_quote("")
[local, object, uuid(932238e4-bea1-11d0-9298-00c04fc99dcf), pointer_default(unique)]
interface INetCfgComponentUpperEdge : IUnknown {
  HRESULT GetInterfaceIdsForAdapter ([in] INetCfgComponent *pAdapter, [out] DWORD *pdwNumInterfaces, [out] GUID **ppguidInterfaceIds);
  HRESULT AddInterfacesToAdapter ([in] INetCfgComponent *pAdapter, [in] DWORD dwNumInterfaces);
  HRESULT RemoveInterfacesFromAdapter ([in] INetCfgComponent *pAdapter, [in] DWORD dwNumInterfaces, [in] const GUID *pguidInterfaceIds);
};

cpp_quote("")
[object, uuid(c08956a6-1cd3-11d1-b1c5-00805fc1270e), pointer_default(unique)]
interface INetLanConnectionUiInfo : IUnknown {
  HRESULT GetDeviceGuid ([out] GUID *pguid);
};

cpp_quote("")
[object, uuid(faedcf58-31fe-11d1-aad2-00805fc1270e), pointer_default(unique)]
interface INetRasConnectionIpUiInfo : IUnknown {
  typedef enum tagRASCON_IPUI_FLAGS {
    RCUIF_VPN = 0x00000001,
    RCUIF_DEMAND_DIAL = 0x00000002,
    RCUIF_NOT_ADMIN = 0x00000004,
    RCUIF_USE_IPv4_STATICADDRESS = 0x00000008,
    RCUIF_USE_IPv4_NAME_SERVERS = 0x00000010,
    RCUIF_USE_IPv4_REMOTE_GATEWAY = 0x00000020,
    RCUIF_USE_IPv4_EXPLICIT_METRIC = 0x00000040,
    RCUIF_USE_HEADER_COMPRESSION = 0x00000080,
    RCUIF_USE_DISABLE_REGISTER_DNS = 0x00000100,
    RCUIF_USE_PRIVATE_DNS_SUFFIX = 0x00000200,
    RCUIF_ENABLE_NBT = 0x00000400,
    RCUIF_USE_IPv6_STATICADDRESS = 0x00000800,
    RCUIF_USE_IPv6_NAME_SERVERS = 0x00001000,
    RCUIF_USE_IPv6_REMOTE_GATEWAY = 0x00002000,
    RCUIF_USE_IPv6_EXPLICIT_METRIC = 0x00004000,
    RCUIF_DISABLE_CLASS_BASED_ROUTE = 0x00008000
  } RASCON_UIINFO_FLAGS;

  typedef struct tagRASCON_IPUI {
  GUID guidConnection;
  WINBOOL fIPv6Cfg;
  DWORD dwFlags;
  WCHAR pszwIpAddr[16];
  WCHAR pszwDnsAddr[16];
  WCHAR pszwDns2Addr[16];
  WCHAR pszwWinsAddr[16];
  WCHAR pszwWins2Addr[16];
  WCHAR pszwDnsSuffix[256];
  WCHAR pszwIpv6Addr[65];
  DWORD dwIpv6PrefixLength;
  WCHAR pszwIpv6DnsAddr[65];
  WCHAR pszwIpv6Dns2Addr[65];
  DWORD dwIPv4InfMetric;
  DWORD dwIPv6InfMetric;
  } RASCON_IPUI;

  HRESULT GetUiInfo ([out] RASCON_IPUI *pInfo);
};

cpp_quote("")
[local, object, uuid(c0e8ae9a-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgComponentSysPrep : IUnknown {
  HRESULT SaveAdapterParameters([in] INetCfgSysPrep *pncsp, [in] LPCWSTR pszwAnswerSections, [in] GUID *pAdapterInstanceGuid);
  HRESULT RestoreAdapterParameters([in] LPCWSTR pszwAnswerFile, [in] LPCWSTR pszwAnswerSection, [in] GUID *pAdapterInstanceGuid);
};

cpp_quote("#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */")
