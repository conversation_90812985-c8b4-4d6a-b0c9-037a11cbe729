------------------------------------------------------------------------
-- copyNegate.decTest -- quiet copy and negate                        --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 999
minExponent: -999

-- Sanity check
cpnx001 copynegate       +7.50  -> -7.50

-- Infinities
cpnx011 copynegate  Infinity    -> -Infinity
cpnx012 copynegate  -Infinity   -> Infinity

-- NaNs, 0 payload
cpnx021 copynegate         NaN  -> -NaN
cpnx022 copynegate        -NaN  -> NaN
cpnx023 copynegate        sNaN  -> -sNaN
cpnx024 copynegate       -sNaN  -> sNaN

-- NaNs, non-0 payload
cpnx031 copynegate       NaN13  -> -NaN13
cpnx032 copynegate      -NaN13  -> NaN13
cpnx033 copynegate      sNaN13  -> -sNaN13
cpnx034 copynegate     -sNaN13  -> sNaN13
cpnx035 copynegate       NaN70  -> -NaN70
cpnx036 copynegate      -NaN70  -> NaN70
cpnx037 copynegate      sNaN101 -> -sNaN101
cpnx038 copynegate     -sNaN101 -> sNaN101

-- finites
cpnx101 copynegate          7   -> -7
cpnx102 copynegate         -7   -> 7
cpnx103 copynegate         75   -> -75
cpnx104 copynegate        -75   -> 75
cpnx105 copynegate       7.50   -> -7.50
cpnx106 copynegate      -7.50   -> 7.50
cpnx107 copynegate       7.500  -> -7.500
cpnx108 copynegate      -7.500  -> 7.500

-- zeros
cpnx111 copynegate          0   -> -0
cpnx112 copynegate         -0   -> 0
cpnx113 copynegate       0E+4   -> -0E+4
cpnx114 copynegate      -0E+4   -> 0E+4
cpnx115 copynegate     0.0000   -> -0.0000
cpnx116 copynegate    -0.0000   -> 0.0000
cpnx117 copynegate      0E-141  -> -0E-141
cpnx118 copynegate     -0E-141  -> 0E-141

-- full coefficients, alternating bits
cpnx121 copynegate  268268268         -> -268268268
cpnx122 copynegate  -268268268        -> 268268268
cpnx123 copynegate  134134134         -> -134134134
cpnx124 copynegate  -134134134        -> 134134134

-- Nmax, Nmin, Ntiny
cpnx131 copynegate  9.99999999E+999   -> -9.99999999E+999
cpnx132 copynegate  1E-999                     -> -1E-999
cpnx133 copynegate  1.00000000E-999   -> -1.00000000E-999
cpnx134 copynegate  1E-1007                    -> -1E-1007

cpnx135 copynegate  -1E-1007                   -> 1E-1007
cpnx136 copynegate  -1.00000000E-999  -> 1.00000000E-999
cpnx137 copynegate  -1E-999                    -> 1E-999
cpnx138 copynegate  -9.99999999E+999  -> 9.99999999E+999
