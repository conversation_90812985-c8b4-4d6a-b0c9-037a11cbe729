<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_index_iter Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structlzma__index__iter.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_index_iter Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Iterator to get information about Blocks and Streams.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;index.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aed9846e926101116b7624c8c7b8fe1ce" id="r_aed9846e926101116b7624c8c7b8fe1ce"><td class="memItemLeft" ><a id="aed9846e926101116b7624c8c7b8fe1ce" name="aed9846e926101116b7624c8c7b8fe1ce"></a>
struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49c88963c2c0ba4ef2f4e7795308abe0" id="r_a49c88963c2c0ba4ef2f4e7795308abe0"><td class="memItemLeft" >&#160;&#160;&#160;const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *&#160;&#160;&#160;<a class="el" href="#a223a046bcf09077a6e720967682deeae">flags</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a49c88963c2c0ba4ef2f4e7795308abe0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to Stream Flags.  <a href="#a223a046bcf09077a6e720967682deeae">More...</a><br /></td></tr>
<tr class="separator:a49c88963c2c0ba4ef2f4e7795308abe0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a616c9d1817f95f3c6c7c4569de80f132" id="r_a616c9d1817f95f3c6c7c4569de80f132"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a115a307dbc778a9de296376dc39c7b23">number</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a616c9d1817f95f3c6c7c4569de80f132"><td class="mdescLeft">&#160;</td><td class="mdescRight">Stream number in the <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e" title="Opaque data type to hold the Index(es) and other information.">lzma_index</a>.  <a href="#a115a307dbc778a9de296376dc39c7b23">More...</a><br /></td></tr>
<tr class="separator:a616c9d1817f95f3c6c7c4569de80f132"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a450877e2fe0c3eebd2a3adf695db507b" id="r_a450877e2fe0c3eebd2a3adf695db507b"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#abc6ee9be23e54f31aed07382c8caaf7c">block_count</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a450877e2fe0c3eebd2a3adf695db507b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of Blocks in the Stream.  <a href="#abc6ee9be23e54f31aed07382c8caaf7c">More...</a><br /></td></tr>
<tr class="separator:a450877e2fe0c3eebd2a3adf695db507b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60c51b8a0f7fc8f617616c1d8f546524" id="r_a60c51b8a0f7fc8f617616c1d8f546524"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a426705df8dde4b094a42f91ea20a46ac">compressed_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a60c51b8a0f7fc8f617616c1d8f546524"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compressed start offset of this Stream.  <a href="#a426705df8dde4b094a42f91ea20a46ac">More...</a><br /></td></tr>
<tr class="separator:a60c51b8a0f7fc8f617616c1d8f546524"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afeafead802a2473450229b3a10d4805f" id="r_afeafead802a2473450229b3a10d4805f"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#abd374b748b4a42e122b90841709609bc">uncompressed_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:afeafead802a2473450229b3a10d4805f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed start offset of this Stream.  <a href="#abd374b748b4a42e122b90841709609bc">More...</a><br /></td></tr>
<tr class="separator:afeafead802a2473450229b3a10d4805f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9555a5934bae876ccb8af3a00e5d009e" id="r_a9555a5934bae876ccb8af3a00e5d009e"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a6e73b1f37e3fcf1e9491e4a53b2c52c7">compressed_size</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a9555a5934bae876ccb8af3a00e5d009e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compressed size of this Stream.  <a href="#a6e73b1f37e3fcf1e9491e4a53b2c52c7">More...</a><br /></td></tr>
<tr class="separator:a9555a5934bae876ccb8af3a00e5d009e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af026c20125e34a441407a165c2c2a03a" id="r_af026c20125e34a441407a165c2c2a03a"><td class="memItemLeft" >
&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<b>uncompressed_size</b>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:af026c20125e34a441407a165c2c2a03a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed size of this Stream. <br /></td></tr>
<tr class="separator:af026c20125e34a441407a165c2c2a03a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac148088d200ef412bf6cbbc781a98800" id="r_ac148088d200ef412bf6cbbc781a98800"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a48cfc856f283fe00b0df37402e012818">padding</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:ac148088d200ef412bf6cbbc781a98800"><td class="mdescLeft">&#160;</td><td class="mdescRight">Size of Stream Padding after this Stream.  <a href="#a48cfc856f283fe00b0df37402e012818">More...</a><br /></td></tr>
<tr class="separator:ac148088d200ef412bf6cbbc781a98800"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed9846e926101116b7624c8c7b8fe1ce" id="r_aed9846e926101116b7624c8c7b8fe1ce"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><b>stream</b>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:aed9846e926101116b7624c8c7b8fe1ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a512428bded08c399908eb085ebdeb232" id="r_a512428bded08c399908eb085ebdeb232"><td class="memItemLeft" ><a id="a512428bded08c399908eb085ebdeb232" name="a512428bded08c399908eb085ebdeb232"></a>
struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa66ef188fc3954390215a46d99ce57df" id="r_aa66ef188fc3954390215a46d99ce57df"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#abe5333de53562189012d5ed084c0ef98">number_in_file</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:aa66ef188fc3954390215a46d99ce57df"><td class="mdescLeft">&#160;</td><td class="mdescRight">Block number in the file.  <a href="#abe5333de53562189012d5ed084c0ef98">More...</a><br /></td></tr>
<tr class="separator:aa66ef188fc3954390215a46d99ce57df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad518aa1391625d486745f77280ef3b0f" id="r_ad518aa1391625d486745f77280ef3b0f"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a26436e75d4c2b5dd8d1de24140d8003e">compressed_file_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:ad518aa1391625d486745f77280ef3b0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compressed start offset of this Block.  <a href="#a26436e75d4c2b5dd8d1de24140d8003e">More...</a><br /></td></tr>
<tr class="separator:ad518aa1391625d486745f77280ef3b0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d4724bc4a6d1e91fd09470823c8ef03" id="r_a4d4724bc4a6d1e91fd09470823c8ef03"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a2f3ecf341b5dc043e9673759b8ff47b9">uncompressed_file_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a4d4724bc4a6d1e91fd09470823c8ef03"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed start offset of this Block.  <a href="#a2f3ecf341b5dc043e9673759b8ff47b9">More...</a><br /></td></tr>
<tr class="separator:a4d4724bc4a6d1e91fd09470823c8ef03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a64c3b47a0db8aad7742ce52e5f2be377" id="r_a64c3b47a0db8aad7742ce52e5f2be377"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a35a752d344ff5d35d2a858a20bd6e5e8">number_in_stream</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a64c3b47a0db8aad7742ce52e5f2be377"><td class="mdescLeft">&#160;</td><td class="mdescRight">Block number in this Stream.  <a href="#a35a752d344ff5d35d2a858a20bd6e5e8">More...</a><br /></td></tr>
<tr class="separator:a64c3b47a0db8aad7742ce52e5f2be377"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88ba8db60fd521bc3c5e5c65500d3955" id="r_a88ba8db60fd521bc3c5e5c65500d3955"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a578bba553c43dc59a5e4032d4f6c89a3">compressed_stream_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a88ba8db60fd521bc3c5e5c65500d3955"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compressed start offset of this Block.  <a href="#a578bba553c43dc59a5e4032d4f6c89a3">More...</a><br /></td></tr>
<tr class="separator:a88ba8db60fd521bc3c5e5c65500d3955"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae845a2d1573d9c633bae9966451cf187" id="r_ae845a2d1573d9c633bae9966451cf187"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a0fc4959fab08e1a6a4902c728c735a99">uncompressed_stream_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:ae845a2d1573d9c633bae9966451cf187"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed start offset of this Block.  <a href="#a0fc4959fab08e1a6a4902c728c735a99">More...</a><br /></td></tr>
<tr class="separator:ae845a2d1573d9c633bae9966451cf187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad841358ab36d4b3fc3b7cbc03d03be73" id="r_ad841358ab36d4b3fc3b7cbc03d03be73"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#aafc48408ed40060a84ecd66bae5e1b23">uncompressed_size</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:ad841358ab36d4b3fc3b7cbc03d03be73"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed size of this Block.  <a href="#aafc48408ed40060a84ecd66bae5e1b23">More...</a><br /></td></tr>
<tr class="separator:ad841358ab36d4b3fc3b7cbc03d03be73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a738809ba50bf04973ec2b9af7160efbf" id="r_a738809ba50bf04973ec2b9af7160efbf"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#a9f4e405b9884be08e3a35bc06e3e15df">unpadded_size</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a738809ba50bf04973ec2b9af7160efbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unpadded size of this Block.  <a href="#a9f4e405b9884be08e3a35bc06e3e15df">More...</a><br /></td></tr>
<tr class="separator:a738809ba50bf04973ec2b9af7160efbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a459a9f1053c76be135f298e64623b414" id="r_a459a9f1053c76be135f298e64623b414"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="#ae164ca3d7492dcf5883769c38baac30e">total_size</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a459a9f1053c76be135f298e64623b414"><td class="mdescLeft">&#160;</td><td class="mdescRight">Total compressed size.  <a href="#ae164ca3d7492dcf5883769c38baac30e">More...</a><br /></td></tr>
<tr class="separator:a459a9f1053c76be135f298e64623b414"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a512428bded08c399908eb085ebdeb232" id="r_a512428bded08c399908eb085ebdeb232"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><b>block</b>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a512428bded08c399908eb085ebdeb232"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Iterator to get information about Blocks and Streams. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a223a046bcf09077a6e720967682deeae" name="a223a046bcf09077a6e720967682deeae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a223a046bcf09077a6e720967682deeae">&#9670;&#160;</a></span>flags</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a>* lzma_index_iter::flags</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to Stream Flags. </p>
<p>This is NULL if Stream Flags have not been set for this Stream with <a class="el" href="index_8h.html#a79a19669237f19f0b11c9f3be80a62b4" title="Set the Stream Flags.">lzma_index_stream_flags()</a>. </p>

</div>
</div>
<a id="a115a307dbc778a9de296376dc39c7b23" name="a115a307dbc778a9de296376dc39c7b23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a115a307dbc778a9de296376dc39c7b23">&#9670;&#160;</a></span>number</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::number</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Stream number in the <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e" title="Opaque data type to hold the Index(es) and other information.">lzma_index</a>. </p>
<p>The first Stream is 1. </p>

</div>
</div>
<a id="abc6ee9be23e54f31aed07382c8caaf7c" name="abc6ee9be23e54f31aed07382c8caaf7c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc6ee9be23e54f31aed07382c8caaf7c">&#9670;&#160;</a></span>block_count</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::block_count</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Number of Blocks in the Stream. </p>
<p>If this is zero, the block structure below has undefined values. </p>

</div>
</div>
<a id="a426705df8dde4b094a42f91ea20a46ac" name="a426705df8dde4b094a42f91ea20a46ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a426705df8dde4b094a42f91ea20a46ac">&#9670;&#160;</a></span>compressed_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::compressed_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compressed start offset of this Stream. </p>
<p>The offset is relative to the beginning of the <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e" title="Opaque data type to hold the Index(es) and other information.">lzma_index</a> (i.e. usually the beginning of the .xz file). </p>

</div>
</div>
<a id="abd374b748b4a42e122b90841709609bc" name="abd374b748b4a42e122b90841709609bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd374b748b4a42e122b90841709609bc">&#9670;&#160;</a></span>uncompressed_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::uncompressed_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Uncompressed start offset of this Stream. </p>
<p>The offset is relative to the beginning of the <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e" title="Opaque data type to hold the Index(es) and other information.">lzma_index</a> (i.e. usually the beginning of the .xz file). </p>

</div>
</div>
<a id="a6e73b1f37e3fcf1e9491e4a53b2c52c7" name="a6e73b1f37e3fcf1e9491e4a53b2c52c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e73b1f37e3fcf1e9491e4a53b2c52c7">&#9670;&#160;</a></span>compressed_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::compressed_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compressed size of this Stream. </p>
<p>This includes all headers except the possible Stream Padding after this Stream. </p>

</div>
</div>
<a id="aafc48408ed40060a84ecd66bae5e1b23" name="aafc48408ed40060a84ecd66bae5e1b23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aafc48408ed40060a84ecd66bae5e1b23">&#9670;&#160;</a></span>uncompressed_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::uncompressed_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Uncompressed size of this Stream. </p>
<p>Uncompressed size of this Block.</p>
<p>You should pass this to the Block decoder if you will decode this Block. It will allow the Block decoder to validate the uncompressed size. </p>

</div>
</div>
<a id="a48cfc856f283fe00b0df37402e012818" name="a48cfc856f283fe00b0df37402e012818"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a48cfc856f283fe00b0df37402e012818">&#9670;&#160;</a></span>padding</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::padding</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Size of Stream Padding after this Stream. </p>
<p>If it hasn't been set with <a class="el" href="index_8h.html#a3ed82f96c688f3c953f6509b6f4e2ef3" title="Set the amount of Stream Padding.">lzma_index_stream_padding()</a>, this defaults to zero. Stream Padding is always a multiple of four bytes. </p>

</div>
</div>
<a id="abe5333de53562189012d5ed084c0ef98" name="abe5333de53562189012d5ed084c0ef98"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe5333de53562189012d5ed084c0ef98">&#9670;&#160;</a></span>number_in_file</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::number_in_file</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Block number in the file. </p>
<p>The first Block is 1. </p>

</div>
</div>
<a id="a26436e75d4c2b5dd8d1de24140d8003e" name="a26436e75d4c2b5dd8d1de24140d8003e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a26436e75d4c2b5dd8d1de24140d8003e">&#9670;&#160;</a></span>compressed_file_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::compressed_file_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compressed start offset of this Block. </p>
<p>This offset is relative to the beginning of the <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e" title="Opaque data type to hold the Index(es) and other information.">lzma_index</a> (i.e. usually the beginning of the .xz file). Normally this is where you should seek in the .xz file to start decompressing this Block. </p>

</div>
</div>
<a id="a2f3ecf341b5dc043e9673759b8ff47b9" name="a2f3ecf341b5dc043e9673759b8ff47b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2f3ecf341b5dc043e9673759b8ff47b9">&#9670;&#160;</a></span>uncompressed_file_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::uncompressed_file_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Uncompressed start offset of this Block. </p>
<p>This offset is relative to the beginning of the <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e" title="Opaque data type to hold the Index(es) and other information.">lzma_index</a> (i.e. usually the beginning of the .xz file).</p>
<p>When doing random-access reading, it is possible that the target offset is not exactly at Block boundary. One will need to compare the target offset against uncompressed_file_offset or uncompressed_stream_offset, and possibly decode and throw away some amount of data before reaching the target offset. </p>

</div>
</div>
<a id="a35a752d344ff5d35d2a858a20bd6e5e8" name="a35a752d344ff5d35d2a858a20bd6e5e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a35a752d344ff5d35d2a858a20bd6e5e8">&#9670;&#160;</a></span>number_in_stream</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::number_in_stream</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Block number in this Stream. </p>
<p>The first Block is 1. </p>

</div>
</div>
<a id="a578bba553c43dc59a5e4032d4f6c89a3" name="a578bba553c43dc59a5e4032d4f6c89a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a578bba553c43dc59a5e4032d4f6c89a3">&#9670;&#160;</a></span>compressed_stream_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::compressed_stream_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compressed start offset of this Block. </p>
<p>This offset is relative to the beginning of the Stream containing this Block. </p>

</div>
</div>
<a id="a0fc4959fab08e1a6a4902c728c735a99" name="a0fc4959fab08e1a6a4902c728c735a99"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0fc4959fab08e1a6a4902c728c735a99">&#9670;&#160;</a></span>uncompressed_stream_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::uncompressed_stream_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Uncompressed start offset of this Block. </p>
<p>This offset is relative to the beginning of the Stream containing this Block. </p>

</div>
</div>
<a id="a9f4e405b9884be08e3a35bc06e3e15df" name="a9f4e405b9884be08e3a35bc06e3e15df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f4e405b9884be08e3a35bc06e3e15df">&#9670;&#160;</a></span>unpadded_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::unpadded_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unpadded size of this Block. </p>
<p>You should pass this to the Block decoder if you will decode this Block. It will allow the Block decoder to validate the unpadded size. </p>

</div>
</div>
<a id="ae164ca3d7492dcf5883769c38baac30e" name="ae164ca3d7492dcf5883769c38baac30e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae164ca3d7492dcf5883769c38baac30e">&#9670;&#160;</a></span>total_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::total_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Total compressed size. </p>
<p>This includes all headers and padding in this Block. This is useful if you need to know how many bytes the Block decoder will actually read. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="index_8h.html">index.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structlzma__index__iter.html">lzma_index_iter</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
