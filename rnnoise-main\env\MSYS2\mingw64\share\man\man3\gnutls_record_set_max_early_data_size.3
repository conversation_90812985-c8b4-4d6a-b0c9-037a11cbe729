.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_set_max_early_data_size" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_set_max_early_data_size \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_record_set_max_early_data_size(gnutls_session_t " session ", size_t " size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "size_t size" 12
is the new size
.SH "DESCRIPTION"
This function sets the maximum early data size in this connection.
This property can only be set to servers.  The client may be
provided with the maximum allowed size through the "early_data"
extension of the NewSessionTicket handshake message.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "SINCE"
3.6.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
