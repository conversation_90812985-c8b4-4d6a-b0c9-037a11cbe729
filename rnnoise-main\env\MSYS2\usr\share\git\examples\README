This directory used to contain scripted implementations of builtins
that have since been rewritten in C.

They have now been removed, but can be retrieved from an older commit
that removed them from this directory.

They're interesting for their reference value to any aspiring plumbing
users who want to learn how pieces can be fit together, but in many
cases have drifted enough from the actual implementations Git uses to
be instructive.

Other things that can be useful:

 * Some commands such as git-gc wrap other commands, and what they're
   doing behind the scenes can be seen by running them under
   GIT_TRACE=1

 * Doing `git log` on paths matching '*--helper.c' will show
   incremental effort in the direction of moving existing shell
   scripts to C.
