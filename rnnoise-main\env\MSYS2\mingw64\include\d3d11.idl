/*
 * Copyright 2010 <PERSON>
 * Copyright 2013 Austin English
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";
import "dxgi.idl";
import "d3dcommon.idl";

typedef D3D_PRIMITIVE D3D11_PRIMITIVE;
typedef D3D_PRIMITIVE_TOPOLOGY D3D11_PRIMITIVE_TOPOLOGY;
typedef D3D_SRV_DIMENSION D3D11_SRV_DIMENSION;
typedef RECT D3D11_RECT;

interface ID3D11Device;
interface ID3D11ClassLinkage;
interface ID3D11Resource;
interface ID3D11VideoProcessorInputView;

cpp_quote("#ifndef _D3D11_CONSTANTS")
cpp_quote("#define _D3D11_CONSTANTS")

const UINT D3D11_16BIT_INDEX_STRIP_CUT_VALUE = 0xffff;
const UINT D3D11_32BIT_INDEX_STRIP_CUT_VALUE = 0xffffffff;
const UINT D3D11_8BIT_INDEX_STRIP_CUT_VALUE = 0xff;

const UINT D3D11_ARRAY_AXIS_ADDRESS_RANGE_BIT_COUNT = 9;

const UINT D3D11_CLIP_OR_CULL_DISTANCE_COUNT = 8;
const UINT D3D11_CLIP_OR_CULL_DISTANCE_ELEMENT_COUNT = 2;

const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT      = 14;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_COMPONENTS          = 4;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_HW_SLOT_COUNT       = 15;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_PARTIAL_UPDATE_EXTENTS_BYTE_ALIGNMENT = 16;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COMPONENTS = 4;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COUNT      = 15;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READS_PER_INST = 1;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READ_PORTS = 1;
const UINT D3D11_COMMONSHADER_FLOWCONTROL_NESTING_LIMIT           = 64;
const UINT D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COMPONENTS = 4;
const UINT D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COUNT = 1;
const UINT D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READS_PER_INST = 1;
const UINT D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READ_PORTS = 1;
const UINT D3D11_COMMONSHADER_IMMEDIATE_VALUE_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_COMPONENTS  = 1;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_COUNT       = 128;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_READS_PER_INST = 1;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_READ_PORTS  = 1;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT           = 128;
const UINT D3D11_COMMONSHADER_SAMPLER_REGISTER_COMPONENTS         = 1;
const UINT D3D11_COMMONSHADER_SAMPLER_REGISTER_COUNT              = 16;
const UINT D3D11_COMMONSHADER_SAMPLER_REGISTER_READS_PER_INST     = 1;
const UINT D3D11_COMMONSHADER_SAMPLER_REGISTER_READ_PORTS         = 1;
const UINT D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT                  = 16;
const UINT D3D11_COMMONSHADER_SUBROUTINE_NESTING_LIMIT            = 32;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_COMPONENTS            = 4;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_COMPONENT_BIT_COUNT   = 32;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_COUNT                 = 4096;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_READS_PER_INST        = 3;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_READ_PORTS            = 3;
const UINT D3D11_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MAX        = 10;
const INT D3D11_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MIN         = -10;
const INT D3D11_COMMONSHADER_TEXEL_OFFSET_MAX_NEGATIVE            = -8;
const UINT D3D11_COMMONSHADER_TEXEL_OFFSET_MAX_POSITIVE           = 7;

const UINT D3D11_CS_4_X_BUCKET00_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 256;
const UINT D3D11_CS_4_X_BUCKET00_MAX_NUM_THREADS_PER_GROUP          = 64;
const UINT D3D11_CS_4_X_BUCKET01_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 240;
const UINT D3D11_CS_4_X_BUCKET01_MAX_NUM_THREADS_PER_GROUP          = 68;
const UINT D3D11_CS_4_X_BUCKET02_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 224;
const UINT D3D11_CS_4_X_BUCKET02_MAX_NUM_THREADS_PER_GROUP          = 72;
const UINT D3D11_CS_4_X_BUCKET03_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 208;
const UINT D3D11_CS_4_X_BUCKET03_MAX_NUM_THREADS_PER_GROUP          = 76;
const UINT D3D11_CS_4_X_BUCKET04_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 192;
const UINT D3D11_CS_4_X_BUCKET04_MAX_NUM_THREADS_PER_GROUP          = 84;
const UINT D3D11_CS_4_X_BUCKET05_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 176;
const UINT D3D11_CS_4_X_BUCKET05_MAX_NUM_THREADS_PER_GROUP          = 92;
const UINT D3D11_CS_4_X_BUCKET06_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 160;
const UINT D3D11_CS_4_X_BUCKET06_MAX_NUM_THREADS_PER_GROUP          = 100;
const UINT D3D11_CS_4_X_BUCKET07_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 144;
const UINT D3D11_CS_4_X_BUCKET07_MAX_NUM_THREADS_PER_GROUP          = 112;
const UINT D3D11_CS_4_X_BUCKET08_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 128;
const UINT D3D11_CS_4_X_BUCKET08_MAX_NUM_THREADS_PER_GROUP          = 128;
const UINT D3D11_CS_4_X_BUCKET09_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 112;
const UINT D3D11_CS_4_X_BUCKET09_MAX_NUM_THREADS_PER_GROUP          = 144;
const UINT D3D11_CS_4_X_BUCKET10_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 96;
const UINT D3D11_CS_4_X_BUCKET10_MAX_NUM_THREADS_PER_GROUP          = 168;
const UINT D3D11_CS_4_X_BUCKET11_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 80;
const UINT D3D11_CS_4_X_BUCKET11_MAX_NUM_THREADS_PER_GROUP          = 204;
const UINT D3D11_CS_4_X_BUCKET12_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 64;
const UINT D3D11_CS_4_X_BUCKET12_MAX_NUM_THREADS_PER_GROUP          = 256;
const UINT D3D11_CS_4_X_BUCKET13_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 48;
const UINT D3D11_CS_4_X_BUCKET13_MAX_NUM_THREADS_PER_GROUP          = 340;
const UINT D3D11_CS_4_X_BUCKET14_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 32;
const UINT D3D11_CS_4_X_BUCKET14_MAX_NUM_THREADS_PER_GROUP          = 512;
const UINT D3D11_CS_4_X_BUCKET15_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 16;
const UINT D3D11_CS_4_X_BUCKET15_MAX_NUM_THREADS_PER_GROUP          = 768;
const UINT D3D11_CS_4_X_DISPATCH_MAX_THREAD_GROUPS_IN_Z_DIMENSION   = 1;
const UINT D3D11_CS_4_X_RAW_UAV_BYTE_ALIGNMENT                      = 256;
const UINT D3D11_CS_4_X_THREAD_GROUP_MAX_THREADS_PER_GROUP          = 768;
const UINT D3D11_CS_4_X_THREAD_GROUP_MAX_X                          = 768;
const UINT D3D11_CS_4_X_THREAD_GROUP_MAX_Y                          = 768;
const UINT D3D11_CS_4_X_UAV_REGISTER_COUNT                          = 1;
const UINT D3D11_CS_DISPATCH_MAX_THREAD_GROUPS_PER_DIMENSION        = 65535;
const UINT D3D11_CS_TGSM_REGISTER_COUNT                             = 8192;
const UINT D3D11_CS_TGSM_REGISTER_READS_PER_INST                    = 1;
const UINT D3D11_CS_TGSM_RESOURCE_REGISTER_COMPONENTS               = 1;
const UINT D3D11_CS_TGSM_RESOURCE_REGISTER_READ_PORTS               = 1;
const UINT D3D11_CS_THREAD_GROUP_MAX_THREADS_PER_GROUP              = 1024;
const UINT D3D11_CS_THREAD_GROUP_MAX_X                              = 1024;
const UINT D3D11_CS_THREAD_GROUP_MAX_Y                              = 1024;
const UINT D3D11_CS_THREAD_GROUP_MAX_Z                              = 64;
const UINT D3D11_CS_THREAD_GROUP_MIN_X                              = 1;
const UINT D3D11_CS_THREAD_GROUP_MIN_Y                              = 1;
const UINT D3D11_CS_THREAD_GROUP_MIN_Z                              = 1;
const UINT D3D11_CS_THREAD_LOCAL_TEMP_REGISTER_POOL                 = 16384;

const UINT D3D11_DEFAULT_DEPTH_BIAS          = 0;
cpp_quote("#define D3D11_DEFAULT_DEPTH_BIAS_CLAMP 0.0f")
const UINT D3D11_DEFAULT_MAX_ANISOTROPY      = 16;
cpp_quote("#define D3D11_DEFAULT_MIP_LOD_BIAS 0.0f")
const UINT D3D11_DEFAULT_RENDER_TARGET_ARRAY_INDEX = 0;
const UINT D3D11_DEFAULT_SAMPLE_MASK         = 0xffffffff;
const UINT D3D11_DEFAULT_SCISSOR_ENDX        = 0;
const UINT D3D11_DEFAULT_SCISSOR_ENDY        = 0;
const UINT D3D11_DEFAULT_SCISSOR_STARTX      = 0;
const UINT D3D11_DEFAULT_SCISSOR_STARTY      = 0;
cpp_quote("#define D3D11_DEFAULT_SLOPE_SCALED_DEPTH_BIAS 0.0f")
const UINT D3D11_DEFAULT_STENCIL_READ_MASK   = 0xff;
const UINT D3D11_DEFAULT_STENCIL_REFERENCE   = 0;
const UINT D3D11_DEFAULT_STENCIL_WRITE_MASK  = 0xff;
const UINT D3D11_DEFAULT_VIEWPORT_AND_SCISSORRECT_INDEX = 0;
const UINT D3D11_DEFAULT_VIEWPORT_HEIGHT     = 0;
cpp_quote("#define D3D11_DEFAULT_VIEWPORT_MAX_DEPTH 0.0f")
cpp_quote("#define D3D11_DEFAULT_VIEWPORT_MIN_DEPTH 0.0f")
const UINT D3D11_DEFAULT_VIEWPORT_TOPLEFTX   = 0;
const UINT D3D11_DEFAULT_VIEWPORT_TOPLEFTY   = 0;
const UINT D3D11_DEFAULT_VIEWPORT_WIDTH      = 0;

cpp_quote("#define D3D11_FLOAT32_MAX         (3.402823466e+38f)")

const UINT D3D11_IA_DEFAULT_INDEX_BUFFER_OFFSET_IN_BYTES = 0;
const UINT D3D11_IA_DEFAULT_PRIMITIVE_TOPOLOGY = 0;
const UINT D3D11_IA_DEFAULT_VERTEX_BUFFER_OFFSET_IN_BYTES = 0;
const UINT D3D11_IA_INDEX_INPUT_RESOURCE_SLOT_COUNT = 1;
const UINT D3D11_IA_INSTANCE_ID_BIT_COUNT = 32;
const UINT D3D11_IA_INTEGER_ARITHMETIC_BIT_COUNT = 32;
const UINT D3D11_IA_PATCH_MAX_CONTROL_POINT_COUNT = 32;
const UINT D3D11_IA_PRIMITIVE_ID_BIT_COUNT = 32;
const UINT D3D11_IA_VERTEX_ID_BIT_COUNT = 32;
const UINT D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT = 32;
const UINT D3D11_IA_VERTEX_INPUT_STRUCTURE_ELEMENTS_COMPONENTS = 128;
const UINT D3D11_IA_VERTEX_INPUT_STRUCTURE_ELEMENT_COUNT = 32;

const unsigned int D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT = 8;

const UINT D3D11_MAX_MAXANISOTROPY           = 16;
const UINT D3D11_MAX_MULTISAMPLE_SAMPLE_COUNT = 32;
const UINT D3D11_VIEWPORT_BOUNDS_MAX         = 32767;
const INT D3D11_VIEWPORT_BOUNDS_MIN          = -32768;
const UINT D3D11_VIEWPORT_AND_SCISSORRECT_MAX_INDEX = 15;
const UINT D3D11_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE = 16;

const UINT D3D11_KEEP_RENDER_TARGETS_AND_DEPTH_STENCIL = 0xffffffff;
const UINT D3D11_KEEP_UNORDERED_ACCESS_VIEWS           = 0xffffffff;

const UINT D3D11_SHADER_MAJOR_VERSION        = 5;
const UINT D3D11_SHADER_MAX_INSTANCES        = 65535;
const UINT D3D11_SHADER_MAX_INTERFACES       = 253;
const UINT D3D11_SHADER_MAX_INTERFACE_CALL_SITES = 4096;
const UINT D3D11_SHADER_MAX_TYPES            = 65535;
const UINT D3D11_SHADER_MINOR_VERSION        = 0;
const UINT D3D11_VS_OUTPUT_REGISTER_COUNT    = 32;

const UINT D3D11_OMAC_SIZE = 16;

const UINT D3D11_PS_CS_UAV_REGISTER_COMPONENTS         = 1;
const UINT D3D11_PS_CS_UAV_REGISTER_COUNT              = 8;
const UINT D3D11_PS_CS_UAV_REGISTER_READS_PER_INST     = 1;
const UINT D3D11_PS_CS_UAV_REGISTER_READ_PORTS         = 1;
const UINT D3D11_PS_FRONTFACING_DEFAULT_VALUE          = 0xffffffff;
const UINT D3D11_PS_FRONTFACING_FALSE_VALUE            = 0;
const UINT D3D11_PS_FRONTFACING_TRUE_VALUE             = 0xffffffff;
const UINT D3D11_PS_INPUT_REGISTER_COMPONENTS          = 4;
const UINT D3D11_PS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_PS_INPUT_REGISTER_COUNT               = 32;
const UINT D3D11_PS_INPUT_REGISTER_READS_PER_INST      = 2;
const UINT D3D11_PS_INPUT_REGISTER_READ_PORTS          = 1;
cpp_quote("#define D3D11_PS_LEGACY_PIXEL_CENTER_FRACTIONAL_COMPONENT (0.0f)")
const UINT D3D11_PS_OUTPUT_DEPTH_REGISTER_COMPONENTS   = 1;
const UINT D3D11_PS_OUTPUT_DEPTH_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_PS_OUTPUT_DEPTH_REGISTER_COUNT        = 1;
const UINT D3D11_PS_OUTPUT_MASK_REGISTER_COMPONENTS    = 1;
const UINT D3D11_PS_OUTPUT_MASK_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_PS_OUTPUT_MASK_REGISTER_COUNT         = 1;
const UINT D3D11_PS_OUTPUT_REGISTER_COMPONENTS         = 4;
const UINT D3D11_PS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_PS_OUTPUT_REGISTER_COUNT              = 8;
cpp_quote("#define D3D11_PS_PIXEL_CENTER_FRACTIONAL_COMPONENT (0.5f)")

const UINT D3D11_RAW_UAV_SRV_BYTE_ALIGNMENT            = 16;

const UINT D3D11_REQ_BLEND_OBJECT_COUNT_PER_DEVICE     = 4096;
const UINT D3D11_REQ_BUFFER_RESOURCE_TEXEL_COUNT_2_TO_EXP = 27;
const UINT D3D11_REQ_CONSTANT_BUFFER_ELEMENT_COUNT     = 4096;
const UINT D3D11_REQ_DEPTH_STENCIL_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D11_REQ_DRAWINDEXED_INDEX_COUNT_2_TO_EXP  = 32;
const UINT D3D11_REQ_DRAW_VERTEX_COUNT_2_TO_EXP        = 32;
const UINT D3D11_REQ_FILTERING_HW_ADDRESSABLE_RESOURCE_DIMENSION = 16384;
const UINT D3D11_REQ_GS_INVOCATION_32BIT_OUTPUT_COMPONENT_LIMIT = 1024;
const UINT D3D11_REQ_IMMEDIATE_CONSTANT_BUFFER_ELEMENT_COUNT = 4096;
const UINT D3D11_REQ_MAXANISOTROPY                     = 16;
const UINT D3D11_REQ_MIP_LEVELS                        = 15;
const UINT D3D11_REQ_MULTI_ELEMENT_STRUCTURE_SIZE_IN_BYTES = 2048;
const UINT D3D11_REQ_RASTERIZER_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D11_REQ_RENDER_TO_BUFFER_WINDOW_WIDTH     = 16384;
const UINT D3D11_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_A_TERM = 128;
cpp_quote("#define D3D11_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_B_TERM (0.25f)")
const UINT D3D11_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_C_TERM = 2048;
const UINT D3D11_REQ_RESOURCE_VIEW_COUNT_PER_DEVICE_2_TO_EXP = 20;
const UINT D3D11_REQ_SAMPLER_OBJECT_COUNT_PER_DEVICE   = 4096;
const UINT D3D11_REQ_TEXTURE1D_ARRAY_AXIS_DIMENSION    = 2048;
const UINT D3D11_REQ_TEXTURE1D_U_DIMENSION             = 16384;
const UINT D3D11_REQ_TEXTURE2D_ARRAY_AXIS_DIMENSION    = 2048;
const UINT D3D11_REQ_TEXTURE2D_U_OR_V_DIMENSION        = 16384;
const UINT D3D11_REQ_TEXTURE3D_U_V_OR_W_DIMENSION      = 2048;
const UINT D3D11_REQ_TEXTURECUBE_DIMENSION             = 16384;

const UINT D3D11_RESINFO_INSTRUCTION_MISSING_COMPONENT_RETVAL = 0;

const UINT D3D11_SHIFT_INSTRUCTION_PAD_VALUE           = 0;
const UINT D3D11_SHIFT_INSTRUCTION_SHIFT_VALUE_BIT_COUNT = 5;

const UINT D3D11_SO_BUFFER_MAX_STRIDE_IN_BYTES         = 2048;
const UINT D3D11_SO_BUFFER_MAX_WRITE_WINDOW_IN_BYTES   = 512;
const UINT D3D11_SO_BUFFER_SLOT_COUNT                  = 4;
const UINT D3D11_SO_DDI_REGISTER_INDEX_DENOTING_GAP    = 0xffffffff;
const UINT D3D11_SO_NO_RASTERIZED_STREAM               = 0xffffffff;
const UINT D3D11_SO_OUTPUT_COMPONENT_COUNT             = 128;
const UINT D3D11_SO_STREAM_COUNT                       = 4;

const UINT D3D11_SPEC_DATE_DAY   = 16;
const UINT D3D11_SPEC_DATE_MONTH = 05;
const UINT D3D11_SPEC_DATE_YEAR  = 2011;

cpp_quote("#define D3D11_SPEC_VERSION                   (1.07)")
cpp_quote("#define D3D11_SRGB_GAMMA                     (2.2f)")
cpp_quote("#define D3D11_SRGB_TO_FLOAT_DENOMINATOR_1    (12.92f)")
cpp_quote("#define D3D11_SRGB_TO_FLOAT_DENOMINATOR_2    (1.055f)")
cpp_quote("#define D3D11_SRGB_TO_FLOAT_EXPONENT         (2.4f)")
cpp_quote("#define D3D11_SRGB_TO_FLOAT_OFFSET           (0.055f)")
cpp_quote("#define D3D11_SRGB_TO_FLOAT_THRESHOLD        (0.04045f)")
cpp_quote("#define D3D11_SRGB_TO_FLOAT_TOLERANCE_IN_ULP (0.5f)")

const UINT D3D11_STANDARD_COMPONENT_BIT_COUNT                     = 32;
const UINT D3D11_STANDARD_COMPONENT_BIT_COUNT_DOUBLED             = 64;
const UINT D3D11_STANDARD_MAXIMUM_ELEMENT_ALIGNMENT_BYTE_MULTIPLE = 4;
const UINT D3D11_STANDARD_PIXEL_COMPONENT_COUNT                   = 128;
const UINT D3D11_STANDARD_PIXEL_ELEMENT_COUNT                     = 32;
const UINT D3D11_STANDARD_VECTOR_SIZE                             = 4;
const UINT D3D11_STANDARD_VERTEX_ELEMENT_COUNT                    = 32;
const UINT D3D11_STANDARD_VERTEX_TOTAL_COMPONENT_COUNT            = 64;

cpp_quote("#endif")

cpp_quote("#ifndef _D3D11_1_CONSTANTS")
cpp_quote("#define _D3D11_1_CONSTANTS")
const UINT D3D11_1_UAV_SLOT_COUNT = 64;
cpp_quote("#endif")

cpp_quote("#ifndef _D3D11_2_CONSTANTS")
cpp_quote("#define _D3D11_2_CONSTANTS")
const UINT D3D11_2_TILED_RESOURCE_TILE_SIZE_IN_BYTES = 0x10000;
cpp_quote("#endif")

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)")
cpp_quote("struct CD3D11_DEFAULT {};")
cpp_quote("extern const DECLSPEC_SELECTANY CD3D11_DEFAULT D3D11_DEFAULT;")
cpp_quote("#endif")

typedef enum D3D11_BLEND
{
    D3D11_BLEND_ZERO = 1,
    D3D11_BLEND_ONE = 2,
    D3D11_BLEND_SRC_COLOR = 3,
    D3D11_BLEND_INV_SRC_COLOR = 4,
    D3D11_BLEND_SRC_ALPHA = 5,
    D3D11_BLEND_INV_SRC_ALPHA = 6,
    D3D11_BLEND_DEST_ALPHA = 7,
    D3D11_BLEND_INV_DEST_ALPHA = 8,
    D3D11_BLEND_DEST_COLOR = 9,
    D3D11_BLEND_INV_DEST_COLOR = 10,
    D3D11_BLEND_SRC_ALPHA_SAT = 11,
    D3D11_BLEND_BLEND_FACTOR = 14,
    D3D11_BLEND_INV_BLEND_FACTOR = 15,
    D3D11_BLEND_SRC1_COLOR = 16,
    D3D11_BLEND_INV_SRC1_COLOR = 17,
    D3D11_BLEND_SRC1_ALPHA = 18,
    D3D11_BLEND_INV_SRC1_ALPHA = 19,
} D3D11_BLEND;

typedef enum D3D11_BLEND_OP
{
    D3D11_BLEND_OP_ADD = 1,
    D3D11_BLEND_OP_SUBTRACT,
    D3D11_BLEND_OP_REV_SUBTRACT,
    D3D11_BLEND_OP_MIN,
    D3D11_BLEND_OP_MAX
} D3D11_BLEND_OP;

typedef enum D3D11_VIDEO_DECODER_BUFFER_TYPE
{
    D3D11_VIDEO_DECODER_BUFFER_PICTURE_PARAMETERS = 0,
    D3D11_VIDEO_DECODER_BUFFER_MACROBLOCK_CONTROL = 1,
    D3D11_VIDEO_DECODER_BUFFER_RESIDUAL_DIFFERENCE = 2,
    D3D11_VIDEO_DECODER_BUFFER_DEBLOCKING_CONTROL = 3,
    D3D11_VIDEO_DECODER_BUFFER_INVERSE_QUANTIZATION_MATRIX = 4,
    D3D11_VIDEO_DECODER_BUFFER_SLICE_CONTROL = 5,
    D3D11_VIDEO_DECODER_BUFFER_BITSTREAM = 6,
    D3D11_VIDEO_DECODER_BUFFER_MOTION_VECTOR = 7,
    D3D11_VIDEO_DECODER_BUFFER_FILM_GRAIN = 8,
} D3D11_VIDEO_DECODER_BUFFER_TYPE;

typedef enum D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE
{
    D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE_OPAQUE = 0,
    D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE_BACKGROUND = 1,
    D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE_DESTINATION = 2,
    D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE_SOURCE_STREAM = 3,
} D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE;

typedef enum D3D11_VIDEO_PROCESSOR_OUTPUT_RATE
{
    D3D11_VIDEO_PROCESSOR_OUTPUT_RATE_NORMAL = 0,
    D3D11_VIDEO_PROCESSOR_OUTPUT_RATE_HALF = 1,
    D3D11_VIDEO_PROCESSOR_OUTPUT_RATE_CUSTOM = 2,
} D3D11_VIDEO_PROCESSOR_OUTPUT_RATE;

typedef enum D3D11_VIDEO_PROCESSOR_STEREO_FORMAT
{
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_MONO = 0,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_HORIZONTAL = 1,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_VERTICAL = 2,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_SEPARATE = 3,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_MONO_OFFSET = 4,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_ROW_INTERLEAVED = 5,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_COLUMN_INTERLEAVED = 6,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_CHECKERBOARD = 7,
} D3D11_VIDEO_PROCESSOR_STEREO_FORMAT;

typedef enum D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE
{
    D3D11_VIDEO_PROCESSOR_STEREO_FLIP_NONE = 0,
    D3D11_VIDEO_PROCESSOR_STEREO_FLIP_FRAME0 = 1,
    D3D11_VIDEO_PROCESSOR_STEREO_FLIP_FRAME1 = 2,
} D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE;

typedef enum D3D11_VIDEO_PROCESSOR_ROTATION
{
    D3D11_VIDEO_PROCESSOR_ROTATION_IDENTITY = 0,
    D3D11_VIDEO_PROCESSOR_ROTATION_90 = 1,
    D3D11_VIDEO_PROCESSOR_ROTATION_180 = 2,
    D3D11_VIDEO_PROCESSOR_ROTATION_270 = 3,
} D3D11_VIDEO_PROCESSOR_ROTATION;

typedef enum D3D11_VIDEO_PROCESSOR_DEVICE_CAPS
{
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_LINEAR_SPACE                          = 0x00000001,
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_xvYCC                                 = 0x00000002,
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_RGB_RANGE_CONVERSION                  = 0x00000004,
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_YCbCr_MATRIX_CONVERSION               = 0x00000008,
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_NOMINAL_RANGE                         = 0x00000010,
} D3D11_VIDEO_PROCESSOR_DEVICE_CAPS;

typedef enum D3D11_VIDEO_PROCESSOR_FEATURE_CAPS
{
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_ALPHA_FILL                           = 0x00000001,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_CONSTRICTION                         = 0x00000002,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_LUMA_KEY                             = 0x00000004,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_ALPHA_PALETTE                        = 0x00000008,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_LEGACY                               = 0x00000010,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_STEREO                               = 0x00000020,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_ROTATION                             = 0x00000040,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_ALPHA_STREAM                         = 0x00000080,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_PIXEL_ASPECT_RATIO                   = 0x00000100,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_MIRROR                               = 0x00000200,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_SHADER_USAGE                         = 0x00000400,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_METADATA_HDR10                       = 0x00000800,
} D3D11_VIDEO_PROCESSOR_FEATURE_CAPS;

typedef enum D3D11_VIDEO_PROCESSOR_FILTER_CAPS
{
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_BRIGHTNESS                            = 0x00000001,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_CONTRAST                              = 0x00000002,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_HUE                                   = 0x00000004,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_SATURATION                            = 0x00000008,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_NOISE_REDUCTION                       = 0x00000010,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_EDGE_ENHANCEMENT                      = 0x00000020,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_ANAMORPHIC_SCALING                    = 0x00000040,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_STEREO_ADJUSTMENT                     = 0x00000080,
} D3D11_VIDEO_PROCESSOR_FILTER_CAPS;

typedef enum D3D11_VIDEO_PROCESSOR_FORMAT_CAPS
{
    D3D11_VIDEO_PROCESSOR_FORMAT_CAPS_RGB_INTERLACED                        = 0x00000001,
    D3D11_VIDEO_PROCESSOR_FORMAT_CAPS_RGB_PROCAMP                           = 0x00000002,
    D3D11_VIDEO_PROCESSOR_FORMAT_CAPS_RGB_LUMA_KEY                          = 0x00000004,
    D3D11_VIDEO_PROCESSOR_FORMAT_CAPS_PALETTE_INTERLACED                    = 0x00000008,
} D3D11_VIDEO_PROCESSOR_FORMAT_CAPS;

typedef enum D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS
{
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_DENOISE                          = 0x00000001,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_DERINGING                        = 0x00000002,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_EDGE_ENHANCEMENT                 = 0x00000004,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_COLOR_CORRECTION                 = 0x00000008,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_FLESH_TONE_MAPPING               = 0x00000010,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_IMAGE_STABILIZATION              = 0x00000020,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_SUPER_RESOLUTION                 = 0x00000040,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_ANAMORPHIC_SCALING               = 0x00000080,
} D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS;

typedef enum D3D11_VIDEO_PROCESSOR_STEREO_CAPS
{
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_MONO_OFFSET                           = 0x00000001,
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_ROW_INTERLEAVED                       = 0x00000002,
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_COLUMN_INTERLEAVED                    = 0x00000004,
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_CHECKERBOARD                          = 0x00000008,
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_FLIP_MODE                             = 0x00000010,
} D3D11_VIDEO_PROCESSOR_STEREO_CAPS;

typedef enum D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS
{
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_DEINTERLACE_BLEND                  = 0x01,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_DEINTERLACE_BOB                    = 0x02,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_DEINTERLACE_ADAPTIVE               = 0x04,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_DEINTERLACE_MOTION_COMPENSATION    = 0x08,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_INVERSE_TELECINE                   = 0x10,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_FRAME_RATE_CONVERSION              = 0x20,
} D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS;

typedef enum D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS
{
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_32                                 = 0x00000001,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_22                                 = 0x00000002,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_2224                               = 0x00000004,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_2332                               = 0x00000008,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_32322                              = 0x00000010,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_55                                 = 0x00000020,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_64                                 = 0x00000040,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_87                                 = 0x00000080,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_222222222223                       = 0x00000100,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_OTHER                              = 0x80000000,
} D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS;

typedef enum D3D11_CONTENT_PROTECTION_CAPS
{
    D3D11_CONTENT_PROTECTION_CAPS_SOFTWARE                                  = 0x00000001,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE                                  = 0x00000002,
    D3D11_CONTENT_PROTECTION_CAPS_PROTECTION_ALWAYS_ON                      = 0x00000004,
    D3D11_CONTENT_PROTECTION_CAPS_PARTIAL_DECRYPTION                        = 0x00000008,
    D3D11_CONTENT_PROTECTION_CAPS_CONTENT_KEY                               = 0x00000010,
    D3D11_CONTENT_PROTECTION_CAPS_FRESHEN_SESSION_KEY                       = 0x00000020,
    D3D11_CONTENT_PROTECTION_CAPS_ENCRYPTED_READ_BACK                       = 0x00000040,
    D3D11_CONTENT_PROTECTION_CAPS_ENCRYPTED_READ_BACK_KEY                   = 0x00000080,
    D3D11_CONTENT_PROTECTION_CAPS_SEQUENTIAL_CTR_IV                         = 0x00000100,
    D3D11_CONTENT_PROTECTION_CAPS_ENCRYPT_SLICEDATA_ONLY                    = 0x00000200,
    D3D11_CONTENT_PROTECTION_CAPS_DECRYPTION_BLT                            = 0x00000400,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE_PROTECT_UNCOMPRESSED             = 0x00000800,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE_PROTECTED_MEMORY_PAGEABLE        = 0x00001000,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE_TEARDOWN                         = 0x00002000,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE_DRM_COMMUNICATION                = 0x00004000,
} D3D11_CONTENT_PROTECTION_CAPS;

typedef enum D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE
{
    D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE_UNDEFINED                           = 0x00000000,
    D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE_16_235                              = 0x00000001,
    D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE_0_255                               = 0x00000002,
} D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE;

typedef enum D3D11_AUTHENTICATED_PROCESS_IDENTIFIER_TYPE
{
    D3D11_PROCESSIDTYPE_UNKNOWN                                             = 0x00000000,
    D3D11_PROCESSIDTYPE_DWM                                                 = 0x00000001,
    D3D11_PROCESSIDTYPE_HANDLE                                              = 0x00000002,
} D3D11_AUTHENTICATED_PROCESS_IDENTIFIER_TYPE;

typedef enum D3D11_BUS_TYPE
{
    D3D11_BUS_TYPE_OTHER                                                    = 0x00000000,
    D3D11_BUS_TYPE_PCI                                                      = 0x00000001,
    D3D11_BUS_TYPE_PCIX                                                     = 0x00000002,
    D3D11_BUS_TYPE_PCIEXPRESS                                               = 0x00000003,
    D3D11_BUS_TYPE_AGP                                                      = 0x00000004,
    D3D11_BUS_IMPL_MODIFIER_INSIDE_OF_CHIPSET                               = 0x00010000,
    D3D11_BUS_IMPL_MODIFIER_TRACKS_ON_MOTHER_BOARD_TO_CHIP                  = 0x00020000,
    D3D11_BUS_IMPL_MODIFIER_TRACKS_ON_MOTHER_BOARD_TO_SOCKET                = 0x00030000,
    D3D11_BUS_IMPL_MODIFIER_DAUGHTER_BOARD_CONNECTOR                        = 0x00040000,
    D3D11_BUS_IMPL_MODIFIER_DAUGHTER_BOARD_CONNECTOR_INSIDE_OF_NUAE         = 0x00050000,
    D3D11_BUS_IMPL_MODIFIER_NON_STANDARD                                    = 0x80000000,
} D3D11_BUS_TYPE;

typedef struct D3D11_BOX
{
    UINT left;
    UINT top;
    UINT front;
    UINT right;
    UINT bottom;
    UINT back;
} D3D11_BOX;

typedef struct D3D11_BUFFER_RTV
{
    union
    {
        UINT FirstElement;
        UINT ElementOffset;
    };
    union
    {
        UINT NumElements;
        UINT ElementWidth;
    };
} D3D11_BUFFER_RTV;

typedef struct D3D11_BUFFER_SRV
{
    union
    {
        UINT FirstElement;
        UINT ElementOffset;
    };
    union
    {
        UINT NumElements;
        UINT ElementWidth;
    };
} D3D11_BUFFER_SRV;

typedef struct D3D11_BUFFER_UAV
{
    UINT FirstElement;
    UINT NumElements;
    UINT Flags;
} D3D11_BUFFER_UAV;

typedef struct D3D11_BUFFEREX_SRV
{
    UINT FirstElement;
    UINT NumElements;
    UINT Flags;
} D3D11_BUFFEREX_SRV;

typedef struct D3D11_CLASS_INSTANCE_DESC
{
    UINT InstanceId;
    UINT InstanceIndex;
    UINT TypeId;
    UINT ConstantBuffer;
    UINT BaseConstantBufferOffset;
    UINT BaseTexture;
    UINT BaseSampler;
    BOOL Created;
} D3D11_CLASS_INSTANCE_DESC;

typedef enum D3D11_COMPARISON_FUNC
{
    D3D11_COMPARISON_NEVER = 1,
    D3D11_COMPARISON_LESS,
    D3D11_COMPARISON_EQUAL,
    D3D11_COMPARISON_LESS_EQUAL,
    D3D11_COMPARISON_GREATER,
    D3D11_COMPARISON_NOT_EQUAL,
    D3D11_COMPARISON_GREATER_EQUAL,
    D3D11_COMPARISON_ALWAYS
} D3D11_COMPARISON_FUNC;

typedef enum D3D11_COUNTER
{
    D3D11_COUNTER_DEVICE_DEPENDENT_0 = 0x40000000,
} D3D11_COUNTER;

typedef struct D3D11_COUNTER_DESC
{
    D3D11_COUNTER Counter;
    UINT MiscFlags;
} D3D11_COUNTER_DESC;

typedef struct D3D11_COUNTER_INFO
{
    D3D11_COUNTER LastDeviceDependentCounter;
    UINT NumSimultaneousCounters;
    UINT8 NumDetectableParallelUnits;
} D3D11_COUNTER_INFO;

typedef enum D3D11_COUNTER_TYPE
{
    D3D11_COUNTER_TYPE_FLOAT32,
    D3D11_COUNTER_TYPE_UINT16,
    D3D11_COUNTER_TYPE_UINT32,
    D3D11_COUNTER_TYPE_UINT64,
} D3D11_COUNTER_TYPE;

typedef enum D3D11_CULL_MODE
{
    D3D11_CULL_NONE = 1,
    D3D11_CULL_FRONT,
    D3D11_CULL_BACK
} D3D11_CULL_MODE;

typedef enum D3D11_DEPTH_WRITE_MASK
{
    D3D11_DEPTH_WRITE_MASK_ZERO,
    D3D11_DEPTH_WRITE_MASK_ALL,
} D3D11_DEPTH_WRITE_MASK;

typedef enum D3D11_STANDARD_MULTISAMPLE_QUALITY_LEVELS
{
    D3D11_STANDARD_MULTISAMPLE_PATTERN = 0xffffffff,
    D3D11_CENTER_MULTISAMPLE_PATTERN = 0xfffffffe,
} D3D11_STANDARD_MULTISAMPLE_QUALITY_LEVELS;

typedef enum D3D11_DEVICE_CONTEXT_TYPE
{
    D3D11_DEVICE_CONTEXT_IMMEDIATE,
    D3D11_DEVICE_CONTEXT_DEFERRED,
} D3D11_DEVICE_CONTEXT_TYPE;

typedef enum D3D11_DSV_DIMENSION
{
    D3D11_DSV_DIMENSION_UNKNOWN,
    D3D11_DSV_DIMENSION_TEXTURE1D,
    D3D11_DSV_DIMENSION_TEXTURE1DARRAY,
    D3D11_DSV_DIMENSION_TEXTURE2D,
    D3D11_DSV_DIMENSION_TEXTURE2DARRAY,
    D3D11_DSV_DIMENSION_TEXTURE2DMS,
    D3D11_DSV_DIMENSION_TEXTURE2DMSARRAY,
} D3D11_DSV_DIMENSION;

typedef enum D3D11_FEATURE
{
    D3D11_FEATURE_THREADING,
    D3D11_FEATURE_DOUBLES,
    D3D11_FEATURE_FORMAT_SUPPORT,
    D3D11_FEATURE_FORMAT_SUPPORT2,
    D3D11_FEATURE_D3D10_X_HARDWARE_OPTIONS,
    D3D11_FEATURE_D3D11_OPTIONS,
    D3D11_FEATURE_ARCHITECTURE_INFO,
    D3D11_FEATURE_D3D9_OPTIONS,
    D3D11_FEATURE_SHADER_MIN_PRECISION_SUPPORT,
    D3D11_FEATURE_D3D9_SHADOW_SUPPORT,
    D3D11_FEATURE_D3D11_OPTIONS1,
    D3D11_FEATURE_D3D9_SIMPLE_INSTANCING_SUPPORT,
    D3D11_FEATURE_MARKER_SUPPORT,
    D3D11_FEATURE_D3D9_OPTIONS1,
    D3D11_FEATURE_D3D11_OPTIONS2,
    D3D11_FEATURE_D3D11_OPTIONS3,
    D3D11_FEATURE_GPU_VIRTUAL_ADDRESS_SUPPORT,
    D3D11_FEATURE_D3D11_OPTIONS4,
    D3D11_FEATURE_SHADER_CACHE,
    D3D11_FEATURE_D3D11_OPTIONS5,
} D3D11_FEATURE;

typedef struct D3D11_FEATURE_DATA_THREADING
{
    BOOL DriverConcurrentCreates;
    BOOL DriverCommandLists;
} D3D11_FEATURE_DATA_THREADING;

typedef struct D3D11_FEATURE_DATA_DOUBLES
{
    BOOL DoublePrecisionFloatShaderOps;
} D3D11_FEATURE_DATA_DOUBLES;

typedef struct D3D11_FEATURE_DATA_FORMAT_SUPPORT
{
    DXGI_FORMAT InFormat;
    UINT OutFormatSupport;
} D3D11_FEATURE_DATA_FORMAT_SUPPORT;

typedef struct D3D11_FEATURE_DATA_FORMAT_SUPPORT2
{
    DXGI_FORMAT InFormat;
    UINT OutFormatSupport2;
} D3D11_FEATURE_DATA_FORMAT_SUPPORT2;

typedef struct D3D11_FEATURE_DATA_D3D10_X_HARDWARE_OPTIONS
{
    BOOL ComputeShaders_Plus_RawAndStructuredBuffers_Via_Shader_4_x;
} D3D11_FEATURE_DATA_D3D10_X_HARDWARE_OPTIONS;

typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS
{
    BOOL OutputMergerLogicOp;
    BOOL UAVOnlyRenderingForcedSampleCount;
    BOOL DiscardAPIsSeenByDriver;
    BOOL FlagsForUpdateAndCopySeenByDriver;
    BOOL ClearView;
    BOOL CopyWithOverlap;
    BOOL ConstantBufferPartialUpdate;
    BOOL ConstantBufferOffsetting;
    BOOL MapNoOverwriteOnDynamicConstantBuffer;
    BOOL MapNoOverwriteOnDynamicBufferSRV;
    BOOL MultisampleRTVWithForcedSampleCountOne;
    BOOL SAD4ShaderInstructions;
    BOOL ExtendedDoublesShaderInstructions;
    BOOL ExtendedResourceSharing;
} D3D11_FEATURE_DATA_D3D11_OPTIONS;

typedef struct D3D11_FEATURE_DATA_ARCHITECTURE_INFO
{
    BOOL TileBasedDeferredRenderer;
} D3D11_FEATURE_DATA_ARCHITECTURE_INFO;

typedef struct D3D11_FEATURE_DATA_D3D9_OPTIONS
{
    BOOL FullNonPow2TextureSupport;
} D3D11_FEATURE_DATA_D3D9_OPTIONS;

typedef struct D3D11_FEATURE_DATA_D3D9_SHADOW_SUPPORT
{
    BOOL SupportsDepthAsTextureWithLessEqualComparisonFilter;
} D3D11_FEATURE_DATA_D3D9_SHADOW_SUPPORT;

typedef enum D3D11_SHADER_MIN_PRECISION_SUPPORT
{
    D3D11_SHADER_MIN_PRECISION_10_BIT = 0x1,
    D3D11_SHADER_MIN_PRECISION_16_BIT = 0x2,
} D3D11_SHADER_MIN_PRECISION_SUPPORT;

typedef struct D3D11_FEATURE_DATA_SHADER_MIN_PRECISION_SUPPORT
{
    UINT PixelShaderMinPrecision;
    UINT AllOtherShaderStagesMinPrecision;
} D3D11_FEATURE_DATA_SHADER_MIN_PRECISION_SUPPORT;

typedef enum D3D11_TILED_RESOURCES_TIER
{
    D3D11_TILED_RESOURCES_NOT_SUPPORTED = 0x0,
    D3D11_TILED_RESOURCES_TIER_1 = 0x1,
    D3D11_TILED_RESOURCES_TIER_2 = 0x2,
    D3D11_TILED_RESOURCES_TIER_3 = 0x3,
} D3D11_TILED_RESOURCES_TIER;

typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS1
{
    D3D11_TILED_RESOURCES_TIER TiledResourcesTier;
    BOOL MinMaxFiltering;
    BOOL ClearViewAlsoSupportsDepthOnlyFormats;
    BOOL MapOnDefaultBuffers;
} D3D11_FEATURE_DATA_D3D11_OPTIONS1;

typedef struct D3D11_FEATURE_DATA_D3D9_SIMPLE_INSTANCING_SUPPORT
{
    BOOL SimpleInstancingSupported;
} D3D11_FEATURE_DATA_D3D9_SIMPLE_INSTANCING_SUPPORT;

typedef struct D3D11_FEATURE_DATA_MARKER_SUPPORT
{
    BOOL Profile;
} D3D11_FEATURE_DATA_MARKER_SUPPORT;

typedef struct D3D11_FEATURE_DATA_D3D9_OPTIONS1
{
    BOOL FullNonPow2TextureSupported;
    BOOL DepthAsTextureWithLessEqualComparisonFilterSupported;
    BOOL SimpleInstancingSupported;
    BOOL TextureCubeFaceRenderTargetWithNonCubeDepthStencilSupported;
} D3D11_FEATURE_DATA_D3D9_OPTIONS1;

typedef enum D3D11_CONSERVATIVE_RASTERIZATION_TIER
{
    D3D11_CONSERVATIVE_RASTERIZATION_NOT_SUPPORTED = 0x0,
    D3D11_CONSERVATIVE_RASTERIZATION_TIER_1 = 0x1,
    D3D11_CONSERVATIVE_RASTERIZATION_TIER_2 = 0x2,
    D3D11_CONSERVATIVE_RASTERIZATION_TIER_3 = 0x3,
} D3D11_CONSERVATIVE_RASTERIZATION_TIER;

typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS2
{
    BOOL PSSpecifiedStencilRefSupported;
    BOOL TypedUAVLoadAdditionalFormats;
    BOOL ROVsSupported;
    D3D11_CONSERVATIVE_RASTERIZATION_TIER ConservativeRasterizationTier;
    D3D11_TILED_RESOURCES_TIER TiledResourcesTier;
    BOOL MapOnDefaultTextures;
    BOOL StandardSwizzle;
    BOOL UnifiedMemoryArchitecture;
} D3D11_FEATURE_DATA_D3D11_OPTIONS2;

typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS3
{
    BOOL VPAndRTArrayIndexFromAnyShaderFeedingRasterizer;
} D3D11_FEATURE_DATA_D3D11_OPTIONS3;

typedef struct D3D11_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT
{
    UINT MaxGPUVirtualAddressBitsPerResource;
    UINT MaxGPUVirtualAddressBitsPerProcess;
} D3D11_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT;

typedef enum D3D11_SHADER_CACHE_SUPPORT_FLAGS
{
    D3D11_SHADER_CACHE_SUPPORT_NONE = 0x0,
    D3D11_SHADER_CACHE_SUPPORT_AUTOMATIC_INPROC_CACHE = 0x1,
    D3D11_SHADER_CACHE_SUPPORT_AUTOMATIC_DISK_CACHE = 0x2,
} D3D11_SHADER_CACHE_SUPPORT_FLAGS;

typedef struct D3D11_FEATURE_DATA_SHADER_CACHE
{
    UINT SupportFlags;
} D3D11_FEATURE_DATA_SHADER_CACHE;

typedef enum D3D11_SHARED_RESOURCE_TIER
{
    D3D11_SHARED_RESOURCE_TIER_0,
    D3D11_SHARED_RESOURCE_TIER_1,
    D3D11_SHARED_RESOURCE_TIER_2,
    D3D11_SHARED_RESOURCE_TIER_3,
} D3D11_SHARED_RESOURCE_TIER;

typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS5
{
    D3D11_SHARED_RESOURCE_TIER SharedResourceTier;
} D3D11_FEATURE_DATA_D3D11_OPTIONS5;

typedef enum D3D11_FILL_MODE
{
    D3D11_FILL_WIREFRAME = 2,
    D3D11_FILL_SOLID
} D3D11_FILL_MODE;

typedef enum D3D11_FILTER_TYPE
{
    D3D11_FILTER_TYPE_POINT  = 0,
    D3D11_FILTER_TYPE_LINEAR = 1
} D3D11_FILTER_TYPE;

const UINT D3D11_MIN_FILTER_SHIFT = 4;
const UINT D3D11_MAG_FILTER_SHIFT = 2;
const UINT D3D11_MIP_FILTER_SHIFT = 0;
const UINT D3D11_FILTER_TYPE_MASK          = 0x00000003;
const UINT D3D11_COMPARISON_FILTERING_BIT  = 0x00000080;
const UINT D3D11_ANISOTROPIC_FILTERING_BIT = 0x00000040;

cpp_quote("#define D3D11_ENCODE_BASIC_FILTER(min, mag, mip, bComparison) \\")
cpp_quote("    ((D3D11_FILTER)(((bComparison) ? D3D11_COMPARISON_FILTERING_BIT : 0 ) | \\")
cpp_quote("                    (((min)&D3D11_FILTER_TYPE_MASK) << D3D11_MIN_FILTER_SHIFT) | \\")
cpp_quote("                    (((mag)&D3D11_FILTER_TYPE_MASK) << D3D11_MAG_FILTER_SHIFT) | \\")
cpp_quote("                    (((mip)&D3D11_FILTER_TYPE_MASK) << D3D11_MIP_FILTER_SHIFT)))")

cpp_quote("#define D3D11_ENCODE_ANISOTROPIC_FILTER(bComparison) \\")
cpp_quote("    ((D3D11_FILTER)(D3D11_ANISOTROPIC_FILTERING_BIT | \\" )
cpp_quote("                    D3D11_ENCODE_BASIC_FILTER(D3D11_FILTER_TYPE_LINEAR,D3D11_FILTER_TYPE_LINEAR, \\" )
cpp_quote("                                              D3D11_FILTER_TYPE_LINEAR,bComparison)))" )

cpp_quote("#define D3D11_DECODE_MIN_FILTER(d3d11Filter) \\")
cpp_quote("    ((D3D11_FILTER_TYPE)(((d3d11Filter) >> D3D11_MIN_FILTER_SHIFT) & D3D11_FILTER_TYPE_MASK))")

cpp_quote("#define D3D11_DECODE_MAG_FILTER(d3d11Filter) \\")
cpp_quote("    ((D3D11_FILTER_TYPE)(((d3d11Filter) >> D3D11_MAG_FILTER_SHIFT) & D3D11_FILTER_TYPE_MASK))")

cpp_quote("#define D3D11_DECODE_MIP_FILTER(d3d11Filter) \\")
cpp_quote("    ((D3D11_FILTER_TYPE)(((d3d11Filter) >> D3D11_MIP_FILTER_SHIFT) & D3D11_FILTER_TYPE_MASK))")

cpp_quote("#define D3D11_DECODE_IS_COMPARISON_FILTER(d3d11Filter) ((d3d11Filter) & D3D11_COMPARISON_FILTERING_BIT)")

cpp_quote("#define D3D11_DECODE_IS_ANISOTROPIC_FILTER(d3d11Filter) \\")
cpp_quote("    (((d3d11Filter) & D3D11_ANISOTROPIC_FILTERING_BIT ) \\" )
cpp_quote("     && (D3D11_FILTER_TYPE_LINEAR == D3D11_DECODE_MIN_FILTER(d3d11Filter)) \\" )
cpp_quote("     && (D3D11_FILTER_TYPE_LINEAR == D3D11_DECODE_MAG_FILTER(d3d11Filter)) \\" )
cpp_quote("     && (D3D11_FILTER_TYPE_LINEAR == D3D11_DECODE_MIP_FILTER(d3d11Filter)))")

typedef enum D3D11_FILTER
{
    D3D11_FILTER_MIN_MAG_MIP_POINT = 0x00,
    D3D11_FILTER_MIN_MAG_POINT_MIP_LINEAR = 0x01,
    D3D11_FILTER_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x04,
    D3D11_FILTER_MIN_POINT_MAG_MIP_LINEAR = 0x05,
    D3D11_FILTER_MIN_LINEAR_MAG_MIP_POINT = 0x10,
    D3D11_FILTER_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x11,
    D3D11_FILTER_MIN_MAG_LINEAR_MIP_POINT = 0x14,
    D3D11_FILTER_MIN_MAG_MIP_LINEAR = 0x15,
    D3D11_FILTER_ANISOTROPIC = 0x55,
    D3D11_FILTER_COMPARISON_MIN_MAG_MIP_POINT = 0x80,
    D3D11_FILTER_COMPARISON_MIN_MAG_POINT_MIP_LINEAR = 0x81,
    D3D11_FILTER_COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x84,
    D3D11_FILTER_COMPARISON_MIN_POINT_MAG_MIP_LINEAR = 0x85,
    D3D11_FILTER_COMPARISON_MIN_LINEAR_MAG_MIP_POINT = 0x90,
    D3D11_FILTER_COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x91,
    D3D11_FILTER_COMPARISON_MIN_MAG_LINEAR_MIP_POINT = 0x94,
    D3D11_FILTER_COMPARISON_MIN_MAG_MIP_LINEAR = 0x95,
    D3D11_FILTER_COMPARISON_ANISOTROPIC = 0xd5,
    D3D11_FILTER_MINIMUM_MIN_MAG_MIP_POINT = 0x100,
    D3D11_FILTER_MINIMUM_MIN_MAG_POINT_MIP_LINEAR = 0x101,
    D3D11_FILTER_MINIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x104,
    D3D11_FILTER_MINIMUM_MIN_POINT_MAG_MIP_LINEAR = 0x105,
    D3D11_FILTER_MINIMUM_MIN_LINEAR_MAG_MIP_POINT = 0x110,
    D3D11_FILTER_MINIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x111,
    D3D11_FILTER_MINIMUM_MIN_MAG_LINEAR_MIP_POINT = 0x114,
    D3D11_FILTER_MINIMUM_MIN_MAG_MIP_LINEAR = 0x115,
    D3D11_FILTER_MINIMUM_ANISOTROPIC = 0x155,
    D3D11_FILTER_MAXIMUM_MIN_MAG_MIP_POINT = 0x180,
    D3D11_FILTER_MAXIMUM_MIN_MAG_POINT_MIP_LINEAR = 0x181,
    D3D11_FILTER_MAXIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x184,
    D3D11_FILTER_MAXIMUM_MIN_POINT_MAG_MIP_LINEAR = 0x185,
    D3D11_FILTER_MAXIMUM_MIN_LINEAR_MAG_MIP_POINT = 0x190,
    D3D11_FILTER_MAXIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x191,
    D3D11_FILTER_MAXIMUM_MIN_MAG_LINEAR_MIP_POINT = 0x194,
    D3D11_FILTER_MAXIMUM_MIN_MAG_MIP_LINEAR = 0x195,
    D3D11_FILTER_MAXIMUM_ANISOTROPIC = 0x1d5
} D3D11_FILTER;

typedef enum D3D11_DSV_FLAG
{
    D3D11_DSV_READ_ONLY_DEPTH = 0x1,
    D3D11_DSV_READ_ONLY_STENCIL = 0x2,
} D3D11_DSV_FLAG;

typedef enum D3D11_BUFFEREX_SRV_FLAG
{
    D3D11_BUFFEREX_SRV_FLAG_RAW = 0x1,
} D3D11_BUFFEREX_SRV_FLAG;

typedef enum D3D11_UAV_FLAG
{
    D3D11_BUFFER_UAV_FLAG_RAW     = 0x1,
    D3D11_BUFFER_UAV_FLAG_APPEND  = 0x2,
    D3D11_BUFFER_UAV_FLAG_COUNTER = 0x4,
} D3D11_UAV_FLAG;

typedef enum D3D11_INPUT_CLASSIFICATION
{
    D3D11_INPUT_PER_VERTEX_DATA,
    D3D11_INPUT_PER_INSTANCE_DATA,
} D3D11_INPUT_CLASSIFICATION;

const UINT D3D11_APPEND_ALIGNED_ELEMENT = 0xffffffff;

typedef struct D3D11_INPUT_ELEMENT_DESC
{
    LPCSTR SemanticName;
    UINT SemanticIndex;
    DXGI_FORMAT Format;
    UINT InputSlot;
    UINT AlignedByteOffset;
    D3D11_INPUT_CLASSIFICATION InputSlotClass;
    UINT InstanceDataStepRate;
} D3D11_INPUT_ELEMENT_DESC;

typedef enum D3D11_MAP
{
    D3D11_MAP_READ = 1,
    D3D11_MAP_WRITE,
    D3D11_MAP_READ_WRITE,
    D3D11_MAP_WRITE_DISCARD,
    D3D11_MAP_WRITE_NO_OVERWRITE
} D3D11_MAP;

typedef enum D3D11_MAP_FLAG
{
    D3D11_MAP_FLAG_DO_NOT_WAIT = 0x00100000
} D3D11_MAP_FLAG;

typedef enum D3D11_RAISE_FLAG
{
    D3D11_RAISE_FLAG_DRIVER_INTERNAL_ERROR = 0x1,
} D3D11_RAISE_FLAG;

typedef struct D3D11_QUERY_DATA_SO_STATISTICS
{
    UINT64 NumPrimitivesWritten;
    UINT64 PrimitivesStorageNeeded;
} D3D11_QUERY_DATA_SO_STATISTICS;

typedef struct D3D11_MAPPED_SUBRESOURCE
{
    void *pData;
    UINT RowPitch;
    UINT DepthPitch;
} D3D11_MAPPED_SUBRESOURCE;

typedef enum D3D11_QUERY
{
    D3D11_QUERY_EVENT,
    D3D11_QUERY_OCCLUSION,
    D3D11_QUERY_TIMESTAMP,
    D3D11_QUERY_TIMESTAMP_DISJOINT,
    D3D11_QUERY_PIPELINE_STATISTICS,
    D3D11_QUERY_OCCLUSION_PREDICATE,
    D3D11_QUERY_SO_STATISTICS,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE,
    D3D11_QUERY_SO_STATISTICS_STREAM0,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM0,
    D3D11_QUERY_SO_STATISTICS_STREAM1,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM1,
    D3D11_QUERY_SO_STATISTICS_STREAM2,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM2,
    D3D11_QUERY_SO_STATISTICS_STREAM3,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM3,
} D3D11_QUERY;

typedef enum D3D11_QUERY_MISC_FLAG
{
    D3D11_QUERY_MISC_PREDICATEHINT = 0x1,
} D3D11_QUERY_MISC_FLAG;

typedef enum D3D11_ASYNC_GETDATA_FLAG
{
    D3D11_ASYNC_GETDATA_DONOTFLUSH = 0x0001,
} D3D11_ASYNC_GETDATA_FLAG;

typedef enum D3D11_RESOURCE_MISC_FLAG
{
    D3D11_RESOURCE_MISC_GENERATE_MIPS                    = 0x00000001L,
    D3D11_RESOURCE_MISC_SHARED                           = 0x00000002L,
    D3D11_RESOURCE_MISC_TEXTURECUBE                      = 0x00000004L,
    D3D11_RESOURCE_MISC_DRAWINDIRECT_ARGS                = 0x00000010L,
    D3D11_RESOURCE_MISC_BUFFER_ALLOW_RAW_VIEWS           = 0x00000020L,
    D3D11_RESOURCE_MISC_BUFFER_STRUCTURED                = 0x00000040L,
    D3D11_RESOURCE_MISC_RESOURCE_CLAMP                   = 0x00000080L,
    D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX                = 0x00000100L,
    D3D11_RESOURCE_MISC_GDI_COMPATIBLE                   = 0x00000200L,
    D3D11_RESOURCE_MISC_SHARED_NTHANDLE                  = 0x00000800L,
    D3D11_RESOURCE_MISC_RESTRICTED_CONTENT               = 0x00001000L,
    D3D11_RESOURCE_MISC_RESTRICT_SHARED_RESOURCE         = 0x00002000L,
    D3D11_RESOURCE_MISC_RESTRICT_SHARED_RESOURCE_DRIVER  = 0x00004000L,
    D3D11_RESOURCE_MISC_GUARDED                          = 0x00008000L,
    D3D11_RESOURCE_MISC_TILE_POOL                        = 0x00020000L,
    D3D11_RESOURCE_MISC_TILED                            = 0x00040000L,
    D3D11_RESOURCE_MISC_HW_PROTECTED                     = 0x00080000L,
    D3D11_RESOURCE_MISC_SHARED_DISPLAYABLE               = 0x00100000L,
    D3D11_RESOURCE_MISC_SHARED_EXCLUSIVE_WRITER          = 0x00200000L
} D3D11_RESOURCE_MISC_FLAG;

typedef struct D3D11_QUERY_DESC
{
    D3D11_QUERY Query;
    UINT MiscFlags;
} D3D11_QUERY_DESC;

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)")
cpp_quote("struct CD3D11_QUERY_DESC : public D3D11_QUERY_DESC {")
cpp_quote("    CD3D11_QUERY_DESC() {}")
cpp_quote("    ~CD3D11_QUERY_DESC() {}")
cpp_quote("    explicit CD3D11_QUERY_DESC(const D3D11_QUERY_DESC &other) : D3D11_QUERY_DESC(other) {}")
cpp_quote("    explicit CD3D11_QUERY_DESC(D3D11_QUERY query, UINT misc_flags = 0) {")
cpp_quote("        Query = query;")
cpp_quote("        MiscFlags = misc_flags;")
cpp_quote("    }")
cpp_quote("    operator const D3D11_QUERY_DESC&() const {")
cpp_quote("        return *this;")
cpp_quote("    }")
cpp_quote("};")
cpp_quote("#endif")

typedef struct D3D11_RASTERIZER_DESC
{
    D3D11_FILL_MODE FillMode;
    D3D11_CULL_MODE CullMode;
    BOOL FrontCounterClockwise;
    INT DepthBias;
    FLOAT DepthBiasClamp;
    FLOAT SlopeScaledDepthBias;
    BOOL DepthClipEnable;
    BOOL ScissorEnable;
    BOOL MultisampleEnable;
    BOOL AntialiasedLineEnable;
} D3D11_RASTERIZER_DESC;

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)")
cpp_quote("struct CD3D11_RASTERIZER_DESC : public D3D11_RASTERIZER_DESC {")
cpp_quote("    CD3D11_RASTERIZER_DESC() {}")
cpp_quote("    explicit CD3D11_RASTERIZER_DESC(const D3D11_RASTERIZER_DESC &o) : D3D11_RASTERIZER_DESC(o) {}")
cpp_quote("    explicit CD3D11_RASTERIZER_DESC(CD3D11_DEFAULT) {")
cpp_quote("        FillMode = D3D11_FILL_SOLID;")
cpp_quote("        CullMode = D3D11_CULL_BACK;")
cpp_quote("        FrontCounterClockwise = FALSE;")
cpp_quote("        DepthBias = D3D11_DEFAULT_DEPTH_BIAS;")
cpp_quote("        DepthBiasClamp = D3D11_DEFAULT_DEPTH_BIAS_CLAMP;")
cpp_quote("        SlopeScaledDepthBias = D3D11_DEFAULT_SLOPE_SCALED_DEPTH_BIAS;")
cpp_quote("        DepthClipEnable = TRUE;")
cpp_quote("        ScissorEnable = FALSE;")
cpp_quote("        MultisampleEnable = FALSE;")
cpp_quote("        AntialiasedLineEnable = FALSE;")
cpp_quote("    }")
cpp_quote("    explicit CD3D11_RASTERIZER_DESC(D3D11_FILL_MODE fillMode, D3D11_CULL_MODE cullMode," )
cpp_quote("            WINBOOL frontCounterClockwise, INT depthBias, FLOAT depthBiasClamp, FLOAT slopeScaledDepthBias,")
cpp_quote("            BOOL depthClipEnable, WINBOOL scissorEnable, WINBOOL multisampleEnable, WINBOOL antialiasedLineEnable) {")
cpp_quote("        FillMode = fillMode;")
cpp_quote("        CullMode = cullMode;")
cpp_quote("        FrontCounterClockwise = frontCounterClockwise;")
cpp_quote("        DepthBias = depthBias;")
cpp_quote("        DepthBiasClamp = depthBiasClamp;")
cpp_quote("        SlopeScaledDepthBias = slopeScaledDepthBias;")
cpp_quote("        DepthClipEnable = depthClipEnable;")
cpp_quote("        ScissorEnable = scissorEnable;")
cpp_quote("        MultisampleEnable = multisampleEnable;")
cpp_quote("        AntialiasedLineEnable = antialiasedLineEnable;")
cpp_quote("    }")
cpp_quote("    ~CD3D11_RASTERIZER_DESC() {}")
cpp_quote("    operator const D3D11_RASTERIZER_DESC&() const { return *this; }")
cpp_quote("};")
cpp_quote("#endif")

typedef enum D3D11_RESOURCE_DIMENSION
{
    D3D11_RESOURCE_DIMENSION_UNKNOWN,
    D3D11_RESOURCE_DIMENSION_BUFFER,
    D3D11_RESOURCE_DIMENSION_TEXTURE1D,
    D3D11_RESOURCE_DIMENSION_TEXTURE2D,
    D3D11_RESOURCE_DIMENSION_TEXTURE3D,
} D3D11_RESOURCE_DIMENSION;

typedef enum D3D11_RTV_DIMENSION
{
    D3D11_RTV_DIMENSION_UNKNOWN,
    D3D11_RTV_DIMENSION_BUFFER,
    D3D11_RTV_DIMENSION_TEXTURE1D,
    D3D11_RTV_DIMENSION_TEXTURE1DARRAY,
    D3D11_RTV_DIMENSION_TEXTURE2D,
    D3D11_RTV_DIMENSION_TEXTURE2DARRAY,
    D3D11_RTV_DIMENSION_TEXTURE2DMS,
    D3D11_RTV_DIMENSION_TEXTURE2DMSARRAY,
    D3D11_RTV_DIMENSION_TEXTURE3D,
} D3D11_RTV_DIMENSION;

typedef struct D3D11_SO_DECLARATION_ENTRY
{
    UINT Stream;
    LPCSTR SemanticName;
    UINT SemanticIndex;
    BYTE StartComponent;
    BYTE ComponentCount;
    BYTE OutputSlot;
} D3D11_SO_DECLARATION_ENTRY;

typedef enum D3D11_STENCIL_OP
{
    D3D11_STENCIL_OP_KEEP = 1,
    D3D11_STENCIL_OP_ZERO,
    D3D11_STENCIL_OP_REPLACE,
    D3D11_STENCIL_OP_INCR_SAT,
    D3D11_STENCIL_OP_DECR_SAT,
    D3D11_STENCIL_OP_INVERT,
    D3D11_STENCIL_OP_INCR,
    D3D11_STENCIL_OP_DECR
} D3D11_STENCIL_OP;

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)")
cpp_quote("}")
cpp_quote("inline UINT D3D11CalcSubresource(UINT MipSlice, UINT ArraySlice, UINT MipLevels) {")
cpp_quote("    return MipSlice + ArraySlice * MipLevels;")
cpp_quote("}")
cpp_quote("extern \"C\"{")
cpp_quote("#endif")

typedef struct D3D11_SUBRESOURCE_DATA
{
    const void *pSysMem;
    UINT SysMemPitch;
    UINT SysMemSlicePitch;
} D3D11_SUBRESOURCE_DATA;

typedef struct D3D11_TEX1D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_DSV;

typedef struct D3D11_TEX1D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_RTV;

typedef struct D3D11_TEX1D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_SRV;

typedef struct D3D11_TEX1D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_UAV;

typedef struct D3D11_TEX1D_DSV
{
    UINT MipSlice;
} D3D11_TEX1D_DSV;

typedef struct D3D11_TEX1D_RTV
{
    UINT MipSlice;
} D3D11_TEX1D_RTV;

typedef struct D3D11_TEX1D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
 } D3D11_TEX1D_SRV;

typedef struct D3D11_TEX1D_UAV
{
    UINT MipSlice;
} D3D11_TEX1D_UAV;

typedef struct D3D11_TEX2D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_DSV;

typedef struct D3D11_TEX2D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_RTV;

typedef struct D3D11_TEX2D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_SRV;

typedef struct D3D11_TEX2D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_UAV;

typedef struct D3D11_TEX2D_DSV
{
    UINT MipSlice;
} D3D11_TEX2D_DSV;

typedef struct D3D11_TEX2D_RTV
{
    UINT MipSlice;
} D3D11_TEX2D_RTV;

typedef struct D3D11_TEX2D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEX2D_SRV;

typedef struct D3D11_TEX2D_UAV
{
    UINT MipSlice;
} D3D11_TEX2D_UAV;

typedef struct D3D11_TEX2DMS_ARRAY_DSV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2DMS_ARRAY_DSV;

typedef struct D3D11_TEX2DMS_ARRAY_RTV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2DMS_ARRAY_RTV;

typedef struct D3D11_TEX2DMS_ARRAY_SRV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2DMS_ARRAY_SRV;

typedef struct D3D11_TEX2DMS_DSV
{
    UINT UnusedField_NothingToDefine;
} D3D11_TEX2DMS_DSV;

typedef struct D3D11_TEX2DMS_RTV
{
    UINT UnusedField_NothingToDefine;
} D3D11_TEX2DMS_RTV;

typedef struct D3D11_TEX2DMS_SRV
{
    UINT UnusedField_NothingToDefine;
} D3D11_TEX2DMS_SRV;

typedef struct D3D11_TEX3D_RTV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D11_TEX3D_RTV;

typedef struct D3D11_TEX3D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEX3D_SRV;

typedef struct D3D11_TEX3D_UAV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D11_TEX3D_UAV;

typedef struct D3D11_TEXCUBE_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT First2DArrayFace;
    UINT NumCubes;
} D3D11_TEXCUBE_ARRAY_SRV;

typedef struct D3D11_TEXCUBE_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEXCUBE_SRV;

typedef enum D3D11_TEXTURE_ADDRESS_MODE
{
    D3D11_TEXTURE_ADDRESS_WRAP = 1,
    D3D11_TEXTURE_ADDRESS_MIRROR,
    D3D11_TEXTURE_ADDRESS_CLAMP,
    D3D11_TEXTURE_ADDRESS_BORDER,
    D3D11_TEXTURE_ADDRESS_MIRROR_ONCE
} D3D11_TEXTURE_ADDRESS_MODE;

typedef enum D3D11_UAV_DIMENSION
{
    D3D11_UAV_DIMENSION_UNKNOWN,
    D3D11_UAV_DIMENSION_BUFFER,
    D3D11_UAV_DIMENSION_TEXTURE1D,
    D3D11_UAV_DIMENSION_TEXTURE1DARRAY,
    D3D11_UAV_DIMENSION_TEXTURE2D,
    D3D11_UAV_DIMENSION_TEXTURE2DARRAY,
    D3D11_UAV_DIMENSION_TEXTURE3D = 8,
} D3D11_UAV_DIMENSION;

typedef struct D3D11_UNORDERED_ACCESS_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D11_UAV_DIMENSION ViewDimension;

    union
    {
        D3D11_BUFFER_UAV Buffer;
        D3D11_TEX1D_UAV Texture1D;
        D3D11_TEX1D_ARRAY_UAV Texture1DArray;
        D3D11_TEX2D_UAV Texture2D;
        D3D11_TEX2D_ARRAY_UAV Texture2DArray;
        D3D11_TEX3D_UAV Texture3D;
    };
} D3D11_UNORDERED_ACCESS_VIEW_DESC;

typedef enum D3D11_USAGE
{
    D3D11_USAGE_DEFAULT,
    D3D11_USAGE_IMMUTABLE,
    D3D11_USAGE_DYNAMIC,
    D3D11_USAGE_STAGING,
} D3D11_USAGE;

typedef enum D3D11_BIND_FLAG
{
    D3D11_BIND_VERTEX_BUFFER    = 0x0001,
    D3D11_BIND_INDEX_BUFFER     = 0x0002,
    D3D11_BIND_CONSTANT_BUFFER  = 0x0004,
    D3D11_BIND_SHADER_RESOURCE  = 0x0008,
    D3D11_BIND_STREAM_OUTPUT    = 0x0010,
    D3D11_BIND_RENDER_TARGET    = 0x0020,
    D3D11_BIND_DEPTH_STENCIL    = 0x0040,
    D3D11_BIND_UNORDERED_ACCESS = 0x0080,
    D3D11_BIND_DECODER          = 0x0200,
    D3D11_BIND_VIDEO_ENCODER    = 0x0400
} D3D11_BIND_FLAG;

typedef enum D3D11_CPU_ACCESS_FLAG
{
    D3D11_CPU_ACCESS_WRITE = 0x00010000,
    D3D11_CPU_ACCESS_READ  = 0x00020000
} D3D11_CPU_ACCESS_FLAG;

typedef struct D3D11_VIEWPORT
{
    FLOAT TopLeftX;
    FLOAT TopLeftY;
    FLOAT Width;
    FLOAT Height;
    FLOAT MinDepth;
    FLOAT MaxDepth;
} D3D11_VIEWPORT;

typedef enum D3D11_COLOR_WRITE_ENABLE
{
    D3D11_COLOR_WRITE_ENABLE_RED   = 1,
    D3D11_COLOR_WRITE_ENABLE_GREEN = 2,
    D3D11_COLOR_WRITE_ENABLE_BLUE  = 4,
    D3D11_COLOR_WRITE_ENABLE_ALPHA = 8,
    D3D11_COLOR_WRITE_ENABLE_ALL   =
        (D3D11_COLOR_WRITE_ENABLE_RED|D3D11_COLOR_WRITE_ENABLE_GREEN|D3D11_COLOR_WRITE_ENABLE_BLUE|D3D11_COLOR_WRITE_ENABLE_ALPHA)
} D3D11_COLOR_WRITE_ENABLE;

typedef enum D3D11_FORMAT_SUPPORT
{
    D3D11_FORMAT_SUPPORT_BUFFER                       = 0x00000001,
    D3D11_FORMAT_SUPPORT_IA_VERTEX_BUFFER             = 0x00000002,
    D3D11_FORMAT_SUPPORT_IA_INDEX_BUFFER              = 0x00000004,
    D3D11_FORMAT_SUPPORT_SO_BUFFER                    = 0x00000008,
    D3D11_FORMAT_SUPPORT_TEXTURE1D                    = 0x00000010,
    D3D11_FORMAT_SUPPORT_TEXTURE2D                    = 0x00000020,
    D3D11_FORMAT_SUPPORT_TEXTURE3D                    = 0x00000040,
    D3D11_FORMAT_SUPPORT_TEXTURECUBE                  = 0x00000080,
    D3D11_FORMAT_SUPPORT_SHADER_LOAD                  = 0x00000100,
    D3D11_FORMAT_SUPPORT_SHADER_SAMPLE                = 0x00000200,
    D3D11_FORMAT_SUPPORT_SHADER_SAMPLE_COMPARISON     = 0x00000400,
    D3D11_FORMAT_SUPPORT_SHADER_SAMPLE_MONO_TEXT      = 0x00000800,
    D3D11_FORMAT_SUPPORT_MIP                          = 0x00001000,
    D3D11_FORMAT_SUPPORT_MIP_AUTOGEN                  = 0x00002000,
    D3D11_FORMAT_SUPPORT_RENDER_TARGET                = 0x00004000,
    D3D11_FORMAT_SUPPORT_BLENDABLE                    = 0x00008000,
    D3D11_FORMAT_SUPPORT_DEPTH_STENCIL                = 0x00010000,
    D3D11_FORMAT_SUPPORT_CPU_LOCKABLE                 = 0x00020000,
    D3D11_FORMAT_SUPPORT_MULTISAMPLE_RESOLVE          = 0x00040000,
    D3D11_FORMAT_SUPPORT_DISPLAY                      = 0x00080000,
    D3D11_FORMAT_SUPPORT_CAST_WITHIN_BIT_LAYOUT       = 0x00100000,
    D3D11_FORMAT_SUPPORT_MULTISAMPLE_RENDERTARGET     = 0x00200000,
    D3D11_FORMAT_SUPPORT_MULTISAMPLE_LOAD             = 0x00400000,
    D3D11_FORMAT_SUPPORT_SHADER_GATHER                = 0x00800000,
    D3D11_FORMAT_SUPPORT_BACK_BUFFER_CAST             = 0x01000000,
    D3D11_FORMAT_SUPPORT_TYPED_UNORDERED_ACCESS_VIEW  = 0x02000000,
    D3D11_FORMAT_SUPPORT_SHADER_GATHER_COMPARISON     = 0x04000000,
    D3D11_FORMAT_SUPPORT_DECODER_OUTPUT               = 0x08000000,
    D3D11_FORMAT_SUPPORT_VIDEO_PROCESSOR_OUTPUT       = 0x10000000,
    D3D11_FORMAT_SUPPORT_VIDEO_PROCESSOR_INPUT        = 0x20000000,
    D3D11_FORMAT_SUPPORT_VIDEO_ENCODER                = 0x40000000,
} D3D11_FORMAT_SUPPORT;

typedef enum D3D11_FORMAT_SUPPORT2
{
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_ADD                                = 0x00000001,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_BITWISE_OPS                        = 0x00000002,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_COMPARE_STORE_OR_COMPARE_EXCHANGE  = 0x00000004,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_EXCHANGE                           = 0x00000008,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_SIGNED_MIN_OR_MAX                  = 0x00000010,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_UNSIGNED_MIN_OR_MAX                = 0x00000020,
    D3D11_FORMAT_SUPPORT2_UAV_TYPED_LOAD                                = 0x00000040,
    D3D11_FORMAT_SUPPORT2_UAV_TYPED_STORE                               = 0x00000080,
    D3D11_FORMAT_SUPPORT2_OUTPUT_MERGER_LOGIC_OP                        = 0x00000100,
    D3D11_FORMAT_SUPPORT2_TILED                                         = 0x00000200,
    D3D11_FORMAT_SUPPORT2_SHAREABLE                                     = 0x00000400,
    D3D11_FORMAT_SUPPORT2_MULTIPLANE_OVERLAY                            = 0x00004000,
} D3D11_FORMAT_SUPPORT2;

typedef enum D3D11_CLEAR_FLAG
{
    D3D11_CLEAR_DEPTH    = 0x0001L,
    D3D11_CLEAR_STENCIL  = 0x0002L
} D3D11_CLEAR_FLAG;

typedef struct D3D11_RENDER_TARGET_BLEND_DESC
{
    BOOL BlendEnable;
    D3D11_BLEND SrcBlend;
    D3D11_BLEND DestBlend;
    D3D11_BLEND_OP BlendOp;
    D3D11_BLEND SrcBlendAlpha;
    D3D11_BLEND DestBlendAlpha;
    D3D11_BLEND_OP BlendOpAlpha;
    UINT8 RenderTargetWriteMask;
} D3D11_RENDER_TARGET_BLEND_DESC;

typedef struct D3D11_BLEND_DESC
{
    BOOL AlphaToCoverageEnable;
    BOOL IndependentBlendEnable;
    D3D11_RENDER_TARGET_BLEND_DESC RenderTarget[D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT];
} D3D11_BLEND_DESC;

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)")
cpp_quote("struct CD3D11_BLEND_DESC : public D3D11_BLEND_DESC {")
cpp_quote("    CD3D11_BLEND_DESC() {}")
cpp_quote("    explicit CD3D11_BLEND_DESC(const D3D11_BLEND_DESC &o) : D3D11_BLEND_DESC(o) {}")
cpp_quote("    explicit CD3D11_BLEND_DESC(CD3D11_DEFAULT) {")
cpp_quote("        AlphaToCoverageEnable = FALSE;")
cpp_quote("        IndependentBlendEnable = FALSE;")
cpp_quote("        for(D3D11_RENDER_TARGET_BLEND_DESC *target = RenderTarget;")
cpp_quote("                target < RenderTarget + D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT;")
cpp_quote("                target++) {")
cpp_quote("            target->BlendEnable = FALSE;")
cpp_quote("            target->SrcBlend = target->SrcBlendAlpha = D3D11_BLEND_ONE;")
cpp_quote("            target->DestBlend = target->DestBlendAlpha = D3D11_BLEND_ZERO;")
cpp_quote("            target->BlendOp = target->BlendOpAlpha = D3D11_BLEND_OP_ADD;")
cpp_quote("            target->RenderTargetWriteMask = D3D11_COLOR_WRITE_ENABLE_ALL;")
cpp_quote("        }")
cpp_quote("    }")
cpp_quote("    ~CD3D11_BLEND_DESC() {}")
cpp_quote("    operator const D3D11_BLEND_DESC&() const { return *this; }")
cpp_quote("};" )
cpp_quote("#endif" )

typedef struct D3D11_BUFFER_DESC
{
    UINT ByteWidth;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
    UINT StructureByteStride;
} D3D11_BUFFER_DESC;

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)")
cpp_quote("struct CD3D11_BUFFER_DESC : public D3D11_BUFFER_DESC {")
cpp_quote("    CD3D11_BUFFER_DESC() {}" )
cpp_quote("    explicit CD3D11_BUFFER_DESC(const D3D11_BUFFER_DESC &o) : D3D11_BUFFER_DESC(o) {}")
cpp_quote("    explicit CD3D11_BUFFER_DESC(UINT byteWidth,UINT bindFlags,")
cpp_quote("            D3D11_USAGE usage = D3D11_USAGE_DEFAULT, UINT cpuaccessFlags = 0,")
cpp_quote("            UINT miscFlags = 0, UINT structureByteStride = 0 ) {")
cpp_quote("        ByteWidth = byteWidth;")
cpp_quote("        Usage = usage;")
cpp_quote("        BindFlags = bindFlags;")
cpp_quote("        CPUAccessFlags = cpuaccessFlags;")
cpp_quote("        MiscFlags = miscFlags;" )
cpp_quote("        StructureByteStride = structureByteStride;")
cpp_quote("    }")
cpp_quote("    ~CD3D11_BUFFER_DESC() {}")
cpp_quote("    operator const D3D11_BUFFER_DESC&() const { return *this; }")
cpp_quote("};" )
cpp_quote("#endif" )

typedef struct D3D11_DEPTH_STENCIL_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D11_DSV_DIMENSION ViewDimension;
    UINT Flags;

    union
    {
        D3D11_TEX1D_DSV Texture1D;
        D3D11_TEX1D_ARRAY_DSV Texture1DArray;
        D3D11_TEX2D_DSV Texture2D;
        D3D11_TEX2D_ARRAY_DSV Texture2DArray;
        D3D11_TEX2DMS_DSV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_DSV Texture2DMSArray;
    };
} D3D11_DEPTH_STENCIL_VIEW_DESC;

typedef struct D3D11_DEPTH_STENCILOP_DESC
{
    D3D11_STENCIL_OP StencilFailOp;
    D3D11_STENCIL_OP StencilDepthFailOp;
    D3D11_STENCIL_OP StencilPassOp;
    D3D11_COMPARISON_FUNC StencilFunc;
} D3D11_DEPTH_STENCILOP_DESC;

typedef struct D3D11_DEPTH_STENCIL_DESC
{
    BOOL DepthEnable;
    D3D11_DEPTH_WRITE_MASK DepthWriteMask;
    D3D11_COMPARISON_FUNC DepthFunc;
    BOOL StencilEnable;
    UINT8 StencilReadMask;
    UINT8 StencilWriteMask;
    D3D11_DEPTH_STENCILOP_DESC FrontFace;
    D3D11_DEPTH_STENCILOP_DESC BackFace;
} D3D11_DEPTH_STENCIL_DESC;

cpp_quote("#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )")
cpp_quote("struct CD3D11_DEPTH_STENCIL_DESC : public D3D11_DEPTH_STENCIL_DESC {")
cpp_quote("    CD3D11_DEPTH_STENCIL_DESC() {}")
cpp_quote("    explicit CD3D11_DEPTH_STENCIL_DESC(const D3D11_DEPTH_STENCIL_DESC &other) : D3D11_DEPTH_STENCIL_DESC(other) {}")
cpp_quote("    explicit CD3D11_DEPTH_STENCIL_DESC(CD3D11_DEFAULT) {")
cpp_quote("        const D3D11_DEPTH_STENCILOP_DESC default_op =")
cpp_quote("            {D3D11_STENCIL_OP_KEEP, D3D11_STENCIL_OP_KEEP, D3D11_STENCIL_OP_KEEP, D3D11_COMPARISON_ALWAYS};")
cpp_quote("        DepthEnable = TRUE;")
cpp_quote("        DepthWriteMask = D3D11_DEPTH_WRITE_MASK_ALL;")
cpp_quote("        DepthFunc = D3D11_COMPARISON_LESS;")
cpp_quote("        StencilEnable = FALSE;")
cpp_quote("        StencilReadMask = D3D11_DEFAULT_STENCIL_READ_MASK;")
cpp_quote("        StencilWriteMask = D3D11_DEFAULT_STENCIL_WRITE_MASK;")
cpp_quote("        FrontFace = default_op;")
cpp_quote("        BackFace = default_op;")
cpp_quote("    }")
cpp_quote("    explicit CD3D11_DEPTH_STENCIL_DESC(")
cpp_quote("            WINBOOL depth_enable,")
cpp_quote("            D3D11_DEPTH_WRITE_MASK depth_write_mask,")
cpp_quote("            D3D11_COMPARISON_FUNC depth_func,")
cpp_quote("            WINBOOL stencil_enable,")
cpp_quote("            UINT8 stencil_read_mask,")
cpp_quote("            UINT8 stencil_write_mask,")
cpp_quote("            D3D11_STENCIL_OP front_stencil_fail_op,")
cpp_quote("            D3D11_STENCIL_OP front_stencil_depth_fail_op,")
cpp_quote("            D3D11_STENCIL_OP front_stencil_pass_op,")
cpp_quote("            D3D11_COMPARISON_FUNC front_stencil_func,")
cpp_quote("            D3D11_STENCIL_OP back_stencil_fail_op,")
cpp_quote("            D3D11_STENCIL_OP back_stencil_depth_fail_op,")
cpp_quote("            D3D11_STENCIL_OP back_stencil_pass_op,")
cpp_quote("            D3D11_COMPARISON_FUNC back_stencil_func) {")
cpp_quote("        DepthEnable = depth_enable;")
cpp_quote("        DepthWriteMask = depth_write_mask;")
cpp_quote("        DepthFunc = depth_func;")
cpp_quote("        StencilEnable = stencil_enable;")
cpp_quote("        StencilReadMask = stencil_read_mask;")
cpp_quote("        StencilWriteMask = stencil_write_mask;")
cpp_quote("        FrontFace.StencilFailOp = front_stencil_fail_op;")
cpp_quote("        FrontFace.StencilDepthFailOp = front_stencil_depth_fail_op;")
cpp_quote("        FrontFace.StencilPassOp = front_stencil_pass_op;")
cpp_quote("        FrontFace.StencilFunc = front_stencil_func;")
cpp_quote("        BackFace.StencilFailOp = back_stencil_fail_op;")
cpp_quote("        BackFace.StencilDepthFailOp = back_stencil_depth_fail_op;")
cpp_quote("        BackFace.StencilPassOp = back_stencil_pass_op;")
cpp_quote("        BackFace.StencilFunc = back_stencil_func;")
cpp_quote("    }")
cpp_quote("    ~CD3D11_DEPTH_STENCIL_DESC() {}")
cpp_quote("    operator const D3D11_DEPTH_STENCIL_DESC&() const { return *this; }")
cpp_quote("};")
cpp_quote("#endif")

typedef struct D3D11_RENDER_TARGET_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D11_RTV_DIMENSION ViewDimension;

    union
    {
        D3D11_BUFFER_RTV Buffer;
        D3D11_TEX1D_RTV Texture1D;
        D3D11_TEX1D_ARRAY_RTV Texture1DArray;
        D3D11_TEX2D_RTV Texture2D;
        D3D11_TEX2D_ARRAY_RTV Texture2DArray;
        D3D11_TEX2DMS_RTV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_RTV Texture2DMSArray;
        D3D11_TEX3D_RTV Texture3D;
    };
} D3D11_RENDER_TARGET_VIEW_DESC;

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)")
cpp_quote("struct CD3D11_RENDER_TARGET_VIEW_DESC : public D3D11_RENDER_TARGET_VIEW_DESC {")
cpp_quote("    CD3D11_RENDER_TARGET_VIEW_DESC() {}")
cpp_quote("    explicit CD3D11_RENDER_TARGET_VIEW_DESC(D3D11_RTV_DIMENSION dim, DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN,")
cpp_quote("            UINT mip_slice = 0, UINT first_slice = 0, UINT array_size = -1) {")
cpp_quote("        Format = format;")
cpp_quote("        ViewDimension = dim;")
cpp_quote("        switch(dim) {")
cpp_quote("        case D3D11_RTV_DIMENSION_BUFFER:")
cpp_quote("            Buffer.FirstElement = mip_slice;")
cpp_quote("            Buffer.NumElements = first_slice;")
cpp_quote("            break;")
cpp_quote("        case D3D11_RTV_DIMENSION_TEXTURE1D:")
cpp_quote("            Texture1D.MipSlice = mip_slice;")
cpp_quote("            break;")
cpp_quote("        case D3D11_RTV_DIMENSION_TEXTURE1DARRAY:")
cpp_quote("            Texture1DArray.MipSlice = mip_slice;")
cpp_quote("            Texture1DArray.FirstArraySlice = first_slice;")
cpp_quote("            Texture1DArray.ArraySize = array_size;")
cpp_quote("            break;")
cpp_quote("        case D3D11_RTV_DIMENSION_TEXTURE2D:")
cpp_quote("            Texture2D.MipSlice = mip_slice;")
cpp_quote("            break;")
cpp_quote("        case D3D11_RTV_DIMENSION_TEXTURE2DARRAY:")
cpp_quote("            Texture2DArray.MipSlice = mip_slice;")
cpp_quote("            Texture2DArray.FirstArraySlice = first_slice;")
cpp_quote("            Texture2DArray.ArraySize = array_size;")
cpp_quote("            break;")
cpp_quote("        case D3D11_RTV_DIMENSION_TEXTURE2DMSARRAY:")
cpp_quote("            Texture2DMSArray.FirstArraySlice = first_slice;")
cpp_quote("            Texture2DMSArray.ArraySize = array_size;")
cpp_quote("            break;")
cpp_quote("        case D3D11_RTV_DIMENSION_TEXTURE3D:")
cpp_quote("            Texture3D.MipSlice = mip_slice;")
cpp_quote("            Texture3D.FirstWSlice = first_slice;")
cpp_quote("            Texture3D.WSize = array_size;")
cpp_quote("            break;")
cpp_quote("        default:")
cpp_quote("            break;")
cpp_quote("        }")
cpp_quote("    }")
cpp_quote("    explicit CD3D11_RENDER_TARGET_VIEW_DESC(ID3D11Buffer*, DXGI_FORMAT format, UINT first_elem,")
cpp_quote("            UINT elem_cnt) {")
cpp_quote("        Format = format;")
cpp_quote("        ViewDimension = D3D11_RTV_DIMENSION_BUFFER;")
cpp_quote("        Buffer.FirstElement = first_elem;")
cpp_quote("        Buffer.NumElements = elem_cnt;")
cpp_quote("    }")
cpp_quote("    explicit CD3D11_RENDER_TARGET_VIEW_DESC(ID3D11Texture1D *texture, D3D11_RTV_DIMENSION dim,")
cpp_quote("            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT mip_slice = 0, UINT first_slice = 0,")
cpp_quote("            UINT array_size = -1);") /* FIXME: implement */
cpp_quote("    explicit CD3D11_RENDER_TARGET_VIEW_DESC(ID3D11Texture2D *texture, D3D11_RTV_DIMENSION dim,")
cpp_quote("            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT mip_slice = 0, UINT first_slice = 0,")
cpp_quote("            UINT array_size = -1);") /* FIXME: implement */
cpp_quote("    explicit CD3D11_RENDER_TARGET_VIEW_DESC(ID3D11Texture3D *texture, DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN,")
cpp_quote("            UINT mip_slice = 0, UINT first_w_slice = 0, UINT w_slice = -1 );") /* FIXME: implement */
cpp_quote("    ~CD3D11_RENDER_TARGET_VIEW_DESC() {}")
cpp_quote("    explicit CD3D11_RENDER_TARGET_VIEW_DESC(const D3D11_RENDER_TARGET_VIEW_DESC &other)")
cpp_quote("        : D3D11_RENDER_TARGET_VIEW_DESC(other) {}")
cpp_quote("    operator const D3D11_RENDER_TARGET_VIEW_DESC&() const {")
cpp_quote("        return *this;")
cpp_quote("    }")
cpp_quote("};")
cpp_quote("#endif")


typedef struct D3D11_SAMPLER_DESC
{
    D3D11_FILTER Filter;
    D3D11_TEXTURE_ADDRESS_MODE AddressU;
    D3D11_TEXTURE_ADDRESS_MODE AddressV;
    D3D11_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D11_COMPARISON_FUNC ComparisonFunc;
    FLOAT BorderColor[4];
    FLOAT MinLOD;
    FLOAT MaxLOD;
} D3D11_SAMPLER_DESC;

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)")
cpp_quote("struct CD3D11_SAMPLER_DESC : public D3D11_SAMPLER_DESC {")
cpp_quote("    CD3D11_SAMPLER_DESC() {}")
cpp_quote("    explicit CD3D11_SAMPLER_DESC(const D3D11_SAMPLER_DESC &o) : D3D11_SAMPLER_DESC(o) {}")
cpp_quote("    explicit CD3D11_SAMPLER_DESC(CD3D11_DEFAULT) {" )
cpp_quote("        Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;")
cpp_quote("        AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;")
cpp_quote("        AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;")
cpp_quote("        AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;")
cpp_quote("        MipLODBias = 0;")
cpp_quote("        MaxAnisotropy = 1;")
cpp_quote("        ComparisonFunc = D3D11_COMPARISON_NEVER;")
cpp_quote("        BorderColor[0] = BorderColor[1] = BorderColor[2] = BorderColor[3] = 1.0f;")
cpp_quote("        MinLOD = -3.402823466e+38f;")
cpp_quote("        MaxLOD = 3.402823466e+38f;")
cpp_quote("    }")
cpp_quote("    explicit CD3D11_SAMPLER_DESC(D3D11_FILTER filter, D3D11_TEXTURE_ADDRESS_MODE addressU,")
cpp_quote("            D3D11_TEXTURE_ADDRESS_MODE addressV, D3D11_TEXTURE_ADDRESS_MODE addressW,")
cpp_quote("            FLOAT mipLODBias, UINT maxAnisotropy, D3D11_COMPARISON_FUNC comparisonFunc,")
cpp_quote("            const FLOAT *borderColor, FLOAT minLOD, FLOAT maxLOD) {" )
cpp_quote("        Filter = filter;")
cpp_quote("        AddressU = addressU;")
cpp_quote("        AddressV = addressV;")
cpp_quote("        AddressW = addressW;")
cpp_quote("        MipLODBias = mipLODBias;")
cpp_quote("        MaxAnisotropy = maxAnisotropy;")
cpp_quote("        ComparisonFunc = comparisonFunc;")
cpp_quote("        if(borderColor) {")
cpp_quote("            BorderColor[0] = borderColor[0];")
cpp_quote("            BorderColor[1] = borderColor[1];")
cpp_quote("            BorderColor[2] = borderColor[2];")
cpp_quote("            BorderColor[3] = borderColor[3];")
cpp_quote("        }else {")
cpp_quote("            BorderColor[0] = BorderColor[1] = BorderColor[2] = BorderColor[3] = 1.0f;")
cpp_quote("        }")
cpp_quote("        MinLOD = minLOD;")
cpp_quote("        MaxLOD = maxLOD;")
cpp_quote("    }")
cpp_quote("    ~CD3D11_SAMPLER_DESC() {}")
cpp_quote("    operator const D3D11_SAMPLER_DESC&() const { return *this; }")
cpp_quote("};")
cpp_quote("#endif")

typedef struct D3D11_SHADER_RESOURCE_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D11_SRV_DIMENSION ViewDimension;

    union
    {
        D3D11_BUFFER_SRV Buffer;
        D3D11_TEX1D_SRV Texture1D;
        D3D11_TEX1D_ARRAY_SRV Texture1DArray;
        D3D11_TEX2D_SRV Texture2D;
        D3D11_TEX2D_ARRAY_SRV Texture2DArray;
        D3D11_TEX2DMS_SRV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_SRV Texture2DMSArray;
        D3D11_TEX3D_SRV Texture3D;
        D3D11_TEXCUBE_SRV TextureCube;
        D3D11_TEXCUBE_ARRAY_SRV TextureCubeArray;
        D3D11_BUFFEREX_SRV BufferEx;
    };
} D3D11_SHADER_RESOURCE_VIEW_DESC;

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined( __cplusplus )")
cpp_quote("struct CD3D11_SHADER_RESOURCE_VIEW_DESC : public D3D11_SHADER_RESOURCE_VIEW_DESC {")
cpp_quote("    CD3D11_SHADER_RESOURCE_VIEW_DESC() {}")
cpp_quote("    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(D3D11_SRV_DIMENSION dim,")
cpp_quote("            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT most_detailed_mip = 0,")
cpp_quote("            UINT mip_levels = -1, UINT first_slice = 0, UINT array_size = -1, UINT flags = 0) {")
cpp_quote("        Format = format;")
cpp_quote("        ViewDimension = dim;")
cpp_quote("        switch(ViewDimension) {")
cpp_quote("        case D3D11_SRV_DIMENSION_BUFFER:")
cpp_quote("            Buffer.FirstElement = most_detailed_mip;")
cpp_quote("            Buffer.NumElements = mip_levels;")
cpp_quote("            break;")
cpp_quote("        case D3D11_SRV_DIMENSION_TEXTURE1D:")
cpp_quote("            Texture1D.MostDetailedMip = most_detailed_mip;")
cpp_quote("            Texture1D.MipLevels = mip_levels;")
cpp_quote("            break;")
cpp_quote("        case D3D11_SRV_DIMENSION_TEXTURE1DARRAY:")
cpp_quote("            Texture1DArray.MostDetailedMip = most_detailed_mip;")
cpp_quote("            Texture1DArray.MipLevels = mip_levels;")
cpp_quote("            Texture1DArray.FirstArraySlice = first_slice;")
cpp_quote("            Texture1DArray.ArraySize = array_size;")
cpp_quote("            break;")
cpp_quote("        case D3D11_SRV_DIMENSION_TEXTURE2D:")
cpp_quote("            Texture2D.MostDetailedMip = most_detailed_mip;")
cpp_quote("            Texture2D.MipLevels = mip_levels;")
cpp_quote("            break;")
cpp_quote("        case D3D11_SRV_DIMENSION_TEXTURE2DARRAY:")
cpp_quote("            Texture2DArray.MostDetailedMip = most_detailed_mip;")
cpp_quote("            Texture2DArray.MipLevels = mip_levels;")
cpp_quote("            Texture2DArray.FirstArraySlice = first_slice;")
cpp_quote("            Texture2DArray.ArraySize = array_size;")
cpp_quote("            break;")
cpp_quote("        case D3D11_SRV_DIMENSION_TEXTURE2DMSARRAY:")
cpp_quote("            Texture2DMSArray.FirstArraySlice = first_slice;")
cpp_quote("            Texture2DMSArray.ArraySize = array_size;")
cpp_quote("            break;")
cpp_quote("        case D3D11_SRV_DIMENSION_TEXTURE3D:")
cpp_quote("            Texture3D.MostDetailedMip = most_detailed_mip;")
cpp_quote("            Texture3D.MipLevels = mip_levels;")
cpp_quote("            break;")
cpp_quote("        case D3D11_SRV_DIMENSION_TEXTURECUBE:")
cpp_quote("            TextureCube.MostDetailedMip = most_detailed_mip;")
cpp_quote("            TextureCube.MipLevels = mip_levels;")
cpp_quote("            break;")
cpp_quote("        case D3D11_SRV_DIMENSION_TEXTURECUBEARRAY:")
cpp_quote("            TextureCubeArray.MostDetailedMip = most_detailed_mip;")
cpp_quote("            TextureCubeArray.MipLevels = mip_levels;")
cpp_quote("            TextureCubeArray.First2DArrayFace = first_slice;")
cpp_quote("            TextureCubeArray.NumCubes = array_size;")
cpp_quote("            break;")
cpp_quote("        case D3D11_SRV_DIMENSION_BUFFEREX:")
cpp_quote("            BufferEx.FirstElement = most_detailed_mip;")
cpp_quote("            BufferEx.NumElements = mip_levels;")
cpp_quote("            BufferEx.Flags = flags;")
cpp_quote("            break;")
cpp_quote("        default:")
cpp_quote("            break;")
cpp_quote("        }")
cpp_quote("    }")
cpp_quote("    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(ID3D11Buffer*, DXGI_FORMAT format, UINT first_elem,")
cpp_quote("            UINT elem_cnt, UINT flags = 0);") /* FIXME: implement */
cpp_quote("    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(ID3D11Texture1D *texture, D3D11_SRV_DIMENSION dim,")
cpp_quote("            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT most_detailed_mip = 0, UINT mip_levels = -1,")
cpp_quote("            UINT first_slice = 0, UINT array_size = -1 );") /* FIXME: implement */
cpp_quote("    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(ID3D11Texture2D *texture, D3D11_SRV_DIMENSION dim,")
cpp_quote("            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT most_detailed_mip = 0, UINT mip_levels = -1,")
cpp_quote("            UINT first_slice = 0, UINT array_size = -1 );") /* FIXME: implement */
cpp_quote("    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(ID3D11Texture3D *texture, DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN,")
cpp_quote("            UINT most_detailed_mip = 0, UINT mip_levels = -1 );")
cpp_quote("    ~CD3D11_SHADER_RESOURCE_VIEW_DESC() {}")
cpp_quote("    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(const D3D11_SHADER_RESOURCE_VIEW_DESC &other)")
cpp_quote("        : D3D11_SHADER_RESOURCE_VIEW_DESC(other) {}")
cpp_quote("    operator const D3D11_SHADER_RESOURCE_VIEW_DESC&() const {")
cpp_quote("        return *this;")
cpp_quote("    }")
cpp_quote("};")
cpp_quote("#endif")

typedef struct D3D11_TEXTURE1D_DESC
{
    UINT Width;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT Format;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
} D3D11_TEXTURE1D_DESC;

typedef struct D3D11_TEXTURE2D_DESC
{
    UINT Width;
    UINT Height;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
} D3D11_TEXTURE2D_DESC;

cpp_quote("#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)")
cpp_quote("struct CD3D11_TEXTURE2D_DESC : public D3D11_TEXTURE2D_DESC {")
cpp_quote("    CD3D11_TEXTURE2D_DESC() {}")
cpp_quote("    explicit CD3D11_TEXTURE2D_DESC(const D3D11_TEXTURE2D_DESC &o) : D3D11_TEXTURE2D_DESC(o) {}")
cpp_quote("    explicit CD3D11_TEXTURE2D_DESC(DXGI_FORMAT format, UINT width, UINT height, UINT arraySize = 1,")
cpp_quote("            UINT mipLevels = 0, UINT bindFlags = D3D11_BIND_SHADER_RESOURCE,")
cpp_quote("            D3D11_USAGE usage = D3D11_USAGE_DEFAULT, UINT cpuaccessFlags = 0, UINT sampleCount = 1," )
cpp_quote("            UINT sampleQuality = 0, UINT miscFlags = 0) {")
cpp_quote("        Width = width;")
cpp_quote("        Height = height;")
cpp_quote("        MipLevels = mipLevels;")
cpp_quote("        ArraySize = arraySize;")
cpp_quote("        Format = format;")
cpp_quote("        SampleDesc.Count = sampleCount;")
cpp_quote("        SampleDesc.Quality = sampleQuality;")
cpp_quote("        Usage = usage;")
cpp_quote("        BindFlags = bindFlags;")
cpp_quote("        CPUAccessFlags = cpuaccessFlags;")
cpp_quote("        MiscFlags = miscFlags;")
cpp_quote("    }" )
cpp_quote("    ~CD3D11_TEXTURE2D_DESC() {}")
cpp_quote("    operator const D3D11_TEXTURE2D_DESC&() const { return *this; }")
cpp_quote("};")
cpp_quote("#endif")

typedef struct D3D11_TEXTURE3D_DESC
{
    UINT Width;
    UINT Height;
    UINT Depth;
    UINT MipLevels;
    DXGI_FORMAT Format;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
} D3D11_TEXTURE3D_DESC;

/*
 *
 * D3D11 video decoder profiles
 *
 */

/* MPEG1 & MPEG2 */
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG2_MOCOMP,                      0xe6a9f44b,0x61b0,0x4563,0x9e,0xa4,0x63,0xd2,0xa3,0xc6,0xfe,0x66);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG2_IDCT,                        0xbf22ad00,0x03ea,0x4690,0x80,0x77,0x47,0x33,0x46,0x20,0x9b,0x7e);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG2_VLD,                         0xee27417f,0x5e28,0x4e65,0xbe,0xea,0x1d,0x26,0xb5,0x08,0xad,0xc9);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG1_VLD,                         0x6f3ec719,0x3735,0x42cc,0x80,0x63,0x65,0xcc,0x3c,0xb3,0x66,0x16);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG2and1_VLD,                     0x86695f12,0x340e,0x4f04,0x9f,0xd3,0x92,0x53,0xdd,0x32,0x74,0x60);")

/* H264 */
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_MOCOMP_NOFGT,                 0x1b81be64,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_MOCOMP_FGT,                   0x1b81be65,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_IDCT_NOFGT,                   0x1b81be66,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_IDCT_FGT,                     0x1b81be67,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_NOFGT,                    0x1b81be68,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_FGT,                      0x1b81be69,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_WITHFMOASO_NOFGT,         0xd5f04ff9,0x3418,0x45d8,0x95,0x61,0x32,0xa7,0x6a,0xae,0x2d,0xdd);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_STEREO_PROGRESSIVE_NOFGT, 0xd79be8da,0x0cf1,0x4c81,0xb8,0x2a,0x69,0xa4,0xe2,0x36,0xf4,0x3d);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_STEREO_NOFGT,             0xf9aaccbb,0xc2b6,0x4cfc,0x87,0x79,0x57,0x07,0xb1,0x76,0x05,0x52);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_MULTIVIEW_NOFGT,          0x705b9d82,0x76cf,0x49d6,0xb7,0xe6,0xac,0x88,0x72,0xdb,0x01,0x3c);")

/* WMV8 */
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_WMV8_POSTPROC,                     0x1b81be80,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_WMV8_MOCOMP,                       0x1b81be81,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")

/* WMV9 */
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_WMV9_POSTPROC,                     0x1b81be90,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_WMV9_MOCOMP,                       0x1b81be91,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_WMV9_IDCT,                         0x1b81be94,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")

/* VC1 */
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_POSTPROC,                      0x1b81beA0,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_MOCOMP,                        0x1b81beA1,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_IDCT,                          0x1b81beA2,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_VLD,                           0x1b81beA3,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_D2010,                         0x1b81beA4,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);")

/* MPEG4 */
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG4PT2_VLD_SIMPLE,               0xefd64d74,0xc9e8,0x41d7,0xa5,0xe9,0xe9,0xb0,0xe3,0x9f,0xa3,0x19);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG4PT2_VLD_ADVSIMPLE_NOGMC,      0xed418a9f,0x010d,0x4eda,0x9a,0xe3,0x9a,0x65,0x35,0x8d,0x8d,0x2e);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG4PT2_VLD_ADVSIMPLE_GMC,        0xab998b5b,0x4258,0x44a9,0x9f,0xeb,0x94,0xe5,0x97,0xa6,0xba,0xae);")

/* HVEC */
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_HEVC_VLD_MAIN,                     0x5b11d51b,0x2f4c,0x4452,0xbc,0xc3,0x09,0xf2,0xa1,0x16,0x0c,0xc0);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_HEVC_VLD_MAIN10,                   0x107af0e0,0xef1a,0x4d19,0xab,0xa8,0x67,0xa1,0x63,0x07,0x3d,0x13);")

/* VP9 */
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_VP9_VLD_PROFILE0,                  0x463707f8,0xa1d0,0x4585,0x87,0x6d,0x83,0xaa,0x6d,0x60,0xb8,0x9e);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_VP9_VLD_10BIT_PROFILE2,            0xa4c749ef,0x6ecf,0x48aa,0x84,0x48,0x50,0xa7,0xa1,0x16,0x5f,0xf7);")
cpp_quote("DEFINE_GUID(D3D11_DECODER_PROFILE_VP8_VLD,                           0x90b899ea,0x3a62,0x4705,0x88,0xb3,0x8d,0xf0,0x4b,0x27,0x44,0xe7);")

typedef struct D3D11_VIDEO_DECODER_DESC
{
    GUID Guid;
    UINT SampleWidth;
    UINT SampleHeight;
    DXGI_FORMAT OutputFormat;
} D3D11_VIDEO_DECODER_DESC;

typedef struct D3D11_VIDEO_DECODER_CONFIG
{
    GUID guidConfigBitstreamEncryption;
    GUID guidConfigMBcontrolEncryption;
    GUID guidConfigResidDiffEncryption;
    UINT ConfigBitstreamRaw;
    UINT ConfigMBcontrolRasterOrder;
    UINT ConfigResidDiffHost;
    UINT ConfigSpatialResid8;
    UINT ConfigResid8Subtraction;
    UINT ConfigSpatialHost8or9Clipping;
    UINT ConfigSpatialResidInterleaved;
    UINT ConfigIntraResidUnsigned;
    UINT ConfigResidDiffAccelerator;
    UINT ConfigHostInverseScan;
    UINT ConfigSpecificIDCT;
    UINT Config4GroupedCoefs;
    USHORT ConfigMinRenderTargetBuffCount;
    USHORT ConfigDecoderSpecific;
} D3D11_VIDEO_DECODER_CONFIG;

typedef enum D3D11_VIDEO_FRAME_FORMAT
{
    D3D11_VIDEO_FRAME_FORMAT_PROGRESSIVE                   = 0,
    D3D11_VIDEO_FRAME_FORMAT_INTERLACED_TOP_FIELD_FIRST    = 1,
    D3D11_VIDEO_FRAME_FORMAT_INTERLACED_BOTTOM_FIELD_FIRST = 2
} D3D11_VIDEO_FRAME_FORMAT;

typedef enum D3D11_VIDEO_USAGE
{
    D3D11_VIDEO_USAGE_PLAYBACK_NORMAL = 0,
    D3D11_VIDEO_USAGE_OPTIMAL_SPEED   = 1,
    D3D11_VIDEO_USAGE_OPTIMAL_QUALITY = 2
} D3D11_VIDEO_USAGE;

typedef struct D3D11_VIDEO_PROCESSOR_CONTENT_DESC
{
    D3D11_VIDEO_FRAME_FORMAT InputFrameFormat;
    DXGI_RATIONAL InputFrameRate;
    UINT InputWidth;
    UINT InputHeight;
    DXGI_RATIONAL OutputFrameRate;
    UINT OutputWidth;
    UINT OutputHeight;
    D3D11_VIDEO_USAGE Usage;
} D3D11_VIDEO_PROCESSOR_CONTENT_DESC;

typedef struct D3D11_VIDEO_PROCESSOR_CAPS
{
    UINT DeviceCaps;
    UINT FeatureCaps;
    UINT FilterCaps;
    UINT InputFormatCaps;
    UINT AutoStreamCaps;
    UINT StereoCaps;
    UINT RateConversionCapsCount;
    UINT MaxInputStreams;
    UINT MaxStreamStates;
} D3D11_VIDEO_PROCESSOR_CAPS;

typedef struct D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS
{
    UINT PastFrames;
    UINT FutureFrames;
    UINT ProcessorCaps;
    UINT ITelecineCaps;
    UINT CustomRateCount;
} D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS;

typedef struct D3D11_VIDEO_PROCESSOR_CUSTOM_RATE
{
    DXGI_RATIONAL CustomRate;
    UINT OutputFrames;
    BOOL InputInterlaced;
    UINT InputFramesOrFields;
} D3D11_VIDEO_PROCESSOR_CUSTOM_RATE;

typedef enum D3D11_VIDEO_PROCESSOR_FILTER
{
    D3D11_VIDEO_PROCESSOR_FILTER_BRIGHTNESS         = 0,
    D3D11_VIDEO_PROCESSOR_FILTER_CONTRAST           = 1,
    D3D11_VIDEO_PROCESSOR_FILTER_HUE                = 2,
    D3D11_VIDEO_PROCESSOR_FILTER_SATURATION         = 3,
    D3D11_VIDEO_PROCESSOR_FILTER_NOISE_REDUCTION    = 4,
    D3D11_VIDEO_PROCESSOR_FILTER_EDGE_ENHANCEMENT   = 5,
    D3D11_VIDEO_PROCESSOR_FILTER_ANAMORPHIC_SCALING = 6,
    D3D11_VIDEO_PROCESSOR_FILTER_STEREO_ADJUSTMENT  = 7
} D3D11_VIDEO_PROCESSOR_FILTER;

typedef struct D3D11_VIDEO_PROCESSOR_FILTER_RANGE
{
    int Minimum;
    int Maximum;
    int Default;
    float Multiplier;
} D3D11_VIDEO_PROCESSOR_FILTER_RANGE;

typedef enum D3D11_AUTHENTICATED_CHANNEL_TYPE
{
    D3D11_AUTHENTICATED_CHANNEL_D3D11           = 1,
    D3D11_AUTHENTICATED_CHANNEL_DRIVER_SOFTWARE = 2,
    D3D11_AUTHENTICATED_CHANNEL_DRIVER_HARDWARE = 3
} D3D11_AUTHENTICATED_CHANNEL_TYPE;

typedef enum D3D11_VDOV_DIMENSION
{
    D3D11_VDOV_DIMENSION_UNKNOWN   = 0,
    D3D11_VDOV_DIMENSION_TEXTURE2D = 1
} D3D11_VDOV_DIMENSION;

typedef struct D3D11_TEX2D_VDOV
{
    UINT ArraySlice;
} D3D11_TEX2D_VDOV;

typedef struct D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC
{
    GUID DecodeProfile;
    D3D11_VDOV_DIMENSION ViewDimension;
    union {
        D3D11_TEX2D_VDOV Texture2D;
    };
} D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC;

typedef enum D3D11_VPIV_DIMENSION
{
    D3D11_VPIV_DIMENSION_UNKNOWN   = 0,
    D3D11_VPIV_DIMENSION_TEXTURE2D = 1
} D3D11_VPIV_DIMENSION;

typedef struct D3D11_TEX2D_VPIV
{
    UINT MipSlice;
    UINT ArraySlice;
} D3D11_TEX2D_VPIV;

typedef struct D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC
{
    UINT FourCC;
    D3D11_VPIV_DIMENSION ViewDimension;
    union {
        D3D11_TEX2D_VPIV Texture2D;
    };
} D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC;

typedef enum D3D11_VPOV_DIMENSION
{
    D3D11_VPOV_DIMENSION_UNKNOWN        = 0,
    D3D11_VPOV_DIMENSION_TEXTURE2D      = 1,
    D3D11_VPOV_DIMENSION_TEXTURE2DARRAY = 2
} D3D11_VPOV_DIMENSION;

typedef struct D3D11_TEX2D_VPOV
{
    UINT MipSlice;
} D3D11_TEX2D_VPOV;

typedef struct D3D11_TEX2D_ARRAY_VPOV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_VPOV;

typedef struct D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC
{
    D3D11_VPOV_DIMENSION ViewDimension;
    union {
        D3D11_TEX2D_VPOV Texture2D;
        D3D11_TEX2D_ARRAY_VPOV Texture2DArray;
    };
} D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC;

typedef struct D3D11_VIDEO_CONTENT_PROTECTION_CAPS
{
    UINT Caps;
    UINT KeyExchangeTypeCount;
    UINT BlockAlignmentSize;
    ULONGLONG ProtectedMemorySize;
} D3D11_VIDEO_CONTENT_PROTECTION_CAPS;

typedef struct D3D11_ENCRYPTED_BLOCK_INFO
{
    UINT NumEncryptedBytesAtBeginning;
    UINT NumBytesInSkipPattern;
    UINT NumBytesInEncryptPattern;
} D3D11_ENCRYPTED_BLOCK_INFO;

typedef struct D3D11_VIDEO_DECODER_BUFFER_DESC
{
    D3D11_VIDEO_DECODER_BUFFER_TYPE BufferType;
    UINT BufferIndex;
    UINT DataOffset;
    UINT DataSize;
    UINT FirstMBaddress;
    UINT NumMBsInBuffer;
    UINT Width;
    UINT Height;
    UINT Stride;
    UINT ReservedBits;
    void *pIV;
    UINT IVSize;
    BOOL PartialEncryption;
    D3D11_ENCRYPTED_BLOCK_INFO EncryptedBlockInfo;
} D3D11_VIDEO_DECODER_BUFFER_DESC;

typedef struct D3D11_VIDEO_DECODER_EXTENSION
{
    UINT Function;
    void *pPrivateInputData;
    UINT PrivateInputDataSize;
    void *pPrivateOutputData;
    UINT PrivateOutputDataSize;
    UINT ResourceCount;
    ID3D11Resource **ppResourceList;
} D3D11_VIDEO_DECODER_EXTENSION;

typedef struct D3D11_VIDEO_COLOR_YCbCrA
{
    float Y;
    float Cb;
    float Cr;
    float A;
} D3D11_VIDEO_COLOR_YCbCrA;

typedef struct D3D11_VIDEO_COLOR_RGBA
{
    float R;
    float G;
    float B;
    float A;
} D3D11_VIDEO_COLOR_RGBA;

typedef struct D3D11_VIDEO_COLOR
{
    union
    {
        D3D11_VIDEO_COLOR_YCbCrA YCbCr;
        D3D11_VIDEO_COLOR_RGBA RGBA;
    };
} D3D11_VIDEO_COLOR;

typedef struct D3D11_VIDEO_PROCESSOR_COLOR_SPACE
{
    UINT Usage : 1;
    UINT RGB_Range : 1;
    UINT YCbCr_Matrix : 1;
    UINT YCbCr_xvYCC : 1;
    UINT Nominal_Range : 2;
    UINT Reserved : 26;
} D3D11_VIDEO_PROCESSOR_COLOR_SPACE;

typedef struct D3D11_VIDEO_PROCESSOR_STREAM
{
    BOOL Enable;
    UINT OutputIndex;
    UINT InputFrameOrField;
    UINT PastFrames;
    UINT FutureFrames;
    ID3D11VideoProcessorInputView **ppPastSurfaces;
    ID3D11VideoProcessorInputView *pInputSurface;
    ID3D11VideoProcessorInputView **ppFutureSurfaces;
    ID3D11VideoProcessorInputView **ppPastSurfacesRight;
    ID3D11VideoProcessorInputView *pInputSurfaceRight;
    ID3D11VideoProcessorInputView **ppFutureSurfacesRight;
} D3D11_VIDEO_PROCESSOR_STREAM;

typedef struct D3D11_OMAC
{
      BYTE Omac[D3D11_OMAC_SIZE];
} D3D11_OMAC;

typedef struct D3D11_AUTHENTICATED_CONFIGURE_OUTPUT
{
    D3D11_OMAC omac;
    GUID ConfigureType;
    HANDLE hChannel;
    UINT SequenceNumber;
    HRESULT ReturnCode;
} D3D11_AUTHENTICATED_CONFIGURE_OUTPUT;

typedef struct D3D11_QUERY_DATA_TIMESTAMP_DISJOINT
{
    UINT64 Frequency;
    BOOL Disjoint;
} D3D11_QUERY_DATA_TIMESTAMP_DISJOINT;

typedef struct D3D11_QUERY_DATA_PIPELINE_STATISTICS
{
    UINT64 IAVertices;
    UINT64 IAPrimitives;
    UINT64 VSInvocations;
    UINT64 GSInvocations;
    UINT64 GSPrimitives;
    UINT64 CInvocations;
    UINT64 CPrimitives;
    UINT64 PSInvocations;
    UINT64 HSInvocations;
    UINT64 DSInvocations;
    UINT64 CSInvocations;
} D3D11_QUERY_DATA_PIPELINE_STATISTICS;

typedef struct D3D11_DRAW_INSTANCED_INDIRECT_ARGS
{
    UINT VertexCountPerInstance;
    UINT InstanceCount;
    UINT StartVertexLocation;
    UINT StartInstanceLocation;
} D3D11_DRAW_INSTANCED_INDIRECT_ARGS;

typedef struct D3D11_DRAW_INDEXED_INSTANCED_INDIRECT_ARGS
{
    UINT IndexCountPerInstance;
    UINT InstanceCount;
    UINT StartIndexLocation;
    INT BaseVertexLocation;
    UINT StartInstanceLocation;
} D3D11_DRAW_INDEXED_INSTANCED_INDIRECT_ARGS;

typedef struct D3D11_AUTHENTICATED_CONFIGURE_INPUT
{
    D3D11_OMAC omac;
    GUID ConfigureType;
    HANDLE hChannel;
    UINT SequenceNumber;
} D3D11_AUTHENTICATED_CONFIGURE_INPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_INPUT
{
    GUID QueryType;
    HANDLE hChannel;
    UINT SequenceNumber;
} D3D11_AUTHENTICATED_QUERY_INPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT
{
    D3D11_OMAC omac;
    GUID QueryType;
    HANDLE hChannel;
    UINT SequenceNumber;
    HRESULT ReturnCode;
} D3D11_AUTHENTICATED_QUERY_OUTPUT;

typedef union D3D11_AUTHENTICATED_PROTECTION_FLAGS
{
    struct
    {
        UINT ProtectionEnabled              : 1;
        UINT OverlayOrFullscreenRequired    : 1;
        UINT Reserved                       : 30;
    } Flags;
    UINT Value;
} D3D11_AUTHENTICATED_PROTECTION_FLAGS;

typedef struct D3D11_AUTHENTICATED_QUERY_PROTECTION_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    D3D11_AUTHENTICATED_PROTECTION_FLAGS ProtectionFlags;
} D3D11_AUTHENTICATED_QUERY_PROTECTION_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_CHANNEL_TYPE_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    D3D11_AUTHENTICATED_CHANNEL_TYPE ChannelType;
} D3D11_AUTHENTICATED_QUERY_CHANNEL_TYPE_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_DEVICE_HANDLE_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    HANDLE DeviceHandle;
} D3D11_AUTHENTICATED_QUERY_DEVICE_HANDLE_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_CRYPTO_SESSION_INPUT
{
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    HANDLE DecoderHandle;
} D3D11_AUTHENTICATED_QUERY_CRYPTO_SESSION_INPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_CRYPTO_SESSION_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    HANDLE DecoderHandle;
    HANDLE CryptoSessionHandle;
    HANDLE DeviceHandle;
} D3D11_AUTHENTICATED_QUERY_CRYPTO_SESSION_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_COUNT_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT RestrictedSharedResourceProcessCount;
} D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_COUNT_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_INPUT
{
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    UINT ProcessIndex;
} D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_INPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT ProcessIndex;
    D3D11_AUTHENTICATED_PROCESS_IDENTIFIER_TYPE ProcessIdentifier;
    HANDLE ProcessHandle;
} D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_UNRESTRICTED_PROTECTED_SHARED_RESOURCE_COUNT_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT UnrestrictedProtectedSharedResourceCount;
} D3D11_AUTHENTICATED_QUERY_UNRESTRICTED_PROTECTED_SHARED_RESOURCE_COUNT_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_COUNT_INPUT
{
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    HANDLE DeviceHandle;
    HANDLE CryptoSessionHandle;
} D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_COUNT_INPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_COUNT_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    HANDLE DeviceHandle;
    HANDLE CryptoSessionHandle;
    UINT OutputIDCount;
} D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_COUNT_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_INPUT
{
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    HANDLE DeviceHandle;
    HANDLE CryptoSessionHandle;
    UINT OutputIDIndex;
} D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_INPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    HANDLE DeviceHandle;
    HANDLE CryptoSessionHandle;
    UINT OutputIDIndex;
    UINT64 OutputID;
} D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_ACESSIBILITY_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    D3D11_BUS_TYPE BusType;
    BOOL AccessibleInContiguousBlocks;
    BOOL AccessibleInNonContiguousBlocks;
} D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_COUNT_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT EncryptionGuidCount;
} D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_COUNT_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_INPUT
{
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    UINT EncryptionGuidIndex;
} D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_INPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT EncryptionGuidIndex;
    GUID EncryptionGuid;
} D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_OUTPUT;

typedef struct D3D11_AUTHENTICATED_QUERY_CURRENT_ACCESSIBILITY_ENCRYPTION_OUTPUT
{
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    GUID EncryptionGuid;
} D3D11_AUTHENTICATED_QUERY_CURRENT_ACCESSIBILITY_ENCRYPTION_OUTPUT;

typedef struct D3D11_AUTHENTICATED_CONFIGURE_INITIALIZE_INPUT
{
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    UINT StartSequenceQuery;
    UINT StartSequenceConfigure;
} D3D11_AUTHENTICATED_CONFIGURE_INITIALIZE_INPUT;

typedef struct D3D11_AUTHENTICATED_CONFIGURE_PROTECTION_INPUT
{
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    D3D11_AUTHENTICATED_PROTECTION_FLAGS Protections;
} D3D11_AUTHENTICATED_CONFIGURE_PROTECTION_INPUT;

typedef struct D3D11_AUTHENTICATED_CONFIGURE_CRYPTO_SESSION_INPUT
{
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    HANDLE DecoderHandle;
    HANDLE CryptoSessionHandle;
    HANDLE DeviceHandle;
} D3D11_AUTHENTICATED_CONFIGURE_CRYPTO_SESSION_INPUT;

typedef struct D3D11_AUTHENTICATED_CONFIGURE_SHARED_RESOURCE_INPUT
{
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    D3D11_AUTHENTICATED_PROCESS_IDENTIFIER_TYPE ProcessType;
    HANDLE ProcessHandle;
    BOOL AllowAccess;
} D3D11_AUTHENTICATED_CONFIGURE_SHARED_RESOURCE_INPUT;

typedef struct D3D11_AUTHENTICATED_CONFIGURE_ACCESSIBLE_ENCRYPTION_INPUT
{
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    GUID EncryptionGuid;
} D3D11_AUTHENTICATED_CONFIGURE_ACCESSIBLE_ENCRYPTION_INPUT;

[
    object,
    local,
    uuid(1841e5c8-16b0-489b-bcc8-44cfb0d5deae)
]
interface ID3D11DeviceChild : IUnknown
{
    void GetDevice(
        [out] ID3D11Device **ppDevice);
    HRESULT GetPrivateData(
        [in] REFGUID guid,
        [in, out] UINT *pDataSize,
        [out] void *pData);
    HRESULT SetPrivateData(
        [in] REFGUID guid,
        [in] UINT DataSize,
        [in] const void *pData);
    HRESULT SetPrivateDataInterface(
        [in] REFGUID guid,
        [in] const IUnknown *pData);
}

[
    object,
    local,
    uuid(4b35d0cd-1e15-4258-9c98-1b1333f6dd3b)
]
interface ID3D11Asynchronous : ID3D11DeviceChild
{
    UINT GetDataSize();
}

[
    object,
    local,
    uuid(d6c00747-87b7-425e-b84d-44d108560afd)
]
interface ID3D11Query : ID3D11Asynchronous
{
    void GetDesc(
        [out] D3D11_QUERY_DESC *pDesc);
}

[
    object,
    local,
    uuid(dc8e63f3-d12b-4952-b47b-5e45026a862d)
]
interface ID3D11Resource : ID3D11DeviceChild
{
    void GetType(
        [out] D3D11_RESOURCE_DIMENSION *pResourceDimension);
    void SetEvictionPriority(
        [in] UINT EvictionPriority);
    UINT GetEvictionPriority();
}

[
    object,
    local,
    uuid(839d1216-bb2e-412b-b7f4-a9dbebe08ed1)
]
interface ID3D11View : ID3D11DeviceChild
{
    void GetResource(
        [out] ID3D11Resource **ppResource);
}

[
    object,
    local,
    uuid(75b68faa-347d-4159-8f45-a0640f01cd9a)
]
interface ID3D11BlendState : ID3D11DeviceChild
{
    void GetDesc(
        [out] D3D11_BLEND_DESC *pDesc);
}

[
    object,
    local,
    uuid(48570b85-d1ee-4fcd-a250-eb350722b037)
]
interface ID3D11Buffer : ID3D11Resource
{
    void GetDesc(
        [out] D3D11_BUFFER_DESC *pDesc);
}

[
    object,
    local,
    uuid(a6cd7faa-b0b7-4a2f-9436-8662a65797cb)
]
interface ID3D11ClassInstance : ID3D11DeviceChild
{
    void GetClassLinkage(
        [out] ID3D11ClassLinkage **ppLinkage);
    void GetDesc(
        [out] D3D11_CLASS_INSTANCE_DESC *pDesc);
    void GetInstanceName(
        [out] LPSTR pInstanceName,
        [in, out] SIZE_T *pBufferLength);
    void GetTypeName(
        [out] LPSTR pTypeName,
        [in, out] SIZE_T *pBufferLength);
}

[
    object,
    local,
    uuid(ddf57cba-9543-46e4-a12b-f207a0fe7fed)
]
interface ID3D11ClassLinkage : ID3D11DeviceChild
{
    HRESULT GetClassInstance(
        [in] LPCSTR pClassInstanceName,
        [in] UINT InstanceIndex,
        [out] ID3D11ClassInstance **ppInstance);
    HRESULT CreateClassInstance(
        [in] LPCSTR pClassTypeName,
        [in] UINT ConstantBufferOffset,
        [in] UINT ConstantVectorOffset,
        [in] UINT TextureOffset,
        [in] UINT SamplerOffset,
        [out] ID3D11ClassInstance **ppInstance);
}

[
    object,
    local,
    uuid(a24bc4d1-769e-43f7-8013-98ff566c18e2)
]
interface ID3D11CommandList : ID3D11DeviceChild
{
    UINT GetContextFlags();
}

[
    object,
    local,
    uuid(4f5b196e-c2bd-495e-bd01-1fded38e4969)
]
interface ID3D11ComputeShader : ID3D11DeviceChild
{
}

[
    object,
    local,
    uuid(6e8c49fb-a371-4770-b440-29086022b741)
]
interface ID3D11Counter : ID3D11Asynchronous
{
    void GetDesc(
        [out] D3D11_COUNTER_DESC *pDesc);
}

[
    object,
    local,
    uuid(03823efb-8d8f-4e1c-9aa2-f64bb2cbfdf1)
]
interface ID3D11DepthStencilState : ID3D11DeviceChild
{
    void GetDesc(
        [out] D3D11_DEPTH_STENCIL_DESC *pDesc);
}

[
    object,
    local,
    uuid(9fdac92a-1876-48c3-afad-25b94f84a9b6)
]
interface ID3D11DepthStencilView : ID3D11View
{
    void GetDesc(
        [out] D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc);
}

[
    object,
    local,
    uuid(f582c508-0f36-490c-9977-31eece268cfa)
]
interface ID3D11DomainShader : ID3D11DeviceChild
{
}

[
    object,
    local,
    uuid(38325b96-effb-4022-ba02-2e795b70275c)
]
interface ID3D11GeometryShader : ID3D11DeviceChild
{
}

[
    object,
    local,
    uuid(8e5c6061-628a-4c8e-8264-bbe45cb3d5dd)
]
interface ID3D11HullShader : ID3D11DeviceChild
{
}

[
    object,
    local,
    uuid(e4819ddc-4cf0-4025-bd26-5de82a3e07b7)
]
interface ID3D11InputLayout : ID3D11DeviceChild
{
}

[
    object,
    local,
    uuid(ea82e40d-51dc-4f33-93d4-db7c9125ae8c)
]
interface ID3D11PixelShader : ID3D11DeviceChild
{
}

[
    object,
    local,
    uuid(9eb576dd-9f77-4d86-81aa-8bab5fe490e2)
]
interface ID3D11Predicate : ID3D11Query
{
}

[
    object,
    local,
    uuid(9bb4ab81-ab1a-4d8f-b506-fc04200b6ee7)
]
interface ID3D11RasterizerState : ID3D11DeviceChild
{
    void GetDesc(
        [out] D3D11_RASTERIZER_DESC *pDesc);
}

[
    object,
    local,
    uuid(dfdba067-0b8d-4865-875b-d7b4516cc164)
]
interface ID3D11RenderTargetView : ID3D11View
{
    void GetDesc(
        [out] D3D11_RENDER_TARGET_VIEW_DESC *pDesc);
}

[
    object,
    local,
    uuid(da6fea51-564c-4487-9810-f0d0f9b4e3a5)
]
interface ID3D11SamplerState : ID3D11DeviceChild
{
    void GetDesc(
        [out] D3D11_SAMPLER_DESC *pDesc);
}

[
    object,
    local,
    uuid(b0e06fe0-8192-4e1a-b1ca-36d7414710b2)
]
interface ID3D11ShaderResourceView : ID3D11View
{
    void GetDesc(
        [out] D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc);
}

[
    object,
    local,
    uuid(f8fb5c27-c6b3-4f75-a4c8-439af2ef564c),
]
interface ID3D11Texture1D : ID3D11Resource
{
    void GetDesc(
        [out] D3D11_TEXTURE1D_DESC *pDesc);
}

[
    object,
    local,
    uuid(6f15aaf2-d208-4e89-9ab4-489535d34f9c)
]
interface ID3D11Texture2D : ID3D11Resource
{
    void GetDesc(
        [out] D3D11_TEXTURE2D_DESC *pDesc);
}

[
    object,
    local,
    uuid(037e866e-f56d-4357-a8af-9dabbe6e250e)
]
interface ID3D11Texture3D : ID3D11Resource
{
    void GetDesc(
        [out] D3D11_TEXTURE3D_DESC *pDesc);
}

[
    object,
    local,
    uuid(28acf509-7f5c-48f6-8611-f316010a6380)
]
interface ID3D11UnorderedAccessView : ID3D11View
{
    void GetDesc(
        [out] D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc);
}

[
    object,
    local,
    uuid(3b301d64-d678-4289-8897-22f8928b72f3)
]
interface ID3D11VertexShader : ID3D11DeviceChild
{
}

[
    object,
    local,
    uuid(c0bfa96c-e089-44fb-8eaf-26f8796190da)
]
interface ID3D11DeviceContext : ID3D11DeviceChild
{
    void VSSetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [in] ID3D11Buffer *const *ppConstantBuffers);
    void PSSetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [in] ID3D11ShaderResourceView *const *ppShaderResourceViews);
    void PSSetShader(
        [in] ID3D11PixelShader *pPixelShader,
        [in] ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);
    void PSSetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [in] ID3D11SamplerState *const *ppSamplers);
    void VSSetShader(
        [in] ID3D11VertexShader *pVertexShader,
        [in] ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);
    void DrawIndexed(
        [in] UINT IndexCount,
        [in] UINT StartIndexLocation,
        [in] INT BaseVertexLocation);
    void Draw(
        [in] UINT VertexCount,
        [in] UINT StartVertexLocation);
    HRESULT Map(
        [in] ID3D11Resource *pResource,
        [in] UINT Subresource,
        [in] D3D11_MAP MapType,
        [in] UINT MapFlags,
        [out] D3D11_MAPPED_SUBRESOURCE *pMappedResource);
    void Unmap(
        [in] ID3D11Resource *pResource,
        [in] UINT Subresource);
    void PSSetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [in] ID3D11Buffer *const *ppConstantBuffers);
    void IASetInputLayout(
        [in] ID3D11InputLayout *pInputLayout);
    void IASetVertexBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [in] ID3D11Buffer *const *ppVertexBuffers,
        [in] const UINT *pStrides,
        [in] const UINT *pOffsets);
    void IASetIndexBuffer(
        [in] ID3D11Buffer *pIndexBuffer,
        [in] DXGI_FORMAT  Format,
        [in] UINT Offset);
    void DrawIndexedInstanced(
        [in] UINT IndexCountPerInstance,
        [in] UINT InstanceCount,
        [in] UINT StartIndexLocation,
        [in] INT BaseVertexLocation,
        [in] UINT StartInstanceLocation);
    void DrawInstanced(
        [in] UINT VertexCountPerInstance,
        [in] UINT InstanceCount,
        [in] UINT StartVertexLocation,
        [in] UINT StartInstanceLocation);
    void GSSetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [in] ID3D11Buffer *const *ppConstantBuffers);
    void GSSetShader(
        [in] ID3D11GeometryShader *pShader,
        [in] ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);
    void IASetPrimitiveTopology(
        [in] D3D11_PRIMITIVE_TOPOLOGY Topology);
    void VSSetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [in] ID3D11ShaderResourceView *const *ppShaderResourceViews);
    void VSSetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [in] ID3D11SamplerState *const *ppSamplers);
    void Begin(
        [in] ID3D11Asynchronous *pAsync);
    void End(
        [in] ID3D11Asynchronous *pAsync);
    HRESULT GetData(
        [in] ID3D11Asynchronous *pAsync,
        [in] void *pData,
        [in] UINT DataSize,
        [in] UINT GetDataFlags);
    void SetPredication(
        [in] ID3D11Predicate *pPredicate,
        [in] BOOL PredicateValue);
    void GSSetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [in] ID3D11ShaderResourceView *const *ppShaderResourceViews);
    void GSSetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [in] ID3D11SamplerState *const *ppSamplers);
    void OMSetRenderTargets(
        [in] UINT NumViews,
        [in] ID3D11RenderTargetView *const *ppRenderTargetViews,
        [in] ID3D11DepthStencilView *pDepthStencilView);
    void OMSetRenderTargetsAndUnorderedAccessViews(
        [in] UINT NumRTVs,
        [in] ID3D11RenderTargetView *const *ppRenderTargetViews,
        [in] ID3D11DepthStencilView *pDepthStencilView,
        [in] UINT UAVStartSlot,
        [in] UINT NumUAVs,
        [in] ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        [in] const UINT *pUAVInitialCounts);
    void OMSetBlendState(
        [in] ID3D11BlendState *pBlendState,
        [in] const FLOAT BlendFactor[4],
        [in] UINT SampleMask);
    void OMSetDepthStencilState(
        [in] ID3D11DepthStencilState *pDepthStencilState,
        [in] UINT StencilRef);
    void SOSetTargets(
        [in] UINT NumBuffers,
        [in] ID3D11Buffer *const *ppSOTargets,
        [in] const UINT *pOffsets);
    void DrawAuto();
    void DrawIndexedInstancedIndirect(
        [in] ID3D11Buffer *pBufferForArgs,
        [in] UINT AlignedByteOffsetForArgs);
    void DrawInstancedIndirect(
        [in] ID3D11Buffer *pBufferForArgs,
        [in] UINT AlignedByteOffsetForArgs);
    void Dispatch(
        [in] UINT ThreadGroupCountX,
        [in] UINT ThreadGroupCountY,
        [in] UINT ThreadGroupCountZ);
    void DispatchIndirect(
        [in] ID3D11Buffer *pBufferForArgs,
        [in] UINT AlignedByteOffsetForArgs);
    void RSSetState(
        [in] ID3D11RasterizerState *pRasterizerState);
    void RSSetViewports(
        [in] UINT NumViewports,
        [in] const D3D11_VIEWPORT *pViewports);
    void RSSetScissorRects(
        [in] UINT NumRects,
        [in] const D3D11_RECT *pRects);
    void CopySubresourceRegion(
        [in] ID3D11Resource *pDstResource,
        [in] UINT DstSubresource,
        [in] UINT DstX,
        [in] UINT DstY,
        [in] UINT DstZ,
        [in] ID3D11Resource *pSrcResource,
        [in] UINT SrcSubresource,
        [in] const D3D11_BOX *pSrcBox);
    void CopyResource(
        [in] ID3D11Resource *pDstResource,
        [in] ID3D11Resource *pSrcResource);
    void UpdateSubresource(
        [in] ID3D11Resource *pDstResource,
        [in] UINT DstSubresource,
        [in] const D3D11_BOX *pDstBox,
        [in] const void *pSrcData,
        [in] UINT SrcRowPitch,
        [in] UINT SrcDepthPitch);
    void CopyStructureCount(
        [in] ID3D11Buffer *pDstBuffer,
        [in] UINT DstAlignedByteOffset,
        [in] ID3D11UnorderedAccessView *pSrcView);
    void ClearRenderTargetView(
        [in] ID3D11RenderTargetView *pRenderTargetView,
        [in] const FLOAT ColorRGBA[4]);
    void ClearUnorderedAccessViewUint(
        [in] ID3D11UnorderedAccessView *pUnorderedAccessView,
        [in] const UINT Values[4]);
    void ClearUnorderedAccessViewFloat(
        [in] ID3D11UnorderedAccessView *pUnorderedAccessView,
        [in] const FLOAT Values[4]);
    void ClearDepthStencilView(
        [in] ID3D11DepthStencilView *pDepthStencilView,
        [in] UINT ClearFlags,
        [in] FLOAT Depth,
        [in] UINT8 Stencil);
    void GenerateMips(
        [in] ID3D11ShaderResourceView *pShaderResourceView);
    void SetResourceMinLOD(
        [in] ID3D11Resource *pResource, FLOAT MinLOD);
    FLOAT GetResourceMinLOD(
        [in] ID3D11Resource *pResource);
    void ResolveSubresource(
        [in] ID3D11Resource *pDstResource,
        [in] UINT DstSubresource,
        [in] ID3D11Resource *pSrcResource,
        [in] UINT SrcSubresource,
        [in] DXGI_FORMAT Format);
    void ExecuteCommandList(
        [in] ID3D11CommandList *pCommandList,
        BOOL RestoreContextState);
    void HSSetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [in] ID3D11ShaderResourceView *const *ppShaderResourceViews);
    void HSSetShader(
        [in] ID3D11HullShader *pHullShader,
        [in] ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);
    void HSSetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [in] ID3D11SamplerState *const *ppSamplers);
    void HSSetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [in] ID3D11Buffer *const *ppConstantBuffers);
    void DSSetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [in] ID3D11ShaderResourceView *const *ppShaderResourceViews);
    void DSSetShader(
        [in] ID3D11DomainShader *pDomainShader,
        [in] ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);
    void DSSetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [in] ID3D11SamplerState *const *ppSamplers);
    void DSSetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [in] ID3D11Buffer *const *ppConstantBuffers);
    void CSSetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [in] ID3D11ShaderResourceView *const *ppShaderResourceViews);
    void CSSetUnorderedAccessViews(
        [in] UINT StartSlot,
        [in] UINT NumUAVs,
        [in] ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        [in] const UINT *pUAVInitialCounts);
    void CSSetShader(
        [in] ID3D11ComputeShader *pComputeShader,
        [in] ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);
    void CSSetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [in] ID3D11SamplerState *const *ppSamplers);
    void CSSetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [in] ID3D11Buffer *const *ppConstantBuffers);
    void VSGetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [out] ID3D11Buffer **ppConstantBuffers);
    void PSGetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [out] ID3D11ShaderResourceView **ppShaderResourceViews);
    void PSGetShader(
        [out] ID3D11PixelShader **ppPixelShader,
        [out] ID3D11ClassInstance **ppClassInstances,
        [in, out] UINT *pNumClassInstances);
    void PSGetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [out] ID3D11SamplerState **ppSamplers);
    void VSGetShader(
        [out] ID3D11VertexShader **ppVertexShader,
        [out] ID3D11ClassInstance **ppClassInstances,
        [in, out] UINT *pNumClassInstances);
    void PSGetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [out] ID3D11Buffer **ppConstantBuffers);
    void IAGetInputLayout(
        [out] ID3D11InputLayout **ppInputLayout);
    void IAGetVertexBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [out] ID3D11Buffer **ppVertexBuffers,
        [out] UINT *pStrides,
        [out] UINT *pOffsets);
    void IAGetIndexBuffer(
        [out] ID3D11Buffer **pIndexBuffer,
        [out] DXGI_FORMAT* Format,
        [out] UINT* Offset);
    void GSGetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [out] ID3D11Buffer **ppConstantBuffers);
    void GSGetShader(
        [out] ID3D11GeometryShader **ppGeometryShader,
        [out] ID3D11ClassInstance **ppClassInstances,
        [in, out] UINT *pNumClassInstances);
    void IAGetPrimitiveTopology(
        [out] D3D11_PRIMITIVE_TOPOLOGY *pTopology);
    void VSGetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [out] ID3D11ShaderResourceView **ppShaderResourceViews);
    void VSGetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [out] ID3D11SamplerState **ppSamplers);
    void GetPredication(
        [out] ID3D11Predicate **ppPredicate,
        [out] BOOL *pPredicateValue);
    void GSGetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [out] ID3D11ShaderResourceView **ppShaderResourceViews);
    void GSGetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [out] ID3D11SamplerState **ppSamplers);
    void OMGetRenderTargets(
        [in] UINT NumViews,
        [out] ID3D11RenderTargetView **ppRenderTargetViews,
        [out] ID3D11DepthStencilView **ppDepthStencilView);
    void OMGetRenderTargetsAndUnorderedAccessViews(
        [in] UINT NumRTVs,
        [out] ID3D11RenderTargetView **ppRenderTargetViews,
        [out] ID3D11DepthStencilView **ppDepthStencilView,
        [in] UINT UAVStartSlot,
        [in] UINT NumUAVs,
        [out] ID3D11UnorderedAccessView **ppUnorderedAccessViews);
    void OMGetBlendState(
        [out] ID3D11BlendState **ppBlendState,
        [out] FLOAT BlendFactor[4],
        [out] UINT *pSampleMask);
    void OMGetDepthStencilState(
        [out] ID3D11DepthStencilState **ppDepthStencilState,
        [out] UINT *pStencilRef);
    void SOGetTargets(
        [in] UINT NumBuffers,
        [out] ID3D11Buffer **ppSOTargets);
    void RSGetState(
        [out] ID3D11RasterizerState **ppRasterizerState);
    void RSGetViewports(
        [in, out] UINT *pNumViewports,
        [out] D3D11_VIEWPORT *pViewports);
    void RSGetScissorRects(
        [in, out] UINT *pNumRects,
        [out] D3D11_RECT *pRects);
    void HSGetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [out] ID3D11ShaderResourceView **ppShaderResourceViews);
    void HSGetShader(
        [out] ID3D11HullShader **ppHullShader,
        [out] ID3D11ClassInstance **ppClassInstances,
        [in, out] UINT *pNumClassInstances);
    void HSGetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [out] ID3D11SamplerState **ppSamplers);
    void HSGetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [out] ID3D11Buffer **ppConstantBuffers);
    void DSGetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [out] ID3D11ShaderResourceView **ppShaderResourceViews);
    void DSGetShader(
        [out] ID3D11DomainShader **ppDomainShader,
        [out] ID3D11ClassInstance **ppClassInstances,
        [in, out] UINT *pNumClassInstances);
    void DSGetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [out] ID3D11SamplerState **ppSamplers);
    void DSGetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [out] ID3D11Buffer **ppConstantBuffers);
    void CSGetShaderResources(
        [in] UINT StartSlot,
        [in] UINT NumViews,
        [out] ID3D11ShaderResourceView **ppShaderResourceViews);
    void CSGetUnorderedAccessViews(
        [in] UINT StartSlot,
        [in] UINT NumUAVs,
        [out] ID3D11UnorderedAccessView **ppUnorderedAccessViews);
    void CSGetShader(
        [out] ID3D11ComputeShader **ppComputeShader,
        [out] ID3D11ClassInstance **ppClassInstances,
        [in, out] UINT *pNumClassInstances);
    void CSGetSamplers(
        [in] UINT StartSlot,
        [in] UINT NumSamplers,
        [out] ID3D11SamplerState **ppSamplers);
    void CSGetConstantBuffers(
        [in] UINT StartSlot,
        [in] UINT NumBuffers,
        [out] ID3D11Buffer **ppConstantBuffers);
    void ClearState();
    void Flush();
    D3D11_DEVICE_CONTEXT_TYPE GetType();
    UINT GetContextFlags();
    HRESULT FinishCommandList(
        BOOL RestoreDeferredContextState,
        [out] ID3D11CommandList **ppCommandList);
}

[
    object,
    uuid(3015a308-dcbd-47aa-a747-192486d14d4a),
    local,
    pointer_default(unique)
]
interface ID3D11AuthenticatedChannel : ID3D11DeviceChild
{
    HRESULT GetCertificateSize(
        UINT *pCertificateSize);
    HRESULT GetCertificate(
        UINT CertificateSize,
        BYTE *pCertificate);
    void GetChannelHandle(
        HANDLE *pChannelHandle);
}

[
    object,
    uuid(9b32f9ad-bdcc-40a6-a39d-d5c865845720),
    local,
    pointer_default(unique)
]
interface ID3D11CryptoSession : ID3D11DeviceChild
{
    void GetCryptoType(
        GUID *pCryptoType);
    void GetDecoderProfile(
        GUID *pDecoderProfile);
    HRESULT GetCertificateSize(
        UINT *pCertificateSize);
    HRESULT GetCertificate(
        UINT CertificateSize,
        BYTE *pCertificate);
    void GetCryptoSessionHandle(
        HANDLE *pCryptoSessionHandle);
}

[
    object,
    uuid(3c9c5b51-995d-48d1-9b8d-fa5caeded65c),
    local,
    pointer_default(unique)
]
interface ID3D11VideoDecoder : ID3D11DeviceChild
{
    HRESULT GetCreationParameters(
        D3D11_VIDEO_DECODER_DESC *pVideoDesc,
        D3D11_VIDEO_DECODER_CONFIG *pConfig);
    HRESULT GetDriverHandle(
        HANDLE *pDriverHandle);
}

[
    object,
    uuid(31627037-53ab-4200-9061-05faa9ab45f9),
    local,
    pointer_default(unique)
]
interface ID3D11VideoProcessorEnumerator : ID3D11DeviceChild
{
    HRESULT GetVideoProcessorContentDesc(
        D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pContentDesc);
    HRESULT CheckVideoProcessorFormat(
        DXGI_FORMAT Format,
        UINT *pFlags);
    HRESULT GetVideoProcessorCaps(
        D3D11_VIDEO_PROCESSOR_CAPS *pCaps);
    HRESULT GetVideoProcessorRateConversionCaps(
        UINT TypeIndex,
        D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps);
    HRESULT GetVideoProcessorCustomRate(
        UINT TypeIndex,
        UINT CustomRateIndex,
        D3D11_VIDEO_PROCESSOR_CUSTOM_RATE *pRate);
    HRESULT GetVideoProcessorFilterRange(
        D3D11_VIDEO_PROCESSOR_FILTER Filter,
        D3D11_VIDEO_PROCESSOR_FILTER_RANGE *pRange);
}

[
    object,
    uuid(1d7b0652-185f-41c6-85ce-0c5be3d4ae6c),
    local,
    pointer_default(unique)
]
interface ID3D11VideoProcessor : ID3D11DeviceChild
{
    void GetContentDesc(
        D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc);
    void GetRateConversionCaps(
        D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps);
}

[
    object,
    uuid(c2931aea-2a85-4f20-860f-fba1fd256e18),
    local,
    pointer_default(unique)
]
interface ID3D11VideoDecoderOutputView : ID3D11View
{
    void GetDesc(
        D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc);
}

[
    object,
    uuid(11ec5a5f-51dc-4945-ab34-6e8c21300ea5),
    local,
    pointer_default(unique)
]
interface ID3D11VideoProcessorInputView : ID3D11View
{
    void GetDesc(
        D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc);
}

[
    object,
    uuid(a048285e-25a9-4527-bd93-d68b68c44254),
    local,
    pointer_default(unique)
]
interface ID3D11VideoProcessorOutputView : ID3D11View
{
    void GetDesc(
        D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc);
}

[
    object,
    uuid(10ec4d5b-975a-4689-b9e4-d0aac30fe333),
    local,
    pointer_default(unique)
]
interface ID3D11VideoDevice : IUnknown
{
    HRESULT CreateVideoDecoder(
        const D3D11_VIDEO_DECODER_DESC *pVideoDesc,
        const D3D11_VIDEO_DECODER_CONFIG *pConfig,
        ID3D11VideoDecoder **ppDecoder);
    HRESULT CreateVideoProcessor(
        ID3D11VideoProcessorEnumerator *pEnum,
        UINT RateConversionIndex,
        ID3D11VideoProcessor **ppVideoProcessor);
    HRESULT CreateAuthenticatedChannel(
        D3D11_AUTHENTICATED_CHANNEL_TYPE ChannelType,
        ID3D11AuthenticatedChannel **ppAuthenticatedChannel);
    HRESULT CreateCryptoSession(
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        const GUID *pKeyExchangeType,
        ID3D11CryptoSession **ppCryptoSession);
    HRESULT CreateVideoDecoderOutputView(
        ID3D11Resource *pResource,
        const D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc,
        ID3D11VideoDecoderOutputView **ppVDOVView);
    HRESULT CreateVideoProcessorInputView(
        ID3D11Resource *pResource,
        ID3D11VideoProcessorEnumerator *pEnum,
        const D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc,
        ID3D11VideoProcessorInputView **ppVPIView);
    HRESULT CreateVideoProcessorOutputView(
        ID3D11Resource *pResource,
        ID3D11VideoProcessorEnumerator *pEnum,
        const D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc,
        ID3D11VideoProcessorOutputView **ppVPOView);
    HRESULT CreateVideoProcessorEnumerator(
        const D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc,
        ID3D11VideoProcessorEnumerator **ppEnum);
    UINT GetVideoDecoderProfileCount();
    HRESULT GetVideoDecoderProfile(
        UINT Index,
        GUID *pDecoderProfile);
    HRESULT CheckVideoDecoderFormat(
        const GUID *pDecoderProfile,
        DXGI_FORMAT Format,
        BOOL *pSupported);
    HRESULT GetVideoDecoderConfigCount(
        const D3D11_VIDEO_DECODER_DESC *pDesc,
        UINT *pCount);
    HRESULT GetVideoDecoderConfig(
        const D3D11_VIDEO_DECODER_DESC *pDesc,
        UINT Index,
        D3D11_VIDEO_DECODER_CONFIG *pConfig);
    HRESULT GetContentProtectionCaps(
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        D3D11_VIDEO_CONTENT_PROTECTION_CAPS *pCaps);
    HRESULT CheckCryptoKeyExchange(
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        UINT Index,
        GUID *pKeyExchangeType);
    HRESULT SetPrivateData(
        REFGUID guid,
        UINT DataSize,
        const void *pData);
    HRESULT SetPrivateDataInterface(
        REFGUID guid,
        const IUnknown *pData);
}

[
    object,
    uuid(61f21c45-3c0e-4a74-9cea-67100d9ad5e4),
    local,
    pointer_default(unique)
]
interface ID3D11VideoContext : ID3D11DeviceChild
{
    HRESULT GetDecoderBuffer(
        [in] ID3D11VideoDecoder *decoder,
        [in] D3D11_VIDEO_DECODER_BUFFER_TYPE type,
        [out] UINT *buffer_size,
        [out] void **buffer
    );
    HRESULT ReleaseDecoderBuffer(
        [in] ID3D11VideoDecoder *decoder,
        [in] D3D11_VIDEO_DECODER_BUFFER_TYPE type
    );
    HRESULT DecoderBeginFrame(
        [in] ID3D11VideoDecoder *decoder,
        [in] ID3D11VideoDecoderOutputView *view,
        [in] UINT key_size,
        [in] const void *key
    );
    HRESULT DecoderEndFrame(
        [in] ID3D11VideoDecoder *decoder
    );
    HRESULT SubmitDecoderBuffers(
        [in] ID3D11VideoDecoder *decoder,
        [in] UINT buffers_count,
        [in] const D3D11_VIDEO_DECODER_BUFFER_DESC *buffer_desc
    );
    HRESULT DecoderExtension(
        [in] ID3D11VideoDecoder *decoder,
        [in] const D3D11_VIDEO_DECODER_EXTENSION *extension
    );
    void VideoProcessorSetOutputTargetRect(
        [in] ID3D11VideoProcessor *processor,
        [in] BOOL enable,
        [in] const RECT *rect
    );
    void VideoProcessorSetOutputBackgroundColor(
        [in] ID3D11VideoProcessor *processor,
        [in] BOOL y_cb_cr,
        [in] const D3D11_VIDEO_COLOR *color
    );
    void VideoProcessorSetOutputColorSpace(
        [in] ID3D11VideoProcessor *processor,
        [in] const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space
    );
    void VideoProcessorSetOutputAlphaFillMode(
        [in] ID3D11VideoProcessor *processor,
        [in] D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE alpha_fill_mode,
        [in] UINT stream_idx
    );
    void VideoProcessorSetOutputConstriction(
        [in] ID3D11VideoProcessor *processor,
        [in] BOOL enable,
        [in] SIZE size
    );
    void VideoProcessorSetOutputStereoMode(
        [in] ID3D11VideoProcessor *processor,
        [in] BOOL enable
    );
    HRESULT VideoProcessorSetOutputExtension(
        [in] ID3D11VideoProcessor *processor,
        [in] const GUID *guid,
        [in] UINT data_size,
        [in] void *data
    );
    void VideoProcessorGetOutputTargetRect(
        [in] ID3D11VideoProcessor *processor,
        [out] BOOL *enabled,
        [out] RECT *rect
    );
    void VideoProcessorGetOutputBackgroundColor(
        [in] ID3D11VideoProcessor *processor,
        [out] BOOL *y_cb_cr,
        [out] D3D11_VIDEO_COLOR *color
    );
    void VideoProcessorGetOutputColorSpace(
        [in] ID3D11VideoProcessor *processor,
        [out] D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space
    );
    void VideoProcessorGetOutputAlphaFillMode(
        [in] ID3D11VideoProcessor *processor,
        [out] D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE *alpha_fill_mode,
        [out] UINT *stream_idx
    );
    void VideoProcessorGetOutputConstriction(
        [in] ID3D11VideoProcessor *processor,
        [out] BOOL *enabled,
        [out] SIZE *size
    );
    void VideoProcessorGetOutputStereoMode(
        [in] ID3D11VideoProcessor *processor,
        [out] BOOL *enabled
    );
    HRESULT VideoProcessorGetOutputExtension(
        [in] ID3D11VideoProcessor *processor,
        [in] const GUID *guid,
        [in] UINT data_size,
        [out] void *data
    );
    void VideoProcessorSetStreamFrameFormat(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] D3D11_VIDEO_FRAME_FORMAT format
    );
    void VideoProcessorSetStreamColorSpace(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space
    );
    void VideoProcessorSetStreamOutputRate(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] D3D11_VIDEO_PROCESSOR_OUTPUT_RATE rate,
        [in] BOOL repeat,
        [in] const DXGI_RATIONAL *custom_rate
    );
    void VideoProcessorSetStreamSourceRect(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] BOOL enable,
        [in] const RECT *rect
    );
    void VideoProcessorSetStreamDestRect(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] BOOL enable,
        [in] const RECT *rect
    );
    void VideoProcessorSetStreamAlpha(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] BOOL enable,
        [in] float alpha
    );
    void VideoProcessorSetStreamPalette(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] UINT entry_count,
        [in] const UINT *entries
    );
    void VideoProcessorSetStreamPixelAspectRatio(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] BOOL enable,
        [in] const DXGI_RATIONAL *src_aspect_ratio,
        [in] const DXGI_RATIONAL *dst_aspect_ratio
    );
    void VideoProcessorSetStreamLumaKey(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] BOOL enable,
        [in] float lower,
        [in] float upper
    );
    void VideoProcessorSetStreamStereoFormat(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] BOOL enable,
        [in] D3D11_VIDEO_PROCESSOR_STEREO_FORMAT format,
        [in] BOOL left_view_frame0,
        [in] BOOL base_view_frame0,
        [in] D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE flip_mode,
        [in] int mono_offset
    );
    void VideoProcessorSetStreamAutoProcessingMode(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] BOOL enable
    );
    void VideoProcessorSetStreamFilter(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] D3D11_VIDEO_PROCESSOR_FILTER filter,
        [in] BOOL enable,
        [in] int level
    );
    HRESULT VideoProcessorSetStreamExtension(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] const GUID *guid,
        [in] UINT data_size,
        [in] void *data
    );
    void VideoProcessorGetStreamFrameFormat(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] D3D11_VIDEO_FRAME_FORMAT *format
    );
    void VideoProcessorGetStreamColorSpace(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space
    );
    void VideoProcessorGetStreamOutputRate(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] D3D11_VIDEO_PROCESSOR_OUTPUT_RATE *rate,
        [out] BOOL *repeat,
        [out] DXGI_RATIONAL *custom_rate
    );
    void VideoProcessorGetStreamSourceRect(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] BOOL *enabled,
        [out] RECT *rect
    );
    void VideoProcessorGetStreamDestRect(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] BOOL *enabled,
        [out] RECT *rect
    );
    void VideoProcessorGetStreamAlpha(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] BOOL *enabled,
        [out] float *alpha
    );
    void VideoProcessorGetStreamPalette(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] UINT entry_count,
        [out] UINT *entries
    );
    void VideoProcessorGetStreamPixelAspectRatio(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] BOOL *enabled,
        [out] DXGI_RATIONAL *src_aspect_ratio,
        [out] DXGI_RATIONAL *dst_aspect_ratio
    );
    void VideoProcessorGetStreamLumaKey(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] BOOL *enabled,
        [out] float *lower,
        [out] float *upper
    );
    void VideoProcessorGetStreamStereoFormat(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] BOOL *enabled,
        [out] D3D11_VIDEO_PROCESSOR_STEREO_FORMAT *format,
        [out] BOOL *left_view_frame0,
        [out] BOOL *base_view_frame0,
        [out] D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE *flip_mode,
        [out] int *mono_offset
    );
    void VideoProcessorGetStreamAutoProcessingMode(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] BOOL *enabled
    );
    void VideoProcessorGetStreamFilter(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] D3D11_VIDEO_PROCESSOR_FILTER filter,
        [out] BOOL *enabled,
        [out] int *level
    );
    HRESULT VideoProcessorGetStreamExtension(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] const GUID *guid,
        [in] UINT data_size,
        [out] void *data
    );
    HRESULT VideoProcessorBlt(
        [in] ID3D11VideoProcessor *processor,
        [in] ID3D11VideoProcessorOutputView *view,
        [in] UINT frame_idx,
        [in] UINT stream_count,
        [in] const D3D11_VIDEO_PROCESSOR_STREAM *streams
    );
    HRESULT NegotiateCryptoSessionKeyExchange(
        [in] ID3D11CryptoSession *session,
        [in] UINT data_size,
        [in, out] void *data
    );
    void EncryptionBlt(
        [in] ID3D11CryptoSession *session,
        [in] ID3D11Texture2D *src_surface,
        [in] ID3D11Texture2D *dst_surface,
        [in] UINT iv_size,
        [in, out] void *iv
    );
    void DecryptionBlt(
        [in] ID3D11CryptoSession *session,
        [in] ID3D11Texture2D *src_surface,
        [in] ID3D11Texture2D *dst_surface,
        [in] D3D11_ENCRYPTED_BLOCK_INFO *block_info,
        [in] UINT key_size,
        [in] const void *key,
        [in] UINT iv_size,
        [in, out] void *iv
    );
    void StartSessionKeyRefresh(
        [in] ID3D11CryptoSession *session,
        [in] UINT random_number_size,
        [out] void *random_number
    );
    void FinishSessionKeyRefresh(
        [in] ID3D11CryptoSession *session
    );
    HRESULT GetEncryptionBltKey(
        [in] ID3D11CryptoSession *session,
        [in] UINT key_size,
        [out] void *key
    );
    HRESULT NegotiateAuthenticatedChannelKeyExchange(
        [in] ID3D11AuthenticatedChannel *channel,
        [in] UINT data_size,
        [in, out] void *data
    );
    HRESULT QueryAuthenticatedChannel(
        [in] ID3D11AuthenticatedChannel *channel,
        [in] UINT input_size,
        [in] const void *input,
        [in] UINT output_size,
        [out] void *output
    );
    HRESULT ConfigureAuthenticatedChannel(
        [in] ID3D11AuthenticatedChannel *channel,
        [in] UINT input_size,
        [in] const void *input,
        [out] D3D11_AUTHENTICATED_CONFIGURE_OUTPUT *output
    );
    void VideoProcessorSetStreamRotation(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [in] BOOL enable,
        [in] D3D11_VIDEO_PROCESSOR_ROTATION rotation
    );
    void VideoProcessorGetStreamRotation(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_idx,
        [out] BOOL *enable,
        [out] D3D11_VIDEO_PROCESSOR_ROTATION *rotation
    );
}

[
    object,
    local,
    uuid(db6f6ddb-ac77-4e88-8253-819df9bbf140)
]
interface ID3D11Device : IUnknown
{
    HRESULT CreateBuffer(
        [in] const D3D11_BUFFER_DESC *pDesc,
        [in] const D3D11_SUBRESOURCE_DATA *pInitialData,
        [out] ID3D11Buffer **ppBuffer);
    HRESULT CreateTexture1D(
        [in] const D3D11_TEXTURE1D_DESC *pDesc,
        [in] const D3D11_SUBRESOURCE_DATA *pInitialData,
        [out] ID3D11Texture1D **ppTexture1D);
    HRESULT CreateTexture2D(
        [in] const D3D11_TEXTURE2D_DESC *pDesc,
        [in] const D3D11_SUBRESOURCE_DATA *pInitialData,
        [out] ID3D11Texture2D **ppTexture2D);
    HRESULT CreateTexture3D(
        [in] const D3D11_TEXTURE3D_DESC *pDesc,
        [in] const D3D11_SUBRESOURCE_DATA *pInitialData,
        [out] ID3D11Texture3D **ppTexture3D);
    HRESULT CreateShaderResourceView(
        [in] ID3D11Resource *pResource,
        [in] const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,
        [out] ID3D11ShaderResourceView **ppSRView);
    HRESULT CreateUnorderedAccessView(
        [in] ID3D11Resource *pResource,
        [in] const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,
        [out] ID3D11UnorderedAccessView **ppUAView);
    HRESULT CreateRenderTargetView(
        [in] ID3D11Resource *pResource,
        [in] const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,
        [out] ID3D11RenderTargetView **ppRTView);
    HRESULT CreateDepthStencilView(
        [in] ID3D11Resource *pResource,
        [in] const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,
        [out] ID3D11DepthStencilView **ppDepthStencilView);
    HRESULT CreateInputLayout(
        [in] const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,
        [in] UINT NumElements,
        [in] const void *pShaderBytecodeWithInputSignature,
        [in] SIZE_T BytecodeLength,
        [out] ID3D11InputLayout **ppInputLayout);
    HRESULT CreateVertexShader(
        [in] const void *pShaderBytecode,
        [in] SIZE_T BytecodeLength,
        [in] ID3D11ClassLinkage *pClassLinkage,
        [out] ID3D11VertexShader **ppVertexShader);
    HRESULT CreateGeometryShader(
        [in] const void *pShaderBytecode,
        [in] SIZE_T BytecodeLength,
        [in] ID3D11ClassLinkage *pClassLinkage,
        [out] ID3D11GeometryShader **ppGeometryShader);
    HRESULT CreateGeometryShaderWithStreamOutput(
        [in] const void *pShaderBytecode,
        [in] SIZE_T BytecodeLength,
        [in] const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,
        [in] UINT NumEntries,
        [in] const UINT *pBufferStrides,
        [in] UINT NumStrides,
        [in] UINT RasterizedStream,
        [in] ID3D11ClassLinkage *pClassLinkage,
        [out] ID3D11GeometryShader **ppGeometryShader);
    HRESULT CreatePixelShader(
        [in] const void *pShaderBytecode,
        [in] SIZE_T BytecodeLength,
        [in] ID3D11ClassLinkage *pClassLinkage,
        [out] ID3D11PixelShader **ppPixelShader);
    HRESULT CreateHullShader(
        [in] const void *pShaderBytecode,
        [in] SIZE_T BytecodeLength,
        [in] ID3D11ClassLinkage *pClassLinkage,
        [out] ID3D11HullShader **ppHullShader);
    HRESULT CreateDomainShader(
        [in] const void *pShaderBytecode,
        [in] SIZE_T BytecodeLength,
        [in] ID3D11ClassLinkage *pClassLinkage,
        [out] ID3D11DomainShader **ppDomainShader);
    HRESULT CreateComputeShader(
        [in] const void *pShaderBytecode,
        [in] SIZE_T BytecodeLength,
        [in] ID3D11ClassLinkage *pClassLinkage,
        [out] ID3D11ComputeShader **ppComputeShader);
    HRESULT CreateClassLinkage(
        [out] ID3D11ClassLinkage **ppLinkage);
    HRESULT CreateBlendState(
        [in] const D3D11_BLEND_DESC *pBlendStateDesc,
        [out] ID3D11BlendState **ppBlendState);
    HRESULT CreateDepthStencilState(
        [in] const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,
        [out] ID3D11DepthStencilState **ppDepthStencilState);
    HRESULT CreateRasterizerState(
        [in] const D3D11_RASTERIZER_DESC *pRasterizerDesc,
        [out] ID3D11RasterizerState **ppRasterizerState);
    HRESULT CreateSamplerState(
        [in] const D3D11_SAMPLER_DESC *pSamplerDesc,
        [out] ID3D11SamplerState **ppSamplerState);
    HRESULT CreateQuery(
        [in] const D3D11_QUERY_DESC *pQueryDesc,
        [out] ID3D11Query **ppQuery);
    HRESULT CreatePredicate(
        [in] const D3D11_QUERY_DESC *pPredicateDesc,
        [out] ID3D11Predicate **ppPredicate);
    HRESULT CreateCounter(
        [in] const D3D11_COUNTER_DESC *pCounterDesc,
        [out] ID3D11Counter **ppCounter);
    HRESULT CreateDeferredContext(
        UINT ContextFlags,
        [out] ID3D11DeviceContext **ppDeferredContext);
    HRESULT OpenSharedResource(
        [in] HANDLE hResource,
        [in] REFIID ReturnedInterface,
        [out] void  **ppResource);
    HRESULT CheckFormatSupport(
        [in] DXGI_FORMAT Format,
        [out] UINT *pFormatSupport);
    HRESULT CheckMultisampleQualityLevels(
        [in] DXGI_FORMAT Format,
        [in] UINT        SampleCount,
        [out] UINT *pNumQualityLevels);
    void CheckCounterInfo(
        [out] D3D11_COUNTER_INFO *pCounterInfo);
    HRESULT CheckCounter(
        [in] const D3D11_COUNTER_DESC *pDesc,
        [out] D3D11_COUNTER_TYPE *pType,
        [out] UINT *pActiveCounters,
        [out] LPSTR szName,
        [in, out] UINT *pNameLength,
        [out] LPSTR szUnits,
        [in, out] UINT *pUnitsLength,
        [out] LPSTR szDescription,
        [in, out] UINT *pDescriptionLength);
    HRESULT CheckFeatureSupport(
        D3D11_FEATURE Feature,
        [out] void *pFeatureSupportData,
        UINT FeatureSupportDataSize);
    HRESULT GetPrivateData(
        [in] REFGUID guid,
        [in, out] UINT *pDataSize,
        [out] void *pData);
    HRESULT SetPrivateData(
        [in] REFGUID guid,
        [in] UINT DataSize,
        [in] const void *pData);
    HRESULT SetPrivateDataInterface(
        [in] REFGUID guid,
        [in] const IUnknown *pData);
    D3D_FEATURE_LEVEL GetFeatureLevel();
    UINT GetCreationFlags();
    HRESULT GetDeviceRemovedReason();
    void GetImmediateContext(
        [out] ID3D11DeviceContext **ppImmediateContext);
    HRESULT SetExceptionMode(UINT RaiseFlags);
    UINT GetExceptionMode();
}

typedef enum D3D11_CREATE_DEVICE_FLAG {
    D3D11_CREATE_DEVICE_SINGLETHREADED  = 0x0001,
    D3D11_CREATE_DEVICE_DEBUG           = 0x0002,
    D3D11_CREATE_DEVICE_SWITCH_TO_REF   = 0x0004,
    D3D11_CREATE_DEVICE_PREVENT_INTERNAL_THREADING_OPTIMIZATIONS = 0x0008,
    D3D11_CREATE_DEVICE_BGRA_SUPPORT    = 0x0020,
    D3D11_CREATE_DEVICE_DEBUGGABLE      = 0x0040,
    D3D11_CREATE_DEVICE_PREVENT_ALTERING_LAYER_SETTINGS_FROM_REGISTRY = 0x0080,
    D3D11_CREATE_DEVICE_DISABLE_GPU_TIMEOUT = 0x0100,
    D3D11_CREATE_DEVICE_VIDEO_SUPPORT   = 0x0800
} D3D11_CREATE_DEVICE_FLAG;

typedef enum D3D11_VIDEO_PROCESSOR_FORMAT_SUPPORT
{
    D3D11_VIDEO_PROCESSOR_FORMAT_SUPPORT_INPUT  = 0x0001,
    D3D11_VIDEO_PROCESSOR_FORMAT_SUPPORT_OUTPUT = 0x0002,
} D3D11_VIDEO_PROCESSOR_FORMAT_SUPPORT;

const UINT D3D11_SDK_VERSION = 7;

cpp_quote("#include <d3d10_1.h>")
cpp_quote("#ifndef D3D11_IGNORE_SDK_LAYERS")
cpp_quote("# include <d3d11sdklayers.h>")
cpp_quote("#endif")
cpp_quote("#include <d3d10misc.h>")
cpp_quote("#include <d3d10shader.h>")
cpp_quote("#include <d3d10effect.h>")
cpp_quote("#include <d3d10_1shader.h>")

const UINT _FACD3D11 = 0x87c;
cpp_quote("#define MAKE_D3D11_HRESULT(code) MAKE_HRESULT(SEVERITY_ERROR, _FACD3D11, code)")

cpp_quote("typedef HRESULT (WINAPI* PFN_D3D11_CREATE_DEVICE)(IDXGIAdapter*,D3D_DRIVER_TYPE,HMODULE,UINT,")
cpp_quote("    const D3D_FEATURE_LEVEL*,UINT,UINT,ID3D11Device**,D3D_FEATURE_LEVEL*,ID3D11DeviceContext**);")

cpp_quote("HRESULT WINAPI D3D11CreateDevice(IDXGIAdapter*,D3D_DRIVER_TYPE,HMODULE,UINT,const D3D_FEATURE_LEVEL*," )
cpp_quote("    UINT,UINT,ID3D11Device**,D3D_FEATURE_LEVEL*,ID3D11DeviceContext**);")

cpp_quote("typedef HRESULT (WINAPI *PFN_D3D11_CREATE_DEVICE_AND_SWAP_CHAIN)(IDXGIAdapter*,D3D_DRIVER_TYPE,HMODULE,UINT,")
cpp_quote("    const D3D_FEATURE_LEVEL*,UINT,UINT,const DXGI_SWAP_CHAIN_DESC*,IDXGISwapChain**,ID3D11Device**,")
cpp_quote("    D3D_FEATURE_LEVEL*,ID3D11DeviceContext**);")

[local] HRESULT __stdcall D3D11CreateDeviceAndSwapChain(IDXGIAdapter *adapter, D3D_DRIVER_TYPE driver_type,
        HMODULE swrast, UINT flags, const D3D_FEATURE_LEVEL *feature_levels, UINT levels, UINT sdk_version,
        const DXGI_SWAP_CHAIN_DESC *swapchain_desc, IDXGISwapChain **swapchain, ID3D11Device **device,
        D3D_FEATURE_LEVEL *obtained_feature_level, ID3D11DeviceContext **immediate_context);
