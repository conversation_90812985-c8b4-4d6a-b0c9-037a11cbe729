<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-cmds</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>asn1parse, ca, ciphers, cmp, cms, crl, crl2pkcs7, dgst, dhparam, dsa, dsaparam, ec, ecparam, enc, engine, errstr, gendsa, genpkey, genrsa, info, kdf, mac, nseq, ocsp, passwd, pkcs12, pkcs7, pkcs8, pkey, pkeyparam, pkeyutl, prime, rand, rehash, req, rsa, rsautl, s_client, s_server, s_time, sess_id, smime, speed, spkac, srp, storeutl, ts, verify, version, x509 - OpenSSL application commands</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <i>cmd</i> <b>-help</b> | [<i>-option</i> | <i>-option</i> <i>arg</i>] ... [<i>arg</i>] ...</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Every <i>cmd</i> listed above is a (sub-)command of the <a href="../man1/openssl.html">openssl(1)</a> application. It has its own detailed manual page at <b>openssl-<i>cmd</i></b>(1). For example, to view the manual page for the <b>openssl dgst</b> command, type <code>man openssl-dgst</code>.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<p>Among others, every subcommand has a help option.</p>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message for the subcommand.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man1/openssl-asn1parse.html">openssl-asn1parse(1)</a>, <a href="../man1/openssl-ca.html">openssl-ca(1)</a>, <a href="../man1/openssl-ciphers.html">openssl-ciphers(1)</a>, <a href="../man1/openssl-cmp.html">openssl-cmp(1)</a>, <a href="../man1/openssl-cms.html">openssl-cms(1)</a>, <a href="../man1/openssl-crl.html">openssl-crl(1)</a>, <a href="../man1/openssl-crl2pkcs7.html">openssl-crl2pkcs7(1)</a>, <a href="../man1/openssl-dgst.html">openssl-dgst(1)</a>, <a href="../man1/openssl-dhparam.html">openssl-dhparam(1)</a>, <a href="../man1/openssl-dsa.html">openssl-dsa(1)</a>, <a href="../man1/openssl-dsaparam.html">openssl-dsaparam(1)</a>, <a href="../man1/openssl-ec.html">openssl-ec(1)</a>, <a href="../man1/openssl-ecparam.html">openssl-ecparam(1)</a>, <a href="../man1/openssl-enc.html">openssl-enc(1)</a>, <a href="../man1/openssl-engine.html">openssl-engine(1)</a>, <a href="../man1/openssl-errstr.html">openssl-errstr(1)</a>, <a href="../man1/openssl-gendsa.html">openssl-gendsa(1)</a>, <a href="../man1/openssl-genpkey.html">openssl-genpkey(1)</a>, <a href="../man1/openssl-genrsa.html">openssl-genrsa(1)</a>, <a href="../man1/openssl-info.html">openssl-info(1)</a>, <a href="../man1/openssl-kdf.html">openssl-kdf(1)</a>, <a href="../man1/openssl-mac.html">openssl-mac(1)</a>, <a href="../man1/openssl-nseq.html">openssl-nseq(1)</a>, <a href="../man1/openssl-ocsp.html">openssl-ocsp(1)</a>, <a href="../man1/openssl-passwd.html">openssl-passwd(1)</a>, <a href="../man1/openssl-pkcs12.html">openssl-pkcs12(1)</a>, <a href="../man1/openssl-pkcs7.html">openssl-pkcs7(1)</a>, <a href="../man1/openssl-pkcs8.html">openssl-pkcs8(1)</a>, <a href="../man1/openssl-pkey.html">openssl-pkey(1)</a>, <a href="../man1/openssl-pkeyparam.html">openssl-pkeyparam(1)</a>, <a href="../man1/openssl-pkeyutl.html">openssl-pkeyutl(1)</a>, <a href="../man1/openssl-prime.html">openssl-prime(1)</a>, <a href="../man1/openssl-rand.html">openssl-rand(1)</a>, <a href="../man1/openssl-rehash.html">openssl-rehash(1)</a>, <a href="../man1/openssl-req.html">openssl-req(1)</a>, <a href="../man1/openssl-rsa.html">openssl-rsa(1)</a>, <a href="../man1/openssl-rsautl.html">openssl-rsautl(1)</a>, <a href="../man1/openssl-s_client.html">openssl-s_client(1)</a>, <a href="../man1/openssl-s_server.html">openssl-s_server(1)</a>, <a href="../man1/openssl-s_time.html">openssl-s_time(1)</a>, <a href="../man1/openssl-sess_id.html">openssl-sess_id(1)</a>, <a href="../man1/openssl-smime.html">openssl-smime(1)</a>, <a href="../man1/openssl-speed.html">openssl-speed(1)</a>, <a href="../man1/openssl-spkac.html">openssl-spkac(1)</a>, <a href="../man1/openssl-srp.html">openssl-srp(1)</a>, <a href="../man1/openssl-storeutl.html">openssl-storeutl(1)</a>, <a href="../man1/openssl-ts.html">openssl-ts(1)</a>, <a href="../man1/openssl-verify.html">openssl-verify(1)</a>, <a href="../man1/openssl-version.html">openssl-version(1)</a>, <a href="../man1/openssl-x509.html">openssl-x509(1)</a>,</p>

<h1 id="HISTORY">HISTORY</h1>

<p>Initially, the manual page entry for the <code>openssl <i>cmd</i></code> command used to be available at <i>cmd</i>(1). Later, the alias <b>openssl-<i>cmd</i></b>(1) was introduced, which made it easier to group the openssl commands using the <a href="../man1/apropos.html">apropos(1)</a> command or the shell&#39;s tab completion.</p>

<p>In order to reduce cluttering of the global manual page namespace, the manual page entries without the &#39;openssl-&#39; prefix have been deprecated in OpenSSL 3.0 and will be removed in OpenSSL 4.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


