# help.ja.txt - Japanese GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.

.#pinentry.qualitybar.tooltip
# [ このエントリは有効にするには、上記のキーの # を削除してください。]
# これは例です。
このバーは、入力されたパスフレーズの品質を示しています。

バーが赤い色となっている場合、GnuPGはパスフレーズが弱すぎると判断し、受
け付けません。管理者にパスフレーズの制限の設定について詳細を問い合わせ
てください。
.

.gnupg.agent-problem
# There was a problem accessing or starting the agent.
動作中のGpg-Agentへの接続ができなかったか、通信の問題が発生しました。

システムは、Gpg-Agentと呼ばれるバックグラウンド・プロセスを利用し、秘密
鍵とパスフレーズの問い合わせを処理します。このエージェントは通常、ユー
ザがログインするときに開始され、ログインしている間、動いています。もし、
エージェントが利用可能でない場合、システムは、その場でエージェントの起
動を試しますが、この場合、機能がやや制限され、若干の問題がある場合があ
ります。

もしかしたら、管理者に問い合わせて、この問題をどのように解決したら良い
か聞いた方が良いかもしれません。とりあえずの方策としては、一度ログアウ
トしてもう一度ログインし、改善が見られるか試してみることがあります。も
し、これがうまくいくようであれば管理者に報告してください。それはおそら
く、ソフトウェアのバグであることを示していますので。
.


.gnupg.dirmngr-problem
# There was a problen accessing the dirmngr.
動作中のDirmngrへの接続ができなかったか、通信の問題が発生しました。

証明書失効リスト(CRL)を検索し、OCSPの検証とLDAPサーバを通じて鍵を検索す
るため、システムは、Dirmngrと呼ばれる外部サービス・プログラムを利用しま
す。Dirmngrは通常、システムサービス(daemon)として実効されます、一般ユー
ザは気にする必要はありません。問題がある場合、システムは、要求に応じて、
Dirmngrを起動することがありますが、これは対応策であり、性能に制限が生じ
ます。

この問題がある場合、システム管理者に連絡し、どのように進めたら良いか問
い合わせてください。とりあえずの解決策としては、gpgsmの設定でCRLの検証
を停止させることが考えられます。
.


.gpg.edit_ownertrust.value
ここでの値の指定は、あなたに任されています。この値は、第三者に開示され
ることは決してありません。ウェブ・オブ・トラストを実装するためにこの値
が必要となりますが、(暗黙的に作られる)証明書の網には何も関係しません。
.

.gpg.edit_ownertrust.set_ultimate.okay
ウェブ・オブ・トラストを構築するためにGnuPGは、どの鍵が究極的に信頼でき
るかを知る必要があります。その鍵は通常は、あなたが秘密鍵へアクセスでき
るものです。この鍵が究極的に信頼できる場合、"yes" と答えてください。
.


.gpg.untrusted_key.override
この信頼されてない鍵をどちらにせよ使いたい場合、"yes" と答えてください。
.

.gpg.pklist.user_id.enter
このメッセージを送りたい宛先のユーザIDを入力してください。
.

.gpg.keygen.algo
使用するアルゴリズムを選択してください。

DSA (別名 DSS)は電子署名アルゴリズムであり、署名にのみ使えます。

Elgamal は暗号化のみのアルゴリズムです。

RSA は署名と暗号化のどちらにも使えます。

主鍵は常に、署名が可能の鍵である必要があります。
.


.gpg.keygen.algo.rsa_se
一般的に、署名と暗号化に同一の鍵を用いることは良いことではありません。
このアルゴリズムはある特定の領域だけに使うべきです。まず、セキュリティ
の専門家に相談してください。
.

.gpg.keygen.cardkey
カードからどの鍵を使用するか選択する。

リストには、選択の番号、keygrip (16進数の文字列)、カード固有の鍵参照、
この鍵に使うアルゴリズム、そして、鍵の使用目的(cert, sign, auth, encr)
が括弧内に示されます。鍵の標準的な使用がわかっている場合には、アスタリ
スクでマークされます。.

.gpg.keygen.flags
鍵の機能をトグルする。

選択されたアルゴリズムで可能な機能だけがトグルできます。

すばやく一度ですべての機能を設定するには、'=' の文字を最初の文字として、
続けて設定する機能の列を入力します: 's' は署名、'e' は暗号化、'a'は認
証です。無効な文字と不可能な機能は無視されます。このサブメニューはこの
ショートカットを利用したのちにただちに閉じられます。
.


.gpg.keygen.size
鍵の長さを入力してください。

提案されたデフォルトが通常良い選択です。

大きな鍵長を使いたい場合、たとえば4096ビットなど、本当に意味があるか再
検討してください。こちらのウェブページを見るのも良いと思います:
http://www.xkcd.com/538/
.

.gpg.keygen.size.huge.okay
"yes" か "no" で答えてください。
.


.gpg.keygen.size.large.okay
"yes" か "no" で答えてください。
.


.gpg.keygen.valid
プロンプトで示された必要な値を入力してください。
ISO形式の日付(YYYY-MM-DD)の入力が可能ですが、わかりやすいエラーの反応
が得られないままにシステムが与えられた値を期間と解釈して扱うことがあり
ます。

.gpg.keygen.valid.okay
"yes" か "no" で答えてください。
.


.gpg.keygen.name
鍵の持ち主の名前を入力してください。
文字 "<" と ">" は許されていません。
例: Heinrich Heine
.


.gpg.keygen.email
オプションですが推奨される電子メールアドレスを入力してください。
例: <EMAIL>
.

.gpg.keygen.comment
オプションのコメントを入力してください。
文字 "(" と ")" は許されていません。
一般的にコメントは必要ではありません。
.


.gpg.keygen.userid.cmd
# (Keep a leading empty line)

N  名前の変更。
C  コメントの変更。
E  電子メールアドレスの変更。
O  鍵生成に進む。
Q  鍵生成を止める。
.

.gpg.keygen.sub.okay
副鍵を生成してよければ、"yes" (あるいは単に "y") と答えてください。
.

.gpg.sign_uid.okay
"yes" か "no" で答えてください。
.

.gpg.sign_uid.class
ある鍵のユーザIDに署名するとき、まず、その鍵がそのユーザIDの人に属する
かどうかをあなたは確認しなければなりません。あなたがどれくらいこれを慎
重に確認したかについて、ほかの人が知ることは有用です。

"0" は、どれくらい慎重に確認したかについて特になにも主張しないことを意味します。

"1" は、主張するその人が所有する鍵であるとあなたは信じるが、その鍵について、
    検証できなかった、あるいはしなかったことを意味します。これは、ペンネームの
    ユーザの鍵に署名するような "persona" 確認に有用です。

"2" は、その鍵に対し、通常の検証を行ったことを意味します。たとえば、鍵
    のフィンガープリントを確認し、写真付きIDでユーザIDを確認したことを
    意味します。

"3" は、その鍵に対し、広範な検証を行ったことを意味します。たとえば、鍵
    のフィンガープリントを対面で確認し、パスポートなど偽造することが難
    しい写真付きIDでユーザIDを確認し、所有者の名前が鍵のユーザIDに適合
    し、メールの交換で、メールアドレスが所有者に属することを確認したこ
    とを意味します。

上記のレベル2とレベル3で示した例は、単に例であることに注意してください。
結局は、ほかの鍵に署名するとき、なにがあなたにとって「通常」で、なにが
「広範」かをを決めるのは、あなた自身に任されています。

正しい答えがなにかわからないときは "0" と答えてください。
.

.gpg.change_passwd.empty.okay
"yes" か "no" で答えてください。
.


.gpg.keyedit.save.okay
"yes" か "no" で答えてください。
.


.gpg.keyedit.cancel.okay
"yes" か "no" で答えてください。
.

.gpg.keyedit.sign_all.okay
すべてのユーザIDに対して署名したい場合、"yes"と答えてください。
.

.gpg.keyedit.remove.uid.okay
このユーザIDを本当に削除したい場合、"yes"と答えてください。
そうすると全部の証明書が失われます!
.

.gpg.keyedit.remove.subkey.okay
副鍵を削除してよい場合、"yes"と答えてください。
.


.gpg.keyedit.delsig.valid
これは、この鍵の有効な署名です。通常、この署名を削除することは望まない
でしょう。この鍵(または、この鍵で証明された別の鍵)への信頼のコネクショ
ンが成立することが重要となる場合があるからです。
.

.gpg.keyedit.delsig.unknown
この署名は検証できませんでした。対応する鍵を持っていないからです。どの
鍵が使われたかわかるまでこの削除を延期すべきです。この署名の鍵は、別の
すでに証明された鍵を通じて信頼のコネクションを成立することがあるからで
す。
.

.gpg.keyedit.delsig.invalid
この署名は有効ではありません。鍵リングから削除することに意味があります。
.

.gpg.keyedit.delsig.selfsig
これはこのユーザIDとこの鍵とを結ぶ署名です。通常、このような署名を削除
することは良いことではありません。実際、GnuPGはこの鍵を使うことができな
くなってしまうかもしれません。ですから、この自己署名がなんらかの理由に
よって無効であり、第二のものが利用可能である場合にだけ、実行してくださ
い。
.

.gpg.keyedit.updpref.okay
すべてのユーザID(もしくは単に選択された一つ)の優先指定を現行の優先指定
に変更します。すべての関係する自己署名のタイムスタンプは、一秒進んだも
のとなります。
.

.gpg.passphrase.enter
# (keep a leading empty line)

パスフレーズを入力してください。秘密の文です。
.


.gpg.passphrase.repeat
もう一度パスフレーズを入力し、間違いなく入力されたことを確認してください。
.

.gpg.detached_signature.filename
署名が適用されるファイルの名前を与えてください。
.

.gpg.openfile.overwrite.okay
# openfile.c (overwrite_filep)
ファイルを上書きしてよければ、"yes"と答えてください。
.

.gpg.openfile.askoutname
# openfile.c (ask_outfile_name)
新しいファイル名を入力してください。単にEnterを打つと、カッコで示された
デフォルトのファイルが使われます。
.

.gpg.ask_revocation_reason.code
# revoke.c (ask_revocation_reason)
証明書の理由を指定します。下記のリストから選択してください:
  "鍵が危うくなった"
      承認していない人があなたの秘密鍵へのアクセスを得たと考える理由が
      ある場合に、これを指定します。
  "鍵を取り替えた"
      新しい鍵でこの鍵を置き換えた場合に、これを指定します。
  "鍵はもう使われない"
      この鍵を使わなくなった場合に、これを指定します。
  "ユーザIDが無効となった"
      ユーザIDをもはや使うべきでない場合に、これを指定します。通常、こ
      れは、電子メールアドレスが無効となった場合です。
.


.gpg.ask_revocation_reason.text
# revoke.c (ask_revocation_reason)
必要であれば、この失効証明書を発行する理由を記述する文章を入力する
ことができます。この文章は簡潔にしてください。空行は文章の終わりを
意味します。
.

.gpg.tofu.conflict
# tofu.c

TOFUが同一(もしくはとてもよく似ている)メールアドレスの別の鍵を検出しま
した。そのユーザが新しい鍵を作ったかもしれません。この場合には、あなた
は新しい鍵を安全に信じることができます(が、その人に聞いて確かめましょ
う)。しかし、また、その鍵が偽造であるか、中間者(MitM)攻撃が行われてい
るのかもしれません。この場合には、あなたはこの鍵を信じられないものとな
るように不正としてマークすべきです。ある鍵を信じられないものとなるよう
にマークするとは、どんな署名も不正と考えられるようになる、その鍵への暗
号化はフラグが立てられることを意味します。わからなくて今は確認できない
場合、一度だけ認める、あるいは、一度だけ拒絶するを選択すべきです。
.


.gpgsm.root-cert-not-trusted
# This text gets displayed by the audit log if
# a root certificates was not trusted.
ルート証明書(信頼の拠り所)が信頼できるとされていません。設定にもよりま
すが、そのルート証明書を信頼できるものと指定するように既に問われたかも
しれませんし、手動でGnuPGがその証明書を信頼できると扱うように設定する必
要があります。信頼できる証明書は、GnuPGのホームディレクトリのファイル
trustlist.txt に設定します。疑問のある場合、システム管理者にこの証明書
を信頼してよいものかどうか問い合わせてください。
.


.gpgsm.crl-problem
# This tex is displayed by the audit log for problems with
# the CRL or OCSP checking.
設定によりますが、CRLの取得か、OCSP検証の際に問題が起きました。これが動
かない場合、実に様々な理由がありえます。解決策は、マニュアルを見てくだ
さい。
.


# Local variables:
# mode: fundamental
# coding: utf-8
# End:
