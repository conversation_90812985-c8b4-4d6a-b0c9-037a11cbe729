/* 增强RNN模型推理代码 - 简化版本 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "enhanced_rnn_weights.h"

/* 简化的矩阵乘法 */
void matrix_multiply(const float* input, const float* weights, float* output, 
                    int input_size, int output_size) {
    for (int i = 0; i < output_size; i++) {
        output[i] = 0.0f;
        for (int j = 0; j < input_size; j++) {
            output[i] += input[j] * weights[i * input_size + j];
        }
    }
}

/* 向量加法 */
void vector_add(float* vec, const float* bias, int size) {
    for (int i = 0; i < size; i++) {
        vec[i] += bias[i];
    }
}

/* Tanh激活函数 */
void tanh_activation(float* vec, int size) {
    for (int i = 0; i < size; i++) {
        vec[i] = tanhf(vec[i]);
    }
}

/* Sigmoid激活函数 */
void sigmoid_activation(float* vec, int size) {
    for (int i = 0; i < size; i++) {
        vec[i] = 1.0f / (1.0f + expf(-vec[i]));
    }
}

/* 简化的GRU前向传播 */
void simple_gru_forward(const float* input, const float* weights, const float* recurrent_weights,
                       const float* bias, float* state, float* output, 
                       int input_size, int hidden_size) {
    // 这是一个极简化的GRU实现，仅用于演示
    // 实际应用中需要完整的GRU计算
    
    float temp[hidden_size];
    
    // 输入变换
    matrix_multiply(input, weights, temp, input_size, hidden_size);
    vector_add(temp, bias, hidden_size);
    tanh_activation(temp, hidden_size);
    
    // 更新状态 (简化)
    for (int i = 0; i < hidden_size; i++) {
        state[i] = 0.9f * state[i] + 0.1f * temp[i];
        output[i] = state[i];
    }
}

/* 增强RNN推理函数 */
void enhanced_rnn_inference(const float* input_features, 
                           float* noise_output, float* voice_output, float* vad_output) {
    
    // 状态变量
    static float gru_state[64] = {0};
    
    // 中间变量
    float dense_output[48];
    float gru_output[64];
    
    // 第1层: Dense层
    matrix_multiply(input_features, feature_dense_weights, dense_output, 
                   FEATURE_DENSE_WEIGHTS_ROWS, FEATURE_DENSE_WEIGHTS_COLS);
    vector_add(dense_output, feature_dense_bias, FEATURE_DENSE_BIAS_SIZE);
    tanh_activation(dense_output, FEATURE_DENSE_BIAS_SIZE);
    
    // 第2层: GRU层 (简化)
    simple_gru_forward(dense_output, main_gru_weights, main_gru_recurrent_weights,
                      main_gru_bias, gru_state, gru_output,
                      MAIN_GRU_WEIGHTS_ROWS, 64);
    
    // 输出层1: 噪声抑制
    matrix_multiply(gru_output, noise_output_weights, noise_output,
                   NOISE_OUTPUT_WEIGHTS_ROWS, NOISE_OUTPUT_WEIGHTS_COLS);
    vector_add(noise_output, noise_output_bias, NOISE_OUTPUT_BIAS_SIZE);
    sigmoid_activation(noise_output, ENHANCED_MODEL_NOISE_OUTPUT_SIZE);
    
    // 输出层2: 人声增强
    matrix_multiply(gru_output, voice_output_weights, voice_output,
                   VOICE_OUTPUT_WEIGHTS_ROWS, VOICE_OUTPUT_WEIGHTS_COLS);
    vector_add(voice_output, voice_output_bias, VOICE_OUTPUT_BIAS_SIZE);
    sigmoid_activation(voice_output, ENHANCED_MODEL_VOICE_OUTPUT_SIZE);
    
    // 输出层3: VAD
    matrix_multiply(gru_output, vad_output_weights, vad_output,
                   VAD_OUTPUT_WEIGHTS_ROWS, VAD_OUTPUT_WEIGHTS_COLS);
    vector_add(vad_output, vad_output_bias, VAD_OUTPUT_BIAS_SIZE);
    sigmoid_activation(vad_output, ENHANCED_MODEL_VAD_OUTPUT_SIZE);
}

/* 测试主函数 */
int main() {
    printf("增强RNN模型测试程序\n");
    printf("输入维度: %d\n", ENHANCED_MODEL_INPUT_SIZE);
    printf("噪声抑制输出: %d\n", ENHANCED_MODEL_NOISE_OUTPUT_SIZE);
    printf("人声增强输出: %d\n", ENHANCED_MODEL_VOICE_OUTPUT_SIZE);
    printf("VAD输出: %d\n", ENHANCED_MODEL_VAD_OUTPUT_SIZE);
    
    // 创建测试输入
    float test_input[ENHANCED_MODEL_INPUT_SIZE];
    for (int i = 0; i < ENHANCED_MODEL_INPUT_SIZE; i++) {
        test_input[i] = 0.1f * sinf(i * 0.1f);  // 简单的测试信号
    }
    
    // 输出缓冲区
    float noise_gains[ENHANCED_MODEL_NOISE_OUTPUT_SIZE];
    float voice_gains[ENHANCED_MODEL_VOICE_OUTPUT_SIZE];
    float vad_prob[ENHANCED_MODEL_VAD_OUTPUT_SIZE];
    
    // 运行推理
    printf("\n运行推理...\n");
    enhanced_rnn_inference(test_input, noise_gains, voice_gains, vad_prob);
    
    // 显示结果
    printf("\n推理结果:\n");
    printf("噪声抑制增益: ");
    for (int i = 0; i < ENHANCED_MODEL_NOISE_OUTPUT_SIZE; i++) {
        printf("%.3f ", noise_gains[i]);
    }
    printf("\n");
    
    printf("人声增强增益: ");
    for (int i = 0; i < ENHANCED_MODEL_VOICE_OUTPUT_SIZE; i++) {
        printf("%.3f ", voice_gains[i]);
    }
    printf("\n");
    
    printf("VAD概率: %.3f\n", vad_prob[0]);
    
    printf("\n模型推理测试完成!\n");
    
    return 0;
}
