.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_state_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_state_init \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "int dane_state_init(dane_state_t * " s ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "dane_state_t * s" 12
The structure to be initialized
.IP "unsigned int flags" 12
flags from the \fBdane_state_flags\fP enumeration
.SH "DESCRIPTION"
This function will initialize the backend resolver. It is
intended to be used in scenarios where multiple resolvings
occur, to optimize against multiple re\-initializations.
.SH "RETURNS"
On success, \fBDANE_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
