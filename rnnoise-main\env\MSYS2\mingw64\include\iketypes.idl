cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file DISCLAIMER within this package.")
cpp_quote(" */")
cpp_quote("")
cpp_quote("#include <winapifamily.h>")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("")

#include "winerror.h"
import "fwptypes.idl";


cpp_quote("#define IKEEXT_ERROR_CODE_COUNT (ERROR_IPSEC_IKE_NEG_STATUS_END - ERROR_IPSEC_IKE_NEG_STATUS_BEGIN)")
cpp_quote("")
cpp_quote("/* Please keep in sync with winerror.h defines.  */")
#define IKEEXT_ERROR_CODE_COUNT 97

cpp_quote("")

cpp_quote("#define IKEEXT_CERT_FLAG_ENABLE_ACCOUNT_MAPPING          (0x1)")
cpp_quote("#define IKEEXT_CERT_FLAG_DISABLE_REQUEST_PAYLOAD         (0x2)")
cpp_quote("#define IKEEXT_CERT_FLAG_USE_NAP_CERTIFICATE             (0x4)")
cpp_quote("#define IKEEXT_CERT_FLAG_INTERMEDIATE_CA                 (0x8)")
cpp_quote("#define IKEEXT_CERT_FLAG_IGNORE_INIT_CERT_MAP_FAILURE    (0x10)")
cpp_quote("#define IKEEXT_CERT_FLAG_PREFER_NAP_CERTIFICATE_OUTBOUND (0x20)")
cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
cpp_quote("#define IKEEXT_CERT_FLAG_SELECT_NAP_CERTIFICATE          (0x40)")
cpp_quote("#define IKEEXT_CERT_FLAG_VERIFY_NAP_CERTIFICATE          (0x80)")
cpp_quote("#define IKEEXT_CERT_FLAG_FOLLOW_RENEWAL_CERTIFICATE      (0x100)")
cpp_quote("#endif")
cpp_quote("")

cpp_quote("#define IKEEXT_CERT_AUTH_FLAG_SSL_ONE_WAY            (0x1)")
cpp_quote("#define IKEEXT_CERT_AUTH_FLAG_DISABLE_CRL_CHECK      (0x2)")
cpp_quote("#define IKEEXT_CERT_AUTH_ENABLE_CRL_CHECK_STRONG     (0x4)")
cpp_quote("#define IKEEXT_CERT_AUTH_DISABLE_SSL_CERT_VALIDATION (0x8)")
cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
cpp_quote("#define IKEEXT_CERT_AUTH_ALLOW_HTTP_CERT_LOOKUP      (0x10)")
cpp_quote("#define IKEEXT_CERT_AUTH_URL_CONTAINS_BUNDLE         (0x20)")
cpp_quote("#endif")
cpp_quote("")

cpp_quote("#define IKEEXT_KERB_AUTH_DISABLE_INITIATOR_TOKEN_GENERATION (0x1)")
cpp_quote("#define IKEEXT_KERB_AUTH_DONT_ACCEPT_EXPLICIT_CREDENTIALS   (0x2)")
cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
cpp_quote("#define IKEEXT_KERB_AUTH_FORCE_PROXY_ON_INITIATOR           (0x4)")
cpp_quote("#endif")
cpp_quote("")

cpp_quote("#define IKEEXT_RESERVED_AUTH_DISABLE_INITIATOR_TOKEN_GENERATION (0x1)")
cpp_quote("")

cpp_quote("#define IKEEXT_NTLM_V2_AUTH_DONT_ACCEPT_EXPLICIT_CREDENTIALS (0x1)")
cpp_quote("")

cpp_quote("#define IKEEXT_POLICY_FLAG_DISABLE_DIAGNOSTICS (0x00000001)")
cpp_quote("#define IKEEXT_POLICY_FLAG_NO_MACHINE_LUID_VERIFY (0x00000002)")
cpp_quote("#define IKEEXT_POLICY_FLAG_NO_IMPERSONATION_LUID_VERIFY (0x00000004)")
cpp_quote("#define IKEEXT_POLICY_FLAG_ENABLE_OPTIONAL_DH (0x00000008)")

cpp_quote("")

cpp_quote("#define IKEEXT_CERT_CREDENTIAL_FLAG_NAP_CERT (0x1)")
cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
cpp_quote("#define IKEEXT_PSK_FLAG_LOCAL_AUTH_ONLY  (0x1)")
cpp_quote("#define IKEEXT_PSK_FLAG_REMOTE_AUTH_ONLY (0x2)")
cpp_quote("")
cpp_quote("#define IKEEXT_EAP_FLAG_LOCAL_AUTH_ONLY   (0x1)")
cpp_quote("#define IKEEXT_EAP_FLAG_REMOTE_AUTH_ONLY  (0x2)")
cpp_quote("#endif")
cpp_quote("")

cpp_quote("#ifndef __IPSEC_V4_UDP_ENCAPSULATION0_FWD_DECLARED")
cpp_quote("#define __IPSEC_V4_UDP_ENCAPSULATION0_FWD_DECLARED")
typedef struct IPSEC_V4_UDP_ENCAPSULATION0_ IPSEC_V4_UDP_ENCAPSULATION0;
cpp_quote("#endif")
cpp_quote("")

typedef UINT64 IKEEXT_COOKIE;

cpp_quote("")
typedef [v1_enum] enum IKEEXT_EM_SA_STATE_ {
  IKEEXT_EM_SA_STATE_NONE,
  IKEEXT_EM_SA_STATE_SENT_ATTS,
  IKEEXT_EM_SA_STATE_SSPI_SENT,
  IKEEXT_EM_SA_STATE_AUTH_COMPLETE,
  IKEEXT_EM_SA_STATE_FINAL,
  IKEEXT_EM_SA_STATE_COMPLETE,
  IKEEXT_EM_SA_STATE_MAX
} IKEEXT_EM_SA_STATE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_SA_ROLE_ {
  IKEEXT_SA_ROLE_INITIATOR,
  IKEEXT_SA_ROLE_RESPONDER,
  IKEEXT_SA_ROLE_MAX
} IKEEXT_SA_ROLE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_AUTHENTICATION_METHOD_TYPE_ {
  IKEEXT_PRESHARED_KEY,
  IKEEXT_CERTIFICATE,
  IKEEXT_KERBEROS,
  IKEEXT_ANONYMOUS,
  IKEEXT_SSL,
  IKEEXT_NTLM_V2,
  IKEEXT_IPV6_CGA,
  IKEEXT_CERTIFICATE_ECDSA_P256,
  IKEEXT_CERTIFICATE_ECDSA_P384,
  IKEEXT_SSL_ECDSA_P256,
  IKEEXT_SSL_ECDSA_P384,
  IKEEXT_EAP,
  IKEEXT_RESERVED,
  IKEEXT_AUTHENTICATION_METHOD_TYPE_MAX
} IKEEXT_AUTHENTICATION_METHOD_TYPE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_KEY_MODULE_TYPE_ {
  IKEEXT_KEY_MODULE_IKE,
  IKEEXT_KEY_MODULE_AUTHIP,
  IKEEXT_KEY_MODULE_IKEV2,
  IKEEXT_KEY_MODULE_MAX
} IKEEXT_KEY_MODULE_TYPE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_MM_SA_STATE_ {
  IKEEXT_MM_SA_STATE_NONE,
  IKEEXT_MM_SA_STATE_SA_SENT,
  IKEEXT_MM_SA_STATE_SSPI_SENT,
  IKEEXT_MM_SA_STATE_FINAL,
  IKEEXT_MM_SA_STATE_FINAL_SENT,
  IKEEXT_MM_SA_STATE_COMPLETE,
  IKEEXT_MM_SA_STATE_MAX
} IKEEXT_MM_SA_STATE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_QM_SA_STATE_ {
  IKEEXT_QM_SA_STATE_NONE,
  IKEEXT_QM_SA_STATE_INITIAL,
  IKEEXT_QM_SA_STATE_FINAL,
  IKEEXT_QM_SA_STATE_COMPLETE,
  IKEEXT_QM_SA_STATE_MAX
} IKEEXT_QM_SA_STATE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE_ {
  IKEEXT_IMPERSONATION_NONE,
  IKEEXT_IMPERSONATION_SOCKET_PRINCIPAL,
  IKEEXT_IMPERSONATION_MAX
} IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_CERT_CONFIG_TYPE_ {
  IKEEXT_CERT_CONFIG_EXPLICIT_TRUST_LIST,
  IKEEXT_CERT_CONFIG_ENTERPRISE_STORE,
  IKEEXT_CERT_CONFIG_TRUSTED_ROOT_STORE,
  IKEEXT_CERT_CONFIG_UNSPECIFIED,
  IKEEXT_CERT_CONFIG_TYPE_MAX
} IKEEXT_CERT_CONFIG_TYPE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_CIPHER_TYPE_ {
  IKEEXT_CIPHER_DES,
  IKEEXT_CIPHER_3DES,
  IKEEXT_CIPHER_AES_128,
  IKEEXT_CIPHER_AES_192,
  IKEEXT_CIPHER_AES_256,
  IKEEXT_CIPHER_TYPE_MAX
} IKEEXT_CIPHER_TYPE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_INTEGRITY_TYPE_ {
  IKEEXT_INTEGRITY_MD5,
  IKEEXT_INTEGRITY_SHA1,
  IKEEXT_INTEGRITY_SHA_256,
  IKEEXT_INTEGRITY_SHA_384,
  IKEEXT_INTEGRITY_TYPE_MAX
} IKEEXT_INTEGRITY_TYPE;

cpp_quote("")

typedef [v1_enum] enum IKEEXT_DH_GROUP_ {
  IKEEXT_DH_GROUP_NONE,
  IKEEXT_DH_GROUP_1,
  IKEEXT_DH_GROUP_2,
  IKEEXT_DH_GROUP_14,
  IKEEXT_DH_GROUP_2048 = IKEEXT_DH_GROUP_14,
  IKEEXT_DH_ECP_256,
  IKEEXT_DH_ECP_384,
  IKEEXT_DH_GROUP_24,
  IKEEXT_DH_GROUP_MAX
} IKEEXT_DH_GROUP;

cpp_quote("")

typedef struct IKEEXT_CERT_ROOT_CONFIG0_ {
  FWP_BYTE_BLOB certData;
  UINT32 flags;
} IKEEXT_CERT_ROOT_CONFIG0;

cpp_quote("")

typedef struct IKEEXT_KERBEROS_AUTHENTICATION0__ {
  UINT32 flags;
} IKEEXT_KERBEROS_AUTHENTICATION0;

cpp_quote("")

typedef struct IKEEXT_NTLM_V2_AUTHENTICATION0__ {
  UINT32 flags;
} IKEEXT_NTLM_V2_AUTHENTICATION0;

cpp_quote("")

typedef struct IKEEXT_PRESHARED_KEY_AUTHENTICATION0__ {
  FWP_BYTE_BLOB presharedKey;
} IKEEXT_PRESHARED_KEY_AUTHENTICATION0;

cpp_quote("")

typedef struct IKEEXT_PRESHARED_KEY_AUTHENTICATION1__ {
  FWP_BYTE_BLOB presharedKey;
  UINT32 flags;
} IKEEXT_PRESHARED_KEY_AUTHENTICATION1;
cpp_quote("")

typedef struct IKEEXT_CERTIFICATE_AUTHENTICATION0_ {
  IKEEXT_CERT_CONFIG_TYPE inboundConfigType;
  [switch_type(IKEEXT_CERT_CONFIG_TYPE), switch_is(inboundConfigType)]
  union {
    [case(IKEEXT_CERT_CONFIG_EXPLICIT_TRUST_LIST)]
    struct {
      UINT32 inboundRootArraySize;
      [size_is(inboundRootArraySize), unique]
      IKEEXT_CERT_ROOT_CONFIG0 *inboundRootArray;
    };
    [case(IKEEXT_CERT_CONFIG_ENTERPRISE_STORE)]
    [unique] IKEEXT_CERT_ROOT_CONFIG0 *inboundEnterpriseStoreConfig;
    [case(IKEEXT_CERT_CONFIG_TRUSTED_ROOT_STORE)]
    [unique] IKEEXT_CERT_ROOT_CONFIG0 *inboundTrustedRootStoreConfig;
  };
  IKEEXT_CERT_CONFIG_TYPE outboundConfigType;
  [switch_type(IKEEXT_CERT_CONFIG_TYPE), switch_is(outboundConfigType)]
  union {
    [case(IKEEXT_CERT_CONFIG_EXPLICIT_TRUST_LIST)]
    struct {
      UINT32 outboundRootArraySize;
      [size_is(outboundRootArraySize), unique]
      IKEEXT_CERT_ROOT_CONFIG0 *outboundRootArray;
    };
    [case(IKEEXT_CERT_CONFIG_ENTERPRISE_STORE)]
    [unique] IKEEXT_CERT_ROOT_CONFIG0 *outboundEnterpriseStoreConfig;
    [case(IKEEXT_CERT_CONFIG_TRUSTED_ROOT_STORE)]
    [unique] IKEEXT_CERT_ROOT_CONFIG0 *outboundTrustedRootStoreConfig;
  };
  UINT32 flags;
} IKEEXT_CERTIFICATE_AUTHENTICATION0;
cpp_quote("")

cpp_quote("#if NTDDI_VERSION >= NTDDI_WIN7")
typedef struct IKEEXT_CERTIFICATE_AUTHENTICATION1_ {
  IKEEXT_CERT_CONFIG_TYPE inboundConfigType;
  [switch_type(IKEEXT_CERT_CONFIG_TYPE), switch_is(inboundConfigType)]
  union {
      [case(IKEEXT_CERT_CONFIG_EXPLICIT_TRUST_LIST)]
      struct {
      UINT32 inboundRootArraySize;
      [size_is(inboundRootArraySize), unique]
      IKEEXT_CERT_ROOT_CONFIG0 *inboundRootArray;
    };
    [case(IKEEXT_CERT_CONFIG_ENTERPRISE_STORE)]
    [unique] IKEEXT_CERT_ROOT_CONFIG0 *inboundEnterpriseStoreConfig;
    [case(IKEEXT_CERT_CONFIG_TRUSTED_ROOT_STORE)]
    [unique] IKEEXT_CERT_ROOT_CONFIG0 *inboundTrustedRootStoreConfig;
    [case(IKEEXT_CERT_CONFIG_UNSPECIFIED)];
  };
  IKEEXT_CERT_CONFIG_TYPE outboundConfigType;
  [switch_type(IKEEXT_CERT_CONFIG_TYPE), switch_is(outboundConfigType)]
  union {
    [case(IKEEXT_CERT_CONFIG_EXPLICIT_TRUST_LIST)]
    struct {
      UINT32 outboundRootArraySize;
      [size_is(outboundRootArraySize), unique]
      IKEEXT_CERT_ROOT_CONFIG0 *outboundRootArray;
    };
    [case(IKEEXT_CERT_CONFIG_ENTERPRISE_STORE)]
    [unique] IKEEXT_CERT_ROOT_CONFIG0 *outboundEnterpriseStoreConfig;
    [case(IKEEXT_CERT_CONFIG_TRUSTED_ROOT_STORE)]
    [unique] IKEEXT_CERT_ROOT_CONFIG0 *outboundTrustedRootStoreConfig;
    [case(IKEEXT_CERT_CONFIG_UNSPECIFIED)];
  };
  UINT32 flags;
  FWP_BYTE_BLOB localCertLocationUrl;
} IKEEXT_CERTIFICATE_AUTHENTICATION1;
cpp_quote("#endif")
cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
typedef enum IKEEXT_CERT_CRITERIA_NAME_TYPE_ {
   IKEEXT_CERT_CRITERIA_DNS,
   IKEEXT_CERT_CRITERIA_UPN,
   IKEEXT_CERT_CRITERIA_RFC822,
   IKEEXT_CERT_CRITERIA_CN,
   IKEEXT_CERT_CRITERIA_OU,
   IKEEXT_CERT_CRITERIA_O,
   IKEEXT_CERT_CRITERIA_DC,
   IKEEXT_CERT_CRITERIA_NAME_TYPE_MAX
} IKEEXT_CERT_CRITERIA_NAME_TYPE;

cpp_quote("")

typedef struct IKEEXT_CERT_EKUS0_ {
  ULONG numEku;
  [string, size_is(numEku), ref] LPSTR *eku;
} IKEEXT_CERT_EKUS0;

cpp_quote("")

typedef struct IKEEXT_CERT_NAME0_ {
  IKEEXT_CERT_CRITERIA_NAME_TYPE nameType;
  [string, ref] LPWSTR certName;
} IKEEXT_CERT_NAME0;

cpp_quote("")

typedef struct IKEEXT_CERTIFICATE_CRITERIA0_ {
   FWP_BYTE_BLOB certData;
   FWP_BYTE_BLOB certHash;
   [unique] IKEEXT_CERT_EKUS0 *eku;
   [unique] IKEEXT_CERT_NAME0 *name;
   UINT32 flags;
} IKEEXT_CERTIFICATE_CRITERIA0;

cpp_quote("")

typedef struct IKEEXT_CERTIFICATE_AUTHENTICATION2_ {
  IKEEXT_CERT_CONFIG_TYPE inboundConfigType;
  [switch_type(IKEEXT_CERT_CONFIG_TYPE), switch_is(inboundConfigType)]
  union {
    [case(IKEEXT_CERT_CONFIG_EXPLICIT_TRUST_LIST)]
    struct {
      UINT32 inboundRootArraySize;
      [size_is(inboundRootArraySize), unique]
      IKEEXT_CERTIFICATE_CRITERIA0 *inboundRootCriteria;
    };
    [case(IKEEXT_CERT_CONFIG_ENTERPRISE_STORE)]        
    struct {
      UINT32 inboundEnterpriseStoreArraySize;
      [size_is(inboundEnterpriseStoreArraySize), unique]
      IKEEXT_CERTIFICATE_CRITERIA0 *inboundEnterpriseStoreCriteria;
    };
    [case(IKEEXT_CERT_CONFIG_TRUSTED_ROOT_STORE)]
    struct {
      UINT32 inboundRootStoreArraySize;
      [size_is(inboundRootStoreArraySize), unique]
      IKEEXT_CERTIFICATE_CRITERIA0 *inboundTrustedRootStoreCriteria;
    };
    [case(IKEEXT_CERT_CONFIG_UNSPECIFIED)];
  };
  IKEEXT_CERT_CONFIG_TYPE outboundConfigType;
  [switch_type(IKEEXT_CERT_CONFIG_TYPE), switch_is(outboundConfigType)]
  union {
    [case(IKEEXT_CERT_CONFIG_EXPLICIT_TRUST_LIST)]
    struct {
      UINT32 outboundRootArraySize;
      [size_is(outboundRootArraySize), unique]
      IKEEXT_CERTIFICATE_CRITERIA0 *outboundRootCriteria;
    };
    [case(IKEEXT_CERT_CONFIG_ENTERPRISE_STORE)]
    struct {
      UINT32 outboundEnterpriseStoreArraySize;
      [size_is(outboundEnterpriseStoreArraySize), unique]
      IKEEXT_CERTIFICATE_CRITERIA0 *outboundEnterpriseStoreCriteria;
    };
    [case(IKEEXT_CERT_CONFIG_TRUSTED_ROOT_STORE)]
    struct {
      UINT32 outboundRootStoreArraySize;
      [size_is(outboundRootStoreArraySize), unique]
      IKEEXT_CERTIFICATE_CRITERIA0 *outboundTrustedRootStoreCriteria;
    };
    [case(IKEEXT_CERT_CONFIG_UNSPECIFIED)];
  };
  UINT32 flags;
  FWP_BYTE_BLOB localCertLocationUrl;
} IKEEXT_CERTIFICATE_AUTHENTICATION2;
cpp_quote("#endif")
cpp_quote("")

typedef struct IKEEXT_IPV6_CGA_AUTHENTICATION0_ {
  [string, ref] wchar_t *keyContainerName;
  [string, unique] wchar_t *cspName;
  UINT32 cspType;
  FWP_BYTE_ARRAY16 cgaModifier;
  BYTE cgaCollisionCount;
} IKEEXT_IPV6_CGA_AUTHENTICATION0;

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
typedef struct IKEEXT_KERBEROS_AUTHENTICATION1__ {
  UINT32 flags;
  [string, unique] wchar_t *proxyServer;
} IKEEXT_KERBEROS_AUTHENTICATION1;
cpp_quote("#endif")
cpp_quote("")

typedef struct IKEEXT_RESERVED_AUTHENTICATION0__ {
  UINT32 flags;
} IKEEXT_RESERVED_AUTHENTICATION0;

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_EAP_AUTHENTICATION0__ {
  UINT32 flags;
} IKEEXT_EAP_AUTHENTICATION0;
cpp_quote("#endif")
cpp_quote("")

typedef struct IKEEXT_AUTHENTICATION_METHOD0_ {
  IKEEXT_AUTHENTICATION_METHOD_TYPE authenticationMethodType;
  [switch_type(IKEEXT_AUTHENTICATION_METHOD_TYPE),
  switch_is(authenticationMethodType)]
  union {
    [case(IKEEXT_PRESHARED_KEY)]
    IKEEXT_PRESHARED_KEY_AUTHENTICATION0 presharedKeyAuthentication;
    [case(IKEEXT_CERTIFICATE,
     IKEEXT_CERTIFICATE_ECDSA_P256,
     IKEEXT_CERTIFICATE_ECDSA_P384)]
    IKEEXT_CERTIFICATE_AUTHENTICATION0 certificateAuthentication;
    [case(IKEEXT_KERBEROS)]
    IKEEXT_KERBEROS_AUTHENTICATION0 kerberosAuthentication;
    [case(IKEEXT_NTLM_V2)]
    IKEEXT_NTLM_V2_AUTHENTICATION0 ntlmV2Authentication;
    [case(IKEEXT_ANONYMOUS)];
    [case(IKEEXT_SSL,
     IKEEXT_SSL_ECDSA_P256,
     IKEEXT_SSL_ECDSA_P384)]
    IKEEXT_CERTIFICATE_AUTHENTICATION0 sslAuthentication;
    [case(IKEEXT_IPV6_CGA)]
    IKEEXT_IPV6_CGA_AUTHENTICATION0 cgaAuthentication;
  };
} IKEEXT_AUTHENTICATION_METHOD0;

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_AUTHENTICATION_METHOD1_ {
  IKEEXT_AUTHENTICATION_METHOD_TYPE authenticationMethodType;
  [switch_type(IKEEXT_AUTHENTICATION_METHOD_TYPE),
  switch_is(authenticationMethodType)] union {
    [case(IKEEXT_PRESHARED_KEY)]
       IKEEXT_PRESHARED_KEY_AUTHENTICATION1 presharedKeyAuthentication;
    [case(IKEEXT_CERTIFICATE,
          IKEEXT_CERTIFICATE_ECDSA_P256,
          IKEEXT_CERTIFICATE_ECDSA_P384)]
       IKEEXT_CERTIFICATE_AUTHENTICATION1 certificateAuthentication;
    [case(IKEEXT_KERBEROS)]
       IKEEXT_KERBEROS_AUTHENTICATION0 kerberosAuthentication;
    [case(IKEEXT_NTLM_V2)]
       IKEEXT_NTLM_V2_AUTHENTICATION0 ntlmV2Authentication;
    [case(IKEEXT_ANONYMOUS)];
    [case(IKEEXT_SSL,
          IKEEXT_SSL_ECDSA_P256,
          IKEEXT_SSL_ECDSA_P384)]
       IKEEXT_CERTIFICATE_AUTHENTICATION1 sslAuthentication;
    [case(IKEEXT_IPV6_CGA)]
       IKEEXT_IPV6_CGA_AUTHENTICATION0 cgaAuthentication;
    [case(IKEEXT_EAP)]
       IKEEXT_EAP_AUTHENTICATION0 eapAuthentication;
  };
} IKEEXT_AUTHENTICATION_METHOD1;
cpp_quote("#endif")
cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
typedef struct IKEEXT_AUTHENTICATION_METHOD2_ {
  IKEEXT_AUTHENTICATION_METHOD_TYPE authenticationMethodType;
  [switch_type(IKEEXT_AUTHENTICATION_METHOD_TYPE),
  switch_is(authenticationMethodType)] union {
    [case(IKEEXT_PRESHARED_KEY)]
    IKEEXT_PRESHARED_KEY_AUTHENTICATION1 presharedKeyAuthentication;
    [case(IKEEXT_CERTIFICATE,
     IKEEXT_CERTIFICATE_ECDSA_P256,
     IKEEXT_CERTIFICATE_ECDSA_P384)]
    IKEEXT_CERTIFICATE_AUTHENTICATION2 certificateAuthentication;
    [case(IKEEXT_KERBEROS)]
    IKEEXT_KERBEROS_AUTHENTICATION1 kerberosAuthentication;
    [case(IKEEXT_RESERVED)]
    IKEEXT_RESERVED_AUTHENTICATION0 reservedAuthentication;
    [case(IKEEXT_NTLM_V2)]
    IKEEXT_NTLM_V2_AUTHENTICATION0 ntlmV2Authentication;
    [case(IKEEXT_ANONYMOUS)];
    [case(IKEEXT_SSL,
     IKEEXT_SSL_ECDSA_P256,
     IKEEXT_SSL_ECDSA_P384)]
    IKEEXT_CERTIFICATE_AUTHENTICATION2 sslAuthentication;
    [case(IKEEXT_IPV6_CGA)]
    IKEEXT_IPV6_CGA_AUTHENTICATION0 cgaAuthentication;
    [case(IKEEXT_EAP)]
    IKEEXT_EAP_AUTHENTICATION0 eapAuthentication;
  };
} IKEEXT_AUTHENTICATION_METHOD2;
cpp_quote("#endif")
cpp_quote("")

typedef struct IKEEXT_CIPHER_ALGORITHM0_ {
  IKEEXT_CIPHER_TYPE algoIdentifier;
  UINT32 keyLen;
  UINT32 rounds;
} IKEEXT_CIPHER_ALGORITHM0;

cpp_quote("")

typedef struct IKEEXT_INTEGRITY_ALGORITHM0_ {
  IKEEXT_INTEGRITY_TYPE algoIdentifier;
} IKEEXT_INTEGRITY_ALGORITHM0;

cpp_quote("")

typedef struct IKEEXT_PROPOSAL0_ {
  IKEEXT_CIPHER_ALGORITHM0 cipherAlgorithm;
  IKEEXT_INTEGRITY_ALGORITHM0 integrityAlgorithm;
  UINT32 maxLifetimeSeconds;
  IKEEXT_DH_GROUP dhGroup;
  UINT32 quickModeLimit;
} IKEEXT_PROPOSAL0;

cpp_quote("")

typedef struct IKEEXT_POLICY0_ {
  UINT32 softExpirationTime;
  UINT32 numAuthenticationMethods;
  [size_is(numAuthenticationMethods), ref]
  IKEEXT_AUTHENTICATION_METHOD0 *authenticationMethods;
  IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE initiatorImpersonationType;
  UINT32 numIkeProposals;
  [size_is(numIkeProposals), ref] IKEEXT_PROPOSAL0 *ikeProposals;
  UINT32 flags;
  UINT32 maxDynamicFilters;
} IKEEXT_POLICY0;

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_POLICY1_ {
  UINT32 softExpirationTime;
  UINT32 numAuthenticationMethods;
  [size_is(numAuthenticationMethods), ref]
  IKEEXT_AUTHENTICATION_METHOD1 *authenticationMethods;
  IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE initiatorImpersonationType;
  UINT32 numIkeProposals;
  [size_is(numIkeProposals), ref] IKEEXT_PROPOSAL0 *ikeProposals;
  UINT32 flags;
  UINT32 maxDynamicFilters;
  UINT32 retransmitDurationSecs;
} IKEEXT_POLICY1;
cpp_quote("#endif")

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
typedef struct IKEEXT_POLICY2_ {
  UINT32 softExpirationTime;
  UINT32 numAuthenticationMethods;
  [size_is(numAuthenticationMethods), ref]
  IKEEXT_AUTHENTICATION_METHOD2 *authenticationMethods;
  IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE initiatorImpersonationType;
  UINT32 numIkeProposals;
  [size_is(numIkeProposals), ref] IKEEXT_PROPOSAL0 *ikeProposals;
  UINT32 flags;
  UINT32 maxDynamicFilters;
  UINT32 retransmitDurationSecs;
} IKEEXT_POLICY2;
cpp_quote("#endif")
cpp_quote("")

typedef struct IKEEXT_EM_POLICY0_ {
  UINT32 numAuthenticationMethods;
  [size_is(numAuthenticationMethods), ref]
  IKEEXT_AUTHENTICATION_METHOD0 *authenticationMethods;
  IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE initiatorImpersonationType;
} IKEEXT_EM_POLICY0;

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_EM_POLICY1_ {
  UINT32 numAuthenticationMethods;
  [size_is(numAuthenticationMethods), ref]
  IKEEXT_AUTHENTICATION_METHOD1 *authenticationMethods;
  IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE initiatorImpersonationType;
} IKEEXT_EM_POLICY1;
cpp_quote("#endif")

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
typedef struct IKEEXT_EM_POLICY2_ {
  UINT32 numAuthenticationMethods;
  [size_is(numAuthenticationMethods), ref]
  IKEEXT_AUTHENTICATION_METHOD2 *authenticationMethods;
  IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE initiatorImpersonationType;
} IKEEXT_EM_POLICY2;
cpp_quote("#endif")

cpp_quote("")

typedef struct IKEEXT_IP_VERSION_SPECIFIC_KEYMODULE_STATISTICS0_ {
  UINT32 currentActiveMainModes;
  UINT32 totalMainModesStarted;
  UINT32 totalSuccessfulMainModes;
  UINT32 totalFailedMainModes;
  UINT32 totalResponderMainModes;
  UINT32 currentNewResponderMainModes;
  UINT32 currentActiveQuickModes;
  UINT32 totalQuickModesStarted;
  UINT32 totalSuccessfulQuickModes;
  UINT32 totalFailedQuickModes;
  UINT32 totalAcquires;
  UINT32 totalReinitAcquires;
  UINT32 currentActiveExtendedModes;
  UINT32 totalExtendedModesStarted;
  UINT32 totalSuccessfulExtendedModes;
  UINT32 totalFailedExtendedModes;
  UINT32 totalImpersonationExtendedModes;
  UINT32 totalImpersonationMainModes;
} IKEEXT_IP_VERSION_SPECIFIC_KEYMODULE_STATISTICS0;

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_IP_VERSION_SPECIFIC_KEYMODULE_STATISTICS1_ {
  UINT32 currentActiveMainModes;
  UINT32 totalMainModesStarted;
  UINT32 totalSuccessfulMainModes;
  UINT32 totalFailedMainModes;
  UINT32 totalResponderMainModes;
  UINT32 currentNewResponderMainModes;
  UINT32 currentActiveQuickModes;
  UINT32 totalQuickModesStarted;
  UINT32 totalSuccessfulQuickModes;
  UINT32 totalFailedQuickModes;
  UINT32 totalAcquires;
  UINT32 totalReinitAcquires;
  UINT32 currentActiveExtendedModes;
  UINT32 totalExtendedModesStarted;
  UINT32 totalSuccessfulExtendedModes;
  UINT32 totalFailedExtendedModes;
  UINT32 totalImpersonationExtendedModes;
  UINT32 totalImpersonationMainModes;
} IKEEXT_IP_VERSION_SPECIFIC_KEYMODULE_STATISTICS1;
cpp_quote("#endif")

cpp_quote("")

typedef struct IKEEXT_KEYMODULE_STATISTICS0_ {
  IKEEXT_IP_VERSION_SPECIFIC_KEYMODULE_STATISTICS0 v4Statistics;
  IKEEXT_IP_VERSION_SPECIFIC_KEYMODULE_STATISTICS0 v6Statistics;
  UINT32 errorFrequencyTable[IKEEXT_ERROR_CODE_COUNT];
  UINT32 mainModeNegotiationTime;
  UINT32 quickModeNegotiationTime;
  UINT32 extendedModeNegotiationTime;
} IKEEXT_KEYMODULE_STATISTICS0;

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_KEYMODULE_STATISTICS1_ {
  IKEEXT_IP_VERSION_SPECIFIC_KEYMODULE_STATISTICS1 v4Statistics;
  IKEEXT_IP_VERSION_SPECIFIC_KEYMODULE_STATISTICS1 v6Statistics;
  UINT32 errorFrequencyTable[IKEEXT_ERROR_CODE_COUNT];
  UINT32 mainModeNegotiationTime;
  UINT32 quickModeNegotiationTime;
  UINT32 extendedModeNegotiationTime;
} IKEEXT_KEYMODULE_STATISTICS1;
cpp_quote("#endif")

cpp_quote("")

typedef struct IKEEXT_IP_VERSION_SPECIFIC_COMMON_STATISTICS0_ {
  UINT32 totalSocketReceiveFailures;
  UINT32 totalSocketSendFailures;
} IKEEXT_IP_VERSION_SPECIFIC_COMMON_STATISTICS0;

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_IP_VERSION_SPECIFIC_COMMON_STATISTICS1_ {
  UINT32 totalSocketReceiveFailures;
  UINT32 totalSocketSendFailures;
} IKEEXT_IP_VERSION_SPECIFIC_COMMON_STATISTICS1;
cpp_quote("#endif")

cpp_quote("")

typedef struct IKEEXT_COMMON_STATISTICS0_ {
  IKEEXT_IP_VERSION_SPECIFIC_COMMON_STATISTICS0 v4Statistics;
  IKEEXT_IP_VERSION_SPECIFIC_COMMON_STATISTICS0 v6Statistics;
  UINT32 totalPacketsReceived;
  UINT32 totalInvalidPacketsReceived;
  UINT32 currentQueuedWorkitems;
} IKEEXT_COMMON_STATISTICS0;

cpp_quote("")
cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_COMMON_STATISTICS1_ {
  IKEEXT_IP_VERSION_SPECIFIC_COMMON_STATISTICS1 v4Statistics;
  IKEEXT_IP_VERSION_SPECIFIC_COMMON_STATISTICS1 v6Statistics;
  UINT32 totalPacketsReceived;
  UINT32 totalInvalidPacketsReceived;
  UINT32 currentQueuedWorkitems;
} IKEEXT_COMMON_STATISTICS1;
cpp_quote("#endif")

cpp_quote("")
typedef struct IKEEXT_STATISTICS0_ {
  IKEEXT_KEYMODULE_STATISTICS0 ikeStatistics;
  IKEEXT_KEYMODULE_STATISTICS0 authipStatistics;
  IKEEXT_COMMON_STATISTICS0 commonStatistics;
} IKEEXT_STATISTICS0;

cpp_quote("")
cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_STATISTICS1_ {
  IKEEXT_KEYMODULE_STATISTICS1 ikeStatistics;
  IKEEXT_KEYMODULE_STATISTICS1 authipStatistics;
  IKEEXT_KEYMODULE_STATISTICS1 ikeV2Statistics;
  IKEEXT_COMMON_STATISTICS1 commonStatistics;
} IKEEXT_STATISTICS1;
cpp_quote("#endif")

cpp_quote("")

typedef struct IKEEXT_TRAFFIC0_ {
  FWP_IP_VERSION ipVersion;
  [switch_type(FWP_IP_VERSION), switch_is(ipVersion)] union {
     [case(FWP_IP_VERSION_V4)]
        UINT32 localV4Address;
     [case(FWP_IP_VERSION_V6)]
        UINT8 localV6Address[16];
  };
  [switch_type(FWP_IP_VERSION), switch_is(ipVersion)] union {
     [case(FWP_IP_VERSION_V4)]
        UINT32 remoteV4Address;
     [case(FWP_IP_VERSION_V6)]
        UINT8 remoteV6Address[16];
  };
  UINT64 authIpFilterId;
} IKEEXT_TRAFFIC0;

cpp_quote("")

typedef struct IKEEXT_COOKIE_PAIR0_ {
  IKEEXT_COOKIE initiator;
  IKEEXT_COOKIE responder;
} IKEEXT_COOKIE_PAIR0;

cpp_quote("")

typedef struct IKEEXT_CERTIFICATE_CREDENTIAL0_ {
  FWP_BYTE_BLOB subjectName;
  FWP_BYTE_BLOB certHash;
  UINT32 flags;
} IKEEXT_CERTIFICATE_CREDENTIAL0;

cpp_quote("")

typedef struct IKEEXT_NAME_CREDENTIAL0_ {
  [string, ref] wchar_t *principalName;
} IKEEXT_NAME_CREDENTIAL0;

typedef struct IKEEXT_CREDENTIAL0_ {
  IKEEXT_AUTHENTICATION_METHOD_TYPE authenticationMethodType;
  IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE impersonationType;
  [switch_type(IKEEXT_AUTHENTICATION_METHOD_TYPE),
   switch_is(authenticationMethodType)] union {
     [case(IKEEXT_PRESHARED_KEY)]
        [unique] IKEEXT_PRESHARED_KEY_AUTHENTICATION0 *presharedKey;
     [case(IKEEXT_CERTIFICATE,
           IKEEXT_CERTIFICATE_ECDSA_P256,
           IKEEXT_CERTIFICATE_ECDSA_P384,
           IKEEXT_SSL,
           IKEEXT_SSL_ECDSA_P256,
           IKEEXT_SSL_ECDSA_P384,
           IKEEXT_IPV6_CGA
           )]
        [unique] IKEEXT_CERTIFICATE_CREDENTIAL0 *certificate;
     [case(IKEEXT_KERBEROS,
           IKEEXT_EAP,
           IKEEXT_NTLM_V2)]
        [unique] IKEEXT_NAME_CREDENTIAL0 *name;
     [case(IKEEXT_ANONYMOUS)];
  };
} IKEEXT_CREDENTIAL0;

cpp_quote("")

typedef struct IKEEXT_CREDENTIAL_PAIR0_ {
  IKEEXT_CREDENTIAL0 localCredentials;
  IKEEXT_CREDENTIAL0 peerCredentials;
} IKEEXT_CREDENTIAL_PAIR0;

cpp_quote("")

typedef struct IKEEXT_CREDENTIALS0_ {
  UINT32 numCredentials;
  [size_is(numCredentials), ref] IKEEXT_CREDENTIAL_PAIR0 *credentials;
} IKEEXT_CREDENTIALS0;

cpp_quote("")

typedef struct IKEEXT_SA_DETAILS0_ {
  UINT64 saId;
  IKEEXT_KEY_MODULE_TYPE keyModuleType;
  FWP_IP_VERSION ipVersion;
  [switch_type(FWP_IP_VERSION), switch_is(ipVersion)] union {
     [case(FWP_IP_VERSION_V4)]
        [unique] IPSEC_V4_UDP_ENCAPSULATION0 *v4UdpEncapsulation;
     [case(FWP_IP_VERSION_V6)];
  };
  IKEEXT_TRAFFIC0 ikeTraffic;
  IKEEXT_PROPOSAL0 ikeProposal;
  IKEEXT_COOKIE_PAIR0 cookiePair;
  IKEEXT_CREDENTIALS0 ikeCredentials;
  GUID ikePolicyKey;
  UINT64 virtualIfTunnelId;
} IKEEXT_SA_DETAILS0;

cpp_quote("")
cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN7)")
typedef struct IKEEXT_CERTIFICATE_CREDENTIAL1_ {
  FWP_BYTE_BLOB subjectName;
  FWP_BYTE_BLOB certHash;
  UINT32 flags;
  FWP_BYTE_BLOB certificate;
} IKEEXT_CERTIFICATE_CREDENTIAL1;

cpp_quote("")

typedef struct IKEEXT_CREDENTIAL1_ {
  IKEEXT_AUTHENTICATION_METHOD_TYPE authenticationMethodType;
  IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE impersonationType;
  [switch_type(IKEEXT_AUTHENTICATION_METHOD_TYPE),
  switch_is(authenticationMethodType)] union {
    [case(IKEEXT_PRESHARED_KEY)]
       [unique] IKEEXT_PRESHARED_KEY_AUTHENTICATION1 *presharedKey;
    [case(IKEEXT_CERTIFICATE,
          IKEEXT_CERTIFICATE_ECDSA_P256,
          IKEEXT_CERTIFICATE_ECDSA_P384,
          IKEEXT_SSL,
          IKEEXT_SSL_ECDSA_P256,
          IKEEXT_SSL_ECDSA_P384,
          IKEEXT_IPV6_CGA
          )]
       [unique] IKEEXT_CERTIFICATE_CREDENTIAL1 *certificate;
    [case(IKEEXT_KERBEROS,
          IKEEXT_EAP,
          IKEEXT_NTLM_V2)]
       [unique] IKEEXT_NAME_CREDENTIAL0 *name;
    [case(IKEEXT_ANONYMOUS)];
  };
} IKEEXT_CREDENTIAL1;

cpp_quote("")

typedef struct IKEEXT_CREDENTIAL_PAIR1_ {
  IKEEXT_CREDENTIAL1 localCredentials;
  IKEEXT_CREDENTIAL1 peerCredentials;
} IKEEXT_CREDENTIAL_PAIR1;

cpp_quote("")

typedef struct IKEEXT_CREDENTIALS1_ {
  UINT32 numCredentials;
  [size_is(numCredentials), ref] IKEEXT_CREDENTIAL_PAIR1 *credentials;
} IKEEXT_CREDENTIALS1;

cpp_quote("")

typedef struct IKEEXT_SA_DETAILS1_ {
  UINT64 saId;
  IKEEXT_KEY_MODULE_TYPE keyModuleType;
  FWP_IP_VERSION ipVersion;
  [switch_type(FWP_IP_VERSION), switch_is(ipVersion)] union {
    [case(FWP_IP_VERSION_V4)]
       [unique] IPSEC_V4_UDP_ENCAPSULATION0 *v4UdpEncapsulation;
    [case(FWP_IP_VERSION_V6)];
  };
  IKEEXT_TRAFFIC0 ikeTraffic;
  IKEEXT_PROPOSAL0 ikeProposal;
  IKEEXT_COOKIE_PAIR0 cookiePair;
  IKEEXT_CREDENTIALS1 ikeCredentials;
  GUID ikePolicyKey;
  UINT64 virtualIfTunnelId;
  FWP_BYTE_BLOB correlationKey;
} IKEEXT_SA_DETAILS1;
cpp_quote("#endif")

cpp_quote("")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")
typedef struct IKEEXT_CREDENTIAL2_ {
  IKEEXT_AUTHENTICATION_METHOD_TYPE authenticationMethodType;
  IKEEXT_AUTHENTICATION_IMPERSONATION_TYPE impersonationType;
  [switch_type(IKEEXT_AUTHENTICATION_METHOD_TYPE),
  switch_is(authenticationMethodType)] union
  {
    [case(IKEEXT_PRESHARED_KEY)]
       [unique] IKEEXT_PRESHARED_KEY_AUTHENTICATION1 *presharedKey;
    [case(IKEEXT_CERTIFICATE,
          IKEEXT_CERTIFICATE_ECDSA_P256,
          IKEEXT_CERTIFICATE_ECDSA_P384,
          IKEEXT_SSL,
          IKEEXT_SSL_ECDSA_P256,
          IKEEXT_SSL_ECDSA_P384,
          IKEEXT_IPV6_CGA
          )]
       [unique] IKEEXT_CERTIFICATE_CREDENTIAL1 *certificate;
    [case(IKEEXT_KERBEROS,
          IKEEXT_EAP,
          IKEEXT_NTLM_V2,
          IKEEXT_RESERVED)]
       [unique] IKEEXT_NAME_CREDENTIAL0 *name;
    [case(IKEEXT_ANONYMOUS)];
  };
} IKEEXT_CREDENTIAL2;

cpp_quote("")

typedef struct IKEEXT_CREDENTIAL_PAIR2_ {
  IKEEXT_CREDENTIAL2 localCredentials;
  IKEEXT_CREDENTIAL2 peerCredentials;
} IKEEXT_CREDENTIAL_PAIR2;

cpp_quote("")

typedef struct IKEEXT_CREDENTIALS2_ {
  UINT32 numCredentials;
  [size_is(numCredentials), ref] IKEEXT_CREDENTIAL_PAIR2 *credentials;
} IKEEXT_CREDENTIALS2;

cpp_quote("")

typedef struct IKEEXT_SA_DETAILS2_ {
  UINT64 saId;
  IKEEXT_KEY_MODULE_TYPE keyModuleType;
  FWP_IP_VERSION ipVersion;
  [switch_type(FWP_IP_VERSION), switch_is(ipVersion)] union {
    [case(FWP_IP_VERSION_V4)]
       [unique] IPSEC_V4_UDP_ENCAPSULATION0 *v4UdpEncapsulation;
    [case(FWP_IP_VERSION_V6)];
  };
  IKEEXT_TRAFFIC0 ikeTraffic;
  IKEEXT_PROPOSAL0 ikeProposal;
  IKEEXT_COOKIE_PAIR0 cookiePair;
  IKEEXT_CREDENTIALS2 ikeCredentials;
  GUID ikePolicyKey;
  UINT64 virtualIfTunnelId;
  FWP_BYTE_BLOB correlationKey;
} IKEEXT_SA_DETAILS2;
cpp_quote("#endif")

cpp_quote("")

typedef struct IKEEXT_SA_ENUM_TEMPLATE0_ {
  FWP_CONDITION_VALUE0 localSubNet;
  FWP_CONDITION_VALUE0 remoteSubNet;
  FWP_BYTE_BLOB localMainModeCertHash;
} IKEEXT_SA_ENUM_TEMPLATE0;

cpp_quote("")
cpp_quote("#endif /* WINAPI_PARTITION_DESKTOP.  */")
