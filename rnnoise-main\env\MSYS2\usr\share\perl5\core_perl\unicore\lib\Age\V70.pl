# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V282
895
896
1320
1328
1421
1423
1541
1542
2209
2210
2221
2227
2303
2304
2424
2425
2432
2433
3072
3073
3124
3125
3201
3202
3329
3330
3558
3568
5873
5881
6429
6431
6832
6847
7416
7418
7655
7670
8379
8382
9204
9211
9984
9985
11085
11088
11098
11124
11126
11158
11160
11194
11197
11209
11210
11218
11836
11843
42648
42654
42900
42912
42923
42926
42928
42930
42999
43000
43488
43519
43644
43648
43824
43872
43876
43878
65063
65070
65931
65933
65952
65953
66272
66300
66335
66336
66384
66427
66816
66856
66864
66916
66927
66928
67072
67383
67392
67414
67424
67432
67680
67743
67751
67760
68224
68256
68288
68327
68331
68343
68480
68498
68505
68509
68521
68528
69759
69760
69968
70007
70093
70094
70106
70107
70113
70133
70144
70162
70163
70206
70320
70379
70384
70394
70401
70404
70405
70413
70415
70417
70419
70441
70442
70449
70450
70452
70453
70458
70460
70469
70471
70473
70475
70478
70487
70488
70493
70500
70502
70509
70512
70517
70784
70856
70864
70874
71040
71094
71096
71114
71168
71237
71248
71258
71840
71923
71935
71936
72384
72441
74607
74649
74851
74863
74868
74869
92736
92767
92768
92778
92782
92784
92880
92910
92912
92918
92928
92998
93008
93018
93019
93026
93027
93048
93053
93072
113664
113771
113776
113789
113792
113801
113808
113818
113820
113828
124928
125125
125127
125143
127167
127168
127200
127222
127243
127245
127777
127789
127798
127799
127869
127870
127892
127904
127941
127942
127947
127951
127956
127968
127985
127992
128063
128064
128065
128066
128248
128249
128253
128255
128318
128320
128324
128331
128360
128378
128379
128420
128421
128507
128577
128579
128592
128640
128710
128720
128736
128749
128752
128756
128896
128981
129024
129036
129040
129096
129104
129114
129120
129160
129168
129198
END
