'\" t
.\"     Title: mkpasswd
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/18/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "MKPASSWD" "1" "06/18/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
mkpasswd \- Write /etc/passwd\-like output to stdout
.SH "SYNOPSIS"
.HP \w'\fBmkpasswd\fR\ 'u
\fBmkpasswd\fR [\-l\ |\ \-L\ [\fIMACHINE\fR]] [\-d\ [\fIDOMAIN\fR]] [\-c] [\-S\ \fICHAR\fR] [\-o\ \fIOFFSET\fR] [\-u\ \fIUSERNAME\fR] [\-b] [\-U\ \fIUSERLIST\fR]
.HP \w'\fBmkpassword\fR\ 'u
\fBmkpassword\fR \-h | \-V 
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
   \-l,\-\-local [machine]    Print local user accounts of \e"machine\e",
                           from local machine if no machine specified\&.
                           Automatically adding machine prefix for local
                           machine depends on settings in /etc/nsswitch\&.conf\&.
   \-L,\-\-Local [machine]    Ditto, but generate username with machine prefix\&.
   \-d,\-\-domain [domain]    Print domain accounts,
                           from current domain if no domain specified\&.
   \-c,\-\-current            Print current user\&.
   \-S,\-\-separator char     For \-L use character char as domain\e\euser
                           separator in username instead of the default \*(Aq+\*(Aq\&.
   \-o,\-\-id\-offset offset   Change the default offset (0x10000) added to uids
                           of foreign local machine accounts\&.  Use with \-l/\-L\&.
   \-u,\-\-username username  Only return information for the specified user\&.
                           One of \-l, \-d must be specified, too
   \-b,\-\-no\-builtin         Don\*(Aqt print BUILTIN users\&.
   \-p,\-\-path\-to\-home path  Use specified path instead of user account home dir
                           or /home prefix\&.
   \-U,\-\-unix userlist      Print UNIX users when using \-l on a UNIX Samba
                           server\&.  Userlist is a comma\-separated list of
                           usernames or uid ranges (root,\-25,50\-100)\&.
                           Enumerating large ranges can take a long time!
   \-h,\-\-help               Displays this message\&.
   \-V,\-\-version            Version information and exit\&.

Default is to print local accounts on stand\-alone machines, domain accounts
on domain controllers and domain member machines\&.
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
Don\*(Aqt use this command to generate a local /etc/passwd file, unless you really need one\&. See the Cygwin User\*(Aqs Guide for more information\&.
.PP
The
\fBmkpasswd\fR
program can be used to create a
/etc/passwd
file\&. Cygwin doesn\*(Aqt need this file, because it reads user information from the Windows account databases, but you can add an
/etc/passwd
file, for instance if your machine is often disconnected from its domain controller\&.
.PP
Note that this information is static, in contrast to the information automatically gathered by Cygwin from the Windows account databases\&. If you change the user information on your system, you\*(Aqll need to regenerate the passwd file for it to have the new information\&.
.PP
By default, the information generated by
\fBmkpasswd\fR
is equivalent to the information generated by Cygwin itself\&. The
\-d
and
\-l/\-L
options allow you to specify where the information comes from, some domain, or the local SAM of a machine\&. Note that you can only enumerate accounts from trusted domains\&. Any non\-trusted domain will be ignored\&. Access\-restrictions of your current account apply\&. The
\-l/\-L
when used with a machine name, tries to contact that machine to enumerate local groups of other machines, typically outside of domains\&. This scenario cannot be covered by Cygwin\*(Aqs account automatism\&. If you want to use the
\-L
option, but you don\*(Aqt like the default domain/group separator from
/etc/nsswitch\&.conf, you can specify another separator using the
\-S
option, analog to
\fBmkgroup\fR\&.
.PP
For very simple needs, an entry for the current user can be created by using the option
\-c\&.
.PP
The
\-o
option allows for special cases (such as multiple domains) where the UIDs might match otherwise\&. The
\-p
option causes
\fBmkpasswd\fR
to use the specified prefix instead of the account home dir or
/home/<USER>
.PP
\fBExample\ \&3.10.\ \&Using an alternate home root\fR
.sp
.if n \{\
.RS 4
.\}
.nf
$ \fBmkpasswd \-l \-p "$(cygpath \-H)" > /etc/passwd\fR
.fi
.if n \{\
.RE
.\}
.PP
would put local users\*(Aq home directories in the Windows \*(AqProfiles\*(Aq directory\&. The
\-u
option creates just an entry for the specified user\&. The
\-U
option allows you to enumerate the standard UNIX users on a Samba machine\&. It\*(Aqs used together with
\-l samba\-server
or
\-L samba\-server\&. The normal UNIX users are usually not enumerated, but they can show up as file owners in
\fBls \-l\fR
output\&.
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
