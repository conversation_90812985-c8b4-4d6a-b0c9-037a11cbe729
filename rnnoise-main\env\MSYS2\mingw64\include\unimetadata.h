/* DO NOT EDIT! GENERATED AUTOMATICALLY! */
/* Meta information about GNU libunistring.
   Copyright (C) 2024 Free Software Foundation, Inc.

   This file is free software: you can redistribute it and/or modify
   it under the terms of the GNU Lesser General Public License as
   published by the Free Software Foundation; either version 2.1 of the
   License, or (at your option) any later version.

   This file is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU Lesser General Public License for more details.

   You should have received a copy of the GNU Lesser General Public License
   along with this program.  If not, see <https://www.gnu.org/licenses/>.  */

#ifndef _UNIMETADATA_H
#define _UNIMETADATA_H

#if 1
# include <unistring/woe32dll.h>
#else
# define LIBUNISTRING_DLL_VARIABLE
#endif

#ifdef __cplusplus
extern "C" {
#endif


/* Supported Unicode version number: (major<<8) + minor  */
extern LIBUNISTRING_DLL_VARIABLE const int _libunistring_unicode_version;


#ifdef __cplusplus
}
#endif

#endif /* _UNIMETADATA_H */
