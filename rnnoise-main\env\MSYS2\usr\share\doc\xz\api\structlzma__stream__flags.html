<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_stream_flags Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structlzma__stream__flags.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_stream_flags Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Options for encoding/decoding Stream Header and Stream Footer.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;stream_flags.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a61e9151869d5b77c868aaa4958e74d10" id="r_a61e9151869d5b77c868aaa4958e74d10"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a61e9151869d5b77c868aaa4958e74d10">version</a></td></tr>
<tr class="memdesc:a61e9151869d5b77c868aaa4958e74d10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Stream Flags format version.  <br /></td></tr>
<tr class="separator:a61e9151869d5b77c868aaa4958e74d10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa65ed7a55a098f829f04dba25d0f212" id="r_aaa65ed7a55a098f829f04dba25d0f212"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaa65ed7a55a098f829f04dba25d0f212">backward_size</a></td></tr>
<tr class="memdesc:aaa65ed7a55a098f829f04dba25d0f212"><td class="mdescLeft">&#160;</td><td class="mdescRight">Backward Size.  <br /></td></tr>
<tr class="separator:aaa65ed7a55a098f829f04dba25d0f212"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1052ea7047c8d67f127f33278166647" id="r_ab1052ea7047c8d67f127f33278166647"><td class="memItemLeft" align="right" valign="top"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab1052ea7047c8d67f127f33278166647">check</a></td></tr>
<tr class="memdesc:ab1052ea7047c8d67f127f33278166647"><td class="mdescLeft">&#160;</td><td class="mdescRight">Check ID.  <br /></td></tr>
<tr class="separator:ab1052ea7047c8d67f127f33278166647"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Options for encoding/decoding Stream Header and Stream Footer. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a61e9151869d5b77c868aaa4958e74d10" name="a61e9151869d5b77c868aaa4958e74d10"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a61e9151869d5b77c868aaa4958e74d10">&#9670;&#160;</a></span>version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_stream_flags::version</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Stream Flags format version. </p>
<p>To prevent API and ABI breakages if new features are needed in Stream Header or Stream Footer, a version number is used to indicate which members in this structure are in use. For now, version must always be zero. With non-zero version, the <a class="el" href="stream__flags_8h.html#a2ebb8d6dff23daeb3de398913b845eff" title="Encode Stream Header.">lzma_stream_header_encode()</a> and <a class="el" href="stream__flags_8h.html#a438249a75ea8da952a7474b92bfe7b7a" title="Encode Stream Footer.">lzma_stream_footer_encode()</a> will return LZMA_OPTIONS_ERROR.</p>
<p><a class="el" href="stream__flags_8h.html#ae03198e464f0d296e601ff841e100805" title="Decode Stream Header.">lzma_stream_header_decode()</a> and <a class="el" href="stream__flags_8h.html#aa92a383f85753bb79ee23227fa68186c" title="Decode Stream Footer.">lzma_stream_footer_decode()</a> will always set this to the lowest value that supports all the features indicated by the Stream Flags field. The application must check that the version number set by the decoding functions is supported by the application. Otherwise it is possible that the application will decode the Stream incorrectly. </p>

</div>
</div>
<a id="aaa65ed7a55a098f829f04dba25d0f212" name="aaa65ed7a55a098f829f04dba25d0f212"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa65ed7a55a098f829f04dba25d0f212">&#9670;&#160;</a></span>backward_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_stream_flags::backward_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Backward Size. </p>
<p>Backward Size must be a multiple of four bytes. In this Stream format version, Backward Size is the size of the Index field.</p>
<p>Backward Size isn't actually part of the Stream Flags field, but it is convenient to include in this structure anyway. Backward Size is present only in the Stream Footer. There is no need to initialize backward_size when encoding Stream Header.</p>
<p><a class="el" href="stream__flags_8h.html#ae03198e464f0d296e601ff841e100805" title="Decode Stream Header.">lzma_stream_header_decode()</a> always sets backward_size to LZMA_VLI_UNKNOWN so that it is convenient to use <a class="el" href="stream__flags_8h.html#a3e25ca4205021302882a696283d45263" title="Compare two lzma_stream_flags structures.">lzma_stream_flags_compare()</a> when both Stream Header and Stream Footer have been decoded. </p>

</div>
</div>
<a id="ab1052ea7047c8d67f127f33278166647" name="ab1052ea7047c8d67f127f33278166647"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab1052ea7047c8d67f127f33278166647">&#9670;&#160;</a></span>check</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> lzma_stream_flags::check</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Check ID. </p>
<p>This indicates the type of the integrity check calculated from uncompressed data. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="stream__flags_8h.html">stream_flags.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
