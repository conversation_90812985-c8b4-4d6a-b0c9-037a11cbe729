.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hex_decode" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hex_decode \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_hex_decode(const gnutls_datum_t * " hex_data ", void * " result ", size_t * " result_size ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * hex_data" 12
contain the encoded data
.IP "void * result" 12
the place where decoded data will be copied
.IP "size_t * result_size" 12
holds the size of the result
.SH "DESCRIPTION"
This function will decode the given encoded data, using the hex
encoding used by PSK password files.

Initially  \fIresult_size\fP must hold the maximum size available in
 \fIresult\fP , and on return it will contain the number of bytes written.
.SH "RETURNS"
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if the buffer given is not
long enough, \fBGNUTLS_E_PARSING_ERROR\fP on invalid hex data, or 0 on success.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
