<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_block Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structlzma__block.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_block Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Options for the Block and Block Header encoders and decoders.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;block.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:ac3936a5b0ec3f9b8f9c7ad68e7d149a5" id="r_ac3936a5b0ec3f9b8f9c7ad68e7d149a5"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac3936a5b0ec3f9b8f9c7ad68e7d149a5">version</a></td></tr>
<tr class="memdesc:ac3936a5b0ec3f9b8f9c7ad68e7d149a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Block format version.  <br /></td></tr>
<tr class="separator:ac3936a5b0ec3f9b8f9c7ad68e7d149a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6689c4f7524b2c05772a2d6151138610" id="r_a6689c4f7524b2c05772a2d6151138610"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6689c4f7524b2c05772a2d6151138610">header_size</a></td></tr>
<tr class="memdesc:a6689c4f7524b2c05772a2d6151138610"><td class="mdescLeft">&#160;</td><td class="mdescRight">Size of the Block Header field in bytes.  <br /></td></tr>
<tr class="separator:a6689c4f7524b2c05772a2d6151138610"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80cd9d3025991db4a476ce7588f853e6" id="r_a80cd9d3025991db4a476ce7588f853e6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a80cd9d3025991db4a476ce7588f853e6">check</a></td></tr>
<tr class="memdesc:a80cd9d3025991db4a476ce7588f853e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type of integrity Check.  <br /></td></tr>
<tr class="separator:a80cd9d3025991db4a476ce7588f853e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8383d489c9ffea8af390669a105c74e5" id="r_a8383d489c9ffea8af390669a105c74e5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8383d489c9ffea8af390669a105c74e5">compressed_size</a></td></tr>
<tr class="memdesc:a8383d489c9ffea8af390669a105c74e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Size of the Compressed Data in bytes.  <br /></td></tr>
<tr class="separator:a8383d489c9ffea8af390669a105c74e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17362d38d1946dd16a9686557ec19a94" id="r_a17362d38d1946dd16a9686557ec19a94"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a17362d38d1946dd16a9686557ec19a94">uncompressed_size</a></td></tr>
<tr class="memdesc:a17362d38d1946dd16a9686557ec19a94"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed Size in bytes.  <br /></td></tr>
<tr class="separator:a17362d38d1946dd16a9686557ec19a94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5900e517e6e0a473a3184074ae7defd1" id="r_a5900e517e6e0a473a3184074ae7defd1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structlzma__filter.html">lzma_filter</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5900e517e6e0a473a3184074ae7defd1">filters</a></td></tr>
<tr class="memdesc:a5900e517e6e0a473a3184074ae7defd1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Array of filters.  <br /></td></tr>
<tr class="separator:a5900e517e6e0a473a3184074ae7defd1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25e9bf1bb1699017694b18ca24f965d2" id="r_a25e9bf1bb1699017694b18ca24f965d2"><td class="memItemLeft" align="right" valign="top">uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a25e9bf1bb1699017694b18ca24f965d2">raw_check</a> [<a class="el" href="check_8h.html#a379e931cf86351ab1d97896cda9abbe0">LZMA_CHECK_SIZE_MAX</a>]</td></tr>
<tr class="memdesc:a25e9bf1bb1699017694b18ca24f965d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Raw value stored in the Check field.  <br /></td></tr>
<tr class="separator:a25e9bf1bb1699017694b18ca24f965d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a516ac9cc63bc1a4fadd9fbfc189a206b" id="r_a516ac9cc63bc1a4fadd9fbfc189a206b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a516ac9cc63bc1a4fadd9fbfc189a206b">ignore_check</a></td></tr>
<tr class="memdesc:a516ac9cc63bc1a4fadd9fbfc189a206b"><td class="mdescLeft">&#160;</td><td class="mdescRight">A flag to Block decoder to not verify the Check field.  <br /></td></tr>
<tr class="separator:a516ac9cc63bc1a4fadd9fbfc189a206b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Options for the Block and Block Header encoders and decoders. </p>
<p>Different Block handling functions use different parts of this structure. Some read some members, other functions write, and some do both. Only the members listed for reading need to be initialized when the specified functions are called. The members marked for writing will be assigned new values at some point either by calling the given function or by later calls to <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="ac3936a5b0ec3f9b8f9c7ad68e7d149a5" name="ac3936a5b0ec3f9b8f9c7ad68e7d149a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac3936a5b0ec3f9b8f9c7ad68e7d149a5">&#9670;&#160;</a></span>version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_block::version</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Block format version. </p>
<p>To prevent API and ABI breakages when new features are needed, a version number is used to indicate which members in this structure are in use:</p><ul>
<li>liblzma &gt;= 5.0.0: version = 0 is supported.</li>
<li>liblzma &gt;= 5.1.4beta: Support for version = 1 was added, which adds the ignore_check member.</li>
</ul>
<p>If version is greater than one, most Block related functions will return LZMA_OPTIONS_ERROR (<a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a> works with any version value).</p>
<p>Read by:</p><ul>
<li><a class="el" href="block_8h.html#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a></li>
<li><a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a></li>
<li><a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a></li>
<li><a class="el" href="block_8h.html#a6c3e102d76db06a07126a569abc6e2bc" title="Validate and set Compressed Size according to Unpadded Size.">lzma_block_compressed_size()</a></li>
<li><a class="el" href="block_8h.html#a412d5605280fa29befae1b89e344bf30" title="Calculate Unpadded Size.">lzma_block_unpadded_size()</a></li>
<li><a class="el" href="block_8h.html#a694424f9dfdd5151e01debac1c501fa9" title="Calculate the total encoded size of a Block.">lzma_block_total_size()</a></li>
<li><a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a></li>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#af415fa5130ab64e8760e9c39e856fa54" title="Single-call .xz Block encoder.">lzma_block_buffer_encode()</a></li>
<li><a class="el" href="block_8h.html#a5a260f634ccd5f54fb98f570d8d92d8c" title="Single-call uncompressed .xz Block encoder.">lzma_block_uncomp_encode()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a></li>
</ul>
<p>Written by:</p><ul>
<li><a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a> </li>
</ul>

</div>
</div>
<a id="a6689c4f7524b2c05772a2d6151138610" name="a6689c4f7524b2c05772a2d6151138610"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6689c4f7524b2c05772a2d6151138610">&#9670;&#160;</a></span>header_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_block::header_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Size of the Block Header field in bytes. </p>
<p>This is always a multiple of four.</p>
<p>Read by:</p><ul>
<li><a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a></li>
<li><a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a></li>
<li><a class="el" href="block_8h.html#a6c3e102d76db06a07126a569abc6e2bc" title="Validate and set Compressed Size according to Unpadded Size.">lzma_block_compressed_size()</a></li>
<li><a class="el" href="block_8h.html#a412d5605280fa29befae1b89e344bf30" title="Calculate Unpadded Size.">lzma_block_unpadded_size()</a></li>
<li><a class="el" href="block_8h.html#a694424f9dfdd5151e01debac1c501fa9" title="Calculate the total encoded size of a Block.">lzma_block_total_size()</a></li>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a></li>
</ul>
<p>Written by:</p><ul>
<li><a class="el" href="block_8h.html#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a></li>
<li><a class="el" href="block_8h.html#af415fa5130ab64e8760e9c39e856fa54" title="Single-call .xz Block encoder.">lzma_block_buffer_encode()</a></li>
<li><a class="el" href="block_8h.html#a5a260f634ccd5f54fb98f570d8d92d8c" title="Single-call uncompressed .xz Block encoder.">lzma_block_uncomp_encode()</a> </li>
</ul>

</div>
</div>
<a id="a80cd9d3025991db4a476ce7588f853e6" name="a80cd9d3025991db4a476ce7588f853e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a80cd9d3025991db4a476ce7588f853e6">&#9670;&#160;</a></span>check</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> lzma_block::check</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Type of integrity Check. </p>
<p>The Check ID is not stored into the Block Header, thus its value must be provided also when decoding.</p>
<p>Read by:</p><ul>
<li><a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a></li>
<li><a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a></li>
<li><a class="el" href="block_8h.html#a6c3e102d76db06a07126a569abc6e2bc" title="Validate and set Compressed Size according to Unpadded Size.">lzma_block_compressed_size()</a></li>
<li><a class="el" href="block_8h.html#a412d5605280fa29befae1b89e344bf30" title="Calculate Unpadded Size.">lzma_block_unpadded_size()</a></li>
<li><a class="el" href="block_8h.html#a694424f9dfdd5151e01debac1c501fa9" title="Calculate the total encoded size of a Block.">lzma_block_total_size()</a></li>
<li><a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a></li>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#af415fa5130ab64e8760e9c39e856fa54" title="Single-call .xz Block encoder.">lzma_block_buffer_encode()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a> </li>
</ul>

</div>
</div>
<a id="a8383d489c9ffea8af390669a105c74e5" name="a8383d489c9ffea8af390669a105c74e5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8383d489c9ffea8af390669a105c74e5">&#9670;&#160;</a></span>compressed_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_block::compressed_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Size of the Compressed Data in bytes. </p>
<p>Encoding: If this is not LZMA_VLI_UNKNOWN, Block Header encoder will store this value to the Block Header. Block encoder doesn't care about this value, but will set it once the encoding has been finished.</p>
<p>Decoding: If this is not LZMA_VLI_UNKNOWN, Block decoder will verify that the size of the Compressed Data field matches compressed_size.</p>
<p>Usually you don't know this value when encoding in streamed mode, and thus cannot write this field into the Block Header.</p>
<p>In non-streamed mode you can reserve space for this field before encoding the actual Block. After encoding the data, finish the Block by encoding the Block Header. Steps in detail:</p>
<ul>
<li>Set compressed_size to some big enough value. If you don't know better, use LZMA_VLI_MAX, but remember that bigger values take more space in Block Header.</li>
<li>Call <a class="el" href="block_8h.html#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a> to see how much space you need to reserve for the Block Header.</li>
<li>Encode the Block using <a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a> and <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>. It sets compressed_size to the correct value.</li>
<li>Use <a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a> to encode the Block Header. Because space was reserved in the first step, you don't need to call <a class="el" href="block_8h.html#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a> anymore, because due to reserving, header_size has to be big enough. If it is "too big", <a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a> will add enough Header Padding to make Block Header to match the size specified by header_size.</li>
</ul>
<p>Read by:</p><ul>
<li><a class="el" href="block_8h.html#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a></li>
<li><a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a></li>
<li><a class="el" href="block_8h.html#a6c3e102d76db06a07126a569abc6e2bc" title="Validate and set Compressed Size according to Unpadded Size.">lzma_block_compressed_size()</a></li>
<li><a class="el" href="block_8h.html#a412d5605280fa29befae1b89e344bf30" title="Calculate Unpadded Size.">lzma_block_unpadded_size()</a></li>
<li><a class="el" href="block_8h.html#a694424f9dfdd5151e01debac1c501fa9" title="Calculate the total encoded size of a Block.">lzma_block_total_size()</a></li>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a></li>
</ul>
<p>Written by:</p><ul>
<li><a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a></li>
<li><a class="el" href="block_8h.html#a6c3e102d76db06a07126a569abc6e2bc" title="Validate and set Compressed Size according to Unpadded Size.">lzma_block_compressed_size()</a></li>
<li><a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a></li>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#af415fa5130ab64e8760e9c39e856fa54" title="Single-call .xz Block encoder.">lzma_block_buffer_encode()</a></li>
<li><a class="el" href="block_8h.html#a5a260f634ccd5f54fb98f570d8d92d8c" title="Single-call uncompressed .xz Block encoder.">lzma_block_uncomp_encode()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a> </li>
</ul>

</div>
</div>
<a id="a17362d38d1946dd16a9686557ec19a94" name="a17362d38d1946dd16a9686557ec19a94"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a17362d38d1946dd16a9686557ec19a94">&#9670;&#160;</a></span>uncompressed_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_block::uncompressed_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Uncompressed Size in bytes. </p>
<p>This is handled very similarly to compressed_size above.</p>
<p>uncompressed_size is needed by fewer functions than compressed_size. This is because uncompressed_size isn't needed to validate that Block stays within proper limits.</p>
<p>Read by:</p><ul>
<li><a class="el" href="block_8h.html#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a></li>
<li><a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a></li>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a></li>
</ul>
<p>Written by:</p><ul>
<li><a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a></li>
<li><a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a></li>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#af415fa5130ab64e8760e9c39e856fa54" title="Single-call .xz Block encoder.">lzma_block_buffer_encode()</a></li>
<li><a class="el" href="block_8h.html#a5a260f634ccd5f54fb98f570d8d92d8c" title="Single-call uncompressed .xz Block encoder.">lzma_block_uncomp_encode()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a> </li>
</ul>

</div>
</div>
<a id="a5900e517e6e0a473a3184074ae7defd1" name="a5900e517e6e0a473a3184074ae7defd1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5900e517e6e0a473a3184074ae7defd1">&#9670;&#160;</a></span>filters</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structlzma__filter.html">lzma_filter</a>* lzma_block::filters</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Array of filters. </p>
<p>There can be 1-4 filters. The end of the array is marked with .id = LZMA_VLI_UNKNOWN.</p>
<p>Read by:</p><ul>
<li><a class="el" href="block_8h.html#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a></li>
<li><a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a></li>
<li><a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a></li>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#af415fa5130ab64e8760e9c39e856fa54" title="Single-call .xz Block encoder.">lzma_block_buffer_encode()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a></li>
</ul>
<p>Written by:</p><ul>
<li><a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a>: Note that this does NOT free() the old filter options structures. All unused filters[] will have .id == LZMA_VLI_UNKNOWN and .options == NULL. If decoding fails, all filters[] are guaranteed to be LZMA_VLI_UNKNOWN and NULL.</li>
</ul>
<dl class="section note"><dt>Note</dt><dd>Because of the array is terminated with .id = LZMA_VLI_UNKNOWN, the actual array must have LZMA_FILTERS_MAX + 1 members or the Block Header decoder will overflow the buffer. </dd></dl>

</div>
</div>
<a id="a25e9bf1bb1699017694b18ca24f965d2" name="a25e9bf1bb1699017694b18ca24f965d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a25e9bf1bb1699017694b18ca24f965d2">&#9670;&#160;</a></span>raw_check</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t lzma_block::raw_check[<a class="el" href="check_8h.html#a379e931cf86351ab1d97896cda9abbe0">LZMA_CHECK_SIZE_MAX</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Raw value stored in the Check field. </p>
<p>After successful coding, the first lzma_check_size(check) bytes of this array contain the raw value stored in the Check field.</p>
<p>Note that CRC32 and CRC64 are stored in little endian byte order. Take it into account if you display the Check values to the user.</p>
<p>Written by:</p><ul>
<li><a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a></li>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#af415fa5130ab64e8760e9c39e856fa54" title="Single-call .xz Block encoder.">lzma_block_buffer_encode()</a></li>
<li><a class="el" href="block_8h.html#a5a260f634ccd5f54fb98f570d8d92d8c" title="Single-call uncompressed .xz Block encoder.">lzma_block_uncomp_encode()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a> </li>
</ul>

</div>
</div>
<a id="a516ac9cc63bc1a4fadd9fbfc189a206b" name="a516ac9cc63bc1a4fadd9fbfc189a206b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a516ac9cc63bc1a4fadd9fbfc189a206b">&#9670;&#160;</a></span>ignore_check</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> lzma_block::ignore_check</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>A flag to Block decoder to not verify the Check field. </p>
<p>This member is supported by liblzma &gt;= 5.1.4beta if .version &gt;= 1.</p>
<p>If this is set to true, the integrity check won't be calculated and verified. Unless you know what you are doing, you should leave this to false. (A reason to set this to true is when the file integrity is verified externally anyway and you want to speed up the decompression, which matters mostly when using SHA-256 as the integrity check.)</p>
<p>If .version &gt;= 1, read by:</p><ul>
<li><a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a></li>
<li><a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f" title="Single-call .xz Block decoder.">lzma_block_buffer_decode()</a></li>
</ul>
<p>Written by (.version is ignored):</p><ul>
<li><a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a> always sets this to false </li>
</ul>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="block_8h.html">block.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structlzma__block.html">lzma_block</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
