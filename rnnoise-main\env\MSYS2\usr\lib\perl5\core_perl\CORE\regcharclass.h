/* -*- mode: C; buffer-read-only: t -*-
 *
 *    regcharclass.h
 *
 *    Copyright (C) 2007, 2011 by <PERSON> and others
 *
 *    You may distribute under the terms of either the GNU General Public
 *    License or the Artistic License, as specified in the README file.
 *
 * !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
 * This file is built by regen/regcharclass.pl.
 * Any changes made here will be lost!
 * WARNING: These macros are for internal Perl core use only, and may be
 * changed or removed without notice.
 */


#ifndef PERL_REGCHARCLASS_H_ /* Guard against nested #includes */
#define PERL_REGCHARCLASS_H_

#if 'A' == 65 /* ASCII/Latin1 */
/*
	LNBREAK: Line Break: \R

	"\x0D\x0A"      # CRLF - Network (Windows) line ending
	\p{VertSpace}
*/
/*** GENERATED CODE ***/
#define is_LNBREAK_safe(s,e,is_utf8)                                        \
( ((e)-(s) > 2) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\n', '\f') ) ? 1            \
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : ( is_utf8 ) ?                                                         \
	( ( 0xC2 == ((const U8*)s)[0] ) ?                                   \
	    ( ( 0x85 == ((const U8*)s)[1] ) ? 2 : 0 )                       \
	: ( ( ( 0xE2 == ((const U8*)s)[0] ) && ( 0x80 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xA8, 0xA9) ) ) ? 3 : 0 )\
    : ( 0x85 == ((const U8*)s)[0] ) )                                       \
: ((e)-(s) > 1) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\n', '\f') ) ? 1            \
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : ( is_utf8 ) ?                                                         \
	( ( ( 0xC2 == ((const U8*)s)[0] ) && ( 0x85 == ((const U8*)s)[1] ) ) ? 2 : 0 )\
    : ( 0x85 == ((const U8*)s)[0] ) )                                       \
: ((e)-(s) > 0) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\n', '\r') ) ? 1            \
    : ( !( is_utf8 ) ) ?                                                    \
	( 0x85 == ((const U8*)s)[0] )                                       \
    : 0 )                                                                   \
: 0 )

/*** GENERATED CODE ***/
#define is_LNBREAK_utf8_safe(s,e)                                           \
( ((e)-(s) > 2) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\n', '\f') ) ? 1            \
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : ( 0xC2 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x85 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( ( ( 0xE2 == ((const U8*)s)[0] ) && ( 0x80 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xA8, 0xA9) ) ) ? 3 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\n', '\f') ) ? 1            \
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : ( ( 0xC2 == ((const U8*)s)[0] ) && ( 0x85 == ((const U8*)s)[1] ) ) ? 2 : 0 )\
: ((e)-(s) > 0) ?                                                           \
    ( inRANGE_helper_(U8, ((const U8*)s)[0], '\n', '\r') )                  \
: 0 )

/*** GENERATED CODE ***/
#define is_LNBREAK_latin1_safe(s,e)                                         \
( ((e)-(s) > 1) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\n', '\f') || 0x85 == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( inRANGE_helper_(U8, ((const U8*)s)[0], '\n', '\r') || 0x85 == ((const U8*)s)[0] )\
: 0 )

/*
	HORIZWS: Horizontal Whitespace: \h \H

	\p{HorizSpace}
*/
/*** GENERATED CODE ***/
#define is_HORIZWS_high(s)                                                  \
( ( 0xE1 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x9A == ((const U8*)s)[1] ) && ( 0x80 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xE2 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x80 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0x8A) || 0xAF == ((const U8*)s)[2] ) ? 3 : 0 )\
    : ( ( 0x81 == ((const U8*)s)[1] ) && ( 0x9F == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( ( ( 0xE3 == ((const U8*)s)[0] ) && ( 0x80 == ((const U8*)s)[1] ) ) && ( 0x80 == ((const U8*)s)[2] ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_HORIZWS_cp_high(cp)                                              \
( 0x1680 == cp || ( 0x1680 < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x2000, 0x200A) || ( 0x200A < cp &&               \
( 0x202F == cp || ( 0x202F < cp &&                                          \
( 0x205F == cp || 0x3000 == cp ) ) ) ) ) ) )

/*
	VERTWS: Vertical Whitespace: \v \V

	\p{VertSpace}
*/
/*** GENERATED CODE ***/
#define is_VERTWS_high(s)                                                   \
( ( ( ( 0xE2 == ((const U8*)s)[0] ) && ( 0x80 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xA8, 0xA9) ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_VERTWS_cp_high(cp)                                               \
( inRANGE_helper_(UV, cp, 0x2028, 0x2029) )

/*
	XDIGIT: Hexadecimal digits

	\p{XDigit}
*/
/*** GENERATED CODE ***/
#define is_XDIGIT_high(s)                                                   \
( ( 0xEF == ((const U8*)s)[0] ) ?                                           \
    ( ( 0xBC == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x90, 0x99) || inRANGE_helper_(U8, ((const U8*)s)[2], 0xA1, 0xA6) ) ? 3 : 0 )\
    : ( ( 0xBD == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x81, 0x86) ) ) ? 3 : 0 )\
: 0 )

/*** GENERATED CODE ***/
#define is_XDIGIT_cp_high(cp)                                               \
( inRANGE_helper_(UV, cp, 0xFF10, 0xFF19) || ( 0xFF19 < cp &&               \
( inRANGE_helper_(UV, cp, 0xFF21, 0xFF26) || inRANGE_helper_(UV, cp, 0xFF41, 0xFF46) ) ) )

/*
	XPERLSPACE: \p{XPerlSpace}

	\p{XPerlSpace}
*/
/*** GENERATED CODE ***/
#define is_XPERLSPACE_high(s)                                               \
( ( 0xE1 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x9A == ((const U8*)s)[1] ) && ( 0x80 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xE2 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x80 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0x8A) || inRANGE_helper_(U8, ((const U8*)s)[2], 0xA8, 0xA9) || 0xAF == ((const U8*)s)[2] ) ? 3 : 0 )\
    : ( ( 0x81 == ((const U8*)s)[1] ) && ( 0x9F == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( ( ( 0xE3 == ((const U8*)s)[0] ) && ( 0x80 == ((const U8*)s)[1] ) ) && ( 0x80 == ((const U8*)s)[2] ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_XPERLSPACE_cp_high(cp)                                           \
( 0x1680 == cp || ( 0x1680 < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x2000, 0x200A) || ( 0x200A < cp &&               \
( inRANGE_helper_(UV, cp, 0x2028, 0x2029) || ( 0x2029 < cp &&               \
( 0x202F == cp || ( 0x202F < cp &&                                          \
( 0x205F == cp || 0x3000 == cp ) ) ) ) ) ) ) ) )

/*
	SPACE: Backwards \p{XPerlSpace}

	\p{XPerlSpace}
*/
/*** GENERATED CODE ***/
#define is_SPACE_utf8_safe_backwards(s,e)                                   \
( ((s) - (e) > 2) ?                                                         \
    ( ( inRANGE_helper_(U8, *((const U8*)s - 1), '\t', '\r') || ' ' == *((const U8*)s - 1) ) ? 1\
    : ( 0x80 == *((const U8*)s - 1) ) ?                                     \
	( ( 0x80 == *((const U8*)s - 2) ) ?                                 \
	    ( ( inRANGE_helper_(U8, *((const U8*)s - 3), 0xE2, 0xE3) ) ? 3 : 0 )\
	: ( ( 0x9A == *((const U8*)s - 2) ) && ( 0xE1 == *((const U8*)s - 3) ) ) ? 3 : 0 )\
    : ( inRANGE_helper_(U8, *((const U8*)s - 1), 0x81, 0x84) || inRANGE_helper_(U8, *((const U8*)s - 1), 0x86, 0x8A) || inRANGE_helper_(U8, *((const U8*)s - 1), 0xA8, 0xA9) || 0xAF == *((const U8*)s - 1) ) ?\
	( ( ( 0x80 == *((const U8*)s - 2) ) && ( 0xE2 == *((const U8*)s - 3) ) ) ? 3 : 0 )\
    : ( 0x85 == *((const U8*)s - 1) ) ?                                     \
	( ( 0x80 == *((const U8*)s - 2) ) ?                                 \
	    ( ( 0xE2 == *((const U8*)s - 3) ) ? 3 : 0 )                     \
	: ( 0xC2 == *((const U8*)s - 2) ) ? 2 : 0 )                         \
    : ( 0x9F == *((const U8*)s - 1) ) ?                                     \
	( ( ( 0x81 == *((const U8*)s - 2) ) && ( 0xE2 == *((const U8*)s - 3) ) ) ? 3 : 0 )\
    : ( ( 0xA0 == *((const U8*)s - 1) ) && ( 0xC2 == *((const U8*)s - 2) ) ) ? 2 : 0 )\
: ((s) - (e) > 1) ?                                                         \
    ( ( inRANGE_helper_(U8, *((const U8*)s - 1), '\t', '\r') || ' ' == *((const U8*)s - 1) ) ? 1\
    : ( ( 0x85 == *((const U8*)s - 1) || 0xA0 == *((const U8*)s - 1) ) && ( 0xC2 == *((const U8*)s - 2) ) ) ? 2 : 0 )\
: ((s) - (e) > 0) ?                                                         \
    ( inRANGE_helper_(U8, *((const U8*)s - 1), '\t', '\r') || ' ' == *((const U8*)s - 1) )\
: 0 )

/*
	NONCHAR: Non character code points

	\p{_Perl_Nchar}
*/
/*** GENERATED CODE ***/
#define is_NONCHAR_utf8_safe(s,e)                                           \
( ( ( LIKELY((e) > (s)) ) && ( LIKELY(((e) - (s)) >= UTF8SKIP(s)) ) ) ? ( ( 0xEF == ((const U8*)s)[0] ) ?\
	    ( ( 0xB7 == ((const U8*)s)[1] ) ?                               \
		( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x90, 0xAF) ) ? 3 : 0 )\
	    : ( ( 0xBF == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xBE, 0xBF) ) ) ? 3 : 0 )\
	: ( 0xF0 == ((const U8*)s)[0] ) ?                                   \
	    ( ( ( ( ((const U8*)s)[1] == 0x9F || ( ( ((const U8*)s)[1] & 0xEF ) == 0xAF ) ) && ( 0xBF == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0xBE, 0xBF) ) ) ? 4 : 0 )\
	: ( inRANGE_helper_(U8, ((const U8*)s)[0], 0xF1, 0xF3) ) ?          \
	    ( ( ( ( ( ((const U8*)s)[1] & 0xCF ) == 0x8F ) && ( 0xBF == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0xBE, 0xBF) ) ) ? 4 : 0 )\
	: ( ( ( ( 0xF4 == ((const U8*)s)[0] ) && ( 0x8F == ((const U8*)s)[1] ) ) && ( 0xBF == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0xBE, 0xBF) ) ) ? 4 : 0 ) : 0 )

/*
	SHORTER_NON_CHARS: # 3 bytes

	0xFDD0 - 0xFDEF
	0xFFFE - 0xFFFF
*/
/*** GENERATED CODE ***/
#define is_SHORTER_NON_CHARS_utf8(s)                                        \
( ( 0xEF == ((const U8*)s)[0] ) ?                                           \
    ( ( 0xB7 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x90, 0xAF) ) ? 3 : 0 )  \
    : ( ( 0xBF == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xBE, 0xBF) ) ) ? 3 : 0 )\
: 0 )

/*
	LARGER_NON_CHARS: # 4 bytes

	0x1FFFE - 0x1FFFF
	0x2FFFE - 0x2FFFF
	0x3FFFE - 0x3FFFF
	0x4FFFE - 0x4FFFF
	0x5FFFE - 0x5FFFF
	0x6FFFE - 0x6FFFF
	0x7FFFE - 0x7FFFF
	0x8FFFE - 0x8FFFF
	0x9FFFE - 0x9FFFF
	0xAFFFE - 0xAFFFF
	0xBFFFE - 0xBFFFF
	0xCFFFE - 0xCFFFF
	0xDFFFE - 0xDFFFF
	0xEFFFE - 0xEFFFF
	0xFFFFE - 0xFFFFF
	0x10FFFE - 0x10FFFF
*/
/*** GENERATED CODE ***/
#define is_LARGER_NON_CHARS_utf8(s)                                         \
( ( 0xF0 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( ( ((const U8*)s)[1] == 0x9F || ( ( ((const U8*)s)[1] & 0xEF ) == 0xAF ) ) && ( 0xBF == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0xBE, 0xBF) ) ) ? 4 : 0 )\
: ( inRANGE_helper_(U8, ((const U8*)s)[0], 0xF1, 0xF3) ) ?                  \
    ( ( ( ( ( ((const U8*)s)[1] & 0xCF ) == 0x8F ) && ( 0xBF == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0xBE, 0xBF) ) ) ? 4 : 0 )\
: ( ( ( ( 0xF4 == ((const U8*)s)[0] ) && ( 0x8F == ((const U8*)s)[1] ) ) && ( 0xBF == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0xBE, 0xBF) ) ) ? 4 : 0 )

/*
	SURROGATE: Surrogate code points

	\p{_Perl_Surrogate}
*/
/*** GENERATED CODE ***/
#define is_SURROGATE_utf8(s)                                                \
( ( ( 0xED == ((const U8*)s)[0] ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 0xA0, 0xBF) ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_SURROGATE_utf8_safe(s,e)                                         \
( ( ( ( ( ((e) - (s)) >= 3 ) && ( 0xED == ((const U8*)s)[0] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 0xA0, 0xBF) ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0xBF) ) ) ? 3 : 0 )

/*
	QUOTEMETA: Meta-characters that \Q should quote

	\p{_Perl_Quotemeta}
*/
/*** GENERATED CODE ***/
#define is_QUOTEMETA_high(s)                                                \
( ( 0xCD == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x8F == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xD8 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x9C == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xE1 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x85 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x9F, 0xA0) ) ? 3 : 0 )  \
    : ( 0x9A == ((const U8*)s)[1] ) ?                                       \
	( ( 0x80 == ((const U8*)s)[2] ) ? 3 : 0 )                           \
    : ( 0x9E == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xB4, 0xB5) ) ? 3 : 0 )  \
    : ( ( 0xA0 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x8B, 0x8F) ) ) ? 3 : 0 )\
: ( 0xE2 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x80 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0xBE) ) ? 3 : 0 )  \
    : ( 0x81 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x81, 0x93) || inRANGE_helper_(U8, ((const U8*)s)[2], 0x95, 0xAF) ) ? 3 : 0 )\
    : ( 0x86 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x90, 0xBF) ) ? 3 : 0 )  \
    : ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x87, 0x90) || inRANGE_helper_(U8, ((const U8*)s)[1], 0x94, 0x9C) || inRANGE_helper_(U8, ((const U8*)s)[1], 0x9F, 0xAF) || inRANGE_helper_(U8, ((const U8*)s)[1], 0xB8, 0xB9) ) ?\
	3                                                                   \
    : ( 0x91 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0x9F) ) ? 3 : 0 )  \
    : ( 0x9D == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0xB5) ) ? 3 : 0 )  \
    : ( ( 0x9E == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x94, 0xBF) ) ) ? 3 : 0 )\
: ( 0xE3 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x80 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0x83) || inRANGE_helper_(U8, ((const U8*)s)[2], 0x88, 0xA0) || 0xB0 == ((const U8*)s)[2] ) ? 3 : 0 )\
    : ( ( 0x85 == ((const U8*)s)[1] ) && ( 0xA4 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xEF == ((const U8*)s)[0] ) ?                                           \
    ( ( 0xB4 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xBE, 0xBF) ) ? 3 : 0 )  \
    : ( 0xB8 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0x8F) ) ? 3 : 0 )  \
    : ( 0xB9 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x85, 0x86) ) ? 3 : 0 )  \
    : ( 0xBB == ((const U8*)s)[1] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[2] ) ? 3 : 0 )                           \
    : ( 0xBE == ((const U8*)s)[1] ) ?                                       \
	( ( 0xA0 == ((const U8*)s)[2] ) ? 3 : 0 )                           \
    : ( ( 0xBF == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xB0, 0xB8) ) ) ? 3 : 0 )\
: ( 0xF0 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x9B == ((const U8*)s)[1] ) ?                                       \
	( ( ( 0xB2 == ((const U8*)s)[2] ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0xA0, 0xA3) ) ) ? 4 : 0 )\
    : ( ( ( 0x9D == ((const U8*)s)[1] ) && ( 0x85 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0xB3, 0xBA) ) ) ? 4 : 0 )\
: ( ( 0xF3 == ((const U8*)s)[0] ) && ( 0xA0 == ((const U8*)s)[1] ) ) ? 4 : 0 )

/*
	MULTI_CHAR_FOLD: multi-char strings that are folded to by a single character

	%regcharclass_multi_char_folds::multi_char_folds('u', 'a')
*/
/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e)                            \
( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                                 \
	    ( ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ) ? 2 : 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e)                            \
( ( ( ((const U8*)s)[0] & 0xDF ) == 'H' ) ?                                 \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'I' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x87 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8C == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[1], 's', 't') ) ? 2\
	: ( ( 0xC5 == ((const U8*)s)[1] ) && ( 0xBF == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x88 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xDF ) == 'W' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ) ?\
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ?                                   \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[2], 's', 't') ) ? 3\
	    : ( ( 0xC5 == ((const U8*)s)[2] ) && ( 0xBF == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: 0 )                                                               \
    : ( 0xCA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0xBC == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xDF ) == 'N' ) ) ? 3 : 0 )\
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( ( ((const U8*)s)[1] & 0xFD ) == 0xAC ) ?                        \
	    ( ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0xB1 == ((const U8*)s)[1] || 0xB7 == ((const U8*)s)[1] ) ?      \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xCE == ((const U8*)s)[4] ) && ( 0xB9 == ((const U8*)s)[5] ) ) ? 6 : 4 )\
		: 0 )                                                       \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0xB9 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCC == ((const U8*)s)[2] ) ?                               \
		( ( 0x88 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xCC == ((const U8*)s)[4] ) ?                       \
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x80, 0x81) ) ? 6 : 0 )\
		    : ( ( 0xCD == ((const U8*)s)[4] ) && ( 0x82 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
		: 0 )                                                       \
	    : ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: 0 )                                                               \
    : ( 0xCF == ((const U8*)s)[0] ) ?                                       \
	( ( 0x81 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCC == ((const U8*)s)[2] ) && ( 0x93 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x85 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCC == ((const U8*)s)[2] ) ?                               \
		( ( 0x88 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xCC == ((const U8*)s)[4] ) ?                       \
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x80, 0x81) ) ? 6 : 0 )\
		    : ( ( 0xCD == ((const U8*)s)[4] ) && ( 0x82 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
		: ( 0x93 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xCC == ((const U8*)s)[4] ) ?                       \
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x80, 0x81) ) ? 6 : 4 )\
		    : ( ( 0xCD == ((const U8*)s)[4] ) && ( 0x82 == ((const U8*)s)[5] ) ) ? 6 : 4 )\
		: 0 )                                                       \
	    : ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x89 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xCE == ((const U8*)s)[4] ) && ( 0xB9 == ((const U8*)s)[5] ) ) ? 6 : 4 )\
		: 0 )                                                       \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x8E == ((const U8*)s)[1] ) && ( 0xCE == ((const U8*)s)[2] ) ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xD5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xA5 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xD6 == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0xB4 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xD5 == ((const U8*)s)[2] ) && ( ( ( ((const U8*)s)[3] & 0xF7 ) == 0xA5 ) || ((const U8*)s)[3] == 0xAB || ((const U8*)s)[3] == 0xB6 ) ) ? 4 : 0 )\
	: ( ( ( 0xBE == ((const U8*)s)[1] ) && ( 0xD5 == ((const U8*)s)[2] ) ) && ( 0xB6 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xE1 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBC == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ( ( ((const U8*)s)[2] & 0xD8 ) == 0x80 ) && ( 0xCE == ((const U8*)s)[3] ) ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
	: ( ( ( ( 0xBD == ((const U8*)s)[1] ) && ( ( ( ((const U8*)s)[2] & 0xF8 ) == 0xA0 ) || ( ( ((const U8*)s)[2] & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0xBC ) ) && ( 0xCE == ((const U8*)s)[3] ) ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
    : 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e)                            \
( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) ?                                 \
	( ( ( 0xCA == ((const U8*)s)[1] ) && ( 0xBE == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'H' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'I' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x87 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8C == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[1], 's', 't') ) ? 2\
	: ( ( 0xC5 == ((const U8*)s)[1] ) && ( 0xBF == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x88 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xDF ) == 'W' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ) ?\
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ?                                   \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[2], 's', 't') ) ? 3\
	    : ( ( 0xC5 == ((const U8*)s)[2] ) && ( 0xBF == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: 0 )                                                               \
    : ( 0xCA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0xBC == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xDF ) == 'N' ) ) ? 3 : 0 )\
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( ( ((const U8*)s)[1] & 0xFD ) == 0xAC ) ?                        \
	    ( ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0xB1 == ((const U8*)s)[1] || 0xB7 == ((const U8*)s)[1] ) ?      \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0xB9 == ((const U8*)s)[1] ) && ( 0xCD == ((const U8*)s)[2] ) ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xCF == ((const U8*)s)[0] ) ?                                       \
	( ( 0x81 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCC == ((const U8*)s)[2] ) && ( 0x93 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x85 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCC == ((const U8*)s)[2] ) ?                               \
		( ( 0x93 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x89 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x8E == ((const U8*)s)[1] ) && ( 0xCE == ((const U8*)s)[2] ) ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xD5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xA5 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xD6 == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0xB4 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xD5 == ((const U8*)s)[2] ) && ( ( ( ((const U8*)s)[3] & 0xF7 ) == 0xA5 ) || ((const U8*)s)[3] == 0xAB || ((const U8*)s)[3] == 0xB6 ) ) ? 4 : 0 )\
	: ( ( ( 0xBE == ((const U8*)s)[1] ) && ( 0xD5 == ((const U8*)s)[2] ) ) && ( 0xB6 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e)                            \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) ?                             \
	( ( ( 0xCA == ((const U8*)s)[1] ) && ( 0xBE == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'H' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'I' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x87 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8C == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[1], 's', 't') ) ? 2\
	: ( ( 0xC5 == ((const U8*)s)[1] ) && ( 0xBF == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x88 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xDF ) == 'W' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ) ?\
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0xBF == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[2], 's', 't') ) ) ? 3 : 0 )\
    : ( ( ( 0xCA == ((const U8*)s)[0] ) && ( 0xBC == ((const U8*)s)[1] ) ) && ( ( ((const U8*)s)[2] & 0xDF ) == 'N' ) ) ? 3 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) || ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ) ? 2 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[1], 's', 't') ) ) ? 2 : 0 )\
: 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe(s,e)                                   \
( ((e)-(s) > 5) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) ?                             \
	( ( ( 0xCA == ((const U8*)s)[1] ) && ( 0xBE == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ? is_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e) : is_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e) )\
: ((e)-(s) > 4) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) ?                             \
	( ( ( 0xCA == ((const U8*)s)[1] ) && ( 0xBE == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'H' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'I' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x87 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8C == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[1], 's', 't') ) ? 2\
	: ( ( 0xC5 == ((const U8*)s)[1] ) && ( 0xBF == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x88 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xDF ) == 'W' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ) ?\
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ?                                   \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[2], 's', 't') ) ? 3\
	    : ( ( 0xC5 == ((const U8*)s)[2] ) && ( 0xBF == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: 0 )                                                               \
    : ( 0xCA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0xBC == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xDF ) == 'N' ) ) ? 3 : 0 )\
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( ( ((const U8*)s)[1] & 0xFD ) == 0xAC ) ?                        \
	    ( ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0xB1 == ((const U8*)s)[1] || 0xB7 == ((const U8*)s)[1] ) ?      \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0xB9 == ((const U8*)s)[1] ) && ( 0xCD == ((const U8*)s)[2] ) ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xCF == ((const U8*)s)[0] ) ?                                       \
	( ( 0x81 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCC == ((const U8*)s)[2] ) && ( 0x93 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x85 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCC == ((const U8*)s)[2] ) ?                               \
		( ( 0x93 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x89 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x8E == ((const U8*)s)[1] ) && ( 0xCE == ((const U8*)s)[2] ) ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xD5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xA5 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xD6 == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0xB4 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xD5 == ((const U8*)s)[2] ) && ( ( ( ((const U8*)s)[3] & 0xF7 ) == 0xA5 ) || ((const U8*)s)[3] == 0xAB || ((const U8*)s)[3] == 0xB6 ) ) ? 4 : 0 )\
	: ( ( ( 0xBE == ((const U8*)s)[1] ) && ( 0xD5 == ((const U8*)s)[2] ) ) && ( 0xB6 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xE1 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBC == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ( ( ((const U8*)s)[2] & 0xD8 ) == 0x80 ) && ( 0xCE == ((const U8*)s)[3] ) ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
	: ( ( ( ( 0xBD == ((const U8*)s)[1] ) && ( ( ( ((const U8*)s)[2] & 0xF8 ) == 0xA0 ) || ( ( ((const U8*)s)[2] & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0xBC ) ) && ( 0xCE == ((const U8*)s)[3] ) ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
    : 0 )                                                                   \
: ((e)-(s) > 3) ? is_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e) : is_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e) )

/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e)                          \
( ( 0x81 == ((const U8*)s)[1] ) ?                                           \
	    ( ( ( 0xCC == ((const U8*)s)[2] ) && ( 0x93 == ((const U8*)s)[3] ) ) ? 0x1FE4 : 0 )\
	: ( 0x85 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCC == ((const U8*)s)[2] ) ?                               \
		( ( 0x88 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xCC == ((const U8*)s)[4] ) ?                       \
			( ( 0x80 == ((const U8*)s)[5] ) ? 0x1FE2            \
			: ( 0x81 == ((const U8*)s)[5] ) ? 0x3B0 : 0 )       \
		    : ( ( 0xCD == ((const U8*)s)[4] ) && ( 0x82 == ((const U8*)s)[5] ) ) ? 0x1FE7 : 0 )\
		: ( 0x93 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xCC == ((const U8*)s)[4] ) ?                       \
			( ( 0x80 == ((const U8*)s)[5] ) ? 0x1F52            \
			: ( 0x81 == ((const U8*)s)[5] ) ? 0x1F54 : 0x1F50 ) \
		    : ( ( 0xCD == ((const U8*)s)[4] ) && ( 0x82 == ((const U8*)s)[5] ) ) ? 0x1F56 : 0x1F50 )\
		: 0 )                                                       \
	    : ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 0x1FE6 : 0 )\
	: ( 0x89 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xCE == ((const U8*)s)[4] ) && ( 0xB9 == ((const U8*)s)[5] ) ) ? 0x1FF7 : 0x1FF6 )\
		: 0 )                                                       \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FF3 : 0 )\
	: ( ( ( 0x8E == ((const U8*)s)[1] ) && ( 0xCE == ((const U8*)s)[2] ) ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FF4 : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e)                          \
( ( 0xD5 == ((const U8*)s)[0] ) ?                                           \
	( ( 0xA5 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xD6 == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 0x587 : 0 )\
	: ( 0xB4 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xD5 == ((const U8*)s)[2] ) ?                               \
		( ( 0xA5 == ((const U8*)s)[3] ) ? 0xFB14                    \
		: ( 0xAB == ((const U8*)s)[3] ) ? 0xFB15                    \
		: ( 0xAD == ((const U8*)s)[3] ) ? 0xFB17                    \
		: ( 0xB6 == ((const U8*)s)[3] ) ? 0xFB13 : 0 )              \
	    : 0 )                                                           \
	: ( ( ( 0xBE == ((const U8*)s)[1] ) && ( 0xD5 == ((const U8*)s)[2] ) ) && ( 0xB6 == ((const U8*)s)[3] ) ) ? 0xFB16 : 0 )\
    : ( 0xE1 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBC == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x80 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F80 : 0 )\
	    : ( 0x81 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F81 : 0 )\
	    : ( 0x82 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F82 : 0 )\
	    : ( 0x83 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F83 : 0 )\
	    : ( 0x84 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F84 : 0 )\
	    : ( 0x85 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F85 : 0 )\
	    : ( 0x86 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F86 : 0 )\
	    : ( 0x87 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F87 : 0 )\
	    : ( 0xA0 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F90 : 0 )\
	    : ( 0xA1 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F91 : 0 )\
	    : ( 0xA2 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F92 : 0 )\
	    : ( 0xA3 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F93 : 0 )\
	    : ( 0xA4 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F94 : 0 )\
	    : ( 0xA5 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F95 : 0 )\
	    : ( 0xA6 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F96 : 0 )\
	    : ( ( ( 0xA7 == ((const U8*)s)[2] ) && ( 0xCE == ((const U8*)s)[3] ) ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F97 : 0 )\
	: ( 0xBD == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xA0 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA0 : 0 )\
	    : ( 0xA1 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA1 : 0 )\
	    : ( 0xA2 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA2 : 0 )\
	    : ( 0xA3 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA3 : 0 )\
	    : ( 0xA4 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA4 : 0 )\
	    : ( 0xA5 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA5 : 0 )\
	    : ( 0xA6 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA6 : 0 )\
	    : ( 0xA7 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA7 : 0 )\
	    : ( 0xB0 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FB2 : 0 )\
	    : ( 0xB4 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FC2 : 0 )\
	    : ( ( ( 0xBC == ((const U8*)s)[2] ) && ( 0xCE == ((const U8*)s)[3] ) ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FF2 : 0 )\
	: 0 )                                                               \
    : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e)                          \
( ( 0x81 == ((const U8*)s)[1] ) ?                                           \
	    ( ( ( 0xCC == ((const U8*)s)[2] ) && ( 0x93 == ((const U8*)s)[3] ) ) ? 0x1FE4 : 0 )\
	: ( 0x85 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCC == ((const U8*)s)[2] ) ?                               \
		( ( 0x93 == ((const U8*)s)[3] ) ? 0x1F50 : 0 )              \
	    : ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 0x1FE6 : 0 )\
	: ( 0x89 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 0x1FF6 : 0 )              \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FF3 : 0 )\
	: ( ( ( 0x8E == ((const U8*)s)[1] ) && ( 0xCE == ((const U8*)s)[2] ) ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FF4 : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e)                          \
( ( 0xD5 == ((const U8*)s)[0] ) ?                                           \
	( ( 0xA5 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xD6 == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 0x587 : 0 )\
	: ( 0xB4 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xD5 == ((const U8*)s)[2] ) ?                               \
		( ( 0xA5 == ((const U8*)s)[3] ) ? 0xFB14                    \
		: ( 0xAB == ((const U8*)s)[3] ) ? 0xFB15                    \
		: ( 0xAD == ((const U8*)s)[3] ) ? 0xFB17                    \
		: ( 0xB6 == ((const U8*)s)[3] ) ? 0xFB13 : 0 )              \
	    : 0 )                                                           \
	: ( ( ( 0xBE == ((const U8*)s)[1] ) && ( 0xD5 == ((const U8*)s)[2] ) ) && ( 0xB6 == ((const U8*)s)[3] ) ) ? 0xFB16 : 0 )\
    : ( 0xE1 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBC == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x80 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F80 : 0 )\
	    : ( 0x81 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F81 : 0 )\
	    : ( 0x82 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F82 : 0 )\
	    : ( 0x83 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F83 : 0 )\
	    : ( 0x84 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F84 : 0 )\
	    : ( 0x85 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F85 : 0 )\
	    : ( 0x86 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F86 : 0 )\
	    : ( 0x87 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F87 : 0 )\
	    : ( 0xA0 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F90 : 0 )\
	    : ( 0xA1 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F91 : 0 )\
	    : ( 0xA2 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F92 : 0 )\
	    : ( 0xA3 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F93 : 0 )\
	    : ( 0xA4 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F94 : 0 )\
	    : ( 0xA5 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F95 : 0 )\
	    : ( 0xA6 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F96 : 0 )\
	    : ( ( ( 0xA7 == ((const U8*)s)[2] ) && ( 0xCE == ((const U8*)s)[3] ) ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1F97 : 0 )\
	: ( 0xBD == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xA0 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA0 : 0 )\
	    : ( 0xA1 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA1 : 0 )\
	    : ( 0xA2 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA2 : 0 )\
	    : ( 0xA3 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA3 : 0 )\
	    : ( 0xA4 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA4 : 0 )\
	    : ( 0xA5 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA5 : 0 )\
	    : ( 0xA6 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA6 : 0 )\
	    : ( 0xA7 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FA7 : 0 )\
	    : ( 0xB0 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FB2 : 0 )\
	    : ( 0xB4 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xCE == ((const U8*)s)[3] ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FC2 : 0 )\
	    : ( ( ( 0xBC == ((const U8*)s)[2] ) && ( 0xCE == ((const U8*)s)[3] ) ) && ( 0xB9 == ((const U8*)s)[4] ) ) ? 0x1FF2 : 0 )\
	: 0 )                                                               \
    : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part4_(s,e)                          \
( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) ?                                 \
	( ( ( 0xCA == ((const U8*)s)[1] ) && ( 0xBE == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'H' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'I' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x87 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8C == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'S' ) ? 0xDF                    \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'T' ) ? 0xFB05                  \
	: ( ( 0xC5 == ((const U8*)s)[1] ) && ( 0xBF == ((const U8*)s)[2] ) ) ? 0xDF : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x88 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'W' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ((const U8*)s)[2] & 0xDF ) == 'S' ) ? 0xDF                \
	    : ( ( ((const U8*)s)[2] & 0xDF ) == 'T' ) ? 0xFB05              \
	    : ( ( 0xC5 == ((const U8*)s)[2] ) && ( 0xBF == ((const U8*)s)[3] ) ) ? 0xDF : 0 )\
	: 0 )                                                               \
    : ( 0xCA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0xBC == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xDF ) == 'N' ) ) ? 0x149 : 0 )\
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( 0xAC == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FB4 : 0 )\
	: ( 0xAE == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FC4 : 0 )\
	: ( 0xB1 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 0x1FB6 : 0 )              \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FB3 : 0 )\
	: ( 0xB7 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 0x1FC6 : 0 )              \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FC3 : 0 )\
	: ( ( ( 0xB9 == ((const U8*)s)[1] ) && ( 0xCD == ((const U8*)s)[2] ) ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 0x1FD6 : 0 )\
    : ( 0xCF == ((const U8*)s)[0] ) ?                                       \
	( ( 0x81 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCC == ((const U8*)s)[2] ) && ( 0x93 == ((const U8*)s)[3] ) ) ? 0x1FE4 : 0 )\
	: ( 0x85 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCC == ((const U8*)s)[2] ) ?                               \
		( ( 0x93 == ((const U8*)s)[3] ) ? 0x1F50 : 0 )              \
	    : ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 0x1FE6 : 0 )\
	: ( 0x89 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 0x1FF6 : 0 )              \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FF3 : 0 )\
	: ( ( ( 0x8E == ((const U8*)s)[1] ) && ( 0xCE == ((const U8*)s)[2] ) ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FF4 : 0 )\
    : ( 0xD5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xA5 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xD6 == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 0x587 : 0 )\
	: ( 0xB4 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xD5 == ((const U8*)s)[2] ) ?                               \
		( ( 0xA5 == ((const U8*)s)[3] ) ? 0xFB14                    \
		: ( 0xAB == ((const U8*)s)[3] ) ? 0xFB15                    \
		: ( 0xAD == ((const U8*)s)[3] ) ? 0xFB17                    \
		: ( 0xB6 == ((const U8*)s)[3] ) ? 0xFB13 : 0 )              \
	    : 0 )                                                           \
	: ( ( ( 0xBE == ((const U8*)s)[1] ) && ( 0xD5 == ((const U8*)s)[2] ) ) && ( 0xB6 == ((const U8*)s)[3] ) ) ? 0xFB16 : 0 )\
    : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part5_(s,e)                          \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) ?                             \
	( ( ( 0xCA == ((const U8*)s)[1] ) && ( 0xBE == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'H' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'I' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x87 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8C == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'S' ) ? 0xDF                    \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'T' ) ? 0xFB05                  \
	: ( ( 0xC5 == ((const U8*)s)[1] ) && ( 0xBF == ((const U8*)s)[2] ) ) ? 0xDF : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x88 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'W' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ((const U8*)s)[2] & 0xDF ) == 'S' ) ? 0xDF                \
	    : ( ( ((const U8*)s)[2] & 0xDF ) == 'T' ) ? 0xFB05 : 0 )        \
	: 0 )                                                               \
    : ( ( ( 0xCA == ((const U8*)s)[0] ) && ( 0xBC == ((const U8*)s)[1] ) ) && ( ( ((const U8*)s)[2] & 0xDF ) == 'N' ) ) ? 0x149 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ? 0xFB00                  \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'S' ) ? 0xDF                    \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'T' ) ? 0xFB05 : 0 )            \
    : 0 )                                                                   \
: 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part6_(s,e)                          \
( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) ?                                 \
	( ( ( 0xCA == ((const U8*)s)[1] ) && ( 0xBE == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'H' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'I' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x87 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8C == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'S' ) ? 0xDF                    \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'T' ) ? 0xFB05                  \
	: ( ( 0xC5 == ((const U8*)s)[1] ) && ( 0xBF == ((const U8*)s)[2] ) ) ? 0xDF : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x88 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'W' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ((const U8*)s)[2] & 0xDF ) == 'S' ) ? 0xDF                \
	    : ( ( ((const U8*)s)[2] & 0xDF ) == 'T' ) ? 0xFB05              \
	    : ( ( 0xC5 == ((const U8*)s)[2] ) && ( 0xBF == ((const U8*)s)[3] ) ) ? 0xDF : 0 )\
	: 0 )                                                               \
    : ( 0xCA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0xBC == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xDF ) == 'N' ) ) ? 0x149 : 0 )\
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( 0xAC == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FB4 : 0 )\
	: ( 0xAE == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FC4 : 0 )\
	: ( 0xB1 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xCE == ((const U8*)s)[4] ) && ( 0xB9 == ((const U8*)s)[5] ) ) ? 0x1FB7 : 0x1FB6 )\
		: 0 )                                                       \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FB3 : 0 )\
	: ( 0xB7 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xCE == ((const U8*)s)[4] ) && ( 0xB9 == ((const U8*)s)[5] ) ) ? 0x1FC7 : 0x1FC6 )\
		: 0 )                                                       \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FC3 : 0 )\
	: ( 0xB9 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCC == ((const U8*)s)[2] ) ?                               \
		( ( 0x88 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xCC == ((const U8*)s)[4] ) ?                       \
			( ( 0x80 == ((const U8*)s)[5] ) ? 0x1FD2            \
			: ( 0x81 == ((const U8*)s)[5] ) ? 0x390 : 0 )       \
		    : ( ( 0xCD == ((const U8*)s)[4] ) && ( 0x82 == ((const U8*)s)[5] ) ) ? 0x1FD7 : 0 )\
		: 0 )                                                       \
	    : ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 0x1FD6 : 0 )\
	: 0 )                                                               \
    : ( 0xCF == ((const U8*)s)[0] ) ? what_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e) )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part7_(s,e)                          \
( ((e)-(s) > 4) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) ?                             \
	( ( ( 0xCA == ((const U8*)s)[1] ) && ( 0xBE == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'H' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'I' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x87 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8C == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'S' ) ? 0xDF                    \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'T' ) ? 0xFB05                  \
	: ( ( 0xC5 == ((const U8*)s)[1] ) && ( 0xBF == ((const U8*)s)[2] ) ) ? 0xDF : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x88 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'W' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ?                             \
	( ( ( 0xCC == ((const U8*)s)[1] ) && ( 0x8A == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ((const U8*)s)[2] & 0xDF ) == 'S' ) ? 0xDF                \
	    : ( ( ((const U8*)s)[2] & 0xDF ) == 'T' ) ? 0xFB05              \
	    : ( ( 0xC5 == ((const U8*)s)[2] ) && ( 0xBF == ((const U8*)s)[3] ) ) ? 0xDF : 0 )\
	: 0 )                                                               \
    : ( 0xCA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0xBC == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xDF ) == 'N' ) ) ? 0x149 : 0 )\
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( 0xAC == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FB4 : 0 )\
	: ( 0xAE == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FC4 : 0 )\
	: ( 0xB1 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 0x1FB6 : 0 )              \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FB3 : 0 )\
	: ( 0xB7 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xCD == ((const U8*)s)[2] ) ?                               \
		( ( 0x82 == ((const U8*)s)[3] ) ? 0x1FC6 : 0 )              \
	    : ( ( 0xCE == ((const U8*)s)[2] ) && ( 0xB9 == ((const U8*)s)[3] ) ) ? 0x1FC3 : 0 )\
	: ( ( ( 0xB9 == ((const U8*)s)[1] ) && ( 0xCD == ((const U8*)s)[2] ) ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 0x1FD6 : 0 )\
    : ( 0xCF == ((const U8*)s)[0] ) ? what_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e) )\
: ((e)-(s) > 3) ? what_MULTI_CHAR_FOLD_utf8_safe_part4_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part5_(s,e) )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe(s,e)                                 \
( ((e)-(s) > 5) ? what_MULTI_CHAR_FOLD_utf8_safe_part6_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part7_(s,e) )

/*
	MULTI_CHAR_FOLD: multi-char strings that are folded to by a single character

	%regcharclass_multi_char_folds::multi_char_folds('l', 'a')
*/
/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_latin1_safe(s,e)                                 \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ) ? 2 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[1], 's', 't') ) ) ? 2 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) || ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ) ? 2 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 'S', 'T') || inRANGE_helper_(U8, ((const U8*)s)[1], 's', 't') ) ) ? 2 : 0 )\
: 0 )

/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_latin1_safe(s,e)                               \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'S' ) ? 0xDF                    \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'T' ) ? 0xFB05 : 0 )            \
    : 0 )                                                                   \
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ? 0xFB00                  \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'I' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'L' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'S' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'S' ) ? 0xDF                    \
	: ( ( ((const U8*)s)[1] & 0xDF ) == 'T' ) ? 0xFB05 : 0 )            \
    : 0 )                                                                   \
: 0 )

/*
	THREE_CHAR_FOLD: A three-character multi-char fold

	%regcharclass_multi_char_folds::multi_char_folds('u', '3')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_utf8_safe(s,e)                                   \
( ((e)-(s) > 5) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) && ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ) ) ? 3 : 0 )\
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( 0xB1 == ((const U8*)s)[1] || 0xB7 == ((const U8*)s)[1] ) ?      \
	    ( ( ( ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) && ( 0xCE == ((const U8*)s)[4] ) ) && ( 0xB9 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
	: ( ( ( 0xB9 == ((const U8*)s)[1] ) && ( 0xCC == ((const U8*)s)[2] ) ) && ( 0x88 == ((const U8*)s)[3] ) ) ? ( ( 0xCC == ((const U8*)s)[4] ) ?\
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x80, 0x81) ) ? 6 : 0 )\
		    : ( ( 0xCD == ((const U8*)s)[4] ) && ( 0x82 == ((const U8*)s)[5] ) ) ? 6 : 0 ) : 0 )\
    : ( 0xCF == ((const U8*)s)[0] ) ?                                       \
	( ( 0x85 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCC == ((const U8*)s)[2] ) && ( 0x88 == ((const U8*)s)[3] || 0x93 == ((const U8*)s)[3] ) ) ? ( ( 0xCC == ((const U8*)s)[4] ) ?\
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x80, 0x81) ) ? 6 : 0 )\
		    : ( ( 0xCD == ((const U8*)s)[4] ) && ( 0x82 == ((const U8*)s)[5] ) ) ? 6 : 0 ) : 0 )\
	: ( ( ( ( ( 0x89 == ((const U8*)s)[1] ) && ( 0xCD == ((const U8*)s)[2] ) ) && ( 0x82 == ((const U8*)s)[3] ) ) && ( 0xCE == ((const U8*)s)[4] ) ) && ( 0xB9 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
    : 0 )                                                                   \
: ( ( ( ((e)-(s) > 2) && ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ) && ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ) && ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ) ) ? 3 : 0 )

/*
	THREE_CHAR_FOLD: A three-character multi-char fold

	%regcharclass_multi_char_folds::multi_char_folds('l', '3')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_latin1_safe(s,e)                                 \
( ( ( ( ( ((e) - (s)) >= 3 ) && ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ) && ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ) && ( ( ( ((const U8*)s)[2] & 0xDF ) == 'I' ) || ( ( ((const U8*)s)[2] & 0xDF ) == 'L' ) ) ) ? 3 : 0 )

/*
	THREE_CHAR_FOLD_HEAD: The first two of three-character multi-char folds

	%regcharclass_multi_char_folds::multi_char_folds('u', 'h')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_HEAD_utf8_safe(s,e)                              \
( ((e)-(s) > 3) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) || ( ( ((const U8*)s)[0] & 0xDE ) == 'H' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) || ( ( ((const U8*)s)[0] & 0xDB ) == 'S' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ? 2 : 1 )                 \
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xCA == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBC == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( ( ((const U8*)s)[1] & 0xFD ) == 0xAC ) ? 2                      \
	: ( 0xB1 == ((const U8*)s)[1] || 0xB7 == ((const U8*)s)[1] ) ?      \
	    ( ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: ( 0xB9 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCC == ((const U8*)s)[2] ) && ( 0x88 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: 0 )                                                               \
    : ( 0xCF == ((const U8*)s)[0] ) ?                                       \
	( ( 0x81 == ((const U8*)s)[1] || 0x8E == ((const U8*)s)[1] ) ? 2    \
	: ( 0x85 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCC == ((const U8*)s)[2] ) && ( 0x88 == ((const U8*)s)[3] || 0x93 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: ( 0x89 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xCD == ((const U8*)s)[2] ) && ( 0x82 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: 0 )                                                               \
    : ( 0xD5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xA5 == ((const U8*)s)[1] || 0xB4 == ((const U8*)s)[1] || 0xBE == ((const U8*)s)[1] ) ? 2 : 0 )\
    : ( 0xE1 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBC == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ((const U8*)s)[2] & 0xD8 ) == 0x80 ) ? 3 : 0 )            \
	: ( ( 0xBD == ((const U8*)s)[1] ) && ( ( ( ((const U8*)s)[2] & 0xF8 ) == 0xA0 ) || ( ( ((const U8*)s)[2] & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0xBC ) ) ? 3 : 0 )\
    : 0 )                                                                   \
: ((e)-(s) > 2) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) || ( ( ((const U8*)s)[0] & 0xDE ) == 'H' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) || ( ( ((const U8*)s)[0] & 0xDB ) == 'S' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ? 2 : 1 )                 \
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xCA == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBC == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( ((const U8*)s)[1] & 0xFD ) == 0xAC ) || ( ( ((const U8*)s)[1] & 0xF7 ) == 0xB1 ) || ((const U8*)s)[1] == 0xB7 ) ? 2 : 0 )\
    : ( 0xCF == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( ((const U8*)s)[1] & 0xFB ) == 0x81 ) || ((const U8*)s)[1] == 0x89 || ((const U8*)s)[1] == 0x8E ) ? 2 : 0 )\
    : ( 0xD5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xA5 == ((const U8*)s)[1] || 0xB4 == ((const U8*)s)[1] || 0xBE == ((const U8*)s)[1] ) ? 2 : 0 )\
    : ( 0xE1 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBC == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ((const U8*)s)[2] & 0xD8 ) == 0x80 ) ? 3 : 0 )            \
	: ( ( 0xBD == ((const U8*)s)[1] ) && ( ( ( ((const U8*)s)[2] & 0xF8 ) == 0xA0 ) || ( ( ((const U8*)s)[2] & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0xBC ) ) ? 3 : 0 )\
    : 0 )                                                                   \
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) || ( ( ((const U8*)s)[0] & 0xDE ) == 'H' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) || ( ( ((const U8*)s)[0] & 0xDB ) == 'S' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ? 2 : 1 )                 \
    : ( 0xC5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBF == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xCA == ((const U8*)s)[0] ) ?                                       \
	( ( 0xBC == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xCE == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( ((const U8*)s)[1] & 0xFD ) == 0xAC ) || ( ( ((const U8*)s)[1] & 0xF7 ) == 0xB1 ) || ((const U8*)s)[1] == 0xB7 ) ? 2 : 0 )\
    : ( 0xCF == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( ((const U8*)s)[1] & 0xFB ) == 0x81 ) || ((const U8*)s)[1] == 0x89 || ((const U8*)s)[1] == 0x8E ) ? 2 : 0 )\
    : ( ( 0xD5 == ((const U8*)s)[0] ) && ( 0xA5 == ((const U8*)s)[1] || 0xB4 == ((const U8*)s)[1] || 0xBE == ((const U8*)s)[1] ) ) ? 2 : 0 )\
: ((e)-(s) > 0) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) || ( ( ((const U8*)s)[0] & 0xDE ) == 'H' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) || ( ( ((const U8*)s)[0] & 0xDB ) == 'S' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) )\
: 0 )

/*
	THREE_CHAR_FOLD_HEAD: The first two of three-character multi-char folds

	%regcharclass_multi_char_folds::multi_char_folds('l', 'h')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_HEAD_latin1_safe(s,e)                            \
( ((e)-(s) > 1) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) || ( ( ((const U8*)s)[0] & 0xDE ) == 'H' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) || ( ( ((const U8*)s)[0] & 0xDB ) == 'S' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xDF ) == 'F' ) ? 2 : 1 )                 \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xDF ) == 'A' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'F' ) || ( ( ((const U8*)s)[0] & 0xDE ) == 'H' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'J' ) || ( ( ((const U8*)s)[0] & 0xDB ) == 'S' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'T' ) || ( ( ((const U8*)s)[0] & 0xDF ) == 'Y' ) )\
: 0 )

/*
	FOLDS_TO_MULTI: characters that fold to multi-char strings

	\p{_Perl_Folds_To_Multi_Char}
*/
/*** GENERATED CODE ***/
#define is_FOLDS_TO_MULTI_utf8(s)                                           \
( ( 0xC3 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x9F == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xC4 == ((const U8*)s)[0] || 0xC7 == ((const U8*)s)[0] ) ?              \
    ( ( 0xB0 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xC5 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x89 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xCE == ((const U8*)s)[0] ) ?                                           \
    ( ( ( ((const U8*)s)[1] & 0xDF ) == 0x90 ) ? 2 : 0 )                    \
: ( 0xD6 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x87 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xE1 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0xBA == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x96, 0x9A) || 0x9E == ((const U8*)s)[2] ) ? 3 : 0 )\
    : ( 0xBD == ((const U8*)s)[1] ) ?                                       \
	( ( ( ((const U8*)s)[2] & 0xF9 ) == 0x90 ) ? 3 : 0 )                \
    : ( 0xBE == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0xAF) || inRANGE_helper_(U8, ((const U8*)s)[2], 0xB2, 0xB4) || inRANGE_helper_(U8, ((const U8*)s)[2], 0xB6, 0xB7) || 0xBC == ((const U8*)s)[2] ) ? 3 : 0 )\
    : ( ( 0xBF == ((const U8*)s)[1] ) && ( ( ( ((const U8*)s)[2] & 0xCA ) == 0x82 ) || ( ( ((const U8*)s)[2] & 0xF7 ) == 0x84 ) || ((const U8*)s)[2] == 0xA4 || ( ( ((const U8*)s)[2] & 0xF7 ) == 0xB4 ) ) ) ? 3 : 0 )\
: ( ( ( 0xEF == ((const U8*)s)[0] ) && ( 0xAC == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0x86) || inRANGE_helper_(U8, ((const U8*)s)[2], 0x93, 0x97) ) ) ? 3 : 0 )

/*
	PROBLEMATIC_LOCALE_FOLD: characters whose fold is problematic under locale

	\p{_Perl_Problematic_Locale_Folds}
*/
/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLD_utf8(s)                                  \
( ( ((const U8*)s)[0] <= 0x7F ) ? 1                                         \
: ( inRANGE_helper_(U8, ((const U8*)s)[0], 0xC2, 0xC3) ) ?                  \
    2                                                                       \
: ( 0xC4 == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0xB0, 0xB1) ) ? 2 : 0 )      \
: ( 0xC5 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x89 == ((const U8*)s)[1] || 0xB8 == ((const U8*)s)[1] || 0xBF == ((const U8*)s)[1] ) ? 2 : 0 )\
: ( 0xC7 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0xB0 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xCC == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x87 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xCE == ((const U8*)s)[0] ) ?                                           \
    ( ( ( ((const U8*)s)[1] & 0xDF ) == 0x9C ) ? 2 : 0 )                    \
: ( 0xE1 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0xBA == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x96, 0x9A) || 0x9E == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xE2 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x84 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xAA, 0xAB) ) ) ? 3 : 0 )\
: ( ( ( 0xEF == ((const U8*)s)[0] ) && ( 0xAC == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0x86) ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLD_cp(cp)                                   \
( cp <= 0xFF || ( 0xFF < cp &&                                              \
( inRANGE_helper_(UV, cp, 0x130, 0x131) || ( 0x131 < cp &&                  \
( 0x149 == cp || ( 0x149 < cp &&                                            \
( 0x178 == cp || ( 0x178 < cp &&                                            \
( 0x17F == cp || ( 0x17F < cp &&                                            \
( 0x1F0 == cp || ( 0x1F0 < cp &&                                            \
( 0x307 == cp || ( 0x307 < cp &&                                            \
( 0x39C == cp || ( 0x39C < cp &&                                            \
( 0x3BC == cp || ( 0x3BC < cp &&                                            \
( inRANGE_helper_(UV, cp, 0x1E96, 0x1E9A) || ( 0x1E9A < cp &&               \
( 0x1E9E == cp || ( 0x1E9E < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x212A, 0x212B) || inRANGE_helper_(UV, cp, 0xFB00, 0xFB06) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) )

/*
	PROBLEMATIC_LOCALE_FOLDEDS_START: The first folded character of folds which are problematic under locale

	\p{_Perl_Problematic_Locale_Foldeds_Start}
*/
/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLDEDS_START_utf8(s)                         \
( ( ((const U8*)s)[0] <= 0x7F ) ? 1                                         \
: ( inRANGE_helper_(U8, ((const U8*)s)[0], 0xC2, 0xC3) ) ?                  \
    2                                                                       \
: ( 0xC4 == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0xB0, 0xB1) ) ? 2 : 0 )      \
: ( 0xC5 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x89 == ((const U8*)s)[1] || 0xB8 == ((const U8*)s)[1] || 0xBF == ((const U8*)s)[1] ) ? 2 : 0 )\
: ( 0xC7 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0xB0 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( 0xBC == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xCE == ((const U8*)s)[0] ) ?                                           \
    ( ( ( ((const U8*)s)[1] & 0xDF ) == 0x9C ) ? 2 : 0 )                    \
: ( 0xE1 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0xBA == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x96, 0x9A) || 0x9E == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xE2 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x84 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0xAA, 0xAB) ) ) ? 3 : 0 )\
: ( ( ( 0xEF == ((const U8*)s)[0] ) && ( 0xAC == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0x86) ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLDEDS_START_cp(cp)                          \
( cp <= 0xFF || ( 0xFF < cp &&                                              \
( inRANGE_helper_(UV, cp, 0x130, 0x131) || ( 0x131 < cp &&                  \
( 0x149 == cp || ( 0x149 < cp &&                                            \
( 0x178 == cp || ( 0x178 < cp &&                                            \
( 0x17F == cp || ( 0x17F < cp &&                                            \
( 0x1F0 == cp || ( 0x1F0 < cp &&                                            \
( 0x2BC == cp || ( 0x2BC < cp &&                                            \
( 0x39C == cp || ( 0x39C < cp &&                                            \
( 0x3BC == cp || ( 0x3BC < cp &&                                            \
( inRANGE_helper_(UV, cp, 0x1E96, 0x1E9A) || ( 0x1E9A < cp &&               \
( 0x1E9E == cp || ( 0x1E9E < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x212A, 0x212B) || inRANGE_helper_(UV, cp, 0xFB00, 0xFB06) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) )

/*
	PATWS: pattern white space

	\p{_Perl_PatWS}
*/
/*** GENERATED CODE ***/
#define is_PATWS_safe(s,e,is_utf8)                                          \
( ( LIKELY((e) > (s)) ) ?                                                   \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\t', '\r') || ' ' == ((const U8*)s)[0] ) ? 1\
    : (! is_utf8 ) ?                                                        \
	    ( 0x85 == ((const U8*)s)[0] )                                   \
	: ( LIKELY(((e) - (s)) >= UTF8SKIP(s)) ) ?                          \
	    ( ( 0xC2 == ((const U8*)s)[0] ) ?                               \
		( ( 0x85 == ((const U8*)s)[1] ) ? 2 : 0 )                   \
	    : ( ( ( 0xE2 == ((const U8*)s)[0] ) && ( 0x80 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x8E, 0x8F) || inRANGE_helper_(U8, ((const U8*)s)[2], 0xA8, 0xA9) ) ) ? 3 : 0 )\
	: 0 )                                                               \
: 0 )

/*
	HANGUL_ED: Hangul syllables whose first UTF-8 byte is \xED

	0xD000 - 0xD7FF
*/
/*** GENERATED CODE ***/
#define is_HANGUL_ED_utf8_safe(s,e)                                         \
( ( ( ( ( ((e) - (s)) >= 3 ) && ( 0xED == ((const U8*)s)[0] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x80, 0x9F) ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x80, 0xBF) ) ) ? 3 : 0 )

#endif	/* ASCII/Latin1 */

#if 'A' == 193 /* EBCDIC 1047 */ \
     && '\\' == 224 && '[' == 173 && ']' == 189 && '{' == 192 && '}' == 208 \
     && '^' == 95 && '~' == 161 && '!' == 90 && '#' == 123 && '|' == 79 \
     && '$' == 91 && '@' == 124 && '`' == 121 && '\n' == 21
/*
	LNBREAK: Line Break: \R

	"\x0D\x0A"      # CRLF - Network (Windows) line ending
	\p{VertSpace}
*/
/*** GENERATED CODE ***/
#define is_LNBREAK_safe(s,e,is_utf8)                                        \
( ((e)-(s) > 2) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || '\n' == ((const U8*)s)[0] || 0x25 == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : ( ( ( ( is_utf8 ) && ( 0xCA == ((const U8*)s)[0] ) ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) ) ) ? 3 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || '\n' == ((const U8*)s)[0] || 0x25 == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\r') || '\n' == ((const U8*)s)[0] || 0x25 == ((const U8*)s)[0] )\
: 0 )

/*** GENERATED CODE ***/
#define is_LNBREAK_utf8_safe(s,e)                                           \
( ((e)-(s) > 2) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || '\n' == ((const U8*)s)[0] || 0x25 == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : ( ( ( 0xCA == ((const U8*)s)[0] ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) ) ) ? 3 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || '\n' == ((const U8*)s)[0] || 0x25 == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\r') || '\n' == ((const U8*)s)[0] || 0x25 == ((const U8*)s)[0] )\
: 0 )

/*** GENERATED CODE ***/
#define is_LNBREAK_latin1_safe(s,e)                                         \
( ((e)-(s) > 1) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || '\n' == ((const U8*)s)[0] || 0x25 == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\r') || '\n' == ((const U8*)s)[0] || 0x25 == ((const U8*)s)[0] )\
: 0 )

/*
	HORIZWS: Horizontal Whitespace: \h \H

	\p{HorizSpace}
*/
/*** GENERATED CODE ***/
#define is_HORIZWS_high(s)                                                  \
( ( 0xBC == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x63 == ((const U8*)s)[1] ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x41 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xAA) ) ? 3 : 0 )\
    : ( 0x42 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x56 == ((const U8*)s)[2] ) ? 3 : 0 )                           \
    : ( ( 0x43 == ((const U8*)s)[1] ) && ( 0x73 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( ( ( 0xCE == ((const U8*)s)[0] ) && ( 0x41 == ((const U8*)s)[1] ) ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_HORIZWS_cp_high(cp)                                              \
( 0x1680 == cp || ( 0x1680 < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x2000, 0x200A) || ( 0x200A < cp &&               \
( 0x202F == cp || ( 0x202F < cp &&                                          \
( 0x205F == cp || 0x3000 == cp ) ) ) ) ) ) )

/*
	VERTWS: Vertical Whitespace: \v \V

	\p{VertSpace}
*/
/*** GENERATED CODE ***/
#define is_VERTWS_high(s)                                                   \
( ( ( ( 0xCA == ((const U8*)s)[0] ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_VERTWS_cp_high(cp)                                               \
( inRANGE_helper_(UV, cp, 0x2028, 0x2029) )

/*
	XDIGIT: Hexadecimal digits

	\p{XDigit}
*/
/*** GENERATED CODE ***/
#define is_XDIGIT_high(s)                                                   \
( ( ( 0xDD == ((const U8*)s)[0] ) && ( 0x73 == ((const U8*)s)[1] ) ) ? ( ( 0x67 == ((const U8*)s)[2] ) ?\
	    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB0, 0xB9) ) ? 4 : 0 )\
	: ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x68, 0x69) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x42, 0x47) ) ) ? 4 : 0 ) : 0 )

/*** GENERATED CODE ***/
#define is_XDIGIT_cp_high(cp)                                               \
( inRANGE_helper_(UV, cp, 0xFF10, 0xFF19) || ( 0xFF19 < cp &&               \
( inRANGE_helper_(UV, cp, 0xFF21, 0xFF26) || inRANGE_helper_(UV, cp, 0xFF41, 0xFF46) ) ) )

/*
	XPERLSPACE: \p{XPerlSpace}

	\p{XPerlSpace}
*/
/*** GENERATED CODE ***/
#define is_XPERLSPACE_high(s)                                               \
( ( 0xBC == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x63 == ((const U8*)s)[1] ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x41 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xAA) ) ? 3 : 0 )\
    : ( 0x42 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) || 0x56 == ((const U8*)s)[2] ) ? 3 : 0 )\
    : ( ( 0x43 == ((const U8*)s)[1] ) && ( 0x73 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( ( ( 0xCE == ((const U8*)s)[0] ) && ( 0x41 == ((const U8*)s)[1] ) ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_XPERLSPACE_cp_high(cp)                                           \
( 0x1680 == cp || ( 0x1680 < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x2000, 0x200A) || ( 0x200A < cp &&               \
( inRANGE_helper_(UV, cp, 0x2028, 0x2029) || ( 0x2029 < cp &&               \
( 0x202F == cp || ( 0x202F < cp &&                                          \
( 0x205F == cp || 0x3000 == cp ) ) ) ) ) ) ) ) )

/*
	SPACE: Backwards \p{XPerlSpace}

	\p{XPerlSpace}
*/
/*** GENERATED CODE ***/
#define is_SPACE_utf8_safe_backwards(s,e)                                   \
( ((s) - (e) > 2) ?                                                         \
    ( ( '\t' == *((const U8*)s - 1) || inRANGE_helper_(U8, *((const U8*)s - 1), '\v', '\r') || '\n' == *((const U8*)s - 1) || 0x25 == *((const U8*)s - 1) || ' ' == *((const U8*)s - 1) ) ? 1\
    : ( 0x41 == *((const U8*)s - 1) ) ?                                     \
	( ( 0x41 == *((const U8*)s - 2) ) ?                                 \
	    ( ( ( *((const U8*)s - 3) & 0xFB ) == 0xCA ) ? 3 : 0 )          \
	: ( 0x63 == *((const U8*)s - 2) ) ?                                 \
	    ( ( 0xBC == *((const U8*)s - 3) ) ? 3 : 0 )                     \
	: ( 0x80 == *((const U8*)s - 2) ) ? 2 : 0 )                         \
    : ( inRANGE_helper_(U8, *((const U8*)s - 1), 0x42, 0x48) || 0x51 == *((const U8*)s - 1) ) ?\
	( ( ( 0x41 == *((const U8*)s - 2) ) && ( 0xCA == *((const U8*)s - 3) ) ) ? 3 : 0 )\
    : ( inRANGE_helper_(U8, *((const U8*)s - 1), 0x49, 0x4A) ) ?            \
	( ( ( inRANGE_helper_(U8, *((const U8*)s - 2), 0x41, 0x42) ) && ( 0xCA == *((const U8*)s - 3) ) ) ? 3 : 0 )\
    : ( 0x56 == *((const U8*)s - 1) ) ?                                     \
	( ( ( 0x42 == *((const U8*)s - 2) ) && ( 0xCA == *((const U8*)s - 3) ) ) ? 3 : 0 )\
    : ( ( ( 0x73 == *((const U8*)s - 1) ) && ( 0x43 == *((const U8*)s - 2) ) ) && ( 0xCA == *((const U8*)s - 3) ) ) ? 3 : 0 )\
: ((s) - (e) > 1) ?                                                         \
    ( ( '\t' == *((const U8*)s - 1) || inRANGE_helper_(U8, *((const U8*)s - 1), '\v', '\r') || '\n' == *((const U8*)s - 1) || 0x25 == *((const U8*)s - 1) || ' ' == *((const U8*)s - 1) ) ? 1\
    : ( ( 0x41 == *((const U8*)s - 1) ) && ( 0x80 == *((const U8*)s - 2) ) ) ? 2 : 0 )\
: ((s) - (e) > 0) ?                                                         \
    ( '\t' == *((const U8*)s - 1) || inRANGE_helper_(U8, *((const U8*)s - 1), '\v', '\r') || '\n' == *((const U8*)s - 1) || 0x25 == *((const U8*)s - 1) || ' ' == *((const U8*)s - 1) )\
: 0 )

/*
	NONCHAR: Non character code points

	\p{_Perl_Nchar}
*/
/*** GENERATED CODE ***/
#define is_NONCHAR_utf8_safe(s,e)                                           \
( ( ( LIKELY((e) > (s)) ) && ( LIKELY(((e) - (s)) >= UTF8SKIP(s)) ) ) ? ( ( 0xDD == ((const U8*)s)[0] ) ?\
	    ( ( 0x73 == ((const U8*)s)[1] ) ?                               \
		( ( 0x55 == ((const U8*)s)[2] ) ?                           \
		    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB0, 0xBF) ) ? 4 : 0 )\
		: ( 0x56 == ((const U8*)s)[2] ) ?                           \
		    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xA0, 0xAF) ) ? 4 : 0 )\
		: ( ( 0x73 == ((const U8*)s)[2] ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x72, 0x73) ) ) ? 4 : 0 )\
	    : 0 )                                                           \
	: ( ((const U8*)s)[0] == 0xDF || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0xFD ) == 0xF5 ) ) ?\
	    ( ( ( ( 0x73 == ((const U8*)s)[1] ) && ( 0x73 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x72, 0x73) ) ) ? 4 : 0 )\
	: ( 0xED == ((const U8*)s)[0] ) ?                                   \
	    ( ( ( ( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF9 ) == 0xA9 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF1 ) == 0xB1 ) ) && ( 0x73 == ((const U8*)s)[2] ) ) && ( 0x73 == ((const U8*)s)[3] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[4], 0x72, 0x73) ) ) ? 5 : 0 )\
	: ( ( ( ( ( 0xEE == ((const U8*)s)[0] ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( 0x73 == ((const U8*)s)[2] ) ) && ( 0x73 == ((const U8*)s)[3] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[4], 0x72, 0x73) ) ) ? 5 : 0 ) : 0 )

/*
	SHORTER_NON_CHARS: # 4 bytes

	0xFDD0 - 0xFDEF
	0xFFFE - 0xFFFF
	0x1FFFE - 0x1FFFF
	0x2FFFE - 0x2FFFF
	0x3FFFE - 0x3FFFF
*/
/*** GENERATED CODE ***/
#define is_SHORTER_NON_CHARS_utf8(s)                                        \
( ( 0xDD == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x73 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x55 == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB0, 0xBF) ) ? 4 : 0 )\
	: ( 0x56 == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xA0, 0xAF) ) ? 4 : 0 )\
	: ( ( 0x73 == ((const U8*)s)[2] ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x72, 0x73) ) ) ? 4 : 0 )\
    : 0 )                                                                   \
: ( ( ( ( ((const U8*)s)[0] == 0xDF || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0xFD ) == 0xF5 ) ) && ( 0x73 == ((const U8*)s)[1] ) ) && ( 0x73 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x72, 0x73) ) ) ? 4 : 0 )

/*
	LARGER_NON_CHARS: # 5 bytes

	0x4FFFE - 0x4FFFF
	0x5FFFE - 0x5FFFF
	0x6FFFE - 0x6FFFF
	0x7FFFE - 0x7FFFF
	0x8FFFE - 0x8FFFF
	0x9FFFE - 0x9FFFF
	0xAFFFE - 0xAFFFF
	0xBFFFE - 0xBFFFF
	0xCFFFE - 0xCFFFF
	0xDFFFE - 0xDFFFF
	0xEFFFE - 0xEFFFF
	0xFFFFE - 0xFFFFF
	0x10FFFE - 0x10FFFF
*/
/*** GENERATED CODE ***/
#define is_LARGER_NON_CHARS_utf8(s)                                         \
( ( 0xED == ((const U8*)s)[0] ) ?                                           \
    ( ( ( ( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF9 ) == 0xA9 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF1 ) == 0xB1 ) ) && ( 0x73 == ((const U8*)s)[2] ) ) && ( 0x73 == ((const U8*)s)[3] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[4], 0x72, 0x73) ) ) ? 5 : 0 )\
: ( ( ( ( ( 0xEE == ((const U8*)s)[0] ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( 0x73 == ((const U8*)s)[2] ) ) && ( 0x73 == ((const U8*)s)[3] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[4], 0x72, 0x73) ) ) ? 5 : 0 )

/*
	SURROGATE: Surrogate code points

	\p{_Perl_Surrogate}
*/
/*** GENERATED CODE ***/
#define is_SURROGATE_utf8(s)                                                \
( ( ( 0xDD == ((const U8*)s)[0] ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x65, 0x66) ) ) ? 4 : 0 )

/*** GENERATED CODE ***/
#define is_SURROGATE_utf8_safe(s,e)                                         \
( ( ( ( ( ( ((e) - (s)) >= 4 ) && ( 0xDD == ((const U8*)s)[0] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x65, 0x66) ) ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xBF) ) ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xA0, 0xBF) ) ) ? 4 : 0 )

/*
	QUOTEMETA: Meta-characters that \Q should quote

	\p{_Perl_Quotemeta}
*/
/*** GENERATED CODE ***/
#define is_QUOTEMETA_high(s)                                                \
( ( 0xB1 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x56 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xB8 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x57 == ((const U8*)s)[1] ) && ( 0x70 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xBB == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x51 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[2] ) ? 3 : 0 )                           \
    : ( ( 0x52 == ((const U8*)s)[1] ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xBC == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x63 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x41 == ((const U8*)s)[2] ) ? 3 : 0 )                           \
    : ( ( 0x71 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x63, 0x64) ) ) ? 3 : 0 )\
: ( 0xBE == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x41 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x52, 0x56) ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x41 == ((const U8*)s)[1] || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xAD, 0xBF) ) ?\
	3                                                                   \
    : ( 0x42 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xBE) ) ? 3 : 0 )\
    : ( 0x43 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA1, 0xB3) || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xB5, 0xBF) ) ? 3 : 0 )\
    : ( 0x44 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xAF) ) ? 3 : 0 )\
    : ( ( 0x53 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xB0, 0xBF) ) ) ? 3 : 0 )\
: ( 0xCB == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xA0, 0xA2) || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xA8, 0xBA) || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xBD, 0xBF) ) ?\
	3                                                                   \
    : ( 0x6A == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xB5) ) ? 3 : 0 )\
    : ( ( 0x70 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xB4, 0xBF) ) ) ? 3 : 0 )\
: ( 0xCC == ((const U8*)s)[0] ) ?                                           \
    3                                                                       \
: ( 0xCD == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xB0, 0xB3) ) ?\
	3                                                                   \
    : 0 )                                                                   \
: ( 0xCE == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x41 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xA3) || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA8, 0xBF) ) ? 3 : 0 )\
    : ( 0x42 == ((const U8*)s)[1] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xEF ) == 0xA0 ) ? 3 : 0 )\
    : ( ( 0x52 == ((const U8*)s)[1] ) && ( 0x45 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xDD == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x73 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x4A == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x72, 0x73) ) ? 4 : 0 )\
	: ( 0x57 == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xA0, 0xAF) ) ? 4 : 0 )\
	: ( 0x59 == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x46, 0x47) ) ? 4 : 0 )\
	: ( 0x66 == ((const U8*)s)[2] ) ?                                   \
	    ( ( 0x73 == ((const U8*)s)[3] ) ? 4 : 0 )                       \
	: ( 0x71 == ((const U8*)s)[2] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[3] ) ? 4 : 0 )                       \
	: ( ( 0x73 == ((const U8*)s)[2] ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB0, 0xB8) ) ) ? 4 : 0 )\
    : 0 )                                                                   \
: ( 0xDF == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x56 == ((const U8*)s)[1] ) ?                                       \
	( ( ( 0x46 == ((const U8*)s)[2] ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x41, 0x44) ) ) ? 4 : 0 )\
    : ( ( ( 0x63 == ((const U8*)s)[1] ) && ( 0x52 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x62, 0x69) ) ) ? 4 : 0 )\
: ( ( ( 0xED == ((const U8*)s)[0] ) && ( 0x70 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x44) ) ) ? 5 : 0 )

/*
	MULTI_CHAR_FOLD: multi-char strings that are folded to by a single character

	%regcharclass_multi_char_folds::multi_char_folds('u', 'a')
*/
/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e)                            \
( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e)                            \
( ( 0x8F == ((const U8*)s)[0] ) ?                                           \
	( ( 0x73 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8F == ((const U8*)s)[2] ) ?                               \
		( ( 0x73 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( ((const U8*)s)[2] & 0xBE ) == 's' ) ? 3 : 0 )             \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8F == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x73 == ((const U8*)s)[2] ) ? 3 : 0 )                       \
	: ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ? 2 : 0 )                 \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ?\
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xAB == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x70 == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 3 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) ?     \
	    ( ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] || 0x66 == ((const U8*)s)[1] ) ?      \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB4 == ((const U8*)s)[4] ) && ( 0x68 == ((const U8*)s)[5] ) ) ? 6 : 4 )\
		: 0 )                                                       \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x68 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x49 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAF == ((const U8*)s)[4] ) ?                       \
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 0 )\
		    : ( ( 0xB1 == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
		: 0 )                                                       \
	    : ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: 0 )                                                               \
    : ( 0xB5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x62 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x49 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAF == ((const U8*)s)[4] ) ?                       \
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 0 )\
		    : ( ( 0xB1 == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
		: ( 0x62 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAF == ((const U8*)s)[4] ) ?                       \
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 4 )\
		    : ( ( 0xB1 == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 4 )\
		: 0 )                                                       \
	    : ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB4 == ((const U8*)s)[4] ) && ( 0x68 == ((const U8*)s)[5] ) ) ? 6 : 4 )\
		: 0 )                                                       \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB4 == ((const U8*)s)[2] ) ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xB8 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x52 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( ( 0xB8 == ((const U8*)s)[3] ) && ( 0x53 == ((const U8*)s)[4] ) ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
	    : ( 0x63 == ((const U8*)s)[2] ) ?                               \
		( ( ( ( 0xB8 == ((const U8*)s)[3] ) && ( 0x52 == ((const U8*)s)[4] ) ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[5]) & 0xF7 ) == 0xA5 ) || ((const U8*)s)[5] == 0x52 || ((const U8*)s)[5] == 0x65 ) ) ? 6 : 0 )\
	    : ( ( ( ( 0x72 == ((const U8*)s)[2] ) && ( 0xB8 == ((const U8*)s)[3] ) ) && ( 0x52 == ((const U8*)s)[4] ) ) && ( 0x65 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
	: 0 )                                                               \
    : ( 0xBF == ((const U8*)s)[0] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x67, 0x68) ) ?          \
	    ( ( ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x48) ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
	: ( ( ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF8 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0x70 ) ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
    : 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e)                            \
( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                                 \
	( ( ( 0xAB == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xB0 == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8F == ((const U8*)s)[2] ) ?                               \
		( ( 0x73 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( ((const U8*)s)[2] & 0xBE ) == 's' ) ? 3 : 0 )             \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8F == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x73 == ((const U8*)s)[2] ) ? 3 : 0 )                       \
	: ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ? 2 : 0 )                 \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ?\
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xAB == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x70 == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 3 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) ?     \
	    ( ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] || 0x66 == ((const U8*)s)[1] ) ?      \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x68 == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xB5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x62 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x62 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB4 == ((const U8*)s)[2] ) ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xBF == ((const U8*)s)[0] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x67, 0x68) ) ?          \
	    ( ( ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x48) ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
	: ( ( ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF8 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0x70 ) ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
    : 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e)                            \
( ((e)-(s) > 3) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAB == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xB0 == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8F == ((const U8*)s)[2] ) ?                               \
		( ( 0x73 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( ((const U8*)s)[2] & 0xBE ) == 's' ) ? 3 : 0 )             \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8F == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x73 == ((const U8*)s)[2] ) ? 3 : 0 )                       \
	: ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ? 2 : 0 )                 \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ?\
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xAB == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x70 == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 3 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) ?     \
	    ( ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] || 0x66 == ((const U8*)s)[1] ) ?      \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x68 == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xB5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x62 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x62 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB4 == ((const U8*)s)[2] ) ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : 0 )                                                                   \
: ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAB == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xB0 == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x73 == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBE ) == 's' ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8F == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x73 == ((const U8*)s)[2] ) ? 3 : 0 )                       \
	: ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ? 2 : 0 )                 \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ?\
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( 0xAB == ((const U8*)s)[0] ) && ( 0x70 == ((const U8*)s)[1] ) ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 3 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) && ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ) ? 2 : 0 )\
: 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe(s,e)                                   \
( ((e)-(s) > 5) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAB == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xB0 == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ? is_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e) : is_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e) )\
: ((e)-(s) > 4) ? is_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e) : is_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e) )

/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e)                          \
( ( 0x52 == ((const U8*)s)[1] ) ?                                           \
	    ( ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( ( 0xB8 == ((const U8*)s)[3] ) && ( 0x53 == ((const U8*)s)[4] ) ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 0x587 : 0 )\
	    : ( 0x63 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB8 == ((const U8*)s)[3] ) && ( 0x52 == ((const U8*)s)[4] ) ) ? ( ( 0x46 == ((const U8*)s)[5] ) ? 0xFB14\
			: ( 0x52 == ((const U8*)s)[5] ) ? 0xFB15            \
			: ( 0x54 == ((const U8*)s)[5] ) ? 0xFB17            \
			: ( 0x65 == ((const U8*)s)[5] ) ? 0xFB13 : 0 ) : 0 )\
	    : ( ( ( ( 0x72 == ((const U8*)s)[2] ) && ( 0xB8 == ((const U8*)s)[3] ) ) && ( 0x52 == ((const U8*)s)[4] ) ) && ( 0x65 == ((const U8*)s)[5] ) ) ? 0xFB16 : 0 )\
	: 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e)                          \
( ( 0xBF == ((const U8*)s)[0] ) ?                                           \
	( ( 0x67 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F80 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F81 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F82 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F83 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F84 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F85 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F86 : 0 )\
	    : ( ( ( 0x48 == ((const U8*)s)[2] ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F87 : 0 )\
	: ( 0x68 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F90 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F91 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F92 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F93 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F94 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F95 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F96 : 0 )\
	    : ( ( ( 0x48 == ((const U8*)s)[2] ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F97 : 0 )\
	: ( 0x6A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA0 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA1 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA2 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA3 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA4 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA5 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA6 : 0 )\
	    : ( 0x48 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA7 : 0 )\
	    : ( 0x57 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FB2 : 0 )\
	    : ( 0x63 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FC2 : 0 )\
	    : ( ( ( 0x70 == ((const U8*)s)[2] ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FF2 : 0 )\
	: 0 )                                                               \
    : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e)                          \
( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                                 \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e)                          \
( ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                                 \
	( ( ( 0xB0 == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8F == ((const U8*)s)[2] ) ?                               \
		( ( 0x73 == ((const U8*)s)[3] ) ? 0x59 : 0 )                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 's' ) ? 0x59                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 't' ) ? 0xFB05 : 0 )        \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8F == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x73 == ((const U8*)s)[2] ) ? 0x59 : 0 )                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xAB == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x70 == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 0x149 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x53 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FB4 : 0 )\
	: ( 0x55 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FC4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB4 == ((const U8*)s)[4] ) && ( 0x68 == ((const U8*)s)[5] ) ) ? 0x1FB7 : 0x1FB6 )\
		: 0 )                                                       \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FB3 : 0 )\
	: ( 0x66 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB4 == ((const U8*)s)[4] ) && ( 0x68 == ((const U8*)s)[5] ) ) ? 0x1FC7 : 0x1FC6 )\
		: 0 )                                                       \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FC3 : 0 )\
	: ( 0x68 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x49 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAF == ((const U8*)s)[4] ) ?                       \
			( ( 0x41 == ((const U8*)s)[5] ) ? 0x1FD2            \
			: ( 0x42 == ((const U8*)s)[5] ) ? 0x390 : 0 )       \
		    : ( ( 0xB1 == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 0x1FD7 : 0 )\
		: 0 )                                                       \
	    : ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FD6 : 0 )\
	: 0 )                                                               \
    : ( 0xB5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x62 == ((const U8*)s)[3] ) ) ? 0x1FE4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x49 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAF == ((const U8*)s)[4] ) ?                       \
			( ( 0x41 == ((const U8*)s)[5] ) ? 0x1FE2            \
			: ( 0x42 == ((const U8*)s)[5] ) ? 0x3B0 : 0 )       \
		    : ( ( 0xB1 == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 0x1FE7 : 0 )\
		: ( 0x62 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAF == ((const U8*)s)[4] ) ?                       \
			( ( 0x41 == ((const U8*)s)[5] ) ? 0x1F52            \
			: ( 0x42 == ((const U8*)s)[5] ) ? 0x1F54 : 0x1F50 ) \
		    : ( ( 0xB1 == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 0x1F56 : 0x1F50 )\
		: 0 )                                                       \
	    : ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FE6 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB4 == ((const U8*)s)[4] ) && ( 0x68 == ((const U8*)s)[5] ) ) ? 0x1FF7 : 0x1FF6 )\
		: 0 )                                                       \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FF3 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB4 == ((const U8*)s)[2] ) ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FF4 : 0 )\
    : ( 0xB8 == ((const U8*)s)[0] ) ? what_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e) )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part4_(s,e)                          \
( ( 0x42 == ((const U8*)s)[1] ) ?                                           \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x62 == ((const U8*)s)[3] ) ) ? 0x1FE4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x62 == ((const U8*)s)[3] ) ? 0x1F50 : 0 )              \
	    : ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FE6 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FF6 : 0 )              \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FF3 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB4 == ((const U8*)s)[2] ) ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FF4 : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part5_(s,e)                          \
( ( 0xBF == ((const U8*)s)[0] ) ?                                           \
	( ( 0x67 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F80 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F81 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F82 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F83 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F84 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F85 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F86 : 0 )\
	    : ( ( ( 0x48 == ((const U8*)s)[2] ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F87 : 0 )\
	: ( 0x68 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F90 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F91 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F92 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F93 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F94 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F95 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F96 : 0 )\
	    : ( ( ( 0x48 == ((const U8*)s)[2] ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1F97 : 0 )\
	: ( 0x6A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA0 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA1 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA2 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA3 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA4 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA5 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA6 : 0 )\
	    : ( 0x48 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FA7 : 0 )\
	    : ( 0x57 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FB2 : 0 )\
	    : ( 0x63 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB4 == ((const U8*)s)[3] ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FC2 : 0 )\
	    : ( ( ( 0x70 == ((const U8*)s)[2] ) && ( 0xB4 == ((const U8*)s)[3] ) ) && ( 0x68 == ((const U8*)s)[4] ) ) ? 0x1FF2 : 0 )\
	: 0 )                                                               \
    : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part6_(s,e)                          \
( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                                 \
	( ( ( 0xAB == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xB0 == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8F == ((const U8*)s)[2] ) ?                               \
		( ( 0x73 == ((const U8*)s)[3] ) ? 0x59 : 0 )                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 's' ) ? 0x59                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 't' ) ? 0xFB05 : 0 )        \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8F == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x73 == ((const U8*)s)[2] ) ? 0x59 : 0 )                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xAB == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x70 == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 0x149 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x53 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FB4 : 0 )\
	: ( 0x55 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FC4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FB6 : 0 )              \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FB3 : 0 )\
	: ( 0x66 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FC6 : 0 )              \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FC3 : 0 )\
	: ( ( ( 0x68 == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FD6 : 0 )\
    : ( 0xB5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x62 == ((const U8*)s)[3] ) ) ? 0x1FE4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x62 == ((const U8*)s)[3] ) ? 0x1F50 : 0 )              \
	    : ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FE6 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FF6 : 0 )              \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FF3 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB4 == ((const U8*)s)[2] ) ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FF4 : 0 )\
    : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part7_(s,e)                          \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAB == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xB0 == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 's' ) ? 0x59                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 't' ) ? 0xFB05 : 0 )        \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8F == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x73 == ((const U8*)s)[2] ) ? 0x59 : 0 )                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( ( ( 0xAB == ((const U8*)s)[0] ) && ( 0x70 == ((const U8*)s)[1] ) ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 0x149 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 0xFB00                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : 0 )                                                                   \
: 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe(s,e)                                 \
( ((e)-(s) > 5) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAB == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ? what_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e) )\
: ((e)-(s) > 4) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAB == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xB0 == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8F == ((const U8*)s)[2] ) ?                               \
		( ( 0x73 == ((const U8*)s)[3] ) ? 0x59 : 0 )                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 's' ) ? 0x59                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 't' ) ? 0xFB05 : 0 )        \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8F == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x73 == ((const U8*)s)[2] ) ? 0x59 : 0 )                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ?                             \
	( ( ( 0xAF == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xAB == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x70 == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 0x149 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x53 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FB4 : 0 )\
	: ( 0x55 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FC4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FB6 : 0 )              \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FB3 : 0 )\
	: ( 0x66 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xB1 == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FC6 : 0 )              \
	    : ( ( 0xB4 == ((const U8*)s)[2] ) && ( 0x68 == ((const U8*)s)[3] ) ) ? 0x1FC3 : 0 )\
	: ( ( ( 0x68 == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FD6 : 0 )\
    : ( 0xB5 == ((const U8*)s)[0] ) ? what_MULTI_CHAR_FOLD_utf8_safe_part4_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part5_(s,e) )\
: ((e)-(s) > 3) ? what_MULTI_CHAR_FOLD_utf8_safe_part6_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part7_(s,e) )

/*
	MULTI_CHAR_FOLD: multi-char strings that are folded to by a single character

	%regcharclass_multi_char_folds::multi_char_folds('l', 'a')
*/
/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_latin1_safe(s,e)                                 \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) && ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ) ? 2 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) && ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ) ? 2 : 0 )\
: 0 )

/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_latin1_safe(s,e)                               \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : 0 )                                                                   \
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 0xFB00                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : 0 )                                                                   \
: 0 )

/*
	THREE_CHAR_FOLD: A three-character multi-char fold

	%regcharclass_multi_char_folds::multi_char_folds('u', '3')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_utf8_safe(s,e)                                   \
( ((e)-(s) > 5) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) && ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ) ? 3 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x58 == ((const U8*)s)[1] || 0x66 == ((const U8*)s)[1] ) ?      \
	    ( ( ( ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) && ( 0xB4 == ((const U8*)s)[4] ) ) && ( 0x68 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
	: ( ( ( 0x68 == ((const U8*)s)[1] ) && ( 0xAF == ((const U8*)s)[2] ) ) && ( 0x49 == ((const U8*)s)[3] ) ) ? ( ( 0xAF == ((const U8*)s)[4] ) ?\
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 0 )\
		    : ( ( 0xB1 == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 ) : 0 )\
    : ( 0xB5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x49 == ((const U8*)s)[3] || 0x62 == ((const U8*)s)[3] ) ) ? ( ( 0xAF == ((const U8*)s)[4] ) ?\
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 0 )\
		    : ( ( 0xB1 == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 ) : 0 )\
	: ( ( ( ( ( 0x4A == ((const U8*)s)[1] ) && ( 0xB1 == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) && ( 0xB4 == ((const U8*)s)[4] ) ) && ( 0x68 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
    : 0 )                                                                   \
: ( ( ( ((e)-(s) > 2) && ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ) && ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ) && ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ) ? 3 : 0 )

/*
	THREE_CHAR_FOLD: A three-character multi-char fold

	%regcharclass_multi_char_folds::multi_char_folds('l', '3')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_latin1_safe(s,e)                                 \
( ( ( ( ( ((e) - (s)) >= 3 ) && ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ) && ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ) && ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ) ? 3 : 0 )

/*
	THREE_CHAR_FOLD_HEAD: The first two of three-character multi-char folds

	%regcharclass_multi_char_folds::multi_char_folds('u', 'h')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_HEAD_utf8_safe(s,e)                              \
( ((e)-(s) > 3) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 2 : 1 )                 \
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xAB == ((const U8*)s)[0] ) ?                                       \
	( ( 0x70 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) ? 2   \
	: ( 0x58 == ((const U8*)s)[1] || 0x66 == ((const U8*)s)[1] ) ?      \
	    ( ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: ( 0x68 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x49 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: 0 )                                                               \
    : ( 0xB5 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] || 0x55 == ((const U8*)s)[1] ) ? 2    \
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x49 == ((const U8*)s)[3] || 0x62 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB1 == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: 0 )                                                               \
    : ( 0xB8 == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x52 == ((const U8*)s)[1] ) && ( 0x46 == ((const U8*)s)[2] || 0x63 == ((const U8*)s)[2] || 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xBF == ((const U8*)s)[0] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x67, 0x68) ) ?          \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x48) ) ? 3 : 0 )\
	: ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF8 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0x70 ) ) ? 3 : 0 )\
    : 0 )                                                                   \
: ((e)-(s) > 2) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 2 : 1 )                 \
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xAB == ((const U8*)s)[0] ) ?                                       \
	( ( 0x70 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF7 ) == 0xB1 ) || ((const U8*)s)[1] == 0x66 ) ? 2 : 0 )\
    : ( 0xB5 == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( ((const U8*)s)[1] & 0xFB ) == 0x42 ) || ((const U8*)s)[1] == 0x4A || ((const U8*)s)[1] == 0x55 ) ? 2 : 0 )\
    : ( 0xB8 == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x52 == ((const U8*)s)[1] ) && ( 0x46 == ((const U8*)s)[2] || 0x63 == ((const U8*)s)[2] || 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xBF == ((const U8*)s)[0] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x67, 0x68) ) ?          \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x48) ) ? 3 : 0 )\
	: ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF8 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0x70 ) ) ? 3 : 0 )\
    : 0 )                                                                   \
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 2 : 1 )                 \
    : ( 0x8F == ((const U8*)s)[0] ) ?                                       \
	( ( 0x73 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xAB == ((const U8*)s)[0] ) ?                                       \
	( ( 0x70 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF7 ) == 0xB1 ) || ((const U8*)s)[1] == 0x66 ) ? 2 : 0 )\
    : ( ( 0xB5 == ((const U8*)s)[0] ) && ( ( ( ((const U8*)s)[1] & 0xFB ) == 0x42 ) || ((const U8*)s)[1] == 0x4A || ((const U8*)s)[1] == 0x55 ) ) ? 2 : 0 )\
: ((e)-(s) > 0) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0x9F ) == 'f' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) )\
: 0 )

/*
	THREE_CHAR_FOLD_HEAD: The first two of three-character multi-char folds

	%regcharclass_multi_char_folds::multi_char_folds('l', 'h')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_HEAD_latin1_safe(s,e)                            \
( ((e)-(s) > 1) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 2 : 1 )                 \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0x9F ) == 'f' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) )\
: 0 )

/*
	FOLDS_TO_MULTI: characters that fold to multi-char strings

	\p{_Perl_Folds_To_Multi_Char}
*/
/*** GENERATED CODE ***/
#define is_FOLDS_TO_MULTI_utf8(s)                                           \
( ( 0x8A == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x73 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0x8D == ((const U8*)s)[0] || 0x9C == ((const U8*)s)[0] || inRANGE_helper_(U8, ((const U8*)s)[0], 0xB3, 0xB4) ) ?\
    ( ( 0x57 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0x8E == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x4A == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xB8 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x53 == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xBF == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x63 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x65, 0x69) || 0x72 == ((const U8*)s)[2] ) ? 3 : 0 )\
    : ( 0x69 == ((const U8*)s)[1] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF9 ) == 0xB0 ) ? 3 : 0 )\
    : ( 0x70 == ((const U8*)s)[1] ) ?                                       \
	3                                                                   \
    : ( 0x71 == ((const U8*)s)[1] ) ?                                       \
	( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF0 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFA ) == 0xB2 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF7 ) == 0xB4 ) ) ? 3 : 0 )\
    : ( 0x72 == ((const U8*)s)[1] ) ?                                       \
	( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xEA ) == 0xA2 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF7 ) == 0xA4 ) ) ? 3 : 0 )\
    : ( ( 0x73 == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xEA ) == 0xA2 ) || ((const U8*)s)[2] == 0x45 || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF7 ) == 0xB4 ) ) ) ? 3 : 0 )\
: ( ( ( ( 0xDD == ((const U8*)s)[0] ) && ( 0x72 == ((const U8*)s)[1] ) ) && ( 0x67 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x41, 0x47) || inRANGE_helper_(U8, ((const U8*)s)[3], 0x62, 0x66) ) ) ? 4 : 0 )

/*
	PROBLEMATIC_LOCALE_FOLD: characters whose fold is problematic under locale

	\p{_Perl_Problematic_Locale_Folds}
*/
/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLD_utf8(s)                                  \
( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0x80 ) == 0x00 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0xE0 ) == 0x80 ) ) ? 1\
: ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[0]), 0xC5, 0xC7) ) ?\
    2                                                                       \
: ( 0x8D == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x57, 0x58) ) ? 2 : 0 )      \
: ( 0x8E == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x4A == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0x8F == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x67 == ((const U8*)s)[1] || 0x73 == ((const U8*)s)[1] ) ? 2 : 0 )  \
: ( 0x9C == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x57 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xAF == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x48 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( inRANGE_helper_(U8, ((const U8*)s)[0], 0xB3, 0xB4) ) ?                  \
    ( ( 0x70 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xBF == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x63 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x65, 0x69) || 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x4A == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x51, 0x52) ) ) ? 3 : 0 )\
: ( ( ( ( 0xDD == ((const U8*)s)[0] ) && ( 0x72 == ((const U8*)s)[1] ) ) && ( 0x67 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x41, 0x47) ) ) ? 4 : 0 )

/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLD_cp(cp)                                   \
( cp <= 0xFF || ( 0xFF < cp &&                                              \
( inRANGE_helper_(UV, cp, 0x130, 0x131) || ( 0x131 < cp &&                  \
( 0x149 == cp || ( 0x149 < cp &&                                            \
( 0x178 == cp || ( 0x178 < cp &&                                            \
( 0x17F == cp || ( 0x17F < cp &&                                            \
( 0x1F0 == cp || ( 0x1F0 < cp &&                                            \
( 0x307 == cp || ( 0x307 < cp &&                                            \
( 0x39C == cp || ( 0x39C < cp &&                                            \
( 0x3BC == cp || ( 0x3BC < cp &&                                            \
( inRANGE_helper_(UV, cp, 0x1E96, 0x1E9A) || ( 0x1E9A < cp &&               \
( 0x1E9E == cp || ( 0x1E9E < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x212A, 0x212B) || inRANGE_helper_(UV, cp, 0xFB00, 0xFB06) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) )

/*
	PROBLEMATIC_LOCALE_FOLDEDS_START: The first folded character of folds which are problematic under locale

	\p{_Perl_Problematic_Locale_Foldeds_Start}
*/
/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLDEDS_START_utf8(s)                         \
( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0x80 ) == 0x00 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0xE0 ) == 0x80 ) ) ? 1\
: ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[0]), 0xC5, 0xC7) ) ?\
    2                                                                       \
: ( 0x8D == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x57, 0x58) ) ? 2 : 0 )      \
: ( 0x8E == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x4A == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0x8F == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x67 == ((const U8*)s)[1] || 0x73 == ((const U8*)s)[1] ) ? 2 : 0 )  \
: ( 0x9C == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x57 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xAB == ((const U8*)s)[0] || inRANGE_helper_(U8, ((const U8*)s)[0], 0xB3, 0xB4) ) ?\
    ( ( 0x70 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xBF == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x63 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x65, 0x69) || 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x4A == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x51, 0x52) ) ) ? 3 : 0 )\
: ( ( ( ( 0xDD == ((const U8*)s)[0] ) && ( 0x72 == ((const U8*)s)[1] ) ) && ( 0x67 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x41, 0x47) ) ) ? 4 : 0 )

/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLDEDS_START_cp(cp)                          \
( cp <= 0xFF || ( 0xFF < cp &&                                              \
( inRANGE_helper_(UV, cp, 0x130, 0x131) || ( 0x131 < cp &&                  \
( 0x149 == cp || ( 0x149 < cp &&                                            \
( 0x178 == cp || ( 0x178 < cp &&                                            \
( 0x17F == cp || ( 0x17F < cp &&                                            \
( 0x1F0 == cp || ( 0x1F0 < cp &&                                            \
( 0x2BC == cp || ( 0x2BC < cp &&                                            \
( 0x39C == cp || ( 0x39C < cp &&                                            \
( 0x3BC == cp || ( 0x3BC < cp &&                                            \
( inRANGE_helper_(UV, cp, 0x1E96, 0x1E9A) || ( 0x1E9A < cp &&               \
( 0x1E9E == cp || ( 0x1E9E < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x212A, 0x212B) || inRANGE_helper_(UV, cp, 0xFB00, 0xFB06) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) )

/*
	PATWS: pattern white space

	\p{_Perl_PatWS}
*/
/*** GENERATED CODE ***/
#define is_PATWS_safe(s,e,is_utf8)                                          \
( ( LIKELY((e) > (s)) ) ?                                                   \
    ( ( '\t' == ((const U8*)s)[0] || inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\r') || '\n' == ((const U8*)s)[0] || 0x25 == ((const U8*)s)[0] || ' ' == ((const U8*)s)[0] ) ? 1\
    : ( ( is_utf8 && LIKELY(((e) - (s)) >= UTF8SKIP(s)) ) && ( 0xCA == ((const U8*)s)[0] ) ) ? ( ( 0x41 == ((const U8*)s)[1] ) ?\
		    ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x55, 0x56) ) ? 3 : 0 )\
		: ( ( 0x42 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) ) ) ? 3 : 0 ) : 0 )\
: 0 )

/*
	HANGUL_ED: Hangul syllables whose first UTF-8 byte is \xED

	0x1 - 0x0
*/
/*** GENERATED CODE ***/
#define is_HANGUL_ED_utf8_safe(s,e)                                         \
( 0 )

#endif	/* EBCDIC 1047 */

#if 'A' == 193 /* EBCDIC 037 */ \
     && '\\' == 224 && '[' == 186 && ']' == 187 && '{' == 192 && '}' == 208 \
     && '^' == 176 && '~' == 161 && '!' == 90 && '#' == 123 && '|' == 79 \
     && '$' == 91 && '@' == 124 && '`' == 121 && '\n' == 37
/*
	LNBREAK: Line Break: \R

	"\x0D\x0A"      # CRLF - Network (Windows) line ending
	\p{VertSpace}
*/
/*** GENERATED CODE ***/
#define is_LNBREAK_safe(s,e,is_utf8)                                        \
( ((e)-(s) > 2) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || 0x15 == ((const U8*)s)[0] || '\n' == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : ( ( ( ( is_utf8 ) && ( 0xCA == ((const U8*)s)[0] ) ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) ) ) ? 3 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || 0x15 == ((const U8*)s)[0] || '\n' == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\r') || 0x15 == ((const U8*)s)[0] || '\n' == ((const U8*)s)[0] )\
: 0 )

/*** GENERATED CODE ***/
#define is_LNBREAK_utf8_safe(s,e)                                           \
( ((e)-(s) > 2) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || 0x15 == ((const U8*)s)[0] || '\n' == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : ( ( ( 0xCA == ((const U8*)s)[0] ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) ) ) ? 3 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || 0x15 == ((const U8*)s)[0] || '\n' == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\r') || 0x15 == ((const U8*)s)[0] || '\n' == ((const U8*)s)[0] )\
: 0 )

/*** GENERATED CODE ***/
#define is_LNBREAK_latin1_safe(s,e)                                         \
( ((e)-(s) > 1) ?                                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\f') || 0x15 == ((const U8*)s)[0] || '\n' == ((const U8*)s)[0] ) ? 1\
    : ( '\r' == ((const U8*)s)[0] ) ?                                       \
	( ( '\n' == ((const U8*)s)[1] ) ? 2 : 1 )                           \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\r') || 0x15 == ((const U8*)s)[0] || '\n' == ((const U8*)s)[0] )\
: 0 )

/*
	HORIZWS: Horizontal Whitespace: \h \H

	\p{HorizSpace}
*/
/*** GENERATED CODE ***/
#define is_HORIZWS_high(s)                                                  \
( ( 0xBD == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x62 == ((const U8*)s)[1] ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x41 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xAA) ) ? 3 : 0 )\
    : ( 0x42 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x56 == ((const U8*)s)[2] ) ? 3 : 0 )                           \
    : ( ( 0x43 == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( ( ( 0xCE == ((const U8*)s)[0] ) && ( 0x41 == ((const U8*)s)[1] ) ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_HORIZWS_cp_high(cp)                                              \
( 0x1680 == cp || ( 0x1680 < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x2000, 0x200A) || ( 0x200A < cp &&               \
( 0x202F == cp || ( 0x202F < cp &&                                          \
( 0x205F == cp || 0x3000 == cp ) ) ) ) ) ) )

/*
	VERTWS: Vertical Whitespace: \v \V

	\p{VertSpace}
*/
/*** GENERATED CODE ***/
#define is_VERTWS_high(s)                                                   \
( ( ( ( 0xCA == ((const U8*)s)[0] ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_VERTWS_cp_high(cp)                                               \
( inRANGE_helper_(UV, cp, 0x2028, 0x2029) )

/*
	XDIGIT: Hexadecimal digits

	\p{XDigit}
*/
/*** GENERATED CODE ***/
#define is_XDIGIT_high(s)                                                   \
( ( ( 0xDD == ((const U8*)s)[0] ) && ( 0x72 == ((const U8*)s)[1] ) ) ? ( ( 0x66 == ((const U8*)s)[2] ) ?\
	    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB0, 0xB9) ) ? 4 : 0 )\
	: ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x67, 0x68) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x42, 0x47) ) ) ? 4 : 0 ) : 0 )

/*** GENERATED CODE ***/
#define is_XDIGIT_cp_high(cp)                                               \
( inRANGE_helper_(UV, cp, 0xFF10, 0xFF19) || ( 0xFF19 < cp &&               \
( inRANGE_helper_(UV, cp, 0xFF21, 0xFF26) || inRANGE_helper_(UV, cp, 0xFF41, 0xFF46) ) ) )

/*
	XPERLSPACE: \p{XPerlSpace}

	\p{XPerlSpace}
*/
/*** GENERATED CODE ***/
#define is_XPERLSPACE_high(s)                                               \
( ( 0xBD == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x62 == ((const U8*)s)[1] ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x41 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xAA) ) ? 3 : 0 )\
    : ( 0x42 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) || 0x56 == ((const U8*)s)[2] ) ? 3 : 0 )\
    : ( ( 0x43 == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( ( ( 0xCE == ((const U8*)s)[0] ) && ( 0x41 == ((const U8*)s)[1] ) ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )

/*** GENERATED CODE ***/
#define is_XPERLSPACE_cp_high(cp)                                           \
( 0x1680 == cp || ( 0x1680 < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x2000, 0x200A) || ( 0x200A < cp &&               \
( inRANGE_helper_(UV, cp, 0x2028, 0x2029) || ( 0x2029 < cp &&               \
( 0x202F == cp || ( 0x202F < cp &&                                          \
( 0x205F == cp || 0x3000 == cp ) ) ) ) ) ) ) ) )

/*
	SPACE: Backwards \p{XPerlSpace}

	\p{XPerlSpace}
*/
/*** GENERATED CODE ***/
#define is_SPACE_utf8_safe_backwards(s,e)                                   \
( ((s) - (e) > 2) ?                                                         \
    ( ( '\t' == *((const U8*)s - 1) || inRANGE_helper_(U8, *((const U8*)s - 1), '\v', '\r') || 0x15 == *((const U8*)s - 1) || '\n' == *((const U8*)s - 1) || ' ' == *((const U8*)s - 1) ) ? 1\
    : ( 0x41 == *((const U8*)s - 1) ) ?                                     \
	( ( 0x41 == *((const U8*)s - 2) ) ?                                 \
	    ( ( ( *((const U8*)s - 3) & 0xFB ) == 0xCA ) ? 3 : 0 )          \
	: ( 0x62 == *((const U8*)s - 2) ) ?                                 \
	    ( ( 0xBD == *((const U8*)s - 3) ) ? 3 : 0 )                     \
	: ( 0x78 == *((const U8*)s - 2) ) ? 2 : 0 )                         \
    : ( inRANGE_helper_(U8, *((const U8*)s - 1), 0x42, 0x48) || 0x51 == *((const U8*)s - 1) ) ?\
	( ( ( 0x41 == *((const U8*)s - 2) ) && ( 0xCA == *((const U8*)s - 3) ) ) ? 3 : 0 )\
    : ( inRANGE_helper_(U8, *((const U8*)s - 1), 0x49, 0x4A) ) ?            \
	( ( ( inRANGE_helper_(U8, *((const U8*)s - 2), 0x41, 0x42) ) && ( 0xCA == *((const U8*)s - 3) ) ) ? 3 : 0 )\
    : ( 0x56 == *((const U8*)s - 1) ) ?                                     \
	( ( ( 0x42 == *((const U8*)s - 2) ) && ( 0xCA == *((const U8*)s - 3) ) ) ? 3 : 0 )\
    : ( ( ( 0x72 == *((const U8*)s - 1) ) && ( 0x43 == *((const U8*)s - 2) ) ) && ( 0xCA == *((const U8*)s - 3) ) ) ? 3 : 0 )\
: ((s) - (e) > 1) ?                                                         \
    ( ( '\t' == *((const U8*)s - 1) || inRANGE_helper_(U8, *((const U8*)s - 1), '\v', '\r') || 0x15 == *((const U8*)s - 1) || '\n' == *((const U8*)s - 1) || ' ' == *((const U8*)s - 1) ) ? 1\
    : ( ( 0x41 == *((const U8*)s - 1) ) && ( 0x78 == *((const U8*)s - 2) ) ) ? 2 : 0 )\
: ((s) - (e) > 0) ?                                                         \
    ( '\t' == *((const U8*)s - 1) || inRANGE_helper_(U8, *((const U8*)s - 1), '\v', '\r') || 0x15 == *((const U8*)s - 1) || '\n' == *((const U8*)s - 1) || ' ' == *((const U8*)s - 1) )\
: 0 )

/*
	NONCHAR: Non character code points

	\p{_Perl_Nchar}
*/
/*** GENERATED CODE ***/
#define is_NONCHAR_utf8_safe(s,e)                                           \
( ( ( LIKELY((e) > (s)) ) && ( LIKELY(((e) - (s)) >= UTF8SKIP(s)) ) ) ? ( ( 0xDD == ((const U8*)s)[0] ) ?\
	    ( ( 0x72 == ((const U8*)s)[1] ) ?                               \
		( ( 0x55 == ((const U8*)s)[2] ) ?                           \
		    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB0, 0xBF) ) ? 4 : 0 )\
		: ( 0x56 == ((const U8*)s)[2] ) ?                           \
		    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xA0, 0xAF) ) ? 4 : 0 )\
		: ( ( 0x72 == ((const U8*)s)[2] ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x71, 0x72) ) ) ? 4 : 0 )\
	    : 0 )                                                           \
	: ( ((const U8*)s)[0] == 0xDF || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0xFD ) == 0xF5 ) ) ?\
	    ( ( ( ( 0x72 == ((const U8*)s)[1] ) && ( 0x72 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x71, 0x72) ) ) ? 4 : 0 )\
	: ( 0xED == ((const U8*)s)[0] ) ?                                   \
	    ( ( ( ( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF9 ) == 0xA9 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF1 ) == 0xB1 ) ) && ( 0x72 == ((const U8*)s)[2] ) ) && ( 0x72 == ((const U8*)s)[3] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[4], 0x71, 0x72) ) ) ? 5 : 0 )\
	: ( ( ( ( ( 0xEE == ((const U8*)s)[0] ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( 0x72 == ((const U8*)s)[2] ) ) && ( 0x72 == ((const U8*)s)[3] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[4], 0x71, 0x72) ) ) ? 5 : 0 ) : 0 )

/*
	SHORTER_NON_CHARS: # 4 bytes

	0xFDD0 - 0xFDEF
	0xFFFE - 0xFFFF
	0x1FFFE - 0x1FFFF
	0x2FFFE - 0x2FFFF
	0x3FFFE - 0x3FFFF
*/
/*** GENERATED CODE ***/
#define is_SHORTER_NON_CHARS_utf8(s)                                        \
( ( 0xDD == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x72 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x55 == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB0, 0xBF) ) ? 4 : 0 )\
	: ( 0x56 == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xA0, 0xAF) ) ? 4 : 0 )\
	: ( ( 0x72 == ((const U8*)s)[2] ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x71, 0x72) ) ) ? 4 : 0 )\
    : 0 )                                                                   \
: ( ( ( ( ((const U8*)s)[0] == 0xDF || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0xFD ) == 0xF5 ) ) && ( 0x72 == ((const U8*)s)[1] ) ) && ( 0x72 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x71, 0x72) ) ) ? 4 : 0 )

/*
	LARGER_NON_CHARS: # 5 bytes

	0x4FFFE - 0x4FFFF
	0x5FFFE - 0x5FFFF
	0x6FFFE - 0x6FFFF
	0x7FFFE - 0x7FFFF
	0x8FFFE - 0x8FFFF
	0x9FFFE - 0x9FFFF
	0xAFFFE - 0xAFFFF
	0xBFFFE - 0xBFFFF
	0xCFFFE - 0xCFFFF
	0xDFFFE - 0xDFFFF
	0xEFFFE - 0xEFFFF
	0xFFFFE - 0xFFFFF
	0x10FFFE - 0x10FFFF
*/
/*** GENERATED CODE ***/
#define is_LARGER_NON_CHARS_utf8(s)                                         \
( ( 0xED == ((const U8*)s)[0] ) ?                                           \
    ( ( ( ( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF9 ) == 0xA9 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF1 ) == 0xB1 ) ) && ( 0x72 == ((const U8*)s)[2] ) ) && ( 0x72 == ((const U8*)s)[3] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[4], 0x71, 0x72) ) ) ? 5 : 0 )\
: ( ( ( ( ( 0xEE == ((const U8*)s)[0] ) && ( 0x42 == ((const U8*)s)[1] ) ) && ( 0x72 == ((const U8*)s)[2] ) ) && ( 0x72 == ((const U8*)s)[3] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[4], 0x71, 0x72) ) ) ? 5 : 0 )

/*
	SURROGATE: Surrogate code points

	\p{_Perl_Surrogate}
*/
/*** GENERATED CODE ***/
#define is_SURROGATE_utf8(s)                                                \
( ( ( 0xDD == ((const U8*)s)[0] ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x64, 0x65) ) ) ? 4 : 0 )

/*** GENERATED CODE ***/
#define is_SURROGATE_utf8_safe(s,e)                                         \
( ( ( ( ( ( ((e) - (s)) >= 4 ) && ( 0xDD == ((const U8*)s)[0] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x64, 0x65) ) ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xBF) ) ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xA0, 0xBF) ) ) ? 4 : 0 )

/*
	QUOTEMETA: Meta-characters that \Q should quote

	\p{_Perl_Quotemeta}
*/
/*** GENERATED CODE ***/
#define is_QUOTEMETA_high(s)                                                \
( ( 0xAF == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x56 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xB7 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x57 == ((const U8*)s)[1] ) && ( 0x6A == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xBC == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x51 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[2] ) ? 3 : 0 )                           \
    : ( ( 0x52 == ((const U8*)s)[1] ) && ( 0x41 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xBD == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x62 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x41 == ((const U8*)s)[2] ) ? 3 : 0 )                           \
    : ( ( 0x70 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x62, 0x63) ) ) ? 3 : 0 )\
: ( 0xBE == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x41 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x52, 0x56) ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x41 == ((const U8*)s)[1] || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xAD, 0xBF) ) ?\
	3                                                                   \
    : ( 0x42 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xBE) ) ? 3 : 0 )\
    : ( 0x43 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA1, 0xB3) || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xB5, 0xBF) ) ? 3 : 0 )\
    : ( 0x44 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xAF) ) ? 3 : 0 )\
    : ( ( 0x53 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xB0, 0xBF) ) ) ? 3 : 0 )\
: ( 0xCB == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xA0, 0xA2) || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xA8, 0xBA) || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xBD, 0xBF) ) ?\
	3                                                                   \
    : ( 0x69 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xB5) ) ? 3 : 0 )\
    : ( ( 0x6A == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xB4, 0xBF) ) ) ? 3 : 0 )\
: ( 0xCC == ((const U8*)s)[0] ) ?                                           \
    3                                                                       \
: ( 0xCD == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[1]), 0xB0, 0xB3) ) ?\
	3                                                                   \
    : 0 )                                                                   \
: ( 0xCE == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x41 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA0, 0xA3) || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[2]), 0xA8, 0xBF) ) ? 3 : 0 )\
    : ( 0x42 == ((const U8*)s)[1] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xEF ) == 0xA0 ) ? 3 : 0 )\
    : ( ( 0x52 == ((const U8*)s)[1] ) && ( 0x45 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xDD == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x72 == ((const U8*)s)[1] ) ?                                       \
	( ( 0x4A == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x71, 0x72) ) ? 4 : 0 )\
	: ( 0x57 == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xA0, 0xAF) ) ? 4 : 0 )\
	: ( 0x59 == ((const U8*)s)[2] ) ?                                   \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x46, 0x47) ) ? 4 : 0 )\
	: ( 0x65 == ((const U8*)s)[2] ) ?                                   \
	    ( ( 0x72 == ((const U8*)s)[3] ) ? 4 : 0 )                       \
	: ( 0x70 == ((const U8*)s)[2] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[3] ) ? 4 : 0 )                       \
	: ( ( 0x72 == ((const U8*)s)[2] ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB0, 0xB8) ) ) ? 4 : 0 )\
    : 0 )                                                                   \
: ( 0xDF == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x56 == ((const U8*)s)[1] ) ?                                       \
	( ( ( 0x46 == ((const U8*)s)[2] ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x41, 0x44) ) ) ? 4 : 0 )\
    : ( ( ( 0x62 == ((const U8*)s)[1] ) && ( 0x52 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB3, 0xBA) ) ) ? 4 : 0 )\
: ( ( ( 0xED == ((const U8*)s)[0] ) && ( 0x6A == ((const U8*)s)[1] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x44) ) ) ? 5 : 0 )

/*
	MULTI_CHAR_FOLD: multi-char strings that are folded to by a single character

	%regcharclass_multi_char_folds::multi_char_folds('u', 'a')
*/
/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e)                            \
( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e)                            \
( ( 0x8E == ((const U8*)s)[0] ) ?                                           \
	( ( 0x72 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8E == ((const U8*)s)[2] ) ?                               \
		( ( 0x72 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( ((const U8*)s)[2] & 0xBE ) == 's' ) ? 3 : 0 )             \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8E == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x72 == ((const U8*)s)[2] ) ? 3 : 0 )                       \
	: ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ? 2 : 0 )                 \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ?\
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xAA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 3 : 0 )\
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) ?     \
	    ( ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] || 0x65 == ((const U8*)s)[1] ) ?      \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB3 == ((const U8*)s)[4] ) && ( 0x67 == ((const U8*)s)[5] ) ) ? 6 : 4 )\
		: 0 )                                                       \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x67 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAD == ((const U8*)s)[2] ) ?                               \
		( ( 0x49 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAD == ((const U8*)s)[4] ) ?                       \
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 0 )\
		    : ( ( 0xAF == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
		: 0 )                                                       \
	    : ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: 0 )                                                               \
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAD == ((const U8*)s)[2] ) && ( 0x5F == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAD == ((const U8*)s)[2] ) ?                               \
		( ( 0x49 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAD == ((const U8*)s)[4] ) ?                       \
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 0 )\
		    : ( ( 0xAF == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
		: ( 0x5F == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAD == ((const U8*)s)[4] ) ?                       \
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 4 )\
		    : ( ( 0xAF == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 4 )\
		: 0 )                                                       \
	    : ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB3 == ((const U8*)s)[4] ) && ( 0x67 == ((const U8*)s)[5] ) ) ? 6 : 4 )\
		: 0 )                                                       \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB3 == ((const U8*)s)[2] ) ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xB7 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x52 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( ( 0xB7 == ((const U8*)s)[3] ) && ( 0x53 == ((const U8*)s)[4] ) ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
	    : ( 0x62 == ((const U8*)s)[2] ) ?                               \
		( ( ( ( 0xB7 == ((const U8*)s)[3] ) && ( 0x52 == ((const U8*)s)[4] ) ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[5]) & 0xF7 ) == 0xA5 ) || ((const U8*)s)[5] == 0x52 || ((const U8*)s)[5] == 0x64 ) ) ? 6 : 0 )\
	    : ( ( ( ( 0x71 == ((const U8*)s)[2] ) && ( 0xB7 == ((const U8*)s)[3] ) ) && ( 0x52 == ((const U8*)s)[4] ) ) && ( 0x64 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
	: 0 )                                                               \
    : ( 0xBF == ((const U8*)s)[0] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x66, 0x67) ) ?          \
	    ( ( ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x48) ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
	: ( ( ( ( 0x69 == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF8 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0x6A ) ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
    : 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e)                            \
( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                                 \
	( ( ( 0xAA == ((const U8*)s)[1] ) && ( 0x71 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xAE == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8E == ((const U8*)s)[2] ) ?                               \
		( ( 0x72 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( ((const U8*)s)[2] & 0xBE ) == 's' ) ? 3 : 0 )             \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8E == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x72 == ((const U8*)s)[2] ) ? 3 : 0 )                       \
	: ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ? 2 : 0 )                 \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ?\
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xAA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 3 : 0 )\
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) ?     \
	    ( ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] || 0x65 == ((const U8*)s)[1] ) ?      \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x67 == ((const U8*)s)[1] ) && ( 0xAF == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAD == ((const U8*)s)[2] ) && ( 0x5F == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAD == ((const U8*)s)[2] ) ?                               \
		( ( 0x5F == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB3 == ((const U8*)s)[2] ) ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xBF == ((const U8*)s)[0] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x66, 0x67) ) ?          \
	    ( ( ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x48) ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
	: ( ( ( ( 0x69 == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF8 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0x6A ) ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 5 : 0 )\
    : 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e)                            \
( ((e)-(s) > 3) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAA == ((const U8*)s)[1] ) && ( 0x71 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xAE == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8E == ((const U8*)s)[2] ) ?                               \
		( ( 0x72 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( ((const U8*)s)[2] & 0xBE ) == 's' ) ? 3 : 0 )             \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8E == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x72 == ((const U8*)s)[2] ) ? 3 : 0 )                       \
	: ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ? 2 : 0 )                 \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ?\
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xAA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 3 : 0 )\
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) ?     \
	    ( ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] || 0x65 == ((const U8*)s)[1] ) ?      \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x67 == ((const U8*)s)[1] ) && ( 0xAF == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAD == ((const U8*)s)[2] ) && ( 0x5F == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAD == ((const U8*)s)[2] ) ?                               \
		( ( 0x5F == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 4 : 0 )                   \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB3 == ((const U8*)s)[2] ) ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 4 : 0 )\
    : 0 )                                                                   \
: ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAA == ((const U8*)s)[1] ) && ( 0x71 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xAE == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x72 == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBE ) == 's' ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8E == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x72 == ((const U8*)s)[2] ) ? 3 : 0 )                       \
	: ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ? 2 : 0 )                 \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ?\
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ( 0xAA == ((const U8*)s)[0] ) && ( 0x6A == ((const U8*)s)[1] ) ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 3 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) && ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ) ? 2 : 0 )\
: 0 )


/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_utf8_safe(s,e)                                   \
( ((e)-(s) > 5) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAA == ((const U8*)s)[1] ) && ( 0x71 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xAE == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ? is_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e) : is_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e) )\
: ((e)-(s) > 4) ? is_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e) : is_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e) )

/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e)                          \
( ( 0x52 == ((const U8*)s)[1] ) ?                                           \
	    ( ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( ( 0xB7 == ((const U8*)s)[3] ) && ( 0x53 == ((const U8*)s)[4] ) ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 0x587 : 0 )\
	    : ( 0x62 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB7 == ((const U8*)s)[3] ) && ( 0x52 == ((const U8*)s)[4] ) ) ? ( ( 0x46 == ((const U8*)s)[5] ) ? 0xFB14\
			: ( 0x52 == ((const U8*)s)[5] ) ? 0xFB15            \
			: ( 0x54 == ((const U8*)s)[5] ) ? 0xFB17            \
			: ( 0x64 == ((const U8*)s)[5] ) ? 0xFB13 : 0 ) : 0 )\
	    : ( ( ( ( 0x71 == ((const U8*)s)[2] ) && ( 0xB7 == ((const U8*)s)[3] ) ) && ( 0x52 == ((const U8*)s)[4] ) ) && ( 0x64 == ((const U8*)s)[5] ) ) ? 0xFB16 : 0 )\
	: 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e)                          \
( ( 0xBF == ((const U8*)s)[0] ) ?                                           \
	( ( 0x66 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F80 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F81 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F82 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F83 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F84 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F85 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F86 : 0 )\
	    : ( ( ( 0x48 == ((const U8*)s)[2] ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F87 : 0 )\
	: ( 0x67 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F90 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F91 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F92 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F93 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F94 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F95 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F96 : 0 )\
	    : ( ( ( 0x48 == ((const U8*)s)[2] ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F97 : 0 )\
	: ( 0x69 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA0 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA1 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA2 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA3 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA4 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA5 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA6 : 0 )\
	    : ( 0x48 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA7 : 0 )\
	    : ( 0x57 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FB2 : 0 )\
	    : ( 0x62 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FC2 : 0 )\
	    : ( ( ( 0x6A == ((const U8*)s)[2] ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FF2 : 0 )\
	: 0 )                                                               \
    : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e)                          \
( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                                 \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e)                          \
( ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                                 \
	( ( ( 0xAE == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8E == ((const U8*)s)[2] ) ?                               \
		( ( 0x72 == ((const U8*)s)[3] ) ? 0x59 : 0 )                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 's' ) ? 0x59                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 't' ) ? 0xFB05 : 0 )        \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8E == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x72 == ((const U8*)s)[2] ) ? 0x59 : 0 )                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xAA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 0x149 : 0 )\
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x53 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FB4 : 0 )\
	: ( 0x55 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FC4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB3 == ((const U8*)s)[4] ) && ( 0x67 == ((const U8*)s)[5] ) ) ? 0x1FB7 : 0x1FB6 )\
		: 0 )                                                       \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FB3 : 0 )\
	: ( 0x65 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB3 == ((const U8*)s)[4] ) && ( 0x67 == ((const U8*)s)[5] ) ) ? 0x1FC7 : 0x1FC6 )\
		: 0 )                                                       \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FC3 : 0 )\
	: ( 0x67 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAD == ((const U8*)s)[2] ) ?                               \
		( ( 0x49 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAD == ((const U8*)s)[4] ) ?                       \
			( ( 0x41 == ((const U8*)s)[5] ) ? 0x1FD2            \
			: ( 0x42 == ((const U8*)s)[5] ) ? 0x390 : 0 )       \
		    : ( ( 0xAF == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 0x1FD7 : 0 )\
		: 0 )                                                       \
	    : ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FD6 : 0 )\
	: 0 )                                                               \
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAD == ((const U8*)s)[2] ) && ( 0x5F == ((const U8*)s)[3] ) ) ? 0x1FE4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAD == ((const U8*)s)[2] ) ?                               \
		( ( 0x49 == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAD == ((const U8*)s)[4] ) ?                       \
			( ( 0x41 == ((const U8*)s)[5] ) ? 0x1FE2            \
			: ( 0x42 == ((const U8*)s)[5] ) ? 0x3B0 : 0 )       \
		    : ( ( 0xAF == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 0x1FE7 : 0 )\
		: ( 0x5F == ((const U8*)s)[3] ) ?                           \
		    ( ( 0xAD == ((const U8*)s)[4] ) ?                       \
			( ( 0x41 == ((const U8*)s)[5] ) ? 0x1F52            \
			: ( 0x42 == ((const U8*)s)[5] ) ? 0x1F54 : 0x1F50 ) \
		    : ( ( 0xAF == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 0x1F56 : 0x1F50 )\
		: 0 )                                                       \
	    : ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FE6 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ?                           \
		    ( ( ( 0xB3 == ((const U8*)s)[4] ) && ( 0x67 == ((const U8*)s)[5] ) ) ? 0x1FF7 : 0x1FF6 )\
		: 0 )                                                       \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FF3 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB3 == ((const U8*)s)[2] ) ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FF4 : 0 )\
    : ( 0xB7 == ((const U8*)s)[0] ) ? what_MULTI_CHAR_FOLD_utf8_safe_part0_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part1_(s,e) )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part4_(s,e)                          \
( ( 0x42 == ((const U8*)s)[1] ) ?                                           \
	    ( ( ( 0xAD == ((const U8*)s)[2] ) && ( 0x5F == ((const U8*)s)[3] ) ) ? 0x1FE4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAD == ((const U8*)s)[2] ) ?                               \
		( ( 0x5F == ((const U8*)s)[3] ) ? 0x1F50 : 0 )              \
	    : ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FE6 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FF6 : 0 )              \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FF3 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB3 == ((const U8*)s)[2] ) ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FF4 : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part5_(s,e)                          \
( ( 0xBF == ((const U8*)s)[0] ) ?                                           \
	( ( 0x66 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F80 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F81 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F82 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F83 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F84 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F85 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F86 : 0 )\
	    : ( ( ( 0x48 == ((const U8*)s)[2] ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F87 : 0 )\
	: ( 0x67 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F90 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F91 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F92 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F93 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F94 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F95 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F96 : 0 )\
	    : ( ( ( 0x48 == ((const U8*)s)[2] ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1F97 : 0 )\
	: ( 0x69 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x41 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA0 : 0 )\
	    : ( 0x42 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA1 : 0 )\
	    : ( 0x43 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA2 : 0 )\
	    : ( 0x44 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA3 : 0 )\
	    : ( 0x45 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA4 : 0 )\
	    : ( 0x46 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA5 : 0 )\
	    : ( 0x47 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA6 : 0 )\
	    : ( 0x48 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FA7 : 0 )\
	    : ( 0x57 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FB2 : 0 )\
	    : ( 0x62 == ((const U8*)s)[2] ) ?                               \
		( ( ( 0xB3 == ((const U8*)s)[3] ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FC2 : 0 )\
	    : ( ( ( 0x6A == ((const U8*)s)[2] ) && ( 0xB3 == ((const U8*)s)[3] ) ) && ( 0x67 == ((const U8*)s)[4] ) ) ? 0x1FF2 : 0 )\
	: 0 )                                                               \
    : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part6_(s,e)                          \
( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                                 \
	( ( ( 0xAA == ((const U8*)s)[1] ) && ( 0x71 == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xAE == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8E == ((const U8*)s)[2] ) ?                               \
		( ( 0x72 == ((const U8*)s)[3] ) ? 0x59 : 0 )                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 's' ) ? 0x59                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 't' ) ? 0xFB05 : 0 )        \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8E == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x72 == ((const U8*)s)[2] ) ? 0x59 : 0 )                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xAA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 0x149 : 0 )\
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x53 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FB4 : 0 )\
	: ( 0x55 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FC4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FB6 : 0 )              \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FB3 : 0 )\
	: ( 0x65 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FC6 : 0 )              \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FC3 : 0 )\
	: ( ( ( 0x67 == ((const U8*)s)[1] ) && ( 0xAF == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FD6 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAD == ((const U8*)s)[2] ) && ( 0x5F == ((const U8*)s)[3] ) ) ? 0x1FE4 : 0 )\
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAD == ((const U8*)s)[2] ) ?                               \
		( ( 0x5F == ((const U8*)s)[3] ) ? 0x1F50 : 0 )              \
	    : ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FE6 : 0 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FF6 : 0 )              \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FF3 : 0 )\
	: ( ( ( 0x55 == ((const U8*)s)[1] ) && ( 0xB3 == ((const U8*)s)[2] ) ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FF4 : 0 )\
    : 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe_part7_(s,e)                          \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAA == ((const U8*)s)[1] ) && ( 0x71 == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xAE == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 's' ) ? 0x59                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 't' ) ? 0xFB05 : 0 )        \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8E == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x72 == ((const U8*)s)[2] ) ? 0x59 : 0 )                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( ( ( 0xAA == ((const U8*)s)[0] ) && ( 0x6A == ((const U8*)s)[1] ) ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 0x149 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 0xFB00                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : 0 )                                                                   \
: 0 )


/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_utf8_safe(s,e)                                 \
( ((e)-(s) > 5) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAA == ((const U8*)s)[1] ) && ( 0x71 == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ? what_MULTI_CHAR_FOLD_utf8_safe_part2_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part3_(s,e) )\
: ((e)-(s) > 4) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'a' ) ?                             \
	( ( ( 0xAA == ((const U8*)s)[1] ) && ( 0x71 == ((const U8*)s)[2] ) ) ? 0x1E9A : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'h' ) ?                             \
	( ( ( 0xAE == ((const U8*)s)[1] ) && ( 0x58 == ((const U8*)s)[2] ) ) ? 0x1E96 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'i' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 0x130 : 0 )\
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x8E == ((const U8*)s)[2] ) ?                               \
		( ( 0x72 == ((const U8*)s)[3] ) ? 0x59 : 0 )                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 's' ) ? 0x59                \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 't' ) ? 0xFB05 : 0 )        \
	: 0 )                                                               \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'j' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x53 == ((const U8*)s)[2] ) ) ? 0x1F0 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( 0x8E == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0x72 == ((const U8*)s)[2] ) ? 0x59 : 0 )                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 't' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x49 == ((const U8*)s)[2] ) ) ? 0x1E97 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E98 : 0 )\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ?                             \
	( ( ( 0xAD == ((const U8*)s)[1] ) && ( 0x51 == ((const U8*)s)[2] ) ) ? 0x1E99 : 0 )\
    : ( 0xAA == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x6A == ((const U8*)s)[1] ) && ( ( ((const U8*)s)[2] & 0xBF ) == 'n' ) ) ? 0x149 : 0 )\
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x53 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FB4 : 0 )\
	: ( 0x55 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FC4 : 0 )\
	: ( 0x58 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FB6 : 0 )              \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FB3 : 0 )\
	: ( 0x65 == ((const U8*)s)[1] ) ?                                   \
	    ( ( 0xAF == ((const U8*)s)[2] ) ?                               \
		( ( 0x43 == ((const U8*)s)[3] ) ? 0x1FC6 : 0 )              \
	    : ( ( 0xB3 == ((const U8*)s)[2] ) && ( 0x67 == ((const U8*)s)[3] ) ) ? 0x1FC3 : 0 )\
	: ( ( ( 0x67 == ((const U8*)s)[1] ) && ( 0xAF == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 0x1FD6 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ? what_MULTI_CHAR_FOLD_utf8_safe_part4_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part5_(s,e) )\
: ((e)-(s) > 3) ? what_MULTI_CHAR_FOLD_utf8_safe_part6_(s,e) : what_MULTI_CHAR_FOLD_utf8_safe_part7_(s,e) )

/*
	MULTI_CHAR_FOLD: multi-char strings that are folded to by a single character

	%regcharclass_multi_char_folds::multi_char_folds('l', 'a')
*/
/*** GENERATED CODE ***/
#define is_MULTI_CHAR_FOLD_latin1_safe(s,e)                                 \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ? 3 : 2 )\
	: ( ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) && ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ) ? 2 : 0 )\
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ) ? 2 : 0 )\
    : ( ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) && ( ( ((const U8*)s)[1] & 0xBE ) == 's' ) ) ? 2 : 0 )\
: 0 )

/*** GENERATED CODE ***/
#define what_MULTI_CHAR_FOLD_latin1_safe(s,e)                               \
( ((e)-(s) > 2) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ?                         \
	    ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) ? 0xFB03              \
	    : ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ? 0xFB04 : 0xFB00 )   \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : 0 )                                                                   \
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 0xFB00                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'i' ) ? 0xFB01                  \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 'l' ) ? 0xFB02 : 0 )            \
    : ( ( ((const U8*)s)[0] & 0xBF ) == 's' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 's' ) ? 0x59                    \
	: ( ( ((const U8*)s)[1] & 0xBF ) == 't' ) ? 0xFB05 : 0 )            \
    : 0 )                                                                   \
: 0 )

/*
	THREE_CHAR_FOLD: A three-character multi-char fold

	%regcharclass_multi_char_folds::multi_char_folds('u', '3')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_utf8_safe(s,e)                                   \
( ((e)-(s) > 5) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) && ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ) ? 3 : 0 )\
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x58 == ((const U8*)s)[1] || 0x65 == ((const U8*)s)[1] ) ?      \
	    ( ( ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) && ( 0xB3 == ((const U8*)s)[4] ) ) && ( 0x67 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
	: ( ( ( 0x67 == ((const U8*)s)[1] ) && ( 0xAD == ((const U8*)s)[2] ) ) && ( 0x49 == ((const U8*)s)[3] ) ) ? ( ( 0xAD == ((const U8*)s)[4] ) ?\
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 0 )\
		    : ( ( 0xAF == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 ) : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAD == ((const U8*)s)[2] ) && ( 0x49 == ((const U8*)s)[3] || 0x5F == ((const U8*)s)[3] ) ) ? ( ( 0xAD == ((const U8*)s)[4] ) ?\
			( ( inRANGE_helper_(U8, ((const U8*)s)[5], 0x41, 0x42) ) ? 6 : 0 )\
		    : ( ( 0xAF == ((const U8*)s)[4] ) && ( 0x43 == ((const U8*)s)[5] ) ) ? 6 : 0 ) : 0 )\
	: ( ( ( ( ( 0x4A == ((const U8*)s)[1] ) && ( 0xAF == ((const U8*)s)[2] ) ) && ( 0x43 == ((const U8*)s)[3] ) ) && ( 0xB3 == ((const U8*)s)[4] ) ) && ( 0x67 == ((const U8*)s)[5] ) ) ? 6 : 0 )\
    : 0 )                                                                   \
: ( ( ( ((e)-(s) > 2) && ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ) && ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ) && ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ) ? 3 : 0 )

/*
	THREE_CHAR_FOLD: A three-character multi-char fold

	%regcharclass_multi_char_folds::multi_char_folds('l', '3')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_latin1_safe(s,e)                                 \
( ( ( ( ( ((e) - (s)) >= 3 ) && ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ) && ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ) && ( ( ( ((const U8*)s)[2] & 0xBF ) == 'i' ) || ( ( ((const U8*)s)[2] & 0xBF ) == 'l' ) ) ) ? 3 : 0 )

/*
	THREE_CHAR_FOLD_HEAD: The first two of three-character multi-char folds

	%regcharclass_multi_char_folds::multi_char_folds('u', 'h')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_HEAD_utf8_safe(s,e)                              \
( ((e)-(s) > 3) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 2 : 1 )                 \
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xAA == ((const U8*)s)[0] ) ?                                       \
	( ( 0x6A == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) ? 2   \
	: ( 0x58 == ((const U8*)s)[1] || 0x65 == ((const U8*)s)[1] ) ?      \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: ( 0x67 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAD == ((const U8*)s)[2] ) && ( 0x49 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: 0 )                                                               \
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( 0x42 == ((const U8*)s)[1] || 0x55 == ((const U8*)s)[1] ) ? 2    \
	: ( 0x46 == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAD == ((const U8*)s)[2] ) && ( 0x49 == ((const U8*)s)[3] || 0x5F == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: ( 0x4A == ((const U8*)s)[1] ) ?                                   \
	    ( ( ( 0xAF == ((const U8*)s)[2] ) && ( 0x43 == ((const U8*)s)[3] ) ) ? 4 : 2 )\
	: 0 )                                                               \
    : ( 0xB7 == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x52 == ((const U8*)s)[1] ) && ( 0x46 == ((const U8*)s)[2] || 0x62 == ((const U8*)s)[2] || 0x71 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xBF == ((const U8*)s)[0] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x66, 0x67) ) ?          \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x48) ) ? 3 : 0 )\
	: ( ( 0x69 == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF8 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0x6A ) ) ? 3 : 0 )\
    : 0 )                                                                   \
: ((e)-(s) > 2) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 2 : 1 )                 \
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xAA == ((const U8*)s)[0] ) ?                                       \
	( ( 0x6A == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF7 ) == 0xB1 ) || ((const U8*)s)[1] == 0x65 ) ? 2 : 0 )\
    : ( 0xB4 == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( ((const U8*)s)[1] & 0xFB ) == 0x42 ) || ((const U8*)s)[1] == 0x4A || ((const U8*)s)[1] == 0x55 ) ? 2 : 0 )\
    : ( 0xB7 == ((const U8*)s)[0] ) ?                                       \
	( ( ( 0x52 == ((const U8*)s)[1] ) && ( 0x46 == ((const U8*)s)[2] || 0x62 == ((const U8*)s)[2] || 0x71 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
    : ( 0xBF == ((const U8*)s)[0] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x66, 0x67) ) ?          \
	    ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x41, 0x48) ) ? 3 : 0 )\
	: ( ( 0x69 == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF8 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFB ) == 0xB0 ) || ((const U8*)s)[2] == 0x6A ) ) ? 3 : 0 )\
    : 0 )                                                                   \
: ((e)-(s) > 1) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 2 : 1 )                 \
    : ( 0x8E == ((const U8*)s)[0] ) ?                                       \
	( ( 0x72 == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xAA == ((const U8*)s)[0] ) ?                                       \
	( ( 0x6A == ((const U8*)s)[1] ) ? 2 : 0 )                           \
    : ( 0xB3 == ((const U8*)s)[0] ) ?                                       \
	( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xFD ) == 0xAC ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[1]) & 0xF7 ) == 0xB1 ) || ((const U8*)s)[1] == 0x65 ) ? 2 : 0 )\
    : ( ( 0xB4 == ((const U8*)s)[0] ) && ( ( ( ((const U8*)s)[1] & 0xFB ) == 0x42 ) || ((const U8*)s)[1] == 0x4A || ((const U8*)s)[1] == 0x55 ) ) ? 2 : 0 )\
: ((e)-(s) > 0) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0x9F ) == 'f' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) )\
: 0 )

/*
	THREE_CHAR_FOLD_HEAD: The first two of three-character multi-char folds

	%regcharclass_multi_char_folds::multi_char_folds('l', 'h')
*/
/*** GENERATED CODE ***/
#define is_THREE_CHAR_FOLD_HEAD_latin1_safe(s,e)                            \
( ((e)-(s) > 1) ?                                                           \
    ( ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'w' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) ) ? 1\
    : ( ( ((const U8*)s)[0] & 0xBF ) == 'f' ) ?                             \
	( ( ( ((const U8*)s)[1] & 0xBF ) == 'f' ) ? 2 : 1 )                 \
    : 0 )                                                                   \
: ((e)-(s) > 0) ?                                                           \
    ( ( ( ((const U8*)s)[0] & 0xAF ) == 'a' ) || ( ( ((const U8*)s)[0] & 0x9F ) == 'f' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 'h' ) || ( ( ((const U8*)s)[0] & 0xBE ) == 's' ) || ( ( ((const U8*)s)[0] & 0xBF ) == 'y' ) )\
: 0 )

/*
	FOLDS_TO_MULTI: characters that fold to multi-char strings

	\p{_Perl_Folds_To_Multi_Char}
*/
/*** GENERATED CODE ***/
#define is_FOLDS_TO_MULTI_utf8(s)                                           \
( ( 0x80 == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x72 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0x8C == ((const U8*)s)[0] || 0x9B == ((const U8*)s)[0] || inRANGE_helper_(U8, ((const U8*)s)[0], 0xB2, 0xB3) ) ?\
    ( ( 0x57 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0x8D == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x4A == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xB7 == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x53 == ((const U8*)s)[1] ) && ( 0x48 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xBF == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x62 == ((const U8*)s)[1] ) ?                                       \
	( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x64, 0x68) || 0x71 == ((const U8*)s)[2] ) ? 3 : 0 )\
    : ( 0x68 == ((const U8*)s)[1] ) ?                                       \
	( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF9 ) == 0xB0 ) ? 3 : 0 )\
    : ( 0x6A == ((const U8*)s)[1] ) ?                                       \
	3                                                                   \
    : ( 0x70 == ((const U8*)s)[1] ) ?                                       \
	( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF0 ) == 0xA0 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xFA ) == 0xB2 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF7 ) == 0xB4 ) ) ? 3 : 0 )\
    : ( 0x71 == ((const U8*)s)[1] ) ?                                       \
	( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xEA ) == 0xA2 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF7 ) == 0xA4 ) ) ? 3 : 0 )\
    : ( ( 0x72 == ((const U8*)s)[1] ) && ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xEA ) == 0xA2 ) || ((const U8*)s)[2] == 0x45 || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[2]) & 0xF7 ) == 0xB4 ) ) ) ? 3 : 0 )\
: ( ( ( ( 0xDD == ((const U8*)s)[0] ) && ( 0x71 == ((const U8*)s)[1] ) ) && ( 0x66 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xA0, 0xA6) || inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[3]), 0xB3, 0xB7) ) ) ? 4 : 0 )

/*
	PROBLEMATIC_LOCALE_FOLD: characters whose fold is problematic under locale

	\p{_Perl_Problematic_Locale_Folds}
*/
/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLD_utf8(s)                                  \
( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0x80 ) == 0x00 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0xE0 ) == 0x80 ) ) ? 1\
: ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[0]), 0xC5, 0xC7) ) ?\
    2                                                                       \
: ( 0x8C == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x57, 0x58) ) ? 2 : 0 )      \
: ( 0x8D == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x4A == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0x8E == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x66 == ((const U8*)s)[1] || 0x72 == ((const U8*)s)[1] ) ? 2 : 0 )  \
: ( 0x9B == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x57 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xAD == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x48 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( inRANGE_helper_(U8, ((const U8*)s)[0], 0xB2, 0xB3) ) ?                  \
    ( ( 0x6A == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xBF == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x62 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x64, 0x68) || 0x71 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x4A == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x51, 0x52) ) ) ? 3 : 0 )\
: ( ( ( ( 0xDD == ((const U8*)s)[0] ) && ( 0x71 == ((const U8*)s)[1] ) ) && ( 0x66 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x41, 0x47) ) ) ? 4 : 0 )

/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLD_cp(cp)                                   \
( cp <= 0xFF || ( 0xFF < cp &&                                              \
( inRANGE_helper_(UV, cp, 0x130, 0x131) || ( 0x131 < cp &&                  \
( 0x149 == cp || ( 0x149 < cp &&                                            \
( 0x178 == cp || ( 0x178 < cp &&                                            \
( 0x17F == cp || ( 0x17F < cp &&                                            \
( 0x1F0 == cp || ( 0x1F0 < cp &&                                            \
( 0x307 == cp || ( 0x307 < cp &&                                            \
( 0x39C == cp || ( 0x39C < cp &&                                            \
( 0x3BC == cp || ( 0x3BC < cp &&                                            \
( inRANGE_helper_(UV, cp, 0x1E96, 0x1E9A) || ( 0x1E9A < cp &&               \
( 0x1E9E == cp || ( 0x1E9E < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x212A, 0x212B) || inRANGE_helper_(UV, cp, 0xFB00, 0xFB06) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) )

/*
	PROBLEMATIC_LOCALE_FOLDEDS_START: The first folded character of folds which are problematic under locale

	\p{_Perl_Problematic_Locale_Foldeds_Start}
*/
/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLDEDS_START_utf8(s)                         \
( ( ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0x80 ) == 0x00 ) || ( ( NATIVE_UTF8_TO_I8(((const U8*)s)[0]) & 0xE0 ) == 0x80 ) ) ? 1\
: ( inRANGE_helper_(U8, NATIVE_UTF8_TO_I8(((const U8*)s)[0]), 0xC5, 0xC7) ) ?\
    2                                                                       \
: ( 0x8C == ((const U8*)s)[0] ) ?                                           \
    ( ( inRANGE_helper_(U8, ((const U8*)s)[1], 0x57, 0x58) ) ? 2 : 0 )      \
: ( 0x8D == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x4A == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0x8E == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x66 == ((const U8*)s)[1] || 0x72 == ((const U8*)s)[1] ) ? 2 : 0 )  \
: ( 0x9B == ((const U8*)s)[0] ) ?                                           \
    ( ( 0x57 == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xAA == ((const U8*)s)[0] || inRANGE_helper_(U8, ((const U8*)s)[0], 0xB2, 0xB3) ) ?\
    ( ( 0x6A == ((const U8*)s)[1] ) ? 2 : 0 )                               \
: ( 0xBF == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x62 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x64, 0x68) || 0x71 == ((const U8*)s)[2] ) ) ? 3 : 0 )\
: ( 0xCA == ((const U8*)s)[0] ) ?                                           \
    ( ( ( 0x4A == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x51, 0x52) ) ) ? 3 : 0 )\
: ( ( ( ( 0xDD == ((const U8*)s)[0] ) && ( 0x71 == ((const U8*)s)[1] ) ) && ( 0x66 == ((const U8*)s)[2] ) ) && ( inRANGE_helper_(U8, ((const U8*)s)[3], 0x41, 0x47) ) ) ? 4 : 0 )

/*** GENERATED CODE ***/
#define is_PROBLEMATIC_LOCALE_FOLDEDS_START_cp(cp)                          \
( cp <= 0xFF || ( 0xFF < cp &&                                              \
( inRANGE_helper_(UV, cp, 0x130, 0x131) || ( 0x131 < cp &&                  \
( 0x149 == cp || ( 0x149 < cp &&                                            \
( 0x178 == cp || ( 0x178 < cp &&                                            \
( 0x17F == cp || ( 0x17F < cp &&                                            \
( 0x1F0 == cp || ( 0x1F0 < cp &&                                            \
( 0x2BC == cp || ( 0x2BC < cp &&                                            \
( 0x39C == cp || ( 0x39C < cp &&                                            \
( 0x3BC == cp || ( 0x3BC < cp &&                                            \
( inRANGE_helper_(UV, cp, 0x1E96, 0x1E9A) || ( 0x1E9A < cp &&               \
( 0x1E9E == cp || ( 0x1E9E < cp &&                                          \
( inRANGE_helper_(UV, cp, 0x212A, 0x212B) || inRANGE_helper_(UV, cp, 0xFB00, 0xFB06) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) ) )

/*
	PATWS: pattern white space

	\p{_Perl_PatWS}
*/
/*** GENERATED CODE ***/
#define is_PATWS_safe(s,e,is_utf8)                                          \
( ( LIKELY((e) > (s)) ) ?                                                   \
    ( ( '\t' == ((const U8*)s)[0] || inRANGE_helper_(U8, ((const U8*)s)[0], '\v', '\r') || 0x15 == ((const U8*)s)[0] || '\n' == ((const U8*)s)[0] || ' ' == ((const U8*)s)[0] ) ? 1\
    : ( ( is_utf8 && LIKELY(((e) - (s)) >= UTF8SKIP(s)) ) && ( 0xCA == ((const U8*)s)[0] ) ) ? ( ( 0x41 == ((const U8*)s)[1] ) ?\
		    ( ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x55, 0x56) ) ? 3 : 0 )\
		: ( ( 0x42 == ((const U8*)s)[1] ) && ( inRANGE_helper_(U8, ((const U8*)s)[2], 0x49, 0x4A) ) ) ? 3 : 0 ) : 0 )\
: 0 )

/*
	HANGUL_ED: Hangul syllables whose first UTF-8 byte is \xED

	0x1 - 0x0
*/
/*** GENERATED CODE ***/
#define is_HANGUL_ED_utf8_safe(s,e)                                         \
( 0 )

#endif	/* EBCDIC 037 */

#endif /* PERL_REGCHARCLASS_H_ */

/* Generated from:
 * 688d673ec947f7ccf898b4eae9848139d4d33676b688dee54f449f8bf9d3bbd2 lib/Unicode/UCD.pm
 * eb840f36e0a7446293578c684a54c6d83d249abde7bdd4dfa89794af1d7fe9e9 lib/unicore/ArabicShaping.txt
 * 333ae1e99db0504ca8a046a07dc45b5e7aa91869c685e6bf955ebe674804827a lib/unicore/BidiBrackets.txt
 * b4b9e1d87d8ea273613880de9d2b2f0b0b696244b42152bfa0a3106e7d983a20 lib/unicore/BidiMirroring.txt
 * 529dc5d0f6386d52f2f56e004bbfab48ce2d587eea9d38ba546c4052491bd820 lib/unicore/Blocks.txt
 * cdd49e55eae3bbf1f0a3f6580c974a0263cb86a6a08daa10fbf705b4808a56f7 lib/unicore/CaseFolding.txt
 * 3b019c0a33c3140cbc920c078f4f9af2680ba4f71869c8d4de5190667c70b6a3 lib/unicore/CompositionExclusions.txt
 * 7570877e0fa197c45338f7c41a02636da4e14c8dba6a3611a01cd30bf329d5ca lib/unicore/DAge.txt
 * d367290bc0867e6b484c68370530bdd1a08b6b32404601b8c7accaf83e05628d lib/unicore/DCoreProperties.txt
 * d5687a48c95c7d6e1ec59cb29c0f2e8b052018eb069a4371b7368d0561e12a29 lib/unicore/DNormalizationProps.txt
 * 743e7bc435c04ab1a8459710b1c3cad56eedced5b806b4659b6e69b85d0adf2a lib/unicore/EastAsianWidth.txt
 * f2e04bae8c856fad3a16353a99d4cc2de6c72770260379f5e4974a97548aad2a lib/unicore/EquivalentUnifiedIdeograph.txt
 * 9a3ab36d36a22bdb84de7a17b17e9b9c242134f0080f0a8b4b28d209465a8fc8 lib/unicore/HangulSyllableType.txt
 * 790bc9595795c0e0a3860a21a7f97157a134b61a4fc4ab03c7d315d07c9a6eb7 lib/unicore/IdStatus.txt
 * 71d3ed8f15cd5d8cd00cdebe62015ff26356462774b261b4a2b83d3bf46b1639 lib/unicore/IdType.txt
 * 0ce56c1294da405c0a0a0071582ac839fd229bbf97bdd260462ee571309d4ec4 lib/unicore/IndicPositionalCategory.txt
 * ffae561a51b47ddbbe267fdd8505ac3776b85b2932268809127acee84200b573 lib/unicore/IndicSyllabicCategory.txt
 * 14733bcb6731ae0c07485bf59a41cb3db08785a50bd2b46b836b4341eab7ee46 lib/unicore/Jamo.txt
 * 012bca868e2c4e59a5a10a7546baf0c6fb1b2ef458c277f054915c8a49d292bf lib/unicore/LineBreak.txt
 * 3e39509e8fae3e5d50ba73759d0b97194501d14a9c63107a6372a46b38be18e8 lib/unicore/NameAliases.txt
 * 1d5202155f14841973aa540b1625f4befbde185ac77ce5aceaaaa0501a68bd66 lib/unicore/NamedSequences.txt
 * fb9ac8cc154a80cad6caac9897af55a4e75176af6f4e2bb6edc2bf8b1d57f326 lib/unicore/NormTest.txt
 * e05c0a2811d113dae4abd832884199a3ea8d187ee1b872d8240a788a96540bfd lib/unicore/PropList.txt
 * 13a7666843abea5c6b7eb8c057c57ab9bb2ba96cfc936e204224dd67d71cafad lib/unicore/PropValueAliases.txt
 * e4935149af407fa455901832b710bccb63d2453e46d09190e234d019bcfbba45 lib/unicore/PropertyAliases.txt
 * 7e07313d9d0bee42220c476b64485995130ae30917bbcf7780b602d677d7e33f lib/unicore/ScriptExtensions.txt
 * cca85d830f46aece2e7c1459ef1249993dca8f2e46d51e869255be140d7ea4b0 lib/unicore/Scripts.txt
 * 78b29c64b5840d25c11a9f31b665ee551b8a499eca6c70d770fcad7dd710f494 lib/unicore/SpecialCasing.txt
 * 806e9aed65037197f1ec85e12be6e8cd870fc5608b4de0fffd990f689f376a73 lib/unicore/UnicodeData.txt
 * ca6d332f485a6f5f452b29b4a74146af0f2c17b7577aa4c821d597210f70611a lib/unicore/VerticalOrientation.txt
 * 0d2080d0def294a4b7660801cc03ddfe5866ff300c789c2cc1b50fd7802b2d97 lib/unicore/auxiliary/GCBTest.txt
 * 5a0f8748575432f8ff95e1dd5bfaa27bda1a844809e17d6939ee912bba6568a1 lib/unicore/auxiliary/GraphemeBreakProperty.txt
 * 371bde4052aa593b108684ae292d8ea2dbb93c19990e0cdf416fa7239557aac3 lib/unicore/auxiliary/LBTest.txt
 * f62279d8fd10935ba0cf0d8417a1dcbe7ab0d4e62f59c17e02cbe40f580c4162 lib/unicore/auxiliary/SBTest.txt
 * 61e4ba975b0a5bc1a76ee931b94914395d7289ef624e3c0d4d6b9460ee387bea lib/unicore/auxiliary/SentenceBreakProperty.txt
 * 2a676130c71194245e7c74a837e58330f202600d8ddcf4518129dd476f26e18e lib/unicore/auxiliary/WBTest.txt
 * 5188a56e91593467c2e912601ebc78750e6adc9b04541b8c5becb5441e388ce2 lib/unicore/auxiliary/WordBreakProperty.txt
 * 29071dba22c72c27783a73016afb8ffaeb025866740791f9c2d0b55cc45a3470 lib/unicore/emoji/emoji.txt
 * 4841f2090c2dbc592d3ce43bb74c2191b3da50fb9a0d00274f1448c202851b02 lib/unicore/extracted/DBidiClass.txt
 * f10a35451429137f7348825f22d624b6390c526ead3d8e756d2af9e5ed5b2b67 lib/unicore/extracted/DBinaryProperties.txt
 * ca54f6360cd288ad92113415bf1f77749015abe11cbd6798d21f7fa81f04205d lib/unicore/extracted/DCombiningClass.txt
 * db059ce45e3cec49bfda56e262fa658b3a5561b1648de266c818d2a08a85b78a lib/unicore/extracted/DDecompositionType.txt
 * d62e6950f086e53f47c593a38342621f8838f48c49a1de070cf83d3959bd1688 lib/unicore/extracted/DEastAsianWidth.txt
 * fe29a45c0882500e591140aaa5c4f5067e6a5d746806148af34400c48b9c06f9 lib/unicore/extracted/DGeneralCategory.txt
 * e13ca1344b16023aa38c6ada39f9658536fc6bb7c3c24d579f0bc316a4f4f1e0 lib/unicore/extracted/DJoinGroup.txt
 * c4870b11e2b8b7d0eb70b99ce85608e5c28a399efa316cca97238a58ae160e5e lib/unicore/extracted/DJoinType.txt
 * 3f4f32ed2a577344a508114527e721d7a8b633d32f38945d47fe0c743650c585 lib/unicore/extracted/DLineBreak.txt
 * 710abf2d581ac9c57f244c0834f9d9969d9781e0396adccd330eaae658ac7d6b lib/unicore/extracted/DNumType.txt
 * 6bd30f385f3baf3ab5d5308c111a81de87bea5f494ba0ba69e8ab45263b8c34d lib/unicore/extracted/DNumValues.txt
 * f7265069b38ba9a0675a18600e241b1ec6fc8c55fd806fe4c13bc5d8cb0dc508 lib/unicore/mktables
 * 55d90fdc3f902e5c0b16b3378f9eaa36e970a1c09723c33de7d47d0370044012 lib/unicore/version
 * 0a6b5ab33bb1026531f816efe81aea1a8ffcd34a27cbea37dd6a70a63d73c844 regen/charset_translations.pl
 * acc94e4afc339fe2cf2ae74d6e1cbcf2c396328d78e56236ad314eadbfc84125 regen/regcharclass.pl
 * b2f896452d2b30da3e04800f478c60c1fd0b03d6b668689b020f1e3cf1f1cdd9 regen/regcharclass_multi_char_folds.pl
 * ex: set ro ft=c: */
