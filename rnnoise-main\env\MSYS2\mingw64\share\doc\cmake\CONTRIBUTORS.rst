Contributors
************

The following individuals and institutions are among the contributors:

* `<PERSON> <mailto:<EMAIL>>`_
* `<PERSON><PERSON><PERSON> <mailto:<EMAIL>>`_
* `<PERSON><PERSON><PERSON> <mailto:<EMAIL>>`_
* `<PERSON> <mailto:<EMAIL>>`_
* `<PERSON> <mailto:<EMAIL>>`_
* `<PERSON><PERSON> <mailto:<EMAIL>>`_
* `<PERSON> <mailto:<EMAIL>>`_
* `<PERSON> <mailto:<EMAIL>>`_
* `<PERSON> <mailto:<EMAIL>>`_
* `<PERSON> <mailto:<EMAIL>>`_
* `<PERSON> <mailto:Andre.<PERSON><EMAIL>>`_
* <PERSON>, <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>um Dresden - Ross<PERSON>orf
* <PERSON>
* `<PERSON><PERSON><PERSON> <mailto:<EMAIL>>`_
* `<PERSON> <mailto:<EMAIL>>`_
* <PERSON>
* `<PERSON> <mailto:<EMAIL>>`_
* `<PERSON>reusot <mailto:<EMAIL>>`_
* `Daniel Blezek <mailto:<EMAIL>>`_
* `Daniel Pfeifer <mailto:<EMAIL>>`_
* `Dawid Wróbel <mailto:<EMAIL>>`_
* `Enrico Scholz <mailto:<EMAIL>>`_
* `Eran Ifrah <mailto:<EMAIL>>`_
* Esben Mose Hansen, Ange Optimization ApS
* `Geoffrey Viola <mailto:<EMAIL>>`_
* `Google Inc <https://www.google.com/>`_
* Gregor Jasny
* `Helio Chissini de Castro <mailto:<EMAIL>>`_
* `Ilya Lavrenov <mailto:<EMAIL>>`_
* `Insight Software Consortium <https://insightsoftwareconsortium.org/>`_
* `Intel Corporation <https://www.intel.com/>`_
* Jan Woetzel
* `Jordan Williams <mailto:<EMAIL>>`_
* Julien Schueller
* `Kelly Thompson <mailto:<EMAIL>>`_
* `Konstantin Podsvirov <mailto:<EMAIL>>`_
* `Laurent Montel <mailto:<EMAIL>>`_
* `Mario Bensi <mailto:<EMAIL>>`_
* `Martin Gräßlin <mailto:<EMAIL>>`_
* `Mathieu Malaterre <mailto:<EMAIL>>`_
* Matthaeus G. Chajdas
* `Matthias Kretz <mailto:<EMAIL>>`_
* `Matthias Maennich <mailto:<EMAIL>>`_
* `Michael Hirsch, Ph.D. <https://www.scivision.dev/>`_
* Michael Stürmer
* Miguel A. Figueroa-Villanueva
* `Mike Durso <mailto:<EMAIL>>`_
* Mike Jackson
* `Mike McQuaid <mailto:<EMAIL>>`_
* `Nicolas Bock <mailto:<EMAIL>>`_
* `Nicolas Despres <mailto:<EMAIL>>`_
* `Nikita Krupen'ko <mailto:<EMAIL>>`_
* `NVIDIA Corporation <https://www.nvidia.com/>`_
* `OpenGamma Ltd. <https://opengamma.com/>`_
* `Patrick Stotko <mailto:<EMAIL>>`_
* `Per Øyvind Karlsen <mailto:<EMAIL>>`_
* `Peter Collingbourne <mailto:<EMAIL>>`_
* `Petr Gotthard <mailto:<EMAIL>>`_
* `Philip Lowman <mailto:<EMAIL>>`_
* `Philippe Proulx <mailto:<EMAIL>>`_
* Raffi Enficiaud, Max Planck Society
* Raumfeld
* `Roger Leigh <mailto:<EMAIL>>`_
* `Rolf Eike Beer <mailto:<EMAIL>>`_
* `Roman Donchenko <mailto:<EMAIL>>`_
* `Roman Kharitonov <mailto:<EMAIL>>`_
* Ruslan Baratov
* `Sebastian Holtermann <mailto:<EMAIL>>`_
* `Stephen Kelly <mailto:<EMAIL>>`_
* `Sylvain Joubert <mailto:<EMAIL>>`_
* `The Qt Company Ltd. <https://www.qt.io/>`_
* `Thomas Sondergaard <mailto:<EMAIL>>`_
* `Tobias Hunger <mailto:<EMAIL>>`_
* `Todd Gamblin <mailto:<EMAIL>>`_
* Tristan Carel
* `University of Dundee <https://www.dundee.ac.uk/>`_
* Vadim Zhukov
* `Will Dicharry <mailto:<EMAIL>>`_

See version control history for details of individual contributions.

Copyright
=========

The accompanying `LICENSE.rst`_ notice applies to distributions of CMake
in source and binary form.  We do not require any formal copyright
assignment or contributor license agreement.  Any contributions
intentionally sent upstream are presumed to be offered under terms
of the OSI-approved BSD 3-clause License.

Third-party software packages supplied with CMake under compatible
licenses provide their own copyright notices documented in corresponding
subdirectories or source files.

.. _`LICENSE.rst`: LICENSE.rst
