#!/usr/bin/env python3
"""
导出增强RNN模型到C代码
"""

import tensorflow as tf
import numpy as np
import sys
import os

def export_weights_to_c(model, c_file, h_file):
    """将模型权重导出为C代码"""
    
    print("开始导出模型权重...")
    
    # 打开输出文件
    with open(c_file, 'w') as f_c, open(h_file, 'w') as f_h:
        # 写入C文件头部
        f_c.write('/* 增强RNN模型权重 - 自动生成 */\n\n')
        f_c.write('#include "enhanced_rnn_weights.h"\n\n')
        
        # 写入头文件
        f_h.write('/* 增强RNN模型权重头文件 - 自动生成 */\n\n')
        f_h.write('#ifndef ENHANCED_RNN_WEIGHTS_H\n')
        f_h.write('#define ENHANCED_RNN_WEIGHTS_H\n\n')
        
        layer_count = 0
        
        # 遍历模型层
        for layer in model.layers:
            if len(layer.get_weights()) > 0:
                layer_name = layer.name.replace('/', '_')
                weights = layer.get_weights()
                
                print(f"导出层: {layer_name}")
                
                # 导出权重
                if len(weights) >= 1:  # 有权重矩阵
                    weight_matrix = weights[0]
                    weight_name = f"{layer_name}_weights"
                    
                    # 写入权重数组
                    f_c.write(f'const float {weight_name}[{weight_matrix.size}] = {{\n')
                    flat_weights = weight_matrix.flatten()
                    for i, w in enumerate(flat_weights):
                        if i % 8 == 0:
                            f_c.write('    ')
                        f_c.write(f'{w:.6f}f')
                        if i < len(flat_weights) - 1:
                            f_c.write(', ')
                        if (i + 1) % 8 == 0:
                            f_c.write('\n')
                    if len(flat_weights) % 8 != 0:
                        f_c.write('\n')
                    f_c.write('};\n\n')
                    
                    # 写入头文件声明
                    f_h.write(f'extern const float {weight_name}[{weight_matrix.size}];\n')
                    f_h.write(f'#define {weight_name.upper()}_ROWS {weight_matrix.shape[0]}\n')
                    f_h.write(f'#define {weight_name.upper()}_COLS {weight_matrix.shape[1] if len(weight_matrix.shape) > 1 else 1}\n\n')
                
                # 导出偏置
                if len(weights) >= 2:  # 有偏置
                    bias_vector = weights[-1]  # 偏置通常是最后一个
                    bias_name = f"{layer_name}_bias"
                    
                    f_c.write(f'const float {bias_name}[{bias_vector.size}] = {{\n')
                    f_c.write('    ')
                    for i, b in enumerate(bias_vector.flatten()):
                        f_c.write(f'{b:.6f}f')
                        if i < len(bias_vector) - 1:
                            f_c.write(', ')
                        if (i + 1) % 8 == 0 and i < len(bias_vector) - 1:
                            f_c.write('\n    ')
                    f_c.write('\n};\n\n')
                    
                    f_h.write(f'extern const float {bias_name}[{bias_vector.size}];\n')
                    f_h.write(f'#define {bias_name.upper()}_SIZE {bias_vector.size}\n\n')
                
                # 导出循环权重 (对于GRU层)
                if len(weights) == 3:  # GRU层有3个权重矩阵
                    recurrent_weights = weights[1]
                    recurrent_name = f"{layer_name}_recurrent_weights"
                    
                    f_c.write(f'const float {recurrent_name}[{recurrent_weights.size}] = {{\n')
                    flat_weights = recurrent_weights.flatten()
                    for i, w in enumerate(flat_weights):
                        if i % 8 == 0:
                            f_c.write('    ')
                        f_c.write(f'{w:.6f}f')
                        if i < len(flat_weights) - 1:
                            f_c.write(', ')
                        if (i + 1) % 8 == 0:
                            f_c.write('\n')
                    if len(flat_weights) % 8 != 0:
                        f_c.write('\n')
                    f_c.write('};\n\n')
                    
                    f_h.write(f'extern const float {recurrent_name}[{recurrent_weights.size}];\n')
                    f_h.write(f'#define {recurrent_name.upper()}_ROWS {recurrent_weights.shape[0]}\n')
                    f_h.write(f'#define {recurrent_name.upper()}_COLS {recurrent_weights.shape[1]}\n\n')
                
                layer_count += 1
        
        # 写入模型信息
        f_h.write('/* 模型信息 */\n')
        f_h.write(f'#define ENHANCED_MODEL_INPUT_SIZE 68\n')
        f_h.write(f'#define ENHANCED_MODEL_NOISE_OUTPUT_SIZE 18\n')
        f_h.write(f'#define ENHANCED_MODEL_VOICE_OUTPUT_SIZE 18\n')
        f_h.write(f'#define ENHANCED_MODEL_VAD_OUTPUT_SIZE 1\n')
        f_h.write(f'#define ENHANCED_MODEL_LAYERS {layer_count}\n\n')
        
        f_h.write('#endif /* ENHANCED_RNN_WEIGHTS_H */\n')
    
    print(f"模型导出完成!")
    print(f"C文件: {c_file}")
    print(f"头文件: {h_file}")
    print(f"导出层数: {layer_count}")

def create_simple_inference_code():
    """创建简化的推理代码"""
    
    inference_c = """/* 增强RNN模型推理代码 - 简化版本 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "enhanced_rnn_weights.h"

/* 简化的矩阵乘法 */
void matrix_multiply(const float* input, const float* weights, float* output, 
                    int input_size, int output_size) {
    for (int i = 0; i < output_size; i++) {
        output[i] = 0.0f;
        for (int j = 0; j < input_size; j++) {
            output[i] += input[j] * weights[i * input_size + j];
        }
    }
}

/* 向量加法 */
void vector_add(float* vec, const float* bias, int size) {
    for (int i = 0; i < size; i++) {
        vec[i] += bias[i];
    }
}

/* Tanh激活函数 */
void tanh_activation(float* vec, int size) {
    for (int i = 0; i < size; i++) {
        vec[i] = tanhf(vec[i]);
    }
}

/* Sigmoid激活函数 */
void sigmoid_activation(float* vec, int size) {
    for (int i = 0; i < size; i++) {
        vec[i] = 1.0f / (1.0f + expf(-vec[i]));
    }
}

/* 简化的GRU前向传播 */
void simple_gru_forward(const float* input, const float* weights, const float* recurrent_weights,
                       const float* bias, float* state, float* output, 
                       int input_size, int hidden_size) {
    // 这是一个极简化的GRU实现，仅用于演示
    // 实际应用中需要完整的GRU计算
    
    float temp[hidden_size];
    
    // 输入变换
    matrix_multiply(input, weights, temp, input_size, hidden_size);
    vector_add(temp, bias, hidden_size);
    tanh_activation(temp, hidden_size);
    
    // 更新状态 (简化)
    for (int i = 0; i < hidden_size; i++) {
        state[i] = 0.9f * state[i] + 0.1f * temp[i];
        output[i] = state[i];
    }
}

/* 增强RNN推理函数 */
void enhanced_rnn_inference(const float* input_features, 
                           float* noise_output, float* voice_output, float* vad_output) {
    
    // 状态变量
    static float gru_state[64] = {0};
    
    // 中间变量
    float dense_output[48];
    float gru_output[64];
    
    // 第1层: Dense层
    matrix_multiply(input_features, feature_dense_weights, dense_output, 
                   FEATURE_DENSE_WEIGHTS_ROWS, FEATURE_DENSE_WEIGHTS_COLS);
    vector_add(dense_output, feature_dense_bias, FEATURE_DENSE_BIAS_SIZE);
    tanh_activation(dense_output, FEATURE_DENSE_BIAS_SIZE);
    
    // 第2层: GRU层 (简化)
    simple_gru_forward(dense_output, main_gru_weights, main_gru_recurrent_weights,
                      main_gru_bias, gru_state, gru_output,
                      MAIN_GRU_WEIGHTS_ROWS, 64);
    
    // 输出层1: 噪声抑制
    matrix_multiply(gru_output, noise_output_weights, noise_output,
                   NOISE_OUTPUT_WEIGHTS_ROWS, NOISE_OUTPUT_WEIGHTS_COLS);
    vector_add(noise_output, noise_output_bias, NOISE_OUTPUT_BIAS_SIZE);
    sigmoid_activation(noise_output, ENHANCED_MODEL_NOISE_OUTPUT_SIZE);
    
    // 输出层2: 人声增强
    matrix_multiply(gru_output, voice_output_weights, voice_output,
                   VOICE_OUTPUT_WEIGHTS_ROWS, VOICE_OUTPUT_WEIGHTS_COLS);
    vector_add(voice_output, voice_output_bias, VOICE_OUTPUT_BIAS_SIZE);
    sigmoid_activation(voice_output, ENHANCED_MODEL_VOICE_OUTPUT_SIZE);
    
    // 输出层3: VAD
    matrix_multiply(gru_output, vad_output_weights, vad_output,
                   VAD_OUTPUT_WEIGHTS_ROWS, VAD_OUTPUT_WEIGHTS_COLS);
    vector_add(vad_output, vad_output_bias, VAD_OUTPUT_BIAS_SIZE);
    sigmoid_activation(vad_output, ENHANCED_MODEL_VAD_OUTPUT_SIZE);
}

/* 测试主函数 */
int main() {
    printf("增强RNN模型测试程序\\n");
    printf("输入维度: %d\\n", ENHANCED_MODEL_INPUT_SIZE);
    printf("噪声抑制输出: %d\\n", ENHANCED_MODEL_NOISE_OUTPUT_SIZE);
    printf("人声增强输出: %d\\n", ENHANCED_MODEL_VOICE_OUTPUT_SIZE);
    printf("VAD输出: %d\\n", ENHANCED_MODEL_VAD_OUTPUT_SIZE);
    
    // 创建测试输入
    float test_input[ENHANCED_MODEL_INPUT_SIZE];
    for (int i = 0; i < ENHANCED_MODEL_INPUT_SIZE; i++) {
        test_input[i] = 0.1f * sinf(i * 0.1f);  // 简单的测试信号
    }
    
    // 输出缓冲区
    float noise_gains[ENHANCED_MODEL_NOISE_OUTPUT_SIZE];
    float voice_gains[ENHANCED_MODEL_VOICE_OUTPUT_SIZE];
    float vad_prob[ENHANCED_MODEL_VAD_OUTPUT_SIZE];
    
    // 运行推理
    printf("\\n运行推理...\\n");
    enhanced_rnn_inference(test_input, noise_gains, voice_gains, vad_prob);
    
    // 显示结果
    printf("\\n推理结果:\\n");
    printf("噪声抑制增益: ");
    for (int i = 0; i < ENHANCED_MODEL_NOISE_OUTPUT_SIZE; i++) {
        printf("%.3f ", noise_gains[i]);
    }
    printf("\\n");
    
    printf("人声增强增益: ");
    for (int i = 0; i < ENHANCED_MODEL_VOICE_OUTPUT_SIZE; i++) {
        printf("%.3f ", voice_gains[i]);
    }
    printf("\\n");
    
    printf("VAD概率: %.3f\\n", vad_prob[0]);
    
    printf("\\n模型推理测试完成!\\n");
    
    return 0;
}
"""
    
    with open('../src/enhanced_inference_test.c', 'w', encoding='utf-8') as f:
        f.write(inference_c)
    
    print("推理测试代码已生成: ../src/enhanced_inference_test.c")

def main():
    """主函数"""
    model_path = '../models_enhanced/stable_20250802_124711/enhanced_rnnoise_stable.keras'
    
    if not os.path.exists(model_path):
        print(f"错误: 找不到模型文件 {model_path}")
        return
    
    print(f"加载模型: {model_path}")
    
    try:
        # 定义自定义损失函数用于加载模型
        def stable_mse_loss(y_true, y_pred):
            return tf.reduce_mean(tf.square(y_true - y_pred))

        # 加载模型，提供自定义对象
        model = tf.keras.models.load_model(model_path, custom_objects={
            'stable_mse_loss': stable_mse_loss
        })
        
        print("模型加载成功!")
        print("模型结构:")
        model.summary()
        
        # 导出权重
        c_file = '../src/enhanced_rnn_weights.c'
        h_file = '../include/enhanced_rnn_weights.h'
        
        export_weights_to_c(model, c_file, h_file)
        
        # 创建推理测试代码
        create_simple_inference_code()
        
        print("\\n模型导出完成!")
        print("生成的文件:")
        print(f"  - {c_file}")
        print(f"  - {h_file}")
        print(f"  - ../src/enhanced_inference_test.c")
        
    except Exception as e:
        print(f"导出失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
