.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_free_ca_names" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_free_ca_names \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_certificate_free_ca_names(gnutls_certificate_credentials_t " sc ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t sc" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.SH "DESCRIPTION"
This function will delete all the CA name in the given
credentials. Clients may call this to save some memory since in
client side the CA names are not used. Servers might want to use
this function if a large list of trusted CAs is present and
sending the names of it would just consume bandwidth without providing
information to client.

CA names are used by servers to advertise the CAs they support to
clients.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
