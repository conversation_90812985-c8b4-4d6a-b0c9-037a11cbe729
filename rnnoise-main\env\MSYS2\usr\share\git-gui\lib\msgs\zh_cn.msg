set ::msgcat::header "Project-Id-Version: git-gui\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2007-07-21 01:23-0700\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Chinese\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset zh_cn "git-gui: fatal error" "git-gui: \u81f4\u547d\u9519\u8bef"
::msgcat::mcset zh_cn "Invalid font specified in %s:" "%s \u4e2d\u6307\u5b9a\u7684\u5b57\u4f53\u65e0\u6548:"
::msgcat::mcset zh_cn "Main Font" "\u4e3b\u8981\u5b57\u4f53"
::msgcat::mcset zh_cn "Diff/Console Font" "Diff/\u63a7\u5236\u7ec8\u7aef\u5b57\u4f53"
::msgcat::mcset zh_cn "Cannot find git in PATH." "PATH \u4e2d\u6ca1\u6709\u627e\u5230 git"
::msgcat::mcset zh_cn "Cannot parse Git version string:" "\u65e0\u6cd5\u89e3\u6790 Git \u7684\u7248\u672c\u4fe1\u606f:"
::msgcat::mcset zh_cn "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "\u65e0\u6cd5\u786e\u5b9a Git \u7684\u7248\u672c.\n\n%s \u58f0\u660e\u5176\u7248\u672c\u4e3a '%s'.\n\n\u800c %s \u9700\u8981 1.5.0 \u6216\u8fd9\u4ee5\u540e\u7684 Git \u7248\u672c.\n\n\u662f\u5426\u5047\u5b9a '%s' \u4e3a\u7248\u672c 1.5.0?\n"
::msgcat::mcset zh_cn "Git directory not found:" "Git \u76ee\u5f55\u65e0\u6cd5\u627e\u5230:"
::msgcat::mcset zh_cn "Cannot move to top of working directory:" "\u65e0\u6cd5\u79fb\u52a8\u5230\u5de5\u4f5c\u6839\u76ee\u5f55:"
::msgcat::mcset zh_cn "Cannot use funny .git directory:" "\u65e0\u6cd5\u4f7f\u7528 .git \u76ee\u5f55:"
::msgcat::mcset zh_cn "No working directory" "\u6ca1\u6709\u5de5\u4f5c\u76ee\u5f55"
::msgcat::mcset zh_cn "Refreshing file status..." "\u66f4\u65b0\u6587\u4ef6\u72b6\u6001..."
::msgcat::mcset zh_cn "Scanning for modified files ..." "\u626b\u63cf\u4fee\u6539\u8fc7\u7684\u6587\u4ef6 ..."
::msgcat::mcset zh_cn "Ready." "\u5c31\u7eea"
::msgcat::mcset zh_cn "Unmodified" "\u672a\u4fee\u6539"
::msgcat::mcset zh_cn "Modified, not staged" "\u4fee\u6539\u4f46\u672a\u7f13\u5b58"
::msgcat::mcset zh_cn "Staged for commit" "\u7f13\u5b58\u4e3a\u63d0\u4ea4"
::msgcat::mcset zh_cn "Portions staged for commit" "\u90e8\u5206\u7f13\u5b58\u4e3a\u63d0\u4ea4"
::msgcat::mcset zh_cn "Staged for commit, missing" "\u7f13\u5b58\u4e3a\u63d0\u4ea4, \u4e0d\u5b58\u5728"
::msgcat::mcset zh_cn "Untracked, not staged" "\u672a\u8ddf\u8e2a, \u672a\u7f13\u5b58"
::msgcat::mcset zh_cn "Missing" "\u4e0d\u5b58\u5728"
::msgcat::mcset zh_cn "Staged for removal" "\u7f13\u5b58\u4e3a\u5220\u9664"
::msgcat::mcset zh_cn "Staged for removal, still present" "\u7f13\u5b58\u4e3a\u5220\u9664, \u4f46\u4ecd\u5b58\u5728"
::msgcat::mcset zh_cn "Requires merge resolution" "\u9700\u8981\u89e3\u51b3\u5408\u5e76\u51b2\u7a81"
::msgcat::mcset zh_cn "Starting gitk... please wait..." "\u542f\u52a8 gitk... \u8bf7\u7b49\u5f85..."
::msgcat::mcset zh_cn "Unable to start gitk:\n\n%s does not exist" "\u65e0\u6cd5\u542f\u52a8 gitk:\n\n%s \u4e0d\u5b58\u5728"
::msgcat::mcset zh_cn "Repository" "\u7248\u672c\u5e93(repository)"
::msgcat::mcset zh_cn "Edit" "\u7f16\u8f91"
::msgcat::mcset zh_cn "Branch" "\u5206\u652f(branch)"
::msgcat::mcset zh_cn "Commit@@noun" "\u63d0\u4ea4(commit)"
::msgcat::mcset zh_cn "Merge" "\u5408\u5e76(merge)"
::msgcat::mcset zh_cn "Remote" "\u8fdc\u7aef(remote)"
::msgcat::mcset zh_cn "Browse Current Branch's Files" "\u6d4f\u89c8\u5f53\u524d\u5206\u652f\u4e0a\u7684\u6587\u4ef6"
::msgcat::mcset zh_cn "Browse Branch Files..." "\u6d4f\u89c8\u5206\u652f\u4e0a\u7684\u6587\u4ef6..."
::msgcat::mcset zh_cn "Visualize Current Branch's History" "\u56fe\u793a\u5f53\u524d\u5206\u652f\u7684\u5386\u53f2"
::msgcat::mcset zh_cn "Visualize All Branch History" "\u56fe\u793a\u6240\u6709\u5206\u652f\u7684\u5386\u53f2"
::msgcat::mcset zh_cn "Browse %s's Files" "\u6d4f\u89c8 %s \u4e0a\u7684\u6587\u4ef6"
::msgcat::mcset zh_cn "Visualize %s's History" "\u56fe\u793a %s \u5206\u652f\u7684\u5386\u53f2"
::msgcat::mcset zh_cn "Database Statistics" "\u6570\u636e\u5e93\u7edf\u8ba1\u4fe1\u606f"
::msgcat::mcset zh_cn "Compress Database" "\u538b\u7f29\u6570\u636e\u5e93"
::msgcat::mcset zh_cn "Verify Database" "\u9a8c\u8bc1\u6570\u636e\u5e93"
::msgcat::mcset zh_cn "Create Desktop Icon" "\u521b\u5efa\u684c\u9762\u56fe\u6807"
::msgcat::mcset zh_cn "Quit" "\u9000\u51fa"
::msgcat::mcset zh_cn "Undo" "\u64a4\u9500"
::msgcat::mcset zh_cn "Redo" "\u91cd\u505a"
::msgcat::mcset zh_cn "Cut" "\u526a\u5207"
::msgcat::mcset zh_cn "Copy" "\u590d\u5236"
::msgcat::mcset zh_cn "Paste" "\u7c98\u8d34"
::msgcat::mcset zh_cn "Delete" "\u5220\u9664"
::msgcat::mcset zh_cn "Select All" "\u5168\u9009"
::msgcat::mcset zh_cn "Create..." "\u65b0\u5efa..."
::msgcat::mcset zh_cn "Checkout..." "Checkout..."
::msgcat::mcset zh_cn "Rename..." "\u66f4\u540d..."
::msgcat::mcset zh_cn "Delete..." "\u5220\u9664..."
::msgcat::mcset zh_cn "Reset..." "\u590d\u4f4d(Reset)..."
::msgcat::mcset zh_cn "New Commit" "\u65b0\u5efa\u63d0\u4ea4"
::msgcat::mcset zh_cn "Amend Last Commit" "\u4fee\u6b63\u4e0a\u6b21\u63d0\u4ea4"
::msgcat::mcset zh_cn "Rescan" "\u91cd\u65b0\u626b\u63cf"
::msgcat::mcset zh_cn "Stage To Commit" "\u7f13\u5b58\u4e3a\u63d0\u4ea4"
::msgcat::mcset zh_cn "Stage Changed Files To Commit" "\u7f13\u5b58\u4fee\u6539\u7684\u6587\u4ef6\u4e3a\u63d0\u4ea4"
::msgcat::mcset zh_cn "Unstage From Commit" "\u4ece\u672c\u6b21\u63d0\u4ea4\u64a4\u9664"
::msgcat::mcset zh_cn "Revert Changes" "\u64a4\u9500\u4fee\u6539"
::msgcat::mcset zh_cn "Sign Off" "\u7b7e\u540d(Sign Off)"
::msgcat::mcset zh_cn "Commit@@verb" "\u63d0\u4ea4"
::msgcat::mcset zh_cn "Local Merge..." "\u672c\u5730\u5408\u5e76..."
::msgcat::mcset zh_cn "Abort Merge..." "\u4e2d\u6b62\u5408\u5e76..."
::msgcat::mcset zh_cn "Push..." "\u4e0a\u4f20..."
::msgcat::mcset zh_cn "Apple" "\u82f9\u679c"
::msgcat::mcset zh_cn "About %s" "\u5173\u4e8e %s"
::msgcat::mcset zh_cn "Preferences..." "\u9996\u9009\u9879..."
::msgcat::mcset zh_cn "Options..." "\u9009\u9879..."
::msgcat::mcset zh_cn "Help" "\u5e2e\u52a9"
::msgcat::mcset zh_cn "Online Documentation" "\u5728\u7ebf\u6587\u6863"
::msgcat::mcset zh_cn "fatal: cannot stat path %s: No such file or directory" "\u81f4\u547d\u9519\u8bef: \u65e0\u6cd5\u83b7\u53d6\u8def\u5f84 %s \u7684\u4fe1\u606f: \u8be5\u6587\u4ef6\u6216\u76ee\u5f55\u4e0d\u5b58\u5728"
::msgcat::mcset zh_cn "Current Branch:" "\u5f53\u524d\u5206\u652f:"
::msgcat::mcset zh_cn "Staged Changes (Will Commit)" "\u5df2\u7f13\u5b58\u7684\u6539\u52a8 (\u5c06\u88ab\u63d0\u4ea4)"
::msgcat::mcset zh_cn "Unstaged Changes" "\u672a\u7f13\u5b58\u7684\u6539\u52a8"
::msgcat::mcset zh_cn "Stage Changed" "\u7f13\u5b58\u6539\u52a8"
::msgcat::mcset zh_cn "Push" "\u4e0a\u4f20"
::msgcat::mcset zh_cn "Initial Commit Message:" "\u521d\u59cb\u7684\u63d0\u4ea4\u63cf\u8ff0:"
::msgcat::mcset zh_cn "Amended Commit Message:" "\u4fee\u6b63\u7684\u63d0\u4ea4\u63cf\u8ff0:"
::msgcat::mcset zh_cn "Amended Initial Commit Message:" "\u4fee\u6b63\u7684\u521d\u59cb\u63d0\u4ea4\u63cf\u8ff0:"
::msgcat::mcset zh_cn "Amended Merge Commit Message:" "\u4fee\u6b63\u7684\u5408\u5e76\u63d0\u4ea4\u63cf\u8ff0:"
::msgcat::mcset zh_cn "Merge Commit Message:" "\u5408\u5e76\u63d0\u4ea4\u63cf\u8ff0:"
::msgcat::mcset zh_cn "Commit Message:" "\u63d0\u4ea4\u63cf\u8ff0:"
::msgcat::mcset zh_cn "Copy All" "\u5168\u90e8\u590d\u5236"
::msgcat::mcset zh_cn "File:" "\u6587\u4ef6:"
::msgcat::mcset zh_cn "Apply/Reverse Hunk" "\u5e94\u7528/\u64a4\u6d88\u6b64\u4fee\u6539\u5757"
::msgcat::mcset zh_cn "Show Less Context" "\u663e\u793a\u66f4\u5c11\u4e0a\u4e0b\u6587"
::msgcat::mcset zh_cn "Show More Context" "\u663e\u793a\u66f4\u591a\u4e0a\u4e0b\u6587"
::msgcat::mcset zh_cn "Refresh" "\u5237\u65b0"
::msgcat::mcset zh_cn "Decrease Font Size" "\u7f29\u5c0f\u5b57\u4f53"
::msgcat::mcset zh_cn "Increase Font Size" "\u653e\u5927\u5b57\u4f53"
::msgcat::mcset zh_cn "Unstage Hunk From Commit" "\u4ece\u63d0\u4ea4\u4e2d\u64a4\u9664\u4fee\u6539\u5757"
::msgcat::mcset zh_cn "Stage Hunk For Commit" "\u7f13\u5b58\u4fee\u6539\u5757\u4e3a\u63d0\u4ea4"
::msgcat::mcset zh_cn "Initializing..." "\u521d\u59cb\u5316..."
::msgcat::mcset zh_cn "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "\u53ef\u80fd\u5b58\u5728\u73af\u5883\u53d8\u91cf\u7684\u95ee\u9898.\n\n\u7531 %s \u6267\u884c\u7684 Git \u5b50\u8fdb\u7a0b\u53ef\u80fd\u5ffd\u7565\u4e0b\u5217\u73af\u5883\u53d8\u91cf:\n\n"
::msgcat::mcset zh_cn "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\n\u8fd9\u662f\u7531 Cygwin \u53d1\u5e03\u7684 Tcl \u4ee3\u7801\u4e2d\u4e00\u4e2a\n\u5df2\u77e5\u95ee\u9898\u6240\u5f15\u8d77."
::msgcat::mcset zh_cn "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\n%s \u7684\u4e00\u4e2a\u5f88\u597d\u7684\u66ff\u4ee3\u65b9\u6848\u662f\u5c06 user.name \u4ee5\u53ca\nuser.email \u8bbe\u7f6e\u653e\u5728\u4f60\u7684\u4e2a\u4eba ~/.gitconfig \u6587\u4ef6\u4e2d.\n"
::msgcat::mcset zh_cn "git-gui - a graphical user interface for Git." "git-gui - Git \u7684\u56fe\u5f62\u5316\u7528\u6237\u754c\u9762"
::msgcat::mcset zh_cn "File Viewer" "\u6587\u4ef6\u67e5\u770b\u5668"
::msgcat::mcset zh_cn "Commit:" "\u63d0\u4ea4:"
::msgcat::mcset zh_cn "Copy Commit" "\u590d\u5236\u63d0\u4ea4"
::msgcat::mcset zh_cn "Reading %s..." "\u8bfb\u53d6 %s..."
::msgcat::mcset zh_cn "Loading copy/move tracking annotations..." "\u88c5\u8f7d\u590d\u5236/\u79fb\u52a8\u8ddf\u8e2a\u6807\u6ce8..."
::msgcat::mcset zh_cn "lines annotated" "\u6807\u6ce8\u884c"
::msgcat::mcset zh_cn "Loading original location annotations..." "\u88c5\u8f7d\u539f\u59cb\u4f4d\u7f6e\u6807\u6ce8..."
::msgcat::mcset zh_cn "Annotation complete." "\u6807\u6ce8\u5b8c\u6210."
::msgcat::mcset zh_cn "Loading annotation..." "\u88dd\u8f09\u6807\u6ce8..."
::msgcat::mcset zh_cn "Author:" "\u4f5c\u8005:"
::msgcat::mcset zh_cn "Committer:" "\u63d0\u4ea4\u8005:"
::msgcat::mcset zh_cn "Original File:" "\u539f\u59cb\u6587\u4ef6:"
::msgcat::mcset zh_cn "Originally By:" "\u6700\u521d\u7531:"
::msgcat::mcset zh_cn "In File:" "\u5728\u6587\u4ef6:"
::msgcat::mcset zh_cn "Copied Or Moved Here By:" "\u7531\u590d\u5236\u6216\u79fb\u52a8\u81f3\u6b64:"
::msgcat::mcset zh_cn "Checkout Branch" "Checkout \u5206\u652f"
::msgcat::mcset zh_cn "Checkout" "Checkout"
::msgcat::mcset zh_cn "Cancel" "\u53d6\u6d88"
::msgcat::mcset zh_cn "Revision" "\u7248\u672c"
::msgcat::mcset zh_cn "Options" "\u9009\u9879..."
::msgcat::mcset zh_cn "Fetch Tracking Branch" "\u83b7\u53d6\u8ddf\u8e2a\u5206\u652f"
::msgcat::mcset zh_cn "Detach From Local Branch" "\u4ece\u672c\u5730\u5206\u652f\u8131\u79bb"
::msgcat::mcset zh_cn "Create Branch" "\u521b\u5efa\u5206\u652f"
::msgcat::mcset zh_cn "Create New Branch" "\u65b0\u5efa\u5206\u652f"
::msgcat::mcset zh_cn "Create" "\u65b0\u5efa"
::msgcat::mcset zh_cn "Branch Name" "\u5206\u652f\u540d"
::msgcat::mcset zh_cn "Name:" "\u540d\u5b57:"
::msgcat::mcset zh_cn "Match Tracking Branch Name" "\u5339\u914d\u8ddf\u8e2a\u5206\u652f\u540d\u5b57"
::msgcat::mcset zh_cn "Starting Revision" "\u8d77\u59cb\u7248\u672c"
::msgcat::mcset zh_cn "Update Existing Branch:" "\u66f4\u65b0\u5df2\u6709\u5206\u652f:"
::msgcat::mcset zh_cn "No" "\u53f7\u7801"
::msgcat::mcset zh_cn "Fast Forward Only" "\u4ec5\u5feb\u901f\u5408\u5e76"
::msgcat::mcset zh_cn "Reset" "\u590d\u4f4d"
::msgcat::mcset zh_cn "Checkout After Creation" "\u5728\u521b\u5efa\u540eCheckout"
::msgcat::mcset zh_cn "Please select a tracking branch." "\u8bf7\u9009\u62e9\u67d0\u4e2a\u8ddf\u8e2a\u5206\u652f."
::msgcat::mcset zh_cn "Tracking branch %s is not a branch in the remote repository." "\u8ddf\u8e2a\u5206\u652f %s \u5e76\u4e0d\u662f\u8fdc\u7aef\u7248\u672c\u5e93\u4e2d\u7684\u4e00\u4e2a\u5206\u652f"
::msgcat::mcset zh_cn "Please supply a branch name." "\u8bf7\u63d0\u4f9b\u5206\u652f\u540d\u5b57."
::msgcat::mcset zh_cn "'%s' is not an acceptable branch name." "'%s'\u4e0d\u662f\u4e00\u4e2a\u53ef\u63a5\u53d7\u7684\u5206\u652f\u540d."
::msgcat::mcset zh_cn "Delete Branch" "\u5220\u9664\u5206\u652f"
::msgcat::mcset zh_cn "Delete Local Branch" "\u5220\u9664\u672c\u5730\u5206\u652f"
::msgcat::mcset zh_cn "Local Branches" "\u672c\u5730\u5206\u652f"
::msgcat::mcset zh_cn "Delete Only If Merged Into" "\u4ec5\u5728\u5408\u5e76\u540e\u5220\u9664"
::msgcat::mcset zh_cn "Always (Do not perform merge test.)" "\u603b\u662f\u5408\u5e76 (\u4e0d\u4f5c\u5408\u5e76\u6d4b\u8bd5.)"
::msgcat::mcset zh_cn "The following branches are not completely merged into %s:" "\u4e0b\u5217\u5206\u652f\u6ca1\u6709\u5b8c\u5168\u88ab\u5408\u5e76\u5230 %s:"
::msgcat::mcset zh_cn "Failed to delete branches:\n%s" "\u65e0\u6cd5\u5220\u9664\u5206\u652f:\n%s"
::msgcat::mcset zh_cn "Rename Branch" "\u66f4\u6539\u5206\u652f\u540d:"
::msgcat::mcset zh_cn "Rename" "\u66f4\u540d..."
::msgcat::mcset zh_cn "Branch:" "\u5206\u652f:"
::msgcat::mcset zh_cn "New Name:" "\u65b0\u540d\u5b57:"
::msgcat::mcset zh_cn "Please select a branch to rename." "\u8bf7\u9009\u62e9\u5206\u652f\u66f4\u540d."
::msgcat::mcset zh_cn "Branch '%s' already exists." "\u5206\u652f '%s' \u5df2\u7ecf\u5b58\u5728."
::msgcat::mcset zh_cn "Failed to rename '%s'." "\u65e0\u6cd5\u66f4\u540d '%s'."
::msgcat::mcset zh_cn "Starting..." "\u5f00\u59cb..."
::msgcat::mcset zh_cn "File Browser" "\u6587\u4ef6\u6d4f\u89c8\u5668"
::msgcat::mcset zh_cn "Loading %s..." "\u88c5\u8f7d %s..."
::msgcat::mcset zh_cn "\[Up To Parent\]" "\[\u4e0a\u5c42\u76ee\u5f55\]"
::msgcat::mcset zh_cn "Browse Branch Files" "\u6d4f\u89c8\u5206\u652f\u6587\u4ef6"
::msgcat::mcset zh_cn "Browse" "\u6d4f\u89c8"
::msgcat::mcset zh_cn "Fetching %s from %s" "\u83b7\u53d6 %s \u81ea %s"
::msgcat::mcset zh_cn "fatal: Cannot resolve %s" "\u81f4\u547d\u9519\u8bef: \u65e0\u6cd5\u89e3\u51b3 %s"
::msgcat::mcset zh_cn "Close" "\u5173\u95ed"
::msgcat::mcset zh_cn "Branch '%s' does not exist." "\u5206\u652f '%s' \u5e76\u4e0d\u5b58\u5728."
::msgcat::mcset zh_cn "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "\u5206\u652f '%s' \u5df2\u7ecf\u5b58\u5728.\n\n\u65e0\u6cd5\u5feb\u901f\u5408\u5e76\u5230 %s.\n\u9700\u8981\u666e\u901a\u5408\u5e76."
::msgcat::mcset zh_cn "Merge strategy '%s' not supported." "\u5408\u5e76\u7b56\u7565 '%s' \u4e0d\u652f\u6301."
::msgcat::mcset zh_cn "Failed to update '%s'." "\u65e0\u6cd5\u66f4\u65b0 '%s'."
::msgcat::mcset zh_cn "Staging area (index) is already locked." "\u7f13\u5b58\u533a\u57df (index) \u5df2\u88ab\u9501\u5b9a."
::msgcat::mcset zh_cn "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "\u6700\u540e\u4e00\u6b21\u626b\u63cf\u7684\u72b6\u6001\u548c\u5f53\u524d\u7248\u672c\u5e93\u72b6\u6001\u4e0d\u7b26.\n\n\u53e6\u4e00 Git \u7a0b\u5e8f\u81ea\u4e0a\u6b21\u626b\u63cf\u540e\u4fee\u6539\u4e86\u672c\u7248\u672c\u5e93. \u5728\u4fee\u6539\u5f53\u524d\u5206\u652f\u4e4b\u524d\u9700\u8981\u91cd\u65b0\u505a\u4e00\u6b21\u626b\u63cf.\n\n\u91cd\u65b0\u626b\u63cf\u5c06\u81ea\u52a8\u5f00\u59cb.\n"
::msgcat::mcset zh_cn "Updating working directory to '%s'..." "\u66f4\u65b0\u5de5\u4f5c\u76ee\u5f55\u5230 '%s'..."
::msgcat::mcset zh_cn "Aborted checkout of '%s' (file level merging is required)." "\u4e2d\u6b62 '%s' \u7684 checkout \u64cd\u4f5c (\u9700\u8981\u505a\u6587\u4ef6\u7ea7\u5408\u5e76)."
::msgcat::mcset zh_cn "File level merge required." "\u9700\u8981\u6587\u4ef6\u7ea7\u5408\u5e76."
::msgcat::mcset zh_cn "Staying on branch '%s'." "\u505c\u7559\u5728\u5206\u652f '%s'."
::msgcat::mcset zh_cn "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "\u4f60\u4e0d\u5728\u67d0\u4e2a\u672c\u5730\u5206\u652f\u4e0a.\n\n\u5982\u679c\u4f60\u60f3\u4f4d\u4e8e\u67d0\u5206\u652f\u4e0a, \u4ece\u5f53\u524d\u8131\u8282\u7684Checkout\u4e2d\u521b\u5efa\u4e00\u4e2a\u65b0\u5206\u652f."
::msgcat::mcset zh_cn "Checked out '%s'." "'%s' \u5df2\u88ab checkout"
::msgcat::mcset zh_cn "Resetting '%s' to '%s' will lose the following commits:" "\u590d\u4f4d '%s' \u5230 '%s' \u5c06\u5bfc\u81f4\u4e0b\u5217\u63d0\u4ea4\u7684\u4e22\u5931:"
::msgcat::mcset zh_cn "Recovering lost commits may not be easy." "\u6062\u590d\u4e22\u5931\u7684\u63d0\u4ea4\u662f\u6bd4\u8f83\u56f0\u96be\u7684."
::msgcat::mcset zh_cn "Reset '%s'?" "\u590d\u4f4d '%s'?"
::msgcat::mcset zh_cn "Visualize" "\u56fe\u793a"
::msgcat::mcset zh_cn "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "\u65e0\u6cd5\u8bbe\u5b9a\u5f53\u524d\u5206\u652f.\n\n\u5f53\u524d\u5de5\u4f5c\u76ee\u5f55\u4ec5\u6709\u90e8\u5206\u88ab\u5207\u6362\u51fa, \u6211\u4eec\u5df2\u6210\u529f\u7684\u66f4\u65b0\u4e86\u60a8\u7684\u6587\u4ef6\u4f46\u662f\u65e0\u6cd5\u66f4\u65b0\u67d0\u4e2a\u5185\u90e8\u7684Git\u6587\u4ef6.\n\n\u8fd9\u672c\u4e0d\u8be5\u53d1\u751f, %s \u5c06\u5173\u95ed\u5e76\u653e\u5f03."
::msgcat::mcset zh_cn "Select" "\u9009\u62e9"
::msgcat::mcset zh_cn "Font Family" "\u5b57\u4f53\u65cf"
::msgcat::mcset zh_cn "Font Size" "\u5b57\u4f53\u5927\u5c0f"
::msgcat::mcset zh_cn "Font Example" "\u5b57\u4f53\u6837\u4f8b"
::msgcat::mcset zh_cn "This is example text.\nIf you like this text, it can be your font." "\u8fd9\u662f\u6837\u4f8b\u6587\u672c.\n\u5982\u679c\u4f60\u559c\u6b22, \u4f60\u53ef\u4ee5\u8bbe\u7f6e\u8be5\u5b57\u4f53."
::msgcat::mcset zh_cn "Git Gui" "Git Gui"
::msgcat::mcset zh_cn "Create New Repository" "\u521b\u5efa\u65b0\u7684\u7248\u672c\u5e93"
::msgcat::mcset zh_cn "New..." "\u65b0\u5efa..."
::msgcat::mcset zh_cn "Clone Existing Repository" "\u514b\u9686\u5df2\u6709\u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Clone..." "\u514b\u9686..."
::msgcat::mcset zh_cn "Open Existing Repository" "\u6253\u5f00\u5df2\u6709\u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Open..." "\u6253\u5f00..."
::msgcat::mcset zh_cn "Recent Repositories" "\u6700\u8fd1\u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Open Recent Repository:" "\u6253\u5f00\u6700\u8fd1\u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Failed to create repository %s:" "\u65e0\u6cd5\u521b\u5efa\u7248\u672c\u5e93 %s:"
::msgcat::mcset zh_cn "Directory:" "\u76ee\u5f55:"
::msgcat::mcset zh_cn "Git Repository" "Git \u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Directory %s already exists." "\u76ee\u5f55 %s \u5df2\u7ecf\u5b58\u5728."
::msgcat::mcset zh_cn "File %s already exists." "\u6587\u4ef6 %s \u5df2\u7ecf\u5b58\u5728."
::msgcat::mcset zh_cn "Clone" "\u514b\u9686"
::msgcat::mcset zh_cn "URL:" "URL:"
::msgcat::mcset zh_cn "Clone Type:" "\u514b\u9686\u7c7b\u578b:"
::msgcat::mcset zh_cn "Standard (Fast, Semi-Redundant, Hardlinks)" "\u6807\u51c6\u65b9\u5f0f (\u5feb\u901f, \u90e8\u5206\u5907\u4efd, \u4f5c\u786c\u8fde\u63a5)"
::msgcat::mcset zh_cn "Full Copy (Slower, Redundant Backup)" "\u5168\u90e8\u590d\u5236 (\u8f83\u6162, \u505a\u5907\u4efd)"
::msgcat::mcset zh_cn "Shared (Fastest, Not Recommended, No Backup)" "\u5171\u4eab\u65b9\u5f0f (\u6700\u5feb, \u4e0d\u63a8\u8350, \u4e0d\u505a\u5907\u4efd)"
::msgcat::mcset zh_cn "Not a Git repository: %s" "\u4e0d\u662f\u4e00\u4e2a Git \u7248\u672c\u5e93: %s"
::msgcat::mcset zh_cn "Standard only available for local repository." "\u6807\u51c6\u65b9\u5f0f\u4ec5\u5f53\u662f\u672c\u5730\u7248\u672c\u5e93\u65f6\u6709\u6548."
::msgcat::mcset zh_cn "Shared only available for local repository." "\u5171\u4eab\u65b9\u5f0f\u4ec5\u5f53\u662f\u672c\u5730\u7248\u672c\u5e93\u65f6\u6709\u6548."
::msgcat::mcset zh_cn "Location %s already exists." "\u4f4d\u7f6e %s \u5df2\u7ecf\u5b58\u5728."
::msgcat::mcset zh_cn "Failed to configure origin" "\u65e0\u6cd5\u914d\u7f6e origin"
::msgcat::mcset zh_cn "Counting objects" "\u6e05\u70b9\u5bf9\u8c61"
::msgcat::mcset zh_cn "Unable to copy objects/info/alternates: %s" "\u65e0\u6cd5\u590d\u5236 objects/info/alternates: %s"
::msgcat::mcset zh_cn "Nothing to clone from %s." "\u6ca1\u6709\u4e1c\u897f\u53ef\u4ece %s \u514b\u9686."
::msgcat::mcset zh_cn "The 'master' branch has not been initialized." "'master'\u5206\u652f\u5c1a\u672a\u521d\u59cb\u5316."
::msgcat::mcset zh_cn "Hardlinks are unavailable.  Falling back to copying." "\u786c\u8fde\u63a5\u4e0d\u53ef\u7528. \u4f7f\u7528\u590d\u5236."
::msgcat::mcset zh_cn "Cloning from %s" "\u4ece %s \u514b\u9686"
::msgcat::mcset zh_cn "Copying objects" "\u590d\u5236 objects"
::msgcat::mcset zh_cn "KiB" "KiB"
::msgcat::mcset zh_cn "Unable to copy object: %s" "\u65e0\u6cd5\u590d\u5236 object: %s"
::msgcat::mcset zh_cn "Linking objects" "\u94fe\u63a5 objects"
::msgcat::mcset zh_cn "objects" "objects"
::msgcat::mcset zh_cn "Unable to hardlink object: %s" "\u65e0\u6cd5\u786c\u94fe\u63a5 object: %s"
::msgcat::mcset zh_cn "Cannot fetch branches and objects.  See console output for details." "\u65e0\u6cd5\u83b7\u53d6\u5206\u652f\u548c\u5bf9\u8c61. \u8bf7\u67e5\u770b\u63a7\u5236\u7ec8\u7aef\u7684\u8f93\u51fa."
::msgcat::mcset zh_cn "Cannot fetch tags.  See console output for details." "\u65e0\u6cd5\u83b7\u53d6\u6807\u7b7e. \u8bf7\u67e5\u770b\u63a7\u5236\u7ec8\u7aef\u7684\u8f93\u51fa."
::msgcat::mcset zh_cn "Cannot determine HEAD.  See console output for details." "\u65e0\u6cd5\u786e\u5b9a HEAD. \u8bf7\u67e5\u770b\u63a7\u5236\u7ec8\u7aef\u7684\u8f93\u51fa."
::msgcat::mcset zh_cn "Unable to cleanup %s" "\u65e0\u6cd5\u6e05\u7406 %s"
::msgcat::mcset zh_cn "Clone failed." "\u514b\u9686\u5931\u8d25."
::msgcat::mcset zh_cn "No default branch obtained." "\u6ca1\u6709\u83b7\u53d6\u7f3a\u7701\u5206\u652f"
::msgcat::mcset zh_cn "Cannot resolve %s as a commit." "\u65e0\u6cd5\u89e3\u6790 %s \u4e3a\u63d0\u4ea4."
::msgcat::mcset zh_cn "Creating working directory" "\u521b\u5efa\u5de5\u4f5c\u76ee\u5f55"
::msgcat::mcset zh_cn "files" "\u6587\u4ef6"
::msgcat::mcset zh_cn "Initial file checkout failed." "\u521d\u59cb\u7684\u6587\u4ef6checkout\u5931\u8d25"
::msgcat::mcset zh_cn "Open" "\u6253\u5f00"
::msgcat::mcset zh_cn "Repository:" "\u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Failed to open repository %s:" "\u65e0\u6cd5\u6253\u5f00\u7248\u672c\u5e93 %s:"
::msgcat::mcset zh_cn "This Detached Checkout" "\u8be5\u8131\u8282\u7684Checkout"
::msgcat::mcset zh_cn "Revision Expression:" "\u7248\u672c\u8868\u8fbe\u5f0f:"
::msgcat::mcset zh_cn "Local Branch" "\u672c\u5730\u5206\u652f"
::msgcat::mcset zh_cn "Tracking Branch" "\u8ddf\u8e2a\u5206\u652f:"
::msgcat::mcset zh_cn "Tag" "\u6807\u7b7e"
::msgcat::mcset zh_cn "Invalid revision: %s" "\u65e0\u6548\u7248\u672c: %s"
::msgcat::mcset zh_cn "No revision selected." "\u6ca1\u6709\u9009\u62e9\u7248\u672c."
::msgcat::mcset zh_cn "Revision expression is empty." "\u7248\u672c\u8868\u8fbe\u5f0f\u4e3a\u7a7a."
::msgcat::mcset zh_cn "Updated" "\u5df2\u66f4\u65b0"
::msgcat::mcset zh_cn "URL" "URL"
::msgcat::mcset zh_cn "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "\u6ca1\u6709\u6539\u52a8\u9700\u8981\u4fee\u6b63.\n\n\u4f60\u6b63\u5728\u521b\u5efa\u6700\u521d\u7684\u63d0\u4ea4. \u5728\u6b64\u4e4b\u524d\u6ca1\u6709\u63d0\u4ea4\u53ef\u4ee5\u4fee\u6b63.\n"
::msgcat::mcset zh_cn "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "\u5728\u5408\u5e76\u65f6\u65e0\u6cd5\u4fee\u6b63.\n\n\u4f60\u5f53\u524d\u6b63\u5728\u4e00\u6b21\u5c1a\u672a\u5b8c\u6210\u7684\u5408\u5e76\u64cd\u4f5c\u8fc7\u7a0b\u4e2d. \u9664\u975e\u4e2d\u6b62\u5f53\u524d\u5408\u5e76\u6d3b\u52a8,\n\u5426\u5219\u65e0\u6cd5\u4fee\u6b63\u4e4b\u524d\u7684\u63d0\u4ea4.\n"
::msgcat::mcset zh_cn "Error loading commit data for amend:" "\u4e3a\u4fee\u6b63\u88c5\u8f7d\u63d0\u4ea4\u6570\u636e\u51fa\u9519:"
::msgcat::mcset zh_cn "Unable to obtain your identity:" "\u65e0\u6cd5\u83b7\u77e5\u4f60\u7684\u8eab\u4efd:"
::msgcat::mcset zh_cn "Invalid GIT_COMMITTER_IDENT:" "\u65e0\u6548\u7684 GIT_COMMITTER_IDENT"
::msgcat::mcset zh_cn "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "\u6700\u540e\u4e00\u6b21\u626b\u63cf\u7684\u72b6\u6001\u548c\u5f53\u524d\u7248\u672c\u5e93\u72b6\u6001\u4e0d\u7b26.\n\n\u53e6\u4e00 Git \u7a0b\u5e8f\u81ea\u4e0a\u6b21\u626b\u63cf\u540e\u4fee\u6539\u4e86\u672c\u7248\u672c\u5e93. \u5728\u4fee\u6539\u5f53\u524d\u5206\u652f\u4e4b\u524d\u9700\u8981\u91cd\u65b0\u505a\u4e00\u6b21\u626b\u63cf.\n\n\u91cd\u65b0\u626b\u63cf\u5c06\u81ea\u52a8\u5f00\u59cb.\n"
::msgcat::mcset zh_cn "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "\u5c1a\u672a\u5408\u5e76\u7684\u6587\u4ef6\u6ca1\u6709\u529e\u6cd5\u63d0\u4ea4.\n\n\u6587\u4ef6 %s \u6709\u5408\u5e76\u51b2\u7a81, \u4f60\u5fc5\u987b\u89e3\u51b3\u8fd9\u4e9b\u51b2\u7a81\u5e76\u7f13\u5b58\u8be5\u6587\u4ef6\u4f5c\u63d0\u4ea4.\n"
::msgcat::mcset zh_cn "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "\u68c0\u6d4b\u5230\u672a\u77e5\u6587\u4ef6\u72b6\u6001 %s.\n\n\u6587\u4ef6 %s \u65e0\u6cd5\u7531\u8be5\u7a0b\u5e8f\u63d0\u4ea4.\n"
::msgcat::mcset zh_cn "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "\u6ca1\u6709\u9700\u8981\u63d0\u4ea4\u7684\u53d8\u52a8.\n\n\u63d0\u4ea4\u524d\u4f60\u5fc5\u987b\u9996\u5148\u7f13\u5b58\u81f3\u5c11\u4e00\u4e2a\u6587\u4ef6.\n"
::msgcat::mcset zh_cn "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "\u8bf7\u63d0\u4f9b\u4e00\u6761\u63d0\u4ea4\u4fe1\u606f.\n\n\u4e00\u6761\u597d\u7684\u63d0\u4ea4\u4fe1\u606f\u6709\u4e0b\u5217\u683c\u5f0f:\n\n- \u7b2c\u4e00\u884c: \u4e00\u53e5\u8bdd\u6982\u62ec\u4f60\u505a\u7684\u4fee\u6539.\n- \u7b2c\u4e8c\u884c: \u7a7a\u884c\n- \u5269\u4f59\u884c: \u8bf7\u63cf\u8ff0\u4e3a\u4ec0\u4e48\u4f60\u505a\u7684\u8fd9\u4e9b\u6539\u52a8\u662f\u597d\u7684.\n"
::msgcat::mcset zh_cn "warning: Tcl does not support encoding '%s'." "\u8b66\u544a: Tcl \u4e0d\u652f\u6301\u7f16\u7801\u65b9\u5f0f '%s'."
::msgcat::mcset zh_cn "write-tree failed:" "write-tree \u5931\u8d25:"
::msgcat::mcset zh_cn "Commit %s appears to be corrupt" "\u63d0\u4ea4 %s \u4f3c\u4e4e\u5df2\u635f\u574f"
::msgcat::mcset zh_cn "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "\u6ca1\u6709\u6539\u52a8\u63d0\u4ea4.\n\n\u8be5\u63d0\u4ea4\u6ca1\u6709\u6539\u52a8\u4efb\u4f55\u6587\u4ef6\u4e5f\u4e0d\u662f\u4e00\u4e2a\u5408\u5e76\u63d0\u4ea4.\n\n\u91cd\u65b0\u626b\u63cf\u5c06\u81ea\u52a8\u5f00\u59cb.\n"
::msgcat::mcset zh_cn "No changes to commit." "\u6ca1\u6709\u6539\u52a8\u8981\u63d0\u4ea4."
::msgcat::mcset zh_cn "commit-tree failed:" "commit-tree \u5931\u8d25:"
::msgcat::mcset zh_cn "update-ref failed:" "update-ref \u5931\u8d25:"
::msgcat::mcset zh_cn "Created commit %s: %s" "\u521b\u5efa\u4e86 commit %s: %s"
::msgcat::mcset zh_cn "Working... please wait..." "\u5de5\u4f5c\u4e2d... \u8bf7\u7b49\u5f85..."
::msgcat::mcset zh_cn "Success" "\u6210\u529f"
::msgcat::mcset zh_cn "Error: Command Failed" "\u9519\u8bef: \u547d\u4ee4\u5931\u8d25"
::msgcat::mcset zh_cn "Number of loose objects" "\u677e\u6563\u5bf9\u8c61\u7684\u6570\u91cf"
::msgcat::mcset zh_cn "Disk space used by loose objects" "\u677e\u6563\u5bf9\u8c61\u6240\u4f7f\u7528\u7684\u78c1\u76d8\u7a7a\u95f4"
::msgcat::mcset zh_cn "Number of packed objects" "\u538b\u7f29\u5bf9\u8c61\u6570\u91cf"
::msgcat::mcset zh_cn "Number of packs" "\u538b\u7f29\u5305\u6570\u91cf"
::msgcat::mcset zh_cn "Disk space used by packed objects" "\u538b\u7f29\u5bf9\u8c61\u6240\u4f7f\u7528\u7684\u78c1\u76d8\u7a7a\u95f4"
::msgcat::mcset zh_cn "Packed objects waiting for pruning" "\u538b\u7f29\u5bf9\u8c61\u7b49\u5f85\u6e05\u7406"
::msgcat::mcset zh_cn "Garbage files" "\u5783\u573e\u6587\u4ef6"
::msgcat::mcset zh_cn "Compressing the object database" "\u538b\u7f29\u5bf9\u8c61\u6570\u636e\u5e93"
::msgcat::mcset zh_cn "Verifying the object database with fsck-objects" "\u4f7f\u7528 fsck-objects \u9a8c\u8bc1\u5bf9\u8c61\u6570\u636e\u5e93"
::msgcat::mcset zh_cn "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database when more than %i loose objects exist.\n\nCompress the database now?" "\u8be5\u7248\u672c\u5e93\u5f53\u524d\u7ea6\u6709 %i \u4e2a\u677e\u6563\u5bf9\u8c61.\n\n\u4e3a\u8fbe\u5230\u8f83\u4f18\u7684\u6027\u80fd\uff0c\u5f3a\u70c8\u5efa\u8bae\u4f60\u5728\u677e\u6563\u5bf9\u8c61\u591a\u4e8e %i \u65f6\u538b\u7f29\u6570\u636e\u5e93.\n\n\u73b0\u5728\u5c31\u538b\u7f29\u6570\u636e\u5e93\u4e48?"
::msgcat::mcset zh_cn "Invalid date from Git: %s" "\u65e0\u6548\u7684\u65e5\u671f: %s"
::msgcat::mcset zh_cn "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "\u672a\u68c0\u6d4b\u5230\u6539\u52a8.\n\n\u8be5\u6587\u4ef6\u7684\u4fee\u6539\u65e5\u671f\u88ab\u53e6\u4e00\u4e2a\u7a0b\u5e8f\u6240\u66f4\u65b0, \u4f46\u5176\u5185\u5bb9\u5e76\u6ca1\u6709\u53d8\u5316.\n\n\u5bf9\u4e8e\u7c7b\u4f3c\u60c5\u51b5\u7684\u5176\u4ed6\u6587\u4ef6\u7684\u91cd\u65b0\u626b\u63cf\u5c06\u81ea\u52a8\u5f00\u59cb."
::msgcat::mcset zh_cn "Unable to display %s" "\u65e0\u6cd5\u663e\u793a %s"
::msgcat::mcset zh_cn "Error loading file:" "\u88c5\u8f7d\u6587\u4ef6\u51fa\u9519:"
::msgcat::mcset zh_cn "Git Repository (subproject)" "Git \u7248\u672c\u5e93 (\u5b50\u9879\u76ee)"
::msgcat::mcset zh_cn "* Binary file (not showing content)." "* \u4e8c\u8fdb\u5236\u6587\u4ef6 (\u4e0d\u663e\u793a\u5185\u5bb9)."
::msgcat::mcset zh_cn "Error loading diff:" "\u88c5\u8f7d diff \u9519\u8bef:"
::msgcat::mcset zh_cn "Failed to unstage selected hunk." "\u65e0\u6cd5\u5c06\u9009\u62e9\u7684\u4ee3\u7801\u6bb5\u4ece\u7f13\u5b58\u4e2d\u5220\u9664."
::msgcat::mcset zh_cn "Failed to stage selected hunk." "\u65e0\u6cd5\u7f13\u5b58\u6240\u9009\u4ee3\u7801\u6bb5."
::msgcat::mcset zh_cn "error" "\u9519\u8bef"
::msgcat::mcset zh_cn "warning" "\u8b66\u544a"
::msgcat::mcset zh_cn "You must correct the above errors before committing." "\u4f60\u5fc5\u987b\u5728\u63d0\u4ea4\u524d\u4fee\u6b63\u4e0a\u8ff0\u9519\u8bef."
::msgcat::mcset zh_cn "Unable to unlock the index." "\u65e0\u6cd5\u89e3\u9501\u7f13\u5b58 (index)"
::msgcat::mcset zh_cn "Index Error" "\u7f13\u5b58(Index)\u9519\u8bef"
::msgcat::mcset zh_cn "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "\u66f4\u65b0 Git \u7f13\u5b58(Index)\u5931\u8d25, \u91cd\u65b0\u626b\u63cf\u5c06\u81ea\u52a8\u5f00\u59cb\u4ee5\u91cd\u65b0\u540c\u6b65 git-gui."
::msgcat::mcset zh_cn "Continue" "\u7ee7\u7eed"
::msgcat::mcset zh_cn "Unlock Index" "\u89e3\u9501 Index"
::msgcat::mcset zh_cn "Unstaging %s from commit" "\u4ece\u63d0\u4ea4\u7f13\u5b58\u4e2d\u5220\u9664 %s"
::msgcat::mcset zh_cn "Adding %s" "\u6dfb\u52a0 %s"
::msgcat::mcset zh_cn "Revert changes in file %s?" "\u64a4\u9500\u6587\u4ef6 %s \u4e2d\u7684\u6539\u52a8?"
::msgcat::mcset zh_cn "Revert changes in these %i files?" "\u64a4\u9500\u8fd9\u4e9b (%i\u4e2a) \u6587\u4ef6\u7684\u6539\u52a8?"
::msgcat::mcset zh_cn "Any unstaged changes will be permanently lost by the revert." "\u4efb\u4f55\u672a\u7f13\u5b58\u7684\u6539\u52a8\u5c06\u5728\u8fd9\u6b21\u64a4\u9500\u4e2d\u6c38\u4e45\u4e22\u5931."
::msgcat::mcset zh_cn "Do Nothing" "\u4e0d\u505a\u64cd\u4f5c"
::msgcat::mcset zh_cn "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "\u4fee\u6b63\u65f6\u65e0\u6cd5\u505a\u5408\u5e76.\n\n\u4f60\u5fc5\u987b\u5b8c\u6210\u5bf9\u8be5\u63d0\u4ea4\u7684\u4fee\u6b63\u624d\u80fd\u7ee7\u7eed\u4efb\u4f55\u7c7b\u578b\u7684\u5408\u5e76\u64cd\u4f5c.\n"
::msgcat::mcset zh_cn "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "\u6700\u540e\u4e00\u6b21\u626b\u63cf\u7684\u72b6\u6001\u548c\u5f53\u524d\u7248\u672c\u5e93\u72b6\u6001\u4e0d\u7b26.\n\n\u53e6\u4e00 Git \u7a0b\u5e8f\u81ea\u4e0a\u6b21\u626b\u63cf\u540e\u4fee\u6539\u4e86\u672c\u7248\u672c\u5e93. \u5728\u4fee\u6539\u5f53\u524d\u5206\u652f\u4e4b\u524d\u9700\u8981\u91cd\u65b0\u505a\u4e00\u6b21\u626b\u63cf.\n\n\u91cd\u65b0\u626b\u63cf\u5c06\u81ea\u52a8\u5f00\u59cb.\n"
::msgcat::mcset zh_cn "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "\u4f60\u6b63\u5904\u5728\u4e00\u4e2a\u6709\u51b2\u7a81\u7684\u5408\u5e76\u64cd\u4f5c\u4e2d.\n\n\u6587\u4ef6 %s \u6709\u5408\u5e76\u51b2\u7a81.\n\n\u4f60\u5fc5\u987b\u89e3\u51b3\u8fd9\u4e9b\u51b2\u7a81, \u7f13\u5b58\u8be5\u6587\u4ef6, \u5e76\u63d0\u4ea4\u6765\u5b8c\u6210\u5f53\u524d\u7684\u5408\u5e76.\u4ec5\u5f53\u8fd9\u6837\u540e\u624d\u80fd\u5f00\u59cb\u4e0b\u4e00\u4e2a\u5408\u5e76\u64cd\u4f5c.\n"
::msgcat::mcset zh_cn "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "\u4f60\u6b63\u5904\u5728\u4e00\u4e2a\u6539\u52a8\u5f53\u4e2d.\n\n\u6587\u4ef6 %s \u5df2\u88ab\u4fee\u6539.\n\n\u4f60\u5fc5\u987b\u5b8c\u6210\u5f53\u524d\u7684\u63d0\u4ea4\u540e\u624d\u80fd\u5f00\u59cb\u5408\u5e76. \u5982\u679c\u9700\u8981, \u8fd9\u4e48\u505a\u5c06\u6709\u52a9\u4e8e\u4e2d\u6b62\u4e00\u6b21\u5931\u8d25\u7684\u5408\u5e76.\n"
::msgcat::mcset zh_cn "Merge completed successfully." "\u5408\u5e76\u6210\u529f\u5b8c\u6210."
::msgcat::mcset zh_cn "Merge failed.  Conflict resolution is required." "\u5408\u5e76\u5931\u8d25. \u9700\u8981\u89e3\u51b3\u51b2\u7a81."
::msgcat::mcset zh_cn "Merge Into %s" "\u5408\u5e76\u5230 %s"
::msgcat::mcset zh_cn "Revision To Merge" "\u8981\u5408\u5e76\u7684\u7248\u672c"
::msgcat::mcset zh_cn "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "\u4fee\u6b63\u64cd\u4f5c\u4e2d\u65e0\u6cd5\u4e2d\u6b62.\n\n\u4f60\u5fc5\u987b\u5148\u5b8c\u6210\u672c\u6b21\u4fee\u6b63\u64cd\u4f5c.\n"
::msgcat::mcset zh_cn "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "\u4e2d\u6b62\u5408\u5e76?\n\n\u4e2d\u6b62\u5f53\u524d\u7684\u5408\u5e76\u64cd\u4f5c\u5c06\u5bfc\u81f4 *\u6240\u6709* \u5c1a\u672a\u63d0\u4ea4\u7684\u6539\u52a8\u4e22\u5931.\n\n\u662f\u5426\u8981\u7ee7\u7eed\u4e2d\u6b62\u5f53\u524d\u7684\u5408\u5e76\u64cd\u4f5c?"
::msgcat::mcset zh_cn "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "\u662f\u5426\u590d\u4f4d\u5f53\u524d\u6539\u52a8?\n\n\u590d\u4f4d\u5f53\u524d\u7684\u6539\u52a8\u5c06\u5bfc\u81f4 *\u6240\u6709* \u672a\u63d0\u4ea4\u7684\u6539\u52a8\u4e22\u5931.\n\n\u662f\u5426\u8981\u7ee7\u7eed\u590d\u4f4d\u5f53\u524d\u7684\u6539\u52a8?"
::msgcat::mcset zh_cn "Aborting" "\u4e2d\u6b62"
::msgcat::mcset zh_cn "Abort failed." "\u4e2d\u6b62\u5931\u8d25"
::msgcat::mcset zh_cn "Abort completed.  Ready." "\u4e2d\u6b62\u5b8c\u6210. \u5c31\u7eea."
::msgcat::mcset zh_cn "Restore Defaults" "\u6062\u590d\u9ed8\u8ba4\u503c"
::msgcat::mcset zh_cn "Save" "\u4fdd\u5b58"
::msgcat::mcset zh_cn "%s Repository" "%s \u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Global (All Repositories)" "\u5168\u5c40 (\u6240\u6709\u7248\u672c\u5e93)"
::msgcat::mcset zh_cn "User Name" "\u7528\u6237\u540d"
::msgcat::mcset zh_cn "Email Address" "Email \u5730\u5740"
::msgcat::mcset zh_cn "Summarize Merge Commits" "\u6982\u8ff0\u5408\u5e76\u63d0\u4ea4:"
::msgcat::mcset zh_cn "Merge Verbosity" "\u5408\u5e76\u5197\u4f59\u5ea6"
::msgcat::mcset zh_cn "Show Diffstat After Merge" "\u5728\u5408\u5e76\u540e\u663e\u793a Diffstat"
::msgcat::mcset zh_cn "Trust File Modification Timestamps" "\u76f8\u4fe1\u6587\u4ef6\u7684\u6539\u52a8\u65f6\u95f4"
::msgcat::mcset zh_cn "Prune Tracking Branches During Fetch" "\u83b7\u53d6\u65f6\u6e05\u9664\u8ddf\u8e2a\u5206\u652f"
::msgcat::mcset zh_cn "Match Tracking Branches" "\u5339\u914d\u8ddf\u8e2a\u5206\u652f"
::msgcat::mcset zh_cn "Number of Diff Context Lines" "Diff \u4e0a\u4e0b\u6587\u884c\u6570"
::msgcat::mcset zh_cn "New Branch Name Template" "\u65b0\u5efa\u5206\u652f\u547d\u540d\u6a21\u677f"
::msgcat::mcset zh_cn "Change Font" "\u66f4\u6539\u5b57\u4f53"
::msgcat::mcset zh_cn "Choose %s" "\u9009\u62e9 %s"
::msgcat::mcset zh_cn "pt." "\u78c5"
::msgcat::mcset zh_cn "Preferences" "\u9996\u9009\u9879"
::msgcat::mcset zh_cn "Failed to completely save options:" "\u65e0\u6cd5\u5b8c\u5168\u4fdd\u5b58\u9009\u9879:"
::msgcat::mcset zh_cn "Delete Remote Branch" "\u5220\u9664\u8fdc\u7aef\u5206\u652f"
::msgcat::mcset zh_cn "From Repository" "\u4ece\u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Remote:" "Remote:"
::msgcat::mcset zh_cn "Arbitrary URL:" "\u4efb\u610f URL:"
::msgcat::mcset zh_cn "Branches" "\u5206\u652f"
::msgcat::mcset zh_cn "Delete Only If" "\u5220\u9664\u4ec5\u5f53"
::msgcat::mcset zh_cn "Merged Into:" "\u5408\u5e76\u5230"
::msgcat::mcset zh_cn "Always (Do not perform merge checks)" "\u603b\u662f\u5408\u5e76 (\u4e0d\u4f5c\u5408\u5e76\u68c0\u67e5)"
::msgcat::mcset zh_cn "A branch is required for 'Merged Into'." "'\u5408\u5e76\u5230' \u9700\u8981\u6307\u5b9a\u67d0\u4e2a\u5206\u652f"
::msgcat::mcset zh_cn "The following branches are not completely merged into %s:\n\n - %s" "\u4e0b\u5217\u5206\u652f\u6ca1\u6709\u88ab\u5168\u90e8\u5408\u5e76\u5230 %s \u4e2d:\n\n - %s"
::msgcat::mcset zh_cn "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "\u7531\u4e8e\u6ca1\u6709\u83b7\u53d6\u5230\u5fc5\u8981\u7684\u63d0\u4ea4\uff0c\u4e00\u4e2a\u6216\u591a\u4e2a\u5408\u5e76\u6d4b\u8bd5\u5931\u8d25\u3002\u8bf7\u5c1d\u8bd5\u4ece %s \u5904\u5148\u83b7\u53d6\u3002"
::msgcat::mcset zh_cn "Please select one or more branches to delete." "\u8bf7\u9009\u62e9\u67d0\u4e2a\u6216\u591a\u4e2a\u5206\u652f\u6765\u5220\u9664"
::msgcat::mcset zh_cn "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "\u6062\u590d\u88ab\u5220\u9664\u7684\u5206\u652f\u975e\u5e38\u56f0\u96be.\n\n\u662f\u5426\u8981\u5220\u9664\u6240\u9009\u5206\u652f?"
::msgcat::mcset zh_cn "Deleting branches from %s" "\u4ece %s \u4e2d\u5220\u9664\u5206\u652f"
::msgcat::mcset zh_cn "No repository selected." "\u6ca1\u6709\u9009\u62e9\u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Scanning %s..." "\u6b63\u5728\u626b\u63cf %s..."
::msgcat::mcset zh_cn "Prune from" "\u4ece..\u6e05\u9664(prune)"
::msgcat::mcset zh_cn "Fetch from" "\u4ece..\u83b7\u53d6(fetch)"
::msgcat::mcset zh_cn "Push to" "\u4e0a\u4f20\u5230(push)"
::msgcat::mcset zh_cn "Cannot write shortcut:" "\u65e0\u6cd5\u4fee\u6539\u5feb\u6377\u65b9\u5f0f:"
::msgcat::mcset zh_cn "Cannot write icon:" "\u65e0\u6cd5\u4fee\u6539\u56fe\u6807:"
::msgcat::mcset zh_cn "%s ... %*i of %*i %s (%3i%%)" "%s ... %*i of %*i %s (%3i%%)"
::msgcat::mcset zh_cn "fetch %s" "\u83b7\u53d6(fetch)"
::msgcat::mcset zh_cn "Fetching new changes from %s" "\u4ece %s \u5904\u83b7\u53d6\u65b0\u7684\u6539\u52a8"
::msgcat::mcset zh_cn "remote prune %s" "\u6e05\u9664\u8fdc\u7aef %s"
::msgcat::mcset zh_cn "Pruning tracking branches deleted from %s" "\u6e05\u9664"
::msgcat::mcset zh_cn "push %s" "\u4e0a\u4f20 %s"
::msgcat::mcset zh_cn "Pushing changes to %s" "\u4e0a\u4f20\u6539\u52a8\u5230 %s"
::msgcat::mcset zh_cn "Pushing %s %s to %s" "\u4e0a\u4f20 %s %s \u5230 %s"
::msgcat::mcset zh_cn "Push Branches" "\u4e0a\u4f20\u5206\u652f"
::msgcat::mcset zh_cn "Source Branches" "\u6e90\u7aef\u5206\u652f:"
::msgcat::mcset zh_cn "Destination Repository" "\u76ee\u6807\u7248\u672c\u5e93"
::msgcat::mcset zh_cn "Transfer Options" "\u4f20\u8f93\u9009\u9879"
::msgcat::mcset zh_cn "Force overwrite existing branch (may discard changes)" "\u5f3a\u5236\u8986\u76d6\u5df2\u6709\u7684\u5206\u652f (\u53ef\u80fd\u4f1a\u4e22\u5931\u6539\u52a8)"
::msgcat::mcset zh_cn "Use thin pack (for slow network connections)" "\u4f7f\u7528 thin pack (\u9002\u7528\u4e8e\u4f4e\u901f\u7f51\u7edc\u8fde\u63a5)"
::msgcat::mcset zh_cn "Include tags" "\u5305\u542b\u6807\u7b7e"
