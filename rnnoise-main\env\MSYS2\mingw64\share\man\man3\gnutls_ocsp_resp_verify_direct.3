.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_verify_direct" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_verify_direct \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_verify_direct(gnutls_ocsp_resp_const_t " resp ", gnutls_x509_crt_t " issuer ", unsigned int * " verify ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
should contain a \fBgnutls_ocsp_resp_t\fP type
.IP "gnutls_x509_crt_t issuer" 12
certificate believed to have signed the response
.IP "unsigned int * verify" 12
output variable with verification status, an \fBgnutls_ocsp_verify_reason_t\fP
.IP "unsigned int flags" 12
verification flags from \fBgnutls_certificate_verify_flags\fP
.SH "DESCRIPTION"
Verify signature of the Basic OCSP Response against the public key
in the  \fIissuer\fP certificate.

The output  \fIverify\fP variable will hold verification status codes
(e.g., \fBGNUTLS_OCSP_VERIFY_SIGNER_NOT_FOUND\fP,
\fBGNUTLS_OCSP_VERIFY_INSECURE_ALGORITHM\fP) which are only valid if the
function returned \fBGNUTLS_E_SUCCESS\fP.

Note that the function returns \fBGNUTLS_E_SUCCESS\fP even when
verification failed.  The caller must always inspect the  \fIverify\fP variable to find out the verification status.

The  \fIflags\fP variable should be 0 for now.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
