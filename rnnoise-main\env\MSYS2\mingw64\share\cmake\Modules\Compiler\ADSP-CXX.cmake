include(Compiler/CMakeCommonCompilerMacros)
include(Compiler/ADSP)

__compiler_adsp(CXX)

set(CMAKE_CXX98_STANDARD_COMPILE_OPTION -c++)
set(CMAKE_CXX98_EXTENSION_COMPILE_OPTION -g++)
set(CMAKE_CXX98_STANDARD__HAS_FULL_SUPPORT ON)
set(CMAKE_CXX_STANDARD_LATEST 98)

if(CMAKE_CXX_COMPILER_VERSION VERSION_GREATER_EQUAL 8.3.0.0)
  set(CMAKE_CXX11_STANDARD_COMPILE_OPTION -c++11)
  set(CMAKE_CXX11_EXTENSION_COMPILE_OPTION -c++11 -g++)
  set(CMAKE_CXX11_STANDARD__HAS_FULL_SUPPORT ON)
  set(CMAKE_CXX_STANDARD_LATEST 11)
endif()

__compiler_check_default_language_standard(CXX 8.0.0.0 98)
