.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srp_verifier" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srp_verifier \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srp_verifier(const char * " username ", const char * " password ", const gnutls_datum_t * " salt ", const gnutls_datum_t * " generator ", const gnutls_datum_t * " prime ", gnutls_datum_t * " res ");"
.SH ARGUMENTS
.IP "const char * username" 12
is the user's name
.IP "const char * password" 12
is the user's password
.IP "const gnutls_datum_t * salt" 12
should be some randomly generated bytes
.IP "const gnutls_datum_t * generator" 12
is the generator of the group
.IP "const gnutls_datum_t * prime" 12
is the group's prime
.IP "gnutls_datum_t * res" 12
where the verifier will be stored.
.SH "DESCRIPTION"
This function will create an SRP verifier, as specified in
RFC2945.  The  \fIprime\fP and  \fIgenerator\fP should be one of the static
parameters defined in gnutls/gnutls.h or may be generated.

The verifier will be allocated with @\fBgnutls_malloc()\fP and will be stored in
 \fIres\fP using binary format.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, or an
error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
