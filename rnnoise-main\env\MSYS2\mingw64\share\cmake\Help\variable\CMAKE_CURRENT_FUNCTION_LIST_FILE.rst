CMAKE_CURRENT_FUNCTION_LIST_FILE
--------------------------------

.. versionadded:: 3.17

When executing code inside a :command:`function`, this variable
contains the full path to the listfile that defined the current function.

See also :variable:`CMAKE_CURRENT_FUNCTION`,
:variable:`CMAKE_CURRENT_FUNCTION_LIST_DIR`,
:variable:`CMAKE_CURRENT_FUNCTION_LIST_LINE` and
:variable:`CMAKE_CURRENT_LIST_FILE`.
