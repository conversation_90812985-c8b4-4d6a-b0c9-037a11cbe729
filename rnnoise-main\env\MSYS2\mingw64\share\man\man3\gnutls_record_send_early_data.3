.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_send_early_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_send_early_data \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_send_early_data(gnutls_session_t " session ", const void * " data ", size_t " data_size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const void * data" 12
contains the data to send
.IP "size_t data_size" 12
is the length of the data
.SH "DESCRIPTION"
This function can be used by a client to send data early in the
handshake processes when resuming a session.  This is used to
implement a zero\-roundtrip (0\-RTT) mode.  It has the same semantics
as \fBgnutls_record_send()\fP.

There may be a limit to the amount of data sent as early data.  Use
\fBgnutls_record_get_max_early_data_size()\fP to check the limit.  If the
limit exceeds, this function returns
\fBGNUTLS_E_RECORD_LIMIT_REACHED\fP.
.SH "RETURNS"
The number of bytes sent, or a negative error code.  The
number of bytes sent might be less than  \fIdata_size\fP .  The maximum
number of bytes this function can send in a single call depends
on the negotiated maximum record size.
.SH "SINCE"
3.6.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
