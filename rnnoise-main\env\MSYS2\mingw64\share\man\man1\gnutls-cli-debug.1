.de1 NOP
.  it 1 an-trap
.  if \\n[.$] \,\\$*\/
..
.ie t \
.ds B-Font [CB]
.ds I-Font [CI]
.ds R-Font [CR]
.el \
.ds B-Font B
.ds I-Font I
.ds R-Font R
.TH gnutls-cli-debug 1 "08 Feb 2025" "3.8.9" "User Commands"
.SH NAME
\f\*[B-Font]gnutls-cli-debug\fP
\- GnuTLS debug client
.SH SYNOPSIS
\f\*[B-Font]gnutls-cli-debug\fP
.\" Mixture of short (flag) options and long options
[\f\*[B-Font]\-flags\f[]]
[\f\*[B-Font]\-flag\f[] [\f\*[I-Font]value\f[]]]
[\f\*[B-Font]\-\-option-name\f[][[=| ]\f\*[I-Font]value\f[]]]
[hostname]
.sp \n(Ppu
.ne 2

Operands and options may be intermixed.  They will be reordered.
.sp \n(Ppu
.ne 2
.SH "DESCRIPTION"
TLS debug client. It sets up multiple TLS connections to 
a server and queries its capabilities. It was created to assist in debugging 
GnuTLS, but it might be useful to extract a TLS server's capabilities.
It connects to a TLS server, performs tests and print the server's 
capabilities. If called with the `-V' parameter more checks will be performed.
Can be used to check for servers with special needs or bugs.
.sp
.SH "OPTIONS"
.TP
.NOP \f\*[B-Font]\-d\f[] \f\*[I-Font]num\f[], \f\*[B-Font]\-\-debug\f[]=\f\*[I-Font]num\f[]
Enable debugging.
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 9999
.fi
.in -4
.sp
Specifies the debug level.
.TP
.NOP \f\*[B-Font]\-V\f[], \f\*[B-Font]\-\-verbose\f[]
More verbose output.
.sp
.TP
.NOP \f\*[B-Font]\-p\f[] \f\*[I-Font]num\f[], \f\*[B-Font]\-\-port\f[]=\f\*[I-Font]num\f[]
The port to connect to.
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 65536
.fi
.in -4
.sp
.TP
.NOP \f\*[B-Font]\-\-app\-proto\f[]
This is an alias for the \fI--starttls-proto\fR option.
.TP
.NOP \f\*[B-Font]\-\-starttls\-proto\f[]=\f\*[I-Font]str\f[]
The application protocol to be used to obtain the server's certificate (https, ftp, smtp, imap, ldap, xmpp, lmtp, pop3, nntp, sieve, postgres).
.sp
Specify the application layer protocol for STARTTLS. If the protocol is supported, gnutls\-cli will proceed to the TLS negotiation.
.TP
.NOP \f\*[B-Font]\-\-attime\f[]=\f\*[I-Font]timestamp\f[]
Perform validation at the timestamp instead of the system time.
.sp
timestamp is an instance in time encoded as Unix time or in a human
 readable timestring such as "29 Feb 2004", "2004\-02\-29".
Full documentation available at 
<https://www.gnu.org/software/coreutils/manual/html_node/Date\-input\-formats.html>
or locally via info '(coreutils) date invocation'.
.TP
.NOP \f\*[B-Font]\-v\f[] \f\*[I-Font]arg\f[], \f\*[B-Font]\-\-version\f[]=\f\*[I-Font]arg\f[]
Output version of program and exit.  The default mode is `v', a simple
version.  The `c' mode will print copyright information and `n' will
print the full copyright notice.
.TP
.NOP \f\*[B-Font]\-h\f[], \f\*[B-Font]\-\-help\f[]
Display usage information and exit.
.TP
.NOP \f\*[B-Font]\-!\f[], \f\*[B-Font]\-\-more\-help\f[]
Pass the extended usage information through a pager.

.sp
.SH EXAMPLES
.br
.in +4
.nf
$ gnutls\-cli\-debug localhost
GnuTLS debug client 3.5.0
Checking localhost:443
                             for SSL 3.0 (RFC6101) support... yes
                        whether we need to disable TLS 1.2... no
                        whether we need to disable TLS 1.1... no
                        whether we need to disable TLS 1.0... no
                        whether %NO_EXTENSIONS is required... no
                               whether %COMPAT is required... no
                             for TLS 1.0 (RFC2246) support... yes
                             for TLS 1.1 (RFC4346) support... yes
                             for TLS 1.2 (RFC5246) support... yes
                                  fallback from TLS 1.6 to... TLS1.2
                        for RFC7507 inappropriate fallback... yes
                                     for HTTPS server name... Local
                               for certificate chain order... sorted
                  for safe renegotiation (RFC5746) support... yes
                     for Safe renegotiation support (SCSV)... no
                    for encrypt\-then\-MAC (RFC7366) support... no
                   for ext master secret (RFC7627) support... no
                           for heartbeat (RFC6520) support... no
                       for version rollback bug in RSA PMS... dunno
                  for version rollback bug in Client Hello... no
            whether the server ignores the RSA PMS version... yes
whether small records (512 bytes) are tolerated on handshake... yes
    whether cipher suites not in SSL 3.0 spec are accepted... yes
whether a bogus TLS record version in the client hello is accepted... yes
         whether the server understands TLS closure alerts... partially
            whether the server supports session resumption... yes
                      for anonymous authentication support... no
                      for ephemeral Diffie\-Hellman support... no
                   for ephemeral EC Diffie\-Hellman support... yes
                    ephemeral EC Diffie\-Hellman group info... SECP256R1
                  for AES\-128\-GCM cipher (RFC5288) support... yes
                  for AES\-128\-CCM cipher (RFC6655) support... no
                for AES\-128\-CCM\-8 cipher (RFC6655) support... no
                  for AES\-128\-CBC cipher (RFC3268) support... yes
             for CAMELLIA\-128\-GCM cipher (RFC6367) support... no
             for CAMELLIA\-128\-CBC cipher (RFC5932) support... no
                     for 3DES\-CBC cipher (RFC2246) support... yes
                  for ARCFOUR 128 cipher (RFC2246) support... yes
                                       for MD5 MAC support... yes
                                      for SHA1 MAC support... yes
                                    for SHA256 MAC support... yes
                              for ZLIB compression support... no
                     for max record size (RFC6066) support... no
                for OCSP status response (RFC6066) support... no
              for OpenPGP authentication (RFC6091) support... no
.in -4
.fi
.sp
You could also use the client to debug services with starttls capability.
.br
.in +4
.nf
$ gnutls\-cli\-debug \-\-starttls\-proto smtp \-\-port 25 localhost
.in -4
.fi
.SH "EXIT STATUS"
One of the following exit values will be returned:
.TP
.NOP 0 " (EXIT_SUCCESS)"
Successful program execution.
.TP
.NOP 1 " (EXIT_FAILURE)"
The operation failed or the command syntax was not valid.
.PP
.SH "SEE ALSO"
gnutls\-cli(1), gnutls\-serv(1)
.SH "AUTHORS"

.SH "COPYRIGHT"
Copyright (C) 2020-2023 Free Software Foundation, and others all rights reserved.
This program is released under the terms of
the GNU General Public License, version 3 or later
.
.SH "BUGS"
Please send bug reports to: <EMAIL>
