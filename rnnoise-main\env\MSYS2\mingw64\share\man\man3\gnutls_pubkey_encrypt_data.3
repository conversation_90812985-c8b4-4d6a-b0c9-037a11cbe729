.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_encrypt_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_encrypt_data \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_encrypt_data(gnutls_pubkey_t " key ", unsigned int " flags ", const gnutls_datum_t * " plaintext ", gnutls_datum_t * " ciphertext ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
Holds the public key
.IP "unsigned int flags" 12
should be 0 for now
.IP "const gnutls_datum_t * plaintext" 12
The data to be encrypted
.IP "gnutls_datum_t * ciphertext" 12
contains the encrypted data
.SH "DESCRIPTION"
This function will encrypt the given data, using the public
key. On success the  \fIciphertext\fP will be allocated using \fBgnutls_malloc()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
