_fsck.cramfs_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-b'|'--blocksize')
			COMPREPLY=( $(compgen -W "size" -- $cur) )
			return 0
			;;
		'--extract')
			local IFS=$'\n'
			compopt -o filenames
			COMPREPLY=( $(compgen -o dirnames -- ${cur:-"/"}) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			COMPREPLY=( $(compgen -W "--verbose --blocksize --extract --help --version" -- $cur) )
			return 0
			;;
	esac
	compopt -o bashdefault -o default
	COMPREPLY=( $(compgen -W "$(lsblk -pnro name)" -- $cur) )
	return 0
}
complete -F _fsck.cramfs_module fsck.cramfs
