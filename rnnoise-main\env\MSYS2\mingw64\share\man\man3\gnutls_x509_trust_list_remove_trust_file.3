.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_remove_trust_file" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_remove_trust_file \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_remove_trust_file(gnutls_x509_trust_list_t " list ", const char * " ca_file ", gnutls_x509_crt_fmt_t " type ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "const char * ca_file" 12
A file containing a list of CAs
.IP "gnutls_x509_crt_fmt_t type" 12
The format of the certificates
.SH "DESCRIPTION"
This function will remove the given certificate authorities
from the trusted list, and add them into a block list when needed. 
PKCS 11 URLs are also accepted, instead
of files, by this function.

See also \fBgnutls_x509_trust_list_remove_cas()\fP.
.SH "RETURNS"
The number of added elements is returned.
.SH "SINCE"
3.1.10
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
