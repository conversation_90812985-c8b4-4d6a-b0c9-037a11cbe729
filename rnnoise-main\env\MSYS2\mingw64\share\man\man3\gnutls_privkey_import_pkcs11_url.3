.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_import_pkcs11_url" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_import_pkcs11_url \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_import_pkcs11_url(gnutls_privkey_t " key ", const char * " url ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
A key of type \fBgnutls_pubkey_t\fP
.IP "const char * url" 12
A PKCS 11 url
.SH "DESCRIPTION"
This function will import a PKCS 11 private key to a \fBgnutls_privkey_t\fP
type.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
