<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>provider-kem</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Context-Management-Functions">Context Management Functions</a></li>
      <li><a href="#Asymmetric-Key-Encapsulation-Functions">Asymmetric Key Encapsulation Functions</a></li>
      <li><a href="#Decapsulation-Functions">Decapsulation Functions</a></li>
      <li><a href="#Asymmetric-Key-Encapsulation-Parameters">Asymmetric Key Encapsulation Parameters</a></li>
      <li><a href="#Asymmetric-Key-Encapsulation-Parameter-Functions">Asymmetric Key Encapsulation Parameter Functions</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>provider-kem - The kem library &lt;-&gt; provider functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/core_dispatch.h&gt;
#include &lt;openssl/core_names.h&gt;

/*
 * None of these are actual functions, but are displayed like this for
 * the function signatures for functions that are offered as function
 * pointers in OSSL_DISPATCH arrays.
 */

/* Context management */
void *OSSL_FUNC_kem_newctx(void *provctx);
void OSSL_FUNC_kem_freectx(void *ctx);
void *OSSL_FUNC_kem_dupctx(void *ctx);

/* Encapsulation */
int OSSL_FUNC_kem_encapsulate_init(void *ctx, void *provkey,
                                   const OSSL_PARAM params[]);
int OSSL_FUNC_kem_auth_encapsulate_init(void *ctx, void *provkey,
                                        void *provauthkey,
                                        const OSSL_PARAM params[]);
int OSSL_FUNC_kem_encapsulate(void *ctx, unsigned char *out, size_t *outlen,
                              unsigned char *secret, size_t *secretlen);

/* Decapsulation */
int OSSL_FUNC_kem_decapsulate_init(void *ctx, void *provkey);
int OSSL_FUNC_kem_auth_decapsulate_init(void *ctx, void *provkey,
                                        void *provauthkey,
                                        const OSSL_PARAM params[]);
int OSSL_FUNC_kem_decapsulate(void *ctx, unsigned char *out, size_t *outlen,
                              const unsigned char *in, size_t inlen);

/* KEM parameters */
int OSSL_FUNC_kem_get_ctx_params(void *ctx, OSSL_PARAM params[]);
const OSSL_PARAM *OSSL_FUNC_kem_gettable_ctx_params(void *ctx, void *provctx);
int OSSL_FUNC_kem_set_ctx_params(void *ctx, const OSSL_PARAM params[]);
const OSSL_PARAM *OSSL_FUNC_kem_settable_ctx_params(void *ctx, void *provctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This documentation is primarily aimed at provider authors. See <a href="../man7/provider.html">provider(7)</a> for further information.</p>

<p>The asymmetric kem (OSSL_OP_KEM) operation enables providers to implement asymmetric kem algorithms and make them available to applications via the API functions <a href="../man3/EVP_PKEY_encapsulate.html">EVP_PKEY_encapsulate(3)</a>, <a href="../man3/EVP_PKEY_decapsulate.html">EVP_PKEY_decapsulate(3)</a> and other related functions.</p>

<p>All &quot;functions&quot; mentioned here are passed as function pointers between <i>libcrypto</i> and the provider in <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays via <a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a> arrays that are returned by the provider&#39;s provider_query_operation() function (see <a href="../man7/provider-base.html">&quot;Provider Functions&quot; in provider-base(7)</a>).</p>

<p>All these &quot;functions&quot; have a corresponding function type definition named <b>OSSL_FUNC_{name}_fn</b>, and a helper function to retrieve the function pointer from an <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> element named <b>OSSL_FUNC_{name}</b>. For example, the &quot;function&quot; OSSL_FUNC_kem_newctx() has these:</p>

<pre><code>typedef void *(OSSL_FUNC_kem_newctx_fn)(void *provctx);
static ossl_inline OSSL_FUNC_kem_newctx_fn
    OSSL_FUNC_kem_newctx(const OSSL_DISPATCH *opf);</code></pre>

<p><a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays are indexed by numbers that are provided as macros in <a href="../man7/openssl-core_dispatch.h.html">openssl-core_dispatch.h(7)</a>, as follows:</p>

<pre><code>OSSL_FUNC_kem_newctx                OSSL_FUNC_KEM_NEWCTX
OSSL_FUNC_kem_freectx               OSSL_FUNC_KEM_FREECTX
OSSL_FUNC_kem_dupctx                OSSL_FUNC_KEM_DUPCTX

OSSL_FUNC_kem_encapsulate_init      OSSL_FUNC_KEM_ENCAPSULATE_INIT
OSSL_FUNC_kem_auth_encapsulate_init OSSL_FUNC_KEM_AUTH_ENCAPSULATE_INIT
OSSL_FUNC_kem_encapsulate           OSSL_FUNC_KEM_ENCAPSULATE

OSSL_FUNC_kem_decapsulate_init      OSSL_FUNC_KEM_DECAPSULATE_INIT
OSSL_FUNC_kem_auth_decapsulate_init OSSL_FUNC_KEM_AUTH_DECAPSULATE_INIT
OSSL_FUNC_kem_decapsulate           OSSL_FUNC_KEM_DECAPSULATE

OSSL_FUNC_kem_get_ctx_params        OSSL_FUNC_KEM_GET_CTX_PARAMS
OSSL_FUNC_kem_gettable_ctx_params   OSSL_FUNC_KEM_GETTABLE_CTX_PARAMS
OSSL_FUNC_kem_set_ctx_params        OSSL_FUNC_KEM_SET_CTX_PARAMS
OSSL_FUNC_kem_settable_ctx_params   OSSL_FUNC_KEM_SETTABLE_CTX_PARAMS</code></pre>

<p>An asymmetric kem algorithm implementation may not implement all of these functions. In order to be a consistent set of functions a provider must implement OSSL_FUNC_kem_newctx and OSSL_FUNC_kem_freectx. It must also implement both of OSSL_FUNC_kem_encapsulate_init and OSSL_FUNC_kem_encapsulate, or both of OSSL_FUNC_kem_decapsulate_init and OSSL_FUNC_kem_decapsulate. OSSL_FUNC_kem_auth_encapsulate_init is optional but if it is present then so must OSSL_FUNC_kem_auth_decapsulate_init. OSSL_FUNC_kem_get_ctx_params is optional but if it is present then so must OSSL_FUNC_kem_gettable_ctx_params. Similarly, OSSL_FUNC_kem_set_ctx_params is optional but if it is present then OSSL_FUNC_kem_settable_ctx_params must also be present.</p>

<p>An asymmetric kem algorithm must also implement some mechanism for generating, loading or importing keys via the key management (OSSL_OP_KEYMGMT) operation. See <a href="../man7/provider-keymgmt.html">provider-keymgmt(7)</a> for further details.</p>

<h2 id="Context-Management-Functions">Context Management Functions</h2>

<p>OSSL_FUNC_kem_newctx() should create and return a pointer to a provider side structure for holding context information during an asymmetric kem operation. A pointer to this context will be passed back in a number of the other asymmetric kem operation function calls. The parameter <i>provctx</i> is the provider context generated during provider initialisation (see <a href="../man7/provider.html">provider(7)</a>).</p>

<p>OSSL_FUNC_kem_freectx() is passed a pointer to the provider side asymmetric kem context in the <i>ctx</i> parameter. This function should free any resources associated with that context.</p>

<p>OSSL_FUNC_kem_dupctx() should duplicate the provider side asymmetric kem context in the <i>ctx</i> parameter and return the duplicate copy.</p>

<h2 id="Asymmetric-Key-Encapsulation-Functions">Asymmetric Key Encapsulation Functions</h2>

<p>OSSL_FUNC_kem_encapsulate_init() initialises a context for an asymmetric encapsulation given a provider side asymmetric kem context in the <i>ctx</i> parameter, a pointer to a provider key object in the <i>provkey</i> parameter and the <i>name</i> of the algorithm. The <i>params</i>, if not NULL, should be set on the context in a manner similar to using OSSL_FUNC_kem_set_ctx_params(). The key object should have been previously generated, loaded or imported into the provider using the key management (OSSL_OP_KEYMGMT) operation (see provider-keymgmt(7)&gt;.</p>

<p>OSSL_FUNC_kem_auth_encapsulate_init() is similar to OSSL_FUNC_kem_encapsulate_init(), but also passes an additional authentication key <i>provauthkey</i> which cannot be NULL.</p>

<p>OSSL_FUNC_kem_encapsulate() performs the actual encapsulation itself. A previously initialised asymmetric kem context is passed in the <i>ctx</i> parameter. Unless <i>out</i> is NULL, the data to be encapsulated is internally generated, and returned into the buffer pointed to by the <i>secret</i> parameter and the encapsulated data should also be written to the location pointed to by the <i>out</i> parameter. The length of the encapsulated data should be written to <i>*outlen</i> and the length of the generated secret should be written to <i>*secretlen</i>.</p>

<p>If <i>out</i> is NULL then the maximum length of the encapsulated data should be written to <i>*outlen</i>, and the maximum length of the generated secret should be written to <i>*secretlen</i>.</p>

<h2 id="Decapsulation-Functions">Decapsulation Functions</h2>

<p>OSSL_FUNC_kem_decapsulate_init() initialises a context for an asymmetric decapsulation given a provider side asymmetric kem context in the <i>ctx</i> parameter, a pointer to a provider key object in the <i>provkey</i> parameter, and a <i>name</i> of the algorithm. The key object should have been previously generated, loaded or imported into the provider using the key management (OSSL_OP_KEYMGMT) operation (see provider-keymgmt(7)&gt;.</p>

<p>OSSL_FUNC_kem_auth_decapsulate_init() is similar to OSSL_FUNC_kem_decapsulate_init(), but also passes an additional authentication key <i>provauthkey</i> which cannot be NULL.</p>

<p>OSSL_FUNC_kem_decapsulate() performs the actual decapsulation itself. A previously initialised asymmetric kem context is passed in the <i>ctx</i> parameter. The data to be decapsulated is pointed to by the <i>in</i> parameter which is <i>inlen</i> bytes long. Unless <i>out</i> is NULL, the decapsulated data should be written to the location pointed to by the <i>out</i> parameter. The length of the decapsulated data should be written to <i>*outlen</i>. If <i>out</i> is NULL then the maximum length of the decapsulated data should be written to <i>*outlen</i>.</p>

<h2 id="Asymmetric-Key-Encapsulation-Parameters">Asymmetric Key Encapsulation Parameters</h2>

<p>See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for further details on the parameters structure used by the OSSL_FUNC_kem_get_ctx_params() and OSSL_FUNC_kem_set_ctx_params() functions.</p>

<p>The OpenSSL FIPS provider also supports the following parameters:</p>

<dl>

<dt id="fips-indicator-OSSL_KEM_PARAM_FIPS_APPROVED_INDICATOR-integer">&quot;fips-indicator&quot; (<b>OSSL_KEM_PARAM_FIPS_APPROVED_INDICATOR</b>) &lt;integer&gt;</dt>
<dd>

<p>A getter that returns 1 if the operation is FIPS approved, or 0 otherwise. This may be used after calling either OSSL_FUNC_kem_encapsulate() or OSSL_FUNC_kem_decapsulate(). It may return 0 if the &quot;key-check&quot; is set to 0.</p>

</dd>
<dt id="key-check-OSSL_KEM_PARAM_FIPS_KEY_CHECK-integer">&quot;key-check&quot; (<b>OSSL_KEM_PARAM_FIPS_KEY_CHECK</b>) &lt;integer&gt;</dt>
<dd>

<p>If required this parameter should be set using OSSL_FUNC_kem_encapsulate_init() or OSSL_FUNC_kem_decapsulate_init(). The default value of 1 causes an error during the init if the key is not FIPS approved (e.g. The key has a security strength of less than 112 bits). Setting this to 0 will ignore the error and set the approved &quot;fips-indicator&quot; to 0. This option breaks FIPS compliance if it causes the approved &quot;fips-indicator&quot; to return 0.</p>

</dd>
</dl>

<h2 id="Asymmetric-Key-Encapsulation-Parameter-Functions">Asymmetric Key Encapsulation Parameter Functions</h2>

<p>OSSL_FUNC_kem_get_ctx_params() gets asymmetric KEM parameters associated with the given provider side asymmetric kem context <i>ctx</i> and stores them in <i>params</i>. Passing NULL for <i>params</i> should return true.</p>

<p>OSSL_FUNC_kem_set_ctx_params() sets the asymmetric KEM parameters associated with the given provider side asymmetric kem context <i>ctx</i> to <i>params</i>. Any parameter settings are additional to any that were previously set. Passing NULL for <i>params</i> should return true.</p>

<p>No parameters are currently recognised by built-in asymmetric kem algorithms.</p>

<p>OSSL_FUNC_kem_gettable_ctx_params() and OSSL_FUNC_kem_settable_ctx_params() get a constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array that describes the gettable and settable parameters, i.e. parameters that can be used with OSSL_FUNC_kem_get_ctx_params() and OSSL_FUNC_kem_set_ctx_params() respectively.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_FUNC_kem_newctx() and OSSL_FUNC_kem_dupctx() should return the newly created provider side asymmetric kem context, or NULL on failure.</p>

<p>All other functions should return 1 for success or 0 on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider.html">provider(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The provider KEM interface was introduced in OpenSSL 3.0.</p>

<p>OSSL_FUNC_kem_auth_encapsulate_init() and OSSL_FUNC_kem_auth_decapsulate_init() were added in OpenSSL 3.2.</p>

<p>The Asymmetric Key Encapsulation Parameters &quot;fips-indicator&quot; and &quot;key-check&quot; were added in OpenSSL 3.4.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


