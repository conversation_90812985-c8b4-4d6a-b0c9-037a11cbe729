<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Trust Policy Module: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="index.html" title="p11-kit">
<link rel="prev" href="remoting.html" title="Remoting / Forwarding">
<link rel="next" href="trust-nss.html" title="Using the Trust Policy Module with NSS">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><img src="up-insensitive.png" width="16" height="16" border="0"></td>
<td><a accesskey="p" href="remoting.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="trust-nss.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="chapter">
<div class="titlepage"><div><div><h1 class="title">
<a name="trust-module"></a>Trust Policy Module</h1></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="trust-module.html#trust-files">Paths loaded by the Module</a></span></dt>
<dt><span class="section"><a href="trust-nss.html">Using the Trust Policy Module with NSS</a></span></dt>
<dt><span class="section"><a href="trust-glib-networking.html">Using the Trust Policy Module with glib-networking</a></span></dt>
<dt><span class="section"><a href="trust-disable.html">Disabling the Trust Policy Module</a></span></dt>
</dl></div>
<p>The trust module provides system certificate anchors, blocklists
	and other trust policy to crypto libraries applications. This
	information is exposed as PKCS#11 objects.</p>
<p>You can use the <a class="link" href="trust.html" title="trust">trust</a> command line
	tool to examine and modify the trust policy store.</p>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="trust-files"></a>Paths loaded by the Module</h2></div></div></div>
<p>The trust module loads certificates and trust policy information
	from preconfigured paths and allows them to be looked up via PKCS#11.
	The input paths can be determined with using the following command:</p>
<pre class="programlisting">
$ pkg-config --variable p11_trust_paths p11-kit-1
/usr/share/p11-kit/trust:/etc/pki/trust
</pre>
<p>Files in the following formats are supported for loading by the
	trust policy module:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">X.509 certificates</span></p></td>
<td><p>X.509 certificates in raw DER format. Does not
			automatically contain trust policy information.</p></td>
</tr>
<tr>
<td><p><span class="term">PEM certificates</span></p></td>
<td><p>X.509 certificates in PEM format. These have a
			<code class="literal">BEGIN CERTIFICATE</code> header. This file does not
			automatically contain trust policy information.</p></td>
</tr>
<tr>
<td><p><span class="term">OpenSSL trust certificates</span></p></td>
<td><p>OpenSSL specific certificates in PEM format
			that contain trust information. These have a
			<code class="literal">BEGIN TRUSTED CERTIFICATE</code> PEM header. Both
			trust anchor and blocklist information can be loaded
			from these files.</p></td>
</tr>
</tbody>
</table></div>
<p>If the input path is a file, then it is loaded. Certificate(s) in the
	file are automatically treated as anchors, unless they contain alternate
	trust policy information.</p>
<p>If the input path is a directory, files inside that directory are
	parsed and loaded. If the file contains trust policy information (such as the
	OpenSSL trust certificates) then it will be respected. Files without trust policy
	information are not automatically marked as an anchor or distrusted.</p>
<p>In addition two optional subdirectories of the input path are loaded. Files
	placed in the <code class="literal">anchors/</code> subdirectory become trust anchors
	when they do not contain trust policy information. Files placed in the
	<code class="literal">blocklist/</code> subdirectory are distrusted whether they
	contain trust information or not.</p>
<p>The first input path becomes the first PKCS#11 token of the trust
	module, and has the highest priority when callers search for trust
	policy information.</p>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>