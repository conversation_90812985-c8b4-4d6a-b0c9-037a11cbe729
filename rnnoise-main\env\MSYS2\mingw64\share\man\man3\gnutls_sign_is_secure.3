.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_sign_is_secure" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_sign_is_secure \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_sign_is_secure(gnutls_sign_algorithm_t " algorithm ");"
.SH ARGUMENTS
.IP "gnutls_sign_algorithm_t algorithm" 12
is a sign algorithm
.SH "RETURNS"
Non\-zero if the provided signature algorithm is considered to be secure.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
