# Time-stamp: "Sat Jul 14 00:27:35 2001 by Automatic Bizooty (__blocks2pm.plx)"
$Text::Unidecode::Char[0x93] = [
'Lun ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ',
'<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', 'Kun ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON>g ', '<PERSON><PERSON> ', '<PERSON> ',
'Ding ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ',
'<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', qq{[?] },
'<PERSON> ', '<PERSON><PERSON> ', qq{[?] }, '<PERSON><PERSON> ', '<PERSON> ', qq{[?] }, '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>n ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON>g ', '<PERSON> ', '<PERSON>ou ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON>i ', '<PERSON>ha ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON>a ', '<PERSON> ', '<PERSON>g ', '<PERSON>ng ',
'<PERSON> ', '<PERSON> ', '<PERSON> ', 'Zhao ', 'Cha ', 'Qie ', 'She ', 'Hong ', 'Kui ', 'Tian ', 'Mou ', 'Qiao ', 'Qiao ', 'Hou ', 'Tou ', 'Cong ',
'Huan ', 'Ye ', 'Min ', 'Jian ', 'Duan ', 'Jian ', 'Song ', 'Kui ', 'Hu ', 'Xuan ', 'Duo ', 'Jie ', 'Zhen ', 'Bian ', 'Zhong ', 'Zi ',
'Xiu ', 'Ye ', 'Mei ', 'Pai ', 'Ai ', 'Jie ', qq{[?] }, 'Mei ', 'Chuo ', 'Ta ', 'Bang ', 'Xia ', 'Lian ', 'Suo ', 'Xi ', 'Liu ',
'Zu ', 'Ye ', 'Nou ', 'Weng ', 'Rong ', 'Tang ', 'Suo ', 'Qiang ', 'Ge ', 'Shuo ', 'Chui ', 'Bo ', 'Pan ', 'Sa ', 'Bi ', 'Sang ',
'Gang ', 'Zi ', 'Wu ', 'Ying ', 'Huang ', 'Tiao ', 'Liu ', 'Kai ', 'Sun ', 'Sha ', 'Sou ', 'Wan ', 'Hao ', 'Zhen ', 'Zhen ', 'Luo ',
'Yi ', 'Yuan ', 'Tang ', 'Nie ', 'Xi ', 'Jia ', 'Ge ', 'Ma ', 'Juan ', 'Kasugai ', 'Habaki ', 'Suo ', qq{[?] }, qq{[?] }, qq{[?] }, 'Na ',
'Lu ', 'Suo ', 'Ou ', 'Zu ', 'Tuan ', 'Xiu ', 'Guan ', 'Xuan ', 'Lian ', 'Shou ', 'Ao ', 'Man ', 'Mo ', 'Luo ', 'Bi ', 'Wei ',
'Liu ', 'Di ', 'Qiao ', 'Cong ', 'Yi ', 'Lu ', 'Ao ', 'Keng ', 'Qiang ', 'Cui ', 'Qi ', 'Chang ', 'Tang ', 'Man ', 'Yong ', 'Chan ',
'Feng ', 'Jing ', 'Biao ', 'Shu ', 'Lou ', 'Xiu ', 'Cong ', 'Long ', 'Zan ', 'Jian ', 'Cao ', 'Li ', 'Xia ', 'Xi ', 'Kang ', qq{[?] },
'Beng ', qq{[?] }, qq{[?] }, 'Zheng ', 'Lu ', 'Hua ', 'Ji ', 'Pu ', 'Hui ', 'Qiang ', 'Po ', 'Lin ', 'Suo ', 'Xiu ', 'San ', 'Cheng ',
];
1;
