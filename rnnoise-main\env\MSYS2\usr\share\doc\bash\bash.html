<HTML><HEAD>
<TITLE>BASH(1) Manual Page</TITLE>
</HEAD>
<BODY><TABLE WIDTH=100%>
<TR>
<TH ALIGN=LEFT width=33%>BASH(1)<TH ALIGN=CENTER width=33%>2022 September 19<TH ALIGN=RIGHT width=33%>BASH(1)
</TR>
</TABLE>
<BR><A HREF="#index">Index</A>
<HR>































<A NAME="lbAB">&nbsp;</A>
<H3>NAME</H3>

bash - GNU Bourne-Again SHell
<A NAME="lbAC">&nbsp;</A>
<H3>SYNOPSIS</H3>

<B>bash</B>

[options]
[command_string | file]
<A NAME="lbAD">&nbsp;</A>
<H3>COPYRIGHT</H3>


Bash is Copyright &#169; 1989-2022 by the Free Software Foundation, Inc.
<A NAME="lbAE">&nbsp;</A>
<H3>DESCRIPTION</H3>

<B>Bash</B>

is an <B>sh</B>-compatible command language interpreter that
executes commands read from the standard input or from a file.
<B>Bash</B>

also incorporates useful features from the <I>Korn</I> and <I>C</I>
shells (<B>ksh</B> and <B>csh</B>).
<P>

<B>Bash</B>

is intended to be a conformant implementation of the
Shell and Utilities portion of the IEEE POSIX specification
(IEEE Standard 1003.1).
<B>Bash</B>

can be configured to be POSIX-conformant by default.
<A NAME="lbAF">&nbsp;</A>
<H3>OPTIONS</H3>

All of the single-character shell options documented in the
description of the <B>set</B> builtin command, including <B>-o</B>,
can be used as options when the shell is invoked.
In addition, <B>bash</B>
interprets the following options when it is invoked:
<P>


<DL COMPACT>
<DT><B>-c</B>

<DD>
If the
<B>-c</B>

option is present, then commands are read from the first non-option argument
<I>command_string</I>.

If there are arguments after the
<I>command_string</I>,

the first argument is assigned to
<B>$0</B>

and any remaining arguments are assigned to the positional parameters.
The assignment to
<B>$0</B>

sets the name of the shell, which is used in warning and error messages.
<DT><B>-i</B>

<DD>
If the
<B>-i</B>

option is present, the shell is
<I>interactive</I>.

<DT><B>-l</B>

<DD>
Make
<B>bash</B>

act as if it had been invoked as a login shell (see
<FONT SIZE=-1><B>INVOCATION</B>

</FONT>
below).
<DT><B>-r</B>

<DD>
If the
<B>-r</B>

option is present, the shell becomes
<I>restricted</I>

(see
<FONT SIZE=-1><B>RESTRICTED SHELL</B>

</FONT>
below).
<DT><B>-s</B>

<DD>
If the
<B>-s</B>

option is present, or if no arguments remain after option
processing, then commands are read from the standard input.
This option allows the positional parameters to be set
when invoking an interactive shell or when reading input
through a pipe.
<DT><B>-D</B>

<DD>
A list of all double-quoted strings preceded by <B>$</B>
is printed on the standard output.
These are the strings that
are subject to language translation when the current locale
is not <B>C</B> or <B>POSIX</B>.
This implies the <B>-n</B> option; no commands will be executed.
<DT><B>[-+]O [</B><I>shopt_option</I>]

<DD>
<I>shopt_option</I> is one of the shell options accepted by the
<B>shopt</B> builtin (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
If <I>shopt_option</I> is present, <B>-O</B> sets the value of that option;
<B>+O</B> unsets it.
If <I>shopt_option</I> is not supplied, the names and values of the shell
options accepted by <B>shopt</B> are printed on the standard output.
If the invocation option is <B>+O</B>, the output is displayed in a format
that may be reused as input.
<DT><B>--</B>

<DD>
A
<B>--</B>

signals the end of options and disables further option processing.
Any arguments after the
<B>--</B>

are treated as filenames and arguments.  An argument of
<B>-</B>

is equivalent to <B>--</B>.

</DL>
<P>

<B>Bash</B>

also interprets a number of multi-character options.
These options must appear on the command line before the
single-character options to be recognized.
<P>


<DL COMPACT>
<DT><B>--debugger</B>

<DD>
Arrange for the debugger profile to be executed before the shell
starts.
Turns on extended debugging mode (see the description of the
<B>extdebug</B>

option to the
<B>shopt</B>

builtin below).
<DT><B>--dump-po-strings</B>

<DD>
Equivalent to <B>-D</B>, but the output is in the GNU <I>gettext</I>
<B>po</B> (portable object) file format.
<DT><B>--dump-strings</B>

<DD>
Equivalent to <B>-D</B>.
<DT><B>--help</B>

<DD>
Display a usage message on standard output and exit successfully.
<DT><B>--init-file</B> <I>file</I><DD>

<DT><B>--rcfile</B> <I>file</I><DD>

Execute commands from
<I>file</I>

instead of the standard personal initialization file
<A HREF="file:~/.bashrc"><I>~/.bashrc</I></A>

if the shell is interactive (see
<FONT SIZE=-1><B>INVOCATION</B>

</FONT>
below).
<DT><B>--login</B>

<DD>
Equivalent to <B>-l</B>.
<DT><B>--noediting</B>

<DD>
Do not use the GNU
<B>readline</B>

library to read command lines when the shell is interactive.
<DT><B>--noprofile</B>

<DD>
Do not read either the system-wide startup file

<A HREF="file:/etc/profile"><I>/etc/profile</I></A>

or any of the personal initialization files
<A HREF="file:~/.bash_profile"><I>~/.bash_profile</I></A>,

<A HREF="file:~/.bash_login"><I>~/.bash_login</I></A>,

or
<A HREF="file:~/.profile"><I>~/.profile</I></A>.

By default,
<B>bash</B>

reads these files when it is invoked as a login shell (see
<FONT SIZE=-1><B>INVOCATION</B>

</FONT>
below).
<DT><B>--norc</B>

<DD>
Do not read and execute the personal initialization file
<A HREF="file:~/.bashrc"><I>~/.bashrc</I></A>

if the shell is interactive.
This option is on by default if the shell is invoked as
<B>sh</B>.

<DT><B>--posix</B>

<DD>
Change the behavior of <B>bash</B> where the default operation differs
from the POSIX standard to match the standard (<I>posix mode</I>).
See
<FONT SIZE=-1><B>SEE ALSO</B>

</FONT>
below for a reference to a document that details how posix mode affects
bash's behavior.
<DT><B>--restricted</B>

<DD>
The shell becomes restricted (see
<FONT SIZE=-1><B>RESTRICTED SHELL</B>

</FONT>
below).
<DT><B>--verbose</B>

<DD>
Equivalent to <B>-v</B>.
<DT><B>--version</B>

<DD>
Show version information for this instance of
<B>bash</B>

on the standard output and exit successfully.

</DL>
<A NAME="lbAG">&nbsp;</A>
<H3>ARGUMENTS</H3>

If arguments remain after option processing, and neither the
<B>-c</B>

nor the
<B>-s</B>

option has been supplied, the first argument is assumed to
be the name of a file containing shell commands.
If
<B>bash</B>

is invoked in this fashion,
<B>$0</B>

is set to the name of the file, and the positional parameters
are set to the remaining arguments.
<B>Bash</B>

reads and executes commands from this file, then exits.
<B>Bash</B>'s exit status is the exit status of the last command
executed in the script.
If no commands are executed, the exit status is 0.
An attempt is first made to open the file in the current directory, and,
if no file is found, then the shell searches the directories in
<FONT SIZE=-1><B>PATH</B>

</FONT>
for the script.
<A NAME="lbAH">&nbsp;</A>
<H3>INVOCATION</H3>

A <I>login shell</I> is one whose first character of argument zero is a
<B>-</B>,

or one started with the
<B>--login</B>

option.
<P>

An <I>interactive</I> shell is one started without non-option arguments
(unless <B>-s</B> is specified)
and without the
<B>-c</B>

option,
whose standard input and error are
both connected to terminals (as determined by
<I>isatty</I>(3)),

or one started with the
<B>-i</B>

option.
<FONT SIZE=-1><B>PS1</B>

</FONT>
is set and
<B>$-</B>

includes
<B>i</B>

if
<B>bash</B>

is interactive,
allowing a shell script or a startup file to test this state.
<P>

The following paragraphs describe how
<B>bash</B>

executes its startup files.
If any of the files exist but cannot be read,
<B>bash</B>

reports an error.
Tildes are expanded in filenames as described below under
<B>Tilde Expansion</B>

in the
<FONT SIZE=-1><B>EXPANSION</B>

</FONT>
section.
<P>

When
<B>bash</B>

is invoked as an interactive login shell, or as a non-interactive shell
with the <B>--login</B> option, it first reads and
executes commands from the file <A HREF="file:/etc/profile"><I>/etc/profile</I></A>, if that
file exists.
After reading that file, it looks for <A HREF="file:~/.bash_profile"><I>~/.bash_profile</I></A>,
<A HREF="file:~/.bash_login"><I>~/.bash_login</I></A>, and <A HREF="file:~/.profile"><I>~/.profile</I></A>, in that order, and reads
and executes commands from the first one that exists and is readable.
The
<B>--noprofile</B>

option may be used when the shell is started to inhibit this behavior.
<P>

When an interactive login shell exits,
or a non-interactive login shell executes the <B>exit</B> builtin command,
<B>bash</B>

reads and executes commands from the file <A HREF="file:~/.bash_logout"><I>~/.bash_logout</I></A>, if it
exists.
<P>

When an interactive shell that is not a login shell is started,
<B>bash</B>

reads and executes commands from <A HREF="file:~/.bashrc"><I>~/.bashrc</I></A>, if that file exists.
This may be inhibited by using the
<B>--norc</B>

option.
The <B>--rcfile</B> <I>file</I> option will force
<B>bash</B>

to read and execute commands from <I>file</I> instead of <A HREF="file:~/.bashrc"><I>~/.bashrc</I></A>.
<P>

When
<B>bash</B>

is started non-interactively, to run a shell script, for example, it
looks for the variable
<FONT SIZE=-1><B>BASH_ENV</B>

</FONT>
in the environment, expands its value if it appears there, and uses the
expanded value as the name of a file to read and execute.
<B>Bash</B>

behaves as if the following command were executed:
<P>
<DL COMPACT><DT><DD>
<TT>if [ -n &quot;$BASH_ENV&quot; ]; then . &quot;$BASH_ENV&quot;; fi</TT>

</DL>

<P>
but the value of the
<FONT SIZE=-1><B>PATH</B>

</FONT>
variable is not used to search for the filename.
<P>

If
<B>bash</B>

is invoked with the name
<B>sh</B>,

it tries to mimic the startup behavior of historical versions of
<B>sh</B>

as closely as possible,
while conforming to the POSIX standard as well.
When invoked as an interactive login shell, or a non-interactive
shell with the <B>--login</B> option, it first attempts to
read and execute commands from
<A HREF="file:/etc/profile"><I>/etc/profile</I></A>

and
<A HREF="file:~/.profile"><I>~/.profile</I></A>,

in that order.
The
<B>--noprofile</B>

option may be used to inhibit this behavior.
When invoked as an interactive shell with the name
<B>sh</B>,

<B>bash</B>

looks for the variable
<FONT SIZE=-1><B>ENV</B>,

</FONT>
expands its value if it is defined, and uses the
expanded value as the name of a file to read and execute.
Since a shell invoked as
<B>sh</B>

does not attempt to read and execute commands from any other startup
files, the
<B>--rcfile</B>

option has no effect.
A non-interactive shell invoked with the name
<B>sh</B>

does not attempt to read any other startup files.
When invoked as
<B>sh</B>,

<B>bash</B>

enters
<I>posix</I>

mode after the startup files are read.
<P>

When
<B>bash</B>

is started in
<I>posix</I>

mode, as with the
<B>--posix</B>

command line option, it follows the POSIX standard for startup files.
In this mode, interactive shells expand the
<FONT SIZE=-1><B>ENV</B>

</FONT>
variable and commands are read and executed from the file
whose name is the expanded value.
No other startup files are read.
<P>

<B>Bash</B>

attempts to determine when it is being run with its standard input
connected to a network connection, as when executed by
the historical remote shell daemon, usually <I>rshd</I>,
or the secure shell daemon <I>sshd</I>.
If
<B>bash</B>

determines it is being run non-interactively in this fashion,
it reads and executes commands from <A HREF="file:~/.bashrc"><I>~/.bashrc</I></A>,
if that file exists and is readable.
It will not do this if invoked as <B>sh</B>.
The
<B>--norc</B>

option may be used to inhibit this behavior, and the
<B>--rcfile</B>

option may be used to force another file to be read, but neither
<I>rshd</I> nor <I>sshd</I> generally invoke the shell with those options
or allow them to be specified.
<P>

If the shell is started with the effective user (group) id not equal to the
real user (group) id, and the <B>-p</B> option is not supplied, no startup
files are read, shell functions are not inherited from the environment, the
<FONT SIZE=-1><B>SHELLOPTS</B>,

</FONT>
<FONT SIZE=-1><B>BASHOPTS</B>,

</FONT>
<FONT SIZE=-1><B>CDPATH</B>,

</FONT>
and
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
variables, if they appear in the environment, are ignored,
and the effective user id is set to the real user id.
If the <B>-p</B> option is supplied at invocation, the startup behavior is
the same, but the effective user id is not reset.
<A NAME="lbAI">&nbsp;</A>
<H3>DEFINITIONS</H3>

The following definitions are used throughout the rest of this
document.

<DL COMPACT>
<DT><B>blank</B>

<DD>
A space or tab.
<DT><B>word</B>

<DD>
A sequence of characters considered as a single unit by the shell.
Also known as a
<B>token</B>.

<DT><B>name</B>

<DD>
A
<I>word</I>

consisting only of alphanumeric characters and underscores, and
beginning with an alphabetic character or an underscore.  Also
referred to as an
<B>identifier</B>.

<DT><B>metacharacter</B>

<DD>
A character that, when unquoted, separates words.  One of the following:
<BR>

<DL COMPACT><DT><DD>
<P>

<B>|  &amp;  ;  (  )  &lt;  &gt;  space  tab  newline</B>

</DL>

<DT><B>control operator</B>

<DD>
A <I>token</I> that performs a control function.  It is one of the following
symbols:
<DL COMPACT><DT><DD>
<P>

<B>||  &amp;  &amp;&amp;  ;  ;;  ;&amp;  ;;&amp;  (  )  |  |&amp;    &lt;newline&gt;</B>

</DL>


</DL>
<A NAME="lbAJ">&nbsp;</A>
<H3>RESERVED WORDS</H3>

<I>Reserved words</I> are words that have a special meaning to the shell.
The following words are recognized as reserved when unquoted and either
the first word of a command (see
<FONT SIZE=-1><B>SHELL GRAMMAR</B>

</FONT>
below), the third word of a
<B>case</B>

or
<B>select</B>

command
(only <B>in</B> is valid), or the third word of a
<B>for</B>

command (only <B>in</B> and <B>do</B> are valid):
<DL COMPACT><DT><DD>

<P>

<B>
</B>

!    case    coproc    do    done    elif    else    esac    fi    for    function    if    in    select    then    until    while    {    }    time    [[    ]]
</DL>


<A NAME="lbAK">&nbsp;</A>
<H3>SHELL GRAMMAR</H3>

This section describes the syntax of the various forms of shell commands.
<A NAME="lbAL">&nbsp;</A>
<H4>Simple Commands</H4>

A <I>simple command</I> is a sequence of optional variable assignments
followed by <B>blank</B>-separated words and redirections, and
terminated by a <I>control operator</I>.  The first word
specifies the command to be executed, and is passed as argument zero.
The remaining words are passed as arguments to the invoked command.
<P>

The return value of a <I>simple command</I> is its exit status, or
128+<I>n</I> if the command is terminated by signal
<I>n</I>.

<A NAME="lbAM">&nbsp;</A>
<H4>Pipelines</H4>

A <I>pipeline</I> is a sequence of one or more commands separated by
one of the control operators
<B>|</B>

or <B>|&amp;</B>.
The format for a pipeline is:
<DL COMPACT><DT><DD>
<P>

[<B>time</B> [<B>-p</B>]] [ ! ] <I>command1</I> [ [<B>|</B>|<B>|&amp;</B>] <I>command2</I> ... ]
</DL>

<P>

The standard output of
<I>command1</I>

is connected via a pipe to the standard input of
<I>command2</I>.

This connection is performed before any redirections specified by the
<I>command1</I>(see

<FONT SIZE=-1><B>REDIRECTION</B>

</FONT>
below).
If <B>|&amp;</B> is used, <I>command1</I>'s standard error, in addition to its
standard output, is connected to
<I>command2</I>'s standard input through the pipe;
it is shorthand for <B>2&gt;&amp;1 |</B>.
This implicit redirection of the standard error to the standard output is
performed after any redirections specified by <I>command1</I>.
<P>

The return status of a pipeline is the exit status of the last
command, unless the <B>pipefail</B> option is enabled.
If <B>pipefail</B> is enabled, the pipeline's return status is the
value of the last (rightmost) command to exit with a non-zero status,
or zero if all commands exit successfully.
If the reserved word
<B>!</B>

precedes a pipeline, the exit status of that pipeline is the logical
negation of the exit status as described above.
The shell waits for all commands in the pipeline to
terminate before returning a value.
<P>

If the
<B>time</B>

reserved word precedes a pipeline, the elapsed as well as user and
system time consumed by its execution are reported when the pipeline
terminates.
The <B>-p</B> option changes the output format to that specified by POSIX.
When the shell is in <I>posix mode</I>, it does not recognize
<B>time</B> as a reserved word if the next token begins with a `-'.
The
<FONT SIZE=-1><B>TIMEFORMAT</B>

</FONT>
variable may be set to a format string that specifies how the timing
information should be displayed; see the description of
<FONT SIZE=-1><B>TIMEFORMAT</B>

</FONT>
under
<B>Shell Variables</B>

below.
<P>

When the shell is in <I>posix mode</I>, <B>time</B>
may be followed by a newline.  In this case, the shell displays the
total user and system time consumed by the shell and its children.
The
<FONT SIZE=-1><B>TIMEFORMAT</B>

</FONT>
variable may be used to specify the format of
the time information.
<P>

Each command in a multi-command pipeline,
where pipes are created,
is executed in a <I>subshell</I>, which is a
separate process.
See
<FONT SIZE=-1><B>COMMAND EXECUTION ENVIRONMENT</B></FONT>
for a description of subshells and a subshell environment.
If the <B>lastpipe</B> option is enabled using the <B>shopt</B> builtin
(see the description of <B>shopt</B> below),
the last element of a pipeline may be run by the shell process
when job control is not active.
<A NAME="lbAN">&nbsp;</A>
<H4>Lists</H4>

A <I>list</I> is a sequence of one or more pipelines separated by one
of the operators
<B>;</B>,

<B>&amp;</B>,

<B>&amp;&amp;</B>,

or
<B>||</B>,

and optionally terminated by one of
<B>;</B>,

<B>&amp;</B>,

or
<B>&lt;newline&gt;</B>.

<P>

Of these list operators,
<B>&amp;&amp;</B>

and
<B>||</B>

have equal precedence, followed by
<B>;</B>

and
<B>&amp;</B>,

which have equal precedence.
<P>

A sequence of one or more newlines may appear in a <I>list</I> instead
of a semicolon to delimit commands.
<P>

If a command is terminated by the control operator
<B>&amp;</B>,

the shell executes the command in the <I>background</I>
in a subshell.
The shell does not wait for the command to
finish, and the return status is 0.
These are referred to as <I>asynchronous</I> commands.
Commands separated by a
<B>;</B>

are executed sequentially; the shell waits for each
command to terminate in turn.  The return status is the
exit status of the last command executed.
<P>

AND and OR lists are sequences of one or more pipelines separated by the
<B>&amp;&amp;</B> and <B>||</B> control operators, respectively.
AND and OR lists are executed with left associativity.
An AND list has the form
<DL COMPACT><DT><DD>
<P>

<I>command1</I> <B>&amp;&amp;</B> <I>command2</I>
</DL>

<P>

<I>command2</I>

is executed if, and only if,
<I>command1</I>

returns an exit status of zero (success).
<P>

An OR list has the form
<DL COMPACT><DT><DD>
<P>

<I>command1</I> <B>||</B> <I>command2</I>
</DL>

<P>

<I>command2</I>

is executed if, and only if,
<I>command1</I>

returns a non-zero exit status.
The return status of
AND and OR lists is the exit status of the last command
executed in the list.
<A NAME="lbAO">&nbsp;</A>
<H4>Compound Commands</H4>

A <I>compound command</I> is one of the following.
In most cases a <I>list</I> in a command's description may be separated from
the rest of the command by one or more newlines, and may be followed by a
newline in place of a semicolon.
<DL COMPACT>
<DT>(<I>list</I>)<DD>
<I>list</I> is executed in a subshell (see
<FONT SIZE=-1><B>COMMAND EXECUTION ENVIRONMENT</B></FONT>
below for a description of a subshell environment).
Variable assignments and builtin
commands that affect the shell's environment do not remain in effect
after the command completes.  The return status is the exit status of
<I>list</I>.
<DT>{ <I>list</I>; }<DD>
<I>list</I> is simply executed in the current shell environment.
<I>list</I> must be terminated with a newline or semicolon.
This is known as a <I>group command</I>.
The return status is the exit status of
<I>list</I>.
Note that unlike the metacharacters <B>(</B> and <B>)</B>, <B>{</B> and
<B>}</B> are <I>reserved words</I> and must occur where a reserved
word is permitted to be recognized.  Since they do not cause a word
break, they must be separated from <I>list</I> by whitespace or another
shell metacharacter.
<DT>((<I>expression</I>))<DD>
The <I>expression</I> is evaluated according to the rules described
below under
<FONT SIZE=-1><B>ARITHMETIC EVALUATION</B>.

</FONT>
If the value of the expression is non-zero, the return status is 0;
otherwise the return status is 1.
The <I>expression</I>
undergoes the same expansions
as if it were within double quotes,
but double quote characters in <I>expression</I> are not treated specially
and are removed.
<DT><B>[[</B> <I>expression</I> <B>]]</B><DD>
Return a status of 0 or 1 depending on the evaluation of
the conditional expression <I>expression</I>.
Expressions are composed of the primaries described below under
<FONT SIZE=-1><B>CONDITIONAL EXPRESSIONS</B>.

</FONT>
The words between the <B>[[</B> and <B>]]</B> do not undergo word splitting
and pathname expansion.
The shell performs tilde expansion, parameter and
variable expansion, arithmetic expansion, command substitution, process
substitution, and quote removal on those words
(the expansions that would occur if the words were enclosed in double quotes).
Conditional operators such as <B>-f</B> must be unquoted to be recognized
as primaries.
<P>


When used with <B>[[</B>, the <B>&lt;</B> and <B>&gt;</B> operators sort
lexicographically using the current locale.
<P>


When the <B>==</B> and <B>!=</B> operators are used, the string to the
right of the operator is considered a pattern and matched according
to the rules described below under <B>Pattern Matching</B>,
as if the <B>extglob</B> shell option were enabled.
The <B>=</B> operator is equivalent to <B>==</B>.
If the
<B>nocasematch</B>

shell option is enabled, the match is performed without regard to the case
of alphabetic characters.
The return value is 0 if the string matches (<B>==</B>) or does not match
(<B>!=</B>) the pattern, and 1 otherwise.
Any part of the pattern may be quoted to force the quoted portion
to be matched as a string.
<P>


An additional binary operator, <B>=~</B>, is available, with the same
precedence as <B>==</B> and <B>!=</B>.
When it is used, the string to the right of the operator is considered
a POSIX extended regular expression and matched accordingly
(using the POSIX <I>regcomp</I> and <I>regexec</I> interfaces
usually described in <I>regex</I>(3)).
The return value is 0 if the string matches
the pattern, and 1 otherwise.
If the regular expression is syntactically incorrect, the conditional
expression's return value is 2.
If the
<B>nocasematch</B>

shell option is enabled, the match is performed without regard to the case
of alphabetic characters.
If any part of the pattern is quoted, the quoted portion is matched literally.
This means every character in the quoted portion matches itself,
instead of having any special pattern matching meaning.
If the pattern is stored in a shell variable, quoting the variable
expansion forces the entire pattern to be matched literally.
Treat bracket expressions in regular expressions carefully,
since normal quoting and pattern characters lose their meanings
between brackets.
<P>


The pattern will match if it matches any part of the string.
Anchor the pattern using the <B>^</B> and <B>$</B> regular expression
operators to force it to match the entire string.
The array variable
<FONT SIZE=-1><B>BASH_REMATCH</B>

</FONT>
records which parts of the string matched the pattern.
The element of
<FONT SIZE=-1><B>BASH_REMATCH</B>

</FONT>
with index 0 contains the portion of
the string matching the entire regular expression.
Substrings matched by parenthesized subexpressions within the regular
expression are saved in the remaining
<FONT SIZE=-1><B>BASH_REMATCH</B>

</FONT>
indices. The element of
<FONT SIZE=-1><B>BASH_REMATCH</B>

</FONT>
with index <I>n</I> is the portion of the
string matching the <I>n</I>th parenthesized subexpression.
Bash sets
<FONT SIZE=-1><B>BASH_REMATCH</B>

</FONT>
in the global scope; declaring it as a local variable will lead to
unexpected results.
<P>


Expressions may be combined using the following operators, listed
in decreasing order of precedence:
<P>


<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>( </B><I>expression</I> )

<DD>
Returns the value of <I>expression</I>.
This may be used to override the normal precedence of operators.
<DT><B>! </B><I>expression</I>

<DD>
True if
<I>expression</I>

is false.
<DT><I>expression1</I> <B>&amp;&amp;</B> <I>expression2</I><DD>
True if both
<I>expression1</I>

and
<I>expression2</I>

are true.
<DT><I>expression1</I> <B>||</B> <I>expression2</I><DD>
True if either
<I>expression1</I>

or
<I>expression2</I>

is true.

</DL>
<P>

The <B>&amp;&amp;</B> and <B>||</B>
operators do not evaluate <I>expression2</I> if the value of
<I>expression1</I> is sufficient to determine the return value of
the entire conditional expression.
</DL>

<DT><B>for</B> <I>name</I> [ [ <B>in</B> [ <I>word ...</I> ] ] ; ] <B>do</B> <I>list</I> ; <B>done</B><DD>
The list of words following <B>in</B> is expanded, generating a list
of items.
The variable <I>name</I> is set to each element of this list
in turn, and <I>list</I> is executed each time.
If the <B>in</B> <I>word</I> is omitted, the <B>for</B> command executes
<I>list</I> once for each positional parameter that is set (see
<FONT SIZE=-1><B>PARAMETERS</B>

</FONT>
below).
The return status is the exit status of the last command that executes.
If the expansion of the items following <B>in</B> results in an empty
list, no commands are executed, and the return status is 0.
<DT><B>for</B> (( <I>expr1</I> ; <I>expr2</I> ; <I>expr3</I> )) ; <B>do</B> <I>list</I> ; <B>done</B><DD>
First, the arithmetic expression <I>expr1</I> is evaluated according
to the rules described below under
<FONT SIZE=-1><B>ARITHMETIC EVALUATION</B>.

</FONT>
The arithmetic expression <I>expr2</I> is then evaluated repeatedly
until it evaluates to zero.
Each time <I>expr2</I> evaluates to a non-zero value, <I>list</I> is
executed and the arithmetic expression <I>expr3</I> is evaluated.
If any expression is omitted, it behaves as if it evaluates to 1.
The return value is the exit status of the last command in <I>list</I>
that is executed, or false if any of the expressions is invalid.
<DT><B>select</B> <I>name</I> [ <B>in</B> <I>word</I> ] ; <B>do</B> <I>list</I> ; <B>done</B><DD>
The list of words following <B>in</B> is expanded, generating a list
of items, and the set of expanded words is printed on the standard
error, each preceded by a number.  If the <B>in</B>
<I>word</I> is omitted, the positional parameters are printed (see
<FONT SIZE=-1><B>PARAMETERS</B>

</FONT>
below).
<B>select</B>

then displays the
<FONT SIZE=-1><B>PS3</B>

</FONT>
prompt and reads a line from the standard input.
If the line consists of a number corresponding to one of
the displayed words, then the value of
<I>name</I>

is set to that word.
If the line is empty, the words and prompt are displayed again.
If EOF is read, the <B>select</B> command completes and returns 1.
Any other value read causes
<I>name</I>

to be set to null.  The line read is saved in the variable
<FONT SIZE=-1><B>REPLY</B>.

</FONT>
The
<I>list</I>

is executed after each selection until a
<B>break</B>

command is executed.
The exit status of
<B>select</B>

is the exit status of the last command executed in
<I>list</I>,

or zero if no commands were executed.
<DT><B>case</B> <I>word</I> <B>in</B> [ [(] <I>pattern</I> [ <B>|</B> <I>pattern</I> ] 
<DD>
A <B>case</B> command first expands <I>word</I>, and tries to match
it against each <I>pattern</I> in turn, using the matching rules
described under
<B>Pattern Matching</B>

below.
The <I>word</I> is expanded using tilde
expansion, parameter and variable expansion, arithmetic expansion,
command substitution, process substitution and quote removal.
Each <I>pattern</I> examined is expanded using tilde
expansion, parameter and variable expansion, arithmetic expansion,
command substitution, process substitution, and quote removal.
If the
<B>nocasematch</B>

shell option is enabled, the match is performed without regard to the case
of alphabetic characters.
When a match is found, the corresponding <I>list</I> is executed.
If the <B>;;</B> operator is used, no subsequent matches are attempted after
the first pattern match.
Using <B>;&amp;</B> in place of <B>;;</B> causes execution to continue with
the <I>list</I> associated with the next set of patterns.
Using <B>;;&amp;</B> in place of <B>;;</B> causes the shell to test the next
pattern list in the statement, if any, and execute any associated <I>list</I>
on a successful match,
continuing the case statement execution as if the pattern list had not matched.
The exit status is zero if no
pattern matches.  Otherwise, it is the exit status of the
last command executed in <I>list</I>.
<DT><B>if</B> <I>list</I>; <B>then</B> <I>list</I>; [ <B>elif</B> <I>list</I>; <B>then</B> <I>list</I>; ] ... [ <B>else</B> <I>list</I>; ] <B>fi</B><DD>
The
<B>if</B>

<I>list</I>

is executed.  If its exit status is zero, the
<B>then</B> <I>list</I> is executed.  Otherwise, each <B>elif</B>
<I>list</I> is executed in turn, and if its exit status is zero,
the corresponding <B>then</B> <I>list</I> is executed and the
command completes.  Otherwise, the <B>else</B> <I>list</I> is
executed, if present.  The exit status is the exit status of the
last command executed, or zero if no condition tested true.
<DT><B>while</B> <I>list-1</I>; <B>do</B> <I>list-2</I>; <B>done</B><DD>

<DT><B>until</B> <I>list-1</I>; <B>do</B> <I>list-2</I>; <B>done</B><DD>

The <B>while</B> command continuously executes the list
<I>list-2</I> as long as the last command in the list <I>list-1</I> returns
an exit status of zero.  The <B>until</B> command is identical
to the <B>while</B> command, except that the test is negated:
<I>list-2</I>

is executed as long as the last command in
<I>list-1</I>

returns a non-zero exit status.
The exit status of the <B>while</B> and <B>until</B> commands
is the exit status
of the last command executed in <I>list-2</I>, or zero if
none was executed.
</DL>
<A NAME="lbAP">&nbsp;</A>
<H4>Coprocesses</H4>

A <I>coprocess</I> is a shell command preceded by the <B>coproc</B> reserved
word.
A coprocess is executed asynchronously in a subshell, as if the command
had been terminated with the <B>&amp;</B> control operator, with a two-way pipe
established between the executing shell and the coprocess.
<P>

The syntax for a coprocess is:
<DL COMPACT><DT><DD>
<P>

<B>coproc</B> [<I>NAME</I>] <I>command</I> [<I>redirections</I>]
</DL>

<P>

This creates a coprocess named <I>NAME</I>.
<I>command</I> may be either a simple command or a compound
command (see above).
<I>NAME</I> is a shell variable name.
If <I>NAME</I> is not supplied, the default name is <B>COPROC</B>.
<P>

The recommended form to use for a coprocess is
<DL COMPACT><DT><DD>
<P>

<B>coproc</B> <I>NAME</I> { <I>command</I> [<I>redirections</I>]; }
</DL>

<P>

This form is recommended because simple commands result in the coprocess
always being named <B>COPROC</B>, and it is simpler to use and more complete
than the other compound commands.
<P>

If <I>command</I> is a compound command, <I>NAME</I> is optional. The
word following <B>coproc</B> determines whether that word is interpreted
as a variable name: it is interpreted as <I>NAME</I> if it is not a
reserved word that introduces a compound command.
If <I>command</I> is a simple command, <I>NAME</I> is not allowed; this
is to avoid confusion between <I>NAME</I> and the first word of the simple
command.
<P>

When the coprocess is executed, the shell creates an array variable (see
<B>Arrays</B>

below) named <I>NAME</I> in the context of the executing shell.
The standard output of
<I>command</I>

is connected via a pipe to a file descriptor in the executing shell,
and that file descriptor is assigned to <I>NAME</I>[0].
The standard input of
<I>command</I>

is connected via a pipe to a file descriptor in the executing shell,
and that file descriptor is assigned to <I>NAME</I>[1].
This pipe is established before any redirections specified by the
command (see
<FONT SIZE=-1><B>REDIRECTION</B>

</FONT>
below).
The file descriptors can be utilized as arguments to shell commands
and redirections using standard word expansions.
Other than those created to execute command and process substitutions,
the file descriptors are not available in subshells.
<P>

The process ID of the shell spawned to execute the coprocess is
available as the value of the variable <I>NAME</I>_PID.
The <B>wait</B>
builtin command may be used to wait for the coprocess to terminate.
<P>

Since the coprocess is created as an asynchronous command,
the <B>coproc</B> command always returns success.
The return status of a coprocess is the exit status of <I>command</I>.
<A NAME="lbAQ">&nbsp;</A>
<H4>Shell Function Definitions</H4>

A shell function is an object that is called like a simple command and
executes a compound command with a new set of positional parameters.
Shell functions are declared as follows:
<DL COMPACT>
<DT><I>fname</I> () <I>compound-command</I> [<I>redirection</I>]<DD>

<DT><B>function</B> <I>fname</I> [()] <I>compound-command</I> [<I>redirection</I>]<DD>

This defines a function named <I>fname</I>.
The reserved word <B>function</B> is optional.
If the <B>function</B> reserved word is supplied, the parentheses are optional.
The <I>body</I> of the function is the compound command
<I>compound-command</I>

(see <B>Compound Commands</B> above).
That command is usually a <I>list</I> of commands between { and }, but
may be any command listed under <B>Compound Commands</B> above.
If the <B>function</B> reserved word is used, but the
parentheses are not supplied, the braces are recommended.
<I>compound-command</I> is executed whenever <I>fname</I> is specified as the
name of a simple command.
When in <I>posix mode</I>, <I>fname</I> must be a valid shell <I>name</I>
and may not be the name of one of the
POSIX <I>special builtins</I>.
In default mode, a function name can be any unquoted shell word that does
not contain <B>$</B>.
Any redirections (see
<FONT SIZE=-1><B>REDIRECTION</B>

</FONT>
below) specified when a function is defined are performed
when the function is executed.
The exit status of a function definition is zero unless a syntax error
occurs or a readonly function with the same name already exists.
When executed, the exit status of a function is the exit status of the
last command executed in the body.  (See
<FONT SIZE=-1><B>FUNCTIONS</B>

</FONT>
below.)
</DL>
<A NAME="lbAR">&nbsp;</A>
<H3>COMMENTS</H3>

In a non-interactive shell, or an interactive shell in which the
<B>interactive_comments</B>

option to the
<B>shopt</B>

builtin is enabled (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below), a word beginning with
<B>#</B>

causes that word and all remaining characters on that line to
be ignored.  An interactive shell without the
<B>interactive_comments</B>

option enabled does not allow comments.  The
<B>interactive_comments</B>

option is on by default in interactive shells.
<A NAME="lbAS">&nbsp;</A>
<H3>QUOTING</H3>

<I>Quoting</I> is used to remove the special meaning of certain
characters or words to the shell.  Quoting can be used to
disable special treatment for special characters, to prevent
reserved words from being recognized as such, and to prevent
parameter expansion.
<P>

Each of the <I>metacharacters</I> listed above under
<FONT SIZE=-1><B>DEFINITIONS</B>

</FONT>
has special meaning to the shell and must be quoted if it is to
represent itself.
<P>

When the command history expansion facilities are being used
(see
<FONT SIZE=-1><B>HISTORY EXPANSION</B>

</FONT>
below), the
<I>history expansion</I> character, usually <B>!</B>, must be quoted
to prevent history expansion.
<P>

There are three quoting mechanisms: the
<I>escape character</I>,

single quotes, and double quotes.
<P>

A non-quoted backslash (<B>\</B>) is the
<I>escape character</I>.

It preserves the literal value of the next character that follows,
with the exception of &lt;newline&gt;.  If a <B>\</B>&lt;newline&gt; pair
appears, and the backslash is not itself quoted, the <B>\</B>&lt;newline&gt;
is treated as a line continuation (that is, it is removed from the
input stream and effectively ignored).
<P>

Enclosing characters in single quotes preserves the literal value
of each character within the quotes.  A single quote may not occur
between single quotes, even when preceded by a backslash.
<P>

Enclosing characters in double quotes preserves the literal value
of all characters within the quotes, with the exception of
<B>$</B>,

<B>`</B>,

<B>\</B>,

and, when history expansion is enabled,
<B>!</B>.

When the shell is in <I>posix mode</I>, the <B>!</B> has no special meaning
within double quotes, even when history expansion is enabled.
The characters
<B>$</B>

and
<B>`</B>

retain their special meaning within double quotes.  The backslash
retains its special meaning only when followed by one of the following
characters:
<B>$</B>,

<B>`</B>,

<B>&quot;</B>,
<B>\</B>,

or
<B>&lt;newline&gt;</B>.

A double quote may be quoted within double quotes by preceding it with
a backslash.
If enabled, history expansion will be performed unless an
<B>!</B>

appearing in double quotes is escaped using a backslash.
The backslash preceding the
<B>!</B>

is not removed.
<P>

The special parameters
<B>*</B>

and
<B>@</B>

have special meaning when in double
quotes (see
<FONT SIZE=-1><B>PARAMETERS</B>

</FONT>
below).
<P>

Character sequences of the form <B>$</B>aq<I>string</I>aq are treated
as a special variant of single quotes.
The sequence expands to <I>string</I>, with backslash-escaped characters
in <I>string</I> replaced as specified by the ANSI C standard.
Backslash escape sequences, if present, are decoded as follows:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>\a</B>

<DD>
alert (bell)
<DT><B>\b</B>

<DD>
backspace
<DT><B>\e</B>

<DD>
<DT><B>\E</B>

<DD>
an escape character
<DT><B>\f</B>

<DD>
form feed
<DT><B>\n</B>

<DD>
new line
<DT><B>\r</B>

<DD>
carriage return
<DT><B>\t</B>

<DD>
horizontal tab
<DT><B>\v</B>

<DD>
vertical tab
<DT><B>\\</B>

<DD>
backslash
<DT><B>\aq</B>

<DD>
single quote
<DT><B>\dq</B>

<DD>
double quote
<DT><B>\?</B>

<DD>
question mark
<DT><B>\</B><I>nnn</I>

<DD>
the eight-bit character whose value is the octal value <I>nnn</I>
(one to three octal digits)
<DT><B>\x</B><I>HH</I>

<DD>
the eight-bit character whose value is the hexadecimal value <I>HH</I>
(one or two hex digits)
<DT><B>\u</B><I>HHHH</I>

<DD>
the Unicode (ISO/IEC 10646) character whose value is the hexadecimal value
<I>HHHH</I> (one to four hex digits)
<DT><B>\U</B><I>HHHHHHHH</I>

<DD>
the Unicode (ISO/IEC 10646) character whose value is the hexadecimal value
<I>HHHHHHHH</I> (one to eight hex digits)
<DT><B>\c</B><I>x</I>

<DD>
a control-<I>x</I> character

</DL></DL>

<P>

The expanded result is single-quoted, as if the dollar sign had
not been present.
<P>

A double-quoted string preceded by a dollar sign (<B>$</B>dq<I>string</I>dq)
will cause the string to be translated according to the current locale.
The <I>gettext</I> infrastructure performs the lookup and
translation, using the <B>LC_MESSAGES</B>, <B>TEXTDOMAINDIR</B>,
and <B>TEXTDOMAIN</B> shell variables.
If the current locale is <B>C</B> or <B>POSIX</B>,
if there are no translations available,
or if the string is not translated,
the dollar sign is ignored.
This is a form of double quoting, so the string remains double-quoted
by default, whether or not it is translated and replaced.
If the <B>noexpand_translation</B> option is enabled
using the <B>shopt</B> builtin,
translated strings are single-quoted instead of double-quoted.
See the description of
<B>shopt</B>

below under
<FONT SIZE=-1><B>SHELL</B>BUILTIN<B>COMMANDS</B>.

</FONT>
<A NAME="lbAT">&nbsp;</A>
<H3>PARAMETERS</H3>

A
<I>parameter</I>

is an entity that stores values.
It can be a
<I>name</I>,

a number, or one of the special characters listed below under
<B>Special Parameters</B>.

A
<I>variable</I>

is a parameter denoted by a
<I>name</I>.

A variable has a <I>value</I> and zero or more <I>attributes</I>.
Attributes are assigned using the
<B>declare</B>

builtin command (see
<B>declare</B>

below in
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>).

</FONT>
<P>

A parameter is set if it has been assigned a value.  The null string is
a valid value.  Once a variable is set, it may be unset only by using
the
<B>unset</B>

builtin command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<P>

A
<I>variable</I>

may be assigned to by a statement of the form
<DL COMPACT><DT><DD>
<P>

<I>name</I>=[<I>value</I>]
</DL>

<P>

If
<I>value</I>

is not given, the variable is assigned the null string.  All
<I>values</I>

undergo tilde expansion, parameter and variable expansion,
command substitution, arithmetic expansion, and quote
removal (see
<FONT SIZE=-1><B>EXPANSION</B>

</FONT>
below).  If the variable has its
<B>integer</B>

attribute set, then
<I>value</I>

is evaluated as an arithmetic expression even if the $((...)) expansion is
not used (see
<B>Arithmetic Expansion</B>

below).
Word splitting and pathname expansion are not performed.
Assignment statements may also appear as arguments to the
<B>alias</B>,

<B>declare</B>,

<B>typeset</B>,

<B>export</B>,

<B>readonly</B>,

and
<B>local</B>

builtin commands (<I>declaration</I> commands).
When in <I>posix mode</I>, these builtins may appear in a command after
one or more instances of the <B>command</B> builtin and retain these
assignment statement properties.
<P>

In the context where an assignment statement is assigning a value
to a shell variable or array index, the += operator can be used to
append to or add to the variable's previous value.
This includes arguments to builtin commands such as <B>declare</B> that
accept assignment statements (<I>declaration</I> commands).
When += is applied to a variable for which the <B>integer</B> attribute has been
set, <I>value</I> is evaluated as an arithmetic expression and added to the
variable's current value, which is also evaluated.
When += is applied to an array variable using compound assignment (see
<B>Arrays</B>

below), the
variable's value is not unset (as it is when using =), and new values are
appended to the array beginning at one greater than the array's maximum index
(for indexed arrays) or added as additional key-value pairs in an
associative array.
When applied to a string-valued variable, <I>value</I> is expanded and
appended to the variable's value.
<P>

A variable can be assigned the <I>nameref</I> attribute using the
<B>-n</B> option to the <B>declare</B> or <B>local</B> builtin commands
(see the descriptions of <B>declare</B> and <B>local</B> below)
to create a <I>nameref</I>, or a reference to another variable.
This allows variables to be manipulated indirectly.
Whenever the nameref variable is referenced, assigned to, unset, or has
its attributes modified (other than using or changing the <I>nameref</I>
attribute itself), the
operation is actually performed on the variable specified by the nameref
variable's value.
A nameref is commonly used within shell functions to refer to a variable
whose name is passed as an argument to the function.
For instance, if a variable name is passed to a shell function as its first
argument, running
<P>
<DL COMPACT><DT><DD>
<TT>declare -n ref=$1</TT>

</DL>

<P>
inside the function creates a nameref variable <B>ref</B> whose value is
the variable name passed as the first argument.
References and assignments to <B>ref</B>, and changes to its attributes,
are treated as references, assignments, and attribute modifications
to the variable whose name was passed as <B>$1</B>.
If the control variable in a <B>for</B> loop has the nameref attribute,
the list of words can be a list of shell variables, and a name reference
will be established for each word in the list, in turn, when the loop is
executed.
Array variables cannot be given the <B>nameref</B> attribute.
However, nameref variables can reference array variables and subscripted
array variables.
Namerefs can be unset using the <B>-n</B> option to the <B>unset</B> builtin.
Otherwise, if <B>unset</B> is executed with the name of a nameref variable
as an argument, the variable referenced by the nameref variable will be unset.
<A NAME="lbAU">&nbsp;</A>
<H4>Positional Parameters</H4>

A
<I>positional parameter</I>

is a parameter denoted by one or more
digits, other than the single digit 0.  Positional parameters are
assigned from the shell's arguments when it is invoked,
and may be reassigned using the
<B>set</B>

builtin command.  Positional parameters may not be assigned to
with assignment statements.  The positional parameters are
temporarily replaced when a shell function is executed (see
<FONT SIZE=-1><B>FUNCTIONS</B>

</FONT>
below).
<P>

When a positional parameter consisting of more than a single
digit is expanded, it must be enclosed in braces (see
<FONT SIZE=-1><B>EXPANSION</B>

</FONT>
below).
<A NAME="lbAV">&nbsp;</A>
<H4>Special Parameters</H4>

The shell treats several parameters specially.  These parameters may
only be referenced; assignment to them is not allowed.

<DL COMPACT>
<DT><B>*</B>

<DD>
Expands to the positional parameters, starting from one.
When the expansion is not within double quotes, each positional parameter
expands to a separate word.
In contexts where it is performed, those words
are subject to further word splitting and pathname expansion.
When the expansion occurs within double quotes, it expands to a single word
with the value of each parameter separated by the first character of the
<FONT SIZE=-1><B>IFS</B>

</FONT>
special variable.  That is, &quot;<B>$*</B>&quot; is equivalent
to &quot;<B>$1</B><I>c</I><B>$2</B><I>c</I><B>...</B>&quot;, where
<I>c</I>

is the first character of the value of the
<FONT SIZE=-1><B>IFS</B>

</FONT>
variable.  If
<FONT SIZE=-1><B>IFS</B>

</FONT>
is unset, the parameters are separated by spaces.
If
<FONT SIZE=-1><B>IFS</B>

</FONT>
is null, the parameters are joined without intervening separators.
<DT><B>@</B>

<DD>
Expands to the positional parameters, starting from one.
In contexts where word splitting is performed, this expands each
positional parameter to a separate word; if not within double
quotes, these words are subject to word splitting.
In contexts where word splitting is not performed,
this expands to a single word
with each positional parameter separated by a space.
When the
expansion occurs within double quotes, each parameter expands to a
separate word.  That is, &quot;<B>$@</B>&quot; is equivalent to
&quot;<B>$1</B>&quot; &quot;<B>$2</B>&quot; ...
If the double-quoted expansion occurs within a word, the expansion of
the first parameter is joined with the beginning part of the original
word, and the expansion of the last parameter is joined with the last
part of the original word.
When there are no positional parameters, &quot;<B>$@</B>&quot; and
<B>$@</B>

expand to nothing (i.e., they are removed).
<DT><B>#</B>

<DD>
Expands to the number of positional parameters in decimal.
<DT><B>?</B>

<DD>
Expands to the exit status of the most recently executed foreground
pipeline.
<DT><B>-</B>

<DD>
Expands to the current option flags as specified upon invocation,
by the
<B>set</B>

builtin command, or those set by the shell itself
(such as the
<B>-i</B>

option).
<DT><B>$</B>

<DD>
Expands to the process ID of the shell. In a subshell, it
expands to the process ID of the current shell, not the
subshell.
<DT><B>!</B>

<DD>
Expands to the process ID of the job most recently placed into the
background, whether executed as an asynchronous command or using
the <B>bg</B> builtin (see
<FONT SIZE=-1><B>JOB CONTROL</B>

</FONT>
below).
<DT><B>0</B>

<DD>
Expands to the name of the shell or shell script.  This is set at
shell initialization.  If
<B>bash</B>

is invoked with a file of commands,
<B>$0</B>

is set to the name of that file.  If
<B>bash</B>

is started with the
<B>-c</B>

option, then
<B>$0</B>

is set to the first argument after the string to be
executed, if one is present.  Otherwise, it is set
to the filename used to invoke
<B>bash</B>,

as given by argument zero.

</DL>
<A NAME="lbAW">&nbsp;</A>
<H4>Shell Variables</H4>

The following variables are set by the shell:
<P>


<DL COMPACT>
<DT><B>_</B>

<DD>
At shell startup, set to the pathname used to invoke the
shell or shell script being executed as passed in the environment
or argument list.
Subsequently, expands to the last argument to the previous simple
command executed in the foreground, after expansion.
Also set to the full pathname used to invoke each command executed
and placed in the environment exported to that command.
When checking mail, this parameter holds the name of the mail file
currently being checked.
<DT><B>BASH</B>

<DD>
Expands to the full filename used to invoke this instance of
<B>bash</B>.

<DT><B>BASHOPTS</B>

<DD>
A colon-separated list of enabled shell options.  Each word in
the list is a valid argument for the
<B>-s</B>

option to the
<B>shopt</B>

builtin command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).  The options appearing in
<FONT SIZE=-1><B>BASHOPTS</B>

</FONT>
are those reported as
<I>on</I>

by <B>shopt</B>.
If this variable is in the environment when
<B>bash</B>

starts up, each shell option in the list will be enabled before
reading any startup files.
This variable is read-only.
<DT><B>BASHPID</B>

<DD>
Expands to the process ID of the current <B>bash</B> process.
This differs from <B>$$</B> under certain circumstances, such as subshells
that do not require <B>bash</B> to be re-initialized.
Assignments to
<FONT SIZE=-1><B>BASHPID</B>

</FONT>
have no effect.
If
<B>BASHPID</B>

is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>BASH_ALIASES</B>

<DD>
An associative array variable whose members correspond to the internal
list of aliases as maintained by the <B>alias</B> builtin.
Elements added to this array appear in the alias list; however,
unsetting array elements currently does not cause aliases to be removed
from the alias list.
If
<B>BASH_ALIASES</B>

is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>BASH_ARGC</B>

<DD>
An array variable whose values are the number of parameters in each
frame of the current <B>bash</B> execution call stack.
The number of
parameters to the current subroutine (shell function or script executed
with <B>.</B> or <B>source</B>) is at the top of the stack.
When a subroutine is executed, the number of parameters passed is pushed onto
<FONT SIZE=-1><B>BASH_ARGC</B>.

</FONT>
The shell sets
<FONT SIZE=-1><B>BASH_ARGC</B>

</FONT>
only when in extended debugging mode (see the description of the
<B>extdebug</B>

option to the
<B>shopt</B>

builtin below).
Setting <B>extdebug</B> after the shell has started to execute a script,
or referencing this variable when <B>extdebug</B> is not set,
may result in inconsistent values.
<DT><B>BASH_ARGV</B>

<DD>
An array variable containing all of the parameters in the current <B>bash</B>
execution call stack.  The final parameter of the last subroutine call
is at the top of the stack; the first parameter of the initial call is
at the bottom.  When a subroutine is executed, the parameters supplied
are pushed onto
<FONT SIZE=-1><B>BASH_ARGV</B>.

</FONT>
The shell sets
<FONT SIZE=-1><B>BASH_ARGV</B>

</FONT>
only when in extended debugging mode
(see the description of the
<B>extdebug</B>

option to the
<B>shopt</B>

builtin below).
Setting <B>extdebug</B> after the shell has started to execute a script,
or referencing this variable when <B>extdebug</B> is not set,
may result in inconsistent values.
<DT><B>BASH_ARGV0</B>

<DD>
When referenced, this variable expands to the name of the shell or shell
script (identical to
<B>$0</B>;

see the description of special parameter 0 above).
Assignment to
<B>BASH_ARGV0</B>

causes the value assigned to also be assigned to <B>$0</B>.
If
<B>BASH_ARGV0</B>

is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>BASH_CMDS</B>

<DD>
An associative array variable whose members correspond to the internal
hash table of commands as maintained by the <B>hash</B> builtin.
Elements added to this array appear in the hash table; however,
unsetting array elements currently does not cause command names to be removed
from the hash table.
If
<B>BASH_CMDS</B>

is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>BASH_COMMAND</B>

<DD>
The command currently being executed or about to be executed, unless the
shell is executing a command as the result of a trap,
in which case it is the command executing at the time of the trap.
If
<B>BASH_COMMAND</B>

is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>BASH_EXECUTION_STRING</B>

<DD>
The command argument to the <B>-c</B> invocation option.
<DT><B>BASH_LINENO</B>

<DD>
An array variable whose members are the line numbers in source files
where each corresponding member of
<FONT SIZE=-1><B>FUNCNAME</B>

</FONT>
was invoked.
<B>${BASH_LINENO[</B><I>$i</I><B>]}</B> is the line number in the source
file (<B>${BASH_SOURCE[</B><I>$i+1</I><B>]}</B>) where
<B>${FUNCNAME[</B><I>$i</I><B>]}</B> was called
(or <B>${BASH_LINENO[</B><I>$i-1</I><B>]}</B> if referenced within another
shell function).
Use
<FONT SIZE=-1><B>LINENO</B>

</FONT>
to obtain the current line number.
<DT><B>BASH_LOADABLES_PATH</B>

<DD>
A colon-separated list of directories in which the shell looks for
dynamically loadable builtins specified by the
<B>enable</B>

command.
<DT><B>BASH_REMATCH</B>

<DD>
An array variable whose members are assigned by the <B>=~</B> binary
operator to the <B>[[</B> conditional command.
The element with index 0 is the portion of the string
matching the entire regular expression.
The element with index <I>n</I> is the portion of the
string matching the <I>n</I>th parenthesized subexpression.
<DT><B>BASH_SOURCE</B>

<DD>
An array variable whose members are the source filenames
where the corresponding shell function names in the
<FONT SIZE=-1><B>FUNCNAME</B>

</FONT>
array variable are defined.
The shell function
<B>${FUNCNAME[</B><I>$i</I><B>]}</B> is defined in the file
<B>${BASH_SOURCE[</B><I>$i</I><B>]}</B> and called from
<B>${BASH_SOURCE[</B><I>$i+1</I><B>]}</B>.
<DT><B>BASH_SUBSHELL</B>

<DD>
Incremented by one within each subshell or subshell environment when
the shell begins executing in that environment.
The initial value is 0.
If
<B>BASH_SUBSHELL</B>

is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>BASH_VERSINFO</B>

<DD>
A readonly array variable whose members hold version information for
this instance of
<B>bash</B>.

The values assigned to the array members are as follows:
<P>
<DL COMPACT><DT><DD>
<DL COMPACT>
<DT><B>BASH_VERSINFO[</B>0]

<DD>
The major version number (the <I>release</I>).
<DT><B>BASH_VERSINFO[</B>1]

<DD>
The minor version number (the <I>version</I>).
<DT><B>BASH_VERSINFO[</B>2]

<DD>
The patch level.
<DT><B>BASH_VERSINFO[</B>3]

<DD>
The build version.
<DT><B>BASH_VERSINFO[</B>4]

<DD>
The release status (e.g., <I>beta1</I>).
<DT><B>BASH_VERSINFO[</B>5]

<DD>
The value of
<FONT SIZE=-1><B>MACHTYPE</B>.

</FONT>
</DL></DL>

<DT><B>BASH_VERSION</B>

<DD>
Expands to a string describing the version of this instance of
<B>bash</B>.

<DT><B>COMP_CWORD</B>

<DD>
An index into <B>${COMP_WORDS}</B> of the word containing the current
cursor position.
This variable is available only in shell functions invoked by the
programmable completion facilities (see <B>Programmable Completion</B>
below).
<DT><B>COMP_KEY</B>

<DD>
The key (or final key of a key sequence) used to invoke the current
completion function.
<DT><B>COMP_LINE</B>

<DD>
The current command line.
This variable is available only in shell functions and external
commands invoked by the
programmable completion facilities (see <B>Programmable Completion</B>
below).
<DT><B>COMP_POINT</B>

<DD>
The index of the current cursor position relative to the beginning of
the current command.
If the current cursor position is at the end of the current command,
the value of this variable is equal to <B>${#COMP_LINE}</B>.
This variable is available only in shell functions and external
commands invoked by the
programmable completion facilities (see <B>Programmable Completion</B>
below).
<DT><B>COMP_TYPE</B>

<DD>
Set to an integer value corresponding to the type of completion attempted
that caused a completion function to be called:
<I>TAB</I>, for normal completion,
<I>?</I>, for listing completions after successive tabs,
<I>!</I>, for listing alternatives on partial word completion,
<I>@</I>, to list completions if the word is not unmodified,
or
<I>%</I>, for menu completion.
This variable is available only in shell functions and external
commands invoked by the
programmable completion facilities (see <B>Programmable Completion</B>
below).
<DT><B>COMP_WORDBREAKS</B>

<DD>
The set of characters that the <B>readline</B> library treats as word
separators when performing word completion.
If
<FONT SIZE=-1><B>COMP_WORDBREAKS</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>COMP_WORDS</B>

<DD>
An array variable (see <B>Arrays</B> below) consisting of the individual
words in the current command line.
The line is split into words as <B>readline</B> would split it, using
<FONT SIZE=-1><B>COMP_WORDBREAKS</B>

</FONT>
as described above.
This variable is available only in shell functions invoked by the
programmable completion facilities (see <B>Programmable Completion</B>
below).
<DT><B>COPROC</B>

<DD>
An array variable (see <B>Arrays</B> below) created to hold the file descriptors
for output from and input to an unnamed coprocess (see <B>Coprocesses</B>
above).
<DT><B>DIRSTACK</B>

<DD>
An array variable (see
<B>Arrays</B>

below) containing the current contents of the directory stack.
Directories appear in the stack in the order they are displayed by the
<B>dirs</B>

builtin.
Assigning to members of this array variable may be used to modify
directories already in the stack, but the
<B>pushd</B>

and
<B>popd</B>

builtins must be used to add and remove directories.
Assignment to this variable will not change the current directory.
If
<FONT SIZE=-1><B>DIRSTACK</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>EPOCHREALTIME</B>

<DD>
Each time this parameter is referenced, it expands to the number of seconds
since the Unix Epoch (see <I>time</I>(3)) as a floating point value
with micro-second granularity.
Assignments to
<FONT SIZE=-1><B>EPOCHREALTIME</B>

</FONT>
are ignored.
If
<FONT SIZE=-1><B>EPOCHREALTIME</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>EPOCHSECONDS</B>

<DD>
Each time this parameter is referenced, it expands to the number of seconds
since the Unix Epoch (see <I>time</I>(3)).
Assignments to
<FONT SIZE=-1><B>EPOCHSECONDS</B>

</FONT>
are ignored.
If
<FONT SIZE=-1><B>EPOCHSECONDS</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>EUID</B>

<DD>
Expands to the effective user ID of the current user, initialized at
shell startup.  This variable is readonly.
<DT><B>FUNCNAME</B>

<DD>
An array variable containing the names of all shell functions
currently in the execution call stack.
The element with index 0 is the name of any currently-executing
shell function.
The bottom-most element (the one with the highest index) is
<TT>&quot;main&quot;</TT>.

This variable exists only when a shell function is executing.
Assignments to
<FONT SIZE=-1><B>FUNCNAME</B>

</FONT>
have no effect.
If
<FONT SIZE=-1><B>FUNCNAME</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<P>


This variable can be used with <B>BASH_LINENO</B> and <B>BASH_SOURCE</B>.
Each element of <B>FUNCNAME</B> has corresponding elements in
<B>BASH_LINENO</B> and <B>BASH_SOURCE</B> to describe the call stack.
For instance, <B>${FUNCNAME[</B><I>$i</I><B>]}</B> was called from the file
<B>${BASH_SOURCE[</B><I>$i+1</I><B>]}</B> at line number
<B>${BASH_LINENO[</B><I>$i</I><B>]}</B>.
The <B>caller</B> builtin displays the current call stack using this
information.
<DT><B>GROUPS</B>

<DD>
An array variable containing the list of groups of which the current
user is a member.
Assignments to
<FONT SIZE=-1><B>GROUPS</B>

</FONT>
have no effect.
If
<FONT SIZE=-1><B>GROUPS</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>HISTCMD</B>

<DD>
The history number, or index in the history list, of the current
command.
Assignments to
<FONT SIZE=-1><B>HISTCMD</B>

</FONT>
are ignored.
If
<FONT SIZE=-1><B>HISTCMD</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>HOSTNAME</B>

<DD>
Automatically set to the name of the current host.
<DT><B>HOSTTYPE</B>

<DD>
Automatically set to a string that uniquely
describes the type of machine on which
<B>bash</B>

is executing.
The default is system-dependent.
<DT><B>LINENO</B>

<DD>
Each time this parameter is referenced, the shell substitutes
a decimal number representing the current sequential line number
(starting with 1) within a script or function.  When not in a
script or function, the value substituted is not guaranteed to
be meaningful.
If
<FONT SIZE=-1><B>LINENO</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>MACHTYPE</B>

<DD>
Automatically set to a string that fully describes the system
type on which
<B>bash</B>

is executing, in the standard GNU <I>cpu-company-system</I> format.
The default is system-dependent.
<DT><B>MAPFILE</B>

<DD>
An array variable (see <B>Arrays</B> below) created to hold the text
read by the <B>mapfile</B> builtin when no variable name is supplied.
<DT><B>OLDPWD</B>

<DD>
The previous working directory as set by the
<B>cd</B>

command.
<DT><B>OPTARG</B>

<DD>
The value of the last option argument processed by the
<B>getopts</B>

builtin command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<DT><B>OPTIND</B>

<DD>
The index of the next argument to be processed by the
<B>getopts</B>

builtin command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<DT><B>OSTYPE</B>

<DD>
Automatically set to a string that
describes the operating system on which
<B>bash</B>

is executing.
The default is system-dependent.
<DT><B>PIPESTATUS</B>

<DD>
An array variable (see
<B>Arrays</B>

below) containing a list of exit status values from the processes
in the most-recently-executed foreground pipeline (which may
contain only a single command).
<DT><B>PPID</B>

<DD>
The process ID of the shell's parent.  This variable is readonly.
<DT><B>PWD</B>

<DD>
The current working directory as set by the
<B>cd</B>

command.
<DT><B>RANDOM</B>

<DD>
Each time this parameter is referenced, it expands to a random integer
between 0 and 32767.
Assigning
a value to
<FONT SIZE=-1><B>RANDOM</B>

</FONT>
initializes (seeds) the sequence of random numbers.
If
<FONT SIZE=-1><B>RANDOM</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>READLINE_ARGUMENT</B>

<DD>
Any numeric argument given to a readline command that was defined using
<TT>bind -x</TT>

(see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below)
when it was invoked.
<DT><B>READLINE_LINE</B>

<DD>
The contents of the
<B>readline</B>

line buffer, for use with
<TT>bind -x</TT>

(see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<DT><B>READLINE_MARK</B>

<DD>
The position of the mark (saved insertion point) in the
<B>readline</B>

line buffer, for use with
<TT>bind -x</TT>

(see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
The characters between the insertion point and the mark are often
called the <I>region</I>.
<DT><B>READLINE_POINT</B>

<DD>
The position of the insertion point in the
<B>readline</B>

line buffer, for use with
<TT>bind -x</TT>

(see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<DT><B>REPLY</B>

<DD>
Set to the line of input read by the
<B>read</B>

builtin command when no arguments are supplied.
<DT><B>SECONDS</B>

<DD>
Each time this parameter is
referenced, it expands to the number of seconds since shell invocation.
If a value is assigned to
<FONT SIZE=-1><B>SECONDS</B>,

</FONT>
the value returned upon subsequent
references is
the number of seconds since the assignment plus the value assigned.
The number of seconds at shell invocation and the current time are always
determined by querying the system clock.
If
<FONT SIZE=-1><B>SECONDS</B>

</FONT>
is unset, it loses its special properties, even if it is
subsequently reset.
<DT><B>SHELLOPTS</B>

<DD>
A colon-separated list of enabled shell options.  Each word in
the list is a valid argument for the
<B>-o</B>

option to the
<B>set</B>

builtin command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).  The options appearing in
<FONT SIZE=-1><B>SHELLOPTS</B>

</FONT>
are those reported as
<I>on</I>

by <B>set -o</B>.
If this variable is in the environment when
<B>bash</B>

starts up, each shell option in the list will be enabled before
reading any startup files.
This variable is read-only.
<DT><B>SHLVL</B>

<DD>
Incremented by one each time an instance of
<B>bash</B>

is started.
<DT><B>SRANDOM</B>

<DD>
This variable expands to a 32-bit pseudo-random number each time it is
referenced. The random number generator is not linear on systems that
support <TT>/dev/urandom</TT> or <I>arc4random</I>, so each returned number
has no relationship to the numbers preceding it.
The random number generator cannot be seeded, so assignments to this
variable have no effect.
If
<FONT SIZE=-1><B>SRANDOM</B>

</FONT>
is unset, it loses its special properties,
even if it is subsequently reset.
<DT><B>UID</B>

<DD>
Expands to the user ID of the current user, initialized at shell startup.
This variable is readonly.

</DL>
<P>

The following variables are used by the shell.  In some cases,
<B>bash</B>

assigns a default value to a variable; these cases are noted
below.
<P>


<DL COMPACT>
<DT><B>BASH_COMPAT</B>

<DD>
The value is used to set the shell's compatibility level.
See
<FONT SIZE=-1><B>SHELL COMPATIBILITY MODE</B>

</FONT>
below for a description of the various compatibility
levels and their effects.
The value may be a decimal number (e.g., 4.2) or an integer (e.g., 42)
corresponding to the desired compatibility level.
If <B>BASH_COMPAT</B> is unset or set to the empty string, the compatibility
level is set to the default for the current version.
If <B>BASH_COMPAT</B> is set to a value that is not one of the valid
compatibility levels, the shell prints an error message and sets the
compatibility level to the default for the current version.
The valid values correspond to the compatibility levels
described below under
<FONT SIZE=-1><B>SHELL COMPATIBILITY MODE</B>.

</FONT>
For example, 4.2 and 42 are valid values that correspond
to the <B>compat42</B> <B>shopt</B> option
and set the compatibility level to 42.
The current version is also a valid value.
<DT><B>BASH_ENV</B>

<DD>
If this parameter is set when <B>bash</B> is executing a shell script,
its value is interpreted as a filename containing commands to
initialize the shell, as in
<A HREF="file:~/.bashrc"><I>~/.bashrc</I></A>.

The value of
<FONT SIZE=-1><B>BASH_ENV</B>

</FONT>
is subjected to parameter expansion, command substitution, and arithmetic
expansion before being interpreted as a filename.
<FONT SIZE=-1><B>PATH</B>

</FONT>
is not used to search for the resultant filename.
<DT><B>BASH_XTRACEFD</B>

<DD>
If set to an integer corresponding to a valid file descriptor, <B>bash</B>
will write the trace output generated when
<TT>set -x</TT>

is enabled to that file descriptor.
The file descriptor is closed when
<FONT SIZE=-1><B>BASH_XTRACEFD</B>

</FONT>
is unset or assigned a new value.
Unsetting
<FONT SIZE=-1><B>BASH_XTRACEFD</B>

</FONT>
or assigning it the empty string causes the
trace output to be sent to the standard error.
Note that setting
<FONT SIZE=-1><B>BASH_XTRACEFD</B>

</FONT>
to 2 (the standard error file
descriptor) and then unsetting it will result in the standard error
being closed.
<DT><B>CDPATH</B>

<DD>
The search path for the
<B>cd</B>

command.
This is a colon-separated list of directories in which the shell looks
for destination directories specified by the
<B>cd</B>

command.
A sample value is
<TT>&quot;.:~:/usr&quot;</TT>.

<DT><B>CHILD_MAX</B>

<DD>
Set the number of exited child status values for the shell to remember.
Bash will not allow this value to be decreased below a POSIX-mandated
minimum, and there is a maximum value (currently 8192) that this may
not exceed.
The minimum value is system-dependent.
<DT><B>COLUMNS</B>

<DD>
Used by the <B>select</B> compound command to determine the terminal width
when printing selection lists.
Automatically set if the
<B>checkwinsize</B>

option is enabled or in an interactive shell upon receipt of a
<FONT SIZE=-1><B>SIGWINCH</B>.

</FONT>
<DT><B>COMPREPLY</B>

<DD>
An array variable from which <B>bash</B> reads the possible completions
generated by a shell function invoked by the programmable completion
facility (see <B>Programmable Completion</B> below).
Each array element contains one possible completion.
<DT><B>EMACS</B>

<DD>
If <B>bash</B> finds this variable in the environment when the shell starts
with value
<TT>t</TT>,

it assumes that the shell is running in an Emacs shell buffer and disables
line editing.
<DT><B>ENV</B>

<DD>
Expanded and executed similarly to
<FONT SIZE=-1><B>BASH_ENV</B>

</FONT>
(see <B>INVOCATION</B> above)
when an interactive shell is invoked in <I>posix mode</I>.
<DT><B>EXECIGNORE</B>

<DD>
A colon-separated list of shell patterns (see <B>Pattern Matching</B>)
defining the list of filenames to be ignored by command search using
<B>PATH</B>.
Files whose full pathnames match one of these patterns are not considered
executable files for the purposes of completion and command execution
via <B>PATH</B> lookup.
This does not affect the behavior of the <B>[</B>, <B>test</B>, and <B>[[</B>
commands.
Full pathnames in the command hash table are not subject to <B>EXECIGNORE</B>.
Use this variable to ignore shared library files that have the executable
bit set, but are not executable files.
The pattern matching honors the setting of the <B>extglob</B> shell
option.
<DT><B>FCEDIT</B>

<DD>
The default editor for the
<B>fc</B>

builtin command.
<DT><B>FIGNORE</B>

<DD>
A colon-separated list of suffixes to ignore when performing
filename completion (see
<FONT SIZE=-1><B>READLINE</B>

</FONT>
below).
A filename whose suffix matches one of the entries in
<FONT SIZE=-1><B>FIGNORE</B>

</FONT>
is excluded from the list of matched filenames.
A sample value is
<TT>&quot;.o:~&quot;</TT>.

<DT><B>FUNCNEST</B>

<DD>
If set to a numeric value greater than 0, defines a maximum function
nesting level.  Function invocations that exceed this nesting level
will cause the current command to abort.
<DT><B>GLOBIGNORE</B>

<DD>
A colon-separated list of patterns defining the set of file names to
be ignored by pathname expansion.
If a file name matched by a pathname expansion pattern also matches one
of the patterns in
<FONT SIZE=-1><B>GLOBIGNORE</B>,

</FONT>
it is removed from the list of matches.
<DT><B>HISTCONTROL</B>

<DD>
A colon-separated list of values controlling how commands are saved on
the history list.
If the list of values includes
<I>ignorespace</I>,

lines which begin with a
<B>space</B>

character are not saved in the history list.
A value of
<I>ignoredups</I>

causes lines matching the previous history entry to not be saved.
A value of
<I>ignoreboth</I>

is shorthand for <I>ignorespace</I> and <I>ignoredups</I>.
A value of
<I>erasedups</I>

causes all previous lines matching the current line to be removed from
the history list before that line is saved.
Any value not in the above list is ignored.
If
<FONT SIZE=-1><B>HISTCONTROL</B>

</FONT>
is unset, or does not include a valid value,
all lines read by the shell parser are saved on the history list,
subject to the value of
<FONT SIZE=-1><B>HISTIGNORE</B>.

</FONT>
The second and subsequent lines of a multi-line compound command are
not tested, and are added to the history regardless of the value of
<FONT SIZE=-1><B>HISTCONTROL</B>.

</FONT>
<DT><B>HISTFILE</B>

<DD>
The name of the file in which command history is saved (see
<FONT SIZE=-1><B>HISTORY</B>

</FONT>
below).  The default value is <A HREF="file:~/.bash_history"><I>~/.bash_history</I></A>.  If unset, the
command history is not saved when a shell exits.
<DT><B>HISTFILESIZE</B>

<DD>
The maximum number of lines contained in the history file.  When this
variable is assigned a value, the history file is truncated, if
necessary,
to contain no more than that number of lines by removing the oldest entries.
The history file is also truncated to this size after
writing it when a shell exits.
If the value is 0, the history file is truncated to zero size.
Non-numeric values and numeric values less than zero inhibit truncation.
The shell sets the default value to the value of <B>HISTSIZE</B>
after reading any startup files.
<DT><B>HISTIGNORE</B>

<DD>
A colon-separated list of patterns used to decide which command lines
should be saved on the history list.  Each pattern is anchored at the
beginning of the line and must match the complete line (no implicit
`<B>*</B>' is appended).  Each pattern is tested against the line
after the checks specified by
<FONT SIZE=-1><B>HISTCONTROL</B>

</FONT>
are applied.
In addition to the normal shell pattern matching characters, `<B>&amp;</B>'
matches the previous history line.  `<B>&amp;</B>' may be escaped using a
backslash; the backslash is removed before attempting a match.
The second and subsequent lines of a multi-line compound command are
not tested, and are added to the history regardless of the value of
<FONT SIZE=-1><B>HISTIGNORE</B>.

</FONT>
The pattern matching honors the setting of the <B>extglob</B> shell
option.
<DT><B>HISTSIZE</B>

<DD>
The number of commands to remember in the command history (see
<FONT SIZE=-1><B>HISTORY</B>

</FONT>
below).
If the value is 0, commands are not saved in the history list.
Numeric values less than zero result in every command being saved
on the history list (there is no limit).
The shell sets the default value to 500 after reading any startup files.
<DT><B>HISTTIMEFORMAT</B>

<DD>
If this variable is set and not null, its value is used as a format string
for <I>strftime</I>(3) to print the time stamp associated with each history
entry displayed by the <B>history</B> builtin.
If this variable is set, time stamps are written to the history file so
they may be preserved across shell sessions.
This uses the history comment character to distinguish timestamps from
other history lines.
<DT><B>HOME</B>

<DD>
The home directory of the current user; the default argument for the
<B>cd</B> builtin command.
The value of this variable is also used when performing tilde expansion.
<DT><B>HOSTFILE</B>

<DD>
Contains the name of a file in the same format as

<I>/etc/hosts</I>

that should be read when the shell needs to complete a
hostname.
The list of possible hostname completions may be changed while the
shell is running;
the next time hostname completion is attempted after the
value is changed,
<B>bash</B>

adds the contents of the new file to the existing list.
If
<FONT SIZE=-1><B>HOSTFILE</B>

</FONT>
is set, but has no value, or does not name a readable file,
<B>bash</B> attempts to read

<I>/etc/hosts</I>

to obtain the list of possible hostname completions.
When
<FONT SIZE=-1><B>HOSTFILE</B>

</FONT>
is unset, the hostname list is cleared.
<DT><B>IFS</B>

<DD>
The
<I>Internal Field Separator</I>

that is used
for word splitting after expansion and to
split lines into words with the
<B>read</B>

builtin command.  The default value is
``&lt;space&gt;&lt;tab&gt;&lt;newline&gt;''.
<DT><B>IGNOREEOF</B>

<DD>
Controls the
action of an interactive shell on receipt of an
<FONT SIZE=-1><B>EOF</B>

</FONT>
character as the sole input.  If set, the value is the number of
consecutive
<FONT SIZE=-1><B>EOF</B>

</FONT>
characters which must be
typed as the first characters on an input line before
<B>bash</B>

exits.  If the variable exists but does not have a numeric value, or
has no value, the default value is 10.  If it does not exist,
<FONT SIZE=-1><B>EOF</B>

</FONT>
signifies the end of input to the shell.
<DT><B>INPUTRC</B>

<DD>
The filename for the
<B>readline</B>

startup file, overriding the default of

<A HREF="file:~/.inputrc"><I>~/.inputrc</I></A>

(see
<FONT SIZE=-1><B>READLINE</B>

</FONT>
below).
<DT><B>INSIDE_EMACS</B>

<DD>
If this variable appears in the environment when the shell starts,
<B>bash</B> assumes that it is running inside an Emacs shell buffer
and may disable line editing, depending on the value of <B>TERM</B>.
<DT><B>LANG</B>

<DD>
Used to determine the locale category for any category not specifically
selected with a variable starting with <B>LC_</B>.
<DT><B>LC_ALL</B>

<DD>
This variable overrides the value of
<FONT SIZE=-1><B>LANG</B>

</FONT>
and any other
<B>LC_</B> variable specifying a locale category.
<DT><B>LC_COLLATE</B>

<DD>
This variable determines the collation order used when sorting the
results of pathname expansion, and determines the behavior of range
expressions, equivalence classes, and collating sequences within
pathname expansion and pattern matching.
<DT><B>LC_CTYPE</B>

<DD>
This variable determines the interpretation of characters and the
behavior of character classes within pathname expansion and pattern
matching.
<DT><B>LC_MESSAGES</B>

<DD>
This variable determines the locale used to translate double-quoted
strings preceded by a <B>$</B>.
<DT><B>LC_NUMERIC</B>

<DD>
This variable determines the locale category used for number formatting.
<DT><B>LC_TIME</B>

<DD>
This variable determines the locale category used for data and time
formatting.
<DT><B>LINES</B>

<DD>
Used by the <B>select</B> compound command to determine the column length
for printing selection lists.
Automatically set if the
<B>checkwinsize</B>

option is enabled or in an interactive shell upon receipt of a
<FONT SIZE=-1><B>SIGWINCH</B>.

</FONT>
<DT><B>MAIL</B>

<DD>
If this parameter is set to a file or directory name and the
<FONT SIZE=-1><B>MAILPATH</B>

</FONT>
variable is not set,
<B>bash</B>

informs the user of the arrival of mail in the specified file or
Maildir-format directory.
<DT><B>MAILCHECK</B>

<DD>
Specifies how
often (in seconds)
<B>bash</B>

checks for mail.  The default is 60 seconds.  When it is time to check
for mail, the shell does so before displaying the primary prompt.
If this variable is unset, or set to a value that is not a number
greater than or equal to zero, the shell disables mail checking.
<DT><B>MAILPATH</B>

<DD>
A colon-separated list of filenames to be checked for mail.
The message to be printed when mail arrives in a particular file
may be specified by separating the filename from the message with a `?'.
When used in the text of the message, <B>$_</B> expands to the name of
the current mailfile.
Example:
<DL COMPACT><DT><DD>
<P>

<B>MAILPATH</B>=aq/var/mail/bfox?&quot;You have mail&quot;:~/shell-mail?&quot;$_ has mail!&quot;aq
<P>

<B>Bash</B>

can be configured to supply
a default value for this variable (there is no value by default),
but the location of the user
mail files that it uses is system dependent (e.g., /var/mail/<B>$USER</B>).
</DL>

<DT><B>OPTERR</B>

<DD>
If set to the value 1,
<B>bash</B>

displays error messages generated by the
<B>getopts</B>

builtin command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<FONT SIZE=-1><B>OPTERR</B>

</FONT>
is initialized to 1 each time the shell is invoked or a shell
script is executed.
<DT><B>PATH</B>

<DD>
The search path for commands.  It
is a colon-separated list of directories in which
the shell looks for commands (see
<FONT SIZE=-1><B>COMMAND EXECUTION</B>

</FONT>
below).
A zero-length (null) directory name in the value of
<FONT SIZE=-1><B>PATH</B>

</FONT>
indicates the current directory.
A null directory name may appear as two adjacent colons, or as an initial
or trailing colon.
The default path is system-dependent,
and is set by the administrator who installs
<B>bash</B>.

A common value is

<TT>/usr/local/bin:/usr/local/sbin:/usr/bin:/usr/sbin:/bin:/sbin</TT>.


<DT><B>POSIXLY_CORRECT</B>

<DD>
If this variable is in the environment when <B>bash</B> starts, the shell
enters <I>posix mode</I> before reading the startup files, as if the
<B>--posix</B>

invocation option had been supplied.  If it is set while the shell is
running, <B>bash</B> enables <I>posix mode</I>, as if the command
<TT>set -o posix</TT>

had been executed.
When the shell enters <I>posix mode</I>, it sets this variable if it was
not already set.
<DT><B>PROMPT_COMMAND</B>

<DD>
If this variable is set, and is an array,
the value of each set element is executed as a command
prior to issuing each primary prompt.
If this is set but not an array variable, 
its value is used as a command to execute instead.
<DT><B>PROMPT_DIRTRIM</B>

<DD>
If set to a number greater than zero, the value is used as the number of
trailing directory components to retain when expanding the <B>\w</B> and
<B>\W</B> prompt string escapes (see
<FONT SIZE=-1><B>PROMPTING</B>

</FONT>
below).  Characters removed are replaced with an ellipsis.
<DT><B>PS0</B>

<DD>
The value of this parameter is expanded (see
<FONT SIZE=-1><B>PROMPTING</B>

</FONT>
below) and displayed by interactive shells after reading a command
and before the command is executed.
<DT><B>PS1</B>

<DD>
The value of this parameter is expanded (see
<FONT SIZE=-1><B>PROMPTING</B>

</FONT>
below) and used as the primary prompt string.  The default value is
``<B>\s-\v\$ </B>''.
<DT><B>PS2</B>

<DD>
The value of this parameter is expanded as with
<FONT SIZE=-1><B>PS1</B>

</FONT>
and used as the secondary prompt string.  The default is
``<B>&gt; </B>''.
<DT><B>PS3</B>

<DD>
The value of this parameter is used as the prompt for the
<B>select</B>

command (see
<FONT SIZE=-1><B>SHELL GRAMMAR</B>

</FONT>
above).
<DT><B>PS4</B>

<DD>
The value of this parameter is expanded as with
<FONT SIZE=-1><B>PS1</B>

</FONT>
and the value is printed before each command
<B>bash</B>

displays during an execution trace.  The first character of
the expanded value of
<FONT SIZE=-1><B>PS4</B>

</FONT>
is replicated multiple times, as necessary, to indicate multiple
levels of indirection.  The default is ``<B>+ </B>''.
<DT><B>SHELL</B>

<DD>
This variable expands to the full pathname to the shell.
If it is not set when the shell starts,
<B>bash</B>

assigns to it the full pathname of the current user's login shell.
<DT><B>TIMEFORMAT</B>

<DD>
The value of this parameter is used as a format string specifying
how the timing information for pipelines prefixed with the
<B>time</B>

reserved word should be displayed.
The <B>%</B> character introduces an escape sequence that is
expanded to a time value or other information.
The escape sequences and their meanings are as follows; the
braces denote optional portions.
<P>
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>%%</B>

<DD>
A literal <B>%</B>.
<DT><B>%[</B><I>p</I>][l]R

<DD>
The elapsed time in seconds.
<DT><B>%[</B><I>p</I>][l]U

<DD>
The number of CPU seconds spent in user mode.
<DT><B>%[</B><I>p</I>][l]S

<DD>
The number of CPU seconds spent in system mode.
<DT><B>%P</B>

<DD>
The CPU percentage, computed as (%U + %S) / %R.

</DL></DL>

<DT><DD>
The optional <I>p</I> is a digit specifying the <I>precision</I>,
the number of fractional digits after a decimal point.
A value of 0 causes no decimal point or fraction to be output.
At most three places after the decimal point may be specified;
values of <I>p</I> greater than 3 are changed to 3.
If <I>p</I> is not specified, the value 3 is used.
<DT><DD>
The optional <B>l</B> specifies a longer format, including
minutes, of the form <I>MM</I>m<I>SS</I>.<I>FF</I>s.
The value of <I>p</I> determines whether or not the fraction is
included.
<DT><DD>
If this variable is not set, <B>bash</B> acts as if it had the
value <B>$aq\nreal\t%3lR\nuser\t%3lU\nsys\t%3lSaq</B>.
If the value is null, no timing information is displayed.
A trailing newline is added when the format string is displayed.

<DT><B>TMOUT</B>

<DD>
If set to a value greater than zero,
<FONT SIZE=-1><B>TMOUT</B>

</FONT>
is treated as the
default timeout for the <B>read</B> builtin.
The <B>select</B> command terminates if input does not arrive
after
<FONT SIZE=-1><B>TMOUT</B>

</FONT>
seconds when input is coming from a terminal.
In an interactive shell, the value is interpreted as the
number of seconds to wait for a line of input after issuing the
primary prompt.
<B>Bash</B>

terminates after waiting for that number of seconds if a complete
line of input does not arrive.
<DT><B>TMPDIR</B>

<DD>
If set, <B>bash</B> uses its value as the name of a directory in which
<B>bash</B> creates temporary files for the shell's use.
<DT><B>auto_resume</B>

<DD>
This variable controls how the shell interacts with the user and
job control.  If this variable is set, single word simple
commands without redirections are treated as candidates for resumption
of an existing stopped job.  There is no ambiguity allowed; if there is
more than one job beginning with the string typed, the job most recently
accessed is selected.  The
<I>name</I>

of a stopped job, in this context, is the command line used to
start it.
If set to the value
<I>exact</I>,

the string supplied must match the name of a stopped job exactly;
if set to
<I>substring</I>,

the string supplied needs to match a substring of the name of a
stopped job.  The
<I>substring</I>

value provides functionality analogous to the
<B>%?</B>

job identifier (see
<FONT SIZE=-1><B>JOB CONTROL</B>

</FONT>
below).  If set to any other value, the supplied string must
be a prefix of a stopped job's name; this provides functionality
analogous to the <B>%</B><I>string</I> job identifier.
<DT><B>histchars</B>

<DD>
The two or three characters which control history expansion
and tokenization (see
<FONT SIZE=-1><B>HISTORY EXPANSION</B>

</FONT>
below).  The first character is the <I>history expansion</I> character,
the character which signals the start of a history
expansion, normally `<B>!</B>'.
The second character is the <I>quick substitution</I>
character, which is used as shorthand for re-running the previous
command entered, substituting one string for another in the command.
The default is `<B>^</B>'.
The optional third character is the character
which indicates that the remainder of the line is a comment when found
as the first character of a word, normally `<B>#</B>'.  The history
comment character causes history substitution to be skipped for the
remaining words on the line.  It does not necessarily cause the shell
parser to treat the rest of the line as a comment.

</DL>
<A NAME="lbAX">&nbsp;</A>
<H4>Arrays</H4>

<B>Bash</B>

provides one-dimensional indexed and associative array variables.
Any variable may be used as an indexed array; the
<B>declare</B>

builtin will explicitly declare an array.
There is no maximum
limit on the size of an array, nor any requirement that members
be indexed or assigned contiguously.
Indexed arrays are referenced using integers (including arithmetic
expressions) and are zero-based; associative arrays are referenced
using arbitrary strings.
Unless otherwise noted, indexed array indices must be non-negative integers.
<P>

An indexed array is created automatically if any variable is assigned to
using the syntax <I>name</I>[<I>subscript</I>]=<I>value</I>.  The
<I>subscript</I>

is treated as an arithmetic expression that must evaluate to a number.
To explicitly declare an indexed array, use
<B>declare -a </B><I>name</I>

(see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<B>declare -a </B><I>name</I>[<I>subscript</I>]

is also accepted; the <I>subscript</I> is ignored.
<P>

Associative arrays are created using
<B>declare -A </B><I>name</I>.

<P>

Attributes may be
specified for an array variable using the
<B>declare</B>

and
<B>readonly</B>

builtins.  Each attribute applies to all members of an array.
<P>

Arrays are assigned to using compound assignments of the form
<I>name</I>=<B>(</B>value<I>1</I> ... value<I>n</I><B>)</B>, where each
<I>value</I> may be of the form [<I>subscript</I>]=<I>string</I>.
Indexed array assignments do not require anything but <I>string</I>.
Each <I>value</I> in the list is expanded using all the shell expansions
described below under
<FONT SIZE=-1><B>EXPANSION</B>.

</FONT>
When assigning to indexed arrays, if the optional brackets and subscript
are supplied, that index is assigned to;
otherwise the index of the element assigned is the last index assigned
to by the statement plus one.  Indexing starts at zero.
<P>

When assigning to an associative array, the words in a compound assignment
may be either assignment statements, for which the subscript is required,
or a list of words that is interpreted as a sequence of alternating keys
and values:
<I>name</I>=<B>( </B><I>key1 value1 key2 value2</I> ...<B>)</B>.
These are treated identically to
<I>name</I>=<B>(</B> [<I>key1</I>]=<I>value1</I> [<I>key2</I>]=<I>value2</I> ...<B>)</B>.
The first word in the list determines how the remaining words
are interpreted; all assignments in a list must be of the same type.
When using key/value pairs, the keys may not be missing or empty;
a final missing value is treated like the empty string.
<P>

This syntax is also accepted by the
<B>declare</B>

builtin.  Individual array elements may be assigned to using the
<I>name</I>[<I>subscript</I>]=<I>value</I> syntax introduced above.
When assigning to an indexed array, if
<I>name</I>

is subscripted by a negative number, that number is
interpreted as relative to one greater than the maximum index of
<I>name</I>, so negative indices count back from the end of the
array, and an index of -1 references the last element.
<P>

The += operator will append to an array variable when assigning
using the compound assignment syntax; see
<FONT SIZE=-1><B>PARAMETERS</B>

</FONT>
above.
<P>

Any element of an array may be referenced using
${<I>name</I>[<I>subscript</I>]}.  The braces are required to avoid
conflicts with pathname expansion.  If
<I>subscript</I> is <B>@</B> or <B>*</B>, the word expands to
all members of <I>name</I>.  These subscripts differ only when the
word appears within double quotes.  If the word is double-quoted,
${<I>name</I>[*]} expands to a single
word with the value of each array member separated by the first
character of the
<FONT SIZE=-1><B>IFS</B>

</FONT>
special variable, and ${<I>name</I>[@]} expands each element of
<I>name</I> to a separate word.  When there are no array members,
${<I>name</I>[@]} expands to nothing.
If the double-quoted expansion occurs within a word, the expansion of
the first parameter is joined with the beginning part of the original
word, and the expansion of the last parameter is joined with the last
part of the original word.
This is analogous to the expansion
of the special parameters <B>*</B> and <B>@</B> (see
<B>Special Parameters</B>

above).  ${#<I>name</I>[<I>subscript</I>]} expands to the length of
${<I>name</I>[<I>subscript</I>]}.  If <I>subscript</I> is <B>*</B> or
<B>@</B>, the expansion is the number of elements in the array.
If the
<I>subscript</I>

used to reference an element of an indexed array
evaluates to a number less than zero, it is
interpreted as relative to one greater than the maximum index of the array,
so negative indices count back from the end of the
array, and an index of -1 references the last element.
<P>

Referencing an array variable without a subscript is equivalent to
referencing the array with a subscript of 0.
Any reference to a variable using a valid subscript is legal, and
<B>bash</B>

will create an array if necessary.
<P>

An array variable is considered set if a subscript has been assigned a
value.  The null string is a valid value.
<P>

It is possible to obtain the keys (indices) of an array as well as the values.
${<B>!</B><I>name</I>[<I>@</I>]} and ${<B>!</B><I>name</I>[<I>*</I>]}
expand to the indices assigned in array variable <I>name</I>.
The treatment when in double quotes is similar to the expansion of the
special parameters <I>@</I> and <I>*</I> within double quotes.
<P>

The
<B>unset</B>

builtin is used to destroy arrays.  <B>unset</B> <I>name</I>[<I>subscript</I>]
destroys the array element at index <I>subscript</I>,
for both indexed and associative arrays.
Negative subscripts to indexed arrays are interpreted as described above.
Unsetting the last element of an array variable does not unset the variable.
<B>unset</B> <I>name</I>, where <I>name</I> is an array,
removes the entire array.
<B>unset</B> <I>name</I>[<I>subscript</I>], where
<I>subscript</I> is <B>*</B> or <B>@</B>, behaves differently depending on
whether <I>name</I> is an indexed or associative array.
If <I>name</I> is an associative array, this unsets the element with
subscript <B>*</B> or <B>@</B>.
If <I>name</I> is an indexed array, unset removes all of the elements but
does not remove the array itself.
<P>

When using a variable name with a subscript as an argument to a command,
such as with <B>unset</B>, without using the word expansion syntax
described above, the argument is subject to pathname expansion.
If pathname expansion is not desired, the argument should be quoted.
<P>

The
<B>declare</B>,

<B>local</B>,

and
<B>readonly</B>

builtins each accept a
<B>-a</B>

option to specify an indexed array and a
<B>-A</B>

option to specify an associative array.
If both options are supplied,
<B>-A</B>

takes precedence.
The
<B>read</B>

builtin accepts a
<B>-a</B>

option to assign a list of words read from the standard input
to an array.  The
<B>set</B>

and
<B>declare</B>

builtins display array values in a way that allows them to be
reused as assignments.
<A NAME="lbAY">&nbsp;</A>
<H3>EXPANSION</H3>

Expansion is performed on the command line after it has been split into
words.  There are seven kinds of expansion performed:
<I>brace expansion</I>,

<I>tilde expansion</I>,

<I>parameter and variable expansion</I>,

<I>command substitution</I>,

<I>arithmetic expansion</I>,

<I>word splitting</I>,

and
<I>pathname expansion</I>.

<P>

The order of expansions is:
brace expansion;
tilde expansion, parameter and variable expansion, arithmetic expansion,
and command substitution (done in a left-to-right fashion);
word splitting;
and pathname expansion.
<P>

On systems that can support it, there is an additional expansion
available: <I>process substitution</I>.
This is performed at the
same time as tilde, parameter, variable, and arithmetic expansion and
command substitution.
<P>

After these expansions are performed, quote characters present in the
original word are removed unless they have been quoted themselves
(<I>quote removal</I>).
<P>

Only brace expansion, word splitting, and pathname expansion
can increase the number of words of the expansion; other expansions
expand a single word to a single word.
The only exceptions to this are the expansions of
&quot;<B>$@</B>&quot; and &quot;<B>${</B><I>name</I><B>[@]}</B>&quot;,
and, in most cases, <B>$*</B> and <B>${</B><I>name</I><B>[*]}</B>
as explained above (see
<FONT SIZE=-1><B>PARAMETERS</B>).

</FONT>
<A NAME="lbAZ">&nbsp;</A>
<H4>Brace Expansion</H4>

<I>Brace expansion</I>

is a mechanism by which arbitrary strings
may be generated.  This mechanism is similar to
<I>pathname expansion</I>, but the filenames generated
need not exist.  Patterns to be brace expanded take
the form of an optional
<I>preamble</I>,

followed by either a series of comma-separated strings or
a sequence expression between a pair of braces, followed by
an optional
<I>postscript</I>.

The preamble is prefixed to each string contained
within the braces, and the postscript is then appended
to each resulting string, expanding left to right.
<P>

Brace expansions may be nested.  The results of each expanded
string are not sorted; left to right order is preserved.
For example, a<B>{</B>d,c,b<B>}</B>e expands into `ade ace abe'.
<P>

A sequence expression takes the form
<B>{</B><I>x</I><B>..</B><I>y</I><B>[..</B><I>incr</I><B>]}</B>,
where <I>x</I> and <I>y</I> are either integers or single letters,
and <I>incr</I>, an optional increment, is an integer.
When integers are supplied, the expression expands to each number between
<I>x</I> and <I>y</I>, inclusive.
Supplied integers may be prefixed with <I>0</I> to force each term to have the
same width.
When either <I>x</I> or y begins with a zero, the shell
attempts to force all generated terms to contain the same number of digits,
zero-padding where necessary.
When letters are supplied, the expression expands to each character
lexicographically between <I>x</I> and <I>y</I>, inclusive,
using the default C locale.
Note that both <I>x</I> and <I>y</I> must be of the same type
(integer or letter).
When the increment is supplied, it is used as the difference between
each term.  The default increment is 1 or -1 as appropriate.
<P>

Brace expansion is performed before any other expansions,
and any characters special to other expansions are preserved
in the result.  It is strictly textual.
<B>Bash</B>

does not apply any syntactic interpretation to the context of the
expansion or the text between the braces.
<P>

A correctly-formed brace expansion must contain unquoted opening
and closing braces, and at least one unquoted comma or a valid
sequence expression.
Any incorrectly formed brace expansion is left unchanged.
A <B>{</B> or <B>,</B> may be quoted with a backslash to prevent its
being considered part of a brace expression.
To avoid conflicts with parameter expansion, the string <B>${</B>
is not considered eligible for brace expansion, and inhibits brace
expansion until the closing <B>}</B>.
<P>

This construct is typically used as shorthand when the common
prefix of the strings to be generated is longer than in the
above example:
<DL COMPACT><DT><DD>
<P>

mkdir /usr/local/src/bash/{old,new,dist,bugs}
</DL>

or
<DL COMPACT><DT><DD>
chown root /usr/{ucb/{ex,edit},lib/{ex?.?*,how_ex}}
</DL>

<P>

Brace expansion introduces a slight incompatibility with
historical versions of
<B>sh</B>.

<B>sh</B>

does not treat opening or closing braces specially when they
appear as part of a word, and preserves them in the output.
<B>Bash</B>

removes braces from words as a consequence of brace
expansion.  For example, a word entered to
<B>sh</B>

as <I>file{1,2}</I>
appears identically in the output.  The same word is
output as
<I>file1 file2</I>

after expansion by
<B>bash</B>.

If strict compatibility with
<B>sh</B>

is desired, start
<B>bash</B>

with the
<B>+B</B>

option or disable brace expansion with the
<B>+B</B>

option to the
<B>set</B>

command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<A NAME="lbBA">&nbsp;</A>
<H4>Tilde Expansion</H4>

If a word begins with an unquoted tilde character (`<B>~</B>'), all of
the characters preceding the first unquoted slash (or all characters,
if there is no unquoted slash) are considered a <I>tilde-prefix</I>.
If none of the characters in the tilde-prefix are quoted, the
characters in the tilde-prefix following the tilde are treated as a
possible <I>login name</I>.
If this login name is the null string, the tilde is replaced with the
value of the shell parameter
<FONT SIZE=-1><B>HOME</B>.

</FONT>
If
<FONT SIZE=-1><B>HOME</B>

</FONT>
is unset, the home directory of the user executing the shell is
substituted instead.
Otherwise, the tilde-prefix is replaced with the home directory
associated with the specified login name.
<P>

If the tilde-prefix is a `~+', the value of the shell variable
<FONT SIZE=-1><B>PWD</B>

</FONT>
replaces the tilde-prefix.
If the tilde-prefix is a `~-', the value of the shell variable
<FONT SIZE=-1><B>OLDPWD</B>,

</FONT>
if it is set, is substituted.
If the characters following the tilde in the tilde-prefix consist
of a number <I>N</I>, optionally prefixed
by a `+' or a `-', the tilde-prefix is replaced with the corresponding
element from the directory stack, as it would be displayed by the
<B>dirs</B>

builtin invoked with the tilde-prefix as an argument.
If the characters following the tilde in the tilde-prefix consist of a
number without a leading `+' or `-', `+' is assumed.
<P>

If the login name is invalid, or the tilde expansion fails, the word
is unchanged.
<P>

Each variable assignment is checked for unquoted tilde-prefixes immediately
following a
<B>:</B>

or the first
<B>=</B>.

In these cases, tilde expansion is also performed.
Consequently, one may use filenames with tildes in assignments to
<FONT SIZE=-1><B>PATH</B>,

</FONT>
<FONT SIZE=-1><B>MAILPATH</B>,

</FONT>
and
<FONT SIZE=-1><B>CDPATH</B>,

</FONT>
and the shell assigns the expanded value.
<P>

Bash also performs tilde expansion on words satisfying the conditions of
variable assignments (as described above under
<FONT SIZE=-1><B>PARAMETERS</B>)

</FONT>
when they appear as arguments to simple commands.
Bash does not do this, except for the <I>declaration</I> commands listed
above, when in <I>posix mode</I>.
<A NAME="lbBB">&nbsp;</A>
<H4>Parameter Expansion</H4>

The `<B>$</B>' character introduces parameter expansion,
command substitution, or arithmetic expansion.  The parameter name
or symbol to be expanded may be enclosed in braces, which
are optional but serve to protect the variable to be expanded from
characters immediately following it which could be
interpreted as part of the name.
<P>

When braces are used, the matching ending brace is the first `<B>}</B>'
not escaped by a backslash or within a quoted string, and not within an
embedded arithmetic expansion, command substitution, or parameter
expansion.
<P>


<DL COMPACT>
<DT>${<I>parameter</I>}<DD>
The value of <I>parameter</I> is substituted.  The braces are required
when
<I>parameter</I>

is a positional parameter with more than one digit,
or when
<I>parameter</I>

is followed by a character which is not to be
interpreted as part of its name.
The <I>parameter</I> is a shell parameter as described above
<B>PARAMETERS</B>) or an array reference (<B>Arrays</B>).

</DL>
<P>

If the first character of <I>parameter</I> is an exclamation point (<B>!</B>),
and <I>parameter</I> is not a <I>nameref</I>,
it introduces a level of indirection.
<B>Bash</B> uses the value formed by expanding the rest of
<I>parameter</I> as the new <I>parameter</I>; this is then
expanded and that value is used in the rest of the expansion, rather
than the expansion of the original <I>parameter</I>.
This is known as <I>indirect expansion</I>.
The value is subject to tilde expansion,
parameter expansion, command substitution, and arithmetic expansion.
If <I>parameter</I> is a nameref, this expands to the name of the
parameter referenced by <I>parameter</I> instead of performing the
complete indirect expansion.
The exceptions to this are the expansions of ${<B>!</B><I>prefix</I><B>*</B>} and
${<B>!</B><I>name</I>[<I>@</I>]} described below.
The exclamation point must immediately follow the left brace in order to
introduce indirection.
<P>

In each of the cases below, <I>word</I> is subject to tilde expansion,
parameter expansion, command substitution, and arithmetic expansion.
<P>

When not performing substring expansion, using the forms documented below
(e.g., <B>:-</B>),
<B>bash</B> tests for a parameter that is unset or null.  Omitting the colon
results in a test only for a parameter that is unset.
<P>


<DL COMPACT>
<DT>${<I>parameter</I><B>:-</B><I>word</I>}<DD>
<B>Use Default Values</B>.  If
<I>parameter</I>

is unset or null, the expansion of
<I>word</I>

is substituted.  Otherwise, the value of
<I>parameter</I>

is substituted.
<DT>${<I>parameter</I><B>:=</B><I>word</I>}<DD>
<B>Assign Default Values</B>.
If
<I>parameter</I>

is unset or null, the expansion of
<I>word</I>

is assigned to
<I>parameter</I>.

The value of
<I>parameter</I>

is then substituted.  Positional parameters and special parameters may
not be assigned to in this way.
<DT>${<I>parameter</I><B>:?</B><I>word</I>}<DD>
<B>Display Error if Null or Unset</B>.
If
<I>parameter</I>

is null or unset, the expansion of <I>word</I> (or a message to that effect
if
<I>word</I>

is not present) is written to the standard error and the shell, if it
is not interactive, exits.  Otherwise, the value of <I>parameter</I> is
substituted.
<DT>${<I>parameter</I><B>:+</B><I>word</I>}<DD>
<B>Use Alternate Value</B>.
If
<I>parameter</I>

is null or unset, nothing is substituted, otherwise the expansion of
<I>word</I>

is substituted.
<DT>${<I>parameter</I><B>:</B><I>offset</I>}<DD>

<DT>${<I>parameter</I><B>:</B><I>offset</I><B>:</B><I>length</I>}<DD>

<B>Substring Expansion</B>.
Expands to up to <I>length</I> characters of the value of <I>parameter</I>
starting at the character specified by <I>offset</I>.
If <I>parameter</I> is <B>@</B> or <B>*</B>, an indexed array subscripted by
<B>@</B> or <B>*</B>, or an associative array name, the results differ as
described below.
If <I>length</I> is omitted, expands to the substring of the value of
<I>parameter</I> starting at the character specified by <I>offset</I>
and extending to the end of the value.
<I>length</I> and <I>offset</I> are arithmetic expressions (see
<FONT SIZE=-1><B>ARITHMETIC EVALUATION</B>

</FONT>
below).
<P>
If <I>offset</I> evaluates to a number less than zero, the value
is used as an offset in characters
from the end of the value of <I>parameter</I>.
If <I>length</I> evaluates to a number less than zero,
it is interpreted as an offset in characters
from the end of the value of <I>parameter</I> rather than
a number of characters, and the expansion is the characters between
<I>offset</I> and that result.
Note that a negative offset must be separated from the colon by at least
one space to avoid being confused with the <B>:-</B> expansion.
<P>
If <I>parameter</I> is <B>@</B> or <B>*</B>, the result is <I>length</I>
positional parameters beginning at <I>offset</I>.
A negative <I>offset</I> is taken relative to one greater than the greatest
positional parameter, so an offset of -1 evaluates to the last positional
parameter.
It is an expansion error if <I>length</I> evaluates to a number less than
zero.
<P>
If <I>parameter</I> is an indexed array name subscripted by @ or *,
the result is the <I>length</I>
members of the array beginning with ${<I>parameter</I>[<I>offset</I>]}.
A negative <I>offset</I> is taken relative to one greater than the maximum
index of the specified array.
It is an expansion error if <I>length</I> evaluates to a number less than
zero.
<P>
Substring expansion applied to an associative array produces undefined
results.
<P>
Substring indexing is zero-based unless the positional parameters
are used, in which case the indexing starts at 1 by default.
If <I>offset</I> is 0, and the positional parameters are used, <B>$0</B> is
prefixed to the list.
<DT>${<B>!</B><I>prefix</I><B>*</B>}<DD>

<DT>${<B>!</B><I>prefix</I><B>@</B>}<DD>

<B>Names matching prefix</B>.
Expands to the names of variables whose names begin with <I>prefix</I>,
separated by the first character of the
<FONT SIZE=-1><B>IFS</B>

</FONT>
special variable.
When <I>@</I> is used and the expansion appears within double quotes, each
variable name expands to a separate word.
<DT>${<B>!</B><I>name</I>[<I>@</I>]}<DD>

<DT>${<B>!</B><I>name</I>[<I>*</I>]}<DD>

<B>List of array keys</B>.
If <I>name</I> is an array variable, expands to the list of array indices
(keys) assigned in <I>name</I>.
If <I>name</I> is not an array, expands to 0 if <I>name</I> is set and null
otherwise.
When <I>@</I> is used and the expansion appears within double quotes, each
key expands to a separate word.
<DT>${<B>#</B><I>parameter</I>}<DD>
<B>Parameter length</B>.
The length in characters of the value of <I>parameter</I> is substituted.
If
<I>parameter</I>

is
<B>*</B>

or
<B>@</B>,

the value substituted is the number of positional parameters.
If
<I>parameter</I>

is an array name subscripted by
<B>*</B>

or
<B>@</B>,

the value substituted is the number of elements in the array.
If
<I>parameter</I>

is an indexed array name subscripted by a negative number, that number is
interpreted as relative to one greater than the maximum index of
<I>parameter</I>, so negative indices count back from the end of the
array, and an index of -1 references the last element.
<DT>${<I>parameter</I><B>#</B><I>word</I>}<DD>

<DT>${<I>parameter</I><B>##</B><I>word</I>}<DD>

<B>Remove matching prefix pattern</B>.
The
<I>word</I>

is expanded to produce a pattern just as in pathname
expansion, and matched against the expanded value of
<I>parameter</I>

using the rules described under
<B>Pattern Matching</B>

below.
If the pattern matches the beginning of
the value of
<I>parameter</I>,

then the result of the expansion is the expanded value of
<I>parameter</I>

with the shortest matching pattern (the ``<B>#</B>'' case) or the
longest matching pattern (the ``<B>##</B>'' case) deleted.
If
<I>parameter</I>

is
<B>@</B>

or
<B>*</B>,

the pattern removal operation is applied to each positional
parameter in turn, and the expansion is the resultant list.
If
<I>parameter</I>

is an array variable subscripted with
<B>@</B>

or
<B>*</B>,

the pattern removal operation is applied to each member of the
array in turn, and the expansion is the resultant list.
<DT>${<I>parameter</I><B>%</B><I>word</I>}<DD>

<DT>${<I>parameter</I><B>%%</B><I>word</I>}<DD>

<B>Remove matching suffix pattern</B>.
The <I>word</I> is expanded to produce a pattern just as in
pathname expansion, and matched against the expanded value of
<I>parameter</I>

using the rules described under
<B>Pattern Matching</B>

below.
If the pattern matches a trailing portion of the expanded value of
<I>parameter</I>,

then the result of the expansion is the expanded value of
<I>parameter</I>

with the shortest matching pattern (the ``<B>%</B>'' case) or the
longest matching pattern (the ``<B>%%</B>'' case) deleted.
If
<I>parameter</I>

is
<B>@</B>

or
<B>*</B>,

the pattern removal operation is applied to each positional
parameter in turn, and the expansion is the resultant list.
If
<I>parameter</I>

is an array variable subscripted with
<B>@</B>

or
<B>*</B>,

the pattern removal operation is applied to each member of the
array in turn, and the expansion is the resultant list.
<DT>${<I>parameter</I><B>/</B><I>pattern</I><B>/</B><I>string</I>}<DD>

<DT>${<I>parameter</I><B>//</B><I>pattern</I><B>/</B><I>string</I>}<DD>
<DT>${<I>parameter</I><B>/#</B><I>pattern</I><B>/</B><I>string</I>}<DD>
<DT>${<I>parameter</I><B>/%</B><I>pattern</I><B>/</B><I>string</I>}<DD>

<B>Pattern substitution</B>.
The <I>pattern</I> is expanded to produce a pattern just as in
pathname expansion.
<I>Parameter</I> is expanded and the longest match of <I>pattern</I>
against its value is replaced with <I>string</I>.
<I>string</I> undergoes tilde expansion, parameter and variable expansion,
arithmetic expansion, command and process substitution, and quote removal.
The match is performed using the rules described under
<B>Pattern Matching</B>

below.
In the first form above, only the first match is replaced.
If there are two slashes separating <I>parameter</I> and <I>pattern</I> 
(the second form above), all matches of <I>pattern</I> are
replaced with <I>string</I>.
If <I>pattern</I> is preceded by <B>#</B> (the third form above),
it must match at the beginning of the expanded value of <I>parameter</I>.
If <I>pattern</I> is preceded by <B>%</B> (the fourth form above),
it must match at the end of the expanded value of <I>parameter</I>.
If the expansion of <I>string</I> is null,
matches of <I>pattern</I> are deleted.
If <I>string</I> is null,
matches of <I>pattern</I> are deleted
and the <B>/</B> following <I>pattern</I> may be omitted.
<P>
If the <B>patsub_replacement</B> shell option is enabled using <B>shopt</B>,
any unquoted instances of <B>&amp;</B> in <I>string</I> are replaced with the
matching portion of <I>pattern</I>.
<P>
Quoting any part of <I>string</I> inhibits replacement in the
expansion of the quoted portion, including replacement strings stored
in shell variables.
Backslash will escape <B>&amp;</B> in <I>string</I>; the backslash is removed
in order to permit a literal <B>&amp;</B> in the replacement string.
Backslash can also be used to escape a backslash; <B>\\</B> results in
a literal backslash in the replacement.
Users should take care if <I>string</I> is double-quoted to avoid
unwanted interactions between the backslash and double-quoting, since
backslash has special meaning within double quotes.
Pattern substitution performs the check for unquoted <B>&amp;</B> after
expanding <I>string</I>;
shell programmers should quote any occurrences of <B>&amp;</B>
they want to be taken literally in the replacement
and ensure any instances of <B>&amp;</B> they want to be replaced are unquoted.
<P>
If the
<B>nocasematch</B>

shell option is enabled, the match is performed without regard to the case
of alphabetic characters.
If
<I>parameter</I>

is
<B>@</B>

or
<B>*</B>,

the substitution operation is applied to each positional
parameter in turn, and the expansion is the resultant list.
If
<I>parameter</I>

is an array variable subscripted with
<B>@</B>

or
<B>*</B>,

the substitution operation is applied to each member of the
array in turn, and the expansion is the resultant list.
<DT>${<I>parameter</I><B>^</B><I>pattern</I>}<DD>

<DT>${<I>parameter</I><B>^^</B><I>pattern</I>}<DD>
<DT>${<I>parameter</I><B>,</B><I>pattern</I>}<DD>
<DT>${<I>parameter</I><B>,,</B><I>pattern</I>}<DD>

<B>Case modification</B>.
This expansion modifies the case of alphabetic characters in <I>parameter</I>.
The <I>pattern</I> is expanded to produce a pattern just as in
pathname expansion.
Each character in the expanded value of <I>parameter</I> is tested against
<I>pattern</I>, and, if it matches the pattern, its case is converted.
The pattern should not attempt to match more than one character.
The <B>^</B> operator converts lowercase letters matching <I>pattern</I>
to uppercase; the <B>,</B> operator converts matching uppercase letters
to lowercase.
The <B>^^</B> and <B>,,</B> expansions convert each matched character in the
expanded value; the <B>^</B> and <B>,</B> expansions match and convert only
the first character in the expanded value.
If <I>pattern</I> is omitted, it is treated like a <B>?</B>, which matches
every character.
If
<I>parameter</I>

is
<B>@</B>

or
<B>*</B>,

the case modification operation is applied to each positional
parameter in turn, and the expansion is the resultant list.
If
<I>parameter</I>

is an array variable subscripted with
<B>@</B>

or
<B>*</B>,

the case modification operation is applied to each member of the
array in turn, and the expansion is the resultant list.
<DT>${<I>parameter</I><B>@</B><I>operator</I>}<DD>
<B>Parameter transformation</B>.
The expansion is either a transformation of the value of <I>parameter</I>
or information about <I>parameter</I> itself, depending on the value of
<I>operator</I>.  Each <I>operator</I> is a single letter:
<P>
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>U</B>

<DD>
The expansion is a string that is the value of <I>parameter</I> with lowercase
alphabetic characters converted to uppercase.
<DT><B>u</B>

<DD>
The expansion is a string that is the value of <I>parameter</I> with the first
character converted to uppercase, if it is alphabetic.
<DT><B>L</B>

<DD>
The expansion is a string that is the value of <I>parameter</I> with uppercase
alphabetic characters converted to lowercase.
<DT><B>Q</B>

<DD>
The expansion is a string that is the value of <I>parameter</I> quoted in a
format that can be reused as input.
<DT><B>E</B>

<DD>
The expansion is a string that is the value of <I>parameter</I> with backslash
escape sequences expanded as with the <B>$aq...aq</B> quoting mechanism.
<DT><B>P</B>

<DD>
The expansion is a string that is the result of expanding the value of
<I>parameter</I> as if it were a prompt string (see <B>PROMPTING</B> below).
<DT><B>A</B>

<DD>
The expansion is a string in the form of
an assignment statement or <B>declare</B> command that, if
evaluated, will recreate <I>parameter</I> with its attributes and value.
<DT><B>K</B>

<DD>
Produces a possibly-quoted version of the value of <I>parameter</I>,
except that it prints the values of
indexed and associative arrays as a sequence of quoted key-value pairs
(see <B>Arrays</B> above).
<DT><B>a</B>

<DD>
The expansion is a string consisting of flag values representing
<I>parameter</I>'s attributes.
<DT><B>k</B>

<DD>
Like the K transformation, but expands the keys and values of
indexed and associative arrays to separate words after word splitting.

</DL>
<P>

If
<I>parameter</I>

is
<B>@</B>

or
<B>*</B>,

the operation is applied to each positional
parameter in turn, and the expansion is the resultant list.
If
<I>parameter</I>

is an array variable subscripted with
<B>@</B>

or
<B>*</B>,

the operation is applied to each member of the
array in turn, and the expansion is the resultant list.
<P>
The result of the expansion is subject to word splitting and pathname
expansion as described below.
</DL>

</DL>
<A NAME="lbBC">&nbsp;</A>
<H4>Command Substitution</H4>

<I>Command substitution</I> allows the output of a command to replace
the command name.  There are two forms:
<DL COMPACT><DT><DD>
<P>

<B>$(</B><I>command</I><B>)</B>
</DL>

or
<DL COMPACT><DT><DD>
<B>`</B><I>command</I><B>`</B>
</DL>

<P>

<B>Bash</B>

performs the expansion by executing <I>command</I> in a subshell environment
and replacing the command substitution with the standard output of the
command, with any trailing newlines deleted.
Embedded newlines are not deleted, but they may be removed during
word splitting.
The command substitution <B>$(cat </B><I>file</I>) can be replaced by
the equivalent but faster <B>$(&lt; </B><I>file</I>).
<P>

When the old-style backquote form of substitution is used,
backslash retains its literal meaning except when followed by
<B>$</B>,

<B>`</B>,

or
<B>\</B>.

The first backquote not preceded by a backslash terminates the
command substitution.
When using the $(<I>command</I>) form, all characters between the
parentheses make up the command; none are treated specially.
<P>

Command substitutions may be nested.  To nest when using the backquoted form,
escape the inner backquotes with backslashes.
<P>

If the substitution appears within double quotes, word splitting and
pathname expansion are not performed on the results.
<A NAME="lbBD">&nbsp;</A>
<H4>Arithmetic Expansion</H4>

Arithmetic expansion allows the evaluation of an arithmetic expression
and the substitution of the result.  The format for arithmetic expansion is:
<DL COMPACT><DT><DD>
<P>

<B>$((</B><I>expression</I><B>))</B>
</DL>

<P>

The
<I>expression</I>

undergoes the same expansions
as if it were within double quotes,
but double quote characters in <I>expression</I> are not treated specially
and are removed.
All tokens in the expression undergo parameter and variable expansion,
command substitution, and quote removal.
The result is treated as the arithmetic expression to be evaluated.
Arithmetic expansions may be nested.
<P>

The evaluation is performed according to the rules listed below under
<FONT SIZE=-1><B>ARITHMETIC EVALUATION</B>.

</FONT>
If
<I>expression</I>

is invalid,
<B>bash</B>

prints a message indicating failure and no substitution occurs.
<A NAME="lbBE">&nbsp;</A>
<H4>Process Substitution</H4>

<I>Process substitution</I> allows a process's input or output to be
referred to using a filename.
It takes the form of
<B>&lt;(</B><I>list</I><B>)</B>
or
<B>&gt;(</B><I>list</I><B>)</B>.
The process <I>list</I> is run asynchronously, and its input or output
appears as a filename.
This filename is
passed as an argument to the current command as the result of the
expansion.
If the <B>&gt;(</B><I>list</I><B>)</B> form is used, writing to
the file will provide input for <I>list</I>.  If the
<B>&lt;(</B><I>list</I><B>)</B> form is used, the file passed as an
argument should be read to obtain the output of <I>list</I>.
Process substitution is supported on systems that support named
pipes (<I>FIFOs</I>) or the <B>/dev/fd</B> method of naming open files.
<P>

When available, process substitution is performed
simultaneously with parameter and variable expansion,
command substitution,
and arithmetic expansion.
<A NAME="lbBF">&nbsp;</A>
<H4>Word Splitting</H4>

The shell scans the results of
parameter expansion,
command substitution,
and
arithmetic expansion
that did not occur within double quotes for
<I>word splitting</I>.

<P>

The shell treats each character of
<FONT SIZE=-1><B>IFS</B>

</FONT>
as a delimiter, and splits the results of the other
expansions into words using these characters as field terminators.
If
<FONT SIZE=-1><B>IFS</B>

</FONT>
is unset, or its
value is exactly
<B>&lt;space&gt;&lt;tab&gt;&lt;newline&gt;</B>,

the default, then
sequences of
<B>&lt;space&gt;</B>,

<B>&lt;tab&gt;</B>,

and
<B>&lt;newline&gt;</B>

at the beginning and end of the results of the previous
expansions are ignored, and
any sequence of
<FONT SIZE=-1><B>IFS</B>

</FONT>
characters not at the beginning or end serves to delimit words.
If
<FONT SIZE=-1><B>IFS</B>

</FONT>
has a value other than the default, then sequences of
the whitespace characters
<B>space</B>,

<B>tab</B>,

and
<B>newline</B>

are ignored at the beginning and end of the
word, as long as the whitespace character is in the
value of
<FONT SIZE=-1><B>IFS</B>

</FONT>
(an
<FONT SIZE=-1><B>IFS</B>

</FONT>
whitespace character).
Any character in
<FONT SIZE=-1><B>IFS</B>

</FONT>
that is not
<FONT SIZE=-1><B>IFS</B>

</FONT>
whitespace, along with any adjacent
<FONT SIZE=-1><B>IFS</B>

</FONT>
whitespace characters, delimits a field.
A sequence of
<FONT SIZE=-1><B>IFS</B>

</FONT>
whitespace characters is also treated as a delimiter.
If the value of
<FONT SIZE=-1><B>IFS</B>

</FONT>
is null, no word splitting occurs.
<P>

Explicit null arguments (<B>&quot;&quot;</B> or <B>aqaq</B>) are retained
and passed to commands as empty strings.
Unquoted implicit null arguments, resulting from the expansion of
parameters that have no values, are removed.
If a parameter with no value is expanded within double quotes, a
null argument results and is retained
and passed to a command as an empty string.
When a quoted null argument appears as part of a word whose expansion is
non-null, the null argument is removed.
That is, the word
<TT>-daqaq</TT> becomes <TT>-d</TT> after word splitting and
null argument removal.
<P>

Note that if no expansion occurs, no splitting
is performed.
<A NAME="lbBG">&nbsp;</A>
<H4>Pathname Expansion</H4>

After word splitting,
unless the
<B>-f</B>

option has been set,
<B>bash</B>

scans each word for the characters
<B>*</B>,

<B>?</B>,

and
<B>[</B>.

If one of these characters appears, and is not quoted, then the word is
regarded as a
<I>pattern</I>,

and replaced with an alphabetically sorted list of
filenames matching the pattern
(see
<FONT SIZE=-1><B>Pattern Matching</B>

</FONT>
below).
If no matching filenames are found,
and the shell option
<B>nullglob</B>

is not enabled, the word is left unchanged.
If the
<B>nullglob</B>

option is set, and no matches are found,
the word is removed.
If the
<B>failglob</B>

shell option is set, and no matches are found, an error message
is printed and the command is not executed.
If the shell option
<B>nocaseglob</B>

is enabled, the match is performed without regard to the case
of alphabetic characters.
When a pattern is used for pathname expansion,
the character
<B>``.''</B>

at the start of a name or immediately following a slash
must be matched explicitly, unless the shell option
<B>dotglob</B>

is set.
In order to match the filenames
<B>``.''</B>

and
<B>``..''</B>,

the pattern must begin with ``.'' (for example, ``.?''),
even if
<B>dotglob</B>

is set.
If the
<B>globskipdots</B>

shell option is enabled, the filenames
<B>``.''</B>

and
<B>``..''</B>

are never matched, even if the pattern begins with a
<B>``.''</B>.

When not matching pathnames, the
<B>``.''</B>

character is not treated specially.
When matching a pathname, the slash character must always be
matched explicitly by a slash in the pattern, but in other matching
contexts it can be matched by a special pattern character as described
below under
<FONT SIZE=-1><B>Pattern Matching</B>.

</FONT>
See the description of
<B>shopt</B>

below under
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
for a description of the
<B>nocaseglob</B>,

<B>nullglob</B>,

<B>globskipdots</B>,

<B>failglob</B>,

and
<B>dotglob</B>

shell options.
<P>

The
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
shell variable may be used to restrict the set of file names matching a
<I>pattern</I>.

If
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
is set, each matching file name that also matches one of the patterns in
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
is removed from the list of matches.
If the <B>nocaseglob</B> option is set, the matching against the patterns in
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
is performed without regard to case.
The filenames
<B>``.''</B>

and
<B>``..''</B>

are always ignored when
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
is set and not null.  However, setting
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
to a non-null value has the effect of enabling the
<B>dotglob</B>

shell option, so all other filenames beginning with a
<B>``.''</B>

will match.
To get the old behavior of ignoring filenames beginning with a
<B>``.''</B>,

make
<B>``.*''</B>

one of the patterns in
<FONT SIZE=-1><B>GLOBIGNORE</B>.

</FONT>
The
<B>dotglob</B>

option is disabled when
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
is unset.
The pattern matching honors the setting of the <B>extglob</B> shell
option.
<P>

<B>Pattern Matching</B>
<P>

Any character that appears in a pattern, other than the special pattern
characters described below, matches itself.  The NUL character may not
occur in a pattern.  A backslash escapes the following character; the
escaping backslash is discarded when matching.
The special pattern characters must be quoted if
they are to be matched literally.
<P>

The special pattern characters have the following meanings:
<P>


<DL COMPACT><DT><DD>
<DL COMPACT>
<DT><B>*</B>

<DD>
Matches any string, including the null string.
When the <B>globstar</B> shell option is enabled, and <B>*</B> is used in
a pathname expansion context, two adjacent <B>*</B>s used as a single
pattern will match all files and zero or more directories and
subdirectories.
If followed by a <B>/</B>, two adjacent <B>*</B>s will match only directories
and subdirectories.
<DT><B>?</B>

<DD>
Matches any single character.
<DT><B>[...]</B>

<DD>
Matches any one of the enclosed characters.  A pair of characters
separated by a hyphen denotes a
<I>range expression</I>;
any character that falls between those two characters, inclusive,
using the current locale's collating sequence and character set,
is matched.  If the first character following the
<B>[</B>

is a
<B>!</B>

or a
<B>^</B>

then any character not enclosed is matched.
The sorting order of characters in range expressions,
and the characters included in the range,
are determined by
the current locale and the values of the
<FONT SIZE=-1><B>LC_COLLATE</B>

</FONT>
or
<FONT SIZE=-1><B>LC_ALL</B>

</FONT>
shell variables, if set.
To obtain the traditional interpretation of range expressions, where
<B>[a-d]</B>

is equivalent to
<B>[abcd]</B>,

set value of the
<B>LC_ALL</B>

shell variable to
<B>C</B>,

or enable the
<B>globasciiranges</B>

shell option.
A
<B>-</B>

may be matched by including it as the first or last character
in the set.
A
<B>]</B>

may be matched by including it as the first character
in the set.
<BR>

<P>


Within
<B>[</B>

and
<B>]</B>,

<I>character classes</I> can be specified using the syntax
<B>[:</B><I>class</I><B>:]</B>, where <I>class</I> is one of the
following classes defined in the POSIX standard:
</DL>
<P>

<DL COMPACT><DT><DD>
<B>
</B>

alnum   alpha   ascii   blank   cntrl   digit   graph   lower   print   punct   space   upper   word   xdigit
<BR>

A character class matches any character belonging to that class.
The <B>word</B> character class matches letters, digits, and the character _.
<BR>

<P>


Within
<B>[</B>

and
<B>]</B>,

an <I>equivalence class</I> can be specified using the syntax
<B>[=</B><I>c</I><B>=]</B>, which matches all characters with the
same collation weight (as defined by the current locale) as
the character <I>c</I>.
<BR>

<P>


Within
<B>[</B>

and
<B>]</B>,

the syntax <B>[.</B><I>symbol</I><B>.]</B> matches the collating symbol
<I>symbol</I>.
</DL>

</DL>


<P>

If the <B>extglob</B> shell option is enabled using the <B>shopt</B>
builtin, the shell recognizes several extended pattern matching operators.
In the following description, a <I>pattern-list</I> is a list of one
or more patterns separated by a <B>|</B>.
Composite patterns may be formed using one or more of the following
sub-patterns:
<P>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT><B>?(</B><I>pattern-list</I><B>)</B><DD>
Matches zero or one occurrence of the given patterns
<DT><B>*(</B><I>pattern-list</I><B>)</B><DD>
Matches zero or more occurrences of the given patterns
<DT><B>+(</B><I>pattern-list</I><B>)</B><DD>
Matches one or more occurrences of the given patterns
<DT><B>@(</B><I>pattern-list</I><B>)</B><DD>
Matches one of the given patterns
<DT><B>!(</B><I>pattern-list</I><B>)</B><DD>
Matches anything except one of the given patterns
</DL></DL>


<P>

The<B>extglob</B> option changes the behavior of the parser, since the
parentheses are normally treated as operators with syntactic meaning.
To ensure that extended matching patterns are parsed correctly, make sure
that <B>extglob</B> is enabled before parsing constructs containing the
patterns, including shell functions and command substitutions.
<P>

When matching filenames, the <B>dotglob</B> shell option determines
the set of filenames that are tested:
when <B>dotglob</B> is enabled, the set of filenames includes all files
beginning with ``.'', but ``.'' and ``..'' must be matched by a
pattern or sub-pattern that begins with a dot;
when it is disabled, the set does not
include any filenames beginning with ``.'' unless the pattern
or sub-pattern begins with a ``.''.
As above, ``.'' only has a special meaning when matching filenames.
<P>

Complicated extended pattern matching against long strings is slow,
especially when the patterns contain alternations and the strings
contain multiple matches.
Using separate matches against shorter strings, or using arrays of
strings instead of a single long string, may be faster.
<A NAME="lbBH">&nbsp;</A>
<H4>Quote Removal</H4>

After the preceding expansions, all unquoted occurrences of the
characters
<B>\</B>,

<B>aq</B>,

and <B>&quot;</B> that did not result from one of the above
expansions are removed.
<A NAME="lbBI">&nbsp;</A>
<H3>REDIRECTION</H3>

Before a command is executed, its input and output
may be
<I>redirected</I>

using a special notation interpreted by the shell.
<I>Redirection</I> allows commands' file handles to be
duplicated, opened, closed,
made to refer to different files,
and can change the files the command reads from and writes to.
Redirection may also be used to modify file handles in the
current shell execution environment.
The following redirection
operators may precede or appear anywhere within a
<I>simple command</I>

or may follow a
<I>command</I>.

Redirections are processed in the order they appear, from
left to right.
<P>

Each redirection that may be preceded by a file descriptor number
may instead be preceded by a word of the form {<I>varname</I>}.
In this case, for each redirection operator except
&gt;&amp;- and &lt;&amp;-, the shell will allocate a file descriptor greater
than or equal to 10 and assign it to <I>varname</I>.
If &gt;&amp;- or &lt;&amp;- is preceded
by {<I>varname</I>}, the value of <I>varname</I> defines the file
descriptor to close.
If {<I>varname</I>} is supplied, the redirection persists beyond
the scope of the command, allowing the shell programmer to manage
the file descriptor's lifetime manually.
The <B>varredir_close</B> shell option manages this behavior.
<P>

In the following descriptions, if the file descriptor number is
omitted, and the first character of the redirection operator is
<B>&lt;</B>,

the redirection refers to the standard input (file descriptor
0).  If the first character of the redirection operator is
<B>&gt;</B>,

the redirection refers to the standard output (file descriptor
1).
<P>

The word following the redirection operator in the following
descriptions, unless otherwise noted, is subjected to
brace expansion, tilde expansion, parameter and variable expansion,
command substitution, arithmetic expansion, quote removal,
pathname expansion, and word splitting.
If it expands to more than one word,
<B>bash</B>

reports an error.
<P>

Note that the order of redirections is significant.  For example,
the command
<DL COMPACT><DT><DD>
<P>

ls <B>&gt;</B> dirlist 2<B>&gt;&amp;</B>1
</DL>

<P>

directs both standard output and standard error to the file
<I>dirlist</I>,

while the command
<DL COMPACT><DT><DD>
<P>

ls 2<B>&gt;&amp;</B>1 <B>&gt;</B> dirlist
</DL>

<P>

directs only the standard output to file
<I>dirlist</I>,

because the standard error was duplicated from the standard output
before the standard output was redirected to
<I>dirlist</I>.

<P>

<B>Bash</B> handles several filenames specially when they are used in
redirections, as described in the following table.
If the operating system on which <B>bash</B> is running provides these
special files, bash will use them; otherwise it will emulate them
internally with the behavior described below.
<DL COMPACT><DT><DD>
<P>


<DL COMPACT>
<DT><B>/dev/fd/</B><I>fd</I>

<DD>
If <I>fd</I> is a valid integer, file descriptor <I>fd</I> is duplicated.
<DT><B>/dev/stdin</B>

<DD>
File descriptor 0 is duplicated.
<DT><B>/dev/stdout</B>

<DD>
File descriptor 1 is duplicated.
<DT><B>/dev/stderr</B>

<DD>
File descriptor 2 is duplicated.
<DT><B>/dev/tcp/</B><I>host</I>/<I>port</I>

<DD>
If <I>host</I> is a valid hostname or Internet address, and <I>port</I>
is an integer port number or service name, <B>bash</B> attempts to open
the corresponding TCP socket.
<DT><B>/dev/udp/</B><I>host</I>/<I>port</I>

<DD>
If <I>host</I> is a valid hostname or Internet address, and <I>port</I>
is an integer port number or service name, <B>bash</B> attempts to open
the corresponding UDP socket.

</DL></DL>

<P>

A failure to open or create a file causes the redirection to fail.
<P>

Redirections using file descriptors greater than 9 should be used with
care, as they may conflict with file descriptors the shell uses
internally.
<A NAME="lbBJ">&nbsp;</A>
<H4>Redirecting Input</H4>

Redirection of input causes the file whose name results from
the expansion of
<I>word</I>

to be opened for reading on file descriptor
<I>n</I>,

or the standard input (file descriptor 0) if
<I>n</I>

is not specified.
<P>

The general format for redirecting input is:
<DL COMPACT><DT><DD>
<P>

[<I>n</I>]<B>&lt;</B><I>word</I>
</DL>

<A NAME="lbBK">&nbsp;</A>
<H4>Redirecting Output</H4>

Redirection of output causes the file whose name results from
the expansion of
<I>word</I>

to be opened for writing on file descriptor
<I>n</I>,

or the standard output (file descriptor 1) if
<I>n</I>

is not specified.  If the file does not exist it is created;
if it does exist it is truncated to zero size.
<P>

The general format for redirecting output is:
<DL COMPACT><DT><DD>
<P>

[<I>n</I>]<B>&gt;</B><I>word</I>
</DL>

<P>

If the redirection operator is
<B>&gt;</B>,

and the
<B>noclobber</B>

option to the
<B>set</B>

builtin has been enabled, the redirection will fail if the file
whose name results from the expansion of <I>word</I> exists and is
a regular file.
If the redirection operator is
<B>&gt;|</B>,

or the redirection operator is
<B>&gt;</B>

and the
<B>noclobber</B>

option to the
<B>set</B>

builtin command is not enabled, the redirection is attempted even
if the file named by <I>word</I> exists.
<A NAME="lbBL">&nbsp;</A>
<H4>Appending Redirected Output</H4>

Redirection of output in this fashion
causes the file whose name results from
the expansion of
<I>word</I>

to be opened for appending on file descriptor
<I>n</I>,

or the standard output (file descriptor 1) if
<I>n</I>

is not specified.  If the file does not exist it is created.
<P>

The general format for appending output is:
<DL COMPACT><DT><DD>
<P>

[<I>n</I>]<B>&gt;&gt;</B><I>word</I>
</DL>

<A NAME="lbBM">&nbsp;</A>
<H4>Redirecting Standard Output and Standard Error</H4>

This construct allows both the
standard output (file descriptor 1) and
the standard error output (file descriptor 2)
to be redirected to the file whose name is the
expansion of
<I>word</I>.

<P>

There are two formats for redirecting standard output and
standard error:
<DL COMPACT><DT><DD>
<P>

<B>&amp;&gt;</B><I>word</I>
</DL>

and
<DL COMPACT><DT><DD>
<B>&gt;&amp;</B><I>word</I>
</DL>

<P>

Of the two forms, the first is preferred.
This is semantically equivalent to
<DL COMPACT><DT><DD>
<P>

<B>&gt;</B><I>word</I> 2<B>&gt;&amp;</B>1
</DL>

<P>

When using the second form, <I>word</I> may not expand to a number or
<B>-</B>.  If it does, other redirection operators apply
(see <B>Duplicating File Descriptors</B> below) for compatibility
reasons.
<A NAME="lbBN">&nbsp;</A>
<H4>Appending Standard Output and Standard Error</H4>

This construct allows both the
standard output (file descriptor 1) and
the standard error output (file descriptor 2)
to be appended to the file whose name is the
expansion of
<I>word</I>.

<P>

The format for appending standard output and standard error is:
<DL COMPACT><DT><DD>
<P>

<B>&amp;&gt;&gt;</B><I>word</I>
</DL>

<P>

This is semantically equivalent to
<DL COMPACT><DT><DD>
<P>

<B>&gt;&gt;</B><I>word</I> 2<B>&gt;&amp;</B>1
</DL>

<P>

(see <B>Duplicating File Descriptors</B> below).
<A NAME="lbBO">&nbsp;</A>
<H4>Here Documents</H4>

This type of redirection instructs the shell to read input from the
current source until a line containing only
<I>delimiter</I>

(with no trailing blanks)
is seen.  All of
the lines read up to that point are then used as the standard
input (or file descriptor <I>n</I> if <I>n</I> is specified) for a command.
<P>

The format of here-documents is:
<DL COMPACT><DT><DD>
<P>

<PRE>
[<I>n</I>]<B>&lt;&lt;</B>[<B>-</B>]<I>word</I>
        <I>here-document</I>
<I>delimiter</I>
</PRE>

</DL>

<P>

No parameter and variable expansion, command substitution,
arithmetic expansion, or pathname expansion is performed on
<I>word</I>.

If any part of
<I>word</I>

is quoted, the
<I>delimiter</I>

is the result of quote removal on
<I>word</I>,

and the lines in the here-document are not expanded.
If <I>word</I> is unquoted,
all lines of the here-document are subjected to
parameter expansion, command substitution, and arithmetic expansion,
the character sequence
<B>\&lt;newline&gt;</B>

is ignored, and
<B>\</B>

must be used to quote the characters
<B>\</B>,

<B>$</B>,

and
<B>`</B>.

<P>

If the redirection operator is
<B>&lt;&lt;-</B>,

then all leading tab characters are stripped from input lines and the
line containing
<I>delimiter</I>.

This allows
here-documents within shell scripts to be indented in a
natural fashion.
<A NAME="lbBP">&nbsp;</A>
<H4>Here Strings</H4>

A variant of here documents, the format is:
<DL COMPACT><DT><DD>
<P>

<PRE>
[<I>n</I>]<B>&lt;&lt;&lt;</B><I>word</I>
</PRE>

</DL>

<P>

The <I>word</I> undergoes
tilde expansion, parameter and variable expansion,
command substitution, arithmetic expansion, and quote removal.
Pathname expansion and word splitting are not performed.
The result is supplied as a single string, with a newline appended,
to the command on its
standard input (or file descriptor <I>n</I> if <I>n</I> is specified).
<A NAME="lbBQ">&nbsp;</A>
<H4>Duplicating File Descriptors</H4>

The redirection operator
<DL COMPACT><DT><DD>
<P>

[<I>n</I>]<B>&lt;&amp;</B><I>word</I>
</DL>

<P>

is used to duplicate input file descriptors.
If
<I>word</I>

expands to one or more digits, the file descriptor denoted by
<I>n</I>

is made to be a copy of that file descriptor.
If the digits in
<I>word</I>

do not specify a file descriptor open for input, a redirection error occurs.
If
<I>word</I>

evaluates to
<B>-</B>,

file descriptor
<I>n</I>

is closed.  If
<I>n</I>

is not specified, the standard input (file descriptor 0) is used.
<P>

The operator
<DL COMPACT><DT><DD>
<P>

[<I>n</I>]<B>&gt;&amp;</B><I>word</I>
</DL>

<P>

is used similarly to duplicate output file descriptors.  If
<I>n</I>

is not specified, the standard output (file descriptor 1) is used.
If the digits in
<I>word</I>

do not specify a file descriptor open for output, a redirection error occurs.
If
<I>word</I>

evaluates to
<B>-</B>,

file descriptor
<I>n</I>

is closed.
As a special case, if <I>n</I> is omitted, and <I>word</I> does not
expand to one or more digits or <B>-</B>, the standard output and standard
error are redirected as described previously.
<A NAME="lbBR">&nbsp;</A>
<H4>Moving File Descriptors</H4>

The redirection operator
<DL COMPACT><DT><DD>
<P>

[<I>n</I>]<B>&lt;&amp;</B><I>digit</I><B>-</B>
</DL>

<P>

moves the file descriptor <I>digit</I> to file descriptor
<I>n</I>,

or the standard input (file descriptor 0) if <I>n</I> is not specified.
<I>digit</I> is closed after being duplicated to <I>n</I>.
<P>

Similarly, the redirection operator
<DL COMPACT><DT><DD>
<P>

[<I>n</I>]<B>&gt;&amp;</B><I>digit</I><B>-</B>
</DL>

<P>

moves the file descriptor <I>digit</I> to file descriptor
<I>n</I>,

or the standard output (file descriptor 1) if <I>n</I> is not specified.
<A NAME="lbBS">&nbsp;</A>
<H4>Opening File Descriptors for Reading and Writing</H4>

The redirection operator
<DL COMPACT><DT><DD>
<P>

[<I>n</I>]<B>&lt;&gt;</B><I>word</I>
</DL>

<P>

causes the file whose name is the expansion of
<I>word</I>

to be opened for both reading and writing on file descriptor
<I>n</I>,

or on file descriptor 0 if
<I>n</I>

is not specified.  If the file does not exist, it is created.
<A NAME="lbBT">&nbsp;</A>
<H3>ALIASES</H3>

<I>Aliases</I> allow a string to be substituted for a word when it is used
as the first word of a simple command.
The shell maintains a list of aliases that may be set and unset with the
<B>alias</B>

and
<B>unalias</B>

builtin commands (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
The first word of each simple command, if unquoted,
is checked to see if it has an
alias.  If so, that word is replaced by the text of the alias.
The characters <B>/</B>, <B>$</B>, <B>`</B>, and <B>=</B> and
any of the shell <I>metacharacters</I> or quoting characters
listed above may not appear in an alias name.
The replacement text may contain any valid shell input,
including shell metacharacters.
The first word of the replacement text is tested
for aliases, but a word that is identical to an alias being expanded
is not expanded a second time.
This means that one may alias
<B>ls</B>

to
<B>ls -F</B>,

for instance, and
<B>bash</B>

does not try to recursively expand the replacement text.
If the last character of the alias value is a
<I>blank</I>,

then the next command
word following the alias is also checked for alias expansion.
<P>

Aliases are created and listed with the
<B>alias</B>

command, and removed with the
<B>unalias</B>

command.
<P>

There is no mechanism for using arguments in the replacement text.
If arguments are needed, use a shell function (see
<FONT SIZE=-1><B>FUNCTIONS</B>

</FONT>
below).
<P>

Aliases are not expanded when the shell is not interactive, unless
the
<B>expand_aliases</B>

shell option is set using
<B>shopt</B>

(see the description of
<B>shopt</B>

under
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B></FONT>
below).
<P>

The rules concerning the definition and use of aliases are
somewhat confusing.
<B>Bash</B>

always reads at least one complete line of input,
and all lines that make up a compound command,
before executing any of the commands on that line or the compound command.
Aliases are expanded when a
command is read, not when it is executed.  Therefore, an
alias definition appearing on the same line as another
command does not take effect until the next line of input is read.
The commands following the alias definition
on that line are not affected by the new alias.
This behavior is also an issue when functions are executed.
Aliases are expanded when a function definition is read,
not when the function is executed, because a function definition
is itself a command.  As a consequence, aliases
defined in a function are not available until after that
function is executed.  To be safe, always put
alias definitions on a separate line, and do not use
<B>alias</B>

in compound commands.
<P>

For almost every purpose, aliases are superseded by
shell functions.
<A NAME="lbBU">&nbsp;</A>
<H3>FUNCTIONS</H3>

A shell function, defined as described above under
<FONT SIZE=-1><B>SHELL GRAMMAR</B>,

</FONT>
stores a series of commands for later execution.
When the name of a shell function is used as a simple command name,
the list of commands associated with that function name is executed.
Functions are executed in the context of the
current shell; no new process is created to interpret
them (contrast this with the execution of a shell script).
When a function is executed, the arguments to the
function become the positional parameters
during its execution.
The special parameter
<B>#</B>

is updated to reflect the change.  Special parameter <B>0</B>
is unchanged.
The first element of the
<FONT SIZE=-1><B>FUNCNAME</B>

</FONT>
variable is set to the name of the function while the function
is executing.
<P>

All other aspects of the shell execution
environment are identical between a function and its caller
with these exceptions: the
<FONT SIZE=-1><B>DEBUG</B>

</FONT>
and
<B>RETURN</B>

traps (see the description of the
<B>trap</B>

builtin under
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below) are not inherited unless the function has been given the
<B>trace</B> attribute (see the description of the
<FONT SIZE=-1><B>declare</B>

</FONT>
builtin below) or the
<B>-o functrace</B> shell option has been enabled with
the <B>set</B> builtin
(in which case all functions inherit the <B>DEBUG</B> and <B>RETURN</B> traps),
and the
<FONT SIZE=-1><B>ERR</B>

</FONT>
trap is not inherited unless the <B>-o errtrace</B> shell option has
been enabled.
<P>

Variables local to the function may be declared with the
<B>local</B>

builtin command (<I>local variables</I>).
Ordinarily, variables and their values
are shared between the function and its caller.
If a variable is declared <B>local</B>, the variable's visible scope
is restricted to that function and its children (including the functions
it calls).
<P>

In the following description, the <I>current scope</I> is a currently-
executing function.
Previous scopes consist of that function's caller and so on,
back to the &quot;global&quot; scope, where the shell is not executing
any shell function.
Consequently, a local variable at the current scope is a variable
declared using the <B>local</B> or <B>declare</B> builtins in the
function that is currently executing.
<P>

Local variables &quot;shadow&quot; variables with the same name declared at
previous scopes.
For instance, a local variable declared in a function
hides a global variable of the same name: references and assignments
refer to the local variable, leaving the global variable unmodified.
When the function returns, the global variable is once again visible.
<P>

The shell uses <I>dynamic scoping</I> to control a variable's visibility
within functions.
With dynamic scoping, visible variables and their values
are a result of the sequence of function calls that caused execution
to reach the current function.
The value of a variable that a function sees depends
on its value within its caller, if any, whether that caller is
the &quot;global&quot; scope or another shell function.
This is also the value that a local variable
declaration &quot;shadows&quot;, and the value that is restored when the function
returns.
<P>

For example, if a variable <I>var</I> is declared as local in function
<I>func1</I>, and <I>func1</I> calls another function <I>func2</I>,
references to <I>var</I> made from within <I>func2</I> will resolve to the
local variable <I>var</I> from <I>func1</I>, shadowing any global variable
named <I>var</I>.
<P>

The <B>unset</B> builtin also acts using the same dynamic scope: if a
variable is local to the current scope, <B>unset</B> will unset it;
otherwise the unset will refer to the variable found in any calling scope
as described above.
If a variable at the current local scope is unset, it will remain so
(appearing as unset)
until it is reset in that scope or until the function returns.
Once the function returns, any instance of the variable at a previous
scope will become visible.
If the unset acts on a variable at a previous scope, any instance of a
variable with that name that had been shadowed will become visible
(see below how the <B>localvar_unset</B> shell option changes this behavior).
<P>

The <B>FUNCNEST</B> variable, if set to a numeric value greater
than 0, defines a maximum function nesting level.  Function
invocations that exceed the limit cause the entire command to
abort.
<P>

If the builtin command
<B>return</B>

is executed in a function, the function completes and
execution resumes with the next command after the function
call.
Any command associated with the <B>RETURN</B> trap is executed
before execution resumes.
When a function completes, the values of the
positional parameters and the special parameter
<B>#</B>

are restored to the values they had prior to the function's
execution.
<P>

Function names and definitions may be listed with the
<B>-f</B>

option to the
<B>declare</B>

or
<B>typeset</B>

builtin commands.  The
<B>-F</B>

option to
<B>declare</B>

or
<B>typeset</B>

will list the function names only
(and optionally the source file and line number, if the <B>extdebug</B>
shell option is enabled).
Functions may be exported so that child shell processes
(those created when executing a separate shell invocation)
automatically have them defined with the
<B>-f</B>

option to the
<B>export</B>

builtin.
A function definition may be deleted using the <B>-f</B> option to
the
<B>unset</B>

builtin.
<P>

Functions may be recursive.
The <B>FUNCNEST</B> variable may be used to limit the depth of the
function call stack and restrict the number of function invocations.
By default, no limit is imposed on the number of recursive calls.
<A NAME="lbBV">&nbsp;</A>
<H3>ARITHMETIC EVALUATION</H3>

The shell allows arithmetic expressions to be evaluated, under
certain circumstances (see the <B>let</B> and <B>declare</B> builtin
commands, the <B>((</B> compound command, and <B>Arithmetic Expansion</B>).
Evaluation is done in fixed-width integers with no check for overflow,
though division by 0 is trapped and flagged as an error.
The operators and their precedence, associativity, and values
are the same as in the C language.
The following list of operators is grouped into levels of
equal-precedence operators.
The levels are listed in order of decreasing precedence.
<P>


<DL COMPACT>
<DT><B></B><I>id</I>++ <I>id</I>--

<DD>
variable post-increment and post-decrement
<DT><B>- +</B>

<DD>
unary minus and plus
<DT><B>++</B><I>id</I> --<I>id</I>

<DD>
variable pre-increment and pre-decrement
<DT><B>! ~</B>

<DD>
logical and bitwise negation
<DT><B>**</B>

<DD>
exponentiation
<DT><B>* / %</B>

<DD>
multiplication, division, remainder
<DT><B>+ -</B>

<DD>
addition, subtraction
<DT><B>&lt;&lt; &gt;&gt;</B>

<DD>
left and right bitwise shifts
<DT><B>&lt;= &gt;= &lt; &gt;</B>

<DD>
comparison
<DT><B>== !=</B>

<DD>
equality and inequality
<DT><B>&amp;</B>

<DD>
bitwise AND
<DT><B>^</B>

<DD>
bitwise exclusive OR
<DT><B>|</B>

<DD>
bitwise OR
<DT><B>&amp;&amp;</B>

<DD>
logical AND
<DT><B>||</B>

<DD>
logical OR
<DT><B></B><I>expr</I>?<I>expr</I>:<I>expr</I>

<DD>
conditional operator
<DT><B>= *= /= %= += -= &lt;&lt;= &gt;&gt;= &amp;= ^= |=</B>

<DD>
assignment
<DT><B></B><I>expr1</I> , <I>expr2</I>

<DD>
comma

</DL>
<P>

Shell variables are allowed as operands; parameter expansion is
performed before the expression is evaluated.
Within an expression, shell variables may also be referenced by name
without using the parameter expansion syntax.
A shell variable that is null or unset evaluates to 0 when referenced
by name without using the parameter expansion syntax.
The value of a variable is evaluated as an arithmetic expression
when it is referenced, or when a variable which has been given the
<I>integer</I> attribute using <B>declare -i</B> is assigned a value.
A null value evaluates to 0.
A shell variable need not have its <I>integer</I> attribute
turned on to be used in an expression.
<P>

Integer constants follow the C language definition, without suffixes or
character constants.
Constants with a leading 0 are interpreted as octal numbers.
A leading 0x or 0X denotes hexadecimal.
Otherwise, numbers take the form [<I>base#</I>]n, where the optional <I>base</I>
is a decimal number between 2 and 64 representing the arithmetic
base, and <I>n</I> is a number in that base.
If <I>base#</I> is omitted, then base 10 is used.
When specifying <I>n</I>,
if a non-digit is required,
the digits greater than 9 are represented by the lowercase letters,
the uppercase letters, @, and _, in that order.
If <I>base</I> is less than or equal to 36, lowercase and uppercase
letters may be used interchangeably to represent numbers between 10
and 35.
<P>

Operators are evaluated in order of precedence.  Sub-expressions in
parentheses are evaluated first and may override the precedence
rules above.
<A NAME="lbBW">&nbsp;</A>
<H3>CONDITIONAL EXPRESSIONS</H3>

Conditional expressions are used by the <B>[[</B> compound command and
the <B>test</B> and <B>[</B> builtin commands to test file attributes
and perform string and arithmetic comparisons.
The <B>test</B> and <B>[</B> commands determine their behavior based on
the number of arguments; see the descriptions of those commands for any
other command-specific actions.
<P>

Expressions are formed from the following unary or binary primaries.
<B>Bash</B> handles several filenames specially when they are used in
expressions.
If the operating system on which <B>bash</B> is running provides these
special files, bash will use them; otherwise it will emulate them
internally with this behavior:
If any <I>file</I> argument to one of the primaries is of the form
<I>/dev/fd/n</I>, then file descriptor <I>n</I> is checked.
If the <I>file</I> argument to one of the primaries is one of
<I>/dev/stdin</I>, <I>/dev/stdout</I>, or <I>/dev/stderr</I>, file
descriptor 0, 1, or 2, respectively, is checked.
<P>

Unless otherwise specified, primaries that operate on files follow symbolic
links and operate on the target of the link, rather than the link itself.
<P>


When used with <B>[[</B>, the <B>&lt;</B> and <B>&gt;</B> operators sort
lexicographically using the current locale.
The <B>test</B> command sorts using ASCII ordering.
<P>

<DL COMPACT>
<DT><B>-a </B><I>file</I>

<DD>
True if <I>file</I> exists.
<DT><B>-b </B><I>file</I>

<DD>
True if <I>file</I> exists and is a block special file.
<DT><B>-c </B><I>file</I>

<DD>
True if <I>file</I> exists and is a character special file.
<DT><B>-d </B><I>file</I>

<DD>
True if <I>file</I> exists and is a directory.
<DT><B>-e </B><I>file</I>

<DD>
True if <I>file</I> exists.
<DT><B>-f </B><I>file</I>

<DD>
True if <I>file</I> exists and is a regular file.
<DT><B>-g </B><I>file</I>

<DD>
True if <I>file</I> exists and is set-group-id.
<DT><B>-h </B><I>file</I>

<DD>
True if <I>file</I> exists and is a symbolic link.
<DT><B>-k </B><I>file</I>

<DD>
True if <I>file</I> exists and its ``sticky'' bit is set.
<DT><B>-p </B><I>file</I>

<DD>
True if <I>file</I> exists and is a named pipe (FIFO).
<DT><B>-r </B><I>file</I>

<DD>
True if <I>file</I> exists and is readable.
<DT><B>-s </B><I>file</I>

<DD>
True if <I>file</I> exists and has a size greater than zero.
<DT><B>-t </B><I>fd</I>

<DD>
True if file descriptor
<I>fd</I>

is open and refers to a terminal.
<DT><B>-u </B><I>file</I>

<DD>
True if <I>file</I> exists and its set-user-id bit is set.
<DT><B>-w </B><I>file</I>

<DD>
True if <I>file</I> exists and is writable.
<DT><B>-x </B><I>file</I>

<DD>
True if <I>file</I> exists and is executable.
<DT><B>-G </B><I>file</I>

<DD>
True if <I>file</I> exists and is owned by the effective group id.
<DT><B>-L </B><I>file</I>

<DD>
True if <I>file</I> exists and is a symbolic link.
<DT><B>-N </B><I>file</I>

<DD>
True if <I>file</I> exists and has been modified since it was last read.
<DT><B>-O </B><I>file</I>

<DD>
True if <I>file</I> exists and is owned by the effective user id.
<DT><B>-S </B><I>file</I>

<DD>
True if <I>file</I> exists and is a socket.
<DT><I>file1</I> <B>-ef</B> <I>file2</I><DD>
True if <I>file1</I> and <I>file2</I> refer to the same device and
inode numbers.
<DT><I>file1</I> -<B>nt</B> <I>file2</I><DD>
True if <I>file1</I> is newer (according to modification date) than <I>file2</I>,
or if <I>file1</I> exists and file2 does not.
<DT><I>file1</I> -<B>ot</B> <I>file2</I><DD>
True if <I>file1</I> is older than <I>file2</I>, or if <I>file2</I> exists
and <I>file1</I> does not.
<DT><B>-o </B><I>optname</I>

<DD>
True if the shell option
<I>optname</I>

is enabled.
See the list of options under the description of the
<B>-o</B>

option to the
<B>set</B>

builtin below.
<DT><B>-v </B><I>varname</I>

<DD>
True if the shell variable
<I>varname</I>

is set (has been assigned a value).
<DT><B>-R </B><I>varname</I>

<DD>
True if the shell variable
<I>varname</I>

is set and is a name reference.
<DT><B>-z </B><I>string</I>

<DD>
True if the length of <I>string</I> is zero.
<DT><I>string</I><DD>

<DT><B>-n </B><I>string</I>

<DD>

True if the length of
<I>string</I>

is non-zero.
<DT><I>string1</I> <B>==</B> <I>string2</I><DD>

<DT><I>string1</I> <B>=</B> <I>string2</I><DD>

True if the strings are equal.  <B>=</B> should be used
with the <B>test</B> command for POSIX conformance.
When used with the <B>[[</B> command, this performs pattern matching as
described above (<B>Compound Commands</B>).
<DT><I>string1</I> <B>!=</B> <I>string2</I><DD>
True if the strings are not equal.
<DT><I>string1</I> <B>&lt;</B> <I>string2</I><DD>
True if <I>string1</I> sorts before <I>string2</I> lexicographically.
<DT><I>string1</I> <B>&gt;</B> <I>string2</I><DD>
True if <I>string1</I> sorts after <I>string2</I> lexicographically.
<DT><I>arg1</I> <B>OP</B> <I>arg2</I>

<DD>
<FONT SIZE=-1><B>OP</B>

</FONT>
is one of
<B>-eq</B>,

<B>-ne</B>,

<B>-lt</B>,

<B>-le</B>,

<B>-gt</B>,

or
<B>-ge</B>.

These arithmetic binary operators return true if <I>arg1</I>
is equal to, not equal to, less than, less than or equal to,
greater than, or greater than or equal to <I>arg2</I>, respectively.
<I>Arg1</I>

and
<I>arg2</I>

may be positive or negative integers.
When used with the <B>[[</B> command,
<I>Arg1</I>

and
<I>Arg2</I>

are evaluated as arithmetic expressions (see
<FONT SIZE=-1><B>ARITHMETIC EVALUATION</B>

</FONT>
above).

</DL>
<A NAME="lbBX">&nbsp;</A>
<H3>SIMPLE COMMAND EXPANSION</H3>

When a simple command is executed, the shell performs the following
expansions, assignments, and redirections, from left to right, in
the following order.
<DL COMPACT>
<DT>1.<DD>
The words that the parser has marked as variable assignments (those
preceding the command name) and redirections are saved for later
processing.
<DT>2.<DD>
The words that are not variable assignments or redirections are
expanded.  If any words remain after expansion, the first word
is taken to be the name of the command and the remaining words are
the arguments.
<DT>3.<DD>
Redirections are performed as described above under
<FONT SIZE=-1><B>REDIRECTION</B>.

</FONT>
<DT>4.<DD>
The text after the <B>=</B> in each variable assignment undergoes tilde
expansion, parameter expansion, command substitution, arithmetic expansion,
and quote removal before being assigned to the variable.
</DL>
<P>

If no command name results, the variable assignments affect the current
shell environment.
In the case of such a command (one that consists only of assignment
statements and redirections), assignment statements are performed before
redirections.
Otherwise, the variables are added to the environment
of the executed command and do not affect the current shell environment.
If any of the assignments attempts to assign a value to a readonly variable,
an error occurs, and the command exits with a non-zero status.
<P>

If no command name results, redirections are performed, but do not
affect the current shell environment.  A redirection error causes the
command to exit with a non-zero status.
<P>

If there is a command name left after expansion, execution proceeds as
described below.  Otherwise, the command exits.  If one of the expansions
contained a command substitution, the exit status of the command is
the exit status of the last command substitution performed.  If there
were no command substitutions, the command exits with a status of zero.
<A NAME="lbBY">&nbsp;</A>
<H3>COMMAND EXECUTION</H3>

After a command has been split into words, if it results in a
simple command and an optional list of arguments, the following
actions are taken.
<P>

If the command name contains no slashes, the shell attempts to
locate it.  If there exists a shell function by that name, that
function is invoked as described above in
<FONT SIZE=-1><B>FUNCTIONS</B>.

</FONT>
If the name does not match a function, the shell searches for
it in the list of shell builtins.  If a match is found, that
builtin is invoked.
<P>

If the name is neither a shell function nor a builtin,
and contains no slashes,
<B>bash</B>

searches each element of the
<FONT SIZE=-1><B>PATH</B>

</FONT>
for a directory containing an executable file by that name.
<B>Bash</B>

uses a hash table to remember the full pathnames of executable
files (see
<B>hash</B>

under
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
A full search of the directories in
<FONT SIZE=-1><B>PATH</B>

</FONT>
is performed only if the command is not found in the hash table.
If the search is unsuccessful, the shell searches for a defined shell
function named <B>command_not_found_handle</B>.
If that function exists, it is invoked in a separate execution environment
with the original command and
the original command's arguments as its arguments, and the function's
exit status becomes the exit status of that subshell.
If that function is not defined, the shell prints an error
message and returns an exit status of 127.
<P>

If the search is successful, or if the command name contains
one or more slashes, the shell executes the named program in a
separate execution environment.
Argument 0 is set to the name given, and the remaining arguments
to the command are set to the arguments given, if any.
<P>

If this execution fails because the file is not in executable
format, and the file is not a directory, it is assumed to be
a <I>shell script</I>, a file
containing shell commands, and the shell creates a
new instance of itself
to execute it.
This subshell reinitializes itself, so
that the effect is as if a new shell had been invoked
to handle the script, with the exception that the locations of
commands remembered by the parent (see
<B>hash</B>

below under
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>)</FONT>
are retained by the child.
<P>

If the program is a file beginning with
<B>#!</B>,

the remainder of the first line specifies an interpreter
for the program.  The shell executes the
specified interpreter on operating systems that do not
handle this executable format themselves.  The arguments to the
interpreter consist of a single optional argument following the
interpreter name on the first line of the program, followed
by the name of the program, followed by the command
arguments, if any.
<A NAME="lbBZ">&nbsp;</A>
<H3>COMMAND EXECUTION ENVIRONMENT</H3>

The shell has an <I>execution environment</I>, which consists of the
following:
<DL COMPACT>
<DT>*<DD>
open files inherited by the shell at invocation, as modified by
redirections supplied to the <B>exec</B> builtin
<DT>*<DD>
the current working directory as set by <B>cd</B>, <B>pushd</B>, or
<B>popd</B>, or inherited by the shell at invocation
<DT>*<DD>
the file creation mode mask as set by <B>umask</B> or inherited from
the shell's parent
<DT>*<DD>
current traps set by <B>trap</B>
<DT>*<DD>
shell parameters that are set by variable assignment or with <B>set</B>
or inherited from the shell's parent in the environment
<DT>*<DD>
shell functions defined during execution or inherited from the shell's
parent in the environment
<DT>*<DD>
options enabled at invocation (either by default or with command-line
arguments) or by <B>set</B>
<DT>*<DD>
options enabled by <B>shopt</B>
<DT>*<DD>
shell aliases defined with <B>alias</B>
<DT>*<DD>
various process IDs, including those of background jobs, the value
of <B>$$</B>, and the value of
<FONT SIZE=-1><B>PPID</B>

</FONT>
</DL>
<P>

When a simple command other than a builtin or shell function
is to be executed, it
is invoked in a separate execution environment that consists of
the following.
Unless otherwise noted, the values are inherited from the shell.

<DL COMPACT>
<DT>*<DD>
the shell's open files, plus any modifications and additions specified
by redirections to the command
<DT>*<DD>
the current working directory
<DT>*<DD>
the file creation mode mask
<DT>*<DD>
shell variables and functions marked for export, along with variables
exported for the command, passed in the environment
<DT>*<DD>
traps caught by the shell are reset to the values inherited from the
shell's parent, and traps ignored by the shell are ignored
</DL>
<P>

A command invoked in this separate environment cannot affect the
shell's execution environment.
<P>

A <I>subshell</I> is a copy of the shell process.
<P>

Command substitution, commands grouped with parentheses,
and asynchronous commands are invoked in a
subshell environment that is a duplicate of the shell environment,
except that traps caught by the shell are reset to the values
that the shell inherited from its parent at invocation.  Builtin
commands that are invoked as part of a pipeline are also executed in a
subshell environment.  Changes made to the subshell environment
cannot affect the shell's execution environment.
<P>

Subshells spawned to execute command substitutions inherit the value of
the <B>-e</B> option from the parent shell.  When not in <I>posix mode</I>,
<B>bash</B> clears the <B>-e</B> option in such subshells.
<P>

If a command is followed by a <B>&amp;</B> and job control is not active, the
default standard input for the command is the empty file <I>/dev/null</I>.
Otherwise, the invoked command inherits the file descriptors of the calling
shell as modified by redirections.
<A NAME="lbCA">&nbsp;</A>
<H3>ENVIRONMENT</H3>

When a program is invoked it is given an array of strings
called the
<I>environment</I>.

This is a list of
<I>name</I>-<I>value</I> pairs, of the form
<I>name</I>=value.

<P>

The shell provides several ways to manipulate the environment.
On invocation, the shell scans its own environment and
creates a parameter for each name found, automatically marking
it for
<I>export</I>

to child processes.  Executed commands inherit the environment.
The
<B>export</B>

and
<B>declare -x</B>

commands allow parameters and functions to be added to and
deleted from the environment.  If the value of a parameter
in the environment is modified, the new value becomes part
of the environment, replacing the old.  The environment
inherited by any executed command consists of the shell's
initial environment, whose values may be modified in the shell,
less any pairs removed by the
<B>unset</B>

command, plus any additions via the
<B>export</B>

and
<B>declare -x</B>

commands.
<P>

The environment for any
<I>simple command</I>

or function may be augmented temporarily by prefixing it with
parameter assignments, as described above in
<FONT SIZE=-1><B>PARAMETERS</B>.

</FONT>
These assignment statements affect only the environment seen
by that command.
<P>

If the
<B>-k</B>

option is set (see the
<B>set</B>

builtin command below), then
<I>all</I>

parameter assignments are placed in the environment for a command,
not just those that precede the command name.
<P>

When
<B>bash</B>

invokes an external command, the variable
<B>_</B>

is set to the full filename of the command and passed to that
command in its environment.
<A NAME="lbCB">&nbsp;</A>
<H3>EXIT STATUS</H3>

The exit status of an executed command is the value returned by the
<I>waitpid</I> system call or equivalent function.  Exit statuses
fall between 0 and 255, though, as explained below, the shell may
use values above 125 specially.  Exit statuses from shell builtins and
compound commands are also limited to this range.  Under certain
circumstances, the shell will use special values to indicate specific
failure modes.
<P>

For the shell's purposes, a command which exits with a
zero exit status has succeeded.  An exit status of zero
indicates success.  A non-zero exit status indicates failure.
When a command terminates on a fatal signal <I>N</I>, <B>bash</B> uses
the value of 128+<I>N</I> as the exit status.
<P>

If a command is not found, the child process created to
execute it returns a status of 127.  If a command is found
but is not executable, the return status is 126.
<P>

If a command fails because of an error during expansion or redirection,
the exit status is greater than zero.
<P>

Shell builtin commands return a status of 0 (<I>true</I>) if
successful, and non-zero (<I>false</I>) if an error occurs
while they execute.
All builtins return an exit status of 2 to indicate incorrect usage,
generally invalid options or missing arguments.
<P>

The exit status of the last command is available in the special
parameter $?.
<P>

<B>Bash</B> itself returns the exit status of the last command
executed, unless a syntax error occurs, in which case it exits
with a non-zero value.  See also the <B>exit</B> builtin
command below.
<A NAME="lbCC">&nbsp;</A>
<H3>SIGNALS</H3>

When <B>bash</B> is interactive, in the absence of any traps, it ignores
<FONT SIZE=-1><B>SIGTERM</B>

</FONT>
(so that <B>kill 0</B> does not kill an interactive shell),
and
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
is caught and handled (so that the <B>wait</B> builtin is interruptible).
In all cases, <B>bash</B> ignores
<FONT SIZE=-1><B>SIGQUIT</B>.

</FONT>
If job control is in effect,
<B>bash</B>

ignores
<FONT SIZE=-1><B>SIGTTIN</B>,

</FONT>
<FONT SIZE=-1><B>SIGTTOU</B>,

</FONT>
and
<FONT SIZE=-1><B>SIGTSTP</B>.

</FONT>
<P>

Non-builtin commands run by <B>bash</B> have signal handlers
set to the values inherited by the shell from its parent.
When job control is not in effect, asynchronous commands
ignore
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
and
<FONT SIZE=-1><B>SIGQUIT</B>

</FONT>
in addition to these inherited handlers.
Commands run as a result of command substitution ignore the
keyboard-generated job control signals
<FONT SIZE=-1><B>SIGTTIN</B>,

</FONT>
<FONT SIZE=-1><B>SIGTTOU</B>,

</FONT>
and
<FONT SIZE=-1><B>SIGTSTP</B>.

</FONT>
<P>

The shell exits by default upon receipt of a
<FONT SIZE=-1><B>SIGHUP</B>.

</FONT>
Before exiting, an interactive shell resends the
<FONT SIZE=-1><B>SIGHUP</B>

</FONT>
to all jobs, running or stopped.
Stopped jobs are sent
<FONT SIZE=-1><B>SIGCONT</B>

</FONT>
to ensure that they receive the
<FONT SIZE=-1><B>SIGHUP</B>.

</FONT>
To prevent the shell from
sending the signal to a particular job, it should be removed from the
jobs table with the
<B>disown</B>

builtin (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below) or marked
to not receive
<FONT SIZE=-1><B>SIGHUP</B>

</FONT>
using
<B>disown -h</B>.

<P>

If the
<B>huponexit</B>

shell option has been set with
<B>shopt</B>,

<B>bash</B>

sends a
<FONT SIZE=-1><B>SIGHUP</B>

</FONT>
to all jobs when an interactive login shell exits.
<P>

If <B>bash</B> is waiting for a command to complete and receives a signal
for which a trap has been set, the trap will not be executed until
the command completes.
When <B>bash</B> is waiting for an asynchronous command via the <B>wait</B>
builtin, the reception of a signal for which a trap has been set will
cause the <B>wait</B> builtin to return immediately with an exit status
greater than 128, immediately after which the trap is executed.
<P>

When job control is not enabled, and <B>bash</B> is waiting for a foreground
command to complete, the shell receives keyboard-generated signals
such as
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
(usually generated by <B>^C</B>) that users commonly intend to send
to that command.
This happens because the shell and the command are in the
same process group as the terminal, and <B>^C</B> sends
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
to all processes in that process group.
<P>

When <B>bash</B> is running without job control enabled and receives 
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
while waiting for a foreground command, it waits until that foreground
command terminates and then decides what to do about the
<FONT SIZE=-1><B>SIGINT</B>:

</FONT>
<DL COMPACT>
<DT>1.<DD>
If the command terminates due to the
<FONT SIZE=-1><B>SIGINT</B>,

</FONT>
<B>bash</B> concludes
that the user meant to end the entire script, and acts on the
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
(e.g., by running a
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
trap or exiting itself);
<DT>2.<DD>
If the command does not terminate due to
<FONT SIZE=-1><B>SIGINT</B>,

</FONT>
the program handled the
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
itself and did not treat it as a fatal signal.
In that case, <B>bash</B> does not treat
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
as a fatal signal, either, instead assuming that the
<FONT SIZE=-1><B>SIGINT</B>

</FONT>
was used as part of the program's normal operation
(e.g., emacs uses it to abort editing
commands) or deliberately discarded.
However, <B>bash</B> will run any
trap set on
<FONT SIZE=-1><B>SIGINT</B>,

</FONT>
as it does with any other trapped signal it
receives while it is waiting for the foreground command to
complete, for compatibility.
</DL>
<A NAME="lbCD">&nbsp;</A>
<H3>JOB CONTROL</H3>

<I>Job control</I>

refers to the ability to selectively stop (<I>suspend</I>)
the execution of processes and continue (<I>resume</I>)
their execution at a later point.  A user typically employs
this facility via an interactive interface supplied jointly
by the operating system kernel's terminal driver and
<B>bash</B>.

<P>

The shell associates a
<I>job</I>

with each pipeline.  It keeps a table of currently executing
jobs, which may be listed with the
<B>jobs</B>

command.  When
<B>bash</B>

starts a job asynchronously (in the
<I>background</I>),

it prints a line that looks like:
<DL COMPACT><DT><DD>
<P>

[1] 25647
</DL>

<P>

indicating that this job is job number 1 and that the process ID
of the last process in the pipeline associated with this job is 25647.
All of the processes in a single pipeline are members of the same job.
<B>Bash</B>

uses the
<I>job</I>

abstraction as the basis for job control.
<P>

To facilitate the implementation of the user interface to job
control, the operating system maintains the notion of a <I>current terminal
process group ID</I>.  Members of this process group (processes whose
process group ID is equal to the current terminal process group ID)
receive keyboard-generated signals such as
<FONT SIZE=-1><B>SIGINT</B>.

</FONT>
These processes are said to be in the
<I>foreground</I>.

<I>Background</I>

processes are those whose process group ID differs from the terminal's;
such processes are immune to keyboard-generated signals.
Only foreground processes are allowed to read from or, if the
user so specifies with <TT>stty tostop</TT>, write to the
terminal.
Background processes which attempt to read from (write to when
<TT>stty tostop</TT> is in effect) the
terminal are sent a
<FONT SIZE=-1><B>SIGTTIN (SIGTTOU)</B>

</FONT>
signal by the kernel's terminal driver,
which, unless caught, suspends the process.
<P>

If the operating system on which
<B>bash</B>

is running supports
job control,
<B>bash</B>

contains facilities to use it.
Typing the
<I>suspend</I>

character (typically
<B>^Z</B>,

Control-Z) while a process is running
causes that process to be stopped and returns control to
<B>bash</B>.

Typing the
<I>delayed suspend</I>

character (typically
<B>^Y</B>,

Control-Y) causes the process to be stopped when it
attempts to read input from the terminal, and control to
be returned to
<B>bash</B>.

The user may then manipulate the state of this job, using the
<B>bg</B>

command to continue it in the background, the
<B>fg</B>

command to continue it in the foreground, or
the
<B>kill</B>

command to kill it.  A <B>^Z</B> takes effect immediately,
and has the additional side effect of causing pending output
and typeahead to be discarded.
<P>

There are a number of ways to refer to a job in the shell.
The character
<B>%</B>

introduces a job specification (<I>jobspec</I>).  Job number
<I>n</I>

may be referred to as
<B>%n</B>.

A job may also be referred to using a prefix of the name used to
start it, or using a substring that appears in its command line.
For example,
<B>%ce</B>

refers to a stopped
job whose command name begins with
<B>ce</B>.

If a prefix matches more than one job,
<B>bash</B>

reports an error.  Using
<B>%?ce</B>,

on the other hand, refers to any job containing the string
<B>ce</B>

in its command line.  If the substring matches more than one job,
<B>bash</B>

reports an error.  The symbols
<B>%%</B>

and
<B>%+</B>

refer to the shell's notion of the
<I>current job</I>,

which is the last job stopped while it was in
the foreground or started in the background.
The
<I>previous job</I>

may be referenced using
<B>%-</B>.

If there is only a single job, <B>%+</B> and <B>%-</B> can both be used
to refer to that job.
In output pertaining to jobs (e.g., the output of the
<B>jobs</B>

command), the current job is always flagged with a
<B>+</B>,

and the previous job with a
<B>-</B>.

A single % (with no accompanying job specification) also refers to the
current job.
<P>

Simply naming a job can be used to bring it into the
foreground:
<B>%1</B>

is a synonym for
<B>``fg %1''</B>,
bringing job 1 from the background into the foreground.
Similarly,
<B>``%1 &amp;''</B>

resumes job 1 in the background, equivalent to
<B>``bg %1''</B>.
<P>

The shell learns immediately whenever a job changes state.
Normally,
<B>bash</B>

waits until it is about to print a prompt before reporting
changes in a job's status so as to not interrupt
any other output.  If the
<B>-b</B>

option to the
<B>set</B>

builtin command
is enabled,
<B>bash</B>

reports such changes immediately.
Any trap on
<FONT SIZE=-1><B>SIGCHLD</B>

</FONT>
is executed for each child that exits.
<P>

If an attempt to exit
<B>bash</B>

is made while jobs are stopped (or, if the <B>checkjobs</B> shell option has
been enabled using the <B>shopt</B> builtin, running), the shell prints a
warning message, and, if the <B>checkjobs</B> option is enabled, lists the
jobs and their statuses.
The
<B>jobs</B>

command may then be used to inspect their status.
If a second attempt to exit is made without an intervening command,
the shell does not print another warning, and any stopped
jobs are terminated.
<P>

When the shell is waiting for a job or process using the <B>wait</B>
builtin, and job control is enabled, <B>wait</B> will return when the
job changes state. The <B>-f</B> option causes <B>wait</B> to wait
until the job or process terminates before returning.
<A NAME="lbCE">&nbsp;</A>
<H3>PROMPTING</H3>

When executing interactively,
<B>bash</B>

displays the primary prompt
<FONT SIZE=-1><B>PS1</B>

</FONT>
when it is ready to read a command, and the secondary prompt
<FONT SIZE=-1><B>PS2</B>

</FONT>
when it needs more input to complete a command.
<B>Bash</B>

displays
<FONT SIZE=-1><B>PS0</B>

</FONT>
after it reads a command but before executing it.
<B>Bash</B>

displays
<FONT SIZE=-1><B>PS4</B>

</FONT>
as described above
before tracing each command when the <B>-x</B> option is enabled.
<B>Bash</B>

allows these prompt strings to be customized by inserting a number of
backslash-escaped special characters that are decoded as follows:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>\a</B>

<DD>
an ASCII bell character (07)
<DT><B>\d</B>

<DD>
the date in &quot;Weekday Month Date&quot; format (e.g., &quot;Tue May 26&quot;)
<DT><B>\D{</B><I>format</I>}

<DD>
the <I>format</I> is passed to <I>strftime</I>(3) and the result is inserted
into the prompt string; an empty <I>format</I> results in a locale-specific
time representation.  The braces are required
<DT><B>\e</B>

<DD>
an ASCII escape character (033)
<DT><B>\h</B>

<DD>
the hostname up to the first `.'
<DT><B>\H</B>

<DD>
the hostname
<DT><B>\j</B>

<DD>
the number of jobs currently managed by the shell
<DT><B>\l</B>

<DD>
the basename of the shell's terminal device name
<DT><B>\n</B>

<DD>
newline
<DT><B>\r</B>

<DD>
carriage return
<DT><B>\s</B>

<DD>
the name of the shell, the basename of
<B>$0</B>

(the portion following the final slash)
<DT><B>\t</B>

<DD>
the current time in 24-hour HH:MM:SS format
<DT><B>\T</B>

<DD>
the current time in 12-hour HH:MM:SS format
<DT><B>\@</B>

<DD>
the current time in 12-hour am/pm format
<DT><B>\A</B>

<DD>
the current time in 24-hour HH:MM format
<DT><B>\u</B>

<DD>
the username of the current user
<DT><B>\v</B>

<DD>
the version of <B>bash</B> (e.g., 2.00)
<DT><B>\V</B>

<DD>
the release of <B>bash</B>, version + patch level (e.g., 2.00.0)
<DT><B>\w</B>

<DD>
the value of the <B>PWD</B> shell variable (<B>$PWD</B>),
with
<FONT SIZE=-1><B>$HOME</B>

</FONT>
abbreviated with a tilde
(uses the value of the
<FONT SIZE=-1><B>PROMPT_DIRTRIM</B>

</FONT>
variable)
<DT><B>\W</B>

<DD>
the basename of <B>$PWD</B>,
with
<FONT SIZE=-1><B>$HOME</B>

</FONT>
abbreviated with a tilde
<DT><B>\!</B>

<DD>
the history number of this command
<DT><B>\#</B>

<DD>
the command number of this command
<DT><B>\$</B>

<DD>
if the effective UID is 0, a
<B>#</B>,

otherwise a
<B>$</B>

<DT><B>\</B><I>nnn</I>

<DD>
the character corresponding to the octal number <I>nnn</I>
<DT><B>\\</B>

<DD>
a backslash
<DT><B>\[</B>

<DD>
begin a sequence of non-printing characters, which could be used to
embed a terminal control sequence into the prompt
<DT><B>\]</B>

<DD>
end a sequence of non-printing characters

</DL></DL>

<P>

The command number and the history number are usually different:
the history number of a command is its position in the history
list, which may include commands restored from the history file
(see
<FONT SIZE=-1><B>HISTORY</B>

</FONT>
below), while the command number is the position in the sequence
of commands executed during the current shell session.
After the string is decoded, it is expanded via
parameter expansion, command substitution, arithmetic
expansion, and quote removal, subject to the value of the
<B>promptvars</B>

shell option (see the description of the
<B>shopt</B>

command under
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
This can have unwanted side effects if escaped portions of the string
appear within command substitution or contain characters special to
word expansion.
<A NAME="lbCF">&nbsp;</A>
<H3>READLINE</H3>

This is the library that handles reading input when using an interactive
shell, unless the
<B>--noediting</B>

option is given at shell invocation.
Line editing is also used when using the <B>-e</B> option to the
<B>read</B> builtin.
By default, the line editing commands are similar to those of Emacs.
A vi-style line editing interface is also available.
Line editing can be enabled at any time using the
<B>-o emacs</B>

or
<B>-o vi</B>

options to the
<B>set</B>

builtin (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
To turn off line editing after the shell is running, use the
<B>+o emacs</B>

or
<B>+o vi</B>

options to the
<B>set</B>

builtin.
<A NAME="lbCG">&nbsp;</A>
<H4>Readline Notation</H4>

In this section, the Emacs-style notation is used to denote
keystrokes.  Control keys are denoted by C-<I>key</I>, e.g., C-n
means Control-N.  Similarly,
<I>meta</I>

keys are denoted by M-<I>key</I>, so M-x means Meta-X.  (On keyboards
without a
<I>meta</I>

key, M-<I>x</I> means ESC <I>x</I>, i.e., press the Escape key
then the
<I>x</I>

key.  This makes ESC the <I>meta prefix</I>.
The combination M-C-<I>x</I> means ESC-Control-<I>x</I>,
or press the Escape key
then hold the Control key while pressing the
<I>x</I>

key.)
<P>

Readline commands may be given numeric
<I>arguments</I>,

which normally act as a repeat count.
Sometimes, however, it is the sign of the argument that is significant.
Passing a negative argument to a command that acts in the forward
direction (e.g., <B>kill-line</B>) causes that command to act in a
backward direction.
Commands whose behavior with arguments deviates from this are noted
below.
<P>

When a command is described as <I>killing</I> text, the text
deleted is saved for possible future retrieval
(<I>yanking</I>).  The killed text is saved in a
<I>kill ring</I>.  Consecutive kills cause the text to be
accumulated into one unit, which can be yanked all at once.
Commands which do not kill text separate the chunks of text
on the kill ring.
<A NAME="lbCH">&nbsp;</A>
<H4>Readline Initialization</H4>

Readline is customized by putting commands in an initialization
file (the <I>inputrc</I> file).
The name of this file is taken from the value of the
<FONT SIZE=-1><B>INPUTRC</B>

</FONT>
variable.  If that variable is unset, the default is
<A HREF="file:~/.inputrc"><I>~/.inputrc</I></A>.

If that file  does not exist or cannot be read, the ultimate default is
<A HREF="file:/etc/inputrc"><I>/etc/inputrc</I></A>.

When a program which uses the readline library starts up, the
initialization file is read, and the key bindings and variables
are set.
There are only a few basic constructs allowed in the
readline initialization file.
Blank lines are ignored.
Lines beginning with a <B>#</B> are comments.
Lines beginning with a <B>$</B> indicate conditional constructs.
Other lines denote key bindings and variable settings.
<P>

The default key-bindings may be changed with an
<I>inputrc</I>

file.
Other programs that use this library may add their own commands
and bindings.
<P>

For example, placing
<DL COMPACT><DT><DD>
<P>

M-Control-u: universal-argument
</DL>

or
<DL COMPACT><DT><DD>
C-Meta-u: universal-argument
</DL>

into the
<I>inputrc</I>

would make M-C-u execute the readline command
<I>universal-argument</I>.

<P>

The following symbolic character names are recognized:
<I>RUBOUT</I>,

<I>DEL</I>,

<I>ESC</I>,

<I>LFD</I>,

<I>NEWLINE</I>,

<I>RET</I>,

<I>RETURN</I>,

<I>SPC</I>,

<I>SPACE</I>,

and
<I>TAB</I>.

<P>

In addition to command names, readline allows keys to be bound
to a string that is inserted when the key is pressed (a <I>macro</I>).
<A NAME="lbCI">&nbsp;</A>
<H4>Readline Key Bindings</H4>

The syntax for controlling key bindings in the
<I>inputrc</I>

file is simple.  All that is required is the name of the
command or the text of a macro and a key sequence to which
it should be bound.  The name may be specified in one of two ways:
as a symbolic key name, possibly with <I>Meta-</I> or <I>Control-</I>
prefixes, or as a key sequence.
<P>

When using the form <B>keyname</B>:<I>function-name</I> or <I>macro</I>,
<I>keyname</I>

is the name of a key spelled out in English.  For example:
<P>
<DL COMPACT><DT><DD>
Control-u: universal-argument
<BR>

Meta-Rubout: backward-kill-word
<BR>

Control-o: &quot;&gt; output&quot;
</DL>

<P>

In the above example,
<I>C-u</I>

is bound to the function
<B>universal-argument</B>,

<I>M-DEL</I>

is bound to the function
<B>backward-kill-word</B>,

and
<I>C-o</I>

is bound to run the macro
expressed on the right hand side (that is, to insert the text
<TT>&gt; output</TT>

into the line).
<P>

In the second form, <B>&quot;keyseq&quot;</B>:<I>function-name</I> or <I>macro</I>,
<B>keyseq</B>

differs from
<B>keyname</B>

above in that strings denoting
an entire key sequence may be specified by placing the sequence
within double quotes.  Some GNU Emacs style key escapes can be
used, as in the following example, but the symbolic character names
are not recognized.
<P>
<DL COMPACT><DT><DD>
&quot;\C-u&quot;: universal-argument
<BR>

&quot;\C-x\C-r&quot;: re-read-init-file
<BR>

&quot;\e[11~&quot;: &quot;Function Key 1&quot;
</DL>

<P>

In this example,
<I>C-u</I>

is again bound to the function
<B>universal-argument</B>.

<I>C-x C-r</I>

is bound to the function
<B>re-read-init-file</B>,

and
<I>ESC [ 1 1 ~</I>

is bound to insert the text
<TT>Function Key 1</TT>.

<P>

The full set of GNU Emacs style escape sequences is
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>\C-</B>

<DD>
control prefix
<DT><B>\M-</B>

<DD>
meta prefix
<DT><B>\e</B>

<DD>
an escape character
<DT><B>\\</B>

<DD>
backslash
<DT><B>\</B>

<DD>
literal &quot;
<DT><B>\aq</B>

<DD>
literal aq
</DL></DL>


<P>

In addition to the GNU Emacs style escape sequences, a second
set of backslash escapes is available:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>\a</B>

<DD>
alert (bell)
<DT><B>\b</B>

<DD>
backspace
<DT><B>\d</B>

<DD>
delete
<DT><B>\f</B>

<DD>
form feed
<DT><B>\n</B>

<DD>
newline
<DT><B>\r</B>

<DD>
carriage return
<DT><B>\t</B>

<DD>
horizontal tab
<DT><B>\v</B>

<DD>
vertical tab
<DT><B>\</B><I>nnn</I>

<DD>
the eight-bit character whose value is the octal value <I>nnn</I>
(one to three digits)
<DT><B>\x</B><I>HH</I>

<DD>
the eight-bit character whose value is the hexadecimal value <I>HH</I>
(one or two hex digits)
</DL></DL>


<P>

When entering the text of a macro, single or double quotes must
be used to indicate a macro definition.
Unquoted text is assumed to be a function name.
In the macro body, the backslash escapes described above are expanded.
Backslash will quote any other character in the macro text,
including &quot; and aq.
<P>

<B>Bash</B>

allows the current readline key bindings to be displayed or modified
with the
<B>bind</B>

builtin command.  The editing mode may be switched during interactive
use by using the
<B>-o</B>

option to the
<B>set</B>

builtin command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<A NAME="lbCJ">&nbsp;</A>
<H4>Readline Variables</H4>

Readline has variables that can be used to further customize its
behavior.  A variable may be set in the
<I>inputrc</I>

file with a statement of the form
<DL COMPACT><DT><DD>
<P>

<B>set</B> <I>variable-name</I> <I>value</I>
</DL>

or using the <B>bind</B> builtin command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).
<P>

Except where noted, readline variables can take the values
<B>On</B>

or
<B>Off</B>

(without regard to case).
Unrecognized variable names are ignored.
When a variable value is read, empty or null values, &quot;on&quot; (case-insensitive),
and &quot;1&quot; are equivalent to <B>On</B>.  All other values are equivalent to
<B>Off</B>.
The variables and their default values are:
<P>


<DL COMPACT>
<DT><B>active-region-start-color   </B>

<DD>
A string variable that controls the text color and background when displaying
the text in the active region (see the description of
<B>enable-active-region</B> below).
This string must not take up any physical character positions on the display,
so it should consist only of terminal escape sequences.
It is output to the terminal before displaying the text in the active region.
This variable is reset to the default value whenever the terminal type changes.
The default value is the string that puts the terminal in standout mode,
as obtained from the terminal's terminfo description.
A sample value might be <TT>&quot;\e[01;33m&quot;</TT>.
<DT><B>active-region-end-color     </B>

<DD>
A string variable that &quot;undoes&quot; the effects of <B>active-region-start-color</B> 
and restores &quot;normal&quot; terminal display appearance after displaying text
in the active region.
This string must not take up any physical character positions on the display,
so it should consist only of terminal escape sequences.
It is output to the terminal after displaying the text in the active region.
This variable is reset to the default value whenever the terminal type changes.
The default value is the string that restores the terminal from standout mode,
as obtained from the terminal's terminfo description.
A sample value might be <TT>&quot;\e[0m&quot;</TT>.
<DT><B>bell-style (audible)</B>

<DD>
Controls what happens when readline wants to ring the terminal bell.
If set to <B>none</B>, readline never rings the bell.  If set to
<B>visible</B>, readline uses a visible bell if one is available.
If set to <B>audible</B>, readline attempts to ring the terminal's bell.
<DT><B>bind-tty-special-chars (On)</B>

<DD>
If set to <B>On</B>, readline attempts to bind the control characters
treated specially by the kernel's terminal driver to their readline
equivalents.
<DT><B>blink-matching-paren (Off)</B>

<DD>
If set to <B>On</B>, readline attempts to briefly move the cursor to an
opening parenthesis when a closing parenthesis is inserted.
<DT><B>colored-completion-prefix (Off)</B>

<DD>
If set to <B>On</B>, when listing completions, readline displays the
common prefix of the set of possible completions using a different color.
The color definitions are taken from the value of the <B>LS_COLORS</B>
environment variable.
If there is a color definition in <B>$LS_COLORS</B> for the custom suffix
&quot;readline-colored-completion-prefix&quot;, readline uses this color for
the common prefix instead of its default.
<DT><B>colored-stats (Off)</B>

<DD>
If set to <B>On</B>, readline displays possible completions using different
colors to indicate their file type.
The color definitions are taken from the value of the <B>LS_COLORS</B>
environment variable.
<DT><B>comment-begin (``#'')</B>

<DD>
The string that is inserted when the readline
<B>insert-comment</B>

command is executed.
This command is bound to
<B>M-#</B>

in emacs mode and to
<B>#</B>

in vi command mode.
<DT><B>completion-display-width (-1)</B>

<DD>
The number of screen columns used to display possible matches
when performing completion.
The value is ignored if it is less than 0 or greater than the terminal
screen width.
A value of 0 will cause matches to be displayed one per line.
The default value is -1.
<DT><B>completion-ignore-case (Off)</B>

<DD>
If set to <B>On</B>, readline performs filename matching and completion
in a case-insensitive fashion.
<DT><B>completion-map-case (Off)</B>

<DD>
If set to <B>On</B>, and <B>completion-ignore-case</B> is enabled, readline
treats hyphens (<I>-</I>) and underscores (<I>_</I>) as equivalent when
performing case-insensitive filename matching and completion.
<DT><B>completion-prefix-display-length (0)</B>

<DD>
The length in characters of the common prefix of a list of possible
completions that is displayed without modification.  When set to a
value greater than zero, common prefixes longer than this value are
replaced with an ellipsis when displaying possible completions.
<DT><B>completion-query-items (100)</B>

<DD>
This determines when the user is queried about viewing
the number of possible completions
generated by the <B>possible-completions</B> command.
It may be set to any integer value greater than or equal to zero.
If the number of possible completions is greater than
or equal to the value of this variable,
readline will ask whether or not the user wishes to view them;
otherwise they are simply listed on the terminal.
A zero value means readline should never ask; negative values are
treated as zero.
<DT><B>convert-meta (On)</B>

<DD>
If set to <B>On</B>, readline will convert characters with the
eighth bit set to an ASCII key sequence
by stripping the eighth bit and prefixing an
escape character (in effect, using escape as the <I>meta prefix</I>).
The default is <I>On</I>, but readline will set it to <I>Off</I> if the
locale contains eight-bit characters.
This variable is dependent on the <B>LC_CTYPE</B> locale category, and 
may change if the locale is changed.
<DT><B>disable-completion (Off)</B>

<DD>
If set to <B>On</B>, readline will inhibit word completion.  Completion
characters will be inserted into the line as if they had been
mapped to <B>self-insert</B>.
<DT><B>echo-control-characters (On)</B>

<DD>
When set to <B>On</B>, on operating systems that indicate they support it,
readline echoes a character corresponding to a signal generated from the
keyboard.
<DT><B>editing-mode (emacs)</B>

<DD>
Controls whether readline begins with a set of key bindings similar
to <I>Emacs</I> or <I>vi</I>.
<B>editing-mode</B>

can be set to either
<B>emacs</B>

or
<B>vi</B>.

<DT><B>emacs-mode-string (@)</B>

<DD>
If the <I>show-mode-in-prompt</I> variable is enabled,
this string is displayed immediately before the last line of the primary
prompt when emacs editing mode is active.  The value is expanded like a
key binding, so the standard set of meta- and control prefixes and
backslash escape sequences is available.
Use the \1 and \2 escapes to begin and end sequences of
non-printing characters, which can be used to embed a terminal control
sequence into the mode string.
<DT><B>enable-active-region (On)</B>

<DD>
The <I>point</I> is the current cursor position, and <I>mark</I> refers  
to a saved cursor position.   
The text between the point and mark is referred to as the <I>region</I>.  
When this variable is set to <I>On</I>, readline allows certain commands
to designate the region as <I>active</I>.  
When the region is active, readline highlights the text in the region using
the value of the <B>active-region-start-color</B>, which defaults to the
string that enables
the terminal's standout mode.
The active region shows the text inserted by bracketed-paste and any
matching text found by incremental and non-incremental history searches.
<DT><B>enable-bracketed-paste (On)</B>

<DD>
When set to <B>On</B>, readline configures the terminal to insert each
paste into the editing buffer as a single string of characters, instead
of treating each character as if it had been read from the keyboard.
This prevents readline from executing any editing commands bound to key
sequences appearing in the pasted text.
<DT><B>enable-keypad (Off)</B>

<DD>
When set to <B>On</B>, readline will try to enable the application
keypad when it is called.  Some systems need this to enable the
arrow keys.
<DT><B>enable-meta-key (On)</B>

<DD>
When set to <B>On</B>, readline will try to enable any meta modifier
key the terminal claims to support when it is called.  On many terminals,
the meta key is used to send eight-bit characters.
<DT><B>expand-tilde (Off)</B>

<DD>
If set to <B>On</B>, tilde expansion is performed when readline
attempts word completion.
<DT><B>history-preserve-point (Off)</B>

<DD>
If set to <B>On</B>, the history code attempts to place point at the
same location on each history line retrieved with <B>previous-history</B>
or <B>next-history</B>.
<DT><B>history-size (unset)</B>

<DD>
Set the maximum number of history entries saved in the history list.
If set to zero, any existing history entries are deleted and no new entries
are saved.
If set to a value less than zero, the number of history entries is not
limited.
By default, the number of history entries is set to the value of the
<B>HISTSIZE</B> shell variable.
If an attempt is made to set <I>history-size</I> to a non-numeric value,
the maximum number of history entries will be set to 500.
<DT><B>horizontal-scroll-mode (Off)</B>

<DD>
When set to <B>On</B>, makes readline use a single line for display,
scrolling the input horizontally on a single screen line when it
becomes longer than the screen width rather than wrapping to a new line.
This setting is automatically enabled for terminals of height 1.
<DT><B>input-meta (Off)</B>

<DD>
If set to <B>On</B>, readline will enable eight-bit input (that is,
it will not strip the eighth bit from the characters it reads),
regardless of what the terminal claims it can support.  The name
<B>meta-flag</B>

is a synonym for this variable.
The default is <I>Off</I>, but readline will set it to <I>On</I> if the
locale contains eight-bit characters.
This variable is dependent on the <B>LC_CTYPE</B> locale category, and 
may change if the locale is changed.
<DT><B>isearch-terminators (``C-[C-J'')</B>

<DD>
The string of characters that should terminate an incremental
search without subsequently executing the character as a command.
If this variable has not been given a value, the characters
<I>ESC</I> and <I>C-J</I> will terminate an incremental search.
<DT><B>keymap (emacs)</B>

<DD>
Set the current readline keymap.  The set of valid keymap names is
<I>emacs, emacs-standard, emacs-meta, emacs-ctlx, vi,
vi-command</I>, and
<I>vi-insert</I>.

<I>vi</I> is equivalent to <I>vi-command</I>; <I>emacs</I> is
equivalent to <I>emacs-standard</I>.  The default value is
<I>emacs</I>;

the value of
<B>editing-mode</B>

also affects the default keymap.
<DT><B>keyseq-timeout (500)</B>

<DD>
Specifies the duration <I>readline</I> will wait for a character when reading an
ambiguous key sequence (one that can form a complete key sequence using
the input read so far, or can take additional input to complete a longer
key sequence).
If no input is received within the timeout, <I>readline</I> will use the shorter
but complete key sequence.
The value is specified in milliseconds, so a value of 1000 means that
<I>readline</I> will wait one second for additional input.
If this variable is set to a value less than or equal to zero, or to a
non-numeric value, <I>readline</I> will wait until another key is pressed to
decide which key sequence to complete.
<DT><B>mark-directories (On)</B>

<DD>
If set to <B>On</B>, completed directory names have a slash
appended.
<DT><B>mark-modified-lines (Off)</B>

<DD>
If set to <B>On</B>, history lines that have been modified are displayed
with a preceding asterisk (<B>*</B>).
<DT><B>mark-symlinked-directories (Off)</B>

<DD>
If set to <B>On</B>, completed names which are symbolic links to directories
have a slash appended (subject to the value of
<B>mark-directories</B>).
<DT><B>match-hidden-files (On)</B>

<DD>
This variable, when set to <B>On</B>, causes readline to match files whose
names begin with a `.' (hidden files) when performing filename
completion.
If set to <B>Off</B>, the leading `.' must be
supplied by the user in the filename to be completed.
<DT><B>menu-complete-display-prefix (Off)</B>

<DD>
If set to <B>On</B>, menu completion displays the common prefix of the
list of possible completions (which may be empty) before cycling through
the list.
<DT><B>output-meta (Off)</B>

<DD>
If set to <B>On</B>, readline will display characters with the
eighth bit set directly rather than as a meta-prefixed escape
sequence.
The default is <I>Off</I>, but readline will set it to <I>On</I> if the
locale contains eight-bit characters.
This variable is dependent on the <B>LC_CTYPE</B> locale category, and 
may change if the locale is changed.
<DT><B>page-completions (On)</B>

<DD>
If set to <B>On</B>, readline uses an internal <I>more</I>-like pager
to display a screenful of possible completions at a time.
<DT><B>print-completions-horizontally (Off)</B>

<DD>
If set to <B>On</B>, readline will display completions with matches
sorted horizontally in alphabetical order, rather than down the screen.
<DT><B>revert-all-at-newline (Off)</B>

<DD>
If set to <B>On</B>, readline will undo all changes to history lines
before returning when <B>accept-line</B> is executed.  By default,
history lines may be modified and retain individual undo lists across
calls to <B>readline</B>.
<DT><B>show-all-if-ambiguous (Off)</B>

<DD>
This alters the default behavior of the completion functions.  If
set to
<B>On</B>,

words which have more than one possible completion cause the
matches to be listed immediately instead of ringing the bell.
<DT><B>show-all-if-unmodified (Off)</B>

<DD>
This alters the default behavior of the completion functions in
a fashion similar to <B>show-all-if-ambiguous</B>.
If set to
<B>On</B>,

words which have more than one possible completion without any
possible partial completion (the possible completions don't share
a common prefix) cause the matches to be listed immediately instead
of ringing the bell.
<DT><B>show-mode-in-prompt (Off)</B>

<DD>
If set to <B>On</B>, add a string to the beginning of the prompt
indicating the editing mode: emacs, vi command, or vi insertion.
The mode strings are user-settable (e.g., <I>emacs-mode-string</I>).
<DT><B>skip-completed-text (Off)</B>

<DD>
If set to <B>On</B>, this alters the default completion behavior when
inserting a single match into the line.  It's only active when
performing completion in the middle of a word.  If enabled, readline
does not insert characters from the completion that match characters
after point in the word being completed, so portions of the word
following the cursor are not duplicated.
<DT><B>vi-cmd-mode-string ((cmd))</B>

<DD>
If the <I>show-mode-in-prompt</I> variable is enabled,
this string is displayed immediately before the last line of the primary
prompt when vi editing mode is active and in command mode.
The value is expanded like a
key binding, so the standard set of meta- and control prefixes and
backslash escape sequences is available.
Use the \1 and \2 escapes to begin and end sequences of
non-printing characters, which can be used to embed a terminal control
sequence into the mode string.
<DT><B>vi-ins-mode-string ((ins))</B>

<DD>
If the <I>show-mode-in-prompt</I> variable is enabled,
this string is displayed immediately before the last line of the primary
prompt when vi editing mode is active and in insertion mode.
The value is expanded like a
key binding, so the standard set of meta- and control prefixes and
backslash escape sequences is available.
Use the \1 and \2 escapes to begin and end sequences of
non-printing characters, which can be used to embed a terminal control
sequence into the mode string.
<DT><B>visible-stats (Off)</B>

<DD>
If set to <B>On</B>, a character denoting a file's type as reported
by <I>stat</I>(2) is appended to the filename when listing possible
completions.

</DL>
<A NAME="lbCK">&nbsp;</A>
<H4>Readline Conditional Constructs</H4>

Readline implements a facility similar in spirit to the conditional
compilation features of the C preprocessor which allows key
bindings and variable settings to be performed as the result
of tests.  There are four parser directives used.
<DL COMPACT>
<DT><B>$if</B><DD>
The
<B>$if</B>

construct allows bindings to be made based on the
editing mode, the terminal being used, or the application using
readline.  The text of the test, after any comparison operator,
<BR>&nbsp;extends&nbsp;to&nbsp;the&nbsp;end&nbsp;of&nbsp;the&nbsp;line;
unless otherwise noted, no characters are required to isolate it.
<DL COMPACT><DT><DD>
<DL COMPACT>
<DT><B>mode</B><DD>
The <B>mode=</B> form of the <B>$if</B> directive is used to test
whether readline is in emacs or vi mode.
This may be used in conjunction
with the <B>set keymap</B> command, for instance, to set bindings in
the <I>emacs-standard</I> and <I>emacs-ctlx</I> keymaps only if
readline is starting out in emacs mode.
<DT><B>term</B><DD>
The <B>term=</B> form may be used to include terminal-specific
key bindings, perhaps to bind the key sequences output by the
terminal's function keys.  The word on the right side of the
<B>=</B>

is tested against both the full name of the terminal and the portion
of the terminal name before the first <B>-</B>.  This allows
<I>sun</I>

to match both
<I>sun</I>

and
<I>sun-cmd</I>,

for instance.
<DT><B>version</B><DD>
The <B>version</B> test may be used to perform comparisons against
specific readline versions.
The <B>version</B> expands to the current readline version.
The set of comparison operators includes
<B>=</B>,

(and
<B>==</B>),

<B>!=</B>,

<B>&lt;=</B>,

<B>&gt;=</B>,

<B>&lt;</B>,

and
<B>&gt;</B>.

The version number supplied on the right side of the operator consists
of a major version number, an optional decimal point, and an optional
minor version (e.g., <B>7.1</B>). If the minor version is omitted, it
is assumed to be <B>0</B>.
The operator may be separated from the string <B>version</B>
and from the version number argument by whitespace.
<DT><B>application</B><DD>
The <B>application</B> construct is used to include
application-specific settings.  Each program using the readline
library sets the <I>application name</I>, and an initialization
file can test for a particular value.
This could be used to bind key sequences to functions useful for
a specific program.  For instance, the following command adds a
key sequence that quotes the current or previous word in <B>bash</B>:
<P>
<DL COMPACT><DT><DD>
<PRE>
<B>$if</B> Bash
# Quote the current or previous word
&quot;\C-xq&quot;: &quot;\eb\&quot;\ef\&quot;&quot;
<B>$endif</B>
</PRE>

</DL>

<DT><I>variable</I><DD>
The <I>variable</I> construct provides simple equality tests for readline
variables and values.
The permitted comparison operators are <I>=</I>, <I>==</I>, and <I>!=</I>.
The variable name must be separated from the comparison operator by
whitespace; the operator may be separated from the value on the right hand
side by whitespace.
Both string and boolean variables may be tested. Boolean variables must be
tested against the values <I>on</I> and <I>off</I>.
</DL></DL>

<DT><B>$endif</B><DD>
This command, as seen in the previous example, terminates an
<B>$if</B> command.
<DT><B>$else</B><DD>
Commands in this branch of the <B>$if</B> directive are executed if
the test fails.
<DT><B>$include</B><DD>
This directive takes a single filename as an argument and reads commands
and bindings from that file.  For example, the following directive
would read <A HREF="file:/etc/inputrc"><I>/etc/inputrc</I></A>:
<P>
<DL COMPACT><DT><DD>
<PRE>
<B>$include</B>  <A HREF="file:/etc/inputrc"><I>/etc/inputrc</I></A>
</PRE>

</DL>

</DL>
<A NAME="lbCL">&nbsp;</A>
<H4>Searching</H4>

Readline provides commands for searching through the command history
(see
<FONT SIZE=-1><B>HISTORY</B>

</FONT>
below) for lines containing a specified string.
There are two search modes:
<I>incremental</I>

and
<I>non-incremental</I>.

<P>

Incremental searches begin before the user has finished typing the
search string.
As each character of the search string is typed, readline displays
the next entry from the history matching the string typed so far.
An incremental search requires only as many characters as needed to
find the desired history entry.
The characters present in the value of the <B>isearch-terminators</B>
variable are used to terminate an incremental search.
If that variable has not been assigned a value the Escape and
Control-J characters will terminate an incremental search.
Control-G will abort an incremental search and restore the original
line.
When the search is terminated, the history entry containing the
search string becomes the current line.
<P>

To find other matching entries in the history list, type Control-S or
Control-R as appropriate.
This will search backward or forward in the history for the next
entry matching the search string typed so far.
Any other key sequence bound to a readline command will terminate
the search and execute that command.
For instance, a <I>newline</I> will terminate the search and accept
the line, thereby executing the command from the history list.
<P>

Readline remembers the last incremental search string.  If two
Control-Rs are typed without any intervening characters defining a
new search string, any remembered search string is used.
<P>

Non-incremental searches read the entire search string before starting
to search for matching history lines.  The search string may be
typed by the user or be part of the contents of the current line.
<A NAME="lbCM">&nbsp;</A>
<H4>Readline Command Names</H4>

The following is a list of the names of the commands and the default
key sequences to which they are bound.
Command names without an accompanying key sequence are unbound by default.
In the following descriptions, <I>point</I> refers to the current cursor
position, and <I>mark</I> refers to a cursor position saved by the
<B>set-mark</B> command.
The text between the point and mark is referred to as the <I>region</I>.
<A NAME="lbCN">&nbsp;</A>
<H4>Commands for Moving</H4>


<DL COMPACT>
<DT><B>beginning-of-line (C-a)</B>

<DD>
Move to the start of the current line.
<DT><B>end-of-line (C-e)</B>

<DD>
Move to the end of the line.
<DT><B>forward-char (C-f)</B>

<DD>
Move forward a character.
<DT><B>backward-char (C-b)</B>

<DD>
Move back a character.
<DT><B>forward-word (M-f)</B>

<DD>
Move forward to the end of the next word.  Words are composed of
alphanumeric characters (letters and digits).
<DT><B>backward-word (M-b)</B>

<DD>
Move back to the start of the current or previous word.
Words are composed of alphanumeric characters (letters and digits).
<DT><B>shell-forward-word</B>

<DD>
Move forward to the end of the next word.
Words are delimited by non-quoted shell metacharacters.
<DT><B>shell-backward-word</B>

<DD>
Move back to the start of the current or previous word.
Words are delimited by non-quoted shell metacharacters.
<DT><B>previous-screen-line</B>

<DD>
Attempt to move point to the same physical screen column on the previous
physical screen line. This will not have the desired effect if the current
readline line does not take up more than one physical line or if point is not
greater than the length of the prompt plus the screen width.
<DT><B>next-screen-line</B>

<DD>
Attempt to move point to the same physical screen column on the next
physical screen line. This will not have the desired effect if the current
readline line does not take up more than one physical line or if the length
of the current readline line is not greater than the length of the prompt
plus the screen width.
<DT><B>clear-display (M-C-l)</B>

<DD>
Clear the screen and, if possible, the terminal's scrollback buffer,
then redraw the current line,
leaving the current line at the top of the screen.
<DT><B>clear-screen (C-l)</B>

<DD>
Clear the screen,
then redraw the current line,
leaving the current line at the top of the screen.
With an argument, refresh the current line without clearing the
screen.
<DT><B>redraw-current-line</B>

<DD>
Refresh the current line.

</DL>
<A NAME="lbCO">&nbsp;</A>
<H4>Commands for Manipulating the History</H4>


<DL COMPACT>
<DT><B>accept-line (Newline, Return)</B>

<DD>
Accept the line regardless of where the cursor is.  If this line is
non-empty, add it to the history list according to the state of the
<FONT SIZE=-1><B>HISTCONTROL</B>

</FONT>
variable.  If the line is a modified history
line, then restore the history line to its original state.
<DT><B>previous-history (C-p)</B>

<DD>
Fetch the previous command from the history list, moving back in
the list.
<DT><B>next-history (C-n)</B>

<DD>
Fetch the next command from the history list, moving forward in the
list.
<DT><B>beginning-of-history (M-&lt;)</B>

<DD>
Move to the first line in the history.
<DT><B>end-of-history (M-&gt;)</B>

<DD>
Move to the end of the input history, i.e., the line currently being
entered.
<DT><B>operate-and-get-next (C-o)</B>

<DD>
Accept the current line for execution and fetch the next line
relative to the current line from the history for editing.
A numeric argument, if supplied, specifies the history entry to use instead
of the current line.
<DT><B>fetch-history</B>

<DD>
With a numeric argument, fetch that entry from the history list
and make it the current line.
Without an argument, move back to the first entry in the history list.
<DT><B>reverse-search-history (C-r)</B>

<DD>
Search backward starting at the current line and moving `up' through
the history as necessary.  This is an incremental search.
<DT><B>forward-search-history (C-s)</B>

<DD>
Search forward starting at the current line and moving `down' through
the history as necessary.  This is an incremental search.
<DT><B>non-incremental-reverse-search-history (M-p)</B>

<DD>
Search backward through the history starting at the current line
using a non-incremental search for a string supplied by the user.
<DT><B>non-incremental-forward-search-history (M-n)</B>

<DD>
Search forward through the history using a non-incremental search for
a string supplied by the user.
<DT><B>history-search-forward</B>

<DD>
Search forward through the history for the string of characters
between the start of the current line and the point.
This is a non-incremental search.
<DT><B>history-search-backward</B>

<DD>
Search backward through the history for the string of characters
between the start of the current line and the point.
This is a non-incremental search.
<DT><B>history-substring-search-backward</B>

<DD>
Search backward through the history for the string of characters
between the start of the current line and the current cursor
position (the <I>point</I>).
The search string may match anywhere in a history line.
This is a non-incremental search.
<DT><B>history-substring-search-forward</B>

<DD>
Search forward through the history for the string of characters
between the start of the current line and the point.
The search string may match anywhere in a history line.
This is a non-incremental search.
<DT><B>yank-nth-arg (M-C-y)</B>

<DD>
Insert the first argument to the previous command (usually
the second word on the previous line) at point.
With an argument
<I>n</I>,

insert the <I>n</I>th word from the previous command (the words
in the previous command begin with word 0).  A negative argument
inserts the <I>n</I>th word from the end of the previous command.
Once the argument <I>n</I> is computed, the argument is extracted
as if the &quot;!<I>n</I>&quot; history expansion had been specified.
<DT><B>yank-last-arg (M-., M-_)</B>

<DD>
Insert the last argument to the previous command (the last word of
the previous history entry).
With a numeric argument, behave exactly like <B>yank-nth-arg</B>.
Successive calls to <B>yank-last-arg</B> move back through the history
list, inserting the last word (or the word specified by the argument to
the first call) of each line in turn.
Any numeric argument supplied to these successive calls determines
the direction to move through the history.  A negative argument switches
the direction through the history (back or forward).
The history expansion facilities are used to extract the last word,
as if the &quot;!$&quot; history expansion had been specified.
<DT><B>shell-expand-line (M-C-e)</B>

<DD>
Expand the line as the shell does.  This
performs alias and history expansion as well as all of the shell
word expansions.  See
<FONT SIZE=-1><B>HISTORY EXPANSION</B>

</FONT>
below for a description of history expansion.
<DT><B>history-expand-line (M-^)</B>

<DD>
Perform history expansion on the current line.
See
<FONT SIZE=-1><B>HISTORY EXPANSION</B>

</FONT>
below for a description of history expansion.
<DT><B>magic-space</B>

<DD>
Perform history expansion on the current line and insert a space.
See
<FONT SIZE=-1><B>HISTORY EXPANSION</B>

</FONT>
below for a description of history expansion.
<DT><B>alias-expand-line</B>

<DD>
Perform alias expansion on the current line.
See
<FONT SIZE=-1><B>ALIASES</B>

</FONT>
above for a description of alias expansion.
<DT><B>history-and-alias-expand-line</B>

<DD>
Perform history and alias expansion on the current line.
<DT><B>insert-last-argument (M-., M-_)</B>

<DD>
A synonym for <B>yank-last-arg</B>.
<DT><B>edit-and-execute-command (C-x C-e)</B>

<DD>
Invoke an editor on the current command line, and execute the result as shell
commands.
<B>Bash</B> attempts to invoke
<FONT SIZE=-1><B>$VISUAL</B>,

</FONT>
<FONT SIZE=-1><B>$EDITOR</B>,

</FONT>
and <I>emacs</I> as the editor, in that order.

</DL>
<A NAME="lbCP">&nbsp;</A>
<H4>Commands for Changing Text</H4>


<DL COMPACT>
<DT><B></B><I>end-of-file</I> (usually C-d)

<DD>
The character indicating end-of-file as set, for example, by
<TT>stty</TT>.

If this character is read when there are no characters
on the line, and point is at the beginning of the line, readline
interprets it as the end of input and returns
<FONT SIZE=-1><B>EOF</B>.

</FONT>
<DT><B>delete-char (C-d)</B>

<DD>
Delete the character at point.
If this function is bound to the
same character as the tty <B>EOF</B> character, as <B>C-d</B>
commonly is, see above for the effects.
<DT><B>backward-delete-char (Rubout)</B>

<DD>
Delete the character behind the cursor.  When given a numeric argument,
save the deleted text on the kill ring.
<DT><B>forward-backward-delete-char</B>

<DD>
Delete the character under the cursor, unless the cursor is at the
end of the line, in which case the character behind the cursor is
deleted.
<DT><B>quoted-insert (C-q, C-v)</B>

<DD>
Add the next character typed to the line verbatim.  This is
how to insert characters like <B>C-q</B>, for example.
<DT><B>tab-insert (C-v TAB)</B>

<DD>
Insert a tab character.
<DT><B>self-insert (a,&nbsp;b,&nbsp;A,&nbsp;1,&nbsp;!,&nbsp;...)</B>

<DD>
Insert the character typed.
<DT><B>transpose-chars (C-t)</B>

<DD>
Drag the character before point forward over the character at point,
moving point forward as well.
If point is at the end of the line, then this transposes
the two characters before point.
Negative arguments have no effect.
<DT><B>transpose-words (M-t)</B>

<DD>
Drag the word before point past the word after point,
moving point over that word as well.
If point is at the end of the line, this transposes
the last two words on the line.
<DT><B>upcase-word (M-u)</B>

<DD>
Uppercase the current (or following) word.  With a negative argument,
uppercase the previous word, but do not move point.
<DT><B>downcase-word (M-l)</B>

<DD>
Lowercase the current (or following) word.  With a negative argument,
lowercase the previous word, but do not move point.
<DT><B>capitalize-word (M-c)</B>

<DD>
Capitalize the current (or following) word.  With a negative argument,
capitalize the previous word, but do not move point.
<DT><B>overwrite-mode</B>

<DD>
Toggle overwrite mode.  With an explicit positive numeric argument,
switches to overwrite mode.  With an explicit non-positive numeric
argument, switches to insert mode.  This command affects only
<B>emacs</B> mode; <B>vi</B> mode does overwrite differently.
Each call to <I>readline()</I> starts in insert mode.
In overwrite mode, characters bound to <B>self-insert</B> replace
the text at point rather than pushing the text to the right.
Characters bound to <B>backward-delete-char</B> replace the character
before point with a space.  By default, this command is unbound.

</DL>
<A NAME="lbCQ">&nbsp;</A>
<H4>Killing and Yanking</H4>


<DL COMPACT>
<DT><B>kill-line (C-k)</B>

<DD>
Kill the text from point to the end of the line.
<DT><B>backward-kill-line (C-x Rubout)</B>

<DD>
Kill backward to the beginning of the line.
<DT><B>unix-line-discard (C-u)</B>

<DD>
Kill backward from point to the beginning of the line.
The killed text is saved on the kill-ring.

<DT><B>kill-whole-line</B>

<DD>
Kill all characters on the current line, no matter where point is.
<DT><B>kill-word (M-d)</B>

<DD>
Kill from point to the end of the current word, or if between
words, to the end of the next word.
Word boundaries are the same as those used by <B>forward-word</B>.
<DT><B>backward-kill-word (M-Rubout)</B>

<DD>
Kill the word behind point.
Word boundaries are the same as those used by <B>backward-word</B>.
<DT><B>shell-kill-word</B>

<DD>
Kill from point to the end of the current word, or if between
words, to the end of the next word.
Word boundaries are the same as those used by <B>shell-forward-word</B>.
<DT><B>shell-backward-kill-word</B>

<DD>
Kill the word behind point.
Word boundaries are the same as those used by <B>shell-backward-word</B>.
<DT><B>unix-word-rubout (C-w)</B>

<DD>
Kill the word behind point, using white space as a word boundary.
The killed text is saved on the kill-ring.
<DT><B>unix-filename-rubout</B>

<DD>
Kill the word behind point, using white space and the slash character
as the word boundaries.
The killed text is saved on the kill-ring.
<DT><B>delete-horizontal-space (M-\)</B>

<DD>
Delete all spaces and tabs around point.
<DT><B>kill-region</B>

<DD>
Kill the text in the current region.
<DT><B>copy-region-as-kill</B>

<DD>
Copy the text in the region to the kill buffer.
<DT><B>copy-backward-word</B>

<DD>
Copy the word before point to the kill buffer.
The word boundaries are the same as <B>backward-word</B>.
<DT><B>copy-forward-word</B>

<DD>
Copy the word following point to the kill buffer.
The word boundaries are the same as <B>forward-word</B>.
<DT><B>yank (C-y)</B>

<DD>
Yank the top of the kill ring into the buffer at point.
<DT><B>yank-pop (M-y)</B>

<DD>
Rotate the kill ring, and yank the new top.  Only works following
<B>yank</B>

or
<B>yank-pop</B>.


</DL>
<A NAME="lbCR">&nbsp;</A>
<H4>Numeric Arguments</H4>


<DL COMPACT>
<DT><B>digit-argument (M-0, M-1, ..., M--)</B>

<DD>
Add this digit to the argument already accumulating, or start a new
argument.  M-- starts a negative argument.
<DT><B>universal-argument</B>

<DD>
This is another way to specify an argument.
If this command is followed by one or more digits, optionally with a
leading minus sign, those digits define the argument.
If the command is followed by digits, executing
<B>universal-argument</B>

again ends the numeric argument, but is otherwise ignored.
As a special case, if this command is immediately followed by a
character that is neither a digit nor minus sign, the argument count
for the next command is multiplied by four.
The argument count is initially one, so executing this function the
first time makes the argument count four, a second time makes the
argument count sixteen, and so on.

</DL>
<A NAME="lbCS">&nbsp;</A>
<H4>Completing</H4>


<DL COMPACT>
<DT><B>complete (TAB)</B>

<DD>
Attempt to perform completion on the text before point.
<B>Bash</B>

attempts completion treating the text as a variable (if the
text begins with <B>$</B>), username (if the text begins with
<B>~</B>), hostname (if the text begins with <B>@</B>), or
command (including aliases and functions) in turn.  If none
of these produces a match, filename completion is attempted.
<DT><B>possible-completions (M-?)</B>

<DD>
List the possible completions of the text before point.
<DT><B>insert-completions (M-*)</B>

<DD>
Insert all completions of the text before point
that would have been generated by
<B>possible-completions</B>.
<DT><B>menu-complete</B>

<DD>
Similar to <B>complete</B>, but replaces the word to be completed
with a single match from the list of possible completions.
Repeated execution of <B>menu-complete</B> steps through the list
of possible completions, inserting each match in turn.
At the end of the list of completions, the bell is rung
(subject to the setting of <B>bell-style</B>)
and the original text is restored.
An argument of <I>n</I> moves <I>n</I> positions forward in the list
of matches; a negative argument may be used to move backward
through the list.
This command is intended to be bound to <B>TAB</B>, but is unbound
by default.
<DT><B>menu-complete-backward</B>

<DD>
Identical to <B>menu-complete</B>, but moves backward through the list
of possible completions, as if <B>menu-complete</B> had been given a
negative argument.  This command is unbound by default.
<DT><B>delete-char-or-list</B>

<DD>
Deletes the character under the cursor if not at the beginning or
end of the line (like <B>delete-char</B>).
If at the end of the line, behaves identically to
<B>possible-completions</B>.
This command is unbound by default.
<DT><B>complete-filename (M-/)</B>

<DD>
Attempt filename completion on the text before point.
<DT><B>possible-filename-completions (C-x /)</B>

<DD>
List the possible completions of the text before point,
treating it as a filename.
<DT><B>complete-username (M-~)</B>

<DD>
Attempt completion on the text before point, treating
it as a username.
<DT><B>possible-username-completions (C-x ~)</B>

<DD>
List the possible completions of the text before point,
treating it as a username.
<DT><B>complete-variable (M-$)</B>

<DD>
Attempt completion on the text before point, treating
it as a shell variable.
<DT><B>possible-variable-completions (C-x $)</B>

<DD>
List the possible completions of the text before point,
treating it as a shell variable.
<DT><B>complete-hostname (M-@)</B>

<DD>
Attempt completion on the text before point, treating
it as a hostname.
<DT><B>possible-hostname-completions (C-x @)</B>

<DD>
List the possible completions of the text before point,
treating it as a hostname.
<DT><B>complete-command (M-!)</B>

<DD>
Attempt completion on the text before point, treating
it as a command name.  Command completion attempts to
match the text against aliases, reserved words, shell
functions, shell builtins, and finally executable filenames,
in that order.
<DT><B>possible-command-completions (C-x !)</B>

<DD>
List the possible completions of the text before point,
treating it as a command name.
<DT><B>dynamic-complete-history (M-TAB)</B>

<DD>
Attempt completion on the text before point, comparing
the text against lines from the history list for possible
completion matches.
<DT><B>dabbrev-expand</B>

<DD>
Attempt menu completion on the text before point, comparing
the text against lines from the history list for possible
completion matches.
<DT><B>complete-into-braces (M-{)</B>

<DD>
Perform filename completion and insert the list of possible completions
enclosed within braces so the list is available to the shell (see
<B>Brace Expansion</B>

above).

</DL>
<A NAME="lbCT">&nbsp;</A>
<H4>Keyboard Macros</H4>


<DL COMPACT>
<DT><B>start-kbd-macro (C-x ()</B>

<DD>
Begin saving the characters typed into the current keyboard macro.
<DT><B>end-kbd-macro (C-x ))</B>

<DD>
Stop saving the characters typed into the current keyboard macro
and store the definition.
<DT><B>call-last-kbd-macro (C-x e)</B>

<DD>
Re-execute the last keyboard macro defined, by making the characters
in the macro appear as if typed at the keyboard.
<DT><B>print-last-kbd-macro ()</B>

<DD>
Print the last keyboard macro defined in a format suitable for the
<I>inputrc</I> file.

</DL>
<A NAME="lbCU">&nbsp;</A>
<H4>Miscellaneous</H4>


<DL COMPACT>
<DT><B>re-read-init-file (C-x C-r)</B>

<DD>
Read in the contents of the <I>inputrc</I> file, and incorporate
any bindings or variable assignments found there.
<DT><B>abort (C-g)</B>

<DD>
Abort the current editing command and
ring the terminal's bell (subject to the setting of
<B>bell-style</B>).

<DT><B>do-lowercase-version (M-A, M-B, M-</B><I>x</I>, ...)

<DD>
If the metafied character <I>x</I> is uppercase, run the command
that is bound to the corresponding metafied lowercase character.
The behavior is undefined if <I>x</I> is already lowercase.
<DT><B>prefix-meta (ESC)</B>

<DD>
Metafy the next character typed.
<FONT SIZE=-1><B>ESC</B>

</FONT>
<B>f</B>

is equivalent to
<B>Meta-f</B>.

<DT><B>undo (C-_, C-x C-u)</B>

<DD>
Incremental undo, separately remembered for each line.
<DT><B>revert-line (M-r)</B>

<DD>
Undo all changes made to this line.  This is like executing the
<B>undo</B>

command enough times to return the line to its initial state.
<DT><B>tilde-expand (M-&amp;)</B>

<DD>
Perform tilde expansion on the current word.
<DT><B>set-mark (C-@, M-&lt;space&gt;)</B>

<DD>
Set the mark to the point.  If a
numeric argument is supplied, the mark is set to that position.
<DT><B>exchange-point-and-mark (C-x C-x)</B>

<DD>
Swap the point with the mark.  The current cursor position is set to
the saved position, and the old cursor position is saved as the mark.
<DT><B>character-search (C-])</B>

<DD>
A character is read and point is moved to the next occurrence of that
character.  A negative argument searches for previous occurrences.
<DT><B>character-search-backward (M-C-])</B>

<DD>
A character is read and point is moved to the previous occurrence of that
character.  A negative argument searches for subsequent occurrences.
<DT><B>skip-csi-sequence</B>

<DD>
Read enough characters to consume a multi-key sequence such as those
defined for keys like Home and End.  Such sequences begin with a
Control Sequence Indicator (CSI), usually ESC-[.  If this sequence is
bound to &quot;\[&quot;, keys producing such sequences will have no effect
unless explicitly bound to a readline command, instead of inserting
stray characters into the editing buffer.  This is unbound by default,
but usually bound to ESC-[.
<DT><B>insert-comment (M-#)</B>

<DD>
Without a numeric argument, the value of the readline
<B>comment-begin</B>

variable is inserted at the beginning of the current line.
If a numeric argument is supplied, this command acts as a toggle: if
the characters at the beginning of the line do not match the value
of <B>comment-begin</B>, the value is inserted, otherwise
the characters in <B>comment-begin</B> are deleted from the beginning of
the line.
In either case, the line is accepted as if a newline had been typed.
The default value of
<B>comment-begin</B> causes this command to make the current line
a shell comment.
If a numeric argument causes the comment character to be removed, the line
will be executed by the shell.
<DT><B>spell-correct-word (C-x s)</B>

<DD>
Perform spelling correction on the current word, treating it as a directory
or filename, in the same way as the <B>cdspell</B> shell option.
Word boundaries are the same as those used by <B>shell-forward-word</B>.
<DT><B>glob-complete-word (M-g)</B>

<DD>
The word before point is treated as a pattern for pathname expansion,
with an asterisk implicitly appended.  This pattern is used to
generate a list of matching filenames for possible completions.
<DT><B>glob-expand-word (C-x *)</B>

<DD>
The word before point is treated as a pattern for pathname expansion,
and the list of matching filenames is inserted, replacing the word.
If a numeric argument is supplied, an asterisk is appended before
pathname expansion.
<DT><B>glob-list-expansions (C-x g)</B>

<DD>
The list of expansions that would have been generated by
<B>glob-expand-word</B>

is displayed, and the line is redrawn.
If a numeric argument is supplied, an asterisk is appended before
pathname expansion.
<DT><B>dump-functions</B>

<DD>
Print all of the functions and their key bindings to the
readline output stream.  If a numeric argument is supplied,
the output is formatted in such a way that it can be made part
of an <I>inputrc</I> file.
<DT><B>dump-variables</B>

<DD>
Print all of the settable readline variables and their values to the
readline output stream.  If a numeric argument is supplied,
the output is formatted in such a way that it can be made part
of an <I>inputrc</I> file.
<DT><B>dump-macros</B>

<DD>
Print all of the readline key sequences bound to macros and the
strings they output.  If a numeric argument is supplied,
the output is formatted in such a way that it can be made part
of an <I>inputrc</I> file.
<DT><B>display-shell-version (C-x C-v)</B>

<DD>
Display version information about the current instance of
<B>bash</B>.


</DL>
<A NAME="lbCV">&nbsp;</A>
<H4>Programmable Completion</H4>

When word completion is attempted for an argument to a command for
which a completion specification (a <I>compspec</I>) has been defined
using the <B>complete</B> builtin (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below), the programmable completion facilities are invoked.
<P>

First, the command name is identified.
If the command word is the empty string (completion attempted at the
beginning of an empty line), any compspec defined with
the <B>-E</B> option to <B>complete</B> is used.
If a compspec has been defined for that command, the
compspec is used to generate the list of possible completions for the word.
If the command word is a full pathname, a compspec for the full
pathname is searched for first.
If no compspec is found for the full pathname, an attempt is made to
find a compspec for the portion following the final slash.
If those searches do not result in a compspec, any compspec defined with
the <B>-D</B> option to <B>complete</B> is used as the default.
If there is no default compspec, <B>bash</B> attempts alias expansion
on the command word as a final resort, and attempts to find a compspec
for the command word from any successful expansion.
<P>

Once a compspec has been found, it is used to generate the list of
matching words.
If a compspec is not found, the default <B>bash</B> completion as
described above under <B>Completing</B> is performed.
<P>

First, the actions specified by the compspec are used.
Only matches which are prefixed by the word being completed are
returned.
When the
<B>-f</B>

or
<B>-d</B>

option is used for filename or directory name completion, the shell
variable
<FONT SIZE=-1><B>FIGNORE</B>

</FONT>
is used to filter the matches.
<P>

Any completions specified by a pathname expansion pattern to the
<B>-G</B> option are generated next.
The words generated by the pattern need not match the word
being completed.
The
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
shell variable is not used to filter the matches, but the
<FONT SIZE=-1><B>FIGNORE</B>

</FONT>
variable is used.
<P>

Next, the string specified as the argument to the <B>-W</B> option
is considered.
The string is first split using the characters in the
<FONT SIZE=-1><B>IFS</B>

</FONT>
special variable as delimiters.
Shell quoting is honored.
Each word is then expanded using
brace expansion, tilde expansion, parameter and variable expansion,
command substitution, and arithmetic expansion,
as described above under
<FONT SIZE=-1><B>EXPANSION</B>.

</FONT>
The results are split using the rules described above under
<B>Word Splitting</B>.
The results of the expansion are prefix-matched against the word being
completed, and the matching words become the possible completions.
<P>

After these matches have been generated, any shell function or command
specified with the <B>-F</B> and <B>-C</B> options is invoked.
When the command or function is invoked, the
<FONT SIZE=-1><B>COMP_LINE</B>,

</FONT>
<FONT SIZE=-1><B>COMP_POINT</B>,

</FONT>
<FONT SIZE=-1><B>COMP_KEY</B>,

</FONT>
and
<FONT SIZE=-1><B>COMP_TYPE</B>

</FONT>
variables are assigned values as described above under
<B>Shell Variables</B>.
If a shell function is being invoked, the
<FONT SIZE=-1><B>COMP_WORDS</B>

</FONT>
and
<FONT SIZE=-1><B>COMP_CWORD</B>

</FONT>
variables are also set.
When the function or command is invoked,
the first argument (<B>$1</B>) is the name of the command whose arguments are
being completed,
the second argument (<B>$2</B>) is the word being completed,
and the third argument (<B>$3</B>) is the word preceding the word being
completed on the current command line.
No filtering of the generated completions against the word being completed
is performed; the function or command has complete freedom in generating
the matches.
<P>

Any function specified with <B>-F</B> is invoked first.
The function may use any of the shell facilities, including the
<B>compgen</B> builtin described below, to generate the matches.
It must put the possible completions in the
<FONT SIZE=-1><B>COMPREPLY</B>

</FONT>
array variable, one per array element.
<P>

Next, any command specified with the <B>-C</B> option is invoked
in an environment equivalent to command substitution.
It should print a list of completions, one per line, to the
standard output.
Backslash may be used to escape a newline, if necessary.
<P>

After all of the possible completions are generated, any filter
specified with the <B>-X</B> option is applied to the list.
The filter is a pattern as used for pathname expansion; a <B>&amp;</B>
in the pattern is replaced with the text of the word being completed.
A literal <B>&amp;</B> may be escaped with a backslash; the backslash
is removed before attempting a match.
Any completion that matches the pattern will be removed from the list.
A leading <B>!</B> negates the pattern; in this case any completion
not matching the pattern will be removed.
If the
<B>nocasematch</B>

shell option is enabled, the match is performed without regard to the case
of alphabetic characters.
<P>

Finally, any prefix and suffix specified with the <B>-P</B> and <B>-S</B>
options are added to each member of the completion list, and the result is
returned to the readline completion code as the list of possible
completions.
<P>

If the previously-applied actions do not generate any matches, and the
<B>-o dirnames</B> option was supplied to <B>complete</B> when the
compspec was defined, directory name completion is attempted.
<P>

If the <B>-o plusdirs</B> option was supplied to <B>complete</B> when the
compspec was defined, directory name completion is attempted and any
matches are added to the results of the other actions.
<P>

By default, if a compspec is found, whatever it generates is returned
to the completion code as the full set of possible completions.
The default <B>bash</B> completions are not attempted, and the readline
default of filename completion is disabled.
If the <B>-o bashdefault</B> option was supplied to <B>complete</B> when
the compspec was defined, the <B>bash</B> default completions are attempted
if the compspec generates no matches.
If the <B>-o default</B> option was supplied to <B>complete</B> when the
compspec was defined, readline's default completion will be performed
if the compspec (and, if attempted, the default <B>bash</B> completions)
generate no matches.
<P>

When a compspec indicates that directory name completion is desired,
the programmable completion functions force readline to append a slash
to completed names which are symbolic links to directories, subject to
the value of the <B>mark-directories</B> readline variable, regardless
of the setting of the <B>mark-symlinked-directories</B> readline variable.
<P>

There is some support for dynamically modifying completions.  This is
most useful when used in combination with a default completion specified
with <B>complete -D</B>.
It's possible for shell functions executed as completion
handlers to indicate that completion should be retried by returning an
exit status of 124.  If a shell function returns 124, and changes
the compspec associated with the command on which completion is being
attempted (supplied as the first argument when the function is executed),
programmable completion restarts from the beginning, with an
attempt to find a new compspec for that command.  This allows a set of
completions to be built dynamically as completion is attempted, rather than
being loaded all at once.
<P>

For instance, assuming that there is a library of compspecs, each kept in a
file corresponding to the name of the command, the following default
completion function would load completions dynamically:
<P>

<TT>_completion_loader()
<BR>

{
<BR>

<TT>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TT>. &quot;/etc/bash_completion.d/$1.sh&quot; &gt;/dev/null 2&gt;&amp;1 &amp;&amp; return 124<BR>
<BR>

}
<BR>

complete -D -F _completion_loader -o bashdefault -o default
<BR>

</TT>
<A NAME="lbCW">&nbsp;</A>
<H3>HISTORY</H3>

When the
<B>-o history</B>

option to the
<B>set</B>

builtin is enabled, the shell provides access to the
<I>command history</I>,
the list of commands previously typed.
The value of the
<FONT SIZE=-1><B>HISTSIZE</B>

</FONT>
variable is used as the
number of commands to save in a history list.
The text of the last
<FONT SIZE=-1><B>HISTSIZE</B>

</FONT>
commands (default 500) is saved.  The shell
stores each command in the history list prior to parameter and
variable expansion (see
<FONT SIZE=-1><B>EXPANSION</B>

</FONT>
above) but after history expansion is performed, subject to the
values of the shell variables
<FONT SIZE=-1><B>HISTIGNORE</B>

</FONT>
and
<FONT SIZE=-1><B>HISTCONTROL</B>.

</FONT>
<P>

On startup, the history is initialized from the file named by
the variable
<FONT SIZE=-1><B>HISTFILE</B>

</FONT>
(default <A HREF="file:~/.bash_history"><I>~/.bash_history</I></A>).
The file named by the value of
<FONT SIZE=-1><B>HISTFILE</B>

</FONT>
is truncated, if necessary, to contain no more than
the number of lines specified by the value of
<FONT SIZE=-1><B>HISTFILESIZE</B>.

</FONT>
If <B>HISTFILESIZE</B> is unset, or set to null, a non-numeric value,
or a numeric value less than zero, the history file is not truncated.
When the history file is read,
lines beginning with the history comment character followed immediately
by a digit are interpreted as timestamps for the following history line.
These timestamps are optionally displayed depending on the value of the
<FONT SIZE=-1><B>HISTTIMEFORMAT</B>

</FONT>
variable.
When a shell with history enabled exits, the last
<FONT SIZE=-1><B>$HISTSIZE</B>

</FONT>
lines are copied from the history list to
<FONT SIZE=-1><B>$HISTFILE</B>.

</FONT>
If the
<B>histappend</B>

shell option is enabled
(see the description of
<B>shopt</B>

under
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below), the lines are appended to the history file,
otherwise the history file is overwritten.
If
<FONT SIZE=-1><B>HISTFILE</B>

</FONT>
is unset, or if the history file is unwritable, the history is
not saved.
If the
<FONT SIZE=-1><B>HISTTIMEFORMAT</B>

</FONT>
variable is set, time stamps are written to the history file, marked
with the history comment character, so
they may be preserved across shell sessions.
This uses the history comment character to distinguish timestamps from
other history lines.
After saving the history, the history file is truncated
to contain no more than
<FONT SIZE=-1><B>HISTFILESIZE</B>

</FONT>
lines.  If
<FONT SIZE=-1><B>HISTFILESIZE</B>

</FONT>
is unset, or set to null, a non-numeric value,
or a numeric value less than zero, the history file is not truncated.
<P>

The builtin command
<B>fc</B>

(see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below) may be used to list or edit and re-execute a portion of
the history list.
The
<B>history</B>

builtin may be used to display or modify the history list and
manipulate the history file.
When using command-line editing, search commands
are available in each editing mode that provide access to the
history list.
<P>

The shell allows control over which commands are saved on the history
list.  The
<FONT SIZE=-1><B>HISTCONTROL</B>

</FONT>
and
<FONT SIZE=-1><B>HISTIGNORE</B>

</FONT>
variables may be set to cause the shell to save only a subset of the
commands entered.
The
<B>cmdhist</B>

shell option, if enabled, causes the shell to attempt to save each
line of a multi-line command in the same history entry, adding
semicolons where necessary to preserve syntactic correctness.
The
<B>lithist</B>

shell option causes the shell to save the command with embedded newlines
instead of semicolons.  See the description of the
<B>shopt</B>

builtin below under
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
for information on setting and unsetting shell options.
<A NAME="lbCX">&nbsp;</A>
<H3>HISTORY EXPANSION</H3>

The shell supports a history expansion feature that
is similar to the history expansion in
<B>csh</B>.

This section describes what syntax features are available.  This
feature is enabled by default for interactive shells, and can be
disabled using the
<B>+H</B>

option to the
<B>set</B>

builtin command (see
<FONT SIZE=-1><B>SHELL BUILTIN COMMANDS</B>

</FONT>
below).  Non-interactive shells do not perform history expansion
by default.
<P>

History expansions introduce words from the history list into
the input stream, making it easy to repeat commands, insert the
arguments to a previous command into the current input line, or
fix errors in previous commands quickly.
<P>

History expansion is performed immediately after a complete line
is read, before the shell breaks it into words, and is performed
on each line individually without taking quoting on previous lines into
account.
It takes place in two parts.
The first is to determine which line from the history list
to use during substitution.
The second is to select portions of that line for inclusion into
the current one.
The line selected from the history is the <I>event</I>,
and the portions of that line that are acted upon are <I>words</I>.
Various <I>modifiers</I> are available to manipulate the selected words.
The line is broken into words in the same fashion as when reading input,
so that several <I>metacharacter</I>-separated words surrounded by
quotes are considered one word.
History expansions are introduced by the appearance of the
history expansion character, which is <B>!</B> by default.
Only backslash (<B>\</B>) and single quotes can quote
the history expansion character, but the history expansion character is
also treated as quoted if it immediately precedes the closing double quote
in a double-quoted string.
<P>

Several characters inhibit history expansion if found immediately
following the history expansion character, even if it is unquoted:
space, tab, newline, carriage return, and <B>=</B>.
If the <B>extglob</B> shell option is enabled, <B>(</B> will also
inhibit expansion.
<P>

Several shell options settable with the
<B>shopt</B>

builtin may be used to tailor the behavior of history expansion.
If the
<B>histverify</B>

shell option is enabled (see the description of the
<B>shopt</B>

builtin below), and
<B>readline</B>

is being used, history substitutions are not immediately passed to
the shell parser.
Instead, the expanded line is reloaded into the
<B>readline</B>

editing buffer for further modification.
If
<B>readline</B>

is being used, and the
<B>histreedit</B>

shell option is enabled, a failed history substitution will be reloaded
into the
<B>readline</B>

editing buffer for correction.
The
<B>-p</B>

option to the
<B>history</B>

builtin command may be used to see what a history expansion will
do before using it.
The
<B>-s</B>

option to the
<B>history</B>

builtin may be used to add commands to the end of the history list
without actually executing them, so that they are available for
subsequent recall.
<P>

The shell allows control of the various characters used by the
history expansion mechanism (see the description of
<B>histchars</B>

above under
<B>Shell Variables</B>).

The shell uses
the history comment character to mark history timestamps when
writing the history file.
<A NAME="lbCY">&nbsp;</A>
<H4>Event Designators</H4>

An event designator is a reference to a command line entry in the
history list.
Unless the reference is absolute, events are relative to the current
position in the history list.
<P>


<DL COMPACT>
<DT><B>!</B>

<DD>
Start a history substitution, except when followed by a
<B>blank</B>,

newline, carriage return, =
or ( (when the <B>extglob</B> shell option is enabled using
the <B>shopt</B> builtin).
<DT><B>!</B><I>n</I>

<DD>
Refer to command line
<I>n</I>.

<DT><B>!-</B><I>n</I>

<DD>
Refer to the current command minus
<I>n</I>.

<DT><B>!!</B>

<DD>
Refer to the previous command.  This is a synonym for `!-1'.
<DT><B>!</B><I>string</I>

<DD>
Refer to the most recent command preceding the current position in the
history list starting with
<I>string</I>.

<DT><B>!?</B><I>string</I><B>[?]</B>

<DD>
Refer to the most recent command preceding the current position in the
history list containing
<I>string</I>.

The trailing <B>?</B> may be omitted if
<I>string</I>

is followed immediately by a newline.
If <I>string</I> is missing, the string from the most recent search is used;
it is an error if there is no previous search string.
<DT><B></B><FONT SIZE=+2><B>^</B></FONT><B></B><I>string1</I><FONT SIZE=+2>^</FONT><I>string2</I><FONT SIZE=+2>^</FONT>

<DD>
Quick substitution.  Repeat the previous command, replacing
<I>string1</I>

with
<I>string2</I>.

Equivalent to
``!!:s<FONT SIZE=+2>^</FONT><I>string1</I><FONT SIZE=+2>^</FONT><I>string2</I><FONT SIZE=+2>^</FONT>''
(see <B>Modifiers</B> below).
<DT><B>!#</B>

<DD>
The entire command line typed so far.

</DL>
<A NAME="lbCZ">&nbsp;</A>
<H4>Word Designators</H4>

Word designators are used to select desired words from the event.
A
<B>:</B>

separates the event specification from the word designator.
It may be omitted if the word designator begins with a
<B>^</B>,

<B>$</B>,

<B>*</B>,

<B>-</B>,

or
<B>%</B>.

Words are numbered from the beginning of the line,
with the first word being denoted by 0 (zero).
Words are inserted into the current line separated by single spaces.
<P>


<DL COMPACT>
<DT><B>0 (zero)</B>

<DD>
The zeroth word.  For the shell, this is the command
word.
<DT><I>n</I>

<DD>
The <I>n</I>th word.
<DT><B>^</B>

<DD>
The first argument.  That is, word 1.
<DT><B>$</B>

<DD>
The last word.  This is usually the last argument, but will expand to the
zeroth word if there is only one word in the line.
<DT><B>%</B>

<DD>
The first word matched by the most recent `?<I>string</I>?' search,
if the search string begins with a character that is part of a word.
<DT><I>x</I><B>-</B>y

<DD>
A range of words; `-<I>y</I>' abbreviates `0-<I>y</I>'.
<DT><B>*</B>

<DD>
All of the words but the zeroth.  This is a synonym
for `<I>1-$</I>'.  It is not an error to use
<B>*</B>

if there is just one
word in the event; the empty string is returned in that case.
<DT><B>x*</B>

<DD>
Abbreviates <I>x-$</I>.
<DT><B>x-</B>

<DD>
Abbreviates <I>x-$</I> like <B>x*</B>, but omits the last word.
If <B>x</B> is missing, it defaults to 0.

</DL>
<P>

If a word designator is supplied without an event specification, the
previous command is used as the event.
<A NAME="lbDA">&nbsp;</A>
<H4>Modifiers</H4>

After the optional word designator, there may appear a sequence of
one or more of the following modifiers, each preceded by a `:'.
These modify, or edit, the word or words selected from the history event.
<P>


<DL COMPACT>
<DT><B>h</B>

<DD>
Remove a trailing filename component, leaving only the head.
<DT><B>t</B>

<DD>
Remove all leading filename components, leaving the tail.
<DT><B>r</B>

<DD>
Remove a trailing suffix of the form <I>.xxx</I>, leaving the
basename.
<DT><B>e</B>

<DD>
Remove all but the trailing suffix.
<DT><B>p</B>

<DD>
Print the new command but do not execute it.
<DT><B>q</B>

<DD>
Quote the substituted words, escaping further substitutions.
<DT><B>x</B>

<DD>
Quote the substituted words as with
<B>q</B>,

but break into words at
<B>blanks</B>

and newlines.
The <B>q</B> and <B>x</B> modifiers are mutually exclusive; the last one
supplied is used.
<DT><B>s/</B><I>old</I>/<I>new</I>/

<DD>
Substitute
<I>new</I>

for the first occurrence of
<I>old</I>

in the event line.
Any character may be used as the delimiter in place of /.
The final delimiter is optional if it is the last character of the
event line.
The delimiter may be quoted in
<I>old</I>

and
<I>new</I>

with a single backslash.  If &amp; appears in
<I>new</I>,

it is replaced by
<I>old</I>.

A single backslash will quote the &amp;.
If
<I>old</I>

is null, it is set to the last
<I>old</I>

substituted, or, if no previous history substitutions took place,
the last
<I>string</I>

in a
<B>!?</B><I>string</I><B>[?]</B>

search.
If
<I>new</I>

is null, each matching
<I>old</I>

is deleted.
<DT><B>&amp;</B>

<DD>
Repeat the previous substitution.
<DT><B>g</B>

<DD>
Cause changes to be applied over the entire event line.  This is
used in conjunction with `<B>:s</B>' (e.g., `<B>:gs/</B><I>old</I>/<I>new</I>/')
or `<B>:&amp;</B>'.  If used with
`<B>:s</B>', any delimiter can be used
in place of /, and the final delimiter is optional
if it is the last character of the event line.
An <B>a</B> may be used as a synonym for <B>g</B>.
<DT><B>G</B>

<DD>
Apply the following `<B>s</B>' or `<B>&amp;</B>' modifier once to each word
in the event line.

</DL>
<A NAME="lbDB">&nbsp;</A>
<H3>SHELL BUILTIN COMMANDS</H3>



<P>

Unless otherwise noted, each builtin command documented in this
section as accepting options preceded by
<B>-</B>

accepts
<B>--</B>

to signify the end of the options.
The <B>:</B>, <B>true</B>, <B>false</B>, and <B>test</B>/<B>[</B> builtins
do not accept options and do not treat <B>--</B> specially.
The <B>exit</B>, <B>logout</B>, <B>return</B>,
<B>break</B>, <B>continue</B>, <B>let</B>,
and <B>shift</B> builtins accept and process arguments beginning with
<B>-</B> without requiring <B>--</B>.
Other builtins that accept arguments but are not specified as accepting
options interpret arguments beginning with <B>-</B> as invalid options and
require <B>--</B> to prevent this interpretation.
<P>

<DL COMPACT>
<DT><B>:</B> [<I>arguments</I>]<DD>

No effect; the command does nothing beyond expanding
<I>arguments</I>

and performing any specified
redirections.
The return status is zero.
<DT><B> . </B> <I>filename</I> [<I>arguments</I>]<DD>

<DT><B>source</B> <I>filename</I> [<I>arguments</I>]<DD>

Read and execute commands from
<I>filename</I>

in the current
shell environment and return the exit status of the last command
executed from
<I>filename</I>.

If
<I>filename</I>

does not contain a slash, filenames in
<FONT SIZE=-1><B>PATH</B>

</FONT>
are used to find the directory containing
<I>filename</I>,

but <I>filename</I> does not need to be executable.
The file searched for in
<FONT SIZE=-1><B>PATH</B>

</FONT>
need not be executable.
When <B>bash</B> is not in <I>posix mode</I>, it searches
the current directory if no file is found in
<FONT SIZE=-1><B>PATH</B>.

</FONT>
If the
<B>sourcepath</B>

option to the
<B>shopt</B>

builtin command is turned off, the
<FONT SIZE=-1><B>PATH</B>

</FONT>
is not searched.
If any <I>arguments</I> are supplied, they become the positional
parameters when <I>filename</I> is executed.  Otherwise the positional
parameters are unchanged.
If the <B>-T</B> option is enabled, <B>.</B> inherits any trap on
<B>DEBUG</B>; if it is not, any <B>DEBUG</B> trap string is saved and
restored around the call to <B>.</B>, and <B>.</B> unsets the
<B>DEBUG</B> trap while it executes.
If <B>-T</B> is not set, and the sourced file changes
the <B>DEBUG</B> trap, the new value is retained when <B>.</B> completes.
The return status is the status of the last command exited within
the script (0 if no commands are executed), and false if
<I>filename</I>

is not found or cannot be read.
<DT><B>alias</B> [<B>-p</B>] [<I>name</I>[=<I>value</I>] ...]<DD>
<B>Alias</B> with no arguments or with the
<B>-p</B>

option prints the list of aliases in the form
<B>alias</B> <I>name</I>=<I>value</I> on standard output.
When arguments are supplied, an alias is defined for
each <I>name</I> whose <I>value</I> is given.
A trailing space in <I>value</I> causes the next word to be
checked for alias substitution when the alias is expanded.
For each <I>name</I> in the argument list for which no <I>value</I>
is supplied, the name and value of the alias is printed.
<B>Alias</B> returns true unless a <I>name</I> is given for which
no alias has been defined.
<DT><B>bg</B> [<I>jobspec</I> ...]<DD>
Resume each suspended job <I>jobspec</I> in the background, as if it
had been started with
<B>&amp;</B>.

If
<I>jobspec</I>

is not present, the shell's notion of the <I>current job</I> is used.
<B>bg</B>

<I>jobspec</I>

returns 0 unless run when job control is disabled or, when run with
job control enabled, any specified <I>jobspec</I> was not found
or was started without job control.
<DT><B>bind</B> [<B>-m</B> <I>keymap</I>] [<B>-lpsvPSVX</B>]<DD>

<DT><B>bind</B> [<B>-m</B> <I>keymap</I>] [<B>-q</B> <I>function</I>] [<B>-u</B> <I>function</I>] [<B>-r</B> <I>keyseq</I>]<DD>
<DT><B>bind</B> [<B>-m</B> <I>keymap</I>] <B>-f</B> <I>filename</I><DD>
<DT><B>bind</B> [<B>-m</B> <I>keymap</I>] <B>-x</B> <I>keyseq</I>:<I>shell-command</I><DD>
<DT><B>bind</B> [<B>-m</B> <I>keymap</I>] <I>keyseq</I>:<I>function-name</I><DD>
<DT><B>bind</B> [<B>-m</B> <I>keymap</I>] <I>keyseq</I>:<I>readline-command</I><DD>
<DT><B>bind</B> <I>readline-command-line</I><DD>

Display current
<B>readline</B>

key and function bindings, bind a key sequence to a
<B>readline</B>

function or macro, or set a
<B>readline</B>

variable.
Each non-option argument is a command as it would appear in a
<B>readline</B>

initialization file such as
<I>.inputrc</I>,

but each binding or command must be passed as a separate argument;
e.g., '&quot;\C-x\C-r&quot;: re-read-init-file'.
Options, if supplied, have the following meanings:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-m </B><I>keymap</I>

<DD>
Use
<I>keymap</I>

as the keymap to be affected by the subsequent bindings.
Acceptable
<I>keymap</I>

names are
<I>emacs, emacs-standard, emacs-meta, emacs-ctlx, vi,
vi-move, vi-command</I>, and
<I>vi-insert</I>.

<I>vi</I> is equivalent to <I>vi-command</I> (<I>vi-move</I> is also
a synonym); <I>emacs</I> is
equivalent to <I>emacs-standard</I>.
<DT><B>-l</B>

<DD>
List the names of all <B>readline</B> functions.
<DT><B>-p</B>

<DD>
Display <B>readline</B> function names and bindings in such a way
that they can be re-read.
<DT><B>-P</B>

<DD>
List current <B>readline</B> function names and bindings.
<DT><B>-s</B>

<DD>
Display <B>readline</B> key sequences bound to macros and the strings
they output in such a way that they can be re-read.
<DT><B>-S</B>

<DD>
Display <B>readline</B> key sequences bound to macros and the strings
they output.
<DT><B>-v</B>

<DD>
Display <B>readline</B> variable names and values in such a way that they
can be re-read.
<DT><B>-V</B>

<DD>
List current <B>readline</B> variable names and values.
<DT><B>-f </B><I>filename</I>

<DD>
Read key bindings from <I>filename</I>.
<DT><B>-q </B><I>function</I>

<DD>
Query about which keys invoke the named <I>function</I>.
<DT><B>-u </B><I>function</I>

<DD>
Unbind all keys bound to the named <I>function</I>.
<DT><B>-r </B><I>keyseq</I>

<DD>
Remove any current binding for <I>keyseq</I>.
<DT><B>-x </B><I>keyseq</I>:<I>shell-command</I>

<DD>
Cause <I>shell-command</I> to be executed whenever <I>keyseq</I> is
entered.
When <I>shell-command</I> is executed, the shell sets the
<FONT SIZE=-1><B>READLINE_LINE</B>

</FONT>
variable to the contents of the <B>readline</B> line buffer and the
<FONT SIZE=-1><B>READLINE_POINT</B>

</FONT>
and
<FONT SIZE=-1><B>READLINE_MARK</B>

</FONT>
variables to the current location of the insertion point and the saved
insertion point (the mark), respectively.
The shell assigns any numeric argument the user supplied to the     
<FONT SIZE=-1><B>READLINE_ARGUMENT</B>

</FONT>
variable.
If there was no argument, that variable is not set.
If the executed command changes the value of any of
<FONT SIZE=-1><B>READLINE_LINE</B>,

</FONT>
<FONT SIZE=-1><B>READLINE_POINT</B>,

</FONT>
or
<FONT SIZE=-1><B>READLINE_MARK</B>,

</FONT>
those new values will be reflected in the editing state.
<DT><B>-X</B>

<DD>
List all key sequences bound to shell commands and the associated commands
in a format that can be reused as input.

</DL>
<P>

The return value is 0 unless an unrecognized option is given or an
error occurred.
</DL>

<DT><B>break</B> [<I>n</I>]<DD>
Exit from within a
<B>for</B>,

<B>while</B>,

<B>until</B>,

or
<B>select</B>

loop.  If <I>n</I> is specified, break <I>n</I> levels.
<I>n</I>

must be >= 1.  If
<I>n</I>

is greater than the number of enclosing loops, all enclosing loops
are exited.
The return value is 0 unless <I>n</I> is not greater than or equal to 1.
<DT><B>builtin</B> <I>shell-builtin</I> [<I>arguments</I>]<DD>
Execute the specified shell builtin, passing it
<I>arguments</I>,

and return its exit status.
This is useful when defining a
function whose name is the same as a shell builtin,
retaining the functionality of the builtin within the function.
The <B>cd</B> builtin is commonly redefined this way.
The return status is false if
<I>shell-builtin</I>

is not a shell builtin command.
<DT><B>caller</B> [<I>expr</I>]<DD>
Returns the context of any active subroutine call (a shell function or
a script executed with the <B>.</B> or <B>source</B> builtins).
Without <I>expr</I>, <B>caller</B> displays the line number and source
filename of the current subroutine call.
If a non-negative integer is supplied as <I>expr</I>, <B>caller</B>
displays the line number, subroutine name, and source file corresponding
to that position in the current execution call stack.  This extra
information may be used, for example, to print a stack trace.  The
current frame is frame 0.
The return value is 0 unless the shell is not executing a subroutine
call or <I>expr</I> does not correspond to a valid position in the
call stack.
<DT><B>cd</B> [<B>-L</B>|[<B>-P</B> [<B>-e</B>]] [-@]] [<I>dir</I>]<DD>
Change the current directory to <I>dir</I>.
if <I>dir</I> is not supplied, the value of the
<FONT SIZE=-1><B>HOME</B>

</FONT>
shell variable is the default.
The variable
<FONT SIZE=-1><B>CDPATH</B>

</FONT>
defines the search path for the directory containing
<I>dir</I>:

each directory name in
<FONT SIZE=-1><B>CDPATH</B>

</FONT>
is searched for <I>dir</I>.
Alternative directory names in
<FONT SIZE=-1><B>CDPATH</B>

</FONT>
are separated by a colon (:).  A null directory name in
<FONT SIZE=-1><B>CDPATH</B>

</FONT>
is the same as the current directory, i.e., ``<B>.</B>''.  If
<I>dir</I>

begins with a slash (/),
then
<FONT SIZE=-1><B>CDPATH</B>

</FONT>
is not used.  The
<B>-P</B>

option causes <B>cd</B> to use the physical directory structure
by resolving symbolic links while traversing <I>dir</I> and
before processing instances of <I>..</I> in <I>dir</I> (see also the
<B>-P</B>

option to the
<B>set</B>

builtin command); the
<B>-L</B>

option forces symbolic links to be followed by resolving the link
after processing instances of <I>..</I> in <I>dir</I>.
If <I>..</I> appears in <I>dir</I>, it is processed by removing the
immediately previous pathname component from <I>dir</I>, back to a slash
or the beginning of <I>dir</I>.
If the
<B>-e</B>

option is supplied with
<B>-P</B>,

and the current working directory cannot be successfully determined
after a successful directory change, <B>cd</B> will return an unsuccessful
status.
On systems that support it, the <B>-@</B> option presents the extended
attributes associated with a file as a directory.
An argument of
<B>-</B>

is converted to
<FONT SIZE=-1><B>$OLDPWD</B>

</FONT>
before the directory change is attempted.
If a non-empty directory name from
<FONT SIZE=-1><B>CDPATH</B>

</FONT>
is used, or if
<B>-</B> is the first argument, and the directory change is
successful, the absolute pathname of the new working directory is
written to the standard output.
If the directory change is successful, <B>cd</B> sets the value of the
<B>PWD</B> environment variable to the new directory name, and sets the
<B>OLDPWD</B> environment variable to the value of the current working
directory before the change.
The return value is true if the directory was successfully changed;
false otherwise.
<DT><B>command</B> [<B>-pVv</B>] <I>command</I> [<I>arg</I> ...]<DD>
Run
<I>command</I>

with
<I>args</I>

suppressing the normal shell function lookup.
Only builtin commands or commands found in the
<FONT SIZE=-1><B>PATH</B>

</FONT>
are executed.  If the
<B>-p</B>

option is given, the search for
<I>command</I>

is performed using a default value for
<FONT SIZE=-1><B>PATH</B>

</FONT>
that is guaranteed to find all of the standard utilities.
If either the
<B>-V</B>

or
<B>-v</B>

option is supplied, a description of
<I>command</I>

is printed.  The
<B>-v</B>

option causes a single word indicating the command or filename
used to invoke
<I>command</I>

to be displayed; the
<B>-V</B>

option produces a more verbose description.
If the
<B>-V</B>

or
<B>-v</B>

option is supplied, the exit status is 0 if
<I>command</I>

was found, and 1 if not.  If neither option is supplied and
an error occurred or
<I>command</I>

cannot be found, the exit status is 127.  Otherwise, the exit status of the
<B>command</B>

builtin is the exit status of
<I>command</I>.

<DT><B>compgen</B> [<I>option</I>] [<I>word</I>]<DD>
Generate possible completion matches for <I>word</I> according to
the <I>option</I>s, which may be any option accepted by the
<B>complete</B>

builtin with the exception of <B>-p</B> and <B>-r</B>, and write
the matches to the standard output.
When using the <B>-F</B> or <B>-C</B> options, the various shell variables
set by the programmable completion facilities, while available, will not
have useful values.
<P>
The matches will be generated in the same way as if the programmable
completion code had generated them directly from a completion specification
with the same flags.
If <I>word</I> is specified, only those completions matching <I>word</I>
will be displayed.
<P>
The return value is true unless an invalid option is supplied, or no
matches were generated.
<DT><B>complete</B> [<B>-abcdefgjksuv</B>] [<B>-o</B> <I>comp-option</I>] [<B>-DEI</B>] [<B>-A</B> <I>action</I>] [<B>-G</B> <I>globpat</I>] [<B>-W</B> <I>wordlist</I>]<DD>
<BR>

[<B>-F</B> <I>function</I>] [<B>-C</B> <I>command</I>] [<B>-X</B> <I>filterpat</I>] [<B>-P</B> <I>prefix</I>] [<B>-S</B> <I>suffix</I>] <I>name</I> [<I>name ...</I>]

<DT><B>complete</B> <B>-pr</B> [<B>-DEI</B>] [<I>name</I> ...]<DD>

Specify how arguments to each <I>name</I> should be completed.
If the <B>-p</B> option is supplied, or if no options are supplied,
existing completion specifications are printed in a way that allows
them to be reused as input.
The <B>-r</B> option removes a completion specification for
each <I>name</I>, or, if no <I>name</I>s are supplied, all
completion specifications.
The <B>-D</B> option indicates that other supplied options and actions should
apply to the ``default'' command completion; that is, completion attempted
on a command for which no completion has previously been defined.
The <B>-E</B> option indicates that other supplied options and actions should
apply to ``empty'' command completion; that is, completion attempted on a
blank line.
The <B>-I</B> option indicates that other supplied options and actions should
apply to completion on the initial non-assignment word on the line, or after
a command delimiter such as <B>;</B> or <B>|</B>, which is usually command
name completion.
If multiple options are supplied, the <B>-D</B> option takes precedence
over <B>-E</B>, and both take precedence over <B>-I</B>.
If any of <B>-D</B>, <B>-E</B>, or <B>-I</B> are supplied, any other
<I>name</I> arguments are ignored; these completions only apply to the case
specified by the option.
<P>
The process of applying these completion specifications when word completion
is attempted is described

above under <B>Programmable Completion</B>.
<P>
Other options, if specified, have the following meanings.
The arguments to the <B>-G</B>, <B>-W</B>, and <B>-X</B> options
(and, if necessary, the <B>-P</B> and <B>-S</B> options)
should be quoted to protect them from expansion before the
<B>complete</B>

builtin is invoked.
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-o</B> <I>comp-option</I><DD>
The <I>comp-option</I> controls several aspects of the compspec's behavior
beyond the simple generation of completions.
<I>comp-option</I> may be one of:
<DL COMPACT><DT><DD>
<DL COMPACT>
<DT><B>bashdefault</B>

<DD>
Perform the rest of the default <B>bash</B> completions if the compspec
generates no matches.
<DT><B>default</B>

<DD>
Use readline's default filename completion if the compspec generates
no matches.
<DT><B>dirnames</B>

<DD>
Perform directory name completion if the compspec generates no matches.
<DT><B>filenames</B>

<DD>
Tell readline that the compspec generates filenames, so it can perform any
filename-specific processing (like adding a slash to directory names,
quoting special characters, or suppressing trailing spaces).
Intended to be used with shell functions.
<DT><B>noquote</B>

<DD>
Tell readline not to quote the completed words if they are filenames
(quoting filenames is the default).
<DT><B>nosort</B>

<DD>
Tell readline not to sort the list of possible completions alphabetically.
<DT><B>nospace</B>

<DD>
Tell readline not to append a space (the default) to words completed at
the end of the line.
<DT><B>plusdirs</B>

<DD>
After any matches defined by the compspec are generated,
directory name completion is attempted and any
matches are added to the results of the other actions.
</DL></DL>

<DT><B>-A</B> <I>action</I><DD>
The <I>action</I> may be one of the following to generate a list of possible
completions:
<DL COMPACT><DT><DD>
<DL COMPACT>
<DT><B>alias</B>

<DD>
Alias names.  May also be specified as <B>-a</B>.
<DT><B>arrayvar</B>

<DD>
Array variable names.
<DT><B>binding</B>

<DD>
<B>Readline</B> key binding names.
<DT><B>builtin</B>

<DD>
Names of shell builtin commands.  May also be specified as <B>-b</B>.
<DT><B>command</B>

<DD>
Command names.  May also be specified as <B>-c</B>.
<DT><B>directory</B>

<DD>
Directory names.  May also be specified as <B>-d</B>.
<DT><B>disabled</B>

<DD>
Names of disabled shell builtins.
<DT><B>enabled</B>

<DD>
Names of enabled shell builtins.
<DT><B>export</B>

<DD>
Names of exported shell variables.  May also be specified as <B>-e</B>.
<DT><B>file</B>

<DD>
File names.  May also be specified as <B>-f</B>.
<DT><B>function</B>

<DD>
Names of shell functions.
<DT><B>group</B>

<DD>
Group names.  May also be specified as <B>-g</B>.
<DT><B>helptopic</B>

<DD>
Help topics as accepted by the <B>help</B> builtin.
<DT><B>hostname</B>

<DD>
Hostnames, as taken from the file specified by the
<FONT SIZE=-1><B>HOSTFILE</B>

</FONT>
shell variable.
<DT><B>job</B>

<DD>
Job names, if job control is active.  May also be specified as <B>-j</B>.
<DT><B>keyword</B>

<DD>
Shell reserved words.  May also be specified as <B>-k</B>.
<DT><B>running</B>

<DD>
Names of running jobs, if job control is active.
<DT><B>service</B>

<DD>
Service names.  May also be specified as <B>-s</B>.
<DT><B>setopt</B>

<DD>
Valid arguments for the <B>-o</B> option to the <B>set</B> builtin.
<DT><B>shopt</B>

<DD>
Shell option names as accepted by the <B>shopt</B> builtin.
<DT><B>signal</B>

<DD>
Signal names.
<DT><B>stopped</B>

<DD>
Names of stopped jobs, if job control is active.
<DT><B>user</B>

<DD>
User names.  May also be specified as <B>-u</B>.
<DT><B>variable</B>

<DD>
Names of all shell variables.  May also be specified as <B>-v</B>.
</DL></DL>

<DT><B>-C</B> <I>command</I><DD>
<I>command</I> is executed in a subshell environment, and its output is
used as the possible completions.
Arguments are passed as with the <B>-F</B> option.
<DT><B>-F</B> <I>function</I><DD>
The shell function <I>function</I> is executed in the current shell
environment.
When the function is executed,
the first argument (<B>$1</B>) is the name of the command whose arguments are
being completed,
the second argument (<B>$2</B>) is the word being completed,
and the third argument (<B>$3</B>) is the word preceding the word being
completed on the current command line.
When it finishes, the possible completions are retrieved from the value
of the
<FONT SIZE=-1><B>COMPREPLY</B>

</FONT>
array variable.
<DT><B>-G</B> <I>globpat</I><DD>
The pathname expansion pattern <I>globpat</I> is expanded to generate
the possible completions.
<DT><B>-P</B> <I>prefix</I><DD>
<I>prefix</I> is added at the beginning of each possible completion
after all other options have been applied.
<DT><B>-S</B> <I>suffix</I><DD>
<I>suffix</I> is appended to each possible completion
after all other options have been applied.
<DT><B>-W</B> <I>wordlist</I><DD>
The <I>wordlist</I> is split using the characters in the
<FONT SIZE=-1><B>IFS</B>

</FONT>
special variable as delimiters, and each resultant word is expanded.
Shell quoting is honored within <I>wordlist</I>,
in order to provide a
mechanism for the words to contain shell metacharacters or characters
in the value of
<FONT SIZE=-1><B>IFS</B>.

</FONT>
The possible completions are the members of the resultant list which
match the word being completed.
<DT><B>-X</B> <I>filterpat</I><DD>
<I>filterpat</I> is a pattern as used for pathname expansion.
It is applied to the list of possible completions generated by the
preceding options and arguments, and each completion matching
<I>filterpat</I> is removed from the list.
A leading <B>!</B> in <I>filterpat</I> negates the pattern; in this
case, any completion not matching <I>filterpat</I> is removed.

</DL>
<P>

The return value is true unless an invalid option is supplied, an option
other than <B>-p</B> or <B>-r</B> is supplied without a <I>name</I>
argument, an attempt is made to remove a completion specification for
a <I>name</I> for which no specification exists, or
an error occurs adding a completion specification.
</DL>

<DT><B>compopt</B> [<B>-o</B> <I>option</I>] [<B>-DEI</B>] [<B>+o</B> <I>option</I>] [<I>name</I>]<DD>
Modify completion options for each <I>name</I> according to the
<I>option</I>s, or for the
currently-executing completion if no <I>name</I>s are supplied.
If no <I>option</I>s are given, display the completion options for each
<I>name</I> or the current completion.
The possible values of <I>option</I> are those valid for the <B>complete</B>
builtin described above.
The <B>-D</B> option indicates that other supplied options should
apply to the ``default'' command completion; that is, completion attempted
on a command for which no completion has previously been defined.
The <B>-E</B> option indicates that other supplied options should
apply to ``empty'' command completion; that is, completion attempted on a
blank line.
The <B>-I</B> option indicates that other supplied options should
apply to completion on the initial non-assignment word on the line,
or after a command delimiter such as <B>;</B> or <B>|</B>, which is usually
command name completion.
<P>
The return value is true unless an invalid option is supplied, an attempt
is made to modify the options for a <I>name</I> for which no completion
specification exists, or an output error occurs.
<DT><B>continue</B> [<I>n</I>]<DD>
Resume the next iteration of the enclosing
<B>for</B>,

<B>while</B>,

<B>until</B>,

or
<B>select</B>

loop.
If
<I>n</I>

is specified, resume at the <I>n</I>th enclosing loop.
<I>n</I>

must be >= 1.  If
<I>n</I>

is greater than the number of enclosing loops, the last enclosing loop
(the ``top-level'' loop) is resumed.
The return value is 0 unless <I>n</I> is not greater than or equal to 1.
<DT><B>declare</B> [<B>-aAfFgiIlnrtux</B>] [<B>-p</B>] [<I>name</I>[=<I>value</I>] ...]<DD>

<DT><B>typeset</B> [<B>-aAfFgiIlnrtux</B>] [<B>-p</B>] [<I>name</I>[=<I>value</I>] ...]<DD>

Declare variables and/or give them attributes.
If no <I>name</I>s are given then display the values of variables.
The
<B>-p</B>

option will display the attributes and values of each
<I>name</I>.

When
<B>-p</B>

is used with <I>name</I> arguments, additional options,
other than <B>-f</B> and <B>-F</B>, are ignored.
When
<B>-p</B>

is supplied without <I>name</I> arguments, it will display the attributes
and values of all variables having the attributes specified by the
additional options.
If no other options are supplied with <B>-p</B>, <B>declare</B> will display
the attributes and values of all shell variables.  The <B>-f</B> option
will restrict the display to shell functions.
The
<B>-F</B>

option inhibits the display of function definitions; only the
function name and attributes are printed.
If the <B>extdebug</B> shell option is enabled using <B>shopt</B>,
the source file name and line number where each <I>name</I>
is defined are displayed as well.  The
<B>-F</B>

option implies
<B>-f</B>.

The
<B>-g</B>

option forces variables to be created or modified at the global scope,
even when <B>declare</B> is executed in a shell function.
It is ignored in all other cases.
The
<B>-I</B>

option causes local variables to inherit the attributes
(except the <I>nameref</I> attribute) 
and value of any existing variable with the same
<I>name</I> at a surrounding scope.
If there is no existing variable, the local variable is initially unset.
The following options can
be used to restrict output to variables with the specified attribute or
to give variables attributes:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-a</B>

<DD>
Each <I>name</I> is an indexed array variable (see
<B>Arrays</B>


above).
<DT><B>-A</B>

<DD>
Each <I>name</I> is an associative array variable (see
<B>Arrays</B>


above).
<DT><B>-f</B>

<DD>
Use function names only.
<DT><B>-i</B>

<DD>
The variable is treated as an integer; arithmetic evaluation (see
<FONT SIZE=-1><B>ARITHMETIC EVALUATION</B>

</FONT>

above)
is performed when the variable is assigned a value.
<DT><B>-l</B>

<DD>
When the variable is assigned a value, all upper-case characters are
converted to lower-case.
The upper-case attribute is disabled.
<DT><B>-n</B>

<DD>
Give each <I>name</I> the <I>nameref</I> attribute, making
it a name reference to another variable.
That other variable is defined by the value of <I>name</I>.
All references, assignments, and attribute modifications
to <I>name</I>, except those using or changing the
<B>-n</B> attribute itself, are performed on the variable referenced by
<I>name</I>'s value.
The nameref attribute cannot be applied to array variables.
<DT><B>-r</B>

<DD>
Make <I>name</I>s readonly.  These names cannot then be assigned values
by subsequent assignment statements or unset.
<DT><B>-t</B>

<DD>
Give each <I>name</I> the <I>trace</I> attribute.
Traced functions inherit the <B>DEBUG</B> and <B>RETURN</B> traps from
the calling shell.
The trace attribute has no special meaning for variables.
<DT><B>-u</B>

<DD>
When the variable is assigned a value, all lower-case characters are
converted to upper-case.
The lower-case attribute is disabled.
<DT><B>-x</B>

<DD>
Mark <I>name</I>s for export to subsequent commands via the environment.

</DL>
<P>

Using `+' instead of `-'
turns off the attribute instead,
with the exceptions that <B>+a</B> and <B>+A</B>
may not be used to destroy array variables and <B>+r</B> will not
remove the readonly attribute.
When used in a function,
<B>declare</B>

and
<B>typeset</B>

make each
<I>name</I> local, as with the
<B>local</B>

command,
unless the <B>-g</B> option is supplied.
If a variable name is followed by =<I>value</I>, the value of
the variable is set to <I>value</I>.
When using <B>-a</B> or <B>-A</B> and the compound assignment syntax to
create array variables, additional attributes do not take effect until
subsequent assignments.
The return value is 0 unless an invalid option is encountered,
an attempt is made to define a function using

<TT>-f foo=bar</TT>,
an attempt is made to assign a value to a readonly variable,
an attempt is made to assign a value to an array variable without
using the compound assignment syntax (see
<B>Arrays</B>


above),
one of the <I>names</I> is not a valid shell variable name,
an attempt is made to turn off readonly status for a readonly variable,
an attempt is made to turn off array status for an array variable,
or an attempt is made to display a non-existent function with <B>-f</B>.
</DL>

<DT><B>dirs [-clpv</B>] [+<I>n</I>] [-<I>n</I>]

<DD>
Without options, displays the list of currently remembered directories.
The default display is on a single line with directory names separated
by spaces.
Directories are added to the list with the
<B>pushd</B>

command; the
<B>popd</B>

command removes entries from the list.
The current directory is always the first directory in the stack.
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-c</B>

<DD>
Clears the directory stack by deleting all of the entries.
<DT><B>-l</B>

<DD>
Produces a listing using full pathnames;
the default listing format uses a tilde to denote the home directory.
<DT><B>-p</B>

<DD>
Print the directory stack with one entry per line.
<DT><B>-v</B>

<DD>
Print the directory stack with one entry per line,
prefixing each entry with its index in the stack.
<DT><B>+</B><I>n</I><DD>
Displays the <I>n</I>th entry counting from the left of the list
shown by
<B>dirs</B>

when invoked without options, starting with zero.
<DT><B>-</B><I>n</I><DD>
Displays the <I>n</I>th entry counting from the right of the list
shown by
<B>dirs</B>

when invoked without options, starting with zero.

</DL>
<P>

The return value is 0 unless an
invalid option is supplied or <I>n</I> indexes beyond the end
of the directory stack.
</DL>

<DT><B>disown</B> [<B>-ar</B>] [<B>-h</B>] [<I>jobspec</I> ... | <I>pid</I> ... ]<DD>
Without options, remove each
<I>jobspec</I>

from the table of active jobs.
If
<I>jobspec</I>

is not present, and neither the <B>-a</B> nor the <B>-r</B> option
is supplied, the <I>current job</I> is used.
If the <B>-h</B> option is given, each
<I>jobspec</I>

is not removed from the table, but is marked so that
<FONT SIZE=-1><B>SIGHUP</B>

</FONT>
is not sent to the job if the shell receives a
<FONT SIZE=-1><B>SIGHUP</B>.

</FONT>
If no
<I>jobspec</I>

is supplied, the
<B>-a</B>

option means to remove or mark all jobs; the
<B>-r</B>

option without a
<I>jobspec</I>

argument restricts operation to running jobs.
The return value is 0 unless a
<I>jobspec</I>

does not specify a valid job.
<DT><B>echo</B> [<B>-neE</B>] [<I>arg</I> ...]<DD>
Output the <I>arg</I>s, separated by spaces, followed by a newline.
The return status is 0 unless a write error occurs.
If <B>-n</B> is specified, the trailing newline is
suppressed.  If the <B>-e</B> option is given, interpretation of
the following backslash-escaped characters is enabled.  The
<B>-E</B>

option disables the interpretation of these escape characters,
even on systems where they are interpreted by default.
The <B>xpg_echo</B> shell option may be used to
dynamically determine whether or not <B>echo</B> expands these
escape characters by default.
<B>echo</B>

does not interpret <B>--</B> to mean the end of options.
<B>echo</B>

interprets the following escape sequences:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>\a</B>

<DD>
alert (bell)
<DT><B>\b</B>

<DD>
backspace
<DT><B>\c</B>

<DD>
suppress further output
<DT><B>\e</B>

<DD>
<DT><B>\E</B>

<DD>
an escape character
<DT><B>\f</B>

<DD>
form feed
<DT><B>\n</B>

<DD>
new line
<DT><B>\r</B>

<DD>
carriage return
<DT><B>\t</B>

<DD>
horizontal tab
<DT><B>\v</B>

<DD>
vertical tab
<DT><B>\\</B>

<DD>
backslash
<DT><B>\0</B><I>nnn</I>

<DD>
the eight-bit character whose value is the octal value <I>nnn</I>
(zero to three octal digits)
<DT><B>\x</B><I>HH</I>

<DD>
the eight-bit character whose value is the hexadecimal value <I>HH</I>
(one or two hex digits)
<DT><B>\u</B><I>HHHH</I>

<DD>
the Unicode (ISO/IEC 10646) character whose value is the hexadecimal value
<I>HHHH</I> (one to four hex digits)
<DT><B>\U</B><I>HHHHHHHH</I>

<DD>
the Unicode (ISO/IEC 10646) character whose value is the hexadecimal value
<I>HHHHHHHH</I> (one to eight hex digits)

</DL></DL>

<DT><B>enable</B> [<B>-a</B>] [<B>-dnps</B>] [<B>-f</B> <I>filename</I>] [<I>name</I> ...]<DD>
Enable and disable builtin shell commands.
Disabling a builtin allows a disk command which has the same name
as a shell builtin to be executed without specifying a full pathname,
even though the shell normally searches for builtins before disk commands.
If <B>-n</B> is used, each <I>name</I>
is disabled; otherwise,
<I>names</I> are enabled.  For example, to use the
<B>test</B>

binary found via the
<FONT SIZE=-1><B>PATH</B>

</FONT>
instead of the shell builtin version, run
<TT>enable -n test</TT>.

The
<B>-f</B>

option means to load the new builtin command
<I>name</I>

from shared object
<I>filename</I>,

on systems that support dynamic loading.
Bash will use the value of the <B>BASH_LOADABLES_PATH</B> variable as a
colon-separated list of directories in which to search for <I>filename</I>.
The default is system-dependent.
The
<B>-d</B>

option will delete a builtin previously loaded with
<B>-f</B>.

If no <I>name</I> arguments are given, or if the
<B>-p</B>

option is supplied, a list of shell builtins is printed.
With no other option arguments, the list consists of all enabled
shell builtins.
If <B>-n</B> is supplied, only disabled builtins are printed.
If <B>-a</B> is supplied, the list printed includes all builtins, with an
indication of whether or not each is enabled.
If <B>-s</B> is supplied, the output is restricted to the POSIX
<I>special</I> builtins.
If no options are supplied and a <I>name</I> is not a shell builtin,
<B>enable</B> will attempt to load <I>name</I> from a shared object named
<I>name</I>, as if the command were
<TT>enable -f</TT> <I>name name</I> .

The return value is 0 unless a
<I>name</I>

is not a shell builtin or there is an error loading a new builtin
from a shared object.
<DT><B>eval</B> [<I>arg</I> ...]<DD>
The <I>arg</I>s are read and concatenated together into a single
command.  This command is then read and executed by the shell, and
its exit status is returned as the value of
<B>eval</B>.

If there are no
<I>args</I>,

or only null arguments,
<B>eval</B>

returns 0.
<DT><B>exec</B> [<B>-cl</B>] [<B>-a</B> <I>name</I>] [<I>command</I> [<I>arguments</I>]]<DD>
If
<I>command</I>

is specified, it replaces the shell.
No new process is created.  The
<I>arguments</I>

become the arguments to <I>command</I>.
If the
<B>-l</B>

option is supplied,
the shell places a dash at the beginning of the zeroth argument passed to
<I>command</I>.

This is what
<I>login</I>(1)

does.  The
<B>-c</B>

option causes
<I>command</I>

to be executed with an empty environment.  If
<B>-a</B>

is supplied, the shell passes
<I>name</I>

as the zeroth argument to the executed command.
If
<I>command</I>

cannot be executed for some reason, a non-interactive shell exits,
unless the
<B>execfail</B>

shell option
is enabled.  In that case, it returns failure.
An interactive shell returns failure if the file cannot be executed.
A subshell exits unconditionally if <B>exec</B> fails.
If
<I>command</I>

is not specified, any redirections take effect in the current shell,
and the return status is 0.  If there is a redirection error, the
return status is 1.
<DT><B>exit</B> [<I>n</I>]<DD>
Cause the shell to exit
with a status of <I>n</I>.  If
<I>n</I>

is omitted, the exit status
is that of the last command executed.
A trap on
<FONT SIZE=-1><B>EXIT</B>

</FONT>
is executed before the shell terminates.
<DT><B>export</B> [<B>-fn</B>] [<I>name</I>[=<I>word</I>]] ...<DD>

<DT><B>export -p</B>

<DD>

The supplied
<I>names</I>

are marked for automatic export to the environment of
subsequently executed commands.  If the
<B>-f</B>

option is given, the
<I>names</I>

refer to functions.
If no
<I>names</I>

are given, or if the
<B>-p</B>

option is supplied, a list
of names of all exported variables is printed.
The
<B>-n</B>

option causes the export property to be removed from each
<I>name</I>.
If a variable name is followed by =<I>word</I>, the value of
the variable is set to <I>word</I>.
<B>export</B>

returns an exit status of 0 unless an invalid option is
encountered,
one of the <I>names</I> is not a valid shell variable name, or
<B>-f</B>

is supplied with a
<I>name</I>

that is not a function.
<DT><B>fc</B> [<B>-e</B> <I>ename</I>] [<B>-lnr</B>] [<I>first</I>] [<I>last</I>]<DD>

<DT><B>fc</B> <B>-s</B> [<I>pat</I>=<I>rep</I>] [<I>cmd</I>]<DD>

The first form selects a range of commands from
<I>first</I>

to
<I>last</I>

from the history list and displays or edits and re-executes them.
<I>First</I>

and
<I>last</I>

may be specified as a string (to locate the last command beginning
with that string) or as a number (an index into the history list,
where a negative number is used as an offset from the current
command number).
When listing, a <I>first</I> or <I>last</I> of
0 is equivalent to -1 and -0 is equivalent to the current
command (usually the <B>fc</B> command); otherwise 0 is equivalent to -1
and -0 is invalid.
If
<I>last</I>

is not specified, it is set to
the current command for listing (so that

<TT>fc -l -10</TT>
prints the last 10 commands) and to
<I>first</I>

otherwise.
If
<I>first</I>

is not specified, it is set to the previous
command for editing and -16 for listing.
<P>
The
<B>-n</B>

option suppresses
the command numbers when listing.  The
<B>-r</B>

option reverses the order of
the commands.  If the
<B>-l</B>

option is given,
the commands are listed on
standard output.  Otherwise, the editor given by
<I>ename</I>

is invoked
on a file containing those commands.  If
<I>ename</I>

is not given, the
value of the
<FONT SIZE=-1><B>FCEDIT</B>

</FONT>
variable is used, and
the value of
<FONT SIZE=-1><B>EDITOR</B>

</FONT>
if
<FONT SIZE=-1><B>FCEDIT</B>

</FONT>
is not set.  If neither variable is set,

<I>vi</I>

is used.  When editing is complete, the edited commands are
echoed and executed.
<P>
In the second form, <I>command</I> is re-executed after each instance
of <I>pat</I> is replaced by <I>rep</I>.
<I>Command</I> is interpreted the same as <I>first</I> above.
A useful alias to use with this is

<TT>r='fc -s'</TT>,
so that typing

<TT>r cc</TT>
runs the last command beginning with

<TT>cc</TT>
and typing

<TT>r</TT>
re-executes the last command.
<P>
If the first form is used, the return value is 0 unless an invalid
option is encountered or
<I>first</I>

or
<I>last</I>

specify history lines out of range.
If the
<B>-e</B>

option is supplied, the return value is the value of the last
command executed or failure if an error occurs with the temporary
file of commands.  If the second form is used, the return status
is that of the command re-executed, unless
<I>cmd</I>

does not specify a valid history line, in which case
<B>fc</B>

returns failure.
<DT><B>fg</B> [<I>jobspec</I>]<DD>
Resume
<I>jobspec</I>

in the foreground, and make it the current job.
If
<I>jobspec</I>

is not present, the shell's notion of the <I>current job</I> is used.
The return value is that of the command placed into the foreground,
or failure if run when job control is disabled or, when run with
job control enabled, if
<I>jobspec</I>

does not specify a valid job or
<I>jobspec</I>

specifies a job that was started without job control.
<DT><B>getopts</B> <I>optstring</I> <I>name</I> [<I>arg ...</I>]<DD>
<B>getopts</B>

is used by shell procedures to parse positional parameters.
<I>optstring</I>

contains the option characters to be recognized; if a character
is followed by a colon, the option is expected to have an
argument, which should be separated from it by white space.
The colon and question mark characters may not be used as
option characters.
Each time it is invoked,
<B>getopts</B>

places the next option in the shell variable
<I>name</I>,

initializing
<I>name</I>

if it does not exist,
and the index of the next argument to be processed into the
variable
<FONT SIZE=-1><B>OPTIND</B>.

</FONT>
<FONT SIZE=-1><B>OPTIND</B>

</FONT>
is initialized to 1 each time the shell or a shell script
is invoked.  When an option requires an argument,
<B>getopts</B>

places that argument into the variable
<FONT SIZE=-1><B>OPTARG</B>.

</FONT>
The shell does not reset
<FONT SIZE=-1><B>OPTIND</B>

</FONT>
automatically; it must be manually reset between multiple
calls to
<B>getopts</B>

within the same shell invocation if a new set of parameters
is to be used.
<P>
When the end of options is encountered, <B>getopts</B> exits with a
return value greater than zero.
<FONT SIZE=-1><B>OPTIND</B>

</FONT>
is set to the index of the first non-option argument,
and <I>name</I> is set to ?.
<P>
<B>getopts</B>

normally parses the positional parameters, but if more arguments are
supplied as
<I>arg</I>

values,
<B>getopts</B>

parses those instead.
<P>
<B>getopts</B>

can report errors in two ways.  If the first character of
<I>optstring</I>

is a colon,
<I>silent</I>

error reporting is used.  In normal operation, diagnostic messages
are printed when invalid options or missing option arguments are
encountered.
If the variable
<FONT SIZE=-1><B>OPTERR</B>

</FONT>
is set to 0, no error messages will be displayed, even if the first
character of
<I>optstring</I>

is not a colon.
<P>
If an invalid option is seen,
<B>getopts</B>

places ? into
<I>name</I>

and, if not silent,
prints an error message and unsets
<FONT SIZE=-1><B>OPTARG</B>.

</FONT>
If
<B>getopts</B>

is silent,
the option character found is placed in
<FONT SIZE=-1><B>OPTARG</B>

</FONT>
and no diagnostic message is printed.
<P>
If a required argument is not found, and
<B>getopts</B>

is not silent,
a question mark (<B>?</B>) is placed in
<I>name</I>,

<FONT SIZE=-1><B>OPTARG</B>

</FONT>
is unset, and a diagnostic message is printed.
If
<B>getopts</B>

is silent, then a colon (<B>:</B>) is placed in
<I>name</I>

and
<FONT SIZE=-1><B>OPTARG</B>

</FONT>
is set to the option character found.
<P>
<B>getopts</B>

returns true if an option, specified or unspecified, is found.
It returns false if the end of options is encountered or an
error occurs.
<DT><B>hash</B> [<B>-lr</B>] [<B>-p</B> <I>filename</I>] [<B>-dt</B>] [<I>name</I>]<DD>
Each time <B>hash</B> is invoked,
the full pathname of the command
<I>name</I>

is determined by searching
the directories in
<B>$PATH</B>

and remembered.  Any previously-remembered pathname is discarded.
If the
<B>-p</B>

option is supplied, no path search is performed, and
<I>filename</I>

is used as the full filename of the command.
The
<B>-r</B>

option causes the shell to forget all
remembered locations.
The
<B>-d</B>

option causes the shell to forget the remembered location of each <I>name</I>.
If the
<B>-t</B>

option is supplied, the full pathname to which each <I>name</I> corresponds
is printed.  If multiple <I>name</I> arguments are supplied with <B>-t</B>,
the <I>name</I> is printed before the hashed full pathname.
The
<B>-l</B>

option causes output to be displayed in a format that may be reused as input.
If no arguments are given, or if only <B>-l</B> is supplied,
information about remembered commands is printed.
The return status is true unless a
<I>name</I>

is not found or an invalid option is supplied.
<DT><B>help</B> [<B>-dms</B>] [<I>pattern</I>]<DD>
Display helpful information about builtin commands.  If
<I>pattern</I>

is specified,
<B>help</B>

gives detailed help on all commands matching
<I>pattern</I>;

otherwise help for all the builtins and shell control structures
is printed.
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-d</B>

<DD>
Display a short description of each <I>pattern</I>
<DT><B>-m</B>

<DD>
Display the description of each <I>pattern</I> in a manpage-like format
<DT><B>-s</B>

<DD>
Display only a short usage synopsis for each <I>pattern</I>

</DL>
<P>

The return status is 0 unless no command matches
<I>pattern</I>.

</DL>

<DT><B>history [</B><I>n</I>]<DD>

<DT><B>history</B> <B>-c</B><DD>
<DT><B>history -d</B> <I>offset</I><DD>
<DT><B>history -d</B> <I>start</I>-<I>end</I><DD>
<DT><B>history</B> <B>-anrw</B> [<I>filename</I>]<DD>
<DT><B>history</B> <B>-p</B> <I>arg</I> [<I>arg ...</I>]<DD>
<DT><B>history</B> <B>-s</B> <I>arg</I> [<I>arg ...</I>]<DD>

With no options, display the command
history list with line numbers.  Lines listed
with a
<B>*</B>

have been modified.  An argument of
<I>n</I>

lists only the last
<I>n</I>

lines.
If the shell variable
<FONT SIZE=-1><B>HISTTIMEFORMAT</B>

</FONT>
is set and not null,
it is used as a format string for <I>strftime</I>(3) to display
the time stamp associated with each displayed history entry.
No intervening blank is printed between the formatted time stamp
and the history line.
If <I>filename</I> is supplied, it is used as the
name of the history file; if not, the value of
<FONT SIZE=-1><B>HISTFILE</B>

</FONT>
is used.  Options, if supplied, have the following meanings:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-c</B>

<DD>
Clear the history list by deleting all the entries.
<DT><B>-d</B> <I>offset</I><DD>
Delete the history entry at position <I>offset</I>.
If <I>offset</I> is negative, it is interpreted as relative to one greater
than the last history position, so negative indices count back from the
end of the history, and an index of -1 refers to the current
<B>history -d</B> command.
<DT><B>-d</B> <I>start</I>-<I>end</I><DD>
Delete the range of history entries between positions <I>start</I> and
<I>end</I>, inclusive.
Positive and negative values for <I>start</I> and <I>end</I>
are interpreted as described above.
<DT><B>-a</B>

<DD>
Append the ``new'' history lines to the history file.
These are history lines entered since the beginning of the current
<B>bash</B> session, but not already appended to the history file.
<DT><B>-n</B>

<DD>
Read the history lines not already read from the history
file into the current history list.  These are lines
appended to the history file since the beginning of the
current <B>bash</B> session.
<DT><B>-r</B>

<DD>
Read the contents of the history file
and append them to the current history list.
<DT><B>-w</B>

<DD>
Write the current history list to the history file, overwriting the
history file's contents.
<DT><B>-p</B>

<DD>
Perform history substitution on the following <I>args</I> and display
the result on the standard output.
Does not store the results in the history list.
Each <I>arg</I> must be quoted to disable normal history expansion.
<DT><B>-s</B>

<DD>
Store the
<I>args</I>

in the history list as a single entry.  The last command in the
history list is removed before the
<I>args</I>

are added.

</DL>
<P>

If the
<FONT SIZE=-1><B>HISTTIMEFORMAT</B>

</FONT>
variable is set, the time stamp information
associated with each history entry is written to the history file,
marked with the history comment character.
When the history file is read, lines beginning with the history
comment character followed immediately by a digit are interpreted
as timestamps for the following history entry.
The return value is 0 unless an invalid option is encountered, an
error occurs while reading or writing the history file, an invalid
<I>offset</I> or range is supplied as an argument to <B>-d</B>, or the
history expansion supplied as an argument to <B>-p</B> fails.
</DL>

<DT><B>jobs</B> [<B>-lnprs</B>] [ <I>jobspec</I> ... ]<DD>

<DT><B>jobs</B> <B>-x</B> <I>command</I> [ <I>args</I> ... ]<DD>

The first form lists the active jobs.  The options have the following
meanings:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-l</B>

<DD>
List process IDs
in addition to the normal information.
<DT><B>-n</B>

<DD>
Display information only about jobs that have changed status since
the user was last notified of their status.
<DT><B>-p</B>

<DD>
List only the process ID of the job's process group
leader.
<DT><B>-r</B>

<DD>
Display only running jobs.
<DT><B>-s</B>

<DD>
Display only stopped jobs.

</DL>
<P>

If
<I>jobspec</I>

is given, output is restricted to information about that job.
The return status is 0 unless an invalid option is encountered
or an invalid
<I>jobspec</I>

is supplied.
<P>

If the
<B>-x</B>

option is supplied,
<B>jobs</B>

replaces any
<I>jobspec</I>

found in
<I>command</I>

or
<I>args</I>

with the corresponding process group ID, and executes
<I>command</I>

passing it
<I>args</I>,

returning its exit status.
</DL>

<DT><B>kill</B> [<B>-s</B> <I>sigspec</I> | <B>-n</B> <I>signum</I> | <B>-</B><I>sigspec</I>] [<I>pid</I> | <I>jobspec</I>] ...<DD>

<DT><B>kill</B> <B>-l</B>|<B>-L</B> [<I>sigspec</I> | <I>exit_status</I>]<DD>

Send the signal named by
<I>sigspec</I>

or
<I>signum</I>

to the processes named by
<I>pid</I>

or
<I>jobspec</I>.

<I>sigspec</I>

is either a case-insensitive signal name such as
<FONT SIZE=-1><B>SIGKILL</B>

</FONT>
(with or without the
<FONT SIZE=-1><B>SIG</B>

</FONT>
prefix) or a signal number;
<I>signum</I>

is a signal number.
If
<I>sigspec</I>

is not present, then
<FONT SIZE=-1><B>SIGTERM</B>

</FONT>
is assumed.
An argument of
<B>-l</B>

lists the signal names.
If any arguments are supplied when
<B>-l</B>

is given, the names of the signals corresponding to the arguments are
listed, and the return status is 0.
The <I>exit_status</I> argument to
<B>-l</B>

is a number specifying either a signal number or the exit status of
a process terminated by a signal.
The
<B>-L</B>

option is equivalent to <B>-l</B>.
<B>kill</B>

returns true if at least one signal was successfully sent, or false
if an error occurs or an invalid option is encountered.
<DT><B>let</B> <I>arg</I> [<I>arg</I> ...]<DD>
Each
<I>arg</I>

is an arithmetic expression to be evaluated (see
<FONT SIZE=-1><B>ARITHMETIC EVALUATION</B>

</FONT>

above).
If the last
<I>arg</I>

evaluates to 0,
<B>let</B>

returns 1; 0 is returned otherwise.
<DT><B>local</B> [<I>option</I>] [<I>name</I>[=<I>value</I>] ... | - ]<DD>
For each argument, a local variable named
<I>name</I>

is created, and assigned
<I>value</I>.

The <I>option</I> can be any of the options accepted by <B>declare</B>.
When
<B>local</B>

is used within a function, it causes the variable
<I>name</I>

to have a visible scope restricted to that function and its children.
If <I>name</I> is -, the set of shell options is made local to the function
in which <B>local</B> is invoked: shell options changed using the
<B>set</B> builtin inside the function are restored to their original values
when the function returns.
The restore is effected as if a series of <B>set</B> commands were executed
to restore the values that were in place before the function.
With no operands,
<B>local</B>

writes a list of local variables to the standard output.  It is
an error to use
<B>local</B>

when not within a function.  The return status is 0 unless
<B>local</B>

is used outside a function, an invalid
<I>name</I>

is supplied, or
<I>name</I> is a readonly variable.
<DT><B>logout</B>

<DD>
Exit a login shell.
<DT><B>mapfile</B> [<B>-d</B> <I>delim</I>] [<B>-n</B> <I>count</I>] [<B>-O</B> <I>origin</I>] [<B>-s</B> <I>count</I>] [<B>-t</B>] [<B>-u</B> <I>fd</I>] [<B>-C</B> <I>callback</I>] [<B>-c</B> <I>quantum</I>] [<I>array</I>]<DD>

<DT><B>readarray</B> [<B>-d</B> <I>delim</I>] [<B>-n</B> <I>count</I>] [<B>-O</B> <I>origin</I>] [<B>-s</B> <I>count</I>] [<B>-t</B>] [<B>-u</B> <I>fd</I>] [<B>-C</B> <I>callback</I>] [<B>-c</B> <I>quantum</I>] [<I>array</I>]<DD>

Read lines from the standard input into the indexed array variable
<I>array</I>,

or from file descriptor
<I>fd</I>

if the
<B>-u</B>

option is supplied.
The variable
<FONT SIZE=-1><B>MAPFILE</B>

</FONT>
is the default <I>array</I>.
Options, if supplied, have the following meanings:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-d</B>

<DD>
The first character of <I>delim</I> is used to terminate each input line,
rather than newline.
If <I>delim</I> is the empty string, <B>mapfile</B> will terminate a line
when it reads a NUL character.
<DT><B>-n</B>

<DD>
Copy at most
<I>count</I>

lines.  If <I>count</I> is 0, all lines are copied.
<DT><B>-O</B>

<DD>
Begin assigning to
<I>array</I>

at index
<I>origin</I>.

The default index is 0.
<DT><B>-s</B>

<DD>
Discard the first <I>count</I> lines read.
<DT><B>-t</B>

<DD>
Remove a trailing <I>delim</I> (default newline) from each line read.
<DT><B>-u</B>

<DD>
Read lines from file descriptor <I>fd</I> instead of the standard input.
<DT><B>-C</B>

<DD>
Evaluate
<I>callback</I>

each time <I>quantum</I> lines are read.  The <B>-c</B> option specifies
<I>quantum</I>.

<DT><B>-c</B>

<DD>
Specify the number of lines read between each call to
<I>callback</I>.


</DL>
<P>

If
<B>-C</B>

is specified without
<B>-c</B>,

the default quantum is 5000.
When <I>callback</I> is evaluated, it is supplied the index of the next
array element to be assigned and the line to be assigned to that element
as additional arguments.
<I>callback</I> is evaluated after the line is read but before the
array element is assigned.
<P>

If not supplied with an explicit origin, <B>mapfile</B> will clear <I>array</I>
before assigning to it.
<P>

<B>mapfile</B> returns successfully unless an invalid option or option
argument is supplied, <I>array</I> is invalid or unassignable, or if
<I>array</I> is not an indexed array.
</DL>

<DT><B>popd</B> [-<B>n</B>] [+<I>n</I>] [-<I>n</I>]<DD>
Removes entries from the directory stack.
The elements are numbered from 0 starting at the first directory
listed by <B>dirs</B>.
With no arguments, <B>popd</B>
removes the top directory from the stack, and
changes to the new top directory.
Arguments, if supplied, have the following meanings:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-n</B>

<DD>
Suppresses the normal change of directory when removing directories
from the stack, so that only the stack is manipulated.
<DT><B>+</B><I>n</I><DD>
Removes the <I>n</I>th entry counting from the left of the list
shown by
<B>dirs</B>,

starting with zero, from the stack.
For example:

<TT>popd +0</TT>
removes the first directory,

<TT>popd +1</TT>
the second.
<DT><B>-</B><I>n</I><DD>
Removes the <I>n</I>th entry counting from the right of the list
shown by
<B>dirs</B>,

starting with zero.  For example:

<TT>popd -0</TT>
removes the last directory,

<TT>popd -1</TT>
the next to last.

</DL>
<P>

If the top element of the directory stack is modified, and
the <I>-n</I> option was not supplied, <B>popd</B> uses the <B>cd</B>
builtin to change to the directory at the top of the stack.
If the <B>cd</B> fails, <B>popd</B> returns a non-zero value.
<P>

Otherwise,
<B>popd</B>

returns false if an invalid option is encountered, the directory stack
is empty, or a non-existent directory stack entry is specified.
<P>

If the
<B>popd</B>

command is successful,
bash runs
<B>dirs</B>

to show the final contents of the directory stack,
and the return status is 0.
</DL>

<DT><B>printf</B> [<B>-v</B> <I>var</I>] <I>format</I> [<I>arguments</I>]<DD>
Write the formatted <I>arguments</I> to the standard output under the
control of the <I>format</I>.
The <B>-v</B> option causes the output to be assigned to the variable
<I>var</I> rather than being printed to the standard output.
<P>
The <I>format</I> is a character string which contains three types of objects:
plain characters, which are simply copied to standard output, character
escape sequences, which are converted and copied to the standard output, and
format specifications, each of which causes printing of the next successive
<I>argument</I>.
In addition to the standard <I>printf</I>(1) format specifications,
<B>printf</B> interprets the following extensions:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>%b</B>

<DD>
causes
<B>printf</B> to expand backslash escape sequences in the corresponding
<I>argument</I>
in the same way as <B>echo -e</B>.
<DT><B>%q</B>

<DD>
causes <B>printf</B> to output the corresponding
<I>argument</I> in a format that can be reused as shell input.
<DT><B>%Q</B>

<DD>
like <B>%q</B>, but applies any supplied precision to the <I>argument</I>
before quoting it.
<DT><B>%(</B><I>datefmt</I>)T

<DD>
causes <B>printf</B> to output the date-time string resulting from using
<I>datefmt</I> as a format string for <I>strftime</I>(3).
The corresponding <I>argument</I> is an integer representing the number of
seconds since the epoch.
Two special argument values may be used: -1 represents the current
time, and -2 represents the time the shell was invoked.
If no argument is specified, conversion behaves as if -1 had been given.
This is an exception to the usual <B>printf</B> behavior.

</DL>
<P>

The %b, %q, and %T directives all use the field width and precision
arguments from the format specification and write that many bytes from
(or use that wide a field for) the expanded argument, which usually
contains more characters than the original.
<P>

Arguments to non-string format specifiers are treated as C constants,
except that a leading plus or minus sign is allowed, and if the leading
character is a single or double quote, the value is the ASCII value of
the following character.
<P>

The <I>format</I> is reused as necessary to consume all of the <I>arguments</I>.
If the <I>format</I> requires more <I>arguments</I> than are supplied, the
extra format specifications behave as if a zero value or null string, as
appropriate, had been supplied.
The return value is zero on success, non-zero on failure.
</DL>

<DT><B>pushd</B> [<B>-n</B>] [+<I>n</I>] [-<I>n</I>]<DD>

<DT><B>pushd</B> [<B>-n</B>] [<I>dir</I>]<DD>

Adds a directory to the top of the directory stack, or rotates
the stack, making the new top of the stack the current working
directory.
With no arguments, <B>pushd</B> exchanges the top two elements of
the directory stack.
Arguments, if supplied, have the following meanings:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-n</B>

<DD>
Suppresses the normal change of directory when rotating or
adding directories to the stack, so that only the stack is manipulated.
<DT><B>+</B><I>n</I><DD>
Rotates the stack so that the <I>n</I>th directory
(counting from the left of the list shown by
<B>dirs</B>,

starting with zero)
is at the top.
<DT><B>-</B><I>n</I><DD>
Rotates the stack so that the <I>n</I>th directory
(counting from the right of the list shown by
<B>dirs</B>,

starting with zero) is at the top.
<DT><I>dir</I>

<DD>
Adds
<I>dir</I>

to the directory stack at the top

</DL>
<P>

After the stack has been modified, if the <B>-n</B> option was not
supplied, <B>pushd</B> uses the <B>cd</B> builtin to change to the
directory at the top of the stack.
If the <B>cd</B> fails, <B>pushd</B> returns a non-zero value.
<P>

Otherwise, if no arguments are supplied,
<B>pushd</B>

returns 0 unless the directory stack is empty.
When rotating the directory stack,
<B>pushd</B>

returns 0 unless the directory stack is empty or
a non-existent directory stack element is specified.
<P>

If the
<B>pushd</B>

command is successful,
bash runs
<B>dirs</B>

to show the final contents of the directory stack.
</DL>

<DT><B>pwd</B> [<B>-LP</B>]<DD>
Print the absolute pathname of the current working directory.
The pathname printed contains no symbolic links if the
<B>-P</B>

option is supplied or the
<B>-o physical</B>

option to the
<B>set</B>

builtin command is enabled.
If the
<B>-L</B>

option is used, the pathname printed may contain symbolic links.
The return status is 0 unless an error occurs while
reading the name of the current directory or an
invalid option is supplied.
<DT><B>read</B> [<B>-ers</B>] [<B>-a</B> <I>aname</I>] [<B>-d</B> <I>delim</I>] [<B>-i</B> <I>text</I>] [<B>-n</B> <I>nchars</I>] [<B>-N</B> <I>nchars</I>] [<B>-p</B> <I>prompt</I>] [<B>-t</B> <I>timeout</I>] [<B>-u</B> <I>fd</I>] [<I>name</I> ...]<DD>
One line is read from the standard input, or from the file descriptor
<I>fd</I> supplied as an argument to the <B>-u</B> option,
split into words as described

above
under <B>Word Splitting</B>,
and the first word
is assigned to the first
<I>name</I>,

the second word to the second
<I>name</I>,

and so on.
If there are more words than names, the remaining words and their
intervening delimiters are assigned to the last
<I>name</I>.

If there are fewer words read from the input stream than names,
the remaining names are assigned empty values.
The characters in
<FONT SIZE=-1><B>IFS</B>

</FONT>
are used to split the line into words using the same rules the shell
uses for expansion (described

above
under <B>Word Splitting</B>).
The backslash character (<B>\</B>) may be used to remove any special
meaning for the next character read and for line continuation.
Options, if supplied, have the following meanings:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-a </B><I>aname</I>

<DD>
The words are assigned to sequential indices
of the array variable
<I>aname</I>,

starting at 0.
<I>aname</I>

is unset before any new values are assigned.
Other <I>name</I> arguments are ignored.
<DT><B>-d </B><I>delim</I>

<DD>
The first character of <I>delim</I> is used to terminate the input line,
rather than newline.
If <I>delim</I> is the empty string, <B>read</B> will terminate a line
when it reads a NUL character.
<DT><B>-e</B>

<DD>
If the standard input
is coming from a terminal,
<B>readline</B>

(see
<FONT SIZE=-1><B>READLINE</B>

</FONT>

above)
is used to obtain the line.
Readline uses the current (or default, if line editing was not previously
active) editing settings, but uses readline's default filename completion.
<DT><B>-i </B><I>text</I>

<DD>
If
<B>readline</B>

is being used to read the line, <I>text</I> is placed into the editing
buffer before editing begins.
<DT><B>-n </B><I>nchars</I>

<DD>
<B>read</B> returns after reading <I>nchars</I> characters rather than
waiting for a complete line of input, but honors a delimiter if fewer
than <I>nchars</I> characters are read before the delimiter.
<DT><B>-N </B><I>nchars</I>

<DD>
<B>read</B> returns after reading exactly <I>nchars</I> characters rather
than waiting for a complete line of input, unless EOF is encountered or
<B>read</B> times out.
Delimiter characters encountered in the input are
not treated specially and do not cause <B>read</B> to return until
<I>nchars</I> characters are read.
The result is not split on the characters in <B>IFS</B>; the intent is
that the variable is assigned exactly the characters read
(with the exception of backslash; see the <B>-r</B> option below).
<DT><B>-p </B><I>prompt</I>

<DD>
Display <I>prompt</I> on standard error, without a
trailing newline, before attempting to read any input.  The prompt
is displayed only if input is coming from a terminal.
<DT><B>-r</B>

<DD>
Backslash does not act as an escape character.
The backslash is considered to be part of the line.
In particular, a backslash-newline pair may not then be used as a line
continuation.
<DT><B>-s</B>

<DD>
Silent mode.  If input is coming from a terminal, characters are
not echoed.
<DT><B>-t </B><I>timeout</I>

<DD>
Cause <B>read</B> to time out and return failure if a complete line of
input (or a specified number of characters)
is not read within <I>timeout</I> seconds.
<I>timeout</I> may be a decimal number with a fractional portion following
the decimal point.
This option is only effective if <B>read</B> is reading input from a
terminal, pipe, or other special file; it has no effect when reading
from regular files.
If <B>read</B> times out, <B>read</B> saves any partial input read into
the specified variable <I>name</I>.
If <I>timeout</I> is 0, <B>read</B> returns immediately, without trying to
read any data.
The exit status is 0 if input is available on the specified file descriptor,
or the read will return EOF,
non-zero otherwise.
The exit status is greater than 128 if the timeout is exceeded.
<DT><B>-u </B><I>fd</I>

<DD>
Read input from file descriptor <I>fd</I>.

</DL>
<P>

If no
<I>names</I>

are supplied, the line read,
without the ending delimiter but otherwise unmodified,
is assigned to the variable
<FONT SIZE=-1><B>REPLY</B>.

</FONT>
The exit status is zero, unless end-of-file is encountered, <B>read</B>
times out (in which case the status is greater than 128),
a variable assignment error (such as assigning to a readonly variable) occurs,
or an invalid file descriptor is supplied as the argument to <B>-u</B>.
</DL>

<DT><B>readonly</B> [<B>-aAf</B>] [<B>-p</B>] [<I>name</I>[=<I>word</I>] ...]<DD>

The given
<I>names</I> are marked readonly; the values of these
<I>names</I>

may not be changed by subsequent assignment.
If the
<B>-f</B>

option is supplied, the functions corresponding to the
<I>names</I> are so
marked.
The
<B>-a</B>

option restricts the variables to indexed arrays; the
<B>-A</B>

option restricts the variables to associative arrays.
If both options are supplied,
<B>-A</B>

takes precedence.
If no
<I>name</I>

arguments are given, or if the
<B>-p</B>

option is supplied, a list of all readonly names is printed.
The other options may be used to restrict the output to a subset of
the set of readonly names.
The
<B>-p</B>

option causes output to be displayed in a format that
may be reused as input.
If a variable name is followed by =<I>word</I>, the value of
the variable is set to <I>word</I>.
The return status is 0 unless an invalid option is encountered,
one of the
<I>names</I>

is not a valid shell variable name, or
<B>-f</B>

is supplied with a
<I>name</I>

that is not a function.
<DT><B>return</B> [<I>n</I>]<DD>
Causes a function to stop executing and return the value specified by
<I>n</I>

to its caller.
If
<I>n</I>

is omitted, the return status is that of the last command
executed in the function body.
If <B>return</B> is executed by a trap handler, the last command used to
determine the status is the last command executed before the trap handler.
If <B>return</B> is executed during a <B>DEBUG</B> trap, the last command
used to determine the status is the last command executed by the trap
handler before <B>return</B> was invoked.
If
<B>return</B>

is used outside a function,
but during execution of a script by the
<B>.</B>

(<B>source</B>) command, it causes the shell to stop executing
that script and return either
<I>n</I>

or the exit status of the last command executed within the
script as the exit status of the script.
If <I>n</I> is supplied, the return value is its least significant
8 bits.
The return status is non-zero if
<B>return</B>

is supplied a non-numeric argument, or
is used outside a
function and not during execution of a script by <B>.</B> or <B>source</B>.
Any command associated with the <B>RETURN</B> trap is executed
before execution resumes after the function or script.
<DT><B>set</B> [<B>-abefhkmnptuvxBCEHPT</B>] [<B>-o</B> <I>option-name</I>] [<B>--</B>] [<B>-</B>] [<I>arg</I> ...]<DD>

<DT><B>set</B> [<B>+abefhkmnptuvxBCEHPT</B>] [<B>+o</B> <I>option-name</I>] [<B>--</B>] [<B>-</B>] [<I>arg</I> ...]<DD>

Without options, display the name and value of each shell variable
in a format that can be reused as input
for setting or resetting the currently-set variables.
Read-only variables cannot be reset.
In <I>posix mode</I>, only shell variables are listed.
The output is sorted according to the current locale.
When options are specified, they set or unset shell attributes.
Any arguments remaining after option processing are treated
as values for the positional parameters and are assigned, in order, to
<B>$1</B>,

<B>$2</B>,

<B>...</B>

<B>$</B><I>n</I>.

Options, if specified, have the following meanings:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-a</B>

<DD>
Each variable or function that is created or modified is given the
export attribute and marked for export to the environment of
subsequent commands.
<DT><B>-b</B>

<DD>
Report the status of terminated background jobs
immediately, rather than before the next primary prompt.  This is
effective only when job control is enabled.
<DT><B>-e</B>

<DD>
Exit immediately if a
<I>pipeline</I> (which may consist of a single <I>simple command</I>),
a <I>list</I>,
or a <I>compound command</I>
(see
<FONT SIZE=-1><B>SHELL GRAMMAR</B>

</FONT>

above),
exits with a non-zero status.
The shell does not exit if the
command that fails is part of the command list immediately following a
<B>while</B>

or
<B>until</B>

keyword,
part of the test following the
<B>if</B>

or
<B>elif</B>

reserved words, part of any command executed in a
<B>&amp;&amp;</B>

or
<B>||</B>

list except the command following the final <B>&amp;&amp;</B> or <B>||</B>,
any command in a pipeline but the last,
or if the command's return value is
being inverted with
<B>!</B>.

If a compound command other than a subshell
returns a non-zero status because a command failed
while <B>-e</B> was being ignored, the shell does not exit.
A trap on <B>ERR</B>, if set, is executed before the shell exits.
This option applies to the shell environment and each subshell environment
separately (see
<FONT SIZE=-1><B>COMMAND EXECUTION ENVIRONMENT</B>

</FONT>

above),
and may cause
subshells to exit before executing all the commands in the subshell.
<P>


If a compound command or shell function executes in a context
where <B>-e</B> is being ignored,
none of the commands executed within the compound command or function body
will be affected by the <B>-e</B> setting, even if <B>-e</B> is set
and a command returns a failure status.
If a compound command or shell function sets <B>-e</B> while executing in
a context where <B>-e</B> is ignored, that setting will not have any
effect until the compound command or the command containing the function
call completes.
<DT><B>-f</B>

<DD>
Disable pathname expansion.
<DT><B>-h</B>

<DD>
Remember the location of commands as they are looked up for execution.
This is enabled by default.
<DT><B>-k</B>

<DD>
All arguments in the form of assignment statements
are placed in the environment for a command, not just
those that precede the command name.
<DT><B>-m</B>

<DD>
Monitor mode.  Job control is enabled.  This option is on
by default for interactive shells on systems that support
it (see
<FONT SIZE=-1><B>JOB CONTROL</B>

</FONT>

above).
All processes run in a separate process group.
When a background job completes, the shell prints a line
containing its exit status.
<DT><B>-n</B>

<DD>
Read commands but do not execute them.
This may be used to check a shell script for syntax errors.
This is ignored by interactive shells.
<DT><B>-o </B><I>option-name</I>

<DD>
The <I>option-name</I> can be one of the following:
<DL COMPACT><DT><DD>
<DL COMPACT>
<DT><B>allexport</B>

<DD>
Same as
<B>-a</B>.

<DT><B>braceexpand</B>

<DD>
Same as
<B>-B</B>.

<DT><B>emacs</B>

<DD>
Use an emacs-style command line editing interface.  This is enabled
by default when the shell is interactive, unless the shell is started
with the
<B>--noediting</B>

option.
This also affects the editing interface used for <B>read -e</B>.
<DT><B>errexit</B>

<DD>
Same as
<B>-e</B>.

<DT><B>errtrace</B>

<DD>
Same as
<B>-E</B>.

<DT><B>functrace</B>

<DD>
Same as
<B>-T</B>.

<DT><B>hashall</B>

<DD>
Same as
<B>-h</B>.

<DT><B>histexpand</B>

<DD>
Same as
<B>-H</B>.

<DT><B>history</B>

<DD>
Enable command history, as described

above
under
<FONT SIZE=-1><B>HISTORY</B>.

</FONT>
This option is on by default in interactive shells.
<DT><B>ignoreeof</B>

<DD>
The effect is as if the shell command
<TT>IGNOREEOF=10</TT>

had been executed
(see
<B>Shell Variables</B>


above).
<DT><B>keyword</B>

<DD>
Same as
<B>-k</B>.

<DT><B>monitor</B>

<DD>
Same as
<B>-m</B>.

<DT><B>noclobber</B>

<DD>
Same as
<B>-C</B>.

<DT><B>noexec</B>

<DD>
Same as
<B>-n</B>.

<DT><B>noglob</B>

<DD>
Same as
<B>-f</B>.

<DT><B>nolog</B>

<DD>
Currently ignored.
<DT><B>notify</B>

<DD>
Same as
<B>-b</B>.

<DT><B>nounset</B>

<DD>
Same as
<B>-u</B>.

<DT><B>onecmd</B>

<DD>
Same as
<B>-t</B>.

<DT><B>physical</B>

<DD>
Same as
<B>-P</B>.

<DT><B>pipefail</B>

<DD>
If set, the return value of a pipeline is the value of the last
(rightmost) command to exit with a non-zero status, or zero if all
commands in the pipeline exit successfully.
This option is disabled by default.
<DT><B>posix</B>

<DD>
Change the behavior of
<B>bash</B>

where the default operation differs
from the POSIX standard to match the standard (<I>posix mode</I>).
See
<FONT SIZE=-1><B>SEE ALSO</B>

</FONT>

below
for a reference to a document that details how posix mode affects
bash's behavior.
<DT><B>privileged</B>

<DD>
Same as
<B>-p</B>.

<DT><B>verbose</B>

<DD>
Same as
<B>-v</B>.

<DT><B>vi</B>

<DD>
Use a vi-style command line editing interface.
This also affects the editing interface used for <B>read -e</B>.
<DT><B>xtrace</B>

<DD>
Same as
<B>-x</B>.

<P>
</DL>
<P>

If
<B>-o</B>

is supplied with no <I>option-name</I>, the values of the current options are
printed.
If
<B>+o</B>

is supplied with no <I>option-name</I>, a series of
<B>set</B>

commands to recreate the current option settings is displayed on
the standard output.
</DL>

<DT><B>-p</B>

<DD>
Turn on
<I>privileged</I>

mode.  In this mode, the
<FONT SIZE=-1><B>$ENV</B>

</FONT>
and
<FONT SIZE=-1><B>$BASH_ENV</B>

</FONT>
files are not processed, shell functions are not inherited from the
environment, and the
<FONT SIZE=-1><B>SHELLOPTS</B>,

</FONT>
<FONT SIZE=-1><B>BASHOPTS</B>,

</FONT>
<FONT SIZE=-1><B>CDPATH</B>,

</FONT>
and
<FONT SIZE=-1><B>GLOBIGNORE</B>

</FONT>
variables, if they appear in the environment, are ignored.
If the shell is started with the effective user (group) id not equal to the
real user (group) id, and the <B>-p</B> option is not supplied, these actions
are taken and the effective user id is set to the real user id.
If the <B>-p</B> option is supplied at startup, the effective user id is
not reset.
Turning this option off causes the effective user
and group ids to be set to the real user and group ids.
<DT><B>-r</B>

<DD>
Enable restricted shell mode.
This option cannot be unset once it has been set.
<DT><B>-t</B>

<DD>
Exit after reading and executing one command.
<DT><B>-u</B>

<DD>
Treat unset variables and parameters other than the special
parameters &quot;@&quot; and &quot;*&quot;,
or array variables subscripted with &quot;@&quot; or &quot;*&quot;,
as an error when performing
parameter expansion.  If expansion is attempted on an
unset variable or parameter, the shell prints an error message, and,
if not interactive, exits with a non-zero status.
<DT><B>-v</B>

<DD>
Print shell input lines as they are read.
<DT><B>-x</B>

<DD>
After expanding each <I>simple command</I>,
<B>for</B> command, <B>case</B> command, <B>select</B> command, or
arithmetic <B>for</B> command, display the expanded value of
<FONT SIZE=-1><B>PS4</B>,

</FONT>
followed by the command and its expanded arguments
or associated word list.
<DT><B>-B</B>

<DD>
The shell performs brace expansion (see
<B>Brace Expansion</B>


above).
This is on by default.
<DT><B>-C</B>

<DD>
If set,
<B>bash</B>

does not overwrite an existing file with the
<B>&gt;</B>,

<B>&gt;&amp;</B>,

and
<B>&lt;&gt;</B>

redirection operators.  This may be overridden when
creating output files by using the redirection operator
<B>&gt;|</B>

instead of
<B>&gt;</B>.

<DT><B>-E</B>

<DD>
If set, any trap on <B>ERR</B> is inherited by shell functions, command
substitutions, and commands executed in a subshell environment.
The <B>ERR</B> trap is normally not inherited in such cases.
<DT><B>-H</B>

<DD>
Enable
<B>!</B>

style history substitution.  This option is on by
default when the shell is interactive.
<DT><B>-P</B>

<DD>
If set, the shell does not resolve symbolic links when executing
commands such as
<B>cd</B>

that change the current working directory.  It uses the
physical directory structure instead.  By default,
<B>bash</B>

follows the logical chain of directories when performing commands
which change the current directory.
<DT><B>-T</B>

<DD>
If set, any traps on <B>DEBUG</B> and <B>RETURN</B> are inherited by shell
functions, command substitutions, and commands executed in a
subshell environment.
The <B>DEBUG</B> and <B>RETURN</B> traps are normally not inherited
in such cases.
<DT><B>--</B>

<DD>
If no arguments follow this option, then the positional parameters are
unset.  Otherwise, the positional parameters are set to the
<I>arg</I>s, even if some of them begin with a
<B>-</B>.

<DT><B>-</B>

<DD>
Signal the end of options, cause all remaining <I>arg</I>s to be
assigned to the positional parameters.  The
<B>-x</B>

and
<B>-v</B>

options are turned off.
If there are no <I>arg</I>s,
the positional parameters remain unchanged.

</DL>
<P>

The options are off by default unless otherwise noted.
Using + rather than - causes these options to be turned off.
The options can also be specified as arguments to an invocation of
the shell.
The current set of options may be found in
<B>$-</B>.

The return status is always true unless an invalid option is encountered.
</DL>

<DT><B>shift</B> [<I>n</I>]<DD>
The positional parameters from <I>n</I>+1 ... are renamed to
<B>$1</B>

<B>....</B>

Parameters represented by the numbers <B>$#</B>
down to <B>$#</B>-<I>n</I>+1 are unset.
<I>n</I>

must be a non-negative number less than or equal to <B>$#</B>.
If
<I>n</I>

is 0, no parameters are changed.
If
<I>n</I>

is not given, it is assumed to be 1.
If
<I>n</I>

is greater than <B>$#</B>, the positional parameters are not changed.
The return status is greater than zero if
<I>n</I>

is greater than
<B>$#</B>

or less than zero; otherwise 0.
<DT><B>shopt</B> [<B>-pqsu</B>] [<B>-o</B>] [<I>optname</I> ...]<DD>
Toggle the values of settings controlling optional shell behavior.
The settings can be either those listed below, or, if the
<B>-o</B>

option is used, those available with the
<B>-o</B>

option to the <B>set</B> builtin command.
With no options, or with the
<B>-p</B>

option, a list of all settable options is displayed, with
an indication of whether or not each is set;
if <I>optnames</I> are supplied, the output is restricted to those options.
The <B>-p</B> option causes output to be displayed in a form that
may be reused as input.
Other options have the following meanings:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-s</B>

<DD>
Enable (set) each <I>optname</I>.
<DT><B>-u</B>

<DD>
Disable (unset) each <I>optname</I>.
<DT><B>-q</B>

<DD>
Suppresses normal output (quiet mode); the return status indicates
whether the <I>optname</I> is set or unset.
If multiple <I>optname</I> arguments are given with
<B>-q</B>,

the return status is zero if all <I>optnames</I> are enabled; non-zero
otherwise.
<DT><B>-o</B>

<DD>
Restricts the values of <I>optname</I> to be those defined for the
<B>-o</B>

option to the
<B>set</B>

builtin.

</DL>
<P>

If either
<B>-s</B>

or
<B>-u</B>

is used with no <I>optname</I> arguments,
<B>shopt</B>

shows only those options which are set or unset, respectively.
Unless otherwise noted, the <B>shopt</B> options are disabled (unset)
by default.
<P>

The return status when listing options is zero if all <I>optnames</I>
are enabled, non-zero otherwise.  When setting or unsetting options,
the return status is zero unless an <I>optname</I> is not a valid shell
option.
<P>

The list of <B>shopt</B> options is:
<P>



<DL COMPACT>
<DT><B>assoc_expand_once</B>

<DD>
If set, the shell suppresses multiple evaluation of associative array
subscripts during arithmetic expression evaluation, while executing
builtins that can perform variable assignments,
and while executing builtins that perform array dereferencing.
<DT><B>autocd</B>

<DD>
If set, a command name that is the name of a directory is executed as if
it were the argument to the <B>cd</B> command.
This option is only used by interactive shells.
<DT><B>cdable_vars</B>

<DD>
If set, an argument to the
<B>cd</B>

builtin command that
is not a directory is assumed to be the name of a variable whose
value is the directory to change to.
<DT><B>cdspell</B>

<DD>
If set, minor errors in the spelling of a directory component in a
<B>cd</B>

command will be corrected.
The errors checked for are transposed characters,
a missing character, and one character too many.
If a correction is found, the corrected filename is printed,
and the command proceeds.
This option is only used by interactive shells.
<DT><B>checkhash</B>

<DD>
If set, <B>bash</B> checks that a command found in the hash
table exists before trying to execute it.  If a hashed command no
longer exists, a normal path search is performed.
<DT><B>checkjobs</B>

<DD>
If set, <B>bash</B> lists the status of any stopped and running jobs before
exiting an interactive shell.  If any jobs are running, this causes
the exit to be deferred until a second exit is attempted without an
intervening command (see
<FONT SIZE=-1><B>JOB CONTROL</B>

</FONT>

above).
The shell always postpones exiting if any jobs are stopped.
<DT><B>checkwinsize</B>

<DD>
If set, <B>bash</B> checks the window size after each external (non-builtin)
command and, if necessary, updates the values of
<FONT SIZE=-1><B>LINES</B>

</FONT>
and
<FONT SIZE=-1><B>COLUMNS</B>.

</FONT>
This option is enabled by default.
<DT><B>cmdhist</B>

<DD>
If set,
<B>bash</B>

attempts to save all lines of a multiple-line
command in the same history entry.  This allows
easy re-editing of multi-line commands.
This option is enabled by default, but only has an effect if command
history is enabled, as described

above
under
<FONT SIZE=-1><B>HISTORY</B>.

</FONT>

<DT><B>compat31</B>

<DD>
<DT><B>compat32</B>

<DD>
<DT><B>compat40</B>

<DD>
<DT><B>compat41</B>

<DD>
<DT><B>compat42</B>

<DD>
<DT><B>compat43</B>

<DD>
<DT><B>compat44</B>

<DD>
<DT><B>compat50</B>

<DD>

These control aspects of the shell's compatibility mode
(see
<FONT SIZE=-1><B>SHELL COMPATIBILITY MODE</B>

</FONT>

below).
<DT><B>complete_fullquote</B>

<DD>
If set,
<B>bash</B>

quotes all shell metacharacters in filenames and directory names when
performing completion.
If not set,
<B>bash</B>

removes metacharacters such as the dollar sign from the set of
characters that will be quoted in completed filenames
when these metacharacters appear in shell variable references in words to be
completed.
This means that dollar signs in variable names that expand to directories
will not be quoted;
however, any dollar signs appearing in filenames will not be quoted, either.
This is active only when bash is using backslashes to quote completed
filenames.
This variable is set by default, which is the default bash behavior in
versions through 4.2.
<DT><B>direxpand</B>

<DD>
If set,
<B>bash</B>

replaces directory names with the results of word expansion when performing
filename completion.  This changes the contents of the readline editing
buffer.
If not set,
<B>bash</B>

attempts to preserve what the user typed.
<DT><B>dirspell</B>

<DD>
If set,
<B>bash</B>

attempts spelling correction on directory names during word completion
if the directory name initially supplied does not exist.
<DT><B>dotglob</B>

<DD>
If set,
<B>bash</B>

includes filenames beginning with a `.' in the results of pathname
expansion.
The filenames
<B>``.''</B>

and
<B>``..''</B>

must always be matched explicitly, even if
<B>dotglob</B>

is set.
<DT><B>execfail</B>

<DD>
If set, a non-interactive shell will not exit if
it cannot execute the file specified as an argument to the
<B>exec</B>

builtin command.  An interactive shell does not exit if
<B>exec</B>

fails.
<DT><B>expand_aliases</B>

<DD>
If set, aliases are expanded as described

above
under
<FONT SIZE=-1><B>ALIASES</B>.

</FONT>
This option is enabled by default for interactive shells.
<DT><B>extdebug</B>

<DD>
If set at shell invocation,
or in a shell startup file,
arrange to execute the debugger profile
before the shell starts, identical to the <B>--debugger</B> option.
If set after invocation, behavior intended for use by debuggers is enabled:
<DL COMPACT><DT><DD>
<DL COMPACT>
<DT><B>1.</B>

<DD>
The <B>-F</B> option to the <B>declare</B> builtin displays the source
file name and line number corresponding to each function name supplied
as an argument.
<DT><B>2.</B>

<DD>
If the command run by the <B>DEBUG</B> trap returns a non-zero value, the
next command is skipped and not executed.
<DT><B>3.</B>

<DD>
If the command run by the <B>DEBUG</B> trap returns a value of 2, and the
shell is executing in a subroutine (a shell function or a shell script
executed by the <B>.</B> or <B>source</B> builtins), the shell simulates
a call to <B>return</B>.
<DT><B>4.</B>

<DD>
<FONT SIZE=-1><B>BASH_ARGC</B>

</FONT>
and
<FONT SIZE=-1><B>BASH_ARGV</B>

</FONT>
are updated as described in their descriptions

above).
<DT><B>5.</B>

<DD>
Function tracing is enabled: command substitution, shell functions, and
subshells invoked with <B>(</B> <I>command</I> <B>)</B> inherit the
<B>DEBUG</B> and <B>RETURN</B> traps.
<DT><B>6.</B>

<DD>
Error tracing is enabled: command substitution, shell functions, and
subshells invoked with <B>(</B> <I>command</I> <B>)</B> inherit the
<B>ERR</B> trap.
</DL></DL>

<DT><B>extglob</B>

<DD>
If set, the extended pattern matching features described

above
under
<B>Pathname Expansion</B> are enabled.
<DT><B>extquote</B>

<DD>
If set, <B>$</B>aq<I>string</I>aq and <B>$</B>&quot;<I>string</I>&quot; quoting is
performed within <B>${</B><I>parameter</I><B>}</B> expansions
enclosed in double quotes.  This option is enabled by default.
<DT><B>failglob</B>

<DD>
If set, patterns which fail to match filenames during pathname expansion
result in an expansion error.
<DT><B>force_fignore</B>

<DD>
If set, the suffixes specified by the
<FONT SIZE=-1><B>FIGNORE</B>

</FONT>
shell variable
cause words to be ignored when performing word completion even if
the ignored words are the only possible completions.
See
<FONT SIZE=-1><B>SHELL VARIABLES</B></FONT>

above
for a description of
<FONT SIZE=-1><B>FIGNORE</B>.

</FONT>
This option is enabled by default.
<DT><B>globasciiranges</B>

<DD>
If set, range expressions used in pattern matching bracket expressions (see
<FONT SIZE=-1><B>Pattern Matching</B>

</FONT>

above)
behave as if in the traditional C locale when performing
comparisons.  That is, the current locale's collating sequence
is not taken into account, so
<B>b</B>

will not collate between
<B>A</B>

and
<B>B</B>,

and upper-case and lower-case ASCII characters will collate together.
<DT><B>globskipdots</B>

<DD>
If set, pathname expansion will never match the filenames
<B>``.''</B>

and
<B>``..''</B>,

even if the pattern begins with a
<B>``.''</B>.

This option is enabled by default.
<DT><B>globstar</B>

<DD>
If set, the pattern <B>**</B> used in a pathname expansion context will
match all files and zero or more directories and subdirectories.
If the pattern is followed by a <B>/</B>, only directories and
subdirectories match.
<DT><B>gnu_errfmt</B>

<DD>
If set, shell error messages are written in the standard GNU error
message format.
<DT><B>histappend</B>

<DD>
If set, the history list is appended to the file named by the value
of the
<FONT SIZE=-1><B>HISTFILE</B>

</FONT>
variable when the shell exits, rather than overwriting the file.
<DT><B>histreedit</B>

<DD>
If set, and
<B>readline</B>

is being used, a user is given the opportunity to re-edit a
failed history substitution.
<DT><B>histverify</B>

<DD>
If set, and
<B>readline</B>

is being used, the results of history substitution are not immediately
passed to the shell parser.  Instead, the resulting line is loaded into
the <B>readline</B> editing buffer, allowing further modification.
<DT><B>hostcomplete</B>

<DD>
If set, and
<B>readline</B>

is being used, <B>bash</B> will attempt to perform hostname completion when a
word containing a <B>@</B> is being completed (see
<B>Completing</B>

under
<FONT SIZE=-1><B>READLINE</B>

</FONT>

above).
This is enabled by default.
<DT><B>huponexit</B>

<DD>
If set, <B>bash</B> will send
<FONT SIZE=-1><B>SIGHUP</B>

</FONT>
to all jobs when an interactive login shell exits.
<DT><B>inherit_errexit</B>

<DD>
If set, command substitution inherits the value of the <B>errexit</B> option,
instead of unsetting it in the subshell environment.
This option is enabled when <I>posix mode</I> is enabled.
<DT><B>interactive_comments</B>

<DD>
If set, allow a word beginning with
<B>#</B>

to cause that word and all remaining characters on that
line to be ignored in an interactive shell (see
<FONT SIZE=-1><B>COMMENTS</B>

</FONT>

above).
This option is enabled by default.
<DT><B>lastpipe</B>

<DD>
If set, and job control is not active, the shell runs the last command of
a pipeline not executed in the background in the current shell environment.
<DT><B>lithist</B>

<DD>
If set, and the
<B>cmdhist</B>

option is enabled, multi-line commands are saved to the history with
embedded newlines rather than using semicolon separators where possible.
<DT><B>localvar_inherit</B>

<DD>
If set, local variables inherit the value and attributes of a variable of
the same name that exists at a previous scope before any new value is
assigned.  The nameref attribute is not inherited.
<DT><B>localvar_unset</B>

<DD>
If set, calling <B>unset</B> on local variables in previous function scopes
marks them so subsequent lookups find them unset until that function
returns. This is identical to the behavior of unsetting local variables
at the current function scope.
<DT><B>login_shell</B>

<DD>
The shell sets this option if it is started as a login shell (see
<FONT SIZE=-1><B>INVOCATION</B>

</FONT>

above).
The value may not be changed.
<DT><B>mailwarn</B>

<DD>
If set, and a file that <B>bash</B> is checking for mail has been
accessed since the last time it was checked, the message ``The mail in
<I>mailfile</I> has been read'' is displayed.
<DT><B>no_empty_cmd_completion</B>

<DD>
If set, and
<B>readline</B>

is being used,
<B>bash</B>

will not attempt to search the
<FONT SIZE=-1><B>PATH</B>

</FONT>
for possible completions when
completion is attempted on an empty line.
<DT><B>nocaseglob</B>

<DD>
If set,
<B>bash</B>

matches filenames in a case-insensitive fashion when performing pathname
expansion (see
<B>Pathname Expansion</B>


above).
<DT><B>nocasematch</B>

<DD>
If set,
<B>bash</B>

matches patterns in a case-insensitive fashion when performing matching
while executing <B>case</B> or <B>[[</B> conditional commands,
when performing pattern substitution word expansions,
or when filtering possible completions as part of programmable completion.
<DT><B>noexpand_translation</B>

<DD>
If set,
<B>bash</B>

encloses the translated results of $&quot;...&quot; quoting in single quotes
instead of double quotes.
If the string is not translated, this has no effect.
<DT><B>nullglob</B>

<DD>
If set,
<B>bash</B>

allows patterns which match no
files (see
<B>Pathname Expansion</B>


above)
to expand to a null string, rather than themselves.
<DT><B>patsub_replacement</B>

<DD>
If set, <B>bash</B>
expands occurrences of <B>&amp;</B> in the replacement string of pattern
substitution to the text matched by the pattern, as described
under <B>Parameter Expansion</B>

above.
This option is enabled by default.
<DT><B>progcomp</B>

<DD>
If set, the programmable completion facilities (see
<B>Programmable Completion</B>

above)
are enabled.
This option is enabled by default.
<DT><B>progcomp_alias</B>

<DD>
If set, and programmable completion is enabled, <B>bash</B> treats a command
name that doesn't have any completions as a possible alias and attempts
alias expansion. If it has an alias, <B>bash</B> attempts programmable
completion using the command word resulting from the expanded alias.
<DT><B>promptvars</B>

<DD>
If set, prompt strings undergo
parameter expansion, command substitution, arithmetic
expansion, and quote removal after being expanded as described in
<FONT SIZE=-1><B>PROMPTING</B>

</FONT>

above.
This option is enabled by default.
<DT><B>restricted_shell</B>

<DD>
The shell sets this option if it is started in restricted mode
(see
<FONT SIZE=-1><B>RESTRICTED SHELL</B>

</FONT>

below).
The value may not be changed.
This is not reset when the startup files are executed, allowing
the startup files to discover whether or not a shell is restricted.
<DT><B>shift_verbose</B>

<DD>
If set, the
<B>shift</B>

builtin prints an error message when the shift count exceeds the
number of positional parameters.
<DT><B>sourcepath</B>

<DD>
If set, the
<B>.</B> (<B>source</B>) builtin uses the value of
<FONT SIZE=-1><B>PATH</B>

</FONT>
to find the directory containing the file supplied as an argument.
This option is enabled by default.
<DT><B>varredir_close</B>

<DD>
If set, the shell automatically closes file descriptors assigned using the
<I>{varname}</I> redirection syntax (see
<FONT SIZE=-1><B>REDIRECTION</B>

</FONT>

above)
instead of leaving them open when the command completes.
<DT><B>xpg_echo</B>

<DD>
If set, the <B>echo</B> builtin expands backslash-escape sequences
by default.
</DL></DL>


<DT><B>suspend</B> [<B>-f</B>]<DD>
Suspend the execution of this shell until it receives a
<FONT SIZE=-1><B>SIGCONT</B>

</FONT>
signal.  A login shell,
or a shell without job control enabled,
cannot be suspended; the
<B>-f</B>

option can be used to override this and force the suspension.
The return status is 0 unless the shell is a login shell
or job control is not enabled
and
<B>-f</B>

is not supplied.
<DT><B>test</B> <I>expr</I><DD>

<DT><B>[</B> <I>expr</I> <B>]</B><DD>
Return a status of 0 (true) or 1 (false) depending on
the evaluation of the conditional expression
<I>expr</I>.

Each operator and operand must be a separate argument.
Expressions are composed of the primaries described 

above
under
<FONT SIZE=-1><B>CONDITIONAL EXPRESSIONS</B>.

</FONT>
<B>test</B> does not accept any options, nor does it accept and ignore
an argument of <B>--</B> as signifying the end of options.
<P>


Expressions may be combined using the following operators, listed
in decreasing order of precedence.
The evaluation depends on the number of arguments; see below.
Operator precedence is used when there are five or more arguments.
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>! </B><I>expr</I>

<DD>
True if
<I>expr</I>

is false.
<DT><B>( </B><I>expr</I> )

<DD>
Returns the value of <I>expr</I>.
This may be used to override the normal precedence of operators.
<DT><I>expr1</I> -<B>a</B> <I>expr2</I><DD>
True if both
<I>expr1</I>

and
<I>expr2</I>

are true.
<DT><I>expr1</I> -<B>o</B> <I>expr2</I><DD>
True if either
<I>expr1</I>

or
<I>expr2</I>

is true.

</DL>
<P>

<B>test</B> and <B>[</B> evaluate conditional
expressions using a set of rules based on the number of arguments.
<P>



<DL COMPACT>
<DT>0 arguments<DD>
The expression is false.
<DT>1 argument<DD>
The expression is true if and only if the argument is not null.
<DT>2 arguments<DD>
If the first argument is <B>!</B>, the expression is true if and
only if the second argument is null.
If the first argument is one of the unary conditional operators listed

above
under
<FONT SIZE=-1><B>CONDITIONAL EXPRESSIONS</B>,

</FONT>
the expression is true if the unary test is true.
If the first argument is not a valid unary conditional operator, the expression
is false.
<DT>3 arguments<DD>
The following conditions are applied in the order listed.
If the second argument is one of the binary conditional operators listed

above
under
<FONT SIZE=-1><B>CONDITIONAL EXPRESSIONS</B>,

</FONT>
the result of the expression is the result of the binary test using
the first and third arguments as operands.
The <B>-a</B> and <B>-o</B> operators are considered binary operators
when there are three arguments.
If the first argument is <B>!</B>, the value is the negation of
the two-argument test using the second and third arguments.
If the first argument is exactly <B>(</B> and the third argument is
exactly <B>)</B>, the result is the one-argument test of the second
argument.
Otherwise, the expression is false.
<DT>4 arguments<DD>
The following conditions are applied in the order listed.
If the first argument is <B>!</B>, the result is the negation of
the three-argument expression composed of the remaining arguments.
the two-argument test using the second and third arguments.
If the first argument is exactly <B>(</B> and the fourth argument is
exactly <B>)</B>, the result is the two-argument test of the second
and third arguments.
Otherwise, the expression is parsed and evaluated according to
precedence using the rules listed above.
<DT>5 or more arguments<DD>
The expression is parsed and evaluated according to precedence
using the rules listed above.
<P>


</DL>
<P>

When used with <B>test</B> or <B>[</B>, the <B>&lt;</B> and <B>&gt;</B> operators
sort lexicographically using ASCII ordering.
</DL>


<DT><B>times</B>

<DD>
Print the accumulated user and system times for the shell and
for processes run from the shell.  The return status is 0.
<DT><B>trap</B> [<B>-lp</B>] [[<I>arg</I>] <I>sigspec</I> ...]<DD>
The command
<I>arg</I>

is to be read and executed when the shell receives
signal(s)
<I>sigspec</I>.

If
<I>arg</I>

is absent (and there is a single <I>sigspec</I>) or
<B>-</B>,

each specified signal is
reset to its original disposition (the value it had
upon entrance to the shell).
If
<I>arg</I>

is the null string the signal specified by each
<I>sigspec</I>

is ignored by the shell and by the commands it invokes.
If
<I>arg</I>

is not present and
<B>-p</B>

has been supplied, then the trap commands associated with each
<I>sigspec</I>

are displayed.
If no arguments are supplied or if only
<B>-p</B>

is given,
<B>trap</B>

prints the list of commands associated with each signal.
The
<B>-l</B>

option causes the shell to print a list of signal names and
their corresponding numbers.
Each
<I>sigspec</I>

is either
a signal name defined in &lt;<I>signal.h</I>&gt;, or a signal number.
Signal names are case insensitive and the
<FONT SIZE=-1><B>SIG</B>

</FONT>
prefix is optional.
<P>


If a
<I>sigspec</I>

is
<FONT SIZE=-1><B>EXIT</B>

</FONT>
(0) the command
<I>arg</I>

is executed on exit from the shell.
If a
<I>sigspec</I>

is
<FONT SIZE=-1><B>DEBUG</B>,

</FONT>
the command
<I>arg</I>

is executed before every <I>simple command</I>, <I>for</I> command,
<I>case</I> command, <I>select</I> command, every arithmetic <I>for</I>
command, and before the first command executes in a shell function (see
<FONT SIZE=-1><B>SHELL GRAMMAR</B>

</FONT>

above).
Refer to the description of the <B>extdebug</B> option to the
<B>shopt</B> builtin for details of its effect on the <B>DEBUG</B> trap.
If a
<I>sigspec</I>

is
<FONT SIZE=-1><B>RETURN</B>,

</FONT>
the command
<I>arg</I>

is executed each time a shell function or a script executed with
the <B>.</B> or <B>source</B> builtins finishes executing.
<P>


If a
<I>sigspec</I>

is
<FONT SIZE=-1><B>ERR</B>,

</FONT>
the command
<I>arg</I>

is executed whenever
a pipeline (which may consist of a single simple
command), a list, or a compound command returns a
non-zero exit status,
subject to the following conditions.
The
<FONT SIZE=-1><B>ERR</B>

</FONT>
trap is not executed if the failed
command is part of the command list immediately following a
<B>while</B>

or
<B>until</B>

keyword,
part of the test in an
<I>if</I>

statement, part of a command executed in a
<B>&amp;&amp;</B>

or
<B>||</B>

list except the command following the final <B>&amp;&amp;</B> or <B>||</B>,
any command in a pipeline but the last,
or if the command's return value is
being inverted using
<B>!</B>.

These are the same conditions obeyed by the <B>errexit</B> (<B>-e</B>) option.
<P>


Signals ignored upon entry to the shell cannot be trapped or reset.
Trapped signals that are not being ignored are reset to their original
values in a subshell or subshell environment when one is created.
The return status is false if any
<I>sigspec</I>

is invalid; otherwise
<B>trap</B>

returns true.
<DT><B>type</B> [<B>-aftpP</B>] <I>name</I> [<I>name</I> ...]<DD>
With no options,
indicate how each
<I>name</I>

would be interpreted if used as a command name.
If the
<B>-t</B>

option is used,
<B>type</B>

prints a string which is one of
<I>alias</I>,

<I>keyword</I>,

<I>function</I>,

<I>builtin</I>,

or
<I>file</I>

if
<I>name</I>

is an alias, shell reserved word, function, builtin, or disk file,
respectively.
If the
<I>name</I>

is not found, then nothing is printed, and an exit status of false
is returned.
If the
<B>-p</B>

option is used,
<B>type</B>

either returns the name of the disk file
that would be executed if
<I>name</I>

were specified as a command name,
or nothing if
<TT>type -t name</TT>

would not return
<I>file</I>.

The
<B>-P</B>

option forces a
<FONT SIZE=-1><B>PATH</B>

</FONT>
search for each <I>name</I>, even if
<TT>type -t name</TT>

would not return
<I>file</I>.

If a command is hashed,
<B>-p</B>

and
<B>-P</B>

print the hashed value, which is not necessarily the file that appears
first in
<FONT SIZE=-1><B>PATH</B>.

</FONT>
If the
<B>-a</B>

option is used,
<B>type</B>

prints all of the places that contain
an executable named
<I>name</I>.

This includes aliases and functions,
if and only if the
<B>-p</B>

option is not also used.
The table of hashed commands is not consulted
when using
<B>-a</B>.

The
<B>-f</B>

option suppresses shell function lookup, as with the <B>command</B> builtin.
<B>type</B>

returns true if all of the arguments are found, false if
any are not found.
<DT><B>ulimit</B> [<B>-HS</B>] <B>-a</B><DD>

<DT><B>ulimit</B> [<B>-HS</B>] [<B>-bcdefiklmnpqrstuvxPRT</B> [<I>limit</I>]]<DD>

Provides control over the resources available to the shell and to
processes started by it, on systems that allow such control.
The <B>-H</B> and <B>-S</B> options specify that the hard or soft limit is
set for the given resource.
A hard limit cannot be increased by a non-root user once it is set;
a soft limit may be increased up to the value of the hard limit.
If neither <B>-H</B> nor <B>-S</B> is specified, both the soft and hard
limits are set.
The value of
<I>limit</I>

can be a number in the unit specified for the resource
or one of the special values
<B>hard</B>,

<B>soft</B>,

or
<B>unlimited</B>,

which stand for the current hard limit, the current soft limit, and
no limit, respectively.
If
<I>limit</I>

is omitted, the current value of the soft limit of the resource is
printed, unless the <B>-H</B> option is given.  When more than one
resource is specified, the limit name and unit, if appropriate,
are printed before the value.
Other options are interpreted as follows:
<DL COMPACT><DT><DD>

<DL COMPACT>
<DT><B>-a</B>

<DD>
All current limits are reported; no limits are set
<DT><B>-b</B>

<DD>
The maximum socket buffer size
<DT><B>-c</B>

<DD>
The maximum size of core files created
<DT><B>-d</B>

<DD>
The maximum size of a process's data segment
<DT><B>-e</B>

<DD>
The maximum scheduling priority (&quot;nice&quot;)
<DT><B>-f</B>

<DD>
The maximum size of files written by the shell and its children
<DT><B>-i</B>

<DD>
The maximum number of pending signals
<DT><B>-k</B>

<DD>
The maximum number of kqueues that may be allocated
<DT><B>-l</B>

<DD>
The maximum size that may be locked into memory
<DT><B>-m</B>

<DD>
The maximum resident set size (many systems do not honor this limit)
<DT><B>-n</B>

<DD>
The maximum number of open file descriptors (most systems do not
allow this value to be set)
<DT><B>-p</B>

<DD>
The pipe size in 512-byte blocks (this may not be set)
<DT><B>-q</B>

<DD>
The maximum number of bytes in POSIX message queues
<DT><B>-r</B>

<DD>
The maximum real-time scheduling priority
<DT><B>-s</B>

<DD>
The maximum stack size
<DT><B>-t</B>

<DD>
The maximum amount of cpu time in seconds
<DT><B>-u</B>

<DD>
The maximum number of processes available to a single user
<DT><B>-v</B>

<DD>
The maximum amount of virtual memory available to the shell and, on
some systems, to its children
<DT><B>-x</B>

<DD>
The maximum number of file locks
<DT><B>-P</B>

<DD>
The maximum number of pseudoterminals
<DT><B>-R</B>

<DD>
The maximum time a real-time process can run before blocking, in microseconds
<DT><B>-T</B>

<DD>
The maximum number of threads

</DL>
<P>

If
<I>limit</I>

is given, and the
<B>-a</B>

option is not used,
<I>limit</I> is the new value of the specified resource.
If no option is given, then
<B>-f</B>

is assumed.  Values are in 1024-byte increments, except for
<B>-t</B>,

which is in seconds;
<B>-R</B>,

which is in microseconds;
<B>-p</B>,

which is in units of 512-byte blocks;
<B>-P</B>,

<B>-T</B>,

<B>-b</B>,

<B>-k</B>,

<B>-n</B>,

and
<B>-u</B>,

which are unscaled values;
and, when in posix mode,
<B>-c</B>

and
<B>-f</B>,

which are in 512-byte increments.
The return status is 0 unless an invalid option or argument is supplied,
or an error occurs while setting a new limit.
</DL>

<DT><B>umask</B> [<B>-p</B>] [<B>-S</B>] [<I>mode</I>]<DD>
The user file-creation mask is set to
<I>mode</I>.

If
<I>mode</I>

begins with a digit, it
is interpreted as an octal number; otherwise
it is interpreted as a symbolic mode mask similar
to that accepted by
<I>chmod</I>(1).

If
<I>mode</I>

is omitted, the current value of the mask is printed.
The
<B>-S</B>

option causes the mask to be printed in symbolic form; the
default output is an octal number.
If the
<B>-p</B>

option is supplied, and
<I>mode</I>

is omitted, the output is in a form that may be reused as input.
The return status is 0 if the mode was successfully changed or if
no <I>mode</I> argument was supplied, and false otherwise.
<DT><B>unalias</B> [-<B>a</B>] [<I>name</I> ...]<DD>
Remove each <I>name</I> from the list of defined aliases.  If
<B>-a</B>

is supplied, all alias definitions are removed.  The return
value is true unless a supplied
<I>name</I>

is not a defined alias.
<DT><B>unset</B> [-<B>fv</B>] [-<B>n</B>] [<I>name</I> ...]<DD>
For each
<I>name</I>,

remove the corresponding variable or function.
If the
<B>-v</B>

option is given, each
<I>name</I>

refers to a shell variable, and that variable is removed.
Read-only variables may not be unset.
If
<B>-f</B>

is specified, each
<I>name</I>

refers to a shell function, and the function definition
is removed.
If the
<B>-n</B>

option is supplied, and <I>name</I> is a variable with the <I>nameref</I>
attribute, <I>name</I> will be unset rather than the variable it
references.
<B>-n</B> has no effect if the <B>-f</B> option is supplied.
If no options are supplied, each <I>name</I> refers to a variable; if
there is no variable by that name, a function with that name, if any, is
unset.
Each unset variable or function is removed from the environment
passed to subsequent commands.
If any of
<FONT SIZE=-1><B>BASH_ALIASES</B>,

</FONT>
<FONT SIZE=-1><B>BASH_ARGV0</B>,

</FONT>
<FONT SIZE=-1><B>BASH_CMDS</B>,

</FONT>
<FONT SIZE=-1><B>BASH_COMMAND</B>,

</FONT>
<FONT SIZE=-1><B>BASH_SUBSHELL</B>,

</FONT>
<FONT SIZE=-1><B>BASHPID</B>,

</FONT>
<FONT SIZE=-1><B>COMP_WORDBREAKS</B>,

</FONT>
<FONT SIZE=-1><B>DIRSTACK</B>,

</FONT>
<FONT SIZE=-1><B>EPOCHREALTIME</B>,

</FONT>
<FONT SIZE=-1><B>EPOCHSECONDS</B>,

</FONT>
<FONT SIZE=-1><B>FUNCNAME</B>,

</FONT>
<FONT SIZE=-1><B>GROUPS</B>,

</FONT>
<FONT SIZE=-1><B>HISTCMD</B>,

</FONT>
<FONT SIZE=-1><B>LINENO</B>,

</FONT>
<FONT SIZE=-1><B>RANDOM</B>,

</FONT>
<FONT SIZE=-1><B>SECONDS</B>,

</FONT>
or
<FONT SIZE=-1><B>SRANDOM</B>

</FONT>
are unset, they lose their special properties, even if they are
subsequently reset.  The exit status is true unless a
<I>name</I>

is readonly or may not be unset.
<DT><B>wait</B> [<B>-fn</B>] [-p <I>varname</I>] [<I>id ...</I>]<DD>
Wait for each specified child process and return its termination status.
Each
<I>id</I>

may be a process
ID or a job specification; if a job spec is given, all processes
in that job's pipeline are waited for.  If
<I>id</I>

is not given,
<B>wait</B> waits for all running background jobs and
the last-executed process substitution, if its process id is the same as
<B>$!</B>,
and the return status is zero.
If the <B>-n</B> option is supplied,
<B>wait</B> waits for a single job
from the list of <I>id</I>s or, if no <I>id</I>s are supplied, any job,
to complete and returns its exit status.
If none of the supplied arguments is a child of the shell, or if no arguments
are supplied and the shell has no unwaited-for children, the exit status
is 127.
If the <B>-p</B> option is supplied, the process or job identifier of the job
for which the exit status is returned is assigned to the variable
<I>varname</I> named by the option argument.
The variable will be unset initially, before any assignment.
This is useful only when the <B>-n</B> option is supplied.
Supplying the <B>-f</B> option, when job control is enabled,
forces <B>wait</B> to wait for <I>id</I> to terminate before returning
its status, instead of returning when it changes status.
If
<I>id</I>

specifies a non-existent process or job, the return status is 127.
If <B>wait</B> is interrupted by a signal, the return status will be greater
than 128, as described under
<B>SIGNALS</B>


above.
Otherwise, the return status is the exit status of the last
process or job waited for.
</DL>
<A NAME="lbDC">&nbsp;</A>
<H3>SHELL COMPATIBILITY MODE</H3>

Bash-4.0 introduced the concept of a <I>shell compatibility level</I>,
specified as a set of options to the shopt builtin (
<B>compat31</B>,

<B>compat32</B>,

<B>compat40</B>,

<B>compat41</B>,

and so on).
There is only one current
compatibility level -- each option is mutually exclusive.
The compatibility level is intended to allow users to select behavior
from previous versions that is incompatible with newer versions
while they migrate scripts to use current features and
behavior. It's intended to be a temporary solution.
<P>

This section does not mention behavior that is standard for a particular
version (e.g., setting <B>compat32</B> means that quoting the rhs of the regexp
matching operator quotes special regexp characters in the word, which is
default behavior in bash-3.2 and subsequent versions). 
<P>

If a user enables, say, <B>compat32</B>, it may affect the behavior of other
compatibility levels up to and including the current compatibility level.
The idea is that each compatibility level controls behavior that changed
in that version of <B>bash</B>,
but that behavior may have been present in earlier versions.
For instance, the change to use locale-based comparisons with the <B>[[</B>
command came in bash-4.1, and earlier versions used ASCII-based comparisons,
so enabling <B>compat32</B> will enable ASCII-based comparisons as well.
That granularity may not be sufficient for
all uses, and as a result users should employ compatibility levels carefully.
Read the documentation for a particular feature to find out the
current behavior.
<P>

Bash-4.3 introduced a new shell variable:
<FONT SIZE=-1><B>BASH_COMPAT</B>.

</FONT>
The value assigned
to this variable (a decimal version number like 4.2, or an integer
corresponding to the <B>compat</B><I>NN</I> option, like 42) determines the
compatibility level.
<P>

Starting with bash-4.4, Bash has begun deprecating older compatibility
levels.
Eventually, the options will be removed in favor of
<FONT SIZE=-1><B>BASH_COMPAT</B>.

</FONT>
<P>

Bash-5.0 is the final version for which there will be an individual shopt
option for the previous version. Users should use
<FONT SIZE=-1><B>BASH_COMPAT</B>

</FONT>
on bash-5.0 and later versions.
<P>

The following table describes the behavior changes controlled by each
compatibility level setting.
The <B>compat</B><I>NN</I> tag is used as shorthand for setting the
compatibility level
to <I>NN</I> using one of the following mechanisms.
For versions prior to bash-5.0, the compatibility level may be set using
the corresponding <B>compat</B><I>NN</I> shopt option.
For bash-4.3 and later versions, the
<FONT SIZE=-1><B>BASH_COMPAT</B>

</FONT>
variable is preferred,
and it is required for bash-5.1 and later versions.
<DL COMPACT>
<DT><B>compat31</B><DD>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT>*<DD>
quoting the rhs of the <B>[[</B> command's regexp matching operator (=~)
has no special effect
</DL></DL>


<DT><B>compat32</B><DD>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT>*<DD>
interrupting a command list such as &quot;a ; b ; c&quot; causes the execution
of the next command in the list (in bash-4.0 and later versions,
the shell acts as if it received the interrupt, so
interrupting one command in a list aborts the execution of the
entire list)
</DL></DL>


<DT><B>compat40</B><DD>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT>*<DD>
the <B>&lt;</B> and <B>&gt;</B> operators to the <B>[[</B> command do not
consider the current locale when comparing strings; they use ASCII
ordering.
Bash versions prior to bash-4.1 use ASCII collation and
<I>strcmp</I>(3);

bash-4.1 and later use the current locale's collation sequence and
<I>strcoll</I>(3).

</DL></DL>


<DT><B>compat41</B><DD>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT>*<DD>
in <I>posix</I> mode, <B>time</B> may be followed by options and still be
recognized as a reserved word (this is POSIX interpretation 267)
<DT>*<DD>
in <I>posix</I> mode, the parser requires that an even number of single
quotes occur in the <I>word</I> portion of a double-quoted
parameter expansion and treats them specially, so that characters within
the single quotes are considered quoted
(this is POSIX interpretation 221)
</DL></DL>


<DT><B>compat42</B><DD>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT>*<DD>
the replacement string in double-quoted pattern substitution does not
undergo quote removal, as it does in versions after bash-4.2
<DT>*<DD>
in posix mode, single quotes are considered special when expanding
the <I>word</I> portion of a double-quoted parameter expansion
and can be used to quote a closing brace or other special character
(this is part of POSIX interpretation 221);
in later versions, single quotes
are not special within double-quoted word expansions
</DL></DL>


<DT><B>compat43</B><DD>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT>*<DD>
the shell does not print a warning message if an attempt is made to
use a quoted compound assignment as an argument to declare
(e.g., declare -a foo=aq(1 2)aq). Later versions warn that this usage is
deprecated
<DT>*<DD>
word expansion errors are considered non-fatal errors that cause the
current command to fail, even in posix mode
(the default behavior is to make them fatal errors that cause the shell
to exit)
<DT>*<DD>
when executing a shell function, the loop state (while/until/etc.)
is not reset, so <B>break</B> or <B>continue</B> in that function will break
or continue loops in the calling context. Bash-4.4 and later reset
the loop state to prevent this
</DL></DL>


<DT><B>compat44</B><DD>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT>*<DD>
the shell sets up the values used by
<FONT SIZE=-1><B>BASH_ARGV</B>

</FONT>
and
<FONT SIZE=-1><B>BASH_ARGC</B>

</FONT>
so they can expand to the shell's positional parameters even if extended
debugging mode is not enabled
<DT>*<DD>
a subshell inherits loops from its parent context, so <B>break</B>
or <B>continue</B> will cause the subshell to exit.
Bash-5.0 and later reset the loop state to prevent the exit
<DT>*<DD>
variable assignments preceding builtins like <B>export</B> and <B>readonly</B>
that set attributes continue to affect variables with the same
name in the calling environment even if the shell is not in posix
mode
</DL></DL>


<DT><B>compat50</B><DD>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT>*<DD>
Bash-5.1 changed the way
<FONT SIZE=-1><B>$RANDOM</B>

</FONT>
is generated to introduce slightly
more randomness. If the shell compatibility level is set to 50 or
lower, it reverts to the method from bash-5.0 and previous versions,
so seeding the random number generator by assigning a value to
<FONT SIZE=-1><B>RANDOM</B>

</FONT>
will produce the same sequence as in bash-5.0
<DT>*<DD>
If the command hash table is empty, bash versions prior to bash-5.1
printed an informational message to that effect, even when producing
output that can be reused as input. Bash-5.1 suppresses that message
when the <B>-l</B> option is supplied.
</DL></DL>


<DT><B>compat51</B><DD>

<DL COMPACT><DT><DD>
<DL COMPACT>
<DT>*<DD>
The <B>unset</B> builtin treats attempts to unset array subscripts <B>@</B>
and <B>*</B> differently depending on whether the array is indexed or
associative, and differently than in previous versions.
</DL></DL>




</DL>
<A NAME="lbDD">&nbsp;</A>
<H3>RESTRICTED SHELL</H3>



<P>

If
<B>bash</B>

is started with the name
<B>rbash</B>,

or the
<B>-r</B>

option is supplied at invocation,
the shell becomes restricted.
A restricted shell is used to
set up an environment more controlled than the standard shell.
It behaves identically to
<B>bash</B>

with the exception that the following are disallowed or not performed:
<DL COMPACT>
<DT>*<DD>
changing directories with <B>cd</B>
<DT>*<DD>
setting or unsetting the values of
<FONT SIZE=-1><B>SHELL</B>,

</FONT>
<FONT SIZE=-1><B>PATH</B>,

</FONT>
<FONT SIZE=-1><B>HISTFILE</B>,

</FONT>
<FONT SIZE=-1><B>ENV</B>,

</FONT>
or
<FONT SIZE=-1><B>BASH_ENV</B>

</FONT>
<DT>*<DD>
specifying command names containing
<B>/</B>

<DT>*<DD>
specifying a filename containing a
<B>/</B>

as an argument to the
<B>.</B>

builtin command
<DT>*<DD>
specifying a filename containing a slash as an argument to the
<B>history</B>

builtin command
<DT>*<DD>
specifying a filename containing a slash as an argument to the
<B>-p</B>

option to the
<B>hash</B>

builtin command
<DT>*<DD>
importing function definitions from the shell environment at startup
<DT>*<DD>
parsing the value of
<FONT SIZE=-1><B>SHELLOPTS</B>

</FONT>
from the shell environment at startup
<DT>*<DD>
redirecting output using the &gt;, &gt;|, &lt;&gt;, &gt;&amp;, &amp;&gt;, and &gt;&gt; redirection operators
<DT>*<DD>
using the
<B>exec</B>

builtin command to replace the shell with another command
<DT>*<DD>
adding or deleting builtin commands with the
<B>-f</B>

and
<B>-d</B>

options to the
<B>enable</B>

builtin command
<DT>*<DD>
using the <B>enable</B> builtin command to enable disabled shell builtins
<DT>*<DD>
specifying the
<B>-p</B>

option to the
<B>command</B>

builtin command
<DT>*<DD>
turning off restricted mode with
<B>set +r</B> or <B>shopt -u restricted_shell</B>.
</DL>
<P>

These restrictions are enforced after any startup files are read.
<P>


 When a command that is found to be a shell script is executed
(see
<FONT SIZE=-1><B>COMMAND EXECUTION</B>

</FONT>

above),

<B>rbash</B>

turns off any restrictions in the shell spawned to execute the
script.


<A NAME="lbDE">&nbsp;</A>
<H3>SEE ALSO</H3>


<DL COMPACT>
<DT><I>Bash Reference Manual</I>, Brian Fox and Chet Ramey<DD>
<DT><I>The Gnu Readline Library</I>, Brian Fox and Chet Ramey<DD>
<DT><I>The Gnu History Library</I>, Brian Fox and Chet Ramey<DD>
<DT><I>Portable Operating System Interface (POSIX) Part 2: Shell and Utilities</I>, IEEE --<DD>
<A HREF="http://pubs.opengroup.org/onlinepubs/9699919799/">http://pubs.opengroup.org/onlinepubs/9699919799/</A>
<DT><A HREF="http://tiswww.case.edu/~chet/bash/POSIX">http://tiswww.case.edu/~chet/bash/POSIX</A> -- a description of posix mode<DD>
<DT><I>sh</I>(1), <I>ksh</I>(1), <I>csh</I>(1)<DD>
<DT><I>emacs</I>(1), <I>vi</I>(1)<DD>
<DT><I>readline</I>(3)<DD>

</DL>
<A NAME="lbDF">&nbsp;</A>
<H3>FILES</H3>


<DL COMPACT>
<DT>
<A HREF="file:/bin/bash"><I>/bin/bash</I></A>

<DD>
The <B>bash</B> executable
<DT>
<A HREF="file:/etc/profile"><I>/etc/profile</I></A>

<DD>
The systemwide initialization file, executed for login shells
<DT>
<A HREF="file:~/.bash_profile"><I>~/.bash_profile</I></A>

<DD>
The personal initialization file, executed for login shells
<DT>
<A HREF="file:~/.bashrc"><I>~/.bashrc</I></A>

<DD>
The individual per-interactive-shell startup file
<DT>
<A HREF="file:~/.bash_logout"><I>~/.bash_logout</I></A>

<DD>
The individual login shell cleanup file, executed when a login shell exits
<DT>
<A HREF="file:~/.bash_history"><I>~/.bash_history</I></A>

<DD>
The default value of <B>HISTFILE</B>, the file in which bash saves the
command history
<DT>
<A HREF="file:~/.inputrc"><I>~/.inputrc</I></A>

<DD>
Individual <I>readline</I> initialization file

</DL>
<A NAME="lbDG">&nbsp;</A>
<H3>AUTHORS</H3>

Brian Fox, Free Software Foundation
<BR>

<A HREF="mailto:<EMAIL>"><EMAIL></A>
<P>

Chet Ramey, Case Western Reserve University
<BR>

<A HREF="mailto:<EMAIL>"><EMAIL></A>
<A NAME="lbDH">&nbsp;</A>
<H3>BUG REPORTS</H3>

If you find a bug in
<B>bash,</B>

you should report it.  But first, you should
make sure that it really is a bug, and that it appears in the latest
version of
<B>bash</B>.

The latest version is always available from
<I><A HREF="ftp://ftp.gnu.org/pub/gnu/bash/">ftp://ftp.gnu.org/pub/gnu/bash/</A></I> and
<I><A HREF="http://git.savannah.gnu.org/cgit/bash.git/snapshot/bash-master.tar.gz">http://git.savannah.gnu.org/cgit/bash.git/snapshot/bash-master.tar.gz</A></I>.
<P>

Once you have determined that a bug actually exists, use the
<I>bashbug</I>

command to submit a bug report.
If you have a fix, you are encouraged to mail that as well!
Suggestions and `philosophical' bug reports may be mailed
to <I><A HREF="mailto:<EMAIL>"><EMAIL></A></I> or posted to the Usenet
newsgroup
<A HREF="news:gnu.bash.bug">gnu.bash.bug</A>.

<P>

ALL bug reports should include:
<P>


<DL COMPACT>
<DT>The version number of <B>bash</B><DD>
<DT>The hardware and operating system<DD>
<DT>The compiler used to compile<DD>
<DT>A description of the bug behaviour<DD>
<DT>A short script or `recipe' which exercises the bug<DD>

</DL>
<P>

<I>bashbug</I>

inserts the first three items automatically into the template
it provides for filing a bug report.
<P>

Comments and bug reports concerning
this manual page should be directed to
<I><A HREF="mailto:<EMAIL>"><EMAIL></A></I>.

<A NAME="lbDI">&nbsp;</A>
<H3>BUGS</H3>

It's too big and too slow.
<P>

There are some subtle differences between
<B>bash</B>

and traditional versions of
<B>sh</B>,

mostly because of the
<FONT SIZE=-1><B>POSIX</B>

</FONT>
specification.
<P>

Aliases are confusing in some uses.
<P>

Shell builtin commands and functions are not stoppable/restartable.
<P>

Compound commands and command sequences of the form `a ; b ; c'
are not handled gracefully when process suspension is attempted.
When a process is stopped, the shell immediately executes the next
command in the sequence.
It suffices to place the sequence of commands between
parentheses to force it into a subshell, which may be stopped as
a unit.
<P>

Array variables may not (yet) be exported.
<P>

There may be only one active coprocess at a time.



<HR>
<TABLE WIDTH=100%>
<TR>
<TH ALIGN=LEFT width=33%>GNU Bash 5.2<TH ALIGN=CENTER width=33%>2022 September 19<TH ALIGN=RIGHT width=33%>BASH(1)
</TR>
</TABLE>
<HR>
<A NAME="index">&nbsp;</A><H2>Index</H2>
<DL>
<DT><A HREF="#lbAB">NAME</A><DD>
<DT><A HREF="#lbAC">SYNOPSIS</A><DD>
<DT><A HREF="#lbAD">COPYRIGHT</A><DD>
<DT><A HREF="#lbAE">DESCRIPTION</A><DD>
<DT><A HREF="#lbAF">OPTIONS</A><DD>
<DT><A HREF="#lbAG">ARGUMENTS</A><DD>
<DT><A HREF="#lbAH">INVOCATION</A><DD>
<DT><A HREF="#lbAI">DEFINITIONS</A><DD>
<DT><A HREF="#lbAJ">RESERVED WORDS</A><DD>
<DT><A HREF="#lbAK">SHELL GRAMMAR</A><DD>
<DL>
<DT><A HREF="#lbAL">Simple Commands</A><DD>
<DT><A HREF="#lbAM">Pipelines</A><DD>
<DT><A HREF="#lbAN">Lists</A><DD>
<DT><A HREF="#lbAO">Compound Commands</A><DD>
<DT><A HREF="#lbAP">Coprocesses</A><DD>
<DT><A HREF="#lbAQ">Shell Function Definitions</A><DD>
</DL>
<DT><A HREF="#lbAR">COMMENTS</A><DD>
<DT><A HREF="#lbAS">QUOTING</A><DD>
<DT><A HREF="#lbAT">PARAMETERS</A><DD>
<DL>
<DT><A HREF="#lbAU">Positional Parameters</A><DD>
<DT><A HREF="#lbAV">Special Parameters</A><DD>
<DT><A HREF="#lbAW">Shell Variables</A><DD>
<DT><A HREF="#lbAX">Arrays</A><DD>
</DL>
<DT><A HREF="#lbAY">EXPANSION</A><DD>
<DL>
<DT><A HREF="#lbAZ">Brace Expansion</A><DD>
<DT><A HREF="#lbBA">Tilde Expansion</A><DD>
<DT><A HREF="#lbBB">Parameter Expansion</A><DD>
<DT><A HREF="#lbBC">Command Substitution</A><DD>
<DT><A HREF="#lbBD">Arithmetic Expansion</A><DD>
<DT><A HREF="#lbBE">Process Substitution</A><DD>
<DT><A HREF="#lbBF">Word Splitting</A><DD>
<DT><A HREF="#lbBG">Pathname Expansion</A><DD>
<DT><A HREF="#lbBH">Quote Removal</A><DD>
</DL>
<DT><A HREF="#lbBI">REDIRECTION</A><DD>
<DL>
<DT><A HREF="#lbBJ">Redirecting Input</A><DD>
<DT><A HREF="#lbBK">Redirecting Output</A><DD>
<DT><A HREF="#lbBL">Appending Redirected Output</A><DD>
<DT><A HREF="#lbBM">Redirecting Standard Output and Standard Error</A><DD>
<DT><A HREF="#lbBN">Appending Standard Output and Standard Error</A><DD>
<DT><A HREF="#lbBO">Here Documents</A><DD>
<DT><A HREF="#lbBP">Here Strings</A><DD>
<DT><A HREF="#lbBQ">Duplicating File Descriptors</A><DD>
<DT><A HREF="#lbBR">Moving File Descriptors</A><DD>
<DT><A HREF="#lbBS">Opening File Descriptors for Reading and Writing</A><DD>
</DL>
<DT><A HREF="#lbBT">ALIASES</A><DD>
<DT><A HREF="#lbBU">FUNCTIONS</A><DD>
<DT><A HREF="#lbBV">ARITHMETIC EVALUATION</A><DD>
<DT><A HREF="#lbBW">CONDITIONAL EXPRESSIONS</A><DD>
<DT><A HREF="#lbBX">SIMPLE COMMAND EXPANSION</A><DD>
<DT><A HREF="#lbBY">COMMAND EXECUTION</A><DD>
<DT><A HREF="#lbBZ">COMMAND EXECUTION ENVIRONMENT</A><DD>
<DT><A HREF="#lbCA">ENVIRONMENT</A><DD>
<DT><A HREF="#lbCB">EXIT STATUS</A><DD>
<DT><A HREF="#lbCC">SIGNALS</A><DD>
<DT><A HREF="#lbCD">JOB CONTROL</A><DD>
<DT><A HREF="#lbCE">PROMPTING</A><DD>
<DT><A HREF="#lbCF">READLINE</A><DD>
<DL>
<DT><A HREF="#lbCG">Readline Notation</A><DD>
<DT><A HREF="#lbCH">Readline Initialization</A><DD>
<DT><A HREF="#lbCI">Readline Key Bindings</A><DD>
<DT><A HREF="#lbCJ">Readline Variables</A><DD>
<DT><A HREF="#lbCK">Readline Conditional Constructs</A><DD>
<DT><A HREF="#lbCL">Searching</A><DD>
<DT><A HREF="#lbCM">Readline Command Names</A><DD>
<DT><A HREF="#lbCN">Commands for Moving</A><DD>
<DT><A HREF="#lbCO">Commands for Manipulating the History</A><DD>
<DT><A HREF="#lbCP">Commands for Changing Text</A><DD>
<DT><A HREF="#lbCQ">Killing and Yanking</A><DD>
<DT><A HREF="#lbCR">Numeric Arguments</A><DD>
<DT><A HREF="#lbCS">Completing</A><DD>
<DT><A HREF="#lbCT">Keyboard Macros</A><DD>
<DT><A HREF="#lbCU">Miscellaneous</A><DD>
<DT><A HREF="#lbCV">Programmable Completion</A><DD>
</DL>
<DT><A HREF="#lbCW">HISTORY</A><DD>
<DT><A HREF="#lbCX">HISTORY EXPANSION</A><DD>
<DL>
<DT><A HREF="#lbCY">Event Designators</A><DD>
<DT><A HREF="#lbCZ">Word Designators</A><DD>
<DT><A HREF="#lbDA">Modifiers</A><DD>
</DL>
<DT><A HREF="#lbDB">SHELL BUILTIN COMMANDS</A><DD>
<DT><A HREF="#lbDC">SHELL COMPATIBILITY MODE</A><DD>
<DT><A HREF="#lbDD">RESTRICTED SHELL</A><DD>
<DT><A HREF="#lbDE">SEE ALSO</A><DD>
<DT><A HREF="#lbDF">FILES</A><DD>
<DT><A HREF="#lbDG">AUTHORS</A><DD>
<DT><A HREF="#lbDH">BUG REPORTS</A><DD>
<DT><A HREF="#lbDI">BUGS</A><DD>
</DL>
<HR>
This document was created by man2html from /usr/local/src/bash/bash-20220907/doc/bash.1.<BR>
Time: 19 September 2022 12:02:51 EDT
</BODY>
</HTML>
