# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the mintty package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2025-03-22 07:33+0100\n"
"PO-Revision-Date: 2022-09-16 00:06+0200\n"
"Last-Translator: A Regnander <anton_r_3 at hotmail dot com>\n"
"Language-Team: \n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.1.1\n"

#: charset.c:228 charset.c:239 winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "(Default)"
msgstr "(standard)"

#: charset.c:250
msgid "(OEM codepage)"
msgstr "(OEM-teckentabell)"

#: charset.c:254
msgid "(ANSI codepage)"
msgstr "(ANSI-teckentabell)"

#: child.c:96
msgid "There are no available terminals"
msgstr "Det finns inga tillgängliga terminaler"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "Fel: Kunde inte öppna loggfilen"

#: child.c:334
msgid "Error: Could not fork child process"
msgstr "Fel: Kunde inte förgrena underprocess"

#: child.c:336
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr "Ombasering av DLL-filer kanske krävs; se \"rebaseall / rebase --help\""

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:426
msgid "Failed to run '%s': %s"
msgstr "Misslyckades att köra \"%s\": %s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:578
msgid "%s: Exit %i"
msgstr "%s: Avsluta %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:585
msgid "TERMINATED"
msgstr "AVSLUTAD"

#: child.c:1232
msgid "Error: Could not fork child daemon"
msgstr "Fel: Kunde inte förgrena underdemon"

#. __ Setting false for Boolean options (localization optional)
#: config.c:655
msgid "no"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:657
msgid "yes"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:659
msgid "false"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:661
msgid "true"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:663
msgid "off"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:665
msgid "on"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:694
msgid "stretch"
msgstr "sträck ut"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:696
msgid "align"
msgstr "justera"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:698
msgid "middle"
msgstr "centrerad"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:700
msgid "full"
msgstr "fullständig"

#. __ %s: unknown option name
#: config.c:866
msgid "Ignoring unknown option '%s'"
msgstr "Ignorera det okända alternativet \"%s\""

#: config.c:914 config.c:943
msgid "Internal error: too many options"
msgstr "Internt fel: För många alternativ"

#: config.c:930
msgid "Internal error: too many options/comments"
msgstr "Internt fel: För många alternativ/kommentarer"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1100
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "Ignorerar det okänt värdet \"%s\" för alternativet \"%s\""

#. __ %s: option name
#: config.c:1112
msgid "Ignoring option '%s' with missing value"
msgstr "Ignorerar alternativet \"%s\" som saknar värde"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1791
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"Kunde inte spara alternativ till \"%s\":\n"
"%s."

#: config.c:2172
msgid "◇ None (printing disabled) ◇"
msgstr "◇ Ingen (utskrift inaktiverat) ◇"

#: config.c:2174
msgid "◆ Default printer ◆"
msgstr "◆ Standardskrivare ◆"

#. __ UI localization disabled
#: config.c:2283
msgid "– None –"
msgstr "– Inget –"

#. __ UI localization: use Windows desktop setting
#: config.c:2285
msgid "@ Windows language @"
msgstr "@ Windowsspråk @"

#. __ UI localization: use environment variable setting (LANGUAGE, LC_*)
#: config.c:2287
msgid "* Locale environm. *"
msgstr "* Miljöspråk *"

#. __ UI localization: use mintty configuration setting (Text - Locale)
#: config.c:2289
msgid "= cfg. Text Locale ="
msgstr "= Konfigurerat språk ="

#: config.c:2394
msgid "simple beep"
msgstr "enkel ljudsignal"

#: config.c:2395
msgid "no beep"
msgstr "ingen ljudsignal"

#: config.c:2396
msgid "Default Beep"
msgstr "Standardljudsignal"

#: config.c:2397
msgid "Critical Stop"
msgstr "Kritiskt stopp"

#: config.c:2398
msgid "Question"
msgstr "Fråga"

#: config.c:2399
msgid "Exclamation"
msgstr "Varning"

#: config.c:2400
msgid "Asterisk"
msgstr "Asterisk"

#: config.c:2443
msgid "◇ None (system sound) ◇"
msgstr "◇ Inget (systemljud) ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2874 config.c:3425
msgid "◇ None ◇"
msgstr "◇ Inget ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2877
msgid "downloaded / give me a name!"
msgstr "nedladdad / namnge mig!"

#: config.c:2983
msgid "Could not load web theme"
msgstr "Kunde inte läsa in webbtema"

#: config.c:3040
msgid "Cannot write theme file"
msgstr "Kan inte skriva temafil"

#: config.c:3045
msgid "Cannot store theme file"
msgstr "Kan inte lagra temafil"

#. __ Options - Text:
#: config.c:3502 config.c:3840 config.c:3939
msgid "as font"
msgstr "teckensnitt"

#. __ Options - Text:
#: config.c:3503 config.c:3845 config.c:3944
msgid "as colour"
msgstr "färg"

#: config.c:3504
msgid "as font & as colour"
msgstr "teckensnitt & färg"

#. __ Options - Text:
#: config.c:3505 config.c:3850 config.c:3949
msgid "xterm"
msgstr "xterm"

#. __ Dialog button - show About text
#: config.c:3653
msgid "About..."
msgstr "Om..."

#. __ Dialog button - save changes
#: config.c:3656
msgid "Save"
msgstr "Spara"

#. __ Dialog button - cancel
#: config.c:3660 winctrls.c:1277 windialog.c:895
msgid "Cancel"
msgstr "Avbryt"

#. __ Dialog button - apply changes
#: config.c:3664
msgid "Apply"
msgstr "Verkställ"

#. __ Dialog button - take notice
#: config.c:3668 windialog.c:892
msgid "I see"
msgstr "Jag förstår"

#. __ Dialog button - confirm action
#: config.c:3670 winctrls.c:1276 windialog.c:894
msgid "OK"
msgstr "OK"

#. __ Options - Looks: treeview label
#: config.c:3677 config.c:3708 config.c:3767
msgid "Looks"
msgstr "Utseende"

#. __ Options - Looks: panel title
#: config.c:3679
msgid "Looks in Terminal"
msgstr "Terminalutseende"

#. __ Options - Looks: section title
#: config.c:3681
msgid "Colours"
msgstr "Färger"

#. __ Options - Looks:
#: config.c:3685
msgid "&Foreground..."
msgstr "&Förgrund..."

#. __ Options - Looks:
#: config.c:3689
msgid "&Background..."
msgstr "&Bakgrund..."

#. __ Options - Looks:
#: config.c:3693
msgid "&Cursor..."
msgstr "Mark&ör..."

#. __ Options - Looks:
#: config.c:3697
msgid "&Theme"
msgstr "&Tema"

#. __ Options - Looks: name of web service
#: config.c:3702
msgid "Color Scheme Designer"
msgstr "Färgtemadesigner"

#. __ Options - Looks: store colour scheme
#: config.c:3705 winctrls.c:484
msgid "Store"
msgstr "Lagra"

#. __ Options - Looks: section title
#: config.c:3710
msgid "Transparency"
msgstr "Transparens"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3716 config.c:4098 config.c:4261 config.c:4459
msgid "&Off"
msgstr "A&v"

#. __ Options - Looks: transparency
#: config.c:3718
msgid "&Low"
msgstr "&Låg"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3720
msgid "&Med."
msgstr "&Med."

#. __ Options - Looks: transparency
#: config.c:3722
msgid "&Medium"
msgstr "&Medel"

#. __ Options - Looks: transparency
#: config.c:3724
msgid "&High"
msgstr "&Hög"

#. __ Options - Looks: transparency
#: config.c:3726
msgid "Gla&ss"
msgstr "Gla&s"

#. __ Options - Looks: transparency
#: config.c:3733 config.c:3745 config.c:3752
msgid "Opa&que when focused"
msgstr "&Ingen transparens vid fokus"

#. __ Options - Looks: transparency
#: config.c:3738
msgid "Blu&r"
msgstr "S&uddighet"

#: config.c:3759
msgid "◄"
msgstr ""

#: config.c:3762
msgid "►"
msgstr ""

#. __ Options - Looks: section title
#: config.c:3769
msgid "Cursor"
msgstr "Markör"

#. __ Options - Looks: cursor type
#: config.c:3774
msgid "Li&ne"
msgstr "Li&nje"

#. __ Options - Looks: cursor type
#: config.c:3776
msgid "Bloc&k"
msgstr "Blo&ck"

#. __ Options - Looks: cursor type
#: config.c:3779
msgid "Bo&x"
msgstr "R&uta"

#. __ Options - Looks: cursor type
#: config.c:3782
msgid "&Underscore"
msgstr "&Understreck"

#. __ Options - Looks: cursor feature
#: config.c:3787
msgid "Blinkin&g"
msgstr "B&linkande markör"

#. __ Options - Text: treeview label
#: config.c:3794 config.c:3819 config.c:3834 config.c:3883 config.c:3933
#: config.c:3958 config.c:3980 config.c:3993 config.c:4001
msgid "Text"
msgstr "Text"

#. __ Options - Text: panel title
#: config.c:3796
msgid "Text and Font properties"
msgstr "Text- och teckensnittsegenskaper"

#. __ Options - Text: section title
#: config.c:3798
msgid "Font"
msgstr "Teckensnitt"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3806 winctrls.c:1287
msgid "Font st&yle:"
msgstr "St&il:"

#. __ Font chooser:
#: config.c:3811 winctrls.c:1289
msgid "&Size:"
msgstr "&Storlek:"

#. __ Options - Text:
#: config.c:3823 config.c:3902
msgid "Sho&w bold as font"
msgstr "&Visa fetstil som teckensnitt"

#. __ Options - Text:
#: config.c:3828 config.c:3907
msgid "Show &bold as colour"
msgstr "&Visa fetstil som färg"

#. __ Options - Text:
#: config.c:3836 config.c:3857 config.c:3935 config.c:3962
msgid "Show bold"
msgstr "Visa fetstil som"

#. __ Options - Text:
#: config.c:3864 config.c:3912 config.c:3968
msgid "&Allow blinking"
msgstr "&Blinka"

#. __ Options - Text:
#: config.c:3869 config.c:3973
msgid "Show dim as font"
msgstr "Visa dim som teckensnitt"

#. __ Options - Text:
#: config.c:3887 config.c:3920 config.c:3955
msgid "Font smoothing"
msgstr "Teckensnittsutjämning"

#. __ Options - Text:
#: config.c:3890 config.c:3923 config.c:4149 config.c:4188 config.c:4342
#: config.c:4355
msgid "&Default"
msgstr "&Standard"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3892 config.c:3925 config.c:4147 config.c:4186 config.c:4340
#: config.c:4353 config.c:4440
msgid "&None"
msgstr "&Ingen"

#. __ Options - Text:
#: config.c:3894 config.c:3927 config.c:4148 config.c:4187 config.c:4341
#: config.c:4354
msgid "&Partial"
msgstr "&Delvis"

#. __ Options - Text:
#: config.c:3896 config.c:3929 config.c:4150 config.c:4189 config.c:4343
#: config.c:4356
msgid "&Full"
msgstr "&Full"

#: config.c:3983
msgid "&Locale"
msgstr "Spr&åk"

#: config.c:3986
msgid "&Character set"
msgstr "Tecken&uppsättning"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3997 config.c:4003
msgid "Emojis"
msgstr "Uttrycksymboler"

#. __ Options - Text - Emojis:
#: config.c:4007
msgid "Style"
msgstr "Stil"

#. __ Options - Text - Emojis:
#: config.c:4012
msgid "Placement"
msgstr "Placering"

#. __ Options - Keys: treeview label
#: config.c:4020 config.c:4050 config.c:4085 config.c:4103
msgid "Keys"
msgstr "Tangenter"

#. __ Options - Keys: panel title
#: config.c:4022
msgid "Keyboard features"
msgstr "Tangentbordsfunktioner"

#. __ Options - Keys:
#: config.c:4026
msgid "&Backarrow sends ^H"
msgstr "&Bakåtpil skriver ^H"

#. __ Options - Keys:
#: config.c:4031
msgid "&Delete sends DEL"
msgstr "&Delete skriver DEL"

#. __ Options - Keys:
#: config.c:4036
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "Ctrl+VänsterAlt fungerar likadant som Alt&Gr"

#. __ Options - Keys:
#: config.c:4041
msgid "AltGr is also Alt"
msgstr "AltGr fungerar även likadant som Alt"

#. __ Options - Keys:
#: config.c:4046
msgid "&Esc/Enter reset IME to alphanumeric"
msgstr "&Esc/Enter återställer IME till alfanumeriskt"

#. __ Options - Keys: section title
#: config.c:4052
msgid "Shortcuts"
msgstr "Kortkommandon"

#. __ Options - Keys:
#: config.c:4055
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "Ko&piera och klistra in (Ctrl/Shift+Ins)"

#. __ Options - Keys:
#: config.c:4060
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "&Meny och helskärm (Alt+Mellanslag/Enter)"

#. __ Options - Keys:
#: config.c:4065
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "B&yt fönster (Ctrl+[Shift+]Tabb)"

#. __ Options - Keys:
#: config.c:4070
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "&Zooma (Ctrl+plus/minus/noll)"

#. __ Options - Keys:
#: config.c:4075
msgid "&Alt+Fn shortcuts"
msgstr "Kortkommandon för Alt+&Fn"

#. __ Options - Keys:
#: config.c:4080
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "Kortkommandon för C&trl+Shift+bokstav"

#. __ Options - Keys: section title
#: config.c:4087 config.c:4105
msgid "Compose key"
msgstr "Kombinationstangent"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:4092 config.c:4253 config.c:4272 config.c:4451 config.c:4470
msgid "&Shift"
msgstr "&Shift"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:4094 config.c:4255 config.c:4280 config.c:4453 config.c:4478
msgid "&Ctrl"
msgstr "&Ctrl"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4096 config.c:4257 config.c:4276 config.c:4455 config.c:4474
msgid "&Alt"
msgstr "&Alt"

#. __ Options - Mouse: treeview label
#: config.c:4112 config.c:4201 config.c:4233
msgid "Mouse"
msgstr "Mus"

#. __ Options - Mouse: panel title
#: config.c:4114
msgid "Mouse functions"
msgstr "Musfunktioner"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4122 config.c:4155 config.c:4171 config.c:4319
msgid "Cop&y on select"
msgstr "Kopiera vid &markering"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4127 config.c:4160 config.c:4324 wininput.c:685
msgid "Copy with TABs"
msgstr "Kopiera (som text) med &TABB"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4132 config.c:4165 config.c:4177 config.c:4329
msgid "Copy as &rich text"
msgstr "Kopiera som &RTF-text"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4138 config.c:4145 config.c:4184 config.c:4338 config.c:4351
msgid "Copy as &HTML"
msgstr "Kopiera som &HTML"

#. __ Options - Mouse:
#: config.c:4197
msgid "Clic&ks place command line cursor"
msgstr "&Placera kommandotolkens markör vid musklick"

#. __ Options - Mouse: section title
#: config.c:4203
msgid "Click actions"
msgstr "Klickåtgärder"

#. __ Options - Mouse:
#: config.c:4206
msgid "Right mouse button"
msgstr "Höger musknapp"

#. __ Options - Mouse:
#: config.c:4209 config.c:4223
msgid "&Paste"
msgstr "&Klistra in"

#. __ Options - Mouse:
#: config.c:4211 config.c:4225
msgid "E&xtend"
msgstr "&Utvidga"

#. __ Options - Mouse:
#: config.c:4213
msgid "&Menu"
msgstr "&Meny"

#. __ Options - Mouse:
#: config.c:4215 config.c:4229
msgid "Ente&r"
msgstr "Ente&r"

#. __ Options - Mouse:
#: config.c:4220
msgid "Middle mouse button"
msgstr "Mittenmusknapp"

#. __ Options - Mouse:
#: config.c:4227
msgid "&Nothing"
msgstr "&Inget"

#. __ Options - Mouse: section title
#: config.c:4235
msgid "Application mouse mode"
msgstr "Applikationens musläge"

#. __ Options - Mouse:
#: config.c:4238
msgid "Default click target"
msgstr "Standardklickmål"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4241
msgid "&Window"
msgstr "&Fönster"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4243
msgid "&Application"
msgstr "App&likation"

#. __ Options - Mouse:
#: config.c:4250 config.c:4268
msgid "Modifier for overriding default"
msgstr "Modifierare för att överskrida standardvärden"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4259 config.c:4284 config.c:4457 config.c:4482
msgid "&Win"
msgstr "&Win"

#. __ Options - Modifier - Super:
#: config.c:4288 config.c:4486
msgid "&Sup"
msgstr "S&up"

#. __ Options - Modifier - Hyper:
#: config.c:4292 config.c:4490
msgid "&Hyp"
msgstr "&Hyp"

#. __ Options - Selection: treeview label
#: config.c:4302 config.c:4313 config.c:4377
msgid "Selection"
msgstr "Markering"

#. __ Options - Selection: panel title
#: config.c:4304
msgid "Selection and clipboard"
msgstr "Markering och urklipp"

#. __ Options - Selection:
#: config.c:4308
msgid "Clear selection on input"
msgstr "Rensa markering vid inmatning"

#. __ Options - Selection: section title
#: config.c:4315
msgid "Clipboard"
msgstr "Urklipp"

#. __ Options - Selection:
#: config.c:4365
msgid "Trim space from selection"
msgstr "Rensa omgivande mellanrum från markering"

#. __ Options - Selection:
#: config.c:4371
msgid "Allow setting selection"
msgstr "Låt kontrollsekvenser ändra markeringar"

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4379 config.c:4401 config.c:4426 config.c:4499
msgid "Window"
msgstr "Fönster"

#. __ Options - Selection: clock position of info popup for text size
#: config.c:4384
msgid "Show size while selecting (0..12)"
msgstr "Visa storlek vid markering (0..12)"

#. __ Options - Selection:
#: config.c:4391
msgid "Suspend output while selecting"
msgstr "Förhindra utmatning vid markering"

#. __ Options - Window: panel title
#: config.c:4403
msgid "Window properties"
msgstr "Fönsteregenskaper"

#. __ Options - Window: section title
#: config.c:4405
msgid "Default size"
msgstr "Standardstorlek"

#. __ Options - Window:
#: config.c:4409
msgid "Colu&mns"
msgstr "Kolu&mner"

#. __ Options - Window:
#: config.c:4413
msgid "Ro&ws"
msgstr "Ra&der"

#. __ Options - Window:
#: config.c:4417
msgid "C&urrent size"
msgstr "&Nuvarande"

#. __ Options - Window:
#: config.c:4422
msgid "Re&wrap on resize"
msgstr "Justera radbrytningar vid storleksförändring"

#. __ Options - Window:
#: config.c:4430
msgid "Scroll&back lines"
msgstr "&Radbuffert"

#. __ Options - Window:
#: config.c:4435
msgid "Scrollbar"
msgstr "Rullningslist"

#. __ Options - Window: scrollbar
#: config.c:4438
msgid "&Left"
msgstr "&Vänster"

#. __ Options - Window: scrollbar
#: config.c:4442
msgid "&Right"
msgstr "H&öger"

#. __ Options - Window:
#: config.c:4448 config.c:4466
msgid "Modifier for scrolling"
msgstr "Modifierare för rullning"

#. __ Options - Window:
#: config.c:4495
msgid "&PgUp and PgDn scroll without modifier"
msgstr "Rulla med &PgUp och PgDn utan modifierare"

#. __ Options - Window: section title
#: config.c:4501
msgid "UI language"
msgstr "Gränssnittsspråk"

#. __ Options - Terminal: treeview label
#: config.c:4511 config.c:4524 config.c:4585 config.c:4599
msgid "Terminal"
msgstr "Terminal"

#. __ Options - Terminal: panel title
#: config.c:4513
msgid "Terminal features"
msgstr "Terminalfunktioner"

#. __ Options - Terminal:
#: config.c:4517
msgid "&Type"
msgstr "&Typ"

#. __ Options - Terminal: answerback string for ^E request
#: config.c:4521
msgid "&Answerback"
msgstr "Svarstr&äng"

#. __ Options - Terminal: section title
#: config.c:4526
msgid "Bell"
msgstr "Ljud"

#. __ Options - Terminal: bell
#: config.c:4533
msgid "► &Play"
msgstr "► &Spela upp"

#. __ Options - Terminal: bell
#: config.c:4539
msgid "&Wave"
msgstr ".&wav-fil"

#. __ Options - Terminal: bell
#: config.c:4561 config.c:4574
msgid "&Flash"
msgstr "&Blinka"

#. __ Options - Terminal: bell
#: config.c:4563 config.c:4578
msgid "&Highlight in taskbar"
msgstr "&Visa i aktivitetsfältet"

#. __ Options - Terminal: bell
#: config.c:4565 config.c:4582
msgid "&Popup"
msgstr "&Popupp"

#. __ Options - Terminal: section title
#: config.c:4587
msgid "Printer"
msgstr "Skrivare"

#. __ Options - Terminal:
#: config.c:4602
msgid "Prompt about running processes on &close"
msgstr "Fråga om pågående processer vid &avslut"

#. __ Options - Terminal:
#. __ Context menu:
#: config.c:4607 wininput.c:581
msgid "Status Line"
msgstr "Statusrad"

#: textprint.c:44 textprint.c:127
msgid "[Printing...] "
msgstr "[Skriver ut...] "

#. __ Options - Text: font chooser activation button
#: winctrls.c:935
msgid "&Select..."
msgstr "&Tecken..."

#. __ Font chooser: title bar label
#: winctrls.c:1281
msgid "Font "
msgstr "Teckensnitt "

#. __ Font chooser: button
#: winctrls.c:1283
msgid "&Apply"
msgstr "&Verkställ"

#. __ Font chooser:
#: winctrls.c:1285
msgid "&Font:"
msgstr "&Teckensnitt:"

#. __ Font chooser:
#: winctrls.c:1291
msgid "Sample"
msgstr "Exempel"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1295 winctrls.c:1554 winctrls.c:1719
msgid "Ferqœm’4€"
msgstr "Flygande bäckasiner söka hwila 0123456789"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1312
msgid "Sc&ript:"
msgstr "Sk&ript:"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1314
msgid "<A>Show more fonts</A>"
msgstr "<A>Visa fler teckensnitt</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1319
msgid "Colour "
msgstr "Redigera färger "

#. __ Colour chooser:
#: winctrls.c:1332 winctrls.c:1344
msgid "B&asic colours:"
msgstr "Gr&undfärger:"

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Custom colours:"
msgstr "Anpassade f&ärger:"

#. __ Colour chooser:
#: winctrls.c:1360
msgid "De&fine Custom Colours >>"
msgstr "&Definiera anpassade färger >>"

#. __ Colour chooser:
#: winctrls.c:1363
msgid "Colour"
msgstr "Färg"

#. __ Colour chooser:
#: winctrls.c:1365
msgid "|S&olid"
msgstr "|R&en färg"

#. __ Colour chooser:
#: winctrls.c:1367
msgid "&Hue:"
msgstr "&Nyans:"

#. __ Colour chooser:
#: winctrls.c:1370
msgid "&Sat:"
msgstr "&Mättnad:"

#. __ Colour chooser:
#: winctrls.c:1372
msgid "&Lum:"
msgstr "&Ljusstyrka:"

#. __ Colour chooser:
#: winctrls.c:1374
msgid "&Red:"
msgstr "&Röd:"

#. __ Colour chooser:
#: winctrls.c:1376
msgid "&Green:"
msgstr "Gr&ön:"

#. __ Colour chooser:
#: winctrls.c:1378
msgid "&Blue:"
msgstr "&Blå:"

#. __ Colour chooser:
#: winctrls.c:1381
msgid "A&dd to Custom Colours"
msgstr "Lägg &till anpassade färger"

#. __ Options: dialog title
#: windialog.c:266 windialog.c:839
msgid "Options"
msgstr "Alternativ"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:268
msgid "available"
msgstr "är tillgänglig"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:783
msgid "100"
msgstr "100"

#: windialog.c:924 windialog.c:951
msgid "Error"
msgstr "Fel"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:300
msgid "Session switcher"
msgstr "Sessionväxlare"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:320
msgid "Session launcher"
msgstr "Sessionstartare"

#: wininput.c:429 wininput.c:435
msgid "Ctrl+"
msgstr "Ctrl+"

#: wininput.c:430 wininput.c:436
msgid "Alt+"
msgstr "Alt+"

#: wininput.c:431 wininput.c:437
msgid "Shift+"
msgstr "Shift+"

#. __ System menu:
#: wininput.c:462
msgid "&Restore"
msgstr "&Återställ"

#. __ System menu:
#: wininput.c:464
msgid "&Move"
msgstr "&Flytta"

#. __ System menu:
#: wininput.c:466
msgid "&Size"
msgstr "&Ändra storlek"

#. __ System menu:
#: wininput.c:468
msgid "Mi&nimize"
msgstr "Mi&nimera"

#. __ System menu:
#: wininput.c:470
msgid "Ma&ximize"
msgstr "Ma&ximera"

#. __ System menu:
#: wininput.c:472
msgid "&Close"
msgstr "&Stäng"

#. __ System menu:
#: wininput.c:477
msgid "New &Window"
msgstr "Nytt &fönster"

#. __ System menu:
#: wininput.c:483
msgid "New &Tab"
msgstr "Ny flik"

#. __ Context menu:
#: wininput.c:490
msgid "&Copy"
msgstr "&Kopiera"

#. __ Context menu:
#: wininput.c:509
msgid "&Paste "
msgstr "K&listra in "

#. __ Context menu:
#: wininput.c:514
msgid "Copy → Paste"
msgstr "Kopiera → Klistra in"

#. __ Context menu:
#: wininput.c:519
msgid "S&earch"
msgstr "S&ök"

#. __ Context menu:
#: wininput.c:526
msgid "&Log to File"
msgstr "&Logga till fil"

#. __ Context menu:
#: wininput.c:532
msgid "Character &Info"
msgstr "Tecken&information"

#. __ Context menu:
#: wininput.c:538
msgid "VT220 Keyboard"
msgstr "VT220-tangentbord"

#. __ Context menu:
#: wininput.c:543
msgid "&Reset"
msgstr "&Rensa"

#. __ Context menu:
#: wininput.c:551
msgid "&Default Size"
msgstr "&Standardstorlek"

#. __ Context menu:
#: wininput.c:561
msgid "Scroll&bar"
msgstr "&Rullningslist"

#. __ Context menu:
#: wininput.c:567
msgid "&Full Screen"
msgstr "&Helskärm"

#. __ Context menu:
#: wininput.c:573
msgid "Flip &Screen"
msgstr "Vänd &skärm"

#. __ System menu:
#: wininput.c:591 wininput.c:763
msgid "Copy &Title"
msgstr "Kopiera &namnlisten"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:593 wininput.c:747 wininput.c:765
msgid "&Options..."
msgstr "&Alternativ..."

#. __ Context menu:
#: wininput.c:678
msgid "Ope&n"
msgstr "&Öppna"

#. __ Context menu:
#: wininput.c:683
msgid "Copy as text"
msgstr "Kopiera som text"

#. __ Context menu:
#: wininput.c:687
msgid "Copy as RTF"
msgstr "Kopiera som RTF"

#. __ Context menu:
#: wininput.c:689
msgid "Copy as HTML text"
msgstr "Kopiera som HTML-text"

#. __ Context menu:
#: wininput.c:691
msgid "Copy as HTML"
msgstr "Kopiera som HTML"

#. __ Context menu:
#: wininput.c:693
msgid "Copy as HTML full"
msgstr "Kopiera som fullständig HTML"

#. __ Context menu:
#: wininput.c:700
msgid "Select &All"
msgstr "Markera &allt"

#. __ Context menu:
#: wininput.c:702
msgid "Save as &Image"
msgstr "Spara som &bild"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:714
msgid "HTML Screen Dump"
msgstr "HTML-skärmdump"

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:722
msgid "Clear Scrollback"
msgstr "Rensa radbuffert"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:733
msgid "Send Break"
msgstr "Skriv Break"

#. __ Context menu, user commands
#: wininput.c:835
msgid "User commands"
msgstr "Användarkommandon"

#: wininput.c:1503 wininput.c:1524 wininput.c:1526 wininput.c:1528
#: wininput.c:1565
msgid "[NO SCROLL] "
msgstr "[INGEN RULLNING] "

#: wininput.c:1516 wininput.c:1525 wininput.c:1530 wininput.c:1586
msgid "[SCROLL MODE] "
msgstr "[RULLNINGSLÄGE] "

#: winmain.c:3845
msgid "Processes are running in session:"
msgstr "Processer som körs i sessionen:"

#: winmain.c:3846
msgid "Close anyway?"
msgstr "Vill du stänga fönstret ändå?"

#: winmain.c:3870
msgid "Reset terminal?"
msgstr "Rensa terminal?"

#: winmain.c:4100
msgid "Try '--help' for more information"
msgstr "Prova att skriva \"--help\" för mer information"

#: winmain.c:4108
msgid "Could not load icon"
msgstr "Kunde inte läsa in ikon"

#: winmain.c:6402
msgid "Usage:"
msgstr "Användning:"

#: winmain.c:6403
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[ALTERNATIV]... [ PROGRAM [ARGUMENT]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:6406
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"Startar ny terminalsession som kör angivet program el. användarens "
"skalprogram.\n"
"Ett inloggande skalprogram startas om bindestreck anges istället för "
"program.\n"
"\n"
"Alternativ:\n"
" -c, --config FIL      Läs in angiven konfig.fil (cf. -C eller -o filnamn)\n"
" -e, --exec ...        Behandla återstående argument som kommandon att köra\n"
" -h, --hold never|start|error|always  Lämna fönster öppet när kommando "
"avslutas\n"
" -p, --position X,Y    Öppna fönster på angivna koordinater\n"
" -p, --position center|left|right|top|bottom  Öppna fönster vid angiven "
"position\n"
" -p, --position @N     Öppna fönster på skärm nummer \"N\"\n"
" -s, --size KOL,RAD    Ändra skärmstorleken i antalet tecken (även KOLxRAD)\n"
" -s, --size maxbredd|maxhöjd  Ändra skärmens maximala storlek\n"
" -t, --title NAMN      Ändra namnlisten (standard: anropat kommando) (cf. -"
"T)\n"
" -w, --window normal|min|max|full|hide  Ange fönstrets ursprungstillstånd\n"
" -i, --icon FIL[,IX]   Läs in fönsterikon från fil, index är valfritt\n"
" -l, --log FIL|-       Logga utmatning till fil eller stdout\n"
"     --nobidi|--nortl  Inaktivera dubbelriktad text (stöd av höger-till-"
"vänster)\n"
" -o, --option OPT=VAL  Ändra/åsidosätt konfig.alternativ med angivet värde\n"
" -B, --Border frame|void  Använd tunn/ingen fönsterram\n"
" -R, --Report s|o      Rapportera fönsterposition (short/long) efter avslut\n"
"     --nopin           Förhindra att denna instans fästs i aktivitetsfältet\n"
" -D, --daemon          Startar en ny instans med Windows "
"kortkommandotangent\n"
" -H, --help            Visar hjälpinformation och avslutar\n"
" -V, --version         Visar versionsinformation och avslutar\n"
"Se manualsidan för ytterligare alternativ och konfigurationer av "
"kommandotolken.\n"

#: winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "WSL distribution '%s' not found"
msgstr "WSL-distributionen \"%s\" hittades inte"

#: winmain.c:6731
msgid "Duplicate option '%s'"
msgstr "Alternativet \"%s\" är en dubblett"

#: winmain.c:6739 winmain.c:6830
msgid "Unknown option '%s'"
msgstr "Alternativet \"%s\" är okänt"

#: winmain.c:6741
msgid "Option '%s' requires an argument"
msgstr "Alternativet \"%s\" kräver ett argument"

#: winmain.c:6768
msgid "Syntax error in position argument '%s'"
msgstr "Syntaxfel vid positionsargumentet \"%s\""

#: winmain.c:6779
msgid "Syntax error in size argument '%s'"
msgstr "Syntaxfel i storleksargumentet \"%s\""

#: winmain.c:6939
msgid "Syntax error in geometry argument '%s'"
msgstr "Syntaxfel i geometriargumentet \"%s\""

#: winmain.c:7038
msgid "Mintty could not detach from caller, starting anyway"
msgstr "Mintty kunde inte frigöras från anroparen, men startas ändå"

#: winmain.c:7387
msgid "Using default title due to invalid characters in program name"
msgstr "Standardnamnlisten används p.g.a. ogiltiga tecken i programmets namn"

#: winsearch.c:232
msgid "◀"
msgstr "◀"

#: winsearch.c:233
msgid "▶"
msgstr "▶"

#: winsearch.c:234
msgid "X"
msgstr "X"

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:165
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "Radavst.: %d, fetstil: %s, understreck: %s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:167
msgid "font"
msgstr "tecken"

#. __ Options - Text: font properties: value affected by option
#: wintext.c:169
msgid "manual"
msgstr "manuellt"

#: wintext.c:544
msgid "Font not found, using system substitute"
msgstr "Teckensnittet hittades inte, systemets ersättningsteckensnitt används"

#: wintext.c:559
msgid "Font has limited support for character ranges"
msgstr "Teckensnittet har begränsat stöd för teckenintervall"

#: wintext.c:702
msgid "Font installation corrupt, using system substitute"
msgstr ""
"Teckensnittsinstallationen är trasig, systemets ersättningsteckensnitt "
"används"

#: wintext.c:756
msgid "Font does not support system locale"
msgstr "Teckensnittet har inte stöd för systemspråket"

#: appinfo.h:64
msgid "There is no warranty, to the extent permitted by law."
msgstr "Det finns ingen garanti i den utsträckning som tillåts enligt lag."

#. __ %s: WEBSITE (URL)
#: appinfo.h:69
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"Rapportera buggar eller ge förslag på förbättringar via bugghanteraren på "
"minttys projektsida som finns på\n"
"%s.\n"
"Se även wikin där för mer tips, tack och erkännanden."
