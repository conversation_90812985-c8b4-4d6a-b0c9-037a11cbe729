# Mintty
# Copyright (C) 2019
# This file is distributed under the same license as the mintty package.
# <AUTHOR> <EMAIL>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2025-03-22 07:33+0100\n"
"PO-Revision-Date: 2019-01-29 16:00+0200\n"
"Last-Translator: JSJ <<EMAIL>>\n"
"Language-Team: JSJ\n"
"Language: Spanish\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: charset.c:228 charset.c:239 winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "(Default)"
msgstr "(Defecto)"

#: charset.c:250
msgid "(OEM codepage)"
msgstr "(Codificación OEM)"

#: charset.c:254
msgid "(ANSI codepage)"
msgstr "(Codificación ANSI)"

#: child.c:96
msgid "There are no available terminals"
msgstr "No hay terminales disponibles"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "Error: No se puede abrir fichero de log"

#: child.c:334
msgid "Error: Could not fork child process"
msgstr "Error: No se puede lanzar proceso hijo"

#: child.c:336
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr ""
"Se requiere rebasing de la DLL; más información 'rebaseall / rebase --help'"

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:426
msgid "Failed to run '%s': %s"
msgstr "Fallo al ejecutar '%s': %s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:578
msgid "%s: Exit %i"
msgstr "%s: Código de salida %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:585
msgid "TERMINATED"
msgstr "FINALIZÓ"

#: child.c:1232
msgid "Error: Could not fork child daemon"
msgstr "Error: No se pudo lanzar demonio hijo"

#. __ Setting false for Boolean options (localization optional)
#: config.c:655
msgid "no"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:657
msgid "yes"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:659
msgid "false"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:661
msgid "true"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:663
msgid "off"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:665
msgid "on"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:694
msgid "stretch"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:696
msgid "align"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:698
msgid "middle"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:700
msgid "full"
msgstr ""

#. __ %s: unknown option name
#: config.c:866
msgid "Ignoring unknown option '%s'"
msgstr "Ignorando opción desconocida '%s'"

#: config.c:914 config.c:943
msgid "Internal error: too many options"
msgstr "Error interno: demasiadas opciones"

#: config.c:930
msgid "Internal error: too many options/comments"
msgstr "Error interno: demasiadas opciones/comentarios"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1100
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "Ignorando valor no válido '%s' de la opción '%s'"

#. __ %s: option name
#: config.c:1112
msgid "Ignoring option '%s' with missing value"
msgstr "Ignorando opción '%s' sin valor"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1791
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"No se pudieron guardar las opciones en '%s':\n"
"%s."

#: config.c:2172
msgid "◇ None (printing disabled) ◇"
msgstr "◇ No (impresión desactivada) ◇"

#: config.c:2174
msgid "◆ Default printer ◆"
msgstr "◆ Impresora predeterminada ◆"

#. __ UI localization disabled
#: config.c:2283
msgid "– None –"
msgstr "– Ninguno –"

#. __ UI localization: use Windows desktop setting
#: config.c:2285
msgid "@ Windows language @"
msgstr "@ Idioma de Windows @"

#. __ UI localization: use environment variable setting (LANGUAGE, LC_*)
#: config.c:2287
msgid "* Locale environm. *"
msgstr "* Entorno local *"

#. __ UI localization: use mintty configuration setting (Text - Locale)
#: config.c:2289
msgid "= cfg. Text Locale ="
msgstr "= cfg. Texto Local ="

#: config.c:2394
msgid "simple beep"
msgstr "Beep simple"

#: config.c:2395
msgid "no beep"
msgstr "Sin sonido"

#: config.c:2396
msgid "Default Beep"
msgstr "Beep predeterminado"

#: config.c:2397
msgid "Critical Stop"
msgstr "Fallo Crítico"

#: config.c:2398
msgid "Question"
msgstr "Pregunta"

#: config.c:2399
msgid "Exclamation"
msgstr "Aviso"

#: config.c:2400
msgid "Asterisk"
msgstr "Importante"

#: config.c:2443
msgid "◇ None (system sound) ◇"
msgstr "◇ Ninguno (sonido del sistema) ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2874 config.c:3425
msgid "◇ None ◇"
msgstr "◇ Ninguno ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2877
msgid "downloaded / give me a name!"
msgstr "descargado / ¡renombra el tema!"

#: config.c:2983
msgid "Could not load web theme"
msgstr "No se pudo cargar el tema web"

#: config.c:3040
msgid "Cannot write theme file"
msgstr "No se ha podido escribir en el fichero del tema"

#: config.c:3045
msgid "Cannot store theme file"
msgstr "No se ha podido guardar el tema"

#. __ Options - Text:
#: config.c:3502 config.c:3840 config.c:3939
msgid "as font"
msgstr "tipo letra"

#. __ Options - Text:
#: config.c:3503 config.c:3845 config.c:3944
msgid "as colour"
msgstr "color"

#: config.c:3504
msgid "as font & as colour"
msgstr "tipo letra & color"

#. __ Options - Text:
#: config.c:3505 config.c:3850 config.c:3949
msgid "xterm"
msgstr ""

#. __ Dialog button - show About text
#: config.c:3653
msgid "About..."
msgstr "Acerca de..."

#. __ Dialog button - save changes
#: config.c:3656
msgid "Save"
msgstr "Guardar"

#. __ Dialog button - cancel
#: config.c:3660 winctrls.c:1277 windialog.c:895
msgid "Cancel"
msgstr "Cancelar"

#. __ Dialog button - apply changes
#: config.c:3664
msgid "Apply"
msgstr "Aplicar"

#. __ Dialog button - take notice
#: config.c:3668 windialog.c:892
msgid "I see"
msgstr "Aceptar"

#. __ Dialog button - confirm action
#: config.c:3670 winctrls.c:1276 windialog.c:894
msgid "OK"
msgstr "Aceptar"

#. __ Options - Looks: treeview label
#: config.c:3677 config.c:3708 config.c:3767
msgid "Looks"
msgstr "Visualización"

#. __ Options - Looks: panel title
#: config.c:3679
msgid "Looks in Terminal"
msgstr "Visualización de la consola"

#. __ Options - Looks: section title
#: config.c:3681
msgid "Colours"
msgstr "Colores"

#. __ Options - Looks:
#: config.c:3685
msgid "&Foreground..."
msgstr "&Tinta..."

#. __ Options - Looks:
#: config.c:3689
msgid "&Background..."
msgstr "&Fondo..."

#. __ Options - Looks:
#: config.c:3693
msgid "&Cursor..."
msgstr "&Cursor..."

#. __ Options - Looks:
#: config.c:3697
msgid "&Theme"
msgstr "&Tema"

#. __ Options - Looks: name of web service
#: config.c:3702
msgid "Color Scheme Designer"
msgstr "Diseñador de Temas"

#. __ Options - Looks: store colour scheme
#: config.c:3705 winctrls.c:484
msgid "Store"
msgstr "Guardar"

#. __ Options - Looks: section title
#: config.c:3710
msgid "Transparency"
msgstr "Transparencia"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3716 config.c:4098 config.c:4261 config.c:4459
msgid "&Off"
msgstr "&No"

#. __ Options - Looks: transparency
#: config.c:3718
msgid "&Low"
msgstr "&Baja"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3720
msgid "&Med."
msgstr "&Med."

#. __ Options - Looks: transparency
#: config.c:3722
msgid "&Medium"
msgstr "&Media"

#. __ Options - Looks: transparency
#: config.c:3724
msgid "&High"
msgstr "&Alta"

#. __ Options - Looks: transparency
#: config.c:3726
msgid "Gla&ss"
msgstr "&Cristal"

#. __ Options - Looks: transparency
#: config.c:3733 config.c:3745 config.c:3752
msgid "Opa&que when focused"
msgstr "&Opaca si seleccionada"

#. __ Options - Looks: transparency
#: config.c:3738
msgid "Blu&r"
msgstr "&Desenfoque"

#: config.c:3759
msgid "◄"
msgstr ""

#: config.c:3762
msgid "►"
msgstr ""

#. __ Options - Looks: section title
#: config.c:3769
msgid "Cursor"
msgstr "Cursor"

#. __ Options - Looks: cursor type
#: config.c:3774
msgid "Li&ne"
msgstr "&Linea"

#. __ Options - Looks: cursor type
#: config.c:3776
msgid "Bloc&k"
msgstr "&Bloque"

#. __ Options - Looks: cursor type
#: config.c:3779
msgid "Bo&x"
msgstr ""

#. __ Options - Looks: cursor type
#: config.c:3782
msgid "&Underscore"
msgstr "S&ubrayado"

#. __ Options - Looks: cursor feature
#: config.c:3787
msgid "Blinkin&g"
msgstr "Parpa&deo"

#. __ Options - Text: treeview label
#: config.c:3794 config.c:3819 config.c:3834 config.c:3883 config.c:3933
#: config.c:3958 config.c:3980 config.c:3993 config.c:4001
msgid "Text"
msgstr "Texto"

#. __ Options - Text: panel title
#: config.c:3796
msgid "Text and Font properties"
msgstr "Propiedades del texto"

#. __ Options - Text: section title
#: config.c:3798
msgid "Font"
msgstr "Tipo letra"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3806 winctrls.c:1287
msgid "Font st&yle:"
msgstr "Es&tilo de letra:"

#. __ Font chooser:
#: config.c:3811 winctrls.c:1289
msgid "&Size:"
msgstr "&Tamaño:"

#. __ Options - Text:
#: config.c:3823 config.c:3902
msgid "Sho&w bold as font"
msgstr "&Negrita: tipo letra"

#. __ Options - Text:
#: config.c:3828 config.c:3907
msgid "Show &bold as colour"
msgstr "Ne&grita: color"

#. __ Options - Text:
#: config.c:3836 config.c:3857 config.c:3935 config.c:3962
msgid "Show bold"
msgstr "Negrita"

#. __ Options - Text:
#: config.c:3864 config.c:3912 config.c:3968
msgid "&Allow blinking"
msgstr "&Parpadeo"

#. __ Options - Text:
#: config.c:3869 config.c:3973
msgid "Show dim as font"
msgstr "Pálido: tipo letra"

#. __ Options - Text:
#: config.c:3887 config.c:3920 config.c:3955
msgid "Font smoothing"
msgstr "Suavizado"

#. __ Options - Text:
#: config.c:3890 config.c:3923 config.c:4149 config.c:4188 config.c:4342
#: config.c:4355
msgid "&Default"
msgstr "&Defecto"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3892 config.c:3925 config.c:4147 config.c:4186 config.c:4340
#: config.c:4353 config.c:4440
msgid "&None"
msgstr "&Ninguna"

#. __ Options - Text:
#: config.c:3894 config.c:3927 config.c:4148 config.c:4187 config.c:4341
#: config.c:4354
msgid "&Partial"
msgstr "&Parcial"

#. __ Options - Text:
#: config.c:3896 config.c:3929 config.c:4150 config.c:4189 config.c:4343
#: config.c:4356
msgid "&Full"
msgstr "&Completo"

#: config.c:3983
msgid "&Locale"
msgstr "&Idioma"

#: config.c:3986
msgid "&Character set"
msgstr "&Juego caracteres"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3997 config.c:4003
msgid "Emojis"
msgstr ""

#. __ Options - Text - Emojis:
#: config.c:4007
msgid "Style"
msgstr ""

#. __ Options - Text - Emojis:
#: config.c:4012
msgid "Placement"
msgstr ""

#. __ Options - Keys: treeview label
#: config.c:4020 config.c:4050 config.c:4085 config.c:4103
msgid "Keys"
msgstr "Teclas"

#. __ Options - Keys: panel title
#: config.c:4022
msgid "Keyboard features"
msgstr "Opciones de teclado"

#. __ Options - Keys:
#: config.c:4026
msgid "&Backarrow sends ^H"
msgstr "&Borrado envía ^H"

#. __ Options - Keys:
#: config.c:4031
msgid "&Delete sends DEL"
msgstr "&Supr envía DEL"

#. __ Options - Keys:
#: config.c:4036
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "Ctrl+Alt Izq es AltGr"

#. __ Options - Keys:
#: config.c:4041
msgid "AltGr is also Alt"
msgstr "AltGr es también Alt"

#. __ Options - Keys:
#: config.c:4046
msgid "&Esc/Enter reset IME to alphanumeric"
msgstr "&Esc/Enter resete IME"

#. __ Options - Keys: section title
#: config.c:4052
msgid "Shortcuts"
msgstr "Atajos"

#. __ Options - Keys:
#: config.c:4055
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "Co&piar y pegar (Ctrl/Shift+Ins)"

#. __ Options - Keys:
#: config.c:4060
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "&Menú y pantalla completa (Alt+Space/Enter)"

#. __ Options - Keys:
#: config.c:4065
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "&Cambiar de ventana (Ctrl+[Shift+]Tab)"

#. __ Options - Keys:
#: config.c:4070
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "&Ampliar (Ctrl+más/menos/cero)"

#. __ Options - Keys:
#: config.c:4075
msgid "&Alt+Fn shortcuts"
msgstr "Atajos &Alt+Fn"

#. __ Options - Keys:
#: config.c:4080
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "Atajos &Ctrl+Shift+letra"

#. __ Options - Keys: section title
#: config.c:4087 config.c:4105
msgid "Compose key"
msgstr "Tecla compos."

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:4092 config.c:4253 config.c:4272 config.c:4451 config.c:4470
msgid "&Shift"
msgstr "&Shift"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:4094 config.c:4255 config.c:4280 config.c:4453 config.c:4478
msgid "&Ctrl"
msgstr "&Ctrl"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4096 config.c:4257 config.c:4276 config.c:4455 config.c:4474
msgid "&Alt"
msgstr "&Alt"

#. __ Options - Mouse: treeview label
#: config.c:4112 config.c:4201 config.c:4233
msgid "Mouse"
msgstr "Ratón"

#. __ Options - Mouse: panel title
#: config.c:4114
msgid "Mouse functions"
msgstr "Funciones del ratón"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4122 config.c:4155 config.c:4171 config.c:4319
msgid "Cop&y on select"
msgstr "Co&piar al seleccionar"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4127 config.c:4160 config.c:4324 wininput.c:685
msgid "Copy with TABs"
msgstr "Copiar con &TABs"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4132 config.c:4165 config.c:4177 config.c:4329
msgid "Copy as &rich text"
msgstr "Copiar &formato texto"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4138 config.c:4145 config.c:4184 config.c:4338 config.c:4351
msgid "Copy as &HTML"
msgstr "Copiar formato &HTML"

#. __ Options - Mouse:
#: config.c:4197
msgid "Clic&ks place command line cursor"
msgstr "&Click coloca el cursor en línea"

#. __ Options - Mouse: section title
#: config.c:4203
msgid "Click actions"
msgstr "Acciones del click"

#. __ Options - Mouse:
#: config.c:4206
msgid "Right mouse button"
msgstr "Botón derecho"

#. __ Options - Mouse:
#: config.c:4209 config.c:4223
msgid "&Paste"
msgstr "&Pegar"

#. __ Options - Mouse:
#: config.c:4211 config.c:4225
msgid "E&xtend"
msgstr "E&xtend."

#. __ Options - Mouse:
#: config.c:4213
msgid "&Menu"
msgstr "&Menú"

#. __ Options - Mouse:
#: config.c:4215 config.c:4229
msgid "Ente&r"
msgstr "Ente&r"

#. __ Options - Mouse:
#: config.c:4220
msgid "Middle mouse button"
msgstr "Botón medio"

#. __ Options - Mouse:
#: config.c:4227
msgid "&Nothing"
msgstr "&Nada"

#. __ Options - Mouse: section title
#: config.c:4235
msgid "Application mouse mode"
msgstr "Modo aplicación"

#. __ Options - Mouse:
#: config.c:4238
msgid "Default click target"
msgstr "Objetivo del click"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4241
msgid "&Window"
msgstr "&Ventana"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4243
msgid "&Application"
msgstr "&Aplicación"

#. __ Options - Mouse:
#: config.c:4250 config.c:4268
msgid "Modifier for overriding default"
msgstr "Modificador ignorar opc. predeterminada"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4259 config.c:4284 config.c:4457 config.c:4482
msgid "&Win"
msgstr ""

#. __ Options - Modifier - Super:
#: config.c:4288 config.c:4486
msgid "&Sup"
msgstr ""

#. __ Options - Modifier - Hyper:
#: config.c:4292 config.c:4490
msgid "&Hyp"
msgstr ""

#. __ Options - Selection: treeview label
#: config.c:4302 config.c:4313 config.c:4377
msgid "Selection"
msgstr "Seleccion"

#. __ Options - Selection: panel title
#: config.c:4304
msgid "Selection and clipboard"
msgstr "Seleccion y clipboard"

#. __ Options - Selection:
#: config.c:4308
msgid "Clear selection on input"
msgstr ""

#. __ Options - Selection: section title
#: config.c:4315
msgid "Clipboard"
msgstr ""

#. __ Options - Selection:
#: config.c:4365
msgid "Trim space from selection"
msgstr ""

#. __ Options - Selection:
#: config.c:4371
msgid "Allow setting selection"
msgstr ""

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4379 config.c:4401 config.c:4426 config.c:4499
msgid "Window"
msgstr "Ventana"

#. __ Options - Selection: clock position of info popup for text size
#: config.c:4384
msgid "Show size while selecting (0..12)"
msgstr ""

#. __ Options - Selection:
#: config.c:4391
msgid "Suspend output while selecting"
msgstr ""

#. __ Options - Window: panel title
#: config.c:4403
msgid "Window properties"
msgstr "Propiedades de ventana"

#. __ Options - Window: section title
#: config.c:4405
msgid "Default size"
msgstr "Tamaño predeterminado"

#. __ Options - Window:
#: config.c:4409
msgid "Colu&mns"
msgstr "Colu&mnas"

#. __ Options - Window:
#: config.c:4413
msgid "Ro&ws"
msgstr "&Filas"

#. __ Options - Window:
#: config.c:4417
msgid "C&urrent size"
msgstr "&Tam. actual"

#. __ Options - Window:
#: config.c:4422
msgid "Re&wrap on resize"
msgstr "Ajustar al redimensionar"

#. __ Options - Window:
#: config.c:4430
msgid "Scroll&back lines"
msgstr "Buffer &scroll"

#. __ Options - Window:
#: config.c:4435
msgid "Scrollbar"
msgstr "Barra scroll"

#. __ Options - Window: scrollbar
#: config.c:4438
msgid "&Left"
msgstr "&Izquierda"

#. __ Options - Window: scrollbar
#: config.c:4442
msgid "&Right"
msgstr "&Derecha"

#. __ Options - Window:
#: config.c:4448 config.c:4466
msgid "Modifier for scrolling"
msgstr "Modificador para scroll"

#. __ Options - Window:
#: config.c:4495
msgid "&PgUp and PgDn scroll without modifier"
msgstr "Scroll con &AvPag y RePag"

#. __ Options - Window: section title
#: config.c:4501
msgid "UI language"
msgstr "Idioma UI"

#. __ Options - Terminal: treeview label
#: config.c:4511 config.c:4524 config.c:4585 config.c:4599
msgid "Terminal"
msgstr "Consola"

#. __ Options - Terminal: panel title
#: config.c:4513
msgid "Terminal features"
msgstr "Opciones de consola"

#. __ Options - Terminal:
#: config.c:4517
msgid "&Type"
msgstr "&Tipo"

#. __ Options - Terminal: answerback string for ^E request
#: config.c:4521
msgid "&Answerback"
msgstr "&Respuesta"

#. __ Options - Terminal: section title
#: config.c:4526
msgid "Bell"
msgstr "Sonido"

#. __ Options - Terminal: bell
#: config.c:4533
msgid "► &Play"
msgstr "► &Reprod."

#. __ Options - Terminal: bell
#: config.c:4539
msgid "&Wave"
msgstr "&WAV"

#. __ Options - Terminal: bell
#: config.c:4561 config.c:4574
msgid "&Flash"
msgstr "&Parpadeo"

#. __ Options - Terminal: bell
#: config.c:4563 config.c:4578
msgid "&Highlight in taskbar"
msgstr "Parpadeo &barra tareas"

#. __ Options - Terminal: bell
#: config.c:4565 config.c:4582
msgid "&Popup"
msgstr "&Popup"

#. __ Options - Terminal: section title
#: config.c:4587
msgid "Printer"
msgstr "Impresora"

#. __ Options - Terminal:
#: config.c:4602
msgid "Prompt about running processes on &close"
msgstr "Avisar sobre procesos en ejecución al &cerrar"

#. __ Options - Terminal:
#. __ Context menu:
#: config.c:4607 wininput.c:581
msgid "Status Line"
msgstr ""

#: textprint.c:44 textprint.c:127
msgid "[Printing...] "
msgstr "[Imprimiendo...] "

#. __ Options - Text: font chooser activation button
#: winctrls.c:935
msgid "&Select..."
msgstr "&Selecc..."

#. __ Font chooser: title bar label
#: winctrls.c:1281
msgid "Font "
msgstr "Tipo letra "

#. __ Font chooser: button
#: winctrls.c:1283
msgid "&Apply"
msgstr "&Aplicar"

#. __ Font chooser:
#: winctrls.c:1285
msgid "&Font:"
msgstr "&Tipo letra:"

#. __ Font chooser:
#: winctrls.c:1291
msgid "Sample"
msgstr "Ejemplo"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1295 winctrls.c:1554 winctrls.c:1719
msgid "Ferqœm’4€"
msgstr "AáBbCçNñ"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1312
msgid "Sc&ript:"
msgstr "S&cript:"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1314
msgid "<A>Show more fonts</A>"
msgstr "<A>Mostrar más tipos letra</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1319
msgid "Colour "
msgstr "Color "

#. __ Colour chooser:
#: winctrls.c:1332 winctrls.c:1344
msgid "B&asic colours:"
msgstr "Colores &básicos:"

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Custom colours:"
msgstr "Colores &personalizados:"

#. __ Colour chooser:
#: winctrls.c:1360
msgid "De&fine Custom Colours >>"
msgstr "Colores per&sonalizados >>"

#. __ Colour chooser:
#: winctrls.c:1363
msgid "Colour"
msgstr "Color"

#. __ Colour chooser:
#: winctrls.c:1365
msgid "|S&olid"
msgstr "|Sólid&o"

#. __ Colour chooser:
#: winctrls.c:1367
msgid "&Hue:"
msgstr "&Tint.:"

#. __ Colour chooser:
#: winctrls.c:1370
msgid "&Sat:"
msgstr "&Sat:"

#. __ Colour chooser:
#: winctrls.c:1372
msgid "&Lum:"
msgstr "&Lum:"

#. __ Colour chooser:
#: winctrls.c:1374
msgid "&Red:"
msgstr "&Rojo:"

#. __ Colour chooser:
#: winctrls.c:1376
msgid "&Green:"
msgstr "&Verde:"

#. __ Colour chooser:
#: winctrls.c:1378
msgid "&Blue:"
msgstr "&Azul:"

#. __ Colour chooser:
#: winctrls.c:1381
msgid "A&dd to Custom Colours"
msgstr "Aña&dir color personal."

#. __ Options: dialog title
#: windialog.c:266 windialog.c:839
msgid "Options"
msgstr "Opciones"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:268
msgid "available"
msgstr "disponible"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:783
msgid "100"
msgstr "100"

#: windialog.c:924 windialog.c:951
msgid "Error"
msgstr "Error"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:300
msgid "Session switcher"
msgstr "Cambiar sesión"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:320
msgid "Session launcher"
msgstr "Lanzar sesión"

#: wininput.c:429 wininput.c:435
msgid "Ctrl+"
msgstr "Ctrl+"

#: wininput.c:430 wininput.c:436
msgid "Alt+"
msgstr "Alt+"

#: wininput.c:431 wininput.c:437
msgid "Shift+"
msgstr "Shift+"

#. __ System menu:
#: wininput.c:462
msgid "&Restore"
msgstr "&Cargar"

#. __ System menu:
#: wininput.c:464
msgid "&Move"
msgstr "&Mover"

#. __ System menu:
#: wininput.c:466
msgid "&Size"
msgstr "&Tam."

#. __ System menu:
#: wininput.c:468
msgid "Mi&nimize"
msgstr "Mi&nimizar"

#. __ System menu:
#: wininput.c:470
msgid "Ma&ximize"
msgstr "Ma&ximizar"

#. __ System menu:
#: wininput.c:472
msgid "&Close"
msgstr "&Cerrar"

#. __ System menu:
#: wininput.c:477
msgid "New &Window"
msgstr "&Ventana nueva"

#. __ System menu:
#: wininput.c:483
msgid "New &Tab"
msgstr "&Pestaña nueva"

#. __ Context menu:
#: wininput.c:490
msgid "&Copy"
msgstr "&Copiar"

#. __ Context menu:
#: wininput.c:509
msgid "&Paste "
msgstr "&Pegar"

#. __ Context menu:
#: wininput.c:514
msgid "Copy → Paste"
msgstr "Copiar → Pegar"

#. __ Context menu:
#: wininput.c:519
msgid "S&earch"
msgstr "B&uscar"

#. __ Context menu:
#: wininput.c:526
msgid "&Log to File"
msgstr "&Log a fichero"

#. __ Context menu:
#: wininput.c:532
msgid "Character &Info"
msgstr "&Info caracter"

#. __ Context menu:
#: wininput.c:538
msgid "VT220 Keyboard"
msgstr "Teclado VT220"

#. __ Context menu:
#: wininput.c:543
msgid "&Reset"
msgstr "&Resetear"

#. __ Context menu:
#: wininput.c:551
msgid "&Default Size"
msgstr "&Tam. predeterminado"

#. __ Context menu:
#: wininput.c:561
msgid "Scroll&bar"
msgstr "Barra scroll"

#. __ Context menu:
#: wininput.c:567
msgid "&Full Screen"
msgstr "&Pantalla completa"

#. __ Context menu:
#: wininput.c:573
msgid "Flip &Screen"
msgstr "&Cambiar pantalla"

#. __ System menu:
#: wininput.c:591 wininput.c:763
msgid "Copy &Title"
msgstr "Copiar &título"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:593 wininput.c:747 wininput.c:765
msgid "&Options..."
msgstr "&Opciones..."

#. __ Context menu:
#: wininput.c:678
msgid "Ope&n"
msgstr "A&brir"

#. __ Context menu:
#: wininput.c:683
msgid "Copy as text"
msgstr "Copiar &formato texto"

#. __ Context menu:
#: wininput.c:687
msgid "Copy as RTF"
msgstr "Copiar formato RTF"

#. __ Context menu:
#: wininput.c:689
msgid "Copy as HTML text"
msgstr "Copiar formato HTML texto"

#. __ Context menu:
#: wininput.c:691
msgid "Copy as HTML"
msgstr "Copiar formato HTML"

#. __ Context menu:
#: wininput.c:693
msgid "Copy as HTML full"
msgstr "Copiar formato HTML completo"

#. __ Context menu:
#: wininput.c:700
msgid "Select &All"
msgstr "Selecc. &todo"

#. __ Context menu:
#: wininput.c:702
msgid "Save as &Image"
msgstr "Guardar formato &imagen"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:714
msgid "HTML Screen Dump"
msgstr ""

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:722
msgid "Clear Scrollback"
msgstr "Limpiar buffer scroll"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:733
msgid "Send Break"
msgstr "Enviar Break"

#. __ Context menu, user commands
#: wininput.c:835
msgid "User commands"
msgstr "Comandos especiales"

#: wininput.c:1503 wininput.c:1524 wininput.c:1526 wininput.c:1528
#: wininput.c:1565
msgid "[NO SCROLL] "
msgstr ""

#: wininput.c:1516 wininput.c:1525 wininput.c:1530 wininput.c:1586
msgid "[SCROLL MODE] "
msgstr "[MODO de SCROLL] "

#: winmain.c:3845
msgid "Processes are running in session:"
msgstr "Hay procesos ejecutándose:"

#: winmain.c:3846
msgid "Close anyway?"
msgstr "¿Cerrar?"

#: winmain.c:3870
msgid "Reset terminal?"
msgstr "¿Resetear consola?"

#: winmain.c:4100
msgid "Try '--help' for more information"
msgstr "Intenta usar '--help' para más información"

#: winmain.c:4108
msgid "Could not load icon"
msgstr "No se pudo cargar icono"

#: winmain.c:6402
msgid "Usage:"
msgstr "Uso:"

#: winmain.c:6403
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[OPCION]... [ PROGRAMA [ARG]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:6406
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"Inicia una nueva sesión de terminal ejecutando el programa especificado o la "
"línea de comandos del usuario.\n"
"Si se introduce un guión en vez de un programa, se lanza como línea de "
"comando de login.\n"
"\n"
"Opciones:\n"
"  -c, --config FICHERO  Carga el fichero de configuración (ver: -C, -o "
"ThemeFile)\n"
"  -e, --exec ...        Trata el resto de argumentos como el comando a "
"ejecutar\n"
"  -h, --hold never|start|error|always  Mantiene la ventana abierta al "
"finalizar el comando\n"
"  -p, --position X,Y    Abre la ventana en la posición dada\n"
"  -p, --position center|left|right|top|bottom  Abre la ventana en una "
"posición específica\n"
"  -p, --position @N     Abre la ventana en el monitor N\n"
"  -s, --size COLS,ROWS  Tamaño de la pantalla en caracteres (también "
"COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Maximiza la pantalla en la dimensión "
"especificada\n"
"  -t, --title TITLE     Cambia el título de la ventala (por defecto: el "
"comando ejecutado) (ver: -T)\n"
"  -w, --window normal|min|max|full|hide  Establece el estado inicial de la "
"ventana\n"
"  -i, --icon FILE[,IX]  Carga el icono de la ventana desde el fichero, se "
"puede usar índice\n"
"  -l, --log FILE|-      Manda la salida de log a fichero o a salida "
"estándar\n"
"      --nobidi|--nortl  Desactiva bidi (soporte izquierda-derecha)\n"
"  -o, --option OPT=VAL  Establece/cambia opciones del fichero de "
"configuración\n"
"  -B, --Border frame|void  Usar borde fino, o desactivarlo\n"
"  -R, --Report s|o      Imprime la posición de la ventana (formato corto/"
"lago) al salir\n"
"      --nopin           Hace que esta instancia no se pueda anclar a la "
"barra de tareas\n"
"  -D, --daemon          Inicia una nueva instancia con la tecla Windows\n"
"  -H, --help            Muestra la ayuda y sale\n"
"  -V, --version         Muestra la versión y sale\n"
"Consultar el manual para más opciones de línea de comandos y "
"configuraciones.\n"

#: winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "WSL distribution '%s' not found"
msgstr "No se ha encontrado la distribución WSL '%s'"

#: winmain.c:6731
msgid "Duplicate option '%s'"
msgstr "Opción duplicada '%s'"

#: winmain.c:6739 winmain.c:6830
msgid "Unknown option '%s'"
msgstr "Opción desconocida '%s'"

#: winmain.c:6741
msgid "Option '%s' requires an argument"
msgstr "Opción '%s' requiere un parámetro"

#: winmain.c:6768
msgid "Syntax error in position argument '%s'"
msgstr "Error de sintaxis en arg. posición '%s'"

#: winmain.c:6779
msgid "Syntax error in size argument '%s'"
msgstr "Error de sintaxis en arg. tamaño '%s'"

#: winmain.c:6939
msgid "Syntax error in geometry argument '%s'"
msgstr "Error de sintaxis en arg. tamaño y posición '%s'"

#: winmain.c:7038
msgid "Mintty could not detach from caller, starting anyway"
msgstr "Mintty no se pudo liberar, inciando de todas formas"

#: winmain.c:7387
msgid "Using default title due to invalid characters in program name"
msgstr ""
"Usando título predeterminado porque hay caracteres extraños en el nombre del "
"programa"

#: winsearch.c:232
msgid "◀"
msgstr ""

#: winsearch.c:233
msgid "▶"
msgstr ""

#: winsearch.c:234
msgid "X"
msgstr ""

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:165
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "Lead.: %d, Negrita: %s, Subray.: %s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:167
msgid "font"
msgstr "tipo letra"

#. __ Options - Text: font properties: value affected by option
#: wintext.c:169
msgid "manual"
msgstr "manual"

#: wintext.c:544
msgid "Font not found, using system substitute"
msgstr "Tipo de letra no encontrado, usando sustituto"

#: wintext.c:559
msgid "Font has limited support for character ranges"
msgstr "Tipo de letra tiene soporte limitado de caracteres"

#: wintext.c:702
msgid "Font installation corrupt, using system substitute"
msgstr "Tipo de letra corrupto, usando sustituto"

#: wintext.c:756
msgid "Font does not support system locale"
msgstr "Tipo de letra no soporta idioma del sistema"

#: appinfo.h:64
msgid "There is no warranty, to the extent permitted by law."
msgstr "No hay garantías, dentro de lo permitido por la ley."

#. __ %s: WEBSITE (URL)
#: appinfo.h:69
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"Por favor informe de los fallos o peticiones de mejora a través del sistema "
"de informe de errores en la página del proyecto mintty localizada en\n"
"%s.\n"
"Consultar también la Wiki para más ayuda, agradecimientos y créditos."
