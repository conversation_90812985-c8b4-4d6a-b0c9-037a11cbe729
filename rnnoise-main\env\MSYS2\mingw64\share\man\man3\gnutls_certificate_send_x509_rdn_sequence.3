.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_send_x509_rdn_sequence" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_send_x509_rdn_sequence \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_certificate_send_x509_rdn_sequence(gnutls_session_t " session ", int " status ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
a \fBgnutls_session_t\fP type.
.IP "int status" 12
is 0 or 1
.SH "DESCRIPTION"
If status is non zero, this function will order gnutls not to send
the rdnSequence in the certificate request message. That is the
server will not advertise its trusted CAs to the peer. If status
is zero then the default behaviour will take effect, which is to
advertise the server's trusted CAs.

This function has no effect in clients, and in authentication
methods other than certificate with X.509 certificates.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
