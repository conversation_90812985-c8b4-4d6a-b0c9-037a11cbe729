<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_CTX_get0_libctx</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_CTX_get0_libctx, EVP_PKEY_CTX_get0_propq, EVP_PKEY_CTX_get0_provider - functions for getting diverse information from an EVP_PKEY_CTX</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

OSSL_LIB_CTX *EVP_PKEY_CTX_get0_libctx(EVP_PKEY_CTX *ctx);
const char *EVP_PKEY_CTX_get0_propq(const EVP_PKEY_CTX *ctx);
const OSSL_PROVIDER *EVP_PKEY_CTX_get0_provider(const EVP_PKEY_CTX *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>EVP_PKEY_CTX_get0_libctx() and EVP_PKEY_CTX_get0_propq() obtain the OSSL_LIB_CTX and property query string values respectively that were associated with the EVP_PKEY_CTX when it was constructed.</p>

<p>EVP_PKEY_CTX_get0_provider() returns the provider associated with the ongoing <b>EVP_PKEY_CTX</b> operation. If the operation is performed by en <b>ENGINE</b>, this function returns NULL.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_PKEY_CTX_get0_libctx() and EVP_PKEY_CTX_get0_propq() functions return the OSSL_LIB_CTX and property query string associated with the EVP_PKEY_CTX or NULL if they are not set. The returned values should not be freed by the caller.</p>

<p>EVP_PKEY_CTX_get0_provider() returns a provider if an operation performed by a provider is ongoing, otherwise NULL.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>All functions were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


