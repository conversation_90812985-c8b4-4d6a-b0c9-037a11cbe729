<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Modules: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="reference.html" title="API Reference">
<link rel="prev" href="reference.html" title="API Reference">
<link rel="next" href="p11-kit-URIs.html" title="URIs">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts">
<a href="#" class="shortcut">Top</a><span id="nav_description">  <span class="dim">|</span> 
                  <a href="#p11-kit-Modules.description" class="shortcut">Description</a></span>
</td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="reference.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="reference.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="p11-kit-URIs.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="refentry">
<a name="p11-kit-Modules"></a><div class="titlepage"></div>
<div class="refnamediv"><table width="100%"><tr>
<td valign="top">
<h2><span class="refentrytitle"><a name="p11-kit-Modules.top_of_page"></a>Modules</span></h2>
<p>Modules — Module loading and initializing</p>
</td>
<td class="gallery_image" valign="top" align="right"></td>
</tr></table></div>
<div class="refsect1">
<a name="p11-kit-Modules.functions"></a><h2>Functions</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="functions_proto_type">
<col class="functions_proto_name">
</colgroup>
<tbody>
<tr>
<td class="function_type">
<span class="returnvalue">CK_FUNCTION_LIST</span> **
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-load-and-initialize" title="p11_kit_modules_load_and_initialize ()">p11_kit_modules_load_and_initialize</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-finalize-and-release" title="p11_kit_modules_finalize_and_release ()">p11_kit_modules_finalize_and_release</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_FUNCTION_LIST</span> **
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()">p11_kit_modules_load</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-initialize" title="p11_kit_modules_initialize ()">p11_kit_modules_initialize</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-finalize" title="p11_kit_modules_finalize ()">p11_kit_modules_finalize</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-release" title="p11_kit_modules_release ()">p11_kit_modules_release</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_FUNCTION_LIST</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-module-load" title="p11_kit_module_load ()">p11_kit_module_load</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-module-initialize" title="p11_kit_module_initialize ()">p11_kit_module_initialize</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-module-finalize" title="p11_kit_module_finalize ()">p11_kit_module_finalize</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">void</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-module-release" title="p11_kit_module_release ()">p11_kit_module_release</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_FUNCTION_LIST</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-module-for-name" title="p11_kit_module_for_name ()">p11_kit_module_for_name</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-module-get-name" title="p11_kit_module_get_name ()">p11_kit_module_get_name</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">int</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-module-get-flags" title="p11_kit_module_get_flags ()">p11_kit_module_get_flags</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-module-get-filename" title="p11_kit_module_get_filename ()">p11_kit_module_get_filename</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Modules.html#p11-kit-config-option" title="p11_kit_config_option ()">p11_kit_config_option</a> <span class="c_punctuation">()</span>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-Modules.other"></a><h2>Types and Values</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="other_proto_type">
<col class="other_proto_name">
</colgroup>
<tbody>
<tr>
<td class="define_keyword">#define</td>
<td class="function_name"><a class="link" href="p11-kit-Modules.html#P11-KIT-MODULE-CRITICAL:CAPS" title="P11_KIT_MODULE_CRITICAL">P11_KIT_MODULE_CRITICAL</a></td>
</tr>
<tr>
<td class="define_keyword">#define</td>
<td class="function_name"><a class="link" href="p11-kit-Modules.html#P11-KIT-MODULE-UNMANAGED:CAPS" title="P11_KIT_MODULE_UNMANAGED">P11_KIT_MODULE_UNMANAGED</a></td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-Modules.description"></a><h2>Description</h2>
<p>PKCS#11 modules are used by crypto libraries and applications to access
crypto objects (like keys and certificates) and to perform crypto operations.</p>
<p>In order for applications to behave consistently with regard to the user's
installed PKCS#11 modules, each module must be configured so that applications
or libraries know that they should load it.</p>
<p>When multiple consumers of a module (such as libraries or applications) are
in the same process, coordination of the initialization and finalization
of PKCS#11 modules is required. To do this modules are managed by p11-kit.
This means that various unsafe methods are coordinated between callers. Unmanaged
modules are simply the raw PKCS#11 module pointers without p11-kit getting in the
way. It is highly recommended that the default managed behavior is used.</p>
<p>The functions here provide support for initializing configured modules. The
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a> function should be used to load and initialize
the configured modules. When done, the <a class="link" href="p11-kit-Modules.html#p11-kit-modules-release" title="p11_kit_modules_release ()"><code class="function">p11_kit_modules_release()</code></a> function
should be used to release those modules and associated resources.</p>
<p>In addition <a class="link" href="p11-kit-Modules.html#p11-kit-config-option" title="p11_kit_config_option ()"><code class="function">p11_kit_config_option()</code></a> can be used to access other parts
of the module configuration.</p>
<p>If a consumer wishes to load an arbitrary PKCS#11 module that's not
configured use <a class="link" href="p11-kit-Modules.html#p11-kit-module-load" title="p11_kit_module_load ()"><code class="function">p11_kit_module_load()</code></a> to do so. And use <a class="link" href="p11-kit-Modules.html#p11-kit-module-release" title="p11_kit_module_release ()"><code class="function">p11_kit_module_release()</code></a>
to later release it.</p>
<p>Modules are represented by a pointer to their <code class="code">CK_FUNCTION_LIST</code>
entry points.</p>
</div>
<div class="refsect1">
<a name="p11-kit-Modules.functions_details"></a><h2>Functions</h2>
<div class="refsect2">
<a name="p11-kit-modules-load-and-initialize"></a><h3>p11_kit_modules_load_and_initialize ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_FUNCTION_LIST</span> **
p11_kit_modules_load_and_initialize (<em class="parameter"><code><span class="type">int</span> flags</code></em>);</pre>
<p>Load and initialize configured modules.</p>
<p>If a critical module fails to load or initialize then the function will
return <code class="literal">NULL</code>. Non-critical modules will be skipped
and not included in the returned module list.</p>
<p>Use <a class="link" href="p11-kit-Modules.html#p11-kit-modules-finalize-and-release" title="p11_kit_modules_finalize_and_release ()"><code class="function">p11_kit_modules_finalize_and_release()</code></a> when you're done with the
modules returned by this function.</p>
<p>The <em class="parameter"><code>flags</code></em>
 allowed by this function, as well as their meaning, are the
same as with <a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a>.</p>
<div class="refsect3">
<a name="p11-kit-modules-load-and-initialize.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>flags</p></td>
<td class="parameter_description"><p>flags to use to load the modules</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-modules-load-and-initialize.returns"></a><h4>Returns</h4>
<p> a <code class="literal">NULL</code> terminated list of modules, or
<code class="literal">NULL</code> on failure</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-modules-finalize-and-release"></a><h3>p11_kit_modules_finalize_and_release ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_modules_finalize_and_release (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> **modules</code></em>);</pre>
<p>Finalize and then release the a set of loaded PKCS#11 modules.</p>
<p>The modules may be either managed or unmanaged. The array containing
the module pointers is also freed by this function.</p>
<p>Modules are released even if their finalization returns an error code.
Managed modules will not be actually finalized or released until all
callers using them have done so.</p>
<p>For managed modules the <code class="literal">C_Finalize</code> function
is overridden so that multiple callers can finalize the same
modules. In addition for managed modules multiple callers can
finalize from different threads, and still guarantee consistent
thread-safe behavior.</p>
<p>For unmanaged modules if multiple callers try to finalize
a module, then one of the calls will return
<code class="literal">CKR_CRYPTOKI_NOT_INITIALIZED</code> according to the
PKCS#11 specification. In addition there are no guarantees that
thread-safe behavior will occur if multiple callers initialize from
different threads.</p>
<div class="refsect3">
<a name="p11-kit-modules-finalize-and-release.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>modules</p></td>
<td class="parameter_description"><p>the modules to release</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-modules-load"></a><h3>p11_kit_modules_load ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_FUNCTION_LIST</span> **
p11_kit_modules_load (<em class="parameter"><code>const <span class="type">char</span> *reserved</code></em>,
                      <em class="parameter"><code><span class="type">int</span> flags</code></em>);</pre>
<p>Load the configured PKCS#11 modules.</p>
<p>If <em class="parameter"><code>flags</code></em>
 contains the <a class="link" href="p11-kit-Modules.html#P11-KIT-MODULE-UNMANAGED:CAPS" title="P11_KIT_MODULE_UNMANAGED"><code class="literal">P11_KIT_MODULE_UNMANAGED</code></a> flag, then the
modules will be not be loaded in 'managed' mode regardless of its
configuration. This is not recommended for general usage.</p>
<p>If <em class="parameter"><code>flags</code></em>
 contains the <a class="link" href="p11-kit-Modules.html#P11-KIT-MODULE-CRITICAL:CAPS" title="P11_KIT_MODULE_CRITICAL"><code class="literal">P11_KIT_MODULE_CRITICAL</code></a> flag then the
modules will all be treated as 'critical', regardless of the module
configuration. This means that a failure to load any module will
cause this function to fail.</p>
<p>For unmanaged modules there is no guarantee to the state of the
modules. Other callers may be using the modules. Using unmanaged
modules haphazardly is not recommended for this reason. Some
modules (such as those configured with RPC) cannot be loaded in
unmanaged mode, and will be skipped.</p>
<p>If <em class="parameter"><code>flags</code></em>
 contains the <code class="literal">P11_KIT_MODULE_TRUSTED</code> flag then only the
marked as trusted modules will be loaded.</p>
<p>Use <a class="link" href="p11-kit-Modules.html#p11-kit-modules-release" title="p11_kit_modules_release ()"><code class="function">p11_kit_modules_release()</code></a> to release the modules returned by
this function.</p>
<p>If this function fails, then an error message will be available via the
<a class="link" href="p11-kit-Utilities.html#p11-kit-message" title="p11_kit_message ()"><code class="function">p11_kit_message()</code></a> function.</p>
<div class="refsect3">
<a name="p11-kit-modules-load.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>reserved</p></td>
<td class="parameter_description"><p>set to <code class="literal">NULL</code></p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>flags</p></td>
<td class="parameter_description"><p>flags to use to load the module</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-modules-load.returns"></a><h4>Returns</h4>
<p> a null terminated list of modules represented as PKCS#11
function lists, or <code class="literal">NULL</code> on failure</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-modules-initialize"></a><h3>p11_kit_modules_initialize ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_modules_initialize (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> **modules</code></em>,
                            <em class="parameter"><code><a class="link" href="p11-kit-Future.html#p11-kit-destroyer" title="p11_kit_destroyer ()"><span class="type">p11_kit_destroyer</span></a> failure_callback</code></em>);</pre>
<p>Initialize all the modules in the <em class="parameter"><code>modules</code></em>
 list by calling their
<code class="literal">C_Initialize</code> function.</p>
<p>For managed modules the <code class="literal">C_Initialize</code> function
is overridden so that multiple callers can initialize the same
modules. In addition for managed modules multiple callers can
initialize from different threads, and still guarantee consistent
thread-safe behavior.</p>
<p>For unmanaged modules if multiple callers try to initialize
a module, then one of the calls will return
<code class="literal">CKR_CRYPTOKI_ALREADY_INITIALIZED</code> according to the
PKCS#11 specification. In addition there are no guarantees that
thread-safe behavior will occur if multiple callers initialize from
different threads.</p>
<p>When a module fails to initialize it is removed from the <em class="parameter"><code>modules</code></em>
 list.
If the <em class="parameter"><code>failure_callback</code></em>
 is not <code class="literal">NULL</code> then it is called with the modules that
fail to initialize. For example, you may pass <a class="link" href="p11-kit-Modules.html#p11-kit-module-release" title="p11_kit_module_release ()"><code class="function">p11_kit_module_release()</code></a>
as a <em class="parameter"><code>failure_callback</code></em>
 if the <em class="parameter"><code>modules</code></em>
 list was loaded wit <a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a>.</p>
<p>The return value will return the failure code of the last critical
module that failed to initialize. Non-critical module failures do not affect
the return value. If no critical modules failed to initialize then the
return value will be <code class="literal">CKR_OK</code>.</p>
<p>When modules are removed, the list will be <code class="literal">NULL</code> terminated at the
appropriate place so it can continue to be used as a modules list.</p>
<p>This function does not accept a <code class="code">CK_C_INITIALIZE_ARGS</code> argument.
Custom initialization arguments cannot be supported when multiple consumers
load the same module.</p>
<div class="refsect3">
<a name="p11-kit-modules-initialize.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>modules</p></td>
<td class="parameter_description"><p>a <code class="literal">NULL</code> terminated list of modules</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>failure_callback</p></td>
<td class="parameter_description"><p>called with modules that fail to initialize</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-modules-initialize.returns"></a><h4>Returns</h4>
<p> <code class="literal">CKR_OK</code> or the failure code of the last critical
module that failed to initialize.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-modules-finalize"></a><h3>p11_kit_modules_finalize ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_modules_finalize (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> **modules</code></em>);</pre>
<p>Finalize each module in the <em class="parameter"><code>modules</code></em>
 list by calling its
<code class="literal">C_Finalize</code> function. Regardless of failures, all
<em class="parameter"><code>modules</code></em>
 will have their <code class="literal">C_Finalize</code> function called.</p>
<p>If a module returns a failure from its <code class="literal">C_Finalize</code>
method it will be returned. If multiple modules fail, the last failure
will be returned.</p>
<p>For managed modules the <code class="literal">C_Finalize</code> function
is overridden so that multiple callers can finalize the same
modules. In addition for managed modules multiple callers can
finalize from different threads, and still guarantee consistent
thread-safe behavior.</p>
<p>For unmanaged modules if multiple callers try to finalize
a module, then one of the calls will return
<code class="literal">CKR_CRYPTOKI_NOT_INITIALIZED</code> according to the
PKCS#11 specification. In addition there are no guarantees that
thread-safe behavior will occur if multiple callers finalize from
different threads.</p>
<div class="refsect3">
<a name="p11-kit-modules-finalize.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>modules</p></td>
<td class="parameter_description"><p>a <code class="literal">NULL</code> terminated list of modules</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-modules-finalize.returns"></a><h4>Returns</h4>
<p> <code class="literal">CKR_OK</code> or the failure code of the last
module that failed to finalize</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-modules-release"></a><h3>p11_kit_modules_release ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_modules_release (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> **modules</code></em>);</pre>
<p>Release the a set of loaded PKCS#11 modules.</p>
<p>The modules may be either managed or unmanaged. The array containing
the module pointers is also freed by this function.</p>
<p>Managed modules will not be actually released until all
callers using them have done so. If the modules were initialized, they
should have been finalized first.</p>
<div class="refsect3">
<a name="p11-kit-modules-release.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>modules</p></td>
<td class="parameter_description"><p>the modules to release</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-module-load"></a><h3>p11_kit_module_load ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_FUNCTION_LIST</span> *
p11_kit_module_load (<em class="parameter"><code>const <span class="type">char</span> *module_path</code></em>,
                     <em class="parameter"><code><span class="type">int</span> flags</code></em>);</pre>
<p>Load an arbitrary PKCS#11 module from a dynamic library file, and
initialize it. Normally using the <a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a> function
is preferred.</p>
<p>A full file path or just (path/)filename relative to
P11_MODULE_PATH are accepted.</p>
<p>Using this function to load modules allows coordination between multiple
callers of the same module in a single process. If <em class="parameter"><code>flags</code></em>
 contains the
<a class="link" href="p11-kit-Modules.html#P11-KIT-MODULE-UNMANAGED:CAPS" title="P11_KIT_MODULE_UNMANAGED"><code class="literal">P11_KIT_MODULE_UNMANAGED</code></a> flag, then the modules will be not be loaded
in 'managed' mode and not be coordinated. This is not recommended
for general usage.</p>
<p>Subsequent calls to this function for the same module will result in an
initialization count being incremented for the module. It is safe (although
usually unnecessary) to use this function on registered modules.</p>
<p>The module should be released with <a class="link" href="p11-kit-Modules.html#p11-kit-module-release" title="p11_kit_module_release ()"><code class="function">p11_kit_module_release()</code></a>.</p>
<p>If this function fails, then an error message will be available via the
<a class="link" href="p11-kit-Utilities.html#p11-kit-message" title="p11_kit_message ()"><code class="function">p11_kit_message()</code></a> function.</p>
<div class="refsect3">
<a name="p11-kit-module-load.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>module_path</p></td>
<td class="parameter_description"><p>relative or full file path of module library</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>flags</p></td>
<td class="parameter_description"><p>flags to use when loading the module</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-module-load.returns"></a><h4>Returns</h4>
<p> the loaded module PKCS#11 functions or <code class="literal">NULL</code> on failure</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-module-initialize"></a><h3>p11_kit_module_initialize ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_module_initialize (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *module</code></em>);</pre>
<p>Initialize a PKCS#11 module by calling its <code class="literal">C_Initialize</code>
function.</p>
<p>For managed modules the <code class="literal">C_Initialize</code> function
is overridden so that multiple callers can initialize the same
modules. In addition for managed modules multiple callers can
initialize from different threads, and still guarantee consistent
thread-safe behavior.</p>
<p>For unmanaged modules if multiple callers try to initialize
a module, then one of the calls will return
<code class="literal">CKR_CRYPTOKI_ALREADY_INITIALIZED</code> according to the
PKCS#11 specification. In addition there are no guarantees that
thread-safe behavior will occur if multiple callers initialize from
different threads.</p>
<p>This function does not accept a <code class="code">CK_C_INITIALIZE_ARGS</code> argument.
Custom initialization arguments cannot be supported when multiple consumers
load the same module.</p>
<div class="refsect3">
<a name="p11-kit-module-initialize.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>the module to initialize</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-module-initialize.returns"></a><h4>Returns</h4>
<p> <code class="literal">CKR_OK</code> or a failure code</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-module-finalize"></a><h3>p11_kit_module_finalize ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_module_finalize (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *module</code></em>);</pre>
<p>Finalize a PKCS#11 module by calling its <code class="literal">C_Finalize</code>
function.</p>
<p>For managed modules the <code class="literal">C_Finalize</code> function
is overridden so that multiple callers can finalize the same
modules. In addition for managed modules multiple callers can
finalize from different threads, and still guarantee consistent
thread-safe behavior.</p>
<p>For unmanaged modules if multiple callers try to finalize
a module, then one of the calls will return
<code class="literal">CKR_CRYPTOKI_NOT_INITIALIZED</code> according to the
PKCS#11 specification. In addition there are no guarantees that
thread-safe behavior will occur if multiple callers finalize from
different threads.</p>
<div class="refsect3">
<a name="p11-kit-module-finalize.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>the module to finalize</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-module-finalize.returns"></a><h4>Returns</h4>
<p> <code class="literal">CKR_OK</code> or a failure code</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-module-release"></a><h3>p11_kit_module_release ()</h3>
<pre class="programlisting"><span class="returnvalue">void</span>
p11_kit_module_release (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *module</code></em>);</pre>
<p>Release the a loaded PKCS#11 modules.</p>
<p>The module may be either managed or unmanaged. The <code class="literal">C_Finalize</code>
function will be called if no other callers are using this module.</p>
<div class="refsect3">
<a name="p11-kit-module-release.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>the module to release</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-module-for-name"></a><h3>p11_kit_module_for_name ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_FUNCTION_LIST</span> *
p11_kit_module_for_name (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> **modules</code></em>,
                         <em class="parameter"><code>const <span class="type">char</span> *name</code></em>);</pre>
<p>Look through the list of <em class="parameter"><code>modules</code></em>
 and return the module whose <em class="parameter"><code>name</code></em>

matches.</p>
<p>Only configured modules have names. Configured modules are loaded by
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a>. The module passed to this function can be either
managed or unmanaged.</p>
<p>The return value is not copied or duplicated in anyway. It is still
'owned' by the <em class="parameter"><code>modules</code></em>
 list.</p>
<div class="refsect3">
<a name="p11-kit-module-for-name.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>modules</p></td>
<td class="parameter_description"><p>a list of modules to look through</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>name</p></td>
<td class="parameter_description"><p>the name of the module to find</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-module-for-name.returns"></a><h4>Returns</h4>
<p> the module which matches the name, or <code class="literal">NULL</code> if no match.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-module-get-name"></a><h3>p11_kit_module_get_name ()</h3>
<pre class="programlisting"><span class="returnvalue">char</span> *
p11_kit_module_get_name (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *module</code></em>);</pre>
<p>Get the configured name of the PKCS#11 module.</p>
<p>Configured modules are loaded by <a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a>. The module
passed to this function can be either managed or unmanaged. Non
configured modules will return <code class="literal">NULL</code>.</p>
<p>Use <code class="function">free()</code> to release the return value when you're done with it.</p>
<div class="refsect3">
<a name="p11-kit-module-get-name.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>pointer to a loaded module</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-module-get-name.returns"></a><h4>Returns</h4>
<p> a newly allocated string containing the module name, or</p>
<code class="code">NULL</code> if the module is not a configured module
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-module-get-flags"></a><h3>p11_kit_module_get_flags ()</h3>
<pre class="programlisting"><span class="returnvalue">int</span>
p11_kit_module_get_flags (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *module</code></em>);</pre>
<p>Get the flags for this module.</p>
<p>The <a class="link" href="p11-kit-Modules.html#P11-KIT-MODULE-UNMANAGED:CAPS" title="P11_KIT_MODULE_UNMANAGED"><code class="literal">P11_KIT_MODULE_UNMANAGED</code></a> flag will be set if the module is not
managed by p11-kit. It is a raw PKCS#11 module function list.</p>
<p>The <a class="link" href="p11-kit-Modules.html#P11-KIT-MODULE-CRITICAL:CAPS" title="P11_KIT_MODULE_CRITICAL"><code class="literal">P11_KIT_MODULE_CRITICAL</code></a> flag will be set if the module is configured
to be critical, and not be skipped over if it fails to initialize or
load. This flag is also set for modules that are not configured, but have
been loaded in another fashion.</p>
<div class="refsect3">
<a name="p11-kit-module-get-flags.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>the module</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-module-get-flags.returns"></a><h4>Returns</h4>
<p> the flags for the module</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-module-get-filename"></a><h3>p11_kit_module_get_filename ()</h3>
<pre class="programlisting"><span class="returnvalue">char</span> *
p11_kit_module_get_filename (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *module</code></em>);</pre>
<p>Get the configured name of the PKCS#11 module.</p>
<p>Configured modules are loaded by <a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a>. The module
passed to this function can be either managed or unmanaged. Non
configured modules will return <code class="literal">NULL</code>.</p>
<p>Use <code class="function">free()</code> to release the return value when you're done with it.</p>
<div class="refsect3">
<a name="p11-kit-module-get-filename.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>pointer to a loaded module</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-module-get-filename.returns"></a><h4>Returns</h4>
<p> a newly allocated string containing the module name, or</p>
<code class="code">NULL</code> if the module is not a configured module
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-config-option"></a><h3>p11_kit_config_option ()</h3>
<pre class="programlisting"><span class="returnvalue">char</span> *
p11_kit_config_option (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST</span> *module</code></em>,
                       <em class="parameter"><code>const <span class="type">char</span> *option</code></em>);</pre>
<p>Retrieve the value for a configured option.</p>
<p>If <em class="parameter"><code>module</code></em>
 is <code class="literal">NULL</code>, then the global option with the given name will
be retrieved. Otherwise <em class="parameter"><code>module</code></em>
 should point to a configured loaded module.
If no such <em class="parameter"><code>option</code></em>
 or configured <em class="parameter"><code>module</code></em>
 exists, then <code class="literal">NULL</code> will be returned.</p>
<p>Use <code class="function">free()</code> to release the returned value.</p>
<div class="refsect3">
<a name="p11-kit-config-option.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>the module to retrieve the option for, or <code class="literal">NULL</code> for global options</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>option</p></td>
<td class="parameter_description"><p>the option to retrieve</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-config-option.returns"></a><h4>Returns</h4>
<p> the option value or <code class="literal">NULL</code></p>
</div>
</div>
</div>
<div class="refsect1">
<a name="p11-kit-Modules.other_details"></a><h2>Types and Values</h2>
<div class="refsect2">
<a name="P11-KIT-MODULE-CRITICAL:CAPS"></a><h3>P11_KIT_MODULE_CRITICAL</h3>
<pre class="programlisting">#define P11_KIT_MODULE_CRITICAL 1
</pre>
<p>Flag to load a module in 'critical' mode. Failure to load a critical module
will prevent all other modules from loading. A failure when loading a
non-critical module skips that module.</p>
</div>
<hr>
<div class="refsect2">
<a name="P11-KIT-MODULE-UNMANAGED:CAPS"></a><h3>P11_KIT_MODULE_UNMANAGED</h3>
<pre class="programlisting">#define P11_KIT_MODULE_UNMANAGED 1
</pre>
<p>Module is loaded in non 'managed' mode. This is not recommended,
disables many features, and prevents coordination between multiple
callers of the same module.</p>
</div>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>