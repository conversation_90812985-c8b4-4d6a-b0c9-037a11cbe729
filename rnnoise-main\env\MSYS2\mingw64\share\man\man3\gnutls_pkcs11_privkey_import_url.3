.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_privkey_import_url" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_privkey_import_url \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_privkey_import_url(gnutls_pkcs11_privkey_t " pkey ", const char * " url ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_privkey_t pkey" 12
The private key
.IP "const char * url" 12
a PKCS 11 url identifying the key
.IP "unsigned int flags" 12
Or sequence of GNUTLS_PKCS11_OBJ_* flags
.SH "DESCRIPTION"
This function will "import" a PKCS 11 URL identifying a private
key to the \fBgnutls_pkcs11_privkey_t\fP type. In reality since
in most cases keys cannot be exported, the private key structure
is being associated with the available operations on the token.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
