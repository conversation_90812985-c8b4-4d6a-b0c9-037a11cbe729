.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_verify_seed" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_verify_seed \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_verify_seed(gnutls_privkey_t " key ", gnutls_digest_algorithm_t " digest ", const void * " seed ", size_t " seed_size ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
should contain a \fBgnutls_privkey_t\fP type
.IP "gnutls_digest_algorithm_t digest" 12
it contains the digest algorithm used for key generation (if applicable)
.IP "const void * seed" 12
the seed of the key to be checked with
.IP "size_t seed_size" 12
holds the size of  \fIseed\fP 
.SH "DESCRIPTION"
This function will verify that the given private key was generated from
the provided seed.
.SH "RETURNS"
In case of a verification failure \fBGNUTLS_E_PRIVKEY_VERIFICATION_ERROR\fP
is returned, and zero or positive code on success.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
