.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_add_cas" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_add_cas \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_add_cas(gnutls_x509_trust_list_t " list ", const gnutls_x509_crt_t * " clist ", unsigned " clist_size ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "const gnutls_x509_crt_t * clist" 12
A list of CAs
.IP "unsigned clist_size" 12
The length of the CA list
.IP "unsigned int flags" 12
flags from \fBgnutls_trust_list_flags_t\fP
.SH "DESCRIPTION"
This function will add the given certificate authorities
to the trusted list. The CAs in  \fIclist\fP must not be deinitialized
during the lifetime of  \fIlist\fP .

If the flag \fBGNUTLS_TL_NO_DUPLICATES\fP is specified, then
this function will ensure that no duplicates will be
present in the final trust list.

If the flag \fBGNUTLS_TL_NO_DUPLICATE_KEY\fP is specified, then
this function will ensure that no certificates with the
same key are present in the final trust list.

If either \fBGNUTLS_TL_NO_DUPLICATE_KEY\fP or \fBGNUTLS_TL_NO_DUPLICATES\fP
are given, \fBgnutls_x509_trust_list_deinit()\fP must be called with parameter
 \fIall\fP being 1.
.SH "RETURNS"
The number of added elements is returned; that includes
duplicate entries.
.SH "SINCE"
3.0.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
