<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_posix_path_list_p</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="cygwin-functions.html#func-cygwin-path" title="Path conversion functions"><link rel="prev" href="func-cygwin-create-path.html" title="cygwin_create_path"><link rel="next" href="func-cygwin-split-path.html" title="cygwin_split_path"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_posix_path_list_p</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="func-cygwin-create-path.html">Prev</a>&#160;</td><th width="60%" align="center">Path conversion functions</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-split-path.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-posix-path-list-p"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_posix_path_list_p</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">int
<b class="fsfunc">cygwin_posix_path_list_p</b>(</code>const char *<var class="pdparam">path</var><code>)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-posix-path-list-p-desc"></a><h2>Description</h2><p>This function tells you if the supplied
<em class="parameter"><code>path</code></em> is a POSIX-style path (i.e. posix names,
forward slashes, colon delimiters) or a Win32-style path (drive
letters, reverse slashes, semicolon delimiters.  The return value is
true if the path is a POSIX path.  Note that "_p" means "predicate", a
lisp term meaning that the function tells you something about the
parameter.</p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="func-cygwin-create-path.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="cygwin-functions.html#func-cygwin-path">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-split-path.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">cygwin_create_path&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;cygwin_split_path</td></tr></table></div></body></html>
