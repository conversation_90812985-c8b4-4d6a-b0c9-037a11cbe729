CMAKE_SYSTEM_IGNORE_PREFIX_PATH
-------------------------------

.. versionadded:: 3.23

.. |CMAKE_IGNORE_VAR| replace:: ``CMAKE_SYSTEM_IGNORE_PREFIX_PATH``
.. |CMAKE_IGNORE_NONPREFIX_VAR| replace:: :variable:`CMAKE_SYSTEM_IGNORE_PATH`
.. |CMAKE_IGNORE_NONSYSTEM_VAR| replace:: :variable:`CMAKE_IGNORE_PREFIX_PATH`

.. include:: IGNORE_SEARCH_PREFIX.txt
.. include:: IGNORE_SEARCH_LOCATIONS.txt
.. include:: IGNORE_SEARCH_SYSTEM.txt

See also the following variables:

- :variable:`CMAKE_SYSTEM_IGNORE_PATH`
- :variable:`CMAKE_SYSTEM_PREFIX_PATH`
- :variable:`CMAKE_SYSTEM_LIBRARY_PATH`
- :variable:`C<PERSON><PERSON>_SYSTEM_INCLUDE_PATH`
- :variable:`<PERSON><PERSON><PERSON>_SYSTEM_PROGRAM_PATH`
