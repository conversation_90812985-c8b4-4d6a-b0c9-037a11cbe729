set(CMAKE_ISPC_COMPILER "@CMAKE_ISPC_COMPILER@")
set(CMAKE_ISPC_COMPILER_ARG1 "@CMAKE_ISPC_COMPILER_ARG1@")
set(CMAKE_ISPC_COMPILER_ID "@CMAKE_ISPC_COMPILER_ID@")
set(CMAKE_ISPC_COMPILER_VERSION "@CMAKE_ISPC_COMPILER_VERSION@")
set(CMAKE_ISPC_COMPILER_VERSION_INTERNAL "@CMAKE_ISPC_COMPILER_VERSION_INTERNAL@")

set(CMAKE_ISPC_PLATFORM_ID "@CMAKE_ISPC_PLATFORM_ID@")
set(CMAKE_ISPC_SIMULATE_ID "@CMAKE_ISPC_SIMULATE_ID@")
set(CMAKE_ISPC_COMPILER_FRONTEND_VARIANT "@CMAKE_ISPC_COMPILER_FRONTEND_VARIANT@")
set(CMAKE_ISPC_SIMULATE_VERSION "@CMAKE_ISPC_SIMULATE_VERSION@")

set(CMAKE_AR "@CMAKE_AR@")
set(CMAKE_ISPC_COMPILER_AR "@CMAKE_ISPC_COMPILER_AR@")
set(CMAKE_RANLIB "@CMAKE_RANLIB@")
set(CMAKE_ISPC_COMPILER_RANLIB "@CMAKE_ISPC_COMPILER_RANLIB@")

set(CMAKE_ISPC_COMPILER_LOADED 1)
set(CMAKE_ISPC_COMPILER_WORKS @CMAKE_ISPC_COMPILER_WORKS@)
set(CMAKE_ISPC_ABI_COMPILED @CMAKE_ISPC_ABI_COMPILED@)

set(CMAKE_ISPC_COMPILER_ENV_VAR "ISPC")

set(CMAKE_ISPC_COMPILER_ID_RUN 1)
set(CMAKE_ISPC_SOURCE_FILE_EXTENSIONS ispc)
set(CMAKE_ISPC_IGNORE_EXTENSIONS o;O)

set(CMAKE_ISPC_LINKER_PREFERENCE 0)
set(CMAKE_ISPC_LINKER_PREFERENCE_PROPAGATES 0)

@CMAKE_ISPC_COMPILER_CUSTOM_CODE@
