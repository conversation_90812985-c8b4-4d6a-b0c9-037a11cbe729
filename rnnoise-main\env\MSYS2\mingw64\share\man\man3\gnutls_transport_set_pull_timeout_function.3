.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_transport_set_pull_timeout_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_transport_set_pull_timeout_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_transport_set_pull_timeout_function(gnutls_session_t " session ", gnutls_pull_timeout_func " func ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_pull_timeout_func func" 12
a callback function
.SH "DESCRIPTION"
This is the function where you set a function for gnutls to know
whether data are ready to be received. It should wait for data a
given time frame in milliseconds. The callback should return 0 on 
timeout, a positive number if data can be received, and \-1 on error.
You'll need to override this function if \fBselect()\fP is not suitable
for the provided transport calls.

As with \fBselect()\fP, if the timeout value is zero the callback should return
zero if no data are immediately available. The special value
\fBGNUTLS_INDEFINITE_TIMEOUT\fP indicates that the callback should wait indefinitely
for data.

 \fIgnutls_pull_timeout_func\fP is of the form,
int (*gnutls_pull_timeout_func)(gnutls_transport_ptr_t, unsigned int ms);

This callback is necessary when \fBgnutls_handshake_set_timeout()\fP or 
\fBgnutls_record_set_timeout()\fP are set, under TLS1.3 and for enforcing the DTLS
mode timeouts when in blocking mode.

For compatibility with future GnuTLS versions this callback must be set when
a custom pull function is registered. The callback will not be used when the
session is in TLS mode with non\-blocking sockets. That is, when \fBGNUTLS_NONBLOCK\fP
is specified for a TLS session in \fBgnutls_init()\fP.

The helper function \fBgnutls_system_recv_timeout()\fP is provided to
simplify writing callbacks. 
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
