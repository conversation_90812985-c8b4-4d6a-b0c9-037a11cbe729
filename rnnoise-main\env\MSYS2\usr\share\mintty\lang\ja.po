# Japanese translations for mintty package
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the mintty package.
# <PERSON> <<EMAIL>>, 2017.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2025-03-22 07:33+0100\n"
"PO-Revision-Date: 2017-02-27 18:11+0900\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: charset.c:228 charset.c:239 winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "(Default)"
msgstr "(既定)"

#: charset.c:250
msgid "(OEM codepage)"
msgstr "(OEMコードページ)"

#: charset.c:254
msgid "(ANSI codepage)"
msgstr "(ANSIコードページ)"

#: child.c:96
msgid "There are no available terminals"
msgstr "使用可能な端末がありません"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "エラー: ログファイルを開けません"

#: child.c:334
msgid "Error: Could not fork child process"
msgstr "エラー: 子プロセスをforkできません"

#: child.c:336
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr "DLLのリベースが必要です; 'rebaseall / rebase --help' を参照"

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:426
msgid "Failed to run '%s': %s"
msgstr "実行に失敗 '%s': %s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:578
msgid "%s: Exit %i"
msgstr "%s: 終了コード %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:585
msgid "TERMINATED"
msgstr "終了しました"

#: child.c:1232
msgid "Error: Could not fork child daemon"
msgstr "エラー: 子デーモンをforkできません"

#. __ Setting false for Boolean options (localization optional)
#: config.c:655
msgid "no"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:657
msgid "yes"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:659
msgid "false"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:661
msgid "true"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:663
msgid "off"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:665
msgid "on"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:694
msgid "stretch"
msgstr "伸張"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:696
msgid "align"
msgstr "整列"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:698
msgid "middle"
msgstr "中央"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:700
msgid "full"
msgstr "全体"

#. __ %s: unknown option name
#: config.c:866
msgid "Ignoring unknown option '%s'"
msgstr "不明なオプション '%s' を無視します"

#: config.c:914 config.c:943
msgid "Internal error: too many options"
msgstr "内部エラー: オプションが多すぎます"

#: config.c:930
msgid "Internal error: too many options/comments"
msgstr "内部エラー: オプション/コメントが多すぎます"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1100
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "オプション '%2$s' 用の不正な値 '%1$s' を無視します"

#. __ %s: option name
#: config.c:1112
msgid "Ignoring option '%s' with missing value"
msgstr "値のないオプション '%s' を無視します"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1791
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"オプションを '%s' に保存できません:\n"
"%s。"

#: config.c:2172
msgid "◇ None (printing disabled) ◇"
msgstr "◇ なし (印刷できません) ◇"

#: config.c:2174
msgid "◆ Default printer ◆"
msgstr "◆ 既定のプリンター ◆"

#. __ UI localization disabled
#: config.c:2283
msgid "– None –"
msgstr "– なし –"

#. __ UI localization: use Windows desktop setting
#: config.c:2285
msgid "@ Windows language @"
msgstr "@ Windowsの言語 @"

#. __ UI localization: use environment variable setting (LANGUAGE, LC_*)
#: config.c:2287
msgid "* Locale environm. *"
msgstr "* ロケール環境変数 *"

#. __ UI localization: use mintty configuration setting (Text - Locale)
#: config.c:2289
msgid "= cfg. Text Locale ="
msgstr "= テキストロケール設定 ="

#: config.c:2394
msgid "simple beep"
msgstr "単純なビープ"

#: config.c:2395
msgid "no beep"
msgstr "ビープなし"

#: config.c:2396
msgid "Default Beep"
msgstr "既定のビープ"

#: config.c:2397
msgid "Critical Stop"
msgstr "致命的停止"

#: config.c:2398
msgid "Question"
msgstr "クエスチョン"

#: config.c:2399
msgid "Exclamation"
msgstr "エクスクラメーション"

#: config.c:2400
msgid "Asterisk"
msgstr "アスタリスク"

#: config.c:2443
msgid "◇ None (system sound) ◇"
msgstr "◇ なし (システムのサウンド) ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2874 config.c:3425
msgid "◇ None ◇"
msgstr "◇ なし ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2877
msgid "downloaded / give me a name!"
msgstr "ダウンロード完了 / 名前を指定してください!"

#: config.c:2983
msgid "Could not load web theme"
msgstr "webテーマをロードできません"

#: config.c:3040
msgid "Cannot write theme file"
msgstr "テーマファイルを書き込めません"

#: config.c:3045
msgid "Cannot store theme file"
msgstr "テーマファイルを保存できません"

#. __ Options - Text:
#: config.c:3502 config.c:3840 config.c:3939
msgid "as font"
msgstr "フォントを使用"

#. __ Options - Text:
#: config.c:3503 config.c:3845 config.c:3944
msgid "as colour"
msgstr "カラー表示"

#: config.c:3504
msgid "as font & as colour"
msgstr "フォントを使用&カラー表示"

#. __ Options - Text:
#: config.c:3505 config.c:3850 config.c:3949
msgid "xterm"
msgstr ""

#. __ Dialog button - show About text
#: config.c:3653
msgid "About..."
msgstr "About..."

#. __ Dialog button - save changes
#: config.c:3656
msgid "Save"
msgstr "保存"

#. __ Dialog button - cancel
#: config.c:3660 winctrls.c:1277 windialog.c:895
msgid "Cancel"
msgstr "キャンセル"

#. __ Dialog button - apply changes
#: config.c:3664
msgid "Apply"
msgstr "適用"

#. __ Dialog button - take notice
#: config.c:3668 windialog.c:892
msgid "I see"
msgstr "了解"

#. __ Dialog button - confirm action
#: config.c:3670 winctrls.c:1276 windialog.c:894
msgid "OK"
msgstr "OK"

#. __ Options - Looks: treeview label
#: config.c:3677 config.c:3708 config.c:3767
msgid "Looks"
msgstr "外観"

#. __ Options - Looks: panel title
#: config.c:3679
msgid "Looks in Terminal"
msgstr "端末の外観"

#. __ Options - Looks: section title
#: config.c:3681
msgid "Colours"
msgstr "色"

#. __ Options - Looks:
#: config.c:3685
msgid "&Foreground..."
msgstr "前景色(&F)..."

#. __ Options - Looks:
#: config.c:3689
msgid "&Background..."
msgstr "背景色(&B)..."

#. __ Options - Looks:
#: config.c:3693
msgid "&Cursor..."
msgstr "カーソル(&C)..."

#. __ Options - Looks:
#: config.c:3697
msgid "&Theme"
msgstr "テーマ(&T)"

#. __ Options - Looks: name of web service
#: config.c:3702
msgid "Color Scheme Designer"
msgstr "カラースキームデザイナー"

#. __ Options - Looks: store colour scheme
#: config.c:3705 winctrls.c:484
msgid "Store"
msgstr "保存"

#. __ Options - Looks: section title
#: config.c:3710
msgid "Transparency"
msgstr "透明度"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3716 config.c:4098 config.c:4261 config.c:4459
msgid "&Off"
msgstr "オフ(&O)"

#. __ Options - Looks: transparency
#: config.c:3718
msgid "&Low"
msgstr "低(&L)"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3720
msgid "&Med."
msgstr "中(&M)"

#. __ Options - Looks: transparency
#: config.c:3722
msgid "&Medium"
msgstr "中間(&M)"

#. __ Options - Looks: transparency
#: config.c:3724
msgid "&High"
msgstr "高(&H)"

#. __ Options - Looks: transparency
#: config.c:3726
msgid "Gla&ss"
msgstr "Glass(&S)"

#. __ Options - Looks: transparency
#: config.c:3733 config.c:3745 config.c:3752
msgid "Opa&que when focused"
msgstr "フォーカス時は不透明(&Q)"

#. __ Options - Looks: transparency
#: config.c:3738
msgid "Blu&r"
msgstr "ぼかし(&R)"

#: config.c:3759
msgid "◄"
msgstr ""

#: config.c:3762
msgid "►"
msgstr ""

#. __ Options - Looks: section title
#: config.c:3769
msgid "Cursor"
msgstr "カーソル"

#. __ Options - Looks: cursor type
#: config.c:3774
msgid "Li&ne"
msgstr "線(&N)"

#. __ Options - Looks: cursor type
#: config.c:3776
msgid "Bloc&k"
msgstr "四角(&K)"

#. __ Options - Looks: cursor type
#: config.c:3779
msgid "Bo&x"
msgstr ""

#. __ Options - Looks: cursor type
#: config.c:3782
msgid "&Underscore"
msgstr "下線(&U)"

#. __ Options - Looks: cursor feature
#: config.c:3787
msgid "Blinkin&g"
msgstr "点滅(&G)"

#. __ Options - Text: treeview label
#: config.c:3794 config.c:3819 config.c:3834 config.c:3883 config.c:3933
#: config.c:3958 config.c:3980 config.c:3993 config.c:4001
msgid "Text"
msgstr "テキスト"

#. __ Options - Text: panel title
#: config.c:3796
msgid "Text and Font properties"
msgstr "テキストとフォントの設定"

#. __ Options - Text: section title
#: config.c:3798
msgid "Font"
msgstr "フォント"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3806 winctrls.c:1287
msgid "Font st&yle:"
msgstr "フォントスタイル(&Y):"

#. __ Font chooser:
#: config.c:3811 winctrls.c:1289
msgid "&Size:"
msgstr "サイズ(&S):"

#. __ Options - Text:
#: config.c:3823 config.c:3902
msgid "Sho&w bold as font"
msgstr "ボールドフォントを使用(&W)"

#. __ Options - Text:
#: config.c:3828 config.c:3907
msgid "Show &bold as colour"
msgstr "ボールドをカラー表示(&B)"

#. __ Options - Text:
#: config.c:3836 config.c:3857 config.c:3935 config.c:3962
msgid "Show bold"
msgstr "ボールド"

#. __ Options - Text:
#: config.c:3864 config.c:3912 config.c:3968
msgid "&Allow blinking"
msgstr "点滅を許可(&A)"

#. __ Options - Text:
#: config.c:3869 config.c:3973
msgid "Show dim as font"
msgstr "淡色をフォントで表示"

#. __ Options - Text:
#: config.c:3887 config.c:3920 config.c:3955
msgid "Font smoothing"
msgstr "フォントスムージング"

#. __ Options - Text:
#: config.c:3890 config.c:3923 config.c:4149 config.c:4188 config.c:4342
#: config.c:4355
msgid "&Default"
msgstr "既定(&D)"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3892 config.c:3925 config.c:4147 config.c:4186 config.c:4340
#: config.c:4353 config.c:4440
msgid "&None"
msgstr "なし(&N)"

#. __ Options - Text:
#: config.c:3894 config.c:3927 config.c:4148 config.c:4187 config.c:4341
#: config.c:4354
msgid "&Partial"
msgstr "部分的(&P)"

#. __ Options - Text:
#: config.c:3896 config.c:3929 config.c:4150 config.c:4189 config.c:4343
#: config.c:4356
msgid "&Full"
msgstr "完全(&F)"

#: config.c:3983
msgid "&Locale"
msgstr "ロケール(&L)"

#: config.c:3986
msgid "&Character set"
msgstr "文字セット(&C)"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3997 config.c:4003
msgid "Emojis"
msgstr "絵文字"

#. __ Options - Text - Emojis:
#: config.c:4007
msgid "Style"
msgstr "スタイル"

#. __ Options - Text - Emojis:
#: config.c:4012
msgid "Placement"
msgstr "配置"

#. __ Options - Keys: treeview label
#: config.c:4020 config.c:4050 config.c:4085 config.c:4103
msgid "Keys"
msgstr "キー"

#. __ Options - Keys: panel title
#: config.c:4022
msgid "Keyboard features"
msgstr "キーボード設定"

#. __ Options - Keys:
#: config.c:4026
msgid "&Backarrow sends ^H"
msgstr "BSは^Hを送信(&B)"

#. __ Options - Keys:
#: config.c:4031
msgid "&Delete sends DEL"
msgstr "DeleteはDELを送信(&D)"

#. __ Options - Keys:
#: config.c:4036
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "Ctrl+左AltはAltGr(&G)"

#. __ Options - Keys:
#: config.c:4041
msgid "AltGr is also Alt"
msgstr "AltGrをAltとしても扱う"

#. __ Options - Keys:
#: config.c:4046
msgid "&Esc/Enter reset IME to alphanumeric"
msgstr "&Esc/Enter で IME を半角入力に戻す"

#. __ Options - Keys: section title
#: config.c:4052
msgid "Shortcuts"
msgstr "ショートカット"

#. __ Options - Keys:
#: config.c:4055
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "コピー&&ペースト(&Y) (Ctrl/Shift+Ins)"

#. __ Options - Keys:
#: config.c:4060
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "メニューと全画面(&M) (Alt+Space/Enter)"

#. __ Options - Keys:
#: config.c:4065
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "ウィンドウの切り替え(&S) (Ctrl+[Shift+]Tab)"

#. __ Options - Keys:
#: config.c:4070
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "ズーム(&Z) (Ctrl+plus/minus/zero)"

#. __ Options - Keys:
#: config.c:4075
msgid "&Alt+Fn shortcuts"
msgstr "Alt+Fnショートカット(&A)"

#. __ Options - Keys:
#: config.c:4080
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "Ctrl+Shift+文字 ショートカット(&C)"

#. __ Options - Keys: section title
#: config.c:4087 config.c:4105
msgid "Compose key"
msgstr "組み合わせキー"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:4092 config.c:4253 config.c:4272 config.c:4451 config.c:4470
msgid "&Shift"
msgstr "Shift(&S)"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:4094 config.c:4255 config.c:4280 config.c:4453 config.c:4478
msgid "&Ctrl"
msgstr "Ctrl(&C)"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4096 config.c:4257 config.c:4276 config.c:4455 config.c:4474
msgid "&Alt"
msgstr "Alt(&A)"

#. __ Options - Mouse: treeview label
#: config.c:4112 config.c:4201 config.c:4233
msgid "Mouse"
msgstr "マウス"

#. __ Options - Mouse: panel title
#: config.c:4114
msgid "Mouse functions"
msgstr "マウス設定"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4122 config.c:4155 config.c:4171 config.c:4319
msgid "Cop&y on select"
msgstr "選択時にコピー(&Y)"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4127 config.c:4160 config.c:4324 wininput.c:685
msgid "Copy with TABs"
msgstr "タブ付きで(テキストとして)コピー"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4132 config.c:4165 config.c:4177 config.c:4329
msgid "Copy as &rich text"
msgstr "リッチテキストとしてコピー(&R)"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4138 config.c:4145 config.c:4184 config.c:4338 config.c:4351
msgid "Copy as &HTML"
msgstr "HTMLとしてコピー(&H)"

#. __ Options - Mouse:
#: config.c:4197
msgid "Clic&ks place command line cursor"
msgstr "クリックでコマンドラインカーソルを移動(&K)"

#. __ Options - Mouse: section title
#: config.c:4203
msgid "Click actions"
msgstr "クリック動作"

#. __ Options - Mouse:
#: config.c:4206
msgid "Right mouse button"
msgstr "右マウスボタン"

#. __ Options - Mouse:
#: config.c:4209 config.c:4223
msgid "&Paste"
msgstr "ペースト(&P)"

#. __ Options - Mouse:
#: config.c:4211 config.c:4225
msgid "E&xtend"
msgstr "拡張(&X)"

#. __ Options - Mouse:
#: config.c:4213
msgid "&Menu"
msgstr "メニュー(&M)"

#. __ Options - Mouse:
#: config.c:4215 config.c:4229
msgid "Ente&r"
msgstr "Enter(&R)"

#. __ Options - Mouse:
#: config.c:4220
msgid "Middle mouse button"
msgstr "中マウスボタン"

#. __ Options - Mouse:
#: config.c:4227
msgid "&Nothing"
msgstr "なし(&N)"

#. __ Options - Mouse: section title
#: config.c:4235
msgid "Application mouse mode"
msgstr "アプリケーションマウスモード"

#. __ Options - Mouse:
#: config.c:4238
msgid "Default click target"
msgstr "既定のクリック先"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4241
msgid "&Window"
msgstr "ウィンドウ(&W)"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4243
msgid "&Application"
msgstr "アプリケーション(&A)"

#. __ Options - Mouse:
#: config.c:4250 config.c:4268
msgid "Modifier for overriding default"
msgstr "既定を上書きするための修飾子"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4259 config.c:4284 config.c:4457 config.c:4482
msgid "&Win"
msgstr "Win(&W)"

#. __ Options - Modifier - Super:
#: config.c:4288 config.c:4486
msgid "&Sup"
msgstr "Sup(&S)"

#. __ Options - Modifier - Hyper:
#: config.c:4292 config.c:4490
msgid "&Hyp"
msgstr "Hyp(&H)"

#. __ Options - Selection: treeview label
#: config.c:4302 config.c:4313 config.c:4377
msgid "Selection"
msgstr "選択"

#. __ Options - Selection: panel title
#: config.c:4304
msgid "Selection and clipboard"
msgstr "選択とクリップボード"

#. __ Options - Selection:
#: config.c:4308
msgid "Clear selection on input"
msgstr "入力時に選択を解除する"

#. __ Options - Selection: section title
#: config.c:4315
msgid "Clipboard"
msgstr "クリップボード"

#. __ Options - Selection:
#: config.c:4365
msgid "Trim space from selection"
msgstr "選択から空白を取り除く"

#. __ Options - Selection:
#: config.c:4371
msgid "Allow setting selection"
msgstr "選択の設定を許可する"

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4379 config.c:4401 config.c:4426 config.c:4499
msgid "Window"
msgstr "ウィンドウ"

#. __ Options - Selection: clock position of info popup for text size
#: config.c:4384
msgid "Show size while selecting (0..12)"
msgstr "選択中にサイズを表示する (0..12)"

#. __ Options - Selection:
#: config.c:4391
msgid "Suspend output while selecting"
msgstr "選択中に一時停止する"

#. __ Options - Window: panel title
#: config.c:4403
msgid "Window properties"
msgstr "ウィンドウ設定"

#. __ Options - Window: section title
#: config.c:4405
msgid "Default size"
msgstr "既定のサイズ"

#. __ Options - Window:
#: config.c:4409
msgid "Colu&mns"
msgstr "桁(&M)"

#. __ Options - Window:
#: config.c:4413
msgid "Ro&ws"
msgstr "行(&W)"

#. __ Options - Window:
#: config.c:4417
msgid "C&urrent size"
msgstr "現在のサイズ(&U)"

#. __ Options - Window:
#: config.c:4422
msgid "Re&wrap on resize"
msgstr "サイズ変更時に改行し直す(&W)"

#. __ Options - Window:
#: config.c:4430
msgid "Scroll&back lines"
msgstr "スクロール行数(&B)"

#. __ Options - Window:
#: config.c:4435
msgid "Scrollbar"
msgstr "スクロールバー"

#. __ Options - Window: scrollbar
#: config.c:4438
msgid "&Left"
msgstr "左(&L)"

#. __ Options - Window: scrollbar
#: config.c:4442
msgid "&Right"
msgstr "右(&R)"

#. __ Options - Window:
#: config.c:4448 config.c:4466
msgid "Modifier for scrolling"
msgstr "スクロール用修飾子"

#. __ Options - Window:
#: config.c:4495
msgid "&PgUp and PgDn scroll without modifier"
msgstr "修飾子なしでPgUpとPgDnでスクロール(&P)"

#. __ Options - Window: section title
#: config.c:4501
msgid "UI language"
msgstr "UI言語"

#. __ Options - Terminal: treeview label
#: config.c:4511 config.c:4524 config.c:4585 config.c:4599
msgid "Terminal"
msgstr "端末"

#. __ Options - Terminal: panel title
#: config.c:4513
msgid "Terminal features"
msgstr "端末設定"

#. __ Options - Terminal:
#: config.c:4517
msgid "&Type"
msgstr "タイプ(&T)"

#. __ Options - Terminal: answerback string for ^E request
#: config.c:4521
msgid "&Answerback"
msgstr "応答(&A)"

#. __ Options - Terminal: section title
#: config.c:4526
msgid "Bell"
msgstr "ベル"

#. __ Options - Terminal: bell
#: config.c:4533
msgid "► &Play"
msgstr "► 再生(&P)"

#. __ Options - Terminal: bell
#: config.c:4539
msgid "&Wave"
msgstr "音声(&S)"

#. __ Options - Terminal: bell
#: config.c:4561 config.c:4574
msgid "&Flash"
msgstr "点滅(&F)"

#. __ Options - Terminal: bell
#: config.c:4563 config.c:4578
msgid "&Highlight in taskbar"
msgstr "タスクバーで強調表示(&H)"

#. __ Options - Terminal: bell
#: config.c:4565 config.c:4582
msgid "&Popup"
msgstr "ポップアップ(&P)"

#. __ Options - Terminal: section title
#: config.c:4587
msgid "Printer"
msgstr "プリンター"

#. __ Options - Terminal:
#: config.c:4602
msgid "Prompt about running processes on &close"
msgstr "クローズ時に動作中のプロセスがあると尋ねる(&C)"

#. __ Options - Terminal:
#. __ Context menu:
#: config.c:4607 wininput.c:581
msgid "Status Line"
msgstr "ステータスライン"

#: textprint.c:44 textprint.c:127
msgid "[Printing...] "
msgstr "[印刷中...] "

#. __ Options - Text: font chooser activation button
#: winctrls.c:935
msgid "&Select..."
msgstr "選択(&S)..."

#. __ Font chooser: title bar label
#: winctrls.c:1281
msgid "Font "
msgstr "フォント "

#. __ Font chooser: button
#: winctrls.c:1283
msgid "&Apply"
msgstr "適用(&A)"

#. __ Font chooser:
#: winctrls.c:1285
msgid "&Font:"
msgstr "フォント(&F):"

#. __ Font chooser:
#: winctrls.c:1291
msgid "Sample"
msgstr "サンプル"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1295 winctrls.c:1554 winctrls.c:1719
msgid "Ferqœm’4€"
msgstr "Aaあぁアァ亜宇"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1312
msgid "Sc&ript:"
msgstr "文字セット(&R):"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1314
msgid "<A>Show more fonts</A>"
msgstr "<A>他のフォントを表示</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1319
msgid "Colour "
msgstr "色"

#. __ Colour chooser:
#: winctrls.c:1332 winctrls.c:1344
msgid "B&asic colours:"
msgstr "基本色:"

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Custom colours:"
msgstr "作成した色:"

#. __ Colour chooser:
#: winctrls.c:1360
msgid "De&fine Custom Colours >>"
msgstr "色の作成 >>"

#. __ Colour chooser:
#: winctrls.c:1363
msgid "Colour"
msgstr "色"

#. __ Colour chooser:
#: winctrls.c:1365
msgid "|S&olid"
msgstr "| 純色"

#. __ Colour chooser:
#: winctrls.c:1367
msgid "&Hue:"
msgstr "色合い:"

#. __ Colour chooser:
#: winctrls.c:1370
msgid "&Sat:"
msgstr "鮮やかさ:"

#. __ Colour chooser:
#: winctrls.c:1372
msgid "&Lum:"
msgstr "明るさ:"

#. __ Colour chooser:
#: winctrls.c:1374
msgid "&Red:"
msgstr "赤:"

#. __ Colour chooser:
#: winctrls.c:1376
msgid "&Green:"
msgstr "緑:"

#. __ Colour chooser:
#: winctrls.c:1378
msgid "&Blue:"
msgstr "青:"

#. __ Colour chooser:
#: winctrls.c:1381
msgid "A&dd to Custom Colours"
msgstr "色の追加"

#. __ Options: dialog title
#: windialog.c:266 windialog.c:839
msgid "Options"
msgstr "オプション"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:268
msgid "available"
msgstr "が利用可能"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:783
msgid "100"
msgstr "126"

#: windialog.c:924 windialog.c:951
msgid "Error"
msgstr "エラー"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:300
msgid "Session switcher"
msgstr "セッション切り替え"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:320
msgid "Session launcher"
msgstr "セッション立ち上げ"

#: wininput.c:429 wininput.c:435
msgid "Ctrl+"
msgstr "Ctrl+"

#: wininput.c:430 wininput.c:436
msgid "Alt+"
msgstr "Alt+"

#: wininput.c:431 wininput.c:437
msgid "Shift+"
msgstr "Shift+"

#. __ System menu:
#: wininput.c:462
msgid "&Restore"
msgstr "元のサイズに戻す(&R)"

#. __ System menu:
#: wininput.c:464
msgid "&Move"
msgstr "移動(&M)"

#. __ System menu:
#: wininput.c:466
msgid "&Size"
msgstr "サイズ変更(&S)"

#. __ System menu:
#: wininput.c:468
msgid "Mi&nimize"
msgstr "最小化(&N)"

#. __ System menu:
#: wininput.c:470
msgid "Ma&ximize"
msgstr "最大化(&X)"

#. __ System menu:
#: wininput.c:472
msgid "&Close"
msgstr "閉じる(&C)"

#. __ System menu:
#: wininput.c:477
msgid "New &Window"
msgstr "新規ウィンドウ(&W)"

#. __ System menu:
#: wininput.c:483
msgid "New &Tab"
msgstr "新規タブ(&T)"

#. __ Context menu:
#: wininput.c:490
msgid "&Copy"
msgstr "コピー(&C)"

#. __ Context menu:
#: wininput.c:509
msgid "&Paste "
msgstr "ペースト(&P)"

#. __ Context menu:
#: wininput.c:514
msgid "Copy → Paste"
msgstr "コピー → ペースト"

#. __ Context menu:
#: wininput.c:519
msgid "S&earch"
msgstr "検索(&E)"

#. __ Context menu:
#: wininput.c:526
msgid "&Log to File"
msgstr "ログをファイルに出力(&L)"

#. __ Context menu:
#: wininput.c:532
msgid "Character &Info"
msgstr "文字情報(&I)"

#. __ Context menu:
#: wininput.c:538
msgid "VT220 Keyboard"
msgstr "VT220キーボード"

#. __ Context menu:
#: wininput.c:543
msgid "&Reset"
msgstr "リセット(&R)"

#. __ Context menu:
#: wininput.c:551
msgid "&Default Size"
msgstr "既定のサイズ(&D)"

#. __ Context menu:
#: wininput.c:561
msgid "Scroll&bar"
msgstr "スクロールバー(&B)"

#. __ Context menu:
#: wininput.c:567
msgid "&Full Screen"
msgstr "全画面(&F)"

#. __ Context menu:
#: wininput.c:573
msgid "Flip &Screen"
msgstr "スクリーン切り替え(&S)"

#. __ System menu:
#: wininput.c:591 wininput.c:763
msgid "Copy &Title"
msgstr "タイトルをコピー(&T)"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:593 wininput.c:747 wininput.c:765
msgid "&Options..."
msgstr "オプション(&O)..."

#. __ Context menu:
#: wininput.c:678
msgid "Ope&n"
msgstr "開く(&N)"

#. __ Context menu:
#: wininput.c:683
msgid "Copy as text"
msgstr "テキストとしてコピー"

#. __ Context menu:
#: wininput.c:687
msgid "Copy as RTF"
msgstr "RTFとしてコピー"

#. __ Context menu:
#: wininput.c:689
msgid "Copy as HTML text"
msgstr "HTMLテキストとしてコピー"

#. __ Context menu:
#: wininput.c:691
msgid "Copy as HTML"
msgstr "HTMLとしてコピー"

#. __ Context menu:
#: wininput.c:693
msgid "Copy as HTML full"
msgstr "完全なHTMLとしてコピー"

#. __ Context menu:
#: wininput.c:700
msgid "Select &All"
msgstr "すべてを選択(&A)"

#. __ Context menu:
#: wininput.c:702
msgid "Save as &Image"
msgstr "画像として保存(&I)"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:714
msgid "HTML Screen Dump"
msgstr "HTML画面ダンプ"

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:722
msgid "Clear Scrollback"
msgstr "スクロールバックをクリア"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:733
msgid "Send Break"
msgstr "Breakを送信"

#. __ Context menu, user commands
#: wininput.c:835
msgid "User commands"
msgstr "ユーザーコマンド"

#: wininput.c:1503 wininput.c:1524 wininput.c:1526 wininput.c:1528
#: wininput.c:1565
msgid "[NO SCROLL] "
msgstr "[スクロール停止] "

#: wininput.c:1516 wininput.c:1525 wininput.c:1530 wininput.c:1586
msgid "[SCROLL MODE] "
msgstr "[スクロールモード] "

#: winmain.c:3845
msgid "Processes are running in session:"
msgstr "セッションでプロセスが動作中です:"

#: winmain.c:3846
msgid "Close anyway?"
msgstr "構わず閉じますか？"

#: winmain.c:3870
msgid "Reset terminal?"
msgstr "端末をリセットしますか？"

#: winmain.c:4100
msgid "Try '--help' for more information"
msgstr "さらなる情報には '--help' を試してください"

#: winmain.c:4108
msgid "Could not load icon"
msgstr "アイコンをロードできません"

#: winmain.c:6402
msgid "Usage:"
msgstr "使用法:"

#: winmain.c:6403
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[オプション]... [ プログラム [引数]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:6406
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"指定されたプログラムまたはユーザーのシェルを実行する新しい端末セッションを開"
"始します。\n"
"プログラムの代わりに - が指定された場合は、シェルをログインシェルとして起動し"
"ます。\n"
"\n"
"オプション:\n"
"  -c, --config FILE     指定された設定ファイルをロードする\n"
"                        (参考: -C および -o ThemeFile)\n"
"  -e, --exec ...        残りの引数を実行するコマンドとして扱う\n"
"  -h, --hold never|start|error|always\n"
"                        コマンドの終了後もウィンドウを開いたまま保持する\n"
"  -p, --position X,Y    指定された座標でウィンドウを開く\n"
"  -p, --position center|left|right|top|bottom  ウィンドウを特別な位置で開く\n"
"  -p, --position @N     ウィンドウをモニター N 上で開く\n"
"  -s, --size COLS,ROWS  画面サイズを文字数で設定する (COLSxROWS も可)\n"
"  -s, --size maxwidth|maxheight  最大画面サイズを指定された大きさに設定する\n"
"  -t, --title TITLE     ウィンドウタイトルを設定する (既定: 実行されたコマン"
"ド)\n"
"                        (参考: -T)\n"
"  -w, --window normal|min|max|full|hide  初期ウィンドウ状態を設定する\n"
"  -i, --icon FILE[,IX]  ウィンドウアイコンをファイルからロードする\n"
"                        (インデックスはオプション)\n"
"  -l, --log FILE|-      ログをファイルまたは標準出力に出力する\n"
"      --nobidi|--nortl  bidi(右から左の対応)を無効化する\n"
"  -o, --option OPT=VAL  設定ファイルのオプションを指定された値に設定/上書きす"
"る\n"
"  -B, --Border frame|void  ウィンドウの枠を細く/無しにする\n"
"  -R, --Report s|o      終了後にウィンドウ位置 (短/長) を報告する\n"
"      --nopin           このインスタンスをタスクバーにピン止めできないように"
"する\n"
"  -D, --daemon          Windowsショートカットキーで新しいインスタンスを開始す"
"る\n"
"  -H, --help            ヘルプを表示して終了する\n"
"  -V, --version         バージョン情報を表示して終了する\n"
"さらなるコマンドラインオプションと設定についてはマニュアルページを参照してく"
"ださい。\n"

#: winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "WSL distribution '%s' not found"
msgstr "WSL ディストリビューション '%s' が見つかりません"

#: winmain.c:6731
msgid "Duplicate option '%s'"
msgstr "オプションが重複しています '%s'"

#: winmain.c:6739 winmain.c:6830
msgid "Unknown option '%s'"
msgstr "未知のオプションです '%s'"

#: winmain.c:6741
msgid "Option '%s' requires an argument"
msgstr "オプション '%s' は引数が必要です"

#: winmain.c:6768
msgid "Syntax error in position argument '%s'"
msgstr "position引数に文法エラーがあります '%s'"

#: winmain.c:6779
msgid "Syntax error in size argument '%s'"
msgstr "size引数に文法エラーがあります '%s'"

#: winmain.c:6939
msgid "Syntax error in geometry argument '%s'"
msgstr "geometry引数に文法エラーがあります '%s'"

#: winmain.c:7038
msgid "Mintty could not detach from caller, starting anyway"
msgstr "minttyは呼び出し元からデタッチできません、とにかく起動します"

#: winmain.c:7387
msgid "Using default title due to invalid characters in program name"
msgstr "プログラム名に不正な文字があるため既定のタイトルを使用します"

#: winsearch.c:232
msgid "◀"
msgstr "◀"

#: winsearch.c:233
msgid "▶"
msgstr "▶"

#: winsearch.c:234
msgid "X"
msgstr "X"

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:165
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "レディング: %d、ボールド: %s、下線: %s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:167
msgid "font"
msgstr "フォント"

#. __ Options - Text: font properties: value affected by option
#: wintext.c:169
msgid "manual"
msgstr "手動"

#: wintext.c:544
msgid "Font not found, using system substitute"
msgstr "フォントが見つかりません、システムの代替を利用します"

#: wintext.c:559
msgid "Font has limited support for character ranges"
msgstr "フォントは文字の範囲について限定的な対応しかありません"

#: wintext.c:702
msgid "Font installation corrupt, using system substitute"
msgstr "フォントのインストールが壊れています、システムの代替を利用します"

#: wintext.c:756
msgid "Font does not support system locale"
msgstr "フォントはシステムロケールに対応していません"

#: appinfo.h:64
msgid "There is no warranty, to the extent permitted by law."
msgstr "法で許可されている範囲において、いかなる保証もありません。"

#. __ %s: WEBSITE (URL)
#: appinfo.h:69
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"バグの報告や改良の要求は、以下のminttyプロジェクトページのissueトラッカーから"
"お願いします。\n"
"%s\n"
"さらなるヒント、謝辞やクレジットについては、サイトのWikiも参照してください。"
