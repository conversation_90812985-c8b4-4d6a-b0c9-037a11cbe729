/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#define FACILITY_SIP_STATUS_CODE 0xEF
#define FACILITY_RTC_INTERFACE 0xEE
#define FACILITY_PINT_STATUS_CODE 0xF0

#define STATUS_SEVERITY_SUCCESS 0x0
#define STATUS_SEVERITY_RTC_ERROR 0x2

#define RTC_E_SIP_CODECS_DO_NOT_MATCH ((HRESULT)0x80EE0000)
#define RTC_E_SIP_STREAM_PRESENT ((HRESULT)0x80EE0001)
#define RTC_E_SIP_STREAM_NOT_PRESENT ((HRESULT)0x80EE0002)
#define RTC_E_SIP_NO_STREAM ((HRESULT)0x80EE0003)
#define RTC_E_SIP_PARSE_FAILED ((HRESULT)0x80EE0004)
#define RTC_E_SIP_HEADER_NOT_PRESENT ((HRESULT)0x80EE0005)
#define RTC_E_SDP_NOT_PRESENT ((HRESULT)0x80EE0006)
#define RTC_E_SDP_PARSE_FAILED ((HRESULT)0x80EE0007)
#define RTC_E_SDP_UPDATE_FAILED ((HRESULT)0x80EE0008)
#define RTC_E_SDP_MULTICAST ((HRESULT)0x80EE0009)
#define RTC_E_SDP_CONNECTION_ADDR ((HRESULT)0x80EE000A)
#define RTC_E_SDP_NO_MEDIA ((HRESULT)0x80EE000B)
#define RTC_E_SIP_TIMEOUT ((HRESULT)0x80EE000C)
#define RTC_E_SDP_FAILED_TO_BUILD ((HRESULT)0x80EE000D)
#define RTC_E_SIP_INVITE_TRANSACTION_PENDING ((HRESULT)0x80EE000E)
#define RTC_E_SIP_AUTH_HEADER_SENT ((HRESULT)0x80EE000F)
#define RTC_E_SIP_AUTH_TYPE_NOT_SUPPORTED ((HRESULT)0x80EE0010)
#define RTC_E_SIP_AUTH_FAILED ((HRESULT)0x80EE0011)
#define RTC_E_INVALID_SIP_URL ((HRESULT)0x80EE0012)
#define RTC_E_DESTINATION_ADDRESS_LOCAL ((HRESULT)0x80EE0013)
#define RTC_E_INVALID_ADDRESS_LOCAL ((HRESULT)0x80EE0014)
#define RTC_E_DESTINATION_ADDRESS_MULTICAST ((HRESULT)0x80EE0015)
#define RTC_E_INVALID_PROXY_ADDRESS ((HRESULT)0x80EE0016)
#define RTC_E_SIP_TRANSPORT_NOT_SUPPORTED ((HRESULT)0x80EE0017)
#define RTC_E_SIP_NEED_MORE_DATA ((HRESULT)0x80EE0018)
#define RTC_E_SIP_CALL_DISCONNECTED ((HRESULT)0x80EE0019)
#define RTC_E_SIP_REQUEST_DESTINATION_ADDR_NOT_PRESENT ((HRESULT)0x80EE001A)
#define RTC_E_SIP_UDP_SIZE_EXCEEDED ((HRESULT)0x80EE001B)
#define RTC_E_SIP_SSL_TUNNEL_FAILED ((HRESULT)0x80EE001C)
#define RTC_E_SIP_SSL_NEGOTIATION_TIMEOUT ((HRESULT)0x80EE001D)
#define RTC_E_SIP_STACK_SHUTDOWN ((HRESULT)0x80EE001E)
#define RTC_E_MEDIA_CONTROLLER_STATE ((HRESULT)0x80EE001F)
#define RTC_E_MEDIA_NEED_TERMINAL ((HRESULT)0x80EE0020)
#define RTC_E_MEDIA_AUDIO_DEVICE_NOT_AVAILABLE ((HRESULT)0x80EE0021)
#define RTC_E_MEDIA_VIDEO_DEVICE_NOT_AVAILABLE ((HRESULT)0x80EE0022)
#define RTC_E_START_STREAM ((HRESULT)0x80EE0023)
#define RTC_E_MEDIA_AEC ((HRESULT)0x80EE0024)
#define RTC_E_CLIENT_NOT_INITIALIZED ((HRESULT)0x80EE0025)
#define RTC_E_CLIENT_ALREADY_INITIALIZED ((HRESULT)0x80EE0026)
#define RTC_E_CLIENT_ALREADY_SHUT_DOWN ((HRESULT)0x80EE0027)
#define RTC_E_PRESENCE_NOT_ENABLED ((HRESULT)0x80EE0028)
#define RTC_E_INVALID_SESSION_TYPE ((HRESULT)0x80EE0029)
#define RTC_E_INVALID_SESSION_STATE ((HRESULT)0x80EE002A)
#define RTC_E_NO_PROFILE ((HRESULT)0x80EE002B)
#define RTC_E_LOCAL_PHONE_NEEDED ((HRESULT)0x80EE002C)
#define RTC_E_NO_DEVICE ((HRESULT)0x80EE002D)
#define RTC_E_INVALID_PROFILE ((HRESULT)0x80EE002E)
#define RTC_E_PROFILE_NO_PROVISION ((HRESULT)0x80EE002F)
#define RTC_E_PROFILE_NO_KEY ((HRESULT)0x80EE0030)
#define RTC_E_PROFILE_NO_NAME ((HRESULT)0x80EE0031)
#define RTC_E_PROFILE_NO_USER ((HRESULT)0x80EE0032)
#define RTC_E_PROFILE_NO_USER_URI ((HRESULT)0x80EE0033)
#define RTC_E_PROFILE_NO_SERVER ((HRESULT)0x80EE0034)
#define RTC_E_PROFILE_NO_SERVER_ADDRESS ((HRESULT)0x80EE0035)
#define RTC_E_PROFILE_NO_SERVER_PROTOCOL ((HRESULT)0x80EE0036)
#define RTC_E_PROFILE_INVALID_SERVER_PROTOCOL ((HRESULT)0x80EE0037)
#define RTC_E_PROFILE_INVALID_SERVER_AUTHMETHOD ((HRESULT)0x80EE0038)
#define RTC_E_PROFILE_INVALID_SERVER_ROLE ((HRESULT)0x80EE0039)
#define RTC_E_PROFILE_MULTIPLE_REGISTRARS ((HRESULT)0x80EE003A)
#define RTC_E_PROFILE_INVALID_SESSION ((HRESULT)0x80EE003B)
#define RTC_E_PROFILE_INVALID_SESSION_PARTY ((HRESULT)0x80EE003C)
#define RTC_E_PROFILE_INVALID_SESSION_TYPE ((HRESULT)0x80EE003D)
#define RTC_E_OPERATION_WITH_TOO_MANY_PARTICIPANTS ((HRESULT)0x80EE003E)
#define RTC_E_BASIC_AUTH_SET_TLS ((HRESULT)0x80EE003F)
#define RTC_E_SIP_HIGH_SECURITY_SET_TLS ((HRESULT)0x80EE0040)
#define RTC_S_ROAMING_NOT_SUPPORTED ((HRESULT)0x00EE0041)
#define RTC_E_PROFILE_SERVER_UNAUTHORIZED ((HRESULT)0x80EE0042)
#define RTC_E_DUPLICATE_REALM ((HRESULT)0x80EE0043)
#define RTC_E_POLICY_NOT_ALLOW ((HRESULT)0x80EE0044)
#define RTC_E_PORT_MAPPING_UNAVAILABLE ((HRESULT)0x80EE0045)
#define RTC_E_PORT_MAPPING_FAILED ((HRESULT)0x80EE0046)
#define RTC_E_SECURITY_LEVEL_NOT_COMPATIBLE ((HRESULT)0x80EE0047)
#define RTC_E_SECURITY_LEVEL_NOT_DEFINED ((HRESULT)0x80EE0048)
#define RTC_E_SECURITY_LEVEL_NOT_SUPPORTED_BY_PARTICIPANT ((HRESULT)0x80EE0049)
#define RTC_E_DUPLICATE_BUDDY ((HRESULT)0x80EE004A)
#define RTC_E_DUPLICATE_WATCHER ((HRESULT)0x80EE004B)
#define RTC_E_MALFORMED_XML ((HRESULT)0x80EE004C)
#define RTC_E_ROAMING_OPERATION_INTERRUPTED ((HRESULT)0x80EE004D)
#define RTC_E_ROAMING_FAILED ((HRESULT)0x80EE004E)
#define RTC_E_INVALID_BUDDY_LIST ((HRESULT)0x80EE004F)
#define RTC_E_INVALID_ACL_LIST ((HRESULT)0x80EE0050)
#define RTC_E_NO_GROUP ((HRESULT)0x80EE0051)
#define RTC_E_DUPLICATE_GROUP ((HRESULT)0x80EE0052)
#define RTC_E_TOO_MANY_GROUPS ((HRESULT)0x80EE0053)
#define RTC_E_NO_BUDDY ((HRESULT)0x80EE0054)
#define RTC_E_NO_WATCHER ((HRESULT)0x80EE0055)
#define RTC_E_NO_REALM ((HRESULT)0x80EE0056)
#define RTC_E_NO_TRANSPORT ((HRESULT)0x80EE0057)
#define RTC_E_NOT_EXIST ((HRESULT)0x80EE0058)
#define RTC_E_INVALID_PREFERENCE_LIST ((HRESULT)0x80EE0059)
#define RTC_E_MAX_PENDING_OPERATIONS ((HRESULT)0x80EE005A)
#define RTC_E_TOO_MANY_RETRIES ((HRESULT)0x80EE005B)
#define RTC_E_INVALID_PORTRANGE ((HRESULT)0x80EE005C)
#define RTC_E_SIP_CALL_CONNECTION_NOT_ESTABLISHED ((HRESULT)0x80EE005D)
#define RTC_E_SIP_ADDITIONAL_PARTY_IN_TWO_PARTY_SESSION ((HRESULT)0x80EE005E)
#define RTC_E_SIP_PARTY_ALREADY_IN_SESSION ((HRESULT)0x80EE005F)
#define RTC_E_SIP_OTHER_PARTY_JOIN_IN_PROGRESS ((HRESULT)0x80EE0060)
#define RTC_E_INVALID_OBJECT_STATE ((HRESULT)0x80EE0061)
#define RTC_E_PRESENCE_ENABLED ((HRESULT)0x80EE0062)
#define RTC_E_ROAMING_ENABLED ((HRESULT)0x80EE0063)
#define RTC_E_SIP_TLS_INCOMPATIBLE_ENCRYPTION ((HRESULT)0x80EE0064)
#define RTC_E_SIP_INVALID_CERTIFICATE ((HRESULT)0x80EE0065)
#define RTC_E_SIP_DNS_FAIL ((HRESULT)0x80EE0066)
#define RTC_E_SIP_TCP_FAIL ((HRESULT)0x80EE0067)
#define RTC_E_TOO_SMALL_EXPIRES_VALUE ((HRESULT)0x80EE0068)
#define RTC_E_SIP_TLS_FAIL ((HRESULT)0x80EE0069)
#define RTC_E_NOT_PRESENCE_PROFILE ((HRESULT)0x80EE006A)
#define RTC_E_SIP_INVITEE_PARTY_TIMEOUT ((HRESULT)0x80EE006B)
#define RTC_E_SIP_AUTH_TIME_SKEW ((HRESULT)0x80EE006C)
#define RTC_E_INVALID_REGISTRATION_STATE ((HRESULT)0x80EE006D)
#define RTC_E_MEDIA_DISABLED ((HRESULT)0x80EE006E)
#define RTC_E_MEDIA_ENABLED ((HRESULT)0x80EE006F)
#define RTC_E_REFER_NOT_ACCEPTED ((HRESULT)0x80EE0070)
#define RTC_E_REFER_NOT_ALLOWED ((HRESULT)0x80EE0071)
#define RTC_E_REFER_NOT_EXIST ((HRESULT)0x80EE0072)
#define RTC_E_SIP_HOLD_OPERATION_PENDING ((HRESULT)0x80EE0073)
#define RTC_E_SIP_UNHOLD_OPERATION_PENDING ((HRESULT)0x80EE0074)
#define RTC_E_MEDIA_SESSION_NOT_EXIST ((HRESULT)0x80EE0075)
#define RTC_E_MEDIA_SESSION_IN_HOLD ((HRESULT)0x80EE0076)
#define RTC_E_ANOTHER_MEDIA_SESSION_ACTIVE ((HRESULT)0x80EE0077)
#define RTC_E_MAX_REDIRECTS ((HRESULT)0x80EE0078)
#define RTC_E_REDIRECT_PROCESSING_FAILED ((HRESULT)0x80EE0079)
#define RTC_E_LISTENING_SOCKET_NOT_EXIST ((HRESULT)0x80EE007A)
#define RTC_E_INVALID_LISTEN_SOCKET ((HRESULT)0x80EE007B)
#define RTC_E_PORT_MANAGER_ALREADY_SET ((HRESULT)0x80EE007C)
#define RTC_E_SECURITY_LEVEL_ALREADY_SET ((HRESULT)0x80EE007D)
#define RTC_E_UDP_NOT_SUPPORTED ((HRESULT)0x80EE007E)
#define RTC_E_SIP_REFER_OPERATION_PENDING ((HRESULT)0x80EE007F)
#define RTC_E_PLATFORM_NOT_SUPPORTED ((HRESULT)0x80EE0080)
#define RTC_E_SIP_PEER_PARTICIPANT_IN_MULTIPARTY_SESSION ((HRESULT)0x80EE0081)
#define RTC_E_NOT_ALLOWED ((HRESULT)0x80EE0082)
#define RTC_E_REGISTRATION_DEACTIVATED ((HRESULT)0x80EE0083)
#define RTC_E_REGISTRATION_REJECTED ((HRESULT)0x80EE0084)
#define RTC_E_REGISTRATION_UNREGISTERED ((HRESULT)0x80EE0085)
#define RTC_E_STATUS_INFO_TRYING ((HRESULT)0x00EF0064)
#define RTC_E_STATUS_INFO_RINGING ((HRESULT)0x00EF00B4)
#define RTC_E_STATUS_INFO_CALL_FORWARDING ((HRESULT)0x00EF00B5)
#define RTC_E_STATUS_INFO_QUEUED ((HRESULT)0x00EF00B6)
#define RTC_E_STATUS_SESSION_PROGRESS ((HRESULT)0x00EF00B7)
#define RTC_E_STATUS_SUCCESS ((HRESULT)0x00EF00C8)
#define RTC_E_STATUS_REDIRECT_MULTIPLE_CHOICES ((HRESULT)0x80EF012C)
#define RTC_E_STATUS_REDIRECT_MOVED_PERMANENTLY ((HRESULT)0x80EF012D)
#define RTC_E_STATUS_REDIRECT_MOVED_TEMPORARILY ((HRESULT)0x80EF012E)
#define RTC_E_STATUS_REDIRECT_SEE_OTHER ((HRESULT)0x80EF012F)
#define RTC_E_STATUS_REDIRECT_USE_PROXY ((HRESULT)0x80EF0131)
#define RTC_E_STATUS_REDIRECT_ALTERNATIVE_SERVICE ((HRESULT)0x80EF017C)
#define RTC_E_STATUS_CLIENT_BAD_REQUEST ((HRESULT)0x80EF0190)
#define RTC_E_STATUS_CLIENT_UNAUTHORIZED ((HRESULT)0x80EF0191)
#define RTC_E_STATUS_CLIENT_PAYMENT_REQUIRED ((HRESULT)0x80EF0192)
#define RTC_E_STATUS_CLIENT_FORBIDDEN ((HRESULT)0x80EF0193)
#define RTC_E_STATUS_CLIENT_NOT_FOUND ((HRESULT)0x80EF0194)
#define RTC_E_STATUS_CLIENT_METHOD_NOT_ALLOWED ((HRESULT)0x80EF0195)
#define RTC_E_STATUS_CLIENT_NOT_ACCEPTABLE ((HRESULT)0x80EF0196)
#define RTC_E_STATUS_CLIENT_PROXY_AUTHENTICATION_REQUIRED ((HRESULT)0x80EF0197)
#define RTC_E_STATUS_CLIENT_REQUEST_TIMEOUT ((HRESULT)0x80EF0198)
#define RTC_E_STATUS_CLIENT_CONFLICT ((HRESULT)0x80EF0199)
#define RTC_E_STATUS_CLIENT_GONE ((HRESULT)0x80EF019A)
#define RTC_E_STATUS_CLIENT_LENGTH_REQUIRED ((HRESULT)0x80EF019B)
#define RTC_E_STATUS_CLIENT_REQUEST_ENTITY_TOO_LARGE ((HRESULT)0x80EF019D)
#define RTC_E_STATUS_CLIENT_REQUEST_URI_TOO_LARGE ((HRESULT)0x80EF019E)
#define RTC_E_STATUS_CLIENT_UNSUPPORTED_MEDIA_TYPE ((HRESULT)0x80EF019F)
#define RTC_E_STATUS_CLIENT_BAD_EXTENSION ((HRESULT)0x80EF01A4)
#define RTC_E_STATUS_CLIENT_TEMPORARILY_NOT_AVAILABLE ((HRESULT)0x80EF01E0)
#define RTC_E_STATUS_CLIENT_TRANSACTION_DOES_NOT_EXIST ((HRESULT)0x80EF01E1)
#define RTC_E_STATUS_CLIENT_LOOP_DETECTED ((HRESULT)0x80EF01E2)
#define RTC_E_STATUS_CLIENT_TOO_MANY_HOPS ((HRESULT)0x80EF01E3)
#define RTC_E_STATUS_CLIENT_ADDRESS_INCOMPLETE ((HRESULT)0x80EF01E4)
#define RTC_E_STATUS_CLIENT_AMBIGUOUS ((HRESULT)0x80EF01E5)
#define RTC_E_STATUS_CLIENT_BUSY_HERE ((HRESULT)0x80EF01E6)
#define RTC_E_STATUS_REQUEST_TERMINATED ((HRESULT)0x80EF01E7)
#define RTC_E_STATUS_NOT_ACCEPTABLE_HERE ((HRESULT)0x80EF01E8)
#define RTC_E_STATUS_SERVER_INTERNAL_ERROR ((HRESULT)0x80EF01F4)
#define RTC_E_STATUS_SERVER_NOT_IMPLEMENTED ((HRESULT)0x80EF01F5)
#define RTC_E_STATUS_SERVER_BAD_GATEWAY ((HRESULT)0x80EF01F6)
#define RTC_E_STATUS_SERVER_SERVICE_UNAVAILABLE ((HRESULT)0x80EF01F7)
#define RTC_E_STATUS_SERVER_SERVER_TIMEOUT ((HRESULT)0x80EF01F8)
#define RTC_E_STATUS_SERVER_VERSION_NOT_SUPPORTED ((HRESULT)0x80EF01F9)
#define RTC_E_STATUS_GLOBAL_BUSY_EVERYWHERE ((HRESULT)0x80EF0258)
#define RTC_E_STATUS_GLOBAL_DECLINE ((HRESULT)0x80EF025B)
#define RTC_E_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE ((HRESULT)0x80EF025C)
#define RTC_E_STATUS_GLOBAL_NOT_ACCEPTABLE ((HRESULT)0x80EF025E)
#define RTC_E_PINT_STATUS_REJECTED_BUSY ((HRESULT)0x80F00005)
#define RTC_E_PINT_STATUS_REJECTED_NO_ANSWER ((HRESULT)0x80F00006)
#define RTC_E_PINT_STATUS_REJECTED_ALL_BUSY ((HRESULT)0x80F00007)
#define RTC_E_PINT_STATUS_REJECTED_PL_FAILED ((HRESULT)0x80F00008)
#define RTC_E_PINT_STATUS_REJECTED_SW_FAILED ((HRESULT)0x80F00009)
#define RTC_E_PINT_STATUS_REJECTED_CANCELLED ((HRESULT)0x80F0000A)
#define RTC_E_PINT_STATUS_REJECTED_BADNUMBER ((HRESULT)0x80F0000B)
