.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_import" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_import \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_import(gnutls_pubkey_t " key ", const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " format ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
The public key. 
.IP "const gnutls_datum_t * data" 12
The DER or PEM encoded certificate. 
.IP "gnutls_x509_crt_fmt_t format" 12
One of DER or PEM 
.SH "DESCRIPTION"
This function will import the provided public key in
a SubjectPublicKeyInfo X.509 structure to a native
\fBgnutls_pubkey_t\fP type. The output will be stored 
in  \fIkey\fP . If the public key is PEM encoded it should have a header 
of "PUBLIC KEY". 
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
