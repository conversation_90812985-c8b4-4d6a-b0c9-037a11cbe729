
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
          "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html>

<head>
    <meta http-equiv="Content-Type"
          content="text/html; charset=utf-8" />
    <title></title>
    <style type="text/css">
        table.diff {font-family: Menlo, Consolas, Monaco, Liberation Mono, Lucida Console, monospace; border:medium}
        .diff_header {background-color:#e0e0e0}
        td.diff_header {text-align:right}
        .diff_next {background-color:#c0c0c0}
        .diff_add {background-color:#aaffaa}
        .diff_chg {background-color:#ffff77}
        .diff_sub {background-color:#ffaaaa}
    </style>
</head>

<body>
    
    <table class="diff" id="difflib_chg_to0__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">from</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">to</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to0__0"><a href="#difflib_chg_to0__0">f</a></td><td class="diff_header" id="from0_1">1</td><td nowrap="nowrap"></td><td class="diff_next"><a href="#difflib_chg_to0__0">f</a></td><td class="diff_header" id="to0_1">1</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to0__1">n</a></td><td class="diff_header" id="from0_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to0__1">n</a></td><td class="diff_header" id="to0_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_3">3</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to0_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_5">5</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to0_4">4</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to0_5">5</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_6">6</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_6">6</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_7">7</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_7">7</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_8">8</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_8">8</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_9">9</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_9">9</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_10">10</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_10">10</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_11">11</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_11">11</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to0__1"></td><td class="diff_header" id="from0_12">12</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_12">12</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_13">13</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_13">13</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_14">14</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_14">14</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_15">15</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_15">15</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_16">16</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to0_16">16</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to0__2">n</a></td><td class="diff_header" id="from0_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to0__2">n</a></td><td class="diff_header" id="to0_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_18">18</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_19">19</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to0_18">18</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_20">20</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to0_19">19</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to0_20">20</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_21">21</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_21">21</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_22">22</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_22">22</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_23">23</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_23">23</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_24">24</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_24">24</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_25">25</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_25">25</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_26">26</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_26">26</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to0__2"></td><td class="diff_header" id="from0_27">27</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_27">27</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_28">28</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_28">28</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_29">29</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_29">29</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_30">30</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_30">30</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_31">31</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to0_31">31</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to0__top">t</a></td><td class="diff_header" id="from0_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to0__top">t</a></td><td class="diff_header" id="to0_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_33">33</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_34">34</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to0_33">33</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_35">35</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to0_34">34</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to0_35">35</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_36">36</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_36">36</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_37">37</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_37">37</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_38">38</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_38">38</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_39">39</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_39">39</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_40">40</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_40">40</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_41">41</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_41">41</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_42">42</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_42">42</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_43">43</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_43">43</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_44">44</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_44">44</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_45">45</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to0_45">45</td><td nowrap="nowrap">123</td></tr>
        </tbody>
    </table>
    <table class="diff" summary="Legends">
        <tr> <th colspan="2"> Legends </th> </tr>
        <tr> <td> <table border="" summary="Colors">
                      <tr><th> Colors </th> </tr>
                      <tr><td class="diff_add">&nbsp;Added&nbsp;</td></tr>
                      <tr><td class="diff_chg">Changed</td> </tr>
                      <tr><td class="diff_sub">Deleted</td> </tr>
                  </table></td>
             <td> <table border="" summary="Links">
                      <tr><th colspan="2"> Links </th> </tr>
                      <tr><td>(f)irst change</td> </tr>
                      <tr><td>(n)ext change</td> </tr>
                      <tr><td>(t)op</td> </tr>
                  </table></td> </tr>
    </table>

<h2>Context (first diff within numlines=5(default))</h2>

    <table class="diff" id="difflib_chg_to1__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">from</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">to</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to1__0"><a href="#difflib_chg_to1__0">f</a></td><td class="diff_header" id="from1_1">1</td><td nowrap="nowrap"></td><td class="diff_next"><a href="#difflib_chg_to1__0">f</a></td><td class="diff_header" id="to1_1">1</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to1__1">n</a></td><td class="diff_header" id="from1_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to1__1">n</a></td><td class="diff_header" id="to1_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_3">3</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to1_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_5">5</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to1_4">4</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to1_5">5</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_6">6</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_6">6</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_7">7</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_7">7</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_8">8</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_8">8</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_9">9</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_9">9</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_10">10</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_10">10</td><td nowrap="nowrap">123</td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to1__1"></td><td class="diff_header" id="from1_12">12</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_12">12</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_13">13</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_13">13</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_14">14</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_14">14</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_15">15</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_15">15</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_16">16</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to1_16">16</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to1__2">n</a></td><td class="diff_header" id="from1_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to1__2">n</a></td><td class="diff_header" id="to1_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_18">18</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_19">19</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to1_18">18</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_20">20</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to1_19">19</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to1_20">20</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_21">21</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_21">21</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_22">22</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_22">22</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_23">23</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_23">23</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_24">24</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_24">24</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_25">25</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_25">25</td><td nowrap="nowrap">123</td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to1__2"></td><td class="diff_header" id="from1_27">27</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_27">27</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_28">28</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_28">28</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_29">29</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_29">29</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_30">30</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_30">30</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_31">31</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to1_31">31</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to1__top">t</a></td><td class="diff_header" id="from1_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to1__top">t</a></td><td class="diff_header" id="to1_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_33">33</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_34">34</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to1_33">33</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_35">35</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to1_34">34</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to1_35">35</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_36">36</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_36">36</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_37">37</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_37">37</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_38">38</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_38">38</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_39">39</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_39">39</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_40">40</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to1_40">40</td><td nowrap="nowrap">123</td></tr>
        </tbody>
    </table>
<h2>Context (first diff after numlines=5(default))</h2>

    <table class="diff" id="difflib_chg_to2__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">from</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">to</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to2__0"></td><td class="diff_header" id="from2_7">7</td><td nowrap="nowrap">456</td><td class="diff_next"></td><td class="diff_header" id="to2_7">7</td><td nowrap="nowrap">456</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_8">8</td><td nowrap="nowrap">456</td><td class="diff_next"></td><td class="diff_header" id="to2_8">8</td><td nowrap="nowrap">456</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_9">9</td><td nowrap="nowrap">456</td><td class="diff_next"></td><td class="diff_header" id="to2_9">9</td><td nowrap="nowrap">456</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_10">10</td><td nowrap="nowrap">456</td><td class="diff_next"></td><td class="diff_header" id="to2_10">10</td><td nowrap="nowrap">456</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_11">11</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to2_11">11</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to2__1">n</a></td><td class="diff_header" id="from2_12">12</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to2__1">n</a></td><td class="diff_header" id="to2_12">12</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_13">13</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_14">14</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to2_13">13</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_15">15</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to2_14">14</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to2_15">15</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_16">16</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_16">16</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_17">17</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_17">17</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_18">18</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_18">18</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_19">19</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_19">19</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_20">20</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_20">20</td><td nowrap="nowrap">123</td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to2__1"></td><td class="diff_header" id="from2_22">22</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_22">22</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_23">23</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_23">23</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_24">24</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_24">24</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_25">25</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_25">25</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_26">26</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to2_26">26</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to2__2">n</a></td><td class="diff_header" id="from2_27">27</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to2__2">n</a></td><td class="diff_header" id="to2_27">27</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_28">28</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_29">29</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to2_28">28</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_30">30</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to2_29">29</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to2_30">30</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_31">31</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_31">31</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_32">32</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_32">32</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_33">33</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_33">33</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_34">34</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_34">34</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_35">35</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_35">35</td><td nowrap="nowrap">123</td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to2__2"></td><td class="diff_header" id="from2_37">37</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_37">37</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_38">38</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_38">38</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_39">39</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_39">39</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_40">40</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_40">40</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_41">41</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to2_41">41</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to2__top">t</a></td><td class="diff_header" id="from2_42">42</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to2__top">t</a></td><td class="diff_header" id="to2_42">42</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_43">43</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_44">44</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to2_43">43</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_45">45</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to2_44">44</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to2_45">45</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_46">46</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_46">46</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_47">47</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_47">47</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_48">48</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_48">48</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_49">49</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_49">49</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from2_50">50</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to2_50">50</td><td nowrap="nowrap">123</td></tr>
        </tbody>
    </table>
<h2>Context (numlines=6)</h2>

    <table class="diff" id="difflib_chg_to3__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">from</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">to</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to3__0"><a href="#difflib_chg_to3__0">f</a></td><td class="diff_header" id="from3_1">1</td><td nowrap="nowrap"></td><td class="diff_next"><a href="#difflib_chg_to3__0">f</a></td><td class="diff_header" id="to3_1">1</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__1">n</a></td><td class="diff_header" id="from3_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to3__1">n</a></td><td class="diff_header" id="to3_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_3">3</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to3_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_5">5</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to3_4">4</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_5">5</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_6">6</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_6">6</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_7">7</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_7">7</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_8">8</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_8">8</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_9">9</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_9">9</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_10">10</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_10">10</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to3__1"></td><td class="diff_header" id="from3_11">11</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_11">11</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_12">12</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_12">12</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_13">13</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_13">13</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_14">14</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_14">14</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_15">15</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_15">15</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_16">16</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_16">16</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__2">n</a></td><td class="diff_header" id="from3_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to3__2">n</a></td><td class="diff_header" id="to3_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_18">18</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_19">19</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to3_18">18</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_20">20</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to3_19">19</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_20">20</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_21">21</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_21">21</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_22">22</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_22">22</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_23">23</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_23">23</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_24">24</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_24">24</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_25">25</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_25">25</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to3__2"></td><td class="diff_header" id="from3_26">26</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_26">26</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_27">27</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_27">27</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_28">28</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_28">28</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_29">29</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_29">29</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_30">30</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_30">30</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_31">31</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_31">31</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__top">t</a></td><td class="diff_header" id="from3_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to3__top">t</a></td><td class="diff_header" id="to3_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_33">33</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_34">34</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to3_33">33</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_35">35</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to3_34">34</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_35">35</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_36">36</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_36">36</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_37">37</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_37">37</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_38">38</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_38">38</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_39">39</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_39">39</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_40">40</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_40">40</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_41">41</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to3_41">41</td><td nowrap="nowrap">123</td></tr>
        </tbody>
    </table>
<h2>Context (numlines=0)</h2>

    <table class="diff" id="difflib_chg_to4__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">from</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">to</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to4__0"><a href="#difflib_chg_to4__1">n</a></td><td class="diff_header" id="from4_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to4__1">n</a></td><td class="diff_header" id="to4_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_3">3</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to4_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_5">5</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to4_4">4</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to4_5">5</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to4__1"><a href="#difflib_chg_to4__2">n</a></td><td class="diff_header" id="from4_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to4__2">n</a></td><td class="diff_header" id="to4_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_18">18</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_19">19</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to4_18">18</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_20">20</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to4_19">19</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to4_20">20</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to4__2"><a href="#difflib_chg_to4__top">t</a></td><td class="diff_header" id="from4_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">TT</span>er&nbsp;than&nbsp;ugly.</td><td class="diff_next"><a href="#difflib_chg_to4__top">t</a></td><td class="diff_header" id="to4_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;be<span class="diff_chg">tt</span>er&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_33">33</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_34">34</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to4_33">33</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.<span class="diff_add">&nbsp;&nbsp;</span>&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_35">35</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</span></td><td class="diff_next"></td><td class="diff_header" id="to4_34">34</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;4.&nbsp;Complicated&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to4_35">35</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;5.&nbsp;Flat&nbsp;is&nbsp;better&nbsp;than&nbsp;nested.</span></td></tr>
        </tbody>
    </table>
<h2>Same Context</h2>

    <table class="diff" id="difflib_chg_to5__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">from</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">to</th></tr></thead>
        <tbody>
            <tr><td class="diff_next"><a href="#difflib_chg_to5__top">t</a></td><td></td><td>&nbsp;No Differences Found&nbsp;</td><td class="diff_next"><a href="#difflib_chg_to5__top">t</a></td><td></td><td>&nbsp;No Differences Found&nbsp;</td></tr>
        </tbody>
    </table>
<h2>Same Full</h2>

    <table class="diff" id="difflib_chg_to6__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">from</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">to</th></tr></thead>
        <tbody>
            <tr><td class="diff_next"><a href="#difflib_chg_to6__top">t</a></td><td class="diff_header" id="from6_1">1</td><td nowrap="nowrap"></td><td class="diff_next"><a href="#difflib_chg_to6__top">t</a></td><td class="diff_header" id="to6_1">1</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;beTTer&nbsp;than&nbsp;ugly.</td><td class="diff_next"></td><td class="diff_header" id="to6_2">2</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;beTTer&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</td><td class="diff_next"></td><td class="diff_header" id="to6_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to6_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_5">5</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</td><td class="diff_next"></td><td class="diff_header" id="to6_5">5</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_6">6</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_6">6</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_7">7</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_7">7</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_8">8</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_8">8</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_9">9</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_9">9</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_10">10</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_10">10</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_11">11</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_11">11</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_12">12</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_12">12</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_13">13</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_13">13</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_14">14</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_14">14</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_15">15</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_15">15</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_16">16</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to6_16">16</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;beTTer&nbsp;than&nbsp;ugly.</td><td class="diff_next"></td><td class="diff_header" id="to6_17">17</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;beTTer&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_18">18</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</td><td class="diff_next"></td><td class="diff_header" id="to6_18">18</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_19">19</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to6_19">19</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_20">20</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</td><td class="diff_next"></td><td class="diff_header" id="to6_20">20</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_21">21</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_21">21</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_22">22</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_22">22</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_23">23</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_23">23</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_24">24</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_24">24</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_25">25</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_25">25</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_26">26</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_26">26</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_27">27</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_27">27</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_28">28</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_28">28</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_29">29</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_29">29</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_30">30</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_30">30</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_31">31</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to6_31">31</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;beTTer&nbsp;than&nbsp;ugly.</td><td class="diff_next"></td><td class="diff_header" id="to6_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;1.&nbsp;Beautiful&nbsp;is&nbsp;beTTer&nbsp;than&nbsp;ugly.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_33">33</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</td><td class="diff_next"></td><td class="diff_header" id="to6_33">33</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;2.&nbsp;Explicit&nbsp;is&nbsp;better&nbsp;than&nbsp;implicit.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_34">34</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td><td class="diff_next"></td><td class="diff_header" id="to6_34">34</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;3.&nbsp;Simple&nbsp;is&nbsp;better&nbsp;than&nbsp;complex.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_35">35</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</td><td class="diff_next"></td><td class="diff_header" id="to6_35">35</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;4.&nbsp;Complex&nbsp;is&nbsp;better&nbsp;than&nbsp;complicated.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_36">36</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_36">36</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_37">37</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_37">37</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_38">38</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_38">38</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_39">39</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_39">39</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_40">40</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_40">40</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_41">41</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_41">41</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_42">42</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_42">42</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_43">43</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_43">43</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_44">44</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_44">44</td><td nowrap="nowrap">123</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from6_45">45</td><td nowrap="nowrap">123</td><td class="diff_next"></td><td class="diff_header" id="to6_45">45</td><td nowrap="nowrap">123</td></tr>
        </tbody>
    </table>
<h2>Empty Context</h2>

    <table class="diff" id="difflib_chg_to7__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">from</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">to</th></tr></thead>
        <tbody>
            <tr><td class="diff_next"><a href="#difflib_chg_to7__top">t</a></td><td></td><td>&nbsp;No Differences Found&nbsp;</td><td class="diff_next"><a href="#difflib_chg_to7__top">t</a></td><td></td><td>&nbsp;No Differences Found&nbsp;</td></tr>
        </tbody>
    </table>
<h2>Empty Full</h2>

    <table class="diff" id="difflib_chg_to8__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">from</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">to</th></tr></thead>
        <tbody>
            <tr><td class="diff_next"><a href="#difflib_chg_to8__top">t</a></td><td></td><td>&nbsp;Empty File&nbsp;</td><td class="diff_next"><a href="#difflib_chg_to8__top">t</a></td><td></td><td>&nbsp;Empty File&nbsp;</td></tr>
        </tbody>
    </table>
<h2>tabsize=2</h2>

    <table class="diff" id="difflib_chg_to9__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to9__0"><a href="#difflib_chg_to9__0">f</a></td><td class="diff_header" id="from9_1">1</td><td nowrap="nowrap"></td><td class="diff_next"><a href="#difflib_chg_to9__0">f</a></td><td class="diff_header" id="to9_1">1</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to9__top">t</a></td><td class="diff_header" id="from9_2">2</td><td nowrap="nowrap"><span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;</span>Line&nbsp;1:&nbsp;preceded&nbsp;by&nbsp;from:[tt]&nbsp;to:[ssss]</td><td class="diff_next"><a href="#difflib_chg_to9__top">t</a></td><td class="diff_header" id="to9_2">2</td><td nowrap="nowrap"><span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;</span>Line&nbsp;1:&nbsp;preceded&nbsp;by&nbsp;from:[tt]&nbsp;to:[ssss]</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from9_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;<span class="diff_chg">&nbsp;&nbsp;</span>&nbsp;&nbsp;Line&nbsp;2:&nbsp;preceded&nbsp;by&nbsp;from:[sstt]&nbsp;to:[sssst]</td><td class="diff_next"></td><td class="diff_header" id="to9_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;<span class="diff_chg">&nbsp;&nbsp;</span>&nbsp;&nbsp;Line&nbsp;2:&nbsp;preceded&nbsp;by&nbsp;from:[sstt]&nbsp;to:[sssst]</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from9_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;<span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;</span>Line&nbsp;3:&nbsp;preceded&nbsp;by&nbsp;from:[sstst]&nbsp;to:[ssssss]</td><td class="diff_next"></td><td class="diff_header" id="to9_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;<span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;</span>Line&nbsp;3:&nbsp;preceded&nbsp;by&nbsp;from:[sstst]&nbsp;to:[ssssss]</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from9_5">5</td><td nowrap="nowrap">Line&nbsp;4:&nbsp;&nbsp;<span class="diff_chg">&nbsp;</span>has&nbsp;from:[sst]&nbsp;to:[sss]&nbsp;after&nbsp;:</td><td class="diff_next"></td><td class="diff_header" id="to9_5">5</td><td nowrap="nowrap">Line&nbsp;4:&nbsp;&nbsp;<span class="diff_chg">&nbsp;</span>has&nbsp;from:[sst]&nbsp;to:[sss]&nbsp;after&nbsp;:</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from9_6">6</td><td nowrap="nowrap">Line&nbsp;5:&nbsp;has&nbsp;from:[t]&nbsp;to:[ss]&nbsp;at&nbsp;end<span class="diff_sub">&nbsp;</span></td><td class="diff_next"></td><td class="diff_header" id="to9_6">6</td><td nowrap="nowrap">Line&nbsp;5:&nbsp;has&nbsp;from:[t]&nbsp;to:[ss]&nbsp;at&nbsp;end</td></tr>
        </tbody>
    </table>
<h2>tabsize=default</h2>

    <table class="diff" id="difflib_chg_to10__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to10__0"><a href="#difflib_chg_to10__0">f</a></td><td class="diff_header" id="from10_1">1</td><td nowrap="nowrap"></td><td class="diff_next"><a href="#difflib_chg_to10__0">f</a></td><td class="diff_header" id="to10_1">1</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to10__top">t</a></td><td class="diff_header" id="from10_2">2</td><td nowrap="nowrap"><span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>Line&nbsp;1:&nbsp;preceded&nbsp;by&nbsp;from:[tt]&nbsp;to:[ssss]</td><td class="diff_next"><a href="#difflib_chg_to10__top">t</a></td><td class="diff_header" id="to10_2">2</td><td nowrap="nowrap"><span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;</span>Line&nbsp;1:&nbsp;preceded&nbsp;by&nbsp;from:[tt]&nbsp;to:[ssss]</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from10_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;<span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>&nbsp;&nbsp;&nbsp;&nbsp;Line&nbsp;2:&nbsp;preceded&nbsp;by&nbsp;from:[sstt]&nbsp;to:[sssst]</td><td class="diff_next"></td><td class="diff_header" id="to10_3">3</td><td nowrap="nowrap">&nbsp;&nbsp;<span class="diff_chg">&nbsp;&nbsp;</span>&nbsp;&nbsp;&nbsp;&nbsp;Line&nbsp;2:&nbsp;preceded&nbsp;by&nbsp;from:[sstt]&nbsp;to:[sssst]</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from10_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;<span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>Line&nbsp;3:&nbsp;preceded&nbsp;by&nbsp;from:[sstst]&nbsp;to:[ssssss]</td><td class="diff_next"></td><td class="diff_header" id="to10_4">4</td><td nowrap="nowrap">&nbsp;&nbsp;<span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;</span>Line&nbsp;3:&nbsp;preceded&nbsp;by&nbsp;from:[sstst]&nbsp;to:[ssssss]</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from10_5">5</td><td nowrap="nowrap">Line&nbsp;4:&nbsp;&nbsp;<span class="diff_chg">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>has&nbsp;from:[sst]&nbsp;to:[sss]&nbsp;after&nbsp;:</td><td class="diff_next"></td><td class="diff_header" id="to10_5">5</td><td nowrap="nowrap">Line&nbsp;4:&nbsp;&nbsp;<span class="diff_chg">&nbsp;</span>has&nbsp;from:[sst]&nbsp;to:[sss]&nbsp;after&nbsp;:</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from10_6">6</td><td nowrap="nowrap">Line&nbsp;5:&nbsp;has&nbsp;from:[t]&nbsp;to:[ss]&nbsp;at&nbsp;end<span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td><td class="diff_next"></td><td class="diff_header" id="to10_6">6</td><td nowrap="nowrap">Line&nbsp;5:&nbsp;has&nbsp;from:[t]&nbsp;to:[ss]&nbsp;at&nbsp;end</td></tr>
        </tbody>
    </table>
<h2>Context (wrapcolumn=14,numlines=0)</h2>

    <table class="diff" id="difflib_chg_to11__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to11__0"><a href="#difflib_chg_to11__1">n</a></td><td class="diff_header" id="from11_4">4</td><td nowrap="nowrap"><span class="diff_sub">line&nbsp;2</span></td><td class="diff_next"><a href="#difflib_chg_to11__1">n</a></td><td class="diff_header" id="to11_4">4</td><td nowrap="nowrap"><span class="diff_add">line&nbsp;2&nbsp;&nbsp;&nbsp;&nbsp;adde</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_add">d</span></td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to11__1"><a href="#difflib_chg_to11__2">n</a></td><td class="diff_header" id="from11_6">6</td><td nowrap="nowrap">line&nbsp;4&nbsp;&nbsp;&nbsp;chan<span class="diff_chg">g</span></td><td class="diff_next"><a href="#difflib_chg_to11__2">n</a></td><td class="diff_header" id="to11_6">6</td><td nowrap="nowrap">line&nbsp;4&nbsp;&nbsp;&nbsp;chan<span class="diff_chg">G</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">e</span>d</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">E</span>d</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from11_7">7</td><td nowrap="nowrap">line&nbsp;5<span class="diff_chg">&nbsp;</span>&nbsp;&nbsp;chan<span class="diff_chg">g</span></td><td class="diff_next"></td><td class="diff_header" id="to11_7">7</td><td nowrap="nowrap">line&nbsp;5<span class="diff_chg">a</span>&nbsp;&nbsp;chan<span class="diff_chg">G</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg"></span>ed</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg"></span>ed</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from11_8">8</td><td nowrap="nowrap">line&nbsp;6<span class="diff_chg">&nbsp;</span>&nbsp;&nbsp;chang</td><td class="diff_next"></td><td class="diff_header" id="to11_8">8</td><td nowrap="nowrap">line&nbsp;6<span class="diff_chg">a</span>&nbsp;&nbsp;chang</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">e</span>d</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">E</span>d</td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to11__2"><a href="#difflib_chg_to11__3">n</a></td><td class="diff_header" id="from11_10">10</td><td nowrap="nowrap"><span class="diff_sub">line&nbsp;8&nbsp;&nbsp;subtra</span></td><td class="diff_next"><a href="#difflib_chg_to11__3">n</a></td><td class="diff_header" id="to11_10">10</td><td nowrap="nowrap"><span class="diff_add">line&nbsp;8</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">cted</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to11__3"><a href="#difflib_chg_to11__top">t</a></td><td class="diff_header" id="from11_12">12</td><td nowrap="nowrap"><span class="diff_sub">12345678901234</span></td><td class="diff_next"><a href="#difflib_chg_to11__top">t</a></td><td class="diff_header" id="to11_12">12</td><td nowrap="nowrap"><span class="diff_add">1234567890</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">56789012345689</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">012345</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from11_13">13</td><td nowrap="nowrap"><span class="diff_sub">short&nbsp;line</span></td><td class="diff_next"></td><td class="diff_header" id="to11_13">13</td><td nowrap="nowrap"><span class="diff_add">another&nbsp;long&nbsp;l</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_add">ine&nbsp;that&nbsp;needs</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_add">&nbsp;to&nbsp;be&nbsp;wrapped</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from11_14">14</td><td nowrap="nowrap">just&nbsp;fit<span class="diff_chg">s</span>&nbsp;in!!</td><td class="diff_next"></td><td class="diff_header" id="to11_14">14</td><td nowrap="nowrap">just&nbsp;fit<span class="diff_chg">S</span>&nbsp;in!!</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from11_15">15</td><td nowrap="nowrap">just&nbsp;fits&nbsp;in&nbsp;t</td><td class="diff_next"></td><td class="diff_header" id="to11_15">15</td><td nowrap="nowrap">just&nbsp;fits&nbsp;in&nbsp;t</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">wo&nbsp;line<span class="diff_chg">s</span>&nbsp;yup!!</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">wo&nbsp;line<span class="diff_chg">S</span>&nbsp;yup!!</td></tr>
        </tbody>
    </table>
<h2>wrapcolumn=14,splitlines()</h2>

    <table class="diff" id="difflib_chg_to12__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to12__0"><a href="#difflib_chg_to12__0">f</a></td><td class="diff_header" id="from12_1">1</td><td nowrap="nowrap">line&nbsp;0</td><td class="diff_next"><a href="#difflib_chg_to12__0">f</a></td><td class="diff_header" id="to12_1">1</td><td nowrap="nowrap">line&nbsp;0</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_2">2</td><td nowrap="nowrap">12345678901234</td><td class="diff_next"></td><td class="diff_header" id="to12_2">2</td><td nowrap="nowrap">12345678901234</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">56789012345689</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">56789012345689</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to12__1"></td><td class="diff_header">></td><td nowrap="nowrap">012345</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">012345</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_3">3</td><td nowrap="nowrap">line&nbsp;1</td><td class="diff_next"></td><td class="diff_header" id="to12_3">3</td><td nowrap="nowrap">line&nbsp;1</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to12__1">n</a></td><td class="diff_header" id="from12_4">4</td><td nowrap="nowrap"><span class="diff_sub">line&nbsp;2</span></td><td class="diff_next"><a href="#difflib_chg_to12__1">n</a></td><td class="diff_header" id="to12_4">4</td><td nowrap="nowrap"><span class="diff_add">line&nbsp;2&nbsp;&nbsp;&nbsp;&nbsp;adde</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_add">d</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_5">5</td><td nowrap="nowrap">line&nbsp;3</td><td class="diff_next"></td><td class="diff_header" id="to12_5">5</td><td nowrap="nowrap">line&nbsp;3</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to12__2">n</a></td><td class="diff_header" id="from12_6">6</td><td nowrap="nowrap">line&nbsp;4&nbsp;&nbsp;&nbsp;chan<span class="diff_chg">g</span></td><td class="diff_next"><a href="#difflib_chg_to12__2">n</a></td><td class="diff_header" id="to12_6">6</td><td nowrap="nowrap">line&nbsp;4&nbsp;&nbsp;&nbsp;chan<span class="diff_chg">G</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">e</span>d</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">E</span>d</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to12__2"></td><td class="diff_header" id="from12_7">7</td><td nowrap="nowrap">line&nbsp;5<span class="diff_chg">&nbsp;</span>&nbsp;&nbsp;chan<span class="diff_chg">g</span></td><td class="diff_next"></td><td class="diff_header" id="to12_7">7</td><td nowrap="nowrap">line&nbsp;5<span class="diff_chg">a</span>&nbsp;&nbsp;chan<span class="diff_chg">G</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg"></span>ed</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg"></span>ed</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_8">8</td><td nowrap="nowrap">line&nbsp;6<span class="diff_chg">&nbsp;</span>&nbsp;&nbsp;chang</td><td class="diff_next"></td><td class="diff_header" id="to12_8">8</td><td nowrap="nowrap">line&nbsp;6<span class="diff_chg">a</span>&nbsp;&nbsp;chang</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to12__3"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">e</span>d</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">E</span>d</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_9">9</td><td nowrap="nowrap">line&nbsp;7</td><td class="diff_next"></td><td class="diff_header" id="to12_9">9</td><td nowrap="nowrap">line&nbsp;7</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to12__3">n</a></td><td class="diff_header" id="from12_10">10</td><td nowrap="nowrap"><span class="diff_sub">line&nbsp;8&nbsp;&nbsp;subtra</span></td><td class="diff_next"><a href="#difflib_chg_to12__3">n</a></td><td class="diff_header" id="to12_10">10</td><td nowrap="nowrap"><span class="diff_add">line&nbsp;8</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">cted</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_11">11</td><td nowrap="nowrap">line&nbsp;9</td><td class="diff_next"></td><td class="diff_header" id="to12_11">11</td><td nowrap="nowrap">line&nbsp;9</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to12__top">t</a></td><td class="diff_header" id="from12_12">12</td><td nowrap="nowrap"><span class="diff_sub">12345678901234</span></td><td class="diff_next"><a href="#difflib_chg_to12__top">t</a></td><td class="diff_header" id="to12_12">12</td><td nowrap="nowrap"><span class="diff_add">1234567890</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">56789012345689</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">012345</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_13">13</td><td nowrap="nowrap"><span class="diff_sub">short&nbsp;line</span></td><td class="diff_next"></td><td class="diff_header" id="to12_13">13</td><td nowrap="nowrap"><span class="diff_add">another&nbsp;long&nbsp;l</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_add">ine&nbsp;that&nbsp;needs</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_add">&nbsp;to&nbsp;be&nbsp;wrapped</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_14">14</td><td nowrap="nowrap">just&nbsp;fit<span class="diff_chg">s</span>&nbsp;in!!</td><td class="diff_next"></td><td class="diff_header" id="to12_14">14</td><td nowrap="nowrap">just&nbsp;fit<span class="diff_chg">S</span>&nbsp;in!!</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_15">15</td><td nowrap="nowrap">just&nbsp;fits&nbsp;in&nbsp;t</td><td class="diff_next"></td><td class="diff_header" id="to12_15">15</td><td nowrap="nowrap">just&nbsp;fits&nbsp;in&nbsp;t</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">wo&nbsp;line<span class="diff_chg">s</span>&nbsp;yup!!</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">wo&nbsp;line<span class="diff_chg">S</span>&nbsp;yup!!</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from12_16">16</td><td nowrap="nowrap">the&nbsp;end</td><td class="diff_next"></td><td class="diff_header" id="to12_16">16</td><td nowrap="nowrap">the&nbsp;end</td></tr>
        </tbody>
    </table>
<h2>wrapcolumn=14,splitlines(True)</h2>

    <table class="diff" id="difflib_chg_to13__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to13__0"><a href="#difflib_chg_to13__0">f</a></td><td class="diff_header" id="from13_1">1</td><td nowrap="nowrap">line&nbsp;0</td><td class="diff_next"><a href="#difflib_chg_to13__0">f</a></td><td class="diff_header" id="to13_1">1</td><td nowrap="nowrap">line&nbsp;0</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_2">2</td><td nowrap="nowrap">12345678901234</td><td class="diff_next"></td><td class="diff_header" id="to13_2">2</td><td nowrap="nowrap">12345678901234</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">56789012345689</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">56789012345689</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to13__1"></td><td class="diff_header">></td><td nowrap="nowrap">012345</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">012345</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_3">3</td><td nowrap="nowrap">line&nbsp;1</td><td class="diff_next"></td><td class="diff_header" id="to13_3">3</td><td nowrap="nowrap">line&nbsp;1</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to13__1">n</a></td><td class="diff_header" id="from13_4">4</td><td nowrap="nowrap"><span class="diff_sub">line&nbsp;2</span></td><td class="diff_next"><a href="#difflib_chg_to13__1">n</a></td><td class="diff_header" id="to13_4">4</td><td nowrap="nowrap"><span class="diff_add">line&nbsp;2&nbsp;&nbsp;&nbsp;&nbsp;adde</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_add">d</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_5">5</td><td nowrap="nowrap">line&nbsp;3</td><td class="diff_next"></td><td class="diff_header" id="to13_5">5</td><td nowrap="nowrap">line&nbsp;3</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to13__2">n</a></td><td class="diff_header" id="from13_6">6</td><td nowrap="nowrap">line&nbsp;4&nbsp;&nbsp;&nbsp;chan<span class="diff_chg">g</span></td><td class="diff_next"><a href="#difflib_chg_to13__2">n</a></td><td class="diff_header" id="to13_6">6</td><td nowrap="nowrap">line&nbsp;4&nbsp;&nbsp;&nbsp;chan<span class="diff_chg">G</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">e</span>d</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">E</span>d</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to13__2"></td><td class="diff_header" id="from13_7">7</td><td nowrap="nowrap">line&nbsp;5<span class="diff_chg">&nbsp;</span>&nbsp;&nbsp;chan<span class="diff_chg">g</span></td><td class="diff_next"></td><td class="diff_header" id="to13_7">7</td><td nowrap="nowrap">line&nbsp;5<span class="diff_chg">a</span>&nbsp;&nbsp;chan<span class="diff_chg">G</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg"></span>ed</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg"></span>ed</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_8">8</td><td nowrap="nowrap">line&nbsp;6<span class="diff_chg">&nbsp;</span>&nbsp;&nbsp;chang</td><td class="diff_next"></td><td class="diff_header" id="to13_8">8</td><td nowrap="nowrap">line&nbsp;6<span class="diff_chg">a</span>&nbsp;&nbsp;chang</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to13__3"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">e</span>d</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_chg">E</span>d</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_9">9</td><td nowrap="nowrap">line&nbsp;7</td><td class="diff_next"></td><td class="diff_header" id="to13_9">9</td><td nowrap="nowrap">line&nbsp;7</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to13__3">n</a></td><td class="diff_header" id="from13_10">10</td><td nowrap="nowrap"><span class="diff_sub">line&nbsp;8&nbsp;&nbsp;subtra</span></td><td class="diff_next"><a href="#difflib_chg_to13__3">n</a></td><td class="diff_header" id="to13_10">10</td><td nowrap="nowrap"><span class="diff_add">line&nbsp;8</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">cted</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_11">11</td><td nowrap="nowrap">line&nbsp;9</td><td class="diff_next"></td><td class="diff_header" id="to13_11">11</td><td nowrap="nowrap">line&nbsp;9</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to13__top">t</a></td><td class="diff_header" id="from13_12">12</td><td nowrap="nowrap"><span class="diff_sub">12345678901234</span></td><td class="diff_next"><a href="#difflib_chg_to13__top">t</a></td><td class="diff_header" id="to13_12">12</td><td nowrap="nowrap"><span class="diff_add">1234567890</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">56789012345689</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">012345</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_13">13</td><td nowrap="nowrap"><span class="diff_sub">short&nbsp;line</span></td><td class="diff_next"></td><td class="diff_header" id="to13_13">13</td><td nowrap="nowrap"><span class="diff_add">another&nbsp;long&nbsp;l</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_add">ine&nbsp;that&nbsp;needs</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_add">&nbsp;to&nbsp;be&nbsp;wrapped</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_14">14</td><td nowrap="nowrap">just&nbsp;fit<span class="diff_chg">s</span>&nbsp;in!!</td><td class="diff_next"></td><td class="diff_header" id="to13_14">14</td><td nowrap="nowrap">just&nbsp;fit<span class="diff_chg">S</span>&nbsp;in!!</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_15">15</td><td nowrap="nowrap">just&nbsp;fits&nbsp;in&nbsp;t</td><td class="diff_next"></td><td class="diff_header" id="to13_15">15</td><td nowrap="nowrap">just&nbsp;fits&nbsp;in&nbsp;t</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">wo&nbsp;line<span class="diff_chg">s</span>&nbsp;yup!!</td><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap">wo&nbsp;line<span class="diff_chg">S</span>&nbsp;yup!!</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from13_16">16</td><td nowrap="nowrap">the&nbsp;end</td><td class="diff_next"></td><td class="diff_header" id="to13_16">16</td><td nowrap="nowrap">the&nbsp;end</td></tr>
        </tbody>
    </table>
</body>

</html>