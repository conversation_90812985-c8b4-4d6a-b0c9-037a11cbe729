/* This file contains the definitions and documentation for the
   additional tree codes used in the GNU C++ compiler (see tree.def
   for the standard codes).
   Copyright (C) 1987-2025 Free Software Foundation, Inc.
   Hacked by <PERSON> (<EMAIL>)

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 3, or (at your option)
any later version.

GCC is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */


/* An OFFSET_REF is used in two situations:

   1. An expression of the form `A::m' where `A' is a class and `m' is
      a non-static member or an overload set.  In this case, operand 0
      will be a TYPE (corresponding to `A') and operand 1 will be a
      FIELD_DECL, BASELINK, or TEMPLATE_ID_EXPR (corresponding to `m').

      The expression is a pointer-to-member if its address is taken (and
      if, after any overload resolution, 'm' does not designate a
      static or explicit object member function).  It simply denotes a
      member of the object if its address is not taken.

      This form is only used during the parsing phase; once semantic
      analysis has taken place they are eliminated.

   2. An expression of the form `x.*p'.  In this case, operand 0 will
      be an expression corresponding to `x' and operand 1 will be an
      expression with pointer-to-member type.  */
DEFTREECODE (OFFSET_REF, "offset_ref", tcc_reference, 2)

/* A pointer-to-member constant.  For a pointer-to-member constant
   `X::Y' The PTRMEM_CST_CLASS is the RECORD_TYPE for `X' and the
   PTRMEM_CST_MEMBER is the _DECL for `Y'.  */
DEFTREECODE (PTRMEM_CST, "ptrmem_cst", tcc_constant, 0)

/* For NEW_EXPR, operand 0 is the placement list.
   Operand 1 is the new-declarator.
   Operand 2 is the number of elements in the array.
   Operand 3 is the initializer.  */
DEFTREECODE (NEW_EXPR, "nw_expr", tcc_expression, 4)
DEFTREECODE (VEC_NEW_EXPR, "vec_nw_expr", tcc_expression, 3)

/* For DELETE_EXPR, operand 0 is the store to be destroyed.
   Operand 1 is the value to pass to the destroying function
   saying whether the store should be deallocated as well.  */
DEFTREECODE (DELETE_EXPR, "dl_expr", tcc_expression, 2)
DEFTREECODE (VEC_DELETE_EXPR, "vec_dl_expr", tcc_expression, 2)

/* Value is reference to particular overloaded class method.
   Operand 0 is the class, operand 1 is the field
   The COMPLEXITY field holds the class level (usually 0).  */
DEFTREECODE (SCOPE_REF, "scope_ref", tcc_reference, 2)

/* When composing an object with a member, this is the result.
   Operand 0 is the object.  Operand 1 is the member (usually
   a dereferenced pointer to member).  */
DEFTREECODE (MEMBER_REF, "member_ref", tcc_reference, 2)

/* Type conversion operator in C++.  TREE_TYPE is type that this
   operator converts to.  Operand is expression to be converted.  */
DEFTREECODE (TYPE_EXPR, "type_expr", tcc_expression, 1)

/* AGGR_INIT_EXPRs have a variably-sized representation similar to
   that of CALL_EXPRs.  Operand 0 is an INTEGER_CST node containing the
   operand count, operand 1 is the function which performs initialization,
   operand 2 is the slot which was allocated for this expression, and
   the remaining operands are the arguments to the initialization function.  */
DEFTREECODE (AGGR_INIT_EXPR, "aggr_init_expr", tcc_vl_exp, 3)

/* Initialization of an array from another array, expressed at a high level
   so that it works with TARGET_EXPR.  Operand 0 is the target, operand 1
   is the initializer.  */
DEFTREECODE (VEC_INIT_EXPR, "vec_init_expr", tcc_expression, 2)

/* A throw expression.  operand 0 is the expression, if there was one,
   else it is NULL_TREE.  */
DEFTREECODE (THROW_EXPR, "throw_expr", tcc_expression, 1)

/* An empty class object.  The TREE_TYPE gives the class type.  We use
   these to avoid actually creating instances of the empty classes.  */
DEFTREECODE (EMPTY_CLASS_EXPR, "empty_class_expr", tcc_expression, 0)

/* A reference to a member function or member functions from a base
   class.  BASELINK_FUNCTIONS gives the FUNCTION_DECL,
   TEMPLATE_DECL, OVERLOAD, or TEMPLATE_ID_EXPR corresponding to the
   functions.  BASELINK_BINFO gives the base from which the functions
   come, i.e., the base to which the `this' pointer must be converted
   before the functions are called.  BASELINK_ACCESS_BINFO gives the
   base used to name the functions.

   A BASELINK is an expression; the TREE_TYPE of the BASELINK gives
   the type of the expression.  This type is either a FUNCTION_TYPE,
   METHOD_TYPE, or `unknown_type_node' indicating that the function is
   overloaded.  */
DEFTREECODE (BASELINK, "baselink", tcc_exceptional, 0)

/* Template definition.  The following fields have the specified uses,
   although there are other macros in cp-tree.h that should be used for
   accessing this data.
	DECL_ARGUMENTS		template parm vector
	DECL_TEMPLATE_INFO      template text &c
	DECL_VINDEX		list of instantiations already produced;
				only done for functions so far
   For class template:
	DECL_INITIAL		associated templates (methods &c)
	DECL_TEMPLATE_RESULT    null
   For non-class templates:
	TREE_TYPE		type of object to be constructed
	DECL_TEMPLATE_RESULT    decl for object to be created
				(e.g., FUNCTION_DECL with tmpl parms used)
 */
DEFTREECODE (TEMPLATE_DECL, "template_decl", tcc_declaration, 0)

/* Index into a template parameter list.  The TEMPLATE_PARM_IDX gives
   the index (from 0) of the parameter, while the TEMPLATE_PARM_LEVEL
   gives the level (from 1) of the parameter.

   Here's an example:

   template <class T> // Index 0, Level 1.
   struct S
   {
      template <class U, // Index 0, Level 2.
		class V> // Index 1, Level 2.
      void f();
   };

   The DESCENDANTS will be a chain of TEMPLATE_PARM_INDEXs descended
   from this one.  The first descendant will have the same IDX, but
   its LEVEL will be one less.  The TREE_CHAIN field is used to chain
   together the descendants.  The TEMPLATE_PARM_DECL is the
   declaration of this parameter, either a TYPE_DECL or CONST_DECL.
   The TEMPLATE_PARM_ORIG_LEVEL is the LEVEL of the most distant
   parent, i.e., the LEVEL that the parameter originally had when it
   was declared.  For example, if we instantiate S<int>, we will have:

   struct S<int>
   {
     template <class U, // Index 0, Level 1, Orig Level 2
	       class V> // Index 1, Level 1, Orig Level 2
     void f();
   };

   The LEVEL is the level of the parameter when we are worrying about
   the types of things; the ORIG_LEVEL is the level when we are
   worrying about instantiating things.  */
DEFTREECODE (TEMPLATE_PARM_INDEX, "template_parm_index", tcc_exceptional, 0)

/* Index into a template parameter list for template template parameters.
   This parameter must be a type.  The TYPE_FIELDS value will be a
   TEMPLATE_PARM_INDEX.

   It is used without template arguments like TT in C<TT>,
   TYPE_NAME is a TEMPLATE_DECL.  */
DEFTREECODE (TEMPLATE_TEMPLATE_PARM, "template_template_parm", tcc_type, 0)

/* The ordering of the following codes is optimized for the checking
   macros in tree.h.  Changing the order will degrade the speed of the
   compiler.  TEMPLATE_TYPE_PARM, TYPENAME_TYPE, TYPEOF_TYPE,
   BOUND_TEMPLATE_TEMPLATE_PARM.  */

/* Index into a template parameter list.  This parameter must be a type.
   The type.values field will be a TEMPLATE_PARM_INDEX.  */
DEFTREECODE (TEMPLATE_TYPE_PARM, "template_type_parm", tcc_type, 0)

/* A type designated by `typename T::t'.  TYPE_CONTEXT is `T',
   TYPE_NAME is an IDENTIFIER_NODE for `t'.  If the type was named via
   template-id, TYPENAME_TYPE_FULLNAME will hold the TEMPLATE_ID_EXPR.
   TREE_TYPE is always NULL.  */
DEFTREECODE (TYPENAME_TYPE, "typename_type", tcc_type, 0)

/* A type designated by `__typeof (expr)'.  TYPEOF_TYPE_EXPR is the
   expression in question.  */
DEFTREECODE (TYPEOF_TYPE, "typeof_type", tcc_type, 0)

/* Like TEMPLATE_TEMPLATE_PARM it is used with bound template arguments
   like TT<int>.
   In this case, TEMPLATE_TEMPLATE_PARM_TEMPLATE_INFO contains the
   template name and its bound arguments.  TYPE_NAME is a TYPE_DECL.  */
DEFTREECODE (BOUND_TEMPLATE_TEMPLATE_PARM, "bound_template_template_parm",
	     tcc_type, 0)

/* For template template argument of the form `T::template C'.
   TYPE_CONTEXT is `T', the template parameter dependent object.
   TYPE_NAME is a TEMPLATE_DECL, whose DECL_TEMPLATE_PARMS are any
   template parms of the instantiation.  That decl's DECL_NAME is the
   IDENTIFIER_NODE for `C', the member class template.  */
DEFTREECODE (UNBOUND_CLASS_TEMPLATE, "unbound_class_template", tcc_type, 0)

/* A using declaration.  USING_DECL_SCOPE contains the specified
   scope.  In a variadic using-declaration, this is a TYPE_PACK_EXPANSION.
   In a member using decl, unless DECL_DEPENDENT_P is true,
   USING_DECL_DECLS contains the _DECL or OVERLOAD so named.  This is
   not an alias, but is later expanded into multiple aliases.  */
DEFTREECODE (USING_DECL, "using_decl", tcc_declaration, 0)

/* A using directive. The operand is USING_STMT_NAMESPACE.  */
DEFTREECODE (USING_STMT, "using_stmt", tcc_statement, 1)

/* An un-parsed operand.  Holds a vector of input tokens and
   a vector of places where the argument was instantiated before
   parsing had occurred.  This is used for default arguments, delayed
   NSDMIs, and noexcept-specifier parsing.  */
DEFTREECODE (DEFERRED_PARSE, "deferred_parse", tcc_exceptional, 0)

/* An uninstantiated/unevaluated noexcept-specification.  For the
   uninstantiated case, DEFERRED_NOEXCEPT_PATTERN is the pattern from the
   template, and DEFERRED_NOEXCEPT_ARGS are the template arguments to
   substitute into the pattern when needed.  For the unevaluated case,
   those slots are NULL_TREE and we use get_defaulted_eh_spec to find
   the exception-specification.  */
DEFTREECODE (DEFERRED_NOEXCEPT, "deferred_noexcept", tcc_exceptional, 0)

/* A template-id, like foo<int>.  The first operand is the template.
   The second is NULL if there are no explicit arguments, or a
   TREE_VEC of arguments.  The template will be a FUNCTION_DECL,
   TEMPLATE_DECL, or an OVERLOAD.  If the template-id refers to a
   member template, the template may be an IDENTIFIER_NODE.  */
DEFTREECODE (TEMPLATE_ID_EXPR, "template_id_expr", tcc_expression, 2)

/* One of a set of overloaded functions.  */
DEFTREECODE (OVERLOAD, "overload", tcc_exceptional, 0)

/* A vector of binding slots.  */
DEFTREECODE (BINDING_VECTOR, "binding_vector", tcc_exceptional, 0)

/* A pseudo-destructor, of the form "OBJECT.~DESTRUCTOR" or
   "OBJECT.SCOPE::~DESTRUCTOR.  The first operand is the OBJECT.  The
   second operand (if non-NULL) is the SCOPE.  The third operand is
   the TYPE node corresponding to the DESTRUCTOR.  The type of the
   first operand will always be a scalar type.

   The type of a PSEUDO_DTOR_EXPR is always "void", even though it can
   be used as if it were a zero-argument function.  We handle the
   function-call case specially, and giving it "void" type prevents it
   being used in expressions in ways that are not permitted.  */
DEFTREECODE (PSEUDO_DTOR_EXPR, "pseudo_dtor_expr", tcc_expression, 3)

/* A whole bunch of tree codes for the initial, superficial parsing of
   templates.  */
DEFTREECODE (MODOP_EXPR, "modop_expr", tcc_expression, 3)
DEFTREECODE (CAST_EXPR, "cast_expr", tcc_unary, 1)
DEFTREECODE (REINTERPRET_CAST_EXPR, "reinterpret_cast_expr", tcc_unary, 1)
DEFTREECODE (CONST_CAST_EXPR, "const_cast_expr", tcc_unary, 1)
DEFTREECODE (STATIC_CAST_EXPR, "static_cast_expr", tcc_unary, 1)
DEFTREECODE (DYNAMIC_CAST_EXPR, "dynamic_cast_expr", tcc_unary, 1)
DEFTREECODE (IMPLICIT_CONV_EXPR, "implicit_conv_expr", tcc_unary, 1)
DEFTREECODE (DOTSTAR_EXPR, "dotstar_expr", tcc_expression, 2)
DEFTREECODE (TYPEID_EXPR, "typeid_expr", tcc_expression, 1)
DEFTREECODE (NOEXCEPT_EXPR, "noexcept_expr", tcc_unary, 1)
DEFTREECODE (SPACESHIP_EXPR, "spaceship_expr", tcc_expression, 2)

/* CTOR_INITIALIZER is a placeholder in template code for a call to
   setup_vtbl_pointer (and appears in all functions, not just ctors).  */
DEFTREECODE (CTOR_INITIALIZER, "ctor_initializer", tcc_expression, 1)

DEFTREECODE (TRY_BLOCK, "try_block", tcc_statement, 2)

DEFTREECODE (EH_SPEC_BLOCK, "eh_spec_block", tcc_statement, 2)

/* A HANDLER wraps a catch handler for the HANDLER_TYPE.  If this is
   CATCH_ALL_TYPE, then the handler catches all types.  The declaration of
   the catch variable is in HANDLER_PARMS, and the body block in
   HANDLER_BODY.  */
DEFTREECODE (HANDLER, "handler", tcc_statement, 2)

/* A MUST_NOT_THROW_EXPR wraps an expression that may not
   throw, and must call terminate if it does.  The second argument
   is a condition, used in templates to express noexcept (condition).  */
DEFTREECODE (MUST_NOT_THROW_EXPR, "must_not_throw_expr", tcc_expression, 2)

/* A CLEANUP_STMT marks the point at which a declaration is fully
   constructed.  The CLEANUP_EXPR is run on behalf of CLEANUP_DECL
   when CLEANUP_BODY completes.  */
DEFTREECODE (CLEANUP_STMT, "cleanup_stmt", tcc_statement, 3)

/* Represents an 'if' statement. The operands are IF_COND,
   THEN_CLAUSE, and ELSE_CLAUSE, and the current scope, respectively.  */
/* ??? It is currently still necessary to distinguish between IF_STMT
   and COND_EXPR for the benefit of templates.  */
DEFTREECODE (IF_STMT, "if_stmt", tcc_statement, 4)

/* Used to represent a range-based `for' statement. The operands are
   RANGE_FOR_DECL, RANGE_FOR_EXPR, RANGE_FOR_BODY, RANGE_FOR_SCOPE,
   RANGE_FOR_UNROLL, and RANGE_FOR_INIT_STMT, respectively.  Only used in
   templates.  */
DEFTREECODE (RANGE_FOR_STMT, "range_for_stmt", tcc_statement, 6)

/* Used to represent an expression statement.  Use `EXPR_STMT_EXPR' to
   obtain the expression.  */
DEFTREECODE (EXPR_STMT, "expr_stmt", tcc_expression, 1)

DEFTREECODE (TAG_DEFN, "tag_defn", tcc_expression, 0)

/* Represents an 'offsetof' expression during template expansion.  */
DEFTREECODE (OFFSETOF_EXPR, "offsetof_expr", tcc_expression, 2)

/* Represents an '__builtin_addressof' expression during template
   expansion.  This is similar to ADDR_EXPR, but it doesn't invoke
   overloaded & operators.  */
DEFTREECODE (ADDRESSOF_EXPR, "addressof_expr", tcc_expression, 1)

/* Represents the -> operator during template expansion.  */
DEFTREECODE (ARROW_EXPR, "arrow_expr", tcc_expression, 1)

/* Represents an '__alignof__' expression during template
   expansion.  */
DEFTREECODE (ALIGNOF_EXPR, "alignof_expr", tcc_expression, 1)

/* Represents an Objective-C++ '@encode' expression during template
   expansion.  */
DEFTREECODE (AT_ENCODE_EXPR, "at_encode_expr", tcc_expression, 1)

/* A STMT_EXPR represents a statement-expression during template
   expansion.  This is the GCC extension { ( ... ) }.  The
   STMT_EXPR_STMT is the statement given by the expression.  */
DEFTREECODE (STMT_EXPR, "stmt_expr", tcc_expression, 1)

/* Unary plus. Operand 0 is the expression to which the unary plus
   is applied.  */
DEFTREECODE (UNARY_PLUS_EXPR, "unary_plus_expr", tcc_unary, 1)

/** C++11 extensions. */

/* A static assertion.  This is a C++11 extension.
   STATIC_ASSERT_CONDITION contains the condition that is being
   checked.  STATIC_ASSERT_MESSAGE contains the message (a string
   literal) to be displayed if the condition fails to hold.  */
DEFTREECODE (STATIC_ASSERT, "static_assert", tcc_exceptional, 0)

/* Represents an argument pack of types (or templates). An argument
   pack stores zero or more arguments that will be used to instantiate
   a parameter pack.

   ARGUMENT_PACK_ARGS retrieves the arguments stored in the argument
   pack.

   Example:
     template<typename... Values>
     class tuple { ... };

     tuple<int, float, double> t;

   Values is a (template) parameter pack. When tuple<int, float,
   double> is instantiated, the Values parameter pack is instantiated
   with the argument pack <int, float, double>. ARGUMENT_PACK_ARGS will
   be a TREE_VEC containing int, float, and double.  */
DEFTREECODE (TYPE_ARGUMENT_PACK, "type_argument_pack", tcc_type, 0)

/* Represents an argument pack of values, which can be used either for
   non-type template arguments or function call arguments.

   NONTYPE_ARGUMENT_PACK plays precisely the same role as
   TYPE_ARGUMENT_PACK, but will be used for packing non-type template
   arguments (e.g., "int... Dimensions") or function arguments ("const
   Args&... args"). */
DEFTREECODE (NONTYPE_ARGUMENT_PACK, "nontype_argument_pack", tcc_expression, 1)

/* Represents a type expression that will be expanded into a list of
   types when instantiated with one or more argument packs.

   PACK_EXPANSION_PATTERN retrieves the expansion pattern. This is
   the type or expression that we will substitute into with each
   argument in an argument pack.

   PACK_EXPANSION_PARAMETER_PACKS contains a TREE_LIST of the parameter
   packs that are used in this pack expansion.

   Example:
     template<typename... Values>
     struct tied : tuple<Values&...> {
       // ...
     };

   The derivation from tuple contains a TYPE_PACK_EXPANSION for the
   template arguments. Its PACK_EXPANSION_PATTERN is "Values&" and its
   PACK_EXPANSION_PARAMETER_PACKS will contain "Values".  */
DEFTREECODE (TYPE_PACK_EXPANSION, "type_pack_expansion", tcc_type, 0)

/* Represents an expression that will be expanded into a list of
   expressions when instantiated with one or more argument packs.

   EXPR_PACK_EXPANSION plays precisely the same role as TYPE_PACK_EXPANSION,
   but will be used for expressions.  */
DEFTREECODE (EXPR_PACK_EXPANSION, "expr_pack_expansion", tcc_expression, 3)

/* Represents a pack index Ts...[I], yielding a type.  PACK_INDEX_PACK is
   the pack expansion Ts, PACK_INDEX_INDEX the index I.  */
DEFTREECODE (PACK_INDEX_TYPE, "pack_index_type", tcc_type, 0)

/* Represents a pack index Ts...[I], yielding an expression.  PACK_INDEX_PACK
   is the pack expansion Ts, PACK_INDEX_INDEX the index I.  */
DEFTREECODE (PACK_INDEX_EXPR, "pack_index_expr", tcc_expression, 2)

/* Selects the Ith parameter out of an argument pack. This node will
   be used when instantiating pack expansions; see
   tsubst_pack_expansion.

   ARGUMENT_PACK_SELECT_FROM_PACK contains the *_ARGUMENT_PACK node
   from which the argument will be selected.

   ARGUMENT_PACK_SELECT_INDEX contains the index into the argument
   pack that will be returned by this ARGUMENT_PACK_SELECT node. The
   index is a machine integer.  */
DEFTREECODE (ARGUMENT_PACK_SELECT, "argument_pack_select", tcc_exceptional, 0)

/* Fold expressions allow the expansion of a template argument pack
   over a binary operator.

   FOLD_EXPR_MODIFY_P is true when the fold operation is a compound assignment
   operator.

   FOLD_EXPR_OP is an INTEGER_CST storing the tree code for the folded
   expression.  Note that when FOLD_EXPR_MODIFY_P is true, the operator is
   a compound assignment operator for that kind of expression.

   FOLD_EXPR_PACK is an expression containing an unexpanded parameter pack;
   when expanded, each term becomes an argument of the folded expression.

   In a BINARY_FOLD_EXPRESSION, FOLD_EXPR_INIT is the non-pack argument.  */
DEFTREECODE (UNARY_LEFT_FOLD_EXPR, "unary_left_fold_expr", tcc_expression, 2)
DEFTREECODE (UNARY_RIGHT_FOLD_EXPR, "unary_right_fold_expr", tcc_expression, 2)
DEFTREECODE (BINARY_LEFT_FOLD_EXPR, "binary_left_fold_expr", tcc_expression, 3)
DEFTREECODE (BINARY_RIGHT_FOLD_EXPR, "binary_right_fold_expr", tcc_expression, 3)

/* Represents the __builtin_bit_cast (type, expr) expression.
   The type is in TREE_TYPE, expression in TREE_OPERAND (bitcast, 0).  */
DEFTREECODE (BIT_CAST_EXPR, "bit_cast_expr", tcc_expression, 1)

/** C++ extensions. */

/* Represents a templated trait that yields an expression.  */
DEFTREECODE (TRAIT_EXPR, "trait_expr", tcc_exceptional, 0)

/* Represents a templated trait that yields a type.  */
DEFTREECODE (TRAIT_TYPE, "trait_type", tcc_type, 0)

/* A lambda expression.  This is a C++0x extension.
   LAMBDA_EXPR_DEFAULT_CAPTURE_MODE is an enum for the default, which may be
   none.
   LAMBDA_EXPR_CAPTURE_LIST holds the capture-list, including `this'.
   LAMBDA_EXPR_THIS_CAPTURE goes straight to the capture of `this', if it exists.
   LAMBDA_EXPR_PENDING_PROXIES is a vector of capture proxies which need to
   be pushed once scope returns to the lambda.  */
DEFTREECODE (LAMBDA_EXPR, "lambda_expr", tcc_exceptional, 0)

/* The declared type of an expression.  This is a C++0x extension.
   DECLTYPE_TYPE_EXPR is the expression whose type we are computing.
   DECLTYPE_TYPE_ID_EXPR_OR_MEMBER_ACCESS_P states whether the
   expression was parsed as an id-expression or a member access
   expression. When false, it was parsed as a full expression.
   DECLTYPE_FOR_LAMBDA_CAPTURE is set if we want lambda capture semantics.
   DECLTYPE_FOR_LAMBDA_RETURN is set if we want lambda return deduction.  */
DEFTREECODE (DECLTYPE_TYPE, "decltype_type", tcc_type, 0)

/* A type designated by one of the bases type traits.
   BASES_TYPE is the type in question.  */
DEFTREECODE (BASES, "bases", tcc_type, 0)

/* Dependent operator expressions are given this type rather than a NULL_TREE
   type so that we have somewhere to stash the result of phase 1 name lookup
   (namely into DEPENDENT_OPERATOR_TYPE_SAVED_LOOKUPS).  */
DEFTREECODE (DEPENDENT_OPERATOR_TYPE, "dependent_operator_type", tcc_type, 0)

/* Used to represent the template information stored by template
   specializations.
   The accessors are:
   TI_TEMPLATE the template declaration associated to the specialization
   TI_ARGS the arguments of the template specialization
   TI_TYPEDEFS_NEEDING_ACCESS_CHECKING the vector of typedefs used in
   the pattern of the template for which access check is needed at template
   instantiation time.  */
DEFTREECODE (TEMPLATE_INFO, "template_info", tcc_exceptional, 0)

/* OpenMP - #pragma omp depobj
   Operand 0: OMP_DEPOBJ_DEPOBJ: Depobj expression
   Operand 1: OMP_DEPOBJ_CLAUSES: List of clauses.  */
DEFTREECODE (OMP_DEPOBJ, "omp_depobj", tcc_statement, 2)

/* Extensions for Concepts. */

/* Used to represent information associated with constrained declarations. */
DEFTREECODE (CONSTRAINT_INFO, "constraint_info", tcc_exceptional, 0)

/* A requires-expr has three operands. The first operand is
   its parameter list (possibly NULL). The second is a list of
   requirements, which are denoted by the _REQ* tree codes
   below.  The third is a TREE_VEC of template arguments to
   be applied when substituting into the parameter list and
   requirements, set by tsubst_requires_expr for partial instantiations.  */
DEFTREECODE (REQUIRES_EXPR,   "requires_expr", tcc_expression, 3)

/* A requirement for an expression. */
DEFTREECODE (SIMPLE_REQ, "simple_req", tcc_expression, 1)

/* A requirement for a type. */
DEFTREECODE (TYPE_REQ, "type_req", tcc_expression, 1)

/* A requirement for an expression and its properties. The
   first operand is the expression, and the 2nd is its type.
   The accessor COMPOUND_REQ_NOEXCEPT determines whether
   the noexcept keyword was present. */
DEFTREECODE (COMPOUND_REQ, "compound_req", tcc_expression, 2)

/* A requires clause within a requires expression. */
DEFTREECODE (NESTED_REQ, "nested_req", tcc_expression, 1)

/* Constraints are modeled as kinds of expressions.
   The operands of a constraint can be either types or expressions.
   Unlike expressions, constraints do not have a type.  */

/* An atomic constraint evaluates an expression E. The operand of the
   constraint is its parameter mapping.  The actual expression is stored
   in the context.

   CONSTR_INFO provides source info to support diagnostics.
   ATOMIC_CONSTR_EXPR has the expression to be evaluated.
   ATOMIC_CONSTR_MAP is the parameter mapping for the atomic constraint
   and is stored in the type field.  */
DEFTREECODE (ATOMIC_CONSTR, "atomic_constr", tcc_expression, 1)

/* The conjunction and disjunction of two constraints, respectively.
   Operands are accessed using TREE_OPERAND.

   CONSTR_INFO provides access to the source information of constraints,
   which is stored in the TREE_TYPE.  */
DEFTREECODE (CONJ_CONSTR, "conj_constr", tcc_expression, 2)
DEFTREECODE (DISJ_CONSTR, "disj_constr", tcc_expression, 2)

/* The co_await expression is used to support coroutines.

  Op 0 is the cast expresssion (potentially modified by the
  promise "await_transform()" method).
  Op1 is a proxy for the temp / coro frame slot 'e' value.
  Op2 is the initialiser for Op1 (Op0, potentially modified by any
  applicable 'co_await' operator).
  Op3 is a vector of the [0] e.ready, [1] e.suspend and [2] e.resume calls.
  Op4 is a mode : 0 (await) 1 (yield) 2 (initial) 3 (final) */
DEFTREECODE (CO_AWAIT_EXPR, "co_await", tcc_expression, 5)

/* The co_yield expression is used to support coroutines.

   Op0 is the original expr (for use in diagnostics)
   Op2 is the co_await derived from this. */
DEFTREECODE (CO_YIELD_EXPR, "co_yield", tcc_expression, 2)

/* The co_return expression is used to support coroutines.

   Op0 is the original expr, can be void (for use in diagnostics)
   Op1 is the promise return_xxxx call for the expression given. */

DEFTREECODE (CO_RETURN_EXPR, "co_return", tcc_statement, 2)

/* Different flavors of contracts.

   Assertions and preconditions have two operands: a node containing
   the their mode and condition. Postconditions have an additional
   operand to store the optional name for the result value.

   CONTRACT_SEMANTIC has the computed behavior of the contract.  */
DEFTREECODE (ASSERTION_STMT, "assertion_stmt", tcc_statement, 3)
DEFTREECODE (PRECONDITION_STMT, "precondition_stmt", tcc_statement, 3)
DEFTREECODE (POSTCONDITION_STMT, "postcondition_stmt", tcc_statement, 4)

/* A reference to a translation-unit local entity.

   This is emitted by modules streaming when writing a TU-local entity that
   wasn't an exposure (e.g. in a non-inline function template).  */
DEFTREECODE (TU_LOCAL_ENTITY, "tu_local_entity", tcc_exceptional, 0)

/*
Local variables:
mode:c
End:
*/
