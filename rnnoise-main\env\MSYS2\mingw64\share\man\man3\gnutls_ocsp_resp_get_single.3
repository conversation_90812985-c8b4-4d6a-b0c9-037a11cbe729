.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_get_single" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_get_single \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_get_single(gnutls_ocsp_resp_const_t " resp ", unsigned " indx ", gnutls_digest_algorithm_t * " digest ", gnutls_datum_t * " issuer_name_hash ", gnutls_datum_t * " issuer_key_hash ", gnutls_datum_t * " serial_number ", unsigned int * " cert_status ", time_t * " this_update ", time_t * " next_update ", time_t * " revocation_time ", unsigned int * " revocation_reason ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
should contain a \fBgnutls_ocsp_resp_t\fP type
.IP "unsigned indx" 12
Specifies response number to get. Use (0) to get the first one.
.IP "gnutls_digest_algorithm_t * digest" 12
output variable with \fBgnutls_digest_algorithm_t\fP hash algorithm
.IP "gnutls_datum_t * issuer_name_hash" 12
output buffer with hash of issuer's DN
.IP "gnutls_datum_t * issuer_key_hash" 12
output buffer with hash of issuer's public key
.IP "gnutls_datum_t * serial_number" 12
output buffer with serial number of certificate to check
.IP "unsigned int * cert_status" 12
a certificate status, a \fBgnutls_ocsp_cert_status_t\fP enum.
.IP "time_t * this_update" 12
time at which the status is known to be correct.
.IP "time_t * next_update" 12
when newer information will be available, or (time_t)\-1 if unspecified
.IP "time_t * revocation_time" 12
when  \fIcert_status\fP is \fBGNUTLS_OCSP_CERT_REVOKED\fP, holds time of revocation.
.IP "unsigned int * revocation_reason" 12
revocation reason, a \fBgnutls_x509_crl_reason_t\fP enum.
.SH "DESCRIPTION"
This function will return the certificate information of the
 \fIindx\fP 'ed response in the Basic OCSP Response  \fIresp\fP .  The
information returned corresponds to the OCSP SingleResponse structure
except the final singleExtensions.

Each of the pointers to output variables may be NULL to indicate
that the caller is not interested in that value.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code is returned.  If you have reached the last
CertID available \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be
returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
