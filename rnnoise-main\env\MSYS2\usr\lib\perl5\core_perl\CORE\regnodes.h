/* -*- mode: C; buffer-read-only: t -*-
   !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
   This file is built by regen/regcomp.pl from regcomp.sym, op_reg_common.h
   and regexp.h.
   Any changes made here will be lost!
 */

#if defined(PERL_CORE) || defined(PERL_EXT_RE_BUILD)


/* typedefs for regex nodes - one typedef per node type */

typedef struct regnode_2                         tregnode_ACCEPT;
typedef struct regnode_1                         tregnode_AHOCORASICK;
typedef struct regnode_charclass                 tregnode_AHOCORASICKC;
typedef struct regnode_charclass                 tregnode_ANYOF;
typedef struct regnode_charclass                 tregnode_ANYOFD;
typedef struct regnode_1                         tregnode_ANYOFH;
typedef struct regnode_1                         tregnode_ANYOFHb;
typedef struct regnode_bbm                       tregnode_ANYOFHbbm;
typedef struct regnode_1                         tregnode_ANYOFHr;
typedef struct regnode_1                         tregnode_ANYOFHs;
typedef struct regnode_charclass                 tregnode_ANYOFL;
typedef struct regnode_1                         tregnode_ANYOFM;
typedef struct regnode_charclass_posixl          tregnode_ANYOFPOSIXL;
typedef struct regnode_1                         tregnode_ANYOFR;
typedef struct regnode_1                         tregnode_ANYOFRb;
typedef struct regnode                           tregnode_BOUND;
typedef struct regnode                           tregnode_BOUNDA;
typedef struct regnode                           tregnode_BOUNDL;
typedef struct regnode                           tregnode_BOUNDU;
typedef struct regnode_1                         tregnode_BRANCH;
typedef struct regnode_2                         tregnode_BRANCHJ;
typedef struct regnode_1                         tregnode_CLOSE;
typedef struct regnode                           tregnode_CLUMP;
typedef struct regnode_1                         tregnode_COMMIT;
typedef struct regnode_3                         tregnode_CURLY;
typedef struct regnode_3                         tregnode_CURLYM;
typedef struct regnode_3                         tregnode_CURLYN;
typedef struct regnode_3                         tregnode_CURLYX;
typedef struct regnode_1                         tregnode_CUTGROUP;
typedef struct regnode_1                         tregnode_DEFINEP;
typedef struct regnode                           tregnode_END;
typedef struct regnode                           tregnode_ENDLIKE;
typedef struct regnode                           tregnode_EOS;
typedef struct regnode_2                         tregnode_EVAL;
typedef struct regnode                           tregnode_EXACT;
typedef struct regnode                           tregnode_EXACTF;
typedef struct regnode                           tregnode_EXACTFAA;
typedef struct regnode                           tregnode_EXACTFAA_NO_TRIE;
typedef struct regnode                           tregnode_EXACTFL;
typedef struct regnode                           tregnode_EXACTFLU8;
typedef struct regnode                           tregnode_EXACTFU;
typedef struct regnode                           tregnode_EXACTFUP;
typedef struct regnode                           tregnode_EXACTFU_REQ8;
typedef struct regnode                           tregnode_EXACTFU_S_EDGE;
typedef struct regnode                           tregnode_EXACTL;
typedef struct regnode                           tregnode_EXACT_REQ8;
typedef struct regnode_2                         tregnode_GOSUB;
typedef struct regnode                           tregnode_GPOS;
typedef struct regnode_1                         tregnode_GROUPP;
typedef struct regnode_1                         tregnode_GROUPPN;
typedef struct regnode_1                         tregnode_IFMATCH;
typedef struct regnode_1                         tregnode_IFTHEN;
typedef struct regnode_1                         tregnode_INSUBP;
typedef struct regnode                           tregnode_KEEPS;
typedef struct regnode_1                         tregnode_LEXACT;
typedef struct regnode_1                         tregnode_LEXACT_REQ8;
typedef struct regnode                           tregnode_LNBREAK;
typedef struct regnode                           tregnode_LOGICAL;
typedef struct regnode_1                         tregnode_LONGJMP;
typedef struct regnode                           tregnode_LOOKBEHIND_END;
typedef struct regnode_1                         tregnode_MARKPOINT;
typedef struct regnode                           tregnode_MBOL;
typedef struct regnode                           tregnode_MEOL;
typedef struct regnode                           tregnode_MINMOD;
typedef struct regnode_1                         tregnode_NANYOFM;
typedef struct regnode                           tregnode_NBOUND;
typedef struct regnode                           tregnode_NBOUNDA;
typedef struct regnode                           tregnode_NBOUNDL;
typedef struct regnode                           tregnode_NBOUNDU;
typedef struct regnode                           tregnode_NOTHING;
typedef struct regnode                           tregnode_NPOSIXA;
typedef struct regnode                           tregnode_NPOSIXD;
typedef struct regnode                           tregnode_NPOSIXL;
typedef struct regnode                           tregnode_NPOSIXU;
typedef struct regnode_1                         tregnode_OPEN;
typedef struct regnode_1                         tregnode_OPFAIL;
typedef struct regnode                           tregnode_OPTIMIZED;
typedef struct regnode                           tregnode_PLUS;
typedef struct regnode                           tregnode_POSIXA;
typedef struct regnode                           tregnode_POSIXD;
typedef struct regnode                           tregnode_POSIXL;
typedef struct regnode                           tregnode_POSIXU;
typedef struct regnode_1                         tregnode_PRUNE;
typedef struct regnode                           tregnode_PSEUDO;
typedef struct regnode_2                         tregnode_REF;
typedef struct regnode_2                         tregnode_REFF;
typedef struct regnode_2                         tregnode_REFFA;
typedef struct regnode_2                         tregnode_REFFAN;
typedef struct regnode_2                         tregnode_REFFL;
typedef struct regnode_2                         tregnode_REFFLN;
typedef struct regnode_2                         tregnode_REFFN;
typedef struct regnode_2                         tregnode_REFFU;
typedef struct regnode_2                         tregnode_REFFUN;
typedef struct regnode_2                         tregnode_REFN;
typedef struct regnode_p                         tregnode_REGEX_SET;
typedef struct regnode                           tregnode_REG_ANY;
typedef struct regnode_1                         tregnode_RENUM;
typedef struct regnode                           tregnode_SANY;
typedef struct regnode                           tregnode_SBOL;
typedef struct regnode                           tregnode_SEOL;
typedef struct regnode_1                         tregnode_SKIP;
typedef struct regnode                           tregnode_SRCLOSE;
typedef struct regnode                           tregnode_SROPEN;
typedef struct regnode                           tregnode_STAR;
typedef struct regnode                           tregnode_SUCCEED;
typedef struct regnode_1                         tregnode_SUSPEND;
typedef struct regnode                           tregnode_TAIL;
typedef struct regnode_1                         tregnode_TRIE;
typedef struct regnode_charclass                 tregnode_TRIEC;
typedef struct regnode_1                         tregnode_UNLESSM;
typedef struct regnode_1                         tregnode_VERB;
typedef struct regnode                           tregnode_WHILEM;

/* end typedefs */

/* Regops and State definitions */

#define REGNODE_MAX           	111
#define REGMATCH_STATE_MAX    	153

/* -- For regexec.c to switch on target being utf8 (t8) or not (tb, b='byte'); */
#define with_t_UTF8ness(op, t_utf8) (((op) << 1) + (cBOOL(t_utf8)))
/* -- same, but also with pattern (p8, pb) -- */
#define with_tp_UTF8ness(op, t_utf8, p_utf8)                        \
		(((op) << 2) + (cBOOL(t_utf8) << 1) + cBOOL(p_utf8))

/* The #defines below give both the basic regnode and the expanded version for
   switching on utf8ness */
#define END                           0        /* 0x00 End of program. */
#define END_tb                           0     /*      0x000 */
#define END_t8                           1     /*      0x001 */
#define END_tb_pb                           0  /*      0x000 */
#define END_tb_p8                           1  /*      0x001 */
#define END_t8_pb                           2  /*      0x002 */
#define END_t8_p8                           3  /*      0x003 */

#define SUCCEED                       1        /* 0x01 Return from a
                                                  subroutine, basically. */
#define SUCCEED_tb                       2     /*      0x002 */
#define SUCCEED_t8                       3     /*      0x003 */
#define SUCCEED_tb_pb                       4  /*      0x004 */
#define SUCCEED_tb_p8                       5  /*      0x005 */
#define SUCCEED_t8_pb                       6  /*      0x006 */
#define SUCCEED_t8_p8                       7  /*      0x007 */

#define SBOL                          2        /* 0x02 Match "" at beginning
                                                  of line: /^/, /\A/ */
#define SBOL_tb                          4     /*      0x004 */
#define SBOL_t8                          5     /*      0x005 */
#define SBOL_tb_pb                          8  /*      0x008 */
#define SBOL_tb_p8                          9  /*      0x009 */
#define SBOL_t8_pb                         10  /*      0x00a */
#define SBOL_t8_p8                         11  /*      0x00b */

#define BOL                           2        /* 0x02 Match "" at beginning
                                                  of line: /^/, /\A/ */
#define BOL_tb                           4     /*      0x004 */
#define BOL_t8                           5     /*      0x005 */
#define BOL_tb_pb                           8  /*      0x008 */
#define BOL_tb_p8                           9  /*      0x009 */
#define BOL_t8_pb                          10  /*      0x00a */
#define BOL_t8_p8                          11  /*      0x00b */

#define MBOL                          3        /* 0x03 Same, assuming
                                                  multiline: /^/m */
#define MBOL_tb                          6     /*      0x006 */
#define MBOL_t8                          7     /*      0x007 */
#define MBOL_tb_pb                         12  /*      0x00c */
#define MBOL_tb_p8                         13  /*      0x00d */
#define MBOL_t8_pb                         14  /*      0x00e */
#define MBOL_t8_p8                         15  /*      0x00f */

#define SEOL                          4        /* 0x04 Match "" at end of
                                                  line: /$/ */
#define SEOL_tb                          8     /*      0x008 */
#define SEOL_t8                          9     /*      0x009 */
#define SEOL_tb_pb                         16  /*      0x010 */
#define SEOL_tb_p8                         17  /*      0x011 */
#define SEOL_t8_pb                         18  /*      0x012 */
#define SEOL_t8_p8                         19  /*      0x013 */

#define EOL                           4        /* 0x04 Match "" at end of
                                                  line: /$/ */
#define EOL_tb                           8     /*      0x008 */
#define EOL_t8                           9     /*      0x009 */
#define EOL_tb_pb                          16  /*      0x010 */
#define EOL_tb_p8                          17  /*      0x011 */
#define EOL_t8_pb                          18  /*      0x012 */
#define EOL_t8_p8                          19  /*      0x013 */

#define MEOL                          5        /* 0x05 Same, assuming
                                                  multiline: /$/m */
#define MEOL_tb                         10     /*      0x00a */
#define MEOL_t8                         11     /*      0x00b */
#define MEOL_tb_pb                         20  /*      0x014 */
#define MEOL_tb_p8                         21  /*      0x015 */
#define MEOL_t8_pb                         22  /*      0x016 */
#define MEOL_t8_p8                         23  /*      0x017 */

#define EOS                           6        /* 0x06 Match "" at end of
                                                  string: /\z/ */
#define EOS_tb                          12     /*      0x00c */
#define EOS_t8                          13     /*      0x00d */
#define EOS_tb_pb                          24  /*      0x018 */
#define EOS_tb_p8                          25  /*      0x019 */
#define EOS_t8_pb                          26  /*      0x01a */
#define EOS_t8_p8                          27  /*      0x01b */

#define GPOS                          7        /* 0x07 Matches where last m//g
                                                  left off. */
#define GPOS_tb                         14     /*      0x00e */
#define GPOS_t8                         15     /*      0x00f */
#define GPOS_tb_pb                         28  /*      0x01c */
#define GPOS_tb_p8                         29  /*      0x01d */
#define GPOS_t8_pb                         30  /*      0x01e */
#define GPOS_t8_p8                         31  /*      0x01f */

#define BOUND                         8        /* 0x08 Like BOUNDA for
                                                  non-utf8, otherwise like
                                                  BOUNDU */
#define BOUND_tb                        16     /*      0x010 */
#define BOUND_t8                        17     /*      0x011 */
#define BOUND_tb_pb                        32  /*      0x020 */
#define BOUND_tb_p8                        33  /*      0x021 */
#define BOUND_t8_pb                        34  /*      0x022 */
#define BOUND_t8_p8                        35  /*      0x023 */

#define BOUNDL                        9        /* 0x09 Like BOUND/BOUNDU, but
                                                  \w and \W are defined by
                                                  current locale */
#define BOUNDL_tb                       18     /*      0x012 */
#define BOUNDL_t8                       19     /*      0x013 */
#define BOUNDL_tb_pb                       36  /*      0x024 */
#define BOUNDL_tb_p8                       37  /*      0x025 */
#define BOUNDL_t8_pb                       38  /*      0x026 */
#define BOUNDL_t8_p8                       39  /*      0x027 */

#define BOUNDU                       10        /* 0x0a Match "" at any
                                                  boundary of a given type
                                                  using /u rules. */
#define BOUNDU_tb                       20     /*      0x014 */
#define BOUNDU_t8                       21     /*      0x015 */
#define BOUNDU_tb_pb                       40  /*      0x028 */
#define BOUNDU_tb_p8                       41  /*      0x029 */
#define BOUNDU_t8_pb                       42  /*      0x02a */
#define BOUNDU_t8_p8                       43  /*      0x02b */

#define BOUNDA                       11        /* 0x0b Match "" at any
                                                  boundary between \w\W or
                                                  \W\w, where \w is
                                                  [_a-zA-Z0-9] */
#define BOUNDA_tb                       22     /*      0x016 */
#define BOUNDA_t8                       23     /*      0x017 */
#define BOUNDA_tb_pb                       44  /*      0x02c */
#define BOUNDA_tb_p8                       45  /*      0x02d */
#define BOUNDA_t8_pb                       46  /*      0x02e */
#define BOUNDA_t8_p8                       47  /*      0x02f */

#define NBOUND                       12        /* 0x0c Like NBOUNDA for
                                                  non-utf8, otherwise like
                                                  BOUNDU */
#define NBOUND_tb                       24     /*      0x018 */
#define NBOUND_t8                       25     /*      0x019 */
#define NBOUND_tb_pb                       48  /*      0x030 */
#define NBOUND_tb_p8                       49  /*      0x031 */
#define NBOUND_t8_pb                       50  /*      0x032 */
#define NBOUND_t8_p8                       51  /*      0x033 */

#define NBOUNDL                      13        /* 0x0d Like NBOUND/NBOUNDU,
                                                  but \w and \W are defined by
                                                  current locale */
#define NBOUNDL_tb                      26     /*      0x01a */
#define NBOUNDL_t8                      27     /*      0x01b */
#define NBOUNDL_tb_pb                      52  /*      0x034 */
#define NBOUNDL_tb_p8                      53  /*      0x035 */
#define NBOUNDL_t8_pb                      54  /*      0x036 */
#define NBOUNDL_t8_p8                      55  /*      0x037 */

#define NBOUNDU                      14        /* 0x0e Match "" at any
                                                  non-boundary of a given type
                                                  using using /u rules. */
#define NBOUNDU_tb                      28     /*      0x01c */
#define NBOUNDU_t8                      29     /*      0x01d */
#define NBOUNDU_tb_pb                      56  /*      0x038 */
#define NBOUNDU_tb_p8                      57  /*      0x039 */
#define NBOUNDU_t8_pb                      58  /*      0x03a */
#define NBOUNDU_t8_p8                      59  /*      0x03b */

#define NBOUNDA                      15        /* 0x0f Match "" betweeen any
                                                  \w\w or \W\W, where \w is
                                                  [_a-zA-Z0-9] */
#define NBOUNDA_tb                      30     /*      0x01e */
#define NBOUNDA_t8                      31     /*      0x01f */
#define NBOUNDA_tb_pb                      60  /*      0x03c */
#define NBOUNDA_tb_p8                      61  /*      0x03d */
#define NBOUNDA_t8_pb                      62  /*      0x03e */
#define NBOUNDA_t8_p8                      63  /*      0x03f */

#define REG_ANY                      16        /* 0x10 Match any one character
                                                  (except newline). */
#define REG_ANY_tb                      32     /*      0x020 */
#define REG_ANY_t8                      33     /*      0x021 */
#define REG_ANY_tb_pb                      64  /*      0x040 */
#define REG_ANY_tb_p8                      65  /*      0x041 */
#define REG_ANY_t8_pb                      66  /*      0x042 */
#define REG_ANY_t8_p8                      67  /*      0x043 */

#define SANY                         17        /* 0x11 Match any one
                                                  character. */
#define SANY_tb                         34     /*      0x022 */
#define SANY_t8                         35     /*      0x023 */
#define SANY_tb_pb                         68  /*      0x044 */
#define SANY_tb_p8                         69  /*      0x045 */
#define SANY_t8_pb                         70  /*      0x046 */
#define SANY_t8_p8                         71  /*      0x047 */

#define ANYOF                        18        /* 0x12 Match character in (or
                                                  not in) this class, single
                                                  char match only */
#define ANYOF_tb                        36     /*      0x024 */
#define ANYOF_t8                        37     /*      0x025 */
#define ANYOF_tb_pb                        72  /*      0x048 */
#define ANYOF_tb_p8                        73  /*      0x049 */
#define ANYOF_t8_pb                        74  /*      0x04a */
#define ANYOF_t8_p8                        75  /*      0x04b */

#define ANYOFD                       19        /* 0x13 Like ANYOF, but /d is
                                                  in effect */
#define ANYOFD_tb                       38     /*      0x026 */
#define ANYOFD_t8                       39     /*      0x027 */
#define ANYOFD_tb_pb                       76  /*      0x04c */
#define ANYOFD_tb_p8                       77  /*      0x04d */
#define ANYOFD_t8_pb                       78  /*      0x04e */
#define ANYOFD_t8_p8                       79  /*      0x04f */

#define ANYOFL                       20        /* 0x14 Like ANYOF, but /l is
                                                  in effect */
#define ANYOFL_tb                       40     /*      0x028 */
#define ANYOFL_t8                       41     /*      0x029 */
#define ANYOFL_tb_pb                       80  /*      0x050 */
#define ANYOFL_tb_p8                       81  /*      0x051 */
#define ANYOFL_t8_pb                       82  /*      0x052 */
#define ANYOFL_t8_p8                       83  /*      0x053 */

#define ANYOFPOSIXL                  21        /* 0x15 Like ANYOFL, but
                                                  matches [[:posix:]] classes
                                               */
#define ANYOFPOSIXL_tb                  42     /*      0x02a */
#define ANYOFPOSIXL_t8                  43     /*      0x02b */
#define ANYOFPOSIXL_tb_pb                  84  /*      0x054 */
#define ANYOFPOSIXL_tb_p8                  85  /*      0x055 */
#define ANYOFPOSIXL_t8_pb                  86  /*      0x056 */
#define ANYOFPOSIXL_t8_p8                  87  /*      0x057 */

#define ANYOFH                       22        /* 0x16 Like ANYOF, but only
                                                  has "High" matches, none in
                                                  the bitmap; the flags field
                                                  contains the lowest
                                                  matchable UTF-8 start byte
                                               */
#define ANYOFH_tb                       44     /*      0x02c */
#define ANYOFH_t8                       45     /*      0x02d */
#define ANYOFH_tb_pb                       88  /*      0x058 */
#define ANYOFH_tb_p8                       89  /*      0x059 */
#define ANYOFH_t8_pb                       90  /*      0x05a */
#define ANYOFH_t8_p8                       91  /*      0x05b */

#define ANYOFHb                      23        /* 0x17 Like ANYOFH, but all
                                                  matches share the same UTF-8
                                                  start byte, given in the
                                                  flags field */
#define ANYOFHb_tb                      46     /*      0x02e */
#define ANYOFHb_t8                      47     /*      0x02f */
#define ANYOFHb_tb_pb                      92  /*      0x05c */
#define ANYOFHb_tb_p8                      93  /*      0x05d */
#define ANYOFHb_t8_pb                      94  /*      0x05e */
#define ANYOFHb_t8_p8                      95  /*      0x05f */

#define ANYOFHr                      24        /* 0x18 Like ANYOFH, but the
                                                  flags field contains packed
                                                  bounds for all matchable
                                                  UTF-8 start bytes. */
#define ANYOFHr_tb                      48     /*      0x030 */
#define ANYOFHr_t8                      49     /*      0x031 */
#define ANYOFHr_tb_pb                      96  /*      0x060 */
#define ANYOFHr_tb_p8                      97  /*      0x061 */
#define ANYOFHr_t8_pb                      98  /*      0x062 */
#define ANYOFHr_t8_p8                      99  /*      0x063 */

#define ANYOFHs                      25        /* 0x19 Like ANYOFHb, but has a
                                                  string field that gives the
                                                  leading matchable UTF-8
                                                  bytes; flags field is len */
#define ANYOFHs_tb                      50     /*      0x032 */
#define ANYOFHs_t8                      51     /*      0x033 */
#define ANYOFHs_tb_pb                     100  /*      0x064 */
#define ANYOFHs_tb_p8                     101  /*      0x065 */
#define ANYOFHs_t8_pb                     102  /*      0x066 */
#define ANYOFHs_t8_p8                     103  /*      0x067 */

#define ANYOFR                       26        /* 0x1a Matches any character
                                                  in the range given by its
                                                  packed args: upper 12 bits
                                                  is the max delta from the
                                                  base lower 20; the flags
                                                  field contains the lowest
                                                  matchable UTF-8 start byte
                                               */
#define ANYOFR_tb                       52     /*      0x034 */
#define ANYOFR_t8                       53     /*      0x035 */
#define ANYOFR_tb_pb                      104  /*      0x068 */
#define ANYOFR_tb_p8                      105  /*      0x069 */
#define ANYOFR_t8_pb                      106  /*      0x06a */
#define ANYOFR_t8_p8                      107  /*      0x06b */

#define ANYOFRb                      27        /* 0x1b Like ANYOFR, but all
                                                  matches share the same UTF-8
                                                  start byte, given in the
                                                  flags field */
#define ANYOFRb_tb                      54     /*      0x036 */
#define ANYOFRb_t8                      55     /*      0x037 */
#define ANYOFRb_tb_pb                     108  /*      0x06c */
#define ANYOFRb_tb_p8                     109  /*      0x06d */
#define ANYOFRb_t8_pb                     110  /*      0x06e */
#define ANYOFRb_t8_p8                     111  /*      0x06f */

#define ANYOFHbbm                    28        /* 0x1c Like ANYOFHb, but only
                                                  for 2-byte UTF-8 characters;
                                                  uses a bitmap to match the
                                                  continuation byte */
#define ANYOFHbbm_tb                    56     /*      0x038 */
#define ANYOFHbbm_t8                    57     /*      0x039 */
#define ANYOFHbbm_tb_pb                   112  /*      0x070 */
#define ANYOFHbbm_tb_p8                   113  /*      0x071 */
#define ANYOFHbbm_t8_pb                   114  /*      0x072 */
#define ANYOFHbbm_t8_p8                   115  /*      0x073 */

#define ANYOFM                       29        /* 0x1d Like ANYOF, but matches
                                                  an invariant byte as
                                                  determined by the mask and
                                                  arg */
#define ANYOFM_tb                       58     /*      0x03a */
#define ANYOFM_t8                       59     /*      0x03b */
#define ANYOFM_tb_pb                      116  /*      0x074 */
#define ANYOFM_tb_p8                      117  /*      0x075 */
#define ANYOFM_t8_pb                      118  /*      0x076 */
#define ANYOFM_t8_p8                      119  /*      0x077 */

#define NANYOFM                      30        /* 0x1e complement of ANYOFM */
#define NANYOFM_tb                      60     /*      0x03c */
#define NANYOFM_t8                      61     /*      0x03d */
#define NANYOFM_tb_pb                     120  /*      0x078 */
#define NANYOFM_tb_p8                     121  /*      0x079 */
#define NANYOFM_t8_pb                     122  /*      0x07a */
#define NANYOFM_t8_p8                     123  /*      0x07b */

#define POSIXD                       31        /* 0x1f Some [[:class:]] under
                                                  /d; the FLAGS field gives
                                                  which one */
#define POSIXD_tb                       62     /*      0x03e */
#define POSIXD_t8                       63     /*      0x03f */
#define POSIXD_tb_pb                      124  /*      0x07c */
#define POSIXD_tb_p8                      125  /*      0x07d */
#define POSIXD_t8_pb                      126  /*      0x07e */
#define POSIXD_t8_p8                      127  /*      0x07f */

#define POSIXL                       32        /* 0x20 Some [[:class:]] under
                                                  /l; the FLAGS field gives
                                                  which one */
#define POSIXL_tb                       64     /*      0x040 */
#define POSIXL_t8                       65     /*      0x041 */
#define POSIXL_tb_pb                      128  /*      0x080 */
#define POSIXL_tb_p8                      129  /*      0x081 */
#define POSIXL_t8_pb                      130  /*      0x082 */
#define POSIXL_t8_p8                      131  /*      0x083 */

#define POSIXU                       33        /* 0x21 Some [[:class:]] under
                                                  /u; the FLAGS field gives
                                                  which one */
#define POSIXU_tb                       66     /*      0x042 */
#define POSIXU_t8                       67     /*      0x043 */
#define POSIXU_tb_pb                      132  /*      0x084 */
#define POSIXU_tb_p8                      133  /*      0x085 */
#define POSIXU_t8_pb                      134  /*      0x086 */
#define POSIXU_t8_p8                      135  /*      0x087 */

#define POSIXA                       34        /* 0x22 Some [[:class:]] under
                                                  /a; the FLAGS field gives
                                                  which one */
#define POSIXA_tb                       68     /*      0x044 */
#define POSIXA_t8                       69     /*      0x045 */
#define POSIXA_tb_pb                      136  /*      0x088 */
#define POSIXA_tb_p8                      137  /*      0x089 */
#define POSIXA_t8_pb                      138  /*      0x08a */
#define POSIXA_t8_p8                      139  /*      0x08b */

#define NPOSIXD                      35        /* 0x23 complement of POSIXD,
                                                  [[:^class:]] */
#define NPOSIXD_tb                      70     /*      0x046 */
#define NPOSIXD_t8                      71     /*      0x047 */
#define NPOSIXD_tb_pb                     140  /*      0x08c */
#define NPOSIXD_tb_p8                     141  /*      0x08d */
#define NPOSIXD_t8_pb                     142  /*      0x08e */
#define NPOSIXD_t8_p8                     143  /*      0x08f */

#define NPOSIXL                      36        /* 0x24 complement of POSIXL,
                                                  [[:^class:]] */
#define NPOSIXL_tb                      72     /*      0x048 */
#define NPOSIXL_t8                      73     /*      0x049 */
#define NPOSIXL_tb_pb                     144  /*      0x090 */
#define NPOSIXL_tb_p8                     145  /*      0x091 */
#define NPOSIXL_t8_pb                     146  /*      0x092 */
#define NPOSIXL_t8_p8                     147  /*      0x093 */

#define NPOSIXU                      37        /* 0x25 complement of POSIXU,
                                                  [[:^class:]] */
#define NPOSIXU_tb                      74     /*      0x04a */
#define NPOSIXU_t8                      75     /*      0x04b */
#define NPOSIXU_tb_pb                     148  /*      0x094 */
#define NPOSIXU_tb_p8                     149  /*      0x095 */
#define NPOSIXU_t8_pb                     150  /*      0x096 */
#define NPOSIXU_t8_p8                     151  /*      0x097 */

#define NPOSIXA                      38        /* 0x26 complement of POSIXA,
                                                  [[:^class:]] */
#define NPOSIXA_tb                      76     /*      0x04c */
#define NPOSIXA_t8                      77     /*      0x04d */
#define NPOSIXA_tb_pb                     152  /*      0x098 */
#define NPOSIXA_tb_p8                     153  /*      0x099 */
#define NPOSIXA_t8_pb                     154  /*      0x09a */
#define NPOSIXA_t8_p8                     155  /*      0x09b */

#define CLUMP                        39        /* 0x27 Match any extended
                                                  grapheme cluster sequence */
#define CLUMP_tb                        78     /*      0x04e */
#define CLUMP_t8                        79     /*      0x04f */
#define CLUMP_tb_pb                       156  /*      0x09c */
#define CLUMP_tb_p8                       157  /*      0x09d */
#define CLUMP_t8_pb                       158  /*      0x09e */
#define CLUMP_t8_p8                       159  /*      0x09f */

#define BRANCH                       40        /* 0x28 Match this alternative,
                                                  or the next... */
#define BRANCH_tb                       80     /*      0x050 */
#define BRANCH_t8                       81     /*      0x051 */
#define BRANCH_tb_pb                      160  /*      0x0a0 */
#define BRANCH_tb_p8                      161  /*      0x0a1 */
#define BRANCH_t8_pb                      162  /*      0x0a2 */
#define BRANCH_t8_p8                      163  /*      0x0a3 */

#define EXACT                        41        /* 0x29 Match this string
                                                  (flags field is the length).
                                               */
#define EXACT_tb                        82     /*      0x052 */
#define EXACT_t8                        83     /*      0x053 */
#define EXACT_tb_pb                       164  /*      0x0a4 */
#define EXACT_tb_p8                       165  /*      0x0a5 */
#define EXACT_t8_pb                       166  /*      0x0a6 */
#define EXACT_t8_p8                       167  /*      0x0a7 */

#define LEXACT                       42        /* 0x2a Match this long string
                                                  (preceded by length; flags
                                                  unused). */
#define LEXACT_tb                       84     /*      0x054 */
#define LEXACT_t8                       85     /*      0x055 */
#define LEXACT_tb_pb                      168  /*      0x0a8 */
#define LEXACT_tb_p8                      169  /*      0x0a9 */
#define LEXACT_t8_pb                      170  /*      0x0aa */
#define LEXACT_t8_p8                      171  /*      0x0ab */

#define EXACTL                       43        /* 0x2b Like EXACT, but /l is
                                                  in effect (used so
                                                  locale-related warnings can
                                                  be checked for) */
#define EXACTL_tb                       86     /*      0x056 */
#define EXACTL_t8                       87     /*      0x057 */
#define EXACTL_tb_pb                      172  /*      0x0ac */
#define EXACTL_tb_p8                      173  /*      0x0ad */
#define EXACTL_t8_pb                      174  /*      0x0ae */
#define EXACTL_t8_p8                      175  /*      0x0af */

#define EXACTF                       44        /* 0x2c Like EXACT, but match
                                                  using /id rules; (string not
                                                  UTF-8, ASCII folded;
                                                  non-ASCII not) */
#define EXACTF_tb                       88     /*      0x058 */
#define EXACTF_t8                       89     /*      0x059 */
#define EXACTF_tb_pb                      176  /*      0x0b0 */
#define EXACTF_tb_p8                      177  /*      0x0b1 */
#define EXACTF_t8_pb                      178  /*      0x0b2 */
#define EXACTF_t8_p8                      179  /*      0x0b3 */

#define EXACTFL                      45        /* 0x2d Like EXACT, but match
                                                  using /il rules; (string not
                                                  likely to be folded) */
#define EXACTFL_tb                      90     /*      0x05a */
#define EXACTFL_t8                      91     /*      0x05b */
#define EXACTFL_tb_pb                     180  /*      0x0b4 */
#define EXACTFL_tb_p8                     181  /*      0x0b5 */
#define EXACTFL_t8_pb                     182  /*      0x0b6 */
#define EXACTFL_t8_p8                     183  /*      0x0b7 */

#define EXACTFU                      46        /* 0x2e Like EXACT, but match
                                                  using /iu rules; (string
                                                  folded) */
#define EXACTFU_tb                      92     /*      0x05c */
#define EXACTFU_t8                      93     /*      0x05d */
#define EXACTFU_tb_pb                     184  /*      0x0b8 */
#define EXACTFU_tb_p8                     185  /*      0x0b9 */
#define EXACTFU_t8_pb                     186  /*      0x0ba */
#define EXACTFU_t8_p8                     187  /*      0x0bb */

#define EXACTFAA                     47        /* 0x2f Like EXACT, but match
                                                  using /iaa rules; (string
                                                  folded except MICRO in
                                                  non-UTF8 patterns; doesn't
                                                  contain SHARP S unless
                                                  UTF-8; folded length <=
                                                  unfolded) */
#define EXACTFAA_tb                     94     /*      0x05e */
#define EXACTFAA_t8                     95     /*      0x05f */
#define EXACTFAA_tb_pb                    188  /*      0x0bc */
#define EXACTFAA_tb_p8                    189  /*      0x0bd */
#define EXACTFAA_t8_pb                    190  /*      0x0be */
#define EXACTFAA_t8_p8                    191  /*      0x0bf */

#define EXACTFAA_NO_TRIE             48        /* 0x30 Like EXACTFAA, (string
                                                  not UTF-8, folded except:
                                                  MICRO, SHARP S; folded
                                                  length <= unfolded, not
                                                  currently trie-able) */
#define EXACTFAA_NO_TRIE_tb             96     /*      0x060 */
#define EXACTFAA_NO_TRIE_t8             97     /*      0x061 */
#define EXACTFAA_NO_TRIE_tb_pb            192  /*      0x0c0 */
#define EXACTFAA_NO_TRIE_tb_p8            193  /*      0x0c1 */
#define EXACTFAA_NO_TRIE_t8_pb            194  /*      0x0c2 */
#define EXACTFAA_NO_TRIE_t8_p8            195  /*      0x0c3 */

#define EXACTFUP                     49        /* 0x31 Like EXACT, but match
                                                  using /iu rules; (string not
                                                  UTF-8, folded except MICRO:
                                                  hence Problematic) */
#define EXACTFUP_tb                     98     /*      0x062 */
#define EXACTFUP_t8                     99     /*      0x063 */
#define EXACTFUP_tb_pb                    196  /*      0x0c4 */
#define EXACTFUP_tb_p8                    197  /*      0x0c5 */
#define EXACTFUP_t8_pb                    198  /*      0x0c6 */
#define EXACTFUP_t8_p8                    199  /*      0x0c7 */

#define EXACTFLU8                    50        /* 0x32 Like EXACTFU, but use
                                                  /il, UTF-8, (string is
                                                  folded, and everything in it
                                                  is above 255 */
#define EXACTFLU8_tb                   100     /*      0x064 */
#define EXACTFLU8_t8                   101     /*      0x065 */
#define EXACTFLU8_tb_pb                   200  /*      0x0c8 */
#define EXACTFLU8_tb_p8                   201  /*      0x0c9 */
#define EXACTFLU8_t8_pb                   202  /*      0x0ca */
#define EXACTFLU8_t8_p8                   203  /*      0x0cb */

#define EXACT_REQ8                   51        /* 0x33 Like EXACT, but only
                                                  UTF-8 encoded targets can
                                                  match */
#define EXACT_REQ8_tb                  102     /*      0x066 */
#define EXACT_REQ8_t8                  103     /*      0x067 */
#define EXACT_REQ8_tb_pb                  204  /*      0x0cc */
#define EXACT_REQ8_tb_p8                  205  /*      0x0cd */
#define EXACT_REQ8_t8_pb                  206  /*      0x0ce */
#define EXACT_REQ8_t8_p8                  207  /*      0x0cf */

#define LEXACT_REQ8                  52        /* 0x34 Like LEXACT, but only
                                                  UTF-8 encoded targets can
                                                  match */
#define LEXACT_REQ8_tb                 104     /*      0x068 */
#define LEXACT_REQ8_t8                 105     /*      0x069 */
#define LEXACT_REQ8_tb_pb                 208  /*      0x0d0 */
#define LEXACT_REQ8_tb_p8                 209  /*      0x0d1 */
#define LEXACT_REQ8_t8_pb                 210  /*      0x0d2 */
#define LEXACT_REQ8_t8_p8                 211  /*      0x0d3 */

#define EXACTFU_REQ8                 53        /* 0x35 Like EXACTFU, but only
                                                  UTF-8 encoded targets can
                                                  match */
#define EXACTFU_REQ8_tb                106     /*      0x06a */
#define EXACTFU_REQ8_t8                107     /*      0x06b */
#define EXACTFU_REQ8_tb_pb                212  /*      0x0d4 */
#define EXACTFU_REQ8_tb_p8                213  /*      0x0d5 */
#define EXACTFU_REQ8_t8_pb                214  /*      0x0d6 */
#define EXACTFU_REQ8_t8_p8                215  /*      0x0d7 */

#define EXACTFU_S_EDGE               54        /* 0x36 /di rules, but nothing
                                                  in it precludes /ui, except
                                                  begins and/or ends with
                                                  [Ss]; (string not UTF-8;
                                                  compile-time only) */
#define EXACTFU_S_EDGE_tb              108     /*      0x06c */
#define EXACTFU_S_EDGE_t8              109     /*      0x06d */
#define EXACTFU_S_EDGE_tb_pb              216  /*      0x0d8 */
#define EXACTFU_S_EDGE_tb_p8              217  /*      0x0d9 */
#define EXACTFU_S_EDGE_t8_pb              218  /*      0x0da */
#define EXACTFU_S_EDGE_t8_p8              219  /*      0x0db */

#define LNBREAK                      55        /* 0x37 generic newline pattern
                                               */
#define LNBREAK_tb                     110     /*      0x06e */
#define LNBREAK_t8                     111     /*      0x06f */
#define LNBREAK_tb_pb                     220  /*      0x0dc */
#define LNBREAK_tb_p8                     221  /*      0x0dd */
#define LNBREAK_t8_pb                     222  /*      0x0de */
#define LNBREAK_t8_p8                     223  /*      0x0df */

#define TRIE                         56        /* 0x38 Match many
                                                  EXACT(F[ALU]?)? at once.
                                                  flags==type */
#define TRIE_tb                        112     /*      0x070 */
#define TRIE_t8                        113     /*      0x071 */
#define TRIE_tb_pb                        224  /*      0x0e0 */
#define TRIE_tb_p8                        225  /*      0x0e1 */
#define TRIE_t8_pb                        226  /*      0x0e2 */
#define TRIE_t8_p8                        227  /*      0x0e3 */

#define TRIEC                        57        /* 0x39 Same as TRIE, but with
                                                  embedded charclass data */
#define TRIEC_tb                       114     /*      0x072 */
#define TRIEC_t8                       115     /*      0x073 */
#define TRIEC_tb_pb                       228  /*      0x0e4 */
#define TRIEC_tb_p8                       229  /*      0x0e5 */
#define TRIEC_t8_pb                       230  /*      0x0e6 */
#define TRIEC_t8_p8                       231  /*      0x0e7 */

#define AHOCORASICK                  58        /* 0x3a Aho Corasick stclass.
                                                  flags==type */
#define AHOCORASICK_tb                 116     /*      0x074 */
#define AHOCORASICK_t8                 117     /*      0x075 */
#define AHOCORASICK_tb_pb                 232  /*      0x0e8 */
#define AHOCORASICK_tb_p8                 233  /*      0x0e9 */
#define AHOCORASICK_t8_pb                 234  /*      0x0ea */
#define AHOCORASICK_t8_p8                 235  /*      0x0eb */

#define AHOCORASICKC                 59        /* 0x3b Same as AHOCORASICK,
                                                  but with embedded charclass
                                                  data */
#define AHOCORASICKC_tb                118     /*      0x076 */
#define AHOCORASICKC_t8                119     /*      0x077 */
#define AHOCORASICKC_tb_pb                236  /*      0x0ec */
#define AHOCORASICKC_tb_p8                237  /*      0x0ed */
#define AHOCORASICKC_t8_pb                238  /*      0x0ee */
#define AHOCORASICKC_t8_p8                239  /*      0x0ef */

#define NOTHING                      60        /* 0x3c Match empty string. */
#define NOTHING_tb                     120     /*      0x078 */
#define NOTHING_t8                     121     /*      0x079 */
#define NOTHING_tb_pb                     240  /*      0x0f0 */
#define NOTHING_tb_p8                     241  /*      0x0f1 */
#define NOTHING_t8_pb                     242  /*      0x0f2 */
#define NOTHING_t8_p8                     243  /*      0x0f3 */

#define TAIL                         61        /* 0x3d Match empty string. Can
                                                  jump here from outside. */
#define TAIL_tb                        122     /*      0x07a */
#define TAIL_t8                        123     /*      0x07b */
#define TAIL_tb_pb                        244  /*      0x0f4 */
#define TAIL_tb_p8                        245  /*      0x0f5 */
#define TAIL_t8_pb                        246  /*      0x0f6 */
#define TAIL_t8_p8                        247  /*      0x0f7 */

#define STAR                         62        /* 0x3e Match this (simple)
                                                  thing 0 or more times:
                                                  /A{0,}B/ where A is width 1
                                                  char */
#define STAR_tb                        124     /*      0x07c */
#define STAR_t8                        125     /*      0x07d */
#define STAR_tb_pb                        248  /*      0x0f8 */
#define STAR_tb_p8                        249  /*      0x0f9 */
#define STAR_t8_pb                        250  /*      0x0fa */
#define STAR_t8_p8                        251  /*      0x0fb */

#define PLUS                         63        /* 0x3f Match this (simple)
                                                  thing 1 or more times:
                                                  /A{1,}B/ where A is width 1
                                                  char */
#define PLUS_tb                        126     /*      0x07e */
#define PLUS_t8                        127     /*      0x07f */
#define PLUS_tb_pb                        252  /*      0x0fc */
#define PLUS_tb_p8                        253  /*      0x0fd */
#define PLUS_t8_pb                        254  /*      0x0fe */
#define PLUS_t8_p8                        255  /*      0x0ff */

#define CURLY                        64        /* 0x40 Match this (simple)
                                                  thing {n,m} times: /A{m,n}B/
                                                  where A is width 1 char */
#define CURLY_tb                       128     /*      0x080 */
#define CURLY_t8                       129     /*      0x081 */
#define CURLY_tb_pb                       256  /*      0x100 */
#define CURLY_tb_p8                       257  /*      0x101 */
#define CURLY_t8_pb                       258  /*      0x102 */
#define CURLY_t8_p8                       259  /*      0x103 */

#define CURLYN                       65        /* 0x41 Capture next-after-this
                                                  simple thing: /(A){m,n}B/
                                                  where A is width 1 char */
#define CURLYN_tb                      130     /*      0x082 */
#define CURLYN_t8                      131     /*      0x083 */
#define CURLYN_tb_pb                      260  /*      0x104 */
#define CURLYN_tb_p8                      261  /*      0x105 */
#define CURLYN_t8_pb                      262  /*      0x106 */
#define CURLYN_t8_p8                      263  /*      0x107 */

#define CURLYM                       66        /* 0x42 Capture this
                                                  medium-complex thing {n,m}
                                                  times: /(A){m,n}B/ where A
                                                  is fixed-length */
#define CURLYM_tb                      132     /*      0x084 */
#define CURLYM_t8                      133     /*      0x085 */
#define CURLYM_tb_pb                      264  /*      0x108 */
#define CURLYM_tb_p8                      265  /*      0x109 */
#define CURLYM_t8_pb                      266  /*      0x10a */
#define CURLYM_t8_p8                      267  /*      0x10b */

#define CURLYX                       67        /* 0x43 Match/Capture this
                                                  complex thing {n,m} times.
                                               */
#define CURLYX_tb                      134     /*      0x086 */
#define CURLYX_t8                      135     /*      0x087 */
#define CURLYX_tb_pb                      268  /*      0x10c */
#define CURLYX_tb_p8                      269  /*      0x10d */
#define CURLYX_t8_pb                      270  /*      0x10e */
#define CURLYX_t8_p8                      271  /*      0x10f */

#define WHILEM                       68        /* 0x44 Do curly processing and
                                                  see if rest matches. */
#define WHILEM_tb                      136     /*      0x088 */
#define WHILEM_t8                      137     /*      0x089 */
#define WHILEM_tb_pb                      272  /*      0x110 */
#define WHILEM_tb_p8                      273  /*      0x111 */
#define WHILEM_t8_pb                      274  /*      0x112 */
#define WHILEM_t8_p8                      275  /*      0x113 */

#define OPEN                         69        /* 0x45 Mark this point in
                                                  input as start of #n. */
#define OPEN_tb                        138     /*      0x08a */
#define OPEN_t8                        139     /*      0x08b */
#define OPEN_tb_pb                        276  /*      0x114 */
#define OPEN_tb_p8                        277  /*      0x115 */
#define OPEN_t8_pb                        278  /*      0x116 */
#define OPEN_t8_p8                        279  /*      0x117 */

#define CLOSE                        70        /* 0x46 Close corresponding
                                                  OPEN of #n. */
#define CLOSE_tb                       140     /*      0x08c */
#define CLOSE_t8                       141     /*      0x08d */
#define CLOSE_tb_pb                       280  /*      0x118 */
#define CLOSE_tb_p8                       281  /*      0x119 */
#define CLOSE_t8_pb                       282  /*      0x11a */
#define CLOSE_t8_p8                       283  /*      0x11b */

#define SROPEN                       71        /* 0x47 Same as OPEN, but for
                                                  script run */
#define SROPEN_tb                      142     /*      0x08e */
#define SROPEN_t8                      143     /*      0x08f */
#define SROPEN_tb_pb                      284  /*      0x11c */
#define SROPEN_tb_p8                      285  /*      0x11d */
#define SROPEN_t8_pb                      286  /*      0x11e */
#define SROPEN_t8_p8                      287  /*      0x11f */

#define SRCLOSE                      72        /* 0x48 Close preceding SROPEN
                                               */
#define SRCLOSE_tb                     144     /*      0x090 */
#define SRCLOSE_t8                     145     /*      0x091 */
#define SRCLOSE_tb_pb                     288  /*      0x120 */
#define SRCLOSE_tb_p8                     289  /*      0x121 */
#define SRCLOSE_t8_pb                     290  /*      0x122 */
#define SRCLOSE_t8_p8                     291  /*      0x123 */

#define REF                          73        /* 0x49 Match some already
                                                  matched string */
#define REF_tb                         146     /*      0x092 */
#define REF_t8                         147     /*      0x093 */
#define REF_tb_pb                         292  /*      0x124 */
#define REF_tb_p8                         293  /*      0x125 */
#define REF_t8_pb                         294  /*      0x126 */
#define REF_t8_p8                         295  /*      0x127 */

#define REFF                         74        /* 0x4a Match already matched
                                                  string, using /di rules. */
#define REFF_tb                        148     /*      0x094 */
#define REFF_t8                        149     /*      0x095 */
#define REFF_tb_pb                        296  /*      0x128 */
#define REFF_tb_p8                        297  /*      0x129 */
#define REFF_t8_pb                        298  /*      0x12a */
#define REFF_t8_p8                        299  /*      0x12b */

#define REFFL                        75        /* 0x4b Match already matched
                                                  string, using /li rules. */
#define REFFL_tb                       150     /*      0x096 */
#define REFFL_t8                       151     /*      0x097 */
#define REFFL_tb_pb                       300  /*      0x12c */
#define REFFL_tb_p8                       301  /*      0x12d */
#define REFFL_t8_pb                       302  /*      0x12e */
#define REFFL_t8_p8                       303  /*      0x12f */

#define REFFU                        76        /* 0x4c Match already matched
                                                  string, usng /ui. */
#define REFFU_tb                       152     /*      0x098 */
#define REFFU_t8                       153     /*      0x099 */
#define REFFU_tb_pb                       304  /*      0x130 */
#define REFFU_tb_p8                       305  /*      0x131 */
#define REFFU_t8_pb                       306  /*      0x132 */
#define REFFU_t8_p8                       307  /*      0x133 */

#define REFFA                        77        /* 0x4d Match already matched
                                                  string, using /aai rules. */
#define REFFA_tb                       154     /*      0x09a */
#define REFFA_t8                       155     /*      0x09b */
#define REFFA_tb_pb                       308  /*      0x134 */
#define REFFA_tb_p8                       309  /*      0x135 */
#define REFFA_t8_pb                       310  /*      0x136 */
#define REFFA_t8_p8                       311  /*      0x137 */

#define REFN                         78        /* 0x4e Match some already
                                                  matched string */
#define REFN_tb                        156     /*      0x09c */
#define REFN_t8                        157     /*      0x09d */
#define REFN_tb_pb                        312  /*      0x138 */
#define REFN_tb_p8                        313  /*      0x139 */
#define REFN_t8_pb                        314  /*      0x13a */
#define REFN_t8_p8                        315  /*      0x13b */

#define REFFN                        79        /* 0x4f Match already matched
                                                  string, using /di rules. */
#define REFFN_tb                       158     /*      0x09e */
#define REFFN_t8                       159     /*      0x09f */
#define REFFN_tb_pb                       316  /*      0x13c */
#define REFFN_tb_p8                       317  /*      0x13d */
#define REFFN_t8_pb                       318  /*      0x13e */
#define REFFN_t8_p8                       319  /*      0x13f */

#define REFFLN                       80        /* 0x50 Match already matched
                                                  string, using /li rules. */
#define REFFLN_tb                      160     /*      0x0a0 */
#define REFFLN_t8                      161     /*      0x0a1 */
#define REFFLN_tb_pb                      320  /*      0x140 */
#define REFFLN_tb_p8                      321  /*      0x141 */
#define REFFLN_t8_pb                      322  /*      0x142 */
#define REFFLN_t8_p8                      323  /*      0x143 */

#define REFFUN                       81        /* 0x51 Match already matched
                                                  string, using /ui rules. */
#define REFFUN_tb                      162     /*      0x0a2 */
#define REFFUN_t8                      163     /*      0x0a3 */
#define REFFUN_tb_pb                      324  /*      0x144 */
#define REFFUN_tb_p8                      325  /*      0x145 */
#define REFFUN_t8_pb                      326  /*      0x146 */
#define REFFUN_t8_p8                      327  /*      0x147 */

#define REFFAN                       82        /* 0x52 Match already matched
                                                  string, using /aai rules. */
#define REFFAN_tb                      164     /*      0x0a4 */
#define REFFAN_t8                      165     /*      0x0a5 */
#define REFFAN_tb_pb                      328  /*      0x148 */
#define REFFAN_tb_p8                      329  /*      0x149 */
#define REFFAN_t8_pb                      330  /*      0x14a */
#define REFFAN_t8_p8                      331  /*      0x14b */

#define LONGJMP                      83        /* 0x53 Jump far away. */
#define LONGJMP_tb                     166     /*      0x0a6 */
#define LONGJMP_t8                     167     /*      0x0a7 */
#define LONGJMP_tb_pb                     332  /*      0x14c */
#define LONGJMP_tb_p8                     333  /*      0x14d */
#define LONGJMP_t8_pb                     334  /*      0x14e */
#define LONGJMP_t8_p8                     335  /*      0x14f */

#define BRANCHJ                      84        /* 0x54 BRANCH with long
                                                  offset. */
#define BRANCHJ_tb                     168     /*      0x0a8 */
#define BRANCHJ_t8                     169     /*      0x0a9 */
#define BRANCHJ_tb_pb                     336  /*      0x150 */
#define BRANCHJ_tb_p8                     337  /*      0x151 */
#define BRANCHJ_t8_pb                     338  /*      0x152 */
#define BRANCHJ_t8_p8                     339  /*      0x153 */

#define IFMATCH                      85        /* 0x55 Succeeds if the
                                                  following matches; non-zero
                                                  flags "f", next_off "o"
                                                  means lookbehind assertion
                                                  starting "f..(f-o)"
                                                  characters before current */
#define IFMATCH_tb                     170     /*      0x0aa */
#define IFMATCH_t8                     171     /*      0x0ab */
#define IFMATCH_tb_pb                     340  /*      0x154 */
#define IFMATCH_tb_p8                     341  /*      0x155 */
#define IFMATCH_t8_pb                     342  /*      0x156 */
#define IFMATCH_t8_p8                     343  /*      0x157 */

#define UNLESSM                      86        /* 0x56 Fails if the following
                                                  matches; non-zero flags "f",
                                                  next_off "o" means
                                                  lookbehind assertion
                                                  starting "f..(f-o)"
                                                  characters before current */
#define UNLESSM_tb                     172     /*      0x0ac */
#define UNLESSM_t8                     173     /*      0x0ad */
#define UNLESSM_tb_pb                     344  /*      0x158 */
#define UNLESSM_tb_p8                     345  /*      0x159 */
#define UNLESSM_t8_pb                     346  /*      0x15a */
#define UNLESSM_t8_p8                     347  /*      0x15b */

#define SUSPEND                      87        /* 0x57 "Independent" sub-RE.
                                               */
#define SUSPEND_tb                     174     /*      0x0ae */
#define SUSPEND_t8                     175     /*      0x0af */
#define SUSPEND_tb_pb                     348  /*      0x15c */
#define SUSPEND_tb_p8                     349  /*      0x15d */
#define SUSPEND_t8_pb                     350  /*      0x15e */
#define SUSPEND_t8_p8                     351  /*      0x15f */

#define IFTHEN                       88        /* 0x58 Switch, should be
                                                  preceded by switcher. */
#define IFTHEN_tb                      176     /*      0x0b0 */
#define IFTHEN_t8                      177     /*      0x0b1 */
#define IFTHEN_tb_pb                      352  /*      0x160 */
#define IFTHEN_tb_p8                      353  /*      0x161 */
#define IFTHEN_t8_pb                      354  /*      0x162 */
#define IFTHEN_t8_p8                      355  /*      0x163 */

#define GROUPP                       89        /* 0x59 Whether the group
                                                  matched. */
#define GROUPP_tb                      178     /*      0x0b2 */
#define GROUPP_t8                      179     /*      0x0b3 */
#define GROUPP_tb_pb                      356  /*      0x164 */
#define GROUPP_tb_p8                      357  /*      0x165 */
#define GROUPP_t8_pb                      358  /*      0x166 */
#define GROUPP_t8_p8                      359  /*      0x167 */

#define EVAL                         90        /* 0x5a Execute some Perl code.
                                               */
#define EVAL_tb                        180     /*      0x0b4 */
#define EVAL_t8                        181     /*      0x0b5 */
#define EVAL_tb_pb                        360  /*      0x168 */
#define EVAL_tb_p8                        361  /*      0x169 */
#define EVAL_t8_pb                        362  /*      0x16a */
#define EVAL_t8_p8                        363  /*      0x16b */

#define MINMOD                       91        /* 0x5b Next operator is not
                                                  greedy. */
#define MINMOD_tb                      182     /*      0x0b6 */
#define MINMOD_t8                      183     /*      0x0b7 */
#define MINMOD_tb_pb                      364  /*      0x16c */
#define MINMOD_tb_p8                      365  /*      0x16d */
#define MINMOD_t8_pb                      366  /*      0x16e */
#define MINMOD_t8_p8                      367  /*      0x16f */

#define LOGICAL                      92        /* 0x5c Next opcode should set
                                                  the flag only. */
#define LOGICAL_tb                     184     /*      0x0b8 */
#define LOGICAL_t8                     185     /*      0x0b9 */
#define LOGICAL_tb_pb                     368  /*      0x170 */
#define LOGICAL_tb_p8                     369  /*      0x171 */
#define LOGICAL_t8_pb                     370  /*      0x172 */
#define LOGICAL_t8_p8                     371  /*      0x173 */

#define RENUM                        93        /* 0x5d Group with
                                                  independently numbered
                                                  parens. */
#define RENUM_tb                       186     /*      0x0ba */
#define RENUM_t8                       187     /*      0x0bb */
#define RENUM_tb_pb                       372  /*      0x174 */
#define RENUM_tb_p8                       373  /*      0x175 */
#define RENUM_t8_pb                       374  /*      0x176 */
#define RENUM_t8_p8                       375  /*      0x177 */

#define GOSUB                        94        /* 0x5e recurse to paren arg1
                                                  at (signed) ofs arg2 */
#define GOSUB_tb                       188     /*      0x0bc */
#define GOSUB_t8                       189     /*      0x0bd */
#define GOSUB_tb_pb                       376  /*      0x178 */
#define GOSUB_tb_p8                       377  /*      0x179 */
#define GOSUB_t8_pb                       378  /*      0x17a */
#define GOSUB_t8_p8                       379  /*      0x17b */

#define GROUPPN                      95        /* 0x5f Whether the group
                                                  matched. */
#define GROUPPN_tb                     190     /*      0x0be */
#define GROUPPN_t8                     191     /*      0x0bf */
#define GROUPPN_tb_pb                     380  /*      0x17c */
#define GROUPPN_tb_p8                     381  /*      0x17d */
#define GROUPPN_t8_pb                     382  /*      0x17e */
#define GROUPPN_t8_p8                     383  /*      0x17f */

#define INSUBP                       96        /* 0x60 Whether we are in a
                                                  specific recurse. */
#define INSUBP_tb                      192     /*      0x0c0 */
#define INSUBP_t8                      193     /*      0x0c1 */
#define INSUBP_tb_pb                      384  /*      0x180 */
#define INSUBP_tb_p8                      385  /*      0x181 */
#define INSUBP_t8_pb                      386  /*      0x182 */
#define INSUBP_t8_p8                      387  /*      0x183 */

#define DEFINEP                      97        /* 0x61 Never execute directly.
                                               */
#define DEFINEP_tb                     194     /*      0x0c2 */
#define DEFINEP_t8                     195     /*      0x0c3 */
#define DEFINEP_tb_pb                     388  /*      0x184 */
#define DEFINEP_tb_p8                     389  /*      0x185 */
#define DEFINEP_t8_pb                     390  /*      0x186 */
#define DEFINEP_t8_p8                     391  /*      0x187 */

#define ENDLIKE                      98        /* 0x62 Used only for the type
                                                  field of verbs */
#define ENDLIKE_tb                     196     /*      0x0c4 */
#define ENDLIKE_t8                     197     /*      0x0c5 */
#define ENDLIKE_tb_pb                     392  /*      0x188 */
#define ENDLIKE_tb_p8                     393  /*      0x189 */
#define ENDLIKE_t8_pb                     394  /*      0x18a */
#define ENDLIKE_t8_p8                     395  /*      0x18b */

#define OPFAIL                       99        /* 0x63 Same as (?!), but with
                                                  verb arg */
#define OPFAIL_tb                      198     /*      0x0c6 */
#define OPFAIL_t8                      199     /*      0x0c7 */
#define OPFAIL_tb_pb                      396  /*      0x18c */
#define OPFAIL_tb_p8                      397  /*      0x18d */
#define OPFAIL_t8_pb                      398  /*      0x18e */
#define OPFAIL_t8_p8                      399  /*      0x18f */

#define ACCEPT                      100        /* 0x64 Accepts the current
                                                  matched string, with verbar
                                               */
#define ACCEPT_tb                      200     /*      0x0c8 */
#define ACCEPT_t8                      201     /*      0x0c9 */
#define ACCEPT_tb_pb                      400  /*      0x190 */
#define ACCEPT_tb_p8                      401  /*      0x191 */
#define ACCEPT_t8_pb                      402  /*      0x192 */
#define ACCEPT_t8_p8                      403  /*      0x193 */

#define VERB                        101        /* 0x65 Used only for the type
                                                  field of verbs */
#define VERB_tb                        202     /*      0x0ca */
#define VERB_t8                        203     /*      0x0cb */
#define VERB_tb_pb                        404  /*      0x194 */
#define VERB_tb_p8                        405  /*      0x195 */
#define VERB_t8_pb                        406  /*      0x196 */
#define VERB_t8_p8                        407  /*      0x197 */

#define PRUNE                       102        /* 0x66 Pattern fails at this
                                                  startpoint if
                                                  no-backtracking through this
                                               */
#define PRUNE_tb                       204     /*      0x0cc */
#define PRUNE_t8                       205     /*      0x0cd */
#define PRUNE_tb_pb                       408  /*      0x198 */
#define PRUNE_tb_p8                       409  /*      0x199 */
#define PRUNE_t8_pb                       410  /*      0x19a */
#define PRUNE_t8_p8                       411  /*      0x19b */

#define MARKPOINT                   103        /* 0x67 Push the current
                                                  location for rollback by
                                                  cut. */
#define MARKPOINT_tb                   206     /*      0x0ce */
#define MARKPOINT_t8                   207     /*      0x0cf */
#define MARKPOINT_tb_pb                   412  /*      0x19c */
#define MARKPOINT_tb_p8                   413  /*      0x19d */
#define MARKPOINT_t8_pb                   414  /*      0x19e */
#define MARKPOINT_t8_p8                   415  /*      0x19f */

#define SKIP                        104        /* 0x68 On failure skip forward
                                                  (to the mark) before
                                                  retrying */
#define SKIP_tb                        208     /*      0x0d0 */
#define SKIP_t8                        209     /*      0x0d1 */
#define SKIP_tb_pb                        416  /*      0x1a0 */
#define SKIP_tb_p8                        417  /*      0x1a1 */
#define SKIP_t8_pb                        418  /*      0x1a2 */
#define SKIP_t8_p8                        419  /*      0x1a3 */

#define COMMIT                      105        /* 0x69 Pattern fails outright
                                                  if backtracking through this
                                               */
#define COMMIT_tb                      210     /*      0x0d2 */
#define COMMIT_t8                      211     /*      0x0d3 */
#define COMMIT_tb_pb                      420  /*      0x1a4 */
#define COMMIT_tb_p8                      421  /*      0x1a5 */
#define COMMIT_t8_pb                      422  /*      0x1a6 */
#define COMMIT_t8_p8                      423  /*      0x1a7 */

#define CUTGROUP                    106        /* 0x6a On failure go to the
                                                  next alternation in the
                                                  group */
#define CUTGROUP_tb                    212     /*      0x0d4 */
#define CUTGROUP_t8                    213     /*      0x0d5 */
#define CUTGROUP_tb_pb                    424  /*      0x1a8 */
#define CUTGROUP_tb_p8                    425  /*      0x1a9 */
#define CUTGROUP_t8_pb                    426  /*      0x1aa */
#define CUTGROUP_t8_p8                    427  /*      0x1ab */

#define KEEPS                       107        /* 0x6b $& begins here. */
#define KEEPS_tb                       214     /*      0x0d6 */
#define KEEPS_t8                       215     /*      0x0d7 */
#define KEEPS_tb_pb                       428  /*      0x1ac */
#define KEEPS_tb_p8                       429  /*      0x1ad */
#define KEEPS_t8_pb                       430  /*      0x1ae */
#define KEEPS_t8_p8                       431  /*      0x1af */

#define LOOKBEHIND_END              108        /* 0x6c Return from lookbehind
                                                  (IFMATCH/UNLESSM) and
                                                  validate position */
#define LOOKBEHIND_END_tb              216     /*      0x0d8 */
#define LOOKBEHIND_END_t8              217     /*      0x0d9 */
#define LOOKBEHIND_END_tb_pb              432  /*      0x1b0 */
#define LOOKBEHIND_END_tb_p8              433  /*      0x1b1 */
#define LOOKBEHIND_END_t8_pb              434  /*      0x1b2 */
#define LOOKBEHIND_END_t8_p8              435  /*      0x1b3 */

#define OPTIMIZED                   109        /* 0x6d Placeholder for dump.
                                               */
#define OPTIMIZED_tb                   218     /*      0x0da */
#define OPTIMIZED_t8                   219     /*      0x0db */
#define OPTIMIZED_tb_pb                   436  /*      0x1b4 */
#define OPTIMIZED_tb_p8                   437  /*      0x1b5 */
#define OPTIMIZED_t8_pb                   438  /*      0x1b6 */
#define OPTIMIZED_t8_p8                   439  /*      0x1b7 */

#define PSEUDO                      110        /* 0x6e Pseudo opcode for
                                                  internal use. */
#define PSEUDO_tb                      220     /*      0x0dc */
#define PSEUDO_t8                      221     /*      0x0dd */
#define PSEUDO_tb_pb                      440  /*      0x1b8 */
#define PSEUDO_tb_p8                      441  /*      0x1b9 */
#define PSEUDO_t8_pb                      442  /*      0x1ba */
#define PSEUDO_t8_p8                      443  /*      0x1bb */

#define REGEX_SET                   111        /* 0x6f Regex set, temporary
                                                  node used in
                                                  pre-optimization compilation
                                               */
#define REGEX_SET_tb                   222     /*      0x0de */
#define REGEX_SET_t8                   223     /*      0x0df */
#define REGEX_SET_tb_pb                   444  /*      0x1bc */
#define REGEX_SET_tb_p8                   445  /*      0x1bd */
#define REGEX_SET_t8_pb                   446  /*      0x1be */
#define REGEX_SET_t8_p8                   447  /*      0x1bf */

	/* ------------ States ------------- */
#define TRIE_next                   112        /* 0x70 state for TRIE */
#define TRIE_next_tb                   224     /*      0x0e0 */
#define TRIE_next_t8                   225     /*      0x0e1 */
#define TRIE_next_tb_pb                   448  /*      0x1c0 */
#define TRIE_next_tb_p8                   449  /*      0x1c1 */
#define TRIE_next_t8_pb                   450  /*      0x1c2 */
#define TRIE_next_t8_p8                   451  /*      0x1c3 */

#define TRIE_next_fail              113        /* 0x71 state for TRIE */
#define TRIE_next_fail_tb              226     /*      0x0e2 */
#define TRIE_next_fail_t8              227     /*      0x0e3 */
#define TRIE_next_fail_tb_pb              452  /*      0x1c4 */
#define TRIE_next_fail_tb_p8              453  /*      0x1c5 */
#define TRIE_next_fail_t8_pb              454  /*      0x1c6 */
#define TRIE_next_fail_t8_p8              455  /*      0x1c7 */

#define EVAL_B                      114        /* 0x72 state for EVAL */
#define EVAL_B_tb                      228     /*      0x0e4 */
#define EVAL_B_t8                      229     /*      0x0e5 */
#define EVAL_B_tb_pb                      456  /*      0x1c8 */
#define EVAL_B_tb_p8                      457  /*      0x1c9 */
#define EVAL_B_t8_pb                      458  /*      0x1ca */
#define EVAL_B_t8_p8                      459  /*      0x1cb */

#define EVAL_B_fail                 115        /* 0x73 state for EVAL */
#define EVAL_B_fail_tb                 230     /*      0x0e6 */
#define EVAL_B_fail_t8                 231     /*      0x0e7 */
#define EVAL_B_fail_tb_pb                 460  /*      0x1cc */
#define EVAL_B_fail_tb_p8                 461  /*      0x1cd */
#define EVAL_B_fail_t8_pb                 462  /*      0x1ce */
#define EVAL_B_fail_t8_p8                 463  /*      0x1cf */

#define EVAL_postponed_AB           116        /* 0x74 state for EVAL */
#define EVAL_postponed_AB_tb           232     /*      0x0e8 */
#define EVAL_postponed_AB_t8           233     /*      0x0e9 */
#define EVAL_postponed_AB_tb_pb           464  /*      0x1d0 */
#define EVAL_postponed_AB_tb_p8           465  /*      0x1d1 */
#define EVAL_postponed_AB_t8_pb           466  /*      0x1d2 */
#define EVAL_postponed_AB_t8_p8           467  /*      0x1d3 */

#define EVAL_postponed_AB_fail      117        /* 0x75 state for EVAL */
#define EVAL_postponed_AB_fail_tb      234     /*      0x0ea */
#define EVAL_postponed_AB_fail_t8      235     /*      0x0eb */
#define EVAL_postponed_AB_fail_tb_pb      468  /*      0x1d4 */
#define EVAL_postponed_AB_fail_tb_p8      469  /*      0x1d5 */
#define EVAL_postponed_AB_fail_t8_pb      470  /*      0x1d6 */
#define EVAL_postponed_AB_fail_t8_p8      471  /*      0x1d7 */

#define CURLYX_end                  118        /* 0x76 state for CURLYX */
#define CURLYX_end_tb                  236     /*      0x0ec */
#define CURLYX_end_t8                  237     /*      0x0ed */
#define CURLYX_end_tb_pb                  472  /*      0x1d8 */
#define CURLYX_end_tb_p8                  473  /*      0x1d9 */
#define CURLYX_end_t8_pb                  474  /*      0x1da */
#define CURLYX_end_t8_p8                  475  /*      0x1db */

#define CURLYX_end_fail             119        /* 0x77 state for CURLYX */
#define CURLYX_end_fail_tb             238     /*      0x0ee */
#define CURLYX_end_fail_t8             239     /*      0x0ef */
#define CURLYX_end_fail_tb_pb             476  /*      0x1dc */
#define CURLYX_end_fail_tb_p8             477  /*      0x1dd */
#define CURLYX_end_fail_t8_pb             478  /*      0x1de */
#define CURLYX_end_fail_t8_p8             479  /*      0x1df */

#define WHILEM_A_pre                120        /* 0x78 state for WHILEM */
#define WHILEM_A_pre_tb                240     /*      0x0f0 */
#define WHILEM_A_pre_t8                241     /*      0x0f1 */
#define WHILEM_A_pre_tb_pb                480  /*      0x1e0 */
#define WHILEM_A_pre_tb_p8                481  /*      0x1e1 */
#define WHILEM_A_pre_t8_pb                482  /*      0x1e2 */
#define WHILEM_A_pre_t8_p8                483  /*      0x1e3 */

#define WHILEM_A_pre_fail           121        /* 0x79 state for WHILEM */
#define WHILEM_A_pre_fail_tb           242     /*      0x0f2 */
#define WHILEM_A_pre_fail_t8           243     /*      0x0f3 */
#define WHILEM_A_pre_fail_tb_pb           484  /*      0x1e4 */
#define WHILEM_A_pre_fail_tb_p8           485  /*      0x1e5 */
#define WHILEM_A_pre_fail_t8_pb           486  /*      0x1e6 */
#define WHILEM_A_pre_fail_t8_p8           487  /*      0x1e7 */

#define WHILEM_A_min                122        /* 0x7a state for WHILEM */
#define WHILEM_A_min_tb                244     /*      0x0f4 */
#define WHILEM_A_min_t8                245     /*      0x0f5 */
#define WHILEM_A_min_tb_pb                488  /*      0x1e8 */
#define WHILEM_A_min_tb_p8                489  /*      0x1e9 */
#define WHILEM_A_min_t8_pb                490  /*      0x1ea */
#define WHILEM_A_min_t8_p8                491  /*      0x1eb */

#define WHILEM_A_min_fail           123        /* 0x7b state for WHILEM */
#define WHILEM_A_min_fail_tb           246     /*      0x0f6 */
#define WHILEM_A_min_fail_t8           247     /*      0x0f7 */
#define WHILEM_A_min_fail_tb_pb           492  /*      0x1ec */
#define WHILEM_A_min_fail_tb_p8           493  /*      0x1ed */
#define WHILEM_A_min_fail_t8_pb           494  /*      0x1ee */
#define WHILEM_A_min_fail_t8_p8           495  /*      0x1ef */

#define WHILEM_A_max                124        /* 0x7c state for WHILEM */
#define WHILEM_A_max_tb                248     /*      0x0f8 */
#define WHILEM_A_max_t8                249     /*      0x0f9 */
#define WHILEM_A_max_tb_pb                496  /*      0x1f0 */
#define WHILEM_A_max_tb_p8                497  /*      0x1f1 */
#define WHILEM_A_max_t8_pb                498  /*      0x1f2 */
#define WHILEM_A_max_t8_p8                499  /*      0x1f3 */

#define WHILEM_A_max_fail           125        /* 0x7d state for WHILEM */
#define WHILEM_A_max_fail_tb           250     /*      0x0fa */
#define WHILEM_A_max_fail_t8           251     /*      0x0fb */
#define WHILEM_A_max_fail_tb_pb           500  /*      0x1f4 */
#define WHILEM_A_max_fail_tb_p8           501  /*      0x1f5 */
#define WHILEM_A_max_fail_t8_pb           502  /*      0x1f6 */
#define WHILEM_A_max_fail_t8_p8           503  /*      0x1f7 */

#define WHILEM_B_min                126        /* 0x7e state for WHILEM */
#define WHILEM_B_min_tb                252     /*      0x0fc */
#define WHILEM_B_min_t8                253     /*      0x0fd */
#define WHILEM_B_min_tb_pb                504  /*      0x1f8 */
#define WHILEM_B_min_tb_p8                505  /*      0x1f9 */
#define WHILEM_B_min_t8_pb                506  /*      0x1fa */
#define WHILEM_B_min_t8_p8                507  /*      0x1fb */

#define WHILEM_B_min_fail           127        /* 0x7f state for WHILEM */
#define WHILEM_B_min_fail_tb           254     /*      0x0fe */
#define WHILEM_B_min_fail_t8           255     /*      0x0ff */
#define WHILEM_B_min_fail_tb_pb           508  /*      0x1fc */
#define WHILEM_B_min_fail_tb_p8           509  /*      0x1fd */
#define WHILEM_B_min_fail_t8_pb           510  /*      0x1fe */
#define WHILEM_B_min_fail_t8_p8           511  /*      0x1ff */

#define WHILEM_B_max                128        /* 0x80 state for WHILEM */
#define WHILEM_B_max_tb                256     /*      0x100 */
#define WHILEM_B_max_t8                257     /*      0x101 */
#define WHILEM_B_max_tb_pb                512  /*      0x200 */
#define WHILEM_B_max_tb_p8                513  /*      0x201 */
#define WHILEM_B_max_t8_pb                514  /*      0x202 */
#define WHILEM_B_max_t8_p8                515  /*      0x203 */

#define WHILEM_B_max_fail           129        /* 0x81 state for WHILEM */
#define WHILEM_B_max_fail_tb           258     /*      0x102 */
#define WHILEM_B_max_fail_t8           259     /*      0x103 */
#define WHILEM_B_max_fail_tb_pb           516  /*      0x204 */
#define WHILEM_B_max_fail_tb_p8           517  /*      0x205 */
#define WHILEM_B_max_fail_t8_pb           518  /*      0x206 */
#define WHILEM_B_max_fail_t8_p8           519  /*      0x207 */

#define BRANCH_next                 130        /* 0x82 state for BRANCH */
#define BRANCH_next_tb                 260     /*      0x104 */
#define BRANCH_next_t8                 261     /*      0x105 */
#define BRANCH_next_tb_pb                 520  /*      0x208 */
#define BRANCH_next_tb_p8                 521  /*      0x209 */
#define BRANCH_next_t8_pb                 522  /*      0x20a */
#define BRANCH_next_t8_p8                 523  /*      0x20b */

#define BRANCH_next_fail            131        /* 0x83 state for BRANCH */
#define BRANCH_next_fail_tb            262     /*      0x106 */
#define BRANCH_next_fail_t8            263     /*      0x107 */
#define BRANCH_next_fail_tb_pb            524  /*      0x20c */
#define BRANCH_next_fail_tb_p8            525  /*      0x20d */
#define BRANCH_next_fail_t8_pb            526  /*      0x20e */
#define BRANCH_next_fail_t8_p8            527  /*      0x20f */

#define CURLYM_A                    132        /* 0x84 state for CURLYM */
#define CURLYM_A_tb                    264     /*      0x108 */
#define CURLYM_A_t8                    265     /*      0x109 */
#define CURLYM_A_tb_pb                    528  /*      0x210 */
#define CURLYM_A_tb_p8                    529  /*      0x211 */
#define CURLYM_A_t8_pb                    530  /*      0x212 */
#define CURLYM_A_t8_p8                    531  /*      0x213 */

#define CURLYM_A_fail               133        /* 0x85 state for CURLYM */
#define CURLYM_A_fail_tb               266     /*      0x10a */
#define CURLYM_A_fail_t8               267     /*      0x10b */
#define CURLYM_A_fail_tb_pb               532  /*      0x214 */
#define CURLYM_A_fail_tb_p8               533  /*      0x215 */
#define CURLYM_A_fail_t8_pb               534  /*      0x216 */
#define CURLYM_A_fail_t8_p8               535  /*      0x217 */

#define CURLYM_B                    134        /* 0x86 state for CURLYM */
#define CURLYM_B_tb                    268     /*      0x10c */
#define CURLYM_B_t8                    269     /*      0x10d */
#define CURLYM_B_tb_pb                    536  /*      0x218 */
#define CURLYM_B_tb_p8                    537  /*      0x219 */
#define CURLYM_B_t8_pb                    538  /*      0x21a */
#define CURLYM_B_t8_p8                    539  /*      0x21b */

#define CURLYM_B_fail               135        /* 0x87 state for CURLYM */
#define CURLYM_B_fail_tb               270     /*      0x10e */
#define CURLYM_B_fail_t8               271     /*      0x10f */
#define CURLYM_B_fail_tb_pb               540  /*      0x21c */
#define CURLYM_B_fail_tb_p8               541  /*      0x21d */
#define CURLYM_B_fail_t8_pb               542  /*      0x21e */
#define CURLYM_B_fail_t8_p8               543  /*      0x21f */

#define IFMATCH_A                   136        /* 0x88 state for IFMATCH */
#define IFMATCH_A_tb                   272     /*      0x110 */
#define IFMATCH_A_t8                   273     /*      0x111 */
#define IFMATCH_A_tb_pb                   544  /*      0x220 */
#define IFMATCH_A_tb_p8                   545  /*      0x221 */
#define IFMATCH_A_t8_pb                   546  /*      0x222 */
#define IFMATCH_A_t8_p8                   547  /*      0x223 */

#define IFMATCH_A_fail              137        /* 0x89 state for IFMATCH */
#define IFMATCH_A_fail_tb              274     /*      0x112 */
#define IFMATCH_A_fail_t8              275     /*      0x113 */
#define IFMATCH_A_fail_tb_pb              548  /*      0x224 */
#define IFMATCH_A_fail_tb_p8              549  /*      0x225 */
#define IFMATCH_A_fail_t8_pb              550  /*      0x226 */
#define IFMATCH_A_fail_t8_p8              551  /*      0x227 */

#define CURLY_B_min                 138        /* 0x8a state for CURLY */
#define CURLY_B_min_tb                 276     /*      0x114 */
#define CURLY_B_min_t8                 277     /*      0x115 */
#define CURLY_B_min_tb_pb                 552  /*      0x228 */
#define CURLY_B_min_tb_p8                 553  /*      0x229 */
#define CURLY_B_min_t8_pb                 554  /*      0x22a */
#define CURLY_B_min_t8_p8                 555  /*      0x22b */

#define CURLY_B_min_fail            139        /* 0x8b state for CURLY */
#define CURLY_B_min_fail_tb            278     /*      0x116 */
#define CURLY_B_min_fail_t8            279     /*      0x117 */
#define CURLY_B_min_fail_tb_pb            556  /*      0x22c */
#define CURLY_B_min_fail_tb_p8            557  /*      0x22d */
#define CURLY_B_min_fail_t8_pb            558  /*      0x22e */
#define CURLY_B_min_fail_t8_p8            559  /*      0x22f */

#define CURLY_B_max                 140        /* 0x8c state for CURLY */
#define CURLY_B_max_tb                 280     /*      0x118 */
#define CURLY_B_max_t8                 281     /*      0x119 */
#define CURLY_B_max_tb_pb                 560  /*      0x230 */
#define CURLY_B_max_tb_p8                 561  /*      0x231 */
#define CURLY_B_max_t8_pb                 562  /*      0x232 */
#define CURLY_B_max_t8_p8                 563  /*      0x233 */

#define CURLY_B_max_fail            141        /* 0x8d state for CURLY */
#define CURLY_B_max_fail_tb            282     /*      0x11a */
#define CURLY_B_max_fail_t8            283     /*      0x11b */
#define CURLY_B_max_fail_tb_pb            564  /*      0x234 */
#define CURLY_B_max_fail_tb_p8            565  /*      0x235 */
#define CURLY_B_max_fail_t8_pb            566  /*      0x236 */
#define CURLY_B_max_fail_t8_p8            567  /*      0x237 */

#define COMMIT_next                 142        /* 0x8e state for COMMIT */
#define COMMIT_next_tb                 284     /*      0x11c */
#define COMMIT_next_t8                 285     /*      0x11d */
#define COMMIT_next_tb_pb                 568  /*      0x238 */
#define COMMIT_next_tb_p8                 569  /*      0x239 */
#define COMMIT_next_t8_pb                 570  /*      0x23a */
#define COMMIT_next_t8_p8                 571  /*      0x23b */

#define COMMIT_next_fail            143        /* 0x8f state for COMMIT */
#define COMMIT_next_fail_tb            286     /*      0x11e */
#define COMMIT_next_fail_t8            287     /*      0x11f */
#define COMMIT_next_fail_tb_pb            572  /*      0x23c */
#define COMMIT_next_fail_tb_p8            573  /*      0x23d */
#define COMMIT_next_fail_t8_pb            574  /*      0x23e */
#define COMMIT_next_fail_t8_p8            575  /*      0x23f */

#define MARKPOINT_next              144        /* 0x90 state for MARKPOINT */
#define MARKPOINT_next_tb              288     /*      0x120 */
#define MARKPOINT_next_t8              289     /*      0x121 */
#define MARKPOINT_next_tb_pb              576  /*      0x240 */
#define MARKPOINT_next_tb_p8              577  /*      0x241 */
#define MARKPOINT_next_t8_pb              578  /*      0x242 */
#define MARKPOINT_next_t8_p8              579  /*      0x243 */

#define MARKPOINT_next_fail         145        /* 0x91 state for MARKPOINT */
#define MARKPOINT_next_fail_tb         290     /*      0x122 */
#define MARKPOINT_next_fail_t8         291     /*      0x123 */
#define MARKPOINT_next_fail_tb_pb         580  /*      0x244 */
#define MARKPOINT_next_fail_tb_p8         581  /*      0x245 */
#define MARKPOINT_next_fail_t8_pb         582  /*      0x246 */
#define MARKPOINT_next_fail_t8_p8         583  /*      0x247 */

#define SKIP_next                   146        /* 0x92 state for SKIP */
#define SKIP_next_tb                   292     /*      0x124 */
#define SKIP_next_t8                   293     /*      0x125 */
#define SKIP_next_tb_pb                   584  /*      0x248 */
#define SKIP_next_tb_p8                   585  /*      0x249 */
#define SKIP_next_t8_pb                   586  /*      0x24a */
#define SKIP_next_t8_p8                   587  /*      0x24b */

#define SKIP_next_fail              147        /* 0x93 state for SKIP */
#define SKIP_next_fail_tb              294     /*      0x126 */
#define SKIP_next_fail_t8              295     /*      0x127 */
#define SKIP_next_fail_tb_pb              588  /*      0x24c */
#define SKIP_next_fail_tb_p8              589  /*      0x24d */
#define SKIP_next_fail_t8_pb              590  /*      0x24e */
#define SKIP_next_fail_t8_p8              591  /*      0x24f */

#define CUTGROUP_next               148        /* 0x94 state for CUTGROUP */
#define CUTGROUP_next_tb               296     /*      0x128 */
#define CUTGROUP_next_t8               297     /*      0x129 */
#define CUTGROUP_next_tb_pb               592  /*      0x250 */
#define CUTGROUP_next_tb_p8               593  /*      0x251 */
#define CUTGROUP_next_t8_pb               594  /*      0x252 */
#define CUTGROUP_next_t8_p8               595  /*      0x253 */

#define CUTGROUP_next_fail          149        /* 0x95 state for CUTGROUP */
#define CUTGROUP_next_fail_tb          298     /*      0x12a */
#define CUTGROUP_next_fail_t8          299     /*      0x12b */
#define CUTGROUP_next_fail_tb_pb          596  /*      0x254 */
#define CUTGROUP_next_fail_tb_p8          597  /*      0x255 */
#define CUTGROUP_next_fail_t8_pb          598  /*      0x256 */
#define CUTGROUP_next_fail_t8_p8          599  /*      0x257 */

#define KEEPS_next                  150        /* 0x96 state for KEEPS */
#define KEEPS_next_tb                  300     /*      0x12c */
#define KEEPS_next_t8                  301     /*      0x12d */
#define KEEPS_next_tb_pb                  600  /*      0x258 */
#define KEEPS_next_tb_p8                  601  /*      0x259 */
#define KEEPS_next_t8_pb                  602  /*      0x25a */
#define KEEPS_next_t8_p8                  603  /*      0x25b */

#define KEEPS_next_fail             151        /* 0x97 state for KEEPS */
#define KEEPS_next_fail_tb             302     /*      0x12e */
#define KEEPS_next_fail_t8             303     /*      0x12f */
#define KEEPS_next_fail_tb_pb             604  /*      0x25c */
#define KEEPS_next_fail_tb_p8             605  /*      0x25d */
#define KEEPS_next_fail_t8_pb             606  /*      0x25e */
#define KEEPS_next_fail_t8_p8             607  /*      0x25f */

#define REF_next                    152        /* 0x98 state for REF */
#define REF_next_tb                    304     /*      0x130 */
#define REF_next_t8                    305     /*      0x131 */
#define REF_next_tb_pb                    608  /*      0x260 */
#define REF_next_tb_p8                    609  /*      0x261 */
#define REF_next_t8_pb                    610  /*      0x262 */
#define REF_next_t8_p8                    611  /*      0x263 */

#define REF_next_fail               153        /* 0x99 state for REF */
#define REF_next_fail_tb               306     /*      0x132 */
#define REF_next_fail_t8               307     /*      0x133 */
#define REF_next_fail_tb_pb               612  /*      0x264 */
#define REF_next_fail_tb_p8               613  /*      0x265 */
#define REF_next_fail_t8_pb               614  /*      0x266 */
#define REF_next_fail_t8_p8               615  /*      0x267 */


/* PL_regnode_name[] - Opcode/state names in string form, for debugging */

#ifndef DOINIT
EXTCONST char * PL_regnode_name[];
#else
EXTCONST char * const PL_regnode_name[] = {
	"END",                   	/* 0000 */
	"SUCCEED",               	/* 0x01 */
	"SBOL",                  	/* 0x02 */
	"MBOL",                  	/* 0x03 */
	"SEOL",                  	/* 0x04 */
	"MEOL",                  	/* 0x05 */
	"EOS",                   	/* 0x06 */
	"GPOS",                  	/* 0x07 */
	"BOUND",                 	/* 0x08 */
	"BOUNDL",                	/* 0x09 */
	"BOUNDU",                	/* 0x0a */
	"BOUNDA",                	/* 0x0b */
	"NBOUND",                	/* 0x0c */
	"NBOUNDL",               	/* 0x0d */
	"NBOUNDU",               	/* 0x0e */
	"NBOUNDA",               	/* 0x0f */
	"REG_ANY",               	/* 0x10 */
	"SANY",                  	/* 0x11 */
	"ANYOF",                 	/* 0x12 */
	"ANYOFD",                	/* 0x13 */
	"ANYOFL",                	/* 0x14 */
	"ANYOFPOSIXL",           	/* 0x15 */
	"ANYOFH",                	/* 0x16 */
	"ANYOFHb",               	/* 0x17 */
	"ANYOFHr",               	/* 0x18 */
	"ANYOFHs",               	/* 0x19 */
	"ANYOFR",                	/* 0x1a */
	"ANYOFRb",               	/* 0x1b */
	"ANYOFHbbm",             	/* 0x1c */
	"ANYOFM",                	/* 0x1d */
	"NANYOFM",               	/* 0x1e */
	"POSIXD",                	/* 0x1f */
	"POSIXL",                	/* 0x20 */
	"POSIXU",                	/* 0x21 */
	"POSIXA",                	/* 0x22 */
	"NPOSIXD",               	/* 0x23 */
	"NPOSIXL",               	/* 0x24 */
	"NPOSIXU",               	/* 0x25 */
	"NPOSIXA",               	/* 0x26 */
	"CLUMP",                 	/* 0x27 */
	"BRANCH",                	/* 0x28 */
	"EXACT",                 	/* 0x29 */
	"LEXACT",                	/* 0x2a */
	"EXACTL",                	/* 0x2b */
	"EXACTF",                	/* 0x2c */
	"EXACTFL",               	/* 0x2d */
	"EXACTFU",               	/* 0x2e */
	"EXACTFAA",              	/* 0x2f */
	"EXACTFAA_NO_TRIE",      	/* 0x30 */
	"EXACTFUP",              	/* 0x31 */
	"EXACTFLU8",             	/* 0x32 */
	"EXACT_REQ8",            	/* 0x33 */
	"LEXACT_REQ8",           	/* 0x34 */
	"EXACTFU_REQ8",          	/* 0x35 */
	"EXACTFU_S_EDGE",        	/* 0x36 */
	"LNBREAK",               	/* 0x37 */
	"TRIE",                  	/* 0x38 */
	"TRIEC",                 	/* 0x39 */
	"AHOCORASICK",           	/* 0x3a */
	"AHOCORASICKC",          	/* 0x3b */
	"NOTHING",               	/* 0x3c */
	"TAIL",                  	/* 0x3d */
	"STAR",                  	/* 0x3e */
	"PLUS",                  	/* 0x3f */
	"CURLY",                 	/* 0x40 */
	"CURLYN",                	/* 0x41 */
	"CURLYM",                	/* 0x42 */
	"CURLYX",                	/* 0x43 */
	"WHILEM",                	/* 0x44 */
	"OPEN",                  	/* 0x45 */
	"CLOSE",                 	/* 0x46 */
	"SROPEN",                	/* 0x47 */
	"SRCLOSE",               	/* 0x48 */
	"REF",                   	/* 0x49 */
	"REFF",                  	/* 0x4a */
	"REFFL",                 	/* 0x4b */
	"REFFU",                 	/* 0x4c */
	"REFFA",                 	/* 0x4d */
	"REFN",                  	/* 0x4e */
	"REFFN",                 	/* 0x4f */
	"REFFLN",                	/* 0x50 */
	"REFFUN",                	/* 0x51 */
	"REFFAN",                	/* 0x52 */
	"LONGJMP",               	/* 0x53 */
	"BRANCHJ",               	/* 0x54 */
	"IFMATCH",               	/* 0x55 */
	"UNLESSM",               	/* 0x56 */
	"SUSPEND",               	/* 0x57 */
	"IFTHEN",                	/* 0x58 */
	"GROUPP",                	/* 0x59 */
	"EVAL",                  	/* 0x5a */
	"MINMOD",                	/* 0x5b */
	"LOGICAL",               	/* 0x5c */
	"RENUM",                 	/* 0x5d */
	"GOSUB",                 	/* 0x5e */
	"GROUPPN",               	/* 0x5f */
	"INSUBP",                	/* 0x60 */
	"DEFINEP",               	/* 0x61 */
	"ENDLIKE",               	/* 0x62 */
	"OPFAIL",                	/* 0x63 */
	"ACCEPT",                	/* 0x64 */
	"VERB",                  	/* 0x65 */
	"PRUNE",                 	/* 0x66 */
	"MARKPOINT",             	/* 0x67 */
	"SKIP",                  	/* 0x68 */
	"COMMIT",                	/* 0x69 */
	"CUTGROUP",              	/* 0x6a */
	"KEEPS",                 	/* 0x6b */
	"LOOKBEHIND_END",        	/* 0x6c */
	"OPTIMIZED",             	/* 0x6d */
	"PSEUDO",                	/* 0x6e */
	"REGEX_SET",             	/* 0x6f */
	/* ------------ States ------------- */
	"TRIE_next",             	/* REGNODE_MAX +0x01 */
	"TRIE_next_fail",        	/* REGNODE_MAX +0x02 */
	"EVAL_B",                	/* REGNODE_MAX +0x03 */
	"EVAL_B_fail",           	/* REGNODE_MAX +0x04 */
	"EVAL_postponed_AB",     	/* REGNODE_MAX +0x05 */
	"EVAL_postponed_AB_fail",	/* REGNODE_MAX +0x06 */
	"CURLYX_end",            	/* REGNODE_MAX +0x07 */
	"CURLYX_end_fail",       	/* REGNODE_MAX +0x08 */
	"WHILEM_A_pre",          	/* REGNODE_MAX +0x09 */
	"WHILEM_A_pre_fail",     	/* REGNODE_MAX +0x0a */
	"WHILEM_A_min",          	/* REGNODE_MAX +0x0b */
	"WHILEM_A_min_fail",     	/* REGNODE_MAX +0x0c */
	"WHILEM_A_max",          	/* REGNODE_MAX +0x0d */
	"WHILEM_A_max_fail",     	/* REGNODE_MAX +0x0e */
	"WHILEM_B_min",          	/* REGNODE_MAX +0x0f */
	"WHILEM_B_min_fail",     	/* REGNODE_MAX +0x10 */
	"WHILEM_B_max",          	/* REGNODE_MAX +0x11 */
	"WHILEM_B_max_fail",     	/* REGNODE_MAX +0x12 */
	"BRANCH_next",           	/* REGNODE_MAX +0x13 */
	"BRANCH_next_fail",      	/* REGNODE_MAX +0x14 */
	"CURLYM_A",              	/* REGNODE_MAX +0x15 */
	"CURLYM_A_fail",         	/* REGNODE_MAX +0x16 */
	"CURLYM_B",              	/* REGNODE_MAX +0x17 */
	"CURLYM_B_fail",         	/* REGNODE_MAX +0x18 */
	"IFMATCH_A",             	/* REGNODE_MAX +0x19 */
	"IFMATCH_A_fail",        	/* REGNODE_MAX +0x1a */
	"CURLY_B_min",           	/* REGNODE_MAX +0x1b */
	"CURLY_B_min_fail",      	/* REGNODE_MAX +0x1c */
	"CURLY_B_max",           	/* REGNODE_MAX +0x1d */
	"CURLY_B_max_fail",      	/* REGNODE_MAX +0x1e */
	"COMMIT_next",           	/* REGNODE_MAX +0x1f */
	"COMMIT_next_fail",      	/* REGNODE_MAX +0x20 */
	"MARKPOINT_next",        	/* REGNODE_MAX +0x21 */
	"MARKPOINT_next_fail",   	/* REGNODE_MAX +0x22 */
	"SKIP_next",             	/* REGNODE_MAX +0x23 */
	"SKIP_next_fail",        	/* REGNODE_MAX +0x24 */
	"CUTGROUP_next",         	/* REGNODE_MAX +0x25 */
	"CUTGROUP_next_fail",    	/* REGNODE_MAX +0x26 */
	"KEEPS_next",            	/* REGNODE_MAX +0x27 */
	"KEEPS_next_fail",       	/* REGNODE_MAX +0x28 */
	"REF_next",              	/* REGNODE_MAX +0x29 */
	"REF_next_fail",         	/* REGNODE_MAX +0x2a */
};
#endif /* DOINIT */


/* PL_regnode_info[] - Opcode/state names in string form, for debugging */

#ifndef DOINIT
EXTCONST struct regnode_meta PL_regnode_info[];
#else
EXTCONST struct regnode_meta PL_regnode_info[] = {
    {
        /* #0 op END */
        .type = END,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #1 op SUCCEED */
        .type = END,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #2 op SBOL */
        .type = BOL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #3 op MBOL */
        .type = BOL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #4 op SEOL */
        .type = EOL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #5 op MEOL */
        .type = EOL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #6 op EOS */
        .type = EOL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #7 op GPOS */
        .type = GPOS,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #8 op BOUND */
        .type = BOUND,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #9 op BOUNDL */
        .type = BOUND,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #10 op BOUNDU */
        .type = BOUND,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #11 op BOUNDA */
        .type = BOUND,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #12 op NBOUND */
        .type = NBOUND,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #13 op NBOUNDL */
        .type = NBOUND,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #14 op NBOUNDU */
        .type = NBOUND,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #15 op NBOUNDA */
        .type = NBOUND,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #16 op REG_ANY */
        .type = REG_ANY,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #17 op SANY */
        .type = REG_ANY,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #18 op ANYOF */
        .type = ANYOF,
        .arg_len = EXTRA_SIZE(tregnode_ANYOF),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #19 op ANYOFD */
        .type = ANYOF,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFD),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #20 op ANYOFL */
        .type = ANYOF,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFL),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #21 op ANYOFPOSIXL */
        .type = ANYOF,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFPOSIXL),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #22 op ANYOFH */
        .type = ANYOFH,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFH),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #23 op ANYOFHb */
        .type = ANYOFH,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFHb),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #24 op ANYOFHr */
        .type = ANYOFH,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFHr),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #25 op ANYOFHs */
        .type = ANYOFH,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFHs),
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #26 op ANYOFR */
        .type = ANYOFR,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFR),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #27 op ANYOFRb */
        .type = ANYOFR,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFRb),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #28 op ANYOFHbbm */
        .type = ANYOFHbbm,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFHbbm),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #29 op ANYOFM */
        .type = ANYOFM,
        .arg_len = EXTRA_SIZE(tregnode_ANYOFM),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #30 op NANYOFM */
        .type = ANYOFM,
        .arg_len = EXTRA_SIZE(tregnode_NANYOFM),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #31 op POSIXD */
        .type = POSIXD,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #32 op POSIXL */
        .type = POSIXD,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #33 op POSIXU */
        .type = POSIXD,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #34 op POSIXA */
        .type = POSIXD,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #35 op NPOSIXD */
        .type = NPOSIXD,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #36 op NPOSIXL */
        .type = NPOSIXD,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #37 op NPOSIXU */
        .type = NPOSIXD,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #38 op NPOSIXA */
        .type = NPOSIXD,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #39 op CLUMP */
        .type = CLUMP,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #40 op BRANCH */
        .type = BRANCH,
        .arg_len = EXTRA_SIZE(tregnode_BRANCH),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #41 op EXACT */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #42 op LEXACT */
        .type = EXACT,
        .arg_len = EXTRA_SIZE(tregnode_LEXACT),
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #43 op EXACTL */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #44 op EXACTF */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #45 op EXACTFL */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #46 op EXACTFU */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #47 op EXACTFAA */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #48 op EXACTFAA_NO_TRIE */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #49 op EXACTFUP */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #50 op EXACTFLU8 */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #51 op EXACT_REQ8 */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #52 op LEXACT_REQ8 */
        .type = EXACT,
        .arg_len = EXTRA_SIZE(tregnode_LEXACT_REQ8),
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #53 op EXACTFU_REQ8 */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #54 op EXACTFU_S_EDGE */
        .type = EXACT,
        .arg_len = 0,
        .arg_len_varies = 1,
        .off_by_arg = 0
    },
    {
        /* #55 op LNBREAK */
        .type = LNBREAK,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #56 op TRIE */
        .type = TRIE,
        .arg_len = EXTRA_SIZE(tregnode_TRIE),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #57 op TRIEC */
        .type = TRIE,
        .arg_len = EXTRA_SIZE(tregnode_TRIEC),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #58 op AHOCORASICK */
        .type = TRIE,
        .arg_len = EXTRA_SIZE(tregnode_AHOCORASICK),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #59 op AHOCORASICKC */
        .type = TRIE,
        .arg_len = EXTRA_SIZE(tregnode_AHOCORASICKC),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #60 op NOTHING */
        .type = NOTHING,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #61 op TAIL */
        .type = NOTHING,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #62 op STAR */
        .type = STAR,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #63 op PLUS */
        .type = PLUS,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #64 op CURLY */
        .type = CURLY,
        .arg_len = EXTRA_SIZE(tregnode_CURLY),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #65 op CURLYN */
        .type = CURLY,
        .arg_len = EXTRA_SIZE(tregnode_CURLYN),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #66 op CURLYM */
        .type = CURLY,
        .arg_len = EXTRA_SIZE(tregnode_CURLYM),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #67 op CURLYX */
        .type = CURLY,
        .arg_len = EXTRA_SIZE(tregnode_CURLYX),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #68 op WHILEM */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #69 op OPEN */
        .type = OPEN,
        .arg_len = EXTRA_SIZE(tregnode_OPEN),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #70 op CLOSE */
        .type = CLOSE,
        .arg_len = EXTRA_SIZE(tregnode_CLOSE),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #71 op SROPEN */
        .type = SROPEN,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #72 op SRCLOSE */
        .type = SRCLOSE,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #73 op REF */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REF),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #74 op REFF */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REFF),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #75 op REFFL */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REFFL),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #76 op REFFU */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REFFU),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #77 op REFFA */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REFFA),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #78 op REFN */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REFN),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #79 op REFFN */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REFFN),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #80 op REFFLN */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REFFLN),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #81 op REFFUN */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REFFUN),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #82 op REFFAN */
        .type = REF,
        .arg_len = EXTRA_SIZE(tregnode_REFFAN),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #83 op LONGJMP */
        .type = LONGJMP,
        .arg_len = EXTRA_SIZE(tregnode_LONGJMP),
        .arg_len_varies = 0,
        .off_by_arg = 1
    },
    {
        /* #84 op BRANCHJ */
        .type = BRANCHJ,
        .arg_len = EXTRA_SIZE(tregnode_BRANCHJ),
        .arg_len_varies = 0,
        .off_by_arg = 1
    },
    {
        /* #85 op IFMATCH */
        .type = BRANCHJ,
        .arg_len = EXTRA_SIZE(tregnode_IFMATCH),
        .arg_len_varies = 0,
        .off_by_arg = 1
    },
    {
        /* #86 op UNLESSM */
        .type = BRANCHJ,
        .arg_len = EXTRA_SIZE(tregnode_UNLESSM),
        .arg_len_varies = 0,
        .off_by_arg = 1
    },
    {
        /* #87 op SUSPEND */
        .type = BRANCHJ,
        .arg_len = EXTRA_SIZE(tregnode_SUSPEND),
        .arg_len_varies = 0,
        .off_by_arg = 1
    },
    {
        /* #88 op IFTHEN */
        .type = BRANCHJ,
        .arg_len = EXTRA_SIZE(tregnode_IFTHEN),
        .arg_len_varies = 0,
        .off_by_arg = 1
    },
    {
        /* #89 op GROUPP */
        .type = GROUPP,
        .arg_len = EXTRA_SIZE(tregnode_GROUPP),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #90 op EVAL */
        .type = EVAL,
        .arg_len = EXTRA_SIZE(tregnode_EVAL),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #91 op MINMOD */
        .type = MINMOD,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #92 op LOGICAL */
        .type = LOGICAL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #93 op RENUM */
        .type = BRANCHJ,
        .arg_len = EXTRA_SIZE(tregnode_RENUM),
        .arg_len_varies = 0,
        .off_by_arg = 1
    },
    {
        /* #94 op GOSUB */
        .type = GOSUB,
        .arg_len = EXTRA_SIZE(tregnode_GOSUB),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #95 op GROUPPN */
        .type = GROUPPN,
        .arg_len = EXTRA_SIZE(tregnode_GROUPPN),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #96 op INSUBP */
        .type = INSUBP,
        .arg_len = EXTRA_SIZE(tregnode_INSUBP),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #97 op DEFINEP */
        .type = DEFINEP,
        .arg_len = EXTRA_SIZE(tregnode_DEFINEP),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #98 op ENDLIKE */
        .type = ENDLIKE,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #99 op OPFAIL */
        .type = ENDLIKE,
        .arg_len = EXTRA_SIZE(tregnode_OPFAIL),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #100 op ACCEPT */
        .type = ENDLIKE,
        .arg_len = EXTRA_SIZE(tregnode_ACCEPT),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #101 op VERB */
        .type = VERB,
        .arg_len = EXTRA_SIZE(tregnode_VERB),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #102 op PRUNE */
        .type = VERB,
        .arg_len = EXTRA_SIZE(tregnode_PRUNE),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #103 op MARKPOINT */
        .type = VERB,
        .arg_len = EXTRA_SIZE(tregnode_MARKPOINT),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #104 op SKIP */
        .type = VERB,
        .arg_len = EXTRA_SIZE(tregnode_SKIP),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #105 op COMMIT */
        .type = VERB,
        .arg_len = EXTRA_SIZE(tregnode_COMMIT),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #106 op CUTGROUP */
        .type = VERB,
        .arg_len = EXTRA_SIZE(tregnode_CUTGROUP),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #107 op KEEPS */
        .type = KEEPS,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #108 op LOOKBEHIND_END */
        .type = END,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #109 op OPTIMIZED */
        .type = NOTHING,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #110 op PSEUDO */
        .type = PSEUDO,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #111 op REGEX_SET */
        .type = REGEX_SET,
        .arg_len = EXTRA_SIZE(tregnode_REGEX_SET),
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #112 state TRIE_next */
        .type = TRIE,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #113 state TRIE_next_fail */
        .type = TRIE,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #114 state EVAL_B */
        .type = EVAL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #115 state EVAL_B_fail */
        .type = EVAL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #116 state EVAL_postponed_AB */
        .type = EVAL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #117 state EVAL_postponed_AB_fail */
        .type = EVAL,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #118 state CURLYX_end */
        .type = CURLYX,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #119 state CURLYX_end_fail */
        .type = CURLYX,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #120 state WHILEM_A_pre */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #121 state WHILEM_A_pre_fail */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #122 state WHILEM_A_min */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #123 state WHILEM_A_min_fail */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #124 state WHILEM_A_max */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #125 state WHILEM_A_max_fail */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #126 state WHILEM_B_min */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #127 state WHILEM_B_min_fail */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #128 state WHILEM_B_max */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #129 state WHILEM_B_max_fail */
        .type = WHILEM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #130 state BRANCH_next */
        .type = BRANCH,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #131 state BRANCH_next_fail */
        .type = BRANCH,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #132 state CURLYM_A */
        .type = CURLYM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #133 state CURLYM_A_fail */
        .type = CURLYM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #134 state CURLYM_B */
        .type = CURLYM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #135 state CURLYM_B_fail */
        .type = CURLYM,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #136 state IFMATCH_A */
        .type = IFMATCH,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #137 state IFMATCH_A_fail */
        .type = IFMATCH,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #138 state CURLY_B_min */
        .type = CURLY,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #139 state CURLY_B_min_fail */
        .type = CURLY,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #140 state CURLY_B_max */
        .type = CURLY,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #141 state CURLY_B_max_fail */
        .type = CURLY,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #142 state COMMIT_next */
        .type = COMMIT,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #143 state COMMIT_next_fail */
        .type = COMMIT,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #144 state MARKPOINT_next */
        .type = MARKPOINT,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #145 state MARKPOINT_next_fail */
        .type = MARKPOINT,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #146 state SKIP_next */
        .type = SKIP,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #147 state SKIP_next_fail */
        .type = SKIP,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #148 state CUTGROUP_next */
        .type = CUTGROUP,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #149 state CUTGROUP_next_fail */
        .type = CUTGROUP,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #150 state KEEPS_next */
        .type = KEEPS,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #151 state KEEPS_next_fail */
        .type = KEEPS,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #152 state REF_next */
        .type = REF,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    },
    {
        /* #153 state REF_next_fail */
        .type = REF,
        .arg_len = 0,
        .arg_len_varies = 0,
        .off_by_arg = 0
    }
};
#endif /* DOINIT */

/* PL_reg_extflags_name[] - Opcode/state names in string form, for debugging */

#ifndef DOINIT
EXTCONST char * PL_reg_extflags_name[];
#else
EXTCONST char * const PL_reg_extflags_name[] = {
	/* Bits in extflags defined: 11111111111111110000111111111111 */
	"MULTILINE",        /* 0x00000001 */
	"SINGLELINE",       /* 0x00000002 */
	"FOLD",             /* 0x00000004 */
	"EXTENDED",         /* 0x00000008 */
	"EXTENDED_MORE",    /* 0x00000010 */
	"NOCAPTURE",        /* 0x00000020 */
	"KEEPCOPY",         /* 0x00000040 */
	"CHARSET0",         /* 0x00000080 : "CHARSET" - 0x00000380 */
	"CHARSET1",         /* 0x00000100 : "CHARSET" - 0x00000380 */
	"CHARSET2",         /* 0x00000200 : "CHARSET" - 0x00000380 */
	"STRICT",           /* 0x00000400 */
	"SPLIT",            /* 0x00000800 */
	"UNUSED_BIT_12",    /* 0x00001000 */
	"UNUSED_BIT_13",    /* 0x00002000 */
	"UNUSED_BIT_14",    /* 0x00004000 */
	"UNUSED_BIT_15",    /* 0x00008000 */
	"NO_INPLACE_SUBST", /* 0x00010000 */
	"EVAL_SEEN",        /* 0x00020000 */
	"UNBOUNDED_QUANTIFIER_SEEN",/* 0x00040000 */
	"CHECK_ALL",        /* 0x00080000 */
	"MATCH_UTF8",       /* 0x00100000 */
	"USE_INTUIT_NOML",  /* 0x00200000 */
	"USE_INTUIT_ML",    /* 0x00400000 */
	"INTUIT_TAIL",      /* 0x00800000 */
	"IS_ANCHORED",      /* 0x01000000 */
	"COPY_DONE",        /* 0x02000000 */
	"TAINTED_SEEN",     /* 0x04000000 */
	"TAINTED",          /* 0x08000000 */
	"START_ONLY",       /* 0x10000000 */
	"SKIPWHITE",        /* 0x20000000 */
	"WHITE",            /* 0x40000000 */
	"NULL",             /* 0x80000000 */
};
#endif /* DOINIT */

#ifdef DEBUGGING
#  define REG_EXTFLAGS_NAME_SIZE 32
#endif

/* PL_reg_intflags_name[] - Opcode/state names in string form, for debugging */

#ifndef DOINIT
EXTCONST char * PL_reg_intflags_name[];
#else
EXTCONST char * const PL_reg_intflags_name[] = {
	"SKIP",                       /* (1<< 0) - 0x00000001 - PREGf_SKIP */
	"IMPLICIT",                   /* (1<< 1) - 0x00000002 - PREGf_IMPLICIT -  Converted .* to ^.*  */
	"NAUGHTY",                    /* (1<< 2) - 0x00000004 - PREGf_NAUGHTY -  how exponential is this pattern?  */
	"VERBARG_SEEN",               /* (1<< 3) - 0x00000008 - PREGf_VERBARG_SEEN */
	"CUTGROUP_SEEN",              /* (1<< 4) - 0x00000010 - PREGf_CUTGROUP_SEEN */
	"USE_RE_EVAL",                /* (1<< 5) - 0x00000020 - PREGf_USE_RE_EVAL -  compiled with "use re 'eval'"  */
	"NOSCAN",                     /* (1<< 6) - 0x00000040 - PREGf_NOSCAN */
	"",                           /* (1<< 7) - 0x00000080 - *UNUSED* */
	"GPOS_SEEN",                  /* (1<< 8) - 0x00000100 - PREGf_GPOS_SEEN */
	"GPOS_FLOAT",                 /* (1<< 9) - 0x00000200 - PREGf_GPOS_FLOAT */
	"ANCH_MBOL",                  /* (1<<10) - 0x00000400 - PREGf_ANCH_MBOL */
	"ANCH_SBOL",                  /* (1<<11) - 0x00000800 - PREGf_ANCH_SBOL */
	"ANCH_GPOS",                  /* (1<<12) - 0x00001000 - PREGf_ANCH_GPOS */
	"RECURSE_SEEN",               /* (1<<13) - 0x00002000 - PREGf_RECURSE_SEEN */
	"PESSIMIZE_SEEN",             /* (1<<14) - 0x00004000 - PREGf_PESSIMIZE_SEEN */
};
#endif /* DOINIT */

#ifdef DEBUGGING
#  define REG_INTFLAGS_NAME_SIZE 15
#endif

/* The following have no fixed length. U8 so we can do strchr() on it. */
#define REGNODE_VARIES(node) (PL_varies_bitmask[(node) >> 3] & (1 << ((node) & 7)))

#ifndef DOINIT
EXTCONST U8 PL_varies[] __attribute__deprecated__;
#else
EXTCONST U8 PL_varies[] __attribute__deprecated__ = {
    CLUMP, BRANCH, STAR, PLUS, CURLY, CURLYN, CURLYM, CURLYX, WHILEM, REF,
    REFF, REFFL, REFFU, REFFA, REFN, REFFN, REFFLN, REFFUN, REFFAN,
    BRANCHJ, SUSPEND, IFTHEN,
    0
};
#endif /* DOINIT */

#ifndef DOINIT
EXTCONST U8 PL_varies_bitmask[];
#else
EXTCONST U8 PL_varies_bitmask[] = {
    0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0xC0, 0x1F, 0xFE, 0x97, 0x01, 0x00, 0x00
};
#endif /* DOINIT */

/* The following always have a length of 1. U8 we can do strchr() on it. */
/* (Note that length 1 means "one character" under UTF8, not "one octet".) */
#define REGNODE_SIMPLE(node) (PL_simple_bitmask[(node) >> 3] & (1 << ((node) & 7)))

#ifndef DOINIT
EXTCONST U8 PL_simple[] __attribute__deprecated__;
#else
EXTCONST U8 PL_simple[] __attribute__deprecated__ = {
    REG_ANY, SANY, ANYOF, ANYOFD, ANYOFL, ANYOFPOSIXL, ANYOFH, ANYOFHb,
    ANYOFHr, ANYOFHs, ANYOFR, ANYOFRb, ANYOFHbbm, ANYOFM, NANYOFM, POSIXD,
    POSIXL, POSIXU, POSIXA, NPOSIXD, NPOSIXL, NPOSIXU, NPOSIXA, REGEX_SET,
    0
};
#endif /* DOINIT */

#ifndef DOINIT
EXTCONST U8 PL_simple_bitmask[];
#else
EXTCONST U8 PL_simple_bitmask[] = {
    0x00, 0x00, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80
};
#endif /* DOINIT */

/* Is 'op', known to be of type EXACT, folding? */
#define isEXACTFish(op) (__ASSERT_(REGNODE_TYPE(op) == EXACT) (PL_EXACTFish_bitmask & (1U << (op - EXACT))))

/* Do only UTF-8 target strings match 'op', known to be of type EXACT? */
#define isEXACT_REQ8(op) (__ASSERT_(REGNODE_TYPE(op) == EXACT) (PL_EXACT_REQ8_bitmask & (1U << (op - EXACT))))

#ifndef DOINIT
EXTCONST U32 PL_EXACTFish_bitmask;
EXTCONST U32 PL_EXACT_REQ8_bitmask;
#else
EXTCONST U32 PL_EXACTFish_bitmask = 0x33F8;
EXTCONST U32 PL_EXACT_REQ8_bitmask = 0x1E00;
#endif /* DOINIT */

#endif /* defined(PERL_CORE) || defined(PERL_EXT_RE_BUILD) */

/* ex: set ro ft=c: */
