.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_global_set_time_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_global_set_time_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_global_set_time_function(gnutls_time_func " time_func ");"
.SH ARGUMENTS
.IP "gnutls_time_func time_func" 12
it's the system time function, a \fBgnutls_time_func()\fP callback.
.SH "DESCRIPTION"
This is the function where you can override the default system time
function.  The application provided function should behave the same
as the standard function.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
