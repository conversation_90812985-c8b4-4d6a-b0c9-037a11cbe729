/* This file contains the definitions and documentation for the
   machine modes used in the GNU compiler.
   Copyright (C) 1987-2025 Free Software Foundation, Inc.

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */


/* This file defines all the MACHINE MODES used by GCC.

   A machine mode specifies a size and format of data
   at the machine level.

   Each RTL expression has a machine mode.

   At the syntax tree level, each ..._TYPE and each ..._DECL node
   has a machine mode which describes data of that type or the
   data of the variable declared.  */

/* This file is included by the genmodes program.  Its text is the
   body of a function.  Do not rely on this, it will change in the
   future.

   The following statements can be used in this file -- all have
   the form of a C macro call.  In their arguments:

   A CLASS argument must be one of the constants defined in
   mode-classes.def, less the leading MODE_ prefix; some statements
   that take CLASS arguments have restrictions on which classes are
   acceptable.  For instance, INT.

   A MODE argument must be the printable name of a machine mode,
   without quotation marks or trailing "mode".  For instance, SI.

   A PRECISION, BYTESIZE, or COUNT argument must be a positive integer
   constant.

   A FORMAT argument must be one of the real_mode_format structures
   declared in real.h, or else a literal 0.  Do not put a leading &
   on the argument.

   An EXPR argument must be a syntactically valid C expression.
   If an EXPR contains commas, you may need to write an extra pair of
   parentheses around it, so it appears to be a single argument to the
   statement.

   This file defines only those modes which are of use on almost all
   machines.  Other modes can be defined in the target-specific
   mode definition file, config/ARCH/ARCH-modes.def.

   Order matters in this file in so far as statements which refer to
   other modes must appear after the modes they refer to.  However,
   statements which do not refer to other modes may appear in any
   order.

     RANDOM_MODE (MODE);
        declares MODE to be of class RANDOM.

     CC_MODE (MODE);
        declares MODE to be of class CC.

     INT_MODE (MODE, BYTESIZE);
        declares MODE to be of class INT and BYTESIZE bytes wide.
	All of the bits of its representation are significant.

     FRACTIONAL_INT_MODE (MODE, PRECISION, BYTESIZE);
        declares MODE to be of class INT, BYTESIZE bytes wide in
	storage, but with only PRECISION significant bits.

     FLOAT_MODE (MODE, BYTESIZE, FORMAT);
        declares MODE to be of class FLOAT and BYTESIZE bytes wide,
	using floating point format FORMAT.
	All of the bits of its representation are significant.

     FRACTIONAL_FLOAT_MODE (MODE, PRECISION, BYTESIZE, FORMAT);
        declares MODE to be of class FLOAT, BYTESIZE bytes wide in
	storage, but with only PRECISION significant bits, using
	floating point format FORMAT.

     DECIMAL_FLOAT_MODE (MODE, BYTESIZE, FORMAT);
	declares MODE to be of class DECIMAL_FLOAT and BYTESIZE bytes
	wide.  All of the bits of its representation are significant.

     FRACTIONAL_DECIMAL_FLOAT_MODE (MODE, BYTESIZE, FORMAT);
	declares MODE to be of class DECIMAL_FLOAT and BYTESIZE bytes
	wide.  All of the bits of its representation are significant.

     FRACT_MODE (MODE, BYTESIZE, FBIT);
	declares MODE to be of class FRACT and BYTESIZE bytes wide
	with FBIT fractional bits.  There may be padding bits.

     UFRACT_MODE (MODE, BYTESIZE, FBIT);
	declares MODE to be of class UFRACT and BYTESIZE bytes wide
	with FBIT fractional bits.  There may be padding bits.

     ACCUM_MODE (MODE, BYTESIZE, IBIT, FBIT);
	declares MODE to be of class ACCUM and BYTESIZE bytes wide
	with IBIT integral bits and FBIT fractional bits.
	There may be padding bits.

     UACCUM_MODE (MODE, BYTESIZE, IBIT, FBIT);
	declares MODE to be of class UACCUM and BYTESIZE bytes wide
	with IBIT integral bits and FBIT fractional bits.
	There may be padding bits.

     RESET_FLOAT_FORMAT (MODE, FORMAT);
	changes the format of MODE, which must be class FLOAT,
	to FORMAT.  Use in an ARCH-modes.def to reset the format
	of one of the float modes defined in this file.

     PARTIAL_INT_MODE (MODE, PRECISION, NAME);
        declares a mode of class PARTIAL_INT with the same size as
	MODE (which must be an INT mode) and precision PREC.
	Optionally, NAME is the new name of the mode.  NAME is the
	name of the mode.

     VECTOR_MODE (CLASS, MODE, COUNT);
        Declare a vector mode whose component mode is MODE (of class
	CLASS) with COUNT components.  CLASS must be INT or FLOAT.
	The name of the vector mode takes the form VnX where n is
	COUNT in decimal and X is MODE.

     VECTOR_MODES (CLASS, WIDTH);
        For all modes presently declared in class CLASS, construct
	corresponding vector modes having width WIDTH.  Modes whose
	byte sizes do not evenly divide WIDTH are ignored, as are
	modes that would produce vector modes with only one component,
	and modes smaller than one byte (if CLASS is INT) or smaller
	than two bytes (if CLASS is FLOAT).  CLASS must be INT or
	FLOAT.  The names follow the same rule as VECTOR_MODE uses.

     VECTOR_MODES_WITH_PREFIX (PREFIX, CLASS, WIDTH, ORDER);
	Like VECTOR_MODES, but start the mode names with PREFIX instead
	of the usual "V".  ORDER is the top-level sorting order of the
	mode, with smaller numbers indicating a higher priority.

     VECTOR_BOOL_MODE (NAME, COUNT, COMPONENT, BYTESIZE)
        Create a vector mode called NAME that contains COUNT boolean
        elements and occupies BYTESIZE bytes in total.  Each boolean
        element is of COMPONENT type and occupies (COUNT * BITS_PER_UNIT) /
        BYTESIZE bits, with the element at index 0 occupying the lsb of the
        first byte in memory.  Only the lowest bit of each element is
        significant.

     OPAQUE_MODE (NAME, BYTESIZE)
        Create an opaque mode called NAME that is BYTESIZE bytes wide.

     COMPLEX_MODES (CLASS);
        For all modes presently declared in class CLASS, construct
	corresponding complex modes.  Modes smaller than one byte
	are ignored.  For FLOAT modes, the names are derived by
	replacing the 'F' in the mode name with a 'C'.  (It is an
	error if there is no 'F'.  For INT modes, the names are
	derived by prefixing a C to the name.

     ADJUST_BYTESIZE (MODE, EXPR);
     ADJUST_ALIGNMENT (MODE, EXPR);
     ADJUST_FLOAT_FORMAT (MODE, EXPR);
     ADJUST_IBIT (MODE, EXPR);
     ADJUST_FBIT (MODE, EXPR);
	Arrange for the byte size, alignment, floating point format, ibit,
	or fbit of MODE to be adjustable at run time.  EXPR will be executed
	once after processing all command line options, and should
	evaluate to the desired byte size, alignment, format, ibit or fbit.

	Unlike a FORMAT argument, if you are adjusting a float format
	you must put an & in front of the name of each format structure.

     ADJUST_NUNITS (MODE, EXPR);
	Like the above, but set the number of nunits of MODE to EXPR.
	This changes the size and precision of the mode in proportion
	to the change in the number of units; for example, doubling
	the number of units doubles the size and precision as well.

   Note: If a mode is ever made which is more than 255 bytes wide,
   machmode.h and genmodes.cc will have to be changed to allocate
   more space for the mode_size and mode_alignment arrays.  */

/* VOIDmode is used when no mode needs to be specified,
   as for example on CONST_INT RTL expressions.  */
RANDOM_MODE (VOID);

/* BLKmode is used for structures, arrays, etc.
   that fit no more specific mode.  */
RANDOM_MODE (BLK);

/* Single bit mode used for booleans.  */
BOOL_MODE (BI, 1, 1);

/* Basic integer modes.  We go up to TI in generic code (128 bits).
   TImode is needed here because the some front ends now genericly
   support __int128.  If the front ends decide to generically support
   larger types, then corresponding modes must be added here.  The
   name OI is reserved for a 256-bit type (needed by some back ends).
    */
INT_MODE (QI, 1);
INT_MODE (HI, 2);
INT_MODE (SI, 4);
INT_MODE (DI, 8);
INT_MODE (TI, 16);

/* No partial integer modes are defined by default.  */

/* The target normally defines any target-specific __intN types and
   their modes, but __int128 for TImode is fairly common so define it
   here.  The type will not be created unless the target supports
   TImode.  */

INT_N (TI, 128);

/* Basic floating point modes.  SF and DF are the only modes provided
   by default.  The names QF, HF, XF, and TF are reserved for targets
   that need 1-word, 2-word, 80-bit, or 128-bit float types respectively.

   These are the IEEE mappings.  They can be overridden with
   RESET_FLOAT_FORMAT or at runtime (in TARGET_OPTION_OVERRIDE).  */

FLOAT_MODE (SF, 4, ieee_single_format);
FLOAT_MODE (DF, 8, ieee_double_format);

/* Basic CC modes.
   FIXME define this only for targets that need it.  */
CC_MODE (CC);

/* Fixed-point modes.  */
FRACT_MODE (QQ, 1, 7); /* s.7 */
FRACT_MODE (HQ, 2, 15); /* s.15 */
FRACT_MODE (SQ, 4, 31); /* s.31 */
FRACT_MODE (DQ, 8, 63); /* s.63 */
FRACT_MODE (TQ, 16, 127); /* s.127 */

UFRACT_MODE (UQQ, 1, 8); /* .8 */
UFRACT_MODE (UHQ, 2, 16); /* .16 */
UFRACT_MODE (USQ, 4, 32); /* .32 */
UFRACT_MODE (UDQ, 8, 64); /* .64 */
UFRACT_MODE (UTQ, 16, 128); /* .128 */

ACCUM_MODE (HA, 2, 8, 7); /* s8.7 */
ACCUM_MODE (SA, 4, 16, 15); /* s16.15 */
ACCUM_MODE (DA, 8, 32, 31); /* s32.31 */
ACCUM_MODE (TA, 16, 64, 63); /* s64.63 */

UACCUM_MODE (UHA, 2, 8, 8); /* 8.8 */
UACCUM_MODE (USA, 4, 16, 16); /* 16.16 */
UACCUM_MODE (UDA, 8, 32, 32); /* 32.32 */
UACCUM_MODE (UTA, 16, 64, 64); /* 64.64 */

/* Allow the target to specify additional modes of various kinds.  */
#if HAVE_EXTRA_MODES
# include EXTRA_MODES_FILE
#endif

/* Complex modes.  */
COMPLEX_MODES (INT);
COMPLEX_MODES (PARTIAL_INT);
COMPLEX_MODES (FLOAT);

/* Decimal floating point modes.  */
DECIMAL_FLOAT_MODE (SD, 4, decimal_single_format);
DECIMAL_FLOAT_MODE (DD, 8, decimal_double_format);
DECIMAL_FLOAT_MODE (TD, 16, decimal_quad_format);

/* The symbol Pmode stands for one of the above machine modes (usually SImode).
   The tm.h file specifies which one.  It is not a distinct mode.  */

/*
Local variables:
mode:c
version-control: t
End:
*/
