XCODE_SCHEME_ENABLE_GPU_FRAME_CAPTURE_MODE
------------------------------------------

.. versionadded:: 3.23

Property value for ``GPU Frame Capture`` in the Options section of
the generated Xcode scheme. Example values are ``Metal`` and
``Disabled``.

This property is initialized by the value of the variable
:variable:`CMAKE_XCODE_SCHEME_ENABLE_GPU_FRAME_CAPTURE_MODE`
if it is set when a target is created.

Please refer to the :prop_tgt:`XCODE_GENERATE_SCHEME` target property
documentation to see all Xcode schema related properties.
