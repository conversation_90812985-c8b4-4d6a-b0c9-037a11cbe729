_pg_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-p')
			COMPREPLY=( $(compgen -W "prompt" -- $cur) )
			return 0
			;;
		'-h'|'-V')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="-number -c -e -f -n -p -r -s -h -V"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
		+*)
			OPTS="+number +/pattern/"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	local IFS=$'\n'
	compopt -o filenames
	COMPREPLY=( $(compgen -f -- $cur) )
	return 0
}
complete -F _pg_module pg
