.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_set_pin_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_set_pin_function \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "void gnutls_pkcs11_set_pin_function(gnutls_pin_callback_t " fn ", void * " userdata ");"
.SH ARGUMENTS
.IP "gnutls_pin_callback_t fn" 12
The PIN callback, a \fBgnutls_pin_callback_t()\fP function.
.IP "void * userdata" 12
data to be supplied to callback
.SH "DESCRIPTION"
This function will set a callback function to be used when a PIN is
required for PKCS 11 operations.  See
\fBgnutls_pin_callback_t()\fP on how the callback should behave.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
