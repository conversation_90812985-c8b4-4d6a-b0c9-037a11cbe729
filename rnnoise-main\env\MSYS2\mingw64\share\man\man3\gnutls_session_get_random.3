.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_get_random" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_get_random \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_session_get_random(gnutls_session_t " session ", gnutls_datum_t * " client ", gnutls_datum_t * " server ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_datum_t * client" 12
the client part of the random
.IP "gnutls_datum_t * server" 12
the server part of the random
.SH "DESCRIPTION"
This function returns pointers to the client and server
random fields used in the TLS handshake. The pointers are
not to be modified or deallocated.

If a client random value has not yet been established, the output
will be garbage.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
