+{
   locale_version => 1.31,
   variable => 'shifted',
   alternate => 'shifted',
   entry => <<'ENTRY', # for DUCET v13.0.0
0E2F      ; [*040C.0020.0002][*FFF0.0000.0000] # THAI CHARACTER PAIYANNOI
0E5A      ; [*040C.0020.0002][*FFF1.0000.0000] # THAI CHARACTER ANGKHANKHU
0E46      ; [*040D.0020.0002][*FFF1.0000.0000] # THAI CHARACTER MAIYAMOK
0E5B      ; [*040D.0020.0002][*FFF0.0000.0000] # THAI CHARACTER KHOMUT
0E4C      ; [.0000.00D4.0002] # THAI CHARACTER THANTHAKHAT
0E47      ; [.0000.00D5.0002] # THAI CHARACTER MAITAIKHU
0E48      ; [.0000.00D6.0002] # THAI CHARACTER MAI EK
0E49      ; [.0000.00D7.0002] # THAI CHARACTER MAI THO
0E4A      ; [.0000.00D8.0002] # THAI CHARACTER MAI TRI
0E4B      ; [.0000.00D9.0002] # THAI CHARACTER MAI CHATTAWA
0E4D      ; [.3245.0020.0002] # THAI CHARACTER NIKHAHIT
0E45      ; [.3248.0020.0003] # THAI CHARACTER LAKKHANGYAO
0E4D 0E32 ; [.3248.0020.0002][.3245.0020.0003] # THAI CHARACTER SARA AM
0E33      ; [.3248.0020.0002][.3245.0020.0004] # THAI CHARACTER SARA AM
0E45 0E4D ; [.3245.0020.0002][.3248.0020.0004] # <THAI CHARACTER LAKKHANGYAO, THAI CHARACTER NIKHAHIT>
0E3A      ; [.3256.0020.0002] # THAI CHARACTER PHINTHU
ENTRY
};
