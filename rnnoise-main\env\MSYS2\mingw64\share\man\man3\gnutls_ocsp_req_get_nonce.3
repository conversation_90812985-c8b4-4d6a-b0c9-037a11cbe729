.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_req_get_nonce" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_req_get_nonce \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_req_get_nonce(gnutls_ocsp_req_const_t " req ", unsigned int * " critical ", gnutls_datum_t * " nonce ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_req_const_t req" 12
should contain a \fBgnutls_ocsp_req_t\fP type
.IP "unsigned int * critical" 12
whether nonce extension is marked critical, or NULL
.IP "gnutls_datum_t * nonce" 12
will hold newly allocated buffer with nonce data
.SH "DESCRIPTION"
This function will return the OCSP request nonce extension data.

The caller needs to deallocate memory by calling \fBgnutls_free()\fP on
 \fInonce\fP \->data.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
