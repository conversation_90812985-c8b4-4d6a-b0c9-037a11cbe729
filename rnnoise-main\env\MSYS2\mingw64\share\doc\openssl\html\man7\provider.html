<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>provider</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#General">General</a></li>
      <li><a href="#Provider">Provider</a></li>
      <li><a href="#Operations">Operations</a>
        <ul>
          <li><a href="#Algorithm-naming">Algorithm naming</a></li>
          <li><a href="#Provider-dependencies">Provider dependencies</a></li>
          <li><a href="#Note-on-naming-clashes">Note on naming clashes</a></li>
        </ul>
      </li>
    </ul>
  </li>
  <li><a href="#OPENSSL-PROVIDERS">OPENSSL PROVIDERS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>provider - OpenSSL operation implementation providers</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p>#include &lt;openssl/provider.h&gt;</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<h2 id="General">General</h2>

<p>This page contains information useful to provider authors.</p>

<p>A <i>provider</i>, in OpenSSL terms, is a unit of code that provides one or more implementations for various operations for diverse algorithms that one might want to perform.</p>

<p>An <i>operation</i> is something one wants to do, such as encryption and decryption, key derivation, MAC calculation, signing and verification, etc.</p>

<p>An <i>algorithm</i> is a named method to perform an operation. Very often, the algorithms revolve around cryptographic operations, but may also revolve around other types of operation, such as managing certain types of objects.</p>

<p>See <a href="../man7/crypto.html">crypto(7)</a> for further details.</p>

<h2 id="Provider">Provider</h2>

<p>A <i>provider</i> offers an initialization function, as a set of base functions in the form of an <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> array, and by extension, a set of <a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a>s (see <a href="../man7/openssl-core.h.html">openssl-core.h(7)</a>). It may be a dynamically loadable module, or may be built-in, in OpenSSL libraries or in the application. If it&#39;s a dynamically loadable module, the initialization function must be named <code>OSSL_provider_init</code> and must be exported. If it&#39;s built-in, the initialization function may have any name.</p>

<p>The initialization function must have the following signature:</p>

<pre><code>int NAME(const OSSL_CORE_HANDLE *handle,
         const OSSL_DISPATCH *in, const OSSL_DISPATCH **out,
         void **provctx);</code></pre>

<p><i>handle</i> is the OpenSSL library object for the provider, and works as a handle for everything the OpenSSL libraries need to know about the provider. For the provider itself, it is passed to some of the functions given in the dispatch array <i>in</i>.</p>

<p><i>in</i> is a dispatch array of base functions offered by the OpenSSL libraries, and the available functions are further described in <a href="../man7/provider-base.html">provider-base(7)</a>.</p>

<p><i>*out</i> must be assigned a dispatch array of base functions that the provider offers to the OpenSSL libraries. The functions that may be offered are further described in <a href="../man7/provider-base.html">provider-base(7)</a>, and they are the central means of communication between the OpenSSL libraries and the provider.</p>

<p><i>*provctx</i> should be assigned a provider specific context to allow the provider multiple simultaneous uses. This pointer will be passed to various operation functions offered by the provider.</p>

<p>Note that the provider will not be made available for applications to use until the initialization function has completed and returned successfully.</p>

<p>One of the functions the provider offers to the OpenSSL libraries is the central mechanism for the OpenSSL libraries to get access to operation implementations for diverse algorithms. Its referred to with the number <b>OSSL_FUNC_PROVIDER_QUERY_OPERATION</b> and has the following signature:</p>

<pre><code>const OSSL_ALGORITHM *provider_query_operation(void *provctx,
                                               int operation_id,
                                               const int *no_store);</code></pre>

<p><i>provctx</i> is the provider specific context that was passed back by the initialization function.</p>

<p><i>operation_id</i> is an operation identity (see <a href="#Operations">&quot;Operations&quot;</a> below).</p>

<p><i>no_store</i> is a flag back to the OpenSSL libraries which, when nonzero, signifies that the OpenSSL libraries will not store a reference to the returned data in their internal store of implementations.</p>

<p>The returned <a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a> is the foundation of any OpenSSL library API that uses providers for their implementation, most commonly in the <i>fetching</i> type of functions (see <a href="../man7/crypto.html">&quot;ALGORITHM FETCHING&quot; in crypto(7)</a>).</p>

<h2 id="Operations">Operations</h2>

<p>Operations are referred to with numbers, via macros with names starting with <code>OSSL_OP_</code>.</p>

<p>With each operation comes a set of defined function types that a provider may or may not offer, depending on its needs.</p>

<p>Currently available operations are:</p>

<dl>

<dt id="Digests">Digests</dt>
<dd>

<p>In the OpenSSL libraries, the corresponding method object is <b>EVP_MD</b>. The number for this operation is <b>OSSL_OP_DIGEST</b>. The functions the provider can offer are described in <a href="../man7/provider-digest.html">provider-digest(7)</a>.</p>

</dd>
<dt id="Symmetric-ciphers">Symmetric ciphers</dt>
<dd>

<p>In the OpenSSL libraries, the corresponding method object is <b>EVP_CIPHER</b>. The number for this operation is <b>OSSL_OP_CIPHER</b>. The functions the provider can offer are described in <a href="../man7/provider-cipher.html">provider-cipher(7)</a>.</p>

</dd>
<dt id="Message-Authentication-Code-MAC">Message Authentication Code (MAC)</dt>
<dd>

<p>In the OpenSSL libraries, the corresponding method object is <b>EVP_MAC</b>. The number for this operation is <b>OSSL_OP_MAC</b>. The functions the provider can offer are described in <a href="../man7/provider-mac.html">provider-mac(7)</a>.</p>

</dd>
<dt id="Key-Derivation-Function-KDF">Key Derivation Function (KDF)</dt>
<dd>

<p>In the OpenSSL libraries, the corresponding method object is <b>EVP_KDF</b>. The number for this operation is <b>OSSL_OP_KDF</b>. The functions the provider can offer are described in <a href="../man7/provider-kdf.html">provider-kdf(7)</a>.</p>

</dd>
<dt id="Key-Exchange">Key Exchange</dt>
<dd>

<p>In the OpenSSL libraries, the corresponding method object is <b>EVP_KEYEXCH</b>. The number for this operation is <b>OSSL_OP_KEYEXCH</b>. The functions the provider can offer are described in <a href="../man7/provider-keyexch.html">provider-keyexch(7)</a>.</p>

</dd>
<dt id="Asymmetric-Ciphers">Asymmetric Ciphers</dt>
<dd>

<p>In the OpenSSL libraries, the corresponding method object is <b>EVP_ASYM_CIPHER</b>. The number for this operation is <b>OSSL_OP_ASYM_CIPHER</b>. The functions the provider can offer are described in <a href="../man7/provider-asym_cipher.html">provider-asym_cipher(7)</a>.</p>

</dd>
<dt id="Asymmetric-Key-Encapsulation">Asymmetric Key Encapsulation</dt>
<dd>

<p>In the OpenSSL libraries, the corresponding method object is <b>EVP_KEM</b>. The number for this operation is <b>OSSL_OP_KEM</b>. The functions the provider can offer are described in <a href="../man7/provider-kem.html">provider-kem(7)</a>.</p>

</dd>
<dt id="Encoding">Encoding</dt>
<dd>

<p>In the OpenSSL libraries, the corresponding method object is <b>OSSL_ENCODER</b>. The number for this operation is <b>OSSL_OP_ENCODER</b>. The functions the provider can offer are described in <a href="../man7/provider-encoder.html">provider-encoder(7)</a>.</p>

</dd>
<dt id="Decoding">Decoding</dt>
<dd>

<p>In the OpenSSL libraries, the corresponding method object is <b>OSSL_DECODER</b>. The number for this operation is <b>OSSL_OP_DECODER</b>. The functions the provider can offer are described in <a href="../man7/provider-decoder.html">provider-decoder(7)</a>.</p>

</dd>
<dt id="Random-Number-Generation">Random Number Generation</dt>
<dd>

<p>The number for this operation is <b>OSSL_OP_RAND</b>. The functions the provider can offer for random number generation are described in <a href="../man7/provider-rand.html">provider-rand(7)</a>.</p>

</dd>
<dt id="Key-Management">Key Management</dt>
<dd>

<p>The number for this operation is <b>OSSL_OP_KEYMGMT</b>. The functions the provider can offer for key management are described in <a href="../man7/provider-keymgmt.html">provider-keymgmt(7)</a>.</p>

</dd>
<dt id="Signing-and-Signature-Verification">Signing and Signature Verification</dt>
<dd>

<p>The number for this operation is <b>OSSL_OP_SIGNATURE</b>. The functions the provider can offer for digital signatures are described in <a href="../man7/provider-signature.html">provider-signature(7)</a>.</p>

</dd>
<dt id="Store-Management">Store Management</dt>
<dd>

<p>The number for this operation is <b>OSSL_OP_STORE</b>. The functions the provider can offer for store management are described in <a href="../man7/provider-storemgmt.html">provider-storemgmt(7)</a>.</p>

</dd>
</dl>

<h3 id="Algorithm-naming">Algorithm naming</h3>

<p>Algorithm names are case insensitive. Any particular algorithm can have multiple aliases associated with it. The canonical OpenSSL naming scheme follows this format:</p>

<p>ALGNAME[VERSION?][-SUBNAME[VERSION?]?][-SIZE?][-MODE?]</p>

<p>VERSION is only present if there are multiple versions of an algorithm (e.g. MD2, MD4, MD5). It may be omitted if there is only one version.</p>

<p>SUBNAME may be present where multiple algorithms are combined together, e.g. MD5-SHA1.</p>

<p>SIZE is only present if multiple versions of an algorithm exist with different sizes (e.g. AES-128-CBC, AES-256-CBC)</p>

<p>MODE is only present where applicable.</p>

<p>Other aliases may exist for example where standards bodies or common practice use alternative names or names that OpenSSL has used historically.</p>

<h3 id="Provider-dependencies">Provider dependencies</h3>

<p>Providers may depend for their proper operation on the availability of (functionality implemented in) other providers. As there is no mechanism to express such dependencies towards the OpenSSL core, provider authors must take care that such dependencies are either completely avoided or made visible to users, e.g., by documentation and/or defensive programming, e.g., outputting error messages if required external dependencies are not available, e.g., when no provider implementing the required functionality has been activated. In particular, provider initialization should not depend on other providers already having been initialized.</p>

<h3 id="Note-on-naming-clashes">Note on naming clashes</h3>

<p>It is possible to register the same algorithm name from within different providers. Users should note that if no property query is specified, or more than one implementation matches the property query then it is unspecified which implementation of a particular algorithm will be returned. Such naming clashes may also occur if algorithms only differ in capitalization as <a href="#Algorithm-naming">&quot;Algorithm naming&quot;</a> is case insensitive.</p>

<h1 id="OPENSSL-PROVIDERS">OPENSSL PROVIDERS</h1>

<p>OpenSSL provides a number of its own providers. These are the default, base, fips, legacy and null providers. See <a href="../man7/crypto.html">crypto(7)</a> for an overview of these providers.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_DigestInit_ex.html">EVP_DigestInit_ex(3)</a>, <a href="../man3/EVP_EncryptInit_ex.html">EVP_EncryptInit_ex(3)</a>, <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a>, <a href="../man3/EVP_set_default_properties.html">EVP_set_default_properties(3)</a>, <a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a>, <a href="../man3/EVP_CIPHER_fetch.html">EVP_CIPHER_fetch(3)</a>, <a href="../man3/EVP_KEYMGMT_fetch.html">EVP_KEYMGMT_fetch(3)</a>, <a href="../man7/openssl-core.h.html">openssl-core.h(7)</a>, <a href="../man7/provider-base.html">provider-base(7)</a>, <a href="../man7/provider-digest.html">provider-digest(7)</a>, <a href="../man7/provider-cipher.html">provider-cipher(7)</a>, <a href="../man7/provider-keyexch.html">provider-keyexch(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The concept of providers and everything surrounding them was introduced in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


