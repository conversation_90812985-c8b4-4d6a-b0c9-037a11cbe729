# bash completion for brctl                                -*- shell-script -*-

_comp_cmd_brctl()
{
    local cur prev words cword comp_args
    _comp_initialize -- "$@" || return

    local command=${words[1]}

    case $cword in
        1)
            _comp_compgen -- -W "addbr delbr addif delif setageing
                setbridgeprio setfd sethello setmaxage setpathcost setportprio
                show showmacs showstp stp"
            ;;
        2)
            case $command in
                show) ;;

                *)
                    _comp_compgen_split -- "$("$1" show |
                        _comp_awk 'NR>1 {print $1}')"
                    ;;
            esac
            ;;
        3)
            case $command in
                addif | delif)
                    _comp_compgen_configured_interfaces
                    ;;
                stp)
                    _comp_compgen -- -W 'on off'
                    ;;
            esac
            ;;
    esac
} &&
    complete -F _comp_cmd_brctl -o default brctl

# ex: filetype=sh
