/*
 * Copyright 2017 <PERSON><PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "dxgi1_5.idl";

typedef enum DXGI_ADAPTER_FLAG3
{
    DXGI_ADAPTER_FLAG3_NONE = 0x0,
    DXGI_ADAPTER_FLAG3_REMOTE = 0x1,
    DXGI_ADAPTER_FLAG3_SOFTWARE = 0x2,
    DXGI_ADAPTER_FLAG3_ACG_COMPATIBLE = 0x4,
    DX<PERSON>_ADAPTER_FLAG3_SUPPORT_MONITORED_FENCES = 0x8,
    DX<PERSON>_ADAPTER_FLAG3_SUPPORT_NON_MONITORED_FENCES = 0x10,
    DXGI_ADAPTER_FLAG3_KEYED_MUTEX_CONFORMANCE = 0x20,
    DXGI_ADAPTER_FLAG3_FORCE_DWORD = 0xffffffff,
} DXGI_ADAPTER_FLAG3;

typedef enum DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAGS
{
    DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAG_FULLSCREEN = 0x1,
    DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAG_WINDOWED = 0x2,
    DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAG_CURSOR_STRETCHED = 0x4,
} DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAGS;

typedef enum DXGI_GPU_PREFERENCE
{
    DXGI_GPU_PREFERENCE_UNSPECIFIED = 0x0,
    DXGI_GPU_PREFERENCE_MINIMUM_POWER = 0x1,
    DXGI_GPU_PREFERENCE_HIGH_PERFORMANCE = 0x2,
} DXGI_GPU_PREFERENCE;

typedef struct DXGI_ADAPTER_DESC3
{
    WCHAR Description[128];
    UINT VendorId;
    UINT DeviceId;
    UINT SubSysId;
    UINT Revision;
    SIZE_T DedicatedVideoMemory;
    SIZE_T DedicatedSystemMemory;
    SIZE_T SharedSystemMemory;
    LUID AdapterLuid;
    DXGI_ADAPTER_FLAG3 Flags;
    DXGI_GRAPHICS_PREEMPTION_GRANULARITY GraphicsPreemptionGranularity;
    DXGI_COMPUTE_PREEMPTION_GRANULARITY ComputePreemptionGranularity;
} DXGI_ADAPTER_DESC3;

typedef struct DXGI_OUTPUT_DESC1
{
    WCHAR DeviceName[32];
    RECT DesktopCoordinates;
    BOOL AttachedToDesktop;
    DXGI_MODE_ROTATION Rotation;
    HMONITOR Monitor;
    UINT BitsPerColor;
    DXGI_COLOR_SPACE_TYPE ColorSpace;
    FLOAT RedPrimary[2];
    FLOAT GreenPrimary[2];
    FLOAT BluePrimary[2];
    FLOAT WhitePoint[2];
    FLOAT MinLuminance;
    FLOAT MaxLuminance;
    FLOAT MaxFullFrameLuminance;
} DXGI_OUTPUT_DESC1;

[
    object,
    uuid(3c8d99d1-4fbf-4181-a82c-af66bf7bd24e),
    local,
    pointer_default(unique)
]
interface IDXGIAdapter4 : IDXGIAdapter3
{
    HRESULT GetDesc3(
        [out] DXGI_ADAPTER_DESC3 *desc
    );
}

[
    object,
    uuid(068346e8-aaec-4b84-add7-137f513f77a1),
    local,
    pointer_default(unique)
]
interface IDXGIOutput6 : IDXGIOutput5
{
    HRESULT GetDesc1(
        [out] DXGI_OUTPUT_DESC1 *desc
    );
    HRESULT CheckHardwareCompositionSupport(
        [out] UINT *flags
    );
}

[
    object,
    uuid(c1b6694f-ff09-44a9-b03c-77900a0a1d17),
    local,
    pointer_default(unique)
]
interface IDXGIFactory6 : IDXGIFactory5
{
    HRESULT EnumAdapterByGpuPreference(
        [in] UINT adapter_idx,
        [in] DXGI_GPU_PREFERENCE gpu_preference,
        [in] REFIID iid,
        [out] void **adapter
    );
}

[
    object,
    uuid(a4966eed-76db-44da-84c1-ee9a7afb20a8),
    local,
    pointer_default(unique)
]
interface IDXGIFactory7 : IDXGIFactory6
{
    HRESULT RegisterAdaptersChangedEvent(
        [in] HANDLE event,
        [out] DWORD *cookie
    );
    HRESULT UnregisterAdaptersChangedEvent(
        [in] DWORD cookie
    );
}
