.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_ocsp_expiration" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_ocsp_expiration \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "time_t gnutls_certificate_get_ocsp_expiration(gnutls_certificate_credentials_t " sc ", unsigned " idx ", int " oidx ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t sc" 12
is a credentials structure.
.IP "unsigned idx" 12
is a certificate chain index as returned by \fBgnutls_certificate_set_key()\fP and friends
.IP "int oidx" 12
is an OCSP response index
.IP "unsigned flags" 12
should be zero
.SH "DESCRIPTION"
This function returns the validity of the loaded OCSP responses,
to provide information on when to reload/refresh them.

Note that the credentials structure should be read\-only when in
use, thus when reloading, either the credentials structure must not
be in use by any sessions, or a new credentials structure should be
allocated for new sessions.

When  \fIoidx\fP is (\-1) then the minimum refresh time for all responses
is returned. Otherwise the index specifies the response corresponding
to the  \fIodix\fP certificate in the certificate chain.
.SH "RETURNS"
On success, the expiration time of the OCSP response. Otherwise
(time_t)(\-1) on error, or (time_t)\-2 on out of bounds.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
