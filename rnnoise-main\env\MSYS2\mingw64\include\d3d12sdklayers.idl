/*
 * Copyright 2016-2019 <PERSON><PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "d3d12.idl";

cpp_quote("#ifdef WINE_NO_UNICODE_MACROS")
cpp_quote("#undef GetMessage")
cpp_quote("#endif")

typedef enum D3D12_MESSAGE_CATEGORY
{
    D3D12_MESSAGE_CATEGORY_APPLICATION_DEFINED = 0x0,
    D3D12_MESSAGE_CATEGORY_MISCELLANEOUS = 0x1,
    D3D12_MESSAGE_CATEGORY_INITIALIZATION = 0x2,
    D3D12_MESSAGE_CATEGORY_CLEANUP = 0x3,
    D3D12_MESSAGE_CATEGORY_COMPILATION = 0x4,
    D3D12_MESSAGE_CATEGORY_STATE_CREATION = 0x5,
    D3D12_MESSAGE_CATEGORY_STATE_SETTING = 0x6,
    D3D12_MESSAGE_CATEGORY_STATE_GETTING = 0x7,
    D3D12_MESSAGE_CATEGORY_RESOURCE_MANIPULATION = 0x8,
    D3D12_MESSAGE_CATEGORY_EXECUTION = 0x9,
    D3D12_MESSAGE_CATEGORY_SHADER = 0xa,
} D3D12_MESSAGE_CATEGORY;

typedef enum D3D12_MESSAGE_SEVERITY
{
    D3D12_MESSAGE_SEVERITY_CORRUPTION = 0x0,
    D3D12_MESSAGE_SEVERITY_ERROR = 0x1,
    D3D12_MESSAGE_SEVERITY_WARNING = 0x2,
    D3D12_MESSAGE_SEVERITY_INFO = 0x3,
    D3D12_MESSAGE_SEVERITY_MESSAGE = 0x4,
} D3D12_MESSAGE_SEVERITY;

typedef enum D3D12_MESSAGE_ID
{
    D3D12_MESSAGE_ID_UNKNOWN = 0x0000,
    D3D12_MESSAGE_ID_STRING_FROM_APPLICATION = 0x0001,
    D3D12_MESSAGE_ID_CORRUPTED_THIS = 0x0002,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER1 = 0x0003,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER2 = 0x0004,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER3 = 0x0005,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER4 = 0x0006,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER5 = 0x0007,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER6 = 0x0008,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER7 = 0x0009,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER8 = 0x000a,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER9 = 0x000b,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER10 = 0x000c,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER11 = 0x000d,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER12 = 0x000e,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER13 = 0x000f,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER14 = 0x0010,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER15 = 0x0011,
    D3D12_MESSAGE_ID_CORRUPTED_MULTITHREADING = 0x0012,
    D3D12_MESSAGE_ID_MESSAGE_REPORTING_OUTOFMEMORY = 0x0013,
    D3D12_MESSAGE_ID_GETPRIVATEDATA_MOREDATA = 0x0014,
    D3D12_MESSAGE_ID_SETPRIVATEDATA_INVALIDFREEDATA = 0x0015,
    D3D12_MESSAGE_ID_SETPRIVATEDATA_CHANGINGPARAMS = 0x0018,
    D3D12_MESSAGE_ID_SETPRIVATEDATA_OUTOFMEMORY = 0x0019,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_UNRECOGNIZEDFORMAT = 0x001a,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDESC = 0x001b,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDFORMAT = 0x001c,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDVIDEOPLANESLICE = 0x001d,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDPLANESLICE = 0x001e,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDIMENSIONS = 0x001f,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDRESOURCE = 0x0020,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_UNRECOGNIZEDFORMAT = 0x0023,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_UNSUPPORTEDFORMAT = 0x0024,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDESC = 0x0025,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDFORMAT = 0x0026,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDVIDEOPLANESLICE = 0x0027,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDPLANESLICE = 0x0028,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDIMENSIONS = 0x0029,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDRESOURCE = 0x002a,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_UNRECOGNIZEDFORMAT = 0x002d,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDESC = 0x002e,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFORMAT = 0x002f,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDIMENSIONS = 0x0030,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDRESOURCE = 0x0031,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_OUTOFMEMORY = 0x0034,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_TOOMANYELEMENTS = 0x0035,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDFORMAT = 0x0036,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INCOMPATIBLEFORMAT = 0x0037,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOT = 0x0038,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDINPUTSLOTCLASS = 0x0039,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_STEPRATESLOTCLASSMISMATCH = 0x003a,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOTCLASSCHANGE = 0x003b,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSTEPRATECHANGE = 0x003c,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDALIGNMENT = 0x003d,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_DUPLICATESEMANTIC = 0x003e,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_UNPARSEABLEINPUTSIGNATURE = 0x003f,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_NULLSEMANTIC = 0x0040,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_MISSINGELEMENT = 0x0041,
    D3D12_MESSAGE_ID_CREATEVERTEXSHADER_OUTOFMEMORY = 0x0042,
    D3D12_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERBYTECODE = 0x0043,
    D3D12_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERTYPE = 0x0044,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_OUTOFMEMORY = 0x0045,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERBYTECODE = 0x0046,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERTYPE = 0x0047,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTOFMEMORY = 0x0048,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERBYTECODE = 0x0049,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERTYPE = 0x004a,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMENTRIES = 0x004b,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSTREAMSTRIDEUNUSED = 0x004c,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSLOT0EXPECTED = 0x004f,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSLOT = 0x0050,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_ONLYONEELEMENTPERSLOT = 0x0051,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDCOMPONENTCOUNT = 0x0052,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTARTCOMPONENTANDCOMPONENTCOUNT = 0x0053,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDGAPDEFINITION = 0x0054,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_REPEATEDOUTPUT = 0x0055,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSTREAMSTRIDE = 0x0056,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGSEMANTIC = 0x0057,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MASKMISMATCH = 0x0058,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_CANTHAVEONLYGAPS = 0x0059,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DECLTOOCOMPLEX = 0x005a,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGOUTPUTSIGNATURE = 0x005b,
    D3D12_MESSAGE_ID_CREATEPIXELSHADER_OUTOFMEMORY = 0x005c,
    D3D12_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERBYTECODE = 0x005d,
    D3D12_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERTYPE = 0x005e,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFILLMODE = 0x005f,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDCULLMODE = 0x0060,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDDEPTHBIASCLAMP = 0x0061,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDSLOPESCALEDDEPTHBIAS = 0x0062,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHWRITEMASK = 0x0064,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHFUNC = 0x0065,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFAILOP = 0x0066,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILZFAILOP = 0x0067,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILPASSOP = 0x0068,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFUNC = 0x0069,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFAILOP = 0x006a,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILZFAILOP = 0x006b,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILPASSOP = 0x006c,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFUNC = 0x006d,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLEND = 0x006f,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLEND = 0x0070,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOP = 0x0071,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLENDALPHA = 0x0072,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLENDALPHA = 0x0073,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOPALPHA = 0x0074,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDRENDERTARGETWRITEMASK = 0x0075,
    D3D12_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_INVALID = 0x0087,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_ROOT_SIGNATURE_NOT_SET = 0x00c8,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_ROOT_SIGNATURE_MISMATCH = 0x00c9,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_BUFFER_NOT_SET = 0x00ca,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_BUFFER_STRIDE_TOO_SMALL = 0x00d1,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_BUFFER_TOO_SMALL = 0x00d2,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_BUFFER_NOT_SET = 0x00d3,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_BUFFER_FORMAT_INVALID = 0x00d4,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_BUFFER_TOO_SMALL = 0x00d5,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INVALID_PRIMITIVETOPOLOGY = 0x00db,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_STRIDE_UNALIGNED = 0x00dd,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_OFFSET_UNALIGNED = 0x00de,
    D3D12_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_AT_FAULT = 0x00e8,
    D3D12_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_POSSIBLY_AT_FAULT = 0x00e9,
    D3D12_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_NOT_AT_FAULT = 0x00ea,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_TRAILING_DIGIT_IN_SEMANTIC = 0x00ef,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_TRAILING_DIGIT_IN_SEMANTIC = 0x00f0,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_TYPE_MISMATCH = 0x00f5,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_EMPTY_LAYOUT = 0x00fd,
    D3D12_MESSAGE_ID_LIVE_OBJECT_SUMMARY = 0x00ff,
    D3D12_MESSAGE_ID_LIVE_DEVICE = 0x0112,
    D3D12_MESSAGE_ID_LIVE_SWAPCHAIN = 0x0113,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFLAGS = 0x0114,
    D3D12_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDCLASSLINKAGE = 0x0115,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDCLASSLINKAGE = 0x0116,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAMTORASTERIZER = 0x0118,
    D3D12_MESSAGE_ID_CREATEPIXELSHADER_INVALIDCLASSLINKAGE = 0x011b,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAM = 0x011c,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDENTRIES = 0x011d,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDSTRIDES = 0x011e,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMSTRIDES = 0x011f,
    D3D12_MESSAGE_ID_CREATEHULLSHADER_OUTOFMEMORY = 0x0121,
    D3D12_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERBYTECODE = 0x0122,
    D3D12_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERTYPE = 0x0123,
    D3D12_MESSAGE_ID_CREATEHULLSHADER_INVALIDCLASSLINKAGE = 0x0124,
    D3D12_MESSAGE_ID_CREATEDOMAINSHADER_OUTOFMEMORY = 0x0126,
    D3D12_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERBYTECODE = 0x0127,
    D3D12_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERTYPE = 0x0128,
    D3D12_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDCLASSLINKAGE = 0x0129,
    D3D12_MESSAGE_ID_RESOURCE_UNMAP_NOTMAPPED = 0x0136,
    D3D12_MESSAGE_ID_DEVICE_CHECKFEATURESUPPORT_MISMATCHED_DATA_SIZE = 0x013e,
    D3D12_MESSAGE_ID_CREATECOMPUTESHADER_OUTOFMEMORY = 0x0141,
    D3D12_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDSHADERBYTECODE = 0x0142,
    D3D12_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDCLASSLINKAGE = 0x0143,
    D3D12_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x014b,
    D3D12_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x014c,
    D3D12_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x014d,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x014e,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEFLOATOPSNOTSUPPORTED = 0x014f,
    D3D12_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x0150,
    D3D12_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x0151,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDRESOURCE = 0x0154,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDESC = 0x0155,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFORMAT = 0x0156,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDVIDEOPLANESLICE = 0x0157,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDPLANESLICE = 0x0158,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDIMENSIONS = 0x0159,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_UNRECOGNIZEDFORMAT = 0x015a,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFLAGS = 0x0162,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFORCEDSAMPLECOUNT = 0x0191,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDLOGICOPS = 0x0193,
    D3D12_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x019a,
    D3D12_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x019c,
    D3D12_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x019e,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x01a0,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEEXTENSIONSNOTSUPPORTED = 0x01a2,
    D3D12_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x01a4,
    D3D12_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x01a6,
    D3D12_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_UAVSNOTSUPPORTED = 0x01a9,
    D3D12_MESSAGE_ID_DEVICE_CREATEHULLSHADER_UAVSNOTSUPPORTED = 0x01aa,
    D3D12_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_UAVSNOTSUPPORTED = 0x01ab,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_UAVSNOTSUPPORTED = 0x01ac,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UAVSNOTSUPPORTED = 0x01ad,
    D3D12_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_UAVSNOTSUPPORTED = 0x01ae,
    D3D12_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_UAVSNOTSUPPORTED = 0x01af,
    D3D12_MESSAGE_ID_DEVICE_CLEARVIEW_INVALIDSOURCERECT = 0x01bf,
    D3D12_MESSAGE_ID_DEVICE_CLEARVIEW_EMPTYRECT = 0x01c0,
    D3D12_MESSAGE_ID_UPDATETILEMAPPINGS_INVALID_PARAMETER = 0x01ed,
    D3D12_MESSAGE_ID_COPYTILEMAPPINGS_INVALID_PARAMETER = 0x01ee,
    D3D12_MESSAGE_ID_CREATEDEVICE_INVALIDARGS = 0x01fa,
    D3D12_MESSAGE_ID_CREATEDEVICE_WARNING = 0x01fb,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_TYPE = 0x0207,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_NULL_POINTER = 0x0208,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_SUBRESOURCE = 0x0209,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_RESERVED_BITS = 0x020a,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISSING_BIND_FLAGS = 0x020b,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISMATCHING_MISC_FLAGS = 0x020c,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MATCHING_STATES = 0x020d,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_COMBINATION = 0x020e,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_BEFORE_AFTER_MISMATCH = 0x020f,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_RESOURCE = 0x0210,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_SAMPLE_COUNT = 0x0211,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_FLAGS = 0x0212,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_COMBINED_FLAGS = 0x0213,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_FLAGS_FOR_FORMAT = 0x0214,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_SPLIT_BARRIER = 0x0215,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_UNMATCHED_END = 0x0216,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_UNMATCHED_BEGIN = 0x0217,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_FLAG = 0x0218,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_COMMAND_LIST_TYPE = 0x0219,
    D3D12_MESSAGE_ID_INVALID_SUBRESOURCE_STATE = 0x021a,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_CONTENTION = 0x021c,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_RESET = 0x021d,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_RESET_BUNDLE = 0x021e,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_CANNOT_RESET = 0x021f,
    D3D12_MESSAGE_ID_COMMAND_LIST_OPEN = 0x0220,
    D3D12_MESSAGE_ID_INVALID_BUNDLE_API = 0x0222,
    D3D12_MESSAGE_ID_COMMAND_LIST_CLOSED = 0x0223,
    D3D12_MESSAGE_ID_WRONG_COMMAND_ALLOCATOR_TYPE = 0x0225,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_SYNC = 0x0228,
    D3D12_MESSAGE_ID_COMMAND_LIST_SYNC = 0x0229,
    D3D12_MESSAGE_ID_SET_DESCRIPTOR_HEAP_INVALID = 0x022a,
    D3D12_MESSAGE_ID_CREATE_COMMANDQUEUE = 0x022d,
    D3D12_MESSAGE_ID_CREATE_COMMANDALLOCATOR = 0x022e,
    D3D12_MESSAGE_ID_CREATE_PIPELINESTATE = 0x022f,
    D3D12_MESSAGE_ID_CREATE_COMMANDLIST12 = 0x0230,
    D3D12_MESSAGE_ID_CREATE_RESOURCE = 0x0232,
    D3D12_MESSAGE_ID_CREATE_DESCRIPTORHEAP = 0x0233,
    D3D12_MESSAGE_ID_CREATE_ROOTSIGNATURE = 0x0234,
    D3D12_MESSAGE_ID_CREATE_LIBRARY = 0x0235,
    D3D12_MESSAGE_ID_CREATE_HEAP = 0x0236,
    D3D12_MESSAGE_ID_CREATE_MONITOREDFENCE = 0x0237,
    D3D12_MESSAGE_ID_CREATE_QUERYHEAP = 0x0238,
    D3D12_MESSAGE_ID_CREATE_COMMANDSIGNATURE = 0x0239,
    D3D12_MESSAGE_ID_LIVE_COMMANDQUEUE = 0x023a,
    D3D12_MESSAGE_ID_LIVE_COMMANDALLOCATOR = 0x023b,
    D3D12_MESSAGE_ID_LIVE_PIPELINESTATE = 0x023c,
    D3D12_MESSAGE_ID_LIVE_COMMANDLIST12 = 0x023d,
    D3D12_MESSAGE_ID_LIVE_RESOURCE = 0x023f,
    D3D12_MESSAGE_ID_LIVE_DESCRIPTORHEAP = 0x0240,
    D3D12_MESSAGE_ID_LIVE_ROOTSIGNATURE = 0x0241,
    D3D12_MESSAGE_ID_LIVE_LIBRARY = 0x0242,
    D3D12_MESSAGE_ID_LIVE_HEAP = 0x0243,
    D3D12_MESSAGE_ID_LIVE_MONITOREDFENCE = 0x0244,
    D3D12_MESSAGE_ID_LIVE_QUERYHEAP = 0x0245,
    D3D12_MESSAGE_ID_LIVE_COMMANDSIGNATURE = 0x0246,
    D3D12_MESSAGE_ID_DESTROY_COMMANDQUEUE = 0x0247,
    D3D12_MESSAGE_ID_DESTROY_COMMANDALLOCATOR = 0x0248,
    D3D12_MESSAGE_ID_DESTROY_PIPELINESTATE = 0x0249,
    D3D12_MESSAGE_ID_DESTROY_COMMANDLIST12 = 0x024a,
    D3D12_MESSAGE_ID_DESTROY_RESOURCE = 0x024c,
    D3D12_MESSAGE_ID_DESTROY_DESCRIPTORHEAP = 0x024d,
    D3D12_MESSAGE_ID_DESTROY_ROOTSIGNATURE = 0x024e,
    D3D12_MESSAGE_ID_DESTROY_LIBRARY = 0x024f,
    D3D12_MESSAGE_ID_DESTROY_HEAP = 0x0250,
    D3D12_MESSAGE_ID_DESTROY_MONITOREDFENCE = 0x0251,
    D3D12_MESSAGE_ID_DESTROY_QUERYHEAP = 0x0252,
    D3D12_MESSAGE_ID_DESTROY_COMMANDSIGNATURE = 0x0253,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDDIMENSIONS = 0x0255,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDMISCFLAGS = 0x0257,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDARG_RETURN = 0x025a,
    D3D12_MESSAGE_ID_CREATERESOURCE_OUTOFMEMORY_RETURN = 0x025b,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDDESC = 0x025c,
    D3D12_MESSAGE_ID_POSSIBLY_INVALID_SUBRESOURCE_STATE = 0x025f,
    D3D12_MESSAGE_ID_INVALID_USE_OF_NON_RESIDENT_RESOURCE = 0x0260,
    D3D12_MESSAGE_ID_POSSIBLE_INVALID_USE_OF_NON_RESIDENT_RESOURCE = 0x0261,
    D3D12_MESSAGE_ID_BUNDLE_PIPELINE_STATE_MISMATCH = 0x0262,
    D3D12_MESSAGE_ID_PRIMITIVE_TOPOLOGY_MISMATCH_PIPELINE_STATE = 0x0263,
    D3D12_MESSAGE_ID_RENDER_TARGET_FORMAT_MISMATCH_PIPELINE_STATE = 0x0265,
    D3D12_MESSAGE_ID_RENDER_TARGET_SAMPLE_DESC_MISMATCH_PIPELINE_STATE = 0x0266,
    D3D12_MESSAGE_ID_DEPTH_STENCIL_FORMAT_MISMATCH_PIPELINE_STATE = 0x0267,
    D3D12_MESSAGE_ID_DEPTH_STENCIL_SAMPLE_DESC_MISMATCH_PIPELINE_STATE = 0x0268,
    D3D12_MESSAGE_ID_CREATESHADER_INVALIDBYTECODE = 0x026e,
    D3D12_MESSAGE_ID_CREATEHEAP_NULLDESC = 0x026f,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDSIZE = 0x0270,
    D3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDHEAPTYPE = 0x0271,
    D3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDCPUPAGEPROPERTIES = 0x0272,
    D3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDMEMORYPOOL = 0x0273,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDPROPERTIES = 0x0274,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDALIGNMENT = 0x0275,
    D3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDMISCFLAGS = 0x0276,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDMISCFLAGS = 0x0277,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDARG_RETURN = 0x0278,
    D3D12_MESSAGE_ID_CREATEHEAP_OUTOFMEMORY_RETURN = 0x0279,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_NULLHEAPPROPERTIES = 0x027a,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDHEAPTYPE = 0x027b,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDCPUPAGEPROPERTIES = 0x027c,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDMEMORYPOOL = 0x027d,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDHEAPPROPERTIES = 0x027e,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDHEAPMISCFLAGS = 0x027f,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDHEAPMISCFLAGS = 0x0280,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDARG_RETURN = 0x0281,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_OUTOFMEMORY_RETURN = 0x0282,
    D3D12_MESSAGE_ID_GETCUSTOMHEAPPROPERTIES_UNRECOGNIZEDHEAPTYPE = 0x0283,
    D3D12_MESSAGE_ID_GETCUSTOMHEAPPROPERTIES_INVALIDHEAPTYPE = 0x0284,
    D3D12_MESSAGE_ID_CREATE_DESCRIPTOR_HEAP_INVALID_DESC = 0x0285,
    D3D12_MESSAGE_ID_INVALID_DESCRIPTOR_HANDLE = 0x0286,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALID_CONSERVATIVERASTERMODE = 0x0287,
    D3D12_MESSAGE_ID_CREATE_CONSTANT_BUFFER_VIEW_INVALID_RESOURCE = 0x0289,
    D3D12_MESSAGE_ID_CREATE_CONSTANT_BUFFER_VIEW_INVALID_DESC = 0x028a,
    D3D12_MESSAGE_ID_CREATE_UNORDEREDACCESS_VIEW_INVALID_COUNTER_USAGE = 0x028c,
    D3D12_MESSAGE_ID_COPY_DESCRIPTORS_INVALID_RANGES = 0x028d,
    D3D12_MESSAGE_ID_COPY_DESCRIPTORS_WRITE_ONLY_DESCRIPTOR = 0x028e,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RTV_FORMAT_NOT_UNKNOWN = 0x028f,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_RENDER_TARGET_COUNT = 0x0290,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_VERTEX_SHADER_NOT_SET = 0x0291,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INPUTLAYOUT_NOT_SET = 0x0292,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_HS_DS_SIGNATURE_MISMATCH = 0x0293,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_REGISTERINDEX = 0x0294,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_COMPONENTTYPE = 0x0295,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_REGISTERMASK = 0x0296,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_SYSTEMVALUE = 0x0297,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_NEVERWRITTEN_ALWAYSREADS = 0x0298,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_MINPRECISION = 0x0299,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_SEMANTICNAME_NOT_FOUND = 0x029a,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_XOR_DS_MISMATCH = 0x029b,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HULL_SHADER_INPUT_TOPOLOGY_MISMATCH = 0x029c,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_DS_CONTROL_POINT_COUNT_MISMATCH = 0x029d,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_DS_TESSELLATOR_DOMAIN_MISMATCH = 0x029e,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_USE_OF_CENTER_MULTISAMPLE_PATTERN = 0x029f,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_USE_OF_FORCED_SAMPLE_COUNT = 0x02a0,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_PRIMITIVETOPOLOGY = 0x02a1,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_SYSTEMVALUE = 0x02a2,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_OM_DUAL_SOURCE_BLENDING_CAN_ONLY_HAVE_RENDER_TARGET_0 = 0x02a3,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_OM_RENDER_TARGET_DOES_NOT_SUPPORT_BLENDING = 0x02a4,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_PS_OUTPUT_TYPE_MISMATCH = 0x02a5,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_OM_RENDER_TARGET_DOES_NOT_SUPPORT_LOGIC_OPS = 0x02a6,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RENDERTARGETVIEW_NOT_SET = 0x02a7,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_DEPTHSTENCILVIEW_NOT_SET = 0x02a8,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_GS_INPUT_PRIMITIVE_MISMATCH = 0x02a9,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_POSITION_NOT_PRESENT = 0x02aa,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MISSING_ROOT_SIGNATURE_FLAGS = 0x02ab,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_INDEX_BUFFER_PROPERTIES = 0x02ac,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_SAMPLE_DESC = 0x02ad,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_ROOT_SIGNATURE_MISMATCH = 0x02ae,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_DS_ROOT_SIGNATURE_MISMATCH = 0x02af,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_VS_ROOT_SIGNATURE_MISMATCH = 0x02b0,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_GS_ROOT_SIGNATURE_MISMATCH = 0x02b1,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_PS_ROOT_SIGNATURE_MISMATCH = 0x02b2,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MISSING_ROOT_SIGNATURE = 0x02b3,
    D3D12_MESSAGE_ID_EXECUTE_BUNDLE_OPEN_BUNDLE = 0x02b4,
    D3D12_MESSAGE_ID_EXECUTE_BUNDLE_DESCRIPTOR_HEAP_MISMATCH = 0x02b5,
    D3D12_MESSAGE_ID_EXECUTE_BUNDLE_TYPE = 0x02b6,
    D3D12_MESSAGE_ID_DRAW_EMPTY_SCISSOR_RECTANGLE = 0x02b7,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_BLOB_NOT_FOUND = 0x02b8,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_DESERIALIZE_FAILED = 0x02b9,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_INVALID_CONFIGURATION = 0x02ba,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_NOT_SUPPORTED_ON_DEVICE = 0x02bb,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_NULLRESOURCEPROPERTIES = 0x02bc,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_NULLHEAP = 0x02bd,
    D3D12_MESSAGE_ID_GETRESOURCEALLOCATIONINFO_INVALIDRDESCS = 0x02be,
    D3D12_MESSAGE_ID_MAKERESIDENT_NULLOBJECTARRAY = 0x02bf,
    D3D12_MESSAGE_ID_EVICT_NULLOBJECTARRAY = 0x02c1,
    D3D12_MESSAGE_ID_SET_DESCRIPTOR_TABLE_INVALID = 0x02c4,
    D3D12_MESSAGE_ID_SET_ROOT_CONSTANT_INVALID = 0x02c5,
    D3D12_MESSAGE_ID_SET_ROOT_CONSTANT_BUFFER_VIEW_INVALID = 0x02c6,
    D3D12_MESSAGE_ID_SET_ROOT_SHADER_RESOURCE_VIEW_INVALID = 0x02c7,
    D3D12_MESSAGE_ID_SET_ROOT_UNORDERED_ACCESS_VIEW_INVALID = 0x02c8,
    D3D12_MESSAGE_ID_SET_VERTEX_BUFFERS_INVALID_DESC = 0x02c9,
    D3D12_MESSAGE_ID_SET_INDEX_BUFFER_INVALID_DESC = 0x02cb,
    D3D12_MESSAGE_ID_SET_STREAM_OUTPUT_BUFFERS_INVALID_DESC = 0x02cd,
    D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDDIMENSIONALITY = 0x02ce,
    D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDLAYOUT = 0x02cf,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDDIMENSIONALITY = 0x02d0,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDALIGNMENT = 0x02d1,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDMIPLEVELS = 0x02d2,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDSAMPLEDESC = 0x02d3,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDLAYOUT = 0x02d4,
    D3D12_MESSAGE_ID_SET_INDEX_BUFFER_INVALID = 0x02d5,
    D3D12_MESSAGE_ID_SET_VERTEX_BUFFERS_INVALID = 0x02d6,
    D3D12_MESSAGE_ID_SET_STREAM_OUTPUT_BUFFERS_INVALID = 0x02d7,
    D3D12_MESSAGE_ID_SET_RENDER_TARGETS_INVALID = 0x02d8,
    D3D12_MESSAGE_ID_CREATEQUERY_HEAP_INVALID_PARAMETERS = 0x02d9,
    D3D12_MESSAGE_ID_BEGIN_END_QUERY_INVALID_PARAMETERS = 0x02db,
    D3D12_MESSAGE_ID_CLOSE_COMMAND_LIST_OPEN_QUERY = 0x02dc,
    D3D12_MESSAGE_ID_RESOLVE_QUERY_DATA_INVALID_PARAMETERS = 0x02dd,
    D3D12_MESSAGE_ID_SET_PREDICATION_INVALID_PARAMETERS = 0x02de,
    D3D12_MESSAGE_ID_TIMESTAMPS_NOT_SUPPORTED = 0x02df,
    D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDFORMAT = 0x02e1,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDFORMAT = 0x02e2,
    D3D12_MESSAGE_ID_GETCOPYABLEFOOTPRINTS_INVALIDSUBRESOURCERANGE = 0x02e3,
    D3D12_MESSAGE_ID_GETCOPYABLEFOOTPRINTS_INVALIDBASEOFFSET = 0x02e4,
    D3D12_MESSAGE_ID_GETCOPYABLELAYOUT_INVALIDSUBRESOURCERANGE = 0x02e3,
    D3D12_MESSAGE_ID_GETCOPYABLELAYOUT_INVALIDBASEOFFSET = 0x02e4,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_HEAP = 0x02e5,
    D3D12_MESSAGE_ID_CREATE_SAMPLER_INVALID = 0x02e6,
    D3D12_MESSAGE_ID_CREATECOMMANDSIGNATURE_INVALID = 0x02e7,
    D3D12_MESSAGE_ID_EXECUTE_INDIRECT_INVALID_PARAMETERS = 0x02e8,
    D3D12_MESSAGE_ID_GETGPUVIRTUALADDRESS_INVALID_RESOURCE_DIMENSION = 0x02e9,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDCLEARVALUE = 0x032f,
    D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDCLEARVALUEFORMAT = 0x0330,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDCLEARVALUEFORMAT = 0x0331,
    D3D12_MESSAGE_ID_CREATERESOURCE_CLEARVALUEDENORMFLUSH = 0x0332,
    D3D12_MESSAGE_ID_CLEARRENDERTARGETVIEW_MISMATCHINGCLEARVALUE = 0x0334,
    D3D12_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_MISMATCHINGCLEARVALUE = 0x0335,
    D3D12_MESSAGE_ID_MAP_INVALIDHEAP = 0x0336,
    D3D12_MESSAGE_ID_UNMAP_INVALIDHEAP = 0x0337,
    D3D12_MESSAGE_ID_MAP_INVALIDRESOURCE = 0x0338,
    D3D12_MESSAGE_ID_UNMAP_INVALIDRESOURCE = 0x0339,
    D3D12_MESSAGE_ID_MAP_INVALIDSUBRESOURCE = 0x033a,
    D3D12_MESSAGE_ID_UNMAP_INVALIDSUBRESOURCE = 0x033b,
    D3D12_MESSAGE_ID_MAP_INVALIDRANGE = 0x033c,
    D3D12_MESSAGE_ID_UNMAP_INVALIDRANGE = 0x033d,
    D3D12_MESSAGE_ID_MAP_INVALIDDATAPOINTER = 0x0340,
    D3D12_MESSAGE_ID_MAP_INVALIDARG_RETURN = 0x0341,
    D3D12_MESSAGE_ID_MAP_OUTOFMEMORY_RETURN = 0x0342,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_BUNDLENOTSUPPORTED = 0x0343,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_COMMANDLISTMISMATCH = 0x0344,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_OPENCOMMANDLIST = 0x0345,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_FAILEDCOMMANDLIST = 0x0346,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_NULLDST = 0x0347,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALIDDSTRESOURCEDIMENSION = 0x0348,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_DSTRANGEOUTOFBOUNDS = 0x0349,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_NULLSRC = 0x034a,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALIDSRCRESOURCEDIMENSION = 0x034b,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_SRCRANGEOUTOFBOUNDS = 0x034c,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALIDCOPYFLAGS = 0x034d,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_NULLDST = 0x034e,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDDSTTYPE = 0x034f,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTRESOURCEDIMENSION = 0x0350,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTRESOURCE = 0x0351,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTSUBRESOURCE = 0x0352,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTOFFSET = 0x0353,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDDSTFORMAT = 0x0354,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTFORMAT = 0x0355,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTDIMENSIONS = 0x0356,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTROWPITCH = 0x0357,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTPLACEMENT = 0x0358,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTDSPLACEDFOOTPRINTFORMAT = 0x0359,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_DSTREGIONOUTOFBOUNDS = 0x035a,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_NULLSRC = 0x035b,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDSRCTYPE = 0x035c,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCRESOURCEDIMENSION = 0x035d,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCRESOURCE = 0x035e,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCSUBRESOURCE = 0x035f,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCOFFSET = 0x0360,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDSRCFORMAT = 0x0361,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCFORMAT = 0x0362,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCDIMENSIONS = 0x0363,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCROWPITCH = 0x0364,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCPLACEMENT = 0x0365,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCDSPLACEDFOOTPRINTFORMAT = 0x0366,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_SRCREGIONOUTOFBOUNDS = 0x0367,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTCOORDINATES = 0x0368,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCBOX = 0x0369,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_FORMATMISMATCH = 0x036a,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_EMPTYBOX = 0x036b,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDCOPYFLAGS = 0x036c,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALID_SUBRESOURCE_INDEX = 0x036d,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALID_FORMAT = 0x036e,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_RESOURCE_MISMATCH = 0x036f,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALID_SAMPLE_COUNT = 0x0370,
    D3D12_MESSAGE_ID_CREATECOMPUTEPIPELINESTATE_INVALID_SHADER = 0x0371,
    D3D12_MESSAGE_ID_CREATECOMPUTEPIPELINESTATE_CS_ROOT_SIGNATURE_MISMATCH = 0x0372,
    D3D12_MESSAGE_ID_CREATECOMPUTEPIPELINESTATE_MISSING_ROOT_SIGNATURE = 0x0373,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_INVALIDCACHEDBLOB = 0x0374,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBADAPTERMISMATCH = 0x0375,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBDRIVERVERSIONMISMATCH = 0x0376,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBDESCMISMATCH = 0x0377,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBIGNORED = 0x0378,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDHEAP = 0x0379,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDRESOURCE = 0x037a,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDBOX = 0x037b,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDSUBRESOURCE = 0x037c,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_EMPTYBOX = 0x037d,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDHEAP = 0x037e,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDRESOURCE = 0x037f,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDBOX = 0x0380,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDSUBRESOURCE = 0x0381,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_EMPTYBOX = 0x0382,
    D3D12_MESSAGE_ID_TOO_MANY_NODES_SPECIFIED = 0x0383,
    D3D12_MESSAGE_ID_INVALID_NODE_INDEX = 0x0384,
    D3D12_MESSAGE_ID_GETHEAPPROPERTIES_INVALIDRESOURCE = 0x0385,
    D3D12_MESSAGE_ID_NODE_MASK_MISMATCH = 0x0386,
    D3D12_MESSAGE_ID_COMMAND_LIST_OUTOFMEMORY = 0x0387,
    D3D12_MESSAGE_ID_COMMAND_LIST_MULTIPLE_SWAPCHAIN_BUFFER_REFERENCES = 0x0388,
    D3D12_MESSAGE_ID_COMMAND_LIST_TOO_MANY_SWAPCHAIN_REFERENCES = 0x0389,
    D3D12_MESSAGE_ID_COMMAND_QUEUE_TOO_MANY_SWAPCHAIN_REFERENCES = 0x038a,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_WRONGSWAPCHAINBUFFERREFERENCE = 0x038b,
    D3D12_MESSAGE_ID_COMMAND_LIST_SETRENDERTARGETS_INVALIDNUMRENDERTARGETS = 0x038c,
    D3D12_MESSAGE_ID_CREATE_QUEUE_INVALID_TYPE = 0x038d,
    D3D12_MESSAGE_ID_CREATE_QUEUE_INVALID_FLAGS = 0x038e,
    D3D12_MESSAGE_ID_CREATESHAREDRESOURCE_INVALIDFLAGS = 0x038f,
    D3D12_MESSAGE_ID_CREATESHAREDRESOURCE_INVALIDFORMAT = 0x0390,
    D3D12_MESSAGE_ID_CREATESHAREDHEAP_INVALIDFLAGS = 0x0391,
    D3D12_MESSAGE_ID_REFLECTSHAREDPROPERTIES_UNRECOGNIZEDPROPERTIES = 0x0392,
    D3D12_MESSAGE_ID_REFLECTSHAREDPROPERTIES_INVALIDSIZE = 0x0393,
    D3D12_MESSAGE_ID_REFLECTSHAREDPROPERTIES_INVALIDOBJECT = 0x0394,
    D3D12_MESSAGE_ID_KEYEDMUTEX_INVALIDOBJECT = 0x0395,
    D3D12_MESSAGE_ID_KEYEDMUTEX_INVALIDKEY = 0x0396,
    D3D12_MESSAGE_ID_KEYEDMUTEX_WRONGSTATE = 0x0397,
    D3D12_MESSAGE_ID_CREATE_QUEUE_INVALID_PRIORITY = 0x0398,
    D3D12_MESSAGE_ID_OBJECT_DELETED_WHILE_STILL_IN_USE = 0x0399,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_INVALID_FLAGS = 0x039a,
    D3D12_MESSAGE_ID_HEAP_ADDRESS_RANGE_HAS_NO_RESOURCE = 0x039b,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_RENDER_TARGET_DELETED = 0x039c,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_ALL_RENDER_TARGETS_HAVE_UNKNOWN_FORMAT = 0x039d,
    D3D12_MESSAGE_ID_HEAP_ADDRESS_RANGE_INTERSECTS_MULTIPLE_BUFFERS = 0x039e,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_GPU_WRITTEN_READBACK_RESOURCE_MAPPED = 0x039f,
    D3D12_MESSAGE_ID_UNMAP_RANGE_NOT_EMPTY = 0x03a1,
    D3D12_MESSAGE_ID_MAP_INVALID_NULLRANGE = 0x03a2,
    D3D12_MESSAGE_ID_UNMAP_INVALID_NULLRANGE = 0x03a3,
    D3D12_MESSAGE_ID_NO_GRAPHICS_API_SUPPORT = 0x03a4,
    D3D12_MESSAGE_ID_NO_COMPUTE_API_SUPPORT = 0x03a5,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_RESOURCE_FLAGS_NOT_SUPPORTED = 0x03a6,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_ROOT_ARGUMENT_UNINITIALIZED = 0x03a7,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_HEAP_INDEX_OUT_OF_BOUNDS = 0x03a8,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_TABLE_REGISTER_INDEX_OUT_OF_BOUNDS = 0x03a9,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_UNINITIALIZED = 0x03aa,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_TYPE_MISMATCH = 0x03ab,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_SRV_RESOURCE_DIMENSION_MISMATCH = 0x03ac,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_UAV_RESOURCE_DIMENSION_MISMATCH = 0x03ad,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_INCOMPATIBLE_RESOURCE_STATE = 0x03ae,
    D3D12_MESSAGE_ID_COPYRESOURCE_NULLDST = 0x03af,
    D3D12_MESSAGE_ID_COPYRESOURCE_INVALIDDSTRESOURCE = 0x03b0,
    D3D12_MESSAGE_ID_COPYRESOURCE_NULLSRC = 0x03b1,
    D3D12_MESSAGE_ID_COPYRESOURCE_INVALIDSRCRESOURCE = 0x03b2,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_NULLDST = 0x03b3,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALIDDSTRESOURCE = 0x03b4,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_NULLSRC = 0x03b5,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALIDSRCRESOURCE = 0x03b6,
    D3D12_MESSAGE_ID_PIPELINE_STATE_TYPE_MISMATCH = 0x03b7,
    D3D12_MESSAGE_ID_COMMAND_LIST_DISPATCH_ROOT_SIGNATURE_NOT_SET = 0x03b8,
    D3D12_MESSAGE_ID_COMMAND_LIST_DISPATCH_ROOT_SIGNATURE_MISMATCH = 0x03b9,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_ZERO_BARRIERS = 0x03ba,
    D3D12_MESSAGE_ID_BEGIN_END_EVENT_MISMATCH = 0x03bb,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_POSSIBLE_BEFORE_AFTER_MISMATCH = 0x03bc,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISMATCHING_BEGIN_END = 0x03bd,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_INVALID_RESOURCE = 0x03be,
    D3D12_MESSAGE_ID_USE_OF_ZERO_REFCOUNT_OBJECT = 0x03bf,
    D3D12_MESSAGE_ID_OBJECT_EVICTED_WHILE_STILL_IN_USE = 0x03c0,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_ROOT_DESCRIPTOR_ACCESS_OUT_OF_BOUNDS = 0x03c1,
    D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_INVALIDLIBRARYBLOB = 0x03c2,
    D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_DRIVERVERSIONMISMATCH = 0x03c3,
    D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_ADAPTERVERSIONMISMATCH = 0x03c4,
    D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_UNSUPPORTED = 0x03c5,
    D3D12_MESSAGE_ID_CREATE_PIPELINELIBRARY = 0x03c6,
    D3D12_MESSAGE_ID_LIVE_PIPELINELIBRARY = 0x03c7,
    D3D12_MESSAGE_ID_DESTROY_PIPELINELIBRARY = 0x03c8,
    D3D12_MESSAGE_ID_STOREPIPELINE_NONAME = 0x03c9,
    D3D12_MESSAGE_ID_STOREPIPELINE_DUPLICATENAME = 0x03ca,
    D3D12_MESSAGE_ID_LOADPIPELINE_NAMENOTFOUND = 0x03cb,
    D3D12_MESSAGE_ID_LOADPIPELINE_INVALIDDESC = 0x03cc,
    D3D12_MESSAGE_ID_PIPELINELIBRARY_SERIALIZE_NOTENOUGHMEMORY = 0x03cd,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_PS_OUTPUT_RT_OUTPUT_MISMATCH = 0x03ce,
    D3D12_MESSAGE_ID_SETEVENTONMULTIPLEFENCECOMPLETION_INVALIDFLAGS = 0x03cf,
    D3D12_MESSAGE_ID_CREATE_QUEUE_VIDEO_NOT_SUPPORTED = 0x03d0,
    D3D12_MESSAGE_ID_CREATE_COMMAND_ALLOCATOR_VIDEO_NOT_SUPPORTED = 0x03d1,
    D3D12_MESSAGE_ID_CREATEQUERY_HEAP_VIDEO_DECODE_STATISTICS_NOT_SUPPORTED = 0x03d2,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODECOMMANDLIST = 0x03d3,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODER = 0x03d4,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODESTREAM = 0x03d5,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODECOMMANDLIST = 0x03d6,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODER = 0x03d7,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODESTREAM = 0x03d8,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODECOMMANDLIST = 0x03d9,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODER = 0x03da,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODESTREAM = 0x03db,
    D3D12_MESSAGE_ID_DECODE_FRAME_INVALID_PARAMETERS = 0x03dc,
    D3D12_MESSAGE_ID_DEPRECATED_API = 0x03dd,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISMATCHING_COMMAND_LIST_TYPE = 0x03de,
    D3D12_MESSAGE_ID_COMMAND_LIST_DESCRIPTOR_TABLE_NOT_SET = 0x03df,
    D3D12_MESSAGE_ID_COMMAND_LIST_ROOT_CONSTANT_BUFFER_VIEW_NOT_SET = 0x03e0,
    D3D12_MESSAGE_ID_COMMAND_LIST_ROOT_SHADER_RESOURCE_VIEW_NOT_SET = 0x03e1,
    D3D12_MESSAGE_ID_COMMAND_LIST_ROOT_UNORDERED_ACCESS_VIEW_NOT_SET = 0x03e2,
    D3D12_MESSAGE_ID_DISCARD_INVALID_SUBRESOURCE_RANGE = 0x03e3,
    D3D12_MESSAGE_ID_DISCARD_ONE_SUBRESOURCE_FOR_MIPS_WITH_RECTS = 0x03e4,
    D3D12_MESSAGE_ID_DISCARD_NO_RECTS_FOR_NON_TEXTURE2D = 0x03e5,
    D3D12_MESSAGE_ID_COPY_ON_SAME_SUBRESOURCE = 0x03e6,
    D3D12_MESSAGE_ID_SETRESIDENCYPRIORITY_INVALID_PAGEABLE = 0x03e7,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_UNSUPPORTED = 0x03e8,
    D3D12_MESSAGE_ID_STATIC_DESCRIPTOR_INVALID_DESCRIPTOR_CHANGE = 0x03e9,
    D3D12_MESSAGE_ID_DATA_STATIC_DESCRIPTOR_INVALID_DATA_CHANGE = 0x03ea,
    D3D12_MESSAGE_ID_DATA_STATIC_WHILE_SET_AT_EXECUTE_DESCRIPTOR_INVALID_DATA_CHANGE = 0x03eb,
    D3D12_MESSAGE_ID_EXECUTE_BUNDLE_STATIC_DESCRIPTOR_DATA_STATIC_NOT_SET = 0x03ec,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_RESOURCE_ACCESS_OUT_OF_BOUNDS = 0x03ed,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_SAMPLER_MODE_MISMATCH = 0x03ee,
    D3D12_MESSAGE_ID_CREATE_FENCE_INVALID_FLAGS = 0x03ef,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_DUPLICATE_SUBRESOURCE_TRANSITIONS = 0x03f0,
    D3D12_MESSAGE_ID_SETRESIDENCYPRIORITY_INVALID_PRIORITY = 0x03f1,
    D3D12_MESSAGE_ID_CREATE_DESCRIPTOR_HEAP_LARGE_NUM_DESCRIPTORS = 0x03f5,
    D3D12_MESSAGE_ID_BEGIN_EVENT = 0x03f6,
    D3D12_MESSAGE_ID_END_EVENT = 0x03f7,
    D3D12_MESSAGE_ID_CREATEDEVICE_DEBUG_LAYER_STARTUP_OPTIONS = 0x03f8,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_DEPTHBOUNDSTEST_UNSUPPORTED = 0x03f9,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_DUPLICATE_SUBOBJECT = 0x03fa,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_UNKNOWN_SUBOBJECT = 0x03fb,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_ZERO_SIZE_STREAM = 0x03fc,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_INVALID_STREAM = 0x03fd,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CANNOT_DEDUCE_TYPE = 0x03fe,
    D3D12_MESSAGE_ID_COMMAND_LIST_STATIC_DESCRIPTOR_RESOURCE_DIMENSION_MISMATCH = 0x03ff,
    D3D12_MESSAGE_ID_CREATE_COMMAND_QUEUE_INSUFFICIENT_PRIVILEGE_FOR_GLOBAL_REALTIME = 0x0400,
    D3D12_MESSAGE_ID_CREATE_COMMAND_QUEUE_INSUFFICIENT_HARDWARE_SUPPORT_FOR_GLOBAL_REALTIME = 0x0401,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_ARCHITECTURE = 0x0402,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_DST = 0x0403,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DST_RESOURCE_DIMENSION = 0x0404,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_DST_RANGE_OUT_OF_BOUNDS = 0x0405,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_SRC = 0x0406,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_SRC_RESOURCE_DIMENSION = 0x0407,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_SRC_RANGE_OUT_OF_BOUNDS = 0x0408,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_OFFSET_ALIGNMENT = 0x0409,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_DEPENDENT_RESOURCES = 0x040a,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_DEPENDENT_SUBRESOURCE_RANGES = 0x040b,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DEPENDENT_RESOURCE = 0x040c,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DEPENDENT_SUBRESOURCE_RANGE = 0x040d,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_DEPENDENT_SUBRESOURCE_OUT_OF_BOUNDS = 0x040e,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_DEPENDENT_RANGE_OUT_OF_BOUNDS = 0x040f,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_ZERO_DEPENDENCIES = 0x0410,
    D3D12_MESSAGE_ID_DEVICE_CREATE_SHARED_HANDLE_INVALIDARG = 0x0411,
    D3D12_MESSAGE_ID_DESCRIPTOR_HANDLE_WITH_INVALID_RESOURCE = 0x0412,
    D3D12_MESSAGE_ID_SETDEPTHBOUNDS_INVALIDARGS = 0x0413,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_RESOURCE_STATE_IMPRECISE = 0x0414,
    D3D12_MESSAGE_ID_COMMAND_LIST_PIPELINE_STATE_NOT_SET = 0x0415,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_MODEL_MISMATCH = 0x0416,
    D3D12_MESSAGE_ID_OBJECT_ACCESSED_WHILE_STILL_IN_USE = 0x0417,
    D3D12_MESSAGE_ID_PROGRAMMABLE_MSAA_UNSUPPORTED = 0x0418,
    D3D12_MESSAGE_ID_SETSAMPLEPOSITIONS_INVALIDARGS = 0x0419,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCEREGION_INVALID_RECT = 0x041a,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODECOMMANDQUEUE = 0x041b,
    D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSCOMMANDLIST = 0x041c,
    D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSCOMMANDQUEUE = 0x041d,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODECOMMANDQUEUE = 0x041e,
    D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSCOMMANDLIST = 0x041f,
    D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSCOMMANDQUEUE = 0x0420,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODECOMMANDQUEUE = 0x0421,
    D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSCOMMANDLIST = 0x0422,
    D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSCOMMANDQUEUE = 0x0423,
    D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSOR = 0x0424,
    D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSSTREAM = 0x0425,
    D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSOR = 0x0426,
    D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSSTREAM = 0x0427,
    D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSOR = 0x0428,
    D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSSTREAM = 0x0429,
    D3D12_MESSAGE_ID_PROCESS_FRAME_INVALID_PARAMETERS = 0x042a,
    D3D12_MESSAGE_ID_COPY_INVALIDLAYOUT = 0x042b,
    D3D12_MESSAGE_ID_CREATE_CRYPTO_SESSION = 0x042c,
    D3D12_MESSAGE_ID_CREATE_CRYPTO_SESSION_POLICY = 0x042d,
    D3D12_MESSAGE_ID_CREATE_PROTECTED_RESOURCE_SESSION = 0x042e,
    D3D12_MESSAGE_ID_LIVE_CRYPTO_SESSION = 0x042f,
    D3D12_MESSAGE_ID_LIVE_CRYPTO_SESSION_POLICY = 0x0430,
    D3D12_MESSAGE_ID_LIVE_PROTECTED_RESOURCE_SESSION = 0x0431,
    D3D12_MESSAGE_ID_DESTROY_CRYPTO_SESSION = 0x0432,
    D3D12_MESSAGE_ID_DESTROY_CRYPTO_SESSION_POLICY = 0x0433,
    D3D12_MESSAGE_ID_DESTROY_PROTECTED_RESOURCE_SESSION = 0x0434,
    D3D12_MESSAGE_ID_PROTECTED_RESOURCE_SESSION_UNSUPPORTED = 0x0435,
    D3D12_MESSAGE_ID_FENCE_INVALIDOPERATION = 0x0436,
    D3D12_MESSAGE_ID_CREATEQUERY_HEAP_COPY_QUEUE_TIMESTAMPS_NOT_SUPPORTED = 0x0437,
    D3D12_MESSAGE_ID_SAMPLEPOSITIONS_MISMATCH_DEFERRED = 0x0438,
    D3D12_MESSAGE_ID_SAMPLEPOSITIONS_MISMATCH_RECORDTIME_ASSUMEDFROMFIRSTUSE = 0x0439,
    D3D12_MESSAGE_ID_SAMPLEPOSITIONS_MISMATCH_RECORDTIME_ASSUMEDFROMCLEAR = 0x043a,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODERHEAP = 0x043b,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODERHEAP = 0x043c,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODERHEAP = 0x043d,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_INVALIDARG_RETURN = 0x043e,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_OUTOFMEMORY_RETURN = 0x043f,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_INVALIDADDRESS = 0x0440,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_INVALIDHANDLE = 0x0441,
    D3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_INVALID_DEST = 0x0442,
    D3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_INVALID_MODE = 0x0443,
    D3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_INVALID_ALIGNMENT = 0x0444,
    D3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_NOT_SUPPORTED = 0x0445,
    D3D12_MESSAGE_ID_SETVIEWINSTANCEMASK_INVALIDARGS = 0x0446,
    D3D12_MESSAGE_ID_VIEW_INSTANCING_UNSUPPORTED = 0x0447,
    D3D12_MESSAGE_ID_VIEW_INSTANCING_INVALIDARGS = 0x0448,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_MISMATCH_DECODE_REFERENCE_ONLY_FLAG = 0x0449,
    D3D12_MESSAGE_ID_COPYRESOURCE_MISMATCH_DECODE_REFERENCE_ONLY_FLAG = 0x044a,
    D3D12_MESSAGE_ID_CREATE_VIDEO_DECODE_HEAP_CAPS_FAILURE = 0x044b,
    D3D12_MESSAGE_ID_CREATE_VIDEO_DECODE_HEAP_CAPS_UNSUPPORTED = 0x044c,
    D3D12_MESSAGE_ID_VIDEO_DECODE_SUPPORT_INVALID_INPUT = 0x044d,
    D3D12_MESSAGE_ID_CREATE_VIDEO_DECODER_UNSUPPORTED = 0x044e,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_METADATA_ERROR = 0x044f,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_VIEW_INSTANCING_VERTEX_SIZE_EXCEEDED = 0x0450,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RUNTIME_INTERNAL_ERROR = 0x0451,
    D3D12_MESSAGE_ID_NO_VIDEO_API_SUPPORT = 0x0452,
    D3D12_MESSAGE_ID_VIDEO_PROCESS_SUPPORT_INVALID_INPUT = 0x0453,
    D3D12_MESSAGE_ID_CREATE_VIDEO_PROCESSOR_CAPS_FAILURE = 0x0454,
    D3D12_MESSAGE_ID_VIDEO_PROCESS_SUPPORT_UNSUPPORTED_FORMAT = 0x0455,
    D3D12_MESSAGE_ID_VIDEO_DECODE_FRAME_INVALID_ARGUMENT = 0x0456,
    D3D12_MESSAGE_ID_ENQUEUE_MAKE_RESIDENT_INVALID_FLAGS = 0x0457,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_UNSUPPORTED = 0x0458,
    D3D12_MESSAGE_ID_VIDEO_PROCESS_FRAMES_INVALID_ARGUMENT = 0x0459,
    D3D12_MESSAGE_ID_VIDEO_DECODE_SUPPORT_UNSUPPORTED = 0x045a,
    D3D12_MESSAGE_ID_CREATE_COMMANDRECORDER = 0x045b,
    D3D12_MESSAGE_ID_LIVE_COMMANDRECORDER = 0x045c,
    D3D12_MESSAGE_ID_DESTROY_COMMANDRECORDER = 0x045d,
    D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_VIDEO_NOT_SUPPORTED = 0x045e,
    D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_INVALID_SUPPORT_FLAGS = 0x045f,
    D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_INVALID_FLAGS = 0x0460,
    D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_MORE_RECORDERS_THAN_LOGICAL_PROCESSORS = 0x0461,
    D3D12_MESSAGE_ID_CREATE_COMMANDPOOL = 0x0462,
    D3D12_MESSAGE_ID_LIVE_COMMANDPOOL = 0x0463,
    D3D12_MESSAGE_ID_DESTROY_COMMANDPOOL = 0x0464,
    D3D12_MESSAGE_ID_CREATE_COMMAND_POOL_INVALID_FLAGS = 0x0465,
    D3D12_MESSAGE_ID_CREATE_COMMAND_LIST_VIDEO_NOT_SUPPORTED = 0x0466,
    D3D12_MESSAGE_ID_COMMAND_RECORDER_SUPPORT_FLAGS_MISMATCH = 0x0467,
    D3D12_MESSAGE_ID_COMMAND_RECORDER_CONTENTION = 0x0468,
    D3D12_MESSAGE_ID_COMMAND_RECORDER_USAGE_WITH_CREATECOMMANDLIST_COMMAND_LIST = 0x0469,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_USAGE_WITH_CREATECOMMANDLIST1_COMMAND_LIST = 0x046a,
    D3D12_MESSAGE_ID_CANNOT_EXECUTE_EMPTY_COMMAND_LIST = 0x046b,
    D3D12_MESSAGE_ID_CANNOT_RESET_COMMAND_POOL_WITH_OPEN_COMMAND_LISTS = 0x046c,
    D3D12_MESSAGE_ID_CANNOT_USE_COMMAND_RECORDER_WITHOUT_CURRENT_TARGET = 0x046d,
    D3D12_MESSAGE_ID_CANNOT_CHANGE_COMMAND_RECORDER_TARGET_WHILE_RECORDING = 0x046e,
    D3D12_MESSAGE_ID_COMMAND_POOL_SYNC = 0x046f,
    D3D12_MESSAGE_ID_EVICT_UNDERFLOW = 0x0470,
    D3D12_MESSAGE_ID_CREATE_META_COMMAND = 0x0471,
    D3D12_MESSAGE_ID_LIVE_META_COMMAND = 0x0472,
    D3D12_MESSAGE_ID_DESTROY_META_COMMAND = 0x0473,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALID_DST_RESOURCE = 0x0474,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALID_SRC_RESOURCE = 0x0475,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DST_RESOURCE = 0x0476,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_SRC_RESOURCE = 0x0477,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_NULL_BUFFER = 0x0478,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_NULL_RESOURCE_DESC = 0x0479,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_UNSUPPORTED = 0x047a,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_BUFFER_DIMENSION = 0x047b,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_BUFFER_FLAGS = 0x047c,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_BUFFER_OFFSET = 0x047d,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_RESOURCE_DIMENSION = 0x047e,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_RESOURCE_FLAGS = 0x047f,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_OUTOFMEMORY_RETURN = 0x0480,
    D3D12_MESSAGE_ID_CANNOT_CREATE_GRAPHICS_AND_VIDEO_COMMAND_RECORDER = 0x0481,
    D3D12_MESSAGE_ID_UPDATETILEMAPPINGS_POSSIBLY_MISMATCHING_PROPERTIES = 0x0482,
    D3D12_MESSAGE_ID_CREATE_COMMAND_LIST_INVALID_COMMAND_LIST_TYPE = 0x0483,
    D3D12_MESSAGE_ID_CLEARUNORDEREDACCESSVIEW_INCOMPATIBLE_WITH_STRUCTURED_BUFFERS = 0x0484,
    D3D12_MESSAGE_ID_COMPUTE_ONLY_DEVICE_OPERATION_UNSUPPORTED = 0x0485,
    D3D12_MESSAGE_ID_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INVALID = 0x0486,
    D3D12_MESSAGE_ID_EMIT_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_INVALID = 0x0487,
    D3D12_MESSAGE_ID_COPY_RAYTRACING_ACCELERATION_STRUCTURE_INVALID = 0x0488,
    D3D12_MESSAGE_ID_DISPATCH_RAYS_INVALID = 0x0489,
    D3D12_MESSAGE_ID_GET_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO_INVALID = 0x048a,
    D3D12_MESSAGE_ID_CREATE_LIFETIMETRACKER = 0x048b,
    D3D12_MESSAGE_ID_LIVE_LIFETIMETRACKER = 0x048c,
    D3D12_MESSAGE_ID_DESTROY_LIFETIMETRACKER = 0x048d,
    D3D12_MESSAGE_ID_DESTROYOWNEDOBJECT_OBJECTNOTOWNED = 0x048e,
    D3D12_MESSAGE_ID_CREATE_TRACKEDWORKLOAD = 0x048f,
    D3D12_MESSAGE_ID_LIVE_TRACKEDWORKLOAD = 0x0490,
    D3D12_MESSAGE_ID_DESTROY_TRACKEDWORKLOAD = 0x0491,
    D3D12_MESSAGE_ID_RENDER_PASS_ERROR = 0x0492,
    D3D12_MESSAGE_ID_META_COMMAND_ID_INVALID = 0x0493,
    D3D12_MESSAGE_ID_META_COMMAND_UNSUPPORTED_PARAMS = 0x0494,
    D3D12_MESSAGE_ID_META_COMMAND_FAILED_ENUMERATION = 0x0495,
    D3D12_MESSAGE_ID_META_COMMAND_PARAMETER_SIZE_MISMATCH = 0x0496,
    D3D12_MESSAGE_ID_UNINITIALIZED_META_COMMAND = 0x0497,
    D3D12_MESSAGE_ID_META_COMMAND_INVALID_GPU_VIRTUAL_ADDRESS = 0x0498,
    D3D12_MESSAGE_ID_CREATE_VIDEOENCODECOMMANDLIST = 0x0499,
    D3D12_MESSAGE_ID_LIVE_VIDEOENCODECOMMANDLIST = 0x049a,
    D3D12_MESSAGE_ID_DESTROY_VIDEOENCODECOMMANDLIST = 0x049b,
    D3D12_MESSAGE_ID_CREATE_VIDEOENCODECOMMANDQUEUE = 0x049c,
    D3D12_MESSAGE_ID_LIVE_VIDEOENCODECOMMANDQUEUE = 0x049d,
    D3D12_MESSAGE_ID_DESTROY_VIDEOENCODECOMMANDQUEUE = 0x049e,
    D3D12_MESSAGE_ID_CREATE_VIDEOMOTIONESTIMATOR = 0x049f,
    D3D12_MESSAGE_ID_LIVE_VIDEOMOTIONESTIMATOR = 0x04a0,
    D3D12_MESSAGE_ID_DESTROY_VIDEOMOTIONESTIMATOR = 0x04a1,
    D3D12_MESSAGE_ID_CREATE_VIDEOMOTIONVECTORHEAP = 0x04a2,
    D3D12_MESSAGE_ID_LIVE_VIDEOMOTIONVECTORHEAP = 0x04a3,
    D3D12_MESSAGE_ID_DESTROY_VIDEOMOTIONVECTORHEAP = 0x04a4,
    D3D12_MESSAGE_ID_MULTIPLE_TRACKED_WORKLOADS = 0x04a5,
    D3D12_MESSAGE_ID_MULTIPLE_TRACKED_WORKLOAD_PAIRS = 0x04a6,
    D3D12_MESSAGE_ID_OUT_OF_ORDER_TRACKED_WORKLOAD_PAIR = 0x04a7,
    D3D12_MESSAGE_ID_CANNOT_ADD_TRACKED_WORKLOAD = 0x04a8,
    D3D12_MESSAGE_ID_INCOMPLETE_TRACKED_WORKLOAD_PAIR = 0x04a9,
    D3D12_MESSAGE_ID_CREATE_STATE_OBJECT_ERROR = 0x04aa,
    D3D12_MESSAGE_ID_GET_SHADER_IDENTIFIER_ERROR = 0x04ab,
    D3D12_MESSAGE_ID_GET_SHADER_STACK_SIZE_ERROR = 0x04ac,
    D3D12_MESSAGE_ID_GET_PIPELINE_STACK_SIZE_ERROR = 0x04ad,
    D3D12_MESSAGE_ID_SET_PIPELINE_STACK_SIZE_ERROR = 0x04ae,
    D3D12_MESSAGE_ID_GET_SHADER_IDENTIFIER_SIZE_INVALID = 0x04af,
    D3D12_MESSAGE_ID_CHECK_DRIVER_MATCHING_IDENTIFIER_INVALID = 0x04b0,
    D3D12_MESSAGE_ID_CHECK_DRIVER_MATCHING_IDENTIFIER_DRIVER_REPORTED_ISSUE = 0x04b1,
    D3D12_MESSAGE_ID_RENDER_PASS_INVALID_RESOURCE_BARRIER = 0x04b2,
    D3D12_MESSAGE_ID_RENDER_PASS_DISALLOWED_API_CALLED = 0x04b3,
    D3D12_MESSAGE_ID_RENDER_PASS_CANNOT_NEST_RENDER_PASSES = 0x04b4,
    D3D12_MESSAGE_ID_RENDER_PASS_CANNOT_END_WITHOUT_BEGIN = 0x04b5,
    D3D12_MESSAGE_ID_RENDER_PASS_CANNOT_CLOSE_COMMAND_LIST = 0x04b6,
    D3D12_MESSAGE_ID_RENDER_PASS_GPU_WORK_WHILE_SUSPENDED = 0x04b7,
    D3D12_MESSAGE_ID_RENDER_PASS_MISMATCHING_SUSPEND_RESUME = 0x04b8,
    D3D12_MESSAGE_ID_RENDER_PASS_NO_PRIOR_SUSPEND_WITHIN_EXECUTECOMMANDLISTS = 0x04b9,
    D3D12_MESSAGE_ID_RENDER_PASS_NO_SUBSEQUENT_RESUME_WITHIN_EXECUTECOMMANDLISTS = 0x04ba,
    D3D12_MESSAGE_ID_TRACKED_WORKLOAD_COMMAND_QUEUE_MISMATCH = 0x04bb,
    D3D12_MESSAGE_ID_TRACKED_WORKLOAD_NOT_SUPPORTED = 0x04bc,
    D3D12_MESSAGE_ID_RENDER_PASS_MISMATCHING_NO_ACCESS = 0x04bd,
    D3D12_MESSAGE_ID_RENDER_PASS_UNSUPPORTED_RESOLVE = 0x04be,
    D3D12_MESSAGE_ID_CLEARUNORDEREDACCESSVIEW_INVALID_RESOURCE_PTR = 0x04bf,
    D3D12_MESSAGE_ID_WINDOWS7_FENCE_OUTOFORDER_SIGNAL = 0x04c0,
    D3D12_MESSAGE_ID_WINDOWS7_FENCE_OUTOFORDER_WAIT = 0x04c1,
    D3D12_MESSAGE_ID_VIDEO_CREATE_MOTION_ESTIMATOR_INVALID_ARGUMENT = 0x04c2,
    D3D12_MESSAGE_ID_VIDEO_CREATE_MOTION_VECTOR_HEAP_INVALID_ARGUMENT = 0x04c3,
    D3D12_MESSAGE_ID_ESTIMATE_MOTION_INVALID_ARGUMENT = 0x04c4,
    D3D12_MESSAGE_ID_RESOLVE_MOTION_VECTOR_HEAP_INVALID_ARGUMENT = 0x04c5,
    D3D12_MESSAGE_ID_GETGPUVIRTUALADDRESS_INVALID_HEAP_TYPE = 0x04c6,
    D3D12_MESSAGE_ID_SET_BACKGROUND_PROCESSING_MODE_INVALID_ARGUMENT = 0x04c7,
    D3D12_MESSAGE_ID_CREATE_COMMAND_LIST_INVALID_COMMAND_LIST_TYPE_FOR_FEATURE_LEVEL = 0x04c8,
    D3D12_MESSAGE_ID_CREATE_VIDEOEXTENSIONCOMMAND = 0x04c9,
    D3D12_MESSAGE_ID_LIVE_VIDEOEXTENSIONCOMMAND = 0x04ca,
    D3D12_MESSAGE_ID_DESTROY_VIDEOEXTENSIONCOMMAND = 0x04cb,
    D3D12_MESSAGE_ID_INVALID_VIDEO_EXTENSION_COMMAND_ID = 0x04cc,
    D3D12_MESSAGE_ID_VIDEO_EXTENSION_COMMAND_INVALID_ARGUMENT = 0x04cd,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_NOT_UNIQUE_IN_DXIL_LIBRARY = 0x04ce,
    D3D12_MESSAGE_ID_VARIABLE_SHADING_RATE_NOT_ALLOWED_WITH_TIR = 0x04cf,
    D3D12_MESSAGE_ID_GEOMETRY_SHADER_OUTPUTTING_BOTH_VIEWPORT_ARRAY_INDEX_AND_SHADING_RATE_NOT_SUPPORTED_ON_DEVICE = 0x04d0,
    D3D12_MESSAGE_ID_RSSETSHADING_RATE_INVALID_SHADING_RATE = 0x04d1,
    D3D12_MESSAGE_ID_RSSETSHADING_RATE_SHADING_RATE_NOT_PERMITTED_BY_CAP = 0x04d2,
    D3D12_MESSAGE_ID_RSSETSHADING_RATE_INVALID_COMBINER = 0x04d3,
    D3D12_MESSAGE_ID_RSSETSHADINGRATEIMAGE_REQUIRES_TIER_2 = 0x04d4,
    D3D12_MESSAGE_ID_RSSETSHADINGRATE_REQUIRES_TIER_1 = 0x04d5,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_FORMAT = 0x04d6,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_ARRAY_SIZE = 0x04d7,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_MIP_LEVEL = 0x04d8,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_SAMPLE_COUNT = 0x04d9,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_SAMPLE_QUALITY = 0x04da,
    D3D12_MESSAGE_ID_NON_RETAIL_SHADER_MODEL_WONT_VALIDATE = 0x04db,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_AS_ROOT_SIGNATURE_MISMATCH = 0x04dc,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MS_ROOT_SIGNATURE_MISMATCH = 0x04dd,
    D3D12_MESSAGE_ID_ADD_TO_STATE_OBJECT_ERROR = 0x04de,
    D3D12_MESSAGE_ID_CREATE_PROTECTED_RESOURCE_SESSION_INVALID_ARGUMENT = 0x04df,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MS_PSO_DESC_MISMATCH = 0x04e0,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_MS_INCOMPLETE_TYPE = 0x04e1,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_AS_NOT_MS_MISMATCH = 0x04e2,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MS_NOT_PS_MISMATCH = 0x04e3,
    D3D12_MESSAGE_ID_NONZERO_SAMPLER_FEEDBACK_MIP_REGION_WITH_INCOMPATIBLE_FORMAT = 0x04e4,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INPUTLAYOUT_SHADER_MISMATCH = 0x04e5,
    D3D12_MESSAGE_ID_EMPTY_DISPATCH = 0x04e6,
    D3D12_MESSAGE_ID_RESOURCE_FORMAT_REQUIRES_SAMPLER_FEEDBACK_CAPABILITY = 0x04e7,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_MIP_REGION = 0x04e8,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_DIMENSION = 0x04e9,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_SAMPLE_COUNT = 0x04ea,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_SAMPLE_QUALITY = 0x04eb,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_LAYOUT = 0x04ec,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_REQUIRES_UNORDERED_ACCESS_FLAG = 0x04ed,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_CREATE_UAV_NULL_ARGUMENTS = 0x04ee,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_UAV_REQUIRES_SAMPLER_FEEDBACK_CAPABILITY = 0x04ef,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_CREATE_UAV_REQUIRES_FEEDBACK_MAP_FORMAT = 0x04f0,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_INVALIDSHADERBYTECODE = 0x04f1,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_OUTOFMEMORY = 0x04f2,
    D3D12_MESSAGE_ID_CREATEMESHSHADERWITHSTREAMOUTPUT_INVALIDSHADERTYPE = 0x04f3,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_SAMPLER_FEEDBACK_TRANSCODE_INVALID_FORMAT = 0x04f4,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_SAMPLER_FEEDBACK_INVALID_MIP_LEVEL_COUNT = 0x04f5,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_SAMPLER_FEEDBACK_TRANSCODE_ARRAY_SIZE_MISMATCH = 0x04f6,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_CREATE_UAV_MISMATCHING_TARGETED_RESOURCE = 0x04f7,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_OUTPUTEXCEEDSMAXSIZE = 0x04f8,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_GROUPSHAREDEXCEEDSMAXSIZE = 0x04f9,
    D3D12_MESSAGE_ID_VERTEX_SHADER_OUTPUTTING_BOTH_VIEWPORT_ARRAY_INDEX_AND_SHADING_RATE_NOT_SUPPORTED_ON_DEVICE = 0x04fa,
    D3D12_MESSAGE_ID_MESH_SHADER_OUTPUTTING_BOTH_VIEWPORT_ARRAY_INDEX_AND_SHADING_RATE_NOT_SUPPORTED_ON_DEVICE = 0x04fb,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_MISMATCHEDASMSPAYLOADSIZE = 0x04fc,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_UNBOUNDED_STATIC_DESCRIPTORS = 0x04fd,
    D3D12_MESSAGE_ID_CREATEAMPLIFICATIONSHADER_INVALIDSHADERBYTECODE = 0x04fe,
    D3D12_MESSAGE_ID_CREATEAMPLIFICATIONSHADER_OUTOFMEMORY = 0x04ff,
    D3D12_MESSAGE_ID_D3D12_MESSAGES_END
} D3D12_MESSAGE_ID;

typedef enum D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE
{
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_NONE = 0x0,
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_STATE_TRACKING_ONLY = 0x1,
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_UNGUARDED_VALIDATION = 0x2,
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_GUARDED_VALIDATION = 0x3,
    NUM_D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODES = 0x4,
} D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE;

typedef enum D3D12_GPU_BASED_VALIDATION_FLAGS
{
    D3D12_GPU_BASED_VALIDATION_FLAGS_NONE = 0x00,
    D3D12_GPU_BASED_VALIDATION_FLAGS_DISABLE_STATE_TRACKING = 0x01,
} D3D12_GPU_BASED_VALIDATION_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_GPU_BASED_VALIDATION_FLAGS)")

typedef enum D3D12_RLDO_FLAGS
{
    D3D12_RLDO_NONE = 0x0,
    D3D12_RLDO_SUMMARY = 0x1,
    D3D12_RLDO_DETAIL = 0x2,
    D3D12_RLDO_IGNORE_INTERNAL = 0x4,
} D3D12_RLDO_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RLDO_FLAGS)")

typedef enum D3D12_DEBUG_DEVICE_PARAMETER_TYPE
{
    D3D12_DEBUG_DEVICE_PARAMETER_FEATURE_FLAGS,
    D3D12_DEBUG_DEVICE_PARAMETER_GPU_BASED_VALIDATION_SETTINGS,
    D3D12_DEBUG_DEVICE_PARAMETER_GPU_SLOWDOWN_PERFORMANCE_FACTOR,
} D3D12_DEBUG_DEVICE_PARAMETER_TYPE;

typedef enum D3D12_DEBUG_FEATURE
{
    D3D12_DEBUG_FEATURE_NONE = 0x00,
    D3D12_DEBUG_FEATURE_ALLOW_BEHAVIOR_CHANGING_DEBUG_AIDS = 0x01,
    D3D12_DEBUG_FEATURE_CONSERVATIVE_RESOURCE_STATE_TRACKING = 0x02,
    D3D12_DEBUG_FEATURE_DISABLE_VIRTUALIZED_BUNDLES_VALIDATION = 0x04,
    D3D12_DEBUG_FEATURE_EMULATE_WINDOWS7 = 0x08,
} D3D12_DEBUG_FEATURE;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_DEBUG_FEATURE)")

typedef enum D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS
{
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_NONE = 0x00,
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_FRONT_LOAD_CREATE_TRACKING_ONLY_SHADERS = 0x01,
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_FRONT_LOAD_CREATE_UNGUARDED_VALIDATION_SHADERS = 0x02,
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_FRONT_LOAD_CREATE_GUARDED_VALIDATION_SHADERS = 0x04,
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS_VALID_MASK = 0x07
} D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS)")

typedef enum D3D12_MESSAGE_CALLBACK_FLAGS
{
    D3D12_MESSAGE_CALLBACK_FLAG_NONE = 0x00,
    D3D12_MESSAGE_CALLBACK_IGNORE_FILTERS = 0x01,
} D3D12_MESSAGE_CALLBACK_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_MESSAGE_CALLBACK_FLAGS)")

typedef struct D3D12_DEBUG_DEVICE_GPU_BASED_VALIDATION_SETTINGS
{
    UINT MaxMessagesPerCommandList;
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE DefaultShaderPatchMode;
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS PipelineStateCreateFlags;
} D3D12_DEBUG_DEVICE_GPU_BASED_VALIDATION_SETTINGS;

typedef struct D3D12_DEBUG_DEVICE_GPU_SLOWDOWN_PERFORMANCE_FACTOR
{
    FLOAT SlowdownFactor;
} D3D12_DEBUG_DEVICE_GPU_SLOWDOWN_PERFORMANCE_FACTOR;

typedef struct D3D12_MESSAGE
{
    D3D12_MESSAGE_CATEGORY Category;
    D3D12_MESSAGE_SEVERITY Severity;
    D3D12_MESSAGE_ID ID;
    const char *pDescription;
    SIZE_T DescriptionByteLength;
} D3D12_MESSAGE;

typedef struct D3D12_INFO_QUEUE_FILTER_DESC
{
    UINT NumCategories;
    D3D12_MESSAGE_CATEGORY *pCategoryList;
    UINT NumSeverities;
    D3D12_MESSAGE_SEVERITY *pSeverityList;
    UINT NumIDs;
    D3D12_MESSAGE_ID *pIDList;
} D3D12_INFO_QUEUE_FILTER_DESC;

typedef struct D3D12_INFO_QUEUE_FILTER
{
    D3D12_INFO_QUEUE_FILTER_DESC AllowList;
    D3D12_INFO_QUEUE_FILTER_DESC DenyList;
} D3D12_INFO_QUEUE_FILTER;

[
    uuid(344488b7-6846-474b-b989-f027448245e0),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Debug : IUnknown
{
    void EnableDebugLayer();
}

[
    uuid(affaa4ca-63fe-4d8e-b8ad-159000af4304),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Debug1 : IUnknown
{
    void EnableDebugLayer();
    void SetEnableGPUBasedValidation(BOOL enable);
    void SetEnableSynchronizedCommandQueueValidation(BOOL enable);
}

[
    uuid(93a665c4-a3b2-4e5d-b692-a26ae14e3374),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Debug2 : IUnknown
{
    void SetGPUBasedValidationFlags(D3D12_GPU_BASED_VALIDATION_FLAGS flags);
}

[
    uuid(5cf4e58f-f671-4ff1-a542-3686e3d153d1),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Debug3 : ID3D12Debug
{
    void SetEnableGPUBasedValidation(BOOL enable);
    void SetEnableSynchronizedCommandQueueValidation(BOOL enable);
    void SetGPUBasedValidationFlags(D3D12_GPU_BASED_VALIDATION_FLAGS flags);
}

[
    uuid(014b816e-9ec5-4a2f-a845-ffbe441ce13a),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Debug4 : ID3D12Debug3
{
    void DisableDebugLayer();
}

[
    uuid(548d6b12-09fa-40e0-9069-5dcd589a52c9),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Debug5 : ID3D12Debug4
{
    void SetEnableAutoName(BOOL enable);
}

[
    uuid(3febd6dd-**************-e45f9e28923e),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DebugDevice : IUnknown
{
    HRESULT SetFeatureMask(D3D12_DEBUG_FEATURE mask);
    D3D12_DEBUG_FEATURE GetFeatureMask();
    HRESULT ReportLiveDeviceObjects(D3D12_RLDO_FLAGS flags);
}

[
    uuid(a9b71770-d099-4a65-a698-3dee10020f88),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DebugDevice1 : IUnknown
{
    HRESULT SetDebugParameter(D3D12_DEBUG_DEVICE_PARAMETER_TYPE type, const void *data, UINT size);
    HRESULT GetDebugParameter(D3D12_DEBUG_DEVICE_PARAMETER_TYPE type, void *data, UINT size);
    HRESULT ReportLiveDeviceObjects(D3D12_RLDO_FLAGS flags);
}

[
    uuid(60eccbc1-378d-4df1-894c-f8ac5ce4d7dd),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DebugDevice2 : ID3D12DebugDevice
{
    HRESULT SetDebugParameter(D3D12_DEBUG_DEVICE_PARAMETER_TYPE type, const void *data, UINT size);
    HRESULT GetDebugParameter(D3D12_DEBUG_DEVICE_PARAMETER_TYPE type, void *data, UINT size);
}

[
    uuid(0adf7d52-929c-4e61-addb-ffed30de66ef),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12SharingContract : IUnknown
{
    void Present(ID3D12Resource *resource, UINT sub_resource, HWND window);
    void SharedFenceSignal(ID3D12Fence *fence, UINT64 fence_value);
    void BeginCapturableWork(REFGUID guid);
    void EndCapturableWork(REFGUID guid);
}

[
    uuid(0742a90b-c387-483f-b946-30a7e4e61458),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12InfoQueue : IUnknown
{
    HRESULT SetMessageCountLimit([in] UINT64 limit);
    void ClearStoredMessages();
    HRESULT GetMessage([in] UINT64 index, [out] D3D12_MESSAGE *message, [in, out] SIZE_T *length);
    UINT64 GetNumMessagesAllowedByStorageFilter();
    UINT64 GetNumMessagesDeniedByStorageFilter();
    UINT64 GetNumStoredMessages();
    UINT64 GetNumStoredMessagesAllowedByRetrievalFilter();
    UINT64 GetNumMessagesDiscardedByMessageCountLimit();
    UINT64 GetMessageCountLimit();
    HRESULT AddStorageFilterEntries([in] D3D12_INFO_QUEUE_FILTER *filter);
    HRESULT GetStorageFilter([out] D3D12_INFO_QUEUE_FILTER *filter, [in, out] SIZE_T *length);
    void ClearStorageFilter();
    HRESULT PushEmptyStorageFilter();
    HRESULT PushCopyOfStorageFilter();
    HRESULT PushStorageFilter([in] D3D12_INFO_QUEUE_FILTER *filter);
    void PopStorageFilter();
    UINT GetStorageFilterStackSize();
    HRESULT AddRetrievalFilterEntries([in] D3D12_INFO_QUEUE_FILTER *filter);
    HRESULT GetRetrievalFilter([out] D3D12_INFO_QUEUE_FILTER *filter, [in, out] SIZE_T *length);
    void ClearRetrievalFilter();
    HRESULT PushEmptyRetrievalFilter();
    HRESULT PushCopyOfRetrievalFilter();
    HRESULT PushRetrievalFilter([in] D3D12_INFO_QUEUE_FILTER *filter);
    void PopRetrievalFilter();
    UINT GetRetrievalFilterStackSize();
    HRESULT AddMessage([in] D3D12_MESSAGE_CATEGORY category, [in] D3D12_MESSAGE_SEVERITY severity,
            [in] D3D12_MESSAGE_ID id, [in] const char *description);
    HRESULT AddApplicationMessage([in] D3D12_MESSAGE_SEVERITY severity, [in] const char *description);
    HRESULT SetBreakOnCategory([in] D3D12_MESSAGE_CATEGORY category, [in] BOOL enable);
    HRESULT SetBreakOnSeverity([in] D3D12_MESSAGE_SEVERITY severity, [in] BOOL enable);
    HRESULT SetBreakOnID([in] D3D12_MESSAGE_ID id, [in] BOOL enable);
    BOOL GetBreakOnCategory([in] D3D12_MESSAGE_CATEGORY category);
    BOOL GetBreakOnSeverity([in] D3D12_MESSAGE_SEVERITY severity);
    BOOL GetBreakOnID([in] D3D12_MESSAGE_ID id);
    void SetMuteDebugOutput([in] BOOL mute);
    BOOL GetMuteDebugOutput();
}

typedef void (__stdcall *D3D12MessageFunc)(D3D12_MESSAGE_CATEGORY category,
    D3D12_MESSAGE_SEVERITY severity, D3D12_MESSAGE_ID id, const char *description, void *context);

[
    uuid(2852dd88-b484-4c0c-b6b1-67168500e600),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12InfoQueue1 : ID3D12InfoQueue
{
    HRESULT RegisterMessageCallback([in] D3D12MessageFunc func,
        [in] D3D12_MESSAGE_CALLBACK_FLAGS flags, [in] void *context, [in, out] DWORD *cookie);
    HRESULT UnregisterMessageCallback([in] DWORD cookie);
}
