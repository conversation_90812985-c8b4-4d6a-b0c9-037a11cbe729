------------------------------------------------------------------------
-- dqRotate.decTest -- rotate decQuad coefficient left or right       --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check
dqrot001 rotate                                   0    0  ->  0
dqrot002 rotate                                   0    2  ->  0
dqrot003 rotate                                   1    2  ->  100
dqrot004 rotate                                   1   33  ->  1000000000000000000000000000000000
dqrot005 rotate                                   1   34  ->  1
dqrot006 rotate                                   1   -1  ->  1000000000000000000000000000000000
dqrot007 rotate                                   0   -2  ->  0
dqrot008 rotate  1234567890123456789012345678901234   -1  ->  4123456789012345678901234567890123
dqrot009 rotate  1234567890123456789012345678901234   -33 ->  2345678901234567890123456789012341
dqrot010 rotate  1234567890123456789012345678901234   -34 ->  1234567890123456789012345678901234
dqrot011 rotate  9934567890123456789012345678901234   -33 ->  9345678901234567890123456789012349
dqrot012 rotate  9934567890123456789012345678901234   -34 ->  9934567890123456789012345678901234

-- rhs must be an integer
dqrot015 rotate        1    1.5    -> NaN Invalid_operation
dqrot016 rotate        1    1.0    -> NaN Invalid_operation
dqrot017 rotate        1    0.1    -> NaN Invalid_operation
dqrot018 rotate        1    0.0    -> NaN Invalid_operation
dqrot019 rotate        1    1E+1   -> NaN Invalid_operation
dqrot020 rotate        1    1E+99  -> NaN Invalid_operation
dqrot021 rotate        1    Inf    -> NaN Invalid_operation
dqrot022 rotate        1    -Inf   -> NaN Invalid_operation
-- and |rhs| <= precision
dqrot025 rotate        1    -1000  -> NaN Invalid_operation
dqrot026 rotate        1    -35    -> NaN Invalid_operation
dqrot027 rotate        1     35    -> NaN Invalid_operation
dqrot028 rotate        1     1000  -> NaN Invalid_operation

-- full pattern
dqrot030 rotate  1234567890123456789012345678901234         -34  -> 1234567890123456789012345678901234
dqrot031 rotate  1234567890123456789012345678901234         -33  -> 2345678901234567890123456789012341
dqrot032 rotate  1234567890123456789012345678901234         -32  -> 3456789012345678901234567890123412
dqrot033 rotate  1234567890123456789012345678901234         -31  -> 4567890123456789012345678901234123
dqrot034 rotate  1234567890123456789012345678901234         -30  -> 5678901234567890123456789012341234
dqrot035 rotate  1234567890123456789012345678901234         -29  -> 6789012345678901234567890123412345
dqrot036 rotate  1234567890123456789012345678901234         -28  -> 7890123456789012345678901234123456
dqrot037 rotate  1234567890123456789012345678901234         -27  -> 8901234567890123456789012341234567
dqrot038 rotate  1234567890123456789012345678901234         -26  -> 9012345678901234567890123412345678
dqrot039 rotate  1234567890123456789012345678901234         -25  ->  123456789012345678901234123456789
dqrot040 rotate  1234567890123456789012345678901234         -24  -> 1234567890123456789012341234567890
dqrot041 rotate  1234567890123456789012345678901234         -23  -> 2345678901234567890123412345678901
dqrot042 rotate  1234567890123456789012345678901234         -22  -> 3456789012345678901234123456789012
dqrot043 rotate  1234567890123456789012345678901234         -21  -> 4567890123456789012341234567890123
dqrot044 rotate  1234567890123456789012345678901234         -20  -> 5678901234567890123412345678901234
dqrot045 rotate  1234567890123456789012345678901234         -19  -> 6789012345678901234123456789012345
dqrot047 rotate  1234567890123456789012345678901234         -18  -> 7890123456789012341234567890123456
dqrot048 rotate  1234567890123456789012345678901234         -17  -> 8901234567890123412345678901234567
dqrot049 rotate  1234567890123456789012345678901234         -16  -> 9012345678901234123456789012345678
dqrot050 rotate  1234567890123456789012345678901234         -15  ->  123456789012341234567890123456789
dqrot051 rotate  1234567890123456789012345678901234         -14  -> 1234567890123412345678901234567890
dqrot052 rotate  1234567890123456789012345678901234         -13  -> 2345678901234123456789012345678901
dqrot053 rotate  1234567890123456789012345678901234         -12  -> 3456789012341234567890123456789012
dqrot054 rotate  1234567890123456789012345678901234         -11  -> 4567890123412345678901234567890123
dqrot055 rotate  1234567890123456789012345678901234         -10  -> 5678901234123456789012345678901234
dqrot056 rotate  1234567890123456789012345678901234         -9   -> 6789012341234567890123456789012345
dqrot057 rotate  1234567890123456789012345678901234         -8   -> 7890123412345678901234567890123456
dqrot058 rotate  1234567890123456789012345678901234         -7   -> 8901234123456789012345678901234567
dqrot059 rotate  1234567890123456789012345678901234         -6   -> 9012341234567890123456789012345678
dqrot060 rotate  1234567890123456789012345678901234         -5   ->  123412345678901234567890123456789
dqrot061 rotate  1234567890123456789012345678901234         -4   -> 1234123456789012345678901234567890
dqrot062 rotate  1234567890123456789012345678901234         -3   -> 2341234567890123456789012345678901
dqrot063 rotate  1234567890123456789012345678901234         -2   -> 3412345678901234567890123456789012
dqrot064 rotate  1234567890123456789012345678901234         -1   -> 4123456789012345678901234567890123
dqrot065 rotate  1234567890123456789012345678901234         -0   -> 1234567890123456789012345678901234

dqrot066 rotate  1234567890123456789012345678901234         +0   -> 1234567890123456789012345678901234
dqrot067 rotate  1234567890123456789012345678901234         +1   -> 2345678901234567890123456789012341
dqrot068 rotate  1234567890123456789012345678901234         +2   -> 3456789012345678901234567890123412
dqrot069 rotate  1234567890123456789012345678901234         +3   -> 4567890123456789012345678901234123
dqrot070 rotate  1234567890123456789012345678901234         +4   -> 5678901234567890123456789012341234
dqrot071 rotate  1234567890123456789012345678901234         +5   -> 6789012345678901234567890123412345
dqrot072 rotate  1234567890123456789012345678901234         +6   -> 7890123456789012345678901234123456
dqrot073 rotate  1234567890123456789012345678901234         +7   -> 8901234567890123456789012341234567
dqrot074 rotate  1234567890123456789012345678901234         +8   -> 9012345678901234567890123412345678
dqrot075 rotate  1234567890123456789012345678901234         +9   ->  123456789012345678901234123456789
dqrot076 rotate  1234567890123456789012345678901234         +10  -> 1234567890123456789012341234567890
dqrot077 rotate  1234567890123456789012345678901234         +11  -> 2345678901234567890123412345678901
dqrot078 rotate  1234567890123456789012345678901234         +12  -> 3456789012345678901234123456789012
dqrot079 rotate  1234567890123456789012345678901234         +13  -> 4567890123456789012341234567890123
dqrot080 rotate  1234567890123456789012345678901234         +14  -> 5678901234567890123412345678901234
dqrot081 rotate  1234567890123456789012345678901234         +15  -> 6789012345678901234123456789012345
dqrot082 rotate  1234567890123456789012345678901234         +16  -> 7890123456789012341234567890123456
dqrot083 rotate  1234567890123456789012345678901234         +17  -> 8901234567890123412345678901234567
dqrot084 rotate  1234567890123456789012345678901234         +18  -> 9012345678901234123456789012345678
dqrot085 rotate  1234567890123456789012345678901234         +19  ->  123456789012341234567890123456789
dqrot086 rotate  1234567890123456789012345678901234         +20  -> 1234567890123412345678901234567890
dqrot087 rotate  1234567890123456789012345678901234         +21  -> 2345678901234123456789012345678901
dqrot088 rotate  1234567890123456789012345678901234         +22  -> 3456789012341234567890123456789012
dqrot089 rotate  1234567890123456789012345678901234         +23  -> 4567890123412345678901234567890123
dqrot090 rotate  1234567890123456789012345678901234         +24  -> 5678901234123456789012345678901234
dqrot091 rotate  1234567890123456789012345678901234         +25  -> 6789012341234567890123456789012345
dqrot092 rotate  1234567890123456789012345678901234         +26  -> 7890123412345678901234567890123456
dqrot093 rotate  1234567890123456789012345678901234         +27  -> 8901234123456789012345678901234567
dqrot094 rotate  1234567890123456789012345678901234         +28  -> 9012341234567890123456789012345678
dqrot095 rotate  1234567890123456789012345678901234         +29  ->  123412345678901234567890123456789
dqrot096 rotate  1234567890123456789012345678901234         +30  -> 1234123456789012345678901234567890
dqrot097 rotate  1234567890123456789012345678901234         +31  -> 2341234567890123456789012345678901
dqrot098 rotate  1234567890123456789012345678901234         +32  -> 3412345678901234567890123456789012
dqrot099 rotate  1234567890123456789012345678901234         +33  -> 4123456789012345678901234567890123
dqrot100 rotate  1234567890123456789012345678901234         +34  -> 1234567890123456789012345678901234

-- zeros
dqrot270 rotate  0E-10              +29   ->   0E-10
dqrot271 rotate  0E-10              -29   ->   0E-10
dqrot272 rotate  0.000              +29   ->   0.000
dqrot273 rotate  0.000              -29   ->   0.000
dqrot274 rotate  0E+10              +29   ->   0E+10
dqrot275 rotate  0E+10              -29   ->   0E+10
dqrot276 rotate -0E-10              +29   ->  -0E-10
dqrot277 rotate -0E-10              -29   ->  -0E-10
dqrot278 rotate -0.000              +29   ->  -0.000
dqrot279 rotate -0.000              -29   ->  -0.000
dqrot280 rotate -0E+10              +29   ->  -0E+10
dqrot281 rotate -0E+10              -29   ->  -0E+10

-- Nmax, Nmin, Ntiny
dqrot141 rotate  9.999999999999999999999999999999999E+6144     -1  -> 9.999999999999999999999999999999999E+6144
dqrot142 rotate  9.999999999999999999999999999999999E+6144     -33 -> 9.999999999999999999999999999999999E+6144
dqrot143 rotate  9.999999999999999999999999999999999E+6144      1  -> 9.999999999999999999999999999999999E+6144
dqrot144 rotate  9.999999999999999999999999999999999E+6144      33 -> 9.999999999999999999999999999999999E+6144
dqrot145 rotate  1E-6143                                       -1  -> 1.000000000000000000000000000000000E-6110
dqrot146 rotate  1E-6143                                       -33 -> 1.0E-6142
dqrot147 rotate  1E-6143                                        1  -> 1.0E-6142
dqrot148 rotate  1E-6143                                        33 -> 1.000000000000000000000000000000000E-6110
dqrot151 rotate  1.000000000000000000000000000000000E-6143     -1  -> 1.00000000000000000000000000000000E-6144
dqrot152 rotate  1.000000000000000000000000000000000E-6143     -33 -> 1E-6176
dqrot153 rotate  1.000000000000000000000000000000000E-6143      1  -> 1E-6176
dqrot154 rotate  1.000000000000000000000000000000000E-6143      33 -> 1.00000000000000000000000000000000E-6144
dqrot155 rotate  9.000000000000000000000000000000000E-6143     -1  -> 9.00000000000000000000000000000000E-6144
dqrot156 rotate  9.000000000000000000000000000000000E-6143     -33 -> 9E-6176
dqrot157 rotate  9.000000000000000000000000000000000E-6143      1  -> 9E-6176
dqrot158 rotate  9.000000000000000000000000000000000E-6143      33 -> 9.00000000000000000000000000000000E-6144
dqrot160 rotate  1E-6176                                       -1  -> 1.000000000000000000000000000000000E-6143
dqrot161 rotate  1E-6176                                       -33 -> 1.0E-6175
dqrot162 rotate  1E-6176                                        1  -> 1.0E-6175
dqrot163 rotate  1E-6176                                        33 -> 1.000000000000000000000000000000000E-6143
--  negatives
dqrot171 rotate -9.999999999999999999999999999999999E+6144     -1  -> -9.999999999999999999999999999999999E+6144
dqrot172 rotate -9.999999999999999999999999999999999E+6144     -33 -> -9.999999999999999999999999999999999E+6144
dqrot173 rotate -9.999999999999999999999999999999999E+6144      1  -> -9.999999999999999999999999999999999E+6144
dqrot174 rotate -9.999999999999999999999999999999999E+6144      33 -> -9.999999999999999999999999999999999E+6144
dqrot175 rotate -1E-6143                                       -1  -> -1.000000000000000000000000000000000E-6110
dqrot176 rotate -1E-6143                                       -33 -> -1.0E-6142
dqrot177 rotate -1E-6143                                        1  -> -1.0E-6142
dqrot178 rotate -1E-6143                                        33 -> -1.000000000000000000000000000000000E-6110
dqrot181 rotate -1.000000000000000000000000000000000E-6143     -1  -> -1.00000000000000000000000000000000E-6144
dqrot182 rotate -1.000000000000000000000000000000000E-6143     -33 -> -1E-6176
dqrot183 rotate -1.000000000000000000000000000000000E-6143      1  -> -1E-6176
dqrot184 rotate -1.000000000000000000000000000000000E-6143      33 -> -1.00000000000000000000000000000000E-6144
dqrot185 rotate -9.000000000000000000000000000000000E-6143     -1  -> -9.00000000000000000000000000000000E-6144
dqrot186 rotate -9.000000000000000000000000000000000E-6143     -33 -> -9E-6176
dqrot187 rotate -9.000000000000000000000000000000000E-6143      1  -> -9E-6176
dqrot188 rotate -9.000000000000000000000000000000000E-6143      33 -> -9.00000000000000000000000000000000E-6144
dqrot190 rotate -1E-6176                                       -1  -> -1.000000000000000000000000000000000E-6143
dqrot191 rotate -1E-6176                                       -33 -> -1.0E-6175
dqrot192 rotate -1E-6176                                        1  -> -1.0E-6175
dqrot193 rotate -1E-6176                                        33 -> -1.000000000000000000000000000000000E-6143

-- more negatives (of sanities)
dqrot201 rotate                                  -0    0  -> -0
dqrot202 rotate                                  -0    2  -> -0
dqrot203 rotate                                  -1    2  -> -100
dqrot204 rotate                                  -1   33  -> -1000000000000000000000000000000000
dqrot205 rotate                                  -1   34  -> -1
dqrot206 rotate                                  -1   -1  -> -1000000000000000000000000000000000
dqrot207 rotate                                  -0   -2  -> -0
dqrot208 rotate -1234567890123456789012345678901234   -1  -> -4123456789012345678901234567890123
dqrot209 rotate -1234567890123456789012345678901234   -33 -> -2345678901234567890123456789012341
dqrot210 rotate -1234567890123456789012345678901234   -34 -> -1234567890123456789012345678901234
dqrot211 rotate -9934567890123456789012345678901234   -33 -> -9345678901234567890123456789012349
dqrot212 rotate -9934567890123456789012345678901234   -34 -> -9934567890123456789012345678901234


-- Specials; NaNs are handled as usual
dqrot781 rotate -Inf  -8     -> -Infinity
dqrot782 rotate -Inf  -1     -> -Infinity
dqrot783 rotate -Inf  -0     -> -Infinity
dqrot784 rotate -Inf   0     -> -Infinity
dqrot785 rotate -Inf   1     -> -Infinity
dqrot786 rotate -Inf   8     -> -Infinity
dqrot787 rotate -1000 -Inf   -> NaN Invalid_operation
dqrot788 rotate -Inf  -Inf   -> NaN Invalid_operation
dqrot789 rotate -1    -Inf   -> NaN Invalid_operation
dqrot790 rotate -0    -Inf   -> NaN Invalid_operation
dqrot791 rotate  0    -Inf   -> NaN Invalid_operation
dqrot792 rotate  1    -Inf   -> NaN Invalid_operation
dqrot793 rotate  1000 -Inf   -> NaN Invalid_operation
dqrot794 rotate  Inf  -Inf   -> NaN Invalid_operation

dqrot800 rotate  Inf  -Inf   -> NaN Invalid_operation
dqrot801 rotate  Inf  -8     -> Infinity
dqrot802 rotate  Inf  -1     -> Infinity
dqrot803 rotate  Inf  -0     -> Infinity
dqrot804 rotate  Inf   0     -> Infinity
dqrot805 rotate  Inf   1     -> Infinity
dqrot806 rotate  Inf   8     -> Infinity
dqrot807 rotate  Inf   Inf   -> NaN Invalid_operation
dqrot808 rotate -1000  Inf   -> NaN Invalid_operation
dqrot809 rotate -Inf   Inf   -> NaN Invalid_operation
dqrot810 rotate -1     Inf   -> NaN Invalid_operation
dqrot811 rotate -0     Inf   -> NaN Invalid_operation
dqrot812 rotate  0     Inf   -> NaN Invalid_operation
dqrot813 rotate  1     Inf   -> NaN Invalid_operation
dqrot814 rotate  1000  Inf   -> NaN Invalid_operation
dqrot815 rotate  Inf   Inf   -> NaN Invalid_operation

dqrot821 rotate  NaN -Inf    ->  NaN
dqrot822 rotate  NaN -1000   ->  NaN
dqrot823 rotate  NaN -1      ->  NaN
dqrot824 rotate  NaN -0      ->  NaN
dqrot825 rotate  NaN  0      ->  NaN
dqrot826 rotate  NaN  1      ->  NaN
dqrot827 rotate  NaN  1000   ->  NaN
dqrot828 rotate  NaN  Inf    ->  NaN
dqrot829 rotate  NaN  NaN    ->  NaN
dqrot830 rotate -Inf  NaN    ->  NaN
dqrot831 rotate -1000 NaN    ->  NaN
dqrot832 rotate -1    NaN    ->  NaN
dqrot833 rotate -0    NaN    ->  NaN
dqrot834 rotate  0    NaN    ->  NaN
dqrot835 rotate  1    NaN    ->  NaN
dqrot836 rotate  1000 NaN    ->  NaN
dqrot837 rotate  Inf  NaN    ->  NaN

dqrot841 rotate  sNaN -Inf   ->  NaN  Invalid_operation
dqrot842 rotate  sNaN -1000  ->  NaN  Invalid_operation
dqrot843 rotate  sNaN -1     ->  NaN  Invalid_operation
dqrot844 rotate  sNaN -0     ->  NaN  Invalid_operation
dqrot845 rotate  sNaN  0     ->  NaN  Invalid_operation
dqrot846 rotate  sNaN  1     ->  NaN  Invalid_operation
dqrot847 rotate  sNaN  1000  ->  NaN  Invalid_operation
dqrot848 rotate  sNaN  NaN   ->  NaN  Invalid_operation
dqrot849 rotate  sNaN sNaN   ->  NaN  Invalid_operation
dqrot850 rotate  NaN  sNaN   ->  NaN  Invalid_operation
dqrot851 rotate -Inf  sNaN   ->  NaN  Invalid_operation
dqrot852 rotate -1000 sNaN   ->  NaN  Invalid_operation
dqrot853 rotate -1    sNaN   ->  NaN  Invalid_operation
dqrot854 rotate -0    sNaN   ->  NaN  Invalid_operation
dqrot855 rotate  0    sNaN   ->  NaN  Invalid_operation
dqrot856 rotate  1    sNaN   ->  NaN  Invalid_operation
dqrot857 rotate  1000 sNaN   ->  NaN  Invalid_operation
dqrot858 rotate  Inf  sNaN   ->  NaN  Invalid_operation
dqrot859 rotate  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dqrot861 rotate  NaN1   -Inf    ->  NaN1
dqrot862 rotate +NaN2   -1000   ->  NaN2
dqrot863 rotate  NaN3    1000   ->  NaN3
dqrot864 rotate  NaN4    Inf    ->  NaN4
dqrot865 rotate  NaN5   +NaN6   ->  NaN5
dqrot866 rotate -Inf     NaN7   ->  NaN7
dqrot867 rotate -1000    NaN8   ->  NaN8
dqrot868 rotate  1000    NaN9   ->  NaN9
dqrot869 rotate  Inf    +NaN10  ->  NaN10
dqrot871 rotate  sNaN11  -Inf   ->  NaN11  Invalid_operation
dqrot872 rotate  sNaN12  -1000  ->  NaN12  Invalid_operation
dqrot873 rotate  sNaN13   1000  ->  NaN13  Invalid_operation
dqrot874 rotate  sNaN14   NaN17 ->  NaN14  Invalid_operation
dqrot875 rotate  sNaN15  sNaN18 ->  NaN15  Invalid_operation
dqrot876 rotate  NaN16   sNaN19 ->  NaN19  Invalid_operation
dqrot877 rotate -Inf    +sNaN20 ->  NaN20  Invalid_operation
dqrot878 rotate -1000    sNaN21 ->  NaN21  Invalid_operation
dqrot879 rotate  1000    sNaN22 ->  NaN22  Invalid_operation
dqrot880 rotate  Inf     sNaN23 ->  NaN23  Invalid_operation
dqrot881 rotate +NaN25  +sNaN24 ->  NaN24  Invalid_operation
dqrot882 rotate -NaN26    NaN28 -> -NaN26
dqrot883 rotate -sNaN27  sNaN29 -> -NaN27  Invalid_operation
dqrot884 rotate  1000    -NaN30 -> -NaN30
dqrot885 rotate  1000   -sNaN31 -> -NaN31  Invalid_operation
