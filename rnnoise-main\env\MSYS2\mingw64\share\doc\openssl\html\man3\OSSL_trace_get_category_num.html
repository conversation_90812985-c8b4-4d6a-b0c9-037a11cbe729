<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_trace_get_category_num</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_trace_get_category_num, OSSL_trace_get_category_name - OpenSSL tracing information functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/trace.h&gt;

int OSSL_trace_get_category_num(const char *name);
const char *OSSL_trace_get_category_name(int num);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>OSSL_trace_get_category_num() gives the category number corresponding to the given <code>name</code>.</p>

<p>OSSL_trace_get_category_name() gives the category name corresponding to the given <code>num</code>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_trace_get_category_num() returns the category number if the given <code>name</code> is a recognised category name, otherwise -1.</p>

<p>OSSL_trace_get_category_name() returns the category name if the given <code>num</code> is a recognised category number, otherwise NULL.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The OpenSSL Tracing API was added ino OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


