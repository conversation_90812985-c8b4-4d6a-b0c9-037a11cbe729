/*
 * Copyright 2016 <PERSON><PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";
import "dxgi.idl";
import "d3dcommon.idl";

cpp_quote("#ifndef _D3D12_CONSTANTS")
cpp_quote("#define _D3D12_CONSTANTS")

const UINT D3D12_16BIT_INDEX_STRIP_CUT_VALUE = 0xffff;
const UINT D3D12_32BIT_INDEX_STRIP_CUT_VALUE = 0xffffffff;
const UINT D3D12_8BIT_INDEX_STRIP_CUT_VALUE = 0xff;
const UINT D3D12_APPEND_ALIGNED_ELEMENT = 0xffffffff;
const UINT D3D12_ARRAY_AXIS_ADDRESS_RANGE_BIT_COUNT = 9;
const UINT D3D12_CLIP_OR_CULL_DISTANCE_COUNT = 8;
const UINT D3D12_CLIP_OR_CULL_DISTANCE_ELEMENT_COUNT = 2;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT = 14;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_COMPONENTS = 4;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_HW_SLOT_COUNT = 15;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_PARTIAL_UPDATE_EXTENTS_BYTE_ALIGNMENT = 16;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COMPONENTS = 4;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COUNT = 15;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READS_PER_INST = 1;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READ_PORTS = 1;
const UINT D3D12_COMMONSHADER_FLOWCONTROL_NESTING_LIMIT = 64;
const UINT D3D12_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COMPONENTS = 4;
const UINT D3D12_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COUNT = 1;
const UINT D3D12_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READS_PER_INST = 1;
const UINT D3D12_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READ_PORTS = 1;
const UINT D3D12_COMMONSHADER_IMMEDIATE_VALUE_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_REGISTER_COMPONENTS = 1;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_REGISTER_COUNT = 128;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_REGISTER_READS_PER_INST = 1;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_REGISTER_READ_PORTS = 1;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT = 128;
const UINT D3D12_COMMONSHADER_SAMPLER_REGISTER_COMPONENTS = 1;
const UINT D3D12_COMMONSHADER_SAMPLER_REGISTER_COUNT = 16;
const UINT D3D12_COMMONSHADER_SAMPLER_REGISTER_READS_PER_INST = 1;
const UINT D3D12_COMMONSHADER_SAMPLER_REGISTER_READ_PORTS = 1;
const UINT D3D12_COMMONSHADER_SAMPLER_SLOT_COUNT = 16;
const UINT D3D12_COMMONSHADER_SUBROUTINE_NESTING_LIMIT = 32;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_COMPONENTS = 4;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_COUNT = 4096;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_READS_PER_INST = 3;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_READ_PORTS = 3;
const UINT D3D12_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MAX = 10;
const INT D3D12_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MIN = -10;
const INT D3D12_COMMONSHADER_TEXEL_OFFSET_MAX_NEGATIVE = -8;
const UINT D3D12_COMMONSHADER_TEXEL_OFFSET_MAX_POSITIVE = 7;
const UINT D3D12_CONSTANT_BUFFER_DATA_PLACEMENT_ALIGNMENT = 256;
const UINT D3D12_CS_4_X_BUCKET00_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 256;
const UINT D3D12_CS_4_X_BUCKET00_MAX_NUM_THREADS_PER_GROUP = 64;
const UINT D3D12_CS_4_X_BUCKET01_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 240;
const UINT D3D12_CS_4_X_BUCKET01_MAX_NUM_THREADS_PER_GROUP = 68;
const UINT D3D12_CS_4_X_BUCKET02_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 224;
const UINT D3D12_CS_4_X_BUCKET02_MAX_NUM_THREADS_PER_GROUP = 72;
const UINT D3D12_CS_4_X_BUCKET03_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 208;
const UINT D3D12_CS_4_X_BUCKET03_MAX_NUM_THREADS_PER_GROUP = 76;
const UINT D3D12_CS_4_X_BUCKET04_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 192;
const UINT D3D12_CS_4_X_BUCKET04_MAX_NUM_THREADS_PER_GROUP = 84;
const UINT D3D12_CS_4_X_BUCKET05_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 176;
const UINT D3D12_CS_4_X_BUCKET05_MAX_NUM_THREADS_PER_GROUP = 92;
const UINT D3D12_CS_4_X_BUCKET06_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 160;
const UINT D3D12_CS_4_X_BUCKET06_MAX_NUM_THREADS_PER_GROUP = 100;
const UINT D3D12_CS_4_X_BUCKET07_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 144;
const UINT D3D12_CS_4_X_BUCKET07_MAX_NUM_THREADS_PER_GROUP = 112;
const UINT D3D12_CS_4_X_BUCKET08_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 128;
const UINT D3D12_CS_4_X_BUCKET08_MAX_NUM_THREADS_PER_GROUP = 128;
const UINT D3D12_CS_4_X_BUCKET09_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 112;
const UINT D3D12_CS_4_X_BUCKET09_MAX_NUM_THREADS_PER_GROUP = 144;
const UINT D3D12_CS_4_X_BUCKET10_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 96;
const UINT D3D12_CS_4_X_BUCKET10_MAX_NUM_THREADS_PER_GROUP = 168;
const UINT D3D12_CS_4_X_BUCKET11_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 80;
const UINT D3D12_CS_4_X_BUCKET11_MAX_NUM_THREADS_PER_GROUP = 204;
const UINT D3D12_CS_4_X_BUCKET12_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 64;
const UINT D3D12_CS_4_X_BUCKET12_MAX_NUM_THREADS_PER_GROUP = 256;
const UINT D3D12_CS_4_X_BUCKET13_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 48;
const UINT D3D12_CS_4_X_BUCKET13_MAX_NUM_THREADS_PER_GROUP = 340;
const UINT D3D12_CS_4_X_BUCKET14_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 32;
const UINT D3D12_CS_4_X_BUCKET14_MAX_NUM_THREADS_PER_GROUP = 512;
const UINT D3D12_CS_4_X_BUCKET15_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 16;
const UINT D3D12_CS_4_X_BUCKET15_MAX_NUM_THREADS_PER_GROUP = 768;
const UINT D3D12_CS_4_X_DISPATCH_MAX_THREAD_GROUPS_IN_Z_DIMENSION = 1;
const UINT D3D12_CS_4_X_RAW_UAV_BYTE_ALIGNMENT = 256;
const UINT D3D12_CS_4_X_THREAD_GROUP_MAX_THREADS_PER_GROUP = 768;
const UINT D3D12_CS_4_X_THREAD_GROUP_MAX_X = 768;
const UINT D3D12_CS_4_X_THREAD_GROUP_MAX_Y = 768;
const UINT D3D12_CS_4_X_UAV_REGISTER_COUNT = 1;
const UINT D3D12_CS_DISPATCH_MAX_THREAD_GROUPS_PER_DIMENSION = 65535;
const UINT D3D12_CS_TGSM_REGISTER_COUNT = 8192;
const UINT D3D12_CS_TGSM_REGISTER_READS_PER_INST = 1;
const UINT D3D12_CS_TGSM_RESOURCE_REGISTER_COMPONENTS = 1;
const UINT D3D12_CS_TGSM_RESOURCE_REGISTER_READ_PORTS = 1;
const UINT D3D12_CS_THREADGROUPID_REGISTER_COMPONENTS = 3;
const UINT D3D12_CS_THREADGROUPID_REGISTER_COUNT = 1;
const UINT D3D12_CS_THREADIDINGROUPFLATTENED_REGISTER_COMPONENTS = 1;
const UINT D3D12_CS_THREADIDINGROUPFLATTENED_REGISTER_COUNT = 1;
const UINT D3D12_CS_THREADIDINGROUP_REGISTER_COMPONENTS = 3;
const UINT D3D12_CS_THREADIDINGROUP_REGISTER_COUNT = 1;
const UINT D3D12_CS_THREADID_REGISTER_COMPONENTS = 3;
const UINT D3D12_CS_THREADID_REGISTER_COUNT = 1;
const UINT D3D12_CS_THREAD_GROUP_MAX_THREADS_PER_GROUP = 1024;
const UINT D3D12_CS_THREAD_GROUP_MAX_X = 1024;
const UINT D3D12_CS_THREAD_GROUP_MAX_Y = 1024;
const UINT D3D12_CS_THREAD_GROUP_MAX_Z = 64;
const UINT D3D12_CS_THREAD_GROUP_MIN_X = 1;
const UINT D3D12_CS_THREAD_GROUP_MIN_Y = 1;
const UINT D3D12_CS_THREAD_GROUP_MIN_Z = 1;
const UINT D3D12_CS_THREAD_LOCAL_TEMP_REGISTER_POOL = 16384;
cpp_quote("#define D3D12_DEFAULT_BLEND_FACTOR_ALPHA (1.0f)")
cpp_quote("#define D3D12_DEFAULT_BLEND_FACTOR_BLUE (1.0f)")
cpp_quote("#define D3D12_DEFAULT_BLEND_FACTOR_GREEN (1.0f)")
cpp_quote("#define D3D12_DEFAULT_BLEND_FACTOR_RED (1.0f)")
cpp_quote("#define D3D12_DEFAULT_BORDER_COLOR_COMPONENT (0.0f)")
const UINT D3D12_DEFAULT_DEPTH_BIAS = 0;
cpp_quote("#define D3D12_DEFAULT_DEPTH_BIAS_CLAMP (0.0f)")
const UINT D3D12_DEFAULT_MAX_ANISOTROPY = 16;
cpp_quote("#define D3D12_DEFAULT_MIP_LOD_BIAS (0.0f)")
const UINT D3D12_DEFAULT_MSAA_RESOURCE_PLACEMENT_ALIGNMENT = 4194304;
const UINT D3D12_DEFAULT_RENDER_TARGET_ARRAY_INDEX = 0;
const UINT D3D12_DEFAULT_RESOURCE_PLACEMENT_ALIGNMENT = 65536;
const UINT D3D12_DEFAULT_SAMPLE_MASK = 0xffffffff;
const UINT D3D12_DEFAULT_SCISSOR_ENDX = 0;
const UINT D3D12_DEFAULT_SCISSOR_ENDY = 0;
const UINT D3D12_DEFAULT_SCISSOR_STARTX = 0;
const UINT D3D12_DEFAULT_SCISSOR_STARTY = 0;
cpp_quote("#define D3D12_DEFAULT_SLOPE_SCALED_DEPTH_BIAS (0.0f)")
const UINT D3D12_DEFAULT_STENCIL_READ_MASK = 0xff;
const UINT D3D12_DEFAULT_STENCIL_REFERENCE = 0;
const UINT D3D12_DEFAULT_STENCIL_WRITE_MASK = 0xff;
const UINT D3D12_DEFAULT_VIEWPORT_AND_SCISSORRECT_INDEX = 0;
const UINT D3D12_DEFAULT_VIEWPORT_HEIGHT = 0;
cpp_quote("#define D3D12_DEFAULT_VIEWPORT_MAX_DEPTH (0.0f)")
cpp_quote("#define D3D12_DEFAULT_VIEWPORT_MIN_DEPTH (0.0f)")
const UINT D3D12_DEFAULT_VIEWPORT_TOPLEFTX = 0;
const UINT D3D12_DEFAULT_VIEWPORT_TOPLEFTY = 0;
const UINT D3D12_DEFAULT_VIEWPORT_WIDTH = 0;
const UINT D3D12_DESCRIPTOR_RANGE_OFFSET_APPEND = 0xffffffff;
const UINT D3D12_DRIVER_RESERVED_REGISTER_SPACE_VALUES_END = 0xfffffff7;
const UINT D3D12_DRIVER_RESERVED_REGISTER_SPACE_VALUES_START = 0xfffffff0;
const UINT D3D12_DS_INPUT_CONTROL_POINTS_MAX_TOTAL_SCALARS = 3968;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_COMPONENTS = 4;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_COUNT = 32;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_READ_PORTS = 1;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_COMPONENTS = 3;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_COUNT = 1;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_READ_PORTS = 1;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_COMPONENTS = 4;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_COUNT = 32;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_READ_PORTS = 1;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_COUNT = 1;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_DS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_DS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_OUTPUT_REGISTER_COUNT = 32;
cpp_quote("#define D3D12_FLOAT16_FUSED_TOLERANCE_IN_ULP (0.6)")
cpp_quote("#define D3D12_FLOAT32_MAX (3.402823466e+38f)")
cpp_quote("#define D3D12_FLOAT32_TO_INTEGER_TOLERANCE_IN_ULP (0.6f)")
cpp_quote("#define D3D12_FLOAT_TO_SRGB_EXPONENT_DENOMINATOR (2.4f)")
cpp_quote("#define D3D12_FLOAT_TO_SRGB_EXPONENT_NUMERATOR (1.0f)")
cpp_quote("#define D3D12_FLOAT_TO_SRGB_OFFSET (0.055f)")
cpp_quote("#define D3D12_FLOAT_TO_SRGB_SCALE_1 (12.92f)")
cpp_quote("#define D3D12_FLOAT_TO_SRGB_SCALE_2 (1.055f)")
cpp_quote("#define D3D12_FLOAT_TO_SRGB_THRESHOLD (0.0031308f)")
cpp_quote("#define D3D12_FTOI_INSTRUCTION_MAX_INPUT (2147483647.999f)")
cpp_quote("#define D3D12_FTOI_INSTRUCTION_MIN_INPUT (-2147483648.999f)")
cpp_quote("#define D3D12_FTOU_INSTRUCTION_MAX_INPUT (4294967295.999f)")
cpp_quote("#define D3D12_FTOU_INSTRUCTION_MIN_INPUT (0.0f)")
const UINT D3D12_GS_INPUT_INSTANCE_ID_READS_PER_INST = 2;
const UINT D3D12_GS_INPUT_INSTANCE_ID_READ_PORTS = 1;
const UINT D3D12_GS_INPUT_INSTANCE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_GS_INPUT_INSTANCE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_GS_INPUT_INSTANCE_ID_REGISTER_COUNT = 1;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_COMPONENTS = 1;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_COUNT = 1;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_READS_PER_INST = 2;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_READ_PORTS = 1;
const UINT D3D12_GS_INPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_GS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_GS_INPUT_REGISTER_COUNT = 32;
const UINT D3D12_GS_INPUT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_GS_INPUT_REGISTER_READ_PORTS = 1;
const UINT D3D12_GS_INPUT_REGISTER_VERTICES = 32;
const UINT D3D12_GS_MAX_INSTANCE_COUNT = 32;
const UINT D3D12_GS_MAX_OUTPUT_VERTEX_COUNT_ACROSS_INSTANCES = 1024;
const UINT D3D12_GS_OUTPUT_ELEMENTS = 32;
const UINT D3D12_GS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_GS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_GS_OUTPUT_REGISTER_COUNT = 32;
const UINT D3D12_HS_CONTROL_POINT_PHASE_INPUT_REGISTER_COUNT = 32;
const UINT D3D12_HS_CONTROL_POINT_PHASE_OUTPUT_REGISTER_COUNT = 32;
const UINT D3D12_HS_CONTROL_POINT_REGISTER_COMPONENTS = 4;
const UINT D3D12_HS_CONTROL_POINT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_CONTROL_POINT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_CONTROL_POINT_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_FORK_PHASE_INSTANCE_COUNT_UPPER_BOUND = 0xffffffff;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_COUNT = 1;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_COUNT = 1;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_COUNT = 1;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_JOIN_PHASE_INSTANCE_COUNT_UPPER_BOUND = 0xffffffff;
cpp_quote("#define D3D12_HS_MAXTESSFACTOR_LOWER_BOUND (1.0f)")
cpp_quote("#define D3D12_HS_MAXTESSFACTOR_UPPER_BOUND (64.0f)")
const UINT D3D12_HS_OUTPUT_CONTROL_POINTS_MAX_TOTAL_SCALARS = 3968;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_COUNT = 1;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_COMPONENTS = 4;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_COUNT = 32;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_SCALAR_COMPONENTS = 128;
const UINT D3D12_IA_DEFAULT_INDEX_BUFFER_OFFSET_IN_BYTES = 0;
const UINT D3D12_IA_DEFAULT_PRIMITIVE_TOPOLOGY = 0;
const UINT D3D12_IA_DEFAULT_VERTEX_BUFFER_OFFSET_IN_BYTES = 0;
const UINT D3D12_IA_INDEX_INPUT_RESOURCE_SLOT_COUNT = 1;
const UINT D3D12_IA_INSTANCE_ID_BIT_COUNT = 32;
const UINT D3D12_IA_INTEGER_ARITHMETIC_BIT_COUNT = 32;
const UINT D3D12_IA_PATCH_MAX_CONTROL_POINT_COUNT = 32;
const UINT D3D12_IA_PRIMITIVE_ID_BIT_COUNT = 32;
const UINT D3D12_IA_VERTEX_ID_BIT_COUNT = 32;
const UINT D3D12_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT = 32;
const UINT D3D12_IA_VERTEX_INPUT_STRUCTURE_ELEMENTS_COMPONENTS = 128;
const UINT D3D12_IA_VERTEX_INPUT_STRUCTURE_ELEMENT_COUNT = 32;
const UINT D3D12_INTEGER_DIVIDE_BY_ZERO_QUOTIENT = 0xffffffff;
const UINT D3D12_INTEGER_DIVIDE_BY_ZERO_REMAINDER = 0xffffffff;
const UINT D3D12_KEEP_RENDER_TARGETS_AND_DEPTH_STENCIL = 0xffffffff;
const UINT D3D12_KEEP_UNORDERED_ACCESS_VIEWS = 0xffffffff;
cpp_quote("#define D3D12_LINEAR_GAMMA (1.0f)")
const UINT D3D12_MAJOR_VERSION = 12;
cpp_quote("#define D3D12_MAX_BORDER_COLOR_COMPONENT (1.0f)")
cpp_quote("#define D3D12_MAX_DEPTH (1.0f)")
const UINT D3D12_MAX_LIVE_STATIC_SAMPLERS = 2032;
const UINT D3D12_MAX_MAXANISOTROPY = 16;
const UINT D3D12_MAX_MULTISAMPLE_SAMPLE_COUNT = 32;
cpp_quote("#define D3D12_MAX_POSITION_VALUE (3.402823466e+34f)")
const UINT D3D12_MAX_ROOT_COST = 64;
const UINT D3D12_MAX_SHADER_VISIBLE_DESCRIPTOR_HEAP_SIZE_TIER_1 = 1000000;
const UINT D3D12_MAX_SHADER_VISIBLE_DESCRIPTOR_HEAP_SIZE_TIER_2 = 1000000;
const UINT D3D12_MAX_SHADER_VISIBLE_SAMPLER_HEAP_SIZE = 2048;
const UINT D3D12_MAX_TEXTURE_DIMENSION_2_TO_EXP = 17;
const UINT D3D12_MAX_VIEW_INSTANCE_COUNT = 4;
const UINT D3D12_MINOR_VERSION = 0;
cpp_quote("#define D3D12_MIN_BORDER_COLOR_COMPONENT (0.0f)")
cpp_quote("#define D3D12_MIN_DEPTH (0.0f)")
const UINT D3D12_MIN_MAXANISOTROPY = 0;
cpp_quote("#define D3D12_MIP_LOD_BIAS_MAX (15.99f)")
cpp_quote("#define D3D12_MIP_LOD_BIAS_MIN (-16.0f)")
const UINT D3D12_MIP_LOD_FRACTIONAL_BIT_COUNT = 8;
const UINT D3D12_MIP_LOD_RANGE_BIT_COUNT = 8;
cpp_quote("#define D3D12_MULTISAMPLE_ANTIALIAS_LINE_WIDTH (1.4f)")
const UINT D3D12_NONSAMPLE_FETCH_OUT_OF_RANGE_ACCESS_RESULT = 0;
const UINT D3D12_OS_RESERVED_REGISTER_SPACE_VALUES_END = 0xffffffff;
const UINT D3D12_OS_RESERVED_REGISTER_SPACE_VALUES_START = 0xfffffff8;
const UINT D3D12_PACKED_TILE = 0xffffffff;
const UINT D3D12_PIXEL_ADDRESS_RANGE_BIT_COUNT = 15;
const UINT D3D12_PREVIEW_SDK_VERSION = 702;
const UINT D3D12_PRE_SCISSOR_PIXEL_ADDRESS_RANGE_BIT_COUNT = 16;
const UINT D3D12_PS_CS_UAV_REGISTER_COMPONENTS = 1;
const UINT D3D12_PS_CS_UAV_REGISTER_COUNT = 8;
const UINT D3D12_PS_CS_UAV_REGISTER_READS_PER_INST = 1;
const UINT D3D12_PS_CS_UAV_REGISTER_READ_PORTS = 1;
const UINT D3D12_PS_FRONTFACING_DEFAULT_VALUE = 0xffffffff;
const UINT D3D12_PS_FRONTFACING_FALSE_VALUE = 0x00000000;
const UINT D3D12_PS_FRONTFACING_TRUE_VALUE = 0xffffffff;
const UINT D3D12_PS_INPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_PS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_PS_INPUT_REGISTER_COUNT = 32;
const UINT D3D12_PS_INPUT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_PS_INPUT_REGISTER_READ_PORTS = 1;
cpp_quote("#define D3D12_PS_LEGACY_PIXEL_CENTER_FRACTIONAL_COMPONENT (0.0f)")
const UINT D3D12_PS_OUTPUT_DEPTH_REGISTER_COMPONENTS = 1;
const UINT D3D12_PS_OUTPUT_DEPTH_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_PS_OUTPUT_DEPTH_REGISTER_COUNT = 1;
const UINT D3D12_PS_OUTPUT_MASK_REGISTER_COMPONENTS = 1;
const UINT D3D12_PS_OUTPUT_MASK_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_PS_OUTPUT_MASK_REGISTER_COUNT = 1;
const UINT D3D12_PS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_PS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_PS_OUTPUT_REGISTER_COUNT = 8;
cpp_quote("#define D3D12_PS_PIXEL_CENTER_FRACTIONAL_COMPONENT (0.5f)")
const UINT D3D12_RAW_UAV_SRV_BYTE_ALIGNMENT = 16;
const UINT D3D12_RAYTRACING_AABB_BYTE_ALIGNMENT = 8;
const UINT D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BYTE_ALIGNMENT = 256;
const UINT D3D12_RAYTRACING_INSTANCE_DESCS_BYTE_ALIGNMENT = 16;
const UINT D3D12_RAYTRACING_MAX_ATTRIBUTE_SIZE_IN_BYTES = 32;
const UINT D3D12_RAYTRACING_MAX_DECLARABLE_TRACE_RECURSION_DEPTH = 31;
const UINT D3D12_RAYTRACING_MAX_GEOMETRIES_PER_BOTTOM_LEVEL_ACCELERATION_STRUCTURE = 16777216;
const UINT D3D12_RAYTRACING_MAX_INSTANCES_PER_TOP_LEVEL_ACCELERATION_STRUCTURE = 16777216;
const UINT D3D12_RAYTRACING_MAX_PRIMITIVES_PER_BOTTOM_LEVEL_ACCELERATION_STRUCTURE = 536870912;
const UINT D3D12_RAYTRACING_MAX_RAY_GENERATION_SHADER_THREADS = 1073741824;
const UINT D3D12_RAYTRACING_MAX_SHADER_RECORD_STRIDE = 4096;
const UINT D3D12_RAYTRACING_SHADER_RECORD_BYTE_ALIGNMENT = 32;
const UINT D3D12_RAYTRACING_SHADER_TABLE_BYTE_ALIGNMENT = 64;
const UINT D3D12_RAYTRACING_TRANSFORM3X4_BYTE_ALIGNMENT = 16;
const UINT D3D12_REQ_BLEND_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D12_REQ_BUFFER_RESOURCE_TEXEL_COUNT_2_TO_EXP = 27;
const UINT D3D12_REQ_CONSTANT_BUFFER_ELEMENT_COUNT = 4096;
const UINT D3D12_REQ_DEPTH_STENCIL_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D12_REQ_DRAWINDEXED_INDEX_COUNT_2_TO_EXP = 32;
const UINT D3D12_REQ_DRAW_VERTEX_COUNT_2_TO_EXP = 32;
const UINT D3D12_REQ_FILTERING_HW_ADDRESSABLE_RESOURCE_DIMENSION = 16384;
const UINT D3D12_REQ_GS_INVOCATION_32BIT_OUTPUT_COMPONENT_LIMIT = 1024;
const UINT D3D12_REQ_IMMEDIATE_CONSTANT_BUFFER_ELEMENT_COUNT = 4096;
const UINT D3D12_REQ_MAXANISOTROPY = 16;
const UINT D3D12_REQ_MIP_LEVELS = 15;
const UINT D3D12_REQ_MULTI_ELEMENT_STRUCTURE_SIZE_IN_BYTES = 2048;
const UINT D3D12_REQ_RASTERIZER_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D12_REQ_RENDER_TO_BUFFER_WINDOW_WIDTH = 16384;
const UINT D3D12_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_A_TERM = 128;
cpp_quote("#define D3D12_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_B_TERM (0.25f)")
const UINT D3D12_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_C_TERM = 2048;
const UINT D3D12_REQ_RESOURCE_VIEW_COUNT_PER_DEVICE_2_TO_EXP = 20;
const UINT D3D12_REQ_SAMPLER_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D12_REQ_SUBRESOURCES = 30720;
const UINT D3D12_REQ_TEXTURE1D_ARRAY_AXIS_DIMENSION = 2048;
const UINT D3D12_REQ_TEXTURE1D_U_DIMENSION = 16384;
const UINT D3D12_REQ_TEXTURE2D_ARRAY_AXIS_DIMENSION = 2048;
const UINT D3D12_REQ_TEXTURE2D_U_OR_V_DIMENSION = 16384;
const UINT D3D12_REQ_TEXTURE3D_U_V_OR_W_DIMENSION = 2048;
const UINT D3D12_REQ_TEXTURECUBE_DIMENSION = 16384;
const UINT D3D12_RESINFO_INSTRUCTION_MISSING_COMPONENT_RETVAL = 0;
const UINT D3D12_RESOURCE_BARRIER_ALL_SUBRESOURCES = 0xffffffff;
const UINT D3D12_RS_SET_SHADING_RATE_COMBINER_COUNT = 2;
const UINT D3D12_SDK_VERSION = 602;
const UINT D3D12_SHADER_IDENTIFIER_SIZE_IN_BYTES = 32;
const UINT D3D12_SHADER_MAJOR_VERSION = 5;
const UINT D3D12_SHADER_MAX_INSTANCES = 65535;
const UINT D3D12_SHADER_MAX_INTERFACES = 253;
const UINT D3D12_SHADER_MAX_INTERFACE_CALL_SITES = 4096;
const UINT D3D12_SHADER_MAX_TYPES = 65535;
const UINT D3D12_SHADER_MINOR_VERSION = 1;
const UINT D3D12_SHIFT_INSTRUCTION_PAD_VALUE = 0;
const UINT D3D12_SHIFT_INSTRUCTION_SHIFT_VALUE_BIT_COUNT = 5;
const UINT D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT = 8;
const UINT D3D12_SMALL_MSAA_RESOURCE_PLACEMENT_ALIGNMENT = 65536;
const UINT D3D12_SMALL_RESOURCE_PLACEMENT_ALIGNMENT = 4096;
const UINT D3D12_SO_BUFFER_MAX_STRIDE_IN_BYTES = 2048;
const UINT D3D12_SO_BUFFER_MAX_WRITE_WINDOW_IN_BYTES = 512;
const UINT D3D12_SO_BUFFER_SLOT_COUNT = 4;
const UINT D3D12_SO_DDI_REGISTER_INDEX_DENOTING_GAP = 0xffffffff;
const UINT D3D12_SO_NO_RASTERIZED_STREAM = 0xffffffff;
const UINT D3D12_SO_OUTPUT_COMPONENT_COUNT = 128;
const UINT D3D12_SO_STREAM_COUNT = 4;
const UINT D3D12_SPEC_DATE_DAY = 14;
const UINT D3D12_SPEC_DATE_MONTH = 11;
const UINT D3D12_SPEC_DATE_YEAR = 2014;
cpp_quote("#define D3D12_SPEC_VERSION (1.16)")
cpp_quote("#define D3D12_SRGB_GAMMA (2.2f)")
cpp_quote("#define D3D12_SRGB_TO_FLOAT_DENOMINATOR_1 (12.92f)")
cpp_quote("#define D3D12_SRGB_TO_FLOAT_DENOMINATOR_2 (1.055f)")
cpp_quote("#define D3D12_SRGB_TO_FLOAT_EXPONENT (2.4f)")
cpp_quote("#define D3D12_SRGB_TO_FLOAT_OFFSET (0.055f)")
cpp_quote("#define D3D12_SRGB_TO_FLOAT_THRESHOLD (0.04045f)")
cpp_quote("#define D3D12_SRGB_TO_FLOAT_TOLERANCE_IN_ULP (0.5f)")
const UINT D3D12_STANDARD_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_STANDARD_COMPONENT_BIT_COUNT_DOUBLED = 64;
const UINT D3D12_STANDARD_MAXIMUM_ELEMENT_ALIGNMENT_BYTE_MULTIPLE = 4;
const UINT D3D12_STANDARD_PIXEL_COMPONENT_COUNT = 128;
const UINT D3D12_STANDARD_PIXEL_ELEMENT_COUNT = 32;
const UINT D3D12_STANDARD_VECTOR_SIZE = 4;
const UINT D3D12_STANDARD_VERTEX_ELEMENT_COUNT = 32;
const UINT D3D12_STANDARD_VERTEX_TOTAL_COMPONENT_COUNT = 64;
const UINT D3D12_SUBPIXEL_FRACTIONAL_BIT_COUNT = 8;
const UINT D3D12_SUBTEXEL_FRACTIONAL_BIT_COUNT = 8;
const UINT D3D12_SYSTEM_RESERVED_REGISTER_SPACE_VALUES_END = 0xffffffff;
const UINT D3D12_SYSTEM_RESERVED_REGISTER_SPACE_VALUES_START = 0xfffffff0;
const UINT D3D12_TESSELLATOR_MAX_EVEN_TESSELLATION_FACTOR = 64;
const UINT D3D12_TESSELLATOR_MAX_ISOLINE_DENSITY_TESSELLATION_FACTOR = 64;
const UINT D3D12_TESSELLATOR_MAX_ODD_TESSELLATION_FACTOR = 63;
const UINT D3D12_TESSELLATOR_MAX_TESSELLATION_FACTOR = 64;
const UINT D3D12_TESSELLATOR_MIN_EVEN_TESSELLATION_FACTOR = 2;
const UINT D3D12_TESSELLATOR_MIN_ISOLINE_DENSITY_TESSELLATION_FACTOR = 1;
const UINT D3D12_TESSELLATOR_MIN_ODD_TESSELLATION_FACTOR = 1;
const UINT D3D12_TEXEL_ADDRESS_RANGE_BIT_COUNT = 16;
const UINT D3D12_TEXTURE_DATA_PITCH_ALIGNMENT = 256;
const UINT D3D12_TEXTURE_DATA_PLACEMENT_ALIGNMENT = 512;
const UINT D3D12_TILED_RESOURCE_TILE_SIZE_IN_BYTES = 65536;
const UINT D3D12_TRACKED_WORKLOAD_MAX_INSTANCES = 32;
const UINT D3D12_UAV_COUNTER_PLACEMENT_ALIGNMENT = 4096;
const UINT D3D12_UAV_SLOT_COUNT = 64;
const UINT D3D12_UNBOUND_MEMORY_ACCESS_RESULT = 0;
const UINT D3D12_VIDEO_DECODE_MAX_ARGUMENTS = 10;
const UINT D3D12_VIDEO_DECODE_MAX_HISTOGRAM_COMPONENTS = 4;
const UINT D3D12_VIDEO_DECODE_MIN_BITSTREAM_OFFSET_ALIGNMENT = 256;
const UINT D3D12_VIDEO_DECODE_MIN_HISTOGRAM_OFFSET_ALIGNMENT = 256;
const UINT D3D12_VIDEO_DECODE_STATUS_MACROBLOCKS_AFFECTED_UNKNOWN = 0xffffffff;
const UINT D3D12_VIDEO_PROCESS_MAX_FILTERS = 32;
const UINT D3D12_VIDEO_PROCESS_STEREO_VIEWS = 2;
const UINT D3D12_VIEWPORT_AND_SCISSORRECT_MAX_INDEX = 15;
const UINT D3D12_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE = 16;
const UINT D3D12_VIEWPORT_BOUNDS_MAX = 32767;
const INT D3D12_VIEWPORT_BOUNDS_MIN = -32768;
const UINT D3D12_VS_INPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_VS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_VS_INPUT_REGISTER_COUNT = 32;
const UINT D3D12_VS_INPUT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_VS_INPUT_REGISTER_READ_PORTS = 1;
const UINT D3D12_VS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_VS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_VS_OUTPUT_REGISTER_COUNT = 32;
const UINT D3D12_WHQL_CONTEXT_COUNT_FOR_RESOURCE_LIMIT = 10;
const UINT D3D12_WHQL_DRAWINDEXED_INDEX_COUNT_2_TO_EXP = 25;
const UINT D3D12_WHQL_DRAW_VERTEX_COUNT_2_TO_EXP = 25;

cpp_quote("#endif")

const UINT D3D12_SHADER_COMPONENT_MAPPING_MASK = 0x7;
const UINT D3D12_SHADER_COMPONENT_MAPPING_SHIFT = 3;
const UINT D3D12_SHADER_COMPONENT_MAPPING_ALWAYS_SET_BIT_AVOIDING_ZEROMEM_MISTAKES
        = 1 << (D3D12_SHADER_COMPONENT_MAPPING_SHIFT * 4);

typedef enum D3D12_SHADER_MIN_PRECISION_SUPPORT
{
    D3D12_SHADER_MIN_PRECISION_SUPPORT_NONE = 0x0,
    D3D12_SHADER_MIN_PRECISION_SUPPORT_10_BIT = 0x1,
    D3D12_SHADER_MIN_PRECISION_SUPPORT_16_BIT = 0x2,
} D3D12_SHADER_MIN_PRECISION_SUPPORT;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_SHADER_MIN_PRECISION_SUPPORT);")

typedef enum D3D12_TILED_RESOURCES_TIER
{
    D3D12_TILED_RESOURCES_TIER_NOT_SUPPORTED = 0,
    D3D12_TILED_RESOURCES_TIER_1 = 1,
    D3D12_TILED_RESOURCES_TIER_2 = 2,
    D3D12_TILED_RESOURCES_TIER_3 = 3,
} D3D12_TILED_RESOURCES_TIER;

typedef enum D3D12_RESOURCE_BINDING_TIER
{
    D3D12_RESOURCE_BINDING_TIER_1 = 1,
    D3D12_RESOURCE_BINDING_TIER_2 = 2,
    D3D12_RESOURCE_BINDING_TIER_3 = 3,
} D3D12_RESOURCE_BINDING_TIER;

typedef enum D3D12_CONSERVATIVE_RASTERIZATION_TIER
{
    D3D12_CONSERVATIVE_RASTERIZATION_TIER_NOT_SUPPORTED = 0,
    D3D12_CONSERVATIVE_RASTERIZATION_TIER_1 = 1,
    D3D12_CONSERVATIVE_RASTERIZATION_TIER_2 = 2,
    D3D12_CONSERVATIVE_RASTERIZATION_TIER_3 = 3,
} D3D12_CONSERVATIVE_RASTERIZATION_TIER;

typedef enum D3D12_CROSS_NODE_SHARING_TIER
{
    D3D12_CROSS_NODE_SHARING_TIER_NOT_SUPPORTED = 0,
    D3D12_CROSS_NODE_SHARING_TIER_1_EMULATED = 1,
    D3D12_CROSS_NODE_SHARING_TIER_1 = 2,
    D3D12_CROSS_NODE_SHARING_TIER_2 = 3,
    D3D12_CROSS_NODE_SHARING_TIER_3 = 4,
} D3D12_CROSS_NODE_SHARING_TIER;

typedef enum D3D12_RESOURCE_HEAP_TIER
{
    D3D12_RESOURCE_HEAP_TIER_1 = 1,
    D3D12_RESOURCE_HEAP_TIER_2 = 2,
} D3D12_RESOURCE_HEAP_TIER;

typedef enum D3D12_FORMAT_SUPPORT1
{
    D3D12_FORMAT_SUPPORT1_NONE = 0x00000000,
    D3D12_FORMAT_SUPPORT1_BUFFER = 0x00000001,
    D3D12_FORMAT_SUPPORT1_IA_VERTEX_BUFFER = 0x00000002,
    D3D12_FORMAT_SUPPORT1_IA_INDEX_BUFFER = 0x00000004,
    D3D12_FORMAT_SUPPORT1_SO_BUFFER = 0x00000008,
    D3D12_FORMAT_SUPPORT1_TEXTURE1D = 0x00000010,
    D3D12_FORMAT_SUPPORT1_TEXTURE2D = 0x00000020,
    D3D12_FORMAT_SUPPORT1_TEXTURE3D = 0x00000040,
    D3D12_FORMAT_SUPPORT1_TEXTURECUBE = 0x00000080,
    D3D12_FORMAT_SUPPORT1_SHADER_LOAD = 0x00000100,
    D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE = 0x00000200,
    D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE_COMPARISON = 0x00000400,
    D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE_MONO_TEXT = 0x00000800,
    D3D12_FORMAT_SUPPORT1_MIP = 0x00001000,
    D3D12_FORMAT_SUPPORT1_RENDER_TARGET = 0x00004000,
    D3D12_FORMAT_SUPPORT1_BLENDABLE = 0x00008000,
    D3D12_FORMAT_SUPPORT1_DEPTH_STENCIL = 0x00010000,
    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_RESOLVE = 0x00040000,
    D3D12_FORMAT_SUPPORT1_DISPLAY = 0x00080000,
    D3D12_FORMAT_SUPPORT1_CAST_WITHIN_BIT_LAYOUT = 0x00100000,
    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_RENDERTARGET = 0x00200000,
    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_LOAD = 0x00400000,
    D3D12_FORMAT_SUPPORT1_SHADER_GATHER = 0x00800000,
    D3D12_FORMAT_SUPPORT1_BACK_BUFFER_CAST = 0x01000000,
    D3D12_FORMAT_SUPPORT1_TYPED_UNORDERED_ACCESS_VIEW = 0x02000000,
    D3D12_FORMAT_SUPPORT1_SHADER_GATHER_COMPARISON = 0x04000000,
    D3D12_FORMAT_SUPPORT1_DECODER_OUTPUT = 0x08000000,
    D3D12_FORMAT_SUPPORT1_VIDEO_PROCESSOR_OUTPUT = 0x10000000,
    D3D12_FORMAT_SUPPORT1_VIDEO_PROCESSOR_INPUT = 0x20000000,
    D3D12_FORMAT_SUPPORT1_VIDEO_ENCODER = 0x40000000,
} D3D12_FORMAT_SUPPORT1;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_FORMAT_SUPPORT1);")

typedef enum D3D12_FORMAT_SUPPORT2
{
    D3D12_FORMAT_SUPPORT2_NONE = 0x00000000,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_ADD = 0x00000001,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_BITWISE_OPS = 0x00000002,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_COMPARE_STORE_OR_COMPARE_EXCHANGE = 0x00000004,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_EXCHANGE = 0x00000008,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_SIGNED_MIN_OR_MAX = 0x00000010,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_UNSIGNED_MIN_OR_MAX = 0x00000020,
    D3D12_FORMAT_SUPPORT2_UAV_TYPED_LOAD = 0x00000040,
    D3D12_FORMAT_SUPPORT2_UAV_TYPED_STORE = 0x00000080,
    D3D12_FORMAT_SUPPORT2_OUTPUT_MERGER_LOGIC_OP = 0x00000100,
    D3D12_FORMAT_SUPPORT2_TILED = 0x00000200,
    D3D12_FORMAT_SUPPORT2_MULTIPLANE_OVERLAY = 0x00004000,
    D3D12_FORMAT_SUPPORT2_SAMPLER_FEEDBACK = 0x00008000,
} D3D12_FORMAT_SUPPORT2;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_FORMAT_SUPPORT2);")

typedef enum D3D12_WRITEBUFFERIMMEDIATE_MODE
{
    D3D12_WRITEBUFFERIMMEDIATE_MODE_DEFAULT = 0x0,
    D3D12_WRITEBUFFERIMMEDIATE_MODE_MARKER_IN = 0x1,
    D3D12_WRITEBUFFERIMMEDIATE_MODE_MARKER_OUT = 0x2,
} D3D12_WRITEBUFFERIMMEDIATE_MODE;

typedef enum D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER
{
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER_NOT_SUPPORTED = 0x0,
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER_1 = 0x1,
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER_2 = 0x2,
}  D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER;

typedef enum D3D12_SHADER_CACHE_SUPPORT_FLAGS
{
    D3D12_SHADER_CACHE_SUPPORT_NONE = 0x0,
    D3D12_SHADER_CACHE_SUPPORT_SINGLE_PSO = 0x1,
    D3D12_SHADER_CACHE_SUPPORT_LIBRARY = 0x2,
    D3D12_SHADER_CACHE_SUPPORT_AUTOMATIC_INPROC_CACHE = 0x4,
    D3D12_SHADER_CACHE_SUPPORT_AUTOMATIC_DISK_CACHE = 0x8,
    D3D12_SHADER_CACHE_SUPPORT_DRIVER_MANAGED_CACHE = 0x10,
    D3D12_SHADER_CACHE_SUPPORT_SHADER_CONTROL_CLEAR = 0x20,
    D3D12_SHADER_CACHE_SUPPORT_SHADER_SESSION_DELETE = 0x40,
}  D3D12_SHADER_CACHE_SUPPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_SHADER_CACHE_SUPPORT_FLAGS);")

typedef enum D3D12_COMMAND_LIST_SUPPORT_FLAGS
{
    D3D12_COMMAND_LIST_SUPPORT_FLAG_NONE = 0x0,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_DIRECT = 0x1,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_BUNDLE = 0x2,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_COMPUTE = 0x4,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_COPY = 0x8,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_DECODE = 0x10,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_PROCESS = 0x20,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_ENCODE = 0x40,
} D3D12_COMMAND_LIST_SUPPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_COMMAND_LIST_SUPPORT_FLAGS);")

typedef enum D3D12_VIEW_INSTANCING_TIER
{
    D3D12_VIEW_INSTANCING_TIER_NOT_SUPPORTED = 0x0,
    D3D12_VIEW_INSTANCING_TIER_1 = 0x1,
    D3D12_VIEW_INSTANCING_TIER_2 = 0x2,
    D3D12_VIEW_INSTANCING_TIER_3 = 0x3,
}  D3D12_VIEW_INSTANCING_TIER;

typedef enum D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER
{
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0 = 0x0,
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1 = 0x1,
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_2 = 0x2,
}  D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER;

typedef struct D3D12_FEATURE_DATA_DISPLAYABLE
{
    BOOL DisplayableTexture;
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER SharedResourceCompatibilityTier;
} D3D12_FEATURE_DATA_DISPLAYABLE;

typedef enum D3D12_HEAP_SERIALIZATION_TIER
{
    D3D12_HEAP_SERIALIZATION_TIER_0 = 0x0,
    D3D12_HEAP_SERIALIZATION_TIER_10 = 0xa,
}  D3D12_HEAP_SERIALIZATION_TIER;

typedef enum D3D12_RENDER_PASS_TIER
{
    D3D12_RENDER_PASS_TIER_0 = 0x0,
    D3D12_RENDER_PASS_TIER_1 = 0x1,
    D3D12_RENDER_PASS_TIER_2 = 0x2,
} D3D12_RENDER_PASS_TIER;

typedef enum D3D12_RAYTRACING_TIER
{
    D3D12_RAYTRACING_TIER_NOT_SUPPORTED = 0x0,
    D3D12_RAYTRACING_TIER_1_0 = 0xa,
    D3D12_RAYTRACING_TIER_1_1 = 0xb,
} D3D12_RAYTRACING_TIER;

typedef enum D3D12_RESIDENCY_FLAGS
{
    D3D12_RESIDENCY_FLAG_NONE = 0x0,
    D3D12_RESIDENCY_FLAG_DENY_OVERBUDGET = 0x1,
} D3D12_RESIDENCY_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RESIDENCY_FLAGS);")

interface ID3D12Fence;
interface ID3D12RootSignature;
interface ID3D12Heap;
interface ID3D12DescriptorHeap;
interface ID3D12Resource;
interface ID3D12CommandAllocator;
interface ID3D12GraphicsCommandList;
interface ID3D12CommandQueue;
interface ID3D12PipelineState;
interface ID3D12Device;

typedef RECT D3D12_RECT;

typedef struct D3D12_BOX
{
    UINT left;
    UINT top;
    UINT front;
    UINT right;
    UINT bottom;
    UINT back;
} D3D12_BOX;

typedef struct D3D12_VIEWPORT
{
    FLOAT TopLeftX;
    FLOAT TopLeftY;
    FLOAT Width;
    FLOAT Height;
    FLOAT MinDepth;
    FLOAT MaxDepth;
} D3D12_VIEWPORT;

typedef struct D3D12_RANGE
{
    SIZE_T Begin;
    SIZE_T End;
} D3D12_RANGE;

typedef struct D3D12_RANGE_UINT64
{
    UINT64 Begin;
    UINT64 End;
} D3D12_RANGE_UINT64;

typedef struct D3D12_SUBRESOURCE_RANGE_UINT64
{
    UINT Subresource;
    D3D12_RANGE_UINT64 Range;
} D3D12_SUBRESOURCE_RANGE_UINT64;

typedef struct D3D12_SUBRESOURCE_INFO
{
    UINT64 Offset;
    UINT RowPitch;
    UINT DepthPitch;
} D3D12_SUBRESOURCE_INFO;

typedef struct D3D12_RESOURCE_ALLOCATION_INFO
{
    UINT64 SizeInBytes;
    UINT64 Alignment;
} D3D12_RESOURCE_ALLOCATION_INFO;

typedef struct D3D12_RESOURCE_ALLOCATION_INFO1
{
    UINT64 Offset;
    UINT64 Alignment;
    UINT64 SizeInBytes;
} D3D12_RESOURCE_ALLOCATION_INFO1;

typedef struct D3D12_DRAW_ARGUMENTS
{
    UINT VertexCountPerInstance;
    UINT InstanceCount;
    UINT StartVertexLocation;
    UINT StartInstanceLocation;
} D3D12_DRAW_ARGUMENTS;

typedef struct D3D12_DRAW_INDEXED_ARGUMENTS
{
    UINT IndexCountPerInstance;
    UINT InstanceCount;
    UINT StartIndexLocation;
    INT BaseVertexLocation;
    UINT StartInstanceLocation;
} D3D12_DRAW_INDEXED_ARGUMENTS;

typedef struct D3D12_DISPATCH_ARGUMENTS
{
    UINT ThreadGroupCountX;
    UINT ThreadGroupCountY;
    UINT ThreadGroupCountZ;
} D3D12_DISPATCH_ARGUMENTS;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS
{
    BOOL DoublePrecisionFloatShaderOps;
    BOOL OutputMergerLogicOp;
    D3D12_SHADER_MIN_PRECISION_SUPPORT MinPrecisionSupport;
    D3D12_TILED_RESOURCES_TIER TiledResourcesTier;
    D3D12_RESOURCE_BINDING_TIER ResourceBindingTier;
    BOOL PSSpecifiedStencilRefSupported;
    BOOL TypedUAVLoadAdditionalFormats;
    BOOL ROVsSupported;
    D3D12_CONSERVATIVE_RASTERIZATION_TIER ConservativeRasterizationTier;
    UINT MaxGPUVirtualAddressBitsPerResource;
    BOOL StandardSwizzle64KBSupported;
    D3D12_CROSS_NODE_SHARING_TIER CrossNodeSharingTier;
    BOOL CrossAdapterRowMajorTextureSupported;
    BOOL VPAndRTArrayIndexFromAnyShaderFeedingRasterizerSupportedWithoutGSEmulation;
    D3D12_RESOURCE_HEAP_TIER ResourceHeapTier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS;

typedef struct D3D12_FEATURE_DATA_FORMAT_SUPPORT
{
    DXGI_FORMAT Format;
    D3D12_FORMAT_SUPPORT1 Support1;
    D3D12_FORMAT_SUPPORT2 Support2;
} D3D12_FEATURE_DATA_FORMAT_SUPPORT;

typedef enum D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS
{
    D3D12_MULTISAMPLE_QUALITY_LEVELS_FLAG_NONE = 0x00000000,
    D3D12_MULTISAMPLE_QUALITY_LEVELS_FLAG_TILED_RESOURCE = 0x00000001,
} D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS);")

typedef struct D3D12_FEATURE_DATA_MULTISAMPLE_QUALITY_LEVELS
{
    DXGI_FORMAT Format;
    UINT SampleCount;
    D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS Flags;
    UINT NumQualityLevels;
} D3D12_FEATURE_DATA_MULTISAMPLE_QUALITY_LEVELS;

typedef enum D3D12_HEAP_TYPE
{
    D3D12_HEAP_TYPE_DEFAULT = 1,
    D3D12_HEAP_TYPE_UPLOAD = 2,
    D3D12_HEAP_TYPE_READBACK = 3,
    D3D12_HEAP_TYPE_CUSTOM = 4,
} D3D12_HEAP_TYPE;

typedef enum D3D12_CPU_PAGE_PROPERTY
{
    D3D12_CPU_PAGE_PROPERTY_UNKNOWN = 0,
    D3D12_CPU_PAGE_PROPERTY_NOT_AVAILABLE = 1,
    D3D12_CPU_PAGE_PROPERTY_WRITE_COMBINE = 2,
    D3D12_CPU_PAGE_PROPERTY_WRITE_BACK = 3,
} D3D12_CPU_PAGE_PROPERTY;

typedef enum D3D12_MEMORY_POOL
{
    D3D12_MEMORY_POOL_UNKNOWN = 0,
    D3D12_MEMORY_POOL_L0 = 1,
    D3D12_MEMORY_POOL_L1 = 2,
} D3D12_MEMORY_POOL;

typedef struct D3D12_HEAP_PROPERTIES
{
    D3D12_HEAP_TYPE Type;
    D3D12_CPU_PAGE_PROPERTY CPUPageProperty;
    D3D12_MEMORY_POOL MemoryPoolPreference;
    UINT CreationNodeMask;
    UINT VisibleNodeMask;
} D3D12_HEAP_PROPERTIES;

typedef enum D3D12_HEAP_FLAGS
{
    D3D12_HEAP_FLAG_NONE = 0x00,
    D3D12_HEAP_FLAG_SHARED = 0x01,
    D3D12_HEAP_FLAG_DENY_BUFFERS = 0x04,
    D3D12_HEAP_FLAG_ALLOW_DISPLAY = 0x08,
    D3D12_HEAP_FLAG_SHARED_CROSS_ADAPTER = 0x20,
    D3D12_HEAP_FLAG_DENY_RT_DS_TEXTURES = 0x40,
    D3D12_HEAP_FLAG_DENY_NON_RT_DS_TEXTURES = 0x80,
    D3D12_HEAP_FLAG_HARDWARE_PROTECTED = 0x100,
    D3D12_HEAP_FLAG_ALLOW_WRITE_WATCH = 0x200,
    D3D12_HEAP_FLAG_ALLOW_SHADER_ATOMICS = 0x400,
    D3D12_HEAP_FLAG_CREATE_NOT_RESIDENT = 0x800,
    D3D12_HEAP_FLAG_CREATE_NOT_ZEROED = 0x1000,
    D3D12_HEAP_FLAG_ALLOW_ALL_BUFFERS_AND_TEXTURES = 0x00,
    D3D12_HEAP_FLAG_ALLOW_ONLY_BUFFERS = 0xc0,
    D3D12_HEAP_FLAG_ALLOW_ONLY_NON_RT_DS_TEXTURES = 0x44,
    D3D12_HEAP_FLAG_ALLOW_ONLY_RT_DS_TEXTURES = 0x84,
} D3D12_HEAP_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_HEAP_FLAGS);")

typedef struct D3D12_HEAP_DESC
{
    UINT64 SizeInBytes;
    D3D12_HEAP_PROPERTIES Properties;
    UINT64 Alignment;
    D3D12_HEAP_FLAGS Flags;
} D3D12_HEAP_DESC;

typedef struct D3D12_TILED_RESOURCE_COORDINATE
{
    UINT X;
    UINT Y;
    UINT Z;
    UINT Subresource;
} D3D12_TILED_RESOURCE_COORDINATE;

typedef struct D3D12_TILE_REGION_SIZE
{
    UINT NumTiles;
    BOOL UseBox;
    UINT Width;
    UINT16 Height;
    UINT16 Depth;
} D3D12_TILE_REGION_SIZE;

typedef struct D3D12_SUBRESOURCE_TILING
{
    UINT WidthInTiles;
    UINT16 HeightInTiles;
    UINT16 DepthInTiles;
    UINT StartTileIndexInOverallResource;
} D3D12_SUBRESOURCE_TILING;

typedef struct D3D12_TILE_SHAPE
{
    UINT WidthInTexels;
    UINT HeightInTexels;
    UINT DepthInTexels;
} D3D12_TILE_SHAPE;

typedef struct D3D12_SHADER_BYTECODE
{
    const void *pShaderBytecode;
    SIZE_T BytecodeLength;
} D3D12_SHADER_BYTECODE;

typedef struct D3D12_DEPTH_STENCIL_VALUE
{
    FLOAT Depth;
    UINT8 Stencil;
} D3D12_DEPTH_STENCIL_VALUE;

typedef struct D3D12_CLEAR_VALUE
{
    DXGI_FORMAT Format;
    union
    {
        FLOAT Color[4];
        D3D12_DEPTH_STENCIL_VALUE DepthStencil;
    };
} D3D12_CLEAR_VALUE;

typedef struct D3D12_PACKED_MIP_INFO
{
    UINT8 NumStandardMips;
    UINT8 NumPackedMips;
    UINT NumTilesForPackedMips;
    UINT StartTileIndexInOverallResource;
} D3D12_PACKED_MIP_INFO;

typedef enum D3D12_RESOURCE_STATES
{
    D3D12_RESOURCE_STATE_COMMON = 0,
    D3D12_RESOURCE_STATE_VERTEX_AND_CONSTANT_BUFFER = 0x1,
    D3D12_RESOURCE_STATE_INDEX_BUFFER = 0x2,
    D3D12_RESOURCE_STATE_RENDER_TARGET = 0x4,
    D3D12_RESOURCE_STATE_UNORDERED_ACCESS = 0x8,
    D3D12_RESOURCE_STATE_DEPTH_WRITE = 0x10,
    D3D12_RESOURCE_STATE_DEPTH_READ = 0x20,
    D3D12_RESOURCE_STATE_NON_PIXEL_SHADER_RESOURCE = 0x40,
    D3D12_RESOURCE_STATE_PIXEL_SHADER_RESOURCE = 0x80,
    D3D12_RESOURCE_STATE_STREAM_OUT = 0x100,
    D3D12_RESOURCE_STATE_INDIRECT_ARGUMENT = 0x200,
    D3D12_RESOURCE_STATE_COPY_DEST = 0x400,
    D3D12_RESOURCE_STATE_COPY_SOURCE = 0x800,
    D3D12_RESOURCE_STATE_RESOLVE_DEST = 0x1000,
    D3D12_RESOURCE_STATE_RESOLVE_SOURCE = 0x2000,
    D3D12_RESOURCE_STATE_RAYTRACING_ACCELERATION_STRUCTURE = 0x400000,
    D3D12_RESOURCE_STATE_SHADING_RATE_SOURCE = 0x1000000,
    D3D12_RESOURCE_STATE_GENERIC_READ = 0x1 | 0x2 | 0x40 | 0x80 | 0x200 | 0x800,
    D3D12_RESOURCE_STATE_ALL_SHADER_RESOURCE = 0x40 | 0x80,
    D3D12_RESOURCE_STATE_PRESENT = 0x0,
    D3D12_RESOURCE_STATE_PREDICATION = 0x200,
    D3D12_RESOURCE_STATE_VIDEO_DECODE_READ = 0x10000,
    D3D12_RESOURCE_STATE_VIDEO_DECODE_WRITE = 0x20000,
    D3D12_RESOURCE_STATE_VIDEO_PROCESS_READ = 0x40000,
    D3D12_RESOURCE_STATE_VIDEO_PROCESS_WRITE = 0x80000,
    D3D12_RESOURCE_STATE_VIDEO_ENCODE_READ = 0x200000,
    D3D12_RESOURCE_STATE_VIDEO_ENCODE_WRITE = 0x800000,
} D3D12_RESOURCE_STATES;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RESOURCE_STATES);")

typedef enum D3D12_RESOURCE_BARRIER_TYPE
{
    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION = 0,
    D3D12_RESOURCE_BARRIER_TYPE_ALIASING = 1,
    D3D12_RESOURCE_BARRIER_TYPE_UAV = 2,
} D3D12_RESOURCE_BARRIER_TYPE;

typedef enum D3D12_RESOURCE_BARRIER_FLAGS
{
    D3D12_RESOURCE_BARRIER_FLAG_NONE = 0x0,
    D3D12_RESOURCE_BARRIER_FLAG_BEGIN_ONLY = 0x1,
    D3D12_RESOURCE_BARRIER_FLAG_END_ONLY = 0x2,
} D3D12_RESOURCE_BARRIER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RESOURCE_BARRIER_FLAGS);")

typedef struct D3D12_RESOURCE_TRANSITION_BARRIER
{
    ID3D12Resource *pResource;
    UINT Subresource;
    D3D12_RESOURCE_STATES StateBefore;
    D3D12_RESOURCE_STATES StateAfter;
} D3D12_RESOURCE_TRANSITION_BARRIER;

typedef struct D3D12_RESOURCE_ALIASING_BARRIER_ALIASING
{
    ID3D12Resource *pResourceBefore;
    ID3D12Resource *pResourceAfter;
} D3D12_RESOURCE_ALIASING_BARRIER;

typedef struct D3D12_RESOURCE_UAV_BARRIER
{
    ID3D12Resource *pResource;
} D3D12_RESOURCE_UAV_BARRIER;

typedef struct D3D12_RESOURCE_BARRIER
{
    D3D12_RESOURCE_BARRIER_TYPE Type;
    D3D12_RESOURCE_BARRIER_FLAGS Flags;
    union
    {
        D3D12_RESOURCE_TRANSITION_BARRIER Transition;
        D3D12_RESOURCE_ALIASING_BARRIER Aliasing;
        D3D12_RESOURCE_UAV_BARRIER UAV;
    };
} D3D12_RESOURCE_BARRIER;

typedef enum D3D12_RESOURCE_DIMENSION
{
    D3D12_RESOURCE_DIMENSION_UNKNOWN = 0,
    D3D12_RESOURCE_DIMENSION_BUFFER = 1,
    D3D12_RESOURCE_DIMENSION_TEXTURE1D = 2,
    D3D12_RESOURCE_DIMENSION_TEXTURE2D = 3,
    D3D12_RESOURCE_DIMENSION_TEXTURE3D = 4,
} D3D12_RESOURCE_DIMENSION;

typedef enum D3D12_TEXTURE_LAYOUT
{
    D3D12_TEXTURE_LAYOUT_UNKNOWN = 0,
    D3D12_TEXTURE_LAYOUT_ROW_MAJOR = 1,
    D3D12_TEXTURE_LAYOUT_64KB_UNDEFINED_SWIZZLE = 2,
    D3D12_TEXTURE_LAYOUT_64KB_STANDARD_SWIZZLE = 3,
} D3D12_TEXTURE_LAYOUT;

typedef enum D3D12_RESOURCE_FLAGS
{
    D3D12_RESOURCE_FLAG_NONE = 0x0,
    D3D12_RESOURCE_FLAG_ALLOW_RENDER_TARGET = 0x1,
    D3D12_RESOURCE_FLAG_ALLOW_DEPTH_STENCIL = 0x2,
    D3D12_RESOURCE_FLAG_ALLOW_UNORDERED_ACCESS = 0x4,
    D3D12_RESOURCE_FLAG_DENY_SHADER_RESOURCE = 0x8,
    D3D12_RESOURCE_FLAG_ALLOW_CROSS_ADAPTER = 0x10,
    D3D12_RESOURCE_FLAG_ALLOW_SIMULTANEOUS_ACCESS = 0x20,
    D3D12_RESOURCE_FLAG_VIDEO_DECODE_REFERENCE_ONLY = 0x40,
    D3D12_RESOURCE_FLAG_VIDEO_ENCODE_REFERENCE_ONLY = 0x80,
    D3D12_RESOURCE_FLAG_RAYTRACING_ACCELERATION_STRUCTURE = 0x100,
} D3D12_RESOURCE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RESOURCE_FLAGS);")

typedef struct D3D12_MIP_REGION
{
    UINT Width;
    UINT Height;
    UINT Depth;
} D3D12_MIP_REGION;

typedef struct D3D12_RESOURCE_DESC
{
    D3D12_RESOURCE_DIMENSION Dimension;
    UINT64 Alignment;
    UINT64 Width;
    UINT Height;
    UINT16 DepthOrArraySize;
    UINT16 MipLevels;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D12_TEXTURE_LAYOUT Layout;
    D3D12_RESOURCE_FLAGS Flags;
} D3D12_RESOURCE_DESC;

typedef struct D3D12_RESOURCE_DESC1
{
    D3D12_RESOURCE_DIMENSION Dimension;
    UINT64 Alignment;
    UINT64 Width;
    UINT Height;
    UINT16 DepthOrArraySize;
    UINT16 MipLevels;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D12_TEXTURE_LAYOUT Layout;
    D3D12_RESOURCE_FLAGS Flags;
    D3D12_MIP_REGION SamplerFeedbackMipRegion;
} D3D12_RESOURCE_DESC1;

typedef enum D3D12_RESOLVE_MODE
{
    D3D12_RESOLVE_MODE_DECOMPRESS = 0,
    D3D12_RESOLVE_MODE_MIN = 1,
    D3D12_RESOLVE_MODE_MAX = 2,
    D3D12_RESOLVE_MODE_AVERAGE = 3,
    D3D12_RESOLVE_MODE_ENCODE_SAMPLER_FEEDBACK = 4,
    D3D12_RESOLVE_MODE_DECODE_SAMPLER_FEEDBACK = 5,
} D3D12_RESOLVE_MODE;

typedef struct D3D12_SAMPLE_POSITION
{
    INT8 X;
    INT8 Y;
} D3D12_SAMPLE_POSITION;

typedef enum D3D12_TEXTURE_COPY_TYPE
{
    D3D12_TEXTURE_COPY_TYPE_SUBRESOURCE_INDEX = 0,
    D3D12_TEXTURE_COPY_TYPE_PLACED_FOOTPRINT = 1,
} D3D12_TEXTURE_COPY_TYPE;

typedef struct D3D12_SUBRESOURCE_FOOTPRINT
{
    DXGI_FORMAT Format;
    UINT Width;
    UINT Height;
    UINT Depth;
    UINT RowPitch;
} D3D12_SUBRESOURCE_FOOTPRINT;

typedef struct D3D12_PLACED_SUBRESOURCE_FOOTPRINT
{
    UINT64 Offset;
    D3D12_SUBRESOURCE_FOOTPRINT Footprint;
} D3D12_PLACED_SUBRESOURCE_FOOTPRINT;

typedef struct D3D12_TEXTURE_COPY_LOCATION
{
    ID3D12Resource *pResource;
    D3D12_TEXTURE_COPY_TYPE Type;
    union
    {
        D3D12_PLACED_SUBRESOURCE_FOOTPRINT PlacedFootprint;
        UINT SubresourceIndex;
    };
} D3D12_TEXTURE_COPY_LOCATION;

typedef enum D3D12_DESCRIPTOR_RANGE_TYPE
{
    D3D12_DESCRIPTOR_RANGE_TYPE_SRV = 0,
    D3D12_DESCRIPTOR_RANGE_TYPE_UAV = 1,
    D3D12_DESCRIPTOR_RANGE_TYPE_CBV = 2,
    D3D12_DESCRIPTOR_RANGE_TYPE_SAMPLER = 3,
} D3D12_DESCRIPTOR_RANGE_TYPE;

typedef struct D3D12_DESCRIPTOR_RANGE
{
    D3D12_DESCRIPTOR_RANGE_TYPE RangeType;
    UINT NumDescriptors;
    UINT BaseShaderRegister;
    UINT RegisterSpace;
    UINT OffsetInDescriptorsFromTableStart;
} D3D12_DESCRIPTOR_RANGE;

typedef enum D3D12_DESCRIPTOR_RANGE_FLAGS
{
    D3D12_DESCRIPTOR_RANGE_FLAG_NONE = 0x0,
    D3D12_DESCRIPTOR_RANGE_FLAG_DESCRIPTORS_VOLATILE = 0x1,
    D3D12_DESCRIPTOR_RANGE_FLAG_DATA_VOLATILE = 0x2,
    D3D12_DESCRIPTOR_RANGE_FLAG_DATA_STATIC_WHILE_SET_AT_EXECUTE = 0x4,
    D3D12_DESCRIPTOR_RANGE_FLAG_DATA_STATIC = 0x8,
    D3D12_DESCRIPTOR_RANGE_FLAG_DESCRIPTORS_STATIC_KEEPING_BUFFER_BOUNDS_CHECKS = 0x10000,
} D3D12_DESCRIPTOR_RANGE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_DESCRIPTOR_RANGE_FLAGS);")

typedef struct D3D12_DESCRIPTOR_RANGE1
{
    D3D12_DESCRIPTOR_RANGE_TYPE RangeType;
    UINT NumDescriptors;
    UINT BaseShaderRegister;
    UINT RegisterSpace;
    D3D12_DESCRIPTOR_RANGE_FLAGS Flags;
    UINT OffsetInDescriptorsFromTableStart;
} D3D12_DESCRIPTOR_RANGE1;

typedef struct D3D12_ROOT_DESCRIPTOR_TABLE
{
    UINT NumDescriptorRanges;
    const D3D12_DESCRIPTOR_RANGE *pDescriptorRanges;
} D3D12_ROOT_DESCRIPTOR_TABLE;

typedef struct D3D12_ROOT_DESCRIPTOR_TABLE1
{
    UINT NumDescriptorRanges;
    const D3D12_DESCRIPTOR_RANGE1 *pDescriptorRanges;
} D3D12_ROOT_DESCRIPTOR_TABLE1;

typedef struct D3D12_ROOT_CONSTANTS
{
    UINT ShaderRegister;
    UINT RegisterSpace;
    UINT Num32BitValues;
} D3D12_ROOT_CONSTANTS;

typedef struct D3D12_ROOT_DESCRIPTOR
{
    UINT ShaderRegister;
    UINT RegisterSpace;
} D3D12_ROOT_DESCRIPTOR;

typedef enum D3D12_ROOT_DESCRIPTOR_FLAGS
{
    D3D12_ROOT_DESCRIPTOR_FLAG_NONE = 0x0,
    D3D12_ROOT_DESCRIPTOR_FLAG_DATA_VOLATILE = 0x2,
    D3D12_ROOT_DESCRIPTOR_FLAG_DATA_STATIC_WHILE_SET_AT_EXECUTE = 0x4,
    D3D12_ROOT_DESCRIPTOR_FLAG_DATA_STATIC = 0x8,
} D3D12_ROOT_DESCRIPTOR_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_ROOT_DESCRIPTOR_FLAGS);")

typedef struct D3D12_ROOT_DESCRIPTOR1
{
    UINT ShaderRegister;
    UINT RegisterSpace;
    D3D12_ROOT_DESCRIPTOR_FLAGS Flags;
} D3D12_ROOT_DESCRIPTOR1;

typedef enum D3D12_ROOT_PARAMETER_TYPE
{
    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE = 0,
    D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS = 1,
    D3D12_ROOT_PARAMETER_TYPE_CBV = 2,
    D3D12_ROOT_PARAMETER_TYPE_SRV = 3,
    D3D12_ROOT_PARAMETER_TYPE_UAV = 4,
} D3D12_ROOT_PARAMETER_TYPE;

typedef enum D3D12_SHADER_VISIBILITY
{
    D3D12_SHADER_VISIBILITY_ALL = 0,
    D3D12_SHADER_VISIBILITY_VERTEX = 1,
    D3D12_SHADER_VISIBILITY_HULL = 2,
    D3D12_SHADER_VISIBILITY_DOMAIN = 3,
    D3D12_SHADER_VISIBILITY_GEOMETRY = 4,
    D3D12_SHADER_VISIBILITY_PIXEL = 5,
    D3D12_SHADER_VISIBILITY_AMPLIFICATION = 6,
    D3D12_SHADER_VISIBILITY_MESH = 7,
} D3D12_SHADER_VISIBILITY;

typedef struct D3D12_ROOT_PARAMETER
{
    D3D12_ROOT_PARAMETER_TYPE ParameterType;
    union
    {
        D3D12_ROOT_DESCRIPTOR_TABLE DescriptorTable;
        D3D12_ROOT_CONSTANTS Constants;
        D3D12_ROOT_DESCRIPTOR Descriptor;
    };
    D3D12_SHADER_VISIBILITY ShaderVisibility;
} D3D12_ROOT_PARAMETER;

typedef struct D3D12_ROOT_PARAMETER1
{
    D3D12_ROOT_PARAMETER_TYPE ParameterType;
    union
    {
        D3D12_ROOT_DESCRIPTOR_TABLE1 DescriptorTable;
        D3D12_ROOT_CONSTANTS Constants;
        D3D12_ROOT_DESCRIPTOR1 Descriptor;
    };
    D3D12_SHADER_VISIBILITY ShaderVisibility;
} D3D12_ROOT_PARAMETER1;

typedef enum D3D12_STATIC_BORDER_COLOR
{
    D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK = 0,
    D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK = 1,
    D3D12_STATIC_BORDER_COLOR_OPAQUE_WHITE = 2,
} D3D12_STATIC_BORDER_COLOR;

typedef enum D3D12_FILTER
{
    D3D12_FILTER_MIN_MAG_MIP_POINT = 0x00,
    D3D12_FILTER_MIN_MAG_POINT_MIP_LINEAR = 0x01,
    D3D12_FILTER_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x04,
    D3D12_FILTER_MIN_POINT_MAG_MIP_LINEAR = 0x05,
    D3D12_FILTER_MIN_LINEAR_MAG_MIP_POINT = 0x10,
    D3D12_FILTER_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x11,
    D3D12_FILTER_MIN_MAG_LINEAR_MIP_POINT = 0x14,
    D3D12_FILTER_MIN_MAG_MIP_LINEAR = 0x15,
    D3D12_FILTER_ANISOTROPIC = 0x55,
    D3D12_FILTER_COMPARISON_MIN_MAG_MIP_POINT = 0x80,
    D3D12_FILTER_COMPARISON_MIN_MAG_POINT_MIP_LINEAR = 0x81,
    D3D12_FILTER_COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x84,
    D3D12_FILTER_COMPARISON_MIN_POINT_MAG_MIP_LINEAR = 0x85,
    D3D12_FILTER_COMPARISON_MIN_LINEAR_MAG_MIP_POINT = 0x90,
    D3D12_FILTER_COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x91,
    D3D12_FILTER_COMPARISON_MIN_MAG_LINEAR_MIP_POINT = 0x94,
    D3D12_FILTER_COMPARISON_MIN_MAG_MIP_LINEAR = 0x95,
    D3D12_FILTER_COMPARISON_ANISOTROPIC = 0xd5,
    D3D12_FILTER_MINIMUM_MIN_MAG_MIP_POINT = 0x100,
    D3D12_FILTER_MINIMUM_MIN_MAG_POINT_MIP_LINEAR = 0x101,
    D3D12_FILTER_MINIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x104,
    D3D12_FILTER_MINIMUM_MIN_POINT_MAG_MIP_LINEAR = 0x105,
    D3D12_FILTER_MINIMUM_MIN_LINEAR_MAG_MIP_POINT = 0x110,
    D3D12_FILTER_MINIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x111,
    D3D12_FILTER_MINIMUM_MIN_MAG_LINEAR_MIP_POINT = 0x114,
    D3D12_FILTER_MINIMUM_MIN_MAG_MIP_LINEAR = 0x115,
    D3D12_FILTER_MINIMUM_ANISOTROPIC = 0x155,
    D3D12_FILTER_MAXIMUM_MIN_MAG_MIP_POINT = 0x180,
    D3D12_FILTER_MAXIMUM_MIN_MAG_POINT_MIP_LINEAR = 0x181,
    D3D12_FILTER_MAXIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x184,
    D3D12_FILTER_MAXIMUM_MIN_POINT_MAG_MIP_LINEAR = 0x185,
    D3D12_FILTER_MAXIMUM_MIN_LINEAR_MAG_MIP_POINT = 0x190,
    D3D12_FILTER_MAXIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x191,
    D3D12_FILTER_MAXIMUM_MIN_MAG_LINEAR_MIP_POINT = 0x194,
    D3D12_FILTER_MAXIMUM_MIN_MAG_MIP_LINEAR = 0x195,
    D3D12_FILTER_MAXIMUM_ANISOTROPIC = 0x1d5,
} D3D12_FILTER;

typedef enum D3D12_FILTER_TYPE
{
    D3D12_FILTER_TYPE_POINT = 0,
    D3D12_FILTER_TYPE_LINEAR = 1,
} D3D12_FILTER_TYPE;

const UINT D3D12_MIP_FILTER_SHIFT = 0;
const UINT D3D12_MAG_FILTER_SHIFT = 2;
const UINT D3D12_MIN_FILTER_SHIFT = 4;
const UINT D3D12_FILTER_TYPE_MASK = 0x3;

const UINT D3D12_ANISOTROPIC_FILTERING_BIT = 0x40;

typedef enum D3D12_FILTER_REDUCTION_TYPE
{
    D3D12_FILTER_REDUCTION_TYPE_STANDARD = 0,
    D3D12_FILTER_REDUCTION_TYPE_COMPARISON = 1,
    D3D12_FILTER_REDUCTION_TYPE_MINIMUM = 2,
    D3D12_FILTER_REDUCTION_TYPE_MAXIMUM = 3,
} D3D12_FILTER_REDUCTION_TYPE;

const UINT D3D12_FILTER_REDUCTION_TYPE_MASK = 0x3;
const UINT D3D12_FILTER_REDUCTION_TYPE_SHIFT = 7;

cpp_quote("#define D3D12_ENCODE_BASIC_FILTER(min, mag, mip, reduction) \\")
cpp_quote("    ((D3D12_FILTER) ( \\")
cpp_quote("    (((min) & D3D12_FILTER_TYPE_MASK) << D3D12_MIN_FILTER_SHIFT) \\")
cpp_quote("    | (((mag) & D3D12_FILTER_TYPE_MASK) << D3D12_MAG_FILTER_SHIFT) \\")
cpp_quote("    | (((mip) & D3D12_FILTER_TYPE_MASK) << D3D12_MIP_FILTER_SHIFT) \\")
cpp_quote("    | (((reduction) & D3D12_FILTER_REDUCTION_TYPE_MASK) << D3D12_FILTER_REDUCTION_TYPE_SHIFT)))")

cpp_quote("#define D3D12_ENCODE_ANISOTROPIC_FILTER(reduction) \\")
cpp_quote("    ((D3D12_FILTER) ( \\")
cpp_quote("     D3D12_ANISOTROPIC_FILTERING_BIT \\")
cpp_quote("     | D3D12_ENCODE_BASIC_FILTER(D3D12_FILTER_TYPE_LINEAR, \\")
cpp_quote("                                 D3D12_FILTER_TYPE_LINEAR, \\")
cpp_quote("                                 D3D12_FILTER_TYPE_LINEAR, \\")
cpp_quote("                                 reduction)))")

cpp_quote("#define D3D12_DECODE_MAG_FILTER(filter) \\")
cpp_quote("    ((D3D12_FILTER_TYPE)(((filter) >> D3D12_MAG_FILTER_SHIFT) & D3D12_FILTER_TYPE_MASK))")

cpp_quote("#define D3D12_DECODE_MIN_FILTER(filter) \\")
cpp_quote("    ((D3D12_FILTER_TYPE)(((filter) >> D3D12_MIN_FILTER_SHIFT) & D3D12_FILTER_TYPE_MASK))")

cpp_quote("#define D3D12_DECODE_MIP_FILTER(filter) \\")
cpp_quote("    ((D3D12_FILTER_TYPE)(((filter) >> D3D12_MIP_FILTER_SHIFT) & D3D12_FILTER_TYPE_MASK))")

cpp_quote("#define D3D12_DECODE_IS_ANISOTROPIC_FILTER(filter)  \\")
cpp_quote("    (((filter) & D3D12_ANISOTROPIC_FILTERING_BIT) \\")
cpp_quote("    && (D3D12_DECODE_MIN_FILTER(filter) == D3D12_FILTER_TYPE_LINEAR) \\")
cpp_quote("    && (D3D12_DECODE_MAG_FILTER(filter) == D3D12_FILTER_TYPE_LINEAR) \\")
cpp_quote("    && (D3D12_DECODE_MIP_FILTER(filter) == D3D12_FILTER_TYPE_LINEAR))")

cpp_quote("#define D3D12_DECODE_FILTER_REDUCTION(filter) \\")
cpp_quote("    ((D3D12_FILTER_REDUCTION_TYPE)(((filter) >> D3D12_FILTER_REDUCTION_TYPE_SHIFT) \\")
cpp_quote("    & D3D12_FILTER_REDUCTION_TYPE_MASK))")

cpp_quote("#define D3D12_DECODE_IS_COMPARISON_FILTER(filter) \\")
cpp_quote("    (D3D12_DECODE_FILTER_REDUCTION(filter) == D3D12_FILTER_REDUCTION_TYPE_COMPARISON)")

typedef enum D3D12_TEXTURE_ADDRESS_MODE
{
    D3D12_TEXTURE_ADDRESS_MODE_WRAP = 1,
    D3D12_TEXTURE_ADDRESS_MODE_MIRROR = 2,
    D3D12_TEXTURE_ADDRESS_MODE_CLAMP = 3,
    D3D12_TEXTURE_ADDRESS_MODE_BORDER = 4,
    D3D12_TEXTURE_ADDRESS_MODE_MIRROR_ONCE = 5,
} D3D12_TEXTURE_ADDRESS_MODE;

typedef enum D3D12_COMPARISON_FUNC
{
    D3D12_COMPARISON_FUNC_NEVER = 1,
    D3D12_COMPARISON_FUNC_LESS = 2,
    D3D12_COMPARISON_FUNC_EQUAL = 3,
    D3D12_COMPARISON_FUNC_LESS_EQUAL = 4,
    D3D12_COMPARISON_FUNC_GREATER = 5,
    D3D12_COMPARISON_FUNC_NOT_EQUAL = 6,
    D3D12_COMPARISON_FUNC_GREATER_EQUAL = 7,
    D3D12_COMPARISON_FUNC_ALWAYS = 8,
} D3D12_COMPARISON_FUNC;

typedef struct D3D12_STATIC_SAMPLER_DESC
{
    D3D12_FILTER Filter;
    D3D12_TEXTURE_ADDRESS_MODE AddressU;
    D3D12_TEXTURE_ADDRESS_MODE AddressV;
    D3D12_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D12_COMPARISON_FUNC ComparisonFunc;
    D3D12_STATIC_BORDER_COLOR BorderColor;
    FLOAT MinLOD;
    FLOAT MaxLOD;
    UINT ShaderRegister;
    UINT RegisterSpace;
    D3D12_SHADER_VISIBILITY ShaderVisibility;
} D3D12_STATIC_SAMPLER_DESC;

typedef enum D3D12_ROOT_SIGNATURE_FLAGS
{
    D3D12_ROOT_SIGNATURE_FLAG_NONE = 0x00,
    D3D12_ROOT_SIGNATURE_FLAG_ALLOW_INPUT_ASSEMBLER_INPUT_LAYOUT = 0x01,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_VERTEX_SHADER_ROOT_ACCESS = 0x02,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_HULL_SHADER_ROOT_ACCESS = 0x04,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_DOMAIN_SHADER_ROOT_ACCESS = 0x08,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_GEOMETRY_SHADER_ROOT_ACCESS = 0x10,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_PIXEL_SHADER_ROOT_ACCESS = 0x20,
    D3D12_ROOT_SIGNATURE_FLAG_ALLOW_STREAM_OUTPUT = 0x40,
    D3D12_ROOT_SIGNATURE_FLAG_LOCAL_ROOT_SIGNATURE = 0x80,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_AMPLIFICATION_SHADER_ROOT_ACCESS = 0x100,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_MESH_SHADER_ROOT_ACCESS = 0x200,
    D3D12_ROOT_SIGNATURE_FLAG_CBV_SRV_UAV_HEAP_DIRECTLY_INDEXED = 0x400,
    D3D12_ROOT_SIGNATURE_FLAG_SAMPLER_HEAP_DIRECTLY_INDEXED = 0x800,
} D3D12_ROOT_SIGNATURE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_ROOT_SIGNATURE_FLAGS);")

typedef struct D3D12_ROOT_SIGNATURE_DESC
{
    UINT NumParameters;
    const D3D12_ROOT_PARAMETER *pParameters;
    UINT NumStaticSamplers;
    const D3D12_STATIC_SAMPLER_DESC *pStaticSamplers;
    D3D12_ROOT_SIGNATURE_FLAGS Flags;
} D3D12_ROOT_SIGNATURE_DESC;

typedef struct D3D12_ROOT_SIGNATURE_DESC1
{
    UINT NumParameters;
    const D3D12_ROOT_PARAMETER1 *pParameters;
    UINT NumStaticSamplers;
    const D3D12_STATIC_SAMPLER_DESC *pStaticSamplers;
    D3D12_ROOT_SIGNATURE_FLAGS Flags;
} D3D12_ROOT_SIGNATURE_DESC1;

typedef enum D3D_ROOT_SIGNATURE_VERSION
{
    D3D_ROOT_SIGNATURE_VERSION_1 = 0x1,
    D3D_ROOT_SIGNATURE_VERSION_1_0 = 0x1,
    D3D_ROOT_SIGNATURE_VERSION_1_1 = 0x2,
} D3D_ROOT_SIGNATURE_VERSION;

typedef struct D3D12_VERSIONED_ROOT_SIGNATURE_DESC
{
    D3D_ROOT_SIGNATURE_VERSION Version;
    union
    {
        D3D12_ROOT_SIGNATURE_DESC Desc_1_0;
        D3D12_ROOT_SIGNATURE_DESC1 Desc_1_1;
    };
} D3D12_VERSIONED_ROOT_SIGNATURE_DESC;

typedef enum D3D12_DESCRIPTOR_HEAP_TYPE
{
    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV,
    D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER,
    D3D12_DESCRIPTOR_HEAP_TYPE_RTV,
    D3D12_DESCRIPTOR_HEAP_TYPE_DSV,
    D3D12_DESCRIPTOR_HEAP_TYPE_NUM_TYPES,
} D3D12_DESCRIPTOR_HEAP_TYPE;

typedef enum D3D12_DESCRIPTOR_HEAP_FLAGS
{
    D3D12_DESCRIPTOR_HEAP_FLAG_NONE = 0x0,
    D3D12_DESCRIPTOR_HEAP_FLAG_SHADER_VISIBLE = 0x1,
} D3D12_DESCRIPTOR_HEAP_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_DESCRIPTOR_HEAP_FLAGS);")

typedef struct D3D12_DESCRIPTOR_HEAP_DESC
{
    D3D12_DESCRIPTOR_HEAP_TYPE Type;
    UINT NumDescriptors;
    D3D12_DESCRIPTOR_HEAP_FLAGS Flags;
    UINT NodeMask;
} D3D12_DESCRIPTOR_HEAP_DESC;

typedef UINT64 D3D12_GPU_VIRTUAL_ADDRESS;

typedef struct D3D12_CONSTANT_BUFFER_VIEW_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT SizeInBytes;
} D3D12_CONSTANT_BUFFER_VIEW_DESC;

typedef enum D3D12_SRV_DIMENSION
{
    D3D12_SRV_DIMENSION_UNKNOWN = 0,
    D3D12_SRV_DIMENSION_BUFFER = 1,
    D3D12_SRV_DIMENSION_TEXTURE1D = 2,
    D3D12_SRV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D12_SRV_DIMENSION_TEXTURE2D = 4,
    D3D12_SRV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D12_SRV_DIMENSION_TEXTURE2DMS = 6,
    D3D12_SRV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D12_SRV_DIMENSION_TEXTURE3D = 8,
    D3D12_SRV_DIMENSION_TEXTURECUBE = 9,
    D3D12_SRV_DIMENSION_TEXTURECUBEARRAY = 10,
    D3D12_SRV_DIMENSION_RAYTRACING_ACCELERATION_STRUCTURE = 11,
} D3D12_SRV_DIMENSION;

typedef enum D3D12_BUFFER_SRV_FLAGS
{
    D3D12_BUFFER_SRV_FLAG_NONE = 0x0,
    D3D12_BUFFER_SRV_FLAG_RAW = 0x1,
} D3D12_BUFFER_SRV_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_BUFFER_SRV_FLAGS);")

typedef enum D3D12_SHADER_COMPONENT_MAPPING
{
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_0 = 0,
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_1 = 1,
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_2 = 2,
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_3 = 3,
    D3D12_SHADER_COMPONENT_MAPPING_FORCE_VALUE_0 = 4,
    D3D12_SHADER_COMPONENT_MAPPING_FORCE_VALUE_1 = 5,
} D3D12_SHADER_COMPONENT_MAPPING;

cpp_quote("#define D3D12_ENCODE_SHADER_4_COMPONENT_MAPPING(x, y, z, w) \\")
cpp_quote("        (((x) & D3D12_SHADER_COMPONENT_MAPPING_MASK) \\")
cpp_quote("        | (((y) & D3D12_SHADER_COMPONENT_MAPPING_MASK) << D3D12_SHADER_COMPONENT_MAPPING_SHIFT) \\")
cpp_quote("        | (((z) & D3D12_SHADER_COMPONENT_MAPPING_MASK) << (D3D12_SHADER_COMPONENT_MAPPING_SHIFT * 2)) \\")
cpp_quote("        | (((w) & D3D12_SHADER_COMPONENT_MAPPING_MASK) << (D3D12_SHADER_COMPONENT_MAPPING_SHIFT * 3)) \\")
cpp_quote("        | D3D12_SHADER_COMPONENT_MAPPING_ALWAYS_SET_BIT_AVOIDING_ZEROMEM_MISTAKES)")
cpp_quote("#define D3D12_DEFAULT_SHADER_4_COMPONENT_MAPPING D3D12_ENCODE_SHADER_4_COMPONENT_MAPPING(0, 1, 2, 3)")

cpp_quote("#define D3D12_DECODE_SHADER_4_COMPONENT_MAPPING(i, mapping) \\")
cpp_quote("        ((D3D12_SHADER_COMPONENT_MAPPING)(mapping >> (i * D3D12_SHADER_COMPONENT_MAPPING_SHIFT) \\")
cpp_quote("        & D3D12_SHADER_COMPONENT_MAPPING_MASK))")

typedef struct D3D12_BUFFER_SRV
{
    UINT64 FirstElement;
    UINT NumElements;
    UINT StructureByteStride;
    D3D12_BUFFER_SRV_FLAGS Flags;
} D3D12_BUFFER_SRV;

typedef struct D3D12_TEX1D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX1D_SRV;

typedef struct D3D12_TEX1D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX1D_ARRAY_SRV;

typedef struct D3D12_TEX2D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT PlaneSlice;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX2D_SRV;

typedef struct D3D12_TEX2D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX2D_ARRAY_SRV;

typedef struct D3D12_TEX2DMS_SRV
{
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_SRV;

typedef struct D3D12_TEX2DMS_ARRAY_SRV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_SRV;

typedef struct D3D12_TEX3D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX3D_SRV;

typedef struct D3D12_TEXCUBE_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    FLOAT ResourceMinLODClamp;
} D3D12_TEXCUBE_SRV;

typedef struct D3D12_TEXCUBE_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT First2DArrayFace;
    UINT NumCubes;
    FLOAT ResourceMinLODClamp;
} D3D12_TEXCUBE_ARRAY_SRV;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_SRV
{
    D3D12_GPU_VIRTUAL_ADDRESS Location;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_SRV;

typedef struct D3D12_SHADER_RESOURCE_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_SRV_DIMENSION ViewDimension;
    UINT Shader4ComponentMapping;
    union
    {
        D3D12_BUFFER_SRV Buffer;
        D3D12_TEX1D_SRV Texture1D;
        D3D12_TEX1D_ARRAY_SRV Texture1DArray;
        D3D12_TEX2D_SRV Texture2D;
        D3D12_TEX2D_ARRAY_SRV Texture2DArray;
        D3D12_TEX2DMS_SRV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_SRV Texture2DMSArray;
        D3D12_TEX3D_SRV Texture3D;
        D3D12_TEXCUBE_SRV TextureCube;
        D3D12_TEXCUBE_ARRAY_SRV TextureCubeArray;
        D3D12_RAYTRACING_ACCELERATION_STRUCTURE_SRV RaytracingAccelerationStructure;
    };
} D3D12_SHADER_RESOURCE_VIEW_DESC;

typedef enum D3D12_UAV_DIMENSION
{
    D3D12_UAV_DIMENSION_UNKNOWN = 0,
    D3D12_UAV_DIMENSION_BUFFER = 1,
    D3D12_UAV_DIMENSION_TEXTURE1D = 2,
    D3D12_UAV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D12_UAV_DIMENSION_TEXTURE2D = 4,
    D3D12_UAV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D12_UAV_DIMENSION_TEXTURE3D = 8,
} D3D12_UAV_DIMENSION;

typedef enum D3D12_BUFFER_UAV_FLAGS
{
    D3D12_BUFFER_UAV_FLAG_NONE = 0x0,
    D3D12_BUFFER_UAV_FLAG_RAW = 0x1,
} D3D12_BUFFER_UAV_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_BUFFER_UAV_FLAGS);")

typedef struct D3D12_BUFFER_UAV
{
    UINT64 FirstElement;
    UINT NumElements;
    UINT StructureByteStride;
    UINT64 CounterOffsetInBytes;
    D3D12_BUFFER_UAV_FLAGS Flags;
} D3D12_BUFFER_UAV;

typedef struct D3D12_TEX1D_UAV
{
    UINT MipSlice;
} D3D12_TEX1D_UAV;

typedef struct D3D12_TEX1D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX1D_ARRAY_UAV;

typedef struct D3D12_TEX2D_UAV
{
    UINT MipSlice;
    UINT PlaneSlice;
} D3D12_TEX2D_UAV;

typedef struct D3D12_TEX2D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D12_TEX2D_ARRAY_UAV;

typedef struct D3D12_TEX3D_UAV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D12_TEX3D_UAV;

typedef struct D3D12_UNORDERED_ACCESS_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_UAV_DIMENSION ViewDimension;
    union
    {
        D3D12_BUFFER_UAV Buffer;
        D3D12_TEX1D_UAV Texture1D;
        D3D12_TEX1D_ARRAY_UAV Texture1DArray;
        D3D12_TEX2D_UAV Texture2D;
        D3D12_TEX2D_ARRAY_UAV Texture2DArray;
        D3D12_TEX3D_UAV Texture3D;
    };
} D3D12_UNORDERED_ACCESS_VIEW_DESC;

typedef enum D3D12_RTV_DIMENSION
{
    D3D12_RTV_DIMENSION_UNKNOWN = 0,
    D3D12_RTV_DIMENSION_BUFFER = 1,
    D3D12_RTV_DIMENSION_TEXTURE1D = 2,
    D3D12_RTV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D12_RTV_DIMENSION_TEXTURE2D = 4,
    D3D12_RTV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D12_RTV_DIMENSION_TEXTURE2DMS = 6,
    D3D12_RTV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D12_RTV_DIMENSION_TEXTURE3D = 8,
} D3D12_RTV_DIMENSION;

typedef struct D3D12_BUFFER_RTV
{
    UINT64 FirstElement;
    UINT NumElements;
} D3D12_BUFFER_RTV;

typedef struct D3D12_TEX1D_RTV
{
    UINT MipSlice;
} D3D12_TEX1D_RTV;

typedef struct D3D12_TEX1D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX1D_ARRAY_RTV;

typedef struct D3D12_TEX2D_RTV
{
    UINT MipSlice;
    UINT PlaneSlice;
} D3D12_TEX2D_RTV;

typedef struct D3D12_TEX2D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D12_TEX2D_ARRAY_RTV;

typedef struct D3D12_TEX2DMS_RTV
{
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_RTV;

typedef struct D3D12_TEX2DMS_ARRAY_RTV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_RTV;

typedef struct D3D12_TEX3D_RTV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D12_TEX3D_RTV;

typedef struct D3D12_RENDER_TARGET_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_RTV_DIMENSION ViewDimension;
    union
    {
        D3D12_BUFFER_RTV Buffer;
        D3D12_TEX1D_RTV Texture1D;
        D3D12_TEX1D_ARRAY_RTV Texture1DArray;
        D3D12_TEX2D_RTV Texture2D;
        D3D12_TEX2D_ARRAY_RTV Texture2DArray;
        D3D12_TEX2DMS_RTV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_RTV Texture2DMSArray;
        D3D12_TEX3D_RTV Texture3D;
    };
} D3D12_RENDER_TARGET_VIEW_DESC;

typedef enum D3D12_DSV_DIMENSION
{
    D3D12_DSV_DIMENSION_UNKNOWN = 0,
    D3D12_DSV_DIMENSION_TEXTURE1D = 1,
    D3D12_DSV_DIMENSION_TEXTURE1DARRAY = 2,
    D3D12_DSV_DIMENSION_TEXTURE2D = 3,
    D3D12_DSV_DIMENSION_TEXTURE2DARRAY = 4,
    D3D12_DSV_DIMENSION_TEXTURE2DMS = 5,
    D3D12_DSV_DIMENSION_TEXTURE2DMSARRAY = 6,
} D3D12_DSV_DIMENSION;

typedef enum D3D12_DSV_FLAGS
{
    D3D12_DSV_FLAG_NONE = 0x0,
    D3D12_DSV_FLAG_READ_ONLY_DEPTH = 0x1,
    D3D12_DSV_FLAG_READ_ONLY_STENCIL = 0x2,
} D3D12_DSV_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_DSV_FLAGS);")

typedef struct D3D12_TEX1D_DSV
{
    UINT MipSlice;
} D3D12_TEX1D_DSV;

typedef struct D3D12_TEX1D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX1D_ARRAY_DSV;

typedef struct D3D12_TEX2D_DSV
{
    UINT MipSlice;
} D3D12_TEX2D_DSV;

typedef struct D3D12_TEX2D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2D_ARRAY_DSV;

typedef struct D3D12_TEX2DMS_DSV
{
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_DSV;

typedef struct D3D12_TEX2DMS_ARRAY_DSV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_DSV;

typedef struct D3D12_DEPTH_STENCIL_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_DSV_DIMENSION ViewDimension;
    D3D12_DSV_FLAGS Flags;
    union
    {
        D3D12_TEX1D_DSV Texture1D;
        D3D12_TEX1D_ARRAY_DSV Texture1DArray;
        D3D12_TEX2D_DSV Texture2D;
        D3D12_TEX2D_ARRAY_DSV Texture2DArray;
        D3D12_TEX2DMS_DSV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_DSV Texture2DMSArray;
    };
} D3D12_DEPTH_STENCIL_VIEW_DESC;

typedef struct D3D12_SAMPLER_DESC
{
    D3D12_FILTER Filter;
    D3D12_TEXTURE_ADDRESS_MODE AddressU;
    D3D12_TEXTURE_ADDRESS_MODE AddressV;
    D3D12_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D12_COMPARISON_FUNC ComparisonFunc;
    FLOAT BorderColor[4];
    FLOAT MinLOD;
    FLOAT MaxLOD;
} D3D12_SAMPLER_DESC;

typedef struct D3D12_CPU_DESCRIPTOR_HANDLE
{
    SIZE_T ptr;
} D3D12_CPU_DESCRIPTOR_HANDLE;

typedef struct D3D12_GPU_DESCRIPTOR_HANDLE
{
    UINT64 ptr;
} D3D12_GPU_DESCRIPTOR_HANDLE;

typedef enum D3D12_STENCIL_OP
{
    D3D12_STENCIL_OP_KEEP = 1,
    D3D12_STENCIL_OP_ZERO = 2,
    D3D12_STENCIL_OP_REPLACE = 3,
    D3D12_STENCIL_OP_INCR_SAT = 4,
    D3D12_STENCIL_OP_DECR_SAT = 5,
    D3D12_STENCIL_OP_INVERT = 6,
    D3D12_STENCIL_OP_INCR = 7,
    D3D12_STENCIL_OP_DECR = 8,
} D3D12_STENCIL_OP;

typedef struct D3D12_DEPTH_STENCILOP_DESC
{
    D3D12_STENCIL_OP StencilFailOp;
    D3D12_STENCIL_OP StencilDepthFailOp;
    D3D12_STENCIL_OP StencilPassOp;
    D3D12_COMPARISON_FUNC StencilFunc;
} D3D12_DEPTH_STENCILOP_DESC;

typedef enum D3D12_DEPTH_WRITE_MASK
{
    D3D12_DEPTH_WRITE_MASK_ZERO = 0,
    D3D12_DEPTH_WRITE_MASK_ALL = 1,
} D3D12_DEPTH_WRITE_MASK;

typedef struct D3D12_DEPTH_STENCIL_DESC
{
    BOOL DepthEnable;
    D3D12_DEPTH_WRITE_MASK DepthWriteMask;
    D3D12_COMPARISON_FUNC DepthFunc;
    BOOL StencilEnable;
    UINT8 StencilReadMask;
    UINT8 StencilWriteMask;
    D3D12_DEPTH_STENCILOP_DESC FrontFace;
    D3D12_DEPTH_STENCILOP_DESC BackFace;
} D3D12_DEPTH_STENCIL_DESC;

typedef struct D3D12_DEPTH_STENCIL_DESC1
{
    BOOL DepthEnable;
    D3D12_DEPTH_WRITE_MASK DepthWriteMask;
    D3D12_COMPARISON_FUNC DepthFunc;
    BOOL StencilEnable;
    UINT8 StencilReadMask;
    UINT8 StencilWriteMask;
    D3D12_DEPTH_STENCILOP_DESC FrontFace;
    D3D12_DEPTH_STENCILOP_DESC BackFace;
    BOOL DepthBoundsTestEnable;
} D3D12_DEPTH_STENCIL_DESC1;

typedef enum D3D12_BLEND
{
    D3D12_BLEND_ZERO = 1,
    D3D12_BLEND_ONE = 2,
    D3D12_BLEND_SRC_COLOR = 3,
    D3D12_BLEND_INV_SRC_COLOR = 4,
    D3D12_BLEND_SRC_ALPHA = 5,
    D3D12_BLEND_INV_SRC_ALPHA = 6,
    D3D12_BLEND_DEST_ALPHA = 7,
    D3D12_BLEND_INV_DEST_ALPHA = 8,
    D3D12_BLEND_DEST_COLOR = 9,
    D3D12_BLEND_INV_DEST_COLOR = 10,
    D3D12_BLEND_SRC_ALPHA_SAT = 11,
    D3D12_BLEND_BLEND_FACTOR = 14,
    D3D12_BLEND_INV_BLEND_FACTOR = 15,
    D3D12_BLEND_SRC1_COLOR = 16,
    D3D12_BLEND_INV_SRC1_COLOR = 17,
    D3D12_BLEND_SRC1_ALPHA = 18,
    D3D12_BLEND_INV_SRC1_ALPHA = 19,
    D3D12_BLEND_ALPHA_FACTOR = 20,
    D3D12_BLEND_INV_ALPHA_FACTOR = 21,
} D3D12_BLEND;

typedef enum D3D12_BLEND_OP
{
    D3D12_BLEND_OP_ADD = 1,
    D3D12_BLEND_OP_SUBTRACT = 2,
    D3D12_BLEND_OP_REV_SUBTRACT = 3,
    D3D12_BLEND_OP_MIN = 4,
    D3D12_BLEND_OP_MAX = 5,
} D3D12_BLEND_OP;

typedef enum D3D12_LOGIC_OP
{
    D3D12_LOGIC_OP_CLEAR = 0x0,
    D3D12_LOGIC_OP_SET = 0x1,
    D3D12_LOGIC_OP_COPY = 0x2,
    D3D12_LOGIC_OP_COPY_INVERTED = 0x3,
    D3D12_LOGIC_OP_NOOP = 0x4,
    D3D12_LOGIC_OP_INVERT = 0x5,
    D3D12_LOGIC_OP_AND = 0x6,
    D3D12_LOGIC_OP_NAND = 0x7,
    D3D12_LOGIC_OP_OR = 0x8,
    D3D12_LOGIC_OP_NOR = 0x9,
    D3D12_LOGIC_OP_XOR = 0xa,
    D3D12_LOGIC_OP_EQUIV = 0xb,
    D3D12_LOGIC_OP_AND_REVERSE = 0xc,
    D3D12_LOGIC_OP_AND_INVERTED = 0xd,
    D3D12_LOGIC_OP_OR_REVERSE = 0xe,
    D3D12_LOGIC_OP_OR_INVERTED = 0xf,
} D3D12_LOGIC_OP;

typedef enum D3D12_COLOR_WRITE_ENABLE
{
    D3D12_COLOR_WRITE_ENABLE_RED = 0x1,
    D3D12_COLOR_WRITE_ENABLE_GREEN = 0x2,
    D3D12_COLOR_WRITE_ENABLE_BLUE = 0x4,
    D3D12_COLOR_WRITE_ENABLE_ALPHA = 0x8,
    D3D12_COLOR_WRITE_ENABLE_ALL = (D3D12_COLOR_WRITE_ENABLE_RED
            | D3D12_COLOR_WRITE_ENABLE_GREEN | D3D12_COLOR_WRITE_ENABLE_BLUE
            | D3D12_COLOR_WRITE_ENABLE_ALPHA),
} D3D12_COLOR_WRITE_ENABLE;

typedef struct D3D12_RENDER_TARGET_BLEND_DESC
{
    BOOL BlendEnable;
    BOOL LogicOpEnable;
    D3D12_BLEND SrcBlend;
    D3D12_BLEND DestBlend;
    D3D12_BLEND_OP BlendOp;
    D3D12_BLEND SrcBlendAlpha;
    D3D12_BLEND DestBlendAlpha;
    D3D12_BLEND_OP BlendOpAlpha;
    D3D12_LOGIC_OP LogicOp;
    UINT8 RenderTargetWriteMask;
} D3D12_RENDER_TARGET_BLEND_DESC;

typedef struct D3D12_BLEND_DESC
{
    BOOL AlphaToCoverageEnable;
    BOOL IndependentBlendEnable;
    D3D12_RENDER_TARGET_BLEND_DESC RenderTarget[D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT];
} D3D12_BLEND_DESC;

typedef enum D3D12_FILL_MODE
{
    D3D12_FILL_MODE_WIREFRAME = 2,
    D3D12_FILL_MODE_SOLID = 3,
} D3D12_FILL_MODE;

typedef enum D3D12_CULL_MODE
{
    D3D12_CULL_MODE_NONE = 1,
    D3D12_CULL_MODE_FRONT = 2,
    D3D12_CULL_MODE_BACK = 3,
} D3D12_CULL_MODE;

typedef enum D3D12_CONSERVATIVE_RASTERIZATION_MODE
{
    D3D12_CONSERVATIVE_RASTERIZATION_MODE_OFF = 0,
    D3D12_CONSERVATIVE_RASTERIZATION_MODE_ON = 1,
} D3D12_CONSERVATIVE_RASTERIZATION_MODE;

typedef struct D3D12_RASTERIZER_DESC
{
    D3D12_FILL_MODE FillMode;
    D3D12_CULL_MODE CullMode;
    BOOL FrontCounterClockwise;
    INT DepthBias;
    FLOAT DepthBiasClamp;
    FLOAT SlopeScaledDepthBias;
    BOOL DepthClipEnable;
    BOOL MultisampleEnable;
    BOOL AntialiasedLineEnable;
    UINT ForcedSampleCount;
    D3D12_CONSERVATIVE_RASTERIZATION_MODE ConservativeRaster;
} D3D12_RASTERIZER_DESC;

typedef struct D3D12_SO_DECLARATION_ENTRY
{
    UINT Stream;
    const char *SemanticName;
    UINT SemanticIndex;
    BYTE StartComponent;
    BYTE ComponentCount;
    BYTE OutputSlot;
} D3D12_SO_DECLARATION_ENTRY;

typedef struct D3D12_STREAM_OUTPUT_DESC
{
    const D3D12_SO_DECLARATION_ENTRY *pSODeclaration;
    UINT NumEntries;
    const UINT *pBufferStrides;
    UINT NumStrides;
    UINT RasterizedStream;
} D3D12_STREAM_OUTPUT_DESC;

typedef enum D3D12_INPUT_CLASSIFICATION
{
    D3D12_INPUT_CLASSIFICATION_PER_VERTEX_DATA = 0,
    D3D12_INPUT_CLASSIFICATION_PER_INSTANCE_DATA = 1,
} D3D12_INPUT_CLASSIFICATION;

typedef struct D3D12_INPUT_ELEMENT_DESC
{
    const char *SemanticName;
    UINT SemanticIndex;
    DXGI_FORMAT Format;
    UINT InputSlot;
    UINT AlignedByteOffset;
    D3D12_INPUT_CLASSIFICATION InputSlotClass;
    UINT InstanceDataStepRate;
} D3D12_INPUT_ELEMENT_DESC;

typedef struct D3D12_INPUT_LAYOUT_DESC
{
    const D3D12_INPUT_ELEMENT_DESC *pInputElementDescs;
    UINT NumElements;
} D3D12_INPUT_LAYOUT_DESC;

typedef enum D3D12_INDEX_BUFFER_STRIP_CUT_VALUE
{
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE_DISABLED = 0,
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE_0xFFFF = 1,
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE_0xFFFFFFFF = 2,
} D3D12_INDEX_BUFFER_STRIP_CUT_VALUE;

typedef D3D_PRIMITIVE_TOPOLOGY D3D12_PRIMITIVE_TOPOLOGY;

typedef D3D_PRIMITIVE D3D12_PRIMITIVE;

typedef enum D3D12_PRIMITIVE_TOPOLOGY_TYPE
{
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_UNDEFINED = 0,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_POINT = 1,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_LINE = 2,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_TRIANGLE = 3,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_PATCH = 4,
} D3D12_PRIMITIVE_TOPOLOGY_TYPE;

typedef struct D3D12_CACHED_PIPELINE_STATE
{
    const void *pCachedBlob;
    SIZE_T CachedBlobSizeInBytes;
} D3D12_CACHED_PIPELINE_STATE;

typedef enum D3D12_PIPELINE_STATE_FLAGS
{
    D3D12_PIPELINE_STATE_FLAG_NONE = 0x0,
    D3D12_PIPELINE_STATE_FLAG_DEBUG = 0x1,
} D3D12_PIPELINE_STATE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_PIPELINE_STATE_FLAGS);")

typedef struct D3D12_GRAPHICS_PIPELINE_STATE_DESC
{
    ID3D12RootSignature *pRootSignature;
    D3D12_SHADER_BYTECODE VS;
    D3D12_SHADER_BYTECODE PS;
    D3D12_SHADER_BYTECODE DS;
    D3D12_SHADER_BYTECODE HS;
    D3D12_SHADER_BYTECODE GS;
    D3D12_STREAM_OUTPUT_DESC StreamOutput;
    D3D12_BLEND_DESC BlendState;
    UINT SampleMask;
    D3D12_RASTERIZER_DESC RasterizerState;
    D3D12_DEPTH_STENCIL_DESC DepthStencilState;
    D3D12_INPUT_LAYOUT_DESC InputLayout;
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE IBStripCutValue;
    D3D12_PRIMITIVE_TOPOLOGY_TYPE PrimitiveTopologyType;
    UINT NumRenderTargets;
    DXGI_FORMAT RTVFormats[D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT];
    DXGI_FORMAT DSVFormat;
    DXGI_SAMPLE_DESC SampleDesc;
    UINT NodeMask;
    D3D12_CACHED_PIPELINE_STATE CachedPSO;
    D3D12_PIPELINE_STATE_FLAGS Flags;
} D3D12_GRAPHICS_PIPELINE_STATE_DESC;

typedef struct D3D12_COMPUTE_PIPELINE_STATE_DESC
{
    ID3D12RootSignature *pRootSignature;
    D3D12_SHADER_BYTECODE CS;
    UINT NodeMask;
    D3D12_CACHED_PIPELINE_STATE CachedPSO;
    D3D12_PIPELINE_STATE_FLAGS Flags;
} D3D12_COMPUTE_PIPELINE_STATE_DESC;

typedef struct D3D12_PIPELINE_STATE_STREAM_DESC
{
    SIZE_T SizeInBytes;
    void *pPipelineStateSubobjectStream;
} D3D12_PIPELINE_STATE_STREAM_DESC;

struct D3D12_RT_FORMAT_ARRAY
{
    DXGI_FORMAT RTFormats[D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT];
    UINT NumRenderTargets;
};

typedef enum D3D12_PIPELINE_STATE_SUBOBJECT_TYPE
{
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_ROOT_SIGNATURE = 0x0,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VS = 0x1,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PS = 0x2,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DS = 0x3,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_HS = 0x4,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_GS = 0x5,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CS = 0x6,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT = 0x7,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_BLEND = 0x8,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_MASK = 0x9,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER = 0xa,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL = 0xb,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT = 0xc,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE = 0xd,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY = 0xe,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS = 0xf,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT = 0x10,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_DESC = 0x11,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_NODE_MASK = 0x12,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CACHED_PSO = 0x13,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_FLAGS = 0x14,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1 = 0x15,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VIEW_INSTANCING = 0x16,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_AS = 0x18,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MS = 0x19,
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MAX_VALID = 0x1a,
} D3D12_PIPELINE_STATE_SUBOBJECT_TYPE;

typedef struct D3D12_VIEW_INSTANCE_LOCATION
{
    UINT ViewportArrayIndex;
    UINT RenderTargetArrayIndex;
} D3D12_VIEW_INSTANCE_LOCATION;

typedef enum D3D12_VIEW_INSTANCING_FLAGS
{
    D3D12_VIEW_INSTANCING_FLAG_NONE = 0x0,
    D3D12_VIEW_INSTANCING_FLAG_ENABLE_VIEW_INSTANCE_MASKING = 0x1,
} D3D12_VIEW_INSTANCING_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIEW_INSTANCING_FLAGS);")

typedef struct D3D12_VIEW_INSTANCING_DESC
{
    UINT ViewInstanceCount;
    const D3D12_VIEW_INSTANCE_LOCATION *pViewInstanceLocations;
    D3D12_VIEW_INSTANCING_FLAGS Flags;
} D3D12_VIEW_INSTANCING_DESC;

typedef enum D3D12_COMMAND_LIST_TYPE
{
    D3D12_COMMAND_LIST_TYPE_DIRECT = 0,
    D3D12_COMMAND_LIST_TYPE_BUNDLE = 1,
    D3D12_COMMAND_LIST_TYPE_COMPUTE = 2,
    D3D12_COMMAND_LIST_TYPE_COPY = 3,
    D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE = 4,
    D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS = 5,
    D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE = 6,
} D3D12_COMMAND_LIST_TYPE;

typedef enum D3D12_COMMAND_QUEUE_PRIORITY
{
    D3D12_COMMAND_QUEUE_PRIORITY_NORMAL = 0,
    D3D12_COMMAND_QUEUE_PRIORITY_HIGH = 100,
    D3D12_COMMAND_QUEUE_PRIORITY_GLOBAL_REALTIME = 10000,
} D3D12_COMMAND_QUEUE_PRIORITY;

typedef enum D3D12_COMMAND_QUEUE_FLAGS
{
    D3D12_COMMAND_QUEUE_FLAG_NONE = 0x0,
    D3D12_COMMAND_QUEUE_FLAG_DISABLE_GPU_TIMEOUT = 0x1,
} D3D12_COMMAND_QUEUE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_COMMAND_QUEUE_FLAGS);")

typedef struct D3D12_COMMAND_QUEUE_DESC
{
    D3D12_COMMAND_LIST_TYPE Type;
    INT Priority;
    D3D12_COMMAND_QUEUE_FLAGS Flags;
    UINT NodeMask;
} D3D12_COMMAND_QUEUE_DESC;

typedef struct D3D12_FEATURE_DATA_ARCHITECTURE
{
    UINT NodeIndex;
    BOOL TileBasedRenderer;
    BOOL UMA;
    BOOL CacheCoherentUMA;
} D3D12_FEATURE_DATA_ARCHITECTURE;

typedef struct D3D12_FEATURE_DATA_FORMAT_INFO
{
    DXGI_FORMAT Format;
    UINT8 PlaneCount;
} D3D12_FEATURE_DATA_FORMAT_INFO;

typedef struct D3D12_FEATURE_DATA_FEATURE_LEVELS
{
    UINT NumFeatureLevels;
    const D3D_FEATURE_LEVEL *pFeatureLevelsRequested;
    D3D_FEATURE_LEVEL MaxSupportedFeatureLevel;
} D3D12_FEATURE_DATA_FEATURE_LEVELS;

typedef struct D3D12_FEATURE_DATA_ROOT_SIGNATURE
{
    D3D_ROOT_SIGNATURE_VERSION HighestVersion;
} D3D12_FEATURE_DATA_ROOT_SIGNATURE;

typedef struct D3D12_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT
{
    UINT MaxGPUVirtualAddressBitsPerResource;
    UINT MaxGPUVirtualAddressBitsPerProcess;
} D3D12_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT;

typedef enum D3D_SHADER_MODEL
{
    D3D_SHADER_MODEL_5_1 = 0x51,
    D3D_SHADER_MODEL_6_0 = 0x60,
    D3D_SHADER_MODEL_6_1 = 0x61,
    D3D_SHADER_MODEL_6_2 = 0x62,
    D3D_SHADER_MODEL_6_3 = 0x63,
    D3D_SHADER_MODEL_6_4 = 0x64,
    D3D_SHADER_MODEL_6_5 = 0x65,
    D3D_SHADER_MODEL_6_6 = 0x66,
    D3D_SHADER_MODEL_6_7 = 0x67,
    D3D_HIGHEST_SHADER_MODEL = D3D_SHADER_MODEL_6_7,
} D3D_SHADER_MODEL;

typedef struct D3D12_FEATURE_DATA_SHADER_MODEL
{
    D3D_SHADER_MODEL HighestShaderModel;
} D3D12_FEATURE_DATA_SHADER_MODEL;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS1
{
    BOOL WaveOps;
    UINT WaveLaneCountMin;
    UINT WaveLaneCountMax;
    UINT TotalLaneCount;
    BOOL ExpandedComputeResourceStates;
    BOOL Int64ShaderOps;
}  D3D12_FEATURE_DATA_D3D12_OPTIONS1;

typedef struct D3D12_FEATURE_DATA_ARCHITECTURE1
{
    UINT NodeIndex;
    BOOL TileBasedRenderer;
    BOOL UMA;
    BOOL CacheCoherentUMA;
    BOOL IsolatedMMU;
}  D3D12_FEATURE_DATA_ARCHITECTURE1;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS2
{
    BOOL DepthBoundsTestSupported;
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER ProgrammableSamplePositionsTier;
}  D3D12_FEATURE_DATA_D3D12_OPTIONS2;

typedef struct D3D12_FEATURE_DATA_SHADER_CACHE
{
    D3D12_SHADER_CACHE_SUPPORT_FLAGS SupportFlags;
}  D3D12_FEATURE_DATA_SHADER_CACHE;

typedef struct D3D12_FEATURE_DATA_COMMAND_QUEUE_PRIORITY
{
    D3D12_COMMAND_LIST_TYPE CommandListType;
    UINT Priority;
    BOOL PriorityForTypeIsSupported;
}  D3D12_FEATURE_DATA_COMMAND_QUEUE_PRIORITY;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS3
{
    BOOL CopyQueueTimestampQueriesSupported;
    BOOL CastingFullyTypedFormatSupported;
    D3D12_COMMAND_LIST_SUPPORT_FLAGS WriteBufferImmediateSupportFlags;
    D3D12_VIEW_INSTANCING_TIER ViewInstancingTier;
    BOOL BarycentricsSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS3;

typedef struct D3D12_FEATURE_DATA_EXISTING_HEAPS
{
    BOOL Supported;
}  D3D12_FEATURE_DATA_EXISTING_HEAPS;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS4
{
    BOOL MSAA64KBAlignedTextureSupported;
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER SharedResourceCompatibilityTier;
    BOOL Native16BitShaderOpsSupported;
}  D3D12_FEATURE_DATA_D3D12_OPTIONS4;

typedef struct D3D12_FEATURE_DATA_SERIALIZATION
{
    UINT NodeIndex;
    D3D12_HEAP_SERIALIZATION_TIER HeapSerializationTier;
}  D3D12_FEATURE_DATA_SERIALIZATION;

typedef struct D3D12_FEATURE_DATA_CROSS_NODE
{
    D3D12_CROSS_NODE_SHARING_TIER SharingTier;
    BOOL AtomicShaderInstructions;
}  D3D12_FEATURE_DATA_CROSS_NODE;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS5
{
    BOOL SRVOnlyTiledResourceTier3;
    D3D12_RENDER_PASS_TIER RenderPassesTier;
    D3D12_RAYTRACING_TIER RaytracingTier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS5;

typedef enum D3D12_VARIABLE_SHADING_RATE_TIER
{
    D3D12_VARIABLE_SHADING_RATE_TIER_NOT_SUPPORTED = 0x0,
    D3D12_VARIABLE_SHADING_RATE_TIER_1 = 0x1,
    D3D12_VARIABLE_SHADING_RATE_TIER_2 = 0x2,
} D3D12_VARIABLE_SHADING_RATE_TIER;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS6
{
    BOOL AdditionalShadingRatesSupported;
    BOOL PerPrimitiveShadingRateSupportedWithViewportIndexing;
    D3D12_VARIABLE_SHADING_RATE_TIER VariableShadingRateTier;
    UINT ShadingRateImageTileSize;
    BOOL BackgroundProcessingSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS6;

typedef enum D3D12_MESH_SHADER_TIER
{
    D3D12_MESH_SHADER_TIER_NOT_SUPPORTED = 0x0,
    D3D12_MESH_SHADER_TIER_1 = 0xa,
} D3D12_MESH_SHADER_TIER;

typedef enum D3D12_SAMPLER_FEEDBACK_TIER
{
    D3D12_SAMPLER_FEEDBACK_TIER_NOT_SUPPORTED = 0x0,
    D3D12_SAMPLER_FEEDBACK_TIER_0_9 = 0x5a,
    D3D12_SAMPLER_FEEDBACK_TIER_1_0 = 0x64,
} D3D12_SAMPLER_FEEDBACK_TIER;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS7
{
    D3D12_MESH_SHADER_TIER MeshShaderTier;
    D3D12_SAMPLER_FEEDBACK_TIER SamplerFeedbackTier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS7;

typedef struct D3D12_FEATURE_DATA_QUERY_META_COMMAND
{
    GUID CommandId;
    UINT NodeMask;
    const void *pQueryInputData;
    SIZE_T QueryInputDataSizeInBytes;
    void *pQueryOutputData;
    SIZE_T QueryOutputDataSizeInBytes;
} D3D12_FEATURE_DATA_QUERY_META_COMMAND;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS8
{
    BOOL UnalignedBlockTexturesSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS8;

typedef enum D3D12_WAVE_MMA_TIER
{
    D3D12_WAVE_MMA_TIER_NOT_SUPPORTED = 0x0,
    D3D12_WAVE_MMA_TIER_1_0 = 0xa,
} D3D12_WAVE_MMA_TIER;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS9
{
    BOOL MeshShaderPipelineStatsSupported;
    BOOL MeshShaderSupportsFullRangeRenderTargetArrayIndex;
    BOOL AtomicInt64OnTypedResourceSupported;
    BOOL AtomicInt64OnGroupSharedSupported;
    BOOL DerivativesInMeshAndAmplificationShadersSupported;
    D3D12_WAVE_MMA_TIER WaveMMATier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS9;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS10
{
    BOOL VariableRateShadingSumCombinerSupported;
    BOOL MeshShaderPerPrimitiveShadingRateSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS10;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS11
{
    BOOL AtomicInt64OnDescriptorHeapResourceSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS11;

typedef enum D3D12_TRI_STATE
{
    D3D12_TRI_STATE_UNKNOWN = -0x1,
    D3D12_TRI_STATE_FALSE = 0x0,
    D3D12_TRI_STATE_TRUE = 0x1,
} D3D12_TRI_STATE;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS12
{
    D3D12_TRI_STATE MSPrimitivesPipelineStatisticIncludesCulledPrimitives;
    BOOL EnhancedBarriersSupported;
    BOOL RelaxedFormatCastingSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS12;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS13
{
    BOOL UnrestrictedBufferTextureCopyPitchSupported;
    BOOL UnrestrictedVertexElementAlignmentSupported;
    BOOL InvertedViewportHeightFlipsYSupported;
    BOOL InvertedViewportDepthFlipsZSupported;
    BOOL TextureCopyBetweenDimensionsSupported;
    BOOL AlphaBlendFactorSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS13;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS14
{
    BOOL AdvancedTextureOpsSupported;
    BOOL WriteableMSAATexturesSupported;
    BOOL IndependentFrontAndBackStencilRefMaskSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS14;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS15
{
    BOOL TriangleFanSupported;
    BOOL DynamicIndexBufferStripCutSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS15;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS16
{
    BOOL DynamicDepthBiasSupported;
    BOOL GPUUploadHeapSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS16;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS17
{
    BOOL NonNormalizedCoordinateSamplersSupported;
    BOOL ManualWriteTrackingResourceSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS17;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS18
{
    BOOL RenderPassesValid;
} D3D12_FEATURE_DATA_D3D12_OPTIONS18;

typedef enum D3D12_FEATURE
{
    D3D12_FEATURE_D3D12_OPTIONS = 0,
    D3D12_FEATURE_ARCHITECTURE = 1,
    D3D12_FEATURE_FEATURE_LEVELS = 2,
    D3D12_FEATURE_FORMAT_SUPPORT = 3,
    D3D12_FEATURE_MULTISAMPLE_QUALITY_LEVELS = 4,
    D3D12_FEATURE_FORMAT_INFO = 5,
    D3D12_FEATURE_GPU_VIRTUAL_ADDRESS_SUPPORT = 6,
    D3D12_FEATURE_SHADER_MODEL = 7,
    D3D12_FEATURE_D3D12_OPTIONS1 = 8,
    D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_SUPPORT = 10,
    D3D12_FEATURE_ROOT_SIGNATURE = 12,
    D3D12_FEATURE_ARCHITECTURE1 = 16,
    D3D12_FEATURE_D3D12_OPTIONS2 = 18,
    D3D12_FEATURE_SHADER_CACHE = 19,
    D3D12_FEATURE_COMMAND_QUEUE_PRIORITY = 20,
    D3D12_FEATURE_D3D12_OPTIONS3 = 21,
    D3D12_FEATURE_EXISTING_HEAPS = 22,
    D3D12_FEATURE_D3D12_OPTIONS4 = 23,
    D3D12_FEATURE_SERIALIZATION = 24,
    D3D12_FEATURE_CROSS_NODE = 25,
    D3D12_FEATURE_D3D12_OPTIONS5 = 27,
    D3D12_FEATURE_DISPLAYABLE = 28,
    D3D12_FEATURE_D3D12_OPTIONS6 = 30,
    D3D12_FEATURE_QUERY_META_COMMAND = 31,
    D3D12_FEATURE_D3D12_OPTIONS7 = 32,
    D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_TYPE_COUNT = 33,
    D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_TYPES = 34,
    D3D12_FEATURE_D3D12_OPTIONS8 = 36,
    D3D12_FEATURE_D3D12_OPTIONS9 = 37,
    D3D12_FEATURE_D3D12_OPTIONS10 = 39,
    D3D12_FEATURE_D3D12_OPTIONS11 = 40,
    D3D12_FEATURE_D3D12_OPTIONS12 = 41,
    D3D12_FEATURE_D3D12_OPTIONS13 = 42,
    D3D12_FEATURE_D3D12_OPTIONS14 = 43,
    D3D12_FEATURE_D3D12_OPTIONS15 = 44,
    D3D12_FEATURE_D3D12_OPTIONS16 = 45,
    D3D12_FEATURE_D3D12_OPTIONS17 = 46,
    D3D12_FEATURE_D3D12_OPTIONS18 = 47,
} D3D12_FEATURE;

typedef struct D3D12_MEMCPY_DEST
{
    void *pData;
    SIZE_T RowPitch;
    SIZE_T SlicePitch;
} D3D12_MEMCPY_DEST;

typedef struct D3D12_SUBRESOURCE_DATA
{
    const void *pData;
    LONG_PTR RowPitch;
    LONG_PTR SlicePitch;
} D3D12_SUBRESOURCE_DATA;

typedef enum D3D12_MULTIPLE_FENCE_WAIT_FLAGS
{
    D3D12_MULTIPLE_FENCE_WAIT_FLAG_NONE = 0x0,
    D3D12_MULTIPLE_FENCE_WAIT_FLAG_ANY = 0x1,
    D3D12_MULTIPLE_FENCE_WAIT_FLAG_ALL = 0x0,
} D3D12_MULTIPLE_FENCE_WAIT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_MULTIPLE_FENCE_WAIT_FLAGS);")

typedef enum D3D12_RESIDENCY_PRIORITY
{
    D3D12_RESIDENCY_PRIORITY_MINIMUM = 0x28000000,
    D3D12_RESIDENCY_PRIORITY_LOW = 0x50000000,
    D3D12_RESIDENCY_PRIORITY_NORMAL = 0x78000000,
    D3D12_RESIDENCY_PRIORITY_HIGH = 0xa0010000,
    D3D12_RESIDENCY_PRIORITY_MAXIMUM = 0xc8000000,
} D3D12_RESIDENCY_PRIORITY;

typedef struct D3D12_WRITEBUFFERIMMEDIATE_PARAMETER
{
    D3D12_GPU_VIRTUAL_ADDRESS Dest;
    UINT32 Value;
} D3D12_WRITEBUFFERIMMEDIATE_PARAMETER;

typedef enum D3D12_PROTECTED_RESOURCE_SESSION_FLAGS
{
    D3D12_PROTECTED_RESOURCE_SESSION_FLAG_NONE = 0,
} D3D12_PROTECTED_RESOURCE_SESSION_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_PROTECTED_RESOURCE_SESSION_FLAGS);")

typedef enum D3D12_PROTECTED_SESSION_STATUS
{
    D3D12_PROTECTED_SESSION_STATUS_OK = 0,
    D3D12_PROTECTED_SESSION_STATUS_INVALID = 1,
} D3D12_PROTECTED_SESSION_STATUS;

typedef struct D3D12_PROTECTED_RESOURCE_SESSION_DESC
{
    UINT NodeMask;
    D3D12_PROTECTED_RESOURCE_SESSION_FLAGS Flags;
} D3D12_PROTECTED_RESOURCE_SESSION_DESC;

[
    uuid(c4fec28f-7966-4e95-9f94-f431cb56c3b8),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Object : IUnknown
{
    HRESULT GetPrivateData(REFGUID guid, UINT *data_size, void *data);
    HRESULT SetPrivateData(REFGUID guid, UINT data_size, const void *data);
    HRESULT SetPrivateDataInterface(REFGUID guid, const IUnknown *data);
    HRESULT SetName(const WCHAR *name);
}

[
    uuid(905db94b-a00c-4140-9df5-2b64ca9ea357),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DeviceChild : ID3D12Object
{
    HRESULT GetDevice(REFIID riid, void **device);
}

[
    uuid(63ee58fb-1268-4835-86da-f008ce62f0d6),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Pageable : ID3D12DeviceChild
{
}

[
    uuid(6b3b2502-6e51-45b3-90ee-9884265e8df3),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Heap : ID3D12Pageable
{
    D3D12_HEAP_DESC GetDesc();
}

[
    uuid(696442be-a72e-4059-bc79-5b5c98040fad),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Resource : ID3D12Pageable
{
    HRESULT Map(UINT sub_resource, const D3D12_RANGE *read_range, void **data);
    void Unmap(UINT sub_resource, const D3D12_RANGE *written_range);

    D3D12_RESOURCE_DESC GetDesc();

    D3D12_GPU_VIRTUAL_ADDRESS GetGPUVirtualAddress();

    HRESULT WriteToSubresource(UINT dst_sub_resource, const D3D12_BOX *dst_box,
            const void *src_data, UINT src_row_pitch, UINT src_slice_pitch);
    HRESULT ReadFromSubresource(void *dst_data, UINT dst_row_pitch, UINT dst_slice_pitch,
            UINT src_sub_resource, const D3D12_BOX *src_box);

    HRESULT GetHeapProperties(D3D12_HEAP_PROPERTIES *heap_properties, D3D12_HEAP_FLAGS *flags);
}

[
    uuid(7116d91c-e7e4-47ce-b8c6-ec8168f437e5),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12CommandList : ID3D12DeviceChild
{
    D3D12_COMMAND_LIST_TYPE GetType();
}

typedef enum D3D12_TILE_COPY_FLAGS
{
    D3D12_TILE_COPY_FLAG_NONE = 0x0,
    D3D12_TILE_COPY_FLAG_NO_HAZARD = 0x1,
    D3D12_TILE_COPY_FLAG_LINEAR_BUFFER_TO_SWIZZLED_TILED_RESOURCE = 0x2,
    D3D12_TILE_COPY_FLAG_SWIZZLED_TILED_RESOURCE_TO_LINEAR_BUFFER = 0x4,
} D3D12_TILE_COPY_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_TILE_COPY_FLAGS);")

typedef struct D3D12_INDEX_BUFFER_VIEW
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT SizeInBytes;
    DXGI_FORMAT Format;
} D3D12_INDEX_BUFFER_VIEW;

typedef struct D3D12_VERTEX_BUFFER_VIEW
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT SizeInBytes;
    UINT StrideInBytes;
} D3D12_VERTEX_BUFFER_VIEW;

typedef struct D3D12_STREAM_OUTPUT_BUFFER_VIEW
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT64 SizeInBytes;
    D3D12_GPU_VIRTUAL_ADDRESS BufferFilledSizeLocation;
} D3D12_STREAM_OUTPUT_BUFFER_VIEW;

typedef enum D3D12_CLEAR_FLAGS
{
    D3D12_CLEAR_FLAG_DEPTH = 0x1,
    D3D12_CLEAR_FLAG_STENCIL = 0x2,
} D3D12_CLEAR_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_CLEAR_FLAGS);")

typedef struct D3D12_DISCARD_REGION
{
    UINT NumRects;
    const D3D12_RECT *pRects;
    UINT FirstSubresource;
    UINT NumSubresources;
} D3D12_DISCARD_REGION;

typedef enum D3D12_QUERY_TYPE
{
    D3D12_QUERY_TYPE_OCCLUSION = 0,
    D3D12_QUERY_TYPE_BINARY_OCCLUSION = 1,
    D3D12_QUERY_TYPE_TIMESTAMP = 2,
    D3D12_QUERY_TYPE_PIPELINE_STATISTICS = 3,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM0 = 4,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM1 = 5,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM2 = 6,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM3 = 7,
    D3D12_QUERY_TYPE_VIDEO_DECODE_STATISTICS = 8,
    D3D12_QUERY_TYPE_PIPELINE_STATISTICS1 = 10,
} D3D12_QUERY_TYPE;

typedef struct D3D12_QUERY_DATA_PIPELINE_STATISTICS
{
    UINT64 IAVertices;
    UINT64 IAPrimitives;
    UINT64 VSInvocations;
    UINT64 GSInvocations;
    UINT64 GSPrimitives;
    UINT64 CInvocations;
    UINT64 CPrimitives;
    UINT64 PSInvocations;
    UINT64 HSInvocations;
    UINT64 DSInvocations;
    UINT64 CSInvocations;
} D3D12_QUERY_DATA_PIPELINE_STATISTICS;

typedef struct D3D12_QUERY_DATA_PIPELINE_STATISTICS1
{
    UINT64 IAVertices;
    UINT64 IAPrimitives;
    UINT64 VSInvocations;
    UINT64 GSInvocations;
    UINT64 GSPrimitives;
    UINT64 CInvocations;
    UINT64 CPrimitives;
    UINT64 PSInvocations;
    UINT64 HSInvocations;
    UINT64 DSInvocations;
    UINT64 CSInvocations;
    UINT64 ASInvocations;
    UINT64 MSInvocations;
    UINT64 MSPrimitives;
} D3D12_QUERY_DATA_PIPELINE_STATISTICS1;

typedef struct D3D12_QUERY_DATA_SO_STATISTICS
{
    UINT64 NumPrimitivesWritten;
    UINT64 PrimitivesStorageNeeded;
} D3D12_QUERY_DATA_SO_STATISTICS;

typedef enum D3D12_PREDICATION_OP
{
    D3D12_PREDICATION_OP_EQUAL_ZERO = 0,
    D3D12_PREDICATION_OP_NOT_EQUAL_ZERO = 1,
} D3D12_PREDICATION_OP;

[
    uuid(8efb471d-616c-4f49-90f7-127bb763fa51),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DescriptorHeap : ID3D12Pageable
{
    D3D12_DESCRIPTOR_HEAP_DESC GetDesc();

    D3D12_CPU_DESCRIPTOR_HANDLE GetCPUDescriptorHandleForHeapStart();
    D3D12_GPU_DESCRIPTOR_HANDLE GetGPUDescriptorHandleForHeapStart();
}

[
    uuid(0d9658ae-ed45-469e-a61d-970ec583cab4),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12QueryHeap : ID3D12Pageable
{
}

[
    uuid(c36a797c-ec80-4f0a-8985-a7b2475082d1),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12CommandSignature : ID3D12Pageable
{
}

[
    uuid(5b160d0f-ac1b-4185-8ba8-b3ae42a5a455),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList : ID3D12CommandList
{
    HRESULT Close();

    HRESULT Reset(ID3D12CommandAllocator *allocator, ID3D12PipelineState *initial_state);

    void ClearState(ID3D12PipelineState *pipeline_state);

    void DrawInstanced(UINT vertex_count_per_instance, UINT instance_count,
            UINT start_vertex_location, UINT start_instance_location);
    void DrawIndexedInstanced(UINT index_count_per_instance, UINT instance_count,
            UINT start_vertex_location, INT base_vertex_location, UINT start_instance_location);

    void Dispatch(UINT x, UINT u, UINT z);

    void CopyBufferRegion(ID3D12Resource *dst_buffer, UINT64 dst_offset,
            ID3D12Resource *src_buffer, UINT64 src_offset, UINT64 byte_count);
    void CopyTextureRegion(const D3D12_TEXTURE_COPY_LOCATION *dst,
            UINT dst_x, UINT dst_y, UINT dst_z,
            const D3D12_TEXTURE_COPY_LOCATION *src, const D3D12_BOX *src_box);
    void CopyResource(ID3D12Resource *dst_resource, ID3D12Resource *src_resource);

    void CopyTiles(ID3D12Resource *tiled_resource,
            const D3D12_TILED_RESOURCE_COORDINATE *tile_region_start_coordinate,
            const D3D12_TILE_REGION_SIZE *tile_region_size,
            ID3D12Resource *buffer,
            UINT64 buffer_offset,
            D3D12_TILE_COPY_FLAGS flags);

    void ResolveSubresource(ID3D12Resource *dst_resource, UINT dst_sub_resource,
            ID3D12Resource *src_resource, UINT src_sub_resource,
            DXGI_FORMAT format);

    void IASetPrimitiveTopology(D3D12_PRIMITIVE_TOPOLOGY primitive_topology);

    void RSSetViewports(UINT viewport_count, const D3D12_VIEWPORT *viewports);
    void RSSetScissorRects(UINT rect_count, const D3D12_RECT *rects);

    void OMSetBlendFactor(const FLOAT blend_factor[4]);
    void OMSetStencilRef(UINT stencil_ref);

    void SetPipelineState(ID3D12PipelineState *pipeline_state);

    void ResourceBarrier(UINT barrier_count, const D3D12_RESOURCE_BARRIER *barriers);

    void ExecuteBundle(ID3D12GraphicsCommandList *command_list);

    void SetDescriptorHeaps(UINT heap_count, ID3D12DescriptorHeap * const *heaps);

    void SetComputeRootSignature(ID3D12RootSignature *root_signature);
    void SetGraphicsRootSignature(ID3D12RootSignature *root_signature);

    void SetComputeRootDescriptorTable(UINT root_parameter_index, D3D12_GPU_DESCRIPTOR_HANDLE base_descriptor);
    void SetGraphicsRootDescriptorTable(UINT root_parameter_index, D3D12_GPU_DESCRIPTOR_HANDLE base_descriptor);

    void SetComputeRoot32BitConstant(UINT root_parameter_index, UINT data, UINT dst_offset);
    void SetGraphicsRoot32BitConstant(UINT root_parameter_index, UINT data, UINT dst_offset);

    void SetComputeRoot32BitConstants(UINT root_parameter_index, UINT constant_count, const void *data,
            UINT dst_offset);
    void SetGraphicsRoot32BitConstants(UINT root_parameter_index, UINT constant_count, const void *data,
            UINT dst_offset);

    void SetComputeRootConstantBufferView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);
    void SetGraphicsRootConstantBufferView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);

    void SetComputeRootShaderResourceView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);
    void SetGraphicsRootShaderResourceView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);

    void SetComputeRootUnorderedAccessView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);
    void SetGraphicsRootUnorderedAccessView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);

    void IASetIndexBuffer(const D3D12_INDEX_BUFFER_VIEW *view);
    void IASetVertexBuffers(UINT start_slot, UINT view_count, const D3D12_VERTEX_BUFFER_VIEW *views);

    void SOSetTargets(UINT start_slot, UINT view_count, const D3D12_STREAM_OUTPUT_BUFFER_VIEW *views);

    void OMSetRenderTargets(UINT render_target_descriptor_count,
            const D3D12_CPU_DESCRIPTOR_HANDLE *render_target_descriptors,
            BOOL single_descriptor_handle,
            const D3D12_CPU_DESCRIPTOR_HANDLE *depth_stencil_descriptor);

    void ClearDepthStencilView(D3D12_CPU_DESCRIPTOR_HANDLE dsv, D3D12_CLEAR_FLAGS flags,
            FLOAT depth, UINT8 stencil, UINT rect_count, const D3D12_RECT *rects);
    void ClearRenderTargetView(D3D12_CPU_DESCRIPTOR_HANDLE rtv, const FLOAT color[4],
            UINT rect_count, const D3D12_RECT *rects);
    void ClearUnorderedAccessViewUint(D3D12_GPU_DESCRIPTOR_HANDLE gpu_handle,
            D3D12_CPU_DESCRIPTOR_HANDLE cpu_handle, ID3D12Resource *resource, const UINT values[4],
            UINT rect_count, const D3D12_RECT *rects);
    void ClearUnorderedAccessViewFloat(D3D12_GPU_DESCRIPTOR_HANDLE gpu_handle,
            D3D12_CPU_DESCRIPTOR_HANDLE cpu_handle, ID3D12Resource *resource, const float values[4],
            UINT rect_count, const D3D12_RECT *rects);

    void DiscardResource(ID3D12Resource *resource, const D3D12_DISCARD_REGION *region);

    void BeginQuery(ID3D12QueryHeap *heap, D3D12_QUERY_TYPE type, UINT index);
    void EndQuery(ID3D12QueryHeap *heap, D3D12_QUERY_TYPE type, UINT index);
    void ResolveQueryData(ID3D12QueryHeap *heap, D3D12_QUERY_TYPE type,
            UINT start_index, UINT query_count,
            ID3D12Resource *dst_buffer, UINT64 aligned_dst_buffer_offset);

    void SetPredication(ID3D12Resource *buffer, UINT64 aligned_buffer_offset,
            D3D12_PREDICATION_OP operation);

    void SetMarker(UINT metadata, const void *data, UINT size);
    void BeginEvent(UINT metadata, const void *data, UINT size);
    void EndEvent();

    void ExecuteIndirect(ID3D12CommandSignature *command_signature,
            UINT max_command_count, ID3D12Resource *arg_buffer, UINT64 arg_buffer_offset,
            ID3D12Resource *count_buffer, UINT64 count_buffer_offset);
}

[
    uuid(553103fb-1fe7-4557-bb38-946d7d0e7ca7),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList1 : ID3D12GraphicsCommandList
{
    void AtomicCopyBufferUINT(ID3D12Resource *dst_buffer, UINT64 dst_offset,
            ID3D12Resource *src_buffer, UINT64 src_offset,
            UINT dependent_resource_count, ID3D12Resource * const *dependent_resources,
            const D3D12_SUBRESOURCE_RANGE_UINT64 *dependent_sub_resource_ranges);

    void AtomicCopyBufferUINT64(ID3D12Resource *dst_buffer, UINT64 dst_offset,
            ID3D12Resource *src_buffer, UINT64 src_offset,
            UINT dependent_resource_count, ID3D12Resource * const *dependent_resources,
            const D3D12_SUBRESOURCE_RANGE_UINT64 *dependent_sub_resource_ranges);

    void OMSetDepthBounds(FLOAT min, FLOAT max);

    void SetSamplePositions(UINT sample_count, UINT pixel_count,
            D3D12_SAMPLE_POSITION *sample_positions);

    void ResolveSubresourceRegion(ID3D12Resource *dst_resource,
            UINT dst_sub_resource_idx, UINT dst_x, UINT dst_y,
            ID3D12Resource *src_resource, UINT src_sub_resource_idx,
            D3D12_RECT *src_rect, DXGI_FORMAT format, D3D12_RESOLVE_MODE mode);

    void SetViewInstanceMask(UINT mask);
}

[
    uuid(38c3e585-ff17-412c-9150-4fc6f9d72a28),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList2 : ID3D12GraphicsCommandList1
{
    void WriteBufferImmediate(UINT count,
            const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *parameters,
            const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes);
}

[
    uuid(a1533d18-0ac1-4084-85b9-89a96116806b),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12ProtectedSession : ID3D12DeviceChild
{
    HRESULT GetStatusFence(REFIID riid, void **fence);

    D3D12_PROTECTED_SESSION_STATUS GetSessionStatus();
}

typedef enum D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS
{
    D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAG_NONE = 0x0,
    D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAG_SUPPORTED = 0x1,
} D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS);")

typedef struct D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_SUPPORT
{
    UINT NodeIndex;
    D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS Support;
} D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_SUPPORT;

[
    uuid(6cd696f4-f289-40cc-8091-5a6c0a099c3d),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12ProtectedResourceSession : ID3D12ProtectedSession
{
    D3D12_PROTECTED_RESOURCE_SESSION_DESC GetDesc();
}

[
    uuid(6fda83a7-b84c-4e38-9ac8-c7bd22016b3d),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList3 : ID3D12GraphicsCommandList2
{
    void SetProtectedResourceSession(ID3D12ProtectedResourceSession *protected_resource_session);
}

typedef enum D3D12_TILE_RANGE_FLAGS
{
    D3D12_TILE_RANGE_FLAG_NONE = 0x0,
    D3D12_TILE_RANGE_FLAG_NULL = 0x1,
    D3D12_TILE_RANGE_FLAG_SKIP = 0x2,
    D3D12_TILE_RANGE_FLAG_REUSE_SINGLE_TILE = 0x4
} D3D12_TILE_RANGE_FLAGS;

typedef enum D3D12_TILE_MAPPING_FLAGS
{
    D3D12_TILE_MAPPING_FLAG_NONE = 0x0,
    D3D12_TILE_MAPPING_FLAG_NO_HAZARD = 0x1,
} D3D12_TILE_MAPPING_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_TILE_MAPPING_FLAGS);")

[
    uuid(0ec870a6-5d7e-4c22-8cfc-5baae07616ed),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12CommandQueue : ID3D12Pageable
{
    void UpdateTileMappings(ID3D12Resource *resource, UINT region_count,
            const D3D12_TILED_RESOURCE_COORDINATE *region_start_coordinates,
            const D3D12_TILE_REGION_SIZE *region_sizes,
            ID3D12Heap *heap,
            UINT range_count,
            const D3D12_TILE_RANGE_FLAGS *range_flags,
            const UINT *heap_range_offsets,
            const UINT *range_tile_counts,
            D3D12_TILE_MAPPING_FLAGS flags);

    void CopyTileMappings(ID3D12Resource *dst_resource,
            const D3D12_TILED_RESOURCE_COORDINATE *dst_region_start_coordinate,
            ID3D12Resource *src_resource,
            const D3D12_TILED_RESOURCE_COORDINATE *src_region_start_coordinate,
            const D3D12_TILE_REGION_SIZE *region_size,
            D3D12_TILE_MAPPING_FLAGS flags);

    void ExecuteCommandLists(UINT command_list_count,
            ID3D12CommandList * const * command_lists);

    void SetMarker(UINT metadata, const void *data, UINT size);
    void BeginEvent(UINT metadata, const void *data, UINT size);
    void EndEvent();

    HRESULT Signal(ID3D12Fence *fence, UINT64 value);
    HRESULT Wait(ID3D12Fence *fence, UINT64 value);

    HRESULT GetTimestampFrequency(UINT64 *frequency);
    HRESULT GetClockCalibration(UINT64 *gpu_timestamp, UINT64 *cpu_timestamp);

    D3D12_COMMAND_QUEUE_DESC GetDesc();
}

typedef enum D3D12_FENCE_FLAGS
{
    D3D12_FENCE_FLAG_NONE = 0x0,
    D3D12_FENCE_FLAG_SHARED = 0x1,
    D3D12_FENCE_FLAG_SHARED_CROSS_ADAPTER = 0x2,
    D3D12_FENCE_FLAG_NON_MONITORED = 0x4,
} D3D12_FENCE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_FENCE_FLAGS);")

typedef enum D3D12_QUERY_HEAP_TYPE
{
    D3D12_QUERY_HEAP_TYPE_OCCLUSION = 0,
    D3D12_QUERY_HEAP_TYPE_TIMESTAMP = 1,
    D3D12_QUERY_HEAP_TYPE_PIPELINE_STATISTICS = 2,
    D3D12_QUERY_HEAP_TYPE_SO_STATISTICS = 3,
    D3D12_QUERY_HEAP_TYPE_VIDEO_DECODE_STATISTICS = 4,
    D3D12_QUERY_HEAP_TYPE_COPY_QUEUE_TIMESTAMP = 5,
    D3D12_QUERY_HEAP_TYPE_PIPELINE_STATISTICS1 = 7,
} D3D12_QUERY_HEAP_TYPE;

typedef struct D3D12_QUERY_HEAP_DESC
{
    D3D12_QUERY_HEAP_TYPE Type;
    UINT Count;
    UINT NodeMask;
} D3D12_QUERY_HEAP_DESC;

typedef enum D3D12_INDIRECT_ARGUMENT_TYPE
{
    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW,
    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED,
    D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH,
    D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT,
    D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_RAYS,
    D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_MESH,
} D3D12_INDIRECT_ARGUMENT_TYPE;

typedef struct D3D12_INDIRECT_ARGUMENT_DESC
{
    D3D12_INDIRECT_ARGUMENT_TYPE Type;
    union
    {
        struct
        {
            UINT Slot;
        } VertexBuffer;
        struct
        {
            UINT RootParameterIndex;
            UINT DestOffsetIn32BitValues;
            UINT Num32BitValuesToSet;
        } Constant;
        struct
        {
            UINT RootParameterIndex;
        } ConstantBufferView;
        struct
        {
            UINT RootParameterIndex;
        } ShaderResourceView;
        struct
        {
            UINT RootParameterIndex;
        } UnorderedAccessView;
    };
} D3D12_INDIRECT_ARGUMENT_DESC;

typedef struct D3D12_COMMAND_SIGNATURE_DESC
{
    UINT ByteStride;
    UINT NumArgumentDescs;
    const D3D12_INDIRECT_ARGUMENT_DESC *pArgumentDescs;
    UINT NodeMask;
} D3D12_COMMAND_SIGNATURE_DESC;

[
    uuid(c54a6b66-72df-4ee8-8be5-a946a1429214),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12RootSignature : ID3D12DeviceChild
{
}

[
    uuid(765a30f3-f624-4c6f-a828-ace948622445),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12PipelineState : ID3D12Pageable
{
    HRESULT GetCachedBlob(ID3DBlob **blob);
}

[
    uuid(0a753dcf-c4d8-4b91-adf6-be5a60d95a76),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Fence : ID3D12Pageable
{
    UINT64 GetCompletedValue();
    HRESULT SetEventOnCompletion(UINT64 value, HANDLE event);
    HRESULT Signal(UINT64 value);
}

[
    uuid(433685fe-e22b-4ca0-a8db-b5b4f4dd0e4a),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Fence1 : ID3D12Fence
{
    D3D12_FENCE_FLAGS GetCreationFlags();
}

[
    uuid(6102dee4-af59-4b09-b999-b44d73f09b24),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12CommandAllocator : ID3D12Pageable
{
    HRESULT Reset();
}

[
    uuid(189819f1-1db6-4b57-be54-1821339b85f7),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device : ID3D12Object
{
    UINT GetNodeCount();

    HRESULT CreateCommandQueue(const D3D12_COMMAND_QUEUE_DESC *desc,
            REFIID riid, void **command_queue);
    HRESULT CreateCommandAllocator(D3D12_COMMAND_LIST_TYPE type,
            REFIID riid, void **command_allocator);
    HRESULT CreateGraphicsPipelineState(const D3D12_GRAPHICS_PIPELINE_STATE_DESC *desc,
            REFIID riid, void **pipeline_state);
    HRESULT CreateComputePipelineState(const D3D12_COMPUTE_PIPELINE_STATE_DESC *desc,
            REFIID riid, void **pipeline_state);
    HRESULT CreateCommandList(UINT node_mask,
            D3D12_COMMAND_LIST_TYPE type,
            ID3D12CommandAllocator *command_allocator,
            ID3D12PipelineState *initial_pipeline_state,
            REFIID riid, void **command_list);

    HRESULT CheckFeatureSupport(D3D12_FEATURE feature,
            void *feature_data, UINT feature_data_size);

    HRESULT CreateDescriptorHeap(const D3D12_DESCRIPTOR_HEAP_DESC *desc,
            REFIID riid, void **descriptor_heap);
    UINT GetDescriptorHandleIncrementSize(D3D12_DESCRIPTOR_HEAP_TYPE descriptor_heap_type);

    HRESULT CreateRootSignature(UINT node_mask,
            const void *bytecode, SIZE_T bytecode_length,
            REFIID riid, void **root_signature);

    void CreateConstantBufferView(const D3D12_CONSTANT_BUFFER_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateShaderResourceView(ID3D12Resource *resource,
            const D3D12_SHADER_RESOURCE_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateUnorderedAccessView(ID3D12Resource *resource, ID3D12Resource *counter_resource,
            const D3D12_UNORDERED_ACCESS_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateRenderTargetView(ID3D12Resource *resource,
            const D3D12_RENDER_TARGET_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateDepthStencilView(ID3D12Resource *resource,
            const D3D12_DEPTH_STENCIL_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateSampler(const D3D12_SAMPLER_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);

    void CopyDescriptors(UINT dst_descriptor_range_count,
            const D3D12_CPU_DESCRIPTOR_HANDLE *dst_descriptor_range_offsets,
            const UINT *dst_descriptor_range_sizes,
            UINT src_descriptor_range_count,
            const D3D12_CPU_DESCRIPTOR_HANDLE *src_descriptor_range_offsets,
            const UINT *src_descriptor_range_sizes,
            D3D12_DESCRIPTOR_HEAP_TYPE descriptor_heap_type);
    void CopyDescriptorsSimple(UINT descriptor_count,
            const D3D12_CPU_DESCRIPTOR_HANDLE dst_descriptor_range_offset,
            const D3D12_CPU_DESCRIPTOR_HANDLE src_descriptor_range_offset,
            D3D12_DESCRIPTOR_HEAP_TYPE descriptor_heap_type);

    D3D12_RESOURCE_ALLOCATION_INFO GetResourceAllocationInfo(UINT visible_mask,
            UINT reource_desc_count, const D3D12_RESOURCE_DESC *resource_descs);

    D3D12_HEAP_PROPERTIES GetCustomHeapProperties(UINT node_mask,
            D3D12_HEAP_TYPE heap_type);

    HRESULT CreateCommittedResource(const D3D12_HEAP_PROPERTIES *heap_properties, D3D12_HEAP_FLAGS heap_flags,
            const D3D12_RESOURCE_DESC *desc, D3D12_RESOURCE_STATES initial_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            REFIID riid, void **resource);

    HRESULT CreateHeap(const D3D12_HEAP_DESC *desc, REFIID riid, void **heap);

    HRESULT CreatePlacedResource(ID3D12Heap *heap, UINT64 heap_offset,
            const D3D12_RESOURCE_DESC *desc, D3D12_RESOURCE_STATES initial_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            REFIID riid, void **resource);
    HRESULT CreateReservedResource(const D3D12_RESOURCE_DESC *desc, D3D12_RESOURCE_STATES initial_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            REFIID riid, void **resource);

    HRESULT CreateSharedHandle(ID3D12DeviceChild *object,
            const SECURITY_ATTRIBUTES *attributes, DWORD access,
            const WCHAR *name, HANDLE *handle);
    HRESULT OpenSharedHandle(HANDLE handle,
            REFIID riid, void **object);
    HRESULT OpenSharedHandleByName(const WCHAR *name, DWORD access, HANDLE *handle);

    HRESULT MakeResident(UINT object_count, ID3D12Pageable * const *objects);
    HRESULT Evict(UINT object_count, ID3D12Pageable * const *objects);

    HRESULT CreateFence(UINT64 initial_value, D3D12_FENCE_FLAGS flags, REFIID riid, void **fence);

    HRESULT GetDeviceRemovedReason();

    void GetCopyableFootprints(const D3D12_RESOURCE_DESC *desc,
            UINT first_sub_resource,
            UINT sub_resource_count,
            UINT64 base_offset,
            D3D12_PLACED_SUBRESOURCE_FOOTPRINT *layouts,
            UINT *row_count,
            UINT64 *row_size,
            UINT64 *total_bytes);

    HRESULT CreateQueryHeap(const D3D12_QUERY_HEAP_DESC *desc,
            REFIID riid, void **heap);

    HRESULT SetStablePowerState(BOOL enable);

    HRESULT CreateCommandSignature(const D3D12_COMMAND_SIGNATURE_DESC *desc,
            ID3D12RootSignature *root_signature,
            REFIID riid, void **command_signature);

    void GetResourceTiling(ID3D12Resource *resource,
            UINT *total_tile_count,
            D3D12_PACKED_MIP_INFO *packed_mip_info,
            D3D12_TILE_SHAPE *standard_tile_shape,
            UINT *sub_resource_tiling_count,
            UINT first_sub_resource_tiling,
            D3D12_SUBRESOURCE_TILING *sub_resource_tilings);

    LUID GetAdapterLuid();
}

[
    uuid(c64226a8-9201-46af-b4cc-53fb9ff7414f),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12PipelineLibrary : ID3D12DeviceChild
{
    HRESULT StorePipeline(const WCHAR *name, ID3D12PipelineState *pipeline);

    HRESULT LoadGraphicsPipeline(const WCHAR *name,
            const D3D12_GRAPHICS_PIPELINE_STATE_DESC *desc, REFIID riid,
            void **pipeline_state);

    HRESULT LoadComputePipeline(const WCHAR *name,
            const D3D12_COMPUTE_PIPELINE_STATE_DESC *desc, REFIID riid,
            void **pipeline_state);

    SIZE_T GetSerializedSize();

    HRESULT Serialize(void *data, SIZE_T data_size_in_bytes);
}

[
    uuid(80eabf42-2568-4e5e-bd82-c37f86961dc3),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12PipelineLibrary1 : ID3D12PipelineLibrary
{
    HRESULT LoadPipeline(const WCHAR *name,
            const D3D12_PIPELINE_STATE_STREAM_DESC *desc, REFIID riid,
            void **pipeline_state);
}

[
    uuid(77acce80-638e-4e65-8895-c1f23386863e),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device1 : ID3D12Device
{
    HRESULT CreatePipelineLibrary(const void *blob, SIZE_T blob_size, REFIID iid, void **lib);

    HRESULT SetEventOnMultipleFenceCompletion(ID3D12Fence * const *fences,
            const UINT64 *values, UINT fence_count, D3D12_MULTIPLE_FENCE_WAIT_FLAGS flags, HANDLE event);

    HRESULT SetResidencyPriority(UINT object_count, ID3D12Pageable * const *objects,
            const D3D12_RESIDENCY_PRIORITY *priorities);
}

[
    uuid(30baa41e-b15b-475c-a0bb-1af5c5b64328),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device2 : ID3D12Device1
{
    HRESULT CreatePipelineState(
        [in] const D3D12_PIPELINE_STATE_STREAM_DESC *desc,
        [in] REFIID riid,
        [out, iid_is(riid)] void **pipeline_state);
}

[
    uuid(81dadc15-2bad-4392-93c5-101345c4aa98),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device3 : ID3D12Device2
{
    HRESULT OpenExistingHeapFromAddress(
        [in] const void *address,
        [in] REFIID riid,
        [out, iid_is(riid)] void **heap);

    HRESULT OpenExistingHeapFromFileMapping(
        [in] HANDLE file_mapping,
        [in] REFIID riid,
        [out, iid_is(riid)] void **heap);

    HRESULT EnqueueMakeResident(
        [in] D3D12_RESIDENCY_FLAGS flags,
        [in] UINT num_objects,
        [in] ID3D12Pageable *const *objects,
        [in] ID3D12Fence *fence,
        [in] UINT64 fence_value);
}

[
    uuid(34ab647b-3cc8-46ac-841b-c0965645c046),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12RootSignatureDeserializer : IUnknown
{
    const D3D12_ROOT_SIGNATURE_DESC *GetRootSignatureDesc();
}

[
    uuid(7f91ce67-090c-4bb7-b78e-ed8ff2e31da0),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VersionedRootSignatureDeserializer : IUnknown
{
    HRESULT GetRootSignatureDescAtVersion(D3D_ROOT_SIGNATURE_VERSION version,
            const D3D12_VERSIONED_ROOT_SIGNATURE_DESC **desc);

    const D3D12_VERSIONED_ROOT_SIGNATURE_DESC *GetUnconvertedRootSignatureDesc();
};

typedef enum D3D12_COMMAND_LIST_FLAGS
{
    D3D12_COMMAND_LIST_FLAG_NONE = 0x0,
} D3D12_COMMAND_LIST_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_COMMAND_LIST_FLAGS);")

typedef enum D3D12_COMMAND_POOL_FLAGS
{
    D3D12_COMMAND_POOL_FLAG_NONE = 0x0,
} D3D12_COMMAND_POOL_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_COMMAND_POOL_FLAGS);")

typedef enum D3D12_COMMAND_RECORDER_FLAGS
{
    D3D12_COMMAND_RECORDER_FLAG_NONE = 0x0,
} D3D12_COMMAND_RECORDER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_COMMAND_RECORDER_FLAGS);")

[
    uuid(e865df17-a9ee-46f9-a463-3098315aa2e5),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device4 : ID3D12Device3
{
    HRESULT CreateCommandList1(
            UINT node_mask, D3D12_COMMAND_LIST_TYPE type,
            D3D12_COMMAND_LIST_FLAGS flags, REFIID riid,
            void **command_list);

    HRESULT CreateProtectedResourceSession(
            const D3D12_PROTECTED_RESOURCE_SESSION_DESC *desc, REFIID riid,
            void **session);

    HRESULT CreateCommittedResource1(
            const D3D12_HEAP_PROPERTIES *heap_properties,
            D3D12_HEAP_FLAGS heap_flags,
            const D3D12_RESOURCE_DESC *desc,
            D3D12_RESOURCE_STATES initial_resource_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            ID3D12ProtectedResourceSession *protected_session,
            REFIID riid_resource,
            void **resource);

    HRESULT CreateHeap1(
            const D3D12_HEAP_DESC *desc,
            ID3D12ProtectedResourceSession *protected_session,
            REFIID riid, void **heap);

    HRESULT CreateReservedResource1(
            const D3D12_RESOURCE_DESC *desc,
            D3D12_RESOURCE_STATES initial_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            ID3D12ProtectedResourceSession *protected_session,
            REFIID riid, void **resource);

    D3D12_RESOURCE_ALLOCATION_INFO GetResourceAllocationInfo1(
            UINT visible_mask,
            UINT resource_descs_count,
            const D3D12_RESOURCE_DESC *resource_descs,
            D3D12_RESOURCE_ALLOCATION_INFO1 *resource_allocation_info1);
}

typedef enum D3D12_LIFETIME_STATE
{
    D3D12_LIFETIME_STATE_IN_USE = 0x0,
    D3D12_LIFETIME_STATE_NOT_IN_USE = 0x1,
} D3D12_LIFETIME_STATE;

[
    uuid(e667af9f-cd56-4f46-83ce-032e595d70a8),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12LifetimeOwner : IUnknown
{
    void LifetimeStateUpdated(D3D12_LIFETIME_STATE NewState);
}

[
    uuid(f1df64b6-57fd-49cd-8807-c0eb88b45c8f),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12SwapChainAssistant : IUnknown
{
    LUID GetLUID();
    HRESULT GetSwapChainObject(REFIID riid, void **ppv);
    HRESULT GetCurrentResourceAndCommandQueue(
            REFIID riid_resource, void **resource,
            REFIID riid_queue, void **queue);
    HRESULT InsertImplicitSync();
}

[
    uuid(3fd03d36-4eb1-424a-a582-494ecb8ba813),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12LifetimeTracker : ID3D12DeviceChild
{
    HRESULT DestroyOwnedObject(ID3D12DeviceChild *object);
}

typedef enum D3D12_META_COMMAND_PARAMETER_TYPE
{
    D3D12_META_COMMAND_PARAMETER_TYPE_FLOAT = 0x0,
    D3D12_META_COMMAND_PARAMETER_TYPE_UINT64 = 0x1,
    D3D12_META_COMMAND_PARAMETER_TYPE_GPU_VIRTUAL_ADDRESS = 0x2,
    D3D12_META_COMMAND_PARAMETER_TYPE_CPU_DESCRIPTOR_HANDLE_HEAP_TYPE_CBV_SRV_UAV = 0x3,
    D3D12_META_COMMAND_PARAMETER_TYPE_GPU_DESCRIPTOR_HANDLE_HEAP_TYPE_CBV_SRV_UAV = 0x4,
} D3D12_META_COMMAND_PARAMETER_TYPE;

typedef enum D3D12_META_COMMAND_PARAMETER_FLAGS
{
    D3D12_META_COMMAND_PARAMETER_FLAG_INPUT = 0x1,
    D3D12_META_COMMAND_PARAMETER_FLAG_OUTPUT = 0x2,
} D3D12_META_COMMAND_PARAMETER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_META_COMMAND_PARAMETER_FLAGS);")

typedef enum D3D12_META_COMMAND_PARAMETER_STAGE
{
    D3D12_META_COMMAND_PARAMETER_STAGE_CREATION = 0x0,
    D3D12_META_COMMAND_PARAMETER_STAGE_INITIALIZATION = 0x1,
    D3D12_META_COMMAND_PARAMETER_STAGE_EXECUTION = 0x2,
} D3D12_META_COMMAND_PARAMETER_STAGE;

typedef struct D3D12_META_COMMAND_PARAMETER_DESC
{
    const WCHAR *Name;
    D3D12_META_COMMAND_PARAMETER_TYPE Type;
    D3D12_META_COMMAND_PARAMETER_FLAGS Flags;
    D3D12_RESOURCE_STATES RequiredResourceState;
    UINT StructureOffset;
} D3D12_META_COMMAND_PARAMETER_DESC;

typedef enum D3D12_GRAPHICS_STATES
{
    D3D12_GRAPHICS_STATE_NONE = 0x0,
    D3D12_GRAPHICS_STATE_IA_VERTEX_BUFFERS = 0x1,
    D3D12_GRAPHICS_STATE_IA_INDEX_BUFFER = 0x2,
    D3D12_GRAPHICS_STATE_IA_PRIMITIVE_TOPOLOGY = 0x4,
    D3D12_GRAPHICS_STATE_DESCRIPTOR_HEAP = 0x8,
    D3D12_GRAPHICS_STATE_GRAPHICS_ROOT_SIGNATURE = 0x10,
    D3D12_GRAPHICS_STATE_COMPUTE_ROOT_SIGNATURE = 0x20,
    D3D12_GRAPHICS_STATE_RS_VIEWPORTS = 0x40,
    D3D12_GRAPHICS_STATE_RS_SCISSOR_RECTS = 0x80,
    D3D12_GRAPHICS_STATE_PREDICATION = 0x100,
    D3D12_GRAPHICS_STATE_OM_RENDER_TARGETS = 0x200,
    D3D12_GRAPHICS_STATE_OM_STENCIL_REF = 0x400,
    D3D12_GRAPHICS_STATE_OM_BLEND_FACTOR = 0x800,
    D3D12_GRAPHICS_STATE_PIPELINE_STATE = 0x1000,
    D3D12_GRAPHICS_STATE_SO_TARGETS = 0x2000,
    D3D12_GRAPHICS_STATE_OM_DEPTH_BOUNDS = 0x4000,
    D3D12_GRAPHICS_STATE_SAMPLE_POSITIONS = 0x8000,
    D3D12_GRAPHICS_STATE_VIEW_INSTANCE_MASK = 0x10000,
} D3D12_GRAPHICS_STATES;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_GRAPHICS_STATES);")

typedef struct D3D12_META_COMMAND_DESC
{
    GUID Id;
    const WCHAR *Name;
    D3D12_GRAPHICS_STATES InitializationDirtyState;
    D3D12_GRAPHICS_STATES ExecutionDirtyState;
} D3D12_META_COMMAND_DESC;

[
    uuid(47016943-fca8-4594-93ea-af258b55346d),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12StateObject : ID3D12Pageable
{
}

[
    uuid(de5fa827-9bf9-4f26-89ff-d7f56fde3860),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12StateObjectProperties : IUnknown
{
    void *GetShaderIdentifier(const WCHAR *export_name);
    UINT64 GetShaderStackSize(const WCHAR *export_name);
    UINT64 GetPipelineStackSize();
    void SetPipelineStackSize(UINT64 pipeline_stack_size_in_bytes);
}

typedef enum D3D12_STATE_SUBOBJECT_TYPE
{
    D3D12_STATE_SUBOBJECT_TYPE_STATE_OBJECT_CONFIG = 0x0,
    D3D12_STATE_SUBOBJECT_TYPE_GLOBAL_ROOT_SIGNATURE = 0x1,
    D3D12_STATE_SUBOBJECT_TYPE_LOCAL_ROOT_SIGNATURE = 0x2,
    D3D12_STATE_SUBOBJECT_TYPE_NODE_MASK = 0x3,
    D3D12_STATE_SUBOBJECT_TYPE_DXIL_LIBRARY = 0x5,
    D3D12_STATE_SUBOBJECT_TYPE_EXISTING_COLLECTION = 0x6,
    D3D12_STATE_SUBOBJECT_TYPE_SUBOBJECT_TO_EXPORTS_ASSOCIATION = 0x7,
    D3D12_STATE_SUBOBJECT_TYPE_DXIL_SUBOBJECT_TO_EXPORTS_ASSOCIATION = 0x8,
    D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_SHADER_CONFIG = 0x9,
    D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_PIPELINE_CONFIG = 0xa,
    D3D12_STATE_SUBOBJECT_TYPE_HIT_GROUP = 0xb,
    D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_PIPELINE_CONFIG1 = 0xc,
    D3D12_STATE_SUBOBJECT_TYPE_MAX_VALID = 0xd,
} D3D12_STATE_SUBOBJECT_TYPE;

typedef struct D3D12_STATE_SUBOBJECT
{
    D3D12_STATE_SUBOBJECT_TYPE Type;
    const void *pDesc;
} D3D12_STATE_SUBOBJECT;

typedef enum D3D12_STATE_OBJECT_FLAGS
{
    D3D12_STATE_OBJECT_FLAG_NONE = 0x0,
    D3D12_STATE_OBJECT_FLAG_ALLOW_LOCAL_DEPENDENCIES_ON_EXTERNAL_DEFINITIONS = 0x1,
    D3D12_STATE_OBJECT_FLAG_ALLOW_EXTERNAL_DEPENDENCIES_ON_LOCAL_DEFINITIONS = 0x2,
    D3D12_STATE_OBJECT_FLAG_ALLOW_STATE_OBJECT_ADDITIONS = 0x4,
} D3D12_STATE_OBJECT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_STATE_OBJECT_FLAGS);")

typedef struct D3D12_STATE_OBJECT_CONFIG
{
    D3D12_STATE_OBJECT_FLAGS Flags;
} D3D12_STATE_OBJECT_CONFIG;

typedef struct D3D12_GLOBAL_ROOT_SIGNATURE
{
    ID3D12RootSignature *pGlobalRootSignature;
} D3D12_GLOBAL_ROOT_SIGNATURE;

typedef struct D3D12_LOCAL_ROOT_SIGNATURE
{
    ID3D12RootSignature *pLocalRootSignature;
} D3D12_LOCAL_ROOT_SIGNATURE;

typedef struct D3D12_NODE_MASK
{
    UINT NodeMask;
} D3D12_NODE_MASK;

typedef enum D3D12_EXPORT_FLAGS
{
    D3D12_EXPORT_FLAG_NONE = 0x0,
} D3D12_EXPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_EXPORT_FLAGS);")

typedef struct D3D12_EXPORT_DESC
{
    const WCHAR *Name;
    const WCHAR *ExportToRename;
    D3D12_EXPORT_FLAGS Flags;
} D3D12_EXPORT_DESC;

typedef struct D3D12_DXIL_LIBRARY_DESC
{
    D3D12_SHADER_BYTECODE DXILLibrary;
    UINT NumExports;
    D3D12_EXPORT_DESC *pExports;
} D3D12_DXIL_LIBRARY_DESC;

typedef struct D3D12_EXISTING_COLLECTION_DESC
{
    ID3D12StateObject *pExistingCollection;
    UINT NumExports;
    D3D12_EXPORT_DESC *pExports;
} D3D12_EXISTING_COLLECTION_DESC;

typedef struct D3D12_SUBOBJECT_TO_EXPORTS_ASSOCIATION
{
    const D3D12_STATE_SUBOBJECT *pSubobjectToAssociate;
    UINT NumExports;
    const WCHAR **pExports;
} D3D12_SUBOBJECT_TO_EXPORTS_ASSOCIATION;

typedef struct D3D12_DXIL_SUBOBJECT_TO_EXPORTS_ASSOCIATION
{
    const WCHAR *SubobjectToAssociate;
    UINT NumExports;
    const WCHAR **pExports;
} D3D12_DXIL_SUBOBJECT_TO_EXPORTS_ASSOCIATION;

typedef enum D3D12_HIT_GROUP_TYPE
{
    D3D12_HIT_GROUP_TYPE_TRIANGLES = 0x0,
    D3D12_HIT_GROUP_TYPE_PROCEDURAL_PRIMITIVE = 0x1,
} D3D12_HIT_GROUP_TYPE;

typedef struct D3D12_HIT_GROUP_DESC
{
    const WCHAR *HitGroupExport;
    D3D12_HIT_GROUP_TYPE Type;
    const WCHAR *AnyHitShaderImport;
    const WCHAR *ClosestHitShaderImport;
    const WCHAR *IntersectionShaderImport;
} D3D12_HIT_GROUP_DESC;

typedef struct D3D12_RAYTRACING_SHADER_CONFIG
{
    UINT MaxPayloadSizeInBytes;
    UINT MaxAttributeSizeInBytes;
} D3D12_RAYTRACING_SHADER_CONFIG;

typedef struct D3D12_RAYTRACING_PIPELINE_CONFIG
{
    UINT MaxTraceRecursionDepth;
} D3D12_RAYTRACING_PIPELINE_CONFIG;

typedef enum D3D12_RAYTRACING_PIPELINE_FLAGS
{
    D3D12_RAYTRACING_PIPELINE_FLAG_NONE = 0x0,
    D3D12_RAYTRACING_PIPELINE_FLAG_SKIP_TRIANGLES = 0x100,
    D3D12_RAYTRACING_PIPELINE_FLAG_SKIP_PROCEDURAL_PRIMITIVES = 0x200,
} D3D12_RAYTRACING_PIPELINE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RAYTRACING_PIPELINE_FLAGS);")

typedef struct D3D12_RAYTRACING_PIPELINE_CONFIG1
{
    UINT MaxTraceRecursionDepth;
    D3D12_RAYTRACING_PIPELINE_FLAGS Flags;
} D3D12_RAYTRACING_PIPELINE_CONFIG1;

typedef enum D3D12_STATE_OBJECT_TYPE
{
    D3D12_STATE_OBJECT_TYPE_COLLECTION = 0x0,
    D3D12_STATE_OBJECT_TYPE_RAYTRACING_PIPELINE = 0x3,
} D3D12_STATE_OBJECT_TYPE;

typedef struct D3D12_STATE_OBJECT_DESC
{
    D3D12_STATE_OBJECT_TYPE Type;
    UINT NumSubobjects;
    const D3D12_STATE_SUBOBJECT *pSubobjects;
} D3D12_STATE_OBJECT_DESC;

typedef enum D3D12_RAYTRACING_GEOMETRY_FLAGS
{
    D3D12_RAYTRACING_GEOMETRY_FLAG_NONE = 0x0,
    D3D12_RAYTRACING_GEOMETRY_FLAG_OPAQUE = 0x1,
    D3D12_RAYTRACING_GEOMETRY_FLAG_NO_DUPLICATE_ANYHIT_INVOCATION = 0x2,
} D3D12_RAYTRACING_GEOMETRY_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RAYTRACING_GEOMETRY_FLAGS);")

typedef enum D3D12_RAYTRACING_GEOMETRY_TYPE
{
    D3D12_RAYTRACING_GEOMETRY_TYPE_TRIANGLES = 0x0,
    D3D12_RAYTRACING_GEOMETRY_TYPE_PROCEDURAL_PRIMITIVE_AABBS = 0x1,
} D3D12_RAYTRACING_GEOMETRY_TYPE;

typedef enum D3D12_RAYTRACING_INSTANCE_FLAGS
{
    D3D12_RAYTRACING_INSTANCE_FLAG_NONE = 0x0,
    D3D12_RAYTRACING_INSTANCE_FLAG_TRIANGLE_CULL_DISABLE = 0x1,
    D3D12_RAYTRACING_INSTANCE_FLAG_TRIANGLE_FRONT_COUNTERCLOCKWISE = 0x2,
    D3D12_RAYTRACING_INSTANCE_FLAG_FORCE_OPAQUE = 0x4,
    D3D12_RAYTRACING_INSTANCE_FLAG_FORCE_NON_OPAQUE = 0x8
} D3D12_RAYTRACING_INSTANCE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RAYTRACING_INSTANCE_FLAGS);")

typedef struct D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE
{
    D3D12_GPU_VIRTUAL_ADDRESS StartAddress;
    UINT64 StrideInBytes;
} D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE;

typedef struct D3D12_GPU_VIRTUAL_ADDRESS_RANGE
{
    D3D12_GPU_VIRTUAL_ADDRESS StartAddress;
    UINT64 SizeInBytes;
} D3D12_GPU_VIRTUAL_ADDRESS_RANGE;

typedef struct D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE
{
    D3D12_GPU_VIRTUAL_ADDRESS StartAddress;
    UINT64 SizeInBytes;
    UINT64 StrideInBytes;
} D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE;

typedef struct D3D12_RAYTRACING_GEOMETRY_TRIANGLES_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS Transform3x4;
    DXGI_FORMAT IndexFormat;
    DXGI_FORMAT VertexFormat;
    UINT IndexCount;
    UINT VertexCount;
    D3D12_GPU_VIRTUAL_ADDRESS IndexBuffer;
    D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE VertexBuffer;
} D3D12_RAYTRACING_GEOMETRY_TRIANGLES_DESC;

typedef struct D3D12_RAYTRACING_AABB
{
    FLOAT MinX;
    FLOAT MinY;
    FLOAT MinZ;
    FLOAT MaxX;
    FLOAT MaxY;
    FLOAT MaxZ;
} D3D12_RAYTRACING_AABB;

typedef struct D3D12_RAYTRACING_GEOMETRY_AABBS_DESC
{
    UINT64 AABBCount;
    D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE AABBs;
} D3D12_RAYTRACING_GEOMETRY_AABBS_DESC;

typedef enum D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_NONE = 0x00,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_ALLOW_UPDATE = 0x01,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_ALLOW_COMPACTION = 0x02,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_PREFER_FAST_TRACE = 0x04,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_PREFER_FAST_BUILD = 0x08,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_MINIMIZE_MEMORY = 0x10,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_PERFORM_UPDATE = 0x20,
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS);")

typedef enum D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_CLONE = 0x0,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_COMPACT = 0x1,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_VISUALIZATION_DECODE_FOR_TOOLS = 0x2,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_SERIALIZE = 0x3,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_DESERIALIZE = 0x4,
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE;

typedef enum D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE_TOP_LEVEL = 0x0,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE_BOTTOM_LEVEL = 0x1
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE;

typedef enum D3D12_ELEMENTS_LAYOUT
{
    D3D12_ELEMENTS_LAYOUT_ARRAY = 0x0,
    D3D12_ELEMENTS_LAYOUT_ARRAY_OF_POINTERS = 0x1
} D3D12_ELEMENTS_LAYOUT;

typedef enum D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TYPE
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_COMPACTED_SIZE = 0x0,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TOOLS_VISUALIZATION  = 0x1,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_SERIALIZATION  = 0x2,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_CURRENT_SIZE  = 0x3,
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TYPE;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS DestBuffer;
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TYPE InfoType;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_COMPACTED_SIZE_DESC
{
    UINT64 CompactedSizeInBytes;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_COMPACTED_SIZE_DESC;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TOOLS_VISUALIZATION_DESC
{
    UINT64 DecodedSizeInBytes;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TOOLS_VISUALIZATION_DESC;

typedef struct D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_TOOLS_VISUALIZATION_HEADER
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE Type;
    UINT NumDescs;
} D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_TOOLS_VISUALIZATION_HEADER;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_SERIALIZATION_DESC
{
    UINT64 SerializedSizeInBytes;
    UINT64 NumBottomLevelAccelerationStructurePointers;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_SERIALIZATION_DESC;

typedef struct D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER
{
    GUID DriverOpaqueGUID;
    BYTE DriverOpaqueVersioningData[16];
} D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER;

typedef enum D3D12_SERIALIZED_DATA_TYPE
{
    D3D12_SERIALIZED_DATA_RAYTRACING_ACCELERATION_STRUCTURE = 0x0,
} D3D12_SERIALIZED_DATA_TYPE;

typedef enum D3D12_DRIVER_MATCHING_IDENTIFIER_STATUS
{
    D3D12_DRIVER_MATCHING_IDENTIFIER_COMPATIBLE_WITH_DEVICE = 0x0,
    D3D12_DRIVER_MATCHING_IDENTIFIER_UNSUPPORTED_TYPE = 0x1,
    D3D12_DRIVER_MATCHING_IDENTIFIER_UNRECOGNIZED = 0x2,
    D3D12_DRIVER_MATCHING_IDENTIFIER_INCOMPATIBLE_VERSION = 0x3,
    D3D12_DRIVER_MATCHING_IDENTIFIER_INCOMPATIBLE_TYPE = 0x4,
} D3D12_DRIVER_MATCHING_IDENTIFIER_STATUS;

typedef struct D3D12_SERIALIZED_RAYTRACING_ACCELERATION_STRUCTURE_HEADER
{
    D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER DriverMatchingIdentifier;
    UINT64 SerializedSizeInBytesIncludingHeader;
    UINT64 DeserializedSizeInBytes;
    UINT64 NumBottomLevelAccelerationStructurePointersAfterHeader;
} D3D12_SERIALIZED_RAYTRACING_ACCELERATION_STRUCTURE_HEADER;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_CURRENT_SIZE_DESC
{
    UINT64 CurrentSizeInBytes;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_CURRENT_SIZE_DESC;

typedef struct D3D12_RAYTRACING_INSTANCE_DESC
{
    FLOAT Transform[3][4];
    UINT InstanceID : 24;
    UINT InstanceMask : 8;
    UINT InstanceContributionToHitGroupIndex : 24;
    UINT Flags : 8;
    D3D12_GPU_VIRTUAL_ADDRESS AccelerationStructure;
} D3D12_RAYTRACING_INSTANCE_DESC;

typedef struct D3D12_RAYTRACING_GEOMETRY_DESC
{
    D3D12_RAYTRACING_GEOMETRY_TYPE Type;
    D3D12_RAYTRACING_GEOMETRY_FLAGS Flags;
    union
    {
        D3D12_RAYTRACING_GEOMETRY_TRIANGLES_DESC Triangles;
        D3D12_RAYTRACING_GEOMETRY_AABBS_DESC AABBs;
    };
} D3D12_RAYTRACING_GEOMETRY_DESC;

typedef struct D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE Type;
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS Flags;
    UINT NumDescs;
    D3D12_ELEMENTS_LAYOUT DescsLayout;
    union
    {
        D3D12_GPU_VIRTUAL_ADDRESS InstanceDescs;
        const D3D12_RAYTRACING_GEOMETRY_DESC *pGeometryDescs;
        const D3D12_RAYTRACING_GEOMETRY_DESC *const *ppGeometryDescs;
    };
} D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS;

typedef struct D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS DestAccelerationStructureData;
    D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS Inputs;
    D3D12_GPU_VIRTUAL_ADDRESS SourceAccelerationStructureData;
    D3D12_GPU_VIRTUAL_ADDRESS ScratchAccelerationStructureData;
} D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_DESC;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO
{
    UINT64 ResultDataMaxSizeInBytes;
    UINT64 ScratchDataSizeInBytes;
    UINT64 UpdateScratchDataSizeInBytes;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO;

typedef enum D3D12_RAY_FLAGS
{
    D3D12_RAY_FLAG_NONE = 0x00,
    D3D12_RAY_FLAG_FORCE_OPAQUE = 0x01,
    D3D12_RAY_FLAG_FORCE_NON_OPAQUE = 0x02,
    D3D12_RAY_FLAG_ACCEPT_FIRST_HIT_AND_END_SEARCH = 0x04,
    D3D12_RAY_FLAG_SKIP_CLOSEST_HIT_SHADER = 0x08,
    D3D12_RAY_FLAG_CULL_BACK_FACING_TRIANGLES = 0x10,
    D3D12_RAY_FLAG_CULL_FRONT_FACING_TRIANGLES = 0x20,
    D3D12_RAY_FLAG_CULL_OPAQUE = 0x40,
    D3D12_RAY_FLAG_CULL_NON_OPAQUE = 0x80,
    D3D12_RAY_FLAG_SKIP_TRIANGLES = 0x100,
    D3D12_RAY_FLAG_SKIP_PROCEDURAL_PRIMITIVES = 0x200,
} D3D12_RAY_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RAY_FLAGS);")

typedef enum D3D12_HIT_KIND
{
    D3D12_HIT_KIND_TRIANGLE_FRONT_FACE = 0xfe,
    D3D12_HIT_KIND_TRIANGLE_BACK_FACE = 0xff,
} D3D12_HIT_KIND;

[
    uuid(8b4f173b-2fea-4b80-8f58-4307191ab95d),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device5 : ID3D12Device4
{
    HRESULT CreateLifetimeTracker(
            ID3D12LifetimeOwner *owner, REFIID riid,
            void **tracker);

    void RemoveDevice();

    HRESULT EnumerateMetaCommands(
            UINT *meta_commands_count,
            D3D12_META_COMMAND_DESC *descs);

    HRESULT EnumerateMetaCommandParameters(
            REFGUID command_id,
            D3D12_META_COMMAND_PARAMETER_STAGE stage,
            UINT *total_structure_size_in_bytes,
            UINT *parameter_count,
            D3D12_META_COMMAND_PARAMETER_DESC *parameter_descs);

    HRESULT CreateMetaCommand(
            REFGUID command_id, UINT node_mask,
            const void *creation_parameters_data,
            SIZE_T creation_parameters_data_size_in_bytes,
            REFIID riid, void **meta_command);

    HRESULT CreateStateObject(
            const D3D12_STATE_OBJECT_DESC *desc, REFIID riid,
            void **state_object);

    void GetRaytracingAccelerationStructurePrebuildInfo(
            const D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS *desc,
            D3D12_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO *info);

    D3D12_DRIVER_MATCHING_IDENTIFIER_STATUS CheckDriverMatchingIdentifier(
            D3D12_SERIALIZED_DATA_TYPE serialized_data_type,
            const D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER *identifier_to_check);
}

typedef enum D3D12_AUTO_BREADCRUMB_OP
{
    D3D12_AUTO_BREADCRUMB_OP_SETMARKER = 0x0,
    D3D12_AUTO_BREADCRUMB_OP_BEGINEVENT = 0x1,
    D3D12_AUTO_BREADCRUMB_OP_ENDEVENT = 0x2,
    D3D12_AUTO_BREADCRUMB_OP_DRAWINSTANCED = 0x3,
    D3D12_AUTO_BREADCRUMB_OP_DRAWINDEXEDINSTANCED = 0x4,
    D3D12_AUTO_BREADCRUMB_OP_EXECUTEINDIRECT = 0x5,
    D3D12_AUTO_BREADCRUMB_OP_DISPATCH = 0x6,
    D3D12_AUTO_BREADCRUMB_OP_COPYBUFFERREGION = 0x7,
    D3D12_AUTO_BREADCRUMB_OP_COPYTEXTUREREGION = 0x8,
    D3D12_AUTO_BREADCRUMB_OP_COPYRESOURCE = 0x9,
    D3D12_AUTO_BREADCRUMB_OP_COPYTILES = 0xa,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVESUBRESOURCE = 0xb,
    D3D12_AUTO_BREADCRUMB_OP_CLEARRENDERTARGETVIEW = 0xc,
    D3D12_AUTO_BREADCRUMB_OP_CLEARUNORDEREDACCESSVIEW = 0xd,
    D3D12_AUTO_BREADCRUMB_OP_CLEARDEPTHSTENCILVIEW = 0xe,
    D3D12_AUTO_BREADCRUMB_OP_RESOURCEBARRIER = 0xf,
    D3D12_AUTO_BREADCRUMB_OP_EXECUTEBUNDLE = 0x10,
    D3D12_AUTO_BREADCRUMB_OP_PRESENT = 0x11,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVEQUERYDATA = 0x12,
    D3D12_AUTO_BREADCRUMB_OP_BEGINSUBMISSION = 0x13,
    D3D12_AUTO_BREADCRUMB_OP_ENDSUBMISSION = 0x14,
    D3D12_AUTO_BREADCRUMB_OP_DECODEFRAME = 0x15,
    D3D12_AUTO_BREADCRUMB_OP_PROCESSFRAMES = 0x16,
    D3D12_AUTO_BREADCRUMB_OP_ATOMICCOPYBUFFERUINT = 0x17,
    D3D12_AUTO_BREADCRUMB_OP_ATOMICCOPYBUFFERUINT64 = 0x18,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVESUBRESOURCEREGION = 0x19,
    D3D12_AUTO_BREADCRUMB_OP_WRITEBUFFERIMMEDIATE = 0x1a,
    D3D12_AUTO_BREADCRUMB_OP_DECODEFRAME1 = 0x1b,
    D3D12_AUTO_BREADCRUMB_OP_SETPROTECTEDRESOURCESESSION = 0x1c,
    D3D12_AUTO_BREADCRUMB_OP_DECODEFRAME2 = 0x1d,
    D3D12_AUTO_BREADCRUMB_OP_PROCESSFRAMES1 = 0x1e,
    D3D12_AUTO_BREADCRUMB_OP_BUILDRAYTRACINGACCELERATIONSTRUCTURE = 0x1f,
    D3D12_AUTO_BREADCRUMB_OP_EMITRAYTRACINGACCELERATIONSTRUCTUREPOSTBUILDINFO = 0x20,
    D3D12_AUTO_BREADCRUMB_OP_COPYRAYTRACINGACCELERATIONSTRUCTURE = 0x21,
    D3D12_AUTO_BREADCRUMB_OP_DISPATCHRAYS = 0x22,
    D3D12_AUTO_BREADCRUMB_OP_INITIALIZEMETACOMMAND = 0x23,
    D3D12_AUTO_BREADCRUMB_OP_EXECUTEMETACOMMAND = 0x24,
    D3D12_AUTO_BREADCRUMB_OP_ESTIMATEMOTION = 0x25,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVEMOTIONVECTORHEAP = 0x26,
    D3D12_AUTO_BREADCRUMB_OP_SETPIPELINESTATE1 = 0x27,
    D3D12_AUTO_BREADCRUMB_OP_INITIALIZEEXTENSIONCOMMAND = 0x28,
    D3D12_AUTO_BREADCRUMB_OP_EXECUTEEXTENSIONCOMMAND = 0x29,
    D3D12_AUTO_BREADCRUMB_OP_DISPATCHMESH = 0x2a,
    D3D12_AUTO_BREADCRUMB_OP_ENCODEFRAME = 0x2b,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVEENCODEROUTPUTMETADATA = 0x2c,
} D3D12_AUTO_BREADCRUMB_OP;

typedef struct D3D12_AUTO_BREADCRUMB_NODE
{
    const char *pCommandListDebugNameA;
    const WCHAR *pCommandListDebugNameW;
    const char *pCommandQueueDebugNameA;
    const WCHAR *pCommandQueueDebugNameW;
    ID3D12GraphicsCommandList *pCommandList;
    ID3D12CommandQueue *pCommandQueue;
    UINT32 BreadcrumbCount;
    const UINT32 *pLastBreadcrumbValue;
    const D3D12_AUTO_BREADCRUMB_OP *pCommandHistory;
    const struct D3D12_AUTO_BREADCRUMB_NODE *pNext;
} D3D12_AUTO_BREADCRUMB_NODE;

typedef struct D3D12_DRED_BREADCRUMB_CONTEXT
{
    UINT BreadcrumbIndex;
    const WCHAR *pContextString;
} D3D12_DRED_BREADCRUMB_CONTEXT;

typedef struct D3D12_AUTO_BREADCRUMB_NODE1
{
    const char *pCommandListDebugNameA;
    const WCHAR *pCommandListDebugNameW;
    const char *pCommandQueueDebugNameA;
    const WCHAR *pCommandQueueDebugNameW;
    ID3D12GraphicsCommandList *pCommandList;
    ID3D12CommandQueue *pCommandQueue;
    UINT BreadcrumbCount;
    const UINT *pLastBreadcrumbValue;
    const D3D12_AUTO_BREADCRUMB_OP *pCommandHistory;
    const struct D3D12_AUTO_BREADCRUMB_NODE1 *pNext;
    UINT BreadcrumbContextsCount;
    D3D12_DRED_BREADCRUMB_CONTEXT *pBreadcrumbContexts;
} D3D12_AUTO_BREADCRUMB_NODE1;

typedef enum D3D12_DRED_VERSION
{
    D3D12_DRED_VERSION_1_0 = 0x1,
    D3D12_DRED_VERSION_1_1 = 0x2,
    D3D12_DRED_VERSION_1_2 = 0x3,
    D3D12_DRED_VERSION_1_3 = 0x4,
} D3D12_DRED_VERSION;

typedef enum D3D12_DRED_FLAGS
{
    D3D12_DRED_FLAG_NONE = 0x0,
    D3D12_DRED_FLAG_FORCE_ENABLE = 0x1,
    D3D12_DRED_FLAG_DISABLE_AUTOBREADCRUMBS = 0x2,
} D3D12_DRED_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_DRED_FLAGS);")

typedef enum D3D12_DRED_ENABLEMENT
{
    D3D12_DRED_ENABLEMENT_SYSTEM_CONTROLLED = 0x0,
    D3D12_DRED_ENABLEMENT_FORCED_OFF = 0x1,
    D3D12_DRED_ENABLEMENT_FORCED_ON = 0x2,
} D3D12_DRED_ENABLEMENT;

typedef struct D3D12_DEVICE_REMOVED_EXTENDED_DATA
{
    D3D12_DRED_FLAGS Flags;
    D3D12_AUTO_BREADCRUMB_NODE *pHeadAutoBreadcrumbNode;
} D3D12_DEVICE_REMOVED_EXTENDED_DATA;

typedef enum D3D12_DRED_ALLOCATION_TYPE
{
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_QUEUE = 0x13,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_ALLOCATOR = 0x14,
    D3D12_DRED_ALLOCATION_TYPE_PIPELINE_STATE = 0x15,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_LIST = 0x16,
    D3D12_DRED_ALLOCATION_TYPE_FENCE = 0x17,
    D3D12_DRED_ALLOCATION_TYPE_DESCRIPTOR_HEAP = 0x18,
    D3D12_DRED_ALLOCATION_TYPE_HEAP = 0x19,
    D3D12_DRED_ALLOCATION_TYPE_QUERY_HEAP = 0x1b,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_SIGNATURE = 0x1c,
    D3D12_DRED_ALLOCATION_TYPE_PIPELINE_LIBRARY = 0x1d,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_DECODER = 0x1e,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_PROCESSOR = 0x20,
    D3D12_DRED_ALLOCATION_TYPE_RESOURCE = 0x22,
    D3D12_DRED_ALLOCATION_TYPE_PASS = 0x23,
    D3D12_DRED_ALLOCATION_TYPE_CRYPTOSESSION = 0x24,
    D3D12_DRED_ALLOCATION_TYPE_CRYPTOSESSIONPOLICY = 0x25,
    D3D12_DRED_ALLOCATION_TYPE_PROTECTEDRESOURCESESSION = 0x26,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_DECODER_HEAP = 0x27,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_POOL = 0x28,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_RECORDER = 0x29,
    D3D12_DRED_ALLOCATION_TYPE_STATE_OBJECT = 0x2a,
    D3D12_DRED_ALLOCATION_TYPE_METACOMMAND = 0x2b,
    D3D12_DRED_ALLOCATION_TYPE_SCHEDULINGGROUP = 0x2c,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_MOTION_ESTIMATOR = 0x2d,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_MOTION_VECTOR_HEAP = 0x2e,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_EXTENSION_COMMAND = 0x2f,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_ENCODER = 0x30,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_ENCODER_HEAP = 0x31,
    D3D12_DRED_ALLOCATION_TYPE_INVALID = 0xffffffff,
} D3D12_DRED_ALLOCATION_TYPE;

typedef struct D3D12_DRED_ALLOCATION_NODE
{
    const char *ObjectNameA;
    const WCHAR *ObjectNameW;
    D3D12_DRED_ALLOCATION_TYPE AllocationType;
    const struct D3D12_DRED_ALLOCATION_NODE *pNext;
} D3D12_DRED_ALLOCATION_NODE;

typedef struct D3D12_DRED_ALLOCATION_NODE1
{
    const char *ObjectNameA;
    const WCHAR *ObjectNameW;
    D3D12_DRED_ALLOCATION_TYPE AllocationType;
    const struct D3D12_DRED_ALLOCATION_NODE1 *pNext;
    const IUnknown *pObject;
} D3D12_DRED_ALLOCATION_NODE1;

typedef struct D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT
{
    const D3D12_AUTO_BREADCRUMB_NODE *pHeadAutoBreadcrumbNode;
} D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT;

typedef struct D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1
{
    const D3D12_AUTO_BREADCRUMB_NODE1 *pHeadAutoBreadcrumbNode;
} D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1;

typedef struct D3D12_DRED_PAGE_FAULT_OUTPUT
{
    D3D12_GPU_VIRTUAL_ADDRESS PageFaultVA;
    const D3D12_DRED_ALLOCATION_NODE *pHeadExistingAllocationNode;
    const D3D12_DRED_ALLOCATION_NODE *pHeadRecentFreedAllocationNode;
} D3D12_DRED_PAGE_FAULT_OUTPUT;

typedef struct D3D12_DRED_PAGE_FAULT_OUTPUT1
{
    D3D12_GPU_VIRTUAL_ADDRESS PageFaultVA;
    const D3D12_DRED_ALLOCATION_NODE1 *pHeadExistingAllocationNode;
    const D3D12_DRED_ALLOCATION_NODE1 *pHeadRecentFreedAllocationNode;
} D3D12_DRED_PAGE_FAULT_OUTPUT1;

typedef enum D3D12_DRED_PAGE_FAULT_FLAGS
{
    D3D12_DRED_PAGE_FAULT_FLAGS_NONE = 0x0,
} D3D12_DRED_PAGE_FAULT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_DRED_PAGE_FAULT_FLAGS);")

typedef enum D3D12_DRED_DEVICE_STATE
{
    D3D12_DRED_DEVICE_STATE_UNKNOWN = 0x0,
    D3D12_DRED_DEVICE_STATE_HUNG = 0x3,
    D3D12_DRED_DEVICE_STATE_FAULT = 0x6,
    D3D12_DRED_DEVICE_STATE_PAGEFAULT = 0x7,
} D3D12_DRED_DEVICE_STATE;

typedef struct D3D12_DRED_PAGE_FAULT_OUTPUT2
{
    D3D12_GPU_VIRTUAL_ADDRESS PageFaultVA;
    const D3D12_DRED_ALLOCATION_NODE1 *pHeadExistingAllocationNode;
    const D3D12_DRED_ALLOCATION_NODE1 *pHeadRecentFreedAllocationNode;
    D3D12_DRED_PAGE_FAULT_FLAGS PageFaultFlags;
} D3D12_DRED_PAGE_FAULT_OUTPUT2;

typedef struct D3D12_DEVICE_REMOVED_EXTENDED_DATA1
{
    HRESULT DeviceRemovedReason;
    D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT AutoBreadcrumbsOutput;
    D3D12_DRED_PAGE_FAULT_OUTPUT PageFaultOutput;
} D3D12_DEVICE_REMOVED_EXTENDED_DATA1;

typedef struct D3D12_DEVICE_REMOVED_EXTENDED_DATA2
{
    HRESULT DeviceRemovedReason;
    D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1 AutoBreadcrumbsOutput;
    D3D12_DRED_PAGE_FAULT_OUTPUT1 PageFaultOutput;
} D3D12_DEVICE_REMOVED_EXTENDED_DATA2;

typedef struct D3D12_DEVICE_REMOVED_EXTENDED_DATA3
{
    HRESULT DeviceRemovedReason;
    D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1 AutoBreadcrumbsOutput;
    D3D12_DRED_PAGE_FAULT_OUTPUT2 PageFaultOutput;
    D3D12_DRED_DEVICE_STATE DeviceState;
} D3D12_DEVICE_REMOVED_EXTENDED_DATA3;

typedef struct D3D12_VERSIONED_DEVICE_REMOVED_EXTENDED_DATA
{
    D3D12_DRED_VERSION Version;
    union
    {
        D3D12_DEVICE_REMOVED_EXTENDED_DATA Dred_1_0;
        D3D12_DEVICE_REMOVED_EXTENDED_DATA1 Dred_1_1;
        D3D12_DEVICE_REMOVED_EXTENDED_DATA2 Dred_1_2;
        D3D12_DEVICE_REMOVED_EXTENDED_DATA3 Dred_1_3;
    };
} D3D12_VERSIONED_DEVICE_REMOVED_EXTENDED_DATA;

[
    uuid(82bc481c-6b9b-4030-aedb-7ee3d1df1e63),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DeviceRemovedExtendedDataSettings : IUnknown
{
    void SetAutoBreadcrumbsEnablement(D3D12_DRED_ENABLEMENT enablement);
    void SetPageFaultEnablement(D3D12_DRED_ENABLEMENT enablement);
    void SetWatsonDumpEnablement(D3D12_DRED_ENABLEMENT enablement);
}

[
    uuid(dbd5ae51-3317-4f0a-adf9-1d7cedcaae0b),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DeviceRemovedExtendedDataSettings1 : ID3D12DeviceRemovedExtendedDataSettings
{
    void SetBreadcrumbContextEnablement(D3D12_DRED_ENABLEMENT enablement);
}

[
    uuid(98931d33-5ae8-4791-aa3c-1a73a2934e71),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DeviceRemovedExtendedData : IUnknown
{
    HRESULT GetAutoBreadcrumbsOutput(D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT *output);
    HRESULT GetPageFaultAllocationOutput(D3D12_DRED_PAGE_FAULT_OUTPUT *output);
}

[
    uuid(9727a022-cf1d-4dda-9eba-effa653fc506),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DeviceRemovedExtendedData1 : ID3D12DeviceRemovedExtendedData
{
    HRESULT GetAutoBreadcrumbsOutput1(D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1 *output);
    HRESULT GetPageFaultAllocationOutput1(D3D12_DRED_PAGE_FAULT_OUTPUT1 *output);
}

[
    uuid(67fc5816-e4ca-4915-bf18-42541272da54),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DeviceRemovedExtendedData2 : ID3D12DeviceRemovedExtendedData1
{
    HRESULT GetPageFaultAllocationOutput2(D3D12_DRED_PAGE_FAULT_OUTPUT2 *output);
    D3D12_DRED_DEVICE_STATE GetDeviceState();
}

typedef enum D3D12_BACKGROUND_PROCESSING_MODE
{
    D3D12_BACKGROUND_PROCESSING_MODE_ALLOWED = 0x0,
    D3D12_BACKGROUND_PROCESSING_MODE_ALLOW_INTRUSIVE_MEASUREMENTS = 0x1,
    D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_BACKGROUND_WORK = 0x2,
    D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_PROFILING_BY_SYSTEM = 0x3,
} D3D12_BACKGROUND_PROCESSING_MODE;

typedef enum D3D12_MEASUREMENTS_ACTION
{
    D3D12_MEASUREMENTS_ACTION_KEEP_ALL = 0x0,
    D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS = 0x1,
    D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS_HIGH_PRIORITY = 0x2,
    D3D12_MEASUREMENTS_ACTION_DISCARD_PREVIOUS = 0x3,
} D3D12_MEASUREMENTS_ACTION;

[
    uuid(c70b221b-40e4-4a17-89af-025a0727a6dc),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device6 : ID3D12Device5
{
    HRESULT SetBackgroundProcessingMode(
            D3D12_BACKGROUND_PROCESSING_MODE mode,
            D3D12_MEASUREMENTS_ACTION action,
            HANDLE event,
            BOOL *further_measurements_desired);
}

cpp_quote("DEFINE_GUID(D3D12_PROTECTED_RESOURCES_SESSION_HARDWARE_PROTECTED, 0x62b0084e, 0xc70e, 0x4daa, 0xa1, 0x09, 0x30, 0xff, 0x8d, 0x5a, 0x04, 0x82);")

typedef struct D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_TYPE_COUNT
{
    UINT NodeIndex;
    UINT Count;
} D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_TYPE_COUNT;

typedef struct D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_TYPES
{
    UINT NodeIndex;
    UINT Count;
    GUID *pTypes;
} D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_TYPES;

typedef struct D3D12_PROTECTED_RESOURCE_SESSION_DESC1
{
    UINT NodeMask;
    D3D12_PROTECTED_RESOURCE_SESSION_FLAGS Flags;
    GUID ProtectionType;
} D3D12_PROTECTED_RESOURCE_SESSION_DESC1;

[
    uuid(d6f12dd6-76fb-406e-8961-4296eefc0409),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12ProtectedResourceSession1 : ID3D12ProtectedResourceSession
{
    D3D12_PROTECTED_RESOURCE_SESSION_DESC1 GetDesc1();
}

[
    uuid(5c014b53-68a1-4b9b-8bd1-dd6046b9358b),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device7 : ID3D12Device6
{
    HRESULT AddToStateObject(
            const D3D12_STATE_OBJECT_DESC *addition,
            ID3D12StateObject *state_object_to_grow_from,
            REFIID riid, void **new_state_object);

    HRESULT CreateProtectedResourceSession1(
            const D3D12_PROTECTED_RESOURCE_SESSION_DESC1 *desc,
            REFIID riid, void **session);
}

[
    uuid(9218e6bb-f944-4f7e-a75c-b1b2c7b701f3),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device8 : ID3D12Device7
{
    D3D12_RESOURCE_ALLOCATION_INFO GetResourceAllocationInfo2(
            UINT visible_mask,
            UINT resource_descs_count,
            const D3D12_RESOURCE_DESC1 *resource_descs,
            D3D12_RESOURCE_ALLOCATION_INFO1 *resource_allocation_info1);

    HRESULT CreateCommittedResource2(
            const D3D12_HEAP_PROPERTIES *heap_properties,
            D3D12_HEAP_FLAGS heap_flags,
            const D3D12_RESOURCE_DESC1 *desc,
            D3D12_RESOURCE_STATES initial_resource_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            ID3D12ProtectedResourceSession *protected_session,
            REFIID riid_resource, void **resource);

    HRESULT CreatePlacedResource1(
            ID3D12Heap *heap,
            UINT64 heap_offset,
            const D3D12_RESOURCE_DESC1 *desc,
            D3D12_RESOURCE_STATES initial_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            REFIID riid, void **resource);

    void CreateSamplerFeedbackUnorderedAccessView(
            ID3D12Resource *targeted_resource,
            ID3D12Resource *feedback_resource,
            D3D12_CPU_DESCRIPTOR_HANDLE dst_descriptor);

    void GetCopyableFootprints1(
            const D3D12_RESOURCE_DESC1 *resource_desc,
            UINT first_subresource,
            UINT subresources_count,
            UINT64 base_offset,
            D3D12_PLACED_SUBRESOURCE_FOOTPRINT *layouts,
            UINT *rows_count,
            UINT64 *row_size_in_bytes,
            UINT64 *total_bytes);
}

[
    uuid(9d5e227a-4430-4161-88b3-3eca6bb16e19),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Resource1 : ID3D12Resource
{
    HRESULT GetProtectedResourceSession(REFIID riid, void **protected_session);
}

[
    uuid(be36ec3b-ea85-4aeb-a45a-e9d76404a495),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Resource2 : ID3D12Resource1
{
    D3D12_RESOURCE_DESC1 GetDesc1();
}

[
    uuid(572f7389-2168-49e3-9693-d6df5871bf6d),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Heap1 : ID3D12Heap
{
    HRESULT GetProtectedResourceSession(REFIID riid, void **protected_session);
}

typedef enum D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE
{
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_DISCARD = 0x0,
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE = 0x1,
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_CLEAR = 0x2,
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_NO_ACCESS = 0x3,
} D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE;

typedef struct D3D12_RENDER_PASS_BEGINNING_ACCESS_CLEAR_PARAMETERS
{
    D3D12_CLEAR_VALUE ClearValue;
} D3D12_RENDER_PASS_BEGINNING_ACCESS_CLEAR_PARAMETERS;

typedef struct D3D12_RENDER_PASS_BEGINNING_ACCESS
{
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE Type;

    union
    {
        D3D12_RENDER_PASS_BEGINNING_ACCESS_CLEAR_PARAMETERS Clear;
    };
} D3D12_RENDER_PASS_BEGINNING_ACCESS;

typedef enum D3D12_RENDER_PASS_ENDING_ACCESS_TYPE
{
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_DISCARD = 0x0,
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE = 0x1,
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_RESOLVE = 0x2,
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_NO_ACCESS = 0x3,
} D3D12_RENDER_PASS_ENDING_ACCESS_TYPE;

typedef struct D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_SUBRESOURCE_PARAMETERS
{
    UINT SrcSubresource;
    UINT DstSubresource;
    UINT DstX;
    UINT DstY;
    D3D12_RECT SrcRect;
} D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_SUBRESOURCE_PARAMETERS;

typedef struct D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_PARAMETERS
{
    ID3D12Resource *pSrcResource;
    ID3D12Resource *pDstResource;
    UINT SubresourceCount;
    const D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_SUBRESOURCE_PARAMETERS *pSubresourceParameters;
    DXGI_FORMAT Format;
    D3D12_RESOLVE_MODE ResolveMode;
    BOOL PreserveResolveSource;
} D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_PARAMETERS;

typedef struct D3D12_RENDER_PASS_ENDING_ACCESS
{
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE Type;
    union
    {
        D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_PARAMETERS Resolve;
    };
} D3D12_RENDER_PASS_ENDING_ACCESS;

typedef struct D3D12_RENDER_PASS_RENDER_TARGET_DESC
{
    D3D12_CPU_DESCRIPTOR_HANDLE cpuDescriptor;
    D3D12_RENDER_PASS_BEGINNING_ACCESS BeginningAccess;
    D3D12_RENDER_PASS_ENDING_ACCESS EndingAccess;
} D3D12_RENDER_PASS_RENDER_TARGET_DESC;

typedef struct D3D12_RENDER_PASS_DEPTH_STENCIL_DESC
{
    D3D12_CPU_DESCRIPTOR_HANDLE cpuDescriptor;
    D3D12_RENDER_PASS_BEGINNING_ACCESS DepthBeginningAccess;
    D3D12_RENDER_PASS_BEGINNING_ACCESS StencilBeginningAccess;
    D3D12_RENDER_PASS_ENDING_ACCESS DepthEndingAccess;
    D3D12_RENDER_PASS_ENDING_ACCESS StencilEndingAccess;
} D3D12_RENDER_PASS_DEPTH_STENCIL_DESC;

typedef enum D3D12_RENDER_PASS_FLAGS
{
    D3D12_RENDER_PASS_FLAG_NONE = 0x0,
    D3D12_RENDER_PASS_FLAG_ALLOW_UAV_WRITES = 0x1,
    D3D12_RENDER_PASS_FLAG_SUSPENDING_PASS = 0x2,
    D3D12_RENDER_PASS_FLAG_RESUMING_PASS = 0x4
} D3D12_RENDER_PASS_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RENDER_PASS_FLAGS);")

[
    uuid(dbb84c27-36ce-4fc9-b801-f048c46ac570),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12MetaCommand : ID3D12Pageable
{
    UINT64 GetRequiredParameterResourceSize(
            D3D12_META_COMMAND_PARAMETER_STAGE stage,
            UINT parameter_index);
}

typedef struct D3D12_DISPATCH_RAYS_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE RayGenerationShaderRecord;
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE MissShaderTable;
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE HitGroupTable;
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE CallableShaderTable;
    UINT Width;
    UINT Height;
    UINT Depth;
} D3D12_DISPATCH_RAYS_DESC;

[
    uuid(8754318e-d3a9-4541-98cf-645b50dc4874),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList4 : ID3D12GraphicsCommandList3
{
    void BeginRenderPass(
            UINT render_targets_count,
            const D3D12_RENDER_PASS_RENDER_TARGET_DESC *render_targets,
            const D3D12_RENDER_PASS_DEPTH_STENCIL_DESC *depth_stencil,
            D3D12_RENDER_PASS_FLAGS flags);

    void EndRenderPass();

    void InitializeMetaCommand(
            ID3D12MetaCommand *meta_command,
            const void *initialization_parameters_data,
            SIZE_T initialization_parameters_data_size_in_bytes);

    void ExecuteMetaCommand(
            ID3D12MetaCommand *meta_command,
            const void *execution_parameters_data,
            SIZE_T execution_parameters_data_size_in_bytes);

    void BuildRaytracingAccelerationStructure(
            const D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_DESC *desc,
            UINT postbuild_info_descs_count,
            const D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC *postbuild_info_descs);

    void EmitRaytracingAccelerationStructurePostbuildInfo(
            const D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC *desc,
            UINT src_acceleration_structures_count,
            const D3D12_GPU_VIRTUAL_ADDRESS *src_acceleration_structure_data);

    void CopyRaytracingAccelerationStructure(
            D3D12_GPU_VIRTUAL_ADDRESS dst_acceleration_structure_data,
            D3D12_GPU_VIRTUAL_ADDRESS src_acceleration_structure_data,
            D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE mode);

    void SetPipelineState1(ID3D12StateObject *state_object);

    void DispatchRays(const D3D12_DISPATCH_RAYS_DESC *desc);
}

typedef enum D3D12_SHADER_CACHE_MODE
{
    D3D12_SHADER_CACHE_MODE_MEMORY = 0x0,
    D3D12_SHADER_CACHE_MODE_DISK = 0x1,
} D3D12_SHADER_CACHE_MODE;

typedef enum D3D12_SHADER_CACHE_FLAGS
{
    D3D12_SHADER_CACHE_FLAG_NONE = 0x0,
    D3D12_SHADER_CACHE_FLAG_DRIVER_VERSIONED = 0x1,
    D3D12_SHADER_CACHE_FLAG_USE_WORKING_DIR = 0x2,
} D3D12_SHADER_CACHE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_SHADER_CACHE_FLAGS);")

typedef struct D3D12_SHADER_CACHE_SESSION_DESC
{
    GUID Identifier;
    D3D12_SHADER_CACHE_MODE Mode;
    D3D12_SHADER_CACHE_FLAGS Flags;

    UINT MaximumInMemoryCacheSizeBytes;
    UINT MaximumInMemoryCacheEntries;

    UINT MaximumValueFileSizeBytes;

    UINT64 Version;
} D3D12_SHADER_CACHE_SESSION_DESC;

typedef enum D3D12_BARRIER_LAYOUT
{
    D3D12_BARRIER_LAYOUT_UNDEFINED = 0xffffffff,
    D3D12_BARRIER_LAYOUT_COMMON = 0x0,
    D3D12_BARRIER_LAYOUT_PRESENT = 0x0,
    D3D12_BARRIER_LAYOUT_GENERIC_READ = 0x1,
    D3D12_BARRIER_LAYOUT_RENDER_TARGET = 0x2,
    D3D12_BARRIER_LAYOUT_UNORDERED_ACCESS = 0x3,
    D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_WRITE = 0x4,
    D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_READ = 0x5,
    D3D12_BARRIER_LAYOUT_SHADER_RESOURCE = 0x6,
    D3D12_BARRIER_LAYOUT_COPY_SOURCE = 0x7,
    D3D12_BARRIER_LAYOUT_COPY_DEST = 0x8,
    D3D12_BARRIER_LAYOUT_RESOLVE_SOURCE = 0x9,
    D3D12_BARRIER_LAYOUT_RESOLVE_DEST = 0xa,
    D3D12_BARRIER_LAYOUT_SHADING_RATE_SOURCE = 0xb,
    D3D12_BARRIER_LAYOUT_VIDEO_DECODE_READ = 0xc,
    D3D12_BARRIER_LAYOUT_VIDEO_DECODE_WRITE = 0xd,
    D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_READ = 0xe,
    D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_WRITE = 0xf,
    D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_READ = 0x10,
    D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_WRITE = 0x11,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COMMON = 0x12,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_GENERIC_READ = 0x13,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_UNORDERED_ACCESS = 0x14,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_SHADER_RESOURCE = 0x15,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_SOURCE = 0x16,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_DEST = 0x17,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COMMON = 0x18,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_GENERIC_READ = 0x19,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_UNORDERED_ACCESS = 0x1a,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_SHADER_RESOURCE = 0x1b,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_SOURCE = 0x1c,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_DEST = 0x1d,
    D3D12_BARRIER_LAYOUT_VIDEO_QUEUE_COMMON = 0x1e,
} D3D12_BARRIER_LAYOUT;

typedef enum D3D12_BARRIER_SYNC
{
    D3D12_BARRIER_SYNC_NONE = 0x0,
    D3D12_BARRIER_SYNC_ALL = 0x1,
    D3D12_BARRIER_SYNC_DRAW = 0x2,
    D3D12_BARRIER_SYNC_INPUT_ASSEMBLER = 0x4,
    D3D12_BARRIER_SYNC_VERTEX_SHADING = 0x8,
    D3D12_BARRIER_SYNC_PIXEL_SHADING = 0x10,
    D3D12_BARRIER_SYNC_DEPTH_STENCIL = 0x20,
    D3D12_BARRIER_SYNC_RENDER_TARGET = 0x40,
    D3D12_BARRIER_SYNC_COMPUTE_SHADING = 0x80,
    D3D12_BARRIER_SYNC_RAYTRACING = 0x100,
    D3D12_BARRIER_SYNC_COPY = 0x200,
    D3D12_BARRIER_SYNC_RESOLVE = 0x400,
    D3D12_BARRIER_SYNC_EXECUTE_INDIRECT = 0x800,
    D3D12_BARRIER_SYNC_PREDICATION = 0x800,
    D3D12_BARRIER_SYNC_ALL_SHADING = 0x1000,
    D3D12_BARRIER_SYNC_NON_PIXEL_SHADING = 0x2000,
    D3D12_BARRIER_SYNC_EMIT_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO = 0x4000,
    D3D12_BARRIER_SYNC_VIDEO_DECODE = 0x100000,
    D3D12_BARRIER_SYNC_VIDEO_PROCESS = 0x200000,
    D3D12_BARRIER_SYNC_VIDEO_ENCODE = 0x400000,
    D3D12_BARRIER_SYNC_BUILD_RAYTRACING_ACCELERATION_STRUCTURE = 0x800000,
    D3D12_BARRIER_SYNC_COPY_RAYTRACING_ACCELERATION_STRUCTURE = 0x1000000,
    D3D12_BARRIER_SYNC_SPLIT = 0x80000000,
} D3D12_BARRIER_SYNC;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_BARRIER_SYNC);")

typedef enum D3D12_BARRIER_ACCESS
{
    D3D12_BARRIER_ACCESS_COMMON = 0,
    D3D12_BARRIER_ACCESS_VERTEX_BUFFER = 0x1,
    D3D12_BARRIER_ACCESS_CONSTANT_BUFFER = 0x2,
    D3D12_BARRIER_ACCESS_INDEX_BUFFER = 0x4,
    D3D12_BARRIER_ACCESS_RENDER_TARGET = 0x8,
    D3D12_BARRIER_ACCESS_UNORDERED_ACCESS = 0x10,
    D3D12_BARRIER_ACCESS_DEPTH_STENCIL_WRITE = 0x20,
    D3D12_BARRIER_ACCESS_DEPTH_STENCIL_READ = 0x40,
    D3D12_BARRIER_ACCESS_SHADER_RESOURCE = 0x80,
    D3D12_BARRIER_ACCESS_STREAM_OUTPUT = 0x100,
    D3D12_BARRIER_ACCESS_INDIRECT_ARGUMENT = 0x200,
    D3D12_BARRIER_ACCESS_PREDICATION = 0x200,
    D3D12_BARRIER_ACCESS_COPY_DEST = 0x400,
    D3D12_BARRIER_ACCESS_COPY_SOURCE = 0x800,
    D3D12_BARRIER_ACCESS_RESOLVE_DEST = 0x1000,
    D3D12_BARRIER_ACCESS_RESOLVE_SOURCE = 0x2000,
    D3D12_BARRIER_ACCESS_RAYTRACING_ACCELERATION_STRUCTURE_READ = 0x4000,
    D3D12_BARRIER_ACCESS_RAYTRACING_ACCELERATION_STRUCTURE_WRITE = 0x8000,
    D3D12_BARRIER_ACCESS_SHADING_RATE_SOURCE = 0x10000,
    D3D12_BARRIER_ACCESS_VIDEO_DECODE_READ = 0x20000,
    D3D12_BARRIER_ACCESS_VIDEO_DECODE_WRITE = 0x40000,
    D3D12_BARRIER_ACCESS_VIDEO_PROCESS_READ = 0x80000,
    D3D12_BARRIER_ACCESS_VIDEO_PROCESS_WRITE = 0x100000,
    D3D12_BARRIER_ACCESS_VIDEO_ENCODE_READ = 0x200000,
    D3D12_BARRIER_ACCESS_VIDEO_ENCODE_WRITE = 0x400000,
    D3D12_BARRIER_ACCESS_NO_ACCESS = 0x80000000,
} D3D12_BARRIER_ACCESS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_BARRIER_ACCESS);")

typedef enum D3D12_BARRIER_TYPE
{
    D3D12_BARRIER_TYPE_GLOBAL = 0x0,
    D3D12_BARRIER_TYPE_TEXTURE = 0x1,
    D3D12_BARRIER_TYPE_BUFFER = 0x2,
} D3D12_BARRIER_TYPE;

typedef enum D3D12_TEXTURE_BARRIER_FLAGS
{
    D3D12_TEXTURE_BARRIER_FLAG_NONE = 0x0,
    D3D12_TEXTURE_BARRIER_FLAG_DISCARD = 0x1,
} D3D12_TEXTURE_BARRIER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_TEXTURE_BARRIER_FLAGS);")

typedef struct D3D12_BARRIER_SUBRESOURCE_RANGE
{
    UINT IndexOrFirstMipLevel;
    UINT NumMipLevels;
    UINT FirstArraySlice;
    UINT NumArraySlices;
    UINT FirstPlane;
    UINT NumPlanes;
} D3D12_BARRIER_SUBRESOURCE_RANGE;

typedef struct D3D12_GLOBAL_BARRIER
{
    D3D12_BARRIER_SYNC SyncBefore;
    D3D12_BARRIER_SYNC SyncAfter;
    D3D12_BARRIER_ACCESS AccessBefore;
    D3D12_BARRIER_ACCESS AccessAfter;
} D3D12_GLOBAL_BARRIER;

typedef struct D3D12_TEXTURE_BARRIER
{
    D3D12_BARRIER_SYNC SyncBefore;
    D3D12_BARRIER_SYNC SyncAfter;
    D3D12_BARRIER_ACCESS AccessBefore;
    D3D12_BARRIER_ACCESS AccessAfter;
    D3D12_BARRIER_LAYOUT LayoutBefore;
    D3D12_BARRIER_LAYOUT LayoutAfter;
    ID3D12Resource *pResource;
    D3D12_BARRIER_SUBRESOURCE_RANGE Subresources;
    D3D12_TEXTURE_BARRIER_FLAGS Flags;
} D3D12_TEXTURE_BARRIER;

typedef struct D3D12_BUFFER_BARRIER
{
    D3D12_BARRIER_SYNC SyncBefore;
    D3D12_BARRIER_SYNC SyncAfter;
    D3D12_BARRIER_ACCESS AccessBefore;
    D3D12_BARRIER_ACCESS AccessAfter;
    ID3D12Resource *pResource;
    UINT64 Offset;
    UINT64 Size;
} D3D12_BUFFER_BARRIER;

typedef struct D3D12_BARRIER_GROUP
{
    D3D12_BARRIER_TYPE Type;
    UINT32 NumBarriers;
    union
    {
        const D3D12_GLOBAL_BARRIER *pGlobalBarriers;
        const D3D12_TEXTURE_BARRIER *pTextureBarriers;
        const D3D12_BUFFER_BARRIER *pBufferBarriers;
    };
} D3D12_BARRIER_GROUP;

[
    uuid(28e2495d-0f64-4ae4-a6ec-129255dc49a8),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12ShaderCacheSession : ID3D12DeviceChild
{
    HRESULT FindValue(
            const void *key,
            UINT key_size,
            void *value,
            UINT *value_size);
    HRESULT StoreValue(
            const void *key,
            UINT key_size,
            const void *value,
            UINT value_size);

    void SetDeleteOnDestroy();
    D3D12_SHADER_CACHE_SESSION_DESC GetDesc();
}

typedef enum D3D12_SHADER_CACHE_KIND_FLAGS
{
    D3D12_SHADER_CACHE_KIND_FLAG_IMPLICIT_D3D_CACHE_FOR_DRIVER = 0x1,
    D3D12_SHADER_CACHE_KIND_FLAG_IMPLICIT_D3D_CONVERSIONS = 0x2,
    D3D12_SHADER_CACHE_KIND_FLAG_IMPLICIT_DRIVER_MANAGED = 0x4,
    D3D12_SHADER_CACHE_KIND_FLAG_APPLICATION_MANAGED = 0x8,
} D3D12_SHADER_CACHE_KIND_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_SHADER_CACHE_KIND_FLAGS);")

typedef enum D3D12_SHADER_CACHE_CONTROL_FLAGS
{
    D3D12_SHADER_CACHE_CONTROL_FLAG_DISABLE = 0x1,
    D3D12_SHADER_CACHE_CONTROL_FLAG_ENABLE = 0x2,
    D3D12_SHADER_CACHE_CONTROL_FLAG_CLEAR = 0x4,
} D3D12_SHADER_CACHE_CONTROL_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_SHADER_CACHE_CONTROL_FLAGS);")

[
    uuid(4c80e962-f032-4f60-bc9e-ebc2cfa1d83c),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device9 : ID3D12Device8
{
    HRESULT CreateShaderCacheSession(
            const D3D12_SHADER_CACHE_SESSION_DESC *desc,
            REFIID riid, void **session);

    HRESULT ShaderCacheControl(
            D3D12_SHADER_CACHE_KIND_FLAGS kinds,
            D3D12_SHADER_CACHE_CONTROL_FLAGS control);

    HRESULT CreateCommandQueue1(
            const D3D12_COMMAND_QUEUE_DESC *desc,
            REFIID creator_id,
            REFIID riid,
            void **command_queue);
}

[
    uuid(517f8718-aa66-49f9-b02b-a7ab89c06031),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device10 : ID3D12Device9
{
    HRESULT CreateCommittedResource3(
            const D3D12_HEAP_PROPERTIES *heap_properties,
            D3D12_HEAP_FLAGS heap_flags,
            const D3D12_RESOURCE_DESC1 *desc,
            D3D12_BARRIER_LAYOUT initial_layout,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            ID3D12ProtectedResourceSession *protected_session,
            UINT32 castable_formats_count,
            DXGI_FORMAT *castable_formats,
            REFIID riid_resource,
            void **resource);

    HRESULT CreatePlacedResource2(
            ID3D12Heap *heap,
            UINT64 heap_offset,
            const D3D12_RESOURCE_DESC1 *desc,
            D3D12_BARRIER_LAYOUT initial_layout,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            UINT32 castable_formats_count,
            DXGI_FORMAT *castable_formats,
            REFIID riid,
            void **resource);

    HRESULT CreateReservedResource2(
            const D3D12_RESOURCE_DESC *desc,
            D3D12_BARRIER_LAYOUT initial_layout,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            ID3D12ProtectedResourceSession *protected_session,
            UINT32 castable_formats_count,
            DXGI_FORMAT *castable_formats,
            REFIID riid,
            void **resource);
}

[
    uuid(bc66d368-**************-fc87dc79e476),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VirtualizationGuestDevice : IUnknown
{
    HRESULT ShareWithHost(
            ID3D12DeviceChild *object,
            HANDLE *handle);

    HRESULT CreateFenceFd(
            ID3D12Fence *fence,
            UINT64 fence_value,
            int *fence_fd);
}

[
    uuid(7071e1f0-e84b-4b33-974f-12fa49de65c5),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Tools : IUnknown
{
    void EnableShaderInstrumentation(BOOL enable);
    BOOL ShaderInstrumentationEnabled();
}

cpp_quote("DEFINE_GUID(CLSID_D3D12Debug,                     0xf2352aeb, 0xdd84, 0x49fe, 0xb9, 0x7b, 0xa9, 0xdc, 0xfd, 0xcc, 0x1b, 0x4f);")
cpp_quote("DEFINE_GUID(CLSID_D3D12Tools,                     0xe38216b1, 0x3c8c, 0x4833, 0xaa, 0x09, 0x0a, 0x06, 0xb6, 0x5d, 0x96, 0xc8);")
cpp_quote("DEFINE_GUID(CLSID_D3D12DeviceRemovedExtendedData, 0x4a75bbc4, 0x9ff4, 0x4ad8, 0x9f, 0x18, 0xab, 0xae, 0x84, 0xdc, 0x5f, 0xf2);")
cpp_quote("DEFINE_GUID(CLSID_D3D12SDKConfiguration,          0x7cda6aca, 0xa03e, 0x49c8, 0x94, 0x58, 0x03, 0x34, 0xd2, 0x0e, 0x07, 0xce);")

[
    uuid(e9eb5314-33aa-42b2-a718-d77f58b1f1c7),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12SDKConfiguration : IUnknown
{
    HRESULT SetSDKVersion(UINT version, const char *path);
}

typedef enum D3D12_AXIS_SHADING_RATE
{
    D3D12_AXIS_SHADING_RATE_1X = 0x0,
    D3D12_AXIS_SHADING_RATE_2X = 0x1,
    D3D12_AXIS_SHADING_RATE_4X = 0x2,
} D3D12_AXIS_SHADING_RATE;

typedef enum D3D12_SHADING_RATE
{
    D3D12_SHADING_RATE_1X1 = 0x0,
    D3D12_SHADING_RATE_1X2 = 0x1,
    D3D12_SHADING_RATE_2X1 = 0x4,
    D3D12_SHADING_RATE_2X2 = 0x5,
    D3D12_SHADING_RATE_2X4 = 0x6,
    D3D12_SHADING_RATE_4X2 = 0x9,
    D3D12_SHADING_RATE_4X4 = 0xa,
} D3D12_SHADING_RATE;

typedef enum D3D12_SHADING_RATE_COMBINER
{
    D3D12_SHADING_RATE_COMBINER_PASSTHROUGH = 0x0,
    D3D12_SHADING_RATE_COMBINER_OVERRIDE = 0x1,
    D3D12_SHADING_RATE_COMBINER_MIN = 0x2,
    D3D12_SHADING_RATE_COMBINER_MAX = 0x3,
    D3D12_SHADING_RATE_COMBINER_SUM = 0x4,
} D3D12_SHADING_RATE_COMBINER;

[
    uuid(*************-474c-87f5-6472eaee44ea),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList5 : ID3D12GraphicsCommandList4
{
    void RSSetShadingRate(
            D3D12_SHADING_RATE base_shading_rate,
            const D3D12_SHADING_RATE_COMBINER *combiners);

    void RSSetShadingRateImage(
            ID3D12Resource *shading_rate_image);
}

const UINT D3D12_SHADING_RATE_X_AXIS_SHIFT = 2;
const UINT D3D12_SHADING_RATE_VALID_MASK = 3;

cpp_quote("#define D3D12_MAKE_COARSE_SHADING_RATE(x, y) ((x) << D3D12_SHADING_RATE_X_AXIS_SHIFT | (y))")
cpp_quote("#define D3D12_GET_COARSE_SHADING_RATE_X_AXIS(val) (((val) >> D3D12_SHADING_RATE_X_AXIS_SHIFT) & D3D12_SHADING_RATE_VALID_MASK)")
cpp_quote("#define D3D12_GET_COARSE_SHADING_RATE_Y_AXIS(val) ((val) & D3D12_SHADING_RATE_VALID_MASK)")

typedef struct D3D12_DISPATCH_MESH_ARGUMENTS
{
    UINT ThreadGroupCountX;
    UINT ThreadGroupCountY;
    UINT ThreadGroupCountZ;
} D3D12_DISPATCH_MESH_ARGUMENTS;

[
    uuid(c3827890-e548-4cfa-96cf-5689a9370f80),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList6 : ID3D12GraphicsCommandList5
{
    void DispatchMesh(
            UINT thread_group_count_x,
            UINT thread_group_count_y,
            UINT thread_group_count_z);
}

[
    uuid(dd171223-8b61-4769-90e3-160ccde4e2c1),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList7 : ID3D12GraphicsCommandList6
{
    void Barrier(
            UINT32 barrier_groups_count,
            const D3D12_BARRIER_GROUP *barrier_groups);
};

cpp_quote("#ifndef D3D12_IGNORE_SDK_LAYERS")
cpp_quote("#include \"d3d12sdklayers.h\"")
cpp_quote("#endif")

typedef HRESULT (__stdcall *PFN_D3D12_CREATE_ROOT_SIGNATURE_DESERIALIZER)(
        const void *data, SIZE_T data_size, REFIID iid, void **deserializer);

[local] HRESULT __stdcall D3D12CreateRootSignatureDeserializer(
        const void *data, SIZE_T data_size, REFIID iid, void **deserializer);

typedef HRESULT (__stdcall *PFN_D3D12_CREATE_VERSIONED_ROOT_SIGNATURE_DESERIALIZER)(
        const void *data, SIZE_T data_size, REFIID iid, void **deserializer);

[local] HRESULT __stdcall D3D12CreateVersionedRootSignatureDeserializer(
        const void *data, SIZE_T data_size, REFIID iid, void **deserializer);

typedef HRESULT (__stdcall *PFN_D3D12_SERIALIZE_ROOT_SIGNATURE)(
        const D3D12_ROOT_SIGNATURE_DESC *root_signature_desc,
        D3D_ROOT_SIGNATURE_VERSION version, ID3DBlob **blob, ID3DBlob **error_blob);

[local] HRESULT __stdcall D3D12SerializeRootSignature(
        const D3D12_ROOT_SIGNATURE_DESC *root_signature_desc,
        D3D_ROOT_SIGNATURE_VERSION version, ID3DBlob **blob, ID3DBlob **error_blob);

typedef HRESULT (__stdcall *PFN_D3D12_SERIALIZE_VERSIONED_ROOT_SIGNATURE)(
        const D3D12_VERSIONED_ROOT_SIGNATURE_DESC *desc, ID3DBlob **blob, ID3DBlob **error_blob);

[local] HRESULT __stdcall D3D12SerializeVersionedRootSignature(
        const D3D12_VERSIONED_ROOT_SIGNATURE_DESC *root_signature_desc,
        ID3DBlob **blob, ID3DBlob **error_blob);

typedef HRESULT (__stdcall *PFN_D3D12_CREATE_DEVICE)(IUnknown *adapter,
        D3D_FEATURE_LEVEL minimum_feature_level, REFIID iid, void **device);

[local] HRESULT __stdcall D3D12CreateDevice(IUnknown *adapter,
        D3D_FEATURE_LEVEL minimum_feature_level, REFIID iid, void **device);

typedef HRESULT (__stdcall *PFN_D3D12_GET_DEBUG_INTERFACE)(REFIID iid, void **debug);

[local] HRESULT __stdcall D3D12GetDebugInterface(REFIID iid, void **debug);

[local] HRESULT __stdcall D3D12EnableExperimentalFeatures(UINT feature_count,
        const IID *iids, void *configurations, UINT *configurations_sizes);

cpp_quote("static const UUID D3D12ExperimentalShaderModels = { 0x76f5573e, 0xf13a, 0x40f5, { 0xb2, 0x97, 0x81, 0xce, 0x9e, 0x18, 0x93, 0x3f } };")
cpp_quote("static const UUID D3D12TiledResourceTier4 = { 0xc9c4725f, 0xa81a, 0x4f56, { 0x8c, 0x5b, 0xc5, 0x10, 0x39, 0xd6, 0x94, 0xfb } };")
cpp_quote("static const UUID D3D12MetaCommand = { 0xc734c97e, 0x8077, 0x48c8, { 0x9f, 0xdc, 0xd9, 0xd1, 0xdd, 0x31, 0xdd, 0x77 } };")

typedef HRESULT (__stdcall *PFN_D3D12_GET_INTERFACE)(REFCLSID clsid, REFIID iid, void **debug);

[local] HRESULT __stdcall D3D12GetInterface(REFCLSID clsid, REFIID iid, void **debug);
