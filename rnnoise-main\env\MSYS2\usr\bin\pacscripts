#!/usr/bin/bash
#
#   pacscripts : tries to print out the {pre,post}_{install,remove,upgrade}
#   scripts of a given package
#
#   Copyright (c) 2009 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
#
#   This program is free software; you can redistribute it and/or modify
#   it under the terms of the GNU General Public License as published by
#   the Free Software Foundation; either version 2 of the License, or
#   (at your option) any later version.
#
#   This program is distributed in the hope that it will be useful,
#   but WITHOUT ANY WARRANTY; without even the implied warranty of
#   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#   GNU General Public License for more details.
#
#   You should have received a copy of the GNU General Public License
#   along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

# bash options
set -o nounset
set -o errexit

declare -r myname='pacscripts'
declare -r myver='1.10.6'

error() {
	local mesg=$1; shift
	# shellcheck disable=SC2059
	printf "==> $(/usr/bin/gettext "ERROR:") ${mesg}\n" "$@" >&2
}

usage() {
	cat <<EOF
${myname} v${myver}

Print the {pre,post}_{install,remove,upgrade} scripts of a given package.

Usage: ${myname} <pkgname | pkgfile>

Options:
  -h, --help     show this help message and exit
  -V, --version  display version information and exit

Example: ${myname} gconf-editor
Example: ${myname} gconf-editor-3.0.1-3-x86_64.pkg.tar.xz
EOF
}


if ! DBPath="$(pacman-conf DBPath)"; then
	error "unable to read /etc/pacman.conf"
	exit 1
fi
pac_db="${DBPath:-/var/lib/pacman}/local"

version() {
	printf "%s %s\n" "$myname" "$myver"
	echo 'Copyright (c) 2009 Giulio "giulivo" Fidente <<EMAIL>>'
	echo 'Copyright (c) 2009 Xavier Chantry <<EMAIL>>'
}

spacman() {
	pacman "$@"
}

print_db() {
	pkg=$(pacman -Q "$1")
	pkg=${pkg/ /-}
	for file in "$pac_db"/"$pkg"*/install; do
		if [ -f "$file" ]; then
			cat "$pac_db/$pkg"*/install
			echo
			return 0
		else
			error "Package $1 does not include any .INSTALL script"
			return 1
		fi
	done
}

print_pkg() {
	if ! /usr/bin/bsdtar -xqOf "$1" .INSTALL 2>/dev/null; then
		error "Package $1 does not include any .INSTALL script"
		return 1
	fi
	echo
}

print_scriptlet() {
	if [ -f "$1" ]; then
		if /usr/bin/bsdtar tf "$1" .PKGINFO &>/dev/null; then
			print_pkg "$1"
			return
		fi
	fi
	if pacman -Q "$1" &>/dev/null; then
		print_db "$1"
		return
	fi
	if ! pacman -Si "$1" &>/dev/null; then
		error "Package $1 not found"
		return 1
	fi
	url=$(pacman -Sddp "$1")
	if [[ $url != file://* ]]; then
		if ! spacman -Sddw --logfile /dev/null --noconfirm "$1" >&2; then
			error "Failed to download $1"
			return 1
		fi
		echo >&2
		url=$(pacman -Sddp "$1")
	fi
	print_pkg "${url#file://}"
	return
}

if [ $# -ne 1 ] ; then
	usage
	exit 1
fi

case "$1" in
	--help|-h) usage; exit 0 ;;
	--version|-V) version; exit 0 ;;
	*) print_scriptlet "$1" ;;
esac
