# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V302
35
36
42
43
48
58
169
170
174
175
8252
8253
8265
8266
8482
8483
8505
8506
8596
8602
8617
8619
8986
8988
9000
9001
9167
9168
9193
9204
9208
9211
9410
9411
9642
9644
9654
9655
9664
9665
9723
9727
9728
9733
9742
9743
9745
9746
9748
9750
9752
9753
9757
9758
9760
9761
9762
9764
9766
9767
9770
9771
9774
9776
9784
9787
9792
9793
9794
9795
9800
9812
9823
9825
9827
9828
9829
9831
9832
9833
9851
9852
9854
9856
9874
9880
9881
9882
9883
9885
9888
9890
9895
9896
9898
9900
9904
9906
9917
9919
9924
9926
9928
9929
9934
9936
9937
9938
9939
9941
9961
9963
9968
9974
9975
9979
9981
9982
9986
9987
9989
9990
9992
9998
9999
10000
10002
10003
10004
10005
10006
10007
10013
10014
10017
10018
10024
10025
10035
10037
10052
10053
10055
10056
10060
10061
10062
10063
10067
10070
10071
10072
10083
10085
10133
10136
10145
10146
10160
10161
10175
10176
10548
10550
11013
11016
11035
11037
11088
11089
11093
11094
12336
12337
12349
12350
12951
12952
12953
12954
126980
126981
127183
127184
127344
127346
127358
127360
127374
127375
127377
127387
127462
127488
127489
127491
127514
127515
127535
127536
127538
127547
127568
127570
127744
127778
127780
127892
127894
127896
127897
127900
127902
127985
127987
127990
127991
128254
128255
128318
128329
128335
128336
128360
128367
128369
128371
128379
128391
128392
128394
128398
128400
128401
128405
128407
128420
128422
128424
128425
128433
128435
128444
128445
128450
128453
128465
128468
128476
128479
128481
128482
128483
128484
128488
128489
128495
128496
128499
128500
128506
128592
128640
128710
128715
128723
128725
128728
128732
128742
128745
128746
128747
128749
128752
128753
128755
128765
128992
129004
129008
129009
129292
129339
129340
129350
129351
129536
129648
129661
129664
129673
129680
129726
129727
129734
129742
129756
129760
129769
129776
129785
END
