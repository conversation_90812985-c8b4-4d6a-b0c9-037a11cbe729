.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_sign_supports_pk_algorithm" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_sign_supports_pk_algorithm \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_sign_supports_pk_algorithm(gnutls_sign_algorithm_t " sign ", gnutls_pk_algorithm_t " pk ");"
.SH ARGUMENTS
.IP "gnutls_sign_algorithm_t sign" 12
is a signature algorithm
.IP "gnutls_pk_algorithm_t pk" 12
is a public key algorithm
.SH "DESCRIPTION"
This function returns non\-zero if the public key algorithm corresponds to
the given signature algorithm. That is, if that signature can be generated
from the given private key algorithm.
.SH "SINCE"
3.6.0
.SH "RETURNS"
return non\-zero when the provided algorithms are compatible.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
