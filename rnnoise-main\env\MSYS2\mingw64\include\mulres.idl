/*
 * Copyright (C) 2020 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#if 0
#pragma makedep install
#endif

[
    object,
    uuid(0c733a90-2a1c-11ce-ade5-00aa0044773d),
    pointer_default(unique)
]
interface IMultipleResults : IUnknown
{
    typedef DB_LRESERVE DBRESULTFLAG;

    enum DBRESULTFLAGENUM
    {
        DBRESULTFLAG_DEFAULT = 0,
        DBRESULTFLAG_ROWSET  = 1,
        DBRESULTFLAG_ROW     = 2,
    };

    [local]
    HRESULT GetResult([in] IUnknown *outer, [in] DBRESULTFLAG result, [in] REFIID riid, [out] DBROWCOUNT *affected,
            [out, iid_is(riid), annotation("_Outptr_opt_result_maybenull_")] IUnknown ** rowset);

    [call_as(GetResult)]
    HRESULT RemoteGetResult([in] IUnknown *outer, [in] DBRESULTFLAG result, [in] REFIID riid,
            [in, out, unique] DBROWCOUNT *affected, [in, out, unique, iid_is(riid)] IUnknown **rowset,
            [out] IErrorInfo **error);
}
