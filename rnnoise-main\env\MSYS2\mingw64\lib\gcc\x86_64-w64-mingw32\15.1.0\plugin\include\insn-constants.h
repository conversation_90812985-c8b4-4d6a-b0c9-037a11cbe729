/* Generated automatically by the program `genconstants'
   from the machine description file `md'.  */

#ifndef GCC_INSN_CONSTANTS_H
#define GCC_INSN_CONSTANTS_H

#define XMM13_REG 49
#define PPERM_SIGN 0xc0
#define R16_REG 76
#define XMM10_REG 46
#define R22_REG 82
#define XMM17_REG 53
#define COM_FALSE_P 3
#define R13_REG 41
#define XMM6_REG 26
#define FPSR_REG 18
#define XMM18_REG 54
#define R10_REG 38
#define XMM3_REG 23
#define ST5_REG 13
#define SI_REG 4
#define MM6_REG 34
#define AX_REG 0
#define XMM0_REG 20
#define DI_REG 5
#define MM3_REG 31
#define MASK7_REG 75
#define ROUND_SAE 8
#define ROUND_NEAREST_INT 0
#define R27_REG 87
#define PPERM_ZERO 0x80
#define MM0_REG 28
#define CX_REG 2
#define MASK4_REG 72
#define ROUND_NO_EXC 0x8
#define XMM27_REG 63
#define R24_REG 84
#define R9_REG 37
#define ABI_DEFAULT 0
#define MASK1_REG 69
#define XMM24_REG 60
#define MM2_REG 30
#define R30_REG 90
#define XMM15_REG 51
#define NO_ROUND 4
#define XMM30_REG 66
#define PPERM_SRC1 0x00
#define PPERM_SRC2 0x10
#define R18_REG 78
#define XMM12_REG 48
#define R15_REG 43
#define PCOM_FALSE 0
#define R21_REG 81
#define XMM5_REG 25
#define R12_REG 40
#define R8_REG 36
#define ST7_REG 15
#define PPERM_REVERSE 0x40
#define BP_REG 6
#define XMM2_REG 22
#define PCOM_TRUE 1
#define ST4_REG 12
#define MM5_REG 33
#define ROUND_TRUNC 0x3
#define XMM7_REG 27
#define XMM21_REG 57
#define PPERM_SRC 0x00
#define R29_REG 89
#define ST1_REG 9
#define MASK6_REG 74
#define XMM8_REG 44
#define XMM29_REG 65
#define ROUND_ROUNDEVEN 0x0
#define R26_REG 86
#define XMM9_REG 45
#define ST2_REG 10
#define MASK3_REG 71
#define XMM26_REG 62
#define ROUND_MXCSR 0x4
#define PPERM_ONES 0xa0
#define ROUND_ZERO 3
#define FIRST_PSEUDO_REG 92
#define ABI_UNKNOWN 2
#define ROUND_FLOOR 0x1
#define COM_TRUE_S 4
#define PPERM_INV_SIGN 0xe0
#define XMM23_REG 59
#define ROUND_NEG_INF 1
#define XMM14_REG 50
#define XMM25_REG 61
#define COM_FALSE_S 2
#define BX_REG 3
#define XMM20_REG 56
#define XMM11_REG 47
#define FRAME_REG 19
#define PPERM_INVERT 0x20
#define R23_REG 83
#define PPERM_REV_INV 0x60
#define R14_REG 42
#define R17_REG 77
#define ROUND_CEIL 0x2
#define ABI_VZEROUPPER 1
#define COM_TRUE_P 5
#define R20_REG 80
#define R11_REG 39
#define XMM4_REG 24
#define ST6_REG 14
#define MM7_REG 35
#define SP_REG 7
#define ARGP_REG 16
#define MASK0_REG 68
#define XMM1_REG 21
#define ST3_REG 11
#define MM4_REG 32
#define R28_REG 88
#define ST0_REG 8
#define MM1_REG 29
#define MASK5_REG 73
#define ROUND_POS_INF 2
#define XMM28_REG 64
#define XMM19_REG 55
#define R25_REG 85
#define MASK2_REG 70
#define R31_REG 91
#define FLAGS_REG 17
#define DX_REG 1
#define XMM16_REG 52
#define XMM31_REG 67
#define R19_REG 79
#define XMM22_REG 58

enum unspec {
  UNSPEC_GOT = 0,
  UNSPEC_GOTOFF = 1,
  UNSPEC_GOTPCREL = 2,
  UNSPEC_GOTTPOFF = 3,
  UNSPEC_TPOFF = 4,
  UNSPEC_NTPOFF = 5,
  UNSPEC_DTPOFF = 6,
  UNSPEC_GOTNTPOFF = 7,
  UNSPEC_INDNTPOFF = 8,
  UNSPEC_PLTOFF = 9,
  UNSPEC_MACHOPIC_OFFSET = 10,
  UNSPEC_PCREL = 11,
  UNSPEC_SIZEOF = 12,
  UNSPEC_STACK_ALLOC = 13,
  UNSPEC_SET_GOT = 14,
  UNSPEC_SET_RIP = 15,
  UNSPEC_SET_GOT_OFFSET = 16,
  UNSPEC_MEMORY_BLOCKAGE = 17,
  UNSPEC_PROBE_STACK = 18,
  UNSPEC_TP = 19,
  UNSPEC_TLS_GD = 20,
  UNSPEC_TLS_LD_BASE = 21,
  UNSPEC_TLSDESC = 22,
  UNSPEC_SCAS = 23,
  UNSPEC_FNSTSW = 24,
  UNSPEC_SAHF = 25,
  UNSPEC_NOTRAP = 26,
  UNSPEC_PARITY = 27,
  UNSPEC_FSTCW = 28,
  UNSPEC_REP = 29,
  UNSPEC_LD_MPIC = 30,
  UNSPEC_TRUNC_NOOP = 31,
  UNSPEC_DIV_ALREADY_SPLIT = 32,
  UNSPEC_PAUSE = 33,
  UNSPEC_LEA_ADDR = 34,
  UNSPEC_XBEGIN_ABORT = 35,
  UNSPEC_STOS = 36,
  UNSPEC_PEEPSIB = 37,
  UNSPEC_INSN_FALSE_DEP = 38,
  UNSPEC_SBB = 39,
  UNSPEC_CC_NE = 40,
  UNSPEC_STC = 41,
  UNSPEC_PUSHFL = 42,
  UNSPEC_POPFL = 43,
  UNSPEC_OPTCOMX = 44,
  UNSPEC_SETCC_SI_SLP = 45,
  UNSPEC_FIX_NOTRUNC = 46,
  UNSPEC_MASKMOV = 47,
  UNSPEC_MOVCC_MASK = 48,
  UNSPEC_MOVMSK = 49,
  UNSPEC_INSERTPS = 50,
  UNSPEC_BLENDV = 51,
  UNSPEC_PSHUFB = 52,
  UNSPEC_XOP_PERMUTE = 53,
  UNSPEC_RCP = 54,
  UNSPEC_RSQRT = 55,
  UNSPEC_PSADBW = 56,
  UNSPEC_US_TRUNCATE = 57,
  UNSPEC_SCALEF = 58,
  UNSPEC_PCMP = 59,
  UNSPEC_CVTBFSF = 60,
  UNSPEC_COMX = 61,
  UNSPEC_IEEE_MIN = 62,
  UNSPEC_IEEE_MAX = 63,
  UNSPEC_SIN = 64,
  UNSPEC_COS = 65,
  UNSPEC_FPATAN = 66,
  UNSPEC_FYL2X = 67,
  UNSPEC_FYL2XP1 = 68,
  UNSPEC_FRNDINT = 69,
  UNSPEC_FIST = 70,
  UNSPEC_F2XM1 = 71,
  UNSPEC_TAN = 72,
  UNSPEC_FXAM = 73,
  UNSPEC_FRNDINT_ROUNDEVEN = 74,
  UNSPEC_FRNDINT_FLOOR = 75,
  UNSPEC_FRNDINT_CEIL = 76,
  UNSPEC_FRNDINT_TRUNC = 77,
  UNSPEC_FIST_FLOOR = 78,
  UNSPEC_FIST_CEIL = 79,
  UNSPEC_SINCOS_COS = 80,
  UNSPEC_SINCOS_SIN = 81,
  UNSPEC_XTRACT_FRACT = 82,
  UNSPEC_XTRACT_EXP = 83,
  UNSPEC_FSCALE_FRACT = 84,
  UNSPEC_FSCALE_EXP = 85,
  UNSPEC_FPREM_F = 86,
  UNSPEC_FPREM_U = 87,
  UNSPEC_FPREM1_F = 88,
  UNSPEC_FPREM1_U = 89,
  UNSPEC_C2_FLAG = 90,
  UNSPEC_FXAM_MEM = 91,
  UNSPEC_SP_SET = 92,
  UNSPEC_SP_TEST = 93,
  UNSPEC_ROUND = 94,
  UNSPEC_CRC32 = 95,
  UNSPEC_LZCNT = 96,
  UNSPEC_TZCNT = 97,
  UNSPEC_BEXTR = 98,
  UNSPEC_PDEP = 99,
  UNSPEC_PEXT = 100,
  UNSPEC_INTERRUPT_RETURN = 101,
  UNSPEC_MOVDIRI = 102,
  UNSPEC_MOVDIR64B = 103,
  UNSPEC_CALLEE_ABI = 104,
  UNSPEC_APXPUSH2 = 105,
  UNSPEC_APXPOP2_LOW = 106,
  UNSPEC_APXPOP2_HIGH = 107,
  UNSPEC_APX_PPX = 108,
  UNSPEC_APX_DFV = 109,
  UNSPEC_3DNOW = 110,
  UNSPEC_MOVNTQ = 111,
  UNSPEC_PFRCP = 112,
  UNSPEC_PFRCPIT1 = 113,
  UNSPEC_PFRCPIT2 = 114,
  UNSPEC_PFRSQRT = 115,
  UNSPEC_PFRSQIT1 = 116,
  UNSPEC_MOVNT = 117,
  UNSPEC_MOVDI_TO_SSE = 118,
  UNSPEC_LDDQU = 119,
  UNSPEC_PSIGN = 120,
  UNSPEC_PALIGNR = 121,
  UNSPEC_EXTRQI = 122,
  UNSPEC_EXTRQ = 123,
  UNSPEC_INSERTQI = 124,
  UNSPEC_INSERTQ = 125,
  UNSPEC_DP = 126,
  UNSPEC_MOVNTDQA = 127,
  UNSPEC_MPSADBW = 128,
  UNSPEC_PHMINPOSUW = 129,
  UNSPEC_PTEST = 130,
  UNSPEC_PCMPESTR = 131,
  UNSPEC_PCMPISTR = 132,
  UNSPEC_FMADDSUB = 133,
  UNSPEC_XOP_UNSIGNED_CMP = 134,
  UNSPEC_XOP_TRUEFALSE = 135,
  UNSPEC_FRCZ = 136,
  UNSPEC_AESENC = 137,
  UNSPEC_AESENCLAST = 138,
  UNSPEC_AESDEC = 139,
  UNSPEC_AESDECLAST = 140,
  UNSPEC_AESIMC = 141,
  UNSPEC_AESKEYGENASSIST = 142,
  UNSPEC_PCLMUL = 143,
  UNSPEC_VPERMIL = 144,
  UNSPEC_VPERMIL2 = 145,
  UNSPEC_VPERMIL2F128 = 146,
  UNSPEC_CAST = 147,
  UNSPEC_VTESTP = 148,
  UNSPEC_VCVTPH2PS = 149,
  UNSPEC_VCVTPS2PH = 150,
  UNSPEC_VPERMVAR = 151,
  UNSPEC_VPERMTI = 152,
  UNSPEC_GATHER = 153,
  UNSPEC_VSIBADDR = 154,
  UNSPEC_VPERMT2 = 155,
  UNSPEC_UNSIGNED_FIX_NOTRUNC = 156,
  UNSPEC_UNSIGNED_PCMP = 157,
  UNSPEC_TESTM = 158,
  UNSPEC_TESTNM = 159,
  UNSPEC_SCATTER = 160,
  UNSPEC_RCP14 = 161,
  UNSPEC_RSQRT14 = 162,
  UNSPEC_FIXUPIMM = 163,
  UNSPEC_VTERNLOG = 164,
  UNSPEC_GETEXP = 165,
  UNSPEC_GETMANT = 166,
  UNSPEC_ALIGN = 167,
  UNSPEC_CONFLICT = 168,
  UNSPEC_COMPRESS = 169,
  UNSPEC_COMPRESS_STORE = 170,
  UNSPEC_EXPAND = 171,
  UNSPEC_VCVTT = 172,
  UNSPEC_VCVTTU = 173,
  UNSPEC_MASKOP = 174,
  UNSPEC_KORTEST = 175,
  UNSPEC_KTEST = 176,
  UNSPEC_MASKLOAD = 177,
  UNSPEC_EMBEDDED_ROUNDING = 178,
  UNSPEC_SHA1MSG1 = 179,
  UNSPEC_SHA1MSG2 = 180,
  UNSPEC_SHA1NEXTE = 181,
  UNSPEC_SHA1RNDS4 = 182,
  UNSPEC_SHA256MSG1 = 183,
  UNSPEC_SHA256MSG2 = 184,
  UNSPEC_SHA256RNDS2 = 185,
  UNSPEC_DBPSADBW = 186,
  UNSPEC_PMADDUBSW512 = 187,
  UNSPEC_PMADDWD512 = 188,
  UNSPEC_PSHUFHW = 189,
  UNSPEC_PSHUFLW = 190,
  UNSPEC_CVTINT2MASK = 191,
  UNSPEC_REDUCE = 192,
  UNSPEC_FPCLASS = 193,
  UNSPEC_RANGE = 194,
  UNSPEC_VPMADD52LUQ = 195,
  UNSPEC_VPMADD52HUQ = 196,
  UNSPEC_VPMULTISHIFT = 197,
  UNSPEC_GF2P8AFFINEINV = 198,
  UNSPEC_GF2P8AFFINE = 199,
  UNSPEC_GF2P8MUL = 200,
  UNSPEC_VPSHLD = 201,
  UNSPEC_VPSHRD = 202,
  UNSPEC_VPSHRDV = 203,
  UNSPEC_VPSHLDV = 204,
  UNSPEC_VPDPBUSD = 205,
  UNSPEC_VPDPBUSDS = 206,
  UNSPEC_VPDPWSSD = 207,
  UNSPEC_VPDPWSSDS = 208,
  UNSPEC_VAESDEC = 209,
  UNSPEC_VAESDECLAST = 210,
  UNSPEC_VAESENC = 211,
  UNSPEC_VAESENCLAST = 212,
  UNSPEC_VPCLMULQDQ = 213,
  UNSPEC_VPSHUFBIT = 214,
  UNSPEC_VP2INTERSECT = 215,
  UNSPEC_VDPBF16PS = 216,
  UNSPEC_COMPLEX_FMA = 217,
  UNSPEC_COMPLEX_FMA_PAIR = 218,
  UNSPEC_COMPLEX_FCMA = 219,
  UNSPEC_COMPLEX_FCMA_PAIR = 220,
  UNSPEC_COMPLEX_FMUL = 221,
  UNSPEC_COMPLEX_FCMUL = 222,
  UNSPEC_COMPLEX_MASK = 223,
  UNSPEC_SM3MSG1 = 224,
  UNSPEC_SM3MSG2 = 225,
  UNSPEC_SM3RNDS2 = 226,
  UNSPEC_VPDPBSSD = 227,
  UNSPEC_VPDPBSSDS = 228,
  UNSPEC_VPDPBSUD = 229,
  UNSPEC_VPDPBSUDS = 230,
  UNSPEC_VPDPBUUD = 231,
  UNSPEC_VPDPBUUDS = 232,
  UNSPEC_VPDPWUSD = 233,
  UNSPEC_VPDPWUSDS = 234,
  UNSPEC_VPDPWSUD = 235,
  UNSPEC_VPDPWSUDS = 236,
  UNSPEC_VPDPWUUD = 237,
  UNSPEC_VPDPWUUDS = 238,
  UNSPEC_SHA512MSG1 = 239,
  UNSPEC_SHA512MSG2 = 240,
  UNSPEC_SHA512RNDS2 = 241,
  UNSPEC_SM4KEY4 = 242,
  UNSPEC_SM4RNDS4 = 243,
  UNSPEC_VDPPHPS = 244,
  UNSPEC_VCVTBIASPH2BF8 = 245,
  UNSPEC_VCVTBIASPH2BF8S = 246,
  UNSPEC_VCVTBIASPH2HF8 = 247,
  UNSPEC_VCVTBIASPH2HF8S = 248,
  UNSPEC_VCVT2PH2BF8 = 249,
  UNSPEC_VCVT2PH2BF8S = 250,
  UNSPEC_VCVT2PH2HF8 = 251,
  UNSPEC_VCVT2PH2HF8S = 252,
  UNSPEC_VCVTPH2BF8 = 253,
  UNSPEC_VCVTPH2BF8S = 254,
  UNSPEC_VCVTPH2HF8 = 255,
  UNSPEC_VCVTPH2HF8S = 256,
  UNSPEC_VCVTHF82PH = 257,
  UNSPEC_VSCALEFBF16 = 258,
  UNSPEC_VRNDSCALEBF16 = 259,
  UNSPEC_VREDUCEBF16 = 260,
  UNSPEC_VGETMANTBF16 = 261,
  UNSPEC_VFPCLASSBF16 = 262,
  UNSPEC_VCVTBF162IBS = 263,
  UNSPEC_VCVTBF162IUBS = 264,
  UNSPEC_VCVTPH2IBS = 265,
  UNSPEC_VCVTPH2IUBS = 266,
  UNSPEC_VCVTPS2IBS = 267,
  UNSPEC_VCVTPS2IUBS = 268,
  UNSPEC_VCVTTBF162IBS = 269,
  UNSPEC_VCVTTBF162IUBS = 270,
  UNSPEC_VCVTTPH2IBS = 271,
  UNSPEC_VCVTTPH2IUBS = 272,
  UNSPEC_VCVTTPS2IBS = 273,
  UNSPEC_VCVTTPS2IUBS = 274,
  UNSPEC_SFIX_SATURATION = 275,
  UNSPEC_UFIX_SATURATION = 276,
  UNSPEC_MINMAXBF16 = 277,
  UNSPEC_MINMAX = 278,
  UNSPEC_VMOVRS = 279,
  UNSPEC_LFENCE = 280,
  UNSPEC_SFENCE = 281,
  UNSPEC_MFENCE = 282,
  UNSPEC_FILD_ATOMIC = 283,
  UNSPEC_FIST_ATOMIC = 284,
  UNSPEC_LDX_ATOMIC = 285,
  UNSPEC_STX_ATOMIC = 286,
  UNSPEC_LDA = 287,
  UNSPEC_STA = 288
};
#define NUM_UNSPEC_VALUES 289
extern const char *const unspec_strings[];

enum unspecv {
  UNSPECV_UD2 = 0,
  UNSPECV_BLOCKAGE = 1,
  UNSPECV_STACK_PROBE = 2,
  UNSPECV_PROBE_STACK_RANGE = 3,
  UNSPECV_ALIGN = 4,
  UNSPECV_PROLOGUE_USE = 5,
  UNSPECV_SPLIT_STACK_RETURN = 6,
  UNSPECV_CLD = 7,
  UNSPECV_NOPS = 8,
  UNSPECV_RDTSC = 9,
  UNSPECV_RDTSCP = 10,
  UNSPECV_RDPMC = 11,
  UNSPECV_LLWP_INTRINSIC = 12,
  UNSPECV_SLWP_INTRINSIC = 13,
  UNSPECV_LWPVAL_INTRINSIC = 14,
  UNSPECV_LWPINS_INTRINSIC = 15,
  UNSPECV_RDFSBASE = 16,
  UNSPECV_RDGSBASE = 17,
  UNSPECV_WRFSBASE = 18,
  UNSPECV_WRGSBASE = 19,
  UNSPECV_FXSAVE = 20,
  UNSPECV_FXRSTOR = 21,
  UNSPECV_FXSAVE64 = 22,
  UNSPECV_FXRSTOR64 = 23,
  UNSPECV_XSAVE = 24,
  UNSPECV_XRSTOR = 25,
  UNSPECV_XSAVE64 = 26,
  UNSPECV_XRSTOR64 = 27,
  UNSPECV_XSAVEOPT = 28,
  UNSPECV_XSAVEOPT64 = 29,
  UNSPECV_XSAVES = 30,
  UNSPECV_XRSTORS = 31,
  UNSPECV_XSAVES64 = 32,
  UNSPECV_XRSTORS64 = 33,
  UNSPECV_XSAVEC = 34,
  UNSPECV_XSAVEC64 = 35,
  UNSPECV_XGETBV = 36,
  UNSPECV_XSETBV = 37,
  UNSPECV_WBINVD = 38,
  UNSPECV_WBNOINVD = 39,
  UNSPECV_FNSTENV = 40,
  UNSPECV_FLDENV = 41,
  UNSPECV_FNSTSW = 42,
  UNSPECV_FNCLEX = 43,
  UNSPECV_RDRAND = 44,
  UNSPECV_RDSEED = 45,
  UNSPECV_XBEGIN = 46,
  UNSPECV_XEND = 47,
  UNSPECV_XABORT = 48,
  UNSPECV_XTEST = 49,
  UNSPECV_NLGR = 50,
  UNSPECV_CLWB = 51,
  UNSPECV_CLFLUSHOPT = 52,
  UNSPECV_MONITORX = 53,
  UNSPECV_MWAITX = 54,
  UNSPECV_CLZERO = 55,
  UNSPECV_PKU = 56,
  UNSPECV_RDPID = 57,
  UNSPECV_NOP_ENDBR = 58,
  UNSPECV_NOP_RDSSP = 59,
  UNSPECV_INCSSP = 60,
  UNSPECV_SAVEPREVSSP = 61,
  UNSPECV_RSTORSSP = 62,
  UNSPECV_WRSS = 63,
  UNSPECV_WRUSS = 64,
  UNSPECV_SETSSBSY = 65,
  UNSPECV_CLRSSBSY = 66,
  UNSPECV_XSUSLDTRK = 67,
  UNSPECV_XRESLDTRK = 68,
  UNSPECV_UMWAIT = 69,
  UNSPECV_UMONITOR = 70,
  UNSPECV_TPAUSE = 71,
  UNSPECV_CLUI = 72,
  UNSPECV_STUI = 73,
  UNSPECV_TESTUI = 74,
  UNSPECV_SENDUIPI = 75,
  UNSPECV_CLDEMOTE = 76,
  UNSPECV_SPECULATION_BARRIER = 77,
  UNSPECV_PTWRITE = 78,
  UNSPECV_ENQCMD = 79,
  UNSPECV_ENQCMDS = 80,
  UNSPECV_SERIALIZE = 81,
  UNSPECV_PATCHABLE_AREA = 82,
  UNSPECV_HRESET = 83,
  UNSPECV_PREFETCHI = 84,
  UNSPECV_URDMSR = 85,
  UNSPECV_UWRMSR = 86,
  UNSPECV_LDTILECFG = 87,
  UNSPECV_STTILECFG = 88,
  UNSPECV_MOVRS = 89,
  UNSPECV_EMMS = 90,
  UNSPECV_FEMMS = 91,
  UNSPECV_LDMXCSR = 92,
  UNSPECV_STMXCSR = 93,
  UNSPECV_CLFLUSH = 94,
  UNSPECV_MONITOR = 95,
  UNSPECV_MWAIT = 96,
  UNSPECV_VZEROALL = 97,
  UNSPECV_LOADIWKEY = 98,
  UNSPECV_AESDEC128KLU8 = 99,
  UNSPECV_AESENC128KLU8 = 100,
  UNSPECV_AESDEC256KLU8 = 101,
  UNSPECV_AESENC256KLU8 = 102,
  UNSPECV_AESDECWIDE128KLU8 = 103,
  UNSPECV_AESENCWIDE128KLU8 = 104,
  UNSPECV_AESDECWIDE256KLU8 = 105,
  UNSPECV_AESENCWIDE256KLU8 = 106,
  UNSPECV_ENCODEKEY128U32 = 107,
  UNSPECV_ENCODEKEY256U32 = 108,
  UNSPECV_CMPXCHG = 109,
  UNSPECV_XCHG = 110,
  UNSPECV_LOCK = 111,
  UNSPECV_CMPCCXADD = 112,
  UNSPECV_RAOINT = 113
};
#define NUM_UNSPECV_VALUES 114
extern const char *const unspecv_strings[];

#endif /* GCC_INSN_CONSTANTS_H */
