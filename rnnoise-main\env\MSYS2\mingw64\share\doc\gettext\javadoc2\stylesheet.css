/* Javadoc style sheet */

/* Define colors, fonts and other style attributes here to override the defaults  */

/* Page background color */
body { background-color: #FFFFFF }

/* Table colors */
.TableHeadingColor     { background: #CCCCFF } /* Dark mauve */
.TableSubHeadingColor  { background: #EEEEFF } /* Light mauve */
.TableRowColor         { background: #FFFFFF } /* White */

/* Font used in left-hand frame lists */
.FrameTitleFont   { font-size: normal; font-family: normal }
.FrameHeadingFont { font-size: normal; font-family: normal }
.FrameItemFont    { font-size: normal; font-family: normal }

/* Example of smaller, sans-serif font in frames */
/* .FrameItemFont  { font-size: 10pt; font-family: Helvetica, Arial, sans-serif } */

/* Navigation bar fonts and colors */
.NavBarCell1    { background-color:#EEEEFF;}/* Light mauve */
.NavBarCell1Rev { background-color:#00008B;}/* Dark Blue */
.NavBarFont1    { font-family: Arial, Helvetica, sans-serif; color:#000000;}
.NavBarFont1Rev { font-family: Arial, Helvetica, sans-serif; color:#FFFFFF;}

.NavBarCell2    { font-family: Arial, Helvetica, sans-serif; background-color:#FFFFFF;}
.NavBarCell3    { font-family: Arial, Helvetica, sans-serif; background-color:#FFFFFF;}

