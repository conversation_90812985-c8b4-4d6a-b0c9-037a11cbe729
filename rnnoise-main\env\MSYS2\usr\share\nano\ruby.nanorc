## Syntax highlighting for Ruby.

## Original author:  <PERSON>

syntax ruby "\.rb$"
header "^#!.*ruby"
magic "Ruby script"
comment "#"

linter ruby -w -c

# Constants.
color brightblue "\<[[:upper:]]+[[:alnum:]_]*|(\$|@|@@)[[:alnum:]_]+"
# Reserved words.
color yellow "\<(BEGIN|END|alias|and|begin|break|case|class|def|defined\?|do|else|elsif|end|ensure|false|for|if|in|module)\>"
color yellow "\<(next|nil|not|or|redo|rescue|retry|return|self|super|then|true|undef|unless|until|when|while|yield)\>"
# Ruby "symbols".
color magenta "([ 	]|^):[[:alnum:]_]+\>"
# Some unique things we want to stand out.
color brightyellow "\<(__FILE__|__LINE__)\>"

# Regular expressions.
color brightmagenta "(/([^/]|\\/)*/|%r\{([^}]|\\\})*\})[iomx]*"
# Shell command expansion is in `backticks` or like %x{this}.
color brightblue "`[^`]*`|%x\{[^}]*\}"

# Strings, double-quoted.
color green ""([^"]|\\")*"|%[QW]?(\{[^}]*\}|\([^)]*\)|<[^>]*>|\[[^]]*\]|\$[^$]*\$|\^[^^]*\^|![^!]*!)"
# Expression substitution for inside double-quoted strings, "like #{this}".
color brightgreen "#\{[^}]*\}"
# Strings, single-quoted.
color green "'([^']|\\')*'|%[qw](\{[^}]*\}|\([^)]*\)|<[^>]*>|\[[^]]*\]|\$[^$]*\$|\^[^^]*\^|![^!]*!)"

# Comments.
color cyan "#([^{#].*|$)"
color brightcyan "##([^{].*|$)"
color cyan start="^=begin\>" end="^=end\>"

# Some common markers.
color brightcyan "XXX|TODO|FIXME|\?\?\?"
