.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srtp_set_profile" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srtp_set_profile \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srtp_set_profile(gnutls_session_t " session ", gnutls_srtp_profile_t " profile ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_srtp_profile_t profile" 12
is the profile id to add.
.SH "DESCRIPTION"
This function is to be used by both clients and servers, to declare
what SRTP profiles they support, to negotiate with the peer.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.

Since 3.1.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
