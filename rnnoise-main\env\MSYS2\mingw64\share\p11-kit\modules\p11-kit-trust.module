# See pkcs11.conf(5) to understand this file

# This is a module config for the 'included' p11-kit trust module
module: p11-kit-trust.dll

# This setting affects the order that trust policy and other information
# is looked up when going across various modules. Other trust policy modules
# need to specify the priority where they slot into things.
priority: 1

# Mark this module as a viable source of trust policy information
trust-policy: yes

# This is for drop-in compatibility with glib-networking and gcr. Those
# projects used this non-standard attribute to denote slots to use to
# retrieve trust information.
x-trust-lookup: pkcs11:library-description=PKCS%2311%20Kit%20Trust%20Module

# Prevent this module being loaded by the proxy module
disable-in: p11-kit-proxy

# This will be overwritten by appending "verbose=yes", if the trust
# command is called with the -v option.
x-init-reserved:
