<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-qlog</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#USAGE">USAGE</a></li>
  <li><a href="#SUPPORTED-EVENT-TYPES">SUPPORTED EVENT TYPES</a></li>
  <li><a href="#FILTERS">FILTERS</a>
    <ul>
      <li><a href="#Examples">Examples</a></li>
      <li><a href="#Filter-Syntax-Specification">Filter Syntax Specification</a></li>
      <li><a href="#Default-Configuration">Default Configuration</a></li>
    </ul>
  </li>
  <li><a href="#FORMAT-STABILITY">FORMAT STABILITY</a>
    <ul>
      <li><a href="#Aims">Aims</a></li>
    </ul>
  </li>
  <li><a href="#LIMITATIONS">LIMITATIONS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-qlog - OpenSSL qlog tracing functionality</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>OpenSSL has unstable support for generating logs in the qlog logging format, which can be used to obtain diagnostic data for QUIC connections. The data generated includes information on packets sent and received and the frames contained within them, as well as loss detection and other events.</p>

<p>The qlog output generated by OpenSSL can be used to obtain diagnostic visualisations of a given QUIC connection using tools such as <b>qvis</b>.</p>

<p><b>WARNING:</b> The output of OpenSSL&#39;s qlog functionality uses an unstable format based on a draft specification. qlog output is not subject to any format stability or compatibility guarantees at this time, and <b>will</b> change in incompatible ways in future versions of OpenSSL. See <b>FORMAT STABILITY</b> below for details.</p>

<h1 id="USAGE">USAGE</h1>

<p>When OpenSSL is built with qlog support, qlog is enabled at run time by setting the standard <b>QLOGDIR</b> environment variable to point to a directory where qlog files should be written. Once set, any QUIC connection established by OpenSSL will have a qlog file written automatically to the specified directory.</p>

<p>Log files are generated in the <i>.sqlog</i> format based on JSON-SEQ (RFC 7464).</p>

<p>The filenames of generated log files under the specified <b>QLOGDIR</b> use the following structure:</p>

<pre><code>{connection_odcid}_{vantage_point_type}.sqlog</code></pre>

<p>where <b>{connection_odcid}</b> is the lowercase hexadecimal encoding of a QUIC connection&#39;s Original Destination Connection ID, which is the Destination Connection ID used in the header of the first Initial packet sent as part of the connection process, and <b>{vantage_point_type}</b> is either <code>client</code> or <code>server</code>, reflecting the perspective of the endpoint producing the qlog output.</p>

<p>The qlog functionality can be disabled at OpenSSL build time using the <i>no-unstable-qlog</i> configure flag.</p>

<h1 id="SUPPORTED-EVENT-TYPES">SUPPORTED EVENT TYPES</h1>

<p>The following event types are currently supported:</p>

<dl>

<dt id="connectivity:connection_started"><b>connectivity:connection_started</b></dt>
<dd>

</dd>
<dt id="connectivity:connection_state_updated"><b>connectivity:connection_state_updated</b></dt>
<dd>

</dd>
<dt id="connectivity:connection_closed"><b>connectivity:connection_closed</b></dt>
<dd>

</dd>
<dt id="transport:parameters_set"><b>transport:parameters_set</b></dt>
<dd>

</dd>
<dt id="transport:packet_sent"><b>transport:packet_sent</b></dt>
<dd>

</dd>
<dt id="transport:packet_received"><b>transport:packet_received</b></dt>
<dd>

</dd>
<dt id="recovery:packet_lost"><b>recovery:packet_lost</b></dt>
<dd>

</dd>
</dl>

<h1 id="FILTERS">FILTERS</h1>

<p>By default, all supported event types are logged. The <b>OSSL_QFILTER</b> environment variable can be used to configure a filter specification which determines which event types are to be logged. Each event type can be turned on and off individually. The filter specification is a space-separated list of terms listing event types to enable or disable. The terms are applied in order, thus the effects of later terms override the effects of earlier terms.</p>

<h2 id="Examples">Examples</h2>

<p>Here are some example filter specifications:</p>

<dl>

<dt id="or"><code>*</code> (or <code>+*</code>)</dt>
<dd>

<p>Enable all supported qlog event types.</p>

</dd>
<dt id="pod"><code>-*</code></dt>
<dd>

<p>Disable all qlog event types.</p>

</dd>
<dt id="transport:packet_received1"><code>* -transport:packet_received</code></dt>
<dd>

<p>Enable all qlog event types, but disable the <b>transport:packet_received</b> event type.</p>

</dd>
<dt id="transport:packet_sent1"><code>-* transport:packet_sent</code></dt>
<dd>

<p>Disable all qlog event types, except for the <b>transport:packet_sent</b> event type.</p>

</dd>
<dt id="connectivity:-transport:parameters_set"><code>-* connectivity:* transport:parameters_set</code></dt>
<dd>

<p>Disable all qlog event types, except for <b>transport:parameters_set</b> and all supported event types in the <b>connectivity</b> category.</p>

</dd>
</dl>

<h2 id="Filter-Syntax-Specification">Filter Syntax Specification</h2>

<p>Formally, the format of the filter specification in ABNF is as follows:</p>

<pre><code>filter              = *filter-term

filter-term         = add-sub-term

add-sub-term        = [&quot;-&quot; / &quot;+&quot;] specifier

specifier           = global-specifier / qualified-specifier

global-specifier    = wildcard

qualified-specifier = component-specifier &quot;:&quot; component-specifier

component-specifier = name / wildcard

wildcard            = &quot;*&quot;

name                = 1*(ALPHA / DIGIT / &quot;_&quot; / &quot;-&quot;)</code></pre>

<p>Filter terms are interpreted as follows:</p>

<dl>

<dt id="or1"><code>+*</code> (or <code>*</code>)</dt>
<dd>

<p>Enables all event types.</p>

</dd>
<dt id="pod1"><code>-*</code></dt>
<dd>

<p>Disables all event types.</p>

</dd>
<dt id="foo:-or-foo"><code>+foo:*</code> (or <code>foo:*</code>)</dt>
<dd>

<p>Enables all event types in the <b>foo</b> category.</p>

</dd>
<dt id="foo"><code>-foo:*</code></dt>
<dd>

<p>Disables all event types in the <b>foo</b> category.</p>

</dd>
<dt id="foo:bar-or-foo:bar"><code>+foo:bar</code> (or <code>foo:bar</code>)</dt>
<dd>

<p>Enables a specific event type <b>foo:bar</b>.</p>

</dd>
<dt id="foo:bar"><code>-foo:bar</code></dt>
<dd>

<p>Disables a specific event type <b>foo:bar</b>.</p>

</dd>
</dl>

<p>Partial wildcard matches are not supported at this time.</p>

<h2 id="Default-Configuration">Default Configuration</h2>

<p>If the <b>OSSL_QFILTER</b> environment variable is not set or set to the empty string, this is equivalent to enabling all event types (i.e., it is equivalent to a filter of <code>*</code>). Note that the <b>QLOGDIR</b> environment variable must also be set to enable qlog.</p>

<h1 id="FORMAT-STABILITY">FORMAT STABILITY</h1>

<p>The OpenSSL qlog functionality currently implements a draft version of the qlog specification. Future revisions to the qlog specification in advance of formal standardisation are expected to introduce incompatible and breaking changes to the qlog format. The OpenSSL qlog functionality will transition to producing output in this format in the future once standardisation is complete.</p>

<p>Because of this, the qlog output of OpenSSL <b>will</b> change in incompatible and breaking ways in the future, including in non-major releases of OpenSSL. The qlog output of OpenSSL is considered unstable and not subject to any format stability or compatibility guarantees at this time.</p>

<p>Users of the OpenSSL qlog functionality must be aware that the output may change arbitrarily between releases and that the preservation of compatibility with any given tool between releases is not guaranteed.</p>

<h2 id="Aims">Aims</h2>

<p>The OpenSSL draft qlog functionality is primarily intended for use in conjunction with the qvis tool <a href="https://qvis.quictools.info/">https://qvis.quictools.info/</a>. In terms of format compatibility, the output format of the OpenSSL qlog functionality is expected to track what is supported by qvis. As such, future changes to the output of the OpenSSL qlog functionality are expected to track changes in qvis as they occur, and reflect the versions of qlog currently supported by qvis.</p>

<p>This means that prior to the finalisation of the qlog standard, in the event of a disparity between the current draft and what qvis supports, the OpenSSL qlog functionality will generally aim for qvis compatibility over compliance with the latest draft.</p>

<p>As such, OpenSSL&#39;s qlog functionality currently implements qlog version 0.3 as defined in <b>draft-ietf-quic-qlog-main-schema-05</b> and <b>draft-ietf-quic-qlog-quic-events-04</b>. These revisions are intentionally used instead of more recent revisions due to their qvis compatibility.</p>

<h1 id="LIMITATIONS">LIMITATIONS</h1>

<p>The OpenSSL implementation of qlog currently has the following limitations:</p>

<ul>

<li><p>Not all event types defined by the draft specification are implemented.</p>

</li>
<li><p>Only the JSON-SEQ (<b>.sqlog</b>) output format is supported.</p>

</li>
<li><p>Only the <b>QLOGDIR</b> environment variable is supported for configuring the qlog output directory. The standard <b>QLOGFILE</b> environment variable is not supported.</p>

</li>
<li><p>There is no API for programmatically enabling or controlling the qlog functionality.</p>

</li>
</ul>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/openssl-quic.html">openssl-quic(7)</a>, <a href="../man7/openssl-env.html">openssl-env(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.3.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


