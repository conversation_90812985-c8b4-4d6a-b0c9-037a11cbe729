.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_tdb_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_tdb_init \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_tdb_init(gnutls_tdb_t * " tdb ");"
.SH ARGUMENTS
.IP "gnutls_tdb_t * tdb" 12
A pointer to the type to be initialized
.SH "DESCRIPTION"
This function will initialize a public key trust storage structure.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
