.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_prf_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_prf_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_prf_raw(gnutls_session_t " session ", size_t " label_size ", const char * " label ", size_t " seed_size ", const char * " seed ", size_t " outsize ", char * " out ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "size_t label_size" 12
length of the  \fIlabel\fP variable.
.IP "const char * label" 12
label used in PRF computation, typically a short string.
.IP "size_t seed_size" 12
length of the  \fIseed\fP variable.
.IP "const char * seed" 12
optional extra data to seed the PRF with.
.IP "size_t outsize" 12
size of pre\-allocated output buffer to hold the output.
.IP "char * out" 12
pre\-allocated buffer to hold the generated data.
.SH "DESCRIPTION"
Apply the TLS Pseudo\-Random\-Function (PRF) on the master secret
and the provided data.

The  \fIlabel\fP variable usually contains a string denoting the purpose
for the generated data.  The  \fIseed\fP usually contains data such as the
client and server random, perhaps together with some additional
data that is added to guarantee uniqueness of the output for a
particular purpose.

Because the output is not guaranteed to be unique for a particular
session unless  \fIseed\fP includes the client random and server random
fields (the PRF would output the same data on another connection
resumed from the first one), it is not recommended to use this
function directly.  The \fBgnutls_prf()\fP function seeds the PRF with the
client and server random fields directly, and is recommended if you
want to generate pseudo random data unique for each session.
.SH "NOTE"
This function will only operate under TLS versions prior to 1.3.
In TLS1.3 the use of PRF is replaced with HKDF and the generic
exporters like \fBgnutls_prf_rfc5705()\fP should be used instead. Under
TLS1.3 this function returns \fBGNUTLS_E_INVALID_REQUEST\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
