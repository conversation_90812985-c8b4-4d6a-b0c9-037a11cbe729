.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_aead_cipher_encrypt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_aead_cipher_encrypt \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_aead_cipher_encrypt(gnutls_aead_cipher_hd_t " handle ", const void * " nonce ", size_t " nonce_len ", const void * " auth ", size_t " auth_len ", size_t " tag_size ", const void * " ptext ", size_t " ptext_len ", void * " ctext ", size_t * " ctext_len ");"
.SH ARGUMENTS
.IP "gnutls_aead_cipher_hd_t handle" 12
is a \fBgnutls_aead_cipher_hd_t\fP type.
.IP "const void * nonce" 12
the nonce to set
.IP "size_t nonce_len" 12
The length of the nonce
.IP "const void * auth" 12
additional data to be authenticated
.IP "size_t auth_len" 12
The length of the data
.IP "size_t tag_size" 12
The size of the tag to use (use zero for the default)
.IP "const void * ptext" 12
the data to encrypt
.IP "size_t ptext_len" 12
The length of data to encrypt
.IP "void * ctext" 12
the encrypted data including authentication tag
.IP "size_t * ctext_len" 12
the length of encrypted data (initially must hold the maximum available size, including space for tag)
.SH "DESCRIPTION"
This function will encrypt the given data using the algorithm
specified by the context. The output data will contain the
authentication tag.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
