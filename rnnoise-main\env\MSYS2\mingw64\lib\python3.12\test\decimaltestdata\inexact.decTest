------------------------------------------------------------------------
-- inexact.decTest -- decimal inexact and rounded edge cases          --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 999
minexponent: -999

inx001 add 1          1              -> 2
inx002 add 123456789  0              -> 123456789
inx003 add 123456789  0.0            -> 123456789 Rounded
inx004 add 123456789  0.00           -> 123456789 Rounded
inx005 add 123456789  1              -> 123456790
inx006 add 123456789  0.1            -> 123456789 Inexact Rounded
inx007 add 123456789  0.01           -> 123456789 Inexact Rounded
inx008 add 123456789  0.001          -> 123456789 Inexact Rounded
inx009 add 123456789  0.000001       -> 123456789 Inexact Rounded
inx010 add 123456789  0.000000001    -> 123456789 Inexact Rounded
inx011 add 123456789  0.000000000001 -> 123456789 Inexact Rounded

inx012 add 123456789  0.9            -> 123456790 Inexact Rounded
inx013 add 123456789  0.09           -> 123456789 Inexact Rounded
inx014 add 123456789  0.009          -> 123456789 Inexact Rounded
inx015 add 123456789  0.000009       -> 123456789 Inexact Rounded
inx016 add 123456789  0.000000009    -> 123456789 Inexact Rounded
inx017 add 123456789  0.000000000009 -> 123456789 Inexact Rounded

inx021 add 1          -1              -> 0
inx022 add 123456789  -0              -> 123456789
inx023 add 123456789  -0.0            -> 123456789 Rounded
inx024 add 123456789  -0.00           -> 123456789 Rounded
inx025 add 123456789  -1              -> 123456788
inx026 add 123456789  -0.1            -> 123456789 Inexact Rounded
inx027 add 123456789  -0.01           -> 123456789 Inexact Rounded
inx028 add 123456789  -0.001          -> 123456789 Inexact Rounded
inx029 add 123456789  -0.000001       -> 123456789 Inexact Rounded
inx030 add 123456789  -0.000000001    -> 123456789 Inexact Rounded
inx031 add 123456789  -0.000000000001 -> 123456789 Inexact Rounded
inx032 add 123456789  -0.9            -> 123456788 Inexact Rounded
inx033 add 123456789  -0.09           -> 123456789 Inexact Rounded
inx034 add 123456789  -0.009          -> 123456789 Inexact Rounded
inx035 add 123456789  -0.000009       -> 123456789 Inexact Rounded
inx036 add 123456789  -0.000000009    -> 123456789 Inexact Rounded
inx037 add 123456789  -0.000000000009 -> 123456789 Inexact Rounded

inx042 add  0               123456789 -> 123456789
inx043 add  0.0             123456789 -> 123456789 Rounded
inx044 add  0.00            123456789 -> 123456789 Rounded
inx045 add  1               123456789 -> 123456790
inx046 add  0.1             123456789 -> 123456789 Inexact Rounded
inx047 add  0.01            123456789 -> 123456789 Inexact Rounded
inx048 add  0.001           123456789 -> 123456789 Inexact Rounded
inx049 add  0.000001        123456789 -> 123456789 Inexact Rounded
inx050 add  0.000000001     123456789 -> 123456789 Inexact Rounded
inx051 add  0.000000000001  123456789 -> 123456789 Inexact Rounded
inx052 add  0.9             123456789 -> 123456790 Inexact Rounded
inx053 add  0.09            123456789 -> 123456789 Inexact Rounded
inx054 add  0.009           123456789 -> 123456789 Inexact Rounded
inx055 add  0.000009        123456789 -> 123456789 Inexact Rounded
inx056 add  0.000000009     123456789 -> 123456789 Inexact Rounded
inx057 add  0.000000000009  123456789 -> 123456789 Inexact Rounded

inx062 add  -0              123456789 -> 123456789
inx063 add  -0.0            123456789 -> 123456789 Rounded
inx064 add  -0.00           123456789 -> 123456789 Rounded
inx065 add  -1              123456789 -> 123456788
inx066 add  -0.1            123456789 -> 123456789 Inexact Rounded
inx067 add  -0.01           123456789 -> 123456789 Inexact Rounded
inx068 add  -0.001          123456789 -> 123456789 Inexact Rounded
inx069 add  -0.000001       123456789 -> 123456789 Inexact Rounded
inx070 add  -0.000000001    123456789 -> 123456789 Inexact Rounded
inx071 add  -0.000000000001 123456789 -> 123456789 Inexact Rounded
inx072 add  -0.9            123456789 -> 123456788 Inexact Rounded
inx073 add  -0.09           123456789 -> 123456789 Inexact Rounded
inx074 add  -0.009          123456789 -> 123456789 Inexact Rounded
inx075 add  -0.000009       123456789 -> 123456789 Inexact Rounded
inx076 add  -0.000000009    123456789 -> 123456789 Inexact Rounded
inx077 add  -0.000000000009 123456789 -> 123456789 Inexact Rounded

-- some boundaries
inx081 add    999999999           0     -> 999999999
inx082 add  0.999999999 0.000000000     -> 0.999999999
inx083 add    999999999           1     -> 1.00000000E+9 Rounded
inx084 add  0.999999999 0.000000001     -> 1.00000000    Rounded
inx085 add    999999999           2     -> 1.00000000E+9 Inexact Rounded
inx086 add  0.999999999 0.000000002     -> 1.00000000    Inexact Rounded
inx087 add    999999999           3     -> 1.00000000E+9 Inexact Rounded
inx089 add  0.999999999 0.000000003     -> 1.00000000    Inexact Rounded

-- minus, plus, and subtract all assumed to work like add.

-- multiply
precision: 8
inx101 multiply  1000  1000        ->  1000000
inx102 multiply  9000  9000        -> 81000000
inx103 multiply  9999  9999        -> 99980001
inx104 multiply  1000 10000        -> 10000000
inx105 multiply 10000 10000        -> 1.0000000E+8 Rounded
inx106 multiply 10001 10000        -> 1.0001000E+8 Rounded
inx107 multiply 10001 10001        -> 1.0002000E+8 Inexact Rounded
inx108 multiply 10101 10001        -> 1.0102010E+8 Inexact Rounded
inx109 multiply 10001 10101        -> 1.0102010E+8 Inexact Rounded

-- divide
precision: 4
inx201 divide  1000  1000        ->  1
inx202 divide  1000     1        ->  1000
inx203 divide  1000     2        ->   500
inx204 divide  1000     3        ->   333.3  Inexact Rounded
inx205 divide  1000     4        ->   250
inx206 divide  1000     5        ->   200
inx207 divide  1000     6        ->   166.7  Inexact Rounded
inx208 divide  1000     7        ->   142.9  Inexact Rounded
inx209 divide  1000     8        ->   125
inx210 divide  1000     9        ->   111.1  Inexact Rounded
inx211 divide  1000    10        ->   100

inx220 divide     1     1        ->   1
inx221 divide     1     2        ->   0.5
inx222 divide     1     4        ->   0.25
inx223 divide     1     8        ->   0.125
inx224 divide     1    16        ->   0.0625
inx225 divide     1    32        ->   0.03125
inx226 divide     1    64        ->   0.01563  Inexact Rounded
inx227 divide     1   128        ->   0.007813 Inexact Rounded

precision: 5
inx230 divide     1     1        ->   1
inx231 divide     1     2        ->   0.5
inx232 divide     1     4        ->   0.25
inx233 divide     1     8        ->   0.125
inx234 divide     1    16        ->   0.0625
inx235 divide     1    32        ->   0.03125
inx236 divide     1    64        ->   0.015625
inx237 divide     1   128        ->   0.0078125

precision: 3
inx240 divide     1     1        ->   1
inx241 divide     1     2        ->   0.5
inx242 divide     1     4        ->   0.25
inx243 divide     1     8        ->   0.125
inx244 divide     1    16        ->   0.0625
inx245 divide     1    32        ->   0.0313   Inexact Rounded
inx246 divide     1    64        ->   0.0156   Inexact Rounded
inx247 divide     1   128        ->   0.00781  Inexact Rounded

precision: 2
inx250 divide     1     1        ->   1
inx251 divide     1     2        ->   0.5
inx252 divide     1     4        ->   0.25
inx253 divide     1     8        ->   0.13     Inexact Rounded
inx254 divide     1    16        ->   0.063    Inexact Rounded
inx255 divide     1    32        ->   0.031    Inexact Rounded
inx256 divide     1    64        ->   0.016    Inexact Rounded
inx257 divide     1   128        ->   0.0078   Inexact Rounded

precision: 1
inx260 divide     1     1        ->   1
inx261 divide     1     2        ->   0.5
inx262 divide     1     4        ->   0.3      Inexact Rounded
inx263 divide     1     8        ->   0.1      Inexact Rounded
inx264 divide     1    16        ->   0.06     Inexact Rounded
inx265 divide     1    32        ->   0.03     Inexact Rounded
inx266 divide     1    64        ->   0.02     Inexact Rounded
inx267 divide     1   128        ->   0.008    Inexact Rounded


-- power
precision: 4
inx301 power    0.5     2        ->   0.25
inx302 power    0.5     4        ->   0.0625
inx303 power    0.5     8        ->   0.003906   Inexact Rounded
inx304 power    0.5    16        ->   0.00001526 Inexact Rounded
inx305 power    0.5    32        ->   2.328E-10  Inexact Rounded

-- compare, divideInteger, and remainder are always exact

-- rescale
precision: 4
inx401 rescale 0       0   -> 0
inx402 rescale 1       0   -> 1
inx403 rescale 0.1    +2   -> 0E+2 Inexact Rounded
inx404 rescale 0.1    +1   -> 0E+1 Inexact Rounded
inx405 rescale 0.1     0   -> 0 Inexact Rounded
inx406 rescale 0.1    -1   -> 0.1
inx407 rescale 0.1    -2   -> 0.10

-- long operands cause rounding too
precision: 9
inx801 plus  123456789  -> 123456789
inx802 plus  1234567890 -> 1.23456789E+9 Rounded
inx803 plus  1234567891 -> 1.23456789E+9 Inexact Rounded
inx804 plus  1234567892 -> 1.23456789E+9 Inexact Rounded
inx805 plus  1234567899 -> 1.23456790E+9 Inexact Rounded
inx806 plus  1234567900 -> 1.23456790E+9 Rounded

