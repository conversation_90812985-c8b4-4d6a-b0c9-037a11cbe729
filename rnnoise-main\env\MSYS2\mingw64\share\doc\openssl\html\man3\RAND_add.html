<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RAND_add</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RAND_add, RAND_poll, RAND_seed, RAND_status, RAND_event, RAND_screen, RAND_keep_random_devices_open - add randomness to the PRNG or get its status</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/rand.h&gt;

int RAND_status(void);
int RAND_poll();

void RAND_add(const void *buf, int num, double randomness);
void RAND_seed(const void *buf, int num);

void RAND_keep_random_devices_open(int keep);</code></pre>

<p>The following functions have been deprecated since OpenSSL 1.1.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>int RAND_event(UINT iMsg, WPARAM wParam, LPARAM lParam);
void RAND_screen(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions can be used to seed the random generator and to check its seeded state. In general, manual (re-)seeding of the default OpenSSL random generator (<a href="../man3/RAND_OpenSSL.html">RAND_OpenSSL(3)</a>) is not necessary (but allowed), since it does (re-)seed itself automatically using trusted system entropy sources. This holds unless the default RAND_METHOD has been replaced or OpenSSL was built with automatic reseeding disabled, see <a href="../man7/RAND.html">RAND(7)</a> for more details.</p>

<p>RAND_status() indicates whether or not the random generator has been sufficiently seeded. If not, functions such as <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a> will fail.</p>

<p>RAND_poll() uses the system&#39;s capabilities to seed the random generator using random input obtained from polling various trusted entropy sources. The default choice of the entropy source can be modified at build time, see <a href="../man7/RAND.html">RAND(7)</a> for more details.</p>

<p>RAND_add() mixes the <b>num</b> bytes at <b>buf</b> into the internal state of the random generator. This function will not normally be needed, as mentioned above. The <b>randomness</b> argument is an estimate of how much randomness is contained in <b>buf</b>, in bytes, and should be a number between zero and <b>num</b>. Details about sources of randomness and how to estimate their randomness can be found in the literature; for example [NIST SP 800-90B]. The content of <b>buf</b> cannot be recovered from subsequent random generator output. Applications that intend to save and restore random state in an external file should consider using <a href="../man3/RAND_load_file.html">RAND_load_file(3)</a> instead.</p>

<p>NOTE: In FIPS mode, random data provided by the application is not considered to be a trusted entropy source. It is mixed into the internal state of the RNG as additional data only and this does not count as a full reseed. For more details, see <a href="../man7/EVP_RAND.html">EVP_RAND(7)</a>.</p>

<p>RAND_seed() is equivalent to RAND_add() with <b>randomness</b> set to <b>num</b>.</p>

<p>RAND_keep_random_devices_open() is used to control file descriptor usage by the random seed sources. Some seed sources maintain open file descriptors by default, which allows such sources to operate in a chroot(2) jail without the associated device nodes being available. When the <b>keep</b> argument is zero, this call disables the retention of file descriptors. Conversely, a nonzero argument enables the retention of file descriptors. This function is usually called during initialization and it takes effect immediately. This capability only applies to the default provider.</p>

<p>RAND_event() and RAND_screen() are equivalent to RAND_poll() and exist for compatibility reasons only. See HISTORY section below.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RAND_status() returns 1 if the random generator has been seeded with enough data, 0 otherwise.</p>

<p>RAND_poll() returns 1 if it generated seed data, 0 otherwise.</p>

<p>RAND_event() returns RAND_status().</p>

<p>The other functions do not return values.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man3/RAND_egd.html">RAND_egd(3)</a>, <a href="../man3/RAND_load_file.html">RAND_load_file(3)</a>, <a href="../man7/RAND.html">RAND(7)</a> <a href="../man7/EVP_RAND.html">EVP_RAND(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>RAND_event() and RAND_screen() were deprecated in OpenSSL 1.1.0 and should not be used.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


