# help.es.txt - es GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
Está en su mano asignar un valor aquí. Dicho valor nunca será exportado a
terceros. Es necesario para implementar la red de confianza, no tiene nada
que ver con la red de certificados (implícitamente creada).
.

.gpg.edit_ownertrust.set_ultimate.okay
Para construir la Red-de-Confianza, GnuPG necesita saber qué claves
tienen confianza absoluta - normalmente son las claves para las que usted
puede acceder a la clave secreta. Conteste "sí" para hacer que esta
clave se considere como de total confianza

.

.gpg.untrusted_key.override
Si quiere usar esta clave no fiable de todos modos, conteste "sí".
.

.gpg.pklist.user_id.enter
Introduzca el ID de usuario al que quiere enviar el mensaje.
.

.gpg.keygen.algo
Seleccione el algoritmo que usar.

DSA (alias DSS) es el Algoritmo de Firma Digital y sólo se usa para firmas.

Elgamal es un algoritmo sólo para cifrar.

RSA sirve tanto para firmar como para cifrar.

La primera clave (clave primaria) debe ser siempre de tipo capaz de firmar.
.

.gpg.keygen.algo.rsa_se
En general no es una buena idea usar la misma clave para firmar y
cifrar. Este algoritmo debéria usarse solo en ciertos contextos.
Por favor consulte primero a un experto en seguridad.
.

.gpg.keygen.size
Introduzca la longitud de la clave
.

.gpg.keygen.size.huge.okay
Responda "sí" o "no"
.

.gpg.keygen.size.large.okay
Responda "sí" o "no"
.

.gpg.keygen.valid
Introduzca el valor requerido conforme se muestra.
Es posible introducir una fecha ISO (AAAA-MM-DD), pero no se obtendrá una
buena respuesta a los errores; el sistema intentará interpretar el valor
introducido como un intervalo.
.

.gpg.keygen.valid.okay
Responda "sí" o "no"
.

.gpg.keygen.name
Introduzca el nombre del dueño de la clave
.

.gpg.keygen.email
Introduzca una dirección de correo electrónico (opcional pero muy
recomendable)
.

.gpg.keygen.comment
Introduzca un comentario opcional
.

.gpg.keygen.userid.cmd
N  para cambiar el nombre.
C  para cambiar el comentario.
E  para cambiar la dirección.
O  para continuar con la generación de clave.
S  para interrumpir la generación de clave.
.

.gpg.keygen.sub.okay
Responda "sí" (o sólo "s") para generar la subclave.
.

.gpg.sign_uid.okay
Responda "sí" o "no"
.

.gpg.sign_uid.class
Cuando firme un ID de usuario en una clave, debería verificar que la clave
pertenece a la persona que se nombra en el ID de usuario. Es útil para
otros saber cómo de cuidadosamente lo ha verificado.

"0" significa que no hace ninguna declaración concreta sobre como ha
      comprobado la validez de la clave.

"1" significa que cree que la clave pertenece a la persona que declara
      poseerla pero no pudo o no verificó la clave en absoluto. Esto es útil
      para una verificación en persona cuando firmas la clave de un usuario
      pseudoanónimo.

"2" significa que hizo una comprobación informal de la clave. Por ejemplo
      podría querer decir que comprobó la huella dactilar de la clave y
      comprobó el ID de usuario en la clave con un ID fotográfico.

"3" significa que hizo una comprobación exhaustiva de la clave. Por
      ejemplo verificando la huella dactilar de la clave con el propietario
      de la clave, y que comprobó, mediante un documento difícil de falsificar
      con ID fotográfico (como un pasaporte) que el nombre del poseedor de la
      clave coincide con el ID de usuario en la clave y finalmente que verificó
      (intercambiando email) que la dirección de email de la clave pertenece
      al poseedor de la clave.

Observe que los ejemplos dados en los niveles 2 y 3 son *solo* ejemplos.
En definitiva, usted decide lo que significa "informal" y "exhaustivo"
para usted cuando firma las claves de otros.

Si no sabe qué contestar, conteste "0".
.

.gpg.change_passwd.empty.okay
Responda "sí" o "no"
.

.gpg.keyedit.save.okay
Responda "sí" o "no"
.

.gpg.keyedit.cancel.okay
Responda "sí" o "no"
.

.gpg.keyedit.sign_all.okay
Responda "sí" si quiere firmar TODOS los IDs de usuario
.

.gpg.keyedit.remove.uid.okay
Responda "sí" si realmente quiere borrar este ID de usuario.
¡También se perderán todos los certificados!
.

.gpg.keyedit.remove.subkey.okay
Responda "sí" si quiere borrar esta subclave
.

.gpg.keyedit.delsig.valid
Esta es una firma válida de esta clave. Normalmente no será deseable
borrar esta firma ya que puede ser importante para establecer una conexión
de confianza con la clave o con otra clave certificada por ésta.
.

.gpg.keyedit.delsig.unknown
Esta firma no puede ser comprobada porque no tiene Vd. la clave
correspondiente. Debería posponer su borrado hasta conocer qué clave
se usó, ya que dicha clave podría establecer una conexión de confianza
a través de otra clave certificada.
.

.gpg.keyedit.delsig.invalid
Esta firma no es válida. Tiene sentido borrarla de su anillo.
.

.gpg.keyedit.delsig.selfsig
Esta es una firma que une el ID de usuario a la clave. No suele ser una
buena idea borrar dichas firmas. De hecho, GnuPG podría no ser capaz de
volver a usar esta clave. Así que bórrela tan sólo si esta autofirma no
es válida por alguna razón y hay otra disponible.
.

.gpg.keyedit.updpref.okay
Cambiar las preferencias de todos los IDs de usuario (o sólo los 
seleccionados) a la lista actual de preferencias. El sello de tiempo
de todas las autofirmas afectadas se avanzará en un segundo.

.

.gpg.passphrase.enter
Por favor introduzca la contraseña: una frase secreta 

.

.gpg.passphrase.repeat
Repita la última frase contraseña para asegurarse de lo que tecleó.
.

.gpg.detached_signature.filename
Introduzca el nombre del fichero al que corresponde la firma
.

.gpg.openfile.overwrite.okay
Responda "sí" para sobreescribir el fichero
.

.gpg.openfile.askoutname
Introduzca un nuevo nombre de fichero. Si pulsa INTRO se usará el fichero
por omisión (mostrado entre corchetes).
.

.gpg.ask_revocation_reason.code
Debería especificar un motivo para la certificación. Dependiendo del
contexto puede elegir una opción de esta lista:
  "La clave ha sido comprometida"
      Use esto si tiene razones para pensar que personas no autorizadas
      tuvieron acceso a su clave secreta.
  "La clave ha sido sustituida"
      Use esto si ha reemplazado la clave por otra más nueva.
  "La clave ya no está en uso"
      Use esto si ha dejado de usar esta clave.
  "La identificación de usuario ya no es válida"
      Use esto para señalar que la identificación de usuario no debería
      seguir siendo usada; esto se utiliza normalmente para marcar una
      dirección de correo-e como inválida.

.

.gpg.ask_revocation_reason.text
Si lo desea puede introducir un texto explicando por qué emite
este certificado de revocación. Por favor, que el texto sea breve.
Una línea vacía pone fin al texto.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
