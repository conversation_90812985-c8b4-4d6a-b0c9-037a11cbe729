.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_reauth" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_reauth \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_reauth(gnutls_session_t " session ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int flags" 12
must be zero
.SH "DESCRIPTION"
This function performs the post\-handshake authentication
for TLS 1.3. The post\-handshake authentication is initiated by the server
by calling this function. Clients respond when \fBGNUTLS_E_REAUTH_REQUEST\fP
has been seen while receiving data.

The non\-fatal errors expected by this function are:
\fBGNUTLS_E_INTERRUPTED\fP, \fBGNUTLS_E_AGAIN\fP, as well as
\fBGNUTLS_E_GOT_APPLICATION_DATA\fP when called on server side.

The former two interrupt the authentication procedure due to the transport
layer being interrupted, and the latter because there were pending data prior
to peer initiating the re\-authentication. The server should read/process that
data as unauthenticated and retry calling \fBgnutls_reauth()\fP.

When this function is called under TLS1.2 or earlier or the peer didn't
advertise post\-handshake auth, it always fails with
\fBGNUTLS_E_INVALID_REQUEST\fP. The verification of the received peers certificate
is delegated to the session or credentials verification callbacks. A
server can check whether post handshake authentication is supported
by the client by checking the session flags with \fBgnutls_session_get_flags()\fP.

Prior to calling this function in server side, the function
\fBgnutls_certificate_server_set_request()\fP must be called setting expectations
for the received certificate (request or require). If none are set
this function will return with \fBGNUTLS_E_INVALID_REQUEST\fP.

Note that post handshake authentication is available irrespective
of the initial negotiation type (PSK or certificate). In all cases
however, certificate credentials must be set to the session prior
to calling this function.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on a successful authentication, otherwise a negative error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
