.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_status_request_get2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_status_request_get2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_ocsp_status_request_get2(gnutls_session_t " session ", unsigned " idx ", gnutls_datum_t * " response ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned idx" 12
the index of peer's certificate
.IP "gnutls_datum_t * response" 12
a \fBgnutls_datum_t\fP with DER encoded OCSP response
.SH "DESCRIPTION"
This function returns the OCSP status response received
from the TLS server for the certificate index provided.
The index corresponds to certificates as returned by
gnutls_certificate_get_peers. When index is zero this
function operates identically to \fBgnutls_ocsp_status_request_get()\fP.

The returned  \fIresponse\fP should be treated as
constant. If no OCSP response is available for the
given index then \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
is returned.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
