.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_heartbeat_ping" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_heartbeat_ping \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_heartbeat_ping(gnutls_session_t " session ", size_t " data_size ", unsigned int " max_tries ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "size_t data_size" 12
is the length of the ping payload.
.IP "unsigned int max_tries" 12
if flags is \fBGNUTLS_HEARTBEAT_WAIT\fP then this sets the number of retransmissions. Use zero for indefinite (until timeout).
.IP "unsigned int flags" 12
if \fBGNUTLS_HEARTBEAT_WAIT\fP then wait for pong or timeout instead of returning immediately.
.SH "DESCRIPTION"
This function sends a ping to the peer. If the  \fIflags\fP is set
to \fBGNUTLS_HEARTBEAT_WAIT\fP then it waits for a reply from the peer.

Note that it is highly recommended to use this function with the
flag \fBGNUTLS_HEARTBEAT_WAIT\fP, or you need to handle retransmissions
and timeouts manually.

The total TLS data transmitted as part of the ping message are given by
the following formula: MAX(16,  \fIdata_size\fP )+\fBgnutls_record_overhead_size()\fP+3.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.1.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
