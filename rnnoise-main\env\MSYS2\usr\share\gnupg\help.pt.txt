# help.pt.txt - Portuguese GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
#
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


# Note that this help file needs to be UTF-8 encoded.  When looking
# for a help item, GnuPG scans the help files in the following order
# (assuming a GNU or Unix system):
#
#    /etc/gnupg/help.LL_TT.txt
#    /etc/gnupg/help.LL.txt
#    /etc/gnupg/help.txt
#    /usr/share/gnupg/help.LL_TT.txt
#    /usr/share/gnupg/help.LL.txt
#    /usr/share/gnupg/help.txt
#
# Here LL_TT denotes the full name of the current locale with the
# territory (.e.g. "de_DE"), LL denotes just the locale name
# (e.g. "de").  The first matching item is returned.  To put a dot or
# a hash mark at the beginning of a help text line, it needs to be
# prefixed with ". ".  A single dot may be used to terminated a help
# entry.

.pinentry.qualitybar.tooltip
# [remove the hash mark from the key to enable this text]
# This entry is just an example on how to customize the tooltip shown
# when hovering over the quality bar of the pinentry.  We don't
# install this text so that the hardcoded translation takes
# precedence.  An administrator should write up a short help to tell
# the users about the configured passphrase constraints and save that
# to /etc/gnupg/help.txt.  The help text should not be longer than
# about 800 characters.
Esta barra indica a qualidade da frase-secreta introduzida acima.

Enquanto a barra estiver vermelha, o GnuPG considera a frase-secreta
demasiada fraca para a aceitar.  Peça ao seu administrador detalhes
sobre as restrições de frase-secreta configuradas.
.

.pinentry.constraints.hint.short
# [remove the hash mark from the key to enable this hint]
# This entry is used by some pinentries to display a hint about
# enabled passphrase constraints.  These constraints are configurable
# and the admin may give a hint about them by using this help entry.
Use letras e dígitos.
.

.pinentry.constraints.hint.long
# [remove the hash mark from the key to enable this hint]
# This entry is used by some pinentries to show a tooltip with more
# information about the configured passphrase constraints.
Use letras e dígitos.
Restrições extras são impostas, por exemplo
o uso de matrículas comuns de veículos.
.

.pinentry.formatted_passphrase.hint",
# [remove the hash mark from the key to enable this hint]
# If this entry is not set a standard text is shown
Nota: Os espaços em branco não fazem parte da frase-secreta.
.

.gnupg.agent-problem
# There was a problem accessing or starting the agent.
Não foi possível conectar-se a um Gpg-Agent em execução ou
ocorreu um problema de comunicação com um agent em execução.

O sistema usa um processo em segundo plano, chamado Gpg-Agent, para
procestamento chaves privadas e pedir frase-secretas.  O agent
geralmente é iniciado quando o utilizador faz login e é executado,
enquanto o utilizador estiver logado.  Caso nenhum agent esteja
disponível, o sistema tenta iniciar um em tempo real mas esta versão
do agent é um pouco limitada em funcionalidade e assim, pode levar a
pequenos problemas.

Você provavelmente precisa perguntar ao seu administrador sobre como
resolver o problema.  Como solução alternativa, você pode tentar sair
e entrar na sua sessão e ver se isso ajuda.  Se isso ajudar, por
favor, informe mesmo assim o administrador, porque isto indica um bug
no software.
.

.gnupg.dirmngr-problem
# There was a problen accessing the dirmngr.
Não foi possível conectar-se a um Dirmngr em execução ou ocorreu um
problema de comunicação com um Dirmngr em execução.

Para pesquisar listas de revogação de certificados (CRLs), executar
validação OCSP e para pesquisar chaves através de servidores LDAP, o
sistema usa um programa de serviço externo chamado Dirmngr.  O Dirmngr
geralmente está em execução como um serviço do sistema (daemon) e não
precisa de qualquer atenção por parte do utilizador.  Em caso de
problemas, o sistema poderá iniciar sua própria cópia do Dirmngr tendo
por base uma requisição; esta é a solução alternativa e produz
desempenho limitado.

Se você encontrar este problema, você deve perguntar ao seu
administrador de sistema como proceder.  Como uma solução provisória,
você pode tentar desabilitar a verificação de CRL na configuração do
gpgsm.
.

.gpg.edit_ownertrust.value
# The help identies prefixed with "gpg." used to be hard coded in gpg
# but may now be overridden by help texts from this file.
Cabe a você atribuir um valor aqui; este valor nunca será exportado a
quaisquer terceiros.  Precisamos dele para implementar a
Rede-da-Confiança; que tem nada a ver com a rede-de-certificados
(criada implicitamente).
.

.gpg.edit_ownertrust.set_ultimate.okay
Para construir a Rede-da-Confiança, o GnuPG precisa saber quais são as
chaves plenamente confiáveis - essas são geralmente as chaves
para as quais você tem acesso à chave secreta.  Responder "sim" para
definir esta chave como plenamente confiável.
.

.gpg.untrusted_key.override
Se você, mesmo assim, quiser usar esta chave não confiável, responder
"sim".
.

.gpg.pklist.user_id.enter
Introduzir a ID de utilizador do destinatário para quem você deseja
enviar a mensagem.
.

.gpg.keygen.algo
Selecionar o algoritmo a ser usado.

DSA (aka DSS) é o Algoritmo de Assinatura Digital e só pode ser usado
para assinaturas.

Elgamal é um algoritmo só para cifração.

O RSA pode ser usado para assinaturas ou cifração.

A primeira chave (principal) deve ser sempre uma chave capaz de
assinar.
.

.gpg.keygen.algo.rsa_se
De modo geral, não é uma boa ideia usar a mesma chave para assinar e
cifrar.  Este algoritmo só deve ser usado em determinados domínios.
Consulte primeiro o seu especialista em segurança.
.

.gpg.keygen.cardkey
Selecionar qual chave do cartão será utilizada.

A listagem mostra o índice de seleção, o keygrip (uma string de
dígitos hex), a referência da chave específica do cartão, o algoritmo
que foi usado para esta chave, e, entre parênteses, a utilização da
chave (cert, sign, auth, encr).  Se conhecida, a utilização padrão de
uma chave está marcada com um asterisco.
.

.gpg.keygen.keygrip
Introduzir o keygrip da chave a ser adicionada.

O keygrip é uma string de 40 dígitos hex que identifica uma chave.
Ele deve pertencer a uma chave secreta ou a uma subchave secreta
armazenada no seu porta-chaves.
.

.gpg.keygen.flags
Alterne as capacidades da chave.

Só é possível alternar as capacidades que são possíveis para o
algoritmo selecionado.

Para definir rapidamente os recursos de uma só vez, é possível inserir
um '=' como primeiro caractere seguido de uma lista de letras
indicando a capacidade a definir: 's' para assinatura, 'e' para
cifração e 'a' para autenticação.  Letras inválidas e capacidades
impossíveis são ignoradas.  Este submenu é imediatamente fechado
depois de usar este atalho.
.

.gpg.keygen.size
Introduzir o tamanho da chave.

A pré-definição sugerida geralmente é uma boa escolha.

Se você quiser usar um tamanho de chave grande, por exemplo, 4096 bit,
pense novamente se realmente faz sentido para você.  Você poderá
querer ver a página web https://www.xkcd.com/538/ .
.

.gpg.keygen.size.huge.okay
Responder "sim" ou "não".
.

.gpg.keygen.size.large.okay
Responder "sim" ou "não".
.

.gpg.keygen.valid
Introduzir o valor exigido, conforme mostrado no prompt.
É possível inserir uma data ISO (AAAA-MM-DD), mas você não vai obter
uma boa resposta de erro - em vez disso, o sistema tenta interpretar o
valor dado como um intervalo.
.

.gpg.keygen.valid.okay
Responder "sim" ou "não".
.

.gpg.keygen.name
Introduzir o nome do titular da chave.
Os caracteres "<" e ">" não são permitidos.
Exemplo: Heinrich Heine
.

.gpg.keygen.email
Introduza um endereço de email opcional, mas altamente sugerido.
Exemplo: <EMAIL>
.

.gpg.keygen.comment
Introduza um comentário opcional.
Os caracteres "(" e ")" não são permitidos.
De modo geral, não há necessidade de comentários.
.

.gpg.keygen.userid.cmd
# (Keep a leading empty line)

N para alterar o nome.
C para alterar o comentário.
E para alterar o endereço de email.
O para continuar com a geração de chaves.
Q para sair da geração de chaves.
.

.gpg.keygen.sub.okay
Responder "sim" (ou apenas "s") se não houver problema em gerar a
subchave.
.

.gpg.sign_uid.okay
Responder "sim" ou "não".
.

.gpg.sign_uid.class
Ao assinar uma ID de utilizador de uma chave, você deve primeiro
verificar que a chave pertence à pessoa correta da ID de utilizador.
É útil para os outros saber com que cuidado você verificou isso.

"0" significa que você faz nenhuma reivindicação específica sobre o
    quão cuidadosamente você verificou o chave.

"1" significa que você acredita que a pessoa é dona da chave que
    afirma possuí-la mas você não pôde, ou não verificou a chave.
    Isto é útil para uma verificação de "persona", onde você assina a
    chave de um utilizador pseudónimo.

"2" significa que você fez uma verificação casual da chave.  Por
    exemplo, isso poderia significar que você verificou a impressão
    digital da chave e verificou a ID de utilizador da chave em
    relação a uma ID fotográfica.

"3" significa que você fez uma verificação completa da chave.  Por
    exemplo, isto poderia significa que você verificou a impressão
    digital da chave com o dono da chave em pessoa, e que você
    verificou, por meio de um documento difícil de falsificar com uma
    ID fotográfica (como um passaporte) que o nome do dono da chave
    corresponde ao na ID de utilizador na chave e, finalmente, que
    você verificou (por troca de email) que o endereço de email na
    chave pertence ao dono da chave.

Note que os exemplos dados acima para os níveis 2 e 3 são *apenas*
exemplos.  No final, cabe a você decidir o que "casual" e "completo"
significa para você quando você assina outras chaves.

Se você não sabe qual é a resposta certa, responda "0".
.

.gpg.change_passwd.empty.okay
Responder "sim" ou "não".
.

.gpg.keyedit.save.okay
Responder "sim" ou "não".
.

.gpg.keyedit.cancel.okay
Responder "sim" ou "não".
.

.gpg.keyedit.sign_all.okay
Responder "sim" se quiser assinar TODAS as IDs de utilizador.
.

.gpg.keyedit.remove.uid.okay
Responda "sim" se tem a certeza que você quer apagar esta ID de
utilizador.  Todos os certificados também são perdidos!
.

.gpg.keyedit.remove.subkey.okay
Responder "sim" se não houver problema em apagar a subchave.
.

.gpg.keyedit.delsig.valid
Esta é uma assinatura válida na chave; você normalmente não quer
apagar esta assinatura porque pode ser importante para estabelecer uma
conexão de confiança com a chave ou com outra chave certificada por
esta chave.
.

.gpg.keyedit.delsig.unknown
Esta assinatura não pode ser verificada porque você não tem a chave
correspondente.  Você deve adiar apagar, até quando você souber qual
chave foi usada, porque esta chave de assinatura pode estabelecer uma
conexão de confiança por meio de outra chave já certificada.
.

.gpg.keyedit.delsig.invalid
A assinatura não é válida.  Faz sentido removê-la de seu porta-chaves.
.

.gpg.keyedit.delsig.selfsig
Esta é uma assinatura que vincula a ID de utilizador à chave.
Geralmente não é uma boa ideia remover tal assinatura.  Até porque o
GnuPG pode deixar de ser capaz de usar esta chave.  Por isso, faça
isso só se, por algum motivo, esta auto-assinatura não for válida e
uma segunda assinatura estiver disponível.
.

.gpg.keyedit.updpref.okay
Alterar as preferências de todas as IDs de utilizador (ou apenas das
selecionadas) para a lista atual de preferências. O timestamp de todas
as auto-assinaturas afetadas serão adiantadas em um segundo.
.

.gpg.passphrase.enter
# (keep a leading empty line)

Introduza a frase-secreta; esta é uma frase que é secreta.
.

.gpg.passphrase.repeat
Repita a última frase-secreta, para ter certeza da que introduziu.
.

.gpg.detached_signature.filename
Fornecer o nome do ficheiro ao qual a assinatura se aplica.
.

.gpg.openfile.overwrite.okay
# openfile.c (overwrite_filep)
Responder "sim" se não houver problema em sobrescrever o ficheiro.
.

.gpg.openfile.askoutname
# openfile.c (ask_outfile_name)
Introduza um novo nome de ficheiro.  Se você apenas carregar RETURN o
ficheiro pré-definido (que está entre parênteses) será usado.
.

.gpg.ask_revocation_reason.code
# revoke.c (ask_revocation_reason)

Você deve especificar um motivo para a revogação.  Dependendo do
contexto que você pode escolher a partir desta lista:
  "Chave foi comprometida"
      Usar isto se você tiver um motivo para acreditar que pessoas não
      autorizadas tiveram acesso à sua chave secreta.
  "Chave foi substituída"
      Usar isto se você tiver substituído esta chave por uma mais
      recente.
  "Chave não é mais usada"
      Usar isto se você tiver desativado esta chave.
  "ID de utilizador não é mais válido"
      Usar isto para declarar que a ID de utilizador não deve ser mais
      utilizada; isto normalmente é usado para marcar um endereço de
      email como inválido.
.

.gpg.ask_revocation_reason.text
# revoke.c (ask_revocation_reason)
Se desejar, você pode introduzir um texto descrevendo porque você emite
este certificado de revogação.  Mantenha este texto conciso.
Uma linha vazia termina o texto.
.

.gpg.tofu.conflict
# tofu.c
TOFU detetou outra chave com o mesmo endereço de email (ou um muito
semelhante).  Pode ser que o utilizador tenha criado uma nova
chave. Neste caso, você pode confiar com segurança na nova chave (mas
confirme perguntando à pessoa).  No entanto, também pode ser que a
chave seja uma falsificação ou esteja a occorrer um ataque de
Man-in-the-Middle (MitM).  Neste caso, você deve marcar a chave como
sendo incorreta, para que não seja confiável.  Marcar uma chave como
sendo não confiável significa que quaisquer assinaturas serão
consideradas incorretas e que as tentativas de cifrar para a chave
serão sinalizadas.  Se você tem dúvidas e não pode verificar de
momento, você deve ou aceitar uma vez ou rejeitar uma vez.
.

.gpgsm.root-cert-not-trusted
# This text gets displayed by the audit log if
# a root certificates was not trusted.
O certificado raiz (a âncora-de-confiança) não é confiável. Dependendo
da configuração, você poderá aparecer-lhe um prompt, para marcar esse
certificado raiz como confiável ou você poderá precisar de dizer
manualmente ao GnuPG para confiar nesse certificado.  Os certificados
confiáveis são configurados no ficheiro trustlist.txt da pasta home do
GnuPG.  Em caso de dúvida, pergunte ao seu administrador de sistema se
deve confiar neste certificado.
.

.gpgsm.crl-problem
# This text is displayed by the audit log for problems with
# the CRL or OCSP checking.
Dependendo da sua configuração, ocorreu um problema ao obter a CRL ou
a realizar de uma verificação OCSP.  Há uma grande variedade de razões
pelas quais isto não funcionou.  Verifique o manual para possíveis
soluções.
.


# Local variables:
# mode: default-generic
# coding: utf-8
# End:
