# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: rnnLib
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/RNN/rnnoise-main/src/
# =============================================================================
# Object build statements for STATIC_LIBRARY target rnnLib


#############################################
# Order-only phony target for rnnLib

build cmake_object_order_depends_target_rnnLib: phony || .

build CMakeFiles/rnnLib.dir/celt_lpc.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/celt_lpc.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = CMakeFiles\rnnLib.dir\celt_lpc.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = CMakeFiles\rnnLib.dir

build CMakeFiles/rnnLib.dir/kiss_fft.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/kiss_fft.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = CMakeFiles\rnnLib.dir\kiss_fft.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = CMakeFiles\rnnLib.dir

build CMakeFiles/rnnLib.dir/rnn_data.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/rnn_data.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = CMakeFiles\rnnLib.dir\rnn_data.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = CMakeFiles\rnnLib.dir

build CMakeFiles/rnnLib.dir/rnn.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/rnn.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = CMakeFiles\rnnLib.dir\rnn.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = CMakeFiles\rnnLib.dir

build CMakeFiles/rnnLib.dir/denoise.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/denoise.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = CMakeFiles\rnnLib.dir\denoise.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = CMakeFiles\rnnLib.dir

build CMakeFiles/rnnLib.dir/pitch.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/pitch.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = CMakeFiles\rnnLib.dir\pitch.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = CMakeFiles\rnnLib.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target rnnLib


#############################################
# Link the static library librnnLib.a

build librnnLib.a: C_STATIC_LIBRARY_LINKER__rnnLib_ CMakeFiles/rnnLib.dir/celt_lpc.c.obj CMakeFiles/rnnLib.dir/kiss_fft.c.obj CMakeFiles/rnnLib.dir/rnn_data.c.obj CMakeFiles/rnnLib.dir/rnn.c.obj CMakeFiles/rnnLib.dir/denoise.c.obj CMakeFiles/rnnLib.dir/pitch.c.obj
  OBJECT_DIR = CMakeFiles\rnnLib.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = librnnLib.a
  TARGET_PDB = rnnLib.a.dbg

# =============================================================================
# Object build statements for EXECUTABLE target denoise_training_gao


#############################################
# Order-only phony target for denoise_training_gao

build cmake_object_order_depends_target_denoise_training_gao: phony || .

build CMakeFiles/denoise_training_gao.dir/denoise.c.obj: C_COMPILER__denoise_training_gao_unscanned_ D$:/RNN/rnnoise-main/src/denoise.c || cmake_object_order_depends_target_denoise_training_gao
  DEFINES = -DTRAINING=1
  DEP_FILE = CMakeFiles\denoise_training_gao.dir\denoise.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\denoise_training_gao.dir
  OBJECT_FILE_DIR = CMakeFiles\denoise_training_gao.dir

build CMakeFiles/denoise_training_gao.dir/kiss_fft.c.obj: C_COMPILER__denoise_training_gao_unscanned_ D$:/RNN/rnnoise-main/src/kiss_fft.c || cmake_object_order_depends_target_denoise_training_gao
  DEFINES = -DTRAINING=1
  DEP_FILE = CMakeFiles\denoise_training_gao.dir\kiss_fft.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\denoise_training_gao.dir
  OBJECT_FILE_DIR = CMakeFiles\denoise_training_gao.dir

build CMakeFiles/denoise_training_gao.dir/pitch.c.obj: C_COMPILER__denoise_training_gao_unscanned_ D$:/RNN/rnnoise-main/src/pitch.c || cmake_object_order_depends_target_denoise_training_gao
  DEFINES = -DTRAINING=1
  DEP_FILE = CMakeFiles\denoise_training_gao.dir\pitch.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\denoise_training_gao.dir
  OBJECT_FILE_DIR = CMakeFiles\denoise_training_gao.dir

build CMakeFiles/denoise_training_gao.dir/celt_lpc.c.obj: C_COMPILER__denoise_training_gao_unscanned_ D$:/RNN/rnnoise-main/src/celt_lpc.c || cmake_object_order_depends_target_denoise_training_gao
  DEFINES = -DTRAINING=1
  DEP_FILE = CMakeFiles\denoise_training_gao.dir\celt_lpc.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\denoise_training_gao.dir
  OBJECT_FILE_DIR = CMakeFiles\denoise_training_gao.dir

build CMakeFiles/denoise_training_gao.dir/rnn.c.obj: C_COMPILER__denoise_training_gao_unscanned_ D$:/RNN/rnnoise-main/src/rnn.c || cmake_object_order_depends_target_denoise_training_gao
  DEFINES = -DTRAINING=1
  DEP_FILE = CMakeFiles\denoise_training_gao.dir\rnn.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\denoise_training_gao.dir
  OBJECT_FILE_DIR = CMakeFiles\denoise_training_gao.dir

build CMakeFiles/denoise_training_gao.dir/rnn_data.c.obj: C_COMPILER__denoise_training_gao_unscanned_ D$:/RNN/rnnoise-main/src/rnn_data.c || cmake_object_order_depends_target_denoise_training_gao
  DEFINES = -DTRAINING=1
  DEP_FILE = CMakeFiles\denoise_training_gao.dir\rnn_data.c.obj.d
  INCLUDES = -ID:/RNN/rnnoise-main/src/../include
  OBJECT_DIR = CMakeFiles\denoise_training_gao.dir
  OBJECT_FILE_DIR = CMakeFiles\denoise_training_gao.dir


# =============================================================================
# Link build statements for EXECUTABLE target denoise_training_gao


#############################################
# Link the executable denoise_training_gao.exe

build denoise_training_gao.exe: C_EXECUTABLE_LINKER__denoise_training_gao_ CMakeFiles/denoise_training_gao.dir/denoise.c.obj CMakeFiles/denoise_training_gao.dir/kiss_fft.c.obj CMakeFiles/denoise_training_gao.dir/pitch.c.obj CMakeFiles/denoise_training_gao.dir/celt_lpc.c.obj CMakeFiles/denoise_training_gao.dir/rnn.c.obj CMakeFiles/denoise_training_gao.dir/rnn_data.c.obj
  LINK_LIBRARIES = -lm  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\denoise_training_gao.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = denoise_training_gao.exe
  TARGET_IMPLIB = libdenoise_training_gao.dll.a
  TARGET_PDB = denoise_training_gao.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main\src && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main\src && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe --regenerate-during-build -SD:\RNN\rnnoise-main\src -BD:\RNN\rnnoise-main\src"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build denoise_training_gao: phony denoise_training_gao.exe

build rnnLib: phony librnnLib.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/RNN/rnnoise-main/src

build all: phony librnnLib.a denoise_training_gao.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja D$:/RNN/rnnoise-main/src/cmake_install.cmake: RERUN_CMAKE | CMakeCache.txt CMakeFiles/4.0.3/CMakeCCompiler.cmake CMakeFiles/4.0.3/CMakeRCCompiler.cmake CMakeFiles/4.0.3/CMakeSystem.cmake CMakeLists.txt D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeCInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeRCInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU-C.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/4.0.3/CMakeCCompiler.cmake CMakeFiles/4.0.3/CMakeRCCompiler.cmake CMakeFiles/4.0.3/CMakeSystem.cmake CMakeLists.txt D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeCInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeRCInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU-C.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows.cmake D$:/RNN/rnnoise-main/env/MSYS2/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
