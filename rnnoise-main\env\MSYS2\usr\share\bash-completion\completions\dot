# dot(1) completion                                        -*- shell-script -*-

_comp_cmd_dot()
{
    local cur prev words cword comp_args
    _comp_initialize -n := -- "$@" || return

    [[ $prev == -[V?] ]] && return

    case $cur in
        -G* | -N* | -E* | -l?* | -q?* | -s?* | -Ln* | -LU* | -LC* | -LT*)
            return
            ;;
        -T*)
            # generate langs
            _comp_compgen -c "${cur#-T}" split -P "-T" -- "$(
                "$1" -TNON_EXISTENT 2>&1 | command sed -ne 's/.*one of://p'
            )"
            return
            ;;
        -K*)
            # generate layouts
            _comp_compgen -c "${cur#-K}" split -P "-K" -- "$(
                "$1" -KNON_EXISTENT 2>&1 | command sed -ne 's/.*one of://p'
            )"
            return
            ;;
        -o*)
            _comp_compgen -c "${cur#-o}" filedir &&
                COMPREPLY=("${COMPREPLY[@]/#/-o}")
            return
            ;;
    esac

    if [[ $cur == -* ]]; then
        _comp_compgen -- -W '-V -v -G -N -E -T -K -l -o -O -P -q -s -y -n -n1
            -n2 -x -Lg -LO -Ln -LU -LC -LT -m -c -?'
        [[ ${COMPREPLY-} == -@([GNETKo]|L[nUCT]) ]] && compopt -o nospace
        return
    fi

    _comp_compgen_filedir '@(gv|dot)'
} &&
    complete -F _comp_cmd_dot dot

# ex: filetype=sh
