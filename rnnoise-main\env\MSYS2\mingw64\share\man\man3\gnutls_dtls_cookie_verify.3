.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dtls_cookie_verify" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dtls_cookie_verify \- API function
.SH SYNOPSIS
.B #include <gnutls/dtls.h>
.sp
.BI "int gnutls_dtls_cookie_verify(gnutls_datum_t * " key ", void * " client_data ", size_t " client_data_size ", void * " _msg ", size_t " msg_size ", gnutls_dtls_prestate_st * " prestate ");"
.SH ARGUMENTS
.IP "gnutls_datum_t * key" 12
is a random key to be used at cookie generation
.IP "void * client_data" 12
contains data identifying the client (i.e. address)
.IP "size_t client_data_size" 12
The size of client's data
.IP "void * _msg" 12
An incoming message that initiates a connection.
.IP "size_t msg_size" 12
The size of the message.
.IP "gnutls_dtls_prestate_st * prestate" 12
The cookie of this client.
.SH "DESCRIPTION"
This function will verify the received message for
a valid cookie. If a valid cookie is returned then
it should be associated with the session using
\fBgnutls_dtls_prestate_set()\fP;

This function must be called after \fBgnutls_dtls_cookie_send()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success, or a negative error code.  
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
