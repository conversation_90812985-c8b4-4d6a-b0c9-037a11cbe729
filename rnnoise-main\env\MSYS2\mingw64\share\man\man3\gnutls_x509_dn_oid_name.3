.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_dn_oid_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_dn_oid_name \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "const char * gnutls_x509_dn_oid_name(const char * " oid ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * oid" 12
holds an Object Identifier in a null terminated string
.IP "unsigned int flags" 12
0 or GNUTLS_X509_DN_OID_*
.SH "DESCRIPTION"
This function will return the name of a known DN OID. If
\fBGNUTLS_X509_DN_OID_RETURN_OID\fP is specified this function
will return the given OID if no descriptive name has been
found.
.SH "RETURNS"
A null terminated string or NULL otherwise.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
