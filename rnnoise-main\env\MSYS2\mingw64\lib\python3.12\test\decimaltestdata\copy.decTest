------------------------------------------------------------------------
-- copy.decTest -- quiet copy                                         --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 999
minExponent: -999

-- Sanity check
cpyx001 copy       +7.50  -> 7.50

-- Infinities
cpyx011 copy  Infinity    -> Infinity
cpyx012 copy  -Infinity   -> -Infinity

-- NaNs, 0 payload
cpyx021 copy         NaN  -> NaN
cpyx022 copy        -NaN  -> -NaN
cpyx023 copy        sNaN  -> sNaN
cpyx024 copy       -sNaN  -> -sNaN

-- NaNs, non-0 payload
cpyx031 copy       NaN10  -> NaN10
cpyx032 copy      -NaN10  -> -NaN10
cpyx033 copy      sNaN10  -> sNaN10
cpyx034 copy     -sNaN10  -> -sNaN10
cpyx035 copy       NaN7   -> NaN7
cpyx036 copy      -NaN7   -> -NaN7
cpyx037 copy      sNaN101 -> sNaN101
cpyx038 copy     -sNaN101 -> -sNaN101

-- finites
cpyx101 copy          7   -> 7
cpyx102 copy         -7   -> -7
cpyx103 copy         75   -> 75
cpyx104 copy        -75   -> -75
cpyx105 copy       7.50   -> 7.50
cpyx106 copy      -7.50   -> -7.50
cpyx107 copy       7.500  -> 7.500
cpyx108 copy      -7.500  -> -7.500

-- zeros
cpyx111 copy          0   -> 0
cpyx112 copy         -0   -> -0
cpyx113 copy       0E+4   -> 0E+4
cpyx114 copy      -0E+4   -> -0E+4
cpyx115 copy     0.0000   -> 0.0000
cpyx116 copy    -0.0000   -> -0.0000
cpyx117 copy      0E-141  -> 0E-141
cpyx118 copy     -0E-141  -> -0E-141

-- full coefficients, alternating bits
cpyx121 copy   268268268        -> 268268268
cpyx122 copy  -268268268        -> -268268268
cpyx123 copy   134134134        -> 134134134
cpyx124 copy  -134134134        -> -134134134

-- Nmax, Nmin, Ntiny
cpyx131 copy  9.99999999E+999   -> 9.99999999E+999
cpyx132 copy  1E-999            -> 1E-999
cpyx133 copy  1.00000000E-999   -> 1.00000000E-999
cpyx134 copy  1E-1007           -> 1E-1007

cpyx135 copy  -1E-1007          -> -1E-1007
cpyx136 copy  -1.00000000E-999  -> -1.00000000E-999
cpyx137 copy  -1E-999           -> -1E-999
cpyx138 copy  -9.99999999E+999  -> -9.99999999E+999
