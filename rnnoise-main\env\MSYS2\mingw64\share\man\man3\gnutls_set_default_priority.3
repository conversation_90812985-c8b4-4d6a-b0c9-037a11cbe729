.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_set_default_priority" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_set_default_priority \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_set_default_priority(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
Sets the default priority on the ciphers, key exchange methods,
and macs. This is the recommended method of
setting the defaults, in order to promote consistency between applications
using GnuTLS, and to allow GnuTLS using applications to update settings
in par with the library. For client applications which require
maximum compatibility consider calling \fBgnutls_session_enable_compatibility_mode()\fP
after this function.

For an application to specify additional options to priority string
consider using \fBgnutls_set_default_priority_append()\fP.

To allow a user to override the defaults (e.g., when a user interface
or configuration file is available), the functions
\fBgnutls_priority_set_direct()\fP or \fBgnutls_priority_set()\fP can
be used.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.
.SH "SINCE"
2.1.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
