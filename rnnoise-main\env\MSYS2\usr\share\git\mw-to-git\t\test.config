# Name of the web server's directory dedicated to the wiki is WIKI_DIR_NAME
WIKI_DIR_NAME=wiki

# Login and password of the wiki's admin
WIKI_ADMIN=WikiAdmin
WIKI_PASSW=AdminPass1

# Address of the web server
SERVER_ADDR=localhost

# If LIGHTTPD is not set to true, the script will use the default
# web server running in WIKI_DIR_INST.
WIKI_DIR_INST=/var/www

# If LIGHTTPD is set to true, the script will use Lighttpd to run
# the wiki.
LIGHTTPD=true

# The variables below are useful only if LIGHTTPD is set to true.
PORT=1234
PHP_DIR=/usr/bin
LIGHTTPD_DIR=/usr/sbin
WEB=WEB
WEB_TMP=$WEB/tmp
WEB_WWW=$WEB/www

# Where our configuration for the wiki is located
FILES_FOLDER=mediawiki
FILES_FOLDER_DOWNLOAD=$FILES_FOLDER/download
FILES_FOLDER_DB=$FILES_FOLDER/db
FILES_FOLDER_POST_INSTALL_DB=$FILES_FOLDER/post-install-db

# The variables below are used by the script to install a wiki.
# You should not modify these unless you are modifying the script itself.
# tested versions: 1.19.X -> 1.21.1 -> 1.34.2
#
# See https://www.mediawiki.org/wiki/Download for what the latest
# version is.
MW_VERSION_MAJOR=1.34
MW_VERSION_MINOR=2
