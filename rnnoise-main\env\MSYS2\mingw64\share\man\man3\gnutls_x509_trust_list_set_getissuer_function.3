.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_set_getissuer_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_set_getissuer_function \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void gnutls_x509_trust_list_set_getissuer_function(gnutls_x509_trust_list_t " tlist ", gnutls_x509_trust_list_getissuer_function * " func ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t tlist" 12
is a \fBgnutls_x509_trust_list_t\fP type.
.IP "gnutls_x509_trust_list_getissuer_function * func" 12
is the callback function
.SH "DESCRIPTION"
This function sets a callback to be called when the peer's certificate
chain is incomplete due a missing intermediate certificate. The callback
may provide the missing certificate for use during verification.

The callback's function prototype is defined in gnutls/x509.h as:

int (*callback)(gnutls_x509_trust_list_t list,
const gnutls_x509_crt_t cert,
gnutls_x509_crt_t **issuers,
unsigned int *issuers_size);

If the callback function is provided then gnutls will call it during the
certificate verification procedure. The callback may wish to use
\fBgnutls_x509_crt_get_authority_info_access()\fP to get a URI from which
to attempt to download the missing issuer certificate, if available.

On a successful call, the callback shall set '*issuers' and '*issuers_size'
even if the result is empty; in that case '*issuers' will point to \fBNULL\fP and
'*issuers_size' will be 0.  Otherwise, the '*issuers' array shall be
allocated using \fBgnutls_x509_crt_list_import2()\fP. The ownership of both the
array and the elements is transferred to the caller and thus the application
does not need to maintain the memory after the call.

The callback function should return 0 if the attempt to retrieve the issuer
certificates for 'crt' succeeded, or non\-zero to indicate any error occurred
during the attempt. In the latter case, '*issuers' and '*issuers_size' are
not set.
.SH "SINCE"
3.7.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
