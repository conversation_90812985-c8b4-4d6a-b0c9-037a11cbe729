.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_ca_status" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_ca_status \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_ca_status(gnutls_x509_crt_t " crt ", unsigned int " ca ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "unsigned int ca" 12
true(1) or false(0). Depending on the Certificate authority status.
.SH "DESCRIPTION"
This function will set the basicConstraints certificate extension.
Use \fBgnutls_x509_crt_set_basic_constraints()\fP if you want to control
the pathLenConstraint field too.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
