.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_server_get_username" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_server_get_username \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_psk_server_get_username(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.SH "DESCRIPTION"
This should only be called in case of PSK authentication and in
case of a server.

The returned pointer should be considered constant (do not free) and valid 
for the lifetime of the session.

This function will return \fBNULL\fP if the username has embedded NULL bytes.
In that case, \fBgnutls_psk_server_get_username2()\fP should be used to retrieve the username.
.SH "RETURNS"
the username of the peer, or \fBNULL\fP in case of an error,
or if the username has embedded NULLs.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
