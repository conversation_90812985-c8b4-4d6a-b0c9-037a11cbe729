cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file DISCLAIMER within this package.")
cpp_quote(" */")
cpp_quote("")
cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("")

import "objidl.idl";
import "oleidl.idl";
import "servprov.idl";
import "msxml.idl";

cpp_quote("")
interface IPersistMoniker;
interface IBindProtocol;
interface IBinding;
interface IBindStatusCallback;
interface IBindStatusCallbackEx;
interface IBindStatusCallbackMsg;
interface IAuthenticate;
interface IAuthenticateEx;
interface IWindowForBindingUI;
interface ICodeInstall;
interface IHttpNegotiate;
interface IHttpNegotiate2;
interface IHttpNegotiate3;
interface IWinInetFileStream;
interface IXMLElement;
cpp_quote("")
cpp_quote("EXTERN_C const IID CLSID_SBS_StdURLMoniker;")
cpp_quote("EXTERN_C const IID CLSID_SBS_HttpProtocol;")
cpp_quote("EXTERN_C const IID CLSID_SBS_FtpProtocol;")
cpp_quote("EXTERN_C const IID CLSID_SBS_GopherProtocol;")
cpp_quote("EXTERN_C const IID CLSID_SBS_HttpSProtocol;")
cpp_quote("EXTERN_C const IID CLSID_SBS_FileProtocol;")
cpp_quote("EXTERN_C const IID CLSID_SBS_MkProtocol;")
cpp_quote("EXTERN_C const IID CLSID_SBS_UrlMkBindCtx;")
cpp_quote("EXTERN_C const IID CLSID_SBS_SoftDistExt;")
cpp_quote("EXTERN_C const IID CLSID_SBS_CdlProtocol;")
cpp_quote("EXTERN_C const IID CLSID_SBS_ClassInstallFilter;")
cpp_quote("EXTERN_C const IID CLSID_SBS_InternetSecurityManager;")
cpp_quote("EXTERN_C const IID CLSID_SBS_InternetZoneManager;")
cpp_quote("")
cpp_quote("#define BINDF_DONTUSECACHE BINDF_GETNEWESTVERSION")
cpp_quote("#define BINDF_DONTPUTINCACHE BINDF_NOWRITECACHE")
cpp_quote("#define BINDF_NOCOPYDATA BINDF_PULLDATA")
cpp_quote("#define INVALID_P_ROOT_SECURITY_ID ((BYTE*)-1)")
cpp_quote("")
cpp_quote("#define PI_DOCFILECLSIDLOOKUP PI_CLSIDLOOKUP")
cpp_quote("")
cpp_quote("EXTERN_C const IID IID_IAsyncMoniker;")
cpp_quote("EXTERN_C const IID CLSID_StdURLMoniker;")
cpp_quote("EXTERN_C const IID CLSID_HttpProtocol;")
cpp_quote("EXTERN_C const IID CLSID_FtpProtocol;")
cpp_quote("EXTERN_C const IID CLSID_GopherProtocol;")
cpp_quote("EXTERN_C const IID CLSID_HttpSProtocol;")
cpp_quote("EXTERN_C const IID CLSID_FileProtocol;")
cpp_quote("EXTERN_C const IID CLSID_MkProtocol;")
cpp_quote("EXTERN_C const IID CLSID_StdURLProtocol;")
cpp_quote("EXTERN_C const IID CLSID_UrlMkBindCtx;")
cpp_quote("EXTERN_C const IID CLSID_CdlProtocol;")
cpp_quote("EXTERN_C const IID CLSID_ClassInstallFilter;")
cpp_quote("EXTERN_C const IID IID_IAsyncBindCtx;")
cpp_quote("")
cpp_quote("#define SZ_URLCONTEXT           OLESTR(\"URL Context\")")
cpp_quote("#define SZ_ASYNC_CALLEE         OLESTR(\"AsyncCallee\")")
cpp_quote("")
cpp_quote("#define MKSYS_URLMONIKER         6")
cpp_quote("#define URL_MK_LEGACY            0")
cpp_quote("#define URL_MK_UNIFORM           1")
cpp_quote("#define URL_MK_NO_CANONICALIZE   2")
cpp_quote("")
cpp_quote("STDAPI CreateURLMoniker(LPMONIKER pMkCtx, LPCWSTR szURL, LPMONIKER *ppmk);")
cpp_quote("STDAPI CreateURLMonikerEx(LPMONIKER pMkCtx, LPCWSTR szURL, LPMONIKER *ppmk, DWORD dwFlags);")
cpp_quote("STDAPI GetClassURL(LPCWSTR szURL, CLSID *pClsID);")
cpp_quote("STDAPI CreateAsyncBindCtx(DWORD reserved, IBindStatusCallback *pBSCb, IEnumFORMATETC *pEFetc, IBindCtx **ppBC);")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("STDAPI CreateURLMonikerEx2(LPMONIKER pMkCtx, IUri *pUri, LPMONIKER *ppmk, DWORD dwFlags);")
cpp_quote("#endif")
cpp_quote("STDAPI CreateAsyncBindCtxEx(IBindCtx *pbc, DWORD dwOptions, IBindStatusCallback *pBSCb, IEnumFORMATETC *pEnum, IBindCtx **ppBC, DWORD reserved);")
cpp_quote("STDAPI MkParseDisplayNameEx(IBindCtx *pbc, LPCWSTR szDisplayName, ULONG *pchEaten, LPMONIKER *ppmk);")
cpp_quote("STDAPI RegisterBindStatusCallback(LPBC pBC, IBindStatusCallback *pBSCb, IBindStatusCallback **ppBSCBPrev, DWORD dwReserved);")
cpp_quote("STDAPI RevokeBindStatusCallback(LPBC pBC, IBindStatusCallback *pBSCb);")
cpp_quote("STDAPI GetClassFileOrMime(LPBC pBC, LPCWSTR szFilename, LPVOID pBuffer, DWORD cbSize, LPCWSTR szMime, DWORD dwReserved, CLSID *pclsid);")
cpp_quote("STDAPI IsValidURL(LPBC pBC, LPCWSTR szURL, DWORD dwReserved);")
cpp_quote("STDAPI CoGetClassObjectFromURL(REFCLSID rCLASSID, LPCWSTR szCODE, DWORD dwFileVersionMS, DWORD dwFileVersionLS, LPCWSTR szTYPE, LPBINDCTX pBindCtx, DWORD dwClsContext, LPVOID pvReserved, REFIID riid, LPVOID *ppv);")
cpp_quote("STDAPI IEInstallScope(LPDWORD pdwScope);")
cpp_quote("STDAPI FaultInIEFeature(HWND hWnd, uCLSSPEC *pClassSpec, QUERYCONTEXT *pQuery, DWORD dwFlags);")
cpp_quote("STDAPI GetComponentIDFromCLSSPEC(uCLSSPEC *pClassspec, LPSTR *ppszComponentID);")
cpp_quote("")
cpp_quote("#define FIEF_FLAG_FORCE_JITUI 0x1")
cpp_quote("#define FIEF_FLAG_PEEK 0x2")
cpp_quote("#define FIEF_FLAG_SKIP_INSTALLED_VERSION_CHECK 0x4")
cpp_quote("")
cpp_quote("STDAPI IsAsyncMoniker(IMoniker *pmk);")
cpp_quote("STDAPI CreateURLBinding(LPCWSTR lpszUrl, IBindCtx *pbc, IBinding **ppBdg);")
cpp_quote("STDAPI RegisterMediaTypes(UINT ctypes, const LPCSTR *rgszTypes, CLIPFORMAT *rgcfTypes);")
cpp_quote("STDAPI FindMediaType(LPCSTR rgszTypes, CLIPFORMAT *rgcfTypes);")
cpp_quote("STDAPI CreateFormatEnumerator(UINT cfmtetc, FORMATETC *rgfmtetc, IEnumFORMATETC **ppenumfmtetc);")
cpp_quote("STDAPI RegisterFormatEnumerator(LPBC pBC, IEnumFORMATETC *pEFetc, DWORD reserved);")
cpp_quote("STDAPI RevokeFormatEnumerator(LPBC pBC, IEnumFORMATETC *pEFetc);")
cpp_quote("STDAPI RegisterMediaTypeClass(LPBC pBC,UINT ctypes, const LPCSTR *rgszTypes, CLSID *rgclsID, DWORD reserved);")
cpp_quote("STDAPI FindMediaTypeClass(LPBC pBC, LPCSTR szType, CLSID *pclsID, DWORD reserved);")
cpp_quote("STDAPI UrlMkSetSessionOption(DWORD dwOption, LPVOID pBuffer, DWORD dwBufferLength, DWORD dwReserved);")
cpp_quote("STDAPI UrlMkGetSessionOption(DWORD dwOption, LPVOID pBuffer, DWORD dwBufferLength, DWORD *pdwBufferLengthOut, DWORD dwReserved);")
cpp_quote("STDAPI FindMimeFromData(LPBC pBC, LPCWSTR pwzUrl, LPVOID pBuffer, DWORD cbSize, LPCWSTR pwzMimeProposed, DWORD dwMimeFlags, LPWSTR *ppwzMimeOut, DWORD dwReserved);")

cpp_quote("")
cpp_quote("#define FMFD_DEFAULT 0x0")
cpp_quote("#define FMFD_URLASFILENAME 0x1")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#define FMFD_ENABLEMIMESNIFFING 0x2")
cpp_quote("#define FMFD_IGNOREMIMETEXTPLAIN 0x4")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#define FMFD_SERVERMIME 0x8")
cpp_quote("#define FMFD_RESPECTTEXTPLAIN 0x10")
cpp_quote("#define FMFD_RETURNUPDATEDIMGMIMES 0x20")
cpp_quote("#define UAS_EXACTLEGACY 0x1000")
cpp_quote("")
cpp_quote("STDAPI ObtainUserAgentString(DWORD dwOption, LPSTR pszUAOut, DWORD *cbSize);")
cpp_quote("STDAPI CompareSecurityIds(BYTE *pbSecurityId1, DWORD dwLen1, BYTE *pbSecurityId2, DWORD dwLen2, DWORD dwReserved);")
cpp_quote("STDAPI CompatFlagsFromClsid(CLSID *pclsid, LPDWORD pdwCompatFlags, LPDWORD pdwMiscStatusFlags);")

cpp_quote("")
cpp_quote("#define URLMON_OPTION_USERAGENT 0x10000001")
cpp_quote("#define URLMON_OPTION_USERAGENT_REFRESH 0x10000002")
cpp_quote("#define URLMON_OPTION_URL_ENCODING 0x10000004")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#define URLMON_OPTION_USE_BINDSTRINGCREDS 0x10000008")
cpp_quote("#endif")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#define URLMON_OPTION_USE_BROWSERAPPSDOCUMENTS 0x10000010")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
cpp_quote("#define CF_NULL                 0")
cpp_quote("#define CFSTR_MIME_NULL         NULL")
cpp_quote("")
cpp_quote("#define CFSTR_MIME_TEXT (TEXT(\"text/plain\"))")
cpp_quote("#define CFSTR_MIME_RICHTEXT (TEXT(\"text/richtext\"))")
cpp_quote("#define CFSTR_MIME_MANIFEST (TEXT(\"text/cache-manifest\"))")
cpp_quote("#define CFSTR_MIME_WEBVTT (TEXT(\"text/vtt\"))")
cpp_quote("#define CFSTR_MIME_X_BITMAP (TEXT(\"image/x-xbitmap\"))")
cpp_quote("#define CFSTR_MIME_POSTSCRIPT (TEXT(\"application/postscript\"))")

cpp_quote("#define CFSTR_MIME_AIFF (TEXT(\"audio/aiff\"))")
cpp_quote("#define CFSTR_MIME_BASICAUDIO (TEXT(\"audio/basic\"))")
cpp_quote("#define CFSTR_MIME_WAV (TEXT(\"audio/wav\"))")
cpp_quote("#define CFSTR_MIME_X_WAV (TEXT(\"audio/x-wav\"))")
cpp_quote("#define CFSTR_MIME_GIF (TEXT(\"image/gif\"))")
cpp_quote("#define CFSTR_MIME_PJPEG (TEXT(\"image/pjpeg\"))")
cpp_quote("#define CFSTR_MIME_JPEG (TEXT(\"image/jpeg\"))")
cpp_quote("#define CFSTR_MIME_TIFF (TEXT(\"image/tiff\"))")
cpp_quote("#define CFSTR_MIME_JPEG_XR (TEXT(\"image/vnd.ms-photo\"))")
cpp_quote("#define CFSTR_MIME_PNG (TEXT(\"image/png\"))")
cpp_quote("#define CFSTR_MIME_X_PNG (TEXT(\"image/x-png\"))")
cpp_quote("#define CFSTR_MIME_X_ICON (TEXT(\"image/x-icon\"))")
cpp_quote("#define CFSTR_MIME_SVG_XML (TEXT(\"image/svg+xml\"))")
cpp_quote("#define CFSTR_MIME_BMP (TEXT(\"image/bmp\"))")
cpp_quote("#define CFSTR_MIME_X_EMF (TEXT(\"image/x-emf\"))")
cpp_quote("#define CFSTR_MIME_X_WMF (TEXT(\"image/x-wmf\"))")
cpp_quote("#define CFSTR_MIME_AVI (TEXT(\"video/avi\"))")
cpp_quote("#define CFSTR_MIME_MPEG (TEXT(\"video/mpeg\"))")
cpp_quote("#define CFSTR_MIME_FRACTALS (TEXT(\"application/fractals\"))")
cpp_quote("#define CFSTR_MIME_RAWDATA (TEXT(\"application/octet-stream\"))")
cpp_quote("#define CFSTR_MIME_RAWDATASTRM (TEXT(\"application/octet-stream\"))")
cpp_quote("#define CFSTR_MIME_PDF (TEXT(\"application/pdf\"))")
cpp_quote("#define CFSTR_MIME_HTA (TEXT(\"application/hta\"))")
cpp_quote("#define CFSTR_MIME_APP_XML (TEXT(\"application/xml\"))")
cpp_quote("#define CFSTR_MIME_XHTML (TEXT(\"application/xhtml+xml\"))")
cpp_quote("#define CFSTR_MIME_X_AIFF (TEXT(\"audio/x-aiff\"))")
cpp_quote("#define CFSTR_MIME_X_REALAUDIO (TEXT(\"audio/x-pn-realaudio\"))")
cpp_quote("#define CFSTR_MIME_XBM (TEXT(\"image/xbm\"))")
cpp_quote("#define CFSTR_MIME_QUICKTIME (TEXT(\"video/quicktime\"))")
cpp_quote("#define CFSTR_MIME_X_MSVIDEO (TEXT(\"video/x-msvideo\"))")
cpp_quote("#define CFSTR_MIME_X_SGI_MOVIE (TEXT(\"video/x-sgi-movie\"))")
cpp_quote("#define CFSTR_MIME_HTML (TEXT(\"text/html\"))")
cpp_quote("#define CFSTR_MIME_XML (TEXT(\"text/xml\"))")
cpp_quote("#define CFSTR_MIME_TTML (TEXT(\"application/ttml+xml\"))")
cpp_quote("#define CFSTR_MIME_TTAF (TEXT(\"application/ttaf+xml\"))")
cpp_quote("")
cpp_quote("#define MK_S_ASYNCHRONOUS    _HRESULT_TYPEDEF_(0x401E8L)")
cpp_quote("#ifndef S_ASYNCHRONOUS")
cpp_quote("#define S_ASYNCHRONOUS MK_S_ASYNCHRONOUS")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef E_PENDING")
cpp_quote("#define E_PENDING _HRESULT_TYPEDEF_(__MSABI_LONG(0x8000000a))")
cpp_quote("#endif")
cpp_quote("#define INET_E_INVALID_URL _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0002))")
cpp_quote("#define INET_E_NO_SESSION _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0003))")
cpp_quote("#define INET_E_CANNOT_CONNECT            _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0004))")
cpp_quote("#define INET_E_RESOURCE_NOT_FOUND        _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0005))")
cpp_quote("#define INET_E_OBJECT_NOT_FOUND          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0006))")
cpp_quote("#define INET_E_DATA_NOT_AVAILABLE        _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0007))")
cpp_quote("#define INET_E_DOWNLOAD_FAILURE          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0008))")
cpp_quote("#define INET_E_AUTHENTICATION_REQUIRED   _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0009))")
cpp_quote("#define INET_E_NO_VALID_MEDIA            _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000A))")
cpp_quote("#define INET_E_CONNECTION_TIMEOUT        _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000B))")
cpp_quote("#define INET_E_INVALID_REQUEST           _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000C))")
cpp_quote("#define INET_E_UNKNOWN_PROTOCOL          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000D))")
cpp_quote("#define INET_E_SECURITY_PROBLEM          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000E))")
cpp_quote("#define INET_E_CANNOT_LOAD_DATA          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000F))")
cpp_quote("#define INET_E_CANNOT_INSTANTIATE_OBJECT _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0010))")
cpp_quote("#define INET_E_INVALID_CERTIFICATE       _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0019))")
cpp_quote("#define INET_E_REDIRECT_FAILED           _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0014))")
cpp_quote("#define INET_E_REDIRECT_TO_DIR           _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0015))")
cpp_quote("#define INET_E_CANNOT_LOCK_REQUEST                   _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0016))")
cpp_quote("#define INET_E_USE_EXTEND_BINDING                    _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0017))")
cpp_quote("#define INET_E_TERMINATED_BIND                       _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0018))")
cpp_quote("#define INET_E_RESERVED_1                            _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C001A))")
cpp_quote("#define INET_E_BLOCKED_REDIRECT_XSECURITYID          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C001B))")
cpp_quote("#define INET_E_DOMINJECTIONVALIDATION                _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C001C))")
cpp_quote("#define INET_E_ERROR_FIRST                           _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0002))")
cpp_quote("#define INET_E_CODE_DOWNLOAD_DECLINED                _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0100))")
cpp_quote("#define INET_E_RESULT_DISPATCHED                     _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0200))")
cpp_quote("#define INET_E_CANNOT_REPLACE_SFP_FILE               _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0300))")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#define INET_E_CODE_INSTALL_SUPPRESSED               _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0400))")
cpp_quote("#endif")
cpp_quote("#define INET_E_CODE_INSTALL_BLOCKED_BY_HASH_POLICY   _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0500))")
cpp_quote("#define INET_E_DOWNLOAD_BLOCKED_BY_INPRIVATE         _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0501))")
cpp_quote("#define INET_E_CODE_INSTALL_BLOCKED_IMMERSIVE        _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0502))")
cpp_quote("#define INET_E_FORBIDFRAMING                         _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0503))")
cpp_quote("#define INET_E_CODE_INSTALL_BLOCKED_ARM              _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0504))")
cpp_quote("#define INET_E_BLOCKED_PLUGGABLE_PROTOCOL            _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0505))")
cpp_quote("#define INET_E_ERROR_LAST INET_E_BLOCKED_PLUGGABLE_PROTOCOL")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("#ifndef _LPPERSISTMONIKER_DEFINED")
cpp_quote("#define _LPPERSISTMONIKER_DEFINED")
[object, uuid (79eac9c9-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IPersistMoniker : IUnknown {
  typedef [unique] IPersistMoniker *LPPERSISTMONIKER;
cpp_quote("")
  HRESULT GetClassID ([out] CLSID *pClassID);
  HRESULT IsDirty (void);
  HRESULT Load ([in] BOOL fFullyAvailable,[in] IMoniker *pimkName,[in] LPBC pibc,[in] DWORD grfMode);
  HRESULT Save ([in] IMoniker *pimkName,[in] LPBC pbc,[in] BOOL fRemember);
  HRESULT SaveCompleted ([in] IMoniker *pimkName,[in] LPBC pibc);
  HRESULT GetCurMoniker ([out] IMoniker **ppimkName);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef _LPMONIKERPROP_DEFINED")
cpp_quote("#define _LPMONIKERPROP_DEFINED")
[object, uuid (a5ca5f7f-1847-4d87-9c5b-918509f7511d), pointer_default (unique)]
interface IMonikerProp : IUnknown {
  typedef [unique] IMonikerProp *LPMONIKERPROP;
cpp_quote("")
  typedef enum {
    MIMETYPEPROP = 0x0,
    USE_SRC_URL = 0x1,
    CLASSIDPROP = 0x2,
    TRUSTEDDOWNLOADPROP = 0x3,
    POPUPLEVELPROP = 0x4,
  } MONIKERPROPERTY;
cpp_quote("")
  HRESULT PutProperty ([in] MONIKERPROPERTY mkp,[in] LPCWSTR val);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPBINDPROTOCOL_DEFINED")
cpp_quote("#define _LPBINDPROTOCOL_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9cd-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IBindProtocol : IUnknown {
  typedef [unique] IBindProtocol *LPBINDPROTOCOL;
cpp_quote("")
  HRESULT CreateBinding ([in] LPCWSTR szUrl,[in] IBindCtx *pbc,[out] IBinding **ppb);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPBINDING_DEFINED")
cpp_quote("#define _LPBINDING_DEFINED")
cpp_quote("")
[object, uuid (79eac9c0-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IBinding: IUnknown {
  typedef [unique] IBinding *LPBINDING;
cpp_quote("")
  HRESULT Abort ();
  HRESULT Suspend ();
  HRESULT Resume ();
  HRESULT SetPriority ([in] LONG nPriority);
  HRESULT GetPriority ([out] LONG *pnPriority);
  [local] HRESULT GetBindResult ([out] CLSID *pclsidProtocol,[out] DWORD *pdwResult,[out] LPOLESTR *pszResult,[in, out] DWORD *pdwReserved);
  [call_as (GetBindResult)] HRESULT RemoteGetBindResult ([out] CLSID *pclsidProtocol,[out] DWORD *pdwResult,[out] LPOLESTR *pszResult,[in] DWORD dwReserved);
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
cpp_quote("#ifndef _LPBINDSTATUSCALLBACK_DEFINED")
cpp_quote("#define _LPBINDSTATUSCALLBACK_DEFINED")
cpp_quote("")
[object, uuid (79eac9c1-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IBindStatusCallback: IUnknown {
  typedef [unique] IBindStatusCallback *LPBINDSTATUSCALLBACK;
cpp_quote("")
  typedef enum {
    BINDVERB_GET = 0x0,
    BINDVERB_POST = 0x1,
    BINDVERB_PUT = 0x2,
    BINDVERB_CUSTOM = 0x3,
    BINDVERB_RESERVED1 = 0x4,
  } BINDVERB;
cpp_quote("")
  typedef enum {
    BINDINFOF_URLENCODESTGMEDDATA = 0x1,
    BINDINFOF_URLENCODEDEXTRAINFO = 0x2,
  } BINDINFOF;
cpp_quote("")
  typedef enum {
    BINDF_ASYNCHRONOUS = 0x1,
    BINDF_ASYNCSTORAGE = 0x2,
    BINDF_NOPROGRESSIVERENDERING = 0x4,
    BINDF_OFFLINEOPERATION = 0x8,
    BINDF_GETNEWESTVERSION = 0x10,
    BINDF_NOWRITECACHE = 0x20,
    BINDF_NEEDFILE = 0x40,
    BINDF_PULLDATA = 0x80,
    BINDF_IGNORESECURITYPROBLEM = 0x100,
    BINDF_RESYNCHRONIZE = 0x200,
    BINDF_HYPERLINK = 0x400,
    BINDF_NO_UI = 0x800,
    BINDF_SILENTOPERATION = 0x1000,
    BINDF_PRAGMA_NO_CACHE = 0x2000,
    BINDF_GETCLASSOBJECT = 0x4000,
    BINDF_RESERVED_1 = 0x8000,
    BINDF_FREE_THREADED = 0x10000,
    BINDF_DIRECT_READ = 0x20000,
    BINDF_FORMS_SUBMIT = 0x40000,
    BINDF_GETFROMCACHE_IF_NET_FAIL = 0x80000,
    BINDF_FROMURLMON = 0x100000,
    BINDF_FWD_BACK = 0x200000,
    BINDF_PREFERDEFAULTHANDLER = 0x400000,
    BINDF_ENFORCERESTRICTED = 0x800000,
    BINDF_RESERVED_2 = 0x80000000,
    BINDF_RESERVED_3 = 0x1000000,
    BINDF_RESERVED_4 = 0x2000000,
    BINDF_RESERVED_5 = 0x4000000,
    BINDF_RESERVED_6 = 0x8000000,
    BINDF_RESERVED_7 = 0x40000000,
    BINDF_RESERVED_8 = 0x20000000
  } BINDF;

cpp_quote("")
  typedef enum {
    URL_ENCODING_NONE = 0x0,
    URL_ENCODING_ENABLE_UTF8 = 0x10000000,
    URL_ENCODING_DISABLE_UTF8 = 0x20000000
  } URL_ENCODING;

cpp_quote("")
  typedef struct _tagBINDINFO {
    ULONG cbSize;
    LPWSTR szExtraInfo;
    STGMEDIUM stgmedData;
    DWORD grfBindInfoF;
    DWORD dwBindVerb;
    LPWSTR szCustomVerb;
    DWORD cbstgmedData;
    DWORD dwOptions;
    DWORD dwOptionsFlags;
    DWORD dwCodePage;
    SECURITY_ATTRIBUTES securityAttributes;
    IID iid;
    IUnknown *pUnk;
    DWORD dwReserved;
  } BINDINFO;

cpp_quote("")
  typedef struct _REMSECURITY_ATTRIBUTES {
    DWORD nLength;
    DWORD lpSecurityDescriptor;
    BOOL bInheritHandle;
  } REMSECURITY_ATTRIBUTES,*PREMSECURITY_ATTRIBUTES,*LPREMSECURITY_ATTRIBUTES;

cpp_quote("")
  typedef struct _tagRemBINDINFO {
    ULONG cbSize;
    LPWSTR szExtraInfo;
    DWORD grfBindInfoF;
    DWORD dwBindVerb;
    LPWSTR szCustomVerb;
    DWORD cbstgmedData;
    DWORD dwOptions;
    DWORD dwOptionsFlags;
    DWORD dwCodePage;
    REMSECURITY_ATTRIBUTES securityAttributes;
    IID iid;
    IUnknown *pUnk;
    DWORD dwReserved;
  } RemBINDINFO;

cpp_quote("")
  typedef struct tagRemFORMATETC {
    DWORD cfFormat;
    DWORD ptd;
    DWORD dwAspect;
    LONG lindex;
    DWORD tymed;
  } RemFORMATETC,*LPREMFORMATETC;

cpp_quote("")
  typedef enum {
    BINDINFO_OPTIONS_WININETFLAG = 0x10000,
    BINDINFO_OPTIONS_ENABLE_UTF8 = 0x20000,
    BINDINFO_OPTIONS_DISABLE_UTF8 = 0x40000,
    BINDINFO_OPTIONS_USE_IE_ENCODING = 0x80000,
    BINDINFO_OPTIONS_BINDTOOBJECT = 0x100000,
    BINDINFO_OPTIONS_SECURITYOPTOUT = 0x200000,
    BINDINFO_OPTIONS_IGNOREMIMETEXTPLAIN = 0x400000,
    BINDINFO_OPTIONS_USEBINDSTRINGCREDS = 0x800000,
    BINDINFO_OPTIONS_IGNOREHTTPHTTPSREDIRECTS = 0x1000000,
    BINDINFO_OPTIONS_IGNORE_SSLERRORS_ONCE = 0x2000000,
    BINDINFO_WPC_DOWNLOADBLOCKED = 0x8000000,
    BINDINFO_WPC_LOGGING_ENABLED = 0x10000000,
    BINDINFO_OPTIONS_ALLOWCONNECTDATA = 0x20000000,
    BINDINFO_OPTIONS_DISABLEAUTOREDIRECTS = 0x40000000,
    BINDINFO_OPTIONS_SHDOCVW_NAVIGATE = (int) 0x80000000
  } BINDINFO_OPTIONS;

cpp_quote("")
  typedef enum {
    BSCF_FIRSTDATANOTIFICATION = 0x1,
    BSCF_INTERMEDIATEDATANOTIFICATION = 0x2,
    BSCF_LASTDATANOTIFICATION = 0x4,
    BSCF_DATAFULLYAVAILABLE = 0x8,
    BSCF_AVAILABLEDATASIZEUNKNOWN = 0x10,
    BSCF_SKIPDRAINDATAFORFILEURLS = 0x20,
    BSCF_64BITLENGTHDOWNLOAD = 0x40
  } BSCF;

cpp_quote("")
  typedef enum tagBINDSTATUS {
    BINDSTATUS_FINDINGRESOURCE = 1,
    BINDSTATUS_CONNECTING,
    BINDSTATUS_REDIRECTING,
    BINDSTATUS_BEGINDOWNLOADDATA,
    BINDSTATUS_DOWNLOADINGDATA,
    BINDSTATUS_ENDDOWNLOADDATA,
    BINDSTATUS_BEGINDOWNLOADCOMPONENTS,
    BINDSTATUS_INSTALLINGCOMPONENTS,
    BINDSTATUS_ENDDOWNLOADCOMPONENTS,
    BINDSTATUS_USINGCACHEDCOPY,
    BINDSTATUS_SENDINGREQUEST,
    BINDSTATUS_CLASSIDAVAILABLE,
    BINDSTATUS_MIMETYPEAVAILABLE,
    BINDSTATUS_CACHEFILENAMEAVAILABLE,
    BINDSTATUS_BEGINSYNCOPERATION,
    BINDSTATUS_ENDSYNCOPERATION,
    BINDSTATUS_BEGINUPLOADDATA,
    BINDSTATUS_UPLOADINGDATA,
    BINDSTATUS_ENDUPLOADDATA,
    BINDSTATUS_PROTOCOLCLASSID,
    BINDSTATUS_ENCODING,
    BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE,
    BINDSTATUS_CLASSINSTALLLOCATION,
    BINDSTATUS_DECODING,
    BINDSTATUS_LOADINGMIMEHANDLER,
    BINDSTATUS_CONTENTDISPOSITIONATTACH,
    BINDSTATUS_FILTERREPORTMIMETYPE,
    BINDSTATUS_CLSIDCANINSTANTIATE,
    BINDSTATUS_IUNKNOWNAVAILABLE,
    BINDSTATUS_DIRECTBIND,
    BINDSTATUS_RAWMIMETYPE,
    BINDSTATUS_PROXYDETECTING,
    BINDSTATUS_ACCEPTRANGES,
    BINDSTATUS_COOKIE_SENT,
    BINDSTATUS_COMPACT_POLICY_RECEIVED,
    BINDSTATUS_COOKIE_SUPPRESSED,
    BINDSTATUS_COOKIE_STATE_UNKNOWN,
    BINDSTATUS_COOKIE_STATE_ACCEPT,
    BINDSTATUS_COOKIE_STATE_REJECT,
    BINDSTATUS_COOKIE_STATE_PROMPT,
    BINDSTATUS_COOKIE_STATE_LEASH,
    BINDSTATUS_COOKIE_STATE_DOWNGRADE,
    BINDSTATUS_POLICY_HREF,
    BINDSTATUS_P3P_HEADER,
    BINDSTATUS_SESSION_COOKIE_RECEIVED,
    BINDSTATUS_PERSISTENT_COOKIE_RECEIVED,
    BINDSTATUS_SESSION_COOKIES_ALLOWED,
    BINDSTATUS_CACHECONTROL,
    BINDSTATUS_CONTENTDISPOSITIONFILENAME,
    BINDSTATUS_MIMETEXTPLAINMISMATCH,
    BINDSTATUS_PUBLISHERAVAILABLE,
    BINDSTATUS_DISPLAYNAMEAVAILABLE,
    BINDSTATUS_SSLUX_NAVBLOCKED,
    BINDSTATUS_SERVER_MIMETYPEAVAILABLE,
    BINDSTATUS_SNIFFED_CLASSIDAVAILABLE,
    BINDSTATUS_64BIT_PROGRESS,
    BINDSTATUS_LAST = BINDSTATUS_64BIT_PROGRESS,
    BINDSTATUS_RESERVED_0,
    BINDSTATUS_RESERVED_1,
    BINDSTATUS_RESERVED_2,
    BINDSTATUS_RESERVED_3,
    BINDSTATUS_RESERVED_4,
    BINDSTATUS_RESERVED_5,
    BINDSTATUS_RESERVED_6,
    BINDSTATUS_RESERVED_7,
    BINDSTATUS_RESERVED_8,
    BINDSTATUS_RESERVED_9,
    BINDSTATUS_LAST_PRIVATE = BINDSTATUS_RESERVED_9
  } BINDSTATUS;
cpp_quote("")
  HRESULT OnStartBinding ([in] DWORD dwReserved,[in] IBinding *pib);
  HRESULT GetPriority ([out] LONG *pnPriority);
  HRESULT OnLowResource ([in] DWORD reserved);
  HRESULT OnProgress ([in] ULONG ulProgress,[in] ULONG ulProgressMax,[in] ULONG ulStatusCode,[in, unique] LPCWSTR szStatusText);
  HRESULT OnStopBinding ([in] HRESULT hresult,[in, unique] LPCWSTR szError);
  [local] HRESULT GetBindInfo ([out] DWORD *grfBINDF,[in, out, unique] BINDINFO *pbindinfo);
  [call_as (GetBindInfo)] HRESULT RemoteGetBindInfo ([out] DWORD *grfBINDF,[in, out, unique] RemBINDINFO *pbindinfo,[in, out, unique] RemSTGMEDIUM *pstgmed);
  [local] HRESULT OnDataAvailable ([in] DWORD grfBSCF,[in] DWORD dwSize,[in] FORMATETC *pformatetc,[in] STGMEDIUM *pstgmed);
  [call_as (OnDataAvailable)] HRESULT RemoteOnDataAvailable ([in] DWORD grfBSCF,[in] DWORD dwSize,[in] RemFORMATETC *pformatetc,[in] RemSTGMEDIUM *pstgmed);
  HRESULT OnObjectAvailable ([in] REFIID riid,[in, iid_is (riid)] IUnknown *punk);
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("#ifndef _LPBINDSTATUSCALLBACKEX_DEFINED")
cpp_quote("#define _LPBINDSTATUSCALLBACKEX_DEFINED")
cpp_quote("")
[object, uuid (aaa74ef9-8ee7-4659-88d9-f8c504da73cc), pointer_default (unique)]
interface IBindStatusCallbackEx: IBindStatusCallback {
  typedef [unique] IBindStatusCallbackEx *LPBINDSTATUSCALLBACKEX;
cpp_quote("")
  typedef enum {
    BINDF2_DISABLEBASICOVERHTTP = 0x1,
    BINDF2_DISABLEAUTOCOOKIEHANDLING = 0x2,
    BINDF2_READ_DATA_GREATER_THAN_4GB = 0x4,
    BINDF2_DISABLE_HTTP_REDIRECT_XSECURITYID = 0x8,
    BINDF2_SETDOWNLOADMODE = 0x20,
    BINDF2_DISABLE_HTTP_REDIRECT_CACHING = 0x40,
    BINDF2_KEEP_CALLBACK_MODULE_LOADED = 0x80,
    BINDF2_ALLOW_PROXY_CRED_PROMPT = 0x100,
    BINDF2_RESERVED_F = 0x20000,
    BINDF2_RESERVED_E = 0x40000,
    BINDF2_RESERVED_D = 0x80000,
    BINDF2_RESERVED_C = 0x100000,
    BINDF2_RESERVED_B = 0x200000,
    BINDF2_RESERVED_A = 0x400000,
    BINDF2_RESERVED_9 = 0x800000,
    BINDF2_RESERVED_8 = 0x1000000,
    BINDF2_RESERVED_7 = 0x2000000,
    BINDF2_RESERVED_6 = 0x4000000,
    BINDF2_RESERVED_5 = 0x8000000,
    BINDF2_RESERVED_4 = 0x10000000,
    BINDF2_RESERVED_3 = 0x20000000,
    BINDF2_RESERVED_2 = 0x40000000,
    BINDF2_RESERVED_1 = 0x80000000,
  } BINDF2;
cpp_quote("")
  [local] HRESULT GetBindInfoEx ([out] DWORD *grfBINDF,[in, out, unique] BINDINFO *pbindinfo,[out] DWORD *grfBINDF2,[out] DWORD *pdwReserved);
  [call_as (GetBindInfoEx)] HRESULT RemoteGetBindInfoEx ([out] DWORD *grfBINDF,[in, out, unique] RemBINDINFO *pbindinfo,[in, out, unique] RemSTGMEDIUM *pstgmed,[out] DWORD *grfBINDF2,[out] DWORD *pdwReserved);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPAUTHENTICATION_DEFINED")
cpp_quote("#define _LPAUTHENTICATION_DEFINED")
cpp_quote("")
[object, uuid (79eac9d0-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IAuthenticate : IUnknown {
  typedef [unique] IAuthenticate *LPAUTHENTICATION;
cpp_quote("")
  HRESULT Authenticate ([out] HWND *phwnd,[out] LPWSTR *pszUsername,[out] LPWSTR *pszPassword);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPAUTHENTICATIONEX_DEFINED")
cpp_quote("#define _LPAUTHENTICATIONEX_DEFINED")
cpp_quote("")
[object, uuid (2ad1edaf-d83d-48b5-9adf-03dbe19f53bd), pointer_default (unique)]
interface IAuthenticateEx : IAuthenticate {
  typedef [unique] IAuthenticateEx *LPAUTHENTICATIONEX;
cpp_quote("")
  typedef enum {
    AUTHENTICATEF_PROXY = 0x1,
    AUTHENTICATEF_BASIC = 0x2,
    AUTHENTICATEF_HTTP = 0x4,
  } AUTHENTICATEF;
cpp_quote("")
  typedef struct _tagAUTHENTICATEINFO {
    DWORD dwFlags;
    DWORD dwReserved;
  } AUTHENTICATEINFO;
cpp_quote("")
  HRESULT AuthenticateEx ([out] HWND *phwnd,[out] LPWSTR *pszUsername,[out] LPWSTR *pszPassword,[in] AUTHENTICATEINFO *pauthinfo);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPHTTPNEGOTIATE_DEFINED")
cpp_quote("#define _LPHTTPNEGOTIATE_DEFINED")
cpp_quote("")
[object, uuid (79eac9d2-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IHttpNegotiate : IUnknown {
  typedef [unique] IHttpNegotiate *LPHTTPNEGOTIATE;
cpp_quote("")
  HRESULT BeginningTransaction ([in] LPCWSTR szURL,[in, unique] LPCWSTR szHeaders,[in] DWORD dwReserved,[out] LPWSTR *pszAdditionalHeaders);
  HRESULT OnResponse ([in] DWORD dwResponseCode,[in, unique] LPCWSTR szResponseHeaders,[in, unique] LPCWSTR szRequestHeaders,[out] LPWSTR *pszAdditionalRequestHeaders);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPHTTPNEGOTIATE2_DEFINED")
cpp_quote("#define _LPHTTPNEGOTIATE2_DEFINED")
cpp_quote("")
[object, uuid (4f9f9fcb-E0F4-48eb-B7AB-FA2EA9365CB4), pointer_default (unique)]
interface IHttpNegotiate2 : IHttpNegotiate {
  typedef [unique] IHttpNegotiate2 *LPHTTPNEGOTIATE2;
cpp_quote("")
  HRESULT GetRootSecurityId ([out, size_is (*pcbSecurityId)]BYTE *pbSecurityId,[in, out]DWORD *pcbSecurityId,[in] DWORD_PTR dwReserved);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPHTTPNEGOTIATE3_DEFINED")
cpp_quote("#define _LPHTTPNEGOTIATE3_DEFINED")
cpp_quote("")
[object, uuid (57b6c80a-34c2-4602-bc26-66a02fc57153), pointer_default (unique)]
interface IHttpNegotiate3 : IHttpNegotiate2 {
  typedef [unique] IHttpNegotiate3 *LPHTTPNEGOTIATE3;
cpp_quote("")
  HRESULT GetSerializedClientCertContext ([out, size_is (,*pcbCert)]BYTE **ppbCert,[out] DWORD *pcbCert);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPWININETFILESTREAM_DEFINED")
cpp_quote("#define _LPWININETFILESTREAM_DEFINED")
cpp_quote("")
[object, uuid (F134C4B7-B1F8-4e75-B886-74b90943becb), pointer_default (unique)]
interface IWinInetFileStream : IUnknown {
  typedef [unique] IWinInetFileStream *LPWININETFILESTREAM;
cpp_quote("")
  HRESULT SetHandleForUnlock ([in] DWORD_PTR hWinInetLockHandle,[in] DWORD_PTR dwReserved);
  HRESULT SetDeleteFile ([in] DWORD_PTR dwReserved);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPWINDOWFORBINDINGUI_DEFINED")
cpp_quote("#define _LPWINDOWFORBINDINGUI_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9d5-bafa-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IWindowForBindingUI : IUnknown {
  typedef [unique] IWindowForBindingUI *LPWINDOWFORBINDINGUI;
cpp_quote("")
  HRESULT GetWindow ([in] REFGUID rguidReason,[out] HWND *phwnd);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPCODEINSTALL_DEFINED")
cpp_quote("#define _LPCODEINSTALL_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9d1-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface ICodeInstall : IWindowForBindingUI {
  typedef [unique] ICodeInstall *LPCODEINSTALL;
cpp_quote("")
  typedef enum {
    CIP_DISK_FULL,
    CIP_ACCESS_DENIED,
    CIP_NEWER_VERSION_EXISTS,
    CIP_OLDER_VERSION_EXISTS,
    CIP_NAME_CONFLICT,
    CIP_TRUST_VERIFICATION_COMPONENT_MISSING,
    CIP_EXE_SELF_REGISTERATION_TIMEOUT,
    CIP_UNSAFE_TO_ABORT,
    CIP_NEED_REBOOT,
    CIP_NEED_REBOOT_UI_PERMISSION
  } CIP_STATUS;
cpp_quote("")
  HRESULT OnCodeInstallProblem ([in] ULONG ulStatusCode,[in, unique] LPCWSTR szDestination,[in, unique] LPCWSTR szSource,[in] DWORD dwReserved);
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#ifndef _LPUri_DEFINED")
cpp_quote("#define _LPUri_DEFINED")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (A39EE748-6a27-4817-A6F2-13914bef5890), pointer_default (unique)]
interface IUri : IUnknown {
  typedef enum {
    Uri_PROPERTY_ABSOLUTE_URI = 0,
    Uri_PROPERTY_STRING_START = Uri_PROPERTY_ABSOLUTE_URI,
    Uri_PROPERTY_AUTHORITY = 1,
    Uri_PROPERTY_DISPLAY_URI = 2,
    Uri_PROPERTY_DOMAIN = 3,
    Uri_PROPERTY_EXTENSION = 4,
    Uri_PROPERTY_FRAGMENT = 5,
    Uri_PROPERTY_HOST = 6,
    Uri_PROPERTY_PASSWORD = 7,
    Uri_PROPERTY_PATH = 8,
    Uri_PROPERTY_PATH_AND_QUERY = 9,
    Uri_PROPERTY_QUERY = 10,
    Uri_PROPERTY_RAW_URI = 11,
    Uri_PROPERTY_SCHEME_NAME = 12,
    Uri_PROPERTY_USER_INFO = 13,
    Uri_PROPERTY_USER_NAME = 14,
    Uri_PROPERTY_STRING_LAST = Uri_PROPERTY_USER_NAME,

    Uri_PROPERTY_HOST_TYPE = 15,
    Uri_PROPERTY_DWORD_START = Uri_PROPERTY_HOST_TYPE,
    Uri_PROPERTY_PORT = 16,
    Uri_PROPERTY_SCHEME = 17,
    Uri_PROPERTY_ZONE = 18,
    Uri_PROPERTY_DWORD_LAST = Uri_PROPERTY_ZONE,
  } Uri_PROPERTY;
cpp_quote("")
  typedef enum {
    Uri_HOST_UNKNOWN,
    Uri_HOST_DNS,
    Uri_HOST_IPV4,
    Uri_HOST_IPV6,
    Uri_HOST_IDN,
  } Uri_HOST_TYPE;
cpp_quote("")
  HRESULT GetPropertyBSTR ([in, range (Uri_PROPERTY_STRING_START, Uri_PROPERTY_STRING_LAST)] Uri_PROPERTY uriProp,[out] BSTR *pbstrProperty,[in] DWORD dwFlags);
  HRESULT GetPropertyLength ([in, range (Uri_PROPERTY_STRING_START, Uri_PROPERTY_STRING_LAST)] Uri_PROPERTY uriProp,[out] DWORD *pcchProperty,[in] DWORD dwFlags);
  HRESULT GetPropertyDWORD ([in, range (Uri_PROPERTY_DWORD_START, Uri_PROPERTY_DWORD_LAST)] Uri_PROPERTY uriProp,[out] DWORD *pdwProperty,[in] DWORD dwFlags);
  HRESULT HasProperty ([in, range (Uri_PROPERTY_STRING_START, Uri_PROPERTY_DWORD_LAST)] Uri_PROPERTY uriProp,[out] BOOL *pfHasProperty);
  HRESULT GetAbsoluteUri ([out] BSTR *pbstrAbsoluteUri);
  HRESULT GetAuthority ([out] BSTR *pbstrAuthority);
  HRESULT GetDisplayUri ([out] BSTR *pbstrDisplayString);
  HRESULT GetDomain ([out] BSTR *pbstrDomain);
  HRESULT GetExtension ([out] BSTR *pbstrExtension);
  HRESULT GetFragment ([out] BSTR *pbstrFragment);
  HRESULT GetHost ([out] BSTR *pbstrHost);
  HRESULT GetPassword ([out] BSTR *pbstrPassword);
  HRESULT GetPath ([out] BSTR *pbstrPath);
  HRESULT GetPathAndQuery ([out] BSTR *pbstrPathAndQuery);
  HRESULT GetQuery ([out] BSTR *pbstrQuery);
  HRESULT GetRawUri ([out] BSTR *pbstrRawUri);
  HRESULT GetSchemeName ([out] BSTR *pbstrSchemeName);
  HRESULT GetUserInfo ([out] BSTR *pbstrUserInfo);
  HRESULT GetUserName ([out] BSTR *pbstrUserName);
  HRESULT GetHostType ([out] DWORD *pdwHostType);
  HRESULT GetPort ([out] DWORD *pdwPort);
  HRESULT GetScheme ([out] DWORD *pdwScheme);
  HRESULT GetZone ([out] DWORD *pdwZone);
  HRESULT GetProperties ([out] LPDWORD pdwFlags);
  HRESULT IsEqual ([in] IUri *pUri,[out] BOOL *pfEqual);
}

cpp_quote("")
cpp_quote("STDAPI CreateUri(LPCWSTR pwzURI, DWORD dwFlags, DWORD_PTR dwReserved, IUri **ppURI);")
cpp_quote("STDAPI CreateUriWithFragment(")
cpp_quote("LPCWSTR pwzURI, LPCWSTR pwzFragment, DWORD dwFlags, DWORD_PTR dwReserved, IUri **ppURI);")
cpp_quote("#endif")

cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("STDAPI CreateUriFromMultiByteString(LPCSTR pszANSIInputUri, DWORD dwEncodingFlags, DWORD dwCodePage, DWORD dwCreateFlags, DWORD_PTR dwReserved, IUri **ppUri);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
cpp_quote("#define Uri_HAS_ABSOLUTE_URI (1 << Uri_PROPERTY_ABSOLUTE_URI)")
cpp_quote("#define Uri_HAS_AUTHORITY (1 << Uri_PROPERTY_AUTHORITY)")
cpp_quote("#define Uri_HAS_DISPLAY_URI (1 << Uri_PROPERTY_DISPLAY_URI)")
cpp_quote("#define Uri_HAS_DOMAIN (1 << Uri_PROPERTY_DOMAIN)")
cpp_quote("#define Uri_HAS_EXTENSION (1 << Uri_PROPERTY_EXTENSION)")
cpp_quote("#define Uri_HAS_FRAGMENT (1 << Uri_PROPERTY_FRAGMENT)")
cpp_quote("#define Uri_HAS_HOST (1 << Uri_PROPERTY_HOST)")
cpp_quote("#define Uri_HAS_PASSWORD (1 << Uri_PROPERTY_PASSWORD)")
cpp_quote("#define Uri_HAS_PATH (1 << Uri_PROPERTY_PATH)")
cpp_quote("#define Uri_HAS_QUERY (1 << Uri_PROPERTY_QUERY)")
cpp_quote("#define Uri_HAS_RAW_URI (1 << Uri_PROPERTY_RAW_URI)")
cpp_quote("#define Uri_HAS_SCHEME_NAME (1 << Uri_PROPERTY_SCHEME_NAME)")
cpp_quote("#define Uri_HAS_USER_NAME (1 << Uri_PROPERTY_USER_NAME)")
cpp_quote("#define Uri_HAS_PATH_AND_QUERY (1 << Uri_PROPERTY_PATH_AND_QUERY)")
cpp_quote("#define Uri_HAS_USER_INFO (1 << Uri_PROPERTY_USER_INFO)")
cpp_quote("#define Uri_HAS_HOST_TYPE (1 << Uri_PROPERTY_HOST_TYPE)")
cpp_quote("#define Uri_HAS_PORT (1 << Uri_PROPERTY_PORT)")
cpp_quote("#define Uri_HAS_SCHEME (1 << Uri_PROPERTY_SCHEME)")
cpp_quote("#define Uri_HAS_ZONE (1 << Uri_PROPERTY_ZONE)")
cpp_quote("")
cpp_quote("#define Uri_CREATE_ALLOW_RELATIVE 0x1")
cpp_quote("#define Uri_CREATE_ALLOW_IMPLICIT_WILDCARD_SCHEME 0x2")
cpp_quote("#define Uri_CREATE_ALLOW_IMPLICIT_FILE_SCHEME 0x4")
cpp_quote("#define Uri_CREATE_NOFRAG 0x8")
cpp_quote("#define Uri_CREATE_NO_CANONICALIZE 0x10")
cpp_quote("#define Uri_CREATE_CANONICALIZE 0x100")
cpp_quote("#define Uri_CREATE_FILE_USE_DOS_PATH 0x20")
cpp_quote("#define Uri_CREATE_DECODE_EXTRA_INFO 0x40")
cpp_quote("#define Uri_CREATE_NO_DECODE_EXTRA_INFO 0x80")
cpp_quote("#define Uri_CREATE_CRACK_UNKNOWN_SCHEMES 0x200")
cpp_quote("#define Uri_CREATE_NO_CRACK_UNKNOWN_SCHEMES 0x400")
cpp_quote("#define Uri_CREATE_PRE_PROCESS_HTML_URI 0x800")
cpp_quote("#define Uri_CREATE_NO_PRE_PROCESS_HTML_URI 0x1000")
cpp_quote("#define Uri_CREATE_IE_SETTINGS 0x2000")
cpp_quote("#define Uri_CREATE_NO_IE_SETTINGS 0x4000")
cpp_quote("#define Uri_CREATE_NO_ENCODE_FORBIDDEN_CHARACTERS 0x8000")
cpp_quote("#define Uri_CREATE_NORMALIZE_INTL_CHARACTERS 0x10000")
cpp_quote("#define Uri_CREATE_CANONICALIZE_ABSOLUTE 0x20000")
cpp_quote("")
cpp_quote("#define Uri_DISPLAY_NO_FRAGMENT 0x1")
cpp_quote("#define Uri_PUNYCODE_IDN_HOST 0x2")
cpp_quote("#define Uri_DISPLAY_IDN_HOST 0x4")
cpp_quote("#define Uri_DISPLAY_NO_PUNYCODE 0x8")
cpp_quote("")
cpp_quote("#define Uri_ENCODING_USER_INFO_AND_PATH_IS_PERCENT_ENCODED_UTF8 0x1")
cpp_quote("#define Uri_ENCODING_USER_INFO_AND_PATH_IS_CP 0x2")
cpp_quote("#define Uri_ENCODING_HOST_IS_IDN 0x4")
cpp_quote("#define Uri_ENCODING_HOST_IS_PERCENT_ENCODED_UTF8 0x8")
cpp_quote("#define Uri_ENCODING_HOST_IS_PERCENT_ENCODED_CP 0x10")
cpp_quote("#define Uri_ENCODING_QUERY_AND_FRAGMENT_IS_PERCENT_ENCODED_UTF8 0x20")
cpp_quote("#define Uri_ENCODING_QUERY_AND_FRAGMENT_IS_CP 0x40")
cpp_quote("")
cpp_quote("#define Uri_ENCODING_RFC (Uri_ENCODING_USER_INFO_AND_PATH_IS_PERCENT_ENCODED_UTF8 | Uri_ENCODING_HOST_IS_PERCENT_ENCODED_UTF8 | Uri_ENCODING_QUERY_AND_FRAGMENT_IS_PERCENT_ENCODED_UTF8)")
cpp_quote("")
cpp_quote("#define UriBuilder_USE_ORIGINAL_FLAGS 0x1")
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, local, uuid (a158a630-ed6f-45fb-b987-f68676f57752), pointer_default (unique)]
interface IUriContainer : IUnknown {
  HRESULT GetIUri ([out] IUri **ppIUri);
}
cpp_quote("")
[local, object, uuid (4221b2e1-8955-46c0-BD5B-DE9897565DE7), pointer_default (unique)]
interface IUriBuilder: IUnknown {
  HRESULT CreateUriSimple ([in] DWORD dwAllowEncodingPropertyMask,[in] DWORD_PTR dwReserved,[out] IUri **ppIUri);
  HRESULT CreateUri ([in] DWORD dwCreateFlags,[in] DWORD dwAllowEncodingPropertyMask,[in] DWORD_PTR dwReserved,[out] IUri **ppIUri);
  HRESULT CreateUriWithFlags ([in] DWORD dwCreateFlags,[in] DWORD dwUriBuilderFlags,[in] DWORD dwAllowEncodingPropertyMask,[in] DWORD_PTR dwReserved,[out] IUri **ppIUri);
  HRESULT GetIUri ([out]IUri **ppIUri);
  HRESULT SetIUri ([in, unique]IUri *pIUri);
  HRESULT GetFragment ([out] DWORD *pcchFragment,[out] LPCWSTR *ppwzFragment);
  HRESULT GetHost ([out] DWORD *pcchHost,[out] LPCWSTR *ppwzHost);
  HRESULT GetPassword ([out] DWORD *pcchPassword,[out] LPCWSTR *ppwzPassword);
  HRESULT GetPath ([out] DWORD *pcchPath,[out] LPCWSTR *ppwzPath);
  HRESULT GetPort ([out] BOOL *pfHasPort,[out]DWORD *pdwPort);
  HRESULT GetQuery ([out] DWORD *pcchQuery,[out] LPCWSTR *ppwzQuery);
  HRESULT GetSchemeName ([out] DWORD *pcchSchemeName,[out] LPCWSTR *ppwzSchemeName);
  HRESULT GetUserName ([out] DWORD *pcchUserName,[out] LPCWSTR *ppwzUserName);
  HRESULT SetFragment ([in]LPCWSTR pwzNewValue);
  HRESULT SetHost ([in]LPCWSTR pwzNewValue);
  HRESULT SetPassword ([in]LPCWSTR pwzNewValue);
  HRESULT SetPath ([in]LPCWSTR pwzNewValue);
  HRESULT SetPort ([in]BOOL fHasPort,[in]DWORD dwNewValue);
  HRESULT SetQuery ([in]LPCWSTR pwzNewValue);
  HRESULT SetSchemeName ([in]LPCWSTR pwzNewValue);
  HRESULT SetUserName ([in]LPCWSTR pwzNewValue);
  HRESULT RemoveProperties ([in]DWORD dwPropertyMask);
  HRESULT HasBeenModified ([out]BOOL *pfModified);
};

cpp_quote("")
[local, object, uuid (E982CE48-0b96-440c-BC37-0c869b27a29e), pointer_default (unique)]
interface IUriBuilderFactory: IUnknown {
  HRESULT CreateIUriBuilder ([in] DWORD dwFlags,[in] DWORD_PTR dwReserved,[out] IUriBuilder **ppIUriBuilder);
  HRESULT CreateInitializedIUriBuilder ([in] DWORD dwFlags,[in] DWORD_PTR dwReserved,[out] IUriBuilder **ppIUriBuilder);
};

cpp_quote("")
cpp_quote("STDAPI CreateIUriBuilder(IUri *pIUri, DWORD dwFlags, DWORD_PTR dwReserved, IUriBuilder **ppIUriBuilder);")
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("#ifndef _LPWININETINFO_DEFINED")
cpp_quote("#define _LPWININETINFO_DEFINED")
cpp_quote("")
[object, uuid (79eac9d6-bafa-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IWinInetInfo : IUnknown {
  typedef [unique] IWinInetInfo *LPWININETINFO;
  [local] HRESULT QueryOption ([in] DWORD dwOption,[in, out, size_is (*pcbBuf)] LPVOID pBuffer,[in, out] DWORD *pcbBuf);
  [call_as (QueryOption)] HRESULT RemoteQueryOption ([in] DWORD dwOption,[in, out, size_is (*pcbBuf)] BYTE *pBuffer,[in, out] DWORD *pcbBuf);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#define WININETINFO_OPTION_LOCK_HANDLE 65534")

cpp_quote("")
cpp_quote("#ifndef _LPHTTPSECURITY_DEFINED")
cpp_quote("#define _LPHTTPSECURITY_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9d7-bafa-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IHttpSecurity : IWindowForBindingUI {
  typedef [unique] IHttpSecurity *LPHTTPSECURITY;
cpp_quote("")
  HRESULT OnSecurityProblem ([in] DWORD dwProblem);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPWININETHTTPINFO_DEFINED")
cpp_quote("#define _LPWININETHTTPINFO_DEFINED")
cpp_quote("")
[object, uuid (79eac9d8-bafa-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IWinInetHttpInfo : IWinInetInfo {
  typedef [unique] IWinInetHttpInfo *LPWININETHTTPINFO;
cpp_quote("")
  [local] HRESULT QueryInfo ([in] DWORD dwOption,[in, out, size_is (*pcbBuf)] LPVOID pBuffer,[in, out] DWORD *pcbBuf,[in, out] DWORD *pdwFlags,[in, out] DWORD *pdwReserved);
  [call_as (QueryInfo)] HRESULT RemoteQueryInfo ([in] DWORD dwOption,[in, out, size_is (*pcbBuf)] BYTE *pBuffer,[in, out] DWORD *pcbBuf,[in, out] DWORD *pdwFlags,[in, out] DWORD *pdwReserved);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPWININETHTTPTIMEOUTS_DEFINED")
cpp_quote("#define _LPWININETHTTPTIMEOUTS_DEFINED")
[local, object, uuid (F286FA56-C1FD-4270-8e67-B3EB790A81E8), pointer_default (unique)]
interface IWinInetHttpTimeouts : IUnknown {
  HRESULT GetRequestTimeouts ([out] DWORD *pdwConnectTimeout,[out] DWORD *pdwSendTimeout,[out] DWORD *pdwReceiveTimeout);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#ifndef _LPWININETCACHEHINTS_DEFINED")
cpp_quote("#define _LPWININETCACHEHINTS_DEFINED")
cpp_quote("")
[local, object, uuid (DD1EC3B3-8391-4fdb-A9E6-347c3caaa7dd), pointer_default (unique)]
interface IWinInetCacheHints : IUnknown {
  typedef [unique] IWinInetCacheHints *LPWININETCACHEHINTS;
cpp_quote("")
  HRESULT SetCacheExtension ([in] LPCWSTR pwzExt,[in, out, size_is (*pcbCacheFile)] LPVOID pszCacheFile,[in, out] DWORD *pcbCacheFile,[in, out] DWORD *pdwWinInetError,[in, out] DWORD *pdwReserved);
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#ifndef _LPWININETCACHEHINTS2_DEFINED")
cpp_quote("#define _LPWININETCACHEHINTS2_DEFINED")
cpp_quote("")
[local, object, uuid (7857aeac-D31F-49bf-884e-DD46DF36780A), pointer_default (unique)]
interface IWinInetCacheHints2 : IWinInetCacheHints {
  typedef [unique] IWinInetCacheHints2 *LPWININETCACHEHINTS2;
cpp_quote("")
  HRESULT SetCacheExtension2 ([in] LPCWSTR pwzExt,[out, size_is (sizeof (WCHAR) *(*pcchCacheFile))]WCHAR *pwzCacheFile,[in, out] DWORD *pcchCacheFile,[out] DWORD *pdwWinInetError,[out] DWORD *pdwReserved);
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#define SID_IBindHost IID_IBindHost")
cpp_quote("#define SID_SBindHost IID_IBindHost")

cpp_quote("")
cpp_quote("#ifndef _LPBINDHOST_DEFINED")
cpp_quote("#define _LPBINDHOST_DEFINED")

cpp_quote("")
cpp_quote("EXTERN_C const GUID SID_BindHost;")
cpp_quote("")
[object, uuid (fc4801a1-2ba9-11cf-a229-00aa003d7352), pointer_default (unique)]
interface IBindHost : IUnknown {
  typedef [unique] IBindHost *LPBINDHOST;
cpp_quote("")
  HRESULT CreateMoniker ([in] LPOLESTR szName,[in] IBindCtx *pBC,[out] IMoniker **ppmk,[in] DWORD dwReserved);
  [local] HRESULT MonikerBindToStorage ([in] IMoniker *pMk,[in] IBindCtx *pBC,[in] IBindStatusCallback *pBSC,[in] REFIID riid,[out] void **ppvObj);
  [call_as (MonikerBindToStorage)] HRESULT RemoteMonikerBindToStorage ([in, unique] IMoniker *pMk,[in, unique] IBindCtx *pBC,[in, unique] IBindStatusCallback *pBSC,[in] REFIID riid,[out, iid_is (riid)] IUnknown **ppvObj);
  [local] HRESULT MonikerBindToObject ([in] IMoniker *pMk,[in] IBindCtx *pBC,[in] IBindStatusCallback *pBSC,[in] REFIID riid,[out] void **ppvObj);
  [call_as (MonikerBindToObject)] HRESULT RemoteMonikerBindToObject ([in, unique] IMoniker *pMk,[in, unique] IBindCtx *pBC,[in, unique] IBindStatusCallback *pBSC,[in] REFIID riid,[out, iid_is (riid)] IUnknown **ppvObj);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#define URLOSTRM_USECACHEDCOPY_ONLY 0x1")
cpp_quote("#define URLOSTRM_USECACHEDCOPY 0x2")
cpp_quote("#define URLOSTRM_GETNEWESTVERSION 0x3")
cpp_quote("")
cpp_quote("struct IBindStatusCallback;")
cpp_quote("STDAPI HlinkSimpleNavigateToString(LPCWSTR szTarget, LPCWSTR szLocation, LPCWSTR szTargetFrameName, IUnknown *pUnk, IBindCtx *pbc, IBindStatusCallback *, DWORD grfHLNF, DWORD dwReserved);")
cpp_quote("STDAPI HlinkSimpleNavigateToMoniker(IMoniker *pmkTarget, LPCWSTR szLocation, LPCWSTR szTargetFrameName, IUnknown *pUnk, IBindCtx *pbc, IBindStatusCallback *, DWORD grfHLNF, DWORD dwReserved);")
cpp_quote("STDAPI URLOpenStreamA(LPUNKNOWN,LPCSTR,DWORD,LPBINDSTATUSCALLBACK);")
cpp_quote("STDAPI URLOpenStreamW(LPUNKNOWN,LPCWSTR,DWORD,LPBINDSTATUSCALLBACK);")
cpp_quote("STDAPI URLOpenPullStreamA(LPUNKNOWN,LPCSTR,DWORD,LPBINDSTATUSCALLBACK);")
cpp_quote("STDAPI URLOpenPullStreamW(LPUNKNOWN,LPCWSTR,DWORD,LPBINDSTATUSCALLBACK);")
cpp_quote("STDAPI URLDownloadToFileA(LPUNKNOWN,LPCSTR,LPCSTR,DWORD,LPBINDSTATUSCALLBACK);")
cpp_quote("STDAPI URLDownloadToFileW(LPUNKNOWN,LPCWSTR,LPCWSTR,DWORD,LPBINDSTATUSCALLBACK);")
cpp_quote("STDAPI URLDownloadToCacheFileA(LPUNKNOWN, LPCSTR,  LPSTR,  DWORD, DWORD, LPBINDSTATUSCALLBACK);")
cpp_quote("STDAPI URLDownloadToCacheFileW(LPUNKNOWN, LPCWSTR, LPWSTR, DWORD, DWORD, LPBINDSTATUSCALLBACK);")
cpp_quote("STDAPI URLOpenBlockingStreamA(LPUNKNOWN,LPCSTR,LPSTREAM*,DWORD,LPBINDSTATUSCALLBACK);")
cpp_quote("STDAPI URLOpenBlockingStreamW(LPUNKNOWN,LPCWSTR,LPSTREAM*,DWORD,LPBINDSTATUSCALLBACK);")
cpp_quote("")
cpp_quote("#define URLOpenStream __MINGW_NAME_AW(URLOpenStream)")
cpp_quote("#define URLOpenPullStream __MINGW_NAME_AW(URLOpenPullStream)")
cpp_quote("#define URLDownloadToFile __MINGW_NAME_AW(URLDownloadToFile)")
cpp_quote("#define URLDownloadToCacheFile __MINGW_NAME_AW(URLDownloadToCacheFile)")
cpp_quote("#define URLOpenBlockingStream __MINGW_NAME_AW(URLOpenBlockingStream)")
cpp_quote("")
cpp_quote("STDAPI HlinkGoBack(IUnknown *pUnk);")
cpp_quote("STDAPI HlinkGoForward(IUnknown *pUnk);")
cpp_quote("STDAPI HlinkNavigateString(IUnknown *pUnk, LPCWSTR szTarget);")
cpp_quote("STDAPI HlinkNavigateMoniker(IUnknown *pUnk, IMoniker *pmkTarget);")

cpp_quote("")
cpp_quote("#ifndef  _URLMON_NO_ASYNC_PLUGABLE_PROTOCOLS_")
interface IInternet;
interface IInternetBindInfo;
interface IInternetBindInfoEx;
interface IInternetProtocolRoot;
interface IInternetProtocol;
interface IInternetProtocolEx;
interface IInternetProtocolSink;
interface IInternetProtocolInfo;
interface IInternetSession;
interface IInternetProtocolSinkStackable;

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNET")
cpp_quote("#define _LPIINTERNET")
cpp_quote("")
[local, object, uuid (79eac9e0-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternet : IUnknown {
  typedef [unique] IInternet *LPIINTERNET;
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETBINDINFO")
cpp_quote("#define _LPIINTERNETBINDINFO")
cpp_quote("")
[local, object, uuid (79eac9e1-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternetBindInfo : IUnknown {
  typedef [unique] IInternetBindInfo *LPIINTERNETBINDINFO;
cpp_quote("")
  typedef enum tagBINDSTRING {
    BINDSTRING_HEADERS = 1,
    BINDSTRING_ACCEPT_MIMES,
    BINDSTRING_EXTRA_URL,
    BINDSTRING_LANGUAGE,
    BINDSTRING_USERNAME,
    BINDSTRING_PASSWORD,
    BINDSTRING_UA_PIXELS,
    BINDSTRING_UA_COLOR,
    BINDSTRING_OS,
    BINDSTRING_USER_AGENT,
    BINDSTRING_ACCEPT_ENCODINGS,
    BINDSTRING_POST_COOKIE,
    BINDSTRING_POST_DATA_MIME,
    BINDSTRING_URL,BINDSTRING_IID,
    BINDSTRING_FLAG_BIND_TO_OBJECT,
    BINDSTRING_PTR_BIND_CONTEXT,
    BINDSTRING_XDR_ORIGIN,
    BINDSTRING_DOWNLOADPATH,
    BINDSTRING_ROOTDOC_URL,
    BINDSTRING_INITIAL_FILENAME,
    BINDSTRING_PROXY_USERNAME,
    BINDSTRING_PROXY_PASSWORD
  } BINDSTRING;
cpp_quote("")
  HRESULT GetBindInfo ([out] DWORD *grfBINDF,[in, out, unique] BINDINFO *pbindinfo);
  HRESULT GetBindString ([in] ULONG ulStringType,[in, out] LPOLESTR *ppwzStr,[in] ULONG cEl,[in, out] ULONG *pcElFetched);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETBINDINFOEX")
cpp_quote("#define _LPIINTERNETBINDINFOEX")
cpp_quote("")
[local, object, uuid (a3e015b7-a82c-4dcd-a150-569aeeed36ab), pointer_default (unique)]
interface IInternetBindInfoEx : IInternetBindInfo {
  typedef [unique] IInternetBindInfoEx *LPIINTERNETBINDINFOEX;
cpp_quote("")
  HRESULT GetBindInfoEx ([out] DWORD *grfBINDF,[in, out, unique] BINDINFO *pbindinfo,[out] DWORD *grfBINDF2,[out] DWORD *pdwReserved);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETPROTOCOLROOT_DEFINED")
cpp_quote("#define _LPIINTERNETPROTOCOLROOT_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9e3-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternetProtocolRoot : IUnknown {
  typedef [unique] IInternetProtocolRoot *LPIINTERNETPROTOCOLROOT;
cpp_quote("")
  typedef enum _tagPI_FLAGS {
    PI_PARSE_URL = 0x1,
    PI_FILTER_MODE = 0x2,
    PI_FORCE_ASYNC = 0x4,
    PI_USE_WORKERTHREAD = 0x8,
    PI_MIMEVERIFICATION = 0x10,
    PI_CLSIDLOOKUP = 0x20,
    PI_DATAPROGRESS = 0x40,
    PI_SYNCHRONOUS = 0x80,
    PI_APARTMENTTHREADED = 0x100,
    PI_CLASSINSTALL = 0x200,
    PI_PASSONBINDCTX = 0x2000,
    PI_NOMIMEHANDLER = 0x8000,
    PI_LOADAPPDIRECT = 0x4000,
    PD_FORCE_SWITCH = 0x10000,
    PI_PREFERDEFAULTHANDLER = 0x20000
  } PI_FLAGS;
  typedef struct _tagPROTOCOLDATA {
    DWORD grfFlags;
    DWORD dwState;
    LPVOID pData;
    ULONG cbData;
  } PROTOCOLDATA;
cpp_quote("")
  typedef struct _tagStartParam {
    IID iid;
    IBindCtx *pIBindCtx;
    IUnknown *pItf;
  } StartParam;
cpp_quote("")
  HRESULT Start ([in] LPCWSTR szUrl,[in] IInternetProtocolSink *pOIProtSink,[in] IInternetBindInfo *pOIBindInfo,[in] DWORD grfPI,[in] HANDLE_PTR dwReserved);
  HRESULT Continue ([in] PROTOCOLDATA *pProtocolData);
  HRESULT Abort ([in] HRESULT hrReason,[in] DWORD dwOptions);
  HRESULT Terminate ([in] DWORD dwOptions);
  HRESULT Suspend ();
  HRESULT Resume ();
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETPROTOCOL_DEFINED")
cpp_quote("#define _LPIINTERNETPROTOCOL_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9e4-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternetProtocol : IInternetProtocolRoot {
  typedef [unique] IInternetProtocol *LPIINTERNETPROTOCOL;
cpp_quote("")
  HRESULT Read ([in, out, size_is (cb), length_is (*pcbRead)]void *pv,[in] ULONG cb,[out] ULONG *pcbRead);
  HRESULT Seek ([in] LARGE_INTEGER dlibMove,[in] DWORD dwOrigin,[out] ULARGE_INTEGER *plibNewPosition);
  HRESULT LockRequest ([in] DWORD dwOptions);
  HRESULT UnlockRequest ();
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#ifndef _LPIINTERNETPROTOCOLEX_DEFINED")
cpp_quote("#define _LPIINTERNETPROTOCOLEX_DEFINED")
cpp_quote("")
[local, object, uuid (C7A98E66-1010-492c-A1C8-C809E1F75905), pointer_default (unique)]
interface IInternetProtocolEx: IInternetProtocol {
  HRESULT StartEx ([in] IUri *pUri,[in] IInternetProtocolSink *pOIProtSink,[in] IInternetBindInfo *pOIBindInfo,[in] DWORD grfPI,[in] HANDLE_PTR dwReserved);
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETPROTOCOLSINK_DEFINED")
cpp_quote("#define _LPIINTERNETPROTOCOLSINK_DEFINED")
[local, object, uuid (79eac9e5-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternetProtocolSink : IUnknown {
  typedef [unique] IInternetProtocolSink *LPIINTERNETPROTOCOLSINK;
cpp_quote("")
  HRESULT Switch ([in] PROTOCOLDATA *pProtocolData);
  HRESULT ReportProgress ([in] ULONG ulStatusCode,[in] LPCWSTR szStatusText);
  HRESULT ReportData ([in] DWORD grfBSCF,[in] ULONG ulProgress,[in] ULONG ulProgressMax);
  HRESULT ReportResult ([in] HRESULT hrResult,[in] DWORD dwError,[in] LPCWSTR szResult);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETPROTOCOLSINKSTACKABLE_DEFINED")
cpp_quote("#define _LPIINTERNETPROTOCOLSINKSTACKABLE_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9f0-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternetProtocolSinkStackable : IUnknown {
  typedef [unique] IInternetProtocolSinkStackable *LPIINTERNETPROTOCOLSINKStackable;
cpp_quote("")
  HRESULT SwitchSink ([in] IInternetProtocolSink *pOIProtSink);
  HRESULT CommitSwitch ();
  HRESULT RollbackSwitch ();
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETSESSION_DEFINED")
cpp_quote("#define _LPIINTERNETSESSION_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9e7-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternetSession : IUnknown {
  typedef [unique] IInternetSession *LPIINTERNETSESSION;
cpp_quote("")
  typedef enum _tagOIBDG_FLAGS {
    OIBDG_APARTMENTTHREADED = 0x100,
    OIBDG_DATAONLY = 0x1000
  } OIBDG_FLAGS;
cpp_quote("")
  HRESULT RegisterNameSpace ([in] IClassFactory *pCF,[in] REFCLSID rclsid,[in] LPCWSTR pwzProtocol,[in] ULONG cPatterns,[in] const LPCWSTR *ppwzPatterns,[in] DWORD dwReserved);
  HRESULT UnregisterNameSpace ([in] IClassFactory *pCF,[in] LPCWSTR pszProtocol);
  HRESULT RegisterMimeFilter ([in] IClassFactory *pCF,[in] REFCLSID rclsid,[in] LPCWSTR pwzType);
  HRESULT UnregisterMimeFilter ([in] IClassFactory *pCF,[in] LPCWSTR pwzType);
  HRESULT CreateBinding ([in] LPBC pBC,[in] LPCWSTR szUrl,[in] IUnknown *pUnkOuter,[out, unique] IUnknown **ppUnk,[out, unique] IInternetProtocol **ppOInetProt,[in] DWORD dwOption);
  HRESULT SetSessionOption ([in] DWORD dwOption,[in] LPVOID pBuffer,[in] DWORD dwBufferLength,[in] DWORD dwReserved);
  HRESULT GetSessionOption ([in] DWORD dwOption,[in, out] LPVOID pBuffer,[in, out] DWORD *pdwBufferLength,[in] DWORD dwReserved);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETTHREADSWITCH_DEFINED")
cpp_quote("#define _LPIINTERNETTHREADSWITCH_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9e8-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternetThreadSwitch : IUnknown {
  typedef [unique] IInternetThreadSwitch *LPIINTERNETTHREADSWITCH;
cpp_quote("")
  HRESULT Prepare ();
  HRESULT Continue ();
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETPRIORITY_DEFINED")
cpp_quote("#define _LPIINTERNETPRIORITY_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9eb-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternetPriority : IUnknown {
  typedef [unique] IInternetPriority *LPIINTERNETPRIORITY;
cpp_quote("")
  HRESULT SetPriority ([in] LONG nPriority);
  HRESULT GetPriority ([out] LONG *pnPriority);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIINTERNETPROTOCOLINFO_DEFINED")
cpp_quote("#define _LPIINTERNETPROTOCOLINFO_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9ec-baf9-11ce-8c82-00aa004ba90b), pointer_default (unique)]
interface IInternetProtocolInfo : IUnknown {
  typedef [unique] IInternetProtocolInfo *LPIINTERNETPROTOCOLINFO;
cpp_quote("")
  typedef enum _tagPARSEACTION {
    PARSE_CANONICALIZE = 1,
    PARSE_FRIENDLY,
    PARSE_SECURITY_URL,
    PARSE_ROOTDOCUMENT,
    PARSE_DOCUMENT,
    PARSE_ANCHOR,
    PARSE_ENCODE_IS_UNESCAPE,
    PARSE_DECODE_IS_ESCAPE,
    PARSE_PATH_FROM_URL,
    PARSE_URL_FROM_PATH,
    PARSE_MIME,
    PARSE_SERVER,
    PARSE_SCHEMA,
    PARSE_SITE,
    PARSE_DOMAIN,
    PARSE_LOCATION,
    PARSE_SECURITY_DOMAIN,
    PARSE_ESCAPE,
    PARSE_UNESCAPE
  } PARSEACTION;
cpp_quote("")
  typedef enum _tagPSUACTION {
    PSU_DEFAULT = 1,
    PSU_SECURITY_URL_ONLY
  } PSUACTION;
cpp_quote("")
  typedef enum _tagQUERYOPTION {
    QUERY_EXPIRATION_DATE = 1,
    QUERY_TIME_OF_LAST_CHANGE,
    QUERY_CONTENT_ENCODING,
    QUERY_CONTENT_TYPE,
    QUERY_REFRESH,
    QUERY_RECOMBINE,
    QUERY_CAN_NAVIGATE,
    QUERY_USES_NETWORK,
    QUERY_IS_CACHED,
    QUERY_IS_INSTALLEDENTRY,
    QUERY_IS_CACHED_OR_MAPPED,
    QUERY_USES_CACHE,
    QUERY_IS_SECURE,
    QUERY_IS_SAFE,
    QUERY_USES_HISTORYFOLDER,
    QUERY_IS_CACHED_AND_USABLE_OFFLINE
  } QUERYOPTION;
cpp_quote("")
  HRESULT ParseUrl ([in] LPCWSTR pwzUrl,[in] PARSEACTION ParseAction,[in] DWORD dwParseFlags,[out] LPWSTR pwzResult,[in] DWORD cchResult,[out] DWORD *pcchResult,[in] DWORD dwReserved);
  HRESULT CombineUrl ([in] LPCWSTR pwzBaseUrl,[in] LPCWSTR pwzRelativeUrl,[in] DWORD dwCombineFlags,[out] LPWSTR pwzResult,[in] DWORD cchResult,[out] DWORD *pcchResult,[in] DWORD dwReserved);
  HRESULT CompareUrl ([in] LPCWSTR pwzUrl1,[in] LPCWSTR pwzUrl2,[in] DWORD dwCompareFlags);
  HRESULT QueryInfo ([in] LPCWSTR pwzUrl,[in] QUERYOPTION OueryOption,[in] DWORD dwQueryFlags,[in, out, size_is (*pcbBuf)] LPVOID pBuffer,[in] DWORD cbBuffer,[in, out] DWORD *pcbBuf,[in] DWORD dwReserved);
}

cpp_quote("")
cpp_quote("#ifndef URLMON_STRICT")
cpp_quote("#define PARSE_ENCODE PARSE_ENCODE_IS_UNESCAPE")
cpp_quote("#define PARSE_DECODE PARSE_DECODE_IS_ESCAPE")
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#define IOInet               IInternet")
cpp_quote("#define IOInetBindInfo       IInternetBindInfo")
cpp_quote("#define IOInetBindInfoEx     IInternetBindInfoEx")
cpp_quote("#define IOInetProtocolRoot   IInternetProtocolRoot")
cpp_quote("#define IOInetProtocol       IInternetProtocol")
cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#define IOInetProtocolEx     IInternetProtocolEx")
cpp_quote("#endif")
cpp_quote("#define IOInetProtocolSink   IInternetProtocolSink")
cpp_quote("#define IOInetProtocolInfo   IInternetProtocolInfo")
cpp_quote("#define IOInetSession        IInternetSession")
cpp_quote("#define IOInetPriority       IInternetPriority")
cpp_quote("#define IOInetThreadSwitch   IInternetThreadSwitch")
cpp_quote("#define IOInetProtocolSinkStackable   IInternetProtocolSinkStackable")
cpp_quote("")
cpp_quote("#define LPOINET              LPIINTERNET")
cpp_quote("#define LPOINETPROTOCOLINFO  LPIINTERNETPROTOCOLINFO")
cpp_quote("#define LPOINETBINDINFO      LPIINTERNETBINDINFO")
cpp_quote("#define LPOINETPROTOCOLROOT  LPIINTERNETPROTOCOLROOT")
cpp_quote("#define LPOINETPROTOCOL      LPIINTERNETPROTOCOL")
cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#define LPOINETPROTOCOLEX LPIINTERNETPROTOCOLEX")
cpp_quote("#endif")
cpp_quote("#define LPOINETPROTOCOLSINK  LPIINTERNETPROTOCOLSINK")
cpp_quote("#define LPOINETSESSION       LPIINTERNETSESSION")
cpp_quote("#define LPOINETTHREADSWITCH  LPIINTERNETTHREADSWITCH")
cpp_quote("#define LPOINETPRIORITY      LPIINTERNETPRIORITY")
cpp_quote("#define LPOINETPROTOCOLINFO  LPIINTERNETPROTOCOLINFO")
cpp_quote("#define LPOINETPROTOCOLSINKSTACKABLE  LPIINTERNETPROTOCOLSINKSTACKABLE")
cpp_quote("")
cpp_quote("#define IID_IOInet               IID_IInternet")
cpp_quote("#define IID_IOInetBindInfo       IID_IInternetBindInfo")
cpp_quote("#define IID_IOInetBindInfoEx     IID_IInternetBindInfoEx")
cpp_quote("#define IID_IOInetProtocolRoot   IID_IInternetProtocolRoot")
cpp_quote("#define IID_IOInetProtocol       IID_IInternetProtocol")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#define IID_IOInetProtocolEx IID_IInternetProtocolEx")
cpp_quote("#endif")

cpp_quote("#define IID_IOInetProtocolSink   IID_IInternetProtocolSink")
cpp_quote("#define IID_IOInetProtocolInfo   IID_IInternetProtocolInfo")
cpp_quote("#define IID_IOInetSession        IID_IInternetSession")
cpp_quote("#define IID_IOInetPriority       IID_IInternetPriority")
cpp_quote("#define IID_IOInetThreadSwitch   IID_IInternetThreadSwitch")
cpp_quote("#define IID_IOInetProtocolSinkStackable   IID_IInternetProtocolSinkStackable")

cpp_quote("")
cpp_quote("STDAPI CoInternetParseUrl(LPCWSTR pwzUrl, PARSEACTION ParseAction, DWORD dwFlags, LPWSTR pszResult, DWORD cchResult, DWORD *pcchResult, DWORD dwReserved);")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("STDAPI CoInternetParseIUri(IUri *pIUri, PARSEACTION ParseAction, DWORD dwFlags, LPWSTR pwzResult, DWORD cchResult, DWORD *pcchResult, DWORD_PTR dwReserved);")
cpp_quote("#endif")
cpp_quote("STDAPI CoInternetCombineUrl(LPCWSTR pwzBaseUrl, LPCWSTR pwzRelativeUrl, DWORD dwCombineFlags, LPWSTR pszResult, DWORD cchResult, DWORD *pcchResult, DWORD dwReserved);")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("STDAPI CoInternetCombineUrlEx(IUri *pBaseUri, LPCWSTR pwzRelativeUrl, DWORD dwCombineFlags, IUri **ppCombinedUri, DWORD_PTR dwReserved);")
cpp_quote("STDAPI CoInternetCombineIUri (IUri *pBaseUri, IUri *pRelativeUri, DWORD dwCombineFlags, IUri **ppCombinedUri, DWORD_PTR dwReserved);")
cpp_quote("#endif")
cpp_quote("STDAPI CoInternetCompareUrl(LPCWSTR pwzUrl1, LPCWSTR pwzUrl2, DWORD dwFlags);")
cpp_quote("STDAPI CoInternetGetProtocolFlags(LPCWSTR pwzUrl, DWORD *pdwFlags, DWORD dwReserved);")
cpp_quote("STDAPI CoInternetQueryInfo(LPCWSTR pwzUrl, QUERYOPTION QueryOptions, DWORD dwQueryFlags, LPVOID pvBuffer, DWORD cbBuffer, DWORD *pcbBuffer, DWORD dwReserved);")
cpp_quote("STDAPI CoInternetGetSession(DWORD dwSessionMode, IInternetSession **ppIInternetSession, DWORD dwReserved);")
cpp_quote("STDAPI CoInternetGetSecurityUrl(LPCWSTR pwszUrl, LPWSTR *ppwszSecUrl, PSUACTION psuAction, DWORD dwReserved);")
cpp_quote("STDAPI AsyncInstallDistributionUnit(LPCWSTR szDistUnit, LPCWSTR szTYPE, LPCWSTR szExt, DWORD dwFileVersionMS, DWORD dwFileVersionLS, LPCWSTR szURL,IBindCtx *pbc, LPVOID pvReserved,DWORD flags);")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("STDAPI CoInternetGetSecurityUrlEx(IUri *pUri, IUri **ppSecUri, PSUACTION psuAction, DWORD_PTR dwReserved);")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#ifndef _INTERNETFEATURELIST_DEFINED")
cpp_quote("#define _INTERNETFEATURELIST_DEFINED")

cpp_quote("")
typedef enum _tagINTERNETFEATURELIST {
  FEATURE_OBJECT_CACHING = 0,
  FEATURE_ZONE_ELEVATION,
  FEATURE_MIME_HANDLING,
  FEATURE_MIME_SNIFFING,
  FEATURE_WINDOW_RESTRICTIONS,
  FEATURE_WEBOC_POPUPMANAGEMENT,
  FEATURE_BEHAVIORS,
  FEATURE_DISABLE_MK_PROTOCOL,
  FEATURE_LOCALMACHINE_LOCKDOWN,
  FEATURE_SECURITYBAND,
  FEATURE_RESTRICT_ACTIVEXINSTALL,
  FEATURE_VALIDATE_NAVIGATE_URL,
  FEATURE_RESTRICT_FILEDOWNLOAD,
  FEATURE_ADDON_MANAGEMENT,
  FEATURE_PROTOCOL_LOCKDOWN,
  FEATURE_HTTP_USERNAME_PASSWORD_DISABLE,
  FEATURE_SAFE_BINDTOOBJECT,
  FEATURE_UNC_SAVEDFILECHECK,
  FEATURE_GET_URL_DOM_FILEPATH_UNENCODED,
  FEATURE_TABBED_BROWSING,
  FEATURE_SSLUX,
  FEATURE_DISABLE_NAVIGATION_SOUNDS,
  FEATURE_DISABLE_LEGACY_COMPRESSION,
  FEATURE_FORCE_ADDR_AND_STATUS,
  FEATURE_XMLHTTP,
  FEATURE_DISABLE_TELNET_PROTOCOL,
  FEATURE_FEEDS,
  FEATURE_BLOCK_INPUT_PROMPTS,
  FEATURE_ENTRY_COUNT,
} INTERNETFEATURELIST;
cpp_quote("")
cpp_quote("#define SET_FEATURE_ON_THREAD 0x1")
cpp_quote("#define SET_FEATURE_ON_PROCESS 0x2")
cpp_quote("#define SET_FEATURE_IN_REGISTRY 0x4")
cpp_quote("#define SET_FEATURE_ON_THREAD_LOCALMACHINE 0x8")
cpp_quote("#define SET_FEATURE_ON_THREAD_INTRANET 0x10")
cpp_quote("#define SET_FEATURE_ON_THREAD_TRUSTED 0x20")
cpp_quote("#define SET_FEATURE_ON_THREAD_INTERNET 0x40")
cpp_quote("#define SET_FEATURE_ON_THREAD_RESTRICTED 0x80")
cpp_quote("")
cpp_quote("#define GET_FEATURE_FROM_THREAD 0x1")
cpp_quote("#define GET_FEATURE_FROM_PROCESS 0x2")
cpp_quote("#define GET_FEATURE_FROM_REGISTRY 0x4")
cpp_quote("#define GET_FEATURE_FROM_THREAD_LOCALMACHINE 0x8")
cpp_quote("#define GET_FEATURE_FROM_THREAD_INTRANET 0x10")
cpp_quote("#define GET_FEATURE_FROM_THREAD_TRUSTED 0x20")
cpp_quote("#define GET_FEATURE_FROM_THREAD_INTERNET 0x40")
cpp_quote("#define GET_FEATURE_FROM_THREAD_RESTRICTED 0x80")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("STDAPI CoInternetSetFeatureEnabled(INTERNETFEATURELIST FeatureEntry, DWORD dwFlags, WINBOOL fEnable);")
cpp_quote("STDAPI CoInternetIsFeatureEnabled(INTERNETFEATURELIST FeatureEntry, DWORD dwFlags);")
cpp_quote("STDAPI CoInternetIsFeatureEnabledForUrl(INTERNETFEATURELIST FeatureEntry, DWORD dwFlags, LPCWSTR szURL, IInternetSecurityManager *pSecMgr);")
cpp_quote("STDAPI CoInternetIsFeatureEnabledForIUri(INTERNETFEATURELIST FeatureEntry, DWORD dwFlags, IUri *pIUri, IInternetSecurityManagerEx2 *pSecMgr);")
cpp_quote("STDAPI CoInternetIsFeatureZoneElevationEnabled(LPCWSTR szFromURL, LPCWSTR szToURL, IInternetSecurityManager *pSecMgr, DWORD dwFlags);")
cpp_quote("#endif")
cpp_quote("STDAPI CopyStgMedium(const STGMEDIUM *pcstgmedSrc, STGMEDIUM *pstgmedDest);")
cpp_quote("STDAPI CopyBindInfo(const BINDINFO *pcbiSrc, BINDINFO *pbiDest);")
cpp_quote("STDAPI_(void) ReleaseBindInfo(BINDINFO *pbindinfo);")

cpp_quote("")
cpp_quote("#define INET_E_USE_DEFAULT_PROTOCOLHANDLER _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0011))")
cpp_quote("#define INET_E_USE_DEFAULT_SETTING         _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0012))")
cpp_quote("#define INET_E_DEFAULT_ACTION              INET_E_USE_DEFAULT_PROTOCOLHANDLER")
cpp_quote("#define INET_E_QUERYOPTION_UNKNOWN         _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0013))")
cpp_quote("#define INET_E_REDIRECTING                 _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0014))")

cpp_quote("")
cpp_quote("#define OInetParseUrl CoInternetParseUrl")
cpp_quote("#define OInetCombineUrl CoInternetCombineUrl")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#define OInetCombineUrlEx CoInternetCombineUrlEx")
cpp_quote("#define OInetCombineIUri CoInternetCombineIUri")
cpp_quote("#endif")
cpp_quote("#define OInetCompareUrl CoInternetCompareUrl")
cpp_quote("#define OInetQueryInfo CoInternetQueryInfo")
cpp_quote("#define OInetGetSession CoInternetGetSession")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#define PROTOCOLFLAG_NO_PICS_CHECK 0x1")
cpp_quote("")
cpp_quote("STDAPI CoInternetCreateSecurityManager(IServiceProvider *pSP, IInternetSecurityManager **ppSM, DWORD dwReserved);")
cpp_quote("STDAPI CoInternetCreateZoneManager(IServiceProvider *pSP, IInternetZoneManager **ppZM, DWORD dwReserved);")
cpp_quote("")
cpp_quote("EXTERN_C const IID CLSID_InternetSecurityManager;")
cpp_quote("EXTERN_C const IID CLSID_InternetZoneManager;")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("EXTERN_C const IID CLSID_PersistentZoneIdentifier;")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#define SID_SInternetSecurityManager IID_IInternetSecurityManager")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#define SID_SInternetSecurityManagerEx IID_IInternetSecurityManagerEx")
cpp_quote("#endif")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#define SID_SInternetSecurityManagerEx2 IID_IInternetSecurityManagerEx2")
cpp_quote("#endif")
cpp_quote("#define SID_SInternetHostSecurityManager IID_IInternetHostSecurityManager")

cpp_quote("")
cpp_quote("#ifndef _LPINTERNETSECURITYMGRSITE_DEFINED")
cpp_quote("#define _LPINTERNETSECURITYMGRSITE_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9ed-baf9-11ce-8c82-00aa004ba90b), helpstring ("IInternetSecurityMgrSite Interface"), pointer_default (unique)]
interface IInternetSecurityMgrSite : IUnknown {
  HRESULT GetWindow ([out] HWND *phwnd);
  HRESULT EnableModeless ([in] BOOL fEnable);
};
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPINTERNETSECURITYMANANGER_DEFINED")
cpp_quote("#define _LPINTERNETSECURITYMANANGER_DEFINED")
cpp_quote("")
[uuid (79eac9ee-baf9-11ce-8c82-00aa004ba90b), helpstring ("IInternetSecurityManager Interface"), pointer_default (unique)]
interface IInternetSecurityManager : IUnknown {
  HRESULT SetSecuritySite ([in, unique] IInternetSecurityMgrSite *pSite);
  HRESULT GetSecuritySite ([out] IInternetSecurityMgrSite **ppSite);
cpp_quote("")
  cpp_quote("#define MUTZ_NOSAVEDFILECHECK 0x1")
  cpp_quote("#define MUTZ_ISFILE 0x2")
  cpp_quote("#define MUTZ_ACCEPT_WILDCARD_SCHEME 0x80")
  cpp_quote("#define MUTZ_ENFORCERESTRICTED 0x100")
  cpp_quote("#define MUTZ_RESERVED 0x200")
  cpp_quote("#define MUTZ_REQUIRESAVEDFILECHECK 0x400")
  cpp_quote("#define MUTZ_DONT_UNESCAPE 0x800")
  cpp_quote("#define MUTZ_DONT_USE_CACHE 0x1000")
  cpp_quote("#define MUTZ_FORCE_INTRANET_FLAGS 0x2000")
  cpp_quote("#define MUTZ_IGNORE_ZONE_MAPPINGS 0x4000")

cpp_quote("")
  HRESULT MapUrlToZone ([in] LPCWSTR pwszUrl,[out] DWORD *pdwZone,[in] DWORD dwFlags);
  cpp_quote("")

  cpp_quote("#define MAX_SIZE_SECURITY_ID 512")

cpp_quote("")
  HRESULT GetSecurityId ([in] LPCWSTR pwszUrl,[out, size_is (*pcbSecurityId)]BYTE *pbSecurityId,[in, out]DWORD *pcbSecurityId,[in] DWORD_PTR dwReserved);
cpp_quote("")
  typedef enum {
    PUAF_DEFAULT = 0x0,
    PUAF_NOUI = 0x1,
    PUAF_ISFILE = 0x2,
    PUAF_WARN_IF_DENIED = 0x4,
    PUAF_FORCEUI_FOREGROUND = 0x8,
    PUAF_CHECK_TIFS = 0x10,
    PUAF_DONTCHECKBOXINDIALOG = 0x20,
    PUAF_TRUSTED = 0x40,
    PUAF_ACCEPT_WILDCARD_SCHEME = 0x80,
    PUAF_ENFORCERESTRICTED = 0x100,
    PUAF_NOSAVEDFILECHECK = 0x200,
    PUAF_REQUIRESAVEDFILECHECK = 0x400,
    PUAF_DONT_USE_CACHE = 0x1000,
    PUAF_RESERVED1 = 0x2000,
    PUAF_RESERVED2 = 0x4000,
    PUAF_LMZ_UNLOCKED = 0x10000,
    PUAF_LMZ_LOCKED = 0x20000,
    PUAF_DEFAULTZONEPOL = 0x40000,
    PUAF_NPL_USE_LOCKED_IF_RESTRICTED = 0x80000,
    PUAF_NOUIIFLOCKED = 0x100000,
    PUAF_DRAGPROTOCOLCHECK = 0x200000
  } PUAF;
  typedef enum {
    PUAFOUT_DEFAULT = 0x0,
    PUAFOUT_ISLOCKZONEPOLICY = 0x1
  } PUAFOUT;

cpp_quote("")
  HRESULT ProcessUrlAction ([in] LPCWSTR pwszUrl,[in] DWORD dwAction,[out, size_is (cbPolicy)]BYTE *pPolicy,[in] DWORD cbPolicy,[in, unique]BYTE *pContext,[in] DWORD cbContext,[in] DWORD dwFlags,[in] DWORD dwReserved);
  HRESULT QueryCustomPolicy ([in] LPCWSTR pwszUrl,[in] REFGUID guidKey,[out, size_is (,*pcbPolicy)]BYTE **ppPolicy,[out] DWORD *pcbPolicy,[in] BYTE *pContext,[in] DWORD cbContext,[in] DWORD dwReserved);

  cpp_quote("")
  typedef enum {
    SZM_CREATE = 0x0,
    SZM_DELETE = 0x1
  } SZM_FLAGS;
cpp_quote("")
  HRESULT SetZoneMapping ([in] DWORD dwZone,[in] LPCWSTR lpszPattern,[in] DWORD dwFlags);
  HRESULT GetZoneMappings ([in] DWORD dwZone,[out] IEnumString **ppenumString,[in] DWORD dwFlags);
};
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#ifndef _LPINTERNETSECURITYMANANGEREX_DEFINED")
cpp_quote("#define _LPINTERNETSECURITYMANANGEREX_DEFINED")
cpp_quote("")
[uuid (F164EDF1-CC7C-4f0d-9a94-34222625c393), helpstring ("IInternetSecurityManagerEx Interface"), pointer_default (unique)]
interface IInternetSecurityManagerEx : IInternetSecurityManager {
  HRESULT ProcessUrlActionEx ([in] LPCWSTR pwszUrl,[in] DWORD dwAction,[out, size_is (cbPolicy)]BYTE *pPolicy,[in] DWORD cbPolicy,[in] BYTE *pContext,[in] DWORD cbContext,[in] DWORD dwFlags,[in] DWORD dwReserved,[out] DWORD *pdwOutFlags);
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#ifndef _LPINTERNETSECURITYMANANGEREx2_DEFINED")
cpp_quote("#define _LPINTERNETSECURITYMANANGEREx2_DEFINED")
cpp_quote("")
[uuid (F1E50292-A795-4117-8e09-2b560a72ac60), helpstring ("IInternetSecurityManagerEx2 Interface"), pointer_default (unique)]
interface IInternetSecurityManagerEx2 : IInternetSecurityManagerEx {
  HRESULT MapUrlToZoneEx2 ([in]IUri *pUri,[out] DWORD *pdwZone,[in] DWORD dwFlags,[out]LPWSTR *ppwszMappedUrl,[out]DWORD *pdwOutFlags);
  HRESULT ProcessUrlActionEx2 ([in]IUri *pUri,[in] DWORD dwAction,[out, size_is (cbPolicy)]BYTE *pPolicy,[in] DWORD cbPolicy,[in, unique]BYTE *pContext,[in] DWORD cbContext,[in] DWORD dwFlags,[in] DWORD_PTR dwReserved,[out] DWORD *pdwOutFlags);
  HRESULT GetSecurityIdEx2 ([in]IUri *pUri,[out, size_is (*pcbSecurityId)]BYTE *pbSecurityId,[in, out]DWORD *pcbSecurityId,[in] DWORD_PTR dwReserved);
  HRESULT QueryCustomPolicyEx2 ([in]IUri *pUri,[in] REFGUID guidKey,[out, size_is (,*pcbPolicy)]BYTE **ppPolicy,[out] DWORD *pcbPolicy,[in] BYTE *pContext,[in] DWORD cbContext,[in] DWORD_PTR dwReserved);
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
[object, uuid (cd45f185-1b21-48e2-967b-ead743a8914e), pointer_default (unique)]
interface IZoneIdentifier : IUnknown {
  HRESULT GetId ([out] DWORD *pdwZone);
  HRESULT SetId ([in] DWORD dwZone);
  HRESULT Remove ();
};
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPINTERNETHOSTSECURITYMANANGER_DEFINED")
cpp_quote("#define _LPINTERNETHOSTSECURITYMANANGER_DEFINED")
cpp_quote("")
[local, object, uuid (3af280b6-cb3f-11d0-891e-00c04fb6bfc4), helpstring ("IInternetHostSecurityManager Interface"), pointer_default (unique)]
interface IInternetHostSecurityManager : IUnknown {
  HRESULT GetSecurityId ([out, size_is (*pcbSecurityId)]BYTE *pbSecurityId,[in, out]DWORD *pcbSecurityId,[in] DWORD_PTR dwReserved);
  HRESULT ProcessUrlAction ([in] DWORD dwAction,[out, size_is (cbPolicy)]BYTE *pPolicy,[in] DWORD cbPolicy,[in] BYTE *pContext,[in] DWORD cbContext,[in] DWORD dwFlags,[in] DWORD dwReserved);
  HRESULT QueryCustomPolicy ([in] REFGUID guidKey,[out, size_is (,*pcbPolicy)]BYTE **ppPolicy,[out] DWORD *pcbPolicy,[in] BYTE *pContext,[in] DWORD cbContext,[in] DWORD dwReserved);
};
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#define URLACTION_MIN 0x1000")
cpp_quote("")
cpp_quote("#define URLACTION_DOWNLOAD_MIN 0x1000")
cpp_quote("#define URLACTION_DOWNLOAD_SIGNED_ACTIVEX 0x1001")
cpp_quote("#define URLACTION_DOWNLOAD_UNSIGNED_ACTIVEX 0x1004")
cpp_quote("#define URLACTION_DOWNLOAD_CURR_MAX 0x1004")
cpp_quote("#define URLACTION_DOWNLOAD_MAX 0x11FF")
cpp_quote("")
cpp_quote("#define URLACTION_ACTIVEX_MIN 0x1200")
cpp_quote("#define URLACTION_ACTIVEX_RUN 0x1200")
cpp_quote("#define URLPOLICY_ACTIVEX_CHECK_LIST 0x10000")
cpp_quote("#define URLACTION_ACTIVEX_OVERRIDE_OBJECT_SAFETY 0x1201")
cpp_quote("#define URLACTION_ACTIVEX_OVERRIDE_DATA_SAFETY 0x1202")
cpp_quote("#define URLACTION_ACTIVEX_OVERRIDE_SCRIPT_SAFETY 0x1203")
cpp_quote("#define URLACTION_SCRIPT_OVERRIDE_SAFETY 0x1401")
cpp_quote("#define URLACTION_ACTIVEX_CONFIRM_NOOBJECTSAFETY 0x1204")
cpp_quote("#define URLACTION_ACTIVEX_TREATASUNTRUSTED 0x1205")
cpp_quote("#define URLACTION_ACTIVEX_NO_WEBOC_SCRIPT 0x1206")
cpp_quote("#define URLACTION_ACTIVEX_OVERRIDE_REPURPOSEDETECTION 0x1207")
cpp_quote("#define URLACTION_ACTIVEX_OVERRIDE_OPTIN 0x1208")
cpp_quote("#define URLACTION_ACTIVEX_SCRIPTLET_RUN 0x1209")
cpp_quote("#define URLACTION_ACTIVEX_DYNSRC_VIDEO_AND_ANIMATION 0x120A")
cpp_quote("#define URLACTION_ACTIVEX_OVERRIDE_DOMAINLIST 0x120B")
cpp_quote("#define URLACTION_ACTIVEX_CURR_MAX 0x120B")
cpp_quote("#define URLACTION_ACTIVEX_MAX 0x13ff")
cpp_quote("")
cpp_quote("#define URLACTION_SCRIPT_MIN 0x1400")
cpp_quote("#define URLACTION_SCRIPT_RUN 0x1400")
cpp_quote("#define URLACTION_SCRIPT_JAVA_USE 0x1402")
cpp_quote("#define URLACTION_SCRIPT_SAFE_ACTIVEX 0x1405")
cpp_quote("#define URLACTION_CROSS_DOMAIN_DATA 0x1406")
cpp_quote("#define URLACTION_SCRIPT_PASTE 0x1407")
cpp_quote("#define URLACTION_ALLOW_XDOMAIN_SUBFRAME_RESIZE 0x1408")
cpp_quote("#define URLACTION_SCRIPT_XSSFILTER 0x1409")
cpp_quote("#define URLACTION_SCRIPT_NAVIGATE 0x140A")
cpp_quote("#define URLACTION_PLUGGABLE_PROTOCOL_XHR 0x140B")
cpp_quote("#define URLACTION_SCRIPT_CURR_MAX 0x140B")
cpp_quote("#define URLACTION_SCRIPT_MAX 0x15ff")
cpp_quote("")
cpp_quote("#define URLACTION_HTML_MIN 0x1600")
cpp_quote("#define URLACTION_HTML_SUBMIT_FORMS 0x1601")
cpp_quote("#define URLACTION_HTML_SUBMIT_FORMS_FROM 0x1602")
cpp_quote("#define URLACTION_HTML_SUBMIT_FORMS_TO 0x1603")
cpp_quote("#define URLACTION_HTML_FONT_DOWNLOAD 0x1604")
cpp_quote("#define URLACTION_HTML_JAVA_RUN 0x1605")
cpp_quote("#define URLACTION_HTML_USERDATA_SAVE 0x1606")
cpp_quote("#define URLACTION_HTML_SUBFRAME_NAVIGATE 0x1607")
cpp_quote("#define URLACTION_HTML_META_REFRESH 0x1608")
cpp_quote("#define URLACTION_HTML_MIXED_CONTENT 0x1609")
cpp_quote("#define URLACTION_HTML_INCLUDE_FILE_PATH 0x160A")
cpp_quote("#define URLACTION_HTML_ALLOW_INJECTED_DYNAMIC_HTML 0x160B")
cpp_quote("#define URLACTION_HTML_REQUIRE_UTF8_DOCUMENT_CODEPAGE 0x160C")
cpp_quote("#define URLACTION_HTML_ALLOW_CROSS_DOMAIN_CANVAS 0x160D")
cpp_quote("#define URLACTION_HTML_ALLOW_WINDOW_CLOSE 0x160E")
cpp_quote("#define URLACTION_HTML_ALLOW_CROSS_DOMAIN_WEBWORKER 0x160F")
cpp_quote("#define URLACTION_HTML_ALLOW_CROSS_DOMAIN_TEXTTRACK 0x1610")
cpp_quote("#define URLACTION_HTML_ALLOW_INDEXEDDB 0x1611")
cpp_quote("")
cpp_quote("#define URLACTION_HTML_MAX 0x17ff")
cpp_quote("")
cpp_quote("#define URLACTION_SHELL_MIN 0x1800")
cpp_quote("#define URLACTION_SHELL_INSTALL_DTITEMS 0x1800")
cpp_quote("#define URLACTION_SHELL_MOVE_OR_COPY 0x1802")
cpp_quote("#define URLACTION_SHELL_FILE_DOWNLOAD 0x1803")
cpp_quote("#define URLACTION_SHELL_VERB 0x1804")
cpp_quote("#define URLACTION_SHELL_WEBVIEW_VERB 0x1805")
cpp_quote("#define URLACTION_SHELL_SHELLEXECUTE 0x1806")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#define URLACTION_SHELL_EXECUTE_HIGHRISK 0x1806")
cpp_quote("#define URLACTION_SHELL_EXECUTE_MODRISK 0x1807")
cpp_quote("#define URLACTION_SHELL_EXECUTE_LOWRISK 0x1808")
cpp_quote("#define URLACTION_SHELL_POPUPMGR 0x1809")
cpp_quote("#define URLACTION_SHELL_RTF_OBJECTS_LOAD 0x180A")
cpp_quote("#define URLACTION_SHELL_ENHANCED_DRAGDROP_SECURITY 0x180B")
cpp_quote("#define URLACTION_SHELL_EXTENSIONSECURITY 0x180C")
cpp_quote("#define URLACTION_SHELL_SECURE_DRAGSOURCE 0x180D")
cpp_quote("#endif")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_WIN7)")
cpp_quote("#define URLACTION_SHELL_REMOTEQUERY 0x180E")
cpp_quote("#define URLACTION_SHELL_PREVIEW 0x180F")
cpp_quote("#define URLACTION_SHELL_SHARE 0x1810")
cpp_quote("#define URLACTION_SHELL_ALLOW_CROSS_SITE_SHARE 0x1811")
cpp_quote("#endif")
cpp_quote("#define URLACTION_SHELL_CURR_MAX 0x1811")
cpp_quote("#define URLACTION_SHELL_MAX 0x19ff")
cpp_quote("")
cpp_quote("#define URLACTION_NETWORK_MIN 0x1A00")
cpp_quote("")
cpp_quote("#define URLACTION_CREDENTIALS_USE 0x1A00")
cpp_quote("#define URLPOLICY_CREDENTIALS_SILENT_LOGON_OK 0x0")
cpp_quote("#define URLPOLICY_CREDENTIALS_MUST_PROMPT_USER 0x10000")
cpp_quote("#define URLPOLICY_CREDENTIALS_CONDITIONAL_PROMPT 0x20000")
cpp_quote("#define URLPOLICY_CREDENTIALS_ANONYMOUS_ONLY 0x30000")
cpp_quote("")
cpp_quote("#define URLACTION_AUTHENTICATE_CLIENT 0x1A01")
cpp_quote("#define URLPOLICY_AUTHENTICATE_CLEARTEXT_OK 0x0")
cpp_quote("#define URLPOLICY_AUTHENTICATE_CHALLENGE_RESPONSE 0x10000")
cpp_quote("#define URLPOLICY_AUTHENTICATE_MUTUAL_ONLY 0x30000")
cpp_quote("")
cpp_quote("#define URLACTION_COOKIES 0x1A02")
cpp_quote("#define URLACTION_COOKIES_SESSION 0x1A03")
cpp_quote("")
cpp_quote("#define URLACTION_CLIENT_CERT_PROMPT 0x1A04")
cpp_quote("")
cpp_quote("#define URLACTION_COOKIES_THIRD_PARTY 0x1A05")
cpp_quote("#define URLACTION_COOKIES_SESSION_THIRD_PARTY 0x1A06")
cpp_quote("")
cpp_quote("#define URLACTION_COOKIES_ENABLED 0x1A10")
cpp_quote("")
cpp_quote("#define URLACTION_NETWORK_CURR_MAX 0x1A10")
cpp_quote("#define URLACTION_NETWORK_MAX 0x1Bff")
cpp_quote("")
cpp_quote("#define URLACTION_JAVA_MIN 0x1C00")
cpp_quote("#define URLACTION_JAVA_PERMISSIONS 0x1C00")
cpp_quote("#define URLPOLICY_JAVA_PROHIBIT 0x0")
cpp_quote("#define URLPOLICY_JAVA_HIGH 0x10000")
cpp_quote("#define URLPOLICY_JAVA_MEDIUM 0x20000")
cpp_quote("#define URLPOLICY_JAVA_LOW 0x30000")
cpp_quote("#define URLPOLICY_JAVA_CUSTOM 0x800000")
cpp_quote("#define URLACTION_JAVA_CURR_MAX 0x1C00")
cpp_quote("#define URLACTION_JAVA_MAX 0x1Cff")
cpp_quote("")
cpp_quote("#define URLACTION_INFODELIVERY_MIN 0x1D00")
cpp_quote("#define URLACTION_INFODELIVERY_NO_ADDING_CHANNELS 0x1D00")
cpp_quote("#define URLACTION_INFODELIVERY_NO_EDITING_CHANNELS 0x1D01")
cpp_quote("#define URLACTION_INFODELIVERY_NO_REMOVING_CHANNELS 0x1D02")
cpp_quote("#define URLACTION_INFODELIVERY_NO_ADDING_SUBSCRIPTIONS 0x1D03")
cpp_quote("#define URLACTION_INFODELIVERY_NO_EDITING_SUBSCRIPTIONS 0x1D04")
cpp_quote("#define URLACTION_INFODELIVERY_NO_REMOVING_SUBSCRIPTIONS 0x1D05")
cpp_quote("#define URLACTION_INFODELIVERY_NO_CHANNEL_LOGGING 0x1D06")
cpp_quote("#define URLACTION_INFODELIVERY_CURR_MAX 0x1D06")
cpp_quote("#define URLACTION_INFODELIVERY_MAX 0x1Dff")
cpp_quote("#define URLACTION_CHANNEL_SOFTDIST_MIN 0x1E00")
cpp_quote("#define URLACTION_CHANNEL_SOFTDIST_PERMISSIONS 0x1E05")
cpp_quote("#define URLPOLICY_CHANNEL_SOFTDIST_PROHIBIT 0x10000")
cpp_quote("#define URLPOLICY_CHANNEL_SOFTDIST_PRECACHE 0x20000")
cpp_quote("#define URLPOLICY_CHANNEL_SOFTDIST_AUTOINSTALL 0x30000")
cpp_quote("#define URLACTION_CHANNEL_SOFTDIST_MAX 0x1Eff")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE80)")
cpp_quote("#define URLACTION_DOTNET_USERCONTROLS 0x2005")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#define URLACTION_BEHAVIOR_MIN 0x2000")
cpp_quote("#define URLACTION_BEHAVIOR_RUN 0x2000")
cpp_quote("#define URLPOLICY_BEHAVIOR_CHECK_LIST 0x10000")
cpp_quote("")
cpp_quote("#define URLACTION_FEATURE_MIN 0x2100")
cpp_quote("#define URLACTION_FEATURE_MIME_SNIFFING 0x2100")
cpp_quote("#define URLACTION_FEATURE_ZONE_ELEVATION 0x2101")
cpp_quote("#define URLACTION_FEATURE_WINDOW_RESTRICTIONS 0x2102")
cpp_quote("#define URLACTION_FEATURE_SCRIPT_STATUS_BAR 0x2103")
cpp_quote("#define URLACTION_FEATURE_FORCE_ADDR_AND_STATUS 0x2104")
cpp_quote("#define URLACTION_FEATURE_BLOCK_INPUT_PROMPTS 0x2105")
cpp_quote("#define URLACTION_FEATURE_DATA_BINDING 0x2106")
cpp_quote("#define URLACTION_FEATURE_CROSSDOMAIN_FOCUS_CHANGE 0x2107")
cpp_quote("")
cpp_quote("#define URLACTION_AUTOMATIC_DOWNLOAD_UI_MIN 0x2200")
cpp_quote("#define URLACTION_AUTOMATIC_DOWNLOAD_UI 0x2200")
cpp_quote("#define URLACTION_AUTOMATIC_ACTIVEX_UI 0x2201")
cpp_quote("")
cpp_quote("#define URLACTION_ALLOW_RESTRICTEDPROTOCOLS 0x2300")
cpp_quote("#endif")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#define URLACTION_ALLOW_APEVALUATION 0x2301")
cpp_quote("#define URLACTION_ALLOW_XHR_EVALUATION 0x2302")
cpp_quote("#define URLACTION_WINDOWS_BROWSER_APPLICATIONS 0x2400")
cpp_quote("#define URLACTION_XPS_DOCUMENTS 0x2401")
cpp_quote("#define URLACTION_LOOSE_XAML 0x2402")
cpp_quote("#define URLACTION_LOWRIGHTS 0x2500")
cpp_quote("#define URLACTION_WINFX_SETUP 0x2600")
cpp_quote("#define URLACTION_INPRIVATE_BLOCKING 0x2700")
cpp_quote("#endif")
cpp_quote("#define URLACTION_ALLOW_AUDIO_VIDEO 0x2701")
cpp_quote("#define URLACTION_ALLOW_ACTIVEX_FILTERING 0x2702")
cpp_quote("#define URLACTION_ALLOW_STRUCTURED_STORAGE_SNIFFING 0x2703")
cpp_quote("#define URLACTION_ALLOW_AUDIO_VIDEO_PLUGINS 0x2704")
cpp_quote("#define URLACTION_ALLOW_ZONE_ELEVATION_VIA_OPT_OUT 0x2705")
cpp_quote("#define URLACTION_ALLOW_ZONE_ELEVATION_OPT_OUT_ADDITION 0x2706")
cpp_quote("#define URLACTION_ALLOW_CROSSDOMAIN_DROP_WITHIN_WINDOW 0x2708")
cpp_quote("#define URLACTION_ALLOW_CROSSDOMAIN_DROP_ACROSS_WINDOWS 0x2709")
cpp_quote("#define URLACTION_ALLOW_CROSSDOMAIN_APPCACHE_MANIFEST 0x270A")
cpp_quote("#define URLACTION_ALLOW_RENDER_LEGACY_DXTFILTERS 0x270B")
cpp_quote("")
cpp_quote("#define URLPOLICY_ALLOW 0x0")
cpp_quote("#define URLPOLICY_QUERY 0x1")
cpp_quote("#define URLPOLICY_DISALLOW 0x3")
cpp_quote("#define URLPOLICY_NOTIFY_ON_ALLOW 0x10")
cpp_quote("#define URLPOLICY_NOTIFY_ON_DISALLOW 0x20")
cpp_quote("#define URLPOLICY_LOG_ON_ALLOW 0x40")
cpp_quote("#define URLPOLICY_LOG_ON_DISALLOW 0x80")
cpp_quote("")
cpp_quote("#define URLPOLICY_MASK_PERMISSIONS 0x0f")
cpp_quote("#define GetUrlPolicyPermissions(dw) (dw & URLPOLICY_MASK_PERMISSIONS)")
cpp_quote("#define SetUrlPolicyPermissions(dw,dw2) ((dw) = ((dw) & ~(URLPOLICY_MASK_PERMISSIONS)) | (dw2))")
cpp_quote("")
cpp_quote("#define URLPOLICY_DONTCHECKDLGBOX 0x100")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("EXTERN_C const GUID GUID_CUSTOM_LOCALMACHINEZONEUNLOCKED;")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPINTERNETZONEMANAGER_DEFINED")
cpp_quote("#define _LPINTERNETZONEMANAGER_DEFINED")
cpp_quote("")
[local, object, uuid (79eac9ef-baf9-11ce-8c82-00aa004ba90b), helpstring ("IInternetZoneManager Interface"), pointer_default (unique)]
interface IInternetZoneManager : IUnknown {
  typedef [unique] IInternetZoneManager *LPURLZONEMANAGER;
cpp_quote("")
  typedef enum tagURLZONE {
    URLZONE_INVALID = -1,
    URLZONE_PREDEFINED_MIN = 0,
    URLZONE_LOCAL_MACHINE = 0,
    URLZONE_INTRANET,
    URLZONE_TRUSTED,
    URLZONE_INTERNET,
    URLZONE_UNTRUSTED,
    URLZONE_PREDEFINED_MAX = 999,
    URLZONE_USER_MIN = 1000,
    URLZONE_USER_MAX = 10000,
  } URLZONE;
  cpp_quote("")
  cpp_quote("#define URLZONE_ESC_FLAG 0x100")

cpp_quote("")
  typedef enum tagURLTEMPLATE {
    URLTEMPLATE_CUSTOM = 0x0,
    URLTEMPLATE_PREDEFINED_MIN = 0x10000,
    URLTEMPLATE_LOW = 0x10000,
    URLTEMPLATE_MEDLOW = 0x10500,
    URLTEMPLATE_MEDIUM = 0x11000,
    URLTEMPLATE_MEDHIGH = 0x11500,
    URLTEMPLATE_HIGH = 0x12000,
    URLTEMPLATE_PREDEFINED_MAX = 0x20000
  } URLTEMPLATE;
cpp_quote("")
  enum { MAX_ZONE_PATH = 260, MAX_ZONE_DESCRIPTION = 200 };
cpp_quote("")
  typedef enum {
    ZAFLAGS_CUSTOM_EDIT = 0x1,
    ZAFLAGS_ADD_SITES = 0x2,
    ZAFLAGS_REQUIRE_VERIFICATION = 0x4,
    ZAFLAGS_INCLUDE_PROXY_OVERRIDE = 0x8,
    ZAFLAGS_INCLUDE_INTRANET_SITES = 0x10,
    ZAFLAGS_NO_UI = 0x20,
    ZAFLAGS_SUPPORTS_VERIFICATION = 0x40,
    ZAFLAGS_UNC_AS_INTRANET = 0x80,
    ZAFLAGS_DETECT_INTRANET = 0x100,
    ZAFLAGS_USE_LOCKED_ZONES = 0x10000,
    ZAFLAGS_VERIFY_TEMPLATE_SETTINGS = 0x20000,
    ZAFLAGS_NO_CACHE = 0x40000,
  } ZAFLAGS;
cpp_quote("")
  typedef struct _ZONEATTRIBUTES {
    ULONG cbSize;
    WCHAR szDisplayName[MAX_ZONE_PATH];
    WCHAR szDescription[MAX_ZONE_DESCRIPTION];
    WCHAR szIconPath[MAX_ZONE_PATH];
    DWORD dwTemplateMinLevel;
    DWORD dwTemplateRecommended;
    DWORD dwTemplateCurrentLevel;
    DWORD dwFlags;
  } ZONEATTRIBUTES,*LPZONEATTRIBUTES;
cpp_quote("")
  HRESULT GetZoneAttributes ([in] DWORD dwZone,[in, out, unique]ZONEATTRIBUTES *pZoneAttributes);
  HRESULT SetZoneAttributes ([in] DWORD dwZone,[in] ZONEATTRIBUTES *pZoneAttributes);

  cpp_quote("")
  typedef enum _URLZONEREG {
    URLZONEREG_DEFAULT=0,
    URLZONEREG_HKLM,
    URLZONEREG_HKCU
  } URLZONEREG;
cpp_quote("")
  HRESULT GetZoneCustomPolicy ([in] DWORD dwZone,[in] REFGUID guidKey,[out, size_is (,*pcbPolicy)]BYTE **ppPolicy,[out] DWORD *pcbPolicy,[in] URLZONEREG urlZoneReg);
  HRESULT SetZoneCustomPolicy ([in] DWORD dwZone,[in] REFGUID guidKey,[in, size_is (cbPolicy)]BYTE *pPolicy,[in] DWORD cbPolicy,[in] URLZONEREG urlZoneReg);
  HRESULT GetZoneActionPolicy ([in] DWORD dwZone,[in] DWORD dwAction,[out, size_is (cbPolicy)]BYTE *pPolicy,[in] DWORD cbPolicy,[in] URLZONEREG urlZoneReg);
  HRESULT SetZoneActionPolicy ([in] DWORD dwZone,[in] DWORD dwAction,[in, size_is (cbPolicy)]BYTE *pPolicy,[in] DWORD cbPolicy,[in] URLZONEREG urlZoneReg);
  HRESULT PromptAction ([in] DWORD dwAction,[in] HWND hwndParent,[in] LPCWSTR pwszUrl,[in] LPCWSTR pwszText,[in] DWORD dwPromptFlags);
  HRESULT LogAction ([in] DWORD dwAction,[in] LPCWSTR pwszUrl,[in] LPCWSTR pwszText,[in] DWORD dwLogFlags);
  HRESULT CreateZoneEnumerator ([out] DWORD *pdwEnum,[out] DWORD *pdwCount,[in] DWORD dwFlags);
  HRESULT GetZoneAt ([in] DWORD dwEnum,[in] DWORD dwIndex,[out] DWORD *pdwZone);
  HRESULT DestroyZoneEnumerator ([in] DWORD dwEnum);
  HRESULT CopyTemplatePoliciesToZone ([in] DWORD dwTemplate,[in] DWORD dwZone,[in] DWORD dwReserved);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE60SP2)")
cpp_quote("#ifndef _LPINTERNETZONEMANAGEREX_DEFINED")
cpp_quote("#define _LPINTERNETZONEMANAGEREX_DEFINED")
cpp_quote("")
[local, object, uuid (A4C23339-8e06-431e-9bf4-7e711c085648), helpstring ("IInternetZoneManagerEx Interface"), pointer_default (unique)]
interface IInternetZoneManagerEx : IInternetZoneManager {
  HRESULT GetZoneActionPolicyEx ([in] DWORD dwZone,[in] DWORD dwAction,[out, size_is (cbPolicy)]BYTE *pPolicy,[in] DWORD cbPolicy,[in] URLZONEREG urlZoneReg,[in] DWORD dwFlags);
  HRESULT SetZoneActionPolicyEx ([in] DWORD dwZone,[in] DWORD dwAction,[in, size_is (cbPolicy)]BYTE *pPolicy,[in] DWORD cbPolicy,[in] URLZONEREG urlZoneReg,[in] DWORD dwFlags);
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if (_WIN32_IE >= _WIN32_IE_IE70)")
cpp_quote("#ifndef _LPINTERNETZONEMANAGEREX2_DEFINED")
cpp_quote("#define _LPINTERNETZONEMANAGEREX2_DEFINED")
cpp_quote("")
cpp_quote("#define SECURITY_IE_STATE_GREEN 0x0")
cpp_quote("#define SECURITY_IE_STATE_RED 0x1")
cpp_quote("")
[local, object, uuid (EDC17559-DD5D-4846-8eef-8becba5a4abf), helpstring ("IInternetZoneManagerEx2 Interface"), pointer_default (unique)]
interface IInternetZoneManagerEx2 : IInternetZoneManagerEx {
  HRESULT GetZoneAttributesEx ([in] DWORD dwZone,[in, out, unique]ZONEATTRIBUTES *pZoneAttributes,[in] DWORD dwFlags);
  HRESULT GetZoneSecurityState ([in] DWORD dwZoneIndex,[in] BOOL fRespectPolicy,[in, out] LPDWORD pdwState,[in, out] BOOL *pfPolicyEncountered);
  HRESULT GetIESecurityState ([in] BOOL fRespectPolicy,[in, out] LPDWORD pdwState,[in, out] BOOL *pfPolicyEncountered,[in] BOOL fNoCache);
  HRESULT FixUnsecureSettings ();
}
cpp_quote("#endif")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("EXTERN_C const IID CLSID_SoftDistExt;")

cpp_quote("")
cpp_quote("#ifndef _LPSOFTDISTEXT_DEFINED")
cpp_quote("#define _LPSOFTDISTEXT_DEFINED")
cpp_quote("")
cpp_quote("#define SOFTDIST_FLAG_USAGE_EMAIL 0x1")
cpp_quote("#define SOFTDIST_FLAG_USAGE_PRECACHE 0x2")
cpp_quote("#define SOFTDIST_FLAG_USAGE_AUTOINSTALL 0x4")
cpp_quote("#define SOFTDIST_FLAG_DELETE_SUBSCRIPTION 0x8")
cpp_quote("")
cpp_quote("#define SOFTDIST_ADSTATE_NONE 0x0")
cpp_quote("#define SOFTDIST_ADSTATE_AVAILABLE 0x1")
cpp_quote("#define SOFTDIST_ADSTATE_DOWNLOADED 0x2")
cpp_quote("#define SOFTDIST_ADSTATE_INSTALLED 0x3")
cpp_quote("")
typedef struct _tagCODEBASEHOLD {
  ULONG cbSize;
  LPWSTR szDistUnit;
  LPWSTR szCodeBase;
  DWORD dwVersionMS;
  DWORD dwVersionLS;
  DWORD dwStyle;
} CODEBASEHOLD,*LPCODEBASEHOLD;

cpp_quote("")
typedef struct _tagSOFTDISTINFO {
  ULONG cbSize;
  DWORD dwFlags;
  DWORD dwAdState;
  LPWSTR szTitle;
  LPWSTR szAbstract;
  LPWSTR szHREF;
  DWORD dwInstalledVersionMS;
  DWORD dwInstalledVersionLS;
  DWORD dwUpdateVersionMS;
  DWORD dwUpdateVersionLS;
  DWORD dwAdvertisedVersionMS;
  DWORD dwAdvertisedVersionLS;
  DWORD dwReserved;
} SOFTDISTINFO, *LPSOFTDISTINFO;

cpp_quote("")
[local, object, uuid (B15B8DC1-C7E1-11d0-8680-00aa00bdcb71), helpstring ("ISoftDistExt Interface"), pointer_default (unique)]
interface ISoftDistExt : IUnknown {
  HRESULT ProcessSoftDist ([in] LPCWSTR szCDFURL, [in] IXMLElement *pSoftDistElement, [in, out] LPSOFTDISTINFO lpsdi);
  HRESULT GetFirstCodeBase ([in] LPWSTR *szCodeBase,[in] LPDWORD dwMaxSize);
  HRESULT GetNextCodeBase ([in] LPWSTR *szCodeBase,[in] LPDWORD dwMaxSize);
  HRESULT AsyncInstallDistributionUnit ([in] IBindCtx *pbc,[in] LPVOID pvReserved,[in] DWORD flags,[in] LPCODEBASEHOLD lpcbh);
}

cpp_quote("")
cpp_quote("STDAPI GetSoftwareUpdateInfo(LPCWSTR szDistUnit, LPSOFTDISTINFO psdi);")
cpp_quote("STDAPI SetSoftwareUpdateAdvertisementState(LPCWSTR szDistUnit, DWORD dwAdState, DWORD dwAdvertisedVersionMS, DWORD dwAdvertisedVersionLS);")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPCATALOGFILEINFO_DEFINED")
cpp_quote("#define _LPCATALOGFILEINFO_DEFINED")
cpp_quote("")
[local, object, uuid (711c7600-6b48-11d1-B403-00aa00b92af1), pointer_default (unique)]
interface ICatalogFileInfo : IUnknown {
  typedef [unique] ICatalogFileInfo *LPCATALOGFILEINFO;
cpp_quote("")
  HRESULT GetCatalogFile ([out] LPSTR *ppszCatalogFile);
  HRESULT GetJavaTrust ([out] void **ppJavaTrust);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPDATAFILTER_DEFINED")
cpp_quote("#define _LPDATAFILTER_DEFINED")
cpp_quote("")
[object, uuid (69d14c80-c18e-11d0-a9ce-006097942311), pointer_default (unique)]
interface IDataFilter: IUnknown {
  typedef [unique] IDataFilter *LPDATAFILTER;
cpp_quote("")
  HRESULT DoEncode ([in] DWORD dwFlags,[in] LONG lInBufferSize,[in, size_is (lInBufferSize)]BYTE *pbInBuffer,[in] LONG lOutBufferSize,[out, size_is (lOutBufferSize)]BYTE *pbOutBuffer,[in] LONG lInBytesAvailable,[out] LONG *plInBytesRead,[out] LONG *plOutBytesWritten,[in] DWORD dwReserved);
  HRESULT DoDecode ([in] DWORD dwFlags,[in] LONG lInBufferSize,[in, size_is (lInBufferSize)]BYTE *pbInBuffer,[in] LONG lOutBufferSize,[out, size_is (lOutBufferSize)]BYTE *pbOutBuffer,[in] LONG lInBytesAvailable,[out] LONG *plInBytesRead,[out] LONG *plOutBytesWritten,[in] DWORD dwReserved);
  HRESULT SetEncodingLevel ([in] DWORD dwEncLevel);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPENCODINGFILTERFACTORY_DEFINED")
cpp_quote("#define _LPENCODINGFILTERFACTORY_DEFINED")
cpp_quote("")
typedef struct _tagPROTOCOLFILTERDATA {
  DWORD cbSize;
  IInternetProtocolSink *pProtocolSink;
  IInternetProtocol *pProtocol;
  IUnknown *pUnk;
  DWORD dwFilterFlags;
} PROTOCOLFILTERDATA;

cpp_quote("")
[local, object, uuid (70bdde00-c18e-11d0-a9ce-006097942311), pointer_default (unique)]
interface IEncodingFilterFactory : IUnknown {
  typedef [unique] IEncodingFilterFactory *LPENCODINGFILTERFACTORY;
cpp_quote("")
  typedef struct _tagDATAINFO {
    ULONG ulTotalSize;
    ULONG ulavrPacketSize;
    ULONG ulConnectSpeed;
    ULONG ulProcessorSpeed;
  } DATAINFO;
cpp_quote("")
  HRESULT FindBestFilter ([in] LPCWSTR pwzCodeIn,[in] LPCWSTR pwzCodeOut,[in] DATAINFO info,[out] IDataFilter **ppDF);
  HRESULT GetDefaultFilter ([in] LPCWSTR pwzCodeIn,[in] LPCWSTR pwzCodeOut,[out] IDataFilter **ppDF);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _HITLOGGING_DEFINED")
cpp_quote("#define _HITLOGGING_DEFINED")

cpp_quote("")
cpp_quote("WINBOOL WINAPI IsLoggingEnabledA(LPCSTR pszUrl);")
cpp_quote("WINBOOL WINAPI IsLoggingEnabledW(LPCWSTR pwszUrl);")

cpp_quote("")
cpp_quote("#define IsLoggingEnabled __MINGW_NAME_AW(IsLoggingEnabled)")

typedef struct _tagHIT_LOGGING_INFO {
  DWORD dwStructSize;
  LPSTR lpszLoggedUrlName;
  SYSTEMTIME StartTime;
  SYSTEMTIME EndTime;
  LPSTR lpszExtendedInfo;
} HIT_LOGGING_INFO,*LPHIT_LOGGING_INFO;
cpp_quote("")
cpp_quote("WINBOOL WINAPI WriteHitLogging(LPHIT_LOGGING_INFO lpLogginginfo);")

cpp_quote("")
cpp_quote("#define CONFIRMSAFETYACTION_LOADOBJECT 0x1")

cpp_quote("")
struct CONFIRMSAFETY {
  CLSID clsid;
  IUnknown *pUnk;
  DWORD dwFlags;
};
cpp_quote("")
cpp_quote("EXTERN_C const GUID GUID_CUSTOM_CONFIRMOBJECTSAFETY;")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPIWRAPPEDPROTOCOL_DEFINED")
cpp_quote("#define _LPIWRAPPEDPROTOCOL_DEFINED")
cpp_quote("")
[local, object, uuid (53c84785-8425-4dc5-971b-e58d9c19f9b6), pointer_default (unique)]
interface IWrappedProtocol : IUnknown {
  typedef [unique] IWrappedProtocol *LPIWRAPPEDPROTOCOL;
cpp_quote("")
  HRESULT GetWrapperCode ([out] LONG *pnCode,[in] DWORD_PTR dwReserved);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPGETBINDHANDLE_DEFINED")
cpp_quote("#define _LPGETBINDHANDLE_DEFINED")
cpp_quote("")
[local, object, uuid (AF0FF408-129d-4b20-91f0-02bd23d88352), pointer_default (unique)]
interface IGetBindHandle: IUnknown {
  typedef [unique] IGetBindHandle *LPGETBINDHANDLE;
cpp_quote("")
  typedef enum {
    BINDHANDLETYPES_APPCACHE = 0x0,
    BINDHANDLETYPES_DEPENDENCY = 0x1,
    BINDHANDLETYPES_COUNT
  } BINDHANDLETYPES;
cpp_quote("")
  HRESULT GetBindHandle ([in] BINDHANDLETYPES enumRequestedHandle,[out] HANDLE *pRetHandle);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _XHRPLUGGABLEPROTOCOL_DEFINED")
cpp_quote("#define _XHRPLUGGABLEPROTOCOL_DEFINED")
cpp_quote("")
typedef struct _tagPROTOCOL_ARGUMENT {
  LPCWSTR szMethod;
  LPCWSTR szTargetUrl;
} PROTOCOL_ARGUMENT,*LPPROTOCOL_ARGUMENT;
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#ifndef _LPBINDCALLBACKREDIRECT_DEFINED")
cpp_quote("#define _LPBINDCALLBACKREDIRECT_DEFINED")
cpp_quote("")
[local, object, uuid (11c81bc2-121e-4ed5-B9C4-B430BD54F2C0), pointer_default (unique)]
interface IBindCallbackRedirect : IUnknown {
  typedef [unique] IBindCallbackRedirect *LPBINDCALLBACKREDIRECT;
cpp_quote("")
  HRESULT Redirect ([in] LPCWSTR lpcUrl,[out] VARIANT_BOOL *vbCancel);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#endif")
