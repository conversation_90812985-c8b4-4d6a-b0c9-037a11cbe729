.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_transport_set_fastopen" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_transport_set_fastopen \- API function
.SH SYNOPSIS
.B #include <gnutls/socket.h>
.sp
.BI "void gnutls_transport_set_fastopen(gnutls_session_t " session ", int " fd ", struct sockaddr * " connect_addr ", socklen_t " connect_addrlen ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "int fd" 12
is the session's socket descriptor
.IP "struct sockaddr * connect_addr" 12
is the address we want to connect to
.IP "socklen_t connect_addrlen" 12
is the length of  \fIconnect_addr\fP 
.IP "unsigned int flags" 12
must be zero
.SH "DESCRIPTION"
Enables TCP Fast Open (TFO) for the specified TLS client session.
That means that TCP connection establishment and the transmission
of the first TLS client hello packet are combined. The
peer's address must be  specified in  \fIconnect_addr\fP and  \fIconnect_addrlen\fP ,
and the socket specified by  \fIfd\fP should not be connected.

TFO only works for TCP sockets of type AF_INET and AF_INET6.
If the OS doesn't support TCP fast open this function will result
to gnutls using \fBconnect()\fP transparently during the first write.
.SH "NOTE"
This function overrides all the transport callback functions.
If this is undesirable, TCP Fast Open must be implemented on the user
callback functions without calling this function. When using
this function, transport callbacks must not be set, and 
\fBgnutls_transport_set_ptr()\fP or \fBgnutls_transport_set_int()\fP
must not be called.

On GNU/Linux TFO has to be enabled at the system layer, that is
in /proc/sys/net/ipv4/tcp_fastopen, bit 0 has to be set.

This function has no effect on server sessions.
.SH "SINCE"
3.5.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
