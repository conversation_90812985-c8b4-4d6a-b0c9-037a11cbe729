.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_alert_send" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_alert_send \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_alert_send(gnutls_session_t " session ", gnutls_alert_level_t " level ", gnutls_alert_description_t " desc ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_alert_level_t level" 12
is the level of the alert
.IP "gnutls_alert_description_t desc" 12
is the alert description
.SH "DESCRIPTION"
This function will send an alert to the peer in order to inform
him of something important (eg. his Certificate could not be verified).
If the alert level is Fatal then the peer is expected to close the
connection, otherwise he may ignore the alert and continue.

The error code of the underlying record send function will be
returned, so you may also receive \fBGNUTLS_E_INTERRUPTED\fP or
\fBGNUTLS_E_AGAIN\fP as well.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
