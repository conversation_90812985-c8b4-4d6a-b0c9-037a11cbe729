CMAKE_CUDA_COMPILE_FEATURES
---------------------------

.. versionadded:: 3.17

List of features known to the CUDA compiler

These features are known to be available for use with the CUDA compiler. This
list is a subset of the features listed in the
:prop_gbl:`CMAKE_CUDA_KNOWN_FEATURES` global property.

See the :manual:`cmake-compile-features(7)` manual for information on
compile features and a list of supported compilers.
