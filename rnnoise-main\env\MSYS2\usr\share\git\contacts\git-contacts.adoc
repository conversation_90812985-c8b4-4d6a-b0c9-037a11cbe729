git-contacts(1)
===============

NAME
----
git-contacts - List people who might be interested in a set of changes


SYNOPSIS
--------
[verse]
'git contacts' (<patch>|<range>|<rev>)...


DESCRIPTION
-----------

Given a set of changes, specified as patch files or revisions, determine people
who might be interested in those changes.  This is done by consulting the
history of each patch or revision hunk to find people mentioned by commits
which touched the lines of files under consideration.

Input consists of one or more patch files or revision arguments.  A revision
argument can be a range or a single `<rev>` which is interpreted as
`<rev>..HEAD`, thus the same revision arguments are accepted as for
linkgit:git-format-patch[1]. Patch files and revision arguments can be combined
in the same invocation.

This command can be useful for determining the list of people with whom to
discuss proposed changes, or for finding the list of recipients to Cc: when
submitting a patch series via `git send-email`. For the latter case, `git
contacts` can be used as the argument to `git send-email`'s `--cc-cmd` option.


DISCUSSION
----------

`git blame` is invoked for each hunk in a patch file or revision.  For each
commit mentioned by `git blame`, the commit message is consulted for people who
authored, reviewed, signed, acknowledged, or were Cc:'d.  Once the list of
participants is known, each person's relevance is computed by considering how
many commits mentioned that person compared with the total number of commits
under consideration.  The final output consists only of participants who exceed
a minimum threshold of participation.


OUTPUT
------

For each person of interest, a single line is output, terminated by a newline.
If the person's name is known, ``Name $$<user@host>$$'' is printed; otherwise
only ``$$<user@host>$$'' is printed.


EXAMPLES
--------

* Consult patch files:
+
------------
$ git contacts feature/*.patch
------------

* Revision range:
+
------------
$ git contacts R1..R2
------------

* From a single revision to `HEAD`:
+
------------
$ git contacts origin
------------

* Helper for `git send-email`:
+
------------
$ git send-email --cc-cmd='git contacts' feature/*.patch
------------


LIMITATIONS
-----------

Several conditions controlling a person's significance are currently
hard-coded, such as minimum participation level (10%), blame date-limiting (5
years), and `-C` level for detecting moved and copied lines (a single `-C`). In
the future, these conditions may become configurable.


GIT
---
Part of the linkgit:git[1] suite
