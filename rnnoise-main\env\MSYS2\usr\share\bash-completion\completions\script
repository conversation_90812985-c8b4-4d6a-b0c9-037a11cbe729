_script_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-c'|'--command')
			compopt -o bashdefault
			COMPREPLY=( $(compgen -c -- $cur) )
			return 0
			;;
		'-E'|'--echo')
			COMPREPLY=( $(compgen -W "auto always never" -- $cur) )
			return 0
			;;
		'-o'|'--output-limit')
			COMPREPLY=( $(compgen -W "size" -- $cur) )
			return 0
			;;
		'-m'|'--logging-format')
			COMPREPLY=( $(compgen -W "classic advanced" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		'=')
			cur=${cur#=}
			;;
		-*)
			OPTS="--append
				--command
				--echo
				--log-in
				--log-out
				--log-io
				--log-timing
				--logging-format
				--return
				--flush
				--force
				--quiet
				--output-limit
				--timing=
				--version
				--help"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	local IFS=$'\n'
	compopt -o filenames
	COMPREPLY=( $(compgen -f -- $cur) )
	return 0
}
complete -F _script_module script
