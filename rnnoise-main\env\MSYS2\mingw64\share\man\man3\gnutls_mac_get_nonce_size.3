.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_mac_get_nonce_size" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_mac_get_nonce_size \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "size_t gnutls_mac_get_nonce_size(gnutls_mac_algorithm_t " algorithm ");"
.SH ARGUMENTS
.IP "gnutls_mac_algorithm_t algorithm" 12
is an encryption algorithm
.SH "DESCRIPTION"
Returns the size of the nonce used by the MAC in TLS.
.SH "RETURNS"
length (in bytes) of the given MAC nonce size, or 0.
.SH "SINCE"
3.2.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
