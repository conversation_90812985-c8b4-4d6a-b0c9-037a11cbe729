.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_random_art" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_random_art \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_random_art(gnutls_random_art_t " type ", const char * " key_type ", unsigned int " key_size ", void * " fpr ", size_t " fpr_size ", gnutls_datum_t * " art ");"
.SH ARGUMENTS
.IP "gnutls_random_art_t type" 12
The type of the random art (for now only \fBGNUTLS_RANDOM_ART_OPENSSH\fP is supported)
.IP "const char * key_type" 12
The type of the key (RSA, DSA etc.)
.IP "unsigned int key_size" 12
The size of the key in bits
.IP "void * fpr" 12
The fingerprint of the key
.IP "size_t fpr_size" 12
The size of the fingerprint
.IP "gnutls_datum_t * art" 12
The returned random art
.SH "DESCRIPTION"
This function will convert a given fingerprint to an "artistic"
image. The returned image is allocated using \fBgnutls_malloc()\fP, is
null\-terminated but art\->size will not account the terminating null.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
