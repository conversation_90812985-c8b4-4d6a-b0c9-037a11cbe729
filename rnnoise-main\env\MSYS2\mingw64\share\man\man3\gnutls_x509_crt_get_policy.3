.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_policy" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_policy \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_policy(gnutls_x509_crt_t " crt ", unsigned " indx ", struct gnutls_x509_policy_st * " policy ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "unsigned indx" 12
This specifies which policy to return. Use (0) to get the first one.
.IP "struct gnutls_x509_policy_st * policy" 12
A pointer to a policy structure.
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical
.SH "DESCRIPTION"
This function will extract the certificate policy (extension *********)
specified by the given index.

The policy returned by this function must be deinitialized by using
\fBgnutls_x509_policy_release()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
if the extension is not present, otherwise a negative error value.
.SH "SINCE"
3.1.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
