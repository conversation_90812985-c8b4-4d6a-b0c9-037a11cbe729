.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_get_preferred_hash_algorithm" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_get_preferred_hash_algorithm \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_get_preferred_hash_algorithm(gnutls_pubkey_t " key ", gnutls_digest_algorithm_t * " hash ", unsigned int * " mand ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t key" 12
Holds the certificate
.IP "gnutls_digest_algorithm_t * hash" 12
The result of the call with the hash algorithm used for signature
.IP "unsigned int * mand" 12
If non zero it means that the algorithm MUST use this hash. May be NULL.
.SH "DESCRIPTION"
This function will read the certificate and return the appropriate digest
algorithm to use for signing with this certificate. Some certificates (i.e.
DSA might not be able to sign without the preferred algorithm).

To get the signature algorithm instead of just the hash use \fBgnutls_pk_to_sign()\fP
with the algorithm of the certificate/key and the provided  \fIhash\fP .
.SH "RETURNS"
the 0 if the hash algorithm is found. A negative error code is
returned on error.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
