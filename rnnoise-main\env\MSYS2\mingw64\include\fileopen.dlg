/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#include <shlobj.h>

#define LBS_DISABLENOSCROLL 0x1000L

NEWFI<PERSON>OPENV2ORD DIALOG DISCARDABLE 0,0,370,237
STYLE DS_MODALFRAME | DS_3DLOOK | WS_POPUP | WS_VISIBLE | WS_CAPTION |
      WS_SYSMENU | DS_CONTEXTHELP | WS_CLIPCHILDREN
CAPTION "Open"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "Look &in:",stc4,4,7,57,8,SS_NOTIFY,WS_EX_RIGHT
    COMBOBOX cmb2,66,4,174,300,CBS_DROPDOWNLIST | CBS_OWNERDRAWFIXED | CBS_HASSTRINGS |
                    WS_VSCROLL | WS_TABSTOP

    LTEXT "",stc1,248,4,80,14,NOT WS_GROUP | NOT WS_VISIBLE,CONTROL "",ctl1,TOOLBARCLASSNAME,WS_TABSTOP |
                    CCS_NODIVIDER |CCS_NOPARENTALIGN | CCS_NORESIZE | TBSTYLE_WRAPABLE |
                    TBSTYLE_FLAT | TBSTYLE_TOOLTIPS | TBSTYLE_CUSTOMERASE,4,22,58,208 ,WS_EX_CLIENTEDGE
    LISTBOX lst1,66,22,300,156,LBS_SORT | LBS_NOINTEGRALHEIGHT | LBS_MULTICOLUMN |
                    WS_HSCROLL | NOT WS_VISIBLE

    LTEXT "File &name:",stc3,67,187,58,8,SS_NOTIFY

    EDITTEXT edt1,130,184,164,12,WS_TABSTOP | ES_AUTOHSCROLL

    CONTROL "",cmb13,WC_COMBOBOXEX,WS_TABSTOP| WS_VSCROLL |
                     CBS_DROPDOWN | CBS_AUTOHSCROLL,130,184,164,150

    LTEXT "Files of &type:",stc2,67,203,58,8,SS_NOTIFY
    COMBOBOX cmb1,130,201,164,100,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP

    AUTOCHECKBOX "Open as &read-only",chx1,130,217,160,8,WS_TABSTOP

    DEFPUSHBUTTON "&Open",IDOK,316,184,50,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,316,200,50,14,WS_GROUP
    PUSHBUTTON "&Help",pshHelp,316,218,50,14,WS_GROUP
END

NEWFILEOPENORD DIALOG DISCARDABLE 0,0,280,164
STYLE DS_MODALFRAME | DS_3DLOOK | WS_POPUP | WS_VISIBLE | WS_CAPTION |
      WS_SYSMENU | DS_CONTEXTHELP | WS_CLIPCHILDREN
CAPTION "Open"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "Look &in:",stc4,7,6,27,8,SS_NOTIFY
    COMBOBOX cmb2,36,3,148,300,CBS_DROPDOWNLIST | CBS_OWNERDRAWFIXED | CBS_HASSTRINGS |
                    WS_VSCROLL | WS_TABSTOP

    LTEXT "",stc1,186,3,88,16,NOT WS_GROUP | NOT WS_VISIBLE
    LISTBOX lst1,4,20,272,85,LBS_SORT | LBS_NOINTEGRALHEIGHT | LBS_MULTICOLUMN |
                    WS_HSCROLL | NOT WS_VISIBLE

    LTEXT "File &name:",stc3,5,112,48,8,SS_NOTIFY
    EDITTEXT edt1,54,111,155,12,WS_TABSTOP | ES_AUTOHSCROLL

    CONTROL "",cmb13,WC_COMBOBOXEX,WS_TABSTOP| WS_VSCROLL |
                     CBS_DROPDOWN | CBS_AUTOHSCROLL,54,111,155,150

    LTEXT "Files of &type:",stc2,5,131,48,8,SS_NOTIFY
    COMBOBOX cmb1,54,129,155,100,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP

    AUTOCHECKBOX "Open as &read-only",chx1,54,148,74,10,WS_TABSTOP

    DEFPUSHBUTTON "&Open",IDOK,222,110,50,14
    PUSHBUTTON "Cancel",IDCANCEL,222,128,50,14
    PUSHBUTTON "&Help",pshHelp,222,145,50,14
END

FILEOPENORD DIALOG LOADONCALL MOVEABLE DISCARDABLE 36,24,268,134
STYLE WS_CAPTION | WS_SYSMENU | WS_POPUP | DS_MODALFRAME |
      DS_CONTEXTHELP | DS_3DLOOK
CAPTION "Open"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "File &name:",stc3,6,6,76,9
    EDITTEXT edt1,6,16,90,12,ES_AUTOHSCROLL | WS_TABSTOP | ES_OEMCONVERT
    LISTBOX lst1,6,32,90,68,LBS_SORT | LBS_HASSTRINGS | LBS_NOTIFY |
                    LBS_DISABLENOSCROLL | WS_VSCROLL |
                    WS_TABSTOP | LBS_OWNERDRAWFIXED

    LTEXT "&Folders:",-1,110,6,96,9
    LTEXT "",stc1,110,16,96,9,SS_NOPREFIX
    LISTBOX lst2,110,32,96,68,LBS_SORT | LBS_HASSTRINGS | LBS_NOTIFY |
                    LBS_DISABLENOSCROLL | WS_VSCROLL |
                    WS_TABSTOP | LBS_OWNERDRAWFIXED

    LTEXT "List files of &type:",stc2,6,104,90,9
    COMBOBOX cmb1,6,114,90,96,CBS_DROPDOWNLIST | CBS_AUTOHSCROLL | WS_VSCROLL | WS_TABSTOP

    LTEXT "Dri&ves:",stc4,110,104,96,9
    COMBOBOX cmb2,110,114,96,68,CBS_SORT | CBS_HASSTRINGS | CBS_OWNERDRAWFIXED |
                    CBS_DROPDOWNLIST | CBS_AUTOHSCROLL | WS_VSCROLL | WS_TABSTOP

    DEFPUSHBUTTON "OK",IDOK,212,6,50,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,212,24,50,14,WS_GROUP

    PUSHBUTTON "&Help",pshHelp,212,46,50,14,WS_GROUP
    AUTOCHECKBOX "&Read only",chx1,212,68,50,12,WS_TABSTOP | WS_GROUP
END

MULTIFILEOPENORD DIALOG LOADONCALL MOVEABLE DISCARDABLE 36,24,268,134
STYLE WS_CAPTION | WS_SYSMENU | WS_POPUP | DS_MODALFRAME |
      DS_CONTEXTHELP | DS_3DLOOK
CAPTION "Open"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "File &name:",stc3,6,6,76,9
    EDITTEXT edt1,6,16,90,12,ES_AUTOHSCROLL | WS_TABSTOP | ES_OEMCONVERT
    LISTBOX lst1,6,32,90,68,LBS_SORT | LBS_NOTIFY | LBS_DISABLENOSCROLL | WS_VSCROLL |
                    WS_TABSTOP | LBS_EXTENDEDSEL | LBS_HASSTRINGS |
                    LBS_OWNERDRAWFIXED

    LTEXT "&Folders:",-1,110,6,96,9
    LTEXT "",stc1,110,16,96,9,SS_NOPREFIX
    LISTBOX lst2,110,32,96,68,LBS_SORT | LBS_HASSTRINGS | LBS_NOTIFY |
                    LBS_DISABLENOSCROLL | WS_VSCROLL |
                    WS_TABSTOP | LBS_OWNERDRAWFIXED

    LTEXT "List files of &type:",stc2,6,104,90,9
    COMBOBOX cmb1,6,114,90,96,CBS_DROPDOWNLIST | CBS_AUTOHSCROLL | WS_VSCROLL | WS_TABSTOP

    LTEXT "Dri&ves:",stc4,110,104,96,9
    COMBOBOX cmb2,110,114,96,68,CBS_SORT | CBS_HASSTRINGS | CBS_OWNERDRAWFIXED |
                    CBS_DROPDOWNLIST | CBS_AUTOHSCROLL | WS_VSCROLL | WS_TABSTOP

    DEFPUSHBUTTON "OK",IDOK,212,6,50,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,212,24,50,14,WS_GROUP

    PUSHBUTTON "&Help",pshHelp,212,46,50,14,WS_GROUP
    AUTOCHECKBOX "&Read only",chx1,212,68,50,12,WS_TABSTOP | WS_GROUP
END
