<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>zstd 1.5.7 Manual</title>
</head>
<body>
<h1>zstd 1.5.7 Manual</h1>
Note: the content of this file has been automatically generated by parsing "zstd.h" 
<hr>
<a name="Contents"></a><h2>Contents</h2>
<ol>
<li><a href="#Chapter1">Introduction</a></li>
<li><a href="#Chapter2">Version</a></li>
<li><a href="#Chapter3">Simple Core API</a></li>
<li><a href="#Chapter4">Explicit context</a></li>
<li><a href="#Chapter5">Advanced compression API (Requires v1.4.0+)</a></li>
<li><a href="#Chapter6">Advanced decompression API (Requires v1.4.0+)</a></li>
<li><a href="#Chapter7">Streaming</a></li>
<li><a href="#Chapter8">Streaming compression - HowTo</a></li>
<li><a href="#Chapter9">Streaming decompression - HowTo</a></li>
<li><a href="#Chapter10">Simple dictionary API</a></li>
<li><a href="#Chapter11">Bulk processing dictionary API</a></li>
<li><a href="#Chapter12">Dictionary helper functions</a></li>
<li><a href="#Chapter13">Advanced dictionary and prefix API (Requires v1.4.0+)</a></li>
<li><a href="#Chapter14">experimental API (static linking only)</a></li>
<li><a href="#Chapter15">Frame header and size functions</a></li>
<li><a href="#Chapter16">Memory management</a></li>
<li><a href="#Chapter17">Advanced compression functions</a></li>
<li><a href="#Chapter18">Advanced decompression functions</a></li>
<li><a href="#Chapter19">Advanced streaming functions</a></li>
<li><a href="#Chapter20">Buffer-less and synchronous inner streaming functions (DEPRECATED)</a></li>
<li><a href="#Chapter21">Buffer-less streaming compression (synchronous mode)</a></li>
<li><a href="#Chapter22">Buffer-less streaming decompression (synchronous mode)</a></li>
<li><a href="#Chapter23">Block level API (DEPRECATED)</a></li>
</ol>
<hr>
<a name="Chapter1"></a><h2>Introduction</h2><pre>
  zstd, short for Zstandard, is a fast lossless compression algorithm, targeting
  real-time compression scenarios at zlib-level and better compression ratios.
  The zstd compression library provides in-memory compression and decompression
  functions.

  The library supports regular compression levels from 1 up to ZSTD_maxCLevel(),
  which is currently 22. Levels >= 20, labeled `--ultra`, should be used with
  caution, as they require more memory. The library also offers negative
  compression levels, which extend the range of speed vs. ratio preferences.
  The lower the level, the faster the speed (at the cost of compression).

  Compression can be done in:
    - a single step (described as Simple API)
    - a single step, reusing a context (described as Explicit context)
    - unbounded multiple steps (described as Streaming compression)

  The compression ratio achievable on small data can be highly improved using
  a dictionary. Dictionary compression can be performed in:
    - a single step (described as Simple dictionary API)
    - a single step, reusing a dictionary (described as Bulk-processing
      dictionary API)

  Advanced experimental functions can be accessed using
  `#define ZSTD_STATIC_LINKING_ONLY` before including zstd.h.

  Advanced experimental APIs should never be used with a dynamically-linked
  library. They are not "stable"; their definitions or signatures may change in
  the future. Only static linking is allowed.
<BR></pre>

<a name="Chapter2"></a><h2>Version</h2><pre></pre>

<pre><b>unsigned ZSTD_versionNumber(void);
</b><p>  Return runtime library version, the value is (MAJOR*100*100 + MINOR*100 + RELEASE). 
</p></pre><BR>

<pre><b>const char* ZSTD_versionString(void);
</b><p>  Return runtime library version, like "1.4.5". Requires v1.3.0+. 
</p></pre><BR>

<a name="Chapter3"></a><h2>Simple Core API</h2><pre></pre>

<pre><b>size_t ZSTD_compress( void* dst, size_t dstCapacity,
                const void* src, size_t srcSize,
                      int compressionLevel);
</b><p>  Compresses `src` content as a single zstd compressed frame into already allocated `dst`.
  NOTE: Providing `dstCapacity >= ZSTD_compressBound(srcSize)` guarantees that zstd will have
        enough space to successfully compress the data.
  @return : compressed size written into `dst` (<= `dstCapacity),
            or an error code if it fails (which can be tested using ZSTD_isError()). 
</p></pre><BR>

<pre><b>size_t ZSTD_decompress( void* dst, size_t dstCapacity,
                  const void* src, size_t compressedSize);
</b><p> `compressedSize` : must be the _exact_ size of some number of compressed and/or skippable frames.
  Multiple compressed frames can be decompressed at once with this method.
  The result will be the concatenation of all decompressed frames, back to back.
 `dstCapacity` is an upper bound of originalSize to regenerate.
  First frame's decompressed size can be extracted using ZSTD_getFrameContentSize().
  If maximum upper bound isn't known, prefer using streaming mode to decompress data.
 @return : the number of bytes decompressed into `dst` (<= `dstCapacity`),
           or an errorCode if it fails (which can be tested using ZSTD_isError()). 
</p></pre><BR>

<h3>Decompression helper functions</h3><pre></pre><b><pre></pre></b><BR>
<pre><b>#define ZSTD_CONTENTSIZE_UNKNOWN (0ULL - 1)
#define ZSTD_CONTENTSIZE_ERROR   (0ULL - 2)
unsigned long long ZSTD_getFrameContentSize(const void *src, size_t srcSize);
</b><p> `src` should point to the start of a ZSTD encoded frame.
 `srcSize` must be at least as large as the frame header.
           hint : any size >= `ZSTD_frameHeaderSize_max` is large enough.
 @return : - decompressed size of `src` frame content, if known
           - ZSTD_CONTENTSIZE_UNKNOWN if the size cannot be determined
           - ZSTD_CONTENTSIZE_ERROR if an error occurred (e.g. invalid magic number, srcSize too small)
  note 1 : a 0 return value means the frame is valid but "empty".
           When invoking this method on a skippable frame, it will return 0.
  note 2 : decompressed size is an optional field, it may not be present (typically in streaming mode).
           When `return==ZSTD_CONTENTSIZE_UNKNOWN`, data to decompress could be any size.
           In which case, it's necessary to use streaming mode to decompress data.
           Optionally, application can rely on some implicit limit,
           as ZSTD_decompress() only needs an upper bound of decompressed size.
           (For example, data could be necessarily cut into blocks <= 16 KB).
  note 3 : decompressed size is always present when compression is completed using single-pass functions,
           such as ZSTD_compress(), ZSTD_compressCCtx() ZSTD_compress_usingDict() or ZSTD_compress_usingCDict().
  note 4 : decompressed size can be very large (64-bits value),
           potentially larger than what local system can handle as a single memory segment.
           In which case, it's necessary to use streaming mode to decompress data.
  note 5 : If source is untrusted, decompressed size could be wrong or intentionally modified.
           Always ensure return value fits within application's authorized limits.
           Each application can set its own limits.
  note 6 : This function replaces ZSTD_getDecompressedSize() 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("Replaced by ZSTD_getFrameContentSize")
unsigned long long ZSTD_getDecompressedSize(const void* src, size_t srcSize);
</b><p>  This function is now obsolete, in favor of ZSTD_getFrameContentSize().
  Both functions work the same way, but ZSTD_getDecompressedSize() blends
  "empty", "unknown" and "error" results to the same return value (0),
  while ZSTD_getFrameContentSize() gives them separate return values.
 @return : decompressed size of `src` frame content _if known and not empty_, 0 otherwise. 
</p></pre><BR>

<pre><b>size_t ZSTD_findFrameCompressedSize(const void* src, size_t srcSize);
</b><p> `src` should point to the start of a ZSTD frame or skippable frame.
 `srcSize` must be >= first frame size
 @return : the compressed size of the first frame starting at `src`,
           suitable to pass as `srcSize` to `ZSTD_decompress` or similar,
           or an error code if input is invalid
  Note 1: this method is called _find*() because it's not enough to read the header,
          it may have to scan through the frame's content, to reach its end.
  Note 2: this method also works with Skippable Frames. In which case,
          it returns the size of the complete skippable frame,
          which is always equal to its content size + 8 bytes for headers. 
</p></pre><BR>

<h3>Compression helper functions</h3><pre></pre><b><pre></pre></b><BR>
<pre><b>#define ZSTD_MAX_INPUT_SIZE ((sizeof(size_t)==8) ? 0xFF00FF00FF00FF00ULL : 0xFF00FF00U)
#define ZSTD_COMPRESSBOUND(srcSize)   (((size_t)(srcSize) >= ZSTD_MAX_INPUT_SIZE) ? 0 : (srcSize) + ((srcSize)>>8) + (((srcSize) < (128<<10)) ? (((128<<10) - (srcSize)) >> 11) </b>/* margin, from 64 to 0 */ : 0))  /* this formula ensures that bound(A) + bound(B) <= bound(A+B) as long as A and B >= 128 KB */<b>
size_t ZSTD_compressBound(size_t srcSize); </b>/*!< maximum compressed size in worst case single-pass scenario */<b>
</b><p> maximum compressed size in worst case single-pass scenario.
 When invoking `ZSTD_compress()`, or any other one-pass compression function,
 it's recommended to provide @dstCapacity >= ZSTD_compressBound(srcSize)
 as it eliminates one potential failure scenario,
 aka not enough room in dst buffer to write the compressed frame.
 Note : ZSTD_compressBound() itself can fail, if @srcSize >= ZSTD_MAX_INPUT_SIZE .
        In which case, ZSTD_compressBound() will return an error code
        which can be tested using ZSTD_isError().

 ZSTD_COMPRESSBOUND() :
 same as ZSTD_compressBound(), but as a macro.
 It can be used to produce constants, which can be useful for static allocation,
 for example to size a static array on stack.
 Will produce constant value 0 if srcSize is too large.
 
</p></pre><BR>

<h3>Error helper functions</h3><pre></pre><b><pre></b>/* ZSTD_isError() :<b>
 * Most ZSTD_* functions returning a size_t value can be tested for error,
 * using ZSTD_isError().
 * @return 1 if error, 0 otherwise
 */
unsigned     ZSTD_isError(size_t result);      </b>/*!< tells if a `size_t` function result is an error code */<b>
ZSTD_ErrorCode ZSTD_getErrorCode(size_t functionResult); </b>/* convert a result into an error code, which can be compared to error enum list */<b>
const char*  ZSTD_getErrorName(size_t result); </b>/*!< provides readable string from a function result */<b>
int          ZSTD_minCLevel(void);             </b>/*!< minimum negative compression level allowed, requires v1.4.0+ */<b>
int          ZSTD_maxCLevel(void);             </b>/*!< maximum compression level available */<b>
int          ZSTD_defaultCLevel(void);         </b>/*!< default compression level, specified by ZSTD_CLEVEL_DEFAULT, requires v1.5.0+ */<b>
</pre></b><BR>
<a name="Chapter4"></a><h2>Explicit context</h2><pre></pre>

<h3>Compression context</h3><pre>  When compressing many times,
  it is recommended to allocate a compression context just once,
  and reuse it for each successive compression operation.
  This will make the workload easier for system's memory.
  Note : re-using context is just a speed / resource optimization.
         It doesn't change the compression ratio, which remains identical.
  Note 2: For parallel execution in multi-threaded environments,
         use one different context per thread .
 
</pre><b><pre>typedef struct ZSTD_CCtx_s ZSTD_CCtx;
ZSTD_CCtx* ZSTD_createCCtx(void);
size_t     ZSTD_freeCCtx(ZSTD_CCtx* cctx);  </b>/* compatible with NULL pointer */<b>
</pre></b><BR>
<pre><b>size_t ZSTD_compressCCtx(ZSTD_CCtx* cctx,
                         void* dst, size_t dstCapacity,
                   const void* src, size_t srcSize,
                         int compressionLevel);
</b><p>  Same as ZSTD_compress(), using an explicit ZSTD_CCtx.
  Important : in order to mirror `ZSTD_compress()` behavior,
  this function compresses at the requested compression level,
  __ignoring any other advanced parameter__ .
  If any advanced parameter was set using the advanced API,
  they will all be reset. Only @compressionLevel remains.
 
</p></pre><BR>

<h3>Decompression context</h3><pre>  When decompressing many times,
  it is recommended to allocate a context only once,
  and reuse it for each successive compression operation.
  This will make workload friendlier for system's memory.
  Use one context per thread for parallel execution. 
</pre><b><pre>typedef struct ZSTD_DCtx_s ZSTD_DCtx;
ZSTD_DCtx* ZSTD_createDCtx(void);
size_t     ZSTD_freeDCtx(ZSTD_DCtx* dctx);  </b>/* accept NULL pointer */<b>
</pre></b><BR>
<pre><b>size_t ZSTD_decompressDCtx(ZSTD_DCtx* dctx,
                           void* dst, size_t dstCapacity,
                     const void* src, size_t srcSize);
</b><p>  Same as ZSTD_decompress(),
  requires an allocated ZSTD_DCtx.
  Compatible with sticky parameters (see below).
 
</p></pre><BR>

<a name="Chapter5"></a><h2>Advanced compression API (Requires v1.4.0+)</h2><pre></pre>

<pre><b>typedef enum { ZSTD_fast=1,
               ZSTD_dfast=2,
               ZSTD_greedy=3,
               ZSTD_lazy=4,
               ZSTD_lazy2=5,
               ZSTD_btlazy2=6,
               ZSTD_btopt=7,
               ZSTD_btultra=8,
               ZSTD_btultra2=9
               </b>/* note : new strategies _might_ be added in the future.<b>
                         Only the order (from fast to strong) is guaranteed */
} ZSTD_strategy;
</b></pre><BR>
<pre><b>typedef enum {

    </b>/* compression parameters<b>
     * Note: When compressing with a ZSTD_CDict these parameters are superseded
     * by the parameters used to construct the ZSTD_CDict.
     * See ZSTD_CCtx_refCDict() for more info (superseded-by-cdict). */
    ZSTD_c_compressionLevel=100, </b>/* Set compression parameters according to pre-defined cLevel table.<b>
                              * Note that exact compression parameters are dynamically determined,
                              * depending on both compression level and srcSize (when known).
                              * Default level is ZSTD_CLEVEL_DEFAULT==3.
                              * Special: value 0 means default, which is controlled by ZSTD_CLEVEL_DEFAULT.
                              * Note 1 : it's possible to pass a negative compression level.
                              * Note 2 : setting a level does not automatically set all other compression parameters
                              *   to default. Setting this will however eventually dynamically impact the compression
                              *   parameters which have not been manually set. The manually set
                              *   ones will 'stick'. */
    </b>/* Advanced compression parameters :<b>
     * It's possible to pin down compression parameters to some specific values.
     * In which case, these values are no longer dynamically selected by the compressor */
    ZSTD_c_windowLog=101,    </b>/* Maximum allowed back-reference distance, expressed as power of 2.<b>
                              * This will set a memory budget for streaming decompression,
                              * with larger values requiring more memory
                              * and typically compressing more.
                              * Must be clamped between ZSTD_WINDOWLOG_MIN and ZSTD_WINDOWLOG_MAX.
                              * Special: value 0 means "use default windowLog".
                              * Note: Using a windowLog greater than ZSTD_WINDOWLOG_LIMIT_DEFAULT
                              *       requires explicitly allowing such size at streaming decompression stage. */
    ZSTD_c_hashLog=102,      </b>/* Size of the initial probe table, as a power of 2.<b>
                              * Resulting memory usage is (1 << (hashLog+2)).
                              * Must be clamped between ZSTD_HASHLOG_MIN and ZSTD_HASHLOG_MAX.
                              * Larger tables improve compression ratio of strategies <= dFast,
                              * and improve speed of strategies > dFast.
                              * Special: value 0 means "use default hashLog". */
    ZSTD_c_chainLog=103,     </b>/* Size of the multi-probe search table, as a power of 2.<b>
                              * Resulting memory usage is (1 << (chainLog+2)).
                              * Must be clamped between ZSTD_CHAINLOG_MIN and ZSTD_CHAINLOG_MAX.
                              * Larger tables result in better and slower compression.
                              * This parameter is useless for "fast" strategy.
                              * It's still useful when using "dfast" strategy,
                              * in which case it defines a secondary probe table.
                              * Special: value 0 means "use default chainLog". */
    ZSTD_c_searchLog=104,    </b>/* Number of search attempts, as a power of 2.<b>
                              * More attempts result in better and slower compression.
                              * This parameter is useless for "fast" and "dFast" strategies.
                              * Special: value 0 means "use default searchLog". */
    ZSTD_c_minMatch=105,     </b>/* Minimum size of searched matches.<b>
                              * Note that Zstandard can still find matches of smaller size,
                              * it just tweaks its search algorithm to look for this size and larger.
                              * Larger values increase compression and decompression speed, but decrease ratio.
                              * Must be clamped between ZSTD_MINMATCH_MIN and ZSTD_MINMATCH_MAX.
                              * Note that currently, for all strategies < btopt, effective minimum is 4.
                              *                    , for all strategies > fast, effective maximum is 6.
                              * Special: value 0 means "use default minMatchLength". */
    ZSTD_c_targetLength=106, </b>/* Impact of this field depends on strategy.<b>
                              * For strategies btopt, btultra & btultra2:
                              *     Length of Match considered "good enough" to stop search.
                              *     Larger values make compression stronger, and slower.
                              * For strategy fast:
                              *     Distance between match sampling.
                              *     Larger values make compression faster, and weaker.
                              * Special: value 0 means "use default targetLength". */
    ZSTD_c_strategy=107,     </b>/* See ZSTD_strategy enum definition.<b>
                              * The higher the value of selected strategy, the more complex it is,
                              * resulting in stronger and slower compression.
                              * Special: value 0 means "use default strategy". */

    ZSTD_c_targetCBlockSize=130, </b>/* v1.5.6+<b>
                                  * Attempts to fit compressed block size into approximately targetCBlockSize.
                                  * Bound by ZSTD_TARGETCBLOCKSIZE_MIN and ZSTD_TARGETCBLOCKSIZE_MAX.
                                  * Note that it's not a guarantee, just a convergence target (default:0).
                                  * No target when targetCBlockSize == 0.
                                  * This is helpful in low bandwidth streaming environments to improve end-to-end latency,
                                  * when a client can make use of partial documents (a prominent example being Chrome).
                                  * Note: this parameter is stable since v1.5.6.
                                  * It was present as an experimental parameter in earlier versions,
                                  * but it's not recommended using it with earlier library versions
                                  * due to massive performance regressions.
                                  */
    </b>/* LDM mode parameters */<b>
    ZSTD_c_enableLongDistanceMatching=160, </b>/* Enable long distance matching.<b>
                                     * This parameter is designed to improve compression ratio
                                     * for large inputs, by finding large matches at long distance.
                                     * It increases memory usage and window size.
                                     * Note: enabling this parameter increases default ZSTD_c_windowLog to 128 MB
                                     * except when expressly set to a different value.
                                     * Note: will be enabled by default if ZSTD_c_windowLog >= 128 MB and
                                     * compression strategy >= ZSTD_btopt (== compression level 16+) */
    ZSTD_c_ldmHashLog=161,   </b>/* Size of the table for long distance matching, as a power of 2.<b>
                              * Larger values increase memory usage and compression ratio,
                              * but decrease compression speed.
                              * Must be clamped between ZSTD_HASHLOG_MIN and ZSTD_HASHLOG_MAX
                              * default: windowlog - 7.
                              * Special: value 0 means "automatically determine hashlog". */
    ZSTD_c_ldmMinMatch=162,  </b>/* Minimum match size for long distance matcher.<b>
                              * Larger/too small values usually decrease compression ratio.
                              * Must be clamped between ZSTD_LDM_MINMATCH_MIN and ZSTD_LDM_MINMATCH_MAX.
                              * Special: value 0 means "use default value" (default: 64). */
    ZSTD_c_ldmBucketSizeLog=163, </b>/* Log size of each bucket in the LDM hash table for collision resolution.<b>
                              * Larger values improve collision resolution but decrease compression speed.
                              * The maximum value is ZSTD_LDM_BUCKETSIZELOG_MAX.
                              * Special: value 0 means "use default value" (default: 3). */
    ZSTD_c_ldmHashRateLog=164, </b>/* Frequency of inserting/looking up entries into the LDM hash table.<b>
                              * Must be clamped between 0 and (ZSTD_WINDOWLOG_MAX - ZSTD_HASHLOG_MIN).
                              * Default is MAX(0, (windowLog - ldmHashLog)), optimizing hash table usage.
                              * Larger values improve compression speed.
                              * Deviating far from default value will likely result in a compression ratio decrease.
                              * Special: value 0 means "automatically determine hashRateLog". */

    </b>/* frame parameters */<b>
    ZSTD_c_contentSizeFlag=200, </b>/* Content size will be written into frame header _whenever known_ (default:1)<b>
                              * Content size must be known at the beginning of compression.
                              * This is automatically the case when using ZSTD_compress2(),
                              * For streaming scenarios, content size must be provided with ZSTD_CCtx_setPledgedSrcSize() */
    ZSTD_c_checksumFlag=201, </b>/* A 32-bits checksum of content is written at end of frame (default:0) */<b>
    ZSTD_c_dictIDFlag=202,   </b>/* When applicable, dictionary's ID is written into frame header (default:1) */<b>

    </b>/* multi-threading parameters */<b>
    </b>/* These parameters are only active if multi-threading is enabled (compiled with build macro ZSTD_MULTITHREAD).<b>
     * Otherwise, trying to set any other value than default (0) will be a no-op and return an error.
     * In a situation where it's unknown if the linked library supports multi-threading or not,
     * setting ZSTD_c_nbWorkers to any value >= 1 and consulting the return value provides a quick way to check this property.
     */
    ZSTD_c_nbWorkers=400,    </b>/* Select how many threads will be spawned to compress in parallel.<b>
                              * When nbWorkers >= 1, triggers asynchronous mode when invoking ZSTD_compressStream*() :
                              * ZSTD_compressStream*() consumes input and flush output if possible, but immediately gives back control to caller,
                              * while compression is performed in parallel, within worker thread(s).
                              * (note : a strong exception to this rule is when first invocation of ZSTD_compressStream2() sets ZSTD_e_end :
                              *  in which case, ZSTD_compressStream2() delegates to ZSTD_compress2(), which is always a blocking call).
                              * More workers improve speed, but also increase memory usage.
                              * Default value is `0`, aka "single-threaded mode" : no worker is spawned,
                              * compression is performed inside Caller's thread, and all invocations are blocking */
    ZSTD_c_jobSize=401,      </b>/* Size of a compression job. This value is enforced only when nbWorkers >= 1.<b>
                              * Each compression job is completed in parallel, so this value can indirectly impact the nb of active threads.
                              * 0 means default, which is dynamically determined based on compression parameters.
                              * Job size must be a minimum of overlap size, or ZSTDMT_JOBSIZE_MIN (= 512 KB), whichever is largest.
                              * The minimum size is automatically and transparently enforced. */
    ZSTD_c_overlapLog=402,   </b>/* Control the overlap size, as a fraction of window size.<b>
                              * The overlap size is an amount of data reloaded from previous job at the beginning of a new job.
                              * It helps preserve compression ratio, while each job is compressed in parallel.
                              * This value is enforced only when nbWorkers >= 1.
                              * Larger values increase compression ratio, but decrease speed.
                              * Possible values range from 0 to 9 :
                              * - 0 means "default" : value will be determined by the library, depending on strategy
                              * - 1 means "no overlap"
                              * - 9 means "full overlap", using a full window size.
                              * Each intermediate rank increases/decreases load size by a factor 2 :
                              * 9: full window;  8: w/2;  7: w/4;  6: w/8;  5:w/16;  4: w/32;  3:w/64;  2:w/128;  1:no overlap;  0:default
                              * default value varies between 6 and 9, depending on strategy */

    </b>/* note : additional experimental parameters are also available<b>
     * within the experimental section of the API.
     * At the time of this writing, they include :
     * ZSTD_c_rsyncable
     * ZSTD_c_format
     * ZSTD_c_forceMaxWindow
     * ZSTD_c_forceAttachDict
     * ZSTD_c_literalCompressionMode
     * ZSTD_c_srcSizeHint
     * ZSTD_c_enableDedicatedDictSearch
     * ZSTD_c_stableInBuffer
     * ZSTD_c_stableOutBuffer
     * ZSTD_c_blockDelimiters
     * ZSTD_c_validateSequences
     * ZSTD_c_blockSplitterLevel
     * ZSTD_c_splitAfterSequences
     * ZSTD_c_useRowMatchFinder
     * ZSTD_c_prefetchCDictTables
     * ZSTD_c_enableSeqProducerFallback
     * ZSTD_c_maxBlockSize
     * Because they are not stable, it's necessary to define ZSTD_STATIC_LINKING_ONLY to access them.
     * note : never ever use experimentalParam? names directly;
     *        also, the enums values themselves are unstable and can still change.
     */
     ZSTD_c_experimentalParam1=500,
     ZSTD_c_experimentalParam2=10,
     ZSTD_c_experimentalParam3=1000,
     ZSTD_c_experimentalParam4=1001,
     ZSTD_c_experimentalParam5=1002,
     </b>/* was ZSTD_c_experimentalParam6=1003; is now ZSTD_c_targetCBlockSize */<b>
     ZSTD_c_experimentalParam7=1004,
     ZSTD_c_experimentalParam8=1005,
     ZSTD_c_experimentalParam9=1006,
     ZSTD_c_experimentalParam10=1007,
     ZSTD_c_experimentalParam11=1008,
     ZSTD_c_experimentalParam12=1009,
     ZSTD_c_experimentalParam13=1010,
     ZSTD_c_experimentalParam14=1011,
     ZSTD_c_experimentalParam15=1012,
     ZSTD_c_experimentalParam16=1013,
     ZSTD_c_experimentalParam17=1014,
     ZSTD_c_experimentalParam18=1015,
     ZSTD_c_experimentalParam19=1016,
     ZSTD_c_experimentalParam20=1017
} ZSTD_cParameter;
</b></pre><BR>
<pre><b>typedef struct {
    size_t error;
    int lowerBound;
    int upperBound;
} ZSTD_bounds;
</b></pre><BR>
<pre><b>ZSTD_bounds ZSTD_cParam_getBounds(ZSTD_cParameter cParam);
</b><p>  All parameters must belong to an interval with lower and upper bounds,
  otherwise they will either trigger an error or be automatically clamped.
 @return : a structure, ZSTD_bounds, which contains
         - an error status field, which must be tested using ZSTD_isError()
         - lower and upper bounds, both inclusive
 
</p></pre><BR>

<pre><b>size_t ZSTD_CCtx_setParameter(ZSTD_CCtx* cctx, ZSTD_cParameter param, int value);
</b><p>  Set one compression parameter, selected by enum ZSTD_cParameter.
  All parameters have valid bounds. Bounds can be queried using ZSTD_cParam_getBounds().
  Providing a value beyond bound will either clamp it, or trigger an error (depending on parameter).
  Setting a parameter is generally only possible during frame initialization (before starting compression).
  Exception : when using multi-threading mode (nbWorkers >= 1),
              the following parameters can be updated _during_ compression (within same frame):
              => compressionLevel, hashLog, chainLog, searchLog, minMatch, targetLength and strategy.
              new parameters will be active for next job only (after a flush()).
 @return : an error code (which can be tested using ZSTD_isError()).
 
</p></pre><BR>

<pre><b>size_t ZSTD_CCtx_setPledgedSrcSize(ZSTD_CCtx* cctx, unsigned long long pledgedSrcSize);
</b><p>  Total input data size to be compressed as a single frame.
  Value will be written in frame header, unless if explicitly forbidden using ZSTD_c_contentSizeFlag.
  This value will also be controlled at end of frame, and trigger an error if not respected.
 @result : 0, or an error code (which can be tested with ZSTD_isError()).
  Note 1 : pledgedSrcSize==0 actually means zero, aka an empty frame.
           In order to mean "unknown content size", pass constant ZSTD_CONTENTSIZE_UNKNOWN.
           ZSTD_CONTENTSIZE_UNKNOWN is default value for any new frame.
  Note 2 : pledgedSrcSize is only valid once, for the next frame.
           It's discarded at the end of the frame, and replaced by ZSTD_CONTENTSIZE_UNKNOWN.
  Note 3 : Whenever all input data is provided and consumed in a single round,
           for example with ZSTD_compress2(),
           or invoking immediately ZSTD_compressStream2(,,,ZSTD_e_end),
           this value is automatically overridden by srcSize instead.
 
</p></pre><BR>

<pre><b>typedef enum {
    ZSTD_reset_session_only = 1,
    ZSTD_reset_parameters = 2,
    ZSTD_reset_session_and_parameters = 3
} ZSTD_ResetDirective;
</b></pre><BR>
<pre><b>size_t ZSTD_CCtx_reset(ZSTD_CCtx* cctx, ZSTD_ResetDirective reset);
</b><p>  There are 2 different things that can be reset, independently or jointly :
  - The session : will stop compressing current frame, and make CCtx ready to start a new one.
                  Useful after an error, or to interrupt any ongoing compression.
                  Any internal data not yet flushed is cancelled.
                  Compression parameters and dictionary remain unchanged.
                  They will be used to compress next frame.
                  Resetting session never fails.
  - The parameters : changes all parameters back to "default".
                  This also removes any reference to any dictionary or external sequence producer.
                  Parameters can only be changed between 2 sessions (i.e. no compression is currently ongoing)
                  otherwise the reset fails, and function returns an error value (which can be tested using ZSTD_isError())
  - Both : similar to resetting the session, followed by resetting parameters.
 
</p></pre><BR>

<pre><b>size_t ZSTD_compress2( ZSTD_CCtx* cctx,
                       void* dst, size_t dstCapacity,
                 const void* src, size_t srcSize);
</b><p>  Behave the same as ZSTD_compressCCtx(), but compression parameters are set using the advanced API.
  (note that this entry point doesn't even expose a compression level parameter).
  ZSTD_compress2() always starts a new frame.
  Should cctx hold data from a previously unfinished frame, everything about it is forgotten.
  - Compression parameters are pushed into CCtx before starting compression, using ZSTD_CCtx_set*()
  - The function is always blocking, returns when compression is completed.
  NOTE: Providing `dstCapacity >= ZSTD_compressBound(srcSize)` guarantees that zstd will have
        enough space to successfully compress the data, though it is possible it fails for other reasons.
 @return : compressed size written into `dst` (<= `dstCapacity),
           or an error code if it fails (which can be tested using ZSTD_isError()).
 
</p></pre><BR>

<a name="Chapter6"></a><h2>Advanced decompression API (Requires v1.4.0+)</h2><pre></pre>

<pre><b>typedef enum {

    ZSTD_d_windowLogMax=100, </b>/* Select a size limit (in power of 2) beyond which<b>
                              * the streaming API will refuse to allocate memory buffer
                              * in order to protect the host from unreasonable memory requirements.
                              * This parameter is only useful in streaming mode, since no internal buffer is allocated in single-pass mode.
                              * By default, a decompression context accepts window sizes <= (1 << ZSTD_WINDOWLOG_LIMIT_DEFAULT).
                              * Special: value 0 means "use default maximum windowLog". */

    </b>/* note : additional experimental parameters are also available<b>
     * within the experimental section of the API.
     * At the time of this writing, they include :
     * ZSTD_d_format
     * ZSTD_d_stableOutBuffer
     * ZSTD_d_forceIgnoreChecksum
     * ZSTD_d_refMultipleDDicts
     * ZSTD_d_disableHuffmanAssembly
     * ZSTD_d_maxBlockSize
     * Because they are not stable, it's necessary to define ZSTD_STATIC_LINKING_ONLY to access them.
     * note : never ever use experimentalParam? names directly
     */
     ZSTD_d_experimentalParam1=1000,
     ZSTD_d_experimentalParam2=1001,
     ZSTD_d_experimentalParam3=1002,
     ZSTD_d_experimentalParam4=1003,
     ZSTD_d_experimentalParam5=1004,
     ZSTD_d_experimentalParam6=1005

} ZSTD_dParameter;
</b></pre><BR>
<pre><b>ZSTD_bounds ZSTD_dParam_getBounds(ZSTD_dParameter dParam);
</b><p>  All parameters must belong to an interval with lower and upper bounds,
  otherwise they will either trigger an error or be automatically clamped.
 @return : a structure, ZSTD_bounds, which contains
         - an error status field, which must be tested using ZSTD_isError()
         - both lower and upper bounds, inclusive
 
</p></pre><BR>

<pre><b>size_t ZSTD_DCtx_setParameter(ZSTD_DCtx* dctx, ZSTD_dParameter param, int value);
</b><p>  Set one compression parameter, selected by enum ZSTD_dParameter.
  All parameters have valid bounds. Bounds can be queried using ZSTD_dParam_getBounds().
  Providing a value beyond bound will either clamp it, or trigger an error (depending on parameter).
  Setting a parameter is only possible during frame initialization (before starting decompression).
 @return : 0, or an error code (which can be tested using ZSTD_isError()).
 
</p></pre><BR>

<pre><b>size_t ZSTD_DCtx_reset(ZSTD_DCtx* dctx, ZSTD_ResetDirective reset);
</b><p>  Return a DCtx to clean state.
  Session and parameters can be reset jointly or separately.
  Parameters can only be reset when no active frame is being decompressed.
 @return : 0, or an error code, which can be tested with ZSTD_isError()
 
</p></pre><BR>

<a name="Chapter7"></a><h2>Streaming</h2><pre></pre>

<pre><b>typedef struct ZSTD_inBuffer_s {
  const void* src;    </b>/**< start of input buffer */<b>
  size_t size;        </b>/**< size of input buffer */<b>
  size_t pos;         </b>/**< position where reading stopped. Will be updated. Necessarily 0 <= pos <= size */<b>
} ZSTD_inBuffer;
</b></pre><BR>
<pre><b>typedef struct ZSTD_outBuffer_s {
  void*  dst;         </b>/**< start of output buffer */<b>
  size_t size;        </b>/**< size of output buffer */<b>
  size_t pos;         </b>/**< position where writing stopped. Will be updated. Necessarily 0 <= pos <= size */<b>
} ZSTD_outBuffer;
</b></pre><BR>
<a name="Chapter8"></a><h2>Streaming compression - HowTo</h2><pre>
  A ZSTD_CStream object is required to track streaming operation.
  Use ZSTD_createCStream() and ZSTD_freeCStream() to create/release resources.
  ZSTD_CStream objects can be reused multiple times on consecutive compression operations.
  It is recommended to reuse ZSTD_CStream since it will play nicer with system's memory, by re-using already allocated memory.

  For parallel execution, use one separate ZSTD_CStream per thread.

  note : since v1.3.0, ZSTD_CStream and ZSTD_CCtx are the same thing.

  Parameters are sticky : when starting a new compression on the same context,
  it will reuse the same sticky parameters as previous compression session.
  When in doubt, it's recommended to fully initialize the context before usage.
  Use ZSTD_CCtx_reset() to reset the context and ZSTD_CCtx_setParameter(),
  ZSTD_CCtx_setPledgedSrcSize(), or ZSTD_CCtx_loadDictionary() and friends to
  set more specific parameters, the pledged source size, or load a dictionary.

  Use ZSTD_compressStream2() with ZSTD_e_continue as many times as necessary to
  consume input stream. The function will automatically update both `pos`
  fields within `input` and `output`.
  Note that the function may not consume the entire input, for example, because
  the output buffer is already full, in which case `input.pos < input.size`.
  The caller must check if input has been entirely consumed.
  If not, the caller must make some room to receive more compressed data,
  and then present again remaining input data.
  note: ZSTD_e_continue is guaranteed to make some forward progress when called,
        but doesn't guarantee maximal forward progress. This is especially relevant
        when compressing with multiple threads. The call won't block if it can
        consume some input, but if it can't it will wait for some, but not all,
        output to be flushed.
 @return : provides a minimum amount of data remaining to be flushed from internal buffers
           or an error code, which can be tested using ZSTD_isError().

  At any moment, it's possible to flush whatever data might remain stuck within internal buffer,
  using ZSTD_compressStream2() with ZSTD_e_flush. `output->pos` will be updated.
  Note that, if `output->size` is too small, a single invocation with ZSTD_e_flush might not be enough (return code > 0).
  In which case, make some room to receive more compressed data, and call again ZSTD_compressStream2() with ZSTD_e_flush.
  You must continue calling ZSTD_compressStream2() with ZSTD_e_flush until it returns 0, at which point you can change the
  operation.
  note: ZSTD_e_flush will flush as much output as possible, meaning when compressing with multiple threads, it will
        block until the flush is complete or the output buffer is full.
  @return : 0 if internal buffers are entirely flushed,
            >0 if some data still present within internal buffer (the value is minimal estimation of remaining size),
            or an error code, which can be tested using ZSTD_isError().

  Calling ZSTD_compressStream2() with ZSTD_e_end instructs to finish a frame.
  It will perform a flush and write frame epilogue.
  The epilogue is required for decoders to consider a frame completed.
  flush operation is the same, and follows same rules as calling ZSTD_compressStream2() with ZSTD_e_flush.
  You must continue calling ZSTD_compressStream2() with ZSTD_e_end until it returns 0, at which point you are free to
  start a new frame.
  note: ZSTD_e_end will flush as much output as possible, meaning when compressing with multiple threads, it will
        block until the flush is complete or the output buffer is full.
  @return : 0 if frame fully completed and fully flushed,
            >0 if some data still present within internal buffer (the value is minimal estimation of remaining size),
            or an error code, which can be tested using ZSTD_isError().

 
<BR></pre>

<pre><b>typedef ZSTD_CCtx ZSTD_CStream;  </b>/**< CCtx and CStream are now effectively same object (>= v1.3.0) */<b>
</b></pre><BR>
<h3>ZSTD_CStream management functions</h3><pre></pre><b><pre>ZSTD_CStream* ZSTD_createCStream(void);
size_t ZSTD_freeCStream(ZSTD_CStream* zcs);  </b>/* accept NULL pointer */<b>
</pre></b><BR>
<h3>Streaming compression functions</h3><pre></pre><b><pre>typedef enum {
    ZSTD_e_continue=0, </b>/* collect more data, encoder decides when to output compressed result, for optimal compression ratio */<b>
    ZSTD_e_flush=1,    </b>/* flush any data provided so far,<b>
                        * it creates (at least) one new block, that can be decoded immediately on reception;
                        * frame will continue: any future data can still reference previously compressed data, improving compression.
                        * note : multithreaded compression will block to flush as much output as possible. */
    ZSTD_e_end=2       </b>/* flush any remaining data _and_ close current frame.<b>
                        * note that frame is only closed after compressed data is fully flushed (return value == 0).
                        * After that point, any additional data starts a new frame.
                        * note : each frame is independent (does not reference any content from previous frame).
                        : note : multithreaded compression will block to flush as much output as possible. */
} ZSTD_EndDirective;
</pre></b><BR>
<pre><b>size_t ZSTD_compressStream2( ZSTD_CCtx* cctx,
                             ZSTD_outBuffer* output,
                             ZSTD_inBuffer* input,
                             ZSTD_EndDirective endOp);
</b><p>  Behaves about the same as ZSTD_compressStream, with additional control on end directive.
  - Compression parameters are pushed into CCtx before starting compression, using ZSTD_CCtx_set*()
  - Compression parameters cannot be changed once compression is started (save a list of exceptions in multi-threading mode)
  - output->pos must be <= dstCapacity, input->pos must be <= srcSize
  - output->pos and input->pos will be updated. They are guaranteed to remain below their respective limit.
  - endOp must be a valid directive
  - When nbWorkers==0 (default), function is blocking : it completes its job before returning to caller.
  - When nbWorkers>=1, function is non-blocking : it copies a portion of input, distributes jobs to internal worker threads, flush to output whatever is available,
                                                  and then immediately returns, just indicating that there is some data remaining to be flushed.
                                                  The function nonetheless guarantees forward progress : it will return only after it reads or write at least 1+ byte.
  - Exception : if the first call requests a ZSTD_e_end directive and provides enough dstCapacity, the function delegates to ZSTD_compress2() which is always blocking.
  - @return provides a minimum amount of data remaining to be flushed from internal buffers
            or an error code, which can be tested using ZSTD_isError().
            if @return != 0, flush is not fully completed, there is still some data left within internal buffers.
            This is useful for ZSTD_e_flush, since in this case more flushes are necessary to empty all buffers.
            For ZSTD_e_end, @return == 0 when internal buffers are fully flushed and frame is completed.
  - after a ZSTD_e_end directive, if internal buffer is not fully flushed (@return != 0),
            only ZSTD_e_end or ZSTD_e_flush operations are allowed.
            Before starting a new compression job, or changing compression parameters,
            it is required to fully flush internal buffers.
  - note: if an operation ends with an error, it may leave @cctx in an undefined state.
          Therefore, it's UB to invoke ZSTD_compressStream2() of ZSTD_compressStream() on such a state.
          In order to be re-employed after an error, a state must be reset,
          which can be done explicitly (ZSTD_CCtx_reset()),
          or is sometimes implied by methods starting a new compression job (ZSTD_initCStream(), ZSTD_compressCCtx())
 
</p></pre><BR>

<pre><b>size_t ZSTD_CStreamInSize(void);    </b>/**< recommended size for input buffer */<b>
</b></pre><BR>
<pre><b>size_t ZSTD_CStreamOutSize(void);   </b>/**< recommended size for output buffer. Guarantee to successfully flush at least one complete compressed block. */<b>
</b></pre><BR>
<pre><b>size_t ZSTD_initCStream(ZSTD_CStream* zcs, int compressionLevel);
</b>/*!<b>
 * Alternative for ZSTD_compressStream2(zcs, output, input, ZSTD_e_continue).
 * NOTE: The return value is different. ZSTD_compressStream() returns a hint for
 * the next read size (if non-zero and not an error). ZSTD_compressStream2()
 * returns the minimum nb of bytes left to flush (if non-zero and not an error).
 */
size_t ZSTD_compressStream(ZSTD_CStream* zcs, ZSTD_outBuffer* output, ZSTD_inBuffer* input);
</b>/*! Equivalent to ZSTD_compressStream2(zcs, output, &emptyInput, ZSTD_e_flush). */<b>
size_t ZSTD_flushStream(ZSTD_CStream* zcs, ZSTD_outBuffer* output);
</b>/*! Equivalent to ZSTD_compressStream2(zcs, output, &emptyInput, ZSTD_e_end). */<b>
size_t ZSTD_endStream(ZSTD_CStream* zcs, ZSTD_outBuffer* output);
</b><p>
     ZSTD_CCtx_reset(zcs, ZSTD_reset_session_only);
     ZSTD_CCtx_refCDict(zcs, NULL); // clear the dictionary (if any)
     ZSTD_CCtx_setParameter(zcs, ZSTD_c_compressionLevel, compressionLevel);

 Note that ZSTD_initCStream() clears any previously set dictionary. Use the new API
 to compress with a dictionary.
 
</p></pre><BR>

<a name="Chapter9"></a><h2>Streaming decompression - HowTo</h2><pre>
  A ZSTD_DStream object is required to track streaming operations.
  Use ZSTD_createDStream() and ZSTD_freeDStream() to create/release resources.
  ZSTD_DStream objects can be re-employed multiple times.

  Use ZSTD_initDStream() to start a new decompression operation.
 @return : recommended first input size
  Alternatively, use advanced API to set specific properties.

  Use ZSTD_decompressStream() repetitively to consume your input.
  The function will update both `pos` fields.
  If `input.pos < input.size`, some input has not been consumed.
  It's up to the caller to present again remaining data.

  The function tries to flush all data decoded immediately, respecting output buffer size.
  If `output.pos < output.size`, decoder has flushed everything it could.

  However, when `output.pos == output.size`, it's more difficult to know.
  If @return > 0, the frame is not complete, meaning
  either there is still some data left to flush within internal buffers,
  or there is more input to read to complete the frame (or both).
  In which case, call ZSTD_decompressStream() again to flush whatever remains in the buffer.
  Note : with no additional input provided, amount of data flushed is necessarily <= ZSTD_BLOCKSIZE_MAX.
 @return : 0 when a frame is completely decoded and fully flushed,
        or an error code, which can be tested using ZSTD_isError(),
        or any other value > 0, which means there is still some decoding or flushing to do to complete current frame :
                                the return value is a suggested next input size (just a hint for better latency)
                                that will never request more than the remaining content of the compressed frame.
 
<BR></pre>

<pre><b>typedef ZSTD_DCtx ZSTD_DStream;  </b>/**< DCtx and DStream are now effectively same object (>= v1.3.0) */<b>
</b></pre><BR>
<h3>ZSTD_DStream management functions</h3><pre></pre><b><pre>ZSTD_DStream* ZSTD_createDStream(void);
size_t ZSTD_freeDStream(ZSTD_DStream* zds);  </b>/* accept NULL pointer */<b>
</pre></b><BR>
<h3>Streaming decompression functions</h3><pre></pre><b><pre></pre></b><BR>
<pre><b>size_t ZSTD_initDStream(ZSTD_DStream* zds);
</b><p> Initialize/reset DStream state for new decompression operation.
 Call before new decompression operation using same DStream.

 Note : This function is redundant with the advanced API and equivalent to:
     ZSTD_DCtx_reset(zds, ZSTD_reset_session_only);
     ZSTD_DCtx_refDDict(zds, NULL);
 
</p></pre><BR>

<pre><b>size_t ZSTD_decompressStream(ZSTD_DStream* zds, ZSTD_outBuffer* output, ZSTD_inBuffer* input);
</b><p> Streaming decompression function.
 Call repetitively to consume full input updating it as necessary.
 Function will update both input and output `pos` fields exposing current state via these fields:
 - `input.pos < input.size`, some input remaining and caller should provide remaining input
   on the next call.
 - `output.pos < output.size`, decoder flushed internal output buffer.
 - `output.pos == output.size`, unflushed data potentially present in the internal buffers,
   check ZSTD_decompressStream() @return value,
   if > 0, invoke it again to flush remaining data to output.
 Note : with no additional input, amount of data flushed <= ZSTD_BLOCKSIZE_MAX.

 @return : 0 when a frame is completely decoded and fully flushed,
           or an error code, which can be tested using ZSTD_isError(),
           or any other value > 0, which means there is some decoding or flushing to do to complete current frame.

 Note: when an operation returns with an error code, the @zds state may be left in undefined state.
       It's UB to invoke `ZSTD_decompressStream()` on such a state.
       In order to re-use such a state, it must be first reset,
       which can be done explicitly (`ZSTD_DCtx_reset()`),
       or is implied for operations starting some new decompression job (`ZSTD_initDStream`, `ZSTD_decompressDCtx()`, `ZSTD_decompress_usingDict()`)
 
</p></pre><BR>

<pre><b>size_t ZSTD_DStreamInSize(void);    </b>/*!< recommended size for input buffer */<b>
</b></pre><BR>
<pre><b>size_t ZSTD_DStreamOutSize(void);   </b>/*!< recommended size for output buffer. Guarantee to successfully flush at least one complete block in all circumstances. */<b>
</b></pre><BR>
<a name="Chapter10"></a><h2>Simple dictionary API</h2><pre></pre>

<pre><b>size_t ZSTD_compress_usingDict(ZSTD_CCtx* ctx,
                               void* dst, size_t dstCapacity,
                         const void* src, size_t srcSize,
                         const void* dict,size_t dictSize,
                               int compressionLevel);
</b><p>  Compression at an explicit compression level using a Dictionary.
  A dictionary can be any arbitrary data segment (also called a prefix),
  or a buffer with specified information (see zdict.h).
  Note : This function loads the dictionary, resulting in significant startup delay.
         It's intended for a dictionary used only once.
  Note 2 : When `dict == NULL || dictSize < 8` no dictionary is used. 
</p></pre><BR>

<pre><b>size_t ZSTD_decompress_usingDict(ZSTD_DCtx* dctx,
                                 void* dst, size_t dstCapacity,
                           const void* src, size_t srcSize,
                           const void* dict,size_t dictSize);
</b><p>  Decompression using a known Dictionary.
  Dictionary must be identical to the one used during compression.
  Note : This function loads the dictionary, resulting in significant startup delay.
         It's intended for a dictionary used only once.
  Note : When `dict == NULL || dictSize < 8` no dictionary is used. 
</p></pre><BR>

<a name="Chapter11"></a><h2>Bulk processing dictionary API</h2><pre></pre>

<pre><b>ZSTD_CDict* ZSTD_createCDict(const void* dictBuffer, size_t dictSize,
                             int compressionLevel);
</b><p>  When compressing multiple messages or blocks using the same dictionary,
  it's recommended to digest the dictionary only once, since it's a costly operation.
  ZSTD_createCDict() will create a state from digesting a dictionary.
  The resulting state can be used for future compression operations with very limited startup cost.
  ZSTD_CDict can be created once and shared by multiple threads concurrently, since its usage is read-only.
 @dictBuffer can be released after ZSTD_CDict creation, because its content is copied within CDict.
  Note 1 : Consider experimental function `ZSTD_createCDict_byReference()` if you prefer to not duplicate @dictBuffer content.
  Note 2 : A ZSTD_CDict can be created from an empty @dictBuffer,
      in which case the only thing that it transports is the @compressionLevel.
      This can be useful in a pipeline featuring ZSTD_compress_usingCDict() exclusively,
      expecting a ZSTD_CDict parameter with any data, including those without a known dictionary. 
</p></pre><BR>

<pre><b>size_t      ZSTD_freeCDict(ZSTD_CDict* CDict);
</b><p>  Function frees memory allocated by ZSTD_createCDict().
  If a NULL pointer is passed, no operation is performed. 
</p></pre><BR>

<pre><b>size_t ZSTD_compress_usingCDict(ZSTD_CCtx* cctx,
                                void* dst, size_t dstCapacity,
                          const void* src, size_t srcSize,
                          const ZSTD_CDict* cdict);
</b><p>  Compression using a digested Dictionary.
  Recommended when same dictionary is used multiple times.
  Note : compression level is _decided at dictionary creation time_,
     and frame parameters are hardcoded (dictID=yes, contentSize=yes, checksum=no) 
</p></pre><BR>

<pre><b>ZSTD_DDict* ZSTD_createDDict(const void* dictBuffer, size_t dictSize);
</b><p>  Create a digested dictionary, ready to start decompression operation without startup delay.
  dictBuffer can be released after DDict creation, as its content is copied inside DDict. 
</p></pre><BR>

<pre><b>size_t      ZSTD_freeDDict(ZSTD_DDict* ddict);
</b><p>  Function frees memory allocated with ZSTD_createDDict()
  If a NULL pointer is passed, no operation is performed. 
</p></pre><BR>

<pre><b>size_t ZSTD_decompress_usingDDict(ZSTD_DCtx* dctx,
                                  void* dst, size_t dstCapacity,
                            const void* src, size_t srcSize,
                            const ZSTD_DDict* ddict);
</b><p>  Decompression using a digested Dictionary.
  Recommended when same dictionary is used multiple times. 
</p></pre><BR>

<a name="Chapter12"></a><h2>Dictionary helper functions</h2><pre></pre>

<pre><b>unsigned ZSTD_getDictID_fromDict(const void* dict, size_t dictSize);
</b><p>  Provides the dictID stored within dictionary.
  if @return == 0, the dictionary is not conformant with Zstandard specification.
  It can still be loaded, but as a content-only dictionary. 
</p></pre><BR>

<pre><b>unsigned ZSTD_getDictID_fromCDict(const ZSTD_CDict* cdict);
</b><p>  Provides the dictID of the dictionary loaded into `cdict`.
  If @return == 0, the dictionary is not conformant to Zstandard specification, or empty.
  Non-conformant dictionaries can still be loaded, but as content-only dictionaries. 
</p></pre><BR>

<pre><b>unsigned ZSTD_getDictID_fromDDict(const ZSTD_DDict* ddict);
</b><p>  Provides the dictID of the dictionary loaded into `ddict`.
  If @return == 0, the dictionary is not conformant to Zstandard specification, or empty.
  Non-conformant dictionaries can still be loaded, but as content-only dictionaries. 
</p></pre><BR>

<pre><b>unsigned ZSTD_getDictID_fromFrame(const void* src, size_t srcSize);
</b><p>  Provides the dictID required to decompressed the frame stored within `src`.
  If @return == 0, the dictID could not be decoded.
  This could for one of the following reasons :
  - The frame does not require a dictionary to be decoded (most common case).
  - The frame was built with dictID intentionally removed. Whatever dictionary is necessary is a hidden piece of information.
    Note : this use case also happens when using a non-conformant dictionary.
  - `srcSize` is too small, and as a result, the frame header could not be decoded (only possible if `srcSize < ZSTD_FRAMEHEADERSIZE_MAX`).
  - This is not a Zstandard frame.
  When identifying the exact failure cause, it's possible to use ZSTD_getFrameHeader(), which will provide a more precise error code. 
</p></pre><BR>

<a name="Chapter13"></a><h2>Advanced dictionary and prefix API (Requires v1.4.0+)</h2><pre>
 This API allows dictionaries to be used with ZSTD_compress2(),
 ZSTD_compressStream2(), and ZSTD_decompressDCtx().
 Dictionaries are sticky, they remain valid when same context is reused,
 they only reset when the context is reset
 with ZSTD_reset_parameters or ZSTD_reset_session_and_parameters.
 In contrast, Prefixes are single-use.
<BR></pre>

<pre><b>size_t ZSTD_CCtx_loadDictionary(ZSTD_CCtx* cctx, const void* dict, size_t dictSize);
</b><p>  Create an internal CDict from `dict` buffer.
  Decompression will have to use same dictionary.
 @result : 0, or an error code (which can be tested with ZSTD_isError()).
  Special: Loading a NULL (or 0-size) dictionary invalidates previous dictionary,
           meaning "return to no-dictionary mode".
  Note 1 : Dictionary is sticky, it will be used for all future compressed frames,
           until parameters are reset, a new dictionary is loaded, or the dictionary
           is explicitly invalidated by loading a NULL dictionary.
  Note 2 : Loading a dictionary involves building tables.
           It's also a CPU consuming operation, with non-negligible impact on latency.
           Tables are dependent on compression parameters, and for this reason,
           compression parameters can no longer be changed after loading a dictionary.
  Note 3 :`dict` content will be copied internally.
           Use experimental ZSTD_CCtx_loadDictionary_byReference() to reference content instead.
           In such a case, dictionary buffer must outlive its users.
  Note 4 : Use ZSTD_CCtx_loadDictionary_advanced()
           to precisely select how dictionary content must be interpreted.
  Note 5 : This method does not benefit from LDM (long distance mode).
           If you want to employ LDM on some large dictionary content,
           prefer employing ZSTD_CCtx_refPrefix() described below.
 
</p></pre><BR>

<pre><b>size_t ZSTD_CCtx_refCDict(ZSTD_CCtx* cctx, const ZSTD_CDict* cdict);
</b><p>  Reference a prepared dictionary, to be used for all future compressed frames.
  Note that compression parameters are enforced from within CDict,
  and supersede any compression parameter previously set within CCtx.
  The parameters ignored are labelled as "superseded-by-cdict" in the ZSTD_cParameter enum docs.
  The ignored parameters will be used again if the CCtx is returned to no-dictionary mode.
  The dictionary will remain valid for future compressed frames using same CCtx.
 @result : 0, or an error code (which can be tested with ZSTD_isError()).
  Special : Referencing a NULL CDict means "return to no-dictionary mode".
  Note 1 : Currently, only one dictionary can be managed.
           Referencing a new dictionary effectively "discards" any previous one.
  Note 2 : CDict is just referenced, its lifetime must outlive its usage within CCtx. 
</p></pre><BR>

<pre><b>size_t ZSTD_CCtx_refPrefix(ZSTD_CCtx* cctx,
                     const void* prefix, size_t prefixSize);
</b><p>  Reference a prefix (single-usage dictionary) for next compressed frame.
  A prefix is **only used once**. Tables are discarded at end of frame (ZSTD_e_end).
  Decompression will need same prefix to properly regenerate data.
  Compressing with a prefix is similar in outcome as performing a diff and compressing it,
  but performs much faster, especially during decompression (compression speed is tunable with compression level).
  This method is compatible with LDM (long distance mode).
 @result : 0, or an error code (which can be tested with ZSTD_isError()).
  Special: Adding any prefix (including NULL) invalidates any previous prefix or dictionary
  Note 1 : Prefix buffer is referenced. It **must** outlive compression.
           Its content must remain unmodified during compression.
  Note 2 : If the intention is to diff some large src data blob with some prior version of itself,
           ensure that the window size is large enough to contain the entire source.
           See ZSTD_c_windowLog.
  Note 3 : Referencing a prefix involves building tables, which are dependent on compression parameters.
           It's a CPU consuming operation, with non-negligible impact on latency.
           If there is a need to use the same prefix multiple times, consider loadDictionary instead.
  Note 4 : By default, the prefix is interpreted as raw content (ZSTD_dct_rawContent).
           Use experimental ZSTD_CCtx_refPrefix_advanced() to alter dictionary interpretation. 
</p></pre><BR>

<pre><b>size_t ZSTD_DCtx_loadDictionary(ZSTD_DCtx* dctx, const void* dict, size_t dictSize);
</b><p>  Create an internal DDict from dict buffer, to be used to decompress all future frames.
  The dictionary remains valid for all future frames, until explicitly invalidated, or
  a new dictionary is loaded.
 @result : 0, or an error code (which can be tested with ZSTD_isError()).
  Special : Adding a NULL (or 0-size) dictionary invalidates any previous dictionary,
            meaning "return to no-dictionary mode".
  Note 1 : Loading a dictionary involves building tables,
           which has a non-negligible impact on CPU usage and latency.
           It's recommended to "load once, use many times", to amortize the cost
  Note 2 :`dict` content will be copied internally, so `dict` can be released after loading.
           Use ZSTD_DCtx_loadDictionary_byReference() to reference dictionary content instead.
  Note 3 : Use ZSTD_DCtx_loadDictionary_advanced() to take control of
           how dictionary content is loaded and interpreted.
 
</p></pre><BR>

<pre><b>size_t ZSTD_DCtx_refDDict(ZSTD_DCtx* dctx, const ZSTD_DDict* ddict);
</b><p>  Reference a prepared dictionary, to be used to decompress next frames.
  The dictionary remains active for decompression of future frames using same DCtx.

  If called with ZSTD_d_refMultipleDDicts enabled, repeated calls of this function
  will store the DDict references in a table, and the DDict used for decompression
  will be determined at decompression time, as per the dict ID in the frame.
  The memory for the table is allocated on the first call to refDDict, and can be
  freed with ZSTD_freeDCtx().

  If called with ZSTD_d_refMultipleDDicts disabled (the default), only one dictionary
  will be managed, and referencing a dictionary effectively "discards" any previous one.

 @result : 0, or an error code (which can be tested with ZSTD_isError()).
  Special: referencing a NULL DDict means "return to no-dictionary mode".
  Note 2 : DDict is just referenced, its lifetime must outlive its usage from DCtx.
 
</p></pre><BR>

<pre><b>size_t ZSTD_DCtx_refPrefix(ZSTD_DCtx* dctx,
                     const void* prefix, size_t prefixSize);
</b><p>  Reference a prefix (single-usage dictionary) to decompress next frame.
  This is the reverse operation of ZSTD_CCtx_refPrefix(),
  and must use the same prefix as the one used during compression.
  Prefix is **only used once**. Reference is discarded at end of frame.
  End of frame is reached when ZSTD_decompressStream() returns 0.
 @result : 0, or an error code (which can be tested with ZSTD_isError()).
  Note 1 : Adding any prefix (including NULL) invalidates any previously set prefix or dictionary
  Note 2 : Prefix buffer is referenced. It **must** outlive decompression.
           Prefix buffer must remain unmodified up to the end of frame,
           reached when ZSTD_decompressStream() returns 0.
  Note 3 : By default, the prefix is treated as raw content (ZSTD_dct_rawContent).
           Use ZSTD_CCtx_refPrefix_advanced() to alter dictMode (Experimental section)
  Note 4 : Referencing a raw content prefix has almost no cpu nor memory cost.
           A full dictionary is more costly, as it requires building tables.
 
</p></pre><BR>

<pre><b>size_t ZSTD_sizeof_CCtx(const ZSTD_CCtx* cctx);
size_t ZSTD_sizeof_DCtx(const ZSTD_DCtx* dctx);
size_t ZSTD_sizeof_CStream(const ZSTD_CStream* zcs);
size_t ZSTD_sizeof_DStream(const ZSTD_DStream* zds);
size_t ZSTD_sizeof_CDict(const ZSTD_CDict* cdict);
size_t ZSTD_sizeof_DDict(const ZSTD_DDict* ddict);
</b><p>  These functions give the _current_ memory usage of selected object.
  Note that object memory usage can evolve (increase or decrease) over time. 
</p></pre><BR>

<a name="Chapter14"></a><h2>experimental API (static linking only)</h2><pre>
 The following symbols and constants
 are not planned to join "stable API" status in the near future.
 They can still change in future versions.
 Some of them are planned to remain in the static_only section indefinitely.
 Some of them might be removed in the future (especially when redundant with existing stable functions)
 
<BR></pre>

<pre><b>typedef struct {
    unsigned int offset;      </b>/* The offset of the match. (NOT the same as the offset code)<b>
                               * If offset == 0 and matchLength == 0, this sequence represents the last
                               * literals in the block of litLength size.
                               */

    unsigned int litLength;   </b>/* Literal length of the sequence. */<b>
    unsigned int matchLength; </b>/* Match length of the sequence. */<b>

                              </b>/* Note: Users of this API may provide a sequence with matchLength == litLength == offset == 0.<b>
                               * In this case, we will treat the sequence as a marker for a block boundary.
                               */

    unsigned int rep;         </b>/* Represents which repeat offset is represented by the field 'offset'.<b>
                               * Ranges from [0, 3].
                               *
                               * Repeat offsets are essentially previous offsets from previous sequences sorted in
                               * recency order. For more detail, see doc/zstd_compression_format.md
                               *
                               * If rep == 0, then 'offset' does not contain a repeat offset.
                               * If rep > 0:
                               *  If litLength != 0:
                               *      rep == 1 --> offset == repeat_offset_1
                               *      rep == 2 --> offset == repeat_offset_2
                               *      rep == 3 --> offset == repeat_offset_3
                               *  If litLength == 0:
                               *      rep == 1 --> offset == repeat_offset_2
                               *      rep == 2 --> offset == repeat_offset_3
                               *      rep == 3 --> offset == repeat_offset_1 - 1
                               *
                               * Note: This field is optional. ZSTD_generateSequences() will calculate the value of
                               * 'rep', but repeat offsets do not necessarily need to be calculated from an external
                               * sequence provider perspective. For example, ZSTD_compressSequences() does not
                               * use this 'rep' field at all (as of now).
                               */
} ZSTD_Sequence;
</b></pre><BR>
<pre><b>typedef struct {
    unsigned windowLog;       </b>/**< largest match distance : larger == more compression, more memory needed during decompression */<b>
    unsigned chainLog;        </b>/**< fully searched segment : larger == more compression, slower, more memory (useless for fast) */<b>
    unsigned hashLog;         </b>/**< dispatch table : larger == faster, more memory */<b>
    unsigned searchLog;       </b>/**< nb of searches : larger == more compression, slower */<b>
    unsigned minMatch;        </b>/**< match length searched : larger == faster decompression, sometimes less compression */<b>
    unsigned targetLength;    </b>/**< acceptable match size for optimal parser (only) : larger == more compression, slower */<b>
    ZSTD_strategy strategy;   </b>/**< see ZSTD_strategy definition above */<b>
} ZSTD_compressionParameters;
</b></pre><BR>
<pre><b>typedef struct {
    int contentSizeFlag; </b>/**< 1: content size will be in frame header (when known) */<b>
    int checksumFlag;    </b>/**< 1: generate a 32-bits checksum using XXH64 algorithm at end of frame, for error detection */<b>
    int noDictIDFlag;    </b>/**< 1: no dictID will be saved into frame header (dictID is only useful for dictionary compression) */<b>
} ZSTD_frameParameters;
</b></pre><BR>
<pre><b>typedef struct {
    ZSTD_compressionParameters cParams;
    ZSTD_frameParameters fParams;
} ZSTD_parameters;
</b></pre><BR>
<pre><b>typedef enum {
    ZSTD_dct_auto = 0,       </b>/* dictionary is "full" when starting with ZSTD_MAGIC_DICTIONARY, otherwise it is "rawContent" */<b>
    ZSTD_dct_rawContent = 1, </b>/* ensures dictionary is always loaded as rawContent, even if it starts with ZSTD_MAGIC_DICTIONARY */<b>
    ZSTD_dct_fullDict = 2    </b>/* refuses to load a dictionary if it does not respect Zstandard's specification, starting with ZSTD_MAGIC_DICTIONARY */<b>
} ZSTD_dictContentType_e;
</b></pre><BR>
<pre><b>typedef enum {
    ZSTD_dlm_byCopy = 0,  </b>/**< Copy dictionary content internally */<b>
    ZSTD_dlm_byRef = 1    </b>/**< Reference dictionary content -- the dictionary buffer must outlive its users. */<b>
} ZSTD_dictLoadMethod_e;
</b></pre><BR>
<pre><b>typedef enum {
    ZSTD_f_zstd1 = 0,           </b>/* zstd frame format, specified in zstd_compression_format.md (default) */<b>
    ZSTD_f_zstd1_magicless = 1  </b>/* Variant of zstd frame format, without initial 4-bytes magic number.<b>
                                 * Useful to save 4 bytes per generated frame.
                                 * Decoder cannot recognise automatically this format, requiring this instruction. */
} ZSTD_format_e;
</b></pre><BR>
<pre><b>typedef enum {
    </b>/* Note: this enum controls ZSTD_d_forceIgnoreChecksum */<b>
    ZSTD_d_validateChecksum = 0,
    ZSTD_d_ignoreChecksum = 1
} ZSTD_forceIgnoreChecksum_e;
</b></pre><BR>
<pre><b>typedef enum {
    </b>/* Note: this enum controls ZSTD_d_refMultipleDDicts */<b>
    ZSTD_rmd_refSingleDDict = 0,
    ZSTD_rmd_refMultipleDDicts = 1
} ZSTD_refMultipleDDicts_e;
</b></pre><BR>
<pre><b>typedef enum {
    </b>/* Note: this enum and the behavior it controls are effectively internal<b>
     * implementation details of the compressor. They are expected to continue
     * to evolve and should be considered only in the context of extremely
     * advanced performance tuning.
     *
     * Zstd currently supports the use of a CDict in three ways:
     *
     * - The contents of the CDict can be copied into the working context. This
     *   means that the compression can search both the dictionary and input
     *   while operating on a single set of internal tables. This makes
     *   the compression faster per-byte of input. However, the initial copy of
     *   the CDict's tables incurs a fixed cost at the beginning of the
     *   compression. For small compressions (< 8 KB), that copy can dominate
     *   the cost of the compression.
     *
     * - The CDict's tables can be used in-place. In this model, compression is
     *   slower per input byte, because the compressor has to search two sets of
     *   tables. However, this model incurs no start-up cost (as long as the
     *   working context's tables can be reused). For small inputs, this can be
     *   faster than copying the CDict's tables.
     *
     * - The CDict's tables are not used at all, and instead we use the working
     *   context alone to reload the dictionary and use params based on the source
     *   size. See ZSTD_compress_insertDictionary() and ZSTD_compress_usingDict().
     *   This method is effective when the dictionary sizes are very small relative
     *   to the input size, and the input size is fairly large to begin with.
     *
     * Zstd has a simple internal heuristic that selects which strategy to use
     * at the beginning of a compression. However, if experimentation shows that
     * Zstd is making poor choices, it is possible to override that choice with
     * this enum.
     */
    ZSTD_dictDefaultAttach = 0, </b>/* Use the default heuristic. */<b>
    ZSTD_dictForceAttach   = 1, </b>/* Never copy the dictionary. */<b>
    ZSTD_dictForceCopy     = 2, </b>/* Always copy the dictionary. */<b>
    ZSTD_dictForceLoad     = 3  </b>/* Always reload the dictionary */<b>
} ZSTD_dictAttachPref_e;
</b></pre><BR>
<pre><b>typedef enum {
  ZSTD_lcm_auto = 0,          </b>/**< Automatically determine the compression mode based on the compression level.<b>
                               *   Negative compression levels will be uncompressed, and positive compression
                               *   levels will be compressed. */
  ZSTD_lcm_huffman = 1,       </b>/**< Always attempt Huffman compression. Uncompressed literals will still be<b>
                               *   emitted if Huffman compression is not profitable. */
  ZSTD_lcm_uncompressed = 2   </b>/**< Always emit uncompressed literals. */<b>
} ZSTD_literalCompressionMode_e;
</b></pre><BR>
<pre><b>typedef enum {
  </b>/* Note: This enum controls features which are conditionally beneficial.<b>
   * Zstd can take a decision on whether or not to enable the feature (ZSTD_ps_auto),
   * but setting the switch to ZSTD_ps_enable or ZSTD_ps_disable force enable/disable the feature.
   */
  ZSTD_ps_auto = 0,         </b>/* Let the library automatically determine whether the feature shall be enabled */<b>
  ZSTD_ps_enable = 1,       </b>/* Force-enable the feature */<b>
  ZSTD_ps_disable = 2       </b>/* Do not use the feature */<b>
} ZSTD_ParamSwitch_e;
</b></pre><BR>
<a name="Chapter15"></a><h2>Frame header and size functions</h2><pre></pre>

<pre><b>ZSTDLIB_STATIC_API unsigned long long ZSTD_findDecompressedSize(const void* src, size_t srcSize);
</b><p>  `src` should point to the start of a series of ZSTD encoded and/or skippable frames
  `srcSize` must be the _exact_ size of this series
       (i.e. there should be a frame boundary at `src + srcSize`)
  @return : - decompressed size of all data in all successive frames
            - if the decompressed size cannot be determined: ZSTD_CONTENTSIZE_UNKNOWN
            - if an error occurred: ZSTD_CONTENTSIZE_ERROR

   note 1 : decompressed size is an optional field, that may not be present, especially in streaming mode.
            When `return==ZSTD_CONTENTSIZE_UNKNOWN`, data to decompress could be any size.
            In which case, it's necessary to use streaming mode to decompress data.
   note 2 : decompressed size is always present when compression is done with ZSTD_compress()
   note 3 : decompressed size can be very large (64-bits value),
            potentially larger than what local system can handle as a single memory segment.
            In which case, it's necessary to use streaming mode to decompress data.
   note 4 : If source is untrusted, decompressed size could be wrong or intentionally modified.
            Always ensure result fits within application's authorized limits.
            Each application can set its own limits.
   note 5 : ZSTD_findDecompressedSize handles multiple frames, and so it must traverse the input to
            read each contained frame header.  This is fast as most of the data is skipped,
            however it does mean that all frame data must be present and valid. 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API unsigned long long ZSTD_decompressBound(const void* src, size_t srcSize);
</b><p>  `src` should point to the start of a series of ZSTD encoded and/or skippable frames
  `srcSize` must be the _exact_ size of this series
       (i.e. there should be a frame boundary at `src + srcSize`)
  @return : - upper-bound for the decompressed size of all data in all successive frames
            - if an error occurred: ZSTD_CONTENTSIZE_ERROR

  note 1  : an error can occur if `src` contains an invalid or incorrectly formatted frame.
  note 2  : the upper-bound is exact when the decompressed size field is available in every ZSTD encoded frame of `src`.
            in this case, `ZSTD_findDecompressedSize` and `ZSTD_decompressBound` return the same value.
  note 3  : when the decompressed size field isn't available, the upper-bound for that frame is calculated by:
              upper-bound = # blocks * min(128 KB, Window_Size)
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_frameHeaderSize(const void* src, size_t srcSize);
</b><p>  srcSize must be large enough, aka >= ZSTD_FRAMEHEADERSIZE_PREFIX.
 @return : size of the Frame Header,
           or an error code (if srcSize is too small) 
</p></pre><BR>

<pre><b>typedef enum { ZSTD_frame, ZSTD_skippableFrame } ZSTD_FrameType_e;
</b></pre><BR>
<pre><b>typedef struct {
    unsigned long long frameContentSize; </b>/* if == ZSTD_CONTENTSIZE_UNKNOWN, it means this field is not available. 0 means "empty" */<b>
    unsigned long long windowSize;       </b>/* can be very large, up to <= frameContentSize */<b>
    unsigned blockSizeMax;
    ZSTD_FrameType_e frameType;          </b>/* if == ZSTD_skippableFrame, frameContentSize is the size of skippable content */<b>
    unsigned headerSize;
    unsigned dictID;                     </b>/* for ZSTD_skippableFrame, contains the skippable magic variant [0-15] */<b>
    unsigned checksumFlag;
    unsigned _reserved1;
    unsigned _reserved2;
} ZSTD_FrameHeader;
</b></pre><BR>
<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_getFrameHeader(ZSTD_FrameHeader* zfhPtr, const void* src, size_t srcSize);
</b>/*! ZSTD_getFrameHeader_advanced() :<b>
 *  same as ZSTD_getFrameHeader(),
 *  with added capability to select a format (like ZSTD_f_zstd1_magicless) */
ZSTDLIB_STATIC_API size_t ZSTD_getFrameHeader_advanced(ZSTD_FrameHeader* zfhPtr, const void* src, size_t srcSize, ZSTD_format_e format);
</b><p>  decode Frame Header into `zfhPtr`, or requires larger `srcSize`.
 @return : 0 => header is complete, `zfhPtr` is correctly filled,
          >0 => `srcSize` is too small, @return value is the wanted `srcSize` amount, `zfhPtr` is not filled,
           or an error code, which can be tested using ZSTD_isError() 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_decompressionMargin(const void* src, size_t srcSize);
</b><p> Zstd supports in-place decompression, where the input and output buffers overlap.
 In this case, the output buffer must be at least (Margin + Output_Size) bytes large,
 and the input buffer must be at the end of the output buffer.

  _______________________ Output Buffer ________________________
 |                                                              |
 |                                        ____ Input Buffer ____|
 |                                       |                      |
 v                                       v                      v
 |---------------------------------------|-----------|----------|
 ^                                                   ^          ^
 |___________________ Output_Size ___________________|_ Margin _|

 NOTE: See also ZSTD_DECOMPRESSION_MARGIN().
 NOTE: This applies only to single-pass decompression through ZSTD_decompress() or
 ZSTD_decompressDCtx().
 NOTE: This function supports multi-frame input.

 @param src The compressed frame(s)
 @param srcSize The size of the compressed frame(s)
 @returns The decompression margin or an error that can be checked with ZSTD_isError().
 
</p></pre><BR>

<pre><b>#define ZSTD_DECOMPRESSION_MARGIN(originalSize, blockSize) ((size_t)(                                              \
        ZSTD_FRAMEHEADERSIZE_MAX                                                              </b>/* Frame header */ + \<b>
        4                                                                                         </b>/* checksum */ + \<b>
        ((originalSize) == 0 ? 0 : 3 * (((originalSize) + (blockSize) - 1) / blockSize)) </b>/* 3 bytes per block */ + \<b>
        (blockSize)                                                                    </b>/* One block of margin */   \<b>
    ))
</b><p> Similar to ZSTD_decompressionMargin(), but instead of computing the margin from
 the compressed frame, compute it from the original size and the blockSizeLog.
 See ZSTD_decompressionMargin() for details.

 WARNING: This macro does not support multi-frame input, the input must be a single
 zstd frame. If you need that support use the function, or implement it yourself.

 @param originalSize The original uncompressed size of the data.
 @param blockSize    The block size == MIN(windowSize, ZSTD_BLOCKSIZE_MAX).
                     Unless you explicitly set the windowLog smaller than
                     ZSTD_BLOCKSIZELOG_MAX you can just use ZSTD_BLOCKSIZE_MAX.
 
</p></pre><BR>

<pre><b>typedef enum {
  ZSTD_sf_noBlockDelimiters = 0,         </b>/* ZSTD_Sequence[] has no block delimiters, just sequences */<b>
  ZSTD_sf_explicitBlockDelimiters = 1    </b>/* ZSTD_Sequence[] contains explicit block delimiters */<b>
} ZSTD_SequenceFormat_e;
</b></pre><BR>
<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_sequenceBound(size_t srcSize);
</b><p> `srcSize` : size of the input buffer
  @return : upper-bound for the number of sequences that can be generated
            from a buffer of srcSize bytes

  note : returns number of sequences - to get bytes, multiply by sizeof(ZSTD_Sequence).
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("For debugging only, will be replaced by ZSTD_extractSequences()")
ZSTDLIB_STATIC_API size_t
ZSTD_generateSequences(ZSTD_CCtx* zc,
           ZSTD_Sequence* outSeqs, size_t outSeqsCapacity,
           const void* src, size_t srcSize);
</b><p> WARNING: This function is meant for debugging and informational purposes ONLY!
 Its implementation is flawed, and it will be deleted in a future version.
 It is not guaranteed to succeed, as there are several cases where it will give
 up and fail. You should NOT use this function in production code.

 This function is deprecated, and will be removed in a future version.

 Generate sequences using ZSTD_compress2(), given a source buffer.

 @param zc The compression context to be used for ZSTD_compress2(). Set any
           compression parameters you need on this context.
 @param outSeqs The output sequences buffer of size @p outSeqsSize
 @param outSeqsCapacity The size of the output sequences buffer.
                    ZSTD_sequenceBound(srcSize) is an upper bound on the number
                    of sequences that can be generated.
 @param src The source buffer to generate sequences from of size @p srcSize.
 @param srcSize The size of the source buffer.

 Each block will end with a dummy sequence
 with offset == 0, matchLength == 0, and litLength == length of last literals.
 litLength may be == 0, and if so, then the sequence of (of: 0 ml: 0 ll: 0)
 simply acts as a block delimiter.

 @returns The number of sequences generated, necessarily less than
          ZSTD_sequenceBound(srcSize), or an error code that can be checked
          with ZSTD_isError().
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_mergeBlockDelimiters(ZSTD_Sequence* sequences, size_t seqsSize);
</b><p> Given an array of ZSTD_Sequence, remove all sequences that represent block delimiters/last literals
 by merging them into the literals of the next sequence.

 As such, the final generated result has no explicit representation of block boundaries,
 and the final last literals segment is not represented in the sequences.

 The output of this function can be fed into ZSTD_compressSequences() with CCtx
 setting of ZSTD_c_blockDelimiters as ZSTD_sf_noBlockDelimiters
 @return : number of sequences left after merging
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t
ZSTD_compressSequences(ZSTD_CCtx* cctx,
           void* dst, size_t dstCapacity,
     const ZSTD_Sequence* inSeqs, size_t inSeqsSize,
     const void* src, size_t srcSize);
</b><p> Compress an array of ZSTD_Sequence, associated with @src buffer, into dst.
 @src contains the entire input (not just the literals).
 If @srcSize > sum(sequence.length), the remaining bytes are considered all literals
 If a dictionary is included, then the cctx should reference the dict (see: ZSTD_CCtx_refCDict(), ZSTD_CCtx_loadDictionary(), etc.).
 The entire source is compressed into a single frame.

 The compression behavior changes based on cctx params. In particular:
    If ZSTD_c_blockDelimiters == ZSTD_sf_noBlockDelimiters, the array of ZSTD_Sequence is expected to contain
    no block delimiters (defined in ZSTD_Sequence). Block boundaries are roughly determined based on
    the block size derived from the cctx, and sequences may be split. This is the default setting.

    If ZSTD_c_blockDelimiters == ZSTD_sf_explicitBlockDelimiters, the array of ZSTD_Sequence is expected to contain
    valid block delimiters (defined in ZSTD_Sequence). Behavior is undefined if no block delimiters are provided.

    When ZSTD_c_blockDelimiters == ZSTD_sf_explicitBlockDelimiters, it's possible to decide generating repcodes
    using the advanced parameter ZSTD_c_repcodeResolution. Repcodes will improve compression ratio, though the benefit
    can vary greatly depending on Sequences. On the other hand, repcode resolution is an expensive operation.
    By default, it's disabled at low (<10) compression levels, and enabled above the threshold (>=10).
    ZSTD_c_repcodeResolution makes it possible to directly manage this processing in either direction.

    If ZSTD_c_validateSequences == 0, this function blindly accepts the Sequences provided. Invalid Sequences cause undefined
    behavior. If ZSTD_c_validateSequences == 1, then the function will detect invalid Sequences (see doc/zstd_compression_format.md for
    specifics regarding offset/matchlength requirements) and then bail out and return an error.

    In addition to the two adjustable experimental params, there are other important cctx params.
    - ZSTD_c_minMatch MUST be set as less than or equal to the smallest match generated by the match finder. It has a minimum value of ZSTD_MINMATCH_MIN.
    - ZSTD_c_compressionLevel accordingly adjusts the strength of the entropy coder, as it would in typical compression.
    - ZSTD_c_windowLog affects offset validation: this function will return an error at higher debug levels if a provided offset
      is larger than what the spec allows for a given window log and dictionary (if present). See: doc/zstd_compression_format.md

 Note: Repcodes are, as of now, always re-calculated within this function, ZSTD_Sequence.rep is effectively unused.
 Dev Note: Once ability to ingest repcodes become available, the explicit block delims mode must respect those repcodes exactly,
         and cannot emit an RLE block that disagrees with the repcode history.
 @return : final compressed size, or a ZSTD error code.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t
ZSTD_compressSequencesAndLiterals(ZSTD_CCtx* cctx,
                      void* dst, size_t dstCapacity,
                const ZSTD_Sequence* inSeqs, size_t nbSequences,
                const void* literals, size_t litSize, size_t litBufCapacity,
                size_t decompressedSize);
</b><p> This is a variant of ZSTD_compressSequences() which,
 instead of receiving (src,srcSize) as input parameter, receives (literals,litSize),
 aka all the literals, already extracted and laid out into a single continuous buffer.
 This can be useful if the process generating the sequences also happens to generate the buffer of literals,
 thus skipping an extraction + caching stage.
 It's a speed optimization, useful when the right conditions are met,
 but it also features the following limitations:
 - Only supports explicit delimiter mode
 - Currently does not support Sequences validation (so input Sequences are trusted)
 - Not compatible with frame checksum, which must be disabled
 - If any block is incompressible, will fail and return an error
 - @litSize must be == sum of all @.litLength fields in @inSeqs. Any discrepancy will generate an error.
 - @litBufCapacity is the size of the underlying buffer into which literals are written, starting at address @literals.
   @litBufCapacity must be at least 8 bytes larger than @litSize.
 - @decompressedSize must be correct, and correspond to the sum of all Sequences. Any discrepancy will generate an error.
 @return : final compressed size, or a ZSTD error code.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_writeSkippableFrame(void* dst, size_t dstCapacity,
                                 const void* src, size_t srcSize,
                                       unsigned magicVariant);
</b><p> Generates a zstd skippable frame containing data given by src, and writes it to dst buffer.

 Skippable frames begin with a 4-byte magic number. There are 16 possible choices of magic number,
 ranging from ZSTD_MAGIC_SKIPPABLE_START to ZSTD_MAGIC_SKIPPABLE_START+15.
 As such, the parameter magicVariant controls the exact skippable frame magic number variant used,
 so the magic number used will be ZSTD_MAGIC_SKIPPABLE_START + magicVariant.

 Returns an error if destination buffer is not large enough, if the source size is not representable
 with a 4-byte unsigned int, or if the parameter magicVariant is greater than 15 (and therefore invalid).

 @return : number of bytes written or a ZSTD error.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_readSkippableFrame(void* dst, size_t dstCapacity,
                                      unsigned* magicVariant,
                                      const void* src, size_t srcSize);
</b><p> Retrieves the content of a zstd skippable frame starting at @src, and writes it to @dst buffer.

 The parameter @magicVariant will receive the magicVariant that was supplied when the frame was written,
 i.e. magicNumber - ZSTD_MAGIC_SKIPPABLE_START.
 This can be NULL if the caller is not interested in the magicVariant.

 Returns an error if destination buffer is not large enough, or if the frame is not skippable.

 @return : number of bytes written or a ZSTD error.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API unsigned ZSTD_isSkippableFrame(const void* buffer, size_t size);
</b><p>  Tells if the content of `buffer` starts with a valid Frame Identifier for a skippable frame.
 
</p></pre><BR>

<a name="Chapter16"></a><h2>Memory management</h2><pre></pre>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_estimateCCtxSize(int maxCompressionLevel);
ZSTDLIB_STATIC_API size_t ZSTD_estimateCCtxSize_usingCParams(ZSTD_compressionParameters cParams);
ZSTDLIB_STATIC_API size_t ZSTD_estimateCCtxSize_usingCCtxParams(const ZSTD_CCtx_params* params);
ZSTDLIB_STATIC_API size_t ZSTD_estimateDCtxSize(void);
</b><p>  These functions make it possible to estimate memory usage
  of a future {D,C}Ctx, before its creation.
  This is useful in combination with ZSTD_initStatic(),
  which makes it possible to employ a static buffer for ZSTD_CCtx* state.

  ZSTD_estimateCCtxSize() will provide a memory budget large enough
  to compress data of any size using one-shot compression ZSTD_compressCCtx() or ZSTD_compress2()
  associated with any compression level up to max specified one.
  The estimate will assume the input may be arbitrarily large,
  which is the worst case.

  Note that the size estimation is specific for one-shot compression,
  it is not valid for streaming (see ZSTD_estimateCStreamSize*())
  nor other potential ways of using a ZSTD_CCtx* state.

  When srcSize can be bound by a known and rather "small" value,
  this knowledge can be used to provide a tighter budget estimation
  because the ZSTD_CCtx* state will need less memory for small inputs.
  This tighter estimation can be provided by employing more advanced functions
  ZSTD_estimateCCtxSize_usingCParams(), which can be used in tandem with ZSTD_getCParams(),
  and ZSTD_estimateCCtxSize_usingCCtxParams(), which can be used in tandem with ZSTD_CCtxParams_setParameter().
  Both can be used to estimate memory using custom compression parameters and arbitrary srcSize limits.

  Note : only single-threaded compression is supported.
  ZSTD_estimateCCtxSize_usingCCtxParams() will return an error code if ZSTD_c_nbWorkers is >= 1.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_estimateCStreamSize(int maxCompressionLevel);
ZSTDLIB_STATIC_API size_t ZSTD_estimateCStreamSize_usingCParams(ZSTD_compressionParameters cParams);
ZSTDLIB_STATIC_API size_t ZSTD_estimateCStreamSize_usingCCtxParams(const ZSTD_CCtx_params* params);
ZSTDLIB_STATIC_API size_t ZSTD_estimateDStreamSize(size_t maxWindowSize);
ZSTDLIB_STATIC_API size_t ZSTD_estimateDStreamSize_fromFrame(const void* src, size_t srcSize);
</b><p>  ZSTD_estimateCStreamSize() will provide a memory budget large enough for streaming compression
  using any compression level up to the max specified one.
  It will also consider src size to be arbitrarily "large", which is a worst case scenario.
  If srcSize is known to always be small, ZSTD_estimateCStreamSize_usingCParams() can provide a tighter estimation.
  ZSTD_estimateCStreamSize_usingCParams() can be used in tandem with ZSTD_getCParams() to create cParams from compressionLevel.
  ZSTD_estimateCStreamSize_usingCCtxParams() can be used in tandem with ZSTD_CCtxParams_setParameter(). Only single-threaded compression is supported. This function will return an error code if ZSTD_c_nbWorkers is >= 1.
  Note : CStream size estimation is only correct for single-threaded compression.
  ZSTD_estimateCStreamSize_usingCCtxParams() will return an error code if ZSTD_c_nbWorkers is >= 1.
  Note 2 : ZSTD_estimateCStreamSize* functions are not compatible with the Block-Level Sequence Producer API at this time.
  Size estimates assume that no external sequence producer is registered.

  ZSTD_DStream memory budget depends on frame's window Size.
  This information can be passed manually, using ZSTD_estimateDStreamSize,
  or deducted from a valid frame Header, using ZSTD_estimateDStreamSize_fromFrame();
  Any frame requesting a window size larger than max specified one will be rejected.
  Note : if streaming is init with function ZSTD_init?Stream_usingDict(),
         an internal ?Dict will be created, which additional size is not estimated here.
         In this case, get total size by adding ZSTD_estimate?DictSize
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_estimateCDictSize(size_t dictSize, int compressionLevel);
ZSTDLIB_STATIC_API size_t ZSTD_estimateCDictSize_advanced(size_t dictSize, ZSTD_compressionParameters cParams, ZSTD_dictLoadMethod_e dictLoadMethod);
ZSTDLIB_STATIC_API size_t ZSTD_estimateDDictSize(size_t dictSize, ZSTD_dictLoadMethod_e dictLoadMethod);
</b><p>  ZSTD_estimateCDictSize() will bet that src size is relatively "small", and content is copied, like ZSTD_createCDict().
  ZSTD_estimateCDictSize_advanced() makes it possible to control compression parameters precisely, like ZSTD_createCDict_advanced().
  Note : dictionaries created by reference (`ZSTD_dlm_byRef`) are logically smaller.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API ZSTD_CCtx*    ZSTD_initStaticCCtx(void* workspace, size_t workspaceSize);
ZSTDLIB_STATIC_API ZSTD_CStream* ZSTD_initStaticCStream(void* workspace, size_t workspaceSize);    </b>/**< same as ZSTD_initStaticCCtx() */<b>
</b><p>  Initialize an object using a pre-allocated fixed-size buffer.
  workspace: The memory area to emplace the object into.
             Provided pointer *must be 8-bytes aligned*.
             Buffer must outlive object.
  workspaceSize: Use ZSTD_estimate*Size() to determine
                 how large workspace must be to support target scenario.
 @return : pointer to object (same address as workspace, just different type),
           or NULL if error (size too small, incorrect alignment, etc.)
  Note : zstd will never resize nor malloc() when using a static buffer.
         If the object requires more memory than available,
         zstd will just error out (typically ZSTD_error_memory_allocation).
  Note 2 : there is no corresponding "free" function.
           Since workspace is allocated externally, it must be freed externally too.
  Note 3 : cParams : use ZSTD_getCParams() to convert a compression level
           into its associated cParams.
  Limitation 1 : currently not compatible with internal dictionary creation, triggered by
                 ZSTD_CCtx_loadDictionary(), ZSTD_initCStream_usingDict() or ZSTD_initDStream_usingDict().
  Limitation 2 : static cctx currently not compatible with multi-threading.
  Limitation 3 : static dctx is incompatible with legacy support.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API ZSTD_DStream* ZSTD_initStaticDStream(void* workspace, size_t workspaceSize);    </b>/**< same as ZSTD_initStaticDCtx() */<b>
</b></pre><BR>
<pre><b>typedef void* (*ZSTD_allocFunction) (void* opaque, size_t size);
typedef void  (*ZSTD_freeFunction) (void* opaque, void* address);
typedef struct { ZSTD_allocFunction customAlloc; ZSTD_freeFunction customFree; void* opaque; } ZSTD_customMem;
static
#ifdef __GNUC__
__attribute__((__unused__))
#endif
</b><p>  These prototypes make it possible to pass your own allocation/free functions.
  ZSTD_customMem is provided at creation time, using ZSTD_create*_advanced() variants listed below.
  All allocation/free operations will be completed using these custom variants instead of regular <stdlib.h> ones.
 
</p></pre><BR>

<pre><b>ZSTD_customMem const ZSTD_defaultCMem = { NULL, NULL, NULL };  </b>/**< this constant defers to stdlib's functions */<b>
</b></pre><BR>
<pre><b>typedef struct POOL_ctx_s ZSTD_threadPool;
ZSTDLIB_STATIC_API ZSTD_threadPool* ZSTD_createThreadPool(size_t numThreads);
ZSTDLIB_STATIC_API void ZSTD_freeThreadPool (ZSTD_threadPool* pool);  </b>/* accept NULL pointer */<b>
ZSTDLIB_STATIC_API size_t ZSTD_CCtx_refThreadPool(ZSTD_CCtx* cctx, ZSTD_threadPool* pool);
</b><p>  These prototypes make it possible to share a thread pool among multiple compression contexts.
  This can limit resources for applications with multiple threads where each one uses
  a threaded compression mode (via ZSTD_c_nbWorkers parameter).
  ZSTD_createThreadPool creates a new thread pool with a given number of threads.
  Note that the lifetime of such pool must exist while being used.
  ZSTD_CCtx_refThreadPool assigns a thread pool to a context (use NULL argument value
  to use an internal thread pool).
  ZSTD_freeThreadPool frees a thread pool, accepts NULL pointer.
 
</p></pre><BR>

<a name="Chapter17"></a><h2>Advanced compression functions</h2><pre></pre>

<pre><b>ZSTDLIB_STATIC_API ZSTD_CDict* ZSTD_createCDict_byReference(const void* dictBuffer, size_t dictSize, int compressionLevel);
</b><p>  Create a digested dictionary for compression
  Dictionary content is just referenced, not duplicated.
  As a consequence, `dictBuffer` **must** outlive CDict,
  and its content must remain unmodified throughout the lifetime of CDict.
  note: equivalent to ZSTD_createCDict_advanced(), with dictLoadMethod==ZSTD_dlm_byRef 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API ZSTD_compressionParameters ZSTD_getCParams(int compressionLevel, unsigned long long estimatedSrcSize, size_t dictSize);
</b><p> @return ZSTD_compressionParameters structure for a selected compression level and estimated srcSize.
 `estimatedSrcSize` value is optional, select 0 if not known 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API ZSTD_parameters ZSTD_getParams(int compressionLevel, unsigned long long estimatedSrcSize, size_t dictSize);
</b><p>  same as ZSTD_getCParams(), but @return a full `ZSTD_parameters` object instead of sub-component `ZSTD_compressionParameters`.
  All fields of `ZSTD_frameParameters` are set to default : contentSize=1, checksum=0, noDictID=0 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_checkCParams(ZSTD_compressionParameters params);
</b><p>  Ensure param values remain within authorized range.
 @return 0 on success, or an error code (can be checked with ZSTD_isError()) 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API ZSTD_compressionParameters ZSTD_adjustCParams(ZSTD_compressionParameters cPar, unsigned long long srcSize, size_t dictSize);
</b><p>  optimize params for a given `srcSize` and `dictSize`.
 `srcSize` can be unknown, in which case use ZSTD_CONTENTSIZE_UNKNOWN.
 `dictSize` must be `0` when there is no dictionary.
  cPar can be invalid : all parameters will be clamped within valid range in the @return struct.
  This function never fails (wide contract) 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtx_setCParams(ZSTD_CCtx* cctx, ZSTD_compressionParameters cparams);
</b><p>  Set all parameters provided within @p cparams into the working @p cctx.
  Note : if modifying parameters during compression (MT mode only),
         note that changes to the .windowLog parameter will be ignored.
 @return 0 on success, or an error code (can be checked with ZSTD_isError()).
         On failure, no parameters are updated.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtx_setFParams(ZSTD_CCtx* cctx, ZSTD_frameParameters fparams);
</b><p>  Set all parameters provided within @p fparams into the working @p cctx.
 @return 0 on success, or an error code (can be checked with ZSTD_isError()).
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtx_setParams(ZSTD_CCtx* cctx, ZSTD_parameters params);
</b><p>  Set all parameters provided within @p params into the working @p cctx.
 @return 0 on success, or an error code (can be checked with ZSTD_isError()).
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_compress2")
ZSTDLIB_STATIC_API
size_t ZSTD_compress_advanced(ZSTD_CCtx* cctx,
                  void* dst, size_t dstCapacity,
            const void* src, size_t srcSize,
            const void* dict,size_t dictSize,
                  ZSTD_parameters params);
</b><p>  Note : this function is now DEPRECATED.
         It can be replaced by ZSTD_compress2(), in combination with ZSTD_CCtx_setParameter() and other parameter setters.
  This prototype will generate compilation warnings. 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_compress2 with ZSTD_CCtx_loadDictionary")
ZSTDLIB_STATIC_API
size_t ZSTD_compress_usingCDict_advanced(ZSTD_CCtx* cctx,
                                  void* dst, size_t dstCapacity,
                            const void* src, size_t srcSize,
                            const ZSTD_CDict* cdict,
                                  ZSTD_frameParameters fParams);
</b><p>  Note : this function is now DEPRECATED.
         It can be replaced by ZSTD_compress2(), in combination with ZSTD_CCtx_loadDictionary() and other parameter setters.
  This prototype will generate compilation warnings. 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtx_loadDictionary_byReference(ZSTD_CCtx* cctx, const void* dict, size_t dictSize);
</b><p>  Same as ZSTD_CCtx_loadDictionary(), but dictionary content is referenced, instead of being copied into CCtx.
  It saves some memory, but also requires that `dict` outlives its usage within `cctx` 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtx_loadDictionary_advanced(ZSTD_CCtx* cctx, const void* dict, size_t dictSize, ZSTD_dictLoadMethod_e dictLoadMethod, ZSTD_dictContentType_e dictContentType);
</b><p>  Same as ZSTD_CCtx_loadDictionary(), but gives finer control over
  how to load the dictionary (by copy ? by reference ?)
  and how to interpret it (automatic ? force raw mode ? full mode only ?) 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtx_refPrefix_advanced(ZSTD_CCtx* cctx, const void* prefix, size_t prefixSize, ZSTD_dictContentType_e dictContentType);
</b><p>  Same as ZSTD_CCtx_refPrefix(), but gives finer control over
  how to interpret prefix content (automatic ? force raw mode (default) ? full mode only ?) 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtx_getParameter(const ZSTD_CCtx* cctx, ZSTD_cParameter param, int* value);
</b><p>  Get the requested compression parameter value, selected by enum ZSTD_cParameter,
  and store it into int* value.
 @return : 0, or an error code (which can be tested with ZSTD_isError()).
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API ZSTD_CCtx_params* ZSTD_createCCtxParams(void);
ZSTDLIB_STATIC_API size_t ZSTD_freeCCtxParams(ZSTD_CCtx_params* params);  </b>/* accept NULL pointer */<b>
</b><p>  Quick howto :
  - ZSTD_createCCtxParams() : Create a ZSTD_CCtx_params structure
  - ZSTD_CCtxParams_setParameter() : Push parameters one by one into
                                     an existing ZSTD_CCtx_params structure.
                                     This is similar to
                                     ZSTD_CCtx_setParameter().
  - ZSTD_CCtx_setParametersUsingCCtxParams() : Apply parameters to
                                    an existing CCtx.
                                    These parameters will be applied to
                                    all subsequent frames.
  - ZSTD_compressStream2() : Do compression using the CCtx.
  - ZSTD_freeCCtxParams() : Free the memory, accept NULL pointer.

  This can be used with ZSTD_estimateCCtxSize_advanced_usingCCtxParams()
  for static allocation of CCtx for single-threaded compression.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtxParams_reset(ZSTD_CCtx_params* params);
</b><p>  Reset params to default values.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtxParams_init(ZSTD_CCtx_params* cctxParams, int compressionLevel);
</b><p>  Initializes the compression parameters of cctxParams according to
  compression level. All other parameters are reset to their default values.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtxParams_init_advanced(ZSTD_CCtx_params* cctxParams, ZSTD_parameters params);
</b><p>  Initializes the compression and frame parameters of cctxParams according to
  params. All other parameters are reset to their default values.
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtxParams_setParameter(ZSTD_CCtx_params* params, ZSTD_cParameter param, int value);
</b><p>  Similar to ZSTD_CCtx_setParameter.
  Set one compression parameter, selected by enum ZSTD_cParameter.
  Parameters must be applied to a ZSTD_CCtx using
  ZSTD_CCtx_setParametersUsingCCtxParams().
 @result : a code representing success or failure (which can be tested with
           ZSTD_isError()).
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtxParams_getParameter(const ZSTD_CCtx_params* params, ZSTD_cParameter param, int* value);
</b><p> Similar to ZSTD_CCtx_getParameter.
 Get the requested value of one compression parameter, selected by enum ZSTD_cParameter.
 @result : 0, or an error code (which can be tested with ZSTD_isError()).
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_CCtx_setParametersUsingCCtxParams(
        ZSTD_CCtx* cctx, const ZSTD_CCtx_params* params);
</b><p>  Apply a set of ZSTD_CCtx_params to the compression context.
  This can be done even after compression is started,
    if nbWorkers==0, this will have no impact until a new compression is started.
    if nbWorkers>=1, new parameters will be picked up at next job,
       with a few restrictions (windowLog, pledgedSrcSize, nbWorkers, jobSize, and overlapLog are not updated).
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_compressStream2_simpleArgs (
                ZSTD_CCtx* cctx,
                void* dst, size_t dstCapacity, size_t* dstPos,
          const void* src, size_t srcSize, size_t* srcPos,
                ZSTD_EndDirective endOp);
</b><p>  Same as ZSTD_compressStream2(),
  but using only integral types as arguments.
  This variant might be helpful for binders from dynamic languages
  which have troubles handling structures containing memory pointers.
 
</p></pre><BR>

<a name="Chapter18"></a><h2>Advanced decompression functions</h2><pre></pre>

<pre><b>ZSTDLIB_STATIC_API unsigned ZSTD_isFrame(const void* buffer, size_t size);
</b><p>  Tells if the content of `buffer` starts with a valid Frame Identifier.
  Note : Frame Identifier is 4 bytes. If `size < 4`, @return will always be 0.
  Note 2 : Legacy Frame Identifiers are considered valid only if Legacy Support is enabled.
  Note 3 : Skippable Frame Identifiers are considered valid. 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API ZSTD_DDict* ZSTD_createDDict_byReference(const void* dictBuffer, size_t dictSize);
</b><p>  Create a digested dictionary, ready to start decompression operation without startup delay.
  Dictionary content is referenced, and therefore stays in dictBuffer.
  It is important that dictBuffer outlives DDict,
  it must remain read accessible throughout the lifetime of DDict 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_DCtx_loadDictionary_byReference(ZSTD_DCtx* dctx, const void* dict, size_t dictSize);
</b><p>  Same as ZSTD_DCtx_loadDictionary(),
  but references `dict` content instead of copying it into `dctx`.
  This saves memory if `dict` remains around.,
  However, it's imperative that `dict` remains accessible (and unmodified) while being used, so it must outlive decompression. 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_DCtx_loadDictionary_advanced(ZSTD_DCtx* dctx, const void* dict, size_t dictSize, ZSTD_dictLoadMethod_e dictLoadMethod, ZSTD_dictContentType_e dictContentType);
</b><p>  Same as ZSTD_DCtx_loadDictionary(),
  but gives direct control over
  how to load the dictionary (by copy ? by reference ?)
  and how to interpret it (automatic ? force raw mode ? full mode only ?). 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_DCtx_refPrefix_advanced(ZSTD_DCtx* dctx, const void* prefix, size_t prefixSize, ZSTD_dictContentType_e dictContentType);
</b><p>  Same as ZSTD_DCtx_refPrefix(), but gives finer control over
  how to interpret prefix content (automatic ? force raw mode (default) ? full mode only ?) 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_DCtx_setMaxWindowSize(ZSTD_DCtx* dctx, size_t maxWindowSize);
</b><p>  Refuses allocating internal buffers for frames requiring a window size larger than provided limit.
  This protects a decoder context from reserving too much memory for itself (potential attack scenario).
  This parameter is only useful in streaming mode, since no internal buffer is allocated in single-pass mode.
  By default, a decompression context accepts all window sizes <= (1 << ZSTD_WINDOWLOG_LIMIT_DEFAULT)
 @return : 0, or an error code (which can be tested using ZSTD_isError()).
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_DCtx_getParameter(ZSTD_DCtx* dctx, ZSTD_dParameter param, int* value);
</b><p>  Get the requested decompression parameter value, selected by enum ZSTD_dParameter,
  and store it into int* value.
 @return : 0, or an error code (which can be tested with ZSTD_isError()).
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_DCtx_setParameter() instead")
ZSTDLIB_STATIC_API
size_t ZSTD_DCtx_setFormat(ZSTD_DCtx* dctx, ZSTD_format_e format);
</b><p>  This function is REDUNDANT. Prefer ZSTD_DCtx_setParameter().
  Instruct the decoder context about what kind of data to decode next.
  This instruction is mandatory to decode data without a fully-formed header,
  such ZSTD_f_zstd1_magicless for example.
 @return : 0, or an error code (which can be tested using ZSTD_isError()). 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_decompressStream_simpleArgs (
                ZSTD_DCtx* dctx,
                void* dst, size_t dstCapacity, size_t* dstPos,
          const void* src, size_t srcSize, size_t* srcPos);
</b><p>  Same as ZSTD_decompressStream(),
  but using only integral types as arguments.
  This can be helpful for binders from dynamic languages
  which have troubles handling structures containing memory pointers.
 
</p></pre><BR>

<a name="Chapter19"></a><h2>Advanced streaming functions</h2><pre>  Warning : most of these functions are now redundant with the Advanced API.
  Once Advanced API reaches "stable" status,
  redundant functions will be deprecated, and then at some point removed.
<BR></pre>

<h3>Advanced Streaming compression functions</h3><pre></pre><b><pre></pre></b><BR>
<pre><b>ZSTD_DEPRECATED("use ZSTD_CCtx_reset, see zstd.h for detailed instructions")
ZSTDLIB_STATIC_API
size_t ZSTD_initCStream_srcSize(ZSTD_CStream* zcs,
             int compressionLevel,
             unsigned long long pledgedSrcSize);
</b><p> This function is DEPRECATED, and equivalent to:
     ZSTD_CCtx_reset(zcs, ZSTD_reset_session_only);
     ZSTD_CCtx_refCDict(zcs, NULL); // clear the dictionary (if any)
     ZSTD_CCtx_setParameter(zcs, ZSTD_c_compressionLevel, compressionLevel);
     ZSTD_CCtx_setPledgedSrcSize(zcs, pledgedSrcSize);

 pledgedSrcSize must be correct. If it is not known at init time, use
 ZSTD_CONTENTSIZE_UNKNOWN. Note that, for compatibility with older programs,
 "0" also disables frame content size field. It may be enabled in the future.
 This prototype will generate compilation warnings.
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_CCtx_reset, see zstd.h for detailed instructions")
ZSTDLIB_STATIC_API
size_t ZSTD_initCStream_usingDict(ZSTD_CStream* zcs,
         const void* dict, size_t dictSize,
               int compressionLevel);
</b><p> This function is DEPRECATED, and is equivalent to:
     ZSTD_CCtx_reset(zcs, ZSTD_reset_session_only);
     ZSTD_CCtx_setParameter(zcs, ZSTD_c_compressionLevel, compressionLevel);
     ZSTD_CCtx_loadDictionary(zcs, dict, dictSize);

 Creates of an internal CDict (incompatible with static CCtx), except if
 dict == NULL or dictSize < 8, in which case no dict is used.
 Note: dict is loaded with ZSTD_dct_auto (treated as a full zstd dictionary if
 it begins with ZSTD_MAGIC_DICTIONARY, else as raw content) and ZSTD_dlm_byCopy.
 This prototype will generate compilation warnings.
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_CCtx_reset, see zstd.h for detailed instructions")
ZSTDLIB_STATIC_API
size_t ZSTD_initCStream_advanced(ZSTD_CStream* zcs,
        const void* dict, size_t dictSize,
              ZSTD_parameters params,
              unsigned long long pledgedSrcSize);
</b><p> This function is DEPRECATED, and is equivalent to:
     ZSTD_CCtx_reset(zcs, ZSTD_reset_session_only);
     ZSTD_CCtx_setParams(zcs, params);
     ZSTD_CCtx_setPledgedSrcSize(zcs, pledgedSrcSize);
     ZSTD_CCtx_loadDictionary(zcs, dict, dictSize);

 dict is loaded with ZSTD_dct_auto and ZSTD_dlm_byCopy.
 pledgedSrcSize must be correct.
 If srcSize is not known at init time, use value ZSTD_CONTENTSIZE_UNKNOWN.
 This prototype will generate compilation warnings.
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_CCtx_reset and ZSTD_CCtx_refCDict, see zstd.h for detailed instructions")
ZSTDLIB_STATIC_API
size_t ZSTD_initCStream_usingCDict(ZSTD_CStream* zcs, const ZSTD_CDict* cdict);
</b><p> This function is DEPRECATED, and equivalent to:
     ZSTD_CCtx_reset(zcs, ZSTD_reset_session_only);
     ZSTD_CCtx_refCDict(zcs, cdict);

 note : cdict will just be referenced, and must outlive compression session
 This prototype will generate compilation warnings.
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_CCtx_reset and ZSTD_CCtx_refCDict, see zstd.h for detailed instructions")
ZSTDLIB_STATIC_API
size_t ZSTD_initCStream_usingCDict_advanced(ZSTD_CStream* zcs,
                   const ZSTD_CDict* cdict,
                         ZSTD_frameParameters fParams,
                         unsigned long long pledgedSrcSize);
</b><p>   This function is DEPRECATED, and is equivalent to:
     ZSTD_CCtx_reset(zcs, ZSTD_reset_session_only);
     ZSTD_CCtx_setFParams(zcs, fParams);
     ZSTD_CCtx_setPledgedSrcSize(zcs, pledgedSrcSize);
     ZSTD_CCtx_refCDict(zcs, cdict);

 same as ZSTD_initCStream_usingCDict(), with control over frame parameters.
 pledgedSrcSize must be correct. If srcSize is not known at init time, use
 value ZSTD_CONTENTSIZE_UNKNOWN.
 This prototype will generate compilation warnings.
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_CCtx_reset, see zstd.h for detailed instructions")
ZSTDLIB_STATIC_API
size_t ZSTD_resetCStream(ZSTD_CStream* zcs, unsigned long long pledgedSrcSize);
</b><p> This function is DEPRECATED, and is equivalent to:
     ZSTD_CCtx_reset(zcs, ZSTD_reset_session_only);
     ZSTD_CCtx_setPledgedSrcSize(zcs, pledgedSrcSize);
 Note: ZSTD_resetCStream() interprets pledgedSrcSize == 0 as ZSTD_CONTENTSIZE_UNKNOWN, but
       ZSTD_CCtx_setPledgedSrcSize() does not do the same, so ZSTD_CONTENTSIZE_UNKNOWN must be
       explicitly specified.

  start a new frame, using same parameters from previous frame.
  This is typically useful to skip dictionary loading stage, since it will reuse it in-place.
  Note that zcs must be init at least once before using ZSTD_resetCStream().
  If pledgedSrcSize is not known at reset time, use macro ZSTD_CONTENTSIZE_UNKNOWN.
  If pledgedSrcSize > 0, its value must be correct, as it will be written in header, and controlled at the end.
  For the time being, pledgedSrcSize==0 is interpreted as "srcSize unknown" for compatibility with older programs,
  but it will change to mean "empty" in future version, so use macro ZSTD_CONTENTSIZE_UNKNOWN instead.
 @return : 0, or an error code (which can be tested using ZSTD_isError())
  This prototype will generate compilation warnings.
 
</p></pre><BR>

<pre><b>typedef struct {
    unsigned long long ingested;   </b>/* nb input bytes read and buffered */<b>
    unsigned long long consumed;   </b>/* nb input bytes actually compressed */<b>
    unsigned long long produced;   </b>/* nb of compressed bytes generated and buffered */<b>
    unsigned long long flushed;    </b>/* nb of compressed bytes flushed : not provided; can be tracked from caller side */<b>
    unsigned currentJobID;         </b>/* MT only : latest started job nb */<b>
    unsigned nbActiveWorkers;      </b>/* MT only : nb of workers actively compressing at probe time */<b>
} ZSTD_frameProgression;
</b></pre><BR>
<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_toFlushNow(ZSTD_CCtx* cctx);
</b><p>  Tell how many bytes are ready to be flushed immediately.
  Useful for multithreading scenarios (nbWorkers >= 1).
  Probe the oldest active job, defined as oldest job not yet entirely flushed,
  and check its output buffer.
 @return : amount of data stored in oldest job and ready to be flushed immediately.
  if @return == 0, it means either :
  + there is no active job (could be checked with ZSTD_frameProgression()), or
  + oldest job is still actively compressing data,
    but everything it has produced has also been flushed so far,
    therefore flush speed is limited by production speed of oldest job
    irrespective of the speed of concurrent (and newer) jobs.
 
</p></pre><BR>

<h3>Advanced Streaming decompression functions</h3><pre></pre><b><pre></pre></b><BR>
<pre><b>ZSTD_DEPRECATED("use ZSTD_DCtx_reset + ZSTD_DCtx_loadDictionary, see zstd.h for detailed instructions")
ZSTDLIB_STATIC_API size_t ZSTD_initDStream_usingDict(ZSTD_DStream* zds, const void* dict, size_t dictSize);
</b><p>
     ZSTD_DCtx_reset(zds, ZSTD_reset_session_only);
     ZSTD_DCtx_loadDictionary(zds, dict, dictSize);

 note: no dictionary will be used if dict == NULL or dictSize < 8
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_DCtx_reset + ZSTD_DCtx_refDDict, see zstd.h for detailed instructions")
ZSTDLIB_STATIC_API size_t ZSTD_initDStream_usingDDict(ZSTD_DStream* zds, const ZSTD_DDict* ddict);
</b><p>
     ZSTD_DCtx_reset(zds, ZSTD_reset_session_only);
     ZSTD_DCtx_refDDict(zds, ddict);

 note : ddict is referenced, it must outlive decompression session
 
</p></pre><BR>

<pre><b>ZSTD_DEPRECATED("use ZSTD_DCtx_reset, see zstd.h for detailed instructions")
ZSTDLIB_STATIC_API size_t ZSTD_resetDStream(ZSTD_DStream* zds);
</b><p>
     ZSTD_DCtx_reset(zds, ZSTD_reset_session_only);

 reuse decompression parameters from previous init; saves dictionary loading
 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API void
ZSTD_registerSequenceProducer(
  ZSTD_CCtx* cctx,
  void* sequenceProducerState,
  ZSTD_sequenceProducer_F sequenceProducer
);
</b><p> Instruct zstd to use a block-level external sequence producer function.

 The sequenceProducerState must be initialized by the caller, and the caller is
 responsible for managing its lifetime. This parameter is sticky across
 compressions. It will remain set until the user explicitly resets compression
 parameters.

 Sequence producer registration is considered to be an "advanced parameter",
 part of the "advanced API". This means it will only have an effect on compression
 APIs which respect advanced parameters, such as compress2() and compressStream2().
 Older compression APIs such as compressCCtx(), which predate the introduction of
 "advanced parameters", will ignore any external sequence producer setting.

 The sequence producer can be "cleared" by registering a NULL function pointer. This
 removes all limitations described above in the "LIMITATIONS" section of the API docs.

 The user is strongly encouraged to read the full API documentation (above) before
 calling this function. 
</p></pre><BR>

<pre><b>ZSTDLIB_STATIC_API void
ZSTD_CCtxParams_registerSequenceProducer(
  ZSTD_CCtx_params* params,
  void* sequenceProducerState,
  ZSTD_sequenceProducer_F sequenceProducer
);
</b><p> Same as ZSTD_registerSequenceProducer(), but operates on ZSTD_CCtx_params.
 This is used for accurate size estimation with ZSTD_estimateCCtxSize_usingCCtxParams(),
 which is needed when creating a ZSTD_CCtx with ZSTD_initStaticCCtx().

 If you are using the external sequence producer API in a scenario where ZSTD_initStaticCCtx()
 is required, then this function is for you. Otherwise, you probably don't need it.

 See tests/zstreamtest.c for example usage. 
</p></pre><BR>

<a name="Chapter20"></a><h2>Buffer-less and synchronous inner streaming functions (DEPRECATED)</h2><pre>
  This API is deprecated, and will be removed in a future version.
  It allows streaming (de)compression with user allocated buffers.
  However, it is hard to use, and not as well tested as the rest of
  our API.

  Please use the normal streaming API instead: ZSTD_compressStream2,
  and ZSTD_decompressStream.
  If there is functionality that you need, but it doesn't provide,
  please open an issue on our GitHub.
 
<BR></pre>

<a name="Chapter21"></a><h2>Buffer-less streaming compression (synchronous mode)</h2><pre>
  A ZSTD_CCtx object is required to track streaming operations.
  Use ZSTD_createCCtx() / ZSTD_freeCCtx() to manage resource.
  ZSTD_CCtx object can be reused multiple times within successive compression operations.

  Start by initializing a context.
  Use ZSTD_compressBegin(), or ZSTD_compressBegin_usingDict() for dictionary compression.

  Then, consume your input using ZSTD_compressContinue().
  There are some important considerations to keep in mind when using this advanced function :
  - ZSTD_compressContinue() has no internal buffer. It uses externally provided buffers only.
  - Interface is synchronous : input is consumed entirely and produces 1+ compressed blocks.
  - Caller must ensure there is enough space in `dst` to store compressed data under worst case scenario.
    Worst case evaluation is provided by ZSTD_compressBound().
    ZSTD_compressContinue() doesn't guarantee recover after a failed compression.
  - ZSTD_compressContinue() presumes prior input ***is still accessible and unmodified*** (up to maximum distance size, see WindowLog).
    It remembers all previous contiguous blocks, plus one separated memory segment (which can itself consists of multiple contiguous blocks)
  - ZSTD_compressContinue() detects that prior input has been overwritten when `src` buffer overlaps.
    In which case, it will "discard" the relevant memory section from its history.

  Finish a frame with ZSTD_compressEnd(), which will write the last block(s) and optional checksum.
  It's possible to use srcSize==0, in which case, it will write a final empty block to end the frame.
  Without last block mark, frames are considered unfinished (hence corrupted) by compliant decoders.

  `ZSTD_CCtx` object can be reused (ZSTD_compressBegin()) to compress again.
<BR></pre>

<h3>Buffer-less streaming compression functions</h3><pre></pre><b><pre>ZSTD_DEPRECATED("The buffer-less API is deprecated in favor of the normal streaming API. See docs.")
ZSTDLIB_STATIC_API size_t ZSTD_compressBegin(ZSTD_CCtx* cctx, int compressionLevel);
ZSTD_DEPRECATED("The buffer-less API is deprecated in favor of the normal streaming API. See docs.")
ZSTDLIB_STATIC_API size_t ZSTD_compressBegin_usingDict(ZSTD_CCtx* cctx, const void* dict, size_t dictSize, int compressionLevel);
ZSTD_DEPRECATED("The buffer-less API is deprecated in favor of the normal streaming API. See docs.")
ZSTDLIB_STATIC_API size_t ZSTD_compressBegin_usingCDict(ZSTD_CCtx* cctx, const ZSTD_CDict* cdict); </b>/**< note: fails if cdict==NULL */<b>
</pre></b><BR>
<pre><b>size_t ZSTD_copyCCtx(ZSTD_CCtx* cctx, const ZSTD_CCtx* preparedCCtx, unsigned long long pledgedSrcSize); </b>/**<  note: if pledgedSrcSize is not known, use ZSTD_CONTENTSIZE_UNKNOWN */<b>
</b></pre><BR>
<pre><b>size_t ZSTD_compressBegin_advanced(ZSTD_CCtx* cctx, const void* dict, size_t dictSize, ZSTD_parameters params, unsigned long long pledgedSrcSize); </b>/**< pledgedSrcSize : If srcSize is not known at init time, use ZSTD_CONTENTSIZE_UNKNOWN */<b>
</b></pre><BR>
<a name="Chapter22"></a><h2>Buffer-less streaming decompression (synchronous mode)</h2><pre>
  A ZSTD_DCtx object is required to track streaming operations.
  Use ZSTD_createDCtx() / ZSTD_freeDCtx() to manage it.
  A ZSTD_DCtx object can be reused multiple times.

  First typical operation is to retrieve frame parameters, using ZSTD_getFrameHeader().
  Frame header is extracted from the beginning of compressed frame, so providing only the frame's beginning is enough.
  Data fragment must be large enough to ensure successful decoding.
 `ZSTD_frameHeaderSize_max` bytes is guaranteed to always be large enough.
  result  : 0 : successful decoding, the `ZSTD_frameHeader` structure is correctly filled.
           >0 : `srcSize` is too small, please provide at least result bytes on next attempt.
           errorCode, which can be tested using ZSTD_isError().

  It fills a ZSTD_FrameHeader structure with important information to correctly decode the frame,
  such as the dictionary ID, content size, or maximum back-reference distance (`windowSize`).
  Note that these values could be wrong, either because of data corruption, or because a 3rd party deliberately spoofs false information.
  As a consequence, check that values remain within valid application range.
  For example, do not allocate memory blindly, check that `windowSize` is within expectation.
  Each application can set its own limits, depending on local restrictions.
  For extended interoperability, it is recommended to support `windowSize` of at least 8 MB.

  ZSTD_decompressContinue() needs previous data blocks during decompression, up to `windowSize` bytes.
  ZSTD_decompressContinue() is very sensitive to contiguity,
  if 2 blocks don't follow each other, make sure that either the compressor breaks contiguity at the same place,
  or that previous contiguous segment is large enough to properly handle maximum back-reference distance.
  There are multiple ways to guarantee this condition.

  The most memory efficient way is to use a round buffer of sufficient size.
  Sufficient size is determined by invoking ZSTD_decodingBufferSize_min(),
  which can return an error code if required value is too large for current system (in 32-bits mode).
  In a round buffer methodology, ZSTD_decompressContinue() decompresses each block next to previous one,
  up to the moment there is not enough room left in the buffer to guarantee decoding another full block,
  which maximum size is provided in `ZSTD_frameHeader` structure, field `blockSizeMax`.
  At which point, decoding can resume from the beginning of the buffer.
  Note that already decoded data stored in the buffer should be flushed before being overwritten.

  There are alternatives possible, for example using two or more buffers of size `windowSize` each, though they consume more memory.

  Finally, if you control the compression process, you can also ignore all buffer size rules,
  as long as the encoder and decoder progress in "lock-step",
  aka use exactly the same buffer sizes, break contiguity at the same place, etc.

  Once buffers are setup, start decompression, with ZSTD_decompressBegin().
  If decompression requires a dictionary, use ZSTD_decompressBegin_usingDict() or ZSTD_decompressBegin_usingDDict().

  Then use ZSTD_nextSrcSizeToDecompress() and ZSTD_decompressContinue() alternatively.
  ZSTD_nextSrcSizeToDecompress() tells how many bytes to provide as 'srcSize' to ZSTD_decompressContinue().
  ZSTD_decompressContinue() requires this _exact_ amount of bytes, or it will fail.

  result of ZSTD_decompressContinue() is the number of bytes regenerated within 'dst' (necessarily <= dstCapacity).
  It can be zero : it just means ZSTD_decompressContinue() has decoded some metadata item.
  It can also be an error code, which can be tested with ZSTD_isError().

  A frame is fully decoded when ZSTD_nextSrcSizeToDecompress() returns zero.
  Context can then be reset to start a new decompression.

  Note : it's possible to know if next input to present is a header or a block, using ZSTD_nextInputType().
  This information is not required to properly decode a frame.

  == Special case : skippable frames 

  Skippable frames allow integration of user-defined data into a flow of concatenated frames.
  Skippable frames will be ignored (skipped) by decompressor.
  The format of skippable frames is as follows :
  a) Skippable frame ID - 4 Bytes, Little endian format, any value from 0x184D2A50 to 0x184D2A5F
  b) Frame Size - 4 Bytes, Little endian format, unsigned 32-bits
  c) Frame Content - any content (User Data) of length equal to Frame Size
  For skippable frames ZSTD_getFrameHeader() returns zfhPtr->frameType==ZSTD_skippableFrame.
  For skippable frames ZSTD_decompressContinue() always returns 0 : it only skips the content.
<BR></pre>

<h3>Buffer-less streaming decompression functions</h3><pre></pre><b><pre></pre></b><BR>
<pre><b>ZSTDLIB_STATIC_API size_t ZSTD_decodingBufferSize_min(unsigned long long windowSize, unsigned long long frameContentSize);  </b>/**< when frame content size is not known, pass in frameContentSize == ZSTD_CONTENTSIZE_UNKNOWN */<b>
</b></pre><BR>
<pre><b>typedef enum { ZSTDnit_frameHeader, ZSTDnit_blockHeader, ZSTDnit_block, ZSTDnit_lastBlock, ZSTDnit_checksum, ZSTDnit_skippableFrame } ZSTD_nextInputType_e;
</b></pre><BR>
<a name="Chapter23"></a><h2>Block level API (DEPRECATED)</h2><pre></pre>

<pre><b></b><p>    You can get the frame header down to 2 bytes by setting:
      - ZSTD_c_format = ZSTD_f_zstd1_magicless
      - ZSTD_c_contentSizeFlag = 0
      - ZSTD_c_checksumFlag = 0
      - ZSTD_c_dictIDFlag = 0

    This API is not as well tested as our normal API, so we recommend not using it.
    We will be removing it in a future version. If the normal API doesn't provide
    the functionality you need, please open a GitHub issue.

    Block functions produce and decode raw zstd blocks, without frame metadata.
    Frame metadata cost is typically ~12 bytes, which can be non-negligible for very small blocks (< 100 bytes).
    But users will have to take in charge needed metadata to regenerate data, such as compressed and content sizes.

    A few rules to respect :
    - Compressing and decompressing require a context structure
      + Use ZSTD_createCCtx() and ZSTD_createDCtx()
    - It is necessary to init context before starting
      + compression : any ZSTD_compressBegin*() variant, including with dictionary
      + decompression : any ZSTD_decompressBegin*() variant, including with dictionary
    - Block size is limited, it must be <= ZSTD_getBlockSize() <= ZSTD_BLOCKSIZE_MAX == 128 KB
      + If input is larger than a block size, it's necessary to split input data into multiple blocks
      + For inputs larger than a single block, consider using regular ZSTD_compress() instead.
        Frame metadata is not that costly, and quickly becomes negligible as source size grows larger than a block.
    - When a block is considered not compressible enough, ZSTD_compressBlock() result will be 0 (zero) !
      ===> In which case, nothing is produced into `dst` !
      + User __must__ test for such outcome and deal directly with uncompressed data
      + A block cannot be declared incompressible if ZSTD_compressBlock() return value was != 0.
        Doing so would mess up with statistics history, leading to potential data corruption.
      + ZSTD_decompressBlock() _doesn't accept uncompressed data as input_ !!
      + In case of multiple successive blocks, should some of them be uncompressed,
        decoder must be informed of their existence in order to follow proper history.
        Use ZSTD_insertBlock() for such a case.
</p></pre><BR>

<h3>Raw zstd block functions</h3><pre></pre><b><pre>ZSTD_DEPRECATED("The block API is deprecated in favor of the normal compression API. See docs.")
ZSTDLIB_STATIC_API size_t ZSTD_getBlockSize   (const ZSTD_CCtx* cctx);
ZSTD_DEPRECATED("The block API is deprecated in favor of the normal compression API. See docs.")
ZSTDLIB_STATIC_API size_t ZSTD_compressBlock  (ZSTD_CCtx* cctx, void* dst, size_t dstCapacity, const void* src, size_t srcSize);
ZSTD_DEPRECATED("The block API is deprecated in favor of the normal compression API. See docs.")
ZSTDLIB_STATIC_API size_t ZSTD_decompressBlock(ZSTD_DCtx* dctx, void* dst, size_t dstCapacity, const void* src, size_t srcSize);
ZSTD_DEPRECATED("The block API is deprecated in favor of the normal compression API. See docs.")
ZSTDLIB_STATIC_API size_t ZSTD_insertBlock    (ZSTD_DCtx* dctx, const void* blockStart, size_t blockSize);  </b>/**< insert uncompressed block into `dctx` history. Useful for multi-blocks decompression. */<b>
</pre></b><BR>
</html>
</body>
