.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srp_set_server_fake_salt_seed" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srp_set_server_fake_salt_seed \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_srp_set_server_fake_salt_seed(gnutls_srp_server_credentials_t " cred ", const gnutls_datum_t * " seed ", unsigned int " salt_length ");"
.SH ARGUMENTS
.IP "gnutls_srp_server_credentials_t cred" 12
is a \fBgnutls_srp_server_credentials_t\fP type
.IP "const gnutls_datum_t * seed" 12
is the seed data, only needs to be valid until the function
returns; size of the seed must be greater than zero
.IP "unsigned int salt_length" 12
is the length of the generated fake salts
.SH "DESCRIPTION"
This function sets the seed that is used to generate salts for
invalid (non\-existent) usernames.

In order to prevent attackers from guessing valid usernames,
when a user does not exist gnutls generates a salt and a verifier
and proceeds with the protocol as usual.
The authentication will ultimately fail, but the client cannot tell
whether the username is valid (exists) or invalid.

If an attacker learns the seed, given a salt (which is part of the
handshake) which was generated when the seed was in use, it can tell
whether or not the authentication failed because of an unknown username.
This seed cannot be used to reveal application data or passwords.

 \fIsalt_length\fP should represent the salt length your application uses.
Generating fake salts longer than 20 bytes is not supported.

By default the seed is a random value, different each time a
\fBgnutls_srp_server_credentials_t\fP is allocated and fake salts are
16 bytes long.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
