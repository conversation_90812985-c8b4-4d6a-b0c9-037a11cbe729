.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_get_signature_algorithm" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_get_signature_algorithm \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_get_signature_algorithm(gnutls_x509_crl_t " crl ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
should contain a \fBgnutls_x509_crl_t\fP type
.SH "DESCRIPTION"
This function will return a value of the \fBgnutls_sign_algorithm_t\fP
enumeration that is the signature algorithm.

Since 3.6.0 this function never returns a negative error code.
Error cases and unknown/unsupported signature algorithms are
mapped to \fBGNUTLS_SIGN_UNKNOWN\fP.
.SH "RETURNS"
a \fBgnutls_sign_algorithm_t\fP value
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
