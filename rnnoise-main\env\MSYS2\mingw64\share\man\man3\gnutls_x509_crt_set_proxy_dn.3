.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_proxy_dn" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_proxy_dn \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_proxy_dn(gnutls_x509_crt_t " crt ", gnutls_x509_crt_t " eecrt ", unsigned int " raw_flag ", const void * " name ", unsigned int " sizeof_name ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a gnutls_x509_crt_t type with the new proxy cert
.IP "gnutls_x509_crt_t eecrt" 12
the end entity certificate that will be issuing the proxy
.IP "unsigned int raw_flag" 12
must be 0, or 1 if the CN is DER encoded
.IP "const void * name" 12
a pointer to the CN name, may be NULL (but MUST then be added later)
.IP "unsigned int sizeof_name" 12
holds the size of  \fIname\fP 
.SH "DESCRIPTION"
This function will set the subject in  \fIcrt\fP to the end entity's
 \fIeecrt\fP subject name, and add a single Common Name component  \fIname\fP of size  \fIsizeof_name\fP .  This corresponds to the required proxy
certificate naming style.  Note that if  \fIname\fP is \fBNULL\fP, you MUST
set it later by using \fBgnutls_x509_crt_set_dn_by_oid()\fP or similar.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
