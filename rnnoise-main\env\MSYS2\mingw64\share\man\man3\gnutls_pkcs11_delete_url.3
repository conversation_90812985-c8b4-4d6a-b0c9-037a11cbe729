.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_delete_url" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_delete_url \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_delete_url(const char * " object_url ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * object_url" 12
The URL of the object to delete.
.IP "unsigned int flags" 12
One of GNUTLS_PKCS11_OBJ_* flags
.SH "DESCRIPTION"
This function will delete objects matching the given URL.
Note that not all tokens support the delete operation.
.SH "RETURNS"
On success, the number of objects deleted is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
