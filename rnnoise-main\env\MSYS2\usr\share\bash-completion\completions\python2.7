# bash completion for python                               -*- shell-script -*-

# @since 2.12
_comp_xfunc_python_compgen_modules()
{
    local _python=python
    [[ ${comp_args[0]##*/} == *3* ]] && _python=python3
    _comp_cmd_python__compgen_modules "$_python"
}

# @deprecated 2.12 use `_comp_xfunc_python_compgen_modules` instead
_python_modules()
{
    _comp_compgen -a -i python modules "${1:-python}"
}

_comp_cmd_python__compgen_modules()
{
    _comp_compgen_split -- "$("$1" \
        "${BASH_SOURCE[0]%/*}/../helpers/python" "$cur" 2>/dev/null)"
}

# @since 2.12
_comp_xfunc_python_compgen_warning_actions()
{
    _comp_compgen -- -W "ignore default all module once error" \
        ${prefix:+-P "$prefix"}
}

# @deprecated 2.12 use `_comp_xfunc_python_compgen_warning_actions` instead
_python_warning_actions()
{
    _comp_compgen -a -i python warning_actions "${1:-python}"
}

_comp_cmd_python()
{
    local cur prev words cword comp_args prefix
    _comp_initialize -- "$@" || return

    case $cur in
        -[QWX]?*)
            prefix=${cur:0:2}
            prev=$prefix
            cur=${cur:2}
            ;;
    esac

    local noargopts='!(-*|*[cmQWX]*)'

    # if command, module, or script is already given by [-c command | -m module
    # | script], complete all kind of files.
    local i has_command=""
    for ((i = 1; i < cword; i++)); do
        # shellcheck disable=SC2254
        case ${words[i]} in
            -${noargopts}[QWX])
                ((i++))
                ;;
            -${noargopts}[cm]?*)
                has_command=set
                break
                ;;
            -- | -${noargopts}[cm])
                if ((i + 1 < cword)); then
                    has_command=set
                    break
                fi
                ;;
            -*) ;;
            *)
                has_command=set
                break
                ;;
        esac
    done
    if [[ $has_command ]]; then
        _comp_compgen_filedir
        return
    fi

    # shellcheck disable=SC2254
    case $prev in
        --help | --version | -${noargopts}[?hVc])
            return
            ;;
        -${noargopts}m)
            _comp_cmd_python__compgen_modules "$1"
            return
            ;;
        -${noargopts}Q)
            _comp_compgen -- -W 'old new warn warnall' -P "$prefix"
            return
            ;;
        -${noargopts}W)
            _comp_xfunc_python_compgen_warning_actions
            return
            ;;
        -${noargopts}X)
            _comp_compgen_split -- "$("$1" -h 2>&1 |
                _comp_awk '$1 == "-X" && $2 ~ /:$/ {
                    sub(":$","",$2); sub("=.*","=",$2); print $2
                }')"
            [[ ${COMPREPLY-} == *= ]] && compopt -o nospace
            return
            ;;
        --jit)
            # TODO: quite a few others, parse from "--jit help" output?
            _comp_compgen -- -W "help off"
            return
            ;;
    esac

    if [[ $prev == -- || $cur != -* ]]; then
        _comp_compgen_filedir '@(py?([cowz])|zip)'
    else
        _comp_compgen_help - <<<"$("$1" -h |
            _comp_awk '{ sub("\\(-bb:","\n-bb "); print }')"
    fi
} &&
    complete -F _comp_cmd_python \
        python python2 python2.7 python3 python3.{3..13} \
        pypy pypy3 pyston pyston3 micropython

# ex: filetype=sh
