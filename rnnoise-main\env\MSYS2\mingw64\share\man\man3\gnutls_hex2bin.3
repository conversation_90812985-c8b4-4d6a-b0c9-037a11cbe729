.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hex2bin" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hex2bin \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_hex2bin(const char * " hex_data ", size_t " hex_size ", void * " bin_data ", size_t * " bin_size ");"
.SH ARGUMENTS
.IP "const char * hex_data" 12
string with data in hex format
.IP "size_t hex_size" 12
size of hex data
.IP "void * bin_data" 12
output array with binary data
.IP "size_t * bin_size" 12
when calling should hold maximum size of  \fIbin_data\fP ,
on return will hold actual length of  \fIbin_data\fP .
.SH "DESCRIPTION"
Convert a buffer with hex data to binary data. This function
unlike \fBgnutls_hex_decode()\fP can parse hex data with separators
between numbers. That is, it ignores any non\-hex characters.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
2.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
