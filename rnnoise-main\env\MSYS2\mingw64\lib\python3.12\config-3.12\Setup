# -*- makefile -*-
# The file Setup is used by the makesetup script to construct the files
# Makefile and config.c, from Makefile.pre and config.c.in,
# respectively.  Note that Makefile.pre is created from Makefile.pre.in
# by the toplevel configure script.

# (VPATH notes: Setup and Makefile.pre are in the build directory, as
# are Makefile and config.c; the *.in files are in the source directory.)

# Each line in this file describes one or more optional modules.
# Modules configured here will not be compiled by the setup.py script,
# so the file can be used to override setup.py's behavior.
# Tag lines containing just the word "*static*", "*shared*" or "*disabled*"
# (without the quotes but with the stars) are used to tag the following module
# descriptions. Tag lines may alternate throughout this file.  Modules are
# built statically when they are preceded by a "*static*" tag line or when
# there is no tag line between the start of the file and the module
# description.  Modules are built as a shared library when they are preceded by
# a "*shared*" tag line.  Modules are not built at all, not by the Makefile,
# nor by the setup.py script, when they are preceded by a "*disabled*" tag
# line.

# Lines have the following structure:
#
# <module> ... [<sourcefile> ...] [<cpparg> ...] [<library> ...]
#
# <sourcefile> is anything ending in .c (.C, .cc, .c++ are C++ files)
# <cpparg> is anything starting with -I, -D, -U or -C
# <library> is anything ending in .a or beginning with -l or -L
# <module> is anything else but should be a valid Python
# identifier (letters, digits, underscores, beginning with non-digit)
#
# (As the makesetup script changes, it may recognize some other
# arguments as well, e.g. *.so and *.sl as libraries.  See the big
# case statement in the makesetup script.)
#
# Lines can also have the form
#
# <name> = <value>
#
# which defines a Make variable definition inserted into Makefile.in.
# You can also use any Make variable that is detected by configure and
# defined in Makefile.pre.in, e.g. OpenSSL flags $(OPENSSL_INCLUDES).
#
# Rules generated by makesetup use additional variables:
#
# - All source file rules have a dependency on $(PYTHON_HEADERS) and on
#   optional variable $(MODULES_{mod_upper}_DEPS).
# - If no <cpparg> and no <library> arguments are given, then makesetup
#   defaults to $(MODULES_{mod_upper}_CFLAGS) cppargs and
#   $(MODULES_{mod_upper}_LDFLAGS) libraries. The variables are typically
#   defined by configure.
#
# The build process works like this:
#
# 1. Build all modules that are declared as static in Modules/Setup,
#    combine them into libpythonxy.a, combine that into python.
# 2. Build all modules that are listed as shared in Modules/Setup.
# 3. Invoke setup.py. That builds all modules that
#    a) are not builtin, and
#    b) are not listed in Modules/Setup, and
#    c) can be build on the target
#
# Therefore, modules declared to be shared will not be
# included in the config.c file, nor in the list of objects to be
# added to the library archive, and their linker options won't be
# added to the linker options. Rules to create their .o files and
# their shared libraries will still be added to the Makefile, and
# their names will be collected in the Make variable SHAREDMODS.  This
# is used to build modules as shared libraries.  (They can be
# installed using "make sharedinstall", which is implied by the
# toplevel "make install" target.)  (For compatibility,
# *noconfig* has the same effect as *shared*.)
#
# NOTE: As a standard policy, as many modules as can be supported by a
# platform should be listed below.  The distribution comes with all
# modules enabled that are supported by most platforms and don't
# require you to download sources from elsewhere.
#
# NOTE: Avoid editing this file directly. Local changes should go into
# Modules/Setup.local file. To enable all modules for testing, run
#
#    sed -n -E 's/^#([a-z_\*].*)$/\1/p' Modules/Setup > Modules/Setup.local


# Some special rules to define PYTHONPATH.
# Edit the definitions below to indicate which options you are using.
# Don't add any whitespace or comments!

# Directories where library files get installed.
# DESTLIB is for Python modules; MACHDESTLIB for shared libraries.
DESTLIB=$(LIBDEST)
MACHDESTLIB=$(BINLIBDEST)

# NOTE: all the paths are now relative to the prefix that is computed
# at run time!

# Standard path -- don't edit.
# No leading colon since this is the first entry.
# Empty since this is now just the runtime prefix.
DESTPATH=

# Site specific path components -- should begin with : if non-empty
SITEPATH=

# Standard path components for test modules
TESTPATH=

COREPYTHONPATH=$(DESTPATH)$(SITEPATH)$(TESTPATH)
PYTHONPATH=$(COREPYTHONPATH)


# ---
# Built-in modules required to get a functioning interpreter are listed in
# Modules/Setup.bootstrap.

# ---
# The rest of the modules listed in this file are all commented out by
# default.  Usually they can be detected and built as dynamically
# loaded modules by setup.py.  If you're on a platform that doesn't
# support dynamic loading, want to compile modules statically into the
# Python binary, or need to specify some odd set of compiler switches,
# you can uncomment the appropriate lines below.

# Uncommenting the following line tells makesetup that all following
# modules are to be built as shared libraries (see above for more
# detail; also note that *static* or *disabled* cancels this effect):

#*shared*

# Modules that should always be present (POSIX and Windows):

#_asyncio _asynciomodule.c
#_bisect _bisectmodule.c
#_contextvars _contextvarsmodule.c
#_csv _csv.c
#_datetime _datetimemodule.c
#_decimal _decimal/_decimal.c
#_heapq _heapqmodule.c
#_json _json.c
#_lsprof _lsprof.c rotatingtree.c
#_multiprocessing -I$(srcdir)/Modules/_multiprocessing _multiprocessing/multiprocessing.c _multiprocessing/semaphore.c
#_opcode _opcode.c
#_pickle _pickle.c
#_queue _queuemodule.c
#_random _randommodule.c
#_socket socketmodule.c
#_statistics _statisticsmodule.c
#_struct _struct.c
#_typing _typingmodule.c
#_zoneinfo _zoneinfo.c
#array arraymodule.c
#audioop audioop.c
#binascii binascii.c
#cmath cmathmodule.c
#math mathmodule.c
#mmap mmapmodule.c
#select selectmodule.c

# XML
#_elementtree _elementtree.c
#pyexpat pyexpat.c

# hashing builtins
#_blake2 _blake2/blake2module.c _blake2/blake2b_impl.c _blake2/blake2s_impl.c
#_md5 md5module.c -I$(srcdir)/Modules/_hacl/include _hacl/Hacl_Hash_MD5.c -D_BSD_SOURCE -D_DEFAULT_SOURCE
#_sha1 sha1module.c -I$(srcdir)/Modules/_hacl/include _hacl/Hacl_Hash_SHA1.c -D_BSD_SOURCE -D_DEFAULT_SOURCE
#_sha2 sha2module.c -I$(srcdir)/Modules/_hacl/include Modules/_hacl/libHacl_Hash_SHA2.a
#_sha3 sha3module.c -I$(srcdir)/Modules/_hacl/include _hacl/Hacl_Hash_SHA3.c -D_BSD_SOURCE -D_DEFAULT_SOURCE

# text encodings and unicode
#_codecs_cn cjkcodecs/_codecs_cn.c
#_codecs_hk cjkcodecs/_codecs_hk.c
#_codecs_iso2022 cjkcodecs/_codecs_iso2022.c
#_codecs_jp cjkcodecs/_codecs_jp.c
#_codecs_kr cjkcodecs/_codecs_kr.c
#_codecs_tw cjkcodecs/_codecs_tw.c
#_multibytecodec cjkcodecs/multibytecodec.c
#unicodedata unicodedata.c

# Modules with some UNIX dependencies

#_posixsubprocess _posixsubprocess.c
#_posixshmem -I$(srcdir)/Modules/_multiprocessing _multiprocessing/posixshmem.c -lrt
#fcntl fcntlmodule.c
#grp grpmodule.c
#ossaudiodev ossaudiodev.c
#resource resource.c
#spwd spwdmodule.c
#syslog syslogmodule.c
#termios termios.c

# Modules with UNIX dependencies that require external libraries

#_crypt _cryptmodule.c -lcrypt
#nis nismodule.c -I/usr/include/tirpc -lnsl -ltirpc

# Modules that require external libraries.

#_bz2 _bz2module.c -lbz2
#_ctypes _ctypes/_ctypes.c _ctypes/callbacks.c _ctypes/callproc.c _ctypes/stgdict.c _ctypes/cfield.c -ldl -lffi -DHAVE_FFI_PREP_CIF_VAR -DHAVE_FFI_PREP_CLOSURE_LOC -DHAVE_FFI_CLOSURE_ALLOC
# The _dbm module supports NDBM, GDBM with compat module, and Berkeley DB.
#_dbm _dbmmodule.c -lgdbm_compat -DUSE_GDBM_COMPAT
#_gdbm _gdbmmodule.c -lgdbm
#_lzma _lzmamodule.c -llzma
#_uuid _uuidmodule.c -luuid
#zlib  zlibmodule.c -lz

# The readline module also supports libeditline (-leditline).
# Some systems may require -ltermcap or -ltermlib.
#readline readline.c -lreadline -ltermcap

# OpenSSL bindings
#_ssl _ssl.c $(OPENSSL_INCLUDES) $(OPENSSL_LDFLAGS) $(OPENSSL_LIBS)
#_hashlib _hashopenssl.c $(OPENSSL_INCLUDES) $(OPENSSL_LDFLAGS) -lcrypto

# To statically link OpenSSL:
# _ssl _ssl.c $(OPENSSL_INCLUDES) $(OPENSSL_LDFLAGS) \
#     -l:libssl.a -Wl,--exclude-libs,libssl.a \
#     -l:libcrypto.a -Wl,--exclude-libs,libcrypto.a
# _hashlib _hashopenssl.c $(OPENSSL_INCLUDES) $(OPENSSL_LDFLAGS) \
#     -l:libcrypto.a -Wl,--exclude-libs,libcrypto.a

# The _tkinter module.
#
# The command for _tkinter is long and site specific.  Please
# uncomment and/or edit those parts as indicated.  If you don't have a
# specific extension (e.g. Tix or BLT), leave the corresponding line
# commented out.  (Leave the trailing backslashes in!  If you
# experience strange errors, you may want to join all uncommented
# lines and remove the backslashes -- the backslash interpretation is
# done by the shell's "read" command and it may not be implemented on
# every system.

# *** Always uncomment this (leave the leading underscore in!):
#_tkinter _tkinter.c tkappinit.c -DWITH_APPINIT $(TCLTK_INCLUDES) $(TCLTK_LIBS) \
# *** Uncomment and edit to reflect where your Tcl/Tk libraries are:
#	-L/usr/local/lib \
# *** Uncomment and edit to reflect where your Tcl/Tk headers are:
#	-I/usr/local/include \
# *** Uncomment and edit to reflect where your X11 header files are:
#	-I/usr/X11R6/include \
# *** Or uncomment this for Solaris:
#	-I/usr/openwin/include \
# *** Uncomment and edit for Tix extension only:
#	-DWITH_TIX -ltix8.1.8.2 \
# *** Uncomment and edit for BLT extension only:
#	-DWITH_BLT -I/usr/local/blt/blt8.0-unoff/include -lBLT8.0 \
# *** Uncomment and edit for PIL (TkImaging) extension only:
#     (See http://www.pythonware.com/products/pil/ for more info)
#	-DWITH_PIL -I../Extensions/Imaging/libImaging  tkImaging.c \
# *** Uncomment and edit for TOGL extension only:
#	-DWITH_TOGL togl.c \
# *** Uncomment and edit to reflect where your X11 libraries are:
#	-L/usr/X11R6/lib \
# *** Or uncomment this for Solaris:
#	-L/usr/openwin/lib \
# *** Uncomment these for TOGL extension only:
#	-lGL -lGLU -lXext -lXmu \
# *** Uncomment for AIX:
#	-lld \
# *** Always uncomment this; X11 libraries to link with:
#	-lX11

# Some system have -lcurses 
#_curses -lncurses -lncursesw -ltermcap _cursesmodule.c
#_curses_panel -lpanel -lncurses _curses_panel.c

# macOS specific module, needs SystemConfiguration and CoreFoundation framework
# _scproxy _scproxy.c

# Examples

#xx xxmodule.c
#xxlimited xxlimited.c
#xxlimited_35 xxlimited_35.c
#xxsubtype xxsubtype.c

# Testing

#_xxsubinterpreters _xxsubinterpretersmodule.c
#_xxinterpchannels _xxinterpchannelsmodule.c
#_xxtestfuzz _xxtestfuzz/_xxtestfuzz.c _xxtestfuzz/fuzzer.c
#_testbuffer _testbuffer.c
#_testinternalcapi _testinternalcapi.c

# Some testing modules MUST be built as shared libraries.

#*shared*
#_ctypes_test _ctypes/_ctypes_test.c
#_testcapi _testcapimodule.c
#_testimportmultiple _testimportmultiple.c
#_testmultiphase _testmultiphase.c
#_testsinglephase _testsinglephase.c

# ---
# Uncommenting the following line tells makesetup that all following modules
# are not built (see above for more detail).
#
#*disabled*
#
# _sqlite3 _tkinter _curses pyexpat
# _codecs_jp _codecs_kr _codecs_tw unicodedata
