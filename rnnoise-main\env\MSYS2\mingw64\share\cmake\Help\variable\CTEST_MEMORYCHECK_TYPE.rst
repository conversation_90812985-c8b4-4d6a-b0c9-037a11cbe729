CTEST_MEMORYCHECK_TYPE
----------------------

.. versionadded:: 3.1

Specify the CTest ``MemoryCheckType`` setting
in a :manual:`ctest(1)` dashboard client script.
Valid values are ``<PERSON><PERSON>rind``, ``Purify``, ``<PERSON><PERSON><PERSON><PERSON><PERSON>``, ``<PERSON><PERSON><PERSON><PERSON>``,
``CudaSanitizer``, ``ThreadSanitizer``, ``AddressSanitizer``, ``LeakSanitizer``,
``MemorySanitizer`` and ``UndefinedBehaviorSanitizer``.
