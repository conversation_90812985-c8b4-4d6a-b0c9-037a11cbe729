.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_sign_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_sign_data \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_sign_data(gnutls_privkey_t " signer ", gnutls_digest_algorithm_t " hash ", unsigned int " flags ", const gnutls_datum_t * " data ", gnutls_datum_t * " signature ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t signer" 12
Holds the key
.IP "gnutls_digest_algorithm_t hash" 12
should be a digest algorithm
.IP "unsigned int flags" 12
Zero or one of \fBgnutls_privkey_flags_t\fP
.IP "const gnutls_datum_t * data" 12
holds the data to be signed
.IP "gnutls_datum_t * signature" 12
will contain the signature allocated with \fBgnutls_malloc()\fP
.SH "DESCRIPTION"
This function will sign the given data using a signature algorithm
supported by the private key. Signature algorithms are always used
together with a hash functions.  Different hash functions may be
used for the RSA algorithm, but only the SHA family for the DSA keys.

You may use \fBgnutls_pubkey_get_preferred_hash_algorithm()\fP to determine
the hash algorithm.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
