.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_anti_replay_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_anti_replay_init \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_anti_replay_init(gnutls_anti_replay_t * " anti_replay ");"
.SH ARGUMENTS
.IP "gnutls_anti_replay_t * anti_replay" 12
is a pointer to \fBgnutls_anti_replay_t\fP type
.SH "DESCRIPTION"
This function will allocate and initialize the  \fIanti_replay\fP context
to be usable for detect replay attacks. The context can then be
attached to a  \fIgnutls_session_t\fP with
\fBgnutls_anti_replay_enable()\fP.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
3.6.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
