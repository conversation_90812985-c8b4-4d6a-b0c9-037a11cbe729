<!--
  Document Type Definition for Texinfo XML output (the '-'-xml option).

  Copyright 2001-2023 Free Software Foundation, Inc.

  Copying and distribution of this file, with or without modification,
  are permitted in any medium without royalty provided the copyright
  notice and this notice are preserved.

  As soon as this file is nontrivially modified, update
  TEXINFO_DTD_VERSION in configure.ac (if not already done).
  See comments there for explanation.

  Email <EMAIL> with any discussion.
  (Original author: <PERSON>.)
-->

<!-- * ENTITIES * -->

<!-- Meta-information -->
<!-- copying | titlepage could be there too -->
<!ENTITY % metainformation "setfilename | settitle | dircategory | direntry
                            | hyphenation
                            | documentdescription 
                            | shorttitlepage">
<!ENTITY % variable.cmds "set | clear">

<!-- @-commands definition commands -->
<!-- def(code)index are there as they define an @*index @-command -->
<!ENTITY % define.cmds "definfoenclose | alias | macro | rmacro | linemacro
                        | unmacro | defindex | defcodeindex">

<!-- Unique options -->
<!ENTITY % unique.option.cmds "novalidate | setcontentsaftertitlepage 
   | setshortcontentsaftertitlepage | documentencoding 
   | everyheadingmarks | everyfootingmarks | evenheadingmarks | oddheadingmarks
   | evenfootingmarks | oddfootingmarks | fonttextsize | pagesizes
   | setchapternewpage | footnotestyle | finalout

   | allowcodebreaks | exampleindent | afourpaper 
   | afivepaper | afourlatex | afourwide | headings
   | everyheading | everyfooting | evenheading | evenfooting | oddheading
   | oddfooting | smallbook | cropmarks">

<!-- Options -->
<!ENTITY % multiple.option.cmds "frenchspacing | documentlanguage 
    | %variable.cmds; | kbdinputstyle | paragraphindent 
    | firstparagraphindent | urefbreakstyle | xrefautomaticsectiontitle
    | deftypefnnewline
    | codequoteundirected | codequotebacktick | microtype | raisesections
    | lowersections | clickstyle
    | synindex | syncodeindex">

<!ENTITY % option.cmds "%unique.option.cmds; | %multiple.option.cmds;">

<!-- ToC -->
<!ENTITY % toc "contents | shortcontents | summarycontents">

<!-- In title page -->
<!ENTITY % intitlepage.cmds "author | title | subtitle">

<!-- index entry commands -->
<!ENTITY % indexentry.cmds "cindex | cpindex | findex | fnindex
  | kindex | kyindex | pindex | pgindex | tindex | tpindex
  | vindex | vrindex | indexcommand | subentry">

<!-- API definition commands -->
<!ENTITY % def.cmds "deffn | defvr | deftypefn | deftypeop | deftypevr
      | defcv | deftypecv | defop | deftp | defun | defmac 
      | defspec | defvar | defopt | deftypefun | deftypevar 
      | defivar | deftypeivar | defmethod | deftypemethod | defblock">

<!ENTITY % raw.cmds "html | tex | latex | docbook | xml | ignore">

<!-- Commands that appear everywhere, both with block and in paragraphs -->
<!-- include is in general absent, since it is replaced, but it is
     present if the file was not found -->
<!ENTITY % ubiquitous.cmds "sp | anchor | indent | noindent | %raw.cmds;
                 | %define.cmds; | %option.cmds; | errormsg | include
                 | %indexentry.cmds; | refill | quote-arg | allow-recursion">

<!-- Block commands -->
<!ENTITY % block.cmds "menu | para | pre | quotation | smallquotation
                  | indentedblock | smallindentedblock
                  | example | smallexample | lisp | smalllisp
                  | cartouche | float | format | smallformat
                  | display | smalldisplay
                  | raggedright | flushleft | flushright
                  | itemize | enumerate | group | nodedescriptionblock
                  | table | vtable | ftable | multitable | displaymath
                  | %def.cmds; | verbatim">

<!-- Headings -->
<!ENTITY % heading.cmds "majorheading | chapheading | heading | subheading
                     | subsubheading">

<!-- Block content, as opposed to Inline content -->
<!ENTITY % block  "%block.cmds; | verbatiminclude
                  | %heading.cmds;
                  | %ubiquitous.cmds;
                  | image | titlefont | center | nodedescription
                  | insertcopying | page | need | vskip">

<!-- API definition line -->
<!ENTITY % definition.args "defcategory | deffunction | defsymbol
                          | defvariable | defparam | defdelimiter
                          | deftype | defparamtype | defdatatype
                          | defclass | defclassvar | defoperation">

<!-- Language codes -->
<!ENTITY % languagecodes "aa|ab|af|am|ar|as|ay|az|ba|be|bg|bh|bi|bn|bo|br|ca|co|cs|cy|da|de|dz|el|en|eo|es|et|eu|fa|fi|fj|fo|fr|fy|ga|gd|gl|gn|gu|ha|he|hi|hr|hu|hy|ia|id|ie|ik|is|it|iu|ja|jw|ka|kk|kl|km|kn|ko|ks|ku|ky|la|ln|lo|lt|lv|mg|mi|mk|ml|mn|mo|mr|ms|mt|my|na|ne|nl|no|oc|om|or|pa|pl|ps|pt|qu|rm|rn|ro|ru|rw|sa|sd|sg|sh|si|sk|sl|sm|sn|so|sq|sr|ss|st|su|sv|sw|ta|te|tg|th|ti|tk|tl|tn|to|tr|ts|tt|tw|ug|uk|ur|uz|vi|vo|wo|xh|yi|yo|za|zh|zu">

<!-- Sectioning -->
<!ENTITY % section.level1 "top | part | chapter | unnumbered | centerchap | appendix">

<!ENTITY % section.level2 "section | unnumberedsec | appendixsec">

<!ENTITY % section.level3 "subsection | unnumberedsubsec | appendixsubsec">

<!ENTITY % section.level4 "subsubsection | unnumberedsubsubsec 
                           | appendixsubsubsec">

<!ENTITY % section.all "%section.level1; | %section.level2; | %section.level3;
                        | %section.level4;">

<!ENTITY % toplevelonly.content "%toc; | copying | titlepage
                             | printindex | listoffloats">

<!-- toplevel content, not in copying nor footnote -->
<!-- FIXME can also contain @* &linebreak; which is CDATA-->
<!ENTITY % main.content "%block; | %toplevelonly.content; | node">

<!ENTITY % section.level0.content "(%main.content;
                                   | %section.level1;
                                   | %section.level2;
                                   | %section.level3;
                                   | %section.level4;)*">


<!ENTITY % section.level1.content "(%main.content;
                                   | %section.level2;
                                   | %section.level3;
                                   | %section.level4;)*">

<!ENTITY % section.level2.content "(%main.content;
                                   | %section.level3;
                                   | %section.level4;)*">

<!ENTITY % section.level3.content "(%main.content;
                                   | %section.level4;)*">

<!ENTITY % section.level4.content "(%main.content;)*">

<!ENTITY % Spaces.elements "formfeed | verticaltab">

<!-- Inline -->
<!ENTITY % Inline.emphasize "strong | emph">
<!ENTITY % Inline.fonts "b | i | r | sansserif | slanted | t
                         | sc">
<!ENTITY % Inline.footnote "footnote">
<!ENTITY % Inline.markup "code | command | env | file | option | samp | verb
                          | dfn | cite | key | kbd | var | acronym | abbr
                          | indicateurl | w | asis | sub | sup ">
<!ENTITY % Inline.math "math | dmn">
<!ENTITY % Inline.reference "xref | ref | pxref | link | inforef ">
<!ENTITY % Inline.hyperreference "email | uref | url">
<!ENTITY % Inline.misc "click | clicksequence | logo | punct | spacecmd | today
                        | linebreak | accent | dotless | ctrl | inlineraw
                        | inlinefmt | inlinefmtifelse | inlineifclear
                        | inlineifset | infoenclose | noeos | U | %Spaces.elements;">

<!-- on lines -->
<!ENTITY % Inline.linesimpletext "%Inline.emphasize; | %Inline.misc; | %Inline.fonts;
          | %Inline.markup; | %Inline.math; | %Inline.hyperreference;">
<!ENTITY % Inline.linetext "%Inline.linesimpletext; | %Inline.reference;
                            | %Inline.footnote;">
<!ENTITY % Inline.line "%Inline.linetext; | image">
<!-- %Inline.line; appears on @vtable item or @def*, so it must be valid in
     indexterm generated in such context.  In that context seealso,
     seeentry and sortas are not valid.
     In index commands (@cindex...) indexterm, however, footnotes and
     cross-reference are not valid, so %Inline.linesimpletext;, for example,
     would have been better than %Inline.line;.
     defbracketed can only appear in @def* indexterm, it is invalid in other
     index term contexts.
     It is not possible to have different contents in different contexts
     in the DTD, so we use the contents for all the contexts -->
<!ENTITY % Inline.indexentryline "%Inline.line; | seealso | seeentry | sortas
                                  | defbracketed">
<!ENTITY % Inline.fullline "%Inline.line; | titlefont | anchor">

<!ENTITY % Inline.defargline "%Inline.line; | defbracketed">

<!-- on headings specification lines -->
<!ENTITY % Inline.headingmark "thischapter | thischaptername
           | thischapternum | thissection | thissectionname | thissectionnum
           | thisfile | thispage | thistitle">
<!ENTITY % Inline.headingline "%Inline.linesimpletext; | divideheading
                               | %Inline.headingmark;">

<!-- in paragraphs -->
<!ENTITY % Inline.phrase
           "%Inline.line; | %ubiquitous.cmds;">

<!ENTITY % Inline.phraseormark
           "%Inline.phrase; | %Inline.headingmark;">

<!-- in raw (everything except sectioning commands) -->
<!ENTITY % Raw.content "#PCDATA | %intitlepage.cmds; | %block;
           | %Inline.linetext; | exdent | %toplevelonly.content;
           | %metainformation;">

<!-- arguments/attribute values. Many are missing -->
<!ENTITY % onoff "on|off">
<!ENTITY % spacetype "spc|tab|nl">
<!ENTITY % topbottom "top|bottom">
<!ENTITY % zeroone "0|1">

<!-- ubiquitous attribute -->
<!ENTITY % spacesattr
  "spaces CDATA #IMPLIED">
<!ENTITY % spacesaftercmdattr
  "spacesaftercmd CDATA #IMPLIED">
<!-- spaces possibly before command opening brace and in braces -->
<!ENTITY % spacesafterincmdattr
   "%spacesattr; %spacesaftercmdattr;">
<!ENTITY % trailingspacesattr
  "trailingspaces CDATA #IMPLIED">
<!-- spaces and trailing spaces for node and for bracketed @def* args -->
<!ENTITY % spacestrailingspacesattr
  "%spacesattr; %trailingspacesattr;">
<!ENTITY % lineattr
  "line CDATA #IMPLIED">
<!ENTITY % linespaceattr
  "%lineattr; %spacesattr;">
<!ENTITY % bracketedattr
  "bracketed (%onoff;) #IMPLIED">
<!-- block commands that have spaces also have endspaces -->
<!-- this is also used for commands without arguments
     when they have a @comment on their line -->
<!ENTITY % spacesblockattr
          "%spacesattr; endspaces CDATA #IMPLIED">

<!-- ELEMENTS -->

<!-- TOP Level Element -->
<!ELEMENT texinfo (filename?, preamblebeforebeginning?, (%metainformation;
                    | %section.all; | %main.content;)*,
                   bye?, postambleafterend?)>
<!ATTLIST texinfo xml:lang (%languagecodes;) 'en'>

<!-- meta-information -->
<!ELEMENT filename EMPTY>
<!ATTLIST filename
          file   CDATA #IMPLIED>
<!ELEMENT setfilename (#PCDATA  | %Inline.line;)*>
<!ATTLIST setfilename
          %spacesattr;
          file   CDATA #IMPLIED>
<!ELEMENT settitle        (#PCDATA | %Inline.line;)*>
<!ATTLIST settitle
          %spacesattr;>
<!ELEMENT shorttitlepage  (#PCDATA | %Inline.line;)*>
<!ATTLIST shorttitlepage
          %spacesattr;>
<!ELEMENT dircategory     (#PCDATA | %Inline.line;)*>
<!ATTLIST dircategory
          %spacesattr;>

<!ELEMENT set    (#PCDATA)>
<!ATTLIST set
          %lineattr;
          name CDATA #REQUIRED>
<!ELEMENT clear  EMPTY>
<!ATTLIST clear
          %lineattr;
          name CDATA #REQUIRED>
<!ELEMENT unmacro  EMPTY>
<!ATTLIST unmacro
          %lineattr;
          name CDATA #REQUIRED>
<!ELEMENT definfoenclose EMPTY>
<!ATTLIST definfoenclose
          %linespaceattr;
          command  NMTOKEN #REQUIRED
          open     CDATA   #IMPLIED
          close    CDATA   #IMPLIED>
<!ELEMENT alias          EMPTY>
<!ATTLIST alias
          %linespaceattr;
          new       NMTOKEN #REQUIRED
          existing  NMTOKEN #REQUIRED>
<!ELEMENT clickstyle  (#PCDATA)>
<!ATTLIST clickstyle
          %lineattr;
          command  NMTOKEN #REQUIRED>

<!-- preamble before texinfo code beginning (empty lines and \input) -->
<!ELEMENT preamblebeforebeginning (#PCDATA)>
<!-- everything appearing after @bye -->
<!ELEMENT postambleafterend (#PCDATA)>

<!-- ToC -->
<!ELEMENT contents      EMPTY>
<!ATTLIST contents
          %lineattr;>
<!ELEMENT shortcontents EMPTY>
<!ATTLIST shortcontents
          %lineattr;>
<!ELEMENT summarycontents EMPTY>
<!ATTLIST summarycontents
          %lineattr;>

<!-- Global unique options -->
<!ELEMENT novalidate EMPTY>
<!ATTLIST novalidate
          %lineattr;>
<!ELEMENT setcontentsaftertitlepage EMPTY>
<!ATTLIST setcontentsaftertitlepage
          %lineattr;>
<!ELEMENT setshortcontentsaftertitlepage EMPTY>
<!ATTLIST setshortcontentsaftertitlepage
          %lineattr;>
<!ELEMENT documentencoding (#PCDATA)>
<!ATTLIST documentencoding
          %spacesattr;
          encoding CDATA #REQUIRED>
<!ELEMENT everyheadingmarks EMPTY>
<!ATTLIST everyheadingmarks
          %linespaceattr;
          value (%topbottom;) #REQUIRED>
<!ELEMENT everyfootingmarks EMPTY>
<!ATTLIST everyfootingmarks
          %linespaceattr;
          value (%topbottom;) #REQUIRED>
<!ELEMENT evenheadingmarks EMPTY>
<!ATTLIST evenheadingmarks
          %linespaceattr;
          value (%topbottom;) #REQUIRED>
<!ELEMENT oddheadingmarks EMPTY>
<!ATTLIST oddheadingmarks
          %linespaceattr;
          value (%topbottom;) #REQUIRED>
<!ELEMENT evenfootingmarks EMPTY>
<!ATTLIST evenfootingmarks
          %linespaceattr;
          value (%topbottom;) #REQUIRED>
<!ELEMENT oddfootingmarks EMPTY>
<!ATTLIST oddfootingmarks
          %linespaceattr;
          value (%topbottom;) #REQUIRED>
<!-- "10|11" -->
<!ELEMENT fonttextsize  EMPTY>
<!ATTLIST fonttextsize
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT pagesizes (#PCDATA)>
<!ATTLIST pagesizes
          %spacesattr;>
<!ELEMENT setchapternewpage  EMPTY>
<!-- "off|on|odd" -->
<!ATTLIST setchapternewpage
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT footnotestyle      EMPTY>
<!-- "end|separate" -->
<!ATTLIST footnotestyle
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT allowcodebreaks    EMPTY>
<!-- "false|true" -->
<!ATTLIST allowcodebreaks
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT exampleindent      EMPTY>
<!ATTLIST exampleindent
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT afourpaper      EMPTY>
<!ATTLIST afourpaper
          %lineattr;>
<!ELEMENT afivepaper      EMPTY>
<!ATTLIST afivepaper
          %lineattr;>
<!ELEMENT afourlatex      EMPTY>
<!ATTLIST afourlatex
          %lineattr;>
<!ELEMENT afourwide       EMPTY>
<!ATTLIST afourwide
          %lineattr;>
<!ELEMENT finalout        EMPTY>
<!ATTLIST finalout
          %lineattr;>
<!ELEMENT headings        EMPTY>
<!-- "off|on|single|double|singleafter|doubleafter" -->
<!ATTLIST headings
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT everyheading        (#PCDATA | %Inline.headingline;)*>
<!ATTLIST everyheading
          %spacesattr;>
<!ELEMENT everyfooting        (#PCDATA | %Inline.headingline;)*>
<!ATTLIST everyfooting
          %spacesattr;>
<!ELEMENT evenheading        (#PCDATA | %Inline.headingline;)*>
<!ATTLIST evenheading
          %spacesattr;>
<!ELEMENT evenfooting        (#PCDATA | %Inline.headingline;)*>
<!ATTLIST evenfooting
          %spacesattr;>
<!ELEMENT oddheading        (#PCDATA | %Inline.headingline;)*>
<!ATTLIST oddheading
          %spacesattr;>
<!ELEMENT oddfooting        (#PCDATA | %Inline.headingline;)*>
<!ATTLIST oddfooting
          %spacesattr;>
<!ELEMENT vskip (#PCDATA)>
<!ELEMENT smallbook       EMPTY>
<!ATTLIST smallbook
          %lineattr;>
<!ELEMENT cropmarks       EMPTY>
<!ATTLIST cropmarks
          %lineattr;>

<!-- Global options -->
<!ELEMENT documentlanguage (#PCDATA)>
<!ATTLIST documentlanguage
          %spacesattr;
          xml:lang (%languagecodes;) 'en'>
<!ELEMENT frenchspacing (#PCDATA)> <!-- must be on or off -->
<!ATTLIST frenchspacing
          %linespaceattr;
          value (%onoff;) 'off'>
<!ELEMENT kbdinputstyle   EMPTY>
<!-- "code|example|distinct" -->
<!ATTLIST kbdinputstyle
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT paragraphindent EMPTY>
<!ATTLIST paragraphindent
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT firstparagraphindent EMPTY>
<!ATTLIST firstparagraphindent
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT microtype EMPTY>
<!-- "on|off" -->
<!ATTLIST microtype
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT urefbreakstyle  EMPTY>
<!-- "after|before|none" -->
<!ATTLIST urefbreakstyle
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT xrefautomaticsectiontitle EMPTY>
<!ATTLIST xrefautomaticsectiontitle
          %linespaceattr;
          value (%onoff;) #REQUIRED>
<!ELEMENT deftypefnnewline EMPTY>
<!ATTLIST deftypefnnewline
          %linespaceattr;
          value (%onoff;) #REQUIRED>
<!ELEMENT codequoteundirected EMPTY>
<!ATTLIST codequoteundirected
          %linespaceattr;
          value (%onoff;) #REQUIRED>
<!ELEMENT codequotebacktick EMPTY>
<!ATTLIST codequotebacktick
          %linespaceattr;
          value (%onoff;) #REQUIRED>
<!ELEMENT raisesections   EMPTY>
<!ATTLIST raisesections
          %lineattr;>
<!ELEMENT lowersections   EMPTY>
<!ATTLIST lowersections
          %lineattr;>

<!-- Titlepage and copying -->
<!ELEMENT copying    (%block;)*>
<!ATTLIST copying
          %spacesblockattr;>
<!ELEMENT insertcopying EMPTY>

<!ELEMENT titlepage  (%intitlepage.cmds; | %block;)*>
<!ATTLIST titlepage
          %spacesblockattr;>
<!ELEMENT author     (#PCDATA | %Inline.line;)*>
<!ATTLIST author
          %spacesattr;>
<!ELEMENT title      (#PCDATA | %Inline.line;)*>
<!ATTLIST title
          %spacesattr;>
<!ELEMENT subtitle   (#PCDATA | %Inline.line;)*>
<!ATTLIST subtitle
          %spacesattr;>

<!-- Formatting -->
<!ELEMENT sp EMPTY>
<!ATTLIST sp
          %linespaceattr;
          value CDATA #IMPLIED>
<!ELEMENT page EMPTY>
<!ATTLIST page
          %lineattr;>
<!ELEMENT need EMPTY>
<!ATTLIST need
          %linespaceattr;
          value CDATA #REQUIRED>
<!ELEMENT indent   EMPTY>
<!ELEMENT noindent EMPTY>

<!-- heading marks -->
<!ELEMENT thischapter EMPTY>
<!ELEMENT thischaptername EMPTY>
<!ELEMENT thischapternum EMPTY>
<!ELEMENT thissection EMPTY>
<!ELEMENT thissectionname EMPTY>
<!ELEMENT thissectionnum EMPTY>
<!ELEMENT thisfile EMPTY>
<!ELEMENT thispage EMPTY>
<!ELEMENT thistitle EMPTY>

<!-- emacs-page
<!ELEMENT node (nodename, nodenext?, nodeprev?, nodeup?,
                (%section.all; | %main.content;)*) >
     Nodes -->
<!ELEMENT node (nodename, nodenext?, nodeprev?, nodeup?)>
<!ATTLIST node
          %spacesattr;
          name CDATA #IMPLIED>

<!ELEMENT nodename (#PCDATA | %Inline.line;)*>
<!ATTLIST nodename
          trailingspaces CDATA #IMPLIED>
<!ELEMENT nodenext (#PCDATA | %Inline.line;)*>
<!ATTLIST nodenext
          %spacestrailingspacesattr;
          automatic (%onoff;) 'off'>
<!ELEMENT nodeprev (#PCDATA | %Inline.line;)*>
<!ATTLIST nodeprev
          %spacestrailingspacesattr;
          automatic (%onoff;) 'off'>
<!ELEMENT nodeup   (#PCDATA | %Inline.line;)*>
<!ATTLIST nodeup
          %spacestrailingspacesattr;
          automatic (%onoff;) 'off'>

<!ENTITY % sectionsattr
  "%spacesattr;
   originalcommand CDATA #IMPLIED">
<!-- Sectioning -->
<!ELEMENT top           (sectiontitle?, (%section.level0.content;))>
<!ATTLIST top
          %sectionsattr;>
<!ELEMENT part          (sectiontitle?, (%section.level0.content;))>
<!ATTLIST part
          %sectionsattr;>

<!ELEMENT chapter       (sectiontitle?, (%section.level1.content;))>
<!ATTLIST chapter
          %sectionsattr;>
<!ELEMENT section       (sectiontitle?, (%section.level2.content;))>
<!ATTLIST section
          %sectionsattr;>
<!ELEMENT subsection    (sectiontitle?, (%section.level3.content;))>
<!ATTLIST subsection
          %sectionsattr;>
<!ELEMENT subsubsection (sectiontitle?, (%section.level4.content;))>
<!ATTLIST subsubsection
          %sectionsattr;>

<!ELEMENT unnumbered          (sectiontitle?, (%section.level1.content;))>
<!ATTLIST unnumbered
          %sectionsattr;>
<!ELEMENT unnumberedsec       (sectiontitle?, (%section.level2.content;))>
<!ATTLIST unnumberedsec
          %sectionsattr;>
<!ELEMENT unnumberedsubsec    (sectiontitle?, (%section.level3.content;))>
<!ATTLIST unnumberedsubsec
          %sectionsattr;>
<!ELEMENT unnumberedsubsubsec (sectiontitle?, (%section.level4.content;))>
<!ATTLIST unnumberedsubsubsec
          %sectionsattr;>
<!ELEMENT centerchap          (sectiontitle?, (%section.level1.content;))>
<!ATTLIST centerchap
          %sectionsattr;>

<!ELEMENT appendix          (sectiontitle?, (%section.level1.content;))>
<!ATTLIST appendix
          %sectionsattr;>
<!ELEMENT appendixsec       (sectiontitle?, (%section.level2.content;))>
<!ATTLIST appendixsec
          %sectionsattr;>
<!ELEMENT appendixsubsec    (sectiontitle?, (%section.level3.content;))>
<!ATTLIST appendixsubsec
          %sectionsattr;>
<!ELEMENT appendixsubsubsec (sectiontitle?, (%section.level4.content;))>
<!ATTLIST appendixsubsubsec
          %sectionsattr;>

<!-- Headings and titles -->
<!ELEMENT majorheading  (#PCDATA | %Inline.line;)*>
<!ATTLIST majorheading
          %spacesattr;>
<!ELEMENT chapheading   (#PCDATA | %Inline.line;)*>
<!ATTLIST chapheading
          %spacesattr;>
<!ELEMENT heading       (#PCDATA | %Inline.line;)*>
<!ATTLIST heading
          %spacesattr;>
<!ELEMENT subheading    (#PCDATA | %Inline.line;)*>
<!ATTLIST subheading
          %spacesattr;>
<!ELEMENT subsubheading (#PCDATA | %Inline.line;)*>
<!ATTLIST subsubheading
          %spacesattr;>
<!ELEMENT sectiontitle  (#PCDATA | %Inline.line;)*>


<!-- emacs-page
     Block elements -->

<!ELEMENT quotation    (quotationtype?, (%block; | exdent | author)*)>
<!ATTLIST quotation
          %spacesblockattr;>
<!ELEMENT smallquotation    (quotationtype?, (%block; | exdent | author)*)>
<!ATTLIST smallquotation
          %spacesblockattr;>
<!ELEMENT quotationtype (#PCDATA | %Inline.line;)*>

<!ELEMENT documentdescription (%block;)*>
<!ATTLIST documentdescription
          %spacesblockattr;>
<!ELEMENT example      (examplelanguage?, examplearg*, (%block; | exdent)*)>
<!ATTLIST example
          %spacesblockattr;>
<!ELEMENT examplelanguage (#PCDATA | %Inline.line;)*>
<!ATTLIST examplelanguage
          %trailingspacesattr;>
<!ELEMENT examplearg (#PCDATA | %Inline.line;)*>
<!ATTLIST examplearg
          %spacesattr;>

<!ELEMENT smallexample (%block; | exdent)*>
<!ATTLIST smallexample
          %spacesblockattr;>
<!ELEMENT lisp         (%block; | exdent)*>
<!ATTLIST lisp
          %spacesblockattr;>
<!ELEMENT smalllisp    (%block; | exdent)*>
<!ATTLIST smalllisp
          %spacesblockattr;>
<!ELEMENT cartouche    (cartouchetitle?, (%block; | exdent))*>
<!ATTLIST cartouche
          %spacesblockattr;>
<!ELEMENT cartouchetitle (#PCDATA | %Inline.line;)*>
<!ELEMENT format       (%block; | exdent)*>
<!ATTLIST format
          %spacesblockattr;>
<!ELEMENT smallformat  (%block; | exdent)*>
<!ATTLIST smallformat
          %spacesblockattr;>
<!ELEMENT display      (%block; | exdent)*>
<!ATTLIST display
          %spacesblockattr;>
<!ELEMENT smalldisplay (%block; | exdent)*>
<!ATTLIST smalldisplay
          %spacesblockattr;>
<!ELEMENT indentedblock    (%block; | exdent)*>
<!ATTLIST indentedblock
          %spacesblockattr;>
<!ELEMENT smallindentedblock    (%block; | exdent)*>
<!ATTLIST smallindentedblock
          %spacesblockattr;>
<!ELEMENT group        (%block; | exdent)*>
<!ATTLIST group
          %spacesblockattr;>
<!ELEMENT flushleft    (%block; | exdent)*>
<!ATTLIST flushleft
          %spacesblockattr;>
<!ELEMENT flushright   (%block; | exdent)*>
<!ATTLIST flushright
          %spacesblockattr;>
<!ELEMENT raggedright  (%block; | exdent)*>
<!ATTLIST raggedright
          %spacesblockattr;>
<!ELEMENT displaymath  (#PCDATA | %Inline.phrase;)*>
<!ATTLIST displaymath
          %spacesblockattr;>
<!ELEMENT nodedescriptionblock (%block;)*>
<!ATTLIST nodedescriptionblock
          %spacesblockattr;>

<!ELEMENT center       (#PCDATA | %Inline.fullline;)*>
<!ATTLIST center
          %spacesattr;>

<!ELEMENT nodedescription  (#PCDATA | %Inline.fullline;)*>
<!ATTLIST nodedescription
          %spacesattr;>

<!ELEMENT image        (imagefile, imagewidth?, imageheight?, alttext?,
                        imageextension?)>
<!ATTLIST image
          %spacesattr;
          where      CDATA #IMPLIED>
<!ELEMENT imagefile       (#PCDATA | %Inline.line;)*>
<!ATTLIST imagefile
          %spacesattr;>
<!ELEMENT imagewidth      (#PCDATA)>
<!ATTLIST imagewidth
          %spacesattr;>
<!ELEMENT imageheight     (#PCDATA)>
<!ATTLIST imageheight
          %spacesattr;>
<!ELEMENT alttext         (#PCDATA | %Inline.line;)*>
<!ATTLIST alttext
          %spacesattr;>
<!ELEMENT imageextension  (#PCDATA)>
<!ATTLIST imageextension
          %spacesattr;>

<!-- Whitespace in these elements are always preserved -->
<!ELEMENT verbatim     (#PCDATA)>
<!ATTLIST verbatim
          %spacesblockattr;
          xml:space (preserve) #FIXED 'preserve'>

<!-- author is there because it may happen in a quotation para -->
<!ELEMENT para         (#PCDATA | %Inline.phrase; | author)*>
<!ATTLIST para
          role CDATA #IMPLIED>
<!ELEMENT pre          (#PCDATA | %Inline.phrase; | author)*>
<!ATTLIST pre          xml:space (preserve) #FIXED 'preserve'>

<!ELEMENT menu (menuentry | detailmenu | menucomment)*>
<!ATTLIST menu
          %spacesblockattr;>
<!ELEMENT detailmenu (menuentry | menucomment)*>
<!ATTLIST detailmenu
          %spacesblockattr;>
<!ELEMENT direntry (menuentry | menucomment)*>
<!ATTLIST direntry
          %spacesblockattr;>
<!ELEMENT menuentry (menuleadingtext,
                     ((menutitle, menuseparator, menunode, menuseparator?)
                     | (menunode, menuseparator)), menudescription?)>
<!ELEMENT menuleadingtext (#PCDATA | %Spaces.elements;)*>
<!ELEMENT menunode (#PCDATA | %Inline.line;)*>
<!ELEMENT menutitle (#PCDATA | %Inline.line;)*>
<!ELEMENT menuseparator (#PCDATA)>
<!ELEMENT menudescription (%block;)*>
<!ELEMENT menucomment (%block;)*>

<!-- Raw formats -->
<!ELEMENT docbook (%Raw.content;)*>
<!ATTLIST docbook
          %spacesblockattr;>
<!ELEMENT html (%Raw.content;)*>
<!ATTLIST html
          %spacesblockattr;>
<!ELEMENT tex (%Raw.content;)*>
<!ATTLIST tex
          %spacesblockattr;>
<!ELEMENT latex (%Raw.content;)*>
<!ATTLIST latex
          %spacesblockattr;>
<!-- this may happen if xml is not expanded -->
<!ELEMENT xml (%Raw.content;)*>
<!ATTLIST xml
          %spacesblockattr;>

<!-- formalarg only before PCDATA, but mandating it seems not possible -->
<!-- same issue for nesting macros -->
<!ELEMENT macro (#PCDATA | formalarg | macro | rmacro | linemacro)*>
<!ATTLIST macro
          name CDATA #REQUIRED
          line CDATA #REQUIRED
          endspaces CDATA #IMPLIED>
<!ELEMENT rmacro (#PCDATA | formalarg | macro | rmacro | linemacro)*>
<!ATTLIST rmacro
          name CDATA #REQUIRED
          line CDATA #REQUIRED
          endspaces CDATA #IMPLIED>
<!ELEMENT linemacro (#PCDATA | formalarg | macro | rmacro | linemacro)*>
<!ATTLIST linemacro
          name CDATA #REQUIRED
          line CDATA #REQUIRED
          endspaces CDATA #IMPLIED>

<!ELEMENT formalarg (#PCDATA)>

<!ELEMENT ignore (#PCDATA)>
<!ATTLIST ignore
          %spacesblockattr;>

<!-- Negative indentation in blocks -->
<!ELEMENT exdent       (#PCDATA | %Inline.fullline;)*>
<!ATTLIST exdent
          %spacesattr;>

<!ELEMENT verbatiminclude (#PCDATA)>
<!ATTLIST verbatiminclude
          %spacesattr;
          file CDATA #REQUIRED>

<!ELEMENT include (#PCDATA)>
<!ATTLIST include
          %spacesattr;>

<!-- Floating displays -->
<!ELEMENT float (floattype?, floatname?, (%block; | caption | shortcaption)*)>
<!ATTLIST float
          %spacesblockattr;
          number CDATA #IMPLIED
          name CDATA #IMPLIED
          type CDATA #IMPLIED>
<!ELEMENT floattype (#PCDATA | %Inline.line;)*>
<!ATTLIST floattype
          %trailingspacesattr;>
<!ELEMENT floatname (#PCDATA | %Inline.line;)*>
<!ATTLIST floatname
          %spacesattr;>
<!ELEMENT caption (#PCDATA | %block;)*>
<!ATTLIST caption
          %spacesafterincmdattr;>
<!ELEMENT shortcaption (#PCDATA | %Inline.phrase; | para)*>
<!ATTLIST shortcaption
          %spacesafterincmdattr;>
<!ELEMENT listoffloats (#PCDATA | %Inline.line;)*>
<!ATTLIST listoffloats
          %spacesattr;
          type CDATA #IMPLIED>

<!-- Lists -->
<!ELEMENT itemize (itemprepend?, beforefirstitem?,
                   (listitem | %indexentry.cmds;)*)>
<!ATTLIST itemize
          %spacesblockattr;
          automaticcommandarg CDATA #IMPLIED
          commandarg CDATA #IMPLIED>
<!ELEMENT enumerate (enumeratefirst?, beforefirstitem?,
                     (listitem | %indexentry.cmds;)*)>
<!ATTLIST enumerate
          %spacesblockattr;
          first CDATA #IMPLIED>
<!ELEMENT enumeratefirst (#PCDATA)>

<!ELEMENT listitem (prepend?, (%block;)*)>
<!ATTLIST listitem
          %spacesattr;>
<!ELEMENT prepend (#PCDATA | %Inline.line;)*>

<!ELEMENT itemprepend (#PCDATA | %Inline.line; | formattingcommand)*>
<!ELEMENT formattingcommand EMPTY>
<!ATTLIST formattingcommand
          command CDATA #REQUIRED
          automatic (%onoff;) 'off'>
<!ELEMENT beforefirstitem (%block;)*>

<!-- Tables -->
<!ELEMENT table (beforefirstitem?, (tableentry | %indexentry.cmds;)+)>

<!ENTITY % blocktablearg
         "%spacesblockattr;
          automaticcommandarg CDATA #IMPLIED
          commandarg CDATA #IMPLIED
          begin CDATA #IMPLIED
          end CDATA #IMPLIED">

<!-- begin and end are for definfoenclose command as table argument -->
<!ATTLIST table
          %blocktablearg;>
<!ELEMENT vtable (beforefirstitem?, (tableentry | %indexentry.cmds;)+)>
<!ATTLIST vtable
          %blocktablearg;>
<!ELEMENT ftable (beforefirstitem?, (tableentry | %indexentry.cmds;)+)>
<!ATTLIST ftable
          %blocktablearg;>
<!ELEMENT tableentry (tableterm, tableitem?)>
<!-- tableterm contains directly pre if there is an index entry before @itemx -->
<!ELEMENT tableterm ((%indexentry.cmds;)*, item,  (pre | itemx | %indexentry.cmds;)*)>
<!ELEMENT item (#PCDATA | %Inline.line; | itemformat | indexterm)*>
<!ATTLIST item
          %spacesattr;>
<!ELEMENT itemx (#PCDATA | %Inline.line; | itemformat | indexterm)*>
<!ATTLIST itemx
          %spacesattr;>
<!ELEMENT itemformat (#PCDATA | %Inline.line; | indexterm)*>
<!-- begin and end are for definfoenclose command for item content -->
<!ATTLIST itemformat
          command   CDATA #REQUIRED
          begin CDATA #IMPLIED
          end CDATA #IMPLIED>
<!ELEMENT tableitem (%block;)*>

<!ELEMENT multitable ((columnfractions | columnprototypes), beforefirstitem?,
                      thead?, tbody)>
<!ATTLIST multitable
          %blocktablearg;>
<!ELEMENT columnfractions (columnfraction+)>
<!ATTLIST columnfractions
          %linespaceattr;>
<!ELEMENT columnprototypes (columnprototype+)>
<!ELEMENT columnfraction EMPTY>
<!ATTLIST columnfraction
          value CDATA #REQUIRED>
<!ELEMENT columnprototype (#PCDATA | %Inline.line;)*>
<!ATTLIST columnprototype
          %spacesattr;
          %bracketedattr;>
<!ELEMENT thead (row+)>
<!ELEMENT tbody (row+)>
<!ELEMENT row (entry*)>
<!ELEMENT entry (%block;)*>
<!ATTLIST entry
          %spacesattr;
          command   CDATA #REQUIRED>

<!-- API definitions -->

<!-- def* contains directly pre if there is an index entry before def*x -->
<!ELEMENT deffn (definitionterm, (pre | deffnx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deffn
          %spacesblockattr;>
<!ELEMENT deffnx (definitionterm)>
<!ATTLIST deffnx
          %spacesattr;>
<!ELEMENT defvr (definitionterm, (pre | defvrx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defvr
          %spacesblockattr;>
<!ELEMENT defvrx (definitionterm)>
<!ATTLIST defvrx
          %spacesattr;>
<!ELEMENT deftypefn (definitionterm, (pre | deftypefnx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deftypefn
          %spacesblockattr;>
<!ELEMENT deftypefnx (definitionterm)>
<!ATTLIST deftypefnx
          %spacesattr;>
<!ELEMENT deftypeop (definitionterm, (pre | deftypeopx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deftypeop
          %spacesblockattr;>
<!ELEMENT deftypeopx (definitionterm)>
<!ATTLIST deftypeopx
          %spacesattr;>
<!ELEMENT deftypevr (definitionterm, (pre | deftypevrx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deftypevr
          %spacesblockattr;>
<!ELEMENT deftypevrx (definitionterm)>
<!ATTLIST deftypevrx
          %spacesattr;>
<!ELEMENT defcv (definitionterm, (pre | defcvx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defcv
          %spacesblockattr;>
<!ELEMENT defcvx (definitionterm)>
<!ATTLIST defcvx
          %spacesattr;>
<!ELEMENT deftypecv (definitionterm, (pre | deftypecvx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deftypecv
          %spacesblockattr;>
<!ELEMENT deftypecvx (definitionterm)>
<!ATTLIST deftypecvx
          %spacesattr;>
<!ELEMENT defop (definitionterm, (pre | defopx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defop
          %spacesblockattr;>
<!ELEMENT defopx (definitionterm)>
<!ATTLIST defopx
          %spacesattr;>
<!ELEMENT deftp (definitionterm, (pre | deftpx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deftp
          %spacesblockattr;>
<!ELEMENT deftpx (definitionterm)>
<!ATTLIST deftpx
          %spacesattr;>
<!ELEMENT defun (definitionterm, (pre | defunx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defun
          %spacesblockattr;>
<!ELEMENT defunx (definitionterm)>
<!ATTLIST defunx
          %spacesattr;>
<!ELEMENT defmac (definitionterm, (pre | defmacx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defmac
          %spacesblockattr;>
<!ELEMENT defmacx (definitionterm)>
<!ATTLIST defmacx
          %spacesattr;>
<!ELEMENT defspec (definitionterm, (pre | defspecx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defspec
          %spacesblockattr;>
<!ELEMENT defspecx (definitionterm)>
<!ATTLIST defspecx
          %spacesattr;>
<!ELEMENT defvar (definitionterm, (pre | defvarx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defvar
          %spacesblockattr;>
<!ELEMENT defvarx (definitionterm)>
<!ATTLIST defvarx
          %spacesattr;>
<!ELEMENT defopt (definitionterm, (pre | defoptx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defopt
          %spacesblockattr;>
<!ELEMENT defoptx (definitionterm)>
<!ATTLIST defoptx
          %spacesattr;>
<!ELEMENT deftypefun (definitionterm, (pre | deftypefunx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deftypefun
          %spacesblockattr;>
<!ELEMENT deftypefunx (definitionterm)>
<!ATTLIST deftypefunx
          %spacesattr;>
<!ELEMENT deftypevar (definitionterm, (pre | deftypevarx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deftypevar
          %spacesblockattr;>
<!ELEMENT deftypevarx (definitionterm)>
<!ATTLIST deftypevarx
          %spacesattr;>
<!ELEMENT defivar (definitionterm, (pre | defivarx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defivar
          %spacesblockattr;>
<!ELEMENT defivarx (definitionterm)>
<!ATTLIST defivarx
          %spacesattr;>
<!ELEMENT deftypeivar (definitionterm, (pre | deftypeivarx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deftypeivar
          %spacesblockattr;>
<!ELEMENT deftypeivarx (definitionterm)>
<!ATTLIST deftypeivarx
          %spacesattr;>
<!ELEMENT defmethod (definitionterm, (pre | defmethodx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST defmethod
          %spacesblockattr;>
<!ELEMENT defmethodx (definitionterm)>
<!ATTLIST defmethodx
          %spacesattr;>
<!ELEMENT deftypemethod (definitionterm, (pre | deftypemethodx | %indexentry.cmds;)*, definitionitem?)>
<!ATTLIST deftypemethod
          %spacesblockattr;>
<!ELEMENT deftypemethodx (definitionterm)>
<!ATTLIST deftypemethodx
          %spacesattr;>

<!ELEMENT defblock (beforefirstdefline?, ((defline | deftypeline), (%indexentry.cmds;)*, definitionitem?)+)>
<!ATTLIST defblock
          endspaces CDATA #IMPLIED>
<!ELEMENT defline (definitionterm)>
<!ATTLIST defline
          %spacesattr;>
<!ELEMENT deftypeline (definitionterm)>
<!ATTLIST deftypeline
          %spacesattr;>

<!ELEMENT definitionterm (indexterm?, (%definition.args;)+)>
<!ELEMENT definitionitem (%block;)*>
<!ELEMENT beforefirstdefline (%block;)*>

<!ENTITY % defargattr
         "%bracketedattr; %spacestrailingspacesattr;">

<!ELEMENT defbracketed (#PCDATA | %Inline.line;)*>
<!ATTLIST defbracketed
          %defargattr;>

<!ELEMENT defcategory  (#PCDATA | %Inline.defargline;)*>
<!ATTLIST defcategory
          %defargattr;
          automatic (%onoff;) 'off'>
<!ELEMENT deffunction  (#PCDATA | %Inline.defargline;)*>
<!ATTLIST deffunction
          %defargattr;>
<!ELEMENT defsymbol (#PCDATA | %Inline.defargline;)*>
<!ATTLIST defsymbol
          %defargattr;>
<!ELEMENT defvariable  (#PCDATA | %Inline.defargline;)*>
<!ATTLIST defvariable
          %defargattr;>
<!ELEMENT defparam     (#PCDATA | %Inline.defargline;)*>
<!ATTLIST defparam
          %defargattr;>
<!ELEMENT defdelimiter (#PCDATA | %Inline.line;)*>
<!ELEMENT deftype      (#PCDATA | %Inline.defargline;)*>
<!ATTLIST deftype
          %defargattr;>
<!ELEMENT defparamtype (#PCDATA | %Inline.defargline;)*>
<!ATTLIST defparamtype
          %defargattr;>
<!ELEMENT defdatatype  (#PCDATA | %Inline.defargline;)*>
<!ATTLIST defdatatype
          %defargattr;>
<!ELEMENT defclass     (#PCDATA | %Inline.defargline;)*>
<!ATTLIST defclass
          %defargattr;>
<!ELEMENT defclassvar  (#PCDATA | %Inline.defargline;)*>
<!ATTLIST defclassvar
          %defargattr;>
<!ELEMENT defoperation (#PCDATA | %Inline.defargline;)*>
<!ATTLIST defoperation
          %defargattr;>

<!-- emacs-page
     Inline elements -->

<!ELEMENT hyphenation (#PCDATA)>
<!ATTLIST hyphenation
          %spacesafterincmdattr;>

<!-- emphasize -->
<!ELEMENT strong (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST strong
          %spacesaftercmdattr;>
<!ELEMENT emph   (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST emph
          %spacesaftercmdattr;>

<!-- small caps -->
<!ELEMENT sc (#PCDATA | %Inline.phraseormark;)*>

<!-- fonts -->
<!ELEMENT b  (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST b
          %spacesaftercmdattr;>
<!ELEMENT i  (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST i
          %spacesaftercmdattr;>
<!ELEMENT r  (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST r
          %spacesaftercmdattr;>
<!ELEMENT sansserif   (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST sansserif
          %spacesaftercmdattr;>
<!ELEMENT slanted     (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST slanted
          %spacesaftercmdattr;>
<!ELEMENT titlefont   (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST titlefont
          %spacesaftercmdattr;>
<!ELEMENT t (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST t
          %spacesaftercmdattr;>

<!-- markup -->
<!ELEMENT code    (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST code
          %spacesaftercmdattr;>
<!ELEMENT command (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST command
          %spacesaftercmdattr;>
<!ELEMENT env     (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST env
          %spacesaftercmdattr;>
<!ELEMENT file    (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST file
          %spacesaftercmdattr;>
<!ELEMENT option  (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST option
          %spacesaftercmdattr;>
<!ELEMENT samp    (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST samp
          %spacesaftercmdattr;>
<!ELEMENT dfn     (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST dfn
          %spacesaftercmdattr;>
<!ELEMENT cite    (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST cite
          %spacesaftercmdattr;>
<!ELEMENT key     (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST key
          %spacesaftercmdattr;>
<!ELEMENT kbd     (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST kbd
          %spacesaftercmdattr;>
<!ELEMENT var     (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST var
          %spacesaftercmdattr;>
<!ELEMENT indicateurl   (#PCDATA | %Inline.phrase;)*>
<!ATTLIST indicateurl
          %spacesaftercmdattr;>
<!ELEMENT clicksequence (#PCDATA | %Inline.phrase;)*>
<!ATTLIST clicksequence
          %spacesaftercmdattr;>
<!ELEMENT w       (#PCDATA | %Inline.phrase;)*>
<!ATTLIST w
          %spacesaftercmdattr;>
<!ELEMENT asis    (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST asis
          %spacesaftercmdattr;>
<!ELEMENT sub     (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST sub
          %spacesaftercmdattr;>
<!ELEMENT sup     (#PCDATA | %Inline.phraseormark;)*>
<!ATTLIST sup
          %spacesaftercmdattr;>
<!ELEMENT verb    (#PCDATA)>
<!ATTLIST verb
          %spacesaftercmdattr;
          delimiter CDATA #REQUIRED>

<!ELEMENT acronym (acronymword, acronymdesc?)>
<!ATTLIST acronym
          %spacesafterincmdattr;>
<!ELEMENT acronymword (#PCDATA | %Inline.phrase;)*>
<!ELEMENT acronymdesc (#PCDATA | %Inline.phrase;)*>
<!ATTLIST acronymdesc
          %spacesattr;>

<!ELEMENT abbr (abbrword, abbrdesc?)>
<!ATTLIST abbr
          %spacesafterincmdattr;>
<!ELEMENT abbrword (#PCDATA | %Inline.phrase;)*>
<!ELEMENT abbrdesc (#PCDATA | %Inline.phrase;)*>
<!ATTLIST abbrdesc
          %spacesattr;>

<!-- math -->
<!ELEMENT math    (#PCDATA | %Inline.phrase;)*>
<!ATTLIST math
          %spacesafterincmdattr;>
<!ELEMENT dmn     (#PCDATA | %Inline.phrase;)*>

<!-- reference -->
<!ELEMENT anchor (#PCDATA | %Inline.phrase;)*>
<!ATTLIST anchor
          %spacesafterincmdattr;
          name CDATA #IMPLIED>

<!ELEMENT errormsg (#PCDATA | %Inline.phrase;)*>
<!ATTLIST errormsg
          %spacesafterincmdattr;>

<!-- inline conditionals -->
<!ELEMENT inlineraw (inlinerawformat, inlinerawcontent?)>
<!ATTLIST inlineraw
          %spacesaftercmdattr;>
<!ELEMENT inlinerawformat (#PCDATA)>
<!ATTLIST inlinerawformat
          %spacesattr;>
<!ELEMENT inlinerawcontent (#PCDATA | %Inline.phrase;)*>
<!ATTLIST inlinerawcontent
          %spacesattr;>

<!ELEMENT inlinefmt (inlinefmtformat, inlinefmtcontent?)>
<!ATTLIST inlinefmt
          %spacesaftercmdattr;>
<!ELEMENT inlinefmtformat (#PCDATA)>
<!ATTLIST inlinefmtformat
          %spacesattr;>
<!ELEMENT inlinefmtcontent (#PCDATA | %Inline.phrase;)*>
<!ATTLIST inlinefmtcontent
          %spacesattr;>

<!ELEMENT inlineifclear (inlineifclearformat, inlineifclearcontent?)>
<!ATTLIST inlineifclear
          %spacesaftercmdattr;>
<!ELEMENT inlineifclearformat (#PCDATA)>
<!ATTLIST inlineifclearformat
          %spacesattr;>
<!ELEMENT inlineifclearcontent (#PCDATA | %Inline.phrase;)*>
<!ATTLIST inlineifclearcontent
          %spacesattr;>

<!ELEMENT inlineifset (inlineifsetformat, inlineifsetcontent?)>
<!ATTLIST inlineifset
          %spacesaftercmdattr;>
<!ELEMENT inlineifsetformat (#PCDATA)>
<!ATTLIST inlineifsetformat
          %spacesattr;>
<!ELEMENT inlineifsetcontent (#PCDATA | %Inline.phrase;)*>
<!ATTLIST inlineifsetcontent
          %spacesattr;>

<!ELEMENT inlinefmtifelse (inlinefmtifelseformat, inlinefmtifelsecontentif?, inlinefmtifelsecontentelse?)>
<!ATTLIST inlinefmtifelse
          %spacesaftercmdattr;>
<!ELEMENT inlinefmtifelseformat (#PCDATA)>
<!ATTLIST inlinefmtifelseformat
          %spacesattr;>
<!ELEMENT inlinefmtifelsecontentif (#PCDATA | %Inline.phrase;)*>
<!ATTLIST inlinefmtifelsecontentif
          %spacesattr;>
<!ELEMENT inlinefmtifelsecontentelse (#PCDATA | %Inline.phrase;)*>
<!ATTLIST inlinefmtifelsecontentelse
          %spacesattr;>


<!-- command defined by definfoenclose -->
<!ELEMENT infoenclose  (#PCDATA | %Inline.phrase;)*>
<!ATTLIST infoenclose
          %spacesaftercmdattr;
          command CDATA #REQUIRED
          begin CDATA #IMPLIED
          end CDATA #IMPLIED>

<!ENTITY % xref.args "xrefnodename?, xrefinfoname?, xrefprinteddesc?,
                xrefinfofile?, xrefprintedname?">
<!ENTITY % xref.attr
          "%spacesafterincmdattr;
           label CDATA #IMPLIED
           manual CDATA #IMPLIED">

<!ELEMENT xref (%xref.args;)>
<!ATTLIST xref
          %xref.attr;>
<!ELEMENT ref (%xref.args;)>
<!ATTLIST ref
          %xref.attr;>
<!ELEMENT pxref (%xref.args;)>
<!ATTLIST pxref
          %xref.attr;>

<!ELEMENT xrefnodename    (#PCDATA | %Inline.phrase;)*>
<!ATTLIST xrefnodename
          %spacesattr;>
<!ELEMENT xrefinfoname    (#PCDATA | %Inline.phrase;)*>
<!ATTLIST xrefinfoname
          %spacesattr;>
<!ELEMENT xrefinfofile    (#PCDATA | %Inline.phrase;)*>
<!ATTLIST xrefinfofile
          %spacesattr;>
<!ELEMENT xrefprintedname (#PCDATA | %Inline.phrase;)*>
<!ATTLIST xrefprintedname
          %spacesattr;>
<!ELEMENT xrefprinteddesc (#PCDATA | %Inline.phrase;)*>
<!ATTLIST xrefprinteddesc
          %spacesattr;>

<!ELEMENT link (linknodename?, linkrefname?, linkinfofile?)>
<!ATTLIST link
          %xref.attr;>
<!ELEMENT linknodename (#PCDATA | %Inline.phrase;)*>
<!ATTLIST linknodename
          %spacesattr;>
<!ELEMENT linkrefname  (#PCDATA | %Inline.phrase;)*>
<!ATTLIST linkrefname
          %spacesattr;>
<!ELEMENT linkinfofile (#PCDATA | %Inline.phrase;)*>
<!ATTLIST linkinfofile
          %spacesattr;>

<!ELEMENT inforef (inforefnodename?, inforefrefname?, inforefinfoname?)>
<!ATTLIST inforef
          %xref.attr;>
<!ELEMENT inforefnodename (#PCDATA | %Inline.phrase;)*>
<!ATTLIST inforefnodename
          %spacesattr;>
<!ELEMENT inforefrefname  (#PCDATA | %Inline.phrase;)*>
<!ATTLIST inforefrefname
          %spacesattr;>
<!ELEMENT inforefinfoname (#PCDATA | %Inline.phrase;)*>
<!ATTLIST inforefinfoname
          %spacesattr;>

<!ELEMENT email (emailaddress, emailname?)>
<!ATTLIST email
          %spacesaftercmdattr;>
<!ELEMENT emailaddress (#PCDATA | %Inline.phrase;)*>
<!ATTLIST emailaddress
          %spacesattr;>
<!ELEMENT emailname (#PCDATA | %Inline.phrase;)*>
<!ATTLIST emailname
          %spacesattr;>

<!ELEMENT uref (urefurl, urefdesc?, urefreplacement?)>
<!ATTLIST uref
          %spacesafterincmdattr;>
<!ELEMENT url  (urefurl, urefdesc?, urefreplacement?)>
<!ATTLIST url
          %spacesafterincmdattr;>
<!ELEMENT urefurl         (#PCDATA | %Inline.phrase;)*>
<!ATTLIST urefurl
          %spacesattr;>
<!ELEMENT urefdesc        (#PCDATA | %Inline.phrase;)*>
<!ATTLIST urefdesc
          %spacesattr;>
<!ELEMENT urefreplacement (#PCDATA | %Inline.phrase;)*>

<!ELEMENT footnote (%block;)*>
<!ATTLIST footnote
          %spacesafterincmdattr;>

<!-- deprecated -->

<!ELEMENT ctrl             (#PCDATA | %Inline.phrase;)*>
<!ELEMENT refill           EMPTY>
<!ELEMENT quote-arg        EMPTY>
<!ELEMENT allow-recursion  EMPTY>

<!-- index commands -->

<!ENTITY % indexcmdattr
         "%spacesattr;
          index CDATA #IMPLIED">

<!ELEMENT defindex  EMPTY>
<!ATTLIST defindex
          %linespaceattr;
          value     NMTOKEN #REQUIRED>
<!ELEMENT defcodeindex  EMPTY>
<!ATTLIST defcodeindex
          %linespaceattr;
          value     NMTOKEN #REQUIRED>
<!ELEMENT synindex  EMPTY>
<!ATTLIST synindex
          %linespaceattr;
          from      NMTOKEN #REQUIRED
          to        NMTOKEN #REQUIRED>
<!ELEMENT syncodeindex  EMPTY>
<!ATTLIST syncodeindex
          %linespaceattr;
          from      NMTOKEN #REQUIRED
          to        NMTOKEN #REQUIRED>
<!ELEMENT indexterm (#PCDATA | %Inline.indexentryline;)*>
<!ATTLIST indexterm
          index         CDATA #IMPLIED
          mergedindex   CDATA #IMPLIED
          number        CDATA #REQUIRED
          incode    (%zeroone;) #IMPLIED>
<!ELEMENT indexcommand (indexterm)>
<!ATTLIST indexcommand
          %indexcmdattr;
          command   CDATA #REQUIRED>
<!ELEMENT cindex (indexterm)>
<!ATTLIST cindex
          %indexcmdattr;>
<!ELEMENT cpindex (indexterm)>
<!ATTLIST cpindex
          %indexcmdattr;>
<!ELEMENT findex (indexterm)>
<!ATTLIST findex
          %indexcmdattr;>
<!ELEMENT fnindex (indexterm)>
<!ATTLIST fnindex
          %indexcmdattr;>
<!ELEMENT kindex (indexterm)>
<!ATTLIST kindex
          %indexcmdattr;>
<!ELEMENT kyindex (indexterm)>
<!ATTLIST kyindex
          %indexcmdattr;>
<!ELEMENT pindex (indexterm)>
<!ATTLIST pindex
          %indexcmdattr;>
<!ELEMENT pgindex (indexterm)>
<!ATTLIST pgindex
          %indexcmdattr;>
<!ELEMENT tindex (indexterm)>
<!ATTLIST tindex
          %indexcmdattr;>
<!ELEMENT tpindex (indexterm)>
<!ATTLIST tpindex
          %indexcmdattr;>
<!ELEMENT vindex (indexterm)>
<!ATTLIST vindex
          %indexcmdattr;>
<!ELEMENT vrindex (indexterm)>
<!ATTLIST vrindex
          %indexcmdattr;>
<!ELEMENT subentry (#PCDATA | %Inline.indexentryline;)*>
<!ATTLIST subentry
          %spacesattr;>
<!ELEMENT sortas (#PCDATA | %Inline.line;)*>
<!ELEMENT seeentry (#PCDATA | %Inline.line;)*>
<!ELEMENT seealso (#PCDATA | %Inline.line;)*>

<!ELEMENT printindex EMPTY>
<!ATTLIST printindex
          %linespaceattr;
          value     NMTOKEN #REQUIRED>

<!-- unusual insertions and other -->

<!ELEMENT accent (#PCDATA | accent | dotless)*>
<!ATTLIST accent
          %bracketedattr;
          %spacesaftercmdattr;
          type        CDATA #REQUIRED>

<!ELEMENT bye EMPTY>

<!ELEMENT click  EMPTY>
<!ATTLIST click
          command        CDATA #REQUIRED>

<!ELEMENT dotless (#PCDATA)>
<!ATTLIST dotless
          %spacesaftercmdattr;>

<!ELEMENT spacecmd EMPTY>
<!ATTLIST spacecmd
          type (%spacetype;) #IMPLIED>

<!ELEMENT today EMPTY>

<!ELEMENT U (#PCDATA)>
<!ATTLIST U
          %spacesattr;>


<!-- emacs-page
     Punctuation and special symbols.  -->

<!ELEMENT punct     (#PCDATA)>
<!ATTLIST punct
          end-of-sentence (yes|no) #IMPLIED>
<!ELEMENT logo      (#PCDATA)>
<!ELEMENT linebreak EMPTY>
<!ELEMENT noeos EMPTY>
<!ELEMENT formfeed EMPTY>
<!ELEMENT verticaltab EMPTY>
<!ELEMENT divideheading EMPTY>

<!ENTITY tex        "<logo>TeX</logo>">
<!ENTITY latex      "<logo>LaTeX</logo>">
<!ENTITY ellipsis   "&#x2026;">
<!-- From the XML specification:
     If the entities lt or amp are declared, they MUST be declared as internal entities whose replacement text is a character reference to the respective character (less-than sign or ampersand) being escaped; the double escaping is REQUIRED for these entities so that references to them produce a well-formed result.
     LibXML error: invalid redeclaration of predefined entity
<!ENTITY lt         "&#x3c;">
Use exactly what is on the XML specification
-->
<!ENTITY lt         "&#38;#60;">
<!ENTITY gt         "&#x3e;">
<!ENTITY bullet     "&#x2022;">
<!ENTITY copyright  "&#xa9;">
<!ENTITY registered "&#xae;">
<!ENTITY euro       "&#x20ac;">
<!ENTITY pounds     "&#xa3;">
<!ENTITY minus      "&#x2212;">
<!ENTITY linebreak  "<linebreak/>">
<!ENTITY dots       "<punct end-of-sentence='no'>&#x2026;</punct>">
<!ENTITY enddots    "<punct end-of-sentence='yes'>&#x2026;</punct>">
<!-- From the XML specification:
     If the entities lt or amp are declared, they MUST be declared as internal entities whose replacement text is a character reference to the respective character (less-than sign or ampersand) being escaped; the double escaping is REQUIRED for these entities so that references to them produce a well-formed result.
     LibXML error: invalid redeclaration of predefined entity
<!ENTITY amp        "&#x26;">
Use exactly what is on the XML specification
<!ENTITY amp        "&#38;#38;">
-->
<!ENTITY ampsymbol  "&amp;"> <!-- same expansion as ampchar -->
<!ENTITY lsquo      "&#x2018;">
<!ENTITY textlsquo      "&#x2018;">
<!ENTITY rsquo      "&#x2019;">
<!ENTITY textrsquo      "&#x2019;">
<!ENTITY sbquo      "&#x201a;">
<!ENTITY ldquo      "&#x201c;">
<!ENTITY textldquo      "&#x201c;">
<!ENTITY rdquo      "&#x201d;">
<!ENTITY textrdquo      "&#x201d;">
<!ENTITY bdquo      "&#x201e;">
<!ENTITY laquo      "&#xab;">
<!ENTITY raquo      "&#xbb;">
<!ENTITY guillemotleft   "&#xab;">
<!ENTITY guillemotright  "&#xbb;">
<!ENTITY lsaquo     "&#x2039;">
<!ENTITY rsaquo     "&#x203a;">
<!ENTITY textmdash      "&#x2014;">
<!ENTITY textndash      "&#x2013;">
<!ENTITY formfeed       "<formfeed/>">
<!ENTITY attrformfeed   "\f">
<!ENTITY verticaltab       "<verticaltab/>">
<!ENTITY attrverticaltab   "\v">
<!ENTITY period     "<punct end-of-sentence='no'>.</punct>">
<!ENTITY eosperiod  "<punct end-of-sentence='yes'>.</punct>">
<!ENTITY quest      "<punct end-of-sentence='no'>?</punct>">
<!ENTITY eosquest   "<punct end-of-sentence='yes'>?</punct>">
<!ENTITY excl       "<punct end-of-sentence='no'>!</punct>">
<!ENTITY eosexcl    "<punct end-of-sentence='yes'>!</punct>">
<!ENTITY hyphenbreak "&#x00ad;">
<!ENTITY slashbreak "/">
<!ENTITY noeos      "<noeos/>">
<!ENTITY arobase    "@">
<!ENTITY lbrace     "{">
<!ENTITY rbrace     "}">
<!ENTITY comma      ",">
<!ENTITY atchar     "@">
<!ENTITY ampchar    "&amp;">
<!ENTITY lbracechar "{">
<!ENTITY rbracechar "}">
<!ENTITY backslashchar "\\">
<!ENTITY hashchar   "#">
<!ENTITY nbsp       "&#xa0;">
<!ENTITY deg        "&#xb0;">
<!ENTITY expansion  "&#x2192;">
<!ENTITY point      "&#x2605;">
<!ENTITY printglyph "&#x22a3;">
<!ENTITY errorglyph "error--&gt;">
<!ENTITY result     "&#x21d2;">
<!ENTITY le         "&#x2264;">
<!ENTITY ge         "&#x2265;">
<!ENTITY equiv      "&#x2261;">

<!ENTITY auml "&#xe4;">
<!ENTITY ouml "&#xf6;">
<!ENTITY uuml "&#xfc;">
<!ENTITY Auml "&#xc4;">
<!ENTITY Ouml "&#xd6;">
<!ENTITY Uuml "&#xdc;">
<!ENTITY Euml "&#xcb;">
<!ENTITY euml "&#xeb;">
<!ENTITY Iuml "&#xcf;">
<!ENTITY iuml "&#xef;">
<!ENTITY yuml "&#xff;">
<!ENTITY uml  "&#xa8;">

<!ENTITY Aacute "&#xc1;">
<!ENTITY Eacute "&#xc9;">
<!ENTITY Iacute "&#xcd;">
<!ENTITY Oacute "&#xd3;">
<!ENTITY Uacute "&#xda;">
<!ENTITY Yacute "&#xdd;">
<!ENTITY aacute "&#xe1;">
<!ENTITY eacute "&#xe9;">
<!ENTITY iacute "&#xed;">
<!ENTITY oacute "&#xf3;">
<!ENTITY uacute "&#xfa;">
<!ENTITY yacute "&#xfd;">

<!ENTITY ccedil "&#xe7;">
<!ENTITY Ccedil "&#xc7;">

<!ENTITY Acirc "&#xc2;">
<!ENTITY Ecirc "&#xca;">
<!ENTITY Icirc "&#xc3;">
<!ENTITY Ocirc "&#xd4;">
<!ENTITY Ucirc "&#xdb;">
<!ENTITY acirc "&#xe2;">
<!ENTITY ecirc "&#xea;">
<!ENTITY icirc "&#xee;">
<!ENTITY ocirc "&#xf4;">
<!ENTITY ucirc "&#xfb;">

<!ENTITY Agrave "&#xc0;">
<!ENTITY Egrave "&#xc8;">
<!ENTITY Igrave "&#xcc;">
<!ENTITY Ograve "&#xd2;">
<!ENTITY Ugrave "&#xd9;">
<!ENTITY agrave "&#xe0;">
<!ENTITY egrave "&#xe8;">
<!ENTITY igrave "&#xec;">
<!ENTITY ograve "&#xf2;">
<!ENTITY ugrave "&#xf9;">

<!ENTITY Atilde "&#xc3;">
<!ENTITY Ntilde "&#xd1;">
<!ENTITY Otilde "&#xd5;">
<!ENTITY atilde "&#xe3;">
<!ENTITY ntilde "&#xf1;">
<!ENTITY otilde "&#xf5;">

<!ENTITY oslash "&#xf8;">
<!ENTITY Oslash "&#xd8;">

<!ENTITY lslash "&#x0142;">
<!ENTITY Lslash "&#x0141;">

<!ENTITY THORN  "&#xde;">
<!ENTITY thorn  "&#xfe;">

<!ENTITY eth "&#xf0;">
<!ENTITY ETH "&#xd0;">

<!ENTITY ordm "&#xba;">
<!ENTITY ordf "&#xaa;">

<!ENTITY iexcl "&#xa1;">
<!ENTITY pound "&#xa3;">
<!ENTITY iquest "&#xbf;">
<!ENTITY AElig "&#xc6;">
<!ENTITY aelig "&#xe6;">
<!ENTITY OElig "&#x152;">
<!ENTITY oelig "&#x153;">
<!ENTITY Aring "&#xc5;">
<!ENTITY aring "&#xe5;">
<!ENTITY szlig "&#xdf;">

<!ENTITY rarr "&#x2192;">
<!ENTITY rArr "&#x21d2;">

<!ENTITY macr "&#xaf;">

<!ENTITY backslash "\">
