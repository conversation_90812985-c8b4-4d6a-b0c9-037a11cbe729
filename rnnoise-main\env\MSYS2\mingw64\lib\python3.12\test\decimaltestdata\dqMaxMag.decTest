------------------------------------------------------------------------
-- dqMaxMag.decTest -- decQuad maxnummag                              --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- we assume that base comparison is tested in compare.decTest, so
-- these mainly cover special cases and rounding
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- sanity checks
dqmxg001 maxmag  -2  -2  -> -2
dqmxg002 maxmag  -2  -1  -> -2
dqmxg003 maxmag  -2   0  -> -2
dqmxg004 maxmag  -2   1  -> -2
dqmxg005 maxmag  -2   2  ->  2
dqmxg006 maxmag  -1  -2  -> -2
dqmxg007 maxmag  -1  -1  -> -1
dqmxg008 maxmag  -1   0  -> -1
dqmxg009 maxmag  -1   1  ->  1
dqmxg010 maxmag  -1   2  ->  2
dqmxg011 maxmag   0  -2  -> -2
dqmxg012 maxmag   0  -1  -> -1
dqmxg013 maxmag   0   0  ->  0
dqmxg014 maxmag   0   1  ->  1
dqmxg015 maxmag   0   2  ->  2
dqmxg016 maxmag   1  -2  -> -2
dqmxg017 maxmag   1  -1  ->  1
dqmxg018 maxmag   1   0  ->  1
dqmxg019 maxmag   1   1  ->  1
dqmxg020 maxmag   1   2  ->  2
dqmxg021 maxmag   2  -2  ->  2
dqmxg022 maxmag   2  -1  ->  2
dqmxg023 maxmag   2   0  ->  2
dqmxg025 maxmag   2   1  ->  2
dqmxg026 maxmag   2   2  ->  2

-- extended zeros
dqmxg030 maxmag   0     0   ->  0
dqmxg031 maxmag   0    -0   ->  0
dqmxg032 maxmag   0    -0.0 ->  0
dqmxg033 maxmag   0     0.0 ->  0
dqmxg034 maxmag  -0     0   ->  0    -- note: -0 = 0, but 0 chosen
dqmxg035 maxmag  -0    -0   -> -0
dqmxg036 maxmag  -0    -0.0 -> -0.0
dqmxg037 maxmag  -0     0.0 ->  0.0
dqmxg038 maxmag   0.0   0   ->  0
dqmxg039 maxmag   0.0  -0   ->  0.0
dqmxg040 maxmag   0.0  -0.0 ->  0.0
dqmxg041 maxmag   0.0   0.0 ->  0.0
dqmxg042 maxmag  -0.0   0   ->  0
dqmxg043 maxmag  -0.0  -0   -> -0.0
dqmxg044 maxmag  -0.0  -0.0 -> -0.0
dqmxg045 maxmag  -0.0   0.0 ->  0.0

dqmxg050 maxmag  -0E1   0E1 ->  0E+1
dqmxg051 maxmag  -0E2   0E2 ->  0E+2
dqmxg052 maxmag  -0E2   0E1 ->  0E+1
dqmxg053 maxmag  -0E1   0E2 ->  0E+2
dqmxg054 maxmag   0E1  -0E1 ->  0E+1
dqmxg055 maxmag   0E2  -0E2 ->  0E+2
dqmxg056 maxmag   0E2  -0E1 ->  0E+2
dqmxg057 maxmag   0E1  -0E2 ->  0E+1

dqmxg058 maxmag   0E1   0E1 ->  0E+1
dqmxg059 maxmag   0E2   0E2 ->  0E+2
dqmxg060 maxmag   0E2   0E1 ->  0E+2
dqmxg061 maxmag   0E1   0E2 ->  0E+2
dqmxg062 maxmag  -0E1  -0E1 -> -0E+1
dqmxg063 maxmag  -0E2  -0E2 -> -0E+2
dqmxg064 maxmag  -0E2  -0E1 -> -0E+1
dqmxg065 maxmag  -0E1  -0E2 -> -0E+1

-- Specials
dqmxg090 maxmag  Inf  -Inf   ->  Infinity
dqmxg091 maxmag  Inf  -1000  ->  Infinity
dqmxg092 maxmag  Inf  -1     ->  Infinity
dqmxg093 maxmag  Inf  -0     ->  Infinity
dqmxg094 maxmag  Inf   0     ->  Infinity
dqmxg095 maxmag  Inf   1     ->  Infinity
dqmxg096 maxmag  Inf   1000  ->  Infinity
dqmxg097 maxmag  Inf   Inf   ->  Infinity
dqmxg098 maxmag -1000  Inf   ->  Infinity
dqmxg099 maxmag -Inf   Inf   ->  Infinity
dqmxg100 maxmag -1     Inf   ->  Infinity
dqmxg101 maxmag -0     Inf   ->  Infinity
dqmxg102 maxmag  0     Inf   ->  Infinity
dqmxg103 maxmag  1     Inf   ->  Infinity
dqmxg104 maxmag  1000  Inf   ->  Infinity
dqmxg105 maxmag  Inf   Inf   ->  Infinity

dqmxg120 maxmag -Inf  -Inf   -> -Infinity
dqmxg121 maxmag -Inf  -1000  -> -Infinity
dqmxg122 maxmag -Inf  -1     -> -Infinity
dqmxg123 maxmag -Inf  -0     -> -Infinity
dqmxg124 maxmag -Inf   0     -> -Infinity
dqmxg125 maxmag -Inf   1     -> -Infinity
dqmxg126 maxmag -Inf   1000  -> -Infinity
dqmxg127 maxmag -Inf   Inf   ->  Infinity
dqmxg128 maxmag -Inf  -Inf   ->  -Infinity
dqmxg129 maxmag -1000 -Inf   -> -Infinity
dqmxg130 maxmag -1    -Inf   -> -Infinity
dqmxg131 maxmag -0    -Inf   -> -Infinity
dqmxg132 maxmag  0    -Inf   -> -Infinity
dqmxg133 maxmag  1    -Inf   -> -Infinity
dqmxg134 maxmag  1000 -Inf   -> -Infinity
dqmxg135 maxmag  Inf  -Inf   ->  Infinity

-- 2004.08.02 754r chooses number over NaN in mixed cases
dqmxg141 maxmag  NaN -Inf    -> -Infinity
dqmxg142 maxmag  NaN -1000   -> -1000
dqmxg143 maxmag  NaN -1      -> -1
dqmxg144 maxmag  NaN -0      -> -0
dqmxg145 maxmag  NaN  0      ->  0
dqmxg146 maxmag  NaN  1      ->  1
dqmxg147 maxmag  NaN  1000   ->  1000
dqmxg148 maxmag  NaN  Inf    ->  Infinity
dqmxg149 maxmag  NaN  NaN    ->  NaN
dqmxg150 maxmag -Inf  NaN    -> -Infinity
dqmxg151 maxmag -1000 NaN    -> -1000
dqmxg152 maxmag -1    NaN    -> -1
dqmxg153 maxmag -0    NaN    -> -0
dqmxg154 maxmag  0    NaN    ->  0
dqmxg155 maxmag  1    NaN    ->  1
dqmxg156 maxmag  1000 NaN    ->  1000
dqmxg157 maxmag  Inf  NaN    ->  Infinity

dqmxg161 maxmag  sNaN -Inf   ->  NaN  Invalid_operation
dqmxg162 maxmag  sNaN -1000  ->  NaN  Invalid_operation
dqmxg163 maxmag  sNaN -1     ->  NaN  Invalid_operation
dqmxg164 maxmag  sNaN -0     ->  NaN  Invalid_operation
dqmxg165 maxmag  sNaN  0     ->  NaN  Invalid_operation
dqmxg166 maxmag  sNaN  1     ->  NaN  Invalid_operation
dqmxg167 maxmag  sNaN  1000  ->  NaN  Invalid_operation
dqmxg168 maxmag  sNaN  NaN   ->  NaN  Invalid_operation
dqmxg169 maxmag  sNaN sNaN   ->  NaN  Invalid_operation
dqmxg170 maxmag  NaN  sNaN   ->  NaN  Invalid_operation
dqmxg171 maxmag -Inf  sNaN   ->  NaN  Invalid_operation
dqmxg172 maxmag -1000 sNaN   ->  NaN  Invalid_operation
dqmxg173 maxmag -1    sNaN   ->  NaN  Invalid_operation
dqmxg174 maxmag -0    sNaN   ->  NaN  Invalid_operation
dqmxg175 maxmag  0    sNaN   ->  NaN  Invalid_operation
dqmxg176 maxmag  1    sNaN   ->  NaN  Invalid_operation
dqmxg177 maxmag  1000 sNaN   ->  NaN  Invalid_operation
dqmxg178 maxmag  Inf  sNaN   ->  NaN  Invalid_operation
dqmxg179 maxmag  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dqmxg181 maxmag  NaN9  -Inf   -> -Infinity
dqmxg182 maxmag  NaN8     9   ->  9
dqmxg183 maxmag -NaN7   Inf   ->  Infinity

dqmxg184 maxmag -NaN1   NaN11 -> -NaN1
dqmxg185 maxmag  NaN2   NaN12 ->  NaN2
dqmxg186 maxmag -NaN13 -NaN7  -> -NaN13
dqmxg187 maxmag  NaN14 -NaN5  ->  NaN14

dqmxg188 maxmag -Inf    NaN4  -> -Infinity
dqmxg189 maxmag -9     -NaN3  -> -9
dqmxg190 maxmag  Inf    NaN2  ->  Infinity

dqmxg191 maxmag  sNaN99 -Inf    ->  NaN99 Invalid_operation
dqmxg192 maxmag  sNaN98 -1      ->  NaN98 Invalid_operation
dqmxg193 maxmag -sNaN97  NaN    -> -NaN97 Invalid_operation
dqmxg194 maxmag  sNaN96 sNaN94  ->  NaN96 Invalid_operation
dqmxg195 maxmag  NaN95  sNaN93  ->  NaN93 Invalid_operation
dqmxg196 maxmag -Inf    sNaN92  ->  NaN92 Invalid_operation
dqmxg197 maxmag  0      sNaN91  ->  NaN91 Invalid_operation
dqmxg198 maxmag  Inf   -sNaN90  -> -NaN90 Invalid_operation
dqmxg199 maxmag  NaN    sNaN89  ->  NaN89 Invalid_operation

-- old rounding checks
dqmxg221 maxmag 12345678000 1  -> 12345678000
dqmxg222 maxmag 1 12345678000  -> 12345678000
dqmxg223 maxmag 1234567800  1  -> 1234567800
dqmxg224 maxmag 1 1234567800   -> 1234567800
dqmxg225 maxmag 1234567890  1  -> 1234567890
dqmxg226 maxmag 1 1234567890   -> 1234567890
dqmxg227 maxmag 1234567891  1  -> 1234567891
dqmxg228 maxmag 1 1234567891   -> 1234567891
dqmxg229 maxmag 12345678901 1  -> 12345678901
dqmxg230 maxmag 1 12345678901  -> 12345678901
dqmxg231 maxmag 1234567896  1  -> 1234567896
dqmxg232 maxmag 1 1234567896   -> 1234567896
dqmxg233 maxmag -1234567891  1 -> -1234567891
dqmxg234 maxmag 1 -1234567891  -> -1234567891
dqmxg235 maxmag -12345678901 1 -> -12345678901
dqmxg236 maxmag 1 -12345678901 -> -12345678901
dqmxg237 maxmag -1234567896  1 -> -1234567896
dqmxg238 maxmag 1 -1234567896  -> -1234567896

-- from examples
dqmxg280 maxmag '3'   '2'  ->  '3'
dqmxg281 maxmag '-10' '3'  ->  '-10'
dqmxg282 maxmag '1.0' '1'  ->  '1'
dqmxg283 maxmag '1' '1.0'  ->  '1'
dqmxg284 maxmag '7' 'NaN'  ->  '7'

-- expanded list from min/max 754r purple prose
-- [explicit tests for exponent ordering]
dqmxg401 maxmag  Inf    1.1     ->  Infinity
dqmxg402 maxmag  1.1    1       ->  1.1
dqmxg403 maxmag  1      1.0     ->  1
dqmxg404 maxmag  1.0    0.1     ->  1.0
dqmxg405 maxmag  0.1    0.10    ->  0.1
dqmxg406 maxmag  0.10   0.100   ->  0.10
dqmxg407 maxmag  0.10   0       ->  0.10
dqmxg408 maxmag  0      0.0     ->  0
dqmxg409 maxmag  0.0   -0       ->  0.0
dqmxg410 maxmag  0.0   -0.0     ->  0.0
dqmxg411 maxmag  0.00  -0.0     ->  0.00
dqmxg412 maxmag  0.0   -0.00    ->  0.0
dqmxg413 maxmag  0     -0.0     ->  0
dqmxg414 maxmag  0     -0       ->  0
dqmxg415 maxmag -0.0   -0       -> -0.0
dqmxg416 maxmag -0     -0.100   -> -0.100
dqmxg417 maxmag -0.100 -0.10    -> -0.100
dqmxg418 maxmag -0.10  -0.1     -> -0.10
dqmxg419 maxmag -0.1   -1.0     -> -1.0
dqmxg420 maxmag -1.0   -1       -> -1.0
dqmxg421 maxmag -1     -1.1     -> -1.1
dqmxg423 maxmag -1.1   -Inf     -> -Infinity
-- same with operands reversed
dqmxg431 maxmag  1.1    Inf     ->  Infinity
dqmxg432 maxmag  1      1.1     ->  1.1
dqmxg433 maxmag  1.0    1       ->  1
dqmxg434 maxmag  0.1    1.0     ->  1.0
dqmxg435 maxmag  0.10   0.1     ->  0.1
dqmxg436 maxmag  0.100  0.10    ->  0.10
dqmxg437 maxmag  0      0.10    ->  0.10
dqmxg438 maxmag  0.0    0       ->  0
dqmxg439 maxmag -0      0.0     ->  0.0
dqmxg440 maxmag -0.0    0.0     ->  0.0
dqmxg441 maxmag -0.0    0.00    ->  0.00
dqmxg442 maxmag -0.00   0.0     ->  0.0
dqmxg443 maxmag -0.0    0       ->  0
dqmxg444 maxmag -0      0       ->  0
dqmxg445 maxmag -0     -0.0     -> -0.0
dqmxg446 maxmag -0.100 -0       -> -0.100
dqmxg447 maxmag -0.10  -0.100   -> -0.100
dqmxg448 maxmag -0.1   -0.10    -> -0.10
dqmxg449 maxmag -1.0   -0.1     -> -1.0
dqmxg450 maxmag -1     -1.0     -> -1.0
dqmxg451 maxmag -1.1   -1       -> -1.1
dqmxg453 maxmag -Inf   -1.1     -> -Infinity
-- largies
dqmxg460 maxmag  1000   1E+3    ->  1E+3
dqmxg461 maxmag  1E+3   1000    ->  1E+3
dqmxg462 maxmag  1000  -1E+3    ->  1000
dqmxg463 maxmag  1E+3  -1000    ->  1E+3
dqmxg464 maxmag -1000   1E+3    ->  1E+3
dqmxg465 maxmag -1E+3   1000    ->  1000
dqmxg466 maxmag -1000  -1E+3    -> -1000
dqmxg467 maxmag -1E+3  -1000    -> -1000

-- subnormals
dqmxg510 maxmag  1.00E-6143       0  ->   1.00E-6143
dqmxg511 maxmag  0.1E-6143        0  ->   1E-6144    Subnormal
dqmxg512 maxmag  0.10E-6143       0  ->   1.0E-6144  Subnormal
dqmxg513 maxmag  0.100E-6143      0  ->   1.00E-6144 Subnormal
dqmxg514 maxmag  0.01E-6143       0  ->   1E-6145    Subnormal
dqmxg515 maxmag  0.999E-6143      0  ->   9.99E-6144 Subnormal
dqmxg516 maxmag  0.099E-6143      0  ->   9.9E-6145  Subnormal
dqmxg517 maxmag  0.009E-6143      0  ->   9E-6146    Subnormal
dqmxg518 maxmag  0.001E-6143      0  ->   1E-6146    Subnormal
dqmxg519 maxmag  0.0009E-6143     0  ->   9E-6147    Subnormal
dqmxg520 maxmag  0.0001E-6143     0  ->   1E-6147    Subnormal

dqmxg530 maxmag -1.00E-6143       0  ->  -1.00E-6143
dqmxg531 maxmag -0.1E-6143        0  ->  -1E-6144    Subnormal
dqmxg532 maxmag -0.10E-6143       0  ->  -1.0E-6144  Subnormal
dqmxg533 maxmag -0.100E-6143      0  ->  -1.00E-6144 Subnormal
dqmxg534 maxmag -0.01E-6143       0  ->  -1E-6145    Subnormal
dqmxg535 maxmag -0.999E-6143      0  ->  -9.99E-6144 Subnormal
dqmxg536 maxmag -0.099E-6143      0  ->  -9.9E-6145  Subnormal
dqmxg537 maxmag -0.009E-6143      0  ->  -9E-6146    Subnormal
dqmxg538 maxmag -0.001E-6143      0  ->  -1E-6146    Subnormal
dqmxg539 maxmag -0.0009E-6143     0  ->  -9E-6147    Subnormal
dqmxg540 maxmag -0.0001E-6143     0  ->  -1E-6147    Subnormal

-- Null tests
dqmxg900 maxmag 10  #  -> NaN Invalid_operation
dqmxg901 maxmag  # 10  -> NaN Invalid_operation

