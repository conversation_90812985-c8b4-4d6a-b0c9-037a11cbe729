.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_aead_cipher_decrypt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_aead_cipher_decrypt \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_aead_cipher_decrypt(gnutls_aead_cipher_hd_t " handle ", const void * " nonce ", size_t " nonce_len ", const void * " auth ", size_t " auth_len ", size_t " tag_size ", const void * " ctext ", size_t " ctext_len ", void * " ptext ", size_t * " ptext_len ");"
.SH ARGUMENTS
.IP "gnutls_aead_cipher_hd_t handle" 12
is a \fBgnutls_aead_cipher_hd_t\fP type.
.IP "const void * nonce" 12
the nonce to set
.IP "size_t nonce_len" 12
The length of the nonce
.IP "const void * auth" 12
additional data to be authenticated
.IP "size_t auth_len" 12
The length of the data
.IP "size_t tag_size" 12
The size of the tag to use (use zero for the default)
.IP "const void * ctext" 12
the data to decrypt (including the authentication tag)
.IP "size_t ctext_len" 12
the length of data to decrypt (includes tag size)
.IP "void * ptext" 12
the decrypted data
.IP "size_t * ptext_len" 12
the length of decrypted data (initially must hold the maximum available size)
.SH "DESCRIPTION"
This function will decrypt the given data using the algorithm
specified by the context. This function must be provided the complete
data to be decrypted, including the authentication tag. On several
AEAD ciphers, the authentication tag is appended to the ciphertext,
though this is not a general rule. This function will fail if
the tag verification fails.
.SH "RETURNS"
Zero or a negative error code on verification failure or other error.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
