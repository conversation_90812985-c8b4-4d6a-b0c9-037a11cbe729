CMP0007
-------

.. |REMOVED_IN_CMAKE_VERSION| replace:: 4.0
.. include:: REMOVED_PROLOGUE.txt

list command no longer ignores empty elements.

This policy determines whether the list command will ignore empty
elements in the list.  CMake 2.4 and below list commands ignored all
empty elements in the list.  For example, ``a;b;;c`` would have length 3
and not 4.  The ``OLD`` behavior for this policy is to ignore empty list
elements.  The ``NEW`` behavior for this policy is to correctly count
empty elements in a list.

.. |INTRODUCED_IN_CMAKE_VERSION| replace:: 2.6.0
.. |WARNED_OR_DID_NOT_WARN| replace:: warned
.. include:: REMOVED_EPILOGUE.txt
