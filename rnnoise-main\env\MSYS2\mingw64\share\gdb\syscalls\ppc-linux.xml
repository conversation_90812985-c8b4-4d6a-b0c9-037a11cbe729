<?xml version="1.0"?>
<!DOCTYPE syscalls_info SYSTEM "gdb-syscalls.dtd">
<!-- Copyright (C) 2009-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->
<!-- This file was generated using the following file:

     arch/powerpc/kernel/syscalls/syscall.tbl

     The file mentioned above belongs to the Linux Kernel.  -->
<syscalls_info>
  <syscall name="restart_syscall" number="0"/>
  <syscall name="exit" number="1" groups="process"/>
  <syscall name="fork" number="2" groups="process"/>
  <syscall name="read" number="3" groups="descriptor"/>
  <syscall name="write" number="4" groups="descriptor"/>
  <syscall name="open" number="5" groups="descriptor,file"/>
  <syscall name="close" number="6" groups="descriptor"/>
  <syscall name="waitpid" number="7" groups="process"/>
  <syscall name="creat" number="8" groups="descriptor,file"/>
  <syscall name="link" number="9" groups="file"/>
  <syscall name="unlink" number="10" groups="file"/>
  <syscall name="execve" number="11" groups="file,process"/>
  <syscall name="chdir" number="12" groups="file"/>
  <syscall name="time" number="13"/>
  <syscall name="mknod" number="14" groups="file"/>
  <syscall name="chmod" number="15" groups="file"/>
  <syscall name="lchown" number="16" groups="file"/>
  <syscall name="break" number="17" groups="memory"/>
  <syscall name="oldstat" number="18" groups="file"/>
  <syscall name="lseek" number="19" groups="descriptor"/>
  <syscall name="getpid" number="20"/>
  <syscall name="mount" number="21" groups="file"/>
  <syscall name="umount" number="22" groups="file"/>
  <syscall name="setuid" number="23"/>
  <syscall name="getuid" number="24"/>
  <syscall name="stime" number="25"/>
  <syscall name="ptrace" number="26"/>
  <syscall name="alarm" number="27"/>
  <syscall name="oldfstat" number="28" groups="descriptor"/>
  <syscall name="pause" number="29" groups="signal"/>
  <syscall name="utime" number="30" groups="file"/>
  <syscall name="stty" number="31"/>
  <syscall name="gtty" number="32"/>
  <syscall name="access" number="33" groups="file"/>
  <syscall name="nice" number="34"/>
  <syscall name="ftime" number="35"/>
  <syscall name="sync" number="36"/>
  <syscall name="kill" number="37" groups="signal,process"/>
  <syscall name="rename" number="38" groups="file"/>
  <syscall name="mkdir" number="39" groups="file"/>
  <syscall name="rmdir" number="40" groups="file"/>
  <syscall name="dup" number="41" groups="descriptor"/>
  <syscall name="pipe" number="42" groups="descriptor"/>
  <syscall name="times" number="43"/>
  <syscall name="prof" number="44"/>
  <syscall name="brk" number="45" groups="memory"/>
  <syscall name="setgid" number="46"/>
  <syscall name="getgid" number="47"/>
  <syscall name="signal" number="48" groups="signal"/>
  <syscall name="geteuid" number="49"/>
  <syscall name="getegid" number="50"/>
  <syscall name="acct" number="51" groups="file"/>
  <syscall name="umount2" number="52" groups="file"/>
  <syscall name="lock" number="53"/>
  <syscall name="ioctl" number="54" groups="descriptor"/>
  <syscall name="fcntl" number="55" groups="descriptor"/>
  <syscall name="mpx" number="56"/>
  <syscall name="setpgid" number="57"/>
  <syscall name="ulimit" number="58"/>
  <syscall name="oldolduname" number="59"/>
  <syscall name="umask" number="60"/>
  <syscall name="chroot" number="61" groups="file"/>
  <syscall name="ustat" number="62"/>
  <syscall name="dup2" number="63" groups="descriptor"/>
  <syscall name="getppid" number="64"/>
  <syscall name="getpgrp" number="65"/>
  <syscall name="setsid" number="66"/>
  <syscall name="sigaction" number="67" groups="signal"/>
  <syscall name="sgetmask" number="68" groups="signal"/>
  <syscall name="ssetmask" number="69" groups="signal"/>
  <syscall name="setreuid" number="70"/>
  <syscall name="setregid" number="71"/>
  <syscall name="sigsuspend" number="72" groups="signal"/>
  <syscall name="sigpending" number="73" groups="signal"/>
  <syscall name="sethostname" number="74"/>
  <syscall name="setrlimit" number="75"/>
  <syscall name="getrlimit" number="76"/>
  <syscall name="getrusage" number="77"/>
  <syscall name="gettimeofday" number="78"/>
  <syscall name="settimeofday" number="79"/>
  <syscall name="getgroups" number="80"/>
  <syscall name="setgroups" number="81"/>
  <syscall name="select" number="82" groups="descriptor"/>
  <syscall name="symlink" number="83" groups="file"/>
  <syscall name="oldlstat" number="84" groups="file"/>
  <syscall name="readlink" number="85" groups="file"/>
  <syscall name="uselib" number="86" groups="file"/>
  <syscall name="swapon" number="87" groups="file"/>
  <syscall name="reboot" number="88"/>
  <syscall name="readdir" number="89" groups="descriptor"/>
  <syscall name="mmap" number="90" groups="descriptor,memory"/>
  <syscall name="munmap" number="91" groups="memory"/>
  <syscall name="truncate" number="92" groups="file"/>
  <syscall name="ftruncate" number="93" groups="descriptor"/>
  <syscall name="fchmod" number="94" groups="descriptor"/>
  <syscall name="fchown" number="95" groups="descriptor"/>
  <syscall name="getpriority" number="96"/>
  <syscall name="setpriority" number="97"/>
  <syscall name="profil" number="98"/>
  <syscall name="statfs" number="99" groups="file"/>
  <syscall name="fstatfs" number="100" groups="descriptor"/>
  <syscall name="ioperm" number="101"/>
  <syscall name="socketcall" number="102" groups="descriptor"/>
  <syscall name="syslog" number="103"/>
  <syscall name="setitimer" number="104"/>
  <syscall name="getitimer" number="105"/>
  <syscall name="stat" number="106" groups="file"/>
  <syscall name="lstat" number="107" groups="file"/>
  <syscall name="fstat" number="108" groups="descriptor"/>
  <syscall name="olduname" number="109"/>
  <syscall name="iopl" number="110"/>
  <syscall name="vhangup" number="111"/>
  <syscall name="idle" number="112"/>
  <syscall name="vm86" number="113"/>
  <syscall name="wait4" number="114" groups="process"/>
  <syscall name="swapoff" number="115" groups="file"/>
  <syscall name="sysinfo" number="116"/>
  <syscall name="ipc" number="117" groups="ipc"/>
  <syscall name="fsync" number="118" groups="descriptor"/>
  <syscall name="sigreturn" number="119" groups="signal"/>
  <syscall name="clone" number="120" groups="process"/>
  <syscall name="setdomainname" number="121"/>
  <syscall name="uname" number="122"/>
  <syscall name="modify_ldt" number="123"/>
  <syscall name="adjtimex" number="124"/>
  <syscall name="mprotect" number="125" groups="memory"/>
  <syscall name="sigprocmask" number="126" groups="signal"/>
  <syscall name="create_module" number="127"/>
  <syscall name="init_module" number="128"/>
  <syscall name="delete_module" number="129"/>
  <syscall name="get_kernel_syms" number="130"/>
  <syscall name="quotactl" number="131" groups="file"/>
  <syscall name="getpgid" number="132"/>
  <syscall name="fchdir" number="133" groups="descriptor"/>
  <syscall name="bdflush" number="134"/>
  <syscall name="sysfs" number="135"/>
  <syscall name="personality" number="136"/>
  <syscall name="afs_syscall" number="137"/>
  <syscall name="setfsuid" number="138"/>
  <syscall name="setfsgid" number="139"/>
  <syscall name="_llseek" number="140" groups="descriptor"/>
  <syscall name="getdents" number="141" groups="descriptor"/>
  <syscall name="_newselect" number="142" groups="descriptor"/>
  <syscall name="flock" number="143" groups="descriptor"/>
  <syscall name="msync" number="144" groups="memory"/>
  <syscall name="readv" number="145" groups="descriptor"/>
  <syscall name="writev" number="146" groups="descriptor"/>
  <syscall name="getsid" number="147"/>
  <syscall name="fdatasync" number="148" groups="descriptor"/>
  <syscall name="_sysctl" number="149"/>
  <syscall name="mlock" number="150" groups="memory"/>
  <syscall name="munlock" number="151" groups="memory"/>
  <syscall name="mlockall" number="152" groups="memory"/>
  <syscall name="munlockall" number="153" groups="memory"/>
  <syscall name="sched_setparam" number="154"/>
  <syscall name="sched_getparam" number="155"/>
  <syscall name="sched_setscheduler" number="156"/>
  <syscall name="sched_getscheduler" number="157"/>
  <syscall name="sched_yield" number="158"/>
  <syscall name="sched_get_priority_max" number="159"/>
  <syscall name="sched_get_priority_min" number="160"/>
  <syscall name="sched_rr_get_interval" number="161"/>
  <syscall name="nanosleep" number="162"/>
  <syscall name="mremap" number="163" groups="memory"/>
  <syscall name="setresuid" number="164"/>
  <syscall name="getresuid" number="165"/>
  <syscall name="query_module" number="166"/>
  <syscall name="poll" number="167" groups="descriptor"/>
  <syscall name="nfsservctl" number="168"/>
  <syscall name="setresgid" number="169"/>
  <syscall name="getresgid" number="170"/>
  <syscall name="prctl" number="171"/>
  <syscall name="rt_sigreturn" number="172" groups="signal"/>
  <syscall name="rt_sigaction" number="173" groups="signal"/>
  <syscall name="rt_sigprocmask" number="174" groups="signal"/>
  <syscall name="rt_sigpending" number="175" groups="signal"/>
  <syscall name="rt_sigtimedwait" number="176" groups="signal"/>
  <syscall name="rt_sigqueueinfo" number="177" groups="signal,process"/>
  <syscall name="rt_sigsuspend" number="178" groups="signal"/>
  <syscall name="pread64" number="179" groups="descriptor"/>
  <syscall name="pwrite64" number="180" groups="descriptor"/>
  <syscall name="chown" number="181" groups="file"/>
  <syscall name="getcwd" number="182" groups="file"/>
  <syscall name="capget" number="183"/>
  <syscall name="capset" number="184"/>
  <syscall name="sigaltstack" number="185" groups="signal"/>
  <syscall name="sendfile" number="186" groups="descriptor,network"/>
  <syscall name="getpmsg" number="187" groups="network"/>
  <syscall name="putpmsg" number="188" groups="network"/>
  <syscall name="vfork" number="189" groups="process"/>
  <syscall name="ugetrlimit" number="190"/>
  <syscall name="readahead" number="191" groups="descriptor"/>
  <syscall name="mmap2" number="192" groups="descriptor,memory"/>
  <syscall name="truncate64" number="193" groups="file"/>
  <syscall name="ftruncate64" number="194" groups="descriptor"/>
  <syscall name="stat64" number="195" groups="file"/>
  <syscall name="lstat64" number="196" groups="file"/>
  <syscall name="fstat64" number="197" groups="descriptor"/>
  <syscall name="pciconfig_read" number="198"/>
  <syscall name="pciconfig_write" number="199"/>
  <syscall name="pciconfig_iobase" number="200"/>
  <syscall name="multiplexer" number="201"/>
  <syscall name="getdents64" number="202" groups="descriptor"/>
  <syscall name="pivot_root" number="203" groups="file"/>
  <syscall name="fcntl64" number="204" groups="descriptor"/>
  <syscall name="madvise" number="205" groups="memory"/>
  <syscall name="mincore" number="206" groups="memory"/>
  <syscall name="gettid" number="207"/>
  <syscall name="tkill" number="208" groups="signal,process"/>
  <syscall name="setxattr" number="209" groups="file"/>
  <syscall name="lsetxattr" number="210" groups="file"/>
  <syscall name="fsetxattr" number="211" groups="descriptor"/>
  <syscall name="getxattr" number="212" groups="file"/>
  <syscall name="lgetxattr" number="213" groups="file"/>
  <syscall name="fgetxattr" number="214" groups="descriptor"/>
  <syscall name="listxattr" number="215" groups="file"/>
  <syscall name="llistxattr" number="216" groups="file"/>
  <syscall name="flistxattr" number="217" groups="descriptor"/>
  <syscall name="removexattr" number="218" groups="file"/>
  <syscall name="lremovexattr" number="219" groups="file"/>
  <syscall name="fremovexattr" number="220" groups="descriptor"/>
  <syscall name="futex" number="221"/>
  <syscall name="sched_setaffinity" number="222"/>
  <syscall name="sched_getaffinity" number="223"/>
  <syscall name="tuxcall" number="225"/>
  <syscall name="sendfile64" number="226" groups="descriptor,network"/>
  <syscall name="io_setup" number="227" groups="memory"/>
  <syscall name="io_destroy" number="228" groups="memory"/>
  <syscall name="io_getevents" number="229"/>
  <syscall name="io_submit" number="230"/>
  <syscall name="io_cancel" number="231"/>
  <syscall name="set_tid_address" number="232"/>
  <syscall name="fadvise64" number="233" groups="descriptor"/>
  <syscall name="exit_group" number="234" groups="process"/>
  <syscall name="lookup_dcookie" number="235"/>
  <syscall name="epoll_create" number="236" groups="descriptor"/>
  <syscall name="epoll_ctl" number="237" groups="descriptor"/>
  <syscall name="epoll_wait" number="238" groups="descriptor"/>
  <syscall name="remap_file_pages" number="239" groups="memory"/>
  <syscall name="timer_create" number="240"/>
  <syscall name="timer_settime" number="241"/>
  <syscall name="timer_gettime" number="242"/>
  <syscall name="timer_getoverrun" number="243"/>
  <syscall name="timer_delete" number="244"/>
  <syscall name="clock_settime" number="245"/>
  <syscall name="clock_gettime" number="246"/>
  <syscall name="clock_getres" number="247"/>
  <syscall name="clock_nanosleep" number="248"/>
  <syscall name="swapcontext" number="249"/>
  <syscall name="tgkill" number="250" groups="signal,process"/>
  <syscall name="utimes" number="251" groups="file"/>
  <syscall name="statfs64" number="252" groups="file"/>
  <syscall name="fstatfs64" number="253" groups="descriptor"/>
  <syscall name="fadvise64_64" number="254" groups="descriptor"/>
  <syscall name="rtas" number="255"/>
  <syscall name="sys_debug_setcontext" number="256"/>
  <syscall name="migrate_pages" number="258" groups="memory"/>
  <syscall name="mbind" number="259" groups="memory"/>
  <syscall name="get_mempolicy" number="260" groups="memory"/>
  <syscall name="set_mempolicy" number="261" groups="memory"/>
  <syscall name="mq_open" number="262" groups="descriptor"/>
  <syscall name="mq_unlink" number="263"/>
  <syscall name="mq_timedsend" number="264" groups="descriptor"/>
  <syscall name="mq_timedreceive" number="265" groups="descriptor"/>
  <syscall name="mq_notify" number="266" groups="descriptor"/>
  <syscall name="mq_getsetattr" number="267" groups="descriptor"/>
  <syscall name="kexec_load" number="268"/>
  <syscall name="add_key" number="269"/>
  <syscall name="request_key" number="270"/>
  <syscall name="keyctl" number="271"/>
  <syscall name="waitid" number="272" groups="process"/>
  <syscall name="ioprio_set" number="273"/>
  <syscall name="ioprio_get" number="274"/>
  <syscall name="inotify_init" number="275" groups="descriptor"/>
  <syscall name="inotify_add_watch" number="276" groups="descriptor,file"/>
  <syscall name="inotify_rm_watch" number="277" groups="descriptor"/>
  <syscall name="spu_run" number="278"/>
  <syscall name="spu_create" number="279"/>
  <syscall name="pselect6" number="280" groups="descriptor"/>
  <syscall name="ppoll" number="281" groups="descriptor"/>
  <syscall name="unshare" number="282"/>
  <syscall name="splice" number="283" groups="descriptor"/>
  <syscall name="tee" number="284" groups="descriptor"/>
  <syscall name="vmsplice" number="285" groups="descriptor"/>
  <syscall name="openat" number="286" groups="descriptor,file"/>
  <syscall name="mkdirat" number="287" groups="descriptor,file"/>
  <syscall name="mknodat" number="288" groups="descriptor,file"/>
  <syscall name="fchownat" number="289" groups="descriptor,file"/>
  <syscall name="futimesat" number="290" groups="descriptor,file"/>
  <syscall name="fstatat64" number="291" groups="descriptor,file"/>
  <syscall name="unlinkat" number="292" groups="descriptor,file"/>
  <syscall name="renameat" number="293" groups="descriptor,file"/>
  <syscall name="linkat" number="294" groups="descriptor,file"/>
  <syscall name="symlinkat" number="295" groups="descriptor,file"/>
  <syscall name="readlinkat" number="296" groups="descriptor,file"/>
  <syscall name="fchmodat" number="297" groups="descriptor,file"/>
  <syscall name="faccessat" number="298" groups="descriptor,file"/>
  <syscall name="get_robust_list" number="299"/>
  <syscall name="set_robust_list" number="300"/>
  <syscall name="move_pages" number="301" groups="memory"/>
  <syscall name="getcpu" number="302"/>
  <syscall name="epoll_pwait" number="303" groups="descriptor"/>
  <syscall name="utimensat" number="304" groups="descriptor,file"/>
  <syscall name="signalfd" number="305" groups="descriptor,signal"/>
  <syscall name="timerfd_create" number="306" groups="descriptor"/>
  <syscall name="eventfd" number="307" groups="descriptor"/>
  <syscall name="sync_file_range2" number="308" groups="descriptor"/>
  <syscall name="fallocate" number="309" groups="descriptor"/>
  <syscall name="subpage_prot" number="310"/>
  <syscall name="timerfd_settime" number="311" groups="descriptor"/>
  <syscall name="timerfd_gettime" number="312" groups="descriptor"/>
  <syscall name="signalfd4" number="313" groups="descriptor,signal"/>
  <syscall name="eventfd2" number="314" groups="descriptor"/>
  <syscall name="epoll_create1" number="315" groups="descriptor"/>
  <syscall name="dup3" number="316" groups="descriptor"/>
  <syscall name="pipe2" number="317" groups="descriptor"/>
  <syscall name="inotify_init1" number="318" groups="descriptor"/>
  <syscall name="perf_event_open" number="319" groups="descriptor"/>
  <syscall name="preadv" number="320" groups="descriptor"/>
  <syscall name="pwritev" number="321" groups="descriptor"/>
  <syscall name="rt_tgsigqueueinfo" number="322" groups="process,signal"/>
  <syscall name="fanotify_init" number="323" groups="descriptor"/>
  <syscall name="fanotify_mark" number="324" groups="descriptor,file"/>
  <syscall name="prlimit64" number="325"/>
  <syscall name="socket" number="326" groups="network"/>
  <syscall name="bind" number="327" groups="network"/>
  <syscall name="connect" number="328" groups="network"/>
  <syscall name="listen" number="329" groups="network"/>
  <syscall name="accept" number="330" groups="network"/>
  <syscall name="getsockname" number="331" groups="network"/>
  <syscall name="getpeername" number="332" groups="network"/>
  <syscall name="socketpair" number="333" groups="network"/>
  <syscall name="send" number="334" groups="network"/>
  <syscall name="sendto" number="335" groups="network"/>
  <syscall name="recv" number="336" groups="network"/>
  <syscall name="recvfrom" number="337" groups="network"/>
  <syscall name="shutdown" number="338" groups="network"/>
  <syscall name="setsockopt" number="339" groups="network"/>
  <syscall name="getsockopt" number="340" groups="network"/>
  <syscall name="sendmsg" number="341" groups="network"/>
  <syscall name="recvmsg" number="342" groups="network"/>
  <syscall name="recvmmsg" number="343" groups="network"/>
  <syscall name="accept4" number="344" groups="network"/>
  <syscall name="name_to_handle_at" number="345" groups="descriptor,file"/>
  <syscall name="open_by_handle_at" number="346" groups="descriptor"/>
  <syscall name="clock_adjtime" number="347"/>
  <syscall name="syncfs" number="348" groups="descriptor"/>
  <syscall name="sendmmsg" number="349" groups="network"/>
  <syscall name="setns" number="350" groups="descriptor"/>
  <syscall name="process_vm_readv" number="351"/>
  <syscall name="process_vm_writev" number="352"/>
  <syscall name="finit_module" number="353" groups="descriptor"/>
  <syscall name="kcmp" number="354"/>
  <syscall name="sched_setattr" number="355"/>
  <syscall name="sched_getattr" number="356"/>
  <syscall name="renameat2" number="357" groups="descriptor,file"/>
  <syscall name="seccomp" number="358"/>
  <syscall name="getrandom" number="359"/>
  <syscall name="memfd_create" number="360" groups="descriptor"/>
  <syscall name="bpf" number="361" groups="descriptor"/>
  <syscall name="execveat" number="362" groups="descriptor,file,process"/>
  <syscall name="switch_endian" number="363"/>
  <syscall name="userfaultfd" number="364" groups="descriptor"/>
  <syscall name="membarrier" number="365"/>
  <syscall name="mlock2" number="378" groups="memory"/>
  <syscall name="copy_file_range" number="379" groups="descriptor"/>
  <syscall name="preadv2" number="380" groups="descriptor"/>
  <syscall name="pwritev2" number="381" groups="descriptor"/>
  <syscall name="kexec_file_load" number="382" groups="descriptor"/>
  <syscall name="statx" number="383" groups="descriptor,file"/>
  <syscall name="pkey_alloc" number="384"/>
  <syscall name="pkey_free" number="385"/>
  <syscall name="pkey_mprotect" number="386" groups="memory"/>
  <syscall name="rseq" number="387"/>
  <syscall name="io_pgetevents" number="388"/>
  <syscall name="semget" number="393" groups="ipc"/>
  <syscall name="semctl" number="394" groups="ipc"/>
  <syscall name="shmget" number="395" groups="ipc"/>
  <syscall name="shmctl" number="396" groups="ipc"/>
  <syscall name="shmat" number="397" groups="ipc,memory"/>
  <syscall name="shmdt" number="398" groups="ipc,memory"/>
  <syscall name="msgget" number="399" groups="ipc"/>
  <syscall name="msgsnd" number="400" groups="ipc"/>
  <syscall name="msgrcv" number="401" groups="ipc"/>
  <syscall name="msgctl" number="402" groups="ipc"/>
  <syscall name="clock_gettime64" number="403"/>
  <syscall name="clock_settime64" number="404"/>
  <syscall name="clock_adjtime64" number="405"/>
  <syscall name="clock_getres_time64" number="406"/>
  <syscall name="clock_nanosleep_time64" number="407"/>
  <syscall name="timer_gettime64" number="408"/>
  <syscall name="timer_settime64" number="409"/>
  <syscall name="timerfd_gettime64" number="410" groups="descriptor"/>
  <syscall name="timerfd_settime64" number="411" groups="descriptor"/>
  <syscall name="utimensat_time64" number="412" groups="descriptor,file"/>
  <syscall name="pselect6_time64" number="413" groups="descriptor"/>
  <syscall name="ppoll_time64" number="414" groups="descriptor"/>
  <syscall name="io_pgetevents_time64" number="416"/>
  <syscall name="recvmmsg_time64" number="417" groups="network"/>
  <syscall name="mq_timedsend_time64" number="418" groups="descriptor"/>
  <syscall name="mq_timedreceive_time64" number="419" groups="descriptor"/>
  <syscall name="semtimedop_time64" number="420" groups="ipc"/>
  <syscall name="rt_sigtimedwait_time64" number="421" groups="signal"/>
  <syscall name="futex_time64" number="422"/>
  <syscall name="sched_rr_get_interval_time64" number="423"/>
  <syscall name="pidfd_send_signal" number="424" groups="descriptor,signal,process"/>
  <syscall name="io_uring_setup" number="425" groups="descriptor"/>
  <syscall name="io_uring_enter" number="426" groups="descriptor,signal"/>
  <syscall name="io_uring_register" number="427" groups="descriptor,memory"/>
  <syscall name="open_tree" number="428" groups="descriptor,file"/>
  <syscall name="move_mount" number="429" groups="descriptor,file"/>
  <syscall name="fsopen" number="430" groups="descriptor"/>
  <syscall name="fsconfig" number="431" groups="descriptor,file"/>
  <syscall name="fsmount" number="432" groups="descriptor"/>
  <syscall name="fspick" number="433" groups="descriptor,file"/>
  <syscall name="pidfd_open" number="434" groups="descriptor"/>
  <syscall name="clone3" number="435" groups="process"/>
  <syscall name="close_range" number="436"/>
  <syscall name="openat2" number="437" groups="descriptor,file"/>
  <syscall name="pidfd_getfd" number="438" groups="descriptor"/>
  <syscall name="faccessat2" number="439" groups="descriptor,file"/>
  <syscall name="process_madvise" number="440" groups="descriptor"/>
  <syscall name="epoll_pwait2" number="441" groups="descriptor"/>
  <syscall name="mount_setattr" number="442" groups="descriptor,file"/>
  <syscall name="quotactl_fd" number="443" groups="descriptor"/>
  <syscall name="landlock_create_ruleset" number="444" groups="descriptor"/>
  <syscall name="landlock_add_rule" number="445" groups="descriptor"/>
  <syscall name="landlock_restrict_self" number="446" groups="descriptor"/>
  <syscall name="process_mrelease" number="448" groups="descriptor"/>
  <syscall name="futex_waitv" number="449"/>
  <syscall name="set_mempolicy_home_node" number="450" groups="memory"/>
  <syscall name="cachestat" number="451" groups="descriptor"/>
  <syscall name="fchmodat2" number="452" groups="descriptor,file"/>
  <syscall name="map_shadow_stack" number="453" groups="memory"/>
  <syscall name="futex_wake" number="454"/>
  <syscall name="futex_wait" number="455"/>
  <syscall name="futex_requeue" number="456"/>
  <syscall name="statmount" number="457"/>
  <syscall name="listmount" number="458"/>
  <syscall name="lsm_get_self_attr" number="459"/>
  <syscall name="lsm_set_self_attr" number="460"/>
  <syscall name="lsm_list_modules" number="461"/>
  <syscall name="mseal" number="462" groups="memory"/>
  <syscall name="setxattrat" number="463"/>
  <syscall name="getxattrat" number="464"/>
  <syscall name="listxattrat" number="465"/>
  <syscall name="removexattrat" number="466"/>
</syscalls_info>
