.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_format_imported_identity" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_format_imported_identity \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_psk_format_imported_identity(const gnutls_datum_t * " identity ", const gnutls_datum_t * " context ", gnutls_protocol_t " version ", gnutls_digest_algorithm_t " hash ", gnutls_datum_t * " imported_identity ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * identity" 12
external identity
.IP "const gnutls_datum_t * context" 12
optional contextual information
.IP "gnutls_protocol_t version" 12
protocol version to which the PSK is imported
.IP "gnutls_digest_algorithm_t hash" 12
hash algorithm used for KDF
.IP "gnutls_datum_t * imported_identity" 12
where the imported identity is stored
.SH "DESCRIPTION"
This formats an external PSK identity  \fIidentity\fP into an imported
form, described in RFC 9258 as ImportedIdentity.

Upon success, the data field of  \fIimported_identity\fP is allocated
using \fBgnutls_malloc()\fP and the caller must free the memory after
use.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success, otherwise a negative error code.
.SH "SINCE"
3.8.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
