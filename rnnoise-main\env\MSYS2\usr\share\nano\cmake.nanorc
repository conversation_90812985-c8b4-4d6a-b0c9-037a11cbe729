## Syntax highlighting for CMake files.

## Original author:  <PERSON>

syntax cmake "(CMakeLists\.txt|\.cmake)$"
comment "#"

color green "^[[:blank:]]*[[:alnum:]_]+"
icolor brightyellow "^[[:blank:]]*(include|include_directories|include_external_msproject)\>"

icolor brightgreen "^[[:blank:]]*((else|end)?if|else|(end)?while|(end)?foreach|break)\>"
color brightgreen "\<(NOT|COMMAND|POLICY|TARGET|EXISTS|IS_(DIRECTORY|ABSOLUTE)|DEFINED)[[:blank:]]"
color brightgreen "[[:blank:]](OR|AND|IS_NEWER_THAN|MATCHES|(STR|VERSION_)?(LESS|GREATER|EQUAL))[[:blank:]]"

icolor brightred "^[[:blank:]]*((end)?(function|macro)|return)"

icolor cyan start="\$(ENV)?\{" end="\}"
color magenta "\<(APPLE|UNIX|WIN32|CYGWIN|BORLAND|MINGW|MSVC(_IDE|60|71|80|90)?)\>"

# Comments.
color brightblue "(^|[[:blank:]])#.*"

# Trailing whitespace.
color ,green "[[:space:]]+$"
