.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_x509_trust_dir" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_x509_trust_dir \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_x509_trust_dir(gnutls_certificate_credentials_t " cred ", const char * " ca_dir ", gnutls_x509_crt_fmt_t " type ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t cred" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "const char * ca_dir" 12
is a directory containing the list of trusted CAs (DER or PEM list)
.IP "gnutls_x509_crt_fmt_t type" 12
is PEM or DER
.SH "DESCRIPTION"
This function adds the trusted CAs present in the directory in order to
verify client or server certificates. This function is identical
to \fBgnutls_certificate_set_x509_trust_file()\fP but loads all certificates
in a directory.
.SH "RETURNS"
the number of certificates processed
.SH "SINCE"
3.3.6
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
