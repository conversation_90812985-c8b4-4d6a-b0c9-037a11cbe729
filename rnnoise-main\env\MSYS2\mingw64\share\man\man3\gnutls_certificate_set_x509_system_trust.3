.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_x509_system_trust" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_x509_system_trust \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_x509_system_trust(gnutls_certificate_credentials_t " cred ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t cred" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.SH "DESCRIPTION"
This function adds the system's default trusted CAs in order to
verify client or server certificates.

In the case the system is currently unsupported \fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP
is returned.
.SH "RETURNS"
the number of certificates processed or a negative error code
on error.
.SH "SINCE"
3.0.20
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
