$VAR1 = {
  "mapping" => {
    "comment" => {
      "type" => "text"
    },
    "cpanconfig" => {
      "mapping" => {
        "=" => {
          "type" => "text"
        }
      },
      "type" => "map"
    },
    "depends" => {
      "mapping" => {
        "build_requires" => {
          "mapping" => {
            "=" => {
              "type" => "text"
            }
          },
          "type" => "map"
        },
        "configure_requires" => {},
        "requires" => {}
      },
      "type" => "map"
    },
    "disabled" => {
      "enum" => [
        0,
        1
      ],
      "type" => "int"
    },
    "features" => {
      "sequence" => [
        {
          "type" => "text"
        }
      ],
      "type" => "seq"
    },
    "goto" => {
      "type" => "text"
    },
    "install" => {
      "mapping" => {
        "args" => {
          "sequence" => [
            {
              "type" => "text"
            }
          ],
          "type" => "seq"
        },
        "commandline" => {
          "type" => "text"
        },
        "eexpect" => {
          "mapping" => {
            "mode" => {
              "enum" => [
                "deterministic",
                "anyorder"
              ],
              "type" => "text"
            },
            "reuse" => {
              "type" => "int"
            },
            "talk" => {
              "sequence" => [
                {
                  "type" => "text"
                }
              ],
              "type" => "seq"
            },
            "timeout" => {
              "type" => "number"
            }
          },
          "type" => "map"
        },
        "env" => {
          "mapping" => {
            "=" => {
              "type" => "text"
            }
          },
          "type" => "map"
        },
        "expect" => {
          "sequence" => [
            {
              "type" => "text"
            }
          ],
          "type" => "seq"
        }
      },
      "type" => "map"
    },
    "make" => {},
    "match" => {
      "mapping" => {
        "distribution" => {
          "type" => "text"
        },
        "env" => {
          "mapping" => {
            "=" => {
              "type" => "text"
            }
          },
          "type" => "map"
        },
        "module" => {
          "type" => "text"
        },
        "perl" => {
          "type" => "text"
        },
        "perlconfig" => {}
      },
      "type" => "map"
    },
    "patches" => {
      "sequence" => [
        {
          "type" => "text"
        }
      ],
      "type" => "seq"
    },
    "pl" => {},
    "reminder" => {
      "type" => "text"
    },
    "test" => {}
  },
  "type" => "map"
};
$VAR1->{"mapping"}{"depends"}{"mapping"}{"configure_requires"} = $VAR1->{"mapping"}{"depends"}{"mapping"}{"build_requires"};
$VAR1->{"mapping"}{"depends"}{"mapping"}{"requires"} = $VAR1->{"mapping"}{"depends"}{"mapping"}{"build_requires"};
$VAR1->{"mapping"}{"make"} = $VAR1->{"mapping"}{"install"};
$VAR1->{"mapping"}{"match"}{"mapping"}{"perlconfig"} = $VAR1->{"mapping"}{"match"}{"mapping"}{"env"};
$VAR1->{"mapping"}{"pl"} = $VAR1->{"mapping"}{"install"};
$VAR1->{"mapping"}{"test"} = $VAR1->{"mapping"}{"install"};
