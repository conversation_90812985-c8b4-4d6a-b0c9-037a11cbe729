<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_CIPHER-ARIA</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Algorithm-Names">Algorithm Names</a></li>
      <li><a href="#Parameters">Parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_CIPHER-ARIA - The ARIA EVP_CIPHER implementations</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for ARIA symmetric encryption using the <b>EVP_CIPHER</b> API.</p>

<h2 id="Algorithm-Names">Algorithm Names</h2>

<p>The following algorithms are available in the default provider:</p>

<dl>

<dt id="ARIA-128-CBC-ARIA-192-CBC-and-ARIA-256-CBC">&quot;ARIA-128-CBC&quot;, &quot;ARIA-192-CBC&quot; and &quot;ARIA-256-CBC&quot;</dt>
<dd>

</dd>
<dt id="ARIA-128-CFB-ARIA-192-CFB-ARIA-256-CFB-ARIA-128-CFB1-ARIA-192-CFB1-ARIA-256-CFB1-ARIA-128-CFB8-ARIA-192-CFB8-and-ARIA-256-CFB8">&quot;ARIA-128-CFB&quot;, &quot;ARIA-192-CFB&quot;, &quot;ARIA-256-CFB&quot;, &quot;ARIA-128-CFB1&quot;, &quot;ARIA-192-CFB1&quot;, &quot;ARIA-256-CFB1&quot;, &quot;ARIA-128-CFB8&quot;, &quot;ARIA-192-CFB8&quot; and &quot;ARIA-256-CFB8&quot;</dt>
<dd>

</dd>
<dt id="ARIA-128-CTR-ARIA-192-CTR-and-ARIA-256-CTR">&quot;ARIA-128-CTR&quot;, &quot;ARIA-192-CTR&quot; and &quot;ARIA-256-CTR&quot;</dt>
<dd>

</dd>
<dt id="ARIA-128-ECB-ARIA-192-ECB-and-ARIA-256-ECB">&quot;ARIA-128-ECB&quot;, &quot;ARIA-192-ECB&quot; and &quot;ARIA-256-ECB&quot;</dt>
<dd>

</dd>
<dt id="AES-192-OCB-AES-128-OCB-and-AES-256-OCB">&quot;AES-192-OCB&quot;, &quot;AES-128-OCB&quot; and &quot;AES-256-OCB&quot;</dt>
<dd>

</dd>
<dt id="ARIA-128-OFB-ARIA-192-OFB-and-ARIA-256-OFB">&quot;ARIA-128-OFB&quot;, &quot;ARIA-192-OFB&quot; and &quot;ARIA-256-OFB&quot;</dt>
<dd>

</dd>
<dt id="ARIA-128-CCM-ARIA-192-CCM-and-ARIA-256-CCM">&quot;ARIA-128-CCM&quot;, &quot;ARIA-192-CCM&quot; and &quot;ARIA-256-CCM&quot;</dt>
<dd>

</dd>
<dt id="ARIA-128-GCM-ARIA-192-GCM-and-ARIA-256-GCM">&quot;ARIA-128-GCM&quot;, &quot;ARIA-192-GCM&quot; and &quot;ARIA-256-GCM&quot;</dt>
<dd>

</dd>
</dl>

<h2 id="Parameters">Parameters</h2>

<p>This implementation supports the parameters described in <a href="../man3/EVP_EncryptInit.html">&quot;PARAMETERS&quot; in EVP_EncryptInit(3)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider-cipher.html">provider-cipher(7)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


