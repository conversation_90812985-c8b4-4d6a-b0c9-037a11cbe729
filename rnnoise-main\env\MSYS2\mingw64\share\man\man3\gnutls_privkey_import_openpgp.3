.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_import_openpgp" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_import_openpgp \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_import_openpgp(gnutls_privkey_t " pkey ", gnutls_openpgp_privkey_t " key ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t pkey" 12
The private key
.IP "gnutls_openpgp_privkey_t key" 12
The private key to be imported
.IP "unsigned int flags" 12
Flags for the import
.SH "DESCRIPTION"
This function is no\-op.
.SH "RETURNS"
\fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
