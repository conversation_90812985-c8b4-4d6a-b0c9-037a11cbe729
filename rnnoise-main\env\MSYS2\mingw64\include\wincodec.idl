/*
 * Copyright 2009 <PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "wtypes.idl";
import "propidl.idl";
import "ocidl.idl";
import "dxgiformat.idl";

cpp_quote ("#include \"dcommon.h\"")

cpp_quote("#define WINCODEC_SDK_VERSION 0x0236")

#define CODEC_FORCE_DWORD 0x7fffffff

typedef enum WICDecodeOptions {
    WICDecodeMetadataCacheOnDemand = 0x00000000,
    WICDecodeMetadataCacheOnLoad = 0x00000001,
    WICMETADATACACHEOPTION_FORCE_DWORD = CODEC_FORCE_DWORD
} WICDecodeOptions;

typedef enum WICBitmapCreateCacheOption {
    WICBitmapNoCache = 0x00000000,
    WICBitmapCacheOnDemand = 0x00000001,
    WICBitmapCacheOnLoad = 0x00000002,
    WICBITMAPCREATECACHEOPTION_FORCE_DWORD = CODEC_FORCE_DWORD
} WICBitmapCreateCacheOption;

typedef enum WICBitmapAlphaChannelOption {
    WICBitmapUseAlpha = 0x00000000,
    WICBitmapUsePremultipliedAlpha = 0x00000001,
    WICBitmapIgnoreAlpha = 0x00000002,
    WICBITMAPALPHACHANNELOPTIONS_FORCE_DWORD = CODEC_FORCE_DWORD
} WICBitmapAlphaChannelOption;

typedef enum WICBitmapDecoderCapabilities {
    WICBitmapDecoderCapabilitySameEncoder = 0x00000001,
    WICBitmapDecoderCapabilityCanDecodeAllImages = 0x00000002,
    WICBitmapDecoderCapabilityCanDecodeSomeImages = 0x00000004,
    WICBitmapDecoderCapabilityCanEnumerateMetadata = 0x00000008,
    WICBitmapDecoderCapabilityCanDecodeThumbnail = 0x00000010,
} WICBitmapDecoderCapabilities;

typedef enum WICBitmapDitherType {
    WICBitmapDitherTypeNone = 0x00000000,
    WICBitmapDitherTypeSolid = 0x00000000,
    WICBitmapDitherTypeOrdered4x4 = 0x00000001,
    WICBitmapDitherTypeOrdered8x8 = 0x00000002,
    WICBitmapDitherTypeOrdered16x16 = 0x00000003,
    WICBitmapDitherTypeSpiral4x4 = 0x00000004,
    WICBitmapDitherTypeSpiral8x8 = 0x00000005,
    WICBitmapDitherTypeDualSpiral4x4 = 0x00000006,
    WICBitmapDitherTypeDualSpiral8x8 = 0x00000007,
    WICBitmapDitherTypeErrorDiffusion = 0x00000008,
    WICBITMAPDITHERTYPE_FORCE_DWORD = CODEC_FORCE_DWORD
} WICBitmapDitherType;

typedef enum WICBitmapEncoderCacheOption {
    WICBitmapEncoderCacheInMemory = 0x00000000,
    WICBitmapEncoderCacheTempFile = 0x00000001,
    WICBitmapEncoderNoCache = 0x00000002,
    WICBITMAPENCODERCACHEOPTION_FORCE_DWORD = CODEC_FORCE_DWORD
} WICBitmapEncoderCacheOption;

typedef enum WICBitmapInterpolationMode {
    WICBitmapInterpolationModeNearestNeighbor = 0x00000000,
    WICBitmapInterpolationModeLinear = 0x00000001,
    WICBitmapInterpolationModeCubic = 0x00000002,
    WICBitmapInterpolationModeFant = 0x00000003,
    WICBITMAPINTERPOLATIONMODE_FORCE_DWORD = CODEC_FORCE_DWORD
} WICBitmapInterpolationMode;

typedef enum WICBitmapLockFlags {
    WICBitmapLockRead = 0x00000001,
    WICBitmapLockWrite = 0x00000002,
    WICBITMAPLOCKFLAGS_FORCE_DWORD = CODEC_FORCE_DWORD
} WICBitmapLockFlags;

typedef enum WICBitmapPaletteType {
    WICBitmapPaletteTypeCustom = 0x00000000,
    WICBitmapPaletteTypeMedianCut = 0x00000001,
    WICBitmapPaletteTypeFixedBW = 0x00000002,
    WICBitmapPaletteTypeFixedHalftone8 = 0x00000003,
    WICBitmapPaletteTypeFixedHalftone27 = 0x00000004,
    WICBitmapPaletteTypeFixedHalftone64 = 0x00000005,
    WICBitmapPaletteTypeFixedHalftone125 = 0x00000006,
    WICBitmapPaletteTypeFixedHalftone216 = 0x00000007,
    WICBitmapPaletteTypeFixedWebPalette = WICBitmapPaletteTypeFixedHalftone216,
    WICBitmapPaletteTypeFixedHalftone252 = 0x00000008,
    WICBitmapPaletteTypeFixedHalftone256 = 0x00000009,
    WICBitmapPaletteTypeFixedGray4 = 0x0000000A,
    WICBitmapPaletteTypeFixedGray16 = 0x0000000B,
    WICBitmapPaletteTypeFixedGray256 = 0x0000000C,
    WICBITMAPPALETTETYPE_FORCE_DWORD = CODEC_FORCE_DWORD
} WICBitmapPaletteType;

typedef enum WICBitmapTransformOptions {
    WICBitmapTransformRotate0 = 0x00000000,
    WICBitmapTransformRotate90 = 0x00000001,
    WICBitmapTransformRotate180 = 0x00000002,
    WICBitmapTransformRotate270 = 0x00000003,
    WICBitmapTransformFlipHorizontal = 0x00000008,
    WICBitmapTransformFlipVertical = 0x00000010,
    WICBITMAPTRANSFORMOPTIONS_FORCE_DWORD = CODEC_FORCE_DWORD
} WICBitmapTransformOptions;

typedef enum WICColorContextType {
    WICColorContextUninitialized = 0x00000000,
    WICColorContextProfile = 0x00000001,
    WICColorContextExifColorSpace = 0x00000002
} WICColorContextType;

typedef enum WICComponentType {
    WICDecoder = 0x00000001,
    WICEncoder = 0x00000002,
    WICPixelFormatConverter = 0x00000004,
    WICMetadataReader = 0x00000008,
    WICMetadataWriter = 0x00000010,
    WICPixelFormat = 0x00000020,
    WICCOMPONENTTYPE_FORCE_DWORD = CODEC_FORCE_DWORD
} WICComponentType;

typedef enum WICComponentSigning {
    WICComponentSigned = 0x00000001,
    WICComponentUnsigned = 0x00000002,
    WICComponentSafe = 0x00000004,
    WICComponentDisabled = 0x80000000
} WICComponentSigning;

typedef enum WICComponentEnumerateOptions {
    WICComponentEnumerateDefault = 0x00000000,
    WICComponentEnumerateRefresh = 0x00000001,
    WICComponentEnumerateBuiltInOnly = 0x20000000,
    WICComponentEnumerateUnsigned = 0x40000000,
    WICComponentEnumerateDisabled = 0x80000000
} WICComponentEnumerateOptions;

typedef enum WICJpegYCrCbSubsamplingOption {
    WICJpegYCrCbSubsamplingDefault  = 0x00000000,
    WICJpegYCrCbSubsampling420 = 0x00000001,
    WICJpegYCrCbSubsampling422 = 0x00000002,
    WICJpegYCrCbSubsampling444 = 0x00000003,
    WICJpegYCrCbSubsampling440 = 0x00000004
} WICJpegYCrCbSubsamplingOption;

typedef enum WICPixelFormatNumericRepresentation {
    WICPixelFormatNumericRepresentationUnspecified = 0x00000000,
    WICPixelFormatNumericRepresentationIndexed = 0x00000001,
    WICPixelFormatNumericRepresentationUnsignedInteger = 0x00000002,
    WICPixelFormatNumericRepresentationSignedInteger = 0x00000003,
    WICPixelFormatNumericRepresentationFixed = 0x00000004,
    WICPixelFormatNumericRepresentationFloat = 0x00000005,
    WICPIXELFORMATNUMERICREPRESENTATION_FORCE_DWORD = CODEC_FORCE_DWORD
} WICPixelFormatNumericRepresentation;

typedef enum WICTiffCompressionOption {
    WICTiffCompressionDontCare = 0x00000000,
    WICTiffCompressionNone = 0x00000001,
    WICTiffCompressionCCITT3 = 0x00000002,
    WICTiffCompressionCCITT4 = 0x00000003,
    WICTiffCompressionLZW = 0x00000004,
    WICTiffCompressionRLE = 0x00000005,
    WICTiffCompressionZIP = 0x00000006,
    WICTiffCompressionLZWHDifferencing = 0x00000007,
    WICTIFFCOMPRESSIONOPTION_FORCE_DWORD = CODEC_FORCE_DWORD
} WICTiffCompressionOption;

typedef enum WICPngFilterOption {
    WICPngFilterUnspecified = 0,
    WICPngFilterNone = 1,
    WICPngFilterSub = 2,
    WICPngFilterUp = 3,
    WICPngFilterAverage = 4,
    WICPngFilterPaeth = 5,
    WICPngFilterAdaptive = 6,
    WICPNFFILTEROPTION_FORCE_DWORD = CODEC_FORCE_DWORD
} WICPngFilterOption;

typedef enum WICSectionAccessLevel {
    WICSectionAccessLevelRead = 0x00000001,
    WICSectionAccessLevelReadWrite = 0x00000003,
    WICSectionAccessLevel_FORCE_DWORD = CODEC_FORCE_DWORD
} WICSectionAccessLevel;

typedef enum WICDdsDimension {
    WICDdsTexture1D = 0x00000000,
    WICDdsTexture2D = 0x00000001,
    WICDdsTexture3D = 0x00000002,
    WICDdsTextureCube = 0x00000003,
    WICDDSTEXTURE_FORCE_DWORD = CODEC_FORCE_DWORD
} WICDdsDimension;

typedef enum WICDdsAlphaMode {
    WICDdsAlphaModeUnknown = 0x00000000,
    WICDdsAlphaModeStraight = 0x00000001,
    WICDdsAlphaModePremultiplied = 0x00000002,
    WICDdsAlphaModeOpaque = 0x00000003,
    WICDdsAlphaModeCustom = 0x00000004,
    WICDDSALPHAMODE_FORCE_DWORD = CODEC_FORCE_DWORD
} WICDdsAlphaMode;

typedef GUID WICPixelFormatGUID;
typedef REFGUID REFWICPixelFormatGUID;

cpp_quote("DEFINE_GUID(GUID_WICPixelFormatDontCare, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x00);")
cpp_quote("#define GUID_WICPixelFormatUndefined GUID_WICPixelFormatDontCare")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat1bppIndexed, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x01);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat2bppIndexed, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x02);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat4bppIndexed, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x03);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat8bppIndexed, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x04);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormatBlackWhite, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x05);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat2bppGray, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x06);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat4bppGray, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x07);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat8bppGray, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x08);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppGray, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0b);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat8bppAlpha, 0xe6cd0116,0xeeba,0x4161,0xaa,0x85,0x27,0xdd,0x9f,0xb3,0xa8,0x95);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppBGR555, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x09);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppBGR565, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0a);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppBGRA5551, 0x05ec7c2b,0xf1e6,0x4961,0xad,0x46,0xe1,0xcc,0x81,0x0a,0x87,0xd2);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat24bppBGR, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0c);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat24bppRGB, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0d);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppBGR, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0e);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppBGRA, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0f);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppPBGRA, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x10);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppRGB, 0xd98c6b95,0x3efe,0x47d6,0xbb,0x25,0xeb,0x17,0x48,0xab,0x0c,0xf1);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppRGBA, 0xf5c7ad2d,0x6a8d,0x43dd,0xa7,0xa8,0xa2,0x99,0x35,0x26,0x1a,0xe9);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppPRGBA, 0x3cc4a650,0xa527,0x4d37,0xa9,0x16,0x31,0x42,0xc7,0xeb,0xed,0xba);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppGrayFloat, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x11);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat48bppRGB, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x15);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat48bppBGR, 0xe605a384,0xb468,0x46ce,0xbb,0x2e,0x36,0xf1,0x80,0xe6,0x43,0x13);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppRGB, 0xa1182111,0x186d,0x4d42,0xbc,0x6a,0x9c,0x83,0x03,0xa8,0xdf,0xf9);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppRGBA, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x16);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppBGRA, 0x1562ff7c,0xd352,0x46f9,0x97,0x9e,0x42,0x97,0x6b,0x79,0x22,0x46);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppPRGBA, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x17);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppPBGRA, 0x8c518e8e,0xa4ec,0x468b,0xae,0x70,0xc9,0xa3,0x5a,0x9c,0x55,0x30);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppGrayFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x13);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppBGR101010, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x14);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat48bppRGBFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x12);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat48bppBGRFixedPoint, 0x49ca140e,0xcab6,0x493b,0x9d,0xdf,0x60,0x18,0x7c,0x37,0x53,0x2a);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat96bppRGBFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x18);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat96bppRGBFloat, 0xe3fed78f,0xe8db,0x4acf,0x84,0xc1,0xe9,0x7f,0x61,0x36,0xb3,0x27);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat128bppRGBAFloat, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x19);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat128bppPRGBAFloat, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1a);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat128bppRGBFloat, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1b);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppCMYK, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1c);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppRGBAFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1d);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppBGRAFixedPoint, 0x356de33c,0x54d2,0x4a23,0xbb,0x4,0x9b,0x7b,0xf9,0xb1,0xd4,0x2d);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppRGBFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x40);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat128bppRGBAFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1e);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat128bppRGBFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x41);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppRGBAHalf, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3a);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppPRGBAHalf, 0x58ad26c2,0xc623,0x4d9d,0xb3,0x20,0x38,0x7e,0x49,0xf8,0xc4,0x42);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppRGBHalf, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x42);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat48bppRGBHalf, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3b);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppRGBE, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3d);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppGrayHalf, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3e);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppGrayFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3f);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppRGBA1010102, 0x25238d72,0xfcf9,0x4522,0xb5,0x14,0x55,0x78,0xe5,0xad,0x55,0xe0);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppRGBA1010102XR, 0x00de6b9a,0xc101,0x434b,0xb5,0x02,0xd0,0x16,0x5e,0xe1,0x12,0x2c);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppR10G10B10A2, 0x604e1bb5,0x8a3c,0x4b65,0xb1,0x1c,0xbc,0x0b,0x8d,0xd7,0x5b,0x7f);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bppR10G10B10A2HDR10, 0x9c215c5d,0x1acc,0x4f0e,0xa4,0xbc,0x70,0xfb,0x3a,0xe8,0xfd,0x28);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bppCMYK, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1f);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat24bpp3Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x20);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bpp4Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x21);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat40bpp5Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x22);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat48bpp6Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x23);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat56bpp7Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x24);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bpp8Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x25);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat48bpp3Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x26);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bpp4Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x27);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat80bpp5Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x28);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat96bpp6Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x29);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat112bpp7Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2a);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat128bpp8Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2b);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat40bppCMYKAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2c);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat80bppCMYKAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2d);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat32bpp3ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2e);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat40bpp4ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2f);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat48bpp5ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x30);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat56bpp6ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x31);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bpp7ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x32);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat72bpp8ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x33);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat64bpp3ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x34);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat80bpp4ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x35);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat96bpp5ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x36);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat112bpp6ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x37);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat128bpp7ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x38);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat144bpp8ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x39);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat8bppY, 0x91b4db54,0x2df9,0x42f0,0xb4,0x49,0x29,0x09,0xbb,0x3d,0xf8,0x8e);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat8bppCb, 0x1339f224,0x6bfe,0x4c3e,0x93,0x02,0xe4,0xf3,0xa6,0xd0,0xca,0x2a);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat8bppCr, 0xb8145053,0x2116,0x49f0,0x88,0x35,0xed,0x84,0x4b,0x20,0x5c,0x51);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppCbCr, 0xff95ba6e,0x11e0,0x4263,0xbb,0x45,0x01,0x72,0x1f,0x34,0x60,0xa4);")

cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppYQuantizedDctCoefficients, 0xa355f433,0x48e8,0x4a42,0x84,0xd8,0xe2,0xaa,0x26,0xca,0x80,0xa4);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppCbQuantizedDctCoefficients, 0xd2c4ff61,0x56a5,0x49c2,0x8b,0x5c,0x4c,0x19,0x25,0x96,0x48,0x37);")
cpp_quote("DEFINE_GUID(GUID_WICPixelFormat16bppCrQuantizedDctCoefficients, 0x2fe354f0,0x1680,0x42d8,0x92,0x31,0xe7,0x3c,0x05,0x65,0xbf,0xc1);")

cpp_quote("#if 0")
typedef DWORD *D2D1_PIXEL_FORMAT;
cpp_quote ("#endif")

typedef struct WICRect {
    INT X;
    INT Y;
    INT Width;
    INT Height;
} WICRect;

typedef struct WICBitmapPattern {
    ULARGE_INTEGER Position;
    ULONG Length;
    BYTE *Pattern;
    BYTE *Mask;
    BOOL EndOfStream;
} WICBitmapPattern;

typedef struct WICImageParameters
{
    D2D1_PIXEL_FORMAT PixelFormat;
    FLOAT DpiX;
    FLOAT DpiY;
    FLOAT Top;
    FLOAT Left;
    UINT32 PixelWidth;
    UINT32 PixelHeight;
} WICImageParameters;

typedef struct WICDdsParameters {
    UINT Width;
    UINT Height;
    UINT Depth;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT DxgiFormat;
    WICDdsDimension Dimension;
    WICDdsAlphaMode AlphaMode;
} WICDdsParameters;

typedef struct WICDdsFormatInfo {
    DXGI_FORMAT DxgiFormat;
    UINT BytesPerBlock;
    UINT BlockWidth;
    UINT BlockHeight;
} WICDdsFormatInfo;

typedef UINT32 WICColor;

interface ID2D1Device;
interface ID2D1Image;
interface IWICPalette;

[
    object,
    uuid(3c613a02-34b2-44ea-9a7c-45aea9c6fd6d)
]
interface IWICColorContext : IUnknown
{
    HRESULT InitializeFromFilename(
        [in] LPCWSTR wzFilename);

    HRESULT InitializeFromMemory(
        [in, size_is(cbBufferSize)] const BYTE *pbBuffer,
        [in] UINT cbBufferSize);

    HRESULT InitializeFromExifColorSpace(
        [in] UINT value);

    HRESULT GetType(
        [out] WICColorContextType *pType);

    HRESULT GetProfileBytes(
        [in] UINT cbBuffer,
        [in, out, unique, size_is(cbBuffer)] BYTE *pbBuffer,
        [out] UINT *pcbActual);

    HRESULT GetExifColorSpace(
        [out] UINT *pValue);
}

[
    object,
    uuid(00000120-a8f2-4877-ba0a-fd2b6645fb94)
]
interface IWICBitmapSource : IUnknown
{
    HRESULT GetSize(
        [out] UINT *puiWidth,
        [out] UINT *puiHeight);

    HRESULT GetPixelFormat(
        [out] WICPixelFormatGUID *pPixelFormat);

    HRESULT GetResolution(
        [out] double *pDpiX,
        [out] double *pDpiY);

    HRESULT CopyPalette(
        [in] IWICPalette *pIPalette);

    HRESULT CopyPixels(
        [in] const WICRect *prc,
        [in] UINT cbStride,
        [in] UINT cbBufferSize,
        [out, size_is(cbBufferSize)] BYTE *pbBuffer);
}

[
    object,
    uuid(00000123-a8f2-4877-ba0a-fd2b6645fb94)
]
interface IWICBitmapLock : IUnknown
{
    HRESULT GetSize(
        [out] UINT *pWidth,
        [out] UINT *pHeight);

    HRESULT GetStride(
        [out] UINT *pcbStride);

    HRESULT GetDataPointer(
        [out] UINT *pcbBufferSize,
        [out, size_is(,*pcbBufferSize)] BYTE **ppbData);

    HRESULT GetPixelFormat(
        [out] WICPixelFormatGUID *pPixelFormat);
}

[
    object,
    uuid(5009834f-2d6a-41ce-9e1b-17c5aff7a782)
]
interface IWICBitmapFlipRotator : IWICBitmapSource
{
    HRESULT Initialize(
        [in] IWICBitmapSource *pISource,
        [in] WICBitmapTransformOptions options);
}

[
    object,
    uuid(00000121-a8f2-4877-ba0a-fd2b6645fb94)
]
interface IWICBitmap : IWICBitmapSource
{
    HRESULT Lock(
        [in] const WICRect *prcLock,
        [in] DWORD flags,
        [out] IWICBitmapLock **ppILock);

    HRESULT SetPalette(
        [in] IWICPalette *pIPalette);

    HRESULT SetResolution(
        [in] double dpiX,
        [in] double dpiY);
}

[
    object,
    uuid(00000040-a8f2-4877-ba0a-fd2b6645fb94)
]
interface IWICPalette : IUnknown
{
    HRESULT InitializePredefined(
        [in] WICBitmapPaletteType ePaletteType,
        [in] BOOL fAddTransparentColor);

    HRESULT InitializeCustom(
        [in, size_is(colorCount)] WICColor *pColors,
        [in] UINT colorCount);

    HRESULT InitializeFromBitmap(
        [in] IWICBitmapSource *pISurface,
        [in] UINT colorCount,
        [in] BOOL fAddTransparentColor);

    HRESULT InitializeFromPalette(
        [in] IWICPalette *pIPalette);

    HRESULT GetType(
        [out] WICBitmapPaletteType *pePaletteType);

    HRESULT GetColorCount(
        [out] UINT *pcCount);

    HRESULT GetColors(
        [in] UINT colorCount,
        [out, size_is(colorCount)] WICColor *pColors,
        [out] UINT *pcActualColors);

    HRESULT IsBlackWhite(
        [out] BOOL *pfIsBlackWhite);

    HRESULT IsGrayscale(
        [out] BOOL *pfIsGrayscale);

    HRESULT HasAlpha(
        [out] BOOL *pfHasAlpha);
}

[
    object,
    uuid(23bc3f0a-698b-4357-886b-f24d50671334)
]
interface IWICComponentInfo : IUnknown
{
    HRESULT GetComponentType(
        [out] WICComponentType *pType);

    HRESULT GetCLSID(
        [out] CLSID *pclsid);

    HRESULT GetSigningStatus(
        [out] DWORD *pStatus);

    HRESULT GetAuthor(
        [in] UINT cchAuthor,
        [in, out, unique, size_is(cchAuthor)] WCHAR *wzAuthor,
        [out] UINT *pcchActual);

    HRESULT GetVendorGUID(
        [out] GUID *pguidVendor);

    HRESULT GetVersion(
        [in] UINT cchVersion,
        [in, out, unique, size_is(cchVersion)] WCHAR *wzVersion,
        [out] UINT *pcchActual);

    HRESULT GetSpecVersion(
        [in] UINT cchSpecVersion,
        [in, out, unique, size_is(cchSpecVersion)] WCHAR *wzSpecVersion,
        [out] UINT *pcchActual);

    HRESULT GetFriendlyName(
        [in] UINT cchFriendlyName,
        [in, out, unique, size_is(cchFriendlyName)] WCHAR *wzFriendlyName,
        [out] UINT *pcchActual);
}

[
    object,
    uuid(30989668-e1c9-4597-b395-458eedb808df)
]
interface IWICMetadataQueryReader : IUnknown
{
    HRESULT GetContainerFormat(
        [out] GUID *pguidContainerFormat);

    HRESULT GetLocation(
        [in] UINT cchMaxLength,
        [in, out, unique, size_is(cchMaxLength)] WCHAR *wzNamespace,
        [out] UINT *pcchActualLength);

    HRESULT GetMetadataByName(
        [in] LPCWSTR wzName,
        [in, out, unique] PROPVARIANT *pvarValue);

    HRESULT GetEnumerator(
        [out] IEnumString **ppIEnumString);
}

[
    object,
    uuid(a721791a-0def-4d06-bd91-2118bf1db10b)
]
interface IWICMetadataQueryWriter : IWICMetadataQueryReader
{
    HRESULT SetMetadataByName(
        [in] LPCWSTR wzName,
        [in] const PROPVARIANT *pvarValue);

    HRESULT RemoveMetadataByName(
        [in] LPCWSTR wzName);
}

[
    object,
    uuid(3b16811b-6a43-4ec9-a813-3d930c13b940)
]
interface IWICBitmapFrameDecode : IWICBitmapSource
{
    HRESULT GetMetadataQueryReader(
        [out] IWICMetadataQueryReader **ppIMetadataQueryReader);

    HRESULT GetColorContexts(
        [in] UINT cCount,
        [in, out, unique, size_is(cCount)] IWICColorContext **ppIColorContexts,
        [out] UINT *pcActualCount);

    HRESULT GetThumbnail(
        [out] IWICBitmapSource **ppIThumbnail);
}

[
    object,
    uuid(e8eda601-3d48-431a-ab44-69059be88bbe)
]
interface IWICPixelFormatInfo : IWICComponentInfo
{
    HRESULT GetFormatGUID(
        [out] GUID* pFormat);

    HRESULT GetColorContext(
        [out] IWICColorContext **ppIColorContext);

    HRESULT GetBitsPerPixel(
        [out] UINT *puiBitsPerPixel);

    HRESULT GetChannelCount(
        [out] UINT *puiChannelCount);

    HRESULT GetChannelMask(
        [in] UINT uiChannelIndex,
        [in] UINT cbMaskBuffer,
        [in, out, unique, size_is(cbMaskBuffer)] BYTE *pbMaskBuffer,
        [out] UINT *pcbActual);
}

[
    object,
    uuid(a9db33a2-af5f-43c7-b679-74f5984b5aa4)
]
interface IWICPixelFormatInfo2 : IWICPixelFormatInfo
{
    HRESULT SupportsTransparency(
        [out] BOOL *pfSupportsTransparency);

    HRESULT GetNumericRepresentation(
        [out] WICPixelFormatNumericRepresentation *pNumericRepresentation);
}

[
    object,
    uuid(e87a44c4-b76e-4c47-8b09-298eb12a2714)
]
interface IWICBitmapCodecInfo : IWICComponentInfo
{
    HRESULT GetContainerFormat(
        [out] GUID *pguidContainerFormat);

    HRESULT GetPixelFormats(
        [in] UINT cFormats,
        [in, out, unique, size_is(cFormats)] GUID *pguidPixelFormats,
        [out] UINT *pcActual);

    HRESULT GetColorManagementVersion(
        [in] UINT cchColorManagementVersion,
        [in, out, unique, size_is(cchColorManagementVersion)] WCHAR *wzColorManagementVersion,
        [out] UINT *pcchActual);

    HRESULT GetDeviceManufacturer(
        [in] UINT cchDeviceManufacturer,
        [in, out, unique, size_is(cchDeviceManufacturer)] WCHAR *wzDeviceManufacturer,
        [out] UINT *pcchActual);

    HRESULT GetDeviceModels(
        [in] UINT cchDeviceModels,
        [in, out, unique, size_is(cchDeviceModels)] WCHAR *wzDeviceModels,
        [out] UINT *pcchActual);

    HRESULT GetMimeTypes(
        [in] UINT cchMimeTypes,
        [in, out, unique, size_is(cchMimeTypes)] WCHAR *wzMimeTypes,
        [out] UINT *pcchActual);

    HRESULT GetFileExtensions(
        [in] UINT cchFileExtensions,
        [in, out, unique, size_is(cchFileExtensions)] WCHAR *wzFileExtensions,
        [out] UINT *pcchActual);

    HRESULT DoesSupportAnimation(
        [out] BOOL *pfSupportAnimation);

    HRESULT DoesSupportChromaKey(
        [out] BOOL *pfSupportChromaKey);

    HRESULT DoesSupportLossless(
        [out] BOOL *pfSupportLossless);

    HRESULT DoesSupportMultiframe(
        [out] BOOL *pfSupportMultiframe);

    HRESULT MatchesMimeType(
        [in] LPCWSTR wzMimeType,
        [out] BOOL *pfMatches);
}

interface IWICBitmapDecoder;

[
    object,
    uuid(d8cd007f-d08f-4191-9bfc-236ea7f0e4b5)
]
interface IWICBitmapDecoderInfo : IWICBitmapCodecInfo
{
    [local]
    HRESULT GetPatterns(
        [in] UINT cbSizePatterns,
        [in, out, unique] WICBitmapPattern *pPatterns,
        [in, out, unique] UINT *pcPatterns,
        [in, out, unique] UINT *pcbPatternsActual);

    HRESULT MatchesPattern(
        [in] IStream *pIStream,
        [out] BOOL *pfMatches);

    HRESULT CreateInstance(
        [out] IWICBitmapDecoder **ppIBitmapDecoder);
}

[
    object,
    uuid(9edde9e7-8dee-47ea-99df-e6faf2ed44bf)
]
interface IWICBitmapDecoder : IUnknown
{
    HRESULT QueryCapability(
        [in] IStream *pIStream,
        [out] DWORD *pdwCapability);

    HRESULT Initialize(
        [in] IStream *pIStream,
        [in] WICDecodeOptions cacheOptions);

    HRESULT GetContainerFormat(
        [out] GUID *pguidContainerFormat);

    HRESULT GetDecoderInfo(
        [out] IWICBitmapDecoderInfo **ppIDecoderInfo);

    HRESULT CopyPalette(
        [in] IWICPalette *pIPalette);

    HRESULT GetMetadataQueryReader(
        [out] IWICMetadataQueryReader **ppIMetadataQueryReader);

    HRESULT GetPreview(
        [out] IWICBitmapSource **ppIBitmapSource);

    HRESULT GetColorContexts(
        [in] UINT cCount,
        [in, out, unique, size_is(cCount)] IWICColorContext **ppIColorContexts,
        [out] UINT *pcActualCount);

    HRESULT GetThumbnail(
        [out] IWICBitmapSource **ppIThumbnail);

    HRESULT GetFrameCount(
        [out] UINT *pCount);

    HRESULT GetFrame(
        [in] UINT index,
        [out] IWICBitmapFrameDecode **ppIBitmapFrame);
}

[
    object,
    uuid(00000105-a8f2-4877-ba0a-fd2b6645fb94)
]
interface IWICBitmapFrameEncode : IUnknown
{
    HRESULT Initialize(
        [in, unique] IPropertyBag2 *pIEncoderOptions);

    HRESULT SetSize(
        [in] UINT uiWidth,
        [in] UINT uiHeight);

    HRESULT SetResolution(
        [in] double dpiX,
        [in] double dpiY);

    HRESULT SetPixelFormat(
        [in, out] WICPixelFormatGUID *pPixelFormat);

    HRESULT SetColorContexts(
        [in] UINT cCount,
        [in, size_is(cCount)] IWICColorContext **ppIColorContext);

    HRESULT SetPalette(
        [in] IWICPalette *pIPalette);

    HRESULT SetThumbnail(
        [in] IWICBitmapSource *pIThumbnail);

    HRESULT WritePixels(
        [in] UINT lineCount,
        [in] UINT cbStride,
        [in] UINT cbBufferSize,
        [in, size_is(cbBufferSize)] BYTE *pbPixels);

    HRESULT WriteSource(
        [in] IWICBitmapSource *pIBitmapSource,
        [in, unique] WICRect *prc);

    HRESULT Commit();

    HRESULT GetMetadataQueryWriter(
        IWICMetadataQueryWriter **ppIMetadataQueryWriter);
}

interface IWICBitmapEncoder;

[
    object,
    uuid(94c9b4ee-a09f-4f92-8a1e-4a9bce7e76fb)
]
interface IWICBitmapEncoderInfo : IWICBitmapCodecInfo
{
    HRESULT CreateInstance(
        [out] IWICBitmapEncoder **ppIBitmapEncoder);
}

[
    object,
    uuid(00000103-a8f2-4877-ba0a-fd2b6645fb94)
]
interface IWICBitmapEncoder : IUnknown
{
    HRESULT Initialize(
        [in] IStream *pIStream,
        [in] WICBitmapEncoderCacheOption cacheOption);

    HRESULT GetContainerFormat(
        [out] GUID *pguidContainerFormat);

    HRESULT GetEncoderInfo(
        [out] IWICBitmapEncoderInfo **ppIEncoderInfo);

    HRESULT SetColorContexts(
        [in] UINT cCount,
        [in, size_is(cCount)] IWICColorContext **ppIColorContext);

    HRESULT SetPalette(
        [in] IWICPalette *pIPalette);

    HRESULT SetThumbnail(
        [in] IWICBitmapSource *pIThumbnail);

    HRESULT SetPreview(
        [in] IWICBitmapSource *pIPreview);

    HRESULT CreateNewFrame(
        [out] IWICBitmapFrameEncode **ppIFrameEncode,
        [in, out, unique] IPropertyBag2 **ppIEncoderOptions);

    HRESULT Commit();

    HRESULT GetMetadataQueryWriter(
        [out] IWICMetadataQueryWriter **ppIMetadataQueryWriter);
}

[
    object,
    uuid(00000301-a8f2-4877-ba0a-fd2b6645fb94)
]
interface IWICFormatConverter : IWICBitmapSource
{
    HRESULT Initialize(
        [in] IWICBitmapSource *pISource,
        [in] REFWICPixelFormatGUID dstFormat,
        [in] WICBitmapDitherType dither,
        [in] IWICPalette *pIPalette,
        [in] double alphaThresholdPercent,
        [in] WICBitmapPaletteType paletteTranslate);

    HRESULT CanConvert(
        [in] REFWICPixelFormatGUID srcPixelFormat,
        [in] REFWICPixelFormatGUID dstPixelFormat,
        [out] BOOL *pfCanConvert);
}

[
    object,
    uuid(9f34fb65-13f4-4f15-bc57-3726b5e53d9f)
]
interface IWICFormatConverterInfo : IWICComponentInfo
{
    HRESULT GetPixelFormats(
        [in] UINT cFormats,
        [in, out, size_is(cFormats)] WICPixelFormatGUID *pPixelFormatGUIDs,
        [out] UINT *pcActual);

    HRESULT CreateInstance(
        [out] IWICFormatConverter **ppIConverter);
}

[
    object,
    uuid(135ff860-22b7-4ddf-b0f6-218f4f299a43)
]
interface IWICStream : IStream
{
    HRESULT InitializeFromIStream(
        [in] IStream *pIStream);

    HRESULT InitializeFromFilename(
        [in] LPCWSTR wzFileName,
        [in] DWORD dwAccessMode);

    HRESULT InitializeFromMemory(
        [in, size_is(cbBufferSize)] BYTE *pbBuffer,
        [in] DWORD cbBufferSize);

    HRESULT InitializeFromIStreamRegion(
        [in] IStream *pIStream,
        [in] ULARGE_INTEGER ulOffset,
        [in] ULARGE_INTEGER ulMaxSize);
}

[
    object,
    uuid(00000302-a8f2-4877-ba0a-fd2b6645fb94)
]
interface IWICBitmapScaler : IWICBitmapSource
{
    HRESULT Initialize(
        [in] IWICBitmapSource *pISource,
        [in] UINT uiWidth,
        [in] UINT uiHeight,
        [in] WICBitmapInterpolationMode mode);
}

[
    object,
    uuid(e4fbcf03-223d-4e81-9333-d635556dd1b5)
]
interface IWICBitmapClipper : IWICBitmapSource
{
    HRESULT Initialize(
        [in] IWICBitmapSource *pISource,
        [in] const WICRect *prc);
}

[
    object,
    uuid(b66f034f-d0e2-40ab-b436-6de39e321a94)
]
interface IWICColorTransform : IWICBitmapSource
{
    HRESULT Initialize(
        [in] IWICBitmapSource *pIBitmapSource,
        [in] IWICColorContext *pIContextSource,
        [in] IWICColorContext *pIContextDest,
        [in] REFWICPixelFormatGUID pixelFmtDest);
}

[
    object,
    uuid(b84e2c09-78c9-4ac4-8bd3-524ae1663a2f)
]
interface IWICFastMetadataEncoder : IUnknown
{
    HRESULT Commit();

    HRESULT GetMetadataQueryWriter(
        [out] IWICMetadataQueryWriter **ppIMetadataQueryWriter);
}

[
    object,
    uuid(04c75bf8-3ce1-473b-acc5-3cc4f5e94999),
    local
]
interface IWICImageEncoder : IUnknown
{
    HRESULT WriteFrame(
        [in] ID2D1Image *image,
        [in] IWICBitmapFrameEncode *encode,
        [in, unique] const WICImageParameters *parameters);

    HRESULT WriteFrameThumbnail(
        [in] ID2D1Image *image,
        [in] IWICBitmapFrameEncode *encode,
        [in, unique] const WICImageParameters *parameters);

    HRESULT WriteThumbnail(
        [in] ID2D1Image *image,
        [in] IWICBitmapEncoder *encoder,
        [in, unique] const WICImageParameters *parameters);
}

cpp_quote("DEFINE_GUID(CLSID_WICImagingFactory,  0xcacaf262,0x9370,0x4615,0xa1,0x3b,0x9f,0x55,0x39,0xda,0x4c,0x0a);")
cpp_quote("DEFINE_GUID(CLSID_WICImagingFactory1, 0xcacaf262,0x9370,0x4615,0xa1,0x3b,0x9f,0x55,0x39,0xda,0x4c,0x0a);")
cpp_quote("DEFINE_GUID(CLSID_WICImagingFactory2, 0x317d06e8,0x5f24,0x433d,0xbd,0xf7,0x79,0xce,0x68,0xd8,0xab,0xc2);")

[
    object,
    uuid(ec5ec8a9-c395-4314-9c77-54d7a935ff70)
]
interface IWICImagingFactory : IUnknown
{
    HRESULT CreateDecoderFromFilename(
        [in] LPCWSTR wzFilename,
        [in, unique] const GUID *pguidVendor,
        [in] DWORD dwDesiredAccess,
        [in] WICDecodeOptions metadataOptions,
        [out, retval] IWICBitmapDecoder **ppIDecoder);

    HRESULT CreateDecoderFromStream(
        [in] IStream *pIStream,
        [in, unique] const GUID *pguidVendor,
        [in] WICDecodeOptions metadataOptions,
        [out, retval] IWICBitmapDecoder **ppIDecoder);

    HRESULT CreateDecoderFromFileHandle(
        [in] ULONG_PTR hFile,
        [in, unique] const GUID *pguidVendor,
        [in] WICDecodeOptions metadataOptions,
        [out, retval] IWICBitmapDecoder **ppIDecoder);

    HRESULT CreateComponentInfo(
        [in] REFCLSID clsidComponent,
        [out] IWICComponentInfo **ppIInfo);

    HRESULT CreateDecoder(
        [in] REFGUID guidContainerFormat,
        [in, unique] const GUID *pguidVendor,
        [out, retval] IWICBitmapDecoder **ppIDecoder);

    HRESULT CreateEncoder(
        [in] REFGUID guidContainerFormat,
        [in, unique] const GUID *pguidVendor,
        [out, retval] IWICBitmapEncoder **ppIEncoder);

    HRESULT CreatePalette(
        [out] IWICPalette **ppIPalette);

    HRESULT CreateFormatConverter(
        [out] IWICFormatConverter **ppIFormatConverter);

    HRESULT CreateBitmapScaler(
        [out] IWICBitmapScaler **ppIBitmapScaler);

    HRESULT CreateBitmapClipper(
        [out] IWICBitmapClipper **ppIBitmapClipper);

    HRESULT CreateBitmapFlipRotator(
        [out] IWICBitmapFlipRotator **ppIBitmapFlipRotator);

    HRESULT CreateStream(
        [out] IWICStream **ppIWICStream);

    HRESULT CreateColorContext(
        [out] IWICColorContext **ppIWICColorContext);

    HRESULT CreateColorTransformer(
        [out] IWICColorTransform **ppIWICColorTransform);

    HRESULT CreateBitmap(
        [in] UINT uiWidth,
        [in] UINT uiHeight,
        [in] REFWICPixelFormatGUID pixelFormat,
        [in] WICBitmapCreateCacheOption option,
        [out] IWICBitmap **ppIBitmap);

    HRESULT CreateBitmapFromSource(
        [in] IWICBitmapSource *piBitmapSource,
        [in] WICBitmapCreateCacheOption option,
        [out] IWICBitmap **ppIBitmap);

    HRESULT CreateBitmapFromSourceRect(
        [in] IWICBitmapSource *piBitmapSource,
        [in] UINT x,
        [in] UINT y,
        [in] UINT width,
        [in] UINT height,
        [out] IWICBitmap **ppIBitmap);

    HRESULT CreateBitmapFromMemory(
        [in] UINT uiWidth,
        [in] UINT uiHeight,
        [in] REFWICPixelFormatGUID pixelFormat,
        [in] UINT cbStride,
        [in] UINT cbBufferSize,
        [in, size_is(cbBufferSize)] BYTE *pbBuffer,
        [out] IWICBitmap **ppIBitmap);

    HRESULT CreateBitmapFromHBITMAP(
        [in] HBITMAP hBitmap,
        [in, unique] HPALETTE hPalette,
        [in] WICBitmapAlphaChannelOption options,
        [out] IWICBitmap **ppIBitmap);

    HRESULT CreateBitmapFromHICON(
        [in] HICON hIcon,
        [out] IWICBitmap **ppIBitmap);

    HRESULT CreateComponentEnumerator(
        [in] DWORD componentTypes,
        [in] DWORD options,
        [out] IEnumUnknown **ppIEnumUnknown);

    HRESULT CreateFastMetadataEncoderFromDecoder(
        [in] IWICBitmapDecoder *pIDecoder,
        [out] IWICFastMetadataEncoder **ppIFastEncoder);

    HRESULT CreateFastMetadataEncoderFromFrameDecode(
        [in] IWICBitmapFrameDecode *pIFrameDecoder,
        [out] IWICFastMetadataEncoder **ppIFastEncoder);

    HRESULT CreateQueryWriter(
        [in] REFGUID guidMetadataFormat,
        [in, unique] const GUID *pguidVendor,
        [out] IWICMetadataQueryWriter **ppIQueryWriter);

    HRESULT CreateQueryWriterFromReader(
        [in] IWICMetadataQueryReader *pIQueryReader,
        [in, unique] const GUID *pguidVendor,
        [out] IWICMetadataQueryWriter **ppIQueryWriter);
}

[
   object,
   uuid(7b816b45-1996-4476-b132-de9e247c8af0),
   local
]
interface IWICImagingFactory2 : IWICImagingFactory
{
    HRESULT CreateImageEncoder(
        [in] ID2D1Device *device,
        [out] IWICImageEncoder **encoder);
}

[
    local,
    object,
    uuid(dc2bb46d-3f07-481e-8625-220c4aedbb33)
]
interface IWICEnumMetadataItem : IUnknown
{
    HRESULT Next(
        [in] ULONG celt,
        [in, out, unique, size_is(celt)] PROPVARIANT *rgeltSchema,
        [in, out, size_is(celt)] PROPVARIANT *rgeltId,
        [in, out, optional, size_is(celt)] PROPVARIANT *rgeltValue,
        [out, optional] ULONG *pceltFetched);

    HRESULT Skip(
        [in] ULONG celt);

    HRESULT Reset();

    HRESULT Clone(
        [out] IWICEnumMetadataItem **ppIEnumMetadataItem);
}

[
    object,
    uuid(409cd537-8532-40cb-9774-e2feb2df4e9c)
]
interface IWICDdsDecoder : IUnknown
{
    HRESULT GetParameters(
        [out] WICDdsParameters *parameters);

    HRESULT GetFrame(
        [in] UINT arrayIndex,
        [in] UINT mipLevel,
        [in] UINT sliceIndex,
        [out, retval] IWICBitmapFrameDecode **bitmapFrame);
};

[
    object,
    uuid(5cacdb4c-407e-41b3-b936-d0f010cd6732)
]
interface IWICDdsEncoder : IUnknown
{
    HRESULT SetParameters(
        [in] WICDdsParameters *parameters);

    HRESULT GetParameters(
        [out] WICDdsParameters *parameters);

    HRESULT CreateNewFrame(
        [out] IWICBitmapFrameEncode **frameEncode,
        [out, optional] UINT *arrayIndex,
        [out, optional] UINT *mipLevel,
        [out, optional] UINT *sliceIndex);
};

[
    object,
    uuid(3d4c0c61-18a4-41e4-bd80-481a4fc9f464)
]
interface IWICDdsFrameDecode : IUnknown
{
    HRESULT GetSizeInBlocks(
        [out] UINT *widthInBlocks,
        [out] UINT *heightInBlocks);

    HRESULT GetFormatInfo(
        [out] WICDdsFormatInfo *formatInfo);

    HRESULT CopyBlocks(
        [in, unique] const WICRect *boundsInBlocks,
        [in] UINT stride,
        [in] UINT bufferSize,
        [out, size_is(bufferSize)] BYTE *buffer);
};

[
    object,
    uuid(b9bd430d-28a8-41d3-a1f5-f36ee02840bf)
]
interface IWICWineDecoder : IUnknown
{
    HRESULT Initialize(
        [in] IStream *stream,
        [in] WICDecodeOptions options);
};

cpp_quote("HRESULT WINAPI WICConvertBitmapSource(REFWICPixelFormatGUID dstFormat, IWICBitmapSource *pISrc, IWICBitmapSource **ppIDst);")
cpp_quote("HRESULT WINAPI WICCreateBitmapFromSection(UINT width, UINT height, REFWICPixelFormatGUID format, HANDLE section, UINT stride, UINT offset, IWICBitmap **bitmap);")
cpp_quote("HRESULT WINAPI WICCreateBitmapFromSectionEx(UINT width, UINT height, REFWICPixelFormatGUID format, HANDLE section, UINT stride, UINT offset, WICSectionAccessLevel access, IWICBitmap **bitmap);")

cpp_quote("HRESULT WINAPI WICMapGuidToShortName(REFGUID,UINT,WCHAR *,UINT *);")
cpp_quote("HRESULT WINAPI WICMapShortNameToGuid(PCWSTR,GUID *);")
cpp_quote("HRESULT WINAPI WICMapSchemaToName(REFGUID,LPWSTR,UINT,WCHAR *,UINT *);")

cpp_quote("DEFINE_GUID(CLSID_WICBmpDecoder, 0x6b462062,0x7cbf,0x400d,0x9f,0xdb,0x81,0x3d,0xd1,0x0f,0x27,0x78);")
cpp_quote("DEFINE_GUID(CLSID_WICPngDecoder, 0x389ea17b,0x5078,0x4cde,0xb6,0xef,0x25,0xc1,0x51,0x75,0xc7,0x51);")
cpp_quote("DEFINE_GUID(CLSID_WICPngDecoder1, 0x389ea17b,0x5078,0x4cde,0xb6,0xef,0x25,0xc1,0x51,0x75,0xc7,0x51);")
cpp_quote("DEFINE_GUID(CLSID_WICPngDecoder2, 0xe018945b,0xaa86,0x4008,0x9b,0xd4,0x67,0x77,0xa1,0xe4,0x0c,0x11);")
cpp_quote("DEFINE_GUID(CLSID_WICIcoDecoder, 0xc61bfcdf,0x2e0f,0x4aad,0xa8,0xd7,0xe0,0x6b,0xaf,0xeb,0xcd,0xfe);")
cpp_quote("DEFINE_GUID(CLSID_WICJpegDecoder, 0x9456a480,0xe88b,0x43ea,0x9e,0x73,0x0b,0x2d,0x9b,0x71,0xb1,0xca);")
cpp_quote("DEFINE_GUID(CLSID_WICGifDecoder, 0x381dda3c,0x9ce9,0x4834,0xa2,0x3e,0x1f,0x98,0xf8,0xfc,0x52,0xbe);")
cpp_quote("DEFINE_GUID(CLSID_WICTiffDecoder, 0xb54e85d9,0xfe23,0x499f,0x8b,0x88,0x6a,0xce,0xa7,0x13,0x75,0x2b);")
cpp_quote("DEFINE_GUID(CLSID_WICWmpDecoder, 0xa26cec36,0x234c,0x4950,0xae,0x16,0xe3,0x4a,0xac,0xe7,0x1d,0x0d);")
cpp_quote("DEFINE_GUID(CLSID_WICDdsDecoder, 0x9053699f,0xa341,0x429d,0x9e,0x90,0xee,0x43,0x7c,0xf8,0x0c,0x73);")

cpp_quote("DEFINE_GUID(CLSID_WICBmpEncoder, 0x69be8bb4,0xd66d,0x47c8,0x86,0x5a,0xed,0x15,0x89,0x43,0x37,0x82);")
cpp_quote("DEFINE_GUID(CLSID_WICPngEncoder, 0x27949969,0x876a,0x41d7,0x94,0x47,0x56,0x8f,0x6a,0x35,0xa4,0xdc);")
cpp_quote("DEFINE_GUID(CLSID_WICJpegEncoder, 0x1a34f5c1,0x4a5a,0x46dc,0xb6,0x44,0x1f,0x45,0x67,0xe7,0xa6,0x76);")
cpp_quote("DEFINE_GUID(CLSID_WICGifEncoder, 0x114f5598,0x0b22,0x40a0,0x86,0xa1,0xc8,0x3e,0xa4,0x95,0xad,0xbd);")
cpp_quote("DEFINE_GUID(CLSID_WICTiffEncoder, 0x0131be10,0x2001,0x4c5f,0xa9,0xb0,0xcc,0x88,0xfa,0xb6,0x4c,0xe8);")
cpp_quote("DEFINE_GUID(CLSID_WICWmpEncoder, 0xac4ce3cb,0xe1c1,0x44cd,0x82,0x15,0x5a,0x16,0x65,0x50,0x9e,0xc2);")
cpp_quote("DEFINE_GUID(CLSID_WICDdsEncoder, 0xa61dde94,0x66ce,0x4ac1,0x88,0x1b,0x71,0x68,0x05,0x88,0x89,0x5e);")
cpp_quote("DEFINE_GUID(CLSID_WICAdngDecoder, 0x981d9411,0x909e,0x42a7,0x8f,0x5d,0xa7,0x47,0xff,0x05,0x2e,0xdb);")

cpp_quote("DEFINE_GUID(CLSID_WICJpegQualcommPhoneEncoder, 0x68ed5c62,0xf534,0x4979,0xb2,0xb3,0x68,0x6a,0x12,0xb2,0xb3,0x4c);")

cpp_quote("DEFINE_GUID(CLSID_WICHeifDecoder, 0xe9a4a80a,0x44fe,0x4de4,0x89,0x71,0x71,0x50,0xb1,0x0a,0x51,0x99);")
cpp_quote("DEFINE_GUID(CLSID_WICHeifEncoder, 0x0dbecec1,0x9eb3,0x4860,0x9c,0x6f,0xdd,0xbe,0x86,0x63,0x45,0x75);")

cpp_quote("DEFINE_GUID(CLSID_WICWebpDecoder, 0x7693e886,0x51c9,0x4070,0x84,0x19,0x9f,0x70,0x73,0x8e,0xc8,0xfa);")
cpp_quote("DEFINE_GUID(CLSID_WICRAWDecoder, 0x41945702,0x8302,0x44a6,0x94,0x45,0xac,0x98,0xe8,0xaf,0xa0,0x86);")

cpp_quote("DEFINE_GUID(CLSID_WICDefaultFormatConverter, 0x1a3f11dc,0xb514,0x4b17,0x8c,0x5f,0x21,0x54,0x51,0x38,0x52,0xf1);")
cpp_quote("DEFINE_GUID(CLSID_WICFormatConverterHighColor, 0xac75d454,0x9f37,0x48f8,0xb9,0x72,0x4e,0x19,0xbc,0x85,0x60,0x11);")
cpp_quote("DEFINE_GUID(CLSID_WICFormatConverterNChannel, 0xc17cabb2,0xd4a3,0x47d7,0xa5,0x57,0x33,0x9b,0x2e,0xfb,0xd4,0xf1);")
cpp_quote("DEFINE_GUID(CLSID_WICFormatConverterWMPhoto, 0x9cb5172b,0xd600,0x46ba,0xab,0x77,0x77,0xbb,0x7e,0x3a,0x00,0xd9);")
cpp_quote("DEFINE_GUID(CLSID_WICPlanarFormatConverter, 0x184132b8,0x32f8,0x4784,0x91,0x31,0xdd,0x72,0x24,0xb2,0x34,0x38);")

cpp_quote("DEFINE_GUID(GUID_ContainerFormatBmp, 0x0af1d87e,0xfcfe,0x4188,0xbd,0xeb,0xa7,0x90,0x64,0x71,0xcb,0xe3);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatPng, 0x1b7cfaf4,0x713f,0x473c,0xbb,0xcd,0x61,0x37,0x42,0x5f,0xae,0xaf);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatIco, 0xa3a860c4,0x338f,0x4c17,0x91,0x9a,0xfb,0xa4,0xb5,0x62,0x8f,0x21);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatJpeg, 0x19e4a5aa,0x5662,0x4fc5,0xa0,0xc0,0x17,0x58,0x02,0x8e,0x10,0x57);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatTiff, 0x163bcc30,0xe2e9,0x4f0b,0x96,0x1d,0xa3,0xe9,0xfd,0xb7,0x88,0xa3);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatGif, 0x1f8a5601,0x7d4d,0x4cbd,0x9c,0x82,0x1b,0xc8,0xd4,0xee,0xb9,0xa5);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatWmp, 0x57a37caa,0x367a,0x4540,0x91,0x6b,0xf1,0x83,0xc5,0x09,0x3a,0x4b);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatDds, 0x9967cb95,0x2e85,0x4ac8,0x8c,0xa2,0x83,0xd7,0xcc,0xd4,0x25,0xc9);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatAdng, 0xf3ff6d0d,0x38c0,0x41c4,0xb1,0xfe,0x1f,0x38,0x24,0xf1,0x7b,0x84);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatHeif, 0xe1e62521,0x6787,0x405b,0xa3,0x39,0x50,0x07,0x15,0xb5,0x76,0x3f);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatWebp, 0xe094b0e2,0x67f2,0x45b3,0xb0,0xea,0x11,0x53,0x37,0xca,0x7c,0xf3);")
cpp_quote("DEFINE_GUID(GUID_ContainerFormatRaw,  0xfe99ce60,0xf19c,0x433c,0xa3,0xae,0x00,0xac,0xef,0xa9,0xca,0x21);")

cpp_quote("DEFINE_GUID(GUID_VendorMicrosoft, 0xf0e749ca,0xedef,0x4589,0xa7,0x3a,0xee,0x0e,0x62,0x6a,0x2a,0x2b);")
cpp_quote("DEFINE_GUID(GUID_VendorMicrosoftBuiltIn, 0x257a30fd,0x6b6,0x462b,0xae,0xa4,0x63,0xf7,0xb,0x86,0xe5,0x33);")

cpp_quote("DEFINE_GUID(CLSID_WICImagingCategories, 0xfae3d380,0xfea4,0x4623,0x8c,0x75,0xc6,0xb6,0x11,0x10,0xb6,0x81);")
cpp_quote("DEFINE_GUID(CATID_WICBitmapDecoders, 0x7ed96837,0x96f0,0x4812,0xb2,0x11,0xf1,0x3c,0x24,0x11,0x7e,0xd3);")
cpp_quote("DEFINE_GUID(CATID_WICBitmapEncoders, 0xac757296,0x3522,0x4e11,0x98,0x62,0xc1,0x7b,0xe5,0xa1,0x76,0x7e);")
cpp_quote("DEFINE_GUID(CATID_WICFormatConverters, 0x7835eae8,0xbf14,0x49d1,0x93,0xce,0x53,0x3a,0x40,0x7b,0x22,0x48);")
cpp_quote("DEFINE_GUID(CATID_WICMetadataReader, 0x05af94d8,0x7174,0x4cd2,0xbe,0x4a,0x41,0x24,0xb8,0x0e,0xe4,0xb8);")
cpp_quote("DEFINE_GUID(CATID_WICMetadataWriter, 0xabe3b9a4,0x257d,0x4b97,0xbd,0x1a,0x29,0x4a,0xf4,0x96,0x22,0x2e);")
cpp_quote("DEFINE_GUID(CATID_WICPixelFormats, 0x2b46e70f,0xcda7,0x473e,0x89,0xf6,0xdc,0x96,0x30,0xa2,0x39,0x0b);")
