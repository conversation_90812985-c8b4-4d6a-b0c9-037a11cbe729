_chrt_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
		'-T'|'--sched-runtime'|'-P'|'--sched-period'|'-D'|'--sched-deadline')
			COMPREPLY=( $(compgen -W "nanoseconds" -- $cur) )
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="
				--all-tasks
				--batch
				--deadline
				--fifo
				--help
				--idle
				--max
				--other
				--pid
				--reset-on-fork
				--rr
				--sched-deadline
				--sched-period
				--sched-runtime
				--verbose
				--version
			"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	local i
	for i in ${COMP_WORDS[*]}; do
		case $i in
		'-p'|'--pid')
			COMPREPLY=( $(compgen -W "$(cd /proc && echo [0-9]*)" -- $cur) )
			return 0
			;;
		esac
	done
	COMPREPLY=( $(compgen -c -- $cur) )
	return 0
}
complete -F _chrt_module chrt
