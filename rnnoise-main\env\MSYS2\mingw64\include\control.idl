cpp_quote("/**")
cpp_quote(" * This file has no copyright assigned and is placed in the Public Domain.")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file DISCLAIMER.PD within this package.")
cpp_quote(" */")

import "oaidl.idl";

cpp_quote("#ifndef __IAMCollection_FWD_DEFINED__")
cpp_quote("#define __IAMCollection_FWD_DEFINED__")
cpp_quote("typedef struct IAMCollection IAMCollection;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMediaEvent_FWD_DEFINED__")
cpp_quote("#define __IMediaEvent_FWD_DEFINED__")
cpp_quote("typedef struct IMediaEvent IMediaEvent;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMediaEventEx_FWD_DEFINED__")
cpp_quote("#define __IMediaEventEx_FWD_DEFINED__")
cpp_quote("typedef struct IMediaEventEx IMediaEventEx;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMediaPosition_FWD_DEFINED__")
cpp_quote("#define __IMediaPosition_FWD_DEFINED__")
cpp_quote("typedef struct IMediaPosition IMediaPosition;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IBasicAudio_FWD_DEFINED__")
cpp_quote("#define __IBasicAudio_FWD_DEFINED__")
cpp_quote("typedef struct IBasicAudio IBasicAudio;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVideoWindow_FWD_DEFINED__")
cpp_quote("#define __IVideoWindow_FWD_DEFINED__")
cpp_quote("typedef struct IVideoWindow IVideoWindow;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IBasicVideo_FWD_DEFINED__")
cpp_quote("#define __IBasicVideo_FWD_DEFINED__")
cpp_quote("typedef struct IBasicVideo IBasicVideo;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IBasicVideo2_FWD_DEFINED__")
cpp_quote("#define __IBasicVideo2_FWD_DEFINED__")
cpp_quote("typedef struct IBasicVideo2 IBasicVideo2;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDeferredCommand_FWD_DEFINED__")
cpp_quote("#define __IDeferredCommand_FWD_DEFINED__")
cpp_quote("typedef struct IDeferredCommand IDeferredCommand;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IQueueCommand_FWD_DEFINED__")
cpp_quote("#define __IQueueCommand_FWD_DEFINED__")
cpp_quote("typedef struct IQueueCommand IQueueCommand;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __FilgraphManager_FWD_DEFINED__")
cpp_quote("#define __FilgraphManager_FWD_DEFINED__")
cpp_quote("")
cpp_quote("#ifdef __cplusplus")
cpp_quote("typedef class FilgraphManager FilgraphManager;")
cpp_quote("#else")
cpp_quote("typedef struct FilgraphManager FilgraphManager;")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IFilterInfo_FWD_DEFINED__")
cpp_quote("#define __IFilterInfo_FWD_DEFINED__")
cpp_quote("typedef struct IFilterInfo IFilterInfo;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IRegFilterInfo_FWD_DEFINED__")
cpp_quote("#define __IRegFilterInfo_FWD_DEFINED__")
cpp_quote("typedef struct IRegFilterInfo IRegFilterInfo;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMediaTypeInfo_FWD_DEFINED__")
cpp_quote("#define __IMediaTypeInfo_FWD_DEFINED__")
cpp_quote("typedef struct IMediaTypeInfo IMediaTypeInfo;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IPinInfo_FWD_DEFINED__")
cpp_quote("#define __IPinInfo_FWD_DEFINED__")
cpp_quote("typedef struct IPinInfo IPinInfo;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMStats_FWD_DEFINED__")
cpp_quote("#define __IAMStats_FWD_DEFINED__")
cpp_quote("typedef struct IAMStats IAMStats;")
cpp_quote("#endif")

[
    version(1.0),
    uuid(56a868b0-0ad4-11ce-b03a-0020af0ba770)
]
library QuartzTypeLib
{
    importlib("stdole2.tlb");

    typedef LONG OAFilterState;
    typedef LONG_PTR OAHWND;
    typedef LONG_PTR OAEVENT;

    cpp_quote("#ifndef REFTIME_DEFINED")
    cpp_quote("#define REFTIME_DEFINED")
    typedef DOUBLE REFTIME;
    cpp_quote("#endif")

    [
        object,
        uuid(56a868b1-0ad4-11ce-b03a-0020af0ba770),
        pointer_default(unique)
    ]
    interface IMediaControl : IDispatch {
        HRESULT Run();
        HRESULT Pause();
        HRESULT Stop();
        HRESULT GetState( [in] LONG msTimeout, [out] OAFilterState *pfs );
        HRESULT RenderFile( [in] BSTR strFilename );
        HRESULT AddSourceFilter( [in] BSTR strFilename, [out] IDispatch **ppUnk );
        [propget] HRESULT FilterCollection( [out] IDispatch **ppUnk );
        [propget] HRESULT RegFilterCollection( [out] IDispatch **ppUnk );
        HRESULT StopWhenReady();
    }

cpp_quote("#ifndef __IAMCollection_INTERFACE_DEFINED__")
cpp_quote("#define __IAMCollection_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IAMCollection,0x56a868b9,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMCollection : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_Count(LONG *plCount) = 0;")
cpp_quote("    virtual HRESULT WINAPI Item(__LONG32 lItem,IUnknown **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI get__NewEnum(IUnknown **ppUnk) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMCollectionVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMCollection *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMCollection *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMCollection *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IAMCollection *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IAMCollection *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IAMCollection *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IAMCollection *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *get_Count)(IAMCollection *This,LONG *plCount);")
cpp_quote("      HRESULT (WINAPI *Item)(IAMCollection *This,__LONG32 lItem,IUnknown **ppUnk);")
cpp_quote("      HRESULT (WINAPI *get__NewEnum)(IAMCollection *This,IUnknown **ppUnk);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMCollectionVtbl;")
cpp_quote("  struct IAMCollection {")
cpp_quote("    CONST_VTBL struct IAMCollectionVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMCollection_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMCollection_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IAMCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IAMCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IAMCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IAMCollection_get_Count(This,plCount) (This)->lpVtbl->get_Count(This,plCount)")
cpp_quote("#define IAMCollection_Item(This,lItem,ppUnk) (This)->lpVtbl->Item(This,lItem,ppUnk)")
cpp_quote("#define IAMCollection_get__NewEnum(This,ppUnk) (This)->lpVtbl->get__NewEnum(This,ppUnk)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMCollection_get_Count_Proxy(IAMCollection *This,LONG *plCount);")
cpp_quote("  void __RPC_STUB IAMCollection_get_Count_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMCollection_Item_Proxy(IAMCollection *This,__LONG32 lItem,IUnknown **ppUnk);")
cpp_quote("  void __RPC_STUB IAMCollection_Item_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMCollection_get__NewEnum_Proxy(IAMCollection *This,IUnknown **ppUnk);")
cpp_quote("  void __RPC_STUB IAMCollection_get__NewEnum_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("#ifndef __IMediaEvent_INTERFACE_DEFINED__")
cpp_quote("#define __IMediaEvent_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IMediaEvent,0x56a868b6,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IMediaEvent : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetEventHandle(OAEVENT *hEvent) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetEvent(__LONG32 *lEventCode,LONG_PTR *lParam1,LONG_PTR *lParam2,__LONG32 msTimeout) = 0;")
cpp_quote("    virtual HRESULT WINAPI WaitForCompletion(__LONG32 msTimeout,__LONG32 *pEvCode) = 0;")
cpp_quote("    virtual HRESULT WINAPI CancelDefaultHandling(__LONG32 lEvCode) = 0;")
cpp_quote("    virtual HRESULT WINAPI RestoreDefaultHandling(__LONG32 lEvCode) = 0;")
cpp_quote("    virtual HRESULT WINAPI FreeEventParams(__LONG32 lEvCode,LONG_PTR lParam1,LONG_PTR lParam2) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IMediaEventVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IMediaEvent *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IMediaEvent *This);")
cpp_quote("      ULONG (WINAPI *Release)(IMediaEvent *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IMediaEvent *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IMediaEvent *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IMediaEvent *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IMediaEvent *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *GetEventHandle)(IMediaEvent *This,OAEVENT *hEvent);")
cpp_quote("      HRESULT (WINAPI *GetEvent)(IMediaEvent *This,__LONG32 *lEventCode,LONG_PTR *lParam1,LONG_PTR *lParam2,__LONG32 msTimeout);")
cpp_quote("      HRESULT (WINAPI *WaitForCompletion)(IMediaEvent *This,__LONG32 msTimeout,__LONG32 *pEvCode);")
cpp_quote("      HRESULT (WINAPI *CancelDefaultHandling)(IMediaEvent *This,__LONG32 lEvCode);")
cpp_quote("      HRESULT (WINAPI *RestoreDefaultHandling)(IMediaEvent *This,__LONG32 lEvCode);")
cpp_quote("      HRESULT (WINAPI *FreeEventParams)(IMediaEvent *This,__LONG32 lEvCode,LONG_PTR lParam1,LONG_PTR lParam2);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IMediaEventVtbl;")
cpp_quote("  struct IMediaEvent {")
cpp_quote("    CONST_VTBL struct IMediaEventVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IMediaEvent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IMediaEvent_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IMediaEvent_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IMediaEvent_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IMediaEvent_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IMediaEvent_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IMediaEvent_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IMediaEvent_GetEventHandle(This,hEvent) (This)->lpVtbl->GetEventHandle(This,hEvent)")
cpp_quote("#define IMediaEvent_GetEvent(This,lEventCode,lParam1,lParam2,msTimeout) (This)->lpVtbl->GetEvent(This,lEventCode,lParam1,lParam2,msTimeout)")
cpp_quote("#define IMediaEvent_WaitForCompletion(This,msTimeout,pEvCode) (This)->lpVtbl->WaitForCompletion(This,msTimeout,pEvCode)")
cpp_quote("#define IMediaEvent_CancelDefaultHandling(This,lEvCode) (This)->lpVtbl->CancelDefaultHandling(This,lEvCode)")
cpp_quote("#define IMediaEvent_RestoreDefaultHandling(This,lEvCode) (This)->lpVtbl->RestoreDefaultHandling(This,lEvCode)")
cpp_quote("#define IMediaEvent_FreeEventParams(This,lEvCode,lParam1,lParam2) (This)->lpVtbl->FreeEventParams(This,lEvCode,lParam1,lParam2)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IMediaEvent_GetEventHandle_Proxy(IMediaEvent *This,OAEVENT *hEvent);")
cpp_quote("  void __RPC_STUB IMediaEvent_GetEventHandle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaEvent_GetEvent_Proxy(IMediaEvent *This,__LONG32 *lEventCode,LONG_PTR *lParam1,LONG_PTR *lParam2,__LONG32 msTimeout);")
cpp_quote("  void __RPC_STUB IMediaEvent_GetEvent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaEvent_WaitForCompletion_Proxy(IMediaEvent *This,__LONG32 msTimeout,__LONG32 *pEvCode);")
cpp_quote("  void __RPC_STUB IMediaEvent_WaitForCompletion_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaEvent_CancelDefaultHandling_Proxy(IMediaEvent *This,__LONG32 lEvCode);")
cpp_quote("  void __RPC_STUB IMediaEvent_CancelDefaultHandling_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaEvent_RestoreDefaultHandling_Proxy(IMediaEvent *This,__LONG32 lEvCode);")
cpp_quote("  void __RPC_STUB IMediaEvent_RestoreDefaultHandling_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaEvent_FreeEventParams_Proxy(IMediaEvent *This,__LONG32 lEvCode,LONG_PTR lParam1,LONG_PTR lParam2);")
cpp_quote("  void __RPC_STUB IMediaEvent_FreeEventParams_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMediaEventEx_INTERFACE_DEFINED__")
cpp_quote("#define __IMediaEventEx_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IMediaEventEx,0x56a868c0,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IMediaEventEx : public IMediaEvent {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetNotifyWindow(OAHWND hwnd,__LONG32 lMsg,LONG_PTR lInstanceData) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetNotifyFlags(__LONG32 lNoNotifyFlags) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetNotifyFlags(__LONG32 *lplNoNotifyFlags) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IMediaEventExVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IMediaEventEx *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IMediaEventEx *This);")
cpp_quote("      ULONG (WINAPI *Release)(IMediaEventEx *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IMediaEventEx *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IMediaEventEx *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IMediaEventEx *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IMediaEventEx *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *GetEventHandle)(IMediaEventEx *This,OAEVENT *hEvent);")
cpp_quote("      HRESULT (WINAPI *GetEvent)(IMediaEventEx *This,__LONG32 *lEventCode,LONG_PTR *lParam1,LONG_PTR *lParam2,__LONG32 msTimeout);")
cpp_quote("      HRESULT (WINAPI *WaitForCompletion)(IMediaEventEx *This,__LONG32 msTimeout,__LONG32 *pEvCode);")
cpp_quote("      HRESULT (WINAPI *CancelDefaultHandling)(IMediaEventEx *This,__LONG32 lEvCode);")
cpp_quote("      HRESULT (WINAPI *RestoreDefaultHandling)(IMediaEventEx *This,__LONG32 lEvCode);")
cpp_quote("      HRESULT (WINAPI *FreeEventParams)(IMediaEventEx *This,__LONG32 lEvCode,LONG_PTR lParam1,LONG_PTR lParam2);")
cpp_quote("      HRESULT (WINAPI *SetNotifyWindow)(IMediaEventEx *This,OAHWND hwnd,__LONG32 lMsg,LONG_PTR lInstanceData);")
cpp_quote("      HRESULT (WINAPI *SetNotifyFlags)(IMediaEventEx *This,__LONG32 lNoNotifyFlags);")
cpp_quote("      HRESULT (WINAPI *GetNotifyFlags)(IMediaEventEx *This,__LONG32 *lplNoNotifyFlags);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IMediaEventExVtbl;")
cpp_quote("  struct IMediaEventEx {")
cpp_quote("    CONST_VTBL struct IMediaEventExVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IMediaEventEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IMediaEventEx_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IMediaEventEx_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IMediaEventEx_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IMediaEventEx_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IMediaEventEx_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IMediaEventEx_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IMediaEventEx_GetEventHandle(This,hEvent) (This)->lpVtbl->GetEventHandle(This,hEvent)")
cpp_quote("#define IMediaEventEx_GetEvent(This,lEventCode,lParam1,lParam2,msTimeout) (This)->lpVtbl->GetEvent(This,lEventCode,lParam1,lParam2,msTimeout)")
cpp_quote("#define IMediaEventEx_WaitForCompletion(This,msTimeout,pEvCode) (This)->lpVtbl->WaitForCompletion(This,msTimeout,pEvCode)")
cpp_quote("#define IMediaEventEx_CancelDefaultHandling(This,lEvCode) (This)->lpVtbl->CancelDefaultHandling(This,lEvCode)")
cpp_quote("#define IMediaEventEx_RestoreDefaultHandling(This,lEvCode) (This)->lpVtbl->RestoreDefaultHandling(This,lEvCode)")
cpp_quote("#define IMediaEventEx_FreeEventParams(This,lEvCode,lParam1,lParam2) (This)->lpVtbl->FreeEventParams(This,lEvCode,lParam1,lParam2)")
cpp_quote("#define IMediaEventEx_SetNotifyWindow(This,hwnd,lMsg,lInstanceData) (This)->lpVtbl->SetNotifyWindow(This,hwnd,lMsg,lInstanceData)")
cpp_quote("#define IMediaEventEx_SetNotifyFlags(This,lNoNotifyFlags) (This)->lpVtbl->SetNotifyFlags(This,lNoNotifyFlags)")
cpp_quote("#define IMediaEventEx_GetNotifyFlags(This,lplNoNotifyFlags) (This)->lpVtbl->GetNotifyFlags(This,lplNoNotifyFlags)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IMediaEventEx_SetNotifyWindow_Proxy(IMediaEventEx *This,OAHWND hwnd,__LONG32 lMsg,LONG_PTR lInstanceData);")
cpp_quote("  void __RPC_STUB IMediaEventEx_SetNotifyWindow_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaEventEx_SetNotifyFlags_Proxy(IMediaEventEx *This,__LONG32 lNoNotifyFlags);")
cpp_quote("  void __RPC_STUB IMediaEventEx_SetNotifyFlags_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaEventEx_GetNotifyFlags_Proxy(IMediaEventEx *This,__LONG32 *lplNoNotifyFlags);")
cpp_quote("  void __RPC_STUB IMediaEventEx_GetNotifyFlags_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMediaPosition_INTERFACE_DEFINED__")
cpp_quote("#define __IMediaPosition_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IMediaPosition,0x56a868b2,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IMediaPosition : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_Duration(REFTIME *plength) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_CurrentPosition(REFTIME llTime) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_CurrentPosition(REFTIME *pllTime) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_StopTime(REFTIME *pllTime) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_StopTime(REFTIME llTime) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_PrerollTime(REFTIME *pllTime) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_PrerollTime(REFTIME llTime) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Rate(double dRate) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Rate(double *pdRate) = 0;")
cpp_quote("    virtual HRESULT WINAPI CanSeekForward(LONG *pCanSeekForward) = 0;")
cpp_quote("    virtual HRESULT WINAPI CanSeekBackward(LONG *pCanSeekBackward) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IMediaPositionVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IMediaPosition *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IMediaPosition *This);")
cpp_quote("      ULONG (WINAPI *Release)(IMediaPosition *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IMediaPosition *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IMediaPosition *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IMediaPosition *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IMediaPosition *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *get_Duration)(IMediaPosition *This,REFTIME *plength);")
cpp_quote("      HRESULT (WINAPI *put_CurrentPosition)(IMediaPosition *This,REFTIME llTime);")
cpp_quote("      HRESULT (WINAPI *get_CurrentPosition)(IMediaPosition *This,REFTIME *pllTime);")
cpp_quote("      HRESULT (WINAPI *get_StopTime)(IMediaPosition *This,REFTIME *pllTime);")
cpp_quote("      HRESULT (WINAPI *put_StopTime)(IMediaPosition *This,REFTIME llTime);")
cpp_quote("      HRESULT (WINAPI *get_PrerollTime)(IMediaPosition *This,REFTIME *pllTime);")
cpp_quote("      HRESULT (WINAPI *put_PrerollTime)(IMediaPosition *This,REFTIME llTime);")
cpp_quote("      HRESULT (WINAPI *put_Rate)(IMediaPosition *This,double dRate);")
cpp_quote("      HRESULT (WINAPI *get_Rate)(IMediaPosition *This,double *pdRate);")
cpp_quote("      HRESULT (WINAPI *CanSeekForward)(IMediaPosition *This,LONG *pCanSeekForward);")
cpp_quote("      HRESULT (WINAPI *CanSeekBackward)(IMediaPosition *This,LONG *pCanSeekBackward);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IMediaPositionVtbl;")
cpp_quote("  struct IMediaPosition {")
cpp_quote("    CONST_VTBL struct IMediaPositionVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IMediaPosition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IMediaPosition_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IMediaPosition_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IMediaPosition_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IMediaPosition_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IMediaPosition_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IMediaPosition_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IMediaPosition_get_Duration(This,plength) (This)->lpVtbl->get_Duration(This,plength)")
cpp_quote("#define IMediaPosition_put_CurrentPosition(This,llTime) (This)->lpVtbl->put_CurrentPosition(This,llTime)")
cpp_quote("#define IMediaPosition_get_CurrentPosition(This,pllTime) (This)->lpVtbl->get_CurrentPosition(This,pllTime)")
cpp_quote("#define IMediaPosition_get_StopTime(This,pllTime) (This)->lpVtbl->get_StopTime(This,pllTime)")
cpp_quote("#define IMediaPosition_put_StopTime(This,llTime) (This)->lpVtbl->put_StopTime(This,llTime)")
cpp_quote("#define IMediaPosition_get_PrerollTime(This,pllTime) (This)->lpVtbl->get_PrerollTime(This,pllTime)")
cpp_quote("#define IMediaPosition_put_PrerollTime(This,llTime) (This)->lpVtbl->put_PrerollTime(This,llTime)")
cpp_quote("#define IMediaPosition_put_Rate(This,dRate) (This)->lpVtbl->put_Rate(This,dRate)")
cpp_quote("#define IMediaPosition_get_Rate(This,pdRate) (This)->lpVtbl->get_Rate(This,pdRate)")
cpp_quote("#define IMediaPosition_CanSeekForward(This,pCanSeekForward) (This)->lpVtbl->CanSeekForward(This,pCanSeekForward)")
cpp_quote("#define IMediaPosition_CanSeekBackward(This,pCanSeekBackward) (This)->lpVtbl->CanSeekBackward(This,pCanSeekBackward)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IMediaPosition_get_Duration_Proxy(IMediaPosition *This,REFTIME *plength);")
cpp_quote("  void __RPC_STUB IMediaPosition_get_Duration_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_put_CurrentPosition_Proxy(IMediaPosition *This,REFTIME llTime);")
cpp_quote("  void __RPC_STUB IMediaPosition_put_CurrentPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_get_CurrentPosition_Proxy(IMediaPosition *This,REFTIME *pllTime);")
cpp_quote("  void __RPC_STUB IMediaPosition_get_CurrentPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_get_StopTime_Proxy(IMediaPosition *This,REFTIME *pllTime);")
cpp_quote("  void __RPC_STUB IMediaPosition_get_StopTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_put_StopTime_Proxy(IMediaPosition *This,REFTIME llTime);")
cpp_quote("  void __RPC_STUB IMediaPosition_put_StopTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_get_PrerollTime_Proxy(IMediaPosition *This,REFTIME *pllTime);")
cpp_quote("  void __RPC_STUB IMediaPosition_get_PrerollTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_put_PrerollTime_Proxy(IMediaPosition *This,REFTIME llTime);")
cpp_quote("  void __RPC_STUB IMediaPosition_put_PrerollTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_put_Rate_Proxy(IMediaPosition *This,double dRate);")
cpp_quote("  void __RPC_STUB IMediaPosition_put_Rate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_get_Rate_Proxy(IMediaPosition *This,double *pdRate);")
cpp_quote("  void __RPC_STUB IMediaPosition_get_Rate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_CanSeekForward_Proxy(IMediaPosition *This,LONG *pCanSeekForward);")
cpp_quote("  void __RPC_STUB IMediaPosition_CanSeekForward_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaPosition_CanSeekBackward_Proxy(IMediaPosition *This,LONG *pCanSeekBackward);")
cpp_quote("  void __RPC_STUB IMediaPosition_CanSeekBackward_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IBasicAudio_INTERFACE_DEFINED__")
cpp_quote("#define __IBasicAudio_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IBasicAudio,0x56a868b3,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IBasicAudio : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI put_Volume(__LONG32 lVolume) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Volume(__LONG32 *plVolume) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Balance(__LONG32 lBalance) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Balance(__LONG32 *plBalance) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IBasicAudioVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IBasicAudio *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IBasicAudio *This);")
cpp_quote("      ULONG (WINAPI *Release)(IBasicAudio *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IBasicAudio *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IBasicAudio *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IBasicAudio *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IBasicAudio *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *put_Volume)(IBasicAudio *This,__LONG32 lVolume);")
cpp_quote("      HRESULT (WINAPI *get_Volume)(IBasicAudio *This,__LONG32 *plVolume);")
cpp_quote("      HRESULT (WINAPI *put_Balance)(IBasicAudio *This,__LONG32 lBalance);")
cpp_quote("      HRESULT (WINAPI *get_Balance)(IBasicAudio *This,__LONG32 *plBalance);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IBasicAudioVtbl;")
cpp_quote("  struct IBasicAudio {")
cpp_quote("    CONST_VTBL struct IBasicAudioVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IBasicAudio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IBasicAudio_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IBasicAudio_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IBasicAudio_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IBasicAudio_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IBasicAudio_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IBasicAudio_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IBasicAudio_put_Volume(This,lVolume) (This)->lpVtbl->put_Volume(This,lVolume)")
cpp_quote("#define IBasicAudio_get_Volume(This,plVolume) (This)->lpVtbl->get_Volume(This,plVolume)")
cpp_quote("#define IBasicAudio_put_Balance(This,lBalance) (This)->lpVtbl->put_Balance(This,lBalance)")
cpp_quote("#define IBasicAudio_get_Balance(This,plBalance) (This)->lpVtbl->get_Balance(This,plBalance)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IBasicAudio_put_Volume_Proxy(IBasicAudio *This,__LONG32 lVolume);")
cpp_quote("  void __RPC_STUB IBasicAudio_put_Volume_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicAudio_get_Volume_Proxy(IBasicAudio *This,__LONG32 *plVolume);")
cpp_quote("  void __RPC_STUB IBasicAudio_get_Volume_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicAudio_put_Balance_Proxy(IBasicAudio *This,__LONG32 lBalance);")
cpp_quote("  void __RPC_STUB IBasicAudio_put_Balance_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicAudio_get_Balance_Proxy(IBasicAudio *This,__LONG32 *plBalance);")
cpp_quote("  void __RPC_STUB IBasicAudio_get_Balance_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVideoWindow_INTERFACE_DEFINED__")
cpp_quote("#define __IVideoWindow_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IVideoWindow,0x56a868b4,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVideoWindow : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI put_Caption(BSTR strCaption) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Caption(BSTR *strCaption) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_WindowStyle(__LONG32 WindowStyle) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_WindowStyle(__LONG32 *WindowStyle) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_WindowStyleEx(__LONG32 WindowStyleEx) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_WindowStyleEx(__LONG32 *WindowStyleEx) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_AutoShow(__LONG32 AutoShow) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_AutoShow(__LONG32 *AutoShow) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_WindowState(__LONG32 WindowState) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_WindowState(__LONG32 *WindowState) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_BackgroundPalette(__LONG32 BackgroundPalette) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_BackgroundPalette(__LONG32 *pBackgroundPalette) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Visible(__LONG32 Visible) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Visible(__LONG32 *pVisible) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Left(__LONG32 Left) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Left(__LONG32 *pLeft) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Width(__LONG32 Width) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Width(__LONG32 *pWidth) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Top(__LONG32 Top) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Top(__LONG32 *pTop) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Height(__LONG32 Height) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Height(__LONG32 *pHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Owner(OAHWND Owner) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Owner(OAHWND *Owner) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_MessageDrain(OAHWND Drain) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_MessageDrain(OAHWND *Drain) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_BorderColor(__LONG32 *Color) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_BorderColor(__LONG32 Color) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_FullScreenMode(__LONG32 *FullScreenMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_FullScreenMode(__LONG32 FullScreenMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetWindowForeground(__LONG32 Focus) = 0;")
cpp_quote("    virtual HRESULT WINAPI NotifyOwnerMessage(OAHWND hwnd,__LONG32 uMsg,LONG_PTR wParam,LONG_PTR lParam) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetWindowPosition(__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetWindowPosition(__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetMinIdealImageSize(__LONG32 *pWidth,__LONG32 *pHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetMaxIdealImageSize(__LONG32 *pWidth,__LONG32 *pHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetRestorePosition(__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI HideCursor(__LONG32 HideCursor) = 0;")
cpp_quote("    virtual HRESULT WINAPI IsCursorHidden(__LONG32 *CursorHidden) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVideoWindowVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVideoWindow *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVideoWindow *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVideoWindow *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IVideoWindow *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IVideoWindow *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IVideoWindow *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IVideoWindow *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *put_Caption)(IVideoWindow *This,BSTR strCaption);")
cpp_quote("      HRESULT (WINAPI *get_Caption)(IVideoWindow *This,BSTR *strCaption);")
cpp_quote("      HRESULT (WINAPI *put_WindowStyle)(IVideoWindow *This,__LONG32 WindowStyle);")
cpp_quote("      HRESULT (WINAPI *get_WindowStyle)(IVideoWindow *This,__LONG32 *WindowStyle);")
cpp_quote("      HRESULT (WINAPI *put_WindowStyleEx)(IVideoWindow *This,__LONG32 WindowStyleEx);")
cpp_quote("      HRESULT (WINAPI *get_WindowStyleEx)(IVideoWindow *This,__LONG32 *WindowStyleEx);")
cpp_quote("      HRESULT (WINAPI *put_AutoShow)(IVideoWindow *This,__LONG32 AutoShow);")
cpp_quote("      HRESULT (WINAPI *get_AutoShow)(IVideoWindow *This,__LONG32 *AutoShow);")
cpp_quote("      HRESULT (WINAPI *put_WindowState)(IVideoWindow *This,__LONG32 WindowState);")
cpp_quote("      HRESULT (WINAPI *get_WindowState)(IVideoWindow *This,__LONG32 *WindowState);")
cpp_quote("      HRESULT (WINAPI *put_BackgroundPalette)(IVideoWindow *This,__LONG32 BackgroundPalette);")
cpp_quote("      HRESULT (WINAPI *get_BackgroundPalette)(IVideoWindow *This,__LONG32 *pBackgroundPalette);")
cpp_quote("      HRESULT (WINAPI *put_Visible)(IVideoWindow *This,__LONG32 Visible);")
cpp_quote("      HRESULT (WINAPI *get_Visible)(IVideoWindow *This,__LONG32 *pVisible);")
cpp_quote("      HRESULT (WINAPI *put_Left)(IVideoWindow *This,__LONG32 Left);")
cpp_quote("      HRESULT (WINAPI *get_Left)(IVideoWindow *This,__LONG32 *pLeft);")
cpp_quote("      HRESULT (WINAPI *put_Width)(IVideoWindow *This,__LONG32 Width);")
cpp_quote("      HRESULT (WINAPI *get_Width)(IVideoWindow *This,__LONG32 *pWidth);")
cpp_quote("      HRESULT (WINAPI *put_Top)(IVideoWindow *This,__LONG32 Top);")
cpp_quote("      HRESULT (WINAPI *get_Top)(IVideoWindow *This,__LONG32 *pTop);")
cpp_quote("      HRESULT (WINAPI *put_Height)(IVideoWindow *This,__LONG32 Height);")
cpp_quote("      HRESULT (WINAPI *get_Height)(IVideoWindow *This,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *put_Owner)(IVideoWindow *This,OAHWND Owner);")
cpp_quote("      HRESULT (WINAPI *get_Owner)(IVideoWindow *This,OAHWND *Owner);")
cpp_quote("      HRESULT (WINAPI *put_MessageDrain)(IVideoWindow *This,OAHWND Drain);")
cpp_quote("      HRESULT (WINAPI *get_MessageDrain)(IVideoWindow *This,OAHWND *Drain);")
cpp_quote("      HRESULT (WINAPI *get_BorderColor)(IVideoWindow *This,__LONG32 *Color);")
cpp_quote("      HRESULT (WINAPI *put_BorderColor)(IVideoWindow *This,__LONG32 Color);")
cpp_quote("      HRESULT (WINAPI *get_FullScreenMode)(IVideoWindow *This,__LONG32 *FullScreenMode);")
cpp_quote("      HRESULT (WINAPI *put_FullScreenMode)(IVideoWindow *This,__LONG32 FullScreenMode);")
cpp_quote("      HRESULT (WINAPI *SetWindowForeground)(IVideoWindow *This,__LONG32 Focus);")
cpp_quote("      HRESULT (WINAPI *NotifyOwnerMessage)(IVideoWindow *This,OAHWND hwnd,__LONG32 uMsg,LONG_PTR wParam,LONG_PTR lParam);")
cpp_quote("      HRESULT (WINAPI *SetWindowPosition)(IVideoWindow *This,__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height);")
cpp_quote("      HRESULT (WINAPI *GetWindowPosition)(IVideoWindow *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *GetMinIdealImageSize)(IVideoWindow *This,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *GetMaxIdealImageSize)(IVideoWindow *This,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *GetRestorePosition)(IVideoWindow *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *HideCursor)(IVideoWindow *This,__LONG32 HideCursor);")
cpp_quote("      HRESULT (WINAPI *IsCursorHidden)(IVideoWindow *This,__LONG32 *CursorHidden);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVideoWindowVtbl;")
cpp_quote("  struct IVideoWindow {")
cpp_quote("    CONST_VTBL struct IVideoWindowVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVideoWindow_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVideoWindow_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVideoWindow_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVideoWindow_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IVideoWindow_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IVideoWindow_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IVideoWindow_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IVideoWindow_put_Caption(This,strCaption) (This)->lpVtbl->put_Caption(This,strCaption)")
cpp_quote("#define IVideoWindow_get_Caption(This,strCaption) (This)->lpVtbl->get_Caption(This,strCaption)")
cpp_quote("#define IVideoWindow_put_WindowStyle(This,WindowStyle) (This)->lpVtbl->put_WindowStyle(This,WindowStyle)")
cpp_quote("#define IVideoWindow_get_WindowStyle(This,WindowStyle) (This)->lpVtbl->get_WindowStyle(This,WindowStyle)")
cpp_quote("#define IVideoWindow_put_WindowStyleEx(This,WindowStyleEx) (This)->lpVtbl->put_WindowStyleEx(This,WindowStyleEx)")
cpp_quote("#define IVideoWindow_get_WindowStyleEx(This,WindowStyleEx) (This)->lpVtbl->get_WindowStyleEx(This,WindowStyleEx)")
cpp_quote("#define IVideoWindow_put_AutoShow(This,AutoShow) (This)->lpVtbl->put_AutoShow(This,AutoShow)")
cpp_quote("#define IVideoWindow_get_AutoShow(This,AutoShow) (This)->lpVtbl->get_AutoShow(This,AutoShow)")
cpp_quote("#define IVideoWindow_put_WindowState(This,WindowState) (This)->lpVtbl->put_WindowState(This,WindowState)")
cpp_quote("#define IVideoWindow_get_WindowState(This,WindowState) (This)->lpVtbl->get_WindowState(This,WindowState)")
cpp_quote("#define IVideoWindow_put_BackgroundPalette(This,BackgroundPalette) (This)->lpVtbl->put_BackgroundPalette(This,BackgroundPalette)")
cpp_quote("#define IVideoWindow_get_BackgroundPalette(This,pBackgroundPalette) (This)->lpVtbl->get_BackgroundPalette(This,pBackgroundPalette)")
cpp_quote("#define IVideoWindow_put_Visible(This,Visible) (This)->lpVtbl->put_Visible(This,Visible)")
cpp_quote("#define IVideoWindow_get_Visible(This,pVisible) (This)->lpVtbl->get_Visible(This,pVisible)")
cpp_quote("#define IVideoWindow_put_Left(This,Left) (This)->lpVtbl->put_Left(This,Left)")
cpp_quote("#define IVideoWindow_get_Left(This,pLeft) (This)->lpVtbl->get_Left(This,pLeft)")
cpp_quote("#define IVideoWindow_put_Width(This,Width) (This)->lpVtbl->put_Width(This,Width)")
cpp_quote("#define IVideoWindow_get_Width(This,pWidth) (This)->lpVtbl->get_Width(This,pWidth)")
cpp_quote("#define IVideoWindow_put_Top(This,Top) (This)->lpVtbl->put_Top(This,Top)")
cpp_quote("#define IVideoWindow_get_Top(This,pTop) (This)->lpVtbl->get_Top(This,pTop)")
cpp_quote("#define IVideoWindow_put_Height(This,Height) (This)->lpVtbl->put_Height(This,Height)")
cpp_quote("#define IVideoWindow_get_Height(This,pHeight) (This)->lpVtbl->get_Height(This,pHeight)")
cpp_quote("#define IVideoWindow_put_Owner(This,Owner) (This)->lpVtbl->put_Owner(This,Owner)")
cpp_quote("#define IVideoWindow_get_Owner(This,Owner) (This)->lpVtbl->get_Owner(This,Owner)")
cpp_quote("#define IVideoWindow_put_MessageDrain(This,Drain) (This)->lpVtbl->put_MessageDrain(This,Drain)")
cpp_quote("#define IVideoWindow_get_MessageDrain(This,Drain) (This)->lpVtbl->get_MessageDrain(This,Drain)")
cpp_quote("#define IVideoWindow_get_BorderColor(This,Color) (This)->lpVtbl->get_BorderColor(This,Color)")
cpp_quote("#define IVideoWindow_put_BorderColor(This,Color) (This)->lpVtbl->put_BorderColor(This,Color)")
cpp_quote("#define IVideoWindow_get_FullScreenMode(This,FullScreenMode) (This)->lpVtbl->get_FullScreenMode(This,FullScreenMode)")
cpp_quote("#define IVideoWindow_put_FullScreenMode(This,FullScreenMode) (This)->lpVtbl->put_FullScreenMode(This,FullScreenMode)")
cpp_quote("#define IVideoWindow_SetWindowForeground(This,Focus) (This)->lpVtbl->SetWindowForeground(This,Focus)")
cpp_quote("#define IVideoWindow_NotifyOwnerMessage(This,hwnd,uMsg,wParam,lParam) (This)->lpVtbl->NotifyOwnerMessage(This,hwnd,uMsg,wParam,lParam)")
cpp_quote("#define IVideoWindow_SetWindowPosition(This,Left,Top,Width,Height) (This)->lpVtbl->SetWindowPosition(This,Left,Top,Width,Height)")
cpp_quote("#define IVideoWindow_GetWindowPosition(This,pLeft,pTop,pWidth,pHeight) (This)->lpVtbl->GetWindowPosition(This,pLeft,pTop,pWidth,pHeight)")
cpp_quote("#define IVideoWindow_GetMinIdealImageSize(This,pWidth,pHeight) (This)->lpVtbl->GetMinIdealImageSize(This,pWidth,pHeight)")
cpp_quote("#define IVideoWindow_GetMaxIdealImageSize(This,pWidth,pHeight) (This)->lpVtbl->GetMaxIdealImageSize(This,pWidth,pHeight)")
cpp_quote("#define IVideoWindow_GetRestorePosition(This,pLeft,pTop,pWidth,pHeight) (This)->lpVtbl->GetRestorePosition(This,pLeft,pTop,pWidth,pHeight)")
cpp_quote("#define IVideoWindow_HideCursor(This,HideCursor) (This)->lpVtbl->HideCursor(This,HideCursor)")
cpp_quote("#define IVideoWindow_IsCursorHidden(This,CursorHidden) (This)->lpVtbl->IsCursorHidden(This,CursorHidden)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_Caption_Proxy(IVideoWindow *This,BSTR strCaption);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_Caption_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_Caption_Proxy(IVideoWindow *This,BSTR *strCaption);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_Caption_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_WindowStyle_Proxy(IVideoWindow *This,__LONG32 WindowStyle);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_WindowStyle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_WindowStyle_Proxy(IVideoWindow *This,__LONG32 *WindowStyle);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_WindowStyle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_WindowStyleEx_Proxy(IVideoWindow *This,__LONG32 WindowStyleEx);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_WindowStyleEx_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_WindowStyleEx_Proxy(IVideoWindow *This,__LONG32 *WindowStyleEx);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_WindowStyleEx_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_AutoShow_Proxy(IVideoWindow *This,__LONG32 AutoShow);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_AutoShow_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_AutoShow_Proxy(IVideoWindow *This,__LONG32 *AutoShow);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_AutoShow_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_WindowState_Proxy(IVideoWindow *This,__LONG32 WindowState);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_WindowState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_WindowState_Proxy(IVideoWindow *This,__LONG32 *WindowState);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_WindowState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_BackgroundPalette_Proxy(IVideoWindow *This,__LONG32 BackgroundPalette);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_BackgroundPalette_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_BackgroundPalette_Proxy(IVideoWindow *This,__LONG32 *pBackgroundPalette);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_BackgroundPalette_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_Visible_Proxy(IVideoWindow *This,__LONG32 Visible);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_Visible_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_Visible_Proxy(IVideoWindow *This,__LONG32 *pVisible);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_Visible_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_Left_Proxy(IVideoWindow *This,__LONG32 Left);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_Left_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_Left_Proxy(IVideoWindow *This,__LONG32 *pLeft);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_Left_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_Width_Proxy(IVideoWindow *This,__LONG32 Width);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_Width_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_Width_Proxy(IVideoWindow *This,__LONG32 *pWidth);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_Width_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_Top_Proxy(IVideoWindow *This,__LONG32 Top);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_Top_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_Top_Proxy(IVideoWindow *This,__LONG32 *pTop);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_Top_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_Height_Proxy(IVideoWindow *This,__LONG32 Height);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_Height_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_Height_Proxy(IVideoWindow *This,__LONG32 *pHeight);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_Height_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_Owner_Proxy(IVideoWindow *This,OAHWND Owner);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_Owner_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_Owner_Proxy(IVideoWindow *This,OAHWND *Owner);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_Owner_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_MessageDrain_Proxy(IVideoWindow *This,OAHWND Drain);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_MessageDrain_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_MessageDrain_Proxy(IVideoWindow *This,OAHWND *Drain);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_MessageDrain_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_BorderColor_Proxy(IVideoWindow *This,__LONG32 *Color);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_BorderColor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_BorderColor_Proxy(IVideoWindow *This,__LONG32 Color);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_BorderColor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_get_FullScreenMode_Proxy(IVideoWindow *This,__LONG32 *FullScreenMode);")
cpp_quote("  void __RPC_STUB IVideoWindow_get_FullScreenMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_put_FullScreenMode_Proxy(IVideoWindow *This,__LONG32 FullScreenMode);")
cpp_quote("  void __RPC_STUB IVideoWindow_put_FullScreenMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_SetWindowForeground_Proxy(IVideoWindow *This,__LONG32 Focus);")
cpp_quote("  void __RPC_STUB IVideoWindow_SetWindowForeground_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_NotifyOwnerMessage_Proxy(IVideoWindow *This,OAHWND hwnd,__LONG32 uMsg,LONG_PTR wParam,LONG_PTR lParam);")
cpp_quote("  void __RPC_STUB IVideoWindow_NotifyOwnerMessage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_SetWindowPosition_Proxy(IVideoWindow *This,__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height);")
cpp_quote("  void __RPC_STUB IVideoWindow_SetWindowPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_GetWindowPosition_Proxy(IVideoWindow *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("  void __RPC_STUB IVideoWindow_GetWindowPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_GetMinIdealImageSize_Proxy(IVideoWindow *This,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("  void __RPC_STUB IVideoWindow_GetMinIdealImageSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_GetMaxIdealImageSize_Proxy(IVideoWindow *This,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("  void __RPC_STUB IVideoWindow_GetMaxIdealImageSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_GetRestorePosition_Proxy(IVideoWindow *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("  void __RPC_STUB IVideoWindow_GetRestorePosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_HideCursor_Proxy(IVideoWindow *This,__LONG32 HideCursor);")
cpp_quote("  void __RPC_STUB IVideoWindow_HideCursor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVideoWindow_IsCursorHidden_Proxy(IVideoWindow *This,__LONG32 *CursorHidden);")
cpp_quote("  void __RPC_STUB IVideoWindow_IsCursorHidden_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IBasicVideo_INTERFACE_DEFINED__")
cpp_quote("#define __IBasicVideo_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IBasicVideo,0x56a868b5,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IBasicVideo : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_AvgTimePerFrame(REFTIME *pAvgTimePerFrame) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_BitRate(__LONG32 *pBitRate) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_BitErrorRate(__LONG32 *pBitErrorRate) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_VideoWidth(__LONG32 *pVideoWidth) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_VideoHeight(__LONG32 *pVideoHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_SourceLeft(__LONG32 SourceLeft) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_SourceLeft(__LONG32 *pSourceLeft) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_SourceWidth(__LONG32 SourceWidth) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_SourceWidth(__LONG32 *pSourceWidth) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_SourceTop(__LONG32 SourceTop) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_SourceTop(__LONG32 *pSourceTop) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_SourceHeight(__LONG32 SourceHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_SourceHeight(__LONG32 *pSourceHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_DestinationLeft(__LONG32 DestinationLeft) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_DestinationLeft(__LONG32 *pDestinationLeft) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_DestinationWidth(__LONG32 DestinationWidth) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_DestinationWidth(__LONG32 *pDestinationWidth) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_DestinationTop(__LONG32 DestinationTop) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_DestinationTop(__LONG32 *pDestinationTop) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_DestinationHeight(__LONG32 DestinationHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_DestinationHeight(__LONG32 *pDestinationHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetSourcePosition(__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetSourcePosition(__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDefaultSourcePosition(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDestinationPosition(__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDestinationPosition(__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDefaultDestinationPosition(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetVideoSize(__LONG32 *pWidth,__LONG32 *pHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetVideoPaletteEntries(__LONG32 StartIndex,__LONG32 Entries,__LONG32 *pRetrieved,__LONG32 *pPalette) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentImage(__LONG32 *pBufferSize,__LONG32 *pDIBImage) = 0;")
cpp_quote("    virtual HRESULT WINAPI IsUsingDefaultSource(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI IsUsingDefaultDestination(void) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IBasicVideoVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IBasicVideo *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IBasicVideo *This);")
cpp_quote("      ULONG (WINAPI *Release)(IBasicVideo *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IBasicVideo *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IBasicVideo *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IBasicVideo *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IBasicVideo *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *get_AvgTimePerFrame)(IBasicVideo *This,REFTIME *pAvgTimePerFrame);")
cpp_quote("      HRESULT (WINAPI *get_BitRate)(IBasicVideo *This,__LONG32 *pBitRate);")
cpp_quote("      HRESULT (WINAPI *get_BitErrorRate)(IBasicVideo *This,__LONG32 *pBitErrorRate);")
cpp_quote("      HRESULT (WINAPI *get_VideoWidth)(IBasicVideo *This,__LONG32 *pVideoWidth);")
cpp_quote("      HRESULT (WINAPI *get_VideoHeight)(IBasicVideo *This,__LONG32 *pVideoHeight);")
cpp_quote("      HRESULT (WINAPI *put_SourceLeft)(IBasicVideo *This,__LONG32 SourceLeft);")
cpp_quote("      HRESULT (WINAPI *get_SourceLeft)(IBasicVideo *This,__LONG32 *pSourceLeft);")
cpp_quote("      HRESULT (WINAPI *put_SourceWidth)(IBasicVideo *This,__LONG32 SourceWidth);")
cpp_quote("      HRESULT (WINAPI *get_SourceWidth)(IBasicVideo *This,__LONG32 *pSourceWidth);")
cpp_quote("      HRESULT (WINAPI *put_SourceTop)(IBasicVideo *This,__LONG32 SourceTop);")
cpp_quote("      HRESULT (WINAPI *get_SourceTop)(IBasicVideo *This,__LONG32 *pSourceTop);")
cpp_quote("      HRESULT (WINAPI *put_SourceHeight)(IBasicVideo *This,__LONG32 SourceHeight);")
cpp_quote("      HRESULT (WINAPI *get_SourceHeight)(IBasicVideo *This,__LONG32 *pSourceHeight);")
cpp_quote("      HRESULT (WINAPI *put_DestinationLeft)(IBasicVideo *This,__LONG32 DestinationLeft);")
cpp_quote("      HRESULT (WINAPI *get_DestinationLeft)(IBasicVideo *This,__LONG32 *pDestinationLeft);")
cpp_quote("      HRESULT (WINAPI *put_DestinationWidth)(IBasicVideo *This,__LONG32 DestinationWidth);")
cpp_quote("      HRESULT (WINAPI *get_DestinationWidth)(IBasicVideo *This,__LONG32 *pDestinationWidth);")
cpp_quote("      HRESULT (WINAPI *put_DestinationTop)(IBasicVideo *This,__LONG32 DestinationTop);")
cpp_quote("      HRESULT (WINAPI *get_DestinationTop)(IBasicVideo *This,__LONG32 *pDestinationTop);")
cpp_quote("      HRESULT (WINAPI *put_DestinationHeight)(IBasicVideo *This,__LONG32 DestinationHeight);")
cpp_quote("      HRESULT (WINAPI *get_DestinationHeight)(IBasicVideo *This,__LONG32 *pDestinationHeight);")
cpp_quote("      HRESULT (WINAPI *SetSourcePosition)(IBasicVideo *This,__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height);")
cpp_quote("      HRESULT (WINAPI *GetSourcePosition)(IBasicVideo *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *SetDefaultSourcePosition)(IBasicVideo *This);")
cpp_quote("      HRESULT (WINAPI *SetDestinationPosition)(IBasicVideo *This,__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height);")
cpp_quote("      HRESULT (WINAPI *GetDestinationPosition)(IBasicVideo *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *SetDefaultDestinationPosition)(IBasicVideo *This);")
cpp_quote("      HRESULT (WINAPI *GetVideoSize)(IBasicVideo *This,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *GetVideoPaletteEntries)(IBasicVideo *This,__LONG32 StartIndex,__LONG32 Entries,__LONG32 *pRetrieved,__LONG32 *pPalette);")
cpp_quote("      HRESULT (WINAPI *GetCurrentImage)(IBasicVideo *This,__LONG32 *pBufferSize,__LONG32 *pDIBImage);")
cpp_quote("      HRESULT (WINAPI *IsUsingDefaultSource)(IBasicVideo *This);")
cpp_quote("      HRESULT (WINAPI *IsUsingDefaultDestination)(IBasicVideo *This);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IBasicVideoVtbl;")
cpp_quote("  struct IBasicVideo {")
cpp_quote("    CONST_VTBL struct IBasicVideoVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IBasicVideo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IBasicVideo_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IBasicVideo_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IBasicVideo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IBasicVideo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IBasicVideo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IBasicVideo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IBasicVideo_get_AvgTimePerFrame(This,pAvgTimePerFrame) (This)->lpVtbl->get_AvgTimePerFrame(This,pAvgTimePerFrame)")
cpp_quote("#define IBasicVideo_get_BitRate(This,pBitRate) (This)->lpVtbl->get_BitRate(This,pBitRate)")
cpp_quote("#define IBasicVideo_get_BitErrorRate(This,pBitErrorRate) (This)->lpVtbl->get_BitErrorRate(This,pBitErrorRate)")
cpp_quote("#define IBasicVideo_get_VideoWidth(This,pVideoWidth) (This)->lpVtbl->get_VideoWidth(This,pVideoWidth)")
cpp_quote("#define IBasicVideo_get_VideoHeight(This,pVideoHeight) (This)->lpVtbl->get_VideoHeight(This,pVideoHeight)")
cpp_quote("#define IBasicVideo_put_SourceLeft(This,SourceLeft) (This)->lpVtbl->put_SourceLeft(This,SourceLeft)")
cpp_quote("#define IBasicVideo_get_SourceLeft(This,pSourceLeft) (This)->lpVtbl->get_SourceLeft(This,pSourceLeft)")
cpp_quote("#define IBasicVideo_put_SourceWidth(This,SourceWidth) (This)->lpVtbl->put_SourceWidth(This,SourceWidth)")
cpp_quote("#define IBasicVideo_get_SourceWidth(This,pSourceWidth) (This)->lpVtbl->get_SourceWidth(This,pSourceWidth)")
cpp_quote("#define IBasicVideo_put_SourceTop(This,SourceTop) (This)->lpVtbl->put_SourceTop(This,SourceTop)")
cpp_quote("#define IBasicVideo_get_SourceTop(This,pSourceTop) (This)->lpVtbl->get_SourceTop(This,pSourceTop)")
cpp_quote("#define IBasicVideo_put_SourceHeight(This,SourceHeight) (This)->lpVtbl->put_SourceHeight(This,SourceHeight)")
cpp_quote("#define IBasicVideo_get_SourceHeight(This,pSourceHeight) (This)->lpVtbl->get_SourceHeight(This,pSourceHeight)")
cpp_quote("#define IBasicVideo_put_DestinationLeft(This,DestinationLeft) (This)->lpVtbl->put_DestinationLeft(This,DestinationLeft)")
cpp_quote("#define IBasicVideo_get_DestinationLeft(This,pDestinationLeft) (This)->lpVtbl->get_DestinationLeft(This,pDestinationLeft)")
cpp_quote("#define IBasicVideo_put_DestinationWidth(This,DestinationWidth) (This)->lpVtbl->put_DestinationWidth(This,DestinationWidth)")
cpp_quote("#define IBasicVideo_get_DestinationWidth(This,pDestinationWidth) (This)->lpVtbl->get_DestinationWidth(This,pDestinationWidth)")
cpp_quote("#define IBasicVideo_put_DestinationTop(This,DestinationTop) (This)->lpVtbl->put_DestinationTop(This,DestinationTop)")
cpp_quote("#define IBasicVideo_get_DestinationTop(This,pDestinationTop) (This)->lpVtbl->get_DestinationTop(This,pDestinationTop)")
cpp_quote("#define IBasicVideo_put_DestinationHeight(This,DestinationHeight) (This)->lpVtbl->put_DestinationHeight(This,DestinationHeight)")
cpp_quote("#define IBasicVideo_get_DestinationHeight(This,pDestinationHeight) (This)->lpVtbl->get_DestinationHeight(This,pDestinationHeight)")
cpp_quote("#define IBasicVideo_SetSourcePosition(This,Left,Top,Width,Height) (This)->lpVtbl->SetSourcePosition(This,Left,Top,Width,Height)")
cpp_quote("#define IBasicVideo_GetSourcePosition(This,pLeft,pTop,pWidth,pHeight) (This)->lpVtbl->GetSourcePosition(This,pLeft,pTop,pWidth,pHeight)")
cpp_quote("#define IBasicVideo_SetDefaultSourcePosition(This) (This)->lpVtbl->SetDefaultSourcePosition(This)")
cpp_quote("#define IBasicVideo_SetDestinationPosition(This,Left,Top,Width,Height) (This)->lpVtbl->SetDestinationPosition(This,Left,Top,Width,Height)")
cpp_quote("#define IBasicVideo_GetDestinationPosition(This,pLeft,pTop,pWidth,pHeight) (This)->lpVtbl->GetDestinationPosition(This,pLeft,pTop,pWidth,pHeight)")
cpp_quote("#define IBasicVideo_SetDefaultDestinationPosition(This) (This)->lpVtbl->SetDefaultDestinationPosition(This)")
cpp_quote("#define IBasicVideo_GetVideoSize(This,pWidth,pHeight) (This)->lpVtbl->GetVideoSize(This,pWidth,pHeight)")
cpp_quote("#define IBasicVideo_GetVideoPaletteEntries(This,StartIndex,Entries,pRetrieved,pPalette) (This)->lpVtbl->GetVideoPaletteEntries(This,StartIndex,Entries,pRetrieved,pPalette)")
cpp_quote("#define IBasicVideo_GetCurrentImage(This,pBufferSize,pDIBImage) (This)->lpVtbl->GetCurrentImage(This,pBufferSize,pDIBImage)")
cpp_quote("#define IBasicVideo_IsUsingDefaultSource(This) (This)->lpVtbl->IsUsingDefaultSource(This)")
cpp_quote("#define IBasicVideo_IsUsingDefaultDestination(This) (This)->lpVtbl->IsUsingDefaultDestination(This)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_AvgTimePerFrame_Proxy(IBasicVideo *This,REFTIME *pAvgTimePerFrame);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_AvgTimePerFrame_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_BitRate_Proxy(IBasicVideo *This,__LONG32 *pBitRate);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_BitRate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_BitErrorRate_Proxy(IBasicVideo *This,__LONG32 *pBitErrorRate);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_BitErrorRate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_VideoWidth_Proxy(IBasicVideo *This,__LONG32 *pVideoWidth);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_VideoWidth_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_VideoHeight_Proxy(IBasicVideo *This,__LONG32 *pVideoHeight);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_VideoHeight_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_put_SourceLeft_Proxy(IBasicVideo *This,__LONG32 SourceLeft);")
cpp_quote("  void __RPC_STUB IBasicVideo_put_SourceLeft_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_SourceLeft_Proxy(IBasicVideo *This,__LONG32 *pSourceLeft);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_SourceLeft_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_put_SourceWidth_Proxy(IBasicVideo *This,__LONG32 SourceWidth);")
cpp_quote("  void __RPC_STUB IBasicVideo_put_SourceWidth_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_SourceWidth_Proxy(IBasicVideo *This,__LONG32 *pSourceWidth);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_SourceWidth_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_put_SourceTop_Proxy(IBasicVideo *This,__LONG32 SourceTop);")
cpp_quote("  void __RPC_STUB IBasicVideo_put_SourceTop_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_SourceTop_Proxy(IBasicVideo *This,__LONG32 *pSourceTop);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_SourceTop_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_put_SourceHeight_Proxy(IBasicVideo *This,__LONG32 SourceHeight);")
cpp_quote("  void __RPC_STUB IBasicVideo_put_SourceHeight_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_SourceHeight_Proxy(IBasicVideo *This,__LONG32 *pSourceHeight);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_SourceHeight_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_put_DestinationLeft_Proxy(IBasicVideo *This,__LONG32 DestinationLeft);")
cpp_quote("  void __RPC_STUB IBasicVideo_put_DestinationLeft_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_DestinationLeft_Proxy(IBasicVideo *This,__LONG32 *pDestinationLeft);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_DestinationLeft_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_put_DestinationWidth_Proxy(IBasicVideo *This,__LONG32 DestinationWidth);")
cpp_quote("  void __RPC_STUB IBasicVideo_put_DestinationWidth_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_DestinationWidth_Proxy(IBasicVideo *This,__LONG32 *pDestinationWidth);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_DestinationWidth_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_put_DestinationTop_Proxy(IBasicVideo *This,__LONG32 DestinationTop);")
cpp_quote("  void __RPC_STUB IBasicVideo_put_DestinationTop_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_DestinationTop_Proxy(IBasicVideo *This,__LONG32 *pDestinationTop);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_DestinationTop_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_put_DestinationHeight_Proxy(IBasicVideo *This,__LONG32 DestinationHeight);")
cpp_quote("  void __RPC_STUB IBasicVideo_put_DestinationHeight_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_get_DestinationHeight_Proxy(IBasicVideo *This,__LONG32 *pDestinationHeight);")
cpp_quote("  void __RPC_STUB IBasicVideo_get_DestinationHeight_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_SetSourcePosition_Proxy(IBasicVideo *This,__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height);")
cpp_quote("  void __RPC_STUB IBasicVideo_SetSourcePosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_GetSourcePosition_Proxy(IBasicVideo *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("  void __RPC_STUB IBasicVideo_GetSourcePosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_SetDefaultSourcePosition_Proxy(IBasicVideo *This);")
cpp_quote("  void __RPC_STUB IBasicVideo_SetDefaultSourcePosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_SetDestinationPosition_Proxy(IBasicVideo *This,__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height);")
cpp_quote("  void __RPC_STUB IBasicVideo_SetDestinationPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_GetDestinationPosition_Proxy(IBasicVideo *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("  void __RPC_STUB IBasicVideo_GetDestinationPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_SetDefaultDestinationPosition_Proxy(IBasicVideo *This);")
cpp_quote("  void __RPC_STUB IBasicVideo_SetDefaultDestinationPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_GetVideoSize_Proxy(IBasicVideo *This,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("  void __RPC_STUB IBasicVideo_GetVideoSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_GetVideoPaletteEntries_Proxy(IBasicVideo *This,__LONG32 StartIndex,__LONG32 Entries,__LONG32 *pRetrieved,__LONG32 *pPalette);")
cpp_quote("  void __RPC_STUB IBasicVideo_GetVideoPaletteEntries_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_GetCurrentImage_Proxy(IBasicVideo *This,__LONG32 *pBufferSize,__LONG32 *pDIBImage);")
cpp_quote("  void __RPC_STUB IBasicVideo_GetCurrentImage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_IsUsingDefaultSource_Proxy(IBasicVideo *This);")
cpp_quote("  void __RPC_STUB IBasicVideo_IsUsingDefaultSource_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBasicVideo_IsUsingDefaultDestination_Proxy(IBasicVideo *This);")
cpp_quote("  void __RPC_STUB IBasicVideo_IsUsingDefaultDestination_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IBasicVideo2_INTERFACE_DEFINED__")
cpp_quote("#define __IBasicVideo2_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IBasicVideo2,0x329bb360,0xf6ea,0x11d1,0x90,0x38,0x00,0xa0,0xc9,0x69,0x72,0x98);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IBasicVideo2 : public IBasicVideo {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetPreferredAspectRatio(__LONG32 *plAspectX,__LONG32 *plAspectY) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IBasicVideo2Vtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IBasicVideo2 *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IBasicVideo2 *This);")
cpp_quote("      ULONG (WINAPI *Release)(IBasicVideo2 *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IBasicVideo2 *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IBasicVideo2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IBasicVideo2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IBasicVideo2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *get_AvgTimePerFrame)(IBasicVideo2 *This,REFTIME *pAvgTimePerFrame);")
cpp_quote("      HRESULT (WINAPI *get_BitRate)(IBasicVideo2 *This,__LONG32 *pBitRate);")
cpp_quote("      HRESULT (WINAPI *get_BitErrorRate)(IBasicVideo2 *This,__LONG32 *pBitErrorRate);")
cpp_quote("      HRESULT (WINAPI *get_VideoWidth)(IBasicVideo2 *This,__LONG32 *pVideoWidth);")
cpp_quote("      HRESULT (WINAPI *get_VideoHeight)(IBasicVideo2 *This,__LONG32 *pVideoHeight);")
cpp_quote("      HRESULT (WINAPI *put_SourceLeft)(IBasicVideo2 *This,__LONG32 SourceLeft);")
cpp_quote("      HRESULT (WINAPI *get_SourceLeft)(IBasicVideo2 *This,__LONG32 *pSourceLeft);")
cpp_quote("      HRESULT (WINAPI *put_SourceWidth)(IBasicVideo2 *This,__LONG32 SourceWidth);")
cpp_quote("      HRESULT (WINAPI *get_SourceWidth)(IBasicVideo2 *This,__LONG32 *pSourceWidth);")
cpp_quote("      HRESULT (WINAPI *put_SourceTop)(IBasicVideo2 *This,__LONG32 SourceTop);")
cpp_quote("      HRESULT (WINAPI *get_SourceTop)(IBasicVideo2 *This,__LONG32 *pSourceTop);")
cpp_quote("      HRESULT (WINAPI *put_SourceHeight)(IBasicVideo2 *This,__LONG32 SourceHeight);")
cpp_quote("      HRESULT (WINAPI *get_SourceHeight)(IBasicVideo2 *This,__LONG32 *pSourceHeight);")
cpp_quote("      HRESULT (WINAPI *put_DestinationLeft)(IBasicVideo2 *This,__LONG32 DestinationLeft);")
cpp_quote("      HRESULT (WINAPI *get_DestinationLeft)(IBasicVideo2 *This,__LONG32 *pDestinationLeft);")
cpp_quote("      HRESULT (WINAPI *put_DestinationWidth)(IBasicVideo2 *This,__LONG32 DestinationWidth);")
cpp_quote("      HRESULT (WINAPI *get_DestinationWidth)(IBasicVideo2 *This,__LONG32 *pDestinationWidth);")
cpp_quote("      HRESULT (WINAPI *put_DestinationTop)(IBasicVideo2 *This,__LONG32 DestinationTop);")
cpp_quote("      HRESULT (WINAPI *get_DestinationTop)(IBasicVideo2 *This,__LONG32 *pDestinationTop);")
cpp_quote("      HRESULT (WINAPI *put_DestinationHeight)(IBasicVideo2 *This,__LONG32 DestinationHeight);")
cpp_quote("      HRESULT (WINAPI *get_DestinationHeight)(IBasicVideo2 *This,__LONG32 *pDestinationHeight);")
cpp_quote("      HRESULT (WINAPI *SetSourcePosition)(IBasicVideo2 *This,__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height);")
cpp_quote("      HRESULT (WINAPI *GetSourcePosition)(IBasicVideo2 *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *SetDefaultSourcePosition)(IBasicVideo2 *This);")
cpp_quote("      HRESULT (WINAPI *SetDestinationPosition)(IBasicVideo2 *This,__LONG32 Left,__LONG32 Top,__LONG32 Width,__LONG32 Height);")
cpp_quote("      HRESULT (WINAPI *GetDestinationPosition)(IBasicVideo2 *This,__LONG32 *pLeft,__LONG32 *pTop,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *SetDefaultDestinationPosition)(IBasicVideo2 *This);")
cpp_quote("      HRESULT (WINAPI *GetVideoSize)(IBasicVideo2 *This,__LONG32 *pWidth,__LONG32 *pHeight);")
cpp_quote("      HRESULT (WINAPI *GetVideoPaletteEntries)(IBasicVideo2 *This,__LONG32 StartIndex,__LONG32 Entries,__LONG32 *pRetrieved,__LONG32 *pPalette);")
cpp_quote("      HRESULT (WINAPI *GetCurrentImage)(IBasicVideo2 *This,__LONG32 *pBufferSize,__LONG32 *pDIBImage);")
cpp_quote("      HRESULT (WINAPI *IsUsingDefaultSource)(IBasicVideo2 *This);")
cpp_quote("      HRESULT (WINAPI *IsUsingDefaultDestination)(IBasicVideo2 *This);")
cpp_quote("      HRESULT (WINAPI *GetPreferredAspectRatio)(IBasicVideo2 *This,__LONG32 *plAspectX,__LONG32 *plAspectY);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IBasicVideo2Vtbl;")
cpp_quote("  struct IBasicVideo2 {")
cpp_quote("    CONST_VTBL struct IBasicVideo2Vtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IBasicVideo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IBasicVideo2_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IBasicVideo2_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IBasicVideo2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IBasicVideo2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IBasicVideo2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IBasicVideo2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IBasicVideo2_get_AvgTimePerFrame(This,pAvgTimePerFrame) (This)->lpVtbl->get_AvgTimePerFrame(This,pAvgTimePerFrame)")
cpp_quote("#define IBasicVideo2_get_BitRate(This,pBitRate) (This)->lpVtbl->get_BitRate(This,pBitRate)")
cpp_quote("#define IBasicVideo2_get_BitErrorRate(This,pBitErrorRate) (This)->lpVtbl->get_BitErrorRate(This,pBitErrorRate)")
cpp_quote("#define IBasicVideo2_get_VideoWidth(This,pVideoWidth) (This)->lpVtbl->get_VideoWidth(This,pVideoWidth)")
cpp_quote("#define IBasicVideo2_get_VideoHeight(This,pVideoHeight) (This)->lpVtbl->get_VideoHeight(This,pVideoHeight)")
cpp_quote("#define IBasicVideo2_put_SourceLeft(This,SourceLeft) (This)->lpVtbl->put_SourceLeft(This,SourceLeft)")
cpp_quote("#define IBasicVideo2_get_SourceLeft(This,pSourceLeft) (This)->lpVtbl->get_SourceLeft(This,pSourceLeft)")
cpp_quote("#define IBasicVideo2_put_SourceWidth(This,SourceWidth) (This)->lpVtbl->put_SourceWidth(This,SourceWidth)")
cpp_quote("#define IBasicVideo2_get_SourceWidth(This,pSourceWidth) (This)->lpVtbl->get_SourceWidth(This,pSourceWidth)")
cpp_quote("#define IBasicVideo2_put_SourceTop(This,SourceTop) (This)->lpVtbl->put_SourceTop(This,SourceTop)")
cpp_quote("#define IBasicVideo2_get_SourceTop(This,pSourceTop) (This)->lpVtbl->get_SourceTop(This,pSourceTop)")
cpp_quote("#define IBasicVideo2_put_SourceHeight(This,SourceHeight) (This)->lpVtbl->put_SourceHeight(This,SourceHeight)")
cpp_quote("#define IBasicVideo2_get_SourceHeight(This,pSourceHeight) (This)->lpVtbl->get_SourceHeight(This,pSourceHeight)")
cpp_quote("#define IBasicVideo2_put_DestinationLeft(This,DestinationLeft) (This)->lpVtbl->put_DestinationLeft(This,DestinationLeft)")
cpp_quote("#define IBasicVideo2_get_DestinationLeft(This,pDestinationLeft) (This)->lpVtbl->get_DestinationLeft(This,pDestinationLeft)")
cpp_quote("#define IBasicVideo2_put_DestinationWidth(This,DestinationWidth) (This)->lpVtbl->put_DestinationWidth(This,DestinationWidth)")
cpp_quote("#define IBasicVideo2_get_DestinationWidth(This,pDestinationWidth) (This)->lpVtbl->get_DestinationWidth(This,pDestinationWidth)")
cpp_quote("#define IBasicVideo2_put_DestinationTop(This,DestinationTop) (This)->lpVtbl->put_DestinationTop(This,DestinationTop)")
cpp_quote("#define IBasicVideo2_get_DestinationTop(This,pDestinationTop) (This)->lpVtbl->get_DestinationTop(This,pDestinationTop)")
cpp_quote("#define IBasicVideo2_put_DestinationHeight(This,DestinationHeight) (This)->lpVtbl->put_DestinationHeight(This,DestinationHeight)")
cpp_quote("#define IBasicVideo2_get_DestinationHeight(This,pDestinationHeight) (This)->lpVtbl->get_DestinationHeight(This,pDestinationHeight)")
cpp_quote("#define IBasicVideo2_SetSourcePosition(This,Left,Top,Width,Height) (This)->lpVtbl->SetSourcePosition(This,Left,Top,Width,Height)")
cpp_quote("#define IBasicVideo2_GetSourcePosition(This,pLeft,pTop,pWidth,pHeight) (This)->lpVtbl->GetSourcePosition(This,pLeft,pTop,pWidth,pHeight)")
cpp_quote("#define IBasicVideo2_SetDefaultSourcePosition(This) (This)->lpVtbl->SetDefaultSourcePosition(This)")
cpp_quote("#define IBasicVideo2_SetDestinationPosition(This,Left,Top,Width,Height) (This)->lpVtbl->SetDestinationPosition(This,Left,Top,Width,Height)")
cpp_quote("#define IBasicVideo2_GetDestinationPosition(This,pLeft,pTop,pWidth,pHeight) (This)->lpVtbl->GetDestinationPosition(This,pLeft,pTop,pWidth,pHeight)")
cpp_quote("#define IBasicVideo2_SetDefaultDestinationPosition(This) (This)->lpVtbl->SetDefaultDestinationPosition(This)")
cpp_quote("#define IBasicVideo2_GetVideoSize(This,pWidth,pHeight) (This)->lpVtbl->GetVideoSize(This,pWidth,pHeight)")
cpp_quote("#define IBasicVideo2_GetVideoPaletteEntries(This,StartIndex,Entries,pRetrieved,pPalette) (This)->lpVtbl->GetVideoPaletteEntries(This,StartIndex,Entries,pRetrieved,pPalette)")
cpp_quote("#define IBasicVideo2_GetCurrentImage(This,pBufferSize,pDIBImage) (This)->lpVtbl->GetCurrentImage(This,pBufferSize,pDIBImage)")
cpp_quote("#define IBasicVideo2_IsUsingDefaultSource(This) (This)->lpVtbl->IsUsingDefaultSource(This)")
cpp_quote("#define IBasicVideo2_IsUsingDefaultDestination(This) (This)->lpVtbl->IsUsingDefaultDestination(This)")
cpp_quote("#define IBasicVideo2_GetPreferredAspectRatio(This,plAspectX,plAspectY) (This)->lpVtbl->GetPreferredAspectRatio(This,plAspectX,plAspectY)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IBasicVideo2_GetPreferredAspectRatio_Proxy(IBasicVideo2 *This,__LONG32 *plAspectX,__LONG32 *plAspectY);")
cpp_quote("  void __RPC_STUB IBasicVideo2_GetPreferredAspectRatio_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDeferredCommand_INTERFACE_DEFINED__")
cpp_quote("#define __IDeferredCommand_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IDeferredCommand,0x56a868b8,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDeferredCommand : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI Cancel(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI Confidence(LONG *pConfidence) = 0;")
cpp_quote("    virtual HRESULT WINAPI Postpone(REFTIME newtime) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetHResult(HRESULT *phrResult) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDeferredCommandVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDeferredCommand *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDeferredCommand *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDeferredCommand *This);")
cpp_quote("      HRESULT (WINAPI *Cancel)(IDeferredCommand *This);")
cpp_quote("      HRESULT (WINAPI *Confidence)(IDeferredCommand *This,LONG *pConfidence);")
cpp_quote("      HRESULT (WINAPI *Postpone)(IDeferredCommand *This,REFTIME newtime);")
cpp_quote("      HRESULT (WINAPI *GetHResult)(IDeferredCommand *This,HRESULT *phrResult);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDeferredCommandVtbl;")
cpp_quote("  struct IDeferredCommand {")
cpp_quote("    CONST_VTBL struct IDeferredCommandVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDeferredCommand_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDeferredCommand_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDeferredCommand_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDeferredCommand_Cancel(This) (This)->lpVtbl->Cancel(This)")
cpp_quote("#define IDeferredCommand_Confidence(This,pConfidence) (This)->lpVtbl->Confidence(This,pConfidence)")
cpp_quote("#define IDeferredCommand_Postpone(This,newtime) (This)->lpVtbl->Postpone(This,newtime)")
cpp_quote("#define IDeferredCommand_GetHResult(This,phrResult) (This)->lpVtbl->GetHResult(This,phrResult)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDeferredCommand_Cancel_Proxy(IDeferredCommand *This);")
cpp_quote("  void __RPC_STUB IDeferredCommand_Cancel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDeferredCommand_Confidence_Proxy(IDeferredCommand *This,LONG *pConfidence);")
cpp_quote("  void __RPC_STUB IDeferredCommand_Confidence_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDeferredCommand_Postpone_Proxy(IDeferredCommand *This,REFTIME newtime);")
cpp_quote("  void __RPC_STUB IDeferredCommand_Postpone_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDeferredCommand_GetHResult_Proxy(IDeferredCommand *This,HRESULT *phrResult);")
cpp_quote("  void __RPC_STUB IDeferredCommand_GetHResult_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IQueueCommand_INTERFACE_DEFINED__")
cpp_quote("#define __IQueueCommand_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IQueueCommand,0x56a868b7,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IQueueCommand : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI InvokeAtStreamTime(IDeferredCommand **pCmd,REFTIME time,GUID *iid,__LONG32 dispidMethod,short wFlags,__LONG32 cArgs,VARIANT *pDispParams,VARIANT *pvarResult,short *puArgErr) = 0;")
cpp_quote("    virtual HRESULT WINAPI InvokeAtPresentationTime(IDeferredCommand **pCmd,REFTIME time,GUID *iid,__LONG32 dispidMethod,short wFlags,__LONG32 cArgs,VARIANT *pDispParams,VARIANT *pvarResult,short *puArgErr) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IQueueCommandVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IQueueCommand *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IQueueCommand *This);")
cpp_quote("      ULONG (WINAPI *Release)(IQueueCommand *This);")
cpp_quote("      HRESULT (WINAPI *InvokeAtStreamTime)(IQueueCommand *This,IDeferredCommand **pCmd,REFTIME time,GUID *iid,__LONG32 dispidMethod,short wFlags,__LONG32 cArgs,VARIANT *pDispParams,VARIANT *pvarResult,short *puArgErr);")
cpp_quote("      HRESULT (WINAPI *InvokeAtPresentationTime)(IQueueCommand *This,IDeferredCommand **pCmd,REFTIME time,GUID *iid,__LONG32 dispidMethod,short wFlags,__LONG32 cArgs,VARIANT *pDispParams,VARIANT *pvarResult,short *puArgErr);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IQueueCommandVtbl;")
cpp_quote("  struct IQueueCommand {")
cpp_quote("    CONST_VTBL struct IQueueCommandVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IQueueCommand_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IQueueCommand_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IQueueCommand_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IQueueCommand_InvokeAtStreamTime(This,pCmd,time,iid,dispidMethod,wFlags,cArgs,pDispParams,pvarResult,puArgErr) (This)->lpVtbl->InvokeAtStreamTime(This,pCmd,time,iid,dispidMethod,wFlags,cArgs,pDispParams,pvarResult,puArgErr)")
cpp_quote("#define IQueueCommand_InvokeAtPresentationTime(This,pCmd,time,iid,dispidMethod,wFlags,cArgs,pDispParams,pvarResult,puArgErr) (This)->lpVtbl->InvokeAtPresentationTime(This,pCmd,time,iid,dispidMethod,wFlags,cArgs,pDispParams,pvarResult,puArgErr)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IQueueCommand_InvokeAtStreamTime_Proxy(IQueueCommand *This,IDeferredCommand **pCmd,REFTIME time,GUID *iid,__LONG32 dispidMethod,short wFlags,__LONG32 cArgs,VARIANT *pDispParams,VARIANT *pvarResult,short *puArgErr);")
cpp_quote("  void __RPC_STUB IQueueCommand_InvokeAtStreamTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IQueueCommand_InvokeAtPresentationTime_Proxy(IQueueCommand *This,IDeferredCommand **pCmd,REFTIME time,GUID *iid,__LONG32 dispidMethod,short wFlags,__LONG32 cArgs,VARIANT *pDispParams,VARIANT *pvarResult,short *puArgErr);")
cpp_quote("  void __RPC_STUB IQueueCommand_InvokeAtPresentationTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  DEFINE_GUID(CLSID_FilgraphManager,0xe436ebb3,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#ifdef __cplusplus")
cpp_quote("  class FilgraphManager;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IFilterInfo_INTERFACE_DEFINED__")
cpp_quote("#define __IFilterInfo_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IFilterInfo,0x56a868ba,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IFilterInfo : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI FindPin(BSTR strPinID,IDispatch **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Name(BSTR *strName) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_VendorInfo(BSTR *strVendorInfo) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Filter(IUnknown **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Pins(IDispatch **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_IsFileSource(LONG *pbIsSource) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Filename(BSTR *pstrFilename) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Filename(BSTR strFilename) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IFilterInfoVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IFilterInfo *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IFilterInfo *This);")
cpp_quote("      ULONG (WINAPI *Release)(IFilterInfo *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IFilterInfo *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IFilterInfo *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IFilterInfo *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IFilterInfo *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *FindPin)(IFilterInfo *This,BSTR strPinID,IDispatch **ppUnk);")
cpp_quote("      HRESULT (WINAPI *get_Name)(IFilterInfo *This,BSTR *strName);")
cpp_quote("      HRESULT (WINAPI *get_VendorInfo)(IFilterInfo *This,BSTR *strVendorInfo);")
cpp_quote("      HRESULT (WINAPI *get_Filter)(IFilterInfo *This,IUnknown **ppUnk);")
cpp_quote("      HRESULT (WINAPI *get_Pins)(IFilterInfo *This,IDispatch **ppUnk);")
cpp_quote("      HRESULT (WINAPI *get_IsFileSource)(IFilterInfo *This,LONG *pbIsSource);")
cpp_quote("      HRESULT (WINAPI *get_Filename)(IFilterInfo *This,BSTR *pstrFilename);")
cpp_quote("      HRESULT (WINAPI *put_Filename)(IFilterInfo *This,BSTR strFilename);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IFilterInfoVtbl;")
cpp_quote("  struct IFilterInfo {")
cpp_quote("    CONST_VTBL struct IFilterInfoVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IFilterInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IFilterInfo_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IFilterInfo_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IFilterInfo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IFilterInfo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IFilterInfo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IFilterInfo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IFilterInfo_FindPin(This,strPinID,ppUnk) (This)->lpVtbl->FindPin(This,strPinID,ppUnk)")
cpp_quote("#define IFilterInfo_get_Name(This,strName) (This)->lpVtbl->get_Name(This,strName)")
cpp_quote("#define IFilterInfo_get_VendorInfo(This,strVendorInfo) (This)->lpVtbl->get_VendorInfo(This,strVendorInfo)")
cpp_quote("#define IFilterInfo_get_Filter(This,ppUnk) (This)->lpVtbl->get_Filter(This,ppUnk)")
cpp_quote("#define IFilterInfo_get_Pins(This,ppUnk) (This)->lpVtbl->get_Pins(This,ppUnk)")
cpp_quote("#define IFilterInfo_get_IsFileSource(This,pbIsSource) (This)->lpVtbl->get_IsFileSource(This,pbIsSource)")
cpp_quote("#define IFilterInfo_get_Filename(This,pstrFilename) (This)->lpVtbl->get_Filename(This,pstrFilename)")
cpp_quote("#define IFilterInfo_put_Filename(This,strFilename) (This)->lpVtbl->put_Filename(This,strFilename)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IFilterInfo_FindPin_Proxy(IFilterInfo *This,BSTR strPinID,IDispatch **ppUnk);")
cpp_quote("  void __RPC_STUB IFilterInfo_FindPin_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IFilterInfo_get_Name_Proxy(IFilterInfo *This,BSTR *strName);")
cpp_quote("  void __RPC_STUB IFilterInfo_get_Name_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IFilterInfo_get_VendorInfo_Proxy(IFilterInfo *This,BSTR *strVendorInfo);")
cpp_quote("  void __RPC_STUB IFilterInfo_get_VendorInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IFilterInfo_get_Filter_Proxy(IFilterInfo *This,IUnknown **ppUnk);")
cpp_quote("  void __RPC_STUB IFilterInfo_get_Filter_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IFilterInfo_get_Pins_Proxy(IFilterInfo *This,IDispatch **ppUnk);")
cpp_quote("  void __RPC_STUB IFilterInfo_get_Pins_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IFilterInfo_get_IsFileSource_Proxy(IFilterInfo *This,LONG *pbIsSource);")
cpp_quote("  void __RPC_STUB IFilterInfo_get_IsFileSource_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IFilterInfo_get_Filename_Proxy(IFilterInfo *This,BSTR *pstrFilename);")
cpp_quote("  void __RPC_STUB IFilterInfo_get_Filename_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IFilterInfo_put_Filename_Proxy(IFilterInfo *This,BSTR strFilename);")
cpp_quote("  void __RPC_STUB IFilterInfo_put_Filename_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IRegFilterInfo_INTERFACE_DEFINED__")
cpp_quote("#define __IRegFilterInfo_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IRegFilterInfo,0x56a868bb,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IRegFilterInfo : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_Name(BSTR *strName) = 0;")
cpp_quote("    virtual HRESULT WINAPI Filter(IDispatch **ppUnk) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IRegFilterInfoVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IRegFilterInfo *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IRegFilterInfo *This);")
cpp_quote("      ULONG (WINAPI *Release)(IRegFilterInfo *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IRegFilterInfo *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IRegFilterInfo *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IRegFilterInfo *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IRegFilterInfo *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *get_Name)(IRegFilterInfo *This,BSTR *strName);")
cpp_quote("      HRESULT (WINAPI *Filter)(IRegFilterInfo *This,IDispatch **ppUnk);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IRegFilterInfoVtbl;")
cpp_quote("  struct IRegFilterInfo {")
cpp_quote("    CONST_VTBL struct IRegFilterInfoVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IRegFilterInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IRegFilterInfo_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IRegFilterInfo_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IRegFilterInfo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IRegFilterInfo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IRegFilterInfo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IRegFilterInfo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IRegFilterInfo_get_Name(This,strName) (This)->lpVtbl->get_Name(This,strName)")
cpp_quote("#define IRegFilterInfo_Filter(This,ppUnk) (This)->lpVtbl->Filter(This,ppUnk)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IRegFilterInfo_get_Name_Proxy(IRegFilterInfo *This,BSTR *strName);")
cpp_quote("  void __RPC_STUB IRegFilterInfo_get_Name_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IRegFilterInfo_Filter_Proxy(IRegFilterInfo *This,IDispatch **ppUnk);")
cpp_quote("  void __RPC_STUB IRegFilterInfo_Filter_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMediaTypeInfo_INTERFACE_DEFINED__")
cpp_quote("#define __IMediaTypeInfo_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IMediaTypeInfo,0x56a868bc,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IMediaTypeInfo : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_Type(BSTR *strType) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Subtype(BSTR *strType) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IMediaTypeInfoVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IMediaTypeInfo *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IMediaTypeInfo *This);")
cpp_quote("      ULONG (WINAPI *Release)(IMediaTypeInfo *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IMediaTypeInfo *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IMediaTypeInfo *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IMediaTypeInfo *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IMediaTypeInfo *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *get_Type)(IMediaTypeInfo *This,BSTR *strType);")
cpp_quote("      HRESULT (WINAPI *get_Subtype)(IMediaTypeInfo *This,BSTR *strType);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IMediaTypeInfoVtbl;")
cpp_quote("  struct IMediaTypeInfo {")
cpp_quote("    CONST_VTBL struct IMediaTypeInfoVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IMediaTypeInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IMediaTypeInfo_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IMediaTypeInfo_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IMediaTypeInfo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IMediaTypeInfo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IMediaTypeInfo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IMediaTypeInfo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IMediaTypeInfo_get_Type(This,strType) (This)->lpVtbl->get_Type(This,strType)")
cpp_quote("#define IMediaTypeInfo_get_Subtype(This,strType) (This)->lpVtbl->get_Subtype(This,strType)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IMediaTypeInfo_get_Type_Proxy(IMediaTypeInfo *This,BSTR *strType);")
cpp_quote("  void __RPC_STUB IMediaTypeInfo_get_Type_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMediaTypeInfo_get_Subtype_Proxy(IMediaTypeInfo *This,BSTR *strType);")
cpp_quote("  void __RPC_STUB IMediaTypeInfo_get_Subtype_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IPinInfo_INTERFACE_DEFINED__")
cpp_quote("#define __IPinInfo_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IPinInfo,0x56a868bd,0x0ad4,0x11ce,0xb0,0x3a,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IPinInfo : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_Pin(IUnknown **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_ConnectedTo(IDispatch **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_ConnectionMediaType(IDispatch **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_FilterInfo(IDispatch **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Name(BSTR *ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Direction(LONG *ppDirection) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_PinID(BSTR *strPinID) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_MediaTypes(IDispatch **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI Connect(IUnknown *pPin) = 0;")
cpp_quote("    virtual HRESULT WINAPI ConnectDirect(IUnknown *pPin) = 0;")
cpp_quote("    virtual HRESULT WINAPI ConnectWithType(IUnknown *pPin,IDispatch *pMediaType) = 0;")
cpp_quote("    virtual HRESULT WINAPI Disconnect(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI Render(void) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IPinInfoVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IPinInfo *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IPinInfo *This);")
cpp_quote("      ULONG (WINAPI *Release)(IPinInfo *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IPinInfo *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IPinInfo *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IPinInfo *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IPinInfo *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *get_Pin)(IPinInfo *This,IUnknown **ppUnk);")
cpp_quote("      HRESULT (WINAPI *get_ConnectedTo)(IPinInfo *This,IDispatch **ppUnk);")
cpp_quote("      HRESULT (WINAPI *get_ConnectionMediaType)(IPinInfo *This,IDispatch **ppUnk);")
cpp_quote("      HRESULT (WINAPI *get_FilterInfo)(IPinInfo *This,IDispatch **ppUnk);")
cpp_quote("      HRESULT (WINAPI *get_Name)(IPinInfo *This,BSTR *ppUnk);")
cpp_quote("      HRESULT (WINAPI *get_Direction)(IPinInfo *This,LONG *ppDirection);")
cpp_quote("      HRESULT (WINAPI *get_PinID)(IPinInfo *This,BSTR *strPinID);")
cpp_quote("      HRESULT (WINAPI *get_MediaTypes)(IPinInfo *This,IDispatch **ppUnk);")
cpp_quote("      HRESULT (WINAPI *Connect)(IPinInfo *This,IUnknown *pPin);")
cpp_quote("      HRESULT (WINAPI *ConnectDirect)(IPinInfo *This,IUnknown *pPin);")
cpp_quote("      HRESULT (WINAPI *ConnectWithType)(IPinInfo *This,IUnknown *pPin,IDispatch *pMediaType);")
cpp_quote("      HRESULT (WINAPI *Disconnect)(IPinInfo *This);")
cpp_quote("      HRESULT (WINAPI *Render)(IPinInfo *This);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IPinInfoVtbl;")
cpp_quote("  struct IPinInfo {")
cpp_quote("    CONST_VTBL struct IPinInfoVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IPinInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IPinInfo_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IPinInfo_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IPinInfo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IPinInfo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IPinInfo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IPinInfo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IPinInfo_get_Pin(This,ppUnk) (This)->lpVtbl->get_Pin(This,ppUnk)")
cpp_quote("#define IPinInfo_get_ConnectedTo(This,ppUnk) (This)->lpVtbl->get_ConnectedTo(This,ppUnk)")
cpp_quote("#define IPinInfo_get_ConnectionMediaType(This,ppUnk) (This)->lpVtbl->get_ConnectionMediaType(This,ppUnk)")
cpp_quote("#define IPinInfo_get_FilterInfo(This,ppUnk) (This)->lpVtbl->get_FilterInfo(This,ppUnk)")
cpp_quote("#define IPinInfo_get_Name(This,ppUnk) (This)->lpVtbl->get_Name(This,ppUnk)")
cpp_quote("#define IPinInfo_get_Direction(This,ppDirection) (This)->lpVtbl->get_Direction(This,ppDirection)")
cpp_quote("#define IPinInfo_get_PinID(This,strPinID) (This)->lpVtbl->get_PinID(This,strPinID)")
cpp_quote("#define IPinInfo_get_MediaTypes(This,ppUnk) (This)->lpVtbl->get_MediaTypes(This,ppUnk)")
cpp_quote("#define IPinInfo_Connect(This,pPin) (This)->lpVtbl->Connect(This,pPin)")
cpp_quote("#define IPinInfo_ConnectDirect(This,pPin) (This)->lpVtbl->ConnectDirect(This,pPin)")
cpp_quote("#define IPinInfo_ConnectWithType(This,pPin,pMediaType) (This)->lpVtbl->ConnectWithType(This,pPin,pMediaType)")
cpp_quote("#define IPinInfo_Disconnect(This) (This)->lpVtbl->Disconnect(This)")
cpp_quote("#define IPinInfo_Render(This) (This)->lpVtbl->Render(This)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IPinInfo_get_Pin_Proxy(IPinInfo *This,IUnknown **ppUnk);")
cpp_quote("  void __RPC_STUB IPinInfo_get_Pin_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_get_ConnectedTo_Proxy(IPinInfo *This,IDispatch **ppUnk);")
cpp_quote("  void __RPC_STUB IPinInfo_get_ConnectedTo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_get_ConnectionMediaType_Proxy(IPinInfo *This,IDispatch **ppUnk);")
cpp_quote("  void __RPC_STUB IPinInfo_get_ConnectionMediaType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_get_FilterInfo_Proxy(IPinInfo *This,IDispatch **ppUnk);")
cpp_quote("  void __RPC_STUB IPinInfo_get_FilterInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_get_Name_Proxy(IPinInfo *This,BSTR *ppUnk);")
cpp_quote("  void __RPC_STUB IPinInfo_get_Name_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_get_Direction_Proxy(IPinInfo *This,LONG *ppDirection);")
cpp_quote("  void __RPC_STUB IPinInfo_get_Direction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_get_PinID_Proxy(IPinInfo *This,BSTR *strPinID);")
cpp_quote("  void __RPC_STUB IPinInfo_get_PinID_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_get_MediaTypes_Proxy(IPinInfo *This,IDispatch **ppUnk);")
cpp_quote("  void __RPC_STUB IPinInfo_get_MediaTypes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_Connect_Proxy(IPinInfo *This,IUnknown *pPin);")
cpp_quote("  void __RPC_STUB IPinInfo_Connect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_ConnectDirect_Proxy(IPinInfo *This,IUnknown *pPin);")
cpp_quote("  void __RPC_STUB IPinInfo_ConnectDirect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_ConnectWithType_Proxy(IPinInfo *This,IUnknown *pPin,IDispatch *pMediaType);")
cpp_quote("  void __RPC_STUB IPinInfo_ConnectWithType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_Disconnect_Proxy(IPinInfo *This);")
cpp_quote("  void __RPC_STUB IPinInfo_Disconnect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IPinInfo_Render_Proxy(IPinInfo *This);")
cpp_quote("  void __RPC_STUB IPinInfo_Render_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMStats_INTERFACE_DEFINED__")
cpp_quote("#define __IAMStats_INTERFACE_DEFINED__")
cpp_quote("  DEFINE_GUID(IID_IAMStats,0xbc9bcf80,0xdcd2,0x11d2,0xab,0xf6,0x00,0xa0,0xc9,0x05,0xf3,0x75);")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMStats : public IDispatch {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI Reset(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Count(LONG *plCount) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetValueByIndex(__LONG32 lIndex,BSTR *szName,__LONG32 *lCount,double *dLast,double *dAverage,double *dStdDev,double *dMin,double *dMax) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetValueByName(BSTR szName,__LONG32 *lIndex,__LONG32 *lCount,double *dLast,double *dAverage,double *dStdDev,double *dMin,double *dMax) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetIndex(BSTR szName,__LONG32 lCreate,__LONG32 *plIndex) = 0;")
cpp_quote("    virtual HRESULT WINAPI AddValue(__LONG32 lIndex,double dValue) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMStatsVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMStats *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMStats *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMStats *This);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfoCount)(IAMStats *This,UINT *pctinfo);")
cpp_quote("      HRESULT (WINAPI *GetTypeInfo)(IAMStats *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);")
cpp_quote("      HRESULT (WINAPI *GetIDsOfNames)(IAMStats *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);")
cpp_quote("      HRESULT (WINAPI *Invoke)(IAMStats *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);")
cpp_quote("      HRESULT (WINAPI *Reset)(IAMStats *This);")
cpp_quote("      HRESULT (WINAPI *get_Count)(IAMStats *This,LONG *plCount);")
cpp_quote("      HRESULT (WINAPI *GetValueByIndex)(IAMStats *This,__LONG32 lIndex,BSTR *szName,__LONG32 *lCount,double *dLast,double *dAverage,double *dStdDev,double *dMin,double *dMax);")
cpp_quote("      HRESULT (WINAPI *GetValueByName)(IAMStats *This,BSTR szName,__LONG32 *lIndex,__LONG32 *lCount,double *dLast,double *dAverage,double *dStdDev,double *dMin,double *dMax);")
cpp_quote("      HRESULT (WINAPI *GetIndex)(IAMStats *This,BSTR szName,__LONG32 lCreate,__LONG32 *plIndex);")
cpp_quote("      HRESULT (WINAPI *AddValue)(IAMStats *This,__LONG32 lIndex,double dValue);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMStatsVtbl;")
cpp_quote("  struct IAMStats {")
cpp_quote("    CONST_VTBL struct IAMStatsVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMStats_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMStats_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMStats_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMStats_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)")
cpp_quote("#define IAMStats_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)")
cpp_quote("#define IAMStats_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)")
cpp_quote("#define IAMStats_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)")
cpp_quote("#define IAMStats_Reset(This) (This)->lpVtbl->Reset(This)")
cpp_quote("#define IAMStats_get_Count(This,plCount) (This)->lpVtbl->get_Count(This,plCount)")
cpp_quote("#define IAMStats_GetValueByIndex(This,lIndex,szName,lCount,dLast,dAverage,dStdDev,dMin,dMax) (This)->lpVtbl->GetValueByIndex(This,lIndex,szName,lCount,dLast,dAverage,dStdDev,dMin,dMax)")
cpp_quote("#define IAMStats_GetValueByName(This,szName,lIndex,lCount,dLast,dAverage,dStdDev,dMin,dMax) (This)->lpVtbl->GetValueByName(This,szName,lIndex,lCount,dLast,dAverage,dStdDev,dMin,dMax)")
cpp_quote("#define IAMStats_GetIndex(This,szName,lCreate,plIndex) (This)->lpVtbl->GetIndex(This,szName,lCreate,plIndex)")
cpp_quote("#define IAMStats_AddValue(This,lIndex,dValue) (This)->lpVtbl->AddValue(This,lIndex,dValue)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  HRESULT WINAPI IAMStats_Reset_Proxy(IAMStats *This);")
cpp_quote("  void __RPC_STUB IAMStats_Reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMStats_get_Count_Proxy(IAMStats *This,LONG *plCount);")
cpp_quote("  void __RPC_STUB IAMStats_get_Count_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMStats_GetValueByIndex_Proxy(IAMStats *This,__LONG32 lIndex,BSTR *szName,__LONG32 *lCount,double *dLast,double *dAverage,double *dStdDev,double *dMin,double *dMax);")
cpp_quote("  void __RPC_STUB IAMStats_GetValueByIndex_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMStats_GetValueByName_Proxy(IAMStats *This,BSTR szName,__LONG32 *lIndex,__LONG32 *lCount,double *dLast,double *dAverage,double *dStdDev,double *dMin,double *dMax);")
cpp_quote("  void __RPC_STUB IAMStats_GetValueByName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMStats_GetIndex_Proxy(IAMStats *This,BSTR szName,__LONG32 lCreate,__LONG32 *plIndex);")
cpp_quote("  void __RPC_STUB IAMStats_GetIndex_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMStats_AddValue_Proxy(IAMStats *This,__LONG32 lIndex,double dValue);")
cpp_quote("  void __RPC_STUB IAMStats_AddValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")

} /* typelib */
