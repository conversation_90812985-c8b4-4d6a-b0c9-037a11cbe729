.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_cipher_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_cipher_init \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_cipher_init(gnutls_cipher_hd_t * " handle ", gnutls_cipher_algorithm_t " cipher ", const gnutls_datum_t * " key ", const gnutls_datum_t * " iv ");"
.SH ARGUMENTS
.IP "gnutls_cipher_hd_t * handle" 12
is a \fBgnutls_cipher_hd_t\fP type
.IP "gnutls_cipher_algorithm_t cipher" 12
the encryption algorithm to use
.IP "const gnutls_datum_t * key" 12
the key to be used for encryption/decryption
.IP "const gnutls_datum_t * iv" 12
the IV to use (if not applicable set NULL)
.SH "DESCRIPTION"
This function will initialize the  \fIhandle\fP context to be usable
for encryption/decryption of data. This will effectively use the
current crypto backend in use by gnutls or the cryptographic
accelerator in use.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
