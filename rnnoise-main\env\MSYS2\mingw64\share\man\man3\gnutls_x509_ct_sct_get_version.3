.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_ct_sct_get_version" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_ct_sct_get_version \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_ct_sct_get_version(gnutls_x509_ct_scts_t " scts ", unsigned " idx ", unsigned int * " version_out ");"
.SH ARGUMENTS
.IP "gnutls_x509_ct_scts_t scts" 12
A list of SCTs
.IP "unsigned idx" 12
The index of the target SCT in the list
.IP "unsigned int * version_out" 12
The version of the target SCT.
.SH "DESCRIPTION"
This function obtains the version of the SCT at the given position
in the SCT list.

The version of that SCT will be placed on  \fIversion_out\fP .

Return : \fBGNUTLS_E_SUCCESS\fP (0) is returned on success,
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP if  \fIidx\fP exceeds the number of SCTs in the list
and \fBGNUTLS_E_INVALID_REQUEST\fP if the SCT's version is different than 1, as that's currently
the only defined version.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
