/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _INC_FUNCTIONDISCOVERYKEYS
#define _INC_FUNCTIONDISCOVERYKEYS

#if (_WIN32_WINNT >= 0x0600)
#ifdef __cplusplus
extern "C" {
#endif

/* More magic keys at  http://msdn.microsoft.com/en-us/library/aa364697%28v=VS.85%29.aspx */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#ifndef DEFINE_API_PKEY
#include <propkey.h>
#endif

#include <functiondiscoverykeys_devpkey.h>

#ifndef DEFINE_API_PKEY
#define DEFINE_API_PKEY(name, managed_name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8, pid) DEFINE_PROPERTYKEY(name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8, pid)
#endif

DEFINE_PROPERTYKEY(PKEY_FunctionInstance, 0x08c0c253, 0xa154, 0x4746, 0x90, 0x05, 0x82, 0xde, 0x53, 0x17, 0x14, 0x8b, 0x00000001);

DEFINE_GUID(FMTID_FD, 0x904b03a2, 0x471d, 0x423c, 0xa5, 0x84, 0xf3, 0x48, 0x32, 0x38, 0xa1, 0x46);
DEFINE_API_PKEY(PKEY_FD_Visibility, VisibilityFlags, 0x904b03a2, 0x471d, 0x423c, 0xa5, 0x84, 0xf3, 0x48, 0x32, 0x38, 0xa1, 0x46, 0x00000001);

#define FD_Visibility_Default 0
#define FD_Visibility_Hidden 1

DEFINE_GUID(FMTID_Device, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57);

DEFINE_API_PKEY(PKEY_Device_NotPresent, DeviceNotPresent, 0x904b03a2, 0x471d, 0x423c, 0xa5, 0x84, 0xf3, 0x48, 0x32, 0x38, 0xa1, 0x46, 0x00000002);
DEFINE_API_PKEY(PKEY_Device_QueueSize, DeviceQueueSize, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000024);
DEFINE_API_PKEY(PKEY_Device_Status, DeviceStatus, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000025);
DEFINE_API_PKEY(PKEY_Device_Comment, DeviceComment, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000026);
DEFINE_API_PKEY(PKEY_Device_Model, DeviceModel, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000027);

DEFINE_GUID(FMTID_DeviceInterface, 0x53808008, 0x07bb, 0x4661, 0xbc, 0x3c, 0xb5, 0x95, 0x3e, 0x70, 0x85, 0x60);

DEFINE_API_PKEY(PKEY_DeviceInterface_DevicePath, DevicePath, 0x53808008, 0x07bb, 0x4661, 0xbc, 0x3c, 0xb5, 0x95, 0x3e, 0x70, 0x85, 0x60, 0x00000001);

DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Address, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000033);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_DiscoveryMethod, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000034);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsEncrypted, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000035);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsAuthenticated, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000036);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsConnected, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000037);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsPaired, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000038);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Icon, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000039);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Version, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000041);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Last_Seen, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000042);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Last_Connected, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000043);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsShowInDisconnectedState, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000044);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsLocalMachine, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000046);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_MetadataPath, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000047);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsMetadataSearchInProgress, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000048);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_MetadataChecksum, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000049);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsNotInterestingForDisplay, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000004a);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_LaunchDeviceStageOnDeviceConnect, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000004c);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_LaunchDeviceStageFromExplorer, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000004d);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_BaselineExperienceId, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000004e);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsDeviceUniquelyIdentifiable, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000004f);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_AssociationArray, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000050);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_DeviceDescription1, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000051);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_DeviceDescription2, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000052);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsNotWorkingProperly, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000053);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsSharedDevice, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000054);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsNetworkDevice, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000055);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_IsDefaultDevice, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000056);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_MetadataCabinet, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000057);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_RequiresPairingElevation, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000058);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_ExperienceId, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000059);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Category, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000005a);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Category_Desc_Singular, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000005b);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Category_Desc_Plural, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000005c);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Category_Icon, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000005d);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_CategoryGroup_Desc, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000005e);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_CategoryGroup_Icon, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x0000005f);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_PrimaryCategory, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000061);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_UnpairUninstall, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000062);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_RequiresUninstallElevation, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000063);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_DeviceFunctionSubRank, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000064);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_AlwaysShowDeviceAsConnected, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000065);

DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_FriendlyName, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003000);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_Manufacturer, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00002000);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_ModelName, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00002002);
DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_ModelNumber, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00002003);

DEFINE_PROPERTYKEY(PKEY_DeviceDisplay_InstallInProgress, 0x83da6326, 0x97a6, 0x4088, 0x94, 0x53, 0xa1, 0x92, 0x3f, 0x57, 0x3b, 0x29, 9);

DEFINE_GUID(FMTID_Pairing, 0x8807cae6, 0x7db6, 0x4f10, 0x8e, 0xe4, 0x43, 0x5e, 0xaa, 0x13, 0x92, 0xbc);
DEFINE_PROPERTYKEY(PKEY_Pairing_ListItemText, 0x8807cae6, 0x7db6, 0x4f10, 0x8e, 0xe4, 0x43, 0x5e, 0xaa, 0x13, 0x92, 0xbc, 0x0000001);
DEFINE_PROPERTYKEY(PKEY_Pairing_ListItemDescription, 0x8807cae6, 0x7db6, 0x4f10, 0x8e, 0xe4, 0x43, 0x5e, 0xaa, 0x13, 0x92, 0xbc, 0x0000002);
DEFINE_PROPERTYKEY(PKEY_Pairing_ListItemIcon, 0x8807cae6, 0x7db6, 0x4f10, 0x8e, 0xe4, 0x43, 0x5e, 0xaa, 0x13, 0x92, 0xbc, 0x0000003);
DEFINE_PROPERTYKEY(PKEY_Pairing_ListItemDefault, 0x8807cae6, 0x7db6, 0x4f10, 0x8e, 0xe4, 0x43, 0x5e, 0xaa, 0x13, 0x92, 0xbc, 0x0000004);
DEFINE_PROPERTYKEY(PKEY_Pairing_IsWifiOnlyDevice, 0x8807cae6, 0x7db6, 0x4f10, 0x8e, 0xe4, 0x43, 0x5e, 0xaa, 0x13, 0x92, 0xbc, 0x0000010);

#define DEVICEDISPLAY_DISCOVERYMETHOD_BLUETOOTH L"Bluetooth"
#define DEVICEDISPLAY_DISCOVERYMETHOD_BLUETOOTH_LE L"Bluetooth Low Energy"
#define DEVICEDISPLAY_DISCOVERYMETHOD_NETBIOS L"NetBIOS"
#define DEVICEDISPLAY_DISCOVERYMETHOD_AD_PRINTER L"Published Printer"
#define DEVICEDISPLAY_DISCOVERYMETHOD_PNP L"PnP"
#define DEVICEDISPLAY_DISCOVERYMETHOD_UPNP L"UPnP"
#define DEVICEDISPLAY_DISCOVERYMETHOD_WSD L"WSD"
#define DEVICEDISPLAY_DISCOVERYMETHOD_WUSB L"WUSB"
#define DEVICEDISPLAY_DISCOVERYMETHOD_WFD L"WiFiDirect"
#define DEVICEDISPLAY_DISCOVERYMETHOD_ASP_INFRA L"AspInfra"

DEFINE_PROPERTYKEY(PKEY_Device_BIOSVersion, 0xeaee7f1d, 0x6a33, 0x44d1, 0x94, 0x41, 0x5f, 0x46, 0xde, 0xf2, 0x31, 0x98, 9);

DEFINE_API_PKEY(PKEY_Write_Time, WriteTime, 0xf53b7e1c, 0x77e0, 0x4450, 0x8c, 0x5f, 0xa7, 0x6c, 0xc7, 0xfd, 0xe0, 0x58, 0x00000100);
DEFINE_API_PKEY(PKEY_Create_Time, CreateTime, 0xf53b7e1c, 0x77e0, 0x4450, 0x8c, 0x5f, 0xa7, 0x6c, 0xc7, 0xfd, 0xe0, 0x58, 0x00000101);

#ifdef FD_XP
DEFINE_API_PKEY(PKEY_Device_InstanceId, DeviceInstanceId, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000100);
#endif
DEFINE_API_PKEY(PKEY_Device_Interface, DeviceInterface, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00000101);

DEFINE_API_PKEY(PKEY_ExposedIIDs, ExposedIIDs, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00003002);
DEFINE_API_PKEY(PKEY_ExposedCLSIDs, ExposedCLSIDs, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00003003);
DEFINE_API_PKEY(PKEY_InstanceValidatorClsid, InstanceValidator, 0x78c34fc8, 0x104a, 0x4aca, 0x9e, 0xa4, 0x52, 0x4d, 0x52, 0x99, 0x6e, 0x57, 0x00003004);

DEFINE_GUID(FMTID_WSD, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92);

DEFINE_API_PKEY(PKEY_WSD_AddressURI, WSD_AddressURI, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00001000);
DEFINE_API_PKEY(PKEY_WSD_Types, WSD_Types, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00001001);
DEFINE_API_PKEY(PKEY_WSD_Scopes, WSD_Scopes, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00001002);
DEFINE_API_PKEY(PKEY_WSD_MetadataVersion, WSD_MetadataVersion, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00001003);
DEFINE_API_PKEY(PKEY_WSD_AppSeqInstanceID, WSD_AppSeqInstanceID, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00001004);
DEFINE_API_PKEY(PKEY_WSD_AppSeqSessionID, WSD_AppSeqSessionID, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00001005);
DEFINE_API_PKEY(PKEY_WSD_AppSeqMessageNumber, WSD_AppSeqMessageNumber, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00001006);
DEFINE_API_PKEY(PKEY_WSD_XAddrs, WSD_XAddrs, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00002000);

DEFINE_API_PKEY(PKEY_WSD_MetadataClean, WSD_MetadataClean, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00000001);
DEFINE_API_PKEY(PKEY_WSD_ServiceInfo, WSD_ServiceInfo, 0x92506491, 0xff95, 0x4724, 0xa0, 0x5a, 0x5b, 0x81, 0x88, 0x5a, 0x7c, 0x92, 0x00000002);

DEFINE_API_PKEY(PKEY_PUBSVCS_TYPE, PUBSVCS_TYPE, 0xf1b88ad3, 0x109c, 0x4fd2, 0xba, 0x3f, 0x53, 0x5a, 0x76, 0x5f, 0x82, 0xf4, 0x00005001);
DEFINE_API_PKEY(PKEY_PUBSVCS_SCOPE, PUBSVCS_SCOPE, 0x2ae2b567, 0xeecb, 0x4a3e, 0xb7, 0x53, 0x54, 0xc7, 0x25, 0x49, 0x43, 0x66, 0x00005002);
DEFINE_API_PKEY(PKEY_PUBSVCS_METADATA, PUBSVCS_METADATA, 0x63c6d5b8, 0xf73a, 0x4aca, 0x96, 0x7e, 0x0c, 0xc7, 0x87, 0xe0, 0xb5, 0x59, 0x00005003);
DEFINE_API_PKEY(PKEY_PUBSVCS_METADATA_VERSION, PUBSVCS_METADATA_VERSION, 0xc0c96c15, 0x1823, 0x4e5b, 0x93, 0x48, 0xe8, 0x25, 0x19, 0x92, 0x3f, 0x04, 0x00005004);
DEFINE_API_PKEY(PKEY_PUBSVCS_NETWORK_PROFILES_ALLOWED, PUBSVCS_NETWORK_PROFILES_ALLOWED, 0x63c6d5b8, 0xf73a, 0x4aca, 0x96, 0x7e, 0x0c, 0xc7, 0x87, 0xe0, 0xb5, 0x59, 0x00005005);
DEFINE_API_PKEY(PKEY_PUBSVCS_NETWORK_PROFILES_DENIED, PUBSVCS_NETWORK_PROFILES_DENIED, 0x63c6d5b8, 0xf73a, 0x4aca, 0x96, 0x7e, 0x0c, 0xc7, 0x87, 0xe0, 0xb5, 0x59, 0x00005006);
DEFINE_API_PKEY(PKEY_PUBSVCS_NETWORK_PROFILES_DEFAULT, PUBSVCS_NETWORK_PROFILES_DEFAULT, 0x63c6d5b8, 0xf73a, 0x4aca, 0x96, 0x7e, 0x0c, 0xc7, 0x87, 0xe0, 0xb5, 0x59, 0x00005007);

DEFINE_GUID(FMTID_PNPX, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd);

DEFINE_PROPERTYKEY(PKEY_PNPX_GlobalIdentity, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00001000);
DEFINE_PROPERTYKEY(PKEY_PNPX_Types, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00001001);
DEFINE_PROPERTYKEY(PKEY_PNPX_Scopes, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00001002);
DEFINE_PROPERTYKEY(PKEY_PNPX_XAddrs, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00001003);
DEFINE_PROPERTYKEY(PKEY_PNPX_MetadataVersion, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00001004);
DEFINE_PROPERTYKEY(PKEY_PNPX_ID, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00001005);

DEFINE_PROPERTYKEY(PKEY_PNPX_RemoteAddress, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00001006);

DEFINE_PROPERTYKEY(PKEY_PNPX_RootProxy, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00001007);

#define PKEY_PNPX_Manufacturer PKEY_DeviceDisplay_Manufacturer

DEFINE_PROPERTYKEY(PKEY_PNPX_ManufacturerUrl, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00002001);

#define PKEY_PNPX_ModelName PKEY_DeviceDisplay_ModelName
#define PKEY_PNPX_ModelNumber PKEY_DeviceDisplay_ModelNumber

DEFINE_PROPERTYKEY(PKEY_PNPX_ModelUrl, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00002004);
DEFINE_PROPERTYKEY(PKEY_PNPX_Upc, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00002005);
DEFINE_PROPERTYKEY(PKEY_PNPX_PresentationUrl, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00002006);

#define PKEY_PNPX_FriendlyName PKEY_DeviceDisplay_FriendlyName

DEFINE_PROPERTYKEY(PKEY_PNPX_FirmwareVersion, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003001);
DEFINE_PROPERTYKEY(PKEY_PNPX_SerialNumber, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003002);
DEFINE_PROPERTYKEY(PKEY_PNPX_DeviceCategory, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003004);

DEFINE_PROPERTYKEY(PKEY_PNPX_SecureChannel, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00007001);
DEFINE_PROPERTYKEY(PKEY_PNPX_CompactSignature, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00007002);
DEFINE_PROPERTYKEY(PKEY_PNPX_DeviceCertHash, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00007003);

#define PNPX_DEVICECATEGORY_COMPUTER L"Computers"
#define PNPX_DEVICECATEGORY_INPUTDEVICE L"Input"
#define PNPX_DEVICECATEGORY_PRINTER L"Printers"
#define PNPX_DEVICECATEGORY_SCANNER L"Scanners"
#define PNPX_DEVICECATEGORY_FAX L"FAX"
#define PNPX_DEVICECATEGORY_MFP L"MFP"
#define PNPX_DEVICECATEGORY_CAMERA L"Cameras"
#define PNPX_DEVICECATEGORY_STORAGE L"Storage"
#define PNPX_DEVICECATEGORY_NETWORK_INFRASTRUCTURE L"NetworkInfrastructure"
#define PNPX_DEVICECATEGORY_DISPLAYS L"Displays"
#define PNPX_DEVICECATEGORY_MULTIMEDIA_DEVICE L"MediaDevices"
#define PNPX_DEVICECATEGORY_GAMING_DEVICE L"Gaming"
#define PNPX_DEVICECATEGORY_TELEPHONE L"Phones"
#define PNPX_DEVICECATEGORY_HOME_AUTOMATION_SYSTEM L"HomeAutomation"
#define PNPX_DEVICECATEGORY_HOME_SECURITY_SYSTEM L"HomeSecurity"
#define PNPX_DEVICECATEGORY_OTHER L"Other"

DEFINE_PROPERTYKEY(PKEY_PNPX_DeviceCategory_Desc, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003005);
DEFINE_PROPERTYKEY(PKEY_PNPX_Category_Desc_NonPlural, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003010);

DEFINE_PROPERTYKEY(PKEY_PNPX_PhysicalAddress, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003006);
DEFINE_PROPERTYKEY(PKEY_PNPX_NetworkInterfaceLuid, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003007);
DEFINE_PROPERTYKEY(PKEY_PNPX_NetworkInterfaceGuid, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003008);
DEFINE_PROPERTYKEY(PKEY_PNPX_IpAddress, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00003009);

DEFINE_PROPERTYKEY(PKEY_PNPX_ServiceAddress, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00004000);
DEFINE_PROPERTYKEY(PKEY_PNPX_ServiceId, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00004001);
DEFINE_PROPERTYKEY(PKEY_PNPX_ServiceTypes, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00004002);
DEFINE_PROPERTYKEY(PKEY_PNPX_ServiceControlUrl, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x4004);
DEFINE_PROPERTYKEY(PKEY_PNPX_ServiceDescUrl, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x4005);
DEFINE_PROPERTYKEY(PKEY_PNPX_ServiceEventSubUrl, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x4006);

DEFINE_API_PKEY(PKEY_PNPX_Devnode, PnPXDevNode, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00000001);
DEFINE_API_PKEY(PKEY_PNPX_AssociationState, AssociationState, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00000002);
DEFINE_API_PKEY(PKEY_PNPX_AssociatedInstanceId, AssociatedInstanceId, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00000003);
DEFINE_API_PKEY(PKEY_PNPX_LastNotificationTime, LastNotificationTime, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00000004);

DEFINE_PROPERTYKEY(PKEY_PNPX_DomainName, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00005000);
DEFINE_PROPERTYKEY(PKEY_PNPX_ShareName, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00005002);

DEFINE_PROPERTYKEY(PKEY_SSDP_AltLocationInfo, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00006000);
DEFINE_PROPERTYKEY(PKEY_SSDP_DevLifeTime, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00006001);
DEFINE_PROPERTYKEY(PKEY_SSDP_NetworkInterface, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00006002);

DEFINE_GUID(FMTID_PNPXDynamicProperty, 0x4fc5077e, 0xb686, 0x44be, 0x93, 0xe3, 0x86, 0xca, 0xfe, 0x36, 0x8c, 0xcd);

DEFINE_PROPERTYKEY(PKEY_PNPX_Installable, 0x4fc5077e, 0xb686, 0x44be, 0x93, 0xe3, 0x86, 0xca, 0xfe, 0x36, 0x8c, 0xcd, 0x00000001);
DEFINE_PROPERTYKEY(PKEY_PNPX_Associated, 0x4fc5077e, 0xb686, 0x44be, 0x93, 0xe3, 0x86, 0xca, 0xfe, 0x36, 0x8c, 0xcd, 0x00000002);

#define PKEY_PNPX_Installed PKEY_PNPX_Associated

DEFINE_PROPERTYKEY(PKEY_PNPX_CompatibleTypes, 0x4fc5077e, 0xb686, 0x44be, 0x93, 0xe3, 0x86, 0xca, 0xfe, 0x36, 0x8c, 0xcd, 0x00000003);
DEFINE_PROPERTYKEY(PKEY_PNPX_InstallState, 0x4fc5077e, 0xb686, 0x44be, 0x93, 0xe3, 0x86, 0xca, 0xfe, 0x36, 0x8c, 0xcd, 0x00000004);

#define PNPX_INSTALLSTATE_NOTINSTALLED 0
#define PNPX_INSTALLSTATE_INSTALLED 1
#define PNPX_INSTALLSTATE_INSTALLING 2
#define PNPX_INSTALLSTATE_FAILED 3

DEFINE_PROPERTYKEY(PKEY_PNPX_Removable, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00007000);
DEFINE_PROPERTYKEY(PKEY_PNPX_IPBusEnumerated, 0x656a3bb3, 0xecc0, 0x43fd, 0x84, 0x77, 0x4a, 0xe0, 0x40, 0x4a, 0x96, 0xcd, 0x00007010);

DEFINE_PROPERTYKEY(PKEY_WNET_Scope, 0xdebda43a, 0x37b3, 0x4383, 0x91, 0xe7, 0x44, 0x98, 0xda, 0x29, 0x95, 0xab, 0x00000001);
DEFINE_PROPERTYKEY(PKEY_WNET_Type, 0xdebda43a, 0x37b3, 0x4383, 0x91, 0xe7, 0x44, 0x98, 0xda, 0x29, 0x95, 0xab, 0x00000002);
DEFINE_PROPERTYKEY(PKEY_WNET_DisplayType, 0xdebda43a, 0x37b3, 0x4383, 0x91, 0xe7, 0x44, 0x98, 0xda, 0x29, 0x95, 0xab, 0x00000003);
DEFINE_PROPERTYKEY(PKEY_WNET_Usage, 0xdebda43a, 0x37b3, 0x4383, 0x91, 0xe7, 0x44, 0x98, 0xda, 0x29, 0x95, 0xab, 0x00000004);
DEFINE_PROPERTYKEY(PKEY_WNET_LocalName, 0xdebda43a, 0x37b3, 0x4383, 0x91, 0xe7, 0x44, 0x98, 0xda, 0x29, 0x95, 0xab, 0x00000005);
DEFINE_PROPERTYKEY(PKEY_WNET_RemoteName, 0xdebda43a, 0x37b3, 0x4383, 0x91, 0xe7, 0x44, 0x98, 0xda, 0x29, 0x95, 0xab, 0x00000006);
DEFINE_PROPERTYKEY(PKEY_WNET_Comment, 0xdebda43a, 0x37b3, 0x4383, 0x91, 0xe7, 0x44, 0x98, 0xda, 0x29, 0x95, 0xab, 0x00000007);
DEFINE_PROPERTYKEY(PKEY_WNET_Provider, 0xdebda43a, 0x37b3, 0x4383, 0x91, 0xe7, 0x44, 0x98, 0xda, 0x29, 0x95, 0xab, 0x00000008);

DEFINE_PROPERTYKEY(PKEY_WCN_Version, 0x88190b80, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x00000001);
DEFINE_PROPERTYKEY(PKEY_WCN_RequestType, 0x88190b81, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x00000002);
DEFINE_PROPERTYKEY(PKEY_WCN_AuthType, 0x88190b82, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x00000003);
DEFINE_PROPERTYKEY(PKEY_WCN_EncryptType, 0x88190b83, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x00000004);
DEFINE_PROPERTYKEY(PKEY_WCN_ConnType, 0x88190b84, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x00000005);
DEFINE_PROPERTYKEY(PKEY_WCN_ConfigMethods, 0x88190b85, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x00000006);

DEFINE_PROPERTYKEY(PKEY_WCN_RfBand, 0x88190b87, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x00000008);
DEFINE_PROPERTYKEY(PKEY_WCN_AssocState, 0x88190b88, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x00000009);
DEFINE_PROPERTYKEY(PKEY_WCN_ConfigError, 0x88190b89, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x0000000a);
DEFINE_PROPERTYKEY(PKEY_WCN_ConfigState, 0x88190b89, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x0000000b);
DEFINE_PROPERTYKEY(PKEY_WCN_DevicePasswordId, 0x88190b89, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x0000000c);
DEFINE_PROPERTYKEY(PKEY_WCN_OSVersion, 0x88190b89, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x0000000d);
DEFINE_PROPERTYKEY(PKEY_WCN_VendorExtension, 0x88190b8a, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x0000000e);
DEFINE_PROPERTYKEY(PKEY_WCN_RegistrarType, 0x88190b8b, 0x4684, 0x11da, 0xa2, 0x6a, 0x00, 0x02, 0xb3, 0x98, 0x8e, 0x81, 0x0000000f);

#define PKEY_DriverPackage_Model PKEY_DrvPkg_Model
#define PKEY_DriverPackage_VendorWebSite PKEY_DrvPkg_VendorWebSite
#define PKEY_DriverPackage_DetailedDescription PKEY_DrvPkg_DetailedDescription
#define PKEY_DriverPackage_DocumentationLink PKEY_DrvPkg_DocumentationLink
#define PKEY_DriverPackage_Icon PKEY_DrvPkg_Icon
#define PKEY_DriverPackage_BrandingIcon PKEY_DrvPkg_BrandingIcon

DEFINE_PROPERTYKEY(PKEY_Hardware_Devinst, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 4097);
DEFINE_PROPERTYKEY(PKEY_Hardware_DisplayAttribute, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 5);
DEFINE_PROPERTYKEY(PKEY_Hardware_DriverDate, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 11);
DEFINE_PROPERTYKEY(PKEY_Hardware_DriverProvider, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 10);
DEFINE_PROPERTYKEY(PKEY_Hardware_DriverVersion, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 9);
DEFINE_PROPERTYKEY(PKEY_Hardware_Function, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 4099);
DEFINE_PROPERTYKEY(PKEY_Hardware_Icon, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 3);
DEFINE_PROPERTYKEY(PKEY_Hardware_Image, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 4098);
DEFINE_PROPERTYKEY(PKEY_Hardware_Manufacturer, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 6);
DEFINE_PROPERTYKEY(PKEY_Hardware_Model, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 7);
DEFINE_PROPERTYKEY(PKEY_Hardware_Name, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 2);
DEFINE_PROPERTYKEY(PKEY_Hardware_SerialNumber, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 8);
DEFINE_PROPERTYKEY(PKEY_Hardware_ShellAttributes, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 4100);
DEFINE_PROPERTYKEY(PKEY_Hardware_Status, 0x5eaf3ef2, 0xe0ca, 0x4598, 0xbf, 0x06, 0x71, 0xed, 0x1d, 0x9d, 0xd9, 0x53, 4096);

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */

#ifdef __cplusplus
}
#endif
#endif /*(_WIN32_WINNT >= 0x0600)*/
#endif /*_INC_FUNCTIONDISCOVERYKEYS*/
