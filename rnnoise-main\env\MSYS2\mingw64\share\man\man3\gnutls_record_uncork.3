.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_uncork" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_uncork \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_record_uncork(gnutls_session_t " session ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int flags" 12
Could be zero or \fBGNUTLS_RECORD_WAIT\fP
.SH "DESCRIPTION"
This resets the effect of \fBgnutls_record_cork()\fP, and flushes any pending
data. If the \fBGNUTLS_RECORD_WAIT\fP flag is specified then this
function will block until the data is sent or a fatal error
occurs (i.e., the function will retry on \fBGNUTLS_E_AGAIN\fP and
\fBGNUTLS_E_INTERRUPTED\fP).

If the flag \fBGNUTLS_RECORD_WAIT\fP is not specified and the function
is interrupted then the \fBGNUTLS_E_AGAIN\fP or \fBGNUTLS_E_INTERRUPTED\fP
errors will be returned. To obtain the data left in the corked
buffer use \fBgnutls_record_check_corked()\fP.
.SH "RETURNS"
On success the number of transmitted data is returned, or
otherwise a negative error code.
.SH "SINCE"
3.1.9
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
