.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs12_mac_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs12_mac_info \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs12.h>
.sp
.BI "int gnutls_pkcs12_mac_info(gnutls_pkcs12_t " pkcs12 ", unsigned int * " mac ", void * " salt ", unsigned int * " salt_size ", unsigned int * " iter_count ", char ** " oid ");"
.SH ARGUMENTS
.IP "gnutls_pkcs12_t pkcs12" 12
A pkcs12 type
.IP "unsigned int * mac" 12
the MAC algorithm used as \fBgnutls_mac_algorithm_t\fP
.IP "void * salt" 12
the salt used for string to key (if non\-NULL then  \fIsalt_size\fP initially holds its size)
.IP "unsigned int * salt_size" 12
string to key salt size
.IP "unsigned int * iter_count" 12
string to key iteration count
.IP "char ** oid" 12
if non\-NULL it will contain an allocated null\-terminated variable with the OID
.SH "DESCRIPTION"
This function will provide information on the MAC algorithm used
in a PKCS \fB12\fP structure. If the structure algorithms
are unknown the code \fBGNUTLS_E_UNKNOWN_HASH_ALGORITHM\fP will be returned,
and only  \fIoid\fP , will be set. That is,  \fIoid\fP will be set on structures
with a MAC whether supported or not. It must be deinitialized using \fBgnutls_free()\fP.
The other variables are only set on supported structures.
.SH "RETURNS"
\fBGNUTLS_E_INVALID_REQUEST\fP if the provided structure doesn't contain a MAC,
\fBGNUTLS_E_UNKNOWN_HASH_ALGORITHM\fP if the structure's MAC isn't supported, or
another negative error code in case of a failure. Zero on success.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
