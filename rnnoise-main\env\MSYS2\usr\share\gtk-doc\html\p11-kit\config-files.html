<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Configuration Files: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="config.html" title="PKCS#11 Configuration">
<link rel="prev" href="config-example.html" title="Example">
<link rel="next" href="sharing.html" title="Sharing PKCS#11 modules">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="config.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="config-example.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="sharing.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="config-files"></a>Configuration Files</h2></div></div></div>
<p>A complete configuration consists of several files. These files are
	text files. Since <code class="literal">p11-kit</code> is built to be used in all
	sorts of environments and at very low levels of the software stack, we
	cannot make use of high level configuration APIs that you may find on a
	modern desktop.</p>
<p><a class="link" href="pkcs11-conf.html" title="pkcs11.conf">See the manual page</a> for more details
	on the format and available options.</p>
<p>Note that user configuration files are not loaded from the home
	directory if running inside a setuid or setgid program.</p>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>