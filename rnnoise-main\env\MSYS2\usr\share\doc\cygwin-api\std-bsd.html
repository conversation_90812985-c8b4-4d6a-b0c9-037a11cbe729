<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>System interfaces compatible with BSD functions:</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="compatibility.html" title="Chapter&#160;1.&#160;Compatibility"><link rel="prev" href="compatibility.html" title="Chapter&#160;1.&#160;Compatibility"><link rel="next" href="std-gnu.html" title="System interfaces compatible with GNU or Linux extensions:"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">System interfaces compatible with BSD functions:</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="compatibility.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;1.&#160;Compatibility</th><td width="20%" align="right">&#160;<a accesskey="n" href="std-gnu.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="std-bsd"></a>System interfaces compatible with BSD functions:</h2></div></div></div><pre class="screen">
    __b64_ntop
    __b64_pton
    arc4random
    arc4random_addrandom
    arc4random_buf
    arc4random_stir
    arc4random_uniform
    bindresvport
    bindresvport_sa
    cfmakeraw
    cfsetspeed
    clearerr_unlocked
    close_range
    daemon
    dn_comp
    dn_expand
    dn_skipname
    drem
    eaccess
    endusershell
    err
    errx
    explicit_bzero
    feof_unlocked
    ferror_unlocked
    fflush_unlocked
    fgetc_unlocked
    fileno_unlocked
    finite
    finitef
    finitel
    fiprintf
    flock			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    fls
    flsl
    flsll
    forkpty
    fpurge
    fputc_unlocked
    fread_unlocked
    freeifaddrs
    fstatfs
    fts_children
    fts_close
    fts_get_clientptr
    fts_get_stream
    fts_open
    fts_read
    fts_set
    fts_set_clientptr
    funopen
    futimes
    fwrite_unlocked
    gamma
    gamma_r
    gammaf
    gammaf_r
    getdomainname
    getdtablesize
    getgrouplist
    getifaddrs
    getloadavg
    getpagesize
    getpeereid
    getprogname
    getusershell
    herror
    hstrerror
    inet_aton
    inet_makeaddr
    inet_netof
    inet_network
    initgroups
    iruserok
    iruserok_sa
    issetugid
    login
    login_tty
    logout
    logwtmp
    madvise
    mkstemps
    openpty
    rcmd
    rcmd_af
    reallocf
    res_close
    res_init
    res_mkquery
    res_nclose
    res_ninit
    res_nmkquery
    res_nquery
    res_nquerydomain
    res_nsearch
    res_nsend
    res_query
    res_querydomain
    res_search
    res_send
    revoke
    rexec
    rpmatch
    rresvport
    rresvport_af
    ruserok
    sbrk
    setbuffer
    setgroups
    sethostname
    setlinebuf
    setpassent
    setprogname
    settimeofday
    setusershell
    statfs
    strcasestr
    strsep
    timingsafe_bcmp
    timingsafe_memcmp
    updwtmp
    valloc
    verr
    verrx
    vhangup			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    vsyslog
    vwarn
    vwarnx
    wait3
    wait4
    warn
    warnx
</pre></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="compatibility.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="compatibility.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="std-gnu.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Chapter&#160;1.&#160;Compatibility&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;System interfaces compatible with GNU or Linux extensions:</td></tr></table></div></body></html>
