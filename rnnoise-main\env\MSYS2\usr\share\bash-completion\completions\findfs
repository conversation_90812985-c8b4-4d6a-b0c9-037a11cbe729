_findfs_module()
{
	local cur prev OPTS findable
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--version --help"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	for i in $(lsblk -rpno label); do
		findable+=" LABEL=$i"
	done
	for i in $(lsblk -rpno uuid); do
		findable+=" UUID=$i"
	done
	for i in $(lsblk -rpno partlabel); do
		findable+=" PARTLABEL=$i"
	done
	for i in $(lsblk -rpno partuuid); do
		findable+=" PARTUUID=$i"
	done
	COMPREPLY=( $(compgen -W "$findable" -- $cur) )
	return 0
}
complete -F _findfs_module findfs
