.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srp_set_server_credentials_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srp_set_server_credentials_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_srp_set_server_credentials_function(gnutls_srp_server_credentials_t " cred ", gnutls_srp_server_credentials_function * " func ");"
.SH ARGUMENTS
.IP "gnutls_srp_server_credentials_t cred" 12
is a \fBgnutls_srp_server_credentials_t\fP type.
.IP "gnutls_srp_server_credentials_function * func" 12
is the callback function
.SH "DESCRIPTION"
This function can be used to set a callback to retrieve the user's
SRP credentials.  The callback's function form is:

int (*callback)(gnutls_session_t, const char* username,
gnutls_datum_t *salt, gnutls_datum_t *verifier, gnutls_datum_t *generator,
gnutls_datum_t *prime);

 \fIusername\fP contains the actual username.
The  \fIsalt\fP ,  \fIverifier\fP ,  \fIgenerator\fP and  \fIprime\fP must be filled
in using the \fBgnutls_malloc()\fP. For convenience  \fIprime\fP and  \fIgenerator\fP may also be one of the static parameters defined in gnutls.h.

Initially, the data field is NULL in every \fBgnutls_datum_t\fP
structure that the callback has to fill in. When the
callback is done GnuTLS deallocates all of those buffers
which are non\-NULL, regardless of the return value.

In order to prevent attackers from guessing valid usernames,
if a user does not exist, g and n values should be filled in
using a random user's parameters. In that case the callback must
return the special value (1).
See \fBgnutls_srp_set_server_fake_salt_seed\fP too.
If this is not required for your application, return a negative
number from the callback to abort the handshake.

The callback function will only be called once per handshake.
The callback function should return 0 on success, while
\-1 indicates an error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
