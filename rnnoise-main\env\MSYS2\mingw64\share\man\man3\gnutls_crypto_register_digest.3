.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_crypto_register_digest" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_crypto_register_digest \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_crypto_register_digest(gnutls_digest_algorithm_t " algorithm ", int " priority ", gnutls_digest_init_func " init ", gnutls_digest_hash_func " hash ", gnutls_digest_output_func " output ", gnutls_digest_deinit_func " deinit ", gnutls_digest_fast_func " hash_fast ");"
.SH ARGUMENTS
.IP "gnutls_digest_algorithm_t algorithm" 12
is the gnutls digest identifier
.IP "int priority" 12
is the priority of the algorithm
.IP "gnutls_digest_init_func init" 12
A function which initializes the digest
.IP "gnutls_digest_hash_func hash" 12
Perform the hash operation
.IP "gnutls_digest_output_func output" 12
Provide the output of the digest
.IP "gnutls_digest_deinit_func deinit" 12
A function which deinitializes the digest
.IP "gnutls_digest_fast_func hash_fast" 12
Perform the digest operation in one go
.SH "DESCRIPTION"
This function will register a digest algorithm to be used by gnutls.
Any algorithm registered will override the included algorithms and
by convention kernel implemented algorithms have priority of 90
and CPU\-assisted of 80.
The algorithm with the lowest priority will be used by gnutls.
.SH "DEPRECATED"
since 3.7.0 it is no longer possible to override cipher implementation
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
