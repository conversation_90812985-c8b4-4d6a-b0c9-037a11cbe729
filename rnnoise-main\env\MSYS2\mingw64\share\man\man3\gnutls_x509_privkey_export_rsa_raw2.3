.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_export_rsa_raw2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_export_rsa_raw2 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_export_rsa_raw2(gnutls_x509_privkey_t " key ", gnutls_datum_t * " m ", gnutls_datum_t * " e ", gnutls_datum_t * " d ", gnutls_datum_t * " p ", gnutls_datum_t * " q ", gnutls_datum_t * " u ", gnutls_datum_t * " e1 ", gnutls_datum_t * " e2 ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
a key
.IP "gnutls_datum_t * m" 12
will hold the modulus
.IP "gnutls_datum_t * e" 12
will hold the public exponent
.IP "gnutls_datum_t * d" 12
will hold the private exponent
.IP "gnutls_datum_t * p" 12
will hold the first prime (p)
.IP "gnutls_datum_t * q" 12
will hold the second prime (q)
.IP "gnutls_datum_t * u" 12
will hold the coefficient
.IP "gnutls_datum_t * e1" 12
will hold e1 = d mod (p\-1)
.IP "gnutls_datum_t * e2" 12
will hold e2 = d mod (q\-1)
.SH "DESCRIPTION"
This function will export the RSA private key's parameters found
in the given structure. The new parameters will be allocated using
\fBgnutls_malloc()\fP and will be stored in the appropriate datum.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
