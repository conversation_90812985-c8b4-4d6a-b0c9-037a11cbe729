.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_store_pubkey" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_store_pubkey \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_store_pubkey(const char * " db_name ", gnutls_tdb_t " tdb ", const char * " host ", const char * " service ", gnutls_certificate_type_t " cert_type ", const gnutls_datum_t * " cert ", time_t " expiration ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * db_name" 12
A file specifying the stored keys (use NULL for the default)
.IP "gnutls_tdb_t tdb" 12
A storage structure or NULL to use the default
.IP "const char * host" 12
The peer's name
.IP "const char * service" 12
non\-NULL if this key is specific to a service (e.g. http)
.IP "gnutls_certificate_type_t cert_type" 12
The type of the certificate
.IP "const gnutls_datum_t * cert" 12
The data of the certificate
.IP "time_t expiration" 12
The expiration time (use 0 to disable expiration)
.IP "unsigned int flags" 12
should be 0.
.SH "DESCRIPTION"
This function will store a raw public\-key or a public\-key provided via
a raw (DER\-encoded) certificate to the list of stored public keys. The key
will be considered valid until the provided expiration time.

The  \fItdb\fP variable if non\-null specifies a custom backend for
the storage of entries. If it is NULL then the
default file backend will be used.

Unless an alternative  \fItdb\fP is provided, the storage format is a textual format
consisting of a line for each host with fields separated by '|'. The contents of
the fields are a format\-identifier which is set to 'g0', the hostname that the
rest of the data applies to, the numeric port or host name, the expiration
time in seconds since the epoch (0 for no expiration), and a base64
encoding of the raw (DER) public key information (SPKI) of the peer.

As of GnuTLS 3.6.6 this function also accepts raw public keys.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0.13
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
