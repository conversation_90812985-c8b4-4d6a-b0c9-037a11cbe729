#!/bin/sh
#
# Modified from: <PERSON> <<EMAIL>>

CWD="`dirname \"$0\"`"
TMP=/tmp/$(id -ru)/TemporaryItems

version=`sw_vers -productVersion`
if [ "$?" = "0" ]; then
  major=${version%%\.*}
  rest=${version#*\.}
  minor=${rest%%\.*}
  build=${rest#*\.}
else
  major=10
  minor=4
  build=0
fi

echo $version
echo "Major = $major"
echo "Minor = $minor"
echo "Build = $build"


# if 10.5 or greater, then all the open-x11 stuff need not occur
if [ "$major" -lt 10 ] || ([ "$major" -eq 10 ] && [ "$minor" -lt 5 ]); then
version=`sw_vers -productVersion`
if [ "$?" = "0" ]; then
  major=${version%%\.*}
  rest=${version#*\.}
  minor=${rest%%\.*}
  build=${rest#*\.}
else
  major=10
  minor=4
  build=0
fi

echo $version
echo "Major = $major"
echo "Minor = $minor"
echo "Build = $build"


# if 10.5 or greater, then all the open-x11 stuff need not occur
if [ "$major" -lt 10 ] || ([ "$major" -eq 10 ] && [ "$minor" -lt 5 ]); then
ps -wx -ocommand | grep -e '[X]11.app' > /dev/null
if [ "$?" != "0" -a ! -f ~/.xinitrc ]; then
    echo "rm -f ~/.xinitrc" > ~/.xinitrc
    sed 's/xterm/# xterm/' /usr/X11R6/lib/X11/xinit/xinitrc >> ~/.xinitrc
fi

mkdir -p $TMP
cat << __END_OF_GETDISPLAY_SCRIPT__ > "$TMP/getdisplay.sh"
#!/bin/sh
mkdir -p "$TMP"

if [ "\$DISPLAY"x = "x" ]; then
    echo :0 > "$TMP/display"
else
    echo \$DISPLAY > "$TMP/display"
fi
__END_OF_GETDISPLAY_SCRIPT__
fi
chmod +x "$TMP/getdisplay.sh"
rm -f $TMP/display
open-x11 $TMP/getdisplay.sh || \
open -a XDarwin $TMP/getdisplay.sh || \
echo ":0" > $TMP/display

while [ "$?" = "0" -a ! -f $TMP/display ];
do
  #echo "Waiting for display $TMP/display"
  sleep 1;
done
export "DISPLAY=`cat $TMP/display`"

ps -wx -ocommand | grep -e '[X]11' > /dev/null || exit 11

cd ~/
echo "$@" > /tmp/arguments.log
if echo $1 | grep -- "^-psn_"; then
  shift
fi
fi
exec "$CWD/bin/@CPACK_EXECUTABLE_NAME@" "$@" > /tmp/slicer.output 2>&1
