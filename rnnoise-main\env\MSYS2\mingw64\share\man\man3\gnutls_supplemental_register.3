.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_supplemental_register" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_supplemental_register \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_supplemental_register(const char * " name ", gnutls_supplemental_data_format_type_t " type ", gnutls_supp_recv_func " recv_func ", gnutls_supp_send_func " send_func ");"
.SH ARGUMENTS
.IP "const char * name" 12
the name of the supplemental data to register
.IP "gnutls_supplemental_data_format_type_t type" 12
the type of the supplemental data format
.IP "gnutls_supp_recv_func recv_func" 12
the function to receive the data
.IP "gnutls_supp_send_func send_func" 12
the function to send the data
.SH "DESCRIPTION"
This function will register a new supplemental data type (rfc4680).
The registered data will remain until \fBgnutls_global_deinit()\fP
is called. The provided  \fItype\fP must be an unassigned type in
\fBgnutls_supplemental_data_format_type_t\fP. If the type is already
registered or handled by GnuTLS internally \fBGNUTLS_E_ALREADY_REGISTERED\fP
will be returned.

This function is not thread safe. As supplemental data are not defined under
TLS 1.3, this function will disable TLS 1.3 support globally.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
