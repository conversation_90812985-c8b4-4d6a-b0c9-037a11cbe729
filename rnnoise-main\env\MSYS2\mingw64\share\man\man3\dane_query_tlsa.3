.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_query_tlsa" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_query_tlsa \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "int dane_query_tlsa(dane_state_t " s ", dane_query_t * " r ", const char * " host ", const char * " proto ", unsigned int " port ");"
.SH ARGUMENTS
.IP "dane_state_t s" 12
The DANE state structure
.IP "dane_query_t * r" 12
A structure to place the result
.IP "const char * host" 12
The host name to resolve.
.IP "const char * proto" 12
The protocol type (tcp, udp, etc.)
.IP "unsigned int port" 12
The service port number (eg. 443).
.SH "DESCRIPTION"
This function will query the DNS server for the TLSA (DANE)
data for the given host.
.SH "RETURNS"
On success, \fBDANE_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
