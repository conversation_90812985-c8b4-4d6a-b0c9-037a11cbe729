<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-rsa</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-rsa - RSA key processing command</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>rsa</b> [<b>-help</b>] [<b>-inform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b>] [<b>-outform</b> <b>DER</b>|<b>PEM</b>] [<b>-in</b> <i>filename</i>|<i>uri</i>] [<b>-passin</b> <i>arg</i>] [<b>-out</b> <i>filename</i>] [<b>-passout</b> <i>arg</i>] [<b>-aes128</b>] [<b>-aes192</b>] [<b>-aes256</b>] [<b>-aria128</b>] [<b>-aria192</b>] [<b>-aria256</b>] [<b>-camellia128</b>] [<b>-camellia192</b>] [<b>-camellia256</b>] [<b>-des</b>] [<b>-des3</b>] [<b>-idea</b>] [<b>-text</b>] [<b>-noout</b>] [<b>-modulus</b>] [<b>-traditional</b>] [<b>-check</b>] [<b>-pubin</b>] [<b>-pubout</b>] [<b>-RSAPublicKey_in</b>] [<b>-RSAPublicKey_out</b>] [<b>-pvk-strong</b>] [<b>-pvk-weak</b>] [<b>-pvk-none</b>] [<b>-engine</b> <i>id</i>] [<b>-provider</b> <i>name</i>] [<b>-provider-path</b> <i>path</i>] [<b>-provparam</b> <i>[name:]key=value</i>] [<b>-propquery</b> <i>propq</i>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command processes RSA keys. They can be converted between various forms and their components printed out.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM-P12-ENGINE"><b>-inform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b></dt>
<dd>

<p>The key input format; unspecified by default. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform</b> <b>DER</b>|<b>PEM</b></dt>
<dd>

<p>The key output format; the default is <b>PEM</b>. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="traditional"><b>-traditional</b></dt>
<dd>

<p>When writing a private key, use the traditional PKCS#1 format instead of the PKCS#8 format.</p>

</dd>
<dt id="in-filename-uri"><b>-in</b> <i>filename</i>|<i>uri</i></dt>
<dd>

<p>This specifies the input to read a key from or standard input if this option is not specified. If the key is encrypted a pass phrase will be prompted for.</p>

</dd>
<dt id="passin-arg--passout-arg"><b>-passin</b> <i>arg</i>, <b>-passout</b> <i>arg</i></dt>
<dd>

<p>The password source for the input and output file. For more information about the format of <b>arg</b> see <a href="../man1/openssl-passphrase-options.html">openssl-passphrase-options(1)</a>.</p>

</dd>
<dt id="out-filename"><b>-out</b> <i>filename</i></dt>
<dd>

<p>This specifies the output filename to write a key to or standard output if this option is not specified. If any encryption options are set then a pass phrase will be prompted for. The output filename should <b>not</b> be the same as the input filename.</p>

</dd>
<dt id="aes128--aes192--aes256--aria128--aria192--aria256--camellia128--camellia192--camellia256--des--des3--idea"><b>-aes128</b>, <b>-aes192</b>, <b>-aes256</b>, <b>-aria128</b>, <b>-aria192</b>, <b>-aria256</b>, <b>-camellia128</b>, <b>-camellia192</b>, <b>-camellia256</b>, <b>-des</b>, <b>-des3</b>, <b>-idea</b></dt>
<dd>

<p>These options encrypt the private key with the specified cipher before outputting it. A pass phrase is prompted for. If none of these options is specified the key is written in plain text. This means that this command can be used to remove the pass phrase from a key by not giving any encryption option is given, or to add or change the pass phrase by setting them. These options can only be used with PEM format output files.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>Prints out the various public or private key components in plain text in addition to the encoded version.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option prevents output of the encoded version of the key.</p>

</dd>
<dt id="modulus"><b>-modulus</b></dt>
<dd>

<p>This option prints out the value of the modulus of the key.</p>

</dd>
<dt id="check"><b>-check</b></dt>
<dd>

<p>This option checks the consistency of an RSA private key.</p>

</dd>
<dt id="pubin"><b>-pubin</b></dt>
<dd>

<p>By default a private key is read from the input. With this option a public key is read instead. If the input contains no public key but a private key, its public part is used.</p>

</dd>
<dt id="pubout"><b>-pubout</b></dt>
<dd>

<p>By default a private key is output: with this option a public key will be output instead. This option is automatically set if the input is a public key.</p>

</dd>
<dt id="RSAPublicKey_in--RSAPublicKey_out"><b>-RSAPublicKey_in</b>, <b>-RSAPublicKey_out</b></dt>
<dd>

<p>Like <b>-pubin</b> and <b>-pubout</b> except <b>RSAPublicKey</b> format is used instead.</p>

</dd>
<dt id="pvk-strong"><b>-pvk-strong</b></dt>
<dd>

<p>Enable &#39;Strong&#39; PVK encoding level (default).</p>

</dd>
<dt id="pvk-weak"><b>-pvk-weak</b></dt>
<dd>

<p>Enable &#39;Weak&#39; PVK encoding level.</p>

</dd>
<dt id="pvk-none"><b>-pvk-none</b></dt>
<dd>

<p>Don&#39;t enforce PVK encoding.</p>

</dd>
<dt id="engine-id"><b>-engine</b> <i>id</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Engine Options&quot; in openssl(1)</a>. This option is deprecated.</p>

</dd>
<dt id="provider-name"><b>-provider</b> <i>name</i></dt>
<dd>

</dd>
<dt id="provider-path-path"><b>-provider-path</b> <i>path</i></dt>
<dd>

</dd>
<dt id="provparam-name:-key-value"><b>-provparam</b> <i>[name:]key=value</i></dt>
<dd>

</dd>
<dt id="propquery-propq"><b>-propquery</b> <i>propq</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Provider Options&quot; in openssl(1)</a>, <a href="../man7/provider.html">provider(7)</a>, and <a href="../man7/property.html">property(7)</a>.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>The <a href="../man1/openssl-pkey.html">openssl-pkey(1)</a> command is capable of performing all the operations this command can, as well as supporting other public key types.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>The documentation for the <a href="../man1/openssl-pkey.html">openssl-pkey(1)</a> command contains examples equivalent to the ones listed here.</p>

<p>To remove the pass phrase on an RSA private key:</p>

<pre><code>openssl rsa -in key.pem -out keyout.pem</code></pre>

<p>To encrypt a private key using triple DES:</p>

<pre><code>openssl rsa -in key.pem -des3 -out keyout.pem</code></pre>

<p>To convert a private key from PEM to DER format:</p>

<pre><code>openssl rsa -in key.pem -outform DER -out keyout.der</code></pre>

<p>To print out the components of a private key to standard output:</p>

<pre><code>openssl rsa -in key.pem -text -noout</code></pre>

<p>To just output the public part of a private key:</p>

<pre><code>openssl rsa -in key.pem -pubout -out pubkey.pem</code></pre>

<p>Output the public part of a private key in <b>RSAPublicKey</b> format:</p>

<pre><code>openssl rsa -in key.pem -RSAPublicKey_out -out pubkey.pem</code></pre>

<h1 id="BUGS">BUGS</h1>

<p>There should be an option that automatically handles <i>.key</i> files, without having to manually edit them.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man1/openssl-pkey.html">openssl-pkey(1)</a>, <a href="../man1/openssl-pkcs8.html">openssl-pkcs8(1)</a>, <a href="../man1/openssl-dsa.html">openssl-dsa(1)</a>, <a href="../man1/openssl-genrsa.html">openssl-genrsa(1)</a>, <a href="../man1/openssl-gendsa.html">openssl-gendsa(1)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>-engine</b> option was deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


