.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_set_client_credentials_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_set_client_credentials_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_psk_set_client_credentials_function(gnutls_psk_client_credentials_t " cred ", gnutls_psk_client_credentials_function * " func ");"
.SH ARGUMENTS
.IP "gnutls_psk_client_credentials_t cred" 12
is a \fBgnutls_psk_server_credentials_t\fP type.
.IP "gnutls_psk_client_credentials_function * func" 12
is the callback function
.SH "DESCRIPTION"
This function can be used to set a callback to retrieve the username and
password for client PSK authentication.
The callback's function form is:
int (*callback)(gnutls_session_t, char** username,
gnutls_datum_t* key);

The  \fIusername\fP and  \fIkey\fP \->data must be allocated using \fBgnutls_malloc()\fP.
The  \fIusername\fP should be an ASCII string or UTF\-8
string. In case of a UTF\-8 string it is recommended to be following
the PRECIS framework for usernames (rfc8265).

The callback function will be called once per handshake.

The callback function should return 0 on success.
\-1 indicates an error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
