.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_global_set_log_level" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_global_set_log_level \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_global_set_log_level(int " level ");"
.SH ARGUMENTS
.IP "int level" 12
it's an integer from 0 to 99.
.SH "DESCRIPTION"
This is the function that allows you to set the log level.  The
level is an integer between 0 and 9.  Higher values mean more
verbosity. The default value is 0.  Larger values should only be
used with care, since they may reveal sensitive information.

Use a log level over 10 to enable all debugging options.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
