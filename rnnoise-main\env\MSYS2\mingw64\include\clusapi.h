/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _CLUSTER_API_
#define _CLUSTER_API_

#define CLUSAPI_VERSION 0x0500

#ifdef __cplusplus
extern "C" {
#endif

#ifndef _CLUSTER_API_TYPES_
  typedef struct _HCLUSTER *HCLUSTER;
  typedef struct _HNODE *HNODE;
  typedef struct _HRESOURCE *HRESOURCE;
  typedef struct _HGROUP *HGROUP;
  typedef struct _HNETWORK *HNETWORK;
  typedef struct _HNETINTERFACE *HNETINTERFACE;
  typedef struct _HCHANGE *HCHANGE;
  typedef struct _HCLUSENUM *HCLUSENUM;
  typedef struct _HGROUPENUM *HGROUPENUM;
  typedef struct _HRESENUM *HRESENUM;
  typedef struct _HNETWORKENUM *HNETWORKENUM;
  typedef struct _HNODEENUM *HNODEENUM;
  typedef struct _HRESTYPEENUM *HRESTYPEENUM;
#endif

#define MAX_CLUSTERNAME_LENGTH MAX_COMPUTERNAME_LENGTH

#ifndef _CLUSTER_API_TYPES_

  typedef enum CLUSTER_QUORUM_TYPE {
    OperationalQuorum,ModifyQuorum
  } CLUSTER_QUORUM_TYPE;

  typedef struct CLUSTERVERSIONINFO_NT4 {
    DWORD dwVersionInfoSize;
    WORD MajorVersion;
    WORD MinorVersion;
    WORD BuildNumber;
    WCHAR szVendorId[64];
    WCHAR szCSDVersion[64];
  } CLUSTERVERSIONINFO_NT4,*PCLUSTERVERSIONINFO_NT4;

  typedef struct CLUSTERVERSIONINFO {
    DWORD dwVersionInfoSize;
    WORD MajorVersion;
    WORD MinorVersion;
    WORD BuildNumber;
    WCHAR szVendorId[64];
    WCHAR szCSDVersion[64];
    DWORD dwClusterHighestVersion;
    DWORD dwClusterLowestVersion;
    DWORD dwFlags;
    DWORD dwReserved;
  } CLUSTERVERSIONINFO,*LPCLUSTERVERSIONINFO,*PCLUSTERVERSIONINFO;

  typedef struct CLUS_STARTING_PARAMS {
    DWORD dwSize;
    WINBOOL bForm;
    WINBOOL bFirst;
  } CLUS_STARTING_PARAMS,*PCLUS_STARTING_PARAMS;

#define CLUSTER_VERSION_FLAG_MIXED_MODE 0x00000001

#define NT4_MAJOR_VERSION 1
#define NT4SP4_MAJOR_VERSION 2
#define NT5_MAJOR_VERSION 3
#define NT51_MAJOR_VERSION 4

#define CLUSTER_VERSION_UNKNOWN 0xFFFFFFFF

#define CLUSTER_MAKE_VERSION(_maj,_min) (((_maj) << 16) | (_min))
#define CLUSTER_GET_MAJOR_VERSION(_ver) ((_ver) >> 16)
#define CLUSTER_GET_MINOR_VERSION(_ver) ((_ver) & 0xFFFF)

#define CLUSTER_INSTALLED 0x00000001
#define CLUSTER_CONFIGURED 0x00000002
#define CLUSTER_RUNNING 0x00000010

  typedef enum NODE_CLUSTER_STATE {
    ClusterStateNotInstalled = 0x00000000,ClusterStateNotConfigured = CLUSTER_INSTALLED,ClusterStateNotRunning = CLUSTER_INSTALLED | CLUSTER_CONFIGURED,
    ClusterStateRunning = CLUSTER_INSTALLED | CLUSTER_CONFIGURED | CLUSTER_RUNNING
  } NODE_CLUSTER_STATE;

#define CLUSCTL_RESOURCE_STATE_CHANGE_REASON_VERSION_1 1

  typedef enum CLUSTER_RESOURCE_STATE_CHANGE_REASON {
    eResourceStateChangeReasonUnknown = 0,eResourceStateChangeReasonMove,eResourceStateChangeReasonFailover,eResourceStateChangeReasonFailedMove,
    eResourceStateChangeReasonShutdown,eResourceStateChangeReasonRundown
  } CLUSTER_RESOURCE_STATE_CHANGE_REASON;

  typedef struct _CLUSCTL_RESOURCE_STATE_CHANGE_REASON_STRUCT {
    DWORD dwSize;
    DWORD dwVersion;
    CLUSTER_RESOURCE_STATE_CHANGE_REASON eReason;
  } CLUSCTL_RESOURCE_STATE_CHANGE_REASON_STRUCT,*PCLUSCTL_RESOURCE_STATE_CHANGE_REASON_STRUCT;
#endif

#define CLUSAPI_READ_ACCESS __MSABI_LONG(0x00000001)
#define CLUSAPI_CHANGE_ACCESS __MSABI_LONG(0x00000002)
#define CLUSAPI_NO_ACCESS __MSABI_LONG(0x00000004)
#define CLUSAPI_ALL_ACCESS (CLUSAPI_READ_ACCESS | CLUSAPI_CHANGE_ACCESS)

  typedef enum CLUSTER_SET_PASSWORD_FLAGS {
    CLUSTER_SET_PASSWORD_IGNORE_DOWN_NODES = 1
  } CLUSTER_SET_PASSWORD_FLAGS;

  typedef struct CLUSTER_SET_PASSWORD_STATUS {
    DWORD NodeId;
    BOOLEAN SetAttempted;
    DWORD ReturnStatus;
  } CLUSTER_SET_PASSWORD_STATUS,*PCLUSTER_SET_PASSWORD_STATUS;

  DWORD WINAPI GetNodeClusterState(LPCWSTR lpszNodeName,DWORD *pdwClusterState);
  HCLUSTER WINAPI OpenCluster(LPCWSTR lpszClusterName);
  WINBOOL WINAPI CloseCluster(HCLUSTER hCluster);
  DWORD WINAPI SetClusterName(HCLUSTER hCluster,LPCWSTR lpszNewClusterName);
  DWORD WINAPI GetClusterInformation(HCLUSTER hCluster,LPWSTR lpszClusterName,LPDWORD lpcchClusterName,LPCLUSTERVERSIONINFO lpClusterInfo);
  DWORD WINAPI GetClusterQuorumResource(HCLUSTER hCluster,LPWSTR lpszResourceName,LPDWORD lpcchResourceName,LPWSTR lpszDeviceName,LPDWORD lpcchDeviceName,LPDWORD lpdwMaxQuorumLogSize);
  DWORD WINAPI SetClusterQuorumResource(HRESOURCE hResource,LPCWSTR lpszDeviceName,DWORD dwMaxQuoLogSize);
  DWORD WINAPI BackupClusterDatabase(HCLUSTER hCluster,LPCWSTR lpszPathName);
  DWORD WINAPI RestoreClusterDatabase(LPCWSTR lpszPathName,WINBOOL bForce,LPCWSTR lpszQuorumDriveLetter);
  DWORD WINAPI SetClusterNetworkPriorityOrder(HCLUSTER hCluster,DWORD NetworkCount,HNETWORK NetworkList[]);
  DWORD WINAPI SetClusterServiceAccountPassword(LPCWSTR lpszClusterName,LPCWSTR lpszNewPassword,DWORD dwFlags,PCLUSTER_SET_PASSWORD_STATUS lpReturnStatusBuffer,LPDWORD lpcbReturnStatusBufferSize);
  DWORD WINAPI ClusterControl(HCLUSTER hCluster,HNODE hHostNode,DWORD dwControlCode,LPVOID lpInBuffer,DWORD nInBufferSize,LPVOID lpOutBuffer,DWORD nOutBufferSize,LPDWORD lpBytesReturned);

#ifndef _CLUSTER_API_TYPES_

  typedef enum CLUSTER_CHANGE {
    CLUSTER_CHANGE_NODE_STATE = 0x00000001,CLUSTER_CHANGE_NODE_DELETED = 0x00000002,CLUSTER_CHANGE_NODE_ADDED = 0x00000004,
    CLUSTER_CHANGE_NODE_PROPERTY = 0x00000008,CLUSTER_CHANGE_REGISTRY_NAME = 0x00000010,CLUSTER_CHANGE_REGISTRY_ATTRIBUTES = 0x00000020,
    CLUSTER_CHANGE_REGISTRY_VALUE = 0x00000040,CLUSTER_CHANGE_REGISTRY_SUBTREE = 0x00000080,CLUSTER_CHANGE_RESOURCE_STATE = 0x00000100,
    CLUSTER_CHANGE_RESOURCE_DELETED = 0x00000200,CLUSTER_CHANGE_RESOURCE_ADDED = 0x00000400,CLUSTER_CHANGE_RESOURCE_PROPERTY = 0x00000800,
    CLUSTER_CHANGE_GROUP_STATE = 0x00001000,CLUSTER_CHANGE_GROUP_DELETED = 0x00002000,CLUSTER_CHANGE_GROUP_ADDED = 0x00004000,
    CLUSTER_CHANGE_GROUP_PROPERTY = 0x00008000,CLUSTER_CHANGE_RESOURCE_TYPE_DELETED = 0x00010000,CLUSTER_CHANGE_RESOURCE_TYPE_ADDED = 0x00020000,
    CLUSTER_CHANGE_RESOURCE_TYPE_PROPERTY = 0x00040000,CLUSTER_CHANGE_CLUSTER_RECONNECT = 0x00080000,CLUSTER_CHANGE_NETWORK_STATE = 0x00100000,
    CLUSTER_CHANGE_NETWORK_DELETED = 0x00200000,CLUSTER_CHANGE_NETWORK_ADDED = 0x00400000,CLUSTER_CHANGE_NETWORK_PROPERTY = 0x00800000,
    CLUSTER_CHANGE_NETINTERFACE_STATE = 0x01000000,CLUSTER_CHANGE_NETINTERFACE_DELETED = 0x02000000,CLUSTER_CHANGE_NETINTERFACE_ADDED = 0x04000000,
    CLUSTER_CHANGE_NETINTERFACE_PROPERTY = 0x08000000,CLUSTER_CHANGE_QUORUM_STATE = 0x10000000,CLUSTER_CHANGE_CLUSTER_STATE = 0x20000000,
    CLUSTER_CHANGE_CLUSTER_PROPERTY = 0x40000000,CLUSTER_CHANGE_HANDLE_CLOSE = 0x80000000,
    CLUSTER_CHANGE_ALL = (CLUSTER_CHANGE_NODE_STATE | CLUSTER_CHANGE_NODE_DELETED | CLUSTER_CHANGE_NODE_ADDED | CLUSTER_CHANGE_NODE_PROPERTY | CLUSTER_CHANGE_REGISTRY_NAME | CLUSTER_CHANGE_REGISTRY_ATTRIBUTES | CLUSTER_CHANGE_REGISTRY_VALUE | CLUSTER_CHANGE_REGISTRY_SUBTREE | CLUSTER_CHANGE_RESOURCE_STATE | CLUSTER_CHANGE_RESOURCE_DELETED | CLUSTER_CHANGE_RESOURCE_ADDED | CLUSTER_CHANGE_RESOURCE_PROPERTY | CLUSTER_CHANGE_GROUP_STATE | CLUSTER_CHANGE_GROUP_DELETED | CLUSTER_CHANGE_GROUP_ADDED | CLUSTER_CHANGE_GROUP_PROPERTY | CLUSTER_CHANGE_RESOURCE_TYPE_DELETED | CLUSTER_CHANGE_RESOURCE_TYPE_ADDED | CLUSTER_CHANGE_RESOURCE_TYPE_PROPERTY | CLUSTER_CHANGE_NETWORK_STATE | CLUSTER_CHANGE_NETWORK_DELETED | CLUSTER_CHANGE_NETWORK_ADDED | CLUSTER_CHANGE_NETWORK_PROPERTY | CLUSTER_CHANGE_NETINTERFACE_STATE | CLUSTER_CHANGE_NETINTERFACE_DELETED | CLUSTER_CHANGE_NETINTERFACE_ADDED | CLUSTER_CHANGE_NETINTERFACE_PROPERTY | CLUSTER_CHANGE_QUORUM_STATE | CLUSTER_CHANGE_CLUSTER_STATE | CLUSTER_CHANGE_CLUSTER_PROPERTY | CLUSTER_CHANGE_CLUSTER_RECONNECT | CLUSTER_CHANGE_HANDLE_CLOSE)
  } CLUSTER_CHANGE;
#endif

  HCHANGE WINAPI CreateClusterNotifyPort(HCHANGE hChange,HCLUSTER hCluster,DWORD dwFilter,DWORD_PTR dwNotifyKey);
  DWORD WINAPI RegisterClusterNotify(HCHANGE hChange,DWORD dwFilterType,HANDLE hObject,DWORD_PTR dwNotifyKey);
  DWORD WINAPI GetClusterNotify(HCHANGE hChange,DWORD_PTR *lpdwNotifyKey,LPDWORD lpdwFilterType,LPWSTR lpszName,LPDWORD lpcchName,DWORD dwMilliseconds);
  WINBOOL WINAPI CloseClusterNotifyPort(HCHANGE hChange);

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_ENUM {
    CLUSTER_ENUM_NODE = 0x00000001,CLUSTER_ENUM_RESTYPE = 0x00000002,CLUSTER_ENUM_RESOURCE = 0x00000004,CLUSTER_ENUM_GROUP = 0x00000008,
    CLUSTER_ENUM_NETWORK = 0x00000010,CLUSTER_ENUM_NETINTERFACE = 0x00000020,CLUSTER_ENUM_INTERNAL_NETWORK = 0x80000000,
    CLUSTER_ENUM_ALL = (CLUSTER_ENUM_NODE | CLUSTER_ENUM_RESTYPE | CLUSTER_ENUM_RESOURCE | CLUSTER_ENUM_GROUP | CLUSTER_ENUM_NETWORK | CLUSTER_ENUM_NETINTERFACE)
  } CLUSTER_ENUM;
#endif

  HCLUSENUM WINAPI ClusterOpenEnum(HCLUSTER hCluster,DWORD dwType);
  DWORD WINAPI ClusterGetEnumCount(HCLUSENUM hEnum);
  DWORD WINAPI ClusterEnum(HCLUSENUM hEnum,DWORD dwIndex,LPDWORD lpdwType,LPWSTR lpszName,LPDWORD lpcchName);
  DWORD WINAPI ClusterCloseEnum(HCLUSENUM hEnum);

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_NODE_ENUM {
    CLUSTER_NODE_ENUM_NETINTERFACES   = 0x00000001,
    CLUSTER_NODE_ENUM_GROUPS          = 0x00000002,
    CLUSTER_NODE_ENUM_ALL             = (CLUSTER_NODE_ENUM_NETINTERFACES|CLUSTER_NODE_ENUM_GROUPS)
  } CLUSTER_NODE_ENUM;

  typedef enum CLUSTER_NODE_STATE {
    ClusterNodeStateUnknown = -1,ClusterNodeUp,ClusterNodeDown,ClusterNodePaused,ClusterNodeJoining
  } CLUSTER_NODE_STATE;
#endif

  HNODE WINAPI OpenClusterNode(HCLUSTER hCluster,LPCWSTR lpszNodeName);
  WINBOOL WINAPI CloseClusterNode(HNODE hNode);
  CLUSTER_NODE_STATE WINAPI GetClusterNodeState(HNODE hNode);
  DWORD WINAPI GetClusterNodeId(HNODE hNode,LPWSTR lpszNodeId,LPDWORD lpcchName);
#define GetCurrentClusterNodeId(_lpszNodeId_,_lpcchName_) GetClusterNodeId(NULL,(_lpszNodeId_),(_lpcchName_))
  HCLUSTER WINAPI GetClusterFromNode(HNODE hNode);
  DWORD WINAPI PauseClusterNode(HNODE hNode);
  DWORD WINAPI ResumeClusterNode(HNODE hNode);
  DWORD WINAPI EvictClusterNode(HNODE hNode);
  HNODEENUM WINAPI ClusterNodeOpenEnum(HNODE hNode,DWORD dwType);
  DWORD WINAPI ClusterNodeGetEnumCount(HNODEENUM hNodeEnum);
  DWORD WINAPI ClusterNodeCloseEnum(HNODEENUM hNodeEnum);
  DWORD WINAPI ClusterNodeEnum(HNODEENUM hNodeEnum,DWORD dwIndex,LPDWORD lpdwType,LPWSTR lpszName,LPDWORD lpcchName);
  DWORD WINAPI EvictClusterNodeEx(HNODE hNode,DWORD dwTimeOut,HRESULT *phrCleanupStatus);
  HKEY WINAPI GetClusterResourceTypeKey(HCLUSTER hCluster,LPCWSTR lpszTypeName,REGSAM samDesired);

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_GROUP_ENUM {
    CLUSTER_GROUP_ENUM_CONTAINS = 0x00000001,CLUSTER_GROUP_ENUM_NODES = 0x00000002,
    CLUSTER_GROUP_ENUM_ALL = (CLUSTER_GROUP_ENUM_CONTAINS | CLUSTER_GROUP_ENUM_NODES)
  } CLUSTER_GROUP_ENUM;

  typedef enum CLUSTER_GROUP_STATE {
    ClusterGroupStateUnknown = -1,ClusterGroupOnline,ClusterGroupOffline,ClusterGroupFailed,ClusterGroupPartialOnline,ClusterGroupPending
  } CLUSTER_GROUP_STATE;

  typedef enum CLUSTER_GROUP_AUTOFAILBACK_TYPE {
    ClusterGroupPreventFailback = 0,ClusterGroupAllowFailback,ClusterGroupFailbackTypeCount
  } CLUSTER_GROUP_AUTOFAILBACK_TYPE,CGAFT;
#endif

  HGROUP WINAPI CreateClusterGroup(HCLUSTER hCluster,LPCWSTR lpszGroupName);
  HGROUP WINAPI OpenClusterGroup(HCLUSTER hCluster,LPCWSTR lpszGroupName);
  WINBOOL WINAPI CloseClusterGroup(HGROUP hGroup);
  HCLUSTER WINAPI GetClusterFromGroup(HGROUP hGroup);
  CLUSTER_GROUP_STATE WINAPI GetClusterGroupState(HGROUP hGroup,LPWSTR lpszNodeName,LPDWORD lpcchNodeName);
  DWORD WINAPI SetClusterGroupName(HGROUP hGroup,LPCWSTR lpszGroupName);
  DWORD WINAPI SetClusterGroupNodeList(HGROUP hGroup,DWORD NodeCount,HNODE NodeList[]);
  DWORD WINAPI OnlineClusterGroup(HGROUP hGroup,HNODE hDestinationNode);
  DWORD WINAPI MoveClusterGroup(HGROUP hGroup,HNODE hDestinationNode);
  DWORD WINAPI OfflineClusterGroup(HGROUP hGroup);
  DWORD WINAPI DeleteClusterGroup(HGROUP hGroup);
  HGROUPENUM WINAPI ClusterGroupOpenEnum(HGROUP hGroup,DWORD dwType);
  DWORD WINAPI ClusterGroupGetEnumCount(HGROUPENUM hGroupEnum);
  DWORD WINAPI ClusterGroupEnum(HGROUPENUM hGroupEnum,DWORD dwIndex,LPDWORD lpdwType,LPWSTR lpszResourceName,LPDWORD lpcchName);
  DWORD WINAPI ClusterGroupCloseEnum(HGROUPENUM hGroupEnum);

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_RESOURCE_STATE {
    ClusterResourceStateUnknown = -1,ClusterResourceInherited,ClusterResourceInitializing,ClusterResourceOnline,ClusterResourceOffline,
    ClusterResourceFailed,ClusterResourcePending = 128,ClusterResourceOnlinePending,ClusterResourceOfflinePending
  } CLUSTER_RESOURCE_STATE;

  typedef enum CLUSTER_RESOURCE_RESTART_ACTION {
    ClusterResourceDontRestart = 0,ClusterResourceRestartNoNotify,ClusterResourceRestartNotify,ClusterResourceRestartActionCount
  } CLUSTER_RESOURCE_RESTART_ACTION,CRRA;

  typedef enum CLUSTER_RESOURCE_CREATE_FLAGS {
    CLUSTER_RESOURCE_DEFAULT_MONITOR = 0,CLUSTER_RESOURCE_SEPARATE_MONITOR = 1,CLUSTER_RESOURCE_VALID_FLAGS = CLUSTER_RESOURCE_SEPARATE_MONITOR
  } CLUSTER_RESOURCE_CREATE_FLAGS;
#endif

  HRESOURCE WINAPI CreateClusterResource(HGROUP hGroup,LPCWSTR lpszResourceName,LPCWSTR lpszResourceType,DWORD dwFlags);
  HRESOURCE WINAPI OpenClusterResource(HCLUSTER hCluster,LPCWSTR lpszResourceName);
  WINBOOL WINAPI CloseClusterResource(HRESOURCE hResource);
  HCLUSTER WINAPI GetClusterFromResource(HRESOURCE hResource);
  DWORD WINAPI DeleteClusterResource(HRESOURCE hResource);
  CLUSTER_RESOURCE_STATE WINAPI GetClusterResourceState(HRESOURCE hResource,LPWSTR lpszNodeName,LPDWORD lpcchNodeName,LPWSTR lpszGroupName,LPDWORD lpcchGroupName);
  DWORD WINAPI SetClusterResourceName(HRESOURCE hResource,LPCWSTR lpszResourceName);
  DWORD WINAPI FailClusterResource(HRESOURCE hResource);
  DWORD WINAPI OnlineClusterResource(HRESOURCE hResource);
  DWORD WINAPI OfflineClusterResource(HRESOURCE hResource);
  DWORD WINAPI ChangeClusterResourceGroup(HRESOURCE hResource,HGROUP hGroup);
  DWORD WINAPI AddClusterResourceNode(HRESOURCE hResource,HNODE hNode);
  DWORD WINAPI RemoveClusterResourceNode(HRESOURCE hResource,HNODE hNode);
  DWORD WINAPI AddClusterResourceDependency(HRESOURCE hResource,HRESOURCE hDependsOn);
  DWORD WINAPI RemoveClusterResourceDependency(HRESOURCE hResource,HRESOURCE hDependsOn);
  WINBOOL WINAPI CanResourceBeDependent(HRESOURCE hResource,HRESOURCE hResourceDependent);
  DWORD WINAPI ClusterResourceControl(HRESOURCE hResource,HNODE hHostNode,DWORD dwControlCode,LPVOID lpInBuffer,DWORD cbInBufferSize,LPVOID lpOutBuffer,DWORD cbOutBufferSize,LPDWORD lpBytesReturned);
  DWORD WINAPI ClusterResourceTypeControl(HCLUSTER hCluster,LPCWSTR lpszResourceTypeName,HNODE hHostNode,DWORD dwControlCode,LPVOID lpInBuffer,DWORD nInBufferSize,LPVOID lpOutBuffer,DWORD nOutBufferSize,LPDWORD lpBytesReturned);
  DWORD WINAPI ClusterGroupControl(HGROUP hGroup,HNODE hHostNode,DWORD dwControlCode,LPVOID lpInBuffer,DWORD nInBufferSize,LPVOID lpOutBuffer,DWORD nOutBufferSize,LPDWORD lpBytesReturned);
  DWORD WINAPI ClusterNodeControl(HNODE hNode,HNODE hHostNode,DWORD dwControlCode,LPVOID lpInBuffer,DWORD nInBufferSize,LPVOID lpOutBuffer,DWORD nOutBufferSize,LPDWORD lpBytesReturned);
  WINBOOL WINAPI GetClusterResourceNetworkName(HRESOURCE hResource,LPWSTR lpBuffer,LPDWORD nSize);

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_PROPERTY_TYPE {
    CLUSPROP_TYPE_UNKNOWN = -1,CLUSPROP_TYPE_ENDMARK = 0,CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_TYPE_RESCLASS,CLUSPROP_TYPE_RESERVED1,CLUSPROP_TYPE_NAME,
    CLUSPROP_TYPE_SIGNATURE,CLUSPROP_TYPE_SCSI_ADDRESS,CLUSPROP_TYPE_DISK_NUMBER,CLUSPROP_TYPE_PARTITION_INFO,CLUSPROP_TYPE_FTSET_INFO,
    CLUSPROP_TYPE_DISK_SERIALNUMBER,
    CLUSPROP_TYPE_DISK_GUID           = 11,
    CLUSPROP_TYPE_DISK_SIZE           = 12,
    CLUSPROP_TYPE_PARTITION_INFO_EX   = 13,
    CLUSPROP_TYPE_USER=32768
  } CLUSTER_PROPERTY_TYPE;

  typedef enum CLUSTER_PROPERTY_FORMAT {
    CLUSPROP_FORMAT_UNKNOWN = 0,CLUSPROP_FORMAT_BINARY,CLUSPROP_FORMAT_DWORD,CLUSPROP_FORMAT_SZ,CLUSPROP_FORMAT_EXPAND_SZ,CLUSPROP_FORMAT_MULTI_SZ,
    CLUSPROP_FORMAT_ULARGE_INTEGER,CLUSPROP_FORMAT_LONG,CLUSPROP_FORMAT_EXPANDED_SZ,CLUSPROP_FORMAT_SECURITY_DESCRIPTOR,CLUSPROP_FORMAT_LARGE_INTEGER,
    CLUSPROP_FORMAT_WORD,
    CLUSPROP_FORMAT_FILETIME              = 12,
    CLUSPROP_FORMAT_USER=32768
  } CLUSTER_PROPERTY_FORMAT;
#endif

#define CLUSPROP_SYNTAX_VALUE(type,format) ((DWORD) ((type << 16) | format))

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_PROPERTY_SYNTAX {
    CLUSPROP_SYNTAX_ENDMARK = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_ENDMARK,CLUSPROP_FORMAT_UNKNOWN),
    CLUSPROP_SYNTAX_NAME = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_NAME,CLUSPROP_FORMAT_SZ),
    CLUSPROP_SYNTAX_RESCLASS = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_RESCLASS,CLUSPROP_FORMAT_DWORD),
    CLUSPROP_SYNTAX_LIST_VALUE_SZ = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_SZ),
    CLUSPROP_SYNTAX_LIST_VALUE_EXPAND_SZ = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_EXPAND_SZ),
    CLUSPROP_SYNTAX_LIST_VALUE_DWORD = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_DWORD),
    CLUSPROP_SYNTAX_LIST_VALUE_BINARY = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_BINARY),
    CLUSPROP_SYNTAX_LIST_VALUE_MULTI_SZ = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_MULTI_SZ),
    CLUSPROP_SYNTAX_LIST_VALUE_LONG = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_LONG),
    CLUSPROP_SYNTAX_LIST_VALUE_EXPANDED_SZ = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_EXPANDED_SZ),
    CLUSPROP_SYNTAX_LIST_VALUE_SECURITY_DESCRIPTOR = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_SECURITY_DESCRIPTOR),
    CLUSPROP_SYNTAX_LIST_VALUE_LARGE_INTEGER = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_LARGE_INTEGER),
    CLUSPROP_SYNTAX_LIST_VALUE_ULARGE_INTEGER = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_ULARGE_INTEGER),
    CLUSPROP_SYNTAX_DISK_SIGNATURE = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_SIGNATURE,CLUSPROP_FORMAT_DWORD),
    CLUSPROP_SYNTAX_SCSI_ADDRESS = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_SCSI_ADDRESS,CLUSPROP_FORMAT_DWORD),
    CLUSPROP_SYNTAX_DISK_NUMBER = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_DISK_NUMBER,CLUSPROP_FORMAT_DWORD),
    CLUSPROP_SYNTAX_PARTITION_INFO = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_PARTITION_INFO,CLUSPROP_FORMAT_BINARY),
    CLUSPROP_SYNTAX_FTSET_INFO = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_FTSET_INFO,CLUSPROP_FORMAT_BINARY),
    CLUSPROP_SYNTAX_DISK_SERIALNUMBER = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_DISK_SERIALNUMBER,CLUSPROP_FORMAT_SZ),
    CLUSPROP_SYNTAX_DISK_GUID = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_DISK_GUID,CLUSPROP_FORMAT_SZ), /*0x000b0003*/
    CLUSPROP_SYNTAX_DISK_SIZE = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_DISK_SIZE,CLUSPROP_FORMAT_ULARGE_INTEGER),/*0x000c0006*/
    CLUSPROP_SYNTAX_PARTITION_INFO_EX = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_PARTITION_INFO_EX,CLUSPROP_FORMAT_BINARY),/*0x000d0001*/
    CLUSPROP_SYNTAX_LIST_VALUE_FILETIME = CLUSPROP_SYNTAX_VALUE(CLUSPROP_TYPE_LIST_VALUE,CLUSPROP_FORMAT_FILETIME) /*0x0001000c*/
  } CLUSTER_PROPERTY_SYNTAX;
#endif

#define CLUS_ACCESS_ANY 0
#define CLUS_ACCESS_READ 0x01
#define CLUS_ACCESS_WRITE 0x02

#define CLUS_NO_MODIFY 0
#define CLUS_MODIFY 0x01

#define CLUS_NOT_GLOBAL 0
#define CLUS_GLOBAL 0x01

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_CONTROL_OBJECT {
    CLUS_OBJECT_INVALID=0,CLUS_OBJECT_RESOURCE,CLUS_OBJECT_RESOURCE_TYPE,CLUS_OBJECT_GROUP,CLUS_OBJECT_NODE,CLUS_OBJECT_NETWORK,
    CLUS_OBJECT_NETINTERFACE,CLUS_OBJECT_CLUSTER,CLUS_OBJECT_USER=128
  } CLUSTER_CONTROL_OBJECT;
#endif

#define CLUSCTL_ACCESS_SHIFT 0
#define CLUSCTL_FUNCTION_SHIFT 2
#define CLCTL_INTERNAL_SHIFT 20
#define CLCTL_USER_SHIFT 21
#define CLCTL_MODIFY_SHIFT 22
#define CLCTL_GLOBAL_SHIFT 23
#define CLUSCTL_OBJECT_SHIFT 24

#define CLCTL_INTERNAL_MASK (1<<CLCTL_INTERNAL_SHIFT)
#define CLCTL_USER_MASK (1<<CLCTL_USER_SHIFT)
#define CLCTL_MODIFY_MASK (1<<CLCTL_MODIFY_SHIFT)
#define CLCTL_GLOBAL_MASK (1<<CLCTL_GLOBAL_SHIFT)
#define CLUSCTL_CONTROL_CODE_MASK 0x3FFFFF
#define CLUSCTL_OBJECT_MASK 0xFF
#define CLUSCTL_ACCESS_MODE_MASK 0x03

#define CLCTL_CLUSTER_BASE 0
#define CLCTL_USER_BASE (1<<CLCTL_USER_SHIFT)

#define CLCTL_EXTERNAL_CODE(Function,Access,Modify) (((Access) << CLUSCTL_ACCESS_SHIFT) | ((CLCTL_CLUSTER_BASE + Function) << CLUSCTL_FUNCTION_SHIFT) | ((Modify) << CLCTL_MODIFY_SHIFT))
#define CLCTL_INTERNAL_CODE(Function,Access,Modify) (((Access) << CLUSCTL_ACCESS_SHIFT) | CLCTL_INTERNAL_MASK | ((CLCTL_CLUSTER_BASE + Function) << CLUSCTL_FUNCTION_SHIFT) | ((Modify) << CLCTL_MODIFY_SHIFT))

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLCTL_CODES {
    CLCTL_UNKNOWN = CLCTL_EXTERNAL_CODE(0,CLUS_ACCESS_ANY,CLUS_NO_MODIFY),
    CLCTL_GET_CHARACTERISTICS = CLCTL_EXTERNAL_CODE(1,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_FLAGS = CLCTL_EXTERNAL_CODE(2,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_CLASS_INFO = CLCTL_EXTERNAL_CODE(3,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_REQUIRED_DEPENDENCIES = CLCTL_EXTERNAL_CODE(4,CLUS_ACCESS_READ,
    CLUS_NO_MODIFY),CLCTL_GET_ARB_TIMEOUT = CLCTL_EXTERNAL_CODE(5,CLUS_ACCESS_READ,
    CLUS_NO_MODIFY),CLCTL_GET_NAME = CLCTL_EXTERNAL_CODE(10,CLUS_ACCESS_READ,
    CLUS_NO_MODIFY),CLCTL_GET_RESOURCE_TYPE = CLCTL_EXTERNAL_CODE(11,
    CLUS_ACCESS_READ,CLUS_NO_MODIFY),CLCTL_GET_NODE = CLCTL_EXTERNAL_CODE(12,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_NETWORK = CLCTL_EXTERNAL_CODE(13,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_ID = CLCTL_EXTERNAL_CODE(14,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_FQDN = CLCTL_EXTERNAL_CODE(15,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_CLUSTER_SERVICE_ACCOUNT_NAME = CLCTL_EXTERNAL_CODE(16,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_CHECK_VOTER_EVICT                    = CLCTL_EXTERNAL_CODE(17,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000045*/
    CLCTL_CHECK_VOTER_DOWN                     = CLCTL_EXTERNAL_CODE(18,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000049*/
    CLCTL_SHUTDOWN                             = CLCTL_EXTERNAL_CODE(19,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x0000004d*/
    CLCTL_ENUM_COMMON_PROPERTIES = CLCTL_EXTERNAL_CODE(20,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_RO_COMMON_PROPERTIES = CLCTL_EXTERNAL_CODE(21,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_COMMON_PROPERTIES = CLCTL_EXTERNAL_CODE(22,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_SET_COMMON_PROPERTIES = CLCTL_EXTERNAL_CODE(23,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_VALIDATE_COMMON_PROPERTIES = CLCTL_EXTERNAL_CODE(24,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_COMMON_PROPERTY_FMTS = CLCTL_EXTERNAL_CODE(25,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_COMMON_RESOURCE_PROPERTY_FMTS = CLCTL_EXTERNAL_CODE(26,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_ENUM_PRIVATE_PROPERTIES = CLCTL_EXTERNAL_CODE(30,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_RO_PRIVATE_PROPERTIES = CLCTL_EXTERNAL_CODE(31,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_PRIVATE_PROPERTIES = CLCTL_EXTERNAL_CODE(32,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_SET_PRIVATE_PROPERTIES = CLCTL_EXTERNAL_CODE(33,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_VALIDATE_PRIVATE_PROPERTIES = CLCTL_EXTERNAL_CODE(34,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_PRIVATE_PROPERTY_FMTS = CLCTL_EXTERNAL_CODE(35,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_PRIVATE_RESOURCE_PROPERTY_FMTS= CLCTL_EXTERNAL_CODE(36,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_ADD_REGISTRY_CHECKPOINT = CLCTL_EXTERNAL_CODE(40,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_DELETE_REGISTRY_CHECKPOINT = CLCTL_EXTERNAL_CODE(41,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_GET_REGISTRY_CHECKPOINTS = CLCTL_EXTERNAL_CODE(42,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_ADD_CRYPTO_CHECKPOINT = CLCTL_EXTERNAL_CODE(43,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_DELETE_CRYPTO_CHECKPOINT = CLCTL_EXTERNAL_CODE(44,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_GET_CRYPTO_CHECKPOINTS = CLCTL_EXTERNAL_CODE(45,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_RESOURCE_UPGRADE_DLL = CLCTL_EXTERNAL_CODE(46,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_ADD_REGISTRY_CHECKPOINT_64BIT = CLCTL_EXTERNAL_CODE(47,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_ADD_REGISTRY_CHECKPOINT_32BIT = CLCTL_EXTERNAL_CODE(48,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_GET_LOADBAL_PROCESS_LIST = CLCTL_EXTERNAL_CODE(50,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_GET_NETWORK_NAME = CLCTL_EXTERNAL_CODE(90,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_NETNAME_GET_VIRTUAL_SERVER_TOKEN = CLCTL_EXTERNAL_CODE(91,CLUS_ACCESS_READ,
    CLUS_NO_MODIFY),CLCTL_NETNAME_REGISTER_DNS_RECORDS = CLCTL_EXTERNAL_CODE(92,CLUS_ACCESS_WRITE,CLUS_NO_MODIFY),
    CLCTL_NETNAME_CREDS_UPDATED = (CLCTL_EXTERNAL_CODE(98,CLUS_ACCESS_WRITE,CLUS_MODIFY)|CLCTL_GLOBAL_MASK),
    CLCTL_STORAGE_GET_DISK_INFO = CLCTL_EXTERNAL_CODE(100,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_STORAGE_GET_AVAILABLE_DISKS = CLCTL_EXTERNAL_CODE(101,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_STORAGE_IS_PATH_VALID = CLCTL_EXTERNAL_CODE(102,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_STORAGE_GET_ALL_AVAILABLE_DISKS = (CLCTL_EXTERNAL_CODE(103,CLUS_ACCESS_READ,CLUS_NO_MODIFY) | CLCTL_GLOBAL_MASK),
    CLCTL_QUERY_DELETE = CLCTL_EXTERNAL_CODE(110,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_QUERY_MAINTENANCE_MODE = CLCTL_EXTERNAL_CODE(120,CLUS_ACCESS_READ,CLUS_NO_MODIFY),
    CLCTL_SET_MAINTENANCE_MODE = CLCTL_EXTERNAL_CODE(121,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_STORAGE_SET_DRIVELETTER              = CLCTL_EXTERNAL_CODE(122,CLUS_ACCESS_WRITE,CLUS_MODIFY),/*0x004001ea*/
    CLCTL_STORAGE_GET_DRIVELETTERS             = CLCTL_EXTERNAL_CODE(123,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x000001ed*/
    CLCTL_STORAGE_GET_DISK_INFO_EX             = CLCTL_EXTERNAL_CODE(124,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x000001f1*/
    CLCTL_STORAGE_GET_AVAILABLE_DISKS_EX       = CLCTL_EXTERNAL_CODE(125,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x000001f5*/
    CLCTL_STORAGE_REMAP_DRIVELETTER            = CLCTL_EXTERNAL_CODE(128,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000201,*/
    CLCTL_STORAGE_GET_DISKID                   = CLCTL_EXTERNAL_CODE(129,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000205*/
    CLCTL_STORAGE_IS_CLUSTERABLE               = CLCTL_EXTERNAL_CODE(130,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000209*/
    CLCTL_STORAGE_REMOVE_VM_OWNERSHIP          = CLCTL_EXTERNAL_CODE(131,CLUS_ACCESS_WRITE,CLUS_MODIFY),/*0x0040020e*/
    CLCTL_STORAGE_GET_MOUNTPOINTS              = CLCTL_EXTERNAL_CODE(132,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000211*/
    CLCTL_STORAGE_CLUSTER_DISK                 = (CLCTL_EXTERNAL_CODE(132,CLUS_ACCESS_WRITE,CLUS_MODIFY)|CLCTL_GLOBAL_MASK),/*0x00c00212*/
    CLCTL_STORAGE_GET_DIRTY                    = CLCTL_EXTERNAL_CODE(134,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000219*/
    CLCTL_STORAGE_GET_SHARED_VOLUME_INFO       = CLCTL_EXTERNAL_CODE(137,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/* 0x00000225 */
    CLCTL_STORAGE_IS_CSV_FILE                  = CLCTL_EXTERNAL_CODE(138,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000229*/
    CLCTL_VALIDATE_PATH                        = CLCTL_EXTERNAL_CODE(140,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000231,*/
    CLCTL_VALIDATE_NETNAME                     = CLCTL_EXTERNAL_CODE(141,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000235*/
    CLCTL_VALIDATE_DIRECTORY                   = CLCTL_EXTERNAL_CODE(142,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000239*/
    CLCTL_BATCH_BLOCK_KEY                      = CLCTL_EXTERNAL_CODE(143,CLUS_ACCESS_WRITE,CLUS_NO_MODIFY),/*0x0000023e*/
    CLCTL_BATCH_UNBLOCK_KEY                    = CLCTL_EXTERNAL_CODE(144,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000241*/
    CLCTL_FILESERVER_SHARE_ADD                 = CLCTL_EXTERNAL_CODE(145,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00400245*/
    CLCTL_FILESERVER_SHARE_DEL                 = CLCTL_EXTERNAL_CODE(146,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00400249*/
    CLCTL_FILESERVER_SHARE_MODIFY              = CLCTL_EXTERNAL_CODE(147,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x0040024d*/
    CLCTL_FILESERVER_SHARE_REPORT              = CLCTL_EXTERNAL_CODE(148,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000251*/
    CLCTL_ENABLE_SHARED_VOLUME_DIRECTIO        = CLCTL_EXTERNAL_CODE(162,CLUS_ACCESS_WRITE,CLUS_MODIFY),/*0x0040028a*/
    CLCTL_DISABLE_SHARED_VOLUME_DIRECTIO       = CLCTL_EXTERNAL_CODE(163,CLUS_ACCESS_WRITE,CLUS_MODIFY),/*0x0040028e*/
    CLCTL_GET_SHARED_VOLUME_ID                 = CLCTL_EXTERNAL_CODE(164,CLUS_ACCESS_READ,CLUS_NO_MODIFY),/*0x00000291*/
    CLCTL_SET_CSV_MAINTENANCE_MODE             = CLCTL_EXTERNAL_CODE(165,CLUS_ACCESS_WRITE,CLUS_MODIFY),/*0x00400296*/
    CLCTL_SET_SHARED_VOLUME_BACKUP_MODE        = CLCTL_EXTERNAL_CODE(166,CLUS_ACCESS_WRITE,CLUS_MODIFY),/*0x0040029a,*/
    CLCTL_DELETE = CLCTL_INTERNAL_CODE(1,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_INSTALL_NODE = CLCTL_INTERNAL_CODE(2,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_EVICT_NODE = CLCTL_INTERNAL_CODE(3,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_ADD_DEPENDENCY = CLCTL_INTERNAL_CODE(4,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_REMOVE_DEPENDENCY = CLCTL_INTERNAL_CODE(5,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_ADD_OWNER = CLCTL_INTERNAL_CODE(6,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_REMOVE_OWNER = CLCTL_INTERNAL_CODE(7,CLUS_ACCESS_WRITE,
    CLUS_MODIFY),CLCTL_SET_NAME = CLCTL_INTERNAL_CODE(9,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_CLUSTER_NAME_CHANGED = CLCTL_INTERNAL_CODE(10,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_CLUSTER_VERSION_CHANGED = CLCTL_INTERNAL_CODE(11,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_FIXUP_ON_UPGRADE = CLCTL_INTERNAL_CODE(12,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_STARTING_PHASE1 = CLCTL_INTERNAL_CODE(13,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_STARTING_PHASE2 = CLCTL_INTERNAL_CODE(14,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_HOLD_IO = CLCTL_INTERNAL_CODE(15,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_RESUME_IO = CLCTL_INTERNAL_CODE(16,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_FORCE_QUORUM = CLCTL_INTERNAL_CODE(17,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_INITIALIZE = CLCTL_INTERNAL_CODE(18,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_STATE_CHANGE_REASON = CLCTL_INTERNAL_CODE(19,CLUS_ACCESS_WRITE,CLUS_MODIFY),
    CLCTL_PROVIDER_STATE_CHANGE = CLCTL_INTERNAL_CODE(20,CLUS_ACCESS_WRITE, CLUS_MODIFY),/*0x00500052*/
    CLCTL_LEAVING_GROUP = CLCTL_INTERNAL_CODE(21,CLUS_ACCESS_WRITE, CLUS_MODIFY),/*0x00500056,*/
    CLCTL_JOINING_GROUP = CLCTL_INTERNAL_CODE(22,CLUS_ACCESS_WRITE, CLUS_MODIFY),/*0x0050005a*/
    CLCTL_FSWITNESS_GET_EPOCH_INFO = CLCTL_INTERNAL_CODE(23,CLUS_ACCESS_READ, CLUS_NO_MODIFY),/*0x0010005d*/
    CLCTL_FSWITNESS_SET_EPOCH_INFO = CLCTL_INTERNAL_CODE(24,CLUS_ACCESS_WRITE, CLUS_MODIFY),/*0x00500062*/
    CLCTL_FSWITNESS_RELEASE_LOCK = CLCTL_INTERNAL_CODE(25,CLUS_ACCESS_WRITE, CLUS_MODIFY),/*0x00500066,*/
    CLCTL_NETNAME_CREDS_NOTIFYCAM = CLCTL_INTERNAL_CODE(26,CLUS_ACCESS_WRITE, CLUS_MODIFY),/*0x0050006a*/
    CLCTL_STORAGE_GET_DISK_NUMBER = CLCTL_INTERNAL_CODE(27,CLUS_ACCESS_READ, CLUS_NO_MODIFY) /*0x0010006d */
  } CLCTL_CODES;
#endif

#define CLUSCTL_RESOURCE_CODE(Function) (((CLUS_OBJECT_RESOURCE << CLUSCTL_OBJECT_SHIFT) | Function))
#define CLUSCTL_RESOURCE_TYPE_CODE(Function) (((CLUS_OBJECT_RESOURCE_TYPE << CLUSCTL_OBJECT_SHIFT) | Function))
#define CLUSCTL_GROUP_CODE(Function) (((CLUS_OBJECT_GROUP << CLUSCTL_OBJECT_SHIFT) | Function))
#define CLUSCTL_NODE_CODE(Function) (((CLUS_OBJECT_NODE << CLUSCTL_OBJECT_SHIFT) | Function))
#define CLUSCTL_NETWORK_CODE(Function) (((CLUS_OBJECT_NETWORK << CLUSCTL_OBJECT_SHIFT) | Function))
#define CLUSCTL_NETINTERFACE_CODE(Function) (((CLUS_OBJECT_NETINTERFACE << CLUSCTL_OBJECT_SHIFT) | Function))
#define CLUSCTL_CLUSTER_CODE(Function) (((CLUS_OBJECT_CLUSTER << CLUSCTL_OBJECT_SHIFT) | Function))
#define CLUSCTL_USER_CODE(Function,Object) (((Object) << CLUSCTL_OBJECT_SHIFT) | ((CLCTL_USER_BASE + Function) << CLUSCTL_FUNCTION_SHIFT))
#define CLUSCTL_GET_CONTROL_FUNCTION(ControlCode) ((ControlCode >> CLUSCTL_ACCESS_SHIFT) & CLUSCTL_CONTROL_CODE_MASK)
#define CLUSCTL_GET_ACCESS_MODE(ControlCode) ((ControlCode >> CLUSCTL_ACCESS_SHIFT) & CLUSCTL_ACCESS_MODE_MASK)
#define CLUSCTL_GET_CONTROL_OBJECT(ControlCode) ((ControlCode >> CLUSCTL_OBJECT_SHIFT) & CLUSCTL_OBJECT_MASK)

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSCTL_RESOURCE_CODES {
    CLUSCTL_RESOURCE_UNKNOWN = CLUSCTL_RESOURCE_CODE(CLCTL_UNKNOWN),
    CLUSCTL_RESOURCE_GET_CHARACTERISTICS = CLUSCTL_RESOURCE_CODE(CLCTL_GET_CHARACTERISTICS),
    CLUSCTL_RESOURCE_GET_FLAGS = CLUSCTL_RESOURCE_CODE(CLCTL_GET_FLAGS),
    CLUSCTL_RESOURCE_GET_CLASS_INFO = CLUSCTL_RESOURCE_CODE(CLCTL_GET_CLASS_INFO),
    CLUSCTL_RESOURCE_GET_REQUIRED_DEPENDENCIES = CLUSCTL_RESOURCE_CODE(CLCTL_GET_REQUIRED_DEPENDENCIES),
    CLUSCTL_RESOURCE_GET_NAME = CLUSCTL_RESOURCE_CODE(CLCTL_GET_NAME),
    CLUSCTL_RESOURCE_GET_ID = CLUSCTL_RESOURCE_CODE(CLCTL_GET_ID),
    CLUSCTL_RESOURCE_GET_RESOURCE_TYPE = CLUSCTL_RESOURCE_CODE(CLCTL_GET_RESOURCE_TYPE),
    CLUSCTL_RESOURCE_ENUM_COMMON_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_ENUM_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_GET_RO_COMMON_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_GET_RO_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_GET_COMMON_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_GET_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_SET_COMMON_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_SET_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_VALIDATE_COMMON_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_VALIDATE_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_GET_COMMON_PROPERTY_FMTS = CLUSCTL_RESOURCE_CODE(CLCTL_GET_COMMON_PROPERTY_FMTS),
    CLUSCTL_RESOURCE_ENUM_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_ENUM_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_GET_RO_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_GET_RO_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_GET_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_GET_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_SET_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_SET_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_VALIDATE_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_CODE(CLCTL_VALIDATE_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_GET_PRIVATE_PROPERTY_FMTS = CLUSCTL_RESOURCE_CODE(CLCTL_GET_PRIVATE_PROPERTY_FMTS),
    CLUSCTL_RESOURCE_ADD_REGISTRY_CHECKPOINT = CLUSCTL_RESOURCE_CODE(CLCTL_ADD_REGISTRY_CHECKPOINT),
    CLUSCTL_RESOURCE_DELETE_REGISTRY_CHECKPOINT = CLUSCTL_RESOURCE_CODE(CLCTL_DELETE_REGISTRY_CHECKPOINT),
    CLUSCTL_RESOURCE_GET_REGISTRY_CHECKPOINTS = CLUSCTL_RESOURCE_CODE(CLCTL_GET_REGISTRY_CHECKPOINTS),
    CLUSCTL_RESOURCE_ADD_CRYPTO_CHECKPOINT = CLUSCTL_RESOURCE_CODE(CLCTL_ADD_CRYPTO_CHECKPOINT),
    CLUSCTL_RESOURCE_DELETE_CRYPTO_CHECKPOINT = CLUSCTL_RESOURCE_CODE(CLCTL_DELETE_CRYPTO_CHECKPOINT),
    CLUSCTL_RESOURCE_GET_CRYPTO_CHECKPOINTS = CLUSCTL_RESOURCE_CODE(CLCTL_GET_CRYPTO_CHECKPOINTS),
    CLUSCTL_RESOURCE_GET_LOADBAL_PROCESS_LIST = CLUSCTL_RESOURCE_CODE(CLCTL_GET_LOADBAL_PROCESS_LIST),
    CLUSCTL_RESOURCE_GET_NETWORK_NAME = CLUSCTL_RESOURCE_CODE(CLCTL_GET_NETWORK_NAME),
    CLUSCTL_RESOURCE_NETNAME_GET_VIRTUAL_SERVER_TOKEN = CLUSCTL_RESOURCE_CODE(CLCTL_NETNAME_GET_VIRTUAL_SERVER_TOKEN),
    CLUSCTL_RESOURCE_NETNAME_REGISTER_DNS_RECORDS = CLUSCTL_RESOURCE_CODE(CLCTL_NETNAME_REGISTER_DNS_RECORDS),
    CLUSCTL_RESOURCE_STORAGE_GET_DISK_INFO = CLUSCTL_RESOURCE_CODE(CLCTL_STORAGE_GET_DISK_INFO),
    CLUSCTL_RESOURCE_STORAGE_IS_PATH_VALID = CLUSCTL_RESOURCE_CODE(CLCTL_STORAGE_IS_PATH_VALID),
    CLUSCTL_RESOURCE_QUERY_DELETE = CLUSCTL_RESOURCE_CODE(CLCTL_QUERY_DELETE),
    CLUSCTL_RESOURCE_UPGRADE_DLL = CLUSCTL_RESOURCE_CODE(CLCTL_RESOURCE_UPGRADE_DLL),
    CLUSCTL_RESOURCE_ADD_REGISTRY_CHECKPOINT_64BIT = CLUSCTL_RESOURCE_CODE(CLCTL_ADD_REGISTRY_CHECKPOINT_64BIT),
    CLUSCTL_RESOURCE_ADD_REGISTRY_CHECKPOINT_32BIT = CLUSCTL_RESOURCE_CODE(CLCTL_ADD_REGISTRY_CHECKPOINT_32BIT),
    CLUSCTL_RESOURCE_QUERY_MAINTENANCE_MODE = CLUSCTL_RESOURCE_CODE(CLCTL_QUERY_MAINTENANCE_MODE),
    CLUSCTL_RESOURCE_SET_MAINTENANCE_MODE = CLUSCTL_RESOURCE_CODE(CLCTL_SET_MAINTENANCE_MODE),
    CLUSCTL_RESOURCE_STORAGE_SET_DRIVELETTER            = CLUSCTL_RESOURCE_CODE(CLCTL_STORAGE_SET_DRIVELETTER),/*0x014001ea*/
    CLUSCTL_RESOURCE_STORAGE_GET_DISK_INFO_EX           = CLUSCTL_RESOURCE_CODE(CLCTL_STORAGE_GET_DISK_INFO_EX),/*0x010001f1*/
    CLUSCTL_RESOURCE_FILESERVER_SHARE_ADD               = CLUSCTL_RESOURCE_CODE(CLCTL_FILESERVER_SHARE_ADD),/*0x01400245*/
    CLUSCTL_RESOURCE_FILESERVER_SHARE_DEL               = CLUSCTL_RESOURCE_CODE(CLCTL_FILESERVER_SHARE_DEL),/*0x01400249*/
    CLUSCTL_RESOURCE_FILESERVER_SHARE_MODIFY            = CLUSCTL_RESOURCE_CODE(CLCTL_FILESERVER_SHARE_MODIFY),/*0x0140024d*/
    CLUSCTL_RESOURCE_FILESERVER_SHARE_REPORT            = CLUSCTL_RESOURCE_CODE(CLCTL_FILESERVER_SHARE_REPORT),/*0x01000251*/
    CLUSCTL_RESOURCE_STORAGE_GET_MOUNTPOINTS            = CLUSCTL_RESOURCE_CODE(CLCTL_STORAGE_GET_MOUNTPOINTS),/*0x01000211*/
    CLUSCTL_RESOURCE_STORAGE_CLUSTER_DISK               = CLUSCTL_RESOURCE_CODE(CLCTL_STORAGE_CLUSTER_DISK),/*0x01c00212*/
    CLUSCTL_RESOURCE_STORAGE_GET_DIRTY                  = CLUSCTL_RESOURCE_CODE(CLCTL_STORAGE_GET_DIRTY),/*0x01000219*/
    CLUSCTL_RESOURCE_STORAGE_GET_SHARED_VOLUME_INFO     = CLUSCTL_RESOURCE_CODE(CLCTL_STORAGE_GET_SHARED_VOLUME_INFO),
    CLUSCTL_RESOURCE_SET_CSV_MAINTENANCE_MODE           = CLUSCTL_RESOURCE_CODE(CLCTL_SET_CSV_MAINTENANCE_MODE),/*0x00400296*/
    CLUSCTL_RESOURCE_ENABLE_SHARED_VOLUME_DIRECTIO      = CLUSCTL_RESOURCE_CODE(CLCTL_ENABLE_SHARED_VOLUME_DIRECTIO),/*0x0140028a*/
    CLUSCTL_RESOURCE_DISABLE_SHARED_VOLUME_DIRECTIO     = CLUSCTL_RESOURCE_CODE(CLCTL_DISABLE_SHARED_VOLUME_DIRECTIO),/*0x0140028e*/
    CLUSCTL_RESOURCE_SET_SHARED_VOLUME_BACKUP_MODE      = CLUSCTL_RESOURCE_CODE(CLCTL_SET_SHARED_VOLUME_BACKUP_MODE),/*0x0140029a*/
    CLUSCTL_RESOURCE_DELETE = CLUSCTL_RESOURCE_CODE(CLCTL_DELETE),
    CLUSCTL_RESOURCE_INSTALL_NODE = CLUSCTL_RESOURCE_CODE(CLCTL_INSTALL_NODE),
    CLUSCTL_RESOURCE_EVICT_NODE = CLUSCTL_RESOURCE_CODE(CLCTL_EVICT_NODE),
    CLUSCTL_RESOURCE_ADD_DEPENDENCY = CLUSCTL_RESOURCE_CODE(CLCTL_ADD_DEPENDENCY),
    CLUSCTL_RESOURCE_REMOVE_DEPENDENCY = CLUSCTL_RESOURCE_CODE(CLCTL_REMOVE_DEPENDENCY),
    CLUSCTL_RESOURCE_ADD_OWNER = CLUSCTL_RESOURCE_CODE(CLCTL_ADD_OWNER),
    CLUSCTL_RESOURCE_REMOVE_OWNER = CLUSCTL_RESOURCE_CODE(CLCTL_REMOVE_OWNER),
    CLUSCTL_RESOURCE_SET_NAME = CLUSCTL_RESOURCE_CODE(CLCTL_SET_NAME),
    CLUSCTL_RESOURCE_CLUSTER_NAME_CHANGED = CLUSCTL_RESOURCE_CODE(CLCTL_CLUSTER_NAME_CHANGED),
    CLUSCTL_RESOURCE_CLUSTER_VERSION_CHANGED = CLUSCTL_RESOURCE_CODE(CLCTL_CLUSTER_VERSION_CHANGED),
    CLUSCTL_RESOURCE_FORCE_QUORUM = CLUSCTL_RESOURCE_CODE(CLCTL_FORCE_QUORUM),
    CLUSCTL_RESOURCE_INITIALIZE = CLUSCTL_RESOURCE_CODE(CLCTL_INITIALIZE),
    CLUSCTL_RESOURCE_STATE_CHANGE_REASON = CLUSCTL_RESOURCE_CODE(CLCTL_STATE_CHANGE_REASON),
    CLUSCTL_RESOURCE_PROVIDER_STATE_CHANGE = CLUSCTL_RESOURCE_CODE(CLCTL_PROVIDER_STATE_CHANGE),/*0x01500052*/
    CLUSCTL_RESOURCE_LEAVING_GROUP = CLUSCTL_RESOURCE_CODE(CLCTL_LEAVING_GROUP),/*0x01500056*/
    CLUSCTL_RESOURCE_JOINING_GROUP = CLUSCTL_RESOURCE_CODE(CLCTL_JOINING_GROUP),/*0x0150005a*/
    CLUSCTL_RESOURCE_FSWITNESS_GET_EPOCH_INFO = CLUSCTL_RESOURCE_CODE(CLCTL_FSWITNESS_GET_EPOCH_INFO),/*0x0110005d*/
    CLUSCTL_RESOURCE_FSWITNESS_SET_EPOCH_INFO = CLUSCTL_RESOURCE_CODE(CLCTL_FSWITNESS_SET_EPOCH_INFO),/*0x01500062*/
    CLUSCTL_RESOURCE_FSWITNESS_RELEASE_LOCK = CLUSCTL_RESOURCE_CODE(CLCTL_FSWITNESS_RELEASE_LOCK),/*0x01500066*/
    CLUSCTL_RESOURCE_NETNAME_CREDS_UPDATED = CLUSCTL_RESOURCE_CODE(CLCTL_NETNAME_CREDS_UPDATED)/*0x01c0018a */
  } CLUSCTL_RESOURCE_CODES;

  typedef enum CLUSCTL_RESOURCE_TYPE_CODES {
    CLUSCTL_RESOURCE_TYPE_UNKNOWN = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_UNKNOWN),
    CLUSCTL_RESOURCE_TYPE_GET_CHARACTERISTICS = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_CHARACTERISTICS),
    CLUSCTL_RESOURCE_TYPE_GET_FLAGS = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_FLAGS),
    CLUSCTL_RESOURCE_TYPE_GET_CLASS_INFO = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_CLASS_INFO),
    CLUSCTL_RESOURCE_TYPE_GET_REQUIRED_DEPENDENCIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_REQUIRED_DEPENDENCIES),
    CLUSCTL_RESOURCE_TYPE_GET_ARB_TIMEOUT = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_ARB_TIMEOUT),
    CLUSCTL_RESOURCE_TYPE_ENUM_COMMON_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_ENUM_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_GET_RO_COMMON_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_RO_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_GET_COMMON_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_VALIDATE_COMMON_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_VALIDATE_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_SET_COMMON_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_SET_COMMON_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_GET_COMMON_PROPERTY_FMTS = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_COMMON_PROPERTY_FMTS),
    CLUSCTL_RESOURCE_TYPE_GET_COMMON_RESOURCE_PROPERTY_FMTS = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_COMMON_RESOURCE_PROPERTY_FMTS),
    CLUSCTL_RESOURCE_TYPE_ENUM_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_ENUM_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_GET_RO_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_RO_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_GET_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_SET_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_SET_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_VALIDATE_PRIVATE_PROPERTIES = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_VALIDATE_PRIVATE_PROPERTIES),
    CLUSCTL_RESOURCE_TYPE_GET_PRIVATE_PROPERTY_FMTS = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_PRIVATE_PROPERTY_FMTS),
    CLUSCTL_RESOURCE_TYPE_GET_PRIVATE_RESOURCE_PROPERTY_FMTS = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_PRIVATE_RESOURCE_PROPERTY_FMTS),
    CLUSCTL_RESOURCE_TYPE_GET_REGISTRY_CHECKPOINTS = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_REGISTRY_CHECKPOINTS),
    CLUSCTL_RESOURCE_TYPE_GET_CRYPTO_CHECKPOINTS = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_GET_CRYPTO_CHECKPOINTS),
    CLUSCTL_RESOURCE_TYPE_STORAGE_GET_AVAILABLE_DISKS = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_STORAGE_GET_AVAILABLE_DISKS),
    CLUSCTL_RESOURCE_TYPE_QUERY_DELETE = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_QUERY_DELETE),
    CLUSCTL_RESOURCE_TYPE_INSTALL_NODE = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_INSTALL_NODE),
    CLUSCTL_RESOURCE_TYPE_EVICT_NODE = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_EVICT_NODE),
    CLUSCTL_RESOURCE_TYPE_CLUSTER_VERSION_CHANGED = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_CLUSTER_VERSION_CHANGED),
    CLUSCTL_RESOURCE_TYPE_FIXUP_ON_UPGRADE = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_FIXUP_ON_UPGRADE),
    CLUSCTL_RESOURCE_TYPE_STARTING_PHASE1 = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_STARTING_PHASE1),
    CLUSCTL_RESOURCE_TYPE_STARTING_PHASE2 = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_STARTING_PHASE2),
    CLUSCTL_RESOURCE_TYPE_HOLD_IO = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_HOLD_IO),
    CLUSCTL_RESOURCE_TYPE_RESUME_IO = CLUSCTL_RESOURCE_TYPE_CODE(CLCTL_RESUME_IO)
  } CLUSCTL_RESOURCE_TYPE_CODES;

  typedef enum CLUSPROP_IPADDR_ENABLENETBIOS {
    CLUSPROP_IPADDR_ENABLENETBIOS_DISABLED    = 0,  // 0x0
    CLUSPROP_IPADDR_ENABLENETBIOS_ENABLED,
    CLUSPROP_IPADDR_ENABLENETBIOS_TRACK_NIC 
  } CLUSPROP_IPADDR_ENABLENETBIOS;

  typedef enum CLUSCTL_GROUP_CODES {
    CLUSCTL_GROUP_UNKNOWN = CLUSCTL_GROUP_CODE(CLCTL_UNKNOWN),
    CLUSCTL_GROUP_GET_CHARACTERISTICS = CLUSCTL_GROUP_CODE(CLCTL_GET_CHARACTERISTICS),
    CLUSCTL_GROUP_GET_FLAGS = CLUSCTL_GROUP_CODE(CLCTL_GET_FLAGS),
    CLUSCTL_GROUP_GET_NAME = CLUSCTL_GROUP_CODE(CLCTL_GET_NAME),
    CLUSCTL_GROUP_GET_ID = CLUSCTL_GROUP_CODE(CLCTL_GET_ID),
    CLUSCTL_GROUP_ENUM_COMMON_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_ENUM_COMMON_PROPERTIES),
    CLUSCTL_GROUP_GET_RO_COMMON_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_GET_RO_COMMON_PROPERTIES),
    CLUSCTL_GROUP_GET_COMMON_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_GET_COMMON_PROPERTIES),
    CLUSCTL_GROUP_SET_COMMON_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_SET_COMMON_PROPERTIES),
    CLUSCTL_GROUP_VALIDATE_COMMON_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_VALIDATE_COMMON_PROPERTIES),
    CLUSCTL_GROUP_ENUM_PRIVATE_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_ENUM_PRIVATE_PROPERTIES),
    CLUSCTL_GROUP_GET_RO_PRIVATE_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_GET_RO_PRIVATE_PROPERTIES),
    CLUSCTL_GROUP_GET_PRIVATE_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_GET_PRIVATE_PROPERTIES),
    CLUSCTL_GROUP_SET_PRIVATE_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_SET_PRIVATE_PROPERTIES),
    CLUSCTL_GROUP_VALIDATE_PRIVATE_PROPERTIES = CLUSCTL_GROUP_CODE(CLCTL_VALIDATE_PRIVATE_PROPERTIES),
    CLUSCTL_GROUP_QUERY_DELETE = CLUSCTL_GROUP_CODE(CLCTL_QUERY_DELETE),
    CLUSCTL_GROUP_GET_COMMON_PROPERTY_FMTS = CLUSCTL_GROUP_CODE(CLCTL_GET_COMMON_PROPERTY_FMTS),
    CLUSCTL_GROUP_GET_PRIVATE_PROPERTY_FMTS = CLUSCTL_GROUP_CODE(CLCTL_GET_PRIVATE_PROPERTY_FMTS)
  } CLUSCTL_GROUP_CODES;

  typedef enum CLUSCTL_NODE_CODES {
    CLUSCTL_NODE_UNKNOWN = CLUSCTL_NODE_CODE(CLCTL_UNKNOWN),
    CLUSCTL_NODE_GET_CHARACTERISTICS = CLUSCTL_NODE_CODE(CLCTL_GET_CHARACTERISTICS),
    CLUSCTL_NODE_GET_FLAGS = CLUSCTL_NODE_CODE(CLCTL_GET_FLAGS),
    CLUSCTL_NODE_GET_NAME = CLUSCTL_NODE_CODE(CLCTL_GET_NAME),
    CLUSCTL_NODE_GET_ID = CLUSCTL_NODE_CODE(CLCTL_GET_ID),
    CLUSCTL_NODE_ENUM_COMMON_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_ENUM_COMMON_PROPERTIES),
    CLUSCTL_NODE_GET_RO_COMMON_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_GET_RO_COMMON_PROPERTIES),
    CLUSCTL_NODE_GET_COMMON_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_GET_COMMON_PROPERTIES),
    CLUSCTL_NODE_SET_COMMON_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_SET_COMMON_PROPERTIES),
    CLUSCTL_NODE_VALIDATE_COMMON_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_VALIDATE_COMMON_PROPERTIES),
    CLUSCTL_NODE_ENUM_PRIVATE_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_ENUM_PRIVATE_PROPERTIES),
    CLUSCTL_NODE_GET_RO_PRIVATE_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_GET_RO_PRIVATE_PROPERTIES),
    CLUSCTL_NODE_GET_PRIVATE_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_GET_PRIVATE_PROPERTIES),
    CLUSCTL_NODE_SET_PRIVATE_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_SET_PRIVATE_PROPERTIES),
    CLUSCTL_NODE_VALIDATE_PRIVATE_PROPERTIES = CLUSCTL_NODE_CODE(CLCTL_VALIDATE_PRIVATE_PROPERTIES),
    CLUSCTL_NODE_GET_COMMON_PROPERTY_FMTS = CLUSCTL_NODE_CODE(CLCTL_GET_COMMON_PROPERTY_FMTS),
    CLUSCTL_NODE_GET_PRIVATE_PROPERTY_FMTS = CLUSCTL_NODE_CODE(CLCTL_GET_PRIVATE_PROPERTY_FMTS),
    CLUSCTL_NODE_GET_CLUSTER_SERVICE_ACCOUNT_NAME = CLUSCTL_NODE_CODE(CLCTL_GET_CLUSTER_SERVICE_ACCOUNT_NAME)
  } CLUSCTL_NODE_CODES;

  typedef enum CLUSCTL_NETWORK_CODES {
    CLUSCTL_NETWORK_UNKNOWN = CLUSCTL_NETWORK_CODE(CLCTL_UNKNOWN),
    CLUSCTL_NETWORK_GET_CHARACTERISTICS = CLUSCTL_NETWORK_CODE(CLCTL_GET_CHARACTERISTICS),
    CLUSCTL_NETWORK_GET_FLAGS = CLUSCTL_NETWORK_CODE(CLCTL_GET_FLAGS),
    CLUSCTL_NETWORK_GET_NAME = CLUSCTL_NETWORK_CODE(CLCTL_GET_NAME),
    CLUSCTL_NETWORK_GET_ID = CLUSCTL_NETWORK_CODE(CLCTL_GET_ID),
    CLUSCTL_NETWORK_ENUM_COMMON_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_ENUM_COMMON_PROPERTIES),
    CLUSCTL_NETWORK_GET_RO_COMMON_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_GET_RO_COMMON_PROPERTIES),
    CLUSCTL_NETWORK_GET_COMMON_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_GET_COMMON_PROPERTIES),
    CLUSCTL_NETWORK_SET_COMMON_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_SET_COMMON_PROPERTIES),
    CLUSCTL_NETWORK_VALIDATE_COMMON_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_VALIDATE_COMMON_PROPERTIES),
    CLUSCTL_NETWORK_ENUM_PRIVATE_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_ENUM_PRIVATE_PROPERTIES),
    CLUSCTL_NETWORK_GET_RO_PRIVATE_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_GET_RO_PRIVATE_PROPERTIES),
    CLUSCTL_NETWORK_GET_PRIVATE_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_GET_PRIVATE_PROPERTIES),
    CLUSCTL_NETWORK_SET_PRIVATE_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_SET_PRIVATE_PROPERTIES),
    CLUSCTL_NETWORK_VALIDATE_PRIVATE_PROPERTIES = CLUSCTL_NETWORK_CODE(CLCTL_VALIDATE_PRIVATE_PROPERTIES),
    CLUSCTL_NETWORK_GET_COMMON_PROPERTY_FMTS = CLUSCTL_NETWORK_CODE(CLCTL_GET_COMMON_PROPERTY_FMTS),
    CLUSCTL_NETWORK_GET_PRIVATE_PROPERTY_FMTS = CLUSCTL_NETWORK_CODE(CLCTL_GET_PRIVATE_PROPERTY_FMTS)
  } CLUSCTL_NETWORK_CODES;

  typedef enum CLUSCTL_NETINTERFACE_CODES {
    CLUSCTL_NETINTERFACE_UNKNOWN = CLUSCTL_NETINTERFACE_CODE(CLCTL_UNKNOWN),
    CLUSCTL_NETINTERFACE_GET_CHARACTERISTICS = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_CHARACTERISTICS),
    CLUSCTL_NETINTERFACE_GET_FLAGS = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_FLAGS),
    CLUSCTL_NETINTERFACE_GET_NAME = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_NAME),
    CLUSCTL_NETINTERFACE_GET_ID = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_ID),
    CLUSCTL_NETINTERFACE_GET_NODE = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_NODE),
    CLUSCTL_NETINTERFACE_GET_NETWORK = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_NETWORK),
    CLUSCTL_NETINTERFACE_ENUM_COMMON_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_ENUM_COMMON_PROPERTIES),
    CLUSCTL_NETINTERFACE_GET_RO_COMMON_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_RO_COMMON_PROPERTIES),
    CLUSCTL_NETINTERFACE_GET_COMMON_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_COMMON_PROPERTIES),
    CLUSCTL_NETINTERFACE_SET_COMMON_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_SET_COMMON_PROPERTIES),
    CLUSCTL_NETINTERFACE_VALIDATE_COMMON_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_VALIDATE_COMMON_PROPERTIES),
    CLUSCTL_NETINTERFACE_ENUM_PRIVATE_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_ENUM_PRIVATE_PROPERTIES),
    CLUSCTL_NETINTERFACE_GET_RO_PRIVATE_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_RO_PRIVATE_PROPERTIES),
    CLUSCTL_NETINTERFACE_GET_PRIVATE_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_PRIVATE_PROPERTIES),
    CLUSCTL_NETINTERFACE_SET_PRIVATE_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_SET_PRIVATE_PROPERTIES),
    CLUSCTL_NETINTERFACE_VALIDATE_PRIVATE_PROPERTIES = CLUSCTL_NETINTERFACE_CODE(CLCTL_VALIDATE_PRIVATE_PROPERTIES),
    CLUSCTL_NETINTERFACE_GET_COMMON_PROPERTY_FMTS = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_COMMON_PROPERTY_FMTS),
    CLUSCTL_NETINTERFACE_GET_PRIVATE_PROPERTY_FMTS = CLUSCTL_NETINTERFACE_CODE(CLCTL_GET_PRIVATE_PROPERTY_FMTS)
  } CLUSCTL_NETINTERFACE_CODES;

  typedef enum CLUSCTL_CLUSTER_CODES {
    CLUSCTL_CLUSTER_UNKNOWN = CLUSCTL_CLUSTER_CODE(CLCTL_UNKNOWN),
    CLUSCTL_CLUSTER_GET_FQDN = CLUSCTL_CLUSTER_CODE(CLCTL_GET_FQDN),
    CLUSCTL_CLUSTER_ENUM_COMMON_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_ENUM_COMMON_PROPERTIES),
    CLUSCTL_CLUSTER_GET_RO_COMMON_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_GET_RO_COMMON_PROPERTIES),
    CLUSCTL_CLUSTER_GET_COMMON_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_GET_COMMON_PROPERTIES),
    CLUSCTL_CLUSTER_SET_COMMON_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_SET_COMMON_PROPERTIES),
    CLUSCTL_CLUSTER_VALIDATE_COMMON_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_VALIDATE_COMMON_PROPERTIES),
    CLUSCTL_CLUSTER_ENUM_PRIVATE_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_ENUM_PRIVATE_PROPERTIES),
    CLUSCTL_CLUSTER_GET_RO_PRIVATE_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_GET_RO_PRIVATE_PROPERTIES),
    CLUSCTL_CLUSTER_GET_PRIVATE_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_GET_PRIVATE_PROPERTIES),
    CLUSCTL_CLUSTER_SET_PRIVATE_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_SET_PRIVATE_PROPERTIES),
    CLUSCTL_CLUSTER_VALIDATE_PRIVATE_PROPERTIES = CLUSCTL_CLUSTER_CODE(CLCTL_VALIDATE_PRIVATE_PROPERTIES),
    CLUSCTL_CLUSTER_GET_COMMON_PROPERTY_FMTS = CLUSCTL_CLUSTER_CODE(CLCTL_GET_COMMON_PROPERTY_FMTS),
    CLUSCTL_CLUSTER_GET_PRIVATE_PROPERTY_FMTS = CLUSCTL_CLUSTER_CODE(CLCTL_GET_PRIVATE_PROPERTY_FMTS),
    CLUSCTL_CLUSTER_CHECK_VOTER_EVICT             = CLUSCTL_CLUSTER_CODE(CLCTL_CHECK_VOTER_EVICT),/*0x07000045*/
    CLUSCTL_CLUSTER_CHECK_VOTER_DOWN              = CLUSCTL_CLUSTER_CODE(CLCTL_CHECK_VOTER_DOWN),/*0x07000049*/
    CLUSCTL_CLUSTER_SHUTDOWN                      = CLUSCTL_CLUSTER_CODE(CLCTL_SHUTDOWN),/*0x0700004d*/
    CLUSCTL_CLUSTER_BATCH_BLOCK_KEY               = CLUSCTL_CLUSTER_CODE(CLCTL_BATCH_BLOCK_KEY),/*0x0700023e*/
    CLUSCTL_CLUSTER_BATCH_UNBLOCK_KEY             = CLUSCTL_CLUSTER_CODE(CLCTL_BATCH_UNBLOCK_KEY),/*0x07000241*/
    CLUSCTL_CLUSTER_GET_SHARED_VOLUME_ID          = CLUSCTL_CLUSTER_CODE(CLCTL_GET_SHARED_VOLUME_ID),/*0x07000291*/
  } CLUSCTL_CLUSTER_CODES;

  typedef enum CLUSTER_RESOURCE_CLASS {
    CLUS_RESCLASS_UNKNOWN = 0,CLUS_RESCLASS_STORAGE,CLUS_RESCLASS_USER = 32768
  } CLUSTER_RESOURCE_CLASS;

  typedef enum CLUS_RESSUBCLASS {
    CLUS_RESSUBCLASS_SHARED = 0x80000000
  } CLUS_RESSUBCLASS;

  typedef enum CLUS_CHARACTERISTICS {
    CLUS_CHAR_UNKNOWN = 0x00000000,CLUS_CHAR_QUORUM = 0x00000001,CLUS_CHAR_DELETE_REQUIRES_ALL_NODES = 0x00000002,CLUS_CHAR_LOCAL_QUORUM = 0x00000004,
    CLUS_CHAR_LOCAL_QUORUM_DEBUG = 0x00000008,CLUS_CHAR_REQUIRES_STATE_CHANGE_REASON = 0x00000010,
    CLUS_CHAR_BROADCAST_DELETE               = 0x00000020,
    CLUS_CHAR_SINGLE_CLUSTER_INSTANCE        = 0x00000040,
    CLUS_CHAR_SINGLE_GROUP_INSTANCE          = 0x00000080 
  } CLUS_CHARACTERISTICS;

  typedef enum CLUS_FLAGS {
    CLUS_FLAG_CORE = 0x00000001
  } CLUS_FLAGS;

  typedef enum CLUS_RESSUBCLASS_NETWORK {
    CLUS_RESSUBCLASS_NETWORK_INTERNET_PROTOCOL   = 0x80000000
  } CLUS_RESSUBCLASS_NETWORK;

  typedef enum CLUS_RESSUBCLASS_STORAGE {
    CLUS_RESSUBCLASS_STORAGE_SHARED_BUS   = 0x80000000
  } CLUS_RESSUBCLASS_STORAGE;

  typedef union CLUSPROP_SYNTAX {
    DWORD dw;
    __C89_NAMELESS struct {
      WORD wFormat;
      WORD wType;
    };
  } CLUSPROP_SYNTAX,*PCLUSPROP_SYNTAX;

  typedef struct CLUSPROP_VALUE {
    CLUSPROP_SYNTAX Syntax;
    DWORD cbLength;
  } CLUSPROP_VALUE,*PCLUSPROP_VALUE;

  typedef struct CLUSPROP_BINARY
#ifdef __cplusplus
    : public CLUSPROP_VALUE
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
#endif
    BYTE rgb[1];
  } CLUSPROP_BINARY,*PCLUSPROP_BINARY;

  typedef struct CLUSPROP_WORD
#ifdef __cplusplus
    : public CLUSPROP_VALUE
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
#endif
    WORD w;
  } CLUSPROP_WORD,*PCLUSPROP_WORD;

  typedef struct CLUSPROP_DWORD
#ifdef __cplusplus
    : public CLUSPROP_VALUE
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
#endif
    DWORD dw;
  } CLUSPROP_DWORD,*PCLUSPROP_DWORD;

  typedef struct CLUSPROP_LONG
#ifdef __cplusplus
    : public CLUSPROP_VALUE
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
#endif
    LONG l;
  } CLUSPROP_LONG,*PCLUSPROP_LONG;

  typedef struct CLUSPROP_SZ
#ifdef __cplusplus
    : public CLUSPROP_VALUE
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
#endif
    WCHAR sz[1];
  } CLUSPROP_SZ,*PCLUSPROP_SZ;

  typedef CLUSPROP_SZ CLUSPROP_MULTI_SZ,*PCLUSPROP_MULTI_SZ;
  typedef CLUSPROP_SZ CLUSPROP_PROPERTY_NAME,*PCLUSPROP_PROPERTY_NAME;

  typedef struct CLUSPROP_ULARGE_INTEGER
#ifdef __cplusplus
    : public CLUSPROP_VALUE
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
#endif
    ULARGE_INTEGER li;
  } CLUSPROP_ULARGE_INTEGER,*PCLUSPROP_ULARGE_INTEGER;

  typedef struct CLUSPROP_LARGE_INTEGER
#ifdef __cplusplus
    : public CLUSPROP_VALUE
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
#endif
    LARGE_INTEGER li;
  } CLUSPROP_LARGE_INTEGER,*PCLUSPROP_LARGE_INTEGER;

  typedef struct CLUSPROP_SECURITY_DESCRIPTOR
#ifdef __cplusplus
    : public CLUSPROP_VALUE
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
#endif
    __C89_NAMELESS union {
      SECURITY_DESCRIPTOR_RELATIVE sd;
      BYTE rgbSecurityDescriptor[1];
    };
  } CLUSPROP_SECURITY_DESCRIPTOR,*PCLUSPROP_SECURITY_DESCRIPTOR;

  typedef struct CLUS_RESOURCE_CLASS_INFO {
    __C89_NAMELESS union {
      __C89_NAMELESS struct {
	__C89_NAMELESS union {
	  DWORD dw;
	  CLUSTER_RESOURCE_CLASS rc;
	};
	DWORD SubClass;
      };
      ULARGE_INTEGER li;
    };
  } CLUS_RESOURCE_CLASS_INFO,*PCLUS_RESOURCE_CLASS_INFO;

  typedef struct CLUSPROP_RESOURCE_CLASS
#ifdef __cplusplus
    : public CLUSPROP_VALUE
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
#endif
    CLUSTER_RESOURCE_CLASS rc;
  } CLUSPROP_RESOURCE_CLASS,*PCLUSPROP_RESOURCE_CLASS;

  typedef struct CLUSPROP_RESOURCE_CLASS_INFO
#ifdef __cplusplus
    : public CLUSPROP_VALUE,public CLUS_RESOURCE_CLASS_INFO
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
    CLUS_RESOURCE_CLASS_INFO;
#endif
  } CLUSPROP_RESOURCE_CLASS_INFO,*PCLUSPROP_RESOURCE_CLASS_INFO;

  typedef union CLUSPROP_REQUIRED_DEPENDENCY {
    CLUSPROP_VALUE Value;
    CLUSPROP_RESOURCE_CLASS ResClass;
    CLUSPROP_SZ ResTypeName;
  } CLUSPROP_REQUIRED_DEPENDENCY,*PCLUSPROP_REQUIRED_DEPENDENCY;

  typedef CLUSPROP_DWORD CLUSPROP_DISK_NUMBER,*PCLUSPROP_DISK_NUMBER;
#endif

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSPROP_PIFLAGS {
    CLUSPROP_PIFLAG_STICKY = 0x00000001,CLUSPROP_PIFLAG_REMOVABLE = 0x00000002,CLUSPROP_PIFLAG_USABLE = 0x00000004,
    CLUSPROP_PIFLAG_DEFAULT_QUORUM = 0x00000008
  } CLUSPROP_PIFLAGS;

  typedef struct CLUS_FORCE_QUORUM_INFO {
    DWORD dwSize;
    DWORD dwNodeBitMask;
    DWORD dwMaxNumberofNodes;
    WCHAR multiszNodeList[1];
  } CLUS_FORCE_QUORUM_INFO,*PCLUS_FORCE_QUORUM_INFO;

  typedef struct CLUS_PARTITION_INFO {
    DWORD dwFlags;
    WCHAR szDeviceName[MAX_PATH];
    WCHAR szVolumeLabel[MAX_PATH];
    DWORD dwSerialNumber;
    DWORD rgdwMaximumComponentLength;
    DWORD dwFileSystemFlags;
    WCHAR szFileSystem[32];
  } CLUS_PARTITION_INFO,*PCLUS_PARTITION_INFO;

  typedef struct CLUSPROP_PARTITION_INFO
#ifdef __cplusplus
    : public CLUSPROP_VALUE,public CLUS_PARTITION_INFO
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
    CLUS_PARTITION_INFO;
#endif
  } CLUSPROP_PARTITION_INFO,*PCLUSPROP_PARTITION_INFO;

  typedef struct CLUS_FTSET_INFO {
    DWORD dwRootSignature;
    DWORD dwFtType;
  } CLUS_FTSET_INFO,*PCLUS_FTSET_INFO;

  typedef struct CLUSPROP_FTSET_INFO
#ifdef __cplusplus
    : public CLUSPROP_VALUE,public CLUS_FTSET_INFO
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
    CLUS_FTSET_INFO;
#endif
  } CLUSPROP_FTSET_INFO,*PCLUSPROP_FTSET_INFO;

  typedef CLUSPROP_DWORD CLUSPROP_DISK_SIGNATURE,*PCLUSPROP_DISK_SIGNATURE;
  typedef struct CLUS_SCSI_ADDRESS {
    __C89_NAMELESS union {
      __C89_NAMELESS struct {
	UCHAR PortNumber;
	UCHAR PathId;
	UCHAR TargetId;
	UCHAR Lun;
      };
      DWORD dw;
    };
  } CLUS_SCSI_ADDRESS,*PCLUS_SCSI_ADDRESS;

  typedef struct CLUSPROP_SCSI_ADDRESS
#ifdef __cplusplus
    : public CLUSPROP_VALUE,public CLUS_SCSI_ADDRESS
#endif
  {
#ifndef __cplusplus
    CLUSPROP_VALUE;
    CLUS_SCSI_ADDRESS;
#endif
  } CLUSPROP_SCSI_ADDRESS,*PCLUSPROP_SCSI_ADDRESS;

  typedef struct CLUS_NETNAME_VS_TOKEN_INFO {
    DWORD ProcessID;
    DWORD DesiredAccess;
    WINBOOL InheritHandle;
  } CLUS_NETNAME_VS_TOKEN_INFO,*PCLUS_NETNAME_VS_TOKEN_INFO;

  typedef struct CLUS_MAINTENANCE_MODE_INFO {
    WINBOOL InMaintenance;
  } CLUS_MAINTENANCE_MODE_INFO,*PCLUS_MAINTENANCE_MODE_INFO;

  typedef struct CLUSPROP_LIST {
    DWORD nPropertyCount;
    CLUSPROP_PROPERTY_NAME PropertyName;
  } CLUSPROP_LIST,*PCLUSPROP_LIST;

  typedef union CLUSPROP_BUFFER_HELPER {
    BYTE *pb;
    WORD *pw;
    DWORD *pdw;
    LONG *pl;
    LPWSTR psz;
    PCLUSPROP_LIST pList;
    PCLUSPROP_SYNTAX pSyntax;
    PCLUSPROP_PROPERTY_NAME pName;
    PCLUSPROP_VALUE pValue;
    PCLUSPROP_BINARY pBinaryValue;
    PCLUSPROP_WORD pWordValue;
    PCLUSPROP_DWORD pDwordValue;
    PCLUSPROP_LONG pLongValue;
    PCLUSPROP_ULARGE_INTEGER pULargeIntegerValue;
    PCLUSPROP_LARGE_INTEGER pLargeIntegerValue;
    PCLUSPROP_SZ pStringValue;
    PCLUSPROP_MULTI_SZ pMultiSzValue;
    PCLUSPROP_SECURITY_DESCRIPTOR pSecurityDescriptor;
    PCLUSPROP_RESOURCE_CLASS pResourceClassValue;
    PCLUSPROP_RESOURCE_CLASS_INFO pResourceClassInfoValue;
    PCLUSPROP_DISK_SIGNATURE pDiskSignatureValue;
    PCLUSPROP_SCSI_ADDRESS pScsiAddressValue;
    PCLUSPROP_DISK_NUMBER pDiskNumberValue;
    PCLUSPROP_PARTITION_INFO pPartitionInfoValue;
    PCLUSPROP_REQUIRED_DEPENDENCY pRequiredDependencyValue;
  } CLUSPROP_BUFFER_HELPER,*PCLUSPROP_BUFFER_HELPER;
#endif

#define ALIGN_CLUSPROP(count) ((count + 3) & ~3)
#define CLUSPROP_BINARY_DECLARE(name,cb) struct { CLUSPROP_SYNTAX Syntax; DWORD cbLength; BYTE rgb[(cb + 3) & ~3]; } name
#define CLUSPROP_SZ_DECLARE(name,cch) struct { CLUSPROP_SYNTAX Syntax; DWORD cbLength; WCHAR sz[(cch + 1) & ~1]; } name
#define CLUSPROP_PROPERTY_NAME_DECLARE(name,cch) CLUSPROP_SZ_DECLARE(name,cch)

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_RESOURCE_ENUM {
    CLUSTER_RESOURCE_ENUM_DEPENDS = 0x00000001,CLUSTER_RESOURCE_ENUM_PROVIDES = 0x00000002,CLUSTER_RESOURCE_ENUM_NODES = 0x00000004,
    CLUSTER_RESOURCE_ENUM_ALL = (CLUSTER_RESOURCE_ENUM_DEPENDS | CLUSTER_RESOURCE_ENUM_PROVIDES | CLUSTER_RESOURCE_ENUM_NODES)
  } CLUSTER_RESOURCE_ENUM;

  typedef enum CLUSTER_RESOURCE_TYPE_ENUM {
    CLUSTER_RESOURCE_TYPE_ENUM_NODES = 0x00000001,
    CLUSTER_RESOURCE_TYPE_ENUM_RESOURCES = 0x00000002,
    CLUSTER_RESOURCE_TYPE_ENUM_ALL = (CLUSTER_RESOURCE_TYPE_ENUM_NODES | CLUSTER_RESOURCE_TYPE_ENUM_RESOURCES)
  } CLUSTER_RESOURCE_TYPE_ENUM;
#endif

  HRESENUM WINAPI ClusterResourceOpenEnum(HRESOURCE hResource,DWORD dwType);
  DWORD WINAPI ClusterResourceGetEnumCount(HRESENUM hResEnum);
  DWORD WINAPI ClusterResourceEnum(HRESENUM hResEnum,DWORD dwIndex,LPDWORD lpdwType,LPWSTR lpszName,LPDWORD lpcchName);
  DWORD WINAPI ClusterResourceCloseEnum(HRESENUM hResEnum);
  DWORD WINAPI CreateClusterResourceType(HCLUSTER hCluster,LPCWSTR lpszResourceTypeName,LPCWSTR lpszDisplayName,LPCWSTR lpszResourceTypeDll,DWORD dwLooksAlivePollInterval,DWORD dwIsAlivePollInterval);
  DWORD WINAPI DeleteClusterResourceType(HCLUSTER hCluster,LPCWSTR lpszResourceTypeName);
  HRESTYPEENUM WINAPI ClusterResourceTypeOpenEnum(HCLUSTER hCluster,LPCWSTR lpszResourceTypeName,DWORD dwType);
  DWORD WINAPI ClusterResourceTypeGetEnumCount(HRESTYPEENUM hResTypeEnum);
  DWORD WINAPI ClusterResourceTypeEnum(HRESTYPEENUM hResTypeEnum,DWORD dwIndex,LPDWORD lpdwType,LPWSTR lpszName,LPDWORD lpcchName);
  DWORD WINAPI ClusterResourceTypeCloseEnum(HRESTYPEENUM hResTypeEnum);

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_NETWORK_ENUM {
    CLUSTER_NETWORK_ENUM_NETINTERFACES = 0x00000001,CLUSTER_NETWORK_ENUM_ALL = CLUSTER_NETWORK_ENUM_NETINTERFACES
  } CLUSTER_NETWORK_ENUM;

  typedef enum CLUSTER_NETWORK_STATE {
    ClusterNetworkStateUnknown = -1,ClusterNetworkUnavailable,ClusterNetworkDown,ClusterNetworkPartitioned,ClusterNetworkUp
  } CLUSTER_NETWORK_STATE;

  typedef enum CLUSTER_NETWORK_ROLE {
    ClusterNetworkRoleNone = 0,ClusterNetworkRoleInternalUse = 0x00000001,ClusterNetworkRoleClientAccess = 0x00000002,
    ClusterNetworkRoleInternalAndClient = 0x00000003
  } CLUSTER_NETWORK_ROLE;
#endif

  HNETWORK WINAPI OpenClusterNetwork(HCLUSTER hCluster,LPCWSTR lpszNetworkName);
  WINBOOL WINAPI CloseClusterNetwork(HNETWORK hNetwork);
  HCLUSTER WINAPI GetClusterFromNetwork(HNETWORK hNetwork);
  HNETWORKENUM WINAPI ClusterNetworkOpenEnum(HNETWORK hNetwork,DWORD dwType);
  DWORD WINAPI ClusterNetworkGetEnumCount(HNETWORKENUM hNetworkEnum);
  DWORD WINAPI ClusterNetworkEnum(HNETWORKENUM hNetworkEnum,DWORD dwIndex,LPDWORD lpdwType,LPWSTR lpszName,LPDWORD lpcchName);
  DWORD WINAPI ClusterNetworkCloseEnum(HNETWORKENUM hNetworkEnum);
  CLUSTER_NETWORK_STATE WINAPI GetClusterNetworkState(HNETWORK hNetwork);
  DWORD WINAPI SetClusterNetworkName(HNETWORK hNetwork,LPCWSTR lpszName);
  DWORD WINAPI GetClusterNetworkId(HNETWORK hNetwork,LPWSTR lpszNetworkId,LPDWORD lpcchName);
  DWORD WINAPI ClusterNetworkControl(HNETWORK hNetwork,HNODE hHostNode,DWORD dwControlCode,LPVOID lpInBuffer,DWORD nInBufferSize,LPVOID lpOutBuffer,DWORD nOutBufferSize,LPDWORD lpBytesReturned);

#ifndef _CLUSTER_API_TYPES_
  typedef enum CLUSTER_NETINTERFACE_STATE {
    ClusterNetInterfaceStateUnknown = -1,ClusterNetInterfaceUnavailable,ClusterNetInterfaceFailed,ClusterNetInterfaceUnreachable,ClusterNetInterfaceUp
  } CLUSTER_NETINTERFACE_STATE;
#endif

  HNETINTERFACE WINAPI OpenClusterNetInterface(HCLUSTER hCluster,LPCWSTR lpszInterfaceName);
  DWORD WINAPI GetClusterNetInterface(HCLUSTER hCluster,LPCWSTR lpszNodeName,LPCWSTR lpszNetworkName,LPWSTR lpszInterfaceName,LPDWORD lpcchInterfaceName);
  WINBOOL WINAPI CloseClusterNetInterface(HNETINTERFACE hNetInterface);
  HCLUSTER WINAPI GetClusterFromNetInterface(HNETINTERFACE hNetInterface);
  CLUSTER_NETINTERFACE_STATE WINAPI GetClusterNetInterfaceState(HNETINTERFACE hNetInterface);
  DWORD WINAPI ClusterNetInterfaceControl(HNETINTERFACE hNetInterface,HNODE hHostNode,DWORD dwControlCode,LPVOID lpInBuffer,DWORD nInBufferSize,LPVOID lpOutBuffer,DWORD nOutBufferSize,LPDWORD lpBytesReturned);
  HKEY WINAPI GetClusterKey(HCLUSTER hCluster,REGSAM samDesired);
  HKEY WINAPI GetClusterGroupKey(HGROUP hGroup,REGSAM samDesired);
  HKEY WINAPI GetClusterResourceKey(HRESOURCE hResource,REGSAM samDesired);
  HKEY WINAPI GetClusterNodeKey(HNODE hNode,REGSAM samDesired);
  HKEY WINAPI GetClusterNetworkKey(HNETWORK hNetwork,REGSAM samDesired);
  HKEY WINAPI GetClusterNetInterfaceKey(HNETINTERFACE hNetInterface,REGSAM samDesired);
  LONG WINAPI ClusterRegCreateKey(HKEY hKey,LPCWSTR lpszSubKey,DWORD dwOptions,REGSAM samDesired,LPSECURITY_ATTRIBUTES lpSecurityAttributes,PHKEY phkResult,LPDWORD lpdwDisposition);
  LONG WINAPI ClusterRegOpenKey(HKEY hKey,LPCWSTR lpszSubKey,REGSAM samDesired,PHKEY phkResult);
  LONG WINAPI ClusterRegDeleteKey(HKEY hKey,LPCWSTR lpszSubKey);
  LONG WINAPI ClusterRegCloseKey(HKEY hKey);
  LONG WINAPI ClusterRegEnumKey(HKEY hKey,DWORD dwIndex,LPWSTR lpszName,LPDWORD lpcchName,PFILETIME lpftLastWriteTime);
  DWORD WINAPI ClusterRegSetValue(HKEY hKey,LPCWSTR lpszValueName,DWORD dwType,CONST BYTE *lpData,DWORD cbData);
  DWORD WINAPI ClusterRegDeleteValue(HKEY hKey,LPCWSTR lpszValueName);
  LONG WINAPI ClusterRegQueryValue(HKEY hKey,LPCWSTR lpszValueName,LPDWORD lpdwValueType,LPBYTE lpData,LPDWORD lpcbData);
  DWORD WINAPI ClusterRegEnumValue(HKEY hKey,DWORD dwIndex,LPWSTR lpszValueName,LPDWORD lpcchValueName,LPDWORD lpdwType,LPBYTE lpData,LPDWORD lpcbData);
  LONG WINAPI ClusterRegQueryInfoKey(HKEY hKey,LPDWORD lpcSubKeys,LPDWORD lpcchMaxSubKeyLen,LPDWORD lpcValues,LPDWORD lpcchMaxValueNameLen,LPDWORD lpcbMaxValueLen,LPDWORD lpcbSecurityDescriptor,PFILETIME lpftLastWriteTime);
  LONG WINAPI ClusterRegGetKeySecurity (HKEY hKey,SECURITY_INFORMATION RequestedInformation,PSECURITY_DESCRIPTOR pSecurityDescriptor,LPDWORD lpcbSecurityDescriptor);
  LONG WINAPI ClusterRegSetKeySecurity(HKEY hKey,SECURITY_INFORMATION SecurityInformation,PSECURITY_DESCRIPTOR pSecurityDescriptor);

typedef HCLUSTER (WINAPI *PCLUSAPI_OPEN_CLUSTER)( 
  LPCWSTR lpszClusterName
);

#if (_WIN32_WINNT >= 0x0600)
#define FS_CASE_SENSITIVE 1
#define FS_CASE_IS_PRESERVED 2
#define FS_UNICODE_STORED_ON_DISK 3
#define FS_PERSISTENT_ACLS 4

typedef enum _MAINTENANCE_MODE_TYPE_ENUM {
  MaintenanceModeTypeDisableIsAliveCheck   = 1,
  MaintenanceModeTypeOfflineResource       = 2,
  MaintenanceModeTypeUnclusterResource     = 3 
} MAINTENANCE_MODE_TYPE_ENUM, *PMAINTENANCE_MODE_TYPE_ENUM;

typedef enum CLUSTER_RESOURCE_STATE {
  ClusterResourceStateUnknown     = -1,
  ClusterResourceInherited        = 0,
  ClusterResourceInitializing     = 1,
  ClusterResourceOnline           = 2,
  ClusterResourceOffline          = 3,
  ClusterResourceFailed           = 4,
  ClusterResourcePending          = 128,  // 0x80
  ClusterResourceOnlinePending    = 129,  // 0x81
  ClusterResourceOfflinePending   = 130   // 0x82
} CLUSTER_RESOURCE_STATE;

typedef enum _CLUSTER_REG_COMMAND {
  CLUSREG_COMMAND_NONE       = 0,
  CLUSREG_SET_VALUE          = 1,
  CLUSREG_CREATE_KEY         = 2,
  CLUSREG_DELETE_KEY         = 3,
  CLUSREG_DELETE_VALUE       = 4,
  CLUSREG_SET_KEY_SECURITY   = 5,
  CLUSREG_VALUE_DELETED      = 6,
  CLUSREG_LAST_COMMAND       = 7 
} CLUSTER_REG_COMMAND;

typedef enum CLUSTER_GROUP_STATE {
  ClusterGroupStateUnknown    = -1,
  ClusterGroupOnline          = 0,
  ClusterGroupOffline         = 1,
  ClusterGroupFailed          = 2,
  ClusterGroupPartialOnline   = 3,
  ClusterGroupPending         = 4 
} CLUSTER_GROUP_STATE;

typedef enum CLUSTER_QUORUM_TYPE {
  OperationalQuorum   = 0,
  ModifyQuorum        = 1 
} CLUSTER_QUORUM_TYPE;

typedef enum CLUSTER_QUORUM_VALUE {
  CLUSTER_QUORUM_MAINTAINED   = 0,
  CLUSTER_QUORUM_LOST         = 1 
} CLUSTER_QUORUM_VALUE;

typedef enum CLUSTER_RESOURCE_CLASS {
  CLUS_RESCLASS_UNKNOWN   = 0,
  CLUS_RESCLASS_STORAGE   = 1,
  CLUS_RESCLASS_NETWORK   = 2,
  CLUS_RESCLASS_USER      = 32768 
} CLUSTER_RESOURCE_CLASS;

typedef enum CLUSTER_RESOURCE_CREATE_FLAGS {
  CLUSTER_RESOURCE_DEFAULT_MONITOR    = 0,
  CLUSTER_RESOURCE_SEPARATE_MONITOR   = 1,
  CLUSTER_RESOURCE_VALID_FLAGS        = 1 
} CLUSTER_RESOURCE_CREATE_FLAGS;

typedef enum _CLUSTER_SETUP_PHASE {
  ClusterSetupPhaseInitialize                   = 1,
  ClusterSetupPhaseValidateNodeState            = 100,
  ClusterSetupPhaseValidateNetft                = 102,
  ClusterSetupPhaseValidateClusDisk             = 103,
  ClusterSetupPhaseConfigureClusSvc             = 104,
  ClusterSetupPhaseStartingClusSvc              = 105,
  ClusterSetupPhaseQueryClusterNameAccount      = 106,
  ClusterSetupPhaseValidateClusterNameAccount   = 107,
  ClusterSetupPhaseCreateClusterAccount         = 108,
  ClusterSetupPhaseConfigureClusterAccount      = 109,
  ClusterSetupPhaseFormingCluster               = 200,
  ClusterSetupPhaseAddClusterProperties         = 201,
  ClusterSetupPhaseCreateResourceTypes          = 202,
  ClusterSetupPhaseCreateGroups                 = 203,
  ClusterSetupPhaseCreateIPAddressResources     = 204,
  ClusterSetupPhaseCreateNetworkName            = 205,
  ClusterSetupPhaseClusterGroupOnline           = 206,
  ClusterSetupPhaseGettingCurrentMembership     = 300,
  ClusterSetupPhaseAddNodeToCluster             = 301,
  ClusterSetupPhaseNodeUp                       = 302,
  ClusterSetupPhaseMoveGroup                    = 400,
  ClusterSetupPhaseDeleteGroup                  = 401,
  ClusterSetupPhaseCleanupCOs                   = 402,
  ClusterSetupPhaseOfflineGroup                 = 403,
  ClusterSetupPhaseEvictNode                    = 404,
  ClusterSetupPhaseCleanupNode                  = 405,
  ClusterSetupPhaseCoreGroupCleanup             = 406,
  ClusterSetupPhaseFailureCleanup               = 999 
} CLUSTER_SETUP_PHASE;

typedef enum _CLUSTER_SETUP_PHASE_TYPE {
  ClusterSetupPhaseStart      = 1,
  ClusterSetupPhaseContinue   = 2,
  ClusterSetupPhaseEnd        = 3 
} CLUSTER_SETUP_PHASE_TYPE;

typedef enum _CLUSTER_SETUP_PHASE_SEVERITY {
  ClusterSetupPhaseInformational   = 1,
  ClusterSetupPhaseWarning         = 2,
  ClusterSetupPhaseFatal           = 3 
} CLUSTER_SETUP_PHASE_SEVERITY;

typedef struct _CLUSPROP_FILETIME {
  CLUSPROP_SYNTAX Syntax;
  DWORD           cbLength;
  FILETIME        ft;
} CLUSPROP_FILETIME, *PCLUSPROP_FILETIME;

typedef struct _CLUS_MAINTENANCE_MODE_INFOEX {
  WINBOOL                    InMaintenance;
  MAINTENANCE_MODE_TYPE_ENUM MaintainenceModeType;
  CLUSTER_RESOURCE_STATE     InternalState;
  DWORD                      Signature;
} CLUS_MAINTENANCE_MODE_INFOEX, *PCLUS_MAINTENANCE_MODE_INFOEX;

typedef struct CLUS_NETNAME_PWD_INFO {
  DWORD Flags;
  WCHAR Password[MAX_CO_PASSWORD_LENGTH];
  WCHAR CreatingDC[MAX_CREATINGDC_LENGTH+2];
  WCHAR ObjectGuid[MAX_OBJECTID];
} CLUS_NETNAME_PWD_INFO, *PCLUS_NETNAME_PWD_INFO;

typedef struct CLUS_NETNAME_VS_TOKEN_INFO {
  DWORD   ProcessID;
  DWORD   DesiredAccess;
  WINBOOL InheritHandle;
} CLUS_NETNAME_VS_TOKEN_INFO, *PCLUS_NETNAME_VS_TOKEN_INFO;

typedef struct CLUS_PARTITION_INFO_EX {
  DWORD          dwFlags;
  WCHAR          szDeviceName[MAX_PATH];
  WCHAR          szVolumeLabel[MAX_PATH];
  DWORD          dwSerialNumber;
  DWORD          rgdwMaximumComponentLength;
  DWORD          dwFileSystemFlags;
  WCHAR          szFileSystem[32];
  ULARGE_INTEGER TotalSizeInBytes;
  ULARGE_INTEGER FreeSizeInBytes;
  DWORD          DeviceNumber;
  DWORD          PartitionNumber;
  GUID           VolumeGuid;
} CLUS_PARTITION_INFO_EX, *PCLUS_PARTITION_INFO_EX;

typedef struct _CLUS_PROVIDER_STATE_CHANGE_INFO {
  DWORD                  dwSize;
  CLUSTER_RESOURCE_STATE resourceState;
  WCHAR                  szProviderId[1];
} CLUS_PROVIDER_STATE_CHANGE_INFO, *PCLUS_PROVIDER_STATE_CHANGE_INFO;

typedef struct _CLUS_STORAGE_GET_AVAILABLE_DRIVELETTERS {
  DWORD AvailDrivelettersMask;
} CLUS_STORAGE_GET_AVAILABLE_DRIVELETTERS, *PCLUS_STORAGE_GET_AVAILABLE_DRIVELETTERS;

typedef struct _CLUS_STORAGE_REMAP_DRIVELETTER {
  DWORD CurrentDriveLetterMask;
  DWORD TargetDriveLetterMask;
} CLUS_STORAGE_REMAP_DRIVELETTER, *PCLUS_STORAGE_REMAP_DRIVELETTER;

typedef struct _CLUS_STORAGE_SET_DRIVELETTER {
  DWORD PartitionNumber;
  DWORD DriveLetterMask;
} CLUS_STORAGE_SET_DRIVELETTER, *PCLUS_STORAGE_SET_DRIVELETTER;

typedef struct _CLUSPROP_PARTITION_INFO_EX {
  CLUSPROP_SYNTAX Syntax;
  DWORD           cbLength;
  DWORD           dwFlags;
  WCHAR           szDeviceName[MAX_PATH];
  WCHAR           szVolumeLabel[MAX_PATH];
  DWORD           dwSerialNumber;
  DWORD           rgdwMaximumComponentLength;
  DWORD           dwFileSystemFlags;
  WCHAR           szFileSystem[32];
  ULARGE_INTEGER  TotalSizeInBytes;
  ULARGE_INTEGER  FreeSizeInBytes;
  DWORD           DeviceNumber;
  DWORD           PartitionNumber;
  GUID            VolumeGuid;
} CLUSPROP_PARTITION_INFO_EX, *PCLUSPROP_PARTITION_INFO_EX;

typedef struct _CLUSTER_BATCH_COMMAND {
  CLUSTER_REG_COMMAND Command;
  DWORD               dwOptions;
  LPCWSTR             wzName;
  BYTE CONST *        lpData;
  DWORD               cbData;
} CLUSTER_BATCH_COMMAND;

typedef struct _CLUSTER_IP_ENTRY {
  PCWSTR lpszIpAddress;
  DWORD  dwPrefixLength;
} CLUSTER_IP_ENTRY, *PCLUSTER_IP_ENTRY;

typedef struct _CREATE_CLUSTER_CONFIG {
  DWORD             dwVersion;
  PCWSTR            lpszClusterName;
  DWORD             cNodes;
  PCWSTR            *ppszNodeNames;
  DWORD             cIpEntries;
  PCLUSTER_IP_ENTRY pIpEntries;
  BOOLEAN           fEmptyCluster;
} CREATE_CLUSTER_CONFIG, *PCREATE_CLUSTER_CONFIG;

typedef struct _CLUSTER_VALIDATE_DIRECTORY {
  __MINGW_EXTENSION WCHAR szPath[0];
} CLUSTER_VALIDATE_DIRECTORY, *PCLUSTER_VALIDATE_DIRECTORY;

typedef struct _CLUSTER_VALIDATE_NETNAME {
  __MINGW_EXTENSION WCHAR szNetworkName[0];
} CLUSTER_VALIDATE_NETNAME, *PCLUSTER_VALIDATE_NETNAME;

typedef struct _CLUSTER_VALIDATE_PATH {
  __MINGW_EXTENSION WCHAR szPath[0];
} CLUSTER_VALIDATE_PATH, *PCLUSTER_VALIDATE_PATH;

typedef LPVOID HREGBATCH;
typedef LPVOID HREGBATCHPORT;
typedef LPVOID HREGBATCHNOTIFICATION;

LONG ClusterRegBatchAddCommand(
  HREGBATCH hRegBatch,
  CLUSTER_REG_COMMAND dwCommand,
  LPCWSTR wzName,
  DWORD dwOptions,
  VOID CONST *lpData,
  DWORD cbData
);

LONG WINAPI ClusterRegBatchCloseNotification(
  HREGBATCHNOTIFICATION hBatchNotification
);

LONG WINAPI ClusterRegBatchReadCommand(
  HREGBATCHNOTIFICATION hBatchNotification,
  CLUSTER_BATCH_COMMAND *pBatchCommand
);

LONG WINAPI ClusterRegCloseBatch(
  HREGBATCH hRegBatch,
  WINBOOL bCommit,
  INT *failedCommandNumber
);

LONG WINAPI ClusterRegCloseBatchNotifyPort(
  HREGBATCHPORT hBatchNotifyPort
);

typedef LONG (WINAPI *PCLUSTER_REG_CREATE_BATCH)(
  HKEY hKey,
  HREGBATCH *pHREGBATCH
);

LONG WINAPI ClusterRegCreateBatch(
  HKEY hKey,
  HREGBATCH *pHREGBATCH
);

typedef LONG (WINAPI *PCLUSTER_REG_CREATE_BATCH_NOTIFY_PORT)(
  HKEY hKey,
  HREGBATCHPORT *phBatchNotifyPort
);

LONG WINAPI ClusterRegCreateBatchNotifyPort(
  HKEY hKey,
  HREGBATCHPORT *phBatchNotifyPort
);

typedef LONG (WINAPI *PCLUSTER_REG_GET_BATCH_NOTIFICATION)(
  HREGBATCHPORT hBatchNotify,
  HREGBATCHNOTIFICATION *phBatchNotification
);

LONG WINAPI ClusterRegGetBatchNotification(
  HREGBATCHPORT hBatchNotify,
  HREGBATCHNOTIFICATION *phBatchNotification
);

typedef WINBOOL (WINAPI *PCLUSTER_SETUP_PROGRESS_CALLBACK)( 
  PVOID pvCallbackArg,
  CLUSTER_SETUP_PHASE eSetupPhase,
  CLUSTER_SETUP_PHASE_TYPE ePhaseType,
  CLUSTER_SETUP_PHASE_SEVERITY ePhaseSeverity,
  DWORD dwPercentComplete,
  PCWSTR lpszObjectName,
  DWORD dwStatus
);

HNODE WINAPI AddClusterNode(
  HCLUSTER hCluster,
  PCWSTR lpszNodeName,
  PCLUSTER_SETUP_PROGRESS_CALLBACK pfnProgressCallback,
  PVOID pvCallbackArg
);

DWORD WINAPI DestroyCluster(
  HCLUSTER hCluster,
  PCLUSTER_SETUP_PROGRESS_CALLBACK pfnProgressCallback,
  PVOID pvCallbackArg,
  WINBOOL fdeleteVirtualComputerObjects
);

HCLUSTER WINAPI CreateCluster(
  PCREATE_CLUSTER_CONFIG pConfig,
  PCLUSTER_SETUP_PROGRESS_CALLBACK pfnProgressCallback,
  PVOID pvCallbackArg
);

DWORD DestroyClusterGroup(
  HGROUP hGroup
);

typedef enum _FILESHARE_CHANGE_ENUM {
  FILESHARE_CHANGE_NONE     = 0,
  FILESHARE_CHANGE_ADD      = 1,
  FILESHARE_CHANGE_DEL      = 2,
  FILESHARE_CHANGE_MODIFY   = 3 
} FILESHARE_CHANGE_ENUM;

#define NNLEN       80                  /* Net name length (share name) */

typedef struct _FILESHARE_CHANGE {
  FILESHARE_CHANGE_ENUM Change;
  WCHAR                 ShareName[NNLEN+4];
} FILESHARE_CHANGE, *PFILESHARE_CHANGE;

typedef struct _FILESHARE_CHANGE_LIST {
  DWORD            NumEntries;
  __MINGW_EXTENSION FILESHARE_CHANGE ChangeEntry[0];
} FILESHARE_CHANGE_LIST, *PFILESHARE_CHANGE_LIST;

DWORD WINAPI GetClusterResourceDependencyExpression(
  HRESOURCE hResource,
  LPWSTR lpszDependencyExpression,
  LPDWORD lpcchDependencyExpression
);

DWORD WINAPI SetClusterResourceDependencyExpression(
  HRESOURCE hResource,
  LPCWSTR lpszDependencyExpression
);

#endif /* (_WIN32_WINNT >= 0x0600) */
#if (_WIN32_WINNT >= 0x0601)
typedef enum _CLUSTER_SHARED_VOLUME_BACKUP_STATE {
  VolumeBackupNone         = 0x00000000,
  VolumeBackupInProgress   = 0x00000001 
} CLUSTER_SHARED_VOLUME_BACKUP_STATE, *PCLUSTER_SHARED_VOLUME_BACKUP_STATE;
#endif /* (_WIN32_WINNT >= 0x0601) */

#ifdef __cplusplus
}
#endif

#ifndef _CLUSTER_API_TYPES_
#define _CLUSTER_API_TYPES_
#endif
#endif
