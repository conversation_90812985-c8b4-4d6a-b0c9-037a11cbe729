# Time-stamp: "Sat Jul 14 00:27:32 2001 by Automatic Bizooty (__blocks2pm.plx)"
$Text::Unidecode::Char[0x6f] = [
'<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ',
'<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', 'Cong ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', 'Ou ', 'Teng ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON> ', 'Shang ', '<PERSON> ', '<PERSON>n ', '<PERSON><PERSON> ', 'Wa ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ',
'<PERSON>g ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', qq{[?] }, qq{[?] }, '<PERSON> ', '<PERSON>n ', '<PERSON>hi ', '<PERSON> ', '<PERSON> ', '<PERSON>e ',
'<PERSON>ao ', '<PERSON> ', '<PERSON>g ', '<PERSON> ', '<PERSON>e ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>ui ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON>an ', '<PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON>an ', '<PERSON>n ', '<PERSON> ', '<PERSON> ', '<PERSON> ', 'Su ', 'Liao ', 'Zhen ', 'Zhong ', 'Yi ', 'Di ', 'Wan ', 'Dan ', 'Tan ', 'Chao ', 'Xun ',
'Kui ', 'Yie ', 'Shao ', 'Tu ', 'Zhu ', 'San ', 'Hei ', 'Bi ', 'Shan ', 'Chan ', 'Chan ', 'Shu ', 'Tong ', 'Pu ', 'Lin ', 'Wei ',
'Se ', 'Se ', 'Cheng ', 'Jiong ', 'Cheng ', 'Hua ', 'Jiao ', 'Lao ', 'Che ', 'Gan ', 'Cun ', 'Heng ', 'Si ', 'Shu ', 'Peng ', 'Han ',
'Yun ', 'Liu ', 'Hong ', 'Fu ', 'Hao ', 'He ', 'Xian ', 'Jian ', 'Shan ', 'Xi ', 'Oki ', qq{[?] }, 'Lan ', qq{[?] }, 'Yu ', 'Lin ',
'Min ', 'Zao ', 'Dang ', 'Wan ', 'Ze ', 'Xie ', 'Yu ', 'Li ', 'Shi ', 'Xue ', 'Ling ', 'Man ', 'Zi ', 'Yong ', 'Kuai ', 'Can ',
'Lian ', 'Dian ', 'Ye ', 'Ao ', 'Huan ', 'Zhen ', 'Chan ', 'Man ', 'Dan ', 'Dan ', 'Yi ', 'Sui ', 'Pi ', 'Ju ', 'Ta ', 'Qin ',
'Ji ', 'Zhuo ', 'Lian ', 'Nong ', 'Guo ', 'Jin ', 'Fen ', 'Se ', 'Ji ', 'Sui ', 'Hui ', 'Chu ', 'Ta ', 'Song ', 'Ding ', qq{[?] },
'Zhu ', 'Lai ', 'Bin ', 'Lian ', 'Mi ', 'Shi ', 'Shu ', 'Mi ', 'Ning ', 'Ying ', 'Ying ', 'Meng ', 'Jin ', 'Qi ', 'Pi ', 'Ji ',
'Hao ', 'Ru ', 'Zui ', 'Wo ', 'Tao ', 'Yin ', 'Yin ', 'Dui ', 'Ci ', 'Huo ', 'Jing ', 'Lan ', 'Jun ', 'Ai ', 'Pu ', 'Zhuo ',
'Wei ', 'Bin ', 'Gu ', 'Qian ', 'Xing ', 'Hama ', 'Kuo ', 'Fei ', qq{[?] }, 'Boku ', 'Jian ', 'Wei ', 'Luo ', 'Zan ', 'Lu ', 'Li ',
];
1;
