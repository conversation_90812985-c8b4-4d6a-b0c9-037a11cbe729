CMAKE_LINK_DEPENDS_USE_LINKER
-----------------------------

.. versionadded:: 3.27

For the :ref:`Makefile <Makefile Generators>` and
:ref:`Ninja <Ninja Generators>` generators, link dependencies are now, for a
selection of linkers, generated by the linker itself. By defining this
variable with value ``FALSE``, you can deactivate this feature.

This feature is also deactivated if the :prop_tgt:`LINK_DEPENDS_NO_SHARED`
target property is true.

.. note::

  CMake version |release| defaults this variable to ``FALSE`` if the linker is
  one from the GNU binutils linkers (``ld`` and ``ld.bfd`` for version less
  than 2.41 or ``ld.gold`` for any version) because it generate spurious
  dependencies on temporary files when LTO is enabled.  See `GNU bug 30568`_.

.. _`GNU bug 30568`: https://sourceware.org/bugzilla/show_bug.cgi?id=30568
