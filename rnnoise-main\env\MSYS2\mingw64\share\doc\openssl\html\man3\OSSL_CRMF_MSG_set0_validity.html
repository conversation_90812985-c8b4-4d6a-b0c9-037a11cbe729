<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_CRMF_MSG_set0_validity</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_CRMF_MSG_set0_validity, OSSL_CRMF_MSG_set_certReqId, OSSL_CRMF_CERTTEMPLATE_fill, OSSL_CRMF_MSG_set0_extensions, OSSL_CRMF_MSG_push0_extension, OSSL_CRMF_MSG_create_popo, OSSL_CRMF_MSGS_verify_popo - functions populating and verifying CRMF CertReqMsg structures</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/crmf.h&gt;

int OSSL_CRMF_MSG_set0_validity(OSSL_CRMF_MSG *crm,
                                ASN1_TIME *notBefore, ASN1_TIME *notAfter);

int OSSL_CRMF_MSG_set_certReqId(OSSL_CRMF_MSG *crm, int rid);

int OSSL_CRMF_CERTTEMPLATE_fill(OSSL_CRMF_CERTTEMPLATE *tmpl,
                                EVP_PKEY *pubkey,
                                const X509_NAME *subject,
                                const X509_NAME *issuer,
                                const ASN1_INTEGER *serial);

int OSSL_CRMF_MSG_set0_extensions(OSSL_CRMF_MSG *crm, X509_EXTENSIONS *exts);

int OSSL_CRMF_MSG_push0_extension(OSSL_CRMF_MSG *crm, X509_EXTENSION *ext);

int OSSL_CRMF_MSG_create_popo(int meth, OSSL_CRMF_MSG *crm,
                              EVP_PKEY *pkey, const EVP_MD *digest,
                              OSSL_LIB_CTX *libctx, const char *propq);

int OSSL_CRMF_MSGS_verify_popo(const OSSL_CRMF_MSGS *reqs,
                               int rid, int acceptRAVerified,
                               OSSL_LIB_CTX *libctx, const char *propq);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>OSSL_CRMF_MSG_set0_validity() sets the <i>notBefore</i> and <i>notAfter</i> fields as validity constraints in the certTemplate of <i>crm</i>. Any of the <i>notBefore</i> and <i>notAfter</i> parameters may be NULL, which means no constraint for the respective field. On success ownership of <i>notBefore</i> and <i>notAfter</i> is transferred to <i>crm</i>.</p>

<p>OSSL_CRMF_MSG_set_certReqId() sets <i>rid</i> as the certReqId of <i>crm</i>.</p>

<p>OSSL_CRMF_CERTTEMPLATE_fill() sets those fields of the certTemplate <i>tmpl</i> for which non-NULL values are provided: <i>pubkey</i>, <i>subject</i>, <i>issuer</i>, and/or <i>serial</i>. X.509 extensions may be set using OSSL_CRMF_MSG_set0_extensions(). On success the reference counter of the <i>pubkey</i> (if given) is incremented, while the <i>subject</i>, <i>issuer</i>, and <i>serial</i> structures (if given) are copied.</p>

<p>OSSL_CRMF_MSG_set0_extensions() sets <i>exts</i> as the extensions in the certTemplate of <i>crm</i>. Frees any pre-existing ones and consumes <i>exts</i>.</p>

<p>OSSL_CRMF_MSG_push0_extension() pushes the X509 extension <i>ext</i> to the extensions in the certTemplate of <i>crm</i>. Consumes <i>ext</i>.</p>

<p>OSSL_CRMF_MSG_create_popo() creates and sets the Proof-of-Possession (POPO) according to the method <i>meth</i> in <i>crm</i>. The library context <i>libctx</i> and property query string <i>propq</i>, may be NULL to select the defaults. In case the method is OSSL_CRMF_POPO_SIGNATURE the POPO is calculated using the private key <i>pkey</i> and the digest method <i>digest</i>, where the <i>digest</i> argument is ignored if <i>pkey</i> is of a type (such as Ed25519 and Ed448) that is implicitly associated with a digest algorithm.</p>

<p><i>meth</i> can be one of the following:</p>

<ul>

<li><p>OSSL_CRMF_POPO_NONE - RFC 4211, section 4, POP field omitted. CA/RA uses out-of-band method to verify POP. Note that servers may fail in this case, resulting for instance in HTTP error code 500 (Internal error).</p>

</li>
<li><p>OSSL_CRMF_POPO_RAVERIFIED - RFC 4211, section 4, explicit indication that the RA has already verified the POP.</p>

</li>
<li><p>OSSL_CRMF_POPO_SIGNATURE - RFC 4211, section 4.1, only case 3 supported so far.</p>

</li>
<li><p>OSSL_CRMF_POPO_KEYENC - RFC 4211, section 4.2, only indirect method (subsequentMessage/enccert) supported, challenge-response exchange (challengeResp) not yet supported.</p>

</li>
<li><p>OSSL_CRMF_POPO_KEYAGREE - RFC 4211, section 4.3, not yet supported.</p>

</li>
</ul>

<p>OSSL_CRMF_MSGS_verify_popo verifies the Proof-of-Possession of the request with the given <i>rid</i> in the list of <i>reqs</i>. Optionally accepts RAVerified. It can make use of the library context <i>libctx</i> and property query string <i>propq</i>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>All functions return 1 on success, 0 on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p>RFC 4211</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The OpenSSL CRMF support was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2007-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


