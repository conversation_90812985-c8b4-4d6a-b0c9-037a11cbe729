.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_type_get2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_type_get2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_certificate_type_t gnutls_certificate_type_get2(gnutls_session_t " session ", gnutls_ctype_target_t " target ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_ctype_target_t target" 12
is a \fBgnutls_ctype_target_t\fP type.
.SH "DESCRIPTION"
This function returns the type of the certificate that a side
is negotiated to use.  The certificate type is by default X.509,
unless an alternative certificate type is enabled by \fBgnutls_init()\fP and
negotiated during the session.

The  \fItarget\fP parameter specifies whether to request the negotiated
certificate type for the client (\fBGNUTLS_CTYPE_CLIENT\fP),
or for the server (\fBGNUTLS_CTYPE_SERVER\fP). Additionally, in P2P mode
connection set up where you don't know in advance who will be client
and who will be server you can use the flag (\fBGNUTLS_CTYPE_OURS\fP) and
(\fBGNUTLS_CTYPE_PEERS\fP) to retrieve the corresponding certificate types.

Resumed sessions will return the certificate type that was negotiated
and used in the original session. That is, this function can be used
to reliably determine the type of the certificate returned by
\fBgnutls_certificate_get_peers()\fP.
.SH "RETURNS"
the currently used \fBgnutls_certificate_type_t\fP certificate
type for the client or the server.
.SH "SINCE"
3.6.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
