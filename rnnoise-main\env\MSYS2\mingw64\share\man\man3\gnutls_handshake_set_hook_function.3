.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake_set_hook_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake_set_hook_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_handshake_set_hook_function(gnutls_session_t " session ", unsigned int " htype ", int " when ", gnutls_handshake_hook_func " func ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type
.IP "unsigned int htype" 12
the \fBgnutls_handshake_description_t\fP of the message to hook at
.IP "int when" 12
\fBGNUTLS_HOOK_\fP* depending on when the hook function should be called
.IP "gnutls_handshake_hook_func func" 12
is the function to be called
.SH "DESCRIPTION"
This function will set a callback to be called after or before the specified
handshake message has been received or generated. This is a
generalization of \fBgnutls_handshake_set_post_client_hello_function()\fP.

To call the hook function prior to the message being generated or processed
use \fBGNUTLS_HOOK_PRE\fP as  \fIwhen\fP parameter, \fBGNUTLS_HOOK_POST\fP to call
after, and \fBGNUTLS_HOOK_BOTH\fP for both cases.

This callback must return 0 on success or a gnutls error code to
terminate the handshake.

To hook at all handshake messages use an  \fIhtype\fP of \fBGNUTLS_HANDSHAKE_ANY\fP.
.SH "WARNING"
You should not use this function to terminate the
handshake based on client input unless you know what you are
doing. Before the handshake is finished there is no way to know if
there is a man\-in\-the\-middle attack being performed.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
