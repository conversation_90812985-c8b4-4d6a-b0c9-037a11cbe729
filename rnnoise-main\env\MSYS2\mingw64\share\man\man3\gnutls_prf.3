.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_prf" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_prf \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_prf(gnutls_session_t " session ", size_t " label_size ", const char * " label ", int " server_random_first ", size_t " extra_size ", const char * " extra ", size_t " outsize ", char * " out ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "size_t label_size" 12
length of the  \fIlabel\fP variable.
.IP "const char * label" 12
label used in PRF computation, typically a short string.
.IP "int server_random_first" 12
non\-zero if server random field should be first in seed
.IP "size_t extra_size" 12
length of the  \fIextra\fP variable.
.IP "const char * extra" 12
optional extra data to seed the PRF with.
.IP "size_t outsize" 12
size of pre\-allocated output buffer to hold the output.
.IP "char * out" 12
pre\-allocated buffer to hold the generated data.
.SH "DESCRIPTION"
Applies the TLS Pseudo\-Random\-Function (PRF) on the master secret
and the provided data, seeded with the client and server random fields.
For the key expansion specified in RFC5705 see \fBgnutls_prf_rfc5705()\fP.

The  \fIlabel\fP variable usually contains a string denoting the purpose
for the generated data.  The  \fIserver_random_first\fP indicates whether
the client random field or the server random field should be first
in the seed.  Non\-zero indicates that the server random field is first,
0 that the client random field is first.

The  \fIextra\fP variable can be used to add more data to the seed, after
the random variables.  It can be used to make sure the
generated output is strongly connected to some additional data
(e.g., a string used in user authentication).

The output is placed in  \fIout\fP , which must be pre\-allocated.
.SH "NOTE"
This function produces identical output with \fBgnutls_prf_rfc5705()\fP
when  \fIserver_random_first\fP is set to 0 and  \fIextra\fP is \fBNULL\fP. Under TLS1.3
this function will only operate when these conditions are true, or otherwise
return \fBGNUTLS_E_INVALID_REQUEST\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
