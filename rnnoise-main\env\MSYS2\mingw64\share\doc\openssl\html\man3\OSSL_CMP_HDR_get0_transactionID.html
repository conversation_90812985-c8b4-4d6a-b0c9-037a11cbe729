<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_CMP_HDR_get0_transactionID</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_CMP_HDR_get0_transactionID, OSSL_CMP_HDR_get0_recipNonce, OSSL_CMP_HDR_get0_geninfo_ITAVs - functions manipulating CMP message headers</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code> #include &lt;openssl/cmp.h&gt;

 ASN1_OCTET_STRING *OSSL_CMP_HDR_get0_transactionID(const
                                                    OSSL_CMP_PKIHEADER *hdr);
 ASN1_OCTET_STRING *OSSL_CMP_HDR_get0_recipNonce(const
                                                 OSSL_CMP_PKIHEADER *hdr);
STACK_OF(OSSL_CMP_ITAV)
    *OSSL_CMP_HDR_get0_geninfo_ITAVs(const OSSL_CMP_PKIHEADER *hdr);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>OSSL_CMP_HDR_get0_transactionID returns the transaction ID of the given PKIHeader.</p>

<p>OSSL_CMP_HDR_get0_recipNonce returns the recipient nonce of the given PKIHeader.</p>

<p>OSSL_CMP_HDR_get0_geninfo_ITAVs() returns the list of ITAVs in the generalInfo field of the given PKIHeader.</p>

<h1 id="NOTES">NOTES</h1>

<p>CMP is defined in RFC 4210.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The functions return the intended pointer value as described above or NULL if the respective entry does not exist and on error.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The OpenSSL CMP support was added in OpenSSL 3.0.</p>

<p>OSSL_CMP_HDR_get0_geninfo_ITAVs() was added in OpenSSL 3.3.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2007-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


