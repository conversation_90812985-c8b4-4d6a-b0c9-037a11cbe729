<?xml version="1.0"?> <!-- THIS FILE IS GENERATED -*- buffer-read-only: t -*-  -->
<!-- vi:set ro: -->
<!-- Copyright (C) 2009-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-syscalls.dtd">

<!-- This file was generated using the following file:

     /usr/src/sys/sys/syscall.h

     The file mentioned above belongs to the FreeBSD Kernel.  -->

<syscalls_info>
  <syscall name="syscall" number="0"/>
  <syscall name="exit" number="1"/>
  <syscall name="fork" number="2"/>
  <syscall name="read" number="3"/>
  <syscall name="write" number="4"/>
  <syscall name="open" number="5"/>
  <syscall name="close" number="6"/>
  <syscall name="wait4" number="7"/>
  <syscall name="link" number="9"/>
  <syscall name="unlink" number="10"/>
  <syscall name="execv" number="11"/>
  <syscall name="chdir" number="12"/>
  <syscall name="fchdir" number="13"/>
  <syscall name="freebsd11_mknod" number="14" alias="mknod"/>
  <syscall name="chmod" number="15"/>
  <syscall name="chown" number="16"/>
  <syscall name="break" number="17"/>
  <syscall name="freebsd4_getfsstat" number="18" alias="getfsstat"/>
  <syscall name="getpid" number="20"/>
  <syscall name="mount" number="21"/>
  <syscall name="unmount" number="22"/>
  <syscall name="setuid" number="23"/>
  <syscall name="getuid" number="24"/>
  <syscall name="geteuid" number="25"/>
  <syscall name="ptrace" number="26"/>
  <syscall name="recvmsg" number="27"/>
  <syscall name="sendmsg" number="28"/>
  <syscall name="recvfrom" number="29"/>
  <syscall name="accept" number="30"/>
  <syscall name="getpeername" number="31"/>
  <syscall name="getsockname" number="32"/>
  <syscall name="access" number="33"/>
  <syscall name="chflags" number="34"/>
  <syscall name="fchflags" number="35"/>
  <syscall name="sync" number="36"/>
  <syscall name="kill" number="37"/>
  <syscall name="getppid" number="39"/>
  <syscall name="dup" number="41"/>
  <syscall name="freebsd10_pipe" number="42" alias="pipe"/>
  <syscall name="getegid" number="43"/>
  <syscall name="profil" number="44"/>
  <syscall name="ktrace" number="45"/>
  <syscall name="getgid" number="47"/>
  <syscall name="getlogin" number="49"/>
  <syscall name="setlogin" number="50"/>
  <syscall name="acct" number="51"/>
  <syscall name="sigaltstack" number="53"/>
  <syscall name="ioctl" number="54"/>
  <syscall name="reboot" number="55"/>
  <syscall name="revoke" number="56"/>
  <syscall name="symlink" number="57"/>
  <syscall name="readlink" number="58"/>
  <syscall name="execve" number="59"/>
  <syscall name="umask" number="60"/>
  <syscall name="chroot" number="61"/>
  <syscall name="msync" number="65"/>
  <syscall name="vfork" number="66"/>
  <syscall name="vread" number="67"/>
  <syscall name="vwrite" number="68"/>
  <syscall name="sbrk" number="69"/>
  <syscall name="sstk" number="70"/>
  <syscall name="freebsd11_vadvise" number="72" alias="vadvise"/>
  <syscall name="munmap" number="73"/>
  <syscall name="mprotect" number="74"/>
  <syscall name="madvise" number="75"/>
  <syscall name="vhangup" number="76"/>
  <syscall name="vlimit" number="77"/>
  <syscall name="mincore" number="78"/>
  <syscall name="getgroups" number="79"/>
  <syscall name="setgroups" number="80"/>
  <syscall name="getpgrp" number="81"/>
  <syscall name="setpgid" number="82"/>
  <syscall name="setitimer" number="83"/>
  <syscall name="swapon" number="85"/>
  <syscall name="getitimer" number="86"/>
  <syscall name="getdtablesize" number="89"/>
  <syscall name="dup2" number="90"/>
  <syscall name="fcntl" number="92"/>
  <syscall name="select" number="93"/>
  <syscall name="fsync" number="95"/>
  <syscall name="setpriority" number="96"/>
  <syscall name="socket" number="97"/>
  <syscall name="connect" number="98"/>
  <syscall name="getpriority" number="100"/>
  <syscall name="bind" number="104"/>
  <syscall name="setsockopt" number="105"/>
  <syscall name="listen" number="106"/>
  <syscall name="vtimes" number="107"/>
  <syscall name="vtrace" number="115"/>
  <syscall name="gettimeofday" number="116"/>
  <syscall name="getrusage" number="117"/>
  <syscall name="getsockopt" number="118"/>
  <syscall name="readv" number="120"/>
  <syscall name="writev" number="121"/>
  <syscall name="settimeofday" number="122"/>
  <syscall name="fchown" number="123"/>
  <syscall name="fchmod" number="124"/>
  <syscall name="setreuid" number="126"/>
  <syscall name="setregid" number="127"/>
  <syscall name="rename" number="128"/>
  <syscall name="flock" number="131"/>
  <syscall name="mkfifo" number="132"/>
  <syscall name="sendto" number="133"/>
  <syscall name="shutdown" number="134"/>
  <syscall name="socketpair" number="135"/>
  <syscall name="mkdir" number="136"/>
  <syscall name="rmdir" number="137"/>
  <syscall name="utimes" number="138"/>
  <syscall name="sigreturn" number="139"/>
  <syscall name="adjtime" number="140"/>
  <syscall name="setsid" number="147"/>
  <syscall name="quotactl" number="148"/>
  <syscall name="nlm_syscall" number="154"/>
  <syscall name="nfssvc" number="155"/>
  <syscall name="freebsd4_statfs" number="157" alias="statfs"/>
  <syscall name="freebsd4_fstatfs" number="158" alias="fstatfs"/>
  <syscall name="lgetfh" number="160"/>
  <syscall name="getfh" number="161"/>
  <syscall name="freebsd4_getdomainname" number="162" alias="getdomainname"/>
  <syscall name="freebsd4_setdomainname" number="163" alias="setdomainname"/>
  <syscall name="freebsd4_uname" number="164" alias="uname"/>
  <syscall name="sysarch" number="165"/>
  <syscall name="rtprio" number="166"/>
  <syscall name="semsys" number="169"/>
  <syscall name="msgsys" number="170"/>
  <syscall name="shmsys" number="171"/>
  <syscall name="freebsd6_pread" number="173" alias="pread"/>
  <syscall name="freebsd6_pwrite" number="174" alias="pwrite"/>
  <syscall name="setfib" number="175"/>
  <syscall name="ntp_adjtime" number="176"/>
  <syscall name="setgid" number="181"/>
  <syscall name="setegid" number="182"/>
  <syscall name="seteuid" number="183"/>
  <syscall name="lfs_bmapv" number="184"/>
  <syscall name="lfs_markv" number="185"/>
  <syscall name="lfs_segclean" number="186"/>
  <syscall name="lfs_segwait" number="187"/>
  <syscall name="freebsd11_stat" number="188" alias="stat"/>
  <syscall name="freebsd11_fstat" number="189" alias="fstat"/>
  <syscall name="freebsd11_lstat" number="190" alias="lstat"/>
  <syscall name="pathconf" number="191"/>
  <syscall name="fpathconf" number="192"/>
  <syscall name="getrlimit" number="194"/>
  <syscall name="setrlimit" number="195"/>
  <syscall name="freebsd11_getdirentries" number="196" alias="getdirentries"/>
  <syscall name="freebsd6_mmap" number="197" alias="mmap"/>
  <syscall name="__syscall" number="198"/>
  <syscall name="freebsd6_lseek" number="199" alias="lseek"/>
  <syscall name="freebsd6_truncate" number="200" alias="truncate"/>
  <syscall name="freebsd6_ftruncate" number="201" alias="ftruncate"/>
  <syscall name="__sysctl" number="202"/>
  <syscall name="mlock" number="203"/>
  <syscall name="munlock" number="204"/>
  <syscall name="undelete" number="205"/>
  <syscall name="futimes" number="206"/>
  <syscall name="getpgid" number="207"/>
  <syscall name="poll" number="209"/>
  <syscall name="freebsd7___semctl" number="220" alias="__semctl"/>
  <syscall name="semget" number="221"/>
  <syscall name="semop" number="222"/>
  <syscall name="semconfig" number="223"/>
  <syscall name="freebsd7_msgctl" number="224" alias="msgctl"/>
  <syscall name="msgget" number="225"/>
  <syscall name="msgsnd" number="226"/>
  <syscall name="msgrcv" number="227"/>
  <syscall name="shmat" number="228"/>
  <syscall name="freebsd7_shmctl" number="229" alias="shmctl"/>
  <syscall name="shmdt" number="230"/>
  <syscall name="shmget" number="231"/>
  <syscall name="clock_gettime" number="232"/>
  <syscall name="clock_settime" number="233"/>
  <syscall name="clock_getres" number="234"/>
  <syscall name="ktimer_create" number="235"/>
  <syscall name="ktimer_delete" number="236"/>
  <syscall name="ktimer_settime" number="237"/>
  <syscall name="ktimer_gettime" number="238"/>
  <syscall name="ktimer_getoverrun" number="239"/>
  <syscall name="nanosleep" number="240"/>
  <syscall name="ffclock_getcounter" number="241"/>
  <syscall name="ffclock_setestimate" number="242"/>
  <syscall name="ffclock_getestimate" number="243"/>
  <syscall name="clock_nanosleep" number="244"/>
  <syscall name="clock_getcpuclockid2" number="247"/>
  <syscall name="ntp_gettime" number="248"/>
  <syscall name="minherit" number="250"/>
  <syscall name="rfork" number="251"/>
  <syscall name="openbsd_poll" number="252"/>
  <syscall name="issetugid" number="253"/>
  <syscall name="lchown" number="254"/>
  <syscall name="aio_read" number="255"/>
  <syscall name="aio_write" number="256"/>
  <syscall name="lio_listio" number="257"/>
  <syscall name="freebsd11_getdents" number="272" alias="getdents"/>
  <syscall name="lchmod" number="274"/>
  <syscall name="netbsd_lchown" number="275"/>
  <syscall name="lutimes" number="276"/>
  <syscall name="netbsd_msync" number="277"/>
  <syscall name="freebsd11_nstat" number="278" alias="nstat"/>
  <syscall name="freebsd11_nfstat" number="279" alias="nfstat"/>
  <syscall name="freebsd11_nlstat" number="280" alias="nlstat"/>
  <syscall name="preadv" number="289"/>
  <syscall name="pwritev" number="290"/>
  <syscall name="freebsd4_fhstatfs" number="297" alias="fhstatfs"/>
  <syscall name="fhopen" number="298"/>
  <syscall name="freebsd11_fhstat" number="299" alias="fhstat"/>
  <syscall name="modnext" number="300"/>
  <syscall name="modstat" number="301"/>
  <syscall name="modfnext" number="302"/>
  <syscall name="modfind" number="303"/>
  <syscall name="kldload" number="304"/>
  <syscall name="kldunload" number="305"/>
  <syscall name="kldfind" number="306"/>
  <syscall name="kldnext" number="307"/>
  <syscall name="kldstat" number="308"/>
  <syscall name="kldfirstmod" number="309"/>
  <syscall name="getsid" number="310"/>
  <syscall name="setresuid" number="311"/>
  <syscall name="setresgid" number="312"/>
  <syscall name="signanosleep" number="313"/>
  <syscall name="aio_return" number="314"/>
  <syscall name="aio_suspend" number="315"/>
  <syscall name="aio_cancel" number="316"/>
  <syscall name="aio_error" number="317"/>
  <syscall name="freebsd6_aio_read" number="318" alias="aio_read"/>
  <syscall name="freebsd6_aio_write" number="319" alias="aio_write"/>
  <syscall name="freebsd6_lio_listio" number="320" alias="lio_listio"/>
  <syscall name="yield" number="321"/>
  <syscall name="thr_sleep" number="322"/>
  <syscall name="thr_wakeup" number="323"/>
  <syscall name="mlockall" number="324"/>
  <syscall name="munlockall" number="325"/>
  <syscall name="__getcwd" number="326"/>
  <syscall name="sched_setparam" number="327"/>
  <syscall name="sched_getparam" number="328"/>
  <syscall name="sched_setscheduler" number="329"/>
  <syscall name="sched_getscheduler" number="330"/>
  <syscall name="sched_yield" number="331"/>
  <syscall name="sched_get_priority_max" number="332"/>
  <syscall name="sched_get_priority_min" number="333"/>
  <syscall name="sched_rr_get_interval" number="334"/>
  <syscall name="utrace" number="335"/>
  <syscall name="freebsd4_sendfile" number="336" alias="sendfile"/>
  <syscall name="kldsym" number="337"/>
  <syscall name="jail" number="338"/>
  <syscall name="nnpfs_syscall" number="339"/>
  <syscall name="sigprocmask" number="340"/>
  <syscall name="sigsuspend" number="341"/>
  <syscall name="freebsd4_sigaction" number="342" alias="sigaction"/>
  <syscall name="sigpending" number="343"/>
  <syscall name="freebsd4_sigreturn" number="344" alias="sigreturn"/>
  <syscall name="sigtimedwait" number="345"/>
  <syscall name="sigwaitinfo" number="346"/>
  <syscall name="__acl_get_file" number="347"/>
  <syscall name="__acl_set_file" number="348"/>
  <syscall name="__acl_get_fd" number="349"/>
  <syscall name="__acl_set_fd" number="350"/>
  <syscall name="__acl_delete_file" number="351"/>
  <syscall name="__acl_delete_fd" number="352"/>
  <syscall name="__acl_aclcheck_file" number="353"/>
  <syscall name="__acl_aclcheck_fd" number="354"/>
  <syscall name="extattrctl" number="355"/>
  <syscall name="extattr_set_file" number="356"/>
  <syscall name="extattr_get_file" number="357"/>
  <syscall name="extattr_delete_file" number="358"/>
  <syscall name="aio_waitcomplete" number="359"/>
  <syscall name="getresuid" number="360"/>
  <syscall name="getresgid" number="361"/>
  <syscall name="kqueue" number="362"/>
  <syscall name="freebsd11_kevent" number="363" alias="kevent"/>
  <syscall name="__cap_get_proc" number="364"/>
  <syscall name="__cap_set_proc" number="365"/>
  <syscall name="__cap_get_fd" number="366"/>
  <syscall name="__cap_get_file" number="367"/>
  <syscall name="__cap_set_fd" number="368"/>
  <syscall name="__cap_set_file" number="369"/>
  <syscall name="extattr_set_fd" number="371"/>
  <syscall name="extattr_get_fd" number="372"/>
  <syscall name="extattr_delete_fd" number="373"/>
  <syscall name="__setugid" number="374"/>
  <syscall name="nfsclnt" number="375"/>
  <syscall name="eaccess" number="376"/>
  <syscall name="afs3_syscall" number="377"/>
  <syscall name="nmount" number="378"/>
  <syscall name="kse_exit" number="379"/>
  <syscall name="kse_wakeup" number="380"/>
  <syscall name="kse_create" number="381"/>
  <syscall name="kse_thr_interrupt" number="382"/>
  <syscall name="kse_release" number="383"/>
  <syscall name="__mac_get_proc" number="384"/>
  <syscall name="__mac_set_proc" number="385"/>
  <syscall name="__mac_get_fd" number="386"/>
  <syscall name="__mac_get_file" number="387"/>
  <syscall name="__mac_set_fd" number="388"/>
  <syscall name="__mac_set_file" number="389"/>
  <syscall name="kenv" number="390"/>
  <syscall name="lchflags" number="391"/>
  <syscall name="uuidgen" number="392"/>
  <syscall name="sendfile" number="393"/>
  <syscall name="mac_syscall" number="394"/>
  <syscall name="freebsd11_getfsstat" number="395" alias="getfsstat"/>
  <syscall name="freebsd11_statfs" number="396" alias="statfs"/>
  <syscall name="freebsd11_fstatfs" number="397" alias="fstatfs"/>
  <syscall name="freebsd11_fhstatfs" number="398" alias="fhstatfs"/>
  <syscall name="ksem_close" number="400"/>
  <syscall name="ksem_post" number="401"/>
  <syscall name="ksem_wait" number="402"/>
  <syscall name="ksem_trywait" number="403"/>
  <syscall name="ksem_init" number="404"/>
  <syscall name="ksem_open" number="405"/>
  <syscall name="ksem_unlink" number="406"/>
  <syscall name="ksem_getvalue" number="407"/>
  <syscall name="ksem_destroy" number="408"/>
  <syscall name="__mac_get_pid" number="409"/>
  <syscall name="__mac_get_link" number="410"/>
  <syscall name="__mac_set_link" number="411"/>
  <syscall name="extattr_set_link" number="412"/>
  <syscall name="extattr_get_link" number="413"/>
  <syscall name="extattr_delete_link" number="414"/>
  <syscall name="__mac_execve" number="415"/>
  <syscall name="sigaction" number="416"/>
  <syscall name="sigreturn" number="417"/>
  <syscall name="getcontext" number="421"/>
  <syscall name="setcontext" number="422"/>
  <syscall name="swapcontext" number="423"/>
  <syscall name="freebsd13_swapoff" number="424" alias="swapoff"/>
  <syscall name="__acl_get_link" number="425"/>
  <syscall name="__acl_set_link" number="426"/>
  <syscall name="__acl_delete_link" number="427"/>
  <syscall name="__acl_aclcheck_link" number="428"/>
  <syscall name="sigwait" number="429"/>
  <syscall name="thr_create" number="430"/>
  <syscall name="thr_exit" number="431"/>
  <syscall name="thr_self" number="432"/>
  <syscall name="thr_kill" number="433"/>
  <syscall name="freebsd10__umtx_lock" number="434" alias="_umtx_lock"/>
  <syscall name="freebsd10__umtx_unlock" number="435" alias="_umtx_unlock"/>
  <syscall name="jail_attach" number="436"/>
  <syscall name="extattr_list_fd" number="437"/>
  <syscall name="extattr_list_file" number="438"/>
  <syscall name="extattr_list_link" number="439"/>
  <syscall name="kse_switchin" number="440"/>
  <syscall name="ksem_timedwait" number="441"/>
  <syscall name="thr_suspend" number="442"/>
  <syscall name="thr_wake" number="443"/>
  <syscall name="kldunloadf" number="444"/>
  <syscall name="audit" number="445"/>
  <syscall name="auditon" number="446"/>
  <syscall name="getauid" number="447"/>
  <syscall name="setauid" number="448"/>
  <syscall name="getaudit" number="449"/>
  <syscall name="setaudit" number="450"/>
  <syscall name="getaudit_addr" number="451"/>
  <syscall name="setaudit_addr" number="452"/>
  <syscall name="auditctl" number="453"/>
  <syscall name="_umtx_op" number="454"/>
  <syscall name="thr_new" number="455"/>
  <syscall name="sigqueue" number="456"/>
  <syscall name="kmq_open" number="457"/>
  <syscall name="kmq_setattr" number="458"/>
  <syscall name="kmq_timedreceive" number="459"/>
  <syscall name="kmq_timedsend" number="460"/>
  <syscall name="kmq_notify" number="461"/>
  <syscall name="kmq_unlink" number="462"/>
  <syscall name="abort2" number="463"/>
  <syscall name="thr_set_name" number="464"/>
  <syscall name="aio_fsync" number="465"/>
  <syscall name="rtprio_thread" number="466"/>
  <syscall name="sctp_peeloff" number="471"/>
  <syscall name="sctp_generic_sendmsg" number="472"/>
  <syscall name="sctp_generic_sendmsg_iov" number="473"/>
  <syscall name="sctp_generic_recvmsg" number="474"/>
  <syscall name="pread" number="475"/>
  <syscall name="pwrite" number="476"/>
  <syscall name="mmap" number="477"/>
  <syscall name="lseek" number="478"/>
  <syscall name="truncate" number="479"/>
  <syscall name="ftruncate" number="480"/>
  <syscall name="thr_kill2" number="481"/>
  <syscall name="freebsd12_shm_open" number="482" alias="shm_open"/>
  <syscall name="shm_unlink" number="483"/>
  <syscall name="cpuset" number="484"/>
  <syscall name="cpuset_setid" number="485"/>
  <syscall name="cpuset_getid" number="486"/>
  <syscall name="cpuset_getaffinity" number="487"/>
  <syscall name="cpuset_setaffinity" number="488"/>
  <syscall name="faccessat" number="489"/>
  <syscall name="fchmodat" number="490"/>
  <syscall name="fchownat" number="491"/>
  <syscall name="fexecve" number="492"/>
  <syscall name="freebsd11_fstatat" number="493" alias="fstatat"/>
  <syscall name="futimesat" number="494"/>
  <syscall name="linkat" number="495"/>
  <syscall name="mkdirat" number="496"/>
  <syscall name="mkfifoat" number="497"/>
  <syscall name="freebsd11_mknodat" number="498" alias="mknodat"/>
  <syscall name="openat" number="499"/>
  <syscall name="readlinkat" number="500"/>
  <syscall name="renameat" number="501"/>
  <syscall name="symlinkat" number="502"/>
  <syscall name="unlinkat" number="503"/>
  <syscall name="posix_openpt" number="504"/>
  <syscall name="gssd_syscall" number="505"/>
  <syscall name="jail_get" number="506"/>
  <syscall name="jail_set" number="507"/>
  <syscall name="jail_remove" number="508"/>
  <syscall name="freebsd12_closefrom" number="509" alias="closefrom"/>
  <syscall name="__semctl" number="510"/>
  <syscall name="msgctl" number="511"/>
  <syscall name="shmctl" number="512"/>
  <syscall name="lpathconf" number="513"/>
  <syscall name="cap_new" number="514"/>
  <syscall name="__cap_rights_get" number="515"/>
  <syscall name="cap_enter" number="516"/>
  <syscall name="cap_getmode" number="517"/>
  <syscall name="pdfork" number="518"/>
  <syscall name="pdkill" number="519"/>
  <syscall name="pdgetpid" number="520"/>
  <syscall name="pselect" number="522"/>
  <syscall name="getloginclass" number="523"/>
  <syscall name="setloginclass" number="524"/>
  <syscall name="rctl_get_racct" number="525"/>
  <syscall name="rctl_get_rules" number="526"/>
  <syscall name="rctl_get_limits" number="527"/>
  <syscall name="rctl_add_rule" number="528"/>
  <syscall name="rctl_remove_rule" number="529"/>
  <syscall name="posix_fallocate" number="530"/>
  <syscall name="posix_fadvise" number="531"/>
  <syscall name="wait6" number="532"/>
  <syscall name="cap_rights_limit" number="533"/>
  <syscall name="cap_ioctls_limit" number="534"/>
  <syscall name="cap_ioctls_get" number="535"/>
  <syscall name="cap_fcntls_limit" number="536"/>
  <syscall name="cap_fcntls_get" number="537"/>
  <syscall name="bindat" number="538"/>
  <syscall name="connectat" number="539"/>
  <syscall name="chflagsat" number="540"/>
  <syscall name="accept4" number="541"/>
  <syscall name="pipe2" number="542"/>
  <syscall name="aio_mlock" number="543"/>
  <syscall name="procctl" number="544"/>
  <syscall name="ppoll" number="545"/>
  <syscall name="futimens" number="546"/>
  <syscall name="utimensat" number="547"/>
  <syscall name="numa_getaffinity" number="548"/>
  <syscall name="numa_setaffinity" number="549"/>
  <syscall name="fdatasync" number="550"/>
  <syscall name="fstat" number="551"/>
  <syscall name="fstatat" number="552"/>
  <syscall name="fhstat" number="553"/>
  <syscall name="getdirentries" number="554"/>
  <syscall name="statfs" number="555"/>
  <syscall name="fstatfs" number="556"/>
  <syscall name="getfsstat" number="557"/>
  <syscall name="fhstatfs" number="558"/>
  <syscall name="mknodat" number="559"/>
  <syscall name="kevent" number="560"/>
  <syscall name="cpuset_getdomain" number="561"/>
  <syscall name="cpuset_setdomain" number="562"/>
  <syscall name="getrandom" number="563"/>
  <syscall name="getfhat" number="564"/>
  <syscall name="fhlink" number="565"/>
  <syscall name="fhlinkat" number="566"/>
  <syscall name="fhreadlink" number="567"/>
  <syscall name="funlinkat" number="568"/>
  <syscall name="copy_file_range" number="569"/>
  <syscall name="__sysctlbyname" number="570"/>
  <syscall name="shm_open2" number="571"/>
  <syscall name="shm_rename" number="572"/>
  <syscall name="sigfastblock" number="573"/>
  <syscall name="__realpathat" number="574"/>
  <syscall name="close_range" number="575"/>
  <syscall name="rpctls_syscall" number="576"/>
  <syscall name="__specialfd" number="577"/>
  <syscall name="aio_writev" number="578"/>
  <syscall name="aio_readv" number="579"/>
  <syscall name="fspacectl" number="580"/>
  <syscall name="sched_getcpu" number="581"/>
  <syscall name="swapoff" number="582"/>
  <syscall name="kqueuex" number="583"/>
  <syscall name="membarrier" number="584"/>
  <syscall name="timerfd_create" number="585"/>
  <syscall name="timerfd_gettime" number="586"/>
  <syscall name="timerfd_settime" number="587"/>
</syscalls_info>
