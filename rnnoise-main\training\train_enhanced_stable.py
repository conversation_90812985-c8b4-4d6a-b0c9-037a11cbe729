#!/usr/bin/env python3
"""
RNN噪声抑制与人声增强模型训练脚本 - 数值稳定版本
使用clean_voice和wind_noise_voice数据
"""

import tensorflow as tf
import numpy as np
import h5py
import os
from datetime import datetime

# 设置CPU运行
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

def create_simple_enhanced_model():
    """创建简化但稳定的增强RNN模型"""
    print("构建简化增强RNN模型...")
    
    # 输入层 (68维)
    inputs = tf.keras.Input(shape=(None, 68), name='main_input')
    
    # 简化的特征处理
    x = tf.keras.layers.Dense(48, activation='tanh', name='feature_dense')(inputs)
    
    # 主要GRU层
    gru_out = tf.keras.layers.GRU(64, activation='tanh', return_sequences=True, name='main_gru')(x)
    
    # 双重输出
    noise_output = tf.keras.layers.Dense(18, activation='sigmoid', name='noise_output')(gru_out)
    voice_output = tf.keras.layers.Dense(18, activation='sigmoid', name='voice_output')(gru_out)
    vad_output = tf.keras.layers.Dense(1, activation='sigmoid', name='vad_output')(gru_out)
    
    model = tf.keras.Model(inputs=inputs, outputs=[noise_output, voice_output, vad_output])
    
    return model

def stable_mse_loss(y_true, y_pred):
    """数值稳定的MSE损失函数"""
    # 创建mask，处理-1标记的无效数据
    mask = tf.cast(tf.greater(y_true, -0.5), tf.float32)
    
    # 将负值设为0，确保数值稳定
    y_true_clean = tf.maximum(y_true, 0.0)
    y_pred_clean = tf.clip_by_value(y_pred, 1e-7, 1.0)
    
    # 简单的MSE损失
    mse = tf.square(y_true_clean - y_pred_clean)
    
    # 应用mask
    masked_loss = mask * mse
    
    # 计算平均值，避免除零
    sum_mask = tf.reduce_sum(mask, axis=-1)
    sum_mask = tf.maximum(sum_mask, 1.0)  # 避免除零
    
    return tf.reduce_sum(masked_loss, axis=-1) / sum_mask

def load_and_clean_data():
    """加载并清理训练数据"""
    print("加载并清理训练数据...")
    
    with h5py.File('training_data.h5', 'r') as hf:
        all_data = hf['data'][:]
    
    print(f"原始数据形状: {all_data.shape}")
    
    # 检查数据中的异常值
    print("数据质量检查:")
    print(f"  NaN数量: {np.sum(np.isnan(all_data))}")
    print(f"  Inf数量: {np.sum(np.isinf(all_data))}")
    print(f"  数据范围: [{np.min(all_data):.3f}, {np.max(all_data):.3f}]")
    
    # 清理异常值
    all_data = np.nan_to_num(all_data, nan=0.0, posinf=1.0, neginf=-1.0)
    
    # 数据预处理
    window_size = 2000
    nb_sequences = len(all_data) // window_size
    
    print(f"训练序列数量: {nb_sequences}")
    
    # 输入特征 (68维)
    x_train = all_data[:nb_sequences*window_size, :68]
    x_train = np.reshape(x_train, (nb_sequences, window_size, 68))
    
    # 噪声抑制目标 (18维)
    noise_train = all_data[:nb_sequences*window_size, 68:86]
    noise_train = np.reshape(noise_train, (nb_sequences, window_size, 18))
    
    # 人声增强目标 (18维)
    voice_train = all_data[:nb_sequences*window_size, 86:104]
    voice_train = np.reshape(voice_train, (nb_sequences, window_size, 18))
    
    # VAD目标 (1维)
    vad_train = all_data[:nb_sequences*window_size, 104:105]
    vad_train = np.reshape(vad_train, (nb_sequences, window_size, 1))
    
    # 数据范围检查和清理
    print("目标数据统计:")
    print(f"  噪声抑制目标范围: [{np.min(noise_train):.3f}, {np.max(noise_train):.3f}]")
    print(f"  人声增强目标范围: [{np.min(voice_train):.3f}, {np.max(voice_train):.3f}]")
    print(f"  VAD目标范围: [{np.min(vad_train):.3f}, {np.max(vad_train):.3f}]")
    
    # 确保VAD在[0,1]范围内
    vad_train = np.clip(vad_train, 0.0, 1.0)
    
    return x_train, noise_train, voice_train, vad_train

def train_stable_model():
    """训练稳定版本的模型"""
    print("=" * 60)
    print("RNN噪声抑制与人声增强模型训练 - 稳定版本")
    print("数据源: clean_voice + wind_noise_voice")
    print("=" * 60)
    
    # 加载数据
    x_train, noise_train, voice_train, vad_train = load_and_clean_data()
    
    # 创建模型
    model = create_simple_enhanced_model()
    
    # 显示模型结构
    print("\n模型结构:")
    model.summary()
    
    # 编译模型 - 使用更稳定的配置
    print("\n编译模型...")
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.0001, clipnorm=1.0),  # 降低学习率，添加梯度裁剪
        loss={
            'noise_output': stable_mse_loss,
            'voice_output': stable_mse_loss,
            'vad_output': 'binary_crossentropy'
        },
        loss_weights={
            'noise_output': 1.0,      # 降低权重避免数值爆炸
            'voice_output': 0.8,
            'vad_output': 0.1
        },
        metrics={
            'noise_output': ['mae'],
            'voice_output': ['mae'],
            'vad_output': ['accuracy']
        }
    )
    
    # 设置回调
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"../models_enhanced/stable_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)
    
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(
            filepath=f"{model_dir}/model_epoch_{{epoch:02d}}.keras",
            monitor='val_loss',
            save_best_only=True,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=15,
            restore_best_weights=True,
            verbose=1
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.8,
            patience=5,
            min_lr=1e-6,
            verbose=1
        )
    ]
    
    # 开始训练
    print(f"\n开始训练... 模型将保存到: {model_dir}")
    
    try:
        history = model.fit(
            x_train,
            {
                'noise_output': noise_train,
                'voice_output': voice_train,
                'vad_output': vad_train
            },
            batch_size=8,  # 减小批次大小
            epochs=50,     # 减少轮数
            validation_split=0.15,
            callbacks=callbacks,
            verbose=1
        )
        
        # 保存最终模型
        final_model_path = f"{model_dir}/enhanced_rnnoise_stable.keras"
        model.save(final_model_path)
        
        print(f"\n✅ 训练成功完成!")
        print(f"模型保存在: {final_model_path}")
        
        # 显示训练结果
        if len(history.history['loss']) > 0:
            final_loss = history.history['loss'][-1]
            final_val_loss = history.history['val_loss'][-1]
            print(f"最终训练损失: {final_loss:.4f}")
            print(f"最终验证损失: {final_val_loss:.4f}")
        
        return model, history
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        return None, None

def test_model(model_path):
    """测试训练好的模型"""
    print(f"\n测试模型: {model_path}")
    
    try:
        model = tf.keras.models.load_model(model_path, custom_objects={
            'stable_mse_loss': stable_mse_loss
        })
        
        # 创建测试数据
        test_input = np.random.randn(1, 100, 68).astype(np.float32)
        
        # 预测
        predictions = model.predict(test_input, verbose=0)
        
        print("模型测试成功!")
        print(f"输入形状: {test_input.shape}")
        print(f"噪声抑制输出形状: {predictions[0].shape}")
        print(f"人声增强输出形状: {predictions[1].shape}")
        print(f"VAD输出形状: {predictions[2].shape}")
        
        return True
        
    except Exception as e:
        print(f"模型测试失败: {e}")
        return False

if __name__ == '__main__':
    model, history = train_stable_model()
    
    if model is not None:
        # 获取最新的模型文件
        import glob
        model_files = glob.glob("../models_enhanced/stable_*/enhanced_rnnoise_stable.keras")
        if model_files:
            latest_model = max(model_files, key=os.path.getctime)
            test_model(latest_model)
    else:
        print("训练失败，请检查数据和配置")
