.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_get_pin_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_get_pin_function \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "gnutls_pin_callback_t gnutls_pkcs11_get_pin_function(void ** " userdata ");"
.SH ARGUMENTS
.IP "void ** userdata" 12
data to be supplied to callback
.SH "DESCRIPTION"
This function will return the callback function set using
\fBgnutls_pkcs11_set_pin_function()\fP.
.SH "RETURNS"
The function set or NULL otherwise.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
