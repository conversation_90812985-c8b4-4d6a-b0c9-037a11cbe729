.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_global_set_audit_log_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_global_set_audit_log_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_global_set_audit_log_function(gnutls_audit_log_func " log_func ");"
.SH ARGUMENTS
.IP "gnutls_audit_log_func log_func" 12
it is the audit log function
.SH "DESCRIPTION"
This is the function to set the audit logging function. This
is a function to report important issues, such as possible
attacks in the protocol. This is different from \fBgnutls_global_set_log_function()\fP
because it will report also session\-specific events. The session
parameter will be null if there is no corresponding TLS session.

 \fIgnutls_audit_log_func\fP is of the form,
void (*gnutls_audit_log_func)( gnutls_session_t, const char*);
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
