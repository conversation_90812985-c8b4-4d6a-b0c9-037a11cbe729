This directory used to contain various modules for Emacs support.

These were added shortly after Git was first released. Since then
Emacs's own support for Git got better than what was offered by these
modes. There are also popular 3rd-party Git modes such as Magit which
offer replacements for these.

The following modules were available, and can be dug up from the Git
history:

* git.el:

  Wrapper for "git status" that provided access to other git commands.

  Modern alternatives to this include Magit, and VC mode that ships
  with Emacs.

* git-blame.el:

  A wrapper for "git blame" written before Emacs's own vc-annotate
  mode learned to invoke git-blame, which can be done via C-x v g.

* vc-git.el:

  This file used to contain the VC-mode backend for git, but it is no
  longer distributed with git. It is now maintained as part of Emacs
  and included in standard Emacs distributions starting from version
  22.2.

  If you have an earlier Emacs version, upgrading to Emacs 22 is
  recommended, since the VC mode in older Emacs is not generic enough
  to be able to support git in a reasonable manner, and no attempt has
  been made to backport vc-git.el.
