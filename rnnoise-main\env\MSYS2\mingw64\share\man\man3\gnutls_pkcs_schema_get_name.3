.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs_schema_get_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs_schema_get_name \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "const char * gnutls_pkcs_schema_get_name(unsigned int " schema ");"
.SH ARGUMENTS
.IP "unsigned int schema" 12
Holds the PKCS \fB12\fP or PBES2 schema (\fBgnutls_pkcs_encrypt_flags_t\fP)
.SH "DESCRIPTION"
This function will return a human readable description of the
PKCS12 or PBES2 schema.
.SH "RETURNS"
a constraint string or \fBNULL\fP on error.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
