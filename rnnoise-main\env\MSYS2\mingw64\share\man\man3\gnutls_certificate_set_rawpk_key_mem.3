.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_rawpk_key_mem" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_rawpk_key_mem \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_rawpk_key_mem(gnutls_certificate_credentials_t " cred ", const gnutls_datum_t * " spki ", const gnutls_datum_t * " pkey ", gnutls_x509_crt_fmt_t " format ", const char * " pass ", unsigned int " key_usage ", const char ** " names ", unsigned int " names_length ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t cred" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "const gnutls_datum_t * spki" 12
contains a raw public key in
PKIX.SubjectPublicKeyInfo format.
.IP "const gnutls_datum_t * pkey" 12
contains a raw private key.
.IP "gnutls_x509_crt_fmt_t format" 12
encoding of the keys. DER or PEM.
.IP "const char * pass" 12
an optional password to unlock the private key pkey.
.IP "unsigned int key_usage" 12
An ORed sequence of \fBGNUTLS_KEY_\fP* flags.
.IP "const char ** names" 12
is an array of DNS names belonging to the public\-key (NULL if none).
.IP "unsigned int names_length" 12
holds the length of the names list.
.IP "unsigned int flags" 12
an ORed sequence of \fBgnutls_pkcs_encrypt_flags_t\fP.
These apply to the private key pkey.
.SH "DESCRIPTION"
This function sets a public/private keypair in the
\fBgnutls_certificate_credentials_t\fP type to be used for authentication
and/or encryption.  \fIspki\fP and  \fIprivkey\fP should match otherwise set
signatures cannot be validated. In case of no match this function
returns \fBGNUTLS_E_CERTIFICATE_KEY_MISMATCH\fP. This function should
be called once for the client because there is currently no mechanism
to determine which raw public\-key to select for the peer when there
are multiple present. Multiple raw public keys for the server can be
distinghuished by setting the  \fInames\fP .

Note here that  \fIspki\fP is a raw public\-key as defined
in RFC7250. It means that there is no surrounding certificate that
holds the public key and that there is therefore no direct mechanism
to prove the authenticity of this key. The keypair can be used during
a TLS handshake but its authenticity should be established via a
different mechanism (e.g. TOFU or known fingerprint).

The supported formats are basic unencrypted key, PKCS8, PKCS12,
and the openssl format and will be autodetected.

If the raw public\-key and the private key are given in PEM encoding
then the strings that hold their values must be null terminated.

Key usage (as defined by X.509 extension (*********)) can be explicitly
set because there is no certificate structure around the key to define
this value. See for more info \fBgnutls_x509_crt_get_key_usage()\fP.

Note that, this function by default returns zero on success and a
negative value on error. Since 3.5.6, when the flag \fBGNUTLS_CERTIFICATE_API_V2\fP
is set using \fBgnutls_certificate_set_flags()\fP it returns an index
(greater or equal to zero). That index can be used in other functions
to refer to the added key\-pair.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, in case the
key pair does not match \fBGNUTLS_E_CERTIFICATE_KEY_MISMATCH\fP is returned,
in other erroneous cases a different negative error code is returned.
.SH "SINCE"
3.6.6
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
