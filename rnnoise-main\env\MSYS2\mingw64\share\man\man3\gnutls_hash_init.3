.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hash_init" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hash_init \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_hash_init(gnutls_hash_hd_t * " dig ", gnutls_digest_algorithm_t " algorithm ");"
.SH ARGUMENTS
.IP "gnutls_hash_hd_t * dig" 12
is a \fBgnutls_hash_hd_t\fP type
.IP "gnutls_digest_algorithm_t algorithm" 12
the hash algorithm to use
.SH "DESCRIPTION"
This function will initialize an context that can be used to
produce a Message Digest of data.  This will effectively use the
current crypto backend in use by gnutls or the cryptographic
accelerator in use.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
