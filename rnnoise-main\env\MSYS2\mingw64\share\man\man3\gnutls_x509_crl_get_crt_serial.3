.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_get_crt_serial" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_get_crt_serial \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_get_crt_serial(gnutls_x509_crl_t " crl ", unsigned " indx ", unsigned char * " serial ", size_t * " serial_size ", time_t * " t ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
should contain a \fBgnutls_x509_crl_t\fP type
.IP "unsigned indx" 12
the index of the certificate to extract (starting from 0)
.IP "unsigned char * serial" 12
where the serial number will be copied
.IP "size_t * serial_size" 12
initially holds the size of serial
.IP "time_t * t" 12
if non null, will hold the time this certificate was revoked
.SH "DESCRIPTION"
This function will retrieve the serial number of the specified, by
the index, revoked certificate.

Note that this function will have performance issues in large sequences
of revoked certificates. In that case use \fBgnutls_x509_crl_iter_crt_serial()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
