<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html401/loose.dtd">
<html>
<!-- Created on October, 16 2024 by texi2html 1.78a -->
<!--
Written by: <PERSON> <<EMAIL>> (original author)
            <PERSON>  <<EMAIL>>
            <PERSON> <o<PERSON><EMAIL>>
            and many others.
Maintained by: Many creative people.
Send bugs and suggestions to <<EMAIL>>

-->
<head>
<title>GNU libunistring: Index</title>

<meta name="description" content="GNU libunistring: Index">
<meta name="keywords" content="GNU libunistring: Index">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="texi2html 1.78a">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
pre.display {font-family: serif}
pre.format {font-family: serif}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: serif; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: serif; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.roman {font-family:serif; font-weight:normal;}
span.sansserif {font-family:sans-serif; font-weight:normal;}
ul.toc {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">

<table cellpadding="1" cellspacing="1" border="0">
<tr><td valign="middle" align="left">[<a href="libunistring_20.html#SEC85" title="Beginning of this chapter or previous chapter"> &lt;&lt; </a>]</td>
<td valign="middle" align="left">[<a href="libunistring_22.html#INDEX0" title="Next chapter"> &gt;&gt; </a>]</td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Top" title="Cover (top) of document">Top</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Contents" title="Table of contents">Contents</a>]</td>
<td valign="middle" align="left">[<a href="#SEC94" title="Index">Index</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_abt.html#SEC_About" title="About (help)"> ? </a>]</td>
</tr></table>

<hr size="2">
<a name="Index"></a>
<a name="SEC94"></a>
<h1 class="unnumbered"> <a href="libunistring_toc.html#TOC89">Index</a> </h1>

<table><tr><th valign="top">Jump to: &nbsp; </th><td><a href="#SEC94_0" class="summary-letter"><b>A</b></a>
 &nbsp; 
<a href="#SEC94_1" class="summary-letter"><b>B</b></a>
 &nbsp; 
<a href="#SEC94_2" class="summary-letter"><b>C</b></a>
 &nbsp; 
<a href="#SEC94_3" class="summary-letter"><b>D</b></a>
 &nbsp; 
<a href="#SEC94_4" class="summary-letter"><b>E</b></a>
 &nbsp; 
<a href="#SEC94_5" class="summary-letter"><b>F</b></a>
 &nbsp; 
<a href="#SEC94_6" class="summary-letter"><b>G</b></a>
 &nbsp; 
<a href="#SEC94_7" class="summary-letter"><b>H</b></a>
 &nbsp; 
<a href="#SEC94_8" class="summary-letter"><b>I</b></a>
 &nbsp; 
<a href="#SEC94_9" class="summary-letter"><b>J</b></a>
 &nbsp; 
<a href="#SEC94_10" class="summary-letter"><b>L</b></a>
 &nbsp; 
<a href="#SEC94_11" class="summary-letter"><b>M</b></a>
 &nbsp; 
<a href="#SEC94_12" class="summary-letter"><b>N</b></a>
 &nbsp; 
<a href="#SEC94_13" class="summary-letter"><b>O</b></a>
 &nbsp; 
<a href="#SEC94_14" class="summary-letter"><b>P</b></a>
 &nbsp; 
<a href="#SEC94_15" class="summary-letter"><b>R</b></a>
 &nbsp; 
<a href="#SEC94_16" class="summary-letter"><b>S</b></a>
 &nbsp; 
<a href="#SEC94_17" class="summary-letter"><b>T</b></a>
 &nbsp; 
<a href="libunistring_22.html#INDEX0_0" class="summary-letter"><b>U</b></a>
 &nbsp; 
<a href="libunistring_23.html#INDEX1_0" class="summary-letter"><b>V</b></a>
 &nbsp; 
<a href="libunistring_23.html#INDEX1_1" class="summary-letter"><b>W</b></a>
 &nbsp; 
</td></tr></table>
<table border="0" class="index-cp">
<tr><td></td><th align="left">Index Entry</th><th align="left"> Section</th></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_0">A</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX779">ambiguous width</a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC43">Arabic shaping</a></td><td valign="top"><a href="libunistring_8.html#SEC43">8.8 Arabic shaping</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_2.html#IDX14">argument conventions</a></td><td valign="top"><a href="libunistring_2.html#SEC8">2. Conventions</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_16.html#SEC80">autoconf macro</a></td><td valign="top"><a href="libunistring_16.html#SEC80">16.4 Autoconf macro</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_1">B</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC38">bidi class</a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC38">bidirectional category</a></td><td valign="top"><a href="libunistring_8.html#SEC38">8.3 Bidi class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_17.html#SEC82">bidirectional reordering</a></td><td valign="top"><a href="libunistring_17.html#SEC82">17. More advanced functionality</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC52">block</a></td><td valign="top"><a href="libunistring_8.html#SEC52">8.12 Blocks</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#SEC56">boundaries, between grapheme clusters</a></td><td valign="top"><a href="libunistring_10.html#SEC56">10. Grapheme cluster breaks in strings <code>&lt;unigbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_11.html#SEC59">boundaries, between words</a></td><td valign="top"><a href="libunistring_11.html#SEC59">11. Word breaks in strings <code>&lt;uniwbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#SEC56">breaks, grapheme cluster</a></td><td valign="top"><a href="libunistring_10.html#SEC56">10. Grapheme cluster breaks in strings <code>&lt;unigbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#SEC62">breaks, line</a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_11.html#SEC59">breaks, word</a></td><td valign="top"><a href="libunistring_11.html#SEC59">11. Word breaks in strings <code>&lt;uniwbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_16.html#SEC81">bug reports</a></td><td valign="top"><a href="libunistring_16.html#SEC81">16.5 Reporting problems</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_16.html#SEC81">bug tracker</a></td><td valign="top"><a href="libunistring_16.html#SEC81">16.5 Reporting problems</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_2">C</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#SEC6">C string functions</a></td><td valign="top"><a href="libunistring_1.html#SEC6">1.5 &lsquo;<samp>char *</samp>&rsquo; strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC53">C, programming language</a></td><td valign="top"><a href="libunistring_8.html#SEC53">8.13 ISO C and Java syntax</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC54">C-like API</a></td><td valign="top"><a href="libunistring_8.html#SEC54">8.14 Classifications like in ISO C</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC37">canonical combining class</a></td><td valign="top"><a href="libunistring_8.html#SEC37">8.2 Canonical combining class</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#SEC74">case detection</a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#SEC71">case mappings</a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX924"><code>casing_prefix_context_t</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX932"><code>casing_suffix_context_t</code></a></td><td valign="top"><a href="libunistring_14.html#SEC72">14.3 Case mappings of substrings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX10">char, type</a></td><td valign="top"><a href="libunistring_1.html#SEC6">1.5 &lsquo;<samp>char *</samp>&rsquo; strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_19.html#SEC84">char16_t, type</a></td><td valign="top"><a href="libunistring_19.html#SEC84">B. The <code>char32_t</code> problem</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_19.html#SEC84">char32_t, type</a></td><td valign="top"><a href="libunistring_19.html#SEC84">B. The <code>char32_t</code> problem</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#SEC65">combining, Unicode characters</a></td><td valign="top"><a href="libunistring_13.html#SEC65">13.2 Composition of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC17">comparing</a></td><td valign="top"><a href="libunistring_4.html#SEC17">4.3.4 Comparing Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC25">comparing</a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#SEC73">comparing, ignoring case</a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX959">comparing, ignoring case, with collation rules</a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#SEC67">comparing, ignoring normalization</a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#SEC73">comparing, ignoring normalization and case</a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX961">comparing, ignoring normalization and case, with collation rules</a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX897">comparing, ignoring normalization, with collation rules</a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX111">comparing, with collation rules</a></td><td valign="top"><a href="libunistring_4.html#SEC25">4.5.4 Comparing NUL terminated Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX960">comparing, with collation rules, ignoring case</a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX898">comparing, with collation rules, ignoring normalization</a></td><td valign="top"><a href="libunistring_13.html#SEC67">13.4 Normalizing comparisons</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX962">comparing, with collation rules, ignoring normalization and case</a></td><td valign="top"><a href="libunistring_14.html#SEC73">14.4 Case insensitive comparison</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_16.html#IDX987">compiler options</a></td><td valign="top"><a href="libunistring_16.html#SEC78">16.2 Compiler options</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#SEC65">composing, Unicode characters</a></td><td valign="top"><a href="libunistring_13.html#SEC65">13.2 Composition of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC12">converting</a></td><td valign="top"><a href="libunistring_4.html#SEC12">4.2 Elementary string conversions</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX155">converting</a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC16">copying</a></td><td valign="top"><a href="libunistring_4.html#SEC16">4.3.3 Copying Unicode strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC24">copying</a></td><td valign="top"><a href="libunistring_4.html#SEC24">4.5.3 Copying a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC19">counting</a></td><td valign="top"><a href="libunistring_4.html#SEC19">4.3.6 Counting the characters in a Unicode string</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_3">D</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#SEC64">decomposing</a></td><td valign="top"><a href="libunistring_13.html#SEC64">13.1 Decomposition of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_16.html#SEC77">dependencies</a></td><td valign="top"><a href="libunistring_16.html#SEC77">16.1 Installation</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#SEC74">detecting case</a></td><td valign="top"><a href="libunistring_14.html#SEC74">14.5 Case detection</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC20">duplicating</a></td><td valign="top"><a href="libunistring_4.html#SEC20">4.4 Elementary string functions with memory allocation</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC26">duplicating</a></td><td valign="top"><a href="libunistring_4.html#SEC26">4.5.5 Duplicating a NUL terminated Unicode string</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_4">E</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX150"><code>enum iconv_ilseq_handler</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_5">F</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_20.html#SEC92">FDL, GNU Free Documentation License</a></td><td valign="top"><a href="libunistring_20.html#SEC92">C.3 GNU Free Documentation License</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#SEC31">formatted output</a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX782">fullwidth</a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_6">G</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC34">general category</a></td><td valign="top"><a href="libunistring_8.html#SEC34">8.1 General category</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_16.html#IDX991"><code>gl_LIBUNISTRING</code></a></td><td valign="top"><a href="libunistring_16.html#SEC80">16.4 Autoconf macro</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_20.html#SEC86">GPL, GNU General Public License</a></td><td valign="top"><a href="libunistring_20.html#SEC86">C.1 GNU GENERAL PUBLIC LICENSE</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#SEC56">grapheme cluster boundaries</a></td><td valign="top"><a href="libunistring_10.html#SEC56">10. Grapheme cluster breaks in strings <code>&lt;unigbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_10.html#SEC56">grapheme cluster breaks</a></td><td valign="top"><a href="libunistring_10.html#SEC56">10. Grapheme cluster breaks in strings <code>&lt;unigbrk.h&gt;</code></a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_7">H</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_9.html#IDX781">halfwidth</a></td><td valign="top"><a href="libunistring_9.html#SEC55">9. Display width <code>&lt;uniwidth.h&gt;</code></a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_8">I</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC53">identifiers</a></td><td valign="top"><a href="libunistring_8.html#SEC53">8.13 ISO C and Java syntax</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC50">Indic_Conjunct_Break</a></td><td valign="top"><a href="libunistring_8.html#SEC50">8.10.1 Indic conjunct break</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_16.html#IDX986">installation</a></td><td valign="top"><a href="libunistring_16.html#SEC77">16.1 Installation</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#SEC3">internationalization</a></td><td valign="top"><a href="libunistring_1.html#SEC3">1.2 Unicode and Internationalization</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC14">iterating</a></td><td valign="top"><a href="libunistring_4.html#SEC14">4.3.1 Iterating over a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#IDX74">iterating</a></td><td valign="top"><a href="libunistring_4.html#SEC22">4.5.1 Iterating over a NUL terminated Unicode string</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_9">J</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC53">Java, programming language</a></td><td valign="top"><a href="libunistring_8.html#SEC53">8.13 ISO C and Java syntax</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC45">joining group</a></td><td valign="top"><a href="libunistring_8.html#SEC45">8.8.2 Joining group of Arabic characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC43">joining of Arabic characters</a></td><td valign="top"><a href="libunistring_8.html#SEC43">8.8 Arabic shaping</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC44">joining type</a></td><td valign="top"><a href="libunistring_8.html#SEC44">8.8.1 Joining type of Arabic characters</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_10">L</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_20.html#SEC91">LGPL, GNU Lesser General Public License</a></td><td valign="top"><a href="libunistring_20.html#SEC91">C.2 GNU LESSER GENERAL PUBLIC LICENSE</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_20.html#SEC92">License, GNU FDL</a></td><td valign="top"><a href="libunistring_20.html#SEC92">C.3 GNU Free Documentation License</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_20.html#SEC86">License, GNU GPL</a></td><td valign="top"><a href="libunistring_20.html#SEC86">C.1 GNU GENERAL PUBLIC LICENSE</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_20.html#SEC91">License, GNU LGPL</a></td><td valign="top"><a href="libunistring_20.html#SEC91">C.2 GNU LESSER GENERAL PUBLIC LICENSE</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_20.html#SEC85">Licenses</a></td><td valign="top"><a href="libunistring_20.html#SEC85">C. Licenses</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_12.html#SEC62">line breaks</a></td><td valign="top"><a href="libunistring_12.html#SEC62">12. Line breaking <code>&lt;unilbrk.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#SEC4">locale</a></td><td valign="top"><a href="libunistring_1.html#SEC4">1.3 Locale encodings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX7">locale categories</a></td><td valign="top"><a href="libunistring_1.html#SEC4">1.3 Locale encodings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX8">locale encoding</a></td><td valign="top"><a href="libunistring_1.html#SEC4">1.3 Locale encodings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX148">locale encoding</a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#IDX913">locale language</a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_1.html#IDX9">locale, multibyte</a></td><td valign="top"><a href="libunistring_1.html#SEC6">1.5 &lsquo;<samp>char *</samp>&rsquo; strings</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_5.html#IDX149"><code>locale_charset</code></a></td><td valign="top"><a href="libunistring_5.html#SEC30">5. Conversions between Unicode and encodings <code>&lt;uniconv.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#SEC71">lowercasing</a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_11">M</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_16.html#SEC81">mailing list</a></td><td valign="top"><a href="libunistring_16.html#SEC81">16.5 Reporting problems</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC42">mirroring, of Unicode character</a></td><td valign="top"><a href="libunistring_8.html#SEC42">8.7 Mirrored character</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_12">N</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#SEC63">normal forms</a></td><td valign="top"><a href="libunistring_13.html#SEC63">13. Normalization forms (composition and decomposition) <code>&lt;uninorm.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#SEC63">normalizing</a></td><td valign="top"><a href="libunistring_13.html#SEC63">13. Normalization forms (composition and decomposition) <code>&lt;uninorm.h&gt;</code></a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_13">O</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_6.html#SEC31">output, formatted</a></td><td valign="top"><a href="libunistring_6.html#SEC31">6. Output with Unicode strings <code>&lt;unistdio.h&gt;</code></a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_14">P</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC46">properties, of Unicode character</a></td><td valign="top"><a href="libunistring_8.html#SEC46">8.9 Properties</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_15">R</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_15.html#SEC75">regular expression</a></td><td valign="top"><a href="libunistring_15.html#SEC75">15. Regular expressions <code>&lt;uniregex.h&gt;</code></a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_17.html#IDX992">rendering</a></td><td valign="top"><a href="libunistring_17.html#SEC82">17. More advanced functionality</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_2.html#IDX15">return value conventions</a></td><td valign="top"><a href="libunistring_2.html#SEC8">2. Conventions</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_16">S</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_8.html#SEC51">scripts</a></td><td valign="top"><a href="libunistring_8.html#SEC51">8.11 Scripts</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC18">searching, for a character</a></td><td valign="top"><a href="libunistring_4.html#SEC18">4.3.5 Searching for a character in a Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC27">searching, for a character</a></td><td valign="top"><a href="libunistring_4.html#SEC27">4.5.6 Searching for a character in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_4.html#SEC28">searching, for a substring</a></td><td valign="top"><a href="libunistring_4.html#SEC28">4.5.7 Searching for a substring in a NUL terminated Unicode string</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#SEC68">stream, normalizing a</a></td><td valign="top"><a href="libunistring_13.html#SEC68">13.5 Normalization of streams of Unicode characters</a></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_13.html#IDX905"><code>struct uninorm_filter</code></a></td><td valign="top"><a href="libunistring_13.html#SEC68">13.5 Normalization of streams of Unicode characters</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
<tr><th><a name="SEC94_17">T</a></th><td></td><td></td></tr>
<tr><td></td><td valign="top"><a href="libunistring_14.html#SEC71">titlecasing</a></td><td valign="top"><a href="libunistring_14.html#SEC71">14.2 Case mappings of strings</a></td></tr>
<tr><td colspan="3"> <hr></td></tr>
</table>
<table><tr><th valign="top">Jump to: &nbsp; </th><td><a href="#SEC94_0" class="summary-letter"><b>A</b></a>
 &nbsp; 
<a href="#SEC94_1" class="summary-letter"><b>B</b></a>
 &nbsp; 
<a href="#SEC94_2" class="summary-letter"><b>C</b></a>
 &nbsp; 
<a href="#SEC94_3" class="summary-letter"><b>D</b></a>
 &nbsp; 
<a href="#SEC94_4" class="summary-letter"><b>E</b></a>
 &nbsp; 
<a href="#SEC94_5" class="summary-letter"><b>F</b></a>
 &nbsp; 
<a href="#SEC94_6" class="summary-letter"><b>G</b></a>
 &nbsp; 
<a href="#SEC94_7" class="summary-letter"><b>H</b></a>
 &nbsp; 
<a href="#SEC94_8" class="summary-letter"><b>I</b></a>
 &nbsp; 
<a href="#SEC94_9" class="summary-letter"><b>J</b></a>
 &nbsp; 
<a href="#SEC94_10" class="summary-letter"><b>L</b></a>
 &nbsp; 
<a href="#SEC94_11" class="summary-letter"><b>M</b></a>
 &nbsp; 
<a href="#SEC94_12" class="summary-letter"><b>N</b></a>
 &nbsp; 
<a href="#SEC94_13" class="summary-letter"><b>O</b></a>
 &nbsp; 
<a href="#SEC94_14" class="summary-letter"><b>P</b></a>
 &nbsp; 
<a href="#SEC94_15" class="summary-letter"><b>R</b></a>
 &nbsp; 
<a href="#SEC94_16" class="summary-letter"><b>S</b></a>
 &nbsp; 
<a href="#SEC94_17" class="summary-letter"><b>T</b></a>
 &nbsp; 
<a href="libunistring_22.html#INDEX0_0" class="summary-letter"><b>U</b></a>
 &nbsp; 
<a href="libunistring_23.html#INDEX1_0" class="summary-letter"><b>V</b></a>
 &nbsp; 
<a href="libunistring_23.html#INDEX1_1" class="summary-letter"><b>W</b></a>
 &nbsp; 
</td></tr></table>
<hr size="6">
<table cellpadding="1" cellspacing="1" border="0">
<tr><td valign="middle" align="left">[<a href="libunistring_20.html#SEC85" title="Beginning of this chapter or previous chapter"> &lt;&lt; </a>]</td>
<td valign="middle" align="left">[<a href="libunistring_22.html#INDEX0" title="Next chapter"> &gt;&gt; </a>]</td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Top" title="Cover (top) of document">Top</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Contents" title="Table of contents">Contents</a>]</td>
<td valign="middle" align="left">[<a href="#SEC94" title="Index">Index</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_abt.html#SEC_About" title="About (help)"> ? </a>]</td>
</tr></table>
<p>
 <font size="-1">
  This document was generated by <em>Bruno Haible</em> on <em>October, 16 2024</em> using <a href="https://www.nongnu.org/texi2html/"><em>texi2html 1.78a</em></a>.
 </font>
 <br>

</p>
</body>
</html>
