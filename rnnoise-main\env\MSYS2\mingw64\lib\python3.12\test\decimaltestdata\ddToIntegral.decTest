------------------------------------------------------------------------
-- ddToIntegral.decTest -- round Double to integral value             --
-- Copyright (c) IBM Corporation, 2001, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- This set of tests tests the extended specification 'round-to-integral
-- value-exact' operations (from IEEE 854, later modified in 754r).
-- All non-zero results are defined as being those from either copy or
-- quantize, so those are assumed to have been tested extensively
-- elsewhere; the tests here are for integrity, rounding mode, etc.
-- Also, it is assumed the test harness will use these tests for both
-- ToIntegralExact (which does set Inexact) and the fixed-name
-- functions (which do not set Inexact).

-- Note that decNumber implements an earlier definition of toIntegral
-- which never sets Inexact; the decTest operator for that is called
-- 'tointegral' instead of 'tointegralx'.

precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

ddintx001 tointegralx      0     ->  0
ddintx002 tointegralx      0.0   ->  0
ddintx003 tointegralx      0.1   ->  0  Inexact Rounded
ddintx004 tointegralx      0.2   ->  0  Inexact Rounded
ddintx005 tointegralx      0.3   ->  0  Inexact Rounded
ddintx006 tointegralx      0.4   ->  0  Inexact Rounded
ddintx007 tointegralx      0.5   ->  0  Inexact Rounded
ddintx008 tointegralx      0.6   ->  1  Inexact Rounded
ddintx009 tointegralx      0.7   ->  1  Inexact Rounded
ddintx010 tointegralx      0.8   ->  1  Inexact Rounded
ddintx011 tointegralx      0.9   ->  1  Inexact Rounded
ddintx012 tointegralx      1     ->  1
ddintx013 tointegralx      1.0   ->  1  Rounded
ddintx014 tointegralx      1.1   ->  1  Inexact Rounded
ddintx015 tointegralx      1.2   ->  1  Inexact Rounded
ddintx016 tointegralx      1.3   ->  1  Inexact Rounded
ddintx017 tointegralx      1.4   ->  1  Inexact Rounded
ddintx018 tointegralx      1.5   ->  2  Inexact Rounded
ddintx019 tointegralx      1.6   ->  2  Inexact Rounded
ddintx020 tointegralx      1.7   ->  2  Inexact Rounded
ddintx021 tointegralx      1.8   ->  2  Inexact Rounded
ddintx022 tointegralx      1.9   ->  2  Inexact Rounded
-- negatives
ddintx031 tointegralx     -0     -> -0
ddintx032 tointegralx     -0.0   -> -0
ddintx033 tointegralx     -0.1   -> -0  Inexact Rounded
ddintx034 tointegralx     -0.2   -> -0  Inexact Rounded
ddintx035 tointegralx     -0.3   -> -0  Inexact Rounded
ddintx036 tointegralx     -0.4   -> -0  Inexact Rounded
ddintx037 tointegralx     -0.5   -> -0  Inexact Rounded
ddintx038 tointegralx     -0.6   -> -1  Inexact Rounded
ddintx039 tointegralx     -0.7   -> -1  Inexact Rounded
ddintx040 tointegralx     -0.8   -> -1  Inexact Rounded
ddintx041 tointegralx     -0.9   -> -1  Inexact Rounded
ddintx042 tointegralx     -1     -> -1
ddintx043 tointegralx     -1.0   -> -1  Rounded
ddintx044 tointegralx     -1.1   -> -1  Inexact Rounded
ddintx045 tointegralx     -1.2   -> -1  Inexact Rounded
ddintx046 tointegralx     -1.3   -> -1  Inexact Rounded
ddintx047 tointegralx     -1.4   -> -1  Inexact Rounded
ddintx048 tointegralx     -1.5   -> -2  Inexact Rounded
ddintx049 tointegralx     -1.6   -> -2  Inexact Rounded
ddintx050 tointegralx     -1.7   -> -2  Inexact Rounded
ddintx051 tointegralx     -1.8   -> -2  Inexact Rounded
ddintx052 tointegralx     -1.9   -> -2  Inexact Rounded
-- next two would be NaN using quantize(x, 0)
ddintx053 tointegralx    10E+60  -> 1.0E+61
ddintx054 tointegralx   -10E+60  -> -1.0E+61

-- numbers around precision
ddintx060 tointegralx '56267E-17'   -> '0'      Inexact Rounded
ddintx061 tointegralx '56267E-5'    -> '1'      Inexact Rounded
ddintx062 tointegralx '56267E-2'    -> '563'    Inexact Rounded
ddintx063 tointegralx '56267E-1'    -> '5627'   Inexact Rounded
ddintx065 tointegralx '56267E-0'    -> '56267'
ddintx066 tointegralx '56267E+0'    -> '56267'
ddintx067 tointegralx '56267E+1'    -> '5.6267E+5'
ddintx068 tointegralx '56267E+9'    -> '5.6267E+13'
ddintx069 tointegralx '56267E+10'   -> '5.6267E+14'
ddintx070 tointegralx '56267E+11'   -> '5.6267E+15'
ddintx071 tointegralx '56267E+12'   -> '5.6267E+16'
ddintx072 tointegralx '56267E+13'   -> '5.6267E+17'
ddintx073 tointegralx '1.23E+96'    -> '1.23E+96'
ddintx074 tointegralx '1.23E+384'   -> #47fd300000000000  Clamped

ddintx080 tointegralx '-56267E-10'  -> '-0'      Inexact Rounded
ddintx081 tointegralx '-56267E-5'   -> '-1'      Inexact Rounded
ddintx082 tointegralx '-56267E-2'   -> '-563'    Inexact Rounded
ddintx083 tointegralx '-56267E-1'   -> '-5627'   Inexact Rounded
ddintx085 tointegralx '-56267E-0'   -> '-56267'
ddintx086 tointegralx '-56267E+0'   -> '-56267'
ddintx087 tointegralx '-56267E+1'   -> '-5.6267E+5'
ddintx088 tointegralx '-56267E+9'   -> '-5.6267E+13'
ddintx089 tointegralx '-56267E+10'  -> '-5.6267E+14'
ddintx090 tointegralx '-56267E+11'  -> '-5.6267E+15'
ddintx091 tointegralx '-56267E+12'  -> '-5.6267E+16'
ddintx092 tointegralx '-56267E+13'  -> '-5.6267E+17'
ddintx093 tointegralx '-1.23E+96'   -> '-1.23E+96'
ddintx094 tointegralx '-1.23E+384'  -> #c7fd300000000000  Clamped

-- subnormal inputs
ddintx100 tointegralx        1E-299 -> 0  Inexact Rounded
ddintx101 tointegralx      0.1E-299 -> 0  Inexact Rounded
ddintx102 tointegralx     0.01E-299 -> 0  Inexact Rounded
ddintx103 tointegralx        0E-299 -> 0

-- specials and zeros
ddintx120 tointegralx 'Inf'       ->  Infinity
ddintx121 tointegralx '-Inf'      -> -Infinity
ddintx122 tointegralx   NaN       ->  NaN
ddintx123 tointegralx  sNaN       ->  NaN  Invalid_operation
ddintx124 tointegralx     0       ->  0
ddintx125 tointegralx    -0       -> -0
ddintx126 tointegralx     0.000   ->  0
ddintx127 tointegralx     0.00    ->  0
ddintx128 tointegralx     0.0     ->  0
ddintx129 tointegralx     0       ->  0
ddintx130 tointegralx     0E-3    ->  0
ddintx131 tointegralx     0E-2    ->  0
ddintx132 tointegralx     0E-1    ->  0
ddintx133 tointegralx     0E-0    ->  0
ddintx134 tointegralx     0E+1    ->  0E+1
ddintx135 tointegralx     0E+2    ->  0E+2
ddintx136 tointegralx     0E+3    ->  0E+3
ddintx137 tointegralx     0E+4    ->  0E+4
ddintx138 tointegralx     0E+5    ->  0E+5
ddintx139 tointegralx    -0.000   -> -0
ddintx140 tointegralx    -0.00    -> -0
ddintx141 tointegralx    -0.0     -> -0
ddintx142 tointegralx    -0       -> -0
ddintx143 tointegralx    -0E-3    -> -0
ddintx144 tointegralx    -0E-2    -> -0
ddintx145 tointegralx    -0E-1    -> -0
ddintx146 tointegralx    -0E-0    -> -0
ddintx147 tointegralx    -0E+1    -> -0E+1
ddintx148 tointegralx    -0E+2    -> -0E+2
ddintx149 tointegralx    -0E+3    -> -0E+3
ddintx150 tointegralx    -0E+4    -> -0E+4
ddintx151 tointegralx    -0E+5    -> -0E+5
-- propagating NaNs
ddintx152 tointegralx   NaN808    ->  NaN808
ddintx153 tointegralx  sNaN080    ->  NaN80  Invalid_operation
ddintx154 tointegralx  -NaN808    -> -NaN808
ddintx155 tointegralx -sNaN080    -> -NaN80  Invalid_operation
ddintx156 tointegralx  -NaN       -> -NaN
ddintx157 tointegralx -sNaN       -> -NaN    Invalid_operation

-- examples
rounding:    half_up
ddintx200 tointegralx     2.1    -> 2            Inexact Rounded
ddintx201 tointegralx   100      -> 100
ddintx202 tointegralx   100.0    -> 100          Rounded
ddintx203 tointegralx   101.5    -> 102          Inexact Rounded
ddintx204 tointegralx  -101.5    -> -102         Inexact Rounded
ddintx205 tointegralx   10E+5    -> 1.0E+6
ddintx206 tointegralx  7.89E+77  -> 7.89E+77
ddintx207 tointegralx   -Inf     -> -Infinity


-- all rounding modes
rounding:    half_even
ddintx210 tointegralx     55.5   ->  56  Inexact Rounded
ddintx211 tointegralx     56.5   ->  56  Inexact Rounded
ddintx212 tointegralx     57.5   ->  58  Inexact Rounded
ddintx213 tointegralx    -55.5   -> -56  Inexact Rounded
ddintx214 tointegralx    -56.5   -> -56  Inexact Rounded
ddintx215 tointegralx    -57.5   -> -58  Inexact Rounded

rounding:    half_up

ddintx220 tointegralx     55.5   ->  56  Inexact Rounded
ddintx221 tointegralx     56.5   ->  57  Inexact Rounded
ddintx222 tointegralx     57.5   ->  58  Inexact Rounded
ddintx223 tointegralx    -55.5   -> -56  Inexact Rounded
ddintx224 tointegralx    -56.5   -> -57  Inexact Rounded
ddintx225 tointegralx    -57.5   -> -58  Inexact Rounded

rounding:    half_down

ddintx230 tointegralx     55.5   ->  55  Inexact Rounded
ddintx231 tointegralx     56.5   ->  56  Inexact Rounded
ddintx232 tointegralx     57.5   ->  57  Inexact Rounded
ddintx233 tointegralx    -55.5   -> -55  Inexact Rounded
ddintx234 tointegralx    -56.5   -> -56  Inexact Rounded
ddintx235 tointegralx    -57.5   -> -57  Inexact Rounded

rounding:    up

ddintx240 tointegralx     55.3   ->  56  Inexact Rounded
ddintx241 tointegralx     56.3   ->  57  Inexact Rounded
ddintx242 tointegralx     57.3   ->  58  Inexact Rounded
ddintx243 tointegralx    -55.3   -> -56  Inexact Rounded
ddintx244 tointegralx    -56.3   -> -57  Inexact Rounded
ddintx245 tointegralx    -57.3   -> -58  Inexact Rounded

rounding:    down

ddintx250 tointegralx     55.7   ->  55  Inexact Rounded
ddintx251 tointegralx     56.7   ->  56  Inexact Rounded
ddintx252 tointegralx     57.7   ->  57  Inexact Rounded
ddintx253 tointegralx    -55.7   -> -55  Inexact Rounded
ddintx254 tointegralx    -56.7   -> -56  Inexact Rounded
ddintx255 tointegralx    -57.7   -> -57  Inexact Rounded

rounding:    ceiling

ddintx260 tointegralx     55.3   ->  56  Inexact Rounded
ddintx261 tointegralx     56.3   ->  57  Inexact Rounded
ddintx262 tointegralx     57.3   ->  58  Inexact Rounded
ddintx263 tointegralx    -55.3   -> -55  Inexact Rounded
ddintx264 tointegralx    -56.3   -> -56  Inexact Rounded
ddintx265 tointegralx    -57.3   -> -57  Inexact Rounded

rounding:    floor

ddintx270 tointegralx     55.7   ->  55  Inexact Rounded
ddintx271 tointegralx     56.7   ->  56  Inexact Rounded
ddintx272 tointegralx     57.7   ->  57  Inexact Rounded
ddintx273 tointegralx    -55.7   -> -56  Inexact Rounded
ddintx274 tointegralx    -56.7   -> -57  Inexact Rounded
ddintx275 tointegralx    -57.7   -> -58  Inexact Rounded

-- Int and uInt32 edge values for testing conversions
ddintx300 tointegralx -2147483646  -> -2147483646
ddintx301 tointegralx -2147483647  -> -2147483647
ddintx302 tointegralx -2147483648  -> -2147483648
ddintx303 tointegralx -2147483649  -> -2147483649
ddintx304 tointegralx  2147483646  ->  2147483646
ddintx305 tointegralx  2147483647  ->  2147483647
ddintx306 tointegralx  2147483648  ->  2147483648
ddintx307 tointegralx  2147483649  ->  2147483649
ddintx308 tointegralx  4294967294  ->  4294967294
ddintx309 tointegralx  4294967295  ->  4294967295
ddintx310 tointegralx  4294967296  ->  4294967296
ddintx311 tointegralx  4294967297  ->  4294967297

