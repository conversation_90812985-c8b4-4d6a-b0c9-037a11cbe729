.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hash_squeeze" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hash_squeeze \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_hash_squeeze(gnutls_hash_hd_t " handle ", void * " output ", size_t " length ");"
.SH ARGUMENTS
.IP "gnutls_hash_hd_t handle" 12
a \fBgnutls_hash_hd_t\fP
.IP "void * output" 12
destination to store the output; must be equal to or larger than  \fIlength\fP 
.IP "size_t length" 12
length of  \fIoutput\fP 
.SH "DESCRIPTION"
This function will extract digest output of  \fIlength\fP bytes. The  \fIhandle\fP must
be initialized with \fBgnutls_hash_init()\fP as an extended output function (XOF),
such as \fBGNUTLS_DIG_SHAKE_128\fP or \fBGNUTLS_DIG_SHAKE_256\fP.

This function can be called multiple times. To reset the state of  \fIhandle\fP ,
call \fBgnutls_hash_deinit()\fP with \fBNULL\fP as the digest argument.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success; negative error code otherwise.
.SH "SINCE"
3.8.6
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
