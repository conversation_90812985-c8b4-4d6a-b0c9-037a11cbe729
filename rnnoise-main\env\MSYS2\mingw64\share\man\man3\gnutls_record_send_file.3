.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_send_file" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_send_file \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_send_file(gnutls_session_t " session ", int " fd ", off_t * " offset ", size_t " count ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "int fd" 12
file descriptor from which to read data.
.IP "off_t * offset" 12
Is relative to file offset, denotes the starting location for
reading.  after function returns, it point to position following
last read byte.
.IP "size_t count" 12
is the length of the data in bytes to be read from file and send.
.SH "DESCRIPTION"
This function sends data from  \fIfd\fP . If KTLS (kernel TLS) is enabled, it will
use the \fBsendfile()\fP system call to avoid overhead of copying data between user
space and the kernel. Otherwise, this functionality is merely emulated by
calling \fBread()\fP and \fBgnutls_record_send()\fP. If this implementation is
suboptimal, check whether KTLS is enabled using
\fBgnutls_transport_is_ktls_enabled()\fP.

If  \fIoffset\fP is NULL then file offset is incremented by number of bytes send,
otherwise file offset remains unchanged.
.SH "RETURNS"
The number of bytes sent, or a negative error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
