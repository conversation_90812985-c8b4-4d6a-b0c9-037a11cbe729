.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_cipher_suite_get_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_cipher_suite_get_name \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_cipher_suite_get_name(gnutls_kx_algorithm_t " kx_algorithm ", gnutls_cipher_algorithm_t " cipher_algorithm ", gnutls_mac_algorithm_t " mac_algorithm ");"
.SH ARGUMENTS
.IP "gnutls_kx_algorithm_t kx_algorithm" 12
is a Key exchange algorithm
.IP "gnutls_cipher_algorithm_t cipher_algorithm" 12
is a cipher algorithm
.IP "gnutls_mac_algorithm_t mac_algorithm" 12
is a MAC algorithm
.SH "DESCRIPTION"
This function returns the ciphersuite name under TLS1.2 or earlier
versions when provided with individual algorithms. The full cipher suite
name must be prepended by TLS or SSL depending of the protocol in use.

To get a description of the current ciphersuite across versions, it
is recommended to use \fBgnutls_session_get_desc()\fP.
.SH "RETURNS"
a string that contains the name of a TLS cipher suite,
specified by the given algorithms, or \fBNULL\fP.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
