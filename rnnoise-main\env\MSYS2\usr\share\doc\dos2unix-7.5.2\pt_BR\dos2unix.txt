NOME
    dos2unix - Conversor de formato de arquivo texto de DOS/Mac para Unix e
    vice-versa

SINOPSE
        dos2unix [opçõ<PERSON>] [ARQUIVO ...] [-n ARQENT ARQSAÍDA ...]
        unix2dos [opções] [ARQUIVO ...] [-n ARQENT ARQSAÍDA ...]

DESCRIÇÃO
    O pacote Dos2unix inclui utilitários de "dos2unix" e "unix2dos" para
    converter arquivos texto nos formatos DOS ou Mac para formato Unix e
    vice-versa.

    Em arquivos texto DOS/Windows uma quebra de linha, também conhecida como
    nova linha, é uma combinação de dois caracteres: um Carriage Return (CR)
    seguido por um Line Feed (LF). Em arquivos texto do Unix uma quebra de
    linha é um único caractere: o Line Feed (LF). Em arquivos texto do Mac,
    anteriores ao Mac OS X, uma quebra de linha era um único caractere
    Carriage Return (CR). Hoje em dia, Mac OS usa quebras de linha no estilo
    do Unix (LF).

    Além das quebras de linhas, Dos2unix também pode converter as
    codificações de arquivos. Algumas poucas páginas podem ser convertidos
    para Latin-1 para Unix. E arquivos Unicode do Windows (UTF-16) podem ser
    convertidos para arquivos Unicode do Unix (UTF-8).

    Arquivos binários são ignorados automaticamente, a menos que a conversão
    seja forçada.

    Arquivos não regulares, tais como diretórios e FIFOs, são ignorados
    automaticamente.

    Ligações simbólicas e seus alvos são por padrão mantidas intocáveis.
    Ligações simbólicas podem opcionalmente ser substituídas, ou a saída
    pode ser escrita para o alvo das ligações simbólicas. Não há suporte às
    ligações simbólicas do Windows.

    Dos2unix foi modelado seguindo dos2unix do SunOS/Solaris. Há uma
    diferença importante em relação à versão original do SunOS/Solaris. Essa
    versão faz conversão no-lugar (modo de arquivo antigo) por padrão,
    enquanto a versão original do SunOS/Solaris fornecia suporte apenas a
    conversão pareada (modo de novo arquivo). Veja também as opções "-o" e
    "-n". Uma outra diferença é que a versão SunOS/Solaris usa, por padrão,
    a conversão de modo do *iso* enquanto esta versão usa o do *ascii*.

OPÇÕES
    --  Trata as opções seguintes como nomes de arquivos. Use essa opção se
        você quiser converter arquivos cujos nomes iniciam com um traço. Por
        exemplo, para converter um arquivo chamado "foo", você pode usar
        este comando:

            dos2unix -- -foo

        Ou em modo de novo arquivo:

            dos2unix -n -- -foo saída.txt

    --allow-chown
        Permite alteração da propriedade de arquivo no modo de arquivo
        antigo.

        Quando esta opção é usada, a conversão não será abortada quando a
        propriedade do usuário e/ou do grupo do arquivo original não puder
        ser preservada no modo de arquivo antigo. A conversão continuará e o
        arquivo convertido receberá a mesma propriedade nova como se tivesse
        convertido no modo de novo arquivo. Veja também as opções "-o" e
        "-n". Esta opção só está disponível se o dos2unix oferecer suporte a
        preservação da propriedade do usuário e do grupo de arquivos.

    -ascii
        Default conversion mode. See also section CONVERSION MODES.

    -iso
        Conversão entre conjunto de caractere do DOS e ISO-8859-1. Veja
        também a seção MODOS DE CONVERSÃO.

    -1252
        Usa a página de código 1252 do Windows (Europa ocidental).

    -437
        Usa a página de código 437 do DOS (EUA). Essa é a página de código
        padrão usada para conversão ISO.

    -850
        Usa a página de código 850 do DOS (Europa ocidental).

    -860
        Usa a página de código 860 do DOS (Português).

    -863
        Usa a página de código 863 do DOS (Francês do Canadá).

    -865
        Usa a página de código 865 do DOS (Nórdico).

    -7  Converte caracteres de 8 bits para espaço de 7 bits.

    -b, --keep-bom
        Mantém marca de ordem de bytes (BOM). Quando o arquivo de entrada
        possuir um BOM, escreve um BOM no arquivo de saída. Esse é o
        comportamento padrão ao converter para quebras de linha do DOS. Veja
        também a opção "-r".

    -c, --convmode MODOCONV
        Define o modo de conversão, sendo MODOCONV um dentre: *ascii*,
        *7bit*, *iso*, *mac* com ascii sendo o padrão.

    -D, --display-enc CODIFICAÇÃO
        Define a codificação do texto exibido, sendo CODIFICAÇÃO um dentre:
        *ansi*, *unicode*, *utf8*, *utf8bom* com ansi sendo o padrão.

        Essa opção está disponível apenas no dos2unix para Windows com
        suporte a nome de arquivo em Unicode. Essa opção não possui efeito
        nos nomes de arquivos lidos e escritos, apenas em como eles são
        exibidos.

        Há vários métodos para exibir texto em um console Windows baseado na
        codificação do texto. Todos eles possuem suas próprias vantagens e
        desvantagens.

        ansi
            O método padrão do dos2unix é usar o texto codificado em ANSI. A
            sua vantagem é a compatibilidade reversa. Ele funciona com
            fontes raster e TrueType. Em algumas regiões você pode precisar
            alterar a página de código OEM do DOS para ANSI do sistema
            Windows usando o comando "chcp", porque dos2unix usa a página de
            código do sistema Windows.

            A desvantagem do ansi é que nomes de arquivos internacionais com
            caracteres fora a página de código padrão do sistema não são
            exibidos apropriadamente. Você verá um sinal de interrogação, ou
            um símbolo incorreto. Quando você não utiliza nomes de arquivos
            estrangeiros, esse método funciona bem.

        unicode, unicodebom
            A vantagem da codificação do unicode (o nome Windows para
            UTF-16) é que o texto é normalmente exibido apropriadamente. Não
            há necessidade para alterar a página de código ativa. Você pode
            precisar definir a fonte do console para uma fonte TrueType para
            que caracteres internacionais sejam exibidos apropriadamente.
            Quando um caractere não está incluído na fonte TrueType,
            geralmente você vê um pequeno quadrado, algumas vezes com um
            sinal de interrogação nele.

            Quando você usa o console ConEmu todo texto é exibido
            apropriadamente, porque o ConEmu seleciona automaticamente um
            fonte boa.

            A desvantagem do unicode é que ele não é compatível com ASCII. A
            saída não é fácil de lidar quando você o redireciona para um
            outro programa.

            Quando o método <unicodebom> é usado, o texto Unicode será
            precedido com um BOM (Byte Order Mark, ou marca de ordem de
            byte). Um BOM é necessário para o redirecionamento, ou "piping",
            correto no PowerShell.

        utf8, utf8bom
            A vantagem do utf8 é que ele é compatível com ASCII. Você
            precisa definir a fonte do console para uma fonte TrueType. Com
            uma fonte TrueType, o texto é exibido similar a uma codificação
            "unicode".

            A desvantagem é que quando você usa a fonte "raster" padrão,
            caracteres não-ASCII são exibidos incorretamente. Não apenas
            nomes de arquivos unicode, mas também mensagens traduzidas ficam
            ilegíveis. No Windows configurado para uma região leste da Ásia,
            você pode ver muitas falhas no console quando as mensagens são
            exibidas.

            Em um console ConEmu, o método de codificação utf8 funciona bem.

            Quando o método <utf8bom> é usado, o texto UTF-8 será precedido
            com um BOM (Byte Order Mark, ou marca de ordem de byte). Um BOM
            é necessário para o redirecionamento, ou "piping", correto no
            PowerShell.

        A codificação padrão pode ser alterada com a variável de ambiente
        DOS2UNIX_DISPLAY_ENC definindo-a para "unicode", "unicodebom",
        "utf8" ou "utf8bom".

    -e, --add-eol
        Add a line break to the last line if there isn't one. This works for
        every conversion.

        A file converted from DOS to Unix format may lack a line break on
        the last line. There are text editors that write text files without
        a line break on the last line. Some Unix programs have problems
        processing these files, because the POSIX standard defines that
        every line in a text file must end with a terminating newline
        character. For instance concatenating files may not give the
        expected result.

    -f, --force
        Força a conversão de arquivos binários.

    -gb, --gb18030
        No Windows, arquivos UTF-16 são convertidos, por padrão, para UTF-8,
        independentemente da localização definida. Use esta opção para
        converter arquivos UTF-16 para GB18030. Essa opção está disponível
        apenas no Windows. Veja também a seção GB18030.

    -h, --help
        Exibe ajuda e sai.

    -i[OPÇÕES], --info[=OPÇÕES] ARQUIVO ...
        Exibe informação do arquivo. Nenhuma conversão é feita.

        A seguinte informação é exibida, nesta ordem: número de quebras de
        linha do DOS, número de quebras de linha do Unix, número de quebras
        de linha do Mac, marca de ordem de byte, "text" ou "binary", nome de
        arquivo.

        Exemplo de saída:

             6       0       0  no_bom    text    dos.txt
             0       6       0  no_bom    text    unix.txt
             0       0       6  no_bom    text    mac.txt
             6       6       6  no_bom    text    mixed.txt
            50       0       0  UTF-16LE  text    utf16le.txt
             0      50       0  no_bom    text    utf8unix.txt
            50       0       0  UTF-8     text    utf8dos.txt
             2     418     219  no_bom    binary  dos2unix.exe

        Note que em algumas vezes um arquivo binário pode ser confundido com
        um arquivo texto. Veja também a opção "-s".

        If in addition option "-e" or "--add-eol" is used also the type of
        the line break of the last line is printed, or "noeol" if there is
        none.

        Exemplo de saída:

             6       0       0  no_bom    text   dos     dos.txt
             0       6       0  no_bom    text   unix    unix.txt
             0       0       6  no_bom    text   mac     mac.txt
             1       0       0  no_bom    text   noeol   noeol_dos.txt

        Opcionalmente, opções extra podem ser definidas para alterar a
        saída. Uma ou mais opções podem ser adicionadas.

        0   Exibe as linhas de informações de arquivo seguido por um
            caractere nulo em vez de um caractere de nova linha. Isso
            habilita interpretação correta de nomes de arquivo com espaços
            ou aspas quando a opção c é usada. Use essa opção na combinação
            com opções -0 ou "--null" do xargs(1).

        d   Exibe o número de quebras de linhas do DOS.

        u   Exibe o número de quebras de linhas do Unix.

        m   Exibe o número de quebras de linhas do Mac.

        b   Exibe a marca de ordem de byte.

        t   Exibe se arquivo é texto ou binário.

        e   Print the type of the line break of the last line, or "noeol" if
            there is none.

        c   Exibe apenas os arquivos que seriam convertidos.

            Com a opção "c", dos2unix vai exibir apenas os arquivos que
            contêm quebras de linha do DOS, unix2dos vai exibir apenas os
            nomes de arquivos que contêm quebras de linha do Unix.

            If in addition option "-e" or "--add-eol" is used also the files
            that lack a line break on the last line will be printed.

        h   Exibe um cabeçalho.

        p   Mostra nomes de arquivos sem caminho.

        Exemplos:

        Mostra informação sobre todos os arquivos *.txt:

            dos2unix -i *.txt

        Mostra apenas o número de quebras de linha DOS e Unix:

            dos2unix -idu *.txt

        Mostra apenas a marca de ordem de byte:

            dos2unix --info=b *.txt

        Lista os arquivos que possuem quebras de linha do DOS:

            dos2unix -ic *.txt

        Lista os arquivos que possuem quebras de linha do Unix:

            unix2dos -ic *.txt

        List the files that have DOS line breaks or lack a line break on the
        last line:

            dos2unix -e -ic *.txt

        Converte apenas arquivos que possuem quebras de linha do DOS e não
        altera outros arquivos:

            dos2unix -ic0 *.txt | xargs -0 dos2unix

        Localiza arquivos de texto que possuam quebras de linha do DOS:

            find -name '*.txt' -print0 | xargs -0 dos2unix -ic

    -k, --keepdate
        Mantém a marca da data do arquivo de saída igual ao do arquivo de
        entrada.

    -L, --license
        Exibe a licença do programa.

    -l, --newline
        Adiciona nova linha adicional.

        dos2unix: Apenas quebras de linha do DOS são alteradas para duas
        quebras de linha do Unix. No modo Mac, apenas quebras de linha do
        Mac são alterados para duas quebras de linha do Unix.

        unix2dos: Apenas quebras de linha do Unix são alteradas para duas
        quebras de linha do DOS. No modo Mac, quebras de linha do Unix são
        alteradas para duas quebras de linha do Mac.

    -m, --add-bom
        Escreve uma marca de ordem de byte (BOM) no arquivo de saída. Por
        padrão, um BOM UTF-8 é escrito.

        Quando o arquivo de entrada é UTF-16, e a opção "-u" é usada, um BOM
        UTF-16 será escrito.

        Nunca use essa opção quando a codificação de saída é outra além de
        UTF-8, UTF-16 ou GB18030. Veja também a seção UNICODE.

    -n, --newfile ARQENT ARQSAÍDA ...
        Modo de novo arquivo. Converte o arquivo ARQENT e escreve a saída
        para o arquivo ARQSAÍDA. Os nomes de arquivos devem ser fornecidos
        em pares e nome coringa *não* deveriam ser usados ou você *vai*
        perder seus arquivos.

        A pessoa que começa a conversão em modo de novo arquivo (pareado)
        será o dono do arquivo convertido. As permissões de leitura/escrita
        do novo arquivo serão as permissões do arquivo original menos a
        umask(1) da pessoa que executa a conversão.

    --no-allow-chown
        Não permite alteração da propriedade do arquivo no modo de arquivo
        antigo (padrão).

        Aborta a conversão quando a propriedade do usuário e/ou do grupo do
        arquivo original não puder ser preservada no modo de arquivo antigo.
        Veja também as opções "-o" e "-n". Esta opção só está disponível se
        o dos2unix oferecer suporte à preservação da propriedade do usuário
        e do grupo de arquivos.

    --no-add-eol
        Do not add a line break to the last line if there isn't one.

    -O, --to-stdout
        Write to standard output, like a Unix filter. Use option "-o" to go
        back to old file (in-place) mode.

        Combined with option "-e" files can be properly concatenated. No
        merged last and first lines, and no Unicode byte order marks in the
        middle of the concatenated file. Example:

            dos2unix -e -O file1.txt file2.txt > output.txt

    -o, --oldfile FILE ...
        Modo de arquivo antigo. Converte o arquivo ARQUIVO e o sobrescreve
        com a saída. O programa, por padrão, executa neste modo. Nomes
        coringas podem ser usados.

        No modo de arquivo antigo (no-lugar) o arquivo convertido recebe no
        mesmo dono, grupo e permissões de leitura/escrita que o arquivo
        original. Também, quando o arquivo é convertido por outro usuário
        que tenha permissões de escrita no arquivo (ex.: usuário root). A
        conversão será abortada quando não for possível preservar os valores
        originais. Alteração do dono pode significar que o dono original não
        é mais capaz de ler o arquivo. Alteração do grupo pode ser um risco
        para a segurança, pois o arquivo pode ficar legível para pessoas
        cujo acesso não é desejado. Preservação do dono, grupo e permissões
        de leitura/escrita tem suporte apenas no Unix.

        Para verificar se dos2unix oferece suporte à preservação da
        propriedade de usuário e de grupo de arquivos, digite "dos2unix -V".

        A conversão sempre é feita através de um arquivo temporário. Quando
        um erro ocorre no meio da conversão, o arquivo temporário é excluído
        e o arquivo original permanece intacto. Quando a conversão é bem
        sucedida, o arquivo original é substituído pelo arquivo temporário.
        Você pode ter permissão de gravação no arquivo original, mas nenhuma
        permissão para colocar a mesma propriedade de usuário e/ou de grupo
        no arquivo temporário como o arquivo original. Isso significa que
        você não consegue preservar a propriedade de usuário e/ou de grupo
        do arquivo original. Neste caso, você pode usar a opção
        "-allow-chown" para continuar com a conversão:

            dos2unix --allow-chown foo.txt

        Outra opção é usar o novo modo de arquivo:

            dos2unix -n foo.txt foo.txt

        A vantagem da opção "--allow-chown" é que você pode usar coringas e
        as informações de propriedade serão preservadas quando possível.

    -q, --quiet
        Modo quieto. Suprime todos os avios e mensagens. O valor retornado é
        zero. Exceto quando opções de linha de comando erradas forem usadas.

    -r, --remove-bom
        Remove marca de ordem de bytes (BOM). Não escreve um BOM no arquivo
        de saída. Esse é o comportamento padrão ao converter para quebras de
        linha Unix. Veja também a opção "-b".

    -s, --safe
        Ignora arquivo binários (padrão).

        A ação de ignorar arquivos binários é feita para evitar equívocos
        acidentais. Fique ciente de que a detecção de arquivos binários não
        é 100% à prova de erros. Arquivos de entrada são analisados por
        símbolos binários que, geralmente, não são encontrados em arquivos
        textos. É possível que um arquivo binário contenha apenas caracteres
        de texto normais. tal arquivo binário pode ser acidentalmente visto
        como um arquivo de texto.

    -u, --keep-utf16
        Mantém a codificação UTF-16 original do arquivo de entrada. O
        arquivo de saída será escrito na mesma codificação UTF-16, em little
        ou big endian, como o arquivo de entrada. Isso evita transformação
        para UTF-8. Como consequência, um BOM UTF-16 será escrito. Essa
        opção pode ser desabilitada com a opção "-ascii".

    -ul, --assume-utf16le
        Presume que o formato de arquivo de entrada é UTF-16LE.

        Quando há uma marca de ordem de byte no arquivo de entrada, esta tem
        prioridade sobre essa opção.

        Quando você fizer uma presunção equivocada (o arquivo de entrada não
        estava no formato UTF-16LE) e a conversão funcionar, você terá um
        arquivo de saída UTF-8 com texto errado. Você pode desfazer a
        conversão errada com iconv(1) pela conversão do arquivo de saída
        UTF-8 de volta para UTF-16LE. Isso vai trazer de volta o arquivo
        para o original.

        A presunção de UTF-16LE funciona como um *modo de conversão*. Ao
        alternara o modo *ascii* padrão, a presunção de UTF-16LE é
        desativada.

    -ub, --assume-utf16be
        Presume que o formato de arquivo de entrada é UTF-16BE.

        Essa opção funciona o mesmo que a opção "-ul".

    -v, --verbose
        Exibe mensagens detalhadas. Informação extra é exibida sobre marcas
        de ordem de byte e a quantidade de quebras de linha convertidas.

    -F, --follow-symlink
        Segue ligações simbólicas e converte os alvos.

    -R, --replace-symlink
        Substitui ligações simbólicas com arquivos convertidos (arquivos
        alvo originais permanecem inalterados).

    -S, --skip-symlink
        Mentém ligações simbólicas e alvos inalterados (padrão).

    -V, --version
        Exibe informação da versão e sai.

MODO MAC
    By default line breaks are converted from DOS to Unix and vice versa.
    Mac line breaks are not converted.

    No modo Mac, quebras de linha são convertidas de Mac para Unix e
    vice-versa. Quebras de linha do DOS não são alteradas.

    Para executar no modo Mac, use a opção de linha de comando "-c mac" ou
    use os comandos "mac2unix" ou "unix2mac".

MODOS DE CONVERSÃO
    ascii
        This is the default conversion mode. This mode is for converting
        ASCII and ASCII-compatible encoded files, like UTF-8. Enabling ascii
        mode disables 7bit and iso mode.

        If dos2unix has UTF-16 support, UTF-16 encoded files are converted
        to the current locale character encoding on POSIX systems and to
        UTF-8 on Windows. Enabling ascii mode disables the option to keep
        UTF-16 encoding ("-u") and the options to assume UTF-16 input ("-ul"
        and "-ub"). To see if dos2unix has UTF-16 support type "dos2unix
        -V". See also section UNICODE.

    7bit
        Neste modo todos os caracteres não-ASCII de 8 bits (com valores
        entre 128 e 255) são convertidos para um espaço de 7 bits.

    iso Caracteres são convertidos entre um conjunto de caracteres do DOS
        (página de código) e conjunto de caracteres ISO-8859-1 (Latin-1) no
        Unix. Caracteres de DOS sem um equivalente ISO-8859-1, para os quais
        a conversão não é possível, são convertidos para um ponto. O mesmo
        vale para caracteres ISO-8859-1 sem a contraparte DOS.

        Quando apenas a opção "-iso" for usada, dos2unix vai tentar
        determinar a página de código ativa. Quando isso não for possível,
        dos2unix vai usar a página de código padrão CP437, a qual é usada
        principalmente nos EUA. Para forçar uma página de código específica,
        use as opções -437 (EUA), -850 (Europeu oriental), -860 (Português),
        -863 (Franco-canadense) ou -865 (Nórdico). Também há suporte à
        página de código do Windows CP1252 (Europeu ocidental) com a opção
        -1252. Para outras páginas de código, use dos2unix em combinação cm
        iconv(1). Iconv pode converter entre uma lista grande de
        codificações de caracteres.

        Nunca use conversão ISO em arquivos textos Unicode. Isso vai
        corromper os arquivos codificados em UTF-8.

        Alguns exemplos:

        Conversão da página de código padrão do DOS para Latin-1 do Unix:

            dos2unix -iso -n entrada.txt saída.txt

        Conversão da CP850 do DOS para Latin-1 do Unix:

            dos2unix -850 -n entrada.txt saída.txt

        Conversão da CP1252 do Windows para Latin-1 do Unix:

            dos2unix -1252 -n entrada.txt saída.txt

        Conversão da CP1252 do Windows para UTF-8 (Unicode) do Unix:

            iconv -f CP1252 -t UTF-8 entrada.txt | dos2unix > saída.txt

        Conversão de Latin-1 do Unix para página de código padrão do DOS:

            unix2dos -iso -n entrada.txt saída.txt

        Conversão do Latin-1 do Unix para CP850 do DOS:

            unix2dos -850 -n entrada.txt saída.txt

        Conversão do Latin-1 do unix para CP1252 do Windows:

            unix2dos -1252 -n entrada.txt saída.txt

        Conversão do UTF-8 (Unicode) do Unix para CP1252 do Windows:

            unix2dos < entrada.txt | iconv -f UTF-8 -t CP1252 > saída.txt

        Veja também <http://czyborra.com/charsets/codepages.html> e
        <http://czyborra.com/charsets/iso8859.html>.

UNICODE
  Codificações
    Exitem codificações Unicode diferentes. No Unix e no Linux, arquivos
    Unicode são geralmente codificados em UTF-8. No Windows, arquivos texto
    Unicode podem ser codificados em UTF-8, UTF-16 ou UTF-16 big endian, mas
    na maioria das vezes são codificados no formato UTF-16.

  Conversão
    Unicode text files can have DOS, Unix or Mac line breaks, like ASCII
    text files.

    Todas as versões do dos2unix e unix2dos podem converter arquivos
    codificados em UTF-8 porque UTF-8 foi projetado para ter compatibilidade
    reversa com ASCII.

    Dos2unix e unix2dos com suporte a Unicode UTF-16 podem ler arquivos
    texto codificados em little e big endian UTF-16. Para ver se dos2unix
    foi compilado com suporte a UTF-16, digite "dos2unix -V".

    No Unix/Linux, arquivos codificados em UTF-16 são convertidos para a
    codificação de caracteres do localização. Use o comando locale(1) para
    descobrir qual é a codificação de caracteres da localização. Quando a
    conversão não for possível, ocorrerá um erro e o arquivo será ignorado.

    No Windows, arquivos UTF-16 são convertidos, por padrão, para UTF-8.
    Arquivos texto formatados em UTF-8 possuem ótimo suporte em ambos
    Windows e Unix/Linux.

    Codificações UTF-16 e UTF-8 são completamente compatíveis, não havendo
    qualquer perda de texto na conversão. Quando um erro de conversão UTF-16
    para UTF-8 ocorre, por exemplo quando o arquivo de entrada UTF-16 contém
    um erro, o arquivo será ignorado.

    Quando a opção "-u" é usada, o arquivo de saída será escrito na mesma
    codificação UTF-16 que o arquivo de saída. A opção "-u" evita conversão
    para UTF-8.

    Dos2unix e unix2dos não possuem opção para converter arquivos UTF-8 para
    UTF-16.

    Modo de conversão ISO e 7 bits não funcionam em arquivos UTF-16.

  Marca de ordem de byte
    On Windows Unicode text files typically have a Byte Order Mark (BOM),
    because many Windows programs (including Notepad) add BOMs by default.
    See also <https://en.wikipedia.org/wiki/Byte_order_mark>.

    No Unix, arquivos Unicode normalmente não têm BOM. Presume-se que
    arquivos texto são codificados na codificação de caracteres da
    localização.

    Dos2unix pode detectar apenas se um arquivo está no formato UTF-16 se o
    arquivo tiver BOM. Quando um arquivo UTF-16 não tiver BOM, dos2unix vai
    ver se o arquivo é um arquivo binário.

    Use a opção "-ul" ou "-ub" para converter um arquivo UTF-16 sem BOM.

    Dos2unix escreve por padrão nenhum BOM no arquivo de saída. Com a opção
    "-b", o Dos2unix escreve um BOM quando o arquivo de entrada possuir BOM.

    Unix2dos escreve por padrão um BOM no arquivo de saída quando o arquivo
    de entrada tem BOM. Use a opção "-m" para remover BOM.

    Dos2unix e unix2dos sempre escrevem BOM quando a opção "-m" é usada.

  Nomes de arquivos Unicode no Windows
    Dos2unix possui um suporte opcional para leitura e escrita de nomes de
    arquivos Unicode no Prompt de Comando Windows. Isso significa que
    dos2unix pode abrir arquivos que possuam caracteres no nome que não são
    parte da página de código ANSI padrão do sistema. Para ver se dos2unix
    para Windows foi compilado com suporte a nomes de arquivos em Unicode,
    digite "dos2unix -V".

    Há alguns problemas com a exibição de nomes de arquivos Unicode em um
    console Windows. Veja a opção "-D", "--display-enc". Para nomes de
    arquivos pode ser exibido incorretamente, mas os arquivos serão escritos
    com o nome correto.

  Exemplos de Unicode
    Conversão de UTF-16 do Windows (com BOM) para UTF-8 do Unix:

        dos2unix -n entrada.txt saída.txt

    Conversão de UTF-16LE do Windows (sem BOM) para UTF-8 do Unix:

        dos2unix -ul -n entrada.txt saída.txt

    Conversão de UTF-8 Unix para UTF-8 do Windows com BOM:

        unix2dos -m -n entrada.txt saída.txt

    Conversão de UTF-8 do Unix para UTF-16 do Windows:

        unix2dos < entrada.txt | iconv -f UTF-8 -t UTF-16 > saída.txt

GB18030
    GB18030 is a Chinese government standard. A mandatory subset of the
    GB18030 standard is officially required for all software products sold
    in China. See also <https://en.wikipedia.org/wiki/GB_18030>.

    GB18030 é completamente compatível com Unicode e pode ser considerado um
    formato de transformação de unicode. Assim como UTF-8, GB18030 é
    compatível com ASCII. GB18030 também é compatível com a página de código
    936 do Windows, também conhecida como GBK.

    No Unix/Linux, arquivos UTF-16 são convertidos para GB18030 quando a
    codificação da localização é definida para GB18030. Note que isso vai
    funcionar apenas se o sistemas oferecer suporte à localização. Use o
    comando "locale -a" para obter a lista de localizações às quais há
    suporte.

    No Windows, você precisa usar a opção "-gb" para converter arquivos
    UTF-16 para GB18030.

    Arquivos codificados em GB18030 possuem uma marca de ordem de bytes,
    como arquivos Unicode.

EXEMPLOS
    Lê a entrada da "stdin" e escreve a saída para "stdout":

        dos2unix < a.txt
        cat a.txt | dos2unix

    Converte e substitui a.txt. Converte e substitui b.txt:

        dos2unix a.txt b.txt
        dos2unix -o a.txt b.txt

    Converte e substitui a.txt no modo de conversão ascii:

        dos2unix a.txt

    Converte e substitui a.txt no modo de conversão ascii. Converte e
    substitui b.txt no modo de conversão 7bit:

        dos2unix a.txt -c 7bit b.txt
        dos2unix -c ascii a.txt -c 7bit b.txt
        dos2unix -ascii a.txt -7 b.txt

    Converte a.txt do formato do Mac para Unix:

        dos2unix -c mac a.txt
        mac2unix a.txt

    Converte a.txt do formato do Unix para Mac:

        unix2dos -c mac a.txt
        unix2mac a.txt

    Converte e substitui a.txt enquanto mantém a marca de data original:

        dos2unix -k a.txt
        dos2unix -k -o a.txt

    Converte a.txt e escreve para e.txt:

        dos2unix -n a.txt e.txt

    Converte a.txt e escreve para e.txt, mantém a marca de data de e.txt
    igual a a.txt:

        dos2unix -k -n a.txt e.txt

    Converte e substitui a.txt. Converte b.txt e escreve para e.txt:

        dos2unix a.txt -n b.txt e.txt
        dos2unix -o a.txt -n b.txt e.txt

    Converte c.txt e escreve para e.txt. Converte e substitui a.txt.
    Converte e substitui b.txt. Converte d.txt e escreve para f.txt:

        dos2unix -n c.txt e.txt -o a.txt b.txt -n d.txt f.txt

CONVERSÃO RECURSIVA
    Em um shell Unix, os comandos find(1) e xargs(1) podem ser usados para
    executar recursivamente o dos2unix em todos os arquivos texto em uma
    árvore de diretórios. Por exemplo, para converter todos os arquivos .txt
    na árvore de diretórios sob o diretório atual, digite:

        find . -name '*.txt' -print0 |xargs -0 dos2unix

    A opção do find(1) "-print0" e a opção correspondente do xargs(1) -0 são
    necessárias quando houver arquivos com espaços ou aspas no nome. Do
    contrário, essas opções podem ser omitidas. Outra alternativa é usar
    find(1) com a opção "-exec":

        find . -name '*.txt' -exec dos2unix {} \;

    Em um Prompt de Comando do Windows o seguinte comando pode ser usado:

        for /R %G in (*.txt) do dos2unix "%G"

    Usuários do PowerShell podem usar o seguinte comando no Windows
    PowerShell:

        get-childitem -path . -filter '*.txt' -recurse | foreach-object {dos2unix $_.Fullname}

LOCALIZAÇÃO
    LANG
        O idioma primário é selecionado com a variável de ambiente LANG. A
        variável LANG consiste em várias partes. A primeira parte está em
        letras pequenas no código do idioma. A segunda parte é opcional e é
        o código do país em letras maiúsculo, precedida de um sublinhado. Há
        também uma terceira parte opcional: codificação de caractere,
        precedida com um ponto. Alguns exemplos para shells do tipo padrão
        POSIX:

            export LANG=nl               Holandês
            export LANG=nl_NL            Holandês, Holanda
            export LANG=nl_BE            Holandês, Bélgica
            export LANG=es_ES            Espanhol, Espanha
            export LANG=es_MX            Espanhol, México
            export LANG=en_US.iso88591   Inglês, EUA, codificação Latin-1
            export LANG=en_GB.UTF-8      Inglês, Reino Unido, codificação UTF-8

        For a complete list of language and country codes see the gettext
        manual:
        <https://www.gnu.org/software/gettext/manual/html_node/Usual-Languag
        e-Codes.html>

        Nos sistemas Unix, você pode usar o comando locale(1) para obter
        informação específica da localização.

    LANGUAGE
        With the LANGUAGE environment variable you can specify a priority
        list of languages, separated by colons. Dos2unix gives preference to
        LANGUAGE over LANG. For instance, first Dutch and then German:
        "LANGUAGE=nl:de". You have to first enable localization, by setting
        LANG (or LC_ALL) to a value other than "C", before you can use a
        language priority list through the LANGUAGE variable. See also the
        gettext manual:
        <https://www.gnu.org/software/gettext/manual/html_node/The-LANGUAGE-
        variable.html>

        Se você selecionou um idioma que não está disponível, você vai terá
        as mensagens em inglês (padrão).

    DOS2UNIX_LOCALEDIR
        Com a variável de ambiente DOS2UNIX_LOCALEDIR, o LOCALEDIR definido
        durante a compilação pode ser sobrescrito. LOCALEDIR é usada para
        localizar os arquivos de idioma. O valor padrão do GNU é
        "/usr/local/share/locale". A opção --version vai exibir o LOCALEDIR
        que é usado.

        Exemplo (shell POSIX):

            export DOS2UNIX_LOCALEDIR=$HOME/share/locale

VALOR RETORNADO
    No sucesso, zero é retornado. Quando um erro de sistema ocorre, o último
    erro de sistema será retornado. Para outros erros, 1 é retornado.

    O valor retornado é sempre zero no modo quieto, exceto quando opções de
    linha de comando erradas são usadas.

PADRÕES
    <https://en.wikipedia.org/wiki/Text_file>

    <https://en.wikipedia.org/wiki/Carriage_return>

    <https://en.wikipedia.org/wiki/Newline>

    <https://en.wikipedia.org/wiki/Unicode>

AUTORES
    Benjamin Lin - <<EMAIL>> Bernd Johannes Wuebben (modo
    mac2unix) - <<EMAIL>>, Christian Wurll (adiciona nova linha
    extra) - <<EMAIL>>, Erwin Waterlander - <<EMAIL>>
    (mantenedor)

    Project page: <https://waterlan.home.xs4all.nl/dos2unix.html>

    SourceForge page: <https://sourceforge.net/projects/dos2unix/>

VEJA TAMBÉM
    file(1) find(1) iconv(1) locale(1) xargs(1)

