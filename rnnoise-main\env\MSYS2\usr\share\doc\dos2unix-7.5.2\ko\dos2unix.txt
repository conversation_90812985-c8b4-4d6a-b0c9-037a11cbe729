이름
    dos2unix - DOS/맥 에서 유닉스로, 내지는 그 반대로의 텍스트 파일 형식 변환 프로그램

개요
        dos2unix [<옵션>] [<파일> ...] [-n <입력파일> <출력파일> ...]
        unix2dos [<옵션>] [<파일> ...] [-n <입력파일> <출력파일> ...]

설명
    dos2unix 패키지에는 DOS 또는 맥 형식을 유닉스 형식으로, 내지는 그 반대로 원시 텍스트 파일을 변환하는
    "dos2unix" 유틸리티와 "unix2dos" 유틸리티가 들어있습니다.

    DOS/윈도우 텍스트 개행 문자는 캐리지 리턴(CR)과 뒤따라오는 라인 피드(LF) 문자를 결합한 형태를 취합니다. 유닉스 텍스트
    파일의 개행문자는 라인 피드(LF) 하나 뿐입니다. Mac OS X 이전의 맥 텍스트 파일은 캐리지 리턴(CR) 문자 하나
    뿐이었습니다. 그러나 지금은 Mac OS에서도 유닉스 방식 (LF) 개행 문자를 취합니다.

    개행 문자 관련하여 dos2unix에서는 파일 인코딩도 변환할 수 있습니다. 일부 DOS 코드 페이지는 유닉스 Latin-1
    인코딩으로 변환할 수 있습니다. 그리고 윈도우 유니코드(UTF-16) 파일은 유닉스 유니코드 (UTF-8) 파일로 변환할 수
    있습니다.

    이진 파일은 변환을 강제하지 않는 한 자동으로 건너뜁니다.

    디렉터리와 FIFO 같은 일반 파일 외 요소는 자동으로 건너뜁니다.

    심볼릭 링크와 대상은 보통 (완전히) 연결한 상태가 아닙니다. 심볼릭 링크는 별도로 바꿀 수 있거나, 심볼릭 링크 대상으로 기록할
    수 있습니다. 심볼릭 링크 대상 기록은 윈도우에서 지원하지 않습니다.

    dos2unix는 SunOS/솔라리스의 dos2unix가 나온 후 구성한 모델입니다. SunOS/솔라리스 버전과 한가지 중요한
    차이가 있습니다. SunOS/솔라리스 버전은 페어링 변환(새 파일 모드)만 지원하지만, 이 버전은 자체 변환(이전 파일 모드)도
    지원합니다. "-o", "-n" 옵션도 참고하십시오. 다른 차이점이 있다면 SunOS/솔라리스 버전은 *iso* 모드 변환이
    기본이지만 이 버전은 *ascii* 모드 변환이 기본입니다.

옵션
    --  뒤따라오는 모든 옵션은 파일 이름으로 간주합니다. 대시 문자로 시작하는 이름을 가진 파일을 변환하려면 이 옵션을
        사용하십시오. 예를 들어 "-foo" 파일을 변환할 경우, 다음 명령을 사용할 수 있습니다:

            dos2unix -- -foo

        또는 새 파일 모드에서:

            dos2unix -n -- -foo out.txt

    --allow-chown
        이전 파일 모드의 파일 소유권을 바꿀 수 있게 합니다.

        이 옵션을 사용하면 이전 파일의 사용자 또는 그룹 소유권을 유지할 수 없을 때 변환을 멈추지 않습니다. 새 파일의 모드로
        변환할 때 처럼 동일하게 변환한 파일에 새 소유권을 부여하면서 변환을 계속합니다. "-o", "-n" 옵션도 참고하십시오.
        이 옵션은 dos2unix에서 파일의 사용자 및 그룹 소유권을 유지하는 기능이 있을 경우에만 사용할 수 있습니다.

    -ascii
        기본 변환 모드입니다. 변환 모드 섹션을 참고하십시오.

    -iso
        DOS와 ISO-8859-1 문자 세트 변환을 진행합니다. 변환 모드 섹션을 참고하십시오.

    -1252
        윈도우 코드 페이지 1252(서유럽어)를 활용합니다.

    -437
        DOS 코드 페이지 437(미합중국)을 활용합니다. ISO 변환시 기본 코드 페이지로 활용합니다.

    -850
        DOS 코드 페이지 850(서유럽어)을 활용합니다.

    -860
        DOS 코드 페이지 860(포르투갈어)을 활용합니다.

    -863
        DOS 코드 페이지 863(캐나다 프랑스어)을 활용합니다.

    -865
        DOS 코드 페이지 865(북유럽어)를 활용합니다.

    -7  8비트 문자를 7비트 영역으로 변환합니다.

    -b, --keep-bom
        바이트 순서 표시(BOM)를 유지합니다. 입력 파일에 BOM이 들어있다면, 출력 파일에 BOM을 기록합니다. DOS 개행
        문자를 변환할 때 기본 동작입니다. "-r"옵션 정보도 참고하십시오.

    -c, --convmode <변환모드>
        변환 모드를 설정합니다. <변환모드>는 *ascii*, *7bit*, *iso*, *mac* 값 중 하나이며 ascii가
        기본값입니다.

    -D, --display-enc <인코딩>
        표시 텍스트 인코딩을 설정합니다. <인코딩>은 *ansi*, *unicode*, *unicodebom*, *utf8*,
        *utf8bom* 값 중 하나이며, ansi가 기본값입니다.

        이 옵션은 유니코드 파일 이름을 지원하는 윈도우용 dos2unix에서만 사용할 수 있습니다. 이 옵션은 실제 파일 이름을
        읽고 쓰는데 아무런 영향을 주지 않으며, 어떻게 나타내는 지에 대해서만 영향을 줍니다.

        텍스트 인코딩 기반으로 윈도우 콘솔에서 텍스트를 나타내는 방법에는 여러가지가 있습니다. 각 방법에는 고유의 장점과 단점이
        있습니다.

        ansi
            dos2unix의 기본 방식은 ANSI 인코딩 텍스트를 활용하는 방법입니다. 하위 호환성이 장점입니다. 래스터 및
            트루타입 글꼴에 동작합니다. 일부 국가에서는 dos2unix에서 윈도우 시스템 코드 페이지를 사용하기 때문에
            "chcp" 명령으로 윈도우 시스템 ANSI 코드 페이지로 DOS OEM 활성 코드 페이지를 바꾸어야 합니다.

            ANSI 방식의 단점은 국제어 파일 이름이 시스템 기본 코드 페이지에 들어있지 않아 제대로 화면에 나타나지
            않습니다. 물음표 기호 또는 잘못된 기호가 대신 나타날 수 있습니다. 타 언어 파일 이름으로 동작하지 않는다면 이
            방식이 괜찮습니다.

        unicode, unicodebom
            유니코드(UTF-16의 윈도우 이름) 인코딩의 장점이란 텍스트가 보통 제대로 나타난다는 점입니다. 활성 코드
            페이지를 바꿀 필요는 없습니다. 콘솔의 전세계 문자가 제대로 나타나려면 트루타입 글꼴을 선택해야합니다. 문자가
            트루타입 글꼴 세트에 들어있지 않으면 작은 사각형 모양을 볼 수 있으며, 때로는 물음표로 나타나기도 합니다.

            ConEmu 콘솔을 사용한다면 ConEmu에서 적절한 글꼴을 자동으로 선택하므로 모든 텍스트가 제대로 나타납니다.

            유니코드 사용의 단점은 아스키와 호환성이 없다는 점입니다. 다른 프로그램으로 출력을 전달할 때 쉽게 처리할 수
            없습니다.

            "unicodebom" 방식을 사용하면 BOM(바이트 순서 표시)을 텍스트 앞에 둡니다. BOM은 파워셸에서 출력
            내용을 올바르게 전달 또는 파이핑할 때 필요합니다.

        utf8, utf8bom
            utf8의 장점은 아스키와의 호환성입니다. 콘솔의 글꼴을 트루타입 글꼴로 선택해야 합니다. 트루타입 글꼴로 설정하면
            "unicode" 인코딩 때와 비슷하게 나타납니다.

            단점은 래스터 글꼴을 기본으로 사용하면 아스키 인코딩을 하지 않는 문자를 잘못 나타낼 수 있습니다. 유니코드 파일
            이름 뿐만 아니라 번역 메시지 조차 제대로 읽을 수 없습니다. 윈도우에서는 동아시아 지역으로 설정했을 경우 메시지가
            나타날 때 콘솔 화면이 상당히 많이 깜빡거리는걸 볼 수 있습니다.

            ConEmu 콘솔에서는 utf8 인코딩이 제대로 동작합니다.

            "utf8bom" 방식을 사용하면 UTF-8 텍스트 앞에 BOM(바이트 순서 표시)를 둡니다. BOM은 파워셸에서
            출력 내용을 올바르게 전달 또는 파이핑할 때 필요합니다.

        기본 인코딩은 DOS2UNIX_DISPLAY_ENC 환경 변수를 "unicode", "unicodebom", "utf8",
        "utf8bom" 중 값 하나로 설정하여 바꿀 수 있습니다.

    -e, --add-eol
        개행 문자가 줄 끝에 없을 경우 추가합니다. 모든 변환 과정에서 동작합니다.

        DOS 형식에서 유닉스 형식으로 변환한 파일의 행 끝에 개행 문자가 빠질 수도 있습니다. 행 끝에 개행 문자가 빠진 채로
        텍스트 파일을 기록하는 텍스트 편집기가 있습니다. 텍스트 파일의 모든 줄 끝에 반드시 개행 문자로 끝나야 하는게 POSIX
        표준이기에, 일부 유닉스 프로그램에서는 이 파일을 처리하는 문제가 있습니다. 예를 들면, 파일 내용을 합칠 때 예상한 대로
        결과가 나타나지 않습니다.

    -f, --force
        강제로 이진 파일을 변환합니다.

    -gb, --gb18030
        윈도우에서는 로캘 설정 여부와 관계 없이 UTF-16 파일이 기본이기에 UTF-8로 변환합니다. 이 옵션을 사용하면
        UTF-16 파일을 GB18030으로 변환합니다. 이 옵션은 윈도우에서만 사용할 수 있습니다. GB18030 섹션을
        참고하십시오.

    -h, --help
        도움말을 표시하고 나갑니다.

    -i[<플래그>], --info[=<플래그>] <파일> ...
        파일 정보를 표시합니다. 변환 동작은 하지 않습니다.

        다음 정보를 DOS 개행 문자 수, 유닉스 개행 문자 수, 맥 개행 문자 수, 바이트 순서 표시, 텍스트 또는 이진 파일
        여부, 파일 이름 순으로 정보를 출력합니다.

        예제 출력:

             6       0       0  no_bom    text    dos.txt
             0       6       0  no_bom    text    unix.txt
             0       0       6  no_bom    text    mac.txt
             6       6       6  no_bom    text    mixed.txt
            50       0       0  UTF-16LE  text    utf16le.txt
             0      50       0  no_bom    text    utf8unix.txt
            50       0       0  UTF-8     text    utf8dos.txt
             2     418     219  no_bom    binary  dos2unix.exe

        때로는 이진 파일을 텍스트 파일로 잘못 알아챌 수도 있습니다. "-s" 옵션도 참고하십시오.

        "-e" 또는 "--add-eol" 옵션을 추가로 사용하면 행 마지막 개행 문자 형식도 출력하며, 없다면 "noeol"을
        출력합니다.

        예제 출력:

             6       0       0  no_bom    text   dos     dos.txt
             0       6       0  no_bom    text   unix    unix.txt
             0       0       6  no_bom    text   mac     mac.txt
             1       0       0  no_bom    text   noeol   noeol_dos.txt

        출력 방식을 바꿀 추가 플래그를 설정할 수 있습니다. 플래그 하나 이상을 추가할 수 있습니다.

        0   파일 정보 행 끝의 개행 문자 대신 널 문자를 출력합니다. c 플래그를 사용할 때 공백 또는 따옴표로 파일 이름
            해석을 올바르게 할 수 있습니다. 이 플래그는 xargs(1) 옵션 -0 또는 "--null"을 함께 사용하십시오.

        d   DOS 개행 문자를 출력합니다.

        u   유닉스 개행 문자를 출력합니다.

        m   맥 개행 문자를 출력합니다.

        b   바이트 순서 표시를 출력합니다.

        t   파일의 텍스트 또는 이진 여부를 출력합니다.

        e   행 마지막에 개행 문자 형식을 출력하거나, 없을 경우 "noeol"을 출력합니다.

        c   변환할 파일만 출력합니다.

            dos2unix에 "c" 플래그를 사용하면 DOS 개행 문자가 들어간 파일만 출력합니다. unix2dos는 유닉스
            개행 문자가 들어간 파일 이름만 출력합니다.

            "-e" 또는 "--add-eol" 옵션을 추가로 사용하면 행 마지막에 빠진 개행 문자를 출력합니다.

        h   헤더를 출력합니다.

        p   경로를 뺀 파일 이름을 나타냅니다.

        예제:

        모든 *.txt 파일 정보 출력:

            dos2unix -i *.txt

        DOS 개행 문자와 유닉스 개행 문자 갯수만 출력:

            dos2unix -idu *.txt

        바이트 순서 표시만 나타내기:

            dos2unix --info=b *.txt

        DOS 개행 문자가 들어간 파일 목록 출력:

            dos2unix -ic *.txt

        유닉스 개행 문자가 들어간 파일 목록 출력:

            unix2dos -ic *.txt

        DOS 개행 문자가 들어갔거나 행 마지막에 개행 문자가 빠진 파일 목록 출력:

            dos2unix -e -ic *.txt

        DOS 개행 문자가 들어간 파일만 변환하며 다른 파일은 건드리지 않습니다:

            dos2unix -ic0 *.txt | xargs -0 dos2unix

        DOS 개행 문자가 들어간 텍스트 파일 찾기:

            find -name '*.txt' -print0 | xargs -0 dos2unix -ic

    -k, --keepdate
        출력 파일의 날짜 스탬프는 입력 파일과 동일하게 설정합니다.

    -L, --license
        프로그램 라이선스를 표시합니다.

    -l, --newline
        부가 개행 문자를 추가합니다.

        dos2unix: DOS 개행 문자만을 유닉스 개행 문자 둘로 변환합니다. 맥 모드에서는 맥 개행 문자만을 유닉스 개행
        문자 둘로 변환합니다.

        unix2dos: 유닉스 개행 문자만을 DOS 개행 문자 둘로 변환합니다. 맥 모드에서는 유닉스 개행 문자를 맥 개행 문자
        둘로 변환합니다.

    -m, --add-bom
        바이트 순서 표시(BOM)를 출력 파일에 기록합니다. 기본적으로 UTF-8 BOM을 기록합니다.

        입력 파일 인코딩이 UTF-16이고 "-u" 옵션을 사용했다면 UTF-16 BOM을 기록합니다.

        출력 인코딩이 UTF-8, UTF-16, GB18030이 아니라면 이 옵션을 사용하지 마십시오. 유니코드 섹션도
        참고하십시오.

    -n, --newfile <입력파일> <출력파일> ...
        새 파일 모드입니다. <입력파일>을 변환하여 <출력파일>에 기록합니다. 파일 이름은 짝으로 지정하며 와일드카드 이름은
        사용하지 *말아야* 하며, 그렇지 않으면 파일을 *잃습니다*.

        새 파일(짝)모드로 파일 변환을 시작한 사용자는 변환한 파일의 소유자가 됩니다. 새 파일의 읽기/쓰기 권한은 변환을 실행한
        사용자의 umask(1)를 뺀 원본 파일의 권한으로 부여합니다.

    --no-allow-chown
        이전 파일 모드의 파일 소유권 변경을 허용하지 않습니다(기본값).

        원본 파일의 사용자 또는 그룹 소유권을 이전 파일 모드에서 유지할 수 없다면 변환을 멈춥니다. "-o", "-n" 옵션도
        참고하십시오. 이 옵션은 dos2unix에서 파일의 사용자 및 그룹 소유권을 유지하는 기능이 있을 경우에만 사용할 수
        있습니다.

    --no-add-eol
        개행 문자가 줄 끝에 없을 경우 넣지 않습니다.

    -O, --to-stdout
        유닉스 필터 동작과 비슷하게 표준 출력에 기록합니다. 이전 파일(기록) 모드로 돌아가려면 "-o" 옵션을 사용합니다.

        "-e" 옵션을 붙여 결합한 파일은 제대로 내용을 합칠 수 있습니다. 마지막 줄과 첫줄은 병합하지 않으며, 결합한 파일
        내용 중간에 들어있는 유니코드 바이트 순서 표시는 들어가지 않습니다. 예를 들면:

            dos2unix -e -O file1.txt file2.txt > output.txt

    -o, --oldfile <파일> ...
        이전 파일 모드. <파일>을 변환하고 출력을 <파일>에 덮어씁니다. 프로그램 기본값은 이 모드로의 실행입니다. 와일드카드
        이름을 사용할 수도 있습니다.

        이전 파일(자체 변환)모드에서 변환한 파일은 동일한 소유자, 그룹, 읽기 쓰기 권한을 원본 파일과 동일하게 유지합니다.
        게다가 쓰기 권한이 있는 다른 사용자(예를 들어, root)가 파일을 변환했다 하더라도 마찬가지입니다. 원본 값을 그대로
        유지할 수 없을 경우에는 변환을 멈춥니다. 소유권 변경이 가능하단건 곧 원본 소유자가 더이상 파일을 읽을 수 없음을
        의미합니다. 소유그룹 변경은 원하지 않는 사용자가 파일을 읽게 할 수 있어 보안 문제를 야기할 수 있습니다. 소유자,
        소유그룹, 읽기/쓰기 권한 유지는 유닉스에서만 지원합니다.

        dos2unix에서 파일의 사용자 및 그룹 소유권을 유지하는 기능을 지원하는지 확인하려면 "dos2unix -V"를
        입력하십시오.

        변환 동작은 항상 임시 파일로 처리합니다. 변환 과정 도중 오류가 발생하면, 임시 파일을 삭제하고 원본 파일을 그대로
        둡니다. 변환이 끝나면 원본 파일을 임시 파일로 바꿉니다. 실행한 사용자는 원본 파일의 쓰기 권한을 가지겠지만, 원본
        파일처럼 임시 파일에 사용자 또는 그룹 소유권을 동일하게 부여할 수는 없습니다. 즉, 원본 파일의 사용자 또는 그룹 권한을
        그대로 대상 파일에 설정할 수는 없단 뜻입니다. 이 경우 "--allow-chown" 옵션을 사용하여 변환을 계속할 수
        있습니다:

            dos2unix --allow-chown foo.txt

        다른 옵션은 새 파일 모드에서 사용합니다:

            dos2unix -n foo.txt foo.txt

        "--allow-chown" 옵션의 장점은 와일드 카드를 사용할 수 있으며, 가능하다면 소유관 속성을 유지합니다.

    -q, --quiet
        미 출력 모드. 모든 경고와 메시지를 끕니다. 반환 값은 0입니다. 잘못된 명령행 옵션을 사용했을 때는 이 경우에서
        제외합니다.

    -r, --remove-bom
        바이트 순서 표시(BOM)를 제거합니다. BOM을 출력 파일에 기록하지 않습니다. 유닉스 개행 문자로 변환할 때 기본
        동작입니다. "-b" 옵션도 참고하십시오.

    -s, --safe
        이진 파일은 건너뜁니다(기본값).

        이진 파일 건너뛰기는 갑작스런 실수를 피하는 동작입니다. 이진 파일 감시는 100% 실패 예방을 하지 않습니다. 입력
        파일에서 텍스트 파일에서 보통 찾을 수 없는 이진 심볼을 검색합니다. 이진 파일에도 일반 텍스트 문자만 들어있을 수
        있습니다. 이런 이진 파일 종류는 (그래서) 텍스트 파일로 실수로 알아챌 수 있습니다.

    -u, --keep-utf16
        입력의 UTF-16 인코딩 원본을 유지합니다. 원본 파일은 동일한 UTF-16 인코딩을 리틀엔디언 또는 빅엔디언으로 입력
        파일과 같이 기록합니다. 이는 UTF-8로의 변환을 막습니다. UTF-16 BOM은 원본을 따라 대상에 기록합니다. 이
        옵션 동작은 "-ascii" 옵션으로 막을 수 있습니다.

    -ul, --assume-utf16le
        입력 파일 형식을 UTF-16LE로 가정합니다.

        바이트 순서 표시가 입력 파일에 있다면 BOM은 이 옵션보다 우선순위를 갖습니다.

        잘못된 가정(입력 파일이 UTF-16LE 형식이 아니라거나)하에 변환에 성공했다면, 잘못된 내용이 들어간 UTF-8 출력
        파일을 받을 수 있습니다. iconf(1) 명령을 활용하여 UTF-8 출력 파일을 UTF-16LE로 되돌려 변환하는
        방식으로 잘못된 변환 결과를 되돌릴 수 있습니다. 이런 방법으로 원본 파일을 되돌릴 수 있습니다.

        UTF-16LE가 *변환 모드*로 동작한다고 가정해보겠습니다. 기본 *ascii* 모드로 전환하면 UTF-16LE에 대한
        가정은 꺼진 상태입니다.

    -ub, --assume-utf16be
        입력 파일 형식을 UTF-16BE로 가정합니다.

        이 옵션은 "-ul"과 동일한 동작을 수행합니다.

    -v, --verbose
        자세한 메시지를 표시합니다. 추가로, 바이트 순서 표시 세부정보가 나타나며 변환 개행 문자가 나타납니다.

    -F, --follow-symlink
        심볼릭 링크를 따라가서 대상을 변환합니다.

    -R, --replace-symlink
        심볼릭 링크를 변환 파일로 바꿉니다(원시 대상 파일은 바뀌지 않은 상태로 둡니다).

    -S, --skip-symlink
        심볼릭 링크와 대상을 바뀌지 않게 그대로 둡니다(기본값).

    -V, --version
        버전 정보를 표시하고 나갑니다.

맥 모드
    DOS 개행 문자와 유닉스 개행 문자를 서로 변환해주는 동작이 기본 동작이빈다. 맥 개행 문자는 변환하지 않습니다.

    맥 모드에서 개행 문자를 맥에서 유닉스로, 내지는 그 반대로 변환합니다. DOS 개행 문자를 바꾸지 않습니다.

    맥 모드를 실행하려면 "-c mac" 명령행 옵션을 사용하거나 "mac2unix" 명령 또는 "unix2mac" 명령을
    사용하십시오.

변환 모드
    ascii
        기본 변환 모드입니다. 이 모드는 아스키와 UTF-8과 같은 아스키 호환 인코딩 파일을 변환합니다. ascii 모드를
        활성화하면 7bit 모드와 iso 모드를 사용하지 않습니다.

        dos2unix에서 UTF-16을 지원하면 UTF-16 인코딩 파일을 POSIX 시스템의 현재 로캘 문자 인코딩과 윈도우의
        UTF-8 인코딩으로 변환합니다. ascii 모드를 사용하면 UTF-16 인코딩 ("u")을 유지하는 옵션과 UTF-16
        입력("-ul" 및 "-ub")을 가정하는 옵션 값을 사용하지 않습니다. dos2unix에 UTF-16 을 지원하는지
        알아보려면 "dos2unix -V"를 입력하십시오. 유니코드 섹션도 살펴보십시오.

    7bit
        이 모드에서는 아스키 영역 밖(128~255 값)의 모든 8비트 문자를 7비트 영역으로 변환합니다.

    iso DOS 문자 세트(코드 페이지)에서 유닉스 ISO 문자 세트 ISO-8859-1(Latin1)로 또는 그 반대로 문자를
        변환합니다. ISO-8859-1에 대응하지 않는 DOS 문자는 변환할 수 없어 구두점으로 변환합니다.
        ISO-8859-1에서 DOS 문자 세트로 변환할 때도 마찬가지입니다.

        dos2unix에 "-iso"옵션만 사용했을 경우 활성 코드 페이지 확인을 시도합니다. 불가능하다면 dos2unix는
        미합중국에서 주로 사용하는 CP437 기본 코드 페이지를 사용합니다. 코드 페이지를 강제로 지정하려면 -437 (US),
        -850 (서유럽어), -860 (포르투갈어), -863 (캐나다 프랑스어), -865 (북유럽어) 옵션 중 하나를
        사용하십시오. 윈도우 코드 페이지 CP1252 (서유럽어)는 -1252 옵션으로 지원합니다. 다른 코드 페이지를 활용하려면
        dos2unix와 iconv(1)를 함께 사용하십시오. iconv는 다양한 문자 인코딩을 변환할 수 있습니다.

        유니코드 텍스트 파일을 ISO 방식으로 변환하지 마십시오. UTF-8 인코딩 파일이 깨집니다.

        일부 예제:

        DOS 기본 코드 페이지에서 유닉스 Latin-1으로 변환:

            dos2unix -iso -n in.txt out.txt

        DOS CP850에서 유닉스 Latin-1으로 변환:

            dos2unix -850 -n in.txt out.txt

        윈도우 CP1252에서 유닉스 Latin-1으로 변환:

            dos2unix -1252 -n in.txt out.txt

        윈도우 CP1252에서 유닉스 UTF-8(유니코드)로 변환:

            iconv -f CP1252 -t UTF-8 in.txt | dos2unix > out.txt

        유닉스 Latin-1에서 DOS 기본 코드 페이지로 변환:

            unix2dos -iso -n in.txt out.txt

        유닉스 Latin-1에서 DOS CP850으로 변환:

            unix2dos -850 -n in.txt out.txt

        유닉스 Latin-1에서 윈도우 CP1252로 변환:

            unix2dos -1252 -n in.txt out.txt

        유닉스 UTF-8(유니코드)에서 윈도우 CP1252로 변환:

            unix2dos < in.txt | iconv -f UTF-8 -t CP1252 > out.txt

        <http://czyborra.com/charsets/codepages.html> 링크와
        <http://czyborra.com/charsets/iso8859.html> 링크도 참고하십시오.

유니코드
  인코딩
    여러가지 유니코드 인코딩이 있습니다. 유닉스와 리뉵스 유니코드 파일은 보통 UTF-8 인코딩 방식으로 인코딩합니다. 윈도우에서는
    유니코드 텍스트 파일을 UTF-8, UTF-16, UTF-16 빅엔디언 방식으로 인코딩할 수 있지만 보통 UTF-16 형식으로
    인코딩합니다.

  변환
    유니코드 텍스트 파일에는 DOS, 유닉스, 맥 개행 문자를 아스키 텍스트 파일처럼 가질 수 있습니다.

    dos2unix와 unix2dos의 모든 버전에서는 UTF-8이 아스키와의 하위 호환성을 고려했기 때문에 UTF-8 인코딩 파일을
    변환할 수 있습니다.

    UTF-16을 지원하는 dos2unix와 unix2dos는 UTF-16 리틀엔디언 및 빅엔디언 인코딩 텍스트 파일을 모두 읽을 수
    있습니다. dos2unix에 UTF-16 지원 기능이 들어갔는지 확인하려면 "dos2unix -V" 명령을 입력하십시오.

    유닉스/리눅스에서 UTF-16 인코딩 파일은 로캘 문자 인코딩으로 변환합니다. 어떤 로캘 문자 인코딩을 사용했는지 확인하려면
    locale(1) 명령을 사용하십시오. 변환이 불가능할 경우 변환 오류가 나타나며, 해당 파일을 건너뜁니다.

    윈도우에서 UTF-16 파일은 기본적으로 UTF-8로 변환합니다. UTF-8 형식 텍스트 파일은 윈도우, 유닉스, 리눅스에서 잘
    읽힙니다.

    UTF-16과 UTF-8 인코딩은 완전한 호환 관계이며, 변환 과정에 잃을 내용은 없습니다. UTF-16에서 UTF-8로 변환하던
    중 오류가 나타났다면, 예를 들어 UTF-16 파일에 오류가 있다면, 해당 파일을 건너뜁니다.

    "-u" 옵션을 사용하면 입력 파일의 UTF-16 인코딩을 출력 파일에도 그대로 적용합니다. "-u" 옵션은 UTF-8로의 변환을
    막습니다.

    dos2unix와 unix2dos에는 UTF-8 파일을 UTF-16으로 변환하는 옵션이 없습니다.

    ISO 및 7비트 모드 변환은 UTF-16 파일에 동작하지 않습니다.

  바이트 순서 표시(BOM)
    윈도우에서는 유니코드 텍스트 파일에 대부분 윈도우 프로그램(메모장 포함)에서 BOM을 기본으로 추가하기 때문에 보통 바이트 순서
    표시(BOM)가 들어갑니다. <https://en.wikipedia.org/wiki/Byte_order_mark> 링크를
    참고하십시오.

    유닉스에서는 유니코드 파일에 BOM이 들어가지 않습니다. 텍스트 파일을 로캘 문자 인코딩으로 인코딩했다고 가정합니다.

    dos2unix는 UTF-16 형식인지, BOM이 들어갔는지만 알 수 있습니다. UTF-16 파일에 BOM이 없다면
    dos2unix는 이진 파일로 인식합니다.

    UTF-16 파일을 BOM을 빼고 변환하려면 "-ul" 옵션 또는 "-ub" 옵션을 사용하십시오.

    dos2unix에서는 기본적으로 출력 파일에 BOM을 기록하지 않습니다. 입력 파일에 BOM이 있다면 dos2unix에 "-b"
    옵션을 지정했을 때 출력 파일에 BOM을 기록합니다.

    unix2dos에서는 기본적으로 입력 파일에 BOM이 있다면 출력 파일에 BOM을 기록합니다. BOM을 제거하려면 "-r" 옵션을
    사용하십시오.

    dos2unix와 unix2dos는 "-m" 옵션을 사용하면 항상 BOM을 기록합니다.

  윈도우의 유니코드 파일 이름
    dos2unix에서는 윈도우 명령 프롬프트의 유니코드 파일 이름을 읽고 쓰는 추가 기능을 지원합니다. dos2unix에서 기본
    시스템 ANSI 코드 페이지의 일부가 아닌 문자가 들어간 이름일지라도 파일을 열 수 있다는 뜻입니다. 윈도우용 dos2unix에
    유니코드 파일 이름 지원 기능이 들어있는지 확인하려면 "dos2unix -V" 명령을 입력하십시오.

    윈도우 콘솔에 유니코드 파일 이름을 표시할 때 몇가지 문제가 있습니다. "-D", "--display-enc" 옵션을
    참고하십시오. 콘솔에서 파일 이름이 잘못 나타날 수는 있지만 파일은 (어쨌든) 올바른 이름으로 기록합니다.

  유니코드 예제
    윈도우 UTF-16(+BOM)을 유닉스 UTF-8로 변환:

        dos2unix -n in.txt out.txt

    윈도우 UTF-16LE(-BOM)를 유닉스 UTF-8로 변환:

        dos2unix -ul -n in.txt out.txt

    유닉스 UTF-8을 윈도우 UTF-8(+BOM)로 변환:

        unix2dos -m -n in.txt out.txt

    유닉스 UTF-8을 윈도우 UTF-16으로 변환:

        unix2dos < in.txt | iconv -f UTF-8 -t UTF-16 > out.txt

GB18030
    GB18030은 중화인민공화국 정부 표준입니다. GB18030 표준 하위 필수 세트는 중화인민공화국에서 판매하는 모든 프로그램
    제품에 공식적으로 필요합니다. <https://en.wikipedia.org/wiki/GB_18030> 링크를 참고하십시오.

    GB18030은 유니코드와 완벽하게 호환하며, 유니코드 변환 형식으로 고려할 수 있습니다. UTF-8과 유사하게, GB18030은
    아스키와 호환성을 지닙니다. GB18030은 또한 GBK로 알려진 윈도우 코드 페이지 936과도 호환성이 있습니다.

    유닉스/리눅스에서 UTF-16 파일은 로캘 인코딩을 GB18030으로 설정하면 GB18030으로 변환합니다. 참고로 시스템에서
    해당 로캘을 지원할 경우에만 동작합니다. 지원 로캘 목록을 살펴보려면 "locale -a" 명령을 사용하십시오.

    윈도우에서는 UTF-16 파일을 GB18030으로 변환하려면 "-gb" 옵션을 사용해야합니다.

    GB18030 인코딩 파일에는 유니코드 파일처럼 바이트 순서 표시가 들어갈 수 있습니다.

예제
    '표준 입력'을 읽어 '표준 출력'에 출력:

        dos2unix < a.txt
        cat a.txt | dos2unix

    a.txt를 변환하고 내용 바꾸기. b.txt를 변환하고 내용 바꾸기:

        dos2unix a.txt b.txt
        dos2unix -o a.txt b.txt

    a.txt를 아스키 변환 모드로 변환하고 내용 바꾸기:

        dos2unix a.txt

    a.txt를 아스키 변환 모드로 변환하고 내용 바꾸기, b.txt를 7비트 변환 모드로 변환하고 내용 바꾸기:

        dos2unix a.txt -c 7bit b.txt
        dos2unix -c ascii a.txt -c 7bit b.txt
        dos2unix -ascii a.txt -7 b.txt

    a.txt를 맥 형식에서 유닉스 형식으로 변환:

        dos2unix -c mac a.txt
        mac2unix a.txt

    a.txt를 유닉스 형시에서 맥 형식으로 변환:

        unix2dos -c mac a.txt
        unix2mac a.txt

    a.txt의 날짜 스탬프를 유지하며 변환하고 내용 바꾸기:

        dos2unix -k a.txt
        dos2unix -k -o a.txt

    a.txt를 변환하여 e.txt로 기록:

        dos2unix -n a.txt e.txt

    a.txt를 변환하고 e.txt로 기록, e.txt의 날짜 스탬프를 a.txt와 동일하게 설정:

        dos2unix -k -n a.txt e.txt

    a.txt를 변환하고 내용 바꾸기, b.txt를 변환하고 e.txt에 기록:

        dos2unix a.txt -n b.txt e.txt
        dos2unix -o a.txt -n b.txt e.txt

    a.txt를 변환하여 e.txt로 기록, a.txt를 변환하고 내용 바꾸기, b.txt를 변환하고 내용 바꾸기, d.txt를
    변환하고 f.txt로 기록:

        dos2unix -n c.txt e.txt -o a.txt b.txt -n d.txt f.txt

재귀 변환
    유닉스 셸에서 dos2unix로 하여금 디렉터리 트리의 모든 텍스트 파일을 재귀적으로 탐색하여 처리하려 할 때, find(1)와
    xargs(1) 명령을 사용할 수 있습니다. 예를 들어 아래 형태를 갖춘 디렉터리 트리의 모든 .txt 파일을 변환하려면:

        find . -name '*.txt' -print0 |xargs -0 dos2unix

    find(1) 명령의 옵션 "-print0" 그리고 이에 해당하는 xargs(1) 명령의 -0옵션은 이름에 공백이나 따옴표가 있을
    경우 필요합니다. 공백이나 따옴표가 없으면 이 옵션을 생략할 수 있습니다. 다른 옵션으로는 find(1)의 "-exec" 옵션이
    있습니다:

        find . -name '*.txt' -exec dos2unix {} \;

    윈도우 명령 프롬프트에서 다음 명령을 활용할 수 없습니다:

        for /R %G in (*.txt) do dos2unix "%G"

    파워셸 사용자는 윈도우 파워셸에서 다음 명령을 활용할 수 있습니다:

        get-childitem -path . -filter '*.txt' -recurse | foreach-object {dos2unix $_.Fullname}

지역화
    LANG
        기본 언어는 LANG 환경 변수로 선택합니다. LANG 환경 변수는 몇가지 부분으로 구성합니다. 첫번째 부분은 언어 코드를
        의미하는 몇가지 문자입니다. 두번제 부분은 대문자로 이루어진 국가 코드입니다. 두 코드 사이에는 밑줄 문자가 들어갑니다.
        마지막으로 추가하는 부분은 점이 앞서 오는 문자 인코딩입니다. POSIX 표준 형 셸에서 몇가지 예제를 보여드리도록
        하겠습니다:

            export LANG=nl               Dutch
            export LANG=nl_NL            Dutch, The Netherlands
            export LANG=nl_BE            Dutch, Belgium
            export LANG=es_ES            Spanish, Spain
            export LANG=es_MX            Spanish, Mexico
            export LANG=en_US.iso88591   English, USA, Latin-1 encoding
            export LANG=en_GB.UTF-8      English, UK, UTF-8 encoding

        언어 및 국가 코드 전체 목록을 보려면
        <https://www.gnu.org/software/gettext/manual/html_node/Usual-Languag
        e-Codes.html> gettext 설명서를 참고하십시오

        유닉스 시스템에서는 locale(1) 명령을 활용하여 로캘별 정보를 가져올 수 있습니다.

    LANGUAGE
        LANGUAGE 환경 변수에는 언어를 우선 순위별로 콜론으로 구분하여 지정할 수 있습니다. dos2unix에서는
        LANGUAGE를 LANG보다 우선하여 취급합니다. 예를 들면 네덜란드어를 먼저 그 다음에 독일어를 설정할 경우
        "LANGUAGE=nl:de"(으)로 설정합니다. LANGUAGE 환경 변수에 언어별 우선 순위를 두어 사용할 수 있기
        전에 LANG 환경 변수에 "C" 대신 다른 값을 넣어 지역화를 우선 설정해야합니다.
        <https://www.gnu.org/software/gettext/manual/html_node/The-LANGUAGE-
        variable.html> gettext 설명서를 참고하십시오

        사용할 수 없는 언어를 선택했다면 표준 (국제) 영어 메시지로 나타납니다.

    DOS2UNIX_LOCALEDIR
        DOS2UNIX_LOCALEDIR 환경 변수는, 컴파일 시간에는 LOCALEDIR 환경 변수를 우선 활용할 수 있습니다.
        LOCALEDIR 환경 변수는 언어 파일을 찾을 때 활용합니다. GNU 기본값은
        "/usr/local/share/locale"입니다. --version 옵션을 사용하면 LOCALEDIR이 사용하는 값을
        보여줍니다.

        예제 (POSIX 셸):

            export DOS2UNIX_LOCALEDIR=$HOME/share/locale

반환 값
    성공하면 0값을 반환합니다. 시스템 오류가 나타나면 가장 마지막에 나타난 시스템 오류를 반환합니다. 다른 오류는 1 값을
    반환합니다.

    미출력 모드의 반환 값은 항상 0이지만, 명령행 옵션이 잘못됐을 경우는 제외합니다.

표준
    <https://en.wikipedia.org/wiki/Text_file>

    <https://en.wikipedia.org/wiki/Carriage_return>

    <https://en.wikipedia.org/wiki/Newline>

    <https://en.wikipedia.org/wiki/Unicode>

저자
    Benjamin Lin - <<EMAIL>>, Bernd Johannes Wuebben (mac2unix
    mode) - <<EMAIL>>, Christian Wurll (add extra newline) -
    <<EMAIL>>, Erwin Waterlander - <<EMAIL>>
    (maintainer)

    프로젝트 페이지: <https://waterlan.home.xs4all.nl/dos2unix.html>

    SourceForge 페이지: <https://sourceforge.net/projects/dos2unix/>

추가 참조
    file(1) find(1) iconv(1) locale(1) xargs(1)

