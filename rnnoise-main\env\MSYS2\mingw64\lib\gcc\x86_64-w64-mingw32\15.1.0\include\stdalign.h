/* Copyright (C) 2011-2025 Free Software Foundation, Inc.

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 3, or (at your option)
any later version.

GCC is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHA<PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

Under Section 7 of GPL version 3, you are granted additional
permissions described in the GCC Runtime Library Exception, version
3.1, as published by the Free Software Foundation.

You should have received a copy of the GNU General Public License and
a copy of the GCC Runtime Library Exception along with this program;
see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
<http://www.gnu.org/licenses/>.  */

/* ISO C1X: 7.15 Alignment <stdalign.h>.  */

#ifndef _STDALIGN_H
#define _STDALIGN_H

#if !(defined __STDC_VERSION__ && __STDC_VERSION__ > 201710L)

#ifndef __cplusplus
#define alignas _Alignas
#define alignof _Alignof
#endif

/* These are defined for C++, but deprecated in C++23.  */
#define __alignas_is_defined 1
#define __alignof_is_defined 1

#endif

#endif	/* stdalign.h */
