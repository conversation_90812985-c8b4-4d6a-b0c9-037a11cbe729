.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_status_request_is_checked" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_status_request_is_checked \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_ocsp_status_request_is_checked(gnutls_session_t " session ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.IP "unsigned int flags" 12
should be zero or \fBGNUTLS_OCSP_SR_IS_AVAIL\fP
.SH "DESCRIPTION"
When flags are zero this function returns non\-zero if a valid OCSP status
response was included in the TLS handshake. That is, an OCSP status response
which is not too old, superseded or marks the certificate as revoked.
It returns zero otherwise.

When the flag \fBGNUTLS_OCSP_SR_IS_AVAIL\fP is specified, the function
returns non\-zero if an OCSP status response was included in the handshake
even if it was invalid. Otherwise, if no OCSP status response was included,
it returns zero. The \fBGNUTLS_OCSP_SR_IS_AVAIL\fP flag was introduced in GnuTLS 3.4.0.

This is a helper function when needing to decide whether to perform an
explicit OCSP validity check on the peer's certificate. Should be called after
any of gnutls_certificate_verify_peers*() are called.

This function is always usable on client side, but on server side only under
TLS 1.3, which is the first version of TLS that allows clients to send OCSP
responses.
.SH "RETURNS"
Non\-zero if the response was valid, or a zero if it wasn't sent,
or sent and was invalid.
.SH "SINCE"
3.1.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
