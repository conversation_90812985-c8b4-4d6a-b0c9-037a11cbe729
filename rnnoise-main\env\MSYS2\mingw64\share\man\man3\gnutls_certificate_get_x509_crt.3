.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_x509_crt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_x509_crt \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_get_x509_crt(gnutls_certificate_credentials_t " res ", unsigned " index ", gnutls_x509_crt_t ** " crt_list ", unsigned * " crt_list_size ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "unsigned index" 12
The index of the certificate list to obtain.
.IP "gnutls_x509_crt_t ** crt_list" 12
Where to store the certificate list.
.IP "unsigned * crt_list_size" 12
Will hold the number of certificates.
.SH "DESCRIPTION"
Obtains a X.509 certificate list that has been stored in  \fIres\fP with one of
\fBgnutls_certificate_set_x509_key()\fP, \fBgnutls_certificate_set_key()\fP,
\fBgnutls_certificate_set_x509_key_file()\fP,
\fBgnutls_certificate_set_x509_key_file2()\fP,
\fBgnutls_certificate_set_x509_key_mem()\fP, or
\fBgnutls_certificate_set_x509_key_mem2()\fP. Each certificate in the returned
certificate list must be deallocated with \fBgnutls_x509_crt_deinit()\fP, and the
list itself must be freed with \fBgnutls_free()\fP.

The  \fIindex\fP matches the return value of \fBgnutls_certificate_set_x509_key()\fP and friends
functions, when the \fBGNUTLS_CERTIFICATE_API_V2\fP flag is set.

If there is no certificate with the given index,
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned. If the certificate
with the given index is not a X.509 certificate, \fBGNUTLS_E_INVALID_REQUEST\fP
is returned. The returned certificates must be deinitialized after
use, and the  \fIcrt_list\fP pointer must be freed using \fBgnutls_free()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success, or a negative error code.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
