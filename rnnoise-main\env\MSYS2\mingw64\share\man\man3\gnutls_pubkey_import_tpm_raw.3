.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pubkey_import_tpm_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pubkey_import_tpm_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pubkey_import_tpm_raw(gnutls_pubkey_t " pkey ", const gnutls_datum_t * " fdata ", gnutls_tpmkey_fmt_t " format ", const char * " srk_password ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pubkey_t pkey" 12
The public key
.IP "const gnutls_datum_t * fdata" 12
The TPM key to be imported
.IP "gnutls_tpmkey_fmt_t format" 12
The format of the private key
.IP "const char * srk_password" 12
The password for the SRK key (optional)
.IP "unsigned int flags" 12
One of the GNUTLS_PUBKEY_* flags
.SH "DESCRIPTION"
This function will import the public key from the provided TPM key
structure.

With respect to passwords the same as in
\fBgnutls_pubkey_import_tpm_url()\fP apply.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
