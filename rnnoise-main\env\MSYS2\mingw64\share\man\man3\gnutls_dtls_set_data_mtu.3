.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dtls_set_data_mtu" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dtls_set_data_mtu \- API function
.SH SYNOPSIS
.B #include <gnutls/dtls.h>
.sp
.BI "int gnutls_dtls_set_data_mtu(gnutls_session_t " session ", unsigned int " mtu ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int mtu" 12
The maximum unencrypted transfer unit of the session
.SH "DESCRIPTION"
This function will set the maximum size of the *unencrypted* records
which will be sent over a DTLS session. It is equivalent to calculating
the DTLS packet overhead with the current encryption parameters, and
calling \fBgnutls_dtls_set_mtu()\fP with that value. In particular, this means
that you may need to call this function again after any negotiation or
renegotiation, in order to ensure that the MTU is still sufficient to
account for the new protocol overhead.

In most cases you only need to call \fBgnutls_dtls_set_mtu()\fP with
the maximum MTU of your transport layer.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success, or a negative error code.
.SH "SINCE"
3.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
