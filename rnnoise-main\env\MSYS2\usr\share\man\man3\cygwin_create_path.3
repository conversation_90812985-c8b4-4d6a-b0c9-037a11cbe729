'\" t
.\"     Title: cygwin_create_path
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/18/2025
.\"    Manual: Cygwin API Reference
.\"    Source: Cygwin API Reference
.\"  Language: English
.\"
.TH "CYGWIN_CREATE_PATH" "3" "06/18/2025" "Cygwin API Reference" "Cygwin API Reference"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
cygwin_create_path
.SH "SYNOPSIS"
.sp
.ft B
.nf
#include <sys/cygwin\&.h>
.fi
.ft
.HP \w'void\ *\ cygwin_create_path('u
.BI "void * cygwin_create_path(cygwin_conv_path_t\ " "what" ", const\ void\ *\ " "from" ");"
.SH "DESCRIPTION"
.PP
This is equivalent to the
\fBcygwin_conv_path\fR, except that
\fBcygwin_create_path\fR
does not take a buffer pointer for the result of the conversion as input\&. Rather it allocates the buffer itself using
\fBmalloc\fR(3) and returns a pointer to this buffer\&. In case of error it returns NULL and sets errno to one of the values defined for
\fBcygwin_conv_path\fR\&. Additionally errno can be set to the below value\&.
.sp
.if n \{\
.RS 4
.\}
.nf
    ENOMEM        Insufficient memory was available\&.
.fi
.if n \{\
.RE
.\}
.PP
When you don\*(Aqt need the returned buffer anymore, use
\fBfree\fR(3) to deallocate it\&.
.SH "SEE ALSO"
.PP
See also
cygwin_conv_path
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
