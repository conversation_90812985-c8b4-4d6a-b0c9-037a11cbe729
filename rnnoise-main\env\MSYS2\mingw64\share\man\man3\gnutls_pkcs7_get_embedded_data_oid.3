.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_get_embedded_data_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_get_embedded_data_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "const char * gnutls_pkcs7_get_embedded_data_oid(gnutls_pkcs7_t " pkcs7 ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_t pkcs7" 12
should contain a gnutls_pkcs7_t type
.SH "DESCRIPTION"
This function will return the OID of the data embedded in the signature of
the PKCS7 structure. If no data are available then \fBNULL\fP will be
returned. The returned value will be valid during the lifetime
of the  \fIpkcs7\fP structure.
.SH "RETURNS"
On success, a pointer to an OID string, \fBNULL\fP on error.
.SH "SINCE"
3.5.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
