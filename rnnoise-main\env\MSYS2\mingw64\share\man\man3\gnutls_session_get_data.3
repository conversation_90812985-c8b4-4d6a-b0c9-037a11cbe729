.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_get_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_get_data \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_get_data(gnutls_session_t " session ", void * " session_data ", size_t * " session_data_size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "void * session_data" 12
is a pointer to space to hold the session.
.IP "size_t * session_data_size" 12
is the session_data's size, or it will be set by the function.
.SH "DESCRIPTION"
Returns all session parameters needed to be stored to support resumption,
in a pre\-allocated buffer.

See \fBgnutls_session_get_data2()\fP for more information.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
