<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Chapter&#160;3.&#160;Using Cygwin</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="prev" href="setup-files.html" title="Customizing bash"><link rel="next" href="using-textbinary.html" title="Text and Binary modes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Chapter&#160;3.&#160;Using Cygwin</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="setup-files.html">Prev</a>&#160;</td><th width="60%" align="center">&#160;</th><td width="20%" align="right">&#160;<a accesskey="n" href="using-textbinary.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h1 class="title"><a name="using"></a>Chapter&#160;3.&#160;Using Cygwin</h1></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="sect1"><a href="using.html#using-pathnames">Mapping path names</a></span></dt><dd><dl><dt><span class="sect2"><a href="using.html#pathnames-intro">Introduction</a></span></dt><dt><span class="sect2"><a href="using.html#mount-table">The Cygwin Mount Table</a></span></dt><dt><span class="sect2"><a href="using.html#unc-paths">UNC paths</a></span></dt><dt><span class="sect2"><a href="using.html#cygdrive">The cygdrive path prefix</a></span></dt><dt><span class="sect2"><a href="using.html#usertemp">The usertemp file system type</a></span></dt><dt><span class="sect2"><a href="using.html#pathnames-symlinks">Symbolic links</a></span></dt><dt><span class="sect2"><a href="using.html#pathnames-win32">Using native Win32 paths</a></span></dt><dt><span class="sect2"><a href="using.html#pathnames-win32-api">Using the Win32 file API in Cygwin applications</a></span></dt><dt><span class="sect2"><a href="using.html#pathnames-additional">Additional Path-related Information</a></span></dt></dl></dd><dt><span class="sect1"><a href="using-textbinary.html">Text and Binary modes</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-textbinary.html#textbin-issue">The Issue</a></span></dt><dt><span class="sect2"><a href="using-textbinary.html#textbin-default">The default Cygwin behavior</a></span></dt><dt><span class="sect2"><a href="using-textbinary.html#textbin-question">Binary or text?</a></span></dt><dt><span class="sect2"><a href="using-textbinary.html#textbin-devel">Programming</a></span></dt></dl></dd><dt><span class="sect1"><a href="using-filemodes.html">File permissions</a></span></dt><dt><span class="sect1"><a href="using-specialnames.html">Special filenames</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-specialnames.html#pathnames-etc">Special files in /etc</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-dosdevices">Invalid filenames</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-specialchars">Forbidden characters in filenames</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-unusual">Filenames with unusual (foreign) characters</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-casesensitive">Case sensitive filenames</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-casesensitivedirs">Case sensitive directories</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-posixdevices">POSIX devices</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-exe">The .exe extension</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-proc">The /proc filesystem</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-proc-registry">The /proc/registry filesystem</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-at">The @pathnames</a></span></dt></dl></dd><dt><span class="sect1"><a href="using-cygwinenv.html">The <code class="envar">CYGWIN</code> environment
variable</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-cygwinenv.html#cygwinenv-implemented-options">Implemented options</a></span></dt><dt><span class="sect2"><a href="using-cygwinenv.html#cygwinenv-removed-options">Obsolete options</a></span></dt></dl></dd><dt><span class="sect1"><a href="ntsec.html">POSIX accounts, permission, and security</a></span></dt><dd><dl><dt><span class="sect2"><a href="ntsec.html#ntsec-common">Brief overview of Windows security</a></span></dt><dt><span class="sect2"><a href="ntsec.html#ntsec-mapping">Mapping Windows accounts to POSIX accounts</a></span></dt><dd><dl><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-how">Mapping Windows SIDs to POSIX uid/gid values</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-caching">Caching account information</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-passwdinfo">Cygwin user names, home dirs, login shells</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-nsswitch">The <code class="filename">/etc/nsswitch.conf</code> file</a></span></dt><dd><dl><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-syntax">The <code class="filename">/etc/nsswitch.conf</code> syntax</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-pwdgrp">The <code class="literal">passwd:</code> and <code class="literal">group:</code> settings</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-enum">The <code class="literal">db_enum:</code> setting</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-passwd">Settings defining how to create the <code class="literal">passwd</code> entry</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-home">The <code class="literal">db_home</code> setting</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-shell">The <code class="literal">db_shell</code> setting</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-gecos">The <code class="literal">db_gecos</code> setting</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-cygwin">The <code class="literal">cygwin</code> schema</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-posix">The <code class="literal">unix</code> schema</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-desc">The <code class="literal">desc</code> schema</a></span></dt></dl></dd><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-nfs">NFS account mapping</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-samba">Samba account mapping</a></span></dt></dl></dd><dt><span class="sect2"><a href="ntsec.html#ntsec-files">File permissions</a></span></dt><dt><span class="sect2"><a href="ntsec.html#ntsec-setuid-overview">Switching the user context</a></span></dt><dd><dl><dt><span class="sect3"><a href="ntsec.html#ntsec-logonuser">Switching the user context with password authentication</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-nopasswd1">Switching the user context without password, Method 1: Kerberos/MsV1_0 S4U authentication</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-nopasswd3">Switching the user context without password, Method 2: With password</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-setuid-impl">Switching the user context, how does it all fit together?</a></span></dt></dl></dd></dl></dd><dt><span class="sect1"><a href="using-cygserver.html">Cygserver</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-cygserver.html#what-is-cygserver">What is Cygserver?</a></span></dt><dt><span class="sect2"><a href="using-cygserver.html#cygserver-command-line">Cygserver command line options</a></span></dt><dt><span class="sect2"><a href="using-cygserver.html#install-cygserver">How to install Cygserver</a></span></dt><dt><span class="sect2"><a href="using-cygserver.html#start-cygserver">How to start Cygserver</a></span></dt><dt><span class="sect2"><a href="using-cygserver.html#cygserver-config">The Cygserver configuration file</a></span></dt></dl></dd><dt><span class="sect1"><a href="using-utils.html">Cygwin Utilities</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="chattr.html">chattr</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="cygcheck.html">cygcheck</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="cygpath.html">cygpath</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="dumper.html">dumper</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="getconf.html">getconf</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="getfacl.html">getfacl</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="gmondump.html">gmondump</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="kill.html">kill</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="ldd.html">ldd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="locale.html">locale</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="lsattr.html">lsattr</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="minidumper.html">minidumper</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="mkgroup.html">mkgroup</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="mkpasswd.html">mkpasswd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="mount.html">mount</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="newgrp.html">newgrp</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="passwd.html">passwd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="pldd.html">pldd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="profiler.html">profiler</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="ps.html">ps</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="regtool.html">regtool</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="setfacl.html">setfacl</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="setmetamode.html">setmetamode</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="ssp.html">ssp</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="strace.html">strace</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="tzset.html">tzset</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="umount.html">umount</a></span><span class="refpurpose"></span></dt></dl></dd><dt><span class="sect1"><a href="using-effectively.html">Using Cygwin effectively with Windows</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-effectively.html#using-pathnames-effectively">Pathnames</a></span></dt><dt><span class="sect2"><a href="using-effectively.html#using-net">Cygwin and Windows Networking</a></span></dt><dt><span class="sect2"><a href="using-effectively.html#using-shortcuts">Creating shortcuts</a></span></dt><dt><span class="sect2"><a href="using-effectively.html#using-printing">Printing</a></span></dt></dl></dd></dl></div><p>This chapter explains some key differences between the Cygwin
environment and traditional UNIX systems. It assumes a working
knowledge of standard UNIX commands.</p><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="using-pathnames"></a>Mapping path names</h2></div></div></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="pathnames-intro"></a>Introduction</h3></div></div></div><p>The Cygwin DLL supports both POSIX- and Win32-style paths.  Directory
delimiters may be either forward slashes or backslashes.  Paths using
backslashes or starting with a drive letter are always handled as
Win32 paths.  POSIX paths must only use forward slashes as delimiter,
otherwise they are treated as Win32 paths and file access might fail
in surprising ways.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Although the Cygwin DLL supports Win32 paths, not all
Cygwin applications support them.  Moreover, the usage of Win32 paths
circumvents important internal path handling mechanisms.  This usage
is therefore strongly deprecated and may be removed in a future
release of Cygwin.
See <a class="xref" href="using.html#pathnames-win32" title="Using native Win32 paths">the section called &#8220;Using native Win32 paths&#8221;</a> and
<a class="xref" href="using.html#pathnames-win32-api" title="Using the Win32 file API in Cygwin applications">the section called &#8220;Using the Win32 file API in Cygwin applications&#8221;</a> for more information.
</p></div><p>POSIX operating systems (such as Linux) do not have the concept
of drive letters.  Instead, all absolute paths begin with a
slash (instead of a drive letter such as "c:") and all file systems
appear as subdirectories (for example, you might buy a new disk and
make it be the <code class="filename">/disk2</code> directory).</p><p>Because many programs written to run on UNIX systems assume
the existence of a single unified POSIX file system structure, Cygwin
maintains a special internal POSIX view of the Win32 file system
that allows these programs to successfully run under Windows.  Cygwin
uses this mapping to translate from POSIX to Win32 paths as
necessary.</p></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="mount-table"></a>The Cygwin Mount Table</h3></div></div></div><p>The <code class="filename">/etc/fstab</code> file is used to map Win32
drives and network shares into Cygwin's internal POSIX directory tree.
This is a similar concept to the typical UNIX fstab file.  The mount
points stored in <code class="filename">/etc/fstab</code> are globally set for
all users.  Sometimes there's a requirement to have user specific
mount points.  The Cygwin DLL supports user specific fstab files.
These are stored in the directory <code class="filename">/etc/fstab.d</code>
and the name of the file is the Cygwin username of the user, as it's
created from the Windows account database or stored in the
<code class="filename">/etc/passwd</code> file (see
<a class="xref" href="ntsec.html#ntsec-mapping" title="Mapping Windows accounts to POSIX accounts">the section called &#8220;Mapping Windows accounts to POSIX accounts&#8221;</a>).  The structure of the
user specific file is identical to the system-wide
<code class="filename">fstab</code> file.</p><p>The file fstab contains descriptive information about the various file
systems.  fstab is only read by programs, and not written; it is the
duty of the system administrator to properly create and maintain this
file.  Each filesystem is described on a separate line; fields on each
line are separated by tabs or spaces.  Lines starting with '#' are
comments.</p><p>The first field describes the block special device or
remote filesystem to be mounted.  On Cygwin, this is the native Windows
path which the mount point links in.  As path separator you MUST use a
slash.  Usage of a backslash might lead to unexpected results.  UNC
paths (using slashes, not backslashes) are allowed.  If the path
contains spaces these can be escaped as <code class="literal">'\040'</code>.</p><p>The second field describes the mount point for the filesystem. 
If the name of the mount point contains spaces these can be
escaped as '\040'.</p><p>The third field describes the type of the filesystem.  Cygwin supports
any string here, since the file system type is usually not evaluated.  So it
doesn't matter if you write <code class="literal">FAT</code> into this field even if
the filesystem is NTFS.  Cygwin figures out the filesystem type and its
capabilities by itself.</p><p>The only two exceptions are the file system types cygdrive and usertemp.
The cygdrive type is used to set the cygdrive prefix.  For a description of
the cygdrive prefix see <a class="xref" href="using.html#cygdrive" title="The cygdrive path prefix">the section called &#8220;The cygdrive path prefix&#8221;</a>, for a description of
the usertemp file system type see <a class="xref" href="using.html#usertemp" title="The usertemp file system type">the section called &#8220;The usertemp file system type&#8221;</a></p><p>The fourth field describes the mount options associated
with the filesystem.  It is formatted as a comma separated list of
options.  It contains at least the type of mount (binary or text) plus
any additional options appropriate to the filesystem type.  The list of
the options, including their meaning, follows.</p><pre class="screen">
  acl       - Cygwin uses the filesystem's access control lists (ACLs) to
              implement real POSIX permissions (default).  This flag only
	      affects filesystems supporting ACLs (NTFS, for instance) and
	      is ignored otherwise.
  auto      - Ignored.
  binary    - Files default to binary mode (default).
  bind      - Allows to remount part of the file hierarchy somewhere else.
              In contrast to other entries, the first field in the fstab
	      line specifies an absolute POSIX path.  This path is remounted
	      to the POSIX path specified as the second path.  The conversion
	      to a Win32 path is done on the fly.  Only the root path and
	      paths preceding the bind entry in the fstab file are used to
	      convert the POSIX path in the first field to an absolute Win32
	      path.  Note that symlinks are ignored while performing this path
	      conversion.
  cygexec   - Treat all files below mount point as cygwin executables.
  dos       - Always convert leading spaces and trailing dots and spaces to
	      characters in the UNICODE private use area.  This allows to use
	      broken filesystems which only allow DOS filenames, even if they
	      are not recognized as such by Cygwin.
  exec      - Treat all files below mount point as executable.
  ihash     - Always fake inode numbers rather than using the ones returned
	      by the filesystem.  This allows to use broken filesystems which
	      don't return unambiguous inode numbers, even if they are not
	      recognized as such by Cygwin.
  noacl     - Cygwin ignores filesystem ACLs and only fakes a subset of
	      permission bits based on the DOS readonly attribute.  This
	      behaviour is the default on FAT and FAT32.  The flag is
	      ignored on NFS filesystems.
  nosuid    - No suid files are allowed (currently unimplemented).
  notexec   - Treat all files below mount point as not executable.
  nouser    - Mount is a system-wide mount.
  override  - Force the override of an immutable mount point (currently "/").
  posix=0   - Switch off case sensitivity for paths under this mount point
	      (default for the cygdrive prefix).
  posix=1   - Switch on case sensitivity for paths under this mount point
	      (default for all other mount points).
  sparse    - Switch on support for sparse files.  This option only makes
              sense on NTFS and then only if you really need sparse files.
	      This flag is always silently enabled on SSD drives.
	      Cygwin does not try to create sparse files by default for
	      performance reasons.
  text      - Files default to CRLF text mode line endings.
  user      - Mount is a user mount.
</pre><p>While normally the execute permission bits are used to evaluate
executability, this is not possible on filesystems which don't support
permissions at all (like FAT/FAT32), or if ACLs are ignored on filesystems
supporting them (see the aforementioned <code class="literal">acl</code> mount option).
In these cases, the following heuristic is used to evaluate if a file is
executable: Files ending in certain extensions (.exe, .com, .lnk) are
assumed to be executable.  Files whose first two characters are "#!", "MZ",
or ":\n" are also considered to be executable.
The <code class="literal">exec</code> option is used to instruct Cygwin that the
mounted file is "executable".  If the <code class="literal">exec</code> option is used
with a directory then all files in the directory are executable.
This option allows other files to be marked as executable and avoids the
overhead of opening each file to check for "magic" bytes.  The
<code class="literal">cygexec</code> option is very similar to <code class="literal">exec</code>,
but also prevents Cygwin from setting up commands and environment variables
for a normal Windows program, adding another small performance gain.  The
opposite of these options is the <code class="literal">notexec</code> option, which
means that no files should be marked as executable under that mount point.</p><p>A correct root directory is quite essential to the operation of
Cygwin.  A default root directory is evaluated at startup so a
<code class="filename">fstab</code> entry for the root directory is not necessary.
If it's wrong, nothing will work as expected.  Therefore, the root directory
evaluated by Cygwin itself is treated as an immutable mount point and can't
be overridden in /etc/fstab... unless you think you really know what you're
doing.  In this case, use the <code class="literal">override</code> flag in the options
field in the <code class="filename">/etc/fstab</code> file.  Since this is a dangerous
thing to do, do so at your own risk.</p><p><code class="filename">/usr/bin</code> and <code class="filename">/usr/lib</code> are
by default also automatic mount points generated by the Cygwin DLL similar
to the way the root directory is evaluated.  <code class="filename">/usr/bin</code>
points to the directory the Cygwin DLL is installed in,
<code class="filename">/usr/lib</code> is supposed to point to the
<code class="filename">/lib</code> directory.  This choice is safe and usually
shouldn't be changed.  An fstab entry for them is not required.</p><p><code class="literal">nouser</code> mount points are not overridable by a later
call to <span class="command"><strong>mount</strong></span>.
Mount points given in <code class="filename">/etc/fstab</code> are by default
<code class="literal">nouser</code> mount points, unless you specify the option
<code class="literal">user</code>.  This allows the administrator to set certain
paths so that they are not overridable by users.  In contrast, all mount
points in the user specific fstab file are <code class="literal">user</code> mount
points.</p><p>The fifth and sixth field are ignored.  They are
so far only specified to keep a Linux-like fstab file layout.</p><p>Note that you don't have to specify an fstab entry for the root dir,
unless you want to have the root dir pointing to somewhere entirely
different (hopefully you know what you're doing), or if you want to
mount the root dir with special options (for instance, as text mount).</p><p>Example entries:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Just a normal mount point:</p><pre class="screen">  c:/foo /bar fat32 binary 0 0</pre></li><li class="listitem"><p>A mount point for a textmode mount with case sensitivity switched off:</p><pre class="screen">  C:/foo /bar/baz ntfs text,posix=0 0 0</pre></li><li class="listitem"><p>A mount point for a Windows directory with spaces in it:</p><pre class="screen">  C:/Documents\040and\040Settings /docs ext3 binary 0 0</pre></li><li class="listitem"><p>A mount point for a remote directory, don't store POSIX permissions in ACLs:</p><pre class="screen">  //server/share/subdir /srv/subdir smbfs binary,noacl 0 0</pre></li><li class="listitem"><p>This is just a comment:</p><pre class="screen">  # This is just a comment</pre></li><li class="listitem"><p>Set the cygdrive prefix to /mnt:</p><pre class="screen">  none /mnt cygdrive binary 0 0</pre></li><li class="listitem"><p>Remount /var to /usr/var:</p><pre class="screen">  /var /usr/var none bind</pre><p>Assuming <code class="filename">/var</code> points to
  <code class="filename">C:/cygwin/var</code>, <code class="filename">/usr/var</code> now
  also points to <code class="filename">C:/cygwin/var</code>.  This is equivalent
  to the Linux <code class="literal">bind</code> option available since
  Linux 2.4.0.</p></li></ul></div><p>Whenever Cygwin generates a Win32 path from a POSIX one, it uses
the longest matching prefix in the mount table.  Thus, if
<code class="filename">C:</code> is mounted as <code class="filename">/c</code> and also
as <code class="filename">/</code>, then Cygwin would translate
<code class="filename">C:/foo/bar</code> to <code class="filename">/c/foo/bar</code>.
This translation is normally only used when trying to derive the
POSIX equivalent current directory.  Otherwise, the handling of MS-DOS
filenames bypasses the mount table.
</p><p>If you want to see the current set of mount points valid in your
session, you can invoke the Cygwin tool <span class="command"><strong>mount</strong></span> without
arguments:</p><div class="example"><a name="pathnames-mount-ex"></a><p class="title"><b>Example&#160;3.1.&#160;Displaying the current set of mount points</b></p><div class="example-contents"><pre class="screen">
  <code class="prompt">bash$</code> <strong class="userinput"><code>mount</code></strong>
  F:/cygwin/bin on /usr/bin type ntfs (binary,auto)
  F:/cygwin/lib on /usr/lib type ntfs (binary,auto)
  F:/cygwin on / type ntfs (binary,auto)
  E:/src on /usr/src type vfat (binary)
  C: on /cygdrive/c type ntfs (binary,posix=0,user,noumount,auto)
  E: on /cygdrive/e type vfat (binary,posix=0,user,noumount,auto)
</pre></div></div><br class="example-break"><p>Starting with Cygwin 3.6.0, the <code class="function">getmntent(3)</code>
function exposes Windows mount points of partitions mounted into a directory
of another drive as if they are cygdrive mounts.  This affects the content
of <code class="filename">/proc/mounts</code> as well as the output of
<span class="command"><strong>mount</strong></span>.</p><p>For instance, consider you have two local disks, one containing the
<code class="filename">C:</code> drive, the other disk has one partition mounted
into <code class="filename">C:\docs</code>.  The output from mount will now show this
mount point like this:</p><div class="example"><a name="pathnames-mount-dir"></a><p class="title"><b>Example&#160;3.2.&#160;Displaying Windows mount points as cygdrives</b></p><div class="example-contents"><pre class="screen">
  <code class="prompt">bash$</code> <strong class="userinput"><code>mount | grep cygdrive</code></strong>
  C: on /cygdrive/c type ntfs (binary,posix=0,user,noumount,auto)
  C:/docs on /cygdrive/c/docs type ntfs (binary,posix=0,user,noumount,auto)
</pre></div></div><br class="example-break"><p>You can also use the <span class="command"><strong>mount</strong></span> command to add
new mount points, and the <span class="command"><strong>umount</strong></span> to delete
them.  However, since they are only stored in memory, these mount
points will disappear as soon as your last Cygwin process ends.
See <a class="xref" href="mount.html" title="mount"><span class="refentrytitle">mount</span>(1)</a> and <a class="xref" href="umount.html" title="umount"><span class="refentrytitle">umount</span>(1)</a> for more
information.</p></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="unc-paths"></a>UNC paths</h3></div></div></div><p>Apart from the unified POSIX tree starting at the <code class="filename">/</code>
directory, UNC pathnames starting with two slashes and a server name
(<code class="filename">//machine/share/...</code>) are supported as well.
They are handled as POSIX paths if only containing forward slashes.  There's
also a virtual directory <code class="filename">//</code> which allows to enumerate
the fileservers known to the local machine with <span class="command"><strong>ls</strong></span>.
Same goes for the UNC paths of the type <code class="filename">//machine</code>,
which allow to enumerate the shares provided by the server
<code class="literal">machine</code>. For often used UNC paths it makes sense to
add them to the mount table (see <a class="xref" href="using.html#mount-table" title="The Cygwin Mount Table">the section called &#8220;The Cygwin Mount Table&#8221;</a> so
they are included in the unified POSIX path tree.</p></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="cygdrive"></a>The cygdrive path prefix</h3></div></div></div><p>As already outlined in <a class="xref" href="highlights.html#ov-hi-files" title="File Access">the section called &#8220;File Access&#8221;</a>, you can
access arbitary drives on your system by using the cygdrive path prefix.
The default value for this prefix is <code class="filename">/cygdrive</code>, and
a path to any drive can be constructed by using the cygdrive prefix and
appending the drive letter as subdirectory, like this:</p><pre class="screen">
  bash$ ls -l /cygdrive/f/somedir
</pre><p>This lists the content of the directory F:\somedir.</p><p>The cygdrive prefix is a virtual directory under which all drives
on a system are subsumed.  The mount options of the cygdrive prefix is
used for all file access through the cygdrive prefixed drives.  For instance,
assuming the cygdrive mount options are <code class="literal">binary,posix=0</code>,
then any file <code class="filename">/cygdrive/x/file</code> will be opened in
binary mode by default (mount option <code class="literal">binary</code>), and the case
of the filename doesn't matter (mount option <code class="literal">posix=0</code>).
</p><p>The cygdrive prefix flags are also used for all UNC paths starting with
two slashes, unless they are accessed through a mount point.  For instance,
consider these <code class="filename">/etc/fstab</code> entries:</p><pre class="screen">
  //server/share /mysrv    ntfs     posix=1,acl   0 0
  none           /cygdrive cygdrive posix=0,noacl 0 0
</pre><p>Consider a file <code class="filename">\\server\share\foo</code>.
When opening the file as <code class="filename">/mysrv/foo</code>, the flags
<code class="literal">posix=1,acl</code> of the /mysrv mount point are used.  When
opening the file as <code class="filename">//server/share/foo</code>, the flags
<code class="literal">posix=0,noacl</code> for the cygdrive prefix are used.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>This only applies to UNC paths using forward slashes.  When
using backslashes the flags for native paths are used.  See
<a class="xref" href="using.html#pathnames-win32" title="Using native Win32 paths">the section called &#8220;Using native Win32 paths&#8221;</a>.</p></div><p>The cygdrive prefix may be changed in the fstab file to another path
and mode:</p><pre class="screen">
  none /mnt cygdrive posix=1,sparse 0 0
</pre><p>Please note that you cannot use the cygdrive prefix for any other mount
point to change modes.  For instance, these mount table entries...</p><pre class="screen">
  none /cygdrive cygdrive binary 0 0
  D:   /cygdrive/d somefs text 0 0
</pre><p>...will not open files using the <code class="filename">/cygdrive/d</code> path
prefix in textmode by default, but in binary mode per the cygdrive prefix
mode.  If you want to mount any drive explicitly in another mode than
the cygdrive prefix, use a different path prefix:</p><pre class="screen">
  none /cygdrive cygdrive binary 0 0
  D:   /mnt/d somefs text 0 0
</pre><p>To simplify scripting, Cygwin also provides a
<code class="filename">/proc/cygdrive</code> symlink, which allows to use a fixed path
in scripts, even if the actual cygdrive prefix has been changed, or is different
between different users.  So, in scripts, conveniently use the
<code class="filename">/proc/cygdrive</code> symlink to successfully access files
independently from the current cygdrive prefix:</p><pre class="screen">
  $ mount -p
  Prefix              Type         Flags
  /mnt                user         binmode
  $ cat &gt; x.sh &lt;&lt;EOF
  cd /proc/cygdrive/c/Windows/System32/Drivers/etc
  ls -l hosts
  EOF
  $ sh -c ./x.sh
  -rwxrwx---+ 1 <USER> <GROUP> 826 Sep  4 22:43 hosts
</pre></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="usertemp"></a>The usertemp file system type</h3></div></div></div><p>On Windows, the environment variable <code class="literal">TEMP</code> specifies
the location of the temp folder.  It serves the same purpose as the /tmp/
directory in Unix systems.  In contrast to /tmp/, it is by default a
different folder for every Windows user.  By using the special purpose usertemp
file system, that temp folder can be mapped to /tmp/.  This is particularly
useful in setups where the administrator wants to write-protect the entire
Cygwin directory.  The usertemp file system can be configured in /etc/fstab
like this:</p><pre class="screen">
  none /tmp usertemp binary,posix=0 0 0
</pre></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="pathnames-symlinks"></a>Symbolic links</h3></div></div></div><p>Symbolic links are supported by Windows only on NTFS and have
a lot of quirks making them (almost) unusable in a POSIX context.
POSIX applications are rightfully expecting to use symlinks and the
<code class="literal">symlink(2)</code> system call, so Cygwin has worked around
the Windows shortcomings.</p><p>Cygwin creates symbolic links potentially in multiple different
ways.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: bullet; "><li class="listitem" style="list-style-type: disc"><p>The default symlinks created by Cygwin are:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: square; "><li class="listitem" style="list-style-type: square"><p>special reparse points shared with WSL (on NTFS on Windows 10 1607
      or later)</p></li><li class="listitem" style="list-style-type: square"><p>plain files with the <code class="literal">system</code> attribute, containing
      a magic cookie followed by the path to which the link points.
      </p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Symlinks of this type created by really old Cygwin releases
      (prior to Cygwin 1.7.0) are usually readable.  However, you could run into
      problems if you're now using another character set than the one you used
      when creating these symlinks (see <a class="xref" href="setup-locale.html#setup-locale-problems" title="Potential Problems when using Locales">the section called &#8220;Potential Problems when using Locales&#8221;</a>).
      </p></div></li></ul></div></li><li class="listitem" style="list-style-type: disc"><p>On filesystems mounted via Microsoft's NFS client, Cygwin always
creates real NFS symlinks.</p></li><li class="listitem" style="list-style-type: disc"><p>Native Windows symlinks are only created on filesystems supporting
reparse points.  Due to their weird restrictions and behaviour, they are
only created if the user explicitely requests creating them.  This is done
by setting the environment variable <code class="literal">CYGWIN</code> to contain
the string <code class="literal">winsymlinks:native</code> or
<code class="literal">winsymlinks:nativestrict</code>.  For the difference between
these two settings, see <a class="xref" href="using-cygwinenv.html" title="The CYGWIN environment variable">the section called &#8220;The <code class="envar">CYGWIN</code> environment
variable&#8221;</a>.
On AFS, native symlinks are the only supported type of symlink due to
AFS lacking support for DOS attributes.  This is independent from the
<code class="literal">winsymlinks</code> setting.</p><p>Creation of native symlinks follows special rules to ensure the links
are usable outside of Cygwin. This includes dereferencing any Cygwin-only
symlinks that lie in the target path.</p></li><li class="listitem" style="list-style-type: disc"><p>Shortcut style symlinks are Windows <code class="literal">.lnk</code>
shortcut files with a special header and the DOS READONLY attribute set.
This symlink type is created if the environment variable
<code class="literal">CYGWIN</code> (see <a class="xref" href="using-cygwinenv.html" title="The CYGWIN environment variable">the section called &#8220;The <code class="envar">CYGWIN</code> environment
variable&#8221;</a>)
is set to contain the string <code class="literal">winsymlinks</code> or
<code class="literal">winsymlinks:lnk</code>.  On the MVFS filesystem, which does
not support the DOS SYSTEM attribute, this is the one and only supported
symlink type, independently from the <code class="literal">winsymlinks</code>
setting.</p></li></ul></div><p>All of the above symlink types are recognized and used as symlinks
under all circumstances.  However, if the default plain file symlink type
is lacking its DOS SYSTEM bit, or if the shortcut file is lacking the DOS
READONLY attribute, they are not recognized as symlink.</p><p>Apart from these types, there's also a Windows native type,
so called directory junctions.  They are recognized as symlink but
never generated by Cygwin.  Filesystem junctions on the other hand
are not handled as symlinks, otherwise they would not be recognized as
filesystem borders by commands like <span class="command"><strong>find -xdev</strong></span>.</p></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="pathnames-win32"></a>Using native Win32 paths</h3></div></div></div><p>Using native Win32 paths in Cygwin, while often possible, is generally
inadvisable.  Those paths circumvent all internal integrity checking and
bypass the information given in the Cygwin mount table.</p><p>The following paths are treated as native Win32 paths by the
Cygwin DLL (but not necessarily by Cygwin applications):</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>All paths starting with a drive specifier</p><pre class="screen">
  C:\foo
  C:/foo
</pre></li><li class="listitem"><p>All paths containing at least one backslash as path component</p><pre class="screen">
  C:/foo/bar<span class="bold"><strong>\</strong></span>baz/...
</pre></li><li class="listitem"><p>UNC paths using backslashes</p><pre class="screen">
  \\server\share\...
</pre></li></ul></div><p>When accessing files using native Win32 paths as above, Cygwin uses a
default setting for the mount flags.  All paths using DOS notation will be
treated as case insensitive, and permissions are just faked as if the
underlying drive is a FAT drive.  This also applies to NTFS and other
filesystems which usually are capable of case sensitivity and storing
permissions.</p></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="pathnames-win32-api"></a>Using the Win32 file API in Cygwin applications</h3></div></div></div><p>Special care must be taken if your application uses Win32 file API
functions like <code class="function">CreateFile</code> to access files using
relative pathnames, or if your application uses functions like
<code class="function">CreateProcess</code> or <code class="function">ShellExecute</code>
to start other applications.</p><p>When a Cygwin application is started, the Windows idea of the current
working directory (CWD) is not necessarily the same as the Cygwin CWD.
There are a couple of restrictions in the Win32 API, which disallow certain
directories as Win32 CWD:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>The Windows subsystem only supports CWD paths of up to 258 chars.
    This restriction doesn't apply for Cygwin processes, at least not as
    long as they use the POSIX API (chdir, getcwd).  This means, if a Cygwin
    process has a CWD using an absolute path longer than 258 characters, the
    Cygwin CWD and the Windows CWD differ.</p></li><li class="listitem"><p>The Win32 API call to set the current directory,
    <code class="function">SetCurrentDirectory</code>, fails for directories for which
    the user has no permissions, even if the user is an administrator.  This
    restriction doesn't apply for Cygwin processes, if they are running under
    an administrator account.</p></li><li class="listitem"><p><code class="function">SetCurrentDirectory</code> does not support
    case-sensitive filenames.
    </p></li><li class="listitem"><p>Last, but not least, <code class="function">SetCurrentDirectory</code> can't
    work on virtual Cygwin paths like /proc or /cygdrive.  These paths only
    exists in the Cygwin realm so they have no meaning to a native Win32
    process.</p></li></ul></div><p>As long as the Cygwin CWD is usable as Windows CWD, the Cygwin and
Windows CWDs are in sync within a process.  However, if the Cygwin process
changes its working directory into one of the directories which are
unusable as Windows CWD, we're in trouble.  If the process uses the
Win32 API to access a file using a relative pathname, the resulting
absolute path would not match the expectations of the process.  In the
worst case, the wrong files are deleted.</p><p>To workaround this problem, Cygwin sets the Windows CWD to a special
directory in this case.  This special directory points to a virtual
filesystem within the native NT namespace (<code class="filename">\??\PIPE\</code>).
Since it's not a real filesystem, the deliberate effect is that a call to,
for instance, <code class="function">CreateFile ("foo", ...);</code> will fail,
as long as the processes CWD doesn't work as Windows CWD.</p><p>So, in general, don't use the Win32 file API in Cygwin applications.
If you <span class="bold"><strong>really</strong></span> need to access files using
the Win32 API, or if you <span class="bold"><strong>really</strong></span> have to use
<code class="function">CreateProcess</code> to start applications, rather than
the POSIX <code class="function">exec(3)</code> family of functions, you have to
make sure that the Cygwin CWD is set to some directory which is valid as
Win32 CWD.</p></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="pathnames-additional"></a>Additional Path-related Information</h3></div></div></div><p>The <span class="command"><strong>cygpath</strong></span> program provides the ability to
translate between Win32 and POSIX pathnames in shell scripts. See
<a class="xref" href="cygpath.html" title="cygpath"><span class="refentrytitle">cygpath</span>(1)</a> for the details.</p><p>The <code class="envar">HOME</code>, <code class="envar">PATH</code>, and
<code class="envar">LD_LIBRARY_PATH</code> environment variables are automatically
converted from Win32 format to POSIX format (e.g.  from
<code class="filename">c:/cygwin\bin</code> to <code class="filename">/bin</code>, if
there was a mount from that Win32 path to that POSIX path) when a Cygwin
process first starts.</p><p>Symbolic links can also be used to map Win32 pathnames to POSIX.
For example, the command
<span class="command"><strong>ln -s //pollux/home/<USER>/data /data</strong></span> would have about
the same effect as creating a mount point from
<code class="filename">//pollux/home/<USER>/data</code> to <code class="filename">/data</code>
using <span class="command"><strong>mount</strong></span>, except that symbolic links cannot set
the default file access mode.  Other differences are that the mapping is
distributed throughout the file system and proceeds by iteratively
walking the directory tree instead of matching the longest prefix in a
kernel table.  Note that symbolic links will only work on network
drives that are properly configured to support the "system" file
attribute.  Many do not do so by default (the Unix Samba server does
not by default, for example).</p></div></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="setup-files.html">Prev</a>&#160;</td><td width="20%" align="center">&#160;</td><td width="40%" align="right">&#160;<a accesskey="n" href="using-textbinary.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Customizing bash&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Text and Binary modes</td></tr></table></div></body></html>
